<?
class vip_groups {
	var $customer_id;
	var $vip_info;
	var $new_rank;
	var $new_middle_point;
	
	function vip_groups($customer_id) {
		$this->customer_id = $customer_id;
		$this->vip_info = array();		
		$this->get_vip_info(true);
	}
	
	function get_vip_info($flush=false) {
		if ($flush) {
			$customers_vip_select_sql = " 	SELECT vip_supplier_groups_id, vip_rank_id, vip_buyback_cummulative_point
											FROM " . TABLE_CUSTOMERS_VIP . " 
											WHERE customers_id='" . tep_db_input($this->customer_id) . "'";
			$customers_vip_result_sql = tep_db_query($customers_vip_select_sql);
			
			if ($customers_vip_row = tep_db_fetch_array($customers_vip_result_sql)) {
				$this->vip_info = $customers_vip_row;
			}
		}		
		return $this->vip_info;
	}
	
	function get_supplier_group_name($status_id){
		$select_status_name_sql = "	SELECT vip_supplier_groups_name 
									FROM " . TABLE_VIP_SUPPLIER_GROUPS ." WHERE language_id='1' AND vip_supplier_groups_id='" . $status_id . "'";
		$select_status_name_result = tep_db_query($select_status_name_sql);
		$select_status_name_row = tep_db_fetch_array($select_status_name_result);
		return strtolower($select_status_name_row['vip_supplier_groups_name']);
	}
	
	function get_supplier_rank_name($rank_id){
		$vip_rank_select_sql = "SELECT vip_rank_name 
								FROM " . TABLE_VIP_RANK . " 
								WHERE vip_rank_id='". $rank_id ."'";
		$vip_rank_select_result = tep_db_query($vip_rank_select_sql);
		if($vip_rank_select_row = tep_db_fetch_array($vip_rank_select_result)){
			;
		} else {
			$vip_rank_select_row['vip_rank_name'] = EMAIL_NO_RANK_AVAILABLE;
		}
		return $vip_rank_select_row['vip_rank_name'];
	}
	
	function get_next_upgrade_point($rank_id){
		$vip_upgrade_point_select_sql = "SELECT  vip_rank_upgrade_point 
										FROM " . TABLE_VIP_RANK . " 
										WHERE vip_rank_id='". $rank_id ."'";
		$vip_upgrade_point_select_result = tep_db_query($vip_upgrade_point_select_sql);
		$vip_upgrade_point_select_row = tep_db_fetch_array($vip_upgrade_point_select_result);
		return $vip_upgrade_point_select_row['vip_rank_upgrade_point'];
	}
	
	function get_supplier_share_percentage($rank_id){
		$vip_rank_percentage_select_sql = 	"SELECT vip_rank_percentage 
											FROM " . TABLE_VIP_RANK . " 
											WHERE vip_rank_id='". $rank_id ."'";
		$vip_rank_percentage_select_result = tep_db_query($vip_rank_percentage_select_sql);
		if($vip_rank_percentage_select_row = tep_db_fetch_array($vip_rank_percentage_select_result)){
			;
		} else {
			$vip_rank_percentage_select_row['vip_rank_percentage'] = 0;
		}
		return $vip_rank_percentage_select_row['vip_rank_percentage'];
	}
	
	function set_vip_group($groups_id, $point, $countries_id){ //admin user manual change status
		$region_group_id = '';
		$status_name = $this->get_supplier_group_name($groups_id);
		
		$region_group_select_sql = "SELECT vip_region_group_id 
									FROM " . TABLE_VIP_REGION_GROUP . " 
									WHERE FIND_IN_SET('".$countries_id."', countries_ids)";
		$region_group_result_sql = tep_db_query($region_group_select_sql);
		if ($region_group_row = tep_db_fetch_array($region_group_result_sql)) {
			$region_group_id = $region_group_row['vip_region_group_id'];
		} else {
			$region_group_select_sql = "SELECT vip_region_group_id 
										FROM " . TABLE_VIP_REGION_GROUP . " 
										WHERE FIND_IN_SET('0', countries_ids)";
			$region_group_result_sql = tep_db_query($region_group_select_sql);
			if ($region_group_row = tep_db_fetch_array($region_group_result_sql)) {
				$region_group_id = $region_group_row['vip_region_group_id'];
			}
		}
		
		if (count($this->vip_info)) {	// has VIP info
			if ($this->vip_info['vip_supplier_groups_id'] != $groups_id) { //status changed
				$this->vip_info['vip_supplier_groups_id'] = $groups_id;
				if (tep_not_null($region_group_id)) {
					$select_basic_rank_sql = "	SELECT * 
												FROM " . TABLE_VIP_RANK . " 
												WHERE vip_region_group_id = '" . tep_db_input($region_group_id) . "' 
													AND vip_rank_action = 'SYSTEM_VIP_GROUPS_STATUS_" . $this->vip_info['vip_supplier_groups_id'] . "' 
												ORDER BY vip_rank_sort_order DESC"; //get the lowest rank
					$select_basic_result = tep_db_query($select_basic_rank_sql);
					
					if ($select_basic_row = tep_db_fetch_array($select_basic_result)) {
						$this->vip_info['vip_rank_id'] = $select_basic_row['vip_rank_id'];
						$this->vip_info['vip_buyback_cummulative_point'] = $this->get_median_point($select_basic_row['vip_rank_upgrade_point'], $select_basic_row['vip_rank_downgrade_point']);
					} else {
						// set zero if vip_supplier_groups_id = 1 (member)
						if ((int)$this->vip_info['vip_supplier_groups_id'] == 1) {
							$this->vip_info['vip_rank_id'] = 0;
							$this->vip_info['vip_buyback_cummulative_point'] = 0;
						}
					}
				} else {
					// set zero if vip_supplier_groups_id = 1 (member)
					if ((int)$this->vip_info['vip_supplier_groups_id'] == 1) {
						$this->vip_info['vip_rank_id'] = 0;
						$this->vip_info['vip_buyback_cummulative_point'] = 0;
					}
				}
    		} else {
    			$this->vip_info['vip_buyback_cummulative_point'] += $point;
    			$this->check_vip_rank();
        	}
        	$customers_vip_data_array = array(	'vip_supplier_groups_id' => $this->vip_info['vip_supplier_groups_id'],
												'vip_rank_id' => $this->vip_info['vip_rank_id'],
												'vip_buyback_cummulative_point' => $this->vip_info['vip_buyback_cummulative_point']
											);
        	tep_db_perform(TABLE_CUSTOMERS_VIP, $customers_vip_data_array, 'update', 'customers_id="' . tep_db_input($this->customer_id) . '"');
        	if(isset($this->vip_info['vip_rank_changed']) && $this->vip_info['vip_rank_changed'] != ''){
				$this->notify_supplier();
			}
		} else {
			$this->vip_info['vip_supplier_groups_id'] = $groups_id;
			
			if (tep_not_null($region_group_id)) {
				$select_basic_rank_sql = "	SELECT * 
											FROM " . TABLE_VIP_RANK . " 
											WHERE vip_region_group_id = '" . tep_db_input($region_group_id) . "' 
												AND vip_rank_action = 'SYSTEM_VIP_GROUPS_STATUS_" . $this->vip_info['vip_supplier_groups_id'] . "' 
											ORDER BY vip_rank_sort_order DESC"; //get the lowest rank
				$select_basic_result = tep_db_query($select_basic_rank_sql);
				
				if ($select_basic_row = tep_db_fetch_array($select_basic_result)) {
					$this->vip_info['vip_rank_id'] = $select_basic_row['vip_rank_id'];
					$this->vip_info['vip_buyback_cummulative_point'] = $this->get_median_point($select_basic_row['vip_rank_upgrade_point'], $select_basic_row['vip_rank_downgrade_point']);
				}
			} else {
				// set zero if vip_supplier_groups_id = 1 (member)
				if ((int)$this->vip_info['vip_supplier_groups_id'] == 1) {
					$this->vip_info['vip_rank_id'] = 0;
					$this->vip_info['vip_buyback_cummulative_point'] = 0;
				}
			}
			
    		$customers_vip_data_array = array(	'vip_supplier_groups_id' => $this->vip_info['vip_supplier_groups_id'],
												'vip_rank_id' => (int)$this->vip_info['vip_rank_id'],
												'vip_buyback_cummulative_point' => (double)$this->vip_info['vip_buyback_cummulative_point'],
												'customers_id' => $this->customer_id
											);
			tep_db_perform(TABLE_CUSTOMERS_VIP, $customers_vip_data_array);
			//add email notification for new supplier.
			//$this->notify_supplier();
		}
	}
	
	function check_vip_rank(){	// check need to change rank 
		$select_vip_rank_sql = "SELECT vip_region_group_id, vip_rank_downgrade_point, vip_rank_upgrade_point, vip_rank_sort_order 
								FROM " . TABLE_VIP_RANK . " 
								WHERE vip_rank_id = '" . $this->vip_info['vip_rank_id'] . "'";
		$select_vip_rank_result = tep_db_query($select_vip_rank_sql);
		$select_vip_rank_row = tep_db_fetch_array($select_vip_rank_result);
		if ($this->vip_info['vip_buyback_cummulative_point'] < $select_vip_rank_row['vip_rank_downgrade_point']) { //downgrade the rank
			$this->assign_rank($select_vip_rank_row['vip_region_group_id'], $select_vip_rank_row['vip_rank_sort_order'], 'down');
		} else if ($this->vip_info['vip_buyback_cummulative_point'] > $select_vip_rank_row['vip_rank_upgrade_point']){ //up rank
			$this->assign_rank($select_vip_rank_row['vip_region_group_id'], $select_vip_rank_row['vip_rank_sort_order'], 'up');
		}
	}
	
	function assign_rank($cur_region_group_id, $cur_sort_order, $level){
		$this->vip_info['vip_rank_changed'] = '';
		$select_rank_sql = "SELECT * 
							FROM " . TABLE_VIP_RANK . " 
							WHERE vip_region_group_id= '" . $cur_region_group_id . "' 
								AND vip_rank_sort_order " . ($level == "up" ? '<' : '>') . " '" . $cur_sort_order . "' 
								AND '" . $this->vip_info['vip_buyback_cummulative_point'] . "' BETWEEN vip_rank_downgrade_point AND vip_rank_upgrade_point
							ORDER BY vip_rank_sort_order ".($level == "up" ? 'DESC' : 'ASC');
		$select_rank_result = tep_db_query($select_rank_sql);
		if ($select_rank_row = tep_db_fetch_array($select_rank_result)){
			$this->vip_info['vip_buyback_cummulative_point'] = $this->get_median_point($select_rank_row['vip_rank_upgrade_point'], $select_rank_row['vip_rank_downgrade_point']);
			$this->vip_info['old_vip_rank_id'] = $this->vip_info['vip_rank_id'];
			$this->vip_info['vip_rank_changed'] = strtoupper($level);
			$this->vip_info['vip_rank_id'] = $select_rank_row['vip_rank_id'];
			$this->check_upgrades();
		} else if($level == 'down'){ //set to default if can't search for any rank below the current rank (member = vip_supplier_groups_id = 1)
			$this->vip_info['vip_buyback_cummulative_point'] = 0;
			$this->vip_info['old_vip_rank_id'] = $this->vip_info['vip_rank_id'];
			$this->vip_info['vip_rank_changed'] = strtoupper('down');
			$this->vip_info['vip_rank_id'] = 0;
			$this->vip_info['vip_supplier_groups_id'] = 1;
		}
	}
	
	function check_upgrades() { // check need to change status?
		$rank_token = $this->get_rank_action_token();
		$rank_token_array = explode('_', $rank_token);
		
		if (strcasecmp($this->vip_info['vip_supplier_groups_id'], $rank_token_array[count($rank_token_array) - 1]) != 0){
			$this->vip_info['vip_supplier_groups_id'] = $rank_token_array[count($rank_token_array) - 1];
		}
	}
	
	function get_median_point($upgrade_point, $downgrade_point){ // set new cummulative point when rank changed
		$middle = ceil(($upgrade_point+$downgrade_point)/2);
		return $middle;
	}
	
	function get_rank_action_token() {
		$select_rank_action_sql = "	SELECT vip_rank_action 
									FROM " . TABLE_VIP_RANK . " 
									WHERE vip_rank_id='" . $this->vip_info['vip_rank_id'] . "'";
		$select_rank_action_result = tep_db_query($select_rank_action_sql);
		$select_rank_action_row = tep_db_fetch_array($select_rank_action_result);
		
		return $select_rank_action_row['vip_rank_action'];
	}
	
	function get_new_rank(){
		return $this->vip_info['vip_rank_id'];
	}
	
	function get_new_point(){
		return $this->new_middle_point;
	}
	
	function calculate_cummulative_point($action){
		$select_point_sql = "SELECT vip_rules_operator, vip_rules_value 
							FROM " . TABLE_VIP_RULES ." 
							WHERE vip_rules_key='" . $action . "'";
		$select_point_result = tep_db_query($select_point_sql);
		if($select_point_row = tep_db_fetch_array($select_point_result)){
			$operator = $select_point_row['vip_rules_operator'];
			$point = $select_point_row['vip_rules_value'];
			$this->vip_info['vip_buyback_cummulative_point'] += $operator.$point;
			$this->check_vip_rank();
			$update_point_array = array('vip_supplier_groups_id' => tep_db_input($this->vip_info['vip_supplier_groups_id']),
										'vip_rank_id' => tep_db_input($this->vip_info['vip_rank_id']),
										'vip_buyback_cummulative_point' => tep_db_input($this->vip_info['vip_buyback_cummulative_point'])
										);
			
			tep_db_perform(TABLE_CUSTOMERS_VIP, $update_point_array, 'update', 'customers_id="' . tep_db_input($this->customer_id) . '"');
			if(isset($this->vip_info['vip_rank_changed']) && $this->vip_info['vip_rank_changed'] != ''){
				$this->notify_supplier();
			}
		}
		
	}
	
	function calculate_customers_rating($point){
		$this->vip_info['vip_buyback_cummulative_point'] += $point;
		$this->check_vip_rank();
		$update_point_array = array('vip_supplier_groups_id' => tep_db_input($this->vip_info['vip_supplier_groups_id']),
									'vip_rank_id' => tep_db_input($this->vip_info['vip_rank_id']),
									'vip_buyback_cummulative_point' => tep_db_input($this->vip_info['vip_buyback_cummulative_point'])
									);
		
		tep_db_perform(TABLE_CUSTOMERS_VIP, $update_point_array, 'update', 'customers_id="' . tep_db_input($this->customer_id) . '"');
		if(isset($this->vip_info['vip_rank_changed']) && $this->vip_info['vip_rank_changed'] != ''){
			$this->notify_supplier();
		}
	}
	
	//for admin site
	function notify_supplier(){
		// email to notify supplier about their rank
    	$customer_info_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname, customers_email_address 
    								FROM " . TABLE_CUSTOMERS . " WHERE customers_id='". $this->customer_id ."'";
    	$customer_info_select_result = tep_db_query($customer_info_select_sql);
    	if($customer_info_select_row = tep_db_fetch_array($customer_info_select_result)){
    		include(DIR_WS_LANGUAGES . 'email_contents_chinese.php');
    		$email_greeting = tep_mb_convert_encoding( tep_get_email_greeting_chinese ( tep_mb_convert_encoding($customer_info_select_row['customers_firstname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE), tep_mb_convert_encoding($customer_info_select_row['customers_lastname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE), $customer_info_select_row['customers_gender']), CHARSET_CHINESE, EMAIL_CHARSET_CHINESE);
    		$rank_trend = (defined('EMAIL_RANK_LEVEL_' . $this->vip_info['vip_rank_changed']) ? constant('EMAIL_RANK_LEVEL_' . $this->vip_info['vip_rank_changed']) : $this->vip_info['vip_rank_changed']);
    		$email_body = 	$email_greeting . 
    						sprintf(EMAIL_RANK_CHANGE_BODY, $this->get_supplier_group_name($this->vip_info['vip_supplier_groups_id']), 
    						$this->get_supplier_rank_name($this->vip_info['old_vip_rank_id']), $rank_trend, $this->get_supplier_rank_name($this->vip_info['vip_rank_id']), 
    						$this->get_supplier_share_percentage($this->vip_info['old_vip_rank_id']), $rank_trend, $this->get_supplier_share_percentage($this->vip_info['vip_rank_id']),
    						$this->vip_info['vip_buyback_cummulative_point'], ($this->get_next_upgrade_point($this->vip_info['vip_rank_id']) - $this->vip_info['vip_buyback_cummulative_point'])
    						) . EMAIL_RANK_CHANGE_SUGGEST_BODY . EMAIL_RANK_CHANGED_FOOTER;
    		tep_mail(tep_mb_convert_encoding($customer_info_select_row['customers_firstname'] . ' ' . $customer_info_select_row['customers_lastname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE), $customer_info_select_row['customers_email_address'], EMAIL_RANK_CHANGE_SUBJECT, $email_body, LOCAL_STORE_NAME, LOCAL_STORE_EMAIL_ADDRESS, EMAIL_CHARSET_CHINESE);
    	}
	}
	
	//check vip info if supplier info changed. ex: country id changed.
	function check_supplier_info_changed($changed_field, $new_value){
		if ($changed_field == 'entry_country_id') {
			$rank_select_sql = "SELECT vrg.vip_region_group_id 
								FROM " . TABLE_VIP_REGION_GROUP . " AS vrg
								RIGHT JOIN " . TABLE_VIP_RANK . " AS vr
									ON (vrg.vip_region_group_id=vr.vip_region_group_id)
								WHERE vr.vip_rank_id='" . tep_db_input($this->vip_info['vip_rank_id']) . "' 
									AND FIND_IN_SET('" . $new_value . "', vrg.countries_ids)";
			$rank_select_result = tep_db_query($rank_select_sql);
			
    		if (tep_db_num_rows($rank_select_result) == 0 ) {
    			// can't find any record. set user to member.
    			$groups_id = 1; //set it to member
    			$point = 0;
    			$this->set_vip_group($groups_id, $point, $new_value);
   			}
		}
	}
	
	function show_ranking_list($filename) {
		ob_start();
?>
		<table border="0" width="100%" cellspacing="0" cellpadding="2">
			<tr>
				<td>[ <a href="<?=tep_href_link($filename, 'action=new_region_group')?>"><?=LINK_ADD_REGION?></a> ]</td>
			</tr>
			<tr>
				<td valign="top">	
					<table border="0" width="100%" cellspacing="1" cellpadding="2">
						<tr>
							<td class="reportBoxHeading" width="5%"><?=TABLE_HEADING_ACTION?></td>
							<td class="reportBoxHeading" width="10%"><?=TABLE_HEADING_REGION?></td>
							<td class="reportBoxHeading" width="20%"><?=TABLE_HEADING_COUNTRIES?></td>
							<td class="reportBoxHeading" width="55%"><?=TABLE_HEADING_RANKING?></td>
						</tr>
<?
		$row_count = 0;
		$vip_ranking_region_select_sql = "	SELECT vip_region_group_id, vip_region_group_name, countries_ids 
											FROM " . TABLE_VIP_REGION_GROUP . " 
											ORDER BY vip_region_group_sort_order ASC";
		$vip_ranking_region_result_sql = tep_db_query($vip_ranking_region_select_sql);
		
		while ($vip_ranking_region_row = tep_db_fetch_array($vip_ranking_region_result_sql)) {
			$countries_name = array();
			$row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
			
			if ($vip_ranking_region_row['countries_ids'] == 0) {
				$countries_name[] = ENTRY_OTHERS_COUNTRIES;
			} else if (tep_not_null($vip_ranking_region_row['countries_ids'])) {
				$country_select_sql = "	SELECT countries_name 
										FROM " . TABLE_COUNTRIES . " 
										WHERE countries_id IN (" . $vip_ranking_region_row['countries_ids'] . ")";
				$country_result_sql = tep_db_query($country_select_sql);
				
				while ($country_row = tep_db_fetch_array($country_result_sql)){
					$countries_name[] = $country_row['countries_name'];
				}
			}
			
			$region_rank_select_sql = "	SELECT vip_rank_id, vip_rank_name, vip_rank_sort_order, vip_rank_percentage, vip_rank_upgrade_point, vip_rank_downgrade_point 
										FROM " . TABLE_VIP_RANK . " 
										WHERE vip_region_group_id='" . $vip_ranking_region_row['vip_region_group_id'] . "' 
										ORDER BY vip_rank_sort_order";
			$region_rank_result_sql = tep_db_query($region_rank_select_sql);
?>
						<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
							<td class="reportRecords" valign="top" width="5%">
							<a href="<?=tep_href_link($filename, 'action=edit_region_group&rgID=' . $vip_ranking_region_row['vip_region_group_id'])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>&nbsp;
							<a href="javascript:void(confirm_delete('<?=$vip_ranking_region_row['vip_region_group_name']?>','VIP Region Group','<?=tep_href_link($filename, 'action=delete_region_group&rgID='.$vip_ranking_region_row['vip_region_group_id'])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
							</td>
							<td valign="top" class="reportRecords" width="10%"><?=$vip_ranking_region_row['vip_region_group_name']?></td>
							<td valign="top" class="reportRecords" width="20%"><?=implode(', ', $countries_name)?></td>
							<td valign="top" class="reportRecords" width="55%">
								<table border="0" width="100%" cellspacing="1" cellpadding="1">	
									<tr>
	          							<td colspan="5">
											<div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 1px;"></div>
										</td>
	          						</tr>
	          						<tr>
	          							<td class="dataTableContent">[<a href="<?=tep_href_link($filename, 'action=new_ranking&rgID='.$vip_ranking_region_row['vip_region_group_id'])?>"><?=LINK_ADD_NEW?></a>]</td>
	          							<td class="dataTableContent"><?=TABLE_HEADING_RANKING_NAME?></td>
	          							<td class="dataTableContent"><?=TABLE_HEADING_PRICE_PERCENTAGE?></td>
	          							<td class="dataTableContent"><?=TABLE_HEADING_DOWNGRADE_POINT?></td>
	          							<td class="dataTableContent"><?=TABLE_HEADING_UPGRADE_POINT?></td>
	          						</tr>
	          						<tr>
	          							<td colspan="5">
											<div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 1px;"></div>
										</td>
	          						</tr>
<?			while ($region_rank_row = tep_db_fetch_array($region_rank_result_sql)) { ?>
	          						<tr>
	          							<td class="dataTableContent">
	          								<a href="<?=tep_href_link($filename, 'action=edit_ranking&rgID='.$vip_ranking_region_row['vip_region_group_id'].'&rkID='.$region_rank_row['vip_rank_id'])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>&nbsp;
	          								<a href="javascript:void(confirm_delete('<?=$region_rank_row['vip_rank_name']?>', 'Rank', '<?=tep_href_link($filename, 'action=delete_ranking&rkID='.$region_rank_row['vip_rank_id'])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a></td>
	          							</td>
	          							<td class="dataTableContent"><?=$region_rank_row['vip_rank_name']?>(<?=$region_rank_row['vip_rank_sort_order']?>)</td>
	          							<td class="dataTableContent"><?=$region_rank_row['vip_rank_percentage']?></td>
	          							<td class="dataTableContent"><?=$region_rank_row['vip_rank_downgrade_point']?></td>
	          							<td class="dataTableContent"><?=$region_rank_row['vip_rank_upgrade_point']?></td>
	          						</tr>
<?			} ?>
								</table>
							</td>
						</tr>
<?			$row_count++;
		} ?>
					</table>
				</td>
			</tr>
		</table>
<?
		$report_section_html = ob_get_contents();
		ob_end_clean();
		
		return $report_section_html;
	}
	
	function manage_rank_region($filename, $input_array) {
		$action = $input_array['action'];
		$region_grp_id = isset($input_array['rgID']) ? $input_array['rgID'] : '';
		$editing_ranking_region_row = array();
		
		$countries_from_array = array( array('id' => '', 'text' => 'Select Country Lists', 'type' => 'optgroup') );
		$countries_to_array = array( array('id' => '', 'text' => 'Selected Country Lists', 'type' => 'optgroup') );
		$in_used_country_array = array();
		
		$ranking_region_select_sql = "	SELECT * 
										FROM " . TABLE_VIP_REGION_GROUP;
		$ranking_region_result_sql = tep_db_query($ranking_region_select_sql);
		while ($ranking_region_row = tep_db_fetch_array($ranking_region_result_sql)){
			if ($ranking_region_row['vip_region_group_id'] == $region_grp_id) {
				$editing_ranking_region_row = $ranking_region_row;
			} else {
				$in_used_country_array[] = explode(',', $ranking_region_row['countries_ids']);
			}
		}
		$countries_select_sql = "	SELECT countries_id, countries_name 
									FROM " . TABLE_COUNTRIES . "
									WHERE countries_id NOT IN ('" . implode("', '", $in_used_country_array) . "')";
		$countries_result_sql = tep_db_query($countries_select_sql);
		
		while ($countries_row = tep_db_fetch_array($countries_result_sql)) {
			if (isset($editing_ranking_region_row['countries_ids'])) {
				if (strpos(",".$editing_ranking_region_row['countries_ids'].",", ",".$countries_row['countries_id'].",") !== FALSE) {
					$countries_to_array[] = array( 	'id' => $countries_row['countries_id'],
												 	'text' => $countries_row['countries_name']
												  );
					continue;
				}
			}
			
			$countries_from_array[] = array('id' => $countries_row['countries_id'],
										 	'text' => $countries_row['countries_name']
										  );
		}
		
		ob_start();
		
		echo tep_draw_form('vip_region_groups_form', FILENAME_VIP_SUPPLIER_RANKING, tep_get_all_get_params(array('subaction')) . 'subaction='.($action=="new_region_group" ? 'insert_region_group' : 'update_region_group'), 'post', 'onSubmit="return vip_region_form_checking();"');
		echo tep_draw_hidden_field("rgID", $region_grp_id);
?>
		<table border="0" width="100%" cellspacing="0" cellpadding="2">
			<tr>
				<td width="100%" class="formArea">
					<table border="0" width="100%" cellspacing="2" cellpadding="1">
						<tr>
							<td class="main" width="20%"><?=ENTRY_REGION_NAME?></td>
							<td class="main" width="80%"><?=tep_draw_input_field('region_group_name', isset($editing_ranking_region_row['vip_region_group_name']) ? $editing_ranking_region_row['vip_region_group_name'] : '', ' size="40" id="region_group_name"')?></td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
						<tr>
							<td class="main" width="20%"><?=ENTRY_COUNTRY?></td>
							<td class="main" width="80%"><?=tep_draw_js_select_boxes('countries_ids', $countries_from_array, $countries_to_array, ' size="10" style="width:20em;"')?><br>
							<?
								if (!in_array('0', $in_used_country_array) || ($action == "edit_region_group" && $editing_ranking_region_row['countries_ids']== 0) ) {
									echo tep_draw_checkbox_field('others', '1', (isset($editing_ranking_region_row['countries_ids']) && $editing_ranking_region_row['countries_ids'] == 0  ? true : false), '', 'id="others_countries" onClick="disable_countries()"'). '&nbsp;'. ENTRY_OTHERS_COUNTRIES;
								}
							?>
							</td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
						<tr>
							<td class="main" width="20%"><?=ENTRY_SORT_ORDER?></td>
							<td class="main" width="80%"><?=tep_draw_input_field('region_group_sort_order', isset($editing_ranking_region_row['vip_region_group_sort_order']) ? $editing_ranking_region_row['vip_region_group_sort_order'] : '5000', ' size="40" id="region_group_sort_order"')?></td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td class="main" colspan="2" width="100%" align="right">
					<br><?=($action=="new_region_group" ? tep_submit_button(BUTTON_INSERT, ALT_BUTTON_INSERT, '', 'inputButton') : tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton')) . '&nbsp;&nbsp;' . tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_VIP_SUPPLIER_RANKING), '', 'inputButton')?>
				</td>
			</tr>
		</table>
		</form>
		<script language="javascript">
		<!--
			function vip_region_form_checking() {
				var selected_grp = document.vip_region_groups_form.elements['countries_ids_to[]'];
				if (selected_grp != null) {
					for (grp_cnt=0; grp_cnt<(selected_grp.length); grp_cnt++) {
    					selected_grp.options[grp_cnt].selected = true;
  					}
				}
			}
			
			function disable_countries(){
				if (document.vip_region_groups_form.elements['others_countries'].checked == true) {
  					document.vip_region_groups_form.elements['countries_ids_to[]'].disabled = true;
  					document.vip_region_groups_form.elements['countries_ids_from[]'].disabled = true;
  				} else {
  					document.vip_region_groups_form.elements['countries_ids_to[]'].disabled = false;
  					document.vip_region_groups_form.elements['countries_ids_from[]'].disabled = false;
  				}
			}
			disable_countries();
		//-->
		</script>
<?
		$report_section_html = ob_get_contents();
		ob_end_clean();
		
		return $report_section_html;
	}
	
	function update_rank_region($input_array, &$messageStack) {
		$subaction = $input_array['subaction'];
		$region_grp_id = isset($input_array['rgID']) ? $input_array['rgID'] : '';
		
		$error = false;
		
		if (!tep_not_null($input_array['region_group_name'])) {
			$error = true;
			$messageStack->add_session(ERROR_NO_REGION_NAME, 'error');
		}
		
		if (!tep_not_null($input_array['countries_ids_to']) && !tep_not_null($input_array['others'])){
			$error = true;
			$messageStack->add_session(ERROR_NO_COUNTRIES_SELECTED, 'error');
		}
		
		if (!tep_not_null($input_array['region_group_sort_order']) || !is_numeric($input_array['region_group_sort_order'])){
			$error = true;
			$messageStack->add_session(ERROR_NO_SORT_ORDER, 'error');
		}
		
		if ($subaction == "update_region_group" && !isset($input_array['rgID'])) {
			$error = true;
			$messageStack->add_session(ERROR_INVALID_FORM, 'error');
		}
		
		if ($input_array['others'] == true){
			$input_array['countries_ids_to'] = array('0');
		}
		
		if ($error){
			return false;
		} else {
			$region_group_data_array = array(	'vip_region_group_name' => tep_db_prepare_input($input_array['region_group_name']),
												'countries_ids' => (isset($input_array['countries_ids_to']) && count($input_array['countries_ids_to']) ? implode(",", $input_array['countries_ids_to']) : ''),
												'vip_region_group_sort_order' => tep_db_prepare_input($input_array['region_group_sort_order'])
											);
			if ($subaction == "insert_region_group"){
				tep_db_perform(TABLE_VIP_REGION_GROUP, $region_group_data_array);
			} else {
				tep_db_perform(TABLE_VIP_REGION_GROUP, $region_group_data_array, 'update', " vip_region_group_id='" . tep_db_input($region_grp_id) . "'");
			}
			return true;
		}
	}
	
	function delete_rank_region($rank_region_id, &$messageStack) {
		if (isset($rank_region_id) && tep_not_null($rank_region_id)) {
			$rank_region_delete_sql = "DELETE FROM " . TABLE_VIP_REGION_GROUP . " WHERE vip_region_group_id='" . tep_db_input($rank_region_id) . "'";
			tep_db_query($rank_region_delete_sql);
			
			$rank_delete_sql = "DELETE FROM " . TABLE_VIP_RANK . " WHERE vip_region_group_id='" . tep_db_input($rank_region_id) . "'";
			tep_db_query($rank_delete_sql);
		}
	}
	
	function manage_rank($filename, $input_array) {
		$action = $input_array['action'];
		$region_grp_id = isset($input_array['rgID']) ? $input_array['rgID'] : '';
		$rank_id = isset($input_array['rkID']) ? $input_array['rkID'] : '';
		$vip_action_token = array();
		
		$ranking_select_sql = "	SELECT vrg.vip_region_group_id, vrg.vip_region_group_name, 
									vr.vip_rank_id, vr.vip_rank_name, vr.vip_rank_sort_order, vr.vip_rank_percentage, vr.vip_rank_upgrade_point, vr.vip_rank_downgrade_point, vr.vip_rank_action 
								FROM " . TABLE_VIP_REGION_GROUP . " AS vrg 
								LEFT JOIN " . TABLE_VIP_RANK . " AS vr 
									ON (vrg.vip_region_group_id=vr.vip_region_group_id AND vr.vip_rank_id='" . tep_db_input($rank_id) . "')
								WHERE vrg.vip_region_group_id='" . tep_db_input($region_grp_id) . "'";
		$ranking_result_sql = tep_db_query($ranking_select_sql);
		$ranking_row = tep_db_fetch_array($ranking_result_sql);
		
		if (isset($ranking_row['vip_rank_action']))	$vip_action_token = explode (",", $ranking_row['vip_rank_action']);
		
		ob_start();
		echo tep_draw_form(ranking_form, FILENAME_VIP_SUPPLIER_RANKING, tep_get_all_get_params(array('subaction')).'subaction='.($action=="new_ranking" ? 'insert_ranking' : 'update_ranking'), 'post', 'onSubmit=""');
		echo tep_draw_hidden_field("rgID", $region_grp_id);
		echo tep_draw_hidden_field("rkID", $rank_id);
?>
		<table border="0" width="100%" cellspacing="0" cellpadding="2">
			<tr>
				<td width="100%" class="formArea">
					<table border="0" width="100%" cellspacing="2" cellpadding="1">
						<tr>
							<td class="main" width="18%"><?=ENTRY_SUPPLIER_REGION?></td>
							<td class="main" width="82%"><?=$ranking_row['vip_region_group_name']?></td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
						<tr>
							<td class="main"><?=ENTRY_SUPPLIER_RANKING_NAME?></td>
							<td class="main" width="82%"><?=tep_draw_input_field('rank_name', isset($ranking_row['vip_rank_name']) ? $ranking_row['vip_rank_name'] : '', ' size="20" id="rank_name"')?></td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
						<tr>
							<td class="main"><?=ENTRY_RANKING_ORDER?></td>
							<td class="main"><?=tep_draw_input_field('rank_sort_order', isset($ranking_row['vip_rank_sort_order']) ? $ranking_row['vip_rank_sort_order'] : '', ' size="20" id="rank_sort_order"')?><span class="redIndicator"><?=NOTE_THE_LOWEST_HIGH_PRIORITY?></span></td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
						<tr>
							<td class="main"><?=ENTRY_PRICE_PERCENTAGE?></td>
							<td class="main"><?=tep_draw_input_field('price_percentage', isset($ranking_row['vip_rank_percentage']) ? $ranking_row['vip_rank_percentage'] : '', ' size="20" id="price_percentage"')?>%</td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
						<tr>
							<td class="main"><?=ENTRY_VIP_RANKING_POINT?></td>
							<td class="main"><?=tep_draw_input_field('downgrade_point', isset($ranking_row['vip_rank_downgrade_point']) ? $ranking_row['vip_rank_downgrade_point'] : '', ' size="10" id="downgrade_point"') . '&nbsp;' . ENTRY_VIP_RANKING_DOWNGRADE_POINT . '&nbsp;' . tep_draw_input_field('upgrade_point', isset($ranking_row['vip_rank_upgrade_point']) ? $ranking_row['vip_rank_upgrade_point'] : '', ' size="10" id="upgrade_point"') . '&nbsp;' . ENTRY_VIP_RANKING_UPGRADE_POINT?></td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
						<tr>
							<td class="main"><?=ENTRY_SUPPLIER_STATUS?></td>
							<td class="main"><?=tep_draw_checkbox_field('SYSTEM_VIP_GROUPS_STATUS_3', '1', (isset($ranking_row['vip_rank_action']) && in_array('SYSTEM_VIP_GROUPS_STATUS_3', $vip_action_token) ? true : false)). '&nbsp;' . ENTRY_SUPPLIER_VVIP_STATUS?></td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td class="main" colspan="2" width="100%" align="right">
					<br><?=($action=="new_ranking" ? tep_submit_button(BUTTON_INSERT, ALT_BUTTON_INSERT, '', 'inputButton') : tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton')) . '&nbsp;&nbsp;' . tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_VIP_SUPPLIER_RANKING), '', 'inputButton')?>
				</td>
			</tr>
		</table>
		</form>
<?
		$report_section_html = ob_get_contents();
		ob_end_clean();
		
		return $report_section_html;
	}
	
	function update_rank($input_array, &$messageStack) {
		$subaction = $input_array['subaction'];
		$region_grp_id = isset($input_array['rgID']) ? $input_array['rgID'] : '';
		$ranking_id = isset($input_array['rkID']) ? $input_array['rkID'] : '';
		
		$error = false;
		if (!isset($input_array['rgID'])) {
			$error = true;
			$messageStack->add_session(ERROR_INVALID_FORM, 'error');
		}
		
		if ($subaction == "update_ranking" && !isset($input_array['rkID'])){
			$error = true;
			$messageStack->add_session(ERROR_INVALID_FORM, 'error');
		}
		
		if (!tep_not_null($input_array['rank_name'])) {
			$error = true;
			$messageStack->add_session(ERROR_NO_RANK_NAME, 'error');
		}
		
		if (!tep_not_null($input_array['rank_sort_order'])) {
			$error = true;
			$messageStack->add_session(ERROR_NO_RANK_LEVEL, 'error');
		}
		
		if (!tep_not_null($input_array['price_percentage']) || !is_numeric($input_array['price_percentage'])) {
			$error = true;
			$messageStack->add_session(ERROR_NO_PERCENTAGE, 'error');
		}
		
		if (!tep_not_null($input_array['upgrade_point']) || !is_numeric($input_array['upgrade_point'])) {
			$error = true;
			$messageStack->add_session(ERROR_NO_UPGRADE_POINT, 'error');
		}
		
		if (!tep_not_null($input_array['downgrade_point']) || !is_numeric($input_array['downgrade_point'])) {
			$error = true;
			$messageStack->add_session(ERROR_NO_DOWNGRADE_POINT, 'error');
		}
		
		if ($input_array['downgrade_point'] > $input_array['upgrade_point']){
			$error = true;
			$messageStack->add_session(ERROR_INVALID_UPGRADE_DOWNGRADE_POINT, 'error');			
		}
		
		if (isset($input_array['SYSTEM_VIP_GROUPS_STATUS_3'])){
			$input_array['rank_action'] = 'SYSTEM_VIP_GROUPS_STATUS_3'; //upgrade to vvip
		} else {
			$input_array['rank_action'] = 'SYSTEM_VIP_GROUPS_STATUS_2'; // upgrade to vip
		}
		
		if ($error){
			return false;
			//tep_redirect(tep_href_link(FILENAME_VIP_SUPPLIER_RANKING, tep_get_all_get_params(array('action')). 'action='.($action == "insert_ranking" ? 'new_ranking' : 'edit_ranking&rkID='.$HTTP_POST_VARS["rkID"])));
		} else {
			$ranking_data_array = array('vip_region_group_id' => tep_db_prepare_input($input_array['rgID']),
										'vip_rank_name' => tep_db_prepare_input($input_array['rank_name']),
										'vip_rank_sort_order' => tep_db_prepare_input($input_array['rank_sort_order']),
										'vip_rank_percentage' => tep_db_prepare_input($input_array['price_percentage']),
										'vip_rank_upgrade_point' => tep_db_prepare_input($input_array['upgrade_point']),
										'vip_rank_downgrade_point' => tep_db_prepare_input($input_array['downgrade_point']),
										'vip_rank_action' => tep_db_prepare_input($input_array['rank_action'])
										);
			if ($subaction == "insert_ranking") {
				tep_db_perform(TABLE_VIP_RANK, $ranking_data_array);
			} else {
				tep_db_perform(TABLE_VIP_RANK, $ranking_data_array, 'update', " vip_rank_id='" . tep_db_input($ranking_id) . "'");
			}
			
			return true;
		}
	}
	
	function delete_rank($rank_id, &$messageStack) {
		if (isset($rank_id) && tep_not_null($rank_id)) {
			$rank_delete_sql = "DELETE FROM " . TABLE_VIP_RANK . " WHERE vip_rank_id='" . tep_db_input($rank_id) . "'";
			tep_db_query($rank_delete_sql);
		}
	}
}
?>