<?php
class vip_inventory_report {
	var $mode, $sql_filter, $info;
	var $cat_array;
	var $sql_sort;
	var $total_price_decimal;
	var $csv_data, $report_header;
	var $report_legend;
	var $cat_id;
	
	function vip_inventory_report() {
      	$this->info = array();
      	$this->csv_data = array();
		$this->sub_header_type = array();
		$this->sql_sort = '';
		$this->cat_id = 0;
	}
	
	function set_mode($mode){
		$this->mode = $mode;
	}
	
	function set_main_category_id($main_cat_id, $icl_subcat) {
		$this->cat_array = array();
		
		switch ($this->mode) {
			case '1':
				$p_rank = tep_check_cat_tree_permissions(FILENAME_VIP_INVENTORY_REPORT, $main_cat_id);
		
				if ($p_rank == 1) {
					$this->cat_array = array($main_cat_id);
				}
				
		  		if ($icl_subcat) {
		  			if ($p_rank > 0) {
		  				$this->cat_array = tep_get_eligible_categories(FILENAME_VIP_INVENTORY_REPORT, $this->cat_array, $main_cat_id, false);
		  			}
		  		}
			break;
			
			case '2':
				if (tep_not_null($main_cat_id)) {
					$this->cat_id = $main_cat_id;
				}
			break;
		}
	}
	
	function set_sub_header_type($sub_header_type_array) {
		if (is_array($sub_header_type_array) && count($sub_header_type_array)) {
			$this->sub_header_type = $sub_header_type_array;
		}
	}
	
	function set_sorting_info($sorting_array) {
		for ($i=0; $i < count($sorting_array); $i++) {
			if ($i == 0)	$this->sql_sort = " ORDER BY ";
			if (tep_not_null($sorting_array[$i])) {
				$this->sql_sort .= $sorting_array[$i][0] . ' ' . $sorting_array[$i][1] . ', ';
			}
		}
		
		if (substr(trim($this->sql_sort), -1) == ',')	$this->sql_sort = substr(trim($this->sql_sort), 0, -1);
	}
	
	function reportQuery() {
		global $languages_id;
		$search_left_join = '';
		$search_by_cat_id = '';
		
		switch ($this->mode) {
			// vip inventory report
			case '1':
				$inv_query_str = "SELECT SUM(vsi.vip_supplier_inventory_qty) AS inventory_qty, p.products_id, p.products_cat_path, pd.products_name, pc.categories_id
									FROM " . TABLE_PRODUCTS . " AS p 
		  							LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
		  								ON (p.products_id=pd.products_id) 
		  							LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
		  								ON (p.products_id=pc.products_id) 
		  							LEFT JOIN " . TABLE_VIP_SUPPLIER_INVENTORY . " AS vsi
		  								ON (p.products_id = vsi.products_id)
		  							WHERE p.products_skip_inventory=0 and p.products_bundle='' 
		  								AND p.products_bundle_dynamic='' 
		  								AND pc.categories_id IN (" . implode(", ", $this->cat_array) . ") 
		  								AND pc.products_is_link=0 
		  								AND pd.language_id='" . $languages_id . "'
		  							GROUP BY p.products_id " ;
		  								
				$_SESSION['vip_param']['vip_stock_select_sql'] = $inv_query_str . $this->sql_sort;
				break;
			// show order assigned.
			case '2':
				/*
				$processing_order_select_sql = "SELECT o.orders_id, o.date_purchased, opB.orders_products_id, opB.products_quantity AS total_qty, opB.products_delivered_quantity AS processed_qty,
													 opB.parent_orders_products_id AS orders_products_bundle_id, p.products_id, p.products_cat_path, p.products_actual_quantity, opB.orders_products_purchase_eta,
													 IF(opB.orders_products_purchase_eta IS NULL or opB.orders_products_purchase_eta=-999, 
														NULL, 
														(TO_DAYS(DATE_ADD(o.date_purchased, INTERVAL opB.orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD(o.date_purchased, INTERVAL opB.orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
													) AS purchase_eta 
												FROM " . TABLE_ORDERS . " AS o 
												LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON (o.orders_id=op.orders_id AND op.parent_orders_products_id = 0) 
												LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS opB 
													ON (o.orders_id=opB.orders_id AND opB.parent_orders_products_id = op.orders_products_id ) 
												INNER JOIN " . TABLE_PRODUCTS . " AS p 
													ON (opB.products_id=p.products_id) 
												INNER JOIN " . TABLE_CATEGORIES ." AS c
													ON (op.products_categories_id=c.categories_id)
												WHERE o.orders_status='2'
													AND (opB.products_quantity - opB.products_delivered_quantity) > 0 ";
				*/
				if ($this->cat_id > 0) {
					$search_left_join = "LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
  											ON (p.products_id=pc.products_id) 
  										 LEFT JOIN " . TABLE_CATEGORIES . " AS c 
  										 	ON (pc.categories_id=c.categories_id) ";
					$search_by_cat_id = "AND c.categories_parent_path LIKE '%\_".$this->cat_id."\_%' ";
				}
				
				$processing_order_select_sql = "SELECT o.orders_id, o.date_purchased, op.orders_products_id, op.products_quantity AS total_qty, op.products_delivered_quantity AS processed_qty,
													 op.parent_orders_products_id AS orders_products_bundle_id, p.products_id, p.products_cat_path, p.products_actual_quantity, op.orders_products_purchase_eta,
													 IF(op.orders_products_purchase_eta IS NULL or op.orders_products_purchase_eta=-999, 
														NULL, 
														(TO_DAYS(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
													) AS purchase_eta 
												FROM " . TABLE_ORDERS . " AS o 
												LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON (o.orders_id=op.orders_id) 
												INNER JOIN " . TABLE_PRODUCTS . " AS p 
													ON (op.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='') 
													" . $search_left_join . " 
												WHERE o.orders_status='2'
													AND p.custom_products_type_id = 0 
													" . $search_by_cat_id . "
													AND (op.products_quantity - op.products_delivered_quantity) > 0 ";
				
				$_SESSION['vip_param']['vip_stock_select_sql'] = $processing_order_select_sql . $this->sql_sort;
				break;
	    }
	}
	
	function display_report() {
		if ($this->mode == '1') {
			$stock_select_sql = $_SESSION['vip_param']['vip_stock_select_sql'];
		  	$show_records = $_REQUEST['show_records'];
			if ($show_records != "ALL") {
				$stock_split_object = new splitPageResults($_REQUEST["page"], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $stock_select_sql, $stock_sql_numrows, true);
			}
			
			$inv_result = tep_db_query($stock_select_sql);
			$info = array();
			$cvs_date = array();
			$report_header = array();
			while($inv_row = tep_db_fetch_array($inv_result)){
				$prod_cat_path = $inv_row['products_cat_path']. ' > ' .$inv_row['products_name'];
				$prod_cat_path_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($inv_row["categories_id"]) . '&pID=' . $inv_row["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;">' . strip_tags($prod_cat_path) . '</a>';
				if (!isset($info[$inv_row["products_id"]]))  {
	 				$info[$inv_row["products_id"]] = array(	'cat_path' => $prod_cat_path,
															'cat_link' => $prod_cat_path_link,
															'total_available' => $inv_row["inventory_qty"],
															'products_id' => $inv_row["products_id"], 
															'supplier' => array()
															);
				}
				
				$customer_info_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, 
										c.customers_telephone, c.customers_mobile, c.customers_msn, c.customers_qq, 
										c.customers_yahoo, c.customers_icq, vsi.vip_supplier_inventory_qty 
										FROM " . TABLE_VIP_SUPPLIER_INVENTORY . " AS vsi
										INNER JOIN " . TABLE_CUSTOMERS . " AS c
										ON(vsi.customers_id=c.customers_id) WHERE vsi.products_id='" . (int)$inv_row["products_id"] . "'";
				$customer_info_result = tep_db_query($customer_info_sql);
				while($customer_info_row = tep_db_fetch_array($customer_info_result)){
					$info[$inv_row["products_id"]]['supplier'][] = array(	'name' => $customer_info_row['customers_firstname'] . ', ' . $customer_info_row['customers_lastname'],
																			'qty' => $customer_info_row['vip_supplier_inventory_qty'],
																			'email' => $customer_info_row['customers_email_address'],
																			'phone' => $customer_info_row['customers_telephone'],
																			'mobile' => $customer_info_row['customers_mobile'],
																			'msn' => $customer_info_row['customers_msn'],
																			'qq' => $customer_info_row['customers_qq'],
																			'yahoo' => $customer_info_row['customers_yahoo'],
																			'icq' => $customer_info_row['customers_icq']
																			);
				}
			}
			ob_start();
?>
			<!-- Begin display report //-->
			<tr>
	      		<td>
<?
			if($info){
				echo '	<table border="0" width="100%" cellspacing="1" cellpadding="2">
							<tr>
								<td class="reportBoxHeading" rowspan="2" width="100%" nowrap>'. TABLE_HEADING_PRODUCTS_NAME .'</td>';
				
				$report_header['HEADER'][] = '';
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_PRODUCTS_NAME;
				
				echo '			<td class="reportBoxHeading" rowspan="2" width="100%" nowrap>'. TABLE_HEADING_AVAILABLE_QUANTITY .'</td>';
				
				// keep in the cvs
				$report_header['HEADER'][] = '';
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_AVAILABLE_QUANTITY;
				
				$report_header['HEADER'][] = TABLE_HEADING_AVAILABLE_SUPPLIER;
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_SUPPLIER_QUANTITY;
				
				$report_header['HEADER'][] = '';
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_SUPPLIER_EMAIL;
				
				$report_header['HEADER'][] = '';
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_SUPPLIER_PHONE;
				
				$report_header['HEADER'][] = '';
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_SUPPLIER_MOBILE;
				
				$report_header['HEADER'][] = '';
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_SUPPLIER_QQ;
				
				$report_header['HEADER'][] = '';
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_SUPPLIER_MSN;
							
				$report_header['HEADER'][] = '';
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_SUPPLIER_YAHOO;
				
				$report_header['HEADER'][] = '';
				$report_header['SUB_HEADER_1'][] = TABLE_HEADING_SUPPLIER_ICQ;
				
				echo '		</tr>
							<tr>
							</tr>';
							
				$prod_cnt = 0;
				$array_counter = 0;
				$csv_data_size = count($csv_data);
				foreach ($info as $pid => $pInfo) {
					$row_style = ($prod_cnt%2) ? 'reportListingEven' : 'reportListingOdd' ;
				
					echo '			<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
										<td class="reportRecords" align="left">'.$pInfo['cat_path'].'</td>';
					
					$csv_data[$csv_data_size + $array_counter][] = $pInfo['cat_path'];
					if ((int)$pInfo['total_available'] > 0) {
						echo '				<td class="reportRecords" align="center" nowrap><a href="javascript:;" onclick="buyback_supplier_info(\'inventory\', ' . $pInfo['products_id'] . ', 0);">' . $pInfo['total_available'] .'</a></td>
										</tr>';
					} else {
						echo '				<td class="reportRecords" align="center" nowrap>&nbsp;</td>
										</tr>';
					}
					$csv_data[$csv_data_size + $array_counter][] = $pInfo['total_available'];
					$csv_data[$csv_data_size + $array_counter][] = '';
					$csv_data[$csv_data_size + $array_counter][] = '';
					$csv_data[$csv_data_size + $array_counter][] = '';
					$csv_data[$csv_data_size + $array_counter][] = '';
					$csv_data[$csv_data_size + $array_counter][] = '';
					$csv_data[$csv_data_size + $array_counter][] = '';
					$csv_data[$csv_data_size + $array_counter][] = '';
					$csv_data[$csv_data_size + $array_counter][] = '';
					
					if(count($pInfo['supplier']) > 0){
						for($sup_cnt = 0; $sup_cnt < count($pInfo['supplier']); $sup_cnt++){
							++$array_counter;
							$csv_data[$csv_data_size + $array_counter][] = $pInfo['cat_path'];
							$csv_data[$csv_data_size + $array_counter][] = '';
							$csv_data[$csv_data_size + $array_counter][] = $pInfo['supplier'][$sup_cnt]['qty'];
							$csv_data[$csv_data_size + $array_counter][] = $pInfo['supplier'][$sup_cnt]['email'];
							$csv_data[$csv_data_size + $array_counter][] = $pInfo['supplier'][$sup_cnt]['phone'];
							$csv_data[$csv_data_size + $array_counter][] = $pInfo['supplier'][$sup_cnt]['mobile'];
							$csv_data[$csv_data_size + $array_counter][] = $pInfo['supplier'][$sup_cnt]['qq'];
							$csv_data[$csv_data_size + $array_counter][] = $pInfo['supplier'][$sup_cnt]['msn'];
							$csv_data[$csv_data_size + $array_counter][] = $pInfo['supplier'][$sup_cnt]['yahoo'];
							$csv_data[$csv_data_size + $array_counter][] = $pInfo['supplier'][$sup_cnt]['icq'];								
						}
					}
					$array_counter++;
					$prod_cnt++;
				}
			}
			$report_csv_data = array_merge($report_header, $csv_data);
?>
					<tr>
   						<td colspan="2">
   							<table border="0" width="100%" cellspacing="1" cellpadding="2">
   								<tr>
									<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_PRODUCTS, tep_db_num_rows($inv_result) > 0 ? "1" : "0", tep_db_num_rows($inv_result), tep_db_num_rows($inv_result)) : $stock_split_object->display_count($stock_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_PRODUCTS)?></td>
									<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $stock_split_object->display_links($stock_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'show_records', 'x', 'y', 'cID', 'cont'))."cont=1&show_records=$show_records")?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
   						<td colspan="2">
   							<?=tep_draw_form('inventory_report_csv_form', FILENAME_VIP_INVENTORY_REPORT, tep_get_all_get_params(array('action')) . 'action=export_report&filename=supplier_inventory', 'post', '')?>
							<?=tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($report_csv_data)) . "\n"?>
   							<table border="0" width="100%" cellspacing="1" cellpadding="2">
   								<tr>
									<td align="right"><?=tep_submit_button('Export', 'Export as csv file', 'name="btn_csv_export"', 'inputButton')?></td>
								</tr>
							</table>
							</form>
						</td>
					</tr>
					<tr>
	      				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
				</table>
	      			<!-- End display report //-->
			
<?	
		} else if ($this->mode == '2') {
			$stock_select_sql = $_SESSION['vip_param']['vip_stock_select_sql'];
	  		//$show_records = $_REQUEST['show_records'];
			$vip_orders_result = tep_db_query($stock_select_sql);
			$orders = array();
			$buyback_setting_array = array();
			while ($vip_orders_row = tep_db_fetch_array($vip_orders_result)) {
				$today_timestamp = mktime(date(H), date(i), date(s), date(m), date(d), date(Y));
				$buyback_main_cat = tep_get_buyback_main_cat_info($vip_orders_row['products_id'], 'product');
				
				if (!isset($buyback_setting_array[$buyback_main_cat['id']]['ofp_purchase_eta']) && !isset($buyback_setting_array[$buyback_main_cat['id']]['vip_request_cancellation_duration'])) {
					$trade_mode_select_sql = "	SELECT buyback_setting_key, buyback_setting_value 
												FROM " . TABLE_BUYBACK_SETTING . " 
												WHERE (buyback_setting_key='vip_request_cancellation_duration'
													OR buyback_setting_key = 'ofp_purchase_eta')
													AND buyback_setting_table_name='" . TABLE_BUYBACK_CATEGORIES . "'
													AND buyback_setting_reference_id='" . $buyback_main_cat['id'] . "'";
					$trade_mode_select_result = tep_db_query($trade_mode_select_sql);
					while($trade_mode_select_row = tep_db_fetch_array($trade_mode_select_result)){
						$buyback_setting_array[$buyback_main_cat['id']][$trade_mode_select_row['buyback_setting_key']] = $trade_mode_select_row['buyback_setting_value'];
					}
				}
				
				$order_assign_select_sql = "SELECT vip_order_allocation_time 
											FROM " . TABLE_VIP_ORDER_ALLOCATION . "
											WHERE orders_products_id='" . $vip_orders_row['orders_products_id'] . "' 
												AND products_id='" . $vip_orders_row['products_id'] . "'";
				$order_assign_select_result = tep_db_query($order_assign_select_sql);
				$num_supplier_assign = 0;
				$order_assign_expired_countdown = 0;
				while ($order_assign_select_row = tep_db_fetch_array($order_assign_select_result)) {
					$order_assign_expired_timestamp = strtotime($order_assign_select_row['vip_order_allocation_time']);
					$order_assign_expired_timestamp = mktime(date(H, $order_assign_expired_timestamp), date(i, $order_assign_expired_timestamp) + $buyback_setting_array[$buyback_main_cat['id']]['vip_request_cancellation_duration'], date(s, $order_assign_expired_timestamp), date(m, $order_assign_expired_timestamp), date(d, $order_assign_expired_timestamp), date(Y, $order_assign_expired_timestamp));
					$order_assign_countdown = $order_assign_expired_timestamp - $today_timestamp;
					if ($order_assign_countdown > 0) {
						if ($order_assign_expired_countdown == 0) {
							$order_assign_expired_countdown = $order_assign_countdown;
						}
						$order_assign_expired_countdown = min($order_assign_expired_countdown, $order_assign_countdown);
					}
					$num_supplier_assign++;
				}
				
				//$forecast_actual_quantity = tep_get_first_list_quantity($vip_orders_row['products_id'], 'NORMAL') + tep_get_vip_first_list_quantity($vip_orders_row['products_id'], $vip_orders_row['orders_products_id']) + $vip_orders_row['products_actual_quantity'];
				$forecast_actual_quantity = (tep_get_first_list_quantity($vip_orders_row['products_id']) + $vip_orders_row['products_actual_quantity']) - tep_get_vip_excluded_quantity($vip_orders_row['products_id'], $vip_orders_row['orders_products_id']);
				$undeliver_quantity = $vip_orders_row['total_qty'] - $vip_orders_row['processed_qty'];
				
				//get customer character online time
				$online_countdown = tep_image(DIR_WS_ICONS.'mail_active.gif', IMAGE_ICON_IMMEDIATE);
				$purchase_timestamp = strtotime($vip_orders_row['date_purchased']);
				$orders_products_extra_info_select_sql = "SELECT orders_products_extra_info_value 
														 FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " 
														 WHERE orders_products_id = '" . (int)$vip_orders_row['orders_products_bundle_id'] . "'
														 	AND orders_products_extra_info_key = 'char_online_time'";
				$orders_products_extra_info_select_result = tep_db_query($orders_products_extra_info_select_sql);
				while ($orders_products_extra_info_select_row = tep_db_fetch_array($orders_products_extra_info_select_result)) {
					$online_timestamp = mktime(date(H, $purchase_timestamp) + $orders_products_extra_info_select_row['orders_products_extra_info_value'], date(i, $purchase_timestamp), date(s, $purchase_timestamp), date(m), date(d), date(Y));
					$online_countdown = $online_timestamp - $today_timestamp;
				}

				$main_cat_eta_offset = (isset($buyback_setting_array[$buyback_main_cat['id']]['ofp_purchase_eta']) ? $buyback_setting_array[$buyback_main_cat['id']]['ofp_purchase_eta'] : 0);
				if ($vip_orders_row['purchase_eta'] == NULL) {
					$purchase_eta_timestamp = mktime(date(H, $purchase_timestamp) + $main_cat_eta_offset, date(i, $purchase_timestamp), date(s, $purchase_timestamp), date(m, $purchase_timestamp), date(d, $purchase_timestamp), date(Y, $purchase_timestamp));
					$purchase_eta = ($purchase_eta_timestamp - $today_timestamp)/3600;
				} else {
					$purchase_eta = $vip_orders_row['purchase_eta'] + $main_cat_eta_offset;
				}
				if ($vip_orders_row['orders_products_purchase_eta'] != '-999' && $forecast_actual_quantity < $undeliver_quantity) {
					$orders[] = array(	'orders_id' => $vip_orders_row['orders_id'],
										'online_countdown' => $online_countdown,
										'online_status' => $this->get_char_oline_status((int)$vip_orders_row['orders_products_bundle_id']),
										'purchase_eta' => tep_get_purchase_eta_string($purchase_eta, 2),
										'purchase_eta_numeric' => $purchase_eta,
										'products_id' => $vip_orders_row['products_id'],
										'orders_products_id' => $vip_orders_row['orders_products_id'],
										'products_cat_path' => $vip_orders_row['products_cat_path'],
										'undeliver_quantity' => $undeliver_quantity - $forecast_actual_quantity,
										'order_assign_expired' => $order_assign_expired_countdown,
										'total_supplier' => $num_supplier_assign
									);
				}
			}
			ob_start();
?>
				<!-- Begin display report //-->
				
				<tr><td style="padding:5px;"><?=tep_button('Refresh', 'Refresh', '', 'onclick="javascript:window.location.href=\''.tep_href_link(basename($_SERVER['PHP_SELF']), tep_get_all_get_params(array('cont'))."cont=1").'\';"', 'inputButton')?></td></tr>
				<tr>
	      			<td>
	      				<table border="0" width="100%" cellspacing="1" cellpadding="2" id="report_table" class="tableSort">
	      					<thead>
							<tr>
								<th class="reportBoxHeading" rowspan="2" width="1%" align="left" style="padding-right:15px;"><span style="cursor:pointer;"><?=TABLE_HEADING_ONLINE_STATUS?></span></th>
								<th class="reportBoxHeading" rowspan="2" width="1%" align="left" style="padding-right:15px;"><span style="cursor:pointer;"><?=TABLE_HEADING_CUSTOMER_ORDERS?></span></th>
								<th class="reportBoxHeading" rowspan="2" width="40%" nowrap align="left" style="padding-right:15px;"><span style="cursor:pointer;"><?=TABLE_HEADING_PRODUCTS_NAME?></span></th>
								<th class="reportBoxHeading" rowspan="2" width="7%" align="left" style="padding-right:15px;"><span style="cursor:pointer;"><?=TABLE_HEADING_PENDING_DELIVERY?></span></th>
								<th class="reportBoxHeading" rowspan="2" width="7%" align="left" style="padding-right:15px;"><span style="cursor:pointer;"><?=TABLE_HEADING_BUYBACK_FROM?></span></th>
								<th class="reportBoxHeading" rowspan="2" width="10%" align="left" style="padding-right:15px;"><span style="cursor:pointer;"><?=TABLE_HEADING_TOTAL_SUPPLIERS_ASSIGNED?></span></th>
								<th class="reportBoxHeading" rowspan="2" width="15%" align="left" style="padding-right:15px;"><span style="cursor:pointer;"><?=TABLE_HEADING_CUSTOMER_ONLINE_COUNTDOWN?></span></th>
							</tr>
							</thead>
							<tbody>
<?			
			$prod_count = 0;
			foreach ($orders as $orders_count => $orders_products) {
				$row_style = ($prod_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
				$text_color = ($prod_count%2) ? '#E5E5E5' : '#D5D5D5' ;
				$buyback_from = 'VIP';
				$total_buyback_supplier = $orders_products['total_supplier'];
				if ($orders_products['total_supplier'] == 0) {
					$buyback_from = 'BB';
					$total_buyback_supplier = $this->get_total_supplier_based_on_server($orders_products['products_id'], $search_buyback_list_start_date, $search_buyback_list_end_date);					
				}
				
				switch ($orders_products['online_status']) {
					case '0': // red
						$online_status_sorting = '2';
						break;
					case '1': // green
						$online_status_sorting = '1';
						break;
					case '-1': // yellow
						$online_status_sorting = '3';
						break;
					default:
						$online_status_sorting = '4';
						break;
				}
				echo'<tr class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \''.$row_style.'\')">'. "\n" .
						'	<td class="reportRecords" align="center"><span class="hide">' . $online_status_sorting . '</span>' . $this->display_char_oline_status($orders_products['online_status']) . '</td>' . "\n" .
						'	<td class="reportRecords" align="center"><span><a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $orders_products['orders_id'] . '&action=edit') . '" target="_blank">' . $orders_products['orders_id'] . '</a></span>&nbsp;';
				echo 	'	</em></strong></td>' . "\n" .
						'	<td class="reportRecords" align="left"><span>' . $orders_products['products_cat_path'] . '</span></td>' . "\n" .
						'	<td class="reportRecords" align="center"><span>' . $orders_products['undeliver_quantity'] . '</span></td>' . "\n" .
						'	<td class="reportRecords" align="center"><span>' . $buyback_from .'</span></td>' . "\n" .
						'	<td class="reportRecords" align="center"><span>' . ($buyback_from == 'VIP' ? '<a href="javascript:;" onclick="buyback_supplier_info(\'order_assign\', ' . $orders_products['products_id'] . ', ' . $orders_products['orders_products_id'] . ');">' : ($total_buyback_supplier > 0 ? '<a href="javascript:;" onclick="buyback_supplier_info(\'buyback_history\', ' . $orders_products['products_id'] . ', 0);">' : '')) . $total_buyback_supplier . '</a>'.($orders_products['order_assign_expired'] == 0 ? '' : ' (' . number_format($orders_products['order_assign_expired']/60, 2) . ' min)').'</span></td>' . "\n" .
						'	<td class="reportRecords" align="center"><span class="hide">' . $orders_products['purchase_eta_numeric'] . '</span>(' . $orders_products['purchase_eta'] . ')<br /> ' . (is_numeric($orders_products['online_countdown']) ? ($orders_products['online_countdown'] > 0 ? '' : '-').tep_datetime_to_string(abs($orders_products['online_countdown']), 2) : $orders_products['online_countdown']) . '</td>'. "\n" .
					' </tr>'. "\n" ;
				$prod_count++;
			}
?>							
					</tbody>
      					<tr>
      						<td colspan="6">
	      						<table border="0" width="100%" cellspacing="1" cellpadding="2">
	   								<tr>
										<td class="smallText" valign="top"><?=sprintf(TEXT_DISPLAY_NUMBER_OF_PRODUCTS, $prod_count > 0 ? "1" : "0", $prod_count, $prod_count)?></td>
									</tr>
									<tr>
										<td class="smallText" valign="top">Legend : BB (CNBB, USBB), VIP (VIP Module) </td>
									</tr>
								</table>
							</td>
      					</tr>
      					<tr>
      						<td colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      					</tr>
					</table>
      			</td>
      		</tr>
			<!-- End display report //-->
<?
		}
		$report_html = ob_get_contents();
		ob_end_clean();
		return $report_html;
	}
	
	function report_criteria($filename){
		ob_start();
		$categories_array = tep_get_eligible_category_tree(FILENAME_VIP_INVENTORY_REPORT, 0, '___', '', $categories_array, false, 0, true);
		$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
									array ('id' => '10', "text" => "10"),
									array ('id' => '20', "text" => "20"),
									array ('id' => '50', "text" => "50"),
									array ('id' => 'ALL', "text" => "All")
								);
		$report_array = array (	array('id' => '1', 'text' => 'VIP Supplier Inventory'),
								array('id' => '2', 'text' => 'Buyback Follow Up Report')
								);
?>
					<script language="javascript">
	      			<!--
	      				function form_checking(){
	      					if (document.getElementById('report').value == '1') {
		      					if (document.getElementById('cat_id').value == '') {
									alert('<?=JS_STOCK_REPORT_CATEGORY?>');
									document.getElementById('cat_id').focus();
									return false;
								}
							}
							return true;
	      				}
	      				
	      				function swap_fields(selected) {
							switch(selected) {
								case '2':
									document.getElementById('categories_selection_section').className = 'show';
									document.getElementById('paging_selection_section').className = 'hide';
									break;
								default:
									document.getElementById('categories_selection_section').className = 'show';
									document.getElementById('paging_selection_section').className = 'show';
									break;
							}
						}
	      			//-->
	      			</script>
	      			<?=tep_draw_form('vip_inv_report_criteria', FILENAME_VIP_INVENTORY_REPORT, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', 'onSubmit="return form_checking();"');?>
	      			<tr>
	      				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
	      			<tr>
	      				<td>
				      		<table border="0" width="100%" cellspacing="2" cellpadding="0">
								<tr>
									<td class="main" width="20%"><?=TABLE_HEADING_REPORT_TYPE?></td>
					    			<td class="main" width="80%">
					    				<?=tep_draw_pull_down_menu("report", $report_array, tep_not_null($_SESSION['vip_param']["report"]) ? $_SESSION['vip_param']["report"] : '1', ' id="report" onChange="swap_fields(this.value)"')?>
					    			</td>
								</tr> 
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tbody id="categories_selection_section" class="show">
								<tr>
									<td class="main" width="20%"><?=TEXT_CATEGORIES?></td>
									<td class="main" width="80%">	<?=tep_draw_pull_down_menu("cat_id", $categories_array, $_SESSION['vip_param']["cat_id"], ' id="cat_id" ')?>
						    										<?=tep_draw_checkbox_field("include_subcategory", 1, (isset($_SESSION['vip_param']) ? ($_SESSION['vip_param']["include_subcategory"] ? true : false) : true), "", ' id="include_subcategory" ') . '&nbsp;' . TEXT_INCLUDE_SUBCATEGORY?>
						    		</td>
								</tr>
								</tbody>
	      						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tbody id="paging_selection_section" class="show">
          						<tr>
									<td class="main" width="20%"><?=TEXT_RECORDS_PER_PAGE?></td>
					    			<td class="main" width="80%">
					    				<?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION['vip_param']["show_records"]) ? $_SESSION['vip_param']["show_records"] : '', '')?>
					    			</td>
								</tr>
								</tbody>
							</table>     				
	      				</td>
	      			</tr>
	      			<tr>
	      				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
	      			<tr>
	      				<td>
			      			<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td width="20%">&nbsp;</td>
									<td align="right">
										<?=tep_submit_button(BUTTON_REPORT, ALT_BUTTON_REPORT, '', 'inputButton')?>
		  								&nbsp;&nbsp;
		  								<?=tep_button(BUTTON_RESET, ALT_BUTTON_RESET, tep_href_link(FILENAME_VIP_INVENTORY_REPORT, 'action=reset_session'), '', 'inputButton')?>								
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
	      				<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
					</form>
					<script language="javascript"><!--
						swap_fields(document.getElementById('report').value);
					//--></script>
<?		
		$report_criteria_html = ob_get_contents();
		ob_end_clean();
		return $report_criteria_html;
	}
	
	function supplier_detail($subaction, $pID, $opID){
		if ($subaction == 'inventory') {
		$customer_info_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, 
								c.customers_telephone, c.customers_mobile, c.customers_msn, c.customers_qq, 
								c.customers_yahoo, c.customers_icq, vsi.vip_supplier_inventory_qty AS quantity
								FROM " . TABLE_VIP_SUPPLIER_INVENTORY . " AS vsi
								INNER JOIN " . TABLE_CUSTOMERS . " AS c
									ON(vsi.customers_id=c.customers_id) 
								WHERE vsi.products_id='" . (int)$pID . "'";
		} else if($subaction == 'order_assign') {
			$customer_info_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, 
									c.customers_telephone, c.customers_mobile, c.customers_msn, c.customers_qq, 
									c.customers_yahoo, c.customers_icq, vip_order_allocation_quantity AS quantity
									FROM " . TABLE_VIP_ORDER_ALLOCATION . " AS voa
									INNER JOIN " . TABLE_CUSTOMERS . " AS c
										ON(voa.customers_id=c.customers_id) 
									WHERE voa.products_id='" . (int)$pID . "'
										AND voa.orders_products_id='" . (int) $opID. "'";
		}
		$customer_info_result = tep_db_query($customer_info_sql);
		ob_start();
		if(tep_db_num_rows($customer_info_result) > 0){
			echo '	<table border="0" width="100%" cellspacing="1" cellpadding="2">
							<tr>	
								<td class="reportBoxHeading" rowspan="2" width="10%" nowrap>' . TABLE_HEADING_SUPPLIER_NAME . '</td>
								<td class="reportBoxHeading" rowspan="2" width="10%" nowrap>' . TABLE_HEADING_SUPPLIER_QUANTITY . '</td>
								<td class="reportBoxHeading" rowspan="2" width="15%" nowrap>' . TABLE_HEADING_SUPPLIER_EMAIL . '</td>
								<td class="reportBoxHeading" rowspan="2" width="12%" nowrap>' . TABLE_HEADING_SUPPLIER_PHONE . '</td>
								<td class="reportBoxHeading" rowspan="2" width="12%" nowrap>' . TABLE_HEADING_SUPPLIER_MOBILE . '</td>
								<td class="reportBoxHeading" rowspan="2" width="11%" nowrap>' . TABLE_HEADING_SUPPLIER_QQ . '</td>
								<td class="reportBoxHeading" rowspan="2" width="10%" nowrap>' . TABLE_HEADING_SUPPLIER_MSN . '</td>
								<td class="reportBoxHeading" rowspan="2" width="10%" nowrap>' . TABLE_HEADING_SUPPLIER_YAHOO . '</td>
								<td class="reportBoxHeading" rowspan="2" width="10%" nowrap>' . TABLE_HEADING_SUPPLIER_ICQ . '</td>
							</tr>
							<tr>
								<td>';
			$footer = '			</td>
							</tr>
						</table>';
		}
		
		$row_cnt = 0;
		while($customer_info_row = tep_db_fetch_array($customer_info_result)){
    		$row_style = ($row_cnt%2) ? 'reportListingEven' : 'reportListingOdd' ;
			
			echo '			<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
								<td class="reportRecords" align="left">'.$customer_info_row['customers_firstname'].', '.$customer_info_row['customers_lastname'].'</td>
								<td class="reportRecords" align="left">'.$customer_info_row['quantity'].'</td>
								<td class="reportRecords" align="left">'.$customer_info_row['customers_email_address'].'</td>
								<td class="reportRecords" align="left">' . ($customer_info_row['customers_telephone']== '' ? '&nbsp;' : $customer_info_row['customers_telephone']) . '</td>
								<td class="reportRecords" align="left">' . ($customer_info_row['customers_mobile']== '' ? '&nbsp;' : $customer_info_row['customers_mobile']) . '</td>
								<td class="reportRecords" align="left">' . ($customer_info_row['customers_qq']== '' ? '&nbsp;' : $customer_info_row['customers_qq']) . '</td>
								<td class="reportRecords" align="left">' . ($customer_info_row['customers_msn']== '' ? '&nbsp;' : $customer_info_row['customers_msn']) . '</td>
								<td class="reportRecords" align="left">' . ($customer_info_row['customers_yahoo']== '' ? '&nbsp;' : $customer_info_row['customers_yahoo']) . '</td>
								<td class="reportRecords" align="left">' . ($customer_info_row['customers_icq']== '' ? '&nbsp;' : $customer_info_row['customers_icq']) . '</td>
							</tr>';
							
    		$row_cnt++;
    	}
    	echo $footer;
    	$supplier_detail_html = ob_get_contents();
		ob_end_clean();
		return $supplier_detail_html;
	}
	
	function get_total_supplier_based_on_server($products_id, $start_date, $end_date) {
		$total_supplier = 0;
		$buyback_list_select_sql = "SELECT distinct(brg.customers_id)
									FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
									INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
										ON (brg.buyback_request_group_id= br.buyback_request_group_id)
									WHERE brg.buyback_status_id <> '4'
										AND br.products_id = '" . $products_id . "'
										AND brg.buyback_request_group_date >= DATE_SUB(CURDATE(), INTERVAL 2 MONTH) 
										AND brg.buyback_request_group_date <= CURDATE()";
		$buyback_list_select_result = tep_db_query($buyback_list_select_sql);
		while ($buyback_list_select_row = tep_db_fetch_array($buyback_list_select_result)) {
			$total_supplier++;
		}
		$total_supplier;
		return $total_supplier;
	}
	
	function get_char_oline_status($order_products_id) {
		$orders_products_online_select_sql = "	SELECT orders_products_extra_info_value 
												FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " 
												WHERE  orders_products_id = '" . (int)$order_products_id . "'
													AND orders_products_extra_info_key = 'char_online_status'";
		$orders_products_online_result_sql = tep_db_query($orders_products_online_select_sql);
		if ($orders_products_online_row = tep_db_fetch_array($orders_products_online_result_sql)) {
			return $orders_products_online_row['orders_products_extra_info_value'];
		}
		return -1;
	}

	function display_char_oline_status($status_id) {
		if ($status_id == '1') {
			return tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_CHAR_ONLINE, 10, 10);
		} else if ($status_id == '0') {
			return tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_CHAR_OFFLINE, 10, 10);
		} else {
			return tep_image(DIR_WS_IMAGES . 'icon_status_yellow.gif', IMAGE_ICON_CHAR_UNKNOWN, 10, 10);
		}
	}
}
?>