<?php

//require_once(DIR_WS_CLASSES . 'email.php');
require_once(DIR_WS_FUNCTIONS . 'general.php');
require_once(DIR_WS_FUNCTIONS . 'html_output.php');
require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
require_once(DIR_WS_CLASSES . 'vip_groups.php');
require_once(DIR_WS_CLASSES . 'log.php');

class vip_order {
	var $order_detail;
	var $trade_mode;
	var $vip_expiry_duration;
	var $vip_request_cancellation_duration;
	var $_DECIMAL_PLACES = 6; //Calculation
	var $_DECIMAL_PLACES_DISPLAY = 4; //Display
	var $assigned_supplier = array();
	
	function vip_order($orders_products_id) {
		$this->order_detail = array();
		$this->order_detail['orders_products_id'] = $orders_products_id;
	}
	
	function get_products_bundle_id($orders_products_id) {
		$select_parent_id = "SELECT orders_products_id 
							FROM " . TABLE_ORDERS_PRODUCTS ." 
							WHERE parent_orders_products_id='" . tep_db_input($orders_products_id) . "'";
		$select_parent_id_result = tep_db_query($select_parent_id);
		if ($select_parent_id_row = tep_db_fetch_array($select_parent_id_result)) {
			$this->order_detail['orders_products_id'] = $select_parent_id_row['orders_products_id'];
		}
	}
	
	function get_orders_details() {
		/*unit price will based on customer's ordered package price.
		[OffGamers] Beast says:
		so the price for supplier will be based on the order price
		[OffGamers] Beast says:
		that customer pay
		[OffGamers] Beast says:
		so we won't make losses*/
		$select_parent_id = "	SELECT parent_orders_products_id 
								FROM " . TABLE_ORDERS_PRODUCTS ." 
								WHERE orders_products_id = '" . tep_db_input($this->order_detail['orders_products_id']) . "'";
		$select_parent_id_result = tep_db_query($select_parent_id);
		if ($select_parent_id_row = tep_db_fetch_array($select_parent_id_result)) {
			if ($select_parent_id_row['parent_orders_products_id'] > 0) {
				$select_order_qty_sql = "	SELECT op.orders_products_id, op.orders_id, op.products_id, p.products_cat_path, op.products_name,
												op.products_quantity - op.products_delivered_quantity AS quantity_undeliver,
												op2.orders_products_store_price / (op.products_quantity/op2.products_quantity) AS order_unit_price
											FROM " . TABLE_ORDERS_PRODUCTS . " AS op
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op2
												ON (op.parent_orders_products_id = op2.orders_products_id) 
											INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON (op.products_id = p.products_id)
											WHERE op.orders_products_id = '" . $this->order_detail['orders_products_id'] . "' 
												AND op.products_bundle_id <> '0'
												AND op.parent_orders_products_id <> '0'";
			} else {
				$select_order_qty_sql = "	SELECT op.orders_products_id, op.orders_id, op.products_id, p.products_cat_path, op.products_name,
											op.products_quantity - op.products_delivered_quantity AS quantity_undeliver,
											op.orders_products_store_price AS order_unit_price
											FROM " . TABLE_ORDERS_PRODUCTS . " AS op
											INNER JOIN " . TABLE_PRODUCTS . " AS p 
												ON (op.products_id = p.products_id)
											WHERE op.orders_products_id = '" . $this->order_detail['orders_products_id'] . "'";
			}
		} else {
			$select_order_qty_sql = "	SELECT op.orders_products_id, op.orders_id, op.products_id, p.products_cat_path, op.products_name,
											op.products_quantity - op.products_delivered_quantity AS quantity_undeliver,
											op2.orders_products_store_price / (op.products_quantity/op2.products_quantity) AS order_unit_price
										FROM " . TABLE_ORDERS_PRODUCTS . " AS op
										INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op2
											ON (op.parent_orders_products_id = op2.orders_products_id) 
										INNER JOIN " . TABLE_PRODUCTS . " AS p 
											ON (op.products_id = p.products_id)
										WHERE op.orders_products_id = '" . $this->order_detail['orders_products_id'] . "' 
											AND op.products_bundle_id <> '0'
											AND op.parent_orders_products_id <> '0'";
		}
											
		$select_order_qty_result = tep_db_query($select_order_qty_sql);
		$select_order_qty_row = tep_db_fetch_array($select_order_qty_result);
		$this->order_detail['orders_products_id'] = $select_order_qty_row['orders_products_id'];
		$this->order_detail['orders_id'] = $select_order_qty_row['orders_id'];
		$this->order_detail['products_id'] = $select_order_qty_row['products_id'];
		$this->order_detail['quantity_undeliver'] = $select_order_qty_row['quantity_undeliver'];
		$this->order_detail['quantity_need'] = $this->order_detail['quantity_undeliver'] - $this->tep_check_vip_qty_reserved($this->order_detail['orders_products_id'], $this->order_detail['products_id']);
		$this->order_detail['order_unit_price'] = $select_order_qty_row['order_unit_price'];
		// get buyback main category
		$buyback_main_cat = tep_get_buyback_main_cat_info($select_order_qty_row['products_id'], 'product');
		$this->order_detail['buyback_categories_id'] = $buyback_main_cat['id'];
		$this->order_detail['buyback_categories_name'] = $buyback_main_cat['text'];
		$this->order_detail['products_cat_path'] = $select_order_qty_row['products_cat_path'];
		$this->order_detail['products_name'] = $select_order_qty_row['products_name'];
		
		$products_to_categories_select_sql = "	SELECT categories_id 
												FROM " . TABLE_PRODUCTS_TO_CATEGORIES . "
												WHERE products_id='" . $this->order_detail['products_id'] . "'";
		$products_to_categories_select_result = tep_db_query($products_to_categories_select_sql);
		$products_to_categories_select_row = tep_db_fetch_array($products_to_categories_select_result);
		$this->order_detail['category_cat_path'] = tep_display_category_path($select_order_qty_row['products_cat_path']." > ".$select_order_qty_row['products_name'], $products_to_categories_select_row['categories_id'], 'catalog', false);		
	}	
	
	function check_vip_mode($buyback_categories_id) {
		$vip_mode = false;
		$setting_array = array();
		$select_trade_mode_sql = "	SELECT buyback_setting_key, buyback_setting_value 
									FROM " . TABLE_BUYBACK_SETTING . " 
									WHERE (	buyback_setting_key='vip_trade_mode_option' 
											OR buyback_setting_key='vip_orders_expiry_duration' 
											OR buyback_setting_key='vip_request_cancellation_duration' )
										AND buyback_setting_table_name='buyback_categories'
										AND buyback_setting_reference_id='" . tep_db_input($buyback_categories_id) . "'";
		$select_trade_mode_result = tep_db_query($select_trade_mode_sql);
		while ($select_trade_mode_row = tep_db_fetch_array($select_trade_mode_result)) {
			$setting_array[$select_trade_mode_row['buyback_setting_key']] = $select_trade_mode_row['buyback_setting_value'];
		}
		if (count($setting_array)) {
			if (isset($setting_array['vip_trade_mode_option'])) {
				$this->trade_mode = array();
				$this->trade_mode = explode(",", $setting_array['vip_trade_mode_option']);
			}
			$this->vip_request_cancellation_duration = $setting_array['vip_request_cancellation_duration'];
			$this->vip_expiry_duration = $setting_array['vip_orders_expiry_duration'];
			if (count($this->trade_mode)) {
				$vip_mode = true;
			}
		}
		return $vip_mode;
	}

	function tep_get_vip_expired_time($action) {
		//get vip order expired duration or get vip request cancellation duration
		if ($action == 'vip_order') {
			$expired_time = mktime(date("H"), date("i") + $this->vip_expiry_duration, date("s"), date("m"), date("d"), date("Y"));
		} else if ($action == 'vip_request') {
			$expired_time = mktime(date("H"), date("i") + $this->vip_request_cancellation_duration, date("s"), date("m"), date("d"), date("Y"));
		}
		return $expired_time;
	}
	/********************************************************
	 calculate quantity
	*********************************************************/
	//total submitted (but undelivered) for same orders_products_id
	function tep_check_vip_qty_reserved($orders_products_id, $products_id) {
		$qty_accepted = 0;
		$qty_accepted += tep_get_first_list_quantity($products_id);
		$qty_accepted -= tep_get_vip_excluded_quantity($products_id, $orders_products_id);
		return ($qty_accepted < 0 ? 0 : $qty_accepted);
	}
	
	// get the max qty for the order products.
	function tep_get_max_qty($products_id, $orders_products_id, $customer_id) { 
		$undeliver_qty = $this->order_detail['quantity_undeliver'];
		$reserve_qty = 0;
		$max_qty = 0;
		
		$reserve_qty = $this->tep_check_vip_qty_reserved($orders_products_id, $products_id);
		
		$max_qty = $undeliver_qty - $reserve_qty;
		// check assign max qty
		$select_allocate_qty = "SELECT vip_order_allocation_quantity 
								FROM " . TABLE_VIP_ORDER_ALLOCATION ." 
								WHERE orders_products_id='". $orders_products_id ."' AND customers_id='". $customer_id ."'";
		$select_allocate_qty_result = tep_db_query($select_allocate_qty);
		if($select_allocate_qty_row = tep_db_fetch_array($select_allocate_qty_result)){
			$allocate_qty = $select_allocate_qty_row['vip_order_allocation_quantity'];
		} else {
			$allocate_qty = 0; // order cancel
		}
		if($max_qty >= $allocate_qty){
			$max_qty = $allocate_qty;
		}
		return ($max_qty > 0 ? $max_qty : 0);
	}
	
	function check_purchase_eta($orders_products_id, $products_id) {
		$main_cat_eta_offset = tep_get_game_preset_eta($products_id);
		$floating_qty = 0;
		$allow_buyback_select_sql = "SELECT products_quantity, products_delivered_quantity 
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
										ON o.orders_id=op.orders_id 
									WHERE op.orders_products_id = '" . tep_db_input($orders_products_id) . "' 
										AND o.orders_status IN (2) 
										AND IF(	op.orders_products_purchase_eta IS NULL, 
												1, 
												IF(	op.orders_products_purchase_eta=-999, 
													0, 
													TO_DAYS(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta + " . $main_cat_eta_offset . " HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta + " . $main_cat_eta_offset . " HOUR)) < (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))
												)
											)";
		$allow_buyback_select_result = tep_db_query($allow_buyback_select_sql);
		while ($allow_buyback_select_row = tep_db_fetch_array($allow_buyback_select_result)) {
			$floating_qty = $allow_buyback_select_row['products_quantity'] - $allow_buyback_select_row['products_delivered_quantity'];
			if ($floating_qty > 0){
				return true;
			} else {
				return false;
			}
		}
	}
	
	/********************************************************
	 assign orders
	*********************************************************/
	function get_available_supplier(){
		//check order status is in processing
		$order_status_select_sql = "SELECT orders_status 
									FROM " . TABLE_ORDERS ." 
									WHERE orders_id='" . tep_db_input($this->order_detail['orders_id']) . "'";
		$order_status_select_result = tep_db_query($order_status_select_sql);
		$order_status_select_row = tep_db_fetch_array($order_status_select_result);
		
		if ((int)$order_status_select_row['orders_status'] == 2 && $this->order_detail['quantity_need'] > 0) {		
			//check available stock on hand
			$product_actual_qty = 0;
			$quantity_select_sql = "SELECT products_actual_quantity 
									FROM " . TABLE_PRODUCTS . " 
									WHERE products_id='" . $this->order_detail['products_id'] . "'";
			
			$quantity_select_result = tep_db_query($quantity_select_sql);
			if ($quantity_select_row = tep_db_fetch_array($quantity_select_result)) {
				$product_actual_qty = $quantity_select_row['products_actual_quantity'];
			}
			
			$first_list_quantity = $this->tep_check_vip_qty_reserved($this->order_detail['orders_products_id'], $this->order_detail['products_id']);
			$forecast_actual_qty = $product_actual_qty + $first_list_quantity;
			
			if ($forecast_actual_qty == 0 && $this->check_purchase_eta($this->order_detail['orders_products_id'], $this->order_detail['products_id'])) {
				$supplier_select_sql = "	SELECT customers_id, vip_supplier_inventory_qty 
											FROM " . TABLE_VIP_SUPPLIER_INVENTORY . "
											WHERE products_id='" . $this->order_detail['products_id'] . "' 
												AND vip_supplier_inventory_qty > 0
												AND vip_supplier_inventory_min_qty <= '" . $this->order_detail['quantity_need'] . "'";
				$supplier_result_sql = tep_db_query($supplier_select_sql);
				
				while ($supplier_row = tep_db_fetch_array($supplier_result_sql)) {
					if ($this->order_detail['quantity_need'] <= $supplier_row['vip_supplier_inventory_qty']) {
						$this->assign_qty($supplier_row['customers_id'], $this->order_detail['quantity_need'], $supplier_row['vip_supplier_inventory_qty']);
					}
				}
			}
		}
	}
	
	function assign_qty($customers_id, $qty_allocate, $supplier_qty) {
		// check supplier's offdays
		$off_time = true;
		$assigned_supplier = false;
		$supplier_setting_array = array();
		$local_time_zone = 0;
		$local_gmt_array = array();
		$working_day_array = array();
		$working_time_array = array();		
		
		$get_supplier_setting_sql = "	SELECT vip_supplier_setting_key, vip_supplier_setting_value 
										FROM " . TABLE_VIP_SUPPLIER_SETTING . " 
										WHERE customers_id='" . tep_db_input($customers_id) . "' 
											AND categories_id='" . tep_db_input($this->order_detail['buyback_categories_id']) . "'";
		$get_supplier_setting_result = tep_db_query($get_supplier_setting_sql);
		while ($get_supplier_setting_row = tep_db_fetch_array($get_supplier_setting_result)) {
			$supplier_setting_array[$get_supplier_setting_row['vip_supplier_setting_key']] = $get_supplier_setting_row['vip_supplier_setting_value'];
		}
		
		// convert to localtime
		if (tep_not_null($supplier_setting_array['TIME_ZONE'])) {
			$local_gmt_array = explode(" ", $supplier_setting_array['TIME_ZONE']);
			$local_time_zone = $local_gmt_array[1]*60*60;
		}
		
		if (tep_not_null($supplier_setting_array['WORKING_TIME'])) {
			$working_time_array = explode(",", $supplier_setting_array['WORKING_TIME']);
		}
		
		$current_gmt_time = gmdate("d F Y H:i:s", mktime());
		$localise_gmt_time = gmdate("d F Y H:i:s", mktime() + $local_time_zone);
		$localise_today_timestamp = strtotime($localise_gmt_time);
		$localise_today_day = date("w", $localise_today_timestamp);
		
		// check the working days and working hour
		if (tep_not_null($supplier_setting_array['WORKING_DAYS'])) {
			$working_day_array = explode(",", $supplier_setting_array['WORKING_DAYS']);
			if (in_array($localise_today_day, $working_day_array)){
				if ($working_time_array[0] != $working_time_array[1]) {
					list($start_hour, $start_min) = explode(":", $working_time_array[0]);
					$working_time_start_timestamp = mktime($start_hour, $start_min, 0, date('m', $localise_today_timestamp), date('d', $localise_today_timestamp), date('Y', $localise_today_timestamp));
					
					list($end_hour, $end_min) = explode(":", $working_time_array[1]);
					$working_time_end_timestamp = mktime($end_hour, $end_min, 0, date('m', $localise_today_timestamp), date('d', $localise_today_timestamp), date('Y', $localise_today_timestamp));
					if ($working_time_start_timestamp <= $localise_today_timestamp && $localise_today_timestamp <= $working_time_end_timestamp) {
						$off_time = false;	// Working now
					}
				} else {
					$off_time = false;	// Working now
				}
			}
		}
		
		// check supplier's group
		$customers_vip_select_sql = "SELECT vip_supplier_groups_id 
									FROM " . TABLE_CUSTOMERS_VIP ." 
									WHERE customers_id='" . tep_db_input($customers_id) . "'";
		$customers_vip_select_result = tep_db_query($customers_vip_select_sql);
		if ($customers_vip_select_row = tep_db_fetch_array($customers_vip_select_result)) {
			if ((int)$customers_vip_select_row['vip_supplier_groups_id'] != 1) {
				$vip_member = true;
			}
		}
		
		if (!$off_time && $vip_member) { //check supplier is assigned
			$check_assigned = "	SELECT customers_id 
								FROM " . TABLE_VIP_ORDER_ALLOCATION ." 
								WHERE customers_id='" . $customers_id . "' 
									AND orders_products_id='" . $this->order_detail['orders_products_id'] . "'";
			$check_assigned_result = tep_db_query($check_assigned);
			if (tep_db_num_rows($check_assigned_result)) { // record found
				$assigned_supplier =true;
			}
			
			// check supplier list is at pending or processing
			$check_submit_pending_order_sql = " SELECT br.buyback_request_id 
												FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
												INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
													ON (brg.buyback_request_group_id=br.buyback_request_group_id)
												WHERE brg.buyback_status_id = '1' 
													AND br.orders_products_id='" . $this->order_detail['orders_products_id'] . "' 
													AND brg.customers_id='" . $customers_id . "' ";
			$check_submit_pending_order_result = tep_db_query($check_submit_pending_order_sql);
			if (tep_db_num_rows($check_submit_pending_order_result)) { // record found
				$assigned_supplier =true;
			}
			$check_submit_processing_order_sql = " SELECT br.buyback_request_id 
													FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
														ON (brg.buyback_request_group_id=br.buyback_request_group_id)
													WHERE brg.buyback_status_id = '2' 
														AND br.orders_products_id='" . $this->order_detail['orders_products_id'] . "' 
														AND brg.customers_id='" . $customers_id . "'
														AND br.buyback_quantity_received = '0' ";
			$check_submit_processing_order_result = tep_db_query($check_submit_processing_order_sql);
			if (tep_db_num_rows($check_submit_processing_order_result)) { // record found
				$assigned_supplier =true;
			}
			
			if (!$assigned_supplier) { // not off today & not yet assign
				// clear memcache
				global $memcache_obj;
				
				$cache_key = TABLE_VIP_ORDER_ALLOCATION . '/vip_allocation_list/xml/customers_id/'.$customers_id;
          		$memcache_obj->delete($cache_key, 0);
				
				// assign qty
				$sql_data_array = array('orders_products_id' => (int)$this->order_detail['orders_products_id'],
										'customers_id' => (int)$customers_id,
										'products_id' => (int)$this->order_detail['products_id'],
										'vip_order_allocation_quantity' => (int)$qty_allocate,
										'vip_order_allocation_time' => date("Y-m-d H:i:s")
										);
				tep_db_perform(TABLE_VIP_ORDER_ALLOCATION, $sql_data_array);
				// deduct from supplier inv
				$inv_data_array = array('vip_supplier_inventory_qty' => $supplier_qty - $qty_allocate);
				tep_db_perform(TABLE_VIP_SUPPLIER_INVENTORY, $inv_data_array, 'update', " customers_id='" . $customers_id . "' AND products_id='" . $this->order_detail['products_id'] . "'");
				$this->notify_supplier($customers_id, $qty_allocate);
			}
		}
	}
	
	// work on admin
	function notify_supplier($customers_id, $qty_allocate) {
		$customer_info_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname, customers_email_address 
    								FROM " . TABLE_CUSTOMERS . " WHERE customers_id='". $customers_id ."'";
    	   	$customer_info_select_result = tep_db_query($customer_info_select_sql);
    	if ($customer_info_select_row = tep_db_fetch_array($customer_info_select_result)) {
	    	$this->assigned_supplier[] = $customer_info_select_row['customers_lastname'] . ', ' . $customer_info_select_row['customers_firstname'] . '  ' . $customer_info_select_row['customers_email_address']. '('.$qty_allocate.')';

    		include(DIR_WS_LANGUAGES . 'email_contents_chinese.php');
    		include_once(DIR_WS_LANGUAGES . 'english.php');
    		$email_greeting = tep_mb_convert_encoding( tep_get_email_greeting_chinese( tep_mb_convert_encoding($customer_info_select_row['customers_firstname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE), tep_mb_convert_encoding($customer_info_select_row['customers_lastname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE), $customer_info_select_row['customers_gender']), CHARSET_CHINESE, EMAIL_CHARSET_CHINESE);
			$buybackSupplierObj = new buyback_supplier($this->order_detail['buyback_categories_id'], $this->order_detail['products_id']);
			$buybackSupplierObj->vip = true;
			$buybackSupplierObj->calculate_offer_price();
			$this->order_detail['min_qty'] = $buybackSupplierObj->vip_min_qty;
    		$vip_order_history_link = tep_cnbb_href_link(FILENAME_VIP_ORDERS_HISTORY);
    		$cat_path = tep_output_generated_category_path($this->order_detail['products_id'], 'product');
    		$email_body = 	$email_greeting . "\n\n".
    						sprintf(EMAIL_VIP_ORDER_REQUEST_NOTIFY_BODY, $vip_order_history_link, $cat_path, $qty_allocate);
    		tep_mail(tep_mb_convert_encoding($customer_info_select_row['customers_firstname'] . ' ' . $customer_info_select_row['customers_lastname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE), $customer_info_select_row['customers_email_address'], EMAIL_VIP_ORDER_REQUEST_NOTIFY_SUBJECT, $email_body, LOCAL_STORE_NAME, LOCAL_STORE_EMAIL_ADDRESS, EMAIL_CHARSET_CHINESE);
    	}
	}
	
	/********************************************************
	 calculate price
	*********************************************************/
	
	function calculate_trade_us_price() {
		global $currencies, $sup_languages_id;
	    $show_error = false;	    
		
    	if ($this->order_detail['buyback_categories_id'] > 0) {
			$buybackSupplierObj = new buyback_supplier($this->order_detail['buyback_categories_id'], $this->order_detail['products_id']);
			$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
			$buybackSupplierObj->vip = true;
			$buybackSupplierObj->calculate_offer_price();
			
			$this->order_detail['min_qty'] = (int)$buybackSupplierObj->vip_min_qty;
			$this->order_detail['min_purchase_qty'] = (int)$buybackSupplierObj->vip_min_purchse_qty;

			if (!isset($buybackSupplierObj->products_arr[$this->order_detail['products_id']])) {
				$show_error = true;
			} else {
				$product_info_arr = $buybackSupplierObj->products_arr[$this->order_detail['products_id']];
				if ($buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status']=='0' || $product_info_arr['is_buyback'] == false) {
					$show_error = true;
				}
			}
    	}
	
    	if (!$show_error) {
	       	$unit_price_base = (float)$product_info_arr['avg_offer_price'];	//USD
			$this->order_detail['trade_us_price'] = $unit_price_base; // in USD
	    	$this->order_detail['trade_us_price_display'] = $currencies->format($unit_price_base, true, DISPLAY_CURRENCY); //Localised
    	}
	}
	
	function get_price_percentage($customers_id) {
		$select_percentage_sql = "	SELECT vr.vip_rank_percentage 
									FROM " . TABLE_VIP_RANK . " AS vr 
									INNER JOIN " . TABLE_CUSTOMERS_VIP . " AS cv 
										ON(vr.vip_rank_id=cv.vip_rank_id)
									WHERE cv.customers_id='".tep_db_input($customers_id)."'";
		$select_percentage_result = tep_db_query($select_percentage_sql);
		$select_percentage_row = tep_db_fetch_array($select_percentage_result);
		return $select_percentage_row['vip_rank_percentage'];
	}
	
	
	function calculate_trade_customers_price($customers_id) {
		global $currencies;
		$percentage = $this->get_price_percentage($customers_id);
		$currencies->set_decimal_places($this->_DECIMAL_PLACES_DISPLAY);
		$offer_price = (float)$this->order_detail['order_unit_price'] * ($percentage/100); //USD
		$this->order_detail['trade_customers_price'] = $offer_price; //USD
		$this->order_detail['trade_customers_price_display'] = $currencies->format($offer_price, true, DISPLAY_CURRENCY);//Localised
	}
	
	/********************************************************
	 vip order request (reject & expired)
	 *******************************************************/
	
	function order_request_expired() { 
		$expired = false;
		$select_order_expired = "SELECT customers_id, orders_products_id, products_id FROM " . TABLE_VIP_ORDER_ALLOCATION . " 
								WHERE vip_order_allocation_time <= DATE_SUB(NOW(), INTERVAL " . $this->vip_request_cancellation_duration . " MINUTE) AND orders_products_id='" . $this->order_detail['orders_products_id'] . "'";
		$select_order_expired_result = tep_db_query($select_order_expired);
		while ($select_order_expired_row = tep_db_fetch_array($select_order_expired_result)) {
			$this->order_request_cancellation($select_order_expired_row['customers_id'], $select_order_expired_row['orders_products_id'], $select_order_expired_row['products_id'], 'VIP_DEDUCT_ORDER_EXPIRATION_CANCELLATION');
			$expired =  true;
		}
		return $expired;
	}
	
	// time's up or order rejected
	function order_request_cancellation($customers_id, $orders_products_id, $products_id, $action='VIP_DEDUCT_SUPPLIER_REJECT') { 
		$select_order_allocated = "DELETE FROM " . TABLE_VIP_ORDER_ALLOCATION . " WHERE orders_products_id='" . $orders_products_id . "' AND customers_id='" .  $customers_id . "'";
		tep_db_query($select_order_allocated);
		$vipGroupsObj = new vip_groups($customers_id);
		$vipGroupsObj->calculate_cummulative_point($action);
	}
	
	/********************************************************
	 vip order accept (reject & expired)
	 *******************************************************/	
	function order_lock($buyback_request_group_id, $orders_log_system_messages, $orders_log_admin_id) {
		$log_object = new log_files($orders_log_admin_id);
		
		if ($orders_log_system_messages == ORDERS_LOG_UNLOCK_OWN_ORDER) {
			$unlocking_orders_sql = "DELETE FROM " . TABLE_LOCKING . " WHERE locking_trans_id = '" . tep_db_input($buyback_request_group_id) . "' AND locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "'";
			tep_db_query($unlocking_orders_sql);
		}
		
		$log_object->insert_orders_log($buyback_request_group_id, $orders_log_system_messages, FILENAME_BUYBACK_REQUESTS_INFO);
	} 
	
	function order_accepted_expired() { 
		$select_expired_buyback = "	SELECT br.orders_products_id, brg.buyback_request_group_id, brg.customers_id 
									FROM " . TABLE_BUYBACK_REQUEST_GROUP ." AS brg
									INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
										ON(brg.buyback_request_group_id = br.buyback_request_group_id)
									WHERE brg.buyback_request_group_expiry_date <= NOW() 
										AND brg.buyback_status_id = '1' 
										AND br.buyback_quantity_confirmed = 0 
										AND br.buyback_request_customer_matching_code = 0 
										AND brg.buyback_request_order_type = 1";
		$select_expired_buyback_result = tep_db_query($select_expired_buyback);
		while ($select_expired_buyback_row = tep_db_fetch_array($select_expired_buyback_result)) {
			$this->order_accepted_cancellation($select_expired_buyback_row['customers_id'], $select_expired_buyback_row['buyback_request_group_id'], 'VIP_DEDUCT_ORDER_EXPIRATION_CANCELLATION_AFTER_ACCEPTED');
		}
	}
	
	function order_accepted_cancellation($customers_id, $buyback_request_group_id, $action = 'VIP_DEDUCT_ORDER_CANCEL_AFTER_ACCEPTED') {
		global $currencies, $login_id;
		// Check if this Pending Order belong to this customer
		$buyback_order_check_select_sql = "	SELECT brg.buyback_status_id, brg.buyback_request_group_date, brg.currency, brg.currency_value, l.locking_by 
											FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
											LEFT JOIN " . TABLE_LOCKING . " AS l 
												ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
											WHERE brg.buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' 
												AND brg.buyback_status_id = 1
												AND brg.customers_id = '" . tep_db_input($customers_id) . "'";
		$buyback_order_check_result_sql = tep_db_query($buyback_order_check_select_sql);
		if ($buyback_order_check_row = tep_db_fetch_array($buyback_order_check_result_sql)) {
			$update_buyback_request = array('buyback_status_id' => '4',
											'show_restock' => 0
											);
			
			tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $update_buyback_request, 'update', " buyback_request_group_id='" . tep_db_input($buyback_request_group_id) . "' ");
			
			$buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
												'buyback_status_id' => '4',
												'date_added' => 'now()',
												'customer_notified' => '1'
												);
															
			if ($action == 'VIP_DEDUCT_ORDER_EXPIRATION_CANCELLATION_AFTER_ACCEPTED') {
				$buyback_history_data_array['comments'] = 'Auto cancel';
				$buyback_history_data_array['changed_by'] = 'System';
				
				if ($buyback_order_check_row['buyback_status_id'] == 1 && tep_not_null($buyback_order_check_row['locking_by'])) {
					$this->order_lock($buyback_request_group_id, ORDERS_LOG_UNLOCK_OWN_ORDER, 'System');
				}

			} else {
				if ($buyback_order_check_row['buyback_status_id'] == 1 && tep_not_null($buyback_order_check_row['locking_by'])) {
					$this->order_lock($buyback_request_group_id, ORDERS_LOG_UNLOCK_OWN_ORDER, $login_id);
				}
				
				//get customers info
				$customer_info_select_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_gender 
												FROM " . TABLE_CUSTOMERS . "
												WHERE customers_id = '" . tep_db_input($customers_id) . "'";
				$customer_info_result_sql = tep_db_query($customer_info_select_sql);
				if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
					$buyback_request_detail_select_sql = "	SELECT buyback_amount, products_id, buyback_request_quantity 
															FROM " . TABLE_BUYBACK_REQUEST ." 
															WHERE buyback_request_group_id='" . $buyback_request_group_id . "'";
					$buyback_request_detail_select_result = tep_db_query($buyback_request_detail_select_sql);
					$buyback_request_detail_select_row = tep_db_fetch_array($buyback_request_detail_select_result);
					$cat_path = tep_output_generated_category_path($buyback_request_detail_select_row['products_id'], 'product');
					$buyback_product_list = $buyback_request_detail_select_row['buyback_request_quantity'].' x '.$cat_path.' = ' . (defined('EMAIL_DISPLAY_CURRENCY') ? constant('EMAIL_DISPLAY_CURRENCY') : constant('DISPLAY_CURRENCY')) . ' ' . $currencies->apply_currency_exchange((double)$buyback_request_detail_select_row['buyback_amount'], $buyback_order_check_row['currency'], $buyback_order_check_row['currency_value'])."\n";
					
					$to_name = tep_mb_convert_encoding($customer_info_row['customers_firstname'].' '.$customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET);
					$to_email = $customer_info_row['customers_email_address'];
					
					$email_content =tep_mb_convert_encoding(tep_get_email_greeting(tep_mb_convert_encoding($customer_info_row['customers_firstname'], EMAIL_CHARSET, CHARSET), tep_mb_convert_encoding($customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_info_row['customers_gender']), CHARSET, EMAIL_CHARSET) . 
									EMAIL_BUYBACK_UPDATE_BODY . 
									sprintf(EMAIL_BUYBACK_UPDATE_ORDER_NUMBER, $buyback_request_group_id) . "\n" .
									sprintf(EMAIL_BUYBACK_UPDATE_DATE_ORDERED, $buyback_order_check_row['buyback_request_group_date']) . "\n" .
									sprintf(EMAIL_BUYBACK_UPDATE_CUSTOMER_EMAIL, $to_email) . "\n\n" .
									EMAIL_BUYBACK_UPDATE_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . $buyback_product_list .
	        						EMAIL_SEPARATOR . "\n" . 
	         						sprintf(EMAIL_BUYBACK_UPDATE_ORDER_TOTAL, defined('EMAIL_DISPLAY_CURRENCY') ? constant('EMAIL_DISPLAY_CURRENCY') : constant('DISPLAY_CURRENCY'), $currencies->apply_currency_exchange($buyback_request_detail_select_row['buyback_amount'], $buyback_order_check_row['currency'], $buyback_order_check_row['currency_value'])) . "\n\n" .
									//sprintf(EMAIL_BUYBACK_UPDATE_STATUS, tep_get_buyback_order_status_name(1, $sup_languages_id) . '->' . tep_get_buyback_order_status_name(4, $sup_languages_id)) . "\n\n" . 
									EMAIL_BUYBACK_UPDATE_STATUS . "\n" .
									EMAIL_BUYBACK_ORDER_GUIDE . "\n\n" .
									EMAIL_BUYBACK_UPDATE_FOOTER;
					tep_mail($to_name, $to_email, sprintf(EMAIL_BUYBACK_UPDATE_SUBJECT, $buyback_request_group_id), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);			
				}
				
				$buyback_history_data_array['comments'] = 'Manual cancel';
				$buyback_history_data_array['changed_by'] = $customer_info_row['customers_email_address'];
			}
			
			tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
			
			$vipGroupsObj = new vip_groups($customers_id);
			$vipGroupsObj->calculate_cummulative_point($action);
			
			return true;
		} else {
			return false;
		}
	}
	
	/********************************************************
	 restock character
	 *******************************************************/
	function tep_get_customer_trading_char($orders_products_id) {
		$select_parent_sql = "SELECT parent_orders_products_id FROM " . TABLE_ORDERS_PRODUCTS ." WHERE orders_products_id='" . (int)$orders_products_id . "'";
		$select_parent_result = tep_db_query($select_parent_sql);
		$select_parent_row = tep_db_fetch_array($select_parent_result);
		
		$orders_products_id = $select_parent_row['parent_orders_products_id'] > 0 ? $select_parent_row['parent_orders_products_id'] : $orders_products_id;
		
		$select_customers_id = "SELECT orders_products_extra_info_value 
								FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " 
								WHERE orders_products_extra_info_key='char_name' AND orders_products_id='" . $orders_products_id . "'";
		$select_customers_result = tep_db_query($select_customers_id);
		$select_customers_row = tep_db_fetch_array($select_customers_result);
		return $select_customers_row['orders_products_extra_info_value'];
	}
	
	/********************************************************
	 check customer/supplier code exist?
	 *******************************************************/
	function tep_check_trading_code($orders_products_id, $for='suppliers'){
		$code = 0;
		if($for=='suppliers'){
			$get_field = 'buyback_request_supplier_code';
		} else {
			$get_field = 'buyback_request_customer_org_code';
		}
		$select_sql = "	SELECT " . $get_field . "  
						FROM " . TABLE_BUYBACK_REQUEST . " 
						WHERE orders_products_id = '" . $orders_products_id . "'
						AND  buyback_dealing_type = 'ofp_deal_with_customers' ";
		$select_result = tep_db_query($select_sql);
		if($select_row = tep_db_fetch_array($select_result)){
			$code = $select_row[$get_field];
		}
		return $code;
	}
}
?>