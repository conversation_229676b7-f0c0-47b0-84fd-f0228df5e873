<?
class vip_rules {
	var $addition_rules, $deduction_rules;
	
	function vip_rules() {
		$this->addition_rules = array();
		$this->deduction_rules = array();
		
		$vip_rules_select_sql = "	SELECT vip_rules_key, vip_rules_title, vip_rules_operator, vip_rules_value 
									FROM " . TABLE_VIP_RULES;
		$vip_rules_result_sql = tep_db_query($vip_rules_select_sql);
		while($vip_rules_row = tep_db_fetch_array($vip_rules_result_sql)) {
			if ($vip_rules_row['vip_rules_operator'] == '+') {
				$this->addition_rules[$vip_rules_row['vip_rules_key']] = array(	'title' => $vip_rules_row['vip_rules_title'],
																				'value' => $vip_rules_row['vip_rules_value']);
			} else if ($vip_rules_row['vip_rules_operator'] == '-') {
				$this->deduction_rules[$vip_rules_row['vip_rules_key']] = array('title' => $vip_rules_row['vip_rules_title'],
																				'value' => $vip_rules_row['vip_rules_value']);
			}
		}
	}
	
	function show_rules_form($filename) {
		$addition_html = $deduction_html = '';
		
		if (count($this->addition_rules)) {
			foreach ($this->addition_rules as $rule => $rule_info) {
				$addition_html .= '	<tr>
										<td width="30%" class="main">' . $rule_info['title'] . '</td>
										<td width="70%" class="main">' . tep_draw_input_field('rules['.$rule.']', $rule_info['value'], ' size="10"', true) . '</td>
									</tr>';
			}
		}
		
		if (count($this->deduction_rules)) {
			foreach ($this->deduction_rules as $rule => $rule_info) {
				$deduction_html .= '<tr>
										<td width="30%" class="main">' . $rule_info['title'] . '</td>
										<td width="70%" class="main">' . tep_draw_input_field('rules['.$rule.']', $rule_info['value'], ' size="10"', true) . '</td>
									</tr>';
			}
		}
		
		ob_start();
		
		echo tep_draw_form('vip_rules_form', $filename, tep_get_all_get_params(array('action', 'subaction')) . 'subaction=update_rules', 'post', '');
?>
		<table border="0" width="80%" cellspacing="0" cellpadding="2">
			<tr>
				<td class="formAreaTitleBg"><?=TABLE_HEADING_VIP_RULES_DEDUCT?></td>
			</tr>
			<tr>
				<td class="formArea">
					<table border="0" width="100%" cellspacing="0" cellpadding="5">
						<?=$deduction_html?>
					</table>
				</td>
			</tr>
		</table>
		<br><br>
		<table border="0" width="80%" cellspacing="0" cellpadding="2">
			<tr>
				<td class="formAreaTitleBg"><?=TABLE_HEADING_VIP_RULES_ADD?></td>
			</tr>
			<tr>
				<td class="formArea">
					<table border="0" width="100%" cellspacing="0" cellpadding="5">
						<?=$addition_html?>
					</table>
				</td>
			</tr>
		</table>
		<br>
		<table border="0" width="80%" cellspacing="0" cellpadding="2">
			<tr>
				<td align="right" class="main"><?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', 'inputButton')?></td>
			</tr>
		</table>
		</form>
<?
		$report_section_html = ob_get_contents();
		ob_end_clean();
		
		return $report_section_html;
	}
	
	function update_rules($input_array, &$messageStack) {
		if (isset($input_array['rules']) && count($input_array['rules'])) {
			foreach ($input_array['rules'] as $rule_key => $rule_val) {
				$vip_rules_data_array = array(	'vip_rules_value' => (double)$rule_val );
		    	tep_db_perform(TABLE_VIP_RULES, $vip_rules_data_array, 'update', 'vip_rules_key="' . tep_db_input($rule_key) . '"');
			}
		}
		
		if (is_object($messageStack)) {
			$messageStack->add_session(SUCCESS_VIP_RULES_POINT_UPDATED, 'success');
		}
	}
}
?>