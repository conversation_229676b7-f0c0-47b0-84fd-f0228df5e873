<?php
include_once "item.php";
include_once "char.php";

class bag extends item {
	var $contents;

	function bag($data){
		parent::item($data);
		
		$this->contents = item_get_many($this->data['id']);
	}
  

	function out() {
		$path = tep_catalog_href_link(DIR_WS_CATALOG . DIR_WS_INTERFACE);
	
		echo '
		<div class="bag">
			<div class="bagTop">
				<div class="bagIcon">'; parent::out(); echo '</div>
				<div class="bagName">'. $this->data['char_item_name'].'</div>
			</div>';
	
		$offset = -1 * ($this->data['char_item_slot_available'] % 4);
		for( $slot = $offset, $idx = $this->data['char_item_slot_available'] - $offset; $slot < $this->data['char_item_slot_available']; $slot++, $idx-- ) {
	
			if( $idx % 4 == 0 ) {
				if( $idx == 4 ) {
					echo '<div class="bagBottomLine">';
				} else {
					echo '<div class="bagLine">';
				}
			}
			
			if( $slot < 0 ) {
				echo '<div class="bagNoSlot"></div>';
			} else {
				echo '<div class="bagSlot">';
				$item = $this->contents[$slot+1];
	
				if(isset($item)) {
					$item->out();
				}
				echo '</div>';
			}
	
			if( $idx % 4 == 1 ) {
				echo "</div>\n";
			}
		}
	
		if ($this->data['char_item_name'] == 'Backpack') {
			$char = char_get_one($this->data['game_char_id'], $this->data['game_char_history_id']);
			
			echo '<div class="bagMoneyBottom">' .
					'<div class="money">' .
			$char->get('money_g') . tep_image($path. 'img/bagCoinGold.gif', 'Gold') . '&nbsp;' .
			$char->get('money_s') . tep_image($path . 'img/bagCoinSilver.gif', 'Silver') . '&nbsp;' .
			$char->get('money_c') . tep_image($path . 'img/bagCoinBronze.gif', 'Copper') . '&nbsp;' .
					'</div>';
		} else {
			echo '<div class="bagBottom"><div></div>';
		}
	
		echo '</div></div>';
	
	}
}

function bag_get($id, $slot, $game_char_history_id, $parent) {
	$item = item_get_one($id, '', $game_char_history_id, $parent);
	
	if($item) {
		return new bag($item->data);
	} else {
		return Null;
	}
}
?>