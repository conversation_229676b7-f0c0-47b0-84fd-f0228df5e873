<?php

class item {
var $data;

	function item($data) {
		$this->data = $data;
	}

	function out() {
		global $img_url;
		global $img_suffix;
		
		$img_suffix = 'jpg';
		$path = tep_catalog_href_link(DIR_WS_CATALOG . DIR_WS_INTERFACE . 'img/');
		$socket_path = $path . 'Interface/' . 'ItemSocketingFrame/';
		
		if (tep_not_null($this->data['char_item_storage_tooltip'])) {
			$tooltip_display = $this->data['char_item_storage_tooltip'];
			$tooltip_color = $this->data['char_item_storage_color'];
			$img_path = $path . $img_url . preg_replace("|\\\\|","/", $this->data['char_item_storage_texture']) . ".$img_suffix";
		} else {
			$tooltip_display = $this->data['char_item_storage_name'];
			$tooltip_color = $this->data['char_item_storage_color'];
			$img_path = $path . $img_url . preg_replace("|\\\\|","/", $this->data['char_item_storage_texture']) . ".$img_suffix";
		}
		
		if (tep_not_null($this->data['char_item_tooltip'])) {
			$tooltip_display = $this->data['char_item_tooltip'];
			if (tep_not_null($this->data['char_item_color'])) {
				$tooltip_color = $this->data['char_item_color'];
			} else {
				$tooltip_color = "ffffff";
			}
			$img_path = tep_catalog_href_link(DIR_WS_CATALOG . DIR_WS_INTERFACE . "img/" . $img_url . preg_replace("|\\\\|","/", $this->data['char_item_texture']) . ".$img_suffix");
		}
		
		$id = $this->data['char_item_storage_id'] . $this->data['char_item_parent'] . $this->data['char_item_storage_slot'];
		
		echo '<div class="item">';
		
		$first_line = True;
		
		foreach (explode("\n", $tooltip_display) as $line) {
			$class = 'tooltipline';
			$socket_image = '';
			
			if($first_line) {
				if (strlen($tooltip_color) == 6) {
					$color = $tooltip_color;
				} else {
					$color = substr($tooltip_color, 2, 6 );
				}
				$first_line = False;
				$class = "tooltipheader";
			} else {
				if(substr($line, 0, 2) == '|c') {
					if (strlen($tooltip_color) == 6) {
						$color = $tooltip_color;
					} else {
						$color = substr($tooltip_color, 2, 6);
					}
					$line = substr( $line, 10, -2 );
				} else if (substr($line, 0, 4) == "Use:" || substr($line, 0, 6) == "Equip:") {
					$color = '00FF00';
				} else if (substr($line, 0, 13) == "Socket Bonus:") {
					$color = '808080';
				} else if (substr($line, 0, 10) == "Red Socket") {
					$color = '808080';
					$socket_image = tep_image($socket_path . 'ui-emptysocket-red.' . $img_suffix, '', 16, 16);
				} else if (substr($line, 0, 11) == "Blue Socket") {
					$color = '808080';
					$socket_image = tep_image($socket_path . 'ui-emptysocket-blue.' . $img_suffix, '', 16, 16);
				} else if (substr($line, 0, 11) == "Meta Socket") {
					$color = '808080';
					$socket_image = tep_image($socket_path . 'ui-emptysocket-meta.' . $img_suffix, '', 16, 16);
				} else if (substr($line, 0, 13) == "Yellow Socket") {
					$color = '808080';
					$socket_image = tep_image($socket_path . 'ui-emptysocket-yellow.' . $img_suffix, '', 16, 16);
				} else if (substr($line, 0, 1) == "\\" && substr($line, -1, 1) == "\"") {
					$color = 'FFFF00';
				} else {
					$color='FFFFFF';
				}
			}
			
			$line = preg_replace('/\|cffffffff([a-zA-Z0-9\s]+)\|r/', '', $line);
			$line = preg_replace('|\\>|', '&amp;gt;', $line);
			$line = preg_replace('|\\<|', '&amp;lt;', $line);
			$line = preg_replace('/(")/', '&quot;', $line);
			
			$socket_image = preg_replace('/(")/', '&quot;', $socket_image);
			
			if(!tep_not_null($line)) {
				$line = '&nbsp;';
			}
			
			if (tep_not_null($socket_image)) {
				$tip .= tep_db_input($socket_image) . '&nbsp;';
			}
			
			$tip .= tep_db_input('<font color=#'.$color.'>' . $line . '</font>' . '<br>');
		}
		
		if (tep_not_null($this->data['char_item_storage_texture']) || tep_not_null($this->data['char_item_texture'])) {
			echo tep_image($img_path, '', 40, 40, 'class="icon" onMouseover="ddrivetip(\'' .$tip. '\', \'#111\' , 250);" onMouseout="hideddrivetip();" onClick="hidebag(\''.$id.'\');" ');
		}
		
		if( ($this->data['char_item_storage_quantity'] > 1) && ($this->data['char_item_parent'] != 'bags') ) {
			echo	'<span class="quant">'.$this->data['char_item_storage_quantity'].'</span>';
		}
		echo 	'</div>';
	}
}

function item_get_one($id, $slot, $game_char_history_id, $parent) {
	$item_info_select_sql = "	SELECT cih.game_char_id, cih.game_char_history_id, cih.char_item_history_id AS id, cih.char_item_slot_available, cih.char_item_name, cih.char_item_parent, cih.char_item_color, char_item, cih.char_item_texture, cih.char_item_tooltip, cis.* 
								FROM " . TABLE_CHAR_ITEM_HISTORY . " AS cih 
								LEFT JOIN " . TABLE_CHAR_ITEM_STORAGE . " AS cis 
									ON (cih.char_item_history_id = cis.char_item_history_id 
										AND cis.char_item_storage_slot ='" . tep_db_input($slot) . "') 
								WHERE cih.game_char_id ='" . tep_db_input($id) . "' 
									AND cih.char_item_parent='" . tep_db_input($parent) . "' 
									AND cih.game_char_history_id = '" . tep_db_input($game_char_history_id) . "'";
	
	
	$item_info_result_sql = tep_db_query($item_info_select_sql);
	$item_info_row = tep_db_fetch_array($item_info_result_sql);
	
	if (tep_db_num_rows($item_info_result_sql) > 0) {
		return new item($item_info_row);
	} else {
		return null;
	}
}

function item_get_many($char_item_history_id) {
	$item_info_select_sql = "	SELECT char_item_history_id, char_item_storage_name, char_item_storage_slot, char_item_storage_color, char_item_storage, char_item_storage_texture, char_item_storage_quantity, char_item_storage_tooltip 
								FROM " . TABLE_CHAR_ITEM_STORAGE . " 
								WHERE char_item_history_id ='" . tep_db_input($char_item_history_id) . "'";
	$item_info_result_sql = tep_db_query($item_info_select_sql);
	$items = array();

	while ($item_info_row = tep_db_fetch_array($item_info_result_sql)) {
		$item = new item($item_info_row);
		$items[$item_info_row['char_item_storage_slot']] = $item;
		unset($item);
	}
	
	return $items;
}
?>