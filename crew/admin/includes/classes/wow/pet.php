<?
	class pet {
		var $data;
		
		function pet ($data) {
			$this->data = $data;
		}
		
		function out () {
			$strength_array = explode(':', $this->data['char_pet_stat_str']);
			$agility_array = explode(':', $this->data['char_pet_stat_agl']);
			$stamina_array = explode(':', $this->data['char_pet_stat_sta']);
			$intellect_array = explode(':', $this->data['char_pet_stat_int']);
			$spirit_array = explode(':', $this->data['char_pet_stat_spr']);
			
			$attack = $this->data['char_pet_melee_rating'];
			$power = $this->data['char_pet_melee_power'];
			$damage_array = explode(':', $this->data['char_pet_melee_range']);
			$defense = $this->data['char_pet_defense'];
			$armor_array = explode(':', $this->data['char_pet_armor']);
			
			$resist_fire_array = explode(':', $this->data['char_pet_res_fire']);
			$resist_frost_array = explode(':', $this->data['char_pet_res_frost']);
			$resist_nature_array = explode(':', $this->data['char_pet_res_nature']);
			$resist_shadow_array = explode(':', $this->data['char_pet_res_shadow']);
			$resist_arcane_array = explode(':', $this->data['char_pet_res_arcane']);
			$resist_holy_array = explode(':', $this->data['char_pet_holy_arcane']);
			
			echo'	<table border="0" width=100%" cellspacing="0" cellpadding="0">
						<tr>
							<td rowspan="4" width="16%">' . '&nbsp;' . '</td>
							<td colspan="2" class="pet_info_title" align="center">' . TEXT_LEVEL . ' ' . $this->data['char_pet_level'] . ' ' . $this->data['char_pet_type'] . '</td>
							<td rowspan="4" width="17%" valign="top" align="right">
								<table border="0" width="55%" cellspacing="0" cellpadding="0" class="resistTable">
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">' .$resist_fire_array[0] . '</span>
										</td>
									</tr>
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">' .$resist_nature_array[0] . '</span>
										</td>
									</tr>
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">' .$resist_arcane_array[0] . '</span>
										</td>
									</tr>
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">' .$resist_frost_array[0] . '</span>
										</td>
									</tr>
									<tr>
										<td height="29px" align="center" valign="bottom">
											<span class="white">' .$resist_shadow_array[0] . '</span>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td colspan="2" class="pet_info_title" align="center" height="12px">' . $this->data['char_pet_loyalty'] . '</td>
						</tr>
						<tr>
							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '238') .'</td>
						</tr>
						<tr>
							<td width="34%">
								<table border="0" width=95%" cellspacing="0" cellpadding="0">
									<tr>
										<td class="pet_info_title" nowarp>' .
											TEXT_STRENGTH .
										'</td>
										<td class="pet_info_info" nowarp>' .
											$strength_array[0] .
										'</td>
									</tr>
									<tr>
										<td class="pet_info_title" nowarp>' .
											TEXT_AGILITY .
										'</td>
										<td class="pet_info_info" nowarp>' .
											$agility_array[0] .	
										'</td>
									</tr>
									<tr>
										<td class="pet_info_title" nowarp>' .
											TEXT_STAMINA .
										'</td>
										<td class="pet_info_info" nowarp>' .
											$stamina_array[0] .
										'</td>
									</tr>
									<tr>
										<td class="pet_info_title" nowarp>' .
											TEXT_INTELLECT .
										'</td>
										<td class="pet_info_info" nowarp>' .
											$intellect_array[0] .
										'</td>
									</tr>
									<tr>
										<td class="pet_info_title" nowarp>' .
											TEXT_SPIRIT .
										'</td>
										<td class="pet_info_info" nowarp>' .
											$spirit_array[0] .
										'</td>
									</tr>
								</table>
							</td>
							<td width="33%" align="right">
								<table border="0" width=95%" cellspacing="0" cellpadding="0">
									<tr>
										<td class="pet_info_title" nowrap>' .
											TEXT_ATTACK .
										'</td>
										<td class="pet_info_info" align="right" nowarp>' .
											$attack .
										'</td>
									</tr>
									<tr>
										<td class="pet_info_title">' .
											TEXT_POWER .
										'</td>
										<td class="pet_info_info" align="right" nowarp>' .
											$power .	
										'</td>
									</tr>
									<tr>
										<td class="pet_info_title" nowarp>' .
											TEXT_DAMAGE .
										'</td>
										<td class="pet_info_info" align="right" nowarp>' .
											$damage_array[0] . ' - ' . $damage_array[1] .
										'</td>
									</tr>
									<tr>
										<td class="pet_info_title" nowarp>' .
											TEXT_DEFENSE .
										'</td>
										<td class="pet_info_info" align="right" nowarp>' .
											$defense .
										'</td>
									</tr>
									<tr>
										<td class="pet_info_title" nowarp>' .
											TEXT_ARMOR .
										'</td>
										<td class="pet_info_info" align="right" nowarp>' .
											$armor_array[0] .
										'</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>';
		}
	}
		
	function get_pet ($id, $game_char_history_id) {
		$pet_info_select_sql = "	SELECT cp.char_pet_id, cp.char_pet_name, cph.* 
									FROM " . TABLE_CHAR_PET . " AS cp 
									INNER JOIN " . TABLE_CHAR_PET_HISTORY . " AS cph 
									ON (cp.char_pet_id = cph.char_pet_id AND cph.game_char_history_id ='" . (int)$game_char_history_id . "') 
									WHERE cp.game_char_id ='" . (int)$id . "'";
		
		$pet_info_result_sql = tep_db_query($pet_info_select_sql);
		
		if ($pet_info_row = tep_db_fetch_array($pet_info_result_sql)) {
			$pet = new pet ($pet_info_row);
		}
		
		return $pet;
	}
?>