<?
class xml_creator {
	function xml_creator($pass_action='') {
		switch($pass_action) {
			case "create_rotating_banners":
				$this->generate_rotating_banners(DIR_FS_CATALOG . 'cache/' . FILENAME_ROTATING_BANNERS_XML);
				break;
			case "create_countries_xml":
				$this->generate_countries_xml(DIR_FS_CATALOG . 'cache/' . FILENAME_COUNTRY_LIST_XML);
				break;
		}
	}
	
	function generate_rotating_banners($pass_destination) {

		require_once(DIR_WS_CLASSES . 'json.php');
		$json = new Services_JSON();
		
		$main_games_select_sql = "	SELECT categories_structures_value   
									FROM " . TABLE_CATEGORIES_STRUCTURES . " 
									WHERE categories_structures_key  = '".SYSTEM_CATEGORIES_STRUCTURE_KEY_GAMES."'";
		$main_games_result_sql = tep_db_query($main_games_select_sql);
		$main_games_array = array();
		if ($main_games_row = tep_db_fetch_array($main_games_result_sql)) {
			$main_games_array = explode(',',$main_games_row['categories_structures_value']);
		}
		
		if (count($main_games_array)) {
			
			$languages_select_sql = "	SELECT languages_id, code, directory 
										FROM " . TABLE_LANGUAGES . " 
										ORDER BY languages_id";
			$languages_result_sql = tep_db_query($languages_select_sql);
			$languages_array = array();
			while ($languages_row = tep_db_fetch_array($languages_result_sql)) {
				$languages_array[$languages_row['languages_id']] = array(	'code' => $languages_row['code'],
																			'directory' => $languages_row['directory']);
			}
			
			$seo_main_games_array = array();
			$zone_info_array = array();
			$games_banners_array = array();
			$pm_banners_array = array();
			
			if (count($main_games_array)) {
				$main_games_cpath_select_sql = "	SELECT categories_parent_path, categories_id 
													FROM " . TABLE_CATEGORIES . "
													WHERE categories_id IN ('".implode("','",$main_games_array)."')";
				$main_games_cpath_result_sql = tep_db_query($main_games_cpath_select_sql);
				while ($main_games_cpath_row = tep_db_fetch_array($main_games_cpath_result_sql)) {
					$seo_main_games_array[$main_games_cpath_row['categories_id']] = tep_catalog_href_link(FILENAME_DEFAULT, 'cPath=' . (tep_not_null($main_games_cpath_row['categories_parent_path'])?ltrim($main_games_cpath_row['categories_parent_path'],"_").$main_games_cpath_row['categories_id']:$main_games_cpath_row['categories_id']));
				}
			}
			
			$geo_zones_select_sql = "	SELECT gz.geo_zone_id, ztgz.zone_country_id, zi.geo_zone_info  
										FROM " . TABLE_GEO_ZONES . " as gz 
										INNER JOIN " . TABLE_ZONES_TO_GEO_ZONES . " as ztgz
											ON gz.geo_zone_id = ztgz.geo_zone_id 
										LEFT JOIN " . TABLE_ZONES_INFO . " as zi
											ON gz.geo_zone_id = zi.geo_zone_id 
										WHERE geo_zone_type = '1'";
			$geo_zones_result_sql = tep_db_query($geo_zones_select_sql);
			while ($geo_zones_row = tep_db_fetch_array($geo_zones_result_sql)) {
				
				$zone_info_array[$geo_zones_row['zone_country_id']] = array();
				
				$zone_info_obj = $json->decode($geo_zones_row['geo_zone_info']);
				if (isset($zone_info_obj->zone_games_categories_banners) && count($zone_info_obj->zone_games_categories_banners)) {
					foreach ($zone_info_obj->zone_games_categories_banners as $zone_info_obj_id_loop => $zone_info_obj_data_loop) {
						$zone_info_array[$geo_zones_row['zone_country_id']][$zone_info_obj_id_loop]['categories'] = $zone_info_obj_data_loop;
						if (tep_not_null($zone_info_obj_data_loop)) {
							foreach (explode(",",$zone_info_obj_data_loop) as $zone_info_obj_key_loop) {
								$games_banners_array[$zone_info_obj_key_loop] = 1;
							}
						}
					}
				}
				
				if (isset($zone_info_obj->zone_games_pm_banners) && count($zone_info_obj->zone_games_pm_banners)) {
					foreach ($zone_info_obj->zone_games_pm_banners as $zone_info_obj_id_loop => $zone_info_obj_data_loop) {
						$zone_info_array[$geo_zones_row['zone_country_id']][$zone_info_obj_id_loop]['pm'] = $zone_info_obj_data_loop;
						if (tep_not_null($zone_info_obj_data_loop)) {
							foreach (explode(",",$zone_info_obj_data_loop) as $zone_info_obj_key_loop) {
								$pm_banners_array[$zone_info_obj_key_loop] = 1;
							}
						}
					}
				}
			}
			
			$games_banners_array = array_keys($games_banners_array);
			$games_banners_str = implode("','",$games_banners_array);
			$games_banners_array = array();
			
			$games_image_select_sql = "	SELECT bs.banners_id, bs.banners_resources_value, bs.languages_id, bs.banners_link_url 
										FROM " . TABLE_BANNERS_RESOURCES . " as bs 
										WHERE bs.banners_resources_key IN (	'".KEY_CATEGORY_BANNER_IMAGE."')
											AND bs.banners_id IN ('".$games_banners_str."')";
			$games_image_result_sql = tep_db_query($games_image_select_sql);
			$games_banners_array = array();
			while ($games_image_row = tep_db_fetch_array($games_image_result_sql)) {
				$games_banners_array[$games_image_row['banners_id']][$games_image_row['languages_id']] = array(	'value' => $games_image_row['banners_resources_value'],
																												'url' => $games_image_row['banners_link_url']);
			}
			
			$pm_banners_array = array_keys($pm_banners_array);
			$pm_banners_str = implode("','",$pm_banners_array);
			$pm_banners_array = array();
			
			$games_image_select_sql = "	SELECT bs.banners_id, bs.banners_resources_value, bs.languages_id, bs.banners_link_url 
										FROM " . TABLE_BANNERS_RESOURCES . " as bs 
										WHERE bs.banners_resources_key IN (	'".KEY_PM_BANNER_IMAGE."')
											AND bs.banners_id IN ('".$pm_banners_str."')";
			$games_image_result_sql = tep_db_query($games_image_select_sql);
			while ($games_image_row = tep_db_fetch_array($games_image_result_sql)) {
				$pm_banners_array[$games_image_row['banners_id']][$games_image_row['languages_id']] = array(	'value' => $games_image_row['banners_resources_value'],
																												'url' => $games_image_row['banners_link_url']);
			}
			
			$xml_content = "<?xml version=\"1.0\"?><COUNTRIES>";
			foreach ($zone_info_array as $zone_info_id_loop => $zone_info_data_loop) {
				$xml_content .= "<COUNTRY>
									<ID><![CDATA[".$zone_info_id_loop."]]></ID>
									<GAMES>";
				foreach ($zone_info_data_loop as $game_id_loop => $game_data_loop) {
					$attr_flag = 'r';
					$selected_rotating_banner_array = array();
					ob_start();
					if (tep_not_null($game_data_loop['categories'])) {
						$selected_rotating_banner_array = explode(",",$game_data_loop['categories']);
						if (count($selected_rotating_banner_array)) {
							foreach ($selected_rotating_banner_array as $selected_rotating_banner_data_loop) {
								if ($selected_rotating_banner_data_loop=='0') {
									$attr_flag = 'n';
								} else {
									$attr_flag = 'c';
									echo "<BANNER>
											<ID><![CDATA[".$selected_rotating_banner_data_loop."]]></ID>";
									foreach ($languages_array as $languages_result_id_loop => $languages_result_data_loop) {
										if (isset($games_banners_array[$selected_rotating_banner_data_loop][$languages_result_id_loop]['value'])) {
											echo "	<LANGUAGE>
														<ID><![CDATA[".$languages_result_id_loop."]]></ID>
														<CODE><![CDATA[".$languages_result_data_loop['code']."]]></CODE>
														<LINK><![CDATA[".$seo_main_games_array[$selected_rotating_banner_data_loop]."]]></LINK>
														<PIC><![CDATA[".tep_catalog_href_link(DIR_WS_CATALOG_LANGUAGES . $languages_array[$languages_result_id_loop]['directory'] . '/images/categories/banners/' . $games_banners_array[$selected_rotating_banner_data_loop][$languages_result_id_loop]['value'])."]]></PIC>
													</LANGUAGE>";
										}
									}
									echo "</BANNER>";
								}
							}
						}
					}
					
					$selected_rotating_banner_array = array();
					if (tep_not_null($game_data_loop['pm'])) {
						$selected_rotating_banner_array = explode(",",$game_data_loop['pm']);
						if (count($selected_rotating_banner_array)) {
							foreach ($selected_rotating_banner_array as $selected_rotating_banner_data_loop) {
								if ($selected_rotating_banner_data_loop=='0') {
									$attr_flag = 'n';
								} else {
									echo "	<BANNER>
												<ID><![CDATA[".$selected_rotating_banner_data_loop."]]></ID>";
									foreach ($languages_array as $languages_result_id_loop => $languages_result_data_loop) {
										if (isset($pm_banners_array[$selected_rotating_banner_data_loop][$languages_result_id_loop]['value'])) {
											echo "	<LANGUAGE>
														<ID><![CDATA[".$languages_result_id_loop."]]></ID>
														<CODE><![CDATA[".$languages_result_data_loop['code']."]]></CODE>
														<LINK><![CDATA[".$seo_main_games_array[$selected_rotating_banner_data_loop]."]]></LINK>
											 			<PIC><![CDATA[".tep_catalog_href_link(DIR_WS_CATALOG_LANGUAGES . $languages_array[$languages_result_id_loop]['directory'] . '/images/categories/banners/' . $pm_banners_array[$selected_rotating_banner_data_loop][$languages_result_id_loop]['value'])."]]></PIC>
											 		</LANGUAGE>";
										}
									}
									
									echo "	</BANNER>";
								}
							}
						}
					}
					$banners_content = ob_get_contents();
					ob_end_clean();
					$xml_content .= "<GAME>
										<ID><![CDATA[".$game_id_loop."]]></ID>
										<ATTR><![CDATA[".$attr_flag."]]></ATTR>
										".$banners_content."
									</GAME>";
				}
				$xml_content .= "</GAMES>";
				$xml_content .= "</COUNTRY>";
			}
			$xml_content .= "</COUNTRIES>";
			
			$xml_file_name = $pass_destination;
		    if ($handle = fopen($xml_file_name, 'w')) {
			    if (fwrite($handle, $xml_content) === FALSE) {
			    	//
			    }
			    fclose($handle);
		    }
		}
	}
	
	function generate_countries_xml($pass_filename) {
		$countries_select_sql = "	SELECT countries_id, countries_name, countries_iso_code_2 
									FROM " . TABLE_COUNTRIES . " 
									WHERE countries_display = 1 
									ORDER BY countries_name";
		$countries_result_sql = tep_db_query($countries_select_sql);
		$file= fopen($pass_filename , "w+");
		
		$xml_content ='<?xml version="1.0" encoding="UTF-8" ?>'."\n\n";
		$xml_content .="<countries>";
		
		while($countries_row = tep_db_fetch_array($countries_result_sql)) {
			$xml_content .= "<country>";
			$xml_content .= "	<countries_id><![CDATA[".$countries_row['countries_id']."]]></countries_id>";
			$xml_content .= "	<countries_name><![CDATA[".$countries_row['countries_name']."]]></countries_name>";
			$xml_content .= "	<countries_iso_code_2><![CDATA[".$countries_row['countries_iso_code_2']."]]></countries_iso_code_2>";
			$xml_content .= "</country>";
		}
		$xml_content .= "</countries>";
		fwrite($file,$xml_content);
		fclose($file);
	}
	
	function parsing_xml($filename) {
		$path = '../cache/';
		$path .= trim($filename);
		
		function startElement($parser, $name, $attrs) { 
		   echo "<" . $name . ">"; 
		} 
		
		function endElement($parser, $name) { 
		   echo "</" . $name . ">"; 
		} 
		
		function characterData($parser, $data) { 
		   echo $data; 
		} 
		
		$xml_parser = xml_parser_create(); 
		// use case-folding so we are sure to find the tag in $map_array 
		xml_parser_set_option($xml_parser, XML_OPTION_CASE_FOLDING, true); 
		xml_set_element_handler($xml_parser, "startElement", "endElement"); 
		xml_set_character_data_handler($xml_parser, "characterData"); 
		if (!($fp = fopen($path, "r"))) { 
		   die("could not open XML input"); 
		} 
		 
		while ($data = fread($fp, 4096)) { 
		   if (!xml_parse($xml_parser, $data, feof($fp))) { 
		       die(sprintf("XML error: %s at line %d", 
		                   xml_error_string(xml_get_error_code($xml_parser)), 
		                   xml_get_current_line_number($xml_parser))); 
		   }
		} 
		xml_parser_free($xml_parser);  
	}
	}
?>