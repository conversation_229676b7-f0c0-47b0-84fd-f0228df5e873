<?php
/*
  	ZIP creation class
  	==================
	This class allows you to create ZIP archives on the
 	fly with local files or dynamical contents.
  	ZLIB extensions are not necessary, so this script is
  	compatible with most of the hosting services !
  	
  	// Created by bouchon
	// http://dev.maxg.info
	// Need help ? http://forum.maxg.info
*/
class zip
{
    var $datasec = array(); // array to store compressed data
    var $ctrl_dir = array(); // central directory   
    var $eof_ctrl_dir = "\x50\x4b\x05\x06\x00\x00\x00\x00"; //end of Central directory record
    var $old_offset = 0;
	var $filesData;
	var $zipFile;
	var $isClosed;
	
    var $zipname = '';	    // Filename of the zip file
	
	function zip($zipname="")
	{
		$this->zipname = $zipname;
		return;
	}
	
	function get_List($zip_name)
	{
		/*
		eturns an array of arrays :
		+ [filename] => Extracted filename
	    + [stored_filename] => Archived filename
	    + [size] => Extracted file size
	    + [compressed_size] => Compressed file size
	    + [crc] => CRC32 of the file (HEX)
	    + [mtime] => Date (Unix time)
	    + [comment] => Comment, if present
	    + [folder] => 0 for a file, 1 for a folder
	    + [index] => Index of the file (see extract)
	    + [status] => ok if the file isn't corrupted
	 	*/
		$zip = @fopen($zip_name, 'rb');
	   	if(!$zip) return(0);
	   	$centd = $this->ReadCentralDir($zip,$zip_name);
		
	    @rewind($zip);
	    @fseek($zip, $centd['offset']);
		
	   	for ($i=0; $i<$centd['entries']; $i++)
	   	{
		    $header = $this->ReadCentralFileHeaders($zip);
		    $header['index'] = $i;$info['filename'] = $header['filename'];
		    $info['stored_filename'] = $header['stored_filename'];
		    $info['size'] = $header['size'];$info['compressed_size']=$header['compressed_size'];
		    $info['crc'] = strtoupper(dechex( $header['crc'] ));
		    $info['mtime'] = $header['mtime']; $info['comment'] = $header['comment'];
		    $info['folder'] = ($header['external']==0x41FF0010||$header['external']==16)?1:0;
		    $info['index'] = $header['index'];$info['status'] = $header['status'];
		    $ret[]=$info;
			unset($header);
	   	}
	  	return $ret;
	}
	
	function ReadCentralDir($zip,$zip_name)
	{
	  	$size = filesize($zip_name);
	  	if ($size < 277)
			$maximum_size = $size;
	  	else
			$maximum_size=277;
		
	  	@fseek($zip, $size-$maximum_size);
	  	$pos = ftell($zip); $bytes = 0x00000000;
		
	  	while ($pos < $size)
	  	{
	   		$byte = @fread($zip, 1); $bytes=($bytes << 8) | Ord($byte);
	    	if ($bytes == 0x504b0506){ $pos++; break; } $pos++;
	 	}
		
	 	$data=unpack('vdisk/vdisk_start/vdisk_entries/ventries/Vsize/Voffset/vcomment_size',
	    		    fread($zip, 18));
		
	  	if ($data['comment_size'] != 0)
	    	$centd['comment'] = fread($zip, $data['comment_size']);
	    else 
			$centd['comment'] = ''; $centd['entries'] = $data['entries'];
	  	$centd['disk_entries'] = $data['disk_entries'];
	  	$centd['offset'] = $data['offset'];$centd['disk_start'] = $data['disk_start'];
	  	$centd['size'] = $data['size'];  $centd['disk'] = $data['disk'];
	  	return $centd;
	}
	
	function ReadCentralFileHeaders($zip)
	{
   		$binary_data = fread($zip, 46);
   		$header = unpack('vchkid/vid/vversion/vversion_extracted/vflag/vcompression/vmtime/vmdate/Vcrc/Vcompressed_size/Vsize/vfilename_len/vextra_len/vcomment_len/vdisk/vinternal/Vexternal/Voffset', $binary_data);
		
	    if ($header['filename_len'] != 0)
     			$header['filename'] = fread($zip,$header['filename_len']);
   		else $header['filename'] = '';
		
   		if ($header['extra_len'] != 0)
     			$header['extra'] = fread($zip, $header['extra_len']);
   		else $header['extra'] = '';
		
   		if ($header['comment_len'] != 0)
     			$header['comment'] = fread($zip, $header['comment_len']);
  	 		else $header['comment'] = '';
		
   		if ($header['mdate'] && $header['mtime'])
   		{
	      	$hour = ($header['mtime'] & 0xF800) >> 11;
	      	$minute = ($header['mtime'] & 0x07E0) >> 5;
	      	$seconde = ($header['mtime'] & 0x001F)*2;
	      	$year = (($header['mdate'] & 0xFE00) >> 9) + 1980;
	      	$month = ($header['mdate'] & 0x01E0) >> 5;
	      	$day = $header['mdate'] & 0x001F;
	      	$header['mtime'] = mktime($hour, $minute, $seconde, $month, $day, $year);
	    }
		else
			$header['mtime'] = time();
   		
   		$header['stored_filename'] = $header['filename'];
   		$header['status'] = 'ok';
   		if (substr($header['filename'], -1) == '/')
     			$header['external'] = 0x41FF0010;
   		return $header;
		}

	/********************************************************************************
	 	adds "directory" to archive - do this before putting any files in directory! 
    	$name - name of directory... like this: "path/"								 
    	...then you can add files using add_file with names like "path/file.txt"	 
	********************************************************************************/
	function add_dir($name, $date="")
	{
		if(!$date)
			$date=time();
		
		$name = trim($name);
		if (substr($name, -1) != '/')
			$name .= '/';		// add the trailing slash
		$name = str_replace("\\", "/", $name); 
	   	
		$dtime = dechex($this->DosTime($date));
		$hexdtime = '\x' . $dtime[6] . $dtime[7]
					. '\x' . $dtime[4] . $dtime[5]
					. '\x' . $dtime[2] . $dtime[3]
					. '\x' . $dtime[0] . $dtime[1];
		eval('$hexdtime = "' . $hexdtime . '";');
		
		$fr = "\x50\x4b\x03\x04";
        $fr .= "\x0a\x00";    // ver needed to extract
        $fr .= "\x00\x00";    // gen purpose bit flag
        $fr .= "\x00\x00";    // compression method
        $fr .= $hexdtime; // last mod time and date
		
		$fr .= pack("V",0); // crc32
        $fr .= pack("V",0); //compressed filesize
        $fr .= pack("V",0); //uncompressed filesize
        $fr .= pack("v", strlen($name) ); //length of pathname
        $fr .= pack("v", 0 ); //extra field length
        $fr .= $name;
		// end of "local file header" segment
        // no "file data" segment for path
		
	   	// "data descriptor" segment (optional but necessary if archive is not served as file)
		$fr .= pack("V", 0).pack("V", 0).pack("V", 0); 	//crc32, compressed filesize, uncompressed filesize
	   	// add this entry to array
		$this -> datasec[] = $fr;
		
	   	$new_offset = strlen(implode("", $this->datasec)); 
		// ext. file attributes mirrors MS-DOS directory attr byte, detailed 
        // at http://support.microsoft.com/support/kb/articles/Q125/0/19.asp 
		
		// now add to central record
	   	$cdrec = "\x50\x4b\x01\x02";
        $cdrec .="\x00\x00";    // version made by
        $cdrec .="\x0a\x00";    // version needed to extract
        $cdrec .="\x00\x00";    // gen purpose bit flag
        $cdrec .="\x00\x00";    // compression method
        $cdrec .= $hexdtime; // last mod time & date
		
		$cdrec .= pack("V",0); // crc32
        $cdrec .= pack("V",0); //compressed filesize
        $cdrec .= pack("V",0); //uncompressed filesize
        $cdrec .= pack("v", strlen($name) ); //length of filename
        $cdrec .= pack("v", 0 ); //extra field length   
        $cdrec .= pack("v", 0 ); //file comment length
        $cdrec .= pack("v", 0 ); //disk number start
        $cdrec .= pack("v", 0 ); //internal file attributes
		
	   	$ext = "\xff\xff\xff\xff"; 
		
	   	$cdrec .= pack("V", 16 );	//external file attributes  - 'directory' bit set
		$cdrec .= pack("V", $this -> old_offset );	//relative offset of local header
		$cdrec .= $name;
		// optional extra field, file comment goes here
        // save to array
	   	$this -> ctrl_dir[] = $cdrec; 
	   	$this -> old_offset = $new_offset; 
	   	$this -> dirs[] = $name;
	}
	
	function add_file($data, $name, $compact = 1)
	{
		// adds "file" to archive   
	    // $data - file contents
	    // $name - name of file in archive. Add path if your want
		$name = str_replace('\\', '/', $name);
	   	$dtime = dechex($this->DosTime());
		
	   	$hexdtime = '\x' . $dtime[6] . $dtime[7].'\x'.$dtime[4] . $dtime[5]
	     			. '\x' . $dtime[2] . $dtime[3].'\x'.$dtime[0].$dtime[1];
	   	eval('$hexdtime = "' . $hexdtime . '";');
		
	   	if($compact)
	   		$fr = "\x50\x4b\x03\x04\x14\x00\x00\x00\x08\x00".$hexdtime;
	   	else $fr = "\x50\x4b\x03\x04\x0a\x00\x00\x00\x00\x00".$hexdtime;
	   	
		$unc_len = strlen($data);
		$crc = crc32($data);
		
	   	if($compact)
		{
	     	$zdata = gzcompress($data); $c_len = strlen($zdata);
	     	$zdata = substr(substr($zdata, 0, strlen($zdata) - 4), 2);
	   	}
		else
	     	$zdata = $data;
	   	
	   	$c_len=strlen($zdata);
	   	
		$fr .= pack('V', $crc).pack('V', $c_len).pack('V', $unc_len); // crc32, compressed filesize, uncompressed filesize
	   	$fr .= pack('v', strlen($name)).pack('v', 0).$name.$zdata;	//length of pathname, extra field length, "file data" segment
		
		// "data descriptor" segment (optional but necessary if archive is not served as file)
	   	$fr .= pack('V', $crc).pack('V', $c_len).pack('V', $unc_len);
		
	   	$this -> datasec[] = $fr;	// add this entry to array
		$new_offset = strlen(implode('', $this->datasec));
	   	
		// now add to central directory record
		if($compact)
	    {
		    $cdrec = "\x50\x4b\x01\x02";
			$cdrec .= "\x00\x00";	// version made by
			$cdrec .= "\x14\x00";	// version needed to extract
			$cdrec .= "\x00\x00";	// gen purpose bit flag
			$cdrec .= "\x08\x00";	// compression method
		}
	   	else
		{
			$cdrec = "\x50\x4b\x01\x02";
			$cdrec .= "\x14\x00";	// version made by
			$cdrec .= "\x0a\x00";	// version needed to extract
			$cdrec .= "\x00\x00";	// gen purpose bit flag
			$cdrec .= "\x00\x00";	// compression method
	   	}
		// last mod time & date
		$cdrec .= $hexdtime;
		$cdrec .= pack('V', $crc);	// crc32
		$cdrec .= pack('V', $c_len);	//compressed filesize
		$cdrec .= pack('V', $unc_len);	//uncompressed filesize
	   	$cdrec .= pack('v', strlen($name) );	//length of filename
		$cdrec .= pack('v', 0 );	//extra field length
		$cdrec .= pack('v', 0 );	//file comment length
	   	$cdrec .= pack('v', 0 );	//disk number start
		$cdrec .= pack('v', 0 );	//internal file attributes
		$cdrec .= pack('V', 32 );	//external file attributes - 'archive' bit set
	   	$cdrec .= pack('V', $this -> old_offset );	//relative offset of local header
		
	   	$this -> old_offset = $new_offset;
	   	$cdrec .= $name;
	   	// optional extra field, file comment goes here
        // save to central directory
		$this -> ctrl_dir[] = $cdrec;
	   	return true;
	}
	
	function DosTime()
	{
		$timearray = getdate();
	   	if ($timearray['year'] < 1980)
		{
	     	$timearray['year'] = 1980; $timearray['mon'] = 1;
	     	$timearray['mday'] = 1; $timearray['hours'] = 0;
	     	$timearray['minutes'] = 0; $timearray['seconds'] = 0;
	   	}
	   	return (($timearray['year'] - 1980) << 25) | ($timearray['mon'] << 21) |     ($timearray['mday'] << 16) | ($timearray['hours'] << 11) | 
	    	($timearray['minutes'] << 5) | ($timearray['seconds'] >> 1);
	}
	
	function file() { // dump out file   
        $data = implode("", $this -> datasec);  
        $ctrldir = implode("", $this -> ctrl_dir);  

		$this->filesData = $data.  
				            $ctrldir.  
				            $this -> eof_ctrl_dir.  
				            pack("v", sizeof($this -> ctrl_dir)).     // total # of entries "on this disk"
				            pack("v", sizeof($this -> ctrl_dir)).     // total # of entries overall
				            pack("V", strlen($ctrldir)).             // size of central dir
				            pack("V", strlen($data)).                 // offset to start of central dir
				            "\x00\x00";                             // .zip file comment length
		
		$this->zipFile =& $this->filesData;
		$this->isClosed = true;
		return $this->filesData;
    }
	
	function Extract( $zn, $to, $index = Array(-1) )
	{
		/*
		Extract all or part of an archive.
	    { $zn } is the (folder/)name of the archive you want
	    extract.
	    { $to } is the folder where files will be extracted.
	    Beware ! Permissions must be accessible for PHP !
		{ $index } is an array of file indexes you want to
    	extract. If one of thoses index is -1 or if {index} is absent,
    	the whole archive is extracted
		*/
	   	if(!@is_dir($to))
			@mkdir($to,0777);
	   	$ok = 0;
		$zip = @fopen($zn,'rb');
	   	if(!$zip)
			return(-1);
	   	$cdir = $this->ReadCentralDir($zip,$zn);
	   	$pos_entry = $cdir['offset'];
		
	   	if(!is_array($index))
			$index = array($index);
		
	   	for($i=0; $index[$i];$i++)
		{
	     	if(intval($index[$i])!=$index[$i]||$index[$i]>$cdir['entries'])
	      		return(-1);
	   	}
		
	   	for ($i=0; $i<$cdir['entries']; $i++)
	   	{
	     	@fseek($zip, $pos_entry);
	     	$header = $this->ReadCentralFileHeaders($zip);
	     	$header['index'] = $i;
			$pos_entry = ftell($zip);
	     	@rewind($zip);
			fseek($zip, $header['offset']);
	     	
			if(in_array("-1",$index)||in_array($i,$index))
	      		$stat[$header['filename']] = $this->ExtractFile($header, $to, $zip);
	   	}
	   	fclose($zip);
	   	return $stat;
	}
	
	function ReadFileHeader($zip)
	{
		$binary_data = fread($zip, 30);
	    $data = unpack('vchk/vid/vversion/vflag/vcompression/vmtime/vmdate/Vcrc/Vcompressed_size/Vsize/vfilename_len/vextra_len', $binary_data);
		
	    $header['filename'] = fread($zip, $data['filename_len']);
	    
		if ($data['extra_len'] != 0)
	      	$header['extra'] = fread($zip, $data['extra_len']);
	    else
			$header['extra'] = '';
		
	    $header['compression'] = $data['compression'];$header['size'] = $data['size'];
	    $header['compressed_size'] = $data['compressed_size'];
	    $header['crc'] = $data['crc']; $header['flag'] = $data['flag'];
	    $header['mdate'] = $data['mdate'];$header['mtime'] = $data['mtime'];
		
	    if ($header['mdate'] && $header['mtime'])
		{
	     	$hour=($header['mtime']&0xF800)>>11;$minute=($header['mtime']&0x07E0)>>5;
	     	$seconde=($header['mtime']&0x001F)*2;$year=(($header['mdate']&0xFE00)>>9)+1980;
	     	$month=($header['mdate']&0x01E0)>>5;$day=$header['mdate']&0x001F;
	     	$header['mtime'] = mktime($hour, $minute, $seconde, $month, $day, $year);
	    }
		else
			$header['mtime'] = time();
		
	    $header['stored_filename'] = $header['filename'];
	    $header['status'] = "ok";
	    return $header;
	}
	
	function ExtractFile($header,$to,$zip)
	{
		$header = $this->readfileheader($zip);
		
	   	if(substr($to,-1)!="/")
			$to.="/";
	   	if(substr($header['filename'],-1)=="/")
	   	{
	    	@mkdir($to.$header['filename'], 0777);
			
			// Store the old umask and set a new one.
			$oldPermissionSetting = @umask(0);
			//Change the permission setting allowing other users can delete this folder
	        @chmod($to.$header['filename'], 0777);
	        // Restore the old umask.
	        @umask($oldPermissionSetting);
			
	    	return +2;
	   	}
		
	   	$pth = explode("/",dirname($header['filename']));
	   	for($i=0,$tmp=""; isset($pth[$i]); $i++)
		{
	     	if(!$pth[$i])
				continue;
	     	if(!is_dir($to.$tmp.$pth[$i]))
				@mkdir($to.$tmp.$pth[$i],0777);
			
			// Store the old umask and set a new one.
			$oldPermissionSetting = @umask(0);
			//Change the permission setting allowing other users can delete this folder
	        @chmod($to.$tmp.$pth[0], 0777);
	        // Restore the old umask.
	        @umask($oldPermissionSetting);
			
	     	$tmp.=$pth[$i]."/";
	   	}
	  	
		if (!($header['external']==0x41FF0010) && !($header['external']==16))
	  	{
	   		if ($header['compression']==0)
	   		{
	    		$fp = @fopen($to.$header['filename'], 'wb');
	    		if(!$fp)
					return(-1);
				else
				{
					// Store the old umask and set a new one.
					$oldPermissionSetting = @umask(0);
					//Change the permission setting allowing other users can delete this file
	        		@chmod($to.$header['filename'], 0777);
	        		// Restore the old umask.
				    @umask($oldPermissionSetting);
				}
				
	    		$size = $header['compressed_size'];
				
	    		while ($size != 0)
	    		{
				     $read_size = ($size < 2048 ? $size : 2048);
				     $buffer = fread($zip, $read_size);
				     $binary_data = pack('a'.$read_size, $buffer);
				     @fwrite($fp, $binary_data, $read_size);
				     $size -= $read_size;
	    		}
	   	 		fclose($fp);
	    		touch($to.$header['filename'], $header['mtime']);
			}
			else
			{
	   			$fp = @fopen($to.$header['filename'].'.gz','wb');
	   			if(!$fp)
					return(-1);
				
	   			$binary_data = pack('va1a1Va1a1', 0x8b1f, Chr($header['compression']),
	     		Chr(0x00), time(), Chr(0x00), Chr(3));
				
	   			fwrite($fp, $binary_data, 10);
	   			$size = $header['compressed_size'];
				
	   			while ($size != 0)
	   			{
	     			$read_size = ($size < 1024 ? $size : 1024);
	     			$buffer = fread($zip, $read_size);
	     			$binary_data = pack('a'.$read_size, $buffer);
	     			@fwrite($fp, $binary_data, $read_size);
	     			$size -= $read_size;
	   			}
				
	   			$binary_data = pack('VV', $header['crc'], $header['size']);
	   			fwrite($fp, $binary_data,8);
				fclose($fp);
				
	   			$gzp = @gzopen($to.$header['filename'].'.gz','rb');
	    		if(!$gzp)
				{
	      			@gzclose($gzp);
					@unlink($to.$header['filename']);
	       			die("Archive is compressed whereas ZLIB is not enabled.");
	    		}
	   			
				$fp = @fopen($to.$header['filename'],'wb');
	   			if(!$fp)
					return(-1);
				else
				{
					// Store the old umask and set a new one.
					$oldPermissionSetting = @umask(0);
					//Change the permission setting allowing other users can delete this file
	        		@chmod($to.$header['filename'], 0777);
	        		// Restore the old umask.
				    @umask($oldPermissionSetting);
				}
				
	   			$size = $header['size'];
				
	   			while ($size != 0)
	   			{
	     			$read_size = ($size < 2048 ? $size : 2048);
	     			$buffer = gzread($gzp, $read_size);
	     			$binary_data = pack('a'.$read_size, $buffer);
	     			@fwrite($fp, $binary_data, $read_size);
	     			$size -= $read_size;
	   			}
	   			fclose($fp);
				gzclose($gzp);
				
	   			touch($to.$header['filename'], $header['mtime']);
	   			@unlink($to.$header['filename'].'.gz');
			}
		}
	  	return true;
	}
	
	function GetSize() {
		if (!$this->is_closed) {
			return false;
		}
		return strlen($this->zipFile);
	}
	
	function GetFile() {
		return $this->zipFile;
    }
	
	function SendFile($file_name)
	{
		$file_name = str_replace(array('"', '<', '>'), '', $file_name);

		header('Content-Type: application/octet-stream');
		header('Content-transfer-encoding: binary'); 
		header('Content-Disposition: attachment; filename="'.escapeshellcmd(htmlspecialchars($file_name)).'.zip"');
		header('Expires: 0');
		header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		header('Pragma: public');
	//	header('Content-Length: '.$this->GetSize());

		echo $this->file();

		exit;
	}
}
?>