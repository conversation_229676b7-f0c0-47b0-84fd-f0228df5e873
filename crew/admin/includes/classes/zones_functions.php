<?php
/*
		$Id: zones_functions.php,v 1.10 2014/08/19 11:19:59 weesiong Exp $
	
	osCommerce, Open Source E-Commerce Solutions
	http://www.oscommerce.com
	
	Copyright (c) 2003 osCommerce
	
	Released under the GNU General Public License
*/
////
// Class to handle currencies
// TABLES: cms_menu
class zones_functions {
	private $sort_cmp_key = '';
	
	// class constructor
	function zones_functions() {
		;
	}
	
	// class methods
	function save($zone_type, $geo_zone_id) {
        global $messageStack, $memcache_obj;

		$configuration_obj = array();
		$cache_key = TABLE_ZONES_INFO . '/geo_zone_id/' . $geo_zone_id;
    	$memcache_obj->delete($cache_key, 0);
    	
		$json = new Services_JSON();
    	
		switch($zone_type) {
			case 1:
				$zone_category_sorted = array();
				
                if ($_POST['zone_sort_type'] == 'define' && isset($_POST['zone_category_sort']) && isset($_POST['zone_categories_id'])) {
                    $zone_category_sorted = $_POST['zone_categories_id']; //array_intersect($_POST['zone_category_sort'], $_POST['zone_categories_id']);
                    //$zone_category_sorted = array_intersect($_POST['zone_category_sort'], $_POST['zone_categories_id']);
				}
				
				$configuration_obj = array ('zone_categories_id' => $_POST['zone_categories_id'],
											'zone_sort_type' => $_POST['zone_sort_type'],
											'zone_sort_order' => $zone_category_sorted
											);
				break;
			case 2:
				$configuration_obj = array ('zone_languages_id' => $_POST['zone_languages_id'],
											'zone_default_languages_id' => $_POST['default_languages_id']
											);
				break;
			case 3:
				$configuration_obj = array ('zone_currency_id' => $_POST['zone_currency_id'],
											'zone_default_currency_id' => $_POST['default_currency_id']
											);
				break;
			case 4:
				$configuration_obj = array ('zone_payment_gateway_id' => $_POST['zone_payment_gateway_id'],
											'zone_payment_gateway_nrp' => $_POST['zone_payment_gateway_nrp']
											);
				break;
			case 5:
				$banner_content = array();
				$banner_country = array();
				$footer_array = array();
				$slider_content = array();
				$slider_country = array();
				$supported_pg_content = array();
                $toll_free_content = array();
                
				$languages = tep_get_languages();
				
				// Cannot delete because tab_content is populated via cronjob.
				$countries_content_del_sql = "UPDATE " . TABLE_COUNTRIES_CONTENT . " SET footer_all_payment_image = '', toll_free = '', supported_pg = ''  WHERE geo_zone_id = '" . tep_db_input($geo_zone_id) . "'";
				tep_db_query($countries_content_del_sql);
				
				$countries_content_desc_del_sql = "DELETE FROM " . TABLE_COUNTRIES_CONTENT_DESCRIPTION . " WHERE geo_zone_id = '" . tep_db_input($geo_zone_id) . "'";
				tep_db_query($countries_content_desc_del_sql);
				
				# =============================   PAYMENT   =============================
				$footer_pg_content = tep_db_prepare_input($_POST['footer_pg_image']);
				$footer_pg_country = tep_db_prepare_input($_POST['footer_pg_country']);
				
				if (tep_not_empty($footer_pg_country)) {
					foreach ($footer_pg_country as $num => $countries_array) {
						for ($i=0, $cnt=count($countries_array); $cnt > $i; $i++) {
							$countries_id = $countries_array[$i];
							$footer_array[$countries_id][] = $footer_pg_content[$num];
						}
					}
				}
				
				if (tep_not_empty($footer_array)) {
					foreach ($footer_array as $countries_id => $val) {
						$footer_data_array = tep_db_prepare_input(array('countries_id' => (int)$countries_id,
																		'geo_zone_id' => $geo_zone_id, 
																		'footer_all_payment_image' => serialize($val)));
						$footer_sel_sql = "	SELECT countries_id 
											FROM " . TABLE_COUNTRIES_CONTENT . " 
											WHERE countries_id = '" . (int)$countries_id . "' 
												AND geo_zone_id = '" . tep_db_input($geo_zone_id) . "'";
						$footer_res_sql = tep_db_query($footer_sel_sql);
						if (tep_db_num_rows($footer_res_sql) > 0) {
							tep_db_perform(TABLE_COUNTRIES_CONTENT, $footer_data_array, 'update', " countries_id = '" . (int)$countries_id . "' AND geo_zone_id = '" . $geo_zone_id . "' ");
						} else {
							tep_db_perform(TABLE_COUNTRIES_CONTENT, $footer_data_array);
						}
					}
				}
				# =============================   PAYMENT   =============================
                
                
                # =============================   Toll Free   =============================
                $toll_free_content_raw = tep_db_prepare_input($_POST['toll_free']);
                
				if (tep_not_empty($toll_free_content_raw)) {
                    $toll_free_country = array();
                    
					foreach ($toll_free_content_raw['country_idx'] as $idx => $country_idx) {
                        if ($toll_free_content_raw['num'][$idx]) {
                            $toll_free_content[] = array(
                                'num' => $toll_free_content_raw['num'][$idx],
                                'country' => $toll_free_content_raw['country'][$country_idx],
                            );
                        }
					}
                    
                    foreach ($toll_free_content as $idx => $toll_free_array) {
                        foreach ($toll_free_array['country'] as $country_id) {
                            $toll_free_country[$country_id][] = $toll_free_array['num'];
                        }
                    }
                    
					foreach ($toll_free_country as $countries_id => $val) {
						$update_data_array = array(
                            'countries_id' => (int)$countries_id, 
                            'geo_zone_id' => $geo_zone_id, 
                            'toll_free' => array_shift($val)
                        );
                        
						$check_sel_sql = "	SELECT countries_id 
											FROM " . TABLE_COUNTRIES_CONTENT . " 
											WHERE countries_id = '" . (int)$countries_id . "' 
												AND geo_zone_id = '" . tep_db_input($geo_zone_id) . "'";
						$check_res_sql = tep_db_query($check_sel_sql);
						if (tep_db_num_rows($check_res_sql) > 0) {
							tep_db_perform(TABLE_COUNTRIES_CONTENT, $update_data_array, 'update', " countries_id = '" . (int)$countries_id . "' AND geo_zone_id = '" . $geo_zone_id . "' ");
						} else {
							tep_db_perform(TABLE_COUNTRIES_CONTENT, $update_data_array);
						}
					}
				}
                # =============================   Toll Free   =============================
                
                
                # =============================   Supported PG   =============================
                $supported_pg_content_raw = tep_db_prepare_input($_POST['supported_pg']);
                
				if (tep_not_empty($supported_pg_content_raw)) {
                    $supported_pg_country = array();
                    
					foreach ($supported_pg_content_raw['country_idx'] as $idx => $country_idx) {
                        if ($supported_pg_content_raw['image_url'][$idx]) {
                            $supported_pg_content[] = array(
                                'image_url' => $supported_pg_content_raw['image_url'][$idx],
                                'image_link' => $supported_pg_content_raw['image_link'][$idx],
                                'sort_order' => $supported_pg_content_raw['sort_order'][$idx],
                                'country' => $supported_pg_content_raw['country'][$country_idx],
                            );
                        }
					}
                    
                    $this->sort_cmp_key = 'sort_order';
                    uasort($supported_pg_content, array($this, 'sort_cmp'));
                    
                    foreach ($supported_pg_content as $idx => $pg_array) {
                        if ($pg_array['country']) {
                            foreach ($pg_array['country'] as $country_id) {
                                $supported_pg_country[$country_id][] = array(
                                    'image_url' => $pg_array['image_url'],
                                    'image_link' => $pg_array['image_link']
                                );
                            }
                        }
                    }
                    
					foreach ($supported_pg_country as $countries_id => $val) {
						$update_data_array = array(
                            'countries_id' => (int)$countries_id, 
                            'geo_zone_id' => $geo_zone_id, 
                            'supported_pg' => json_encode($val)
                        );
                        
						$check_sel_sql = "	SELECT countries_id 
											FROM " . TABLE_COUNTRIES_CONTENT . " 
											WHERE countries_id = '" . (int)$countries_id . "' 
												AND geo_zone_id = '" . tep_db_input($geo_zone_id) . "'";
						$check_res_sql = tep_db_query($check_sel_sql);
						if (tep_db_num_rows($check_res_sql) > 0) {
							tep_db_perform(TABLE_COUNTRIES_CONTENT, $update_data_array, 'update', " countries_id = '" . (int)$countries_id . "' AND geo_zone_id = '" . $geo_zone_id . "' ");
						} else {
							tep_db_perform(TABLE_COUNTRIES_CONTENT, $update_data_array);
						}
					}
				}
                # =============================   Supported PG   =============================

                $s3_slider_info = [];

				for ($i=0, $cnt=count($languages); $cnt > $i; $i++) {
					$banner_array = array();
					$slider_array = array();
					
					$lang_id = (int)$languages[$i]['id'];
					$lang_code = $languages[$i]['code'];
					
					# BANNER
					if (tep_not_empty($_POST['banner_image'][$lang_code])) {
						$banner_content[$lang_code] = tep_db_prepare_input($_POST['banner_image'][$lang_code]);
					}
					
					if (tep_not_empty($_POST['banner_country'][$lang_code])) {
						$banner_country[$lang_code] = $_POST['banner_country'][$lang_code];
						
						if (tep_not_empty($banner_country[$lang_code])) {
							foreach ($banner_country[$lang_code] as $num => $countries_array) {
								for ($j=0, $jcnt=count($countries_array); $jcnt > $j; $j++) {
									$countries_id = $countries_array[$j];
									$banner_array[$countries_id][] = $banner_content[$lang_code][$num];
								}
							}
						}
						$banner_country[$lang_code] = tep_db_prepare_input(serialize($banner_country[$lang_code]));
					}
					
					# SLIDER
					if (tep_not_empty($_POST['slider_background_image'][$lang_code])) {
						$array = array();
						
						$slider_bg = tep_db_prepare_input($_POST['slider_background_image'][$lang_code]);
						
						foreach ($slider_bg as $key => $val) {
                            $array[] = array (	'slider_background_image' => $val,
												'slider_thumbnail_image' => tep_db_prepare_input($_POST['slider_thumbnail_image'][$lang_code][$key]), 
												'slider_image_url' => tep_db_prepare_input($_POST['slider_image_url'][$lang_code][$key]),
                                                'slider_caption' => tep_db_prepare_input(convert_ascii_to_entities($_POST['slider_caption'][$lang_code][$key])),
												'slider_sort_order' => (int)$_POST['slider_sort_order'][$lang_code][$key],
												'key' => $key );
						}
						
						$this->sort_cmp_key = 'slider_sort_order';
						uasort($array, array($this, 'sort_cmp'));
						
						foreach ($array as $num => $key) {
							$slider_content[$lang_code][] = array ( 'slider_background_image' => $array[$num]['slider_background_image'], 
																	'slider_thumbnail_image' => $array[$num]['slider_thumbnail_image'], 
																	'slider_image_url' => $array[$num]['slider_image_url'],
																	'slider_caption' => $array[$num]['slider_caption'],
																	'slider_sort_order' => $array[$num]['slider_sort_order'] );
							$slider_country[$lang_code][] = $_POST['slider_country'][$lang_code][$key['key']];
						}
					}
					
					if (tep_not_empty($slider_country[$lang_code])) {
						foreach ($slider_country[$lang_code] as $num => $countries_array) {
							for ($j=0, $jcnt=count($countries_array); $jcnt > $j; $j++) {
								$countries_id = $countries_array[$j];
								$slider_array[$countries_id][] = tep_not_empty($slider_content[$lang_code][$num]) ? $slider_content[$lang_code][$num] : '';
							}
						}
						$slider_country[$lang_code] = tep_db_prepare_input(serialize($slider_country[$lang_code]));
					}
					
					// update `countries_content_description`
					if (tep_not_empty($banner_array)) {
						foreach ($banner_array as $countries_id => $val) {
							$banner_data_array = array(	'countries_id' => (int)$countries_id,
														'language_id' => $lang_id,
														'geo_zone_id' => $geo_zone_id, 
														'banner_content' => serialize($val) );
							$banner_sel_sql = "	SELECT countries_id 
												FROM " . TABLE_COUNTRIES_CONTENT_DESCRIPTION . " 
												WHERE countries_id = '" . (int)$countries_id . "' 
													AND language_id = '" . $lang_id . "'";
							$banner_res_sql = tep_db_query($banner_sel_sql);
							if (tep_db_num_rows($banner_res_sql) > 0) {
								tep_db_perform(TABLE_COUNTRIES_CONTENT_DESCRIPTION, $banner_data_array, 'update', " countries_id = '" . (int)$countries_id . "' AND language_id = '" . $lang_id . "' ");
							} else {
								tep_db_perform(TABLE_COUNTRIES_CONTENT_DESCRIPTION, $banner_data_array);
							}
						}
					}

					if (tep_not_empty($slider_array)) {

                        $language_code_s3 = $lang_code;
                        if ($lang_id == 2) {
                            $language_code_s3 = 'cn';
                        }

						foreach ($slider_array as $countries_id => $val) {
							$slider_data_array = array(	'countries_id' => (int)$countries_id,
														'language_id' => $lang_id, 
														'geo_zone_id' => $geo_zone_id, 
														'slider_content' => serialize($val) );
							$slider_sel_sql = "	SELECT countries_id 
												FROM " . TABLE_COUNTRIES_CONTENT_DESCRIPTION . " 
												WHERE countries_id = '" . (int)$countries_id . "' 
													AND language_id = '" . $lang_id . "'";
							$slider_res_sql = tep_db_query($slider_sel_sql);
							if (tep_db_num_rows($slider_res_sql) > 0) {
								tep_db_perform(TABLE_COUNTRIES_CONTENT_DESCRIPTION, $slider_data_array, 'update', " countries_id = '" . (int)$countries_id . "' AND language_id = '" . $lang_id . "' ");
							} else {
								tep_db_perform(TABLE_COUNTRIES_CONTENT_DESCRIPTION, $slider_data_array);
							}

                            if ($lang_code == 'zh-TW') {
                                continue;
                            }

                            foreach ($val as $index => $data) {
                                if (isset($data['slider_background_image'])) {
                                    $s3_slider_info[$countries_id][$language_code_s3][$index]['src'] = $data['slider_background_image'];
                                }

                                if (isset($data['slider_caption'])) {
                                    $s3_slider_info[$countries_id][$language_code_s3][$index]['title'] = html_entity_decode($data['slider_caption']);
                                }

                            }
						}
					}
				}

                // update `zones_info`
                $configuration_obj = array (
                    'banner_image' => $banner_content,
                    'banner_country' => $banner_country,
                    'footer_all_payment_image' => $footer_pg_content,
                    'footer_all_payment_country' => $footer_pg_country,
                    'slider_content' => serialize($slider_content),
                    'slider_country' => $slider_country,
                    'toll_free' => $toll_free_content != array() ? array_values($toll_free_content) : '',
                    'supported_pg_content' => $supported_pg_content != array() ? array_values($supported_pg_content) : '',
                );

                                # temporary comment this feature due to misused by some party and accidentally update other info (2016-09-06)
				# CLONE `zone_info`
//				if (tep_not_empty($_POST['clone_geo_zone_id'])) {
//					$geo_zone_id_list = explode(',', $_POST['clone_geo_zone_id']);
//					
//					# ZONE COUNTRY
//					for ($l=0, $lcnt=count($geo_zone_id_list); $lcnt > $l; $l++) {
//						$clone_zone_id = (int)$geo_zone_id_list[$l];
//						
//						$countries = array();
//						$clone_banner_content = array();
//						$clone_banner_country = array();
//						$clone_footer_content = array();
//						$clone_slider_content = array();
//						$clone_slider_country = array();
//						
//						$zone_country_sel_sql = "	SELECT zone_country_id 
//													FROM " . TABLE_ZONES_TO_GEO_ZONES . " 
//													WHERE geo_zone_id = '" . $clone_zone_id . "'";
//						$zone_country_res_sql = tep_db_query($zone_country_sel_sql);
//						while ($zone_country_row = tep_db_fetch_array($zone_country_res_sql)) {
//							$countries_id = $zone_country_row['zone_country_id'];
//							
//							$countries[] = $countries_id;
//							$clone_footer_content[$countries_id] = $footer_pg_content;
//							
//							for ($i=0, $cnt=count($languages); $cnt > $i; $i++) {
//								$lang_id = (int)$languages[$i]['id'];
//								$lang_code = $languages[$i]['code'];
//								
//								if (tep_not_empty($banner_content[$lang_code])) {
//									$clone_banner_content[$lang_id][$countries_id] = $banner_content[$lang_code];
//									foreach ($banner_content[$lang_code] as $num => $val) {
//										if (tep_not_empty($val)) {
//											$clone_banner_country[$lang_code][$num][] = $countries_id;
//										}
//									}
//								}
//								
//								if (tep_not_empty($slider_content[$lang_code])) {
//									$clone_slider_content[$lang_id][$countries_id] = $slider_content[$lang_code];
//									foreach ($slider_content[$lang_code] as $num => $val) {
//										if (tep_not_empty($val)) {
//											$clone_slider_country[$lang_code][$num][] = $countries_id;
//										}
//									}
//								}
//							}
//						}
//						
//						if (tep_not_empty($countries)) {
//							$countries_content_del_sql = "UPDATE " . TABLE_COUNTRIES_CONTENT . " SET footer_all_payment_image = '', toll_free = '', supported_pg = '' WHERE geo_zone_id = '" . $clone_zone_id . "'";
//							tep_db_query($countries_content_del_sql);
//							
//							$countries_content_desc_del_sql = "DELETE FROM " . TABLE_COUNTRIES_CONTENT_DESCRIPTION . " WHERE geo_zone_id = '" . $clone_zone_id . "'";
//							tep_db_query($countries_content_desc_del_sql);
//							
//							# PAYMENT
//							if (tep_not_empty($clone_footer_content)) {
//								foreach ($clone_footer_content as $countries_id => $val) {
//									$footer_data_array = array();
//									$footer_data_array = tep_db_prepare_input(array('countries_id' => $countries_id, 
//																					'geo_zone_id' => $clone_zone_id, 
//																					'footer_all_payment_image' => serialize($val)));
//									$footer_sel_sql = "	SELECT countries_id 
//														FROM " . TABLE_COUNTRIES_CONTENT . " 
//														WHERE countries_id = '" . $countries_id . "' 
//															AND geo_zone_id = '" . tep_db_input($clone_zone_id) . "'";
//									$footer_res_sql = tep_db_query($footer_sel_sql);
//									if (tep_db_num_rows($footer_res_sql) > 0) {
//										tep_db_perform(TABLE_COUNTRIES_CONTENT, $footer_data_array, 'update', " countries_id = '" . $countries_id . "' AND geo_zone_id = '" . $clone_zone_id . "' ");
//									} else {
//										tep_db_perform(TABLE_COUNTRIES_CONTENT, $footer_data_array);
//									}
//								}
//							}
//							
//                            if ($supported_pg_content) {
//                                $supported_pg_country = array();
//                                
//                                foreach ($supported_pg_content as $idx => &$pg_array) {
//                                    $pg_array['country'] = $countries;
//
//                                    $supported_pg_country[] = array(
//                                        'image_url' => $pg_array['image_url'],
//                                        'image_link' => $pg_array['image_link']
//                                    );
//                                }
//                                
//                                foreach ($countries as $countries_id) {
//                                    $update_data_array = array(
//                                        'countries_id' => (int)$countries_id, 
//                                        'geo_zone_id' => $clone_zone_id, 
//                                        'supported_pg' => json_encode($supported_pg_country)
//                                    );
//
//                                    $check_sel_sql = "	SELECT countries_id 
//                                                        FROM " . TABLE_COUNTRIES_CONTENT . " 
//                                                        WHERE countries_id = '" . (int)$countries_id . "' 
//                                                            AND geo_zone_id = '" . tep_db_input($clone_zone_id) . "'";
//                                    $check_res_sql = tep_db_query($check_sel_sql);
//                                    if (tep_db_num_rows($check_res_sql) > 0) {
//                                        tep_db_perform(TABLE_COUNTRIES_CONTENT, $update_data_array, 'update', " countries_id = '" . (int)$countries_id . "' AND geo_zone_id = '" . $clone_zone_id . "' ");
//                                    } else {
//                                        tep_db_perform(TABLE_COUNTRIES_CONTENT, $update_data_array);
//                                    }
//                                }
//                            }
//                            
//							for ($i=0, $cnt=count($languages); $cnt > $i; $i++) {
//								$lang_id = (int)$languages[$i]['id'];
//								$lang_code = $languages[$i]['code'];
//								
//								if (tep_not_empty($clone_banner_country[$lang_code])) {
//									$clone_banner_country[$lang_code] = serialize($clone_banner_country[$lang_code]);
//								}
//								
//								if (tep_not_empty($clone_slider_country[$lang_code])) {
//									$clone_slider_country[$lang_code] = serialize($clone_slider_country[$lang_code]);
//								}
//								
//								# BANNER
//								if (tep_not_empty($clone_banner_content[$lang_id])) {
//									foreach ($clone_banner_content[$lang_id] as $countries_id => $val) {
//										foreach ($val as $kval => $aval) {
//											if (!tep_not_empty($aval)) {
//												unset($val[$kval]);
//											}
//										}
//										
//										if (tep_not_empty($val)) {
//											$banner_data_array = array();
//											$banner_data_array = array(	'countries_id' => $countries_id, 
//																		'language_id' => $lang_id, 
//																		'geo_zone_id' => $clone_zone_id, 
//																		'banner_content' => serialize($val) );
//											$banner_sel_sql = "	SELECT countries_id 
//																FROM " . TABLE_COUNTRIES_CONTENT_DESCRIPTION . " 
//																WHERE countries_id = '" . $countries_id . "' 
//																	AND language_id = '" . $lang_id . "'";
//											$banner_res_sql = tep_db_query($banner_sel_sql);
//											if (tep_db_num_rows($banner_res_sql) > 0) {
//												tep_db_perform(TABLE_COUNTRIES_CONTENT_DESCRIPTION, $banner_data_array, 'update', " countries_id = '" . $countries_id . "' AND language_id = '" . $lang_id . "' ");
//											} else {
//												tep_db_perform(TABLE_COUNTRIES_CONTENT_DESCRIPTION, $banner_data_array);
//											}
//										}
//									}
//								}
//								
//								# SLIDER
//								if (tep_not_empty($clone_slider_content[$lang_id])) {
//									foreach ($clone_slider_content[$lang_id] as $countries_id => $val) {
//										if (tep_not_empty($val)) {
//											$slider_data_array = array();
//											$slider_data_array = array(	'countries_id' => $countries_id, 
//																		'language_id' => $lang_id, 
//																		'geo_zone_id' => $clone_zone_id, 
//																		'slider_content' => serialize($val) );
//											$slider_sel_sql = "	SELECT countries_id 
//																FROM " . TABLE_COUNTRIES_CONTENT_DESCRIPTION . " 
//																WHERE countries_id = '" . $countries_id . "' 
//																	AND language_id = '" . $lang_id . "'";
//											$slider_res_sql = tep_db_query($slider_sel_sql);
//											if (tep_db_num_rows($slider_res_sql) > 0) {
//												tep_db_perform(TABLE_COUNTRIES_CONTENT_DESCRIPTION, $slider_data_array, 'update', " countries_id = '" . $countries_id . "' AND language_id = '" . $lang_id . "' ");
//											} else {
//												tep_db_perform(TABLE_COUNTRIES_CONTENT_DESCRIPTION, $slider_data_array);
//											}
//										}
//									}
//								}
//							}
//							
//							// update clone `zones_info`
//							$config_obj = array (	'banner_image' => $banner_content, 
//													'banner_country' => $clone_banner_country, 
//													'footer_all_payment_image' => $footer_pg_content, 
//													'footer_all_payment_country' => array ( $countries ), 
//													'slider_content' => serialize($slider_content), 
//													'slider_country' => $clone_slider_country,
//                                                    'supported_pg_content' => $supported_pg_content != array() ? array_values($supported_pg_content) : '',
//                                    );
//							$config_json = $json->encode($config_obj);
//							
//							$clone_zone_sel_sql = "	SELECT geo_zone_id 
//													FROM " . TABLE_ZONES_INFO . "
//													WHERE geo_zone_id = '" . tep_db_input($clone_zone_id). "'";
//							$clone_zone_res_sql = tep_db_query($clone_zone_sel_sql);
//							
//							$db_obj = array (	'geo_zone_id' => $clone_zone_id,
//												'geo_zone_info' => $config_json );
//							
//							if ($clone_zone_row = tep_db_fetch_array($clone_zone_res_sql)) {
//								tep_db_perform(TABLE_ZONES_INFO, $db_obj, 'update', "geo_zone_id='" . tep_db_input($clone_zone_id) . "'");
//							} else {
//								tep_db_perform(TABLE_ZONES_INFO, $db_obj);
//							}
//						}
//					}
//				}

                $this->send_slider_info_to_s3($s3_slider_info);

				break;
		}
		
		$configuration_json = $json->encode($configuration_obj);
		
		$zones_info_select_sql = "	SELECT geo_zone_id , geo_zone_info
									FROM " . TABLE_ZONES_INFO . "
									WHERE geo_zone_id = '" . tep_db_input($geo_zone_id). "'";
		$zones_info_result = tep_db_query($zones_info_select_sql);
		$zones_info_row = tep_db_fetch_array($zones_info_result);

		// Send slack notifications when updating Games & Product categories
        if ($zone_type == '1') {
            $this->tep_send_slack_notification($zone_type, $geo_zone_id, json_decode($zones_info_row['geo_zone_info'])->zone_categories_id);
        }

       if ($zone_type == '5') {
			$database_obj = array(	'geo_zone_id' => $geo_zone_id,
									'geo_zone_info' => $configuration_json );
		} else {
			$database_obj = array(	'geo_zone_id' => tep_db_prepare_input($geo_zone_id),
									'geo_zone_info' => tep_db_prepare_input($configuration_json));
		}
		
		if ($zones_info_row['geo_zone_id']) {
			tep_db_perform(TABLE_ZONES_INFO, $database_obj, 'update', "geo_zone_id='".tep_db_input($geo_zone_id)."'");
			
			$messageStack->add_session(ZONE_INFO_SUCCESSFUL_MESSAGE, 'success');
		} else {
			tep_db_perform(TABLE_ZONES_INFO, $database_obj);
			
			$messageStack->add_session(ZONE_INFO_SUCCESSFUL_MESSAGE, 'success');
		}
	}

    /**
     * Send Slack Notification
     * @param $zone_type
     * @param $geo_zone_id
     * @param $zone_categories_ids
     */
	function tep_send_slack_notification($zone_type, $geo_zone_id, $zone_categories_ids ) {

	    // Send slack notifications when updating Games & Product categories
	    if($zone_type == 1) {
	        $zone_type_name = $this->tep_get_zone_type_name($zone_type);
            $geo_zone_name = $this->tep_get_geo_zone_name($zone_type, $geo_zone_id);

            $new_category_ids = array_diff($_POST['zone_categories_id'], $zone_categories_ids);
            $old_category_ids = array_diff($zone_categories_ids, $_POST['zone_categories_id']);
            $category_ids = array_merge($new_category_ids,$old_category_ids);

            // if got new / delete category then only send slack notification
            if(!empty($new_category_ids) || !empty($old_category_ids)) {
                $category_where_statement = " categories_id IN ('" . implode("', '", $category_ids) . "') ";
                $categories_select_sql = "	SELECT categories_id, categories_name 
                                            FROM " . TABLE_CATEGORIES_DESCRIPTION . "
												WHERE " . $category_where_statement . " AND
												language_id = 1";

                $categories_result_sql = tep_db_query($categories_select_sql);

                $attachments = [];
                while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                    $status = (in_array($categories_row['categories_id'], $new_category_ids)) ? "Status Change : Inactive => Active" : "Status Change : Active => Inactive";
                    array_push($attachments, [
                        'color' => 'warning',
                        'text' => "Zone Type : " . $zone_type_name . "\n" .
                            "Zones : " . $geo_zone_name . "\n" .
                            "Category : <" . HTTPS_SERVER . "zones_configuration.php?zone_type=" . $_REQUEST['zone_type'] . "&geo_zone_id=" . $_REQUEST['geo_zone_id'] . "|" . $categories_row['categories_name'] . " (" . $categories_row['categories_id'] . ")" . ">\n" .
                            $status . "\n" .
                            "Change by : " . $_SESSION['login_email_address']
                    ]);
                }

                // if got any fields is updated then only send out the notifications
                if (!empty($attachments)) {
                    require_once(DIR_WS_CLASSES . 'slack_notification.php');
                    foreach (array_chunk($attachments, 100) as $attachments) {
                        $slack = new slack_notification();
                        $data = json_encode(array(
                            'text' => '[OG Crew] Region Category ' . LOG_STATUS_ADJUST . ' - ' . date("F j, Y H:i"),
                            'attachments' => $attachments
                        ));
                        $slack->send(SLACK_WEBHOOK_BDT, $data);
                    }
                }
            }
        }
    }

    /**
     * Get Geo Zone Name
     * @param $zone_type
     * @param $geo_zone_id
     * @return mixed|null
     */
    function tep_get_geo_zone_name ($zone_type, $geo_zone_id) {
        $geo_zones_select_sql = "	SELECT geo_zone_id, geo_zone_name 
									FROM " . TABLE_GEO_ZONES . " 
									WHERE  geo_zone_type = '" . tep_db_input($zone_type) . "' 
									AND geo_zone_id = '" . tep_db_input($geo_zone_id) . "'
									ORDER BY geo_zone_name";
        $geo_zones_result = tep_db_query($geo_zones_select_sql);
        $geo_zones_row = tep_db_fetch_array($geo_zones_result);

        return (isset($geo_zones_row['geo_zone_name'])) ?
            $geo_zones_row['geo_zone_name'] :
            null;
    }

    /**
     * Get Zone Type Name
     * @param $zone_type
     * @return mixed|string
     */
    function tep_get_zone_type_name($zone_type) {
        $zone_type_options[] = array('id' => '', 'text' => SELECT_ZONES_DEFAULT);
        for ($i=1; 6 > $i; $i++) {
            $permission = tep_admin_files_actions(FILENAME_ZONES_INFO, 'ZONE_TYPE_' . $i);
            $zone_type_permission[$i] = $permission;
            if ($permission) {
                $zone_type_options[] = array ('id' => $i, 'text' => constant('LIST_ZONE_TYPE_' . $i));
            }
        }
        return (!empty($zone_type_options) && $zone_type_options[$zone_type]['text'])
            ? $zone_type_options[$zone_type]['text']
            : null;
	}
	
	function sort_cmp($a, $b) {
		$key = $this->sort_cmp_key;
		
		if ($a[$key] == $b[$key]) {
			return 0;
		}
		
		return ($a[$key] < $b[$key]) ? -1 : 1;
	}

    function send_slider_info_to_s3($s3_slider_info)
    {
        if (empty($s3_slider_info)) {
            return;
        }

        $aws_obj = $this->get_aws_object();
        $countries_iso_code = $this->get_countries(array_keys($s3_slider_info));

        // save slider info to s3 for each country
        foreach ($s3_slider_info as $countries_id => $language_slider_info) {

            // populate missing language slider info with en slider info
            if (!isset($language_slider_info['cn'])) {
                $s3_slider_info[$countries_id]['cn'] = $language_slider_info['en'];
            }

            if (!isset($language_slider_info['id'])) {
                $s3_slider_info[$countries_id]['id'] = $language_slider_info['en'];
            }

            foreach ($s3_slider_info[$countries_id] as $language_code => $slider_info) {
                $this->save_to_s3(
                    $aws_obj,
                    $slider_info,
                    strtolower($countries_iso_code[$countries_id]),
                    $language_code);
            }

        }

        unset($countries_iso_code);
        unset($s3_slider_info);
    }

    function get_aws_object()
    {
        include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
        return new ogm_amazon_ws();
    }

    function get_countries($countries_code)
    {
        $countries_select_sql = "SELECT countries_id, countries_iso_code_2
                                        FROM " . TABLE_COUNTRIES . "
                                        WHERE countries_id IN (" . implode(',', $countries_code) . ")";
        $countries_result_sql = tep_db_query($countries_select_sql);

        $countries_iso_code = [];
        while ($countries_row = tep_db_fetch_array($countries_result_sql)) {
            $countries_iso_code[$countries_row['countries_id']] = $countries_row['countries_iso_code_2'];
        }

        return $countries_iso_code;
    }

    function save_to_s3($aws_obj, $slider_content, $countryCode, $languageCode)
    {
        if ($aws_obj->is_aws_s3_enabled()) {

            $file_path = sprintf("data/%s/", $countryCode);
            $file_name = sprintf("slider-%s.json", $languageCode);

            $aws_obj->set_bucket_key('BUCKET_STATIC');
            $aws_obj->set_filepath($file_path);
            $aws_obj->set_acl('ACL_PUBLIC');
            $json_encoded_content = json_encode(
                $slider_content ,
                JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES );
            $aws_obj->set_file_content($json_encoded_content);
            $aws_obj->set_filename($file_name);
            $aws_obj->set_file_content_type(CFMimeTypes::get_mimetype('json'));
            $aws_obj->save_file();
        }
    }
}
?>