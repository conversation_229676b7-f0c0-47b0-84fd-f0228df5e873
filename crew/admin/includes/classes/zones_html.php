<?php
/*
  $Id: zones_html.php,v 1.13 2014/08/11 09:21:15 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

////
// Class to handle currencies
// TABLES: Menu Management

class zones_HTML
{

    // class constructor
    function zones_HTML()
    {
        ;
    }

    function zonesListing()
    {
        global $langID, $language;

        $zone_type_options = array();
        $zone_type_options[] = array('id' => '', 'text' => SELECT_ZONES_DEFAULT);
        $zone_type_permission = array();

        // permission
        for ($i = 1; 6 > $i; $i++) {
            $permission = tep_admin_files_actions(FILENAME_ZONES_INFO, 'ZONE_TYPE_' . $i);
            $zone_type_permission[$i] = $permission;

            if ($permission) {
                $zone_type_options[] = array('id' => $i, 'text' => constant('LIST_ZONE_TYPE_' . $i));
            }
        }

        $json = new Services_JSON();

        if ($_GET['zone_type'] && tep_admin_files_actions(FILENAME_ZONES_INFO, 'ZONE_TYPE_' . $_GET['zone_type'])) {
            $zone_type = $_GET['zone_type'];
        } else {
            $zone_type = '';
        }

        if ($_GET['geo_zone_id'] && $_GET['zone_type']) {
            $geo_zone_id = $_GET['geo_zone_id'];

            // Extract from database
            $zones_info_select_sql = "	SELECT geo_zone_info
                                        FROM " . TABLE_ZONES_INFO . "
                                        WHERE geo_zone_id = '" . tep_db_input($geo_zone_id) . "'";
            $zones_info_result = tep_db_query($zones_info_select_sql);
            $zones_info_row = tep_db_fetch_array($zones_info_result);
            // Decode JSON String to PHP array Object
            $zone_info_obj = $json->decode($zones_info_row['geo_zone_info']);
        }

        // Generate Select Box
        $zones_opt_arr = array();
        $zones_opt_arr[] = array('id' => '', 'text' => SELECT_ZONES_DEFAULT);

        $geo_zones_select_sql = "	SELECT geo_zone_id, geo_zone_name
                                FROM " . TABLE_GEO_ZONES . "
                                WHERE  geo_zone_type = '" . tep_db_input($zone_type) . "'
                                ORDER BY geo_zone_name";
        $geo_zones_result = tep_db_query($geo_zones_select_sql);
        while ($geo_zones_row = tep_db_fetch_array($geo_zones_result)) {
            $zones_opt_arr[] = array('id' => $geo_zones_row['geo_zone_id'], 'text' => $geo_zones_row['geo_zone_name']);
        }

        if ($zone_type_permission[$zone_type]) {
            switch ($zone_type) {
                case '1':
                    $categories_opt_arr = array();

                    $categories_structure_select_sql = "SELECT categories_structures_value
                                                        FROM " . TABLE_CATEGORIES_STRUCTURES . "
                                                        WHERE categories_structures_key = 'games'";
                    $categories_structure_result_sql = tep_db_query($categories_structure_select_sql);
                    $categories_structure_row = tep_db_fetch_array($categories_structure_result_sql);

                    $categories_games_id = explode(',', $categories_structure_row['categories_structures_value']);

                    $category_where_statement = " c.categories_id IN ('" . implode("', '", $categories_games_id) . "') ";

                    $categories_select_sql = "	SELECT c.categories_id, cd.categories_name
                                                FROM " . TABLE_CATEGORIES . " AS c
                                                INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                                    ON (c.categories_id = cd.categories_id AND cd.language_id = '" . (int) $langID . "')
                                                WHERE " . $category_where_statement . "
                                                ORDER BY cd.categories_name, c.sort_order";
                    // TODO, cd.language_id = '".$langID."' <-- make it more flexible
                    $categories_result_sql = tep_db_query($categories_select_sql);
                    while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
                        $categories_opt_arr[] = array('id' => $categories_row['categories_id'], 'text' => $categories_row['categories_name']);
                    }

                    $categories_opt_arr = $categories_opt_arr;
                    break;
                case '2':
                    $languages_select_sql = "	SELECT languages_id, name, code
                                            FROM " . TABLE_LANGUAGES . "
                                            ORDER BY sort_order, name";
                    $languages_result_sql = tep_db_query($languages_select_sql);
                    while ($languages_row = tep_db_fetch_array($languages_result_sql)) {
                        $languages_opt_arr[] = array('id' => $languages_row['code'], 'text' => $languages_row['name']);

                        if ($zone_info_obj->zone_languages_id) {
                            foreach ($zone_info_obj->zone_languages_id as $selected_language) {
                                if ($languages_row['code'] == $selected_language)
                                    $default_languages_opt_arr[] = array('id' => $languages_row['code'], 'text' => $languages_row['name']);
                            }
                        }
                    }

                    break;
                case '3':
                    $default_currency_opt_arr = array();

                    $currency_select_sql = "	SELECT currencies_id, code, title
                                            FROM " . TABLE_CURRENCIES . "
                                            WHERE FIND_IN_SET( 'SELL',currencies_used_for)
                                            ORDER BY title ASC";
                    $currency_result_sql = tep_db_query($currency_select_sql);
                    while ($currency_row = tep_db_fetch_array($currency_result_sql)) {
                        $currency_opt_arr[] = array('id' => $currency_row['code'], 'text' => $currency_row['title']);

                        if ($zone_info_obj->zone_currency_id) {
                            foreach ($zone_info_obj->zone_currency_id as $selected_currency) {
                                if ($currency_row['code'] == $selected_currency)
                                    $default_currency_opt_arr[] = array('id' => $currency_row['code'], 'text' => $currency_row['title']);
                            }
                        }
                    }

                    break;
                case '4':
                    $payment_gateway_opt_arr = array();
                    $payment_gateway_array = array();
                    $payment_methods_array = array();

                    $payment_methods_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_title, pm.payment_methods_parent_id
                                                    FROM " . TABLE_PAYMENT_METHODS . " AS pm
                                                    WHERE pm.payment_methods_receive_status = 1
                                                    ORDER BY pm.payment_methods_title";
                    $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
                    while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                        if ((int) $payment_methods_row['payment_methods_parent_id'] > 0) {
                            $payment_methods_obj = new payment_methods($payment_methods_row['payment_methods_id']);

                            $payment_methods_array[(int) $payment_methods_row['payment_methods_parent_id']][] = array('payment_methods_id' => $payment_methods_row['payment_methods_id'],
                                'payment_methods_title' => $payment_methods_row['payment_methods_title'],
                                'confirm_complete_days' => $payment_methods_obj->payment_method_array->confirm_complete_days
                            );
                        } else {
                            $payment_gateway_array[] = array('payment_methods_id' => $payment_methods_row['payment_methods_id'],
                                'payment_methods_title' => $payment_methods_row['payment_methods_title']
                            );
                        }
                    }

                    foreach ($payment_gateway_array as $payment_gateway) {
                        $payment_gateway_opt_arr[] = array('payment_gateway' => $payment_gateway,
                            'payment_methods' => $payment_methods_array[$payment_gateway['payment_methods_id']]
                        );
                    }

                    unset($payment_gateway_array);
                    unset($payment_methods_array);

                    break;
            }
        }

        ob_start();
        ?>
        <script language="javascript">

        jQuery.noConflict();
        <? if ($zone_type == '1') { // Categories   ?>
                function generate_sort_table() {
                    var zone_categories_id_value = new Array()
                    var table_string = '';
                    var category_name = '';

                    jQuery('#category_sorting li').each(function (a) {
                        if (this.id != 'category_sorting_row_header')
                            jQuery(this).hide();
                    });

                    if (zone_categories_id_value = jQuery('#zone_categories_id').val()) {
                        for (i = 0; i < zone_categories_id_value.length; i++) {
                            jQuery('#zone_categories_row_' + zone_categories_id_value[i]).show();
                        }
                    }
                }

                jQuery(document).ready(function () {
                    generate_sort_table();

                    jQuery('#zone_categories_id').change(function () {
                        generate_sort_table();
                    });

                    <?
                    if ($zone_info_obj->zone_sort_type == 'define') {
                        echo '	jQuery("#category_sorting").slideDown();';
                    }
                    ?>
                    jQuery('#category_sorting_list').sortable();
                });

        <? } else if ($zone_type == '2') { // Language   ?>
                function generate_default_language_list() {
                    jQuery('#zone_default_languages_id').removeOption(/./);

                    jQuery('#zone_languages_id option').each(function (a) {
                        if (this.selected == true) {
                            jQuery('#zone_default_languages_id').addOption(this.value, this.text);
                        }
                    });
                }
        <? } else if ($zone_type == '3') { //Currency   ?>
                function generate_default_currency_list() {
                    var selected_status = false;
                    var current_selected = jQuery('#zone_default_currency_id').find(':selected').val();

                    jQuery('#zone_default_currency_id').removeOption(/./);

                    jQuery('#zone_currency_id option').each(function (a) {
                        if (this.selected == true) {
                            jQuery('#zone_default_currency_id').addOption(this.value, this.text);
                            if (this.value == current_selected) {
                                selected_status = true;
                            }
                        }
                    });

                    if (selected_status) {
                        jQuery('#zone_default_currency_id').val(current_selected);
                    }
                }
        <? } else if ($zone_type == '4') { // Payment Gateway   ?>
                jQuery(document).ready(function () {
                    tree = jQuery('#myTree');
                    jQuery('img.expandImage', tree.get(0)).click(
                            function () {
                                if (this.src.indexOf('spacer') == -1) {
                                    subbranch = jQuery('ul', this.parentNode).eq(0);
                                    if (subbranch.css('display') == 'none') {
                                        subbranch.show();
                                        this.src = 'images/icon-collapse-small.gif';
                                    } else {
                                        subbranch.hide();
                                        this.src = 'images/icon-expand-small.gif';
                                    }
                                }
                            }
                    );
                });
            <?php
        } else if ($zone_type == '5') { // Main Page Content
            ?>
                function add_slider(geo_zone_id, lang_code) {
                    var action = 'add_slider';
                    var cnt = jQuery('#slider_row_' + lang_code).val();

                    jQuery.get("<?= FILENAME_ZONES_HTML_XMLHTTP; ?>?action=" + action + "&cnt=" + cnt + "&geo_zone_id=" + geo_zone_id + "&lang_code=" + lang_code, function (xml) {
                        jQuery(xml).find('response').each(function () {
                            var html = jQuery('result', this).text();
                            jQuery('#slider_content_' + lang_code).append(html);
                            jQuery('#slider_row_' + lang_code).val(jQuery('row', this).text());
                        });
                    });
                }

                function del_slider(lang_code, num) {
                    if (!empty(lang_code) && typeof lang_code != 'undefined' && !empty(num) && typeof num != 'undefined') {
                        jQuery('#slider_content_row_' + lang_code + '_' + num).remove();
                    }
                }

                function banner_copy_func(lang_code, b_row) {
                    var code = jQuery('#banner_copy_' + lang_code).val();

                    if (!empty(code) && typeof code != 'undefined') {
                        var html = jQuery('#banner_content_' + code).html();

                        html = str_replace('banner_image[' + code, 'banner_image[' + lang_code, html);
                        html = str_replace('banner_image_' + code, 'banner_image_' + lang_code, html);
                        html = str_replace('banner_country[' + code, 'banner_country[' + lang_code, html);
                        html = str_replace('banner_country_' + code, 'banner_country_' + lang_code, html);

                        jQuery('#banner_content_' + lang_code).html('');
                        jQuery('#banner_content_' + lang_code).append(html);

                        for (i = 0; b_row > i; i++) {
                            jQuery('#banner_country_' + lang_code + '_' + i).val(jQuery('#banner_country_' + code + '_' + i).val());
                            jQuery('#banner_image_' + lang_code + '_' + i).val(jQuery('#banner_image_' + code + '_' + i).val());
                        }
                    }
                }

                function slider_copy_func(lang_code) {
                    var code = jQuery('#slider_copy_' + lang_code).val();

                    if (!empty(code) && typeof code != 'undefined') {
                        var html = jQuery('#slider_content_' + code).html();

                        html = str_replace('slider_content_row_' + code, 'slider_content_row_' + lang_code, html);
                        html = str_replace('del_slider(\'' + code, 'del_slider(\'' + lang_code, html);

                        html = str_replace('slider_thumbnail_image[' + code, 'slider_thumbnail_image[' + lang_code, html);
                        html = str_replace('slider_thumbnail_image_' + code, 'slider_thumbnail_image_' + lang_code, html);
                        html = str_replace('slider_background_image[' + code, 'slider_background_image[' + lang_code, html);
                        html = str_replace('slider_background_image_' + code, 'slider_background_image_' + lang_code, html);
                        html = str_replace('slider_image_url[' + code, 'slider_image_url[' + lang_code, html);
                        html = str_replace('slider_image_url_' + code, 'slider_image_url_' + lang_code, html);
                        html = str_replace('slider_caption[' + code, 'slider_caption[' + lang_code, html);
                        html = str_replace('slider_caption_' + code, 'slider_caption_' + lang_code, html);
                        html = str_replace('slider_country[' + code, 'slider_country[' + lang_code, html);
                        html = str_replace('slider_country_' + code, 'slider_country_' + lang_code, html);
                        html = str_replace('slider_sort_order[' + code, 'slider_sort_order[' + lang_code, html);
                        html = str_replace('slider_sort_order_' + code, 'slider_sort_order_' + lang_code, html);

                        jQuery('#slider_content_' + lang_code).html('');
                        jQuery('#slider_content_' + lang_code).append(html);

                        var sc_row = jQuery('#slider_row_' + code).val();
                        jQuery('#slider_row_' + lang_code).val(sc_row);

                        for (i = 0; sc_row >= i; i++) {
                            jQuery('#slider_thumbnail_image_' + lang_code + '_' + i).val(jQuery('#slider_thumbnail_image_' + code + '_' + i).val());
                            jQuery('#slider_background_image_' + lang_code + '_' + i).val(jQuery('#slider_background_image_' + code + '_' + i).val());
                            jQuery('#slider_image_url_' + lang_code + '_' + i).val(jQuery('#slider_image_url_' + code + '_' + i).val());
                            jQuery('#slider_caption_' + lang_code + '_' + i).val(jQuery('#slider_caption_' + code + '_' + i).val());
                            jQuery('#slider_country_' + lang_code + '_' + i).val(jQuery('#slider_country_' + code + '_' + i).val());
                            jQuery('#slider_sort_order_' + lang_code + '_' + i).val(jQuery('#slider_sort_order_' + code + '_' + i).val());
                        }
                    }
                }

                function add_row(id, country_class) {
                    var next_row_id = parseInt(jQuery('#' + id + '>tbody>tr:last').attr('id').split('_')[1]) + 1;
                    console.log(next_row_id);

                    jQuery('#' + id + '>tbody').append('<tr id="row_' + next_row_id + '">' + jQuery('#' + id + '>tbody>tr:first').html() + '</tr>');
                    jQuery('#' + id + '>tbody>tr:last').each(function () {

                        jQuery('.del', this).show();
                        jQuery('input', this).val('');
                        jQuery('.' + country_class + '_idx', this).val(next_row_id)
                        jQuery('.' + country_class, this).attr('name', id + '[country][' + next_row_id + '][]');
                        jQuery('.' + country_class + ' option:selected', this).removeAttr("selected");
                    });
                }

                function del_row(obj) {
                    jQuery(obj).closest('table').closest('tr').remove();
                }

                function pre_submit2() {
                    var display_html = 'Are you sure?';
                    jquery_confirm_box(display_html, 2, 0, 'Confirmation');

                    jQuery('#jconfirm_submit').click(function () {
                        document.zones_form.submit();
                    });
                }
                function pre_submit() {
                    var display_html = '';
                    display_html += '<table border="0" cellpadding="0" cellspacing="0">';
                    display_html += '<tr><td>&nbsp;</td></tr>';
                    display_html += '<tr><td class="main"><?= TEXT_PRE_SUBMIT_ALL_COUNTRY; ?></td></tr>';

            <?php
            foreach ($zones_opt_arr as $zone) {
                if (tep_not_empty($zone['id']) && ($zone['id'] != $geo_zone_id)) {
                    ?>
                            var zone_id = "<?= $zone['id']; ?>";
                            var zone_text = "<?= $zone['text']; ?>";

                            display_html += '<tr><td class="main">';
                            display_html += '<input type="checkbox" class="zone_id_checkbox" value="' + zone_id + '" id="zone_id_' + zone_id + '">&nbsp;';
                            display_html += '<label for="zone_id_' + zone_id + '">' + zone_text + '</label>';
                            display_html += '</td></tr>';
                    <?php
                }
            }
            ?>

                    display_html += '<tr><td>&nbsp;</td></tr>';
                    display_html += '</table>';

                    jquery_confirm_box(display_html, 2, 0, '<?= TEXT_PRE_SUBMIT_TITLE; ?>');

                    jQuery('#jconfirm_submit').click(function () {
                        var clone_geo_zone_id = '';

                        jQuery('.zone_id_checkbox').each(function (i) {
                            if (jQuery(this).attr('checked') == true) {
                                clone_geo_zone_id += this.value + ',';
                            }
                        });

                        jQuery('#clone_geo_zone_id').val(clone_geo_zone_id);
                        document.zones_form.submit();
                    });
                }

                jQuery(document).ready(function () {
                    jQuery("#languages_tab > ul").tabs();
                    jQuery('.languages_tab').css({
                        border: '1px solid #C9C9C9'
                    });
                });
        <? } ?>
        //-->
        </script>
        <?= tep_draw_form('zones_form', FILENAME_ZONES_INFO, 'selected_box=localization&action=save&geo_zone_id=' . $geo_zone_id, 'post', ' id="zones_form"') ?>
        <table border="0" width="100%" cellspacing="0" cellpadding="2">
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="pageHeading" valign="top"><?= HEADING_TITLE ?></td>
                            <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="40"></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table border="0" width="100%" cellspacing="0" cellpadding="5">
                        <tr>
                            <td width="100" class="main" nowrap><?= TEXT_INFO_ZONE_TYPE . ': ' ?></td>
                            <td width="100%" class="main" nowrap><?= tep_draw_pull_down_menu('zone_type', $zone_type_options, ($zone_type) ? $zone_type : '', ' id="zone_type" onchange="document.location.href=\'?zone_type=\'+jQuery(\'#zone_type\').val()"') ?></td>
                        </tr>
                        <tr>
                            <td class="main"><?= ENTRY_ZONES . ': '; ?></td>
                            <td><?= tep_draw_pull_down_menu('geo_zone_id', $zones_opt_arr, ($geo_zone_id) ? $geo_zone_id : '', ' id="geo_zone_id" onchange="document.location.href=\'?zone_type=' . $zone_type . '&geo_zone_id=\'+jQuery(\'#geo_zone_id\').val()"') ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <? if ($geo_zone_id && $zone_type && $zone_type_permission[$zone_type]) { ?>
                <tr>
                    <td valign="top" width="100%">
                        <table border="0" width="100%" cellspacing="0" cellpadding="5">
                            <?
                            switch ($zone_type) {
                                case '1': // Categories
                                    $select_default = false;
                                    $select_sales = false;
                                    $select_define = false;

                                    $zone_category_sort = array();

                                    if ($zone_info_obj->zone_sort_type == 'default') {
                                        $select_default = true;
                                    } else if ($zone_info_obj->zone_sort_type == 'sales') {
                                        $select_sales = true;
                                    } else if ($zone_info_obj->zone_sort_type == 'define') {
                                        $select_define = true;
                                    }
                                    ?>
                                    <tr class="reportListingEven">
                                        <td width="140" class="main" valign="top"><?= ENTRY_CATEGORIES ?>:</td>
                                        <td class="reportRecords">
                                            <?php echo tep_draw_pull_down_menu('zone_categories_id[]', $categories_opt_arr, ($zone_info_obj->zone_categories_id) ? $zone_info_obj->zone_categories_id : '', ' id="zone_categories_id" multiple size=10'); ?>
                                        </td>
                                    </tr>
                                    <tr class="reportListingOdd">
                                        <td width="140" class="main" valign="top"><?= ENTRY_CATEGORY_SORTING ?>:</td>
                                        <td class="reportRecords">
                                            <?
                                            echo tep_draw_radio_field('zone_sort_type', 'default', $select_default, '', ' onclick="javascript:jQuery(\'#category_sorting\').slideUp()"') . ' ' . RADIO_VALUE_DEFAULT . '<br>' .
                                            tep_draw_radio_field('zone_sort_type', 'sales', $select_sales, '', ' onclick="javascript:jQuery(\'#category_sorting\').slideUp()"') . ' ' . RADIO_VALUE_SALES . '<br>' .
                                            tep_draw_radio_field('zone_sort_type', 'define', $select_define, '', ' onclick="javascript:jQuery(\'#category_sorting\').slideDown()"') . ' ' . RADIO_VALUE_DEFINE;
                                            ?>
                                            <div id="category_sorting" style="display: none">
                                                <ul id="category_sorting_list" style="cursor:move;list-style-image:none;list-style-position:outside;list-style-type:none;">
                                                    <?
                                                    foreach ($zone_info_obj->zone_sort_order as $zone_sort_order_rows) {
                                                        foreach ($categories_opt_arr as $categories_rows) {
                                                            if ($categories_rows['id'] == $zone_sort_order_rows) {
                                                                $zone_category_sort[] = $categories_rows;
                                                            }
                                                        }
                                                    }

                                                    // Combine new categories to sorted list
                                                    foreach ($categories_opt_arr as $categories_rows) {
                                                        $exist_flag = false;
                                                        foreach ($zone_info_obj->zone_sort_order as $zone_sort_order_row) {
                                                            if ($categories_rows['id'] == $zone_sort_order_row) {
                                                                $exist_flag = true;
                                                                break;
                                                            }
                                                        }

                                                        if ($exist_flag == false) {
                                                            $zone_category_sort[] = $categories_rows;
                                                        }
                                                    }

                                                    foreach ($zone_category_sort as $categories_rows) {
                                                        ?>
                                                        <li style="padding:5px" id="zone_categories_row_<?= $categories_rows['id'] ?>">
                                                            <img src="images/icons/folder.gif" align="absbottom"> <?= $categories_rows['text'] . tep_draw_hidden_field("zone_category_sort[]", $categories_rows['id']) ?>
                                                        </li>
                                                    <? } ?>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    <?
                                    break;
                                case '2': // Language
                                    ?>
                                    <tr class="reportListingEven">
                                        <td width="140" class="main" valign="top"><?= ENTRY_SUPPORTED_LANGUAGES ?>:</td>
                                        <td class="reportRecords">
                                            <?php echo tep_draw_pull_down_menu('zone_languages_id[]', $languages_opt_arr, ($zone_info_obj->zone_languages_id) ? $zone_info_obj->zone_languages_id : '', ' onchange="javascript:generate_default_language_list()" id="zone_languages_id" multiple size="15"'); ?>
                                        </td>
                                    </tr>
                                    <tr class="reportListingOdd">
                                        <td width="140" class="main" valign="top"><?= ENTRY_DEFAULT_LANGUAGES ?>:</td>
                                        <td class="reportRecords">
                                            <?php echo tep_draw_pull_down_menu('default_languages_id', $default_languages_opt_arr, ($zone_info_obj->zone_default_languages_id) ? $zone_info_obj->zone_default_languages_id : '', 'id="zone_default_languages_id"'); ?>
                                        </td>
                                    </tr>
                                    <?
                                    break;
                                case '3': // Currencies
                                    ?>
                                    <tr class="reportListingEven">
                                        <td width="145" class="main" valign="top"><?= ENTRY_SUPPORTED_CURRENCY ?>:</td>
                                        <td class="reportRecords">
                                            <?php echo tep_draw_pull_down_menu('zone_currency_id[]', $currency_opt_arr, ($zone_info_obj->zone_currency_id) ? $zone_info_obj->zone_currency_id : '', ' id="zone_currency_id" multiple size="15" onchange="generate_default_currency_list()"'); ?>
                                        </td>
                                    </tr>
                                    <tr class="reportListingOdd">
                                        <td width="145" class="main" valign="top"><?= ENTRY_DEFAULT_CURRENCY ?>:</td>
                                        <td class="reportRecords">
                                            <?php echo tep_draw_pull_down_menu('default_currency_id', $default_currency_opt_arr, ($zone_info_obj->zone_default_currency_id) ? $zone_info_obj->zone_default_currency_id : '', 'id="zone_default_currency_id"'); ?>
                                        </td>
                                    </tr>
                                    <?
                                    break;
                                case '4': // Payment Gateway
                                    $zones_info_select_sql = "	SELECT geo_zone_info
                                                                FROM " . TABLE_ZONES_INFO . "
                                                                WHERE geo_zone_id = '" . tep_db_input($geo_zone_id) . "'";
                                    $zones_info_result = tep_db_query($zones_info_select_sql);
                                    $zones_info_row = tep_db_fetch_array($zones_info_result);

                                    // Decode JSON String to PHP array Object
                                    $zone_info_obj = $json->decode($zones_info_row['geo_zone_info']);
                                    $zone_info_selected_array = $zone_info_obj->zone_payment_gateway_id;
                                    $zone_info_nrp_array = $zone_info_obj->zone_payment_gateway_nrp;
                                    ?>
                                    <tr class="reportListingEven">
                                        <td width="100%" class="main" valign="top"><?= ENTRY_SUPPORTED_PAYMENT_METHOD ?>:<br>
                                            <ul class="myTree">
                                                <? foreach ($payment_gateway_opt_arr as $payment_gateway) { ?>
                                                    <li style="-moz-user-select: none;" class="treeItem">
                                                        <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                                        <span class="textHolder"><?= $payment_gateway['payment_gateway']['payment_methods_title'] ?></span>
                                                        <?
                                                        if (isset($payment_gateway['payment_methods']) && count($payment_gateway['payment_methods'])) {
                                                            echo '	<ul style="display:none;">';

                                                            foreach ($payment_gateway['payment_methods'] as $payment_methods) {
                                                                echo '	<li style="-moz-user-select: none;" class="treeItem">
											<span class="textHolder">' .
                                                                tep_draw_checkbox_field('zone_payment_gateway_id[]', $payment_methods['payment_methods_id'], (is_array($zone_info_selected_array) && in_array($payment_methods['payment_methods_id'], $zone_info_selected_array) ? true : false)) . '&nbsp;' . $payment_methods['payment_methods_title'] . '&nbsp;';

                                                                if ($payment_methods['confirm_complete_days'] <= 0) {
                                                                    echo '		&nbsp;&nbsp;' . tep_draw_checkbox_field('zone_payment_gateway_nrp[]', $payment_methods['payment_methods_id'], (is_array($zone_info_nrp_array) && in_array($payment_methods['payment_methods_id'], $zone_info_nrp_array) ? true : false)) . '<small><small>' . TEXT_SHOW_TO_NRP_CUSTOMER . '</small></small>';
                                                                }

                                                                echo '		</span>
										</li>';
                                                            }

                                                            echo '	</ul>';
                                                        }
                                                        echo '	</li>';
                                                    }

                                                    echo '	</ul>
						</td>
					</tr>';

                                                    break;

                                                case '5': // Main Page Content
                                                    define('MAX_BANNER_IMAGE', 4);
                                                    define('MAX_ALL_PAYMENT_IMAGE', 1);

                                                    $banner_content = array();
                                                    $banner_country = array();
                                                    $footer_pg_content = array();
                                                    $footer_pg_country = array();
                                                    $slider_content = array();
                                                    $slider_country = array();

                                                    $languages = tep_get_languages();
                                                    $countries = array();
                                                    $countries_select_all = array();

                                                    # ZONE COUNTRY
                                                    $zone_country_sel_sql = "	SELECT c.countries_id, c.countries_name
                                                                            FROM " . TABLE_ZONES_TO_GEO_ZONES . " AS ztgz
                                                                            INNER JOIN " . TABLE_COUNTRIES . " AS c
                                                                                    ON ztgz.zone_country_id = c.countries_id
                                                                            WHERE ztgz.geo_zone_id = '" . tep_db_input($geo_zone_id) . "'";
                                                    $zone_country_res_sql = tep_db_query($zone_country_sel_sql);
                                                    while ($zone_country_row = tep_db_fetch_array($zone_country_res_sql)) {
                                                        $countries[] = array('id' => $zone_country_row['countries_id'],
                                                            'text' => $zone_country_row['countries_name']);
                                                        $countries_select_all[] = $zone_country_row['countries_id'];
                                                    }

                                                    if (tep_not_empty($zones_info_row['geo_zone_info'])) {
                                                        $zone_info_obj = $json->decode($zones_info_row['geo_zone_info'], true);

                                                        # BANNER
                                                        for ($i = 0, $cnt = count($languages); $cnt > $i; $i++) {
                                                            $lang_code = $languages[$i]['code'];

                                                            $banner_content[$lang_code] = $zone_info_obj->banner_image->{$lang_code};
                                                            $banner_country[$lang_code] = unserialize($zone_info_obj->banner_country->{$lang_code});
                                                            $slider_country[$lang_code] = unserialize($zone_info_obj->slider_country->{$lang_code});
                                                        }

                                                        # PAYMENT
                                                        $footer_pg_content = $zone_info_obj->footer_all_payment_image;
                                                        $footer_pg_country = $zone_info_obj->footer_all_payment_country;

                                                        # SLIDER
                                                        $slider_content = unserialize($zone_info_obj->slider_content);

                                                        # Toll Free
                                                        $toll_free = isset($zone_info_obj->toll_free) && !empty($zone_info_obj->toll_free) ? json_decode(json_encode($zone_info_obj->toll_free), true) : array(
                                                            array(
                                                                'num' => '',
                                                                'country' => array(),
                                                            )
                                                        );

                                                        # New supported PG link info
                                                        $supported_pg_content = isset($zone_info_obj->supported_pg_content) && !empty($zone_info_obj->supported_pg_content) ? json_decode(json_encode($zone_info_obj->supported_pg_content), true) : array(
                                                            array(
                                                                'image_url' => '',
                                                                'image_link' => '',
                                                                'sort_order' => '',
                                                                'country' => array(),
                                                            )
                                                        );
                                                    }
                                                    ?>
                                                    <!-- Start All Payment Bar Image Column -->
                                                <tr>
                                                    <td colspan="2">
                                                        <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                            <tr>
                                                                <td class="customerFormAreaTitle"><?= ENTRY_ALL_PAYMENT_COLUMNS; ?></td>
                                                            </tr>
                                                            <tr>
                                                                <td class="formArea" colspan="2">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td>&nbsp;</td>
                                                                            <td class="main"><b><?= TEXT_IMAGE_SOURCE; ?></b></td>
                                                                            <td class="main"><b><?= SELECT_APPLICABLE_COUNTRY; ?></b></td>
                                                                        </tr>
                                                                        <?php
                                                                        for ($i = 0; MAX_ALL_PAYMENT_IMAGE > $i; $i++) {
                                                                            ?>
                                                                            <tr>
                                                                                <th class="main" width="90px" align="left" valign="top"><?= TEXT_IMAGE . ' #' . ($i + 1); ?></th>
                                                                                <td class="main" valign="top"><?= tep_draw_textarea_field('footer_pg_image[' . $i . ']', '', '120', '3', $footer_pg_content[$i]); ?></td>
                                                                                <td class="main"><?php echo tep_draw_pull_down_menu('footer_pg_country[' . $i . '][]', $countries, $footer_pg_country[$i], ' multiple size=10 style="min-width: 150px;"'); ?></td>
                                                                            </tr>
                                                                            <?php
                                                                        }
                                                                        ?>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <?= tep_draw_hidden_field('clone_geo_zone_id', '', ' id="clone_geo_zone_id" '); ?>
                                                    </td>
                                                </tr>
                                                <!-- // End All Payment Bar Image Column -->

                                                <!-- Start Toll Free Column -->
                                                <tr>
                                                    <td colspan="2">
                                                        <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                            <tr>
                                                                <td class="customerFormAreaTitle">Toll Free</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="formArea" colspan="2">
                                                                    <table id="toll_free" border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <thead>
                                                                            <tr>
                                                                                <td class="main" colspan="2">
                                                                                    <input type="button" name="add_slider_btn" value="Add Toll Free" class="inputButton" onClick="javascript:add_row('toll_free', 'supported_pg_country');" />
                                                                                </td>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            <?php
                                                                            foreach ($toll_free as $num => $val) {
                                                                                $count = $num + 1;
                                                                                ?>
                                                                                <tr id="row_<?php echo $count; ?>">
                                                                                    <td colspan="2">
                                                                                        <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                                            <tr>
                                                                                                <td colspan="3" class="dottedLine">&nbsp;<?php echo tep_draw_hidden_field('toll_free[country_idx][]', $count, ' class="toll_free_country_idx"'); ?></td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td colspan="2"><a class="del" href="javascript: void(0);" onclick="del_row(this);" style="<?php echo $num != 0 ? 'display:inline' : 'display:none'; ?>"><img src="images/icons/delete.gif" border="0" alt="<?php echo TEXT_DELETE_TOLL_FREE; ?>" title="<?php echo TEXT_DELETE_TOLL_FREE; ?>" /></a></td>
                                                                                                <td class="main"><b><?= SELECT_APPLICABLE_COUNTRY; ?></b></td>
                                                                                            </tr>
                                                                                            <tr valign="top">
                                                                                                <td class="main" width="120px"><?= TEXT_TOLL_FREE; ?></td>
                                                                                                <td class="main"><?= tep_draw_input_field('toll_free[num][]', $val['num'], ' class="toll_free_num" size="115"'); ?></td>
                                                                                                <td class="main"><?php echo tep_draw_pull_down_menu('toll_free[country][' . $count . '][]', $countries, $val['country'], ' class="toll_free_country" multiple size=6 style="min-width: 150px;"'); ?></td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                                <?php
                                                                            }
                                                                            ?>
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <!-- // End Toll Free Column -->

                                                <!-- Start All Payment Bar Image Column -->
                                                <tr>
                                                    <td colspan="2">
                                                        <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                            <tr>
                                                                <td class="customerFormAreaTitle">New <?= ENTRY_ALL_PAYMENT_COLUMNS; ?></td>
                                                            </tr>
                                                            <tr>
                                                                <td class="formArea" colspan="2">
                                                                    <table id="supported_pg" border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <thead>
                                                                            <tr>
                                                                                <td class="main" colspan="2">
                                                                                    <input type="button" name="add_slider_btn" value="Add Supported PG" class="inputButton" onClick="javascript:add_row('supported_pg', 'supported_pg_country');" />
                                                                                </td>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            <?php
                                                                            foreach ($supported_pg_content as $num => $val) {
                                                                                $count = $num + 1;
                                                                                ?>
                                                                                <tr id="row_<?php echo $count; ?>">
                                                                                    <td colspan="2">
                                                                                        <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                                            <tr>
                                                                                                <td colspan="3" class="dottedLine">&nbsp;<?php echo tep_draw_hidden_field('supported_pg[country_idx][]', $count, ' class="supported_pg_country_idx"'); ?></td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td colspan="2"><a class="del" href="javascript: void(0);" onclick="del_row(this);" style="<?php echo $num != 0 ? 'display:inline' : 'display:none'; ?>"><img src="images/icons/delete.gif" border="0" alt="<?php echo TEXT_DELETE_SUPPORTED_PG; ?>" title="<?php echo TEXT_DELETE_SUPPORTED_PG; ?>" /></a></td>
                                                                                                <td class="main"><b><?= SELECT_APPLICABLE_COUNTRY; ?></b></td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td class="main" width="120px"><?= TEXT_IMAGE_URL; ?></td>
                                                                                                <td class="main"><?= tep_draw_input_field('supported_pg[image_url][]', $val['image_url'], ' class="supported_pg_thumbnail_image" size="115"'); ?></td>
                                                                                                <td class="main" rowspan="3"><?php echo tep_draw_pull_down_menu('supported_pg[country][' . $count . '][]', $countries, $val['country'], ' class="supported_pg_country" multiple size=6 style="min-width: 150px;"'); ?></td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td class="main" width="120px">Image Link</td>
                                                                                                <td class="main"><?= tep_draw_input_field('supported_pg[image_link][]', $val['image_link'], ' class="supported_pg_image_url" size="115"'); ?></td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td class="main" width="120px"><?= TEXT_SORT_ORDER; ?></td>
                                                                                                <td class="main"><?= tep_draw_input_field('supported_pg[sort_order][]', $val['sort_order'], ' class="supported_pg_sort_order" size="20"'); ?></td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                                <?php
                                                                            }
                                                                            ?>
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <!-- // End All Payment Bar Image Column -->

                                                <tr>
                                                    <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                </tr>

                                                <tr>
                                                    <td colspan="2">
                                                        <div id="languages_tab">
                                                            <ul>
                                                                <?php
                                                                for ($j = 0, $jcnt = count($languages); $jcnt > $j; $j++) {
                                                                    ?>
                                                                    <li id="languages_li_<?= $languages[$j]['code']; ?>"><a href="#languages_tab_<?= $languages[$j]['code']; ?>"><span><?= $languages[$j]['name']; ?></span></a></li>
                                                                    <?php
                                                                }
                                                                ?>
                                                            </ul>

                                                            <?php
                                                            for ($j = 0, $jcnt = count($languages); $jcnt > $j; $j++) {
                                                                $copy_lang = array();
                                                                $lang_code = $languages[$j]['code'];

                                                                $copy_lang[] = array('id' => '',
                                                                    'text' => SELECT_ZONES_DEFAULT);
                                                                for ($i = 0, $cnt = count($languages); $cnt > $i; $i++) {
                                                                    if ($languages[$i]['code'] != $lang_code) {
                                                                        $copy_lang[] = array('id' => $languages[$i]['code'],
                                                                            'text' => $languages[$i]['name']);
                                                                    }
                                                                }
                                                                ?>
                                                                <div id="languages_tab_<?= $lang_code; ?>" class="languages_tab">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <!-- Start Banner Column -->
                                                                        <tr>
                                                                            <td>
                                                                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                                    <tr>
                                                                                        <td class="customerFormAreaTitle"><?= ENTRY_BANNER_CONTENT; ?></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td class="formArea">
                                                                                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                                                <tr>
                                                                                                    <td class="main" align="right">
                                                                                                        <?php
                                                                                                        echo TEXT_COPY_FROM;
                                                                                                        echo '&nbsp;';
                                                                                                        echo tep_draw_pull_down_menu('banner_copy_' . $lang_code, $copy_lang, '', ' id="banner_copy_' . $lang_code . '" ');
                                                                                                        echo '&nbsp;';
                                                                                                        echo tep_button(TEXT_COPY, TEXT_COPY, '', ' onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'" onclick="javascript: banner_copy_func(\'' . $lang_code . '\', ' . MAX_BANNER_IMAGE . ');" ', 'inputButton', true);
                                                                                                        ?>
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr>
                                                                                                    <td id="banner_content_<?= $lang_code; ?>">
                                                                                                        <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                                                            <?php
                                                                                                            for ($i = 0; MAX_BANNER_IMAGE > $i; $i++) {
                                                                                                                ?>
                                                                                                                <tr>
                                                                                                                    <td>&nbsp;</td>
                                                                                                                    <td class="main"><b><?= TEXT_IMAGE_SOURCE; ?></b></td>
                                                                                                                    <td class="main"><b><?= SELECT_APPLICABLE_COUNTRY; ?></b></td>
                                                                                                                </tr>
                                                                                                                <tr>
                                                                                                                    <th class="main" width="80px" align="left" valign="top"><?= TEXT_IMAGE . ' #' . ($i + 1); ?></th>
                                                                                                                    <td class="main" valign="top"><?= tep_draw_textarea_field('banner_image[' . $lang_code . '][' . $i . ']', '', '120', '5', $banner_content[$lang_code][$i], ' id="banner_image_' . $lang_code . '_' . $i . '" '); ?></td>
                                                                                                                    <td class="main"><?php echo tep_draw_pull_down_menu('banner_country[' . $lang_code . '][' . $i . '][]', $countries, $banner_country[$lang_code][$i], ' id="banner_country_' . $lang_code . '_' . $i . '" multiple size=10 style="min-width: 150px;"'); ?></td>
                                                                                                                </tr>
                                                                                                                <?php
                                                                                                                if (MAX_BANNER_IMAGE > ($i + 1)) {
                                                                                                                    ?>
                                                                                                                    <tr>
                                                                                                                        <td colspan="3" class="dottedLine">&nbsp;</td>
                                                                                                                        </td>
                                                                                                                    <tr>
                                                                                                                        <td colspan="3">&nbsp;</td>
                                                                                                                    </tr>
                                                                                                                    <?php
                                                                                                                }
                                                                                                            }
                                                                                                            ?>
                                                                                                        </table>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <!-- // End Banner Column -->

                                                                        <tr>
                                                                            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                                        </tr>

                                                                        <!-- Start Slider Content -->
                                                                        <tr>
                                                                            <td>
                                                                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                                    <tr>
                                                                                        <td class="customerFormAreaTitle"><?= ENTRY_SLIDER_CONTENT ?></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td class="formArea">
                                                                                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                                                <tr>
                                                                                                    <td>
                                                                                                        <input type="button" name="add_slider_btn" value="Add Slider" class="inputButton" onClick="javascript:add_slider('<?= $geo_zone_id; ?>', '<?= $lang_code; ?>');" />
                                                                                                        <?php
                                                                                                        $slider_row = tep_not_empty($slider_content[$lang_code]) ? count($slider_content[$lang_code]) : 0;
                                                                                                        echo tep_draw_hidden_field('slider_row_' . $lang_code, $slider_row, ' id="slider_row_' . $lang_code . '" ');
                                                                                                        ?>
                                                                                                    </td>
                                                                                                    <td class="main" align="right">
                                                                                                        <?php
                                                                                                        echo TEXT_COPY_FROM;
                                                                                                        echo '&nbsp;';
                                                                                                        echo tep_draw_pull_down_menu('slider_copy_' . $lang_code, $copy_lang, '', ' id="slider_copy_' . $lang_code . '" ');
                                                                                                        echo '&nbsp;';
                                                                                                        echo tep_button(TEXT_COPY, TEXT_COPY, '', ' onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'" onclick="javascript: slider_copy_func(\'' . $lang_code . '\');" ', 'inputButton', true);
                                                                                                        ?>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                            <table id="slider_content_<?= $lang_code; ?>" border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                                                <?php
                                                                                                if (tep_not_empty($slider_content[$lang_code])) {
                                                                                                    foreach ($slider_content[$lang_code] as $num => $val) {
                                                                                                        $cnt = $num + 1;
                                                                                                        ?>
                                                                                                        <tr id="slider_content_row_<?= $lang_code; ?>_<?= $cnt; ?>">
                                                                                                            <td>
                                                                                                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                                                                    <tr>
                                                                                                                        <td class="dottedLine" colspan="3">&nbsp;</td>
                                                                                                                    </tr>
                                                                                                                    <tr>
                                                                                                                        <td class="main" colspan="2">
                                                                                                                            <a href="javascript: del_slider('<?= $lang_code; ?>', '<?= $cnt; ?>');"><img src="images/icons/delete.gif" border="0" alt="<?= TEXT_DELETE_SLIDER; ?>" title="<?= TEXT_DELETE_SLIDER; ?>" /></a>
                                                                                                                        </td>
                                                                                                                        <td class="main"><b><?= SELECT_APPLICABLE_COUNTRY; ?></b></td>
                                                                                                                    </tr>
                                                                                                                    <tr>
                                                                                                                        <td class="main" width="120px"><?= TEXT_THUMBNAIL_IMAGE; ?><span style="color: red">*</span></td>
                                                                                                                        <td class="main"><?= tep_draw_input_field('slider_thumbnail_image[' . $lang_code . '][' . $cnt . ']', $val['slider_thumbnail_image'], ' id="slider_thumbnail_image_' . $lang_code . '_' . $cnt . '" size="115"'); ?></td>
                                                                                                                        <td class="main" rowspan="6"><?php echo tep_draw_pull_down_menu('slider_country[' . $lang_code . '][' . $cnt . '][]', $countries, $slider_country[$lang_code][$num], ' id="slider_country_' . $lang_code . '_' . $cnt . '" multiple size=10 style="min-width: 150px;"'); ?></td>
                                                                                                                    </tr>
                                                                                                                    <tr>
                                                                                                                        <td class="main" width="120px">Desktop View<span style="color: red">*</span></td>
                                                                                                                        <td class="main"><?= tep_draw_input_field('slider_image_url[' . $lang_code . '][' . $cnt . ']', $val['slider_image_url'], ' id="slider_image_url_' . $lang_code . '_' . $cnt . '" size="115"'); ?></td>
                                                                                                                    </tr>
                                                                                                                    <tr>
                                                                                                                        <td class="main" width="120px">Redirect Link<span style="color: red">*</span></td>
                                                                                                                        <td class="main"><?= tep_draw_input_field('slider_background_image[' . $lang_code . '][' . $cnt . ']', $val['slider_background_image'], ' id="slider_background_image_' . $lang_code . '_' . $cnt . '" size="115"'); ?></td>
                                                                                                                    </tr>
                                                                                                                    <tr>
                                                                                                                        <td class="main" width="120px"><?= TEXT_CAPTION; ?><span style="color: red">*</span></td>
                                                                                                                        <td class="main"><?= tep_draw_input_field('slider_caption[' . $lang_code . '][' . $cnt . ']', $val['slider_caption'], ' id="slider_caption_' . $lang_code . '_' . $cnt . '" size="115"'); ?></td>
                                                                                                                    </tr>
                                                                                                                    <tr>
                                                                                                                        <td class="main" width="120px"><?= TEXT_SORT_ORDER; ?></td>
                                                                                                                        <td class="main"><?= tep_draw_input_field('slider_sort_order[' . $lang_code . '][' . $cnt . ']', $val['slider_sort_order'], ' id="slider_sort_order_' . $lang_code . '_' . $cnt . '" size="20"'); ?></td>
                                                                                                                    </tr>
                                                                                                                    <tr>
                                                                                                                        <td colspan="3">&nbsp;</td>
                                                                                                                    </tr>
                                                                                                                </table>
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        <?php
                                                                                                    }
                                                                                                }
                                                                                                ?>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <!-- // End of Slider Content -->

                                                                    </table>
                                                                </div>
                                                                <?php
                                                            }
                                                            ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?
                                                break;
                                        }
                                        ?>
                                        <tr>
                                            <td colspan="2"><?= tep_submit_button('Save Info', 'Save Info', ' onmouseover="this.className=\'inputButtonOver\'" onmouseout="this.className=\'inputButton\'" ' . ( $zone_type == '5' ? ' onClick="checkforinput();" ' : ''), 'inputButton', true) ?></td>
                                        </tr>
                                    <? } ?>
                    </table>
                </td>
            </tr>
        </table>
        </form>
        <script>
            function checkforinput() {
                jQuery(function ($) {
                    var err = false;
                    $("input[name^='slider_thumbnail_image']").filter(function () {
                        if ($(this).val() == "") {
                            err = true;
                            event.preventDefault();
                        }
                    });
                    $("input[name^='slider_background_image']").filter(function () {
                        if ($(this).val() == "") {
                            err = true;
                            event.preventDefault();
                        }
                    });
                    $("input[name^='slider_image_url']").filter(function () {
                        if ($(this).val() == "") {
                            err = true;
                            event.preventDefault();
                        }
                    });
                    $("input[name^='slider_caption']").filter(function () {
                        if ($(this).val() == "") {
                            err = true;
                            event.preventDefault();
                        }
                    });
                    if (err == true) {
                        alert("Please fill-in required field(s)");
                    }
                })
            }
        </script>
        <?
        $listing_html = ob_get_contents();
        ob_end_clean();

        return $listing_html;
    }

}
?>