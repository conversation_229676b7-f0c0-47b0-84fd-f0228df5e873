<?
/*
  	$Id: column_left.php,v 1.13 2013/06/07 13:02:51 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

    if (tep_admin_check_boxes('administrator.php') == true) { require(DIR_WS_BOXES . 'administrator.php'); }
    if (tep_admin_check_boxes('buyback.php') == true) { require(DIR_WS_BOXES . 'buyback.php'); }
    if (tep_admin_check_boxes('purchase_orders.php') == true) { require(DIR_WS_BOXES . 'purchase_orders.php'); }
    if (tep_admin_check_boxes('catalog.php') == true) {     require(DIR_WS_BOXES . 'catalog.php'); }
    if (tep_admin_check_boxes('configuration.php') == true) { require(DIR_WS_BOXES . 'configuration.php'); }
    if (tep_admin_check_boxes('customers.php') == true) { require(DIR_WS_BOXES . 'customers.php'); }
    if (tep_admin_check_boxes('data_pool.php') == true) { require(DIR_WS_BOXES . 'data_pool.php'); }
    if (tep_admin_check_boxes('infolinks.php') == true) { require(DIR_WS_BOXES . 'infolinks.php'); }
    if (tep_admin_check_boxes('latest_news.php') == true) { require(DIR_WS_BOXES . 'latest_news.php'); }
    if (tep_admin_check_boxes('localization.php') == true) { require(DIR_WS_BOXES . 'localization.php'); }
    if (tep_admin_check_boxes('modules.php') == true) { require(DIR_WS_BOXES . 'modules.php'); }
    if (tep_admin_check_boxes('payments.php') == true) { require(DIR_WS_BOXES . 'payments.php'); }
    if (tep_admin_check_boxes('paypalipn.php') == true) { require(DIR_WS_BOXES . 'paypalipn.php'); }
    if (tep_admin_check_boxes('promotions.php') == true) { require(DIR_WS_BOXES . 'promotions.php'); }
    if (tep_admin_check_boxes('reports.php') == true) {     require(DIR_WS_BOXES . 'reports.php'); }
    if (tep_admin_check_boxes('sales.php') == true) { require(DIR_WS_BOXES . 'sales.php'); }
    if (tep_admin_check_boxes('ogc.php') == true) { require(DIR_WS_BOXES . 'ogc.php'); }
    if (tep_admin_check_boxes('stock.php') == true) { require(DIR_WS_BOXES . 'stock.php'); }
    if (tep_admin_check_boxes('taxes.php') == true) { require(DIR_WS_BOXES . 'taxes.php'); }
    if (tep_admin_check_boxes('theme.php') == true) { require(DIR_WS_BOXES . 'theme.php'); }
    if (tep_admin_check_boxes('tools.php') == true) { require(DIR_WS_BOXES . 'tools.php'); }
    if (tep_admin_check_boxes('c2c.php') == true) { require(DIR_WS_BOXES . 'c2c.php'); }
?>