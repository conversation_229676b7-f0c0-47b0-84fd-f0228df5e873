<?php

/*
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

/* * *****************************************************************
  Define the webserver and path parameters
  DIR_FS_* = Filesystem directories (local/physical)

  DIR_WS_* = Webserver directories (virtual/URL)
  Note: Should not has leading slash and must has ending slash
 * ****************************************************************** */
define('HTTP_SERVER', 'http://crew54.offgamers.dev/'); // eg, http://localhost - should not be empty for productive servers
define('HTTPS_SERVER', 'https://crew54.offgamers.dev/'); // eg, http://localhost - should not be empty for productive servers
define('ENABLE_SSL', 'false'); // secure webserver for admin module
define('HTTP_CATALOG_SERVER', 'http://www.offgamers.dev/');
define('HTTPS_CATALOG_SERVER', '');
define('HTTP_STATIC_SERVER', 'http://www.offgamers.dev/'); // eg, static server
define('ENABLE_SSL_CATALOG', 'false'); // secure webserver for catalog module
define('HTTP_SUPPLIER_SERVER', 'http://crew54.offgamers.dev/');
define('HTTPS_SUPPLIER_SERVER', 'https://crew54.offgamers.dev/');
define('ENABLE_SSL_SUPPLIER', 'false'); // secure webserver for supplier module
define('HTTP_API_SERVER', 'http://crew54.offgamers.dev/');
define('HTTPS_API_SERVER', 'https://crew54.offgamers.dev/');
define('ENABLE_SSL_API', 'false'); // secure webserver for API module
define('HTTP_CNBB_SERVER', 'http://crew54.offgamers.dev/');
define('HTTPS_CNBB_SERVER', 'https://crew54.offgamers.dev/');
define('ENABLE_SSL_CNBB', 'false'); // secure webserver for cnbb module
define('ENABLE_SSL_UPLOAD', 'false'); // secure webserver for upload module
define('HTTP_UPLOAD_SERVER', 'http://crew54.offgamers.dev/');
define('HTTPS_UPLOAD_SERVER', 'https://crew54.offgamers.dev/');
define('PUBAPI_SERVER_URL', 'http://crew54.offgamers.dev');
define('HTTP_COOKIE_DOMAIN', 'crew54.offgamers.dev');
define('HTTPS_COOKIE_DOMAIN', 'crew54.offgamers.dev');
define('HTTP_COOKIE_PATH', '/');
define('HTTPS_COOKIE_PATH', '/');
define('HTTP_AFFILIATE_SERVER', 'http://crew54.offgamers.dev/');
define('HTTPS_AFFILIATE_SERVER', 'https://crew54.offgamers.dev/');
define('ENABLE_SSL_AFFILIATE', 'false'); // secure webserver for supplier module
define('DIR_WS_AFFILIATE', 'affiliate/'); // absolute path required
define('DIR_FS_DOCUMENT_ROOT', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/admin/'); // where the pages are located on the server
define('DIR_WS_ADMIN', ''); // absolute path required
define('DIR_WS_CNBB', 'buyback/'); // absolute path required
define('DIR_FS_CNBB', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/buyback/'); // absolute path required
define('DIR_WS_UPLOAD', 'upload/'); // absolute path required
define('DIR_WS_SUPPLIER', 'supplier/'); // absolute path required
define('DIR_WS_API', 'api/'); // absolute path required
define('DIR_FS_ADMIN', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/admin/'); // absolute path required
define('DIR_WS_CATALOG', ''); // absolute path required
define('DIR_FS_CATALOG', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/'); // absolute path required
define('DIR_FS_STATIC_SERVER_DEST', 'D:/_www/offgamers.dev/www54.offgamers.dev/httpdocs/'); // Actual location of static file
define('DIR_WS_IMAGES', 'images/');
define('DIR_WS_ICONS', DIR_WS_IMAGES . 'icons/');
define('DIR_WS_INTERFACE', DIR_WS_IMAGES . 'Interface/');
define('DIR_WS_CATALOG_IMAGES', HTTP_STATIC_SERVER . 'images/');
define('DIR_FS_HLA', DIR_FS_STATIC_SERVER_DEST);
define('DIR_WS_INCLUDES', 'includes/');
define('DIR_WS_BOXES', DIR_WS_INCLUDES . 'boxes/');
define('DIR_WS_FUNCTIONS', DIR_WS_INCLUDES . 'functions/');
define('DIR_WS_CLASSES', DIR_WS_INCLUDES . 'classes/');
define('DIR_WS_MODULES', DIR_WS_INCLUDES . 'modules/');
define('DIR_WS_MODULES_HLA', DIR_WS_MODULES . 'hla/');
define('DIR_WS_LANGUAGES', DIR_WS_INCLUDES . 'languages/');
define('DIR_WS_STATISTICS', DIR_WS_INCLUDES . 'statistics/');
define('DIR_WS_CATALOG_LANGUAGES', DIR_WS_CATALOG . 'includes/languages/');
define('DIR_WS_CATALOG_JAVASCRIPT', HTTP_STATIC_SERVER . 'javascripts/');
define("DIR_WS_CSS_IMAGE", DIR_WS_CATALOG_IMAGES);
define('DIR_FS_CATALOG_LANGUAGES', DIR_FS_CATALOG . 'includes/languages/');
define('DIR_FS_CATALOG_IMAGES', DIR_FS_CATALOG . 'images/');
define('DIR_FS_CATALOG_MODULES', DIR_FS_CATALOG . 'includes/modules/');
define('DIR_FS_BACKUP', DIR_FS_ADMIN . 'backups/');
define('DIR_FS_UPGRADE', DIR_FS_ADMIN . 'upgrade/');
define('DIR_FS_UPLOAD', DIR_FS_ADMIN . 'images/upload/');
define('DIR_FS_IMPORT', DIR_FS_UPLOAD . 'import/');
define('DIR_FS_THEME', DIR_FS_CATALOG . 'theme/');
define('DIR_FS_DATA_GRAPHIC_MAIL', DIR_FS_CATALOG . 'data/graphic_mail/');

/* * **************************
  CD Key
 * ************************** */
define('DIR_FS_SECURE', 'D:/_secure/');
define('DIR_FS_SECURE_TEMP', 'D:/_secure/temp/');
define('SECURE_KEY', '2QH4E9B1M9L0F37D3HJ4FH6853FL8GD8'); // Must be 32 characters length
define('SECURE_KEY_IV', '9S?C3B4WMQW6XM8P'); // Must be 16 characters length
define('SECURE_CIPHER', MCRYPT_RIJNDAEL_128);

/* * **************************
  Customer Documents
 * ************************** */
define('DIR_FS_DATA_LL_SUBMISSION', DIR_FS_CATALOG . 'upload/docs/');
define('DIR_FS_DATA_LL_SUBMISSION_TEMP', DIR_FS_CATALOG . 'upload/docs/temp_editing/');

/* * **************************
  PIMA + BJ
 * ************************** */
define('PIMA_RC4_KEY', '~#[.ab!@MNm234f*Vuv2-<EFef`$KLkl7&\XwhgfABW{,CDc-0lo+>}/GHgh9%]IJi*_)(j8^|YZyz0=?*:OPop 5("QRqr4);STst3_\'*/JK<>e#df;mvufe4w89ds&');

// define affiliate
define('AFFILIATE_SITE_ID', 2); // for customers login sites

/* * **************************
  My Account
 * ************************** */
define('HTTP_MY_ACCOUNT_PORTAL', 'http://shasso.localhost');
define("HTTP_SHASSO_PORTAL", "http://www.shasso.local/api");
define("HTTP_SHASSO_CLIENT_ID", "ogm_crew");
define("HTTP_SHASSO_CLIENT_SECRET", "123456");
define('SHASSO_STORE_OWNER', 'Shasso Team');
define('SHASSO_STORE_OWNER_EMAIL_ADDRESS', '<EMAIL>');
define('SHASSO_EMAIL_SUBJECT_PREFIX', '[Shasso]');
define('SHASSO_STORE_NAME', 'Shasso');
define('SHASSO_STORE_EMAIL_SIGNATURE', 'Shasso.biz');

/* * **************************
  Memcached
 * ************************** */
define('CFG_MEMCACHE_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
define('CFG_MEMCACHE_KEY_PREFIX', 'ogm/'); // To ensure all memcache across the applications start with same prefix

/* * **************************
  Gift Card Redeemption
 * ************************** */
define('HTTPS_PIN_SERVER', 'https://api.offgamers.biz/ws/pin/');
define('PIN_REQUEST_MERCANT_CODE', 'OGMGLOBAL');
define('PIN_REQUEST_SECRET_KEY', 'klwe0990');

define('MGC_PIN_REQUEST_MERCANT_CODE', 'MGCGLOBAL');
define('MGC_PIN_REQUEST_SECRET_KEY', 'mgc151401');
define('HTTPS_MGC_SERVER', 'https://api.multigamecard.biz/pin/status');
define('MGC_API_CREDENTIAL_CODE', 'og-crew');
define('MGC_API_CREDENTIAL_KEY', '123456');
define('MGC_CATEGORY_ID', 12295);

/* * **************************
  Chat Module
 * ************************** */
define('CHAT_API_KEY', 'ae2b1fca515949e5d54fb22b8ed95575');
define('CHAT_URL', 'http://chat.localhost.com/index.php');

/* * **************************
  G2G crew
 * ************************** */
define('C2C_PRODUCT_TYPE', '0, 1, 4, 5');
define('C2C_PRODUCT_TYPE_CHILD', '1, 2, 5, 16, 17, 18, 19, 20');
define('HTTP_G2G_CREW_PORTAL', 'http://g2g-crew.offgamers.biz');
#Point to G2G-store API
define("HTTPS_G2G_STORE", "https://g2g.offgamers.biz");
define("G2G_API_MERCHANT", "ogm_crew"); //mapping with API key, cannot change
define("G2G_API_SIGNATURE", "123456");
#Point to G2G-Crew API
define('G2G_API_URL', 'https://g2g-api.offgamers.biz'); //staging https://staging-api.g2g.com
define('G2G_API_OGCREW_MERCHANT', 'ogm_crew'); //mapping with API key, cannot change
define('G2G_API_OGCREW_SECRET', '123456');

#CHANGE PRIMARY COUNTRY
define('G2G_CHANGE_PRIMARY_COUNTRY_API_URL',G2G_API_URL . '/user/change-primary-country');

# G2G PayPal Account Info
define("G2G_PAYPAL_RECEIVER_EMAIL", "<EMAIL>");
define("G2G_PAYPAL_USERNAME", "paypal.my.staging_api1.g2g.com");
define("G2G_PAYPAL_API_PASSWORD", "5PUZEBSNGS98TCR8");
define("G2G_PAYPAL_API_SIGNATURE", "AiPC9BjkCyDFQXbSkoZcgqH3hpacAlfVMh3veIuKBikTv0oGrbttXjUD");
define('G2G_PAYPAL_MASS_PAY_USERNAME', 'dev.g2g.alpha.merchant5_api1.gmail.com');
define('G2G_PAYPAL_MASS_PAY_PASSWORD', 'KVUUKH9BUDNYPFKB');
define('G2G_PAYPAL_MASS_PAY_SIGNATURE', 'Ah8PmIKmly2srwm5STD5J3cnQpyuAvJr0BJ7zsEhmGPVuQk90zoZd5OQ');
define('G2G_PAYPAL_MASS_PAY_VERSION', 'NVP'); //"REST V1" or "NVP"
define('G2G_PAYPAL_DISBURSE_PAYMENT_METHODS', '7,231,232,233,234,235,236,237,238,239,240,241,242'); // live : '7,267,389,390,391,392,479,480,481,482,483,484,485,486'

define("G2G_ADD_RESERVE_EMAIL", "<EMAIL>, <EMAIL>");
define("G2G_NOREPLY_SENDER_EMAIL", "<EMAIL>");
# G2G Order Tags
define("G2G_SO_NO_RECEIVE_TAG_ID", 516); //LIVE 684
define("G2G_BO_NO_RECEIVE_TAG_ID", 515);//LIVE 683,
/* * **************************
  AWS SQS
 * ************************** */
define('AWS_SQS_KEY', '');
define('AWS_SQS_SECRET', '');
define('AWS_SQS_INVOICE_QUEUE_URL', '');
define('AWS_SQS_CREDIT_NOTE_QUEUE_URL', '');
define('AWS_SQS_MS_ORDER_URL', '');
define('AWS_SQS_PDF_QUEUE_URL', '');

/* * **************************
  Auto Tagging
 * ************************** */
define('PWLING_UNSUBMITTED_ORDER_TAG_ID', 101);
define('HLA_WOW_ACC_ORDER_TAG_ID', 472);

/* * **************************
  pipwave - prediction & full integration service
 * ************************** */
define('PIPWAVE_MERCHANT_API_KEY', 'ZWYqoCLpe71skiDJMVzMu5KvKzydh9199OhLIZ3i'); // eg, localhost - should not be empty for productive servers
define('PIPWAVE_MERCHANT_API_SECRET', 'StLz5RuHP1swKdi2Tisn13530Hxz0RlZ64e30VGh');
define('PIPWAVE_MERCHANT_API_VERSION', 'amlv1');
define('PIPWAVE_MERCHANT_API_URL', 'http://api.pipwave.localhost/risk-prediction');
define('PIPWAVE_G2G_MERCHANT_API_KEY', 'ZWYqoCLpe71skiDJMVzMu5KvKzydh9199OhLIZ3i');
define('PIPWAVE_G2G_MERCHANT_API_SECRET', 'StLz5RuHP1swKdi2Tisn13530Hxz0RlZ64e30VGh');
define('PIPWAVE_PAYMENT_API_URL', 'http://api.pipwave.localhost/payment');
define('PIPWAVE_PAYMENT_TRANSACTION_REPORT_URL', 'http://merchant.pipwave.localhost/reports/payment-transaction/view');
define('PIPWAVE_RULE_DIRECT_VIEW_URL', 'http://merchant.pipwave.localhost/direct/view-rule-result');
define('PIPWAVE_MERCHANT_URL', 'https://staging-merchant.pipwave.com'); //LIVE: https://merchant.pipwave.com
define('PIPWAVE_MERCHANT_AML_API_URL', 'https://staging-api-ag.pipwave.com/doc'); // LIVE: https://api.pipwave.com/doc

/* * **************************
  Algolia - Search Engine Service
 * ************************** */
define('HTTP_PROXY', '');
define('ALGOLIA_APPLICATIONS', '');
define('ALGOLIA_ADMIN_KEY', '');
define('ALGOLIA_CATEGORY_INDICES', 'categories');
define('ALGOLIA_PRODUCT_INDICES', 'products');

/* * **************************
  Slack Integration
 * ************************** */
define('SLACK_WEBHOOK_BASE_PATH', 'https://hooks.slack.com/services');
define('SLACK_WEBHOOK_RESTOCK_FAIL_URL','TFQ825EGN/BUP7W5555/SlmnEnDGwIZYV8hoP9I9ItYg');
define('SLACK_WEBHOOK_DEV_DEBUG', 'TFQ825EGN/BUP7W5555/SlmnEnDGwIZYV8hoP9I9ItYg');
define('G2G_SLACK_WEBHOOK_DEV_DEBUG_ID', 'TBZ9H9FUN/B012A0C769E/1KfTLNNnfMRsAuYymZfxmctm');
define('G2G_SLACK_WEBHOOK_DEV_DEBUG_CHANNEL', 'g2g-staging-log');
define('SLACK_WEBHOOK_ANB', 'TFQ825EGN/BUP7W5555/SlmnEnDGwIZYV8hoP9I9ItYg');
define('SLACK_WEBHOOK_RESET_DAILY_LIMIT', 'TFQ825EGN/BUP7W5555/SlmnEnDGwIZYV8hoP9I9ItYg');
define('SLACK_WEBHOOK_CET', 'TFQ825EGN/BUP7W5555/SlmnEnDGwIZYV8hoP9I9ItYg');
define('SLACK_WEBHOOK_BDT', 'T01DJ75ELQ0/B01CUGSM7BL/zIOnNasWqOBdIkF4BEwFvChV');
define('SLACK_WEBHOOK_INV_PRODUCT_PRICE', 'TFQ825EGN/BUP7W5555/SlmnEnDGwIZYV8hoP9I9ItYg');
define('SLACK_WEBHOOK_INV_PRODUCT_STOCK', 'TFQ825EGN/BUP7W5555/SlmnEnDGwIZYV8hoP9I9ItYg');
define('SLACK_WEBHOOK_INV_CREDITNOTE', 'TFQ825EGN/BUP7W5555/SlmnEnDGwIZYV8hoP9I9ItYg');

/* * **************************
  Currency rate provider
 * ************************** */
define('CURRENCY_LAYER_API_KEY', '');

// define our database connection
define('DB_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
define('DB_SERVER_USERNAME', 'skc_user');
define('DB_SERVER_PASSWORD', 'skc_password');
define('DB_DATABASE', 'offgamers');
define('USE_PCONNECT', 'false'); // use persisstent connections?
define('STORE_SESSIONS', 'mysql'); // leave empty '' for default handler or set to 'mysql'

// read replica
define('DB_RR_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
define('DB_RR_SERVER_USERNAME', 'skc_user');
define('DB_RR_SERVER_PASSWORD', 'skc_password');
define('DB_RR_DATABASE', 'offgamers');

define('IP_RESTRICTION_CATEGORY', serialize (array(
    'WHITELIST' => array(
        'MY' => array(4444,4422),
        'CN' => array(5236),
    ),
    'BLACKLIST' => array(
        'MY' => array(7946, 4204),
    ),
)));

// define our reporting database connection
define('DB_REPORT_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
define('DB_REPORT_SERVER_USERNAME', 'skc_user');
define('DB_REPORT_SERVER_PASSWORD', 'skc_password');
define('DB_REPORT_DATABASE', 'offgamers');

// define our new-og database connection
define('DB_OG_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
define('DB_OG_SERVER_USERNAME', 'root');
define('DB_OG_SERVER_PASSWORD', 'root');
define('DB_OG_DATABASE', 'OG');

define('DB_OG_RR_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
define('DB_OG_RR_SERVER_USERNAME', 'root');
define('DB_OG_RR_SERVER_PASSWORD', 'root');
define('DB_OG_RR_DATABASE', 'OG');

define('MICRO_SERVICE_INVENTORY',serialize([
    'baseUrl' => 'https://dev-ms-inventory.offgamers.com',
    'key' => 'og-crew',
    'secret' => '123456'
]));

define('MICRO_SERVICE_ORDER',serialize([
    'baseUrl' => 'https://staging-ms-order.offgamers.com',
    'key' => 'backend',
    'secret' => '123456'
]));

// Store Credit Microservice Endpoints
define('MS_API_SC_URL','https://staging-ms-storecredit.offgamers.com');
define("MS_API_SC_MERCHANT", "OG"); //mapping with API key, cannot change
define("MS_API_SC_SECRET", "123456");

// Crew2 Path Config
define('CREW2_PATH', 'https://dev-crew2.offgamers.com');
define('CREW2_OPEN_PATH', 'https://dev-open.offgamers.com');

// G2G Serveless Store Credit
define('G2G_SERVERLESS_USER', 'https://staging-sls.g2g.com');
define('G2G_SERVERLESS', 'https://staging-sls-bd29rt5h.g2g.com');
define('X_API_KEY', '7UaC2MAmr27VuGQQb9qhv4pTgD8SHspba279lRbm');
define("CREW_PATH_G2G", 'https://staging-crew.g2g.com');
define("CREW2_PATH_G2G", 'https://staging-crew-vue.g2g.com');

// OG Checkout Modules Endpoints
define('MICRO_SERVICE_SECURE',serialize([
    'baseUrl' => 'https://secure.offgamers.com',
    'key' => 'crew',
    'secret' => '123456'
]));

define('CONFIG_APACHE_USER','apache');
define('OGC_CATEGORIES_ID',11833);

define("DELIVERY_EMAIL_CET_ADDRESS", "<EMAIL>");

define("G2G_OG_MAPPER_USER", 684255);
?>