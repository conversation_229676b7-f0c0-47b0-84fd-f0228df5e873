<?php

function tep_remove_from_authorized($remove_id, $admin_groups_authorized) {
    $admin_groups_authorized_array = explode(",", $admin_groups_authorized);
    $remove_id_array = explode(",", $remove_id);

    $new_admin_groups_authorized_array = array_diff($admin_groups_authorized_array, $remove_id_array);
    $new_admin_groups_authorized = implode(",", $new_admin_groups_authorized_array);

    return $new_admin_groups_authorized;
}

function tep_admin_member_exist($admin_id) {
    $admin_id_select_sql = "	SELECT admin_id
                        		FROM " . TABLE_ADMIN . " 
                        		WHERE admin_id = '" . tep_db_input($admin_id) . "'";
    $admin_id_result_sql = tep_db_query($admin_id_select_sql);

    if (tep_db_num_rows($admin_id_result_sql)) {
        return true;
    } else {
        return false;
    }
}

function tep_admin_group_exist($admin_groups_id) {
    $admin_groups_id_select_sql = "	SELECT admin_groups_id
                        			FROM " . TABLE_ADMIN_GROUPS . " 
                        			WHERE admin_groups_id = '" . tep_db_input($admin_groups_id) . "'";
    $admin_groups_id_result_sql = tep_db_query($admin_groups_id_select_sql);

    if (tep_db_num_rows($admin_groups_id_result_sql)) {
        return true;
    } else {
        return false;
    }
}

function tep_get_admin_credit_limit($admin_id) {
    $admin_credit_limit = array();
    $admin_credit_limit_select_sql = "	SELECT admin_credit_limit_max, admin_credit_limit_total, reset_team_limit_used, reset_team_limit_max
                                        FROM " . TABLE_ADMIN_CREDIT_LIMIT . "
                                        WHERE admin_id = '" . tep_db_input($admin_id) . "'";
    $admin_credit_limit_result_sql = tep_db_query($admin_credit_limit_select_sql);
    $admin_credit_limit_row = tep_db_fetch_array($admin_credit_limit_result_sql);

    $admin_credit_limit['admin_credit_limit_max'] = tep_not_null($admin_credit_limit_row['admin_credit_limit_max']) ? $admin_credit_limit_row['admin_credit_limit_max'] : 0;
    $admin_credit_limit['admin_credit_limit_total'] = tep_not_null($admin_credit_limit_row['admin_credit_limit_total']) ? $admin_credit_limit_row['admin_credit_limit_total'] : 0;
    $admin_credit_limit['reset_team_limit_used'] = tep_not_null($admin_credit_limit_row['reset_team_limit_used']) ? $admin_credit_limit_row['reset_team_limit_used'] : 0;
    $admin_credit_limit['reset_team_limit_max'] = tep_not_null($admin_credit_limit_row['reset_team_limit_max']) ? $admin_credit_limit_row['reset_team_limit_max'] : 0;

    return $admin_credit_limit;
}

function tep_get_group_info() {
    $group_info_arr = array();
    $get_group_info_select_sql = "	SELECT admin_groups_id, admin_groups_name
									FROM " . TABLE_ADMIN_GROUPS . " 
									ORDER BY admin_groups_name";

    $get_group_info_result_sql = tep_db_query($get_group_info_select_sql);

    while ($get_group_info_row = tep_db_fetch_array($get_group_info_result_sql)) {
        $group_info_arr[] = array("groups_id" => $get_group_info_row['admin_groups_id'], "groups_name" => $get_group_info_row['admin_groups_name'],);
    }

    return $group_info_arr;
}

function tep_generate_admin_permission_csv($group_info_arr, $admin_files_arr) {
    $export_csv_data = '';
    $export_heading_csv = array();
    $groups_name_arr = array();
    $export_group_csv_data = array();
    $export_admin_file_csv_data = array();
    $group_id_arr = array();
    $merged_csv_data_rows = array();
    $generate_csv_data = array();

    $export_heading_csv[] = 'Permission / Group name';

    foreach ($group_info_arr as $group_info) {
        $groups_name_arr[] = '"' . $group_info['groups_name'] . '"';
    }
    $export_group_csv_data = array_merge($export_heading_csv, $groups_name_arr);

    foreach ($admin_files_arr as $admin_arr_id => $admin_files) {
        $group_id_arr = explode(',', $admin_files['groups_id']);

        switch ($admin_files['files_level']) {
            case '1':
                $export_admin_file_csv_data[$admin_arr_id][] = tep_filter_admin_permission_csv($admin_files['files_name']);
                break;

            case '2':
                $export_admin_file_csv_data[$admin_arr_id][] = str_repeat(" ", 5) . tep_filter_admin_permission_csv($admin_files['files_name']);
                break;

            case '3':
                $export_admin_file_csv_data[$admin_arr_id][] = str_repeat(" ", 10) . tep_filter_admin_permission_csv($admin_files['files_name']);
                break;
        }

        foreach ($group_info_arr as $group_info) {
            if (in_array($group_info['groups_id'], $group_id_arr)) {
                $export_admin_file_csv_data[$admin_arr_id][] = '1';
            } else {
                $export_admin_file_csv_data[$admin_arr_id][] = '0';
            }
        }
        unset($group_id_arr);
    }

    $merged_csv_data_rows = array_merge(array($export_group_csv_data), $export_admin_file_csv_data);

    foreach ($merged_csv_data_rows as $csv_data) {
        $export_csv_data .= implode(',', $csv_data) . "\n";
    }

    return $export_csv_data;
}

function tep_filter_admin_permission_csv($csv_data) {
    $csv_data = str_replace(array('"', '<br>', '<BR>'), array('""', ''), $csv_data);
    return $csv_data;
}

function tep_admin_login_attempt_notification($user_email) {
    $email_subject = EMAIL_SUBJECT_PREFIX . ' Crew failed login';
    $email_text = 'The admin user (' . $user_email . ') has reached the maximum 10 failed login attempt on ' . date("Y-m-d G:i:s");

    if (tep_not_null(SITE_INFO_ADMIN_EMAIL_ADDRESS)) {
        $email_to_array = tep_parse_email_string(SITE_INFO_ADMIN_EMAIL_ADDRESS);
        for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
            @tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $email_subject, $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }
    }
}

function tep_admin_group_edit_discount_permission($customer_group_id) {
    $edit_discount_setting_select_sql = "	SELECT discount_setting_admin_groups_id
											FROM " . TABLE_SITE_CUSTOMERS_ACCESS . "
											WHERE customers_groups_id = '" . tep_db_prepare_input($customer_group_id) . "' 
												AND FIND_IN_SET( '" . $_SESSION['login_groups_id'] . "', discount_setting_admin_groups_id)";
    $edit_discount_setting_result_sql = tep_db_query($edit_discount_setting_select_sql);
    if (tep_db_num_rows($edit_discount_setting_result_sql)) {
        return true;
    }
    return false;
}

// Checking group permission
function tep_get_admin_cdkey_view_limit($admin_id) {
    $admin_cdkey_view_limit = array();
    $admin_credit_limit_select_sql = "	SELECT cdkey_limit, cdkey_total_view
										FROM " . TABLE_CDKEY_VIEW . " 
										WHERE cdkey_user_id = '" . tep_db_input($admin_id) . "'";
    $admin_credit_limit_result_sql = tep_db_query($admin_credit_limit_select_sql);

    $admin_credit_limit_row = tep_db_fetch_array($admin_credit_limit_result_sql);

    $admin_cdkey_view_limit['cdkey_limit'] = tep_not_null($admin_credit_limit_row['cdkey_limit']) ? $admin_credit_limit_row['cdkey_limit'] : 0;
    $admin_cdkey_view_limit['cdkey_total_view'] = tep_not_null($admin_credit_limit_row['cdkey_total_view']) ? $admin_credit_limit_row['cdkey_total_view'] : 0;

    return $admin_cdkey_view_limit;
}

?>