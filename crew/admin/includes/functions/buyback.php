<?php
function tep_draw_colour_picker($id=0, $selected_colour_code='', $type, $colour_picker_arr) {
	//---- Start Lame Colour Picker
	if ($type == 'bo_days') {
		$colour_pickerHTML = "<select name='bo_days_row_colour[".$id."]' id='bo_days_row_colour_".$id."'>";
	} elseif($type == 'margin') {
		$colour_pickerHTML = "<select name='margin_row_colour[".$id."]' id='margin_row_colour_".$id."'>";
	} else {
		$colour_pickerHTML = "<select name='row_colour[".$id."]' id='row_colour_".$id."'>";
	}
	
	$colour_pickerHTML .= "<option value='$id'> ".PULL_DOWN_DEFAULT." </option>";
	
	foreach ($colour_picker_arr as $colour_name => $colour_code) {
		$colour_pickerHTML .= "<option value='$colour_code' style='background-color:$colour_code' ";
		if ($colour_code == $selected_colour_code) {
			$colour_pickerHTML .= "selected";
		}
		$colour_pickerHTML .= "> $colour_name </option>";
	}
	$colour_pickerHTML .= '</select>';
	return $colour_pickerHTML;
}

function tep_set_alert_color($main_cat_id, $type) {
	$color_array = array();
	
	$get_color_select_sql = "	SELECT automate_alert_value, automate_alert_color 
								FROM " . TABLE_AUTOMATE_ALERT_COLOR . " 
								WHERE categories_id = '" . $main_cat_id . "' 
									AND automate_alert_type = '" . $type . "' 
								ORDER BY automate_alert_value";
	$get_color_result_sql = tep_db_query($get_color_select_sql);
	
	while($get_color_row = tep_db_fetch_array($get_color_result_sql)) {
		$color_array[$get_color_row['automate_alert_value']] = $get_color_row['automate_alert_color'];
	}
	return $color_array;
}

function tep_save_categories_configuration($cat_str, $cat_cfg_update_array, $messageStack) {
    if (is_numeric($cat_str)) {
		foreach ($cat_cfg_update_array as $cfg_key => $cfg_val) {
			$cat_configuration_select_sql = "	SELECT categories_configuration_id
												FROM " . TABLE_CATEGORIES_CONFIGURATION . "
												WHERE categories_configuration_key = '" . tep_db_input($cfg_key) . "'
													AND categories_id = '" . tep_db_input($cat_str) . "'";
			$cat_configuration_result_sql = tep_db_query($cat_configuration_select_sql);

			if (!tep_db_num_rows($cat_configuration_result_sql)) {
				// insert
				$cfg_default_value_select_sql = "SELECT *
												 FROM " . TABLE_CATEGORIES_CONFIGURATION . "
												 WHERE categories_configuration_key = '" . tep_db_input($cfg_key) . "'
												 	AND categories_id = 0 ";
				$cfg_default_value_result_sql = tep_db_query($cfg_default_value_select_sql);

				if ($cfg_default_value_row = tep_db_fetch_array($cfg_default_value_result_sql)) {
					$sql_data_array = array('categories_id' => $cat_str,
		                      				'categories_configuration_title' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_title"]),
		                      				'categories_configuration_key' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_key"]),
		                      				'categories_configuration_value' => $cfg_val,
		                      				'categories_configuration_description' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_description"]),
		                      				'categories_configuration_group_id' => $cfg_default_value_row["categories_configuration_group_id"],
		                      				'sort_order' => tep_db_prepare_input($cfg_default_value_row["sort_order"]),
		                      				'last_modified' => 'NULL',
		                      				'date_added' => 'now()',
		                      				'use_function' => tep_db_prepare_input($cfg_default_value_row["use_function"]),
		                      				'set_function' => tep_db_prepare_input($cfg_default_value_row["set_function"])
		                      				);

					tep_db_perform(TABLE_CATEGORIES_CONFIGURATION, $sql_data_array);
				}
			} else if (tep_db_num_rows($cat_configuration_result_sql) == 1) {
				// update
				$cat_configuration_update_sql = "	UPDATE " . TABLE_CATEGORIES_CONFIGURATION . "
													SET categories_configuration_value = '" . $cfg_val . "'
													WHERE categories_configuration_key = '" . tep_db_input($cfg_key) . "'
														AND categories_id = '" . tep_db_input($cat_str) . "'";
				tep_db_query($cat_configuration_update_sql);
			} else {
				$messageStack->add_session('More than one records found for this key: ' . $cfg_key, 'error');
			}
		}
	}
}
?>