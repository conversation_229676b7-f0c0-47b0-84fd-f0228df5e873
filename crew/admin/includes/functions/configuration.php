<?
/*
  	$Id: configuration.php,v 1.24 2012/02/15 08:52:33 sionghuat.chng Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Venture

  	Released under the GNU General Public License
*/

function tep_get_cfg_setting($id, $id_type='catalog', $cfg_key='', $key_type='configuration_key') {
	$cat_cfg_array = array();
	$cid = $id;

	if ($id_type == 'product') 	$cid = tep_get_actual_product_cat_id($id);

	$cat_path = tep_get_particular_cat_path($cid);

	if (tep_not_null($cfg_key)) {
		$cat_path_array = explode('_', $cat_path);
		$cat_path_array = array_merge(array(0), $cat_path_array);

		if ($key_type == 'configuration_key') {
			for ($i=count($cat_path_array)-1; $i >= 0; $i--) {
				$cfg_value_select_sql = "	SELECT categories_configuration_value AS cfgValue
											FROM " . TABLE_CATEGORIES_CONFIGURATION . "
											WHERE categories_id ='" . $cat_path_array[$i] . "'
												AND categories_configuration_key = '" . tep_db_input($cfg_key) . "'; ";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				if ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_key] = $cfg_value_row['cfgValue'];
					break;
				}
			}
		} else if ($key_type == 'group_id') {
			for ($i=0; $i < count($cat_path_array); $i++) {
				$cfg_value_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue
											FROM " . TABLE_CATEGORIES_CONFIGURATION . "
											WHERE categories_id ='" . $cat_path_array[$i] . "'
												AND categories_configuration_group_id = '" . tep_db_input($cfg_key) . "'";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				while ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_value_row['cfgKey']] = $cfg_value_row['cfgValue'];
				}
			}
		}
	} else {
		$cat_cfg_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue
								FROM " . TABLE_CATEGORIES_CONFIGURATION . "
								WHERE categories_id=0; ";
		$cat_cfg_result_sql = tep_db_query($cat_cfg_select_sql);
		while ($cat_cfg_row = tep_db_fetch_array($cat_cfg_result_sql)) {
			$cat_cfg_array[$cat_cfg_row['cfgKey']] = $cat_cfg_row['cfgValue'];
		}

		if (tep_not_null($cat_path)) {
			$cat_path_array = explode('_', $cat_path);
			for ($i=0; $i < count($cat_path_array); $i++) {
				$cfg_value_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue
											FROM " . TABLE_CATEGORIES_CONFIGURATION . "
											WHERE categories_id ='" . $cat_path_array[$i] . "' ";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				while ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_value_row['cfgKey']] = $cfg_value_row['cfgValue'];
				}
			}
		}
	}

	return $cat_cfg_array;
}

// Transaction status configuration related functions
function tep_check_status_update_permission($trans_type, $user_grp_id, $from_status, $to_status, $pm_id='') {
	if ($to_status > 0) {	// Ignore update comment
		$update_permission_checking_select_sql = "	SELECT status_configuration_user_groups_id
													FROM " . TABLE_STATUS_CONFIGURATION . "
													WHERE FIND_IN_SET('" . $user_grp_id . "', status_configuration_user_groups_id)
														AND status_configuration_trans_type = '".tep_db_input($trans_type)."'
														AND status_configuration_source_status_id = '".tep_db_input($from_status)."'
														AND status_configuration_destination_status_id = '".tep_db_input($to_status)."'";
		$update_permission_checking_result_sql = tep_db_query($update_permission_checking_select_sql);

		if (tep_db_num_rows($update_permission_checking_result_sql)) {
			if ($trans_type == 'C' && $from_status == '1' && $to_status == '7') {	// For customer order from Pending to Verifying
				$verify_status_select_sql = "	SELECT status_configuration_payment_methods_id 
												FROM " . TABLE_STATUS_CONFIGURATION_PAYMENT_METHODS . " 
												WHERE status_configuration_trans_type = '".tep_db_input($trans_type)."'
													AND status_configuration_source_status_id = ".(int)$from_status."
													AND status_configuration_destination_status_id = ".(int)$to_status."
													AND status_configuration_user_groups_id = " .(int)$user_grp_id;
				$verify_status_result_sql = tep_db_query($verify_status_select_sql);
				
				if ($verify_status_row = tep_db_fetch_array($verify_status_result_sql)) {
					if( tep_not_null($verify_status_row['status_configuration_payment_methods_id']) 
						&& in_array($pm_id, explode(",", $verify_status_row['status_configuration_payment_methods_id']))) {
						return true;
					} else {
						return false;
					}
				} else {
					return false;
				}
			} else {
				return true;
			}
		} else {
			return false;
		}
	} else {
		return true;
	}
}

function tep_check_customer_type_permission($user_grp_id, $site_id) {
	$site_id_select_sql = "	SELECT site_id FROM " . TABLE_SITE_CUSTOMERS_ACCESS . " WHERE site_id = '" . (int)$site_id . "' AND FIND_IN_SET('" . $user_grp_id . "', admin_groups_id) ";
	$site_id_result_sql = tep_db_query($site_id_select_sql);
	
	if (tep_db_num_rows($site_id_result_sql) > 0) {
		return true;
	} else {
		return false;
	}
}

function tep_check_customer_group_permission($user_grp_id, $site_id, $customer_groups_id) {
	if ($site_id > 0)	$customer_groups_id = 0;
	
	$site_id_select_sql = "	SELECT site_id FROM " . TABLE_SITE_CUSTOMERS_ACCESS . " 
							WHERE site_id = '" . (int)$site_id . "'   
								AND FIND_IN_SET('" . $user_grp_id . "', admin_groups_id) 
								AND customers_groups_id = '" . $customer_groups_id . "'";
	$site_id_result_sql = tep_db_query($site_id_select_sql);
	
	if (tep_db_num_rows($site_id_result_sql)) {
		return true;
	} else {
		return false;
	}
}

function tep_status_update_notification($trans_type, $trans_id, $user_email_address, $from_status, $to_status, $mode='M', $comments='') {
	global $languages_id, $currencies;
	$readable_trans_id = '';

    //query for admin group
	$display_admin_group = tep_get_admin_group_name($user_email_address);
	if (tep_not_null($display_admin_group)) {
		$display_admin_group = ' ['.$display_admin_group.']';
	}

	$notification_field = $mode == 'M' ? 'status_configuration_manual_notification' : 'status_configuration_auto_notification';

	$notification_select_sql = "SELECT " . $notification_field . "
								FROM " . TABLE_STATUS_CONFIGURATION . "
								WHERE status_configuration_trans_type = '".tep_db_input($trans_type)."'
									AND status_configuration_source_status_id = '".tep_db_input($from_status)."'
									AND status_configuration_destination_status_id = '".tep_db_input($to_status)."'";
	$notification_result_sql = tep_db_query($notification_select_sql);

	if ($notification_row = tep_db_fetch_array($notification_result_sql)) {
		$notification_email_subject = '';
		
		$email_to_array = tep_parse_email_string($notification_row[$notification_field]);
		
		if (count($email_to_array)) {
			if ($trans_type == 'C') {
				$notification_email_subject = EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT;

				$orders_status_array = array();
				$orders_status_query = tep_db_query("SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY orders_status_sort_order");
				while ($orders_status = tep_db_fetch_array($orders_status_query)) {
					$orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
				}
				
				$cb_status_array = array('1' => 'WIN', '2' => 'LOST', '3' => 'RESOLVED');
				
				$order_info_select_sql = "	SELECT o.date_purchased, o.payment_methods_parent_id, o.payment_methods_id, o.customers_id, o.customers_email_address, o.customers_country, o.currency, o.currency_value, o.orders_cb_status 
											FROM " . TABLE_ORDERS  . " AS o
											WHERE o.orders_id = '" . tep_db_input($trans_id) . "'";
				$order_info_result_sql = tep_db_query($order_info_select_sql);
				$order_info_row = tep_db_fetch_array($order_info_result_sql);
				
				$cust_info_select_sql = "	SELECT cg.customers_groups_name 
											FROM " . TABLE_CUSTOMERS . " AS c 
											INNER JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg 
												ON c.customers_groups_id=cg.customers_groups_id 
											WHERE c.customers_id = '".tep_db_input($order_info_row['customers_id'])."'";
				$cust_info_result_sql = tep_db_query($cust_info_select_sql);
				$cust_info_row = tep_db_fetch_array($cust_info_result_sql);
				
				$order_amt_breakdown_str = '';
				$order_total_select_sql = "	SELECT title, text 
											FROM " . TABLE_ORDERS_TOTAL . " 
											WHERE orders_id = '" . tep_db_input($trans_id) . "' 
											ORDER BY sort_order";
				$order_total_result_sql = tep_db_query($order_total_select_sql);
				
		      	while ($order_total_row = tep_db_fetch_array($order_total_result_sql)) {
		        	if ($order_total_row['title'] == 'Store Credit:') {
		        		// Prepare query data for MS SC API
                        $scDataRequest = array(
                            'order_id' => $trans_id,
                            'activity' => 'P',
                        );
                        
                        // get all transaction related to orders_id and activity
                        if ((c2c_order::orderSiteID($trans_id) == 5)) {
                            // TODO: G2G code put here
                            $scArrayList = g2g_serverless::getScAllTransactions($scDataRequest);
                        } else {
                            $scArrayList = ms_store_credit::getScAllTransactions($scDataRequest);
                        }

                        foreach ($scArrayList as $scList) {
                            $order_amt_breakdown_str .= TEXT_ORDER_INFO_SC_TOTAL . ' ' . $currencies->format($sc_row['transaction_amount'], false, $sc_row['transaction_currency']) . "\n";
                        }
					} else {
						$order_amt_breakdown_str .= $order_total_row['title'] . ' ' . $order_total_row['text'] . "\n";
					}
		      	}
				
				$email_to_status_str = $orders_status_array[$to_status];
				
				if ($from_status == '8') {
					if ($to_status == '3') {
						$email_to_status_str .= ' ('.$cb_status_array[$order_info_row['orders_cb_status']].')';
					}
				}
				
				$display_pm_title = payment_methods::get_pm_display_title($order_info_row['payment_methods_id'], $order_info_row['payment_methods_parent_id']);
				
				$trans_status_update_notification_email = sprintf(EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT, $trans_id, $order_info_row['date_purchased'], $order_amt_breakdown_str, $display_pm_title, $order_info_row['customers_id'], $order_info_row['customers_email_address'], $cust_info_row['customers_groups_name'], $order_info_row['customers_country'], ($mode == 'M' ? EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION : EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION), $orders_status_array[$from_status], $email_to_status_str, date("Y-m-d H:i:s"), tep_get_ip_address(), $user_email_address . $display_admin_group, $comments);
			} else if ($trans_type == 'S') {
				$notification_email_subject = EMAIL_SUPPLIER_ORDER_UPDATE_NOTIFICATION_SUBJECT;
				
				$sup_order_status_select_sql = "SELECT supplier_list_status_id, supplier_list_status_name FROM " . TABLE_SUPPLIER_LIST_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY supplier_list_status_sort_order";
				$sup_order_status_result_sql = tep_db_query($sup_order_status_select_sql);
				while ($sup_order_status_row = tep_db_fetch_array($sup_order_status_result_sql)) {
					$orders_status_array[$sup_order_status_row["supplier_list_status_id"]] = $sup_order_status_row["supplier_list_status_name"];
				}
				
				$order_info_select_sql = "	SELECT supplier_order_lists_date, products_purchases_lists_name, currency, currency_value
											FROM " . TABLE_SUPPLIER_ORDER_LISTS  . " 
											WHERE supplier_order_lists_id = '" . tep_db_input($trans_id) . "'";
				$order_info_result_sql = tep_db_query($order_info_select_sql);
				$order_info_row = tep_db_fetch_array($order_info_result_sql);
				
				$payable_amount = supplier_order::get_order_total_payable_amount($trans_id);
				$payable_amount = $currencies->format($payable_amount, true, $order_info_row['currency'], $order_info_row['currency_value']);
				
				$trans_status_update_notification_email = sprintf(EMAIL_SUPPLIER_ORDER_UPDATE_NOTIFICATION_CONTENT, $trans_id, $order_info_row['supplier_order_lists_date'], $payable_amount, $order_info_row['products_purchases_lists_name'], ($mode == 'M' ? EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION : EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION), $orders_status_array[$from_status], $orders_status_array[$to_status], date("Y-m-d H:i:s"), tep_get_ip_address(), $user_email_address . $display_admin_group, $comments);
			} else if ($trans_type == 'B') {//Website and Supplier Buyback
				$notification_email_subject = EMAIL_BUYBACK_ORDER_UPDATE_NOTIFICATION_SUBJECT;
				
				$buyback_order_status_array = array();
				$buyback_order_status_query = tep_db_query("SELECT buyback_status_id, buyback_status_name FROM " . TABLE_BUYBACK_STATUS . " WHERE buyback_status_id IN ($from_status, $to_status) AND language_id = '" . (int)$languages_id . "'");
				while ($buyback_order_status = tep_db_fetch_array($buyback_order_status_query)) {
					$orders_status_array[$buyback_order_status['buyback_status_id']] = $buyback_order_status['buyback_status_name'];
				}
				
				$order_grp_select_sql = "SELECT brg.buyback_request_group_date, brg.buyback_request_group_site_id, brg.currency, brg.currency_value
										  FROM " . TABLE_BUYBACK_REQUEST_GROUP  . " AS brg 
										  WHERE brg.buyback_request_group_id = '" . tep_db_input($trans_id) . "'";
				$order_grp_result_sql = tep_db_query($order_grp_select_sql);
				if ($order_grp_row = tep_db_fetch_array($order_grp_result_sql)) {
					$payable_amount = 0;
					$buyback_products_select_sql = "SELECT buyback_unit_price, buyback_quantity_received, buyback_quantity_confirmed, buyback_request_quantity 
													FROM ".TABLE_BUYBACK_REQUEST." 
													WHERE buyback_request_group_id = '" . tep_db_input($trans_id) . "'";
					$buyback_products_result_sql = tep_db_query($buyback_products_select_sql);
					while ($buyback_products_row = tep_db_fetch_array($buyback_products_result_sql)) {
						// Wei Chen
						$unit_price = $buyback_products_row['buyback_unit_price'];
						
						if ($order_grp_row['buyback_request_group_site_id'] == 0) {	// For website buyback, assume confirm qty same as request qty
							$buyback_products_row['buyback_quantity_confirmed'] = $buyback_products_row['buyback_request_quantity'];
						}
						
						$received_quantity = (int)$buyback_products_row['buyback_quantity_received'];
						$actual_amount = $unit_price * ($received_quantity > $buyback_products_row['buyback_quantity_confirmed'] ? $buyback_products_row['buyback_quantity_confirmed'] : $received_quantity);
						$payable_amount += $actual_amount;
					}
					
					$payable_amount = $currencies->format($payable_amount, true, $order_grp_row['currency'], $order_grp_row['currency_value']);
					
					$trans_status_update_notification_email = sprintf(EMAIL_BUYBACK_ORDER_UPDATE_NOTIFICATION_CONTENT, 
																$trans_id,
																$order_grp_row['buyback_request_group_date'],
																$payable_amount,
																($mode == 'M' ? EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION : EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION),
																$orders_status_array[$from_status],
																$orders_status_array[$to_status],
																date("Y-m-d H:i:s"),
																tep_get_ip_address(),
																$user_email_address . $display_admin_group,
																$comments);
				}
			} else if ($trans_type == 'PO') {
				$notification_email_subject = EMAIL_PURCHASE_ORDER_UPDATE_NOTIFICATION_SUBJECT;
				
				$po_status_select_sql = "SELECT purchase_orders_status_id, purchase_orders_status_name FROM " . TABLE_PURCHASE_ORDERS_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY purchase_orders_status_sort_order";
				$po_status_result_sql = tep_db_query($po_status_select_sql);
				while ($po_status_row = tep_db_fetch_array($po_status_result_sql)) {
					$orders_status_array[$po_status_row["purchase_orders_status_id"]] = $po_status_row["purchase_orders_status_name"];
				}
				
				$orders_products_select_sql = "	SELECT po.purchase_orders_id, po.purchase_orders_ref_id, po.purchase_orders_issue_date 
												FROM " . TABLE_PURCHASE_ORDERS . " AS po 
												WHERE po.purchase_orders_id='" . (int)$trans_id . "'";
				$orders_products_result_sql = tep_db_query($orders_products_select_sql);
				$orders_products_row = tep_db_fetch_array($orders_products_result_sql);
				
				$readable_trans_id = $orders_products_row['purchase_orders_id'] . '-' . $orders_products_row['purchase_orders_ref_id'];
				
				$trans_status_update_notification_email = sprintf(EMAIL_PURCHASE_ORDER_UPDATE_NOTIFICATION_CONTENT, $readable_trans_id, $orders_products_row['purchase_orders_issue_date'], ($mode == 'M' ? EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION : EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION), $orders_status_array[$from_status], $orders_status_array[$to_status], date("Y-m-d H:i:s"), tep_get_ip_address(), $user_email_address . $display_admin_group, $comments);
			} else if ($trans_type == 'PWL') {
				$notification_email_subject = EMAIL_PWL_ORDER_UPDATE_NOTIFICATION_SUBJECT;
				
				$pwl_order_status_select_sql = "SELECT supplier_tasks_status_id, supplier_tasks_status_name FROM " . TABLE_SUPPLIER_TASKS_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY supplier_tasks_status_sort_order";
				$pwl_order_status_result_sql = tep_db_query($pwl_order_status_select_sql);
				while ($pwl_order_status_row = tep_db_fetch_array($pwl_order_status_result_sql)) {
					$orders_status_array[$pwl_order_status_row["supplier_tasks_status_id"]] = $pwl_order_status_row["supplier_tasks_status_name"];
				}
				
				$orders_products_select_sql = "	SELECT IF(op.orders_products_store_price IS NULL, (((op.final_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust), (((op.orders_products_store_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust)) AS payable_price, 
													ocp.orders_custom_products_number, o.orders_id, o.date_purchased, op.products_name, op.products_id, o.currency, o.currency_value 
												FROM " . TABLE_ORDERS . " AS o 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON (o.orders_id=op.orders_id AND op.custom_products_type_id = 1) 
												INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
													ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
												INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
													ON (op.orders_products_id=sta.orders_products_id) 
												WHERE sta.orders_products_id='" . (int)$trans_id . "'";
				$orders_products_result_sql = tep_db_query($orders_products_select_sql);
				$orders_products_row = tep_db_fetch_array($orders_products_result_sql);
				
				$readable_trans_id = $orders_products_row['orders_id'] . '-' . $orders_products_row['orders_custom_products_number'];
				$payable_amount = $currencies->format($orders_products_row['payable_price'], true, $orders_products_row['currency'], $orders_products_row['currency_value']);
				
				$trans_status_update_notification_email = sprintf(EMAIL_PWL_ORDER_UPDATE_NOTIFICATION_CONTENT, $readable_trans_id, $orders_products_row['date_purchased'], $payable_amount, ($mode == 'M' ? EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION : EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION), $orders_status_array[$from_status], $orders_status_array[$to_status], date("Y-m-d H:i:s"), tep_get_ip_address(), $user_email_address . $display_admin_group, $comments);
			} else if ($trans_type == 'CGRP') {
				$notification_email_subject = sprintf(EMAIL_CUSTOMER_GROUP_STATUS_SUBJECT, $trans_id);
				
				$customers_groups_name_array = array();
                $customer_name = '';
				$customers_status_select_sql = "	SELECT customers_groups_id, customers_groups_name  
													FROM " . TABLE_CUSTOMERS_GROUPS . " 
													WHERE customers_groups_id IN ('" . $from_status . "', '" . $to_status . "')";
				$customers_status_result_sql = tep_db_query ($customers_status_select_sql);
				while ($customers_status_row = tep_db_fetch_array ($customers_status_result_sql)) {
					$customers_groups_name_array[$customers_status_row['customers_groups_id']] = $customers_status_row['customers_groups_name'];
				}
                
				$customer_name_query = "	SELECT customers_firstname, customers_lastname 
                                            FROM " . TABLE_CUSTOMERS . " 
                                            WHERE customers_id = '".$trans_id."'";
                $customer_name_result = tep_db_query($customer_name_query);
                if($customer_name_row = tep_db_fetch_array($customer_name_result)) {
                    $customer_name = $customer_name_row['customers_firstname']." ".$customer_name_row['customers_lastname'];
                }

                $trans_status_update_notification_email = sprintf(EMAIL_CUSTOMER_GROUP_STATUS_CONTENT, ($mode == 'M' ? EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION : EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION), $trans_id, $customer_name, $customers_groups_name_array[$from_status], $customers_groups_name_array[$to_status], date("Y-m-d H:i:s"), tep_get_ip_address(), $user_email_address . $display_admin_group, $comments);
			}
		}

		if (tep_not_null($notification_email_subject)) {
			for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
				tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf($notification_email_subject, (tep_not_null($readable_trans_id) ? $readable_trans_id : $trans_id) ))), $trans_status_update_notification_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			}
		}
	}
}

function tep_customer_flag_update_notification($customer_id, $flag_id, $user_email_address, $mode='ON', $comments='') {
	/***************************************************
		$mode: 	ON  - Turn on the flag
				OFF - Turn off the flag
	***************************************************/
	$flag_notice_select_sql = "	SELECT user_flags_name, user_flags_on_notification, user_flags_off_notification
								FROM " . TABLE_USER_FLAGS . "
								WHERE user_flags_id = '" . tep_db_input($flag_id) . "'";
	$flag_notice_result_sql = tep_db_query($flag_notice_select_sql);
	if ($flag_notice_row = tep_db_fetch_array($flag_notice_result_sql)) {
		$customer_info_select_sql = "	SELECT cg.customers_groups_name 
										FROM " . TABLE_CUSTOMERS . " AS c 
										INNER JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg 
											ON c.customers_groups_id=cg.customers_groups_id 
										WHERE c.customers_id = '" . tep_db_input($customer_id) . "'";
		$customer_info_result_sql = tep_db_query($customer_info_select_sql);
		if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
			$email_to_array = tep_parse_email_string($mode == 'OFF' ? $flag_notice_row['user_flags_off_notification'] : $flag_notice_row['user_flags_on_notification']);
			
			$flag_email_contents = sprintf(EMAIL_CUSTOMER_FLAG_UPDATE_NOTIFICATION_CONTENT, $customer_id, $customer_info_row['customers_groups_name'], $mode, $flag_notice_row['user_flags_name'], date("Y-m-d H:i:s"), tep_get_ip_address(), $user_email_address, $comments);
			
			for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
				tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_CUSTOMER_FLAG_UPDATE_NOTIFICATION_SUBJECT, $customer_id) )), $flag_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			}
		}
	}
}

function tep_check_image_upload_permission($image_dir) {
	$image_configuration_check_permission_sql = "	SELECT user_groups
													FROM ". TABLE_IMAGE_CONFIGURATION ."
													WHERE FIND_IN_SET( '" . $_SESSION['login_groups_id'] . "', user_groups)
														AND image_category= '". tep_db_prepare_input($image_dir) ."'";
	$image_configuration_check_permission_result_sql = tep_db_query($image_configuration_check_permission_sql);
	if(tep_db_num_rows($image_configuration_check_permission_result_sql) > 0) {
		return true;
	} else {
		return false;
	}	
}
?>