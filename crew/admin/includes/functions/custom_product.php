<?php

include_once(DIR_WS_CLASSES . 'slack_notification.php');
include_once(DIR_WS_CLASSES . 'memcache.php');

function tep_get_product_type()
{
    $product_type_array = array();

    $product_type_select_sql = "SELECT custom_products_type_id, custom_products_type_name
                                FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . "
                                ORDER BY custom_products_type_id";
    $product_type_result_sql = tep_db_query($product_type_select_sql);
    while ($product_type_row = tep_db_fetch_array($product_type_result_sql)) {
        $product_type_array[] = array(
            'id' => $product_type_row['custom_products_type_id'],
            'text' => $product_type_row['custom_products_type_name']
        );
    }

    return $product_type_array;
}

function tep_get_product_child_type()
{
    $product_child_type_array = array();
    $memcache_obj = new OGM_Cache_MemCache();

    $cache_key = TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . '/cpt_child_list';
    $cache_result = $memcache_obj->fetch($cache_key);
    if ($cache_result !== false) {
        $product_child_type_array = $cache_result;
    } else {
        $product_child_type_select_sql = "SELECT custom_products_type_id, custom_products_type_child_id, custom_products_type_child_name
                                        FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
                                        ORDER BY sort_order";
        $product_child_type_result_sql = tep_db_query($product_child_type_select_sql);
        while ($product_child_type_row = tep_db_fetch_array($product_child_type_result_sql)) {
            $product_child_type_array[] = array(
                'id' => $product_child_type_row['custom_products_type_child_id'],
                'cpt_id' => $product_child_type_row['custom_products_type_id'],
                'text' => $product_child_type_row['custom_products_type_child_name']
            );
        }
        $memcache_obj->store($cache_key, $product_child_type_array, 86400);
    }

    return $product_child_type_array;
}

/**
 * Delivery and reverse delivery of CD Keys (Custom products type 2)
 */
function deliver_cdkey($products_id, $orders_products_id, $qty, $mode = 'deliver', $orders_custom_products_id = 0, $custom_products_code_id = 0, $message_stack = true)
{
    global $messageStack, $log_object, $login_email_address;

    //$affected_cd_key_array = array();

    switch ($mode) {
        case 'reverse_single':
            $cdkey_reverse_success_arr = array();

            //$affected_cd_key_array[] = $custom_products_code_id;

            $orders_products_select_sql = "	SELECT orders_id
                                                FROM " . TABLE_ORDERS_PRODUCTS . "
                                                WHERE orders_products_id = '" . (int)$orders_products_id . "'";
            $orders_products_result_sql = tep_db_query($orders_products_select_sql);
            if ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
                // Keep log
                $cdkey_log_select_sql = "	SELECT status_id AS custom_products_code_status_id, custom_products_code_viewed
                                                FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                                WHERE custom_products_code_id='" . tep_db_input($custom_products_code_id) . "'";
                $cdkey_old_log_result_sql = tep_db_query($cdkey_log_select_sql);
                $cdkey_old_log_row = tep_db_fetch_array($cdkey_old_log_result_sql);

                // Set CD Key to Available (if Not Viewed) or Disabled (if Viewed)
                $status_set_available_array = array();
                if ($cdkey_old_log_row['custom_products_code_viewed'] == '1') {
                    $status_set_available_array['status_id'] = '-1';
                } else {
                    $status_set_available_array['status_id'] = '1';
                }
                $status_set_available_array['orders_products_id'] = '0';
                $status_set_available_array['code_date_modified'] = 'now()';

                $status_set_available = tep_db_perform(TABLE_CUSTOM_PRODUCTS_CODE, $status_set_available_array, 'update', " custom_products_code_id = '" . (int)$custom_products_code_id . "'");

                // Keep log
                $cdkey_new_log_result_sql = tep_db_query($cdkey_log_select_sql);
                $cdkey_new_log_row = tep_db_fetch_array($cdkey_new_log_result_sql);

                $cdkey_changes_array = $log_object->detect_changes($cdkey_old_log_row, $cdkey_new_log_row);

                $cdkey_changes_formatted_array = $log_object->construct_log_message($cdkey_changes_array);

                if (count($cdkey_changes_formatted_array)) {
                    $changes_str = 'Changes made from Order# C' . $orders_products_row['orders_id'] . ":\n";
                    for ($logCnt = 0; $logCnt < count($cdkey_changes_formatted_array); $logCnt++) {
                        if (count($cdkey_changes_formatted_array[$logCnt])) {
                            foreach ($cdkey_changes_formatted_array[$logCnt] as $field => $res) {
                                if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                    $changes_str .= $res['text'] . "\n";
                                } else {
                                    $changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                }
                            }
                        }
                    }
                    $log_object->insert_cdkey_history_log('admin', $custom_products_code_id, $changes_str);
                }

                $orders_custom_products_select_sql = "SELECT orders_custom_products_value FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " WHERE orders_custom_products_id = '$orders_custom_products_id'";
                $orders_custom_products_result_sql = tep_db_query($orders_custom_products_select_sql);
                if ($orders_custom_products_row = tep_db_fetch_array($orders_custom_products_result_sql)) {
                    $ids_arr = explode(',', $orders_custom_products_row['orders_custom_products_value']);
                    $ids_modified_arr = array_diff($ids_arr, array($custom_products_code_id));
                    if (!count($ids_modified_arr)) {
                        //Single cdkey in this delivery.
                        $cdkey_already_delivered_delete_sql = 'DELETE FROM ' . TABLE_ORDERS_CUSTOM_PRODUCTS . " WHERE orders_products_id = '" . (int)$orders_products_id . "'";
                        $status_update_order = tep_db_query($cdkey_already_delivered_delete_sql);
                    } else {
                        //Multiple cdkeys in this delivery.
                        if (!empty($orders_products_id)) {
                            //Increase Group Concat Length to prevent cpc id truncated
                            tep_db_query('SET SESSION group_concat_max_len = 1000000000;');
                            $status_update_order = tep_db_query('UPDATE orders_custom_products ocp SET ocp.orders_custom_products_value = ( select group_concat(cpc.custom_products_code_id) from custom_products_code cpc where cpc.orders_products_id = ocp.orders_products_id ) where ocp.orders_products_id = "' . (int)$orders_products_id . '"');
                        } else {
                            $status_update_order = false;
                        }
                    }
                }
            }

            //return the successfully reversed keys
            if ($status_set_available && $status_update_order) {
                $retval = array($custom_products_code_id);

                tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . $orders_products_row['orders_id'] . "', 0, now(), '0', 'Release CD Key ID: " . $custom_products_code_id . "', 2, '" . tep_db_input($login_email_address) . "')");

                if ($message_stack == true) {
                    $messageStack->add_session(sprintf(SUCCESS_CDKEY_UNDELIVERED, $custom_products_code_id), 'success');
                }
            } else {
                if ($message_stack == true) {
                    $messageStack->add_session(sprintf(ERROR_CDKEY_UNDELIVERED, $custom_products_code_id), 'error');
                }
            }
            break;

        case 'deliver':
            $retval = [];
            $deliver_error = false;
            $cd_key_info_array = array();

            $orders_products_select_sql = "	SELECT orders_id, orders_products_id, products_quantity, products_delivered_quantity
                                                FROM " . TABLE_ORDERS_PRODUCTS . "
                                                WHERE orders_products_id = '" . (int)$orders_products_id . "'";
            $orders_products_result_sql = tep_db_query($orders_products_select_sql);
            if ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
                $delivered_qty = $orders_products_row['products_delivered_quantity'];
                $ordered_qty = $orders_products_row['products_quantity'];
                if (($delivered_qty + $qty) > $ordered_qty) {
                    $deliver_error = true;
                } else {
                    $orders_cp_value_select_sql = "SELECT custom_products_code_id FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " WHERE orders_products_id = '" . (int)$orders_products_id . "' AND status_id = 0";
                    $orders_cp_value_result_sql = tep_db_query($orders_cp_value_select_sql);
                    $delivered_qty = tep_db_num_rows($orders_cp_value_result_sql);
                    if ($delivered_qty > 0) {
                        if (($delivered_qty + $qty) > $ordered_qty) {
                            $deliver_error = true;
                        }
                    }

                    $lock_name = sha1('custom_products_code_delivery/' . $products_id);
                    $lock_query = tep_db_query("SELECT GET_LOCK('$lock_name',30)");

                    if ($lock_success = (tep_db_fetch_row($lock_query)[0] == '0')) {
                        $deliver_error = true;
                    }

                    if (!$deliver_error) {
                        // get available keys limited to quantity param. quantity might be less than requested.
                        // we'll just update a different delivered quantity in edit orders if that happens
                        $cdkey_available_arr = array();

                        $cdkey_available_select_sql = "	SELECT custom_products_code_id, status_id
                                                        FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                                        WHERE status_id > 0 
                                                            AND products_id = '" . (int)$products_id . "'
                                                            AND (orders_products_id = '" . (int)$orders_products_id . "'
                                                                OR orders_products_id = 0)
                                                        ORDER BY orders_products_id DESC, code_date_added ASC, custom_products_code_id ASC
                                                        LIMIT " . $qty;
                        $cdkey_available_result_sql = tep_db_query($cdkey_available_select_sql);
                        while ($cdkey_available = tep_db_fetch_array($cdkey_available_result_sql)) {
                            $cdkey_available_arr[] = $cdkey_available['custom_products_code_id'];
                            $cd_key_info_array[$cdkey_available['custom_products_code_id']] = array('status' => $cdkey_available['status_id']);
                            //$affected_cd_key_array[] = $cdkey_available['custom_products_code_id'];
                        }

                        //Demand greater than supply.
                        if ($qty > count($cdkey_available_arr)) {
                            if ($message_stack == true) {
                                $messageStack->add_session(WARNING_CDKEY_DELIVERY_STOCK_NOT_ENOUGH, 'error');
                            }
                        }

                        //assign the keys to sold
                        $id_str = "'" . implode("','", $cdkey_available_arr) . "'";

                        // Deliver at one time
                        tep_db_perform(TABLE_CUSTOM_PRODUCTS_CODE, array('status_id' => 0, 'orders_products_id' => $orders_products_row['orders_products_id'], 'code_date_modified' => 'now()'), 'update', " custom_products_code_id IN (" . $id_str . ")");

                        foreach ($cdkey_available_arr as $sell_cdkey_id) {
                            // Keep log
                            /*
                              $cdkey_log_select_sql = "	SELECT status_id AS custom_products_code_status_id
                              FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                              WHERE custom_products_code_id = '" . tep_db_input($sell_cdkey_id) . "'";
                              $cdkey_old_log_result_sql = tep_db_query($cdkey_log_select_sql);
                              $cdkey_old_log_row = tep_db_fetch_array($cdkey_old_log_result_sql);
                             */
                            $cdkey_old_log_row['custom_products_code_status_id'] = $cd_key_info_array[$sell_cdkey_id]['status'];

                            // Keep log
                            //$cdkey_new_log_result_sql = tep_db_query($cdkey_log_select_sql);
                            $cdkey_new_log_row['custom_products_code_status_id'] = 0;
                            //$cdkey_new_log_row = tep_db_fetch_array($cdkey_new_log_result_sql);

                            $cdkey_changes_array = $log_object->detect_changes($cdkey_old_log_row, $cdkey_new_log_row);

                            $cdkey_changes_formatted_array = $log_object->construct_log_message($cdkey_changes_array);

                            if (count($cdkey_changes_formatted_array)) {
                                $changes_str = 'Changes made from Order# C' . $orders_products_row['orders_id'] . ":\n";
                                for ($logCnt = 0; $logCnt < count($cdkey_changes_formatted_array); $logCnt++) {
                                    if (count($cdkey_changes_formatted_array[$logCnt])) {
                                        foreach ($cdkey_changes_formatted_array[$logCnt] as $field => $res) {
                                            if (isset($res['plain_result']) && $res['plain_result'] == '1') {
                                                $changes_str .= $res['text'] . "\n";
                                            } else {
                                                $changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
                                            }
                                        }
                                    }
                                }
                                $log_object->insert_cdkey_history_log('admin', $sell_cdkey_id, $changes_str);
                            }
                        }

                        //confirm keys are sold
                        $cdkey_confirmed_sold_arr = array();
                        $cdkey_confirmed_sold_select_sql = "SELECT custom_products_code_id
                                                            FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                                            WHERE custom_products_code_id IN (" . $id_str . ")
                                                                    AND status_id = '0' ";
                        $cdkey_confirmed_sold_result_sql = tep_db_query($cdkey_confirmed_sold_select_sql);
                        while ($cdkey_confirmed_sold = tep_db_fetch_array($cdkey_confirmed_sold_result_sql)) {
                            if ($message_stack == true) {
                                $messageStack->add_session(sprintf(SUCCESS_CDKEY_DELIVERED, $cdkey_confirmed_sold['custom_products_code_id']), 'success');
                            }

                            $cdkey_confirmed_sold_arr[] = $cdkey_confirmed_sold['custom_products_code_id'];
                        }

                        if (count($cdkey_confirmed_sold_arr)) {
                            //insert orders_custom_products based on whats now sold.
                            $orders_custom_products_select_sql = "SELECT orders_custom_products_value FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " WHERE orders_products_id = '" . (int)$orders_products_id . "'";
                            $orders_custom_products_result_sql = tep_db_query($orders_custom_products_select_sql);
                            if ($orders_custom_products_row = tep_db_fetch_array($orders_custom_products_result_sql)) {
                                //Increase Group Concat Length to prevent cpc id truncated
                                tep_db_query('SET SESSION group_concat_max_len = 1000000000;');
                                $status_update_order = tep_db_query('UPDATE orders_custom_products ocp SET ocp.orders_custom_products_value = ( select group_concat(cpc.custom_products_code_id) from custom_products_code cpc where cpc.orders_products_id = ocp.orders_products_id ) where ocp.orders_products_id = "' . (int)$orders_products_id . '"');
                            } else {
                                $data = array(
                                    'orders_custom_products_value' => implode(",", $cdkey_confirmed_sold_arr),
                                    'orders_products_id' => $orders_products_id,
                                    'products_id' => $products_id,
                                    'orders_custom_products_key' => 'cd_key_id'
                                );
                                tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $data);
                            }
                            //return the successfully delivered keys
                        }
                        $retval = $cdkey_confirmed_sold_arr;
                    }

                    if ($lock_success) {
                        tep_db_query("SELECT RELEASE_LOCK('$lock_name')");
                    }
                }
            }

            if ($deliver_error) {
                if ($message_stack == true) {
                    $messageStack->add_session(WARNING_CDKEY_OVER_DELIVERY, 'error');
                }
            }
    }

    return $retval;
}

/**
 * Send notification email on stock qty change for custom product.
 */
function tep_send_custom_product_stock_adjustment_email($email_notify_data_arr, $productID, $offset_str, $custom_product_type_id, $this_prod_cat_path)
{
    global $login_email_address;

    //Get the email receivers.
    $custom_product_notification_select_sql = 'SELECT custom_products_add_stock_email, custom_products_deduct_stock_email FROM ' . TABLE_CUSTOM_PRODUCTS_TYPE . ' WHERE custom_products_type_id=' . $custom_product_type_id;
    $custom_product_notification_result_sql = tep_db_query($custom_product_notification_select_sql);
    $custom_product_notification_row = tep_db_fetch_array($custom_product_notification_result_sql);

    $offset_int = intval($offset_str);
    if ($offset_int > 0) {
        //Addition
        $subject = $title = STOCK_CUSTPROD_ADD_EMAIL_TEXT_SUBJECT;

        $cat_cfg_array = tep_get_cfg_setting((int)$productID, 'product', $custom_product_notification_row['custom_products_add_stock_email']);
        $email_to_array = tep_parse_email_string($cat_cfg_array[$custom_product_notification_row['custom_products_add_stock_email']]);
    } elseif ($offset_int < 0) {
        //Deduction
        $subject = $title = STOCK_CUSTPROD_DEDUCT_EMAIL_TEXT_SUBJECT;

        $cat_cfg_array = tep_get_cfg_setting((int)$productID, 'product', $custom_product_notification_row['custom_products_deduct_stock_email']);
        $email_to_array = tep_parse_email_string($cat_cfg_array[$custom_product_notification_row['custom_products_deduct_stock_email']]);
    }

    if (!$email_to_array && $offset_int < 0) {
        //Ignore email for addition if not set in custom_products_type table.
        $cat_cfg_array = tep_get_cfg_setting((int)$productID, 'product', 'MANUAL_STOCK_DEDUCTION_EMAIL');
        $email_to_array = tep_parse_email_string($cat_cfg_array['MANUAL_STOCK_DEDUCTION_EMAIL']);
    }

    //Construct the email summary
    if ($affected_cnt = count($email_notify_data_arr["filename"])) {
        $diff_prod_actual_qty = ($offset_int * $affected_cnt);
    } else {
        $diff_prod_actual_qty = $offset_int;
    }

    $email_str = sprintf(STOCK_CUSTPROD_EMAIL_TEXT_ADJUSTMENT_START, $title, tep_get_products_name((int)$productID) . ' (' . (int)$productID . ')', $email_notify_data_arr['action_type'], date("Y-m-d H:i:s"), $this_prod_cat_path, $email_notify_data_arr['old_prod_actual_qty'], $email_notify_data_arr['new_prod_actual_qty'], $diff_prod_actual_qty, $login_email_address, getenv("REMOTE_ADDR"), $email_notify_data_arr['comments']);

    //Construct the email detail.
    if ($affected_cnt) {
        $cnt = 0;
        ksort($email_notify_data_arr['filename']);

        foreach ($email_notify_data_arr['filename'] as $custom_product_id => $filename) {
            $cnt++;
            $email_str .= sprintf(STOCK_CUSTPROD_EMAIL_TEXT_ADJUSTMENT_FILES_BODY, $cnt, $custom_product_id, $filename);
        }
    }

    $slack = new slack_notification();
    $data = json_encode(array(
        'text' => $subject,
        'attachments' => array(
            array(
                'color' => 'warning',
                'text' => $email_str
            )
        )
    ));
    $slack->send(SLACK_WEBHOOK_INV_PRODUCT_STOCK, $data);
}

function local_get_pwl_key(&$var, $key, $prefix)
{
    $var = $prefix . $var;
}

function tep_get_custom_product_info_option_class($order_products_id)
{
    $custom_product_info_option_class_array = array();
    $orders_custom_products_key_array = array();

    $custom_product_info_option_class_type = array('account_username' => ENTRY_ACCOUNT_USERNAME, 'account_password' => ENTRY_ACCOUNT_PASSWORD, 'character_name' => ENTRY_CHAR_NAME, 'realm' => ENTRY_REALM, 'faction' => ENTRY_FACTION, 'game' => ENTRY_GAME);

    $pwl_key = array_keys($custom_product_info_option_class_type);
    array_walk($pwl_key, 'local_get_pwl_key', 'power_leveling_account_');

    $account_info_select_sql = "	SELECT orders_custom_products_value, orders_custom_products_key
                                        FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . "
                                        WHERE orders_products_id ='" . (int)$order_products_id . "' AND orders_custom_products_key IN ('" . implode("', '", $pwl_key) . "')";

    $account_info_result_sql = tep_db_query($account_info_select_sql);

    while ($account_info_row = tep_db_fetch_array($account_info_result_sql)) {
        $keys_array = array();
        $cp_opt_class_type_key = substr($account_info_row['orders_custom_products_key'], strlen('power_leveling_account_'));
        if (isset($custom_product_info_option_class_type[$cp_opt_class_type_key])) {
            $custom_product_info_option_class_array[$cp_opt_class_type_key] = array('label' => $custom_product_info_option_class_type[$cp_opt_class_type_key], 'value' => $account_info_row['orders_custom_products_value']);
        }
    }

    return $custom_product_info_option_class_array;
}

function tep_get_profiler_link($order_products_id)
{
    $profiler_url = '';

    $game_char_id_select_sql = "SELECT game_char_id FROM " . TABLE_GAME_CHAR . " WHERE orders_products_id ='" . (int)$order_products_id . "'";
    $game_char_id_result_sql = tep_db_query($game_char_id_select_sql);
    $game_char_id_row = tep_db_fetch_array($game_char_id_result_sql);

    if (tep_not_null($game_char_id_row['game_char_id_row']) || $game_char_id_row['game_char_id'] != 0) {
        $profiler_url = '<a href="' . tep_href_link(FILENAME_PROGRESS_REPORT, 'action=char_info&game_char_id=' . $game_char_id_row['game_char_id']) . '">' . LINK_PROFILER . '</a>';
    }

    return $profiler_url;
}

function tep_get_custom_product_info($order_products_id)
{
    $custom_products_info_select_sql = "	SELECT op.products_id, ocp.orders_custom_products_number
                                                FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                                                LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp
                                                        ON (op.products_id = ocp.products_id AND op.orders_products_id = ocp.orders_products_id)
                                                WHERE op.orders_products_id ='" . tep_db_input($order_products_id) . "'";

    $custom_products_info_result_sql = tep_db_query($custom_products_info_select_sql);
    $custom_products_info_row = tep_db_fetch_array($custom_products_info_result_sql);

    $custom_product_info_array = array('products_id' => $custom_products_info_row['products_id'], 'orders_custom_products_number' => $custom_products_info_row['orders_custom_products_number']);

    return $custom_product_info_array;
}

function tep_unassign_custom_product($order_products_id, $login_email_address)
{
    $supplier_tasks_status_select_sql = " SELECT supplier_tasks_status FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id ='" . (int)$order_products_id . "'";
    $supplier_tasks_status_result_sql = tep_db_query($supplier_tasks_status_select_sql);
    $supplier_tasks_status_row = tep_db_fetch_array($supplier_tasks_status_result_sql);

    if ($supplier_tasks_status_row['supplier_tasks_status'] == 0) {
        $unassign_sql_data_array = array('suppliers_id' => 0);

        tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION, $unassign_sql_data_array, 'update', "orders_products_id = '" . (int)$order_products_id . "'");

        $unassign_update_array = array(
            'orders_products_id' => (int)$order_products_id,
            'supplier_tasks_status' => 'NULL',
            'date_added' => 'now()',
            'comments' => TEXT_NOT_ASSIGN_TO_SUPPLIER,
            'changed_by' => $login_email_address,
            'user_role' => 'admin',
            'notify_recipient' => 0,
            'supplier_tasks_allocation_history_show' => 0
        );

        tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $unassign_update_array);

        $unassign_append_str = "\n" . str_repeat('-', 20) . "\n" . date('Y-m-d H:i:s') . ' (by ' . $login_email_address . ")\nSupplier Assigned: " . TEXT_NOT_ASSIGN_TO_SUPPLIER;

        $custom_info_update_sql = "UPDATE " . TABLE_ORDERS_CUSTOM_PRODUCTS . " SET orders_custom_products_value = CONCAT(orders_custom_products_value, '" . tep_db_input($unassign_append_str) . "') WHERE orders_products_id ='" . (int)$order_products_id . "' AND orders_custom_products_key = 'power_leveling_info'";
        tep_db_query($custom_info_update_sql);
    }
}

// PWL Customising Page
function tep_get_bracket_range($id)
{
    $start_level = $end_level = 0;
    $start_level_alias = '';
    $range = array();
    $range_mode = 'continuos';

    $start_level_select_sql = "SELECT brackets_value FROM " . TABLE_BRACKETS . " WHERE data_pool_level_id ='" . $id . "' AND brackets_key='" . KEY_PL_START_LEVEL . "'";
    $start_level_result_sql = tep_db_query($start_level_select_sql);
    if ($start_level_row = tep_db_fetch_array($start_level_result_sql)) {
        $start_level = $start_level_row['brackets_value'];
    }

    $start_level_alias_select_sql = "SELECT brackets_value FROM " . TABLE_BRACKETS . " WHERE data_pool_level_id ='" . $id . "' AND brackets_key='" . KEY_PL_START_LEVEL_ALIAS . "'";
    $start_level_alias_result_sql = tep_db_query($start_level_alias_select_sql);
    if ($start_level_alias_row = tep_db_fetch_array($start_level_alias_result_sql)) {
        $start_level_alias = $start_level_alias_row['brackets_value'];
        if (trim($start_level_alias) != '' && !is_numeric($start_level_alias)) {
            $range_mode = 'discrete';
        }
    }

    $level_select_sql = "	SELECT CAST(b1.brackets_value as SIGNED) AS level_val, b2.brackets_value AS alias, b3.brackets_value AS value, b4.brackets_value AS interval_val
                                FROM " . TABLE_BRACKETS . " AS b1, " . TABLE_BRACKETS . " AS b2, " . TABLE_BRACKETS . " AS b3, " . TABLE_BRACKETS . " AS b4
                                WHERE b1.brackets_dependent=b2.brackets_dependent
                                        AND b2.brackets_dependent=b3.brackets_dependent
                                        AND b3.brackets_dependent=b4.brackets_dependent
                                        AND b1.data_pool_level_id ='" . tep_db_input($id) . "'
                                        AND b1.brackets_key='" . KEY_PL_END_LEVEL . "'
                                        AND b2.brackets_key='" . KEY_PL_LEVEL_ALIAS . "'
                                        AND b3.brackets_key='" . KEY_PL_VALUE . "'
                                        AND b4.brackets_key='" . KEY_PL_INTERVAL . "'
                                ORDER BY level_val";
    $level_result_sql = tep_db_query($level_select_sql);
    while ($level_row = tep_db_fetch_array($level_result_sql)) {
        $range[] = array('level' => $level_row['level_val'], 'alias' => $level_row['alias'], 'value' => $level_row['value'], 'interval' => $level_row['interval_val']);

        if (isset($level_row['alias']) && trim($level_row['alias']) != '' && !is_numeric($level_row['alias'])) {
            $range_mode = 'discrete';
        }
    }

    $select_bracket_cfg_select_sql = "SELECT brackets_key, brackets_value FROM " . TABLE_BRACKETS . " WHERE data_pool_level_id = '" . (int)$id . "' AND brackets_dependent = 0";
    $select_bracket_cfg_result_sql = tep_db_query($select_bracket_cfg_select_sql);

    while ($select_bracket_row = tep_db_fetch_array($select_bracket_cfg_result_sql)) {
        $bracket_cfg[$select_bracket_row['brackets_key']] = $select_bracket_row['brackets_value'];
    }

    return array('start' => array('level' => $start_level, 'alias' => $start_level_alias), 'range' => $range, 'bracket_cfg' => $bracket_cfg, 'mode' => $range_mode);
}

function tep_get_start_level($start_from_array, $end_levels, $range_mode)
{
    $start_from = $start_from_array['level'];
    $start_from_alias = $start_from_array['alias'];

    $start_level_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));

    $start_level_array[] = array('id' => $start_from, 'text' => trim($start_from_alias) != '' ? $start_from_alias : $start_from);

    for ($i = 0; $i < count($end_levels); $i++) {
        if ($range_mode == 'continuos') {
            $from_point = ($i == 0) ? $start_from : $end_levels[$i - 1]['level'];
            for ($linking = $from_point + 1; $linking < $end_levels[$i]['level']; $linking++) {
                $start_level_array[] = array('id' => $linking, 'text' => $linking);
            }
        }
        if ($i < (count($end_levels) - 1)) { //	Do not include the last level
            $start_level_array[] = array('id' => $end_levels[$i]['level'], 'text' => (isset($end_levels[$i]['alias']) && $end_levels[$i]['alias'] != '' ? $end_levels[$i]['alias'] : $end_levels[$i]['level']));
        }
    }

    return $start_level_array;
}

function tep_get_bracket_value($data_pool_level_id, $tag = KEY_PL_START_LEVEL)
{
    $id = (int)$data_pool_level_id;

    $bracket_value_select_sql = "SELECT brackets_value FROM " . TABLE_BRACKETS . " WHERE data_pool_level_id ='" . $id . "' AND brackets_key ='" . $tag . "'";
    $bracket_value_result_sql = tep_db_query($bracket_value_select_sql);
    $bracket_value_row = tep_db_fetch_array($bracket_value_result_sql);
    return $bracket_value_row['brackets_value'];
}

function tep_get_level_alias($level_id, $bracket_selection_id)
{
    $level_select_sql = "	SELECT b2.brackets_value AS alias
                                FROM " . TABLE_BRACKETS . " AS b1, " . TABLE_BRACKETS . " AS b2
                                WHERE b1.brackets_dependent=b2.brackets_dependent AND b1.data_pool_level_id ='" . tep_db_input($level_id) . "' AND b1.brackets_key='" . KEY_PL_END_LEVEL . "' AND b1.brackets_value ='" . $bracket_selection_id . "' AND b2.brackets_key='" . KEY_PL_LEVEL_ALIAS . "' ";
    $level_result_sql = tep_db_query($level_select_sql);
    if (tep_db_num_rows($level_result_sql)) {
        $level_row = tep_db_fetch_array($level_result_sql);
        if (trim($level_row["alias"]) != '') {
            return $level_row["alias"];
        } else {
            return $bracket_selection_id;
        }
    } else {
        // Might be the start level. So need to grab its alias if any
        $start_level_select_sql = "	SELECT b2.brackets_value AS alias
                                        FROM " . TABLE_BRACKETS . " AS b1, " . TABLE_BRACKETS . " AS b2
                                        WHERE b1.data_pool_level_id=b2.data_pool_level_id
                                                AND b1.data_pool_level_id ='" . tep_db_input($level_id) . "'
                                                AND b1.brackets_key='" . KEY_PL_START_LEVEL . "'
                                                AND b1.brackets_value ='" . tep_db_input($bracket_selection_id) . "'
                                                AND b2.brackets_key='" . KEY_PL_START_LEVEL_ALIAS . "' ";
        $start_level_result_sql = tep_db_query($start_level_select_sql);
        $start_level_row = tep_db_fetch_array($start_level_result_sql);
        if (trim($start_level_row["alias"]) != '') {
            return $start_level_row["alias"];
        } else {
            return $bracket_selection_id;
        }
    }
}

function tep_get_options_html($products_id, $end_level)
{
    $option_ctrl_array = array();
    $distinct_options_select_sql = "SELECT DISTINCT dpo.data_pool_options_id
                                    FROM " . TABLE_DATA_POOL_OPTIONS . " AS dpo
                                    INNER JOIN " . TABLE_DATA_POOL_OPTIONS_VALUES . " AS dpov
                                            ON dpo.data_pool_options_id=dpov.data_pool_options_id
                                    WHERE dpo.products_id ='" . $products_id . "'
                                            AND ( 	(dpov.data_pool_options_values_min_level <= $end_level && dpov.data_pool_options_values_max_level >= $end_level) OR
                                                            (dpov.data_pool_options_values_min_level = 0 && dpov.data_pool_options_values_max_level = 0)
                                                    )
                                    ORDER BY dpo.data_pool_options_sort_order";
    $distinct_options_result_sql = tep_db_query($distinct_options_select_sql);
    while ($distinct_options_row = tep_db_fetch_array($distinct_options_result_sql)) {
        $options_select_sql = "	SELECT data_pool_options_title, data_pool_options_class, data_pool_options_input_type, data_pool_options_input_size, data_pool_options_required, data_pool_options_show_supplier
								FROM " . TABLE_DATA_POOL_OPTIONS . " 
								WHERE data_pool_options_id ='" . $distinct_options_row["data_pool_options_id"] . "'";
        $options_result_sql = tep_db_query($options_select_sql);
        $options_row = tep_db_fetch_array($options_result_sql);
        $option_ctrl_array[$distinct_options_row["data_pool_options_id"]] = $options_row;
        $option_ctrl_array[$distinct_options_row["data_pool_options_id"]]['values'] = array();

        $options_values_select_sql = "	SELECT *
                                        FROM " . TABLE_DATA_POOL_OPTIONS_VALUES . "
                                        WHERE data_pool_options_id ='" . $distinct_options_row["data_pool_options_id"] . "'
                                                AND ( 	(data_pool_options_values_min_level <= $end_level && data_pool_options_values_max_level >= $end_level) OR
                                                                (data_pool_options_values_min_level = 0 && data_pool_options_values_max_level = 0)
                                                        )
                                        ORDER BY data_pool_options_values_sort_order, data_pool_options_values_id";
        $options_values_result_sql = tep_db_query($options_values_select_sql);
        while ($options_values_row = tep_db_fetch_array($options_values_result_sql)) {
            $option_ctrl_array[$distinct_options_row["data_pool_options_id"]]['values'][] = $options_values_row;
        }
    }

    return $option_ctrl_array;
}

function tep_preload_options_js($products_id)
{
    $required_fields = array();

    $preload_price_eta_js = "\nvar cp_option_price = new Array();\n" .
        "var cp_option_eta = new Array();\n";

    $options_select_sql = "	SELECT data_pool_options_id, data_pool_options_input_type, data_pool_options_title, data_pool_options_required
                                FROM " . TABLE_DATA_POOL_OPTIONS . "
                                WHERE products_id ='" . (int)$products_id . "'
                                ORDER BY data_pool_options_sort_order";
    $options_result_sql = tep_db_query($options_select_sql);
    while ($options_row = tep_db_fetch_array($options_result_sql)) {
        $option_id = $options_row["data_pool_options_id"];

        if ($options_row["data_pool_options_required"] == 1) {
            $required_fields[$option_id] = array("type" => $options_row["data_pool_options_input_type"], "title" => strip_tags(preg_replace("/(<([\w]+)[^>]*>)(.*)(<\/\\2>)/", '', $options_row["data_pool_options_title"])));
        }

        $option_ctrl_array = array();

        $options_values_select_sql = "	SELECT data_pool_options_values_id, data_pool_options_values_price, data_pool_options_values_eta
                                        FROM " . TABLE_DATA_POOL_OPTIONS_VALUES . "
                                        WHERE data_pool_options_id ='" . $option_id . "' 
                                        ORDER BY data_pool_options_values_sort_order, data_pool_options_values_id";
        $options_values_result_sql = tep_db_query($options_values_select_sql);
        while ($options_values_row = tep_db_fetch_array($options_values_result_sql)) {
            $option_ctrl_array[] = $options_values_row;
        }

        $js_html = '';
        switch ($options_row["data_pool_options_input_type"]) {
            case "1": // Text Box
            case "2": // Text Area
                if ($option_ctrl_array[0]["data_pool_options_values_price"] > 0 || $option_ctrl_array[0]["data_pool_options_values_eta"] > 0) {
                    $js_html = "cp_option_price[" . $option_id . "] = new Array();\n" .
                        "cp_option_eta[" . $option_id . "] = new Array();\n" .
                        "cp_option_price[" . $option_id . "][0] = '" . $option_ctrl_array[0]["data_pool_options_values_price"] . "';\n" .
                        "cp_option_eta[" . $option_id . "][0] = '" . $option_ctrl_array[0]["data_pool_options_values_eta"] . "';\n";
                }
                $preload_price_eta_js .= $js_html;
                break;
            case "3": // Dropdown Menu
            case "4": // Radio Button
                if (count($option_ctrl_array)) {
                    foreach ($option_ctrl_array as $selection) {
                        if ($selection["data_pool_options_values_price"] > 0 || $selection["data_pool_options_values_eta"] > 0) {
                            $js_html .= "cp_option_price[" . $option_id . "][" . $selection["data_pool_options_values_id"] . "] = '" . $selection["data_pool_options_values_price"] . "';\n" .
                                "cp_option_eta[" . $option_id . "][" . $selection["data_pool_options_values_id"] . "] = '" . $selection["data_pool_options_values_eta"] . "';\n";
                        }
                    }

                    if (tep_not_null($js_html)) {
                        $js_html = "cp_option_price[" . $option_id . "] = new Array();\n" .
                            "cp_option_eta[" . $option_id . "] = new Array();\n" .
                            $js_html;
                    }
                    $preload_price_eta_js .= $js_html;
                }

                break;
            case "5":
            case "7":
                // Currently no price and eta assigned from admin site
                break;
        }
    }

    if (count($required_fields)) {
        $preload_price_eta_js .= "\n function options_validation() {\n" .
            "		var err_str = '';\n";
        foreach ($required_fields as $id => $info) {
            switch ($info["type"]) {
                case "1":
                case "2":
                    $preload_price_eta_js .= '	if (document.getElementById(\'cp_option_' . $id . '\') != null) {' . "\n" .
                        '		if (trim_str(document.getElementById(\'cp_option_' . $id . '\').value) == "") {' . "\n" .
                        '			err_str += \'* Please provide \"' . $info["title"] . '\" infomation!\'+' . '"\n"' . ";\n" .
                        '		}' . "\n" .
                        '	}' . "\n";
                    break;
                case "3":
                    $preload_price_eta_js .= '	if (document.getElementById(\'cp_option_' . $id . '\') != null) {' . "\n" .
                        '		if (document.getElementById(\'cp_option_' . $id . '\').selectedIndex < 1) {' . "\n" .
                        '			err_str += \'* Please provide \"' . $info["title"] . '\" infomation!\'+' . '"\n"' . ";\n" .
                        '		}' . "\n" .
                        '	}' . "\n";
                    break;
                case "4":
                    $preload_price_eta_js .= '	if (document.getElementById(\'cp_option_' . $id . '\') != null) {' . "\n" .
                        '		if (!checkForSelection("mainForm", "cp_option[' . $id . ']")) {' . "\n" .
                        '			err_str += \'* Please provide \"' . $info["title"] . '\" infomation!\'+' . '"\n"' . ";\n" .
                        '		}' . "\n" .
                        '	}' . "\n";
                    break;
                case "5": // 	Date Selection - no need validate since all options in the box are valid
                case "7": //	Information Text
                    break;
            }
        }
        $preload_price_eta_js .= " 	if (trim_str(err_str) != '') {\n" .
            "		return err_str;\n" .
            " 	} else { return true; }\n" .
            "} \n";
    }

    return $preload_price_eta_js;
}

function tep_draw_option_field($option_id, $option_res, $default = '')
{
    global $currencies;

    $field_name = 'cp_option[' . $option_id . ']';
    $field_id = 'cp_option_' . $option_id;
    $required = $option_res["data_pool_options_required"] == 1 ? true : false;
    switch ($option_res["data_pool_options_input_type"]) {
        case "1": // Text Box
            $option_price_eta_label = array();
            if (tep_not_null($option_res["data_pool_options_input_size"])) {
                list($size, $max_len) = explode(',', $option_res["data_pool_options_input_size"]);
            }

            if ($option_res["values"][0]["data_pool_options_values_price"] > 0 || $option_res["values"][0]["data_pool_options_values_eta"] > 0) {
                $onBlurEvent = ' onBlur="update_option_price_and_eat(this, \'' . $option_id . '\');" ';
                $js_html = "cp_option_price[" . $option_id . "] = new Array();\n" .
                    "cp_option_eta[" . $option_id . "] = new Array();\n" .
                    "cp_option_price[" . $option_id . "][0] = '" . $option_res["values"][0]["data_pool_options_values_price"] . "';\n" .
                    "cp_option_eta[" . $option_id . "][0] = '" . $option_res["values"][0]["data_pool_options_values_eta"] . "';\n";

                if ($option_res["values"][0]["data_pool_options_values_price"] > 0) {
                    $option_price_eta_label[] = '+' . $currencies->format($option_res["values"][0]["data_pool_options_values_price"]);
                }
                if ($option_res["values"][0]["data_pool_options_values_eta"] > 0) {
                    $option_price_eta_label[] = '+' . tep_get_option_eta_string($option_res["values"][0]["data_pool_options_values_eta"]);
                }
            }

            $param = ' id="' . $field_id . '" SIZE=' . ($size > 0 ? $size : '20') . (isset($max_len) && $max_len > 0 ? " MAXLENGTH=" . $max_len : '') . $onBlurEvent;

            return array('field' => tep_draw_input_field($field_name, $default, $param) . (count($option_price_eta_label) ? '&nbsp;[' . implode('; ', $option_price_eta_label) . ']' : '') . ($required ? TEXT_FIELD_REQUIRED : '') . (count($option_price_eta_label) ? '<noscript>&nbsp;' . tep_image_submit(THEMA . 'button_update.gif', IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"') . '</noscript>' : ''), 'js' => $js_html);

            break;
        case "2": // Text Area
            $option_price_eta_label = array();
            if (tep_not_null($option_res["data_pool_options_input_size"])) {
                list($row_val, $col_val) = explode(',', $option_res["data_pool_options_input_size"]);
            }

            if ($option_res["values"][0]["data_pool_options_values_price"] > 0 || $option_res["values"][0]["data_pool_options_values_eta"] > 0) {
                $onBlurEvent = ' onBlur="update_option_price_and_eat(this, \'' . $option_id . '\');" ';
                $js_html = "cp_option_price[" . $option_id . "] = new Array();\n" .
                    "cp_option_eta[" . $option_id . "] = new Array();\n" .
                    "cp_option_price[" . $option_id . "][0] = '" . $option_res["values"][0]["data_pool_options_values_price"] . "';\n" .
                    "cp_option_eta[" . $option_id . "][0] = '" . $option_res["values"][0]["data_pool_options_values_eta"] . "';\n";

                if ($option_res["values"][0]["data_pool_options_values_price"] > 0) {
                    $option_price_eta_label[] = '+' . $currencies->format($option_res["values"][0]["data_pool_options_values_price"]);
                }
                if ($option_res["values"][0]["data_pool_options_values_eta"] > 0) {
                    $option_price_eta_label[] = '+' . tep_get_option_eta_string($option_res["values"][0]["data_pool_options_values_eta"]);
                }
            }

            $param = ' id="' . $field_id . '" ' . $onBlurEvent;

            $field_html = '<div style="float: left;">' . tep_draw_textarea_field($field_name, "soft", $col_val, $row_val, $default, $param, false) . '</div>' .
                '<div>' . (count($option_price_eta_label) ? '&nbsp;[' . implode('; ', $option_price_eta_label) . ']<br>' : '') . ($required ? TEXT_FIELD_REQUIRED : '&nbsp;') .
                (count($option_price_eta_label) ? '<noscript>&nbsp;' . tep_image_submit(THEMA . 'button_update.gif', IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"') . '</noscript>' : '') . '</div>';
            return array('field' => $field_html, 'js' => $js_html);

            break;
        case "3": // Dropdown Menu
            if (count($option_res["values"])) {
                $js_html = '';
                $update_on_change = false;
                $selection_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
                foreach ($option_res["values"] as $selection) {
                    $option_price_eta_label = array();

                    if ($selection["data_pool_options_values_price"] > 0 || $selection["data_pool_options_values_eta"] > 0) {
                        $update_on_change = true;

                        $js_html .= "cp_option_price[" . $option_id . "][" . $selection["data_pool_options_values_id"] . "] = '" . $selection["data_pool_options_values_price"] . "';\n" .
                            "cp_option_eta[" . $option_id . "][" . $selection["data_pool_options_values_id"] . "] = '" . $selection["data_pool_options_values_eta"] . "';\n";

                        if ($selection["data_pool_options_values_price"] > 0) {
                            $option_price_eta_label[] = '+' . $currencies->format($selection["data_pool_options_values_price"]);
                        }
                        if ($selection["data_pool_options_values_eta"] > 0) {
                            $option_price_eta_label[] = '+' . tep_get_option_eta_string($selection["data_pool_options_values_eta"]);
                        }
                    }

                    $selection_array[] = array('id' => $selection["data_pool_options_values_id"], 'text' => $selection["data_pool_options_value"] . (count($option_price_eta_label) ? ' [' . implode('; ', $option_price_eta_label) . ']' : ''));
                }

                if ($update_on_change) {
                    $js_html = "cp_option_price[" . $option_id . "] = new Array();\n" .
                        "cp_option_eta[" . $option_id . "] = new Array();\n" .
                        $js_html;
                    $onChangeEvent = ' onChange="update_option_price_and_eat(this, \'' . $option_id . '\');" ';
                }

                $param = ' id="' . $field_id . '" ' . $onChangeEvent;

                return array('field' => tep_draw_pull_down_menu($field_name, $selection_array, $default, $param) . ($required ? TEXT_FIELD_REQUIRED : '') . ($update_on_change ? '<noscript>&nbsp;' . tep_image_submit(THEMA . 'button_update.gif', IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"') . '</noscript>' : ''), 'js' => $js_html);
            }

            break;
        case "4": // Radio Button
            if (count($option_res["values"])) {
                $js_html = '';
                $update_on_change = false;
                $field_html = '';
                foreach ($option_res["values"] as $selection) {
                    $option_price_eta_label = array();

                    if ($selection["data_pool_options_values_price"] > 0 || $selection["data_pool_options_values_eta"] > 0) {
                        $update_on_change = true;

                        $js_html .= "cp_option_price[" . $option_id . "][" . $selection["data_pool_options_values_id"] . "] = '" . $selection["data_pool_options_values_price"] . "';\n" .
                            "cp_option_eta[" . $option_id . "][" . $selection["data_pool_options_values_id"] . "] = '" . $selection["data_pool_options_values_eta"] . "';\n";

                        if ($selection["data_pool_options_values_price"] > 0) {
                            $option_price_eta_label[] = '+' . $currencies->format($selection["data_pool_options_values_price"]);
                        }
                        if ($selection["data_pool_options_values_eta"] > 0) {
                            $option_price_eta_label[] = '+' . tep_get_option_eta_string($selection["data_pool_options_values_eta"]);
                        }
                    }

                    $field_html .= tep_draw_radio_field($field_name, $selection["data_pool_options_values_id"], ($selection["data_pool_options_values_id"] == $default ? true : false), '##POST_PARAM##') . '&nbsp;' . $selection["data_pool_options_value"] . (count($option_price_eta_label) ? '&nbsp;[' . implode('; ', $option_price_eta_label) . ']' : '') . '<br>';
                }

                if (tep_not_null($field_html)) {
                    if ($update_on_change) {
                        $js_html = "cp_option_price[" . $option_id . "] = new Array();\n" .
                            "cp_option_eta[" . $option_id . "] = new Array();\n" .
                            $js_html;
                        $onClickEvent = ' onClick="update_option_price_and_eat(this, \'' . $option_id . '\');" ';
                        $param = ' id="' . $field_id . '" ' . $onClickEvent;

                        $field_html = str_replace('##POST_PARAM##', $param, $field_html);
                    } else {
                        $param = ' id="' . $field_id . '" ';
                        $field_html = str_replace('##POST_PARAM##', $param, $field_html);
                    }

                    if ($required) {
                        $field_html .= TEXT_FIELD_REQUIRED;
                    }

                    if ($update_on_change) {
                        $field_html .= '<noscript>&nbsp;' . tep_image_submit(THEMA . 'button_update.gif', IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"') . '</noscript>';
                    }
                }
                return array('field' => $field_html, 'js' => $js_html);
            }

            break;
        case "5": // Date Selection
            if (tep_not_null($option_res["data_pool_options_input_size"])) {
                list($from_date, $period) = explode(',', $option_res["data_pool_options_input_size"]);
                if ($from_date == 'TODAY') {
                    $from_date = date('Y-m-d');
                }
                return array('field' => tep_draw_date_box($field_name, $from_date, $period, $default) . ($required ? TEXT_FIELD_REQUIRED : ''));
            }

            break;
        case "7":
            if (count($option_res["values"])) {
                $hidden_value = tep_draw_hidden_field($field_name, $option_res["values"][0]["data_pool_options_value"]); // Use hidden field to pass info of this type. need it to be saved in database for shown in "Edit Order" page as well.
                return array('field' => $option_res["values"][0]["data_pool_options_value"] . $hidden_value, 'js' => '');
            }
            break;
    }
}

function tep_get_desired_level_array($data_pool_level_id, $start_level)
{
    $desired_level_array = array();
    $level_select_sql = "	SELECT CAST(b1.brackets_value as SIGNED) AS level_val, b2.brackets_value AS alias
                                FROM " . TABLE_BRACKETS . " AS b1, " . TABLE_BRACKETS . " AS b2
                                WHERE b1.brackets_dependent=b2.brackets_dependent
                                        AND b1.data_pool_level_id ='" . tep_db_input($data_pool_level_id) . "'
                                        AND b1.brackets_value > " . (int)$start_level . "
                                        AND b1.brackets_key='pl_end_level'
                                        AND b2.brackets_key='pl_level_alias'
                                ORDER BY level_val";
    $level_result_sql = tep_db_query($level_select_sql);

    $complete_range_array = tep_get_bracket_range($data_pool_level_id);
    $range_mode = $complete_range_array["mode"];

    $i = 0;
    $previous_val = $start_level;
    while ($level_row = tep_db_fetch_array($level_result_sql)) {
        if ($range_mode == 'continuos') {
            if ($i == 0) { // First loop, need to check this bracket is the 'first bracket' of the whole bracket level. If yes, do not auto generate the rage between the selected start level and this first bracket level.
                if ($start_level >= $complete_range_array["range"][0]["level"]) {
                    for ($linking = $previous_val + 1; $linking < $level_row['level_val']; $linking++) {
                        $desired_level_array[] = array('id' => $linking, 'text' => $linking);
                    }
                }
            } else {
                for ($linking = $previous_val + 1; $linking < $level_row['level_val']; $linking++) {
                    $desired_level_array[] = array('id' => $linking, 'text' => $linking);
                }
            }
        }
        $previous_val = $level_row['level_val'];
        $desired_level_array[] = array('id' => $level_row['level_val'], 'text' => (isset($level_row['alias']) && trim($level_row['alias']) != '' ? $level_row['alias'] : $level_row['level_val']));
        $i++;
    }

    return $desired_level_array;
}

function tep_display_selection_option_html($level_tree_array, $selected_level, &$storage_array)
{
    for ($i = 0; $i < count($level_tree_array); $i++) {
        if ($level_tree_array[$i]["data_pool_min_level"] <= $selected_level && $level_tree_array[$i]["data_pool_max_level"] >= $selected_level ||
            $level_tree_array[$i]["data_pool_min_level"] == 0 && $level_tree_array[$i]["data_pool_max_level"] == 0) {
            $storage_array[] = array('id' => $level_tree_array[$i]["id"], 'text' => $level_tree_array[$i]["path"]);
        }
        if (isset($level_tree_array[$i]["child"]) && count($level_tree_array[$i]["child"])) {
            tep_display_selection_option_html($level_tree_array[$i]["child"], $selected_level, $storage_array);
        }
    }
}

//	Call from custom_product_xmlhttp.php as well
function tep_get_level_name_path($level_id, $separator = '_')
{
    $this_level_path = '';
    $level_name_array = array();
    if (tep_not_null($level_id)) {
        $level_name_select_sql = "SELECT data_pool_level_parent_id, data_pool_level_name FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id ='" . (int)$level_id . "' AND data_pool_level_parent_id<>0";
        $level_name_result_sql = tep_db_query($level_name_select_sql);
        while ($level_name_row = tep_db_fetch_array($level_name_result_sql)) {
            $level_name_array[] = $level_name_row["data_pool_level_name"];

            $level_name_select_sql = "SELECT data_pool_level_parent_id, data_pool_level_name FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id ='" . $level_name_row["data_pool_level_parent_id"] . "' AND data_pool_level_parent_id<>0";
            $level_name_result_sql = tep_db_query($level_name_select_sql);
        }

        $level_name_array = array_reverse($level_name_array);
        $this_level_path = count($level_name_array) ? implode($separator, $level_name_array) : '';
    }

    return $this_level_path;
}

function tep_get_option_eta_string($eta_value, $day_only = false)
{
    $eta_days = floor($eta_value / 24);
    $eta_hours = 0;
    if ($eta_value < 24 || $eta_value % 24 > 0) {
        $eta_hours = ceil($eta_value - ($eta_days * 24));

        if ($eta_hours == 24) {
            $eta_days++;
            $eta_hours = 0;
        }
    }

    if ($day_only) {
        if ($eta_hours > 0) {
            $eta_days++;
        }
        $days_str = ($eta_days > 0 ? $eta_days . ($eta_days <= 1 ? ' day' : ' days') : '');
        $hrs_str = '';
    } else {
        $days_str = ($eta_days > 0 ? $eta_days . ($eta_days <= 1 ? ' day' : ' days') : '');
        $hrs_str = ($eta_hours > 0 ? $eta_hours . ' hour' . ($eta_hours > 1 ? 's' : '') : '');
    }

    return $days_str . (tep_not_null($days_str) && tep_not_null($hrs_str) ? '&nbsp;' : '') . $hrs_str;
}

function tep_grab_option_title_and_value($option_array, $key, $value)
{
    $result_label = $key;
    $result_value = $value;
    $option_array[$key]["data_pool_options_title"] = preg_replace("/(<([\w]+)[^>]*>)(.*)(<\/\\2>)/", '$3', strip_tags($option_array[$key]["data_pool_options_title"]));

    switch ($option_array[$key]["data_pool_options_input_type"]) {
        case "1":
        case "2":
        case "5":
        case "7":
            $result_label = $option_array[$key]["data_pool_options_title"];
            if ($option_array[$key]["data_pool_options_input_type"] == '7') {
                $result_value = strip_tags($result_value);
            }
            break;
        case "3":
        case "4":
            $result_label = $option_array[$key]["data_pool_options_title"];
            for ($i = 0; $i < count($option_array[$key]["values"]); $i++) {
                if ($option_array[$key]["values"][$i]["data_pool_options_values_id"] == (int)$value) {
                    $result_value = $option_array[$key]["values"][$i]["data_pool_options_value"];
                    break;
                }
            }
            break;
        case "999":
            return false;
            break;
    }

    return array('label' => $result_label, 'value' => $result_value);
}

function tep_calculate_grand_total_price($base_price, $base_time, $bracket_price, $bracket_eta, $min_eta, $valid_option_array, $selected_option_array)
{
    global $currencies;

    $total_price = (double)$base_price + (double)$bracket_price;
    $total_eta = (double)$base_time + (double)$bracket_eta;

    $selected_option_key = (is_array($selected_option_array) && count($selected_option_array)) ? array_keys($selected_option_array) : array();

    if (is_array($valid_option_array) && count($valid_option_array)) {
        foreach ($valid_option_array as $option_key => $option_res) {
            if (in_array($option_key, $selected_option_key) && trim($selected_option_array[$option_key]) != '') {
                switch ($option_res["data_pool_options_input_type"]) {
                    case "1":
                    case "2":
                        $total_price += (double)$option_res["values"][0]["data_pool_options_values_price"];
                        $total_eta += (double)$option_res["values"][0]["data_pool_options_values_eta"];
                        break;
                    case "3":
                    case "4":
                        for ($i = 0; $i < count($option_res["values"]); $i++) {
                            if ($option_res["values"][$i]["data_pool_options_values_id"] == (int)$selected_option_array[$option_key]) {
                                $total_price += (double)$option_res["values"][$i]["data_pool_options_values_price"];
                                $total_eta += (double)$option_res["values"][$i]["data_pool_options_values_eta"];
                                break;
                            }
                        }
                        break;
                    case "5":
                        // Currently no price and eta assigned from admin site
                        break;
                }
            }
        }
    }

    $total_eta = ($total_eta < $min_eta) ? $min_eta : $total_eta;

    return array('price' => $total_price, 'price_text' => $currencies->format($total_price), 'eta' => $total_eta, 'eta_text' => TEXT_APPROXIMATE_ETA . ' ' . tep_get_option_eta_string($total_eta));
}

function tep_calculate_bracket_price($leaf_id, $start, $end, &$price, &$eta, &$msg, &$custom_tags)
{
    $data_pool_id = (int)$data_pool_id;
    $bracket_roots = array();
    $arr_brackets = array();
    $sortStack = array();
    $custom_bracket_labels_array = array();

    $bracket_select_sql = "SELECT * FROM " . TABLE_BRACKETS . " WHERE data_pool_level_id ='" . $leaf_id . "' AND brackets_key ='" . KEY_PL_BRACKET . "'";
    $bracket_result_sql = tep_db_query($bracket_select_sql);
    while ($bracket_row = tep_db_fetch_array($bracket_result_sql)) {
        // we got the root now
        array_push($bracket_roots, $bracket_row);
    }

    foreach ($bracket_roots as $root) {
        $tmp = array();

        $sql = "SELECT * FROM " . TABLE_BRACKETS . " WHERE brackets_dependent = '" . $root['brackets_id'] . "';";
        $result = tep_db_query($sql);

        $tmp['data_pool_level_id'] = $leaf_id;
        $tmp['brackets_dependent'] = $root['brackets_id'];

        while ($row = tep_db_fetch_array($result)) {
            $tmp[$row['brackets_key']] = $row['brackets_value'];
        }

        $tmp[KEY_PL_END_LEVEL] = (int)$tmp[KEY_PL_END_LEVEL];

        $arr_brackets[] = $tmp;
    }

    $arr_brackets = tep_bubble_sort($arr_brackets, true);
    $tot_price = 0;
    $tot_time = 0;
    $break_point = 0;

    if (count($arr_brackets[0])) {
        foreach ($arr_brackets[0] as $field_key => $field_value) {
            if (preg_match('/(?:pl_c_)(\d+)_(.*)/is', $field_key, $regs)) {
                $custom_bracket_labels_array[$regs[1] - 1] = array(
                    'key' => $field_key,
                    'display_label' => $regs[2],
                    'value' => 0
                );
            }
        }
    }

    foreach ($arr_brackets as $brak) {
        if ((int)$brak[KEY_PL_END_LEVEL] < $end) {
            if ((int)$brak[KEY_PL_END_LEVEL] > $start) {
                $tot_price = $tot_price + (double)$brak[KEY_PL_VALUE];
                $tot_time = $tot_time + (double)$brak[KEY_PL_INTERVAL];

                if (count($custom_bracket_labels_array)) {
                    foreach ($custom_bracket_labels_array as $array_index => $array_res) {
                        $custom_bracket_labels_array[$array_index]['value'] += (double)$brak[$array_res['key']];
                    }
                }
            }
        } else {
            // also give ideal case suggestion
            $tot_time = $tot_time + (double)$brak[KEY_PL_INTERVAL];
            $tot_price = $tot_price + (double)$brak[KEY_PL_VALUE];

            if (count($custom_bracket_labels_array)) {
                foreach ($custom_bracket_labels_array as $array_index => $array_res) {
                    $custom_bracket_labels_array[$array_index]['value'] += (double)$brak[$array_res['key']];
                }
            }

            $break_point = (int)$brak[KEY_PL_END_LEVEL];
            break;
        }

        $skipFirst++;
    }

    if ($break_point > 0 && $break_point != $end) {
        $msg = sprintf(TEXT_LEVEL_SUGGESTION, $start, $break_point);
    } else {
        $msg = "";
    }
    tep_calculate_level_value($leaf_id, $level_total);

    ksort($custom_bracket_labels_array);
    reset($custom_bracket_labels_array);

    $tot_price = $tot_price + $level_total;
    $eta = $tot_time;
    $price = $tot_price;
    $custom_tags = $custom_bracket_labels_array;
}

function tep_calculate_level_value($id, &$total)
{
    $sql = "SELECT data_pool_level_value,data_pool_level_parent_id FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id='$id' and data_pool_level_parent_id!='0';";
    $result = tep_db_query($sql);

    if ($row = tep_db_fetch_array($result)) {
        $total = (double)$total + (double)$row['data_pool_level_value'];
        tep_calculate_level_value((int)$row['data_pool_level_parent_id'], $total);
    }
}

function tep_get_custom_product_listing_info($product_id)
{
    $product_id = (int)$product_id;
    $configured = 0;

    $data_pool_level_root_select_sql = "SELECT data_pool_level_id
                                        FROM " . TABLE_DATA_POOL_LEVEL . "
                                        WHERE data_pool_level_parent_id=0 AND products_id ='" . $product_id . "'";
    $data_pool_level_root_result_sql = tep_db_query($data_pool_level_root_select_sql);
    if ($data_pool_level_root_row = tep_db_fetch_array($data_pool_level_root_result_sql)) {
        $configured = 1;

        $data_pool_level_id = (int)$data_pool_level_root_row['data_pool_level_id'];

        $base_price_value = tep_get_bracket_value($data_pool_level_id, KEY_PL_BASE_PRICE);
        $base_time_value = tep_get_bracket_value($data_pool_level_id, KEY_PL_BASE_TIME);
        $min_time_value = tep_get_bracket_value($data_pool_level_id, KEY_PL_MIN_TIME);
        $level_label = tep_get_bracket_value($data_pool_level_id, KEY_PL_LEVEL_LABEL);

        $range_resource_array = tep_get_bracket_range($data_pool_level_id);
        $no_of_brackets = count($range_resource_array["range"]);
        if ($no_of_brackets >= 1) {
            $bracket_type = ($no_of_brackets == 1 ? 'SINGLE' : 'MULTI');

            tep_calculate_bracket_price($data_pool_level_id, $range_resource_array["start"]["level"], $range_resource_array["range"][0]["level"], $level_price, $level_eta, $level_msg, $custom_tags);
            $price_amt = (double)$base_price_value + (double)$level_price;
            $eta_value = (double)$base_time_value + (double)$level_eta;
            $eta_value = ($eta_value < $min_time_value) ? $min_time_value : $eta_value;
            $eta_text = tep_get_option_eta_string($eta_value, true);

            if ($bracket_type == 'SINGLE') {
                $end_level = $range_resource_array["range"][0]["level"];
                $option_with_price_select_sql = "	SELECT COUNT(dpov.data_pool_options_values_id) AS total_price_option
                                                        FROM " . TABLE_DATA_POOL_OPTIONS . " AS dpo
                                                        INNER JOIN " . TABLE_DATA_POOL_OPTIONS_VALUES . " AS dpov
                                                                ON dpo.data_pool_options_id=dpov.data_pool_options_id
                                                        WHERE dpo.products_id ='" . $product_id . "'
                                                                AND dpov.data_pool_options_values_price > 0
                                                                AND ( 	(dpov.data_pool_options_values_min_level <= $end_level && dpov.data_pool_options_values_max_level >= $end_level) OR
                                                                                (dpov.data_pool_options_values_min_level = 0 && dpov.data_pool_options_values_max_level = 0)
                                                                        )";
                $option_with_price_result_sql = tep_db_query($option_with_price_select_sql);
                $option_with_price_row = tep_db_fetch_array($option_with_price_result_sql);

                if ($option_with_price_row["total_price_option"] > 0) { // These option will cause the price vary
                    $price_label = 'Regular: From';
                } else {
                    $price_label = 'Regular:';
                }
                /*
                  $start_label = trim($range_resource_array["start"]["alias"] != '') ? $range_resource_array["start"]["alias"] : $range_resource_array["start"]["level"];
                  $end_label = trim($range_resource_array["range"][0]["alias"] != '') ? $range_resource_array["range"][0]["alias"] : $range_resource_array["range"][0]["level"];

                  $level_text = $level_label . ' ' . $start_label . ' - ' . $level_label . ' ' . $end_label;
                 */
                $option_with_eta_select_sql = "	SELECT COUNT(dpov.data_pool_options_values_id) AS total_eta_option
                                                FROM " . TABLE_DATA_POOL_OPTIONS . " AS dpo
                                                INNER JOIN " . TABLE_DATA_POOL_OPTIONS_VALUES . " AS dpov
                                                        ON dpo.data_pool_options_id=dpov.data_pool_options_id
                                                WHERE dpo.products_id ='" . $product_id . "'
                                                        AND dpov.data_pool_options_values_eta > 0
                                                        AND ( 	(dpov.data_pool_options_values_min_level <= $end_level && dpov.data_pool_options_values_max_level >= $end_level) OR
                                                                        (dpov.data_pool_options_values_min_level = 0 && dpov.data_pool_options_values_max_level = 0)
                                                                )";
                $option_with_eta_result_sql = tep_db_query($option_with_eta_select_sql);
                $option_with_eta_row = tep_db_fetch_array($option_with_eta_result_sql);
                if ($option_with_eta_row["total_eta_option"] > 0) { // These option will cause the eta vary
                    $eta_label = 'ETA: From';
                } else {
                    $eta_label = 'ETA:';
                }
                $level_text .= '(' . $eta_label . ' ' . $eta_text . ')';
            } else {
                $price_label = 'Regular: From';
                $level_text = '(ETA: From ' . $eta_text . ')';
            }
        } else {
            // Assume no bracket package
            $price_amt = (double)$base_price_value;

            $eta_value = ($base_time_value < $min_time_value) ? $min_time_value : $base_time_value;
            $eta_text = tep_get_option_eta_string($eta_value, true);

            $price_label = 'Regular: From';
            $level_text = '(ETA: From ' . $eta_text . ')';
        }
    }

    return array(
        'configured' => $configured,
        'price' => array('label' => $price_label, 'value' => $price_amt),
        'level_text' => $level_text
    );
}

function tep_bubble_sort($arr, $isbracket = false)
{
    if (!$isbracket) {
        $key = 1;
    } else {
        $key = KEY_PL_END_LEVEL;
    }

    $size = sizeof($arr);

    if ($size == 0) {
        return array();
    }

    for ($i = ($size - 1); $i >= 0; $i--) {
        for ($j = 1; $j <= $i; $j++) {
            if ($arr[$j - 1][$key] > $arr[$j][$key]) {
                $temp = $arr[$j - 1];
                $arr[$j - 1] = $arr[$j];
                $arr[$j] = $temp;
            }
        }
    }

    return $arr;
}

function tep_check_product_bundle($products_id)
{
    $product_info_select_sql = "SELECT products_bundle, products_bundle_dynamic
								FROM " . TABLE_PRODUCTS . " 
								WHERE products_id='" . tep_db_input($products_id) . "'";
    $product_info_result_sql = tep_db_query($product_info_select_sql);

    if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
        if ($product_info_row['products_bundle'] == 'yes' || $product_info_row['products_bundle_dynamic'] == 'yes') {
            return true;
        }
    }

    return false;
}

function tep_get_custom_product_type($products_id)
{
    $custom_type = '';

    $product_info_select_sql = "SELECT products_bundle, products_bundle_dynamic, custom_products_type_id
								FROM " . TABLE_PRODUCTS . " 
								WHERE products_id='" . tep_db_input($products_id) . "'";
    $product_info_result_sql = tep_db_query($product_info_select_sql);

    if ($product_info_row = tep_db_fetch_array($product_info_result_sql)) {
        if ($product_info_row['products_bundle'] != 'yes' && $product_info_row['products_bundle_dynamic'] != 'yes') {
            $custom_type = $product_info_row['custom_products_type_id'];
        } else {
            // Assume package ONLY consists of ONE product type
            $package_type_select_sql = "SELECT p.custom_products_type_id
                                        FROM " . TABLE_PRODUCTS . " AS p
                                                INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
                                                        ON p.products_id=pb.subproduct_id
                                        WHERE pb.bundle_id = '" . tep_db_input($products_id) . "'
                                                LIMIT 1";
            $package_type_result_sql = tep_db_query($package_type_select_sql);
            if ($package_type_row = tep_db_fetch_array($package_type_result_sql)) {
                $custom_type = $package_type_row['custom_products_type_id'];
            }
        }
    }
    return $custom_type;
}

function tep_get_products_access($admin_group_id)
{
    $products_access_array = array();
    $products_access_select_sql = "	SELECT custom_products_type_id
                                        FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . "
                                        WHERE FIND_IN_SET('" . $admin_group_id . "', custom_products_admin_group_id)";
    $products_access_result_sql = tep_db_query($products_access_select_sql);

    while ($products_access_row = tep_db_fetch_array($products_access_result_sql)) {
        $products_access_array[] = $products_access_row['custom_products_type_id'];
    }
    return $products_access_array;
}

// Get all the notification email key for a particular product type
function tep_get_custom_products_notification_email($product_type_id)
{
    $notification_email_row = array();

    $notification_email_select_sql = "	SELECT custom_products_low_stock_email, custom_products_add_stock_email, custom_products_deduct_stock_email, custom_product_price_email, custom_products_upload_email, custom_products_change_status_email
                                        FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . "
                                        WHERE custom_products_type_id = '" . tep_db_input($product_type_id) . "'";
    $notification_email_result_sql = tep_db_query($notification_email_select_sql);
    $notification_email_row = tep_db_fetch_array($notification_email_result_sql);

    return $notification_email_row;
}

?>