<?
/*
  	$Id: customers_info_verification.php,v 1.26 2013/11/13 10:11:58 chingyen Exp $
	
  	Developer: <PERSON>wang <PERSON>
  	Copyright (c) 2006 SKC Venture
	
  	Released under the GNU General Public License
*/

function tep_format_telephone($customer_id) {
	$customer_info_select_sql = "select customers_telephone, customers_country_dialing_code_id, customers_default_address_id from " . TABLE_CUSTOMERS . " where customers_id ='" . (int)$customer_id . "'";
	$customer_info_result_sql = tep_db_query($customer_info_select_sql);
	$customer_info_row = tep_db_fetch_array($customer_info_result_sql);

	$customer_telephone_not_standard_format = $customer_info_row['customers_telephone'];
	$customer_country_dialing_code_id = $customer_info_row['customers_country_dialing_code_id'];
	$customer_default_address_id = $customer_info_row['customers_default_address_id'];
	$customer_telephone_not_standard_format = preg_replace('/[^\d]/', '', $customer_telephone_not_standard_format);
	
	if (!tep_not_null($customer_country_dialing_code_id)) {
		$customer_country_dialing_code_id_select_sql = "select entry_country_id from " . TABLE_ADDRESS_BOOK . " where address_book_id = '" . $customer_default_address_id . "' and customers_id ='" . (int)$customer_id . "'";
		$customer_country_dialing_code_id_query = tep_db_query($customer_country_dialing_code_id_select_sql);
		$customer_country_dialing_code_id_row = tep_db_fetch_array($customer_country_dialing_code_id_query);
		
		$international_dialing_code_id_update_sql = "update " . TABLE_CUSTOMERS . " set customers_country_dialing_code_id = '" . $customer_country_dialing_code_id_row['entry_country_id'] . "' where customers_id ='" . (int)$customer_id . "'";
		tep_db_query($international_dialing_code_id_update_sql);
		
		$customer_country_dialing_code_id = $customer_country_dialing_code_id_row['entry_country_id'];
	}
	
	$country_internation_dialing_code_sql_select = "select countries_international_dialing_code from " . TABLE_COUNTRIES . " where countries_id='" . $customer_country_dialing_code_id . "'";
	$country_international_dialing_code_query = tep_db_query($country_internation_dialing_code_sql_select);
	$country_international_dialing_code_row = tep_db_fetch_array($country_international_dialing_code_query);
	
	$country_international_dialing_code = $country_international_dialing_code_row['countries_international_dialing_code'];
	
	$customer_telephone = tep_parse_telephone($customer_telephone_not_standard_format, $country_international_dialing_code, 'code');
	
	$customer_telephone = array('country_id' => $customer_country_dialing_code_id, 'country_international_dialing_code' => $country_international_dialing_code, 'telephone_number' => $customer_telephone);
	return $customer_telephone;
}

function tep_parse_telephone($telephone, $country_needle, $type = 'id') {
	$country_code = '';
	
	$telephone = preg_replace('/[^\d]/', '', $telephone);
	
	if ( $type == 'id' ) {
        
        $telephone_country_code_select_sql = "select countries_id, countries_international_dialing_code from " . TABLE_COUNTRIES . " where countries_id ='" . tep_db_input($country_needle) . "'";
	    $telephone_country_code_result_sql = tep_db_query($telephone_country_code_select_sql);
	    $telephone_country_code_row = tep_db_fetch_array($telephone_country_code_result_sql);
	    
	    $country_code = $telephone_country_code_row['countries_international_dialing_code'];
	    $country_id = $telephone_country_code_row['countries_id'];
            
	} else if ( $type == 'code' ) {
        
        $country_code = $country_needle;
        $country_id_select_sql = "select countries_id from " . TABLE_COUNTRIES . " where countries_international_dialing_code ='" . tep_db_input($country_code) . "'";
	    $country_id_result_sql = tep_db_query($country_id_select_sql);
	    $country_id_row = tep_db_fetch_array($country_id_result_sql);
	    $country_id = $country_id_row['countries_id'];
            
	}
	
	switch ( $country_id ) {
        
		case 105: // Italy (Fixed line has one leading zero but Mobile does not have)
			$extra_reg_rule = '(?:[0|39])';
            $max_len = 11;
			break;
        case 30: // Brazil may have area code of 55, same as the country code
        	$extra_reg_rule = '(?:55)';
            $max_len = 11;
            break;
		default:
			$extra_reg_rule = '';
            $max_len = 10;
			break;
                        
	}
        
    //Remove all leading zero before country code, if any
    $telephone = preg_replace('/^(0*)(' . $country_code . '\d+)/', '$2', $telephone);
	
	//Remove all leading zero except one 0, before phone number
	$telephone =  preg_replace('/^(0+)('. $extra_reg_rule .'\d+)/', '$2', $telephone);
	
	if ( tep_not_null($country_code) ) {
		while ( strlen($telephone) > $max_len ) {
			if (preg_match('/^(' . $country_code . ')(' . $extra_reg_rule . '\d+)/', $telephone)) {
				$telephone = preg_replace('/^(' . $country_code . ')(' . $extra_reg_rule . '\d+)/', '$2', $telephone);
				$telephone = preg_replace('/^(0+)(' . $extra_reg_rule . '\d+)/', '$2', $telephone);
			} else {
				break;
			}
		}
	}
	
	switch ( $country_id ) {
		case 105: // Italy (Fixed line has one elading zero but Mobile does not have)
			if (substr($telephone, 0, 2) == '03') {	// Mobile number
				$telephone = substr($telephone, 1);
			}
			
			break;
		default:
			$telephone = ltrim($telephone, 0);
			break;
	}
	
	return $telephone;
}

function tep_info_verified_check($customer_id, $verify_info, $type) {
	/*
		A - Auto
		M - Manual
		T - Triggered
	*/
	if ($type == 'telephone') {
		$info_verified_select_sql = "	SELECT info_verified, customers_info_verification_mode 
										FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
										WHERE customers_id ='" . tep_db_input($customer_id) . "' 
                                            AND customers_info_value ='" . tep_db_input($verify_info) . "' 
											AND info_verification_type ='" . tep_db_input($type) . "' 
											AND info_verified = '1'";
	} else {
		$info_verified_select_sql = "	SELECT info_verified, customers_info_verification_mode 
										FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
										WHERE customers_id ='" . tep_db_input($customer_id) . "' 
											AND customers_info_value ='" . tep_db_input($verify_info) . "' 
											AND info_verification_type ='" . tep_db_input($type) . "'";
	}
	
	$info_verified_result_sql = tep_db_query($info_verified_select_sql);
	if ($info_verified_row = tep_db_fetch_array($info_verified_result_sql)) {
		if ($info_verified_row['info_verified'] == '1') {
			return $info_verified_row['customers_info_verification_mode'];
		} else {
			return 'T';
		}
	} else {
		return false;
	}
}

function tep_set_telephone_verified($customer_id, $verify_info, $type) {
	// Requested changes. Phone is consider verify if it has been verify but belong to other customer
	$info_verified_select_sql = "	SELECT info_verified 
									FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
									WHERE customers_id = '" . $customer_id . "'
                                        AND customers_info_value ='" . tep_db_input($verify_info) . "' 
										AND info_verification_type='" . tep_db_input($type) . "'";
	$info_verified_result_sql = tep_db_query($info_verified_select_sql);
	
	if (tep_db_num_rows($info_verified_result_sql)) {
		$telephone_verified_update_sql = "	UPDATE " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
											SET serial_number='',
												info_verified=1,
												customers_info_verification_date = now(),
												customers_info_verification_mode='M'
											WHERE customers_id = '" . $customer_id . "'
                                                AND customers_info_value = '" . tep_db_input($verify_info) . "' 
												AND info_verification_type='telephone'";
		tep_db_query($telephone_verified_update_sql);
	} else {
		$sql_data_array = array('customers_id' => $customer_id,
								'customers_info_value' => $verify_info,
								'serial_number' => '',
								'info_verified' => 1,
		                        'info_verification_type' => 'telephone',
		                        'customers_info_verification_date' => 'now()',
		                        'customers_info_verification_mode' => 'M'
		                        );
		tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $sql_data_array);
	}
}

function tep_set_telephone_unverified($customer_id, $verify_info, $type) {
    // Requested changes. Phone is consider verify if it has been verify but belong to other customer
    $info_verified_select_sql = "	SELECT info_verified 
									FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
									WHERE customers_id = '" . $customer_id . "'
                                        AND customers_info_value ='" . tep_db_input($verify_info) . "' 
										AND info_verification_type='" . tep_db_input($type) . "'";
    $info_verified_result_sql = tep_db_query($info_verified_select_sql);

    if (tep_db_num_rows($info_verified_result_sql)) {
        $telephone_verified_update_sql = "	UPDATE " . TABLE_CUSTOMERS_INFO_VERIFICATION . "
											SET serial_number='',
												info_verified=0,
												customers_info_verification_date = now(),
												customers_info_verification_mode='M'
											WHERE customers_id = '" . $customer_id . "'
                                                AND customers_info_value = '" . tep_db_input($verify_info) . "' 
												AND info_verification_type='telephone'";
        tep_db_query($telephone_verified_update_sql);
    }
}

function tep_get_order_telephone($orders_id) {
	$order_telephone_select_sql = "select customers_id, customers_country_international_dialing_code, customers_telephone, customers_country from " . TABLE_ORDERS . " where orders_id ='" . $orders_id . "'";
	$order_telephone_result_sql = tep_db_query($order_telephone_select_sql);
	$order_telephone_row = tep_db_fetch_array($order_telephone_result_sql);
	
	$country_code = $order_telephone_row['customers_country_international_dialing_code'];
	
	if (!tep_not_null($country_code)) {
		$country_select_sql = "select countries_international_dialing_code from " . TABLE_COUNTRIES . " where countries_name = '" . $order_telephone_row['customers_country'] . "'";
		$country_result_sql = tep_db_query($country_select_sql);
		$country_row = tep_db_fetch_array($country_result_sql);
		
		$country_code = $country_row['countries_international_dialing_code'];
		
		$update_country_code = "update " . TABLE_ORDERS . " set customers_country_international_dialing_code = '" . $country_code . "' where orders_id ='" . $orders_id . "'";
		tep_db_query($update_country_code);
	}
	
	return array('customer_id' => $order_telephone_row['customers_id'], 'orders_country_dailing_code' => $country_code, 'orders_telephone' => preg_replace('/[^\d]/', '', $order_telephone_row['customers_telephone']));
}

function tep_get_user_flags($list_all=true) {
    global $login_groups_id;
    
	$user_flags_array = array();
	$user_flags_select_sql = "	SELECT user_flags_id, user_flags_name, user_flags_description, user_flags_alias, user_flags_css_style, user_flags_on_notification, user_flags_off_notification 
								FROM " . TABLE_USER_FLAGS . " 
								WHERE user_flags_status = 1 " . ($list_all ? '' : " AND FIND_IN_SET('". $login_groups_id ."', user_flags_admin_groups_id)") . " 
								ORDER BY user_flags_id	";
	$user_flags_result_sql = tep_db_query($user_flags_select_sql);
	
	while ($user_flags_row = tep_db_fetch_array($user_flags_result_sql)) {
		$user_flags_array[$user_flags_row['user_flags_id']] = $user_flags_row;
	}
	
	return $user_flags_array;
}

function tep_is_cb_customer($customer_id) {
	$customer_cb_flag_select_sql = "SELECT customers_id 
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id = '" . tep_db_input($customer_id) . "' 
										AND FIND_IN_SET('4', customers_flag)";
	$customer_cb_flag_result_sql = tep_db_query($customer_cb_flag_select_sql);
	
	if (tep_db_num_rows($customer_cb_flag_result_sql))	{
		return true;
	} else {
		return false;
	}
}

function tep_send_info_verification($email_address, $firstname, $lastname, $gender, $serial = '', $customer_id, $site_id = '') {
	if(!tep_not_null($serial)){
        // Generate Serial Number
        $new_serial = tep_gen_random_serial($email_address, TABLE_CUSTOMERS_INFO_VERIFICATION, 'serial_number', '', 12);
        
        $_res = tep_db_query("SELECT serial_number FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " WHERE customers_info_value = '" . tep_db_input($email_address) . "' and customers_id ='" . (int)$customer_id . "'");
        if (tep_db_num_rows($_res)) {
            tep_db_query("update " . TABLE_CUSTOMERS_INFO_VERIFICATION . " set serial_number = '" . $new_serial . "' where customers_info_value = '" . tep_db_input($email_address) . "' and customers_id ='" . (int)$customer_id . "'");
        } else {
            $_data = array(
                'customers_id ' => (int)$customer_id,
                'customers_info_value' => tep_db_input($email_address),
                'serial_number' => $new_serial,
                'info_verified' => 0,
                'info_verification_type' => 'email');
            tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $_data);
        }
	} else {
		$new_serial = $serial;
	}
	
	$name = $firstname . ' ' . $lastname;
	$email_text .= tep_get_email_greeting($firstname, $lastname, $gender);
	
    $activate_address = HTTP_MY_ACCOUNT_PORTAL . '/verify-email/index?action=verify&serialNumber=' . $new_serial . '&email=' . urlencode($email_address);
    $mail_prefix = '';
    $store_owner = '';
    $store_mail = '';
    switch ($site_id) {
        case 5:
            $_res = tep_db_query('SELECT configuration_key AS cfgKey, configuration_value AS cfgValue FROM ' . TABLE_C2C_CONFIGURATION . " WHERE configuration_key LIKE 'G2G_%'");
            while ($_row = tep_db_fetch_array($_res)) {
                define($_row['cfgKey'], $_row['cfgValue']);
            }
            
            $mail_prefix = G2G_EMAIL_SUBJECT_PREFIX;
            $mail_to = G2G_EMAIL_TO;
            $mail_footer = G2G_EMAIL_FOOTER;
            $store_mail = G2G_STORE_OWNER_EMAIL_ADDRESS;
            $store_name = G2G_STORE_NAME;
            $store_owner = G2G_STORE_OWNER;
            break;
        
        default:
            $mail_prefix = EMAIL_SUBJECT_PREFIX;
            $mail_to = EMAIL_TO;
            $mail_footer = EMAIL_FOOTER;
            $store_mail = "<EMAIL>";
            $store_name = "<EMAIL>";
            $store_owner = STORE_OWNER;
            break;
    }
    
    $link = '<a href="' . $activate_address . '" target="_blank">' . $activate_address . '</a><br>';
	$email_text .= sprintf(TEXT_EMAIL_VERIFY_CONTENT, $store_name) . $link . TEXT_EMAIL_VERIFY_CONTENT_ADDRESS_INFO . "\n\n" . sprintf(EMAIL_CONTACT, $mail_to, $store_name) . $mail_footer;
	tep_mail($name, $email_address, implode(' ', array($mail_prefix, EMAIL_SUBJECT)), $email_text, $store_owner, $store_mail);
}

function tep_get_customer_store_credit_currency($customer_id, $checkout_currency = DEFAULT_CURRENCY, $return_id = true) {
	global $currencies;
	
	$sc_info_select_sql = "	SELECT DISTINCT sc_currency_id
							FROM " . TABLE_COUPON_GV_CUSTOMER . "
							WHERE customer_id = '" . tep_db_input($customer_id) . "'";
	$sc_info_result_sql = tep_db_query($sc_info_select_sql);
	if ($sc_info_row = tep_db_fetch_array($sc_info_result_sql)) {
		if ($return_id) {
			return $sc_info_row['sc_currency_id'];
		} else {
			return $currencies->get_code_by_id($sc_info_row['sc_currency_id']);
		}
	} else {
		 // use selected currency if no record found.
		if ($return_id) {
			return array_search($checkout_currency, $currencies->internal_currencies);
		} else {
			return $checkout_currency;
		}
	}
}

function check_email_domain($email_address, $email_status) {
	if (tep_not_null($email_address)) {
		$email_address_array = explode("@", $email_address);
		
		$email_domain_groups_select_sql = "	SELECT email_domain_groups_name, ".($email_status == 1 ? 'dg.verified_files_name' : 'dg.not_verified_files_name') ." AS file_name
											FROM ".TABLE_EMAIL_DOMAIN_GROUPS." AS dg 
											INNER JOIN ".TABLE_EMAIL_DOMAIN_GROUPS_DOMAINS." AS dgd 
												ON dg.email_domain_groups_id = dgd.email_domain_groups_id
											WHERE dgd.email_domain_groups_domains_name = '@".$email_address_array[1]."'";
		$email_domain_groups_result_sql = tep_db_query($email_domain_groups_select_sql);
		if ($email_domain_groups_row = tep_db_fetch_array($email_domain_groups_result_sql)) {
			if (tep_not_null($email_domain_groups_row['file_name'])) {
				return tep_image(DIR_WS_IMAGES.'user_icons/'.$email_domain_groups_row['file_name'], "", "", "", 'align="top" onMouseover="ddrivetip(\'' .$email_domain_groups_row['email_domain_groups_name']. '\', \'\' , 110);" onMouseout="hideddrivetip();"'). '&nbsp;';
			}
		}
	}
	return '';
}

function tep_file_uploaded_email_notification($cust_id) {
	$email_subject = 'Verification Form Uploaded From Customer <'.$cust_id.'>';
	$email_content = 'Verification form successfully uploaded from Customer, please review.';
	
	$email_to_array = tep_parse_email_string(STORE_AFT_DOCUMENT_NOTIFICATION_EMAIL);
	for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
		tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, $email_subject)), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
}

function tep_is_mobile_num_exist($phone_no, $country_dial_id, $customer_id = 0, $country_dial_code = '') {
    $return_bool = FALSE;
    $used_by = '';
    
    if (!empty($country_dial_id) && empty($country_dial_code)) {
        $country_id_select_sql = "  SELECT countries_international_dialing_code 
                                    FROM " . TABLE_COUNTRIES . " 
                                    WHERE countries_id ='" . tep_db_input($country_dial_id) . "'";
        $country_id_result_sql = tep_db_query($country_id_select_sql);
        if ($country_id_row = tep_db_fetch_array($country_id_result_sql)) {
            $country_dial_code = $country_id_row['countries_international_dialing_code'];
        }
    } else if (empty($country_dial_id) && !empty($country_dial_code)) {
        $country_id_select_sql = "  SELECT countries_id
                                    FROM " . TABLE_COUNTRIES . " 
                                    WHERE countries_international_dialing_code ='" . tep_db_input($country_dial_code) . "'";
        $country_id_result_sql = tep_db_query($country_id_select_sql);
        if ($country_id_row = tep_db_fetch_array($country_id_result_sql)) {
            $country_dial_id = $country_id_row['countries_id'];
        }
    }
    
	$customer_phone_select_sql = "  SELECT customers_id 
                                    FROM " . TABLE_CUSTOMERS . "
									WHERE customers_country_dialing_code_id IN (SELECT countries.countries_id FROM countries WHERE countries.countries_international_dialing_code = '" . $country_dial_code . "')
                                        AND customers_telephone = '" . tep_db_input($phone_no) . "'
                                        AND customers_id != '" . (int)$customer_id . "'";
	$customer_phone_result_sql = tep_db_query($customer_phone_select_sql);
    while ($phone_row = tep_db_fetch_array($customer_phone_result_sql)) {
        $verified_info = tep_info_verified_check($phone_row['customers_id'], ($country_dial_code . $phone_no), 'telephone');
        
        if (in_array($verified_info, array('A', 'M'))) {
            $used_by = $phone_row['customers_id'];
            $return_bool = TRUE;
            break;
        }
    }
    
    return array('status' => $return_bool, 'customers_id' => $used_by);
}
?>