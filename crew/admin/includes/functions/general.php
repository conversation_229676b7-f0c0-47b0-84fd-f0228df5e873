<?php

/*
  $Id: general.php,v 1.260 2016/04/18 08:42:30 jeeva.kasinathan Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce
  Released under the GNU General Public License
 */

function tep_get_percentage($total, $count) {
    $percentage = 0.00;

    if ($total != 0) {
        $percentage = floatval(($count * 100) / $total);
    }

    return $percentage;
}

function tep_get_categories_name($categories_id, $language_id = 0) {
    global $languages_id;
    if ($language_id == 0)
        $language_id = $languages_id;

    $categories_name_select_sql = " SELECT categories_name
                                    FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                    WHERE categories_id = '" . (int) $categories_id . "'
                                            AND categories_name <> ''
                                            AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(categories_id) > 0
                                                FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                                WHERE categories_id = '" . (int) $categories_id . "'
                                                        AND language_id = '" . (int) $language_id . "'
                                                        AND categories_name <> ''), 0, language_id = '" . (int) $_SESSION['default_languages_id'] . "')))";
    $categories_name_result_sql = tep_db_query($categories_name_select_sql);
    $categories_name_row = tep_db_fetch_array($categories_name_result_sql);

    return $categories_name_row['categories_name'];
}

function tep_get_categories_short_name($categories_id, $language_id = 0) {
    global $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $categories_name_select_sql = " SELECT categories_short_name
                                    FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                    WHERE categories_id = '" . (int) $categories_id . "'
                                        AND categories_name <> ''
                                        AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(categories_id) > 0
                                            FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                            WHERE categories_id = '" . (int) $categories_id . "'
                                                    AND language_id = '" . (int) $language_id . "'
                                                    AND categories_name <> ''), 0, language_id = '" . (int) $_SESSION['default_languages_id'] . "')))";
    $categories_name_result_sql = tep_db_query($categories_name_select_sql);
    $categories_name_row = tep_db_fetch_array($categories_name_result_sql);

    return $categories_name_row['categories_short_name'];
}

function tep_get_categories_parent_path($categories_id) {
    $categories_path_select_sql = " SELECT categories_parent_path
                                    FROM " . TABLE_CATEGORIES . "
                                    WHERE categories_id = '" . (int) $categories_id . "'";
    $categories_path_result_sql = tep_db_query($categories_path_select_sql);
    $categories_path_row = tep_db_fetch_array($categories_path_result_sql);
    return $categories_path_row['categories_parent_path'];
}

function tep_get_categories_description($categories_id, $language_id = 0) {
    global $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $categories_description_select_sql = "  SELECT categories_description
                                            FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                            WHERE categories_id = '" . (int) $categories_id . "'
                                                AND categories_description <> ''
                                                AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(categories_id) > 0
                                                    FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                                    WHERE categories_id = '" . (int) $categories_id . "'
                                                            AND categories_description <> ''
                                                            AND language_id = '" . (int) $language_id . "'), 0, language_id = '" . (int) $_SESSION['default_languages_id'] . "')))";
    $categories_description_result_sql = tep_db_query($categories_description_select_sql);
    $categories_description_row = tep_db_fetch_array($categories_description_result_sql);

    return $categories_description_row['categories_description'];
}

function tep_update_categories_keywords($categories_id) {
    $keywords_data_array = array();

    $category_id_array_select_sql = "	SELECT categories_structures_value
                                        FROM " . TABLE_CATEGORIES_STRUCTURES . "
                                        WHERE categories_structures_key = 'games'";
    $category_id_array_result_sql = tep_db_query($category_id_array_select_sql);
    if ($category_id_array_row = tep_db_fetch_array($category_id_array_result_sql)) {
        $category_id_array = explode(',', $category_id_array_row['categories_structures_value']);
    }

    // Remove all search value of this Categories (Regardless of Game or not)
    $categories_search_delete_sql = "	DELETE FROM " . TABLE_CATEGORIES_SEARCH . "
                                        WHERE categories_id= '" . $categories_id . "'";
    tep_db_query($categories_search_delete_sql);

    if (in_array($categories_id, $category_id_array)) { // If is GAME type
        $all_languages = tep_get_languages();
        $categories_id = tep_db_prepare_input($categories_id);

        foreach ($all_languages as $languages) {
            $category_name = tep_get_categories_name($categories_id, $languages['id']);
            $game_keyword_select_sql = "SELECT game_keyword
                                        FROM " . TABLE_CATEGORIES_GAME_DETAILS . "
                                        WHERE categories_id = '" . $categories_id . "'
                                            AND language_id = " . (int) $languages['id'];
            $game_keyword_result_sql = tep_db_query($game_keyword_select_sql);
            $game_keyword_row = tep_db_fetch_array($game_keyword_result_sql);

            $keywords_data_array = array('search_value' => tep_db_prepare_input($category_name . (tep_not_empty($game_keyword_row['game_keyword']) ? ', ' . $game_keyword_row['game_keyword'] : '')),
                'categories_id' => $categories_id,
                'language_id' => (int) $languages['id']
            );
            tep_db_perform(TABLE_CATEGORIES_SEARCH, $keywords_data_array);
        }
    }
    unset($keywords_data_array);
}

function tep_get_categories($categories_array = '', $parent_id = '0', $indent = '', $showsubcat = false) {
    if (!is_array($categories_array))
        $categories_array = array();

    $categories_sql = "	SELECT categories_id
                        FROM " . TABLE_CATEGORIES . "
                        WHERE parent_id = '" . (int) $parent_id . "'
                        ORDER BY sort_order";
    $categories_query = tep_db_query($categories_sql);

    while ($categories = tep_db_fetch_array($categories_query)) {
        $categories_name = tep_get_categories_name($categories['categories_id']);

        $categories_array[] = array('id' => $categories['categories_id'],
            'text' => $indent . $categories_name);

        if ($categories['categories_id'] != $parent_id) {
            $categories_array = tep_get_categories($categories_array, $categories['categories_id'], $indent . '__');
        }
    }

    return $categories_array;
}

function tep_get_custom_product_type_name($id) {
    $result = '';

    if ($id != '') {
        $_sel_sql = "SELECT custom_products_type_name FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . " WHERE custom_products_type_id = '" . (int) $id . "'";
        $_res_sql = tep_db_query($_sel_sql);
        if ($_row = tep_db_fetch_array($_res_sql)) {
            $result = $_row['custom_products_type_name'];
        }
    }

    return $result;
}

function tep_get_subcategories(&$subcategories_array, $parent_id = 0, $filename = '') {
    if (tep_not_null($filename)) {
        $subcategories_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int) $parent_id . "'");
        while ($subcategories = tep_db_fetch_array($subcategories_query)) {
            $p_rank = tep_check_cat_tree_permissions($filename, $subcategories['categories_id']);
            if ($p_rank == 1) {
                $subcategories_array[sizeof($subcategories_array)] = $subcategories['categories_id'];
            }

            if ($subcategories['categories_id'] != $parent_id) {
                tep_get_subcategories($subcategories_array, $subcategories['categories_id'], $filename);
            }
        }
    } else {

        $parent_path_str = tep_get_categories_parent_path((int) $parent_id);
        if (tep_not_null($parent_path_str)) {
            $parent_path_str = $parent_path_str . (int) $parent_id . '_';
        } else {
            $parent_path_str = '_' . (int) $parent_id . '_';
        }
        $parent_path_str = preg_replace("/_/u", "\_", $parent_path_str);

        $sub_categories_select_sql = "	SELECT categories_id
                                        FROM " . TABLE_CATEGORIES . "
                                        WHERE " . ($parent_id > 0 ? " categories_parent_path LIKE '" . $parent_path_str . "%'" : '1');
        $sub_categories_result_sql = tep_db_query($sub_categories_select_sql);
        while ($sub_categories_row = tep_db_fetch_array($sub_categories_result_sql)) {
            $subcategories_array[sizeof($subcategories_array)] = $sub_categories_row['categories_id'];
        }
    }
}

function tep_get_parent_categories(&$parent_categories_array, $cat_id = 0, $exclude_zero = false) {
    $parent_categories_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $cat_id . "'");
    if ($parent_categories = tep_db_fetch_array($parent_categories_query)) {
        if ($exclude_zero) {
            if ($parent_categories['parent_id'] == 0)
                return true;
        }

        $parent_categories_array[sizeof($parent_categories_array)] = $parent_categories['parent_id'];
        if ($parent_categories['parent_id'] != 0) {
            tep_get_parent_categories($parent_categories_array, $parent_categories['parent_id'], $exclude_zero);
        }
    }
}

function tep_get_products_delivery_mode_title($id) {
    $result = '';

    if ($id != '') {
        $_sel_sql = "SELECT products_delivery_mode_title FROM " . TABLE_PRODUCTS_DELIVERY_MODE . " WHERE products_delivery_mode_id = '" . (int) $id . "'";
        $_res_sql = tep_db_query($_sel_sql);
        if ($_row = tep_db_fetch_array($_res_sql)) {
            $result = $_row['products_delivery_mode_title'];
        }
    }

    return $result;
}

function tep_get_buyback_main_cat_info($id, $from = 'category') {
    $buyback_main_cat_info = array();
    $buyback_main_cat_path = '';

    $cat_path_array = tep_generate_category_path($id, $from);

    if ($from == 'category') {
        for ($cat_cnt = count($cat_path_array[0]) - 1; $cat_cnt >= 0; $cat_cnt--) {
            $buyback_main_cat_path .= $cat_path_array[0][$cat_cnt]['text'] . ' >';

            $main_cat_select_sql = "SELECT categories_id
                                    FROM " . TABLE_CATEGORIES . "
                                    WHERE categories_buyback_main_cat = 1
                                            AND categories_id = '" . (int) $cat_path_array[0][$cat_cnt]['id'] . "'";
            $main_cat_result_sql = tep_db_query($main_cat_select_sql);
            if (tep_db_num_rows($main_cat_result_sql)) {
                $buyback_main_cat_info = array('id' => (int) $cat_path_array[0][$cat_cnt]['id'],
                    'text' => $cat_path_array[0][$cat_cnt]['text'],
                    'path' => substr($buyback_main_cat_path, 0, -1));
                break;
            }
        }
    } else {
        for ($cat_cnt = 0; $cat_cnt < count($cat_path_array[0]); $cat_cnt++) {
            $buyback_main_cat_path .= $cat_path_array[0][$cat_cnt]['text'] . ' >';

            $main_cat_select_sql = "SELECT categories_id
                                    FROM " . TABLE_CATEGORIES . "
                                    WHERE categories_buyback_main_cat = 1
                                            AND categories_id = '" . (int) $cat_path_array[0][$cat_cnt]['id'] . "'";
            $main_cat_result_sql = tep_db_query($main_cat_select_sql);
            if (tep_db_num_rows($main_cat_result_sql)) {
                $buyback_main_cat_info = array('id' => (int) $cat_path_array[0][$cat_cnt]['id'],
                    'text' => $cat_path_array[0][$cat_cnt]['text'],
                    'path' => substr($buyback_main_cat_path, 0, -1));
                break;
            }
        }
    }

    return $buyback_main_cat_info;
}

function tep_get_buyback_main_cat_id($product_id) {
    $products_cat_id_path_arr = array();
    $main_cat_id = '';

    $products_cat_id_path_select_sql = "SELECT products_cat_id_path
                                        FROM " . TABLE_PRODUCTS . "
                                        WHERE products_id = '" . (int) $product_id . "'";
    $products_cat_id_path_result_sql = tep_db_query($products_cat_id_path_select_sql);

    if ($products_cat_id_path_row = tep_db_fetch_array($products_cat_id_path_result_sql)) {
        $products_cat_id_path_arr = explode("_", $products_cat_id_path_row['products_cat_id_path']);
        $products_cat_id_path_arr = array_slice($products_cat_id_path_arr, 1);

        $main_cat_select_sql = "SELECT categories_id
                                FROM " . TABLE_CATEGORIES . "
                                WHERE categories_buyback_main_cat = '1'
                                        AND categories_id IN ('" . implode("','", $products_cat_id_path_arr) . "')";
        $main_cat_result_sql = tep_db_query($main_cat_select_sql);

        if ($main_cat_row = tep_db_fetch_array($main_cat_result_sql)) {
            $main_cat_id = $main_cat_row['categories_id'];
        }
    }

    return $main_cat_id;
}

function tep_get_categories_info($categories_id, $field_name, $languages_id, $default_languages_id) {
    $categories_info_select_sql = " SELECT " . $field_name . "
                                    FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                    WHERE categories_id = '" . (int) $categories_id . "'
                                    AND " . $field_name . " <> ''
                                        and (IF(language_id = '" . $languages_id . "' , 1, IF (( SELECT COUNT(categories_id) > 0
                                        FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                        WHERE categories_id = '" . (int) $categories_id . "'
                                               and language_id = '" . $languages_id . "'
                                        AND " . $field_name . " <> ''
                                               ), 0, language_id = '" . $default_languages_id . "')))";
    $categories_info_result_sql = tep_db_query($categories_info_select_sql);
    $categories_info_row = tep_db_fetch_array($categories_info_result_sql);

    return $categories_info_row[$field_name];
}

function tep_get_games_id() {
    tep_db_connect_og();

    $categories_games_array = [];
    $categories_select_sql = "SELECT DISTINCT(categories_id) FROM category";
    $categories_result_sql = tep_db_query($categories_select_sql, 'db_og_link');
    while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
        $categories_games_array[] = $categories_row['categories_id'];
    }

    return $categories_games_array;
}

//Admin begin
////
//Check login and file access
function tep_admin_check_login() {
    global $PHP_SELF;
    if (!isset($_SESSION['login_id'])) {
        tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
    } else {
        $filename = basename($PHP_SELF);
        if ($filename != FILENAME_DEFAULT && $filename != FILENAME_FORBIDEN && $filename != FILENAME_LOGOFF &&
                $filename != FILENAME_ADMIN_ACCOUNT && $filename != FILENAME_POPUP_IMAGE && $filename != 'packingslip.php' &&
                $filename != 'invoice.php' && !preg_match('/(.*?)_xmlhttp.php$/', $filename)) { // Skip xmlhttp script as well. Permission is checking in there
            $db_file_query = tep_db_query("select admin_files_name from " . TABLE_ADMIN_FILES . " where FIND_IN_SET( '" . $_SESSION['login_groups_id'] . "', admin_groups_id) and admin_files_name = '" . $filename . "' and admin_files_is_boxes=0");
            if (!tep_db_num_rows($db_file_query)) {
                tep_redirect(tep_href_link(FILENAME_FORBIDEN));
            }
        }
    }
}

////
//Return 'true' or 'false' value to display boxes and files in index.php and column_left.php
function tep_admin_check_boxes($filename, $boxes = '') {
    global $login_groups_id;

    $is_boxes = 1;
    if ($boxes == 'sub_boxes') {
        $is_boxes = 0;
    }
    $dbquery = tep_db_query("select admin_files_id from " . TABLE_ADMIN_FILES . " where FIND_IN_SET( '" . $login_groups_id . "', admin_groups_id) and admin_files_is_boxes = '" . $is_boxes . "' and admin_files_name = '" . $filename . "'");

    $return_value = false;
    if (tep_db_num_rows($dbquery)) {
        $return_value = true;
    }
    return $return_value;
}

////
//Return files stored in box that can be accessed by user
function tep_admin_files_boxes($filename, $sub_box_name, $parameters = '') {
    global $login_groups_id;
    $sub_boxes = '';

    $dbquery = tep_db_query("select admin_files_name from " . TABLE_ADMIN_FILES . " where FIND_IN_SET( '" . $login_groups_id . "', admin_groups_id) and admin_files_is_boxes = '0' and admin_files_name = '" . $filename . "'");
    if (tep_db_num_rows($dbquery)) {
        $sub_boxes = '<a href="' . tep_href_link($filename, $parameters) . '" class="menuBoxContentLink">' . $sub_box_name . '</a><br>';
    }
    return $sub_boxes;
}

////
//Return files stored in box that can be accessed by user
function tep_admin_check_files($files_array) {
    $allowed_files = array();

    for ($i = 0; $i < count($files_array); $i++) {
        if (tep_admin_check_boxes($files_array[$i], 'sub_boxes')) {
            $allowed_files[] = $files_array[$i];
        }
    }

    return $allowed_files;
}

////
//Check whether a particular action of a file can be accessed by user
function tep_admin_files_actions($filename, $action_name) {
    global $login_groups_id;

    $files_actions_select_sql = "SELECT afa.admin_files_actions_id FROM " . TABLE_ADMIN_FILES_ACTIONS . " AS afa INNER JOIN " . TABLE_ADMIN_FILES . " AS af ON afa.admin_files_id=af.admin_files_id WHERE af.admin_files_name='" . $filename . "' AND af.admin_files_is_boxes=0 AND afa.admin_files_actions_key='" . $action_name . "' AND FIND_IN_SET( '" . $login_groups_id . "', afa.admin_groups_id)";
    $files_actions_result_sql = tep_db_query($files_actions_select_sql);
    if (tep_db_num_rows($files_actions_result_sql)) {
        return true;
    }
    return false;
}

////
//Get selected file for index.php
function tep_selected_file($filename) {
    global $login_groups_id;
    $randomize = FILENAME_ADMIN_ACCOUNT;

    $dbquery = tep_db_query("select admin_files_id as boxes_id from " . TABLE_ADMIN_FILES . " where FIND_IN_SET( '" . $login_groups_id . "', admin_groups_id) and admin_files_is_boxes = '1' and admin_files_name = '" . $filename . "'");
    if (tep_db_num_rows($dbquery)) {
        $boxes_id = tep_db_fetch_array($dbquery);
        $randomize_query = tep_db_query("select admin_files_name from " . TABLE_ADMIN_FILES . " where FIND_IN_SET( '" . $login_groups_id . "', admin_groups_id) and admin_files_is_boxes = '0' and admin_files_to_boxes = '" . $boxes_id['boxes_id'] . "'");
        if (tep_db_num_rows($randomize_query)) {
            $file_selected = tep_db_fetch_array($randomize_query);
            $randomize = $file_selected['admin_files_name'];
        }
    }
    return $randomize;
}

//Admin end
////
// Redirect to another page or site
function tep_redirect($url) {
    global $logger;

    header('Location: ' . $url);

    if (STORE_PAGE_PARSE_TIME == 'true') {
        if (!is_object($logger))
            $logger = new logger;
        $logger->timer_stop();
    }
    exit;
}

////
// Parse the data used in the html tags to ensure the tags will not break
function tep_parse_input_field_data($data, $parse) {
    return strtr(trim($data), $parse);
}

function tep_output_string($string, $translate = false, $protected = false) {
    if ($protected == true) {
        return htmlspecialchars($string);
    } else {
        if ($translate == false) {
            return tep_parse_input_field_data($string, array('"' => '&quot;'));
        } else {
            return tep_parse_input_field_data($string, $translate);
        }
    }
}

function tep_output_string_protected($string) {
    return tep_output_string($string, false, true);
}

function tep_sanitize_string($string) {
    $string = ereg_replace_dep(' +', ' ', $string);
    return preg_replace("/[<>]/", '_', $string);
}

function tep_customers_name($customers_id) {
    $customers = tep_db_query("select customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " where customers_id = '" . (int) $customers_id . "'");
    $customers_values = tep_db_fetch_array($customers);

    return $customers_values['customers_firstname'] . ' ' . $customers_values['customers_lastname'];
}

function tep_get_customers_email($customers_id) {
    $customers = tep_db_query("select customers_email_address from " . TABLE_CUSTOMERS . " where customers_id = '" . (int) $customers_id . "'");
    $customers_values = tep_db_fetch_array($customers);

    return $customers_values['customers_email_address'];
}

function tep_get_customers_groups($grp_id = '') {
    $cust_grp_array = array();

    $where_str = (tep_not_null($grp_id)) ? 'customers_groups_id="' . tep_db_input($grp_id) . '"' : ' 1 ';

    $customer_grp_select_sql = "SELECT customers_groups_id, customers_groups_name
                                FROM " . TABLE_CUSTOMERS_GROUPS . "
                                WHERE " . $where_str;
    $customer_grp_result_sql = tep_db_query($customer_grp_select_sql);

    while ($customer_grp_row = tep_db_fetch_array($customer_grp_result_sql)) {
        $cust_grp_array[] = array('id' => $customer_grp_row['customers_groups_id'],
            'name' => $customer_grp_row['customers_groups_name']);
    }

    return $cust_grp_array;
}

function tep_parse_email_string($email_string) {
    $email_array = tep_not_null($email_string) ? explode(',', $email_string) : array();
    $email_pattern = "/([^<]*?)(?:<)([^>]+)(?:>)/is";
    $email_receivers = array();

    for ($receiver_cnt = 0; $receiver_cnt < count($email_array); $receiver_cnt++) {
        if (preg_match($email_pattern, $email_array[$receiver_cnt], $regs)) {
            $receiver_name = trim($regs[1]);
            $receiver_email = trim($regs[2]);

            $email_receivers[] = array('name' => $receiver_name, 'email' => $receiver_email);
        }
    }

    return $email_receivers;
}

function tep_get_email_greeting($firstname, $lastname, $gender = '') {
    if (ACCOUNT_GENDER == 'true') {
        if ($gender == 'm') {
            $email_greeting = sprintf(EMAIL_GREET_MR, $lastname);
        } else if ($gender == 'f') {
            $email_greeting = sprintf(EMAIL_GREET_MS, $lastname);
        } else {
            $email_greeting = sprintf(EMAIL_GREET_NONE, $firstname);
        }
    } else {
        $email_greeting = sprintf(EMAIL_GREET_NONE, $firstname);
    }

    return $email_greeting;
}

function tep_get_email_greeting_english($firstname, $lastname, $gender = '') {
    if (ACCOUNT_GENDER == 'true') {
        if ($gender == 'm') {
            $email_greeting = sprintf(EN_EMAIL_GREET_MR, $lastname);
        } else if ($gender == 'f') {
            $email_greeting = sprintf(EN_EMAIL_GREET_MS, $lastname);
        } else {
            $email_greeting = sprintf(EN_EMAIL_GREET_NONE, $firstname);
        }
    } else {
        $email_greeting = sprintf(EN_EMAIL_GREET_NONE, $firstname);
    }

    return $email_greeting;
}

function tep_get_email_greeting_chinese($firstname, $lastname, $gender = '') {
    if (ACCOUNT_GENDER == 'true') {
        if ($gender == 'm') {
            $email_greeting = sprintf(EMAIL_GREET_MR_CHINESE, $lastname);
        } else if ($gender == 'f') {
            $email_greeting = sprintf(EMAIL_GREET_MS_CHINESE, $lastname);
        } else {
            $email_greeting = sprintf(EMAIL_GREET_NONE_CHINESE, $firstname);
        }
    } else {
        $email_greeting = sprintf(EMAIL_GREET_NONE_CHINESE, $firstname);
    }

    return $email_greeting;
}

function tep_gen_random_serial($email, $searchTable = "", $searchField = "", $prefix = "", $str_length = "") {
    $again = true;
    while ($again) {
        $time = time(getdate());
        $random_key = rand(1, 99999);

        $value = md5($email . $time . $random_key . $prefix);

        if (tep_not_null($str_length) && is_numeric($str_length)) {
            $value = substr($value, 0, $str_length);
        }

        $sql = "SELECT " . $searchField . " FROM " . $searchTable . " WHERE " . $searchField . "='" . $value . "'";
        $result = tep_db_query($sql);

        if (tep_db_num_rows($result)) {
            $again = true;
        } else {
            $again = false;
        }
    }
    return $value;
}

function tep_gen_random_pin() {
    $pin_number_max_length = 6;

    $plain = '';
    for ($i = 0; $i < $pin_number_max_length; $i++) {
        $plain .= tep_rand(0, 9);
    }
    $encrypted = tep_encrypt_password($plain);
    return array($encrypted, $plain);
}

function tep_get_path($current_category_id = '', $value_pair = true) {
    global $cPath_array;

    if ($current_category_id == '') {
        $path_array = is_array($cPath_array[0]) ? $cPath_array[0] : $cPath_array;
        if (is_array($path_array)) {
            $cPath_new = implode('_', array_reverse($path_array));
        }
    } else {
        if (sizeof($cPath_array) == 0) {
            $path_array = array($current_category_id);
            $current_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $current_category_id . "' AND parent_id<>0");
            while ($current_category = tep_db_fetch_array($current_category_query)) {
                $path_array[] = $current_category["parent_id"];
                $current_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $current_category["parent_id"] . "' AND parent_id<>0");
            }
            $cPath_new = implode('_', array_reverse($path_array));
        } else {
            $cPath_new = '';
            $last_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $cPath_array[(sizeof($cPath_array) - 1)] . "'");
            $last_category = tep_db_fetch_array($last_category_query);

            $current_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = '" . (int) $current_category_id . "'");
            $current_category = tep_db_fetch_array($current_category_query);

            if ($last_category['parent_id'] == $current_category['parent_id']) {
                for ($i = 0, $n = sizeof($cPath_array) - 1; $i < $n; $i++) {
                    $cPath_new .= '_' . $cPath_array[$i];
                }
            } else {
                for ($i = 0, $n = sizeof($cPath_array); $i < $n; $i++) {
                    $cPath_new .= '_' . $cPath_array[$i];
                }
            }

            $cPath_new .= '_' . $current_category_id;
            if (substr($cPath_new, 0, 1) == '_') {
                $cPath_new = substr($cPath_new, 1);
            }
        }
    }
    if ($value_pair)
        return 'cPath=' . $cPath_new;
    else
        return $cPath_new;
}

function tep_get_all_get_params($exclude_array = '') {
    global $HTTP_GET_VARS;

    if ($exclude_array == '')
        $exclude_array = array();

    $get_url = '';

    reset($HTTP_GET_VARS);
    while (list($key, $value) = each($HTTP_GET_VARS)) {
        if (($key != tep_session_name()) && ($key != 'error') && (!in_array($key, $exclude_array)))
            $get_url .= $key . '=' . $value . '&';
    }

    return $get_url;
}

function tep_date_long($raw_date, $date_format = DATE_FORMAT_LONG) {
    if (($raw_date == '0000-00-00 00:00:00') || ($raw_date == ''))
        return false;

    $year = (int) substr($raw_date, 0, 4);
    $month = (int) substr($raw_date, 5, 2);
    $day = (int) substr($raw_date, 8, 2);
    $hour = (int) substr($raw_date, 11, 2);
    $minute = (int) substr($raw_date, 14, 2);
    $second = (int) substr($raw_date, 17, 2);

    return strftime($date_format, mktime($hour, $minute, $second, $month, $day, $year));
}

////
// Output a raw date string in the selected locale date format
// $raw_date needs to be in this format: YYYY-MM-DD HH:MM:SS
// NOTE: Includes a workaround for dates before 01/01/1970 that fail on windows servers
function tep_date_short($raw_date, $format = DATE_FORMAT) {
    if (($raw_date == '0000-00-00 00:00:00') || ($raw_date == ''))
        return false;

    $year = substr($raw_date, 0, 4);
    $month = (int) substr($raw_date, 5, 2);
    $day = (int) substr($raw_date, 8, 2);
    $hour = (int) substr($raw_date, 11, 2);
    $minute = (int) substr($raw_date, 14, 2);
    $second = (int) substr($raw_date, 17, 2);

    if (@date('Y', mktime($hour, $minute, $second, $month, $day, $year)) == $year) {
        return date($format, mktime($hour, $minute, $second, $month, $day, $year));
    } else {
        return ereg_replace_dep('2037' . '$', $year, date($format, mktime($hour, $minute, $second, $month, $day, 2037)));
    }
}

function tep_datetime_short($raw_datetime, $format = DATE_TIME_FORMAT) {
    if (($raw_datetime == '0000-00-00 00:00:00') || ($raw_datetime == ''))
        return false;

    $year = (int) substr($raw_datetime, 0, 4);
    $month = (int) substr($raw_datetime, 5, 2);
    $day = (int) substr($raw_datetime, 8, 2);
    $hour = (int) substr($raw_datetime, 11, 2);
    $minute = (int) substr($raw_datetime, 14, 2);
    $second = (int) substr($raw_datetime, 17, 2);

    return strftime($format, mktime($hour, $minute, $second, $month, $day, $year));
}

function tep_date_delta($ts_start_date, $ts_end_date) {
    /*     * ****************************************************************
      Both date parameter is an array in the form:
      (hour, minute, second, month, day, year)
     * **************************************************************** */
    $secs_in_day = 86400;

    $i_years = $ts_end_date["Y"] - $ts_start_date["Y"];
    $i_months = $ts_end_date["m"] - $ts_start_date["m"];
    $i_days = $ts_end_date["d"] - $ts_start_date["d"];
    if ($i_days < 0)
        $i_months--;
    if ($i_months < 0) {
        $i_years--;
        $i_months += 12;
    }
    if ($i_days < 0) {
        $i_days = date('t', mktime(0, 0, 0, $ts_start_date["m"], 1, date('Y'))) - $ts_start_date["d"];
        $i_days += $ts_end_date["d"];
    }

    /*
      # calculate HMS delta
      $f_delta = $ts_end_date - $ts_start_date;
      $f_secs = $f_delta % $secs_in_day;
      $f_secs -= ($i_secs = $f_secs % 60);
      $i_mins = intval($f_secs/60)%60;
      $f_secs -= $i_mins * 60;
      $i_hours = intval($f_secs/3600);
     */
    //return array($i_years, $i_months, $i_days, $i_hours, $i_mins, $i_secs);
    return array($i_years, $i_months, $i_days, 0, 0, 0);
}

function tep_calculate_age($s_start_date, $s_end_date = '', $b_show_days = 0, $b_show_time = false) {
    if ($b_show_time == true) {
        $b_show_time = strlen($s_start_date > 10) ? true : false;
    }

    $ts_start_date = array('H' => substr($s_start_date, 11, 2),
        'i' => substr($s_start_date, 14, 2),
        's' => substr($s_start_date, 17, 2),
        'm' => substr($s_start_date, 5, 2),
        'd' => substr($s_start_date, 8, 2),
        'Y' => substr($s_start_date, 0, 4));
    if ($s_end_date) {
        $ts_end_date = array('H' => substr($s_end_date, 11, 2),
            'i' => substr($s_end_date, 14, 2),
            's' => substr($s_end_date, 17, 2),
            'm' => substr($s_end_date, 5, 2),
            'd' => substr($s_end_date, 8, 2),
            'Y' => substr($s_end_date, 0, 4));
    } else {
        $ts_end_date = array('H' => date('H'),
            'i' => date('i'),
            's' => date('s'),
            'm' => date('m'),
            'd' => date('d'),
            'Y' => date('Y'));
    }

    if (checkdate($ts_start_date['m'], $ts_start_date['d'], $ts_start_date['Y']) == false || checkdate($ts_end_date['m'], $ts_end_date['d'], $ts_end_date['Y']) == false) {
        return 'Invalid Date';
    }

    list ($i_age_years, $i_age_months, $i_age_days, $i_age_hours, $i_age_mins, $i_age_secs) = tep_date_delta($ts_start_date, $ts_end_date);

    // output
    $s_age = '';
    if ($i_age_years)
        $s_age .= "$i_age_years yr";
    if ($i_age_months)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_months mth";
    if ($b_show_days && $i_age_days) {
        $s_age .= ($s_age ? ', ' : '') . "$i_age_days day" . (abs($i_age_days) > 1 ? 's' : '');
    }

    if ($b_show_time && $i_age_hours)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_hours hour" . (abs($i_age_hours) > 1 ? 's' : '');
    if ($b_show_time && $i_age_mins)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_mins minute" . (abs($i_age_mins) > 1 ? 's' : '');
    if ($b_show_time && $i_age_secs)
        $s_age .= ($s_age ? ', ' : '') . "$i_age_secs second" . (abs($i_age_secs) > 1 ? 's' : '');

    if (!tep_not_null($s_age)) {
        $s_age = $b_show_days ? '0 day' : '0 mth';
    }

    return $s_age;
}

function tep_day_diff($start_date_time, $end_date_time, $result_unit = 'day') {
    list($s_date_str, $s_time_str) = explode(' ', $start_date_time);
    list($s_yr, $s_mth, $s_day) = explode('-', $s_date_str);
    list($s_hr, $s_min, $s_sec) = explode(':', $s_time_str);

    list($e_date_str, $e_time_str) = explode(' ', $end_date_time);
    list($e_yr, $e_mth, $e_day) = explode('-', $e_date_str);
    list($e_hr, $e_min, $e_sec) = explode(':', $e_time_str);

    if (checkdate((int) $s_mth, (int) $s_day, (int) $s_yr) == false || checkdate((int) $e_mth, (int) $e_day, (int) $e_yr) == false) {
        return false;
    }

    $start_time_secs = mktime((int) $s_hr, (int) $s_min, (int) $s_sec, $s_mth, $s_day, $s_yr);
    $end_time_secs = mktime((int) $e_hr, (int) $e_min, (int) $e_sec, $e_mth, $e_day, $e_yr);
    $daydiff = $end_time_secs - $start_time_secs;

    if ($daydiff < 0) {
        return false;
    }
    if ($result_unit == 'day') {
        return $daydiff / 86400;
    } else if ($result_unit == 'sec') {
        return $daydiff;
    }
}

function tep_sec_to_daytime($secs) {
    if ($days = intval((floor($secs / 86400))))
        $secs = $secs % 86400;
    if ($hours = intval((floor($secs / 3600))))
        $secs = $secs % 3600;
    if ($minutes = intval((floor($secs / 60))))
        $secs = $secs % 60;
    $secs = intval($secs);

    $result = tep_not_null($days) ? $days . 'd ' : '';
    $result .= tep_not_null($hours) ? strlen($hours) == 1 ? '0' . $hours . ':' : $hours . ':'  : '00:';
    $result .= tep_not_null($minutes) ? strlen($minutes) == 1 ? '0' . $minutes . ':' : $minutes . ':'  : '00:';
    $result .= tep_not_null($secs) ? strlen($secs) == 1 ? '0' . $secs : $secs  : '00';
    return $result;
}

function tep_datetime_to_string($secs, $type = "1") {
    if ($secs < 0)
        return false;
    $s = $secs % 60;
    $m = (int) ($secs / 60);
    $m_to_h = (int) ($m / 60);
    $m = $m_to_h > 0 ? $m - ($m_to_h * 60) : $m;

    $h = $m_to_h;

    $h_to_d = (int) ($h / 24);
    $h = $h_to_d > 0 ? $h - ($h_to_d * 24) : $h;

    $d = $h_to_d;

    switch ($type) {
        case 1:
            return $d . 'd ' . $h . 'h ' . $m . 'm';
            break;
        case 2:
            return $h . 'h ' . $m . 'm';
            break;
    }
}

function tep_string_output($string, $num, $style_array) {
    for ($array_count = sizeof($style_array) - 1; $array_count >= 0; $array_count--) {
        if ($num >= $style_array[$array_count]["time"]) {
            $string_output_result = '<span class="' . $style_array[$array_count]["color"] . '">' . $string . '</span>';
            break;
        }
    }

    return $string_output_result;
}

function tep_get_category_buyback_tree($parent_id = '0', $spacing = '', $exclude = '', $category_tree_array = '', $include_itself = false, $level = 0) {
    if (!is_array($category_tree_array))
        $category_tree_array = array();

    if ((sizeof($category_tree_array) < 1) && ($exclude != '0'))
        $category_tree_array[] = array('id' => '0', 'text' => TEXT_TOP);

    if ($include_itself) {
        $categories_name = tep_get_categories_name($parent_id);
        $category_tree_array[] = array('id' => $parent_id, 'text' => str_repeat($spacing, $level) . strip_tags($categories_name));
        $skip = array($parent_id);
    } else {
        $skip = array();
    }

    tep_get_sub_cat_buyback_tree($parent_id, $category_tree_array, $level, $spacing, $skip, false);

    return $category_tree_array;
}

function tep_get_sub_cat_buyback_tree($category_id, &$cat_id_array, $level, $spacing = '', $skip = '', $by_level = true) {
    global $default_languages_id, $languages_id;

    if ($by_level) {
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
                                    FROM " . TABLE_CATEGORIES . " AS c
                                    LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                            ON c.categories_id=cd.categories_id
                                    WHERE c.parent_id = '" . $category_id . "'
                                            AND cd.categories_name <> ''
                                            AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))";
        $sub_category_query = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_query)) {
            tep_get_sub_cat_buyback_tree($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing);
            $cat_id_array[$level][] = array("id" => $sub_category["categories_id"], 'text' => strip_tags($sub_category["categories_name"]));
        }
    } else {
        if (is_array($skip) && !in_array($category_id, $skip)) {
            $cat_select_sql = "	SELECT c.categories_id, cd.categories_name
                                FROM " . TABLE_CATEGORIES . " AS c
                                LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                    ON c.categories_id=cd.categories_id
                                WHERE c.categories_id = '" . $category_id . "'
                                    AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                    AND cd.categories_name <> ''";
            $cat_result_sql = tep_db_query($cat_select_sql);
            if ($cat_row = tep_db_fetch_array($cat_result_sql)) {
                $cat_id_array[] = array("id" => $category_id, 'text' => ($level > 1 ? str_repeat($spacing, $level - 1) . strip_tags($cat_row["categories_name"]) : strip_tags($cat_row["categories_name"])));
            }
        }
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
                                    FROM " . TABLE_CATEGORIES . " AS c
                                    LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                        ON c.categories_id = cd.categories_id
                                    WHERE c.parent_id = '" . $category_id . "'
                                        AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                        AND cd.categories_name <> ''
                                    ORDER BY c.sort_order, cd.categories_name";
        $sub_category_result_sql = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_result_sql)) {
            tep_get_sub_cat_buyback_tree($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing, $skip, false);
        }
    }
    return true;
}

function tep_get_category_tree($parent_id = '0', $spacing = '', $exclude = '', $category_tree_array = '', $include_itself = false, $level = 0, $append_title = false, $custom_product = '') {
    global $default_languages_id, $languages_id, $memcache_obj;
    $array_original_size = 0;

    $cache_key = TABLE_CATEGORIES_DESCRIPTION . '/categories_name/array/parent_id/' . $parent_id;
    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $category_tree_array = $cache_result;
    } else {
        if (!is_array($category_tree_array))
            $category_tree_array = array();
        if ($append_title) {
            $category_tree_array[] = array('id' => '', "text" => "--------------- Select Categories ---------------");
            $array_original_size++;
        }
        if ((sizeof($category_tree_array) < $array_original_size + 1) && ($exclude != '0'))
            $category_tree_array[] = array('id' => '0', 'text' => TEXT_TOP);

        if ($include_itself) {
            $category_query = tep_db_query("SELECT cd.categories_name
                                        FROM " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                        WHERE (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                                AND cd.categories_name <> ''
                                                AND cd.categories_id = '" . (int) $parent_id . "'");
            $category = tep_db_fetch_array($category_query);
            $category_tree_array[] = array('id' => $parent_id, 'text' => str_repeat($spacing, $level) . strip_tags($category['categories_name']));
            $skip = array($parent_id);
        } else {
            $skip = array();
        }

        tep_get_sub_cat_tree($parent_id, $category_tree_array, $level, $spacing, $skip, false, $custom_product);
        $memcache_obj->store($cache_key, $category_tree_array, 7200);
    }

    return $category_tree_array;
}

function tep_get_eligible_category_tree_cacheable($filename, $parent_id = '0', $spacing = '', $exclude = '', $category_tree_array = '', $include_itself = false, $level = 0, $append_title = false, $expand_parent_cat = '') {
    global $default_languages_id, $languages_id, $memcache_obj;
    $array_original_size = 0;

    if (!is_array($category_tree_array))
        $category_tree_array = array();
    $md5_value = md5($filename . '|' . $spacing . '|' . $exclude . '|' . $category_tree_array . '|' . $include_itself . '|' . $level . '|' . $append_title . '|' . $expand_parent_cat);

    $cache_key = TABLE_CATEGORIES . '/eligible_category_tree/array/categories_id/' . $parent_id . '/language/' . $languages_id . '/fn_param_md5/' . $md5_value;

    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $category_tree_array = $cache_result;
    } else {
        if ($append_title) {
            $category_tree_array[] = array('id' => '', "text" => "--------------- Select Categories ---------------");
            $array_original_size++;
        }
        if ((sizeof($category_tree_array) < $array_original_size + 1) && ($exclude != '0')) {
            if (($p_rank = tep_check_cat_tree_permissions($filename, 0)) > 0) {
                $category_tree_array[] = array('id' => '0', 'text' => TEXT_TOP, 'param' => ($p_rank == 2 ? '' : ''));
            }
        }

        if ($include_itself) {
            $category_query = tep_db_query("SELECT cd.categories_name FROM " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                            WHERE (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                                AND cd.categories_name <> ''
                                                AND cd.categories_id = '" . (int) $parent_id . "'
                                            ORDER BY c.sort_order, cd.categories_name");
            $category = tep_db_fetch_array($category_query);
            if (($p_rank = tep_check_cat_tree_permissions($filename, $parent_id)) > 0) {
                $category_tree_array[] = array('id' => $parent_id,
                    'text' => str_repeat($spacing, $level) . strip_tags($category['categories_name']),
                    'param' => $p_rank == 2 ? '' : '');
            }
            $skip = array($parent_id);
        } else {
            $skip = array();
        }

        if (trim($expand_parent_cat) != '') { // Only expand the section of currently viewed accessed game
            $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
                                        FROM " . TABLE_CATEGORIES . " AS c
                                        LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                                ON c.categories_id = cd.categories_id
                                        WHERE c.parent_id = '" . $parent_id . "'
                                                AND cd.categories_name <> ''
                                                AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                        ORDER BY c.sort_order, cd.categories_name";
            $sub_category_result_sql = tep_db_query($sub_category_select_sql);

            while ($sub_category_row = tep_db_fetch_array($sub_category_result_sql)) {
                if ($sub_category_row['categories_id'] == $expand_parent_cat) {
                    tep_get_eligible_sub_cat_tree($filename, $sub_category_row['categories_id'], $category_tree_array, $level + 1, $spacing, $skip, false);
                } else {
                    if (($p_rank = tep_check_cat_tree_permissions($filename, $sub_category_row['categories_id'])) > 0) {
                        $category_tree_array[] = array('id' => $sub_category_row['categories_id'],
                            'text' => str_repeat($spacing, $level) . strip_tags($sub_category_row["categories_name"]),
                            'param' => $p_rank == 2 ? '' : '');
                    }
                }
            }
        } else {
            tep_get_eligible_sub_cat_tree($filename, $parent_id, $category_tree_array, $level, $spacing, $skip, false);
        }

        $memcache_obj->store($cache_key, $category_tree_array, 7200);
    }

    return $category_tree_array;
}

function tep_get_eligible_category_tree($filename, $parent_id = '0', $spacing = '', $exclude = '', $category_tree_array = '', $include_itself = false, $level = 0, $append_title = false, $expand_parent_cat = '') {
    global $default_languages_id, $languages_id;
    $array_original_size = 0;

    if (!is_array($category_tree_array))
        $category_tree_array = array();
    if ($append_title) {
        $category_tree_array[] = array('id' => '', "text" => "--------------- Select Categories ---------------");
        $array_original_size++;
    }
    if ((sizeof($category_tree_array) < $array_original_size + 1) && ($exclude != '0')) {
        if (($p_rank = tep_check_cat_tree_permissions($filename, 0)) > 0) {
            $category_tree_array[] = array('id' => '0', 'text' => TEXT_TOP, 'param' => ($p_rank == 2 ? '' : ''));
        }
    }

    if ($include_itself) {
        $category_query = tep_db_query("SELECT cd.categories_name FROM " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                        WHERE (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                                AND cd.categories_name <> ''
                                                AND cd.categories_id = '" . (int) $parent_id . "'
                                        ORDER BY c.sort_order, cd.categories_name");
        $category = tep_db_fetch_array($category_query);
        if (($p_rank = tep_check_cat_tree_permissions($filename, $parent_id)) > 0) {
            $category_tree_array[] = array('id' => $parent_id,
                'text' => str_repeat($spacing, $level) . strip_tags($category['categories_name']),
                'param' => $p_rank == 2 ? '' : '');
        }
        $skip = array($parent_id);
    } else {
        $skip = array();
    }


    // HLA : orders.php compensate exclude HLA products and categories
    if ($filename == FILENAME_ORDERS) {
        $products_hla_select_sql = "SELECT ptc.categories_id
                                    FROM " . TABLE_PRODUCTS . " AS p
                                    LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS ptc
                                            ON ptc.products_id = p.products_id
                                    WHERE p.custom_products_type_id = 4";
        $products_hla_result_sql = tep_db_query($products_hla_select_sql);
        while ($products_hla_row = tep_db_fetch_array($products_hla_result_sql)) {
            $skip[] = $products_hla_row['categories_id'];
        }
    }


    if (trim($expand_parent_cat) != '') { // Only expand the section of currently viewed accessed game
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
                                    FROM " . TABLE_CATEGORIES . " AS c
                                    LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                            ON c.categories_id = cd.categories_id
                                    WHERE c.parent_id = '" . $parent_id . "'
                                            AND cd.categories_name <> ''
                                            AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                    ORDER BY cd.categories_name, c.sort_order";
        $sub_category_result_sql = tep_db_query($sub_category_select_sql);

        while ($sub_category_row = tep_db_fetch_array($sub_category_result_sql)) {
            if ($sub_category_row['categories_id'] == $expand_parent_cat) {
                tep_get_eligible_sub_cat_tree($filename, $sub_category_row['categories_id'], $category_tree_array, $level + 1, $spacing, $skip, false);
            } else {
                if (($p_rank = tep_check_cat_tree_permissions($filename, $sub_category_row['categories_id'])) > 0) {
                    $category_tree_array[] = array('id' => $sub_category_row['categories_id'],
                        'text' => str_repeat($spacing, $level) . strip_tags($sub_category_row["categories_name"]),
                        'param' => $p_rank == 2 ? '' : '');
                }
            }
        }
    } else {
        tep_get_eligible_sub_cat_tree($filename, $parent_id, $category_tree_array, $level, $spacing, $skip, false);
    }

    return $category_tree_array;
}

function tep_get_eligible_categories($filename, $categories_array = '', $parent_id = '0', $include_itself = false) {
    global $default_languages_id, $languages_id;

    if (!is_array($categories_array))
        $categories_array = array();

    if ($include_itself) {
        if (tep_check_cat_tree_permissions($filename, $parent_id) == 1) {
            $categories_array[] = $parent_id;
        }
    }

    $categories_query = tep_db_query("	SELECT c.categories_id, cd.categories_name
                                        FROM " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd
                                        WHERE parent_id = '" . (int) $parent_id . "'
                                                AND c.categories_id = cd.categories_id
                                                AND cd.categories_name <> ''
                                                AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                        ORDER BY sort_order, cd.categories_name");
    while ($categories = tep_db_fetch_array($categories_query)) {
        if (($p_rank = tep_check_cat_tree_permissions($filename, $categories['categories_id'])) > 0) {
            if ($p_rank == 1)
                $categories_array[] = $categories['categories_id'];

            if ($categories['categories_id'] != $parent_id) {
                $categories_array = tep_get_eligible_categories($filename, $categories_array, $categories['categories_id'], false);
            }
        }
    }

    return $categories_array;
}

function tep_get_ineligible_categories($filename, $categories_array = '', $parent_id = '0', $include_itself = false) {
    global $default_languages_id, $languages_id;

    if (!is_array($categories_array))
        $categories_array = array();

    if ($include_itself) {
        if (tep_check_cat_tree_permissions($filename, $parent_id) == 0) {
            $categories_array[] = $parent_id;
        }
    }

    $categories_query = tep_db_query("	SELECT c.categories_id, cd.categories_name
                                        FROM " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd
                                        WHERE parent_id = '" . (int) $parent_id . "'
                                                AND c.categories_id = cd.categories_id
                                                AND cd.categories_name <> ''
                                                AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                        ORDER BY sort_order, cd.categories_name");
    while ($categories = tep_db_fetch_array($categories_query)) {
        if ($categories['categories_id'] == 0) {
            break;
        } else if (tep_check_cat_tree_permissions($filename, $categories['categories_id']) == 0) {
            $categories_array[] = $categories['categories_id'];
            break;
        }

        if ($categories['categories_id'] != $parent_id) {
            $categories_array = tep_get_ineligible_categories($filename, $categories_array, $categories['categories_id'], false);
        }
    }

    return $categories_array;
}

function tep_get_admin_file_cat_permissions($filename, $admin_group_id = '') {
    global $login_groups_id;

    $login_groups_cat_perms = array();

    if (!tep_not_null($admin_group_id)) {
        $admin_group_id = $login_groups_id;
    }

    $cat_perms_select_sql = "	SELECT categories_ids
                                FROM " . TABLE_ADMIN_FILES_CATEGORIES . " AS afc
                                INNER JOIN " . TABLE_ADMIN_FILES . " AS af
                                        ON (afc.admin_files_id=af.admin_files_id AND af.admin_files_name='" . tep_db_input($filename) . "' AND af.admin_files_is_boxes=0)
                                WHERE afc.admin_groups_id = '" . tep_db_input($admin_group_id) . "' ";
    $cat_perms_result_sql = tep_db_query($cat_perms_select_sql);
    $cat_perms_row = tep_db_fetch_array($cat_perms_result_sql);

    if (trim($cat_perms_row['categories_ids']) != '') {
        $login_groups_cat_perms = explode(',', $cat_perms_row['categories_ids']);
    }

    return $login_groups_cat_perms;
}

function tep_check_cat_tree_permissions($filename, $cat_id, $admin_group_id = '') {
    global $login_groups_id;

    if (!tep_not_null($admin_group_id)) {
        $admin_group_id = $login_groups_id;
    }

    $cat_perms_select_sql = "	SELECT categories_ids
                                FROM " . TABLE_ADMIN_FILES_CATEGORIES . " AS afc
                                INNER JOIN " . TABLE_ADMIN_FILES . " AS af
                                        ON (afc.admin_files_id=af.admin_files_id AND af.admin_files_name='" . tep_db_input($filename) . "' AND af.admin_files_is_boxes=0)
                                WHERE afc.admin_groups_id = '" . tep_db_input($admin_group_id) . "' ";
    $cat_perms_result_sql = tep_db_query($cat_perms_select_sql);
    $cat_perms_row = tep_db_fetch_array($cat_perms_result_sql);

    if (trim($cat_perms_row['categories_ids']) != '') {
        $login_groups_cat_perms = explode(',', $cat_perms_row['categories_ids']);
    } else {
        $login_groups_cat_perms = array();
    }

    if (count($login_groups_cat_perms) > 0) {
        if (in_array(0, $login_groups_cat_perms)) {
            return 1;
        } else if (in_array($cat_id, $login_groups_cat_perms)) {
            return 1;
        } else {
            unset($subcategories_array);
            tep_get_subcategories($subcategories_array, $cat_id);
            if (count($subcategories_array)) {
                $child_got_access = array_intersect($login_groups_cat_perms, $subcategories_array);
                if (count($child_got_access) > 0) {
                    return 2;
                }
            }

            unset($parent_categories_array);
            tep_get_parent_categories($parent_categories_array, $cat_id);
            if (count($parent_categories_array)) {
                $parent_got_access = array_intersect($login_groups_cat_perms, $parent_categories_array);
                if (count($parent_got_access) > 0) {
                    return 1;
                }
            }
        }
    }
    return 0;
}

function tep_options_name($options_id) {
    global $default_languages_id, $languages_id;

    $options = tep_db_query("select products_options_name from " . TABLE_PRODUCTS_OPTIONS . "
                                where products_options_id = '" . (int) $options_id . "'
                                        and (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                        and products_options_name <> ''");
    $options_values = tep_db_fetch_array($options);

    return $options_values['products_options_name'];
}

function tep_values_name($values_id) {
    global $languages_id;

    $values = tep_db_query("select products_options_values_name from " . TABLE_PRODUCTS_OPTIONS_VALUES . "
                            where products_options_values_id = '" . (int) $values_id . "'
                                    and (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                    and products_options_values_name <> ''");
    $values_values = tep_db_fetch_array($values);

    return $values_values['products_options_values_name'];
}

function tep_info_image($image, $alt, $width = '', $height = '', $ext_param = '') {
    $return_image = '';

    if (tep_not_null($ext_param) && is_array($ext_param)) {
        require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

        $aws_obj = new ogm_amazon_ws();
        $aws_obj->set_bucket_key($ext_param['bucket']);
        $aws_obj->set_filepath($ext_param['filepath']);

        $return_image = $aws_obj->get_image_info($image);
        if (tep_not_null($return_image)) {
            $return_image = tep_image($return_image['src'], $alt, $width, $height);
        }

        unset($aws_obj);
    }

    if (!tep_not_null($return_image)) {
        if ((file_exists(DIR_FS_CATALOG_IMAGES . $image))) {
            $return_image = tep_image(DIR_WS_CATALOG_IMAGES . $image, $alt, $width, $height);
        } else {
            $return_image = TEXT_IMAGE_NONEXISTENT;
        }
    }

    return $return_image;
}

function tep_break_string($string, $len, $break_char = '-') {
    $l = 0;
    $output = '';
    for ($i = 0, $n = strlen($string); $i < $n; $i++) {
        $char = substr($string, $i, 1);
        if ($char != ' ') {
            $l++;
        } else {
            $l = 0;
        }
        if ($l > $len) {
            $l = 1;
            $output .= $break_char;
        }
        $output .= $char;
    }

    return $output;
}

function tep_get_countries_info($seek_value, $fieldname = 'countries_name') {
    $country_info_select_sql = "select * from " . TABLE_COUNTRIES . " where $fieldname = '" . tep_db_input($seek_value) . "'";
    $country_info_result_sql = tep_db_query($country_info_select_sql);
    $country_info_row = tep_db_fetch_array($country_info_result_sql);

    $country_info = array("id" => $country_info_row["countries_id"],
        "countries_name" => $country_info_row["countries_name"],
        "countries_iso_code_2" => $country_info_row['countries_iso_code_2'],
        "countries_iso_code_3" => $country_info_row['countries_iso_code_3'],
        "address_format_id" => $country_info_row['address_format_id'],
        "aft_risk_type" => $country_info_row['aft_risk_type']);

    return $country_info;
}

function tep_get_ip_country_info($ip_address) {
    require_once(DIR_FS_CATALOG_MODULES . 'anti_fraud/maxmind/db/geoip.inc');

    $country_info = array();

    if (file_exists(DIR_FS_CATALOG_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat') && is_readable(DIR_FS_CATALOG_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat')) {
        $gi = geoip_open(DIR_FS_CATALOG_MODULES . 'anti_fraud/maxmind/db/GeoIP.dat', GEOIP_STANDARD);
        $countries_iso_code_2 = geoip_country_code_by_addr($gi, $ip_address); //'************'

        if (tep_not_null($countries_iso_code_2)) {
            $country_info = tep_get_countries_info($countries_iso_code_2, 'countries_iso_code_2');
        }
    }

    return $country_info;
}

function tep_show_ip($ip_address, $link_text = '') {
    if (!tep_not_null($link_text))
        $link_text = $ip_address;

    return '<a href="https://iphub.info/?ip=' . $ip_address . '" target="_blank">' . $link_text . '</a>';
}

function tep_get_country($country_id) {
    $country_query = tep_db_query("select * from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $country_id . "'");

    if (!tep_db_num_rows($country_query)) {
        return $country_id;
    } else {
        $country = tep_db_fetch_array($country_query);
        return $country;
    }
}

function tep_get_country_name($country_id) {
    $country_query = tep_db_query("select countries_name from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $country_id . "'");

    if (!tep_db_num_rows($country_query)) {
        return $country_id;
    } else {
        $country = tep_db_fetch_array($country_query);
        return $country['countries_name'];
    }
}

function tep_get_zone_name($country_id, $zone_id, $default_zone) {
    $zone_query = tep_db_query("select zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int) $country_id . "' and zone_id = '" . (int) $zone_id . "'");
    if (tep_db_num_rows($zone_query)) {
        $zone = tep_db_fetch_array($zone_query);
        return $zone['zone_name'];
    } else {
        return $default_zone;
    }
}

function tep_get_po_company_name($po_company_id) {
    $company_query = tep_db_query("SELECT po_company_code, po_company_name "
            . "FROM " . TABLE_PO_COMPANY . " WHERE po_company_id = '" . (int) $po_company_id . "'");

    if (!tep_db_num_rows($company_query)) {
        return $po_company_id;
    } else {
        $company_result = tep_db_fetch_array($company_query);
        $company = "[".$company_result['po_company_code']."] ".$company_result['po_company_name'];
        return $company;
    }
}

function tep_not_null($value) {
    if (is_array($value)) {
        if (sizeof($value) > 0) {
            return true;
        } else {
            return false;
        }
    } else {
        if ((is_string($value) || is_numeric($value)) && ($value != '') && ($value !== 'NULL') && (strlen(trim($value)) > 0)) {
            return true;
        } else {
            return false;
        }
    }
}

function tep_not_empty($value) {
    if (is_array($value)) {
        if (sizeof($value) > 0) {
            return true;
        } else {
            return false;
        }
    } else {
        if (($value !== '') && (strtolower($value) != 'null') && (strlen(trim($value)) > 0)) {
            return true;
        } else {
            return false;
        }
    }
}

function filter_empty_val($var) {
    return tep_not_null($var);
}

function tep_browser_detect($component) {
    global $HTTP_USER_AGENT;
    return stristr($HTTP_USER_AGENT, $component);
}

function tep_tax_classes_pull_down($name, $tax_class_id = '', $parameters = '') {
    $tax_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from " . TABLE_TAX_CLASS . " order by tax_class_title");
    while ($tax_class = tep_db_fetch_array($tax_class_query)) {
        $tax_class_array[] = array('id' => $tax_class['tax_class_id'],
            'text' => $tax_class['tax_class_title']);
    }

    return tep_draw_pull_down_menu($name, $tax_class_array, $tax_class_id, $parameters);
}

function tep_geo_zones_pull_down($name, $zone_class_id = '', $parameters = '') {
    $zone_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $zone_class_query = tep_db_query("select geo_zone_id, geo_zone_name from " . TABLE_GEO_ZONES . " order by geo_zone_name");
    while ($zone_class = tep_db_fetch_array($zone_class_query)) {
        $zone_class_array[] = array('id' => $zone_class['geo_zone_id'],
            'text' => $zone_class['geo_zone_name']);
    }

    return tep_draw_pull_down_menu($name, $zone_class_array, $zone_class_id, $parameters);
}

function tep_get_geo_zone_name($geo_zone_id) {
    $zones_query = tep_db_query("select geo_zone_name from " . TABLE_GEO_ZONES . " where geo_zone_id = '" . (int) $geo_zone_id . "'");

    if (!tep_db_num_rows($zones_query)) {
        $geo_zone_name = $geo_zone_id;
    } else {
        $zones = tep_db_fetch_array($zones_query);
        $geo_zone_name = $zones['geo_zone_name'];
    }

    return $geo_zone_name;
}

function tep_address_format($address_format_id, $address, $html, $boln, $eoln, $name_style = '', $display_name = true) {
    $address_format_query = tep_db_query("select address_format as format from " . TABLE_ADDRESS_FORMAT . " where address_format_id = '" . (int) $address_format_id . "'");
    $address_format = tep_db_fetch_array($address_format_query);

    $company = tep_output_string_protected($address['company']);
    if ($display_name) {
        if (isset($address['firstname']) && tep_not_null($address['firstname'])) {
            $firstname = tep_output_string_protected($address['firstname']);
            $lastname = tep_output_string_protected($address['lastname']);
        } elseif (isset($address['name']) && tep_not_null($address['name'])) {
            $firstname = tep_output_string_protected($address['name']);
            $lastname = '';
        } else {
            $firstname = '';
            $lastname = '';
        }
        $firstname = '<span class="' . $name_style . '">' . $firstname . '</span>';
        $lastname = '<span class="' . $name_style . '">' . $lastname . '</span>';
    }

    $street = tep_output_string_protected($address['street_address']);
    $suburb = tep_output_string_protected($address['suburb']);
    $city = tep_output_string_protected($address['city']);
    $state = tep_output_string_protected($address['state']);
    if (isset($address['country_id']) && tep_not_null($address['country_id'])) {
        $country = tep_get_country_name($address['country_id']);
        if (isset($address['zone_id']) && tep_not_null($address['zone_id'])) {
            $state = tep_get_zone_code($address['country_id'], $address['zone_id'], $state);
        }
    } elseif (isset($address['country']) && tep_not_null($address['country'])) {
        $country = tep_output_string_protected($address['country']);
    } else {
        $country = '';
    }
    $postcode = tep_output_string_protected($address['postcode']);
    $zip = $postcode;

    if ($html) {
        // HTML Mode
        $HR = '<hr>';
        $hr = '<hr>';
        if (($boln == '') && ($eoln == "\n")) { // Values not specified, use rational defaults
            $CR = '<br>';
            $cr = '<br>';
            $eoln = $cr;
        } else { // Use values supplied
            $CR = $eoln . $boln;
            $cr = $CR;
        }
    } else {
        // Text Mode
        $CR = $eoln;
        $cr = $CR;
        $HR = '----------------------------------------';
        $hr = '----------------------------------------';
    }

    $statecomma = '';
    $streets = $street;
    if ($suburb != '')
        $streets = $street . $cr . $suburb;
    if ($country == '')
        $country = tep_output_string_protected($address['country']);
    if ($state != '')
        $statecomma = $state . ', ';

    $fmt = $address_format['format'];
    eval("\$address = \"$fmt\";");

    if ((ACCOUNT_COMPANY == 'true') && (tep_not_null($company))) {
        $address = $company . $cr . $address;
    }

    $address = trim($address);

    if (strpos($address, $cr) === 0) {
        $address = substr($address, strlen($cr));
    }
    return $address;
}

////////////////////////////////////////////////////////////////////////////////////////////////
//
// Function    : tep_get_zone_code
//
// Arguments   : country           country code string
//               zone              state/province zone_id
//               def_state         default string if zone==0
//
// Return      : state_prov_code   state/province code
//
// Description : Function to retrieve the state/province code (as in FL for Florida etc)
//
////////////////////////////////////////////////////////////////////////////////////////////////
function tep_get_zone_code($country, $zone, $def_state) {
    $state_prov_query = tep_db_query("select zone_code from " . TABLE_ZONES . " where zone_country_id = '" . (int) $country . "' and zone_id = '" . (int) $zone . "'");
    if (!tep_db_num_rows($state_prov_query)) {
        $state_prov_code = $def_state;
    } else {
        $state_prov_values = tep_db_fetch_array($state_prov_query);
        $state_prov_code = $state_prov_values['zone_code'];
    }

    return $state_prov_code;
}

function tep_get_uprid($prid, $params) {
    $uprid = $prid;
    if ((is_array($params)) && (!strstr($prid, '{'))) {
        while (list($option, $value) = each($params)) {
            $uprid = $uprid . '{' . $option . '}' . $value;
        }
    }

    return $uprid;
}

function tep_get_prid($uprid) {
    $pieces = explode('{', $uprid);
    return $pieces[0];
}

function tep_get_languages() {
    $languages_query = tep_db_query("select languages_id, name, code, image, directory from " . TABLE_LANGUAGES . " order by sort_order");
    while ($languages = tep_db_fetch_array($languages_query)) {
        $languages_array[] = array('id' => $languages['languages_id'],
            'name' => $languages['name'],
            'text' => $languages['name'],
            'code' => $languages['code'],
            'image' => $languages['image'],
            'directory' => $languages['directory']);
    }
    return $languages_array;
}

function tep_get_language_info($lang_id) {
    $language_query = tep_db_query("select languages_id, name, code, image, directory from " . TABLE_LANGUAGES . " where languages_id='" . (int) $lang_id . "'");
    while ($language = tep_db_fetch_array($language_query)) {
        $language_array = array('id' => $language['languages_id'],
            'name' => $language['name'],
            'text' => $language['name'],
            'code' => $language['code'],
            'image' => $language['image'],
            'directory' => $language['directory']);
    }

    return $language_array;
}

function tep_utf8_to_unicode($str) {
    $unicode = array();
    $values = array();
    $lookingFor = 1;

    for ($i = 0; $i < strlen($str); $i++) {
        $thisValue = ord($str[$i]);
        if ($thisValue < 128)
            $unicode[] = $thisValue;
        else {
            if (count($values) == 0)
                $lookingFor = ( $thisValue < 224 ) ? 2 : 3;
            $values[] = $thisValue;
            if (count($values) == $lookingFor) {
                $number = ( $lookingFor == 3 ) ?
                        ( ( $values[0] % 16 ) * 4096 ) + ( ( $values[1] % 64 ) * 64 ) + ( $values[2] % 64 ) :
                        ( ( $values[0] % 32 ) * 64 ) + ( $values[1] % 64 );

                $unicode[] = $number;
                $values = array();
                $lookingFor = 1;
            } // if
        } // if
    } // for

    return $unicode;
}

// utf8_to_unicode

function tep_unicode_to_entities_preserving_ascii($unicode) {
    $entities = '';
    foreach ($unicode as $value) {
        $entities .= ( $value > 127 ) ? '&#' . $value . ';' : chr($value);
    } //foreach
    return $entities;
}

// unicode_to_entities_preserving_ascii

function tep_get_category_name($category_id, $language_id) {
    $category_query = tep_db_query("select categories_name from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int) $category_id . "' and language_id = '" . (int) $language_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_name'];
}

function tep_get_category_short_name($category_id, $language_id) {
    $category_query = tep_db_query("select categories_short_name from " . TABLE_CATEGORIES_DESCRIPTION . " where categories_id = '" . (int) $category_id . "' and language_id = '" . (int) $language_id . "'");
    $category = tep_db_fetch_array($category_query);
    return $category['categories_short_name'];
}

function tep_get_orders_status_name($orders_status_id, $language_id = '') {
    global $default_languages_id, $languages_id;

    $orders_status_query = tep_db_query("select orders_status_name from " . TABLE_ORDERS_STATUS . "
                                        where orders_status_id = '" . (int) $orders_status_id . "'
                                                and (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                                AND orders_status_name <> ''");
    $orders_status = tep_db_fetch_array($orders_status_query);

    return $orders_status['orders_status_name'];
}

function tep_get_category_pin_yin($category_id, $language_id) {
    $category_pin_yin_select_sql = "SELECT categories_pin_yin
                                    FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                    WHERE categories_id = '" . (int) $category_id . "'
                                            AND language_id = '" . (int) $language_id . "'";
    $category_pin_yin_result_sql = tep_db_query($category_pin_yin_select_sql);
    $category_pin_yin_row = tep_db_fetch_array($category_pin_yin_result_sql);

    return $category_pin_yin_row['categories_pin_yin'];
}

function tep_get_orders_status() {
    global $default_languages_id, $languages_id;

    $orders_status_array = array();
    $orders_status_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . "
                                            where (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                                    AND orders_status_name <> ''
                                            order by orders_status_sort_order");
    while ($orders_status = tep_db_fetch_array($orders_status_query)) {
        $orders_status_array[] = array('id' => $orders_status['orders_status_id'],
            'text' => $orders_status['orders_status_name']);
    }

    return $orders_status_array;
}

function tep_get_supplier_tasks_status() {
    global $default_languages_id, $languages_id;

    $tasks_status_array = array();
    $tasks_status_select_sql = "SELECT supplier_tasks_status_id, supplier_tasks_status_name FROM " . TABLE_SUPPLIER_TASKS_STATUS . "
                                WHERE (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                        AND  supplier_tasks_status_name <> ''
                                ORDER BY supplier_tasks_status_sort_order";
    $tasks_status_result_sql = tep_db_query($tasks_status_select_sql);
    while ($tasks_status_row = tep_db_fetch_array($tasks_status_result_sql)) {
        $tasks_status_array[] = array('id' => $tasks_status_row['supplier_tasks_status_id'],
            'text' => $tasks_status_row['supplier_tasks_status_name']);
    }

    return $tasks_status_array;
}

function tep_get_buyback_status() {
    global $default_languages_id, $languages_id;

    $buyback_status_array = array();
    $buyback_status_select_sql = "  SELECT buyback_status_id, buyback_status_name FROM " . TABLE_BUYBACK_STATUS . "
                                    WHERE (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                            AND buyback_status_name <> ''
                                    ORDER BY buyback_status_sort_order";
    $buyback_status_result_sql = tep_db_query($buyback_status_select_sql);
    while ($buyback_status_row = tep_db_fetch_array($buyback_status_result_sql)) {
        $buyback_status_array[] = array('id' => $buyback_status_row['buyback_status_id'],
            'text' => $buyback_status_row['buyback_status_name']);
    }

    return $buyback_status_array;
}

function tep_get_products_low_stock_status() {
    $lowstock_status_array = array(array('id' => '1', 'text' => 'Pending'));
    return $lowstock_status_array;
}

function tep_get_purchase_orders_status() {
    global $default_languages_id, $languages_id;

    $po_status_array = array();
    $po_status_query = tep_db_query("select purchase_orders_status_id, purchase_orders_status_name from " . TABLE_PURCHASE_ORDERS_STATUS . "
                                    where (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                            AND purchase_orders_status_name <> ''
                                    order by purchase_orders_status_sort_order");
    while ($po_status = tep_db_fetch_array($po_status_query)) {
        $po_status_array[] = array('id' => $po_status['purchase_orders_status_id'],
            'text' => $po_status['purchase_orders_status_name']);
    }

    return $po_status_array;
}

function tep_get_products_name($product_id, $language_id = 0) {
    global $default_languages_id, $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $product_query = tep_db_query(" SELECT products_name
                                    FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                                            WHERE products_id = '" . (int) $product_id . "'
                                                    AND (IF(language_id = '" . tep_db_input($language_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                                    AND products_name <> ''");
    $product = tep_db_fetch_array($product_query);

    return $product['products_name'];
}

function tep_get_products_description($product_id, $language_id = 0) {
    global $default_languages_id, $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $products_description_select_sql = "SELECT products_description
                                        FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                                        WHERE products_id = '" . (int) $product_id . "'
                                            AND products_description <> ''
                                            AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(products_id) > 0
                                                FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                                                WHERE products_id = '" . (int) $product_id . "'
                                                        AND products_description <> ''
                                                        AND language_id = '" . (int) $language_id . "'), 0, language_id = '" . (int) $default_languages_id . "')))";
    $products_description_result_sql = tep_db_query($products_description_select_sql);
    $products_description_row = tep_db_fetch_array($products_description_result_sql);

    return $products_description_row['products_description'];
}

function tep_get_products_url($product_id, $language_id = 0) {
    global $default_languages_id, $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $product_sql = "SELECT products_url
                    FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                    WHERE products_id = '" . (int) $product_id . "'
                        AND products_url <> ''
                        AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(products_id) > 0
                            FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                            WHERE products_id = '" . (int) $product_id . "'
                                    AND products_url <> ''
                                    AND language_id = '" . (int) $language_id . "'), 0, language_id = '" . (int) $default_languages_id . "')))";
    $product_query = tep_db_query($product_sql);
    $product = tep_db_fetch_array($product_query);

    return $product['products_url'];
}

function tep_get_products_location($product_id, $language_id = 0) {
    global $default_languages_id, $languages_id;

    if ($language_id == 0)
        $language_id = $languages_id;

    $product_sql = "SELECT products_location
                    FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                    WHERE products_id = '" . (int) $product_id . "'
                        AND products_location <> ''
                        AND (IF (language_id = '" . (int) $language_id . "', 1, IF(( SELECT COUNT(products_id) > 0
                            FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                            WHERE products_id = '" . (int) $product_id . "'
                                    AND products_location <> ''
                                    AND language_id = '" . (int) $language_id . "'), 0, language_id = '" . (int) $default_languages_id . "')))";
    $product_query = tep_db_query($product_sql);
    $product = tep_db_fetch_array($product_query);

    return $product['products_location'];
}

// Get products_payment_mature_period
function tep_get_products_payment_mature_period($products_id, $supplier_id = 0) {
    $mature_period = 0;

    $product_mature_period_select_sql = "   SELECT products_payment_mature_period
                                            FROM " . TABLE_PRODUCTS . "
                                            WHERE products_id = '" . tep_db_input($products_id) . "'";
    $product_mature_period_result_sql = tep_db_query($product_mature_period_select_sql);

    if ($product_mature_period_row = tep_db_fetch_array($product_mature_period_result_sql)) {
        $mature_period = $product_mature_period_row['products_payment_mature_period'];
    }

    if ($supplier_id > 0) {
        include_once(DIR_WS_CLASSES . 'products_supplier.php');

        $prod_sup_obj = new products_supplier($supplier_id);
        if (isset($prod_sup_obj->products_supplier['payout_grace_period_offset']) && tep_not_null($prod_sup_obj->products_supplier['payout_grace_period_offset'])) {
            $mature_period += $prod_sup_obj->products_supplier['payout_grace_period_offset'];
        }
    }

    return $mature_period;
}

// Get product's predefined currencies price
function tep_get_products_currencies_price($products_id) {
    $currency_price_array = array();

    $product_currency_price_select_sql = "  SELECT products_currency_prices_code, products_currency_prices_value
                                            FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . "
                                            WHERE products_id = '" . tep_db_input($products_id) . "'";
    $product_currency_price_result_sql = tep_db_query($product_currency_price_select_sql);

    while ($product_currency_price_row = tep_db_fetch_array($product_currency_price_result_sql)) {
        $currency_price_array[] = array('id' => $product_currency_price_row['products_currency_prices_code'], 'text' => $product_currency_price_row['products_currency_prices_value']);
    }

    return $currency_price_array;
}

////
// Return a product's stock
// TABLES: products
function tep_get_products_stock($products_id, $offset_out_of_stock_level = false) {
    $products_id = tep_get_prid($products_id);
    $stock_query = tep_db_query("select products_quantity, products_out_of_stock_level from " . TABLE_PRODUCTS . " where products_id = '" . (int) $products_id . "'");
    $stock_values = tep_db_fetch_array($stock_query);

    if ($offset_out_of_stock_level) {
        return ($stock_values['products_quantity'] - (int) $stock_values['products_out_of_stock_level']);
    } else {
        return $stock_values['products_quantity'];
    }
}

////
// Check if the required stock is available
// If insufficent stock then determine whether it is pre-order or out-of-stock status
function tep_check_stock_status($products_id, $products_quantity) {
    $show_it = '';

    $product_info_select_sql = "SELECT products_pre_order_level, products_out_of_stock_level, products_purchase_mode
                                FROM " . TABLE_PRODUCTS . "
                                WHERE products_id = '" . tep_db_input($products_id) . "'";
    $product_info_result_sql = tep_db_query($product_info_select_sql);
    $product_info_row = tep_db_fetch_array($product_info_result_sql);

    switch ($product_info_row["products_purchase_mode"]) {
        case '1': // Always Add to Cart
            $show_it = 1;

            break;
        case '2': // Always Pre-Order
            $show_it = 2; // pre-order

            break;
        case '3': // Always Out of Stock
            $show_it = 0;

            break;
        case '4': // Auto Mode
            $stock_left = tep_get_products_stock($products_id) - $products_quantity;
            $pre_order_level = ($product_info_row["products_pre_order_level"] != '') ? $product_info_row["products_pre_order_level"] : '';

            if (tep_not_null($product_info_row["products_out_of_stock_level"]) && $stock_left < (int) $product_info_row["products_out_of_stock_level"]) { // If there is setting for out of stock level
                $show_it = 0;
            } else if (tep_not_null($pre_order_level) && $stock_left < $pre_order_level) {
                $show_it = 2; // pre-order
            } else {
                $show_it = 1;
            }

            break;
    }

    if ($show_it == "0")
        $stock_status = '<span class="markProductOutOfStock">' . STOCK_MARK_PRODUCT_OUT_OF_STOCK . '</span>';
    else if ($show_it == "2")
        $stock_status = "pre-order";
    else
        $stock_status = '';

    return $stock_status;
}

////
// Return the manufacturers URL in the needed language
// TABLES: manufacturers_info
function tep_get_manufacturer_url($manufacturer_id, $language_id) {
    $manufacturer_query = tep_db_query("select manufacturers_url from " . TABLE_MANUFACTURERS_INFO . "
                                        where manufacturers_id = '" . (int) $manufacturer_id . "'
                                                and (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                                AND manufacturers_url <> ''");
    $manufacturer = tep_db_fetch_array($manufacturer_query);

    return $manufacturer['manufacturers_url'];
}

////
// Wrapper for class_exists() function
// This function is not available in all PHP versions so we test it before using it.
function tep_class_exists($class_name) {
    if (function_exists('class_exists')) {
        return class_exists($class_name);
    } else {
        return true;
    }
}

////
// Count how many products exist in a category
// TABLES: products, products_to_categories, categories
function tep_products_in_category_count($categories_id, $include_deactivated = false) {
    $products_count = 0;

    if ($include_deactivated) {
        $products_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = p2c.products_id and p2c.categories_id = '" . (int) $categories_id . "'");
    } else {
        $products_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = p2c.products_id and p.products_status = '1' and p2c.categories_id = '" . (int) $categories_id . "'");
    }

    $products = tep_db_fetch_array($products_query);
    $products_count += $products['total'];
    $childs_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int) $categories_id . "'");

    if (tep_db_num_rows($childs_query)) {
        while ($childs = tep_db_fetch_array($childs_query)) {
            $products_count += tep_products_in_category_count($childs['categories_id'], $include_deactivated);
        }
    }
    return $products_count;
}

////
// Count how many subcategories exist in a category
// TABLES: categories
function tep_childs_in_category_count($categories_id) {
    $categories_count = 0;
    $categories_query = tep_db_query("select categories_id from " . TABLE_CATEGORIES . " where parent_id = '" . (int) $categories_id . "'");
    while ($categories = tep_db_fetch_array($categories_query)) {
        $categories_count++;
        $categories_count += tep_childs_in_category_count($categories['categories_id']);
    }

    return $categories_count;
}

function tep_get_currency($default = '') {
    $currencies_array = array();

    if ($default) {
        $currencies_array[] = array('id' => '',
            'text' => $default);
    }

    $cur_sel_sql = "SELECT currencies_id, title FROM " . TABLE_CURRENCIES . " ORDER BY title ";
    $cur_res_sql = tep_db_query($cur_sel_sql);
    while ($currencies = tep_db_fetch_array($cur_res_sql)) {
        $currencies_array[] = array('id' => $currencies['currencies_id'],
            'text' => $currencies['title']);
    }

    return $currencies_array;
}

////
// Returns an array with countries
// TABLES: countries
function tep_get_countries($default = '') {
    $countries_array = array();
    if ($default) {
        $countries_array[] = array('id' => '',
            'text' => $default);
    }
    $countries_query = tep_db_query("select countries_id, countries_name from " . TABLE_COUNTRIES . " order by countries_name");
    while ($countries = tep_db_fetch_array($countries_query)) {
        $countries_array[] = array('id' => $countries['countries_id'],
            'text' => $countries['countries_name']);
    }

    return $countries_array;
}

function tep_get_countries_list($countries_id = '', $with_iso_codes = false) {
    $countries_array = array();
    if (tep_not_null($countries_id)) {
        if ($with_iso_codes == true) {
            $countries = tep_db_query("select countries_name, countries_iso_code_2, countries_iso_code_3 from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $countries_id . "' order by countries_name");
            $countries_values = tep_db_fetch_array($countries);
            $countries_array = array('countries_name' => $countries_values['countries_name'],
                'countries_iso_code_2' => $countries_values['countries_iso_code_2'],
                'countries_iso_code_3' => $countries_values['countries_iso_code_3']);
        } else {
            $countries = tep_db_query("select countries_name from " . TABLE_COUNTRIES . " where countries_id = '" . (int) $countries_id . "'");
            $countries_values = tep_db_fetch_array($countries);
            $countries_array = array('countries_name' => $countries_values['countries_name']);
        }
    } else {
        $countries = tep_db_query("select countries_id, countries_name from " . TABLE_COUNTRIES . " order by countries_name");
        while ($countries_values = tep_db_fetch_array($countries)) {
            $countries_array[] = array('countries_id' => $countries_values['countries_id'],
                'countries_name' => $countries_values['countries_name']);
        }
    }

    return $countries_array;
}

////
// return an array with country zones
function tep_get_country_zones($country_id) {
    $zones_array = array();
    $zones_query = tep_db_query("select zone_id, zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int) $country_id . "' order by zone_name");
    while ($zones = tep_db_fetch_array($zones_query)) {
        $zones_array[] = array('id' => $zones['zone_id'],
            'text' => $zones['zone_name']);
    }

    return $zones_array;
}

function tep_prepare_country_zones_pull_down($country_id = '') {
    // preset the width of the drop-down for Netscape
    $pre = '';
    if ((!tep_browser_detect('MSIE')) && (tep_browser_detect('Mozilla/4'))) {
        for ($i = 0; $i < 45; $i++)
            $pre .= '&nbsp;';
    }

    $zones = tep_get_country_zones($country_id);

    if (sizeof($zones) > 0) {
        $zones_select = array(array('id' => '0', 'text' => PLEASE_SELECT));
        $zones = array_merge($zones_select, $zones);
    } else {
        $zones = array(array('id' => '0', 'text' => TYPE_BELOW));
        // create dummy options for Netscape to preset the height of the drop-down
        if ((!tep_browser_detect('MSIE')) && (tep_browser_detect('Mozilla/4'))) {
            for ($i = 0; $i < 9; $i++) {
                $zones[] = array('id' => '', 'text' => $pre);
            }
        }
    }

    return $zones;
}

////
// Get list of address_format_id's
function tep_get_address_formats() {
    $address_format_query = tep_db_query("select address_format_id from " . TABLE_ADDRESS_FORMAT . " order by address_format_id");
    $address_format_array = array();
    while ($address_format_values = tep_db_fetch_array($address_format_query)) {
        $address_format_array[] = array('id' => $address_format_values['address_format_id'],
            'text' => $address_format_values['address_format_id']);
    }
    return $address_format_array;
}

function tep_get_po_companies_list($po_companies_code = '') {
    $po_companies_array = array();
    if (tep_not_null($po_companies_code)) {
        $companies = tep_db_query("SELECT po_company_code, po_company_name FROM " . TABLE_PO_COMPANY . " WHERE po_company_code = '" . $po_companies_code . "'");
        $companies_values = tep_db_fetch_array($companies);
        $po_companies_array = array(
            'po_company_code' => $companies_values['po_company_code'],
            'po_company_name' => $companies_values['po_company_name']);
    } else {
        $companies = tep_db_query("SELECT po_company_code, po_company_name FROM " . TABLE_PO_COMPANY . " WHERE po_company_status = 1 ORDER BY po_company_name");
        while ($companies_values = tep_db_fetch_array($companies)) {
            $po_companies_array[] = array(
                'po_company_code' => $companies_values['po_company_code'],
                'po_company_name' => $companies_values['po_company_name']);
        }
    }

    return $po_companies_array;
}

function tep_set_company_status($po_company_id, $to_status) {
    $company_status_update_sql = " UPDATE " . TABLE_PO_COMPANY . "
                                    SET po_company_status = '".(int)$to_status."'
                                    WHERE po_company_id='" . (int)$po_company_id . "'";
    tep_db_query($company_status_update_sql);
}

////
// Alias function for Store configuration values in the Administration Tool
function tep_cfg_pull_down_country_list($country_id, $key = '') {
    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    return tep_draw_pull_down_menu($name, tep_get_countries(), $country_id);
}

function tep_cfg_pull_down_zone_list($zone_id, $key = '') {
    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    return tep_draw_pull_down_menu($name, tep_get_country_zones(STORE_COUNTRY), $zone_id);
}

function tep_cfg_pull_down_tax_classes($tax_class_id, $key = '') {
    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    $tax_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from " . TABLE_TAX_CLASS . " order by tax_class_title");
    while ($tax_class = tep_db_fetch_array($tax_class_query)) {
        $tax_class_array[] = array('id' => $tax_class['tax_class_id'],
            'text' => $tax_class['tax_class_title']);
    }

    return tep_draw_pull_down_menu($name, $tax_class_array, $tax_class_id);
}

////
// Function to read in text area in admin
function tep_cfg_textarea($text, $key = '', $cols = '', $rows = '') {
    $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');
    $text = tep_db_prepare_input($text);
    return tep_draw_textarea_field($name, 'soft', (isset($cols) && tep_not_null($cols) ? $cols : 35), (isset($rows) && tep_not_null($rows) ? $rows : 5), str_replace('&nbsp;', '&amp;nbsp;', $text));
}

function tep_cfg_get_zone_name($zone_id) {
    $zone_query = tep_db_query("select zone_name from " . TABLE_ZONES . " where zone_id = '" . (int) $zone_id . "'");

    if (!tep_db_num_rows($zone_query)) {
        return $zone_id;
    } else {
        $zone = tep_db_fetch_array($zone_query);
        return $zone['zone_name'];
    }
}

function tep_cfg_currencies($default_currency = '', $key = '') {
    $name = (($key) ? 'configuration[' . $key . '][]' : 'configuration_value');

    return tep_draw_pull_down_menu($name, tep_get_currency(), explode(',', $default_currency), ' multiple ');
}

////
// Sets the status of a banner
function tep_set_banner_status($banners_id, $status) {
    if ($status == '1') {
        return tep_db_query("update " . TABLE_BANNERS . " set status = '1', expires_impressions = NULL, expires_date = NULL, date_status_change = NULL where banners_id = '" . $banners_id . "'");
    } elseif ($status == '0') {
        return tep_db_query("update " . TABLE_BANNERS . " set status = '0', date_status_change = now() where banners_id = '" . $banners_id . "'");
    } else {
        return -1;
    }
}

////
// Get the status of a product
function tep_get_product_status($products_id) {
    $product_status_select_sql = "  SELECT products_status
                                    FROM " . TABLE_PRODUCTS . "
                                    WHERE products_id = '" . (int) $products_id . "'";
    $product_status_result_sql = tep_db_query($product_status_select_sql);
    $product_status_row = tep_db_fetch_array($product_status_result_sql);

    return $product_status_row['products_status'];
}

////
// Sets the status of a product
function tep_set_product_status($products_id, $status) {
    if ($status == '1') {
        $product_price_select_sql = "	SELECT products_price, custom_products_type_id
                                        FROM " . TABLE_PRODUCTS . "
                                        WHERE products_id = '" . (int) $products_id . "'";
        $product_price_result_sql = tep_db_query($product_price_select_sql);
        $product_price_row = tep_db_fetch_array($product_price_result_sql);

        if ($product_price_row["products_price"] <= 0) {
            if ($product_price_row["custom_products_type_id"] != '1') {
                // Send notification email
                $categories_id = tep_get_actual_product_cat_id($products_id);
                $product_id_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($categories_id) . '&pID=' . $products_id . '&selected_box=catalog') . '"  target="_blank">' . $products_id . '</a>';
                $zero_price_email_contents = sprintf(EMAIL_PRODUCT_ZERO_AMOUNT_NOTIFICATION_CONTENT, $product_id_link, tep_get_products_name($products_id), $product_price_row["products_price"], $product_price_row["products_price"] . ' (Activated)', date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'], tep_get_admin_group_name($_SESSION['login_email_address']));

                $cat_cfg_array = tep_get_cfg_setting($products_id, 'product', 'STOCK_ZERO_PRICE_PRODUCT_EMAIL');
                $email_to_array = tep_parse_email_string($cat_cfg_array['STOCK_ZERO_PRICE_PRODUCT_EMAIL']);
                for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                    tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, 'Zero Price Product')), $zero_price_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                }
            }

            // Remove fix price setting
            $fix_price_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . " WHERE products_id = '" . tep_db_input($products_id) . "'";
            tep_db_query($fix_price_delete_sql);
        }

        return tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '1', products_last_modified = now() where products_id = '" . (int) $products_id . "'");
    } elseif ($status == '0') {
        return tep_db_query("update " . TABLE_PRODUCTS . " set products_status = '0', products_last_modified = now() where products_id = '" . (int) $products_id . "'");
    } else {
        return -1;
    }
}

////
// Sets the hidden status of a product
function tep_set_product_display($products_id, $display) {
    if ($display == '1') {
        return tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_display='1', products_last_modified=now() WHERE products_id='" . (int) $products_id . "'");
    } elseif ($display == '0') {
        return tep_db_query("UPDATE " . TABLE_PRODUCTS . " SET products_display='0', products_last_modified = now() WHERE products_id='" . (int) $products_id . "'");
    } else {
        return -1;
    }
}

////
// Sets the status of a product on special
function tep_set_specials_status($specials_id, $status) {
    if ($status == '1') {
        return tep_db_query("update " . TABLE_SPECIALS . " set status = '1', expires_date = NULL, date_status_change = NULL where specials_id = '" . (int) $specials_id . "'");
    } elseif ($status == '0') {
        return tep_db_query("update " . TABLE_SPECIALS . " set status = '0', date_status_change = now() where specials_id = '" . (int) $specials_id . "'");
    } else {
        return -1;
    }
}

//// By Wei Chen
// Sets the available & actual qty of a product
function tep_set_product_qty($products_id, $qty_array, $keep_log = true, $admin_msg = '', $user_msg = '') {
    global $log_object;
    /*     * *****************************************************************
      operator = + (add stock), - (deduct stock), = (assign new value)
     * ***************************************************************** */
    $sql_update_array = array();
    $product_instance_id = null;
    $product_array_info = explode('_', $products_id);
    $new_product_quantity = null;

    // HLA products_hla_id
    if (count($product_array_info) == 2) {
        list($products_id, $product_instance_id) = $product_array_info;
    }

    // Generate the update sql
    for ($qty_cnt = 0; $qty_cnt < count($qty_array); $qty_cnt++) {
        if (($qty_array[$qty_cnt]['field_name'] == 'products_quantity' || $qty_array[$qty_cnt]['field_name'] == 'products_actual_quantity')) {
            if (tep_not_null($product_instance_id)) {
                if ($qty_array[$qty_cnt]['field_name'] == 'products_quantity') {
                    $qty_array[$qty_cnt]['field_name'] = 'available_quantity';
                } else if ($qty_array[$qty_cnt]['field_name'] == 'products_actual_quantity') {
                    $qty_array[$qty_cnt]['field_name'] = 'actual_quantity';
                }
            }

            $qty_array[$qty_cnt]['operator'] = trim($qty_array[$qty_cnt]['operator']);
            switch ($qty_array[$qty_cnt]['operator']) {
                case '+':
                    $sql_update_array[] = $qty_array[$qty_cnt]['field_name'] . ' = ' . $qty_array[$qty_cnt]['field_name'] . ' + ' . tep_db_input($qty_array[$qty_cnt]['value']);
                    break;
                case '-':
                    $sql_update_array[] = $qty_array[$qty_cnt]['field_name'] . ' = ' . $qty_array[$qty_cnt]['field_name'] . ' - ' . tep_db_input($qty_array[$qty_cnt]['value']);
                    break;
                case '=':
                    $sql_update_array[] = $qty_array[$qty_cnt]['field_name'] . ' = ' . tep_db_input($qty_array[$qty_cnt]['value']);
                    break;
                default:
                    break;
            }
        }
    }

    if (count($sql_update_array)) {
        if ($keep_log) {
            if (tep_not_null($product_instance_id)) { // HLA
                $product_stock_select_sql = "	SELECT available_quantity AS products_quantity, actual_quantity AS products_actual_quantity
                                                FROM " . TABLE_PRODUCTS_HLA . "
                                                WHERE products_hla_id = '" . tep_db_input($product_instance_id) . "'
                                                        AND products_id = '" . tep_db_input($products_id) . "'";
            } else {
                $product_stock_select_sql = "	SELECT products_quantity, products_actual_quantity
                                                FROM " . TABLE_PRODUCTS . "
                                                WHERE products_id = '" . tep_db_input($products_id) . "'";
            }
            $product_stock_result_sql = tep_db_query($product_stock_select_sql);

            if ($product_stock_row = tep_db_fetch_array($product_stock_result_sql)) {
                $previous_product_quantity = $product_stock_row['products_quantity'];
                $previous_product_actual_quantity = $product_stock_row['products_actual_quantity'];
            } else {
                $previous_product_quantity = 0;
                $previous_product_actual_quantity = 0;
            }
        }

        $update_sql_str = " SET " . implode(', ', $sql_update_array);

        if (tep_not_null($product_instance_id)) { // HLA
            $product_stock_update_sql = "UPDATE " . TABLE_PRODUCTS_HLA . $update_sql_str . "
                                            WHERE products_hla_id = '" . tep_db_input($product_instance_id) . "'
                                                AND products_id = '" . tep_db_input($products_id) . "'";
        } else {
            $product_stock_update_sql = "UPDATE " . TABLE_PRODUCTS . $update_sql_str . " WHERE products_id = '" . tep_db_input($products_id) . "'";
        }
        tep_db_query($product_stock_update_sql);

        /*         * ***********************************************************
          This section must be outside the if ($keep_log) section
          since we need it to grab the latest FIFO Average price
         * *********************************************************** */
        if (tep_not_null($product_instance_id)) { // HLA
            $products_id = $products_id . '_' . $product_instance_id;
        } else {
            $product_stock_select_sql = "   SELECT products_quantity, products_actual_quantity
                                            FROM " . TABLE_PRODUCTS . "
                                            WHERE products_id = '" . tep_db_input($products_id) . "'";
            $product_stock_result_sql = tep_db_query($product_stock_select_sql);

            if ($product_stock_row = tep_db_fetch_array($product_stock_result_sql)) {
                $new_product_quantity = $product_stock_row['products_quantity'];
                $new_product_actual_quantity = $product_stock_row['products_actual_quantity'];

                // Calculate the latest FIFO Average price
                tep_set_product_fifo_avg_price($products_id, $new_product_quantity, $new_product_actual_quantity);
            } else {
                $new_product_quantity = 0;
                $new_product_actual_quantity = 0;
            }
        }

        if ($keep_log) {
            $log_object->insert_log($products_id, 'products_quantity', $previous_product_quantity . ':~:' . $previous_product_actual_quantity, $new_product_quantity . ':~:' . $new_product_actual_quantity, $admin_msg, $user_msg);
        }
    } else {
        # Used to proess out of stock rules
        $product_stock_select_sql = "   SELECT products_quantity, products_actual_quantity
                                        FROM " . TABLE_PRODUCTS . "
                                        WHERE products_id = '" . tep_db_input($products_id) . "'";
        $product_stock_result_sql = tep_db_query($product_stock_select_sql);

        if ($product_stock_row = tep_db_fetch_array($product_stock_result_sql)) {
            $new_product_quantity = $product_stock_row['products_quantity'];
        } else {
            $new_product_quantity = 0;
        }
    }

    # Out of Stock Rules
    if (!is_null($new_product_quantity)) {
        include_once(DIR_WS_CLASSES . 'purchase_control_tool.php');
        $pct_obj = new purchase_control_tool();
        $pct_obj->updateOutOfStockFlag($products_id, $new_product_quantity);
        unset($pct_obj);
    }
}

//// By Wei Chen
function tep_set_product_fifo_avg_price($products_id, $available_qty, $actual_qty) {
    $qty_type_array = array('available' => array('qty' => $available_qty, 'fifo_field' => 'products_quantity_fifo_cost'),
        'actual' => array('qty' => $actual_qty, 'fifo_field' => 'products_actual_quantity_fifo_cost')
    );

    foreach ($qty_type_array as $qty_type => $qty_info) {
        $total_cost = 0;
        if ($qty_info['qty'] > 0) {
            $current_stock = $qty_info['qty'];
            $keep_looping = true;
            $loop = 0;
            $rec_count = 10;
            do {
                $offset = $loop * $rec_count;

                $restock_select_sql = "	SELECT brg.buyback_request_group_date AS ref_date, br.buyback_quantity_received AS rstk_qty, (IF(br.buyback_request_quantity > 0, buyback_amount/br.buyback_request_quantity, 0)) AS unit_price
                                        FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
                                        INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
                                                ON (brg.buyback_request_group_id=br.buyback_request_group_id AND br.products_id='" . tep_db_input($products_id) . "')
                                        WHERE brg.buyback_status_id IN (1, 2, 3)
                                        ORDER BY ref_date desc
                                        LIMIT " . $offset . ", " . $rec_count;
                $restock_result_sql = tep_db_query($restock_select_sql);
                if (tep_db_num_rows($restock_result_sql)) {
                    while ($current_stock > 0 && $restock_row = tep_db_fetch_array($restock_result_sql)) {
                        $list_price = (double) $restock_row['unit_price'];
                        $list_rstk_qty = $restock_row['rstk_qty'];

                        if ($list_rstk_qty > 0) {
                            if ($current_stock > $list_rstk_qty) {
                                $total_cost += $list_rstk_qty * $list_price;
                                $current_stock -= $list_rstk_qty;
                            } else {
                                $total_cost += $current_stock * $list_price;
                                $current_stock = 0;
                            }
                        }
                    }
                } else {
                    $keep_looping = false; // End the loop
                }

                $loop++;
            } while ($current_stock > 0 && $keep_looping);

            if ($current_stock > 0) { // Not enough information
                $calculated_unit_price = 'NULL';
            } else {
                $calculated_unit_price = $total_cost / $qty_info['qty'];
            }
            $fifo_cost_data_array = array($qty_info['fifo_field'] => is_numeric($calculated_unit_price) ? round($calculated_unit_price, 6) : $calculated_unit_price);
            tep_db_perform(TABLE_PRODUCTS, $fifo_cost_data_array, 'update', "products_id = '" . tep_db_input($products_id) . "'");
        } else {
            $fifo_cost_data_array = array($qty_info['fifo_field'] => $total_cost);
            tep_db_perform(TABLE_PRODUCTS, $fifo_cost_data_array, 'update', "products_id = '" . tep_db_input($products_id) . "'");
        }
    }
}

// Sets the status of a promotion
function tep_set_promotions_status($promotions_id, $promotion) {
    if ($promotion == '1') {
        return tep_db_query("update " . TABLE_PROMOTIONS . " set promotions_status = '1' where promotions_id = '" . (int) $promotions_id . "'");
    } elseif ($promotion == '0') {
        return tep_db_query("update " . TABLE_PROMOTIONS . " set promotions_status = '0' where promotions_id = '" . (int) $promotions_id . "'");
    } else {
        return -1;
    }
}

// Sets timeout for the current script.
// Cant be used in safe mode.
function tep_set_time_limit($limit) {
    if (!get_cfg_var('safe_mode')) {
        set_time_limit($limit);
    }
}

////
// Alias function for Store configuration values in the Administration Tool
function tep_cfg_select_option($select_array, $key_value, $key = '') {
    $string = '';

    for ($i = 0, $n = sizeof($select_array); $i < $n; $i++) {
        $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');
        $string .= '<input type="radio" name="' . $name . '" value="' . $select_array[$i] . '"';

        if ($key_value == $select_array[$i])
            $string .= ' CHECKED';

        $string .= '> ' . $select_array[$i] . '<br>';
    }

    if (substr($string, -4) == '<br>')
        $string = substr($string, 0, -4);
    return $string;
}

////
// Function for radio buttons configuration values in the Administration Tool
function tep_cfg_key_select_option($select_array, $key_value, $key = '') {
    $string = '';

    if (count($select_array)) {
        foreach ($select_array as $sel_key => $sel_val) {
            $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');
            $string .= '<input type="radio" name="' . $name . '" value="' . $sel_key . '"';

            if ($key_value == $sel_key)
                $string .= ' CHECKED';

            $string .= '> ' . $sel_val . '<br>';
        }
    }

    if (substr($string, -4) == '<br>')
        $string = substr($string, 0, -4);
    return $string;
}

////
// Alias function for Shipping configuration values in the Administration Tool
function tep_cfg_pull_down_option($select_array, $key_value, $key = '') {
    $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');

    $resource_array = array();
    for ($i = 0; $i < count($select_array); $i++) {
        $resource_array[] = array('id' => $select_array[$i],
            'text' => $select_array[$i]);
    }

    return tep_draw_pull_down_menu($name, $resource_array, $key_value);
}

////
// Alias function for Payment configuration values in the Administration Tool
function tep_cfg_colour_palette($key_value, $key = '') {
    $name = ((tep_not_null($key)) ? 'configuration[' . $key . ']' : 'configuration_value');

    $string = tep_draw_input_field($name, $key_value, 'id="' . $name . '" size="9" onfocus="select() "');
    $string .= "&nbsp;<a href=\"javascript:;\" onClick=\"popUpColorLab('" . $name . "');\">" . tep_image(IMAGE_LEGEND_COLOUR_SELECTION, '', '', '', " border=0 unselectable=on ") . "</a>";

    return $string;
}

////
// Alias function for module configuration keys
function tep_mod_select_option($select_array, $key_name, $key_value) {
    reset($select_array);
    while (list($key, $value) = each($select_array)) {
        if (is_int($key))
            $key = $value;
        $string .= '<br><input type="radio" name="configuration[' . $key_name . ']" value="' . $key . '"';
        if ($key_value == $key)
            $string .= ' CHECKED';
        $string .= '> ' . $value;
    }

    return $string;
}

////
// Retreive server information
function tep_get_system_information() {
    global $HTTP_SERVER_VARS;

    $db_query = tep_db_query("select now() as datetime");
    $db = tep_db_fetch_array($db_query);

    list($system, $host, $kernel) = preg_split('/[\s,]+/', @exec('uname -a'), 5);

    return array('date' => tep_datetime_short(date('Y-m-d H:i:s')),
        'system' => $system,
        'kernel' => $kernel,
        'host' => $host,
        'ip' => gethostbyname($host),
        'uptime' => @exec('uptime'),
        'http_server' => $HTTP_SERVER_VARS['SERVER_SOFTWARE'],
        'php' => PHP_VERSION,
        'zend' => (function_exists('zend_version') ? zend_version() : ''),
        'db_server' => DB_SERVER,
        'db_ip' => gethostbyname(DB_SERVER),
        'db_version' => 'MySQL ' . (function_exists('mysql_get_server_info') ? mysql_get_server_info() : ''),
        'db_date' => tep_datetime_short($db['datetime']));
}

function tep_generate_category_path($id, $from = 'category', $categories_array = '', $index = 0, $filename = '') {
    global $default_languages_id, $languages_id;
    global $memcache_obj;
    if (!is_array($categories_array))
        $categories_array = array();

    if ($from == 'product') {
        $categories_select_sql = "SELECT categories_id FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE products_id = '" . (int) $id . "'";
        $categories_query = tep_db_query($categories_select_sql);
        while ($categories = tep_db_fetch_array($categories_query)) {
            if (tep_not_null($filename)) {
                if (tep_check_cat_tree_permissions($filename, $categories['categories_id']) != 1) {
                    continue;
                }
            }

            if ($categories['categories_id'] == '0') {
                $categories_array[$index][] = array('id' => '0', 'text' => TEXT_TOP);
            } else {
                $cache_key = 'category_path/product/' . $id;
                $cache_result = $memcache_obj->fetch($cache_key);
                if($cache_result === FALSE){
                    $category_select_sql = "select cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd
                                        where c.categories_id = '" . (int) $categories['categories_id'] . "'
                                                and c.categories_id = cd.categories_id
                                                and (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
                                                and cd.categories_name <> ''";
                    $category_query = tep_db_query($category_select_sql);
                    $category = tep_db_fetch_array($category_query);
                    $memcache_obj->store($cache_key, $category, 3600);
                }
                else{
                    $category = $cache_result;
                }
                $categories_array[$index][] = array('id' => $categories['categories_id'], 'text' => strip_tags($category['categories_name']));
                if ((tep_not_null($category['parent_id'])) && ($category['parent_id'] != '0'))
                    $categories_array = tep_generate_category_path($category['parent_id'], 'category', $categories_array, $index);
                $categories_array[$index] = array_reverse($categories_array[$index]);


            }
            $index++;
        }
    } elseif ($from == 'category') {
        $cache_key = 'category_path/category/' . $id;
        $cache_result = $memcache_obj->fetch($cache_key);
        if($cache_result === FALSE) {
            $category_query = tep_db_query("select cd.categories_name, c.parent_id from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd
                                        where c.categories_id = '" . (int)$id . "'
                                                and c.categories_id = cd.categories_id
                                                and cd.categories_name <> ''
                                                and (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))");
            $category = tep_db_fetch_array($category_query);
            $memcache_obj->store($cache_key, $category, 3600);
        }
        else{
            $category = $cache_result;
        }
        $categories_array[$index][] = array('id' => $id, 'text' => strip_tags($category['categories_name']));
        if ((tep_not_null($category['parent_id'])) && ($category['parent_id'] != '0'))
            $categories_array = tep_generate_category_path($category['parent_id'], 'category', $categories_array, $index);
    }

    return $categories_array;
}

//Kenson 27/08/2004 :: Top Heading Path for Product List
function tep_output_generated_category_path_sq($id, $from = 'category') {
    $ts = '';
    $calculated_category_path = tep_generate_category_path($id, $from);
    for ($i = 0, $n = sizeof($calculated_category_path); $i < $n; $i++) {
        for ($j = 0, $k = sizeof($calculated_category_path[$i]); $j < $k; $j++) {
            if ($ts) {
                $ts = $calculated_category_path[$i][$j]['text'] . " > " . $ts;
            } else {
                $ts = $calculated_category_path[$i][$j]['text'];
            }
        }
    }
    return $ts;
}

//Wei Chen 18/01/2005 :: Top Heading Navigation Path for Category / Product Edit Page
function tep_output_category_nav_path($category_id, $filename = '', $params = '', $safe_quote = true, $display = '') {
    global $default_languages_id, $languages_id;

    $cat_id_array = array($category_id);
    $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "' AND parent_id<>0");
    while ($category = tep_db_fetch_array($category_query)) {
        $cat_id_array[] = $category["parent_id"];
        $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $category["parent_id"] . "' AND parent_id<>0");
    }

    $complete_cat_array = $cat_id_array = array_reverse($cat_id_array);

    $cat_nav_array = array();
    $count = 0;
    while (count($cat_id_array)) {
        $cur_cat = end($cat_id_array);
        $extra_param = "";

        $category_name_query = tep_db_query("SELECT categories_name FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                            WHERE categories_id = '" . $cur_cat . "'
                                                    AND (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
                                                    AND categories_name <> ''");
        $category_name = tep_db_fetch_array($category_name_query);
        if ($filename) {
            if ($display == 'cat') {
                $extra_param = "cID=" . (!$count ? $_REQUEST["cID"] : $complete_cat_array[count($cat_id_array)]);
            } else if ($display == 'product') {
                $extra_param = (!$count ? "pID=" . $_REQUEST["pID"] : "cID=" . $complete_cat_array[count($cat_id_array)]);
            }
            $link = '<a href="' . tep_href_link($filename, 'cPath=' . implode("_", $cat_id_array) . ($extra_param != '' ? "&$extra_param" : "")) . '">' . strip_tags($category_name["categories_name"]) . '</a>';
        } else {
            $link = strip_tags($category_name["categories_name"]);
        }

        $cat_nav_array[] = $link;
        array_pop($cat_id_array);
        $count++;
    }

    return (count($cat_nav_array) ? implode(( $safe_quote ? "&nbsp;&gt;&nbsp;" : " > "), array_reverse($cat_nav_array)) : TEXT_TOP );
}

function tep_get_particular_cat_path($category_id) {
    $this_cat_path = '';
    if (tep_not_null($category_id)) {
        $cat_id_array = array($category_id);
        $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "' AND parent_id<>0");
        while ($category = tep_db_fetch_array($category_query)) {
            $cat_id_array[] = $category["parent_id"];
            $category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $category["parent_id"] . "' AND parent_id<>0");
        }

        $cat_id_array = array_reverse($cat_id_array);
        $this_cat_path = count($cat_id_array) ? implode('_', $cat_id_array) : '';
    }

    return $this_cat_path;
}

function tep_update_cat_path($cat_id) {
    if (!$cat_id)
        return;
    $cat_path = tep_output_category_nav_path($cat_id, '', '', false);

    $p2c_select_sql = " SELECT ptc.products_id, c.categories_parent_path
                        FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " as ptc
                        INNER JOIN " . TABLE_CATEGORIES . " as c
                                ON c.categories_id =  ptc.categories_id
                        WHERE ptc.categories_id ='" . $cat_id . "'
                                AND products_is_link=0
                        ORDER BY products_id ";
    $p2c_result_sql = tep_db_query($p2c_select_sql);
    while ($p2c_row_sql = tep_db_fetch_array($p2c_result_sql)) {
        $parent_path_str = $p2c_row_sql['categories_parent_path'];
        if (tep_not_null($parent_path_str)) {
            $parent_path_array = explode("_", $parent_path_str);
            $top_parent_id = $parent_path_array[1];
            $parent_path_str .= (int) $cat_id . "_";
        } else {
            $top_parent_id = (int) $cat_id;
            $parent_path_str = "_" . (int) $cat_id . "_";
        }
        $prod_update_data_sql = array('products_cat_path' => tep_db_prepare_input($cat_path),
            'products_cat_id_path' => tep_db_prepare_input($parent_path_str),
            'products_main_cat_id' => (int) $top_parent_id,
        );
        tep_db_perform(TABLE_PRODUCTS, $prod_update_data_sql, 'update', " products_id='" . $p2c_row_sql['products_id'] . "' ");
    }

    $cat_select_sql = " SELECT categories_id
                        FROM " . TABLE_CATEGORIES . "
                        WHERE parent_id ='" . $cat_id . "'
                        ORDER BY sort_order ";
    $cat_result_sql = tep_db_query($cat_select_sql);
    while ($cat_row_sql = tep_db_fetch_array($cat_result_sql)) {
        tep_update_cat_path($cat_row_sql["categories_id"]);
    }
}

function tep_get_actual_product_cat_id($prod_id) {
    $category_select_sql = "SELECT categories_id FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE products_id = '" . $prod_id . "' AND products_is_link=0";
    $category_result_sql = tep_db_query($category_select_sql);
    $category_id = tep_db_fetch_array($category_result_sql);

    return $category_id["categories_id"];
}

function tep_output_generated_category_path($id, $from = 'category') {
    $calculated_category_path_string = '';
    $calculated_category_path = tep_generate_category_path($id, $from);

    for ($i = 0, $n = sizeof($calculated_category_path); $i < $n; $i++) {
        for ($j = 0, $k = sizeof($calculated_category_path[$i]); $j < $k; $j++) {
            if ($ts) {
                $ts = $calculated_category_path[$i][$j]['text'] . " > " . $ts;
            } else {
                $ts = $calculated_category_path[$i][$j]['text'];
            }
            $calculated_category_path_string .= $calculated_category_path[$i][$j]['text'] . '&nbsp;&gt;&nbsp;';
        }
        $calculated_category_path_string = substr($calculated_category_path_string, 0, -16) . '<br>';
    }
    $calculated_category_path_string = substr($calculated_category_path_string, 0, -4);

    if (strlen($calculated_category_path_string) < 1)
        $calculated_category_path_string = TEXT_TOP;

    return $calculated_category_path_string;
}

function tep_get_generated_category_path_ids($id, $from = 'category') {
    $calculated_category_path_string = '';
    $calculated_category_path = tep_generate_category_path($id, $from);
    for ($i = 0, $n = sizeof($calculated_category_path); $i < $n; $i++) {
        for ($j = 0, $k = sizeof($calculated_category_path[$i]); $j < $k; $j++) {
            $calculated_category_path_string .= $calculated_category_path[$i][$j]['id'] . '_';
        }
        $calculated_category_path_string = substr($calculated_category_path_string, 0, -1) . '<br>';
    }
    $calculated_category_path_string = substr($calculated_category_path_string, 0, -4);

    if (strlen($calculated_category_path_string) < 1)
        $calculated_category_path_string = TEXT_TOP;

    return $calculated_category_path_string;
}

function tep_remove_category($category_id) {
    include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
    $aws_obj = new ogm_amazon_ws();

    $category_image_select_sql = "  SELECT categories_image
                                    FROM " . TABLE_CATEGORIES_DESCRIPTION . "
                                    WHERE categories_id = '" . (int) $category_id . "'";
    $category_image_result_sql = tep_db_query($category_image_select_sql);
    while ($category_image_row = tep_db_fetch_array($category_image_result_sql)) {
        if ($aws_obj->is_image_exists($category_image_row['categories_image'], 'images/category/', 'BUCKET_STATIC')) {
            $aws_obj->delete_file();
        } else if (file_exists(DIR_FS_CATALOG_IMAGES . 'category/' . $category_image_row['categories_image'])) {
            @unlink(DIR_FS_CATALOG_IMAGES . 'category/' . $category_image_row['categories_image']);
        }
    }
    unset($aws_obj);

    require_once(DIR_WS_CLASSES . 'category.php');
    $cat_object = new category($category_id);
    $cat_object->delete_cache();
    unset($cat_object);

    tep_db_query("DELETE FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "'");
    tep_db_query("DELETE FROM " . TABLE_CATEGORIES_DESCRIPTION . " WHERE categories_id = '" . (int) $category_id . "'");
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "'");

    /* -- remove categories record from CATEGORIES_GAME_DETAILS (template) and relevant tables -- */
    tep_db_query("DELETE FROM " . TABLE_CATEGORIES_GAME_DETAILS . " WHERE categories_id = '" . (int) $category_id . "'");
    tep_db_query("DELETE FROM " . TABLE_CATEGORIES_SETTING_LANG . " WHERE categories_id = '" . (int) $category_id . "'");
    tep_db_query("DELETE FROM " . TABLE_GAME_ESRB_TO_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "'");
    tep_db_query("DELETE FROM " . TABLE_GAME_GENRE_TO_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "'");
    tep_db_query("DELETE FROM " . TABLE_GAME_PLATFORM_TO_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "'");
    tep_db_query("DELETE FROM " . TABLE_GAME_REGION_LANGUAGE_TO_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "'");

    // Delete Payment Methods Restrictions
    tep_db_query("DELETE FROM " . TABLE_CATEGORIES_PAYMENT_METHODS_RESTRICTIONS . " WHERE categories_id = '" . (int)$category_id . "'");

    //clear TABLE_CATEGORIES_SEARCH
    $categories_search_delete_sql = "	DELETE FROM " . TABLE_CATEGORIES_SEARCH . "
                                        WHERE categories_id = '" . (int) $category_id . "'";
    tep_db_query($categories_search_delete_sql);

    if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('also_purchased');
    }
}

function tep_get_product_path($id) {
    $product_path_select_sql = "SELECT products_cat_path
                                FROM " . TABLE_PRODUCTS . "
                                WHERE products_id = '" . tep_db_input($id) . "'";
    $product_path_result_sql = tep_db_query($product_path_select_sql);

    if ($product_path_row = tep_db_fetch_array($product_path_result_sql)) {
        $products_name = tep_get_products_name($id);

        return $product_path_row['products_cat_path'] . ' > ' . $products_name;
    } else {
        return '';
    }
}

function tep_get_product_path2($products_id, $show_all = false) {
    $cPath = '';

    if ($show_all)
        $category_query = tep_db_query("select p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = '" . (int) $products_id . "' and p.products_id = p2c.products_id limit 1");
    else
        $category_query = tep_db_query("select p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = '" . (int) $products_id . "' and p.products_status = '1' and p.products_id = p2c.products_id limit 1");

    if (tep_db_num_rows($category_query)) {
        $category = tep_db_fetch_array($category_query);

        $categories = array();
        tep_get_parent_categories($categories, $category['categories_id'], true);

        $categories = array_reverse($categories);

        $cPath = implode('_', $categories);

        if (tep_not_null($cPath))
            $cPath .= '_';
        $cPath .= $category['categories_id'];
    }
    return $cPath;
}

function tep_get_customer_group_discount($customer_id, $id, $id_type = 'catalog') {
    $cust_group_discount = 0;
    $cust_group_rebate = 0;
    $cust_group_discount_id = 0;
    $cid = $id;
    $cust_group_info_array = array();

    if ($id_type == 'product')
        $cid = tep_get_actual_product_cat_id($id);

    $cat_path = tep_get_particular_cat_path($cid);
    $cat_path_array = explode('_', $cat_path);
    $cat_path_array = array_merge(array(0), $cat_path_array);

    for ($i = count($cat_path_array) - 1; $i >= 0; $i--) {
        $grp_discount_select_str = "SELECT customers_groups_discount, customers_groups_rebate, customers_groups_discount_id
									FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " AS gd
									INNER JOIN " . TABLE_CUSTOMERS . " AS c
										ON gd.customers_groups_id = c.customers_groups_id";

        $grp_discount_where_str = " WHERE gd.categories_id ='" . tep_db_input($cat_path_array[$i]) . "'";

        if ((int) $customer_id > 0) {
            $grp_discount_where_str .= " AND c.customers_id = '" . tep_db_input($customer_id) . "'";
        } else {
            $grp_discount_where_str .= " AND gd.customers_groups_id = '1'";
        }

        $grp_discount_select_sql = $grp_discount_select_str . $grp_discount_where_str;
        $grp_discount_result_sql = tep_db_query($grp_discount_select_sql);
        if ($grp_discount_row = tep_db_fetch_array($grp_discount_result_sql)) {
            $cust_group_discount = $grp_discount_row['customers_groups_discount'];
            $cust_group_rebate = $grp_discount_row['customers_groups_rebate'];
            $cust_group_discount_id = $grp_discount_row['customers_groups_discount_id'];

            break;
        }
    }

    $cust_group_info_array['cust_group_discount'] = $cust_group_discount;
    $cust_group_info_array['cust_group_rebate'] = $cust_group_rebate;
    $cust_group_info_array['cust_group_discount_id'] = $cust_group_discount_id;

    return $cust_group_info_array;
}

function tep_get_product_cat_id_path($id) {
    $product_path_select_sql = "SELECT products_cat_id_path
                                FROM " . TABLE_PRODUCTS . "
                                WHERE products_id = '" . tep_db_input($id) . "'";
    $product_path_result_sql = tep_db_query($product_path_select_sql);
    $product_path_row = tep_db_fetch_array($product_path_result_sql);
    return $product_path_row['products_cat_id_path'];
}

function tep_remove_product($product_id) {
    global $log_object;

    $languages = tep_get_languages();

    require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
    $aws_obj = new ogm_amazon_ws();

    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
        $language_id = $languages[$i]['id'];
        $product_select_sql = "	SELECT pd.products_image, pd.products_description_image, p.products_bundle, p.products_bundle_dynamic, p.products_skip_inventory, p.products_quantity
                                FROM " . TABLE_PRODUCTS . " AS p
                                INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
                                        ON p.products_id = pd.products_id
                                WHERE p.products_id = '" . (int) $product_id . "'
                                        AND language_id = '" . (int) $language_id . "'";
        $product_info_query = tep_db_query($product_select_sql);
        $product_info = tep_db_fetch_array($product_info_query);

        $duplicate_select_sql = "   SELECT COUNT(products_id) AS total
                                    FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                                    WHERE products_image = '" . tep_db_input($product_info["products_image"]) . "'
                                            AND language_id = '" . (int) $language_id . "'";
        $duplicate_image_query = tep_db_query($duplicate_select_sql);
        $duplicate_image = tep_db_fetch_array($duplicate_image_query);


        if ($duplicate_image["total"] < 2) {
            if ($aws_obj->is_image_exists($product_info["products_image"], 'images/products/', 'BUCKET_STATIC')) {
                $aws_obj->delete_file();
            } else if (file_exists(DIR_FS_CATALOG_IMAGES . "/products/" . $product_info["products_image"])) {
                @unlink(DIR_FS_CATALOG_IMAGES . "/products/" . $product_info["products_image"]);
            }
        } else {
            $products_id_select_sql = "	SELECT products_id
                                        FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                                        WHERE products_image = '" . tep_db_input($product_info['products_image']) . "'
                                                AND products_id != '" . (int) $product_id . "'
                                                AND language_id = '" . (int) $language_id . "'
                                        ORDER BY products_id ASC
                                        LIMIT 1";
            $products_id_result_sql = tep_db_query($products_id_select_sql);
            if ($products_id_row = tep_db_fetch_array($products_id_result_sql)) {

                $deleted_image_path_parts = pathinfo($product_info['products_image']);
                $update_products_image_data_sql = array('products_image' => tep_db_prepare_input($products_id_row['products_id'] . ($language_id > 1 ? '_' . $language_id : '') . '.' . $deleted_image_path_parts['extension']));

                if (tep_not_null($deleted_image_path_parts['basename'])) {
                    tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $update_products_image_data_sql, 'update', " products_image = '" . tep_db_input($product_info['products_image']) . "' and language_id = '" . (int) $language_id . "' ");
                }

                if ($aws_obj->is_aws_s3_enabled()) {
                    $source_arr = array('bucket' => 'BUCKET_STATIC', 'filename' => 'images/products/' . $product_info['products_image']);
                    $dest_arr = array('bucket' => 'BUCKET_STATIC', 'filename' => 'images/products/' . $products_id_row['products_id'] . ($language_id > 1 ? '_' . $language_id : '') . '.' . $deleted_image_path_parts['extension']);
                    $aws_obj->move_file($source_arr, $dest_arr);
                } else {
                    @rename(DIR_FS_CATALOG_IMAGES . "products/" . $product_info['products_image'], DIR_FS_CATALOG_IMAGES . "products/" . $products_id_row['products_id'] . ($language_id > 1 ? '_' . $language_id : '') . '.' . $deleted_image_path_parts['extension']);
                }
            }
        }

        $duplicate_desc_image_select_sql = "SELECT COUNT(products_id) AS total
                                            FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                                            WHERE products_description_image = '" . tep_db_input($product_info["products_description_image"]) . "'
                                            AND language_id = '" . (int) $language_id . "'";
        $duplicate_desc_image_query = tep_db_query($duplicate_desc_image_select_sql);
        $duplicate_desc_image = tep_db_fetch_array($duplicate_desc_image_query);

        if ($duplicate_desc_image["total"] < 2) {
            if ($aws_obj->is_image_exists($product_info["products_description_image"], 'images/products/', 'BUCKET_STATIC')) {
                $aws_obj->delete_file();
            } else if (file_exists(DIR_FS_CATALOG_IMAGES . "/products/" . $product_info["products_description_image"])) {
                @unlink(DIR_FS_CATALOG_IMAGES . "/products/" . $product_info["products_description_image"]);
            }
        } else {
            $products_id_select_sql = "	SELECT products_id
                                        FROM " . TABLE_PRODUCTS_DESCRIPTION . "
                                        WHERE products_description_image ='" . tep_db_input($product_info['products_description_image']) . "'
                                                AND products_id !='" . (int) $product_id . "'
                                                AND language_id = '" . (int) $language_id . "'
                                        ORDER BY products_id ASC LIMIT 1";
            $products_id_result_sql = tep_db_query($products_id_select_sql);
            if ($products_id_row = tep_db_fetch_array($products_id_result_sql)) {

                $deleted_description_image_path_parts = pathinfo($product_info['products_description_image']);
                $update_products_description_image_data_sql = array('products_description_image' => tep_db_prepare_input($products_id_row['products_id'] . ($language_id > 1 ? '_' . $language_id : '') . '_large.' . $deleted_description_image_path_parts['extension']));

                if (tep_not_null($deleted_description_image_path_parts['basename'])) {
                    tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $update_products_description_image_data_sql, 'update', " products_description_image = '" . tep_db_input($product_info['products_description_image']) . "' and language_id = '" . (int) $language_id . "' ");
                }

                if ($aws_obj->is_aws_s3_enabled()) {
                    $source_arr = array('bucket' => 'BUCKET_STATIC', 'filename' => 'images/products/' . $product_info['products_description_image']);
                    $dest_arr = array('bucket' => 'BUCKET_STATIC', 'filename' => 'images/products/' . $products_id_row['products_id'] . ($language_id > 1 ? '_' . $language_id : '') . '_large.' . $deleted_description_image_path_parts['extension']);
                    $aws_obj->move_file($source_arr, $dest_arr);
                } else {
                    @rename(DIR_FS_CATALOG_IMAGES . "products/" . $product_info['products_description_image'], DIR_FS_CATALOG_IMAGES . "products/" . $products_id_row['products_id'] . ($language_id > 1 ? '_' . $language_id : '') . '_large.' . $deleted_description_image_path_parts['extension']);
                }
            }
        }
        $product_name = tep_get_products_name($product_id);

        // log product deletion records
        $log_object->insert_log((int) $product_id, 'products_quantity', ($product_info["products_bundle"] == "yes" || $product_info["products_bundle_dynamic"] == "yes" || $product_info["products_skip_inventory"] ? 'NULL' : $product_info["products_quantity"]), 'n/a', LOG_DELETE_PRODUCT, '');
        tep_db_query("UPDATE " . TABLE_LOG_TABLE . " SET log_products_id=CONCAT(log_products_id, '" . tep_db_input(sprintf("##%s<br>(Deleted: %s)", $product_name, date("Y-m-d"))) . "') WHERE log_products_id = '" . (int) $product_id . "'");
    }

    unset($aws_obj);

    tep_db_query("DELETE FROM " . TABLE_SPECIALS . " WHERE products_id = '" . (int) $product_id . "'");

    // **** Begin Separate Price per Customer mod
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_GROUPS . " WHERE products_id = '" . (int) $product_id . "'");
    // **** End Separate Price per Customer mod
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE products_id = '" . (int) $product_id . "'");
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_DESCRIPTION . " WHERE products_id = '" . (int) $product_id . "'");
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_ATTRIBUTES . " WHERE products_id = '" . (int) $product_id . "'");
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_BUNDLES . " WHERE bundle_id = '" . (int) $product_id . "' ");
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_BUNDLES . " WHERE subproduct_id = '" . (int) $product_id . "' ");
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . " WHERE products_id = '" . (int) $product_id . "'");

    tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET . " WHERE products_id = '" . (int) $product_id . "'");
    tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_ATTRIBUTES . " WHERE products_id = '" . (int) $product_id . "'");
    tep_db_query("DELETE FROM " . TABLE_CUSTOMERS_BASKET_BUNDLE . " WHERE products_bundle_id = '" . (int) $product_id . "' OR subproducts_id= '" . (int) $product_id . "'");

    // Delete Promotion Product
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_PROMOTION . " WHERE products_id = '" . (int) $product_id . "'");
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_PROMOTION_DESCRIPTION . " WHERE products_id = '" . (int) $product_id . "'");

    $product_reviews_query = tep_db_query("SELECT reviews_id FROM " . TABLE_REVIEWS . " WHERE products_id = '" . (int) $product_id . "'");
    while ($product_reviews = tep_db_fetch_array($product_reviews_query)) {
        tep_db_query("DELETE FROM " . TABLE_REVIEWS_DESCRIPTION . " WHERE reviews_id = '" . (int) $product_reviews['reviews_id'] . "'");
    }
    tep_db_query("DELETE FROM " . TABLE_REVIEWS . " WHERE products_id = '" . (int) $product_id . "'");

    $follow_product_delete_sql = "	DELETE FROM " . TABLE_PRODUCTS_FOLLOW_PRICE . "
									WHERE products_id = '" . (int) $product_id . "' OR follow_products_id = '" . (int) $product_id . "'";
    tep_db_query($follow_product_delete_sql);

    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_COST . " WHERE products_id = '" . (int)$product_id . "'");

    // Delete Payment Methods Restrictions
    tep_db_query("DELETE FROM " . TABLE_PRODUCTS_PAYMENT_METHODS_RESTRICTIONS . " WHERE products_id = '" . (int)$product_id . "'");

    tep_db_query("DELETE FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int) $product_id . "'");

    if (USE_CACHE == 'true') {
        tep_reset_cache_block('categories');
        tep_reset_cache_block('also_purchased');
    }
}

////
// Check if this product has the right to be checkout even if low qty or negative qty
// TABLES: products
function tep_check_product_skip_inventory($products_id) {
    $products_id = tep_get_prid($products_id);
    $prod_permission_query = tep_db_query("SELECT products_skip_inventory FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . (int) $products_id . "'");
    $prod_permission = tep_db_fetch_array($prod_permission_query);

    return ($prod_permission['products_skip_inventory']);
}

function tep_remove_order($order_id, $restock = false) {
    if ($restock == 'on') {
        $order_query = tep_db_query("select products_id, products_quantity from " . TABLE_ORDERS_PRODUCTS . " where orders_id = '" . (int) $order_id . "'");
        while ($order = tep_db_fetch_array($order_query)) {
            tep_db_query("update " . TABLE_PRODUCTS . " set products_quantity = products_quantity + " . $order['products_quantity'] . ", products_ordered = products_ordered - " . $order['products_quantity'] . " where products_id = '" . (int) $order['products_id'] . "'");
        }
    }

    //begin PayPal_Shopping_Cart_IPN
//    $ipn_query = tep_db_query("select paypal_ipn_id from " . TABLE_ORDERS . " where orders_id = '" . (int) $order_id . "'");
//    if (tep_db_num_rows($ipn_query)) { // this is a paypal ipn order
//        include_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/database_tables.inc.php');
//        $ipn_order = tep_db_fetch_array($ipn_query);
//        $paypal_ipn_id = $ipn_order['paypal_ipn_id'];
//        tep_db_query("delete from " . TABLE_PAYPAL . " where paypal_ipn_id = '" . (int) $paypal_ipn_id . "'");
//        tep_db_query("delete from " . TABLE_PAYPAL_PAYMENT_STATUS_HISTORY . " where paypal_ipn_id = '" . (int) $paypal_ipn_id . "'");
//    }
    //end PayPal_Shopping_Cart_IPN

    tep_db_query("delete from " . TABLE_ORDERS . " where orders_id = '" . (int) $order_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_PRODUCTS . " where orders_id = '" . (int) $order_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_PRODUCTS_ATTRIBUTES . " where orders_id = '" . (int) $order_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_id = '" . (int) $order_id . "'");
    tep_db_query("delete from " . TABLE_ORDERS_TOTAL . " where orders_id = '" . (int) $order_id . "'");
}

function tep_admin_group_unlock_permission() {
    $admin_group_array = array();
    $unlock_admin_group_select_sql = "SELECT ag.admin_groups_id, ag.admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " AS ag, " . TABLE_ADMIN_FILES_ACTIONS . " AS afa WHERE afa.admin_files_actions_key='UNLOCK_OTHERS_ORDERS' AND FIND_IN_SET(ag.admin_groups_id, afa.admin_groups_id)";
    $unlock_admin_group_result_sql = tep_db_query($unlock_admin_group_select_sql);
    while ($unlock_admin_group_row = tep_db_fetch_array($unlock_admin_group_result_sql)) {
        $admin_group_array[$unlock_admin_group_row["admin_groups_id"]] = $unlock_admin_group_row["admin_groups_name"];
    }

    return $admin_group_array;
}

// unloack normal PO
function tep_admin_group_unlock_po_permission() {
    $admin_group_array = array();
    $unlock_admin_group_select_sql = "SELECT ag.admin_groups_id, ag.admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " AS ag, " . TABLE_ADMIN_FILES_ACTIONS . " AS afa WHERE afa.admin_files_actions_key='PO_UNLOCK_PO' AND FIND_IN_SET(ag.admin_groups_id, afa.admin_groups_id)";
    $unlock_admin_group_result_sql = tep_db_query($unlock_admin_group_select_sql);
    while ($unlock_admin_group_row = tep_db_fetch_array($unlock_admin_group_result_sql)) {
        $admin_group_array[$unlock_admin_group_row["admin_groups_id"]] = $unlock_admin_group_row["admin_groups_name"];
    }

    return $admin_group_array;
}

// unloack DTU PO
function tep_admin_group_unlock_dtu_po_permission() {
    $admin_group_array = array();
    $unlock_admin_group_select_sql = "SELECT ag.admin_groups_id, ag.admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " AS ag, " . TABLE_ADMIN_FILES_ACTIONS . " AS afa WHERE afa.admin_files_actions_key='DTU_UNLOCK_PO' AND FIND_IN_SET(ag.admin_groups_id, afa.admin_groups_id)";
    $unlock_admin_group_result_sql = tep_db_query($unlock_admin_group_select_sql);
    while ($unlock_admin_group_row = tep_db_fetch_array($unlock_admin_group_result_sql)) {
        $admin_group_array[$unlock_admin_group_row["admin_groups_id"]] = $unlock_admin_group_row["admin_groups_name"];
    }

    return $admin_group_array;
}

// unloack API PO
function tep_admin_group_unlock_api_po_permission() {
    $admin_group_array = array();
    $unlock_admin_group_select_sql = "SELECT ag.admin_groups_id, ag.admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " AS ag, " . TABLE_ADMIN_FILES_ACTIONS . " AS afa WHERE afa.admin_files_actions_key='API_UNLOCK_PO' AND FIND_IN_SET(ag.admin_groups_id, afa.admin_groups_id)";
    $unlock_admin_group_result_sql = tep_db_query($unlock_admin_group_select_sql);
    while ($unlock_admin_group_row = tep_db_fetch_array($unlock_admin_group_result_sql)) {
        $admin_group_array[$unlock_admin_group_row["admin_groups_id"]] = $unlock_admin_group_row["admin_groups_name"];
    }

    return $admin_group_array;
}

// unloack consignment PO
function tep_admin_group_unlock_cdk_po_permission() {
    $admin_group_array = array();
    $unlock_admin_group_select_sql = "SELECT ag.admin_groups_id, ag.admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " AS ag, " . TABLE_ADMIN_FILES_ACTIONS . " AS afa WHERE afa.admin_files_actions_key='CDK_UNLOCK_PO' AND FIND_IN_SET(ag.admin_groups_id, afa.admin_groups_id)";
    $unlock_admin_group_result_sql = tep_db_query($unlock_admin_group_select_sql);
    while ($unlock_admin_group_row = tep_db_fetch_array($unlock_admin_group_result_sql)) {
        $admin_group_array[$unlock_admin_group_row["admin_groups_id"]] = $unlock_admin_group_row["admin_groups_name"];
    }

    return $admin_group_array;
}

// release those orders locked by this admin id when logoff
function tep_release_locked_order($admin_id, $inclusion = true) {
    if (is_array($admin_id) && count($admin_id)) { // batch release for a group of inactive admin staff
        if ($inclusion == true) {
            $unlock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_locked_by IS NOT NULL AND orders_locked_by IN (" . implode(', ', $admin_id) . ")";
            tep_db_query($unlock_orders_update_sql);
        } else {
            $unlock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_locked_by IS NOT NULL AND orders_locked_by NOT IN (" . implode(', ', $admin_id) . ")";
            tep_db_query($unlock_orders_update_sql);
        }
    } else { // for individual admin
        $unlock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_locked_by = '" . (int) $admin_id . "'";
        tep_db_query($unlock_orders_update_sql);
    }
}

function tep_get_payment_info_verified_date($check_info, $compare_type, $verified_status = '') {
    $payment_method_array = array(
        'credit_card',
        'paypal_payer_id',
        'moneybookers'
    );
    $verified_date = '';

    if (in_array($compare_type, $payment_method_array)) {
        $s_key = '';
        switch ($compare_type) {
            case 'credit_card':
                if (isset($check_info['card_number']) && tep_not_null($check_info['card_number'])) {
                    $s_key = substr($check_info['card_number'], -4);
                }
                break;

            case 'paypal_payer_id':
                if (isset($check_info['payer_id']) && tep_not_null($check_info['payer_id'])) {
                    $s_key = tep_db_input($check_info['payer_id']);
                }
                break;

            case 'moneybookers':
                if (isset($check_info['email']) && tep_not_null($check_info['email'])) {
                    $s_key = tep_db_input($check_info['email']);
                }
                break;
        }

        if (!empty($s_key)) {
            $m_sel = "  SELECT statistic_value FROM " . TABLE_CUSTOMERS_PURCHASE_STATISTIC . "
                        WHERE customers_id = " . $check_info['customers_id'] . "
                            AND statistic_key = '" . $compare_type . "_" . $s_key . "'";
            $m_res = tep_db_query($m_sel);
            if ($m_row = tep_db_fetch_array($m_res)) {
                $verified_date = $m_row['statistic_value'];
            } else {
                $orders_array = array();

                $check_status = array("2", "3");
                if (is_array($verified_status) && count($verified_status)) {
                    $check_status = $verified_status;
                }

                switch ($compare_type) {
                    case 'credit_card':
                        if (isset($check_info['card_number']) && tep_not_null($check_info['card_number'])) {
                            $formatted_credit_card_number = substr($check_info['card_number'], -4);

                            $adyen_credit_card_select_sql = "   SELECT o.orders_id
                                                                FROM " . TABLE_ORDERS . " AS o
                                                                INNER JOIN " . TABLE_ADYEN . " AS a
                                                                    ON o.orders_id = a.adyen_order_id
                                                                WHERE a.adyen_cc_card_summary = '" . tep_db_input($formatted_credit_card_number) . "'
                                                                    AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                    AND o.date_purchased <= '" . $check_info['date_purchased'] . "'
                                                                    AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $adyen_credit_card_result_sql = tep_db_query($adyen_credit_card_select_sql);
                            while ($adyen_credit_card_row = tep_db_fetch_array($adyen_credit_card_result_sql)) {
                                $orders_array[] = $adyen_credit_card_row['orders_id'];
                            }

                            $payu_credit_card_select_sql = "	SELECT o.orders_id
                                                                FROM " . TABLE_ORDERS . " AS o
                                                                INNER JOIN " . TABLE_PAYU . " AS p
                                                                        ON o.orders_id = p.order_id
                                                                WHERE p.cc_number LIKE '%" . tep_db_input($formatted_credit_card_number) . "'
                                                                        AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                        AND o.date_purchased <= '" . $check_info['date_purchased'] . "'
                                                                        AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $payu_credit_card_result_sql = tep_db_query($payu_credit_card_select_sql);
                            while ($payu_credit_card_row = tep_db_fetch_array($payu_credit_card_result_sql)) {
                                $orders_array[] = $payu_credit_card_row['orders_id'];
                            }

                            $bibit_credit_card_select_sql = "   SELECT o.orders_id
                                                                FROM " . TABLE_ORDERS . " AS o
                                                                INNER JOIN " . TABLE_BIBIT . " AS b
                                                                    ON o.orders_id = b.orders_id
                                                                WHERE b.bibit_card_number LIKE '%" . tep_db_input($formatted_credit_card_number) . "'
                                                                    AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                    AND o.date_purchased <= '" . $check_info['date_purchased'] . "'
                                                                    AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $bibit_credit_card_result_sql = tep_db_query($bibit_credit_card_select_sql);
                            while ($bibit_credit_card_row = tep_db_fetch_array($bibit_credit_card_result_sql)) {
                                $orders_array[] = $bibit_credit_card_row['orders_id'];
                            }

                            $gc_credit_card_select_sql = "  SELECT o.orders_id
                                                            FROM " . TABLE_ORDERS . " AS o
                                                            INNER JOIN " . TABLE_GLOBAL_COLLECT . " AS gc
                                                                ON o.orders_id = gc.global_collect_orders_id
                                                            WHERE gc.global_collect_cc_last_4_digit = '" . tep_db_input($formatted_credit_card_number) . "'
                                                                AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                AND o.date_purchased < '" . $check_info['date_purchased'] . "'
                                                                AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $gc_credit_card_result_sql = tep_db_query($gc_credit_card_select_sql);
                            while ($gc_credit_card_row = tep_db_fetch_array($gc_credit_card_result_sql)) {
                                $orders_array[] = $gc_credit_card_row['orders_id'];
                            }
                            $pw_credit_card_select_sql = "  SELECT o.orders_id
                                                            FROM " . TABLE_ORDERS . " AS o
                                                            INNER JOIN " . TABLE_ANALYSIS_CREDIT_CARD_INFO . " AS acci
                                                                ON o.orders_id = acci.orders_id
                                                            WHERE acci.fomatted_pan = '" . tep_db_input($check_info['card_number']) . "'
                                                                AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                AND o.date_purchased < '" . $check_info['date_purchased'] . "'
                                                                AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $pw_credit_card_result_sql = tep_db_query($pw_credit_card_select_sql);
                            while ($pw_credit_card_row = tep_db_fetch_array($pw_credit_card_result_sql)) {
                                $orders_array[] = $pw_credit_card_row['orders_id'];
                            }
                        }

                        break;
                    case 'paypal_payer_id':
                        if (isset($check_info['payer_id']) && tep_not_null($check_info['payer_id'])) {
                            $check_credit_card_select_sql = "	SELECT o.orders_id
                                                                FROM " . TABLE_ORDERS . " AS o
                                                                INNER JOIN " . TABLE_PAYPAL . " AS p
                                                                        ON o.orders_id = p.invoice
                                                                WHERE p.payer_id = '" . tep_db_input($check_info['payer_id']) . "'
                                                                        AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                        AND o.date_purchased <= '" . $check_info['date_purchased'] . "'
                                                                        AND o.orders_status IN ('" . implode("','", $check_status) . "')
                                                                UNION
                                                                SELECT o.orders_id
                                                                FROM " . TABLE_ORDERS . " AS o
                                                                INNER JOIN " . TABLE_PAYPALEC . " AS pc
                                                                        ON o.orders_id = pc.paypal_order_id
                                                                WHERE pc.payer_id = '" . tep_db_input($check_info['payer_id']) . "'
                                                                        AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                        AND o.date_purchased <= '" . $check_info['date_purchased'] . "'
                                                                        AND o.orders_status IN ('" . implode("','", $check_status) . "')
                                                                UNION
                                                                SELECT o.orders_id
                                                                FROM " . TABLE_ORDERS . " AS o
                                                                INNER JOIN " . TABLE_ANALYSIS_PG_INFO . " AS api
                                                                        ON (o.orders_id = api.orders_id AND api.info_key = 'payer_id')
                                                                WHERE api.info_value = '" . tep_db_input($check_info['payer_id']) . "'
                                                                        AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                                        AND o.date_purchased <= '" . $check_info['date_purchased'] . "'
                                                                        AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $check_credit_card_result_sql = tep_db_query($check_credit_card_select_sql);
                            while ($check_credit_card_row = tep_db_fetch_array($check_credit_card_result_sql)) {
                                $orders_array[] = $check_credit_card_row['orders_id'];
                            }
                        }
                        break;
                    case 'moneybookers':
                        if (isset($check_info['email']) && tep_not_null($check_info['email'])) {
                            $check_select_sql = "   SELECT o.orders_id
                                                    FROM " . TABLE_ORDERS . " AS o
                                                    INNER JOIN " . TABLE_PAYMENT_MONEYBOOKERS . " AS p
                                                        ON o.orders_id = p.mb_trans_id
                                                    WHERE p.mb_payer_email = '" . tep_db_input($check_info['email']) . "'
                                                        AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                        AND o.date_purchased <= '" . $check_info['date_purchased'] . "'
                                                        AND o.orders_status IN ('" . implode("','", $check_status) . "')
                                                    UNION
                                                    SELECT o.orders_id
                                                    FROM " . TABLE_ORDERS . " AS o
                                                    INNER JOIN " . TABLE_ANALYSIS_PG_INFO . " AS api
                                                        ON (o.orders_id = api.orders_id AND api.info_key = 'moneybooker_email')
                                                    WHERE api.info_value = '" . tep_db_input($check_info['email']) . "'
                                                        AND o.customers_id = '" . (int) $check_info['customers_id'] . "'
                                                        AND o.date_purchased <= '" . $check_info['date_purchased'] . "'
                                                        AND o.orders_status IN ('" . implode("','", $check_status) . "')";
                            $check_result_sql = tep_db_query($check_select_sql);
                            while ($check_row = tep_db_fetch_array($check_result_sql)) {
                                $orders_array[] = $check_row['orders_id'];
                            }
                        }
                        break;
                }

                if (count($orders_array)) {
                    $orders_status_history_select_sql = "   SELECT first_date
                                                            FROM " . TABLE_ORDERS_STATUS_STAT . "
                                                            WHERE orders_id IN ('" . implode("','", $orders_array) . "')
                                                                AND orders_status_id = '2'
                                                            ORDER BY first_date
                                                            LIMIT 1";
                    $orders_status_history_result_sql = tep_db_query($orders_status_history_select_sql);
                    if ($orders_status_history_row = tep_db_fetch_array($orders_status_history_result_sql)) {
                        $verified_date = $orders_status_history_row['first_date'];
                        $m_sel = "  INSERT INTO " . TABLE_CUSTOMERS_PURCHASE_STATISTIC . " (customers_id, statistic_key, statistic_value, created_date)
                                    VALUES (" . $check_info['customers_id'] . ", '" . $compare_type . '_' . $s_key . "', '" . $verified_date . "', NOW())
                                    ON DUPLICATE KEY UPDATE customers_id = customers_id";
                        tep_db_query($m_sel);
                    }
                }
            }

            if ($verified_date !== '') {
                $verified_date = date('Y-m-d', strtotime($verified_date));
            }
        }
    }
    return $verified_date;
}

function tep_check_duplicate_name($original_customer, $payment_method) {
    $customer_id_array = array();
    $duplicate_name_select_sql = '';
    if ($payment_method == 'paypal') {
        $duplicate_name_select_sql = "SELECT DISTINCT(o.customers_id) AS cust_id FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYPAL . " AS p ON o.orders_id = p.invoice WHERE p.first_name = '" . tep_db_input($original_customer[0]) . "' AND p.last_name = '" . tep_db_input($original_customer[1]) . "'";
    } else if ($payment_method == 'worldpay') {
        $duplicate_name_select_sql = "SELECT DISTINCT(o.customers_id) AS cust_id FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYMENT_EXTRA_INFO . " AS p ON o.orders_id = p.orders_id WHERE p.credit_card_owner = '" . tep_db_input($original_customer) . "'";
    } else if ($payment_method == 'paypalEC') {
        $duplicate_name_select_sql = "	SELECT DISTINCT(o.customers_id) AS cust_id
										FROM " . TABLE_ORDERS . " AS o
										INNER JOIN " . TABLE_PAYPAL . " AS p
											ON o.orders_id = p.invoice
										WHERE p.first_name = '" . tep_db_input($original_customer[0]) . "'
											AND p.last_name = '" . tep_db_input($original_customer[1]) . "'
										UNION
										SELECT DISTINCT(o.customers_id) AS cust_id
										FROM " . TABLE_ORDERS . " AS o
										INNER JOIN " . TABLE_PAYPALEC . " AS pc
											ON o.orders_id = pc.paypal_order_id
										WHERE pc.first_name = '" . tep_db_input($original_customer[0]) . "'
											AND pc.last_name = '" . tep_db_input($original_customer[1]) . "'";
    } else if ($payment_method == 'payU') {
        $duplicate_name_select_sql = "SELECT DISTINCT(o.customers_id) AS cust_id FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYU . " AS p ON o.orders_id = p.order_id WHERE p.payer_name = '" . tep_db_input($original_customer[0] . ' ' . $original_customer[1]) . "'";
    }

    if (tep_not_null($duplicate_name_select_sql)) {
        $duplicate_name_result_sql = tep_db_query($duplicate_name_select_sql);

        while ($duplicate_name_row = tep_db_fetch_array($duplicate_name_result_sql)) {
            $customer_id_array[] = $duplicate_name_row["cust_id"];
        }
    }

    return $customer_id_array;
}

function tep_check_duplicate_payment_email($original_customer, $payment_method) {
    $customer_id_array = array();
    $duplicate_email_select_sql = '';

    if ($payment_method == 'mb') {
        $duplicate_email_select_sql = "	SELECT DISTINCT(o.customers_id) AS cust_id
                                        FROM " . TABLE_ORDERS . " AS o
                                        INNER JOIN " . TABLE_PAYMENT_MONEYBOOKERS . " AS mb
                                                ON o.orders_id = mb.mb_trans_id
                                        WHERE mb_payer_email = '" . tep_db_input($original_customer) . "'";
    } else if ($payment_method == 'egold') {
        $duplicate_email_select_sql = "	SELECT DISTINCT(o.customers_id) AS cust_id
                                        FROM " . TABLE_ORDERS . " AS o
                                        INNER JOIN " . TABLE_PAYMENT_EGOLD . " AS egold
                                                ON o.orders_id = egold.orders_id
                                        WHERE egold_payer_account = '" . tep_db_input($original_customer) . "'";
    } else if ($payment_method == 'webmoney') {
        $duplicate_email_select_sql = "	SELECT DISTINCT(o.customers_id) AS cust_id
                                        FROM " . TABLE_ORDERS . " AS o
                                        INNER JOIN " . TABLE_PAYMENT_WEBMONEY . " AS wm
                                                ON o.orders_id = wm.webmoney_payment_no
                                        WHERE wm.webmoney_payer_wm = '" . tep_db_input($original_customer) . "'";
    }

    if (tep_not_null($duplicate_email_select_sql)) {
        $duplicate_email_result_sql = tep_db_query($duplicate_email_select_sql);

        while ($duplicate_email_row = tep_db_fetch_array($duplicate_email_result_sql)) {
            $customer_id_array[] = $duplicate_email_row["cust_id"];
        }
    }

    return $customer_id_array;
}

function tep_get_distinct_name_used($customer_id, $current_name, $payment_method) {
    $customer_name_array = array();
    $name_count_select_sql = '';
    if ($payment_method == 'paypal') {
        $name_count_select_sql = "SELECT DISTINCT(concat(p.first_name, ' ', p.last_name)) cust_name FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYPAL . " AS p ON o.orders_id = p.invoice WHERE o.customers_id = '" . (int) $customer_id . "'";
    } else if ($payment_method == 'worldpay') {
        $name_count_select_sql = "SELECT DISTINCT(p.credit_card_owner) cust_name FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYMENT_EXTRA_INFO . " AS p ON o.orders_id = p.orders_id WHERE o.customers_id = '" . (int) $customer_id . "'";
    } else if ($payment_method == 'paypalEC') {
        $name_count_select_sql = "	SELECT DISTINCT(concat(p.first_name, ' ', p.last_name)) cust_name
									FROM " . TABLE_ORDERS . " AS o
									INNER JOIN " . TABLE_PAYPALEC . " AS p
										ON o.orders_id = p.paypal_order_id
									WHERE o.customers_id = '" . $customer_id . "'
									UNION
									SELECT DISTINCT(concat(p.first_name, ' ', p.last_name)) cust_name
									FROM " . TABLE_ORDERS . " AS o
									INNER JOIN " . TABLE_PAYPAL . " AS p
										ON o.orders_id = p.invoice
									WHERE o.customers_id = '" . (int) $customer_id . "'
									";
    } else if ($payment_method == 'payU') {
        $name_count_select_sql = "SELECT DISTINCT(p.payer_name) cust_name FROM " . TABLE_ORDERS . " AS o INNER JOIN " . TABLE_PAYU . " AS p ON o.orders_id = p.order_id WHERE o.customers_id = '" . (int) $customer_id . "'";
    }

    if (tep_not_null($name_count_select_sql)) {
        $name_count_result_sql = tep_db_query($name_count_select_sql);

        while ($name_count_row = tep_db_fetch_array($name_count_result_sql)) {
            $customer_name_array[] = $name_count_row["cust_name"];
        }
    }

    return $customer_name_array;
}

function tep_get_distinct_payment_email_used($customer_id, $payment_method) {
    $customer_array = array();

    $email_count_select_sql = '';
    if ($payment_method == 'mb') {
        $email_count_select_sql = " SELECT DISTINCT(mb.mb_payer_email) AS payment_email
                                    FROM " . TABLE_ORDERS . " AS o
                                    INNER JOIN " . TABLE_PAYMENT_MONEYBOOKERS . " AS mb
                                            ON o.orders_id = mb.mb_order_id
                                    WHERE o.customers_id = '" . (int) $customer_id . "' AND mb.mb_payer_email <> ''";
    } else if ($payment_method == 'egold') {
        $email_count_select_sql = " SELECT DISTINCT(egold.egold_payer_account) AS payment_email
                                    FROM " . TABLE_ORDERS . " AS o
                                    INNER JOIN " . TABLE_PAYMENT_EGOLD . " AS egold
                                            ON o.orders_id = egold.orders_id
                                    WHERE o.customers_id = '" . (int) $customer_id . "' AND egold.egold_payer_account <> ''";
    } else if ($payment_method == 'webmoney') {
        $email_count_select_sql = " SELECT DISTINCT(wm.webmoney_payer_wm) AS payment_email
                                    FROM " . TABLE_ORDERS . " AS o
                                    INNER JOIN " . TABLE_PAYMENT_WEBMONEY . " AS wm
                                            ON o.orders_id = wm.webmoney_payment_no
                                    WHERE o.customers_id = '" . (int) $customer_id . "' AND wm.webmoney_payer_wm <> ''";
    }

    if (tep_not_null($email_count_select_sql)) {
        $email_count_result_sql = tep_db_query($email_count_select_sql);

        while ($email_count_row = tep_db_fetch_array($email_count_result_sql)) {
            $customer_array[] = $email_count_row["cust_name"];
        }
    }

    return $customer_array;
}

function tep_get_latest_payment_method($customer_id, $order_id) {
    $payment_method_array = array();

    $m_sel = "  SELECT statistic_value FROM " . TABLE_CUSTOMERS_PURCHASE_STATISTIC . "
                WHERE customers_id = " . $customer_id . "
                    AND statistic_key = 'last_payment_method_id'";
    $m_res = tep_db_query($m_sel);
    if ($m_row = tep_db_fetch_array($m_res)) {
        $payment_method_array['payment_methods_id'] = $m_row['statistic_value'];

        $m_sel = "  SELECT statistic_value FROM " . TABLE_CUSTOMERS_PURCHASE_STATISTIC . "
                    WHERE customers_id = " . $customer_id . "
                        AND statistic_key = 'last_payment_method_title'";
        $m_res = tep_db_query($m_sel);
        if ($m_row = tep_db_fetch_array($m_res)) {
            $payment_method_array['payment_methods_title'] = $m_row['statistic_value'];
        }
    } else {
        $payment_method_select_sql = "	SELECT o.payment_methods_id, o.date_purchased, pm.payment_methods_title, parent_pm.payment_methods_title as parent_payment_methods_title
                                        FROM " . TABLE_ORDERS . " as o
                                        LEFT JOIN " . TABLE_PAYMENT_METHODS . " as pm
                                            ON o.payment_methods_id = pm.payment_methods_id
                                        LEFT JOIN " . TABLE_PAYMENT_METHODS . " as parent_pm
                                            ON o.payment_methods_parent_id = parent_pm.payment_methods_id
                                        INNER JOIN " . TABLE_ORDERS_STATUS_STAT . " AS oss
                                            ON oss.orders_id = o.orders_id
                                                AND oss.orders_status_id = 7
                                        WHERE o.customers_id = '" . (int) $customer_id . "'
                                        ORDER BY oss.first_date DESC LIMIT 1";
        $payment_method_result_sql = tep_db_query($payment_method_select_sql);
        if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
            $payment_method_array['payment_methods_id'] = $payment_method_row['payment_methods_id'];
            unset($payment_method_row['payment_methods_id']);

            if (empty($payment_method_array['payment_methods_id'])) {
                $payment_method_array['payment_methods_title'] = 'Full Store Credit';
            } else {
                $payment_method_array['payment_methods_title'] = $payment_method_row['parent_payment_methods_title'] . ' -> ' . $payment_method_row['payment_methods_title'];
            }

            $m_data = array(
                'customers_id' => $customer_id,
                'statistic_key' => 'last_payment_method_id',
                'statistic_value' => $payment_method_array['payment_methods_id'],
                'created_date' => $payment_method_row['date_purchased']
            );
            tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data);
            unset($m_data);

            $m_data = array(
                'customers_id' => $customer_id,
                'statistic_key' => 'last_payment_method_title',
                'statistic_value' => $payment_method_array['payment_methods_title'],
                'created_date' => $payment_method_row['date_purchased']
            );
            tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data);
            unset($m_data);
        }
    }

    return $payment_method_array;
}

function tep_get_customer_share_ip($ip_add, $order_type = "CO", $reference_date_time = '') {
    // Look for number of customer sharing the same ip address
    $customer_id_array = array();

    $reference_date_time = tep_not_null($reference_date_time) ? $reference_date_time : date('Y-m-d H:i:s');

    if ($order_type == 'BO') { // Buyback Order
        $share_ip_select_sql = "SELECT DISTINCT(customers_id) AS user_id
                                FROM " . TABLE_BUYBACK_REQUEST_GROUP . "
                                WHERE buyback_request_group_date >= DATE_SUB('" . $reference_date_time . "', INTERVAL 7 DAY)
                                        AND buyback_request_group_date <= DATE_ADD('" . $reference_date_time . "', INTERVAL 7 DAY)
                                        AND remote_addr = '" . tep_db_input($ip_add) . "'";
    } else {
        $share_ip_select_sql = "SELECT DISTINCT(customers_id) AS user_id
                                FROM " . TABLE_ORDERS . "
                                WHERE date_purchased >= DATE_SUB('" . $reference_date_time . "', INTERVAL 7 DAY)
                                        AND date_purchased <= DATE_ADD('" . $reference_date_time . "', INTERVAL 7 DAY)
                                        AND remote_addr = '" . tep_db_input($ip_add) . "'";
    }

    $share_ip_result_sql = tep_db_query($share_ip_select_sql);

    while ($share_ip_row = tep_db_fetch_array($share_ip_result_sql)) {
        $customer_id_array[] = $share_ip_row["user_id"];
    }

    return $customer_id_array;
}

function tep_get_customer_share_phone($tel_code, $tel_num, $mode = 'all', $reference_date_time = '') {
    // Look for number of customer sharing the same telephone number (used to made order not teal time stat)
    // $tel_num just consists of digits
    $tel_code = preg_replace('/[^\d]/', '', $tel_code);
    $tel_num = preg_replace('/[^\d]/', '', $tel_num);

    $customer_id_array = array();
    if ($mode == 'all') {
        $share_tel_select_sql = "   SELECT DISTINCT(o.customers_id) AS cust_id
                                    FROM " . TABLE_ORDERS . " AS o
                                    LEFT JOIN " . TABLE_COUNTRIES . " AS country
                                            ON (o.customers_country=country.countries_name)
                                    WHERE IF(o.customers_country_international_dialing_code IS NOT NULL, o.customers_country_international_dialing_code, country.countries_international_dialing_code) = '" . tep_db_input($tel_code) . "'
                                            AND o.customers_telephone = '" . tep_db_input($tel_num) . "'";
    } else {
        $reference_date_time = tep_not_null($reference_date_time) ? $reference_date_time : date('Y-m-d H:i:s');
        $tel_num = strlen($tel_num) > 5 ? substr($tel_num, 0, 5) : $tel_num;

        $share_tel_select_sql = "   SELECT DISTINCT(o.customers_id) AS cust_id
                                    FROM " . TABLE_ORDERS . " AS o
                                    LEFT JOIN " . TABLE_COUNTRIES . " AS country
                                            ON (o.customers_country=country.countries_name)
                                    WHERE o.date_purchased >= DATE_SUB('" . $reference_date_time . "', INTERVAL 14 DAY)
                                            AND o.date_purchased <= DATE_ADD('" . $reference_date_time . "', INTERVAL 14 DAY)
                                            AND IF(o.customers_country_international_dialing_code IS NOT NULL, o.customers_country_international_dialing_code, country.countries_international_dialing_code) = '" . tep_db_input($tel_code) . "'
                                            AND o.customers_telephone LIKE '" . tep_db_input($tel_num) . "%'";
    }
    $share_tel_result_sql = tep_db_query($share_tel_select_sql);

    while ($share_tel_row = tep_db_fetch_array($share_tel_result_sql)) {
        $customer_id_array[] = $share_tel_row["cust_id"];
    }

    return $customer_id_array;
}

function tep_get_buyback_share_phone($tel_code, $tel_num, $mode = 'all', $reference_date_time = '') {
    // Look for number of customer sharing the same telephone number (used to made order not teal time stat)
    // $tel_num just consists of digits
    $tel_code = preg_replace('/[^\d]/', '', $tel_code);
    $tel_num = preg_replace('/[^\d]/', '', $tel_num);

    $customer_id_array = array();
    if ($mode == 'all') {
        $share_tel_select_sql = "   SELECT DISTINCT(brg.customers_id) AS cust_id
                                    FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
                                    INNER JOIN " . TABLE_CUSTOMERS . " AS c
                                            ON c.customers_id = brg.customers_id
                                    LEFT JOIN " . TABLE_COUNTRIES . " AS country
                                            ON (c.customers_country_dialing_code_id=country.countries_id)
                                    WHERE country.countries_international_dialing_code = '" . tep_db_input($tel_code) . "'
                                            AND (brg.buyback_request_contact_telephone = '" . tep_db_input($tel_num) . "'
                                                            OR brg.buyback_request_contact_telephone = '0" . tep_db_input($tel_num) . "')";
    } else {
        $reference_date_time = tep_not_null($reference_date_time) ? $reference_date_time : date('Y-m-d H:i:s');
        $tel_num = strlen($tel_num) > 5 ? substr($tel_num, 0, 5) : $tel_num;

        $share_tel_select_sql = "   SELECT DISTINCT(brg.customers_id) AS cust_id
                                    FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
                                    INNER JOIN " . TABLE_CUSTOMERS . " AS c
                                            ON c.customers_id = brg.customers_id
                                    LEFT JOIN " . TABLE_COUNTRIES . " AS country
                                            ON (c.customers_country_dialing_code_id=country.countries_id)
                                    WHERE brg.buyback_request_group_date >= DATE_SUB('" . $reference_date_time . "', INTERVAL 7 DAY)
                                            AND brg.buyback_request_group_date <= DATE_ADD('" . $reference_date_time . "', INTERVAL 7 DAY)
                                            AND country.countries_international_dialing_code = '" . tep_db_input($tel_code) . "'
                                            AND brg.buyback_request_contact_telephone LIKE '%" . tep_db_input($tel_num) . "%'";
    }

    $share_tel_result_sql = tep_db_query($share_tel_select_sql);

    while ($share_tel_row = tep_db_fetch_array($share_tel_result_sql)) {
        $customer_id_array[] = $share_tel_row["cust_id"];
    }

    return $customer_id_array;
}

function tep_reset_cache_block($cache_block) {
    global $cache_blocks;

    for ($i = 0, $n = sizeof($cache_blocks); $i < $n; $i++) {
        if ($cache_blocks[$i]['code'] == $cache_block) {
            if ($cache_blocks[$i]['multiple']) {
                if ($dir = @opendir(DIR_FS_CACHE)) {
                    while ($cache_file = readdir($dir)) {
                        $cached_file = $cache_blocks[$i]['file'];
                        $languages = tep_get_languages();
                        for ($j = 0, $k = sizeof($languages); $j < $k; $j++) {
                            $cached_file_unlink = ereg_replace_dep('-language', '-' . $languages[$j]['directory'], $cached_file);
                            if (ereg_dep('^' . $cached_file_unlink, $cache_file)) {
                                @unlink(DIR_FS_CACHE . $cache_file);
                            }
                        }
                    }
                    closedir($dir);
                }
            } else {
                $cached_file = $cache_blocks[$i]['file'];
                $languages = tep_get_languages();
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $cached_file = ereg_replace_dep('-language', '-' . $languages[$i]['directory'], $cached_file);
                    @unlink(DIR_FS_CACHE . $cached_file);
                }
            }
            break;
        }
    }
}

function tep_get_file_permissions($mode) {
    // determine type
    if (($mode & 0xC000) == 0xC000) { // unix domain socket
        $type = 's';
    } elseif (($mode & 0x4000) == 0x4000) { // directory
        $type = 'd';
    } elseif (($mode & 0xA000) == 0xA000) { // symbolic link
        $type = 'l';
    } elseif (($mode & 0x8000) == 0x8000) { // regular file
        $type = '-';
    } elseif (($mode & 0x6000) == 0x6000) { //bBlock special file
        $type = 'b';
    } elseif (($mode & 0x2000) == 0x2000) { // character special file
        $type = 'c';
    } elseif (($mode & 0x1000) == 0x1000) { // named pipe
        $type = 'p';
    } else { // unknown
        $type = '?';
    }

    // determine permissions
    $owner['read'] = ($mode & 00400) ? 'r' : '-';
    $owner['write'] = ($mode & 00200) ? 'w' : '-';
    $owner['execute'] = ($mode & 00100) ? 'x' : '-';
    $group['read'] = ($mode & 00040) ? 'r' : '-';
    $group['write'] = ($mode & 00020) ? 'w' : '-';
    $group['execute'] = ($mode & 00010) ? 'x' : '-';
    $world['read'] = ($mode & 00004) ? 'r' : '-';
    $world['write'] = ($mode & 00002) ? 'w' : '-';
    $world['execute'] = ($mode & 00001) ? 'x' : '-';

    // adjust for SUID, SGID and sticky bit
    if ($mode & 0x800)
        $owner['execute'] = ($owner['execute'] == 'x') ? 's' : 'S';
    if ($mode & 0x400)
        $group['execute'] = ($group['execute'] == 'x') ? 's' : 'S';
    if ($mode & 0x200)
        $world['execute'] = ($world['execute'] == 'x') ? 't' : 'T';

    return $type .
            $owner['read'] . $owner['write'] . $owner['execute'] .
            $group['read'] . $group['write'] . $group['execute'] .
            $world['read'] . $world['write'] . $world['execute'];
}

function tep_remove($source) {
    global $messageStack, $tep_remove_error;

    if (isset($tep_remove_error))
        $tep_remove_error = false;

    if (is_dir($source)) {
        $dir = dir($source);
        while ($file = $dir->read()) {
            if (($file != '.') && ($file != '..')) {
                if (is_writeable($source . '/' . $file)) {
                    tep_remove($source . '/' . $file);
                } else {
                    $messageStack->add(sprintf(ERROR_FILE_NOT_REMOVEABLE, $source . '/' . $file), 'error');
                    $tep_remove_error = true;
                }
            }
        }
        $dir->close();

        if (is_writeable($source)) {
            rmdir($source);
        } else {
            $messageStack->add(sprintf(ERROR_DIRECTORY_NOT_REMOVEABLE, $source), 'error');
            $tep_remove_error = true;
        }
    } else {
        if (is_writeable($source)) {
            unlink($source);
        } else {
            $messageStack->add(sprintf(ERROR_FILE_NOT_REMOVEABLE, $source), 'error');
            $tep_remove_error = true;
        }
    }
}

////
// Output the tax percentage with optional padded decimals
function tep_display_tax_value($value, $padding = TAX_DECIMAL_PLACES) {
    if (strpos($value, '.')) {
        $loop = true;
        while ($loop) {
            if (substr($value, -1) == '0') {
                $value = substr($value, 0, -1);
            } else {
                $loop = false;
                if (substr($value, -1) == '.') {
                    $value = substr($value, 0, -1);
                }
            }
        }
    }

    if ($padding > 0) {
        if ($decimal_pos = strpos($value, '.')) {
            $decimals = strlen(substr($value, ($decimal_pos + 1)));
            for ($i = $decimals; $i < $padding; $i++) {
                $value .= '0';
            }
        } else {
            $value .= '.';
            for ($i = 0; $i < $padding; $i++) {
                $value .= '0';
            }
        }
    }

    return $value;
}

function tep_mail($to_name, $to_email_address, $email_subject, $email_text, $from_email_name, $from_email_address, $email_charset = '') {
    require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
    if (SEND_EMAILS != 'true')
        return false;

    $sent_status = false;

    $letter = array();
    $letter['message']['subject'] = $email_subject;
    $letter['message']['body'] = $email_text;
    $letter['envelope']['to'] = array('name' => $to_email_address, 'address' => $to_email_address);
    $letter['envelope']['from'] = array('name' => $from_email_name, 'address' => $from_email_address);

    $aws_obj = new ogm_amazon_ws();

    if ($aws_obj->send_mail_by_ses_controller($letter)) {
        $sent_status = $aws_obj->send_mail_by_ses($letter);
    }

    if (!$sent_status) {
        // Instantiate a new mail object
        $message = new email(array('X-Mailer: php',
            'Reply-To: ' . $from_email_address
        ));

        // Set charset
        if ($email_charset != '') {
            $message->set_charset($email_charset);
        }

        // Build the text version
        $text = strip_tags($email_text);
        $text .= '   ' . $email_charset;
        if (EMAIL_USE_HTML == 'true') {
            $message->add_html($email_text, $text);
        } else {
            $message->add_text($text);
        }

        // Send message
        $message->build_message();
        $message->send($to_name, $to_email_address, $from_email_name, $from_email_address, $email_subject);
    }

    unset($aws_obj, $letter);
}

function tep_get_tax_class_title($tax_class_id) {
    if ($tax_class_id == '0') {
        return TEXT_NONE;
    } else {
        $classes_query = tep_db_query("select tax_class_title from " . TABLE_TAX_CLASS . " where tax_class_id = '" . (int) $tax_class_id . "'");
        $classes = tep_db_fetch_array($classes_query);

        return $classes['tax_class_title'];
    }
}

function tep_banner_image_extension() {
    if (function_exists('imagetypes')) {
        if (imagetypes() & IMG_PNG) {
            return 'png';
        } elseif (imagetypes() & IMG_JPG) {
            return 'jpg';
        } elseif (imagetypes() & IMG_GIF) {
            return 'gif';
        }
    } elseif (function_exists('imagecreatefrompng') && function_exists('imagepng')) {
        return 'png';
    } elseif (function_exists('imagecreatefromjpeg') && function_exists('imagejpeg')) {
        return 'jpg';
    } elseif (function_exists('imagecreatefromgif') && function_exists('imagegif')) {
        return 'gif';
    }

    return false;
}

////
// Wrapper function for round() for php3 compatibility
function tep_round($value, $precision) {
    if (PHP_VERSION < 4) {
        $exp = pow(10, $precision);
        return round($value * $exp) / $exp;
    } else {
        return round($value, $precision);
    }
}

function tep_round_up_to($value, $unit) {
    $result = 0;
    if ($value > 0 && $unit > 0) {
        $integral_multiple = ceil($value / $unit);

        $result = $integral_multiple * $unit;
    }

    return $result;
}

////
// Add tax to a products price
function tep_add_tax($price, $tax) {
    global $currencies;

    if (DISPLAY_PRICE_WITH_TAX == 'true') {
        return tep_round($price, $currencies->currencies[DEFAULT_CURRENCY]['decimal_places']) + tep_calculate_tax($price, $tax);
    } else {
        return tep_round($price, $currencies->currencies[DEFAULT_CURRENCY]['decimal_places']);
    }
}

// Calculates Tax rounding the result
function tep_calculate_tax($price, $tax) {
    global $currencies;

    return tep_round($price * $tax / 100, $currencies->currencies[DEFAULT_CURRENCY]['decimal_places']);
}

////
// Returns the tax rate for a zone / class
// TABLES: tax_rates, zones_to_geo_zones
function tep_get_tax_rate($class_id, $country_id = -1, $zone_id = -1) {
    global $customer_zone_id, $customer_country_id;

    if (($country_id == -1) && ($zone_id == -1)) {
        if (!tep_session_is_registered('customer_id')) {
            $country_id = STORE_COUNTRY;
            $zone_id = STORE_ZONE;
        } else {
            $country_id = $customer_country_id;
            $zone_id = $customer_zone_id;
        }
    }

    $tax_query = tep_db_query("select SUM(tax_rate) as tax_rate from " . TABLE_TAX_RATES . " tr left join " . TABLE_ZONES_TO_GEO_ZONES . " za ON tr.tax_zone_id = za.geo_zone_id left join " . TABLE_GEO_ZONES . " tz ON tz.geo_zone_id = tr.tax_zone_id WHERE (za.zone_country_id IS NULL OR za.zone_country_id = '0' OR za.zone_country_id = '" . (int) $country_id . "') AND (za.zone_id IS NULL OR za.zone_id = '0' OR za.zone_id = '" . (int) $zone_id . "') AND tr.tax_class_id = '" . (int) $class_id . "' GROUP BY tr.tax_priority");
    if (tep_db_num_rows($tax_query)) {
        $tax_multiplier = 0;
        while ($tax = tep_db_fetch_array($tax_query)) {
            $tax_multiplier += $tax['tax_rate'];
        }
        return $tax_multiplier;
    } else {
        return 0;
    }
}

// Returns the tax rate for a tax class
// TABLES: tax_rates
function tep_get_tax_rate_value($class_id) {
    $tax_query = tep_db_query("select SUM(tax_rate) as tax_rate from " . TABLE_TAX_RATES . " where tax_class_id = '" . (int) $class_id . "' group by tax_priority");
    if (tep_db_num_rows($tax_query)) {
        $tax_multiplier = 0;
        while ($tax = tep_db_fetch_array($tax_query)) {
            $tax_multiplier += $tax['tax_rate'];
        }
        return $tax_multiplier;
    } else {
        return 0;
    }
}

function tep_call_function($function, $parameter, $object = '') {
    if ($object == '') {
        return call_user_func($function, $parameter);
    } elseif (PHP_VERSION < 4) {
        return call_user_method($function, $object, $parameter);
    } else {
        return call_user_func(array($object, $function), $parameter);
    }
}

function tep_get_zone_class_title($zone_class_id) {
    if ($zone_class_id == '0') {
        return TEXT_NONE;
    } else {
        $classes_query = tep_db_query("select geo_zone_name from " . TABLE_GEO_ZONES . " where geo_zone_id = '" . (int) $zone_class_id . "'");
        $classes = tep_db_fetch_array($classes_query);

        return $classes['geo_zone_name'];
    }
}

function tep_cfg_pull_down_zone_classes($zone_class_id, $key = '') {
    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    $zone_class_array = array(array('id' => '0', 'text' => TEXT_NONE));
    $zone_class_query = tep_db_query("select geo_zone_id, geo_zone_name from " . TABLE_GEO_ZONES . " order by geo_zone_name");
    while ($zone_class = tep_db_fetch_array($zone_class_query)) {
        $zone_class_array[] = array('id' => $zone_class['geo_zone_id'],
            'text' => $zone_class['geo_zone_name']);
    }

    return tep_draw_pull_down_menu($name, $zone_class_array, $zone_class_id);
}

function tep_cfg_pull_down_order_statuses($order_status_id, $key = '') {
    global $default_languages_id, $languages_id;

    $name = (($key) ? 'configuration[' . $key . ']' : 'configuration_value');

    $statuses_array = array(array('id' => '0', 'text' => TEXT_DEFAULT));
    $statuses_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . "
									where (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
										AND  orders_status_name <> ''
									order by orders_status_sort_order");
    while ($statuses = tep_db_fetch_array($statuses_query)) {
        $statuses_array[] = array('id' => $statuses['orders_status_id'],
            'text' => $statuses['orders_status_name']);
    }

    return tep_draw_pull_down_menu($name, $statuses_array, $order_status_id);
}

function tep_get_order_status_name($order_status_id, $language_id = '') {
    global $default_languages_id, $languages_id;

    if ($order_status_id < 1)
        return TEXT_DEFAULT;

    if (!is_numeric($language_id))
        $language_id = $languages_id;

    $status_query = tep_db_query("	select orders_status_name from " . TABLE_ORDERS_STATUS . "
									where orders_status_id = '" . (int) $order_status_id . "'
										and (IF(language_id='" . tep_db_input($language_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
										and orders_status_name <> ''
								");
    $status = tep_db_fetch_array($status_query);

    return $status['orders_status_name'];
}

function tep_get_buyback_order_status_name($order_status_id, $language_id = '') {
    global $default_languages_id, $languages_id;

    $status_query = tep_db_query("select buyback_status_name from " . TABLE_BUYBACK_STATUS . "
									where buyback_status_id = '" . (int) $order_status_id . "'
										and (IF(language_id='" . tep_db_input($languages_id) . "', 1, language_id='" . tep_db_input($default_languages_id) . "'))
										and buyback_status_name <> ''
								");
    $status = tep_db_fetch_array($status_query);

    return $status['buyback_status_name'];
}

////
// Return a random value
function tep_rand($min = null, $max = null, $length = 1) {
    $rand_result = '';

    static $seeded;

    if (!$seeded) {
        mt_srand((double) microtime() * 1000000);
        $seeded = true;
    }

    for ($len_cnt = 0; $len_cnt < $length; $len_cnt++) {
        if (isset($min) && isset($max)) {
            if ($min >= $max) {
                $rand_result .= $min;
            } else {
                $rand_result .= mt_rand($min, $max);
            }
        } else {
            $rand_result .= mt_rand();
        }
    }

    return $rand_result;
}

// nl2br() prior PHP 4.2.0 did not convert linefeeds on all OSs (it only converted \n)
function tep_convert_linefeeds($from, $to, $string) {
    if ((PHP_VERSION < "4.0.5") && is_array($from)) {
        return ereg_replace_dep('(' . implode('|', $from) . ')', $to, $string);
    } else {
        return str_replace($from, $to, $string);
    }
}

function tep_string_to_int($string) {
    return (int) $string;
}

////
// Parse and secure the cPath parameter values
function tep_parse_category_path($cPath) {
    // make sure the category IDs are integers
    $cPath_array = array_map('tep_string_to_int', explode('_', $cPath));

    // make sure no duplicate category IDs exist which could lock the server in a loop
    $tmp_array = array();
    $n = sizeof($cPath_array);
    for ($i = 0; $i < $n; $i++) {
        if (!in_array($cPath_array[$i], $tmp_array)) {
            $tmp_array[] = $cPath_array[$i];
        }
    }

    return $tmp_array;
}

function tep_gen_random_key($prefix = "", $searchTable = "", $searchField = "") {
    if (trim($searchTable) == "" || trim($searchField) == "") {
        $value = tep_rand(10000, 99999);
    } else {
        $again = true;
        while ($again) {
            $value = $prefix . tep_rand(1, 99999);
            $sql = "SELECT * FROM $searchTable WHERE $searchField='$value';";
            $result = tep_db_query($sql);

            if (tep_db_num_rows($result)) {
                $again = true;
            } else {
                $again = false;
            }
        }
    }
    return $value;
}

// Alias function for array of configuration values in the Administration Tool
function tep_cfg_select_multioption($select_array, $key_value, $key = '') {
    $name = (($key) ? 'configuration[' . $key . '][]' : 'configuration_value');
    for ($i = 0; $i < sizeof($select_array); $i++) {
        $string .= '<br><input type="checkbox" name="' . $name . '" value="' . $select_array[$i] . '"';
        $key_values = explode(", ", $key_value);
        if (in_array($select_array[$i], $key_values))
            $string .= 'CHECKED';
        $string .= '> ' . $select_array[$i];
    }
    return $string;
}

function tep_cfg_key_select_multioption($select_array, $key_value, $key = '') {
    $name = (($key) ? 'configuration[' . $key . '][]' : 'configuration_value');
    $key_values = tep_not_null($key_value) ? explode(", ", $key_value) : array();
    if (count($select_array)) {
        foreach ($select_array as $sel_key => $sel_val) {
            $string .= '<input type="checkbox" name="' . $name . '" value="' . $sel_key . '"';
            if (in_array($sel_key, $key_values))
                $string .= 'CHECKED';
            $string .= '> ' . $sel_val . '<br>';
        }
    }

    if (substr($string, -4) == '<br>')
        $string = substr($string, 0, -4);
    return $string;
}

//
function tep_insert_log_files($log_userid = '', $log_time = '', $log_ip = '', $log_details = '', $log_categories = '') {
    $sql_data_array = array('log_userid' => $log_userid,
        'log_ip' => $log_ip,
        'log_details' => $log_details,
        'log_categories' => $log_categories
    );
    $insert_sql_data = array('log_time' => 'now()');
    $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
    tep_db_perform(TABLE_LOG_FILES, $sql_data_array);
    //$log_id = tep_db_insert_id();
}

function tep_update_products_count($category_id, $include_inactive = false, $include_hidden = false) {
    global $cust_grp_id, $const_alluseraccess;
    $products_count = 0;

    $StatusWhereStr = ($include_inactive == true) ? " 1" : " p.products_status='1' ";
    $DisplayWhereStr = ($include_hidden == true) ? " 1" : " ( p.products_bundle='yes' OR p.products_bundle_dynamic='yes' OR (p.products_bundle<>'yes' AND p.products_bundle_dynamic<>'yes' AND p.products_display='1')) ";

    $items_count_select_sql = "	SELECT COUNT(p2c.products_id) AS total
    							FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
    							INNER JOIN " . TABLE_CATEGORIES . " AS c
    								ON p2c.categories_id=c.categories_id
								INNER JOIN " . TABLE_PRODUCTS . " AS p
									ON p2c.products_id=p.products_id
    							WHERE c.categories_id='" . (int) $category_id . "' AND c.categories_status=1
    								AND " . $StatusWhereStr . " AND " . $DisplayWhereStr;
    $items_count_result_sql = tep_db_query($items_count_select_sql);
    $items_count_row = tep_db_fetch_array($items_count_result_sql);
    $products_count += $items_count_row['total'];

    $sub_categories_select_sql = "	SELECT c.categories_id
									FROM " . TABLE_CATEGORIES . " AS c
									WHERE c.parent_id = '" . (int) $category_id . "' AND c.categories_status=1 ";
    $sub_categories_result_sql = tep_db_query($sub_categories_select_sql);
    if (tep_db_num_rows($sub_categories_result_sql)) {
        while ($sub_categories_row = tep_db_fetch_array($sub_categories_result_sql)) {
            $products_count += tep_update_products_count($sub_categories_row['categories_id'], $mode, $include_inactive, $include_hidden);
        }
    }

    if ($category_id > 0) {
        $products_count_update_sql = "UPDATE " . TABLE_CATEGORIES . " SET products_count = '" . (int) $products_count . "' WHERE categories_id = '" . $category_id . "'";
        tep_db_query($products_count_update_sql);
    }

    return $products_count;
}

// Update certain path from selected category upward
function tep_update_products_count_for_category($category_id, $include_inactive = false, $include_hidden = false) {
    $category_id = (int) $category_id;

    $old_products_count_select_sql = "SELECT products_count FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $category_id . "'";
    $old_products_count_result_sql = tep_db_query($old_products_count_select_sql);
    if ($old_products_count_row = tep_db_fetch_array($old_products_count_result_sql)) {
        $old_count = $old_products_count_row["products_count"];
    } else {
        $old_count = 0;
    }

    tep_update_products_count($category_id, $include_inactive, $include_hidden);

    $tree = tep_get_cat_tree($category_id, "root");
    if (count($tree)) {
        $cats_to_update = array();
        for ($i = 0; $i < count($tree); $i++) {
            $cats_to_update[] = $tree[$i]["id"];
        }

        $products_count_select_sql = "SELECT products_count FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $category_id . "'";
        $products_count_result_sql = tep_db_query($products_count_select_sql);
        if ($products_count_row = tep_db_fetch_array($products_count_result_sql)) {
            $changes_amount = $products_count_row["products_count"] - $old_count;

            $products_count_update_sql = "UPDATE " . TABLE_CATEGORIES . " SET products_count = products_count + " . (int) $changes_amount . " WHERE categories_id IN ('" . implode("', '", $cats_to_update) . "') AND categories_id <> " . $category_id;
            tep_db_query($products_count_update_sql);
        }
    }
}

// BOF Enable - Disable Categories Contribution--------------------------------------
// Sets the status of a category and all nested categories and products whithin.
function tep_set_categories_status($category_id, $status, $fieldname = 'categories_status') {

    $category_id = (int) $category_id;

    $fieldname = (($fieldname == '') ? 'categories_status' : $fieldname);

    $category_ids = array();

    if ($status == '1') {
        array_push($category_ids, $category_id);

        $cats_to_update = array();
        $tree = tep_get_cat_tree($category_id, "root");
        for ($i = 0; $i < count($tree); $i++) {
            if ($tree[$i]["id"] != $category_id) {
                array_push($category_ids, $tree[$i]["id"] );
            }
            $cats_to_update[] = $tree[$i]["id"];
        }

        $tree = tep_get_cat_tree($category_id, "base");

        for ($i = 0; $i < count($tree); $i++) {
            for ($j = 0; $j < count($tree[$i]); $j++) {
                if ($tree[$i][$j]["id"] != $category_id) {
                    array_push($category_ids, $tree[$i][$j]["id"] );
                }
            }
        }

    } elseif ($status == '0') {
        array_push($category_ids, $category_id);
        $tree = tep_get_cat_tree($category_id, "base");

        for ($i = 0; $i < count($tree); $i++) {
            for ($j = 0; $j < count($tree[$i]); $j++) {
                if ($tree[$i][$j]["id"] != $category_id) {
                    array_push($category_ids, $tree[$i][$j]["id"]);
                }
            }
        }
    }

    // g2g no need to send notification
    if($fieldname == 'c2c_categories_status') {
        tep_db_query("UPDATE " . TABLE_CATEGORIES . " SET " . $fieldname . " = " . $status .", last_modified = now() WHERE categories_id IN ('" . implode("', '", $category_ids) . "')");
    } else{
        $categories_select_sql = "	SELECT cd.categories_id, cd.categories_name, c.categories_status, c.c2c_categories_status, c.categories_parent_path
										FROM " . TABLE_CATEGORIES . " AS c
										INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
											ON (c.categories_id = cd.categories_id)
										WHERE cd.language_id = '1'
									AND c.categories_id IN ('" . implode("', '", $category_ids) . "')";

        $categories_result_sql = tep_db_query($categories_select_sql);

        $attachments = [];
        while ($category_row = tep_db_fetch_array($categories_result_sql)) {
            $slack_status = "";
            $current_status = (isset($category_row[$fieldname])) ? $category_row[$fieldname] : null;

            //if the status got update then only send out the slack notification
            if($current_status != $status) {
                $slack_status = ($status == "1") ? "Status Change : Inactive => Active" : "Status Change : Active => Inactive";
            }

            if($slack_status) {
                array_push($attachments, [
                    'color' => 'warning',
                    'text' => "Category : <" . HTTPS_SERVER . FILENAME_CATEGORIES . "?cPath=" . $category_row['categories_parent_path'] . "&cID=" . $_REQUEST['cID'] . "|" . $category_row["categories_name"] . " (" . $category_row["categories_id"] . ")" . ">\n" .
                        $slack_status . "\n" .
                        "Change by : " . $_SESSION['login_email_address']
                ]);
            }
        }

        tep_db_query("UPDATE " . TABLE_CATEGORIES . " SET " . $fieldname . " = " . $status .", last_modified = now() WHERE categories_id IN ('" . implode("', '", $category_ids) . "')");

        // if got any fields is updated then only send out the notifications
        if (!empty($attachments)) {
            require_once(DIR_WS_CLASSES . 'slack_notification.php');
            foreach (array_chunk($attachments, 100) as $attachments) {
                $slack = new slack_notification();
                $data = json_encode(array(
                    'text' => '[OG Crew] Category ' . LOG_STATUS_ADJUST . ' - ' . date("F j, Y H:i"),
                    'attachments' => $attachments
                ));
                $slack->send(SLACK_WEBHOOK_BDT, $data);
            }
        }
    }

    if ($status == '1') {
        tep_update_products_count($category_id, 'batch');

        if (count($cats_to_update)) {
            $products_count_select_sql = "SELECT products_count FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $category_id . "'";
            $products_count_result_sql = tep_db_query($products_count_select_sql);
            if ($products_count_row = tep_db_fetch_array($products_count_result_sql)) {
                $products_count_update_sql = "UPDATE " . TABLE_CATEGORIES . " SET products_count = products_count + " . (int) $products_count_row["products_count"] . " WHERE categories_id IN ('" . implode("', '", $cats_to_update) . "') AND categories_id <> " . $category_id;
                tep_db_query($products_count_update_sql);
            }
        }
    } elseif ($status == '0') {
        $tree = tep_get_cat_tree($category_id, "root");
        if (count($tree)) {
            $cats_to_update = array();
            for ($i = 0; $i < count($tree); $i++) {
                $cats_to_update[] = $tree[$i]["id"];
            }

            $products_count_select_sql = "SELECT products_count FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $category_id . "'";
            $products_count_result_sql = tep_db_query($products_count_select_sql);
            if ($products_count_row = tep_db_fetch_array($products_count_result_sql)) {
                $products_count_update_sql = "UPDATE " . TABLE_CATEGORIES . " SET products_count = products_count - " . (int) $products_count_row["products_count"] . " WHERE categories_id IN ('" . implode("', '", $cats_to_update) . "') AND categories_id <> " . $category_id;
                tep_db_query($products_count_update_sql);
            }
        }
    }
}

function tep_set_categories_type($category_id) {
    $parentcat_array = array();
    $subcat_array = array();

    tep_get_parent_categories($parentcat_array, $category_id);
    tep_get_subcategories($subcat_array, $category_id);

    $cat_array = array_merge($parentcat_array, $subcat_array);

    return $cat_array;
}

// EOF Enable - Disable Categories Contribution--------------------------------------
// Customers Status
function tep_set_customers_status($customers_id, $customers_status, $ignore_status_array = '') {
    $extra_where = " 1 ";
    if (isset($ignore_status_array) && is_array($ignore_status_array) && count($ignore_status_array)) {
        $extra_where = " customers_status NOT IN ('" . implode("', '", $ignore_status_array) . "')";
    }
    if ($customers_status == '1') {
        return tep_db_query("update " . TABLE_CUSTOMERS . " set customers_status = '1' WHERE customers_id = '" . $customers_id . "' and " . $extra_where);
    } elseif ($customers_status == '0') {
        return tep_db_query("update " . TABLE_CUSTOMERS . " set customers_status = '0' WHERE customers_id = '" . $customers_id . "' and " . $extra_where);
    } else {
        return -1;
    }
}

function tep_get_category_tree_cacheable($parent_id = '0', $spacing = '', $exclude = '', $category_tree_array = '', $include_itself = false, $level = 0, $append_title = false, $custom_product = '') {
    global $default_languages_id, $languages_id, $memcache_obj;
    $array_original_size = 0;

    if (!is_array($category_tree_array))
        $category_tree_array = array();
    $md5_value = md5($spacing . '|' . $exclude . '|' . $category_tree_array . '|' . $include_itself . '|' . $level . '|' . $append_title . '|' . $custom_product);

    $cache_key = TABLE_CATEGORIES . '/category_tree/array/categories_id/' . $parent_id . '/language/' . $languages_id . '/fn_param_md5/' . $md5_value;

    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $category_tree_array = $cache_result;
    } else {
        if ($append_title) {
            $category_tree_array[] = array('id' => '', "text" => "--------------- Select Categories ---------------");
            $array_original_size++;
        }
        if ((sizeof($category_tree_array) < $array_original_size + 1) && ($exclude != '0'))
            $category_tree_array[] = array('id' => '0', 'text' => TEXT_TOP);

        if ($include_itself) {
            $category_query = tep_db_query("	SELECT cd.categories_name
												FROM " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
												WHERE (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
													AND cd.categories_name <> ''
													AND cd.categories_id = '" . (int) $parent_id . "'
											");
            $category = tep_db_fetch_array($category_query);
            $category_tree_array[] = array('id' => $parent_id, 'text' => str_repeat($spacing, $level) . strip_tags($category['categories_name']));
            $skip = array($parent_id);
        } else {
            $skip = array();
        }

        tep_get_sub_cat_tree_cacheable($parent_id, $category_tree_array, $level, $spacing, $skip, false, $custom_product);

        $memcache_obj->store($cache_key, $category_tree_array, 7200);
    }

    return $category_tree_array;
}

function tep_get_sub_cat_tree_cacheable($category_id, &$cat_id_array, $level, $spacing = '', $skip = '', $by_level = true, $custom_product = '') {
    global $default_languages_id, $languages_id;

    if ($by_level) {
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
										ON c.categories_id=cd.categories_id
									WHERE c.parent_id = '" . $category_id . "'
										AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
										AND cd.categories_name <> ''
								";
        $sub_category_query = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_query)) {
            tep_get_sub_cat_tree_cacheable($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing);
            $cat_id_array[$level][] = array("id" => $sub_category["categories_id"], 'text' => strip_tags($sub_category["categories_name"]));
        }
    } else {
        if (is_array($skip) && !in_array($category_id, $skip)) {
            $custom_product_sql = '';
            if (tep_not_null($custom_product) && $category_id != 0) {
                $custom_product_sql = " , (	SELECT count( c1.categories_id )
										FROM " . TABLE_CATEGORIES . " AS c1
										WHERE c1.categories_parent_path LIKE '%\_" . $category_id . "\_%'
											AND c1.custom_products_type_id = '" . (int) $custom_product . "'
									) AS total_products ";
            }

            $cat_select_sql = "	SELECT c.custom_products_type_id, c.categories_id, c.parent_id, cd.categories_name " . $custom_product_sql . "
								FROM " . TABLE_CATEGORIES . " AS c
								LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
									ON c.categories_id=cd.categories_id
								WHERE c.categories_id = '" . $category_id . "'
									AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
									AND cd.categories_name <> ''";
            $cat_result_sql = tep_db_query($cat_select_sql);
            if ($cat_row = tep_db_fetch_array($cat_result_sql)) {
                $verified = false;

                if (tep_not_null($custom_product)) {
                    if ($cat_row['parent_id'] == '0') {
                        $verified = true;
                    } else {
                        $verified = ($cat_row['custom_products_type_id'] == '999' || $cat_row['custom_products_type_id'] == $custom_product) && (!tep_not_null($cat_row['total_products']) || (tep_not_null($cat_row['total_products']) && (($cat_row['total_products'] > 0) || ($cat_row['custom_products_type_id'] == $custom_product)))) ? true : false;
                    }

                    if ($verified) {
                        $cat_id_array[] = array("id" => $category_id, 'text' => ($level > 1 ? str_repeat($spacing, $level - 1) . strip_tags($cat_row["categories_name"]) : strip_tags($cat_row["categories_name"])));
                    } else {
                        return false;
                    }
                } else {
                    $cat_id_array[] = array("id" => $category_id, 'text' => ($level > 1 ? str_repeat($spacing, $level - 1) . strip_tags($cat_row["categories_name"]) : strip_tags($cat_row["categories_name"])));
                }
            }
        }

        $custom_product_sql = '';
        if (($category_id == 0 && tep_not_null($custom_product)) || (!tep_not_null($skip) || !in_array($category_id, $skip))) {
            $custom_product_sql = " , (	SELECT count( c1.categories_id )
										FROM " . TABLE_CATEGORIES . " AS c1
										WHERE c1.categories_parent_path LIKE CONCAT('%\_', c.categories_id, '\_%')
											AND c1.custom_products_type_id = '" . (int) $custom_product . "'
									) AS total_products ";
        } else if (tep_not_null($skip) && in_array($category_id, $skip)) {
            return true;
        }

        $sub_category_select_sql = "SELECT c.categories_id, c.custom_products_type_id, cd.categories_name " . $custom_product_sql . "
									FROM " . TABLE_CATEGORIES . " AS c
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
										ON c.categories_id = cd.categories_id
									WHERE c.parent_id = '" . $category_id . "'
										AND cd.categories_name <> ''
										AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
									ORDER BY c.sort_order, cd.categories_name";
        $sub_category_result_sql = tep_db_query($sub_category_select_sql);
        while ($sub_category = tep_db_fetch_array($sub_category_result_sql)) {
            if (tep_not_null($custom_product) && tep_not_null($sub_category['total_products']) && (0 == $sub_category['total_products'])) {
                if ($sub_category['custom_products_type_id'] != $custom_product) {
                    $skip[] = $sub_category['categories_id'];
                }
            }

            tep_get_sub_cat_tree_cacheable($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing, $skip, false, $custom_product);
        }
    }
    return true;
}

function tep_get_products_list_by_category($cat_id = '0') {
    global $languages_id, $memcache_obj;

    $product_list_array = array();

    $cache_key = TABLE_PRODUCTS . '/product_by_cat/array/categories_id/' . $cat_id . '/language/' . $languages_id;

    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        $product_list_array = $cache_result;
    } else {
        $product_select_sql = "	SELECT p.products_id, pd.products_name
								FROM " . TABLE_PRODUCTS . " AS p, " . TABLE_PRODUCTS_DESCRIPTION . " AS pd, " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
								WHERE pc.categories_id='" . tep_db_input($cat_id) . "'
									AND pc.products_id=p.products_id
									AND p.products_id=pd.products_id
									AND pd.language_id ='" . (int) $languages_id . "'
								ORDER BY p.products_sort_order, pd.products_name";
        $product_result_sql = tep_db_query($product_select_sql);

        while ($product_row = tep_db_fetch_array($product_result_sql)) {
            $product_list_array[] = array('id' => $product_row['products_id'],
                'name' => $product_row['products_name']);
        }

        $memcache_obj->store($cache_key, $product_list_array, 7200);
    }

    return $product_list_array;
}

//Wei Chen 24/02/2005 :: Category tree array
function tep_get_cat_tree($category_id, $direction = "") {
    global $default_languages_id, $languages_id;

    $cat_id_array = array();

    if ($direction == "root") {
        $cat_id_array = array(array("id" => $category_id));
        $parent_category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . (int) $category_id . "' AND parent_id<>0");
        while ($parent_category = tep_db_fetch_array($parent_category_query)) {
            $cat_id_array[]["id"] = $parent_category["parent_id"];
            $parent_category_query = tep_db_query("SELECT parent_id FROM " . TABLE_CATEGORIES . " WHERE categories_id = '" . $parent_category["parent_id"] . "' AND parent_id<>0");
        }
        $cat_id_array = array_reverse($cat_id_array);
    } else if ($direction == "base") {
        $category_name_query = tep_db_query("SELECT cd.categories_name FROM " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
											WHERE (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
												AND cd.categories_name <> ''
												AND cd.categories_id = '" . $category_id . "'
											");
        $category_name = tep_db_fetch_array($category_name_query);

        $cat_id_array["0"] = array(array("id" => $category_id, "text" => strip_tags($category_name["categories_name"])));
        tep_get_sub_cat_tree($category_id, $cat_id_array, 1, '___');
    }
    return $cat_id_array;
}

function tep_get_sub_cat_tree($category_id, &$cat_id_array, $level, $spacing = '', $skip = '', $by_level = true, $custom_product = '') {
    global $default_languages_id, $languages_id;

    if ($by_level) {
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
										ON c.categories_id=cd.categories_id
									WHERE c.parent_id = '" . $category_id . "'
										AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
										AND cd.categories_name <> ''
								";
        $sub_category_query = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_query)) {
            tep_get_sub_cat_tree($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing);
            $cat_id_array[$level][] = array("id" => $sub_category["categories_id"], 'text' => strip_tags($sub_category["categories_name"]));
        }
    } else {
        if (is_array($skip) && !in_array($category_id, $skip)) {
            $cat_select_sql = "	SELECT c.custom_products_type_id, c.categories_id, c.parent_id, cd.categories_name
								FROM " . TABLE_CATEGORIES . " AS c
								LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
									ON c.categories_id=cd.categories_id
								WHERE c.categories_id = '" . $category_id . "'
									AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
									AND cd.categories_name <> ''
								";
            $cat_result_sql = tep_db_query($cat_select_sql);
            if ($cat_row = tep_db_fetch_array($cat_result_sql)) {
                $verified = false;

                if (tep_not_null($custom_product)) {
                    if ($cat_row['parent_id'] == '0') {
                        $verified = true;
                    } else {
                        $verified = $cat_row['custom_products_type_id'] == '999' || $cat_row['custom_products_type_id'] == $custom_product ? true : false;
                    }

                    if ($verified) {
                        $cat_id_array[] = array("id" => $category_id, 'text' => ($level > 1 ? str_repeat($spacing, $level - 1) . strip_tags($cat_row["categories_name"]) : strip_tags($cat_row["categories_name"])));
                    } else {
                        return false;
                    }
                } else {
                    $cat_id_array[] = array("id" => $category_id, 'text' => ($level > 1 ? str_repeat($spacing, $level - 1) . strip_tags($cat_row["categories_name"]) : strip_tags($cat_row["categories_name"])));
                }
            }
        }
        $sub_category_select_sql = "SELECT c.categories_id, c.custom_products_type_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
										ON c.categories_id = cd.categories_id
									WHERE c.parent_id = '" . $category_id . "'
										AND cd.categories_name <> ''
										AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
									ORDER BY c.sort_order, cd.categories_name";
        $sub_category_result_sql = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_result_sql)) {
            tep_get_sub_cat_tree($sub_category["categories_id"], $cat_id_array, $level + 1, $spacing, $skip, false, $custom_product);
        }
    }
    return true;
}

function tep_get_eligible_sub_cat_tree($filename, $category_id, &$cat_id_array, $level, $spacing = '', $skip = '', $by_level = true) {
    global $default_languages_id, $languages_id;

    if ($by_level) {
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
										ON c.categories_id=cd.categories_id
									WHERE c.parent_id = '" . $category_id . "'
										AND cd.categories_name <> ''
										AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
								";
        $sub_category_query = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_query)) {
            if (($p_rank = tep_check_cat_tree_permissions($filename, $sub_category["categories_id"])) > 0) {
                tep_get_eligible_sub_cat_tree($filename, $sub_category["categories_id"], $cat_id_array, $level + 1, $spacing);
                $cat_id_array[$level][] = array('id' => $sub_category["categories_id"],
                    'text' => strip_tags($sub_category["categories_name"]),
                    'param' => $p_rank == 2 ? '' : '');
            }
        }
    } else {
        if (is_array($skip) && !in_array($category_id, $skip)) {
            $cat_select_sql = "	SELECT c.categories_id, cd.categories_name
								FROM " . TABLE_CATEGORIES . " AS c
								LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
									ON c.categories_id=cd.categories_id
								WHERE c.categories_id = '" . $category_id . "'
									AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
									AND cd.categories_name <> ''
							";
            $cat_result_sql = tep_db_query($cat_select_sql);
            if ($cat_row = tep_db_fetch_array($cat_result_sql)) {
                if (($p_rank = tep_check_cat_tree_permissions($filename, $category_id)) > 0) {
                    $cat_id_array[] = array('id' => $category_id,
                        'text' => ($level > 1 ? str_repeat($spacing, $level - 1) . strip_tags($cat_row["categories_name"]) : strip_tags($cat_row["categories_name"])),
                        'param' => $p_rank == 2 ? '' : '');
                }
            }
        }
        $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
										ON c.categories_id = cd.categories_id
									WHERE c.parent_id = '" . $category_id . "'
										AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
										AND cd.categories_name <> ''
									ORDER BY cd.categories_name, c.sort_order";
        $sub_category_result_sql = tep_db_query($sub_category_select_sql);

        while ($sub_category = tep_db_fetch_array($sub_category_result_sql)) {
            if (tep_check_cat_tree_permissions($filename, $sub_category["categories_id"])) {
                tep_get_eligible_sub_cat_tree($filename, $sub_category["categories_id"], $cat_id_array, $level + 1, $spacing, $skip, false);
            }
        }
    }
    return true;
}

function tep_get_eligible_sub_cat_tree2($filename, $category_id, &$cat_id_array, $level, $selected_array = array()) {
    global $default_languages_id, $languages_id;

    $last_cat_id = '';

    $sub_category_select_sql = "SELECT c.categories_id, cd.categories_name, c.categories_parent_path
								FROM " . TABLE_CATEGORIES . " AS c
								LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
									ON c.categories_id = cd.categories_id
								WHERE c.parent_id = '" . $category_id . "'
									AND (IF(cd.language_id='" . tep_db_input($languages_id) . "', 1, cd.language_id='" . tep_db_input($default_languages_id) . "'))
									AND cd.categories_name <> ''
								ORDER BY c.sort_order, cd.categories_name";
    $sub_category_result_sql = tep_db_query($sub_category_select_sql);
    if ($sub_category = tep_db_fetch_array($sub_category_result_sql)) {
        do {
            if (($p_rank = tep_check_cat_tree_permissions($filename, $sub_category["categories_id"])) > 0) {
                if (isset($selected_array[-999])) {
                    $category_selected = 1;
                    $auto_selected = 1;
                } else if (isset($selected_array[$sub_category['categories_id']])) {
                    $category_selected = 1;
                    $auto_selected = 1;

                    unset($selected_array[$sub_category['categories_id']]);
                } else if ($category_id == 0) {
                    $category_selected = 0;
                    $auto_selected = 1;
                } else {
                    $category_selected = 0;
                    $auto_selected = 0;
                    foreach (explode("_", substr($sub_category['categories_parent_path'], 1, -1)) as $parent_cat_id) {
                        $cat_id_array[$parent_cat_id]['auto_selected'] = 0;
                    }
                }

                $cat_id_array[$sub_category['categories_id']] = array('text' => $sub_category['categories_name'],
                    'level' => $level,
                    'level_type' => 'parent',
                    'selected' => $category_selected,
                    'auto_selected' => $auto_selected,
                    'last_child' => ''
                );

                tep_get_eligible_sub_cat_tree2($filename, $sub_category["categories_id"], $cat_id_array, $level + 1, $selected_array);
                $last_cat_id = $sub_category["categories_id"];
            }
        } while ($sub_category = tep_db_fetch_array($sub_category_result_sql));
        $cat_id_array[$last_cat_id]['last_child'] = ' class="last"';
    } else {
        $cat_id_array[$category_id]['level_type'] = 'child';
    }
}

// Created by wei chen for pre-order purpose
function tep_get_eta_string($eta) {
    $hours = (int) $eta;
    $minutes = ($eta * 60) - ($hours * 60);

    $eta_str = $hours . " hr" . ($hours > 1 ? "s " : " ") . ($minutes > 0 ? $minutes . " min" . ($minutes > 1 ? "s" : "") : "");

    return trim($eta_str);
}

function tep_get_purchase_eta_string($eta, $type = '1') {
    $day = (int) ($eta / 24);
    $hours = abs($eta) - (abs($day) * 24); // We need decimal part
    switch ($type) {
        case 1:
            return ($eta < 0 ? '-' : '') . (abs($day) >= 1 ? abs($day) . " Day" . (abs($day) > 1 ? 's' : '') : '') . ( $hours > 0 || (abs($day) < 1 && $hours >= 0) ? ' ' . $hours . " Hour" . ($hours > 1 ? 's' : '') : '');
            break;
        case 2:
            return ($eta < 0 ? '-' : '') . (abs($day) >= 1 ? abs($day) . " D" : '') . ( $hours > 0 || (abs($day) < 1 && $hours >= 0) ? ' ' . round($hours, 2) . " H" : '');
            break;
    }
}

function tep_get_order_comment($id) {
    $id = (int) $id;

    if ($id == 0) {
        return "";
    } else {
        $result = tep_db_query("select orders_comments_text from orders_comments where orders_comments_id='$id';");

        if ($row = tep_db_fetch_array($result)) {
            return $row['orders_comments_text'];
        } else {
            return "";
        }
    }
}

function tep_update_record_tags($filename, $rec_id, $status_id, $extra_tag) {
    if ($filename == FILENAME_STATS_ORDERS_TRACKING) {
        $orders_tag_select_sql = "SELECT orders_tag_ids FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($rec_id) . "'";
        $orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
        $orders_tag_row = tep_db_fetch_array($orders_tag_result_sql);
        if (tep_not_null($orders_tag_row["orders_tag_ids"])) {
            $new_order_tags_array = array();
            $applicable_orders_tag_select_sql = "	SELECT orders_tag_id
													FROM " . TABLE_ORDERS_TAG . "
													WHERE FIND_IN_SET(orders_tag_id, '" . $orders_tag_row["orders_tag_ids"] . "')
														AND FIND_IN_SET('" . (int) $status_id . "', orders_tag_status_ids)
														AND filename='" . tep_db_input(FILENAME_STATS_ORDERS_TRACKING) . "'";
            $applicable_orders_tag_result_sql = tep_db_query($applicable_orders_tag_select_sql);
            while ($applicable_orders_tag_row = tep_db_fetch_array($applicable_orders_tag_result_sql)) {
                $new_order_tags_array[] = $applicable_orders_tag_row["orders_tag_id"];
            }
            if (tep_not_null($extra_tag) && !in_array($extra_tag, $new_order_tags_array))
                $new_order_tags_array[] = $extra_tag;

            $new_tag_string = count($new_order_tags_array) > 0 ? implode(',', $new_order_tags_array) : '';
            tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_id='" . tep_db_input($rec_id) . "'");
        } else {
            if (tep_not_null($extra_tag)) {
                tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_tag_ids='" . $extra_tag . "' WHERE orders_id='" . tep_db_input($rec_id) . "'");
            }
        }
    } else if ($filename == FILENAME_PROGRESS_REPORT) {
        $orders_tag_select_sql = "SELECT orders_tag_ids FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id = '" . tep_db_input($rec_id) . "'";
        $orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
        $orders_tag_row = tep_db_fetch_array($orders_tag_result_sql);

        if (tep_not_null($orders_tag_row["orders_tag_ids"])) {
            $new_order_tags_array = array();
            $applicable_orders_tag_select_sql = "	SELECT orders_tag_id
													FROM " . TABLE_ORDERS_TAG . "
													WHERE FIND_IN_SET(orders_tag_id, '" . $orders_tag_row["orders_tag_ids"] . "')
														AND FIND_IN_SET('" . (int) $status_id . "', orders_tag_status_ids)
														AND filename='" . tep_db_input(FILENAME_PROGRESS_REPORT) . "'";
            $applicable_orders_tag_result_sql = tep_db_query($applicable_orders_tag_select_sql);
            while ($applicable_orders_tag_row = tep_db_fetch_array($applicable_orders_tag_result_sql)) {
                $new_order_tags_array[] = $applicable_orders_tag_row["orders_tag_id"];
            }
            if (tep_not_null($extra_tag) && !in_array($extra_tag, $new_order_tags_array))
                $new_order_tags_array[] = $extra_tag;

            $new_tag_string = count($new_order_tags_array) > 0 ? implode(',', $new_order_tags_array) : '';
            tep_db_query("UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_products_id='" . tep_db_input($rec_id) . "'");
        } else {
            if (tep_not_null($extra_tag)) {
                tep_db_query("UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET orders_tag_ids='" . $extra_tag . "' WHERE orders_products_id='" . tep_db_input($rec_id) . "'");
            }
        }
    } else if ($filename == FILENAME_BUYBACK_REQUESTS) {
        $orders_tag_select_sql = "SELECT orders_tag_ids FROM " . TABLE_BUYBACK_REQUEST_GROUP . " WHERE buyback_request_group_id = '" . tep_db_input($rec_id) . "'";
        $orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
        $orders_tag_row = tep_db_fetch_array($orders_tag_result_sql);

        if (tep_not_null($orders_tag_row["orders_tag_ids"])) {
            $new_order_tags_array = array();
            $applicable_orders_tag_select_sql = "	SELECT orders_tag_id
													FROM " . TABLE_ORDERS_TAG . "
													WHERE FIND_IN_SET(orders_tag_id, '" . $orders_tag_row["orders_tag_ids"] . "')
														AND FIND_IN_SET('" . (int) $status_id . "', orders_tag_status_ids)
														AND filename='" . tep_db_input(FILENAME_BUYBACK_REQUESTS) . "'";
            $applicable_orders_tag_result_sql = tep_db_query($applicable_orders_tag_select_sql);
            while ($applicable_orders_tag_row = tep_db_fetch_array($applicable_orders_tag_result_sql)) {
                $new_order_tags_array[] = $applicable_orders_tag_row["orders_tag_id"];
            }
            if (tep_not_null($extra_tag) && !in_array($extra_tag, $new_order_tags_array))
                $new_order_tags_array[] = $extra_tag;

            $new_tag_string = count($new_order_tags_array) > 0 ? implode(',', $new_order_tags_array) : '';
            tep_db_query("UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids='" . $new_tag_string . "' WHERE buyback_request_group_id='" . tep_db_input($rec_id) . "'");
        } else {
            if (tep_not_null($extra_tag)) {
                tep_db_query("UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . " SET orders_tag_ids='" . $extra_tag . "' WHERE buyback_request_group_id='" . tep_db_input($rec_id) . "'");
            }
        }
    } else if ($filename == FILENAME_EDIT_PURCHASE_ORDERS) {
        $purchase_orders_tag_select_sql = "SELECT purchase_orders_tag_ids FROM " . TABLE_PURCHASE_ORDERS . " WHERE purchase_orders_id = '" . tep_db_input($rec_id) . "'";
        $purchase_orders_tag_result_sql = tep_db_query($purchase_orders_tag_select_sql);
        $purchase_orders_tag_row = tep_db_fetch_array($purchase_orders_tag_result_sql);
        if (tep_not_null($purchase_orders_tag_row["purchase_orders_tag_ids"])) {
            $new_order_tags_array = array();
            $applicable_orders_tag_select_sql = "	SELECT orders_tag_id
													FROM " . TABLE_ORDERS_TAG . "
													WHERE FIND_IN_SET(orders_tag_id, '" . $purchase_orders_tag_row["orders_tag_ids"] . "')
														AND FIND_IN_SET('" . (int) $status_id . "', orders_tag_status_ids)
														AND filename='" . tep_db_input(FILENAME_EDIT_PURCHASE_ORDERS) . "'";
            $applicable_orders_tag_result_sql = tep_db_query($applicable_orders_tag_select_sql);
            while ($applicable_orders_tag_row = tep_db_fetch_array($applicable_orders_tag_result_sql)) {
                $new_order_tags_array[] = $applicable_orders_tag_row["orders_tag_id"];
            }
            if (tep_not_null($extra_tag) && !in_array($extra_tag, $new_order_tags_array))
                $new_order_tags_array[] = $extra_tag;

            $new_tag_string = count($new_order_tags_array) > 0 ? implode(',', $new_order_tags_array) : '';
            tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids='" . $new_tag_string . "' WHERE purchase_orders_id='" . tep_db_input($rec_id) . "'");
        } else {
            if (tep_not_null($extra_tag)) {
                tep_db_query("UPDATE " . TABLE_PURCHASE_ORDERS . " SET purchase_orders_tag_ids='" . $extra_tag . "' WHERE purchase_orders_id='" . tep_db_input($rec_id) . "'");
            }
        }
    }
}

// by subrat, datapool tree
function tep_get_datapool_tree_array($tmp_id = 0, $reverse = true) {
    $retArray = $parentArray = array();

    if ($tmp_id <= 0) {
        $parent_level_tags_select_sql = "SELECT * from " . TABLE_DATA_POOL_LEVEL_TAGS . " where data_pool_level_parent_id = '0' order by data_pool_sort_order,data_pool_level_name;";
    } else {
        $parent_level_tags_select_sql = "	SELECT * FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " AS a, " . TABLE_DATA_POOL_TEMPLATE . " AS b
											WHERE a.data_pool_level_parent_id = '0'
												AND a.data_pool_level_tags_id = b.data_pool_level_tags_id
												AND b.data_pool_template_id = '" . $tmp_id . "'
											ORDER BY a.data_pool_sort_order, a.data_pool_level_name;";
    }

    $parent_level_tags_result_sql = tep_db_query($parent_level_tags_select_sql);

    $retArray = $parentArray = array();

    while ($parent_level_tags_row = tep_db_fetch_array($parent_level_tags_result_sql)) {
        $path = $parent_level_tags_row['data_pool_level_name'];

        $parentArray[] = array('id' => (int) $parent_level_tags_row['data_pool_level_tags_id'],
            'parent_id' => (int) $parent_level_tags_row['data_pool_level_parent_id'],
            'ref_id' => (int) $parent_level_tags_row['data_pool_ref_id'],
            'name' => $parent_level_tags_row['data_pool_level_name'],
            'ident' => 0,
            'path' => $path,
            'child' => array());

        tep_get_datapool_subtree_array((int) $parent_level_tags_row['data_pool_level_tags_id'], $retArray, 0, $path, $reverse);
        $parentArray[sizeof($parentArray) - 1]['child'] = $retArray;
        $retArray = array();
    }

    if ($reverse)
        return array_reverse($parentArray);
    else
        return $parentArray;
}

function tep_get_datapool_subtree_array($id, &$retArray, $identLength = 0, $parentName = "", $reverse = true) {
    $identLength = (int) $identLength + 20;
    $level_tags_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " WHERE data_pool_level_parent_id ='" . $id . "' ORDER BY data_pool_sort_order, data_pool_level_name;";
    $level_tags_result_sql = tep_db_query($level_tags_select_sql);
    $parentArray = array();

    while ($level_tags_row = tep_db_fetch_array($level_tags_result_sql)) {
        $path = $parentName . " > " . $level_tags_row['data_pool_level_name'];

        $parentArray[] = array('id' => (int) $level_tags_row['data_pool_level_tags_id'],
            'parent_id' => (int) $level_tags_row['data_pool_level_parent_id'],
            'ref_id' => (int) $level_tags_row['data_pool_ref_id'],
            'name' => $level_tags_row['data_pool_level_name'],
            'ident' => $identLength,
            'path' => $path,
            'child' => array());

        tep_get_datapool_subtree_array((int) $level_tags_row['data_pool_level_tags_id'], $retArray, $identLength, $path);
        $parentArray[sizeof($parentArray) - 1]['child'] = $retArray;

        $retArray = array();
    }

    if ($reverse) {
        $retArray = array_reverse($parentArray);
    } else {
        $retArray = $parentArray;
    }
}

function tep_get_catalog_datapool_subtree_array($id, &$retArray, $identLength = 0, $parentName = '') {
    $identLength = (int) $identLength + 20;
    $level_select_sql = "SELECT * from " . TABLE_DATA_POOL_LEVEL . " where data_pool_level_parent_id ='" . $id . "' order by data_pool_sort_order, data_pool_level_name;";
    $level_result_sql = tep_db_query($level_select_sql);
    $parentArray = array();

    while ($level_row = tep_db_fetch_array($level_result_sql)) {
        $path = $parentName . (tep_not_null($parentName) && tep_not_null($level_row['data_pool_level_name']) ? ' > ' : '') . $level_row['data_pool_level_name'];

        $parentArray[] = array('id' => (int) $level_row['data_pool_level_id'],
            'parent_id' => (int) $level_row['data_pool_level_parent_id'],
            'ref_id' => (int) $level_row['data_pool_ref_id'],
            'name' => $level_row['data_pool_level_name'],
            'ident' => $identLength,
            'path' => $path,
            'data_pool_max_level' => $level_row['data_pool_max_level'],
            'data_pool_min_level' => $level_row['data_pool_min_level'],
            'data_pool_level_value' => $level_row['data_pool_level_value'],
            'data_pool_input_field' => $level_row['data_pool_input_field'],
            'data_pool_level_class' => $level_row['data_pool_level_class'],
            'data_pool_level_class_name' => $level_row['data_pool_level_class_name'],
            'child' => array());

        tep_get_catalog_datapool_subtree_array((int) $level_row['data_pool_level_id'], $retArray, $identLength, $path);
        $parentArray[sizeof($parentArray) - 1]['child'] = $retArray;

        $retArray = array();
    }

    $retArray = $parentArray;
}

function tep_data_pool_delete($id) {
    $sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " WHERE data_pool_level_parent_id ='" . $id . "';";
    $result = tep_db_query($sql);

    //	Delete all brackets also
    tep_db_query("DELETE FROM " . TABLE_BRACKETS_TAGS . " WHERE data_pool_level_tags_id ='" . $id . "';");

    // 	Delete from relationship table
    tep_db_query("DELETE FROM " . TABLE_BRACKETS_GROUPS_TO_LEVEL_TAGS . " WHERE data_pool_level_tags_id ='" . $id . "';");

    while ($row = tep_db_fetch_array($result)) {
        tep_data_pool_delete($row['data_pool_level_tags_id']);

        // 	Check if i was the last datapool ref to be deleted, if i was, delete my datapool also :)
        $sql_ref_datapool = "SELECT data_pool_id FROM " . TABLE_DATA_POOL_REF . " WHERE data_pool_ref_id ='" . $row['data_pool_ref_id'] . "';";
        $result_ref_datapool = tep_db_query($sql_ref_datapool);
        if ($row_ref_datapool = tep_db_fetch_array($result_ref_datapool)) {
            $data_pool_id = (int) $row_ref_datapool['data_pool_id'];
        }

        tep_db_query("DELETE FROM " . TABLE_DATA_POOL_REF . " WHERE data_pool_ref_id ='" . $row['data_pool_ref_id'] . "';");

        // 	Check if still datapool id exists, if it does not exist, remove the main datapool, simple as that
        $sql_datapool = "SELECT data_pool_ref_id FROM " . TABLE_DATA_POOL_REF . " WHERE data_pool_id ='" . $data_pool_id . "';";
        $sql_datapool_result = tep_db_query($sql_datapool);

        if (tep_db_num_rows($sql_datapool_result) == 0) {
            tep_db_query("DELETE FROM " . TABLE_DATA_POOL . " WHERE data_pool_id = '" . $data_pool_id . "';");
        }
    }

    tep_db_query("DELETE FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " WHERE data_pool_level_tags_id ='" . $id . "';");
}

function tep_get_one_time_bracket_value($data_pool_id, $field = 'data_pool_level_tags_id', $tag = KEY_PL_START_LEVEL) {
    $data_pool_id = (int) $data_pool_id;

    if (!tep_not_null($field))
        $field = 'data_pool_level_tags_id';

    $bracket_value_select_sql = "	SELECT brackets_tags_value FROM " . TABLE_BRACKETS_TAGS . "
						    		WHERE $field ='" . $data_pool_id . "' AND brackets_tags_key ='" . $tag . "'";
    $bracket_value_result_sql = tep_db_query($bracket_value_select_sql);

    if ($bracket_value_row = tep_db_fetch_array($bracket_value_result_sql)) {
        return $bracket_value_row['brackets_tags_value'];
    } else {
        return false;
    }
}

function tep_get_input_field($ref_id) {
    $res = tep_db_query("select a.data_pool_input_field, a.data_pool_name, a.data_pool_class from " . TABLE_DATA_POOL . " as a, " . TABLE_DATA_POOL_REF . " as b where b.data_pool_ref_id='$ref_id' and b.data_pool_id = a.data_pool_id;");

    if ($row = tep_db_fetch_array($res)) {
        return $row;
    } else {
        return array('data_pool_input_field' => '', 'data_pool_name' => '', 'data_pool_class' => '');
    }
}

function tep_has_brackets($id) {
    $bracket_select_sql = "SELECT COUNT(*) AS counted FROM " . TABLE_BRACKETS_TAGS . " WHERE data_pool_level_tags_id='" . $id . "';";
    $bracket_result_sql = tep_db_query($bracket_select_sql);

    if ($bracket_row = tep_db_fetch_array($bracket_result_sql)) {
        if ((int) $bracket_row['counted'] > 0)
            return true;
        else
            return false;
    } else {
        return false;
    }
}

function tep_apply_template($template_id, $id, $parent, $prod_id) {
    $prod_id = (int) $prod_id;

    //	Start by clearing all the existing items from brackets and levels
    $data_pool_level_cat_select_sql = "SELECT data_pool_level_id FROM " . TABLE_DATA_POOL_LEVEL . " WHERE products_id ='" . $prod_id . "';";
    $data_pool_level_cat_result_sql = tep_db_query($data_pool_level_cat_select_sql);
    while ($data_pool_level_cat_row = tep_db_fetch_array($data_pool_level_cat_result_sql)) {
        //	Delete the bracket
        tep_db_query("DELETE FROM " . TABLE_BRACKETS . " WHERE data_pool_level_id ='" . $data_pool_level_cat_row['data_pool_level_id'] . "';");
    }

    //	Finally delete all the levels
    tep_db_query("DELETE FROM " . TABLE_DATA_POOL_LEVEL . " WHERE products_id ='" . $prod_id . "';");

    //	Delete all the options values associated to options for this product
    $options_select_sql = "SELECT data_pool_options_id FROM " . TABLE_DATA_POOL_OPTIONS . " WHERE products_id ='" . $prod_id . "'";
    $options_result_sql = tep_db_query($options_select_sql);
    while ($options_row = tep_db_fetch_array($options_result_sql)) {
        tep_db_query("DELETE FROM " . TABLE_DATA_POOL_OPTIONS_VALUES . " WHERE data_pool_options_id ='" . $options_row['data_pool_options_id'] . "'");
    }

    //	Remove all the options assigned for this product
    tep_db_query("DELETE FROM " . TABLE_DATA_POOL_OPTIONS . " WHERE products_id ='" . $prod_id . "'");

    $options_tags_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_OPTIONS_TAGS . " WHERE data_pool_template_id ='" . $template_id . "' ORDER BY data_pool_options_sort_order";
    $options_tags_result_sql = tep_db_query($options_tags_select_sql);
    while ($options_tags_row = tep_db_fetch_array($options_tags_result_sql)) {
        $option_sql_data_array = array('products_id' => $prod_id,
            'data_pool_options_title' => $options_tags_row["data_pool_options_title"],
            'data_pool_options_class' => $options_tags_row["data_pool_options_class"],
            'data_pool_options_input_type' => $options_tags_row["data_pool_options_input_type"],
            'data_pool_options_input_size' => $options_tags_row["data_pool_options_input_size"],
            'data_pool_options_required' => $options_tags_row["data_pool_options_required"],
            'data_pool_options_show_supplier' => $options_tags_row["data_pool_options_show_supplier"],
            'data_pool_options_sort_order' => $options_tags_row["data_pool_options_sort_order"]
        );

        tep_db_perform(TABLE_DATA_POOL_OPTIONS, $option_sql_data_array);
        $option_id = tep_db_insert_id();

        $option_values_tags_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_OPTIONS_VALUES_TAGS . " WHERE data_pool_options_id ='" . $options_tags_row["data_pool_options_id"] . "' ORDER BY data_pool_options_values_sort_order, data_pool_options_values_id";
        $option_values_tags_result_sql = tep_db_query($option_values_tags_select_sql);
        $i = 0;
        while ($option_values_tags_row = tep_db_fetch_array($option_values_tags_result_sql)) {
            $option_values_sql_data_array = array('data_pool_options_values_id' => ($i + 1),
                'data_pool_options_id' => $option_id,
                'data_pool_options_value' => $option_values_tags_row["data_pool_options_value"],
                'data_pool_options_values_price' => $option_values_tags_row["data_pool_options_values_price"],
                'data_pool_options_values_eta' => $option_values_tags_row["data_pool_options_values_eta"],
                'data_pool_options_values_min_level' => $option_values_tags_row["data_pool_options_values_min_level"],
                'data_pool_options_values_max_level' => $option_values_tags_row["data_pool_options_values_max_level"],
                'data_pool_options_values_sort_order' => $option_values_tags_row["data_pool_options_values_sort_order"]
            );
            tep_db_perform(TABLE_DATA_POOL_OPTIONS_VALUES, $option_values_sql_data_array);
            $i++;
        }
    }

    //	Now we need to make copy... simply call the function :)
    tep_data_duplicate_template2($id, $parent, $prod_id);
}

function tep_import_selections($id, $parent, $selection_group_id = null, $skip_id = 0) {
    if ($skip_id == $id)
        return;

    $sql = "SELECT *
			FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " AS a, " . TABLE_DATA_POOL_REF . " AS b, " . TABLE_DATA_POOL . " AS c
			WHERE a.data_pool_level_parent_id='" . $id . "'
				AND a.data_pool_ref_id=b.data_pool_ref_id
				AND b.data_pool_id = c.data_pool_id;";
    $result = tep_db_query($sql);
    $arr = array();

    if (!is_null($selection_group_id)) {
        if ((int) $current_selection_group_id <= 0) {
            $selection_group_class_name = tep_gen_random_key("untitled_group", TABLE_DATA_POOL, "data_pool_class");
            $selection_group_name = tep_gen_random_key("Untitled Group ", TABLE_DATA_POOL, "data_pool_name");

            $data_pool_data_array = array('data_pool_name' => $selection_group_name,
                'data_pool_class' => $selection_group_class_name,
                'data_pool_description' => "Untitled Selection Group",
                'data_pool_input_field' => "radio"
            );
            tep_db_perform(TABLE_DATA_POOL, $arr);

            $current_selection_group_id = tep_db_insert_id();
        } else {
            $current_selection_group_id = (int) $selection_group_id;
        }
    }

    while ($row_main = tep_db_fetch_array($result)) {
        if ($skip_id != $row_main['data_pool_level_tags_id']) {
            if (!is_null($selection_group_id)) {
                $lid = $current_selection_group_id;
            } else {
                $data_pool_random_class = tep_gen_random_key(str_replace(" ", "_", $row_main['data_pool_name']), TABLE_DATA_POOL, "data_pool_class");

                $arr_data_pool = array('data_pool_name' => $row_main['data_pool_name'],
                    'data_pool_class' => $data_pool_random_class,
                    'data_pool_description' => $row_main['data_pool_description'],
                    'data_pool_input_field' => $row_main['data_pool_input_field']
                );

                tep_db_perform(TABLE_DATA_POOL, $arr_data_pool);
            }

            $arr_data_pool_ref = array('data_pool_id' => $lid);

            tep_db_perform(TABLE_DATA_POOL_REF, $arr_data_pool_ref);
            $lid = tep_db_insert_id();

            $arr = array('data_pool_level_parent_id' => $parent,
                'data_pool_level_name' => $row_main['data_pool_level_name'],
                'data_pool_level_value' => $row_main['data_pool_level_value'],
                'data_pool_ref_id' => $lid,
                'data_pool_sort_order' => $row_main['data_pool_sort_order']
            );

            tep_db_perform(TABLE_DATA_POOL_LEVEL_TAGS, $arr);
            $lid = tep_db_insert_id();

            tep_import_selections($row_main['data_pool_level_tags_id'], $lid, null, $skip_id);
        }
    }
}

function tep_data_duplicate_template2($id, $parent, $prod_id) {
    static $ret;

    if ($parent == 0) {
        $level_child_tag_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " WHERE data_pool_level_parent_id=0 AND data_pool_level_tags_id ='" . $id . "';";
    } else {
        $level_child_tag_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL_TAGS . " WHERE data_pool_level_parent_id ='" . $id . "';";
    }

    $level_child_tag_result_sql = tep_db_query($level_child_tag_select_sql);
    $arr = array();

    while ($row_main = tep_db_fetch_array($level_child_tag_result_sql)) {
        $fields = tep_get_input_field($row_main['data_pool_ref_id']);

        $arr = array('data_pool_level_parent_id' => $parent,
            'data_pool_level_name' => $row_main['data_pool_level_name'],
            'data_pool_level_value' => $row_main['data_pool_level_value'],
            'data_pool_max_level' => $row_main['data_pool_max_level'],
            'data_pool_min_level' => $row_main['data_pool_min_level'],
            'products_id' => $prod_id,
            'data_pool_sort_order' => $row_main['data_pool_sort_order'],
            'data_pool_input_field' => $fields['data_pool_input_field'],
            'data_pool_level_class_name' => $fields['data_pool_name'],
            'data_pool_level_class' => $fields['data_pool_class']
        );

        tep_db_perform(TABLE_DATA_POOL_LEVEL, $arr);
        $lid = tep_db_insert_id();

        if ($parent == 0)
            $ret = $lid;

        //	Copy the brackets from bracket tag to real bracket
        $main_bracket_id_select_sql = "SELECT DISTINCT brackets_tags_dependent FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_tags_dependent != '0' AND data_pool_level_tags_id ='" . $row_main['data_pool_level_tags_id'] . "'";
        $main_bracket_id_result_sql = tep_db_query($main_bracket_id_select_sql);

        if (tep_db_num_rows($main_bracket_id_result_sql) > 0) {
            while ($main_bracket_id_row = tep_db_fetch_array($main_bracket_id_result_sql)) {
                //	Create the bracket parent first
                $arr = array('brackets_key' => KEY_PL_BRACKET,
                    'brackets_dependent' => 0,
                    'data_pool_level_id' => $lid
                );

                tep_db_perform(TABLE_BRACKETS, $arr);
                $lid_second = tep_db_insert_id();

                $res_second = tep_db_query("SELECT * FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_tags_dependent ='" . $main_bracket_id_row['brackets_tags_dependent'] . "';");

                while ($row_second = tep_db_fetch_array($res_second)) {
                    $arr = array('brackets_key' => $row_second['brackets_tags_key'],
                        'brackets_value' => $row_second['brackets_tags_value'],
                        'brackets_dependent' => $lid_second,
                        'data_pool_level_id' => $lid
                    );

                    tep_db_perform(TABLE_BRACKETS, $arr);
                }
            }
        }

        // This one-time settings are not depending on bracket anymore. As long as there is such setting for this level_id, we include it.
        if (tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_START_LEVEL) !== false) {
            $start_level_data_array = array('brackets_key' => KEY_PL_START_LEVEL,
                'brackets_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id']),
                'brackets_dependent' => 0,
                'data_pool_level_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS, $start_level_data_array);

            $start_level_alias_data_array = array('brackets_key' => KEY_PL_START_LEVEL_ALIAS,
                'brackets_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_START_LEVEL_ALIAS),
                'brackets_dependent' => 0,
                'data_pool_level_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS, $start_level_alias_data_array);

            $base_time_data_array = array('brackets_key' => KEY_PL_BASE_TIME,
                'brackets_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_BASE_TIME),
                'brackets_dependent' => 0,
                'data_pool_level_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS, $base_time_data_array);

            $base_price_data_array = array('brackets_key' => KEY_PL_BASE_PRICE,
                'brackets_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_BASE_PRICE),
                'brackets_dependent' => 0,
                'data_pool_level_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS, $base_price_data_array);

            $level_label_data_array = array('brackets_key' => KEY_PL_LEVEL_LABEL,
                'brackets_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_LEVEL_LABEL),
                'brackets_dependent' => 0,
                'data_pool_level_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS, $level_label_data_array);

            $class_label_data_array = array('brackets_key' => KEY_PL_CLASS_LABEL,
                'brackets_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_CLASS_LABEL),
                'brackets_dependent' => 0,
                'data_pool_level_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS, $class_label_data_array);

            $min_time_data_array = array('brackets_key' => KEY_PL_MIN_TIME,
                'brackets_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_MIN_TIME),
                'brackets_dependent' => 0,
                'data_pool_level_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS, $min_time_data_array);
        }

        tep_data_duplicate_template2($row_main['data_pool_level_tags_id'], $lid, $prod_id);
    }

    return (int) $ret;
}

// ---------------------------------- START FUNCTION FOR DUPLICATING STUFFS WITHIN THE SAME TABLE ----------------------------------
// Duplicating from existing template for newly created template
function tep_duplicate_system_options($from_template_id, $new_template_id) {
    $options_tags_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_OPTIONS_TAGS . " WHERE data_pool_template_id ='" . $from_template_id . "' ORDER BY data_pool_options_sort_order";
    $options_tags_result_sql = tep_db_query($options_tags_select_sql);
    while ($options_tags_row = tep_db_fetch_array($options_tags_result_sql)) {
        $option_sql_data_array = array('data_pool_template_id' => $new_template_id,
            'data_pool_options_title' => $options_tags_row["data_pool_options_title"],
            'data_pool_options_class' => $options_tags_row["data_pool_options_class"],
            'data_pool_options_input_type' => $options_tags_row["data_pool_options_input_type"],
            'data_pool_options_input_size' => $options_tags_row["data_pool_options_input_size"],
            'data_pool_options_required' => $options_tags_row["data_pool_options_required"],
            'data_pool_options_show_supplier' => $options_tags_row["data_pool_options_show_supplier"],
            'data_pool_options_sort_order' => $options_tags_row["data_pool_options_sort_order"]
        );

        tep_db_perform(TABLE_DATA_POOL_OPTIONS_TAGS, $option_sql_data_array);
        $option_id = tep_db_insert_id();

        $option_values_tags_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_OPTIONS_VALUES_TAGS . " WHERE data_pool_options_id ='" . $options_tags_row["data_pool_options_id"] . "' ORDER BY data_pool_options_values_sort_order, data_pool_options_values_id";
        $option_values_tags_result_sql = tep_db_query($option_values_tags_select_sql);
        $i = 0;
        while ($option_values_tags_row = tep_db_fetch_array($option_values_tags_result_sql)) {
            $option_values_sql_data_array = array('data_pool_options_values_id' => ($i + 1),
                'data_pool_options_id' => $option_id,
                'data_pool_options_value' => $option_values_tags_row["data_pool_options_value"],
                'data_pool_options_values_price' => $option_values_tags_row["data_pool_options_values_price"],
                'data_pool_options_values_eta' => $option_values_tags_row["data_pool_options_values_eta"],
                'data_pool_options_values_min_level' => $option_values_tags_row["data_pool_options_values_min_level"],
                'data_pool_options_values_max_level' => $option_values_tags_row["data_pool_options_values_max_level"],
                'data_pool_options_values_sort_order' => $option_values_tags_row["data_pool_options_values_sort_order"]
            );
            tep_db_perform(TABLE_DATA_POOL_OPTIONS_VALUES_TAGS, $option_values_sql_data_array);
            $i++;
        }
    }
}

function tep_data_duplicate_template($id, $parent) {
    static $ret;

    if ($parent == 0) {
        $sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL_TAGS . "
				WHERE data_pool_level_parent_id ='" . $parent . "'
			   		AND data_pool_level_tags_id ='" . $id . "'";
    } else {
        $sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL_TAGS . "
				WHERE data_pool_level_parent_id ='" . $id . "'";
    }

    $result = tep_db_query($sql);
    $arr = array();

    while ($row_main = tep_db_fetch_array($result)) {
        $level_tags_data_array = array('data_pool_level_parent_id' => $parent,
            'data_pool_level_name' => $row_main['data_pool_level_name'],
            'data_pool_level_value' => $row_main['data_pool_level_value'],
            'data_pool_min_level' => $row_main['data_pool_min_level'],
            'data_pool_max_level' => $row_main['data_pool_max_level'],
            'data_pool_ref_id' => $row_main['data_pool_ref_id'],
            'data_pool_sort_order' => $row_main['data_pool_sort_order']
        );

        tep_db_perform(TABLE_DATA_POOL_LEVEL_TAGS, $level_tags_data_array);
        $lid = tep_db_insert_id();

        if ($parent == 0)
            $ret = $lid;

        // copy the brackets
        $main_bracket_id_select_sql = "	SELECT DISTINCT brackets_tags_dependent
										FROM " . TABLE_BRACKETS_TAGS . "
										WHERE brackets_tags_dependent<>'0' AND data_pool_level_tags_id ='" . $row_main['data_pool_level_tags_id'] . "'";
        $main_bracket_id_result_sql = tep_db_query($main_bracket_id_select_sql);

        while ($main_bracket_id_row = tep_db_fetch_array($main_bracket_id_result_sql)) {
            //create the bracket parent first
            $main_bracket_data_array = array('brackets_tags_key' => KEY_PL_BRACKET,
                'brackets_tags_dependent' => 0,
                'data_pool_level_tags_id' => $lid
            );

            tep_db_perform(TABLE_BRACKETS_TAGS, $main_bracket_data_array);
            $lid_second = tep_db_insert_id();

            $res_second = tep_db_query("SELECT * FROM " . TABLE_BRACKETS_TAGS . " WHERE brackets_tags_dependent='" . $main_bracket_id_row['brackets_tags_dependent'] . "';");

            while ($row_second = tep_db_fetch_array($res_second)) {
                $arr = array('brackets_tags_key' => $row_second['brackets_tags_key'],
                    'brackets_tags_value' => $row_second['brackets_tags_value'],
                    'brackets_tags_dependent' => $lid_second,
                    'data_pool_level_tags_id' => $lid
                );

                tep_db_perform(TABLE_BRACKETS_TAGS, $arr);
            }
        }

        // This one-time settings are not depending on bracket anymore. As long as there is such setting for this level_id, we include it.
        if (tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_START_LEVEL) !== false) {
            $start_level_data_array = array('brackets_tags_key' => KEY_PL_START_LEVEL,
                'brackets_tags_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id']),
                'brackets_tags_dependent' => 0,
                'data_pool_level_tags_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS_TAGS, $start_level_data_array);


            $start_level_alias_data_array = array('brackets_tags_key' => KEY_PL_START_LEVEL_ALIAS,
                'brackets_tags_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_START_LEVEL_ALIAS),
                'brackets_tags_dependent' => 0,
                'data_pool_level_tags_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS_TAGS, $start_level_alias_data_array);

            $base_time_data_array = array('brackets_tags_key' => KEY_PL_BASE_TIME,
                'brackets_tags_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_BASE_TIME),
                'brackets_tags_dependent' => 0,
                'data_pool_level_tags_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS_TAGS, $base_time_data_array);

            $base_price_data_array = array('brackets_tags_key' => KEY_PL_BASE_PRICE,
                'brackets_tags_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_BASE_PRICE),
                'brackets_tags_dependent' => 0,
                'data_pool_level_tags_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS_TAGS, $base_price_data_array);

            $level_label_data_array = array('brackets_tags_key' => KEY_PL_LEVEL_LABEL,
                'brackets_tags_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_LEVEL_LABEL),
                'brackets_tags_dependent' => 0,
                'data_pool_level_tags_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS_TAGS, $level_label_data_array);

            $class_label_data_array = array('brackets_tags_key' => KEY_PL_CLASS_LABEL,
                'brackets_tags_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_CLASS_LABEL),
                'brackets_tags_dependent' => 0,
                'data_pool_level_tags_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS_TAGS, $class_label_data_array);

            $min_time_data_array = array('brackets_tags_key' => KEY_PL_MIN_TIME,
                'brackets_tags_value' => tep_get_one_time_bracket_value($row_main['data_pool_level_tags_id'], 'data_pool_level_tags_id', KEY_PL_MIN_TIME),
                'brackets_tags_dependent' => 0,
                'data_pool_level_tags_id' => $lid
            );
            tep_db_perform(TABLE_BRACKETS_TAGS, $min_time_data_array);
        }

        tep_data_duplicate_template($row_main['data_pool_level_tags_id'], $lid);
    }

    return (int) $ret;
}

// ---------------------------------- END FUNCTION FOR DUPLICATING STUFFS WITHIN THE SAME TABLE ----------------------------------
// by subrat, clean the data
function tep_filtersqldata(&$data) {
    $data = mysql_escape_string($data);
    $data = htmlentities($data, ENT_QUOTES);
}

function tep_clean_data($var, $filter = false) {
    if (!is_array($var)) {
        if (isset($var)) {
            if ($filter)
                tep_filtersqldata($var);
            return $var;
        } else {
            return "";
        }
    } else {
        for ($i = 0; $i < sizeof($var); ++$i) {
            if (isset($var[$i])) {
                if ($filter)
                    tep_filtersqldata($var[$i]);
            } else {
                $var[$i] = "";
            }
        }
        return $var;
    }
}

function tep_get_supplier_list_timer($sup_grp_id, $list_id) {
    $supplier_timer_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_LIST_TIME_SETTING . " WHERE supplier_groups_id = '" . (int) $sup_grp_id . "' AND products_purchases_lists_id = '" . (int) $list_id . "'";
    $supplier_timer_result_sql = tep_db_query($supplier_timer_select_sql);

    $time_res = array();
    if ($supplier_timer_row = tep_db_fetch_array($supplier_timer_result_sql)) {
        $time_res['status'] = $supplier_timer_row['normal_status'];

        $time_res['first_list_start_time'] = explode(':', $supplier_timer_row['first_list_start_time']);
        $time_res['first_list_end_time'] = explode(':', $supplier_timer_row['first_list_end_time']);
        if (tep_not_null($supplier_timer_row['first_list_edit_time'])) {
            $time_res['first_list_edit_time'] = explode(':', $supplier_timer_row['first_list_edit_time']);
            $time_res['first_list_edit_anytime'] = 0;
        } else {
            $time_res['first_list_edit_anytime'] = 1;
        }
        $time_res['second_list_start_time'] = explode(':', $supplier_timer_row['second_list_start_time']);
        $time_res['second_list_end_time'] = explode(':', $supplier_timer_row['second_list_end_time']);

        $time_res['auto_on'] = (int) $supplier_timer_row['auto_on'];
        $time_res['auto_off'] = (int) $supplier_timer_row['auto_off'];
        $time_res['current_status'] = $supplier_timer_row['current_status'];
    }

    return $time_res;
}

function tep_get_supplier_pref_setting($sup_id) {
    $setting_array = array();

    $pref_setting_select_sql = "SELECT user_setting_key, user_setting_value FROM " . TABLE_USER_SETTING . " WHERE user_setting_user_id = '" . (int) $sup_id . "'";
    $pref_setting_result_sql = tep_db_query($pref_setting_select_sql);

    while ($pref_setting_row = tep_db_fetch_array($pref_setting_result_sql)) {
        $setting_array[$pref_setting_row["user_setting_key"]] = $pref_setting_row["user_setting_value"];
    }

    return $setting_array;
}

function tep_time_check($start_time, $end_time, $use_time = '') {
    if (!tep_not_null($start_time) || !tep_not_null($end_time)) {
        return false;
    }
    $start_time_array = explode(':', $start_time);
    $end_time_array = explode(':', $end_time);

    $start_time = ltrim($start_time_array[0], '0') . $start_time_array[1];
    $end_time = ltrim($end_time_array[0], '0') . $end_time_array[1];

    $start_time = (int) $start_time;
    $end_time = (int) $end_time;

    if (tep_not_null($use_time)) {
        $t_t_check = ltrim($use_time, '0');
        $t_t_check = (int) $t_t_check;
    } else {
        $t_t_check = date('H') . date('i');
        $t_t_check = ltrim($t_t_check, '0');
        $t_t_check = (int) $t_t_check;
    }

    if ($end_time > $start_time) {
        return ($t_t_check >= $start_time) && ($end_time >= $t_t_check);
    } else {
        return ($t_t_check >= $start_time) || ($t_t_check <= $end_time);
    }
}

function tep_set_current_status($doSet = true) {
    $time = tep_get_open_close_time();
    $end_time = $time['end_time'];
    $start_time = $time['start_time'];
    $status = $time['status'];
    $current_status = $time['current_status'];
    $auto_off_check = (int) $time['auto_off'];
    $auto_on_check = (int) $time['auto_on'];
    $time_in_range = tep_time_check($time['start_time_hrs'], $time['end_time_hrs']);

    if ($status == "STATUS_ON") {
        if ($doSet)
            tep_db_query("UPDATE supplier_time_setting SET current_status='STATUS_ON';");

        return "STATUS_ON";
    } else if ($status == "STATUS_OFF") {
        if ($doSet)
            tep_db_query("UPDATE supplier_time_setting SET current_status='STATUS_OFF';");

        return "STATUS_OFF";
    } else if ($status == "STATUS_AUTO") {
        if ($current_status == 'STATUS_OFF') {
            if ($time_in_range && $auto_on_check) {
                if ($doSet)
                    tep_db_query("UPDATE supplier_time_setting SET current_status='STATUS_ON';");

                return "STATUS_ON";
            } else {
                //if($doSet)
                //tep_db_query("UPDATE supplier_time_setting SET current_status='STATUS_ON';");
                return "STATUS_OFF";
            }
        } else if ($current_status == 'STATUS_ON') {
            if (!$time_in_range && $auto_off_check) {
                if ($doSet)
                    tep_db_query("UPDATE supplier_time_setting SET current_status='STATUS_OFF';");

                return "STATUS_OFF";
            } else {
                //if($doSet)
                //tep_db_query("UPDATE supplier_time_setting SET current_status='STATUS_ON';");
                return "STATUS_ON";
            }
        }
    }
}

function tep_array_serialize($arr) {
    return urlencode(serialize($arr));
}

function tep_array_unserialize($val) {
    return unserialize(urldecode(stripslashes($val)));
}

////
// Encrypt Data
function tep_encrypt_data($theData) {
    $length = strlen($theData);
    $length = sprintf("%020d", $length);
    $theData = mcrypt_encrypt(SECURE_CIPHER, SECURE_KEY, $theData, MCRYPT_MODE_CBC, SECURE_KEY_IV);

    $theData = base64_encode($theData);
    $theData = $length . $theData;
    return ($theData);
}

////
// Decrypt Data
function tep_decrypt_data($theData) {
    if ($theData) {
        $length = substr($theData, 0, 20);
        $theData = substr($theData, 20);
        $length = intval($length);

        $theData = base64_decode($theData);
        $theData = mcrypt_decrypt(SECURE_CIPHER, SECURE_KEY, $theData, MCRYPT_MODE_CBC, SECURE_KEY_IV);
        $theData = substr($theData, 0, $length);
        $theData = base64_decode($theData);
    }

    return $theData;
}

function tep_show_base64_img($keyident) {
    require_once(DIR_WS_CLASSES . 'custom_product_code.php');

    $cd_key_info_select_sql = "	SELECT file_type, custom_products_code_viewed, orders_products_id,
                                        to_s3, products_id, code_date_added
								FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
								WHERE custom_products_code_id = '" . tep_db_input($keyident) . "'";
    $cd_key_info_result_sql = tep_db_query($cd_key_info_select_sql);
    $cd_key_info_row = tep_db_fetch_array($cd_key_info_result_sql);

    if ($cd_key_info_row['file_type'] == 'soft') {
        ; // Text format
    } else {
        header("Content-type: image/jpeg");
        header("Expires: Mon, 02 May 2001 23:00:00 GMT");
        header("Cache-Control: no-store, no-cache, must-revalidate");
        header("Cache-Control: post-check=0, pre-check=0", false);
        header("Pragma: no-cache");
    }

    $view_cdkey_images_permission = tep_admin_files_actions(FILENAME_CDKEY, 'CP_VIEW_CDKEY_IMAGES');
    $to_s3 = $cd_key_info_row['to_s3'];
    $products_id = $cd_key_info_row['products_id'];
    $code_date_added = $cd_key_info_row['code_date_added'];
    $cpc_obj = new custom_product_code();

    if ($view_cdkey_images_permission) {
        $theData = $cpc_obj->getCode($keyident, $to_s3, $products_id, $code_date_added);
        echo ($theData !== FALSE ? tep_decrypt_data($theData) : '');
    } else {
        // Only can view key for CB order
        $view_sold_cdkey_images_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_CDKEY_DETAILS');

        if ($view_sold_cdkey_images_permission) {
            if ($cd_key_info_row['custom_products_code_viewed'] == '1') {
                $customer_is_cb_select_sql = "	SELECT c.customers_id
												FROM " . TABLE_CUSTOMERS . " AS c
												INNER JOIN " . TABLE_ORDERS . " AS o
													ON c.customers_id=o.customers_id
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
													ON o.orders_id=op.orders_id
												WHERE op.orders_products_id = '" . (int) $cd_key_info_row['orders_products_id'] . "'
													AND FIND_IN_SET(4, customers_flag) ";
                $customer_is_cb_result_sql = tep_db_query($customer_is_cb_select_sql);
                if ($customer_is_cb_row = tep_db_fetch_array($customer_is_cb_result_sql)) { // This is CB Order
                    $theData = $cpc_obj->getCode($keyident, $to_s3, $products_id, $code_date_added);
                    echo ($theData !== FALSE ? tep_decrypt_data($theData) : '');
                }
            }
        }
    }

    unset($cpc_obj);

    if ($cd_key_info_row['file_type'] != 'soft') {
        exit;
    }
}

/**
 * @param	dir		string	Full path with trailing slash
 * @return	bool	True if folder is empty, False if not empty.
 * */
function tep_is_dir_empty($dir) {
    $dl = opendir($dir);
    if ($dl) {
        while ($name = readdir($dl)) {
            if ($name == '.' || $name == '..') {
                continue;
            }
            if (is_dir("$dir$name")) {
                continue;
            }
            return false;
            break;
        }
        closedir($dl);
    }
    return true;
}

function tep_change_mobile_country($cid){
    include_once(DIR_WS_CLASSES . 'curl.php');

    $curl_obj = new curl();
    $data = array(
        'merchant' => HTTP_SHASSO_CLIENT_ID,
        'signature' => md5($cid . "|" . HTTP_SHASSO_CLIENT_SECRET),
        'cid' => $cid,
    );
    $curl_resp = $curl_obj->curl_post(HTTP_SHASSO_PORTAL . "/sso/change-mobile-country", $data);
    return json_decode($curl_resp, true);
}

function tep_kick_customer($cid) {
    include_once(DIR_WS_CLASSES . 'curl.php');

    $curl_obj = new curl();
    $data = array(
        'merchant' => HTTP_SHASSO_CLIENT_ID,
        'signature' => md5($cid . "|" . HTTP_SHASSO_CLIENT_SECRET),
        'cid' => $cid,
    );
    $curl_resp = $curl_obj->curl_post(HTTP_SHASSO_PORTAL . "/sso/kickUser", $data);
    return json_decode($curl_resp, true);
}

function tep_disabled_2fa($cid) {
    include_once(DIR_WS_CLASSES . 'curl.php');

    $curl_obj = new curl();
    $data = array(
        'merchant' => HTTP_SHASSO_CLIENT_ID,
        'signature' => md5($cid . "|" . HTTP_SHASSO_CLIENT_SECRET),
        'cid' => $cid,
    );
    $curl_resp = $curl_obj->curl_post(HTTP_SHASSO_PORTAL . "/sso/disable-2fa", $data);
    return json_decode($curl_resp, true);
}

function tep_has_2fa($cid) {
    include_once(DIR_WS_CLASSES . 'curl.php');

    $curl_obj = new curl();
    $data = array(
        'merchant' => HTTP_SHASSO_CLIENT_ID,
        'signature' => md5($cid . "|" . HTTP_SHASSO_CLIENT_SECRET),
        'cid' => $cid,
    );
    $curl_resp = $curl_obj->curl_post(HTTP_SHASSO_PORTAL . "/sso/has-2fa", $data);
    $result = json_decode($curl_resp, true);

    return (isset($result['result']['tfa_enabled']) ? $result['result']['tfa_enabled'] : false);
}

function tep_generate_game_currencies($money = 0, $currency_array, $format = '') {
    $currencies_generated_array = array();

    switch ($format) {
        case 'all':
            for ($currency_count = 0; $currency_count < sizeof($currency_array); $currency_count++) {
                $current_currency = (int) ($money / $currency_array[$currency_count]['value']);
                $currencies_generated_array[] = array($currency_array[$currency_count]['currency'] => $current_currency);
                $money -= ($current_currency * $currency_array[$currency_count]['value']);
            }
            break;
        default:
            for ($currency_count = 0; $currency_count < sizeof($currency_array); $currency_count++) {
                if ($currency_array[$currency_count]['currency'] == $format) {
                    //echo $money;
                    $currencies_generated_array[] = array($format => number_format($money / $currency_array[$currency_count]['value'], 4));
                }
            }
            break;
    }

    return $currencies_generated_array;
}

function tep_get_product_cPath($products_id, $show_all = false) {
    $cPath = '';

    if ($show_all)
        $category_query = tep_db_query("select p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = '" . (int) $products_id . "' and p.products_id = p2c.products_id limit 1");
    else
        $category_query = tep_db_query("select p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = '" . (int) $products_id . "' and p.products_status = '1' and p.products_id = p2c.products_id limit 1");

    if (tep_db_num_rows($category_query)) {
        $category = tep_db_fetch_array($category_query);

        $categories = array();

        tep_get_parent_categories($categories, $category['categories_id'], true);
        $categories = array_reverse($categories);

        $cPath = implode('_', $categories);

        if (tep_not_null($cPath))
            $cPath .= '_';
        $cPath .= $category['categories_id'];
    }
    return $cPath;
}

function tep_get_first_list_quantity($products_id) {
    $first_list_quantity = 0;

    $product_first_list_qty_select_sql = "	SELECT sum(solp.products_quantity) as first_list_qty_total
								            FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol
								            INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp
								                ON sol.supplier_order_lists_id = solp.supplier_order_lists_id
								            WHERE solp.products_id = '" . tep_db_input($products_id) . "'
								            	AND sol.supplier_order_lists_status IN (1, 5)
								            	AND IF(sol.supplier_order_lists_status=1, solp.supplier_order_lists_type='2' AND solp.products_received_quantity IS NULL, solp.supplier_order_lists_type='1')";
    $product_first_list_qty_result_sql = tep_db_query($product_first_list_qty_select_sql);
    if ($row2 = tep_db_fetch_array($product_first_list_qty_result_sql)) {
        $first_list_quantity += (int) $row2['first_list_qty_total'];
    }

    $buyback_prod_pending_list_qty_select_sql = "	SELECT sum(br.buyback_request_quantity) as first_list_qty_total
								   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
								                		ON brg.buyback_request_group_id = br.buyback_request_group_id
								            		WHERE br.products_id = '" . tep_db_input($products_id) . "'
								            			AND brg.buyback_status_id = 1 ";
    $buyback_prod_pending_list_qty_result_sql = tep_db_query($buyback_prod_pending_list_qty_select_sql);
    if ($buyback_prod_pending_list_qty_row = tep_db_fetch_array($buyback_prod_pending_list_qty_result_sql)) {
        $first_list_quantity += (int) $buyback_prod_pending_list_qty_row['first_list_qty_total'];
    }

    $buyback_prod_processing_list_qty_select_sql = "SELECT sum(IF(brg.buyback_request_group_site_id > 0, br.buyback_quantity_confirmed, br.buyback_request_quantity)) as first_list_qty_total
								   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
								                		ON brg.buyback_request_group_id = br.buyback_request_group_id
								            		WHERE br.products_id = '" . tep_db_input($products_id) . "'
								            			AND brg.buyback_status_id = 2
								            			AND br.buyback_quantity_received=0";
    $buyback_prod_processing_list_qty_result_sql = tep_db_query($buyback_prod_processing_list_qty_select_sql);
    if ($buyback_prod_processing_list_qty_row = tep_db_fetch_array($buyback_prod_processing_list_qty_result_sql)) {
        $first_list_quantity += (int) $buyback_prod_processing_list_qty_row['first_list_qty_total'];
    }

    return $first_list_quantity;
}

function tep_get_game_list_arr() {
    require_once(DIR_WS_CLASSES . 'category.php');

    if (!is_array($selection_array)) {
        $game_list_array = array(array('id' => 0, 'text' => PULL_DOWN_DEFAULT)); //TEXT_SELECT_YOUR_GAME
    } else {
        $game_list_array = $selection_array;
    }
    $main_cat_select_sql = "SELECT c.categories_id
                            FROM " . TABLE_CATEGORIES . " AS c
                            WHERE c.categories_buyback_main_cat = 1
                            ORDER BY c.sort_order";
    $main_cat_result_sql = tep_db_query($main_cat_select_sql);
    while ($main_cat_row = tep_db_fetch_array($main_cat_result_sql)) {
        $cat_obj = new category($main_cat_row['categories_id']);
        if ($cat_obj->category_has_product_type(0, 1)) {
            $game_list_array[] = array('id' => $main_cat_row['categories_id'],
                'text' => strip_tags(tep_get_categories_name($main_cat_row['categories_id'])));
        }
        unset($cat_obj);
    }
    return $game_list_array;
}

function tep_get_site_name($site_id) {
    $site_name = '';
    $site_name_select_sql = "SELECT site_name FROM " . TABLE_SITE_CODE . " WHERE site_id = '$site_id'";
    $site_name_result_sql = tep_db_query($site_name_select_sql);
    if ($site_name_row = tep_db_fetch_array($site_name_result_sql)) {
        $site_name = $site_name_row['site_name'];
    }
    return $site_name;
}

function tep_get_site_name_list($retrieve_mode = 'all') {
    global $login_groups_id;

    $site_name_list = array();
    $site_name_select_sql = "SELECT site_id, site_name FROM " . TABLE_SITE_CODE;

    switch ($retrieve_mode) {
        case 'all':
            break;
        case 'buyback':
            $site_name_select_sql .= " WHERE site_has_buyback = 1 AND FIND_IN_SET('" . $login_groups_id . "', buyback_admin_groups_id)";
            break;
        case 'x_buyback':
            $site_name_select_sql .= " WHERE site_has_buyback = 0";
            break;
    }
    $site_name_select_sql .= " ORDER BY site_name";
    $site_name_result_sql = tep_db_query($site_name_select_sql);
    while ($site_name_row = tep_db_fetch_array($site_name_result_sql)) {
        $site_name_list[$site_name_row['site_id']] = $site_name_row['site_name'];
    }
    return $site_name_list;
}

function tep_insert_cron_pending_credit($trans_type, $trans_id, $trans_created_date, $mature_period, $trans_status) {
    // Preparing data for scheduled cron job
    $insert_cron = false;

    $cron_job_verify_select_sql = "	SELECT cron_pending_credit_trans_completed_date, cron_pending_credit_trans_error, cron_pending_credit_trans_executable_error
									FROM " . TABLE_CRON_PENDING_CREDIT . "
									WHERE cron_pending_credit_trans_type = '" . tep_db_input($trans_type) . "'
										AND cron_pending_credit_trans_id = '" . tep_db_input($trans_id) . "'";
    $cron_job_verify_result_sql = tep_db_query($cron_job_verify_select_sql);

    if (tep_db_num_rows($cron_job_verify_result_sql)) {
        $cron_job_verify_row = tep_db_fetch_array($cron_job_verify_result_sql);

        if ($cron_job_verify_row['cron_pending_credit_trans_error'] == 1 && $cron_job_verify_row['cron_pending_credit_trans_executable_error'] == 1) {
            $pending_credit_delete_sql = "	DELETE FROM " . TABLE_CRON_PENDING_CREDIT . "
											WHERE cron_pending_credit_trans_type = '" . tep_db_input($trans_type) . "'
											AND cron_pending_credit_trans_id = '" . tep_db_input($trans_id) . "'";
            tep_db_query($pending_credit_delete_sql);

            $insert_cron = true;
        }
    } else {
        $insert_cron = true;
    }

    if ($insert_cron) {
        $cron_pending_credit_data_array = array('cron_pending_credit_trans_type' => tep_db_prepare_input($trans_type),
            'cron_pending_credit_trans_id' => tep_db_prepare_input($trans_id),
            'cron_pending_credit_trans_created_date' => tep_db_prepare_input($trans_created_date),
            'cron_pending_credit_trans_completed_date' => 'now()',
            'cron_pending_credit_mature_period' => $mature_period,
            'cron_pending_credit_trans_status' => $trans_status
        );
        tep_db_perform(TABLE_CRON_PENDING_CREDIT, $cron_pending_credit_data_array);
    }
    // End of preparing data for scheduled cron job
}

function tep_remove_cron_pending_credit($trans_type, $trans_id) {
    $cron_job_delete_sql = "	DELETE FROM " . TABLE_CRON_PENDING_CREDIT . "
								WHERE cron_pending_credit_trans_type = '" . tep_db_input($trans_type) . "'
									AND cron_pending_credit_trans_id = '" . tep_db_input($trans_id) . "'";
    tep_db_query($cron_job_delete_sql);
}

function tep_insert_cron_po_dayterm($trans_id, $trans_created_date, $dayterm_period) {
    // Preparing data for scheduled cron job
    $insert_cron = false;

    $cron_job_verify_select_sql = " SELECT cron_po_dayterm_trans_error
                                    FROM " . TABLE_CRON_PO_DAYTERM . "
                                    WHERE cron_po_dayterm_trans_id = '" . tep_db_input($trans_id) . "'";
    $cron_job_verify_result_sql = tep_db_query($cron_job_verify_select_sql);

    if (tep_db_num_rows($cron_job_verify_result_sql)) {
        $cron_job_verify_row = tep_db_fetch_array($cron_job_verify_result_sql);

        if ($cron_job_verify_row['cron_po_dayterm_trans_error'] == 1) {
            $po_dayterm_delete_sql = "  DELETE FROM " . TABLE_CRON_PO_DAYTERM . "
                                        WHERE cron_po_dayterm_trans_id = '" . tep_db_input($trans_id) . "'";
            tep_db_query($po_dayterm_delete_sql);

            $insert_cron = true;
        }
    } else {
        $insert_cron = true;
    }

    if ($insert_cron) {
        $cron_po_dayterm_data_array = array(
            'cron_po_dayterm_trans_id' => tep_db_prepare_input($trans_id),
            'cron_po_dayterm_trans_created_date' => tep_db_prepare_input($trans_created_date),
            'cron_po_dayterm_trans_period' => tep_db_prepare_input($dayterm_period),
            'cron_po_dayterm_trans_date' => date('Y-m-d H:i:s', strtotime($trans_created_date.' +'.$dayterm_period.' day'))
        );
        tep_db_perform(TABLE_CRON_PO_DAYTERM, $cron_po_dayterm_data_array);
    }
    // End of preparing data for scheduled cron job
}

function tep_remove_cron_po_dayterm($trans_id) {
    $cron_job_delete_sql = "    DELETE FROM " . TABLE_CRON_PO_DAYTERM . "
                                WHERE cron_po_dayterm_trans_id = '" . tep_db_input($trans_id) . "'";
    tep_db_query($cron_job_delete_sql);
}

function tep_get_ip_address() {
    if (defined('CREW_USE_PROXY_SERVER') && CREW_USE_PROXY_SERVER == 'true') {
        if (isset($_SERVER)) {
            if (isset($_SERVER['HTTP_TRUE_CLIENT_IP'])) {
                $ip = $_SERVER['HTTP_TRUE_CLIENT_IP'];
            } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && tep_not_null($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $ip_array = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
                $ip = trim(end($ip_array));
            } else {
                $ip = $_SERVER['REMOTE_ADDR'];
            }
        } else {
            if (getenv('HTTP_TRUE_CLIENT_IP')) {
                $ip = getenv('HTTP_TRUE_CLIENT_IP');
            } else {
                $ip = getenv('REMOTE_ADDR');
            }
        }
    } else {
        if (getenv('HTTP_TRUE_CLIENT_IP')) {
            $ip = getenv('HTTP_TRUE_CLIENT_IP');
        } else {
            $ip = getenv('REMOTE_ADDR');
        }
    }
    return $ip;
}

//check customer whether is china buyback supplier
function tep_check_cn_buyback($customers_login_sites) {
    if (isset($customers_login_sites) && in_array('1', explode(",", $customers_login_sites))) {
        return true;
    }
    return false;
}

function tep_get_total_reserve_vip($products_id) {
    $total_reserve = 0;
    //get the orders products id which for same product
    $select_orders_products = " SELECT DISTINCT(orders_products_id) FROM " . TABLE_VIP_ORDER_ALLOCATION . " WHERE products_id='" . $products_id . "'";
    $select_orders_products_result = tep_db_query($select_orders_products);
    while ($select_orders_products_row = tep_db_fetch_array($select_orders_products_result)) {
        $vipOrderObj = new vip_order($select_orders_products_row['orders_products_id']);
        $vipOrderObj->get_orders_details();
        $max_reserve = $vipOrderObj->order_detail['quantity_need'];
        //total allocate for supplier
        $select_total_in_allocate = "SELECT SUM(vip_order_allocation_quantity) AS vip_order_allocation_quantity
									FROM " . TABLE_VIP_ORDER_ALLOCATION . "
									WHERE orders_products_id='" . $select_orders_products_row['orders_products_id'] . "'";
        $select_total_in_allocate_result = tep_db_query($select_total_in_allocate);
        $select_total_in_allocate_row = tep_db_fetch_array($select_total_in_allocate_result);

        if ($max_reserve >= $select_total_in_allocate_row['vip_order_allocation_quantity']) {
            $total_reserve = $total_reserve + $select_total_in_allocate_row['vip_order_allocation_quantity'];
        } else {
            $total_reserve = $total_reserve + $max_reserve;
        }
    }
    return $total_reserve;
}

function tep_get_enabled_pm_title() {
    $payment_gateways_obj = new payment_methods('payment_gateways');

    $pm_list_array = array();
    foreach ($payment_gateways_obj->payment_gateways_array as $payment_gateways_obj_id => $payment_gateways_obj_data) {
        if ($payment_gateways_obj_data->enabled) {
            $pm_list_array[] = array('text' => $payment_gateways_obj_data->display_title);
        }
    }

    return $pm_list_array;
}

//lock and unlock customer order and keep log.
function tep_customer_order_locking($orders_id, $lock_by = '0', $action = 'lock', $vip = 1) {
    // check for order lock
    $user_id = $lock_by;
    if ($lock_by == 0) {
        $user_id = 'system';
    }
    $log_obj = new log_files($user_id);

    if ($action == 'unlock') {
        $order_lock_select_sql = "SELECT orders_locked_by
								FROM " . TABLE_ORDERS . "
								WHERE orders_id='" . tep_db_input($orders_id) . "'";
        $order_lock_select_result = tep_db_query($order_lock_select_sql);
        $order_lock_select_row = tep_db_fetch_array($order_lock_select_result);

        if (tep_not_null($order_lock_select_row['orders_locked_by'])) {
            //unlock the order
            $admin_email_select_sql = "	SELECT admin_email_address
										FROM " . TABLE_ADMIN . "
										WHERE admin_id='" . $order_lock_select_row['orders_locked_by'] . "'";
            $admin_email_select_result = tep_db_query($admin_email_select_sql);
            $admin_email_select_row = tep_db_fetch_array($admin_email_select_result);

            if ($lock_by == $order_lock_select_row['orders_locked_by']) {
                //keep log for unlock order
                $log_obj->insert_orders_log($orders_id, ORDERS_LOG_UNLOCK_OWN_ORDER, FILENAME_ORDERS);
            } else {
                $log_obj->insert_orders_log($orders_id, sprintf(ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER, $admin_email_select_row['admin_email_address'], ($vip == 1) ? 'Lock for VIP Order' : 'Lock for GENESIS Project'), FILENAME_ORDERS);
            }
            $order_lock_array = array('orders_locked_by' => 'NULL',
                'orders_locked_from_ip' => 'NULL',
                'orders_locked_datetime' => 'NULL');
            tep_db_perform(TABLE_ORDERS, $order_lock_array, 'update', "  orders_id='" . tep_db_input($orders_id) . "'");
        }
    } else if ($action == 'lock') {
        //lock order by system.
        $order_lock_array = array('orders_locked_by' => $lock_by,
            'orders_locked_from_ip' => getenv("REMOTE_ADDR"),
            'orders_locked_datetime' => 'now()');
        tep_db_perform(TABLE_ORDERS, $order_lock_array, 'update', "  orders_id='" . tep_db_input($orders_id) . "'");
        $log_obj->insert_orders_log($orders_id, ORDERS_LOG_LOCK_ORDER, FILENAME_ORDERS);
    }
}

// get admin member's group name
function tep_get_admin_group_name($admin_email) {
    global $memcache_obj;

    $fresh_request = true;
    $cache_result = array();

    $cache_key = TABLE_ADMIN_GROUPS . '/admin_groups_name/array/admin_email_address';

    $cache_result = $memcache_obj->fetch($cache_key);

    if ($cache_result !== FALSE) {
        if (isset($cache_result[$admin_email])) {
            $fresh_request = false;
        }
    }

    if ($fresh_request) {
        $admin_group_select_sql = "	SELECT ag.admin_groups_name
									FROM " . TABLE_ADMIN . " AS a
									INNER JOIN " . TABLE_ADMIN_GROUPS . " AS ag
										ON a.admin_groups_id = ag.admin_groups_id
									WHERE a.admin_email_address = '" . tep_db_input($admin_email) . "'";
        $admin_group_result_sql = tep_db_query($admin_group_select_sql);
        if ($admin_group_row = tep_db_fetch_array($admin_group_result_sql)) {
            $cache_result[$admin_email] = $admin_group_row['admin_groups_name'];
            $memcache_obj->replace($cache_key, $cache_result, 86400);

            return $admin_group_row['admin_groups_name'];
        }
    } else {
        return $cache_result[$admin_email];
    }

    return '';
}

function tep_check_is_ip($passIp, &$pass_array) {
    $pass_array = array();
    if (preg_match("/^([0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}|[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}\/[0-9]{1,2})$/", $passIp, $matches)) {
        preg_match_all("/([0-9]+)/u", $passIp, $matched_all);
        if (count($matched_all[1])) {
            $count_loop = 0;
            $pass_array['ip'] = '';
            $pass_array['subnet'] = '';
            foreach ($matched_all[1] as $matched_loop) {
                if ($count_loop < 4) {
                    if ($matched_loop < 0 || $matched_loop > 255)
                        return false;
                    if ($pass_array['ip'] != '') {
                        $pass_array['ip'] .= '.';
                    }
                    $pass_array['ip'] .= $matched_loop;
                } else {
                    if ($matched_loop < 0 || $matched_loop > 32)
                        return false;
                    $pass_array['subnet'] = $matched_loop;
                }
                $count_loop++;
            }
            return true;
        }
    }
}

function tep_ip_in_binary_form($ip) {
    $ip_bin_str = '';

    $ip_array = explode('.', $ip);

    for ($i = 0; $i < count($ip_array); $i++) {
        $this_ip_value = ((int) $ip_array[$i] < 0 || (int) $ip_array[$i] > 255) ? 0 : (int) $ip_array[$i];

        $ip_bin_str .= sprintf("%08s", decbin($this_ip_value));
    }


    return $ip_bin_str;
}

function tep_update_orders_status_counter($record_array, $increment = 1) {
    $o_id = isset($record_array['orders_id']) ? (int) $record_array['orders_id'] : 0;
    $o_status_id = isset($record_array['orders_status_id']) ? (int) $record_array['orders_status_id'] : 0;
    $o_latest_date = (isset($record_array['date_added']) && $record_array['date_added'] != 'now()') ? '\'' . $record_array['date_added'] . '\'' : 'now()';

    if (!isset($record_array['changed_by']))
        $record_array['changed_by'] = 'system';

    if ($o_id && $o_status_id > 0) {
        $record_exist_select_sql = "	SELECT orders_id
                                        FROM " . TABLE_ORDERS_STATUS_STAT . "
                                        WHERE orders_id = " . $o_id . "
                                            AND orders_status_id = " . $o_status_id;
        $record_exist_result_sql = tep_db_query($record_exist_select_sql);

        if ($record_exist_row = tep_db_fetch_array($record_exist_result_sql)) {
            $orders_status_sql = "  UPDATE " . TABLE_ORDERS_STATUS_STAT . "
                                    SET occurrence=occurrence+" . (int) $increment . ",
                                        latest_date = " . $o_latest_date . ",
                                        changed_by = '" . $record_array['changed_by'] . "'
                                    WHERE orders_id = " . $o_id . "
                                        AND orders_status_id = " . $o_status_id;
            tep_db_query($orders_status_sql);
        } else {
            $orders_status_sql = "  INSERT INTO " . TABLE_ORDERS_STATUS_STAT . "
                                    (orders_id, orders_status_id, occurrence, first_date, latest_date, changed_by)
                                    VALUES (" . $o_id . ", " . $o_status_id . ", " . (int) $increment . ", NOW(), " . $o_latest_date . ", '" . $record_array['changed_by'] . "')";
            tep_db_query($orders_status_sql);

            switch ($o_status_id) {
                case 7:
                    $c_sel = "  SELECT customers_id, payment_methods_id, payment_methods_parent_id, date_purchased
                                FROM " . TABLE_ORDERS . " WHERE orders_id = " . $o_id;
                    $c_res = tep_db_query($c_sel);
                    if ($c_row = tep_db_fetch_array($c_res)) {
                        $pm_title = array();
                        $pm_id = array(
                            'payment_methods_parent_id' => $c_row['payment_methods_parent_id'],
                            'payment_methods_id' => $c_row['payment_methods_id']
                        );

                        foreach ($pm_id as $key => $val) {
                            $_title = '';

                            if (empty($val) && ($key == 'payment_methods_id')) {
                                $_title = 'Full Store Credit';
                            } else {
                                $pm_sel = " SELECT payment_methods_title FROM " . TABLE_PAYMENT_METHODS . " WHERE payment_methods_id = '" . $val . "'";
                                $pm_res = tep_db_query($pm_sel);
                                if ($pm_row = tep_db_fetch_array($pm_res)) {
                                    $_title = $pm_row['payment_methods_title'];
                                }
                            }

                            if ($_title !== '') {
                                $pm_title[$key] = $_title;
                            }
                        }

                        $m_data1 = array(
                            'customers_id' => $c_row['customers_id'],
                            'statistic_key' => 'last_payment_method_id',
                            'statistic_value' => $pm_id['payment_methods_id'],
                            'created_date' => $c_row['date_purchased']
                        );
                        $m_data2 = array(
                            'customers_id' => $c_row['customers_id'],
                            'statistic_key' => 'last_payment_method_title',
                            'statistic_value' => implode(' -> ', $pm_title),
                            'created_date' => $c_row['date_purchased']
                        );

                        $m_sel = "  SELECT created_date FROM " . TABLE_CUSTOMERS_PURCHASE_STATISTIC . "
                                    WHERE customers_id = " . $c_row['customers_id'] . "
                                        AND statistic_key = 'last_payment_method_id'";
                        $m_res = tep_db_query($m_sel);
                        if ($m_row = tep_db_fetch_array($m_res)) {
                            if (strtotime($c_row['date_purchased']) > strtotime($m_row['created_date'])) {
                                tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data1, 'update', " customers_id = '" . $c_row['customers_id'] . "' AND statistic_key = 'last_payment_method_id' ");
                                tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data2, 'update', " customers_id = '" . $c_row['customers_id'] . "' AND statistic_key = 'last_payment_method_title' ");
                            }
                        } else {
                            tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data1);
                            tep_db_perform(TABLE_CUSTOMERS_PURCHASE_STATISTIC, $m_data2);
                        }
                        unset($m_data1, $m_data);
                    }
                    break;
            }
        }

        return true;
    } else {
        return false;
    }
}

function tep_update_purchase_orders_status_counter($record_array, $increment = 1) {
    $po_id = isset($record_array['purchase_orders_id']) ? (int) $record_array['purchase_orders_id'] : 0;
    $po_status_id = isset($record_array['purchase_orders_status_id']) ? (int) $record_array['purchase_orders_status_id'] : 0;
    $po_latest_date = (isset($record_array['date_added']) && $record_array['date_added'] != 'now()') ? '\'' . $record_array['date_added'] . '\'' : 'now()';

    if (!isset($record_array['changed_by']))
        $record_array['changed_by'] = 'system';

    if ($po_id && $po_status_id > 0) {
        $record_exist_select_sql = "	SELECT purchase_orders_id
										FROM " . TABLE_PURCHASE_ORDERS_STATUS_STAT . "
										WHERE purchase_orders_id = " . $po_id . "
											AND purchase_orders_status_id = " . $po_status_id;
        $record_exist_result_sql = tep_db_query($record_exist_select_sql);

        if ($record_exist_row = tep_db_fetch_array($record_exist_result_sql)) {
            $orders_status_sql = "	UPDATE " . TABLE_PURCHASE_ORDERS_STATUS_STAT . "
	    								SET occurrence=occurrence+" . (int) $increment . ",
	    									latest_date = " . $po_latest_date . ",
	    									changed_by = '" . $record_array['changed_by'] . "'
	    							WHERE purchase_orders_id = " . $po_id . "
	    								AND purchase_orders_status_id = " . $po_status_id;
            tep_db_query($orders_status_sql);
        } else {
            $orders_status_sql = "	INSERT INTO " . TABLE_PURCHASE_ORDERS_STATUS_STAT . "
										(purchase_orders_id, purchase_orders_status_id, occurrence, latest_date, changed_by)
									VALUES (" . $po_id . ", " . $po_status_id . ", " . (int) $increment . ", " . $po_latest_date . ", '" . $record_array['changed_by'] . "')";
            tep_db_query($orders_status_sql);
        }

        return true;
    } else {
        return false;
    }
}

// get product extra info details
function tep_draw_products_extra_info($orders_product_id = '', $info_key = '') {
    $qry1 = tep_not_null($info_key) ? "AND orders_products_extra_info_key='" . tep_db_input($info_key) . "'" : '';
    $orders_products_extra_info_select_query = "SELECT * FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " WHERE orders_products_id='" . (int) $orders_product_id . "' $qry1 ";
    $orders_products_extra_info_select_res = tep_db_query($orders_products_extra_info_select_query);

    while ($orders_products_extra_info_row = tep_db_fetch_array($orders_products_extra_info_select_res)) {
        $extra_info[$orders_products_extra_info_row['orders_products_extra_info_key']] = $orders_products_extra_info_row['orders_products_extra_info_value'];
    }
    return $extra_info;
}

function tep_direct_open_buyback($method_id) {
    switch ($method_id) {
        case '1': // Face 2 Face
        case '4': // Open Store
            return false;
            break;
        case '2': // Put Into My Account
        case '3': // Mail
            return true;
            break;
        default:
            return false;
            break;
    }
}

function tep_order_product_pending_delivery($order_status, $custom_type, $customer_id) {
    $total_count = 0;

    if (tep_not_null($custom_type)) {
        switch ($custom_type) {
            case 1:
                $get_total_pending_select_sql = "	SELECT o.orders_id
													FROM " . TABLE_ORDERS . " AS o
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
														ON o.orders_id = op.orders_id
													WHERE customers_id = '" . (int) $customer_id . "'
														AND orders_status = '" . $order_status . "'
														AND op.custom_products_type_id = '1'
														AND op.products_quantity <> op.products_delivered_quantity";
                $get_total_pending_result_sql = tep_db_query($get_total_pending_select_sql);
                if (tep_db_num_rows($get_total_pending_result_sql)) {
                    $total_count++;
                }

                break;

            case 2:
                $get_total_pending_select_sql = "	SELECT o.orders_id
													FROM " . TABLE_ORDERS . " AS o
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
														ON o.orders_id = op.orders_id
													WHERE customers_id = '" . (int) $customer_id . "'
														AND orders_status = '" . $order_status . "'
														AND op.custom_products_type_id = '2'
														AND op.products_quantity <> op.products_delivered_quantity";
                $get_total_pending_result_sql = tep_db_query($get_total_pending_select_sql);
                if (tep_db_num_rows($get_total_pending_result_sql)) {
                    $total_count++;
                }
                break;

            case 3:
                $get_total_pending_select_sql = "	SELECT o.orders_id, op.orders_products_is_compensate, op.parent_orders_products_id
													FROM " . TABLE_ORDERS . " AS o
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
														ON o.orders_id = op.orders_id
													WHERE customers_id = '" . (int) $customer_id . "'
														AND orders_status = '" . $order_status . "'
														AND op.custom_products_type_id = '0'
														AND op.products_quantity <> op.products_delivered_quantity";
                $get_total_pending_result_sql = tep_db_query($get_total_pending_select_sql);
                while ($get_total_pending_row = tep_db_fetch_array($get_total_pending_result_sql)) {
                    if ($get_total_pending_row['orders_products_is_compensate'] == 1) {
                        if ($get_total_pending_row['parent_orders_products_id'] == 0) {
                            $total_count++;
                        }
                    } else {
                        if ($get_total_pending_row['parent_orders_products_id'] != 0) {
                            $total_count++;
                        }
                    }
                }
                break;
            default:
                break;
        }
    }
    return $total_count;
}

// Deducting the stock for the order when its status turn to verifying from pending.
// Created by wei chen
function tep_deduct_stock_for_automated_payment($order_id, $products, $from_status_id, $to_status_id) {
    global $messageStack;

    $orders_select_sql = "	SELECT customers_id
							FROM " . TABLE_ORDERS . "
							WHERE orders_id = '" . (int) $order_id . "'";
    $orders_result_sql = tep_db_query($orders_select_sql);
    $orders_row = tep_db_fetch_array($orders_result_sql);
    $customer_id = (int) $orders_row['customers_id'];

    $reset_purchase_eta_products_array = array();
    $reset_purchase_eta = false;

    reset($products);

    for ($i = 0; $i < count($products); $i++) {
        $stock_query = tep_db_query("SELECT products_quantity, products_actual_quantity, products_bundle, products_bundle_dynamic, products_cat_path, products_quantity_order, products_skip_inventory, custom_products_type_id FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_get_prid($products[$i]["id"]) . "'");

        if (tep_db_num_rows($stock_query) > 0) {
            $stock_values = tep_db_fetch_array($stock_query);
            if ($stock_values["products_bundle_dynamic"] == 'yes') {
                for ($pbd_loop = 0; $pbd_loop < count($products[$i]["bundle"]); $pbd_loop++) {
                    $subproduct_stock_query = tep_db_query("SELECT products_quantity, products_actual_quantity, products_bundle, products_bundle_dynamic, products_cat_path, products_quantity_order, products_skip_inventory, custom_products_type_id FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_get_prid($products[$i]["bundle"][$pbd_loop]["id"]) . "'");
                    $subproduct_stock_values = tep_db_fetch_array($subproduct_stock_query);

                    $estimate_stock_left = $subproduct_stock_values["products_quantity"] - $products[$i]["bundle"][$pbd_loop]["qty"];

                    // Update product's quantity if do not keep inventory is not set and keep a log for it
                    if (!$subproduct_stock_values["products_skip_inventory"]) {
                        $update_qty_array = array(array('field_name' => 'products_quantity',
                                'operator' => '=',
                                'value' => $estimate_stock_left)
                        );
                        // This function will handle the qty adjustment and keep the log if asking so
                        tep_set_product_qty(tep_get_prid($products[$i]["bundle"][$pbd_loop]["id"]), $update_qty_array, true, sprintf(LOG_SALES_ORDER, $order_id, $from_status_id, $to_status_id), '');
                    }

                    if (!$subproduct_stock_values["products_skip_inventory"]) {
                        $cat_cfg_array = tep_get_cfg_setting($products[$i]["bundle"][$pbd_loop]["id"], 'product');
                        $email_to_array = tep_parse_email_string($cat_cfg_array['LOW_STOCK_EMAIL']);

                        $stock_reorder = $subproduct_stock_values['products_quantity_order']; // individual product reorder level, if any
                        $warning_stock = ($stock_reorder != "") ? $stock_reorder : $cat_cfg_array['STOCK_REORDER_LEVEL'];

                        $current_stock = $estimate_stock_left;

                        $low_stock_email = '<b>Low stock warning:</b> ' . $products[$i]["bundle"][$pbd_loop]["name"] . "\n" . '<b>Model No.:</b> ' . $products[$i]["bundle"][$pbd_loop]["model"] . "\n" . '<b>Quantity:</b> ' . $estimate_stock_left . "\n" . '<b>Product URL:</b> ' . HTTP_CATALOG_SERVER . 'custom_product_info.php?products_id=' . $products[$i]["bundle"][$pbd_loop]["id"] . "\n\n" . '<b>Product\'s Reorder Level:</b> ' . $warning_stock . " units\n\n<b>Category Path:</b> " . $subproduct_stock_values["products_cat_path"];
                        $low_stock_subject = 'Low Stock Warning: ' . strip_tags($products[$i]["bundle"][$pbd_loop]["name"]);

                        if ($current_stock <= $warning_stock) {
                            if ($subproduct_stock_values["custom_products_type_id"] == '2') {
                                $low_stock_data = array('products_id' => $products[$i]["bundle"][$pbd_loop]["id"], 'custom_products_type_id' => $subproduct_stock_values["custom_products_type_id"]);
                                products_low_stock::add_low_stock_warning($low_stock_data, $messageStack);
                            }
                            if ((int) $subproduct_stock_values["custom_products_type_id"] > 0) {
                                //If custom product, check if exists config for low stock email to overwrite global.
                                $custom_product_low_stock_email_select_sql = "select custom_products_low_stock_email from " . TABLE_CUSTOM_PRODUCTS_TYPE . " where custom_products_type_id = '" . tep_db_prepare_input($subproduct_stock_values["custom_products_type_id"]) . "'";
                                $custom_product_low_stock_email_result = tep_db_query($custom_product_low_stock_email_select_sql);
                                if (($custom_product_low_stock_email_row = tep_db_fetch_array($custom_product_low_stock_email_result)) && tep_not_null($custom_product_low_stock_email_row['custom_products_low_stock_email'])) {
                                    if (tep_not_null($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']])) {
                                        $email_to_array = tep_parse_email_string($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']]);
                                    }
                                }
                            }

                            for ($email_cnt = 0; $email_cnt < count($email_to_array); $email_cnt++) {
                                tep_mail($email_to_array[$email_cnt]['name'], $email_to_array[$email_cnt]['email'], $low_stock_subject, $low_stock_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                            }
                        }
                    }
                    $reset_purchase_eta_products_array[] = $products[$i]["bundle"][$pbd_loop]["orders_products_id"];
                }
            } else if ($stock_values['products_bundle'] == 'yes') {
                for ($pbd_loop = 0; $pbd_loop < count($products[$i]["static"]); $pbd_loop++) {
                    $subproduct_stock_query = tep_db_query("SELECT products_quantity, products_actual_quantity, products_bundle, products_bundle_dynamic, products_cat_path, products_quantity_order, products_skip_inventory, custom_products_type_id FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_get_prid($products[$i]["static"][$pbd_loop]["id"]) . "'");
                    $subproduct_stock_values = tep_db_fetch_array($subproduct_stock_query);

                    $estimate_stock_left = $subproduct_stock_values["products_quantity"] - $products[$i]["static"][$pbd_loop]["qty"];

                    // Update product's quantity if do not keep inventory is not set and keep a log for it
                    if (!$subproduct_stock_values["products_skip_inventory"]) {
                        $update_qty_array = array(array('field_name' => 'products_quantity',
                                'operator' => '=',
                                'value' => $estimate_stock_left)
                        );
                        // This function will handle the qty adjustment and keep the log if asking so
                        tep_set_product_qty(tep_get_prid($products[$i]["static"][$pbd_loop]["id"]), $update_qty_array, true, sprintf(LOG_SALES_ORDER, $order_id, $from_status_id, $to_status_id), '');
                    }

                    if (!$subproduct_stock_values["products_skip_inventory"]) {
                        $cat_cfg_array = tep_get_cfg_setting($products[$i]["static"][$pbd_loop]["id"], 'product');
                        $email_to_array = tep_parse_email_string($cat_cfg_array['LOW_STOCK_EMAIL']);

                        $stock_reorder = $subproduct_stock_values['products_quantity_order']; // individual product reorder level, if any
                        $warning_stock = ($stock_reorder != "") ? $stock_reorder : $cat_cfg_array['STOCK_REORDER_LEVEL'];

                        $current_stock = $estimate_stock_left;

                        $low_stock_email = '<b>Low stock warning:</b> ' . $products[$i]["static"][$pbd_loop]["name"] . "\n" . '<b>Model No.:</b> ' . $products[$i]["static"][$pbd_loop]["model"] . "\n" . '<b>Quantity:</b> ' . $estimate_stock_left . "\n" . '<b>Product URL:</b> ' . HTTP_CATALOG_SERVER . 'custom_product_info.php?products_id=' . $products[$i]["static"][$pbd_loop]["id"] . "\n\n" . '<b>Product\'s Reorder Level:</b> ' . $warning_stock . " units\n\n<b>Category Path:</b> " . $subproduct_stock_values["products_cat_path"];
                        $low_stock_subject = 'Low Stock Warning: ' . strip_tags($products[$i]["static"][$pbd_loop]["name"]);

                        if ($current_stock <= $warning_stock) {
                            if ($subproduct_stock_values["custom_products_type_id"] == '2') {
                                $low_stock_data = array('products_id' => $products[$i]["static"][$pbd_loop]["id"], 'custom_products_type_id' => $subproduct_stock_values["custom_products_type_id"]);
                                products_low_stock::add_low_stock_warning($low_stock_data, $messageStack);
                            }
                            if ((int) $subproduct_stock_values["custom_products_type_id"] > 0) {
                                //If custom product, check if exists config for low stock email to overwrite global.
                                $custom_product_low_stock_email_select_sql = "select custom_products_low_stock_email from " . TABLE_CUSTOM_PRODUCTS_TYPE . " where custom_products_type_id = '" . tep_db_prepare_input($subproduct_stock_values["custom_products_type_id"]) . "'";
                                $custom_product_low_stock_email_result = tep_db_query($custom_product_low_stock_email_select_sql);
                                if (($custom_product_low_stock_email_row = tep_db_fetch_array($custom_product_low_stock_email_result)) && tep_not_null($custom_product_low_stock_email_row['custom_products_low_stock_email'])) {
                                    if (tep_not_null($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']])) {
                                        $email_to_array = tep_parse_email_string($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']]);
                                    }
                                }
                            }

                            for ($email_cnt = 0; $email_cnt < count($email_to_array); $email_cnt++) {
                                tep_mail($email_to_array[$email_cnt]['name'], $email_to_array[$email_cnt]['email'], $low_stock_subject, $low_stock_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                            }
                        }
                    }
                    $reset_purchase_eta_products_array[] = $products[$i]["static"][$pbd_loop]["orders_products_id"];
                }
            } else { // single product
                $estimate_stock_left = $stock_values['products_quantity'] - $products[$i]['qty'];

                //Update product's quantity if do not keep inventory is not set and keep a log for it
                if (!$stock_values["products_skip_inventory"]) {
                    $update_qty_array = array(array('field_name' => 'products_quantity',
                            'operator' => '=',
                            'value' => $estimate_stock_left)
                    );
                    // This function will handle the qty adjustment and keep the log if asking so
                    tep_set_product_qty(tep_get_prid($products[$i]['id']), $update_qty_array, true, sprintf(LOG_SALES_ORDER, $order_id, $from_status_id, $to_status_id), '');
                }

                if (!$stock_values["products_skip_inventory"]) {
                    $cat_cfg_array = tep_get_cfg_setting($products[$i]['id'], 'product');
                    $email_to_array = tep_parse_email_string($cat_cfg_array['LOW_STOCK_EMAIL']);

                    $stock_reorder = $stock_values['products_quantity_order'];
                    $warning_stock = ($stock_reorder != "") ? $stock_reorder : $cat_cfg_array['STOCK_REORDER_LEVEL'];

                    $current_stock = $estimate_stock_left;

                    $low_stock_email = '<b>Low stock warning:</b> ' . $products[$i]['name'] . "\n" . '<b>Model No.:</b> ' . $products[$i]['model'] . "\n" . '<b>Quantity:</b> ' . $estimate_stock_left . "\n" . '<b>Product URL:</b> ' . HTTP_CATALOG_SERVER . 'custom_product_info.php?products_id=' . $products[$i]['id'] . "\n\n" . '<b>Product\'s Reorder Level:</b> ' . $warning_stock . " units\n\n<b>Category Path:</b> " . $stock_values['products_cat_path'];
                    $low_stock_subject = 'Low Stock Warning: ' . strip_tags($products[$i]['name']);

                    if ($current_stock <= $warning_stock) {
                        if ($stock_values["custom_products_type_id"] == '2') {
                            $low_stock_data = array('products_id' => $products[$i]['id'], 'custom_products_type_id' => $stock_values["custom_products_type_id"]);
                            products_low_stock::add_low_stock_warning($low_stock_data, $messageStack);
                        }
                        if ((int) $stock_values["custom_products_type_id"] > 0) {
                            //If custom product, check if exists config for low stock email to overwrite global.
                            $custom_product_low_stock_email_select_sql = "select custom_products_low_stock_email from " . TABLE_CUSTOM_PRODUCTS_TYPE . " where custom_products_type_id = '" . tep_db_prepare_input($stock_values["custom_products_type_id"]) . "'";
                            $custom_product_low_stock_email_result = tep_db_query($custom_product_low_stock_email_select_sql);
                            if (($custom_product_low_stock_email_row = tep_db_fetch_array($custom_product_low_stock_email_result)) && tep_not_null($custom_product_low_stock_email_row['custom_products_low_stock_email'])) {
                                if (tep_not_null($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']])) {
                                    $email_to_array = tep_parse_email_string($cat_cfg_array[$custom_product_low_stock_email_row['custom_products_low_stock_email']]);
                                }
                            }
                        }

                        for ($email_cnt = 0; $email_cnt < count($email_to_array); $email_cnt++) {
                            tep_mail($email_to_array[$email_cnt]['name'], $email_to_array[$email_cnt]['email'], $low_stock_subject, $low_stock_email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                        }
                    }
                }

                $reset_purchase_eta_products_array[] = $products[$i]["orders_products_id"];
                if ($products[$i]['custom_products_type_id'] == 1)
                    $reset_purchase_eta = true;
            }
        }//end if
    }//end for loop

    /*     * ********************************************************
      Reset the Purchase ETA to -999 (Not counted as buyback
      demand, awaiting admin to enter the ETA)
     * ******************************************************** */
    if (!$reset_purchase_eta) {
        if ($to_status_id == 7) {
            $reset_purchase_eta = true;
        } else {
            $pwl_order_select_sql = "	SELECT COUNT(DISTINCT o.orders_id) AS total_count
										FROM " . TABLE_ORDERS . " AS o
										INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
											ON o.orders_id=op.orders_id
										WHERE customers_id='" . tep_db_input($customer_id) . "'
											AND orders_status='2'
											AND op.custom_products_type_id=1";
            $pwl_order_result_sql = tep_db_query($pwl_order_select_sql);
            $pwl_order_row = tep_db_fetch_array($pwl_order_result_sql);

            if ($pwl_order_row['total_count'] > 0)
                $reset_purchase_eta = true;
        }
    }

    if (count($reset_purchase_eta_products_array)) {
        if ($reset_purchase_eta) {
            $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
								SET orders_products_purchase_eta = -999
								WHERE orders_products_id IN ('" . implode("', '", $reset_purchase_eta_products_array) . "')
									AND custom_products_type_id = 0 ";
            tep_db_query($eta_update_sql);
        } else if ($to_status_id == 2) {
            $customer_profile_email = tep_get_customers_email($customer_id);
            if ($customer_id < '127270' || tep_info_verified_check($customer_id, $customer_profile_email, 'email') == '1') {
                $eta_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " AS opMain
									INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS opSub
										ON opMain.orders_products_id=opSub.parent_orders_products_id
									INNER JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei
										ON (opMain.orders_products_id=opei.orders_products_id AND opei.orders_products_extra_info_key = 'char_online_time')
									SET opSub.orders_products_purchase_eta=opei.orders_products_extra_info_value
									WHERE opSub.orders_products_id IN ('" . implode("', '", $reset_purchase_eta_products_array) . "')
										AND opSub.custom_products_type_id = 0 ";
                tep_db_query($eta_update_sql);
            }
        }
    }
}

function LoadImage($imgname) {
    /* Attempt to open */
    $dim = GetImageSize($imgname);

    switch ($dim[2]) {
        case 1:
            $im = imagecreatefromgif($imgname);
            $type = "gif";
            break;
        case 2:
            ini_set('gd.jpeg_ignore_warning', 1);
            $im = imagecreatefromjpeg($imgname);
            $type = "jpeg";
            break;
        case 3:
            $im = imagecreatefrompng($imgname);
            $type = "png";
            break;
    }

    /* See if it failed */
    if (!$im) {
        /* Create a black image */
        $im = imagecreatetruecolor(750, 30);
        $bgc = imagecolorallocate($im, 255, 255, 255);
        $tc = imagecolorallocate($im, 0, 0, 0);

        imagefilledrectangle($im, 0, 0, 750, 30, $bgc);

        /* Output an error message */
        imagestring($im, 3, 5, 5, 'Error loading ' . $imgname, $tc); //'Error loading image.'
    }
    return $im;
}

function resizeImage($file_path, $maxdim = 75) {
    $size = GetImageSize($file_path);
    $new_w = $maxdim;
    $new_h = $maxdim;

    if ($size[0] > $size[1]) {
        $to_w = $maxdim;
        $to_h = round($size[1] * ($maxdim / $size[0]));
        $to_x = 0;
        $to_y = round($maxdim - $to_h) / 2;
    } else {
        $to_h = $maxdim;
        $to_w = round($size[0] * ($maxdim / $size[1]));
        $to_y = 0;
        $to_x = round($maxdim - $to_w) / 2;
    }

    $scr_im = LoadImage($file_path);

    $dst_im = imagecreatetruecolor($new_w, $new_h);
    $set_bg_colour = ImageColorAllocate($dst_im, 255, 255, 255);
    $fill_bg_colour = ImageFill($dst_im, 0, 0, $set_bg_colour);

    ImageCopyResized($dst_im, $scr_im, $to_x, $to_y, 0, 0, $to_w, $to_h, $size[0], $size[1]);

    return $dst_im;
}

function tep_get_account_created_from($id) {
    $account_created_from = 0;
    $get_customer_info_sql = " SELECT customers_info_account_created_from
								FROM " . TABLE_CUSTOMERS_INFO . "
								WHERE customers_info_id = '" . tep_db_input($id) . "'";
    $get_customer_info_result_sql = tep_db_query($get_customer_info_sql);
    if ($get_customer_info_row = tep_db_fetch_array($get_customer_info_result_sql)) {
        $account_created_from = $get_customer_info_row['customers_info_account_created_from'];
    }
    return $account_created_from;
}

function tep_get_customers_name($customers_id) {
    $customers = tep_db_query("select customers_firstname, customers_lastname from " . TABLE_CUSTOMERS . " where customers_id = '" . (int) $customers_id . "'");
    $customers_values = tep_db_fetch_array($customers);

    return $customers_values['customers_firstname'] . ' ' . $customers_values['customers_lastname'];
}

function tep_create_buyback_order($orders_products_id, $products_hla_info, &$buyback_request_group_id) {
    global $currencies;

    if (count($products_hla_info) > 0) {
        $orders_info_select_sql = "	SELECT orders_id, products_id, custom_products_type_id
									FROM " . TABLE_ORDERS_PRODUCTS . "
									WHERE orders_products_id = '" . (int) $orders_products_id . "'
									AND products_delivered_quantity <= 0";
        $orders_info_result_sql = tep_db_query($orders_info_select_sql);

        if ($orders_info_row = tep_db_fetch_array($orders_info_result_sql)) {
            $orders_id = $orders_info_row['orders_id'];
            $products_id = $orders_info_row['products_id'];
            $custom_products_type_id = $orders_info_row['custom_products_type_id'];
            $buyback_request_group_id = '';
            $ref_id = '';

            if ($products_hla_info['products_hla_id'] > 0) {
                $seller_id = $products_hla_info['seller_id'];
                if (!class_exists('products_supplier')) {
                    include_once(DIR_WS_CLASSES . 'products_supplier.php');
                }

                if (class_exists('products_supplier')) {
                    $prod_sup_obj = new products_supplier($seller_id);

                    $supplier_margin = isset($prod_sup_obj->products_supplier['payout_percentage']) ? $prod_sup_obj->products_supplier['payout_percentage'] : 0;

                    $products_base_currency = tep_not_null($products_hla_info['products_base_currency']) ? $products_hla_info['products_base_currency'] : DEFAULT_CURRENCY;
                    $products_price = $currencies->advance_currency_conversion($products_hla_info['products_original_price'], $products_base_currency, DEFAULT_CURRENCY, false, 'sell');
                    $products_price = ($products_price * $supplier_margin) / 100;
                    $products_price = number_format($products_price, 2, '.', '');

                    if ($custom_products_type_id == 4) {
                        if (count($products_hla_info) > 0) {
                            $customer_id = $products_hla_info['seller_id'];
                            $products_hla_id = $products_hla_info['products_hla_id'];

                            $order_products_item_select_sql = "	SELECT orders_products_item_info
																FROM " . TABLE_ORDERS_PRODUCTS_ITEM . "
																WHERE orders_products_id = '" . $orders_products_id . "'
																ORDER BY products_hla_characters_id LIMIT 1";
                            $order_products_item_result_sql = tep_db_query($order_products_item_select_sql);
                            if ($order_products_item_row = tep_db_fetch_array($order_products_item_result_sql)) {
                                $orders_products_item_info = tep_array_unserialize($order_products_item_row['orders_products_item_info']);
                                $ref_id = $orders_products_item_info['products_ref_id'];
                            }

                            $bo_exist_select_sql = "SELECT buyback_request_id, buyback_request_group_id
													FROM " . TABLE_BUYBACK_REQUEST . "
													WHERE orders_products_id = " . (int) $orders_products_id;
                            $bo_exist_result_sql = tep_db_query($bo_exist_select_sql);

                            if ($bo_exist_row = tep_db_fetch_array($bo_exist_result_sql)) {
                                $buyback_request_group_id = $bo_exist_row['buyback_request_group_id'];
                                $update_bo_status_data_sql = array('buyback_status_id' => '1');
                                tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $update_bo_status_data_sql, 'update', " buyback_request_group_id = '" . $buyback_request_group_id . "'");

                                // Insert buyback history comment
                                $buyback_history_data_array = array('buyback_status_id' => '1',
                                    'buyback_request_group_id' => $buyback_request_group_id,
                                    'date_added' => 'now()',
                                    'customer_notified' => '1',
                                    'set_as_buyback_remarks' => '1',
                                    'comments' => ''
                                );

                                tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);

                                // Insert order history comment
                                $comment = '##BO##' . $buyback_request_group_id . '## BO Created for REF#' . $ref_id;

                                $comment_array = array('orders_id' => $orders_id,
                                    'orders_status_id' => '0',
                                    'date_added' => 'now()',
                                    'customer_notified' => '0',
                                    'comments' => $comment,
                                    'comments_type' => '0',
                                    'set_as_order_remarks' => '0',
                                    'changed_by' => $_SESSION['login_email_address']
                                );
                                tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $comment_array);
                            } else {
                                if (tep_not_null($products_hla_id)) {
                                    // Create buyback order
                                    $y = array('buyback_request_group_id' => 0,
                                        'customers_id' => $customer_id,
                                        'buyback_request_group_date' => 'now()',
                                        'buyback_request_group_expiry_date' => '',
                                        'remote_addr' => tep_get_ip_address(),
                                        'currency' => DEFAULT_CURRENCY,
                                        'currency_value' => $currencies->currencies[DEFAULT_CURRENCY]['buy_value'],
                                        'buyback_request_group_user_type' => (int) tep_get_account_created_from($customer_id)
                                    );

                                    tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $y);
                                    $buyback_request_group_id = tep_db_insert_id();

                                    // Insert buyback history comment
                                    $buyback_history_data_array = array('buyback_status_id' => '1',
                                        'buyback_request_group_id' => $buyback_request_group_id,
                                        'date_added' => 'now()',
                                        'customer_notified' => '1',
                                        'set_as_buyback_remarks' => '1',
                                        'comments' => ''
                                    );

                                    tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);

                                    // Insert order history comment
                                    $comment = '##BO##' . $buyback_request_group_id . '## BO Created for REF#' . $ref_id;

                                    $comment_array = array('orders_id' => $orders_id,
                                        'orders_status_id' => '0',
                                        'date_added' => 'now()',
                                        'customer_notified' => '0',
                                        'comments' => $comment,
                                        'comments_type' => '0',
                                        'set_as_order_remarks' => '0',
                                        'changed_by' => $_SESSION['login_email_address']
                                    );
                                    tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $comment_array);

                                    unset($y);
                                    $y = array('buyback_request_group_id' => $buyback_request_group_id,
                                        'products_id' => $products_id,
                                        'buyback_request_quantity' => 1,
                                        'buyback_amount' => $products_price,
                                        'buyback_unit_price' => $products_price,
                                        'orders_id' => $orders_id,
                                        'orders_products_id' => $orders_products_id
                                    );
                                    tep_db_perform(TABLE_BUYBACK_REQUEST, $y);
                                }
                            }

                            # insert notification
                            $m_attr = array(
                                'customers_id' => $customer_id,
                                'orders_id' => $buyback_request_group_id,
                                'orders_type' => 'BO',
                                'site_id' => 0
                            );
                            tep_db_perform(TABLE_ORDERS_NOTIFICATION, $m_attr);
                            unset($m_attr);

                            // Sending email to notify supplier
                            if (tep_not_null($buyback_request_group_id)) {
                                $customer_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_gender
													FROM " . TABLE_CUSTOMERS . "
													WHERE customers_id = '" . $customer_id . "'";
                                $customer_result = tep_db_query($customer_sql);
                                if ($customer_row = tep_db_fetch_array($customer_result)) {
                                    $customer_name = $customer_row['customers_firstname'] . " " . $customer_row['customers_lastname'];
                                    $customer_greeting_name = tep_get_email_greeting($customer_row['customers_firstname'], $customer_row['customers_lastname'], $customer_row['customers_gender']);

                                    if (eregi_dep('^[a-zA-Z0-9._-]+@[a-zA-Z0-9-]+\.[a-zA-Z.]{2,5}$', $customer_row['customers_email_address'])) {
                                        $products_cat_path_sql = "	SELECT p.products_cat_path, pd.products_name
																	FROM " . TABLE_PRODUCTS . " AS p
																	INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
																	ON (p.products_id = pd.products_id)
					 												WHERE pd.products_id = '" . tep_db_input($products_id) . "'
					 												AND language_id = '1'";
                                        $products_cat_path_result = tep_db_query($products_cat_path_sql);
                                        if ($products_cat_path_row = tep_db_fetch_array($products_cat_path_result)) {
                                            $buyback_product_list = $products_cat_path_row["products_cat_path"] . ' > ' . $products_cat_path_row["products_name"] . ' > REF#' . $ref_id . ' = ' . $currencies->format((double) $products_price, true, DEFAULT_CURRENCY, '', 'buy') . "\n";
                                        }

                                        $email_content = $customer_greeting_name .
                                                EMAIL_HLA_NEW_BUYBACK_BODY .
                                                sprintf(EMAIL_HLA_NEW_BUYBACK_ORDER_NUMBER, $buyback_request_group_id) . "\n" .
                                                sprintf(EMAIL_HLA_NEW_BUYBACK_DATE_ORDERED, tep_date_long(date("Y-m-d") . " 00:00:00")) . "\n" .
                                                sprintf(EMAIL_HLA_NEW_BUYBACK_CUSTOMER_EMAIL, $customer_row['customers_email_address']) . "\n\n" .
                                                EMAIL_HLA_NEW_BUYBACK_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . $buyback_product_list .
                                                EMAIL_SEPARATOR . "\n" .
                                                sprintf(EMAIL_HLA_NEW_BUYBACK_ORDER_TOTAL, $currencies->format($products_price, true, DEFAULT_CURRENCY, '', 'buy')) . "\n\n" .
                                                EMAIL_HLA_NEW_BUYBACK_COMMENTS . "\n\n" .
                                                EMAIL_HLA_NEW_BUYBACK_STATUS . "\n" .
                                                EMAIL_HLA_BUYBACK_ORDER_GUIDE . "\n\n" .
                                                EMAIL_HLA_NEW_BUYBACK_ORDER_CLOSING . "\n\n" .
                                                EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER;
                                        tep_mail($customer_name, $customer_row['customers_email_address'], sprintf(EMAIL_HLA_NEW_BUYBACK_SUBJECT, $buyback_request_group_id), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                    }
                                }
                            }

                            if (tep_not_null($products_hla_id)) {
                                $update_hla = "	UPDATE " . TABLE_PRODUCTS_HLA . "
												SET products_status = '1',
												actual_quantity = actual_quantity - 1
												WHERE products_hla_id = '" . tep_db_input($products_hla_id) . "'";
                                tep_db_query($update_hla);
                            }
                        }
                    }
                }
            }
        }
    }
}

function tep_get_categories_discount($categories_id, $id_type = 'catalog') {
    $cust_group_discount = 0;
    $cust_group_rebate = 0;
    $cust_group_discount_id = 0;
    $cid = $categories_id;
    $cust_group_info_array = array();

    if ($id_type == 'product')
        $cid = tep_get_actual_product_cat_id($categories_id);

    $cat_path = tep_get_particular_cat_path($cid);
    $cat_path_array = explode('_', $cat_path);
    $cat_path_array = array_merge(array(0), $cat_path_array);

    for ($i = 0; $i < count($cat_path_array); $i++) {
        $grp_discount_select_sql = "SELECT cgd.customers_groups_discount, cgd.customers_groups_rebate, cgd.customers_groups_discount_id, cg.customers_groups_name, cgd.customers_groups_id
									FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " as cgd
									INNER JOIN " . TABLE_CUSTOMERS_GROUPS . " as cg
										ON (cgd.customers_groups_id = cg.customers_groups_id)
									WHERE cgd.categories_id ='" . tep_db_input($cat_path_array[$i]) . "'
									ORDER BY cg.sort_order";
        $grp_discount_result_sql = tep_db_query($grp_discount_select_sql);
        while ($grp_discount_row = tep_db_fetch_array($grp_discount_result_sql)) {
            $cust_group_info_array[$grp_discount_row['customers_groups_id']] = array('cust_group_discount' => $grp_discount_row['customers_groups_discount'],
                'cust_group_rebate' => $grp_discount_row['customers_groups_rebate'],
                'cust_group_discount_id' => $grp_discount_row['customers_groups_discount_id'],
                'customers_groups_name' => $grp_discount_row['customers_groups_name']
            );
        }
    }
    return $cust_group_info_array;
}

if (!function_exists('memory_get_usage')) {

    function memory_get_usage() {
        //If its Windows
        //Tested on Win XP Pro SP2. Should work on Win 2003 Server too
        //Doesn't work for 2000
        //If you need it to work for 2000 look at http://us2.php.net/manual/en/function.memory-get-usage.php#54642
        if (substr(PHP_OS, 0, 3) == 'WIN') {
            if (substr(PHP_OS, 0, 3) == 'WIN') {
                $output = array();
                exec('tasklist /FI "PID eq ' . getmypid() . '" /FO LIST', $output);

                $memory_usage = preg_replace('/[\D]/', '', $output[5]) * 1024;
                return $memory_usage;
            }
        } else {
            //We now assume the OS is UNIX
            //Tested on Mac OS X 10.4.6 and Linux Red Hat Enterprise 4
            //This should work on most UNIX systems
            $pid = getmypid();
            exec("ps -eo%mem,rss,pid | grep $pid", $output);
            $output = explode(' ', $output[0]);
            //rss is given in 1024 byte units
            $memory_usage = $output[1] * 1024;
            return $memory_usage;
        }
    }

}

function convert_ascii_to_entities($str) {
    $count = 1;
    $out = '';
    $temp = array();

    for ($i = 0, $s = strlen($str); $i < $s; $i++) {
        $ordinal = ord($str[$i]);

        if ($ordinal < 128) {
            if (count($temp) == 1) {
                $out .= '&#' . array_shift($temp) . ';';
                $count = 1;
            }

            $out .= $str[$i];
        } else {
            if (count($temp) == 0) {
                $count = ($ordinal < 224) ? 2 : 3;
            }

            $temp[] = $ordinal;

            if (count($temp) == $count) {
                $number = ($count == 3) ? (($temp['0'] % 16) * 4096) + (($temp['1'] % 64) * 64) + ($temp['2'] % 64) : (($temp['0'] % 32) * 64) + ($temp['1'] % 64);
                $out .= '&#' . $number . ';';
                $count = 1;
                $temp = array();
            }
        }
    }

    return $out;
}

function _verify_locking_status($lock_id, $lock_tbl) {
    $_result = array();

    $_lock_sel_sql = "	SELECT a.admin_email_address, l.locking_from_ip, l.locking_datetime
                        FROM " . TABLE_LOCKING . " AS l
                        LEFT JOIN " . TABLE_ADMIN . " AS a
                                ON a.admin_id = l.locking_by
                        WHERE l.locking_trans_id = '" . $lock_id . "'
                                AND l.locking_table_name = '" . $lock_tbl . "'";
    $_lock_res_sql = tep_db_query($_lock_sel_sql);
    if ($_lock_row = tep_db_fetch_array($_lock_res_sql)) {
        $_result['locking_by'] = str_replace(strstr($_lock_row['admin_email_address'], '@'), '', $_lock_row['admin_email_address']);
        $_result['locking_from_ip'] = $_lock_row['locking_from_ip'];
        $_result['locking_datetime'] = $_lock_row['locking_datetime'];
    }

    return $_result;
}

function _get_custom_product_type($cpt_filter) {
    $cpt_list = tep_get_product_type();

    $cpt[] = array('id' => '',
        'text' => SELECT_DEFAULT_EMPTY);

    for ($i = 0, $cnt = count($cpt_list); $cnt > $i; $i++) {
        if (in_array($cpt_list[$i]['id'], $cpt_filter)) {
            $cpt[] = array('id' => $cpt_list[$i]['id'],
                'text' => $cpt_list[$i]['text']);
        }
    }

    return $cpt;
}

function _get_customer_detail($id) {
    $data = array();

    if (tep_not_null($id)) {
        include_once(DIR_WS_FUNCTIONS . FILENAME_CUSTOMERS_INFO_VERIFICATION);

        $_seller_dob = '';
        $_seller_im = array();

        $_seller_sel_sql = "SELECT c.customers_gender, c.customers_firstname, c.customers_lastname, c.customers_dob, c.customers_email_address,
                                c.customers_country_dialing_code_id, c.customers_telephone, c.customers_mobile, c.customers_msn, c.customers_qq,
                                c.customers_yahoo, c.customers_icq,
                                cc.username, cc.seller_status, cc.seller_group_id, csg.seller_group_name,
                                ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, ab.entry_state, ab.entry_country_id AS country_id,
                                ct.address_format_id
                            FROM " . TABLE_CUSTOMERS . " AS c
                            LEFT JOIN " . TABLE_C2C_CUSTOMERS . " AS cc
                                    ON cc.customers_id = c.customers_id
                            LEFT JOIN " . TABLE_C2C_SELLER_GROUPS . " AS csg
                                    ON csg.seller_group_id = cc.seller_group_id
                            LEFT JOIN " . TABLE_ADDRESS_BOOK . " AS ab
                                    ON ab.customers_id = c.customers_id
                            LEFT JOIN " . TABLE_COUNTRIES . " AS ct
                                    ON ct.countries_id = ab.entry_country_id
                            WHERE c.customers_id = '" . $id . "'";
        $_seller_res_sql = tep_db_query($_seller_sel_sql);
        $_seller_row = tep_db_fetch_array($_seller_res_sql);

        # Address
        $data['seller_address'] = tep_address_format($_seller_row['address_format_id'], $_seller_row, 1, '', '<br />', '', false);

        # IM
        (isset($_seller_row['customers_msn']) && !empty($_seller_row['customers_msn'])) ? $_seller_im[] = $_seller_row['customers_msn'] : '';
        (isset($_seller_row['customers_qq']) && !empty($_seller_row['customers_qq'])) ? $_seller_im[] = $_seller_row['customers_qq'] : '';
        (isset($_seller_row['customers_yahoo']) && !empty($_seller_row['customers_yahoo'])) ? $_seller_im[] = $_seller_row['customers_yahoo'] : '';
        (isset($_seller_row['customers_icq']) && !empty($_seller_row['customers_icq'])) ? $_seller_im[] = $_seller_row['customers_icq'] : '';
        $data['seller_im'] = (tep_not_null($_seller_im) ? implode('<br />', $_seller_im) : TEXT_NOT_AVAILABLE);

        # DOB
        if (isset($_seller_row['customers_dob']) && !empty($_seller_row['customers_dob'])) {
            $dob_style = '';
            $_seller_age = tep_calculate_age($_seller_row['customers_dob'], '', 0);

            if (preg_match('/([\d ]+)(yr)/is', $_seller_age, $regs)) {
                if ((int) trim($regs[1]) < 17) {
                    $dob_style = ' class="redIndicator" ';
                }
            } else {
                $dob_style = ' class="redIndicator" ';
            }

            $_seller_dob = '<span ' . $dob_style . '>' . tep_date_short($_seller_row['customers_dob'], PREFERRED_DATE_FORMAT) . ' (' . $_seller_age . ') </span>';
        }

        # Telephone
        $dialing_code = tep_format_telephone($id);
        $_seller_telephone = $dialing_code['country_international_dialing_code'] . ' ' . tep_parse_telephone($_seller_row['customers_telephone'], $_seller_row['customers_country_dialing_code_id'], 'id');
        $_seller_telephone = ($_seller_telephone != ' ' ? '+' . $_seller_telephone : TEXT_NOT_AVAILABLE);

        $data['seller_name'] = (tep_not_null($_seller_row['customers_firstname']) && tep_not_null($_seller_row['customers_lastname']) ? '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $id . '&action=edit') . '" target="_blank">' . $_seller_row['customers_firstname'] . ' ' . $_seller_row['customers_lastname'] . '</a><br />' : TEXT_NOT_AVAILABLE);
        $data['seller_username'] = tep_not_null($_seller_row['username']) ? $_seller_row['username'] : TEXT_NOT_AVAILABLE;
        $data['seller_status'] = tep_not_null($_seller_row['seller_status']) ? ($_seller_row['seller_status'] == 1 ? ACTIVE : NOT_ACTIVE) : TEXT_NOT_AVAILABLE;
        $data['seller_group_name'] = tep_not_null($_seller_row['seller_group_name']) ? $_seller_row['seller_group_name'] : TEXT_NOT_AVAILABLE;
        $data['customers_gender'] = ($_seller_row['customers_gender'] == 'm' ? TEXT_MALE : ($_seller_row['customers_gender'] == 'f' ? TEXT_FEMALE : TEXT_NOT_AVAILABLE));
        $data['seller_dob'] = tep_not_null($_seller_dob) ? $_seller_dob : TEXT_NOT_AVAILABLE;
        $data['customers_email_address'] = tep_not_null($_seller_row['customers_email_address']) ? $_seller_row['customers_email_address'] : TEXT_NOT_AVAILABLE;
        $data['seller_telephone'] = $_seller_telephone;
        $data['customers_mobile'] = (tep_not_null($_seller_row['customers_mobile']) ? $_seller_row['customers_mobile'] : TEXT_NOT_AVAILABLE);
    }

    return $data;
}

function tep_get_customer_language($custId)
{
    // Set default to English
    $lang = 'en';
    $custSql = "SELECT value AS customers_language
                FROM " . TABLE_CUSTOMERS_PREFERENCE . "
                WHERE preference_key = 'language' AND customers_id = '" . (int) $custId . "'";
    $custResultSql = tep_db_query($custSql);
    if ($custRow = tep_db_fetch_array($custResultSql)) {
        $lang = $custRow['customers_language'];
    }
    return $lang;
}

//Customers Status
// BOF: WebMakers.com Added: Downloads Controller
require(DIR_WS_FUNCTIONS . 'downloads_controller.php');
// EOF: WebMakers.com Added: Downloads Controller
// BOF IndvShip
//require(DIR_WS_FUNCTIONS . 'indvship_status.php');
// EOF
?>