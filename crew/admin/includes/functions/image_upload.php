<?
function tep_get_image_configuration($search_key, $search_value) {
	$result = array();
	
	$image_configuration_get_info_sql = "SELECT image_configuration_id, file_path, web_path, aws_s3_info, user_groups
											FROM ". TABLE_IMAGE_CONFIGURATION ."
											WHERE " . tep_db_prepare_input($search_key) . " = '". tep_db_prepare_input($search_value) ."'";
	$image_configuration_get_result_sql = tep_db_query($image_configuration_get_info_sql);
	if ($image_configuration_row = tep_db_fetch_array($image_configuration_get_result_sql)) {
		$result = array( "image_configuration_id" => $image_configuration_row['image_configuration_id'],
						 "file_path" => $image_configuration_row['file_path'],
						 "web_path" => $image_configuration_row['web_path'],
						 "aws_s3_info" => json_decode($image_configuration_row['aws_s3_info']),
						 "user_groups" => $image_configuration_row['user_groups']
						);
	}
	return $result;
}

function tep_get_directory_files($image_dir) {
	# array to hold return value
	$retval = array();
	# add trailing slash if missing
	if(substr($image_dir, -1) != "/") {
		$image_dir .= "/";
	}
		# open pointer to directory and read list of files
		$d = @dir($image_dir) or die("Failed opening directory $image_dir for reading");

		while (false !== ($entry = $d->read())) {
			# skip hidden files
			if ($entry[0] == ".") {
				continue;
			}

			$filepath = $image_dir.$entry;

			if (is_dir($filepath)){
				$retval[] = array( "lastmod" => filemtime($filepath),
								   "name" => $entry,
								   "size" => 0,
								   "lastacc" => fileatime($filepath)
								   );
			} elseif (is_readable($filepath)) {
				$retval[] = array( "lastmod" => filemtime($filepath),
									"name" => $entry,
									"size" => filesize($filepath),
									"lastacc" => fileatime($filepath)
									);
			}
		}
		$d->close();

	return $retval;
}

function tep_get_file_ext($filename) {
	$filename = strtolower($filename) ;
	$exts = split_dep("[/\\.]", $filename) ;
	$n = count($exts)-1;
	$exts = $exts[$n];

	return $exts;
}

function tep_verify_file_mime($filename) {
	//detect type and process accordinally
    $size=getimagesize($filename);
    switch($size["mime"]){
        case "image/jpeg":
            $im = imagecreatefromjpeg($filename); //jpeg file
        break;
        case "image/gif":
            $im = imagecreatefromgif($filename); //gif file
      	break;
		case "image/png":
		  $im = imagecreatefrompng($filename); //png file
		break;
		default:
	    	$im=false;
	    break;
    }
	return $im;
}
?>