<?

function tep_get_all_categories_in_array($pass_id='') {
	$cat_select_sql = "	SELECT c.categories_id, cd.categories_name, c.categories_parent_path, c.parent_id 
						FROM " . TABLE_CATEGORIES . " AS c
						INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
							ON c.categories_id=cd.categories_id 
						WHERE cd.language_id ='1' ";
    if (tep_not_null($pass_id)) {
		$cat_select_sql .= "  AND categories_parent_path LIKE '%\_".$pass_id."\_%' ";
    }
	$cat_select_sql .= "ORDER BY c.sort_order, cd.categories_name";
	$cat_result_sql = tep_db_query($cat_select_sql);
	$cat_array = array();
	while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
        $cat_array[$cat_row['parent_id']][] = array("categories_id"=>$cat_row['categories_id'],
													"categories_name"=>$cat_row['categories_name'],
													"categories_parent_path"=>$cat_row['categories_parent_path']);
	}
	$return_array = array();
    if (count($cat_array)) {
		tep_get_all_categories_child_in_array($cat_array, $return_array, (int)$pass_id);
    }
    return $return_array;
}

function tep_get_all_categories_child_in_array($pass_array, &$return_array, $pass_id='0', $max_layer = 2) {
	if (!isset($pass_array[$pass_id])) return 0;
	
    foreach($pass_array[$pass_id] as $cat_parent_id => $cat_data) {
    	$cat_parent_path_array = array();
    	if (tep_not_null($cat_data['categories_parent_path'])) {
			$cat_parent_path_array = array_filter(explode("_",$cat_data['categories_parent_path']),'tep_not_null');
            
            if (count($cat_parent_path_array) >= $max_layer) {
                return 0;
            }
        }
        
        $cat_parent_path_eval = "";
        if (count($cat_parent_path_array)) {
        	$cat_parent_path_eval = '[' . implode('][', $cat_parent_path_array) . ']';
        }
        eval('$return_array'.$cat_parent_path_eval . '['.$cat_data['categories_id'].'] = $cat_data;');
        if (isset($pass_array[$cat_data["categories_id"]])) {
			tep_get_all_categories_child_in_array($pass_array, $return_array, $cat_data["categories_id"], $max_layer);
        }
    }
}

function tep_ul_categories_tree_array($pass_id='', $pass_open_id='') {
    $cat_array = tep_get_all_categories_in_array((int)$pass_id);
    $cat_str = '';
    tep_ul_categories_tree_child_array($cat_array, $cat_str, $pass_open_id);
    return $cat_str;
}

function tep_ul_categories_tree_child_array($pass_array, &$cat_str, $pass_open_id='') {
    $open_array = array();
    if (tep_not_null($pass_open_id)) {
        $open_array = explode(",",$pass_open_id);
        $open_array = array_filter($open_array, "tep_not_null");
    }
    
    if (is_array($pass_array)) {
    	if ($cat_str=='') {
    		$cat_str = '	<ul id="myTree">';
    	} else {
    		$cat_str .= '	<ul style="display: none;" id="ul_cat_'.$pass_array['categories_id'].'">';
    	}
		$temp_array = array_keys($pass_array);
		$temp_array = array_filter($temp_array,"is_int");
    	foreach ($temp_array as $id) {
    		$cat_str .= '<li style="-moz-user-select: none;" class="treeItem">';
    		//check sub
    		$sub_temp_array = array_keys($pass_array[$id]);
    		$sub_temp_array = array_filter($sub_temp_array,"is_int");
            if (isset($sub_temp_array) && count($sub_temp_array) > 0) {
            	//$cat_str .= '	<img src="images/icon-expand-small.gif" class="expandImage" width="12" height="9.3" id="'.$id.'" onclick="toggle_cat(this)">';
            	$cat_str .= '	<span class="expandImage" onclick="toggle_cat(this)"><font style="color:green;cursor:pointer">[+]</font></span>';
            } else {
            	//$cat_str .= '	<img src="images/icon-collapse-small.gif" class="expandImage" width="12" height="9.3" id="'.$id.'">';
            	$cat_str .= '	<span class="expandImage"><font style="color:green;cursor:pointer">[+]</font></span>';
            }
            $cat_str .= '	<span class="textHolder">
            					<input type="checkbox" name="chk_cat[]" value="'.$id.'" ' .(in_array($id, $open_array)?' checked ':'') . ' >
            						' . $pass_array[$id]['categories_name'] . ' (' . $id . ')
            				</span>';
			tep_ul_categories_tree_child_array($pass_array[$id], $cat_str, $pass_open_id);
            $cat_str .= '</li>';
    	}
    	$cat_str .= '	</ul>';
    }
}
?>