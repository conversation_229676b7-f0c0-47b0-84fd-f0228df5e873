<?php

/*
  $Id: localization.php,v 1.7 2015/06/10 07:07:31 weesiong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */
//To disable the warning when no respond
error_reporting(0);
include_once(DIR_WS_CLASSES . 'curl.php');

function quote_layer_currency()
{
    $curl_obj = new curl();
    // true for local testing
    $curl_obj->connect_via_proxy = true;
    $currency_data = $curl_obj->curl_request('GET', 'http://www.apilayer.net/api/live?access_key='.CURRENCY_LAYER_API_KEY);

    if(empty($currency_data)){
        reportError(array(
            'curlError' => $curl_obj->get_error()
        ));
    }

    return $currency_data;
}

function quote_oanda_currency($code, $base = DEFAULT_CURRENCY)
{

    try {
        $page = file('http://www.oanda.com/convert/fxdaily?value=1&redirected=1&exch=' . $code . '&format=CSV&dest=Get+Table&sel_list=' . $base);
    } catch (Exception $e) {
        echo 'Caught exception: ', $e->getMessage(), "\n";
    }

    $match = array();

    preg_match('/(.+),(\w{3}),([0-9.]+),([0-9.]+)/i', implode('', $page), $match);

    if (sizeof($match) > 0) {
        return $match[3];
    } else {
        return false;
    }
}

function quote_xe_currency($to, $from = DEFAULT_CURRENCY)
{
    if ($to == $from) {
        return '1';
    }

    try {
        $page = file('https://www.xe.com/currencyconverter/convert/?Amount=1&From=' . $from . '&To=' . $to);
    } catch (Exception $e) {
        echo 'Caught exception: ', $e->getMessage(), "\n";
    }

    $match = array();

    preg_match('/1( |&nbsp;)' . trim($from) . '( |&nbsp;)=( |&nbsp;)([0-9]+[,]?[0-9.]+)( |&nbsp;)' . trim($to) . '/i', implode('', $page), $match);

    if (isset($match[4]) && $match[4]) {
        $m = str_replace(",", "", $match[4]);
        return $m;
    } else {
        return false;
    }
}

function tep_get_currencies_array($omit_currency_array = '')
{
    $currency_array = array();

    if (!is_array($omit_currency_array)) {
        $omit_currency_array = array();
    }

    $currency_select_sql = "SELECT currencies_id, code, title
							FROM " . TABLE_CURRENCIES . " 
							WHERE code NOT IN ('" . implode("', '", $omit_currency_array) . "') 
							ORDER BY code";
    $currency_result_sql = tep_db_query($currency_select_sql);

    while ($currency_row = tep_db_fetch_array($currency_result_sql)) {
        $currency_array[] = array(
            'id' => $currency_row['currencies_id'],
            'text' => $currency_row['code'] . ' - ' . $currency_row['title']
        );
    }

    return $currency_array;
}

function tep_get_currencies_info_array($searchField, $searchValue)
{
    $currency_info_array = array();

    $currencies_info_select_sql = "	SELECT * FROM " . TABLE_CURRENCIES . " WHERE " . $searchField . " = '" . tep_db_input($searchValue) . "' ORDER BY code";
    $currencies_info_result_sql = tep_db_query($currencies_info_select_sql);

    while ($currencies_info_row = tep_db_fetch_array($currencies_info_result_sql)) {
        $currency_info_array[] = $currencies_info_row;
    }

    return $currency_info_array;
}

function tep_get_currencies_list()
{
    $currencies_default_list_array = array(array('id' => '', 'text' => 'Please Select'));

    $currencies_info_array = tep_get_currencies_info_array('currencies_live_update', 1);

    for ($currencies_info_cnt = 0; $currencies_info_cnt < sizeof($currencies_info_array); $currencies_info_cnt++) {
        $currencies_default_list_array[] = array(
            'id' => $currencies_info_array[$currencies_info_cnt]['currencies_id'],
            'text' => $currencies_info_array[$currencies_info_cnt]['code']
        );
    }

    return $currencies_default_list_array;
}

function reportError($response_data)
{
    include_once(DIR_WS_CLASSES . 'slack_notification.php');
    $slack = new slack_notification();
    $data = json_encode(array(
        'text' => 'cURL Error - ' . date("F j, Y H:i") . ' from ' . (isset($_SERVER['SERVER_NAME']) && !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-'),
        'attachments' => array(
            array(
                'color' => 'warning',
                'text' => json_encode($response_data)
            )
        )
    ));
    $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
}

?>