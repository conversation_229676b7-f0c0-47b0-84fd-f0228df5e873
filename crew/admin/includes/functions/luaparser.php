<?php

function lua_parse($filename) {
	$stack = array( array( "",  array()) );
	$stack_pos = 0; 
	$lines = file($filename);
	$last_line = "";
	
	foreach( $lines as $line ) {
		if( substr( $line, -2, 1 ) == '\\' ) {
			$last_line .= substr($line, 0, -2) . "\n";
			continue;
		}
		
		$line = $last_line . $line;
		$last_line = "";
		if(strstr( $line, "=" )) {
			list($name, $value) = explode("=", $line, 2);
			$name = preg_replace("/^\s*/", "", $name);
			$name = preg_replace("/\s*$/", "", $name);
			
			if(substr($name,0,2) == "[\"") {
				$name = substr($name, 2, -2);
			} else if(substr($name, 0, 1) == "[") {
				$name = intval(substr($name, 1, -1));
			}
			
			$value = preg_replace( "/^\s*/", "", $value );
			$value = preg_replace( "/\s*$/", "", $value );
			
			if( $value == "{" ) {
				$stack_pos++;
				$stack[$stack_pos] = array($name, array());
			} else {
				if( preg_match("/^\"([^\"\\\\]|\\\\.)*\"/", $value, $matches ) ) {
					$value = substr($matches[0],1,-1);
					$value = preg_replace( "/\\\\(.)/", "\\1", $value );
				} else if( preg_match("/^-?[0-9]+\\.[0-9]+/", $value, $matches ) ) {
					$value = floatval($matches[0]);
				} else if( preg_match("/^-?[0-9+]+/", $value, $matches ) ) {
					$value = intval($matches[0]);
				} else if( preg_match("/^(True|False)/", $value, $matches ) ) {
					if( $matches[0] == "True" ) {
						$value = True;
					} else {
						$value = False;
					}
				} else if( preg_match("/^nil/", $value ) ) {
					$value = NULL;
				}
				
				$stack[$stack_pos][1][$name] = $value;
			}
		} else if( preg_match( "/^\s*}/", $line ) ) {
			$hash = $stack[$stack_pos];
			$stack_pos--;
			$stack[$stack_pos][1][$hash[0]] = $hash[1];
		}
	}
	return $stack[0][1];
}

function update_char($name, $data) {
	$char_name = tep_db_prepare_input($name);
	$server = tep_db_prepare_input($data['Server']);
	
	$game_char_id_select_sql = "	SELECT game_char_id 
									FROM " . TABLE_GAME_CHAR . " 
									WHERE name = '" . tep_db_input($char_name) . "' 
										AND server = '" . tep_db_input($server) . "'";
									
	$game_char_id_result_sql = tep_db_query($game_char_id_select_sql);
	$game_char_id_row = tep_db_fetch_array($game_char_id_result_sql);
	
	if (tep_not_null($game_char_id_row['game_char_id'])) {
		$game_char_id = $game_char_id_row['game_char_id'];
	} else {
		$sql_data_array = array('name' => $char_name,
								'server' => $server
								);
								
		tep_db_perform(TABLE_GAME_CHAR, $sql_data_array);
		$game_char_id = tep_db_insert_id();
	}
	
	
	$sql_data_array = array('game_char_id' => $game_char_id,
							'stat_int' => $data['Stats']['Intellect'],
							'stat_agl' => $data['Stats']['Agility'],
							'stat_sta' => $data['Stats']['Stamina'],
							'stat_str' => $data['Stats']['Strength'],
							'stat_spr' => $data['Stats']['Spirit'],
							'guild_name' => $data['Guild']['GuildName'],
							'guild_title' => $data['Guild']['Title'],
							'guild_rank' => $data['Guild']['Rank'],
							'race' => $data['Race'],
							'res_frost' => $data['Resists']['Frost'],
							'res_arcane' => $data['Resists']['Arcane'],
							'res_fire' => $data['Resists']['Fire'],
							'res_shadow' => $data['Resists']['Shadow'],
							'res_nature' => $data['Resists']['Nature'],
							'armor' => $data['Armor'],
							'level' => $data['Level'],
							'defense' => $data['Defense'],
							'talent_points' => $data['TalentPoints'],
							'money_c' => $data['Money']['Copper'],
							'money_s' => $data['Money']['Silver'],
							'money_g' => $data['Money']['Gold'],
							'exp' => $data['Experience'],
							'class' => $data['Class'],
							'health' => $data['Health'],
							'melee_power' => $data["Melee Attack"]['AttackPower'],
							'melee_rating' => $data["Melee Attack"]['AttackRating'],
							'melee_range' => $data["Melee Attack"]['DamageRange'],
							'melee_range_tooltip' => tooltip($data["Melee Attack"]['DamageRangeTooltip']),
							'melee_power_tooltip' => tooltip($data["Melee Attack"]['AttackPowerTooltip']),
							'ranged_power' => $data["Ranged Attack"]['AttackPower'],
							'ranged_rating' => $data["Ranged Attack"]['AttackRating'],
							'ranged_range' => $data["Ranged Attack"]['DamageRange'],
							'ranged_range_tooltip' => tooltip($data["Ranged Attack"]['DamageRangeTooltip']),
							'ranged_power_tooltip' => tooltip($data["Ranged Attack"]['AttackPowerTooltip']),
							'version' => $data['ProfilerVersion'],
							'game_char_history_date' => 'now()'
							);
	
							
	tep_db_perform(TABLE_GAME_CHAR_HISTORY, $sql_data_array);
	
	$game_char_history_id = tep_db_insert_id();
	
	// Start Equipments
	$sql_data_array = array('game_char_id' => $game_char_id,
							'game_char_history_id' => $game_char_history_id,
							'char_item_parent' => 'equip'
							);
	
	tep_db_perform(TABLE_CHAR_ITEM_HISTORY, $sql_data_array);
	
	$item_char_item_history_id = tep_db_insert_id();
	
	if (isset($data['Equipment'])) {
		$equip = $data['Equipment'];
		foreach(array_keys($equip) as $slot_name) {
			$sql_data_array = array('char_item_history_id' => $item_char_item_history_id,
									'char_item_storage_name' => $equip[$slot_name]['Name'],
									'char_item_storage_slot' => $slot_name,
									'char_item_storage_color' => $equip[$slot_name]['Color'],
									'char_item_storage' => $equip[$slot_name]['Item'],
									'char_item_storage_texture' => $equip[$slot_name]['Texture'],
									'char_item_storage_tooltip' => tooltip($equip[$slot_name]['Tooltip'])
									);
			
			tep_db_perform(TABLE_CHAR_ITEM_STORAGE, $sql_data_array);
		}
	} // End Equipments
	
	if (isset($data['Bank'])) { // Start Bank
		$sql_data_array = array('game_char_id' => $game_char_id,
								'game_char_history_id' => $game_char_history_id,
								'char_item_parent' => 'bank'
								);
		
		tep_db_perform(TABLE_CHAR_ITEM_HISTORY, $sql_data_array);
		
		$bank_char_item_history_id = tep_db_insert_id();
		
		$bank = $data['Bank'];
		
		if (isset($bank['Contents'])) {
			foreach(array_keys($bank['Contents']) as $bank_slot) {
				$sql_data_array = array('char_item_history_id' => $bank_char_item_history_id,
										'char_item_storage_name' => $bank['Contents'][$bank_slot]['Name'],
										'char_item_storage_slot' => $bank_slot,
										'char_item_storage_color' => $bank['Contents'][$bank_slot]['Color'],
										'char_item_storage' => $bank['Contents'][$bank_slot]['Item'],
										'char_item_storage_texture' => $bank['Contents'][$bank_slot]['Texture'],
										'char_item_storage_quantity' => $bank['Contents'][$bank_slot]['Quantity'],
										'char_item_storage_tooltip' => tooltip($bank['Contents'][$bank_slot]['Tooltip'])
										);
											
				tep_db_perform(TABLE_CHAR_ITEM_STORAGE, $sql_data_array);
			}
		}
		
		foreach(array_keys($bank) as $bank_bag_name) {
			if ($bank_bag_name != 'Contents' && substr($bank_bag_name, 0, 3) == 'Bag') {
				$bank_bag = $bank[$bank_bag_name];
				
				$sql_data_array = array('game_char_id' => $game_char_id,
										'game_char_history_id' => $game_char_history_id,
										'char_item_name' => $bank[$bank_bag_name]['Name'],
										'char_item_parent' => "bank".$bank_bag_name,
										'char_item_slot_available' => $bank[$bank_bag_name]['Slots'],
										'char_item_color' => $bank[$bank_bag_name]['Color'],
										'char_item' => $bank[$bank_bag_name]['Item'],
										'char_item_texture' => $bank[$bank_bag_name]['Texture'],
										'char_item_tooltip' => tooltip($bank[$bank_bag_name]['Tooltip'])
										);
										
				tep_db_perform(TABLE_CHAR_ITEM_HISTORY, $sql_data_array);
				$char_item_history_id_bank_bag = tep_db_insert_id();
				
				if (isset($bank_bag['Contents'])) {
					foreach(array_keys($bank_bag['Contents']) as $slot_num) {
						$slot = $bank_bag['Contents'][$slot_num];
						
						$sql_data_array = array('char_item_history_id' => $char_item_history_id_bank_bag,
												'char_item_storage_name' => $slot['Name'],
												'char_item_storage_slot' => $slot_num,
												'char_item_storage_color' => $slot['Color'],
												'char_item_storage' => $slot['Item'],
												'char_item_storage_texture' => $slot['Texture'],
												'char_item_storage_quantity' => $slot['Quantity'],
												'char_item_storage_tooltip' => tooltip($slot['Tooltip'])
												);
												
						tep_db_perform(TABLE_CHAR_ITEM_STORAGE, $sql_data_array);
					}
				}
			}
		}
	}
	
	if (isset($data['Inventory'])) { // Start Inventory
		$inv = $data['Inventory'];
		foreach(array_keys($inv) as $bag_name) {
			$bag = $inv[$bag_name];
			
			$sql_data_array = array('game_char_id' => $game_char_id,
									'game_char_history_id' => $game_char_history_id,
									'char_item_name' => $inv[$bag_name]['Name'],
									'char_item_parent' => $bag_name,
									'char_item_slot_available' => $inv[$bag_name]['Slots'],
									'char_item_color' => $inv[$bag_name]['Color'],
									'char_item' => $inv[$bag_name]['Item'],
									'char_item_texture' => $inv[$bag_name]['Texture'],
									'char_item_tooltip' => tooltip($inv[$bag_name]['Tooltip'])
									);
									
			tep_db_perform(TABLE_CHAR_ITEM_HISTORY, $sql_data_array);
			$char_item_history_id = tep_db_insert_id();
			
			if (isset($bag['Contents'])) { // Start In
				foreach(array_keys($bag['Contents']) as $slot_name) {
					$slot = $bag['Contents'][$slot_name];
					
					$sql_data_array = array('char_item_history_id' => $char_item_history_id,
											'char_item_storage_name' => $slot['Name'],
											'char_item_storage_slot' => $slot_name,
											'char_item_storage_color' => $slot['Color'],
											'char_item_storage' => $slot['Item'],
											'char_item_storage_texture' => $slot['Texture'],
											'char_item_storage_quantity' => $slot['Quantity'],
											'char_item_storage_tooltip' => tooltip($slot['Tooltip'])
											);
											
					tep_db_perform(TABLE_CHAR_ITEM_STORAGE, $sql_data_array);
				}
			}
		}
	} // End Inventory
	
	// Start Skill
	if (isset($data['Skills'])) {
		foreach (array_keys($data['Skills']) as $skill_type) {
			$sub_skill = $data['Skills'][$skill_type];
			$order = $sub_skill['Order'];
			
			foreach (array_keys($sub_skill) as $skill_name) {
				if($skill_name != 'Order') {
					$sql_data_array = array('game_char_id' => $game_char_id,
											'game_char_history_id' => $game_char_history_id,
											'char_skill_type' => $skill_type,
											'char_skill_name' => $skill_name,
											'char_skill_order' => $order,
											'char_skill_level' => $sub_skill[$skill_name]
											);
											
					tep_db_perform(TABLE_CHAR_SKILL_HISTORY, $sql_data_array);
				}
			}
		}
	} // End Skill
	
	// Start Quest
	if (isset($data['Quests'])) {
		foreach(array_keys($data['Quests']) as $quest_location) {
		 	$quest_header = $data['Quests'][$quest_location];
		 	$order = $quest_header['Order'];
		 	
		 	foreach( array_keys( $quest_header ) as $quest ) {
		 		if( $quest != 'Order' ) {
		 			$sql_data_array = array('game_char_id' => $game_char_id,
		 									'game_char_history_id' => $game_char_history_id,
											'char_quest_location' => $quest_location,
											'char_quest_order' => $quest,
											'char_quest_level' => $order
											);
											
					tep_db_perform(TABLE_CHAR_QUEST_HISTORY, $sql_data_array);
		 		}
		 	}
		}
	} // End Quest
	
	// Start Honor
	if (isset($data['Honor'])) {
		$sql_data_array = array('game_char_id' => $game_char_id,
								'game_char_history_id' => $game_char_history_id,
								'current_honor_description' => $data['Honor']['Current']['Description'],
								'current_honor_progress' => $data['Honor']['Current']['Progress'],
								'current_honor' => $data['Honor']['Current']["HonorPoints"],
								'current_honor_arena' => $data['Honor']['Current']["ArenaPoints"],
								'current_honor_texture' => $data['Honor']['Current']['Icon'],
								'current_honor_rank' => $data['Honor']['Current']['Rank'],
								'yesterday_honorable_kill' => $data['Honor']['Yesterday']['HK'],
								'yesterday_dishonorable_kill' => $data['Honor']['Yesterday']['DK'],
								'yesterday_honor_contribution' => $data['Honor']['Yesterday']['Contribution'],
								'this_week_honorable_kill' => $data['Honor']['ThisWeek']['HK'],
								'this_week_honor_contribution' => $data['ThisWeek']['Contribution'],
								'last_week_honorable_kill' => $data['Honor']['LastWeek']['HK'],
								'last_week_dishonorable_kill' => $data['Honor']['LastWeek']['DK'],
								'last_week_honor_rank' => $data['Honor']['LastWeek']['Rank'],
								'last_week_honor_contribution' => $data['Honor']['LastWeek']['Contribution'],
								'life_time_honor_name' => $data['Honor']['Lifetime']['Name'],
								'life_time_honorable_kill' => $data['Honor']['Lifetime']['HK'],
								'life_time_dishonorable_kill' => $data['Honor']['Lifetime']['DK'],
								'life_time_rank' => $data['Honor']['Lifetime']['Rank'],
								'session_honorable_kill' => $data['Honor']['Session']['HK'],
								'session_dishonorable_kill' => $data['Honor']['Session']['DK']
								);
	
							
		tep_db_perform(TABLE_CHAR_HONOR_HISTORY, $sql_data_array);
	} // End Honor
	
	// Start Reputation
	if (isset($data['Reputation'])) {
		$sql_data_array = array('game_char_id' => $game_char_id,
								'game_char_history_id' => $game_char_history_id
								);
								
		tep_db_perform(TABLE_CHAR_REPUTATION_HISTORY, $sql_data_array);
		
		$char_reputation_history_id = tep_db_insert_id();
		
		foreach ($data['Reputation'] as $char_reputation_detail_category => $char_reputation_detail_name_value_array) {
			if ($char_reputation_detail_category != 'Count') {
				foreach ($char_reputation_detail_name_value_array as $char_reputation_detail_name => $char_reputation_detail_name_value_array) {
					echo $char_reputation_detail_name . '<br>';
					
					$sql_data_array = array('char_reputation_history_id' => $char_reputation_history_id,
											'char_reputation_detail_category' => $char_reputation_detail_category,
											'char_reputation_detail_name' => $char_reputation_detail_name,
											'char_reputation_detail_standing' => $char_reputation_detail_name_value_array['Standing'],
											'char_reputation_detail_value' => $char_reputation_detail_name_value_array['Value'],
											'char_reputation_detail_at_war' => $char_reputation_detail_name_value_array['AtWar']
											);
					
					tep_db_perform(TABLE_CHAR_REPUTATION_DETAIL, $sql_data_array);
				}
			}
		}
	}
}

function tooltip($tipdata) {
	$tooltip = "";
	if( !is_array( $tipdata ) ) {
		$tipdata = explode( "<br>", $tipdata );
	}

	foreach( $tipdata as $tip ) {
		$tooltip .= $tip . "\n";
	}
	
	return $tooltip;
}

?>