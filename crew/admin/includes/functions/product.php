<?
function tep_update_follow_product_price($products_id, $zero_product_price, $request_arr, $log_object) {
	$old_product_price_array = array();
	
	// Actual Product's Other Prices
	$products_test_select = "SELECT products_currency_prices_code, products_currency_prices_value
                      		 FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . "
					  		 WHERE products_id = " . tep_db_input($products_id) . "";
	$products_test_result = tep_db_query($products_test_select);
	while ($products_test_row = tep_db_fetch_array($products_test_result)) {
		$old_product_price_array[$products_test_row['products_currency_prices_code']] = $products_test_row['products_currency_prices_value'];
	}
	
	// Actual Product's Base Price
	$follow_actual_product_price_data_array = array('products_price' => tep_db_input($request_arr["products_price"]),
													'products_base_currency' => tep_db_input($request_arr["products_base_currency"]));
	
	$follow_product_id_select_sql = "	SELECT products_id
										FROM " . TABLE_PRODUCTS_FOLLOW_PRICE . "
										WHERE follow_products_id = '".tep_db_input($products_id)."'";
	$follow_product_id_result_sql = tep_db_query($follow_product_id_select_sql);
	
	while($follow_product_id_row = tep_db_fetch_array($follow_product_id_result_sql)) {
		// Update Bundle Product's Base Price
		tep_db_perform(TABLE_PRODUCTS, $follow_actual_product_price_data_array, 'update', "products_id = '" . (int)$follow_product_id_row['products_id'] . "'");
		
		$product_currencies_price_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_CURRENCY_PRICES . " 
												WHERE products_id = '" . tep_db_input($follow_product_id_row['products_id']) . "'";
		tep_db_query($product_currencies_price_delete_sql);
		
		if ($request_arr['products_base_currency'] . ' ' . $request_arr['products_price'] != $request_arr['hidden_products_base_currency'] . ' ' . $request_arr['hidden_products_price'] ) {
			$log_object->insert_log($follow_product_id_row['products_id'], 'products_price', $request_arr['hidden_products_base_currency'] . ' ' . $request_arr['hidden_products_price'], $request_arr['products_base_currency']. ' ' . number_format($request_arr['products_price'],6,'.',''), LOG_PRICE_ADJUST, $request_arr["user_comment"], $_SESSION['login_email_address']);
		}
		
		if (!$zero_product_price) {	// Rules (1)
			$existed_product_currency_array = array($request_arr['products_base_currency']);
			$new_product_price_array = array();
			
			$total_products_cur_code = count($request_arr['products_cur_code']);
			
			for ($cur_cnt=0; $cur_cnt < $total_products_cur_code; $cur_cnt++) {
				if (tep_not_null($request_arr['products_cur_code'][$cur_cnt]) && !in_array($request_arr['products_cur_code'][$cur_cnt], $existed_product_currency_array)) {
					if ($request_arr['products_cur_price'][$cur_cnt] > 0) {	// Rules (2)
						$existed_product_currency_array[] = $request_arr['products_cur_code'][$cur_cnt];
						$new_product_price_array[$request_arr['products_cur_code'][$cur_cnt]] = $request_arr['products_cur_price'][$cur_cnt];
						
						$product_currency_data_array = array(	'products_id' => $follow_product_id_row['products_id'],
				  												'products_currency_prices_code' => $request_arr['products_cur_code'][$cur_cnt],
				  												'products_currency_prices_value' => $request_arr['products_cur_price'][$cur_cnt]
				  											);												
						tep_db_perform(TABLE_PRODUCTS_CURRENCY_PRICES, $product_currency_data_array);
					}
				}
			}
			
			$old_and_new_product_price_array = array_merge($old_product_price_array, $new_product_price_array);
			$log_product_old_other_price = $log_product_new_other_price = '';
			
			foreach ($old_and_new_product_price_array as $merged_cur_key => $merged_cur_value) {
				if (isset($old_product_price_array[$merged_cur_key]) && isset($new_product_price_array[$merged_cur_key])) {
					// Old Record and New Record still existing, Check for price for next
					$log_product_old_other_price = $merged_cur_key.' '.number_format($old_product_price_array[$merged_cur_key],6,'.','');
					$log_product_new_other_price = $merged_cur_key.' '.number_format($new_product_price_array[$merged_cur_key],6,'.','');
					
					if ($log_product_old_other_price != $log_product_new_other_price) {
						$log_object->insert_log($follow_product_id_row['products_id'], 'products_other_price', $log_product_old_other_price, $log_product_new_other_price, LOG_PRICE_OTHER_ADJUST, $request_arr["user_comment"], $_SESSION['login_email_address'], false);
					}
				} else if (isset($old_product_price_array[$merged_cur_key])) {
					// Old Record has been removed
					$log_product_old_other_price = $merged_cur_key.' '.number_format($old_product_price_array[$merged_cur_key],6,'.','');
					$log_product_new_other_price = 'Price Deleted';
					
					$log_object->insert_log($follow_product_id_row['products_id'], 'products_other_price', $log_product_old_other_price, $log_product_new_other_price, LOG_PRICE_OTHER_ADJUST, $request_arr["user_comment"], $_SESSION['login_email_address'], false);
				} else {
					// New Record has been added
					$log_product_old_other_price = 'New Price';
					$log_product_new_other_price = $merged_cur_key.' '.number_format($new_product_price_array[$merged_cur_key],6,'.','');
					
					$log_object->insert_log($follow_product_id_row['products_id'], 'products_other_price', $log_product_old_other_price, $log_product_new_other_price, LOG_PRICE_OTHER_ADJUST, $request_arr["user_comment"], $_SESSION['login_email_address'], false);
				}
			}
		}
	}
}
?>