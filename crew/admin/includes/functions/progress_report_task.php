<?
function tep_get_time_diff($orders_products_id) {
	$time_diff_select_sql = "SELECT SEC_TO_TIME( (TO_DAYS(now())*24*3600+TIME_TO_SEC(now())) - (TO_DAYS(supplier_tasks_time_reference)*24*3600+TIME_TO_SEC(supplier_tasks_time_reference)) ) AS time FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " WHERE orders_products_id ='" . (int)$orders_products_id . "'";
	$time_diff_result_sql = tep_db_query($time_diff_select_sql);
	$time_diff_row = tep_db_fetch_array($time_diff_result_sql);
	
	$time_array = explode(':', $time_diff_row['time']);
	
	$hour = (int)$time_array[0];
	$min = ((int)$time_array[0] * 60) + $time_array[1];
	
	return $time_diff_array = array('hour_format' => $hour, 'minute_format' => $min);
}

function tep_get_trade_mail_log($order_id, $type, $mode) {
	$amount = 0;
	
	switch($mode) {
		case 'send':
			$game_char_log_send_select_sql = "	SELECT game_char_log_send, game_char_log_sender, game_char_log_login_as FROM " . TABLE_GAME_CHAR_LOG . " WHERE game_char_log_orders_id = '" . (int)$order_id . "' AND game_char_log_login_as = game_char_log_sender";
			$game_char_log_send_result_sql = tep_db_query($game_char_log_send_select_sql);
			while ($game_char_log_send_row = tep_db_fetch_array($game_char_log_send_result_sql)) {
				$outgoing_array = explode(':~:', $game_char_log_send_row['game_char_log_send']);
				
				switch ($type) {
					case 'currency':
						if (isset($outgoing_array[0])) {
							$amount += (int)$outgoing_array[0];
						}
						break;
					case 'item':
						if (isset($outgoing_array[1])) {
							$item_array = explode(',', $outgoing_array[1]);
							
							for ($item_array_size = 0; $item_array_size < sizeof($item_array); $item_array_size++) {
								if (tep_not_null($item_array[$item_array_size])) {
									$amount += sizeof($item_array);
								}
							}
						}
						break;
				}
			}
			break;
		case 'receive':
			$game_char_log_receive_select_sql = "	SELECT game_char_log_receive, game_char_log_receiver, game_char_log_login_as FROM " . TABLE_GAME_CHAR_LOG . " WHERE game_char_log_orders_id = '" . (int)$order_id . "' AND game_char_log_login_as = game_char_log_receiver";
			$game_char_log_receive_result_sql = tep_db_query($game_char_log_receive_select_sql);
			while ($game_char_log_receive_row = tep_db_fetch_array($game_char_log_receive_result_sql)) {
				$incoming_array = explode(':~:', $game_char_log_receive_row['game_char_log_receive']);
				
				switch ($type) {
					case 'currency':
						if (isset($incoming_array[0])) {
							$amount += $incoming_array[0];
						}
						break;
					case 'item':
						if (isset($incoming_array[1])) {
							$item_array = explode(',', $incoming_array[1]);
							
							for ($item_array_size = 0; $item_array_size < sizeof($item_array); $item_array_size++) {
								if (tep_not_null($item_array[$item_array_size])) {
									$amount += sizeof($item_array);
								}
							}
						}
						break;
				}
			}
			break;
	}
	return $amount;
}

function tep_get_cp_password($products_id, $comments, $view_password) {
	$comment_str = '';
	
	$pattern = '/^(?:#sys#)(Account\s)?(Password:\s)((.*)+)/';
	$comments_array = explode ("\n", $comments);
	
	for ($comments_array_count = 0; $comments_array_count < sizeof($comments_array); $comments_array_count++) {
		if ($view_password) {
			$replacement = '$1$2$3';
		} else {
			$replacement = '$1$2xxxxxx --> xxxxxx';
		}
		
		$comment_str .= preg_replace($pattern, $replacement, $comments_array[$comments_array_count]) . '<br>';
	}
	
	return $comment_str;
}
?>