<?php

/*
  $Id: sessions.php,v 1.10 2015/01/02 08:59:05 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

if (STORE_SESSIONS == 'mysql') {
    if (!$SESS_LIFE = get_cfg_var('session.gc_maxlifetime')) {
        //$SESS_LIFE = 1440;
        //$SESS_LIFE = 3600; //1 hours
        $SESS_LIFE = 2700; //45 minutes
    }

    function _sess_open($save_path, $session_name) {
        return true;
    }

    function _sess_close() {
        return true;
    }

    function _sess_read($key) {
        $qid = tep_db_query("select value from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($key) . "' and expiry > '" . time() . "'");

        $value = tep_db_fetch_array($qid);
        if ($value['value']) {
            return $value['value'];
        }

        return false;
    }

    function _sess_write($key, $val) {
        global $SESS_LIFE;

        $expiry = time() + $SESS_LIFE;
        $value = $val;

        $qid = tep_db_query("select count(*) as total from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($key) . "'");
        $total = tep_db_fetch_array($qid);

        if ($total['total'] > 0) {
            return tep_db_query("update " . TABLE_SESSIONS . " set expiry = '" . tep_db_input($expiry) . "', value = '" . tep_db_input($value) . "' where sesskey = '" . tep_db_input($key) . "'");
        } else {
            return tep_db_query("insert into " . TABLE_SESSIONS . " values ('" . tep_db_input($key) . "', '" . tep_db_input($expiry) . "', '" . tep_db_input($value) . "')");
        }
    }

    function _sess_destroy($key) {
        return tep_db_query("delete from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($key) . "'");
    }

    function _sess_gc($maxlifetime) {
        tep_db_query("delete from " . TABLE_SESSIONS . " where expiry < '" . time() . "'");
        return true;
    }

    session_set_save_handler('_sess_open', '_sess_close', '_sess_read', '_sess_write', '_sess_destroy', '_sess_gc');
}

function tep_session_start() {
    return session_start();
}

function tep_session_register($name, $value = '') {
    global $$name;
    
    $val = is_null($$name) ? $value : $$name;
    $_SESSION[$name] = $val;
    $$name = &$_SESSION[$name];
    
    return true;
}

function tep_session_is_registered($variable) {
    return isset($_SESSION[$variable]);
}

/*	This function does not unset the corresponding global variable for $variable, 
  it only prevents the variable from being saved as part of the session.
  You must call unset() to remove the corresponding global variable.
 */

function tep_session_unregister($variable) {
    unset($_SESSION[$variable]);
    return true;
}

function tep_session_id($sessid = '') {
    if ($sessid != '') {
        return session_id($sessid);
    } else {
        return session_id();
    }
}

function tep_session_name($name = '') {
    if ($name != '') {
        return session_name($name);
    } else {
        return session_name();
    }
}

function tep_session_close() {
    if (function_exists('session_close')) {
        return session_close();
    }
}

function tep_session_destroy() {
    return session_destroy();
}

function tep_session_save_path($path = '') {
    if ($path != '') {
        return session_save_path($path);
    } else {
        return session_save_path();
    }
}

function tep_session_active_admin() {
    $active_admin_array = array();
    if (STORE_SESSIONS == 'mysql') {
        if (PHP_VERSION < 4) {
            $admin_lgin_id_pattern = "/(login_id)(\[)[^:]*?(:)(\d+)(:)(\")([^:]+)(\")/is";
        } else {
            $admin_lgin_id_pattern = "/(?:login_id)(?:\|)[^:]*?(?::)(\d+)(?::)(?:\")([^:]+)(?:\")/is";
        }

        $active_session_select_sql = "SELECT value FROM " . TABLE_SESSIONS . " WHERE expiry > '" . time() . "' and value REGEXP \"(login_id)[\[|\|]\" ";
        $active_session_result_sql = tep_db_query($active_session_select_sql);
        while ($active_session_row = tep_db_fetch_array($active_session_result_sql)) {
            if (preg_match($admin_lgin_id_pattern, $active_session_row["value"], $regs)) {
                $acquired_admin_id = (int) $regs[2];
                if (strlen($acquired_admin_id) == $regs[1]) {
                    $active_admin_array[] = $acquired_admin_id;
                }
            }
        }
    }
    return $active_admin_array;
}

function tep_session_active_customer($customer_id) {
    include_once(DIR_WS_CLASSES . 'curl.php');

    $curl_obj = new curl();
    $data = array(
        'merchant' => HTTP_SHASSO_CLIENT_ID,
        'signature' => md5($customer_id . "|" . HTTP_SHASSO_CLIENT_SECRET),
        'cid' => $customer_id,
    );
    $curl_resp = $curl_obj->curl_post(HTTP_SHASSO_PORTAL . "/sso/onlineStatus", $data);
    $result = json_decode($curl_resp, true);

    return (isset($result["status"]) ? $result["status"] : false);
}

?>
