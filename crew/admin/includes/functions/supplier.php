<?
/*
  	$Id: supplier.php,v 1.36 2013/11/01 10:14:38 chingyen Exp $

  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue

  	Released under the GNU General Public License
*/

function tep_show_purchase_status($sup_grp_id) {
	$show_purchase_status_select_sql = "SELECT show_products_purchase_demand_status as show_status
										FROM " . TABLE_SUPPLIER_GROUPS . "
										WHERE supplier_groups_id = '" . (int)$sup_grp_id . "'";
	$show_purchase_status_result_sql = tep_db_query($show_purchase_status_select_sql);
	$show_purchase_status_row = tep_db_fetch_array($show_purchase_status_result_sql);

	return ((int)$show_purchase_status_row["show_status"] == 1);
}

function tep_get_active_suppliers_count($sup_grp_id) {
	$active_supplier_count_sql = "	SELECT COUNT(supplier_id) AS active_supplier
									FROM " . TABLE_SUPPLIER . "
									WHERE supplier_groups_id = '" . (int)$sup_grp_id . "' AND supplier_status=1";
	$active_supplier_result_sql = tep_db_query($active_supplier_count_sql);
	$active_supplier_row = tep_db_fetch_array($active_supplier_result_sql);

	return (int)$active_supplier_row["active_supplier"];
}

function tep_get_supplier_pricing_setting($sup_grp_id, $list_id) {
	$setting_array = array();

	$pricing_setting_select_sql = "SELECT supplier_pricing_setting_key, supplier_pricing_setting_value FROM " . TABLE_SUPPLIER_PRICING_SETTING . " WHERE supplier_groups_id = '" . (int)$sup_grp_id . "' AND products_purchases_lists_id = '" . (int)$list_id . "'";
	$pricing_setting_result_sql = tep_db_query($pricing_setting_select_sql);

	while ($pricing_setting_row = tep_db_fetch_array($pricing_setting_result_sql)) {
		$setting_array[$pricing_setting_row["supplier_pricing_setting_key"]] = $pricing_setting_row["supplier_pricing_setting_value"];
	}

	return $setting_array;
}

function tep_get_previously_restocked_qty($product_id, $sup_grp_id, $list_id) {
	$list_info_select_sql = "SELECT products_purchases_lists_reference_date FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id = '" . (int)$list_id . "'";
	$list_info_result_sql = tep_db_query($list_info_select_sql);
  	$list_info_row = tep_db_fetch_array($list_info_result_sql);

	$restock_qty_select_sql = "	SELECT SUM(solp.products_received_quantity) AS restock_qty
								FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol
								INNER JOIN " . TABLE_SUPPLIER . " AS s
									ON (sol.suppliers_id=s.supplier_id AND s.supplier_groups_id='".(int)$sup_grp_id."')
								INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp
									ON (sol.supplier_order_lists_id=solp.supplier_order_lists_id AND solp.supplier_order_lists_type=2 AND solp.products_id='".(int)$product_id."')
								WHERE DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT('".(tep_not_null($list_info_row["products_purchases_lists_reference_date"]) ? trim($list_info_row["products_purchases_lists_reference_date"]) : date('Y-m-d H:i:s'))."', '%Y-%m-%d %H:%i:%s')
									AND sol.supplier_order_lists_status IN (2, 3)";
	$restock_qty_result_sql = tep_db_query($restock_qty_select_sql);
	$restock_qty_row = tep_db_fetch_array($restock_qty_result_sql);

	return (int)$restock_qty_row["restock_qty"];
}

function tep_get_suggested_max_purchase_qty($product_id, $setting_array) {
    $total_days_inventory = $setting_array['ppls_inventory_days'];
    if (!$total_days_inventory>0) {
        return 0;
    }

    $suggested_max_purchase_qty = 0;
    $product_total_sales_qty = 0;
    $suggested_max_purchase_qty = 0;

    $start_date = $setting_array['ppls_sales_start_date'];
    $end_date = $setting_array['ppls_sales_end_date'];
  	
	$days_in_range = tep_day_diff($start_date, $end_date);
	
	if ($days_in_range <= 0) {
		return 0;
	}
	
	$product_total_sales_qty_select_sql = "	SELECT SUM(op.products_quantity) AS total_prod_qty
									        FROM " . TABLE_ORDERS . " AS o
									        INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON (o.orders_id=op.orders_id AND o.orders_status = '3')
									        WHERE op.products_id = '".$product_id."'
									        AND o.date_purchased >= '".$start_date."'
									        AND o.date_purchased <= '".$end_date."' ";
    $product_total_sales_qty_result_sql = tep_db_query($product_total_sales_qty_select_sql);
    
    while ($product_total_sales_qty_row = tep_db_fetch_array($product_total_sales_qty_result_sql)) {
        $product_total_sales_qty += $product_total_sales_qty_row['total_prod_qty'];
    }
   	
   	$suggested_max_purchase_qty = ceil(($product_total_sales_qty/$days_in_range) * $total_days_inventory);
	
   	return $suggested_max_purchase_qty;
}

function tep_order_lock($buyback_request_group_id, $orders_log_system_messages, $orders_log_admin_id) {
	$log_object = new log_files($orders_log_admin_id);
	$log_object->set_log_table(TABLE_ORDERS_LOG_TABLE);
	
	if ($orders_log_system_messages == ORDERS_LOG_UNLOCK_OWN_ORDER) {
		$unlocking_orders_sql = "DELETE FROM " . TABLE_LOCKING . " WHERE locking_trans_id = '" . tep_db_input($buyback_request_group_id) . "' AND locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "'";
		tep_db_query($unlocking_orders_sql);
	} else {
		$locking_by_select_sql = "	SELECT locking_by FROM " . TABLE_LOCKING . " 
									WHERE locking_trans_id = '" . tep_db_input($buyback_request_group_id) . "' 
										AND locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "'";
		$locking_by_result_sql = tep_db_query($locking_by_select_sql);
		
		if (tep_db_num_rows($locking_by_result_sql) < 1) {
			$buyback_status_id_select_sql = "	SELECT buyback_status_id 
												FROM " . TABLE_BUYBACK_REQUEST_GROUP . " 
												WHERE buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "'";
			$buyback_status_id_result_sql = tep_db_query($buyback_status_id_select_sql);
			$buyback_status_id_row = tep_db_fetch_array($buyback_status_id_result_sql);
			
			if ($buyback_status_id_row["buyback_status_id"] == 1) {
				$from_time = $HTTP_GET_VARS['from_time'];
				
				$check_within_locking_select_sql = "	SELECT orders_log_id 
														FROM " . TABLE_ORDERS_LOG_TABLE . " 
														WHERE orders_log_orders_id = '" . tep_db_input($buyback_request_group_id) . "' 
															AND DATE_FORMAT(orders_log_time, '%Y-%m-%d %H:%i:%s') >= '" . tep_db_input($from_time) . "' 
															AND orders_log_admin_id <> '" . tep_db_input($_SESSION['login_id']) . "' 
															AND orders_log_filename = '" . tep_db_input(FILENAME_BUYBACK_REQUESTS) . "'";
				$check_within_locking_result_sql = tep_db_query($check_within_locking_select_sql);
				
				if (tep_db_num_rows($check_within_locking_result_sql) < 1) {	// someone lock and unlock before you manage to lock it.
					$locking_data_array = array('locking_trans_id' => $buyback_request_group_id,
												'locking_table_name' => TABLE_BUYBACK_REQUEST_GROUP,
												'locking_by' => $_SESSION['login_id'],
												'locking_from_ip' => getenv("REMOTE_ADDR"),
												'locking_datetime' => 'now()'
												);
					
					tep_db_perform(TABLE_LOCKING, $locking_data_array);
				}
			}
		}
	}
	
	$log_object->insert_orders_log($buyback_request_group_id, $orders_log_system_messages, FILENAME_BUYBACK_REQUESTS_INFO);
}

function tep_cancel_expirable_buyback_request() {
	global $language, $languages_id;
	/*****************************************************************************************
		Buyback Order Expiration Cancellation Task
		Note: If adjusting intervals, adjust auto-cancellation notice in Buyback List as well.
	*******************************************************************************************/
	
	$buyback_data_array = array();
	
	$expired_pending_buyback_order_select_sql = "	SELECT br.orders_id, br.buyback_request_group_id, br.buyback_dealing_type, brg.customers_id, 
													brg.buyback_request_group_date, brg.buyback_request_group_site_id, brg.buyback_request_order_type, l.locking_by, 
													SUM(br.buyback_quantity_received) AS total_recv_qty
													FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
														ON (brg.buyback_request_group_id=br.buyback_request_group_id) 
													LEFT JOIN " . TABLE_LOCKING . " AS l 
														ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
													WHERE brg.buyback_status_id=1
														AND ( (br.buyback_quantity_confirmed <= 0 AND 
																	( brg.buyback_request_group_expiry_date > '0000-00-00 00:00:00'
																  		AND brg.buyback_request_group_expiry_date <= DATE_SUB(NOW(), INTERVAL 10 MINUTE) )
															   )
															) 
													GROUP BY br.buyback_request_group_id
													HAVING total_recv_qty <= 0";
	$expired_pending_buyback_order_result_sql = tep_db_query($expired_pending_buyback_order_select_sql);
	
	while ($expired_pending_buyback_order_row = tep_db_fetch_array($expired_pending_buyback_order_result_sql)) {
		$buyback_data_array[$expired_pending_buyback_order_row['buyback_request_group_id']] = $expired_pending_buyback_order_row;
	}
	
	if (count($buyback_data_array)) {
		$cancel_buyback_order_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . "
											SET buyback_status_id = 4,
												show_restock = 0
											WHERE buyback_request_group_id IN ('" . implode("', '", array_keys($buyback_data_array)) . "')";
		tep_db_query($cancel_buyback_order_update_sql);
        
        # Remove New Order Notification
        tep_db_query("DELETE FROM " . TABLE_ORDERS_NOTIFICATION . " WHERE orders_id = '" . $buyback_group_id . "' AND orders_type = 'BO' AND site_id = 0");

		foreach ($buyback_data_array as $buyback_group_id => $buyback_info) {
			$status_name_lang = array();
			
			if (tep_not_null($buyback_info['locking_by'])) {
				tep_order_lock($buyback_group_id, ORDERS_LOG_UNLOCK_OWN_ORDER, 'system');
			}
			
			$buyback_history_data_array = array('buyback_request_group_id' => $buyback_group_id,
						                        'buyback_status_id' => '4',
												'date_added' => 'now()',
												'customer_notified' => '0',
												'comments' => 'Auto Cancel',
												'changed_by' => 'system',
												);
			tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
			
			if ($buyback_info['buyback_dealing_type'] != 'extra_inventory') {
				$comment = '##BO##'.$buyback_group_id.'## - BO AUTO CANCELED by System';
				
		    	$comment_array = array(	'orders_id' => $buyback_info['orders_id'],
										'orders_status_id' => '0',
										'date_added' => 'now()',
										'customer_notified' => '0',
										'comments' => $comment,
										'comments_type' => '0',
										'set_as_order_remarks' => '0',
										'changed_by' => 'system'
										);
				tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $comment_array);
			}
			
			//check is vip order or not
		    if ($buyback_info['buyback_request_order_type'] == 1) {
		    	$vipGroupsObj = new vip_groups($buyback_info['customers_id']);
				$vipGroupsObj->calculate_cummulative_point('VIP_DEDUCT_ORDER_EXPIRATION_CANCELLATION_AFTER_ACCEPTED');
			}			
			
			$buyback_order_date = tep_date_long($buyback_info['buyback_request_group_date']);
			
			include_once(DIR_WS_LANGUAGES . "buyback_system.php");
			
			$customer_info_select_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, c.customers_gender, ci.customer_info_selected_language_id
											FROM " . TABLE_CUSTOMERS . " AS c 
											INNER JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
												ON (c.customers_id = ci.customers_info_id) 
											WHERE c.customers_id = '".(int)$buyback_info['customers_id']."'";
			$customer_info_result_sql = tep_db_query($customer_info_select_sql);
			
			if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
				$user_language_id = $customer_info_row['customer_info_selected_language_id'];
				
				$to_name = tep_mb_convert_encoding($customer_info_row['customers_firstname'].' '.$customer_info_row['customers_lastname'], EMAIL_CHARSET_CHINESE, CHARSET_CHINESE);
				$to_email = $customer_info_row['customers_email_address'];
				$first_name = $customer_info_row['customers_firstname'];
				$last_name = $customer_info_row['customers_lastname'];
				$gender = $customer_info_row['customers_gender'];
			}
			
			if (isset($status_name_lang[$user_language_id][4])) {
				$status_str = $status_name_lang[$user_language_id][4];
			} else {
				$status_str = tep_get_buyback_order_status_name(4, $user_language_id); //Cancel
				$status_name_lang[$user_language_id][4] = $status_str;
			}
			
			if ($user_language_id == 2) {
				include_once(DIR_WS_LANGUAGES . "email_contents_chinese.php");
				$subject_str = EMAIL_SUBJECT_BUYBACK_ORDER_UPDATED_CHINESE;
				$order_summary_title = EMAIL_TEXT_BUYBACK_ORDER_SUMMARY_CHINESE;
				
				// if chinese need to encode first
				$greeting_str = tep_get_email_greeting_chinese($first_name, $last_name, $gender);
				
				$email_body = sprintf(EMAIL_BODY_ORDER_UPDATED_CHINESE, $order_summary_title, $buyback_group_id, $buyback_order_date, $status_str, EMAIL_BODY_ORDER_CANCELLED_EXPIRED_CHINESE);				
				
				$email_subject = sprintf($subject_str, $buyback_group_id);
				
				tep_mail($to_name, $to_email, $email_subject, $greeting_str . $email_body, LOCAL_STORE_NAME, LOCAL_STORE_EMAIL_ADDRESS_CHINESE, EMAIL_CHARSET_CHINESE);
			} else {
				$subject_str = EMAIL_SUBJECT_BUYBACK_ORDER_UPDATED_ENGLISH;
				$order_summary_title = EMAIL_TEXT_BUYBACK_ORDER_SUMMARY_ENGLISH;
				
				$greeting_str = tep_get_email_greeting($first_name, $last_name, $gender);
				
				$email_body = sprintf(EMAIL_BODY_ORDER_UPDATED_ENGLISH, $order_summary_title, $buyback_group_id, $buyback_order_date, $status_str, EMAIL_BODY_ORDER_CANCELLED_EXPIRED_ENGLISH);				
				
				$email_subject = sprintf($subject_str, $buyback_group_id);
				
				tep_mail($to_name, $to_email, $email_subject, $greeting_str . $email_body, STORE_OWNER, LOCAL_STORE_EMAIL_ADDRESS_ENGLISH);
			}
		}
	}
	// End of Buyback Order Expiration Cancellation Task
	
	//delete those order allocation which expired
	$orders_products_select_sql = "SELECT DISTINCT(orders_products_id) FROM " . TABLE_VIP_ORDER_ALLOCATION;
	$orders_products_select_result = tep_db_query($orders_products_select_sql);
	while($orders_products_select_row = tep_db_fetch_array($orders_products_select_result)) {
		$vipOrderObj = new vip_order($orders_products_select_row['orders_products_id']);
		$vipOrderObj->get_orders_details();
		$vipOrderObj->check_vip_mode($vipOrderObj->order_detail['buyback_categories_id']);
		$vipOrderObj->order_request_expired();
	}
}

function tep_get_buyback_restock_char($products_id) {
	//get category
	$buyback_main_cat_array = tep_get_buyback_main_cat_info($products_id, 'product');
	
	//get restock
	$restock_character = '';
	$get_restock_charname_select_sql = "SELECT rcs.restock_character_sets_id, rcs.restock_character_sets_cat_id as server_cat_id, rcs.restock_character_sets_name,
											rci.restock_character, rcs2ctg.categories_id as main_cat_id
										FROM " . TABLE_RESTOCK_CHARACTER_SETS . " AS rcs
										INNER JOIN " . TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES . " AS rcs2ctg
											ON (rcs.restock_character_sets_id = rcs2ctg.restock_character_sets_id
												AND rcs2ctg.categories_id = '" . tep_db_input($buyback_main_cat_array['id']) . "') 
										LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci
											ON (rci.restock_character_sets_id = rcs.restock_character_sets_id)
										WHERE rci.products_id = '" . (int)$products_id. "'";
	$get_restock_charname_result_sql = tep_db_query($get_restock_charname_select_sql);
	
	if ($get_restock_charname_row = tep_db_fetch_array($get_restock_charname_result_sql)) {
		$restock_character = $get_restock_charname_row['restock_character'];
	}
	
	return $restock_character;
}

function tep_calculate_floating_available_qty($products_id) {
	$main_cat_eta_offset = tep_get_game_preset_eta($products_id);
	$floating_qty = 0;
	
//	$floating_qty_select_sql = "SELECT products_quantity, products_delivered_quantity, op.orders_products_purchase_eta 
//								FROM " . TABLE_ORDERS . " AS o 
//								INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
//									ON o.orders_id=op.orders_id 
//								WHERE op.products_id = '" . tep_db_input($products_id) . "' 
//									AND o.orders_status IN (2, 7) 
//									AND IF(	op.orders_products_purchase_eta IS NULL, 
//											0, 
//											IF(	op.orders_products_purchase_eta=-999, 
//												1, 
//												TO_DAYS(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta + " . $main_cat_eta_offset . " HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD(o.date_purchased, INTERVAL op.orders_products_purchase_eta + " . $main_cat_eta_offset . " HOUR)) > (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))
//											)
//										)";
	
	$floating_qty_select_sql = "SELECT products_quantity, products_delivered_quantity, o.date_purchased, op.orders_products_purchase_eta 
								FROM " . TABLE_ORDERS . " AS o 
								INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
									ON o.orders_id=op.orders_id 
								WHERE op.products_id = '" . tep_db_input($products_id) . "' 
									AND o.orders_status IN (2, 7)";
										
	$floating_qty_result_sql = tep_db_query($floating_qty_select_sql);
	while ($floating_qty_row = tep_db_fetch_array($floating_qty_result_sql)) {
		if (tep_not_null($floating_qty_row['orders_products_purchase_eta'])) {
			if ($floating_qty_row['orders_products_purchase_eta'] == '-999') {
				$floating_qty += $floating_qty_row['products_quantity'] - $floating_qty_row['products_delivered_quantity'];	
			} else if ($floating_qty_row['orders_products_purchase_eta'] > 0) {
				list($dp_date_str, $dp_time_str) = explode(' ', $floating_qty_row['date_purchased']);
				list($dp_yr, $dp_mth, $dp_day) = explode('-', $dp_date_str);
				list($dp_hr, $dp_min, $dp_sec) = explode(':', $dp_time_str);
				
				$current_time  = mktime(date("H"), date("i"), date("s"), date("m"), date("d"), date("Y"));
				$eta = mktime((int)$dp_hr+$main_cat_eta_offset+$floating_qty_row['orders_products_purchase_eta'], (int)$dp_min, (int)$dp_sec, $dp_mth, $dp_day, $dp_yr);
				
				if ($eta > $current_time) {
					$floating_qty += $floating_qty_row['products_quantity'] - $floating_qty_row['products_delivered_quantity'];	
				}
			}
		}
	}
	
	return (int)$floating_qty;
}

function tep_get_vip_excluded_quantity($products_id, $orders_products_id) {
	// find which quantity are not belong to the particular orders_products_id
	$first_list_quantity = 0;
    
    $buyback_prod_pending_list_qty_select_sql = "	SELECT br.buyback_request_quantity
								   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
								                		ON brg.buyback_request_group_id = br.buyback_request_group_id 
								            		WHERE br.products_id = '" . tep_db_input($products_id) . "' 
								            			AND br.orders_products_id <> '" . tep_db_input($orders_products_id) . "' 
								            			AND brg.buyback_status_id = 1 
								            			AND brg.buyback_request_order_type = 1 ";
    $buyback_prod_pending_list_qty_result_sql = tep_db_query($buyback_prod_pending_list_qty_select_sql);
    while ($buyback_prod_pending_list_qty_row = tep_db_fetch_array($buyback_prod_pending_list_qty_result_sql)) {
    	$first_list_quantity += (int)$buyback_prod_pending_list_qty_row['buyback_request_quantity'];
    }
    
    $buyback_prod_processing_list_qty_select_sql = "SELECT IF(brg.buyback_request_group_site_id > 0, br.buyback_quantity_confirmed, br.buyback_request_quantity) as first_list_qty
								   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
								                		ON brg.buyback_request_group_id = br.buyback_request_group_id 
								            		WHERE br.products_id = '" . tep_db_input($products_id) . "' 
								            			AND br.orders_products_id <> '" . tep_db_input($orders_products_id) . "' 
								            			AND brg.buyback_status_id = 2 
								            			AND brg.buyback_request_order_type = 1
								            			AND br.buyback_quantity_received = 0";
    $buyback_prod_processing_list_qty_result_sql = tep_db_query($buyback_prod_processing_list_qty_select_sql);
    while ($buyback_prod_processing_list_qty_row = tep_db_fetch_array($buyback_prod_processing_list_qty_result_sql)) {
    	$first_list_quantity += (int)$buyback_prod_processing_list_qty_row['first_list_qty'];
    }
    
	return $first_list_quantity;
}

function tep_get_game_pending_buyback($game_id, $site_id) {
	$subcat_array = array($game_id);
	tep_get_subcategories($subcat_array, $game_id);
	
	$pending_buyback_select_sql = "	SELECT COUNT(DISTINCT brg.buyback_request_group_id) AS total_pending_order 
		            				FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
		            				INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
		            					ON brg.buyback_request_group_id = br.buyback_request_group_id 
		            				INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
										ON br.products_id=p2c.products_id 
		            				WHERE brg.buyback_status_id=1 
		            					AND brg.buyback_request_group_site_id='".tep_db_input($site_id)."' 
		            					AND p2c.categories_id IN ('" . implode("', '", $subcat_array) . "') 
		            					AND p2c.products_is_link=0 ";
    $pending_buyback_result_sql = tep_db_query($pending_buyback_select_sql);
	$pending_buyback_row = tep_db_fetch_array($pending_buyback_result_sql);
	
	return $pending_buyback_row['total_pending_order'];
}

function tep_get_game_preset_eta($product_id) {
	$buyback_main_cat_info = tep_get_buyback_main_cat_info($product_id, 'product');
	
	$cat_purchase_eta_offset_select_sql = "	SELECT buyback_setting_value 
											FROM " . TABLE_BUYBACK_SETTING . " 
											WHERE buyback_setting_table_name = '" . TABLE_BUYBACK_CATEGORIES . "' 
												AND buyback_setting_key = 'ofp_purchase_eta' 
												AND buyback_setting_reference_id = '" . tep_db_input($buyback_main_cat_info['id']) . "'";
	$cat_purchase_eta_offset_result_sql = tep_db_query($cat_purchase_eta_offset_select_sql);
	if ($cat_purchase_eta_offset_row = tep_db_fetch_array($cat_purchase_eta_offset_result_sql)) {
		$main_cat_eta_offset = (int)$cat_purchase_eta_offset_row['buyback_setting_value'];
	} else {
		$main_cat_eta_offset = 0;
	}
	
	return $main_cat_eta_offset;
}

function tep_hold_cb_orders_eta($customer_id) {
	$eta_update_sql = "	UPDATE " . TABLE_ORDERS . " AS o 
						INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
							ON o.orders_id=op.orders_id 
						SET op.orders_products_purchase_eta = -999 
						WHERE o.customers_id = '" . tep_db_input($customer_id) . "' 
							AND o.orders_status IN (2, 7) 
							AND op.custom_products_type_id = 0 ";
	tep_db_query($eta_update_sql);
}

?>