<?php
/*
  $Id: upgrade.php,v 1.30 2015/05/01 00:58:39 weichen Exp $

  Developer: <PERSON> (c) 2004 SKC Ventrue

  Released under the GNU General Public License
*/

function add_new_tables ($new_tables_array, $db_tables_pool) {
	global $DBTables;
	
	foreach ($new_tables_array as $table_name=>$table_info) {
		if (!in_array($table_name, $db_tables_pool)) {
			if ($table_info["structure"]) {
				tep_db_query($table_info["structure"]);
				if ($table_info["data"]) tep_db_query($table_info["data"]);
				$DBTables[] = $table_name;
				$db_tables_pool[] = $table_name;
			}
		}
	}
}

function delete_tables ($delete_tables_array, $db_tables_pool) {
	global $DBTables;
	
	foreach ($delete_tables_array as $table_name) {
		if (in_array($table_name, $db_tables_pool)) {
			tep_db_query("DROP TABLE IF EXISTS " . $table_name);
			$table_key = array_search($table_name, $DBTables);
			unset($DBTables[$table_key]);
			$table_key = array_search($table_name, $db_tables_pool);
			unset($db_tables_pool[$table_key]);	
		}
	}
}

function get_table_fields ($table) {
	$field = array();
	$show_sql = " SHOW COLUMNS FROM " . $table ;
	$show_result = tep_db_query($show_sql);
	while ($show_row = tep_db_fetch_array($show_result)) {
		$field[] = $show_row["Field"];
	}
	return $field;
}

function get_field_info ($table, $field_name) {
	$field = array();
	$show_sql = " SHOW COLUMNS FROM " . $table . " LIKE '" . $field_name . "'";
	$show_result = tep_db_query($show_sql);
	if ($show_row = tep_db_fetch_array($show_result)) {
		$field = $show_row;
	}
	
	return $field;
}

function add_field ($add_field_set, $update_if_exists=true) {
	foreach ($add_field_set as $table=>$field_set_array) {
		foreach ($field_set_array as $ind_field) {
			$ind_field["field_name"] = trim($ind_field["field_name"]);
			$existing_fields = get_table_fields($table);
			if (!in_array($ind_field["field_name"], $existing_fields)) {
				// new field not exists in table yet!
				$alter_sql = " ALTER TABLE " . $table . 
							 " ADD " . $ind_field["field_name"] . " " . $ind_field["field_attr"] .
							 (($ind_field["add_after"] && in_array($ind_field["add_after"], $existing_fields)) ? " AFTER " . $ind_field["add_after"] : '');
				tep_db_query($alter_sql);
			} else {
				// found same field name, so just alter its structure
				if ($update_if_exists) {
					$alter_sql = " ALTER TABLE " . $table . " CHANGE " . $ind_field["field_name"] .
								 "  ". $ind_field["field_name"] . " " . $ind_field["field_attr"];
					tep_db_query($alter_sql);
				}
			}
		}
	}
}

function change_field_structure ($change_field_set) {
	foreach ($change_field_set as $table=>$field_set_array) {
		foreach ($field_set_array as $ind_field) {
			$existing_fields = get_table_fields($table);
			if (in_array($ind_field["field_name"], $existing_fields)) {
				// found the field, so alter its structure
				$alter_sql = " ALTER TABLE " . $table . " CHANGE " . $ind_field["field_name"] .
							 "  ". (isset($ind_field["field_new_name"]) && tep_not_null($ind_field["field_new_name"]) ? $ind_field["field_new_name"] : $ind_field["field_name"]) . " " . $ind_field["field_attr"];
				tep_db_query($alter_sql);
			}
		}
	}
}

function delete_field ($delete_field_set) {
	foreach ($delete_field_set as $table=>$field_set_array) {
		foreach ($field_set_array as $ind_field) {
			$existing_fields = get_table_fields($table);
			if (in_array($ind_field["field_name"], $existing_fields)) {
				// the field is exists in table
				$alter_sql = " ALTER TABLE " . $table . 
							 " DROP " . $ind_field["field_name"] ;
				tep_db_query($alter_sql);
			}
		}
	}
}

function insert_new_records ($table, $key, $records, $db_tables_pool, $fields_list='', $extra_check='') {
	foreach ($records as $unique_key=>$sql_action) {
		if (tep_not_null($extra_check)) {
			$select_sql = "	SELECT * 
							FROM " . $table . "
							WHERE " . $extra_check ;
		} else {
			$select_sql = "	SELECT * 
							FROM " . $table . "
							WHERE ".$key."='".$unique_key."'" ;
		}
		
		$result_sql = tep_db_query($select_sql);
		if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
			if ($sql_action["update"] != "") {
				if (tep_not_null($extra_check)) {
					$update_sql = "	UPDATE " . $table . "	
									SET ".$sql_action["update"]."
									WHERE " . $extra_check ;
				} else {
					$update_sql = "	UPDATE " . $table . "	
									SET ".$sql_action["update"]."
									WHERE ".$key."='".$unique_key."'" ;
				}
				tep_db_query($update_sql);
			}
		} else {	// no records found
			$insert_sql = "INSERT INTO " . $table . " " . $fields_list . "
							VALUES " . $sql_action["insert"] ;
			tep_db_query($insert_sql);
		}
	}
}

function update_records ($table, $key, $records, $db_tables_pool) {
	if (!in_array($table, $db_tables_pool)) return;
	
	foreach ($records as $unique_key=>$sql_action) {
		$select_sql = "	SELECT * 
						FROM " . $table . "
						WHERE ".$key."='".$unique_key."'" ;
		$result_sql = tep_db_query($select_sql);
		if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
			if ($sql_action["update"] != "") {
				$update_sql = "	UPDATE " . $table . "	
								SET ".$sql_action["update"]."
								WHERE ".$key."='".$unique_key."'" ;
				tep_db_query($update_sql);
			}
		}
	}
}

function delete_records ($table, $key, $records, $db_tables_pool) {
	if (!in_array($table, $db_tables_pool)) return;
	
	foreach ($records as $unique_key=>$requirement) {
		$select_sql = "	SELECT * 
						FROM " . $table . "
						WHERE ".$key."='".$unique_key."'" ;
		$result_sql = tep_db_query($select_sql);
		if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
			if ($requirement["unique"] == "1") {
				if (tep_db_num_rows($result_sql) == 1) {
					$delete_sql = "	DELETE FROM " . $table . " 
									WHERE ".$key."='".$unique_key."' " . (isset($requirement["extra_where"]) && tep_not_null($requirement["extra_where"]) ? ' AND ' . $requirement["extra_where"] : '');
					tep_db_query($delete_sql);
				}
			} else {
				$delete_sql = "	DELETE FROM " . $table . " 
									WHERE ".$key."='".$unique_key."' " . (isset($requirement["extra_where"]) && tep_not_null($requirement["extra_where"]) ? ' AND ' . $requirement["extra_where"] : '');
				tep_db_query($delete_sql);
			}
		}
	}
}

function advance_update_records ($update_field_set, $db_tables_pool) {
	foreach ($update_field_set as $table=>$field_set_array) {
		if (!in_array($table, $db_tables_pool)) continue;
		
		foreach ($field_set_array as $ind_field) {
			$existing_fields = get_table_fields($table);
			if (in_array($ind_field["field_name"], $existing_fields)) {
				// the field is exists in table
				$update_sql = "	UPDATE " . $table . "	
								SET ".$ind_field["update"]."
								WHERE ".$ind_field["where_str"] . " AND 1";
				tep_db_query($update_sql);
			}
		}
	}
}

function add_index_key ($table, $index_name, $index_type, $column_to_indexes, $db_tables_pool) {
	if (!in_array($table, $db_tables_pool)) return;
	if (trim($column_to_indexes) == '' || trim($index_name) == '')	return;
	
	$existing_fields = get_table_fields($table);
	$columns_array = explode(',', $column_to_indexes);
	
	for ($i=0; $i < count($columns_array); $i++) {
		$columns_array[$i] = preg_replace('/`/', '', $columns_array[$i]);
		$columns_array[$i] = preg_replace('/(\(.*?\))/', '', $columns_array[$i]);
		
		if (!in_array(trim($columns_array[$i]), $existing_fields)) {
			return;
		}
	}
	
	$ret_keys = array();
	$key_select_sql = "SHOW KEYS FROM " . $table;
	$key_result_sql = tep_db_query($key_select_sql);
	while ($key_row = tep_db_fetch_array($key_result_sql)) {
		$ret_keys[] = $key_row;
	}
	
	if ($index_type == 'unique' || $index_type == 'index' || $index_type == 'fulltext') {
		$found_key = false;
		foreach ($ret_keys as $row) {
			if ($row["Key_name"] == $index_name) {
				$found_key = true;
				break;
			}
		}
		
		if (!$found_key) {
			$key_insert_sql = "ALTER TABLE " . $table . " ADD ". strtoupper($index_type) . " " . $index_name . " ( " . $column_to_indexes . " )";
			tep_db_query($key_insert_sql);
		}
	} else if ($index_type == 'primary') {
		$found_key = false;
		foreach ($ret_keys as $row) {
			if (strtolower($row["Key_name"]) == $index_type) {
				$found_key = true;
				break;
			}
		}
		
		if (!$found_key) {
			$key_insert_sql = "ALTER TABLE " . $table . " ADD PRIMARY KEY ( " . $column_to_indexes . " )";
			tep_db_query($key_insert_sql);
		}
	}
}

function drop_index_key ($table, $index_name, $index_type, $db_tables_pool, $column_name='') {
	if (!in_array($table, $db_tables_pool)) return;
	if (trim($index_name) == '')	return;
	
	$ret_keys = array();
	$key_select_sql = "SHOW KEYS FROM " . $table;
	$key_result_sql = tep_db_query($key_select_sql);
	while ($key_row = tep_db_fetch_array($key_result_sql)) {
		$ret_keys[] = $key_row;
	}
	
	if ($index_type == 'primary') {
		$found_key = false;
		$column_name_checking = array();
		foreach ($ret_keys as $row) {
			if (strtolower($row["Key_name"]) == $index_type) {
				if (is_array($column_name) && count($column_name) > 0) {
					$column_name_checking[] = $row["Column_name"];
				} else {
					$found_key = true;
					break;
				}
			}
		}
		
		if (!$found_key && count($column_name_checking) > 0) {
			$diff_result = array_diff($column_name_checking, $column_name);
			if (count($diff_result) > 0) {	// do nothing
				;
			} else {
				$found_key = true;
			}
		}
		
		if ($found_key) {
			$key_delete_sql = "ALTER TABLE " . $table . " DROP " . $index_name ;
			tep_db_query($key_delete_sql);
		}
	} else if ($index_type == 'index') {
		$found_key = false;
		foreach ($ret_keys as $row) {
			if (strtolower($row["Key_name"]) == $index_name) {
				$found_key = true;
			}
		}
		
		if ($found_key) {
			$key_delete_sql = "ALTER TABLE " . $table . " DROP INDEX " . $index_name ;
			tep_db_query($key_delete_sql);
		}
	}
}

function copy_field_content ($contents_info) {
	foreach ($contents_info as $info) {
		$existing_from_fields = get_table_fields($info["from_table"]);
		$existing_to_fields = get_table_fields($info["to_table"]);
		
		if (is_array($info["from_field"]) && is_array($info["to_field"])) {
			if (count($info["from_field"]) == count($info["to_field"])) {
				$non_exist_from_fields = array_diff($info["from_field"], $existing_from_fields);
				$non_exist_to_fields = array_diff($info["to_field"], $existing_to_fields);
				
				if (count($non_exist_from_fields) > 0 || count($non_exist_to_fields) > 0) {
					continue;
				} else {
					$from_and_to_field_array = array();
					for ($i=0; $i < count($info["from_field"]); $i++) {
						$from_and_to_field_array[] = $info["to_field"][$i] . ' = \'". addslashes($row_sql["' . $info["from_field"][$i] . '"]) ."\' ';
					}
					$from_and_to_field_sql = count($from_and_to_field_array) ? implode(' AND ', $from_and_to_field_array) : " 1 ";
					
					$select_sql = " SELECT $info[primary_key], " . implode(',', $info[from_field]) . " 
									FROM " . $info["from_table"] ;
					$result_sql = tep_db_query($select_sql);
					while ($row_sql = tep_db_fetch_array($result_sql)) {  // do checking
						eval("\$formatted_from_and_to_field_sql = \"$from_and_to_field_sql\";");
						$checking_sql = "	SELECT $info[foreign_key]
										  	FROM " . $info["to_table"] . "
										  	WHERE $info[foreign_key]='". $row_sql[$info[primary_key]] . "'
										  		AND " . $formatted_from_and_to_field_sql;
						
						$checking_result_sql = tep_db_query($checking_sql);
						if (tep_db_num_rows($checking_result_sql) > 0) {
							continue;	// record exists
						} else {
							if (isset($info["skip_empty_record"]) && $info["skip_empty_record"] == '1') {
								$empty_record = true;
								for ($i=0; $i < count($info["from_field"]); $i++) {
									if (tep_not_null($row_sql[$info["from_field"][$i]])) {
										$empty_record = false;
										break;
									}
								}
								
								if (!$empty_record) {
									$inserted_count = 0;
									$new_primary_key = 0;
									for ($i=0; $i < count($info["from_field"]); $i++) {
										if (tep_not_null($info["from_field"][$i])) {
											if (!$inserted_count) {
												$insert_sql = " INSERT INTO " . $info["to_table"] . " ($info[foreign_key], " . $info["to_field"][$i] . ") 
															 	VALUES (".$row_sql[$info[primary_key]]. ", '" .addslashes($row_sql[$info[from_field][$i]])."') " ;
												tep_db_query($insert_sql);
												$new_primary_key = tep_db_insert_id();
											} else {
												$update_sql = " UPDATE " . $info["to_table"] . " SET " . $info[to_field][$i] . " = '" . addslashes($row_sql[$info[from_field][$i]]) . "' WHERE " . $info["to_table_pk"] . " = '" . $new_primary_key . "'";
												tep_db_query($update_sql);
											}
											$inserted_count++;
										}
									}
								}
							} else {
								$inserted_count = 0;
								$new_primary_key = 0;
								for ($i=0; $i < count($info["from_field"]); $i++) {
									if (tep_not_null($info["from_field"][$i])) {
										if (!$inserted_count) {
											$insert_sql = " INSERT INTO " . $info["to_table"] . " ($info[foreign_key], " . $info["to_field"][$i] . ") 
														 	VALUES (".$row_sql[$info[primary_key]]. ", '" .addslashes($row_sql[$info[from_field][$i]])."') " ;
											tep_db_query($insert_sql);
											$new_primary_key = tep_db_insert_id();
										} else {
											$update_sql = " UPDATE " . $info["to_table"] . " SET " . $info[to_field][$i] . " = '" . addslashes($row_sql[$info[from_field][$i]]) . "' WHERE " . $info["to_table_pk"] . " = '" . $new_primary_key . "'";
											tep_db_query($update_sql);
										}
										$inserted_count++;
									}
								}
							}
						}
					}
				}
			}
		} else if (!is_array($info["from_field"]) && in_array($info["from_field"], $existing_from_fields)) {
			$select_sql = " SELECT $info[primary_key],$info[from_field] 
							FROM " . $info["from_table"] ;
			$result_sql = tep_db_query($select_sql);
			while ($row_sql = tep_db_fetch_array($result_sql)) {  // do checking
				$checking_sql = "	SELECT $info[foreign_key]
								  	FROM " . $info["to_table"] . "
								  	WHERE $info[foreign_key]='". $row_sql[$info[primary_key]] . "'
								  		AND $info[to_field]='". addslashes($row_sql[$info[from_field]]) ."'";
				$checking_result_sql = tep_db_query($checking_sql);
				if (tep_db_num_rows($checking_result_sql) > 0) {
					continue;
				} else {
					if (isset($info["skip_empty_record"]) && $info["skip_empty_record"] == '1') {
						if (tep_not_null($row_sql[$info["from_field"]])) {
							$insert_sql = " INSERT INTO " . $info["to_table"] . " ($info[foreign_key], $info[to_field]) 
										 	VALUES (".$row_sql[$info[primary_key]]. ", '" .addslashes($row_sql[$info[from_field]])."') " ;
							tep_db_query($insert_sql);
						}
					} else {
						$insert_sql = " INSERT INTO " . $info["to_table"] . " ($info[foreign_key], $info[to_field]) 
									 	VALUES (".$row_sql[$info[primary_key]]. ", '" .addslashes($row_sql[$info[from_field]])."') " ;
						tep_db_query($insert_sql);
					}
				}
			}
		}
	}
}

function first_version_greater($first_version, $second_version) {
	if ($first_version === $second_version)
		return false;
	
	$first_version_id_array = explode(".", $first_version);
	$second_version_id_array = explode(".", $second_version);
	
	$greater = false;
	if (count($first_version_id_array) < count($second_version_id_array)) {
		for ($i=0; $i < count($first_version_id_array); $i++) {
			if ((int)$first_version_id_array[$i] > (int)$second_version_id_array[$i]) {
				$greater = true;
				break;
			} else if ((int)$first_version_id_array[$i] < (int)$second_version_id_array[$i]) {
				$greater = false;
				break;
			}
		}
	} else {
		for ($i=0; $i < count($first_version_id_array); $i++) {
			if ($i < count($second_version_id_array)) {
				if ((int)$first_version_id_array[$i] > (int)$second_version_id_array[$i]) {
					$greater = true;
					break;
				} else if ((int)$first_version_id_array[$i] < (int)$second_version_id_array[$i]) {
					$greater = false;
					break;
				}
			} else {	
				$greater = true;
				break;
			}
		}
	}
	return $greater;
}
?>