<?
/*
  	$Id: header.php,v 1.4 2007/12/17 03:58:31 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

if ($messageStack->size > 0) {
	echo $messageStack->output();
}
?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
  	<!--tr>
  		<td align="center" background="images/header01.jpg">
  			<a href="http://www.offgamers.com"><img border="0" src="images/header02.jpg" width="245" height="81"></a>
  		</td>
  	</tr-->
  	<tr class="headerBar">
    	<td class="headerBarContent" align="left"><?=date("D M j G:i:s \G\M\T O Y");?></td>
    	<td class="headerBarContent" align="right">&nbsp;
    		<?='&nbsp; <a href="' . tep_catalog_href_link() . '" class="headerLink">' . HEADER_TITLE_ONLINE_CATALOG . '</a> &nbsp;|&nbsp; <a href="' . tep_href_link(FILENAME_DEFAULT, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_ADMINISTRATION . '</a>'; ?>&nbsp;|&nbsp;
<?
//Admin begin
//  echo '<a href="' . tep_href_link(FILENAME_DEFAULT, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_TOP . '</a>'; 
  	if (tep_session_is_registered('login_id')) {
    	echo '<a href="' . tep_href_link(FILENAME_ADMIN_ACCOUNT, '', 'SSL') . '" class="headerLink">' . HEADER_TITLE_ACCOUNT . '</a> | <a href="' . tep_href_link(FILENAME_LOGOFF, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_LOGOFF . '</a>';
  	} else {
    	echo '<a href="' . tep_href_link(FILENAME_DEFAULT, '', 'NONSSL') . '" class="headerLink">' . HEADER_TITLE_TOP . '</a>';
  	}
//Admin end
?>&nbsp;&nbsp;&nbsp;
		</td>
  	</tr>
</table>