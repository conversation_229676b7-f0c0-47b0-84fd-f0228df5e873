<HTML>

<!--------------------------------------------------------------->
<!-- Copyright (c) 2006 by <PERSON>.                     -->
<!-- For enquiries, <NAME_EMAIL>.        -->
<!-- Please keep all copyright notices below.                  -->
<!-- Original author of TreeView script is <PERSON><PERSON>.  -->
<!--------------------------------------------------------------->
<!-- This document includes the TreeView script.  The TreeView -->
<!-- script can be found at http://www.TreeView.net.  The      -->
<!-- script is Copyright (c) 2006 by <PERSON>.           -->
<!--------------------------------------------------------------->

 <HEAD>
  <STYLE>
   BODY {
     background-color: white;
     font-size: 10pt;
     font-family: verdana,helvetica}
  </STYLE>
 </HEAD>

 <BODY bgcolor="white">
  <H2>TreeView</H2>
  <P>There are many possible ways of displaying and using the links in the 
  tree, and they are all configurable by you. The four main folders in this 
  demo try to show the more relevant variations for a frame-based layout 
  (for more info see the 
  <A HREF="http://www.treeview.net/tv/instructions.asp" target=_top>instructions</A>
  on the Web site.</P>
  <P>The <B>Photos example</B> node organizes photos and maps.  You can 
  put any other type of document in the tree: HTML, ASP, movies, sounds, etc. 
  You can even fill the outliner with data from a database or list the files 
  and directories of a computer drive.</P>
  <P>The <B>3 Types of folders</B> node illustrates the two roles the folder 
  plays in the TreeView script: it is used to contain others entries, but it 
  may also be itself a link. Folders with entries inside show the +/- icon, 
  which is used to expand it or collapse it. Clicking on the folder icon also 
  expands its contents. For those "linked" folders, clicking on the folder 
  icon not only expands it but also loads a page. "Europe" and "United States" 
  in the "Photos example" are linked folders that, when clicked, load maps of 
  those continents.</P>
  <P>The <B>Targets</B> node shows the different places where the linked page 
  may be loaded: right frame, blank window, top frame (removes frameset), and 
  the self frame. Additionaly, instead of an HTTP link, you may have a node in 
  the tree that calls a JavaScript function (a <I>javascript:</I> link.)</P>
  <P>The <B>Other icons</B> node is a folder with a customized icon, and so 
  is the document inside of it. Your tree may use a custom icon uniformly (all 
  folders with same icon, for example) or apply different icons on a node-by-node 
  basis.</P>
  <P>The <B>Formats</B> node shows different ways of changing the default 
  format used for the entries' text. That is, the default font, size, and so on. 
  For the "Formats" folder, the colors were specified by embedding HTML tags 
  within the Title argument of the gLnk function.  For more information about 
  the gLnk function, see <A HREF="http://www.treeview.net/tv/instructions.asp#ADVANCEDCONFIG" 
  target=_top>Advanced Configuration</A> in the Instructions on the Web site. 
  For the "CSS Class" link, the the name was enclosed in a &lt;DIV&gt; tag that 
  points to a predefined style sheet class.</P>
  <P>These combinations are just examples of what can be done with Treeview. 
  Check the documentation and see how to set-up the tree for your particular 
  site-navigation needs.  For more information, see the 
  <A HREF="http://www.treeview.net/tv/instructions.asp" target="_top">Instructions</A>
  on the Web site.</P>

 </BODY>

</HTML>