<HTML>

<!--------------------------------------------------------------->
<!-- Copyright (c) 2006 by <PERSON>.                     -->
<!-- For enquiries, <NAME_EMAIL>.        -->
<!-- Please keep all copyright notices below.                  -->
<!-- Original author of TreeView script is <PERSON><PERSON>.  -->
<!--------------------------------------------------------------->
<!-- This document includes the TreeView script.  The TreeView -->
<!-- script can be found at http://www.TreeView.net.  The      -->
<!-- script is Copyright (c) 2006 by <PERSON>.           -->
<!--------------------------------------------------------------->

 <HEAD>

  <TITLE>TreeView Demo: JavaScript Functions</TITLE>

  <style>
    BODY {
      background-color: white;
      font-size: 10pt;
      font-family: verdana,helvetica;}
   .menuBtn {
      width:100%;}
  </style>

  <script>
   // The JavaScript for the buttons at the bottom of the left-hand frame follows.  
   // These functions expand all nodes in the tree, collapse all nodes in the tree,
   // open a specific node in the tree, or load a particular document in the 
   // right-hand frame.
   //
   // The buttons in this demo are in a separate frame both from the actual tree 
   // and from the actual content.  If you want to include the buttons in the same 
   // frame as the tree, or in the same frame as the documents, move these 
   // functions to the appropriate file.  For example, in the case of this example,
   // if you want to display the buttons on the same frame as the tree, move these 
   // functions to the demoFuncs.html file.  If you move these functions, you must
   // update the DOM paths used to access the functions and used by the functions 
   // themselves.

   // The expandTree function expands all children nodes for the specified node. 
   // The specified node must be a folder node. 
   // When the specified node is the root node, this function opens all nodes in 
   // the tree.  When the specified is not the root node, only the children of the 
   // specified node are opened.
   // Note that for some very large trees, the browser may time out when this 
   // function is used.
   function expandTree(folderObj) {
    var childObj;
    var i;

    // Open the folder
    if (!folderObj.isOpen)
      parent.treeframe.clickOnNodeObj(folderObj)

    // Call this function for all children
    for (i=0 ; i < folderObj.nChildren; i++)  {
      childObj = folderObj.children[i]
      if (typeof childObj.setState != "undefined") { // If this is a folder
        expandTree(childObj)
      }
    }
   }

   // The collapseTree function closes all nodes in the tree. 
   function collapseTree() {
    // Close all folder nodes
    parent.treeframe.clickOnNodeObj(parent.treeframe.foldersTree)
    // Restore the tree to the top level
    parent.treeframe.clickOnNodeObj(parent.treeframe.foldersTree)
   }

   // The openFolderInTree function open all children nodes of the specified 
   // node.  Note that in order to open a folder, we need to open the parent
   // folders all the way to the root.  (Of course, this does not affect 
   // selection highlight.)
   function openFolderInTree(linkID) {
    var folderObj;
    folderObj = parent.treeframe.findObj(linkID);
    folderObj.forceOpeningOfAncestorFolders();
    if (!folderObj.isOpen)
     parent.treeframe.clickOnNodeObj(folderObj);
   } 

   // The loadSynchPage function load the appropriate document, as if
   // the specified node on the tree was clicked.  This function also
   // synchronizes the frames and highlights the selection (if highlight 
   // is enabled).
   function loadSynchPage(linkID) {
    var folderObj;
    docObj = parent.treeframe.findObj(linkID);
    docObj.forceOpeningOfAncestorFolders();
    parent.treeframe.clickOnLink(linkID,docObj.link,'basefrm'); 

    // Scroll the tree to show the selected node.
    // For this function to work with frameless pages, you will need to
    // remove the following code and also probably change other code in 
    // these functions.
    if (typeof parent.treeframe.document.body != "undefined") // To handle scrolling not working with NS4
     parent.treeframe.document.body.scrollTop=docObj.navObj.offsetTop
   } 

  </script>

 </HEAD>

 <BODY BGCOLOR="#FFFFFF" leftmargin="0" topmargin="0" marginheight="0" marginwidth="0">

  <center>

   <form>
    <input type="button" onClick="javascript:expandTree(parent.treeframe.foldersTree)" value="Open All" class="menuBtn">

    <!----------------------------------------------------------->
    <!-- The next three function calls have hardcoded values   -->
    <!-- that specify the id of a node in the tree.  To figure -->
    <!-- out the node ids that you might want to use, you can: -->
    <!--  - Use node.xID (which may be a string).              -->
    <!--  - Open the tree in a browser, move your mouse over   -->
    <!--    the +/- nodes to the left of the folder names.     -->
    <!--    The node ids are shown in the star=tus bar.        -->
    <!-- Increment the numbers sequentially to get the node    -->
    <!-- ids of the "documents" inside that folder.            -->
    <!----------------------------------------------------------->

    <input type="button" onClick="javascript:collapseTree(1)" value="Close All" class="menuBtn">
    <input type="button" onClick="javascript:openFolderInTree(1)" value="Open Flags Folder" class="menuBtn">
    <input type="button" onClick="javascript:loadSynchPage(10)" value="Load Map of Europe" class="menuBtn">
   </form>
  </center>

 </BODY>
</HTML>



