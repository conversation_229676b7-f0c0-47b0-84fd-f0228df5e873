<HTML>

<!--------------------------------------------------------------->
<!-- Copyright (c) 2006 by <PERSON>.                     -->
<!-- For enquiries, <NAME_EMAIL>.        -->
<!-- Please keep all copyright notices below.                  -->
<!-- Original author of TreeView script is <PERSON><PERSON>.  -->
<!--------------------------------------------------------------->
<!-- This document includes the TreeView script.  The TreeView -->
<!-- script can be found at http://www.TreeView.net.  The      -->
<!-- script is Copyright (c) 2006 by <PERSON>.           -->
<!--------------------------------------------------------------->

 <HEAD>

  <style>
   BODY {
     background-color: white;
     font-size: 10pt;
     font-family: verdana,helvetica;}
  </style>

 </HEAD>

 <BODY BGCOLOR="#FFFFFF">

  <H3>TreeView Demo: JavaScript Functions</H3>
  <p>JavaScript functions can be used to manipulate the Treeview.  Also, 
  tree nodes can be used to call JavaScript functions.  Both cases are 
  demonstrated in this example.</p>

  <p><B>How other pages can manipulate the tree...</B></p>
  <p>The buttons in the bottom-left corner shows four common options that 
  manipulate the tree programmatically.  Download the Treeview ZIP to get 
  access to these functions and to all the code in this demo. The last 
  two buttons activate nodes in the tree. With these functions it is 
  possible to keep the tree in synch with what is happening in the right 
  frame, by opening folders and selecting documents without any actual 
  mouse click on them. Links and buttons to trigger these operations are 
  more commonly found in the right frame than in these buttons.</p>
  <p>Imagine, for example, a tree of photos.  You may want to let the 
  user load a photo by clicking on the tree or by clicking on a "Next" 
  link under the photo in the right frame. This "Next" link would not 
  only load the next photo in sequence like in a slideshow, but would 
  also select the corresponding node in the tree. You can accomplish 
  this by putting in the HREF of the "Next" link something similar to 
  what is found in the onClick parameter of the "Load Map of Europe" 
  button in this demo.</P>
  <p>Note that the "Load Map of Europe" button not only selects the node 
  and loads the image, but it also scrolls down the tree frame. To see the 
  scroll function working, we are going to take a couple of steps that are 
  only really needed in this example because of the small dimensions of 
  this tree. (You may want to first read through the whole next paragraph 
  one time, and, later, to return to this page, press "Flags and 
  maps".)</p>
  <p>Start by making the browser window really small such that, above the 
  menu, you can only see the root folder and maybe the Flags folder.
  Then click the "Open All" button, and click the "Load Map of Europe"
  button. See how the tree scrolled down to show the Europe node!</p>

  <p><B>How a node can execute a function...</B></p>
  <p>Instead of actual pages, images, and so on, the folders and 
  "documents" in the tree can be made to call JavaScript functions. 
  Three such links can be found inside the "<I>javascript:</I> links" 
  folder. To learn more about exactly what is the definition of these 
  functions and how they are called by the tree nodes, see the comments 
  in the code of these pages.</p>
  <p>One simple case where a node needs to call a function is when the 
  window for the new page should not have menu bar, status bar, and so 
  on. In this demo we created a generic function that takes the URL of 
  the page to load as an argument, we and are using it with the "Window 
  w/o bars" link.</p>
  <p>The other two links, including the final folder, simply call the 
  built-in function "alert". They show how you can pass strings to 
  functions and how it is possible for these strings to have the 
  characters " and  ' (which are reserved in JavaScript).</p>
 
 </BODY>

</HTML>