<HTML>

<!--------------------------------------------------------------->
<!-- Copyright (c) 2006 by <PERSON>.                     -->
<!-- For enquiries, <NAME_EMAIL>.        -->
<!-- Please keep all copyright notices.                        -->
<!--------------------------------------------------------------->

 <HEAD>
  <TITLE>Readme file for TreeView</TITLE>
 </HEAD>

 <BODY>
  <h3>Thank You!</h3>
  <p>Thank you for your interest in TreeView.  This ZIP file contains the files that you need to use TreeView.  It also contains all the code for the TreeView demos, so you can see exactly how each of those demos works.  To see those demos in action, check out the <A HREF="index.html">index.html</A> file.</p>

  <h3>Instructions</h3>
  <p>The most up-to-date <A HREF="http://www.treeview.net/tv/instructions.asp">instructions</A> for configuring and using TreeView are up on our Web site.</p>

  <h3>Frequently Asked Questions</h3>
  <p>Also, if you have questions, be sure to look at the information in the <A HREF="http://www.treeview.net/tv/support.asp">Support</A> page on our Web site.</p>

  <h3>Files</h3>
  <p>This ZIP file contains the following:</p>
  <table border="1">
   <tr>
    <td colspan="2"><b>TreeView Scripts:</b></td>
   </tr>
   <tr>
    <td>ftiens4.js</td>
    <td>JavaScript code that displays the tree.</td>
   </tr>
   <tr>
    <td>ua.js</td>
    <td>JavaScript code that detects the different browsers.</td>
   </tr>
   <tr>
    <td colspan="2"><b>TreeView Images:</b></td>
   </tr>
   <tr>
    <td>diffDoc.gif</td>
    <td>Image file for <img src="diffDoc.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>diffFolder.gif</td>
    <td>Image file for <img src="diffFolder.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2blank.gif</td>
    <td>Image file for <img src="ftv2blank.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2doc.gif</td>
    <td>Image file for <img src="ftv2doc.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2folderclosed.gif</td>
    <td>Image file for <img src="ftv2folderclosed.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2folderopen.gif</td>
    <td>Image file for <img src="ftv2folderopen.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2lastnode.gif</td>
    <td>Image file for <img src="ftv2lastnode.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2mnode.gif</td>
    <td>Image file for <img src="ftv2mnode.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2node.gif</td>
    <td>Image file for <img src="ftv2node.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2plastnode.gif</td>
    <td>Image file for <img src="ftv2plastnode.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2pnode.gif</td>
    <td>Image file for <img src="ftv2pnode.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td>ftv2vertline.gif</td>
    <td>Image file for <img src="ftv2vertline.gif" align="absbottom"></td>
   </tr>
   <tr>
    <td colspan="2"><b>TreeView Demos:</b></td>
   </tr>
   <tr>
    <td>demoCheckbox.html</td>
    <td>Main page for "TreeView Demo: Checkboxes and Radio Buttons for a Form"</td>
   </tr>
   <tr>
    <td>demoCheckboxLeftFrame.html</td>
    <td>Left frame for "TreeView Demo: Checkboxes and Radio Buttons for a Form"</td>
   </tr>
   <tr>
    <td>demoCheckboxNodes.js</td>
    <td>Script for "TreeView Demo: Checkboxes and Radio Buttons for a Form"</td>
   </tr>
   <tr>
    <td>demoCheckboxRightFrame.html</td>
    <td>Right frame for "TreeView Demo: Checkboxes and Radio Buttons for a Form"</td>
   </tr>
   <tr>
    <td>demoDynamic.html</td>
    <td>Main page for "TreeView Demo: Dynamic Script that Reads from a Database"</td>
   </tr>
   <tr>
    <td>demoDynamic.mdb</td>
    <td>Main page for "TreeView Demo: Dynamic Script that Reads from a Database"</td>
   </tr>
   <tr>
    <td>demoDynamicLeftFrame.asp</td>
    <td>Left frame for "TreeView Demo: Dynamic Script that Reads from a Database"</td>
   </tr>
   <tr>
    <td>demoFrameless.html</td>
    <td>Main page for "TreeView Demo: Frameless Layout"</td>
   </tr>
   <tr>
    <td>demoFramelessNodes.js</td>
    <td>Script for "TreeView Demo: Frameless Layout"</td>
   </tr>
   <tr>
    <td>demoFramelessHili.html</td>
    <td>Main page for "TreeView Demo: Frameless Layout with Highlighting"</td>
   </tr>
   <tr>
    <td>demoFramelessHiliNodes.js</td>
    <td>Script for "TreeView Demo: Frameless Layout with Highlighting"</td>
   </tr>
   <tr>
    <td>demoFrameset.html</td>
    <td>Main page for "TreeView Demo: Frame-Based Layout"</td>
   </tr>
   <tr>
    <td>demoFramesetLeftFrame.html</td>
    <td>Left frame for "TreeView Demo: Frame-Based Layout"</td>
   </tr>
   <tr>
    <td>demoFramesetNodes.js</td>
    <td>Script for "TreeView Demo: Frame-Based Layout"</td>
   </tr>
   <tr>
    <td>demoFramesetRightFrame.html</td>
    <td>Right frame for "TreeView Demo: Frame-Based Layout"</td>
   </tr>
   <tr>
    <td>demoFuncs.html</td>
    <td>Main page for "TreeView Demo: JavaScript Functions"</td>
   </tr>
   <tr>
    <td>demoFuncsLeftFrame.html</td>
    <td>Left frame for "TreeView Demo: JavaScript Functions"</td>
   </tr>
   <tr>
    <td>demoFuncsMenu.html</td>
    <td>JavaScript buttons for "TreeView Demo: JavaScript Functions"</td>
   </tr>
   <tr>
    <td>demoFuncsNodes.js</td>
    <td>Script for "TreeView Demo: JavaScript Functions"</td>
   </tr>
   <tr>
    <td>demoFuncsRightFrame.html</td>
    <td>Right frame for "TreeView Demo: JavaScript Functions"</td>
   </tr>

   <tr>
    <td colspan="2"><b>Other Files:</b></td>
   </tr>
   <tr>
    <td>index.html</td>
    <td>A file with links to the different demos in this ZIP file.</td>
   </tr>
   <tr>
    <td>readme.html</td>
    <td>This readme file.</td>
   </tr>

  </table>

 </BODY>

</HTML>