var xmlhttp=loadXMLHTTP();
var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
stateSelectionTitle['text'] = 'Please Select';

function loadXMLHTTP() {
	try { 
		varXMLHTTP = new ActiveXObject("Msxml2.XMLHTTP"); 
	}
	
	catch (e) { 
		try { 
			varXMLHTTP = new ActiveXObject("Microsoft.XMLHTTP"); 
		}
		catch (E) { 
			varXMLHTTP = false; 
		}
	}
	
	if (!xmlhttp && typeof XMLHttpRequest!='undefined') { 
		try {
			varXMLHTTP = new XMLHttpRequest(); 
		}
		catch (ex) {
			varXMLHTTP = false;		
		}
	}
	
	return varXMLHTTP;
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');
    
    objBody.appendChild(objContainer);
}

function loadCatSelectionLists(res_div, fid, _lang) {
	var div_obj = DOMCall(res_div);
	var fid_obj = DOMCall('files_cat_' + fid);
	var def_cat_selection = '';
	
	var cat_list_boxes_html = '';
	
	var msg = 'Loading... Please be patience!';
	showMainInfo(msg);
	
	if (fid_obj != null) def_cat_selection = fid_obj.value;
	
	var server_action = 'get_categories_list_boxes';
	var ref_url = "admin_xmlhttp.php?action="+server_action+"&lang="+_lang+"&fid="+fid+"&sel_cat="+def_cat_selection;
	
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
      		
      		var result_html = xmlhttp.responseXML.getElementsByTagName("html_result")[0];
      		
      		if (typeof (result_html) != 'undefined' && result_html != null) {
      			if (result_html.getElementsByTagName("cat_boxes_title")[0] != null) {
      				cat_list_boxes_html += '<p>' + result_html.getElementsByTagName("cat_boxes_title")[0].firstChild.nodeValue + '<br><br>';
      			}
      			
      			if (result_html.getElementsByTagName("cat_boxes_html")[0] != null) {
      				cat_list_boxes_html += result_html.getElementsByTagName("cat_boxes_html")[0].firstChild.nodeValue;
	      		}
	      		
	      		if (result_html.getElementsByTagName("nav_html")[0] != null) {
      				cat_list_boxes_html += '<br><br>' + result_html.getElementsByTagName("nav_html")[0].firstChild.nodeValue;
	      		}
	      	}
	      	
	      	div_obj.innerHTML = cat_list_boxes_html;
	      	hideMainInfo();
      	}
      	
      	//div_obj.className = 'show';
    }
	xmlhttp.send(null);
	
	setTimeout('hideMainInfo()', 3000);
}

// add item to select element the less elegant, but compatible way.
function appendToSelect(select, value, content) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content;
	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	var objInfo = DOMCall('infocaption');
	
	if (DisplayMsg == null || objInfo == null) {
		return;
	}
	
	var scroll = getScroll();
	
	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';
	
	objInfo.style.position = "absolute";
	
	objInfo.style.top = 5 + scroll.y;
	
	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}

function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	if (objInfo == null) {
		return;
	}
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

function payment_method_edit_permission(user_group_id, pass_type, pass_from, pass_to){
	var display_html  = '<div id="methods_div" style=\"width: 650px; height: 250px; overflow-y: scroll;\">';
		display_html += '	<div id="payment_method_list"></div>';
		display_html += '</div>';

	jQuery.ajax({
		type: "post",
		url: 'orders_status_conf_xmlhttp.php',
		data: 'action=get_payment_methods&type='+pass_type+'&from='+pass_from+'&to='+pass_to+'&group_id='+user_group_id,
		dataType: 'xml',
		success: function(xml){
			var display_content  = '<table cellpadding=\"10\" align=\"left\">';
				display_content += '<tr><td align=\"left\"><dl>\n';
			
			if (jQuery(xml).find('method').length > 0) {
				jQuery(xml).find("method").each(function(){
					var parent_id = jQuery(this).attr('parent_id');
					if (parent_id=='0')
						display_content += '<dt><input type=\"checkbox\" name=\"payment_method[parent_id][]\" value=\"'+jQuery(this).attr('id')+'\" onClick=\"payment_method_check_selection(this);\" ><b>'+jQuery(this).text()+'</b></dt>\n';
					else
						display_content += '<dd><input type=\"checkbox\" name=\"payment_method[id]['+parent_id+'][]\" value=\"'+jQuery(this).attr('id')+'\" '+jQuery(this).attr('checked')+' >'+jQuery(this).text()+'</dd>\n';
				});
			} else {
				display_content += '<dt>No payment method found.</dt>';
			}
			display_content += '</dl></td></tr>';
			display_content += '</table>';
			jQuery("div#methods_div div#payment_method_list").html(display_content);
		}
	});
	
	jquery_confirm_box(display_html ,2 ,0 ,'Payment Method List' ,0 , '800');
	
	jQuery("#jconfirm_submit").click(function(){
		var selected_methods = '';
		jQuery("input[type=checkbox]:checked").each(function(){
			if(jQuery(this).attr('name') != 'payment_method[parent_id][]')
				if(selected_methods == '')
					selected_methods += jQuery(this).val();
				else
					selected_methods += ',' + jQuery(this).val();
		});
		jQuery.ajax({
			type: "post",
			url: 'orders_status_conf_xmlhttp.php',
			data: 'action=update_payment_methods&selected_methods='+selected_methods+'&type='+pass_type+'&from='+pass_from+'&to='+pass_to+'&group_id='+user_group_id,
			dataType: 'xml',
			success: function(xml){
				
			}
		});
	});
}

function payment_method_check_selection(status_obj) {
	if (status_obj != null) {
		var cur_status_id = status_obj.value;
		var disabled_mode = status_obj.checked ? false : true;
		var sub_status_select = document.getElementsByName("payment_method[id]["+cur_status_id+"][]");
		
		if (typeof(sub_status_select) != 'undefined') {
			if (typeof(sub_status_select.length) != 'undefined') {
				for (sub_cnt=0; sub_cnt < sub_status_select.length; sub_cnt++) {
					sub_status_select[sub_cnt].checked = !disabled_mode;
				}
			} else {
				sub_status_select.checked = !disabled_mode;
			}
		}
	}
}

function refresh_cache() {
	
	jQuery.ajax({
		type: 'GET',
		url:'admin_xmlhttp.php?action=refresh_cache',
		data: {},
		success: function(xml){
		var cache_refreshed = jQuery(xml).find('cache_refreshed').text();
			if (cache_refreshed == '1') {
				alert('Refresh Memcache Successful..!!');
			}
		}
	});
	
}
