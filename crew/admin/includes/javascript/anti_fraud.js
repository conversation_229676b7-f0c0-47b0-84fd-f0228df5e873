function getFunctionConfig(function_id) {
	var server_action = 'get_aft_function';
	var display_html = '';
	var ref_url = 'anti_fraud_xmlhttp.php?action='+server_action+'&aft_functions_id='+function_id;
	
	if (function_id != '') {
		jquery_confirm_box('Loading...');
		
		jQuery.ajax({
			url: ref_url,
			type: 'GET',
			dataType: 'xml',
			timeout: 10000,
			error: function() {
				jQuery.unblockUI();
			},
			success: function(xml) {
				var box_content = jQuery(xml).find('box_content').text();
				var total_para = jQuery(xml).find('total_para').text();
				var function_name = jQuery(xml).find('function_name').text();
				var line_determine = jQuery(xml).find('line_determine').text();
				
				var box_content_html = '';
				var error = jQuery(xml).find('error').text();
				
				if (error <= 0) {
					var complete_function_code = '$this->' + function_name + '(';
					box_content_html = '<form name="function_para_select" id="function_para_select"><table border="0"><tr><td>';
					
					box_content_html += box_content;
	      			box_content_html += '</td></tr></form>';
	      			
	      			jquery_confirm_box(box_content_html, 2);
	      			
	      			jQuery('#jconfirm_submit').click(function() {
	      				for (para_cnt = 0; para_cnt < total_para; para_cnt++) {
	      					complete_function_code += jQuery('#para_'+para_cnt).val();
	      					
	      					if (para_cnt < (total_para - 1)) {
	      						complete_function_code += ', ';
	      					}
	      				}
	      				
	      				if (line_determine == 1) {
	      					complete_function_code += ((total_para > 0) ? ', __LINE__' : '__LINE__');
	      				}
	      				
	      				complete_function_code += ')';
	      				
	      				editAreaLoader.insertTags('aft_code', complete_function_code, '');
	      			});
	      		} else {
	      			jquery_confirm_box('Error loading XML document', 1, 1, 'Error');
	      		}
			}
		});
	}
	
	jQuery('#functions').val('');
}

function rerun_genesis_project(formObj) {
	jquery_confirm_box('Are you sure to rerun Genesis Project?', 2, 0 , 'Confirm');
	jQuery('#jconfirm_submit').click(function() {
        var count_second = Math.floor((Math.random()*20)+10); ;
        jQuery('#jconfirm_cancel').unbind("click");
        jquery_confirm_box('Are you sure to rerun Genesis Project?', 2, 0, 'Confirm');
        interval_id = setInterval("process_count_down_button()", 1000);

        process_count_down_button = function() {
            jQuery("#jconfirm_submit").attr('onClick','void(0);')
            jQuery("#jconfirm_submit").css('color','grey');
            jQuery("#jconfirm_cancel").attr('onClick',"clearTimeout(interval_id);jQuery.unblockUI();return false;")
            jQuery("#jconfirm_cancel").css('background-color','#F49BB2')
                                        .css('color','#FF0000')
                                        .css('border-color','#100F37');
            if (jQuery("#jconfirm_submit").length) {
                jQuery("#jconfirm_submit").text(count_second);
                if (count_second < 1) {
                    clearTimeout(interval_id);
                    formObj.submit();
                } else {
                    count_second--;
                }
            } else {
                clearTimeout(interval_id);
            }
            jQuery('#jconfirm_cancel').click(function() {
                
            });
        }
    });
}