<SCRIPT language="JavaScript" type="text/javascript">
<!--
    var remark_link_pool = Array();

    function DOMCall(name) {
        if (document.getElementById)
            return document.getElementById(name);
        else if (document.all)
            return document.all[name];
        else if (document.layers)
            return document.layers[name];
    }

    function initInfoCaptions() {
        // create a dynamic layer and attach it to the HTML document.
        objBody = document.getElementsByTagName("body").item(0);
        objContainer = document.createElement('div');
        objContainer.setAttribute('id', 'infocaption');

        objBody.appendChild(objContainer);
    }

    function appendToSelect(select, value, content, disabledMode) {
        var opt = document.createElement('option');
        opt.value = value;
        opt.text = content.replace(/&nbsp;/ig, String.fromCharCode(160));
        if (disabledMode != null) {
            opt.setAttribute('disabled', (disabledMode == 1 ? true : false));
        }
        select.options.add(opt);
    }

    function clearOptionList(obj) {
        while (obj.options.length > 0) {
            obj.remove(0);
        }
    }

    function showMainInfo(DisplayMsg) {
        hideMainInfo();
        var objInfo = DOMCall('infocaption');

        if (DisplayMsg == null || objInfo == null) {
            return;
        }

        var scroll = getScroll();

        objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
        objInfo.style.visibility = 'visible';

        objInfo.style.position = "absolute";

        objInfo.style.top = 5 + scroll.y;

        var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
        objInfo.style.left = msg_left_pos - 20;
    }

    function hideMainInfo() {
        var objInfo = DOMCall('infocaption');

        objInfo.style.visibility = 'hidden';
        objInfo.innerHTML = '';
    }

    function getWindowWidth() {
        var windowWidth = 0;
        if (typeof (window.innerWidth) == 'number') {
            windowWidth = window.innerWidth;
        } else {
            if (document.documentElement && document.documentElement.clientWidth) {
                windowWidth = document.documentElement.clientWidth;
            } else {
                if (document.body && document.body.clientWidth) {
                    windowWidth = document.body.clientWidth;
                }
            }
        }
        return windowWidth;
    }

    function getScroll() {
        if (document.body.scrollTop != undefined) { // IE model
            var ieBox = document.compatMode != "CSS1Compat";
            var cont = ieBox ? document.body : document.documentElement;
            return {x: cont.scrollLeft, y: cont.scrollTop};
        } else {
            return {x: window.pageXOffset, y: window.pageYOffset};
        }
    }

    function hideShow(groupName, styleClass) {
        var row_count = eval(groupName + "_count");
        for (var i = 0; i < row_count; i++) {
            document.getElementById(groupName + "_" + i).className = styleClass;
        }

        if (styleClass == "show") {
            document.getElementById(groupName + '_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('" + groupName + "', 'hide')\">Hide Details</a>";
            SetCookie(groupName, '1');
        } else {
            document.getElementById(groupName + '_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('" + groupName + "', 'show')\">Show Details</a>";
            SetCookie(groupName, '0');
        }
    }

    function showOverEffect(object, class_name, extra_row) {
        rowOverEffect(object, class_name);
        var rowObjArray = extra_row.split('##');
        for (var i = 0; i < rowObjArray.length; i++) {
            if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
            }
        }
    }

    function showOutEffect(object, class_name, extra_row) {
        rowOutEffect(object, class_name);
        var rowObjArray = extra_row.split('##');
        for (var i = 0; i < rowObjArray.length; i++) {
            if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
            }
        }
    }

    function showClicked(object, class_name, extra_row) {
        rowClicked(object, class_name);
        var rowObjArray = extra_row.split('##');
        for (var i = 0; i < rowObjArray.length; i++) {
            if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                rowClicked(document.getElementById(rowObjArray[i]), class_name);
            }
        }
    }

    function calculateUnitPriceAPI(cdk_id) {
        var server_action = 'get_product_sell_price';
        //var p_id = jQuery('#po_product_id_'+cdk_id).text();
        var sell_curr = jQuery('#po_currency').val();
        //var amount = 0;
        var settle_amount = 0;

        jQuery.ajax({
            url: "api_replenish_payment_xmlhttp.php?action=" + server_action + "&cdk_id=" + cdk_id + "&sell_curr=" + sell_curr,
            type: 'GET',
            dataType: 'xml',
            timeout: 180000,
            error: function () {
                jQuery('#settle_amount_price_' + cdk_id + '_div').html('0.0000');
            },
            success: function (xml) {
                settle_amount = parseFloat(jQuery(xml).find('product_sell_price').text());
                jQuery('#settle_amount_price_'+cdk_id+'_div').html(settle_amount.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                refreshAPITotal();
            }
        });

        return true;
    }
    
    function refreshAPITotal() {
        var cdk_id;
        var subtotal;
        var total = 0;
        
        jQuery(".api_items_cdk_id").each(function () {
            cdk_id = this.value;
            subtotal = parseFloat(jQuery('#settle_amount_price_' + cdk_id + '_div').text().replace(/[^0-9\.]+/g,''));
            if (subtotal == '') {
                subtotal = 0;
            }
            if (this.checked == true) {
                total = parseFloat(eval(total + '+' + subtotal));
            }
        });

        jQuery('#po_select_total').val(total.toFixed(4));
        jQuery('#po_select_total_div').html(total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        
        if (total <= 0) {
            jQuery("#poCalculateButton").attr('disabled', 'disabled');
        } else {
            jQuery("#poCalculateButton").removeAttr('disabled');
        }

        return true;
    }

    function refreshSummaryTotal() {
        useCreditNote(DOMCall('po_use_credit_note'));
        return true;
    }
    
    function useCreditNote(selObj) {
        var cr_tbody = DOMCall('creditnote_row');
        var cr_amt = parseFloat(jQuery('#credit_note_amount').val());
        var taxtotal_amt = parseFloat(jQuery('#po_tax_total').val());
        var bankcharge_amt = parseFloat(jQuery('#po_pay_bankcharge').val());
        var adjustment_amt = parseFloat(jQuery('#po_pay_adjustment').val());
        var adjustment_type = parseFloat(jQuery('#adjustment_type').val());
        var confirm_rate = parseFloat(jQuery('#confirm_rate').val());
        var converted_cr_amt = 0;
        var po_bankcharge_amt = 0;
        var po_adjustment_amt = 0;
        var cr_amt_to_use = 0 ;
        var po_dn_amount = 0;
        var tax_bc_amt = 0;
        var bank_charge = false;
        var adjustment_charge = false;

        if (jQuery('#po_dn').length > 0) {    
            var dn_amt = parseFloat(jQuery('#po_dn').val());
            if (!dn_amt) {dn_amt = 0}
            taxtotal_amt = taxtotal_amt - dn_amt;
        }
	
        if (selObj.checked == true) {
            if (confirm_rate > 0) {
                po_bankcharge_amt = parseFloat(eval(bankcharge_amt + '*(' + 1 + '/' + confirm_rate + ')'));
                po_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + confirm_rate + ')'));
                converted_cr_amt = parseFloat(eval(cr_amt + '*(' + 1 + '/' + confirm_rate + ')'));
                
                if(adjustment_type === 1) {
                    tax_bc_amt = taxtotal_amt + po_bankcharge_amt + po_adjustment_amt;
                } else {
                    tax_bc_amt = taxtotal_amt + po_bankcharge_amt - po_adjustment_amt;
                }
                if (converted_cr_amt >= tax_bc_amt) {
                    cr_amt_to_use = tax_bc_amt;
                } else {
                    cr_amt_to_use = converted_cr_amt;
                }
                
                var new_total_amt = tax_bc_amt;
                
                if (po_bankcharge_amt > 0) {
                    bank_charge = true;
                    jQuery('#po_bankcharge').val(po_bankcharge_amt.toFixed(4));
                    jQuery('#po_bankcharge_div').html(po_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                }
                
                if (po_adjustment_amt > 0) {
                    adjustment_charge = true;
                    jQuery('#po_adjustment').val(po_adjustment_amt.toFixed(4));
                    jQuery('#po_adjustment_div').html(po_adjustment_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                }
                jQuery('#po_creditnote').val(cr_amt_to_use.toFixed(4));
                jQuery('#po_creditnote_div').html(cr_amt_to_use.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                jQuery('#po_total').val(new_total_amt.toFixed(4));
                jQuery('#po_total_div').html(new_total_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                cr_tbody.className = 'show';
            }
        } else {
            jQuery('#po_creditnote').val(0);
            jQuery('#po_creditnote_div').html('');
            jQuery('#po_total').val(taxtotal_amt.toFixed(4));
            jQuery('#po_total_div').html(taxtotal_amt.toFixed(4));
            cr_tbody.className = 'hide';
        }
        refreshPayableTotal(bank_charge, adjustment_charge);
        return true;
    }

    function getGST(selObj) {
        var comp_code = selObj.value;
        var server_action = 'get_company_gst';
        var gst_tbody = DOMCall('gst_row');
        var po_curr = jQuery('#po_currency').val();
        var po_total_amt = 0;
        var gst_checkbox = DOMCall('include_gst');
        var include_gst = 0;
        
        if(jQuery('#po_cb').length == 0) {
            po_total_amt = parseFloat(jQuery('#po_subtotal').val());
        } else {
            var sub_total = parseFloat(jQuery('#po_subtotal').val());
            var cb_total = parseFloat(jQuery('#po_cb').val());
            po_total_amt = parseFloat(eval(sub_total + '-' + cb_total));
        }

        jQuery('#po_gst_currency').html(po_curr);

        if (gst_checkbox && gst_checkbox.checked == true) {
            include_gst = 1;
        }
        
        if (gst_checkbox) {
            jQuery.ajax({
                url: "api_replenish_payment_xmlhttp.php?action=" + server_action + "&incl_gst=" + include_gst + "&comp_code=" + comp_code + '&total_amt=' + po_total_amt + '&po_curr=' + po_curr,
                type: 'GET',
                dataType: 'xml',
                timeout: 180000,
                error: function () {
                    jQuery('#po_tax').val(0);
                    jQuery('#po_tax_div').html('0.0000');
                    jQuery('#po_tax_total').val(po_total_amt.toFixed(4));
                    jQuery('#po_tax_total_div').html(po_total_amt.toFixed(4));
                    gst_tbody.className = 'hide';
                    refreshSummaryTotal();
                },
                success: function (xml) {
                    var gst_val = parseFloat(jQuery(xml).find('gst_value').text());
                    var gst_title = jQuery(xml).find('gst_title_text').text();
                    var tax_val = parseFloat(jQuery(xml).find('tax_amount').text());
                    var tax_total_val = parseFloat(jQuery(xml).find('after_tax_total').text());

                    // if (gst_val > 0) {
                        jQuery('#gst_title_div').html(gst_title);
                        jQuery('#po_tax').val(tax_val.toFixed(4));
                        jQuery('#po_tax_div').html(tax_val.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                        jQuery('#po_tax_total').val(tax_total_val.toFixed(4));
                        jQuery('#po_tax_total_div').html(tax_total_val.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                        gst_tbody.className = 'show';
                    // } else {
                    //     jQuery('#po_tax').val(0);
                    //     jQuery('#po_tax_div').html('0.0000');
                    //     jQuery('#po_tax_total').val(po_total_amt.toFixed(4));
                    //     jQuery('#po_tax_total_div').html(po_total_amt.toFixed(4));
                    //     gst_tbody.className = 'hide';

                    // }
                    refreshSummaryTotal();
                }
            });
        }
        return true;
    }

    function refreshSubtotalAPIProduct() {
        var p_id;
	   var subtotal;
	   var total = 0;
        var total_cb = 0;
	   var new_total = 0;
        var new_total_cb = 0;
	
    	jQuery(".po_items_prod_id").each (function() {
                p_id = this.value;
                subtotal = parseFloat(jQuery('#subtotal_'+p_id).val());
                if (subtotal == '') { subtotal = 0; }
                total = parseFloat(eval(total + '+' + subtotal));
    	});
        
        if(jQuery('#include_cb').is(':checked')) {
            jQuery(".po_cb_items_prod_id").each (function() {
                p_id = this.value;
                subtotal = parseFloat(jQuery('#subtotal_cb_'+p_id).val());
                if (subtotal == '') { subtotal = 0; }
                total_cb = parseFloat(eval(total_cb + '+' + subtotal));
            });
        }
        
	   new_total = total;
        new_total_cb = parseFloat(eval(total + '-' + total_cb));
	
	   jQuery('#po_subtotal').val(new_total.toFixed(4));
	   jQuery('#po_subtotal_div').html(new_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        jQuery('#po_cb').val(total_cb.toFixed(4));
	   jQuery('#po_cb_div').html(total_cb.toFixed(4));
        jQuery('#po_cb_total').val(new_total_cb.toFixed(4));
	   jQuery('#po_cb_total_div').html(new_total_cb.toFixed(4));
	
	   getGST(DOMCall('po_delivery_address'));
	
	   return true;
    }
    
    function refreshPayableTotal(bank_charge, adjustment_charge) {
        var bankcharge_amt = parseFloat(jQuery('#po_pay_bankcharge').val());
        var adjustment_amt = parseFloat(jQuery('#po_pay_adjustment').val());
        var adjustment_type = parseFloat(jQuery('#adjustment_type').val());
        var creditnote_amt = parseFloat(jQuery('#po_creditnote').val());
        var convert_rate = parseFloat(jQuery('#confirm_rate').val());
        var total = parseFloat(jQuery('#po_total').val());
        var po_bankcharge_amt = 0;
        var po_adjustment_amt = 0;
        var payable_total = 0;
        
        var adjustment_formula = (adjustment_type === 1) ? '+' : '-';
        
        if (bankcharge_amt > 0 && bank_charge === false && adjustment_amt > 0 && adjustment_charge === false) {
            po_bankcharge_amt = parseFloat(eval(bankcharge_amt + '*(' + 1 + '/' + convert_rate + ')'));
            po_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + convert_rate + ')'));
            
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(total + '+' + po_bankcharge_amt + adjustment_formula + po_adjustment_amt + '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + '+' + po_bankcharge_amt + adjustment_formula + po_adjustment_amt));
            }

            jQuery('#po_bankcharge').val(po_bankcharge_amt.toFixed(4));
            jQuery('#po_bankcharge_div').html(po_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#po_adjustment').val(po_adjustment_amt.toFixed(4));
            jQuery('#po_adjustment_div').html(po_adjustment_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#po_payable_total').val(payable_total.toFixed(4));
            jQuery('#po_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else if (adjustment_amt > 0 && adjustment_charge === false) {
            po_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + convert_rate + ')'));
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(total + adjustment_formula + po_adjustment_amt + '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + adjustment_formula + po_adjustment_amt));
            }
            
            if (bank_charge === false) {
                jQuery('#po_bankcharge').val(0);
                jQuery('#po_bankcharge_div').html('0.0000');
            }

            jQuery('#po_adjustment').val(po_adjustment_amt.toFixed(4));
            jQuery('#po_adjustment_div').html(po_adjustment_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#po_payable_total').val(payable_total.toFixed(4));
            jQuery('#po_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else if (bankcharge_amt > 0 && bank_charge === false) {
            po_bankcharge_amt = parseFloat(eval(bankcharge_amt + '*(' + 1 + '/' + convert_rate + ')'));
            po_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + convert_rate + ')'));
            
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(total + '+' + po_bankcharge_amt + adjustment_formula + po_adjustment_amt + '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + '+' + po_bankcharge_amt + adjustment_formula + po_adjustment_amt));
            }

            jQuery('#po_bankcharge').val(po_bankcharge_amt.toFixed(4));
            jQuery('#po_bankcharge_div').html(po_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#po_adjustment').val(po_adjustment_amt.toFixed(4));
            jQuery('#po_adjustment_div').html(po_adjustment_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#po_payable_total').val(payable_total.toFixed(4));
            jQuery('#po_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else if (adjustment_amt > 0 && adjustment_charge === false) {
            po_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + convert_rate + ')'));
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(total + '+' + po_bankcharge_amt +  '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + '+' + po_bankcharge_amt));
            }
            
            if (adjustment_charge === false) {
                jQuery('#po_adjustment').val(0);
                jQuery('#po_adjustment_div').html('0.0000');
            }

            jQuery('#po_bankcharge').val(po_bankcharge_amt.toFixed(4));
            jQuery('#po_bankcharge_div').html(po_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#po_payable_total').val(payable_total.toFixed(4));
            jQuery('#po_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else {
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(total + '-' + creditnote_amt));
            } else {
                payable_total = total;
            }
            
            if (bank_charge === false) {
                jQuery('#po_bankcharge').val(0);
                jQuery('#po_bankcharge_div').html('0.0000');
            }
            
            if (adjustment_charge === false) {
                jQuery('#po_adjustment').val(0);
                jQuery('#po_adjustment_div').html('0.0000');
            }
            jQuery('#po_payable_total').val(payable_total.toFixed(4));
            jQuery('#po_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        }

        return true;
    }

    function refreshGST(selObj) {
        if (selObj.checked == false) {
            jQuery('#po_tax').val(0);
            jQuery('#po_tax_div').html('0.00');
            jQuery('#po_tax_total').val(jQuery('#po_payable_total').val());
            jQuery('#po_tax_total_div').html(jQuery('#po_payable_total').val());
        }
        getGST(DOMCall('po_delivery_address'));
        return true;
    }
    
    function refreshCB(selObj) {
        var quantity;
        var cb_quantity;
        var new_quantity = 0;
        var p_id;
        var subtotal_cb;
        var subtotal;
        var new_subtotal = 0;
        var total = 0;
        var new_total = 0;
        var new_po_subtotal = 0;
        
        var sub_total = parseFloat(jQuery('#po_subtotal').val());
        
        if (selObj.checked == false) {
            jQuery(".po_cb_items_prod_id").each (function() {
                p_id = this.value;
                subtotal_cb = parseFloat(jQuery('#subtotal_cb_'+p_id).val());
                subtotal = parseFloat(jQuery('#subtotal_'+p_id).val());
                quantity = parseFloat(jQuery('#po_item_qty_'+p_id).val());
                cb_quantity = parseFloat(jQuery('#po_cb_item_qty_'+p_id).val());
                new_quantity = parseFloat(eval(quantity + '-' + cb_quantity));
                new_subtotal = parseFloat(eval(subtotal + '-' + subtotal_cb));

                jQuery('#po_item_qty_'+p_id).val(new_quantity.toFixed(0));
                jQuery('#po_item_qty_'+p_id+'_div').html(new_quantity.toFixed(0));
                jQuery('#subtotal_'+p_id).val(new_subtotal.toFixed(4));
                jQuery('#subtotal_'+p_id+'_div').html(new_subtotal.toFixed(4));
                
                if (subtotal_cb === '') { subtotal_cb = 0; }
                total = parseFloat(eval(total + '+' + subtotal_cb));
            });
            
            new_total = parseFloat(eval(sub_total + '-' + total));
            
            jQuery('#po_cb').val(0);
            jQuery('#po_cb_div').html('0.0000');
            jQuery('#po_cb_total').val(new_total.toFixed(4));
            jQuery('#po_cb_total_div').html(new_total.toFixed(4));
            jQuery('#po_subtotal').val(new_total.toFixed(4));
            jQuery('#po_subtotal_div').html(new_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else {
            jQuery(".po_cb_items_prod_id").each (function() {
                p_id = this.value;
                subtotal_cb = parseFloat(jQuery('#subtotal_cb_'+p_id).val());
                subtotal = parseFloat(jQuery('#subtotal_'+p_id).val());
                quantity = parseFloat(jQuery('#po_item_qty_'+p_id).val());
                cb_quantity = parseFloat(jQuery('#po_cb_item_qty_'+p_id).val());
                new_quantity = parseFloat(eval(quantity + '+' + cb_quantity));
                new_subtotal = parseFloat(eval(subtotal + '+' + subtotal_cb));
                
                jQuery('#po_item_qty_'+p_id).val(new_quantity.toFixed(0));
                jQuery('#po_item_qty_'+p_id+'_div').html(new_quantity.toFixed(0));
                jQuery('#subtotal_'+p_id).val(new_subtotal.toFixed(4));
                jQuery('#subtotal_'+p_id+'_div').html(new_subtotal.toFixed(4));

                if (subtotal_cb === '') { subtotal_cb = 0; }
                total = parseFloat(eval(total + '+' + subtotal_cb));
            });
            
            new_po_subtotal = parseFloat(eval(sub_total + '+' + total));
            new_total = parseFloat(eval(new_po_subtotal + '-' + total));
            
            jQuery('#po_cb').val(total.toFixed(4));
            jQuery('#po_cb_div').html(total.toFixed(4));
            jQuery('#po_cb_total').val(new_total.toFixed(4));
            jQuery('#po_cb_total_div').html(new_total.toFixed(4));
            jQuery('#po_subtotal').val(new_po_subtotal.toFixed(4));
            jQuery('#po_subtotal_div').html(new_po_subtotal.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        }
        
        getGST(DOMCall('po_delivery_address'));
        
        return true;
    }

    function refreshDN(selObj) {
        var tax_total = parseFloat(jQuery('#po_tax_total').val());
        if (selObj.checked == false) {
            jQuery('#po_dn').val(0);
            jQuery('#po_dn_div').html('0.0000');
            jQuery('#po_dn_total').val(tax_total.toFixed(2));
            jQuery('#po_dn_total_div').html(tax_total.toFixed(2));
        } else {
            var p_id;
            var subtotal;
            var total = 0;
            var new_total = 0;

            jQuery(".po_dn_items_prod_id").each (function() {
                    p_id = this.value;
                    subtotal = parseFloat(jQuery('#subtotal_dn_'+p_id).val());
                    if (subtotal == '') { subtotal = 0; }
                    total = parseFloat(eval(total + '+' + subtotal));
            });

            new_total = parseFloat(eval(tax_total + '-' + total));
            
            jQuery('#po_dn').val(total.toFixed(4));
            jQuery('#po_dn_div').html(total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#po_dn_total').val(new_total.toFixed(4));
            jQuery('#po_dn_total_div').html(new_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        }
        
        getGST(DOMCall('po_delivery_address'));
        
        return true;
    }
    
    function orderListsOptions(selObj, sid, _lang, o, wl) {
        var msg = '';
        var rExp = /_tag_selector/gi;
        var status_name = selObj.name.replace(rExp, '');
        var option_action = selObj.value;
        if (o != null && o != '') {
            var selected_order = new Array(o);
        } else {
            var selected_order = new Array();
            var order_str_obj = DOMCall(status_name + '_order_str');
            if (order_str_obj != null) {
                var order_product_ids_array = order_str_obj.value.split(',');
                for (i = 1; i <= order_product_ids_array.length; i++) {
                    var order_row = DOMCall(status_name + '_main_' + i);
                    if (order_row != null && order_row.className == "rowSelected") {
                        selected_order.push(order_product_ids_array[i - 1]);
                    }
                }
            }
        }
        var selected_order_str = selected_order.join(',');
        var act = '';
        var s_val = '';
        switch (option_action) {
            case 'nt':	// create new tag
                if (selected_order.length > 0) {
                    var new_tag_name = prompt("Please enter a new tag name", "");
                    // check to see if anything was entered
                    // null == pressing cancel
                    // ""   == entered nothing and pressing ok
                    while (new_tag_name != null && trim_str(new_tag_name) == '') {
                        new_tag_name = prompt("Please enter a new tag name", "");
                    }
                    if (new_tag_name == null) {
                        selObj.selectedIndex = 0;
                        return;
                    }
                    act = 'perform_tagging';
                    s_val = trim_str(new_tag_name);
                } else {
                    msg = 'No purchase order record selected.';
                    showMainInfo(msg);
                    selObj.selectedIndex = 0;
                }
                break;
            default:
                if (option_action.indexOf('otag_') != -1) {
                    if (selected_order.length > 0) {
                        var temp_array = option_action.split('_');
                        option_action = 'at';
                        s_val = temp_array[1];
                        act = 'perform_tagging';
                    } else {
                        msg = 'No purchase order record selected.';
                        showMainInfo(msg);
                        selObj.selectedIndex = 0;
                    }
                } else if (option_action.indexOf('rmtag_') != -1) {
                    if (selected_order.length > 0) {
                        var temp_array = option_action.split('_');
                        option_action = 'rt';
                        s_val = temp_array[1];
                        act = 'perform_tagging';
                    } else {
                        msg = 'No purchase order record selected.';
                        showMainInfo(msg);
                        selObj.selectedIndex = 0;
                    }
                }
                break;
        }

        if (trim_str(act) != '') {
            selObj.disabled = true;
            clearOptionList(selObj);
            selObj.options[0] = new Option('Loading ...', '');

            jQuery.ajax({
                url: "api_replenish_payment_xmlhttp.php?action=" + act + "&subaction=" + option_action + "&status_id=" + sid + "&setting=" + s_val + "&po_str=" + selected_order_str + "&lang=" + _lang + "&list_mode=" + (wl == true ? '2' : '1'),
                type: 'GET',
                dataType: 'xml',
                timeout: 180000,
                error: function () {

                },
                success: function (xml) {
                    clearOptionList(selObj);
                    var tag_info = jQuery(xml).find('tag_info').text();
                    if (typeof (tag_info) != 'undefined' && tag_info != null) {
                        var selection = jQuery(xml).find('selection').text();
                        if (typeof (selection) != 'undefined' && selection != null) {
                            var option_sel = '';
                            jQuery(xml).find("option").each(function () {
                                option_sel = jQuery(this).text();
                                appendToSelect(selObj, jQuery(this).attr("index"), option_sel, jQuery(this).attr("disabled"));
                            });
                        }

                        var tag_details = jQuery(xml).find('tag_details').text();
                        if (typeof (tag_details) != 'undefined' && tag_details != null) {
                            var order_tags = '';
                            jQuery(xml).find("order_tags").each(function () {
                                var current_tag = jQuery(this).text();
                                var po_id = jQuery(this).attr("order_id");
                                jQuery('#tag_' + po_id).html(current_tag);
                            });
                        }

                    }
                    selObj.disabled = false;
                }
            });
        }
        setTimeout('hideMainInfo()', 3000);
    }

    function setCBAPIChecked(cdkey_id, bulk=false) {
        jQuery('#api_items_cdk_id_'+cdkey_id).each(function () {
           this.checked = true;
           if (!bulk) {
                jQuery('#show_remarks_'+cdkey_id).html('<br /><b>Add Remarks : </b><textarea name="remarks_'+cdkey_id+'" wrap="1" cols="25" rows="3" size="50" maxlength="50" id="remarks_'+cdkey_id+'"></textarea>');
           }
        });
        return true;
    }
    
    function setCBAPIUnChecked(cdkey_id, bulk) {
        jQuery('#api_items_cdk_id_'+cdkey_id).each(function () {
           this.checked = false;
           if (!bulk) {
                jQuery('#show_remarks_'+cdkey_id).html('');
           }
        });
        return true;
    }
    
    function setCbBulkChecked(obj_sel) {
        var bulk_status = obj_sel.value;
        var cdkey_id;
        
        jQuery('.api_items_cdk_id').each(function () {
            if (this.disabled == false) {
                cdkey_id = this.value;
                if (bulk_status === '0') {
                    jQuery('#api_status_select_' + cdkey_id).val(jQuery('#api_status_select_' + cdkey_id + ' option[selected]').val());
                    setCBAPIUnChecked(cdkey_id, true);
                    jQuery('#show_bulk_remarks').html('');
                } else {
                    jQuery('#api_status_select_' + cdkey_id).val(bulk_status);
                    setCBAPIChecked(cdkey_id, true);
                    jQuery('#show_bulk_remarks').html('<br /><b>Add Remarks : </b><textarea name="bulk_remarks" wrap="1" cols="25" rows="3" size="50" maxlength="50" id="bulk_bulk_remarks"></textarea>');
                }
            }
        });
        return true;
    }
    
    function getSupplierInfo(ref, payment_id, address_div, payment_div, disburse_div, delivery_div, delivery_set, rate_refresh) {
        var obj_ref = ref;
        var address_res;
        var payment_res;
        var disburse_res;
        var delivery_res;
        var supp_locked_html;
        var api_provider;
        var server_action = 'get_supplier_info';

        if (obj_ref.value != '') {
            jQuery.ajax({
                url: "api_replenish_payment_xmlhttp.php?action=" + server_action + "&s_id=" + obj_ref.value + "&pay_id=" + payment_id + "&delivery_set=" + delivery_set,
                type: 'GET',
                dataType: 'xml',
                timeout: 180000,
                error: function () {
                    jQuery('#' + address_div).html('Error loading XML document');
                },
                success: function (xml) {
                    address_res = jQuery(xml).find('supplier_address').text();
                    payment_res = jQuery(xml).find('supplier_payment').text();
                    disburse_res = jQuery(xml).find('supplier_disbursement').text();
                    delivery_res = jQuery(xml).find('supplier_delivery').text();
                    supp_locked_html = jQuery(xml).find('supplier_locked').text();
                    api_provider = jQuery(xml).find('api_provider').text();
                    
                    jQuery('#po_payment_supplier_lock_div').html(supp_locked_html);
                    jQuery('#' + address_div).html(address_res);
                    jQuery('#' + payment_div).html(payment_res);
                    jQuery('#' + disburse_div).html(disburse_res);
                    jQuery('#' + delivery_div).html(delivery_res);
                    
                    if (api_provider === 'RIXTY' || api_provider === 'ECLUB' || api_provider === 'MINT') {
                        jQuery('#upload_csv_matching').hide();
                    } else {
                        jQuery('#upload_csv_matching').show();
                    }
                    
                    if (rate_refresh == 'true') {
                        getCurrencyRate(document.po_form.po_currency, 'suggest_rate', 'confirm_rate');
                        var row_html = '';
                        row_html += '<table id="new_po_products" border="0" width="100%" cellspacing="1" cellpadding="4">';
                        row_html += '   <tbody>';
                        row_html += '       <tr id="title">';
                        row_html += '           <td width="5%" align="center" class="invoiceBoxHeading">#</td>';
                        row_html += '           <td width="4%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_API_SELECT ?></td>';
                        row_html += '           <td width="10%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_API_DATETIME ?></td>';
                        row_html += '           <td width="10%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_PRODUCT_ID ?></td>';
                        row_html += '           <td class="invoiceBoxHeading"><?= TABLE_HEADING_PRODUCT_NAME ?></td>';
                        row_html += '           <td width="10%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_API_ORDER_NO ?></td>';
                        row_html += '           <td width="10%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_API_CDKID ?></td>';
                        row_html += '           <td width="10%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_API_SKU ?></td>';
                        row_html += '           <td width="10%" align="center" class="invoiceBoxHeading"><div id="settle_amount_price_title"><?= sprintf(TABLE_HEADING_API_SETTLE_AMOUNT, (isset($input_array['po_currency'])? $input_array['po_currency'] : DEFAULT_CURRENCY))?></div></td>';
                        row_html += '           <td width="13%" align="center" class="invoiceBoxHeading"><?= TABLE_HEADING_API_BILLING_STATUS ?></td>';
                        row_html += '       </tr>';
                        row_html += '   </tbody>';
                        row_html += '</table>';

                        jQuery('#po_list_div').html(row_html);
                    }
                }
            });
        } else {
            jQuery('#' + address_div).html('');
            jQuery('#' + payment_div).html('');
            jQuery('#' + disburse_div).html('');
            jQuery('#' + delivery_div).html('');
        }

        return true;
    }
    
    function lock_cdk_supplier() {
	   var pub_id = jQuery('#po_supplier').val();
        var supp_locked_html;
        var res;
	   var server_action = 'lock_po_supplier';
	
    	jQuery.ajax({
                url: "api_replenish_payment_xmlhttp.php?action=" + server_action + "&p_id=" + pub_id,
                type: 'GET',
                dataType: 'xml',
                timeout: 180000,
                Error: function(){
                    
                },
                success: function (xml){
                    supp_locked_html = jQuery(xml).find('supplier_locked').text();
                    jQuery('#po_payment_supplier_lock_div').html(supp_locked_html);
                    
                    res = jQuery(xml).find('action_result').text();
                    alert(res);
                }
    	});
        
    }

    function unlock_cdk_supplier() {
        var pub_id = jQuery('#po_supplier').val();
        var supp_locked_html;
        var res;
        var server_action = 'unlock_po_supplier';

        jQuery.ajax({
            url: "api_replenish_payment_xmlhttp.php?action="+server_action+"&p_id="+pub_id,
            type: 'GET',
            dataType: 'xml',
            timeout: 180000,
            Error: function(){

            },
            success: function(xml){
                supp_locked_html = jQuery(xml).find('supplier_locked').text();
                jQuery('#po_payment_supplier_lock_div').html(supp_locked_html);

                res = jQuery(xml).find('action_result').text();
                alert(res);
            }
        });
    }
    
    function getSupplierInfoReport(ref, address_div) {
        var obj_ref = ref;
        var server_action = 'get_supplier_info';

        if (obj_ref.value != '') {
            jQuery.ajax({
                url: "api_replenish_payment_xmlhttp.php?action=" + server_action + "&s_id=" + obj_ref.value,
                type: 'GET',
                dataType: 'xml',
                timeout: 180000,
                error: function () {
                    jQuery('#' + address_div).html('Error loading XML document');
                },
                success: function (xml) {
                    address_res = jQuery(xml).find('supplier_address').text();
                    jQuery('#' + address_div).html(address_res);
                }
            });
        } else {
            jQuery('#' + address_div).html('');
        }

        return true;
    }
    
    function csvUpload(obj_sel) {
        var api_search;
        if (obj_sel.value) {
            api_search = '<div id="div_search_po_button"><input type="SUBMIT" value="Search API" class="generalBtn" title="Search API" name="csvMatchBtn" onMouseOver="this.className=\'generalBtnOver\'" onMouseOut="this.className=\'generalBtn\'"></div>';
            
            jQuery('#div_search_po_button').html(api_search);
        } else {
            api_search = '<div id="div_search_po_button"><input type="BUTTON" value="Search API" class="generalBtn" title="Search API" onclick="if(checkAPIform(document.po_form.start_date, document.po_form.end_date, document.po_form.po_supplier)){searchAPIlist(document.po_form.po_list_type, document.po_form.start_date, document.po_form.end_date, document.po_form.po_supplier, document.po_form.po_currency, \'false\', \'\', \'cdkey_date\', \'asc\');}" onMouseOver="this.className=\'generalBtnOver\'" onMouseOut="this.className=\'generalBtn\'"></div>';
            
            jQuery('#div_search_po_button').html(api_search);
        }
        
        return true;
    }
    
    function searchAPIlist(po_list_type, start_date, end_date, publisher_id, currency_id, api_reload, page, po_sorting, sort_option) {
        var ltype = po_list_type.value;
        var sdate = start_date.value;
        var edate = end_date.value;
        var pubb_id = publisher_id.value;
        var c_id = currency_id.value;
        var row_num = jQuery('#new_api_replenish tr:last').prev().attr('id');
        var cdk_id = '';
        var new_po_amt = 0;
        var paging = 0;
        var initial_load = 0;
        var server_action = 'get_po_supplier';
        
        if (page === '') {
            initial_load = 1;
            paging = 1;
        } else {
            initial_load = 0;
            paging = page;
        }
        
        if (ltype === 'add_po_cb') {
            cdk_id = jQuery('#po_cdk_id').val();
        }
        
        if (pubb_id != '' || paging > 1 || initial_load === 1) {
            
            if(api_reload != 'true') {
                var msg = 'Loading... Please be patience!';
                showMainInfo(msg);
            }
            
            jQuery.ajax({
                url: "api_replenish_payment_xmlhttp.php?action=" + server_action + "&l_type=" + ltype + "&s_date=" + sdate + "&e_date=" + edate + "&pub_id=" + pubb_id + "&c_id=" + c_id + "&cdk_id=" + cdk_id + "&api_reload=" + api_reload + "&page=" + paging + "&po_sort=" + po_sorting + "&sort_option=" + sort_option + "&row_num=" + row_num,
                type: 'GET',
                dataType: 'xml',
                timeout: 180000,
                error: function () {
                    jQuery('#po_list_div').html('Error loading XML document');
                    jQuery('#po_select_total').val(new_po_amt.toFixed(4));
                    jQuery('#po_select_total_div').html(new_po_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                },
                success: function (xml) {
                    api_res = jQuery(xml).find('api_list').text();
                    api_search = jQuery(xml).find('api_search_button').text();
                    
                    if (paging > 1) {
                        jQuery('td#tr_load').parent().replaceWith(api_res);
                    } else {
                        jQuery('#po_list_div').html(api_res);
                        jQuery('#div_search_po_button').html(api_search);
                        jQuery('#po_select_total').val(new_po_amt.toFixed(4));
                        jQuery('#po_select_total_div').html(new_po_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                    }
                    
                    refreshAPITotal();
                }
            });
            
            if(api_reload != 'true') {
                setTimeout('hideMainInfo()', 3000);
            }
        }
        
        return true;
    }
    
    function getAPIList(start_date, end_date, publisher_id, product_id, order_id, cdkey_id, currency_id, list_type, page, po_sorting, sort_option) {
        var sdate = start_date.value;
        var edate = end_date.value;
        var pubb_id = publisher_id.value;
        var prd_id = product_id.value;
        var ord_id = order_id.value;
        var cd_id = cdkey_id.value;
        var c_id = currency_id;
        var have_data = 0;
        var paging = 0;
        var initial_load = 0;
        var server_action = 'get_po_list_report';
        
        if (page === '') {
            initial_load = 1;
            paging = 1;
        } else {
            initial_load = 0;
            paging = page;
        }
        
        have_data = jQuery('#tab_'+list_type).children().length;
        
        if (have_data === 0 || paging > 1 || initial_load === 1) {
            var msg = 'Loading... Please be patience!';
            showMainInfo(msg);

            jQuery.ajax({
                url: "api_replenish_payment_xmlhttp.php?action=" + server_action + "&s_date=" + sdate + "&e_date=" + edate + "&pub_id=" + pubb_id + "&prd_id=" + prd_id + "&ord_id=" + ord_id + "&cdkey_id=" + cd_id + "&c_id=" + c_id + "&l_type=" + list_type + "&page=" + paging + "&po_sort=" + po_sorting + "&sort_option=" + sort_option,
                type: 'GET',
                dataType: 'xml',
                timeout: 180000,
                error: function () {
                    alert('Error loading XML document');
                },
                success: function (xml) {
                    po_res = jQuery(xml).find('po_list').text();
                    if (paging > 1) {
                        jQuery('td#tr_load_'+list_type).parent().replaceWith(po_res);
                    } else {
                        jQuery('#tab_'+list_type).html(po_res);
                    }
                    
                    if (list_type === 'unpaid' && paging === 1) {
                        jQuery('#search-tab').html(po_res);
                        po_search = jQuery(xml).find('po_search_button').text();
                        jQuery('#div_search_po_button').html(po_search);
                        jQuery('#search-tab > ul').tabs();
                    }

                    refreshAPITotalReport(list_type);
                }
            });
            
            setTimeout('hideMainInfo()', 3000);
        }
        
        return true;
    }
    
    function refreshAPITotalReport(list_type) {
        var cdkey_id;
        var subtotal;
        var total = 0;
        
        jQuery(".po_" + list_type + "_cdkey_id").each(function () {
            cdkey_id = this.value;
            subtotal = parseFloat(jQuery('#subtotal_' + cdkey_id).val());
            if (subtotal == '') {
                subtotal = 0;
            }
            total = parseFloat(eval(total + '+' + subtotal));
        });

        jQuery('#po_select_total').val(total.toFixed(4));
        jQuery('#po_select_total_div').html(total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));

        return true;
    }
    
    function checkAPIform(start_date, end_date, publisher_id) {
        var sdate = start_date.value;
        var edate = end_date.value;
        var pubb_id = publisher_id.value;
        var supplier_lock = jQuery('#supplier_lock').val();
        var disbursement = jQuery('#po_supplier_payment').val();
        
        if (!sdate) {
            alert('Please Select Start Date');
            return false;
        }
        
        if (!edate) {
            alert('Please Select End Date');
            return false;
        }
        
        if (!pubb_id) {
            alert('Please Select Supplier/Publisher');
            return false;
        }
        
        if (!disbursement) {
            alert('Please Select Disbursement Method');
            return false;
        }
        
        if (supplier_lock==='Lock') {
            alert('Please Lock Supplier');
            return false;
        }
        
        return true;
    }

    function checkAPIformCsv(start_date, end_date, publisher_id) {
        var sdate = start_date.value;
        var edate = end_date.value;
        var pubb_id = publisher_id.value;
        
        if (!sdate) {
            alert('Please Select Start Date');
            return false;
        }
        
        if (!edate) {
            alert('Please Select End Date');
            return false;
        }
        
        if (!pubb_id) {
            alert('Please Select Supplier/Publisher');
            return false;
        }
        
        return true;
    }
    
    function checkAPIReportform(start_date, end_date) {
        var sdate = start_date.value;
        var edate = end_date.value;
        
        if (!sdate) {
            alert('Please Select Start Date');
            return true;
        }
        
        if (!edate) {
            alert('Please Select End Date');
            return true;
        }
        
        return true;
    }

    function getAPIcsv(start_date, end_date, publisher_id) {
        var sdate = start_date.value;
        var edate = end_date.value;
        var pubb_id = publisher_id.value;
        var csv = '';
        var csv_content = '';
        var csv_error = '';
        var server_action = 'get_csv_export';

        jQuery.ajax({
            url: "api_replenish_payment_xmlhttp.php?action=" + server_action + "&s_date=" + sdate + "&e_date=" + edate + "&pub_id=" + pubb_id,
            type: 'GET',
            dataType: 'json',
            timeout: 180000,
            error: function () {
                alert('Error loading JSON document');
            },
            success: function (data) {
                csv_content = data;
                
                csv += "Replenish ID,CD KeyID,sku,amount,settle_amount,serialnumber,State,Error Msg,Date,SRP Price (USD)\n";

                csv_content.forEach(function(row) {
                        csv += row;
                        csv += "\n";
                });
                
                console.log(csv);
                var hiddenElement = document.createElement('a');
                hiddenElement.href = 'data:text/csv;charset=utf-8,' + encodeURI(csv);
                hiddenElement.download = jQuery("#po_supplier option:selected").text().replace(/ /g,"_")+'_'+sdate+'_'+edate+'.csv';
                hiddenElement.click();
            }

        });

        return true;
    }
    
    function toggleCheckBox(obj_sel) {
        var cdk_id;
        var new_po_amt = 0;
        if (obj_sel.checked === true) {
            jQuery('.api_items_cdk_id').each(function () {
                if (this.disabled == false) {
                    this.checked = true;
                    cdk_id = this.value;
                    var po_amt = jQuery('#settle_amount_price_' + cdk_id + '_div').text().replace(/[^0-9\.]+/g,'');
                    new_po_amt = new_po_amt + parseFloat(po_amt);
                }
            });
            jQuery('#po_select_total').val(new_po_amt.toFixed(4));
            jQuery('#po_select_total_div').html(new_po_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else {
            jQuery('.api_items_cdk_id').each(function () {
                this.checked = false;
            });
            jQuery('#po_select_total').val('0.0000');
            jQuery('#po_select_total_div').html('0.0000');
        }

        if (new_po_amt <= 0) {
            jQuery("#poCalculateButton").attr('disabled', 'disabled');
        } else {
            jQuery("#poCalculateButton").removeAttr('disabled');
        }
        
        return true;
    }
    
    function calculatePayAPITotal(po_sel, cdk_id) {
        var total_amt = jQuery('#po_select_total').val();
        var po_amt = jQuery('#settle_amount_price_' + cdk_id + '_div').text().replace(/[^0-9\.]+/g,'');
        var po_list_type = jQuery('#po_list_type').val();
        var new_po_amt = 0;
	
        if (po_sel.checked == true) {
            new_po_amt = parseFloat(total_amt) + parseFloat(po_amt);
            if (po_list_type != 'create_blank_po') {
                jQuery('#show_remarks_'+cdk_id).html('<br /><b>Add Remarks : </b><textarea name="remarks_'+cdk_id+'" wrap="1" cols="25" rows="3" size="50" maxlength="50" id="remarks_'+cdk_id+'"></textarea>');
            }
        } else {
            new_po_amt = parseFloat(total_amt) - parseFloat(po_amt);
            if (po_list_type != 'create_blank_po') {
                jQuery('#show_remarks_'+cdk_id).html('');
            }
        }

        jQuery('#po_select_total').val(new_po_amt.toFixed(4));
        jQuery('#po_select_total_div').html(new_po_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        
        if (new_po_amt <= 0) {
            jQuery("#poCalculateButton").attr('disabled', 'disabled');
        } else {
            jQuery("#poCalculateButton").removeAttr('disabled');
        }
        
        return true;
    }
    
    function getCurrencyRate(ref, suggest_text, confirm_text) {
        var obj_ref = ref;
        var pay_curr;
        var rate_res;
        var server_action = 'get_currency_rate';
        //var amount_title = "<?= sprintf(TABLE_HEADING_API_AMOUNT, ''); ?>";
        var settle_amount_title = "<?= sprintf(TABLE_HEADING_API_SETTLE_AMOUNT, ''); ?>";
        var total_title = '';

        if (obj_ref.value != '' && typeof (jQuery('#po_supplier_payment')) != "undefined") {
            if (jQuery('#po_supplier_payment').val() != '') {
                jQuery.ajax({
                    url: "api_replenish_payment_xmlhttp.php?action=" + server_action + "&curr_code=" + obj_ref.value + "&pm_id=" + jQuery('#po_supplier_payment').val() + "&s_id=" + jQuery('#po_supplier').val(),
                    type: 'GET',
                    dataType: 'xml',
                    timeout: 180000,
                    error: function () {
                        jQuery('#' + suggest_text + '_text').html('Error loading XML document');
                    },
                    success: function (xml) {
                        pay_curr = jQuery(xml).find('payment_currency').text();
                        rate_res = jQuery(xml).find('currency_rate').text();
                        settle_amount_title = jQuery(xml).find('sell_price_title').text();
                        //amount_title = jQuery(xml).find('total_price_title').text();

                        jQuery('#' + suggest_text).val(rate_res);
                        jQuery('#' + suggest_text + '_text').html(rate_res);
                        jQuery('#' + confirm_text).val(rate_res);
                        //jQuery('#amount_price_title').html(amount_title);
                        jQuery('#settle_amount_price_title').html(settle_amount_title);
                        jQuery('#po_payment_method_currency').val(pay_curr);
                        jQuery('#po_total_currency').html(obj_ref.value);
                    }
                });
            }

            jQuery(".api_items_cdk_id").each(function () {
                cdk_id = this.value;
                calculateUnitPriceAPI(cdk_id);
            });
        } else {
            jQuery('#' + suggest_text).val('');
            jQuery('#' + suggest_text + '_text').html('');
            jQuery('#' + confirm_text).val('');
            //jQuery('#amount_price_title').html(amount_title);
            jQuery('#settle_amount_price_title').html(settle_amount_title);
            jQuery('#po_payment_method_currency').val(total_title);
            jQuery('#po_total_currency').html(total_title);
        }

        return true;
    }

    function doPOLocked(admin, o, _lang, act, element_id, btn_show_time) {
        if (pageLoaded == null || !pageLoaded)
            return;

        var objRef = DOMCall(element_id);
        var user_msg = '';

        if (act == "ulo") {
            user_msg = prompt("Leave any message for unlocking this order?", "");
            if (user_msg != null) {
                //user_msg = URLEncode(user_msg);
            } else {
                return false;
            }
        }

        objRef.innerHTML = '';
        var msg = 'Loading... Please be patience!';
        showMainInfo(msg);

        jQuery.ajax({
            url: "api_replenish_payment_xmlhttp.php?action=show_lock_btn&subaction=" + act + "&adm=" + admin + "&oid=" + o + "&lang=" + _lang + "&log_comment=" + user_msg + "&from_time=" + btn_show_time,
            type: 'GET',
            dataType: 'xml',
            timeout: 180000,
            error: function () {

            },
            success: function (xml) {
                var A = jQuery(xml).find('action').text();

                if (act == 'l') {
                    if (A == 'Prompt Alert Message') {
                        if (typeof (jQuery(xml).find('lock_msg')) != "undefined" && jQuery(xml).find('lock_msg').text() != null) {
                            var l_msg = jQuery(xml).find('lock_msg').text();
                        } else {
                            var l_msg = '';
                        }
                        alert(l_msg);

                        if (typeof (jQuery(xml).find('close_win')) != "undefined" && jQuery(xml).find('close_win').text() != null) {
                            if (jQuery(xml).find('close_win').text() == '1') {
                                if (document.all) {	// For IE
                                    window.close();
                                } else {
                                    /*****************************************************************************************
                                     The first step is to fool the browser into thinking that it was opened with a script.
                                     
                                     This opens a new page, (non-existent), into a target frame/window, 
                                     (_parent which of course is the window in which the script is executed, so replacing itself), 
                                     and defines parameters such as window size etc, (in this case none are defined as none are needed). 
                                     Now that the browser thinks a script opened a page we can quickly close it in the standard way.
                                     *****************************************************************************************/
                                    window.open('', '_parent', '');
                                    window.close();
                                }
                            }
                        }
                    }

                    window.location.reload();
                } else {
                    showBtn(admin, o, _lang, element_id, A, xml);
                    lockingHistory(o, 0, 'locking_history');
                }
            }
        });

        setTimeout('hideMainInfo()', 3000);
    }

    function showBtn(admin, o, _lang, element_id, act_msg, xml) {
        var objRef = DOMCall(element_id);
        var deliveryBoxObj = DOMCall('partial_delivery_box');

        if (typeof (jQuery(xml).find('lock_msg')) != "undefined" && jQuery(xml).find('lock_msg').text() != null) {
            var l_msg = jQuery(xml).find('lock_msg').text();
        } else {
            var l_msg = '';
        }

        var R = (typeof (jQuery(xml).find('result')) != 'undefined' && jQuery(xml).find('result').text() != null) ? jQuery(xml).find('result').text() : '';
        var Sub_A = (typeof (jQuery(xml).find('subaction')) != 'undefined' && jQuery(xml).find('subaction').text() != null) ? jQuery(xml).find('subaction').text() : '';

        if (act_msg == "Show Lock Button") {
            var show_time = (typeof (jQuery(xml).find('time')) != 'undefined' && jQuery(xml).find('time').text() != null) ? jQuery(xml).find('time').text() : '';

            objRef.innerHTML = '<div id="navBtn" style="float:left;">' + "\n" +
                    '<ul>' + "\n" +
                    '<li id="lock_btn"><a href="javascript:;" onClick="doPOLocked(\'' + admin + '\', \'' + o + '\', \'' + _lang + '\', \'l\', \'' + element_id + '\', \'' + show_time + '\');" title="Locking this order">LOCK</a></li>' + "\n" +
                    '</ul>' + "\n" +
                    '</div>' + "\n" +
                    '<div style="width:90%">' + "\n" +
                    l_msg + "\n" +
                    '</div>';

            DOMCall('order_comment_box').className = 'hide';
            DOMCall('order_comment_selection_box').className = 'hide';

            if (typeof (deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
                deliveryBoxObj.className = 'hide';
            }

            for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
                var custom_info_box = DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_info_box) != 'undefined' && custom_info_box != null) {
                    custom_info_box.className = 'hide';
                }

                var custom_extra_info_box = DOMCall('custom_extra_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_extra_info_box) != 'undefined' && custom_extra_info_box != null) {
                    custom_extra_info_box.className = 'hide';
                }

                var sup_sel_box = DOMCall('supplier_select_' + unique_product_ref_array[p_cnt]);
                if (typeof (sup_sel_box) != 'undefined' && sup_sel_box != null) {
                    sup_sel_box.disabled = true;
                }

                //For cdkey, each unique key is grouped into its parent product, so we access them individually here.
                var cdkey_release_str = 'cdkey_release_link_' + unique_product_ref_array[p_cnt];
                var tbodys = document.getElementsByTagName('tbody');
                for (var i = 0; i < tbodys.length; i++) {
                    id = tbodys[i].getAttribute("id");
                    if (typeof (id) == 'string') {
                        if (parseInt(id.search(cdkey_release_str)) >= 0) {
                            var cdkey_release_link = DOMCall(id);
                            cdkey_release_link.className = 'hide';
                        }
                    }
                }
            }

            for (loc in location_control_array) {
                if (DOMCall(loc) != null) {
                    DOMCall(loc).innerHTML = '-Hidden-';
                }
            }

            for (delivery in partial_delivery_control_array) {
                if (delivery.indexOf("deliver_") === 0) {
                    var ref_id = replace(delivery, 'deliver_', '');
                    var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
                    var delivery_obj = DOMCall(delivery);
                    if (delivery_obj != null) {
                        qty_sign_obj.disabled = true;
                        delivery_obj.disabled = true;
                        delivery_obj.value = '-Hidden-';
                        delivery_obj.size = 4;
                    }
                }
            }

            disabledFormInputs('partial_delivery', true);

            for (remark_link_ins in remark_link_pool) {
                if (remark_link_ins.indexOf("set_remark_") === 0) {
                    var remark_link_obj = DOMCall(remark_link_ins);
                    if (remark_link_obj != null)
                        remark_link_obj.innerHTML = '-Hidden-';
                }
            }
        } else if (act_msg == "Show Unlock Button") {
            objRef.innerHTML = '<div id="navBtn" style="float:left;">' + "\n" +
                    '<ul>' + "\n" +
                    '<li id="unlock_btn"><a href="javascript:;" onClick="doPOLocked(\'' + admin + '\', \'' + o + '\', \'' + _lang + '\', \'' + (Sub_A == "Prompt For Unlocking Msg" ? 'ulo' : 'ul') + '\', \'' + element_id + '\');" title="Unlocking this order">UNLOCK</a></li>' + "\n" +
                    '</ul>' + "\n" +
                    '</div>' + "\n" +
                    '<div style="width:90%">' + "\n" +
                    l_msg + "\n" +
                    '</div>' + "\n";

            DOMCall('order_comment_box').className = 'show';
            DOMCall('order_comment_selection_box').className = 'show';
            if (typeof (deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
                deliveryBoxObj.className = 'show';
            }
            for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
                var custom_info_box = DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_info_box) != 'undefined' && custom_info_box != null) {
                    custom_info_box.className = 'show';
                }

                var custom_info_box = DOMCall('custom_extra_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_extra_info_box) != 'undefined' && custom_extra_info_box != null) {
                    custom_extra_info_box.className = 'show';
                }

                var sup_sel_box = DOMCall('supplier_select_' + unique_product_ref_array[p_cnt]);
                if (typeof (sup_sel_box) != 'undefined' && sup_sel_box != null) {
                    sup_sel_box.disabled = false;
                }

                var cdkey_release_link = DOMCall('cdkey_release_link_' + unique_product_ref_array[p_cnt]);
                if (typeof (cdkey_release_link) != 'undefined' && cdkey_release_link != null) {
                    cdkey_release_link.className = 'show';
                }
            }

            for (loc in location_control_array) {
                if (DOMCall(loc) != null) {
                    DOMCall(loc).innerHTML = URLDecode(location_control_array[loc]);
                }
            }

            for (delivery in partial_delivery_control_array) {
                if (delivery.indexOf("deliver_") === 0) {
                    var ref_id = replace(delivery, 'deliver_', '');
                    var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
                    var delivery_obj = DOMCall(delivery);
                    if (delivery_obj != null) {
                        qty_sign_obj.disabled = false;
                        delivery_obj.disabled = false;
                        delivery_obj.value = '0';
                        delivery_obj.size = 4;
                    }
                }
            }

            disabledFormInputs('partial_delivery', false);

            for (remark_link_ins in remark_link_pool) {
                if (remark_link_ins.indexOf("set_remark_") === 0) {
                    var remark_link_obj = DOMCall(remark_link_ins);
                    if (remark_link_obj != null)
                        remark_link_obj.innerHTML = remark_link_pool[remark_link_ins];
                }
            }
        } else if (act_msg == "Show Failed Lock Msg") {
            DOMCall('order_comment_box').className = 'hide';
            DOMCall('order_comment_selection_box').className = 'hide';
            if (typeof (deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
                deliveryBoxObj.className = 'hide';
            }

            for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
                var custom_info_box = DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_info_box) != 'undefined' && custom_info_box != null) {
                    DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]).className = 'hide';
                }
            }

            for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
                var supplier_custom_info_box = DOMCall('supplier_custom_info_box' + unique_product_ref_array[p_cnt]);
                if (typeof (supplier_custom_info_box) != 'undefined' && supplier_custom_info_box != null) {
                    DOMCall('supplier_custom_info_box' + unique_product_ref_array[p_cnt]).className = 'hide';
                }
            }

            for (loc in location_control_array) {
                if (DOMCall(loc) != null) {
                    DOMCall(loc).innerHTML = '-Hidden-';
                }
            }

            for (delivery in partial_delivery_control_array) {
                if (delivery.indexOf("deliver_") === 0) {
                    var ref_id = replace(delivery, 'deliver_', '');
                    var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
                    var delivery_obj = DOMCall(delivery);
                    if (delivery_obj != null) {
                        qty_sign_obj.disabled = true;
                        delivery_obj.disabled = true;
                        delivery_obj.value = '-Hidden-';
                        delivery_obj.size = 4;
                    }
                }
            }

            disabledFormInputs('partial_delivery', true);

            for (remark_link_ins in remark_link_pool) {
                if (remark_link_ins.indexOf("set_remark_") === 0) {
                    var remark_link_obj = DOMCall(remark_link_ins);
                    if (remark_link_obj != null)
                        remark_link_obj.innerHTML = '-Hidden-';
                }
            }

            objRef.innerHTML = l_msg;
        }

        showMainInfo(R);
    }

    function lockingHistory(o, mode, element_id) {
        if (pageLoaded == null || !pageLoaded)
            return;

        var obj = DOMCall(element_id);
        if (obj == null)
            return;

        obj.className = 'hide';

        if (mode == 1) {
            var msg = 'Loading... Please be patience!';
            showMainInfo(msg);

            jQuery.ajax({
                url: "api_replenish_payment_xmlhttp.php?action=get_order_locking_history&oid=" + o,
                type: 'GET',
                dataType: 'xml',
                timeout: 180000,
                error: function () {
                    return;
                },
                success: function (xml) {
                    var A = jQuery(xml).find('action').text();

                    var R_C = (typeof (jQuery(xml).find('res_code')) != 'undefined' && jQuery(xml).find('res_code').text() != null) ? jQuery(xml).find('res_code').text() : '';
                    var R = (typeof (jQuery(xml).find('result')) != 'undefined' && jQuery(xml).find('result').text() != null) ? jQuery(xml).find('result').text() : '';

                    if (R_C == 0) {
                        alert(R);
                    } else if (R_C == 1) {
                        obj.innerHTML = '	<div>' + "\n" +
                                '		<a href="javascript:;" onClick="lockingHistory(\'' + o + '\', \'0\', \'' + element_id + '\');">Hide Locking History</a>' + "\n" +
                                '	</div>' + "\n" +
                                '	<div>' + R + '</div>';
                    }

                    obj.className = 'show';

                    hideMainInfo();
                }
            });
        } else {
            obj.innerHTML = '	<div>' + "\n" +
                    '		<a href="javascript:;" onClick="lockingHistory(\'' + o + '\', \'1\', \'' + element_id + '\');">Show Locking History</a>' + "\n" +
                    '	</div>';
            obj.className = 'show';
        }

        setTimeout('hideMainInfo()', 3000);
    }

    function getAPIPaymentStatistic(suppid) {
        var server_action = 'get_po_payment_statistic';
        var ref_url = 'api_replenish_payment_xmlhttp.php?action=' + server_action + '&suppID=' + suppid;

        jQuery('#po_stat_div').html('<b>Loading Statistic...</b>');

        jQuery.ajax({
            url: ref_url,
            type: 'GET',
            dataType: 'xml',
            timeout: 180000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                var po_stat = jQuery(xml).find('stat').text();

                if (po_stat != '') {
                    po_stat = '<table width="100%" cellspacing="0" cellpadding="2" border="0">' + po_stat + '</table>';
                }

                jQuery('#po_stat_div').html(po_stat);
            }
        });
    }

    function toggle_po_comment(whichLink, whichLayer) {
        var show_mode = '';

        if (typeof (whichLink.innerText) != 'undefined') {
            show_mode = whichLink.innerText.indexOf('Hide') == 0 ? true : false;
        } else {
            show_mode = whichLink.text.indexOf('Hide') == 0 ? true : false;
        }

        if (show_mode) {
            whichLink.innerHTML = whichLink.innerHTML.replace(/Hide/ig, "Show");

            if (whichLayer == 'o_comment_') {
                for (var i = 0; i < 3; i++) {
                    newLayer = whichLayer + i + '_';
                    jQuery('.' + newLayer).each(function () {
                        jQuery('#' + this.value).addClass('hide');
                    });
                }
            } else {
                jQuery('.' + whichLayer).each(function () {
                    jQuery('#' + this.value).addClass('hide');
                });
            }
        } else {
            whichLink.innerHTML = whichLink.innerHTML.replace(/Show/ig, "Hide");

            if (whichLayer == 'o_comment_') {
                for (var i = 0; i < 3; i++) {
                    newLayer = whichLayer + i + '_';
                    jQuery('.' + newLayer).each(function () {
                        jQuery('#' + this.value).removeClass('hide');
                    });
                }
            } else {
                jQuery('.' + whichLayer).each(function () {
                    jQuery('#' + this.value).removeClass('hide');
                });
            }
        }
    }

    function setAsPORemark(shid, o, _lang, element_id) {
        var obj = DOMCall(element_id);

        if (obj == null)
            return;

        obj.className = 'hide';

        var msg = 'Loading... Please be patience!';
        showMainInfo(msg);

        jQuery.ajax({
            url: ref_url = "api_replenish_payment_xmlhttp.php?action=set_po_remark&shid=" + shid + "&oid=" + o + "&lang=" + _lang,
            type: 'GET',
            dataType: 'xml',
            timeout: 180000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                var R_C = jQuery(xml).find('res_code').text();
                var R = jQuery(xml).find('result').text();

                if (R_C == 0) {
                    alert(R);
                } else if (R_C == 1) {
                    obj.innerHTML = R;
                    initSetRemarkView(1);	// the inner html of order comment's set as remark link has been changed.
                }

                obj.className = 'show';

                hideMainInfo();
            }
        });

        setTimeout('hideMainInfo()', 3000);
    }

    function initSetRemarkView(mode) {
        for (var i = 0; i < set_as_remark_array.length; i++) {
            if (set_as_remark_array[i].indexOf("set_remark_") === 0) {
                var set_remark_obj = DOMCall(set_as_remark_array[i]);
                if (set_remark_obj != null) {
                    remark_link_pool[set_as_remark_array[i]] = set_remark_obj.innerHTML;
                    if (mode == 1) {
                        ;//set_remark_obj.innerHTML = '';
                    } else {
                        set_remark_obj.innerHTML = '-Hidden-';
                    }
                }
            }
        }
    }

    function poInfo(sec, mode, instruction, _lang, SID) {
        var order_str_obj = DOMCall(sec + '_order_str');
        var order_detail_link = DOMCall(sec + '_nav');

        if (mode == 1) {
            if (order_str_obj.value != '') {
                order_detail_link.innerHTML = '';

                if (instruction == true) {
                    var msg = 'Loading... Please be patience!';
                    showMainInfo(msg);
                }
                
                var fetch_fresh_info = true;
                var order_ids_array = order_str_obj.value.split(',');
                for (i = 0; i < order_ids_array.length; i++) {
                    var temp_order_div = DOMCall(sec + '_' + order_ids_array[i] + '_order');
                    if (trim_str(temp_order_div.innerHTML) != '')
                        fetch_fresh_info = false;
                    break;
                }

                if (fetch_fresh_info == true) {
                    jQuery.ajax({
                        url: ref_url = "api_replenish_payment_xmlhttp.php?action=get_po_info&o_str=" + order_str_obj.value + "&lang=" + _lang + "&SID=" + SID,
                        type: 'GET',
                        dataType: 'xml',
                        timeout: 180000,
                        error: function () {
                            jQuery.unblockUI();
                        },
                        success: function (xml) {
                            order_detail_link.innerHTML = '<a href="javascript:;" onClick="poInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\', \'' + SID + '\');">Hide Details</a>';

                            var order_info = jQuery(xml).find('order_info').text();

                            if (typeof (order_info) != 'undefined' && order_info != null) {
                                var order_details = '';

                                jQuery(xml).find("order_detail").each(function () {
                                    order_details = jQuery(this).text();
                                    var order_div = DOMCall(sec + '_' + jQuery(this).attr("order_id") + '_order');
                                    order_div.innerHTML = order_details;
                                    var order_tbody = DOMCall(sec + '_' + jQuery(this).attr("order_id") + '_order_sec');
                                    order_tbody.className = 'show';
                                });
                            }

                            if (instruction == true)
                                hideMainInfo();
                        }
                    });
                } else {
                    order_detail_link.innerHTML = '<a href="javascript:;" onClick="poInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\', \'' + SID + '\');">Hide Details</a>';

                    for (i = 0; i < order_ids_array.length; i++) {
                        var order_detail_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_order_sec');
                        order_detail_tbody.className = 'show';
                    }

                    if (instruction == true)
                        hideMainInfo();
                }
            }
        } else {
            if (order_str_obj.value != '') {
                order_detail_link.innerHTML = '<a href="javascript:;" onClick="poInfo(\'' + sec + '\', \'1\', true, \'' + _lang + '\', \'' + SID + '\');">Show Details</a>';

                var order_ids_array = order_str_obj.value.split(',');
                for (i = 0; i < order_ids_array.length; i++) {
                    var order_detail_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_order_sec');
                    order_detail_tbody.className = 'hide';
                }
            }
        }
    }

    function client_height_adjust() {
        if (typeof (window.innerWidth) == 'number') { //not IE
            return 20;
        } else {
            return 20;
        }
    }

    function set_fancybox_position() {
        var boxTopValue = 0, boxLeftValue = 0;
        //Get the screen height and width
        var docH = jQuery(document).height();
        var winW = jQuery(window).width();
        var winH = jQuery(window).height();

        boxTopValue = winH / 2 - jQuery('#fancy_content').height() / 2 - 30;
        boxLeftValue = winW / 2 - 245;

        jQuery("#fancy_box").css({'display': 'block', 'left': boxLeftValue, 'visibility': 'visible', 'top': boxTopValue});
        jQuery("#fancy_box_Bg").css({'display': 'block', 'position': 'absolute', 'width': winW, 'height': docH, 'visibility': 'visible'});

        if (typeof (window.innerWidth) == 'number') {
            jQuery("#fancy_box").css({'position': 'fixed'});
        }
    }

    function realign_fancybox(table_id) {
        if (jQuery("#fancy_box").css('display') == 'block') {
            jQuery("#fancy_box").css('height', jQuery("#" + table_id).height() + client_height_adjust());

            if (jQuery("#" + table_id).width() > jQuery("#fancy_box").width()) {
                jQuery("#fancy_box").css('width', jQuery("#" + table_id).width() + 10);
            }

            var boxTopValue = 0, boxLeftValue = 0;
            var docH = jQuery(document).height();
            var winW = jQuery(window).width();
            var winH = jQuery(window).height();

            boxTopValue = winH / 2 - jQuery('#fancy_content').height() / 2 - 30;
            boxLeftValue = winW / 2 - 245;

            jQuery("#fancy_box").css({'left': boxLeftValue, 'top': boxTopValue});
            jQuery("#fancy_box_Bg").css({'width': winW, 'height': docH});

            if (typeof (window.innerWidth) != 'number') {
                var scrollWinH = (jQuery(window).height() / 2) - 222 + jQuery(this).scrollTop();
                jQuery("#fancy_box").css({top: scrollWinH + "px"});
            }
        }
    }

    function hideFancyBox() {
        jQuery('#footerBar').css('display', 'block');
        jQuery("#fancy_close").css('display', 'none');
        jQuery("#fancy_box").css({'display': 'none', 'visibility': 'hidden'});
        jQuery("#fancy_box_Bg").css({'display': 'none', 'visibility': 'hidden'});
        jQuery("body").css('overflow', '');
    }

    function get_crew_remark() {
        var ref_url = "api_replenish_payment_xmlhttp.php?action=popup_remark";

        pop_out = '<table id="crew_remark" border="0" cellspacing="0" cellpadding="0" width="100%">';
        pop_out += '<tr><td>';
        <?= "pop_out += '" . tep_draw_form("po_remark_form", FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array("subaction")) . "subaction=add_remark", "post", 'id="po_remark_form"') . "';" ?>
        pop_out += '<table id="sc_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%">';
        pop_out += '<tr><td width="3" style="padding:15px 0px;"></td><td class="footerPopupTitle" colspan="4"><?= HEADER_ADD_REMARK ?></td></tr>';
        pop_out += '<tr><td colspan="5"><div style="padding-top: 3px; margin-top: 0px;" class="solidGreyLine"><!-- --></div></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3"><textarea id="crew_comments" rows="5" cols="100" wrap="soft" name="crew_comments"></textarea></td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td><input type="checkbox" value="1" name="notify_supplier" id="notify_supplier">&nbsp;<b><?= TEXT_NOTIFY_SUPPLIER ?></b></td><td>&nbsp;</td><td align="right"><input type="checkbox" value="1" name="set_important" id="set_important">&nbsp;<b><?= TEXT_SET_IMPORTANT_REMARK ?></b></td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '<tr><td colspan="5"><div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3">';
        pop_out += '<div class="green_button_fix_width" id="btn_remark_update"><a href="javascript:;" class="text-decoration: none;"><span><font><?= BUTTON_UPDATE ?></font></span></a></div>';
        pop_out += '</td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '</table></form></td></tr></table>';

        blockUI_disable();
        jQuery.ajax({
            url: ref_url,
            type: 'GET',
            dataType: 'xml',
            timeout: 60000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                set_fancybox_position();

                jQuery("#fancy_content").html(pop_out);
                jQuery(".fancy_close_footer").css('display', 'block');
                realign_fancybox("crew_remark");

                jQuery.unblockUI();

                jQuery("#btn_remark_update").click(function () {
                    hideFancyBox();
                    jQuery('#po_remark_form').submit();
                });
            }
        });
    }

    function get_refund_remark(button_obj, curr_code) {
        var ref_url = "api_replenish_payment_xmlhttp.php?action=popup_remark";
        var buton_action = '';
        var refund_title = 'Please insert the reason to Refund this PO:';
        var po_id = jQuery('#po_id').val();

        if (button_obj.name == 'CancelPendingReceiveBtn') {
            buton_action = 'refund_po_cancel';
            refund_title = 'Please insert the reason to Cancel Pending Receive this PO:';
        }

        pop_out = '<table id="refund_remark" border="0" cellspacing="0" cellpadding="0" width="100%">';
        pop_out += '<tr><td>';
        <?= "pop_out += '" . tep_draw_form("po_refund_form", FILENAME_API_REPLENISH_PAYMENT, tep_get_all_get_params(array("subaction")), "post", 'id="po_refund_form"') . "';" ?>
        pop_out += '<table id="sc_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%">';
        pop_out += '<tr><td width="3" style="padding:15px 0px;"><input type="hidden" id="subaction" name="subaction" value="' + buton_action + '" /><input type="hidden" id="po_id" name="po_id" value="' + po_id + '" /></td><td class="footerPopupTitle" colspan="4">' + refund_title + '</td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3"><textarea id="cancel_comments" rows="5" cols="100" wrap="soft" name="cancel_comments"></textarea></td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '<tr><td colspan="5"><div style="padding-top: 3px; margin-top: 0px;" class="solidGreyLine"><!-- --></div></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3"><b>Bank Charges to refund: ' + curr_code + '</b>&nbsp;<input type="text" name="bankcharges_refund" id="bankcharges_refund" size="10" /></td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '<tr><td colspan="5"><div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3">';
        pop_out += '<div class="green_button_fix_width" id="btn_refund_confirm"><a href="javascript:;" class="text-decoration: none;"><span><font><?= BUTTON_CONFIRM ?></font></span></a></div>';
        pop_out += '</td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '</table></form></td></tr></table>';

        blockUI_disable();
        jQuery.ajax({
            url: ref_url,
            type: 'GET',
            dataType: 'xml',
            timeout: 60000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                set_fancybox_position();

                jQuery("#fancy_content").html(pop_out);
                jQuery(".fancy_close_footer").css('display', 'block');
                realign_fancybox("refund_remark");

                jQuery.unblockUI();

                jQuery("#btn_refund_confirm").click(function () {
                    hideFancyBox();
                    jQuery('#po_refund_form').submit();
                });
            }
        });
    }

    function get_product_last_purchase_price(r_id, p_id, o_id) {
        var cost_hist_table = DOMCall('1_sub_' + r_id + '_' + p_id + '_cost');

        var ref_url = "api_replenish_payment_xmlhttp.php?action=product_price_history&p_id=" + p_id + "&o_id=" + o_id;

        blockUI_disable();
        jQuery.ajax({
            url: ref_url,
            type: 'GET',
            dataType: 'xml',
            timeout: 60000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                <?= "var nav_html = '<a href=\"javascript:void(0);\" onClick=\"hide_product_price_history(\''+r_id+'\', \''+p_id+'\', \''+o_id+'\')\">" . tep_image(DIR_WS_ICONS . "att.gif", "Hide Last 4x Cot Price", "14", "13", 'align="top"') . "</a>';" ?>

                var prd_cost_html = jQuery(xml).find('cost_history_html').text();
                ;

                jQuery("#" + r_id + "_nav").html(nav_html);
                jQuery("#" + p_id + "_cost").html(prd_cost_html);

                cost_hist_table.className = 'show';

                jQuery.unblockUI();
            }
        });
    }

    function hide_product_price_history(r_id, p_id, o_id) {
        var cost_hist_table = DOMCall('1_sub_' + r_id + '_' + p_id + '_cost');
        <?= "var nav_html = '<a href=\"javascript:void(0);\" onClick=\"get_product_last_purchase_price(\''+r_id+'\', \''+p_id+'\', \''+o_id+'\')\">" . tep_image(DIR_WS_ICONS . "att.gif", "View Last 4x Cot Price", "14", "13", 'align="top"') . "</a>';" ?>

        jQuery("#" + r_id + "_nav").html(nav_html);

        cost_hist_table.className = 'hide';
    }
//--></SCRIPT>