
isIE=document.all;
isNN=!document.all&&document.getElementById;
isN4=document.layers;
isHot=false;

function ddInit(e){
  topDog=isIE ? "BODY" : "HTML";
  whichDog=isIE ? document.all.theLayer : document.getElementById("theLayer");  
  hotDog=isIE ? event.srcElement : e.target;  
  while (hotDog.id!="titleBar"&&hotDog.tagName!=topDog){
    hotDog=isIE ? hotDog.parentElement : hotDog.parentNode;
  }  
  if (hotDog.id=="titleBar") {
    offsetx=isIE ? event.clientX : e.clientX;
    offsety=isIE ? event.clientY : e.clientY;
    nowX=parseInt(whichDog.style.left);
    nowY=parseInt(whichDog.style.top);
    ddEnabled=true;
    document.onmousemove=dd;
  }
}

function dd(e) {
	if (!ddEnabled) return;
	whichDog.style.left=isIE ? nowX+event.clientX-offsetx : nowX+e.clientX-offsetx; 
	whichDog.style.top=isIE ? nowY+event.clientY-offsety : nowY+e.clientY-offsety;
	return false;
}

function ddN4(whatDog) {
	if (!isN4) return;
	N4=eval(whatDog);
	N4.captureEvents(Event.MOUSEDOWN|Event.MOUSEUP);
	N4.onmousedown=function(e) {
	N4.captureEvents(Event.MOUSEMOVE);
	N4x=e.x;
	N4y=e.y;
}

	N4.onmousemove=function(e) {
		if (isHot) {
			N4.moveBy(e.x-N4x,e.y-N4y);
			return false;
		}
	}

	N4.onmouseup=function() {
		N4.releaseEvents(Event.MOUSEMOVE);
	}
}

function hideMe() {
	if (isIE||isNN) whichDog.style.visibility="hidden";
	else if (isN4) document.theLayer.visibility="hide";
}

function showMe(rsc_id, pid) {
	pop_out = '<table border="0" width="1000" bgcolor="#FF6633" cellspacing="0" cellpadding="5"><tr><td width="100%" valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="0" height="361px"><tr><td width="100%" height="10px"><font color=#ffffff size=2 face=arial style="text-decoration:none;font-weight:bold;"><big>Restock Character Log</big></font></td><td id="titleBar" style="cursor:move"><ilayer width="100%" onSelectStart="return false"><layer width="100%" onMouseover="isHot=true;if (isN4) ddN4(theLayer)" onMouseout="isHot=false"><font color=#ffffff size=2 face=arial style="text-decoration:none;font-weight:bold;"><big>+<big></font></layer></ilayer></td><td style="cursor:hand" valign="top">&nbsp;<a href="#" onClick="hideMe();return false"><font color=#ffffff size=2 face=arial style="text-decoration:none;font-weight:bold;">X</font></a></td></tr><tr><td><table border="0" height="100%" width="100%" cellspacing="0" cellpadding="0"><td width="100%" bgcolor="#FFFFFF" style="padding:4px" colspan="2" valign="top"><iframe width="100%" src="restock_character_log.php?action=search&rcsID='+rsc_id;	
	if (pid != '' && pid != 0) {
		pop_out += '&product_id=' +pid;
	}
	pop_out += '" height="100%" frameborder="0"></iframe><td></tr></table></td><td bgcolor="white">&nbsp;</td></tr></table>';
	
	if (isIE||isNN) {
		whichDog.style.visibility = "visible";
		whichDog.innerHTML = pop_out;
	}  else if (isN4) {
		document.theLayer.visibility = "show";
		document.theLayer.innerHTML = pop_out;
	}
}

document.onmousedown=ddInit;
document.onmouseup=Function("ddEnabled=false");