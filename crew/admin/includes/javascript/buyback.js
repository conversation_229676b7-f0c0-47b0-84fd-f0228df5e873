function orderListsOptions(selObj, sid, _lang, o, wl, store) {
    var msg = '';
    var rExp = /_tag_selector/gi;
    var status_name = selObj.name.replace(rExp, '');
    var option_action = selObj.value;

    if (typeof(store) == undefined || store == null) {
        var store = '';
    }

    if (o != null && o != '') {
        var selected_order = new Array(o);
    } else {
        var selected_order = new Array();
        var order_str_obj = DOMCall(status_name + '_order_str');
        if (order_str_obj != null) {
            var order_ids_array = order_str_obj.value.split(',');
            for (i = 0; i < order_ids_array.length; i++) {
                var order_row = DOMCall(status_name + '_main_' + i);
                if (order_row != null && order_row.className == "rowSelected") {
                    selected_order.push(order_ids_array[i]);
                }
            }
        }
    }
    var selected_order_str = selected_order.join(',');
    var act = '';
    var s_val = '';
    switch (option_action) {
        case 'nt':	// create new tag
            if (selected_order.length > 0) {
                var new_tag_name = prompt("Please enter a new tag name", "");
                // check to see if anything was entered
                // null == pressing cancel
                // ""   == entered nothing and pressing ok
                while (new_tag_name != null && trim_str(new_tag_name) == '') {
                    new_tag_name = prompt("Please enter a new tag name", "");
                }
                if (new_tag_name == null) {
                    selObj.selectedIndex = 0;
                    return;
                }
                act = 'perform_tagging';
                s_val = trim_str(new_tag_name);
            } else {
                msg = 'No orders selected.';
                showMainInfo(msg);
                selObj.selectedIndex = 0;
            }
            break;
        case 'rd':
        case 'ur':
            if (selected_order.length > 0) {
                act = 'perform_tagging';
                s_val = option_action == 'rd' ? '1' : '0';
                var msg = 'This order is set to ' + (option_action == 'rd' ? 'read' : 'unread') + '!';
                showMainInfo(msg);
            } else {
                msg = 'No orders selected.';
                showMainInfo(msg);
                selObj.selectedIndex = 0;
            }
            break;
        default:
            if (option_action.indexOf('otag_') != -1) {
                if (selected_order.length > 0) {
                    var temp_array = option_action.split('_');
                    option_action = 'at';
                    s_val = temp_array[1];
                    act = 'perform_tagging';
                } else {
                    msg = 'No orders selected.';
                    showMainInfo(msg);
                    selObj.selectedIndex = 0;
                }
            } else if (option_action.indexOf('rmtag_') != -1) {
                if (selected_order.length > 0) {
                    var temp_array = option_action.split('_');
                    option_action = 'rt';
                    s_val = temp_array[1];
                    act = 'perform_tagging';
                } else {
                    msg = 'No orders selected.';
                    showMainInfo(msg);
                    selObj.selectedIndex = 0;
                }
            }
            break;
    }

    if (trim_str(act) != '') {
        selObj.disabled = true;
        clearOptionList(selObj);
        selObj.options[0] = new Option('Loading ...', '');

        var ref_url = "buyback_xmlhttp.php?action=" + act + "&subaction=" + option_action + "&status_id=" + sid + "&setting=" + s_val + "&o_str=" + selected_order_str + "&lang=" + _lang + "&list_mode=" + (wl == true ? '2' : '1') + "&store=" + store;
        xmlhttp.open("GET", ref_url);
        xmlhttp.onreadystatechange = function() {
            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                //res = xmlhttp.responseText;
                clearOptionList(selObj);

                var tag_info = xmlhttp.responseXML.getElementsByTagName("tag_info")[0];
                if (typeof (tag_info) != 'undefined' && tag_info != null) {
                    var selection = tag_info.getElementsByTagName("selection")[0];
                    if (typeof (selection) != 'undefined' && selection != null) {
                        var option_sel = '';
                        for (var i = 0; i < selection.childNodes.length; i++) {
                            option_sel = selection.getElementsByTagName("option")[i];
                            appendToSelect(selObj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
                        }
                    }

                    var tag_details = tag_info.getElementsByTagName("tag_details")[0];
                    if (typeof (tag_details) != 'undefined' && tag_details != null) {
                        var order_tags = '';
                        for (var i = 0; i < tag_details.childNodes.length; i++) {
                            order_tags = tag_details.getElementsByTagName("order_tags")[i];
                            var cus_tag_str = DOMCall('tag_' + order_tags.getAttribute("order_id"));
                            if (cus_tag_str != null) {
                                cus_tag_str.innerHTML = order_tags.firstChild.nodeValue;
                            }
                        }
                    }

                    var read_mode = tag_info.getElementsByTagName("read_mode")[0];
                    if (typeof (read_mode) != 'undefined' && read_mode != null) {
                        var order_mode = '';
                        for (var i = 0; i < read_mode.childNodes.length; i++) {
                            order_mode = read_mode.getElementsByTagName("order_mode")[i];
                            var cus_order_row_obj = DOMCall('read_' + order_mode.getAttribute("order_id"));
                            if (cus_order_row_obj != null) {
                                cus_order_row_obj.className = option_action == 'ur' ? 'boldText' : '';
                            }
                        }
                    }
                }
                selObj.disabled = false;
            }
        }
        xmlhttp.send(null);
    }

    setTimeout('hideMainInfo()', 3000);
}

function insert_restock_char(buyback_request_id) {
    var server_action = 'get_restock_character';
    var ref_url = "buyback_xmlhttp.php?action=" + server_action + "&buyback_request_id=" + buyback_request_id;

    var rstk_name = prompt("Please enter Restock Character", "");

    if (rstk_name != null) {
        if (trim_str(rstk_name) != '') {
            document.getElementById('insert_restock_char_div').innerHTML = 'Please wait...';

            xmlhttp.open("GET", ref_url);
            xmlhttp.onreadystatechange = function() {
                if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                    var restock_character = xmlhttp.responseXML.getElementsByTagName("restock_character")[0];

                    if (restock_character.firstChild.data == '') {
                        document.getElementById('restock_char[' + buyback_request_id + ']').value = rstk_name;
                        document.partial_receive_form.submit();
                    } else {
                        document.getElementById('insert_restock_char_div').innerHTML = restock_character.firstChild.data;
                    }
                }
            }
            xmlhttp.send(null);
        } else {
            alert('Please enter Restock Character');
        }
    }
}

function doBBLocked(admin, o, _lang, act, element_id, btn_show_time) {
    if (pageLoaded == null || !pageLoaded)
        return;
    if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4)
        return;

    var objRef = DOMCall(element_id);
    var res;
    var user_msg = '';

    if (act == "ulo") {
        user_msg = prompt("Leave any message for unlocking this order?", "");
        if (user_msg != null) {
            //user_msg = URLEncode(user_msg);
        } else {
            return false;
        }
    }

    objRef.innerHTML = '';
    var msg = 'Loading... Please be patience!';
    showMainInfo(msg);

    var ref_url = "buyback_xmlhttp.php?action=show_buyback_lock_btn&subaction=" + act + "&adm=" + admin + "&oid=" + o + "&lang=" + _lang + "&log_comment=" + user_msg + "&from_time=" + btn_show_time;
    xmlhttp.open("GET", ref_url, asynMode);
    xmlhttp.onreadystatechange = function() {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            var A = (typeof (xmlhttp.responseXML.getElementsByTagName("action")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("action")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("action")[0].firstChild.data : '';

            if (act == 'l') {
                if (A == 'Prompt Alert Message') {
                    if (typeof (xmlhttp.responseXML.getElementsByTagName("lock_msg")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("lock_msg")[0] != null) {
                        var l_msg = xmlhttp.responseXML.getElementsByTagName("lock_msg")[0].firstChild.data
                    } else {
                        var l_msg = '';
                    }
                    alert(l_msg);

                    if (typeof (xmlhttp.responseXML.getElementsByTagName("close_win")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("close_win")[0] != null) {
                        if (xmlhttp.responseXML.getElementsByTagName("close_win")[0].firstChild.data == '1') {
                            if (document.all) {	// For IE
                                window.close();
                            } else {
                                window.open('', '_parent', '');
                                window.close();
                            }
                        }
                    }
                }

                window.location.reload();
            } else {
                showBtn(admin, o, _lang, element_id, A);
                lockingHistory(o, 0, 'locking_history');
            }
        }
    }

    xmlhttp.send(null);

    setTimeout('hideMainInfo()', 3000);
}

function showBtn(admin, o, _lang, element_id, act_msg) {
    var objRef = DOMCall(element_id);

    if (typeof (xmlhttp.responseXML.getElementsByTagName("lock_msg")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("lock_msg")[0] != null) {
        var l_msg = xmlhttp.responseXML.getElementsByTagName("lock_msg")[0].firstChild.data
    } else {
        var l_msg = '';
    }
    var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';
    var Sub_A = (typeof (xmlhttp.responseXML.getElementsByTagName("subaction")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("subaction")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("subaction")[0].firstChild.data : '';

    if (act_msg == "Show Lock Button") {

        var show_time = (typeof (xmlhttp.responseXML.getElementsByTagName("time")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("time")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("time")[0].firstChild.data : '';

        objRef.innerHTML = '<div id="navBtn" style="float:left;">' + "\n" +
                '<ul>' + "\n" +
                '<li id="lock_btn"><a href="javascript:;" onClick="doBBLocked(\'' + admin + '\', \'' + o + '\', \'' + _lang + '\', \'l\', \'' + element_id + '\', \'' + show_time + '\');" title="Locking this order">LOCK</a></li>' + "\n" +
                '</ul>' + "\n" +
                '</div>' + "\n" +
                '<div style="width:90%">' + "\n" +
                l_msg + "\n" +
                '</div>';

        DOMCall('comments_div').className = 'hide';
        DOMCall('char_div').className = 'hide';

        /*
         if (typeof(deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
         deliveryBoxObj.className = 'hide';
         }

         for (var p_cnt=0; p_cnt < unique_product_ref_array.length; p_cnt++) {
         var custom_info_box = DOMCall('custom_info_box_'+unique_product_ref_array[p_cnt]);
         if (typeof(custom_info_box) != 'undefined' && custom_info_box != null) {
         custom_info_box.className = 'hide';
         }
         }
         */

        //disabledFormInputs('partial_delivery', true);
    } else if (act_msg == "Show Unlock Button") {
        objRef.innerHTML = '<div id="navBtn" style="float:left;">' + "\n" +
                '<ul>' + "\n" +
                '<li id="unlock_btn"><a href="javascript:;" onClick="doLocked(\'' + admin + '\', \'' + o + '\', \'' + _lang + '\', \'' + (Sub_A == "Prompt For Unlocking Msg" ? 'ulo' : 'ul') + '\', \'' + element_id + '\');" title="Unlocking this order">UNLOCK</a></li>' + "\n" +
                '</ul>' + "\n" +
                '</div>' + "\n" +
                '<div style="width:90%">' + "\n" +
                l_msg + "\n" +
                '</div>' + "\n";

        DOMCall('comments_div').className = 'show';
        DOMCall('char_div').className = 'show';

        /*
         if (typeof(deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
         deliveryBoxObj.className = 'show';
         }

         for (var p_cnt=0; p_cnt < unique_product_ref_array.length; p_cnt++) {
         var custom_info_box = DOMCall('custom_info_box_'+unique_product_ref_array[p_cnt]);
         if (typeof(custom_info_box) != 'undefined' && custom_info_box != null) {
         custom_info_box.className = 'show';
         }
         }
         */

        //disabledFormInputs('compensate_form', false);
    } else if (act_msg == "Show Failed Lock Msg") {
        DOMCall('comments_div').className = 'hide';
        DOMCall('char_div').className = 'hide';

        /*
         if (typeof(deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
         deliveryBoxObj.className = 'hide';
         }

         for (var p_cnt=0; p_cnt < unique_product_ref_array.length; p_cnt++) {
         var custom_info_box = DOMCall('custom_info_box_'+unique_product_ref_array[p_cnt]);
         if (typeof(custom_info_box) != 'undefined' && custom_info_box != null) {
         DOMCall('custom_info_box_'+unique_product_ref_array[p_cnt]).className = 'hide';
         }
         }
         */

        //disabledFormInputs('partial_delivery', true);

        objRef.innerHTML = l_msg;
    }

    showMainInfo(R);
}

function lockingHistory(o, mode, element_id) {
    if (pageLoaded == null || !pageLoaded)
        return;
    if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4)
        return;

    var obj = DOMCall(element_id);
    if (obj == null)
        return;

    var element = '';
    if (element_id != 'locking_history')
        element = '&element=' + element_id;

    obj.className = 'hide';

    if (mode == 1) {
        var msg = 'Loading... Please be patience!';
        showMainInfo(msg);

        var ref_url = "buyback_xmlhttp.php?action=get_order_locking_history&oid=" + o + element;

        xmlhttp.open("GET", ref_url, asynMode);
        xmlhttp.onreadystatechange = function() {
            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                var R_C = (typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
                var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';

                if (R_C == 0) {
                    alert(R);
                } else if (R_C == 1) {
                    obj.innerHTML = '	<div>' + "\n" +
                            '		<a href="javascript:;" onClick="lockingHistory(\'' + o + '\', \'0\', \'' + element_id + '\');">Hide Locking History</a>' + "\n" +
                            '	</div>' + "\n" +
                            '	<div>' + R + '</div>';
                }

                obj.className = 'show';

                hideMainInfo();
            }
        }

        xmlhttp.send(null);
    } else {
        obj.innerHTML = '	<div>' + "\n" +
                '		<a href="javascript:;" onClick="lockingHistory(\'' + o + '\', \'1\', \'' + element_id + '\');">Show Locking History</a>' + "\n" +
                '	</div>';
        obj.className = 'show';
    }

    setTimeout('hideMainInfo()', 3000);
}

function cancel_delivery(delivery_id){
	
	var server_action = 'cancelDelivery';
	
    
    var user_msg = prompt("Leave your reason...", "");
	if (user_msg != null) {
		user_msg = encodeURIComponent(user_msg);
	} else {
		return false;
    }
    
    var msg = 'Loading... Please be patience!';
    showMainInfo(msg);
    var ref_url = 'buyback_xmlhttp.php?action='+server_action+'&delivery_id='+delivery_id+'&cancel_msg='+user_msg;

    var asynMode = true;
    xmlhttp.open("POST", ref_url, asynMode);
    xmlhttp.onreadystatechange = function() {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            hideMainInfo();
            window.location.reload();
        }
    }

        xmlhttp.send(null);
	
}