function doC2CBBLocked (admin, o, _lang, act, element_id, btn_show_time) {
	if (pageLoaded == null || !pageLoaded) return ;
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var objRef = DOMCall(element_id);
	var res;
    var user_msg = '';
    
    if (act == "ulo") {
		user_msg = prompt("Leave any message for unlocking this order?","");
		if (user_msg != null) {
			//user_msg = URLEncode(user_msg);
		} else {
			return false;
		}
	}
	
	objRef.innerHTML = '';
	var msg = 'Loading... Please be patience!';
	showMainInfo(msg);
	
	var ref_url = "c2c_buyback_order.php?action=show_buyback_lock_btn&subaction="+act+"&adm="+admin+"&oid="+o+"&lang="+_lang+"&log_comment="+user_msg+"&from_time="+btn_show_time;
	xmlhttp.open("GET", ref_url, asynMode); 
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		var A = (typeof (xmlhttp.responseXML.getElementsByTagName("action")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("action")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("action")[0].firstChild.data : '';
      		
      		if (act == 'l') {
      			if (A == 'Prompt Alert Message') {
      				if (typeof (xmlhttp.responseXML.getElementsByTagName("lock_msg")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("lock_msg")[0] != null) {
						var l_msg = xmlhttp.responseXML.getElementsByTagName("lock_msg")[0].firstChild.data
					} else {
						var l_msg = '';
					}
      				alert(l_msg);
      				
      				if (typeof (xmlhttp.responseXML.getElementsByTagName("close_win")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("close_win")[0] != null) {
						if (xmlhttp.responseXML.getElementsByTagName("close_win")[0].firstChild.data == '1') {
							if (document.all) {	// For IE
			      				window.close();
			      			} else {
				      			window.open('','_parent','');
								window.close();
							}
						}
					}
      			}
      			
      			window.location.reload();
      		} else {
    			showBtn(admin, o, _lang, element_id, A);
    			lockingHistory(o, 0, 'locking_history');
    		}
      	}
    }
    
    xmlhttp.send(null);
	
   	setTimeout('hideMainInfo()', 3000);
}