function get_seller_detail(id) {
    if (jQuery("#seller_detail").css("display") == "none") {
        var src = jQuery("#seller_detail_ico").attr("src").replace("expand", "collapse");
        jQuery("#seller_detail_ico").attr("src", src);

        if (jQuery('#seller_detail_init').val() == 0) {
            jQuery('#seller_detail').html('<img src="images/loading.gif" border="0" />');

            jQuery.getJSON('c2c_seller_product_listing.php?action=get_seller_detail&id=' + id, function(data) {
                var content = '';

                content += '<table border="0" cellpadding="2" cellspacing="2" width="100%">';
                content += '<tr>';
                content += '<td class="main" valign="top" width="195px"><b>' + ENTRY_SELLER + '</b></td>';
                content += '<td class="main" valign="top" width="5px"><b>:</b></td>';
                content += '<td class="main" align="left">' + data.seller_name + data.seller_address + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<td class="main" valign="top" width="195px"><b>' + ENTRY_SELLER_USERNAME + '</b></td>';
                content += '<td class="main" valign="top" width="5px"><b>:</b></td>';
                content += '<td class="main" align="left">' + data.seller_username + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<td class="main" valign="top" width="195px"><b>' + ENTRY_SELLER_STATUS + '</b></td>';
                content += '<td class="main" valign="top" width="5px"><b>:</b></td>';
                content += '<td class="main" align="left">' + data.seller_status + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<td class="main" valign="top" width="195px"><b>' + ENTRY_SELLER_GROUP + '</b></td>';
                content += '<td class="main" valign="top" width="5px"><b>:</b></td>';
                content += '<td class="main" align="left">' + data.seller_group_name + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<td class="main" valign="top" width="195px"><b>' + ENTRY_SELLER_GENDER + '</b></td>';
                content += '<td class="main" valign="top" width="5px"><b>:</b></td>';
                content += '<td class="main" align="left">' + data.customers_gender + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<td class="main" valign="top" width="195px"><b>' + ENTRY_SELLER_DOB + '</b></td>';
                content += '<td class="main" valign="top" width="5px"><b>:</b></td>';
                content += '<td class="main" align="left">' + data.seller_dob + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<td class="main" valign="top" width="195px"><b>' + ENTRY_SELLER_EMAIL + '</b></td>';
                content += '<td class="main" valign="top" width="5px"><b>:</b></td>';
                content += '<td class="main" align="left">' + data.customers_email_address + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<td class="main" valign="top" width="195px"><b>' + ENTRY_SELLER_MOBILE_PHONE + '</b></td>';
                content += '<td class="main" valign="top" width="5px"><b>:</b></td>';
                content += '<td class="main" align="left">' + data.seller_telephone + '</td>';
                content += '</tr>';
                content += '<tr>';
                content += '<td class="main" valign="top" width="195px"><b>' + ENTRY_SELLER_IM + '</b></td>';
                content += '<td class="main" valign="top" width="5px"><b>:</b></td>';
                content += '<td class="main" align="left">' + data.seller_im + '</td>';
                content += '</tr>';
                content += '</table>';

                jQuery('#seller_detail').html(content);
                jQuery('#seller_detail_init').val(1);
            });
        }
        jQuery('#seller_detail').css({
            display: 'block'
        });
    } else {
        var src = jQuery("#seller_detail_ico").attr("src").replace("collapse", "expand");
        jQuery("#seller_detail_ico").attr("src", src);

        jQuery("#seller_detail").css({
            display: 'none'
        });
    }
}

function get_product_listing_remark(id) {
    if (jQuery("#product_listing_remark").css("display") == "none") {
        var src = jQuery("#product_listing_remark_ico").attr("src").replace("expand", "collapse");
        jQuery("#product_listing_remark_ico").attr("src", src);

        var dmp = new diff_match_patch();

        if (jQuery('#product_listing_remark_init').val() == 0) {
            jQuery('#product_listing_remark').html('<img src="images/loading.gif" border="0" />');

            jQuery.getJSON('c2c_seller_product_listing.php?action=get_product_listing_remark&id=' + id, function(data) {
                var content = '';
                var cnt = data.length;

                content += '<table border="1" cellpadding="5" cellspacing="0" width="100%">';
                content += '<tr>';
                content += '<td class="smallText" valign="top" width="100px"><b>' + ENTRY_DATE + '</b></td>';
                content += '<td class="smallText" valign="top"><b>' + ENTRY_LOG_ACTION + '</b></td>';
                content += '<td class="smallText" valign="top"><b>' + ENTRY_REMARKS_BEFORE + '</b></td>';
                content += '<td class="smallText" valign="top"><b>' + ENTRY_REMARKS_AFTER + '</b></td>';
                content += '<td class="smallText" valign="top" width="200px"><b>' + ENTRY_ADDED_BY + '</b></td>';
                content += '<td class="smallText" valign="top" width="200px"><b>' + ENTRY_USER_ROLE + '</b></td>';
                content += '</tr>';

                if (cnt > 0) {
                    jQuery.each(data, function(i) {
                        var text1 = JSON.stringify(data[i].remarks_before_changes).replace(/\ /g, "").replace(/\,"/g, "\n").replace(/\"/g, "").replace("{", "").replace("}", "");
                        var text2 = JSON.stringify(data[i].remarks_after_changes).replace(/\ /g, "").replace(/\,"/g, "\n").replace(/\"/g, "").replace("{", "").replace("}", "");

                        dmp.Diff_EditCost = parseFloat(4);

                        var d = dmp.diff_main(text1, text2);
                        dmp.diff_cleanupEfficiency(d);
                        var ds = dmp.diff_prettyHtml(d);
//                        document.getElementById('outputdiv').innerHTML = ds;

                        content += '<tr class="' + data[i].tr_class + '">';
                        content += '<td class="smallText" valign="top" width="150px">' + data[i].remarks_added_date + '</td>';
                        content += '<td class="smallText" valign="top">' + data[i].log_action + '</td>';
                        content += '<td class="smallText" valign="top">' + text1.replace(/\n/g, "<br />") + '</td>';
                        content += '<td class="smallText" valign="top">' + ds + '</td>';
                        content += '<td class="smallText" valign="top" width="200px">' + data[i].remarks_added_by + '</td>';
                        content += '<td class="smallText" valign="top" width="200px">' + data[i].user_role + '</td>';
                        content += '</tr>';
                    });
                } else {
                    content += '<tr><td colspan="3">' + TEXT_NOT_AVAILABLE + '</td></tr>';
                }

                content += '</table>';

                jQuery('#product_listing_remark').html(content);
                jQuery('#product_listing_remark_init').val(1);
            });
        }
        jQuery('#product_listing_remark').css({
            display: 'block'
        });
    } else {
        var src = jQuery("#product_listing_remark_ico").attr("src").replace("collapse", "expand");
        jQuery("#product_listing_remark_ico").attr("src", src);

        jQuery("#product_listing_remark").css({
            display: 'none'
        });
    }
}


function get_reserved_qty_listing(id) {
    if (jQuery("#reserved_qty_listing").css("display") == "none") {
        var src = jQuery("#reserved_qty_listing_ico").attr("src").replace("expand", "collapse");
        jQuery("#reserved_qty_listing_ico").attr("src", src);

        if (jQuery('#reserved_qty_listing_init').val() == 0) {
            jQuery('#reserved_qty_listing_loading').html('<img src="images/loading.gif" border="0" />');

            jQuery.getJSON('c2c_seller_product_listing.php?action=get_reserved_qty_listing&id=' + id, function(data) {
                var content = '';
                var cnt = data.length;

                content += '<table border="1" cellpadding="5" cellspacing="0" width="400px">';
                content += '<tr>';
                content += '<td class="smallText" valign="top" width="200px"><b>' + TEXT_ORDER_ID + '</b></td>';
                content += '<td class="smallText" valign="top" width="200px"><b>' + ENTRY_PLI_RESERVED_QUANTITY + '</b></td>';
                content += '</tr>';

                if (cnt > 0) {
                    jQuery.each(data, function(i) {//
                        content += '<tr>';
                        content += '<td class="smallText" valign="top" width="200px">' + data[i].orders_id + '</td>';
                        content += '<td class="smallText" valign="top" width="200px">' + data[i].products_quantity + '</td>';
                        content += '</tr>';
                    });
                } else {
                    content += '<tr><td colspan="3">' + TEXT_NOT_AVAILABLE + '</td></tr>';
                }

                content += '</table>';

                jQuery('#reserved_qty_listing_loading').html(content);
                jQuery('#reserved_qty_listing_init').val(1);
            });
        }
        jQuery('#reserved_qty_listing').css({
            display: ''
        });
    } else {
        var src = jQuery("#reserved_qty_listing_ico").attr("src").replace("collapse", "expand");
        jQuery("#reserved_qty_listing_ico").attr("src", src);

        jQuery("#reserved_qty_listing").css({
            display: 'none'
        });
    }
}