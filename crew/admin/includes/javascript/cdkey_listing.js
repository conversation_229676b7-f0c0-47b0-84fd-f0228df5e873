function toggleCheckBox(obj_sel) {
    var cdkey_id;
    var cdk_count = 0;
    var total = 0;
    var subtotal = 0;
    if (obj_sel.checked === true) {
        jQuery('.cdkey_item').each(function () {
            cdkey_id = this.value;
            subtotal = parseFloat(jQuery('#selling_price_' + cdkey_id + '_div').text().replace(/[^0-9\.]+/g,''));
            if (!this.disabled) {
                this.checked = true;
                total = parseFloat(eval(total + '+' + subtotal));
                jQuery('#cdk_select_total').val(total.toFixed(4));
                jQuery('#cdk_select_total_div').html(total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                cdk_count++;
            }
        });
    } else {
        jQuery('.cdkey_item').each(function () {
            this.checked = false;
            jQuery('#cdk_select_total').val('0.0000');
            jQuery('#cdk_select_total_div').html('0.0000');
            cdk_count = 0;
        });
    }
    if (cdk_count > 1) {
        jQuery('#select_count').html(cdk_count + ' entries');
    } else {
        jQuery('#select_count').html(cdk_count + ' entry');
    }
    return true;
}

function calculateCdkeyTotal(obj_sel) {
    var cdkey_id = obj_sel.value;
    var cdkey_price = parseFloat(jQuery('#selling_price_' + cdkey_id + '_div').text().replace(/[^0-9\.]+/g,''));
    var subtotal_price = parseFloat(jQuery('#cdk_select_total').val());
    var new_total = 0;
    
    if (obj_sel.checked === true) {
        new_total = parseFloat(eval(subtotal_price + '+' + cdkey_price));
    } else {
        new_total = parseFloat(eval(subtotal_price + '-' + cdkey_price));
    }
    jQuery('#cdk_select_total').val(new_total.toFixed(4));
    jQuery('#cdk_select_total_div').html(new_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
    
    return true;
}

function Show_CDK_Image(CDK_id, owner_only) {
    var user_msg = '';
    
    jQuery('#Show_CDK_image_'+CDK_id).hide();
    
    if (jQuery('#CDK_image_'+CDK_id).html() == "<img src=images/loading.gif>" || jQuery('#CDK_image_'+CDK_id).html() == "") {
        jQuery('#CDK_image_'+CDK_id).show().html("<img src=images/loading.gif>");
        jQuery.ajax({
            url: "popup_cdkey_list.php?action=showcdk&cdk_id=" + CDK_id,
            type: 'GET',
            dataType: 'json',
            timeout: 10000,
            error: function () {
                alert('Error: No Data');
                jQuery('#CDK_image_'+CDK_id).html('');
                jQuery('#Show_CDK_image_'+CDK_id).show();
            },
            success: function (data) {
                if (data.result != 2) {
                    jQuery('#CDK_image_'+CDK_id).html(data.value);
                } else {
                    var user_msg = prompt("This CD Key is not uploaded by you, please enter your reason to view it:", "");

                    if (user_msg != null && user_msg != '') {
                        jQuery.ajax({
                            url: "popup_cdkey_list.php?action=showcdk&cdk_id=" + CDK_id + "&remark=" + user_msg,
                            type: 'GET',
                            dataType: 'json',
                            timeout: 10000,
                            error: function () {
                                alert('Error: No Data');
                                jQuery('#CDK_image_'+CDK_id).html('');
                                jQuery('#Show_CDK_image_'+CDK_id).show();
                            },
                            success: function (data) {
                                if (data.result != 2) {
                                    jQuery('#CDK_image_'+CDK_id).html(data.value);
                                } else {
                                    jQuery('#CDK_image_'+CDK_id).html('');
                                    jQuery('#Show_CDK_image_'+CDK_id).show();
                                }
                            }
                        });
                    } else {
                        jQuery('#CDK_image_'+CDK_id).html('');
                        jQuery('#Show_CDK_image_'+CDK_id).show();
                    }
                }
            }
        });
    } else {
        jQuery('#CDK_image_'+CDK_id).show();
    }

    return true;
}