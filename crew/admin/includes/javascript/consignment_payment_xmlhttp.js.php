<SCRIPT language="JavaScript" type="text/javascript">
<!--
    var remark_link_pool = Array();

    function DOMCall(name) {
        if (document.getElementById)
            return document.getElementById(name);
        else if (document.all)
            return document.all[name];
        else if (document.layers)
            return document.layers[name];
    }

    function initInfoCaptions() {
        // create a dynamic layer and attach it to the HTML document.
        objBody = document.getElementsByTagName("body").item(0);
        objContainer = document.createElement('div');
        objContainer.setAttribute('id', 'infocaption');

        objBody.appendChild(objContainer);
    }

    function appendToSelect(select, value, content, disabledMode) {
        var opt = document.createElement('option');
        opt.value = value;
        opt.text = content.replace(/&nbsp;/ig, String.fromCharCode(160));
        if (disabledMode != null) {
            opt.setAttribute('disabled', (disabledMode == 1 ? true : false));
        }
        select.options.add(opt);
    }

    function clearOptionList(obj) {
        while (obj.options.length > 0) {
            obj.remove(0);
        }
    }

    function showMainInfo(DisplayMsg) {
        hideMainInfo();
        var objInfo = DOMCall('infocaption');

        if (DisplayMsg == null || objInfo == null) {
            return;
        }

        var scroll = getScroll();

        objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
        objInfo.style.visibility = 'visible';

        objInfo.style.position = "absolute";

        objInfo.style.top = 5 + scroll.y;

        var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
        objInfo.style.left = msg_left_pos - 20;
    }

    function hideMainInfo() {
        var objInfo = DOMCall('infocaption');

        objInfo.style.visibility = 'hidden';
        objInfo.innerHTML = '';
    }

    function getWindowWidth() {
        var windowWidth = 0;
        if (typeof (window.innerWidth) == 'number') {
            windowWidth = window.innerWidth;
        } else {
            if (document.documentElement && document.documentElement.clientWidth) {
                windowWidth = document.documentElement.clientWidth;
            } else {
                if (document.body && document.body.clientWidth) {
                    windowWidth = document.body.clientWidth;
                }
            }
        }
        return windowWidth;
    }

    function getScroll() {
        if (document.body.scrollTop != undefined) { // IE model
            var ieBox = document.compatMode != "CSS1Compat";
            var cont = ieBox ? document.body : document.documentElement;
            return {x: cont.scrollLeft, y: cont.scrollTop};
        } else {
            return {x: window.pageXOffset, y: window.pageYOffset};
        }
    }

    function hideShow(groupName, styleClass) {
        var row_count = eval(groupName + "_count");
        for (var i = 0; i < row_count; i++) {
            document.getElementById(groupName + "_" + i).className = styleClass;
        }

        if (styleClass == "show") {
            document.getElementById(groupName + '_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('" + groupName + "', 'hide')\">Hide Details</a>";
            SetCookie(groupName, '1');
        } else {
            document.getElementById(groupName + '_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('" + groupName + "', 'show')\">Show Details</a>";
            SetCookie(groupName, '0');
        }
    }

    function showOverEffect(object, class_name, extra_row) {
        rowOverEffect(object, class_name);
        var rowObjArray = extra_row.split('##');
        for (var i = 0; i < rowObjArray.length; i++) {
            if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
            }
        }
    }

    function showOutEffect(object, class_name, extra_row) {
        rowOutEffect(object, class_name);
        var rowObjArray = extra_row.split('##');
        for (var i = 0; i < rowObjArray.length; i++) {
            if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
            }
        }
    }

    function showClicked(object, class_name, extra_row) {
        rowClicked(object, class_name);
        var rowObjArray = extra_row.split('##');
        for (var i = 0; i < rowObjArray.length; i++) {
            if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                rowClicked(document.getElementById(rowObjArray[i]), class_name);
            }
        }
    }

    function calculateUnitPrice(p_id, cdk_status) {
        var server_action = 'get_product_sell_price';
        
        if (cdk_status == 'cb') {
            var cdk_div = 'cb_';
        } else {
            var cdk_div = '';
        }
        
        var sell_curr = jQuery('#cdk_currency').val();
        var cdk_quantity = parseInt(jQuery('#cdk_'+cdk_div+'item_qty_'+p_id).val());
        var stock_type = jQuery('#stock_'+cdk_div+'price_type_'+p_id+' option:selected').val();
        var stock_val = parseFloat(jQuery('#unit_'+cdk_div+'price_'+p_id).val());
        var prod_total = 0;
        var sell_price = 0;
        
        if (cdk_quantity == '') { cdk_quantity = 0; jQuery('#cdk_'+cdk_div+'item_qty_'+p_id).val(0); }
        if (stock_val == '') { stock_val = 0; jQuery('#unit_'+cdk_div+'price_'+p_id).val(0); }

        jQuery.ajax({
            url: "consignment_payment_xmlhttp.php?action=" + server_action + "&p_id=" + p_id + "&sell_curr=" + sell_curr,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            error: function () {
                sell_price = 0;

                jQuery('#subtotal_'+cdk_div+p_id).val(0);
                jQuery('#subtotal_'+cdk_div+p_id+'_div').html('0.00');
            },
            success: function (xml) {
                sell_price = parseFloat(jQuery(xml).find('product_sell_price').text());
                if (stock_type == '%') {
                    var stock_unit_price = parseFloat(eval(sell_price.toFixed(8)+'*('+1+'-('+stock_val+'/'+100+'))'));
                    prod_total = parseFloat(eval(cdk_quantity+'*'+stock_unit_price.toFixed(8)));
                } else if (stock_type == '$') {
                    prod_total = parseFloat(eval(cdk_quantity+'*'+stock_val));
                }
                
                jQuery('#subtotal_'+cdk_div+p_id).val(prod_total.toFixed(4));
                jQuery('#subtotal_'+cdk_div+p_id+'_div').html(prod_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));

                refreshSubtotalCDKProduct();
            }
        });

        return true;
    }

    function calculateUnitPriceDTU(tp_id) {
        var server_action = 'get_product_sell_price';
        var p_id = jQuery('#dtu_product_id_'+tp_id).text();
        var sell_curr = jQuery('#dtu_currency').val();
        var dtu_quantity = parseInt(jQuery('#dtu_qty_'+tp_id).text());
        var prod_total = 0;
        var sell_price = 0;
        
        if (dtu_quantity == '') { dtu_quantity = 0; jQuery('#dtu_qty_'+tp_id).val(0); }

        jQuery.ajax({
            url: "consignment_payment_xmlhttp.php?action=" + server_action + "&p_id=" + p_id + "&sell_curr=" + sell_curr,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            error: function () {
                sell_price = 0;
                jQuery('#subtotal_' + tp_id).val(0);
                jQuery('#subtotal_' + tp_id + '_div').html('0.0000');
                jQuery('#sell_price_' + tp_id).val(0);
                jQuery('#sell_price_' + tp_id + '_div').html('0.0000');
            },
            success: function (xml) {
                sell_price = parseFloat(jQuery(xml).find('product_sell_price').text());
                prod_total = parseFloat(eval(dtu_quantity+'*'+sell_price));

                jQuery('#sell_price_' + tp_id).val(sell_price.toFixed(4));
                jQuery('#sell_price_' + tp_id + '_div').html(sell_price.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                jQuery('#subtotal_'+tp_id).val(prod_total.toFixed(4));
                jQuery('#subtotal_'+tp_id+'_div').html(prod_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));

                refreshDTUTotal();
            }
        });

        return true;
    }
    
    function refreshCDKTotal() {
        var po_id;
        var subtotal;
        var total = 0;
        
        jQuery(".po_consignment_id").each(function () {
            po_id = this.value;
            subtotal = parseFloat(jQuery('#subtotal_' + po_id + '_div').text().replace(/[^0-9\.]+/g,''));
            if (subtotal == '') {
                subtotal = 0;
            }
            if (this.checked == true) {
                total = parseFloat(eval(total + '+' + subtotal));
            }
        });
        
        jQuery('#cdk_select_total').val(total.toFixed(4));
        jQuery('#cdk_select_total_div').html(total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        
        if (total <= 0) {
            jQuery("#cdkCalculateButton").attr('disabled', 'disabled');
        } else {
            jQuery("#cdkCalculateButton").removeAttr('disabled');
        }

        return true;
    }

    function refreshSummaryTotal() {
        useCreditNote(DOMCall('cdk_use_credit_note'));
        return true;
    }
    
    function useCreditNote(selObj) {
        var cr_tbody = DOMCall('creditnote_row');
        var cr_amt = parseFloat(jQuery('#credit_note_amount').val());
        var dn_amt = parseFloat(jQuery('#cdk_dn').val());
        var taxtotal_amt = parseFloat(jQuery('#cdk_tax_total').val());
        var bankcharge_amt = parseFloat(jQuery('#cdk_pay_bankcharge').val());
        var adjustment_amt = parseFloat(jQuery('#cdk_pay_adjustment').val());
        var adjustment_type = parseFloat(jQuery('#adjustment_type').val());
        var confirm_rate = parseFloat(jQuery('#confirm_rate').val());
        var converted_cr_amt = 0;
        var cdk_dn_amt = 0;
        var cdk_bankcharge_amt = 0;
        var cdk_adjustment_amt = 0;
        var cr_amt_to_use = 0 ;
        var tax_bc_amt = 0;
        var bank_charge = false;
        var adjustment_charge = false;
        var dn_charge = false;
	
    	if (selObj.checked == true) {
            if (confirm_rate > 0) {
                cdk_dn_amt = parseFloat(eval(dn_amt + '*(' + 1 + '/' + confirm_rate + ')'));
                if (!cdk_dn_amt) { cdk_dn_amt = 0;}

                cdk_bankcharge_amt = parseFloat(eval(bankcharge_amt + '*(' + 1 + '/' + confirm_rate + ')'));
                cdk_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + confirm_rate + ')'));
                converted_cr_amt = parseFloat(eval(cr_amt + '*(' + 1 + '/' + confirm_rate + ')'));
                
                if(adjustment_type === 1) {
                    tax_bc_amt = taxtotal_amt - cdk_dn_amt + cdk_bankcharge_amt + cdk_adjustment_amt;
                } else {
                    tax_bc_amt = taxtotal_amt - cdk_dn_amt + cdk_bankcharge_amt - cdk_adjustment_amt;
                }
                
                if (converted_cr_amt >= tax_bc_amt) {
                    cr_amt_to_use = tax_bc_amt;
                } else {
                    cr_amt_to_use = converted_cr_amt;
                }
                
                var new_total_amt = tax_bc_amt;

                if (cdk_dn_amt > 0 ) {
                    dn_charge = true;
                }
                
                if (cdk_bankcharge_amt > 0) {
                    bank_charge = true;
                    jQuery('#cdk_bankcharge').val(cdk_bankcharge_amt.toFixed(4));
                    jQuery('#cdk_bankcharge_div').html(cdk_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                }
                
                if (cdk_adjustment_amt > 0) {
                    adjustment_charge = true;
                    jQuery('#cdk_adjustment').val(cdk_adjustment_amt.toFixed(4));
                    jQuery('#cdk_adjustment_div').html(cdk_adjustment_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                }
                
                jQuery('#cdk_creditnote').val(cr_amt_to_use.toFixed(4));
                jQuery('#cdk_creditnote_div').html(cr_amt_to_use.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                jQuery('#cdk_total').val(new_total_amt.toFixed(4));
                jQuery('#cdk_total_div').html(new_total_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                cr_tbody.className = 'show';
            }
    	} else {
            jQuery('#cdk_creditnote').val(0);
            jQuery('#cdk_creditnote_div').html('');
            jQuery('#cdk_total').val(taxtotal_amt.toFixed(4));
            jQuery('#cdk_total_div').html(taxtotal_amt.toFixed(4));
            cr_tbody.className = 'hide';
    	}
	   refreshPayableTotal(bank_charge, adjustment_charge, dn_charge);
	   return true;
    }

    function getGST(selObj) {
        var comp_code = selObj.value;
        var server_action = 'get_company_gst';
        var gst_tbody = DOMCall('gst_row');
        var cdk_curr = jQuery('#cdk_currency').val();
        var cdk_total_amt = 0;
        var gst_checkbox = DOMCall('include_gst');
        var include_gst = 0;
        
        if(jQuery('#cdk_cb').length == 0) {
            cdk_total_amt = parseFloat(jQuery('#cdk_subtotal').val());
        } else {
            var sub_total = parseFloat(jQuery('#cdk_subtotal').val());
            var cb_total = parseFloat(jQuery('#cdk_cb').val());
            cdk_total_amt = parseFloat(eval(sub_total + '-' + cb_total));
        }
        
        jQuery('#cdk_gst_currency').html(cdk_curr);

        if (gst_checkbox.checked == true) {
            include_gst = 1;
        }
        
        jQuery.ajax({
            url: "consignment_payment_xmlhttp.php?action=" + server_action + "&incl_gst=" + include_gst + "&comp_code=" + comp_code + '&total_amt=' + cdk_total_amt + '&cdk_curr=' + cdk_curr,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            error: function () {
                jQuery('#cdk_tax').val(0);
                jQuery('#cdk_tax_div').html('0.0000');
                jQuery('#cdk_tax_total').val(cdk_total_amt.toFixed(4));
                jQuery('#cdk_tax_total_div').html(cdk_total_amt.toFixed(4));
                gst_tbody.className = 'hide';
                refreshSummaryTotal();
            },
            success: function (xml) {
                var gst_val = parseFloat(jQuery(xml).find('gst_value').text());
                var gst_title = jQuery(xml).find('gst_title_text').text();
                var tax_val = parseFloat(jQuery(xml).find('tax_amount').text());
                var tax_total_val = parseFloat(jQuery(xml).find('after_tax_total').text());
                
                // if (gst_val > 0) {
                    jQuery('#gst_title_div').html(gst_title);
                    jQuery('#cdk_tax').val(tax_val.toFixed(4));
                    jQuery('#cdk_tax_div').html(tax_val.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                    jQuery('#cdk_tax_total').val(tax_total_val.toFixed(4));
                    jQuery('#cdk_tax_total_div').html(tax_total_val.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                    gst_tbody.className = 'show';
                // } else {
                //     jQuery('#cdk_tax').val(0);
                //     jQuery('#cdk_tax_div').html('0.0000');
                //     jQuery('#cdk_tax_total').val(cdk_total_amt.toFixed(4));
                //     jQuery('#cdk_tax_total_div').html(cdk_total_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                //     gst_tbody.className = 'hide';
                    
                // }
                refreshSummaryTotal();
            }
        });

        return true;
    }

    function refreshSubtotalCDKProduct() {
        var p_id;
        var subtotal;
        var total = 0;
        var total_cb = 0;
        var new_total = 0;
        var new_total_cb = 0;
	
        jQuery(".cdk_items_prod_id").each (function() {
            p_id = this.value;
            subtotal = parseFloat(jQuery('#subtotal_'+p_id).val());
            if (subtotal == '') { subtotal = 0; }
            total = parseFloat(eval(total + '+' + subtotal));
        });
        
        if(jQuery('#include_cb').is(':checked')) {
            jQuery(".cdk_cb_items_prod_id").each (function() {
                p_id = this.value;
                subtotal = parseFloat(jQuery('#subtotal_cb_'+p_id).val());
                if (subtotal == '') { subtotal = 0; }
                total_cb = parseFloat(eval(total_cb + '+' + subtotal));
            });
        }
        
        new_total = total;
        new_total_cb = parseFloat(eval(total + '-' + total_cb));
	
        jQuery('#cdk_subtotal').val(new_total.toFixed(4));
        jQuery('#cdk_subtotal_div').html(new_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        jQuery('#cdk_cb').val(total_cb.toFixed(4));
        jQuery('#cdk_cb_div').html(total_cb.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        jQuery('#cdk_cb_total').val(new_total_cb.toFixed(4));
        jQuery('#cdk_cb_total_div').html(new_total_cb.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
	
        getGST(DOMCall('cdk_delivery_address'));
	
        return true;
    }
    
    function refreshPayableTotal(bank_charge, adjustment_charge, dn_charge) {
        var dn_amt = parseFloat(jQuery('#cdk_dn').val());
        var bankcharge_amt = parseFloat(jQuery('#cdk_pay_bankcharge').val());
        var adjustment_amt = parseFloat(jQuery('#cdk_pay_adjustment').val());
        var adjustment_type = parseFloat(jQuery('#adjustment_type').val());
        var creditnote_amt = parseFloat(jQuery('#cdk_creditnote').val());
        var convert_rate = parseFloat(jQuery('#confirm_rate').val());
        var total = parseFloat(jQuery('#cdk_total').val());
        
        var cdk_dn_amt = 0;
        var cdk_bankcharge_amt = 0;
        var cdk_adjustment_amt = 0;
        var payable_total = 0;
        
        var adjustment_formula = (adjustment_type === 1) ? '+' : '-';

        if (bankcharge_amt > 0 && bank_charge === false && adjustment_amt > 0 && adjustment_charge === false && dn_amt > 0 && dn_charge === false) {
            cdk_dn_amt = parseFloat(eval(dn_amt + '*(' + 1 + '/' + convert_rate + ')'));
            cdk_bankcharge_amt = parseFloat(eval(bankcharge_amt + '*(' + 1 + '/' + convert_rate + ')'));
            cdk_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + convert_rate + ')'));
            
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(payable_total + '-' + cdk_dn_amt + '+' + cdk_bankcharge_amt + adjustment_formula + cdk_adjustment_amt + '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + '-' + cdk_dn_amt + '+' + cdk_bankcharge_amt + adjustment_formula + cdk_adjustment_amt));
            }

            jQuery('#cdk_bankcharge').val(cdk_bankcharge_amt.toFixed(4));
            jQuery('#cdk_bankcharge_div').html(cdk_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#cdk_adjustment').val(cdk_adjustment_amt.toFixed(4));
            jQuery('#cdk_adjustment_div').html(cdk_adjustment_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#cdk_payable_total').val(payable_total.toFixed(4));
            jQuery('#cdk_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else if (bankcharge_amt > 0 && bank_charge === false && adjustment_amt > 0 && adjustment_charge === false) {
            cdk_bankcharge_amt = parseFloat(eval(bankcharge_amt + '*(' + 1 + '/' + convert_rate + ')'));
            cdk_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + convert_rate + ')'));
            
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(payable_total + '+' + cdk_bankcharge_amt + adjustment_formula + cdk_adjustment_amt + '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + '+' + cdk_bankcharge_amt + adjustment_formula + cdk_adjustment_amt));
            }

            jQuery('#cdk_bankcharge').val(cdk_bankcharge_amt.toFixed(4));
            jQuery('#cdk_bankcharge_div').html(cdk_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#cdk_adjustment').val(cdk_adjustment_amt.toFixed(4));
            jQuery('#cdk_adjustment_div').html(cdk_adjustment_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#cdk_payable_total').val(payable_total.toFixed(4));
            jQuery('#cdk_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else if (adjustment_amt > 0 && adjustment_charge === false && dn_amt > 0 && dn_charge === false) {
            cdk_dn_amt = parseFloat(eval(dn_amt + '*(' + 1 + '/' + convert_rate + ')'));
            cdk_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + convert_rate + ')'));
            
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(payable_total + '-' + cdk_dn_amt + adjustment_formula + cdk_adjustment_amt + '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + '-' + cdk_dn_amt + adjustment_formula + cdk_adjustment_amt));
            }

            if (bank_charge === false) {
                jQuery('#cdk_bankcharge').val(0);
                jQuery('#cdk_bankcharge_div').html('0.0000');
            }

            jQuery('#cdk_adjustment').val(cdk_adjustment_amt.toFixed(4));
            jQuery('#cdk_adjustment_div').html(cdk_adjustment_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#cdk_payable_total').val(payable_total.toFixed(4));
            jQuery('#cdk_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else if (bankcharge_amt > 0 && bank_charge === false && dn_amt > 0 && dn_charge === false) {
            cdk_dn_amt = parseFloat(eval(dn_amt + '*(' + 1 + '/' + convert_rate + ')'));
            cdk_bankcharge_amt = parseFloat(eval(bankcharge_amt + '*(' + 1 + '/' + convert_rate + ')'));
            
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(payable_total + '-' + cdk_dn_amt + '+' + cdk_bankcharge_amt + '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + '-' + cdk_dn_amt + '+' + cdk_bankcharge_amt));
            }

            if (adjustment_charge === false) {
                jQuery('#cdk_adjustment').val(0);
                jQuery('#cdk_adjustment_div').html('0.0000');
            }

            jQuery('#cdk_bankcharge').val(cdk_bankcharge_amt.toFixed(4));
            jQuery('#cdk_bankcharge_div').html(cdk_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#cdk_payable_total').val(payable_total.toFixed(4));
            jQuery('#cdk_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else if (adjustment_amt > 0 && adjustment_charge === false) {
            cdk_adjustment_amt = parseFloat(eval(adjustment_amt + '*(' + 1 + '/' + convert_rate + ')'));
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(payable_total + adjustment_formula + cdk_adjustment_amt + '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + adjustment_formula + cdk_adjustment_amt));
            }
            
            if (bank_charge === false) {
                jQuery('#cdk_bankcharge').val(0);
                jQuery('#cdk_bankcharge_div').html('0.0000');
            }

            jQuery('#cdk_adjustment').val(cdk_adjustment_amt.toFixed(4));
            jQuery('#cdk_adjustment_div').html(cdk_adjustment_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#cdk_payable_total').val(payable_total.toFixed(4));
            jQuery('#cdk_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else if (bankcharge_amt > 0 && bank_charge === false) {
            cdk_bankcharge_amt = parseFloat(eval(bankcharge_amt + '*(' + 1 + '/' + convert_rate + ')'));
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(payable_total + '+' + cdk_bankcharge_amt +  '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + '+' + cdk_bankcharge_amt));
            }
            
            if (adjustment_charge === false) {
                jQuery('#cdk_adjustment').val(0);
                jQuery('#cdk_adjustment_div').html('0.0000');
            }

            jQuery('#cdk_bankcharge').val(cdk_bankcharge_amt.toFixed(4));
            jQuery('#cdk_bankcharge_div').html(cdk_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#cdk_payable_total').val(payable_total.toFixed(4));
            jQuery('#cdk_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else if (dn_amt > 0 && dn_charge === false) {
            cdk_dn_amt = parseFloat(eval(dn_amt + '*(' + 1 + '/' + convert_rate + ')'));
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(payable_total + '-' + cdk_dn_amt +  '-' + creditnote_amt));
            } else {
                payable_total = parseFloat(eval(total + '-' + cdk_dn_amt));
            }
            
            if (bank_charge === false) {
                jQuery('#cdk_bankcharge').val(0);
                jQuery('#cdk_bankcharge_div').html('0.0000');
            }
            
            if (adjustment_charge === false) {
                jQuery('#cdk_adjustment').val(0);
                jQuery('#cdk_adjustment_div').html('0.0000');
            }

            jQuery('#cdk_bankcharge').val(cdk_bankcharge_amt.toFixed(4));
            jQuery('#cdk_bankcharge_div').html(cdk_bankcharge_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
            jQuery('#cdk_payable_total').val(payable_total.toFixed(4));
            jQuery('#cdk_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        } else {
            if(creditnote_amt > 0) {
                payable_total = parseFloat(eval(total + '-' + creditnote_amt));
            } else {
                payable_total = total;
            }
            
            if (bank_charge === false) {
                jQuery('#cdk_bankcharge').val(0);
                jQuery('#cdk_bankcharge_div').html('0.0000');
            }
            
            if (adjustment_charge === false) {
                jQuery('#cdk_adjustment').val(0);
                jQuery('#cdk_adjustment_div').html('0.0000');
            }
            
            jQuery('#cdk_payable_total').val(payable_total.toFixed(4));
            jQuery('#cdk_payable_total_div').html(payable_total.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        }

        return true;
    }

    function refreshGST(selObj) {
        if (selObj.checked == false) {
            jQuery('#cdk_tax').val(0);
            jQuery('#cdk_tax_div').html('0.00');
            jQuery('#cdk_tax_total').val(jQuery('#cdk_payable_total').val());
            jQuery('#cdk_tax_total_div').html(jQuery('#cdk_payable_total').val());
        }
        getGST(DOMCall('cdk_delivery_address'));
        return true;
    }
    
    function refreshCB(selObj) {
        var quantity;
        var cb_quanity;
        var new_quantity = 0;
        
        var sub_total = parseFloat(jQuery('#cdk_subtotal').val());
        if (selObj.checked == false) {
            
            jQuery(".cdk_cb_items_prod_id").each (function() {
                p_id = this.value;
                subtotal = parseFloat(jQuery('#subtotal_cb_'+p_id).val());
                quantity = parseFloat(jQuery('#cdk_item_qty_'+p_id).val());
                cb_quantity = parseFloat(jQuery('#cdk_cb_item_qty_'+p_id).val());
                new_quanity = parseFloat(eval(quantity + '-' + cb_quantity));

                jQuery('#cdk_item_qty_'+p_id).val(new_quanity.toFixed(0));
                jQuery('#cdk_item_qty_'+p_id+'_div').html(new_quanity.toFixed(0));

                calculateUnitPrice(p_id, '');
            });
            
            jQuery('#cdk_cb').val(0);
            jQuery('#cdk_cb_div').html('0.0000');
            jQuery('#cdk_cb_total').val(sub_total.toFixed(4));
            jQuery('#cdk_cb_total_div').html(sub_total.toFixed(4));
        } else {
            var p_id;
            var subtotal;
            var total = 0;
            var new_total = 0;

            jQuery(".cdk_cb_items_prod_id").each (function() {
                    p_id = this.value;
                    subtotal = parseFloat(jQuery('#subtotal_cb_'+p_id).val());
                    quantity = parseFloat(jQuery('#cdk_item_qty_'+p_id).val());
                    cb_quantity = parseFloat(jQuery('#cdk_cb_item_qty_'+p_id).val());
                    new_quanity = parseFloat(eval(quantity + '+' + cb_quantity));
                    
                    jQuery('#cdk_item_qty_'+p_id).val(new_quanity.toFixed(0));
                    jQuery('#cdk_item_qty_'+p_id+'_div').html(new_quanity.toFixed(0));
                    
                    calculateUnitPrice(p_id, '');
                    
                    if (subtotal == '') { subtotal = 0; }
                    total = parseFloat(eval(total + '+' + subtotal));
            });

            new_total = parseFloat(eval(sub_total + '-' + total));
            
            jQuery('#cdk_cb').val(total.toFixed(4));
            jQuery('#cdk_cb_div').html(total.toFixed(4));
            jQuery('#cdk_cb_total').val(new_total.toFixed(4));
            jQuery('#cdk_cb_total_div').html(new_total.toFixed(4));
        }
        
        getGST(DOMCall('cdk_delivery_address'));
        
        return true;
    }
    
    function refreshDN(selObj) {
        var tax_total = parseFloat(jQuery('#cdk_tax_total').val());
        if (selObj.checked == false) {
            jQuery('#cdk_dn').val(0);
            jQuery('#cdk_dn_div').html('0.0000');
            jQuery('#cdk_dn_total').val(tax_total.toFixed(2));
            jQuery('#cdk_dn_total_div').html(tax_total.toFixed(2));
        } else {
            var p_id;
            var subtotal;
            var total = 0;
            var new_total = 0;

            jQuery(".cdk_dn_items_prod_id").each (function() {
                    p_id = this.value;
                    subtotal = parseFloat(jQuery('#subtotal_dn_'+p_id).val());
                    if (subtotal == '') { subtotal = 0; }
                    total = parseFloat(eval(total + '+' + subtotal));
            });

            new_total = parseFloat(eval(tax_total + '-' + total));
            
            jQuery('#cdk_dn').val(total.toFixed(4));
            jQuery('#cdk_dn_div').html(total.toFixed(4));
            jQuery('#cdk_dn_total').val(new_total.toFixed(4));
            jQuery('#cdk_dn_total_div').html(new_total.toFixed(4));
        }
        
        getGST(DOMCall('cdk_delivery_address'));
        
        return true;
    }
    
    function orderListsOptions(selObj, sid, _lang, o, wl) {
        var msg = '';
        var rExp = /_tag_selector/gi;
        var status_name = selObj.name.replace(rExp, '');
        var option_action = selObj.value;
        if (o != null && o != '') {
            var selected_order = new Array(o);
        } else {
            var selected_order = new Array();
            var order_str_obj = DOMCall(status_name + '_order_str');
            if (order_str_obj != null) {
                var order_product_ids_array = order_str_obj.value.split(',');
                for (i = 1; i <= order_product_ids_array.length; i++) {
                    var order_row = DOMCall(status_name + '_main_' + i);
                    if (order_row != null && order_row.className == "rowSelected") {
                        selected_order.push(order_product_ids_array[i - 1]);
                    }
                }
            }
        }
        var selected_order_str = selected_order.join(',');
        var act = '';
        var s_val = '';
        switch (option_action) {
            case 'nt':	// create new tag
                if (selected_order.length > 0) {
                    var new_tag_name = prompt("Please enter a new tag name", "");
                    // check to see if anything was entered
                    // null == pressing cancel
                    // ""   == entered nothing and pressing ok
                    while (new_tag_name != null && trim_str(new_tag_name) == '') {
                        new_tag_name = prompt("Please enter a new tag name", "");
                    }
                    if (new_tag_name == null) {
                        selObj.selectedIndex = 0;
                        return;
                    }
                    act = 'perform_tagging';
                    s_val = trim_str(new_tag_name);
                } else {
                    msg = 'No purchase order record selected.';
                    showMainInfo(msg);
                    selObj.selectedIndex = 0;
                }
                break;
            default:
                if (option_action.indexOf('otag_') != -1) {
                    if (selected_order.length > 0) {
                        var temp_array = option_action.split('_');
                        option_action = 'at';
                        s_val = temp_array[1];
                        act = 'perform_tagging';
                    } else {
                        msg = 'No purchase order record selected.';
                        showMainInfo(msg);
                        selObj.selectedIndex = 0;
                    }
                } else if (option_action.indexOf('rmtag_') != -1) {
                    if (selected_order.length > 0) {
                        var temp_array = option_action.split('_');
                        option_action = 'rt';
                        s_val = temp_array[1];
                        act = 'perform_tagging';
                    } else {
                        msg = 'No purchase order record selected.';
                        showMainInfo(msg);
                        selObj.selectedIndex = 0;
                    }
                }
                break;
        }

        if (trim_str(act) != '') {
            selObj.disabled = true;
            clearOptionList(selObj);
            selObj.options[0] = new Option('Loading ...', '');

            jQuery.ajax({
                url: "consignment_payment_xmlhttp.php?action=" + act + "&subaction=" + option_action + "&status_id=" + sid + "&setting=" + s_val + "&po_str=" + selected_order_str + "&lang=" + _lang + "&list_mode=" + (wl == true ? '2' : '1'),
                type: 'GET',
                dataType: 'xml',
                timeout: 10000,
                error: function () {

                },
                success: function (xml) {
                    clearOptionList(selObj);
                    var tag_info = jQuery(xml).find('tag_info').text();
                    if (typeof (tag_info) != 'undefined' && tag_info != null) {
                        var selection = jQuery(xml).find('selection').text();
                        if (typeof (selection) != 'undefined' && selection != null) {
                            var option_sel = '';
                            jQuery(xml).find("option").each(function () {
                                option_sel = jQuery(this).text();
                                appendToSelect(selObj, jQuery(this).attr("index"), option_sel, jQuery(this).attr("disabled"));
                            });
                        }

                        var tag_details = jQuery(xml).find('tag_details').text();
                        if (typeof (tag_details) != 'undefined' && tag_details != null) {
                            var order_tags = '';
                            jQuery(xml).find("order_tags").each(function () {
                                var current_tag = jQuery(this).text();
                                var po_id = jQuery(this).attr("order_id");
                                jQuery('#tag_' + po_id).html(current_tag);
                            });
                        }

                    }
                    selObj.disabled = false;
                }
            });
        }
        setTimeout('hideMainInfo()', 3000);
    }

    function setCBDTUChecked(topup_id) {
        jQuery('#dtu_items_top_up_id_'+topup_id).each(function () {
           this.checked = true; 
        });
        return true;
    }
    
    function getSupplierInfo(ref, payment_id, new_disburse, address_div, payment_div, delivery_div, delivery_set, currency_set, pm_set, po_id_array, rate_refresh) {
        var obj_ref = ref;
        var address_res;
        var payment_res;
        var delivery_res;
        var option_selection;
        var supp_locked_html;
        var server_action = 'get_supplier_info';
        
        if (obj_ref.value != '') {
            jQuery.ajax({
                url: "consignment_payment_xmlhttp.php?action=" + server_action + "&s_id=" + obj_ref.value + "&pay_id=" + payment_id + "&delivery_set=" + delivery_set + "&currency_set=" + currency_set,
                type: 'GET',
                dataType: 'xml',
                timeout: 10000,
                error: function () {
                    jQuery('#' + address_div).html('Error loading XML document');
                },
                success: function (xml) {
                    address_res = jQuery(xml).find('supplier_address').text();
                    payment_res = jQuery(xml).find('supplier_payment').text();
                    delivery_res = jQuery(xml).find('supplier_delivery').text();
                    supp_locked_html = jQuery(xml).find('supplier_locked').text();
                    option_selection = jQuery(xml).find('option_selection').text();
                    jQuery('#cdk_payment_supplier_lock_div').html(supp_locked_html);
                    jQuery('#' + address_div).html(address_res);
                    jQuery('#' + payment_div).html(payment_res);
                    jQuery('#' + delivery_div).html(delivery_res);
                    jQuery('#po_payment_currency_div').html(option_selection);
                    jQuery('#cdk_supplier_disbursement').html('');
                    jQuery('#suggest_rate').val('');
                    jQuery('#suggest_rate_text').html('');
                    jQuery('#confirm_rate').val('');
                    clearPOList();
                    if (rate_refresh === true) {
                        getPOPayMethodByCurrency(document.cdk_form.cdk_currency, pm_set, po_id_array, new_disburse, rate_refresh);
                    }
                }
            });
        } else {
            jQuery('#cdk_payment_supplier_lock_div').html('');
            jQuery('#' + address_div).html('');
            jQuery('#' + payment_div).html('');
            jQuery('#' + delivery_div).html('');
            jQuery('#po_payment_currency_div').html('');
        }

        return true;
    }

    function getSupplierSummary(supplier_id, currency, mode) {
        var supplier_summary;
        var success_html;
        var error_html;
        var sdate = jQuery('#start_date').val();
        var edate = jQuery('#end_date').val();
        var cdk_status = jQuery('#cdk_status').val();
        var server_action = 'get_supplier_summary';
        
        if (mode == '1') {
            jQuery.ajax({
                url: "consignment_payment_xmlhttp.php?action=" + server_action + "&supp_id=" + supplier_id + "&curr_id=" + currency + "&s_date=" + sdate + "&e_date=" + edate + "&cdk_status=" + cdk_status,
                type: 'GET',
                dataType: 'xml',
                timeout: 30000,
                error: function () {
                    error_html = '<a href="javascript:;" id="supplier_' + supplier_id + '_' + currency + '" onclick="getSupplierSummary(\'' + supplier_id + '\', \'' + currency + '\', \'1\')"><img src="/images/icons/expand_arrow.gif"></a>';
                    jQuery('#' + supplier_id + '_' + currency + '_td').html(error_html);
                },
                success: function (xml) {
                    supplier_summary = jQuery(xml).find('supplier_summary').text();
                    jQuery('#' + supplier_id + '_' + currency + '_div').html(supplier_summary);
                    
                    success_html = '<a href="javascript:;" id="supplier_' + supplier_id + '_' + currency + '" onclick="getSupplierSummary(\'' + supplier_id + '\', \'' + currency + '\', \'0\')"><img style="-moz-transform: scaleY(-1);-o-transform: scaleY(-1);-webkit-transform: scaleY(-1);transform: scaleY(-1);filter: FlipV;-ms-filter: "FlipV";}" src="/images/icons/expand_arrow.gif"></a>';
                    jQuery('#' + supplier_id + '_' + currency + '_td').html(success_html);

                    var order_tbody = DOMCall(supplier_id + '_' + currency + '_tbody');
                    order_tbody.className = 'show';
                    
                }
            });
        } else {
            success_html = '<a href="javascript:;" id="supplier_' + supplier_id + '_' + currency + '" onclick="getSupplierSummary(\'' + supplier_id + '\', \'' + currency + '\', \'1\')"><img src="/images/icons/expand_arrow.gif"></a>';
            jQuery('#' + supplier_id + '_' + currency + '_td').html(success_html);

            var order_tbody = DOMCall(supplier_id + '_' + currency + '_tbody');
            order_tbody.className = 'hide';
        }

        return true;
    }
    
    function clearPOList() {
        var form_type = jQuery('#cdk_list_type').val();
        var row_html = '';
        
        row_html += '<table id="new_cdk_products" border="0" width="100%" cellspacing="1" cellpadding="4">';
        row_html += '    <tbody>';
        row_html += '        <tr>';
        row_html += '            <td class="invoiceBoxHeading" align="center" width="4%">#</td>';
        if (form_type == 'create_blank_cdk') {
            row_html += '            <td class="invoiceBoxHeading" align="center" width="5%"><?= TABLE_HEADING_CDK_SELECT ?></td>';
        }
        row_html += '            <td class="invoiceBoxHeading" align="center" width="10%"><?= TABLE_HEADING_CDK_DATE ?></td>';
        row_html += '            <td class="invoiceBoxHeading" align="center" width="15%"><?= TABLE_HEADING_CDK_PO_NUMBER ?></td>';
        row_html += '            <td class="invoiceBoxHeading" align="center" width="10%"><?= TABLE_HEADING_CDK_PO_QUANTITY ?></td>';
        row_html += '            <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_po_amount_title"><?=sprintf(TABLE_HEADING_CDK_PO_AMOUNT,(isset($input_array['po_currency'])? $input_array['po_currency'] : DEFAULT_CURRENCY))?></div></td>';
        if (form_type == 'create_blank_cdk') {
            row_html += '            <td class="invoiceBoxHeading" align="center" width="10%"><?= TABLE_HEADING_CDK_QUANTITY ?></td>';
            row_html += '            <td class="invoiceBoxHeading" align="center" width="10%"><div id="total_price_title"><?=sprintf(TABLE_HEADING_CDK_AMOUNT,(isset($input_array['po_currency'])? $input_array['po_currency'] : DEFAULT_CURRENCY))?></div></td>';
        }
        row_html += '            <td class="invoiceBoxHeading" align="center" width="10%"><?= TABLE_HEADING_CDK_BILLING_STATUS ?></td>';
        row_html += '        </tr>';
        row_html += '    </tbody>';
        row_html += '</table>';
        
        jQuery('#cdk_list_div').html(row_html);
        
        jQuery('#cdk_select_total').val('0.0000');
        jQuery('#cdk_select_total_div').html('0.0000');
        
        jQuery("#cdkCalculateButton").attr('disabled', 'disabled');
        
        return true;
    }
    
    function getPOList(supplier_id, po_id_array, rate_refresh) {
        var res;
        var po_amt;
        var sdate = jQuery('#start_date').val();
        var edate = jQuery('#end_date').val();
        var curr_id = jQuery('#cdk_currency').val();
        var form_type = jQuery('#cdk_list_type').val();
        var server_action = 'get_supplier_po';
        
        jQuery.ajax({
            url: "consignment_payment_xmlhttp.php?action=" + server_action + "&s_id=" + supplier_id + "&c_id=" + curr_id + "&reload=" + rate_refresh + "&form_type=" + form_type + "&s_date=" + sdate + "&e_date=" + edate,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            Error: function(){
                jQuery('#cdk_list_div').html('Error loading XML document');
            },
            success: function(xml){
                res = jQuery(xml).find('po_list').text();
                jQuery('#cdk_list_div').html(res);
                if (rate_refresh === true) {
                    var arrayLength = po_id_array.length;
                    for (var i = 0; i < arrayLength; i++) {
                        updateTablePOList(po_id_array[i]);
                    }
                }
                if (form_type!='create_blank_cdk') {
                    po_amt = parseFloat(jQuery(xml).find('po_list_amount').text());
                    jQuery('#cdk_select_total').val(po_amt.toFixed(4));
                    jQuery('#cdk_select_total_div').html(po_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
                }
            }
        });
        
        return true;
    }
    
    function updateTablePOList(po_id) {
        var res_qty = 0;
        var res_subtotal = 0;
        var curr_id = jQuery('#cdk_currency').val();
        var server_action = 'update_supplier_po';
        
        jQuery.ajax({
            url: "consignment_payment_xmlhttp.php?action=" + server_action + "&po_id=" + po_id + "&c_id=" + curr_id,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            Error: function(){
                jQuery('#cdk_list_div').html('Error loading XML document');
            },
            success: function(xml){
                res_qty = jQuery(xml).find('cdkey_quantity').text();
                res_subtotal = jQuery(xml).find('cdkey_total').text();
                jQuery('#po_select_quantity_' + po_id).html(res_qty);
                jQuery('#subtotal_' + po_id + '_div').html(res_subtotal);
                jQuery('#po_consignment_id_' + po_id).attr('checked', true);
                refreshCDKTotal();
            }
        });
        
        return true;
    }
    
    function getPOPayMethodByCurrency(curr_sel, pm_set, po_id_array, new_disburse, rate_refresh) {
    	var obj_ref = curr_sel;
    	var sup_obj = DOMCall('cdk_supplier');
    	var res;
    	var server_action = 'get_po_supplier_pay_method';
        
        if (obj_ref.value != '') {
            jQuery.ajax({
                url: "consignment_payment_xmlhttp.php?action=" + server_action + "&s_id=" + sup_obj.value + "&c_id=" + obj_ref.value + "&pm_set=" + pm_set,
                type: 'GET',
                dataType: 'xml',
                timeout: 10000,
                Error: function(){
                    jQuery('#cdk_supplier_disbursement').html('Error loading XML document');
                },
                success: function(xml){
                    res = jQuery(xml).find('option_selection').text();
                    jQuery('#cdk_supplier_disbursement').html(res);
                    if (rate_refresh === true) {
                        getPOPayForPO(document.cdk_form.cdk_supplier_payment, po_id_array, new_disburse, rate_refresh);
                    }
                }
            });
    	} else {
            jQuery('#cdk_supplier_disbursement').html('');
            jQuery('#cdk_new_supplier_disbursement').html('');
            jQuery('#suggest_rate').val('');
            jQuery('#suggest_rate_text').html('');
            jQuery('#confirm_rate').val('');
            clearPOList();
        }
	   return true;
    }
    
    function getPOPayForPO(curr_sel, po_id_array, new_disburse, rate_refresh) {
        var obj_ref = curr_sel;
        var server_action = 'get_all_disbursement_methods';
        var supplier_id = jQuery('#cdk_supplier').val();
        var dis_method = jQuery('#cdk_supplier_payment').val();
        var curr_id = jQuery('#cdk_currency').val();
        
        if (obj_ref.value != '') {
            if (checkCDKform()) {
                getPOList(supplier_id, po_id_array, rate_refresh);
            }

            // Get all Disbursement method list
            jQuery.ajax({
                url: "consignment_payment_xmlhttp.php?action=" + server_action + "&s_id=" + supplier_id + "&dis_method=" + dis_method + "&curr_id=" + curr_id + "&reload_dis=" + new_disburse,
                type: 'GET',
                dataType: 'xml',
                timeout: 10000,
                Error: function(){
                    jQuery('#cdk_new_supplier_disbursement').html('Error loading XML document');
                },
                success: function(xml){
                    disburse_res = jQuery(xml).find('supplier_disbursement').text();
                    jQuery('#cdk_new_supplier_disbursement').html(disburse_res);
                    jQuery('#cdk_new_payment_method_currency').val(curr_id);
                    
                    getCurrencyRate(document.cdk_form.cdk_currency, 'suggest_rate', 'confirm_rate');
                    
                    if (rate_refresh === true) {
                        getNewCurrencyRate(document.cdk_form.cdk_currency, 'suggest_rate', 'confirm_rate');
                    }
                }
            });
        } else {
            jQuery('#cdk_new_supplier_disbursement').html('');
            jQuery('#cdk_new_payment_method_currency').val('');
            jQuery('#suggest_rate').val('');
            jQuery('#suggest_rate_text').html('');
            jQuery('#confirm_rate').val('');
            clearPOList();
        }
        return true;
    }
    
    function getCurrencyRate(ref, suggest_text, confirm_text) {
        var obj_ref = ref;
        var pay_curr;
        var rate_res;
        var payment_id = jQuery('#cdk_supplier_payment').val();
        var supp_id = jQuery('#cdk_supplier').val();
        var server_action = 'get_currency_rate';
        var sell_title = "<?= sprintf(TABLE_HEADING_DTU_SRP, ''); ?>";
        var amount_title = "<?= sprintf(TABLE_HEADING_DTU_AMOUNT, ''); ?>";
        var total_title = '';
        if (obj_ref.value != '' && typeof (jQuery('#cdk_supplier_payment')) != "undefined") {
            if (jQuery('#cdk_supplier_payment').val() != '') {
                jQuery.ajax({
                    url: "consignment_payment_xmlhttp.php?action=" + server_action + "&curr_code=" + obj_ref.value + "&pm_id=" + payment_id + "&s_id=" + supp_id,
                    type: 'GET',
                    dataType: 'xml',
                    timeout: 10000,
                    error: function () {
                        jQuery('#' + suggest_text + '_text').html('Error loading XML document');
                    },
                    success: function (xml) {
                        pay_curr = jQuery(xml).find('payment_currency').text();
                        rate_res = jQuery(xml).find('currency_rate').text();
                        sell_title = jQuery(xml).find('sell_price_title').text();
                        amount_title = jQuery(xml).find('total_price_title').text();
                        jQuery('#' + suggest_text).val(rate_res);
                        jQuery('#' + suggest_text + '_text').html(rate_res);
                        jQuery('#' + confirm_text).val(rate_res);
                        jQuery('#total_po_amount_title').html(sell_title);
                        jQuery('#total_price_title').html(amount_title);
                        jQuery('#cdk_payment_method_currency').val(pay_curr);
                        jQuery('#cdk_total_currency').html(obj_ref.value);
                    }
                });
            }
        } else {
            jQuery('#' + suggest_text).val('');
            jQuery('#' + suggest_text + '_text').html('');
            jQuery('#' + confirm_text).val('');
            jQuery('#total_po_amount_title').html(sell_title);
            jQuery('#total_price_title').html(amount_title);
            jQuery('#cdk_payment_method_currency').val(total_title);
            jQuery('#cdk_total_currency').html(total_title);
        }

        return true;
    }

    function getNewCurrencyRate(ref, suggest_text, confirm_text) {
        var obj_ref = ref;
        var pay_curr;
        var rate_res;
        var payment_id = jQuery('#cdk_new_supplier_payment').val();
        var supp_id = jQuery('#cdk_supplier').val();
        var server_action = 'get_new_currency_rate';
        var sell_title = "<?= sprintf(TABLE_HEADING_CDK_PO_AMOUNT, ''); ?>";
        var amount_title = "<?= sprintf(TABLE_HEADING_CDK_AMOUNT, ''); ?>";
        var total_title = '';
        
        if (obj_ref.value != '' && typeof (jQuery('#cdk_new_supplier_payment')) != "undefined") {
            if (jQuery('#cdk_new_supplier_payment').val() != '') {
                jQuery.ajax({
                    url: "consignment_payment_xmlhttp.php?action=" + server_action + "&curr_code=" + obj_ref.value + "&pm_id=" + payment_id + "&s_id=" + supp_id,
                    type: 'GET',
                    dataType: 'xml',
                    timeout: 10000,
                    error: function () {
                        jQuery('#' + suggest_text + '_text').html('Error loading XML document');
                    },
                    success: function (xml) {
                        pay_curr = jQuery(xml).find('payment_currency').text();
                        rate_res = jQuery(xml).find('currency_rate').text();
                        sell_title = jQuery(xml).find('sell_price_title').text();
                        amount_title = jQuery(xml).find('total_price_title').text();

                        jQuery('#' + suggest_text).val(rate_res);
                        jQuery('#' + suggest_text + '_text').html(rate_res);
                        jQuery('#' + confirm_text).val(rate_res);
                        jQuery('#total_po_amount_title').html(sell_title);
                        jQuery('#total_price_title').html(amount_title);
                        jQuery('#cdk_new_payment_method_currency').val(pay_curr);
                        jQuery('#cdk_total_currency').html(obj_ref.value);
                    }
                });
            }
        } else {
            jQuery('#' + suggest_text).val('');
            jQuery('#' + suggest_text + '_text').html('');
            jQuery('#' + confirm_text).val('');
            jQuery('#total_po_amount_title').html(sell_title);
            jQuery('#total_price_title').html(amount_title);
            jQuery('#cdk_new_payment_method_currency').val(total_title);
            jQuery('#cdk_total_currency').html(total_title);
        }

        return true;
    }
    
    function lock_cdk_supplier() {
        var supplier_id = jQuery('#cdk_supplier').val();
        var supp_locked_html;
        var res;
        var server_action = 'lock_po_supplier';
	
        jQuery.ajax({
            url: "consignment_payment_xmlhttp.php?action="+server_action+"&p_id="+supplier_id,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            Error: function(){

            },
            success: function(xml){
                supp_locked_html = jQuery(xml).find('supplier_locked').text();
                jQuery('#cdk_payment_supplier_lock_div').html(supp_locked_html);

                res = jQuery(xml).find('action_result').text();
                alert(res);
                if (checkCDKform()) {
                    getPOList(supplier_id, '', false);
                }
            }
        });
    }

    function unlock_cdk_supplier() {
        var pub_id = jQuery('#cdk_supplier').val();
        var supp_locked_html;
        var res;
        var server_action = 'unlock_po_supplier';

        jQuery.ajax({
            url: "consignment_payment_xmlhttp.php?action="+server_action+"&p_id="+pub_id,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            Error: function(){

            },
            success: function(xml){
                supp_locked_html = jQuery(xml).find('supplier_locked').text();
                jQuery('#cdk_payment_supplier_lock_div').html(supp_locked_html);

                res = jQuery(xml).find('action_result').text();
                alert(res);
                clearPOList();
            }
        });
    }
    
    function getSupplierInfoReport(ref, address_div) {
        var obj_ref = ref;
        var server_action = 'get_supplier_info';

        if (obj_ref.value != '') {
            jQuery.ajax({
                url: "consignment_payment_xmlhttp.php?action=" + server_action + "&s_id=" + obj_ref.value,
                type: 'GET',
                dataType: 'xml',
                timeout: 10000,
                error: function () {
                    jQuery('#' + address_div).html('Error loading XML document');
                },
                success: function (xml) {
                    address_res = jQuery(xml).find('supplier_address').text();
                    jQuery('#' + address_div).html(address_res);
                }
            });
        } else {
            jQuery('#' + address_div).html('');
        }

        return true;
    }
    
    function checkCDKform() {
        var sdate = jQuery('#start_date').val();
        var edate = jQuery('#end_date').val();
        var supp_id = jQuery('#cdk_supplier').val();
        var delivery_add = jQuery('#cdk_delivery_address').val();
        var currency = jQuery('#cdk_currency').val();
        var disburse_method = jQuery('#cdk_supplier_payment').val();
        var supplier_lock = jQuery('#supplier_lock').val();
        
        var form_type = jQuery('#cdk_list_type').val();
        
        if (!sdate) {
            alert('Please Select Start Date');
            return false;
        }
        
        if (form_type === 'add_cdk_cb') {
            if (!edate) {
                alert('Please Select End Date');
                return false;
            }
        }
        
        if (!supp_id) {
            alert('Please Select Supplier');
            return false;
        }
        
        if (!delivery_add) {
            alert('Please Select Delivery Address');
            return false;
        }
        
        if (!currency) {
            alert('Please Select Currency');
            return false;
        }
        
        if (!disburse_method) {
            alert('Please Select Disbursement Method');
            return false;
        }
        
        if (supplier_lock==='Lock') {
            alert('Please Lock Supplier');
            return false;
        }
        
        return true;
    }
    
    function checkCDKformReport(cdk_status, start_date, end_date) {
        var sdate = start_date.value;
        var edate = end_date.value;
        var cdk_status = cdk_status.value;
        
        if (!sdate) {
            alert('Please Select Start Date');
            return false;
        }
        
        if (!edate) {
            alert('Please Select End Date');
            return false;
        }

        if (!cdk_status) {
            alert('Please Select CDK Status');
            return false;
        }
        
        return true;
    }
    
    function unCheckedDTU() {
        jQuery('.dtu_items_top_up_id').each(function () {
            this.checked = false;
        });
        
        return true;
    }
    
    function calculatePayPOTotal(obj_sel, po_id) {
        var total_amt = jQuery('#cdk_select_total').val();
        var cdk_amt = jQuery('#subtotal_' + po_id + '_div').text().replace(/[^0-9\.]+/g,'');
        var new_po_amt = 0;

        if (!cdk_amt) { cdk_amt = 0; }
	
        if (obj_sel.checked == true) {
            new_po_amt = parseFloat(total_amt) + parseFloat(cdk_amt);
        } else {
            new_po_amt = parseFloat(total_amt) - parseFloat(cdk_amt);
        }

        jQuery('#cdk_select_total').val(new_po_amt.toFixed(4));
        jQuery('#cdk_select_total_div').html(new_po_amt.toFixed(4).replace(/(\d)(?=(\d{3})+\.)/g, '$1,'));
        
        if (new_po_amt <= 0) {
            jQuery("#cdkCalculateButton").attr('disabled', 'disabled');
        } else {
            jQuery("#cdkCalculateButton").removeAttr('disabled');
        }
        
        return true;
    }

    function doPOLocked(admin, o, _lang, act, element_id, btn_show_time) {
        if (pageLoaded == null || !pageLoaded)
            return;

        var objRef = DOMCall(element_id);
        var user_msg = '';

        if (act == "ulo") {
            user_msg = prompt("Leave any message for unlocking this order?", "");
            if (user_msg != null) {
                //user_msg = URLEncode(user_msg);
            } else {
                return false;
            }
        }

        objRef.innerHTML = '';
        var msg = 'Loading... Please be patience!';
        showMainInfo(msg);

        jQuery.ajax({
            url: "consignment_payment_xmlhttp.php?action=show_lock_btn&subaction=" + act + "&adm=" + admin + "&oid=" + o + "&lang=" + _lang + "&log_comment=" + user_msg + "&from_time=" + btn_show_time,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            error: function () {

            },
            success: function (xml) {
                var A = jQuery(xml).find('action').text();

                if (act == 'l') {
                    if (A == 'Prompt Alert Message') {
                        if (typeof (jQuery(xml).find('lock_msg')) != "undefined" && jQuery(xml).find('lock_msg').text() != null) {
                            var l_msg = jQuery(xml).find('lock_msg').text();
                        } else {
                            var l_msg = '';
                        }
                        alert(l_msg);

                        if (typeof (jQuery(xml).find('close_win')) != "undefined" && jQuery(xml).find('close_win').text() != null) {
                            if (jQuery(xml).find('close_win').text() == '1') {
                                if (document.all) {	// For IE
                                    window.close();
                                } else {
                                    /*****************************************************************************************
                                     The first step is to fool the browser into thinking that it was opened with a script.
                                     
                                     This opens a new page, (non-existent), into a target frame/window, 
                                     (_parent which of course is the window in which the script is executed, so replacing itself), 
                                     and defines parameters such as window size etc, (in this case none are defined as none are needed). 
                                     Now that the browser thinks a script opened a page we can quickly close it in the standard way.
                                     *****************************************************************************************/
                                    window.open('', '_parent', '');
                                    window.close();
                                }
                            }
                        }
                    }

                    window.location.reload();
                } else {
                    showBtn(admin, o, _lang, element_id, A, xml);
                    lockingHistory(o, 0, 'locking_history');
                }
            }
        });

        setTimeout('hideMainInfo()', 3000);
    }

    function showBtn(admin, o, _lang, element_id, act_msg, xml) {
        var objRef = DOMCall(element_id);
        var deliveryBoxObj = DOMCall('partial_delivery_box');

        if (typeof (jQuery(xml).find('lock_msg')) != "undefined" && jQuery(xml).find('lock_msg').text() != null) {
            var l_msg = jQuery(xml).find('lock_msg').text();
        } else {
            var l_msg = '';
        }

        var R = (typeof (jQuery(xml).find('result')) != 'undefined' && jQuery(xml).find('result').text() != null) ? jQuery(xml).find('result').text() : '';
        var Sub_A = (typeof (jQuery(xml).find('subaction')) != 'undefined' && jQuery(xml).find('subaction').text() != null) ? jQuery(xml).find('subaction').text() : '';

        if (act_msg == "Show Lock Button") {
            var show_time = (typeof (jQuery(xml).find('time')) != 'undefined' && jQuery(xml).find('time').text() != null) ? jQuery(xml).find('time').text() : '';

            objRef.innerHTML = '<div id="navBtn" style="float:left;">' + "\n" +
                    '<ul>' + "\n" +
                    '<li id="lock_btn"><a href="javascript:;" onClick="doPOLocked(\'' + admin + '\', \'' + o + '\', \'' + _lang + '\', \'l\', \'' + element_id + '\', \'' + show_time + '\');" title="Locking this order">LOCK</a></li>' + "\n" +
                    '</ul>' + "\n" +
                    '</div>' + "\n" +
                    '<div style="width:90%">' + "\n" +
                    l_msg + "\n" +
                    '</div>';

            DOMCall('order_comment_box').className = 'hide';
            DOMCall('order_comment_selection_box').className = 'hide';

            if (typeof (deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
                deliveryBoxObj.className = 'hide';
            }

            for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
                var custom_info_box = DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_info_box) != 'undefined' && custom_info_box != null) {
                    custom_info_box.className = 'hide';
                }

                var custom_extra_info_box = DOMCall('custom_extra_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_extra_info_box) != 'undefined' && custom_extra_info_box != null) {
                    custom_extra_info_box.className = 'hide';
                }

                var sup_sel_box = DOMCall('supplier_select_' + unique_product_ref_array[p_cnt]);
                if (typeof (sup_sel_box) != 'undefined' && sup_sel_box != null) {
                    sup_sel_box.disabled = true;
                }

                //For cdkey, each unique key is grouped into its parent product, so we access them individually here.
                var cdkey_release_str = 'cdkey_release_link_' + unique_product_ref_array[p_cnt];
                var tbodys = document.getElementsByTagName('tbody');
                for (var i = 0; i < tbodys.length; i++) {
                    id = tbodys[i].getAttribute("id");
                    if (typeof (id) == 'string') {
                        if (parseInt(id.search(cdkey_release_str)) >= 0) {
                            var cdkey_release_link = DOMCall(id);
                            cdkey_release_link.className = 'hide';
                        }
                    }
                }
            }

            for (loc in location_control_array) {
                if (DOMCall(loc) != null) {
                    DOMCall(loc).innerHTML = '-Hidden-';
                }
            }

            for (delivery in partial_delivery_control_array) {
                if (delivery.indexOf("deliver_") === 0) {
                    var ref_id = replace(delivery, 'deliver_', '');
                    var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
                    var delivery_obj = DOMCall(delivery);
                    if (delivery_obj != null) {
                        qty_sign_obj.disabled = true;
                        delivery_obj.disabled = true;
                        delivery_obj.value = '-Hidden-';
                        delivery_obj.size = 4;
                    }
                }
            }

            disabledFormInputs('partial_delivery', true);

            for (remark_link_ins in remark_link_pool) {
                if (remark_link_ins.indexOf("set_remark_") === 0) {
                    var remark_link_obj = DOMCall(remark_link_ins);
                    if (remark_link_obj != null)
                        remark_link_obj.innerHTML = '-Hidden-';
                }
            }
        } else if (act_msg == "Show Unlock Button") {
            objRef.innerHTML = '<div id="navBtn" style="float:left;">' + "\n" +
                    '<ul>' + "\n" +
                    '<li id="unlock_btn"><a href="javascript:;" onClick="doPOLocked(\'' + admin + '\', \'' + o + '\', \'' + _lang + '\', \'' + (Sub_A == "Prompt For Unlocking Msg" ? 'ulo' : 'ul') + '\', \'' + element_id + '\');" title="Unlocking this order">UNLOCK</a></li>' + "\n" +
                    '</ul>' + "\n" +
                    '</div>' + "\n" +
                    '<div style="width:90%">' + "\n" +
                    l_msg + "\n" +
                    '</div>' + "\n";

            DOMCall('order_comment_box').className = 'show';
            DOMCall('order_comment_selection_box').className = 'show';
            if (typeof (deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
                deliveryBoxObj.className = 'show';
            }
            for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
                var custom_info_box = DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_info_box) != 'undefined' && custom_info_box != null) {
                    custom_info_box.className = 'show';
                }

                var custom_info_box = DOMCall('custom_extra_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_extra_info_box) != 'undefined' && custom_extra_info_box != null) {
                    custom_extra_info_box.className = 'show';
                }

                var sup_sel_box = DOMCall('supplier_select_' + unique_product_ref_array[p_cnt]);
                if (typeof (sup_sel_box) != 'undefined' && sup_sel_box != null) {
                    sup_sel_box.disabled = false;
                }

                var cdkey_release_link = DOMCall('cdkey_release_link_' + unique_product_ref_array[p_cnt]);
                if (typeof (cdkey_release_link) != 'undefined' && cdkey_release_link != null) {
                    cdkey_release_link.className = 'show';
                }
            }

            for (loc in location_control_array) {
                if (DOMCall(loc) != null) {
                    DOMCall(loc).innerHTML = URLDecode(location_control_array[loc]);
                }
            }

            for (delivery in partial_delivery_control_array) {
                if (delivery.indexOf("deliver_") === 0) {
                    var ref_id = replace(delivery, 'deliver_', '');
                    var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
                    var delivery_obj = DOMCall(delivery);
                    if (delivery_obj != null) {
                        qty_sign_obj.disabled = false;
                        delivery_obj.disabled = false;
                        delivery_obj.value = '0';
                        delivery_obj.size = 4;
                    }
                }
            }

            disabledFormInputs('partial_delivery', false);

            for (remark_link_ins in remark_link_pool) {
                if (remark_link_ins.indexOf("set_remark_") === 0) {
                    var remark_link_obj = DOMCall(remark_link_ins);
                    if (remark_link_obj != null)
                        remark_link_obj.innerHTML = remark_link_pool[remark_link_ins];
                }
            }
        } else if (act_msg == "Show Failed Lock Msg") {
            DOMCall('order_comment_box').className = 'hide';
            DOMCall('order_comment_selection_box').className = 'hide';
            if (typeof (deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
                deliveryBoxObj.className = 'hide';
            }

            for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
                var custom_info_box = DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]);
                if (typeof (custom_info_box) != 'undefined' && custom_info_box != null) {
                    DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]).className = 'hide';
                }
            }

            for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
                var supplier_custom_info_box = DOMCall('supplier_custom_info_box' + unique_product_ref_array[p_cnt]);
                if (typeof (supplier_custom_info_box) != 'undefined' && supplier_custom_info_box != null) {
                    DOMCall('supplier_custom_info_box' + unique_product_ref_array[p_cnt]).className = 'hide';
                }
            }

            for (loc in location_control_array) {
                if (DOMCall(loc) != null) {
                    DOMCall(loc).innerHTML = '-Hidden-';
                }
            }

            for (delivery in partial_delivery_control_array) {
                if (delivery.indexOf("deliver_") === 0) {
                    var ref_id = replace(delivery, 'deliver_', '');
                    var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
                    var delivery_obj = DOMCall(delivery);
                    if (delivery_obj != null) {
                        qty_sign_obj.disabled = true;
                        delivery_obj.disabled = true;
                        delivery_obj.value = '-Hidden-';
                        delivery_obj.size = 4;
                    }
                }
            }

            disabledFormInputs('partial_delivery', true);

            for (remark_link_ins in remark_link_pool) {
                if (remark_link_ins.indexOf("set_remark_") === 0) {
                    var remark_link_obj = DOMCall(remark_link_ins);
                    if (remark_link_obj != null)
                        remark_link_obj.innerHTML = '-Hidden-';
                }
            }

            objRef.innerHTML = l_msg;
        }

        showMainInfo(R);
    }

    function lockingHistory(o, mode, element_id) {
        if (pageLoaded == null || !pageLoaded)
            return;

        var obj = DOMCall(element_id);
        if (obj == null)
            return;

        obj.className = 'hide';

        if (mode == 1) {
            var msg = 'Loading... Please be patience!';
            showMainInfo(msg);

            jQuery.ajax({
                url: "consignment_payment_xmlhttp.php?action=get_order_locking_history&oid=" + o,
                type: 'GET',
                dataType: 'xml',
                timeout: 10000,
                error: function () {
                    return;
                },
                success: function (xml) {
                    var A = jQuery(xml).find('action').text();

                    var R_C = (typeof (jQuery(xml).find('res_code')) != 'undefined' && jQuery(xml).find('res_code').text() != null) ? jQuery(xml).find('res_code').text() : '';
                    var R = (typeof (jQuery(xml).find('result')) != 'undefined' && jQuery(xml).find('result').text() != null) ? jQuery(xml).find('result').text() : '';

                    if (R_C == 0) {
                        alert(R);
                    } else if (R_C == 1) {
                        obj.innerHTML = '	<div>' + "\n" +
                                '		<a href="javascript:;" onClick="lockingHistory(\'' + o + '\', \'0\', \'' + element_id + '\');">Hide Locking History</a>' + "\n" +
                                '	</div>' + "\n" +
                                '	<div>' + R + '</div>';
                    }

                    obj.className = 'show';

                    hideMainInfo();
                }
            });
        } else {
            obj.innerHTML = '	<div>' + "\n" +
                    '		<a href="javascript:;" onClick="lockingHistory(\'' + o + '\', \'1\', \'' + element_id + '\');">Show Locking History</a>' + "\n" +
                    '	</div>';
            obj.className = 'show';
        }

        setTimeout('hideMainInfo()', 3000);
    }

    function getCDKPaymentStatistic(suppid) {
        var server_action = 'get_cdk_payment_statistic';
        var ref_url = 'consignment_payment_xmlhttp.php?action=' + server_action + '&suppID=' + suppid;

        jQuery('#po_stat_div').html('<b>Loading Statistic...</b>');

        jQuery.ajax({
            url: ref_url,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                var po_stat = jQuery(xml).find('stat').text();

                if (po_stat != '') {
                    po_stat = '<table width="100%" cellspacing="0" cellpadding="2" border="0">' + po_stat + '</table>';
                }

                jQuery('#po_stat_div').html(po_stat);
            }
        });
    }

    function toggle_po_comment(whichLink, whichLayer) {
        var show_mode = '';

        if (typeof (whichLink.innerText) != 'undefined') {
            show_mode = whichLink.innerText.indexOf('Hide') == 0 ? true : false;
        } else {
            show_mode = whichLink.text.indexOf('Hide') == 0 ? true : false;
        }

        if (show_mode) {
            whichLink.innerHTML = whichLink.innerHTML.replace(/Hide/ig, "Show");

            if (whichLayer == 'o_comment_') {
                for (var i = 0; i < 3; i++) {
                    newLayer = whichLayer + i + '_';
                    jQuery('.' + newLayer).each(function () {
                        jQuery('#' + this.value).addClass('hide');
                    });
                }
            } else {
                jQuery('.' + whichLayer).each(function () {
                    jQuery('#' + this.value).addClass('hide');
                });
            }
        } else {
            whichLink.innerHTML = whichLink.innerHTML.replace(/Show/ig, "Hide");

            if (whichLayer == 'o_comment_') {
                for (var i = 0; i < 3; i++) {
                    newLayer = whichLayer + i + '_';
                    jQuery('.' + newLayer).each(function () {
                        jQuery('#' + this.value).removeClass('hide');
                    });
                }
            } else {
                jQuery('.' + whichLayer).each(function () {
                    jQuery('#' + this.value).removeClass('hide');
                });
            }
        }
    }

    function setAsPORemark(shid, o, _lang, element_id) {
        var obj = DOMCall(element_id);

        if (obj == null)
            return;

        obj.className = 'hide';

        var msg = 'Loading... Please be patience!';
        showMainInfo(msg);

        jQuery.ajax({
            url: ref_url = "consignment_payment_xmlhttp.php?action=set_po_remark&shid=" + shid + "&oid=" + o + "&lang=" + _lang,
            type: 'GET',
            dataType: 'xml',
            timeout: 10000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                var R_C = jQuery(xml).find('res_code').text();
                var R = jQuery(xml).find('result').text();

                if (R_C == 0) {
                    alert(R);
                } else if (R_C == 1) {
                    obj.innerHTML = R;
                    initSetRemarkView(1);	// the inner html of order comment's set as remark link has been changed.
                }

                obj.className = 'show';

                hideMainInfo();
            }
        });

        setTimeout('hideMainInfo()', 3000);
    }

    function initSetRemarkView(mode) {
        for (var i = 0; i < set_as_remark_array.length; i++) {
            if (set_as_remark_array[i].indexOf("set_remark_") === 0) {
                var set_remark_obj = DOMCall(set_as_remark_array[i]);
                if (set_remark_obj != null) {
                    remark_link_pool[set_as_remark_array[i]] = set_remark_obj.innerHTML;
                    if (mode == 1) {
                        ;//set_remark_obj.innerHTML = '';
                    } else {
                        set_remark_obj.innerHTML = '-Hidden-';
                    }
                }
            }
        }
    }

    function poInfo(sec, mode, instruction, _lang, SID) {
        var order_str_obj = DOMCall(sec + '_order_str');
        var order_detail_link = DOMCall(sec + '_nav');

        if (mode == 1) {
            if (order_str_obj.value != '') {
                order_detail_link.innerHTML = '';

                if (instruction == true) {
                    var msg = 'Loading... Please be patience!';
                    showMainInfo(msg);
                }
                
                var fetch_fresh_info = true;
                var order_ids_array = order_str_obj.value.split(',');
                for (i = 0; i < order_ids_array.length; i++) {
                    var temp_order_div = DOMCall(sec + '_' + order_ids_array[i] + '_order');
                    if (trim_str(temp_order_div.innerHTML) != '')
                        fetch_fresh_info = false;
                    break;
                }

                if (fetch_fresh_info == true) {
                    jQuery.ajax({
                        url: ref_url = "consignment_payment_xmlhttp.php?action=get_po_info&o_str=" + order_str_obj.value + "&lang=" + _lang + "&SID=" + SID,
                        type: 'GET',
                        dataType: 'xml',
                        timeout: 10000,
                        error: function () {
                            jQuery.unblockUI();
                        },
                        success: function (xml) {
                            order_detail_link.innerHTML = '<a href="javascript:;" onClick="poInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\', \'' + SID + '\');">Hide Details</a>';

                            var order_info = jQuery(xml).find('order_info').text();

                            if (typeof (order_info) != 'undefined' && order_info != null) {
                                var order_details = '';

                                jQuery(xml).find("order_detail").each(function () {
                                    order_details = jQuery(this).text();
                                    var order_div = DOMCall(sec + '_' + jQuery(this).attr("order_id") + '_order');
                                    order_div.innerHTML = order_details;
                                    var order_tbody = DOMCall(sec + '_' + jQuery(this).attr("order_id") + '_order_sec');
                                    order_tbody.className = 'show';
                                });
                            }

                            if (instruction == true)
                                hideMainInfo();
                        }
                    });
                } else {
                    order_detail_link.innerHTML = '<a href="javascript:;" onClick="poInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\', \'' + SID + '\');">Hide Details</a>';

                    for (i = 0; i < order_ids_array.length; i++) {
                        var order_detail_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_order_sec');
                        order_detail_tbody.className = 'show';
                    }

                    if (instruction == true)
                        hideMainInfo();
                }
            }
        } else {
            if (order_str_obj.value != '') {
                order_detail_link.innerHTML = '<a href="javascript:;" onClick="poInfo(\'' + sec + '\', \'1\', true, \'' + _lang + '\', \'' + SID + '\');">Show Details</a>';

                var order_ids_array = order_str_obj.value.split(',');
                for (i = 0; i < order_ids_array.length; i++) {
                    var order_detail_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_order_sec');
                    order_detail_tbody.className = 'hide';
                }
            }
        }
    }

    function client_height_adjust() {
        if (typeof (window.innerWidth) == 'number') { //not IE
            return 20;
        } else {
            return 20;
        }
    }

    function set_fancybox_position() {
        var boxTopValue = 0, boxLeftValue = 0;
        //Get the screen height and width
        var docH = jQuery(document).height();
        var winW = jQuery(window).width();
        var winH = jQuery(window).height();

        boxTopValue = winH / 2 - jQuery('#fancy_content').height() / 2 - 30;
        boxLeftValue = winW / 2 - 245;

        jQuery("#fancy_box").css({'display': 'block', 'left': boxLeftValue, 'visibility': 'visible', 'top': boxTopValue});
        jQuery("#fancy_box_Bg").css({'display': 'block', 'position': 'absolute', 'width': winW, 'height': docH, 'visibility': 'visible'});

        if (typeof (window.innerWidth) == 'number') {
            jQuery("#fancy_box").css({'position': 'fixed'});
        }
    }

    function realign_fancybox(table_id) {
        if (jQuery("#fancy_box").css('display') == 'block') {
            jQuery("#fancy_box").css('height', jQuery("#" + table_id).height() + client_height_adjust());

            if (jQuery("#" + table_id).width() > jQuery("#fancy_box").width()) {
                jQuery("#fancy_box").css('width', jQuery("#" + table_id).width() + 10);
            }

            var boxTopValue = 0, boxLeftValue = 0;
            var docH = jQuery(document).height();
            var winW = jQuery(window).width();
            var winH = jQuery(window).height();

            boxTopValue = winH / 2 - jQuery('#fancy_content').height() / 2 - 30;
            boxLeftValue = winW / 2 - 245;

            jQuery("#fancy_box").css({'left': boxLeftValue, 'top': boxTopValue});
            jQuery("#fancy_box_Bg").css({'width': winW, 'height': docH});

            if (typeof (window.innerWidth) != 'number') {
                var scrollWinH = (jQuery(window).height() / 2) - 222 + jQuery(this).scrollTop();
                jQuery("#fancy_box").css({top: scrollWinH + "px"});
            }
        }
    }

    function hideFancyBox() {
        jQuery('#footerBar').css('display', 'block');
        jQuery("#fancy_close").css('display', 'none');
        jQuery("#fancy_box").css({'display': 'none', 'visibility': 'hidden'});
        jQuery("#fancy_box_Bg").css({'display': 'none', 'visibility': 'hidden'});
        jQuery("body").css('overflow', '');
    }

    function get_crew_remark() {
        var ref_url = "consignment_payment_xmlhttp.php?action=popup_remark";

        pop_out = '<table id="crew_remark" border="0" cellspacing="0" cellpadding="0" width="100%">';
        pop_out += '<tr><td>';
        <?= "pop_out += '" . tep_draw_form("dtu_remark_form", FILENAME_CDK_PAYMENT, tep_get_all_get_params(array("subaction")) . "subaction=add_remark", "post", 'id="dtu_remark_form"') . "';" ?>
        pop_out += '<table id="sc_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%">';
        pop_out += '<tr><td width="3" style="padding:15px 0px;"></td><td class="footerPopupTitle" colspan="4"><?= HEADER_ADD_REMARK ?></td></tr>';
        pop_out += '<tr><td colspan="5"><div style="padding-top: 3px; margin-top: 0px;" class="solidGreyLine"><!-- --></div></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3"><textarea id="crew_comments" rows="5" cols="100" wrap="soft" name="crew_comments"></textarea></td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td><input type="checkbox" value="1" name="notify_supplier" id="notify_supplier">&nbsp;<b><?= TEXT_NOTIFY_SUPPLIER ?></b></td><td>&nbsp;</td><td align="right"><input type="checkbox" value="1" name="set_important" id="set_important">&nbsp;<b><?= TEXT_SET_IMPORTANT_REMARK ?></b></td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '<tr><td colspan="5"><div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3">';
        pop_out += '<div class="green_button_fix_width" id="btn_remark_update"><a href="javascript:;" class="text-decoration: none;"><span><font><?= BUTTON_UPDATE ?></font></span></a></div>';
        pop_out += '</td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '</table></form></td></tr></table>';

        blockUI_disable();
        jQuery.ajax({
            url: ref_url,
            type: 'GET',
            dataType: 'xml',
            timeout: 60000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                set_fancybox_position();

                jQuery("#fancy_content").html(pop_out);
                jQuery(".fancy_close_footer").css('display', 'block');
                realign_fancybox("crew_remark");

                jQuery.unblockUI();

                jQuery("#btn_remark_update").click(function () {
                    hideFancyBox();
                    jQuery('#dtu_remark_form').submit();
                });
            }
        });
    }

    function get_refund_remark(button_obj, curr_code) {
        var ref_url = "consignment_payment_xmlhttp.php?action=popup_remark";
        var buton_action = '';
        var refund_title = 'Please insert the reason to Refund this PO:';
        var po_id = jQuery('#po_id').val();

        if (button_obj.name == 'CancelPendingReceiveBtn') {
            buton_action = 'refund_cdk_cancel';
            refund_title = 'Please insert the reason to Cancel Pending Receive this PO:';
        }

        pop_out = '<table id="refund_remark" border="0" cellspacing="0" cellpadding="0" width="100%">';
        pop_out += '<tr><td>';
        <?= "pop_out += '" . tep_draw_form("cdk_refund_form", FILENAME_CDK_PAYMENT, tep_get_all_get_params(array("subaction")), "post", 'id="cdk_refund_form"') . "';" ?>
        pop_out += '<table id="sc_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%">';
        pop_out += '<tr><td width="3" style="padding:15px 0px;"><input type="hidden" id="subaction" name="subaction" value="' + buton_action + '" /><input type="hidden" id="po_id" name="po_id" value="' + po_id + '" /></td><td class="footerPopupTitle" colspan="4">' + refund_title + '</td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3"><textarea id="cancel_comments" rows="5" cols="100" wrap="soft" name="cancel_comments"></textarea></td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '<tr><td colspan="5"><div style="padding-top: 3px; margin-top: 0px;" class="solidGreyLine"><!-- --></div></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3"><b>Bank Charges to refund: ' + curr_code + '</b>&nbsp;<input type="text" name="bankcharges_refund" id="bankcharges_refund" size="10" /></td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '<tr><td colspan="5"><div style="padding-top: 5px; margin-top: 0px;" class="dottedLine"><!-- --></div></td></tr>';
        pop_out += '<tr><td width="3" style="padding:5px 10px;"></td><td colspan="3">';
        pop_out += '<div class="green_button_fix_width" id="btn_refund_confirm"><a href="javascript:;" class="text-decoration: none;"><span><font><?= BUTTON_CONFIRM ?></font></span></a></div>';
        pop_out += '</td><td width="3" style="padding:5px 10px;"></td></tr>';
        pop_out += '</table></form></td></tr></table>';

        blockUI_disable();
        jQuery.ajax({
            url: ref_url,
            type: 'GET',
            dataType: 'xml',
            timeout: 60000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                set_fancybox_position();

                jQuery("#fancy_content").html(pop_out);
                jQuery(".fancy_close_footer").css('display', 'block');
                realign_fancybox("refund_remark");

                jQuery.unblockUI();

                jQuery("#btn_refund_confirm").click(function () {
                    hideFancyBox();
                    jQuery('#cdk_refund_form').submit();
                });
            }
        });
    }

    function get_product_last_purchase_price(r_id, p_id, o_id) {
        var cost_hist_table = DOMCall('1_sub_' + r_id + '_' + p_id + '_cost');

        var ref_url = "consignment_payment_xmlhttp.php?action=product_price_history&p_id=" + p_id + "&o_id=" + o_id;

        blockUI_disable();
        jQuery.ajax({
            url: ref_url,
            type: 'GET',
            dataType: 'xml',
            timeout: 60000,
            error: function () {
                jQuery.unblockUI();
            },
            success: function (xml) {
                <?= "var nav_html = '<a href=\"javascript:void(0);\" onClick=\"hide_product_price_history(\''+r_id+'\', \''+p_id+'\', \''+o_id+'\')\">" . tep_image(DIR_WS_ICONS . "att.gif", "Hide Last 4x Cot Price", "14", "13", 'align="top"') . "</a>';" ?>

                var prd_cost_html = jQuery(xml).find('cost_history_html').text();
                ;

                jQuery("#" + r_id + "_nav").html(nav_html);
                jQuery("#" + p_id + "_cost").html(prd_cost_html);

                cost_hist_table.className = 'show';

                jQuery.unblockUI();
            }
        });
    }

    function hide_product_price_history(r_id, p_id, o_id) {
        var cost_hist_table = DOMCall('1_sub_' + r_id + '_' + p_id + '_cost');
        <?= "var nav_html = '<a href=\"javascript:void(0);\" onClick=\"get_product_last_purchase_price(\''+r_id+'\', \''+p_id+'\', \''+o_id+'\')\">" . tep_image(DIR_WS_ICONS . "att.gif", "View Last 4x Cot Price", "14", "13", 'align="top"') . "</a>';" ?>

        jQuery("#" + r_id + "_nav").html(nav_html);

        cost_hist_table.className = 'hide';
    }
//--></SCRIPT>