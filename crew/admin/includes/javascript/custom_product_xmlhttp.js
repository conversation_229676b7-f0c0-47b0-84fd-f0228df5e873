var xmlhttp=loadXMLHTTP();
var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
stateSelectionTitle['text'] = 'Please Select';

function loadXMLHTTP() {
	try { 
		varXMLHTTP = new ActiveXObject("Msxml2.XMLHTTP"); 
	}
	
	catch (e) { 
		try { 
			varXMLHTTP = new ActiveXObject("Microsoft.XMLHTTP"); 
		}
		catch (E) { 
			varXMLHTTP = false; 
		}
	}
	
	if (!xmlhttp && typeof XMLHttpRequest!='undefined') { 
		try {
			varXMLHTTP = new XMLHttpRequest(); 
		}
		catch (ex) {
			varXMLHTTP = false;		
		}
	}
	
	return varXMLHTTP;
}

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');
    
    objBody.appendChild(objContainer);
}

function getCDKeyImg(cdkey_id) {
	var server_action = 'get_cdkey_image';
	var ref_url = "custom_product_xmlhttp.php?action="+server_action+"&cp_id="+cdkey_id;
	
	var cdkey_tbody = DOMCall('cdkey_img_' + cdkey_id + '_sec');
	var cdkey_div = DOMCall('cdkey_img_' + cdkey_id);
	
	if (cdkey_tbody.className == 'show') {
		cdkey_tbody.className = 'hide';
	} else {
		var msg = 'Loading... Please be patience!';
		showMainInfo(msg);
		
		xmlhttp.open("GET", ref_url); 
	    xmlhttp.onreadystatechange = function() {
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
	      		//res = xmlhttp.responseText;
	      		
	      		var cdkey_image = xmlhttp.responseXML.getElementsByTagName("cdkey_image")[0];
	      		
				cdkey_div.innerHTML = cdkey_image.firstChild.nodeValue;
				cdkey_tbody.className = 'show';
				
				hideMainInfo();
	      	}
	    }
		xmlhttp.send(null);
	}
}

function getCDKeyImg_multiple(cdkey_id_arr, mode) {
	var server_action = 'get_cdkey_image_multiple';
	var ref_url = "custom_product_xmlhttp.php?action="+server_action+"&cp_id="+cdkey_id_arr;
	
	if (mode == 'hide') {
		//Hide
		for (i=0; i < cdkey_id_arr.length; i++) {
      		var cdkey_tbody = DOMCall('cdkey_img_' + cdkey_id_arr[i] + '_sec');
			cdkey_tbody.className = 'hide';
  		} //end for	
	} else {
		//Show
		var msg = 'Loading... Please be patient!';
		showMainInfo(msg);
	
		xmlhttp.open("GET", ref_url); 
	    xmlhttp.onreadystatechange = function() {
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
	      		//res = xmlhttp.responseText;
				
	      		for (i=0; i < cdkey_id_arr.length; i++) {
		      		var cdkey_tbody = DOMCall('cdkey_img_' + cdkey_id_arr[i] + '_sec');
					var cdkey_div = DOMCall('cdkey_img_' + cdkey_id_arr[i]);			      		
					
		      		var cdkey_body = xmlhttp.responseXML.getElementsByTagName("cdkey_image")[0];
		      		var cdkey_image = cdkey_body.getElementsByTagName("image_" + cdkey_id_arr[i])[0];
					cdkey_div.innerHTML = cdkey_image.firstChild.nodeValue;
					cdkey_tbody.className = 'show';
					
	      		} //end for
	      		
				hideMainInfo();
	      	}
	    }
	    
	    xmlhttp.send(null);
	}
}

function getCDKeyLog(cp_id) {
	var server_action = 'get_cdkey_log';
	var ref_url = "custom_product_xmlhttp.php?action="+server_action+"&cp_id="+cp_id;
	
	var msg = 'Loading... Please be patience!';
	showMainInfo(msg);
	
	xmlhttp.open("GET", ref_url); 
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		res = xmlhttp.responseText;
      		
        	new popUp(300, f_scrollTop()+200, 600, 500, cp_id, res, "white", "#00385c", "9pt sans-serif", cp_id, "#00385c", "white", "lightgrey", "#00568c", "black", true, true, false, true, false, false, '', '', 'cir_close.gif', 'resize.gif');
        	
			hideMainInfo();
      	}
    }
	xmlhttp.send(null);
}

function appendToSelect(select, value, content, disabledMode) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content.replace(/&nbsp;/ig, String.fromCharCode(160));
	if (disabledMode != null) {
		opt.setAttribute('disabled', (disabledMode==1 ? true : false));
	}
	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	var objInfo = DOMCall('infocaption');
	
	if (DisplayMsg == null || objInfo == null) {
		return;
	}
	
	var scroll = getScroll();
	
	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';
	
	objInfo.style.position = "absolute";
	
	objInfo.style.top = 5 + scroll.y;
	
	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}


function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

function orderListsOptions(selObj, sid, _lang, o, wl) {
	var msg = '';
	var rExp = /_tag_selector/gi;
	var status_name = selObj.name.replace(rExp, '');
	var option_action = selObj.value;
	if (o != null && o != '') {
		var selected_order = new Array(o);
	} else {
		var selected_order = new Array();
		var order_str_obj = DOMCall(status_name + '_order_str');
		if (order_str_obj != null) {
			var order_product_ids_array = order_str_obj.value.split(',');
			for (i=0; i < order_product_ids_array.length; i++) {
				var order_row = DOMCall(status_name + '_main_' + i);
				if (order_row != null && order_row.className == "rowSelected") {
					selected_order.push(order_product_ids_array[i]);
				}
			}
		}
	}
	var selected_order_str = selected_order.join(',');
	var act = '';
	var s_val = '';
	switch (option_action) {
		case 'nt':	// create new tag
			if (selected_order.length > 0) {
				var new_tag_name = prompt("Please enter a new tag name","");
				// check to see if anything was entered
                // null == pressing cancel
                // ""   == entered nothing and pressing ok
				while (new_tag_name != null && trim_str(new_tag_name) == '') {
					new_tag_name = prompt("Please enter a new tag name","");
				}
				if (new_tag_name == null) {
					selObj.selectedIndex = 0;
					return;
				}
				act = 'perform_tagging';
				s_val = trim_str(new_tag_name);
			} else {
				msg = 'No orders selected.';
				showMainInfo(msg);
				selObj.selectedIndex = 0;
			}
			break;
		/*
		case 'rd':
		case 'ur':
			if (selected_order.length > 0) {
				act = 'perform_tagging';
				s_val = option_action == 'rd' ? '1' : '0';
				var msg = 'This order is set to ' + (option_action == 'rd' ? 'read' : 'unread') + '!';
				showMainInfo(msg);
			} else {
				msg = 'No orders selected.';
				showMainInfo(msg);
				selObj.selectedIndex = 0;
			}
			break;
		*/
		default:
			if (option_action.indexOf('otag_') != -1) {
				if (selected_order.length > 0) {
					var temp_array = option_action.split('_');
					option_action = 'at';
					s_val = temp_array[1];
					act = 'perform_tagging';
				} else {
					msg = 'No orders selected.';
					showMainInfo(msg);
					selObj.selectedIndex = 0;
				}
			} else if (option_action.indexOf('rmtag_') != -1) {
				if (selected_order.length > 0) {
					var temp_array = option_action.split('_');
					option_action = 'rt';
					s_val = temp_array[1];
					act = 'perform_tagging';
				} else {
					msg = 'No orders selected.';
					showMainInfo(msg);
					selObj.selectedIndex = 0;
				}
			}
			break;
	}
	
	if (trim_str(act) != '') {
		selObj.disabled = true;
	   	clearOptionList(selObj);
		selObj.options[0] = new Option('Loading ...', '');
		var ref_url = "custom_product_xmlhttp.php?action="+act+"&subaction="+option_action+"&status_id="+sid+"&setting="+s_val+"&o_str="+selected_order_str+"&lang="+_lang+"&list_mode="+(wl == true ? '2' : '1');
		xmlhttp.open("GET", ref_url); 
	    xmlhttp.onreadystatechange = function() {
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
	      		//res = xmlhttp.responseText;
	      		//alert(res);
	      		
	      		clearOptionList(selObj);
	      		var tag_info = xmlhttp.responseXML.getElementsByTagName("tag_info")[0];
	      		if (typeof (tag_info) != 'undefined' && tag_info != null) {
		      		var selection = tag_info.getElementsByTagName("selection")[0];
		      		if (typeof (selection) != 'undefined' && selection != null) {
		      			var option_sel = '';
			      		for (var i=0; i < selection.childNodes.length; i++) {
			      			option_sel = selection.getElementsByTagName("option")[i];
			      			appendToSelect(selObj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
					    }
		      		}
		      		
		      		var tag_details = tag_info.getElementsByTagName("tag_details")[0];
		      		if (typeof (tag_details) != 'undefined' && tag_details != null) {
		      			var order_tags = '';
			      		for (var i=0; i < tag_details.childNodes.length; i++) {
			      			order_tags = tag_details.getElementsByTagName("order_tags")[i];
			      			var cus_tag_str = DOMCall('tag_'+order_tags.getAttribute("order_id"));
			      			if (cus_tag_str != null) {
			      				cus_tag_str.innerHTML = order_tags.firstChild.nodeValue;
			      			}
					    }
		      		}
		      		
		      		var read_mode = tag_info.getElementsByTagName("read_mode")[0];
		      		if (typeof (read_mode) != 'undefined' && read_mode != null) {
		      			var order_mode = '';
			      		for (var i=0; i < read_mode.childNodes.length; i++) {
			      			order_mode = read_mode.getElementsByTagName("order_mode")[i];
			      			var cus_order_row_obj = DOMCall('read_'+order_mode.getAttribute("order_id"));
			      			if (cus_order_row_obj != null) {
			      				cus_order_row_obj.className = option_action == 'ur' ? 'boldText' : '';
			      			}
					    }
		      		}
		      	}
	      		selObj.disabled = false;
	      	}
	    }
	    xmlhttp.send(null);
	}
	setTimeout('hideMainInfo()', 3000);
}

function refreshOrderListsOptions(status_name, _lang, o) {
	//isProcessing = true;
	var tag_selector_obj = DOMCall(status_name + '_tag_selector');
	if (o != null) {
		var selected_order = new Array(o);
	} else {
		var order_str_obj = DOMCall(status_name + '_order_str');
		var selected_order = new Array();
		if (order_str_obj != null) {
			var order_product_ids_array = order_str_obj.value.split(',');
			for (i=0; i < order_product_ids_array.length; i++) {
				var order_row = DOMCall(status_name + '_main_' + i);
				if (order_row != null && order_row.className == "rowSelected") {
					selected_order.push(order_product_ids_array[i]);
				}
			}
		}
	}
	var selected_order_str = selected_order.join(',');
	
	if (tag_selector_obj != null) {
		tag_selector_obj.disabled = true;
	   	clearOptionList(tag_selector_obj);
		tag_selector_obj.options[0] = new Option('Loading ...', '');
		
		var ref_url = "custom_product_xmlhttp.php?action=refresh_tag_selection&status="+status_name+"&o_str="+selected_order_str+"&lang="+_lang;
		xmlhttp.open("GET", ref_url); 
		
	    xmlhttp.onreadystatechange = function() {
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
	      		//res = xmlhttp.responseText;
	      		//alert(res);
	      		
	      		clearOptionList(tag_selector_obj);
	      		
	      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];
	      		
	      		if (typeof (selection) != 'undefined' && selection != null) {
	      			var option_sel = '';
		      		for (var i=0; i < selection.childNodes.length; i++) {
		      			option_sel = selection.getElementsByTagName("option")[i];
		      			appendToSelect(tag_selector_obj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
				    }
	      		}
	      		tag_selector_obj.disabled = false;
	      		
	      		//cursor_clear();
	      		//isProcessing = false;
	      	}
	    }
	    xmlhttp.send(null);
	}
}

function fetchStatusTagSelections(status_id, div_objRef, sel, _lang) {
	div_objRef = DOMCall(div_objRef);
	objRef = DOMCall(sel);
	if (objRef != null) {
		if (status_id > 0) {
			clearOptionList(objRef);
			objRef.options[0] = new Option('Loading ...', '');
			
			var ref_url = "custom_product_xmlhttp.php?action=retrieve_status_tags&status="+status_id+"&lang="+_lang;
			xmlhttp.open("GET", ref_url); 
			
		    xmlhttp.onreadystatechange = function() {
		      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
		      		clearOptionList(objRef);
		      		
		      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];
		      		
		      		if (typeof (selection) != 'undefined' && selection != null) {
		      			var option_sel = '';
			      		for (var i=0; i < selection.childNodes.length; i++) {
			      			option_sel = selection.getElementsByTagName("option")[i];
			      			appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
					    }
		      		}
		      		objRef.disabled = false;
		      		div_objRef.className = 'show';
		      	}
		    }
		    xmlhttp.send(null);
		} else {
			clearOptionList(objRef);
			div_objRef.className = "hide";
		}
	}
}

function updateFollowUpDate(o, date_objRef, btn_obj) {
	objRef = DOMCall(date_objRef);
	
	var msg = 'Updating... Please be patience!';
	showMainInfo(msg);
	
	btn_obj.disabled = true;
	
	var ref_url = "custom_product_xmlhttp.php?action=update_follow_up&follow_date="+objRef.value+"&op_id="+o;
	xmlhttp.open("GET", ref_url); 
	
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		var R_C = (typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
      		var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';
      		
      		if (R_C == 0) {
      			alert(R);
      		} else if (R_C == 1) {
      			alert(R);
      		}
      		
      		btn_obj.disabled = false;
      		
      		hideMainInfo();
      	}
    }
    xmlhttp.send(null);
}

function refreshDynamicSelectOptions (sel1, sel2, sel3, _lang, show_title, game_char_id_view) {
	var div_objRef = DOMCall(sel2);
	var objRef = DOMCall(sel3);
	var res;
    var server_action = 'character_history_date';
    var selection_available = false;
    
    if (objRef.type == 'text') {
    	objRef = document.createElement("SELECT");
    	objRef.name = sel3;
    	objRef.id = sel3;
    } else {
    	clearOptionList(objRef);
    }
    
    if (sel1.value == game_char_id_view) {
    	document.getElementById('compare').disabled = false;
    } else {
    	document.getElementById('compare').disabled = true;
    }
    
    div_objRef.innerHTML = 'Loading ...';
    sel1.disabled = true;
	
	var ref_url = "custom_product_xmlhttp.php?action="+server_action+"&game_char_id="+sel1.value+"&lang="+_lang;
	xmlhttp.open("GET", ref_url); 
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
      		//res = xmlhttp.responseText;
      		
      		clearOptionList(objRef);
      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];
      		
      		if (typeof (selection) != 'undefined' && selection != null) {
      			if (show_title == true) {
      				appendToSelect(objRef, stateSelectionTitle['id'], stateSelectionTitle['text']);
      			}
      			
      			var option_sel = '';
	      		for (var i=0; i < selection.childNodes.length; i++) {
	      			option_sel = selection.getElementsByTagName("option")[i];
	      			appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
			    }
      		}
      		
      		if ((show_title == true && objRef.options.length > 1) || (show_title == false && objRef.options.length > 0)) {
      			selection_available = true;
      		} else {
      			selection_available = false;
      		}
      		
      		if (!selection_available) {
      			objRef = document.createElement("INPUT");
		    	objRef.name = sel3;
		    	objRef.id = sel3;
      		}
      		
      		div_objRef.innerHTML = '';
      		div_objRef.appendChild(objRef);
      		sel1.disabled = false;
      	}
    }
    
    xmlhttp.send(null);
}

function searchSuppliersPopUp (search_box, hidden_id) {
	var search_box = DOMCall(search_box);
	var hidden_id = DOMCall(hidden_id);
	
	var server_action = 'get_supplier_info';
	var ref_url = "custom_product_xmlhttp.php?action="+server_action+"&search_val="+search_box.value;
	
	var content_table_html = '';
	
	if (search_box == null || trim_str(search_box.value) == '') {
		alert('There is nothing to look up.');
		search_box.focus();
		return false;
	}
	
	var msg = 'Loading... Please be patience!';
	showMainInfo(msg);
	
	xmlhttp.open("GET", ref_url); 
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
      		
      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];
      		
      		content_table_html += 	'<div style="overflow:auto;width:580px;height:400px;">' + 
      								'	<table border="0" cellspacing="2" cellpadding="2">' + 
      								'		<tr>' +
      								'			<td class="reportBoxHeading">First Name</td>' +
      								'			<td class="reportBoxHeading">Last Name</td>' + 
      								'			<td class="reportBoxHeading">Supplier Code</td>' + 
      								'			<td class="reportBoxHeading">E-mail Address</td>' +
      								'			<td class="reportBoxHeading">Action</td>' +
      								'		</tr>';
      		
      		if (typeof (selection) != 'undefined' && selection != null) {
      			var option_sel = '';
      			
	      		for (var i=0; i < selection.childNodes.length; i++) {
	      			var fname_html = '';
	      			var lname_html = '';
	      			var scode_html = '';
	      			var email_html = '';
	      			var this_cust_email = '';
	      			var sup_id = '';
	      			var scode = '';
	      			
	      			option_sel = selection.getElementsByTagName("option")[i];
	      			
	      			var result_array = option_sel.firstChild.nodeValue.split(':~:');
	      			
	      			for (var row_cnt=0; row_cnt < result_array.length; row_cnt++) {
	      				var tmp_cell_value = result_array[row_cnt].split('=');
	      				
	      				for (var cell_cnt=0; cell_cnt < tmp_cell_value.length; cell_cnt+=2) {
		      				if (tmp_cell_value[cell_cnt] == 'first_name') {
		      					fname_html = '<td class="reportRecords">'+tmp_cell_value[cell_cnt+1]+'</td>';
			      			} else if (tmp_cell_value[cell_cnt] == 'last_name') {
			      				lname_html = '<td class="reportRecords">'+tmp_cell_value[cell_cnt+1]+'</td>';
			      			} else if (tmp_cell_value[cell_cnt] == 'supplier_code') {
			      				scode = tmp_cell_value[cell_cnt+1];
			      				scode_html = '<td class="reportRecords">'+tmp_cell_value[cell_cnt+1]+'</td>';
			      			} else if (tmp_cell_value[cell_cnt] == 'email') {
			      				email_html = '<td class="reportRecords">'+tmp_cell_value[cell_cnt+1]+'</td>';
			      				this_cust_email = tmp_cell_value[cell_cnt+1];
			      			} else if (tmp_cell_value[cell_cnt] == 'supplier_id') {
			      				sup_id = tmp_cell_value[cell_cnt+1];
			      			}
			      		}
	      			}
	      			
	      			content_table_html += '<tr ' + (i%2 ? 'class="reportListingEven"' : 'class="reportListingOdd"') + '>' + fname_html + lname_html + scode_html + email_html + '<td><a href="javascript:;" onClick="document.getElementById(\''+search_box.id+'\').value=\''+scode+'\';document.getElementById(\''+hidden_id.id+'\').value=\''+sup_id+'\'; jQuery.unblockUI();return false;">Select</a></td></tr>';
			    }
			    
			    if (selection.childNodes.length == 0) {
			    	content_table_html += '<tr><td colspan="5" align="center">No Record Found.</td></tr>';	
			    }
      		}
      		
      		content_table_html += '</table>';
      		content_table_html += '</div>';
      		
      		jquery_confirm_box(content_table_html, 1, 0, 'Search Result for: '+search_box.value, '', '600', '450');
        	//new popUp(550, f_scrollTop()+100, 500, 400, 'search_res', content_table_html, "white", "#00385c", "9pt sans-serif", 'Search Result for: '+search_box.value, "#00385c", "white", "lightgrey", "#00568c", "black", true, true, false, true, false, false, '', '', 'cir_close.gif', 'resize.gif');
      	}
      	
      	hideMainInfo();
    }
	xmlhttp.send(null);
	
	setTimeout('hideMainInfo()', 3000);
}