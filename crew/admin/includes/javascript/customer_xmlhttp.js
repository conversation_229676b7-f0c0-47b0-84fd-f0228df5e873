var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
stateSelectionTitle['text'] = 'Please Select';

function refreshCountryCode(country_id, sel1) {
    var div_objRef1 = DOMCall(sel1);

    if (country_id.value == '') {
        div_objRef1.innerHTML = '';
    } else {
        var server_action = 'refresh_country_code';
        var ref_url = "customer_xmlhttp.php?action=" + server_action + "&country_id=" + country_id.value;

        div_objRef1.innerHTML = 'Loading...';

        xmlhttp.open("GET", ref_url);
        xmlhttp.onreadystatechange = function() {
            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                var country_code = xmlhttp.responseXML.getElementsByTagName("country_dialing_code")[0];

                div_objRef1.innerHTML = '+' + country_code.firstChild.data;
            }
        }
        xmlhttp.send(null);
    }
}

function searchCustomers(res_div, email_box, search_by) {
    var div_obj = DOMCall(res_div);
    var email_box = DOMCall(email_box);
    var server_action = 'get_customer_info';
    var ref_url = "customer_xmlhttp.php?action=" + server_action + "&search_by=" + search_by + "&search_val=" + email_box.value;

    var content_table_html = '';

    if (email_box == null || trim_str(email_box.value) == '') {
        alert('There is nothing to look up.');
        email_box.focus();
        return false;
    }

    var msg = 'Loading... Please be patience!';
    showMainInfo(msg);

    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            res = xmlhttp.responseText;

            var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];

            content_table_html += '	<table border="0" cellspacing="2" cellpadding="2">' +
                    '		<tr>' +
                    '			<td align="right" colspan="4"><a href="javascript:;" onClick="document.getElementById(\'' + res_div + '\').className=\'hide\'" class="highlightLink">Close [x]</a></td>' +
                    '		</tr>' +
                    '		<tr>' +
                    '			<td class="reportBoxHeading">First Name</td>' +
                    '			<td class="reportBoxHeading">Last Name</td>' +
                    '			<td class="reportBoxHeading">E-mail Address</td>' +
                    '			<td class="reportBoxHeading">Action</td>' +
                    '		</tr>';

            if (typeof (selection) != 'undefined' && selection != null) {
                var option_sel = '';

                for (var i = 0; i < selection.childNodes.length; i++) {
                    var fname_html = '';
                    var lname_html = '';
                    var email_html = '';
                    var this_cust_email = '';

                    option_sel = selection.getElementsByTagName("option")[i];

                    var result_array = option_sel.firstChild.nodeValue.split(':~:');

                    for (var row_cnt = 0; row_cnt < result_array.length; row_cnt++) {
                        var tmp_cell_value = result_array[row_cnt].split('=');

                        for (var cell_cnt = 0; cell_cnt < tmp_cell_value.length; cell_cnt += 2) {
                            if (tmp_cell_value[cell_cnt] == 'first_name') {
                                fname_html = '<td class="reportRecords">' + tmp_cell_value[cell_cnt + 1] + '</td>';
                            } else if (tmp_cell_value[cell_cnt] == 'last_name') {
                                lname_html = '<td class="reportRecords">' + tmp_cell_value[cell_cnt + 1] + '</td>';
                            } else if (tmp_cell_value[cell_cnt] == 'email') {
                                email_html = '<td class="reportRecords">' + tmp_cell_value[cell_cnt + 1] + '</td>';
                                this_cust_email = tmp_cell_value[cell_cnt + 1];
                            }
                        }
                    }

                    content_table_html += '<tr ' + (i % 2 ? 'class="reportListingEven"' : 'class="reportListingOdd"') + '>' + fname_html + lname_html + email_html + '<td><a href="javascript:;" onClick="document.getElementById(\'' + email_box.id + '\').value=\'' + this_cust_email + '\'">Select</a></td></tr>';
                }
            }

            content_table_html += '</table>';
        }

        div_obj.innerHTML = content_table_html;
        div_obj.className = 'show';

        hideMainInfo();
    }
    xmlhttp.send(null);

    setTimeout('hideMainInfo()', 3000);
}

function searchCustomersPopUp(search_box, search_by) {
    var search_box = DOMCall(search_box);
    var server_action = 'get_customer_info';
    var ref_url = "customer_xmlhttp.php?action=" + server_action + "&search_by=" + search_by + "&search_val=" + search_box.value;

    var content_table_html = '';

    if (search_box == null || trim_str(search_box.value) == '') {
        alert('There is nothing to look up.');
        search_box.focus();
        return false;
    }

    var msg = 'Loading... Please be patience!';
    showMainInfo(msg);

    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            //res = xmlhttp.responseText;

            var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];

            content_table_html += '	<table border="0" cellspacing="2" cellpadding="2">' +
                    '		<tr>' +
                    '			<td class="reportBoxHeading">First Name</td>' +
                    '			<td class="reportBoxHeading">Last Name</td>' +
                    '			<td class="reportBoxHeading">E-mail Address</td>' +
                    '			<td class="reportBoxHeading">Action</td>' +
                    '		</tr>';

            if (typeof (selection) != 'undefined' && selection != null) {
                var option_sel = '';

                for (var i = 0; i < selection.childNodes.length; i++) {
                    var fname_html = '';
                    var lname_html = '';
                    var email_html = '';
                    var this_cust_email = '';

                    option_sel = selection.getElementsByTagName("option")[i];

                    var result_array = option_sel.firstChild.nodeValue.split(':~:');

                    for (var row_cnt = 0; row_cnt < result_array.length; row_cnt++) {
                        var tmp_cell_value = result_array[row_cnt].split('=');

                        for (var cell_cnt = 0; cell_cnt < tmp_cell_value.length; cell_cnt += 2) {
                            if (tmp_cell_value[cell_cnt] == 'first_name') {
                                fname_html = '<td class="reportRecords">' + tmp_cell_value[cell_cnt + 1] + '</td>';
                            } else if (tmp_cell_value[cell_cnt] == 'last_name') {
                                lname_html = '<td class="reportRecords">' + tmp_cell_value[cell_cnt + 1] + '</td>';
                            } else if (tmp_cell_value[cell_cnt] == 'email') {
                                email_html = '<td class="reportRecords">' + tmp_cell_value[cell_cnt + 1] + '</td>';
                                this_cust_email = tmp_cell_value[cell_cnt + 1];
                            }
                        }
                    }

                    content_table_html += '<tr ' + (i % 2 ? 'class="reportListingEven"' : 'class="reportListingOdd"') + '>' + fname_html + lname_html + email_html + '<td><a href="javascript:;" onClick="document.getElementById(\'' + search_box.id + '\').value=\'' + this_cust_email + '\'; hidebox(\'search_res\');">Select</a></td></tr>';
                }
            }

            content_table_html += '</table>';

            new popUp(550, f_scrollTop() + 100, 500, 400, 'search_res', content_table_html, "white", "#00385c", "9pt sans-serif", 'Search Result for: ' + search_box.value, "#00385c", "white", "lightgrey", "#00568c", "black", true, true, false, true, false, false, '', '', 'cir_close.gif', 'resize.gif');
        }

        hideMainInfo();
    }
    xmlhttp.send(null);

    setTimeout('hideMainInfo()', 3000);
}

function refreshDynamicSelectOptions(sel1, sel2, sel3, _lang, show_title) {
    var div_objRef = DOMCall(sel2);
    var objRef = DOMCall(sel3);
    var res;
    var server_action = 'state_list';
    var selection_available = false;

    if (objRef.type == 'text') {
        objRef = document.createElement("SELECT");
        objRef.name = sel3;
        objRef.id = sel3;
    } else {
        clearOptionList(objRef);
    }

    div_objRef.innerHTML = 'Loading ...';
    sel1.disabled = true;

    var ref_url = "customer_xmlhttp.php?action=" + server_action + "&country_id=" + sel1.value + "&lang=" + _lang;
    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            //res = xmlhttp.responseText;

            clearOptionList(objRef);
            var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];

            if (typeof (selection) != 'undefined' && selection != null) {
                if (show_title == true) {
                    appendToSelect(objRef, stateSelectionTitle['id'], stateSelectionTitle['text']);
                }

                var option_sel = '';
                for (var i = 0; i < selection.childNodes.length; i++) {
                    option_sel = selection.getElementsByTagName("option")[i];
                    appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
                }
            }

            if ((show_title == true && objRef.options.length > 1) || (show_title == false && objRef.options.length > 0)) {
                selection_available = true;
            } else {
                selection_available = false;
            }

            if (!selection_available) {
                objRef = document.createElement("INPUT");
                objRef.name = sel3;
                objRef.id = sel3;
            }

            div_objRef.innerHTML = '';
            div_objRef.appendChild(objRef);
            sel1.disabled = false;
        }
    }

    xmlhttp.send(null);
}

function check_customer_online_status(sel1, customer_id, site_id) {
    var div_objRef = DOMCall(sel1);
    if(site_id == 1){
        var server_action = 'get_og_customer_online_status';
    }

    div_objRef.innerHTML = 'Loading';
    div_objRef.parentNode.setAttribute('bgcolor','grey');

    var ref_url = "customer_xmlhttp.php?action=" + server_action  + "&customer_id=" + customer_id;

    jQuery.ajax({
        url: ref_url,
        type: 'GET',
        dataType: 'xml',
        timeout: 90000,
        error: function () {
            jQuery.unblockUI();
        },
        success: function (xml) {
            console.log(xml)
            var customer_status = jQuery(xml).find('status').text();
            var bgcolor = '#ECC2C4';

            if (customer_status ==  'Online') {
                bgcolor = '#66FF66';
            } else {
                bgcolor = '#FF8000';
            }
            div_objRef.parentNode.setAttribute('bgcolor', bgcolor);
            
            div_objRef.innerHTML = customer_status;
            return true;
        }
    });
            


    
}

function check_customer_2fa_status(sel1, customer_id, _lang) {
    var div_objRef = DOMCall(sel1);
    var server_action = 'get_customer_2fa_status';
    var content_table_html = '';

    div_objRef.innerHTML = 'Loading ...';
    var ref_url = "customer_xmlhttp.php?action=" + server_action + "&lang=" + _lang + "&customer_id=" + customer_id;

    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            var customer_2fa_status = xmlhttp.responseXML.getElementsByTagName("status")[0];
            var kick_button = '';

            if (customer_2fa_status.firstChild.data == 'Enabled') {
                content_table_html += 'Enabled&nbsp&nbsp';
                content_table_html += '<input type="button" onmouseout="this.className=\'InputButton\'" onmouseover="this.className=\'InputButtonOver\'" onclick="disable_2fa();" id="disable_2fa" class="InputButton" value="Disable 2FA"/>';
            } else {
                content_table_html += 'Disabled&nbsp&nbsp';
            }

            div_objRef.innerHTML = content_table_html;
        }
    }
    xmlhttp.send(null);
}

function retrieve_state_list(sel1, sel2, _lang) {
    var div_objRef = DOMCall(sel1);
    var country_id = '';
    var server_action = 'get_state';
    var state_list_boxes_html;
    var country_total = DOMCall(sel2).length;
    div_objRef.innerHTML = '<select name="" multiple="multiple" size="10" style="width: 20em;" disabled><option value=""></option></select>';

    if (country_total == 0) {
        alert("Please select your country.");
        return false;
    } else if (country_total == 1) {
        country_id = DOMCall(sel2).options[0].value;
    } else if (country_total > 1) {
        alert("Allowed to select one country only!");
        return false;
    }

    div_objRef.innerHTML = 'Loading ...';
    var ref_url = "customer_xmlhttp.php?action=" + server_action + "&country_id=" + country_id + "&lang=" + _lang;

    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            //var res = xmlhttp.responseText;
            //alert(res)

            div_objRef.innerHTML = xmlhttp.responseText;
        }
    }
    xmlhttp.send(null);
}

async function kick_customer(sel_og,sel_g2g, customer_id, _lang) {
    var server_action = 'kick_customer';

    var ref_url = "customer_xmlhttp.php?action=" + server_action + "&customer_id=" + customer_id;

    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            var customer_status = xmlhttp.responseXML.getElementsByTagName("status")[0].firstChild.data;
            if (customer_status > 0) {
               $('#'+sel_og).html('');
               $('#'+sel_g2g).html('');
               $('#'+sel_og).parent().attr('bgcolor','grey');
               $('#'+sel_g2g).parent().attr('bgcolor','grey');
            } else {
                alert('Kick failed');
            }
        }
    }
    xmlhttp.send(null);
}

function disable_customer_2fa(sel1, customer_id, _lang, link, user_remark) {
    var server_action = 'disable_customer_2fa';
    // var return_status = false;

    var ref_url = "customer_xmlhttp.php?action=" + server_action + "&customer_id=" + customer_id;

    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            var customer_2fa_status = xmlhttp.responseXML.getElementsByTagName("status")[0].firstChild.data;
            if (customer_2fa_status > 0) {
                check_customer_2fa_status(sel1, customer_id, _lang);
                // return_status = true;
                window.location.href = link + '&user_remark=' + user_remark;
            } else {
                alert('Disable 2FA failed');
            }
        }
    }
    xmlhttp.send(null);

    // return return_status;
}

function show_customer_group_discount(customers_groups_id, show_msg, hide_msg) {
    if (trim(jQuery("#customer_group_discount_info_" + customers_groups_id).html()) == '') {
        jQuery.get('customer_xmlhttp.php?action=get_customer_group_discount&customers_groups_id=' + customers_groups_id, function(xml) {
            jQuery(xml).find('response').each(function() {
                var customer_group_discount_info = jQuery("customer_group_discount_info", this).text();
                if (trim(customer_group_discount_info) != '') {
                    jQuery("#customer_group_discount_info_" + customers_groups_id).html(html_entity_decode(customer_group_discount_info));
                }
            });
            if (jQuery("#toggle_discount_setting_" + customers_groups_id).text() == show_msg) {
                jQuery("#toggle_discount_setting_" + customers_groups_id).text(hide_msg);
            } else {
                jQuery("#toggle_discount_setting_" + customers_groups_id).text(show_msg);
            }
        });
    } else {
        if (jQuery("#toggle_discount_setting_" + customers_groups_id).text() == show_msg) {
            jQuery("#customer_group_discount_info_" + customers_groups_id).show();
            jQuery("#toggle_discount_setting_" + customers_groups_id).text(hide_msg);
        } else {
            jQuery("#customer_group_discount_info_" + customers_groups_id).hide();
            jQuery("#toggle_discount_setting_" + customers_groups_id).text(show_msg);
        }

    }

}

function view_shasso_security_code(action, otp_key, uid) {
    jQuery.get("customer_xmlhttp.php?action=view_shasso_security_code&api_action=" + action + "&otp_key=" + otp_key + "&customer_id=" + uid, function(xml) {
        jQuery(xml).find('response').each(function() {
            jQuery("#shasso_" + otp_key).css('display', 'none');
            jQuery("#shasso_" + otp_key + "_error").css('display', 'none');

            var status = jQuery("status", this).text();
            if (status == 1) {
                var token = jQuery("result", this).children("otp_pin").text();
                var expiry = jQuery("result", this).children("expiry_date").text();

                jQuery("#txt_shasso_" + otp_key).html(token);
                jQuery("#txt_shasso_" + otp_key + "_expiry").html(expiry);
                jQuery('#shasso_' + otp_key).css('display', 'inline-block');
            } else {
                var error = jQuery("error", this).text();
                if (error != "") {
                    jQuery("#shasso_" + otp_key + "_error").html(error);
                    jQuery("#shasso_" + otp_key + "_error").css('display', 'inline-block');
                }
            }
        });
    });
}

function getOGStoreCreditOP(cid) {
    var server_action = 'get_og_storecredit_op';
    var ref_url = 'orders_xmlhttp.php?action=' + server_action + '&customers_id=' + cid;

    jQuery('#og_storecredit').html('<b>Loading...</b>');
    jQuery('#og_op').html('<b>Loading...</b>');

    jQuery.ajax({
        url: ref_url,
        type: 'GET',
        dataType: 'xml',
        timeout: 90000,
        error: function () {
            jQuery.unblockUI();
        },
        success: function (xml) {
            var og_storecredit = jQuery(xml).find('og_storecredit').text();
            var og_op = jQuery(xml).find('og_op').text();
            jQuery('#og_storecredit').html(og_storecredit);
            jQuery('#og_op').html(og_op);
        }
    });
}

function get_pipwave_name_screen_result(cid) {
    var server_action = 'get_pipwave_name_check';
    var ref_url = 'customer_xmlhttp.php?action=' + server_action + '&customer_id=' + cid ;
    var ori_cont = jQuery('#name_screen_btn').html();
  
    jQuery('#name_screen_btn').html ('Loading');
    jQuery.ajax({
        url: ref_url,
        type: 'GET',
        dataType: 'xml',
        timeout: 90000,
        error: function () {
            jQuery.unblockUI();
        },
        success: function (xml) {
            var name_screen = jQuery(xml).find('name_screen').text();
            jQuery('#name_screen_btn').html(ori_cont);

            jQuery('#name_screen').html(name_screen);
        }
    });
}
function get_pipwave_name_screen_result(cid) {
    var server_action = 'get_pipwave_name_check';
    var ref_url = 'customer_xmlhttp.php?action=' + server_action + '&customer_id=' + cid ;
    var ori_cont = jQuery('#name_screen_btn').html();
  
    jQuery('#name_screen_btn').html ('Loading');
    jQuery.ajax({
        url: ref_url,
        type: 'GET',
        dataType: 'xml',
        timeout: 90000,
        error: function () {
            jQuery.unblockUI();
        },
        success: function (xml) {
            var name_screen = jQuery(xml).find('name_screen').text();
            jQuery('#name_screen_btn').html(ori_cont);

            jQuery('#name_screen').html(name_screen);
        }
    });
}