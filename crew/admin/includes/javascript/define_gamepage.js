function add_game_region() {
    var html = '';
    var game_region_id = jQuery('#region').val();
    var game_region_txt = jQuery("#region option[value='" + game_region_id + "']").text();

    if (!empty(game_region_id) && typeof game_region_id != 'undefined' && (jQuery('#game_region_' + game_region_id).length == 0)) {
        html += '<div id="game_region_' + game_region_id + '">';
        html += '<a href="javascript: del_game_region(\'' + game_region_id + '\');"><img src="images/icons/delete.gif" border="0" alt="Delete" title="Delete" align="bottom"></a>';
        html += '&nbsp;&nbsp;' + game_region_txt + '<input type="hidden" name="game[game_region][]" value="' + game_region_id + '" />';
        html += '</div>';
    }

    jQuery('#game_region_section').append(html);
}

function del_game_region(game_region_id) {
    if (!empty(game_region_id) && typeof game_region_id != 'undefined') {
        jQuery('#game_region_' + game_region_id).remove();
    }
}

//var date_start_date = Array();
var giMonthMode=1;

function addTableRow(jTable, lang_id){		    
    var tds = '';
    var jQtable = jQuery(jTable+'_'+lang_id);

    jQtable.each(function(){
        var $table = jQuery(this);
         // Number of td's in the last table row
        var n = jQuery('tr:last td', this).length;
        var r = lang_id+'_'+jQuery('tr', this).length;

        if($table.attr("id") == 'game_features_desc'){
            tds += '<tr>';
            tds += '<td><textarea name="game_features[subdesc][]" wrap="soft" cols="70" rows="3"></textarea></td>';
            tds += '</tr>';
        } else {
            tds += '<tr>';
            tds += '<td style="vertical-align:top;"><input type="text" name="game_details['+lang_id+'][related_link][label][]" style="width:250px" maxlength="180"></td>';
            tds += '<td style="vertical-align:top;"><input type="text" name="game_details['+lang_id+'][related_link][link][]" style="width:250px" maxlength="180"></td>';
            tds += '</tr>';
        }

        if(jQuery('tbody', this).length > 0){
            jQuery('tbody', this).append(tds);
        }else {
            jQuery(this).append(tds);
        }
    });
}
function removeTableRow(jTable){
    var jQtable = jQuery(jTable);
    jQtable.each(function(){
        jQuery('tbody tr:last', this).remove();
    });
}

function formSubmit(frmname) {
    var prompt_alert = true;
    var message = '';
    var cnt = 0;

    document.getElementById('define_landgamepage_form').name= frmname; 

    if ((prompt_alert == true) && (message != '')) {
        alert(message);
    } else {
        document.forms[frmname].submit();
    }
}

jQuery(document).ready(function() {
    jQuery("#languages_tab > ul").tabs();
    jQuery('.languages_tab').css({
        border:'1px solid #C9C9C9'
    });
});

function page_reload(type) {
    var cid = type != "tpl" ? (jQuery('#category_id').length ? jQuery('#category_id').val() : 0) : '';
    document.location.href='?template_key='+jQuery('#template_key').val()+'&id='+cid;
}