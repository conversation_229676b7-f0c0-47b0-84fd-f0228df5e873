body, html{
	margin: 0;
	padding: 0;
	height: 100%;
	border: none;
	overflow: hidden;
	background-color: #FFF;
}

body, html, table, form, textarea{
	font: 12px monospace, sans-serif;
}

#editor{
	border: solid #888 1px;
	overflow: visible;
}

#result{
	z-index: 4;
	overflow-x: auto;
	overflow-y: scroll;
	border-top: solid #888 1px;
	border-bottom: solid #888 1px;
	position: relative;
	clear: both;
}

#result.empty {
	overflow: hidden;
}

#container {
	overflow: hidden;
	border: solid blue 0;
	position: relative;
	z-index: 10;
	padding: 0 5px 0 45px;
	/*padding-right: 5px;*/ 
}

#textarea {
	position: relative;
	top: 0;
	left: 0;
	margin: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	z-index: 7;
	border-width: 0;
	background-color: transparent;
}

#textarea, #textarea:hover {
	outline: none;	/* safari outline fix */
}

#content_highlight {
	white-space: pre;
	margin: 0;
	padding: 0;
	position : absolute;
	z-index: 4;
	overflow: visible;
}


#selection_field {
	margin: 0;
	background-color: #E1F2F9; 
	height: 1px;
	position: absolute;
	z-index: 5;
	top: -100px;
	padding: 0;
	white-space: pre;
	overflow: hidden;
}

#selection_field.show_colors {
	z-index: 3;
	background-color: #EDF9FC;
	color: transparent;
}

#container.wrap_text #content_highlight, #container.wrap_text #selection_field {
	white-space: pre-wrap;       /* css-3 */
	white-space: -moz-pre-wrap !important;  /* Mozilla, since 1999 */
	white-space: -pre-wrap;      /* Opera 4-6 */
	white-space: -o-pre-wrap;    /* Opera 7 */
	word-wrap: break-word;       /* Internet Explorer 5.5+ */
	width: 99%;
}

#line_number {
	position: absolute;
	overflow: hidden;
	border-right: solid black 1px;
	z-index: 8;
	width: 38px;
	padding: 0 5px 0 0;
	margin: 0 0 0 -45px;
	text-align: right;
	color: #AAAAAA;
}

#test_font_size {
	padding: 0;
	margin: 0;
	visibility: hidden;
	position: absolute;
	white-space: pre;
}

pre {
	margin: 0;
	padding: 0;
}

.hidden {
	opacity: 0.2;
	filter: alpha(opacity=20);
}

#result .edit_area_cursor {
	position: absolute;
	z-index: 6;
	background-color: #FF6633;
	top: -100px;
	margin: 1px 0 0 0;
}

#result .edit_area_selection_field .overline {
	background-color: #996600;
}

/* area popup */
.editarea_popup {
	border: solid 1px #888888;
	background-color: #ECE9D8;
	width: 250px;
	padding: 4px;
	position: absolute;
	visibility: hidden;
	z-index: 15;
	top: -500px;
}

.editarea_popup, .editarea_popup table {
	font-family: sans-serif;
	font-size: 10pt;
}

.editarea_popup img {
	border: 0;
}

.editarea_popup .close_popup {
	float: right;
	line-height: 16px;
	border: 0;
	padding: 0;
}

.editarea_popup h1,.editarea_popup h2,.editarea_popup h3,.editarea_popup h4,.editarea_popup h5,.editarea_popup h6 {
	margin: 0;
	padding: 0;
}

.editarea_popup .copyright {
	text-align: right;
}	

/* Area_search */
div#area_search_replace {
	/*width: 250px;*/
}

div#area_search_replace img {
	border: 0;
}

div#area_search_replace div.button {
	text-align: center;
	line-height: 1.7em;
}

div#area_search_replace .button a {
	cursor: pointer;
	border: solid 1px #888888;
	background-color: #DEDEDE;
	text-decoration: none;
	padding: 0 2px;
	color: #000000;
	white-space: nowrap;
}

div#area_search_replace a:hover {	
	/*border: solid 1px #888888;*/
	background-color: #EDEDED;
}

div#area_search_replace  #move_area_search_replace {
	cursor: move;
	border: solid 1px #888;
}

div#area_search_replace  #close_area_search_replace {
	text-align: right;
	vertical-align: top;
	white-space: nowrap;
}

div#area_search_replace  #area_search_msg {
	height: 18px;
	overflow: hidden;
	border-top: solid 1px #888;
	margin-top: 3px;
}

/* area help */
#edit_area_help {
	width: 350px;
}

#edit_area_help div.close_popup {
	float: right;
}

/* area_toolbar */
.area_toolbar {
	/*font: 11px sans-serif;*/
	width: 100%;
	/*height: 21px; */
	margin: 0; 
	padding: 0;
	background-color: #ECE9D8;
	text-align: center;
}

.area_toolbar, .area_toolbar table {
	font: 11px sans-serif;
}

.area_toolbar img {
	border: 0;
	vertical-align: middle;
}

.area_toolbar input {
	margin: 0;
	padding: 0;
}

.area_toolbar select {
    font-family: 'MS Sans Serif',sans-serif,Verdana,Arial;
    font-size: 7pt;
    font-weight: normal;
    margin: 2px 0 0 0 ;
    padding: 0;
    vertical-align: top;
    background-color: #F0F0EE;
}

table.statusbar {
	width: 100%;
}

.area_toolbar td.infos {
	text-align: center;
	width: 130px;
	border-right: solid 1px #888;
	border-width: 0 1px 0 0;
	padding: 0;
}

.area_toolbar td.total {
	text-align: right;
	width: 50px;
	padding: 0;
}

.area_toolbar td.resize {
	text-align: right;
}
/*
.area_toolbar span{
	line-height: 1px;
	padding: 0;
	margin: 0;
}*/

.area_toolbar span#resize_area {
	cursor: nw-resize;
	visibility: hidden;
}

/* toolbar buttons */
.editAreaButtonNormal, .editAreaButtonOver, .editAreaButtonDown, .editAreaSeparator, .editAreaSeparatorLine, .editAreaButtonDisabled, .editAreaButtonSelected {
	border: 0; margin: 0; padding: 0; background: transparent;
	margin-top: 0;
	margin-left: 1px;
	padding: 0;
}

.editAreaButtonNormal {
	border: 1px solid #ECE9D8 !important;
	cursor: pointer;
}

.editAreaButtonOver {
	border: 1px solid #0A246A !important;
	cursor: pointer;
	background-color: #B6BDD2;
}

.editAreaButtonDown {
	cursor: pointer;
	border: 1px solid #0A246A !important;
	background-color: #8592B5;
}

.editAreaButtonSelected {
	border: 1px solid #C0C0BB !important;
	cursor: pointer;
	background-color: #F4F2E8;
}

.editAreaButtonDisabled {
	filter:progid:DXImageTransform.Microsoft.Alpha(opacity=30);
	-moz-opacity:0.3;
	opacity: 0.3;
	border: 1px solid #F0F0EE !important;
	cursor: pointer;
}

.editAreaSeparatorLine {
	margin: 1px 2px;
	background-color: #C0C0BB;
	width: 2px;
	height: 18px;
}

/* waiting screen */
#processing {
	display: none;
	background-color:#ECE9D8;
	border: solid #888 1px;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 100;
	text-align: center;
}

#processing_text {
	position:absolute;
	left: 50%;
	top: 50%;
	width: 200px;
	height: 20px;
	margin-left: -100px;
	margin-top: -10px;
	text-align: center;
}
/* end */


/**** tab browsing area ****/
#tab_browsing_area {
	display: none;
	background-color: #CCC9A8;
	border-top: 1px solid #888;
	text-align: left;
	margin: 0;
}

#tab_browsing_list {
	padding: 0;
	margin: 0;
	list-style-type: none;
	white-space: nowrap;
}
#tab_browsing_list li {
	float: left;
	margin: -1px;
}
#tab_browsing_list a {
	position: relative;
	display: block;
	text-decoration: none;
	float: left;
	cursor: pointer;
	line-height:14px;
}

#tab_browsing_list a span {
	display: block;
	color: #000;
	background: #ECE9D8;
	border:	1px solid #888;
	border-width: 1px 1px 0;
	text-align: center;
	padding: 2px 2px 1px 4px;
	position: relative;	/*IE 6 hack */
}

#tab_browsing_list a b {
	display: block;
	border-bottom: 2px solid #617994;
}

#tab_browsing_list a .edited {
	display: none;
}

#tab_browsing_list a.edited .edited {
	display: inline;
}

#tab_browsing_list a img {
	margin-left: 7px;
}

#tab_browsing_list a.edited img {
	margin-left: 3px;
}

#tab_browsing_list a:hover span {
	background: #F4F2E8;
	border-color: #0A246A;
}

#tab_browsing_list .selected a span {
	background: #046380;
	color: #FFF;
}

#no_file_selected {
	height: 100%;
	width: 150%; /* Opera need more than 100% */
	background: #CCC;
	display: none;
	z-index: 20;
	position: absolute;
}


/*** Non-editable mode ***/
.non_editable #editor {
	border-width: 0 1px;
}

.non_editable .area_toolbar {
	display: none;
}

/*** Auto completion ***/
#auto_completion_area {
	background:	#FFF;
	border: solid 1px #888;
	position: absolute;
	z-index: 15;
	width: 280px;
	height:	180px;
	overflow: auto;
	display:none;
}

#auto_completion_area a, #auto_completion_area a:visited {
	display: block;
	padding: 0 2px 1px;
	color: #000;
	text-decoration: none;
}

#auto_completion_area a:hover, #auto_completion_area a:focus, #auto_completion_area a.focus {
	background:	#D6E1FE;
	text-decoration: none;
}

#auto_completion_area ul {
	margin:	0;
	padding: 0;
	list-style: none inside;
}

#auto_completion_area li {
	padding: 0;
}

#auto_completion_area .prefix {
	font-style: italic;
	padding: 0 3px;
}