editAreaLoader.lang["en"]={
new_document: "new empty document",
search_button: "search and replace",
search_command: "search next / open search area",
search: "search",
replace: "replace",
replace_command: "replace / open search area",
find_next: "find next",
replace_all: "replace all",
reg_exp: "regular expressions",
match_case: "match case",
not_found: "not found.",
occurrence_replaced: "occurences replaced.",
search_field_empty: "Search field empty",
restart_search_at_begin: "End of area reached. Restart at begin.",
move_popup: "move search popup",
font_size: "--Font size--",
go_to_line: "go to line",
go_to_line_prompt: "go to line number:",
undo: "undo",
redo: "redo",
change_smooth_selection: "enable/disable some display features (smarter display but more CPU charge)",
highlight: "toggle syntax highlight on/off",
reset_highlight: "reset highlight (if desyncronized from text)",
help: "about",
save: "save",
load: "load",
line_abbr: "Ln",
char_abbr: "Ch",
position: "Position",
total: "Total",
close_popup: "close popup",
shortcuts: "Shortcuts",
add_tab: "add tabulation to text",
remove_tab: "remove tabulation to text",
about_notice: "Notice: syntax highlight function is only for small text",
toggle: "Toggle editor",
accesskey: "Accesskey",
tab: "Tab",
shift: "Shift",
ctrl: "Ctrl",
esc: "Esc",
processing: "Processing...",
fullscreen: "fullscreen",
syntax_selection: "--Syntax--",
syntax_css: "CSS",
syntax_html: "HTML",
syntax_js: "Javascript",
syntax_php: "Php",
syntax_python: "Python",
syntax_vb: "Visual Basic",
syntax_xml: "Xml",
syntax_c: "C",
syntax_cpp: "CPP",
syntax_basic: "Basic",
syntax_pas: "Pascal",
syntax_brainfuck: "Brainfuck",
syntax_sql: "SQL",
syntax_ruby: "Ruby",
syntax_robotstxt: "Robots txt",
syntax_tsql: "T-SQL",
syntax_perl: "Perl",
syntax_coldfusion: "Coldfusion",
close_tab: "Close file"
};
