editAreaLoader.load_syntax["css"] = {
	'COMMENT_SINGLE' : {1 : '@'}
	,'COMMENT_MULTI' : {'/*' : '*/'}
	,'QUOTEMARKS' : ['"', "'"]
	,'KEYWORD_CASE_SENSITIVE' : false
	,'KEYWORDS' : {
		'attributes' : [
			'aqua', 'azimuth', 'background-attachment', 'background-color',
			'background-image', 'background-position', 'background-repeat',
			'background', 'border-bottom-color', 'border-bottom-style',
			'border-bottom-width', 'border-left-color', 'border-left-style',
			'border-left-width', 'border-right', 'border-right-color',
			'border-right-style', 'border-right-width', 'border-top-color',
			'border-top-style', 'border-top-width','border-bottom', 'border-collapse',
			'border-left', 'border-width', 'border-color', 'border-spacing',
			'border-style', 'border-top', 'border',  'caption-side',
			'clear', 'clip', 'color', 'content', 'counter-increment', 'counter-reset',
			'cue-after', 'cue-before', 'cue', 'cursor', 'direction', 'display',
			'elevation', 'empty-cells', 'float', 'font-family', 'font-size',
			'font-size-adjust', 'font-stretch', 'font-style', 'font-variant',
			'font-weight', 'font', 'height', 'letter-spacing', 'line-height',
			'list-style', 'list-style-image', 'list-style-position', 'list-style-type',
			'margin-bottom', 'margin-left', 'margin-right', 'margin-top', 'margin',
			'marker-offset', 'marks', 'max-height', 'max-width', 'min-height',
			'min-width', 'opacity', 'orphans', 'outline', 'outline-color', 'outline-style',
			'outline-width', 'overflow', 'padding-bottom', 'padding-left',
			'padding-right', 'padding-top', 'padding', 'page', 'page-break-after',
			'page-break-before', 'page-break-inside', 'pause-after', 'pause-before',
			'pause', 'pitch', 'pitch-range',  'play-during', 'position', 'quotes',
			'richness', 'right', 'size', 'speak-header', 'speak-numeral', 'speak-punctuation',
			'speak', 'speech-rate', 'stress', 'table-layout', 'text-align', 'text-decoration',
			'text-indent', 'text-shadow', 'text-transform', 'top', 'unicode-bidi',
			'vertical-align', 'visibility', 'voice-family', 'volume', 'white-space', 'widows',
			'width', 'word-spacing', 'z-index', 'bottom', 'left'
		]
		,'values' : [
			'above', 'absolute', 'always', 'armenian', 'aural', 'auto', 'avoid',
			'baseline', 'behind', 'below', 'bidi-override', 'black', 'blue', 'blink', 'block', 'bold', 'bolder', 'both',
			'capitalize', 'center-left', 'center-right', 'center', 'circle', 'cjk-ideographic', 
            'close-quote', 'collapse', 'condensed', 'continuous', 'crop', 'crosshair', 'cross', 'cursive',
			'dashed', 'decimal-leading-zero', 'decimal', 'default', 'digits', 'disc', 'dotted', 'double',
			'e-resize', 'embed', 'extra-condensed', 'extra-expanded', 'expanded',
			'fantasy', 'far-left', 'far-right', 'faster', 'fast', 'fixed', 'fuchsia',
			'georgian', 'gray', 'green', 'groove', 'hebrew', 'help', 'hidden', 'hide', 'higher',
			'high', 'hiragana-iroha', 'hiragana', 'icon', 'inherit', 'inline-table', 'inline',
			'inset', 'inside', 'invert', 'italic', 'justify', 'katakana-iroha', 'katakana',
			'landscape', 'larger', 'large', 'left-side', 'leftwards', 'level', 'lighter', 'lime', 'line-through', 'list-item', 'loud', 'lower-alpha', 'lower-greek', 'lower-roman', 'lowercase', 'ltr', 'lower', 'low',
			'maroon', 'medium', 'message-box', 'middle', 'mix', 'monospace',
			'n-resize', 'narrower', 'navy', 'ne-resize', 'no-close-quote', 'no-open-quote', 'no-repeat', 'none', 'normal', 'nowrap', 'nw-resize',
			'oblique', 'olive', 'once', 'open-quote', 'outset', 'outside', 'overline',
			'pointer', 'portrait', 'purple', 'px',
			'red', 'relative', 'repeat-x', 'repeat-y', 'repeat', 'rgb', 'ridge', 'right-side', 'rightwards',
			's-resize', 'sans-serif', 'scroll', 'se-resize', 'semi-condensed', 'semi-expanded', 'separate', 'serif', 'show', 'silent', 'silver', 'slow', 'slower', 'small-caps', 'small-caption', 'smaller', 'soft', 'solid', 'spell-out', 'square',
			'static', 'status-bar', 'super', 'sw-resize',
			'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row', 'table-row-group', 'teal', 'text', 'text-bottom', 'text-top', 'thick', 'thin', 'transparent',
			'ultra-condensed', 'ultra-expanded', 'underline', 'upper-alpha', 'upper-latin', 'upper-roman', 'uppercase', 'url',
			'visible',
			'w-resize', 'wait', 'white', 'wider',
			'x-fast', 'x-high', 'x-large', 'x-loud', 'x-low', 'x-small', 'x-soft', 'xx-large', 'xx-small',
			'yellow', 'yes'
		]
		,'specials' : [
			'important'
		]
	}
	,'OPERATORS' :[
		':', ';', '!', '.', '#'
	]
	,'DELIMITERS' :[
		'{', '}'
	]
	,'STYLES' : {
		'COMMENTS': 'color: #AAAAAA;'
		,'QUOTESMARKS': 'color: #6381F8;'
		,'KEYWORDS' : {
			'attributes' : 'color: #48BDDF;'
			,'values' : 'color: #2B60FF;'
			,'specials' : 'color: #FF0000;'
			}
		,'OPERATORS' : 'color: #FF00FF;'
		,'DELIMITERS' : 'color: #60CA00;'
				
	}
};
