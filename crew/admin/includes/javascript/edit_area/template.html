<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" >
	<head>
		<title>EditArea</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		[__CSSRULES__]
		[__JSCODE__]
	</head>
	<body>
		<div id="editor">
			<div class="area_toolbar" id="toolbar_1">[__TOOLBAR__]</div>
			<div class="area_toolbar" id="tab_browsing_area"><ul id="tab_browsing_list" class="menu"> <li> </li> </ul></div>
			<div id="result">
				<div id="no_file_selected"></div>
				<div id="container">
					<div id="cursor_pos" class="edit_area_cursor">&nbsp;</div>
					<div id="end_bracket" class="edit_area_cursor">&nbsp;</div>
					<div id="selection_field"></div>
					<div id="line_number" selec="none"></div>
					<div id="content_highlight"></div>
					<div id="test_font_size"></div>
					<textarea id="textarea" wrap="off" onchange="editArea.execCommand('onchange');" onfocus="javascript:editArea.textareaFocused=true;" onblur="javascript:editArea.textareaFocused=false;">
					</textarea>
				</div>
			</div>
			<div class="area_toolbar" id="toolbar_2">
				<table class="statusbar" cellspacing="0" cellpadding="0">
					<tr>
						<td class="total" selec="none">{$position}:</td>
						<td class="infos" selec="none">
							{$line_abbr} <span  id="linePos">0</span>, {$char_abbr} <span id="currPos">0</span>
						</td>
						<td class="total" selec="none">{$total}:</td>
						<td class="infos" selec="none">
							{$line_abbr} <span id="nbLine">0</span>, {$char_abbr} <span id="nbChar">0</span>
						</td>
						<td class="resize">
							<span id="resize_area">
								<img src="[__BASEURL__]images/statusbar_resize.gif" alt="resize" selec="none">
							</span>
						</td>
					</tr>
				</table>
			</div>
		</div>
		<div id="processing">
			<div id="processing_text">{$processing}</div>
		</div>
		<div id="area_search_replace" class="editarea_popup">
			<table cellspacing="2" cellpadding="0" style="width: 100%">
				<tr>
					<td selec="none">{$search}</td>
					<td><input type="text" id="area_search" /></td>
					<td id="close_area_search_replace">
						<a onclick="Javascript:editArea.execCommand('hidden_search')"><img selec="none" src="[__BASEURL__]images/close.gif" alt="{$close_popup}" title="{$close_popup}" /></a><br />
				</tr><tr>
					<td selec="none">{$replace}</td>
					<td><input type="text" id="area_replace" /></td>
					<td><img id="move_area_search_replace" onmousedown="return parent.start_move_element(event, 'area_search_replace', parent.frames['frame_'+editArea.id]);"  src='"[__BASEURL__]images/move.gif" alt="{$move_popup}" title="{$move_popup}" /></td>
				</tr>
			</table>
			<div class="button">
				<input type="checkbox" id="area_search_match_case" /><label for="area_search_match_case" selec="none">{$match_case}</label>
				<input type="checkbox" id="area_search_reg_exp" /><label for="area_search_reg_exp" selec="none">{$reg_exp}</label>
				<br />
				<a onclick="Javascript:editArea.execCommand('area_search')" selec="none">{$find_next}</a>
				<a onclick="Javascript:editArea.execCommand('area_replace')" selec="none">{$replace}</a>
				<a onclick="Javascript:editArea.execCommand('area_replace_all')" selec="none">{$replace_all}</a><br />
			</div>
			<div id="area_search_msg" selec="none"></div>
		</div>
		<div id="edit_area_help" class="editarea_popup">
			<div class="close_popup">
				<a onclick="Javascript:editArea.execCommand('close_all_inline_popup')"><img src="[__BASEURL__]images/close.gif" alt="{$close_popup}" title="{$close_popup}" /></a>
			</div>
			<div>
				<h2>Editarea [__EA_VERSION__]</h2><br />
				<h3>{$shortcuts}:</h3>
					{$tab}: {$add_tab}<br />
					{$shift}+{$tab}: {$remove_tab}<br />
					{$ctrl}+f: {$search_command}<br />
					{$ctrl}+r: {$replace_command}<br />
					{$ctrl}+h: {$highlight}<br />
					{$ctrl}+g: {$go_to_line}<br />
					{$ctrl}+z: {$undo}<br />
					{$ctrl}+y: {$redo}<br />
					{$ctrl}+e: {$help}<br />
					{$ctrl}+q, {$esc}: {$close_popup}<br />
					{$accesskey} E: {$toggle}<br />
				<br />
				<em>{$about_notice}</em>
				<br /><div class="copyright">&copy; Christophe Dolivet 2007-2008</div>
			</div>
		</div>
	</body>
</html>
