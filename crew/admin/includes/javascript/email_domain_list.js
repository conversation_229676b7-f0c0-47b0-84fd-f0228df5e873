﻿var img = {'del': ['images/icons/off_grey.gif','images/icons/off.gif']};
theme = {newDomainFlashColor: '#ffffaa',errorFlashColor: '#ffffff'};

function form_checking() {
	document.email_domain_group_form.submit();
}

function loadDomains(){
	var domains = '';
	
	resetAjaxErrorTrigger();
	jQuery.ajax({
		type: "post",
		url: 'email_domain_list_xmlhttp.php',
		data: 'action=get_email_domain_list&group_id='+document.email_domain_group_form.groupID.value,
		dataType: 'xml',
		success: function(xml){
			if (jQuery(xml).find('domain').length > 0) {
				jQuery('#total').html(jQuery(xml).find('domain').length);
				jQuery(xml).find("domain").each(function(){
					domains += prepareDomainStr(jQuery(this));
				});
			}
			
			jQuery('#domainlist').html(domains);
		}
	});
}

function newDomain(form) {
	if(form.email_domain.value == '')	return false;
	
	resetAjaxErrorTrigger();
	jQuery.ajax({
		type: "post",
		url: 'email_domain_list_xmlhttp.php',
		data: 'action=insert_domain&group_id='+document.email_domain_group_form.groupID.value+'&domain_name='+form.email_domain.value,
		dataType: 'xml',
		success: function(xml){
			if (jQuery(xml).find('domain').length > 0) {
				jQuery('#total').text( parseInt(jQuery('#total').text()) + parseInt(jQuery(xml).find('domain').length) );
				
				jQuery(xml).find("domain").each(function(){
					jQuery('#domainlist').append(prepareDomainStr(jQuery(this)));
					jQuery('#domainrow_'+jQuery(this).attr('id')).effect("highlight", {color:theme.newDomainFlashColor}, 2000);
				});
			}
			
			if (jQuery(xml).find('error_domains').length > 0) {
				var error_details = lang.insert_error_detail;
				error_details = error_details +' ['+lang.error_domain+' '+jQuery(xml).find("error_domains").text()+']';
				flashError(lang.error, error_details);
			}
		}
	});
	
	return false;
}

function deleteDomain(id) {
	if(!confirm(lang.confirmDelete))	return false;
	
	resetAjaxErrorTrigger();
	jQuery.ajax({
		type: "post",
		url: 'email_domain_list_xmlhttp.php',
		data: 'action=remove_domain&domain_id='+id,
		dataType: 'xml',
		success: function(xml){
			if (jQuery(xml).find('domain').length > 0) {
				jQuery('#total').text( parseInt(jQuery('#total').text()) - parseInt(jQuery(xml).find('domain').length) );
				
				jQuery(xml).find("domain").each(function(){
					jQuery('#domainrow_'+jQuery(this).attr('id')).fadeOut('normal', function(){ jQuery(this).remove() });
				});
			}
		}
	});
	
	return false;
}

function prepareDomainStr(item) {
	return 	'<li id="domainrow_'+item.attr('id')+'">' +
			'<div class="domain-actions">' +
				'<a href="javascript:void(0);" onClick="return deleteDomain('+item.attr('id')+')">' + 
					'<img src="'+img.del[0]+'" onMouseOver="this.src=img.del[1]" onMouseOut="this.src=img.del[0]" title="'+lang.actionDelete+'">' + 
				'</a>' +
			'</div>' +
			'<div class="domain-middle"><span class="domain-title">'+item.text()+'</span></span>'+"</div></li>\n";
}

function flashError(str, details) {
	jQuery("#msg").text(str).css('display','block');
	jQuery("#msgdetails").text(details);
	jQuery("#loading").hide();
	jQuery("#msg").effect("highlight", {color:theme.errorFlashColor}, 700);
}

function resetAjaxErrorTrigger() {
	jQuery("#msg").hide().unbind('ajaxError');
	jQuery("#msgdetails").text('').hide();
}

function toggleMsgDetails() {
	el = jQuery("#msgdetails");
	if(!el) return;
	if(el.css('display') == 'none') el.show();
	else el.hide()
}

function preloadImg() {
	for(i in img) {
		o = new Image();
		o.src = img[i][0];
		if(img[i][1] != img[i][0]) {
			o = new Image();
			o.src = img[i][1];
		}
	}
}