/*
 * FancyBox - jQ<PERSON>y Plugin
 * Simple and fancy lightbox alternative
 *
 * Copyright (c) 20010 <PERSON><PERSON>
 * Examples and documentation at: http://fancybox.net
 *
 * Version: 1.3.0 (02/02/2010)
 * Requires: jQuery v1.3+
 *
 * Dual licensed under the MIT and GPL licenses:
 *   http://www.opensource.org/licenses/mit-license.php
 *   http://www.gnu.org/licenses/gpl.html
 */
 
#fancybox-loading {
	position: fixed;
	top: 50%;
	left: 50%;
	height: 40px;
	width: 40px;
	margin-top: -20px;
	margin-left: -20px;
	cursor: pointer;
	overflow: hidden;
	background: transparent;
	z-index: 1104;
	display: none;
}

* html #fancybox-loading {	/* IE6 */
	position: absolute;
	margin-top: 0;
}

#fancybox-loading div {
	position: absolute;
	top: 0;
	left: 0;
	width: 40px;
	height: 480px;
	background: transparent url('fancy_loading.png') no-repeat;
}

#fancybox-overlay {
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background: #000;
	z-index: 1100;
	display: none;
}

* html #fancybox-overlay {	/* IE6 */
	position: absolute;
	width: 100%;
}

#fancybox-tmp {
	padding: 0;
	margin: 0;
	border: 0;
	overflow: auto;
	display: none;
}

#fancybox-wrap {
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	padding: 20px;
	z-index: 1101;
	display: none;
}

#fancybox-outer {
	position: relative;
	width: 100%;
	height: 100%;
	background: #FFF;
}

#fancybox-inner {
	position: absolute;
	top: 0;
	left: 0;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: 0;
	outline: none;
	overflow: hidden;
}

#fancybox-hide-sel-frame {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: transparent;
}

#fancybox-close {
	position: absolute;
	top: -15px;
	right: -15px;
	width: 32px;
	height: 32px;
	background: url('fancy_close.png') top left no-repeat;
	cursor: pointer;
	z-index: 1103;
	display: none;
}

#fancybox_error {
	color: #444;
	font: normal 12px/20px Arial;
}

#fancybox-content {
	height: auto;
	width: auto;
	padding: 0;
	margin: 0;
}

#fancybox-img {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
	border: none;
	outline: none;
	line-height: 0;
	vertical-align: top;
	-ms-interpolation-mode: bicubic;
}

#fancybox-frame {
	position: relative;
	width: 100%;
	height: 100%;
	border: none;
	display: block;
}

#fancybox-title {
	position: absolute;
	bottom: 0;
	left: 0;
	font-family: Arial;
	font-size: 12px;
	z-index: 1102;
}

.fancybox-title-inside {
	padding: 10px 0;
	text-align: center;
	color: #333;
}

.fancybox-title-outside {
	padding-top: 5px;
	color: #FFF;
	text-align: center;
	font-weight: bold;
}

.fancybox-title-over {
	color: #FFF;
	text-align: left;
}

#fancybox-title-over {
	padding: 10px;
	background: url('fancy_title_over.png');
	display: block;
}

#fancybox-title-wrap {
	display: inline-block;
}

#fancybox-title-wrap span {
	height: 32px;
	float: left;
}

#fancybox-title-left {
	padding-left: 15px;
	background: transparent url('fancy_title_left.png') repeat-x;
}

#fancybox-title-main {
	font-weight: bold;
	line-height: 29px;
	background: transparent url('fancy_title_main.png') repeat-x;
	color: #FFF;
}

#fancybox-title-right {
	padding-left: 15px;
	background: transparent url('fancy_title_right.png') repeat-x;
}

#fancybox-left, #fancybox-right {
	position: absolute;
	bottom: 0px;
	height: 100%;
	width: 35%;
	cursor: pointer;
	outline: none;
	background-image: url('blank.gif');
	z-index: 1102;
	display: none;
}

#fancybox-left {
	left: 0px;
}

#fancybox-right {
	right: 0px;
}

#fancybox-left-ico, #fancybox-right-ico {
	position: absolute;
	top: 50%;
	left: -9999px;
	width: 30px;
	height: 30px;
	margin-top: -15px;
	cursor: pointer;
	z-index: 1102;
	display: block;
}

#fancybox-left-ico {
	background: transparent url('fancy_nav_left.png') no-repeat;
}

#fancybox-right-ico {
	background: transparent url('fancy_nav_right.png') no-repeat;
}

#fancybox-left:hover, #fancybox-right:hover {
	visibility: visible;    /* IE6 */
}

#fancybox-left:hover span {
	left: 20px;
}

#fancybox-right:hover span {
	left: auto;
	right: 20px;
}

div.fancy-bg {
	position: absolute;
	padding: 0;
	margin: 0;
	border: 0;
	z-index: 1001;
}

div#fancy-bg-n {
	top: -20px;
	left: 0;
	width: 100%;
	height: 20px;
	background: transparent url('fancy_shadow_n.png') repeat-x;
}

div#fancy-bg-ne {
	top: -20px;
	right: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('fancy_shadow_ne.png') no-repeat;
}

div#fancy-bg-e {
	top: 0;
	right: -20px;
	height: 100%;
	width: 20px;
	background: transparent url('fancy_shadow_e.png') repeat-y;
}

div#fancy-bg-se {
	bottom: -20px;
	right: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('fancy_shadow_se.png') no-repeat;
}

div#fancy-bg-s {
	bottom: -20px;
	left: 0;
	width: 100%;
	height: 20px;
	background: transparent url('fancy_shadow_s.png') repeat-x;
}

div#fancy-bg-sw {
	bottom: -20px;
	left: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('fancy_shadow_sw.png') no-repeat;
}

div#fancy-bg-w {
	top: 0;
	left: -20px;
	height: 100%;
	width: 20px;
	background: transparent url('fancy_shadow_w.png') repeat-y;
}

div#fancy-bg-nw {
	top: -20px;
	left: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('fancy_shadow_nw.png') no-repeat;
}