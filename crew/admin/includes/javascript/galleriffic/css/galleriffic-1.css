div.content {
	/* The display of content is enabled using jQuery so that the slideshow content won't display unless javascript is enabled. */
	display: none;
	float: right;
	width: 550px; 
}
div.content a, div.navigation a {
	text-decoration: none;
	color: #777;
}
div.content a:focus, div.content a:hover, div.content a:active {
	text-decoration: underline;
}
div.controls {
	margin-top: 5px;
	height: 23px;
}
div.controls a {
	padding: 5px;
}
div.ss-controls {
	float: left;
}
div.nav-controls {
	float: right;
}
div.slideshow-container {
	position: relative;
	clear: both;
	height: 502px; /* This should be set to be at least the height of the largest image in the slideshow */
}
div.loader {
	position: absolute;
	top: 0;
	left: 0;
	background-image: url('loader.gif');
	background-repeat: no-repeat;
	background-position: center;
	width: 550px;
	height: 502px; /* This should be set to be at least the height of the largest image in the slideshow */
}
div.slideshow {

}
div.slideshow span.image-wrapper {
	display: block;
	position: absolute;
	top: 0;
	left: 0;
}
div.slideshow a.advance-link {
	display: block;
	width: 550px;
	height: 502px; /* This should be set to be at least the height of the largest image in the slideshow */
	line-height: 502px; /* This should be set to be at least the height of the largest image in the slideshow */
	text-align: center;
}
div.slideshow a.advance-link:hover, div.slideshow a.advance-link:active, div.slideshow a.advance-link:visited {
	text-decoration: none;
}
div.slideshow img {
	vertical-align: middle;
	border: 1px solid #ccc;
}
div.download {
	float: right;
}
div.caption-container {
	
}
span.image-caption {
	display: block;
	position: absolute;
}
div.caption {
	background-color: #000;
	padding: 12px;
	color: #ccc;
}
div.caption a {
	color: #fff;
}
div.image-title {
	font-weight: bold;
	font-size: 1.4em;
}

div.image-desc {
	line-height: 1.3em;
	padding-top: 12px;
}
div.navigation {
	/* The navigation style is set using jQuery so that the javascript specific styles won't be applied unless javascript is enabled. */
}
ul.thumbs {
	clear: both;
	margin: 0;
	padding: 0;
}
ul.thumbs li {
	float: none;
	padding: 0;
	margin: 0;
	list-style: none;
}
a.thumb {
	padding: 0;
	display: inline;
	border: none;
}
ul.thumbs li.selected a.thumb {
	color: #000;
	font-weight: bold;
}
a.thumb:focus {
	outline: none;
}
ul.thumbs img {
	border: none;
	display: block;
}
div.pagination {
	clear: both;
}
div.navigation div.top {
	margin-bottom: 12px;
	height: 11px;
}
div.navigation div.bottom {
	margin-top: 12px;
}
div.pagination a, div.pagination span.current, div.pagination span.ellipsis {
	display: block;
	float: left;
	margin-right: 2px;
	padding: 4px 7px 2px 7px;
	border: 1px solid #ccc;
}
div.pagination a:hover {
	background-color: #eee;
	text-decoration: none;
}
div.pagination span.current {
	font-weight: bold;
	background-color: #000;
	border-color: #000;
	color: #fff;
}
div.pagination span.ellipsis {
	border: none;
	padding: 5px 0 3px 2px;
}
#captionToggle a {
	float: right;
	display: block;
	background-image: url('caption.png');
	background-repeat: no-repeat;
	background-position: right;
	margin-top: 5px;
	padding: 5px 30px 5px 5px;
}
