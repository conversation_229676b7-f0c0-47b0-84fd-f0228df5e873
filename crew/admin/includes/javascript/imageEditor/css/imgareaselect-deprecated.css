/*
 * imgAreaSelect style to be used with deprecated options
 */

.imgareaselect-border1, .imgareaselect-border2,
.imgareaselect-border3, .imgareaselect-border4 {
    opacity: 0.5;
    filter: alpha(opacity=50);
}

.imgareaselect-border1 {
	border: solid 1px #000;
}

.imgareaselect-border2 {
	border: dashed 1px #fff;
}

.imgareaselect-handle {
    background-color: #fff;
    border: solid 1px #000;
    opacity: 0.5;
    filter: alpha(opacity=50);
}

.imgareaselect-outer {
    background-color: #000;
    opacity: 0.4;
    filter: alpha(opacity=40);
}

.imgareaselect-selection {
	background-color: #fff;
	opacity: 0;
	filter: alpha(opacity=0);
}
