<?php
$i18n['grayPalette'] = 'GrayScale Palette';
$i18n['macPalette']  = 'Mac OS Palette';
$i18n['webSafe']     = 'Web Safe Palette';
$i18n['winPalette']  = 'Windows System Palette';

?>
<!--
Title: Tigra Color Picker
URL: http://www.softcomplex.com/products/tigra_color_picker/
Version: 1.1
Date: 06/26/2003 (mm/dd/yyyy)
Note: Permission given to use this script in ANY kind of applications if
   header lines are left unchanged.
Note: Script consists of two files: picker.js and picker.php
-->

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Color Picker</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style type="text/css">
<!--
body { background: buttonface; margin: 5px; }
.bd { border: 1px #fff; }
.s  { width: 100%; display: block; }
select,input { font-family: verdana,sans-serif; font-size: 11px; }
//-->
</style>
<script type="text/javascript">
<!--
function focusWindow()
{
    if ( !modal ) window.focus();

    mytimer = setTimeout( 'focusWindow()', 0 );
}
//-->
</script>
</head>
<body onload="P.C( P.initPalette );mytimer=setTimeout( 'focusWindow()', 0 );">
<form action="#top">
    <table cellpadding="0" cellspacing="2" border="0" width="100%" align="center">
        <tr>
            <td style="text-align: center;">
                <select name="type" onchange="P.C( this.selectedIndex )" class="s">
	                <option><?php echo $i18n['webSafe']; ?></option>
	                <option><?php echo $i18n['winPalette']; ?></option>
	                <option><?php echo $i18n['grayPalette']; ?></option>
	                <option><?php echo $i18n['macPalette']; ?></option>
                </select>
            </td>
        </tr>
        <tr>
            <td style="text-align: center; border: 1px solid; border-color: #C2BEB6 #F6F2EA #F6F2EA #C2BEB6;">
                <script type="text/javascript">
                <!--
	            var P = opener.TCP;
	            onload = "P.show( P.initPalette )";
	            document.forms[0].elements[0].selectedIndex = P.initPalette;
	            P.draw( window, document );
	            //-->
                </script>
            </td>
        </tr>
    </table>
</form>
<script type="text/javascript">
<!--
function focusWindow() 
{
    window.focus();
    myTimer = setTimeout( 'focusWindow()', 0 );
}

function exitEditor()
{
    window.close();
}
//-->
</script>
</body>
</html>