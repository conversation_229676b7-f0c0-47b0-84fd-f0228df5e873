jQuery(document).ready(function() {
	jQuery('a.grouped_elements').fancybox({	
	'transitionIn'	:	'fade',
	'transitionOut'	:	'fade',
	'padding'		:   40,
	'speedIn'		:	600, 
	'speedOut'		:	200, 
	'overlayShow'	:	false,
	'type' 			:   'image',
	'titlePosition' :   'float',
	'titleFromAlt'  :   true,
	'titleFormat'	:   function(title, currentArray, currentIndex, currentOpts) {
			return '<span id="fancybox-title-over" style="color:#FFFFFF;text-align: center">Image ' + (currentIndex + 1) + ' / ' + currentArray.length + (title.length ? ' &nbsp; ' + title : '') 
				+ '&nbsp;&nbsp;' + '<a href="'+img_conf_obj.FILENAME_IMAGE_UPLOAD+'?action=del_image_file&gallery='+img_conf_obj.gallery+'&dir='+img_conf_obj.dir+'&file='+title+'"' 
				+ 'onclick="return confirm(\''+img_conf_obj.TEXT_INFO_NOTICE_DELETE_CONFIRMATION+'?\')">'+img_conf_obj.img_del+'</a></span>';
		}
	});
	
	jQuery('a.single_element').fancybox({
	'overlayShow'	:   false,
	'transitionIn'	:   'elastic',
	'transitionOut'	:   'elastic',	
	'padding'		:   40,
	'speedIn'		:	600, 
	'speedOut'		:	200, 
	'overlayShow'	:	false,
	'type' 			:   'image',
	'titlePosition' :   'float',
	'titleFromAlt'  :   true,
	'hideOnContentClick'    : false,
	'titleFormat'	:   function(title, currentArray, currentIndex, currentOpts) {
			return '<span id="fancybox-title-over" style="color:#FFFFFF;text-align: center">Image ' + (currentIndex + 1) + ' / ' + currentArray.length + (title.length ? ' &nbsp; ' + title : '') 
				+ '&nbsp;&nbsp;' + '<a href="'+img_conf_obj.FILENAME_IMAGE_UPLOAD+'?action=del_image_file&gallery='+img_conf_obj.gallery+'&dir='+img_conf_obj.dir+'&file='+title+'"' 
				+ 'onclick="return confirm(\''+img_conf_obj.TEXT_INFO_NOTICE_DELETE_CONFIRMATION+'?\')">'+img_conf_obj.img_del+'</a></span>';
		}
	});
	
	jQuery('a.uploaded_element').fancybox({
	'overlayShow'	:   false,
	'transitionIn'	:   'elastic',
	'transitionOut'	:   'elastic',	
	'padding'		:   40,
	'speedIn'		:	600, 
	'speedOut'		:	200, 
	'overlayShow'	:	false,
	'type' 			:   'image',
	'titlePosition' :   'float',
	'titleFromAlt'  :   true,
	'hideOnContentClick'    : true,
	'titleFormat'	:   function(title, currentArray, currentIndex, currentOpts) {
			return '<span id="fancybox-title-over" style="color:#FFFFFF;text-align: center">Image ' + (currentIndex + 1) + ' / ' + currentArray.length + (title.length ? ' &nbsp; ' + title : '') 
		}
	});
	
	var SID = getCookie("osCAdminID");
	jQuery("#upload").uploadify({
	'uploader': 'includes/javascript/uploadify/uploadify.swf',
	'cancelImg': 'includes/javascript/uploadify/cancel.png',
	'script': 'image_upload_xmlhttp.php?action=do_upload',
	'scriptData'  : {'SID': SID, 'REF':img_conf_obj.REF, 'dir': img_conf_obj.dir, 'mass_upload_cache':img_conf_obj.mass_upload_cache},
	'folder': img_conf_obj.dir,
	'multi': true,
	'auto' : false,
	'buttonText': 'Select Files',
	'checkScript': 'image_upload_xmlhttp.php?action=validate_file',
	'displayData': 'speed',	
	'sizeLimit': '5242880', //5MB
	'displayData': 'percentage', 
	'fileDesc': 'Multimedia(.jpg .gif .png .swf .flv)',
	'fileExt': '*.jpg;*.jpeg;*.gif;*.png;*.swf;*.flv',
	'queueSizeLimit' : '20',
	'simUploadLimit': '10',
	onCheck: function(event,data,key) {
		jQuery('#upload' + key).addClass('uploadifyError');
		jQuery('#upload' + key).find('.percentage').text(' - File Exists');
	},
	onSelect: function(event, queueID, fileObj){
		var file_type = fileObj.type.toLowerCase();
		if(file_type!=".jpg" && file_type!=".jpeg" && file_type!=".gif" && file_type!=".png" 
			&& file_type!=".swf" && file_type!=".flv"){
			jQuery("#fileUploadError").show(); 
			jQuery("#upload").uploadifyCancel(queueID); 
			return false;
		}else{
			jQuery("#fileUploadError").hide();
		}
	},
	onError: function (event, ID, fileObj, errorObj) {
		if (errorObj.status == 404)
			alert('Could not find upload script.');
		else if (errorObj.type ==="File Size")
			alert(fileObj.name+' '+errorObj.type+' Exceed Limit: '+Math.round(errorObj.info/1024/1024)+'MB');
		else
			alert('error '+errorObj.type+": "+errorObj.info+" - "+errorObj.toSource());
	},
	onComplete: function(event, queueID, fileObj, response, data) {
		 data= response.split('#');
		jQuery('#message_complete').html(img_conf_obj.TEXT_INFO_HEADING_UPLOADED_FILES_TITLE);
                jQuery('#filesUploaded').append('<a href=javascript:callFancy("'+img_conf_obj.web_path+encodeURI(data[1].toLowerCase())+'","'+encodeURI(data[1])+'"); >'+data[1]+'</a><br>');
		if(data[0] != '1') {
			if(data[0] != '{"Filename":"' + s3_file_name + '"}'){
				alert(data[0]);
			}
		}
	},
	onAllComplete: function (event, data) { 
		alert("The total uploaded files: " + data.filesUploaded + "\n" + "The total of errors: " + data.errors); 
		jQuery('#message_all_complete').html(img_conf_obj.TEXT_INFO_NOTICE_FILE_UPLOAD_SUCCESSFUL).fadeIn('slow', function() { setTimeout('jQuery("#message_all_complete").fadeOut("slow");', 2000); });
	}
	
	}); 
	
	jQuery('#ufile').MultiFile({
		accept:'gif|jpg|jpeg|png|swf|flv', max:20, STRING: {
			remove: '<img src="includes/javascript/uploadify/cancel.png" border="0" title="Click to remove" height="16" width="16" alt="x"/>'
		}
	});
	
	function getCookie(Name){
	var re=new RegExp(Name+"=[^;]+", "i"); //construct RE to search for target name/value pair
		if (document.cookie.match(re)) //if cookie found
		return document.cookie.match(re)[0].split("=")[1] //return its value
	return ""
	}
	
});

function callFancy(my_href,my_title) {
	var url = document.getElementById("hiddenclicker");
	url.href = my_href;
	url.title = my_title;
	jQuery('#hiddenclicker').trigger('click');
}