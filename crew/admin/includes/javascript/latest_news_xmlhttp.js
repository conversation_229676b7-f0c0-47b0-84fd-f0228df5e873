var newsSelectionTitle = Array();
newsSelectionTitle['id'] = '';
newsSelectionTitle['text'] = '---Select Product---';

function change_status(id, flag, action) {
	
	var status	= confirm('Confirm to update status?');
	if (status ==  true) {
		switch (action) {
			case 'update_option_status':
				var objRef = DOMCall('status-o-'+id);
				
				var ref_url = "news_listing_xmlhttp.php?action=" + action + "&flag=" + flag + "&oID="+id;
				xmlhttp.open("GET", ref_url); 
				
			    xmlhttp.onreadystatechange = function() {
			      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
						var item = xmlhttp.responseXML.getElementsByTagName("item")[0];
						image_stat = item.getElementsByTagName("image_status")[0];
						objRef.innerHTML = image_stat.firstChild.nodeValue;
			      	}
			    }
			break;
			
			case 'update_event_status':
				var objRef = DOMCall('status-e-'+id);
				
				var ref_url = "news_listing_xmlhttp.php?action=" + action + "&flag=" + flag + "&eID="+id;
				xmlhttp.open("GET", ref_url); 
				
			    xmlhttp.onreadystatechange = function() {
			      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
						var item = xmlhttp.responseXML.getElementsByTagName("item")[0];
						image_stat = item.getElementsByTagName("image_status")[0];
						objRef.innerHTML = image_stat.firstChild.nodeValue;
			      	}
			    }
			break;
		}
	}
	xmlhttp.send(null);
}

function refreshDynamicSelectOptions(sel1, sel2, _lang, show_title) {
	var objRef = DOMCall(sel2);
	var res;
	
    objRef.disabled = true;
   	clearOptionList(objRef);
	objRef.options[0] = new Option('Loading ...', '');
	
	var ref_url = "news_listing_xmlhttp.php?news_id="+sel1.value+"&lang="+_lang;

	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
      		
      		clearOptionList(objRef);

      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];

      		if (typeof (selection) != 'undefined' && selection != null) {
      			if (show_title == true) {
      				appendToSelect(objRef, newsSelectionTitle['id'], newsSelectionTitle['text']);
      			}
      			var option_sel = '';
	      		for (var i=0; i < selection.childNodes.length; i++) {
	      			option_sel = selection.getElementsByTagName("option")[i];
	      			appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
			    }
      		}

      		if ((show_title == true && objRef.options.length > 1) || (show_title == false && objRef.options.length > 0)) {
      			objRef.disabled = false;
      		} else {
      			objRef.options[0] = new Option('No product is found', '');
      		}
      	}
    }

    xmlhttp.send(null);
}