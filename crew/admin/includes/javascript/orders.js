var remark_link_pool = Array();
var asynMode = true;

function doLocked(admin, o, _lang, act, element_id, btn_show_time) {
    if (pageLoaded == null || !pageLoaded)
        return;
    if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4)
        return;

    var objRef = DOMCall(element_id);
    var res;
    var user_msg = '';

    if (act == "ulo") {
        user_msg = prompt("Leave any message for unlocking this order?", "");
        if (user_msg != null) {
            //user_msg = URLEncode(user_msg);
        } else {
            return false;
        }
    }

    objRef.innerHTML = '';
    var msg = 'Loading... Please be patience!';
    showMainInfo(msg);

    var ref_url = "orders_xmlhttp.php?action=show_lock_btn&subaction=" + act + "&adm=" + admin + "&oid=" + o + "&lang=" + _lang + "&log_comment=" + user_msg + "&from_time=" + btn_show_time;
    xmlhttp.open("GET", ref_url, asynMode);
    xmlhttp.onreadystatechange = function () {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            //res = xmlhttp.responseText;

            var A = (typeof (xmlhttp.responseXML.getElementsByTagName("action")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("action")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("action")[0].firstChild.data : '';

            if (act == 'l') {
                if (A == 'Prompt Alert Message') {
                    if (typeof (xmlhttp.responseXML.getElementsByTagName("lock_msg")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("lock_msg")[0] != null) {
                        var l_msg = xmlhttp.responseXML.getElementsByTagName("lock_msg")[0].firstChild.data
                    } else {
                        var l_msg = '';
                    }
                    alert(l_msg);

                    if (typeof (xmlhttp.responseXML.getElementsByTagName("close_win")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("close_win")[0] != null) {
                        if (xmlhttp.responseXML.getElementsByTagName("close_win")[0].firstChild.data == '1') {
                            if (document.all) {	// For IE
                                window.close();
                            } else {
                                /*****************************************************************************************
                                 The first step is to fool the browser into thinking that it was opened with a script.
                                 
                                 This opens a new page, (non-existent), into a target frame/window, 
                                 (_parent which of course is the window in which the script is executed, so replacing itself), 
                                 and defines parameters such as window size etc, (in this case none are defined as none are needed). 
                                 Now that the browser thinks a script opened a page we can quickly close it in the standard way.
                                 *****************************************************************************************/
                                window.open('', '_parent', '');
                                window.close();
                            }
                        }
                    }
                }

                window.location.reload();
            } else {
                showBtn(admin, o, _lang, element_id, A);
                lockingHistory(o, 0, 'locking_history');
            }
        }
    }

    xmlhttp.send(null);

    setTimeout('hideMainInfo()', 3000);
}

function showBtn(admin, o, _lang, element_id, act_msg) {
    var objRef = DOMCall(element_id);
    var deliveryBoxObj = DOMCall('partial_delivery_box');
    var purchaseETABoxObj = DOMCall('purchase_eta_box');

    if (typeof (xmlhttp.responseXML.getElementsByTagName("lock_msg")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("lock_msg")[0] != null) {
        var l_msg = xmlhttp.responseXML.getElementsByTagName("lock_msg")[0].firstChild.data
    } else {
        var l_msg = '';
    }
    var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';
    var Sub_A = (typeof (xmlhttp.responseXML.getElementsByTagName("subaction")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("subaction")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("subaction")[0].firstChild.data : '';

    if (act_msg == "Show Lock Button") {
        var show_time = (typeof (xmlhttp.responseXML.getElementsByTagName("time")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("time")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("time")[0].firstChild.data : '';

        objRef.innerHTML = '<div id="navBtn" style="float:left;">' + "\n" +
                '<ul>' + "\n" +
                '<li id="lock_btn"><a href="javascript:;" onClick="doLocked(\'' + admin + '\', \'' + o + '\', \'' + _lang + '\', \'l\', \'' + element_id + '\', \'' + show_time + '\');" title="Locking this order">LOCK</a></li>' + "\n" +
                '</ul>' + "\n" +
                '</div>' + "\n" +
                '<div style="width:90%">' + "\n" +
                l_msg + "\n" +
                '</div>';

        DOMCall('order_comment_box').className = 'hide';
        DOMCall('order_comment_selection_box').className = 'hide';

        if (typeof (deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
            deliveryBoxObj.className = 'hide';
        }

        if (typeof (purchaseETABoxObj) != 'undefined' && purchaseETABoxObj != null) {
            purchaseETABoxObj.className = 'hide';
        }

        for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
            var custom_info_box = DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]);
            if (typeof (custom_info_box) != 'undefined' && custom_info_box != null) {
                custom_info_box.className = 'hide';
            }

            var custom_extra_info_box = DOMCall('custom_extra_info_box_' + unique_product_ref_array[p_cnt]);
            if (typeof (custom_extra_info_box) != 'undefined' && custom_extra_info_box != null) {
                custom_extra_info_box.className = 'hide';
            }

            var sup_sel_box = DOMCall('supplier_select_' + unique_product_ref_array[p_cnt]);
            if (typeof (sup_sel_box) != 'undefined' && sup_sel_box != null) {
                sup_sel_box.disabled = true;
            }

            //For cdkey, each unique key is grouped into its parent product, so we access them individually here.
            var cdkey_release_str = 'cdkey_release_link_' + unique_product_ref_array[p_cnt];
            var tbodys = document.getElementsByTagName('tbody');
            for (var i = 0; i < tbodys.length; i++) {
                id = tbodys[i].getAttribute("id");
                if (typeof (id) == 'string') {
                    if (parseInt(id.search(cdkey_release_str)) >= 0) {
                        var cdkey_release_link = DOMCall(id);
                        cdkey_release_link.className = 'hide';
                    }
                }
            }
        }

        for (loc in location_control_array) {
            if (DOMCall(loc) != null) {
                DOMCall(loc).innerHTML = '-Hidden-';
            }
        }

        for (delivery in partial_delivery_control_array) {
            if (delivery.indexOf("deliver_") === 0) {
                var ref_id = replace(delivery, 'deliver_', '');
                var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
                var delivery_obj = DOMCall(delivery);
                if (delivery_obj != null) {
                    qty_sign_obj.disabled = true;
                    delivery_obj.disabled = true;
                    delivery_obj.value = '-Hidden-';
                    delivery_obj.size = 4;
                }
            }
        }

        disabledFormInputs('partial_delivery', true);
        disabledFormInputs('compensation_delivery_form', true);
        disabledFormInputs('compensate_form', true);

        for (remark_link_ins in remark_link_pool) {
            if (remark_link_ins.indexOf("set_remark_") === 0) {
                var remark_link_obj = DOMCall(remark_link_ins);
                if (remark_link_obj != null)
                    remark_link_obj.innerHTML = '-Hidden-';
            }
        }
    } else if (act_msg == "Show Unlock Button") {
        objRef.innerHTML = '<div id="navBtn" style="float:left;">' + "\n" +
                '<ul>' + "\n" +
                '<li id="unlock_btn"><a href="javascript:;" onClick="doLocked(\'' + admin + '\', \'' + o + '\', \'' + _lang + '\', \'' + (Sub_A == "Prompt For Unlocking Msg" ? 'ulo' : 'ul') + '\', \'' + element_id + '\');" title="Unlocking this order">UNLOCK</a></li>' + "\n" +
                '</ul>' + "\n" +
                '</div>' + "\n" +
                '<div style="width:90%">' + "\n" +
                l_msg + "\n" +
                '</div>' + "\n";

        DOMCall('order_comment_box').className = 'show';
        DOMCall('order_comment_selection_box').className = 'show';
        if (typeof (deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
            deliveryBoxObj.className = 'show';
        }
        if (typeof (purchaseETABoxObj) != 'undefined' && purchaseETABoxObj != null) {
            purchaseETABoxObj.className = 'show';
        }

        for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
            var custom_info_box = DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]);
            if (typeof (custom_info_box) != 'undefined' && custom_info_box != null) {
                custom_info_box.className = 'show';
            }

            var custom_info_box = DOMCall('custom_extra_info_box_' + unique_product_ref_array[p_cnt]);
            if (typeof (custom_extra_info_box) != 'undefined' && custom_extra_info_box != null) {
                custom_extra_info_box.className = 'show';
            }

            var sup_sel_box = DOMCall('supplier_select_' + unique_product_ref_array[p_cnt]);
            if (typeof (sup_sel_box) != 'undefined' && sup_sel_box != null) {
                sup_sel_box.disabled = false;
            }

            var cdkey_release_link = DOMCall('cdkey_release_link_' + unique_product_ref_array[p_cnt]);
            if (typeof (cdkey_release_link) != 'undefined' && cdkey_release_link != null) {
                cdkey_release_link.className = 'show';
            }
        }

        //DOMCall('custom_info_box').className = 'show';

        for (loc in location_control_array) {
            if (DOMCall(loc) != null) {
                DOMCall(loc).innerHTML = URLDecode(location_control_array[loc]);
            }
        }

        for (delivery in partial_delivery_control_array) {
            if (delivery.indexOf("deliver_") === 0) {
                var ref_id = replace(delivery, 'deliver_', '');
                var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
                var delivery_obj = DOMCall(delivery);
                if (delivery_obj != null) {
                    qty_sign_obj.disabled = false;
                    delivery_obj.disabled = false;
                    delivery_obj.value = '0';
                    delivery_obj.size = 4;
                }
            }
        }

        disabledFormInputs('partial_delivery', false);
        disabledFormInputs('compensation_delivery_form', false);
        disabledFormInputs('compensate_form', false);

        for (remark_link_ins in remark_link_pool) {
            if (remark_link_ins.indexOf("set_remark_") === 0) {
                var remark_link_obj = DOMCall(remark_link_ins);
                if (remark_link_obj != null)
                    remark_link_obj.innerHTML = remark_link_pool[remark_link_ins];
            }
        }
    } else if (act_msg == "Show Failed Lock Msg") {
        DOMCall('order_comment_box').className = 'hide';
        DOMCall('order_comment_selection_box').className = 'hide';
        if (typeof (deliveryBoxObj) != 'undefined' && deliveryBoxObj != null) {
            deliveryBoxObj.className = 'hide';
        }
        if (typeof (purchaseETABoxObj) != 'undefined' && purchaseETABoxObj != null) {
            purchaseETABoxObj.className = 'hide';
        }

        for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
            var custom_info_box = DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]);
            if (typeof (custom_info_box) != 'undefined' && custom_info_box != null) {
                DOMCall('custom_info_box_' + unique_product_ref_array[p_cnt]).className = 'hide';
            }
        }

        for (var p_cnt = 0; p_cnt < unique_product_ref_array.length; p_cnt++) {
            var supplier_custom_info_box = DOMCall('supplier_custom_info_box' + unique_product_ref_array[p_cnt]);
            if (typeof (supplier_custom_info_box) != 'undefined' && supplier_custom_info_box != null) {
                DOMCall('supplier_custom_info_box' + unique_product_ref_array[p_cnt]).className = 'hide';
            }
        }

        for (loc in location_control_array) {
            if (DOMCall(loc) != null) {
                DOMCall(loc).innerHTML = '-Hidden-';
            }
        }

        for (delivery in partial_delivery_control_array) {
            if (delivery.indexOf("deliver_") === 0) {
                var ref_id = replace(delivery, 'deliver_', '');
                var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
                var delivery_obj = DOMCall(delivery);
                if (delivery_obj != null) {
                    qty_sign_obj.disabled = true;
                    delivery_obj.disabled = true;
                    delivery_obj.value = '-Hidden-';
                    delivery_obj.size = 4;
                }
            }
        }

        disabledFormInputs('partial_delivery', true);
        disabledFormInputs('compensation_delivery_form', true);
        disabledFormInputs('compensate_form', true);

        for (remark_link_ins in remark_link_pool) {
            if (remark_link_ins.indexOf("set_remark_") === 0) {
                var remark_link_obj = DOMCall(remark_link_ins);
                if (remark_link_obj != null)
                    remark_link_obj.innerHTML = '-Hidden-';
            }
        }

        objRef.innerHTML = l_msg;
    }

    showMainInfo(R);
}

function initLocationView(mode) {
    for (loc in location_control_array) {
        if (DOMCall(loc) != null) {
            DOMCall(loc).innerHTML = mode == 1 ? URLDecode(location_control_array[loc]) : '-Hidden-';
        }
    }
}

function initDeliveryInputView(mode) {
    for (delivery in partial_delivery_control_array) {
        if (delivery.indexOf("deliver_") === 0) {
            var ref_id = replace(delivery, 'deliver_', '');
            var qty_sign_obj = DOMCall('deliver_sign_' + ref_id);
            var deliver_obj = DOMCall(delivery);

            if (deliver_obj != null) {
                if (mode == 1) {
                    qty_sign_obj.disabled = false;
                    deliver_obj.disabled = false;
                    deliver_obj.value = '0';
                    deliver_obj.size = 4;
                } else {
                    qty_sign_obj.disabled = true;
                    deliver_obj.disabled = true;
                    deliver_obj.value = '-Hidden-';
                    deliver_obj.size = 4;
                }
            }
        }
    }
}

function initSetRemarkView(mode) {
    for (var i = 0; i < set_as_remark_array.length; i++) {
        if (set_as_remark_array[i].indexOf("set_remark_") === 0) {
            var set_remark_obj = DOMCall(set_as_remark_array[i]);
            if (set_remark_obj != null) {
                remark_link_pool[set_as_remark_array[i]] = set_remark_obj.innerHTML;
                if (mode == 1) {
                    ;//set_remark_obj.innerHTML = '';
                } else {
                    set_remark_obj.innerHTML = '-Hidden-';
                }
            }
        }
    }
}

function setOrderComment(comment_id) {
    var msg = 'Loading... Please be patience!';
    showMainInfo(msg);

    var ref_url = "orders_xmlhttp.php?action=get_comments&cmID=" + comment_id;
    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function () {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';
            jQuery("#comments").val(html_entity_decode(R));
            hideMainInfo();
        }
    }

    xmlhttp.send(null);

    setTimeout('hideMainInfo()', 3000);
}

function setAsOrderRemark(shid, o, _lang, element_id) {
    var obj = DOMCall(element_id);

    if (obj == null)
        return;

    obj.className = 'hide';

    var msg = 'Loading... Please be patience!';
    showMainInfo(msg);

    var ref_url = "orders_xmlhttp.php?action=set_order_remark&shid=" + shid + "&oid=" + o + "&lang=" + _lang;
    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function () {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            var R_C = (typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
            var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';

            if (R_C == 0) {
                alert(R);
            } else if (R_C == 1) {
                obj.innerHTML = R;
                initSetRemarkView(1);	// the inner html of order comment's set as remark link has been changed.
            }

            obj.className = 'show';

            hideMainInfo();
        }
    }

    xmlhttp.send(null);

    setTimeout('hideMainInfo()', 3000);
}

function lockingHistory(o, mode, element_id) {
    if (pageLoaded == null || !pageLoaded)
        return;
    if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4)
        return;

    var obj = DOMCall(element_id);
    if (obj == null)
        return;

    obj.className = 'hide';

    if (mode == 1) {
        var msg = 'Loading... Please be patience!';
        showMainInfo(msg);

        var ref_url = "orders_xmlhttp.php?action=get_order_locking_history&oid=" + o;
        xmlhttp.open("GET", ref_url, asynMode);
        xmlhttp.onreadystatechange = function () {
            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                var R_C = (typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
                var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';

                if (R_C == 0) {
                    alert(R);
                } else if (R_C == 1) {
                    obj.innerHTML = '	<div>' + "\n" +
                            '		<a href="javascript:;" onClick="lockingHistory(\'' + o + '\', \'0\', \'' + element_id + '\');">Hide Locking History</a>' + "\n" +
                            '	</div>' + "\n" +
                            '	<div>' + R + '</div>';
                }

                obj.className = 'show';

                hideMainInfo();
            }
        }

        xmlhttp.send(null);
    } else {
        obj.innerHTML = '	<div>' + "\n" +
                '		<a href="javascript:;" onClick="lockingHistory(\'' + o + '\', \'1\', \'' + element_id + '\');">Show Locking History</a>' + "\n" +
                '	</div>';
        obj.className = 'show';
    }

    setTimeout('hideMainInfo()', 3000);
}

function paymentInfo(sec, mode, instruction, _lang) {
    var order_str_obj = DOMCall(sec + '_order_str');
    var order_payment_link = DOMCall(sec + '_payment_nav');

    if (mode == 1) {
        if (order_str_obj.value != '') {
            order_payment_link.innerHTML = '';

            if (instruction == true) {
                var msg = 'Loading... Please be patience!';
                showMainInfo(msg);
            }

            var fetch_fresh_info = true;
            var order_ids_array = order_str_obj.value.split(',');
            for (i = 0; i < order_ids_array.length; i++) {
                var temp_payment_div = DOMCall(sec + '_' + order_ids_array[i] + '_payment');
                if (trim_str(temp_payment_div.innerHTML) != '')
                    fetch_fresh_info = false;
                break;
            }

            if (fetch_fresh_info == true) {
                var ref_url = "orders_xmlhttp.php?action=get_payment_info&o_str=" + order_str_obj.value + "&lang=" + _lang;
                xmlhttp.open("GET", ref_url);

                xmlhttp.onreadystatechange = function () {
                    if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                        order_payment_link.innerHTML = '<a href="javascript:;" onClick="paymentInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\');">Hide Payment Info</a>';

                        var payment_info = xmlhttp.responseXML.getElementsByTagName("payment_info")[0];

                        if (typeof (payment_info) != 'undefined' && payment_info != null) {
                            var payment_details = '';
                            for (var i = 0; i < payment_info.childNodes.length; i++) {
                                payment_details = payment_info.getElementsByTagName("payment_detail")[i];
                                var payment_div = DOMCall(sec + '_' + payment_details.getAttribute("order_id") + '_payment');
                                payment_div.innerHTML = payment_details.firstChild.nodeValue;
                                var payment_tbody = DOMCall(sec + '_' + payment_details.getAttribute("order_id") + '_payment_sec');
                                payment_tbody.className = 'show';
                            }
                        }

                        if (instruction == true)
                            hideMainInfo();
                    }
                }
            } else {
                order_payment_link.innerHTML = '<a href="javascript:;" onClick="paymentInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\');">Hide Payment Info</a>';

                for (i = 0; i < order_ids_array.length; i++) {
                    var payment_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_payment_sec');
                    payment_tbody.className = 'show';
                }

                if (instruction == true)
                    hideMainInfo();
            }
        }
        xmlhttp.send(null);
    } else {
        if (order_str_obj.value != '') {
            order_payment_link.innerHTML = '<a href="javascript:;" onClick="paymentInfo(\'' + sec + '\', \'1\', true, \'' + _lang + '\');">Show Payment Info</a>';

            var order_ids_array = order_str_obj.value.split(',');
            for (i = 0; i < order_ids_array.length; i++) {
                var payment_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_payment_sec');
                payment_tbody.className = 'hide';
            }
        }
    }
}

function orderInfo(sec, mode, instruction, _lang, SID) {
    var order_str_obj = DOMCall(sec + '_order_str');
    var order_detail_link = DOMCall(sec + '_nav');

    if (mode == 1) {
        if (order_str_obj.value != '') {
            order_detail_link.innerHTML = '';

            if (instruction == true) {
                var msg = 'Loading... Please be patience!';
                showMainInfo(msg);
            }

            var fetch_fresh_info = true;
            var order_ids_array = order_str_obj.value.split(',');
            for (i = 0; i < order_ids_array.length; i++) {
                var temp_order_div = DOMCall(sec + '_' + order_ids_array[i] + '_order');
                if (trim_str(temp_order_div.innerHTML) != '')
                    fetch_fresh_info = false;
                break;
            }

            if (fetch_fresh_info == true) {
                var ref_url = "orders_xmlhttp.php?action=get_order_info&o_str=" + order_str_obj.value + "&lang=" + _lang + "&SID=" + SID;
                xmlhttp.open("GET", ref_url);

                xmlhttp.onreadystatechange = function () {
                    if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                        order_detail_link.innerHTML = '<a href="javascript:;" onClick="orderInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\', \'' + SID + '\');">Hide Order Details</a>';

                        var order_info = xmlhttp.responseXML.getElementsByTagName("order_info")[0];

                        if (typeof (order_info) != 'undefined' && order_info != null) {
                            var order_details = '';
                            for (var i = 0; i < order_info.childNodes.length; i++) {
                                order_details = order_info.getElementsByTagName("order_detail")[i];
                                var order_div = DOMCall(sec + '_' + order_details.getAttribute("order_id") + '_order');
                                order_div.innerHTML = order_details.firstChild.nodeValue;
                                var order_tbody = DOMCall(sec + '_' + order_details.getAttribute("order_id") + '_order_sec');
                                order_tbody.className = 'show';
                            }
                        }

                        if (instruction == true)
                            hideMainInfo();
                    }
                }

                xmlhttp.send(null);
            } else {
                order_detail_link.innerHTML = '<a href="javascript:;" onClick="orderInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\', \'' + SID + '\');">Hide Order Details</a>';

                for (i = 0; i < order_ids_array.length; i++) {
                    var order_detail_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_order_sec');
                    order_detail_tbody.className = 'show';
                }

                if (instruction == true)
                    hideMainInfo();
            }
        }
    } else {
        if (order_str_obj.value != '') {
            order_detail_link.innerHTML = '<a href="javascript:;" onClick="orderInfo(\'' + sec + '\', \'1\', true, \'' + _lang + '\', \'' + SID + '\');">Show Order Details</a>';

            var order_ids_array = order_str_obj.value.split(',');
            for (i = 0; i < order_ids_array.length; i++) {
                var order_detail_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_order_sec');
                order_detail_tbody.className = 'hide';
            }
        }
    }
}

function supplierOrderInfo(sec, mode, instruction, _lang, SID, admin) {
    var order_str_obj = DOMCall(sec + '_order_str');
    var order_detail_link = DOMCall(sec + '_sol_nav');

    if (mode == 1) {
        if (order_str_obj.value != '') {
            order_detail_link.innerHTML = '';

            if (instruction == true) {
                var msg = 'Loading... Please be patience!';
                showMainInfo(msg);
            }

            var fetch_fresh_info = true;
            if (fetch_fresh_info == true) {
                var ref_url = "orders_xmlhttp.php?action=get_supplier_order_info&adm=" + admin + "&o_str=" + order_str_obj.value + "&lang=" + _lang + "&SID=" + SID;
                xmlhttp.open("GET", ref_url);
                xmlhttp.onreadystatechange = function () {
                    if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                        order_detail_link.innerHTML = '<a href="javascript:;" onClick="supplierOrderInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\', \'' + SID + '\', \'' + admin + '\');">Hide Order Details</a>';

                        var order_info = xmlhttp.responseXML.getElementsByTagName("order_info")[0];
                        if (typeof (order_info) != 'undefined' && order_info != null) {
                            var order_details = '';
                            for (var i = 0; i < order_info.childNodes.length; i++) {
                                order_details = order_info.getElementsByTagName("order_detail")[i];
                                var order_div = DOMCall(sec + '_' + order_details.getAttribute("order_id") + '_order');
                                order_div.innerHTML = order_details.firstChild.nodeValue;
                                var order_tbody = DOMCall(sec + '_' + order_details.getAttribute("order_id") + '_order_sec');
                                order_tbody.className = 'show';
                            }
                        }

                        if (instruction == true)
                            hideMainInfo();
                    }
                }
                xmlhttp.send(null);
            } else {
                order_detail_link.innerHTML = '<a href="javascript:;" onClick="supplierOrderInfo(\'' + sec + '\', \'0\', true, \'' + _lang + '\', \'' + SID + '\', \'' + admin + '\');">Hide Order Details</a>';

                for (i = 0; i < order_ids_array.length; i++) {
                    var order_detail_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_order_sec');
                    order_detail_tbody.className = 'show';
                }

                if (instruction == true)
                    hideMainInfo();
            }
        }
    } else {
        if (order_str_obj.value != '') {
            order_detail_link.innerHTML = '<a href="javascript:;" onClick="supplierOrderInfo(\'' + sec + '\', \'1\', true, \'' + _lang + '\', \'' + SID + '\', \'' + admin + '\');">Show Order Details</a>';

            var order_ids_array = order_str_obj.value.split(',');
            for (i = 0; i < order_ids_array.length; i++) {
                var order_detail_tbody = DOMCall(sec + '_' + order_ids_array[i] + '_order_sec');
                order_detail_tbody.className = 'hide';
            }
        }
    }
}

function supplierCPPaymentProductInfo(sec, pid, tid, _lang, SID, admin) {
    var payment_tbody_obj = DOMCall(sec + '_order_sec');
    var payment_div_obj = DOMCall(sec + '_order');
    var payment_info_div_obj = DOMCall(sec + '_p_info');

    if (payment_div_obj != null) {
        var fetch_fresh_info = true;
        if (trim_str(payment_div_obj.innerHTML) != '')
            fetch_fresh_info = false;

        if (fetch_fresh_info == true) {
            var msg = 'Loading... Please be patience!';
            showMainInfo(msg);
        }

        if (fetch_fresh_info == true) {
            var ref_url = "orders_xmlhttp.php?action=get_supplier_cp_payment_products&adm=" + admin + "&pay_id=" + pid + "&tid=" + tid + "&lang=" + _lang + "&SID=" + SID;
            xmlhttp.open("GET", ref_url);
            xmlhttp.onreadystatechange = function () {
                if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                    //res = xmlhttp.responseText;

                    var order_records = xmlhttp.responseXML.getElementsByTagName("payment_records")[0];
                    if (typeof (order_records) != 'undefined' && order_records != null) {
                        order_details = order_records.getElementsByTagName("order_detail")[0];
                        payment_div_obj.innerHTML = order_details.firstChild.nodeValue;

                        payment_details = order_records.getElementsByTagName("reverse_detail")[0];
                        if (typeof (payment_details) != 'undefined' && payment_details != null) {
                            payment_info_div_obj.innerHTML = payment_details.firstChild.nodeValue;
                        }

                        payment_tbody_obj.className = 'show';
                    }

                    hideMainInfo();
                }
            }
            xmlhttp.send(null);
        } else {
            if (payment_tbody_obj.className == 'show') {
                payment_tbody_obj.className = 'hide';
            } else {
                payment_tbody_obj.className = 'show';
            }
        }
    }
}

function supplierPaymentOrderInfo(sec, pid, tid, _lang, SID, admin) {
    var payment_tbody_obj = DOMCall(sec + '_order_sec');
    var payment_div_obj = DOMCall(sec + '_order');
    var payment_info_div_obj = DOMCall(sec + '_p_info');

    if (payment_div_obj != null) {
        var fetch_fresh_info = true;
        if (trim_str(payment_div_obj.innerHTML) != '')
            fetch_fresh_info = false;

        if (fetch_fresh_info == true) {
            var msg = 'Loading... Please be patience!';
            showMainInfo(msg);
        }

        if (fetch_fresh_info == true) {
            var ref_url = "orders_xmlhttp.php?action=get_supplier_payment_orders&adm=" + admin + "&pay_id=" + pid + "&tid=" + tid + "&lang=" + _lang + "&SID=" + SID;
            xmlhttp.open("GET", ref_url);
            xmlhttp.onreadystatechange = function () {
                if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                    var order_records = xmlhttp.responseXML.getElementsByTagName("payment_records")[0];
                    if (typeof (order_records) != 'undefined' && order_records != null) {
                        order_details = order_records.getElementsByTagName("order_detail")[0];
                        payment_div_obj.innerHTML = order_details.firstChild.nodeValue;

                        payment_details = order_records.getElementsByTagName("reverse_detail")[0];
                        if (typeof (payment_details) != 'undefined' && payment_details != null) {
                            payment_info_div_obj.innerHTML = payment_details.firstChild.nodeValue;
                        }

                        payment_tbody_obj.className = 'show';
                    }

                    hideMainInfo();
                }
            }
            xmlhttp.send(null);
        } else {
            if (payment_tbody_obj.className == 'show') {
                payment_tbody_obj.className = 'hide';
            } else {
                payment_tbody_obj.className = 'show';
            }
        }
    }
}

function orderListsOptions(selObj, sid, _lang, o, wl) {
    var msg = '';
    var rExp = /_tag_selector/gi;
    var status_name = selObj.name.replace(rExp, '');
    var option_action = selObj.value;
    if (o != null && o != '') {
        var selected_order = new Array(o);
    } else {
        var selected_order = new Array();
        var order_str_obj = DOMCall(status_name + '_order_str');
        if (order_str_obj != null) {
            var order_ids_array = order_str_obj.value.split(',');
            for (i = 0; i < order_ids_array.length; i++) {
                var order_row = DOMCall(status_name + '_main_' + i);
                if (order_row != null && order_row.className == "rowSelected") {
                    selected_order.push(order_ids_array[i]);
                }
            }
        }
    }
    var selected_order_str = selected_order.join(',');
    var act = '';
    var s_val = '';
    switch (option_action) {
        case 'nt':	// create new tag
            if (selected_order.length > 0) {
                var new_tag_name = prompt("Please enter a new tag name", "");
                // check to see if anything was entered
                // null == pressing cancel
                // ""   == entered nothing and pressing ok
                while (new_tag_name != null && trim_str(new_tag_name) == '') {
                    new_tag_name = prompt("Please enter a new tag name", "");
                }
                if (new_tag_name == null) {
                    selObj.selectedIndex = 0;
                    return;
                }
                act = 'perform_tagging';
                s_val = trim_str(new_tag_name);
            } else {
                msg = 'No orders selected.';
                showMainInfo(msg);
                selObj.selectedIndex = 0;
            }
            break;
        case 'rd':
        case 'ur':
            if (selected_order.length > 0) {
                act = 'perform_tagging';
                s_val = option_action == 'rd' ? '1' : '0';
                var msg = 'This order is set to ' + (option_action == 'rd' ? 'read' : 'unread') + '!';
                showMainInfo(msg);
            } else {
                msg = 'No orders selected.';
                showMainInfo(msg);
                selObj.selectedIndex = 0;
            }
            break;
        default:
            if (option_action.indexOf('otag_') != -1) {
                if (selected_order.length > 0) {
                    var temp_array = option_action.split('_');
                    option_action = 'at';
                    s_val = temp_array[1];
                    act = 'perform_tagging';
                } else {
                    msg = 'No orders selected.';
                    showMainInfo(msg);
                    selObj.selectedIndex = 0;
                }
            } else if (option_action.indexOf('rmtag_') != -1) {
                if (selected_order.length > 0) {
                    var temp_array = option_action.split('_');
                    option_action = 'rt';
                    s_val = temp_array[1];
                    act = 'perform_tagging';
                } else {
                    msg = 'No orders selected.';
                    showMainInfo(msg);
                    selObj.selectedIndex = 0;
                }
            }
            break;
    }

    if (trim_str(act) != '') {
        selObj.disabled = true;
        clearOptionList(selObj);
        selObj.options[0] = new Option('Loading ...', '');

        var ref_url = "orders_xmlhttp.php?action=" + act + "&subaction=" + option_action + "&status_id=" + sid + "&setting=" + s_val + "&o_str=" + selected_order_str + "&lang=" + _lang + "&list_mode=" + (wl == true ? '2' : '1');
        xmlhttp.open("GET", ref_url);
        xmlhttp.onreadystatechange = function () {
            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                clearOptionList(selObj);

                var tag_info = xmlhttp.responseXML.getElementsByTagName("tag_info")[0];
                if (typeof (tag_info) != 'undefined' && tag_info != null) {
                    var selection = tag_info.getElementsByTagName("selection")[0];
                    if (typeof (selection) != 'undefined' && selection != null) {
                        var option_sel = '';
                        for (var i = 0; i < selection.childNodes.length; i++) {
                            option_sel = selection.getElementsByTagName("option")[i];
                            appendToSelect(selObj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
                        }
                    }

                    var tag_details = tag_info.getElementsByTagName("tag_details")[0];
                    if (typeof (tag_details) != 'undefined' && tag_details != null) {
                        var order_tags = '';
                        for (var i = 0; i < tag_details.childNodes.length; i++) {
                            order_tags = tag_details.getElementsByTagName("order_tags")[i];
                            var cus_tag_str = DOMCall('tag_' + order_tags.getAttribute("order_id"));
                            if (cus_tag_str != null) {
                                cus_tag_str.innerHTML = order_tags.firstChild.nodeValue;
                            }
                        }
                    }

                    var read_mode = tag_info.getElementsByTagName("read_mode")[0];
                    if (typeof (read_mode) != 'undefined' && read_mode != null) {
                        var order_mode = '';
                        for (var i = 0; i < read_mode.childNodes.length; i++) {
                            order_mode = read_mode.getElementsByTagName("order_mode")[i];
                            var cus_order_row_obj = DOMCall('read_' + order_mode.getAttribute("order_id"));
                            if (cus_order_row_obj != null) {
                                cus_order_row_obj.className = option_action == 'ur' ? 'boldText' : '';
                            }
                        }
                    }
                }
                selObj.disabled = false;
            }
        }
        xmlhttp.send(null);
    }

    setTimeout('hideMainInfo()', 3000);
}

function refreshOrderListsOptions(status_name, _lang, o) {
    var tag_selector_obj = DOMCall(status_name + '_tag_selector');
    if (o != null) {
        var selected_order = new Array(o);
    } else {
        var order_str_obj = DOMCall(status_name + '_order_str');
        var selected_order = new Array();
        if (order_str_obj != null) {
            var order_ids_array = order_str_obj.value.split(',');
            for (i = 0; i < order_ids_array.length; i++) {
                var order_row = DOMCall(status_name + '_main_' + i);
                if (order_row != null && order_row.className == "rowSelected") {
                    selected_order.push(order_ids_array[i]);
                }
            }
        }
    }
    var selected_order_str = selected_order.join(',');

    if (tag_selector_obj != null) {
        tag_selector_obj.disabled = true;
        clearOptionList(tag_selector_obj);
        tag_selector_obj.options[0] = new Option('Loading ...', '');

        var ref_url = "orders_xmlhttp.php?action=refresh_tag_selection&status=" + status_name + "&o_str=" + selected_order_str + "&lang=" + _lang;
        xmlhttp.open("GET", ref_url);

        xmlhttp.onreadystatechange = function () {
            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                clearOptionList(tag_selector_obj);

                var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];

                if (typeof (selection) != 'undefined' && selection != null) {
                    var option_sel = '';
                    for (var i = 0; i < selection.childNodes.length; i++) {
                        option_sel = selection.getElementsByTagName("option")[i];
                        appendToSelect(tag_selector_obj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
                    }
                }
                tag_selector_obj.disabled = false;
            }
        }
        xmlhttp.send(null);
    }
}

function fetchStatusTagSelections(status_id, div_objRef, sel, _lang) {
    div_objRef = DOMCall(div_objRef);
    objRef = DOMCall(sel);
    if (objRef != null) {
        if (status_id > 0) {
            clearOptionList(objRef);
            objRef.options[0] = new Option('Loading ...', '');

            var ref_url = "orders_xmlhttp.php?action=retrieve_status_tags&status=" + status_id + "&lang=" + _lang;
            xmlhttp.open("GET", ref_url);

            xmlhttp.onreadystatechange = function () {
                if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                    clearOptionList(objRef);

                    var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];

                    if (typeof (selection) != 'undefined' && selection != null) {
                        var option_sel = '';
                        for (var i = 0; i < selection.childNodes.length; i++) {
                            option_sel = selection.getElementsByTagName("option")[i];
                            appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
                        }
                    }
                    objRef.disabled = false;
                    div_objRef.className = 'show';
                }
            }
            xmlhttp.send(null);
        } else {
            clearOptionList(objRef);
            div_objRef.className = "hide";
        }
    }
}

function requestTransId(shField, dBox, edit) {
    var old_value = DOMCall(shField).value;
    var new_trans_id = prompt("Please enter the \"Transaction ID\" provided by WorldPay for this order:", "");
    // check to see if anything was entered
    // null == pressing cancel
    // ""   == entered nothing and pressing ok
    while (new_trans_id != null && (trim_str(new_trans_id) == '' || !validateInteger(trim_str(new_trans_id)) || new_trans_id.length > 12)) {
        new_trans_id = prompt("Invalid Transaction ID!\nPlease enter the \"Transaction ID\" provided by WorldPay for this order:", "");
    }
    if (new_trans_id == null) {		// user get tired and give up :)
        if (edit == null) {
            DOMCall(shField).value = '';
            DOMCall(dBox).innerHTML = '';
        } else {
            DOMCall(shField).value = old_value;
            DOMCall(dBox).innerHTML = "Transaction ID: " + old_value + '&nbsp;<a href="javascript:;" onClick="if (!requestTransId(\'' + shField + '\', \'' + dBox + '\', true)) { DOMCall(\'' + shField + '\').value = ' + old_value + '; };"><u>Edit</u></a>';
        }
        return false;
    } else {
        DOMCall(shField).value = trim_str(new_trans_id);
        var old_value = trim_str(new_trans_id);
        DOMCall(dBox).innerHTML = "Transaction ID: " + trim_str(new_trans_id) + '&nbsp;<a href="javascript:;" onClick="if (!requestTransId(\'' + shField + '\', \'' + dBox + '\', true)) { DOMCall(\'' + shField + '\').value = ' + old_value + '; };"><u>Edit</u></a>';
    }

    return true;
}

function requestRefundAmt(shField, dBox, max_amt, edit) {
    var old_value = currency_display(DOMCall(shField).value, 2);
    var new_refund_amt = prompt("Please enter the Refund amount:", "");
    // check to see if anything was entered
    // null == pressing cancel
    // ""   == entered nothing and pressing ok
    while (new_refund_amt != null && (trim_str(new_refund_amt) == '' || !currencyValidation(trim_str(new_refund_amt)) || parseFloat(new_refund_amt) >= parseFloat(max_amt))) {
        new_refund_amt = prompt("Invalid Refund amount!\nPlease enter the Refund amount:", "");
    }
    if (new_refund_amt == null) {		// user get tired and give up :)
        if (edit == null) {
            DOMCall(shField).value = '';
            DOMCall(dBox).innerHTML = '';
        } else {
            DOMCall(shField).value = old_value;
            DOMCall(dBox).innerHTML = "Refund Amount: " + old_value + '&nbsp;<a href="javascript:;" onClick="if (!requestRefundAmt(\'' + shField + '\', \'' + dBox + '\', \'' + max_amt + '\', true)) { DOMCall(\'' + shField + '\').value = ' + old_value + '; };"><u>Edit</u></a>';
        }
        return false;
    } else {
        DOMCall(shField).value = currency_display(trim_str(new_refund_amt), 2);
        var old_value = currency_display(trim_str(new_refund_amt), 2);
        DOMCall(dBox).innerHTML = "Refund Amount: " + old_value + '&nbsp;<a href="javascript:;" onClick="if (!requestRefundAmt(\'' + shField + '\', \'' + dBox + '\', \'' + max_amt + '\', true)) { DOMCall(\'' + shField + '\').value = ' + old_value + '; };"><u>Edit</u></a>';
    }

    return true;
}

function editTransId(o, span_objRef, adm_email) {
    objRef = DOMCall(span_objRef);

    var new_trans_id = prompt("Please enter the new \"Transaction ID\" for this order:", "");
    // check to see if anything was entered
    // null == pressing cancel
    // ""   == entered nothing and pressing ok
    while (new_trans_id != null && (trim_str(new_trans_id) == '' || !validateInteger(trim_str(new_trans_id)) || new_trans_id.length > 12)) {
        new_trans_id = prompt("Invalid Transaction ID!\nPlease enter the new \"Transaction ID\" for this order:", "");
    }
    if (new_trans_id == null) {
        return false;
    } else {
        var msg = 'Updating... Please be patience!';
        showMainInfo(msg);

        var ref_url = "orders_xmlhttp.php?action=update_transID&transID=" + new_trans_id + "&oid=" + o + "&adm_email=" + adm_email;
        xmlhttp.open("GET", ref_url);

        xmlhttp.onreadystatechange = function () {
            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                //res = xmlhttp.responseText;

                var R_C = (typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
                var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';

                if (R_C == 0) {
                    alert(R);
                } else if (R_C == 1 && objRef != null) {
                    objRef.innerHTML = R;
                    initSetRemarkView(1);
                }

                hideMainInfo();
            }
        }
        xmlhttp.send(null);
    }
}

function updateFollowUpDate(o, date_objRef, btn_obj) {
    objRef = DOMCall(date_objRef);

    var msg = 'Updating... Please be patience!';
    showMainInfo(msg);

    btn_obj.disabled = true;

    var ref_url = "orders_xmlhttp.php?action=update_follow_up&follow_date=" + objRef.value + "&oid=" + o;
    xmlhttp.open("GET", ref_url);

    xmlhttp.onreadystatechange = function () {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            //res = xmlhttp.responseText;

            var R_C = (typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
            var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';

            if (R_C == 0) {
                alert(R);
            } else if (R_C == 1) {
                alert(R);
                initSetRemarkView(1);
            }

            btn_obj.disabled = false;

            hideMainInfo();
        }
    }
    xmlhttp.send(null);
}



function updateAftWhiteList(cid, date_objRef, btn_obj) {
    objRef = DOMCall(date_objRef);

    var msg = 'Updating... Please be patience!';
    showMainInfo(msg);

    btn_obj.disabled = true;

    var ref_url = "orders_xmlhttp.php?action=update_aft_whitelisted_date&aft_whitelisted_date=" + objRef.value + "&cid=" + cid;
    xmlhttp.open("GET", ref_url);

    xmlhttp.onreadystatechange = function () {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            var R_C = (typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
            var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';

            if (R_C == 0) {
                alert(R);
            } else if (R_C == 1) {
                alert(R);
                jQuery('#td_aft_whitelisted').html(xmlhttp.responseXML.getElementsByTagName("res_code_value")[0].firstChild.data);
            }

            btn_obj.disabled = false;

            hideMainInfo();
        }
    }
    xmlhttp.send(null);
}



// Order Compensation
function retrieveLevelers(ref_p, ref_s, ref_sv) {
    var p_obj = DOMCall(ref_p);
    var s_obj = DOMCall(ref_s);
    var sv_obj = DOMCall(ref_sv);
    var notify_obj = DOMCall('compensate_comments_notify');

    if (p_obj.value == '*' || p_obj.value == '') {	// Non-PWL Product
        if (s_obj != null) {
            s_obj.selectedIndex = 0;
            s_obj.disabled = true;
        }

        if (sv_obj != null) {
            sv_obj.value = '';
            sv_obj.disabled = true;
        }

        if (notify_obj != null)
            notify_obj.disabled = true;

        p_obj.disabled = false;
    } else {
        var server_action = 'retrieve_levelers';
        var msg = 'Loading... Please be patience!';

        showMainInfo(msg);

        p_obj.disabled = true;

        var ref_url = "orders_xmlhttp.php?action=" + server_action + "&op_id=" + p_obj.value;
        xmlhttp.open("GET", ref_url);
        xmlhttp.onreadystatechange = function () {
            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                //res = xmlhttp.responseText;

                var res_code = typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';

                if (res_code == 1) {
                    var leveler_info = xmlhttp.responseXML.getElementsByTagName("leveler_info")[0];
                    if (typeof (leveler_info) != 'undefined' && leveler_info != null) {
                        var selection = leveler_info.getElementsByTagName("selection")[0];
                        if (typeof (selection) != 'undefined' && selection != null) {
                            clearOptionList(s_obj);
                            var option_sel = '';

                            appendToSelect(s_obj, '', 'Please Select');

                            for (var i = 0; i < selection.childNodes.length; i++) {
                                option_sel = selection.getElementsByTagName("option")[i];
                                appendToSelect(s_obj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
                            }

                            if (s_obj != null)
                                s_obj.disabled = false;
                            if (sv_obj != null)
                                sv_obj.disabled = false;
                            if (notify_obj != null)
                                notify_obj.disabled = false;
                        }
                    }

                    p_obj.disabled = false;
                } else {
                    if (s_obj != null) {
                        s_obj.selectedIndex = 0;
                        s_obj.disabled = true;
                    }

                    if (sv_obj != null) {
                        sv_obj.value = '';
                        sv_obj.disabled = true;
                    }

                    if (notify_obj != null)
                        notify_obj.disabled = true;

                    p_obj.disabled = false;
                }

                hideMainInfo();
            }
        }

        xmlhttp.send(null);
    }
}

function retrieveProductPrice(p_val, ref_price, ref_cur) {
    var price_obj = DOMCall(ref_price);
    var cur_obj = DOMCall(ref_cur);

    var server_action = 'retrieve_price';
    var msg = 'Loading... Please be patience!';
    showMainInfo(msg);

    var ref_url = "orders_xmlhttp.php?action=" + server_action + "&p_id=" + p_val + "&p_cur=" + cur_obj.value;
    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function () {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            //res = xmlhttp.responseText;

            price_info_obj = xmlhttp.responseXML.getElementsByTagName("result")[0];
            if (price_info_obj != "undefined" && price_info_obj != null) {
                var price = price_info_obj.firstChild.data;
                cur_obj.value = price_info_obj.getAttribute("currency");
            }

            if (price != 'N/A') {
                price_obj.value = price;
                update_compensate_amount();
            }

            hideMainInfo();
        }
    }

    xmlhttp.send(null);
}

function fillPWLInfo(p_val, file_path) {
    var server_action = 'get_product_type';

    var msg = 'Loading... Please be patience!';
    showMainInfo(msg);

    var ref_url = "orders_xmlhttp.php?action=" + server_action + "&p_id=" + p_val;
    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function () {
        if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            //res = xmlhttp.responseText;

            var p_type = typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("result")[0] != null ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';

            if (p_type == 1) {
                var goto_url = file_path + (file_path.indexOf('?') == -1 ? '?' : '&') + 'products_id=' + p_val;
                openDGDialog(goto_url, 800, 700, '');
            }

            hideMainInfo();
        }
    }

    xmlhttp.send(null);
}

function hideShowOrderComment(groupName, commentRowPrefix, totalComment, styleClass) {
    /*
     for (var i=0; i < totalComment; i++) {
     var commentRow = DOMCall(commentRowPrefix + i);
     if (commentRow != null)
     commentRow.className = styleClass;
     }
     
     if (styleClass == "show") {
     DOMCall(groupName).innerHTML = "<a href=\"javascript:;\" onClick=\"hideShowOrderComment('"+groupName+"', '"+commentRowPrefix+"', '"+totalComment+"', 'hide')\">Show Order Remark Only</a>";
     } else {
     DOMCall(groupName).innerHTML = "<a href=\"javascript:;\" onClick=\"hideShowOrderComment('"+groupName+"', '"+commentRowPrefix+"', '"+totalComment+"', 'show')\">Show All Order Comments</a>";
     }
     */
}

function showDiscount(objLink, cust_grp_id) {
    if (objLink.innerHTML.indexOf('Hide') == 0) {
        expand_and_collapse('show_discount', 'hide');
        objLink.innerHTML = "Show Group Discount";
    } else {
        if (document.getElementById('show_discount_long').innerHTML == '') {
            var ref_url = "orders_xmlhttp.php?action=show_discount&cust_grp_id=" + cust_grp_id;
            xmlhttp.open('GET', ref_url);
            xmlhttp.onreadystatechange = function () {
                if (xmlhttp.readyState == 4) {
                    var p_groupdiscount = typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("result")[0] != null ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';
                    document.getElementById('show_discount_long').innerHTML = p_groupdiscount;
                }
            } //end function
            xmlhttp.send(null);
        } //end if
        expand_and_collapse('show_discount', 'show');
        objLink.innerHTML = "Hide Group Discount";
    }
}

function findValueCallback(event, data, formatted) {
    setOrderComment(data.name);
}

function getOrderStatistic(cid, oid, cgid) {
    var server_action = 'get_order_statistic';
    var ref_url = 'orders_xmlhttp.php?action=' + server_action + '&customers_id=' + cid + '&oid=' + oid + '&customers_groups_id=' + cgid;

    jQuery('#order_stat_div').html('<b>Loading Statistic...</b>');

    jQuery.ajax({
        url: ref_url,
        type: 'GET',
        dataType: 'xml',
        timeout: 90000,
        error: function () {
            jQuery.unblockUI();
        },
        success: function (xml) {
            var order_stat = jQuery(xml).find('stat').text();

            if (order_stat != '') {
                order_stat = '<table width="100%" border="0" cellspacing="2" cellpadding="2">' + order_stat + '</table>';
            }

            jQuery('#order_stat_div').html(order_stat);

            getFirstCompletedOrder(cid);
        }
    });
}

function getFirstCompletedOrder(cid) {
    var server_action = 'get_first_completed_order';
    var ref_url = 'orders_xmlhttp.php?action=' + server_action + '&customers_id=' + cid;

    jQuery('#first_order_div').html('<b>Loading...</b>');

    jQuery.ajax({
        url: ref_url,
        type: 'GET',
        dataType: 'xml',
        timeout: 10000,
        error: function () {
            jQuery.unblockUI();
        },
        success: function (xml) {
            var order_stat = jQuery(xml).find('first_order').text();

            jQuery('#first_order_div').html(order_stat);
        }
    });
}

function getOrderTimeZone(oip) {
    $.getJSON('https://ipapi.co/' + oip + '/json/', function (data) {
        // $.getJSON('https://ipapi.co/*************/json/', function(data){	
        if (data.error) {
            alert(data.reason);
        } else if (data.utc_offset) {
            var offset = data.utc_offset;
            offset = offset.slice(0, -2) + "." + offset.slice(-2);
            // create Date object for current location
            var d = new Date();
            // convert to msec
            // subtract local time zone offset
            // get UTC time in msec
            var utc = d.getTime() + (d.getTimezoneOffset() * 60000);
            // create new Date object for different city
            // using supplied offset
            var nd = new Date(utc + (3600000 * offset));
            var options = {
                year: "numeric",
                month: "long",
                day: "numeric",
                hour: "numeric",
                minute: "numeric",
                second: "numeric",
            };
            // return time as a string
            var s = nd.toLocaleString(undefined, options);
            var timezone_text = '<br><b>' + data.timezone + '</b>&nbsp;&nbsp;' + s;
            jQuery('#order_time_zone').html(timezone_text);
        } else {
            alert('Timezone & date time does not available');
        }
    })
}

function checkBalance(obj, prefix, deliver_qty, good_delivered_qty, max_qty) {
    if (obj != null) {
        deliver_qty = parseInt(deliver_qty, 10);
        good_deliver_qty = parseInt(good_delivered_qty, 10);
        max_qty = parseInt(max_qty, 10);
        obj.value = trim_str(obj.value);

        var ref_id = replace(obj.id, prefix, '');
        var qty_sign_obj = DOMCall(prefix + 'sign_' + ref_id);

        if (obj.value != '' && qty_sign_obj != null) {
            if (!validateInteger(obj.value)) {
                obj.value = '';
            } else {
                var entered_qty = parseInt(qty_sign_obj.value + '' + obj.value);

                if (entered_qty >= 0) {
                    if (entered_qty > max_qty) {
                        alert('Entered deliver quantity exceed balance quantity!\nPlease reduce the quantity!');

                        obj.focus();
                        obj.select();

                        return false;
                    }
                } else {
                    if (Math.abs(entered_qty) > deliver_qty) {
                        alert('Entered deduct deliver quantity exceed delivered quantity!\nPlease reduce the deduction quantity!');

                        obj.focus();
                        obj.select();

                        return false;
                    }
                }
            }
        }
    }

    return true;
}

function checkPercentBalance(obj, prefix, deliver_qty, max_qty) {
    if (obj != null) {
        deliver_qty = deliver_qty * 100;
        max_qty = Math.round(max_qty * 100);
        obj.value = trim_str(obj.value);

        var ref_id = replace(obj.id, prefix, '');
        var qty_sign_obj = DOMCall(prefix + 'sign_' + ref_id);

        if (obj.value != '' && qty_sign_obj != null) {
            if (!validateInteger(obj.value)) {
                obj.value = '';
            } else {
                var entered_qty = parseInt(qty_sign_obj.value + '' + obj.value);

                if (entered_qty >= 0) {
                    if (entered_qty > max_qty) {
                        alert('Entered deliver quantity exceed balance quantity!\nPlease reduce the quantity!');
                        obj.focus();
                        obj.select();
                        return false;
                    }
                } else {
                    if (Math.abs(entered_qty) > deliver_qty) {
                        alert('Entered deduct deliver quantity exceed delivered quantity!\nPlease reduce the deduction quantity!');
                        obj.focus();
                        obj.select();
                        return false;
                    }
                }
            }
        }
    }

    return true;
}

function toggle_order_comment(whichLink, whichLayer) {
    var show_mode = '';
    var f = document.forms['order_comment_form'];

    if (typeof (whichLink.innerText) != 'undefined') {
        show_mode = whichLink.innerText.indexOf('Hide') == 0 ? true : false;
    } else {
        show_mode = whichLink.text.indexOf('Hide') == 0 ? true : false;
    }

    if (show_mode) {
        whichLink.innerHTML = whichLink.innerHTML.replace(/Hide/ig, "Show");

        for (var elem, i = 0; i < f.length, elem = f.elements[i]; i++) {
            if (elem.type == 'hidden' && elem.name.indexOf('hidden_' + whichLayer) == 0) {
                document.getElementById(elem.name.replace(/hidden_/ig, '')).className = 'hide';
            }
        }
    } else {
        whichLink.innerHTML = whichLink.innerHTML.replace(/Show/ig, "Hide");

        for (var elem, i = 0; i < f.length, elem = f.elements[i]; i++) {
            if (elem.type == 'hidden' && elem.name.indexOf('hidden_' + whichLayer) == 0) {
                document.getElementById(elem.name.replace(/hidden_/ig, '')).className = 'show';
            }
        }
    }
}

function check_onecard_transaction_status(OrderId) {
    jQuery.ajax({
        type: 'GET',
        url: 'orders_xmlhttp.php?action=onecard&subaction=check_voucher_code_entered&oID=' + OrderId,
        dataType: "xml",
        success: function (xml) {
            if (jQuery(xml).find('success').text() == 1) {
                document.onecard_status_check_form.submit();
            } else {
                jquery_confirm_box(jQuery(xml).find('message').text(), 1, 1, 'Warning');
            }
        }
    });
}

// G2G
function create_buyback(opid, site_id) {
    var site = '';
    if (typeof site_id != 'undefined') {
        site = site_id;
    }

    jquery_confirm_box('<h1>Please wait...</h1>', 0, 0);
    if (opid != '') {
        var create_bo_url = '';
        if (site == 5) {
            create_bo_url = "orders_xmlhttp.php?action=create_c2c_bo&opid=" + opid;
        } else {
            create_bo_url = "orders_xmlhttp.php?action=create_bo&opid=" + opid;
        }

        jQuery.get(create_bo_url, function (xml) {
            jQuery(xml).find('response').each(function () {
                var error = jQuery("error", this).text();
                if (error == '') {
                    var result = jQuery("result", this).text();

                    alert('G2G Sell Order # ' + result + ' has been created.');
                    jQuery('#create_bo_button_' + opid).css('display', 'none');
                    jQuery.unblockUI();
                } else {
                    alert(error);
                    jQuery.unblockUI();
                }
            });
        });
    }
}

function showDeletedCustomersInfo() {
    var full_customer_info = document.getElementById('full-customers-info').style;
    var deleted_customer_info = document.getElementById('full-deleted-customers-info').style;

    full_customer_info.display = 'table-row';
    deleted_customer_info.display = 'none';
}

function orderCancellation() {
    confirm_box_content = "Customer is requesting order cancellation and awaiting Seller response.";
    jquery_confirm_box(confirm_box_content, 1, 0, 'Notice');
}

function getOGStoreCreditOP(cid) {
    var server_action = 'get_og_storecredit_op';
    var ref_url = 'orders_xmlhttp.php?action=' + server_action + '&customers_id=' + cid;

    jQuery('#og_storecredit').html('<b>Loading...</b>');
    jQuery('#og_op').html('<b>Loading...</b>');

    jQuery.ajax({
        url: ref_url,
        type: 'GET',
        dataType: 'xml',
        timeout: 90000,
        error: function () {
            jQuery.unblockUI();
        },
        success: function (xml) {
            var og_storecredit = jQuery(xml).find('og_storecredit').text();
            var og_op = jQuery(xml).find('og_op').text();
            jQuery('#og_storecredit').html(og_storecredit);
            jQuery('#og_op').html(og_op);
        }
    });
}

(function () {
    if (typeof document.getElementsByName('hiddenCancellation')[0] != 'undefined' && document.getElementsByName('hiddenCancellation')[0].value == 1) {
        orderCancellation();
    }
})();