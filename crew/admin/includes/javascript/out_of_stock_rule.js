function show_out_of_stock_rules_by_id(rule_id, show_msg, hide_msg) {
    if (trim_str(jQuery("#customer_group_discount_info_" + rule_id).html()).length == 0) {
        jQuery.ajax({
            type: "GET",
            dataType: 'html',
            url: "out_of_stock_rule_xmlhttp.php?action=get_out_of_stock_rules_by_id&rule_id=" + rule_id,
            error: function () {
                jquery_confirm_box("Error loading", 1, 1, "Warning");
            },
            success: function (rule_reponse) {
                jQuery.unblockUI();
                if (jQuery("#toggle_discount_setting_" + rule_id).text() == show_msg) {
                    jQuery("#toggle_discount_setting_" + rule_id).text(hide_msg);
                    jQuery("#customer_group_discount_info_" + rule_id).html(rule_reponse);
                } else {
                    jQuery("#toggle_discount_setting_" + rule_id).text(show_msg);
                }

            }
        });

    } else {
        if (jQuery("#toggle_discount_setting_" + rule_id).text() == show_msg) {
            jQuery("#customer_group_discount_info_" + rule_id).show();
            jQuery("#toggle_discount_setting_" + rule_id).text(hide_msg);
        } else {
            jQuery("#customer_group_discount_info_" + rule_id).hide();
            jQuery("#toggle_discount_setting_" + rule_id).text(show_msg);

        }

    }




}