function load_payment_methods ( pmID ){
	var display_html = '';

	jQuery('#payment_method_tab_zone').css({display:'block'});
	if (!validateInteger(pmID)) {	// mostly the PG that havent install will given a filename
		display_html += "<table width='100%'>";
		display_html += "	<tr>";
		display_html += "		<td><img src='includes/languages/english/images/buttons/button_module_install.gif' onclick='install_payment_gateway(\""+pmID+".php\")'></td>";
		display_html += "	</tr>";
		display_html += "</table>";

		jQuery('#payment_method_tab_header').text(pmID);
		jQuery('#payment_method_tab_1').html(display_html);

		jQuery('#payment_method_li_2').css({display:'none'});
		jQuery('#payment_method_tab_2').html('');

		jQuery('#payment_method_li_3').css({display:'none'});
		jQuery('#payment_method_tab_3').html('');
		
		jQuery('#payment_method_li_4').css({display:'none'});
		jQuery('#payment_method_tab_4').html('');
		
		jQuery('#payment_method_li_5').css({display:'none'});
		jQuery('#payment_method_tab_5').html('');
		
		jQuery('#payment_method_li_6').css({display:'none'});
		jQuery('#payment_method_tab_6').html('');
		
		jQuery('#payment_method_li_7').css({display:'none'});
		jQuery('#payment_method_tab_7').html('');
        
        jQuery('#payment_method_li_8').css({display:'none'});
		jQuery('#payment_method_tab_8').html('');
	} else {
		jquery_confirm_box('<h1>Please wait...</h1>', 0, 0);
		jQuery.ajax({
		    url: "payment_methods_xmlhttp.php?action=_&pm_id="+pmID,
		    type: 'GET',
		    dataType: 'xml',
		    timeout: 10000,
		    error: function(){
		    	jQuery.unblockUI();	
		        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
		    },
		    success: function(xml){
		    	jQuery("#payment_methods_id").val(pmID);
		    	jQuery("#payment_method_tab_header").html(jQuery(xml).find('display_title').text());

				jQuery('#payment_method_li_1').css({display:'block'});
				jQuery('#payment_method_tab_1').html('');
				
				jQuery('#payment_method_li_2').css({display:'block'});
				jQuery('#payment_method_tab_2').html('');

				jQuery('#payment_method_li_3').css({display:'block'});
				jQuery('#payment_method_tab_3').html('');

				jQuery('#payment_method_li_4').css({display:'block'});
				jQuery('#payment_method_tab_4').html('');

				jQuery('#payment_method_li_5').css({display:'block'});
				jQuery('#payment_method_tab_5').html('');
				
				jQuery('#payment_method_li_6').css({display:'block'});
				jQuery('#payment_method_tab_6').html('');
				
				jQuery('#payment_method_li_7').css({display:'block'});
				jQuery('#payment_method_tab_7').html('');

                jQuery('#payment_method_li_8').css({display:'block'});
				jQuery('#payment_method_tab_8').html('');
                
				load_tab('display_status_tab', pmID);

				jQuery.unblockUI();
			}
		});
	}
}

function load_tab(display_type, pmID){
	display_type = display_type || 'display_all_tab';
	pmID = pmID || 0;
	
	if (pmID == 0) {
		if (display_type == 'display_status_tab' && jQuery('#payment_method_tab_1').html()=="") {
			pmID = jQuery("#payment_methods_id").val();
		} else if (display_type == 'display_settings_tab' && jQuery('#payment_method_tab_2').html()=="") {
			pmID = jQuery("#payment_methods_id").val();
		} else if (display_type == 'display_currencies_tab' && jQuery('#payment_method_tab_3').html()=="") {
			pmID = jQuery("#payment_methods_id").val();
		} else if (display_type == 'display_surcharge_tab' && jQuery('#payment_method_tab_4').html()=="") {
			pmID = jQuery("#payment_methods_id").val();
        } else if (display_type == 'display_sc_surcharge_tab' && jQuery('#payment_method_tab_8').html()=="") {
			pmID = jQuery("#payment_methods_id").val();
		} else if (display_type == 'display_send_payment_tab' && jQuery('#payment_method_tab_5').html()=="") {
			pmID = jQuery("#payment_methods_id").val();
		} else if (display_type == 'display_send_payment_info_tab' && jQuery('#payment_method_tab_6').html()=="") {
			pmID = jQuery("#payment_methods_id").val();
		} else if (display_type == 'display_send_currencies_tab' && jQuery('#payment_method_tab_7').html()=="") {
			pmID = jQuery("#payment_methods_id").val();
		}
	}
	
	if (display_type!="" && pmID > 0) {
		jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
		jQuery.ajax({
		    url: "payment_methods_xmlhttp.php?action="+display_type+"&pm_id="+pmID,
		    type: 'GET',
		    dataType: 'xml',
		    timeout: 10000,
		    error: function(){
		    	jQuery.unblockUI();	
		        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
		    },
		    success: function(xml){
		    	jQuery.unblockUI();	
		    	jQuery('#payment_method_tab_zone').css({display:'block'});
		    	display_status_tab(pmID, xml);
		    	display_settings_tab(pmID, xml);
		    	display_currencies_tab(pmID, xml);
		    	display_surcharge_tab(pmID, xml);
                display_sc_surcharge_tab(pmID, xml);
		    	display_send_payment_tab(pmID, xml);
		    	display_send_payment_info_tab(pmID, xml);
		    	display_send_currencies_tab(pmID, xml);
			}
		});
	}
}

function install_payment_gateway(file_name) {
	jquery_confirm_box("Are you sure to install this payment gateway?", 2, 0 , "Confirm");
	jQuery('#jconfirm_submit').click(function(){
    	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
		jQuery.ajax({
		    url: "payment_methods_xmlhttp.php?action=install_payment_gateway&filename=" + file_name,
		    type: 'GET',
		    dataType: 'xml',
		  	success: function(xml){
		  		jQuery.unblockUI();
		  		var message_str = '';
		  		jQuery(xml).find('message').each(function(){
		  			message_str += jQuery(this).text() + "<BR>";
		  		});
		  		if (jQuery(xml).find('success').text()==1) {
			  		jquery_confirm_box(message_str, 0, 0);
			  		window.location="payment_methods.php?action=display_pg&pmID=" + jQuery(xml).find('payment_gateway_id').text();
		  		} else {
		  			jquery_confirm_box(message_str, 1, 1, "Error");
		  		}
		  	},
		  	error:function(xhr,err,e){ 
		  		jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
		  	}
		});
	});
}

function remove_payment_method(pmID) {
	jQuery.ajax({
	    url: "payment_methods_xmlhttp.php?action=payment_method_info&pm_id="+pmID,
	    type: 'GET',
	    dataType: 'xml',
	    timeout: 10000,
	    error: function(){
	        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
	    },
	    success: function(xml){
	    	var direct_url = "payment_methods.php";
	    	if(jQuery(xml).find('parent_id').text()==0) {
	    		jquery_confirm_box("Are you sure to remove this payment gateway ["+jQuery(xml).find('display_title').text()+"]?", 2, 0 , "Confirm");
	    	} else {
	    		jquery_confirm_box("Are you sure to remove this payment method ["+jQuery(xml).find('display_title').text()+"]?", 2, 0 , "Confirm");
	    		direct_url = "payment_methods.php?action=display_new_child&pmID="+jQuery(xml).find('parent_id').text();
	    	}
			jQuery('#jconfirm_submit').click(function(){
				jQuery.ajax({
				    url: "payment_methods_xmlhttp.php?action=remove_payment_method&pm_id="+pmID,
				    type: 'GET',
				    dataType: 'xml',
				    timeout: 10000,
				    error: function(){
				        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
				    },
				    success: function(xml){
				    	window.location=direct_url;
				    }
				});
			});
	    }
	});
}

function tongle_child_payment_methods (pmID) {
	if (jQuery('.tr_for_pm_'+pmID).css('display') == "none") {
		jQuery('.tr_for_pm_'+pmID).fadeIn('slow');
	} else {
		jQuery('.tr_for_pm_'+pmID).fadeOut('slow');
	}
}

function display_status_tab(pmID, xml) {
	if (jQuery(xml).find('display_status_tab').text()=="") return "";

	display_html = 	'<form id="submit_status_frm" name="submit_status_frm"  enctype="multipart/form-data">'+
					'<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">'+
					'	<tr><td width="350px" class="dataTableContent" valign="top">'+
					'		<fieldset>'+
					'			<legend><b>Receive Payment:</b></legend>' +
					'			<table>' +
					'				<tr>' +
					'					<td class="dataTableContent">' + 
					'						<input type="radio" name="rd_receive_payment" value="1" onclick="jQuery(\'.receive_input_field\').attr(\'disabled\', false);" checked> Enable' + 
					'						<input type="radio" name="rd_receive_payment" value="0" onclick="jQuery(\'.receive_input_field\').attr(\'disabled\', true);" ';
	if (jQuery(xml).find('payment_methods_receive_status').text()==0) {
		display_html += ' checked';
	}
	display_html += '> Disable' + 
					'					</td>' +
					'				</tr>' +
					'				<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' +
					'				<tr>' +
					'					<td class="dataTableContent"><b>Payment Method Name</b></td>' +
					'				</tr>' +
					'				<tr>' +
					'					<td><input type="text" name="payment_methods_title" value="' + jQuery(xml).find('payment_methods_title').text() + '" class="receive_input_field"><td>' +
					'				<tr>' +
					'				<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' +
					'				<tr>' +
					'					<td class="dataTableContent"><b>Payment Method Name (Display)</b></td>' +
					'				</tr>' +
					'				<tr>' +
					'					<td><input type="text" name="payment_methods_description_title" value="' + jQuery(xml).find('payment_methods_description_title').text() + '" class="receive_input_field"><td>' +
					'				<tr>' +
					'				<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' +
					'				<tr>' +
					'					<td class="dataTableContent">' + 
					'						<fieldset>'+
					'							<legend>Status</legend>' +
					'								<input type="radio" name="payment_methods_status" id="payment_methods_status" class="receive_input_field" value="1"';
	if (jQuery(xml).find('payment_methods_receive_status_mode').text()==1) {
		display_html += "		checked";
	}
	display_html += '			> Active<br/>'+
					'								<input type="radio" name="payment_methods_status" id="payment_methods_status" class="receive_input_field" value="-1"';
	if (jQuery(xml).find('payment_methods_receive_status_mode').text()!=1 && jQuery(xml).find('payment_methods_receive_status_mode').text()!=0) {
		display_html += '		checked';
	}
	display_html += '			> Inactive, <input type="text" name="payment_methods_status_message" id="payment_methods_status_message" class="receive_input_field" value="'+jQuery(xml).find('payment_methods_status_message').text()+'" size="30"><br/>'+
					'			<input type="radio" name="payment_methods_status" id="payment_methods_status" value="0"  class="receive_input_field" ';
	if (jQuery(xml).find('payment_methods_receive_status_mode').text()==0) {
		display_html += '		checked';
	}	
	display_html += '			> Disabled'+
					'						</fieldset>'+
					'					</td>' +
					'				</tr>';
	if (jQuery(xml).find('payment_methods_parent_id').text() != 0) {
		display_html += '			<tr><td class="dataTableContent">'+
						'				<fieldset>' +
						'					<legend>Payment Methods Type (Receiving)</legend>';
		jQuery(xml).find('payment_methods_type').each(function(){
			display_html += '<input type="radio" id="payment_methods_type" name="payment_methods_type" value="'+jQuery(this).attr('id')+'" class="receive_input_field"';
			if (jQuery(xml).find('payment_methods_types_id').text()==jQuery(this).attr('id')) {
				display_html += ' checked ';
			}
			display_html += ">";
			display_html += jQuery(this).text();
			display_html += "<br/>";
		});
		display_html += '					</fieldset>'+
						'				</td></tr>'+
						'				<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>';
	}
        /*-- Featured Status --*/
    display_html += '				<tr>' +
            '					<td class="dataTableContent">' +
            '						<fieldset>' +
    '							<legend>Featured Status</legend>' +
    '								<input type="radio" name="payment_methods_receive_featured_status" id="payment_methods_receive_featured_status" class="receive_input_field" value="1"';
    if (jQuery(xml).find('payment_methods_receive_featured_status').text() == 1) {
        display_html += "		checked";
    }
    display_html += '			> Enable<br/>' +
    '			<input type="radio" name="payment_methods_receive_featured_status" id="payment_methods_receive_featured_status" value="0"  class="receive_input_field" ';
    if (jQuery(xml).find('payment_methods_receive_featured_status').text() == 0) {
        display_html += '		checked';
    }
    display_html += '			> Disabled' +
            '						</fieldset>' +
            '					</td>' +
            '				</tr>';
        /*-- Featured Status --*/
        
	display_html += '			</table>'+
					'		</td>'+
					'		<td width="350px" class="dataTableContent" valign="top">' +
					'			<fieldset>'+
					'				<legend><b>Outgoing Payment:</b></legend>' +
					'				<table>' +
					'					<tr>' +
					'						<td class="dataTableContent">' + 
					'							<input type="radio" name="rd_outgoing_payment" value="1" onclick="jQuery(\'.outgoing_input_field\').attr(\'disabled\', false);" checked> Enable' + 
					'							<input type="radio" name="rd_outgoing_payment" value="0" onclick="jQuery(\'.outgoing_input_field\').attr(\'disabled\', true);" ';
	if (jQuery(xml).find('payment_methods_send_status').text()==0) {
		display_html += ' checked';
	}
	
	display_html += '> Disable' + 
					'						</td>' +
					'					</tr>' +
					'					<tr><td class="dataTableContent"><b>Title</b></td></tr>' +
					'					<tr>' +
					'						<td class="dataTableContent"><input type="text" id="send_mode_name" name="send_mode_name" value="'+jQuery(xml).find('payment_methods_send_mode_name').text()+'" class="outgoing_input_field"></td>' +
					'					</tr>' +
                                        '                                       <tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
					'					</tr>' +
					'					<tr><td class="dataTableContent"><b>Payment method code</b></td></tr>' +
					'					<tr>' +
					'						<td class="dataTableContent"><input type="text" id="payment_methods_send_code" name="payment_methods_send_code" value="'+jQuery(xml).find('payment_methods_send_code').text()+'" class="outgoing_input_field"></td>' +
					'					</tr>' +
                                        '                                       <tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
                                        '                                       <tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
					'					<tr><td class="dataTableContent">'+
					'						<fieldset id="field_outgoing_payment">'+
					'							<legend>Status</legend>'+
					'							<input type="radio" name="payment_methods_send_status" id="payment_methods_send_status" value="1" class="outgoing_input_field"';
	if (jQuery(xml).find('payment_methods_send_status_mode').text() == 1) {
		display_html += ' checked ';
	}
	display_html += '> Active <br>' +
					'							<input type="radio" name="payment_methods_send_status" id="payment_methods_send_status" value="-1" class="outgoing_input_field"';
	if (jQuery(xml).find('payment_methods_send_status_mode').text() == -1) {
		display_html += ' checked ';
	}
	display_html += '> Deactive, ' +
					'							<input type="text" name="payment_methods_send_status_message" id="payment_methods_send_status_message" value="'+jQuery(xml).find('payment_methods_status_send_message').text()+'" size="30" class="outgoing_input_field"><br>' +
					'							<input type="radio" name="payment_methods_send_status" id="payment_methods_send_status" value="0" class="outgoing_input_field"';
	if (jQuery(xml).find('payment_methods_send_status_mode').text() == 0) {
		display_html += ' checked ';
	}
	display_html += '> Disable' +
					'						</fieldset>' +
					'					</td></tr>';
	if (jQuery(xml).find('payment_methods_parent_id').text() > 0) {
		display_html += '				<tr><td class="dataTableContent">'+
						'					<fieldset id="field_outgoing_payment">'+
						'						<legend>Support Mass Payment</legend>'+
						'						<input type="radio" name="payment_methods_send_mass_payment" id="payment_methods_send_mass_payment" value="1" class="outgoing_input_field" checked> Yes <br>' +
						'						<input type="radio" name="payment_methods_send_mass_payment" id="payment_methods_send_mass_payment" value="0" class="outgoing_input_field"';
		if (jQuery(xml).find('payment_methods_send_mass_payment').text() == 0) {
			display_html += ' checked ';
		}
		display_html += '> No' +
						'						</fieldset>' +
						'					</td></tr>';
	}
	
	display_html += '					<tr><td class="dataTableContent">'+
					'						<fieldset id="field_outgoing_payment">'+
					'							<legend>Payment Methods Type (Outgoing)</legend>';
	jQuery(xml).find('payment_methods_send_type').each(function(){
		display_html += '<input type="radio" id="payment_methods_type" name="payment_methods_type" value="'+jQuery(this).attr('id')+'" class="outgoing_input_field"';
		if (jQuery(xml).find('payment_methods_types_id').text()==jQuery(this).attr('id')) {
			display_html += ' checked ';
		}
		display_html += ">";
		display_html += jQuery(this).text();
		display_html += "<br/>";
	});
	display_html += '						</fieldset>' +
					'					</td></tr>';
	
	display_html += '					<tr><td class="dataTableContent">'+
					'						<fieldset id="field_outgoing_payment">'+
					'							<legend>Available Sites (Outgoing)</legend>';
	jQuery(xml).find('payment_methods_send_available_site').each(function(){
		display_html += '<input type="checkbox" id="payment_methods_send_sites_'+jQuery(this).attr('id')+'" name="payment_methods_send_sites[]" value="'+jQuery(this).attr('id')+'" class="outgoing_input_field"';
		if (jQuery(xml).find('payment_methods_send_applicable_sites').text().indexOf(jQuery(this).attr('id')) >= 0) {
			display_html += ' checked ';
		}
		display_html += ">";
		display_html += jQuery(this).text();
		display_html += "<br/>";
	});
	display_html += '						</fieldset>' +
					'					</td></tr>';
	
	if (jQuery(xml).find('payment_methods_parent_id').text() != 0) {
		display_html += '				<tr><td class="dataTableContent"><b>Currency</b></td></tr>' +
						'				<tr>' +
						'					<td class="dataTableContent">';
		if (jQuery(xml).find('payment_methods_send_currency').text() == '') {
			if (jQuery(xml).find('payment_methods_send_currency').length > 0) {
				display_html +=	'					<select name="sel_outgoing_currency" class="outgoing_input_field">'+
								'						<option value="0">[Please Select]</option>"';
				jQuery(xml).find('currency').each(function(){
					display_html += '					<option value="'+jQuery(this).attr('id')+'">' + jQuery(this).text() + "</option>";
				});
				display_html +=	'					</select>';
			} else {
				display_html += 'error';
			}
		} else {
			display_html += jQuery(xml).find('payment_methods_send_currency').text();
		}
	display_html += '						</td>' +
					'					</tr>';
	}
	display_html += '				</table>' +
					'			</fieldset>'+
					'		</td>'+
					'	</tr>'+
					'	<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
					'	<tr><td class="dataTableContent"><b>Sort Order</b></td></tr>'+
					'	<tr><td class="dataTableContent">' +
					'		<input type="text" id="payment_methods_sort_order" name="payment_methods_sort_order" value="'+jQuery(xml).find('payment_methods_sort_order').text()+'">'+
					'	</td></tr>'+
					'	<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' + 
					'	<tr><td class="dataTableContent"><b>Logo</b></td></tr>' +
					'	<tr><td class="dataTableContent">'+
					'		<input type="file" id="payment_methods_logo" name="payment_methods_logo" value="">';
	if (jQuery(xml).find('payment_methods_logo').text()) {
		
		display_html += '<br><img id="payment_methods_display_logo" src="'+jQuery(xml).find('payment_methods_logo').text()+'" border="0" alt="" width="64" width="40">' + 
						'<br><input type="checkbox" value="1" name="remove_logo" id="remove_logo"> <i>Remove this payment method\'s logo</i>';
	} else {
		display_html += '<br><img id="payment_methods_display_logo" border="0" alt="" width="64" width="40">';
	}
	
	display_html += '	</td></tr>'+
					'	<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
					'	<tr><td>'+
					'		<input type="button" value="Update" title=" Update " name="Insert" class="inputButton" id="btn_submit_status"/>'+
					'	</td></tr>'+
					'</table>'+
					'</form>';
	
	jQuery('#payment_method_tab_1').html(display_html);
	
	if (jQuery(xml).find('payment_methods_receive_status').text()==0) {
		jQuery('.receive_input_field').attr('disabled', true);
	}
	
	if (jQuery(xml).find('payment_methods_send_status').text()==0) {
		jQuery('.outgoing_input_field').attr('disabled', true);
	}
	
	jQuery('#payment_method_li_1').css({display:'block'});
    
	// UPDATE TABLE
	var display_function = '';
	if (jQuery(xml).find('payment_methods_parent_id').text()==0) {
		display_function = ' onclick="tongle_child_payment_methods('+pmID+')"';
	}
	display_html = '';
	
	var display_receive_style='greenIndicatorBold';
	if (jQuery(xml).find('payment_methods_receive_status').text()!='1') {
		display_receive_style='redIndicatorBold';
	}
	
	display_html += "<td class='dataTableContent' "+display_function+">";
	if (jQuery(xml).find('payment_methods_parent_id').text()!=0) {
		display_html += "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
	}
	display_html += "<span class='"+display_receive_style+"'>"+jQuery(xml).find('payment_methods_display_title').text()+"</span>";
	display_html += "</td>";
	
	var display_send_style='greenIndicatorBold';
	if (jQuery(xml).find('payment_methods_send_status').text()!='1') {
		display_send_style='redIndicatorBold';
	}
	
	display_html += "<td class='dataTableContent' "+display_function+"><span class='"+display_send_style+"'>"+jQuery(xml).find('payment_methods_send_mode_name').text()+"</span></td>";
	display_html += "<td class='dataTableContent' align='left' "+display_function+">"+jQuery(xml).find('payment_methods_types_name').text()+"</td>";
	display_html += "<td class='dataTableContent' align='center' "+display_function+">"+jQuery(xml).find('payment_methods_sort_order').text()+"</td>";
	display_html += "<td class='dataTableContent' align='center' "+display_function+">";
	if (jQuery(xml).find('payment_methods_legend_color').text()!="") {
		display_html += '<hr style=" border: 1px solid black; color:'+jQuery(xml).find('payment_methods_legend_color').text()+'; background-color:'+jQuery(xml).find('payment_methods_legend_color').text()+'; height:10px; width:40%; text-align:center;">';
	}
	display_html += "</td>";
	display_html += "<td class='dataTableContent' align='center' "+display_function+">";

    if (jQuery(xml).find('payment_methods_receive_status_mode').text()!="") {
    	if (jQuery(xml).find('payment_methods_receive_status_mode').text()>0) {
      		display_html += "<img src='images/icon_status_green.gif' border='0' alt=' Active ' width='10' height='10'>";
    	} else if (jQuery(xml).find('payment_methods_receive_status_mode').text()!="" && jQuery(xml).find('payment_methods_receive_status_mode').text()== 0) {
      		display_html += "<img src='images/icon_status_red.gif' border='0' alt=' Inactive ' width='10' height='10'>";
    	} else {
    		display_html += "<img src='images/icon_status_yellow.gif' border='0' alt=' Deactive ' width='10' height='10'>";
    	}
	} else {
    	display_html += "&nbsp;";
  	}
	display_html += "</td>";
	
	display_html += "<td class='dataTableContent' align='center' "+display_function+" id='td_for_pm_send_status_"+pmID+"'>";
	
    if (jQuery(xml).find('payment_methods_send_status_mode').text()!="") {
    	if (jQuery(xml).find('payment_methods_send_status_mode').text()>0) {
      		display_html += "<img src='images/icon_status_green.gif' border='0' alt=' Active ' width='10' height='10'>";
    	} else {
      		display_html += "<img src='images/icon_status_red.gif' border='0' alt=' Inactive ' width='10' height='10'>";
    	}
	} else {
    	display_html += "&nbsp;";
  	}
	display_html += "</td>";
	
	display_html += '<td class="dataTableContent" align="left">';
	display_html += '	<a href="javascript:load_payment_methods('+pmID+')">';
	display_html += '	<img src="images/icons/edit.gif" border="0" alt=" Edit "></a>';
	//Check Child
	if (jQuery(xml).find('payment_methods_parent_id').text()==0) {
		display_html += "&nbsp;&nbsp;<a href='javascript:display_insert_payment_methods_tab("+pmID+");'>";
		display_html += '<img src="images/icons/add_item.gif" border="0" alt=" Add ">';
		display_html += '</a>'; 
	}
	display_html += "</td>";
	
	jQuery('tr#tr_for_pm_'+pmID).html(display_html);

    jQuery('#btn_submit_status').click(function(){ 
		jquery_confirm_box("Are you sure to update this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){
        	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
			jQuery('#submit_status_frm').ajaxSubmit({ 
				url: "payment_methods_xmlhttp.php?action=submit_status_tab&pm_id=" + pmID,
				dataType: 'xml',
				timeout: 10000,
				type: "POST",
			  	success: function(xml){
			  		jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
			  		if (jQuery(xml).find('success').text()==1) {
			  			jquery_confirm_box(message_str, 0, 0);
				  		if (!jQuery("#remove_logo").attr('checked')) {
				  			jupload_file(pmID, 'payment_methods_logo');
				  		}
			  			load_tab('display_status_tab', pmID);
			  			load_tab('display_send_currencies_tab', pmID);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
			  		}
			  	},
			  	error: function(xhr,err,e) { 
			  		jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
			  	}
			});
		});
	});
}

function active_receive_payment_method(passID) {
   	jQuery.ajax({
		type: "GET",
		dataType: 'xml',
		url: "payment_methods_xmlhttp.php?action=active_receive_payment_method&pm_id=" + passID,
		timeout: 10000,
	    error: function(){
	        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
	    },
		success: function(xml){
			jQuery.unblockUI();
	  		var message_str = '';
	  		jQuery(xml).find('message').each(function(){
	  			message_str += jQuery(this).text() + "<BR>";
	  		});
			if (jQuery(xml).find('success').text()==1) {
				load_tab('display_status_tab', passID);
	  		} else {
	  			jquery_confirm_box(message_str, 1, 1, "Error");
		  	}
		}
	});
}

function inactive_receive_payment_method(passID) {
   	jQuery.ajax({
		type: "GET",
		dataType: 'xml',
		url: "payment_methods_xmlhttp.php?action=inactive_receive_payment_method&pm_id=" + passID,
		timeout: 10000,
	    error: function(){
	        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
	    },
		success: function(xml){
			jQuery.unblockUI();
	  		var message_str = '';
	  		jQuery(xml).find('message').each(function(){
	  			message_str += jQuery(this).text() + "<BR>";
	  		});
			if (jQuery(xml).find('success').text()==1) {
				load_tab('display_status_tab', passID);
	  		} else {
	  			jquery_confirm_box(message_str, 1, 1, "Error");
		  	}
		}
	});
}

function active_send_payment_method(passID) {
   	jQuery.ajax({
		type: "GET",
		dataType: 'xml',
		url: "payment_methods_xmlhttp.php?action=active_send_payment_method&pm_id=" + passID,
		timeout: 10000,
	    error: function(){
	        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
	    },
		success: function(xml){
			jQuery.unblockUI();
	  		var message_str = '';
	  		jQuery(xml).find('message').each(function(){
	  			message_str += jQuery(this).text() + "<BR>";
	  		});
			if (jQuery(xml).find('success').text()==1) {
				load_tab('display_status_tab', passID);
				load_tab('display_send_payment_tab', passID);
	  		} else {
	  			jquery_confirm_box(message_str, 1, 1, "Error");
		  	}
		}
	});
}

function inactive_send_payment_method(passID) {
   	jQuery.ajax({
		type: "GET",
		dataType: 'xml',
		url: "payment_methods_xmlhttp.php?action=inactive_send_payment_method&pm_id=" + passID,
		timeout: 10000,
	    error: function(){
	        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
	    },
		success: function(xml){
			jQuery.unblockUI();
	  		var message_str = '';
	  		jQuery(xml).find('message').each(function(){
	  			message_str += jQuery(this).text() + "<BR>";
	  		});
			if (jQuery(xml).find('success').text()==1) {
				load_tab('display_status_tab', passID);
				load_tab('display_send_payment_tab', passID);
	  		} else {
	  			jquery_confirm_box(message_str, 1, 1, "Error");
		  	}
		}
	});
}

function jupload_file(pmID, file_id) {
	jQuery('#'+file_id).each(function(){
		jQuery.ajaxFileUpload({
			url:"payment_methods_xmlhttp.php?action=upload_logo&pm_id=" + pmID + "&file_name=" + file_id,
		    secureuri:false,
		    fileElementId:file_id,
		    dataType: 'xml',
		    timeout: 10000,
		    success: function(xml){
		    	jQuery("#payment_methods_display_logo").attr({ 
													          	src: jQuery(xml).find('path').text()
													          });
		  		if (jQuery(xml).find('path').text()!="") {
		  			jQuery("#remove_logo").attr("checked", "");
		  		}
		  		return 1;
		    },
		    error:function(xhr,err,e){ 
		        alert(e);
		    }
		});
	});
}

function display_settings_tab(pmID, xml) {
	if (jQuery(xml).find('display_settings_tab').text()=="") return "";
	
    display_html = '<form id="submit_setting_frm" name="submit_setting_frm">';
	display_html +='<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">';

	display_html += '<tr><td class="dataTableContent"><b>System Code</b></td></tr>';
	display_html += '<tr><td class="dataTableContent"><input type="text" id="payment_methods_code" name="payment_methods_code" value="' + jQuery(xml).find('payment_methods_code').text() + '"></td></tr>';
	display_html += "<tr><td><img src='images/pixel_trans.gif' border='0' alt='' width='1'></td></tr>";
	
    if (jQuery(xml).find('payment_methods_parent_id').text()>0) {
    	
    	display_html += '<tr><td class="dataTableContent"><b>Applicable for Customer Group</b></td></tr>';
    	
    	if (jQuery(xml).find('customers_groups').length) {
    		display_html += '<tr>'+
    						'	<td class="dataTableContent">';
	    	jQuery(xml).find('customers_groups').each(function(){
	    		display_html += '	<input type="checkbox" value="'+jQuery(this).attr('id')+'" name="chk_customers_groups[]"';
	    		if (jQuery(this).attr('selected')==1) {
	    			display_html += ' checked ';
	    		}
	    		display_html += '>' + jQuery(this).text() + '<BR>';
	    	});
	    	display_html += '	<td>' + 
	    					'<tr>';
	    }
    }
	display_html += "<tr><td><hr></td></tr>";
    if (jQuery(xml).find('payment_methods_parent_id').text()>0) {
    	display_html += '<tr>';
    	display_html += '	<td class="dataTableContent">';
    	display_html += 		"<input type='checkbox' value='1' name='use_parent'";
    	if (jQuery(xml).find('display_settings_tab').attr('found')=="0") {
    		display_html += 		" checked ";
    		
    	}
    	display_html += 		"> <i>Remove this payment method's setting [This payment method will use parent setting]</i>";
    	display_html += '	</td>';
    	display_html += '</tr>';
    }
    if (jQuery(xml).find('display_settings_tab').attr('found')!="0") {
    	display_html += "<tr><td><img src='images/pixel_trans.gif' border='0' alt='' width='1'></td></tr>";
        jQuery(xml).find('payment_methods_setting').each(function(){
        	display_html += '<tr>';
        	display_html += '	<td class="dataTableContent"><b>';
        	display_html += 		jQuery('payment_configuration_info_title', this).text();
        	display_html += '		</b><br>';
        	if (jQuery('payment_configuration_info_description', this).text()!="") {
        		display_html += 	jQuery('payment_configuration_info_description', this).text() + "<BR>";
        	}
        	display_html += 		jQuery('display', this).text();		        	
        	display_html += '	</td>';
        	display_html += '</tr>';
        	display_html += "<tr><td><img src='images/pixel_trans.gif' border='0' alt='' width='1'></td></tr>";
    	});
    }
	display_html += "	<tr><td><input id='btn_submit_setting' type='button' value='Update' title=' Update ' name='update' class='inputButton'/></td></tr>";
	display_html += '</table>';
	display_html += '</form>';
	
	jQuery('#payment_method_tab_2').html(display_html);
	
    jQuery('#btn_submit_setting').click(function(){ 
		jquery_confirm_box("Are you sure to update this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){ 		        	
        	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
			jQuery('#submit_setting_frm').ajaxSubmit({ 
				url: "payment_methods_xmlhttp.php?action=submit_setting_tab&pm_id=" + pmID,
				dataType: 'xml',
				type: "POST",
				timeout: 10000,
				cache: false,
				async: true,
			  	success: function(xml){
			  		jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
			  		if (jQuery(xml).find('success').text()==1) {
						load_tab('display_settings_tab', pmID);
				  		jquery_confirm_box(message_str, 0, 0);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
			  		}
			  	},
			  	error:function(xhr,err,e){ 
			  		jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
			  	}
			});
		});
	});	    
}

function display_currencies_tab(pmID, xml) {
	if (jQuery(xml).find('display_currencies_tab').text()=="") return "";
	
	var display_html = "";
	var payment_methods_instance_setting_keys_array = new Array();
	var count_payment_gateway_key = 0;
	jQuery(xml).find('payment_methods_instance_setting_key').each(function(){
		payment_methods_instance_setting_keys_array[count_payment_gateway_key] = new Array(jQuery(this).attr('key'), jQuery(this).find('mapping').text());
		count_payment_gateway_key++;
	});

	var number_of_colspan = count_payment_gateway_key + 3;
	display_html = '<form id="currencies_frm" name="currencies_frm">';
	display_html += '	<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">';

    if (jQuery(xml).find('payment_methods_parent_id').text() > 0) {
		display_html += '	<tr class="bracketListingEven">';
	 	display_html += '		<td class="menuBoxHeading" align="left" colspan="'+(number_of_colspan+1)+'"  nowrap="true">';
	 	if (jQuery(xml).find('payment_methods_settlement_currency').text()=="") {
	 		display_html += '			<input type="checkbox" name="follow_payment_gateway" id="follow_payment_gateway" checked>';
	 	} else {
			display_html += '			<input type="checkbox" name="follow_payment_gateway" id="follow_payment_gateway">';
	 	}
	 	display_html += '			Follow Payment Gateway &nbsp;<input value="Update" title=" Update " name="update" class="inputButton" type="button" onclick="update_currency_dependency('+pmID+')">';
	 	display_html += '		</td>';
	    display_html += '	</tr>';
		display_html += '	<tr class="bracketListingEven"><td colspan="'+(number_of_colspan+1)+'" class="menuBoxHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>';
	}
	display_html += '		<tr class="dataTableHeadingRow">';
	display_html += '			<td class="reportBoxHeading" width="50" align="left">Currency</td>';
	display_html += '			<td class="reportBoxHeading" width="50" align="center">Default</td>';
	display_html += '			<td class="reportBoxHeading" width="50" align="center">Copy from Default</td>';
	for (var keys in payment_methods_instance_setting_keys_array) {
		display_html += '		<td class="reportBoxHeading" width="*" align="center">'+payment_methods_instance_setting_keys_array[keys][1]+'</td>';
		
	}
	display_html += '			<td class="reportBoxHeading" width="15%">&nbsp;</td>';
	display_html += '		</tr>';

	var available_settlement_currency_array = new Array();
	count_settlement_currency = 0;
	jQuery(xml).find('currency').each(function(){
		//available_settlement_currency_array[count_settlement_currency] = new Array(jQuery(this).attr('id'),jQuery(this).text());
		available_settlement_currency_array[count_settlement_currency] = new Array(jQuery(this).text(),jQuery(this).text());
		count_settlement_currency++;
	});

	var pmi_id = 0;
	var count_row = 0;
	var row_style = '';
	var is_primary = 0;
	var display_class = '';
	var display_function = '';
	jQuery(xml).find('payment_methods_settlement_currency').each(function(){
		is_primary = false;
		if (jQuery('payment_methods_instance_default',this).text()=='1') {
			is_primary = true;
		}
		if (count_row%2>0) {
			row_style = "reportListingEven";
		} else {
			row_style = "reportListingOdd";
		}
		count_row++;
		pmi_id = jQuery('payment_methods_instance_id',this).text();

		display_html += '	<tr class="'+row_style+'">';
		display_html += '		<td class="dataTableContent" align="center" valign="top">';
		
		display_html += jQuery('code',this).text();
		display_html += '		</td>';
		display_html += '		<td class="dataTableContent" align="center" valign="top">';
		display_html += '			<input type="radio" class="settlement_setting_'+pmi_id+'" name="payment_methods_instance_default" value="'+pmi_id+'" ';
		if (is_primary) {
			display_html += ' checked';
		}
		display_html += '		onchange ="default_values_on_changed();"></td>';
		display_html += '		<td class="dataTableContent" align="center" valign="top">';
		if (!is_primary) {
			display_html += '		<input type="checkbox" onclick="cron_default_currency(this, \'settlement_setting_'+pmi_id+'\', \'settlement_default_setting\')" id="checkbox_copy_default_'+pmi_id+'" class="checkbox_copy_default settlement_setting_'+pmi_id+'" value="true" name="copy_default['+pmi_id+']"';
			if (jQuery('payment_methods_instance_follow_default',this).text()>0) {
				display_html += ' checked ';
			}
			display_html += '>';
		}
		display_html += '		</td>';
		jQuery(this).find('payment_methods_instance_setting').each(function(){
			display_class = 'settlement_setting_'+pmi_id+' ' + jQuery(this).attr('key');
			if (is_primary) {
				display_class += " settlement_default_setting";
				display_function = ' onKeyUp="default_values_on_changed()" ';
				display_function_dropdown = ' onchange="default_values_on_changed()" ';
			} else {
				display_function = ' onKeyUp="default_values_on_changed(\'checkbox_copy_default_'+pmi_id+'\')" ';
				display_function_dropdown = ' onchange="default_values_on_changed(\'checkbox_copy_default_'+pmi_id+'\')" ';
			}
			display_html += '	<td class="dataTableContent" align="center" valign="top">';
			
			stored_value = jQuery(this).text();
			
			if (jQuery(this).attr('field')=="textarea") {
				display_html += '		<textarea class="'+display_class+'" cols="'+jQuery(xml).find('payment_methods_instance_setting_key[key='+jQuery(this).attr('key')+'] cols').text()+'" rows="'+jQuery(xml).find('payment_methods_instance_setting_key[key='+jQuery(this).attr('key')+'] rows').text()+'"';
				display_html += ' ' + display_function;
				display_html += ' name="'+jQuery(this).attr('key')+'['+pmi_id+']" id="'+jQuery(this).attr('key')+'">'+jQuery(this).text()+'</textarea>';
			} else if (jQuery(this).attr('field')=="select") {
				display_html += '		<select name="'+jQuery(this).attr('key')+'['+pmi_id+']" id="'+jQuery(this).attr('key')+'" class="'+display_class+'" '+display_function_dropdown+' >';
				jQuery(xml).find('payment_methods_instance_setting_key[key='+jQuery(this).attr('key')+'] option').each(function(){
			    	display_html += '		<option value="'+jQuery(this).attr('value')+'"';
			    	if (jQuery(this).attr('value') == stored_value) {
			    		display_html += ' selected ';
			    	}
			    	display_html += '>'+jQuery(this).text()+'</option>';
			    });
				display_html += '		</select>';
			} else {
				display_html += '		<input class="'+display_class+'"';
				display_html += '" type="text" name="'+jQuery(this).attr('key')+'['+pmi_id+']" id="'+jQuery(this).attr('key')+'" value="'+jQuery(this).text()+'" ';
				display_html += ' ' + display_function;
				display_html += '>';
			}
			display_html += '	</td>';
		});
		display_html += '		<td class="dataTableContent" nowrap valign="top">';
		if (jQuery('payment_methods_instance_default',this).text()!='1') {
			display_html += '	&nbsp;<input type="button" value="Delete" title=" Delete " name="delete" class="inputButton" onclick="submit_currencies_tab(\'delete\', '+pmID+','+pmi_id+')" />';
		}
		display_html += '		</td>';
		display_html += '	</tr>';
	});
	if (jQuery(xml).find('payment_methods_settlement_currency').text()!='') {
		display_html += '	<tr class="bracketListingEven">';
		display_html += '		<td class="menuBoxHeading" align="left" colspan="'+(number_of_colspan-1)+'">&nbsp;</td>';
		display_html += '		<td class="menuBoxHeading" align="right"><input value="Update" title=" Update " name="update" class="inputButton" type="button" onclick="submit_currencies_tab(\'update\', '+pmID+')"></td>';
		display_html += '		<td class="menuBoxHeading" align="left">&nbsp;</td>';
		display_html += '	</tr>';
	}
	if (available_settlement_currency_array.length > 0) {
		if (count_row%2>0) {
			row_style = "reportListingEven";
		} else {
			row_style = "reportListingOdd";
		}
		display_html += '	<tr class="'+row_style+'">';
		display_html += '		<td class="dataTableContent">';
		display_html += '			<select class="new_currencies_setting" name="new[currencies]">';
		for (var count_settlement_currency in available_settlement_currency_array) {
			display_html += '			<option value="'+available_settlement_currency_array[count_settlement_currency][0]+'">'+available_settlement_currency_array[count_settlement_currency][1]+'</option>';
		}
		display_html += '			</select>';
		display_html += '		</td>';
		display_html += '		<td class="dataTableContent" align="center">&nbsp;</td>';
		display_html += '		<td class="dataTableContent" align="center">';
		if (jQuery(xml).find('payment_methods_settlement_currency').text()!="") {
			display_html += '		<input type="checkbox" onclick="cron_default_currency(this, \'new_currencies_setting\', \'settlement_default_setting\')" class="checkbox_copy_default new_currencies_setting" id="checkbox_copy_default" value="true" name="new[copy_default]">';
		}
		display_html += '		</td>';
		count_payment_gateway_key = 0;
		jQuery(xml).find('payment_methods_instance_setting_key').each(function(){
			display_class = 'new_currencies_setting '+payment_methods_instance_setting_keys_array[count_payment_gateway_key][0];
			display_function = ' onKeyUp="default_values_on_changed(\'checkbox_copy_default\')" ';						
			display_function_dropdown = ' onchange="default_values_on_changed(\'checkbox_copy_default\')" ';
			
			display_html += '	<td class="dataTableContent" align="center" valign="top">';
			if (jQuery(this).attr('field')=="textarea") {
				display_html += '		<textarea class="'+display_class+'" cols="'+jQuery(xml).find('payment_methods_instance_setting_key[key='+jQuery(this).attr('key')+'] cols').text()+'" rows="'+jQuery(xml).find('payment_methods_instance_setting_key[key='+jQuery(this).attr('key')+'] rows').text()+'"';
				display_html += ' ' + display_function;
				display_html += ' name="new['+payment_methods_instance_setting_keys_array[count_payment_gateway_key][0]+']" id="'+payment_methods_instance_setting_keys_array[count_payment_gateway_key][0]+'"></textarea>';
			} else if (jQuery(this).attr('field')=="select") {
				display_html += '		<select name="new['+jQuery(this).attr('key')+']['+pmi_id+']" class="'+display_class+'" id="'+payment_methods_instance_setting_keys_array[count_payment_gateway_key][0]+'" '+display_function_dropdown+'>';
				jQuery(xml).find('payment_methods_instance_setting_key[key='+jQuery(this).attr('key')+'] option').each(function(){
			    	display_html += '		<option value="'+jQuery(this).attr('value')+'">'+jQuery(this).text()+'</option>';
			    });
				display_html += '		</select>';
			} else {
				display_html += '		<input class="'+display_class+'"';
				display_html += '" type="text" name="new['+payment_methods_instance_setting_keys_array[count_payment_gateway_key][0]+']" id="'+payment_methods_instance_setting_keys_array[count_payment_gateway_key][0]+'" value="'+jQuery(this).find('default').text()+'" ';
				display_html += ' ' + display_function;
				display_html += '>';
			}
			display_html += '	</td>';
			count_payment_gateway_key++;
		});/*
		for (var keys in payment_methods_instance_setting_keys_array) {
			display_html += '	<td class="dataTableContent" align="center" align="center"><input class="new_currencies_setting '+payment_methods_instance_setting_keys_array[keys][0]+'" type="text" name="new['+payment_methods_instance_setting_keys_array[keys][0]+']" id="'+payment_methods_instance_setting_keys_array[keys][0]+'" value=""></td>';
		}*/
		display_html += '		<td class="dataTableContent"><input type="button" value="Insert" title=" Insert " name="insert" class="inputButton" onclick="submit_currencies_tab(\'insert\', '+pmID+','+pmi_id+')" /></td>';
		display_html += '	</tr>';
	}
	display_html += '	</table>';
	display_html += '</form>';
	jQuery('#payment_method_tab_3').html(display_html);
	    
}

function display_send_currencies_tab(pmID, xml) {
	if (jQuery(xml).find('display_send_currencies_tab').text()=="") return "";
	
	if (jQuery(xml).find('payment_methods_parent_id').text() == 0) {
		display_html = "This feature only available at payment methods (Childs only)";
	} else if (jQuery(xml).find('payment_methods_instance_setting').length == 0) {
		display_html = "Please enable outgoing payment or no outgoing payment supported for this payment.";
	} else {
		var display_html = "";
		var payment_methods_instance_setting_keys_array = new Array();
		var count_payment_gateway_key = 0;
		jQuery(xml).find('payment_methods_instance_setting_key').each(function(){
			payment_methods_instance_setting_keys_array[count_payment_gateway_key] = new Array(jQuery(this).attr('key'), jQuery(this).find('mapping').text());
			count_payment_gateway_key++;
		});
		
		var number_of_colspan = count_payment_gateway_key + 1;
		display_html = '<form id="outgoing_currencies_frm" name="outgoing_currencies_frm">';
		display_html += '	<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">';
		display_html += '		<tr class="dataTableHeadingRow">';
		display_html += '			<td class="reportBoxHeading" width="50" align="left">Currency</td>';
		for (var keys in payment_methods_instance_setting_keys_array) {
			display_html += '		<td class="reportBoxHeading" width="*" align="center">'+payment_methods_instance_setting_keys_array[keys][1]+'</td>';
			
		}
		display_html += '		</tr>';
		
		var available_settlement_currency_array = new Array();
		count_settlement_currency = 0;
		jQuery(xml).find('currency').each(function(){
			//available_settlement_currency_array[count_settlement_currency] = new Array(jQuery(this).attr('id'),jQuery(this).text());
			available_settlement_currency_array[count_settlement_currency] = new Array(jQuery(this).text(),jQuery(this).text());
			count_settlement_currency++;
		});
		
		var pmi_id = 0;
		var count_row = 0;
		var row_style = '';
		var is_primary = 0;
		var display_class = '';
		var display_function = '';
		jQuery(xml).find('payment_methods_settlement_currency').each(function(){
			is_primary = false;
			if (jQuery('payment_methods_instance_default',this).text()=='1') {
				is_primary = true;
			}
			if (count_row%2>0) {
				row_style = "reportListingEven";
			} else {
				row_style = "reportListingOdd";
			}
			count_row++;
			pmi_id = jQuery('payment_methods_instance_id',this).text();
	
			display_html += '	<tr class="'+row_style+'">';
			display_html += '		<td class="dataTableContent" align="center" valign="top">';
			display_html += jQuery('code',this).text();
			display_html += '		</td>';
			jQuery(this).find('payment_methods_instance_setting').each(function(){
				display_class = 'settlement_setting_'+pmi_id+' ' + jQuery(this).attr('key');
				if (is_primary) {
					display_class += " settlement_default_setting";
					display_function = ' onKeyUp="default_values_on_changed()" ';
					display_function_dropdown = ' onchange="default_values_on_changed()" ';
				} else {
					display_function = ' onKeyUp="default_values_on_changed(\'checkbox_copy_default_'+pmi_id+'\')" ';
					display_function_dropdown = ' onchange="default_values_on_changed(\'checkbox_copy_default_'+pmi_id+'\')" ';
				}
				display_html += '	<td class="dataTableContent" align="center" valign="top">';
				
				stored_value = jQuery(this).text();
				
				if (jQuery(this).attr('field')=="textarea") {
					display_html += '		<textarea class="'+display_class+'" cols="'+jQuery(xml).find('payment_methods_instance_setting_key[key='+jQuery(this).attr('key')+'] cols').text()+'" rows="'+jQuery(xml).find('payment_methods_instance_setting_key[key='+jQuery(this).attr('key')+'] rows').text()+'"';
					display_html += ' ' + display_function;
					display_html += ' name="'+jQuery(this).attr('key')+'['+pmi_id+']" id="'+jQuery(this).attr('key')+'">'+jQuery(this).text()+'</textarea>';
				} else if (jQuery(this).attr('field')=="select") {
					display_html += '		<select name="'+jQuery(this).attr('key')+'['+pmi_id+']" id="'+jQuery(this).attr('key')+'" class="'+display_class+'" '+display_function_dropdown+' >';
					jQuery(xml).find('payment_methods_instance_setting_key[key='+jQuery(this).attr('key')+'] option').each(function(){
				    	display_html += '		<option value="'+jQuery(this).attr('value')+'"';
				    	if (jQuery(this).attr('value') == stored_value) {
				    		display_html += ' selected ';
				    	}
				    	display_html += '>'+jQuery(this).text()+'</option>';
				    });
					display_html += '		</select>';
				} else {
					display_html += '		<input class="'+display_class+'"';
					display_html += '" type="text" name="'+jQuery(this).attr('key')+'['+pmi_id+']" id="'+jQuery(this).attr('key')+'" value="'+jQuery(this).text()+'" ';
					display_html += ' ' + display_function;
					display_html += '>';
				}
				display_html += '	</td>';
			});
			display_html += '	</tr>';
		});
		if (jQuery(xml).find('payment_methods_settlement_currency').text()!='') {
			display_html += '	<tr class="bracketListingEven">';
			display_html += '		<td class="menuBoxHeading" align="left" colspan="'+(number_of_colspan-1)+'">&nbsp;</td>';
			display_html += '		<td class="menuBoxHeading" align="right"><input value="Update" title=" Update " name="update" class="inputButton" type="button" onclick="submit_outgoing_currencies_tab(\'update\', '+pmID+')"></td>';
			display_html += '	</tr>';
		}
		display_html += '	</table>';
		display_html += '</form>';
	}
	jQuery('#payment_method_tab_7').html(display_html);
	    
}

function display_send_payment_tab(pmID, xml) {
	if (jQuery(xml).find('display_send_payment_tab').text()=="") return "";
	
	display_html =	'<form id="send_payment_tab_frm" name="send_payment_tab_frm"  enctype="multipart/form-data">'+
					'<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">'+
					'	<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>';
	if (jQuery(xml).find('payment_methods_parent_id').text()!=0) {
		display_html += '	<tr>'+
	    				'		<td class="dataTableContent">'+
	    				'			<input type="checkbox" value="1" name="use_parent" ';
	    if (jQuery(xml).find('payment_fees').length == 0) {
	    	display_html += ' checked ';
	    }
	    display_html += '	"><i>Remove this payment method\'s setting [This payment method will use parent setting]</i>'+
	    				'		</td>'+
	    				'	</tr>';
	}
	if (jQuery(xml).find('payment_methods_parent_id').text()!=0 && jQuery(xml).find('payment_fees').length == 0) {
		//
	} else {
		
		display_html += '	<tr><td class="dataTableContent"><b>PG Payout Currency: '+ jQuery(xml).find('currency').text() +'</b></td></tr>'+
						'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'	<tr><td ><hr></td></tr>'+
						'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'	<tr><td class="dataTableContent"><b>Transaction Fees (in USD)</b></td></tr>'+
						'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'	<tr><td class="dataTableContent"><b>Minimum Payment</b></td></tr>'+
						'	<tr><td class="dataTableContent"><input type="text" name="payment_fees_min" id="payment_fees_min" value="'+jQuery(xml).find('payment_fees_min').text()+'"></td></tr>'+
						'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						
						'	<tr><td class="dataTableContent"><b>Maximum Payment</b></td></tr>'+
						'	<tr><td class="dataTableContent"><input type="text" name="payment_fees_max" id="payment_fees_max" value="'+jQuery(xml).find('payment_fees_max').text()+'"></td></tr>'+
						'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						
						'	<tr><td class="dataTableContent"><b>Estimate Payment Received Period(mins)</b></td></tr>'+
						'	<tr><td class="dataTableContent"><input type="text" name="estimated_receive_period" id="estimated_receive_period" value="'+jQuery(xml).find('estimated_receive_period').text()+'"></td></tr>'+
						'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						
						'	<tr><td class="dataTableContent"><b>Transaction Fees</b></td></tr>'+
						'	<tr><td class="dataTableContent"><input type="text" name="payment_fees_cost_value" id="payment_fees_cost_value" value="'+jQuery(xml).find('payment_fees_cost_value').text()+'">(Per Transaction Fee, $)</td></tr>'+
						'	<tr><td class="dataTableContent"><input type="text" name="payment_fees_cost_percent" id="payment_fees_cost_percent" value="'+jQuery(xml).find('payment_fees_cost_percent').text()+'">(Amount Based Fee, %)</td></tr>'+
						'	<tr><td class="dataTableContent"><input type="text" name="payment_fees_cost_percent_min" id="payment_fees_cost_percent_min" value="'+jQuery(xml).find('payment_fees_cost_percent_min').text()+'">(min) / <input type="text" name="payment_fees_cost_percent_max" id="payment_fees_cost_percent_max" value="'+jQuery(xml).find('payment_fees_cost_percent_max').text()+'">(max)</td></tr>'+
						'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						
						'	<tr><td class="dataTableContent"><b>Who bear the fees</b></td></tr>'+
						'	<tr><td class="dataTableContent">'+
						'			<select name="payment_fees_bear_by" id="payment_fees_bear_by">'+
						'				<option value="payer">OffGamers</option>'+
						'				<option value="beneficiary">Beneficiary</option>'+
						'			</select>(Above minimum)'+
						'	</td></tr>'+
						'	<tr><td class="dataTableContent">'+
						'			<select name="payment_fees_below_min" id="payment_fees_below_min">'+
						'				<option value="na">Not allowed</option>'+
						'				<option value="payer">OffGamers</option>'+
						'				<option value="beneficiary">Beneficiary</option>'+
						'			</select>(Below minimum)'+
						'	</td></tr>'+
						'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'	<tr><td class="dataTableContent"><b>Information Needed</b></td></tr>'+
						'	<tr><td class="dataTableContent"><textarea name="payment_methods_send_required_info" rows="3" cols="50">'+jQuery(xml).find('payment_methods_send_required_info').text()+'</textarea></td></tr>';
	}
	display_html += '	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
					'	<tr><td><input id="btn_submit_send_payment" type="button" value="Update" title=" Update " name="update" class="inputButton"/></td></tr>'+
					'</form>';
	jQuery('#payment_method_tab_5').html(display_html);
	
	// set table/row's send status
	display_html = '';
    if (jQuery(xml).find('payment_methods_send_status_mode').text()!="") {
    	if (jQuery(xml).find('payment_methods_send_status_mode').text()>0) {
      		display_html += "<img src='images/icon_status_green.gif' border='0' alt=' Active ' width='10' height='10'>";
    	} else {
      		display_html += "<img src='images/icon_status_red.gif' border='0' alt=' Inactive ' width='10' height='10'>";
    	}
	} else {
    	display_html += "&nbsp;";
  	}
	jQuery('#td_for_pm_send_status_'+pmID).html(display_html);
	
	jQuery('#payment_fees_bear_by').val(jQuery(xml).find('payment_fees_bear_by').text());
	jQuery('#payment_fees_below_min').val(jQuery(xml).find('payment_fees_below_min').text());
	
	jQuery("#btn_submit_send_payment").click(function(){
		jquery_confirm_box("Are you sure to update this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){
        	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
			jQuery('#send_payment_tab_frm').ajaxSubmit({ 
				type: "POST",
				url: "payment_methods_xmlhttp.php?action=send_payment_tab&pm_id=" + pmID,
				timeout: 10000,
				cache: false,
	    		async: true,
				success: function(xml){
					jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
					if (jQuery(xml).find('success').text()==1) {
						load_tab('display_status_tab', pmID);
						load_tab('display_send_payment_tab', pmID);
			  			jquery_confirm_box(message_str, 0, 0);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
				  	}
				}
			});
		});
	});
}

function display_send_payment_info_tab(pmID, xml) {
	if (jQuery(xml).find('display_send_payment_info_tab').text()=="") return "";
	
	display_html = 	'	<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableContent">';
	/*if (jQuery(xml).find('payment_methods_parent_id').text() != 0) {
		checked_follow_parent =	'';
	    if (jQuery(xml).find('send_payment_info').length == 0) {
	    	checked_follow_parent += ' checked ';
	    }
		display_html += '	<tr>'+
						'		<td class="dataTableContent">'+
						'			<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">'+
						'				<tr>'+
						'					<td class="dataTableContent">'+
						'						<input type="checkbox" value="1" id="use_parent" name="use_parent" ' + checked_follow_parent + '> This payment method will use parent setting</td>'+
						'					</td>'+
						'				</tr>'+
						'				<tr>'+
						'					<td class="dataTableContent">'+
						'						<input id="btn_follow_parent" type="button" value="Update" title=" Update " name="btn_follow_parent" class="inputButton"/>'+
						'					</td>'+
						'				</tr>'
						'			</table>'+
						'		</td>'+
						'	</tr>'+
						'	<tr>'+
						'		<td><hr></td>'+
						'	</tr>';
	}*/
	
	var display_payment_info_html = ' style="display:none" ';
	if (jQuery(xml).find('payment_methods_parent_id').text() == 0) {
	 	display_payment_info_html = '';
	}
	if (jQuery(xml).find('send_payment_info').length > 0) {
		display_payment_info_html = '';
		display_html += '	<tr>'+
						'		<td class="dataTableContent">' +
						'			<form id="send_payment_info_tab_frm" name="send_payment_info_tab_frm" enctype="multipart/form-data">' + 
						'			<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">' +
						'				<tr class="dataTableHeadingRow">' +
						'					<td class="reportBoxHeading" align="center">Field Title</td>'+
						'					<td class="reportBoxHeading" align="center">Field Type</td>'+
						'					<td class="reportBoxHeading" align="center">Status</td>'+
						'					<td class="reportBoxHeading" align="center">Mandarory</td>'+
						'					<td class="reportBoxHeading" align="center">Sort Order</td>'+
						'					<td class="reportBoxHeading" align="center">Action</td>'+
						'				</tr>';
		jQuery(xml).find('send_payment_info').each(function(){
			display_html += '			<tr class="reportListingOdd">' +
							'				<td class="dataTableContent">' + jQuery(this).find('payment_methods_fields_title').text();
			if (jQuery(this).find('payment_methods_fields_system_type').text()!='') {
				display_html +=	' <i>['+jQuery(this).find('payment_methods_fields_system_type').text()+']</i>'
			}
			
			display_html += ' </td>'+
							'				<td class="dataTableContent" align="center">' + jQuery(this).find('payment_methods_fields_type').text() +'</td>'+
							'				<td class="dataTableContent" align="center">' + jQuery(this).find('payment_methods_fields_status').text() +'</td>'+
							'				<td class="dataTableContent" align="center">' + jQuery(this).find('payment_methods_fields_required').text() +'</td>'+
							'				<td class="dataTableContent">' + jQuery(this).find('payment_methods_fields_sort_order').text() +'</td>'+
							'				<td class="dataTableContent" align="center">' +
							'					<a href="javascript:load_payment_info(\''+pmID+'\',\''+jQuery(this).attr('id')+'\')"><img border="0" align="top" title=" Edit " alt="Edit" src="images/icons/edit.gif"/></a>';
			if (jQuery(this).find('payment_methods_fields_system_type').text()=='') {
				display_html += '				<a href="javascript:delete_send_payment_info(\''+jQuery(this).attr('id')+'\', \''+pmID+'\')"><img border="0" align="top" title=" Delete " alt="Delete" src="images/icons/delete.gif"/></a>';
			}
			display_html += '				</td>'+
							'			</tr>';
		});
		display_html += '				<tr>'+
						'					<td colspan="6">'+
						'						<input id="btn_submit_update" type="button" value="Update" title=" Update " name="btn_submit_update" class="inputButton" onclick="update_send_payment(\'sort_order\',\''+pmID+'\')"/>'+
						'					</td>' +
						'				</tr>' +
						'			</table>'+
						'			</form>'+
						'		</td>'+
						'	</tr>'+
						'	<tr>'+
						'		<td><hr></td>'+
						'	</tr>';
	}
	//display_html += 	'	<tr id="tr_add_payment_info" '+display_payment_info_html+'>'+
	display_html += 	'	<tr id="tr_add_payment_info">'+
						'		<td>'+
						'			<div id="div_add_payment_info">'+
						'				<form name="insert_payment_info_frm" id="insert_payment_info_frm">'+
						'				<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">'+
						'					<tr>'+
						'						<td><b>Add New</b></td>'+
						'					</tr>'+
						'					<tr>'+
						'						<td class="dataTableContent" width="120px" valign="top">Field Title:</td>'+
						'						<td class="dataTableContent"><input type="text" name="fields_title" id="fields_title"></td>'+
						'					</tr>'+
						'					<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr>'+
						'						<td colspan="2"><fieldset class="selectedFieldSet">'+
						'							<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">'+
						'								<tr>'+
						'									<td class="dataTableContent" valign="top">Field Type:</td>'+
						'									<td class="dataTableContent">'+
						'										<select name="fields_type" id="fields_type" onchange="update_send_payment_setting_div(this.value)">'+
						'											<option selected="" value="1">Text Box</option>'+
						'											<option value="2">Text Area</option>'+
						'											<option value="3">Dropdown Menu</option>'+
						'											<option value="4">Radio Button</option>'+
						'											<option value="5">Date Selection</option>'+
						'											<option value="6">Display Label</option>'+
						'											<option value="7">Information Text</option>'+
						'											<option value="8">Hidden Field</option>'+
						'										</select>'+
						'									</td>'+
						'								</tr>'+
						'								<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'								<tr>'+
						'									<td class="dataTableContent" valign="top">Settings:</td>'+
						'									<td class="dataTableContent"><div id="div_insert_setting"></fieldset></div></td>'+
						'								</tr>'+
						'								<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'								<tr>'+
						'									<td class="dataTableContent" valign="top"></td>'+
						'									<td class="dataTableContent">'+
						'									Pre Text&nbsp;&nbsp;<input type="text" name="fields_pre_info" id="fields_pre_info">'+
						'									Post Text&nbsp;&nbsp;<input type="text" name="fields_post_info" id="fields_post_info">'+
						'									</td>'+
						'								</tr>'+
						'							</table>'+
						'						</fieldset></td>'+
						'					</tr>'+
						'					<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr>'+
						'						<td class="dataTableContent" valign="top">Status:</td>'+
						'						<td class="dataTableContent">'+
						'							<input type="radio" value="1" name="fields_status"> Yes'+
						'							<input type="radio" value="0" name="fields_status" checked> No'+
						'						</td>'+
						'					</tr>'+
						'					<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr>'+
						'						<td class="dataTableContent" valign="top">Mandatory Field:</td>'+
						'						<td class="dataTableContent">'+
						'							<input type="radio" value="1" name="fields_required"> Yes'+
						'							<input type="radio" value="0" name="fields_required" checked> No'+
						'						</td>'+
						'					</tr>'+
						'					<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr>'+
						'						<td class="dataTableContent" valign="top">Sort Order:</td>'+
						'						<td class="dataTableContent"><input type="text" name="fields_sort_order" id="fields_sort_order"></td>'+
						'					</tr>'+
						'					<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr>'+
						'						<td colspan="2"><input id="btn_insert_send_payment" type="button" value="Add New" title=" Add New " name="btn_add_new" class="inputButton"/></td>'+
						'					</tr>' + 
						'				</table>'+
						'				</form>'+
						'			</div>'+
						'		</td>'+
						'	</tr>'+
						'	<tr '+display_payment_info_html+'>'+
						'		<td>'+
						'			<div id="div_update_payment_info" style="display:none;">'+
						'				<form name="update_payment_info_frm" id="update_payment_info_frm">'+
						'				<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">'+
						'					<tr>'+
						'						<td colspan="2"><b>Edit Payment info<input type="hidden" id="fields_id" value=""></b></td>'+
						'					</tr>'+
						'					<tr>'+
						'						<td class="dataTableContent" width="120px" valign="top">Field Title:</td>'+
						'						<td class="dataTableContent"><input type="text" name="fields_title" id="fields_title"></td>'+
						'					</tr>'+
						'					<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr>'+
						'						<td colspan="2"><fieldset class="selectedFieldSet">'+
						'							<table>'+
						'								<tr>'+
						'									<td class="dataTableContent" valign="top">Field Type:</td>'+
						'									<td class="dataTableContent">'+
						'										<select name="fields_type" id="fields_type" onchange="update_send_payment_setting_div(this.value, 1)">'+
						'											<option selected="" value="1">Text Box</option>'+
						'											<option value="2">Text Area</option>'+
						'											<option value="3">Dropdown Menu</option>'+
						'											<option value="4">Radio Button</option>'+
						'											<option value="5">Date Selection</option>'+
						'											<option value="6">Display Label</option>'+
						'											<option value="7">Information Text</option>'+
						'										</select>'+
						'									</td>'+
						'								</tr>'+
						'								<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'								<tr>'+
						'									<td class="dataTableContent" valign="top">Settings:</td>'+
						'									<td class="dataTableContent"><div id="div_update_setting"></fieldset></div></td>'+
						'								</tr>'+
						'								<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'								<tr>'+
						'									<td class="dataTableContent" valign="top"></td>'+
						'									<td class="dataTableContent">'+
						'									Pre Text&nbsp;&nbsp;<input type="text" name="fields_pre_info" id="fields_pre_info">'+
						'									Post Text&nbsp;&nbsp;<input type="text" name="fields_post_info" id="fields_post_info">'+
						'									</td>'+
						'								</tr>'+
						'							</table>'+
						'						</fieldset></td>'+
						'					</tr>'+
						'					<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr class="tr_update_pmf_status">'+
						'						<td class="dataTableContent" valign="top">Status:</td>'+
						'						<td class="dataTableContent">'+
						'							<input type="radio" value="1" name="fields_status"> Yes'+
						'							<input type="radio" value="0" name="fields_status"> No'+
						'						</td>'+
						'					</tr>'+
						'					<tr class="tr_update_pmf_status"><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr class="tr_update_pmf_mandatory">'+
						'						<td class="dataTableContent" valign="top">Mandatory Field:</td>'+
						'						<td class="dataTableContent">'+
						'							<input type="radio" value="1" name="fields_required"> Yes'+
						'							<input type="radio" value="0" name="fields_required"> No'+
						'						</td>'+
						'					</tr>'+
						'					<tr class="tr_update_pmf_mandatory"><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr>'+
						'						<td class="dataTableContent" valign="top">Sort Order:</td>'+
						'						<td class="dataTableContent"><input type="text" name="fields_sort_order" id="fields_sort_order"></td>'+
						'					</tr>'+
						'					<tr><td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
						'					<tr>'+
						'						<td></td>'+
						'						<td>'+
						'							<input id="btn_update_payment_info" type="button" value="Update" title=" Update " name="btn_update_payment_info" class="inputButton"/>'+
						'							<input id="btn_cancel_update_payment_info" type="button" value="Cancel" title=" Cancel " name="btn_cancel_update_payment_info" class="inputButton"/>'+
						'						</td>'+
						'					</tr>' + 
						'				</table>'+
						'				</form>'+
						'			</div>'+
						'		</td>'+
						'	</tr>'+
						'</table>';
	jQuery('#payment_method_tab_6').html(display_html);
	update_send_payment_setting_div('1');
	
	jQuery("#btn_insert_send_payment").click(function(){
		jquery_confirm_box("Are you sure to insert this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){ 		        	
        	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
			jQuery('#insert_payment_info_frm').ajaxSubmit({ 
				type: "POST",
				url: "payment_methods_xmlhttp.php?action=insert_pm_info&pm_id=" + pmID,
				timeout: 10000,
				cache: false,
	    		async: true,
				success: function(xml){
					jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
					if (jQuery(xml).find('success').text()==1) {
						load_tab('display_send_payment_info_tab', pmID);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
				  	}
				}
			});
		});
	});
	
	jQuery("#btn_follow_parent").click(function(){
		use_parent_flag = 0;
		if (jQuery("#use_parent").attr("checked")) {
			use_parent_flag = 1;
		}
		jquery_confirm_box("Are you sure to update this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){
			jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
		   	jQuery.ajax({
				type: "GET",
				dataType: 'xml',
				url: "payment_methods_xmlhttp.php?action=follow_parent_send_payment_info&flag="+use_parent_flag+"&pm_id=" + pmID,
				timeout: 10000,
			    error: function(){
			        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
			    },
				success: function(xml){
					jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
					if (jQuery(xml).find('success').text()==1) {
						if (jQuery(xml).find('record_created').text()==1) {
							load_tab('display_send_payment_info_tab', pmID);
						} else {
							jQuery("#tr_add_payment_info").toggle();
						}
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
				  	}
				}
			});
		});
	});
	
	jQuery("#btn_update_payment_info").click(function(){
		jquery_confirm_box("Are you sure to update this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){ 		        	
        	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
			jQuery('#update_payment_info_frm').ajaxSubmit({ 
				type: "POST",
				url: "payment_methods_xmlhttp.php?action=update_pm_info&pm_id=" + pmID+ "&pmf_id=" + jQuery('div#div_update_payment_info input#fields_id').val(),
				timeout: 10000,
				cache: false,
	    		async: true,
				success: function(xml){
					jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
					if (jQuery(xml).find('success').text()==1) {
						load_tab('display_send_payment_info_tab', pmID);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
				  	}
				}
			});
		});
	});
	
	jQuery("#btn_cancel_update_payment_info").click(function(){
		jQuery("#div_add_payment_info").show();
		jQuery("#div_update_payment_info").hide();	
	});
}

function update_send_payment(passType, passID, passFieldID, passValue) {
	passValue = passValue || '';
	jquery_confirm_box("Are you sure to update this record?", 2, 0 , "Confirm");
	jQuery('#jconfirm_submit').click(function(){
    	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
		jQuery('#send_payment_info_tab_frm').ajaxSubmit({ 
			type: "POST",
			url: "payment_methods_xmlhttp.php?action=update_send_payment&type="+passType+"&pmId="+passID+"&pmfID=" + passFieldID+"&value=" + passValue,
			timeout: 10000,
			cache: false,
    		async: true,
			success: function(xml){
				jQuery.unblockUI();
		  		var message_str = '';
		  		jQuery(xml).find('message').each(function(){
		  			message_str += jQuery(this).text() + "<BR>";
		  		});
				if (jQuery(xml).find('success').text()==1) {
					load_tab('display_send_payment_info_tab', passID);
		  		} else {
		  			jquery_confirm_box(message_str, 1, 1, "Error");
			  	}
			}
		});
	});
}

function delete_send_payment_info(pass_id, pass_pm_id) {
	jquery_confirm_box("Are you sure to delete this record?", 2, 0 , "Confirm");
	jQuery('#jconfirm_submit').click(function(){ 		        	
    	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
    	
    	jQuery.ajax({
			type: "GET",
			dataType: 'xml',
			url: "payment_methods_xmlhttp.php?action=delete_send_payment_info&pmf_id=" + pass_id,
			timeout: 10000,
		    error: function(){
		        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
		    },
			success: function(xml){
				jQuery.unblockUI();
		  		var message_str = '';
		  		jQuery(xml).find('message').each(function(){
		  			message_str += jQuery(this).text() + "<BR>";
		  		});
				if (jQuery(xml).find('success').text()==1) {
					load_tab('display_send_payment_info_tab', pass_pm_id);
		  		} else {
		  			jquery_confirm_box(message_str, 1, 1, "Error");
			  	}
			}
		});
	});
}

function load_payment_info(passPmId, passPmfId) {
	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
	jQuery.ajax({
		type: "GET",
		dataType: 'xml',
		url: "payment_methods_xmlhttp.php?action=load_send_payment_info&pm_id="+passPmId+"&pmf_id=" + passPmfId,
		timeout: 10000,
	    error: function(){
	        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
	    },
		success: function(xml){
			jQuery.unblockUI();
			if (jQuery(xml).find('payment_methods_fields').length > 0) {
				update_send_payment_setting_div(jQuery(xml).find('payment_methods_fields_type').text(), 1);
				jQuery("div#div_update_payment_info #fields_id").val(jQuery(xml).find('payment_methods_fields_id').text());
				jQuery("div#div_update_payment_info #fields_title").val(jQuery(xml).find('payment_methods_fields_title').text());
				jQuery("div#div_update_payment_info #fields_type").val(jQuery(xml).find('payment_methods_fields_type').text());
				jQuery("div#div_update_payment_info #fields_pre_info").val(jQuery(xml).find('payment_methods_fields_pre_info').text());
				jQuery("div#div_update_payment_info #fields_post_info").val(jQuery(xml).find('payment_methods_fields_post_info').text());
				
				jQuery("div#div_update_payment_info input[name=fields_status][value="+jQuery(xml).find('payment_methods_fields_status').text()+"]").attr('checked',true);
				jQuery("div#div_update_payment_info input[name=fields_required][value="+jQuery(xml).find('payment_methods_fields_required').text()+"]").attr('checked',true);
				
				jQuery("div#div_update_payment_info #fields_status").val(jQuery(xml).find('payment_methods_fields_status').text());
				jQuery("div#div_update_payment_info #fields_required").val(jQuery(xml).find('payment_methods_fields_required').text());
				jQuery("div#div_update_payment_info #fields_sort_order").val(jQuery(xml).find('payment_methods_fields_sort_order').text());
				
				if (jQuery(xml).find('payment_methods_fields_system_type').text()=='') {
					jQuery("tr.tr_update_pmf_status").toggle(true);
					jQuery("tr.tr_update_pmf_mandatory").toggle(true);
				} else {
					jQuery("tr.tr_update_pmf_status").toggle(false);
					jQuery("tr.tr_update_pmf_mandatory").toggle(false);
				}
				
				
				switch (jQuery(xml).find('payment_methods_fields_type').text()) {
					case '1': // textbox
						jQuery('div#div_update_payment_info #box_size').val(jQuery(xml).find('box_size').text());
						jQuery('div#div_update_payment_info #box_text_max_len').val(jQuery(xml).find('box_text_max_len').text());
						jQuery("div#div_update_payment_info #fields_system_type").val(jQuery(xml).find('payment_methods_fields_system_type').text());
						break;
					case '2': // textarea
						jQuery('div#div_update_payment_info #box_row').val(jQuery(xml).find('box_row').text());
						jQuery('div#div_update_payment_info #box_col').val(jQuery(xml).find('box_col').text());
						break;
					case '3': // dropdown
						jQuery('div#div_update_payment_info #selection').val(jQuery(xml).find('selection').text());
						jQuery('div#div_update_payment_info [name=option_title][value='+jQuery(xml).find('option_title').text()+']').attr('checked',true);
						jQuery('div#div_update_payment_info #box_from').val(jQuery(xml).find('box_from').text());
						jQuery('div#div_update_payment_info #box_period').val(jQuery(xml).find('box_period').text());
						jQuery("div#div_update_payment_info #fields_system_type").val(jQuery(xml).find('payment_methods_fields_system_type').text());
						break;
					case '4': // radio
						jQuery('div#div_update_payment_info #selection').val(jQuery(xml).find('selection').text());
						jQuery('div#div_update_payment_info #box_from').val(jQuery(xml).find('box_from').text());
						jQuery('div#div_update_payment_info #box_period').val(jQuery(xml).find('box_period').text());
						break;
					case '5': // date
						jQuery('div#div_update_payment_info #box_from').val(jQuery(xml).find('box_from').text());
						jQuery('div#div_update_payment_info #box_period').val(jQuery(xml).find('box_period').text());
						break;
					case '6': // Display Label
						break;
					case '7': // information text
						jQuery('div#div_update_payment_info #selection').val(jQuery(xml).find('selection').text());
						break;
				}
			}
		}
	});
	jQuery("#div_add_payment_info").hide();
	jQuery("#div_update_payment_info").show();
}

function update_send_payment_setting_div(passId, updateFlag) {
	var display_html = '';
	
	updateFlag = updateFlag || 0;
	
	switch (passId) {
		case '1':
				display_html += 'Size&nbsp;<input id="box_size" type="text" onkeyup="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" size="5" value="55" name="box_size"/>'+
								'&nbsp;&nbsp;&nbsp;'+
								'Maximum Character&nbsp;<input id="box_text_max_len" type="text" onkeyup="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" size="5" value="44" name="box_text_max_len"/>';
			break;
		case '2':
				display_html += 'Row&nbsp;<input id="box_row" type="text" onkeyup="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" size="5" value="55" name="box_row"/>'+
								'&nbsp;&nbsp;'+
								'Column&nbsp;<input id="box_col" type="text" onkeyup="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" size="5" value="44" name="box_col"/>';
			break;
		case '3':
		case '4':
				display_html += 'Option values:<BR><textarea style="width: 370px; height: 100px;" id="selection" name="selection"/><BR>'+
								'Each option values must be in a new line.<BR>';
				if (passId==3) {
					display_html += 'First option is just a title?'+
									'<input type="radio" value="1" name="option_title"/>Yes&nbsp;&nbsp;'+
									'<input type="radio" checked="" value="0" name="option_title"/>No<BR>';
				}
			break;
		case '5':
				display_html += 'From: <input name="box_from" id="box_from" value="55" size="12" type="text">'+
								'<small>Options: YYYY-MM-DD, "TODAY" to use system current date</small><BR>'+
								'Periods (days): <input name="box_period" id="box_period" value="" size="5" type="text">';
			break;
		case '6':
				display_html += 'No setting needed! This is just a normal text label.';
			break;
		case '7':
				display_html += 'Display Text<br>'+
								'<textarea id="selection" style="width: 370px; height: 100px;" name="selection"/>';
			break;
	}
	
	if (updateFlag==1) {
		jQuery("div#div_update_setting").html(display_html);
	} else {
		jQuery("div#div_insert_setting").html(display_html);
	}
	
}

function update_currency_dependency(pmID) {
	if (jQuery('#follow_payment_gateway').attr('checked')) {
		var follow = 1;
		jquery_confirm_box("Are you sure to update this to 'Follow Payment Gateway'?",2 , 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){
			jquery_confirm_box('<h1>Updating...</h1>', 0, 0);
			jQuery.ajax({
				type: "GET",
				dataType: 'xml',
				url: "payment_methods_xmlhttp.php?action=currency_dependency&pm_id=" + pmID,
				timeout: 10000,
			    error: function(){
			        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
			    },
				success: function(xml){
					jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
					if (jQuery(xml).find('success').text()==1) {
			  			load_tab('display_currencies_tab', pmID);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
				  	}
				}
			});
		});
	}
}

function default_values_on_changed (pass_id) {
	pass_id = pass_id || 0;
	if (pass_id==0) {
		jQuery('.checkbox_copy_default').each(function(){
			jQuery(this).attr("checked", "");
		});
	} else {
		jQuery("#"+pass_id).attr("checked", "");
	}
}

function cron_default_currency(pass_obj, selected_class, default_class){
	jQuery('.'+selected_class).each(function(){
		if (jQuery(pass_obj).attr('checked')) {
			if (jQuery(this).attr('type')=='text') {
				jQuery("."+selected_class+"#"+jQuery(this).attr('id')).val(jQuery(".settlement_default_setting#"+jQuery(this).attr('id')).val());
            } else {
            	jQuery("textarea."+selected_class).val(jQuery(".settlement_default_setting#"+jQuery(this).attr('id')).val());
            }
	    }
	});
}

function submit_currencies_tab(pm_action, pmID, pmiID) {
	var submit_data = '';
	pmiID = pmiID || 0;
	if (pm_action =='update') {
		jquery_confirm_box("Are you sure to update this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){
			jquery_confirm_box('<h1>Updating...</h1>', 0, 0);
			jQuery('#currencies_frm').ajaxSubmit({ 
				type: "POST",
				url: "payment_methods_xmlhttp.php?action=submit_currencies_tab&pm_id=" + pmID,
				timeout: 10000,
				cache: false,
	    		async: true,
				data: submit_data,
				success: function(xml){
					jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
					if (jQuery(xml).find('success').text()==1) {
						load_tab('display_currencies_tab', pmID);
						//update_supported_tab(pmID);
			  			jquery_confirm_box(message_str, 0, 0);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
				  	}
				}
			});
		});
	} else if (pm_action=='delete'){
		jquery_confirm_box("Are you sure to delete this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){
			jquery_confirm_box('<h1>Deleting...</h1>', 0, 0);
			jQuery.ajax({
				type: "GET",
				url: "payment_methods_xmlhttp.php?action=delete_settlement&pm_id=" + pmID + "&pmi_id=" + pmiID,
				timeout: 10000,
				cache: false,
	    		async: true,
				success: function(xml){
					jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
					if (jQuery(xml).find('success').text()==1) {
						load_tab('display_currencies_tab', pmID);
						//update_supported_tab(pmID);
			  			jquery_confirm_box(message_str, 0, 0);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
				  	}
				}
			});
		});
	} else if (pm_action=='insert'){
		jQuery(".new_currencies_setting").each(function(){
			if (jQuery(this).attr('type')=="checkbox") {
				if (jQuery(this).attr('checked')) {
					submit_data += jQuery(this).attr('name') + "=1&";
				} else {
					submit_data += jQuery(this).attr('name') + "=0&";
				}
			} else {
				submit_data += jQuery(this).attr('name') + "=" + jQuery(this).val() + "&";
			}
			
		});

		jquery_confirm_box("Are you sure to insert this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){
			jquery_confirm_box('<h1>Inserting...</h1>', 0, 0);
			jQuery.ajax({
				type: "POST",
				url: "payment_methods_xmlhttp.php?action=insert_currencies&pm_id=" + pmID,
				timeout: 10000,
				cache: false,
	    		async: true,
				data: submit_data,
				success: function(xml){
					jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
					if (jQuery(xml).find('success').text()==1) {
						load_tab('display_currencies_tab', pmID);
			  			jquery_confirm_box(message_str, 0, 0);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
				  	}
				}
			});
		});
	}
}


function submit_outgoing_currencies_tab(pm_action, pmID, pmiID) {
	var submit_data = '';
	pmiID = pmiID || 0;
	if (pm_action =='update') {
		jquery_confirm_box("Are you sure to update this record?", 2, 0 , "Confirm");
		jQuery('#jconfirm_submit').click(function(){
			jquery_confirm_box('<h1>Updating...</h1>', 0, 0);
			jQuery('#outgoing_currencies_frm').ajaxSubmit({ 
				type: "POST",
				url: "payment_methods_xmlhttp.php?action=submit_outgoing_currencies_tab&pm_id=" + pmID,
				timeout: 10000,
				cache: false,
	    		async: true,
				data: submit_data,
				success: function(xml){
					jQuery.unblockUI();
			  		var message_str = '';
			  		jQuery(xml).find('message').each(function(){
			  			message_str += jQuery(this).text() + "<BR>";
			  		});
					if (jQuery(xml).find('success').text()==1) {
						load_tab('display_send_currencies_tab', pmID);
						//update_supported_tab(pmID);
			  			jquery_confirm_box(message_str, 0, 0);
			  		} else {
			  			jquery_confirm_box(message_str, 1, 1, "Error");
				  	}
				}
			});
		});
	}
}

function display_insert_payment_methods_tab(pmID) {
	jQuery("#payment_methods_id").val("");
	
	jQuery('#payment_method_li_2').css({display:'none'});
	jQuery('#payment_method_li_3').css({display:'none'});
	jQuery('#payment_method_li_4').css({display:'none'});
	jQuery('#payment_method_li_5').css({display:'none'});
	jQuery('#payment_method_li_6').css({display:'none'});
	jQuery('#payment_method_li_7').css({display:'none'});

	jQuery('#payment_method_tab_1').html('');
	jQuery('#payment_method_tab_2').html('');
	jQuery('#payment_method_tab_3').html('');
	jQuery('#payment_method_tab_4').html('');
	jQuery('#payment_method_tab_5').html('');
	jQuery('#payment_method_tab_6').html('');
	jQuery('#payment_method_tab_7').html('');

	jQuery('#payment_method_tab_zone').css({display:'block'});
	
	var display_html = "";

	jQuery.ajax({
	    url: "payment_methods_xmlhttp.php?action=display_insert_payment_methods_tab&pm_id="+pmID,
	    type: 'GET',
	    dataType: 'xml',
	    timeout: 10000,
	    cache: false,
	    error: function(){
	        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
			jQuery('#payment_method_tab_zone').css({
				display:'none'
			});
	    },
	    success: function(xml){
	    	
	    	display_html = 	'<form id="submit_status_frm" name="submit_status_frm"  enctype="multipart/form-data">' +
	    					'<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">' +
	    					'	<tr>' +
	    					'		<td width="350px" class="dataTableContent" valign="top">' +
	    					'			<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">' +
							'				<tr><td class="dataTableContent">' +
							'					<fieldset>'+
							'						<legend><b>Receive Payment:</b></legend>' +
							'						<table>' +
							'							<tr>' +
							'								<td class="dataTableContent">' + 
							'									<input type="radio" name="rd_receive_payment" value="1" onclick="jQuery(\'.receive_input_field\').attr(\'disabled\', false);"> Enable' + 
							'									<input type="radio" name="rd_receive_payment" value="0" onclick="jQuery(\'.receive_input_field\').attr(\'disabled\', true);" checked> Disable' + 
							'								</td>' +
							'							</tr>' +
							'							<tr>' +
							'								<td class="dataTableContent"><b>Code</b></td>' +
							'							</tr>' +
							'							<tr>' +
							'								<td class="dataTableContent"><input type="text" id="payment_methods_code" name="payment_methods_code" value="" class="receive_input_field"></td>' +
							'							</tr>' +
							'							<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' +
							'							<tr><td class="dataTableContent"><b>Title</b></td></tr>' +
							'							<tr><td class="dataTableContent"><input type="text" name="payment_methods_title" value="" class="receive_input_field"></td></tr>' +
							'							<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' +
							'							<tr><td class="dataTableContent"><b>Display Title</b></td></tr>' +
							'							<tr><td class="dataTableContent"><input type="text" name="payment_methods_description_title" value="" class="receive_input_field"></td></tr>' +
							'							<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' +
							'							<tr><td class="dataTableContent">' + 
							'								<fieldset>'+
							'									<legend>Status:</legend>' +
							'									<input type="radio" name="payment_methods_status" id="payment_methods_status" value="1" class="receive_input_field"> Active <br>' +
							'									<input type="radio" name="payment_methods_status" id="payment_methods_status" value="-1" class="receive_input_field"> Deactive, ' +
							'									<input type="text" name="payment_methods_status_message" id="payment_methods_status_message" value="" size="25" class="receive_input_field"><br>' +
							'									<input type="radio" name="payment_methods_status" id="payment_methods_status" value="0" class="receive_input_field" checked> Disable' +
							'								</fieldset>'+
							'							</td></tr>'+
							'							<tr><td class="dataTableContent">'+
							'								<fieldset>'+
							'									<legend>Applicable for Customer Group</legend>';
			
			if (jQuery(xml).find('customers_groups').length) {
		    	jQuery(xml).find('customers_groups').each(function(){
		    		display_html += '	<input type="checkbox" value="'+jQuery(this).attr('id')+'" name="chk_customers_groups[]"';
		    		if (jQuery(this).attr('selected')=='1') {
		    			display_html += ' checked ';
		    		}
		    		display_html += ' class="receive_input_field">' + jQuery(this).text() + '<BR>';
		    	});
		    }
		    
			display_html += '								</fieldset>' +
							'							</td></tr>' +
							'							<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' +
							'							<tr><td class="dataTableContent">' +
							'								<fieldset>' +
							'									<legend>Payment Methods Type</legend>';
			jQuery(xml).find('payment_methods_type').each(function(){
				display_html += '<input type="radio" id="payment_methods_types_id" name="payment_methods_types_id" value="'+jQuery(this).attr('id')+'" class="receive_input_field">' + jQuery(this).text() + "<br/>";
			});
			
			display_html +=	'								</fieldset>' + 
							'							</td></tr>' + 
							'							<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' + 
							'						</table>' +
							'					</fieldset>' +
							'				</td></tr>' + 
							'			</table>'+
							'		</td>'+
							'		<td width="350px" class="dataTableContent" valign="top">' +
							'			<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">' +
							'				<tr><td class="dataTableContent">' +
							'					<fieldset id="field_outgoing_payment">'+
							'						<legend><b>Outgoing Payment:</b></legend>' +
							'						<table>' +
							'							<tr>' +
							'								<td class="dataTableContent">' + 
							'									<input type="radio" name="rd_outgoing_payment" value="1" onclick="jQuery(\'.outgoing_input_field\').attr(\'disabled\', false);"> Enable' + 
							'									<input type="radio" name="rd_outgoing_payment" value="0" onclick="jQuery(\'.outgoing_input_field\').attr(\'disabled\', true);" checked> Disable' + 
							'								</td>' +
							'							</tr>' +
							'							<tr><td class="dataTableContent"><b>Title</b></td></tr>' +
							'							<tr>' +
							'								<td class="dataTableContent"><input type="text" id="send_mode_name" name="send_mode_name" value="" class="outgoing_input_field"></td>' +
							'							</tr>' +
                                                        '                                                       <tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'							<tr><td class="dataTableContent"><b>Payment method code</b></td></tr>' +
							'							<tr>' +
							'								<td class="dataTableContent"><input type="text" id="payment_methods_send_code" name="payment_methods_send_code" value="" class="outgoing_input_field"></td>' +
							'							</tr>' +
							'							<tr><td class="dataTableContent">'+
							'								<fieldset id="field_outgoing_payment">'+
							'									<legend>Status</legend>'+
							'									<input type="radio" name="payment_methods_send_status" id="payment_methods_send_status" value="1" class="outgoing_input_field"> Active <br>' +
							'									<input type="radio" name="payment_methods_send_status" id="payment_methods_send_status" value="-1" class="outgoing_input_field"> Deactive, ' +
							'									<input type="text" name="payment_methods_send_status_message" id="payment_methods_send_status_message" value="" size="30" class="outgoing_input_field"><br>' +
							'									<input type="radio" name="payment_methods_send_status" id="payment_methods_send_status" value="0" checked class="outgoing_input_field"> Disable' +
							'								</fieldset>' +
							'							</td></tr>';
			display_html += '							<tr><td class="dataTableContent">'+
							'								<fieldset id="field_outgoing_payment">'+
							'									<legend>Support Mass Payment</legend>'+
							'									<input type="radio" name="payment_methods_send_mass_payment" id="payment_methods_send_mass_payment" value="1" class="outgoing_input_field" checked> Yes <br>' +
							'									<input type="radio" name="payment_methods_send_mass_payment" id="payment_methods_send_mass_payment" value="0" class="outgoing_input_field"';
			if (jQuery(xml).find('payment_methods_send_mass_payment').text() == 0) {
				display_html += ' checked ';
			}
			display_html += '> No' +
							'									</fieldset>' +
							'							</td></tr>';
			
			display_html += '							<tr><td class="dataTableContent">'+
							'								<fieldset id="field_outgoing_payment">'+
							'									<legend>Payment Methods Type (Outgoing)</legend>';
			jQuery(xml).find('payment_methods_send_type').each(function(){
				display_html += '<input type="radio" id="payment_methods_types_id" name="payment_methods_types_id" value="'+jQuery(this).attr('id')+'" class="outgoing_input_field"';
				if (jQuery(xml).find('payment_methods_types_id').text()==jQuery(this).attr('id')) {
					display_html += ' checked ';
				}
				display_html += ">";
				display_html += jQuery(this).text();
				display_html += "<br/>";

			});
			display_html += '								</fieldset>' +
							'							</td></tr>';
			display_html += '							<tr><td class="dataTableContent">'+
							'								<fieldset id="field_outgoing_payment">'+
							'								<legend>Available Sites (Outgoing)</legend>';
			jQuery(xml).find('payment_methods_send_available_site').each(function(){
				display_html += '<input type="checkbox" id="payment_methods_send_sites_'+jQuery(this).attr('id')+'" name="payment_methods_send_sites[]" value="'+jQuery(this).attr('id')+'" class="outgoing_input_field"';
				if (jQuery(xml).find('payment_methods_send_applicable_sites').text().indexOf(jQuery(this).attr('id')) >= 0) {
					display_html += ' checked ';
				}
				display_html += ">";
				display_html += jQuery(this).text();
				display_html += "<br/>";
			});
			display_html += '								</fieldset>' +
							'							</td></tr>';
			display_html +=	'							<tr><td class="dataTableContent"><b>Currency</b></td></tr>' +
							'							<tr>' +

							'								<td class="dataTableContent">' + 
							'									<select name="sel_outgoing_currency" class="outgoing_input_field">'+
							'										<option value="0">[Please Select]</option>"';
			jQuery(xml).find('currency').each(function(){
				display_html += '<option value="'+jQuery(this).attr('id')+'">' + jQuery(this).text() + "</option>";
			});
			display_html +=	'									</select>' +
							'								</td>' +
							'							</tr>' +
							'						</table>' +
							'					</fieldset>' +
							'				</td></tr>' +
							'				<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'				<tr><td class="dataTableContent"><b>Sort Order</b></td></tr>'+
							'				<tr><td class="dataTableContent">' +
							'					<input type="text" id="payment_methods_sort_order" name="payment_methods_sort_order" value="50000">'+
							'				</td></tr>'+
							'				<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>' + 
							'				<tr><td class="dataTableContent"><b>Logo</b></td></tr>' +
							'				<tr><td class="dataTableContent">'+
							'					<input type="file" id="payment_methods_logo" name="payment_methods_logo" value="">';
			if (jQuery(xml).find('payment_methods_logo').text()) {
				display_html += '<br><img id="payment_methods_display_logo" src="'+jQuery(xml).find('payment_methods_logo').text()+'" border="0" alt="" width="64" width="40">';
				display_html += "<br><input type='checkbox' value='1' name='remove_logo' id='remove_logo'> <i>Remove this payment method's logo</i>";
			} else {
				display_html += '<br><img id="payment_methods_display_logo" border="0" alt="" width="64" width="40">';	
			}
			display_html +=	'				</td></tr>'+
							'				<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'				<tr><td>'+
							'					<input type="button" value="Insert" title=" Insert " name="Insert" class="inputButton" id="btn_insert_child"/>'+
							'				</td></tr>'+
		    				'			</table>'+
		    				'		</td>'+
		    				'	</tr>'+
		    				'</table>'+
		    				'</form>';
			jQuery('#payment_method_tab_1').html(display_html);
			jQuery('.receive_input_field, .outgoing_input_field').attr('disabled', true);
			
			/* Send Payment Method */
			display_html =	'<form id="send_payment_tab_frm" name="send_payment_tab_frm"  enctype="multipart/form-data">'+
							'<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">'+
							'	<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'	<tr><td class="dataTableContent"><b>Code</b></td></tr>'+
							'	<tr><td class="dataTableContent"><input type="text" id="payment_methods_code" name="payment_methods_code" value=""></td></tr>'+
							'	<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'	<tr><td class="dataTableContent"><b>Send Mode Name</b></td></tr>'+
							'	<tr><td class="dataTableContent"><input type="text" name="send_mode_name" id="send_mode_name" value="'+jQuery(xml).find('send_mode_name').text()+'"></td></tr>'+
							'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'	<tr><td class="dataTableContent"><b>Status</b></td></tr>'+
							'	<tr><td class="dataTableContent">'+
							'		<input type="radio" name="send_status_mode" id="send_status_mode" value="1"> Active '+
							'	</td></tr>'+
							'	<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'	<tr><td class="dataTableContent">'+
							'		<input type="radio" name="send_status_mode" id="send_status_mode" value="-1"> Inactive,  '+
							'		<input type="text" name="payment_methods_status_message" id="payment_methods_status_message" value="'+jQuery(xml).find('payment_methods_send_status_message').text()+'" size="30">'+
							'	</td></tr>'+
							'	<tr><td class="dataTableContent">'+
							'		<input type="radio" name="send_status_mode" id="send_status_mode" value="0" checked> Disabled '+
							'	</td></tr>'+
							'	<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'	<tr><td><hr></td></tr>'+
							'	<tr><td class="dataTableContent"><b>Currency</b></td></tr>'+
							'	<tr><td class="dataTableContent">'+
							'		<select name="send_currency" id="send_currency">';
			jQuery(xml).find('currency').each(function(){
				display_html +=	'		<option value="'+jQuery(this).attr('id')+'">'+jQuery(this).text()+'</option>';
			});
			display_html +=	'		</select>'+
							'	</td></tr>'+
							'	<tr><td><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'	<tr><td><hr></td></tr>'+
							'	<tr>'+
		    				'		<td class="dataTableContent">'+
							'			<i>This payment method\'s setting [This payment method will use parent setting]</i>'+
			    			'		</td>'+
			    			'	</tr>'+
							'	<tr><td ><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>'+
							'	<tr><td><input type="button" value="Insert" title=" Insert " name="btn_insert_send_child" class="inputButton" id="btn_insert_send_child"/></td></tr>'+
							'</form>';
			jQuery('#payment_method_tab_4').html(display_html);
			
			jQuery('#payment_method_tab_header').html(jQuery(xml).find('display_title').text() + " - Insert New Payment Methods");
		    
		    jQuery('#btn_insert_send_child').click(function(){ 
				jquery_confirm_box("Are you sure to insert this record?", 2, 0 , "Confirm");
				jQuery('#jconfirm_submit').click(function(){
		        	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
		        	jQuery('#send_payment_tab_frm').ajaxSubmit({
						url: "payment_methods_xmlhttp.php?action=insert_send_payment_method&pm_id=" + pmID,
						dataType: 'xml',
						type: "post",
						timeout: 10000,
						cache: false,
	    				async: true,
					  	success: function(xml){
					  		jQuery.unblockUI();
					  		var message_str = '';
					  		jQuery(xml).find('message').each(function(){
					  			message_str += jQuery(this).text() + "<BR>";
					  		});
					  		if (jQuery(xml).find('success').text()==1) {
					  			window.location="payment_methods.php?action=display_new_send_child&pmID="+pmID;
					  		} else {
					  			jquery_confirm_box(message_str, 1, 1, "Error");
					  		}
					  	}
					  });
				});
			});
			jQuery('#btn_insert_child').click(function(){ 
				jquery_confirm_box("Are you sure to insert this record?", 2, 0 , "Confirm");
				jQuery('#jconfirm_submit').click(function(){      	
		        	jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
					jQuery('#submit_status_frm').ajaxSubmit({ 
						url: "payment_methods_xmlhttp.php?action=insert_payment_method&pm_id=" + pmID,
						dataType: 'xml',
						type: "post",
						timeout: 10000,
						cache: false,
	    				async: true,
					  	success: function(xml){
					  		jQuery.unblockUI();
					  		var message_str = '';
					  		jQuery(xml).find('message').each(function(){
					  			message_str += jQuery(this).text() + "<BR>";
					  		});
					  		if (jQuery(xml).find('success').text()==1) {
					  			var new_pm_id = jQuery(xml).find('new_payment_method_id').text();
				  				jQuery('#payment_methods_logo').each(function(){
									jQuery.ajaxFileUpload({
									    url:"payment_methods_xmlhttp.php?action=upload_logo&pm_id=" + new_pm_id + "&file_name=payment_methods_logo",
									    secureuri:false,
									    fileElementId:'payment_methods_logo',
									    dataType: 'xml',
									    timeout: 10000,
									    cache: false,
	    								async: true,
									    success: function(xml){
									  		window.location="payment_methods.php?action=display_new_child&pmID="+pmID;
									    },
									    error:function(xhr,err,e){ 
									        alert(e);
									    }
									});
								});
					  		} else {
					  			jquery_confirm_box(message_str, 1, 1, "Error");
					  		}
					  	},
					  	error:function(xhr,err,e){ 
					  		jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
					  	}
					});
				});
			});
		}
	});
}

function display_surcharge_tab(pmID, xml) {
	if (jQuery(xml).find('display_surcharge_tab').text()=='') return '';
	
    var display_html = "";
    var display_tab_html = "";
    var selected_currency_code = "";
    var currencies_array = new Array();
    var count_currencies_key = 0;
    var current_grps_id = 1;
    var current_fees_id = 0;
    var current_currency_code = '';
    var follow_group = 0;
    var disabled_str = '';

    // array of all currencies
    jQuery(xml).find('currency').each(function(){
        currencies_array[count_currencies_key] = jQuery(this).text();
        count_currencies_key++;
    });

    display_html = '<form id="surcharge_frm" name="surcharge_frm">';
    // Follow Parent Setting
    if (jQuery(xml).find('parent_id').text() > 0) {
        display_html += '	<table class="dataTableRow" width="100%" cellspacing="0" cellpadding="2" border="0">';
        display_html += '		<tr class="dataTableHeadingRow">';
        display_html += '			<td class="menuBoxHeading" colspan="2" align="left" nowrap="nowrap">';
        display_html += '				<input type="checkbox" name="follow_parent" id="follow_parent" ';
        if (jQuery(xml).find('payment_fees').attr('found')=="0" || jQuery(xml).find('payment_fees').attr('found')=="2") {
            display_html += ' checked';
        }
        display_html += '			>&nbsp;<b>Follow Payment Gateway</b>';
        display_html += '			<input id="btn_update_surcharge_dependency" type="button" value="Update" title=" Update " name="update" class="inputButton" />';
        display_html += '			</td>';
        display_html += '		</tr>';
        display_html += '		<tr class="dataTableHeadingRow"><td colspan="2" class="menuBoxHeading"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>';
        display_html += '	</table>';
    }

    if (jQuery(xml).find('payment_fees').attr('found')!="2") {
        display_html += '	<div id="payment_surcharge_customer_group_tab" width="100%">';
        display_html += '		<ul class="ui-tabs-nav">';
        jQuery(xml).find('customers_group').each(function(){
            display_html += '		<li id="payment_surcharge_customer_group_li_'+jQuery(this).attr('id')+'"';
            if (jQuery(this).attr('id') == current_grps_id) { display_html += ' class="ui-tabs-selected"'; }
            display_html += '><a href="#payment_surcharge_customer_group_tab_'+jQuery(this).attr('id')+'"><span onclick="load_customer_group_tab(\''+jQuery(this).attr('id')+'\')"><font color="'+jQuery(this).attr('legend_color')+'">'+jQuery(this).text()+'</font>';
            if (jQuery(this).attr('has_setting') == '1') { display_html += '&nbsp;<strong>*</strong>'; }
            display_html += '</span></a></li>';
        });
        display_html += '		</ul>';
        jQuery(xml).find('customers_group').each(function(){
            display_html += '	<div id="payment_surcharge_customer_group_tab_'+jQuery(this).attr('id')+'" style="display:none"></div>';
        });

        follow_group = jQuery(xml).find('payment_follow_customer_group').text();

        display_tab_html = '		<table border="0" width="100%" cellspacing="0" cellpadding="2">';
        display_tab_html += '			<tr class="dataTableHeadingRow">';
        display_tab_html += '				<td colspan="2">';
        display_tab_html += '					<table cellspacing="0" cellpadding="2" border="0" width="100%">';
        display_tab_html += '						<tr>';
        display_tab_html += '							<td class="menuBoxHeading" width="35%">';
        display_tab_html += '								Follow <select name="follow_grouping" id="follow_grouping">';
        display_tab_html += '									<option value="">Please select</option>';
        jQuery(xml).find('followed_group').each(function(){
            if (jQuery(this).attr('id') != current_grps_id) {
                display_tab_html += '								<option value="'+jQuery(this).attr('id')+'"';
                if (follow_group == jQuery(this).attr('id')) { display_tab_html += ' selected'; }
                display_tab_html += '>'+jQuery(this).text()+'</option>';
            }
        });
        display_tab_html += '								</select>';
        display_tab_html += '							</td>';
        display_tab_html += '							<td class="menuBoxHeading" width="65%">';
        display_tab_html += '								Copy from <select name="copy_grouping" id="copy_grouping">';
        display_tab_html += '									<option value="">Please select</option>';
        jQuery(xml).find('followed_group').each(function(){
            if (jQuery(this).attr('id') != current_grps_id) {
                display_tab_html += '								<option value="'+jQuery(this).attr('id')+'">'+jQuery(this).text()+'</option>';
            }
        });
        display_tab_html += '								</select>';
        display_tab_html += '								<input id="btn_copy_group_surcharge" type="button" value="Copy" title=" Copy " name="copy" class="inputButton" />';
        display_tab_html += '							</td>';
        display_tab_html += '						</tr>';
        display_tab_html += '					</table>';
        display_tab_html += '				</td>';
        display_tab_html += '			</tr>';
        if (follow_group == 0 || follow_group == current_grps_id) {
            display_tab_html += '			<tr class="dataTableHeadingRow">';
            display_tab_html += '				<td class="reportBoxHeading" width="5%">Currencies</td>';
            display_tab_html += '				<td class="reportBoxHeading" width="95%">&nbsp;</td>';
            display_tab_html += '			</tr>';

            jQuery(xml).find('payment_fees_setting').each(function(){
                if (jQuery(this).find('payment_fees_id').text() != '') { current_fees_id = jQuery(this).find('payment_fees_id').text(); } else { current_fees_id = 0; }
                current_currency_code = jQuery(this).attr('curr_code');
                disabled_str = '';
                if (jQuery(this).find('payment_fees_min').text() == '') {
                    disabled_str = ' disabled="disabled"';
                }

                display_tab_html += '		<tr class="dataTableHeadingRow">';
                display_tab_html += '			<td class="dataTableContent" width="5%">';
                display_tab_html += current_currency_code;
                display_tab_html += '				<input type="hidden" name="payment_methods_fees_set[]" value="'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" />';
                display_tab_html += '				<input type="hidden" id="payment_methods_currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_methods_currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+current_currency_code+'" />';
                display_tab_html += '			</td>';
                display_tab_html += '			<td class="dataTableContent" width="95%">';
                display_tab_html += '				<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">';
                selected_currency_code = jQuery(this).find('payment_fees_currency_code').text();
                display_tab_html += '					<tr class="reportListingEven">';
                display_tab_html += '						<td class="dataTableContent" width="180"><b>Surcharge calculation based on</b></td>';
                display_tab_html += '						<td class="dataTableContent">';
                display_tab_html += '							<select id="currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" onchange="change_currency(this.value,\''+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'\');">';
                for (var keys in currencies_array) {
                    display_tab_html += '						<option value="'+currencies_array[keys]+'"';
                    if (selected_currency_code == currencies_array[keys]) { display_tab_html += ' selected'; }
                    display_tab_html += '>'+currencies_array[keys]+'</option>';
                }
                display_tab_html += '							</select>';
                display_tab_html += '						</td>';
                display_tab_html += '					</tr>';
                display_tab_html += '					<tr class="reportListingEven">';
                display_tab_html += '						<td class="dataTableContent" width="180"><b>Apply to Order Amount</b></td>';
                display_tab_html += '						<td class="dataTableContent">';
                display_tab_html += '							<input type="hidden" id="payment_fees_id-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_id-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(xml).find('payment_fees_id').text()+'" />';
                display_tab_html += '							<select id="payment_fees_operator-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_operator-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">';
                selected_operator = jQuery(this).find('payment_fees_operator').text();
                display_tab_html += '							<option value="3"';
                if (selected_operator == "3") { display_tab_html += ' selected'; }
                display_tab_html += '>></option>';
                display_tab_html += '							<option value="4"';
                if (selected_operator == "4") { display_tab_html += ' selected'; }
                display_tab_html += '>>=</option>';
                display_tab_html += '							<option value="1"';
                if (selected_operator == "1") { display_tab_html += ' selected'; }
                display_tab_html += '><</option>';
                display_tab_html += '							<option value="2"';
                if (selected_operator == "2") { display_tab_html += ' selected'; }
                display_tab_html += '><=</option>';
                display_tab_html += '							</select>';
                display_tab_html += '							<span class="boldText" id="fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_min').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onchange="toggle_fees_fields(this.value,\''+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'\')">';
                display_tab_html += '						</td>';
                display_tab_html += '					</tr>';
                display_tab_html += '					<tr class="reportListingEven">';
                display_tab_html += '						<td class="dataTableContent" width="180"><b></b></td>';
                display_tab_html += '						<td class="dataTableContent">';
                display_tab_html += '						<table class="dataTableRow" width="100%" border="0" cellpadding="2" cellspacing="0">';
                display_tab_html += '							<tr class="reportListingEven">';
                display_tab_html += '								<td class="dataTableContent" width="100"><b>Transaction Fees</b></td>';
                display_tab_html += '								<td class="dataTableContent">';
                display_tab_html += '									<span class="boldText" id="fees_cost_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_value-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_value-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_value').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (Per Transaction Fee, $) + ';
                display_tab_html += '									<input type="text" id="payment_fees_cost_percent-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (Amount Based Fee, %)';
                display_tab_html += '								</td>';
                display_tab_html += '							</tr>';
                display_tab_html += '							<tr class="reportListingEven">';
                display_tab_html += '								<td class="dataTableContent" width="150"><b>Surcharge Fees</b></td>';
                display_tab_html += '								<td class="dataTableContent">';
                display_tab_html += '									<span class="boldText" id="min_fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_percent_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent_min').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (min)&nbsp;/&nbsp;';
                display_tab_html += '									<span class="boldText" id="max_fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_percent_max-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent_max-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent_max').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (max)';
                display_tab_html += '								</td>';
                display_tab_html += '							</tr>';
                display_tab_html += '						</table>';
                display_tab_html += '						</td>';
                display_tab_html += '					</tr>';
                display_tab_html += '					<tr class="reportListingEven"><td colspan="2" class="dataTableContent"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>';
                display_tab_html += '				</table>';
                display_tab_html += '			</td>';
                display_tab_html += '		</tr>';
                display_tab_html += '		<tr class="dataTableHeadingRow">';
                display_tab_html += '			<td class="reportBoxHeading" colspan="2"><img width="1" border="0" alt="" src="images/pixel_trans.gif"/></td>';
                display_tab_html += '		</tr>';
            });
        }
        display_tab_html += '		<tr class="dataTableHeadingRow">';
        display_tab_html += '			<td class="menuBoxHeading" width="5%"></td>';
        display_tab_html += '			<td class="menuBoxHeading" width="95%">';
        display_tab_html += '				<input type="hidden" id="payment_methods_id" name="payment_methods_id" value="'+jQuery(xml).find('payment_methods_id').text()+'" />';
        display_tab_html += '				<input type="hidden" id="payment_methods_customer_group_id" name="payment_methods_customer_group_id" value="'+current_grps_id+'" />';
        display_tab_html += '				<input id="btn_update_surcharge" type="button" value="Update" title=" Update " name="btn_update_surcharge" class="inputButton" />';
        display_tab_html += '			</td>';
        display_tab_html += '		</tr>';
        display_tab_html += '	</table>';
    }
    display_html += '</form>';

    jQuery('#payment_method_tab_4').html(display_html);
    jQuery('#payment_surcharge_customer_group_tab_'+current_grps_id).html(display_tab_html);
    jQuery('#payment_surcharge_customer_group_tab_'+current_grps_id).css({border:'1px solid #C9C9C9'});
    jQuery('#payment_surcharge_customer_group_tab_'+current_grps_id).css({display:'block'});

    jQuery('#btn_update_surcharge_dependency').click(function(){
        jquery_confirm_box("Are you sure to update this record?", 2, 0, "Confirm");
        jQuery('#jconfirm_submit').click(function(){
            jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
            jQuery('#surcharge_frm').ajaxSubmit({
                url: "payment_methods_xmlhttp.php?action=update_surcharge_dependency&pm_id=" + pmID,
                timeout: 10000,
                type: "POST",
                dataType: 'xml',
                cache: false,
                async: true,
                success: function(xml){
                    jQuery.unblockUI();
                    var message_str = '';
                    jQuery(xml).find('message').each(function(){
                        message_str += jQuery(this).text() + "<BR>";
                    });
                    if (jQuery(xml).find('success').text()==1) {
                        load_tab('display_surcharge_tab', pmID);
                        jquery_confirm_box(message_str, 0, 0);
                    } else {
                        jquery_confirm_box(message_str, 1, 1, "Error");
                    }
                },
                error:function(xhr,err,e){
                    jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
                }
            });
        });
    });

    jQuery('#btn_copy_group_surcharge').click(function(){
        jquery_confirm_box("Are you sure to copy over the data?", 2, 0, "Confirm");
        jQuery('#jconfirm_submit').click(function(){
            jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
            jQuery('#surcharge_frm').ajaxSubmit({
                url: "payment_methods_xmlhttp.php?action=copy_group_surcharge&pm_id="+pmID+"&cgrp_id="+current_grps_id,
                timeout: 10000,
                type: "POST",
                dataType: 'xml',
                cache: false,
                async: true,
                success: function(xml){
                    jQuery.unblockUI();
                    var message_str = '';
                    jQuery(xml).find('message').each(function(){
                        message_str += jQuery(this).text() + "<BR>";
                    });
                    if (jQuery(xml).find('success').text()==1) {
                        load_tab('display_surcharge_tab', pmID);
                        jquery_confirm_box(message_str, 0, 0);
                    } else {
                        jquery_confirm_box(message_str, 1, 1, "Error");
                    }
                },
                error:function(xhr,err,e){
                    jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
                }
            });
        });
    });

    jQuery('#btn_update_surcharge').click(function(){
        jquery_confirm_box("Are you sure to update this record?", 2, 0, "Confirm");
        jQuery('#jconfirm_submit').click(function(){
            jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
            jQuery('#surcharge_frm').ajaxSubmit({
                url: "payment_methods_xmlhttp.php?action=submit_surcharge_tab&pm_id="+pmID+"&cgrp_id="+current_grps_id,
                timeout: 10000,
                type: "POST",
                dataType: 'xml',
                cache: false,
                async: true,
                success: function(xml){
                    jQuery.unblockUI();
                    var message_str = '';
                    jQuery(xml).find('message').each(function(){
                        message_str += jQuery(this).text() + "<BR>";
                    });
                    if (jQuery(xml).find('success').text()==1) {
                        load_tab('display_surcharge_tab', pmID);
                        jquery_confirm_box(message_str, 0, 0);
                    } else {
                        jquery_confirm_box(message_str, 1, 1, "Error");
                    }
                },
                error:function(xhr,err,e){
                    jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
                }
            });
        });
    });
}

function display_sc_surcharge_tab(pmID, xml) {
	if (jQuery(xml).find('display_sc_surcharge_tab').text()=='') return '';
	
	var display_html = '';
    var display_tab_html = '';
    var selected_currency_code = '';
    var currencies_array = new Array();
    var count_currencies_key = 0;
    var current_grps_id = 1;
    var current_fees_id = 0;
    var current_currency_code = '';
    var follow_group = 0;
    var disabled_str = '';

    // array of all currencies
    jQuery(xml).find('currency').each(function(){
        currencies_array[count_currencies_key] = jQuery(this).text();
        count_currencies_key++;
    });

    display_html = '<form id="sc_surcharge_frm" name="sc_surcharge_frm">';
    // Follow Parent Setting
    if (jQuery(xml).find('parent_id').text() > 0) {
        display_html += '	<table class="dataTableRow" width="100%" cellspacing="0" cellpadding="2" border="0">';
        display_html += '		<tr class="dataTableHeadingRow">';
        display_html += '			<td class="menuBoxHeading" colspan="2" align="left" nowrap="nowrap">';
        display_html += '				<input type="checkbox" name="follow_parent" id="follow_parent" ';
        if (jQuery(xml).find('payment_fees').attr('found')=="0" || jQuery(xml).find('payment_fees').attr('found')=="2") {
            display_html += ' checked';
        }
        display_html += '			>&nbsp;<b>Follow Payment Gateway</b>';
        display_html += '			<input id="btn_update_sc_surcharge_dependency" type="button" value="Update" title=" Update " name="update" class="inputButton" />';
        display_html += '			</td>';
        display_html += '		</tr>';
        display_html += '		<tr class="dataTableHeadingRow"><td colspan="2" class="menuBoxHeading">&nbsp;</td></tr>';
        display_html += '	</table>';
    }

    if (jQuery(xml).find('payment_fees').attr('found')!="2") {
        display_html += '	<div id="payment_sc_surcharge_customer_group_tab" width="100%">';
        display_html += '		<ul class="ui-tabs-nav">';
        jQuery(xml).find('customers_group').each(function(){
            display_html += '		<li id="payment_sc_surcharge_customer_group_li_'+jQuery(this).attr('id')+'"';
            if (jQuery(this).attr('id') == current_grps_id) { display_html += ' class="ui-tabs-selected"'; }
            display_html += '><a href="#payment_sc_surcharge_customer_group_tab_'+jQuery(this).attr('id')+'"><span onclick="load_sc_customer_group_tab(\''+jQuery(this).attr('id')+'\')"><font color="'+jQuery(this).attr('legend_color')+'">'+jQuery(this).text()+'</font>';
            if (jQuery(this).attr('has_setting') == '1') { display_html += '&nbsp;<strong>*</strong>'; }
            display_html += '</span></a></li>';
        });
        display_html += '		</ul>';
        jQuery(xml).find('customers_group').each(function(){
            display_html += '	<div id="payment_sc_surcharge_customer_group_tab_'+jQuery(this).attr('id')+'" style="display:none"></div>';
        });

        follow_group = jQuery(xml).find('payment_follow_customer_group').text();

        display_tab_html = '		<table border="0" width="100%" cellspacing="0" cellpadding="2">';
        display_tab_html += '			<tr class="dataTableHeadingRow">';
        display_tab_html += '				<td colspan="2">';
        display_tab_html += '					<table cellspacing="0" cellpadding="2" border="0" width="100%">';
        display_tab_html += '						<tr>';
        display_tab_html += '							<td class="menuBoxHeading" width="35%">';
        display_tab_html += '								Follow <select name="follow_grouping" id="follow_grouping">';
        display_tab_html += '									<option value="">Please select</option>';
        jQuery(xml).find('followed_group').each(function(){
            if (jQuery(this).attr('id') != current_grps_id) {
                display_tab_html += '								<option value="'+jQuery(this).attr('id')+'"';
                if (follow_group == jQuery(this).attr('id')) { display_tab_html += ' selected'; }
                display_tab_html += '>'+jQuery(this).text()+'</option>';
            }
        });
        display_tab_html += '								</select>';
        display_tab_html += '							</td>';
        display_tab_html += '							<td class="menuBoxHeading" width="65%">';
        display_tab_html += '								Copy from <select name="copy_grouping" id="copy_grouping">';
        display_tab_html += '									<option value="">Please select</option>';
        jQuery(xml).find('followed_group').each(function(){
            if (jQuery(this).attr('id') != current_grps_id) {
                display_tab_html += '								<option value="'+jQuery(this).attr('id')+'">'+jQuery(this).text()+'</option>';
            }
        });
        display_tab_html += '								</select>';
        display_tab_html += '								<input id="btn_copy_group_sc_surcharge" type="button" value="Copy" title=" Copy " name="copy" class="inputButton" />';
        display_tab_html += '							</td>';
        display_tab_html += '						</tr>';
        display_tab_html += '					</table>';
        display_tab_html += '				</td>';
        display_tab_html += '			</tr>';
        if (follow_group == 0 || follow_group == current_grps_id) {
            display_tab_html += '			<tr class="dataTableHeadingRow">';
            display_tab_html += '				<td class="reportBoxHeading" width="5%">Currencies</td>';
            display_tab_html += '				<td class="reportBoxHeading" width="95%">&nbsp;</td>';
            display_tab_html += '			</tr>';

            jQuery(xml).find('payment_fees_setting').each(function(){
                if (jQuery(this).find('payment_fees_id').text() != '') { current_fees_id = jQuery(this).find('payment_fees_id').text(); } else { current_fees_id = 0; }
                current_currency_code = jQuery(this).attr('curr_code');
                disabled_str = '';
                if (jQuery(this).find('payment_fees_min').text() == '') {
                    disabled_str = ' disabled="disabled"';
                }

                display_tab_html += '		<tr class="dataTableHeadingRow">';
                display_tab_html += '			<td class="dataTableContent" width="5%">';
                display_tab_html += current_currency_code;
                display_tab_html += '				<input type="hidden" name="payment_methods_fees_set[]" value="'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" />';
                display_tab_html += '				<input type="hidden" id="payment_methods_currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_methods_currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+current_currency_code+'" />';
                display_tab_html += '			</td>';
                display_tab_html += '			<td class="dataTableContent" width="95%">';
                display_tab_html += '				<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">';
                selected_currency_code = jQuery(this).find('payment_fees_currency_code').text();
                display_tab_html += '					<tr class="reportListingEven">';
                display_tab_html += '						<td class="dataTableContent" width="180"><b>Surcharge calculation based on</b></td>';
                display_tab_html += '						<td class="dataTableContent">';
                display_tab_html += '							<select id="currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" onchange="change_currency(this.value,\''+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'\');">';
                for (var keys in currencies_array) {
                    display_tab_html += '						<option value="'+currencies_array[keys]+'"';
                    if (selected_currency_code == currencies_array[keys]) { display_tab_html += ' selected'; }
                    display_tab_html += '>'+currencies_array[keys]+'</option>';
                }
                display_tab_html += '							</select>';
                display_tab_html += '						</td>';
                display_tab_html += '					</tr>';
                display_tab_html += '					<tr class="reportListingEven">';
                display_tab_html += '						<td class="dataTableContent" width="180"><b>Apply to Order Amount</b></td>';
                display_tab_html += '						<td class="dataTableContent">';
                display_tab_html += '							<input type="hidden" id="payment_fees_id-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_id-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(xml).find('payment_fees_id').text()+'" />';
                display_tab_html += '							<select id="payment_fees_operator-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_operator-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">';
                selected_operator = jQuery(this).find('payment_fees_operator').text();
                display_tab_html += '							<option value="3"';
                if (selected_operator == "3") { display_tab_html += ' selected'; }
                display_tab_html += '>></option>';
                display_tab_html += '							<option value="4"';
                if (selected_operator == "4") { display_tab_html += ' selected'; }
                display_tab_html += '>>=</option>';
                display_tab_html += '							<option value="1"';
                if (selected_operator == "1") { display_tab_html += ' selected'; }
                display_tab_html += '><</option>';
                display_tab_html += '							<option value="2"';
                if (selected_operator == "2") { display_tab_html += ' selected'; }
                display_tab_html += '><=</option>';
                display_tab_html += '							</select>';
                display_tab_html += '							<span class="boldText" id="fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_min').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onchange="toggle_fees_fields(this.value,\''+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'\')">';
                display_tab_html += '						</td>';
                display_tab_html += '					</tr>';
                display_tab_html += '					<tr class="reportListingEven">';
                display_tab_html += '						<td class="dataTableContent" width="180"><b></b></td>';
                display_tab_html += '						<td class="dataTableContent">';
                display_tab_html += '						<table class="dataTableRow" width="100%" border="0" cellpadding="2" cellspacing="0">';
                display_tab_html += '							<tr class="reportListingEven">';
                display_tab_html += '								<td class="dataTableContent" width="100"><b>Transaction Fees</b></td>';
                display_tab_html += '								<td class="dataTableContent">';
                display_tab_html += '									<span class="boldText" id="fees_cost_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_value-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_value-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_value').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (Per Transaction Fee, $) + ';
                display_tab_html += '									<input type="text" id="payment_fees_cost_percent-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (Amount Based Fee, %)';
                display_tab_html += '								</td>';
                display_tab_html += '							</tr>';
                display_tab_html += '							<tr class="reportListingEven">';
                display_tab_html += '								<td class="dataTableContent" width="150"><b>Surcharge Fees</b></td>';
                display_tab_html += '								<td class="dataTableContent">';
                display_tab_html += '									<span class="boldText" id="min_fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_percent_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent_min').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (min)&nbsp;/&nbsp;';
                display_tab_html += '									<span class="boldText" id="max_fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_percent_max-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent_max-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent_max').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (max)';
                display_tab_html += '								</td>';
                display_tab_html += '							</tr>';
                display_tab_html += '						</table>';
                display_tab_html += '						</td>';
                display_tab_html += '					</tr>';
                display_tab_html += '					<tr class="reportListingEven"><td colspan="2" class="dataTableContent"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>';
                display_tab_html += '				</table>';
                display_tab_html += '			</td>';
                display_tab_html += '		</tr>';
                display_tab_html += '		<tr class="dataTableHeadingRow">';
                display_tab_html += '			<td class="reportBoxHeading" colspan="2">&nbsp;</td>';
                display_tab_html += '		</tr>';
            });
        }
        display_tab_html += '		<tr class="dataTableHeadingRow">';
        display_tab_html += '			<td class="menuBoxHeading" width="5%"></td>';
        display_tab_html += '			<td class="menuBoxHeading" width="95%">';
        display_tab_html += '				<input type="hidden" id="payment_methods_id" name="payment_methods_id" value="'+jQuery(xml).find('payment_methods_id').text()+'" />';
        display_tab_html += '				<input type="hidden" id="payment_methods_customer_group_id" name="payment_methods_customer_group_id" value="'+current_grps_id+'" />';
        display_tab_html += '				<input id="btn_update_sc_surcharge" type="button" value="Update" title=" Update " name="btn_update_sc_surcharge" class="inputButton" />';
        display_tab_html += '			</td>';
        display_tab_html += '		</tr>';
        display_tab_html += '	</table>';
    }
    display_html += '</form>';

    jQuery('#payment_method_tab_8').html(display_html);
    jQuery('#payment_sc_surcharge_customer_group_tab_'+current_grps_id).html(display_tab_html);
    jQuery('#payment_sc_surcharge_customer_group_tab_'+current_grps_id).css({border:'1px solid #C9C9C9'});
    jQuery('#payment_sc_surcharge_customer_group_tab_'+current_grps_id).css({display:'block'});

    jQuery('#btn_update_sc_surcharge_dependency').click(function(){
        jquery_confirm_box("Are you sure to update this record?", 2, 0, "Confirm");
        jQuery('#jconfirm_submit').click(function(){
            jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
            jQuery('#sc_surcharge_frm').ajaxSubmit({
                url: "payment_methods_xmlhttp.php?action=update_sc_surcharge_dependency&pm_id=" + pmID,
                timeout: 10000,
                type: "POST",
                dataType: 'xml',
                cache: false,
                async: true,
                success: function(xml){
                    jQuery.unblockUI();
                    var message_str = '';
                    jQuery(xml).find('message').each(function(){
                        message_str += jQuery(this).text() + "<BR>";
                    });
                    if (jQuery(xml).find('success').text()==1) {
                        load_tab('display_sc_surcharge_tab', pmID);
                        jquery_confirm_box(message_str, 0, 0);
                    } else {
                        jquery_confirm_box(message_str, 1, 1, "Error");
                    }
                },
                error:function(xhr,err,e){
                    jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
                }
            });
        });
    });

    jQuery('#btn_copy_group_sc_surcharge').click(function(){
        jquery_confirm_box("Are you sure to copy over the data?", 2, 0, "Confirm");
        jQuery('#jconfirm_submit').click(function(){
            jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
            jQuery('#sc_surcharge_frm').ajaxSubmit({
                url: "payment_methods_xmlhttp.php?action=copy_group_sc_surcharge&pm_id="+pmID+"&cgrp_id="+current_grps_id,
                timeout: 10000,
                type: "POST",
                dataType: 'xml',
                cache: false,
                async: true,
                success: function(xml){
                    jQuery.unblockUI();
                    var message_str = '';
                    jQuery(xml).find('message').each(function(){
                        message_str += jQuery(this).text() + "<BR>";
                    });
                    if (jQuery(xml).find('success').text()==1) {
                        load_tab('display_sc_surcharge_tab', pmID);
                        jquery_confirm_box(message_str, 0, 0);
                    } else {
                        jquery_confirm_box(message_str, 1, 1, "Error");
                    }
                },
                error:function(xhr,err,e){
                    jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
                }
            });
        });
    });

    jQuery('#btn_update_sc_surcharge').click(function(){
        jquery_confirm_box("Are you sure to update this record?", 2, 0, "Confirm");
        jQuery('#jconfirm_submit').click(function(){
            jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
            jQuery('#sc_surcharge_frm').ajaxSubmit({
                url: "payment_methods_xmlhttp.php?action=submit_sc_surcharge_tab&pm_id="+pmID+"&cgrp_id="+current_grps_id,
                timeout: 10000,
                type: "POST",
                dataType: 'xml',
                cache: false,
                async: true,
                success: function(xml){
                    jQuery.unblockUI();
                    var message_str = '';
                    jQuery(xml).find('message').each(function(){
                        message_str += jQuery(this).text() + "<BR>";
                    });
                    if (jQuery(xml).find('success').text()==1) {
                        load_tab('display_sc_surcharge_tab', pmID);
                        jquery_confirm_box(message_str, 0, 0);
                    } else {
                        jquery_confirm_box(message_str, 1, 1, "Error");
                    }
                },
                error:function(xhr,err,e){
                    jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
                }
            });
        });
    });
}

function display_surcharge_customer_group_tab(pmID, cgrpID, xml) {
	if (jQuery(xml).find('display_surcharge_tab').text()=="") return "";
	
	var li_html = "";
	var display_tab_html = "";
	var selected_currency_code = "";
	var currencies_array = new Array();
	var count_currencies_key = 0;
	var current_grps_id = cgrpID;
	var current_fees_id = 0;
	var current_currency_code = '';
	var follow_group = 0;
	var disabled_str = '';
	
	// array of all currencies
	jQuery(xml).find('currency').each(function(){
		currencies_array[count_currencies_key] = jQuery(this).text();
		count_currencies_key++;
	});
	
	// set all customer group UI presentation accordingly
	jQuery(xml).find('customers_group').each(function(){
		jQuery('#payment_surcharge_customer_group_li_'+jQuery(this).attr('id')).css({display:'block'});
		if (jQuery(this).attr('id') == cgrpID) {
			jQuery('#payment_surcharge_customer_group_li_'+jQuery(this).attr('id')).attr({class:'ui-tabs-selected'});
			li_html = '<a href="#payment_surcharge_customer_group_tab_'+jQuery(this).attr('id')+'"><span onclick="load_customer_group_tab(\''+jQuery(this).attr('id')+'\')"><font color="'+jQuery(this).attr('legend_color')+'">'+jQuery(this).text()+'</font>';
			if (jQuery(this).attr('has_setting') == '1') { li_html += '&nbsp;<strong>*</strong>'; }
			li_html += '</span></a></li>';
			jQuery('#payment_surcharge_customer_group_li_'+jQuery(this).attr('id')).html(li_html);
		} else {
			jQuery('#payment_surcharge_customer_group_li_'+jQuery(this).attr('id')).attr({class:''});
		}
		jQuery('#payment_surcharge_customer_group_tab_'+jQuery(this).attr('id')).html('');
		jQuery("#payment_surcharge_customer_group_tab_"+jQuery(this).attr('id')).css({border:'1px solid #C9C9C9'});
		if (jQuery(this).attr('id') == cgrpID) {
			jQuery('#payment_surcharge_customer_group_tab_'+jQuery(this).attr('id')).css({display:'block'});
		} else {
			jQuery('#payment_surcharge_customer_group_tab_'+jQuery(this).attr('id')).css({display:'none'});
		}
	});
	
	follow_group = jQuery(xml).find('payment_follow_customer_group').text();
	
	display_tab_html = '		<table border="0" width="100%" cellspacing="0" cellpadding="2">';
	display_tab_html += '			<tr class="dataTableHeadingRow">';
	display_tab_html += '				<td colspan="2">';
	display_tab_html += '					<table cellspacing="0" cellpadding="2" border="0" width="100%">';
	display_tab_html += '						<tr>';
	display_tab_html += '							<td class="menuBoxHeading" width="35%">';
	display_tab_html += '								Follow <select name="follow_grouping" id="follow_grouping">';
	display_tab_html += '									<option value="">Please select</option>';
	jQuery(xml).find('followed_group').each(function(){
		if (jQuery(this).attr('id') != cgrpID) {
			display_tab_html += '									<option value="'+jQuery(this).attr('id')+'"';
			if (follow_group == jQuery(this).attr('id')) { display_tab_html += ' selected'; }
			display_tab_html += '>'+jQuery(this).text()+'</option>';
		}
	});
	display_tab_html += '								</select>';
	display_tab_html += '							</td>';
	display_tab_html += '							<td class="menuBoxHeading" width="65%">';
	display_tab_html += '								Copy from <select name="copy_grouping" id="copy_grouping">';
	display_tab_html += '									<option value="">Please select</option>';
	jQuery(xml).find('followed_group').each(function(){
		if (jQuery(this).attr('id') != cgrpID) {
			display_tab_html += '									<option value="'+jQuery(this).attr('id')+'">'+jQuery(this).text()+'</option>';
		}
	});
	display_tab_html += '								</select>';
	display_tab_html += '								<input id="btn_copy_group_surcharge" type="button" value="Copy" title=" Copy " name="copy" class="inputButton" />';
	display_tab_html += '							</td>';
	display_tab_html += '						</tr>';
	display_tab_html += '					</table>';
	display_tab_html += '				</td>';
	display_tab_html += '			</tr>';
	
	if (follow_group == 0 || follow_group == cgrpID) {
		display_tab_html += '			<tr class="dataTableHeadingRow">';
		display_tab_html += '				<td class="reportBoxHeading" width="5%">Currencies</td>';
		display_tab_html += '				<td class="reportBoxHeading" width="95%">&nbsp;</td>';
		display_tab_html += '			</tr>';
		
		jQuery(xml).find('payment_fees_setting').each(function(){
			if (jQuery(this).find('payment_fees_id').text() != '') { current_fees_id = jQuery(this).find('payment_fees_id').text(); } else { current_fees_id = 0; }
			current_currency_code = jQuery(this).attr('curr_code');
			disabled_str = '';
			if (jQuery(this).find('payment_fees_min').text() == '') {
				disabled_str = ' disabled="disabled"';
			}
			
			display_tab_html += '		<tr class="dataTableHeadingRow">';
			display_tab_html += '			<td class="dataTableContent" width="5%">';
			display_tab_html += current_currency_code;
			display_tab_html += '				<input type="hidden" name="payment_methods_fees_set[]" value="'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" />';
			display_tab_html += '				<input type="hidden" id="payment_methods_currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_methods_currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+current_currency_code+'" />';
			display_tab_html += '			</td>';
			display_tab_html += '			<td class="dataTableContent" width="95%">';
			display_tab_html += '				<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">';
			selected_currency_code = jQuery(this).find('payment_fees_currency_code').text();
			display_tab_html += '					<tr class="reportListingEven">';
			display_tab_html += '						<td class="dataTableContent" width="180"><b>Surcharge calculation based on</b></td>';
			display_tab_html += '						<td class="dataTableContent">';
			display_tab_html += '							<select id="currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" onchange="change_currency(this.value,\''+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'\');">';
			for (var keys in currencies_array) {
				display_tab_html += '						<option value="'+currencies_array[keys]+'"';
				if (selected_currency_code == currencies_array[keys]) { display_tab_html += ' selected'; }
				display_tab_html += '>'+currencies_array[keys]+'</option>';
			}
			display_tab_html += '							</select>';
			display_tab_html += '						</td>';
			display_tab_html += '					</tr>';
			display_tab_html += '					<tr class="reportListingEven">';
			display_tab_html += '						<td class="dataTableContent" width="180"><b>Apply to Order Amount</b></td>';
			display_tab_html += '						<td class="dataTableContent">';
			display_tab_html += '							<input type="hidden" id="payment_fees_id-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_id-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(xml).find('payment_fees_id').text()+'" />';
			display_tab_html += '							<select id="payment_fees_operator-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_operator-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">';
			selected_operator = jQuery(this).find('payment_fees_operator').text();
			display_tab_html += '							<option value="3"';
			if (selected_operator == "3") { display_tab_html += ' selected'; }
			display_tab_html += '>></option>';
			display_tab_html += '							<option value="4"';
			if (selected_operator == "4") { display_tab_html += ' selected'; }
			display_tab_html += '>>=</option>';
			display_tab_html += '							<option value="1"';
			if (selected_operator == "1") { display_tab_html += ' selected'; }
			display_tab_html += '><</option>';
			display_tab_html += '							<option value="2"';
			if (selected_operator == "2") { display_tab_html += ' selected'; }
			display_tab_html += '><=</option>';
			display_tab_html += '							</select>';
			display_tab_html += '							<span class="boldText" id="fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_min').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onchange="toggle_fees_fields(this.value,\''+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'\')">';
			display_tab_html += '						</td>';
			display_tab_html += '					</tr>';
			display_tab_html += '					<tr class="reportListingEven">';
			display_tab_html += '						<td class="dataTableContent" width="180"><b></b></td>';
			display_tab_html += '						<td class="dataTableContent">';
			display_tab_html += '						<table class="dataTableRow" width="100%" border="0" cellpadding="2" cellspacing="0">';
			display_tab_html += '							<tr class="reportListingEven">';
			display_tab_html += '								<td class="dataTableContent" width="100"><b>Transaction Fees</b></td>';
			display_tab_html += '								<td class="dataTableContent">';
			display_tab_html += '									<span class="boldText" id="fees_cost_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_value-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_value-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_value').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (Per Transaction Fee, $) + ';
			display_tab_html += '									<input type="text" id="payment_fees_cost_percent-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (Amount Based Fee, %)';
			display_tab_html += '								</td>';
			display_tab_html += '							</tr>';
			display_tab_html += '							<tr class="reportListingEven">';
			display_tab_html += '								<td class="dataTableContent" width="150"><b>Surcharge Fees</b></td>';
			display_tab_html += '								<td class="dataTableContent">';
			display_tab_html += '									<span class="boldText" id="min_fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_percent_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent_min').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (min)&nbsp;/&nbsp;';
			display_tab_html += '									<span class="boldText" id="max_fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_percent_max-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent_max-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent_max').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (max)';
			display_tab_html += '								</td>';
			display_tab_html += '							</tr>';
			display_tab_html += '						</table>';
			display_tab_html += '						</td>';
			display_tab_html += '					</tr>';
			display_tab_html += '					<tr class="reportListingEven"><td colspan="2" class="dataTableContent"><img src="images/pixel_trans.gif" border="0" alt="" width="1"></td></tr>';
			display_tab_html += '				</table>';
			display_tab_html += '			</td>';
			display_tab_html += '		</tr>';
			display_tab_html += '		<tr class="dataTableHeadingRow">';
			display_tab_html += '			<td class="reportBoxHeading" colspan="2"><img width="1" border="0" alt="" src="images/pixel_trans.gif"/></td>';
			display_tab_html += '		</tr>';
		});
	}
	
	display_tab_html += '		<tr class="dataTableHeadingRow">';
	display_tab_html += '			<td class="menuBoxHeading" width="5%"></td>';
	display_tab_html += '			<td class="menuBoxHeading" width="95%">';
	display_tab_html += '				<input type="hidden" id="payment_methods_id" name="payment_methods_id" value="'+jQuery(xml).find('payment_methods_id').text()+'" />';
	display_tab_html += '				<input type="hidden" id="payment_methods_customer_group_id" name="payment_methods_customer_group_id" value="'+current_grps_id+'" />';
	display_tab_html += '				<input id="btn_update_surcharge" type="button" value="Update" title=" Update " name="btn_update_surcharge" class="inputButton" />';
	display_tab_html += '			</td>';
	display_tab_html += '		</tr>';
	display_tab_html += '	</table>';
	
	jQuery('#payment_surcharge_customer_group_tab_'+cgrpID).html(display_tab_html);
	
	jQuery('#btn_update_surcharge').click(function(){ 
		jquery_confirm_box("Are you sure to update this record?", 2, 0, "Confirm");
		jQuery('#jconfirm_submit').click(function(){ 		        	
			jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
			jQuery('#surcharge_frm').ajaxSubmit({ 
				url: "payment_methods_xmlhttp.php?action=submit_surcharge_tab&pm_id=" + pmID + "&cgrp_id=" + cgrpID,
				timeout: 10000,
				type: "POST",
				dataType: 'xml',
				cache: false,
				async: true,
				success: function(xml){
					jQuery.unblockUI();
					var message_str = '';
					jQuery(xml).find('message').each(function(){
						message_str += jQuery(this).text() + "<BR>";
					});
					if (jQuery(xml).find('success').text()==1) {
						load_customer_group_tab(cgrpID, pmID);
						jquery_confirm_box(message_str, 0, 0);
					} else {
						jquery_confirm_box(message_str, 1, 1, "Error");
					}
				},
				error:function(xhr,err,e){ 
					jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
				}
			});
		});
	});
	
	jQuery('#btn_copy_group_surcharge').click(function(){
		jquery_confirm_box("Are you sure to copy over the data?", 2, 0, "Confirm");
		jQuery('#jconfirm_submit').click(function(){
			jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
			jQuery('#surcharge_frm').ajaxSubmit({
				url: "payment_methods_xmlhttp.php?action=copy_group_surcharge&pm_id="+pmID+"&cgrp_id="+cgrpID,
				timeout: 10000,
				type: "POST",
				dataType: 'xml',
				cache: false,
				async: true,
				success: function(xml){
					jQuery.unblockUI();
					var message_str = '';
					jQuery(xml).find('message').each(function(){
						message_str += jQuery(this).text() + "<BR>";
					});
					if (jQuery(xml).find('success').text()==1) {
						load_customer_group_tab(cgrpID, pmID);
						jquery_confirm_box(message_str, 0, 0);
					} else {
						jquery_confirm_box(message_str, 1, 1, "Error");
					}
				},
				error:function(xhr,err,e){
					jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
				}
			});
		});
	});
}

function display_sc_surcharge_customer_group_tab(pmID, cgrpID, xml) {
	if (jQuery(xml).find('display_sc_surcharge_tab').text()=='') return '';
	
	var li_html = '';
	var display_tab_html = '';
	var selected_currency_code = '';
	var currencies_array = new Array();
	var count_currencies_key = 0;
	var current_grps_id = cgrpID;
	var current_fees_id = 0;
	var current_currency_code = '';
	var follow_group = 0;
	var disabled_str = '';
	
	// array of all currencies
	jQuery(xml).find('currency').each(function(){
		currencies_array[count_currencies_key] = jQuery(this).text();
		count_currencies_key++;
	});
	
	// set all customer group UI presentation accordingly
	jQuery(xml).find('customers_group').each(function(){
		jQuery('#payment_sc_surcharge_customer_group_li_'+jQuery(this).attr('id')).css({display:'block'});
		if (jQuery(this).attr('id') == cgrpID) {
			jQuery('#payment_sc_surcharge_customer_group_li_'+jQuery(this).attr('id')).attr({class:'ui-tabs-selected'});
			li_html = '<a href="#payment_sc_surcharge_customer_group_tab_'+jQuery(this).attr('id')+'"><span onclick="load_sc_customer_group_tab(\''+jQuery(this).attr('id')+'\')"><font color="'+jQuery(this).attr('legend_color')+'">'+jQuery(this).text()+'</font>';
			if (jQuery(this).attr('has_setting') == '1') { li_html += '&nbsp;<strong>*</strong>'; }
			li_html += '</span></a></li>';
			jQuery('#payment_sc_surcharge_customer_group_li_'+jQuery(this).attr('id')).html(li_html);
		} else {
			jQuery('#payment_sc_surcharge_customer_group_li_'+jQuery(this).attr('id')).attr({class:''});
		}
		jQuery('#payment_sc_surcharge_customer_group_tab_'+jQuery(this).attr('id')).html('');
		jQuery("#payment_sc_surcharge_customer_group_tab_"+jQuery(this).attr('id')).css({border:'1px solid #C9C9C9'});
		if (jQuery(this).attr('id') == cgrpID) {
			jQuery('#payment_sc_surcharge_customer_group_tab_'+jQuery(this).attr('id')).css({display:'block'});
		} else {
			jQuery('#payment_sc_surcharge_customer_group_tab_'+jQuery(this).attr('id')).css({display:'none'});
		}
	});
	
	follow_group = jQuery(xml).find('payment_follow_customer_group').text();
	
	display_tab_html = '		<table border="0" width="100%" cellspacing="0" cellpadding="2">';
	display_tab_html += '			<tr class="dataTableHeadingRow">';
	display_tab_html += '				<td colspan="2">';
	display_tab_html += '					<table cellspacing="0" cellpadding="2" border="0" width="100%">';
	display_tab_html += '						<tr>';
	display_tab_html += '							<td class="menuBoxHeading" width="35%">';
	display_tab_html += '								Follow <select name="follow_grouping" id="follow_grouping">';
	display_tab_html += '									<option value="">Please select</option>';
	jQuery(xml).find('followed_group').each(function(){
		if (jQuery(this).attr('id') != cgrpID) {
			display_tab_html += '									<option value="'+jQuery(this).attr('id')+'"';
			if (follow_group == jQuery(this).attr('id')) { display_tab_html += ' selected'; }
			display_tab_html += '>'+jQuery(this).text()+'</option>';
		}
	});
	display_tab_html += '								</select>';
	display_tab_html += '							</td>';
	display_tab_html += '							<td class="menuBoxHeading" width="65%">';
	display_tab_html += '								Copy from <select name="copy_grouping" id="copy_grouping">';
	display_tab_html += '									<option value="">Please select</option>';
	jQuery(xml).find('followed_group').each(function(){
		if (jQuery(this).attr('id') != cgrpID) {
			display_tab_html += '									<option value="'+jQuery(this).attr('id')+'">'+jQuery(this).text()+'</option>';
		}
	});
	display_tab_html += '								</select>';
	display_tab_html += '								<input id="btn_copy_group_sc_surcharge" type="button" value="Copy" title=" Copy " name="copy" class="inputButton" />';
	display_tab_html += '							</td>';
	display_tab_html += '						</tr>';
	display_tab_html += '					</table>';
	display_tab_html += '				</td>';
	display_tab_html += '			</tr>';
	
	if (follow_group == 0 || follow_group == cgrpID) {
		display_tab_html += '			<tr class="dataTableHeadingRow">';
		display_tab_html += '				<td class="reportBoxHeading" width="5%">Currencies</td>';
		display_tab_html += '				<td class="reportBoxHeading" width="95%">&nbsp;</td>';
		display_tab_html += '			</tr>';
		
		jQuery(xml).find('payment_fees_setting').each(function(){
			if (jQuery(this).find('payment_fees_id').text() != '') { current_fees_id = jQuery(this).find('payment_fees_id').text(); } else { current_fees_id = 0; }
			current_currency_code = jQuery(this).attr('curr_code');
			disabled_str = '';
			if (jQuery(this).find('payment_fees_min').text() == '') {
				disabled_str = ' disabled="disabled"';
			}
			
			display_tab_html += '		<tr class="dataTableHeadingRow">';
			display_tab_html += '			<td class="dataTableContent" width="5%">';
			display_tab_html += current_currency_code;
			display_tab_html += '				<input type="hidden" name="payment_methods_fees_set[]" value="'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" />';
			display_tab_html += '				<input type="hidden" id="payment_methods_currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_methods_currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+current_currency_code+'" />';
			display_tab_html += '			</td>';
			display_tab_html += '			<td class="dataTableContent" width="95%">';
			display_tab_html += '				<table border="0" width="100%" cellspacing="0" cellpadding="2" class="dataTableRow">';
			selected_currency_code = jQuery(this).find('payment_fees_currency_code').text();
			display_tab_html += '					<tr class="reportListingEven">';
			display_tab_html += '						<td class="dataTableContent" width="180"><b>Surcharge calculation based on</b></td>';
			display_tab_html += '						<td class="dataTableContent">';
			display_tab_html += '							<select id="currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="currency_code-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" onchange="change_currency(this.value,\''+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'\');">';
			for (var keys in currencies_array) {
				display_tab_html += '						<option value="'+currencies_array[keys]+'"';
				if (selected_currency_code == currencies_array[keys]) { display_tab_html += ' selected'; }
				display_tab_html += '>'+currencies_array[keys]+'</option>';
			}
			display_tab_html += '							</select>';
			display_tab_html += '						</td>';
			display_tab_html += '					</tr>';
			display_tab_html += '					<tr class="reportListingEven">';
			display_tab_html += '						<td class="dataTableContent" width="180"><b>Apply to Order Amount</b></td>';
			display_tab_html += '						<td class="dataTableContent">';
			display_tab_html += '							<input type="hidden" id="payment_fees_id-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_id-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(xml).find('payment_fees_id').text()+'" />';
			display_tab_html += '							<select id="payment_fees_operator-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_operator-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">';
			selected_operator = jQuery(this).find('payment_fees_operator').text();
			display_tab_html += '							<option value="3"';
			if (selected_operator == "3") { display_tab_html += ' selected'; }
			display_tab_html += '>></option>';
			display_tab_html += '							<option value="4"';
			if (selected_operator == "4") { display_tab_html += ' selected'; }
			display_tab_html += '>>=</option>';
			display_tab_html += '							<option value="1"';
			if (selected_operator == "1") { display_tab_html += ' selected'; }
			display_tab_html += '><</option>';
			display_tab_html += '							<option value="2"';
			if (selected_operator == "2") { display_tab_html += ' selected'; }
			display_tab_html += '><=</option>';
			display_tab_html += '							</select>';
			display_tab_html += '							<span class="boldText" id="fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_min').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onchange="toggle_fees_fields(this.value,\''+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'\')">';
			display_tab_html += '						</td>';
			display_tab_html += '					</tr>';
			display_tab_html += '					<tr class="reportListingEven">';
			display_tab_html += '						<td class="dataTableContent" width="180"><b></b></td>';
			display_tab_html += '						<td class="dataTableContent">';
			display_tab_html += '						<table class="dataTableRow" width="100%" border="0" cellpadding="2" cellspacing="0">';
			display_tab_html += '							<tr class="reportListingEven">';
			display_tab_html += '								<td class="dataTableContent" width="100"><b>Transaction Fees</b></td>';
			display_tab_html += '								<td class="dataTableContent">';
			display_tab_html += '									<span class="boldText" id="fees_cost_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_value-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_value-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_value').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (Per Transaction Fee, $) + ';
			display_tab_html += '									<input type="text" id="payment_fees_cost_percent-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (Amount Based Fee, %)';
			display_tab_html += '								</td>';
			display_tab_html += '							</tr>';
			display_tab_html += '							<tr class="reportListingEven">';
			display_tab_html += '								<td class="dataTableContent" width="150"><b>Surcharge Fees</b></td>';
			display_tab_html += '								<td class="dataTableContent">';
			display_tab_html += '									<span class="boldText" id="min_fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_percent_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent_min-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent_min').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (min)&nbsp;/&nbsp;';
			display_tab_html += '									<span class="boldText" id="max_fees_curr-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'">'+selected_currency_code+'</span>&nbsp;<input type="text" id="payment_fees_cost_percent_max-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" name="payment_fees_cost_percent_max-'+current_grps_id+'-'+current_currency_code+'-'+current_fees_id+'" value="'+jQuery(this).find('payment_fees_cost_percent_max').text()+'" size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"'+disabled_str+'> (max)';
			display_tab_html += '								</td>';
			display_tab_html += '							</tr>';
			display_tab_html += '						</table>';
			display_tab_html += '						</td>';
			display_tab_html += '					</tr>';
			display_tab_html += '					<tr class="reportListingEven"><td colspan="2" class="dataTableContent">&nbsp;</td></tr>';
			display_tab_html += '				</table>';
			display_tab_html += '			</td>';
			display_tab_html += '		</tr>';
			display_tab_html += '		<tr class="dataTableHeadingRow">';
			display_tab_html += '			<td class="reportBoxHeading" colspan="2">&nbsp;</td>';
			display_tab_html += '		</tr>';
		});
	}
	
	display_tab_html += '		<tr class="dataTableHeadingRow">';
	display_tab_html += '			<td class="menuBoxHeading" width="5%"></td>';
	display_tab_html += '			<td class="menuBoxHeading" width="95%">';
	display_tab_html += '				<input type="hidden" id="payment_methods_id" name="payment_methods_id" value="'+jQuery(xml).find('payment_methods_id').text()+'" />';
	display_tab_html += '				<input type="hidden" id="payment_methods_customer_group_id" name="payment_methods_customer_group_id" value="'+current_grps_id+'" />';
	display_tab_html += '				<input id="btn_update_sc_surcharge" type="button" value="Update" title=" Update " name="btn_update_sc_surcharge" class="inputButton" />';
	display_tab_html += '			</td>';
	display_tab_html += '		</tr>';
	display_tab_html += '	</table>';
	
	jQuery('#payment_sc_surcharge_customer_group_tab_'+cgrpID).html(display_tab_html);
	
	jQuery('#btn_update_sc_surcharge').click(function(){
		jquery_confirm_box("Are you sure to update this record?", 2, 0, "Confirm");
		jQuery('#jconfirm_submit').click(function(){ 		        	
			jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
			jQuery('#sc_surcharge_frm').ajaxSubmit({ 
				url: "payment_methods_xmlhttp.php?action=submit_sc_surcharge_tab&pm_id=" + pmID + "&cgrp_id=" + cgrpID,
				timeout: 10000,
				type: "POST",
				dataType: 'xml',
				cache: false,
				async: true,
				success: function(xml){
					jQuery.unblockUI();
					var message_str = '';
					jQuery(xml).find('message').each(function(){
						message_str += jQuery(this).text() + "<BR>";
					});
					if (jQuery(xml).find('success').text()==1) {
						load_sc_customer_group_tab(cgrpID, pmID);
						jquery_confirm_box(message_str, 0, 0);
					} else {
						jquery_confirm_box(message_str, 1, 1, "Error");
					}
				},
				error:function(xhr,err,e){ 
					jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
				}
			});
		});
	});
	
	jQuery('#btn_copy_group_sc_surcharge').click(function(){
		jquery_confirm_box("Are you sure to copy over the data?", 2, 0, "Confirm");
		jQuery('#jconfirm_submit').click(function(){
			jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
			jQuery('#sc_surcharge_frm').ajaxSubmit({
				url: "payment_methods_xmlhttp.php?action=copy_group_sc_surcharge&pm_id="+pmID+"&cgrp_id="+cgrpID,
				timeout: 10000,
				type: "POST",
				dataType: 'xml',
				cache: false,
				async: true,
				success: function(xml){
					jQuery.unblockUI();
					var message_str = '';
					jQuery(xml).find('message').each(function(){
						message_str += jQuery(this).text() + "<BR>";
					});
					if (jQuery(xml).find('success').text()==1) {
						load_sc_customer_group_tab(cgrpID, pmID);
						jquery_confirm_box(message_str, 0, 0);
					} else {
						jquery_confirm_box(message_str, 1, 1, "Error");
					}
				},
				error:function(xhr,err,e){
					jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
				}
			});
		});
	});
}

function toggle_fees_fields(fees_amt, field_idx) {
	var amt = trim_str(fees_amt);
	if (isNaN(amt) || amt == '') {
		jQuery("#payment_fees_cost_value-"+field_idx).attr('disabled', true);
		jQuery("#payment_fees_cost_percent-"+field_idx).attr('disabled', true);
		jQuery("#payment_fees_cost_percent_min-"+field_idx).attr('disabled', true);
		jQuery("#payment_fees_cost_percent_max-"+field_idx).attr('disabled', true);
	} else {
		jQuery("#payment_fees_cost_value-"+field_idx).attr('disabled', false);
		jQuery("#payment_fees_cost_percent-"+field_idx).attr('disabled', false);
		jQuery("#payment_fees_cost_percent_min-"+field_idx).attr('disabled', false);
		jQuery("#payment_fees_cost_percent_max-"+field_idx).attr('disabled', false);
	}
}

function change_currency(new_currency, field_idx) {
	jQuery("#fees_curr-"+field_idx).html(new_currency);
	jQuery("#fees_cost_curr-"+field_idx).html(new_currency);
	jQuery("#min_fees_curr-"+field_idx).html(new_currency);
	jQuery("#max_fees_curr-"+field_idx).html(new_currency);
}

function load_customer_group_tab(cgrpID, pmID){
	cgrpID = cgrpID || 0;
	pmID = pmID || 0;
	
	if (pmID == 0) {
		pmID = jQuery("#payment_methods_id").val();
	}
	
	if (cgrpID > 0 && pmID > 0) {
		jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
		jQuery.ajax({
		    url: "payment_methods_xmlhttp.php?action=display_surcharge_tab&pm_id="+pmID+"&cgrp_id="+cgrpID,
		    type: 'GET',
		    dataType: 'xml',
		    timeout: 10000,
		    error: function(){
		    	jQuery.unblockUI();	
		        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
		    },
		    success: function(xml){
		    	jQuery.unblockUI();	
		    	jQuery('#payment_method_tab_zone').css({display:'block'});
		    	display_surcharge_customer_group_tab(pmID, cgrpID, xml);
			}
		});
	}
}

function load_sc_customer_group_tab(cgrpID, pmID){
	cgrpID = cgrpID || 0;
	pmID = pmID || 0;
	
	if (pmID == 0) {
		pmID = jQuery("#payment_methods_id").val();
	}
	
	if (cgrpID > 0 && pmID > 0) {
		jquery_confirm_box('<h1>Loading...</h1>', 0, 0);
		jQuery.ajax({
		    url: "payment_methods_xmlhttp.php?action=display_sc_surcharge_tab&pm_id="+pmID+"&cgrp_id="+cgrpID,
		    type: 'GET',
		    dataType: 'xml',
		    timeout: 10000,
		    error: function(){
		    	jQuery.unblockUI();	
		        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
		    },
		    success: function(xml){
		    	jQuery.unblockUI();	
		    	jQuery('#payment_method_tab_zone').css({display:'block'});
		    	display_sc_surcharge_customer_group_tab(pmID, cgrpID, xml);
			}
		});
	}
}