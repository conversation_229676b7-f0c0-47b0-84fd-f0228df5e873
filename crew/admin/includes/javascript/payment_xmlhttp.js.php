<SCRIPT language="JavaScript" type="text/javascript">
  
var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
if (typeof(pls_select) != 'undefined' && pls_select != null) {
	stateSelectionTitle['text'] = pls_select;
} else {
	stateSelectionTitle['text'] = 'Please Select';
}

function reserveTrans (t_id, t_type, u_id, u_type, r_mode, div_sec) {
	var div_obj = DOMCall(div_sec);
	var res;
    var server_action = 'reserve_trans';
    var old_link = '';
    
    old_link = div_obj.innerHTML;
    
    var user_msg = prompt("Leave your remark...", "");
	if (user_msg != null) {
		//user_msg = URLEncode(user_msg);
	} else {
		return false;
	}
	
    div_obj.innerHTML = 'Loading ...';
    
	var ref_url = "payment_xmlhttp.php";
	var params = "action="+server_action+"&t_id="+t_id+"&t_type="+t_type+"&u_id="+u_id+"&u_type="+u_type+"&r_mode="+r_mode+"&remark="+user_msg;
	xmlhttp.open("POST", ref_url, true); 
	xmlhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xmlhttp.setRequestHeader("Content-length", params.length);
	xmlhttp.setRequestHeader("Connection", "close");
	
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
      		//res = xmlhttp.responseText;
      		
      		var res_code = typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
      		
      		if (res_code == 1) {
      			if (r_mode == '1') {
      				div_obj.innerHTML = '[<a href="javascript:;" onClick="reserveTrans(\''+t_id+'\', \''+t_type+'\', \''+u_id+'\', \''+u_type+'\', \'0\', \''+div_sec+'\');">Remove from Reserve</a>]';
      			} else {
      				div_obj.innerHTML = '[<a href="javascript:;" onClick="reserveTrans(\''+t_id+'\', \''+t_type+'\', \''+u_id+'\', \''+u_type+'\', \'1\', \''+div_sec+'\');">Add to Reserve</a>]';
      			}
      			showMainInfo('Action Success');
			} else {
				var res_msg = typeof (xmlhttp.responseXML.getElementsByTagName("res_message")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_message")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_message")[0].firstChild.data : '';
      			
      			alert(res_msg);
      			div_obj.innerHTML = old_link;
      		}
      	}
    }
    
    xmlhttp.send(params);
    
    setTimeout('hideMainInfo()', 3000);
}

function reserveSCTrans (h_id, u_id, r_mode, div_sec, sc_type) {
	var div_obj = DOMCall(div_sec);
	var res;
    var server_action = 'reserve_sc_trans';
    var old_link = '';

	if (sc_type == 'W') {
		server_action = 'reserve_wc_trans';
	}
	    
    old_link = div_obj.innerHTML;
    
    div_obj.innerHTML = 'Loading ...';
    
	var ref_url = "payment_xmlhttp.php?action="+server_action+"&h_id="+h_id+"&u_id="+u_id+"&r_mode="+r_mode;
	xmlhttp.open("GET", ref_url); 
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
      		//res = xmlhttp.responseText;
      		//alert(res);
      		var res_code = typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
      		
      		if (res_code == 1) {
      			if (r_mode == '1') {
      				div_obj.innerHTML = '[<a href="javascript:;" onClick="reserveSCTrans(\''+h_id+'\', \''+u_id+'\', \'0\', \''+div_sec+'\', \''+sc_type+'\');">Remove from Reserve</a>]';
      			} else {
      				div_obj.innerHTML = '[<a href="javascript:;" onClick="reserveSCTrans(\''+h_id+'\', \''+u_id+'\', \'1\', \''+div_sec+'\', \''+sc_type+'\');">Add to Reserve</a>]';
      			}
      			showMainInfo('Action Success');
			} else {
				var res_msg = typeof (xmlhttp.responseXML.getElementsByTagName("res_message")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_message")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_message")[0].firstChild.data : '';
      			
      			alert(res_msg);
      			div_obj.innerHTML = old_link;
      		}
      	}
    }
    
    xmlhttp.send(null);
    
    setTimeout('hideMainInfo()', 3000);
}

function updateEditPayment(file_name, btn_obj, p_id, fr_status, to_status, pm_id, manual_complete) {
	
	manual_complete = manual_complete || 0;
	
	var btn_old_value = '';
    var server_action = 'update_payment';
   	
    btn_obj.disabled = true;
    btn_old_value = btn_obj.value;
    btn_obj.value = 'Please wait';
    
	var error = false;
	var payRef = '';
	var need_payRef = false;
	if (fr_status == '2' && to_status == '3') { need_payRef = true; }
	
	if (need_payRef) {
		payRef = prompt("Payment Reference", "");
		if (payRef == 'undefined' || payRef == null) {
			btn_obj.value = btn_old_value;
			btn_obj.disabled = false;
			error = true;
		} else {
			var payRefValue = URLEncode(payRef);
		}
	}
	
	if (!error) {
		var ref_url = "payment_xmlhttp.php?action="+server_action+"&p_id="+p_id+"&fr_status="+fr_status+"&to_status="+to_status+"&filename="+file_name;

		if (manual_complete > 0) {
			ref_url += "&manual_complete=" + manual_complete;
		}

		if (need_payRef) {
			ref_url += "&pay_ref="+payRefValue;
		}

		blockUI_disable();
		jQuery.ajax({
			url: ref_url,
			type: 'GET',
			dataType: 'xml',
			timeout: 60000,
			error: function(){
				jQuery.unblockUI();
			},
			success: function(xml) {
				var res_code = jQuery(xml).find('res_code').text();
				var res_msg = jQuery(xml).find('res_message').text();

				if (res_code == 1) {
					if (res_msg != '') {
						alert(res_msg);
					}
				} else {
					alert(res_msg);
				}

				jQuery.unblockUI();
				location.href = "<?=tep_href_link(FILENAME_PAYMENT, tep_get_all_get_params(array('action', 'cont')) . 'action=edit')?>";
			}
		});
	}	
}

function updatePayment(file_name, btn_obj, p_id, fr_status, to_status, pm_id, manual_complete) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	manual_complete = manual_complete || 0;
	
	var exRate = DOMCall('exchange_rate_' + p_id);
	var payRef = DOMCall('payment_reference_' + p_id);
	var sysDef = DOMCall('system_defined_' + pm_id);
	var btn_old_value = '';
	var res;
    var server_action = 'update_payment';
   	
    btn_obj.disabled = true;
    btn_old_value = btn_obj.value;
    btn_obj.value = 'Please wait';
    
    var exRateValue = exRate != null ? URLEncode(exRate.value) : 0;
    var payRefValue = payRef != null ? URLEncode(payRef.value) : '';
    var sysDefValue = sysDef != null ? sysDef.value : 'no';
    
	var ref_url = "payment_xmlhttp.php?action="+server_action+"&p_id="+p_id+"&fr_status="+fr_status+"&to_status="+to_status+"&filename="+file_name;
	
	if (manual_complete > 0) {
		ref_url += "&manual_complete=" + manual_complete;
	}
	
	if (sysDefValue == 'yes') {
		ref_url = ref_url + "&cur_ex="+storecreditInfoArray[p_id]['currency_ex'];
	} else {
		ref_url = ref_url + "&cur_ex="+currencyInfoArray[pm_id]['currency_ex'];
	}
	ref_url = ref_url + "&ex_rate="+exRateValue+"&pay_ref="+payRefValue;
	xmlhttp.open("GET", ref_url); 
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
      		// res = xmlhttp.responseText;
      		// alert(res);
      		
			var res_code = typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
			var res_msg = typeof (xmlhttp.responseXML.getElementsByTagName("res_message")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_message")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_message")[0].firstChild.data : '';
			
			if (res_code == 1) {
				if (res_msg != '') {
					alert(res_msg);
				}
				
				var thisRow = btn_obj.parentNode.parentNode;
	      		var oldRow = thisRow;
	      		var oldRowIndex = thisRow.sectionRowIndex;
	      		
	      		var thisTbody = thisRow.parentNode;
	      		var thisTable = thisTbody.parentNode;
				
				thisTbody.deleteRow(thisRow.sectionRowIndex);
				
				/*
      			if (r_mode == '1') {
      				div_obj.innerHTML = '[<a href="javascript:;" onClick="reserveTrans(\''+t_id+'\', \''+t_type+'\', \''+u_id+'\', \''+u_type+'\', \'0\', \''+div_sec+'\');">Remove from Reserve</a>]';
      			} else {
      				div_obj.innerHTML = '[<a href="javascript:;" onClick="reserveTrans(\''+t_id+'\', \''+t_type+'\', \''+u_id+'\', \''+u_type+'\', \'1\', \''+div_sec+'\');">Add to Reserve</a>]';
      			}
      			showMainInfo('Action Success');
      			*/
      			
      			/* Get the latest row contents*/
				var row_style = (oldRowIndex % 2) ? 'reportListingEven' : 'reportListingOdd' ;
				
	      		myNewRow = thisTbody.insertRow(oldRowIndex);
	      		myNewRow.className = row_style.toString();
	      		myNewRow.setAttribute('height', 20);
	      		myNewRow.onmouseover=function(){ rowOverEffect(this, 'reportListingRowOver'); }
	      		myNewRow.onmouseout=function(){ rowOutEffect(this, row_style); }
				myNewRow.onclick=function(){ rowClicked(this, row_style) }
				
				var table_cell = xmlhttp.responseXML.getElementsByTagName("table_cell")[0];
	      		
	      		if (typeof(table_cell) != 'undefined' && table_cell != null) {
	      			var cell_obj = '';
		      		for (var cell_cnt=0; cell_cnt < table_cell.childNodes.length; cell_cnt++) {
		      			cell_obj = table_cell.getElementsByTagName("cell")[cell_cnt];
		      			//appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
		      			
		      			var myNewCell = myNewRow.insertCell(myNewRow.cells.length);
						myNewCell.className = 'reportRecords';
						myNewCell.setAttribute('valign', 'top');
						
						var attribute_str = cell_obj.getAttribute("property");
						if (attribute_str != null) {
							var value_pair = attribute_str.split('&');
							for (var pair_cnt=0; pair_cnt < value_pair.length; pair_cnt++) {
								var key_value = value_pair[pair_cnt].split('=');
								myNewCell.setAttribute(key_value[0], key_value[1]);
							}
						}
						
			   			myNewCell.innerHTML = cell_obj.firstChild.nodeValue;
				    }
				    
					jQuery("img.img_lock").click(function() {
						var pid = jQuery(this).attr('pid');
						jquery_confirm_box("Are you sure to unlock this payment'?",2 , 0 , "Confirm");
						jQuery('#jconfirm_submit').click(function(){
							jquery_confirm_box('<h1>Unlocking...</h1>', 0, 0);
							jQuery.ajax({
								type: "GET",
								dataType: 'xml',
								url: "payment_xmlhttp.php?action=unlock_store_payment&spID=" + pid,
								timeout: 10000,
							    error: function(){
							        jquery_confirm_box("Error loading XML document", 1, 1, "Warning");
							    },
								success: function(xml){
									if (jQuery(xml).find('result').text() == '1') {
										location.href = "<?=tep_href_link(FILENAME_PAYMENT, tep_get_all_get_params(array('action', 'cont')) . 'action=show_report&cont=1')?>";
									}
								}
							});
						});
					});
	      		}
			} else {
      			alert(res_msg);
      			
      			btn_obj.value = btn_old_value;
      			btn_obj.disabled = false;
      		}
      	}
    }
    
    xmlhttp.send(null);
    
    setTimeout('hideMainInfo()', 3000);
}

function updateRefund(file_name, btn_obj, r_id, fr_status, to_status, tbl_cnt, auto_refund, refresh = 0) {
    if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;

    var payRef = DOMCall('payment_reference_' + r_id);
    var btn_old_value = '';
    var res;
    var server_action = 'update_refund';
   	
    btn_obj.disabled = true;
    btn_old_value = btn_obj.value;
    btn_obj.value = 'Please wait';
    
    if (typeof(document.getElementById('MCompleteBtn_'+r_id)) != 'undefined' && document.getElementById('MCompleteBtn_'+r_id) != null) {
        document.getElementById('MCompleteBtn_'+r_id).disabled = true;
    }
    
    var payRefValue = payRef != null ? URLEncode(payRef.value) : '';
    
    var ref_url = "payment_xmlhttp.php?action="+server_action+"&r_id="+r_id+"&fr_status="+fr_status+"&to_status="+to_status+"&filename="+file_name+"&pay_ref="+payRefValue+"&row_cnt="+tbl_cnt+"&auto_refund="+auto_refund+"&refresh="+refresh;
    xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            if (refresh == 1) {
                location.reload();
            }
            
            var res_code = typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
            var res_msg = typeof (xmlhttp.responseXML.getElementsByTagName("res_message")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_message")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_message")[0].firstChild.data : '';
			
            if (res_code == 1) {
                if (res_msg != '') {
                    alert(res_msg);
                }

                var thisRow = btn_obj.parentNode.parentNode;
                var oldRow = thisRow;
                var oldRowIndex = thisRow.sectionRowIndex;

                var thisTbody = thisRow.parentNode;
                var thisTable = thisTbody.parentNode;

                thisTbody.deleteRow(thisRow.sectionRowIndex);

                /* Get the latest row contents*/
                var row_style = (oldRowIndex % 2) ? 'reportListingEven' : 'reportListingOdd' ;

                myNewRow = thisTbody.insertRow(oldRowIndex);
                myNewRow.className = row_style.toString();
                myNewRow.setAttribute('height', 20);
                myNewRow.onmouseover=function(){ rowOverEffect(this, 'reportListingRowOver'); }
                myNewRow.onmouseout=function(){ rowOutEffect(this, row_style); }
                myNewRow.onclick=function(){ rowClicked(this, row_style) }

                var table_cell = xmlhttp.responseXML.getElementsByTagName("table_cell")[0];

                if (typeof(table_cell) != 'undefined' && table_cell != null) {
                    var cell_obj = '';
                    for (var cell_cnt=0; cell_cnt < table_cell.childNodes.length; cell_cnt++) {
                        cell_obj = table_cell.getElementsByTagName("cell")[cell_cnt];

                        var myNewCell = myNewRow.insertCell(myNewRow.cells.length);
                            myNewCell.className = 'reportRecords';
                            myNewCell.setAttribute('valign', 'top');

                        var attribute_str = cell_obj.getAttribute("property");
                        if (attribute_str != null) {
                            var value_pair = attribute_str.split('&');
                            for (var pair_cnt=0; pair_cnt < value_pair.length; pair_cnt++) {
                                var key_value = value_pair[pair_cnt].split('=');
                                myNewCell.setAttribute(key_value[0], key_value[1]);
                            }
                        }

                        myNewCell.innerHTML = cell_obj.firstChild.nodeValue;
                    }
                }
            } else {
                alert(res_msg);

                btn_obj.value = btn_old_value;
                btn_obj.disabled = false;

                if (typeof(document.getElementById('MCompleteBtn_'+r_id)) != 'undefined' && document.getElementById('MCompleteBtn_'+r_id) != null) {
                    document.getElementById('MCompleteBtn_'+r_id).disabled = false;
                }
            }
      	}
    }
    
    xmlhttp.send(null);
    
    setTimeout('hideMainInfo()', 3000);
}

function getAgingStats () {
	var link_div_obj;
	var status_div_obj;
	var aging_div_obj;
	var res;
	var server_action = 'get_customer_aging';
	var bo_str = '';
	
	jQuery(".bo_id").each(function(i){
		bo_str = bo_str + jQuery(this).attr('id') + ',';
	});
	
	var ref_url = "payment_xmlhttp.php";
	var params = "action="+server_action+"&bo_str="+bo_str;
	xmlhttp.open("POST", ref_url, true); 
	xmlhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xmlhttp.setRequestHeader("Content-length", params.length);
	xmlhttp.setRequestHeader("Connection", "close");
	
	showMainInfo('Loading ...');
	
	xmlhttp.onreadystatechange = function() { 
		if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
			
      		var res_code = (typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
			
			if (res_code == 1) {
				var buyback_order = xmlhttp.responseXML.getElementsByTagName("buyback_order");
				var trans_id = '';
				var ord_id_str = '';
				var ord_status_str = '';
				var cust_aging_str = '';
				
				for (var rec_cnt=0; rec_cnt < buyback_order.length; rec_cnt++) {
					trans_id = buyback_order[rec_cnt].getAttribute("id");
					
					link_div_obj = DOMCall('cor_store_'+trans_id);
					status_div_obj = DOMCall('cor_status_'+trans_id);
					aging_div_obj = DOMCall('cust_aging_'+trans_id);
					
					ord_id_str = buyback_order[rec_cnt].getElementsByTagName("order_id")[0].firstChild.data;
					ord_status_str = buyback_order[rec_cnt].getElementsByTagName("order_status")[0].firstChild.data;
					cust_aging_str = buyback_order[rec_cnt].getElementsByTagName("customer_aging")[0].firstChild.data;
					
					if (typeof(link_div_obj) != 'undefined' && link_div_obj != null) {
						link_div_obj.innerHTML = ord_id_str;
					}
					if (typeof(status_div_obj) != 'undefined' && status_div_obj != null) {
						status_div_obj.innerHTML = ord_status_str;
					}
					if (typeof(aging_div_obj) != 'undefined' && aging_div_obj != null) {
						aging_div_obj.innerHTML = cust_aging_str;
					}
				}
      			showMainInfo('Action Success');
			} else {
				var res_msg = typeof (xmlhttp.responseXML.getElementsByTagName("res_message")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_message")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_message")[0].firstChild.data : '';
				
				alert(res_msg);
			}
			setTimeout('hideMainInfo()', 3000);
		}
	}
	
	xmlhttp.send(params);
}

function getSuppAgingStats (p_id) {
	var aging_div_obj;
	var cb_div_obj;
	var res;
	var server_action = 'get_payment_aging';
	
	var ref_url = "payment_xmlhttp.php";
	var params = "action="+server_action+"&p_id="+p_id;
	xmlhttp.open("POST", ref_url, true); 
	xmlhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xmlhttp.setRequestHeader("Content-length", params.length);
	xmlhttp.setRequestHeader("Connection", "close");
	
	aging_div_obj = DOMCall('supp_aging_'+p_id);
	if (typeof(aging_div_obj) != 'undefined' && aging_div_obj != null) {
		aging_div_obj.innerHTML = 'Loading ...';
	}
	
	xmlhttp.onreadystatechange = function() { 
		if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
			
      		var res_code = (typeof (xmlhttp.responseXML.getElementsByTagName("res_code")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_code")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("res_code")[0].firstChild.data : '';
			
			if (res_code == 1) {
				var payment_aging = xmlhttp.responseXML.getElementsByTagName("payment_aging");
				var supp_aging_str = '';
				var cb_order_str = '';
				
				aging_div_obj = DOMCall('supp_aging_'+p_id);
				cb_div_obj = DOMCall('cb_order_'+p_id);
				
				supp_aging_str = payment_aging[0].getElementsByTagName("supplier_payment_aging")[0].firstChild.data;
				cb_order_str = payment_aging[0].getElementsByTagName("cb_order_ratio")[0].firstChild.data;
				
				if (typeof(aging_div_obj) != 'undefined' && aging_div_obj != null) {
					aging_div_obj.innerHTML = supp_aging_str;
				}
				if (typeof(cb_div_obj) != 'undefined' && cb_div_obj != null) {
					cb_div_obj.innerHTML = cb_order_str;
				}
			} else {
				var res_msg = typeof (xmlhttp.responseXML.getElementsByTagName("res_message")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("res_message")[0] != null ? xmlhttp.responseXML.getElementsByTagName("res_message")[0].firstChild.data : '';
				
				alert(res_msg);
			}
		}
	}
	
	xmlhttp.send(params);
}

function getPOPMLists (curr_sel, pm_div, opt_div, pm_id, b_id, u_id) {
	var obj_ref = curr_sel;
	var res;
    var server_action = 'refresh_po_pm_fields';
    
	jQuery.ajax({
		    url: "payment_xmlhttp.php?action="+server_action+"&pm_id="+pm_id+"&b_id="+b_id+"&u_id="+u_id+"&cur_code="+obj_ref.value,
		    type: 'GET',
		    dataType: 'xml',
		    timeout: 10000,
		    error: function(){
      			jQuery('#'+pm_div).html('Error loading XML document');
      			jQuery('#'+opt_div).html('');
		    },
		    success: function(xml){
		    	res = jQuery(xml).find('option_selection').text();
      			jQuery('#'+pm_div).html(res);
      			jQuery('#'+opt_div).html('');
			}
	});
	
    return true;
}

function getPOPMOptions (pm_sel, opt_div, b_id, u_id) {
	var obj_ref = pm_sel;
	var res;
    var server_action = (b_id != null && b_id != '') ? 'edit_po_pm_fields' : 'add_po_pm_fields';
    
	jQuery.ajax({
		    url: "payment_xmlhttp.php?action="+server_action+"&b_id="+b_id+"&u_id="+u_id+"&pm_id="+obj_ref.value,
		    type: 'GET',
		    dataType: 'xml',
		    timeout: 10000,
		    error: function(){
      			jQuery('#'+opt_div).html('Error loading XML document');
		    },
		    success: function(xml){
		    	res = jQuery(xml).find('option_selection').text();
      			jQuery('#'+opt_div).html(res);
			}
	});
	
    return true;
}

function editPOPMBook(opt_div, b_id, u_id) {
	var res;
    var server_action = 'edit_po_pm_book';
    
	jQuery.ajax({
		    url: "payment_xmlhttp.php?action="+server_action+"&b_id="+b_id+"&u_id="+u_id,
		    type: 'GET',
		    dataType: 'xml',
		    timeout: 10000,
		    error: function(){
      			jQuery('#'+opt_div).html('Error loading XML document');
		    },
		    success: function(xml){
		    	res = jQuery(xml).find('option_selection').text();
      			jQuery('#'+opt_div).html(res);
			}
	});
	
    return true;
}

function addPOPMBook(opt_div, u_id) {
	var res;
    var server_action = 'add_po_pm_book';
    
	jQuery.ajax({
		    url: "payment_xmlhttp.php?action="+server_action+"&u_id="+u_id,
		    type: 'GET',
		    dataType: 'xml',
		    timeout: 10000,
		    error: function(){
      			jQuery('#'+opt_div).html('Error loading XML document');
		    },
		    success: function(xml){
		    	res = jQuery(xml).find('option_selection').text();
      			jQuery('#'+opt_div).html(res);
			}
	});
	
    return true;
}

function lock_cdk_supplier() {
	var supp_id = jQuery('#po_payment_supplier').val();
	var server_action = 'lock_po_supplier';
	
	jQuery.ajax({
			url: "po_payment_xmlhttp.php?action="+server_action+"&s_id="+supp_id,
			type: 'GET',
			dataType: 'xml',
			timeout: 10000,
			Error: function(){
				
			},
			success: function(xml){
				supp_locked_html = jQuery(xml).find('supplier_locked').text();
				jQuery('#po_payment_supplier_lock_div').html(supp_locked_html);
				
				res = jQuery(xml).find('action_result').text();
				alert(res);
			}
	});
}

function unlock_cdk_supplier() {
	var supp_id = jQuery('#po_payment_supplier').val();
	var server_action = 'unlock_po_supplier';
	
	jQuery.ajax({
			url: "po_payment_xmlhttp.php?action="+server_action+"&s_id="+supp_id,
			type: 'GET',
			dataType: 'xml',
			timeout: 10000,
			Error: function(){
				
			},
			success: function(xml){
				supp_locked_html = jQuery(xml).find('supplier_locked').text();
				jQuery('#po_payment_supplier_lock_div').html(supp_locked_html);
				
				res = jQuery(xml).find('action_result').text();
				alert(res);
			}
	});
}

function getPOPayCurrency(sup_sel) {
	var obj_ref = sup_sel;
	var res;
	var supp_locked_html;
	var server_action = 'get_po_supplier_pay_currency';
	
	var list_table = '';
	list_table += '<table border="0" width="100%" cellspacing="1" cellpadding="4">';
	list_table += '	<tr>';
	<?
	echo "list_table += '		<td class=\"infoBoxHeading\">".TABLE_HEADING_PO_SELECT."</td>';\n";
	echo "list_table += '		<td class=\"infoBoxHeading\">".TABLE_HEADING_PO_PO_REF_NO."</td>';\n";
	echo "list_table += '		<td class=\"infoBoxHeading\">".TABLE_HEADING_PO_PO_AMOUNT."</td>';\n";
	?>
	list_table += '</tr></table>';
	
	jQuery('#po_payment_currency_div').html('<?=TEXT_SELECT_PO_SUPPLIER?>');
	jQuery('#po_payment_avail_amount_div').html('<?=TEXT_ZERO_AMOUNT?>');
	jQuery('#po_payment_method_div').html('<?=TEXT_SELECT_CURRENCY?>');
	jQuery('#po_list_div').html(list_table);
	jQuery('#withdraw_total').val(0);
	jQuery('#withdraw_total_div').html('0.00');
	jQuery('#po_payment_type').val('');
	
	jQuery.ajax({
			url: "po_payment_xmlhttp.php?action="+server_action+"&s_id="+obj_ref.value,
			type: 'GET',
			dataType: 'xml',
			timeout: 10000,
			Error: function(){
				jQuery('#po_payment_currency_div').html('Error loading XML document');
			},
			success: function(xml){
				res = jQuery(xml).find('option_selection').text();
				supp_locked_html = jQuery(xml).find('supplier_locked').text();
				jQuery('#po_payment_supplier_lock_div').html(supp_locked_html);
				jQuery('#po_payment_currency_div').html(res);
			}
	});
	
	return true;
}

function getPOPayMethodByCurrency(curr_sel) {
	var obj_ref = curr_sel;
	var sup_obj = DOMCall('po_payment_supplier');
	var res;
	var cur_left;
	var cur_right;
	var avail_amt = 0;
	var server_action = 'get_po_supplier_pay_method';
	
	var list_table = '';
	list_table += '<table border="0" width="100%" cellspacing="1" cellpadding="4">';
	list_table += '	<tr>';
	<?
	echo "list_table += '		<td class=\"infoBoxHeading\">".TABLE_HEADING_PO_SELECT."</td>';\n";
	echo "list_table += '		<td class=\"infoBoxHeading\">".TABLE_HEADING_PO_PO_REF_NO."</td>';\n";
	echo "list_table += '		<td class=\"infoBoxHeading\">".TABLE_HEADING_PO_PO_AMOUNT."</td>';\n";
	?>
	list_table += '</tr></table>';
	
	jQuery('#po_payment_method_div').html('<?=TEXT_SELECT_CURRENCY?>');
	jQuery('#po_list_div').html(list_table);
	jQuery('#withdraw_total').val(0);
	jQuery('#withdraw_total_div').html('0.00');
	jQuery('#po_payment_type').val('');
	
	jQuery.ajax({
			url: "po_payment_xmlhttp.php?action="+server_action+"&s_id="+sup_obj.value+"&c_id="+obj_ref.value,
			type: 'GET',
			dataType: 'xml',
			timeout: 10000,
			Error: function(){
				jQuery('#po_payment_method_div').html('Error loading XML document');
			},
			success: function(xml){
				res = jQuery(xml).find('option_selection').text();
				cur_left = jQuery(xml).find('currency_left_symbol').text();
				cur_right = jQuery(xml).find('currency_right_symbol').text();
				avail_amt = jQuery(xml).find('available_amount').text();
				jQuery('#po_payment_method_div').html(res);
				jQuery('#withdraw_currency_left_div').html(cur_left);
				jQuery('#withdraw_currency_right_div').html(cur_right);
				jQuery('#po_payment_avail_amount_div').html(avail_amt);
			}
	});
	
	return true;
}

function getPOPayForPO(pm_sel) {
	var obj_ref = pm_sel;
	var sup_obj = DOMCall('po_payment_supplier');
	var curr_obj = DOMCall('po_payment_currency');
	var res;
	var server_action = 'get_po_supplier_pay_for_po';
	
	jQuery('#withdraw_total').val(0);
	jQuery('#withdraw_total_div').html('0.00');
	jQuery('#po_payment_type').val('');
	
	jQuery.ajax({
			url: "po_payment_xmlhttp.php?action="+server_action+"&s_id="+sup_obj.value+"&c_id="+curr_obj.value+"&pm_id="+obj_ref.value,
			type: 'GET',
			dataType: 'xml',
			timeout: 10000,
			Error: function(){
				jQuery('#po_list_div').html('Error loading XML document');
			},
			success: function(xml){
				res = jQuery(xml).find('option_selection').text();
				jQuery('#po_list_div').html(res);
			}
	});
	
	return true;
}

function calculatePayPOTotal(po_sel, po_amt, po_pay_type) {
	var total_amt = DOMCall('withdraw_total');
	var new_po_amt = 0;
	var allow_add = false;
	
	// check to prevent consignment PO mix with pre-payment or day-term PO
	if (po_sel.checked == true) {
		if (jQuery('#po_payment_type').val() != '') {
			if (po_pay_type == 'g' && jQuery('#po_payment_type').val() == po_pay_type) {
				allow_add = true;
			} else if (po_pay_type != 'g' && jQuery('#po_payment_type').val() == 'g') {
				allow_add = false;
			} else if (po_pay_type != 'g' && jQuery('#po_payment_type').val() != 'g') {
				allow_add = true;
			}
		} else {
			allow_add = true;
		}
		if (allow_add) { jQuery('#po_payment_type').val(po_pay_type); }
	} else {
		allow_add = true;
		jQuery('#po_payment_type').val('');
	}
	
	if (allow_add) {
		if (po_sel.checked == true) {
			new_po_amt = parseFloat(total_amt.value) + parseFloat(po_amt);
		} else {
			new_po_amt = parseFloat(total_amt.value) - parseFloat(po_amt);
		}

		jQuery('#withdraw_total').val(new_po_amt.toFixed(2));
		jQuery('#withdraw_total_div').html(new_po_amt.toFixed(2));
		return true;
	} else {
		alert('Consignment PO are not allowed to mix with Pre-Payment or Day-Term PO.');
		return false;
	}
}

function client_height_adjust () {
	if (typeof(window.innerWidth) == 'number') { //not IE
		return 20;
	} else {
		return 20;
	}
}

function set_fancybox_position () {
	var boxTopValue = 0, boxLeftValue = 0;
	//Get the screen height and width
	var docH = jQuery(document).height();
	var winW = jQuery(window).width();
	var winH = jQuery(window).height();
	
	boxTopValue = winH/2 - jQuery('#fancy_content').height()/2 - 30;
	boxLeftValue = winW/2 - 245;
	
	jQuery("#fancy_box").css({'display':'block', 'left':boxLeftValue, 'visibility':'visible', 'top':boxTopValue});
	jQuery("#fancy_box_Bg").css({'display':'block', 'position':'absolute', 'width':winW, 'height':docH, 'visibility':'visible'});
	
	if (typeof(window.innerWidth) == 'number') {
		jQuery("#fancy_box").css({'position':'fixed'});
	}
}

function realign_fancybox (table_id) {
	if(jQuery("#fancy_box").css('display') == 'block') {
		jQuery("#fancy_box").css('height',jQuery("#"+table_id).height()+client_height_adjust());
		
		if (jQuery("#"+table_id).width() > jQuery("#fancy_box").width()) {
			jQuery("#fancy_box").css('width',jQuery("#"+table_id).width()+10);
		}
		
		var boxTopValue = 0, boxLeftValue = 0;
		var docH = jQuery(document).height();
		var winW = jQuery(window).width();
		var winH = jQuery(window).height();
		
		boxTopValue = winH/2 - jQuery('#fancy_content').height()/2 - 30;
		boxLeftValue = winW/2 - 245;
		
		jQuery("#fancy_box").css({'left':boxLeftValue, 'top':boxTopValue});
		jQuery("#fancy_box_Bg").css({'width':winW, 'height':docH});
		
		if(typeof(window.innerWidth) != 'number'){
			var scrollWinH = (jQuery(window).height()/2) - 222 + jQuery(this).scrollTop();	
			jQuery("#fancy_box").css({top:scrollWinH+"px"});
		}
	}
}

function hideFancyBox() {
	jQuery('#footerBar').css('display', 'block');
	jQuery("#fancy_close").css('display','none');
	jQuery("#fancy_box").css({'display':'none','visibility':'hidden'});
	jQuery("#fancy_box_Bg").css({'display':'none','visibility':'hidden'});
	jQuery("body").css('overflow','');
}

function show_disbursement_details() {
	
	var sup_obj = DOMCall('po_payment_supplier');
	var curr_obj = DOMCall('po_payment_currency');
	var pm_obj = DOMCall('po_payment_method');
	var total_obj = DOMCall('withdraw_total');
	var po_pay_obj = DOMCall('po_payment_type');
	var po_id;
	var po_str = '';
	
	jQuery(".pay_po_batch").each (function() {
		if (this.checked == true) {
			po_str = po_str + (po_str.length > 0 ? ',' : '') + this.value;
		}
	});
	
	var ref_url = "po_payment_xmlhttp.php?action=get_disburse_details&po_payment_supplier="+sup_obj.value+"&po_payment_currency="+curr_obj.value+"&po_payment_method="+pm_obj.value+"&withdraw_total="+total_obj.value+"&pay_po_str_list="+po_str;
	
	blockUI_disable();
	jQuery.ajax({
		url: ref_url,
		type: 'GET',
		dataType: 'xml',
		timeout: 60000,
		Error: function(){
			jQuery.unblockUI();
		},
		success: function(xml) {
			detail_html = jQuery(xml).find('disburse_details').text();
			supplier_locked = jQuery(xml).find('supplier_locked').text();
			
			if (supplier_locked > 0) {
				pop_out  = '<table id="disburse_details" border="0" cellspacing="0" cellpadding="0" width="100%">';
				pop_out += detail_html;
				pop_out += '<tr><td><div class="green_button_fix_width" id="btn_cancel"><a href="javascript:;" class="text-decoration: none;"><span><font><?=BUTTON_CANCEL?></font></span></a></div></td></tr>';
				pop_out += '</table>';
			} else {
				pop_out  = '<table id="disburse_details" border="0" cellspacing="0" cellpadding="0" width="100%">';
				pop_out += '<tr><td>';
				<?="pop_out += '" . tep_draw_form("po_payment_form", FILENAME_PO_PAYMENT, tep_get_all_get_params(array("subaction")) . "subaction=insert_po_payment", "post", 'id="po_payment_form"') . "';"?>
				pop_out += '<input type="hidden" name="confirm_po_payment_supplier" id="confirm_po_payment_supplier" value="'+sup_obj.value+'">';
				pop_out += '<input type="hidden" name="confirm_po_payment_currency" id="confirm_po_payment_currency" value="'+curr_obj.value+'">';
				pop_out += '<input type="hidden" name="confirm_po_payment_method" id="confirm_po_payment_method" value="'+pm_obj.value+'">';
				jQuery(".pay_po_batch").each (function() {
					if (this.checked == true) {
						po_id = this.value;
						pop_out += '<input type="hidden" name="confirm_pay_po_batch[]" class="confirm_pay_po_batch" value="'+po_id+'">';
					}
				});
				pop_out += '<input type="hidden" name="confirm_withdraw_total" id="confirm_withdraw_total" value="'+total_obj.value+'">';
				pop_out += '<input type="hidden" name="po_payment_type" id="po_payment_type" value="'+po_pay_obj.value+'">';
				pop_out += '<table id="pay_table" border="0" cellspacing="0" cellpadding="3" height="100%" width="100%">';
				pop_out += detail_html;
				pop_out += '<tr><td><div class="green_button_fix_width" id="btn_confirm"><a href="javascript:;" class="text-decoration: none;"><span><font><?=BUTTON_CONFIRM?></font></span></a></div></td><td>&nbsp;</td><td><div class="green_button_fix_width" id="btn_cancel"><a href="javascript:;" class="text-decoration: none;"><span><font><?=BUTTON_CANCEL?></font></span></a></div></td></tr>';
				pop_out += '</table>';
				pop_out += '</form></td></tr></table>';
			}
			
			set_fancybox_position();
			
			jQuery("#fancy_content").html(pop_out);
			jQuery(".fancy_close_footer").css('display', 'block');
			realign_fancybox("disburse_details");
			
			jQuery.unblockUI();
			
			jQuery("#btn_confirm").click(function(){
				hideFancyBox();
				jQuery('#po_payment_form').submit();
			});
			
			jQuery("#btn_cancel").click(function(){
				hideFancyBox();
			});
		}
	});
}

function getPOSupplierPaymentsStatistic(suppid) {
	var server_action = 'get_po_supplier_payments_statistic';
	var ref_url = 'po_payment_xmlhttp.php?action='+server_action+'&suppID='+suppid;
	
	jQuery('#stock_received_div').html('<b>Loading Statistic...</b>');
	jQuery('#payment_sent_div').html('<b>Loading Statistic...</b>');
	jQuery('#net_outstanding_div').html('<b>Loading Statistic...</b>');
	jQuery('#po_pending_pay_div').html('<b>Loading Statistic...</b>');
	jQuery('#supplier_credit_div').html('<b>Loading Statistic...</b>');
	
	jQuery.ajax({
		url: ref_url,
		type: 'GET',
		dataType: 'xml',
		timeout: 10000,
		error: function() {
			jQuery.unblockUI();
		},
		success: function(xml) {
			var stock_received_stat = jQuery(xml).find('stock_received_pending_payment').text();
			var payment_sent_stat = jQuery(xml).find('payment_sent_pending_stock').text();
			var net_outstanding_stat = jQuery(xml).find('net_outstanding').text();
			var po_pending_pay_stat = jQuery(xml).find('total_po_pending_payment').text();
			var supplier_credit_stat = jQuery(xml).find('supplier_credit').text();
			
			jQuery('#stock_received_div').html(stock_received_stat);
			jQuery('#payment_sent_div').html(payment_sent_stat);
			jQuery('#net_outstanding_div').html(net_outstanding_stat);
			jQuery('#po_pending_pay_div').html(po_pending_pay_stat);
			jQuery('#supplier_credit_div').html(supplier_credit_stat);
		}
	});
}

function pipwavePopUp(url) {
	window.open(url,'_blank');
}
//--></SCRIPT>