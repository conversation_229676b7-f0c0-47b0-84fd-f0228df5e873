function showPollsComments(poll_id) {
	if (xmlhttp == null) {
 		alert ("<PERSON><PERSON><PERSON> does not support HTTP Request")
		return
	} 
	var url="polling_xmlhttp.php"
	url=url+"?action=display"
	url=url+"&pid="+poll_id
	url=url+"&sid="+Math.random()
	xmlhttp.onreadystatechange=stateChanged
	xmlhttp.open("GET",url,true)
	xmlhttp.send(null)
}

function stateChanged() {
	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
		document.getElementById("polls_comments").innerHTML=xmlhttp.responseXML.getElementsByTagName("comments")[0].firstChild.data;
	}
}