var prodSelectionTitle = Array();
prodSelectionTitle['id'] = '';
prodSelectionTitle['text'] = '---Select Product---';
var curPrice = new Array();

function refreshDynamicSelectOptions(sel1, sel2, _lang, filter, show_title, fname, adm, direct_topup_flag) {
	var objRef = DOMCall(sel2);
	var res;
    var server_action = 'product_list';
	var param = '';
	
	direct_topup_flag = direct_topup_flag || '';
	
    objRef.disabled = true;
   	clearOptionList(objRef);
	objRef.options[0] = new Option('Loading ...', '');
	
	var cp_id;
	if(typeof sel1.val == 'function') {
		cp_id = sel1.val();
	} else {
		cp_id = sel1.value;
	}
        
	switch(filter) {
		case 'custom_product':
			server_action = 'custom_product_list';
			param = "cp_id="+cp_id+"&lang="+_lang+'&fname='+fname+'&adm='+adm+'&direct_top_up='+direct_topup_flag;
			break;
		case 'single_product':
			server_action = 'single_product_list';
			param = "cat_id="+cp_id+"&lang="+_lang+'&fname='+fname+'&adm='+adm+'&direct_top_up='+direct_topup_flag;
			break;
		case 'non_package_product':
			server_action = 'non_package_product_list';
			param = "cat_id="+cp_id+"&lang="+_lang+'&fname='+fname+'&adm='+adm+'&direct_top_up='+direct_topup_flag;
			break;
		default:
			param = "cat_id="+cp_id+"&lang="+_lang+'&fname='+fname+'&adm='+adm+'&direct_top_up='+direct_topup_flag;
			break;
	}
	
	var ref_url = "product_listing_xmlhttp.php?action="+server_action+"&"+param;
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
      		
      		clearOptionList(objRef);

      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];

      		if (typeof (selection) != 'undefined' && selection != null) {
      			if (show_title == true) {
      				appendToSelect(objRef, prodSelectionTitle['id'], prodSelectionTitle['text']);
      			}
      			var option_sel = '';
	      		for (var i=0; i < selection.childNodes.length; i++) {
	      			option_sel = selection.getElementsByTagName("option")[i];
	      			appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
			    }
      		}

      		if ((show_title == true && objRef.options.length > 1) || (show_title == false && objRef.options.length > 0)) {
      			objRef.disabled = false;
      		} else {
      			objRef.options[0] = new Option('No product is found', '');
      		}
      	}
    }

    xmlhttp.send(null);
}


function populateCategory(catObj) {
    if (document.getElementById("cat_sel").length == 1) {
        var catObjRef = DOMCall(catObj);
        catObjRef.disabled = true;
        clearOptionList(catObjRef);
        catObjRef.options[0] = new Option('Loading ...', '');

        var ref_url = "product_listing_xmlhttp.php?action=populate_catgory";
        xmlhttp.open("GET", ref_url);
        xmlhttp.onreadystatechange = function () {
            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                clearOptionList(catObjRef);

                var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];

                if (typeof (selection) != 'undefined' && selection != null) {
                    var option_sel = '';
                    for (var i = 0; i < selection.childNodes.length; i++) {
                        option_sel = selection.getElementsByTagName("option")[i];
                        appendToSelect(catObjRef, option_sel.getAttribute("value"), option_sel.firstChild.nodeValue);
                    }
                }

                if ((catObjRef.options.length > 1) && catObjRef.options.length > 0) {
                    catObjRef.disabled = false;
                } else {
                    catObjRef.options[0] = new Option('No product is found', '');
                }


            }
        }
        xmlhttp.send(null);
    }



}

function getCatPackageQuantitySetting(catSel, prodObj, qtyObj) {
	var catObjRef = catSel;
	var prodObjRef = DOMCall(prodObj);
	var qtyObjRef = DOMCall(qtyObj);

	var cfg_info_array = new Array();

	var ref_url = "product_listing_xmlhttp.php?action=get_package_qty&cat_id="+catObjRef.value;
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
      		//alert(res);

      		var cfg_info = xmlhttp.responseXML.getElementsByTagName("cfg_key");
      		for (var i=0; i < cfg_info.length; i++) {
      			cfg_info_array[cfg_info[i].getAttribute("key")] = cfg_info[i].firstChild.nodeValue;
      		}

      		prodObjRef.value = cfg_info_array['PRODUCT_NAME_FOR_BATCH_UPDATE'];
      		qtyObjRef.value = cfg_info_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'];
      	}
    }

    xmlhttp.send(null);
}

function getCatPackageQuantitySetting_2(catSel, prodObj, qtyObj, allPackagesObj) {
	var catObjRef = catSel;
	var prodObjRef = DOMCall(prodObj);
	var qtyObjRef = DOMCall(qtyObj);
	var allPackagesObjRef = DOMCall(allPackagesObj);

	var cfg_info_array = new Array();

	var ref_url = "product_listing_xmlhttp.php?action=get_package_qty&cat_id="+catObjRef.value;

	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
      		//alert(res);

      		var cfg_info = xmlhttp.responseXML.getElementsByTagName("cfg_key");
      		for (var i=0; i < cfg_info.length; i++) {
      			cfg_info_array[cfg_info[i].getAttribute("key")] = cfg_info[i].firstChild.nodeValue;
      		}

      		prodObjRef.value = cfg_info_array['PRODUCT_NAME_FOR_BATCH_UPDATE'];
      		if (cfg_info_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'] == 'all') {
      			allPackagesObjRef.checked = true;
      			qtyObjRef.value = '';
      		} else {
      			allPackagesObjRef.checked = false;
      			qtyObjRef.value = cfg_info_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'];
      		}
      		toggleQuantities();
      	}
    }

    xmlhttp.send(null);
}


function savePackageQuantitySetting(btnObj, txtProdName, txtCatID, txtQtyStr) {
	var prodObjRef = DOMCall(txtProdName);
	var catObjRef = DOMCall(txtCatID);
	var qtyObjRef = DOMCall(txtQtyStr);

    btnObj.disabled = true;

	var ref_url = "product_listing_xmlhttp.php?action=update_package_qty&prod_name="+prodObjRef.value+"&cat_id="+catObjRef.value+"&qty_str="+qtyObjRef.value;
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		btnObj.disabled = false;
      	}
    }

    xmlhttp.send(null);
}

function savePackageQuantitySetting_2(btnObj, txtProdName, txtCatID, txtQtyStr, cbAllPackageQuantities) {
	var prodObjRef = DOMCall(txtProdName);
	var catObjRef = DOMCall(txtCatID);
	var qtyObjRef = DOMCall(txtQtyStr);
	var allpackagequantitiesObjRef = DOMCall(cbAllPackageQuantities);

    btnObj.disabled = true;

	var ref_url = "product_listing_xmlhttp.php?action=update_package_qty_2&prod_name="+prodObjRef.value+"&cat_id="+catObjRef.value+"&qty_str="+qtyObjRef.value+"&all_package_quantities="+allpackagequantitiesObjRef.checked;
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		btnObj.disabled = false;
      	}
    }

    xmlhttp.send(null);
}

function priceSetOptions(selObj, pageURL, prodStr) {
	var server_action = '';
	var win_width = 1024;
	var win_height = 500;
	var price_values = '';

	if (selObj.value == 'eps') {
		server_action = 'action=edit_price_set';
		win_width = 1200;
		win_height = 750;
	} else if (selObj.value == 'nps') {
		server_action = 'action=add_price_set';
	}

    if (selObj.value == 'rps') {
		refreshPriceSetOptions('template_listing_form', 'price_set');
		return true;
	} else if (server_action.length > 0) {
	    if (pageURL.indexOf('?') == -1) {
	    	pageURL += '?' + server_action;
	    } else {
	    	pageURL += '&' + server_action;
	    }
	   	openDGDialog(pageURL, win_width, win_height, updateSetOptions, ',resizable=yes,scrollbars=yes');
	} else {
		if (selObj.value != '' && selObj.value != '0' && selObj.value != 'comment' && prodStr.length > 0) {	// load the set value
			selObj.disabled = true;

			var ref_url = "product_listing_xmlhttp.php?action=get_price_set&price_set_id="+selObj.value+"&prod_str="+prodStr;
			xmlhttp.open("GET", ref_url);
		    xmlhttp.onreadystatechange = function() {
		      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
		      		//res = xmlhttp.responseText;

		      		var price_set = xmlhttp.responseXML.getElementsByTagName("price_set")[0];

		      		if (typeof (price_set) != 'undefined' && price_set != null) {
			      		for (var i=0; i < price_set.childNodes.length; i++) {
			      			var price_sel = price_set.getElementsByTagName("price")[i];

			      			price_values = price_sel.firstChild.nodeValue;
			      			var cur_price_input = DOMCall('update_price_'+price_sel.getAttribute("id"));
			      			if (cur_price_input != null) {
			      				cur_price_input.value = price_values;
			      				curPrice['update_price_'+price_sel.getAttribute("id")] = price_values;

			      				cur_price_input.onkeyup=function() {
									if (trim_str(this.value) != '' && !currencyValidation(trim_str(this.value))) {
										this.value = '';
									}
								}
								cur_price_input.onblur=function() {
									if (parseFloat(this.value!='' ? this.value : 0) != parseFloat((curPrice[this.id] != null && curPrice[this.id] != '' ? curPrice[this.id] : 0))) {
										selObj.selectedIndex=0;
									}
								}
			      			}
					    }

					    selObj.disabled = false;
		      		}
		      	}
		    }

		    xmlhttp.send(null);
		}
	}
}

function updateSetOptions() {
	refreshPriceSetOptions('template_listing_form', 'price_set');
}

function refreshPriceSetOptions(frmName, curname) {
	var cur_sel_obj = null;
	var cur_sel_val = '';

	var latest_price_set = new Array();

	frm = document.forms[frmName];
	ele_len = frm.elements.length;

	var msg = 'Please wait... Updating price set options.';
	showMainInfo(msg);

	if (ele_len > 0) {
		var ref_url = "product_listing_xmlhttp.php?action=get_price_selection";
		xmlhttp.open("GET", ref_url);
	    xmlhttp.onreadystatechange = function() {
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
	      		//res = xmlhttp.responseText;

	      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];

	      		if (typeof (selection) != 'undefined' && selection != null) {
	      			var option_sel = '';
		      		for (var i=0; i < selection.childNodes.length; i++) {
		      			option_sel = selection.getElementsByTagName("option")[i];

		      			latest_price_set[i] = new Array();
		      			latest_price_set[i]['id'] = option_sel.getAttribute("index");
		      			latest_price_set[i]['text'] = option_sel.firstChild.nodeValue;
		      			latest_price_set[i]['param'] = option_sel.getAttribute("disabled");
				    }
	      		}

	      		var i=0;
				for( i=0 ; i < ele_len ; i++) {
					if (frm.elements[i].type.indexOf("select") === 0 && frm.elements[i].name.indexOf("price_set") === 0) {
						cur_sel_obj = frm.elements[i];
						cur_sel_val = cur_sel_obj.options[cur_sel_obj.selectedIndex].value;

						cur_sel_obj.disabled = true;

						clearOptionList(cur_sel_obj);

						for (var loop=0; loop < latest_price_set.length; loop++) {
							appendToSelect(cur_sel_obj, latest_price_set[loop]['id'], latest_price_set[loop]['text'], latest_price_set[loop]['param'], (latest_price_set[loop]['id'].toString() == cur_sel_val.toString() ? 1 : 0));
						}

						cur_sel_obj.disabled = false;
					}
				}

				hideMainInfo();
	      	}
	    }

	    xmlhttp.send(null);
    }

    setTimeout('hideMainInfo()', 3000);
}