var xmlhttp=loadXMLHTTP();
var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
stateSelectionTitle['text'] = 'Please Select';

function loadXMLHTTP() {
	try { 
		varXMLHTTP = new ActiveXObject("Msxml2.XMLHTTP"); 
	}
	
	catch (e) { 
		try { 
			varXMLHTTP = new ActiveXObject("Microsoft.XMLHTTP"); 
		}
		catch (E) { 
			varXMLHTTP = false; 
		}
	}
	
	if (!xmlhttp && typeof XMLHttpRequest!='undefined') { 
		try {
			varXMLHTTP = new XMLHttpRequest(); 
		}
		catch (ex) {
			varXMLHTTP = false;		
		}
	}
	
	return varXMLHTTP;
}

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');
    
    objBody.appendChild(objContainer);
}

function appendToSelect(select, value, content, disabledMode) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content.replace(/&nbsp;/ig, String.fromCharCode(160));
	if (disabledMode != null) {
		opt.setAttribute('disabled', (disabledMode==1 ? true : false));
	}
	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	var objInfo = DOMCall('infocaption');
	
	if (DisplayMsg == null || objInfo == null) {
		return;
	}
	
	var scroll = getScroll();
	
	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';
	
	objInfo.style.position = "absolute";
	
	objInfo.style.top = 5 + scroll.y;
	
	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}


function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

function orderListsOptions(selObj, sid, _lang, o, wl) {
	var msg = '';
	var rExp = /_tag_selector/gi;
	var status_name = selObj.name.replace(rExp, '');
	var option_action = selObj.value;
	if (o != null && o != '') {
		var selected_order = new Array(o);
	} else {
		var selected_order = new Array();
		var order_str_obj = DOMCall(status_name + '_prd_str');
		if (order_str_obj != null) {
			var order_product_ids_array = order_str_obj.value.split(',');
			for (i=1; i <= order_product_ids_array.length; i++) {
				var order_row = DOMCall(status_name + '_main_' + i);
				if (order_row != null && order_row.className == "rowSelected") {
					selected_order.push(order_product_ids_array[i-1]);
				}
			}
		}
	}
	var selected_order_str = selected_order.join(',');
	var act = '';
	var s_val = '';
	switch (option_action) {
		case 'nt':	// create new tag
			if (selected_order.length > 0) {
				var new_tag_name = prompt("Please enter a new tag name","");
				// check to see if anything was entered
                // null == pressing cancel
                // ""   == entered nothing and pressing ok
				while (new_tag_name != null && trim_str(new_tag_name) == '') {
					new_tag_name = prompt("Please enter a new tag name","");
				}
				if (new_tag_name == null) {
					selObj.selectedIndex = 0;
					return;
				}
				act = 'perform_tagging';
				s_val = trim_str(new_tag_name);
			} else {
				msg = 'No warning record selected.';
				showMainInfo(msg);
				selObj.selectedIndex = 0;
			}
			break;
		default:
			if (option_action.indexOf('otag_') != -1) {
				if (selected_order.length > 0) {
					var temp_array = option_action.split('_');
					option_action = 'at';
					s_val = temp_array[1];
					act = 'perform_tagging';
				} else {
					msg = 'No warning record selected.';
					showMainInfo(msg);
					selObj.selectedIndex = 0;
				}
			} else if (option_action.indexOf('rmtag_') != -1) {
				if (selected_order.length > 0) {
					var temp_array = option_action.split('_');
					option_action = 'rt';
					s_val = temp_array[1];
					act = 'perform_tagging';
				} else {
					msg = 'No warning record selected.';
					showMainInfo(msg);
					selObj.selectedIndex = 0;
				}
			}
			break;
	}
	
	if (trim_str(act) != '') {
		selObj.disabled = true;
	   	clearOptionList(selObj);
		selObj.options[0] = new Option('Loading ...', '');
		var ref_url = "products_low_stock_xmlhttp.php?action="+act+"&subaction="+option_action+"&status_id="+sid+"&setting="+s_val+"&o_str="+selected_order_str+"&lang="+_lang+"&list_mode="+(wl == true ? '2' : '1');
		xmlhttp.open("GET", ref_url); 
	    xmlhttp.onreadystatechange = function() {
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
	      		//res = xmlhttp.responseText;
	      		//alert(res);
	      		
	      		clearOptionList(selObj);
	      		var tag_info = xmlhttp.responseXML.getElementsByTagName("tag_info")[0];
	      		if (typeof (tag_info) != 'undefined' && tag_info != null) {
		      		var selection = tag_info.getElementsByTagName("selection")[0];
		      		if (typeof (selection) != 'undefined' && selection != null) {
		      			var option_sel = '';
			      		for (var i=0; i < selection.childNodes.length; i++) {
			      			option_sel = selection.getElementsByTagName("option")[i];
			      			appendToSelect(selObj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
					    }
		      		}
		      		
		      		var tag_details = tag_info.getElementsByTagName("tag_details")[0];
		      		if (typeof (tag_details) != 'undefined' && tag_details != null) {
		      			var order_tags = '';
			      		for (var i=0; i < tag_details.childNodes.length; i++) {
			      			order_tags = tag_details.getElementsByTagName("order_tags")[i];
			      			var cus_tag_str = DOMCall('tag_'+order_tags.getAttribute("order_id"));
			      			if (cus_tag_str != null) {
			      				cus_tag_str.innerHTML = order_tags.firstChild.nodeValue;
			      			}
					    }
		      		}
		      	}
	      		selObj.disabled = false;
	      	}
	    }
	    xmlhttp.send(null);
	}
	setTimeout('hideMainInfo()', 3000);
}

function refreshOrderListsOptions(status_name, _lang, o) {
	//isProcessing = true;
	var tag_selector_obj = DOMCall(status_name + '_tag_selector');
	if (o != null) {
		var selected_order = new Array(o);
	} else {
		var order_str_obj = DOMCall(status_name + '_prd_str');
		var selected_order = new Array();
		if (order_str_obj != null) {
			var order_product_ids_array = order_str_obj.value.split(',');
			for (i=0; i < order_product_ids_array.length; i++) {
				var order_row = DOMCall(status_name + '_main_' + i);
				if (order_row != null && order_row.className == "rowSelected") {
					selected_order.push(order_product_ids_array[i]);
				}
			}
		}
	}
	var selected_order_str = selected_order.join(',');
	
	if (tag_selector_obj != null) {
		tag_selector_obj.disabled = true;
	   	clearOptionList(tag_selector_obj);
		tag_selector_obj.options[0] = new Option('Loading ...', '');
		
		var ref_url = "custom_product_xmlhttp.php?action=refresh_tag_selection&status="+status_name+"&o_str="+selected_order_str+"&lang="+_lang;
		xmlhttp.open("GET", ref_url); 
		
	    xmlhttp.onreadystatechange = function() {
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
	      		//res = xmlhttp.responseText;
	      		//alert(res);
	      		
	      		clearOptionList(tag_selector_obj);
	      		
	      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];
	      		
	      		if (typeof (selection) != 'undefined' && selection != null) {
	      			var option_sel = '';
		      		for (var i=0; i < selection.childNodes.length; i++) {
		      			option_sel = selection.getElementsByTagName("option")[i];
		      			appendToSelect(tag_selector_obj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
				    }
	      		}
	      		tag_selector_obj.disabled = false;
	      		
	      		//cursor_clear();
	      		//isProcessing = false;
	      	}
	    }
	    xmlhttp.send(null);
	}
}

function fetchStatusTagSelections(status_id, div_objRef, sel, _lang) {
	div_objRef = DOMCall(div_objRef);
	objRef = DOMCall(sel);
	if (objRef != null) {
		if (status_id > 0) {
			clearOptionList(objRef);
			objRef.options[0] = new Option('Loading ...', '');
			
			var ref_url = "custom_product_xmlhttp.php?action=retrieve_status_tags&status="+status_id+"&lang="+_lang;
			xmlhttp.open("GET", ref_url); 
			
		    xmlhttp.onreadystatechange = function() {
		      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
		      		clearOptionList(objRef);
		      		
		      		var selection = xmlhttp.responseXML.getElementsByTagName("selection")[0];
		      		
		      		if (typeof (selection) != 'undefined' && selection != null) {
		      			var option_sel = '';
			      		for (var i=0; i < selection.childNodes.length; i++) {
			      			option_sel = selection.getElementsByTagName("option")[i];
			      			appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue, option_sel.getAttribute("disabled"));
					    }
		      		}
		      		objRef.disabled = false;
		      		div_objRef.className = 'show';
		      	}
		    }
		    xmlhttp.send(null);
		} else {
			clearOptionList(objRef);
			div_objRef.className = "hide";
		}
	}
}