<SCRIPT language="JavaScript" type="text/javascript">
<!--
jQuery.noConflict();

var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
if (typeof(pls_select) != 'undefined' && pls_select != null) {
	stateSelectionTitle['text'] = pls_select;
} else {
	stateSelectionTitle['text'] = 'Please Select';
}

function updateRedeem(file_name, btn_obj, r_id, fr_status, to_status) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	//var exRate = DOMCall('exchange_rate_' + r_id);
	//var payRef = DOMCall('payment_reference_' + r_id);
	var btn_old_value = '';
	var res;
    var server_action = 'update_redeem';
   	
    btn_obj.disabled = true;
    btn_old_value = btn_obj.value;
    btn_obj.value = 'Please wait';
    
    //var exRateValue = exRate != null ? URLEncode(exRate.value) : 0;
    //var payRefValue = payRef != null ? URLEncode(payRef.value) : '';
    
	var ref_url = "redeem_xmlhttp.php?action="+server_action+"&r_id="+r_id+"&fr_status="+fr_status+"&to_status="+to_status+"&filename="+file_name;
	xmlhttp.open('GET', ref_url); 
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
      		//res = xmlhttp.responseText;
      		//alert(res);
      		
			var res_code = typeof (xmlhttp.responseXML.getElementsByTagName('res_code')[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName('res_code')[0] != null ? xmlhttp.responseXML.getElementsByTagName('res_code')[0].firstChild.data : '';
			var res_msg = typeof (xmlhttp.responseXML.getElementsByTagName('res_message')[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName('res_message')[0] != null ? xmlhttp.responseXML.getElementsByTagName('res_message')[0].firstChild.data : '';
			
			if (res_code == 1) {
				if (res_msg != '') {
					alert(res_msg);
				}
				
				var thisRow = btn_obj.parentNode.parentNode;
	      		var oldRow = thisRow;
	      		var oldRowIndex = thisRow.sectionRowIndex;
	      		
	      		var thisTbody = thisRow.parentNode;
	      		var thisTable = thisTbody.parentNode;
				
				thisTbody.deleteRow(thisRow.sectionRowIndex);
				
      			/* Get the latest row contents*/
				var row_style = (oldRowIndex % 2) ? 'reportListingEven' : 'reportListingOdd' ;
				
	      		myNewRow = thisTbody.insertRow(oldRowIndex);
	      		myNewRow.className = row_style.toString();
	      		myNewRow.setAttribute('height', 20);
	      		myNewRow.onmouseover=function(){ rowOverEffect(this, 'reportListingRowOver'); }
	      		myNewRow.onmouseout=function(){ rowOutEffect(this, row_style); }
				myNewRow.onclick=function(){ rowClicked(this, row_style) }
				
				var table_cell = xmlhttp.responseXML.getElementsByTagName("table_cell")[0];
	      		
	      		if (typeof(table_cell) != 'undefined' && table_cell != null) {
	      			var cell_obj = '';
		      		for (var cell_cnt=0; cell_cnt < table_cell.childNodes.length; cell_cnt++) {
		      			cell_obj = table_cell.getElementsByTagName("cell")[cell_cnt];
		      			//appendToSelect(objRef, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
		      			
		      			var myNewCell = myNewRow.insertCell(myNewRow.cells.length);
						myNewCell.className = 'reportRecords';
						myNewCell.setAttribute('valign', 'top');
						
						var attribute_str = cell_obj.getAttribute("property");
						if (attribute_str != null) {
							var value_pair = attribute_str.split('&');
							for (var pair_cnt=0; pair_cnt < value_pair.length; pair_cnt++) {
								var key_value = value_pair[pair_cnt].split('=');
								myNewCell.setAttribute(key_value[0], key_value[1]);
							}
						}
						
			   			myNewCell.innerHTML = cell_obj.firstChild.nodeValue;
				    }
	      		}
			} else {
      			alert(res_msg);
      			
      			btn_obj.value = btn_old_value;
      			btn_obj.disabled = false;
      		}
      	}
    }
    
    xmlhttp.send(null);
    
    setTimeout('hideMainInfo()', 3000);
}
//--></SCRIPT>