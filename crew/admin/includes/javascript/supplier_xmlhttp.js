var xmlhttp=loadXMLHTTP();
var stateSelectionTitle = Array();
stateSelectionTitle['id'] = '';
stateSelectionTitle['text'] = 'Please Select';

function loadXMLHTTP() {
	try { 
		varXMLHTTP = new ActiveXObject("Msxml2.XMLHTTP"); 
	}
	
	catch (e) { 
		try { 
			varXMLHTTP = new ActiveXObject("Microsoft.XMLHTTP"); 
		}
		catch (E) { 
			varXMLHTTP = false; 
		}
	}
	
	if (!xmlhttp && typeof XMLHttpRequest!='undefined') { 
		try {
			varXMLHTTP = new XMLHttpRequest(); 
		}
		catch (ex) {
			varXMLHTTP = false;		
		}
	}
	
	return varXMLHTTP;
}

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');
    
    objBody.appendChild(objContainer);
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	if (typeof DisplayMsg == 'undefined') {
		return;
	}
	var objInfo = DOMCall('infocaption');
	var scroll = getScroll();
	
	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';
	
	objInfo.style.position = "absolute";
	
	objInfo.style.top = 5 + scroll.y;
	
	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}

function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

function preview_suggested_max_qty(products_id, SID) {
    var sales_start_date = document.getElementById('txtSalesStartDate').value;
    var sales_end_date = document.getElementById('txtSalesEndDate').value;
    var inventory_days = document.getElementById('txtInventoryDays').value;
    var last_n_days_sales = document.getElementById('txtLastNDaysSales').value;
    var sales_retrieval_method1 = document.getElementById('rdoSalesRetrievalMethod1').value;
    var sales_retrieval_method2 = document.getElementById('rdoSalesRetrievalMethod2').value;
    
    if (document.getElementById('rdoSalesRetrievalMethod1').checked == true) {
        var sales_retrieval_method = sales_retrieval_method1;
        if (trim_str(sales_start_date) == '') {
            alert('Please specify sales start date.');
            return;
        }
    } else if (document.getElementById('rdoSalesRetrievalMethod2').checked == true) {
        var sales_retrieval_method = sales_retrieval_method2;
        if (trim_str(last_n_days_sales) == '') {
            alert('Please specify Last [X] days sales.');
            return;
        }
    }
	
    var server_action = 'calculate_max_inventory_space';
    var ref_url = "supplier_xmlhttp.php?action="+server_action+"&products_id="+products_id+"&sales_start_date="+sales_start_date+"&sales_end_date="+sales_end_date+"&inventory_days="+inventory_days+"&last_n_days_sales="+last_n_days_sales+"&sales_retrieval_method="+sales_retrieval_method+"&SID="+SID;
	var msg = 'Loading... Please be patient!';
	showMainInfo(msg);
	
	xmlhttp.open("GET", ref_url);
	
    xmlhttp.onreadystatechange = function() {
     	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
        	
      		var max_inv_space_derived = xmlhttp.responseXML.getElementsByTagName("max_inv_space_derived")[0].firstChild.nodeValue;
            document.getElementById('spanMaxInvSpaceDerived').innerHTML = '<b>'+max_inv_space_derived+'</b>';
      		var max_buyback_qty = xmlhttp.responseXML.getElementsByTagName("max_buyback_qty")[0].firstChild.nodeValue;
            document.getElementById('spanMaxBuyBackQty').innerHTML = '<b>'+max_buyback_qty+'</b>';
            document.getElementById('hidMaxQtySystem').value = max_buyback_qty;
			hideMainInfo();
      	}
    }
	xmlhttp.send(null);
}

function buyback_qty_by_supplier(search_id, search_type, supply_date_from, supply_date_to, supply_from, supply_status) {
    var server_action = 'qty_stat_by_supplier';
    var search_by_query = (search_type == 'Product') ? 'products_id='+search_id : 'cat_id='+search_id;
    var ref_url = "supplier_xmlhttp.php?action="+server_action+"&"+search_by_query+"&supply_date_from="+supply_date_from+"&supply_date_to="+supply_date_to+"&supply_from="+supply_from+"&supply_status="+supply_status;
    var result_html = '';
    var total_qty = 0;
    
	/*
	var msg = 'Loading... Please be patient!';
	showMainInfo(msg);
	*/
	
	xmlhttp.open("GET", ref_url);
	
    xmlhttp.onreadystatechange = function() {
     	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
      		
        	result_html += '<table border="0" cellspacing="1" cellpadding="2" width="100%">';
        	result_html += '<tr><td width="20%" class="reportBoxHeading">Name</td><td width="21%" class="reportBoxHeading">E-mail</td><td width="20%" class="reportBoxHeading">Mobile Number</td><td width="19%" class="reportBoxHeading">QQ Number</td><td width="20%" align="center" class="reportBoxHeading">Qty</td></tr>';
        	
      		var cn_sec = xmlhttp.responseXML.getElementsByTagName("cn_buy_mod");
      		if (typeof(cn_sec) != 'undefined' && cn_sec != null) {
      			var rec_obj = '';
      			result_html += '<tr><td class="reportBoxHeading" colspan="5"><b>China Buyback:</b></td></tr>';
      			for (var rec_cnt=0; rec_cnt < cn_sec.length; rec_cnt++) {
      				var row_style = (rec_cnt%2) ? 'reportListingEven' : 'reportListingOdd';
      				
      				total_qty += parseInt(cn_sec[rec_cnt].getElementsByTagName("qty")[0].firstChild.data, 10);
      				
	      			result_html += '<tr class="'+row_style+'">';
	      			result_html += '<td class="reportRecords">'+cn_sec[rec_cnt].getElementsByTagName("name")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+cn_sec[rec_cnt].getElementsByTagName("email")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+cn_sec[rec_cnt].getElementsByTagName("mobile")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+cn_sec[rec_cnt].getElementsByTagName("qq")[0].firstChild.data+'</td>';
	      			result_html += '<td align="center" class="reportRecords">'+cn_sec[rec_cnt].getElementsByTagName("qty")[0].firstChild.data+'</td>';
	      			result_html += '</tr>';
	      		}
	      		
	      		result_html += '<tr><td colspan="3"></td></tr>';
      		}
      		
      		var web_sec = xmlhttp.responseXML.getElementsByTagName("web_us_mod");
      		if (typeof(web_sec) != 'undefined' && web_sec != null) {
      			var rec_obj = '';
      			result_html += '<tr><td class="reportBoxHeading" colspan="5"><b>Website Buyback:</b></td></tr>';
      			
      			for (var rec_cnt=0; rec_cnt < web_sec.length; rec_cnt++) {
	      			var row_style = (rec_cnt%2) ? 'reportListingEven' : 'reportListingOdd';
      				
      				total_qty += parseInt(web_sec[rec_cnt].getElementsByTagName("qty")[0].firstChild.data, 10);
      				
	      			result_html += '<tr class="'+row_style+'">';
	      			result_html += '<td class="reportRecords">'+web_sec[rec_cnt].getElementsByTagName("name")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+web_sec[rec_cnt].getElementsByTagName("email")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+web_sec[rec_cnt].getElementsByTagName("mobile")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+web_sec[rec_cnt].getElementsByTagName("qq")[0].firstChild.data+'</td>';
	      			result_html += '<td align="center" class="reportRecords">'+web_sec[rec_cnt].getElementsByTagName("qty")[0].firstChild.data+'</td>';
	      			result_html += '</tr>';
	      		}
	      		result_html += '<tr><td colspan="3"></td></tr>';
      		}
      		
      		var sup_sec = xmlhttp.responseXML.getElementsByTagName("sup_mod");
      		if (typeof(sup_sec) != 'undefined' && sup_sec != null) {
      			var rec_obj = '';
      			result_html += '<tr><td class="reportBoxHeading" colspan="5"><b>Supplier Module:</b></td></tr>';
      			
      			for (var rec_cnt=0; rec_cnt < sup_sec.length; rec_cnt++) {
	      			var row_style = (rec_cnt%2) ? 'reportListingEven' : 'reportListingOdd';
      				
      				total_qty += parseInt(sup_sec[rec_cnt].getElementsByTagName("qty")[0].firstChild.data, 10);
      				
	      			result_html += '<tr class="'+row_style+'">';
	      			result_html += '<td class="reportRecords">'+sup_sec[rec_cnt].getElementsByTagName("name")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+sup_sec[rec_cnt].getElementsByTagName("email")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+sup_sec[rec_cnt].getElementsByTagName("mobile")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+sup_sec[rec_cnt].getElementsByTagName("qq")[0].firstChild.data+'</td>';
	      			result_html += '<td align="center" class="reportRecords">'+sup_sec[rec_cnt].getElementsByTagName("qty")[0].firstChild.data+'</td>';
	      			result_html += '</tr>';
	      		}
	      		result_html += '<tr><td colspan="3"></td></tr>';
      		}
      		
      		result_html += '<tr><td align="right" class="reportRecords" colspan="4">Total:</td><td align="center" class="reportRecords">'+total_qty+'</td></tr></table>';
      		
        	new popUp(300, f_scrollTop()+200, 600, 500, search_type+search_id+supply_date_from+supply_date_to, result_html, "white", "#00385c", "9pt sans-serif", search_type+search_id+'-'+supply_date_from+'-'+supply_date_to, "#00385c", "white", "lightgrey", "#00568c", "black", true, true, false, true, false, false, '', '', 'cir_close.gif', 'resize.gif');
        	
      		//hideMainInfo();
      	}
    }
	xmlhttp.send(null);
	
	//setTimeout('hideMainInfo()', 3000);
}

function buyback_supplier_info(subaction, products_id, orders_products_id, start_date, end_date) {
	var server_action = 'supplier_info';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&subaction="+subaction+"&pID="+products_id+"&opID="+orders_products_id;
	var result_html = '';
	var total_qty = 0;
	var website_html = '';
	var china_html = '';
	
	xmlhttp.open("GET", ref_url);
	
	xmlhttp.onreadystatechange = function() {
		if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
			result_html += '<table border="0" cellspacing="1" cellpadding="2" width="100%">';
        	result_html += '<tr>';
        	result_html += '	<td width="10%" class="reportBoxHeading">Date</td>';
			result_html += '	<td width="10%" class="reportBoxHeading">Name</td>';
			result_html += '	<td width="15%" class="reportBoxHeading">E-mail</td>';
			result_html += '	<td width="12%" class="reportBoxHeading">Phone Number</td>';
			result_html += '	<td width="12%" class="reportBoxHeading">Mobile Number</td>';
			result_html += '	<td width="11%" class="reportBoxHeading">QQ</td>';
			result_html += '	<td width="10%" class="reportBoxHeading">MSN</td>';
			result_html += '	<td width="10%" class="reportBoxHeading">Yahoo</td>';
			result_html += '	<td width="10%" class="reportBoxHeading">ICQ</td>';
			result_html += '	<td width="10%" class="reportBoxHeading">Supplier Quantity</td>';
			result_html += '</tr>';
			
			if (subaction == 'buyback_history') {
				website_html += '<tr>';
				website_html += '<td width="100%" class="reportBoxHeading" colspan="10">Website Buyback:</td>';
				website_html += '</tr>';
				
				china_html += '<tr>';
				china_html += '<td width="100%" class="reportBoxHeading" colspan="10">China Buyback:</td>';
				china_html += '</tr>';
				
				china_cnt = 0;
				website_cnt = 0;
			}
			
			var supplier_detail = xmlhttp.responseXML.getElementsByTagName("supplier");
			if (typeof(supplier_detail) != 'undefined' && supplier_detail != null) {
				for (var rec_cnt=0; rec_cnt < supplier_detail.length; rec_cnt++) {
					if (subaction == 'buyback_history') {
						var site = supplier_detail[rec_cnt].getElementsByTagName("site")[0].firstChild.data;
						var row_style = (site == 0 ? website_cnt%2 : china_cnt%2) ? 'reportListingEven' : 'reportListingOdd';
						if (site == 0 && website_cnt == 0) {
							result_html += website_html;
						} else if (site == 1 && china_cnt == 0) {
							result_html += china_html;
						}
					} else {
						var row_style = (rec_cnt%2) ? 'reportListingEven' : 'reportListingOdd';
					}
					
					total_qty += parseInt(supplier_detail[rec_cnt].getElementsByTagName("quantity")[0].firstChild.data, 10);
					
					result_html += '<tr class="'+row_style+'">';
					result_html += '<td class="reportRecords">'+supplier_detail[rec_cnt].getElementsByTagName("date")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+supplier_detail[rec_cnt].getElementsByTagName("name")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+supplier_detail[rec_cnt].getElementsByTagName("email")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+supplier_detail[rec_cnt].getElementsByTagName("phone")[0].firstChild.data+'</td>';
	      			result_html += '<td class="reportRecords">'+supplier_detail[rec_cnt].getElementsByTagName("mobile")[0].firstChild.data+'</td>';
					var qq = supplier_detail[rec_cnt].getElementsByTagName("qq")[0].firstChild.data;
					result_html += '<td class="reportRecords">';
					if (qq != '&nbsp;') {
						result_html += '<a href="tencent://message/?uin='+qq+'&amp;Menu=yes"><img src="http://wpa.qq.com/pa?p=1:'+qq+':1" alt="message" align="absmiddle" border="0"></a>';
					}
					result_html += '</td>';
					result_html += '<td class="reportRecords">'+supplier_detail[rec_cnt].getElementsByTagName("msn")[0].firstChild.data+'</td>';
					result_html += '<td class="reportRecords">'+supplier_detail[rec_cnt].getElementsByTagName("yahoo")[0].firstChild.data+'</td>';
					result_html += '<td class="reportRecords">'+supplier_detail[rec_cnt].getElementsByTagName("icq")[0].firstChild.data+'</td>';
	      			result_html += '<td align="center" class="reportRecords">'+supplier_detail[rec_cnt].getElementsByTagName("quantity")[0].firstChild.data+'</td>';
	      			result_html += '</tr>';
	      			if (subaction == 'buyback_history') {
		      			if (site == 0) {
		      				website_cnt++;
		      			} else {
		      				china_cnt++;
		      			}
		      		}
				}
			}
			result_html += '<tr>';
			result_html += '	<td align="right" class="reportRecords" colspan="9">Total:</td>';
			result_html += '	<td align="center" class="reportRecords">'+total_qty+'</td>';
			result_html += '</tr></table>';
						
			new popUp(300, f_scrollTop()+200, 800, 500, 'Suppliers_Details', result_html, "white", "#00385c", "9pt sans-serif", 'Suppliers Details', "#00385c", "white", "lightgrey", "#00568c", "black", true, true, false, true, false, false, '', '', 'cir_close.gif', 'resize.gif');
		}
	}
	xmlhttp.send(null);
}