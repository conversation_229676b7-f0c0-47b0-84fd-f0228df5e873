tinyMCE.addI18n('en.style_dlg',{
title:"Edit CSS Style",
apply:"Apply",
text_tab:"Text",
background_tab:"Background",
block_tab:"Block",
box_tab:"Box",
border_tab:"Border",
list_tab:"List",
positioning_tab:"Positioning",
text_props:"Text",
text_font:"Font",
text_size:"Size",
text_weight:"Weight",
text_style:"Style",
text_variant:"Variant",
text_lineheight:"Line height",
text_case:"Case",
text_color:"Color",
text_decoration:"Decoration",
text_overline:"overline",
text_underline:"underline",
text_striketrough:"strikethrough",
text_blink:"blink",
text_none:"none",
background_color:"Background color",
background_image:"Background image",
background_repeat:"Repeat",
background_attachment:"Attachment",
background_hpos:"Horizontal position",
background_vpos:"Vertical position",
block_wordspacing:"Word spacing",
block_letterspacing:"Letter spacing",
block_vertical_alignment:"Vertical alignment",
block_text_align:"Text align",
block_text_indent:"Text indent",
block_whitespace:"Whitespace",
block_display:"Display",
box_width:"Width",
box_height:"Height",
box_float:"Float",
box_clear:"Clear",
padding:"Padding",
same:"Same for all",
top:"Top",
right:"Right",
bottom:"Bottom",
left:"Left",
margin:"Margin",
style:"Style",
width:"Width",
height:"Height",
color:"Color",
list_type:"Type",
bullet_image:"Bullet image",
position:"Position",
positioning_type:"Type",
visibility:"Visibility",
zindex:"Z-index",
overflow:"Overflow",
placement:"Placement",
clip:"Clip"
});