<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>{#xhtmlxtras_dlg.attribs_title}</title>
	<script type="text/javascript" src="../../tiny_mce_popup.js"></script>
	<script type="text/javascript" src="../../utils/mctabs.js"></script>
	<script type="text/javascript" src="../../utils/editable_selects.js"></script>
	<script type="text/javascript" src="../../utils/form_utils.js"></script>
	<script type="text/javascript" src="js/attributes.js"></script>
	<link rel="stylesheet" type="text/css" href="css/attributes.css" />
	<base target="_self" />
</head>
<body style="display: none">
<form onsubmit="insertAction();return false;" action="#">
	<div class="tabs">
		<ul>
			<li id="general_tab" class="current"><span><a href="javascript:mcTabs.displayTab('general_tab','general_panel');" onmousedown="return false;">{#xhtmlxtras_dlg.attribute_attrib_tab}</a></span></li>
			<li id="events_tab"><span><a href="javascript:mcTabs.displayTab('events_tab','events_panel');" onmousedown="return false;">{#xhtmlxtras_dlg.attribute_events_tab}</a></span></li>
		</ul>
	</div>

	<div class="panel_wrapper">
		<div id="general_panel" class="panel current">
			<fieldset>
				<legend>{#xhtmlxtras_dlg.attribute_attrib_tab}</legend>
				<table border="0" cellpadding="0" cellspacing="4">
					<tr>
						<td class="label"><label id="titlelabel" for="title">{#xhtmlxtras_dlg.attribute_label_title}</label>:</td> 
						<td><input id="title" name="title" type="text" value="" /></td> 
					</tr>
					<tr>
						<td class="label"><label id="idlabel" for="id">{#xhtmlxtras_dlg.attribute_label_id}</label>:</td> 
						<td><input id="id" name="id" type="text" value="" /></td> 
					</tr>
					<tr>
						<td><label id="classlabel" for="classlist">{#class_name}</label></td>
						<td>
							<select id="classlist" name="classlist" class="mceEditableSelect">
								<option value="" selected>{#not_set}</option>
							</select>
						</td>
					</tr>
					<tr>
						<td class="label"><label id="stylelabel" for="class">{#xhtmlxtras_dlg.attribute_label_style}</label>:</td> 
						<td><input id="style" name="style" type="text" value="" /></td> 
					</tr>
					<tr>
						<td class="label"><label id="dirlabel" for="dir">{#xhtmlxtras_dlg.attribute_label_langdir}</label>:</td> 
						<td>
							<select id="dir" name="dir"> 
								<option value="">{#not_set}</option> 
								<option value="ltr">{#xhtmlxtras_dlg.option_ltr}</option> 
								<option value="rtl">{#xhtmlxtras_dlg.option_rtl}</option> 
							</select>
						</td> 
					</tr>
					<tr>
						<td class="label"><label id="langlabel" for="lang">{#xhtmlxtras_dlg.attribute_label_langcode}</label>:</td> 
						<td>
							<input id="lang" name="lang" type="text" value="" />
						</td> 
					</tr>
					<tr>
							<td><label id="tabindexlabel" for="tabindex">{#xhtmlxtras_dlg.attribute_label_tabindex}</label></td>
							<td><input type="text" id="tabindex" name="tabindex" value="" /></td>
						</tr>

						<tr>
							<td><label id="accesskeylabel" for="accesskey">{#xhtmlxtras_dlg.attribute_label_accesskey}</label></td>
							<td><input type="text" id="accesskey" name="accesskey" value="" /></td>
						</tr>
				</table>
			</fieldset>
		</div>
		<div id="events_panel" class="panel">
			<fieldset>
				<legend>{#xhtmlxtras_dlg.attribute_events_tab}</legend>

				<table border="0" cellpadding="0" cellspacing="4">
					<tr>
						<td class="label"><label for="onfocus">onfocus</label>:</td> 
						<td><input id="onfocus" name="onfocus" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onblur">onblur</label>:</td> 
						<td><input id="onblur" name="onblur" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onclick">onclick</label>:</td> 
						<td><input id="onclick" name="onclick" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="ondblclick">ondblclick</label>:</td> 
						<td><input id="ondblclick" name="ondblclick" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onmousedown">onmousedown</label>:</td> 
						<td><input id="onmousedown" name="onmousedown" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onmouseup">onmouseup</label>:</td> 
						<td><input id="onmouseup" name="onmouseup" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onmouseover">onmouseover</label>:</td> 
						<td><input id="onmouseover" name="onmouseover" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onmousemove">onmousemove</label>:</td> 
						<td><input id="onmousemove" name="onmousemove" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onmouseout">onmouseout</label>:</td> 
						<td><input id="onmouseout" name="onmouseout" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onkeypress">onkeypress</label>:</td> 
						<td><input id="onkeypress" name="onkeypress" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onkeydown">onkeydown</label>:</td> 
						<td><input id="onkeydown" name="onkeydown" type="text" value="" /></td> 
					</tr>

					<tr>
						<td class="label"><label for="onkeyup">onkeyup</label>:</td> 
						<td><input id="onkeyup" name="onkeyup" type="text" value="" /></td> 
					</tr>
				</table>
			</fieldset>
		</div>
	</div>
	<div class="mceActionPanel">
		<div style="float: left">
			<input type="button" id="insert" name="insert" value="{#insert}" onclick="insertAction();" />
		</div>
		<div style="float: right">
			<input type="button" id="cancel" name="cancel" value="{#cancel}" onclick="tinyMCEPopup.close();" />
		</div>
	</div>

</form>
</body>
</html>
