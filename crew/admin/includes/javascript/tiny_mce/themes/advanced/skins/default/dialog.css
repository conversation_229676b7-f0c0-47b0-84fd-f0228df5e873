/* Generic */
body {
font-family:Verdana, Arial, Helvetica, sans-serif; font-size:11px;
scrollbar-3dlight-color:#F0F0EE;
scrollbar-arrow-color:#676662;
scrollbar-base-color:#F0F0EE;
scrollbar-darkshadow-color:#DDDDDD;
scrollbar-face-color:#E0E0DD;
scrollbar-highlight-color:#F0F0EE;
scrollbar-shadow-color:#F0F0EE;
scrollbar-track-color:#F5F5F5;
background:#F0F0EE;
padding:0;
margin:8px 8px 0 8px;
}

html {background:#F0F0EE;}
td {font-family:Verdana, Arial, Helvetica, sans-serif; font-size:10px;}
textarea {resize:none;outline:none;}
a:link, a:visited {color:black;}
a:hover {color:#2B6FB6;}

/* Forms */
fieldset {margin:0; padding:4px; border:1px solid #919B9C; font-family:<PERSON>erdana, <PERSON>l; font-size:10px;}
legend {color:#2B6FB6; font-weight:bold;}
label.msg {display:none;}
label.invalid {color:#EE0000; display:inline;}
input.invalid {border:1px solid #EE0000;}
input {background:#FFF; border:1px solid #CCC;}
input, select, textarea {font-family:Verdana, Arial, Helvetica, sans-serif; font-size:10px;}
input, select, textarea {border:1px solid #808080;}
input.radio {border:1px none #000000; background:transparent; vertical-align:middle;}
input.checkbox {border:1px none #000000; background:transparent; vertical-align:middle;}
.input_noborder {border:0;}

/* Buttons */
#insert, #cancel, input.button, .updateButton {
border:0; margin:0; padding:0;
font-weight:bold;
width:94px; height:26px;
background:url(img/buttons.png) 0 -26px;
cursor:pointer;
padding-bottom:2px;
}

#insert {background:url(img/buttons.png) 0 -52px;}
#cancel {background:url(img/buttons.png) 0 0;}

/* Browse */
a.browse span {display:block; width:20px; height:18px; background:url(../../img/icons.gif) -860px 0; border:1px solid #FFF; margin-left:1px;}
.mceOldBoxModel a.browse span {width:22px; height:20px;}
a.browse:hover span {border:1px solid #0A246A; background-color:#B2BBD0;}
a.browse span.disabled {border:1px solid white; -moz-opacity:0.3; opacity:0.3; filter:progid:DXImageTransform.Microsoft.Alpha(opacity=30);}
a.browse:hover span.disabled {border:1px solid white; background-color:transparent;}
a.pickcolor span {display:block; width:20px; height:16px; background:url(../../img/icons.gif) -840px 0; margin-left:2px;}
.mceOldBoxModel a.pickcolor span {width:21px; height:17px;}
a.pickcolor:hover span {background-color:#B2BBD0;}
a.pickcolor:hover span.disabled {}

/* Charmap */
table.charmap {border-style:solid; border-width:1px; border-color:#AAA;}
td.charmap, td.charmapOver {color:#000; border-color:#AAA; border-style:solid; border-width:1px; text-align:center; font-size:12px;}
td.charmapOver {background:#CCC; cursor:default;}
a.charmap {color:#000; text-decoration:none}

/* Source */
.wordWrapCode {vertical-align:middle; border:1px none #000000; background:transparent;}
.mceActionPanel {margin-top:5px;}

/* Tabs classes */
.tabs {width:100%; height:18px; line-height:normal; background:url(img/tabs.gif) repeat-x 0 -72px;}
.tabs ul {margin:0; padding:0; list-style:none;}
.tabs li {float:left; background:url(img/tabs.gif) no-repeat 0 0; margin:0 2px 0 0; padding:0 0 0 10px; line-height:18px;}
.tabs li.current {background:url(img/tabs.gif) no-repeat 0 -18px; margin-right:2px;}
.tabs span {float:left; display:block; background:url(img/tabs.gif) no-repeat right -36px; padding:0px 10px 0 0;}
.tabs .current span {background:url(img/tabs.gif) no-repeat right -54px;}
.tabs a {text-decoration:none; font-family:Verdana, Arial; font-size:10px;}
.tabs a:link, .tabs a:visited, .tabs a:hover {color:black;}

/* Panels */
.panel_wrapper div.panel {display:none;}
.panel_wrapper div.current {display:block; width:100%; height:300px; overflow:visible;}
.panel_wrapper {border:1px solid #919B9C; border-top:0px; padding:10px; padding-top:5px; clear:both; background:white;}

/* Columns */
.column {float:left;}
.properties {width:100%;}
.properties .column1 {}
.properties .column2 {text-align:left;}

/* Titles */
h1, h2, h3, h4 {color:#2B6FB6; margin:0; padding:0; padding-top:5px;}
h3 {font-size:14px;}
.title {font-size:12px; font-weight:bold; color:#2B6FB6;}

/* Dialog specific */
#link .panel_wrapper, #link div.current {height:125px;}
#image .panel_wrapper, #image div.current {height:200px;}
#plugintable thead {font-weight:bold; background:#DDD;}
#plugintable, #about #plugintable td {border:1px solid #919B9C;}
#plugintable {width:96%; margin-top:10px;}
#pluginscontainer {height:290px; overflow:auto;}
#colorpicker #preview {float:right; width:50px; height:14px;line-height:1px; border:1px solid black; margin-left:5px;}
#colorpicker #colors {float:left; border:1px solid gray; cursor:crosshair;}
#colorpicker #light {border:1px solid gray; margin-left:5px; float:left;width:15px; height:150px; cursor:crosshair;}
#colorpicker #light div {overflow:hidden;}
#colorpicker #previewblock {float:right; padding-left:10px; height:20px;}
#colorpicker .panel_wrapper div.current {height:175px;}
#colorpicker #namedcolors {width:150px;}
#colorpicker #namedcolors a {display:block; float:left; width:10px; height:10px; margin:1px 1px 0 0; overflow:hidden;}
#colorpicker #colornamecontainer {margin-top:5px;}
