var xmlhttp=loadXMLHTTP();

function loadXMLHTTP() {
	try {
		varXMLHTTP = new ActiveXObject("Msxml2.XMLHTTP");
	}

	catch (e) {
		try {
			varXMLHTTP = new ActiveXObject("Microsoft.XMLHTTP");
		}
		catch (E) {
			varXMLHTTP = false;
		}
	}

	if (!xmlhttp && typeof XMLHttpRequest!='undefined') {
		try {
			varXMLHTTP = new XMLHttpRequest();
		}
		catch (ex) {
			varXMLHTTP = false;
		}
	}

	return varXMLHTTP;
}

// add item to select element the less elegant, but compatible way.
function appendToSelect(select, value, content, disabledMode, selectedItem) {
	var opt = document.createElement('option');
	opt.value = value;
	opt.text = content.replace(/&nbsp;/ig, String.fromCharCode(160));
	if (disabledMode != null) {
		opt.setAttribute('disabled', (disabledMode==1 ? true : false));
	}
	if (selectedItem == 1) {
		opt.setAttribute('selected', true);
	}

	select.options.add(opt);
}

function clearOptionList(obj) {
	while (obj.options.length > 0) {
		obj.remove(0);
	}
}

function showMainInfo(DisplayMsg) {
	hideMainInfo();
	var objInfo = DOMCall('infocaption');
	
	if (DisplayMsg == null || objInfo == null) {
		return;
	}
	
	var scroll = getScroll();
	
	objInfo.innerHTML = '<div class="xmlHttpMainInfo">' + DisplayMsg + '</div>';
	objInfo.style.visibility = 'visible';
	
	objInfo.style.position = "absolute";
	
	objInfo.style.top = 5 + scroll.y;
	
	var msg_left_pos = getWindowWidth() - objInfo.offsetWidth;
	objInfo.style.left = msg_left_pos - 20;
}

function hideMainInfo() {
	var objInfo = DOMCall('infocaption');
	if (objInfo == null) {
		return;
	}
	objInfo.style.visibility = 'hidden';
	objInfo.innerHTML = '';
}

function getWindowWidth() {
	var windowWidth = 0;
	if (typeof(window.innerWidth) == 'number') {
		windowWidth = window.innerWidth;
	} else {
		if (document.documentElement && document.documentElement.clientWidth) {
			windowWidth = document.documentElement.clientWidth;
		} else {
			if (document.body && document.body.clientWidth) {
				windowWidth = document.body.clientWidth;
			}
		}
	}
	return windowWidth;
}

function getScroll() {
    if (document.body.scrollTop != undefined) { // IE model
        var ieBox = document.compatMode != "CSS1Compat";
        var cont = ieBox ? document.body : document.documentElement;
        return {x : cont.scrollLeft, y : cont.scrollTop};
    } else {
        return {x : window.pageXOffset, y : window.pageYOffset};
    }
}

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function initInfoCaptions() {
   	// create a dynamic layer and attach it to the HTML document.
	objBody = document.getElementsByTagName("body").item(0);
    objContainer = document.createElement('div');
    objContainer.setAttribute('id', 'infocaption');

    objBody.appendChild(objContainer);
}

window.onload = function() {
	initInfoCaptions();
}