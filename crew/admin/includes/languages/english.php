<?php

/*
  $Id: english.php,v 1.227 2016/04/25 03:34:50 jeeva.kasin<PERSON>an Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

//Admin begin
// header text in includes/header.php
define('HEADER_TITLE_ACCOUNT', 'My Account');
define('HEADER_TITLE_LOGOFF', 'Logoff');

define('BOX_CONFIGURATION_LINK', 'Configuration');
// Buyback
define('BOX_HEADING_BUYBACK', 'Buyback/Supplier');
define('BOX_BUYBACK_PRODUCTS', 'Buyback Products');
define('BOX_BUYBACK_REQUESTS', 'Buyback Lists');
define('BOX_SUPPLIERS_RESTOCK_CHARACTERS', 'Suppliers Rstk Character');
define('BOX_BUYBACK_GROUPS', 'Buyback Groups');
define('BOX_SUPPLIERS_GROUPS', 'Suppliers Grouping');
define('BOX_SUPPLIERS_LIST', 'Suppliers List');
define('BOX_SUPPLIERS_ORDERS_TRACKING', 'Suppliers Orders');
define('BOX_SUPPLIER_PAYMENT', 'Suppliers Payments');
define('BOX_SUPPLIER_PRICING', 'Suppliers Pricing');
define('BOX_SUPPLIER_REPORT', 'Suppliers Report');
define('BOX_SUPPLIERS_PURCHASE_LISTS', 'Suppliers Purchase Lists');
define('BOX_SUPPLIERS_AVERAGE_OFFER_PRICE', 'Average Offer Price');
define('BOX_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS', 'Avg Offer Price Brackets');
define('BOX_COMPETITORS', 'Competitors');
define('BOX_VIP_SUPPLIER_RANKING', 'VIP Suppliers Ranking');
define('BOX_VIP_RULES', 'VIP Rules');
define('BOX_VIP_BUYBACK_FOLLOW_UP', 'Buyback Report');
define('BOX_PRICE_AUTOMATION', 'Price Automation');
define('BOX_PRODUCTS_SUPPLIER', 'Products Supplier'); // HLA Suppliers Module
// Purchase Order
define('BOX_HEADING_PO', 'CDK PO/Supplier');
define('BOX_PO_SUPPLIERS_LIST', 'CDK Suppliers List');
define('BOX_PO_PAYMENT', 'CDK Suppliers Payment Withdrawal');
define('BOX_PO_COMPANY', 'CDK PO Company');

// Data Pool
define('BOX_HEADING_CUSTOM_PRODUCT', 'Custom Product');
define('BOX_BRACKET_GROUP', 'Bracket Group');
define('BOX_DATA_POOL_TEMPLATE', 'Template');
define('BOX_HLA_TEMPLATE', 'HLA Template');
define('BOX_CDKEY_TEMPLATE', 'CD Key');
define('BOX_PROGRESS_REPORT_TEMPLATE', 'Progress Report');
define('BOX_CUSTOM_PRODUCT_PAYMENT_TEMPLATE', 'Custom Product Payment');
define('BOX_CUSTOM_PRODUCT_LISTS_CRITERIA', 'Custom Product Lists Criteria');
define('BOX_TRADE_MAIL_LOGS_TEMPLATE', 'Trade & Mail Logs');
define('BOX_DEFINE_IP_ZONE', 'Define IP Zone');
define('BOX_TOOLS_CSV_MANAGER', 'CSV Manager');
define('BOX_PUBLISHERS_TEMPLATE', 'Publishers');
define('BOX_PUBLISHERS_GAMES_TEMPLATE', 'Publishers Games');
define('BOX_PUBLISHERS_REPLENISH_TEMPLATE', 'Publishers API Replenish');
define('BOX_DTU_PAYMENT_TEMPLATE', 'DTU Payment Request');
define('BOX_API_REPLENISH_PAYMENT_TEMPLATE', 'API Replenish Payment Request');
define('BOX_CDK_PAYMENT_TEMPLATE', 'PO Consignment CDK Payment Request');

//sales
define('BOX_HEADING_SALES', 'Orders');
define('BOX_SALES_ACTIVE_ORDERS', 'Order Lists Criteria');
define('BOX_SALES_PREDEFINED_COMMENTS', 'Order Comments');
define('BOX_SALES_ORDERS_TAGS', 'Order Tags');
define('BOX_SALES_CUSTOMERS_ORDER_ACTIVITIES', 'Customers Order Activities');
define('BOX_SALES_ORDERS_MATCHING', 'Orders Matching');
define('BOX_SALES_ORDERS_LISTING', 'Order Listing 2');

define('BOX_REPORTS_PRODUCTS_LOW_STOCK', 'Products Low Stock Warning');
define('BOX_EDIT_PURCHASE_ORDERS', 'CDK PO List');
define('BOX_REPORTS_STOCK_LEVEL', 'Low Stock Report');
define('BOX_REPORTS_PRODUCTS', 'Products Discovery');
define('BOX_REPORTS_API', 'API Report');

// Admin Account
define('BOX_HEADING_MY_ACCOUNT', 'My Account');

// configuration box text in includes/boxes/administrator.php
define('BOX_HEADING_ADMINISTRATOR', 'Administrator');
define('BOX_ADMINISTRATOR_MEMBERS', 'Member &amp; Groups');
define('BOX_ADMINISTRATOR_MEMBER', 'Members');
define('BOX_ADMINISTRATOR_BOXES', 'Boxes &amp; Files');
define('BOX_ADMINISTRATOR_LOG', 'Log Files');
define('BOX_ADMINISTRATOR_SYSTEM_LOG', 'System Log');
define('BOX_ADMINISTRATOR_PAYMENT_GATEWAY', 'Payment Gateway');
define('BOX_ADMINISTRATOR_CUSTOMERS_CONF', 'Customers Configuration');
define('BOX_ADMINISTRATOR_ORDERS_STATUS_CONF', 'Status Configuration');
define('BOX_ADMINISTRATOR_IMAGE_UPLOAD_CONF', 'Image Upload Config');
define('BOX_ADMINISTRATOR_EMAIL_DOMAIN_LIST', 'Email Domain List');

//CGDiscountSpecials start
define('BOX_CUSTOMERS_GROUPS', 'Customers Grouping');
//CGDiscountSpecials end
// theme layout
define('BOX_HEADING_THEME', 'Theme');
define('BOX_THEME_CUSTOMIZING', 'Theme Customizing');
define('BOX_THEME_ASSIGNATION', 'Theme Assignation');

// BEGIN latest news
define('BOX_EVENTS', 'Events');
define('BOX_HEADING_LATEST_NEWS', 'Latest News');
define('BOX_LATEST_NEWS_ALL', 'Latest News - All');
define('BOX_LATEST_NEWS_POLLING', 'Polling');
define('BOX_NEWS_GROUPS', 'News Groups');
define('ERROR_NEWS_GROUPS_NAME', 'Please fill in News Groups Name!');
define('ERROR_NEWS_HEADLINE', 'Please fill in News headline!');
define('ENTRY_GROUPS_NAME', 'News Groups Name:');
define('ENTRY_GROUPS_LEGEND_COLOR', 'Legend of display color:');
define('ENTRY_SORT_ORDER', 'Sort Order:');

// BEGIN Navigation
define('BOX_MENU_MANAGEMENT', 'Menu Management');
define('BOX_SEO_META_TAG', 'SEO Meta Tag');

// images
define('IMAGE_FILE_PERMISSION', 'File Permission');
define('IMAGE_GROUPS', 'Groups List');
define('IMAGE_INSERT_FILE', 'Insert File');
define('IMAGE_MEMBERS', 'Members List');
define('IMAGE_NEW_GROUP', 'New Group');
define('IMAGE_NEW_MEMBER', 'New Member');
define('IMAGE_NEXT', 'Next');

// constants for use in tep_prev_next_display function
define('TEXT_DISPLAY_NUMBER_OF_FILENAMES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> filenames)');
define('TEXT_DISPLAY_NUMBER_OF_MEMBERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> members)');
//Admin end
// look in your $PATH_LOCALE/locale directory for available locales..
// on RedHat6.0 I used 'en_US'
// on FreeBSD 4.0 I use 'en_US.ISO_8859-1'
// this may not work under win32 environments..
setlocale(LC_TIME, 'en_US.ISO_8859-1');
define('DATE_FORMAT_SHORT', '%m/%d/%Y');  // this is used for strftime()
define('DATE_FORMAT_LONG', '%A, %d %B, %Y'); // this is used for strftime()
define('DATE_FORMAT', 'm/d/Y'); // this is used for date()
define('PHP_DATE_TIME_FORMAT', 'm/d/Y H:i:s'); // this is used for date()
define('DATE_TIME_FORMAT', DATE_FORMAT_SHORT . ' %H:%M:%S');

define('PREFERRED_DATE_FORMAT', 'Y-m-d'); // this is used for date()
define('PREFERRED_DATE_FORMAT_SHORT', '%Y-%m-%d');  // this is used for strftime()
define('PREFERRED_DATE_TIME_FORMAT', PREFERRED_DATE_FORMAT_SHORT . ' %H:%M:%S');

//Separate Price per Customer Mod
define('ENTRY_CUSTOMERS_GROUP_NAME', 'Customer Group:');

// Return date in raw format
// $date should be in format mm/dd/yyyy
// raw date is in format YYYYMMDD, or DDMMYYYY
function tep_date_raw($date, $reverse = false) {
    if ($reverse) {
        return substr($date, 3, 2) . substr($date, 0, 2) . substr($date, 6, 4);
    } else {
        return substr($date, 6, 4) . substr($date, 0, 2) . substr($date, 3, 2);
    }
}

// Global entries for the <html> tag
define('HTML_PARAMS', 'dir="ltr" lang="en"');

// charset for web pages and emails
//define('CHARSET', 'iso-8859-1');
define('CHARSET', 'utf-8');
define('EMAIL_CHARSET_CHINESE', 'gb2312');
define('CHARSET_CHINESE', 'utf-8');

// page title
define('TITLE', STORE_NAME);

// e-mail greeting
define('EMAIL_GREET_MR', 'Dear Mr. %s,' . "\n\n");
define('EMAIL_GREET_MS', 'Dear Ms. %s,' . "\n\n");
define('EMAIL_GREET_NONE', 'Dear %s,' . "\n\n");

// header text in includes/header.php
define('HEADER_TITLE_TOP', 'Admin');
define('HEADER_TITLE_SUPPORT_SITE', 'osCommerce');
define('HEADER_TITLE_ONLINE_CATALOG', 'Catalog');
define('HEADER_TITLE_ADMINISTRATION', 'Admin');
define('HEADER_TITLE_OSCDOX', 'OSCdox.com');
define('HEADER_TITLE_AABOX', 'AABox Loaded MS2');

// MaxiDVD Added Line For WYSIWYG HTML Area: BOF
define('BOX_CATALOG_DEFINE_MAINPAGE', 'Define MainPage');
// MaxiDVD Added Line For WYSIWYG HTML Area: EOF
// text for account activation
define('NOT_ACTIVATED', 'Not Activated');
define('ACTIVATED', 'Activated');

// text for account status
define('NOT_ACTIVE', 'Not Active');
define('ACTIVE', 'Active');
define('PERMANENT_INACTIVE', 'Permanent Inactive');

// text for gender
define('MALE', 'Male');
define('FEMALE', 'Female');

// text for date of birth example
define('DOB_FORMAT_STRING', 'mm/dd/yyyy');

// configuration box text in includes/boxes/configuration.php
define('BOX_HEADING_CONFIGURATION', 'Configuration');
define('BOX_CONFIGURATION_MYSTORE', 'Store Information');
define('BOX_CONFIGURATION_LOGGING', 'Logging');
define('BOX_CONFIGURATION_CACHE', 'Cache');
define('BOX_CONFIGURATION_CUSTOMER', 'Customer Options');
define('BOX_CONFIGURATION_STOCK', 'Stock Options');
define('BOX_CONFIGURATION_EMAIL', 'E-mail Options');

// Promotions box
define('BOX_HEADING_PROMOTIONS', 'Promotions');
define('BOX_MODULES_PROMOTIONS', 'Store Wide Promotions');
define('BOX_MODULES_SPECIAL', 'Special Discounts');
define('BOX_MODULES_PRODUCT_PROMOTIONS', 'Product Promotions');

// stock
define('BOX_HEADING_STOCK', 'Stocks');

// infolinks
define('BOX_HEADING_INFOLINKS', 'InfoLinks');
define('BOX_INFOLINKS_INDEX', 'InfoLinks');
define('BOX_DEFINE_MAIN_PAGE', 'Define main page');
define('BOX_DEFINE_HELP_CENTER_PAGE', 'Define Help center page');
define('BOX_DEFINE_GAME_LAND_PAGE', 'Define Game Page');
define('BOX_GAME_DATABASE_CONFIG', 'Game Database Configuration');

// modules box text in includes/boxes/modules.php
define('BOX_HEADING_MODULES', 'Modules');
define('BOX_MODULES_PAYMENT', 'Payment Options');
define('BOX_MODULES_PAYMENT_METHODS', 'Payment Methods');
define('BOX_MODULES_SHIPPING', 'Shipping');
define('BOX_MODULES_ORDER_TOTAL', 'Cart Display');
define('BOX_MODULES_SHOPPING_CART_COMMENTS', 'Customer Comments');

// categories box text in includes/boxes/catalog.php
define('BOX_HEADING_CATALOG', 'Catalog');
define('BOX_CATALOG_CATEGORIES_PRODUCTS', 'Products List');
define('BOX_CATALOG_CATEGORIES_PRODUCTS_ATTRIBUTES', 'Products Attributes');
define('BOX_CATALOG_DEFINE_PRODUCT_TYPE_PAGE', 'Define Product Type Page');
define('BOX_CATALOG_MANUFACTURERS', 'Manufacturers');
define('BOX_CATALOG_REVIEWS', 'Product Reviews');
define('BOX_CATALOG_PAYMENT_RESTRICTIONS', 'Product Payment Restrictions List');
define('BOX_CATALOG_SPECIALS', 'Specials');
define('BOX_CATALOG_PRODUCTS_EXPECTED', 'Products Expected');
define('BOX_CATALOG_EASYPOPULATE', 'EasyPopulate');
define('BOX_CATALOG_ATTRIBUTE_MANAGER', 'Attribute Manager');
define('BOX_CATALOG_PRICE_TAGS', 'Batch Update');
define('BOX_CATALOG_BATCH_UPDATE_PRICES', 'Batch Update Prices');
define('BOX_CATALOG_BATCH_UPDATE_PRICES2', 'Batch Update Prices 2');
define('BOX_CATALOG_PRINTABLE_LIST', 'Printable List');
define('BOX_CATALOG_PRODUCTS_TYPE_TEMPLATE', 'Products Type Template');
define('BOX_CATALOG_CATEGORIES_TYPES', 'Categories Types');
define('BOX_CATALOG_CATEGORIES_STRUCTURES', 'Categories Structure');
define('BOX_CATALOG_DIRECT_TOPUP', 'Direct Topup');
define('BOX_CATALOG_OUT_OF_STOCK_RULE', 'Product Out Of Stock Rule');
define('BOX_CATALOG_DISCOUNT_RULE', 'Categories Discount Rule');

// customers box text in includes/boxes/customers.php
define('BOX_HEADING_CUSTOMERS', 'Customers');
define('BOX_CUSTOMERS_ACC_MERGING', 'Merge Account');
define('BOX_CUSTOMERS_CUSTOMERS', 'Customers List');
define('BOX_CUSTOMERS_DISCOUNT', 'Customers Discount List');
define('BOX_CUSTOMERS_ORDERS', 'Customers Order');
define('BOX_CUSTOMERS_EDIT_ORDERS', 'Edit Orders');
define('BOX_CUSTOMERS_PURCHASE_LIMIT', 'Purchase Limit');
define('BOX_CUSOMTERS_VERIFICATION_SETTING', 'Customers Verification Setting');

// OGC
define('BOX_HEADING_OGC', 'Gift Card');
define('BOX_OGC_REDEEM_STATUS', 'OGC Redeem Status');

//begin PayPal_Shopping_Cart_IPN
define('BOX_CUSTOMERS_PAYPAL', 'PayPal IPN');
//end PayPal_Shopping_Cart_IPN
// taxes box text in includes/boxes/taxes.php
define('BOX_HEADING_LOCATION_AND_TAXES', 'Taxes');
define('BOX_TAXES_COUNTRIES', 'Countries');
define('BOX_TAXES_ZONES', 'Zones');
define('BOX_TAXES_GEO_ZONES', 'Defined Zones');
define('BOX_TAXES_ZONES_CONFIG', 'Zones Configuration');
define('BOX_ZONES_INFO', 'Zones Info');
define('BOX_TAXES_TAX_CLASSES', 'Tax Classes');
define('BOX_TAXES_TAX_RATES', 'Tax Rates');

// taxes box text in includes/boxes/payments.php
define('BOX_HEADING_PAYMENTS', 'Payments');
define('BOX_PAYMENTS_ACCOUNT_STATEMENT', 'Account Statement');
define('BOX_PAYMENTS_PAYMENT', 'Payment');
define('BOX_PAYMENTS_REFUND', 'Refund');
define('BOX_POINTS_REDEEM', 'Redemption');
define('BOX_PAYMENTS_STORE_CREDIT', 'Store Credit');
define('BOX_PAYMENTS_STORE_POINT', 'OP');
define('BOX_PAYMENTS_CREDIT_NOTES_STATEMENT', 'Credit Notes');
define('BOX_PO_PROVISION_PAYMENTS', 'Provision for PO Payments');

// reports box text in includes/boxes/reports.php
define('BOX_HEADING_REPORTS', 'Reports');
define('BOX_REPORTS_PRODUCTS_VIEWED', 'Products - Best Viewed');
define('BOX_REPORTS_PRODUCTS_PURCHASED', 'Product Sales Report');
define('BOX_REPORTS_STOCK', 'Stock Report');
define('BOX_REPORTS_SALES', 'Sales Report');
define('BOX_REPORTS_SALES_NEW', 'Sales Report 3');
define('BOX_REPORTS_MGC_SALES', 'MGC Sales Report');
define('BOX_REPORTS_ORDERS_TOTAL', 'Total Purchased');

// tools text in includes/boxes/tools.php
define('BOX_HEADING_TOOLS', 'Tools');
define('BOX_TOOLS_AFT_AUTOMATION', 'AFT Automation');
define('BOX_TOOLS_AFT_RULE', 'AFT Rule');
define('BOX_TOOLS_AFT_ACCOUNT_LIMIT', 'AFT Account Limit');
define('BOX_TOOLS_UPGRADE', 'Version Upgrade');  // for version upgrading
define('BOX_TOOLS_CSS_GENERATOR', 'CSS Generator'); // for css generator
define('BOX_TOOLS_BACKUP', 'Database Backup');
define('BOX_TOOLS_BANNER_MANAGER', 'Banner Manager');
define('BOX_TOOLS_CACHE', 'Cache Control');
define('BOX_TOOLS_DB_ARCHIVE', 'Database Archive');
define('BOX_TOOLS_DEFINE_LANGUAGE', 'Define Languages');
define('BOX_TOOLS_DOWNLOAD_CENTER', 'Download Center');
define('BOX_TOOLS_IMAGE_UPLOAD', 'Image Upload');
define('BOX_TOOLS_FILE_MANAGER', 'File Manager');
define('BOX_TOOLS_MAIL', 'E-mail Customer');
define('BOX_TOOLS_NEWSLETTER_MANAGER', 'Newsletter Manager');
define('BOX_TOOLS_SERVER_INFO', 'Server Info');
define('BOX_TOOLS_WHOS_ONLINE', 'Who\'s Online');
define('BOX_TOOLS_PAP_AFFILIATE_REPORT', 'PAP4 Affiliate Report');
define('BOX_PAGE_VIEW_MODULE', 'Page View Module');

// localizaion box text in includes/boxes/localization.php
define('BOX_HEADING_LOCALIZATION', 'Localization');
define('BOX_LOCALIZATION_CURRENCIES', 'Currencies');
define('BOX_LOCALIZATION_LANGUAGES', 'Languages');
define('BOX_LOCALIZATION_ORDERS_STATUS', 'Orders Status');
define('BOX_GST_CONFIGURATION', 'GST Configuration');

// C2C in includes/boxes/c2c.php
define('BOX_HEADING_C2C', 'C2C');
define('BOX_C2C_SELLER_GROUP', 'Seller Group');
define('BOX_C2C_SELLER_PRODUCT_LISTING', 'Seller Product Listing');
define('BOX_C2C_SELLER_VERIFICATION_SETTING', 'Seller Verification Setting');
define('BOX_C2C_BUYBACK_ORDER', 'C2C Buyback Order');
define('BOX_C2C_DELIVERY_SPEED', 'Delivery Speed');
define('BOX_C2C_PRODUCT_CONFIGURATION', 'Product Configuration');
define('BOX_C2C_CONFIGURATION', 'Configuration');
define('BOX_C2C_RATING_TYPE', 'Rating Type');
define('BOX_C2C_SELLER_LEVEL_CONFIGURATION', 'Seller Level Configuration');

// javascript messages
define('JS_ERROR', 'Errors have occured during the process of your form!\nPlease make the following corrections:\n\n');

define('JS_OPTIONS_VALUE_PRICE', '* The new product atribute needs a price value\n');
define('JS_OPTIONS_VALUE_PRICE_PREFIX', '* The new product atribute needs a price prefix\n');

define('JS_PRODUCTS_NAME', '* The new product needs a name\n');
define('JS_PRODUCTS_DESCRIPTION', '* The new product needs a description\n');
define('JS_PRODUCTS_PRICE', '* The new product needs a price value\n');
define('JS_PRODUCTS_WEIGHT', '* The new product needs a weight value\n');
define('JS_PRODUCTS_QUANTITY', '* The new product needs a quantity value\n');
define('JS_PRODUCTS_MODEL', '* The new product needs a model value\n');
define('JS_PRODUCTS_IMAGE', '* The new product needs an image value\n');

define('JS_SPECIALS_PRODUCTS_PRICE', '* A new price for this product needs to be set\n');

define('JS_GENDER', '* The \'Gender\' value must be chosen.\n');
define('JS_FIRST_NAME', '* The \'First Name\' entry must have at least ' . ENTRY_FIRST_NAME_MIN_LENGTH . ' characters.\n');
define('JS_LAST_NAME', '* The \'Last Name\' entry must have at least ' . ENTRY_LAST_NAME_MIN_LENGTH . ' characters.\n');
define('JS_DOB', '* The \'Date of Birth\' entry must be in the format: xx/xx/xxxx (month/date/year).\n');
define('JS_EMAIL_ADDRESS', '* The \'E-Mail Address\' entry must have at least ' . ENTRY_EMAIL_ADDRESS_MIN_LENGTH . ' characters.\n');
define('JS_ADDRESS', '* The \'Street Address\' entry must have at least ' . ENTRY_STREET_ADDRESS_MIN_LENGTH . ' characters.\n');
define('JS_POST_CODE', '* The \'Post Code\' entry must have at least ' . ENTRY_POSTCODE_MIN_LENGTH . ' characters.\n');
define('JS_CITY', '* The \'City\' entry must have at least ' . ENTRY_CITY_MIN_LENGTH . ' characters.\n');
define('JS_STATE', '* The \'State\' entry is must be selected/entered.\n');
define('JS_STATE_SELECT', '-- Select Above --');
define('JS_ZONE', '* The \'State\' entry must be selected from the list for this country.');
define('JS_COUNTRY', '* The \'Country\' must be chosen.\n');
define('JS_LOCATION', '* \'Location\' must be chonsen.\n');
define('JS_TELEPHONE', '* The \'Telephone Number\' entry must have at least ' . ENTRY_TELEPHONE_MIN_LENGTH . ' characters.\n');
define('JS_SUPPLIER_CODE', '* The \'Supplier Code\' entry must be entered.\n');
define('JS_PO_COMPANY_CODE', '* The \'PO Company Code\' entry must be entered.\n');
define('JS_CUSTOMER_ONLINE', '* The \'Customer\' is currently Online.\n');

define('JS_PASSWORD', '* The \'Password\' amd \'Confirmation\' entries must match amd have at least ' . ENTRY_PASSWORD_MIN_LENGTH . ' characters.\n');

define('JS_ORDER_DOES_NOT_EXIST', 'Order Number %s does not exist!');

define('CATEGORY_ACCOUNT', 'Account');
define('CATEGORY_PERSONAL', 'Personal');
define('CATEGORY_ADDRESS', 'Address');
define('CATEGORY_DELIVERY_ADDRESS', 'Delivery Address');
define('CATEGORY_CONTACT', 'Contact');
define('CATEGORY_COMPANY', 'Company');
define('CATEGORY_OPTIONS', 'Options');
define('CATEGORY_GROUP', 'Group');
define('CATEGORY_PREFERENCES', 'Preferences');
define('CATEGORY_VOUCHER', 'Balance');
define('CATEGORY_RESERVE_AMOUNT', 'Reserve Amount');
define('CATEGORY_WITHDRAW_ACCOUNT', 'Withdraw Account');
define('CATEGORY_CUSTOMERS_INFORMATION_FLAG', 'Customer\'s Information Flag');
define('CATEGORY_FLAGS', 'Flags');
define('CATEGORY_REMARKS', 'Remarks');
define('CATEGORY_VERIFICATION_SUBMISSION_FORM', 'Verification submission form');
define('CATEGORY_PAYMENT_INFO', 'Payment Info');
define('CATEGORY_DISCOUNT_AGREEMENT', 'Discount Agreement');

define('ENTRY_CUSTOMER_ID', 'Customer ID:');
define('ENTRY_ACCOUNT_ACTIVATION', 'First Time E-Mail Activation:');
define('ENTRY_ACCOUNT_STATUS', 'Account Status:');
define('ENTRY_GENDER', 'Gender:');
define('ENTRY_GENDER_ERROR', '&nbsp;<span class="errorText">required</span>');
define('ENTRY_TASK_ERROR', '&nbsp;<span class="errorText">required</span>');
define('ENTRY_FIRST_NAME', 'First Name:');
define('ENTRY_FIRST_NAME_ERROR', '&nbsp;<span class="errorText">min ' . ENTRY_FIRST_NAME_MIN_LENGTH . ' chars</span>');
define('ENTRY_LAST_NAME', 'Last Name:');
define('ENTRY_LAST_NAME_ERROR', '&nbsp;<span class="errorText">min ' . ENTRY_LAST_NAME_MIN_LENGTH . ' chars</span>');
define('ENTRY_DATE_OF_BIRTH', 'Date of Birth:');
define('ENTRY_DATE_OF_BIRTH_ERROR', 'Your Date of Birth must contain a valid date.');
define('ENTRY_DATE_OF_BIRTH_ERROR_1', 'You must select a');
define('ENTRY_DATE_OF_BIRTH_ERROR_2', ' from the');
define('ENTRY_DATE_OF_BIRTH_ERROR_3', ' pull down menu. ');
define('ENTRY_DATE_OF_BIRTH_ERROR_4', 'Your year must contain 4 digits.');
define('ENTRY_EMAIL_ADDRESS', 'E-Mail Address:');
define('ENTRY_EMAIL_ADDRESS_ERROR', '&nbsp;<span class="errorText">min ' . ENTRY_EMAIL_ADDRESS_MIN_LENGTH . ' chars</span>');
define('ENTRY_EMAIL_ADDRESS_CHECK_ERROR', '&nbsp;<span class="errorText">The email address doesn\'t appear to be valid!</span>');
define('ENTRY_EMAIL_ADDRESS_ERROR_EXISTS', '&nbsp;<span class="errorText">This email address already exists!</span>');
define('ENTRY_COMPANY', 'Company name:');
define('ENTRY_COMPANY_ERROR', '');
define('ENTRY_STREET_ADDRESS', 'Street Address:');
define('ENTRY_STREET_ADDRESS_ERROR', '&nbsp;<span class="errorText">min ' . ENTRY_STREET_ADDRESS_MIN_LENGTH . ' chars</span>');
define('ENTRY_SUBURB', 'Suburb:');
define('ENTRY_SUBURB_ERROR', '');
define('ENTRY_POST_CODE', 'Post Code:');
define('ENTRY_POST_CODE_ERROR', '&nbsp;<span class="errorText">min ' . ENTRY_POSTCODE_MIN_LENGTH . ' chars</span>');
define('ENTRY_CITY', 'City:');
define('ENTRY_CITY_ERROR', '&nbsp;<span class="errorText">min ' . ENTRY_CITY_MIN_LENGTH . ' chars</span>');
define('ENTRY_STATE', 'State:');
define('ENTRY_STATE_ERROR', '&nbsp;<span class="errorText">required</span>');
define('ENTRY_COUNTRY', 'Country:');
define('ENTRY_COUNTRY_ERROR', '&nbsp;<span class="errorText">required</span>');
define('ENTRY_LOCATION', 'Location:');
define('ENTRY_LOCATION_ERROR', '&nbsp;<span class="errorText">required</span>');
define('ENTRY_DELIVERY_ADDRESS', 'Company Name');
define('ENTRY_DELIVERY_ADDRESS_ERROR', '&nbsp;<span class="errorText">required</span>');
define('ENTRY_TELEPHONE_NUMBER', 'Mobile Number:');
define('ENTRY_FIXLINE_NUMBER', 'Office Phone Number:');
define('ENTRY_TELEPHONE_NUMBER_ERROR', '&nbsp;<span class="errorText">min ' . ENTRY_TELEPHONE_MIN_LENGTH . ' chars</span>');
define('ENTRY_FAX_NUMBER', 'Fax Number:');
define('ENTRY_FAX_NUMBER_ERROR', '');
define('ENTRY_MOBILE_NUMBER', 'Mobile Number:');
define('ENTRY_QQ_NUMBER', 'QQ Number:');
define('ENTRY_QQ_NUMBER_ERROR', '&nbsp;<span class="errorText">Accept number only</span>');
define('ENTRY_MSN_ADDRESS', 'MSN Address:');
define('ENTRY_MSN_ADDRESS_ERROR', '&nbsp;<span class="errorText">The MSN doesn\'t appear to be valid</span>');
define('ENTRY_YAHOO_ADDRESS', 'Yahoo Address:');
define('ENTRY_YAHOO_ADDRESS_ERROR', '&nbsp;<span class="errorText">The Yahoo doesn\'t appear to be valid</span>');
define('ENTRY_ICQ_NUMBER', 'QQ Number:');
define('ENTRY_ICQ_NUMBER_ERROR', '&nbsp;<span class="errorText">Accept number only</span>');
define('ENTRY_NEWSLETTER', 'Newsletter:');
define('ENTRY_NEWSLETTER_YES', 'Subscribed');
define('ENTRY_NEWSLETTER_NO', 'Unsubscribed');
define('ENTRY_NEWSLETTER_ERROR', '');
define('ENTRY_SUPPLIER_CODE_ERROR', '&nbsp;<span class="errorText">required</span>');
define('ENTRY_SUPPLIER_CODE_ERROR_EXISTS', '&nbsp;<span class="errorText">This supplier code already exists!</span>');
define('ENTRY_PREF_LANGUAGE', 'Language:');
define('ENTRY_PREF_TIME_ZONE', 'Time Zone:');
define('ENTRY_LATITUDE', 'latitude:');
define('ENTRY_LONGITUDE', 'longitude:');
define('ENTRY_DISABLE_WITHDRAWAL', 'Disable Withdrawal:');

// buttons
define('BUTTON_ADD_BALANCE', 'Add');
define('BUTTON_ADD_COMMENT', 'Add Comment');
define('BUTTON_ADD_ITEM', 'Add Item');
define('BUTTON_APPLY_ALL', 'Apply All');
define('BUTTON_MANUALLY_NOTIFY_SUPPLIER', 'Manually Notify Supplier');
define('BUTTON_BACK', 'Back');
define('BUTTON_OK', 'OK');
define('BUTTON_CANCEL', 'Cancel');
define('BUTTON_CONFIRM', 'Confirm');
define('BUTTON_DEDUCT_BALANCE', 'Deduct');
define('BUTTON_DELIVERY_MODE', 'Delivery Mode');
define('BUTTON_DEFINE_CURRENCIES_PRICE', 'Other Currencies Price');
define('BUTTON_EXECUTE_AFT_DEMO', 'RERUN (DEMO)');
define('BUTTON_EXECUTE_AFT_LIVE', 'RERUN (LIVE)');
define('BUTTON_GO', 'Go');
define('BUTTON_GO_LIVE', 'GO LIVE');
define('BUTTON_INSERT', 'Insert');
define('BUTTON_ISSUE_STORE_CREDIT', 'Issue SC');
define('BUTTON_NEXT', 'Next');
define('BUTTON_OFF', 'OFF');
define('BUTTON_PREVIEW', 'Preview');
define('BUTTON_PREVIEW_CURRENCY', 'Preview Exchange Rate');
define('BUTTON_PRINT_VERSION', 'Printable Version');
define('BUTTON_PUBLISH_TO_DEMO', 'PUBLISH TO DEMO');
define('BUTTON_REFUND', 'Refund');
define('BUTTON_RELEASE_KEY', 'Release');
define('BUTTON_REMOVE', 'Remove');
define('BUTTON_REPORT', 'Report');
define('BUTTON_RESET', 'Reset');
define('BUTTON_SAVE_DRAFT', 'Save Draft');
define('BUTTON_SEARCH', 'Search');
define('BUTTON_UPDATE', 'Update');
define('BUTTON_KICK_CUSTOMER', 'Kick Customer');
define('BUTTON_ISSUE', 'Issue');

define('ALT_BUTTON_ADD_BALANCE', 'Add');
define('ALT_BUTTON_ADD_COMMENT', 'Add Comment');
define('ALT_BUTTON_ADD_ITEM', 'Add Item');
define('ALT_BUTTON_APPLY_ALL', 'Apply these settings to all the files');
define('ALT_BUTTON_MANUALLY_NOTIFY_SUPPLIER', 'Manually Notify Supplier');
define('ALT_BUTTON_BACK', 'Back to previous page');
define('ALT_BUTTON_OK', 'OK');
define('ALT_BUTTON_CANCEL', 'Cancel');
define('ALT_BUTTON_CONFIRM', 'Confirm');
define('ALT_BUTTON_DEDUCT_BALANCE', 'Deduct');
define('ALT_BUTTON_DEFINE_CURRENCIES_PRICE', 'Manage Other Currencies Price');
define('ALT_BUTTON_DELIVERY_MODE', 'Delivery Mode');
define('ALT_BUTTON_EXECUTE_AFT_DEMO', 'Execute AFT DEMO');
define('ALT_BUTTON_EXECUTE_AFT_LIVE', 'Execute AFT LIVE');
define('ALT_BUTTON_GO', 'Go');
define('ALT_BUTTON_GO_LIVE', 'GO LIVE');
define('ALT_BUTTON_INSERT', 'Insert');
define('ALT_BUTTON_ISSUE_STORE_CREDIT', 'Issue Store Credit');
define('ALT_BUTTON_NEXT', 'Proceed to next page');
define('ALT_BUTTON_OFF', 'OFF');
define('ALT_BUTTON_PREVIEW', 'Click to Preview');
define('ALT_BUTTON_PREVIEW_CURRENCY', 'Preview Latest Exchange Rate');
define('ALT_BUTTON_PRINT_VERSION', 'Printer-Friendly Page');
define('ALT_BUTTON_PUBLISH_TO_DEMO', 'PUBLISH TO DEMO');
define('ALT_BUTTON_REFUND', 'Refund');
define('ALT_BUTTON_RELEASE_KEY', 'Release');
define('ALT_BUTTON_REMOVE', 'Remove this entry');
define('ALT_BUTTON_REPORT', 'Report');
define('ALT_BUTTON_RESET', 'Reset');
define('ALT_BUTTON_SAVE_DRAFT', 'Save Draft');
define('ALT_BUTTON_SEARCH', 'Search');
define('ALT_BUTTON_UPDATE', 'Update');
define('ALT_BUTTON_ISSUE', 'Issue');

// images
define('IMAGE_ANI_SEND_EMAIL', 'Sending E-Mail');
define('IMAGE_BACK', 'Back');
define('IMAGE_BACKUP', 'Backup');
define('IMAGE_CANCEL', 'Cancel');
define('IMAGE_CONFIRM', 'Confirm');
define('IMAGE_COPY', 'Copy');
define('IMAGE_COPY_TO', 'Copy To');
define('IMAGE_DETAILS', 'Details');
define('IMAGE_DELETE', 'Delete');
define('IMAGE_EDIT', 'Edit');
define('IMAGE_EMAIL', 'Email');
define('IMAGE_FILE_MANAGER', 'File Manager');
define('IMAGE_ICON_FACE_TO_FACE', 'Face to face');
define('IMAGE_ICON_MAIL', 'Mail');
define('IMAGE_ICON_PIMA', 'Pima');
define('IMAGE_ICON_CONTRACT', 'Contract');
define('IMAGE_ICON_OPEN_STORE', 'Open Store');
define('IMAGE_ICON_HIDE_CONTENT', 'Hide Content');
define('IMAGE_ICON_SHOW_CONTENT', 'Show Content');
define('IMAGE_ICON_STATUS_GREEN', 'Active');
define('IMAGE_ICON_STATUS_GREEN_LIGHT', 'Set Active');
define('IMAGE_ICON_STATUS_RED', 'Inactive');
define('IMAGE_ICON_STATUS_RED_LIGHT', 'Set Inactive');
define('IMAGE_ICON_STATUS_YELLOW', 'Pending');
define('IMAGE_ICON_STATUS_YELLOW_LIGHT', 'Set Pending');
define('IMAGE_ICON_STATUS_LOCKED', 'Permanent Inactive');
//Added for product hidden
define('IMAGE_ICON_HIDDEN_GREEN', 'Show');
define('IMAGE_ICON_HIDDEN_GREEN_LIGHT', 'Show It');
define('IMAGE_ICON_HIDDEN_RED', 'Hidden');
define('IMAGE_ICON_HIDDEN_RED_LIGHT', 'Hide It');

define('IMAGE_ICON_CHAR_ONLINE', 'Character Online');
define('IMAGE_ICON_CHAR_OFFLINE', 'Character Offline');
define('IMAGE_ICON_CHAR_UNKNOWN', 'Character Unknown Status');
//End of added for product hidden
define('IMAGE_ICON_INFO', 'Info');
define('IMAGE_ICON_SEARCH', 'Search');
define('IMAGE_INSERT', 'Insert');
define('IMAGE_LOCK', 'Lock');
define('IMAGE_MODULE_INSTALL', 'Install Module');
define('IMAGE_MODULE_REMOVE', 'Remove Module');
define('IMAGE_MOVE', 'Move');
define('IMAGE_NEW_BANNER', 'New Banner');
define('IMAGE_NEW_CATEGORY', 'New Category');
define('IMAGE_NEW_COUNTRY', 'New Country');
define('IMAGE_NEW_CURRENCY', 'New Currency');
define('IMAGE_NEW_FILE', 'New File');
define('IMAGE_NEW_FOLDER', 'New Folder');
define('IMAGE_NEW_LANGUAGE', 'New Language');
define('IMAGE_NEW_NEWSLETTER', 'New Newsletter');
define('IMAGE_NEW_PRODUCT', 'New Product');
define('IMAGE_NEW_TAX_CLASS', 'New Tax Class');
define('IMAGE_NEW_TAX_RATE', 'New Tax Rate');
define('IMAGE_NEW_TAX_ZONE', 'New Tax Zone');
define('IMAGE_NEW_ZONE', 'New Zone');
define('IMAGE_ORDERS', 'Orders');
define('IMAGE_ORDERS_INVOICE', 'Invoice');
define('IMAGE_ORDERS_PACKINGSLIP', 'Packing Slip');
define('IMAGE_PREVIEW', 'Preview');
define('IMAGE_REPORT', 'View Report');
define('IMAGE_RESTORE', 'Restore');
define('IMAGE_RESET', 'Reset');
define('IMAGE_SAVE', 'Save');
define('IMAGE_SEARCH', 'Search');
define('IMAGE_SELECT', 'Select');
define('IMAGE_SEND', 'Send');
define('IMAGE_SEND_EMAIL', 'Send Email');
define('IMAGE_UNLOCK', 'Unlock');
define('IMAGE_UPDATE', 'Update');
define('IMAGE_UPDATE_CURRENCIES', 'Update Exchange Rate');
define('IMAGE_UPLOAD', 'Upload');

define('IMAGE_BUTTON_BACK', 'Back');
define('IMAGE_BUTTON_UPDATE', 'Update');
define('IMAGE_BUTTON_BATCH_APPLY', 'Batch Apply');

define('ICON_CROSS', 'False');
define('ICON_CURRENT_FOLDER', 'Current Folder');
define('ICON_DELETE', 'Delete');
define('ICON_ERROR', 'Error');
define('ICON_FILE', 'File');
define('ICON_FILE_DOWNLOAD', 'Download');
define('ICON_FOLDER', 'Folder');
define('ICON_LOCKED', 'Locked');
define('ICON_PREVIOUS_LEVEL', 'Previous Level');
define('ICON_PREVIEW', 'Preview');
define('ICON_STATISTICS', 'Statistics');
define('ICON_SUCCESS', 'Success');
define('ICON_TICK', 'True');
define('ICON_UNLOCKED', 'Unlocked');
define('ICON_WARNING', 'Warning');

// constants for use in tep_prev_next_display function
define('TEXT_RESULT_PAGE', 'Page %s of %s');
define('TEXT_DISPLAY_NUMBER_OF_BANNERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> banners)');
define('TEXT_DISPLAY_NUMBER_OF_CDKEY', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> cd keys)');
define('TEXT_DISPLAY_NUMBER_OF_COMMENTS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> comments)');
define('TEXT_DISPLAY_NUMBER_OF_COMMISSIONS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> commissions)');
define('TEXT_DISPLAY_NUMBER_OF_COUNTRIES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> countries)');
define('TEXT_DISPLAY_NUMBER_OF_CUSTOMERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> customers)');
define('TEXT_DISPLAY_NUMBER_OF_CURRENCIES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> currencies)');
define('TEXT_DISPLAY_NUMBER_OF_EVENTS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> events)');
define('TEXT_DISPLAY_NUMBER_OF_GROUPS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> groups)');
define('TEXT_DISPLAY_NUMBER_OF_LOGS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> logs)');
define('TEXT_DISPLAY_NUMBER_OF_LANGUAGES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> languages)');
define('TEXT_DISPLAY_NUMBER_OF_MANUFACTURERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> manufacturers)');
define('TEXT_DISPLAY_NUMBER_OF_NEWSLETTERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> newsletters)');
define('TEXT_DISPLAY_NUMBER_OF_ORDERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> orders)');
define('TEXT_DISPLAY_NUMBER_OF_ORDERS_STATUS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> orders status)');
define('TEXT_DISPLAY_NUMBER_OF_ORDERS_TAGS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> order tags)');
define('TEXT_DISPLAY_NUMBER_OF_PAYMENTS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> payments)');
define('TEXT_DISPLAY_NUMBER_OF_TRANSACTIONS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> transactions)');
define('TEXT_DISPLAY_NUMBER_OF_PUBLISHERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> publishers)');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> products)');
define('TEXT_DISPLAY_NUMBER_OF_PRODUCTS_EXPECTED', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> products expected)');
define('TEXT_DISPLAY_NUMBER_OF_RECORDS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> records)');
define('TEXT_DISPLAY_NUMBER_OF_REDEEMS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> redeems)');
define('TEXT_DISPLAY_NUMBER_OF_REVIEWS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> product reviews)');
define('TEXT_DISPLAY_NUMBER_OF_SPECIALS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> products on special)');
define('TEXT_DISPLAY_NUMBER_OF_SUPPLIERS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> suppliers)');
define('TEXT_DISPLAY_NUMBER_OF_SUPPLIER_GROUPS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> supplier groups)');
define('TEXT_DISPLAY_NUMBER_OF_TAX_CLASSES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> tax classes)');
define('TEXT_DISPLAY_NUMBER_OF_TAX_ZONES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> tax zones)');
define('TEXT_DISPLAY_NUMBER_OF_TAX_RATES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> tax rates)');
define('TEXT_DISPLAY_NUMBER_OF_THEMES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> themes)');
define('TEXT_DISPLAY_NUMBER_OF_TEMPLATES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> templates)');
define('TEXT_DISPLAY_NUMBER_OF_ZONES', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> zones)');
define('TEXT_DISPLAY_NUMBER_OF_LOW_STOCK_WARNINGS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> warnings)');
define('TEXT_DISPLAY_NUMBER_OF_PO_COMPANY', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> companies)');

define('TEXT_EDIT_CATEGORIES_HEADING_TITLE', 'Category Heading Title:');
define('TEXT_EDIT_CATEGORIES_DESCRIPTION', 'Category Description:');

define('PREVNEXT_BUTTON_PREV', '&lt;&lt;');
define('PREVNEXT_BUTTON_NEXT', '&gt;&gt;');

define('TEXT_EMAIL_BUTTON_TEXT', '<p><HR><b><font color="red">The Back Button has been DISABLE while HTML WYSIWG Editor is turned ON,</b></font> WHY? - Because if you click the back button to edit your HTML email, The PHP (php.ini - "Magic Quotes = On") will automatically add "\\\\\\\" backslashes everywhere Double Quotes " appear (HTML uses them in Links, Images and More) and this destorts the HTML and the pictures will dissapear once you submit the email again, If you turn OFF WYSIWYG Editor in Admin the HTML Ability of osCommerce is also turned OFF and the back button will re-appear. A fix for this HTML and PHP issue would be nice if someone knows a solution Iv\'e tried.<br><br><b>If you really need to Preview your emails before sending them, use the Preview Button located on the WYSIWYG Editor.<br><HR>');
define('TEXT_EMAIL_BUTTON_HTML', '<p><HR><b><font color="red">HTML is currently Disabled!</b></font><br><br>If you want to send HTML email, Enable WYSIWYG Editor for Email in: Admin-->Configuration-->WYSIWYG Editor-->Options<br>');

define('TEXT_ALL_PAGES', 'All');
define('PULL_DOWN_DEFAULT', 'Please Select');
define('PULL_DOWN_ALL', 'Select All');
define('TEXT_DEFAULT', 'default');
define('TEXT_SET_DEFAULT', 'Set as default');
define('TEXT_FIELD_REQUIRED', '&nbsp;<span class="fieldRequired">* Required</span>');
define('TEXT_SELECT_PREDEFINED_COMMENT', 'Select a Comment');
define('TEXT_SEARCH_PREDEFINED_COMMENT', 'Search a Comment');

define('ERROR_NO_DEFAULT_CURRENCY_DEFINED', 'Error: There is currently no default currency set. Please set one at: Administration Tool->Localization->Currencies');
define('ERROR_REACHED_DAILY_CREDIT_LIMIT', 'Error: You had reached the allowed daily send money limit');

define('TEXT_CACHE_CATEGORIES', 'Categories Box');
define('TEXT_CACHE_MANUFACTURERS', 'Manufacturers Box');
define('TEXT_CACHE_ALSO_PURCHASED', 'Also Purchased Module');

define('TEXT_NONE', '--none--');
define('TEXT_NONE2', 'None');
define('TEXT_HIDDEN', '-Hidden-');
define('TEXT_NO_ACCESS', '-No Access-');
define('TEXT_TOP', 'Top');
define('TEXT_REFUND_TITLE', 'Refund:');
define('TEXT_NOT_AVAILABLE', 'n/a');
define('TEXT_TRANS_BILLED', 'Billed');
define('TEXT_TRANS_NOT_BILLED', 'Not Billed');
define('TEXT_ONLINE', 'Online');
define('TEXT_OFFLINE', 'Offline');
define('TEXT_SELECT_NO_STATE', 'No State Data');
define('TEXT_HIDE_DETAILS', 'Hide Details');
define('TEXT_SHOW_DETAILS', 'Show Details');
define('TEXT_COMMENT', 'Comment');
define('TEXT_MEMORY_USAGE', 'Memory Usage');
define('TEXT_SHOW', 'Show');
define('TEXT_HIDE', 'Hide');

//Manual Phone Verification
define('TEXT_IMAGE_MOUSEOVER_MAXMIND_VERIFIED', 'Phone auto verified.');
define('TEXT_IMAGE_MOUSEOVER_MAXMIND_NOT_VERIFY', 'Information has not verify by MaxMind.');
define('TEXT_IMAGE_MOUSEOVER_MANUAL_VERIFIED', 'Manual verified.');
define('TEXT_IMAGE_MOUSEOVER_MANUAL_NOT_VERIFY', 'Information has not manually verify.');
define('TEXT_IMAGE_MOUSEOVER_PHONE_CALLED', '%s phone verification is triggered but not completed.');
define('TEXT_IMAGE_MOUSEOVER_PHONE_NOT_CALL', '%s phone verification is not triggered.');
define('TEXT_IMAGE_MOUSEOVER_VERIFY_GREY', "Change to \'Verified\'");
define('TEXT_IMAGE_MOUSEOVER_NOT_VERIFY_GREY', "Change to \'Not Verified\'");

define('EMAIL_SUBJECT', 'E-mail Address Verification');
define('SUCCESS_SEND_VERIFICATION_EMAIL', "The e-mail address verification's e-mail has been sent to the customer.");
define('ERROR_SEND_EMAIL_VERIFICATION', 'The e-mail address has been verified. Therefore no e-mail address verification e-mail is sent.');
define('TEXT_EMAIL_VERIFY_CONTENT', 'Recently, you have registered %s using this email address. To complete your registration, follow the link below:' . "\n");
define('TEXT_EMAIL_VERIFY_CONTENT_ADDRESS_INFO', '(If clicking on the link doesn\'t work, try copying and pasting it into your browser.)' . "\n\n");
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
define('EMAIL_CONTACT', 'For any enquiries or assistance, you may use our Online Live Support service or feel free to leave us a message. Please remember to include your Order Number when contacting us to expedite the process. Thanks again for shopping with us!' . "\n\n\n");

define('TEXT_SEND_PAYPAL_EMAIL_VERIFICATION', "An paypal e-mail verification's e-mail have been sent to the customer.");
define('TEXT_SEND_PAYPAL_EMAIL_VERIFICATION_ERROR', 'Send paypal e-mail address verification e-mail fail.');

define('TEXT_SEND_MB_EMAIL_VERIFICATION', "An moneybookers e-mail verification's e-mail have been sent to the customer.");
define('TEXT_SEND_MB_EMAIL_VERIFICATION_ERROR', 'Send moneybookers e-mail address verification e-mail fail.');

// Moneybookers Account Verification E-mail
define('TEXT_MB_EMAIL_VERIFY_INSTRUCTION_CONTENT', "\n\n\n" . 'Please kindly verify this e-mail account by clicking on the following link or copy and paste the link to your browser.  This link will confirm that you are the owner of this e-mail address and we will not ask for any Moneybookers information in the page.  Please understand that this step is taken to protect Moneybookers account owners from unauthorized charges and this is a one-time process unless you change your Moneybookers e-mail.' . "\n\n");
define('TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT1', 'You have made a Moneybookers payment of ');
define('TEXT_MB_PAYMENT_MAKE_EMAIL_CONTENT2', ' to %s using the following account:' . "\n");
define('TEXT_MB_EMAIL_VERIFY_ENDING_CONTENT', "\n\n\n" . 'If you have not made any purchase at %s, please report this fraudulent use to %s immediately and we also advise you to report the case to Moneybookers at their website and change your Moneybookers password immediately.  Thank you for shopping at %s.');
define('TEXT_MB_VERIFICATION_TITLE', 'Moneybookers Account Verification');
define('TEXT_NOTICE_OF_MB_VERIFICATION_SEND_TITLE', 'Moneybookers Account Verification Notice');
define('TEXT_MB_EMAIL_VERIFICATION_NOTICE_HEADING_CONTENT', "\n\n" . 'In order to protect the owner of the Moneybookers account from fraudulent use, a verification e-mail with "Subject: ');
define('TEXT_MB_EMAIL_VERIFICATION_NOTICE_ENDING_CONTENT', '" has been sent to the e-mail address shown above.  Please kindly check the e-mail account and follow the instructions stated to verify your Moneybookers e-mail account.  Thank you for helping us to serve you better.');
define('REMARK_MB_VERIFICATION_EMAIL_SENT', 'Moneybookers verification e-mail sent.');
define('REMARK_MB_VERIFICATION_EMAIL_NOTICE_SENT', 'Moneybookers verification e-mail notice sent.');

define('TEXT_IMAGE_MOUSEOVER_VERIFY', 'Information has been verified.');
define('TEXT_IMAGE_MOUSEOVER_NOT_VERIFY', 'Information has not been verified.');
define('TEXT_IMAGE_MOUSEOVER_NOT_ACCTIVATE', 'Account Not Activated');

define('WARNING_CAT_ACCESS_DENIED', 'Warning: You are not allowed to access this category.');

define('TEXT_SELECT_GAME', 'Select Game');

define('TEXT_BUYBACK_ORDER_ITEMS_DELIVERED_REMARK', 'The following items have been delivered:');
define('TEXT_BUYBACK_ORDER_ITEMS_DEDUCTED_REMARK', 'The following items have been deducted:');
define('TEXT_ORDER_HAS_BEEN_ROLLBACK', 'This order has been rollback.');


// File upload message
define('ERROR_NO_UPLOAD_FILE', 'Error: Source file does not exist.');
define('ERROR_DESTINATION_DOES_NOT_EXIST', 'Error: Destination does not exist.');
define('ERROR_DESTINATION_NOT_WRITEABLE', 'Error: Destination not writeable.');
define('ERROR_FILE_NOT_SAVED', 'Error: File upload not saved.');
define('ERROR_FILETYPE_NOT_ALLOWED', 'Error: File upload type not allowed.');
define('ERROR_FILESIZE_EXCEED', 'Error: File size has been exceeded the limit allowed.');
define('SUCCESS_FILE_SAVED_SUCCESSFULLY', 'Success: File upload saved successfully.');
define('WARNING_NO_FILE_UPLOADED', 'Warning: No file uploaded.');
define('WARNING_MISSING_FILE_UPLOADED', 'Warning: No file uploaded.');
define('WARNING_FILE_UPLOADS_DISABLED', 'Warning: File uploads are disabled in the php.ini configuration file.');
define('WARNING_NOTHING_TO_EXPORT', 'Warning: There is no any data to be exported.');
define('WARNING_NOTHING_TO_IMPORT', 'Warning: There is no any data to be imported.');

define('TEXT_DISPLAY_NUMBER_OF_PAYPALIPN_TRANSACTIONS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> transactions)'); // PAYPALIPN

define('BOX_HEADING_PAYPALIPN_ADMIN', 'Paypal IPN'); // PAYPALIPN
define('BOX_PAYPALIPN_ADMIN_TRANSACTIONS', 'Paypal IPN Transactions'); // PAYPALIPN
define('BOX_PAYPALIPN_ADMIN_TESTS', 'Paypal IPN Tests'); // PAYPALIPN
define('BOX_CATALOG_XSELL_PRODUCTS', 'Cross Selling'); // X-Sell
// direct top-up
define('ERROR_INCOMPLETE_REQUEST', 'Incomplete request.');
define('ERROR_DELIVERY_MODE_NOT_FOUND', 'This product does not support delivery mode configuration.');

if (file_exists(DIR_WS_LANGUAGES . 'add_ccgvdc_english.php')) {
    require(DIR_WS_LANGUAGES . 'add_ccgvdc_english.php');
}

// for payment method legend colour
define('IMAGE_COLOUR_PATH', (getenv('HTTPS') == 'on' ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN . 'htmlarea/images/');
define('IMAGE_LEGEND_COLOUR_SELECTION', IMAGE_COLOUR_PATH . 'ed_color_bg.gif');

// supplier key
define('KEY_SP_TIME_ZONE', "sp_time_zone");
define('KEY_SP_LANG', "sp_lang");

// system log comment
define('LOG_QTY_ADJUST', "Quantity Adjustment \n%s");
define('LOG_SALES_ORDER', '##%d##: ##%d## ##%d##');
define('LOG_SALES_COMPLETED', '##%d##: ##%d## ##%d##');
define('LOG_SALES_CANCELLED', '##%d##: ##%d## ##%d##');
define('LOG_SALES_NF_DISPUTE', '##%d##: Dispute');
define('LOG_SALES_NF_REFUND', '##%d##: Refund');
define('LOG_SALES_REFUNDED', '##%d##: ##%d## ##%d##');
define('LOG_PARTIAL_DELIVERY_SALES', '##%d##: Partial Delivery Sales');
define('LOG_COMPENSATE_DELIVERY', '##%d##: Compensate Delivery');
define('LOG_COMPENSATE_ORDER', '##%d##: Compensation');
define('LOG_CDKEY_MANUAL_RELEASE', '##%d##: CD Key Manual Release');
define('LOG_CDKEY_VIEWED_MANUAL_RELEASE', '##%d##: Viewed CD Key Manual Release');
define('LOG_REVERSED_FROM_VERIFYING_2_PENDING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_PROCESSING_2_PENDING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_COMPLETE_2_PROCESSING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_COMPLETE_2_VERIFYING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_CANCEL_2_PENDING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_CANCEL_2_PROCESSING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_REFUND_2_VERIFYING', '##%d##: ##%d## ##%d##');
define('LOG_REVERSED_FROM_REFUND_2_PROCESSING', '##%d##: ##%d## ##%d##');
define('LOG_NEW_PRODUCT', "Product Added \n%s");
define('LOG_DELETE_PRODUCT', 'Product Deleted');
define('LOG_PRICE_ADJUST', 'Price Adjustment');
define('LOG_PRODUCT_BUNDLE', 'Product Bundle Adjustment');
define('LOG_PRICE_OTHER_ADJUST', 'Other Price Adjustment');
define('LOG_NAME_ADJUST', 'Name Adjustment');
define('LOG_STATUS_ADJUST', 'Status Adjustment');
define('LOG_LOCATION_ADJUST', 'Location Adjustment');
define('LOG_REORDER_ADJUST', "Reorder Level Adjustment \n%s");
define('LOG_DISPLAY_ADJUST', 'Display Adjustment');
define('LOG_DELETE_CDKEY', 'CD Key Deleted');
define('LOG_CDKEY_ID_STR', 'CD Key ID: %s');
define('LOG_URL_ALIAS_ADJUST', 'URL Alias Adjustment');
define('LOG_ZERO_PRICE_PRODUCT_SETTING_ADJUST', 'Zero Price Product Setting');
define('LOG_PRODUCT_COST_ADJUST', 'Product Cost Adjustment');
define('LOG_PRODUCT_COST_COMPANY_UPDATE', 'Product Cost Company Updated');
define('LOG_PRODUCT_COST_COMPANY_ADD', 'Product Cost Company Added');
define('LOG_PRODUCT_MODEL_UPDATE', 'Product Model Updated');
define('LOG_PRODUCT_COST_ADD', 'Product Cost Added');
define('LOG_PRODUCT_MODEL_ADD', 'Product Model Added');
define('LOG_PRODUCT_STOCK_QUANTITY_ADD', 'Out Of Stock Rule\'s Stock Quantity Added');
define('LOG_PRODUCT_STOCK_QUANTITY_UPDATE', 'Out Of Stock Rule\'s Stock Quantity Updated');
define('LOG_PRODUCT_OUT_OF_STOCK_RULE_ADD', 'Out Of Stock Rule Added');
define('LOG_PRODUCT_OUT_OF_STOCK_RULE_UPDATE', 'Out Of Stock Rule Updated');
define('LOG_PRODUCT_PAYMENT_METHODS_RESTRICTIONS', 'Products Level Payment Methods Restrictions');
define('LOG_CATEGORY_PAYMENT_METHODS_RESTRICTIONS', 'Category Level Payment Methods Restrictions');

define('LOG_BUYBACK_ORDER', '##BUYBACK:%d##');
define('LOG_SUPPLIER_ORDER', '##SUPPLIER_ORDER:%d##');

define('TITLE_TRANS_PAYMENT', 'Payment %s');
define('TITLE_TRANS_PAYMENT_WITHDRAW', 'OffGamers Payment %s');
define('TITLE_TRANS_CUSTOMER_ORDER', 'Customer Order %s');
define('TITLE_TRANS_SUPPLIER_ORDER', 'Supplier Order %s');
define('TITLE_TRANS_PWL_ORDER', 'PWL Order %s');
define('TITLE_TRANS_BUYBACK_ORDER', 'Buyback Order %s');
define('TITLE_TRANS_ADJUSTMENT', 'Adjustment');
define('TITLE_TRANS_REDEEM_RECORD', 'Redemption RDN-%s');
define('TITLE_TRANS_STORE_CREDIT', 'Store Credit %s');
define('TITLE_TRANS_PURCHASE_ORDER', 'Purchase Order %s');
define('TITLE_TRANS_DTU_PAYMENT_REQUEST', 'DTU Payment Request %s');
define('TITLE_TRANS_API_PAYMENT_REQUEST', 'API Replenish PO Request %s');
define('TITLE_TRANS_CDK_PAYMENT_REQUEST', 'PO CDK Payment Request %s');

define('LOG_ACC_STAT_MANUAL_DEDUCTION', 'Manual Deduction');
define('LOG_ACC_STAT_MANUAL_ADDITION', 'Manual Addition');
define('LOG_ACC_STAT_COMPENSATE', 'Compensate');
define('LOG_ACC_STAT_PAYMENT_WITHDRAW', 'OffGamers Payment');
define('LOG_PAYMENT_CANCELLATION', 'Cancelation of Payment %s');
define('LOG_REDEEM_CANCELLATION', 'Cancelation of Redemption %s');

define('LOG_SC_STAT_DISBURSEMENT', 'Disbursement');
define('LOG_SC_STAT_MANUAL_DEDUCTION', 'Manual Deduction');
define('LOG_SC_STAT_MANUAL_ADDITION', 'Manual Addition');
define('LOG_SC_STAT_SC_SALES', 'Store Credit Sales');
define('LOG_SC_STAT_PAYMENT_WITHDRAW', 'OffGamers Payment');
define('LOG_SC_ACTIVITY_TYPE_COMPENSATE', 'C');
define('LOG_SC_ACTIVITY_TYPE_MANUAL_ISSUE', 'MI');
define('LOG_SC_ACTIVITY_TYPE_MANUAL_RECLAIM', 'MR');
define('LOG_SC_ACTIVITY_TYPE_PURCHASE', 'P');
define('LOG_SC_ACTIVITY_TYPE_REFUND', 'R');
define('LOG_SC_ACTIVITY_TYPE_CANCEL', 'X');
define('LOG_SC_ACTIVITY_TYPE_REPURCHASE', 'RP');
define('LOG_SC_ACTIVITY_TYPE_BONUS', 'B');
define('LOG_SC_ACTIVITY_TYPE_SALES', 'S');
define('LOG_SC_ACTIVITY_TYPE_EXTRA_SALES', 'XS');
define('LOG_SC_ACTIVITY_TYPE_CONVERT', 'V');
define('LOG_SC_ACTIVITY_TYPE_PAYMENT_WITHDRAW', 'PW');

define('LOG_SP_STAT_MANUAL_DEDUCTION', 'Manual Deduction');
define('LOG_SP_STAT_MANUAL_ADDITION', 'Manual Addition');
define('LOG_SP_ACTIVITY_TYPE_COMPENSATE', 'C');
define('LOG_SP_ACTIVITY_TYPE_MANUAL_ISSUE', 'MI');
define('LOG_SP_ACTIVITY_TYPE_MANUAL_RECLAIM', 'MR');
define('LOG_SP_ACTIVITY_TYPE_PURCHASE', 'P');
define('LOG_SP_ACTIVITY_TYPE_PURCHASE_DEDUCT', 'PD');
define('LOG_SP_ACTIVITY_TYPE_REFUND', 'R');
define('LOG_SP_ACTIVITY_TYPE_CANCEL', 'X');
define('LOG_SP_ACTIVITY_TYPE_REPURCHASE', 'RP');
define('LOG_SP_ACTIVITY_TYPE_BONUS', 'B');

define('TEXT_COMMENT_ORDER_AMOUNT_RESERVED', 'This order amount has been reserved');
define('TEXT_COMMENT_ORDER_AMOUNT_REMOVED_FROM_RESERVED', 'This reserved order amount has been removed');

define('EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT', 'Customer Order Update Notification #%d');
define('EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT', 'Order ID: %s' . "\n" . 'Order Date: %s' . "\n" . '%s' . "\n" . 'Payment Method: %s' . "\n\n" . 'Customer ID: %s' . "\n" . 'Customer E-mail: %s' . "\n" . 'Customer Group: %s' . "\n" . 'Customer Country: %s' . "\n" . "\n\n" . 'Update Type: %s' . "\n" . 'Status Update: %s -> %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n" . 'Update Comment:' . "\n" . '%s');
define('EMAIL_SUPPLIER_ORDER_UPDATE_NOTIFICATION_SUBJECT', 'Supplier Order Update Notification #%d');
define('EMAIL_SUPPLIER_ORDER_UPDATE_NOTIFICATION_CONTENT', 'Supplier Order ID: %s' . "\n" . 'Supplier Order Date: %s' . "\n" . 'Supplier Order Payable Amount: %s' . "\n" . 'Supplier Order Title: %s' . "\n\n" . 'Update Type: %s' . "\n" . 'Status Update: %s -> %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n" . 'Update Comment:' . "\n" . '%s');
define('EMAIL_BUYBACK_ORDER_UPDATE_NOTIFICATION_SUBJECT', 'Buyback Order Update Notification #%d');
define('EMAIL_BUYBACK_ORDER_UPDATE_NOTIFICATION_CONTENT', 'Buyback Order ID: %s' . "\n" . 'Buyback Order Date: %s' . "\n" . 'Buyback Order Payable Amount: %s' . "\n\n" . 'Update Type: %s' . "\n" . 'Status Update: %s -> %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n" . 'Update Comment:' . "\n" . '%s');
define('EMAIL_PWL_ORDER_UPDATE_NOTIFICATION_SUBJECT', 'Powerleveling Order Update Notification #%s');
define('EMAIL_PWL_ORDER_UPDATE_NOTIFICATION_CONTENT', 'Powerleveling Order ID: %s' . "\n" . 'Powerleveling Order Date: %s' . "\n" . 'Powerleveling Order Payable Amount: %s' . "\n\n" . 'Update Type: %s' . "\n" . 'Status Update: %s -> %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n" . 'Update Comment:' . "\n" . '%s');
define('EMAIL_PURCHASE_ORDER_UPDATE_NOTIFICATION_SUBJECT', 'Purchase Order Update Notification #%s');
define('EMAIL_PURCHASE_ORDER_UPDATE_NOTIFICATION_CONTENT', 'Purchase Order ID: %s' . "\n" . 'Purchase Order Date: %s' . "\n\n" . 'Update Type: %s' . "\n" . 'Status Update: %s -> %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n" . 'Update Comment:' . "\n" . '%s');
define('EMAIL_PRODUCT_ZERO_AMOUNT_NOTIFICATION_CONTENT', 'Product ID: %s' . "\n" . 'Product Name: %s' . "\n" . 'Price Update: %s -> %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . " (%s)" . "\n\n");
define('EMAIL_PRODUCT_PRICE_CHANGES_NOTIFICATION_CONTENT', 'Product ID: %s' . "\n" . 'Product: %s > %s' . "\n" . 'Selling Price: %s %s' . "\n" . 'Other Price: %s' . "\n" . 'Price Update: %s -> %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . " (%s)" . "\n\n");
define('EMAIL_PRODUCT_PRICE_CHANGES_NOTIFICATION_CONTENT_WITHOUT_USD', 'Product ID: %s' . "\n" . 'Product: %s > %s' . "\n" . 'Selling Price: %s %s (Equivalent USD %s )' . "\n" . 'Other Price: %s' . "\n" . 'Price Update: %s -> %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . " (%s)" . "\n\n");
define('EMAIL_PRODUCT_STATUS_NOTIFICATION_CONTENT', 'Product ID: %s' . "\n" . 'Product Type: %s' . "\n" . 'Product Name: %s' . "\n" . 'Status Update: %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . " (%s)" . "\n\n");
define('EMAIL_PRODUCT_DISPLAY_STATUS_NOTIFICATION_CONTENT', 'Product ID: %s' . "\n" . 'Product Type: %s' . "\n" . 'Product Name: %s' . "\n" . 'Display Status Update: %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . " (%s)" . "\n\n");
define('EMAIL_PRODUCT_UPLOADED_NOTIFICATION_CONTENT', 'PO Ref Number: %s' . "\n" . 'Product ID: %s' . "\n" . 'Product: %s > %s' . "\n" . 'Selling Price: %s %s' . "\n" . 'Other Price: %s' . "\n" . 'Uploaded Unit Price: USD %s' . "\n" . 'GP: %s' . "\n" . 'Remark: %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . " (%s)" . "\n\n");
define('EMAIL_PRODUCT_UPLOADED_NOTIFICATION_CONTENT_WITHOUT_USD', 'PO Ref Number: %s' . "\n" . 'Product ID: %s' . "\n" . 'Product: %s > %s' . "\n" . 'Selling Price: %s %s (Equivalent USD %s)' . "\n" . 'Other Price: %s' . "\n" . 'Uploaded Unit Price: USD %s' . "\n" . 'GP: %s' . "\n" . 'Remark: %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . " (%s)" . "\n\n");
define('EMAIL_CUSTOMER_FLAG_UPDATE_NOTIFICATION_SUBJECT', 'Customer Flag Update Notification #%d');
define('EMAIL_CUSTOMER_FLAG_UPDATE_NOTIFICATION_CONTENT', 'Customer ID: %s' . "\n" . 'Customer Group: %s' . "\n\n" . 'Flag Update: Turn %s %s flag' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n" . 'Update Comment:' . "\n" . '%s');
define('EMAIL_PO_SUPPLIER_INACTIVE_NOTIFICATION_CONTENT', 'PO Supplier ID: %s' . "\n" . 'Flag Update: Turn %s %s flag' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n" . 'Update Comment:' . "\n" . '%s');
define('EMAIL_PO_NEW_PO_NOTIFICATION_CONTENT', 'PO ID: %s' . "\n" . 'PO Ref ID: %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n");
define('EMAIL_PO_WSC_AUTO_CREDITED_NOTIFICATION_CONTENT', 'PO ID: %s' . "\n" . 'PO Ref ID: %s' . "\n" . 'PO Complete Date: %s' . "\n" . 'PO Term: %s' . "\n" . 'Payment Require Date: %s' . "\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n");

define('EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION', 'Manual');
define('EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION', 'Automatic');

define('EMAIL_TEXT_STATUS_UPDATE_TITLE', '');
define('EMAIL_TEXT_ORDER_NUMBER', 'Order Number:');
define('EMAIL_TEXT_DATE_ORDERED', 'Order Date:');
define('EMAIL_TEXT_INVOICE_URL', 'Detailed Invoice:');
define('EMAIL_TEXT_SUBJECT', 'Order Update #%d');
define('EMAIL_TEXT_PARTIAL_DELIVERY', '');
define('EMAIL_TEXT_UPDATED_STATUS', 'Status: %s' . "\n\n");
define('EMAIL_TEXT_CLOSING', 'Thank you for all your support! For any enquiries or assistance, you may e-mail your enquiries to ' . EMAIL_TO . '. Please remember to include your Order Number when contacting us to expedite the process. Thanks again for shopping with us!' . ".\n");
define('EMAIL_TEXT_CLOSING_2', 'For any enquiries or assistance, you may use our Online Live Support service or feel free to leave us a message. Please remember to include your Order Number when contacting us to expedite the process. Thanks again for shopping with us!');
define('EMAIL_SEPARATOR', '------------------------------------------------------');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
define('EMAIL_HLA_NEW_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
define('EMAIL_HLA_NEW_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_HLA_NEW_BUYBACK_STATUS', 'Status: Pending');
define('EMAIL_HLA_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "New Buyback Order #%s")));
define('EMAIL_HLA_NEW_BUYBACK_BODY', "Thank you for selling your items to " . STORE_NAME . ".\n\n Supplier Order Summary:\n" . EMAIL_SEPARATOR . "\n");
define('EMAIL_HLA_NEW_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_HLA_NEW_BUYBACK_COMMENTS', 'Extra Information:');
define('EMAIL_HLA_NEW_BUYBACK_ORDER_CLOSING', "Please contact us via our Online Live Support service or e-<NAME_EMAIL> if you face an issue with the buyback. Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status. You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting " . STORE_NAME . '.');

define('EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_FOOTER);

define('EMAIL_HLA_BUYBACK_ORDER_GUIDE', '
<b>Order Flow : [Pending] --> [Processing] --> [Completed] or [Canceled]</b>

[Pending] : Order submission incomplete. Kindly login to OffGamers.com and submit the missing character information.
[Processing] : This delivery is being registered into our system.
[Completed] : This delivery has been registered completely.
[Canceled] : The order has been canceled.

<b>Note 1:</b> Please ensure the Account information you provided is 100% accurate.

<b>Note 2:</b> Once the Account information been submitted, please do not login to the account, either via the account management page or ingame.

Important: Please take note that Blizzard may lock the account if they detect a different IP accessing the account within a short period of time. As a precaution:

<b>Note 3:</b> Once your order is in the [Completed] status, payment will be made available to you within 15 minutes at your OffGamers Account. Use the [Withdraw] function to withdraw your payment and have it credited to a payment method of your choosing.
');


define('ENTRY_VERIFY_INFO', 'Click to verified');
define('ENTRY_EMAIL_VERIFY', '<span class="greenIndicator">E-mail Verified</span>');
define('ENTRY_EMAIL_NOT_VERIFY', '<span class="redIndicator">E-mail Not Verified</span>');
define('ENTRY_TELEPHONE_VERIFY', '<span class="greenIndicator">Telephone Verified</span>');
define('ENTRY_TELEPHONE_NOT_VERIFY', '<span class="redIndicator">Telephone Not Verified</span>');

define('ERROR_CAT_ACCESS_DENIED', 'Error: You do not have permission to access this category.');
define('ERROR_TRANS_UPDATE_DENIED', 'Error: You are not permitted for this update.');
define('ERROR_PERFORMED_ACTION_DENIED', 'Error: You are not permitted for this action.');
define('SUCCESS_CUSTOMERS_TELEPHONE_VERIFIED', "Customer's telephone have been verified");
define('SUCCESS_CUSTOMERS_TELEPHONE_UNVERIFIED', "Customer's telephone have been unverified");

define('SUCCESS_GENESIS_PROJECT_TURNED_OFF', 'Genesis Project is turned off.');
define('SUCCESS_GENESIS_PROJECT_TURNED_ON', 'Genesis Project is turned on.');

define('ERROR_STORE_BALANCE_CURRENCY_ACC_NOT_EXISTS', 'Error: This user does not has store balance in this currency');
define('ERROR_STORE_BALANCE_AMOUNT', 'Error: Invalid store balance amount');
define('ERROR_STORE_CREDIT_NOTE_CURRENCY_ACC_NOT_EXISTS', 'Error: This user does not has credit note in this currency');
define('ERROR_STORE_CREDIT_NOTE_AMOUNT', 'Error: Invalid credit note amount');

define('EMAIL_VERIFY_SEND_LINK', 'Click to send verification e-mail');

define('LINK_PROFILER', 'Profiler');

if (!defined('ORDERS_LOG_LOCK_ORDER'))
    define('ORDERS_LOG_LOCK_ORDER', '##l_1##');
if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))
    define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
if (!defined('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER'))
    define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');
if (!defined('ORDERS_LOG_LOCK_ORDER'))
    define('ORDERS_LOG_LOCK_ORDER', '##l_1##');

define('ERROR_UNABLE_TO_GROUP_DIFF_DELIVERY_MODE_PRODUCT', 'Error: Unable to group diff delivery mode product');
define('ERROR_UNABLE_TO_GROUP_DIFF_DIRECT_TOP_UP_GAMES', 'Error: Unable to group diff direct top-up game');

define('EMAIL_G2G_BUYBACK_SUBJECT', "New Sell Order #%s");
define('EMAIL_G2G_BUYBACK_BODY', "You have a new sell order. Please prepare the items for delivery as soon as possible or click \"Chat Now\" in your order details page to contact the buyer for delivery arrangement.\n\nOrder Summary:\n%s \n");
define('EMAIL_G2G_BUYBACK_CLOSE', "For any enquiries or assistance, you may use our Online Live Support service or e-mail your enquiries to %s. Please remember to include your Payment Number when contacting us to expedite the process. Thank you.");
define('EMAIL_G2G_BUYBACK_ORDER_NUMBER', 'Sell Order Number: %s');
define('EMAIL_G2G_BUYBACK_DATE_ORDERED', 'Sell Order Date: %s');
define('EMAIL_G2G_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
define('EMAIL_G2G_BUYBACK_PRODUCTS', "\n\n" . 'Products');
define('EMAIL_G2G_BUYBACK_ORDER_TOTAL', 'Total: %s');
define('EMAIL_G2G_BUYBACK_STATUS', 'Status: New Order');
define('EMAIL_G2G_BUYBACK_ORDER_GUIDE', '<b><a href="https://support.g2g.com/support/solutions/articles/5000001410">Order Flow</a> : [New Order] --> [Preparing] --> [Delivering] --> [Delivered] --> [Completed] or [Canceled]</b><br /><br /><a href="https://support.g2g.com/support/solutions/folders/5000007799">Read more</a> about selling at G2G and <a href="https://support.g2g.com/support/solutions/articles/5000001416">payment request schedule and fee.</a>');
define('EMAIL_G2G_BUYBACK_ORDER_CLOSING', "Thank you for supporting %s.");
define('EMAIL_G2G_ORDER_CLOSE', "For any enquiries or assistance, you may use our Online Live Support service or feel free to leave us a message. Please remember to include your Order Number when contacting us to expedite the process. Thanks again for shopping with us! \n\nOffGamers: http://support.offgamers.com/support/discussions \nG2G: http://support.g2g.com/support/discussions/");
define('CHANGE_REQUESTED','Change Requested');

//for delivery address details

define('TEXT_DELIVERY_DETAILS_TITLE', 'Delivery Details');
define('TEXT_DELIVERY_ADDRESS_RECIPIENT_NAME', 'Recipient Name');
define('TEXT_DELIVERY_ADDRESS_CONTACT_NUMBER', 'Contact Number');
define('TEXT_DELIVERY_ADDRESS', 'Address');
define('TEXT_DELIVERY_ADDRESS_PRODUCT_NAME', 'Product Name');
define('TEXT_DELIVERY_ADDRESS_QUANTITY', 'Quantity');

?>