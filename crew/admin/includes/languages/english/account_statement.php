<?php
/*
  	$Id: account_statement.php,v 1.13 2011/07/08 08:16:22 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_ACC_STAT_TITLE', 'Account Statement');

define('TABLE_SECTION_HEADING_ACC_STAT', '<b>Account statement for %s, %s [%s]</b>');

define('TABLE_HEADING_ACC_STAT_DATETIME', 'Date/Time');
define('TABLE_HEADING_ACC_STAT_ACTIVITY', 'Activity');
define('TABLE_HEADING_ACC_STAT_CUSTOMER_ORDER', 'COR');
define('TABLE_HEADING_ACC_STAT_CUSTOMER_ORDER_STATUS', 'Current COR Status');
define('TABLE_HEADING_ACC_STAT_CUSTOMER_AGING', 'Customer Aging (Month)');
define('TABLE_HEADING_ACC_STAT_RESERVE', 'Reserve');
define('TABLE_HEADING_ACC_STAT_PAYMENT_STATUS', 'Payment Status');
define('TABLE_HEADING_ACC_STAT_DEBIT', 'Debit');
define('TABLE_HEADING_ACC_STAT_CREDIT', 'Credit');
define('TABLE_HEADING_ACC_STAT_BALANCE', 'Balance');

define('TABLE_HEADING_ACC_STAT_MANUAL_ADD', 'Manual Addition');
define('TABLE_HEADING_ACC_STAT_MANUAL_DEDUCT', 'Manual Deduction');

define('ENTRY_USER_ROLE', 'User Group');
define('ENTRY_USER_EMAIL', 'User Email');
define('ENTRY_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', 'Payment ID');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_TRANSACTION_ID', 'Transaction ID');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');

define('ENTRY_ACC_STAT_CURRENCY_ACCOUNT', 'Currency Account');
define('ENTRY_ACC_STAT_WSC_ACCOUNT', 'WSC Account');
define('ENTRY_ACC_STAT_DEDUCT_AMOUNT', 'Deduct Amount');
define('ENTRY_ACC_STAT_ADD_AMOUNT', 'Addition Amount');
define('ENTRY_ACC_STAT_MANUAL_ADJUST_COMMENT', 'Comment: ');
define('ENTRY_ACC_STAT_SUPPLIER_NOTIFY', 'Notify Supplier:');
define('ENTRY_ACC_STAT_COMMENT_SHOW_SUPPLIER', 'Show Supplier:');

define('TEXT_ACC_STAT_NOT_APPLICABLE', '-');
define('TEXT_ACC_STAT_TRANS_VERIFIED', 'Verified');
define('TEXT_ACC_STAT_TRANS_NOT_VERIFIED', 'Unverify');
define('TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE', '%s on %s');
define('TEXT_ACC_STAT_FIXED_RESERVED', 'Fixed Reserve');
define('TEXT_ACC_STAT_DYNAMIC_RESERVED', 'Dynamic Reserve');
define('TEXT_ACC_STAT_TRANS_ID', '(Transaction ID: %s)');
define('TEXT_ACC_STAT_ADD_COMMENT_SHOW', 'Show Add Comment');
define('TEXT_ACC_STAT_ADD_COMMENT_HIDE', 'Hide Add Comment');
define('TEXT_ACC_STAT_NOTIFIED', 'Supplier Notified');
define('TEXT_ACC_STAT_NOT_NOTIFY', 'Supplier Not Notify');
define('TEXT_ACC_STAT_NOT_NOTIFY', 'Supplier Not Notify');
define('TEXT_ACC_STAT_MANUAL_ACTIVITY_SHOW', 'Show Supplier');
define('TEXT_ACC_STAT_MANUAL_ACTIVITY_HIDE', 'Not Show Supplier');
define('TEXT_ACC_STAT_DISABLE_WITHDRAWAL', 'Disable Withdrawal');

define('LINK_ACC_AGING_STATISTIC', '[<a href="javascript:;" onClick="%s">Aging Statistics</a>]');
define('LINK_ACC_STAT_RESERVE', '[<a href="javascript:;" onClick="%s">Add to Reserve</a>]');
define('LINK_ACC_STAT_LIFT_RESERVE', '[<a href="javascript:;" onClick="%s">Remove from Reserve</a>]');

define('ERROR_ACC_STAT_CURRENCY_ACC_NOT_EXISTS', 'Error: This user does not has store balance in this currency');
define('ERROR_ACC_STAT_INVALID_DEDUCTION_AMOUNT', 'Error: Invalid manual deduction amount');
define('ERROR_ACC_STAT_INVALID_ADDITION_AMOUNT', 'Error: Invalid manual addition amount');
define('ERROR_ACC_STAT_COMMENTS_EMPTY', 'Error: Comments is not entered');
define('SUCCESS_ACC_STAT_MANUAL_DEDUCTION', 'Success: Manual deduction has been successfully performed');
define('SUCCESS_ACC_STAT_MANUAL_ADDITION', 'Success: Manual addition has been successfully performed');
define('SUCCESS_ACC_COMMENTS', 'Success: Comments has been successfully added');
define('SUCCESS_ACC_COMMENTS_EMAIL', 'Success: Supplier Notification email has been successfully sent');

define('JS_ERROR_ACC_STAT_CURRENCY_ACC_NOT_EXISTS', 'This user does not has store balance in this currency');
define('JS_ERROR_ACC_STAT_NO_PERMISSION', 'You have no permission for this action');
define('JS_ERROR_ACC_STAT_TRANS_NOT_EXISTS', 'The transaction does not exists');
define('JS_ERROR_ACC_STAT_TRANS_MODE_CHANGED', 'The transaction reserve status has been modified');
define('JS_ERROR_ACC_STAT_TRANS_INVALID_MODE', 'Unknown operation');

// Email definitions
define('EMAIL_SEPARATOR', '--------------------------------------------------------------');
define('EMAIL_ACC_STAT_UPDATE_SUBJECT', "Account Statement Update #%s");

define('EMAIL_ACC_STAT_TEXT_TITLE', 'Your account statement transaction has been updated as below.');
define('EMAIL_ACC_STAT_TEXT_TRANSACT_NUMBER', 'Transaction Number: %s');
define('EMAIL_ACC_STAT_TEXT_TRANSACT_DATE', 'Transaction Date: %s');
define('EMAIL_ACC_STAT_TEXT_ACTIVITY', 'Activity: %s');
define('EMAIL_ACC_STAT_TEXT_COMMENTS', 'Updated Comment:' . "\n%s\n");

define('EMAIL_ACC_STAT_NOTIFY_UPDATE_SUBJECT', "%s #%s");
define('EMAIL_ACC_STAT_NOTIFY_TRANSACT_ID', "Transaction ID: %s");
define('EMAIL_ACC_STAT_NOTIFY_TRANSACT_DATE', "Transaction Date: %s");
define('EMAIL_ACC_STAT_NOTIFY_TRANSACT_AMOUNT', "%s Amount: %s");
define('EMAIL_ACC_STAT_NOTIFY_UPDATE_USER', "Update User: %s");
define('EMAIL_ACC_STAT_NOTIFY_UPDATE_IP', "Update IP: %s");
define('EMAIL_ACC_STAT_NOTIFY_UPDATE_COMMENT', "Update Comment: \n%s\n");

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
?>