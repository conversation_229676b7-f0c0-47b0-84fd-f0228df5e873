<?
/*
  	$Id: admin_members.php,v 1.19 2010/04/01 04:25:10 henry.chow Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

if ($HTTP_GET_VARS['gID']) {
  define('HEADING_TITLE', 'Admin Groups');
} elseif ($HTTP_GET_VARS['gPath']) {
  define('HEADING_TITLE', 'Define Groups');
} else {
  define('HEADING_TITLE', 'Members &amp; Groups');
}

define('TEXT_COUNT_GROUPS', 'Groups: ');

define('TABLE_HEADING_NAME', 'Name');
define('TABLE_HEADING_EMAIL', 'Email Address');
define('TABLE_HEADING_PASSWORD', 'Password');
define('TABLE_HEADING_CONFIRM', 'Confirm Password');
define('TABLE_HEADING_GROUPS', 'Groups Level');
define('TABLE_HEADING_CREATED', 'Account Created');
define('TABLE_HEADING_MODIFIED', 'Account Created');
define('TABLE_HEADING_USED_LIMIT', 'Daily Used/Limit (USD)');
define('TABLE_HEADING_LOGDATE', 'Last Access');
define('TABLE_HEADING_LOGNUM', 'LogNum');
define('TABLE_HEADING_LOG_NUM', 'Log Number');
define('TABLE_HEADING_LOGIN_FAILED_ATTEMPT', 'Login Failed');
define('TABLE_HEADING_ACTION', 'Action');

define('TABLE_HEADING_GROUPS_NAME', 'Groups Name');
define('TABLE_HEADING_GROUPS_MEMBERS_NUM', 'Total Members');
define('TABLE_HEADING_GROUPS_DEFINE', 'Boxes and Files Selection');
define('TABLE_HEADING_GROUPS_GROUP', 'Level');
define('TABLE_HEADING_GROUPS_CATEGORIES', 'Categories Permission');
define('TABLE_HEADING_GROUPS_PAYMENT_METHODS', 'Payment Methods Permission');
define('TABLE_HEADING_GROUPS_USER_FLAGS', 'Customer Flags Permission');
define('TABLE_HEADING_GROUPS_SITE_CODE', 'Buyback Site Permission');
define('TABLE_HEADING_GROUPS_PRODUCT', 'Product Type Permission');
define('TABLE_HEADING_GROUPS_PAYMENT_METHODS_MESSAGE', 'Allow to view');
define('TABLE_HEADING_GROUPS_PAYMENT_METHODS_PAYMENT_MESSAGE', 'Allow to process');
define('TABLE_HEADING_GROUPS_PAYMENT_METHODS_UPDATE_MESSAGE', 'Allow to update');
define('TABLE_HEADING_GROUPS_PAYMENT_METHODS_CANCEL_MESSAGE', 'Allow to cancel');
define('TABLE_HEADING_GROUPS_USER_FLAGS_MESSAGE', 'Allow to edit');
define('TABLE_HEADING_GROUPS_SITE_CODE_MESSAGE', 'Allow to view');
define('TABLE_HEADING_GROUPS_PRODUCT_MESSAGE', 'Allow to access');
define('TABLE_HEADING_GROUPS_CONTROL', 'Groups Control Permission');
define('TABLE_HEADING_GROUPS_CONTROL_MESSAGE', 'Authorized to control');

define('TEXT_INFO_HEADING_DEFAULT', 'Admin Member ');
define('TEXT_INFO_HEADING_DELETE', 'Delete Permission ');
define('TEXT_INFO_HEADING_EDIT', 'Edit Category / ');
define('TEXT_INFO_HEADING_NEW', 'New Admin Member ');

define('TEXT_INFO_DEFAULT_INTRO', 'Member group');
define('TEXT_INFO_DELETE_INTRO', 'Remove <nobr><b>%s</b></nobr> from <nobr>Admin Members?</nobr>');
define('TEXT_INFO_DELETE_INTRO_NOT', 'You can not delete <nobr><b>%s</b> member!</nobr>');
define('TEXT_INFO_EDIT_INTRO', 'Set permission level here: ');

define('TEXT_INFO_FULLNAME', 'Name: ');
define('TEXT_INFO_FIRSTNAME', 'Firstname: ');
define('TEXT_INFO_LASTNAME', 'Lastname: ');
define('TEXT_INFO_EMAIL', 'Email Address: ');
define('TEXT_INFO_PASSWORD', 'Password: ');
define('TEXT_INFO_CONFIRM', 'Confirm Password: ');
define('TEXT_INFO_CREATED', 'Account Created: ');
define('TEXT_INFO_MODIFIED', 'Account Modified: ');
define('TEXT_INFO_LOGDATE', 'Last Access: ');
define('TEXT_INFO_LOGNUM', 'Log Number: ');
define('TEXT_INFO_GROUP', 'Group Level: ');
define('TEXT_INFO_ERROR', '<font color="red">Email address has already been used! Please try again.</font>');

define('TEXT_INFO_BUTTON_CREDIT_LIMIT', 'Credit Limit');
define('TEXT_INFO_HEADING_CREDIT_LIMIT', 'Credit Limit');
define('TEXT_INFO_DAILY_LIMIT', 'Daily Limit: ');
define('TEXT_INFO_TODAY_USED_LIMIT', 'Today Used Limit: ');
define('TEXT_INFO_RESET', 'Reset');
define('TEXT_INFO_RESET_TEAM_LIMIT', 'Reset Team Daily Limit: ');
define('TEXT_INFO_RESET_TEAM_LIMIT_TOTAL', 'Today Reset Total: ');

define('JS_ALERT_FIRSTNAME', '- Required: Firstname \n');
define('JS_ALERT_LASTNAME', '- Required: Lastname \n');
define('JS_ALERT_EMAIL', '- Required: Email address \n');
define('JS_ALERT_EMAIL_FORMAT', '- Email address format is invalid! \n');
define('JS_ALERT_EMAIL_USED', '- Email address has already been used! \n');
define('JS_ALERT_LEVEL', '- Required: Group Member \n');
define('JS_ALERT_DAILY_LIMIT_ENTERED_MUST_BE_A_NUMBER', 'Daily Limit entered must be a non-negative number.');
define('JS_CONFIRM_LOAD_LIST', 'Are you sure to load the permissions setting from this admin group?');
define('JS_CONFIRM_REACTIVATE_ADMIN_MEMBERS', 'Are you sure to clear failed login attempt counter?');

define('ADMIN_EMAIL_SUBJECT', 'New Admin Member');
define('ADMIN_EMAIL_TEXT', 'Hi %s,' . "\n\n" . 'You can access the admin panel with the following password. Once you access the admin, please change your password!' . "\n\n" . 'Website : %s' . "\n" . 'Username: %s' . "\n" . 'Password: %s' . "\n\n" . 'Thanks!' . "\n" . '%s' . "\n\n" . 'This is an automated response, please do not reply!'); 

define('TEXT_INFO_HEADING_DEFAULT_GROUPS', 'Admin Group ');
define('TEXT_INFO_HEADING_DELETE_GROUPS', 'Delete Group ');

define('TEXT_INFO_DEFAULT_GROUPS_INTRO', '<b>NOTE:</b><li><b>Edit:</b> edit group name.</li><li><b>Delete:</b> delete group.</li><li><b>Permission:</b> define group access.</li><li><b>Member:</b> Add/Remove admin member to/from this group.</li>');
define('TEXT_INFO_DELETE_GROUPS_INTRO', 'It\'s also will delete member of this group. Are you sure want to delete <nobr><b>%s</b> group?</nobr>');
define('TEXT_INFO_DELETE_GROUPS_INTRO_NOT', 'You can not delete this groups!');
define('TEXT_INFO_GROUPS_INTRO', 'Give an unique group name. Click next to submit.');
define('TEXT_INFO_UPDATE_LOG_ADMIN_DELETE', '%s<br>(Deleted: %s)');

define('TEXT_INFO_HEADING_GROUPS', 'New Group');
define('TEXT_INFO_GROUPS_NAME', ' <b>Group Name:</b><br>Give an unique group name. Then, click next to submit.<br>');
define('TEXT_INFO_GROUPS_NAME_FALSE', '<font color="red"><b>ERROR:</b> At least the group name must have more than 5 character!</font>');
define('TEXT_INFO_GROUPS_NAME_USED', '<font color="red"><b>ERROR:</b> Group name has already been used!</font>');
define('TEXT_INFO_GROUPS_LEVEL', 'Group Level: ');
define('TEXT_INFO_GROUPS_BOXES', '<b>Boxes Permission:</b><br>Give access to selected boxes.');
define('TEXT_INFO_GROUPS_BOXES_INCLUDE', 'Include files stored in: ');

define('TEXT_INFO_HEADING_EDIT_GROUP', 'Edit Group');
define('TEXT_INFO_EDIT_GROUP_INTRO', ' <b>Group Name:</b><br>Give an unique group name. Then, click save to submit.<br>');

define('TEXT_INFO_HEADING_EDIT_GROUP_MEMBERS', 'Edit Group Members');
define('TEXT_INFO_EDIT_GROUP_MEMBERS_INTRO', '<b>Group Members:</b>');
define('TEXT_INFO_EDIT_GROUP_MEMBERS_REMOVE', 'Select and click remove to remove selected members from current group to unassigned group.');
define('TEXT_INFO_EDIT_GROUP_MEMBERS_ADD', 'Select and click add to add selected members to current group.');

define('TEXT_INFO_HEADING_DEFINE', 'Define Group');
define('TEXT_LOAD_FROM_GROUP', 'Load permissions from');

if ($HTTP_GET_VARS['gPath'] == 1) {
  define('TEXT_INFO_DEFINE_INTRO', '<b>%s :</b><br>You can not change file permission for this group.<br><br>');
} else {
  define('TEXT_INFO_DEFINE_INTRO', '<b>%s :</b><br>Change permission for this group by selecting or unselecting boxes and files provided. Click <b>save</b> to save the changes.<br><br>');
}

define('TEXT_INFO_GROUPS_ALLOWED', 'You are allowed to view only members in groups below: ');
define('TEXT_INFO_GROUPS_AUTHORIZED', 'Authorized Groups');
define('TEXT_INFO_GROUPS_UNASSIGNED', 'Unassigned Group');
define('TEXT_INFO_GROUPS_AND_UNASSIGNED', 'Unassigned Group');

define('TEXT_INFO_MEMBERS_EDIT', 'Edit');
define('TEXT_INFO_MEMBERS_GROUP_EDIT', 'Group');
define('TEXT_INFO_MEMBERS_DELETE', 'Delete');
define('TEXT_INFO_GROUPS_PERMISSION', 'Permission');
define('TEXT_INFO_GROUPS_MEMBERS', 'Member');
define('TEXT_INFO_GROUPS_SAVE', 'Save');
define('TEXT_INFO_GROUPS_INSERT', 'Insert');
define('TEXT_INFO_GROUPS_EDIT', 'Edit');
define('TEXT_INFO_GROUPS_DELETE', 'Delete');
define('TEXT_INFO_GROUPS_CANCEL', 'Cancel');
define('TEXT_INFO_GROUPS_GROUPS', 'Group');
define('TEXT_INFO_GROUPS_NEW', 'New Group');
define('TEXT_INFO_GROUPS_BACK', 'Back');
define('TEXT_INFO_GROUPS_MEMBERS_ADD', 'Add');
define('TEXT_INFO_GROUPS_MEMBERS_NEW', 'New Member');
define('TEXT_INFO_GROUPS_MEMBERS_REMOVE', 'Remove');
define('TEXT_INFO_GROUPS_MEMBERS_BACK', 'Back');
define('TEXT_INFO_GROUPS_MEMBERS_INSERT', 'Insert');
define('TEXT_INFO_GROUPS_MEMBERS_UPDATE', 'Update');
define('TEXT_INFO_GROUPS_MEMBERS_DELETE', 'Delete');
define('TEXT_INFO_GROUPS_MEMBERS_CANCEL', 'Cancel');
define('TEXT_INFO_CREDIT_LIMIT_UPDATE', 'Update');
define('TEXT_INFO_CREDIT_LIMIT_CANCEL', 'Cancel');
define('TEXT_INFO_GROUPS_EXPORT_PERMISSIONS', 'Export Permissions');

define('TEXT_SELECT_REMOVE_MEMBER', '---Remove member from this group---');
define('TEXT_SELECT_ADD_MEMBER', '---Add member to this group---');

define('JS_ALERT_GROUP_ADD_MEMBER_NO_SELECTION', 'Error: Please select admin member to be added.');
define('JS_ALERT_GROUP_REMOVE_MEMBER_NO_SELECTION', 'Error: Please select admin member to be removed.');
define('JS_ALERT_GROUP_REMOVE_MEMBER_ROOT_SELECTED', 'Error: root@localhost cannot be removed from Top Administrator group.');
define('JS_ALERT_GROUP_EDIT_MEMBER_ERROR', 'Error: Invalid Access.');
define('JS_CONFIRM_GROUP_ADD_MEMBER', 'Add selected admin member to current group?');
define('JS_CONFIRM_GROUP_REMOVE_MEMBER', 'Remove selected admin member from current group?');

define('SUCCESS_NEW_MEMBER_CREATED', 'Success: New admin member <b>%s</b> created.');
define('SUCCESS_EDIT_MEMBER', 'Success: Changes for admin member <b>%s</b> saved.');
define('SUCCESS_DELETE_MEMBER', 'Success: Admin member <b>%s</b> deleted.');
define('SUCCESS_DELETE_GROUP', 'Success: Admin group deleted.');
define('SUCCESS_EDIT_GROUP', 'Success: Changes for admin member <b>%s</b> saved.');
define('SUCCESS_ADD_GROUP_MEMBER', 'Success: Admin member added to this group.');
define('SUCCESS_REMOVE_GROUP_MEMBER', 'Success: Admin member removed from this group.');
define('SUCCESS_NEW_GROUP_CREATED', 'Success: New admin group <b>%s</b> created.');
define('SUCCESS_UPDATE_GROUP_PERMISSION_BOXES_FILES', 'Success: Boxes and Files Selection Permission updated.');
define('SUCCESS_UPDATE_GROUP_PERMISSION_PAYMENT', 'Success: Payment Methods Permission updated.');
define('SUCCESS_UPDATE_GROUP_PERMISSION_CUSTOMER_FLAGS', 'Success: Customer Flags Permission updated.');
define('SUCCESS_UPDATE_GROUP_PERMISSION_BUYBACK_SITE', 'Success: Buyback Site Permission updated.');
define('SUCCESS_UPDATE_GROUP_PRODUCTS', 'Success: Products Type Permission updated.');
define('SUCCESS_UPDATE_GROUP_PERMISSION_GROUPS_CONTROL', 'Success: Groups Control Permission updated.');
define('SUCCESS_UPDATE_CREDIT_LIMIT', 'Success: Credit limit has been successfully updated.');
define('SUCCESS_REACTIVATE_MEMBER', 'Success: Admin member activated.');

define('ERROR_MEMBER_NOT_EXIST', 'Error: Member not exist!');
define('ERROR_GROUP_NOT_EXIST', 'Error: Group not exist!');
define('ERROR_UPDATE_CREDIT_LIMIT', 'Error: Fail to update credit limit, reset total has exceed limit.');

define('TEXT_INFO_BUTTON_CDKEY_VIEW_LIMIT', 'CDKey View Limit');
define('TEXT_INFO_HEADING_CDKEY_VIEW_LIMIT', 'CDKey View Limit');
define('TEXT_INFO_DAILY_LIMIT', 'Daily Limit: ');
define('TEXT_INFO_TODAY_USED_LIMIT', 'Today Used Limit: ');
define('TEXT_INFO_CDKEY_VIEW_LIMIT_UPDATE', 'Update');
define('TEXT_INFO_CDKEY_VIEW_LIMIT_CANCEL', 'Cancel');
define('SUCCESS_UPDATE_CDKEY_VIEW_LIMIT', 'Success: CDKey View limit has been successfully updated.');
?>