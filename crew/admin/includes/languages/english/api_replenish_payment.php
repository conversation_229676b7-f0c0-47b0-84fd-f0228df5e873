<?php

define('TABLE_HEADING_API_PAYMENT_INFO', 'Payment Information');
define('TABLE_HEADING_API_SUPPLIER_INFO', 'Supplier Information');
define('TABLE_HEADING_API_PRODUCT_DETAILS', 'API Product Details');
define('TABLE_HEADING_API_CB_PRODUCT_DETAILS', 'API Charge Back Product Details ( - )');
define('TABLE_HEADING_API_DN_PRODUCT_DETAILS', 'API Debit Note Product Details ( - )');

define('ENTRY_SEARCH_API_ID', 'API Request Number');

define('ENTRY_API_FORM_CONTACT_PERSON', 'Contact Person');
define('ENTRY_API_FORM_CREDIT_NOTE', 'Credit Note');
define('ENTRY_API_FORM_CREDIT_NOTE_AVAIL_AMOUNT', 'Credit Note Available Amount');
define('ENTRY_API_FORM_CURRENCY', 'Currency');
define('ENTRY_API_FORM_CURRENCY_CONFIRM_RATE', 'Confirm Rate');
define('ENTRY_API_FORM_BULK_REMARKS', 'Bulk Remarks');
define('ENTRY_API_FORM_CURRENCY_SUGGEST_RATE', 'Suggested Rate');
define('ENTRY_API_FORM_DISBURSEMENT_METHOD', 'Disbursement Method');
define('ENTRY_API_FORM_START_DATE', 'Start Date');
define('ENTRY_API_FORM_END_DATE', 'End Date');
define('ENTRY_API_FORM_ISSUE_DATE', 'API Request Date');
define('ENTRY_API_FORM_PAYMENT_TERM', 'Payment Term');
define('ENTRY_API_FORM_BILLING_STATUS', 'Billing Status');
define('ENTRY_API_FORM_WITHDRAW_STATUS', 'Withdraw Status');
define('ENTRY_API_FORM_PAYMENT_TRANS', 'Payment Transaction(s)');
define('ENTRY_API_FORM_API_HISTORY', 'API Request History');
define('ENTRY_API_FORM_API_REF_ID', 'API Request Number');
define('ENTRY_API_FORM_API_STATUS', 'API Request Status');
define('ENTRY_API_FORM_API_STATISTICS', 'API Request Statistics');
define('ENTRY_API_FORM_SUGGESTED_SUPPLIER', 'Suggested Supplier/Publisher');
define('ENTRY_API_FORM_SUPPLIER', 'Supplier');
define('ENTRY_API_FORM_SUPPLIER_ADDRESS', 'Supplier Address');
define('ENTRY_API_FORM_SUPPLIER_PAYMENT_TERM', 'Payment Term');
define('ENTRY_API_FORM_DELIVERY_ADDRESS', 'Delivery Address');
define('ENTRY_API_FORM_UPLOAD_CSV_MATCHING', 'Upload CSV Matching');

// API Report form
define('ENTRY_API_FORM_START_DATE_REPORT', 'Start Date');
define('ENTRY_API_FORM_END_DATE_REPORT', 'End Date');
define('ENTRY_API_FORM_REPORT_PRODUCT_ID', 'Product ID');
define('ENTRY_API_FORM_REPORT_ORDER_NUMBER', 'Order Number');
define('ENTRY_API_FORM_REPORT_CDKEY_ID', 'CDKEY ID');

// label for create new API form
define('TABLE_HEADING_API_SELECT', 'Select');
define('TABLE_HEADING_API_DATETIME', 'Date');
define('TABLE_HEADING_API_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_API_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_API_QUANTITY', 'Quantity');
define('TABLE_HEADING_API_PUBLISHER_NAME', 'Publisher');
define('TABLE_HEADING_API_ORDER_NO', 'Order #');
define('TABLE_HEADING_API_CDKID', 'CDK ID');
define('TABLE_HEADING_API_SKU', 'SKU');
define('TABLE_HEADING_API_SRP', 'Selling Price<br>%s');
define('TABLE_HEADING_API_AMOUNT', 'Amount<br>%s');
define('TABLE_HEADING_API_SETTLE_AMOUNT', 'Cost Amount<br>%s');
define('TABLE_HEADING_API_BILLING_STATUS', 'API Payment Status');
define('TABLE_HEADING_API_SORT_UP', '&nbsp;&uarr;');
define('TABLE_HEADING_API_SORT_DOWN', '&nbsp;&darr;');

// heading for PO items in new PO form
define('TABLE_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_PRODUCT_AVAIL_QTY', 'Available Qty');
define('TABLE_HEADING_PRODUCT_ACTUAL_QTY', 'Actual Qty');
define('TABLE_HEADING_MULTIPLIER', 'Multiplier');
define('TABLE_HEADING_LAST_N_DAYS', 'Last N Days');
define('TABLE_HEADING_SUGGEST_RESTOCK_AMOUNT', 'Suggested Restock Amount');
define('TABLE_HEADING_SELLING_PRICE', "Selling Price<br>%s");
define('TABLE_HEADING_STOCK_PRICE', "Unit Price<br>%s");
define('TABLE_HEADING_SUBTOTAL', "Subtotal<br>%s");
define('TABLE_HEADING_TOTAL', "Total");
define('TABLE_HEADING_BOTTOM_SUBTOTAL', "Sub-Total");
define('TABLE_HEADING_BOTTOM_CHARGE_BACK', "Charge Back");
define('TABLE_HEADING_BOTTOM_DEBIT_NOTE', "Debit Note");
define('TABLE_HEADING_BOTTOM_TOTAL_AFTER_CHARGE_BACK', "Total After Charge Back");
define('TABLE_HEADING_BOTTOM_GST', "Include GST %s&percnt;");
define('TABLE_HEADING_BOTTOM_TOTAL_AFTER_TAX', "Total After Tax");
define('TABLE_HEADING_BOTTOM_CREDIT_NOTE', "Credit Note");
define('TABLE_HEADING_BOTTOM_TOTAL_AFTER_REBATE', "Total After Rebate");
define('TABLE_HEADING_BOTTOM_TOTAL', "Total");
define('TABLE_HEADING_BOTTOM_BANK_CHARGES', "Include Overseas Bank Charges");
define('TABLE_HEADING_BOTTOM_ADJUSTMENT', "Include Adjustment");
define('TABLE_HEADING_BOTTOM_TOTAL_API_SELECT', 'Total API Amount');
define('TABLE_HEADING_BOTTOM_TOTAL_PAYABLE_AMOUNT', "Total Payable Amount");
define('TABLE_HEADING_BOTTOM_AFTER_CONVERSION_RATE', "After Conversion Rate");

// heading for API items in Pending status
define('TABLE_HEADING_API_PENDING', "API Payment In Pending Processing");
define('TABLE_HEADING_PRODUCT_AVAIL_STOCK', 'Available Stock');
define('TABLE_HEADING_PRODUCT_ACTUAL_STOCK', 'Actual Stock');
define('TABLE_HEADING_REQUEST_QTY', 'Request Qty');
define('TABLE_HEADING_RECEIVED_QTY', 'RECV');
define('TABLE_HEADING_PENDING_RECEIVE_QTY', 'Pending RECV Qty');

// heading for API Request Statistic table
define('STAT_TABLE_HEADING_API_STATUS', '&nbsp;');
define('STAT_TABLE_HEADING_TOTAL_API', 'Total #');
define('STAT_TABLE_HEADING_TOTAL_AMOUNT', 'Total %s');
define('STAT_TABLE_ENTRY_API_1_DAY', 'API Request in 1 day');
define('STAT_TABLE_ENTRY_TOTAL_API', 'Total API Request');

// heading for API PO Status History table
define('STATUS_TABLE_HEADING_DATE_ADDED', 'Date Added');
define('STATUS_TABLE_HEADING_NOTIFY_SUPPLIER', 'Show to Supplier');
define('STATUS_TABLE_HEADING_STATUS', 'Status');
define('STATUS_TABLE_HEADING_COMMENTS', 'Comments');
define('STATUS_TABLE_HEADING_MODIFIED_BY', 'Changed By');
define('STATUS_TABLE_HEADING_ACTION', 'Action');

// label for API Listing search criteria
define('ENTRY_HEADING_API_LIST_START_DATE', 'Start Date (YYYY-MM-DD /YYYY-MM-DD HH:MM)');
define('ENTRY_HEADING_API_LIST_END_DATE', 'End Date (YYYY-MM-DD /YYYY-MM-DD HH:MM)');
define('ENTRY_HEADING_API_LIST_API_NO', 'API Payment Request Number (Formatted Version)');
define('ENTRY_HEADING_API_LIST_API_STATUS', 'API Payment Request Status');
define('ENTRY_HEADING_API_LIST_PAYMENT_STATUS', 'API Payment Transaction Status');
define('ENTRY_HEADING_RECORDS_PER_PAGE', 'Records per page');

// heading for API List show API details table
define('TABLE_HEADING_API_LIST_API_NO', "API PO No.");
define('TABLE_HEADING_API_LIST_TERM', "Term");
define('TABLE_HEADING_API_LIST_DELIVERY_STATUS', "Delivery Status");
define('TABLE_HEADING_API_LIST_TAG', "Tag");
define('TABLE_HEADING_API_LIST_SUPPLIER', "Suplier");
define('TABLE_HEADING_API_LIST_PAYMENT_STATUS', "Payment Status");
define('TABLE_HEADING_API_LIST_DATE', "Date");
define('TABLE_HEADING_API_LIST_TOTAL_PURCHASED', "Total Purchased");
define('TABLE_HEADING_API_LIST_TOTAL_UPLOADED', "Total Uploaded");
define('TABLE_HEADING_PO_LIST_BILLING_STATUS', "Billing Status");
define('TABLE_HEADING_API_LIST_VERIFIED', "Verified?");
define('TABLE_HEADING_API_LIST_LOCKED', "Locked");
define('TABLE_HEADING_API_LIST_ACTION', "Action");

// table heading for API List show API details
define('TABLE_HEADING_PRODUCTS', "Product Name");
define('TABLE_HEADING_STOCK_QUANTITY', "Actual Stock");
define('TABLE_HEADING_QUANTITY', "Ordered Qty");
define('TABLE_HEADING_PREV_RECEIVED', "Received Qty");
define('TABLE_HEADING_BALANCE', "Pending Delivery");
define('TABLE_HEADING_PRICE', "Unit Price");
define('TABLE_HEADING_TOTAL_EXCLUDING_TAX', "Amount (ex)");
define('TABLE_HEADING_TOTAL_INCLUDING_TAX', "Total (inc)");

// API report tabs
define('TAB_DEFAULT_TEXT', "Please select supplier!");

// heading for product last purchase cost history
define('TABLE_HEADING_COT_PRICE_PO_NO', "PO No.");
define('TABLE_HEADING_COT_PRICE_PO_DATE', "Date");
define('TABLE_HEADING_COT_PRICE_SUPPLIER', "Previous Supplier");
define('TABLE_HEADING_COT_PRICE_UNIT_COST', "Unit Cost Price");
define('TABLE_HEADING_COT_PRICE_PO_STATUS', "Status");
define('TABLE_HEADING_COT_PRICE_PO_ISSUE_BY', "Issue By");

// heading for printable API Request
define('TABLE_HEADING_API_PRINT_NO', "No.");
define('TABLE_HEADING_API_PRINT_PRODUCT_ID', "Product ID");
define('TABLE_HEADING_API_PRINT_PARTICULARS', "Particulars");
define('TABLE_HEADING_API_PRINT_QUANTITY', "Quantity");
define('TABLE_HEADING_API_PRINT_UNIT_PRICE', "Unit Price");
define('TABLE_HEADING_API_PRINT_DISCOUNT', "Discount");
define('TABLE_HEADING_API_PRINT_TOTAL', "Total");

// label text
define('TEXT_SUPPLIER_CREDIT_NOTE_UTILISE', 'Include credit note');
define('TEXT_SUPPLIER_CREDIT_NOTE_UTILISED', 'Utilised');
define('TEXT_SUPPLIER_CREDIT_NOTE_NOT_UTILISED', 'Not Utilising');
define('TEXT_SUPPLIER_PRE_PAYMENT', 'Pre-Payment');
define('TEXT_SUPPLIER_API_PAYMENT', 'API Payment');
define('TEXT_SUPPLIER_DAY_TERM', 'day-Term');
define('TEXT_SUPPLIER_CONSIGNMENT', 'Consignment');
define('TEXT_PAYMENT_BILLED', 'Billed');
define('TEXT_PAYMENT_PENDING_BILLING', 'Pending Billing');
define('TEXT_PAYMENT_PENDING_WITHDRAW', 'Pending Withdraw');
define('TEXT_PAYMENT_FULLY_WITHDRAW', 'Fully Withdraw');
define('TEXT_PAYMENT_PARTIAL_WITHDRAW', 'Partial Withdraw');
define('TEXT_PAYMENT_PAID', 'Paid');
define('TEXT_PAYMENT_PENDING_PAYMENT', 'Pending Payment');
define('TEXT_PAYMENT_NO_PAYMENT', 'No Payment Required');
define('TEXT_API_ANY', '<i>Any</i>');
define('TEXT_NOTIFY_SUPPLIER', 'Show to Supplier');
define('TEXT_SET_AS_REMARK', 'Set as Important Remark');
define('TEXT_NO_PO_HISTORY', 'No Purchase Order History Available');
define('TEXT_PRICE_UP', "Cost Price Higher Than Previous Purchase");
define('TEXT_PRICE_EQUAL', "Cost Price Equal To Previous Purchase");
define('TEXT_PRICE_DOWN', "Cost Price Lower Than Previous Purchase");
define('TEXT_NO_PRICE', "No Previous Purchase Price");
define('TEXT_REMARK_MODIFIED_BY', 'Last changed by ');
define('TEXT_VERIFIED', ' <b>[Verified]</b>');
define('TEXT_UNVERIFIED', ' <b>[Unverified]</b>');
define('TEXT_DELIVERED_AMOUNT', 'Delivered Amount');
define('TEXT_REMAINING_AMOUNT', 'Remaining Amount');
define('TEXT_REFUNDED_BANKCHARGES', 'Refunded Bank Charges');
define('TEXT_PRODUCTS_ID', 'Product ID:');
define('TEXT_API_SUPPLIER_CONSIGNMENT', 'API Consignment');
define('TEXT_API_SUPPLIER_PRE_PAYMENT', 'API Pre-payment');

// button text
define('IMAGE_BUTTON_ADD_ITEM', 'Add Item');
define('IMAGE_BUTTON_RESET', 'Reset');
define('BUTTON_SEARCH_API', 'Search');
define('BUTTON_SEARCH_API_RESET','Search');
define('BUTTON_EXPORT_API_CSV', 'Export');
define('BUTTON_STATUS_API', 'Preview API');
define('BUTTON_CALCULATE_API', 'Calculate API');
define('BUTTON_PREVIEW_API', 'Preview API');
define('BUTTON_GENERATE_API', 'Generate PO');
define('BUTTON_SET_CB_API', 'Generate CB');
define('BUTTON_ADD_REMARK', 'Add Remark');
define('BUTTON_COMPLETE', 'Manual Complete');
define('BUTTON_ROLLBACK_TO_PROCESS', 'Rollback to Process');
define('BUTTON_CANCEL_PENDING_RECEIVE', 'Cancel API Request');
define('BUTTON_VERIFY', 'Mark as Verified');
define('BUTTON_UNVERIFY', 'Mark as Unverified');
define('BUTTON_MAKE_PAYMENT', 'Credit to WSC');
define('BUTTON_DEBIT_PAYMENT', 'Debit from WSC');
define('BUTTON_LOCK', 'Lock');
define('BUTTON_UNLOCK', 'Unlock');
define('ALT_BUTTON_SEARCH_API', 'Search');
define('ALT_BUTTON_SEARCH_API_RESET', 'Search');
define('ALT_BUTTON_EXPORT_API_CSV', 'Export');
define('ALT_BUTTON_STATUS_API', 'Preview API');
define('ALT_BUTTON_CALCULATE_API', 'Calculate API');
define('ALT_BUTTON_PREVIEW_API', ' Preview API ');
define('ALT_BUTTON_GENERATE_API', ' Generate PO ');
define('ALT_BUTTON_SET_CB_API', ' Generate CB ');
define('ALT_BUTTON_ADD_REMARK', ' Add Remark ');
define('ALT_BUTTON_COMPLETE', ' Manual Complete ');
define('ALT_ROLLBACK_TO_PROCESS', ' Rollback to Process ');
define('ALT_BUTTON_CANCEL_PENDING_RECEIVE', ' Cancel Pending Receive ');
define('ALT_BUTTON_VERIFY', ' Mark as Verified ');
define('ALT_BUTTON_UNVERIFY', ' Mark as Unverified ');
define('ALT_BUTTON_MAKE_PAYMENT', ' Credit to WSC ');
define('ALT_BUTTON_DEBIT_PAYMENT', ' Debit from WSC ');
define('ALT_BUTTON_LOCK', 'Lock');
define('ALT_BUTTON_UNLOCK', 'Unlock');

// Status Remark
define('HEADER_ADD_REMARK', 'Add Remark');
define('TEXT_SET_IMPORTANT_REMARK', 'Set as Important Remark');

// text for PO locking in various stage
define('TEXT_ORDER_NOT_BEEN_LOCKED', '<span class="redIndicator">This Purchase Order has not been locked. Product Location information is hidden to prevent double-delivery of Products. Click the "LOCK" button on your right if you would like to edit this Purchase Order.</span>');
define('TEXT_LOCKED_ORDER_SEEN_BY_OWNER', 'This Purchase Order has been locked by you on %s at %s. If you are not going to edit this Purchase Order or if you have finished your Partial Delivery, please remember to click "UNLOCK" button on your right so that others may lock it.');
define('TEXT_ORDER_LOCKED_BY_OTHER', '<span class="redIndicator">This Purchase Order has been locked by %s on %s at %s. Product Location information is hidden to prevent double delivery of Products. You cannot edit this Purchase Order unless you lock the Order. To do so, you must first contact this person or any of the person in the following Admin Goups to unlock the Order. %s</span>');
define('TEXT_UNLOCK_OTHER_PEOPLE_ORDER', '<span class="redIndicator">This Purchase Order has been locked by %s on %s at %s. Product Location information is hidden to prevent double-delivery of Products. You have the Permission to unlock it by clicking the "UNLOCK" button on your right. Please note that unlocking this Order may result in double-delivery of Products.</span>');
define('TEXT_LOCKED_ORDER_NOT_VALID_STATUS', '<span class="redIndicator">This order cannot be locked since the order status is not in "Processing".</span>');
define('TEXT_LOCKED_OUTDATED_ORDER', '<span class="redIndicator">Someone has changed this order before you manage to lock it.</span>');
define('TEXT_DUPLICATE_LOCKED_ORDER_SEEN_BY_OWNER', '<span class="redIndicator">This order has been opened and locked by you on another browser window. This window will close after you click OK.</span>');

define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');
define('ORDERS_LOG_LOCK_ORDER', '##l_1##');

// success or error messages
define('SUCCESS_PO_FORM_ADDED', "Success: Purchase Order, %s, has been successfully added.");
define('SUCCESS_CB_FORM_ADDED', "Success: API status successfully changed.");
define('ERROR_PO_LOAD_EMPTY_ID', "Error: Can't retrieve purchase order with empty ID.");
define('ERROR_PO_SEARCH_INVALID_ID', "Error: Can't find purchase order with PO Number = %s.");
define('ERROR_API_FORM_LOCKED_SUPPLIER', 'Supplier/Publisher is Locked by other user');
define('ERROR_API_FORM_NOT_LOCKED_SUPPLIER', 'Please lock Supplier/Publishers');
define('ERROR_API_FORM_EMPTY_API_START_DATE', "Error: Please select start date for this API Payment Request.");
define('ERROR_API_FORM_EMPTY_API_END_DATE', "Error: Please select end date for this API Payment Request.");
define('ERROR_API_FORM_EMPTY_DELIVERY_ADDRESS', "Error: Please select a delivery address for this API Payment Request.");
define('ERROR_API_FORM_EMPTY_REMARKS', "Error: Please provide remarks.");
define('ERROR_API_FORM_EMPTY_SUPPLIER', "Error: Please select a supplier/publisher for this API withdrawal request.");
define('ERROR_API_FORM_SUPPLIER_TYPE', "Error: Please select a supplier/publisher which are using API Payment Term.");
define('ERROR_API_FORM_EMPTY_PAYMENT_METHOD', "Error: Please select a payment method for this API Payment Request.");
define('ERROR_API_FORM_EMPTY_CURRENCY', "Error: Please select a currency for this API withdrawal request.");
define('ERROR_API_FORM_EMPTY_CURRENCY_CONFIRM_RATE', "Error: Please enter a confirm currency rate for this API Payment Request.");
define('ERROR_API_FORM_EMPTY_PRODUCTS', "Error: Please select APIs for this API Payment Request.");
define('ERROR_API_CB_FORM_EMPTY_PRODUCTS', "Error: Please select APIs for this API CB Form.");
define('ERROR_API_FORM_EMPTY_SUGGEST_QUANTITY', "Error: invalid quantity for %s.");
define('ERROR_API_FORM_EMPTY_STOCK_PRICE', "Error: Please enter stock price for %s.");
define('ERROR_API_FORM_EMPTY_SELECT_STATUS', "Error: Please select status for Top-up ID %s.");
define('ERROR_API_FORM_PUBLISHER_NOT_MATCH', "Error: Supplier/publisher is not match with API List items");

define('ERROR_API_FORM_CREATE_PERMISSION', "Error: You are not allow to create new API Payment Request.");
define('ERROR_PO_FORM_PO_APPROVE_PERMISSION', "Error: You are not allow to approve/cancel the Purchase Order.");
define('ERROR_API_FORM_ADD_REMARK_PERMISSION', "Error: You are not allow to add remark for this API Payment Request.");
define('ERROR_API_FORM_API_CANCEL_PENDING_RECEIVE_PERMISSION', "Error: You are not allow to cancel for API Payment Request.");
define('ERROR_PO_FORM_VERIFY_PERMISSION', "Error: You are not allow to set verify/unverify for Purchase Order.");
define('ERROR_API_FORM_API_MAKE_PAYMENT_PERMISSION', "Error: You are not allow to make payment for this API Payment Request.");

define('ERROR_API_CANCELLATION_FAILED', "Failed to Cancel the API Payment Request! Missing API Request ID.");
define('ERROR_API_APPROVE_FAILED', "Failed to Approve the API Payment Request! Missing API Request ID.");
define('ERROR_API_COMPLETE_FAILED', "Failed to Complete the purchase order! Missing Purchase Order ID.");
define('ERROR_PO_COMPLETE_FAILED_PENDING_RECEIVE', "Failed to Complete the purchase order! There are items still having pending receive quantity.");
define('ERROR_API_ROLLBACK_COMPLETE_FAILED', "Failed to rollback Completed API Payment Request! Missing API Request ID.");
define('ERROR_API_STATUS_CHANGE_FAILED', "Failed to change API Payment Request status from %s to %s! Status Change Permission denied.");
define('ERROR_PO_VERIFY_FAILED', "Failed to Verify the purchase order! Missing Purchase Order ID.");
define('ERROR_API_ADD_REMARK_FAILED', "Failed to add remark for the API Payment Request! Missing API Payment Request ID.");

define('SUCCESS_CREDIT_WSC', "Success: WSC has been credited.");
define('SUCCESS_DEBIT_WSC', "Success: WSC has been debited.");
define('ERROR_API_CREDIT_WSC_PAYMENT_TYPE_FAILED', "Failed to credit the WSC! Only allow for Pre-payment type purchase order.");
define('ERROR_PO_DEBIT_WSC_PAYMENT_TYPE_FAILED', "Failed to debit the WSC! Only allow for Pre-payment type purchase order.");
define('ERROR_PO_DEBIT_WSC_PAYMENT_FOUND_FAILED', "Failed to debit the WSC! Payment has been made.");
define('ERROR_PO_DEBIT_WSC_FUND_FAILED', "Failed to debit the WSC! Insufficient WSC fund.");

define('ERROR_API_CANCEL_PENDING_RECEIVE_FAILED', "Failed to cancel for the API Payment Request! Missing API Request ID.");
define('ERROR_API_CANCEL_PENDING_RECEIVE_PAID_STATUS_FAILED', "Failed to cancel for the API Payment Request!");
define('ERROR_PO_CANCEL_PENDING_RECEIVE_PREPAYMENT_STATUS_FAILED', "Failed to cancel pending stock receive for the purchase order! Pre-payment Purchase Order is not allow to perform cancellation. Please Credit WSC if it is haven't done so.");

define('WARNING_TEXT_API_FORM_API_PARTIAL_PAID', "Please make this Purchase Order fully paid before can refund it by cash.");
?>