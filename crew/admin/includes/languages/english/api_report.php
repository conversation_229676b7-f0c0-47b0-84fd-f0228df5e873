<?
/*
  	$Id: api_report.php,v 1.2 2011/07/19 07:55:33 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'API Report');

/*********************************************
	Input section
*********************************************/
define('ENTRY_API_REPORT_PUBLISHER', 'Publisher');
define('ENTRY_API_REPORT_MONTH', 'Month');
define('ENTRY_API_REPORT_REPORT_TYPE', 'Report Type');
define('ENTRY_API_REPORT_STATUS', 'Status');
define('TABLE_HEADING_PROCESS', 'Process');

define('TEXT_INFO_API_REPORT_SESSOPN_RESET', 'Session Reset');
define('TEXT_INFO_API_REPORT_TOPUP_REPORT_IN_QUEUE', 'Top-up repost in queue');

define('WARNING_API_REPORT_INVALID_REPORT_TYPE', 'Invalid Report Type');
define('WARNING_API_REPORT_NO_RECORD', 'No record');
define('WARNING_API_REPORT_INVALID_PUBLISHER', 'Invalid publisher');
define('WARNING_API_REPORT_INVALID_REPORT_MONTH', 'Invalid Month');
define('WARNING_API_REPORT_NO_PERMISSION_ALLOWED', 'Permission not allowed');

define('WARNING_API_REPORT_DTU_ID_NOT_FOUND', 'Top-up ID not found');
define('WARNING_API_REPORT_NO_PERMISSION_TO_REPOST', 'Repost permission not allowed');
define('WARNING_API_REPORT_NOT_ALLOWED_TO_REPORT', 'Not allow to repost');

define('TABLE_HEADING_TOP_UP_ID', 'Top-up ID');
define('TABLE_HEADING_ORDER_ID', 'Order ID');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_TOP_UP_DATE', 'Top-up Date');
define('TABLE_HEADING_DETAILS', 'Details');
define('TABLE_HEADING_ACTION', 'Action');
?>