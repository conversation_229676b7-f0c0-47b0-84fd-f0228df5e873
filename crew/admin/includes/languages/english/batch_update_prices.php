<?
/*
  	$Id: batch_update_prices.php,v 1.5 2006/07/19 07:54:24 weichen Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Batch Update Prices');

define('TABLE_HEADING_CATEGORY_ID', 'Category ID');
define('TABLE_HEADING_CATEGORY', 'Category');
define('TABLE_HEADING_AVAILABLE_QTY', 'Available Qty');
define('TABLE_HEADING_ACTUAL_QTY', 'Actual Qty');
define('TABLE_HEADING_PRICE_SET', 'Price Set');

define('ENTRY_PRODUCT_NAME', 'Product Name');
define('ENTRY_CATEGORY', 'Categories');
define('ENTRY_PACKAGE_QUANTITY', "Package Quantity");

define('TEXT_MATCHED_PRODUCT_NAME', 'Matched Product Name: ');
define('TEXT_MATCH_PRODUCT_CRITERIA', '(Package\'s price is updatable if it contains only this subproduct)');
define('TEXT_MATCHED_CATEGORY_NAME', 'Under Category: ');
define('TEXT_REFRESH_PRICE_SET', '<span class="redIndicator">Note:</span> Please remember to click "Refresh Price Set" link or select "Refresh price set ..." option from any dropdown selection box after you add new price set or delete any existing price set. However, editing any price set does not required to refresh the price set.');

define('TEXT_MULTI_QTY_ENTRIES', "(Use ';' as delimiter for multiple quantity)");
define('TEXT_OPTION_NOT_APPLICABLE', 'n/a');

define('ERROR_UPDATE_ACCESS_DENIED_CAT', 'Error: You are not allowed to update category with id %s.');
?>