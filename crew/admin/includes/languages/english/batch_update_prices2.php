<?
/*
  	$Id: batch_update_prices2.php,v 1.1 2006/08/18 11:06:54 nickyap Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Batch Update Prices');

define('TABLE_HEADING_CATEGORY_ID', 'Category ID');
define('TABLE_HEADING_CATEGORY', 'Category');
define('TABLE_HEADING_AVAILABLE_QTY', 'Available Qty');
define('TABLE_HEADING_ACTUAL_QTY', 'Actual Qty');
define('TABLE_HEADING_PRICE_SET', 'Price Set');
define('TABLE_HEADING_PRODUCTS_PRICE', 'Unit Price');
define('TABLE_HEADING_BATCH_FILL', 'Batch Fill');
define('TABLE_HEADING_INCREMENT_VALUE', 'Increment Value');

define('ENTRY_PRODUCT_NAME', 'Product Name');
define('ENTRY_CATEGORY', 'Categories');
define('ENTRY_PACKAGE_QUANTITY', "Package Quantity");

define('TEXT_MATCHED_PRODUCT_NAME', 'Matched Product Name: ');
define('TEXT_MATCH_PRODUCT_CRITERIA', '(Package\'s price is updatable if it contains only this subproduct)');
define('TEXT_MATCHED_CATEGORY_NAME', 'Under Category: ');
define('TEXT_REFRESH_PRICE_SET', '<span class="redIndicator">Note:</span> Please remember to click "Refresh Price Set" link or select "Refresh price set ..." option from any dropdown selection box after you add new price set or delete any existing price set. However, editing any price set does not required to refresh the price set.');
define('TEXT_INCREMENT_VALUE', '<i>i.e. 0.0050 or 0.5%</i>');
define('TEXT_ALL_PACKAGE_QUANTITIES', 'All Packages');
define('TEXT_SHOW_BATCH_FILL', 'Show Batch Fill');
define('TEXT_HIDE_BATCH_FILL', 'Hide Batch Fill');
define('TEXT_APPLY_TO_SELECTED', 'Apply to selected');
define('TEXT_PRICE_NOT_AVAILABLE', 'Not Available');

define('TEXT_MULTI_QTY_ENTRIES', "(Use ';' as delimiter for multiple quantity)");

define('ERROR_UPDATE_ACCESS_DENIED_CAT', 'Error: You are not allowed to update category with id %s.');
?>