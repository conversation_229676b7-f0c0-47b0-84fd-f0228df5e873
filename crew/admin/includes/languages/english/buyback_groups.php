<?
/*
  	$Id: buyback_groups.php,v 1.13 2007/02/05 08:26:42 nickyap Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

if ($_REQUEST['action']=='new_buyback_group') {
	define('HEADING_TITLE', 'New Buyback Group');
} else {
	define('HEADING_TITLE', 'Buyback Groups');
}

define('TABLE_HEADING_BUYBACK_GROUPS_NAME', 'Buyback Group Name');
define('TABLE_HEADING_BUYBACK_GROUPS_DESCRIPTION', 'Description');
define('TABLE_HEADING_BUYBACK_SORT_ORDER', 'Sort Order');
define('TABLE_HEADING_BRACKETS', 'Brackets');
define('TABLE_HEADING_SALES_START_DATE', 'Sales Start Date');
define('TABLE_HEADING_SALES_END_DATE', 'Sales End Date');
define('TABLE_HEADING_DAYS_INVENTORY', 'No. Days Inventory');
define('TABLE_HEADING_PERCENTAGE_OF_INV_SPACE', 'Percentage of Quantity');
define('TABLE_HEADING_SALES_RETRIEVAL_METHOD', 'Get Average Sales');
define('TABLE_HEADING_SALES_RETRIEVE_BY_DATE_RANGE', 'By Date Range');
define('TABLE_HEADING_SALES_RETRIEVE_BY_LAST', 'By Last');

define('TABLE_HEADING_PRODUCT_TAGS_FIELD', 'Matching Field');
define('TABLE_HEADING_BRACKET_VALUE', 'Value');
define('TABLE_HEADING_BRACKET_QTY', 'Quantity');
define('TABLE_HEADING_BRACKET_ACTION', 'Action');

define('ENTRY_MIN_BUYBACK_QTY', 'Minimum Quantity: ');
define('ENTRY_PRODUCT_NAME', 'Product Name:');
define('ENTRY_TAG_ORDER', 'Sort Order: ');
define('ENTRY_PRODUCT_TAGS_FIELD', 'Matching Field: ');

define('ENTRY_BUYBACK_GROUP_NAME', 'Group Name: ');
define('ENTRY_BUYBACK_GROUP_DESCRIPTION', 'Group Description: ');
define('ENTRY_BUYBACK_GROUP_SORT_ORDER', 'Sort Order: ');
define('ENTRY_PRODUCT_MIN_QTY', 'Minimum Buyback Quantity: ');
define('ENTRY_PRODUCT_MAX_QTY', 'Maximum Buyback Quantity: ');
define('ENTRY_PRODUCT_OVERWRITE_MAX_QTY', 'Overwrite Maximum<br/>Buyback Quantity: ');
define('ENTRY_PRODUCT_MAX_QTY_SETTINGS', 'Max. Buyback Quantity Settings ');
define('ENTRY_NO_CHANGE', 'No Change');
define('ENTRY_OVERWRITE_WITH', 'Overwrite with : ');

define('ENTRY_BUYBACK_BRACKET_QUANTITY', 'Quantity: ');
define('ENTRY_BUYBACK_BRACKET_VALUE', 'Value: ');
define('ENTRY_BUYBACK_BRACKET_TYPE', 'Type: ');

define('TEXT_TOTAL_LINKED_CATEGORIES', '(Total categories: %d)');

define('TEXT_BUYBACK_GROUP_DISABLED', '<font color="red"><b>Group Disabled</b></font>');
define('TEXT_ENABLE_GROUP', 'Enable');
define('TEXT_DISABLE_GROUP', 'Disable Group');

define('TEXT_MESSAGE_BUYBACK_GROUP_SETTING_NOT_FOUND', 'Incomplete configuration detected for this buyback group.');
define('TEXT_MESSAGE_BUYBACK_CATEGORY_NOT_FOUND', 'No categories selected.');
define('TEXT_MESSAGE_USING_SUGGESTED_MAX', '<i>( Using Suggested Max Qty )</i>');
define('TEXT_MESSAGE_USE_SUGGESTED_MAX', 'Suggest based on Buyback Group\'s Max Qty. Settings');
define('MESSAGE_MAX_NOT_GREATER_THAN_MIN', 'Maximum quantity is not greater than minimum quantity.');

define('MESSAGE_INVALID_BRACKET_COUNT', 'Invalid Bracket count.');
define('MESSAGE_INVALID_START_END_DATE', '[%s] Invalid Sales Date range.');
define('MESSAGE_INVALID_START_END_DATE_SINGLE', 'Invalid Sales Date range.');
define('MESSAGE_INVALID_NUM_DAYS_SALES', 'Invalid Number of days sales.');
define('MESSAGE_ERROR_SO_ABORT_GENERAL', 'Errors found. Update aborted.');
define('MESSAGE_ERROR_SO_ABORT', '[%s] Errors found. Update aborted.');

define('TEXT_MIN_MAX', 'Min/Max: ');
define('TEXT_MIN_BUYBACK_QUANTITY', 'Minimum Buyback');
define('TEXT_MAX_BUYBACK_QUANTITY', 'Maximum Buyback');
define('TEXT_DAYS', 'days');
define('TEXT_OVERWRITE', 'Overwrite');
define('TEXT_SYSTEM_DEFINE', 'System Defined');
define('TEXT_MAX_INV_SPACE', 'Maximum Inventory Space');
define('TEXT_USE_GREATER_BETWEEN', 'Using the greater between the following;');

define('LINK_ADD_BUYBACK_GROUP', 'Add Buyback Group');
define('LINK_EDIT_PRODUCT', 'Edit');
define('LINK_DELETE_PRODUCT', 'Delete');
define('LINK_NEW_BRACKET', 'New Bracket');

define('LINK_NEW_PRODUCT', 'New Product');
?>