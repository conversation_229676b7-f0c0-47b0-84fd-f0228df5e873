<?
/*
  	$Id: buyback_products.php,v 1.8 2006/02/23 10:53:59 weichen Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Buyback Products');
define('HEADING_TITLE_SELECT', 'Select:');

define('TABLE_HEADING_BRACKET', 'Bracket(%s)');
define('TABLE_HEADING_BASE', 'Base');

define('TEXT_PERCENTAGE', '%');
define('TEXT_MONEY', '$');
define('TEXT_CURRENT_TIME', 'Server Time');
define('TEXT_QUANTITY', 'Quantity: ');
define('TEXT_PRICE', 'Price: ');

define('TEXT_HEADING_PRODUCT', 'Product Name');
define('TEXT_HEADING_STATUS', 'Status');
define('TEXT_HEADING_MIN_QTY', 'Minimum Quantity');
define('TEXT_HEADING_MAX_QTY', 'Maximum Quantity');
define('TEXT_HEADING_UNIT_PRICE', 'Unit Price');
define('TEXT_HEADING_DISABLED', 'Disabled');
define('TEXT_HEADING_COMMENT', 'Comments');

define('HEADING_TITLE_SUPPLIER_PRICING', 'Supplier Pricing');
define('TEXT_HIDE_BATCH_FILL', 'Hide Batch Fill');
define('TEXT_SHOW_BATCH_FILL', 'Show Batch Fill');

define('TEXT_HIDE_DISABLED', 'Hide Disabled');
define('TEXT_SHOW_DISABLED', 'Show Disabled');

define('TEXT_BATCH_APPLY', 'Batch Apply');
define('TEXT_APPLY_TO_SELECTED', 'Apply to Selected');
define('TEXT_HEADING_ACTION', '');
define('TEXT_ACTION', 'Action');

define('BUYBACK_SUPPLIER_ON_OFF_STATUS', 'Supplier Page Status');
define('TEXT_CURRENT_STATUS', 'Current Status');
define('TEXT_AUTO_ON_PERIOD', 'Start Time');
define('TEXT_AUTO_OFF_PERIOD', 'End Time');
define('TEXT_TO', ' to ');
define('TEXT_ON', 'On');
define('TEXT_OFF', 'Off');
define('TEXT_AUTO', 'Auto');
define('TEXT_ENABLED', 'Enabled');
define('TEXT_DISABLED', 'Disabled');
define('TEXT_SERVER_FULL', 'Server Full');

define('TEXT_CURRENT_STATUS_ON','<font color="green">ON</font>');
define('TEXT_CURRENT_STATUS_OFF','<font color="red">OFF</font>');
define('TEXT_OPERATING_HOURS','Operating Hours: ');
define('TEXT_AUTO_ON','Auto turn ON if the module is OFF during operating hours');
define('TEXT_AUTO_OFF','Auto turn OFF if the module is ON outside operating hours');

define('SUCCESS_SUPPLIER_PRICING_UPDATE', 'Success: The supplier pricing has been successfully updated.');
?>