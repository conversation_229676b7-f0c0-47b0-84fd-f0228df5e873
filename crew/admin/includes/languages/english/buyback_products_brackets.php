<?
/*
  	$Id: buyback_products_brackets.php,v 1.8 2006/10/18 08:23:28 nickyap Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Buyback Products');
define('HEADING_TITLE_SELECT', 'Select:');



define('TABLE_HEADING_BRACKET', 'Bracket %s');
define('TABLE_HEADING_BRACKET_NEW', 'New Bracket');
define('TABLE_HEADING_SALES_RETRIEVAL_METHOD', 'Get Average Sales');
define('TABLE_HEADING_SALES_RETRIEVE_BY_DATE_RANGE', 'By Date Range');
define('TABLE_HEADING_SALES_RETRIEVE_BY_LAST', 'By Last');
define('TABLE_HEADING_SALES_START_DATE', 'Sales Start Date');
define('TABLE_HEADING_SALES_END_DATE', 'Sales End Date');
define('TABLE_HEADING_DAYS_INVENTORY', 'No. Days Inventory');
define('TABLE_HEADING_PERCENTAGE_OF_INV_SPACE', 'Percentage of Quantity');
define('TABLE_HEADING_PRODUCT_LEVELS', 'Product Levels');

define('TEXT_PERCENTAGE', '%');
define('TEXT_DAYS', 'days');
define('TEXT_MONEY', '$');
define('TEXT_MIN_BUYBACK_QUANTITY', 'Minimum Buyback');
define('TEXT_MAX_BUYBACK_QUANTITY', 'Maximum Buyback');
define('TEXT_MAX_BUYBACK_QUANTITY_CURRENT', 'Maximum Buyback ( Current )');
define('TEXT_MAX_INV_SPACE', 'Maximum Inventory Space');
define('TEXT_REMAINDER_INV_SPACE', 'Remainder Inventory Space');
define('TEXT_CURRENT', 'Current');
define('TEXT_SUGGESTED', 'Suggested');
define('TEXT_PREVIEW', 'Preview');
define('TEXT_OVERWRITE', 'Overwrite');
define('TEXT_QUANTITY', 'Qty.');
define('TEXT_SYSTEM_DEFINE', 'System Defined');
define('TEXT_AVAILABLE', 'Available');
define('TEXT_FORECAST_AVAILABLE', 'Forecast Available');
define('TEXT_ACTUAL', 'Actual');
define('TEXT_FORECAST_ACTUAL', 'Forecast Actual');
define('TEXT_ACTIVE_BRACKET_QTY', 'Active Bracket Qty');
define('TEXT_ACTIVE_BRACKET_PRICE', 'Active Bracket Unit Price');
define('TEXT_SERVER_FULL', 'Server Full');
define('TEXT_BUYBACK_OK', 'Accepting Buybacks');
define('TEXT_BACKORDER', 'Back-Order');
define('TEXT_NOT_BACKORDER', 'Not Back-Order');
define('TEXT_DAILY_CONFIRMED_PRODUCTION', 'Daily Confirmed Production');
define('TEXT_USE_GREATER_BETWEEN', 'Using the greater between the following;');

define('MESSAGE_INVALID_MAX_INV_SPACE', 'Invalid Maximum Inventory Space.');
define('MESSAGE_INVALID_BRACKET_COUNT', 'Invalid Bracket count.');
define('MESSAGE_INVALID_START_END_DATE', '[%s] Invalid Sales Date range.');
define('MESSAGE_MAX_NOT_GREATER_THAN_MIN', 'Maximum quantity %s is not greater than minimum quantity %s.');
define('MESSAGE_ERROR_SO_ABORT', '[%s] Errors found. Update aborted.');

define('TEXT_BRACKETS_FOR', 'Brackets for: ');
define('TEXT_QUANTITY', 'Buyback Level: ');
define('TEXT_PRICE', 'Price: ');
define('TEXT_DELETE', 'Delete');

?>
