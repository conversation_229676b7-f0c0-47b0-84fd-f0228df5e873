<?
/*
  	$Id: buyback_requests.php,v 1.30 2011/04/13 10:26:25 keepeng.foong Exp $

  	Copyright (c) 2002 osCommerce

  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Buyback Lists');
define('HEADING_INPUT_TITLE', 'Buyback Lists Criteria');
define('HEADING_TITLE_SEARCH', 'Order ID:');

define('TABLE_HEADING_CUSTOMER_NAME', 'Name');
define('TABLE_HEADING_CUSTOMER_EMAIL', 'Email');
define('TABLE_HEADING_DATE', 'Date');
define('TABLE_HEADING_AMOUNT', 'Amount');
define('TABLE_HEADING_PRODUCT_UNIT_PRICE', 'Price');
define('TABLE_HEADING_PRODUCT_ACTUAL_QTY', 'Actual Stock');
define('TABLE_HEADING_RSTK_CHARACTER', 'RSTK CHAR');
define('TABLE_HEADING_BUYBACK_ORDER_BILLING_STATUS', 'Billing Status');
define('TABLE_HEADING_BUYBACK_ORDER_VERIFY_STATUS', 'Verified?');
define('TABLE_HEADING_TOTAL', 'Total');
define('TABLE_HEADING_IP_ADDRESS', 'IP Address');
define('TABLE_HEADING_GAME_NAME', 'Game Name');
define('TABLE_HEADING_DELIVERY_OPTION', 'Delivery Option');
define('TABLE_HEADING_TAG', 'Tag');
define('TABLE_HEADING_CUSTOMER_TYPE', 'Type');
define('TABLE_HEADING_BUYBACK_FROM', 'Buyback From');
define('TABLE_HEADING_BUYBACK_ORDER_EXPIRY_TIME', 'Expiry In (mins)');
define('TABLE_HEADING_LOCKED_BY', 'Locked');
define('TABLE_HEADING_MINS_SINCE_PROCESSING', 'Mins Since Processing');

define('TYPE_CUSTOMER', 'Customer');
define('TYPE_SUPPLIER', 'Supplier');
define('TEXT_SUPPLIER_COMMENT', 'Supplier Comment');
define('TEXT_CUSTOMER_COMMENT', 'Customer Comment');
define('TEXT_SITE', 'Site:');

define('TABLE_HEADING_PRODUCT_QUANTITY_RECEIVED', 'Received Quantity');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_BUYBACK_REQUEST_NO', 'No.');
define('TABLE_HEADING_TRADE_MODE', 'Trade Mode');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_PRODUCT_QUANTITY', 'Request Quantity');
define('TABLE_HEADING_PRODUCT_PRICE', 'Amount');
define('TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED', 'Received');
define('TABLE_HEADING_PRODUCT_BALANCE', 'Balance');
define('TABLE_HEADING_PRODUCT_CONFIRMED_QUANTITY', 'Confirmed Quantity');
define('TABLE_HEADING_PRODUCT_RECEIVE', 'Receive');
define('TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT', 'Amount Payable');
define('TABLE_HEADING_PAYABLE_TOTAL', 'Total Payable');

define('ENTRY_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small>');
define('ENTRY_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD<br>/YYYY-MM-DD HH:MM)</small>');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_ORDER_STATUS', 'Order Status');
define('ENTRY_BUYBACK_ORDER_SUBMIT_FROM', 'Buyback From');
define('ENTRY_CATEGORY', 'Category');
define('ENTRY_CUSTOMER_EMAIL', 'Customer Email');
define('ENTRY_HEADING_PAGE_REFRESH', 'Refresh (minutes)');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');
define('ENTRY_HEADING_DELIVERY_OPTION', 'Deliver Option');
define('ENTRY_TRADE_WITH_OFFGAMERS', 'Trade With OffGamers (T2O)');
define('ENTRY_TRADE_WITH_CUSTOMERS', 'Trade With Customers (T2C)');

define('TEXT_ANY', '<i>Any</i>');

define('TEXT_ADMIN_COMMENT', 'Admin Comment:');
define('TEXT_SAVE', 'Save Comment');
define('TEXT_CUSTOMER_COMMENT', 'Customer Comment:');
define('TEXT_SUPPLIER_COMMENT', 'Supplier Comment:');
define('TEXT_LAST_CHANGED_BY', 'Last changed by ');
define('TEXT_INCLUDE_SUBCATEGORY', 'Include Sub Categories');
define('TEXT_NOTIFY', 'Notify Supplier');
define('TEXT_ACTION_SET_BUYBACK_REMARK', 'Set as Buyback Remark');
define('TEXT_TRADE_WITH_OFFGAMERS', 'T2O');
define('TEXT_TRADE_WITH_CUSTOMERS', 'T2C');
define('TEXT_TRADE_ON_MAIL', 'Mail');
define('TEXT_FACE_TO_FACE_TRADE', 'F2F');
define('TEXT_OPEN_STORE_TRADE', 'Open Store');
define('TEXT_NON_VIP', 'Normal');

define('TEXT_CHAR_DISCONNECTED', 'Our Receiving Character has been disconnected from the game due to unforeseen circumstances, please allow us 3minutes to resolve this matter and complete the transfer with you. Your patience is highly appreciated');
define('TEXT_CHAR_READY', 'Our Receiving Character is now ready. Please kindly invite our character into party, and confirm your buyback order no. before trade. Once the transfer is complete, remember to fill in the [Sent Qty] box.');
define('TEXT_PREPARE_NEW_CHAR', 'Please kindly allow us 3-10minutes while we prepare a new Receiving Character for you.');

define('TEXT_DISPLAY_NUMBER_OF_BUYBACK_REQUESTS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> buyback orders)');

define('ERROR_BUYBACK_ORDER_NOT_EXISTS', 'Error: Buyback order #%s does not exists.');
define('ERROR_SHOW_BUYBACK_RESTOCK_CHARACTER', 'Success: Show restock character for buyback order #%s is failed.');
define('ERROR_HIDE_BUYBACK_RESTOCK_CHARACTER', 'Success: Hide restock character for buyback order #%s is failed.');
define('ERROR_INAVALID_EMAIL_ADDRESS', 'Error: Invalid Customer Email Address.');
define('WARNING_RESTOCK_CHARACTER_ALREADY_SHOWN', 'Warning: Restock character for buyback order #%s already shown.');
define('WARNING_RESTOCK_CHARACTER_ALREADY_HIDE', 'Warning: Restock character for buyback order #%s already hide.');
define('WARNING_TEXT_NO_RESTK_CHAR_ASSIGN', '<span class="redIndicator">No Restock Character</span>');
define('SUCCESS_SHOW_BUYBACK_RESTOCK_CHARACTER', 'Success: Restock character for buyback order #%s has been successfully show.');
define('SUCCESS_HIDE_BUYBACK_RESTOCK_CHARACTER', 'Success: Restock character for buyback order #%s has been successfully hide.');
?>