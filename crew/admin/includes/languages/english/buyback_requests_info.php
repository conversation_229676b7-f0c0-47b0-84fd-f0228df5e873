<?
/*
  	$Id: buyback_requests_info.php,v 1.39 2010/12/07 09:13:38 weesiong Exp $

  	Copyright (c) 2002 osCommerce

  	Released under the GNU General Public License
*/

define('HEADING_TITLE_BUYBACK_ORDERS', 'Buyback');
define('HEADING_TITLE_SEARCH', 'Order ID:');

define('TABLE_HEADING_CUSTOMER_NAME', 'Customer Name');
define('TABLE_HEADING_CUSTOMER_EMAIL', 'Email');
define('TABLE_HEADING_DATE', 'Date');
define('TABLE_HEADING_AMOUNT', 'Amount');
define('TABLE_HEADING_PRODUCT_UNIT_PRICE', 'Unit Price');
define('TABLE_HEADING_PRODUCT_ACTUAL_QTY', 'Actual Stock');
define('TABLE_HEADING_TOTAL', 'Total');
define('TABLE_HEADING_IP_ADDRESS', 'IP Address');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_CUSTOMER_TYPE', 'Customer Type');
define('SUB_TABLE_HEADING_TOTAL_ORDER', 'Total #');
define('SUB_TABLE_HEADING_TOTAL_AMOUNT', 'Total ');
define('TYPE_CUSTOMER', 'Customer');
define('TYPE_SUPPLIER', 'Supplier');
define('TEXT_INFORMATION', 'Information');
define('TEXT_GENDER', 'Gender:');
define('TEXT_DOB', 'Date of Birth:');
define('TEXT_TELEPHONE', 'Office Phone:');
define('TEXT_FAX', 'Fax:');
define('TEXT_MOBILE', 'Mobile Phone:');
define('TEXT_EMAIL', 'Email:');
define('TEXT_IM', 'IM:');
define('TEXT_QQ', 'QQ Number:');
define('TEXT_MSN', 'MSN Address:');
define('TEXT_YAHOO', 'Yahoo Address:');
define('TEXT_ICQ', 'ICQ Address:');
define('TEXT_ORDERING_IP_ADD', 'Ordering IP:');
define('TEXT_SITE', 'Site:');
define('TEXT_ORDER_STATISTICS', 'Order Statistics:');

define('LINK_BUYBACK_HISTORY', 'Buyback History');

define('TEXT_BUYBACK_ORDER_NUMBER', 'Buyback Number:');
define('TEXT_ORDER_DATE_TIME', 'Buyback Date & Time:');
define('TEXT_ORDER_STATUS', 'Buyback Status:');
define('TEXT_ORDER_TRADE_MODE', 'Buyback Trade Mode:');
define('TEXT_DELIVERY_MODE', 'Customer Order Delivery Mode:');
define('TEXT_TRADE_WITH_OFFGAMERS', 'Trade With OffGamers');
define('TEXT_TRADE_WITH_CUSTOMERS', 'Trade With Customers');
define('TEXT_NORMAL_ORDERS', 'Normal');
define('TEXT_DELIVER_FOR_ORDER', 'Deliver for Customer Order:');
define('TEXT_SUPPLIER_CODE', 'Supplier Code:');
define('TEXT_CUSTOMER_CODE', 'Customer Code (provide by supplier):');
define('TEXT_SCREEN_SHOT', 'Screen Shot:');
define('TEXT_MATCH', 'Match');
define('TEXT_NOT_MATCH', 'Not Match');
define('TEXT_TOTAL', 'Total');
define('TEXT_STATUS', 'Status');
define('TEXT_RESTOCK_CHARACTER_SET', 'Restock Character Set');
define('TEXT_RESTOCK_CHARACTER_SET_ASSIGNED_TO', 'Restock Character Set assigned to <b> %s </b>');
define('TEXT_BUYBACK_ORDER_VERIFIED_STATUS', '[%s]');
define('TEXT_BUYBACK_ORDER_VERIFIED', 'Verified');
define('TEXT_BUYBACK_ORDER_NOT_VERIFIED', 'Unverify');
define('TEXT_AUTO_CANCELLATION', '<p><b>Auto-Cancellation in<br><span class="redIndicator">%s</span> mins.</b></p>');
define('TEXT_BUYBACK_LANG_CONFIRM_TRADE_PIN', '<font color="red">After Trading Code: %s</font>');

if (!defined('TEXT_ORDER_NOT_BEEN_LOCKED'))			define('TEXT_ORDER_NOT_BEEN_LOCKED', '<span class="redIndicator">This Order has not been locked. Click the "LOCK" button on your right if you would like to edit this Order.</span>');
if (!defined('TEXT_LOCKED_ORDER_SEEN_BY_OWNER'))	define('TEXT_LOCKED_ORDER_SEEN_BY_OWNER', 'This order has been locked by you on %s at %s. If you are not going to edit this Order, please remember to click "UNLOCK" button on your right so that others may lock it.');
if (!defined('TEXT_ORDER_LOCKED_BY_OTHER'))			define('TEXT_ORDER_LOCKED_BY_OTHER', '<span class="redIndicator">This order has been locked by %s on %s at %s. You cannot edit this Order unless you lock the Order. To do so, you must first contact this person or any of the person in the following Admin Goups to unlock the Order. %s</span>');
if (!defined('TEXT_UNLOCK_OTHER_PEOPLE_ORDER'))		define('TEXT_UNLOCK_OTHER_PEOPLE_ORDER', '<span class="redIndicator">This order has been locked by %s on %s at %s. You have the Permission to unlock it by clicking the "UNLOCK" button on your right.</span>');

// Locking / Unlocking log tag
if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))	define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');

define('TABLE_HEADING_PRODUCT_BUYBACK_STATUS', 'Status');
define('TABLE_HEADING_PRODUCT_ADMIN_COMMENT', 'Admin Comment');
define('TABLE_HEADING_PRODUCT_COMMENT', 'Comments');
define('TABLE_HEADING_BACK_ORDER', 'Back Orders');
define('TABLE_HEADING_BUYBACK_ORDER', 'Buyback Order');
define('TABLE_HEADING_NORMAL_ORDER', 'Current Orders');
define('TABLE_HEADING_PRODUCT_LOCATION', 'Product Location');
define('TABLE_HEADING_RSTK_CHARACTER', 'RSTK CHAR');
define('TABLE_HEADING_SENDER_CHARACTER', 'SENDER CHAR');
define('TABLE_HEADING_BUYBACK_REQUEST_NO', 'No.');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_PRODUCT_QUANTITY', 'Request Quantity');
define('TABLE_HEADING_PRODUCT_PRICE', 'Amount');
define('TABLE_HEADING_PRODUCT_CONFIRMED_QUANTITY', 'Confirmed Quantity');
define('TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED', '<span title="Received">RECV*</span>');
define('TABLE_HEADING_PRODUCT_BALANCE', '<span title="Balance">BAL</span>');
define('TABLE_HEADING_PRODUCT_RECEIVE', '<span title="Receive">RECV</span>');
define('TABLE_HEADING_EXTRA_QTY', '<span title="Extra quantity received for this product">EXTRA</span>');
define('TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT', 'Amount Payable');
define('TABLE_HEADING_PAYABLE_TOTAL', 'Payable Total');
define('TABLE_HEADING_DEVICE_IDENTIFICATION', 'Device Information');

define('TEXT_ADMIN_COMMENT', 'Admin Comment:');
define('TEXT_SAVE', 'Save Comment');
define('TEXT_CUSTOMER_COMMENT', 'Customer Comment:');
define('TEXT_SUPPLIER_COMMENT', 'Supplier Comment:');
define('TEXT_OPTION_NOT_APPLICABLE', 'n/a');

define('TEXT_DATE_ADDED', 'Date Added');
define('TEXT_CUSTOMER_NOTIFIED', 'Customer Notified');
define('TEXT_COMMENTS', 'Comments');
define('TEXT_CHANGED_BY', 'Changed By');
define('TEXT_ACTION', 'Action');
define('TEXT_ACTION_SET_BUYBACK_REMARK', 'Set as Important Remark');
define('TEXT_NOTIFY', 'Show This Message to ');
define('TEXT_MSG_SUCCESS', 'Success: Buyback has been successfully updated.');
define('TEXT_PRINTABLE_VERSION', 'Printable Version');
define('TEXT_BUYBACK_IN_SERVICE', 'Service In Process');
define('TEXT_HELP_PROVISION_CONFIRMED_QTY','Provision confirmed qty (110%% of %s)');
define('WARNING_RECEIVED_BY_OTHERS', 'Warning: Someone had update the received quantity for %s. Your received quantity is NOT UPDATED.');
define('WARNING_RESTOCK_NON_PENDING_ORDERS', 'Warning: Restock is not done. %s status is not "%s"!');
define('WARNING_ORDER_NOT_UPDATED', 'Warning: Nothing to change. The buyback order(%s) was not updated.');
define('SUCCESS_EXTRA_QUANTITY_UPDATED', 'Success: Extra %d of product quantities for Product with ID: %d has been successfully updated to stock.');

define('TEXT_CHAR_DISCONNECTED', 'Our Receiving Character has been disconnected from the game due to unforeseen circumstances, please allow us 3minutes to resolve this matter and complete the transfer with you. Your patience is highly appreciated');
define('TEXT_CHAR_READY', 'Our Receiving Character is now ready. Please kindly invite our character into party, and confirm your buyback order no. before trade. Once the transfer is complete, remember to fill in the [Sent Qty] box.');
define('TEXT_PREPARE_NEW_CHAR', 'Please kindly allow us 3-10minutes while we prepare a new Receiving Character for you.');

define('SELECT_OPTION_UPDATE_COMMENT', 'Update Comment');

define('BUTTON_BUYBACK_SERVE_CUSTOMER', 'Serve Customer');

define('ERROR_ORDER_NOT_LOCK', 'This order is not locked by you! Please lock it if you would like to edit this order.');
define('ERROR_BUYBACK_ORDER_NOT_EXISTS', 'Error: Buyback order #%s does not exists.');
define('ERROR_SHOW_BUYBACK_RESTOCK_CHARACTER', 'Success: Show restock character for buyback order #%s is failed.');
define('ERROR_HIDE_BUYBACK_RESTOCK_CHARACTER', 'Success: Hide restock character for buyback order #%s is failed.');
define('ERROR_INAVALID_EMAIL_ADDRESS', 'Error: Invalid Customer Email Address.');
define('ERROR_BUYBACK_QTY_OVER', 'Error: Buyback Order quantity over Customer Order pending delivery quantity. Please check all the Pending Buyback Order for this Customer Order: %s');
define('WARNING_RESTOCK_CHARACTER_ALREADY_SHOWN', 'Warning: Restock character for buyback order #%s already shown.');
define('WARNING_RESTOCK_CHARACTER_ALREADY_HIDE', 'Warning: Restock character for buyback order #%s already hide.');
define('WARNING_TEXT_NO_RESTK_CHAR_ASSIGN', '<span class="redIndicator">No Restock Character</span>');
define('SUCCESS_SHOW_BUYBACK_RESTOCK_CHARACTER', 'Success: Restock character for buyback order #%s has been successfully show.');
define('SUCCESS_HIDE_BUYBACK_RESTOCK_CHARACTER', 'Success: Restock character for buyback order #%s has been successfully hide.');
define('SUCCESS_RESTOCK_CHARACTER_UPDATE', 'Restock Character has been successfully updated.');
define('SUCCESS_GENESIS_PROJECT_TURNED_OFF', 'Genesis Project is turned off.');

if (!defined('ORDERS_LOG_LOCK_ORDER'))				define('ORDERS_LOG_LOCK_ORDER', '##l_1##');

define('TEXT_BUYBACK_ORDER_DELIVERED_REFERENCE_REMARK', 'Delivered via Buyback Order: ##BO##%d##');
define('TEXT_BUYBACK_ORDER_DEDUCTED_REFERENCE_REMARK', 'Deducted off via Buyback Order: ##BO##%d##');

define('TEXT_TRANSACTION_IDENTIFIER', 'Order');
define('TEXT_DEVICE_ID', 'Device ID');
define('TEXT_VERIFICATION_RESULT', 'Verification Result');
define('TEXT_SUCCESS', 'Success');
define('TEXT_FAILED', 'Failed');
define('TEXT_TOTAL_DEVICES', 'Total Devices Detected From This Account');
define('TEXT_BO_CO_DEVICE_ID_MATCH', 'BO Device Match With CO Device');
define('TEXT_TRUE_IP', 'IP');
define('TEXT_TRUE_IP_CITY', 'IP City');
define('TEXT_TRUE_IP_COUNTRY', 'IP Country');
define('TEXT_TRUE_IP_COUNTRY_CODE', 'IP Country Code');
define('TEXT_TRUE_IP_ISP', 'IP ISP');
define('TEXT_TRUE_IP_ORGANIZATION', 'IP Organization');

define('TEXT_YES', 'Yes');
define('TEXT_NO', 'No');
define('TEXT_PROXY_DETECTED', 'Proxy Detected');
define('TEXT_PROXY_TYPE', 'Proxy Type');
define('TEXT_TRUE_IP_ATTRIBUTES', 'IP Attributes');
define('TEXT_PROXY_IP_INFORMATION', 'Proxy IP Information');
define('TEXT_PROXY_IP_CITY', 'Proxy IP City');
define('TEXT_PROXY_IP_COUNTRY_CODE', 'Proxy IP Country Code');
define('TEXT_PROXY_IP_ISP', 'Proxy IP ISP');

?>