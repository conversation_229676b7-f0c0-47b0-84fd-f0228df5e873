<?

define('HEADING_TITLE_BUYBACK_ORDERS', 'Buyback');
define('TABLE_HEADING_CUSTOMER_NAME', 'Customer Name');
define('TABLE_HEADING_DATE', 'Date');
define('TABLE_HEADING_AMOUNT', 'Amount');
define('TABLE_HEADING_PRODUCT_UNIT_PRICE', 'Price');
define('TABLE_HEADING_PRODUCT_ACTUAL_QTY', 'Actual Stock');
define('TABLE_HEADING_TOTAL', 'Total');
define('TABLE_HEADING_CUSTOMER_TYPE', 'Customer Type');
define('TYPE_CUSTOMER', 'Customer');
define('TYPE_SUPPLIER', 'Supplier');


define('TEXT_BUYBACK_ORDER_NUMBER', 'Buyback Number:');
define('TEXT_ORDER_DATE_TIME', 'Buyback Date & Time:');
define('TEXT_ORDER_STATUS', 'Buyback Status:');
define('TEXT_STATUS', 'Status');


define('TABLE_HEADING_PRODUCT_LOCATION', 'Product Location');
define('TABLE_HEADING_BUYBACK_REQUEST_NO', 'No.');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_PRODUCT_QUANTITY', 'Request Quantity');
define('TABLE_HEADING_PRODUCT_PRICE', 'Amount');
define('TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED', 'Received');
define('TABLE_HEADING_PRODUCT_BALANCE', 'Balance');
define('TABLE_HEADING_PRODUCT_RECEIVE', 'Receive');
define('TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT', 'Amount Payable');
define('TABLE_HEADING_PAYABLE_TOTAL', 'Payable Total');

define('TEXT_DATE_ADDED', 'Date Added');
define('TEXT_CUSTOMER_NOTIFIED', 'Notified');
define('TEXT_COMMENTS', 'Comments');
define('TEXT_CHANGED_BY', 'Changed By');
define('TEXT_TOTAL', 'Total');

?>
