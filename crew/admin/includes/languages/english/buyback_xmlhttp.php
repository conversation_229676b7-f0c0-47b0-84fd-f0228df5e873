<?
if (!defined('TEXT_ORDER_NOT_BEEN_LOCKED'))			define('TEXT_ORDER_NOT_BEEN_LOCKED', '<span class="redIndicator">This Order has not been locked. Click the "LOCK" button on your right if you would like to edit this Order.</span>');
if (!defined('TEXT_LOCKED_ORDER_SEEN_BY_OWNER'))	define('TEXT_LOCKED_ORDER_SEEN_BY_OWNER', 'This order has been locked by you on %s at %s. If you are not going to edit this Order, please remember to click "UNLOCK" button on your right so that others may lock it.');
if (!defined('TEXT_DUPLICATE_LOCKED_ORDER_SEEN_BY_OWNER'))	define('TEXT_DUPLICATE_LOCKED_ORDER_SEEN_BY_OWNER', 'This order has been opened and locked by you on another browser window. This window will close after you click OK.');
if (!defined('TEXT_ORDER_LOCKED_BY_OTHER'))			define('TEXT_ORDER_LOCKED_BY_OTHER', '<span class="redIndicator">This order has been locked by %s on %s at %s. You cannot edit this Order unless you lock the Order. To do so, you must first contact this person or any of the person in the following Admin Goups to unlock the Order. %s</span>');
if (!defined('TEXT_UNLOCK_OTHER_PEOPLE_ORDER'))		define('TEXT_UNLOCK_OTHER_PEOPLE_ORDER', '<span class="redIndicator">This order has been locked by %s on %s at %s. You have the Permission to unlock it by clicking the "UNLOCK" button on your right.</span>');
if (!defined('TEXT_LOCKED_ORDER_NOT_VALID_STATUS'))	define('TEXT_LOCKED_ORDER_NOT_VALID_STATUS', 'This order cannot be locked since the order status is not in "Pending".');
if (!defined('TEXT_LOCKED_OUTDATED_ORDER'))			define('TEXT_LOCKED_OUTDATED_ORDER', 'Someone has changed this order before you manage to lock it.');

if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))		define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
if (!defined('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER'))	define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');
if (!defined('ORDERS_LOG_LOCK_ORDER'))				define('ORDERS_LOG_LOCK_ORDER', '##l_1##');
if (!defined('TEXT_LOG_MESSAGE_AUTO_CANCEL_OFF'))	define('TEXT_LOG_MESSAGE_AUTO_CANCEL_OFF', 'Auto cancel is turned off');
?>