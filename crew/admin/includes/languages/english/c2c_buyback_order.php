<?php

# Search Criteria
define('ERROR_RECORD_NOT_EXIST', 'Error: Record does not exist.');

define('HEADING_FROM_TO_DATE', '( <b>FROM</b> %s <b>TO</b> %s )');
define('HEADING_SEARCH_CRITERIA', 'C2C Sell Order Search Criteria');
define('HEADING_TITLE', 'C2C Sell Order');

define('LINK_PREVIEW', 'Preview');
define('LINK_SEARCH_CRITERIA', 'Search Criteria');

define('SELECT_C2C_BUYBACK_ORDER_ID', 'Buyback Order ID');
define('SELECT_CUSTOMERS_EMAIL_ADDRESS', 'OffGamers Email');
define('SELECT_DEFAULT_EMPTY', 'Please Select');
define('SELECT_SELLER_ID', 'OffGamers Customer ID');

define('SUB_TABLE_ACTION', 'Action');
define('SUB_TABLE_BO_ID', 'No.');
define('SUB_TABLE_BO_DATE', 'Date');
define('SUB_TABLE_BO_TOTAL_PAYABLE', 'Total Payable');
define('SUB_TABLE_GAME_NAME', 'Game Name');
define('SUB_TABLE_LOCKED', 'Locked');
define('SUB_TABLE_PRODUCT_TYPE', 'Product Type');
define('SUB_TABLE_SELLER_NAME', 'Seller Name');
define('SUB_TABLE_SELLER_EMAIL', 'Seller Email');

define('TEXT_ANY', 'Any');
define('TEXT_BUYBACK_ORDER_STATUS', 'Buyback Order Status');
define('TEXT_DISPLAY_NUMBER_OF_RECORD', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> record)');
define('TEXT_END_CREATE_DATE', 'End Create Date<br><small>(YYYY-MM-DD / YYYY-MM-DD HH:MM)</small>');
define('TEXT_FEMALE', 'Female');
define('TEXT_GAME_CATEGORIES', 'Game Categories');
define('TEXT_MALE', 'Male');
define('TEXT_PRODUCT_TYPE', 'Product Type');
define('TEXT_START_CREATE_DATE', 'Start Create Date<br><small>(YYYY-MM-DD / YYYY-MM-DD HH:MM)</small>');
define('TEXT_SEARCH_BY', 'Search By');

# Detail
define('EMAIL_SELL_ORDER_UPDATE', 'Sell Order ID : %s' . "\n" . 'Sell Order Date : %s' . "\n" . 'Sell Order Payable Amount : %s' . "\n\n" . 'Update Type : %s' . "\n" . 'Status Update : %s -> %s' . "\n" . 'Update Date : %s' . "\n" . 'Update IP : %s' . "\n" . 'Update User : %s' . "\n\n" . 'Update Comment :' . "\n" . '%s');
define('EMAIL_SELL_ORDER_UPDATE_SUBJECT', 'Sell Order Update Notification #%d');
define('EMAIL_SELL_ORDER_UPDATE_TYPE_A', 'Automatic');
define('EMAIL_SELL_ORDER_UPDATE_TYPE_M', 'Manual');

define('ERROR_BUY_ORDER_STATUS_DENIED', 'Error: Not allow to rollback when Buy Order not in Processing status.');
define('ERROR_MIN_DELIVERED_QTY_TO_COMPLETE', 'Error: Record must has at least 1 delivered quantity before update to Complete status');
define('ERROR_MIN_DELIVERED_QTY_TO_DELIVERED', 'Error: Record must has at least 1 delivered quantity before update to Delivered status');
define('ERROR_ORDER_NOT_LOCK', 'This order is not locked by you! Please lock it if you would like to edit this order.');

define('MESSAGE_UPDATE_SUCCESS', 'Success: Sell Order has been successfully updated.');

define('SUB_HEADING_SELLER_INFO', 'Seller Information');
define('SUB_TABLE_HEADING_TOTAL_ORDER', 'Total #');
define('SUB_TABLE_HEADING_TOTAL_AMOUNT', 'Total ');

define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_CUSTOMER_NOTIFIED', 'Customer Notified');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_CHANGED_BY', 'Changed By');
define('TABLE_HEADING_COMMENTS', 'Comments');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_CUSTOMER_ORDER', 'Customer Order');
define('TABLE_HEADING_TRADE_MODE', 'Trade Mode');
define('TABLE_HEADING_RSTK_CHARACTER', 'Trade Mode Details');
define('TABLE_HEADING_PRODUCT_ACTUAL_QTY', 'Actual Stock');
define('TABLE_HEADING_PRODUCT_UNIT_PRICE', 'Unit Price');
define('TABLE_HEADING_PRODUCT_REQUEST_LEVEL', 'Request Level');
define('TABLE_HEADING_PRODUCT_QUANTITY', 'Request Quantity');
define('TABLE_HEADING_PRODUCT_CONFIRMED_QUANTITY', 'Confirmed Quantity');
define('TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED', '<span title="Received">RECV*</span>');
define('TABLE_HEADING_PRODUCT_BALANCE', 'Balance');
define('TABLE_HEADING_PRODUCT_CURRENT_LEVEL', 'Current Level');
define('TABLE_HEADING_PRODUCT_RECEIVE', '<span title="Receive">RECV</span>');
define('TABLE_HEADING_EXTRA_QTY', '<span title="Extra quantity received for this product">EXTRA</span>');
define('TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT', 'Amount Payable');

define('TEXT_ACTION_SET_BUYBACK_REMARK', 'Set as Important Remark');
define('TEXT_BO_DATE', 'Sell Order Date');
define('TEXT_BO_LOCK', 'LOCK');
define('TEXT_BO_NOT_BEEN_LOCKED', '<span class="redIndicator">This Order has not been locked. Click the "LOCK" button on your right if you would like to edit this Order.</span>');
define('TEXT_BO_NUMBER', 'Sell Order Number');
define('TEXT_BO_STATUS', 'Sell Order Status');
define('TEXT_SELLER_FAULT', 'Seller Fault');
define('TEXT_BO_TOTAL', 'Total : ');
define('TEXT_BO_UNLOCK', 'UNLOCK');
define('TEXT_BO_UPDATE_DELIVER', 'Update quantity via Sell Order: ##G2G##%s##');
define('TEXT_BUYBACK_IN_1_DAY', 'Buyback orders in 1 day');
define('TEXT_COMMENTS_PROCESSING_TO_COMPLETED', 'You may withdraw the payment for this order from your G2G account after %s minutes if there is no outstanding issue.');
define('TEXT_LOCKED_BO_SEEN_BY_OWNER', 'This order has been locked by %s on %s at %s. If you are not going to edit this Order, please remember to click "UNLOCK" button on your right so that others may lock it.');
define('TEXT_LOG_ITEM_UPDATED', 'The following items have been delivered: ');
define('TEXT_LOG_ITEM_DEDUCTED', 'The following items have been updated: ');
define('TEXT_ORDER_STATISTICS', 'Order Statistics:');
define('TEXT_REASON_TO_CANCELED', 'Seller unable delivery this order');
define('TEXT_REMARKS', 'Remarks');
define('TEXT_SELLER', 'Seller');
define('TEXT_SELLER_DOB', 'Date of Birth');
define('TEXT_SELLER_EMAIL', 'Email Address');
define('TEXT_SELLER_GENDER', 'Gender');
define('TEXT_SELLER_GROUP', 'Seller Group');
define('TEXT_SELLER_IM', 'IM');
define('TEXT_SELLER_MOBILE_PHONE', 'Mobile Phone');
define('TEXT_SELLER_STATUS', 'Seller Status');
define('TEXT_SELLER_TELEPHONE', 'Telephone Number');
define('TEXT_SELLER_USERNAME', 'Seller ID');
define('TEXT_SHOW_MESSAGE_TO', 'Show this message to seller');
define('TEXT_STATUS', 'Status');
define('TEXT_TOTAL_BUYBACK', 'Total buyback order');
define('TEXT_UNLOCK_BO_OTHER_PEOPLE', '<span class="redIndicator">This order has been locked by %s on %s at %s. You have the Permission to unlock it by clicking the "UNLOCK" button on your right.</span>');
define('TEXT_SCREEN_SHOT', 'Screen Shot');

// HLA, Currency Automailling template
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_CONTECT1', 'The seller has been notified to prepare your order. You may also <a href="https://www.g2g.com/order/buyOrder/index">login to your G2G account</a> order details page and click "Chat Now" to contact the seller for delivery arrangement. The seller will respond to you as soon possible.<br><br>');
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_CONTECT2', 'Upon receiving the delivery, remember to login to your order page and confirm the amount received. Our system will automatically confirm the delivery on your behalf if there is no response after 72 hours.<br><br>');
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_MYACCOUNT_LINK', '<a href="https://www.g2g.com/order/buyOrder/index"><b>https://www.g2g.com/order/buyOrder/index</b></a><br><br>');
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_IMPORTANT_NOTICE', '<p style="color:#99999; padding:6px 0 6px 0; border-bottom:2px dotted #999999; border-top:2px dotted #999999;"><b><font color=\'red\'>IMPORTANT NOTE:</font></b><br /><br />DON’T return the in-game items under any circumstances after you have received it.<br/>DON’T purchase the product listing as a form of payment or as an exchange for other goods.<br/>Read more about <a href="https://support.g2g.com/support/solutions/articles/**********">trading safety guidelines</a>.<br><br>');
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_MORE_INFO_LINK', '<a href="https://support.g2g.com/support/solutions/folders/**********">Read more</a> about buying at G2G.');

define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_CONTECT1', 'The seller has been notified to prepare your order. You may also <a href="https://www.g2g.com/order/buyOrder/index">login to your G2G account</a> order details page and click "Chat Now" to contact the seller for delivery arrangement. The seller will respond to you as soon possible.<br><br>');
define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_CONTECT2', 'Please ensure the account you received is in working condition and secure all access and recovery options before confirming receipt of your order. Our system will automatically confirm the delivery on your behalf if there is no response after 72 hours.');
define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_MYACCOUNT_LINK', '<a href =\'https://www.g2g.com/order/buyOrder/index\'>https://www.g2g.com/order/buyOrder/index</a><br><br>');
define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_IMPORTANT_NOTICE',  '<p style="color:#99999; padding:6px 0 6px 0; border-bottom:2px dotted #999999; border-top:2px dotted #999999;"><b><font color=\'red\'>IMPORTANT NOTE:</font></b><br /><br />DON’T return the in-game items under any circumstances after you have received it.<br/>DON’T purchase the product listing as a form of payment or as an exchange for other goods.<br/>Read more about <a href="https://support.g2g.com/support/solutions/articles/**********">trading safety guidelines</a>.<br><br>');
define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_MORE_INFO_LINK', '<a href="https://support.g2g.com/support/solutions/folders/**********">Read more</a> about buying at G2G.');
