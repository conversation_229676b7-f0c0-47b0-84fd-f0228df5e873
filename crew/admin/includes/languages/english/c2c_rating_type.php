<?php
define('HEADING_TITLE', 'C2C Rating Type');
define('TEXT_PRODUCT_TYPE','Product Type');
define('SUB_TABLE_ATTRIBUTE','Rating Attribute');
define('SUB_TABLE_RATING_TYPE','Rating type <br/>( 1 = product rating attribute , 2 = product level)');
define('SUB_TABLE_DISPLAY_STATUS','Display Status');
define('SUB_TABLE_SORTING','Sorting');
define('SUB_TABLE_ACTION','Action');
define('LINK_ADD_RATING_TYPE', 'Add rating type');
define('TITLE_ADD_RATING_TYPE','Add rating type');
define('TITLE_EDIT_RATING_TYPE','Edit rating type');
define('ENTRY_RATING_DESC_EN','Rating Description (en)');
define('ENTRY_RATING_DESC_CNZH','Rating Description (cn-Zh)');
define('ENTRY_RATING_DESC_CNTW','Rating Description (cn-Tw)');
define('ENTRY_RATING_DESC_IN','Rating Description (in)');
define('ENTRY_CPTC_ID','Custom Product Type Child');
define('ENTRY_TYPE','Type');
define('TEXT_PRODUCT_RATING_ATTRIBUTE','Product rating attribute');
define('TEXT_PRODUCT_LEVEL','Product level');
define('ENTRY_DISPLAY_STATUS','Display Status');
define('TEXT_SHOW','Show');
define('TEXT_HIDE','Hide');
define('ENTRY_SORT_ORDER','Sort Order');
define('BUTTON_SAVE','Save');
define('BUTTON_UPDATE','Update');
define('BUTTON_CANCEL','Cancel');
define('ALT_BUTTON_SAVE','Save');
define('ALT_BUTTON_UPDATE','Update');
define('ALT_BUTTON_CANCEL','Cancel');
define('ERROR_DUPLICATE_SELLER_LEVEL_RATING_TYPE','Each product type only allow to have one product level rating attribute.');
?>