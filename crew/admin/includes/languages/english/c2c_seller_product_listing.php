<?php
define('HEADING_TITLE', 'C2C Seller Product Listing');
define('HEADING_SEARCH_CRITERIA', 'C2C Seller Product Listing Search Criteria');

define('SUB_TABLE_HEADING_ACTION', 'Action');
define('SUB_TABLE_PRODUCT_LIST_ID', 'No.');
define('SUB_TABLE_PRODUCT_CREATED_DATE', 'Created Date');
define('SUB_TABLE_PRODUCT_EXPIRY_DATE', 'Expiry Date');
define('SUB_TABLE_GAME_NAME', 'Game Name');
define('SUB_TABLE_PRODUCT_TYPE', 'Product Type');
define('SUB_TABLE_SELLER_NAME', 'Seller Name');
define('SUB_TABLE_SELLER_EMAIL', 'Seller Email');
define('SUB_TABLE_PRODUCT_PRICE_PER_UNIT', 'Product Price Per Unit');
define('SUB_TABLE_PRODUCT_STATUS', 'Product Status');
define('SUB_TABLE_PRODUCT_DISPLAY', 'Product Display');

define('ENTRY_START_CREATE_DATE', 'Start Create Date<br><small>(YYYY-MM-DD / YYYY-MM-DD HH:MM)</small>');
define('ENTRY_END_CREATE_DATE', 'End Create Date<br><small>(YYYY-MM-DD / YYYY-MM-DD HH:MM)</small>');
define('ENTRY_SEARCH_BY', 'Search By');
define('ENTRY_GAME_CATEGORIES', 'Game Categories');
define('ENTRY_PRODUCT_TYPE', 'Product Type');

define('SELECT_DEFAULT_EMPTY', 'Please Select');
define('SELECT_C2C_PRODUCTS_LISTING_ID', 'Product Listing ID');
define('SELECT_SELLER_ID', 'OffGamers Customer ID');
define('SELECT_CUSTOMERS_EMAIL_ADDRESS', 'OffGamers Email');

define('LINK_PREVIEW', 'Preview');
define('LINK_SEARCH_CRITERIA', 'Search Criteria');

define('ERROR_RECORD_NOT_EXIST', 'Error: Record does not exist.');

# C2C Product List Detail
define('SUB_HEADING_SELLER_INFO', 'Seller Information');
define('ENTRY_SELLER', 'Seller');
define('ENTRY_SELLER_USERNAME', 'Username');
define('ENTRY_SELLER_GROUP', 'Seller Group');
define('ENTRY_SELLER_STATUS', 'Seller Status');
define('ENTRY_SELLER_GENDER', 'Gender');
define('ENTRY_SELLER_DOB', 'Date of Birth');
define('ENTRY_SELLER_EMAIL', 'Email Address');
define('ENTRY_SELLER_TELEPHONE', 'Telephone Number');
define('ENTRY_SELLER_MOBILE_PHONE', 'Mobile Phone');
define('ENTRY_SELLER_IM', 'IM');

define('SUB_HEADING_PRODUCT_LIST_INFO', 'Product Listing Detail');
define('ENTRY_PLI_ID', 'Product Listing ID');
define('ENTRY_PLI_GAME', 'Game');
define('ENTRY_PLI_PRODUCT_TYPE', 'Product Type');
define('ENTRY_PLI_SERVICE', 'Service');
define('ENTRY_PLI_TITLE', 'Title');
define('ENTRY_PLI_DESCRIPTION', 'Description');
define('ENTRY_PLI_STATUS', 'Status');
define('ENTRY_PLI_DISPLAY', 'Display Status');
define('ENTRY_PLI_FORECAST_QUANTITY', 'Forecast Quantity');
define('ENTRY_PLI_RESERVED_QUANTITY', 'Reserved Quantity');
define('ENTRY_PLI_AVAILABLE_QUANTITY', 'Available Quantity');
define('ENTRY_PLI_ACTUAL_QUANTITY', 'Actual Quantity');
define('ENTRY_PLI_PRODUCT_PRICE', 'Product Price Per Unit');
define('ENTRY_PLI_PRODUCT_PRICE_USD_CHECKOUT', 'USD Price (USD checkout)');
define('ENTRY_PLI_PRODUCT_PRICE_NONUSD_CHECKOUT', 'USD Price (Non-USD checkout)');
define('ENTRY_PLI_MINIMUM_QUANTITY', 'Min. Sellable Qty');
define('ENTRY_PLI_BUSINESS_HR', 'Business Hr(s) *Based on GMT+8');
define('ENTRY_PLI_ONLINE_HR', 'Online Hr(s)');
define('ENTRY_PLI_OFFLINE_HR', 'Offline Hr(s)');
define('ENTRY_PLI_NON_BUSINESS_HR', 'Non-business Hr(s)');
define('ENTRY_PLI_DELIVERY_MODE', 'Delivery Mode');
define('ENTRY_PLI_CREATED_DATE', 'Created Date');
define('ENTRY_PLI_LAST_MODIFIED_DATE', 'Last Modified Date');
define('ENTRY_PLI_EXPIRY_DATE', 'Expiry Date');
define('ENTRY_PLI_GAME_ACCOUNT_INFO', 'Game Account Info');

define('SUB_HEADING_PRODUCT_LIST_MEDIA', 'Product Listing Media');
define('ENTRY_PLM_MEDIA_URL', 'Media URL');

define('SUB_HEADING_PRODUCT_LIST_HLA_INFO', 'HLA Product Detail');
define('ENTRY_PLH_ACCOUNT_TYPE', 'Account Type');
define('ENTRY_PLH_HLA_CHAR_NAME', 'HLA Character Name');
define('ENTRY_PLH_HLA_CHAR_DESC', 'HLA Character Description');
define('ENTRY_PLH_HLA_CHAR_ATTR', 'HLA Character Attribute');
define('ENTRY_ITEM_ATTR', 'Item Attribute');

define('SUB_HEADING_PRODUCT_LIST_GAME_ITEM_INFO', 'Game Item Product Detail');

define('SUB_HEADING_CUSTOMER_ORDER', 'Customer Order');
define('ENTRY_DELIVER_FOR_CUSTOMER_ORDER', 'Deliver for Customer Order');
define('ENTRY_ORDER_DELIVERY_MODE', 'Order Delivery Mode');

define('SUB_HEADING_PRODUCT_LIST_REMARKS_HISTORY', 'Product Listing Remarks History');
define('ENTRY_DATE', 'Date');
define('ENTRY_LOG_ACTION', 'Log Action');
define('ENTRY_REMARKS_BEFORE', 'Remarks Before');
define('ENTRY_REMARKS_AFTER', 'Remarks After');
define('ENTRY_ADDED_BY', 'Added By');
define('ENTRY_USER_ROLE', 'User Role');

define('TEXT_MALE', 'Male');
define('TEXT_FEMALE', 'Female');
define('TEXT_HOUR', 'Hour');
define('TEXT_SINGLE_CHAR_ACCOUNT', 'Single Character Account');
define('TEXT_STATUS__2', 'Deactivate');
define('TEXT_STATUS__1', 'Expire');
define('TEXT_STATUS_0', 'Suspend');
define('TEXT_STATUS_1', 'Active');
define('TEXT_STATUS_2', 'Deleted');
define('TEXT_MULTI_CHAR_ACCOUNT', 'Multiple Character Account');
define('TEXT_DISPLAY_NUMBER_OF_RECORD', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> record)');
define('TEXT_ORDER_ID', 'Order ID');

define('SUCCESS_PRODUCT_LIST_UPDATED', 'Success: C2C Seller Product Listing updated.');
define('ERROR_PRODUCT_LIST_UPDATE_FAIL', 'Error: Fail to update C2C Seller Product Listing.');
?>