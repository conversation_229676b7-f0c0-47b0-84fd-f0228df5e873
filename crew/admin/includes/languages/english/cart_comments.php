<?
/*
  	$Id: cart_comments.php,v 1.3 2005/12/28 06:23:54 chan Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

if ($_REQUEST['action']=='new_comments') {
	define('HEADING_TITLE', 'Add Comments');
} else {
	define('HEADING_TITLE', 'Customer Comments');
}

define('TABLE_HEADING_CART_COMMENTS_TITLE', 'Comment Title');
define('TABLE_HEADING_CART_COMMENTS_INPUT_TYPE', 'Field Type');
define('TABLE_HEADING_CART_COMMENTS_TYPE', 'Comment Type');
define('TABLE_HEADING_CART_COMMENTS_STATUS', 'Status');
define('TABLE_HEADING_CART_COMMENTS_REQUIRED', 'Mandarory');
define('TABLE_HEADING_CART_COMMENTS_SORT_ORDER', 'Sort Order');

define('ENTRY_COMMENT_TITLE', 'Comment Title');
define('ENTRY_COMMENT_FIELD_TYPE', 'Field Type');
define('ENTRY_COMMENTS_TYPE', 'Comments Type');
define('ENTRY_COMMENT_SETTING', 'Settings');
define('ENTRY_COMMENT_MANDATORY', 'Mandatory Comment');
define('ENTRY_COMMENT_STATUS', 'Comment Status');
define('ENTRY_COMMENT_ORDER', 'Sort Order');

define('TEXT_STATUS_ACTIVE', 'Active');
define('TEXT_STATUS_INACTIVE', 'Inactive');
define('TEXT_ICON_MANDATORY', 'Mandatory');
define('TEXT_ICON_SET_MANDATORY', 'Set Mandatory');
define('TEXT_ICON_OPTIONAL', 'Optional');
define('TEXT_ICON_SET_OPTIONAL', 'Set Optional');

define('LINK_ADD_CART_COMMENT', 'Add Comments');

//User Comments Statistic
define('HEADING_STATISTIC_TITLE', "Customer Comment's Statistics");

define('TABLE_HEADING_CART_COMMENTS_STATISTIC_OPTION', 'Options');
define('TABLE_HEADING_CART_COMMENTS_STATISTIC_COUNT', 'Count (%)');

define('ENTRY_COMMENTS_STATISTIC_EMPTY', 'Empty Comment');
define('ENTRY_COMMENTS_STATISTIC_NOTEMPTY', 'Comments');
define('ENTRY_COMMENTS_STATISTIC_TOTAL', 'Total');
?>