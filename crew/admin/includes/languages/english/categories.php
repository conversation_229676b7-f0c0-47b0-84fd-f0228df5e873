<?
/*
  	$Id: categories.php,v 1.52 2016/04/06 04:14:21 jeeva.kasinathan Exp $
    
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

  	Released under the GNU General Public License
*/

define('ENTRY_CATEGORY_DELIVERY_OPTIONS', 'Delivery Options:');
define('ENTRY_SORT_ORDER', 'Default Sort Order:');
define('ENTRY_ZONES_OPTIONS', 'Zones:');
define('ENTRY_CUSTOM_PRODUCTS_TYPE', 'Available Product Type:');

define('ENTRY_GAME_CURRENCY', 'Game Currency');
define('ENTRY_CD_KEY', 'CD Key');
define('ENTRY_POWER_LEVELLING', 'Power Levelling');
define('ENTRY_PRODUCTS_PAYMENT_MATURE_PERIOD', 'Payment Mature Period:<br><small>(mins)</small>');
define('ENTRY_DELIVERY_MODE', 'Delivery Mode');

define('ENTRY_INVENTORY', 'Inventory:');
define('ENTRY_ACTUAL_QTY', 'Actual:');
define('ENTRY_AVAILABLE_QTY', 'Available:');

// BOF Enable - Disable Categories Contribution--------------------------------------
define('TEXT_EDIT_STATUS', 'Status');
// EOF Enable - Disable Categories Contribution--------------------------------------
define('TEXT_EDIT_DISPLAY', 'Display');			// for products hidden

define('HEADING_TITLE', 'Products List');
define('HEADING_TITLE_SEARCH', 'Search:');
define('HEADING_TITLE_GOTO', 'Go To:');

//products_flag
define('TEXT_PRODUCTS_FLAG', 'Product Flags:');

define('TABLE_HEADING_ID', 'ID');
define('TABLE_HEADING_CATEGORIES_PRODUCTS', 'Categories / Products');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_C2C_STATUS', 'C2C Status');
define('TABLE_HEADING_DISPLAY', 'Display');		// for products hidden
define('TABLE_HEADING_PRICE_ADJUSTED', 'New Price');		// for adjusted price
define('TABLE_HEADING_PRODUCT_SORT', 'Sort Order');
define('TABLE_HEADING_ACTUAL_QTY', 'Actual Qty');

define('TABLE_HEADING_MULTI_CURRENCY_CODE', 'Currency');
define('TABLE_HEADING_MULTI_CURRENCY_PRICE', 'Price');

// Purchase Mode
define('TABLE_HEADING_AUTO_MODE', 'Auto<br><small>(Based on Available Qty)</small>');
define('TABLE_HEADING_MANUAL_MODE', 'Manual');
define('TABLE_HEADING_PURCHASE_MODE_DELIVERY_MSG', 'Delivery Message');

define('TEXT_PRODUCTS_BUNDLE', 'Products Bundle');

define('TEXT_NEW_PRODUCT', 'New Product in &quot;%s&quot;');
define('TEXT_CATEGORIES', 'Categories:');
define('TEXT_CATEGORIES_TYPE_GENERAL_SELECTION', 'General');
define('TEXT_SUBCATEGORIES', 'Subcategories:');
define('TEXT_PRODUCTS', 'Products:');
define('TEXT_PRODUCTS_ID', 'Product ID:');
define('TEXT_PRODUCTS_PRICE_INFO', 'Price:');
define('TEXT_PRODUCTS_TAX_CLASS', 'Tax Class:');
define('TEXT_PRODUCTS_AVERAGE_RATING', 'Average Rating:');
define('TEXT_PRODUCTS_QUANTITY_INFO', 'Quantity:');
define('TEXT_PRODUCTS_SKIP_INVENTORY', 'Do not keep inventory');
define('TEXT_PRODUCTS_ALLOW_NEGATIVE_QTY', 'Allow negative product quantity');
define('TEXT_LINKED_PRODUCT', 'Linked Products:');
define('TEXT_DATE_ADDED', 'Date Added:');
define('TEXT_DATE_AVAILABLE', 'Date Available:');
define('TEXT_LAST_MODIFIED', 'Last Modified:');
define('TEXT_IMAGE_NONEXISTENT', 'IMAGE DOES NOT EXIST');
define('TEXT_NO_CHILD_CATEGORIES_OR_PRODUCTS', 'Please insert a new category or product in this level.');
define('TEXT_PRODUCT_MORE_INFORMATION', 'For more information, please visit this products <a href="http://%s" target="blank"><u>webpage</u></a>.');
define('TEXT_PRODUCT_DATE_ADDED', 'This product was added to our catalog on %s.');
define('TEXT_PRODUCT_DATE_AVAILABLE', 'This product will be in stock on %s.');
define('TEXT_OPTION_NOT_APPLICABLE', 'n/a');
define('TEXT_PRODUCTS_DEFAULT_REORDER', '(Default: %d)');
define('TEXT_PRODUCTS_DEFAULT_PRE_ORDER_LEVEL', '(Default: %d)');
define('TEXT_PRODUCTS_DEFAULT_ETA', '(Default: %s)');
define('TEXT_PRUCHASE_MODE_ADD_TO_CART', 'Add to Cart:');
define('TEXT_PRUCHASE_MODE_PRE_ORDER', 'Pre-Order:');
define('TEXT_PRUCHASE_MODE_OUT_OF_STOCK', 'Out of Stock:');
define('TEXT_PRODUCT_TYPE_KEYWORDS', 'Product Type Keywords:');
define('TEXT_SEPARATE_BY_COMMA', 'Separate each keyword with \',\'');
define('TEXT_CHECKOUT_QUANTITY_MAX_QTY', 'Maximum Qty:');
define('TEXT_CHECKOUT_QUANTITY_MINUTES', 'Within Y minutes:');

define('TEXT_EDIT_INTRO', 'Please make any necessary changes');
define('TEXT_EDIT_CATEGORIES_ID', 'Category ID:');
define('TEXT_EDIT_CATEGORIES_NAME', 'Category Name');
define('TEXT_EDIT_CATEGORIES_SHORT_NAME', 'Short Name');
define('TEXT_EDIT_CATEGORIES_TEMPLATE', 'Category Template:');
define('TEXT_EDIT_CATEGORIES_TYPE', 'Category Type:');
define('TEXT_EDIT_CATEGORIES_URL_ALIAS', 'SEO URL Alias:');
define('TEXT_EDIT_CATEGORIES_CURRENT_SEO_URL', 'Current SEO URL: ');
define('TEXT_EDIT_CATEGORIES_SEO_URL', 'SEO URL: ');
define('TEXT_EDIT_CATEGORIES_AUTO_URL_ALIAS', 'Auto Generate SEO');
define('TEXT_EDIT_CATEGORIES_AUTO_SHORT_NAME', 'Auto Generate Short Name');
define('TEXT_EDIT_CATEGORIES_IMAGE', 'Category Image:');
define('TEXT_EDIT_CATEGORIES_IMAGE_TITLE', 'Category Image Title:');
define('TEXT_EDIT_CATEGORY_PINNED', 'Pin Category:');
define('TEXT_EDIT_PAYMENT_METHODS_RESTRICTIONS', 'Restrict by Payment Methods:');
define('TEXT_EDIT_PAYMENT_METHODS_RESTRICTIONS_ALLOW', '* Leave it empty to allow all payment methods.');
define('TEXT_EDIT_PAYMENT_METHODS_RESTRICTIONS_DISALLOW', '* Leave it empty to allow all payment methods. This setting will be ignore if there is specific allowed payment methods setting.');
define('TEXT_EDIT_SORT_ORDER', 'Sort Order:');
define('TEXT_EDIT_GAME_EVENT', 'Game Event:');
define('TEXT_EDIT_GAME_EVENT_URL', 'Game Event URL:');
define('TEXT_EDIT_GAME_GUIDE', 'Game Guide:');
define('TEXT_EDIT_GAME_GUIDE_URL', 'Game Guide URL:');

define('TEXT_CS_DELIVERY_MODE_F2F', 'Face to face trade');
define('TEXT_CS_DELIVERY_MODE_GUYA', 'Put into my account');
define('TEXT_CS_DELIVERY_MODE_MAIL', 'Mail');
define('TEXT_CS_DELIVERY_MODE_OPEN_STORE', 'Open Store');

define('TEXT_INFO_COPY_TO_INTRO', 'Please choose a new category you wish to copy this product to');
define('TEXT_INFO_CURRENT_CATEGORIES', 'Current Categories:');
define('TEXT_INFO_SET_CAT_ACTIVE', 'Active this category will also active its parents categories and all its subcategories!'.'\n'.' Click \'Yes\' to continue or \'No\' to cancel!');

define('TEXT_INFO_HEADING_NEW_CATEGORY', 'New Category');
define('TEXT_INFO_HEADING_EDIT_CATEGORY', 'Edit Category');
define('TEXT_INFO_HEADING_DELETE_CATEGORY', 'Delete Category');
define('TEXT_INFO_HEADING_MOVE_CATEGORY', 'Move Category');
define('TEXT_INFO_HEADING_DELETE_PRODUCT', 'Delete Product');
define('TEXT_INFO_HEADING_MOVE_PRODUCT', 'Move Product');
define('TEXT_INFO_HEADING_COPY_TO', 'Copy To');

define('TEXT_CUSTOMER_GROUP_CAT_DISCOUNT', 'Customer Group Discount:<ul class="extraInfo">%s</ul>');

define('TEXT_DELETE_CATEGORY_INTRO', 'Are you sure you want to delete this category?');
define('TEXT_DELETE_PRODUCT_INTRO', 'Are you sure you want to permanently delete this product?');

define('TEXT_DELETE_WARNING_CHILDS', '<b>WARNING:</b> There are %s (child-)categories still linked to this category!');
define('TEXT_DELETE_WARNING_PRODUCTS', '<b>WARNING:</b> There are %s products still linked to this category!');

define('TEXT_MOVE_PRODUCTS_INTRO', 'Please select which category you wish <b>%s</b> to reside in');
define('TEXT_MOVE_CATEGORIES_INTRO', 'Please select which category you wish <b>%s</b> to reside in');
define('TEXT_MOVE', 'Move <b>%s</b> to:');

define('TEXT_NEW_CATEGORY_INTRO', 'Please fill out the following information for the new category');
define('TEXT_CATEGORIES_NAME', 'Category Name:');
define('TEXT_CATEGORIES_SHORT_NAME', 'Short Name:');
define('TEXT_CATEGORIES_PIN_YIN', 'Pin Yin:');
define('TEXT_CATEGORIES_IMAGE', 'Category Image:');
define('TEXT_SORT_ORDER', 'Sort Order:');

define('TEXT_PRODUCTS_DTU_EXTRA_INFO_LEFT', 'DTU left label extra info');
define('TEXT_PRODUCTS_DTU_EXTRA_INFO_CALCULATION', 'DTU deno calculation extra info');
define('TEXT_PRODUCTS_DTU_EXTRA_INFO_RIGHT', 'DTU right label extra info');
define('TEXT_PRODUCTS_STATUS', 'Product Status:');
define('TEXT_PRODUCTS_DATE_AVAILABLE', 'Date Available:');
define('TEXT_PRODUCT_AVAILABLE', 'In Stock');
define('TEXT_PRODUCT_NOT_AVAILABLE', 'Out of Stock');
//define('TEXT_PRODUCTS_MANUFACTURER', 'Products Manufacturer:');
define('TEXT_PRODUCTS_MANUFACTURER', 'Realm:');
define('TEXT_PRODUCTS_NAME', 'Product Name:');
define('TEXT_PRODUCTS_ALT_NAME', 'Alt Product Name:');
define('TEXT_PRODUCTS_KEYWORD', 'Product Keyword:');
define('TEXT_EDIT_PRODUCTS_IMAGE_TITLE', 'Products Image Title:');
define('TEXT_EDIT_PRODUCTS_DESCRIPTION_IMAGE_TITLE', 'Products Description Image Title:');
define('TEXT_PRODUCTS_URL_ALIAS', 'SEO URL Alias:');
define('TEXT_PRODUCTS_DESCRIPTION', 'Product Description:');
define('TEXT_PRODUCTS_QUANTITY', 'Actual Quantity:');
define('TEXT_PRODUCTS_REORDER', 'Reorder Level:');
define('TEXT_PRODUCTS_REORDER_SUB', '<small>(Available Quantity)</small>');
define('TEXT_PRODUCTS_ETA', 'ETA (hours):');
define('TEXT_PRODUCTS_PRE_ORDER_LEVEL', 'Pre-Order Level:');
define('TEXT_PRODUCTS_MODEL', 'Product Model:');
define('TEXT_PRODUCTS_IMAGE', 'Product Image:');
define('TEXT_PRODUCTS_URL', 'Product URL:');
define('TEXT_PRODUCTS_URL_WITHOUT_HTTP', '<small>(without http://)</small>');
define('TEXT_PRODUCTS_PRICE_NET', 'Product Price (Net):');
define('TEXT_PRODUCTS_PRICE_GROSS', 'Product Price (Gross):');
define('TEXT_PRODUCTS_WEIGHT', 'Product Weight:');
define('TEXT_PRODUCTS_ZERO_PRICE', 'Zero Price Product:');
define('TEXT_API_PRODUCTS_COST','Product Cost');
define('TEXT_API_PRODUCTS_COST_COMPANY','Product Cost Company');
define('TEXT_API_PRODUCTS_COST_FINAL','Final Cost');

define('TEXT_CHECKOUT_QUANTITY_CONTROL', 'Checkout Qty Control:');
define('TEXT_PRODUCTS_PURCHASE_MODE', 'Purchase Mode:');
define('TEXT_INFO_DATE_ADDED', 'Info Date Added');
define('TEXT_INFO_LABEL', 'Label');
define('TEXT_INFO_REMARK', 'Info Remark');
define('TEXT_INFO_ADDED_BY', 'Added By');
define('TEXT_INFO_CHANGED_HISTORY_CATEGORIES', 'categories');
define('TEXT_INFO_CHANGED_HISTORY_PRODUCTS', 'products');
define('TEXT_PRODUCTS_SHARING_THIS_IMAGE', 'Products sharing this image');
define('TEXT_MESSAGE_LEAVE_COMMENT', 'Leave any message for this update?');

define('LINK_HIDE_INFO_HISTORY_BOX', 'Hide Info Changed History');
define('LINK_SHOW_INFO_HISTORY_BOX', 'Show Info Changed History');

define('JS_TEXT_SEO_ALIAS_REQUESTED', 'The SEO Alias is requested!');
define('JS_TEXT_SEO_SPECIAL_CHARACTER', 'The SEO Alias has special characters or space (accept minus "-"). These are not allowed. \nPlease remove them and try again.');

define('WARNING_PRODUCT_URL_ALIAS_DUPLICATE', 'Warning: The SEO URL Alias of this products is duplicate!');
define('WARNING_CATEGORIES_URL_ALIAS_DUPLICATE', 'Warning: The SEO URL Alias of this category is duplicate!');
define('WARNING_CATEGORIES_URL_ALIAS_CHANGED', 'Warning: The SEO URL Alias changed FROM %s TO %s!');
define('WARNING_PRODUCT_IMG_NOT_EXIST', 'Warning: Product image does not exist!');
define('WARNING_PRODUCT_DESCRIPTION_IMG_NOT_EXIST', 'Warning: Product description image does not exist');
define('WARNING_MIXED_PRODUCT_TYPE_IN_PACKAGE', 'Warning: Cannot mix the subproduct type in a package');
define('WARNING_DUPLICATE_SUBPRODUCT_IN_PACKAGE', 'Warning: Duplicate subproduct (%s)');

define('EMPTY_CATEGORY', 'Empty Category');

define('TEXT_HOW_TO_COPY', 'Copy Method:');
define('TEXT_COPY_AS_LINK', 'Link product');
define('TEXT_COPY_AS_DUPLICATE', 'Duplicate product');
define('TEXT_COPY_AS_DUPLICATE_FAP', 'Duplicate as bundle product (Follow Actual Product)');
define('X_SELL_ADD_PRODUCT', 'X-Sell edit product');

define('LINK_UPDATE_PRODUCT_COUNTS', 'Batch Update Products Count');

define('ERROR_CANNOT_LINK_TO_SAME_CATEGORY', 'Error: Can not link products in the same category.');
define('ERROR_CATALOG_IMAGE_DIRECTORY_NOT_WRITEABLE', 'Error: Catalog images directory is not writeable: ' . DIR_FS_CATALOG_IMAGES);
define('ERROR_CATALOG_IMAGE_DIRECTORY_DOES_NOT_EXIST', 'Error: Catalog images directory does not exist: ' . DIR_FS_CATALOG_IMAGES);
define('ERROR_CANNOT_MOVE_CATEGORY_TO_PARENT', 'Error: Category cannot be moved into child category.');
define('ERROR_COPY_TO_ACCESS_DENIED_CATEGORY', 'You are not allowed to copy product to category with id %s.');
define('ERROR_COPY_PRODUCT', 'Error: Some of the selected categories do not match the criteria of copying this product as indicated above. Click on "Confirm" button to proceed anyway or click "Edit" button to edit category list.');
define('ERROR_NEW_CATEGORY_PERMISSION', 'You are not allowed to create new category.');
define('ERROR_EXISTING_CAT_NAME', 'Error: The Category Name Already Exist.');
define('ERROR_EXISTING_PRODUCT_NAME', 'Error: The Product Name Already Exist.');
define('ERROR_EDIT_CATEGORY_PERMISSION', 'You are not allowed to edit category.');
define('ERROR_DELETE_CATEGORY_PERMISSION', 'You are not allowed to delete category.');
define('ERROR_MOVE_CATEGORY_PERMISSION', 'You are not allowed to move category.');
define('ERROR_FLAG_CATEGORY_PERMISSION', 'You are not allowed to change the category status.');
define('ERROR_NEW_PRODUCT_PERMISSION', 'You are not allowed to create new product.');
define('ERROR_EDIT_PRODUCT_PERMISSION', 'You are not allowed to edit product.');
define('ERROR_DELETE_PRODUCT_PERMISSION', 'You are not allowed to delete product.');
define('ERROR_MOVE_PRODUCT_PERMISSION', 'You are not allowed to move product.');
define('ERROR_COPY_PRODUCT_PERMISSION', 'You are not allowed to copy product.');
define('ERROR_FLAG_CATEGORY_PERMISSION', 'You are not allowed to change the product status.');
define('ERROR_UNABLE_TO_EDIT_FOLLOW_ACTUAL_PRODUCT', 'You are not allowed to edit the follow actual product.');
define('ERROR_CANNOT_MORE_THAN_ONE_SUBPRODUCT', 'Error: Cannot has more than 1 subproduct.');
define('ERROR_SUBPRODUCT_QTY_CANNOT_MORE_THAN_ONE', 'Error: Subproduct qty must be 1.');
define('ERROR_ADD_NEW_CURRENCY_PRICE_PERMISSION', 'You are not allow to add new currency price.');
define('ERROR_REMOVE_CURRENCY_PRICE_PERMISSION', 'You are not allow to remove currency price.');
define('ERROR_UPDATE_BUNDLE_EMPTY_PRODUCT', 'You are not allowed to update bundle with empty products.');

define('ERROR_FOLLOW_ACTUAL_PRODUCT_SUBPRODUCT_INVALID_TYPE', 'Only can has CD Key product as subproduct for Follow Actual Product type.');

define('ERROR_IMAGE_TYPE', 'You are only allowed to upload jpeg image.');

define('STOCK_EMAIL_SEPARATOR', '------------------------------------------------------');
define('STOCK_DEDUCT_EMAIL_TEXT_SUBJECT', '[' . STORE_NAME . '] Manual Stock Deduction Notification');
define('STOCK_ADD_EMAIL_TEXT_SUBJECT', '[' . STORE_NAME . '] Manual Stock Addition Notification');

define('STOCK_EMAIL_TEXT_DEDUCTION_INFO', '	<table border="0" cellspacing="2" cellpadding="2">'.
										  '		<tr>' .
										  '			<td height="5">&nbsp;</td>' .
										  '		</tr>' .
										  '		<tr>' .
										  '			<td style="color:#000099";" align="center">Manual Stock Deduction for Product: <b>%s</b></td>' .
										  '		</tr>' .
										  '		<tr>' .
										  '			<td>' .
										  '				<table border="0" cellspacing="2" cellpadding="2">'.
										  '					<tr>'.
										  '						<td style="background-color:#FFD073; color:#000099";">Time</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";">Category Path</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";" align="center">Old Actual Qty</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";" align="center">New Actual Qty</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";" align="center">Difference</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";">User</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";">IP</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";">Comments</td>'.
										  '					</tr>'.
										  '					<tr>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="center">%d</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="center">%d</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="center">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '					</tr>'.
										  '				</table>'.
										  '			</td>'.
										  '		</tr>'.
										  '	</table>');

define('STOCK_EMAIL_TEXT_ADDITION_INFO',  ' <table border="0" cellspacing="2" cellpadding="2">'.
										  '		<tr>' .
										  '			<td height="5">&nbsp;</td>' .
										  '		</tr>' .
										  '		<tr>' .
										  '			<td style="color:#000099";" align="center">Manual Stock Addition for Product: <b>%s</b></td>' .
										  '		</tr>' .
										  '		<tr>' .
										  '			<td>' .
										  '				<table border="0" cellspacing="2" cellpadding="2">'.
										  '					<tr>'.
										  '						<td style="background-color:#FFD073; color:#000099";">Time</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";">Category Path</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";" align="center">Old Actual Qty</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";" align="center">New Actual Qty</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";" align="center">Difference</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";">User</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";">IP</td>'.
										  '						<td style="background-color:#FFD073; color:#000099";">Comments</td>'.
										  '					</tr>'.
										  '					<tr>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="center">%d</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="center">%d</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="center">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '						<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										  '					</tr>'.
										  '				</table>'.
										  '			</td>'.
										  '		</tr>'.
										  '	</table>');

define('STOCK_CUSTPROD_DEDUCT_EMAIL_TEXT_SUBJECT', '[' . STORE_NAME . '] Manual Custom Product Stock Deduction Notification');
define('STOCK_CUSTPROD_ADD_EMAIL_TEXT_SUBJECT', '[' . STORE_NAME . '] Manual Custom Product Stock Addition Notification');
define('STOCK_CUSTPROD_EMAIL_TEXT_ADJUSTMENT_START', '%s for Product: %s' . "\n" .
                                                    'Action: %s' . "\n" .
                                                    'Time: %s' . "\n" .
                                                    'Category Path: %s' . "\n" .
                                                    'Old Actual Qty: %d' . "\n" .
                                                    'New Actual Qty: %d' . "\n" .
                                                    'Difference: %s' . "\n" .
                                                    'User: %s' . "\n" .
                                                    'IP: %s' . "\n" .
                                                    'Comments: %s' . "\n"
        );

define('STOCK_CUSTPROD_EMAIL_TEXT_ADJUSTMENT_FILES_BODY', "\n" .
                                                        'No. : %s' . "\n" .
                                                        'ID : %s' . "\n" .
                                                        'File Name : %s' . "\n"
        );

define('STOCK_CUSTPROD_EMAIL_ACTION_CATALOG_BATCH_UPD', 'Catalog: Batch Update.');
define('STOCK_CUSTPROD_EMAIL_ACTION_CATALOG_UPD', 'Catalog: Update');

//Categories Discount Settings
define('LINK_CATEGORIES_DISCOUNT_SETTINS_POPUP','Categories Discount Settings');
define('TEXT_EDIT_CATEGORY_DISCOUNT_TYPE','Category Discount Rules: ');
define('ERROR_EXISTING_DISCOUNT_RULES', 'Error: The Category Discount Rules Already Assign.');

?>