<?php
/*
  	$Id: polling.php,v 1.1 2008/01/02 10:00:33 leechuan.goh Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_CATEGORY_DISCOUNT_TITLE', 'Category Discount Rule List');
define('HEADER_FORM_CATEGORY_DISCOUNT_ADD_TITLE', 'Add Category Discount Rule');
define('HEADER_FORM_CATEGORY_DISCOUNT_EDIT_TITLE', 'Edit Category Discount Rule');
define('HEADER_CATEGORY_DISCOUNT_SHOW_MONITOR_PAGE', 'Category Discount Rule Monitor Page');

define('LINK_ADD_CATEGORY_DISCOUNT_RULE', 'Add Category Discount Rule');
define('LINK_SHOW_MONITOR_PAGE', 'Category Discount Monitor Page');
define('LINK_MONITOR_PAGE_BY_RULE_ID', '[Monitor Page]');

define('TABLE_HEADING_CATEGORY_DISCOUNT_RULE_ID', 'ID');
define('TABLE_HEADING_CATEGORY_DISCOUNT_RULE_NAME', 'Rule Name');
define('TABLE_HEADING_CATEGORY_DISCOUNT_RULE_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_CATEGORY_DISCOUNT_RULE_SETTING', 'Rule Setting');
define('TABLE_HEADING_CATEGORY_DISCOUNT_RULE_LAST_MODIFY_DATE', 'Last Modified Date');
define('TABLE_HEADING_CATEGORY_DISCOUNT_RULE_ACTION', 'Action');
define('TABLE_HEADING_REMOVE_PRODUCT', 'Remove');

define('MAX_DISPLAY_SEARCH_RESULTS_RULE_MONITOR_PAGE', '10');
define('MAX_DISPLAY_SEARCH_RESULTS', '10');


define('TABLE_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Title');
define('TABLE_HEADING_CUSTOMER_GROUP', 'Customer Group');
define('TABLE_HEADING_CUSTOMER_CURRENT_AVAILABLE_QTY', 'Last Updated Available Quantity');
define('TABLE_HEADING_CUSTOMER_RULES_AVAILABLE_QTY', 'Purchase Limit Quantity');
define('TABLE_HEADING_AUTO_CALCULATE','');



define('TABLE_CATEGORY_DISCOUNT_RULE_DATE', 'Date');
define('TABLE_CATEGORY_DISCOUNT_RULE_USER', 'User');
define('TABLE_CATEGORY_DISCOUNT_RULE_TYPE', 'Type');
define('TABLE_CATEGORY_DISCOUNT_RULE_COMMENT', 'Comment');

define('ENTRY_POLLING_QUESTION', 'Question:');
define('ENTRY_CATEGORY_DISCOUNT_RULE_NAME', 'Rule Name:');



define('ERROR_CATEGORY_DISCOUNT_MISSING_RULE_NAME', 'Error: Please enter rule name!');
define('ERROR_CATEGORY_DISCOUNT_MISSING_RULE_OR_PRODUCT_ID', 'Error: Rule ID or product ID missing!');
define('ERROR_CATEGORY_DISCOUNT_MISSING_RULE_ID', 'Error: Rule ID Missing');
define('ERROR_CATEGORY_DISCOUNT_RECALCULATE_ERROR', 'Error: Something Went Wrong, Please try again!');



define('MESSAGE_UPDATE_SUCCESS', 'Success: This Category Discount rule has updated.');
define('MESSAGE_INSERT_SUCCESS', 'Success: This Category Discount rule has inserted.');
define('MESSAGE_DELETE_SUCCESS', 'Success: This Category Discount rule has deleted.');
define('MESSAGE_INSERT_ERROR',  'Error: This Category Discount rule has not inserted.');
define('MESSAGE_UPDATE_ERROR',  'Error: This Category Discount rule has not updated.');

define('ERROR_ACCESS_CATEGORY_DISCOUNT_RULE_DENIED', 'Error: You do not have permission for the out of stock rule.');


define('ENTRY_DEFAULT_DISCOUNT', 'Discount Rate');
define('ENTRY_DEFAULT_REBATE', 'OP Rate');
define('ENTRY_CATEGORY', 'Category');
define('CATEGORY_DISCOUNT_GROUP_LIST', 'Category Discount List');
define('CATEGORY_ID', 'Category ID');
define('TABLE_HEADING_CATEGORY_DISCOUNT_WOR', 'Customer Group[Discount, WOR]');

?>