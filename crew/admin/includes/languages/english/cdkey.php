<?php
/*
  	$Id: cdkey.php,v 1.13 2011/07/11 04:50:18 wilson.sun Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
define('MAX_DISPLAY_SEARCH_RESULTS_CDK', '150');
define('MAX_DISPLAY_SEARCH_RESULTS_CDK_ZIP', '20');
define('MAX_UNZIPPING_RUNNING', '3');

define('TABLE_HEADING_PRODUCT', 'Product');
define('TABLE_HEADING_DATE', 'Last Change');
define('TABLE_HEADING_RELEASE_DATE', 'Release Date');
define('TABLE_HEADING_LIVE_DATE', 'Live Date');
define('TABLE_HEADING_LAST_STATUS_CHANGE', 'Last Status Change');
define('TABLE_HEADING_ADDED_DATE', 'Created');
define('TABLE_HEADING_MOD_DATE', 'Last Modified');
//define('TABLE_HEADING_IMAGE', 'Image');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_REMARKS', 'Remarks');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_OPERATIONS', 'Operations');
define('TABLE_HEADING_KEYS', 'Keys');
define('TABLE_HEADING_CODE', 'Code');
define('TABLE_HEADING_PRODUCTDESC', 'Description');
define('TABLE_HEADING_FILENAME', 'File Name');
define('TABLE_HEADING_RENAME_FILENAME', 'Rename File Name');
define('TABLE_HEADING_UPLOADED_BY', 'Uploaded By');
define('TABLE_HEADING_CDK_FILE', 'CDK File');
define('TABLE_HEADING_CDK_ID', 'CDK ID');
define('TABLE_HEADING_PO_REF_NUM', 'PO Ref #');
define('TABLE_HEADING_ORDER_ID', 'Order ID');
define('TABLE_HEADING_ZIP_ID', 'ZIP ID');
define('TABLE_HEADING_ZIP_FILENAME', 'ZIP File Name');
define('TABLE_HEADING_PASSWORD_UNZIP_FILE', 'Password / UNZIP');
define('TABLE_HEADING_FILE_SIZE', 'File Size');
define('TABLE_HEADING_UPLOADED_ON', 'Uploaded On');
define('TABLE_HEADING_QTY', 'Quantity');
define('TABLE_HEADING_UNIT_PRICE', 'Unit Price');
define('TABLE_HEADING_CATEGORY', 'Category');

define('HEADING_TITLE_GOTO', 'Go To:');

define('LABEL_LIST_BY_ORDER_ID', 'Order ID ');
define('LABEL_LIST_BY_CDK_ID', 'CDK ID ');
define('LABEL_LIST_BY_PO_REF_NUM', 'PO Ref Number ');
define('LABEL_LIST_BY_FILTER_STATUS', 'CDK Status ');
define('LABEL_LIST_BY_PRODUCT_CATEGORY', 'Category Filter');
define('LABEL_LIST_BY_SEARCH_PRODUCT', 'Search Product ');
define('LABEL_SET_STATUS', 'Set Status: ');
define('LABEL_SET_AVAILABLE', 'Set as Actual ');
define('LABEL_SET_ACTUAL', 'Move to Actual ');
define('LABEL_SET_DISABLED', 'Set as Disabled ');
define('LABEL_SET_ON_HOLD', 'Set as On Hold ');
define('LABEL_SET_BULK_REMARKS', 'Add Remark ');
define('LABEL_DELETE_DISABLED', 'Delete Disabled ');
define('LABEL_ADD_REMARKS', 'Add Remarks ');
define('LABEL_SET_DELETE', 'Delete ');
define('LABEL_LIST_CATEGORY', 'Category');
define('LABEL_LIST_PRODUCT', 'Product ');

define('LABEL_BATCH_ACTION', 'With selected: ');
define('LABEL_FILENAME', 'File name');
define('LABEL_OVERWRITE_FILENAME', 'Overwrite File');
define('LABEL_ID', 'ID');
define('LABEL_PRODUCT', 'Product');
define('LABEL_FILETYPES_ALLOWED', 'Allowed File Types');
define('LABEL_CATEGORY', 'Category');
define('LABEL_ZIP_FILE_OPTION', 'Zip File Option');
define('LABEL_QUANTITY', 'Quantity');
define('LABEL_UNIT_PRICE', 'Unit Price (USD)');
define('LABEL_DESCRIPTION', 'Description');
define('LABEL_PO_NUMBER', 'PO Ref. Number');

define('BUTTON_UPLOAD', 'Upload');
define('LABEL_ZIP_FILE_OPTION', 'Category');

define('OPTIONS_UPLOAD_TO_MANAGE_VAULT', '<img border="0" align="bottom" src="images/icons/locked.gif">Password Protected.');
define('OPTIONS_UPLOAD_TO_LIVE', 'NOT Password protected (auto unzipped to "On Hold" status)');
define('OPTIONS_UPLOAD_TO_JPG', 'JPG/PNG/GIF File, add individual CDK to "On Hold" status');

define('LINK_SORT_BY_DEFAULT', '-- Select --');
define('LINK_SORT_BY_STATUS', 'By Status');
define('LINK_SORT_BY_DATE', 'By Date');
define('LINK_SORT_BY_PRODUCT', 'By Product');
define('LINK_RESET', '[ Reset ]');

define('LINK_FILTER_BY_DEFAULT', '-- Select --');
define('LINK_FILTER_BY_ACTUAL', 'Actual');
define('LINK_FILTER_BY_AVAILABLE', 'Available');
define('LINK_FILTER_BY_ONHOLD', 'On Hold');
define('LINK_FILTER_BY_SOLD', 'Sold');
define('LINK_FILTER_BY_DISABLED', 'Disabled');
define('LINK_FILTER_BY_G2G', 'G2G');
define('LINK_FILTER_BY_RESERVED', 'Reserved');
define('LINK_FILTER_BY_ZIP', 'Zip');
define('LINK_FILTER_BY_UPLOAD', 'Upload');
define('LINK_FILTER_BY_PRODUCT', '-- By Product --');
define('LINK_SHOW_CDKEYIMAGES', 'Show CD Key Images / Soft Pin');
define('LINK_HIDE_CDKEYIMAGES', 'Hide CD Key Images / Soft Pin');

define('SUCCESS_MESSAGE_INSERT_CODE', 'Success: Insert OK : File %s (%s)');
define('ERROR_INSERT_CODE_FAILED', 'Upload failed : File %s');
define('ERROR_ZIP_NOT_PASSWORD_PROTECTED', 'Zip file you upload is not password protected');
define('SUCCESS_ZIP_FILE_UPLOAD', 'Zip file you upload is successfully uploaded');
define('ERROR_INVALID_ZIP_FILE', 'Zip file you upload is invalid. Make sure the zip file is not password protected.');
define('ERROR_INVALID_ZIP_PASSWORD', 'You have entered an incorrect Zip Password');
define('ERROR_MAX_UNZIPPING_RUNNING', 'There are too many unzipping proccess running currently. Please try again later.');

define('ERROR_FILE_NOT_FOUND', 'File not found');
define('ERROR_NO_PERMISSION', 'You have no permission');


define('SUCCESS_MESSAGE_UPDATE_CODE', 'Success: Update OK : File %s (%s)');
define('ERROR_UPDATE_CODE_FAILED', 'Update failed : File %s');
define('SUCCESS_MESSAGE_UPDATE_STATUS_BATCH', 'Success: Update Status OK for files listed below ;<br/>&nbsp;&nbsp;%s<br/>');
define('SUCCESS_MESSAGE_ADD_REMARKS_BATCH', 'Success: Add Remarks OK for files listed below ;<br/>&nbsp;&nbsp;%s<br/>');
define('ERROR_UPDATE_STATUS_BATCH_FAILED', 'Update Status failed for files listed below ;<br/>&nbsp;&nbsp;%s<br/>');
define('ERROR_UPDATE_STATUS_BATCH_PO_ERROR_FAILED', 'Update Status failed, %s, for files listed below ;<br/>&nbsp;&nbsp;%s<br/>');
define('ERROR_DELETE_BATCH_DENIED', 'Delete CD Key failed : Kindly set the following files to <U>Disabled</U> before deleting the following files ; <br/>(%s)<br/>');

define('MESSAGE_QTY_ESTIMATION', '* According to quantity entered by Crew member when uploading.');
define('MESSAGE_UNZIPPING_TABLE_STATUS', '<b>Unzipping is in Progress</b><br><small>Process Started at<br>%s</small>');
define('MESSAGE_UNZIPPING_TOP_STATUS_ITEM', 'Zip ID : %s - <b>%s</b> - Starts at <b>%s</b> by <b>%s</b>');
define('MESSAGE_UNZIPPING_TOP_STATUS', 'There are currently %s (out of %s available slots) unzipping process running');
define('SUCCESS_MESSAGE_DELETE_CODE', 'Success: Delete OK : File (%s)');
define('SUCCESS_MESSAGE_DELETE_ZIP_CODE', 'Success: Delete OK : Zip File (%s)');
define('ERROR_DELETE_CODE_FAILED', 'Delete failed : File (%s)');
define('ERROR_DELETE_ZIP_CODE_FAILED', 'Delete failed : Zip File (%s)');
define('SUCCESS_MESSAGE_UPDATE_STOCKQTY', 'Success: Update stock qty : Product %s (%s)');
define('ERROR_UPDATE_STOCKQTY_FAILED', 'Failed: Update stock qty : Product %s (%s)');
define('ERROR_DELETE_DISABLED', 'Delete only allowed for Disabled codes.');
define('ERROR_EXTENSION_ZIP_NOTFOUND', 'Failed : Zip Extension not loaded.');
define('ERROR_UPLOAD_IN_PROGRESS', 'Upload Failed : Another upload is in progress. Please try again later.');
define('ERROR_FILESIZE_EXCEEDED', 'Upload Failed : File Size of <b>%s<b> is too large and will not be imported. Please try again by lowering the resolution.');
define('ERROR_SINGLE_IMAGE_ONLY', 'Update Failed : Multiple images is not allowed.');

define('MESSAGE_FILTER_RESET', 'Reset');
define('ERROR_DISABLE_FAILED_REMARKS_CHECK', 'Remarks is required for disabling a code.');
define('ERROR_ON_HOLD_FAILED_REMARKS_CHECK', 'Remarks is required for changing status to On Hold');

define('ERROR_STATUS_IS_SOLD', '<font color=blue>Sold</font>');
define('ERROR_PERMISSION_DENIED', 'Permission denied');
define('ERROR_INVALID_PRODUCTID', 'Product ID is invalid.');
define('ERROR_INVALID_ZIP_QTY', 'ZIP Quantity is invalid.');
define('ERROR_INVALID_ZIP_UNIT_PRICE', 'Zip Unit Price is invalid.');
define('ERROR_ZIP_FILE_OPTION', 'Zip File Option is invalid.');

define('TEXT_DISPLAY_CURRENT_EDIT', 'File: %s (%s)');
define('TEXT_DISPLAY_REPLACE_IMAGE', 'Replace Image');

define('STOCK_CUSTPROD_DEDUCT_EMAIL_TEXT_SUBJECT', '[' . STORE_NAME . '] Manual Custom Product Stock Deduction Notification');
define('STOCK_CUSTPROD_ADD_EMAIL_TEXT_SUBJECT', '[' . STORE_NAME . '] Manual Custom Product Stock Addition Notification');
define('STOCK_CUSTPROD_EMAIL_TEXT_ADJUSTMENT_START', '%s for Product: %s' . "\n" .
                                                    'Action: %s' . "\n" .
                                                    'Time: %s' . "\n" .
                                                    'Category Path: %s' . "\n" .
                                                    'Old Actual Qty: %d' . "\n" .
                                                    'New Actual Qty: %d' . "\n" .
                                                    'Difference: %s' . "\n" .
                                                    'User: %s' . "\n" .
                                                    'IP: %s' . "\n" .
                                                    'Comments: %s' . "\n"
        );

define('STOCK_CUSTPROD_EMAIL_TEXT_ADJUSTMENT_FILES_BODY', "\n" .
                                                        'No. : %s' . "\n" .
                                                        'ID : %s' . "\n" .
                                                        'File Name : %s' . "\n"
        );
										  
define('STOCK_CUSTPROD_EMAIL_ACTION_FILEUPLOAD', 'New Upload');
define('STOCK_CUSTPROD_EMAIL_ACTION_UNZIP', 'UNZIP CD Key From Vault');
define('STOCK_CUSTPROD_EMAIL_ACTION_CODE_SET_DISABLE', 'Status set to <b>Disabled</b>');
define('STOCK_CUSTPROD_EMAIL_ACTION_CODE_SET_AVAILABLE', 'Status set to <b>Actual</b>');
define('STOCK_CUSTPROD_EMAIL_ACTION_PRODUCT_CHANGE', 'Changed Product assignment');
?>