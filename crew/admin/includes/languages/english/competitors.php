<?
if ($_REQUEST['action']=='new_competitor') {
	define('NAV_TITLE', 'Add New Competitor');
	define('HEADING_TITLE', 'Add New Competitor');
} else {
	define('NAV_TITLE', 'Competitor');
	define('HEADING_TITLE', 'Competitors Page');
}

define('TABLE_HEADING_COMPETITOR_NAME', 'Competitor Name');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_COMPETITOR_WEIGHT', 'Weight');
define('TABLE_HEADING_MAIN_CATEGORY', 'Main Category');

define('ENTRY_COMPETITOR_NAME', 'Competitor Name: ');

define('LINK_ADD_COMPETITOR', 'Create New Competitor');
define('LINK_ASSIGN_COMPETITOR', 'Assign Competitor');
define('IMAGE_ASSIGN', 'Assign');

define('TEXT_INFO_COMPETITOR_NAME_FALSE', '<b>ERROR:</b> At least the competitor name must have more than 1 character!');
define('TEXT_INFO_COMPETITOR_WEIGHT_FALSE', '<b>ERROR:</b> At least the competitor weight must have more than 1 character!');
define('TEXT_INFO_COMPETITOR_NAME_USED', '<b>ERROR:</b> Competitor name has been exists!');
define('TEXT_DISPLAY_NUMBER_OF_COMPETITORS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> competitors)');
?>