<?php
/*
  	$Id: competitors_average_market_price.php,v 1.5 2007/08/07 16:29:52 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', BOX_COMPETITORS_AVERAGE_MARKET_PRICE);
define('TABLE_HEADING_MAIN_CATEGORY', 'Main Category');
define('HEADING_SELECT_GAME', 'Select Game');
define('TABLE_HEADING_PRODUCT', 'Product Name');
define('TABLE_HEADING_COMPETITOR_CODE', 'Competitor');
define('TABLE_HEADING_COMPETITOR_WEIGHT', 'Weight');
define('TABLE_HEADING_AVERAGE_MARKET_PRICE', 'Buying Price<br>(Avg)');
define('TABLE_HEADING_OVERWRITE_AVERAGE_MARKET_PRICE', 'Overwrite Buying Price<br>(Avg)');
define('TABLE_HEADING_BRACKET_NEW', 'New Bracket');
define('TABLE_HEADING_BRACKET', 'Bracket(%s)');
define('TABLE_HEADING_ACTIVE_BRACKET', 'Active Bracket');

define('CSV_HEADING_BUYING_PRICE_IMPORT_CURRENCY', 'Currency (USD/CNY):');
define('CSV_HEADING_COMPETITOR_ID', 'COMPETITOR_ID');
define('CSV_HEADING_COMPETITOR_PRODUCT_ID', 'PRODUCT_ID');
define('CSV_HEADING_COMPETITOR_PRODUCT_PRICE', 'PRICE');
define('CSV_HEADING_COMPETITOR_PRODUCT_STATUS', 'STATUS');

define('TEXT_BRACKETS_FOR', 'Brackets for ');

define('IMAGE_BUTTON_UPDATE_BRACKETS', 'Update Brackets');

define('HEADING_ID', 'ID');
define('HEADING_NAME', 'NAME');
define('HEADING_PRICE', 'PRICE');
define('HEADING_STATUS', 'STATUS');

define('TEXT_PERCENTAGE', '%');
define('TEXT_MONEY', '$');
define('TEXT_PCT_SVR_FULL', '&#37; Server Full');
define('TEXT_MULTIPLIER', 'Multiplier');
define('TEXT_DELETE', 'Delete');

define('MESSAGE_ERROR_SO_ABORT', '[%s] Errors found. Update aborted.');
define('MESSAGE_INVALID_BRACKET_COUNT', 'Invalid Bracket Count.');
define('MESSAGE_INVALID_SUPPLIER_ID', 'Invalid Supplier ID : %s.');
define('MESSAGE_MISMATCHED_COLUMNS', 'Mismatched Columns detected. Competitor count does not match price columns.');
define('MESSAGE_INVALID_START_DATE', 'Get Average Sales : Invalida Start Date');

define('WARNING_COMPETITOR_WEIGHT_INVALID', 'Warning: The weight(%s) for competitor(%s) is invalid! No weight update for this competitor.');
define('WARNING_PRODUCT_DOES_NOT_EXISTS', 'Warning: The product id(%s) for %s is invalid.');
define('ERROR_IMPORT_FAILED', 'Error: Import aborted. Please check your csv file for errors.');
define('ERROR_AVG_PRICE_INVALID_GAME', 'Error: Invalid game specified.');
define('ERROR_AVG_PRICE_IMPORTED_PRODUCT_NOT_MATCH', 'Error: Product #%d does not match any of this game\'s products!');
define('SUCCESS_UPDATE_BUYING_PRICE', 'Success: Buying price has been successfully updated.');
define('SUCCESS_AVG_PRICE_IMPORT_FILE', 'Success: Imported file is valid. Please click on UPDATE to confirm the changes.');
?>