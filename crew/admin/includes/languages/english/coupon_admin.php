<?php
/*
  $Id: coupon_admin.php,v 1.7 2010/11/30 10:52:02 wilson.sun Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com
  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

define('TOP_BAR_TITLE', 'Statistics');
define('HEADING_TITLE', 'Discount Coupons');
define('HEADING_TITLE_STATUS', 'Status : ');
define('HEADING_TITLE_GOTO', 'Go To : ');
define('EDIT_HEADING_TITLE', 'Edit Discount Coupon');

define('TEXT_CUSTOMER', 'Customer:');
define('TEXT_COUPON', 'Coupon Name');
define('TEXT_COUPON_ALL', 'All Coupons');
define('TEXT_COUPON_PENDING', 'Pending Coupons');
define('TEXT_COUPON_ACTIVE', 'Active Coupons');
define('TEXT_COUPON_INACTIVE', 'Inactive Coupons');
define('TEXT_COUPON_CLOSED', 'Closed Coupons');
define('TEXT_COUPON_DELETED', 'Deleted Coupons');
define('TEXT_SUBJECT', 'Subject:');
define('TEXT_FROM', 'From:');
define('TEXT_FREE_SHIPPING', 'Free Shipping');
define('TEXT_MESSAGE', 'Message:');
define('TEXT_SELECT_CUSTOMER', 'Select Customer');
define('TEXT_ALL_CUSTOMERS', 'All Customers');
define('TEXT_NEWSLETTER_CUSTOMERS', 'To All Newsletter Subscribers');
define('TEXT_CONFIRM_DELETE', 'Are you sure you want to delete this Coupon?');

define('TEXT_COUPON_GENERATION_ALL', 'All Coupons Generations');
define('TEXT_COUPON_GENERATION_PENDING', 'Pending Coupons Generations');
define('TEXT_COUPON_GENERATION_APPROVED', 'Approved Coupons Generations');
define('TEXT_COUPON_GENERATION_CANCEL', 'Cancelled Coupons Generations');

define('TEXT_TO_REDEEM', 'You can redeem this coupon during checkout. Just enter the code in the box provided, and click on the redeem button.');
define('TEXT_IN_CASE', ' in case you have any problems. ');
define('TEXT_VOUCHER_IS', 'The coupon code is ');
define('TEXT_REMEMBER', 'Don\'t lose the coupon code, make sure to keep the code safe so you can benefit from this special offer.');
define('TEXT_VISIT', 'when you visit ' . HTTP_SERVER . DIR_WS_CATALOG);
define('TEXT_ENTER_CODE', ' and enter the code ');

define('TABLE_HEADING_COUPON_NAME', 'Coupons Generation / Coupons Name');
define('TABLE_HEADING_COUPON_REQUESTER', 'Coupons Requester');
define('TABLE_HEADING_COUPON_AMOUNT', 'Coupon Amount');
define('TABLE_HEADING_COUPON_NUMBERS', 'Actual Coupons / Number of Coupons');
define('TABLE_HEADING_COUPON_CODE', 'Coupon Code');
define('TABLE_HEADING_COUPON_PREFIX', 'Coupon Code Prefix');
define('TABLE_HEADING_COUPON_SUFFIX', 'Coupon Code Suffix');
define('TABLE_HEADING_COUPON_STATUS', 'Status');
define('TABLE_HEADING_COUPON_USAGE', 'Usage');
define('TABLE_HEADING_ACTION', 'Action');

define('CUSTOMER_ID', 'Customer id');
define('CUSTOMER_NAME', 'Customer Name');
define('REDEEM_DATE', 'Date Redeemed');
define('IP_ADDRESS', 'IP Address');

define('TEXT_REDEMPTIONS', 'Redemptions');
define('TEXT_REDEMPTIONS_TOTAL', 'In Total');
define('TEXT_REDEMPTIONS_CUSTOMER', 'For this Customer');
define('TEXT_NO_FREE_SHIPPING', 'No Free Shipping');

define('NOTICE_EMAIL_SENT_TO', 'Notice: Email sent to: %s');
define('EMAIL_APPROVAL_SUBJECT_TEXT', 'Discount Coupons Request Approved');
define('EMAIL_APPROVAL_BODY_TEXT', 'Your discount coupons request(%s) has been approved and generated.');
define('EMAIL_CANCEL_SUBJECT_TEXT', 'Discount Coupons Request Cancelled');
define('EMAIL_CANCEL_BODY_TEXT', 'Your discount coupon request(%s) has been cancelled.');

define('ERROR_NO_CUSTOMER_SELECTED', 'Error: No customer has been selected.');
define('ERROR_NO_COUPON_NAME', 'Error: No coupon name given.');
define('ERROR_NO_COUPON_AMOUNT', 'Error: No coupon amount.');
define('ERROR_NO_EDIT_PERMISSION', 'Error: You are not permitted to edit this discount code generation data.');
define('ERROR_NO_APPROVE_PERMISSION', 'Error: You are not permitted to approve this discount code generation.');
define('ERROR_COUPON_EXISTS', 'Error: Coupon already exists.');
define('ERROR_COUPON_NUMBER', 'Error: No Number of Coupons given.');
define('ERROR_COUPON_USES_COUPON', 'Error: No Uses per Coupon given.');
define('ERROR_COUPON_USES_USER', 'Error: No Uses per Customer given.');
define('ERROR_COUPON_NUMBER_MAX', 'Error: Maximum Number of Coupons allowed is %d.');
define('ERROR_COUPON_USES_ERROR', 'Error: Uses per Coupon must be in greater value than Uses per Customer.');
define('ERROR_COUPON_EXPIRY_OVER_START_DATE', 'Error: Expiry date can not be set earlier than start date.');

define('COUPON_TITLE_NAME', 'Coupons Generation / Coupons Name');
define('COUPON_REQUESTER', 'Coupon Requester');
define('COUPON_NAME', 'Coupon Name');
//define('COUPON_VALUE', 'Coupon Value');
define('COUPON_AMOUNT', 'Coupon Amount');
define('COUPON_CODE', 'Coupon Code');
define('COUPON_PREFIX', 'Coupon Code Prefix');
define('COUPON_SUFFIX', 'Coupon Code Suffix');
define('COUPON_STARTDATE', 'Start Date');
define('COUPON_FINISHDATE', 'End Date');
define('COUPON_FREE_SHIP', 'Free Shipping');
define('COUPON_DESC', 'Coupon Description');
define('COUPON_NUMBERS', 'Number of Coupons');
define('COUPON_MIN_ORDER', 'Coupon Minimum Order');
define('COUPON_USES_COUPON', 'Uses per Coupon');
define('COUPON_USES_USER', 'Uses per Customer');
define('COUPON_PRODUCTS', 'Valid Product List');
define('COUPON_CATEGORIES', 'Valid Categories List');
define('COUPON_CUSTOMERS_GROUPS', 'Valid Customers Groups');
define('COUPON_STATUS', 'Status');
define('COUPON_MAX_USAGE', 'Coupons Maximum Usage');
define('COUPON_GENERATION_NAME', 'Coupon Generation Name');
define('COUPON_GENERATION_DESC', 'Coupon Generation Description');
define('VOUCHER_NUMBER_USED', 'Number Used');
define('DATE_CREATED', 'Date Created');
define('DATE_MODIFIED', 'Date Modified');
define('TEXT_HEADING_NEW_COUPON', 'Create New Coupon');
define('TEXT_NEW_INTRO', 'Please fill out the following information for the new coupon.<br>');
define('TEXT_UNLIMITED', 'Unlimited');

define('TEXT_INFO_DATE_ADDED', 'Info Date Added');
define('TEXT_INFO_STATUS', 'Status');
define('TEXT_INFO_REMARK', 'User Remark');
define('TEXT_INFO_ADDED_BY', 'Added By');

define('LINK_HIDE_INFO_HISTORY_BOX', 'Hide Info Changed History');
define('LINK_SHOW_INFO_HISTORY_BOX', 'Show Info Changed History');

define('COUPON_NAME_HELP', 'A short name for the coupon');
define('COUPON_AMOUNT_HELP', 'The value of the discount for the coupon, either fixed or add a % on the end for a percentage discount.');
define('COUPON_CODE_HELP', 'You can enter your own code here, or leave blank for an auto generated one.');
define('COUPON_PREFIX_HELP', 'This is the Prefix for the discount code, default value is "OGM".');
define('COUPON_SUFFIX_HELP', 'This is the Suffix for the discount code.');
define('COUPON_STARTDATE_HELP', 'The date the coupon will be valid from (Midnight 00:00)<br>If your promotion start on 2010-05-01 00:00:00, please set to 2010-05-01.');
define('COUPON_FINISHDATE_HELP', 'The date the coupon expired (Midnight 00:00)<br>If your promotion end on 2010-05-05 23:59:59, please set to 2010-05-06.');
define('COUPON_FREE_SHIP_HELP', 'The coupon gives free shipping on an order. Note. This overrides the coupon_amount figure but respects the minimum order value');
define('COUPON_DESC_HELP', 'A description of the coupon for the customer');
define('COUPON_NUMBERS_HELP', 'Number of discount codes to be generated.');
define('COUPON_MIN_ORDER_HELP', 'The minimum order value before the coupon is valid');
define('COUPON_USES_COUPON_HELP', 'The maximum number of times the coupon can be used, leave blank if you want no limit.');
define('COUPON_USES_USER_HELP', 'Number of times a user can use the coupon, leave blank for no limit.');
define('COUPON_PRODUCTS_HELP', 'A comma separated list of product_ids that this coupon can be used with. Leave blank for no restrictions.');
define('COUPON_CATEGORIES_HELP', 'A comma separated list of cpaths that this coupon can be used with, leave blank for no restrictions.');
define('COUPON_CUSTOMERS_GROUPS_HELP', 'A list of Customer Discount Status that this coupon can be used with, checked ALL for no restrictions.');
define('COUPON_MAX_USAGE_HELP', 'Formula: Coupon Maximum Usage = Number of Coupons * Uses Per Coupon<br>Unlimited if Uses Per Coupon is set to unlimited.');
define('COUPON_GENERATION_NAME_HELP', 'A short name for the coupon generation, will be used as coupon name.');
define('COUPON_GENERATION_DESC_HELP', 'A description of the coupon generation for the customer, will be used as coupon description.');
?>