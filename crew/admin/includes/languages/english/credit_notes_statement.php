<?php
/*
  	$Id: credit_notes_statement.php,v 1.4 2011/07/19 04:31:58 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_CRNOTES_STAT_TITLE', 'Credit Notes Statement');

define('TABLE_SECTION_HEADING_CRNOTES_STAT', '<b>Credit Notes statement for %s, %s</b>');

define('TABLE_HEADING_CRNOTES_STAT_DATETIME', 'Date/Time');
define('TABLE_HEADING_CRNOTES_STAT_ACTIVITY', 'Activity');
define('TABLE_HEADING_CRNOTES_STAT_PURCHASE_ORDER', 'PO');
define('TABLE_HEADING_CRNOTES_STAT_DEBIT', 'Debit');
define('TABLE_HEADING_CRNOTES_STAT_CREDIT', 'Credit');
define('TABLE_HEADING_CRNOTES_STAT_BALANCE', 'Balance');

define('TABLE_HEADING_CRNOTES_STAT_MANUAL_ADD', 'Manual Addition');
define('TABLE_HEADING_CRNOTES_STAT_MANUAL_DEDUCT', 'Manual Deduction');

define('ENTRY_SUPPLIER_ID', 'Supplier ID');
define('ENTRY_CRNOTES_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_CRNOTES_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_TRANSACTION_ID', 'Transaction ID');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');

define('ENTRY_CRNOTES_STAT_CURRENCY_ACCOUNT', 'Currency Account');
define('ENTRY_CRNOTES_STAT_DEDUCT_AMOUNT', 'Deduct Amount');
define('ENTRY_CRNOTES_STAT_ADD_AMOUNT', 'Addition Amount');
define('ENTRY_CRNOTES_STAT_MANUAL_ADJUST_COMMENT', 'Comment: ');

define('TEXT_CRNOTES_STAT_NOT_APPLICABLE', '-');
define('TEXT_CRNOTES_STAT_TRANS_ID', '(Transaction ID: %s)');
define('TEXT_CRNOTES_STAT_ADD_COMMENT_SHOW', 'Show Add Comment');
define('TEXT_CRNOTES_STAT_ADD_COMMENT_HIDE', 'Hide Add Comment');

define('WARNING_CRNOTES_STAT_COMMENTS_EMPTY', 'Warning: No comments provided');
define('ERROR_CRNOTES_STAT_CURRENCY_ACC_NOT_EXISTS', 'Error: This user does not has credit note in this currency');
define('ERROR_CRNOTES_STAT_INVALID_DEDUCTION_AMOUNT', 'Error: Invalid manual deduction amount');
define('ERROR_CRNOTES_STAT_INVALID_ADDITION_AMOUNT', 'Error: Invalid manual addition amount');
define('SUCCESS_CRNOTES_STAT_MANUAL_DEDUCTION', 'Success: Manual deduction has been successfully performed');
define('SUCCESS_CRNOTES_STAT_MANUAL_ADDITION', 'Success: Manual addition has been successfully performed');
define('SUCCESS_CRNOTES_COMMENTS', 'Success: Comments has been successfully added');
define('SUCCESS_CRNOTES_COMMENTS_EMAIL', 'Success: Supplier Notification email has been successfully sent');

// Email definitions
define('EMAIL_SEPARATOR', '--------------------------------------------------------------');
define('EMAIL_CRNOTES_STAT_UPDATE_SUBJECT', "Credit Notes Statement Update #%s");

define('EMAIL_CRNOTES_STAT_TEXT_TITLE', 'Credit note statement transaction has been updated as below.');
define('EMAIL_CRNOTES_STAT_TEXT_TRANSACT_NUMBER', 'Transaction Number: %s');
define('EMAIL_CRNOTES_STAT_TEXT_TRANSACT_DATE', 'Transaction Date: %s');
define('EMAIL_CRNOTES_STAT_TEXT_ACTIVITY', 'Activity: %s');
define('EMAIL_CRNOTES_STAT_TEXT_COMMENTS', 'Updated Comment:' . "\n%s\n");
define('EMAIL_CRNOTES_STAT_TEXT_PO_LINK', 'Purchase Order Number: %s');
define('EMAIL_CRNOTES_STAT_TEXT_ACTIVITY_CURRENCY', 'Activity Currency: %s');
define('EMAIL_CRNOTES_STAT_TEXT_ACTIVITY_AMOUNT', 'Activity Amount: %s');
define('EMAIL_CRNOTES_STAT_TEXT_BALANCE_AMOUNT', 'Balance Amount: %s');
define('EMAIL_CRNOTES_STAT_TEXT_SUPPLIER_ALIAS', "Supplier Alias: %s");
define('EMAIL_CRNOTES_STAT_TEXT_SUPPLIER_NAME', "Supplier Name: %s");
define('EMAIL_CRNOTES_STAT_TEXT_UPDATE_USER', "Update User: %s");
define('EMAIL_CRNOTES_STAT_TEXT_UPDATE_IP', "Update IP: %s");

define('EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_SUBJECT', "%s #%s");
define('EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_ID', "Transaction ID: %s");
define('EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_DATE', "Transaction Date: %s");
define('EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_AMOUNT', "%s Amount: %s");
define('EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_SUPPLIER_ALIAS', "Supplier Alias: %s");
define('EMAIL_CRNOTES_STAT_NOTIFY_TRANSACT_SUPPLIER_NAME', "Supplier Name: %s");
define('EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_USER', "Update User: %s");
define('EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_IP', "Update IP: %s");
define('EMAIL_CRNOTES_STAT_NOTIFY_UPDATE_COMMENT', "Update Comment: \n%s\n");

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
?>