<?php
/*
  $Id: css_generator.php,v 1.2 2006/07/25 07:17:33 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

define('HEADING_TITLE', 'CSS Generator');

define('TABLE_HEADING_CURRENT_VERSION', 'Elements');
define('TABLE_HEADING_UPGRADE_VERSION', 'Values');

define('ENTRY_THEME', 'Theme');

define('ERROR_THEME_DIRECTORY_DOES_NOT_EXIST', 'Error: Theme directory does not exist. You are selecting non-existence theme.');

define('IMAGE_COLOUR_PATH', ($request_type == 'SSL' ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN . 'htmlarea/images/');
define('IMAGE_BG_COLOUR_SELECTION', IMAGE_COLOUR_PATH . 'ed_color_bg.gif');
define('IMAGE_FONT_COLOUR_SELECTION', IMAGE_COLOUR_PATH . 'ed_color_fg.gif');
/*
define('TABLE_HEADING_FILE_SIZE', 'Size');
define('TABLE_HEADING_ACTION', 'Action');

define('TEXT_VERSION_TITLE', "Version");
define('TEXT_INFO_NO_NEWEST_VERSION', 'No newest version is detected!');

define('ERROR_UPGRADE_DIRECTORY_DOES_NOT_EXIST', 'Error: Upgrade directory does not exist. Please set this in configure.php.');
define('ERROR_UPGRADE_DIRECTORY_NOT_READABLE', 'Error: Upgrade directory is not readable.');
define('ERROR_UPGRADE_VERSION_NOT_FOUND', 'Error: Upgrade versions file is not found.');

define('SUCCESS_STORE_UPGRADED', 'Success: The store has been successfully upgraded to Version ');
*/
?>