<?
/*
  $Id: customers.php,v 1.57 2015/06/24 03:09:19 chingyen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
 */

define('HEADING_TITLE', 'Customers List');
define('HEADING_TITLE_SEARCH', 'Search:');
define('HEADING_TITLE_SHARED_IP_LOGIN_HISTORY', 'SHARED IP History Page');
define('HEADING_TITLE_SHARED_IP_LOGIN_SUMMARY', 'SHARED IP Login Summary');
define('HEADING_TITLE_ACCOUNT_CREATION_SUMMARY', 'SHARED ACCOUNT CREATION IP Summary');
define('HEADING_TITLE_BUYBACK_IP_LOGIN_SUMMARY', 'SHARED BUYBACK IP Summary');
define('HEADING_TITLE_ORDER_IP_LOGIN_SUMMARY', 'SHARED ORDER IP Summary');
define('HEADING_TITLE_SHARED_IP_FOR_ACCOUNT_CREATION_SUMMARY', 'Shared IP for Account Creation Summary');
define('HEADING_TITLE_SHARED_DEVICE_ID_HISTORY', 'Device ID and IP History Page');
define('HEADING_TITLE_IP_INFORMATION', 'IP Information');
define('HEADING_TITLE_PROXY_IP_INFORMATION', 'Proxy IP Information');
define('HEADING_TITLE_DEVICE_INFORMATION', 'Device Information');
define('HEADING_TITLE_PERSONA_INFORMATION', 'Persona Information');

define('TABLE_HEADING_FIRSTNAME', 'First Name');
define('TABLE_HEADING_LASTNAME', 'Last Name');
define('TABLE_HEADING_CUSTOMER_FLAG', 'Flag');
define('TABLE_HEADING_EMAIL_ADDRESS', 'Email Address');
define('TABLE_HEADING_VOUCHER_BALANCE', 'OG Store Credit Balance');
define('TABLE_HEADING_ACCOUNT_CREATED', 'Account Created');
define('TABLE_HEADING_CUSTOMERS_STATUS', 'Status');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_CUSTOMER_ID', 'Customer ID');
define('TABLE_HEADING_LAST_LOGON', 'Last Logon');
define('TABLE_HEADING_COUNTRY', 'Country');
define('TABLE_HEADING_ANY_PURCHASE', 'Any Purchase');
define('TABLE_HEADING_FIRST_PURCHASE', 'First Purchase');
define('TABLE_HEADING_LAST_PURCHASE', 'Last Purchase');
define('TABLE_HEADING_SORT', 'Sort by');
define('TABLE_HEADING_RECORDS_PER_PAGE', 'Records per page');
define('TABLE_HEADING_LOGIN_DATE', 'Login Date');
define('TABLE_HEADING_LOGIN_IP', 'Login IP');
define('TABLE_HEADING_LOGIN_IP_SHARING', 'Login IP Sharing');
define('TABLE_HEADING_ACCOUNT_CREATION_IP_SHARING', 'Account Creation Ip Sharing');
define('TABLE_HEADING_BUYBACK_IP_SHARING', 'BUYBACK IP Sharing');
define('TABLE_HEADING_CUSTOMER_ORDER_IP_SHARING', 'Customer Order Ip Sharing');
define('TABLE_HEADING_BROWSER_UA', 'Browser UA');
define('TABLE_HEADING_OS', 'OS');
define('TABLE_HEADING_IP_ACCOUNT_CREATED', 'IP Account Created');
define('TABLE_HEADING_IP_SHARING', 'IP Sharing');
define('TABLE_HEADING_CUSTOMER_GROUP', 'Member Group');
define('TABLE_HEADING_ORDER_IP', 'Order IP');
define('TABLE_HEADING_DEVICE_ID_SHARING', 'Device ID Sharing');
define('TABLE_HEADING_PIN_REQUEST', 'Pin Request');

define('ENTRY_AFFILIATE_INFO', 'Affiliate: %s %s (%s)');
define('ENTRY_CUSTOMERS_AFT_GROUPS_NAME', 'AFT Group Level:');
define('ENTRY_CUSTOMERS_GROUPS_NAME', 'Customer Group Status:');
define('ENTRY_STORE_SC_BALANCE', 'OG SC Balance:');
define('ENTRY_STORE_SC_BALANCE_G2G', 'G2G SC Balance:');
define('ENTRY_STORE_RC_BALANCE', 'Reversible SC (RSC):');
define('ENTRY_STORE_NRC_BALANCE', 'Non-Reversible SC (NRSC):');
define('ENTRY_STORE_ACCOUNT_BALANCE', 'Withdrawable SC (WSC):');
define('ENTRY_STORE_POINT_BALANCE', 'OP:');
define('ENTRY_G2G_TOKEN_BALANCE', 'G2G Points:');
define('ENTRY_RESERVE_AMOUNT', 'Reserve Amount:');
define('ENTRY_CUSTOMERS_FLAG', 'Flag:');
define('ENTRY_CUSTOMERS_PHONE_VERIFICATION', 'Phone Verification:');
define('ENTRY_CUSTOMERS_REMARK', 'Remark:');
define('ENTRY_CUSTOMER_TYPE', 'Customer Type:');
define('ENTRY_CUSTOMER_RELATIONSHIP', 'Relationship with us:');
define('ENTRY_ACCOUNT_AGE', 'Account Age:');
define('ENTRY_ACCESS_TO', 'Access To:');

define('ENTRY_C2C_BUYER_ORDER_LEVEL', 'Buyer Order Level:');
define('ENTRY_C2C_PRODUCT_LEVEL', 'Seller Product Level:');
define('ENTRY_C2C_SELLER_PRODUCT_LISTING_STATUS', 'Seller Product Listing Status:');
define('ENTRY_C2C_SELLER_CREATE_LISTING_PERMISSION', 'Seller Create Listing Permission:');
define('ENTRY_C2C_SELLER_GROUP', 'Seller Group:');
define('ENTRY_C2C_SELLER_STATUS', 'Seller Status:');
define('ENTRY_C2C_SELLER_PRODUCT_LISTING_LIMIT', 'Seller Product Listing Limit:');
define('ENTRY_C2C_USERNAME', 'Username:');
define('ENTRY_C2C_VERIFIED_SELLER', 'Verified Seller:');
define('TEXT_C2C_SELLER_PRODUCT_LISTING_STATUS_CHANGED', '*Once STATUS changed, it will apply to Seller all current Product Listing.');
define('TEXT_C2C_SELLER_PRODUCT_PERMISSION', 'Seller Product Permission');
define('TEXT_UNVERIFIED', 'Unverified');
define('TEXT_VERIFIED', 'Verified');
define('LOG_C2C_SELLER_PRODUCT_LISTING_STATUS_CHANGED', 'Change seller all current Product Listing status to %s.');
define('LOG_C2C_REMARK', 'Remark : %s.');
define('ENTRY_AFT_WHITELISTED', 'AFT Whitelisted:');


define('LINK_BUYBACK_ORDER', 'BO');
define('LINK_CUSTOMER_ORDER', 'CO');
define('LINK_VIEW_ARCHIVE_IMAGE', 'View Archive Image');
define('LINK_VIEW_LOG', 'View Log');
define('LINK_VIEW_IN_PIPWAVE', 'View In pipwave');
define('LINK_VIEW_DEVICE_PIN', 'View Device Pin');
define('LINK_VIEW_DISABLE_2FA', 'Disable 2FA');
define('LINK_REMOVE_WHITELIST', 'Remove From Whitelist');

// Customer request account closure start
define('BUTTON_REQUEST_ACCOUNT_CLOSURE', 'Request Account Closure');
define('BUTTON_CANCEL_CLOSURE_REQUEST', 'Cancel Closure Request');
define('BUTTON_PENDING_ACCOUNT_CLOSURE', 'Pending Account Closure');
define('TEXT_ACCOUNT_CLOSURE', '*Set for Account Closure.');
define('TEXT_ACCOUNT_CLOSURE_CONFIRMED', '*Account Deleted.');
define('TEXT_CUSTOMER_REQUEST_ACCOUNT_CLOSURE', 'Changes made: Set for account closure');
define('TEXT_CUSTOMER_CANCEL_ACCOUNT_CLOSURE', 'Changes made: Cancel account closure');
define('TEXT_CUSTOMERS_CLOSURE_FORM_SUCCESS', 'Success: Account closure request received.');
define('TEXT_CUSTOMERS_CANCEL_CLOSURE_FORM_SUCCESS', 'Success: Cancel account closure request received.');
define('ERROR_NO_EDIT_CUSTOMER_ACCOUNT_CLOSURE', 'Error: You are not permitted to update Account Closure.');
// Customer request account closure end

// Customer disabled 2fa start
define('TEXT_DISABLED_2FA', 'Changes made: Disabled 2FA');
define('TEXT_DISABLED_2FA_FORM_SUCCESS', 'Success: 2FA disabled.');
// Customer disabled 2fa end

// Customer Online Offline start
define('BUTTON_CHECK_G2G', 'Check G2G Online');
define('BUTTON_CHECK_OG', 'Check OG Online');
//Customer Online Offline end

//customer_vip start
define('TABLE_HEADING_CUSTOMERS_VIP', 'Customers VIP');
define('TEXT_GROUP_CHANGED', '*Once group changed, it will reset the rank.');
define('ENTRY_BUYBACK_GROUP', 'Buyback Group:');
define('ENTRY_SUPPLIER_RATING', 'Supplier Rank:');
define('ENTRY_CUMMULATIVE_POINT', 'Cummulative Point:');
//customer_vip end

define('TEXT_INFO_NUMBER_OF_REVIEWS', 'Number of Reviews:');
define('TEXT_DELETE_INTRO', 'Are you sure you want to delete this customer?');
define('TEXT_DELETE_REVIEWS', 'Delete %s review(s)');
define('TEXT_INFO_HEADING_DELETE_CUSTOMER', 'Delete Customer');
define('TEXT_CUSTOMERS_INFO_UPDATE_SUCCESS', 'Success: Customer\'s information successfully updated.');
define('TYPE_BELOW', 'Type below');
define('PLEASE_SELECT', 'Select One');
define('TEXT_CATEGORY_LIST', 'Category List');
define('TEXT_VERIFICATION_SUBMISSION_FLAG_UPDATE_SUCCESS', 'Success: %s has been update successfully.');
define('TEXT_VERIFICATION_SUBMISSION_LOCK', 'Lock');
define('TEXT_VERIFICATION_SUBMISSION_UNLOCK', 'Unlock');

define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
define('EMAIL_CONTACT', 'For any enquiries or assistance, you may use our Online Live Support service or feel free to leave us a message. Please remember to include your Order Number when contacting us to expedite the process. Thanks again for shopping with us!');

define('TEXT_IMAGE_MOUSEOVER_FLAG', "Information has been flagged. Click to \'Unflagged\'");
define('TEXT_IMAGE_MOUSEOVER_NOT_FLAG', "Information has been unflagged. Click to \'flagged\'");

define('TEXT_SELECT_COUNTRY_LIST', 'Select Country');
define('LINK_SHOW_CRITERIA_BOX', 'Show Search');
define('LINK_HIDE_CRITERIA_BOX', 'Hide search');

define('TEXT_PURCHASE_AUTHORISATION_FORM', 'Selfie Only');
define('TEXT_PHOTO_ID', 'Photo ID');
define('TEXT_FRONT_CREDIT_CARD', 'Front Credit Card');
define('TEXT_BACK_CREDIT_CARD', 'Virtual Card / Mobile Payment App');
define('TEXT_UTILITY_BILLS', 'Utility Bills');
define('TEXT_NO_PERMISSION', 'No Permission.');

define('TEXT_REQUEST_ON', 'Requested On');
define('TEXT_REQUEST_DATE', 'Request Date');
define('TEXT_DEVICE_ID', 'Device ID');
define('TEXT_FUZZY_DEVICE_ID', 'Fuzzy Device ID');
define('TEXT_TRANSACTION_IDENTIFIER', 'Order');
define('TEXT_REQUEST_IDENTIFIER', 'Requested Identifier');
define('TEXT_VERIFICATION_RESULT', 'Verification Result');
define('TEXT_OPERATING_SYSTEM', 'Operating System');
define('TEXT_BROWSER_LANGUAGE', 'Browser Language');
define('TEXT_BROWSER_USER_AGENT', 'Browser User Agent');
define('TEXT_JS_ENABLITY', 'JavaScript Enabled');
define('TEXT_FLASH_ENABLITY', 'Flash Enabled');
define('TEXT_COOKIES_ENABLITY', 'Cookies Enabled');
define('TEXT_IMAGE_ENABLITY', 'Images Enabled');
define('TEXT_CSS_IMAGE_LOADED', 'CSS Image Loaded');
define('TEXT_SCREEN_RESOLUTION', 'Screen Resolution');
define('TEXT_FONTS', 'No. Of Fonts Installed');
define('TEXT_LOCAL_TIME_OFFSET', 'Local Time Offset');
define('TEXT_LOCAL_TIME_OFFSET_RANGE', 'Local Time Offset Range');
define('TEXT_TIME_ZONE', 'Time Zone');
define('TEXT_DEVICE_LAST_UPDATE', 'Device Last Update Date And Time');
define('TEXT_PROFILING_DATETIME', 'Profiling Date And Time');
define('TEXT_TRUE_IP_INFORMATION', 'True IP');
define('TEXT_TRUE_IP_CITY', 'True IP City');
define('TEXT_TRUE_IP_GEO', 'True IP Country Code');
define('TEXT_TRUE_IP_ISP', 'True IP ISP');
define('TEXT_TRUE_IP_LATITUDE_LONGITUDE', 'True IP Latitude/Longitude');
define('TEXT_PROXY_IP_INFORMATION', 'Proxy IP Information');
define('TEXT_PROXY_IP_CITY', 'Proxy IP City');
define('TEXT_PROXY_IP_GEO', 'Proxy IP Country Code');
define('TEXT_PROXY_IP_ISP', 'Proxy IP ISP');
define('TEXT_PROXY_IP_LATITUDE_LONGITUDE', 'Proxy IP Latitude/Longitude');
define('TEXT_DEVICE_FIRST_SEEN', 'Device First Seen');
define('TEXT_DEVICE_SETTING_COUNTRY', 'Device Setting Country');
define('TEXT_DEVICE_PHYSICAL_COUNTRY', 'Device Physical Country');
define('TEXT_DEVICE_PHYSICAL_REGION', 'Device Physical Region');
define('TEXT_PC_REMOTE_ENABLITY', 'PC Remote Enabled');
define('TEXT_IS_MOBILE_DEVICE', 'Is Mobile Device');
define('TEXT_MOBILE_FORWARDER', 'Device Forwarder');
define('TEXT_MOBILE_TYPE', 'Mobile Type');
define('TEXT_VOICE_ENABLITY', 'Voice Device Enabled');
define('TEXT_PERSONA_COUNTRY', 'Associated Riskiest Country');
define('TEXT_PERSONA_NETWORK', 'Associated Riskiest Network Type');
define('TEXT_PERSONA_CREDIT_CARDS', 'Associated Credit Cards');
define('TEXT_PERSONA_DEVICES', 'Associated Devices');
define('TEXT_PERSONA_EMAILS', 'Associated Emails');
define('TEXT_PERSONA_ORDERS_14_DAYS', 'Orders (Last 14 Days)');
define('TEXT_PERSONA_ORDERS_6_HOURS', 'Orders (Most Active 6 hours in Last 14 days)');

define('TEXT_SUCCESS', 'Success');
define('TEXT_FAILED', 'Failed');
define('TEXT_DEVICE_PIN', 'Shasso Device Pin:');
define('TEXT_DISABLE_2FA', 'Shasso 2FA:');
define('TEXT_2FA_STATUS_ENABLE', 'Enabled');
define('TEXT_2FA_STATUS_DISABLED', 'Disabled');
define('TEXT_VALID_UNTIL', 'valid until');

define('TEXT_ENABLE', 'Enable');
define('TEXT_DISABLE', 'Disable');
define('TEXT_EMPTY', '- Empty -');
define('TEXT_UNLIMITED', 'Unlimited');
define('TEXT_MAXIMUM_PRODUCT_LISTING', 'Max Product Listing');
define('TEXT_CUSTOMERS_C2C_UPDATED', 'Success: The customer\'s C2C account infomations have been update successfully.');

define('ERROR_CUSTOMER_NOT_EXIST', 'Error: Customer does not exist.');
define('ERROR_NO_EDIT_CUSTOMER_PERMISSION', 'Error: You do not have the permission to edit this customer.');
define('ERROR_NO_EDIT_CUSTOMER_FLAG_PERMISSION', 'Error: You do not have the permission to edit some customer flag.');
define('ERROR_EMPTY_POST_DATA','Error : No data submitted, please try again');
define('ERROR_INVALID_CUMMULATIVE_POINT', 'Error: Please Enter number only');
define('ERROR_NO_EDIT_CUSTOMER_GROUP_PERMISSION', 'Error: You are not permitted to update Customer Group to: %s'); # Customer Group Status: ********
define('ERROR_NO_EDIT_CUSTOMER_AFT_GROUP_PERMISSION', 'Error: You are not permitted to update AFT Group Level to: %s');
define('ERROR_C2C_ACCOUNT_NOT_EXIST', 'Error: C2C Account does not exist.');
define('ERROR_C2C_USERNAME_EXIST', 'Error: C2C Username already in use by other seller.');

define('SEARCH_ENTRY_TEXT_CUSTOMERS_ID', 'Customer ID');
define('SEARCH_ENTRY_TEXT_ACCOUNT_ACTIVATION', 'Account Activation');
define('SEARCH_ENTRY_TEXT_ACCOUNT_STATUS', 'Account Status');
define('SEARCH_ENTRY_TEXT_FIRSTNAME', 'First Name');
define('SEARCH_ENTRY_TEXT_LASTNAME', 'Last Name');
define('SEARCH_ENTRY_TEXT_CUSTOMERS_ACCOUNT_CREATED_IP', 'Account Created IP Address');
define('SEARCH_ENTRY_CUSTOMERS_TYPE', 'Customers Type:');
define('SEARCH_ENTRY_TEXT_CUSTOMERS_BUYBACK_ORDER_IP', 'Buyback Order IP Address');
define('SEARCH_ENTRY_TEXT_CUSTOMERS_ORDER_IP', 'Order IP Address');
define('SEARCH_ENTRY_TEXT_CUSTOMERS_DOB', 'Customer D.O.B');
define('SEARCH_ENTRY_TEXT_EMAIL_ADDRESS', 'Email Address');
define('SEARCH_ENTRY_TEXT_COUNTRY', 'Country');
define('SEARCH_ENTRY_TEXT_TELEPHONE_LOCATION', 'Telephone Location');
define('SEARCH_ENTRY_TEXT_CUSTOMERS_PHONE', 'Telephone');
define('SEARCH_ENTRY_TEXT_CUSTOMER_GROUP', 'Customers Group');
define('SEARCH_ENTRY_TEXT_CUSTOMERS_FLAG_STATUS', 'Flag Status');
define('SEARCH_ENTRY_TEXT_CUSTOMERS_FLAG', 'Flag');
define('SEARCH_ENTRY_TEXT_CUSTOMERS_NO_FLAG', 'No Flag');
define('SEARCH_ENTRY_TEXT_DESC', 'Descending');
define('SEARCH_ENTRY_TEXT_ASC', 'Ascending');

define('SEARCH_ENTRY_TEXT_SIGNUP', 'Sign Up Period');
define('SEARCH_ENTRY_TEXT_FROM', 'From');
define('SEARCH_ENTRY_TEXT_TO', 'To');
define('SEARCH_ENTRY_TEXT_CONFIRMED_COMPLETED', 'Confirmed Completed');
define('SEARCH_ENTRY_TEXT_COMPLETED', 'Completed');
define('SEARCH_ENTRY_TEXT_CATEGORY', 'Ordered Category');
define('SEARCH_ENTRY_TEXT_INCLUDE_SUBCATEGORY', 'Include Sub Categories');
define('SEARCH_ENTRY_TEXT_NEWSLETTER', 'Newsletter');
define('SEARCH_ENTRY_TEXT_EMAIL_VERIFICATION', 'Email Verification');
define('SEARCH_ENTRY_TEXT_EMAIL_VERIFIED', 'Verify');
define('SEARCH_ENTRY_TEXT_EMAIL_UNVERIFY', 'Unverify');
define('SEARCH_ENTRY_TEXT_NEWSLETTER_ANY', 'Any');
define('SEARCH_ENTRY_TEXT_NEWSLETTER_UNSUBSCRIBE', 'Unsubscribe');

define('SEARCH_ENTRY_TEXT_G2G_USERNAME', 'G2G Username');

define('SECTION_ACCOUNT', 'Account');
define('SECTION_G2G', 'G2G');
define('SECTION_PARTICULR', 'Particulars');
define('SECTION_ORDER', 'Order');
define('SECTION_RESULT_OPTION', 'Result Options');

define('EMAIL_USER_RESET_PIN_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, 'Secret Token reset notification')));

# email subject and content for Customer Group Status
define('EMAIL_CUSTOMER_GROUP_STATUS_SUBJECT', 'Customer Group Update Notification Customer ID#%d');
define('EMAIL_CUSTOMER_GROUP_STATUS_CONTENT', 'Update Type: %s' . "\n" . 'Customer ID: %d' . "\n" . 'Customer Name: %s' . "\n" . 'Status Update: %s -> %s' . "\n\n" . 'Update Date: %s' . "\n" . 'Update IP: %s' . "\n" . 'Update User: %s' . "\n\n" . 'Update Comment:' . "\n" . '%s');

define('ENTRY_CONTACT_NUMBER_EXIST_ERROR', "This mobile phone number you provide has been used by %s.");
define('ENTRY_OP_BALANCE','OP Balance:');

?>