<?
/*
  	$Id: customers_acc_merging.php,v 1.5 2008/06/11 04:22:54 boonhock Exp $

  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Venture
  	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Customers Account Merging');

define('HEADING_TITLE_STEP_1', 'Step 1: Fill in customers e-mail for merging.');
define('HEADING_TITLE_STEP_2', 'Step 2: Please find the confirmation customers details as below. Click "Confirm" to merge these accounts.');
define('HEADING_TITLE_STEP_3', 'Step 3: Perform customers account merging.');

define('TABLE_HEADING_MERGE_FROM_CUSTOMER_INFO', 'Merge from customer');
define('TABLE_HEADING_MERGE_TO_CUSTOMER_INFO', 'Merge into customer');

define('ENTRY_ACCOUNT_MERGING_EMAILS', 'Merge this customer account, %s (e-mail of Customer A) into this customer account, %s (e-mail of Customer B)');

define('ERROR_MISSING_SOURCE_CUSTOMER', 'Error: Missing Customer A e-mail.');
define('ERROR_MISSING_DESTINATION_CUSTOMER', 'Error: Missing Customer B e-mail.');
define('ERROR_CUSTOMER_NOT_EXISTS', 'Error: Customer(%s) does not exists.');
define('ERROR_CUSTOMER_NOT_ACTIVE', 'Error: Customer(%s) account is not active.');
define('ERROR_CUSTOMER_FROM_BUYBACK_OR_AFFILIATE', 'Error: Customer(%s) account is able access to buyback or affiliate.');

define('ERROR_SAME_CUSTOMERS', 'Error: You are entering same customer email address.');

define('TEXT_ACC_MERGING_NOTES', '<span class="redIndicator">You only can merge active customer account.<br>Both customers account status will be deactivated to prevent new records being created during merging.</span>');
define('TEXT_WAIT_MERGING_PROCESS', '<span class="redIndicator">Please wait while the accounts being merge...</span>');
define('TEXT_ACC_MERGING_DONE', 'DONE: The customer account (%s) has been successfully merged into customer account (%s).');

define('JS_CONFIRM_MERGE', 'Are you sure to merge &lt;%s&gt; account into &lt;%s&gt; account?');

// Email definitions
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_CUSTOMERS_ACC_MERGED_SUBJECT', 'Your accounts have been merged');
define('EMAIL_CUSTOMERS_ACC_MERGED_EMAILS', 'Kindly note that your account at '. STORE_NAME .', %s, has been merged into %s.');
define('EMAIL_CUSTOMERS_ACC_MERGED_STATUS', ' All transactions have been preserved.  You member status is now %s and all profile information has been retained from %s.  Please login with %s from now onwards.');
define('EMAIL_CUSTOMERS_ACC_MERGED_PASS', ' You may use the "<a href="'. tep_catalog_href_link(FILENAME_PASSWORD_FORGOTTEN) .'">Forgot Password?</a>" link at our store to reset your password if you have lost the password to %s.  Kindly contact our Customer Support if you encounter any problem.');
define('EMAIL_CUSTOMERS_ACC_VOUCHER_BALANCE', 'Your voucher amount from account of %s has added into account of %s.');

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
?>