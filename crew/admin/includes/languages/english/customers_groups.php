<?
if ($action == 'new_discount') {
	define('HEADING_TITLE', 'Add Customers Group Discount');
} else if ($action == 'edit_discount') {
	define('HEADING_TITLE', 'Edit Customers Group Discount');
} else {
	define('HEADING_TITLE', 'Customers Grouping');
}
define('HEADING_TITLE_SEARCH', 'Search:');
define('HEADING_TITLE_AFT_GROUP', 'AFT Group Level');

define('TABLE_HEADING_NAME', 'Name');
define('TABLE_HEADING_DISCOUNT_SETTING', 'Discount Setting');
define('TABLE_HEADING_CATEGORY', 'Category');
define('TABLE_HEADING_DISCOUNT', 'Discount Rate');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_REBATE', 'Rebate Rate');
define('TABLE_HEADING_SORT_ORDER', 'Sort Order');
define('TABLE_HEADING_CUSTOMER_GROUP_EXTRA_SC', 'Extra SC (%)');
define('TABLE_HEADING_C2C_DISCOUNT', 'C2C Discount Rate');
define('TABLE_HEADING_C2C_REBATE', 'C2C Rebate Rate');
define('ENTRY_CUSTOMER_GROUP_NAME', 'Group Name');
define('ENTRY_CUSTOMER_AFT_GROUP_NAME', 'AFT Group Name');
define('ENTRY_CATEGORY', 'Category');
define('ENTRY_DEFAULT_DISCOUNT', 'Discount Rate');
define('ENTRY_DEFAULT_REBATE', 'Rebate Rate');
define('ENTRY_DEFAULT_C2C_DISCOUNT', 'C2C Discount Rate');
define('ENTRY_DEFAULT_C2C_REBATE', 'C2C Rebate Rate');
define('ENTRY_PAYMENT_METHODS', 'Payment Methods');
define('ENTRY_CUSTOMER_GROUP_EXTRA_SC', 'Extra Store Credit (in %)');
define('ENTRY_DEFAULT_C2C_DISCOUNT', 'C2C Discount Rate');
define('ENTRY_DEFAULT_C2C_REBATE', 'C2C Rebate Rate');

define('TEXT_DELETE_INTRO', 'Are you sure you want to delete this group?');
define('TEXT_INFO_HEADING_DELETE_GROUP', 'Delete Group');
define('TEXT_CUSTOMER_GROUP_DISCOUNT', 'Customer Group Discount');
define('TEXT_NO_DISCOUNT_DEFINE', 'No Discount Define');

define('LINK_ADD_CUSTOMER_GROUP', 'Add Customer Group');
define('LINK_ADD_CUSTOMER_AFT_GROUP', 'Add AFT Group Level');

define('ERROR_CUSTOMERS_GROUPS_NAME', 'Please enter a Group Name');
define('ERROR_GROUPS_NAME_USED', 'Error: Group name has already been used!');
define('ERROR_GROUPS_HAS_PAYMENT_SETTING', 'Error: Group is having payment methods surcharge setting!');
define('ERROR_AFT_GROUPS_HAS_CUSTOMER', 'Error: Some Customer is having this AFT Group Level!');

define('ERROR_NO_CATEGORY_SELECTED', 'Please select the category.');
define('ERROR_EMPTY_DISCOUNT', 'No discount is entered.');
define('ERROR_CAT_DISCOUNT_EXISTS', 'Error: The category discount for this customer group has already exists.');
define('SUCCESS_CAT_DISCOUNT_DELETED', 'Success: The category discount setting for this customer group has been successfully deleted.');

define('JS_ERROR_NO_CATEGORY_SELECTED', '* Please select the category.\n');
define('JS_ERROR_EMPTY_DISCOUNT', '* No discount is entered.\n');

define('CUST_GROUP_EMAIL_SUBJECT_DISCOUNT_INSERT', 'Customer Discount Insert Notification');
define('CUST_GROUP_EMAIL_SUBJECT_DISCOUNT_UPDATE', 'Customer Discount Update Notification');
define('CUST_GROUP_EMAIL_SUBJECT_DISCOUNT_DELETE', 'Customer Discount Remove Notification');
?>