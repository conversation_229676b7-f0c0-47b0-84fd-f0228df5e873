<?php
/*
 	$Id: customers_order_activities.php,v 1.3 2008/02/29 09:09:26 leechuan.goh Exp $
	
 	Copyright (c) 2007 Dynamic Podium
	
 	Released under the GNU General Public License
*/


define('HEADER_FORM_CUSTOMERS_ORDER_ACTIVITIES_TITLE', 'Customers Order Activities');
define('TABLE_SECTION_HEADING_COA', '<b>Store Credit statement for %s, %s</b>');

define('ENTRY_COA_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_COA_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_COA_ACTIVITY_CODE', 'Activity Code');
define('ENTRY_COA_PAYMENT_GATEWAY', 'Payment Gateway');
define('ENTRY_COA_ORDER_SITE', 'Order Site');
define('ENTRY_COA_ORDER_SITE_PULLDOWN_DEFAULT', 'Select Order Site');

define('TABLE_HEADING_COA_DATETIME', 'Date/Time');
define('TABLE_HEADING_COA_ACTIVITY_CODE', 'Code');
define('TABLE_HEADING_COA_ORDER_ID', 'Order ID');
define('TABLE_HEADING_COA_ORDER_STATUS', 'Order Status');
define('TABLE_HEADING_COA_ENTITY', 'Entity');
define('TABLE_HEADING_COA_CATEGORY_PATH', 'Category Path');
define('TABLE_HEADING_COA_PAYMENT_GATEWAY', 'Payment Gateway');
define('TABLE_HEADING_COA_QUANTITY', 'Qty');
define('TABLE_HEADING_COA_AMOUNT', 'Value($)');
define('TABLE_HEADING_COA_CURRENCY', 'Currency');
define('TABLE_HEADING_COA_CUSTOMERS_NAME', 'Customer Name');
define('TABLE_HEADING_COA_ORDER_AMOUNT_BREAKDOWN', 'Order Amount Breakdown');
define('TABLE_HEADING_COA_PG', 'PG Amount');
define('TABLE_HEADING_COA_SC', 'SC');
define('TABLE_HEADING_COA_SC_R', 'RSC');
define('TABLE_HEADING_COA_SC_NR', 'NRSC');
define('TABLE_HEADING_COA_TOTAL_PG', 'Total PG Amount');
define('TABLE_HEADING_COA_TOTAL_SC', 'Total SC');
define('TABLE_HEADING_COA_TOTAL_SC_R', 'Total RSC');
define('TABLE_HEADING_COA_TOTAL_SC_NR', 'Total NRSC');

define('TEXT_COA_NOT_APPLICABLE', '-');
define('TEXT_COA_MANUAL_ACTIVITY_SHOW', 'Show Customer');
define('TEXT_COA_MANUAL_ACTIVITY_HIDE', 'Not Show Customer');

// JavaScript Message
define('JS_ERROR_COA_SEARCH_CRITERIA_NOT_ENTERED', 'Please kindly enter at least one of these search criteria: \n\n1. Start Date and End Date (Mandatory) \n2. Activity Code \n3. Payment Gateway');
?>