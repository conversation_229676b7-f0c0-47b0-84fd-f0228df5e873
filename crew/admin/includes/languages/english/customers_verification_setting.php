<?php
define('HEADING_TITLE', 'Customers Verification Setting');
define('HEADING_ADD_MENU', 'Add Customers Verification Setting');
define('HEADING_EDIT_MENU', 'Edit Customers Verification Setting');

define('ENTRY_COUNTRY', 'Country: ');
define('ENTRY_VERIFICATION_TYPE', 'Verification Type: ');

define('TEXT_DISPLAY_NUMBER_OF_RESULT', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> result)');
define('TEXT_ALL_COUNTRY', 'All Country');

define('SUB_TABLE_HEADING_ACTION', 'Action');
define('SUB_TABLE_HEADING_COUNTRY', 'Country');
define('SUB_TABLE_HEADING_VERIFICATION_TYPE', 'Verification Type');
define('SUB_TABLE_HEADING_EMAIL', 'Email');
define('SUB_TABLE_HEADING_TELEPHONE', 'Product Type');
define('SUB_TABLE_HEADING_IDENTIFICATION', 'Product Type');

define('LINK_ADD_SETTING', 'Add Setting');

define('LIST_COUNTRY_DEFAULT', '-- Select Country --');

define('ERROR_SETTING_EXIST', 'Error: Customers Verification Setting for "%s" already exist.');
define('ERROR_REQUIRED_ENTRY_EMPTY', 'Error: Required entry is empty.');
?>