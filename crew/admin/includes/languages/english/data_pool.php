<?
/*
  	$Id: data_pool.php,v 1.7 2006/10/30 02:11:59 chan Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
if(strtolower($_REQUEST['action'])=="bracket_group" || strpos(strtolower($_REQUEST['action']),"bracket_group"))
define('HEADING_TITLE', 'Bracket Group');
else
define('HEADING_TITLE', 'Template');

define('TEXT_DATA_POOL_INSERT', 'Insert Level');
define('TEXT_INSERT_TEMPLATE', 'Insert Template');
define('TEXT_EDIT_TEMPLATE', 'Edit Template');
define('TEXT_TEMPLATE_NAME', 'Template Name');
define('TEXT_TEMPLATE_DESCRIPTION', 'Template Description');
define('TEXT_PRODUCT_TYPE', 'Product Type');
define('TEXT_NODES', 'Nodes');
define('TEXT_PATH', 'Path');

/************************************************************
	Bracket Page
************************************************************/
define('TEXT_STARTING_LEVEL', 'Starting Level: ');
define('TEXT_STARTING_LEVEL_ALIAS', 'Starting Level Alias: ');
define('TEXT_LEVEL', 'Level');
define('TEXT_BASE_TIME', 'Base Time (hours): ');
define('TEXT_INTERVAL', 'Interval (hours)');
define('TEXT_BASE_PRICE', 'Base Price: ');
define('TEXT_MIN_TIME', 'Minimum Time (hours): ');
define('TEXT_VALUE', 'Value(US$)');
define('TEXT_LEVEL_LABEL', 'Level Label: ');
define('DEFAULT_LEVEL_TEXT', 'Level');
define('TEXT_CLASS_LABEL', 'Class Label: ');
define('DEFAULT_CLASS_TEXT', 'Class');

define('BRACKET_LABEL_ALIAS', 'Alias');

define('TEXT_ADD_EDIT_BRACKETS', 'Add/Edit Brackets');
define('TEXT_EDIT', 'Edit');
define('TEXT_ADD_NEW_SELECTION', '[Add New Selection]');
define('TEXT_REMOVE', '');
define('TEXT_MODIFY', 'Modify');
define('TEXT_SORT_ORDER', 'Sort Order');

define('TEXT_INCLUDE_SUBCATEGORY', 'Include Subcategories');
define('TEXT_SELECTION_GROUP_NAME', 'Selection Group Name');
define('TEXT_SELECTION_GROUP_DESCRIPTION', 'Selection Group Description');
define('TEXT_SELECTION_GROUP_INPUT_TYPE', 'Selection Group Input Type');
define('TEXT_EDIT_BRACKET_GROUP', 'Edit Bracket Group');

define('TEXT_DATA_POOL_BRACKETS_ADD', 'Add Brackets');
define('TEXT_DATA_POOL_BRACKETS_EDIT', 'Edit Brackets');
define('TEXT_FROM_NEW_TEMPLATE', '[New from Scratch]');
define('TEXT_DUPLICATE_FROM', 'Duplicate from ');
define('TEXT_DATA_SORT_ORDER', 'Sort Order');
define('TEXT_DATA_POOL', 'Data Pool');
define('TEXT_DATA_POOL_EDIT', 'Edit');
define('TEXT_LEVEL_VALUE', 'Level Value (US$)');
define('TEXT_NEW_TEMPLATE', 'New Template');
define('TEXT_NEW_DATA_POOL', 'New Data Pool');
define('TEXT_LEVEL_NAME', 'Level Name');
define('TEXT_TOTAL_CATEGORIES', 'Total Categories: ');
define('TEXT_ACTION', 'Action');
define('TEXT_DELETE_ALL', 'Delete All');
define('TEXT_DELETE', 'Delete');
define('TEXT_EDIT_DATAPOOL', 'Edit Data Pool');
define('TEXT_DATAPOOL_NAME', 'Datapool Name');
define('TEXT_DATAPOOL_DESCRIPTION', 'Description');
define('TEXT_DATAPOOL_CLASS', 'Class');
define('TEXT_DATAPOOL_TYPE', 'Type');
define('TEXT_RADIO', 'Radio Button');
define('TEXT_SELECT', 'Dropdown Menu');
define('TEXT_INSERT_DATAPOOL', 'Insert Datapool');
define('TEXT_ADD_BRACKET', 'Add Bracket');
define('TEXT_EDIT_SUB_ITEM', 'Edit Datapool Item');
define('TEXT_SUBITEM_NAME', 'Item Name');
define('TEXT_ERROR_DUPLICATE_CLASS_OR_NAME', 'Internal or class name already exists.');
define('TEXT_ERROR', 'Error');
define('TEXT_DATAPOOL_INTERNAL_NAME_2', 'Description');
define('TEXT_NEW_BRACKET_GROUP', 'New Bracket Group');
define('TEXT_NEW_BRACKET', 'New Bracket');
define('TEXT_EDIT_BRACKET', 'Edit Bracket');
define('TEXT_INSERT_NEW_BRACKET_GROUP', 'New Bracket Group');
define('TEXT_BRACKET_GROUP_NAME', 'Group Name');
define('TEXT_BRACKET_GROUP_SORT_ORDER', 'Sort Order');
define('TEXT_BRACKET_GROUP_START_LEVEL', 'Start Level');
define('TEXT_BRACKET_GROUP_DESCRIPTION', 'Description');

/************************************************************
	Subselections section
************************************************************/
define('TEXT_SELECTION', 'Selection');
define('TEXT_OPTION', 'Options');
define('TEXT_ADD_NEW_OPTION', 'Add New Option');
define('TEXT_MIN_LEVEL', 'Min. Level');
define('TEXT_MAX_LEVEL', 'Max. Level');
define('TEXT_OPTION_PRICE', 'Price');
define('TEXT_OPTION_ETA', 'Time');
define('TEXT_REMOVE_OPTION', 'Remove');
define('LINK_REMOVE_OPTION', '[remove]');

define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_BRACKET', 'Bracket(%s)');
define('TABLE_HEADING_TEMPLATE_NAME', 'Template Name');
define('TABLE_HEADING_TEMPLATE_DESCRIPTION', 'Template Description');
define('TABLE_HEADING_TEMPLATE_TYPE', 'Type');
define('TABLE_HEADING_TEMPLATE_OPTIONS', 'Options');
define('TABLE_HEADING_OPTIONS_TITLE', 'Title');
define('TABLE_HEADING_OPTIONS_INPUT_TYPE', 'Input Type');
define('TABLE_HEADING_OPTIONS_SORT_ORDER', 'Sort Order');

define('TABLE_HEADING_DATAPOOL_NAME', 'Data Pool Name');
define('TABLE_HEADING_BASE', 'Base');
define('TABLE_HEADING_DATAPOOL_TYPE', 'Input Type');
define('TABLE_HEADING_BRACKET_ACTION', 'Action');
define('TABLE_HEADING_BRACKET_END_LEVEL', 'Level');
define('TABLE_HEADING_BRACKET_VALUE', 'Value');
define('TABLE_HEADING_BRACKET_INTERVAL', 'Interval');

define('TABLE_HEADING_BRACKET_GROUP_NAME', 'Bracket Group Name');
define('TABLE_HEADING_BRACKET_GROUP_SORT_ORDER', 'Sort Order');
define('TABLE_HEADING_BRACKET_GROUP_DESCRIPTION', 'Description');
define('TABLE_HEADING_BRACKET_GROUP_BRACKETS', 'Brackets');
define('TABLE_HEADING_BRACKET_GROUP_START_LEVEL', 'Bracket Start Level');

define('TEXT_EDIT_TEMPLATE_INFO', 'Edit Template');

define('ERROR_LESS_THAN_START_LEVEL', 'Level must be greater than the starting level.');
define('ERROR_INVALID_INTERVAL_VALUE', 'Time must has value greater than zero.');
define('ERROR_INVALID_PRICE_VALUE', 'Price must has value greater than zero.');
define('ERROR_TEMPLATE_NOT_EXISTS', 'The provided template does not exists.');
define('WARNING_NO_MATCH_PRODUCT_TO_APPLY', 'There is no any matched products of the linked categories for this template to apply on it.');
define('WARNING_INVALID_INTERVAL_VALUE', 'Time has the value less than or equal zero.');
define('WARNING_INVALID_PRICE_VALUE', 'Price has the value less than or equal zero.');
define('SUCCESS_APPLY_TEMPLATE', 'This template had been successfully applied to %d matched product(s).');

/*****************************************************************************************
	Data Pool Options Section
*****************************************************************************************/
define('SUBTITLE_ADD_OPTIONS', 'Add Option');
define('ENTRY_OPTIONS_TITLE', 'Option Title');
define('ENTRY_OPTIONS_CLASS', 'Option Class');
define('ENTRY_OPTION_FIELD_TYPE', 'Input Type');
define('ENTRY_OPTION_SETTING', 'Setting');
define('ENTRY_OPTION_MANDATORY', 'Mandatory Option');
define('ENTRY_OPTION_SHOW_SUPPLIER', 'Show to Supplier');
define('ENTRY_OPTION_ORDER', 'Sort Order');
define('SYSTEM_PRICE_ETA_OPTION_TITLE', 'Price and ETA');

define('KEY_PL_START_LEVEL', "pl_start_level");
define('KEY_PL_START_LEVEL_ALIAS', "pl_start_level_alias");
define('KEY_PL_END_LEVEL', "pl_end_level");
define('KEY_PL_LEVEL_ALIAS', "pl_level_alias");
define('KEY_PL_VALUE', "pl_value");
define('KEY_PL_INTERVAL', "pl_interval");
define('KEY_PL_BRACKET', "pl_bracket");
define('KEY_PL_BASE_TIME', "pl_base_time");
define('KEY_PL_BASE_PRICE', "pl_base_price");
define('KEY_PL_MIN_TIME', "pl_min_time");
define('KEY_PL_LEVEL_LABEL', "pl_level_label");
define('KEY_PL_CLASS_LABEL', "pl_class_label");
?>
