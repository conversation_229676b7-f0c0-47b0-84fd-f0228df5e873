<?php  
define('HEADING_TITLE', 'Define Games Landing Page');
define('ENTRY_GAMES', 'Select Games');
define('ENTRY_LANGUAGE', 'Language');
define('ENTRY_TEMPLATE', 'Template');
define('ENTRY_COPY_FROM_LANGUAGE', 'Copy From Language');
define('ENTRY_USING_OWN_LANGUAGE', 'Using Own Setting');
define('ENTRY_COMPANY', 'Company');

//Game Features
define('SECTION_TITLE_GAME_FEATURES', 'Game Features');
define('ENTRY_DESCRIPTION', 'Description');
define('ENTRY_SUB_DESCRIPTION', 'Sub-Description');

//Game Details
define('SECTION_TITLE_GAME_DETAILS', 'Game Details');
define('ENTRY_PUBLISHER', 'Publisher');
define('ENTRY_DEVELOPER', 'Developer');
define('ENTRY_PLATFORM', 'Platform');
define('ENTRY_CATEGORY', 'Genre');					//define('ENTRY_CATEGORY', 'Category');
define('ENTRY_MODEL', 'Model');
define('ENTRY_REGION', 'Region');
define('ENTRY_DEFAULT', 'Default');
define('ENTRY_STATUS', 'Status');
define('ENTRY_INTERFACE', 'Interface');
define('ENTRY_CLIENT_TYPE', 'Client Type');
define('ENTRY_TIME_KEEPING_SYSTEM', 'Time-keeping System');
define('ENTRY_YEAR', 'Year');
define('ENTRY_EXPANSIONS', 'Expansions');
define('ENTRY_PLAYER_RATING', 'Player Rating');
define('ENTRY_RESET', 'Reset');
define('ENTRY_LAUNCHING_DATE', 'Launching Date');
define('ENTRY_WEBSITE', 'Official Website');		//define('ENTRY_WEBSITE', 'Website');
define('ENTRY_GAME_ESRB', 'Game ESRB');
define('ENTRY_KEYWORD', 'Keyword');
define('TEXT_SEPARATE_BY_COMMA', 'Separate each keyword with \',\'');
define('ENTRY_DOWNLOAD_CLIENT', 'Download Client URL');
define('ENTRY_SIGNUP_ACCOUNT', 'Sign Up Account URL');
define('ENTRY_GAME_PUBLISHER', 'Game Publisher');
define('ENTRY_SUPPORTED_TITLES', 'Supported Titles');

//News / Events
define('SECTION_TITLE_NEWS_EVENTS', 'News/Events');
define('ENTRY_MORE_NEWS_EVENTS', 'More News/Events URL');
define('ENTRY_NEWS_EVENTS_IMAGE_SOURCE', 'Image Source URL');
define('ENTRY_NEWS_EVENTS_DESCRIPTION', 'Link Description');
define('ENTRY_NEWS_EVENTS_LINK', 'Link URL');
define('ENTRY_NEWS_EVENTS_DATE', 'Date');

//SPECIFICATIONS
define('SECTION_TITLE_SPECIFICATIONS', 'Specifications');
define('ENTRY_MINIMUM', 'Minimum');
define('ENTRY_RECOMMENDED', 'Recommended');
define('SECTION_TITLE_BACKGROUND_SETTING', 'Background Setting');
define('SECTION_TITLE_EXTRA_SETTING', 'Extra Setting');
define('ENTRY_HEADER_IMAGE', 'Header Background Image URL');
define('ENTRY_FOOTER_IMAGE', 'Footer Background Image URL');
define('ENTRY_BACKGROUND_COLOR', 'Background Color');
define('ENTRY_AF_NOTICE_ENABLE_OPTION', 'Anti Fraud Notice Option');

//GAME PUBLISHER TEMPLATE
//GAME_LIST
define('SECTION_TITLE_GAME_LIST', 'GAME LIST');
define('SECTION_TITLE_GAME_PUBLISHER_PROFILE', 'GAME PUBLISHER PROFILE');
define('ENTRY_COMPANY_NAME', 'Company Name');
define('ENTRY_COMPANY_WEBSITE', 'Company Website');

?>