<?php  
define('HEADING_TITLE', 'Define Main Page');
define('ENTRY_MAINPAGE_CONTENT', 'Main Page Content');

// SLIDER CONTENT
define('HEADER_SLIDER_CONTENT', 'Slider Content');
define('ENTRY_SLIDER_THUMBNAIL_IMAGE', 'Slider Thumbnail Image');
define('ENTRY_SLIDER_BACKGROUND_IMAGE', 'Slider Background Image');
define('ENTRY_SLIDER_IMAGE_URL', 'Slider Image URL');
define('ENTRY_SLIDER_CAPTION', 'Slider Caption');
define('ENTRY_DELETE_SLIDER', 'Delete Slider');

// BEST SELLING PRODUCTS CONTENT COLUMNS
define('HEADER_BESTSELLING_CONTENT_COLUMNS', 'Best Selling Products Content Columns');
define('HEADER_COLUMNS', 'Column');
define('HEADER_ALL_PAYMENT_COLUMNS' ,'All Payment Image Columns');


define('ENTRY_ZONES', 'Zones');
define('ENTRY_CATEGORY_ID', 'Category ID');
define('ENTRY_DESC', 'Description');
define('ENTRY_LANGUAGE', 'Language'); 
define('ENTRY_IMAGE_SOURCE', 'Image Source');

// TAB CONTENT
define('HEADER_TAB_CONTENT_COLUMNS', 'TAB Content Columns');
define('HEADER_TAB', 'Tab');

define('ENTRY_TAB_NAME', 'TAB Name');
define('ENTRY_TAB_HREF', 'TAB URL');
define('ENTRY_TAB_IMAGE_SOURCE', 'Image Source');
define('ENTRY_TAB_IMAGE_HREF', 'Image URL');
define('ENTRY_TAB_TITLE', 'Title');

?>