<?php

define('TABLE_HEADING_DTU_PAYMENT_INFO', 'Payment Information');
define('TABLE_HEADING_DTU_SUPPLIER_INFO', 'Supplier Information');
define('TABLE_HEADING_DTU_PRODUCT_DETAILS', 'DTU Product Details');
define('TABLE_HEADING_DTU_CB_PRODUCT_DETAILS', 'DTU Charge Back Product Details ( - )');
define('TABLE_HEADING_DTU_DB_PRODUCT_DETAILS', 'DTU Debit Note Product Details ( - )');

define('ENTRY_SEARCH_DTU_ID', 'DTU Request Number');

define('ENTRY_DTU_FORM_CONTACT_PERSON', 'Contact Person');
define('ENTRY_DTU_FORM_CURRENCY', 'Currency');
define('ENTRY_DTU_FORM_CURRENCY_CONFIRM_RATE', 'Confirm Rate');
define('ENTRY_DTU_FORM_CURRENCY_SUGGEST_RATE', 'Suggested Rate');
define('ENTRY_DTU_FORM_DISBURSEMENT_METHOD', 'Disbursement Method');
define('ENTRY_DTU_FORM_START_DATE', 'Start Date');
define('ENTRY_DTU_FORM_END_DATE', 'End Date');
define('ENTRY_DTU_FORM_ISSUE_DATE', 'DTU Request Date');
define('ENTRY_DTU_FORM_PAYMENT_TERM', 'Payment Term');
define('ENTRY_DTU_FORM_BILLING_STATUS', 'Billing Status');
define('ENTRY_DTU_FORM_WITHDRAW_STATUS', 'Withdraw Status');
define('ENTRY_DTU_FORM_PAYMENT_TRANS', 'Payment Transaction(s)');
define('ENTRY_DTU_FORM_DTU_HISTORY', 'DTU Request History');
define('ENTRY_DTU_FORM_DTU_REF_ID', 'DTU Request Number');
define('ENTRY_DTU_FORM_DTU_STATUS', 'DTU Request Status');
define('ENTRY_DTU_FORM_DTU_STATISTICS', 'DTU Request Statistics');
define('ENTRY_DTU_FORM_SUGGESTED_SUPPLIER', 'Suggested Supplier/Publisher');
define('ENTRY_DTU_FORM_SUPPLIER', 'Supplier');
define('ENTRY_DTU_FORM_SUPPLIER_ADDRESS', 'Supplier Address');
define('ENTRY_DTU_FORM_SUPPLIER_PAYMENT_TERM', 'Payment Term');
define('ENTRY_DTU_FORM_DELIVERY_ADDRESS', 'Delivery Address');

// DTU Report form
define('ENTRY_DTU_FORM_START_DATE_REPORT', 'Start Date');
define('ENTRY_DTU_FORM_END_DATE_REPORT', 'End Date');
define('ENTRY_DTU_FORM_REPORT_PRODUCT_ID', 'Product ID');
define('ENTRY_DTU_FORM_REPORT_ORDER_NUMBER', 'Order Number');
define('ENTRY_DTU_FORM_REPORT_TOPUP_ID', 'Top-up ID');

// label for create new DTU form
define('TABLE_HEADING_DTU_SELECT', 'Select');
define('TABLE_HEADING_DTU_DATE', 'Top Up Date');
define('TABLE_HEADING_DTU_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_DTU_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_DTU_PUBLISHER_NAME', 'Publisher');
define('TABLE_HEADING_DTU_ORDER_NO', 'Order Number');
define('TABLE_HEADING_DTU_TOP_UP_ID', 'Top-up ID');
define('TABLE_HEADING_DTU_QUANTITY', 'Quantity');
define('TABLE_HEADING_DTU_SRP', 'Selling Price<br>%s');
define('TABLE_HEADING_DTU_AMOUNT', 'Subtotal<br>%s');
define('TABLE_HEADING_DTU_BILLING_STATUS', 'DTU Payment Status');
define('TABLE_HEADING_DTU_SORT_UP', '&nbsp;&uarr;');
define('TABLE_HEADING_DTU_SORT_DOWN', '&nbsp;&darr;');

// heading for PO items in new PO form
define('TABLE_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_PRODUCT_AVAIL_QTY', 'Available Qty');
define('TABLE_HEADING_PRODUCT_ACTUAL_QTY', 'Actual Qty');
define('TABLE_HEADING_MULTIPLIER', 'Multiplier');
define('TABLE_HEADING_LAST_N_DAYS', 'Last N Days');
define('TABLE_HEADING_SUGGEST_RESTOCK_AMOUNT', 'Suggested Restock Amount');
define('TABLE_HEADING_SELLING_PRICE', "Selling Price<br>%s");
define('TABLE_HEADING_STOCK_PRICE', "Unit Price<br>%s");
define('TABLE_HEADING_SUBTOTAL', "Subtotal<br>%s");
define('TABLE_HEADING_TOTAL', "Total");
define('TABLE_HEADING_BOTTOM_SUBTOTAL', "Sub-Total");
define('TABLE_HEADING_BOTTOM_CHARGE_BACK', "Charge Back");
define('TABLE_HEADING_BOTTOM_DEBIT_NOTE', "Debit Note");
define('TABLE_HEADING_BOTTOM_TOTAL_AFTER_CHARGE_BACK', "Total After Charge Back");
define('TABLE_HEADING_BOTTOM_GST', "Include GST %s&percnt;");
define('TABLE_HEADING_BOTTOM_TOTAL_AFTER_TAX', "Total After Tax");
define('TABLE_HEADING_BOTTOM_TOTAL_AFTER_REBATE', "Total After Rebate");
define('TABLE_HEADING_BOTTOM_BANK_CHARGES', "Include Overseas Bank Charges");
define('TABLE_HEADING_BOTTOM_ADJUSTMENT', 'Include Adjustment');
define('TABLE_HEADING_BOTTOM_TOTAL_DTU_SELECT', 'Total DTU Amount');
define('TABLE_HEADING_BOTTOM_TOTAL_PAYABLE_AMOUNT', "Total Payable Amount");
define('TABLE_HEADING_BOTTOM_AFTER_CONVERSION_RATE', "After Conversion Rate");

// heading for DTU items in Pending status
define('TABLE_HEADING_DTU_PENDING', "DTU Payment In Pending Processing");
define('TABLE_HEADING_PRODUCT_AVAIL_STOCK', 'Available Stock');
define('TABLE_HEADING_PRODUCT_ACTUAL_STOCK', 'Actual Stock');
define('TABLE_HEADING_REQUEST_QTY', 'Request Qty');
define('TABLE_HEADING_RECEIVED_QTY', 'RECV');
define('TABLE_HEADING_PENDING_RECEIVE_QTY', 'Pending RECV Qty');

// heading for DTU Request Statistic table
define('STAT_TABLE_HEADING_DTU_STATUS', '&nbsp;');
define('STAT_TABLE_HEADING_TOTAL_DTU', 'Total #');
define('STAT_TABLE_HEADING_TOTAL_AMOUNT', 'Total %s');
define('STAT_TABLE_ENTRY_DTU_1_DAY', 'DTU Request in 1 day');
define('STAT_TABLE_ENTRY_TOTAL_DTU', 'Total DTU Request');

// heading for DTU PO Status History table
define('STATUS_TABLE_HEADING_DATE_ADDED', 'Date Added');
define('STATUS_TABLE_HEADING_NOTIFY_SUPPLIER', 'Show to Supplier');
define('STATUS_TABLE_HEADING_STATUS', 'Status');
define('STATUS_TABLE_HEADING_COMMENTS', 'Comments');
define('STATUS_TABLE_HEADING_MODIFIED_BY', 'Changed By');
define('STATUS_TABLE_HEADING_ACTION', 'Action');

// label for DTU Listing search criteria
define('ENTRY_HEADING_DTU_LIST_START_DATE', 'Start Date (YYYY-MM-DD /YYYY-MM-DD HH:MM)');
define('ENTRY_HEADING_DTU_LIST_END_DATE', 'End Date (YYYY-MM-DD /YYYY-MM-DD HH:MM)');
define('ENTRY_HEADING_DTU_LIST_DTU_NO', 'DTU Request Number (Formatted Version)');
define('ENTRY_HEADING_DTU_LIST_DTU_STATUS', 'DTU Request Status');
define('ENTRY_HEADING_DTU_LIST_PAYMENT_STATUS', 'Payment Transaction Status');
define('ENTRY_HEADING_RECORDS_PER_PAGE', 'Records per page');

// heading for DTU List show DTU details table
define('TABLE_HEADING_DTU_LIST_DTU_NO', "DTU PO No.");
define('TABLE_HEADING_DTU_LIST_TERM', "Term");
define('TABLE_HEADING_DTU_LIST_DELIVERY_STATUS', "Delivery Status");
define('TABLE_HEADING_DTU_LIST_TAG', "Tag");
define('TABLE_HEADING_DTU_LIST_SUPPLIER', "Suplier");
define('TABLE_HEADING_DTU_LIST_PAYMENT_STATUS', "Payment Status");
define('TABLE_HEADING_DTU_LIST_DATE', "Date");
define('TABLE_HEADING_DTU_LIST_TOTAL_PURCHASED', "Total Purchased");
define('TABLE_HEADING_DTU_LIST_TOTAL_UPLOADED', "Total Uploaded");
define('TABLE_HEADING_PO_LIST_BILLING_STATUS', "Billing Status");
define('TABLE_HEADING_DTU_LIST_VERIFIED', "Verified?");
define('TABLE_HEADING_DTU_LIST_LOCKED', "Locked");
define('TABLE_HEADING_DTU_LIST_ACTION', "Action");

// table heading for DTU List show DTU details
define('TABLE_HEADING_PRODUCTS', "Product Name");
define('TABLE_HEADING_STOCK_QUANTITY', "Actual Stock");
define('TABLE_HEADING_QUANTITY', "Ordered Qty");
define('TABLE_HEADING_PREV_RECEIVED', "Received Qty");
define('TABLE_HEADING_BALANCE', "Pending Delivery");
define('TABLE_HEADING_PRICE', "Unit Price");
define('TABLE_HEADING_TOTAL_EXCLUDING_TAX', "Amount (ex)");
define('TABLE_HEADING_TOTAL_INCLUDING_TAX', "Total (inc)");

// DTU report tabs
define('TAB_DEFAULT_TEXT', "Please select supplier!");

// heading for product last purchase cost history
define('TABLE_HEADING_COT_PRICE_PO_NO', "PO No.");
define('TABLE_HEADING_COT_PRICE_PO_DATE', "Date");
define('TABLE_HEADING_COT_PRICE_SUPPLIER', "Previous Supplier");
define('TABLE_HEADING_COT_PRICE_UNIT_COST', "Unit Cost Price");
define('TABLE_HEADING_COT_PRICE_PO_STATUS', "Status");
define('TABLE_HEADING_COT_PRICE_PO_ISSUE_BY', "Issue By");

// heading for printable DTU Request
define('TABLE_HEADING_DTU_PRINT_NO', "No.");
define('TABLE_HEADING_DTU_PRINT_PRODUCT_ID', "Product ID");
define('TABLE_HEADING_DTU_PRINT_PARTICULARS', "Particulars");
define('TABLE_HEADING_DTU_PRINT_QUANTITY', "Quantity");
define('TABLE_HEADING_DTU_PRINT_UNIT_PRICE', "Unit Price");
define('TABLE_HEADING_DTU_PRINT_DISCOUNT', "Discount");
define('TABLE_HEADING_DTU_PRINT_TOTAL', "Total");

// label text
define('TEXT_SUPPLIER_PRE_PAYMENT', 'Pre-Payment');
define('TEXT_SUPPLIER_DTU_PAYMENT', 'DTU Payment');
define('TEXT_SUPPLIER_DAY_TERM', 'day-Term');
define('TEXT_SUPPLIER_CONSIGNMENT', 'Consignment');
define('TEXT_PAYMENT_BILLED', 'Billed');
define('TEXT_PAYMENT_PENDING_BILLING', 'Pending Billing');
define('TEXT_PAYMENT_PENDING_WITHDRAW', 'Pending Withdraw');
define('TEXT_PAYMENT_FULLY_WITHDRAW', 'Fully Withdraw');
define('TEXT_PAYMENT_PARTIAL_WITHDRAW', 'Partial Withdraw');
define('TEXT_PAYMENT_PAID', 'Paid');
define('TEXT_PAYMENT_PENDING_PAYMENT', 'Pending Payment');
define('TEXT_PAYMENT_NO_PAYMENT', 'No Payment Required');
define('TEXT_DTU_ANY', '<i>Any</i>');
define('TEXT_NOTIFY_SUPPLIER', 'Show to Supplier');
define('TEXT_SET_AS_REMARK', 'Set as Important Remark');
define('TEXT_NO_PO_HISTORY', 'No Purchase Order History Available');
define('TEXT_PRICE_UP', "Cost Price Higher Than Previous Purchase");
define('TEXT_PRICE_EQUAL', "Cost Price Equal To Previous Purchase");
define('TEXT_PRICE_DOWN', "Cost Price Lower Than Previous Purchase");
define('TEXT_NO_PRICE', "No Previous Purchase Price");
define('TEXT_REMARK_MODIFIED_BY', 'Last changed by ');
define('TEXT_VERIFIED', ' <b>[Verified]</b>');
define('TEXT_UNVERIFIED', ' <b>[Unverified]</b>');
define('TEXT_DELIVERED_AMOUNT', 'Delivered Amount');
define('TEXT_REMAINING_AMOUNT', 'Remaining Amount');
define('TEXT_REFUNDED_BANKCHARGES', 'Refunded Bank Charges');
define('TEXT_PRODUCTS_ID', 'Product ID:');

// button text
define('IMAGE_BUTTON_ADD_ITEM', 'Add Item');
define('IMAGE_BUTTON_RESET', 'Reset');
define('BUTTON_SEARCH_DTU', 'Search DTU');
define('BUTTON_SEARCH_DTU_RESET','Reset DTU List');
define('BUTTON_STATUS_DTU', 'Preview DTU');
define('BUTTON_CALCULATE_DTU', 'Calculate DTU');
define('BUTTON_PREVIEW_DTU', 'Preview DTU');
define('BUTTON_GENERATE_DTU', 'Generate DTU');
define('BUTTON_SET_CB_DTU', 'Process');
define('BUTTON_ADD_REMARK', 'Add Remark');
define('BUTTON_COMPLETE', 'Manual Complete');
define('BUTTON_ROLLBACK_TO_PROCESS', 'Rollback to Process');
define('BUTTON_CANCEL_PENDING_RECEIVE', 'Cancel DTU Request');
define('BUTTON_VERIFY', 'Mark as Verified');
define('BUTTON_UNVERIFY', 'Mark as Unverified');
define('BUTTON_MAKE_PAYMENT', 'Credit to WSC');
define('BUTTON_DEBIT_PAYMENT', 'Debit from WSC');
define('BUTTON_LOCK', 'Lock');
define('BUTTON_UNLOCK', 'Unlock');
define('ALT_BUTTON_SEARCH_DTU', 'Search DTU');
define('ALT_BUTTON_SEARCH_DTU_RESET', 'Reset DTU List');
define('ALT_BUTTON_STATUS_DTU', 'Preview DTU');
define('ALT_BUTTON_CALCULATE_DTU', 'Calculate DTU');
define('ALT_BUTTON_PREVIEW_DTU', ' Preview DTU ');
define('ALT_BUTTON_GENERATE_DTU', ' Generate DTU ');
define('ALT_BUTTON_SET_CB_DTU', ' Generate CB ');
define('ALT_BUTTON_ADD_REMARK', ' Add Remark ');
define('ALT_BUTTON_COMPLETE', ' Manual Complete ');
define('ALT_ROLLBACK_TO_PROCESS', ' Rollback to Process ');
define('ALT_BUTTON_CANCEL_PENDING_RECEIVE', ' Cancel Pending Receive ');
define('ALT_BUTTON_VERIFY', ' Mark as Verified ');
define('ALT_BUTTON_UNVERIFY', ' Mark as Unverified ');
define('ALT_BUTTON_MAKE_PAYMENT', ' Credit to WSC ');
define('ALT_BUTTON_DEBIT_PAYMENT', ' Debit from WSC ');
define('ALT_BUTTON_LOCK', 'Lock');
define('ALT_BUTTON_UNLOCK', 'Unlock');

// Status Remark
define('HEADER_ADD_REMARK', 'Add Remark');
define('TEXT_SET_IMPORTANT_REMARK', 'Set as Important Remark');

// text for PO locking in various stage
define('TEXT_ORDER_NOT_BEEN_LOCKED', '<span class="redIndicator">This Purchase Order has not been locked. Product Location information is hidden to prevent double-delivery of Products. Click the "LOCK" button on your right if you would like to edit this Purchase Order.</span>');
define('TEXT_LOCKED_ORDER_SEEN_BY_OWNER', 'This Purchase Order has been locked by you on %s at %s. If you are not going to edit this Purchase Order or if you have finished your Partial Delivery, please remember to click "UNLOCK" button on your right so that others may lock it.');
define('TEXT_ORDER_LOCKED_BY_OTHER', '<span class="redIndicator">This Purchase Order has been locked by %s on %s at %s. Product Location information is hidden to prevent double delivery of Products. You cannot edit this Purchase Order unless you lock the Order. To do so, you must first contact this person or any of the person in the following Admin Goups to unlock the Order. %s</span>');
define('TEXT_UNLOCK_OTHER_PEOPLE_ORDER', '<span class="redIndicator">This Purchase Order has been locked by %s on %s at %s. Product Location information is hidden to prevent double-delivery of Products. You have the Permission to unlock it by clicking the "UNLOCK" button on your right. Please note that unlocking this Order may result in double-delivery of Products.</span>');
define('TEXT_LOCKED_ORDER_NOT_VALID_STATUS', '<span class="redIndicator">This order cannot be locked since the order status is not in "Processing".</span>');
define('TEXT_LOCKED_OUTDATED_ORDER', '<span class="redIndicator">Someone has changed this order before you manage to lock it.</span>');
define('TEXT_DUPLICATE_LOCKED_ORDER_SEEN_BY_OWNER', '<span class="redIndicator">This order has been opened and locked by you on another browser window. This window will close after you click OK.</span>');

define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');
define('ORDERS_LOG_LOCK_ORDER', '##l_1##');

// success or error messages
define('SUCCESS_PO_FORM_ADDED', "Success: Purchase Order, %s, has been successfully added.");
define('SUCCESS_CB_FORM_ADDED', "Success: DTU status successfully changed.");
define('ERROR_PO_LOAD_EMPTY_ID', "Error: Can't retrieve purchase order with empty ID.");
define('ERROR_PO_SEARCH_INVALID_ID', "Error: Can't find purchase order with PO Number = %s.");
define('ERROR_DTU_FORM_LOCKED_SUPPLIER', 'Supplier/Publisher is Locked by other user');
define('ERROR_DTU_FORM_NOT_LOCKED_SUPPLIER', 'Please lock Supplier/Publishers');
define('ERROR_DTU_FORM_EMPTY_DTU_START_DATE', "Error: Please select start date for this DTU Payment Request.");
define('ERROR_DTU_FORM_EMPTY_DTU_END_DATE', "Error: Please select end date for this DTU Payment Request.");
define('ERROR_DTU_FORM_EMPTY_DELIVERY_ADDRESS', "Error: Please select a delivery address for this DTU Payment Request.");
define('ERROR_DTU_FORM_EMPTY_SUPPLIER', "Error: Please select a supplier/publisher for this DTU withdrawal request.");
define('ERROR_DTU_FORM_SUPPLIER_TYPE', "Error: Please select a supplier/publisher which are usign DTU Payment Term.");
define('ERROR_DTU_FORM_EMPTY_PAYMENT_METHOD', "Error: Please select a payment method for this DTU Payment Request.");
define('ERROR_DTU_FORM_EMPTY_CURRENCY', "Error: Please select a currency for this DTU withdrawal request.");
define('ERROR_DTU_FORM_EMPTY_CURRENCY_CONFIRM_RATE', "Error: Please enter a confirm currency rate for this DTU Payment Request.");
define('ERROR_DTU_FORM_EMPTY_PRODUCTS', "Error: Please select DTUs for this DTU Payment Request.");
define('ERROR_DTU_FORM_EMPTY_CB_PRODUCTS', "Error: Only CB DTU are selected.");
define('ERROR_DTU_CB_FORM_EMPTY_PRODUCTS', "Error: Please select DTUs for this DTU CB Form.");
define('ERROR_DTU_FORM_EMPTY_SUGGEST_QUANTITY', "Error: invalid quantity for %s.");
define('ERROR_DTU_FORM_EMPTY_STOCK_PRICE', "Error: Please enter stock price for %s.");
define('ERROR_DTU_FORM_EMPTY_SELECT_STATUS', "Error: Please select status for Top-up ID %s.");

define('ERROR_DTU_FORM_CREATE_PERMISSION', "Error: You are not allow to create new DTU Payment Request.");
define('ERROR_PO_FORM_PO_APPROVE_PERMISSION', "Error: You are not allow to approve/cancel the Purchase Order.");
define('ERROR_DTU_FORM_ADD_REMARK_PERMISSION', "Error: You are not allow to add remark for this DTU Payment Request.");
define('ERROR_DTU_FORM_DTU_CANCEL_PENDING_RECEIVE_PERMISSION', "Error: You are not allow to cancel for DTU Payment Request.");
define('ERROR_PO_FORM_VERIFY_PERMISSION', "Error: You are not allow to set verify/unverify for Purchase Order.");
define('ERROR_DTU_FORM_DTU_MAKE_PAYMENT_PERMISSION', "Error: You are not allow to make payment for this DTU Payment Request.");

define('ERROR_DTU_CANCELLATION_FAILED', "Failed to Cancel the DTU Payment Request! Missing DTU Request ID.");
define('ERROR_DTU_APPROVE_FAILED', "Failed to Approve the DTU Payment Request! Missing DTU Request ID.");
define('ERROR_DTU_COMPLETE_FAILED', "Failed to Complete the purchase order! Missing Purchase Order ID.");
define('ERROR_PO_COMPLETE_FAILED_PENDING_RECEIVE', "Failed to Complete the purchase order! There are items still having pending receive quantity.");
define('ERROR_DTU_ROLLBACK_COMPLETE_FAILED', "Failed to rollback Completed DTU Payment Request! Missing DTU Request ID.");
define('ERROR_DTU_STATUS_CHANGE_FAILED', "Failed to change DTU Payment Request status from %s to %s! Status Change Permission denied.");
define('ERROR_PO_VERIFY_FAILED', "Failed to Verify the purchase order! Missing Purchase Order ID.");
define('ERROR_DTU_ADD_REMARK_FAILED', "Failed to add remark for the DTU Payment Request! Missing DTU Payment Request ID.");

define('SUCCESS_CREDIT_WSC', "Success: WSC has been credited.");
define('SUCCESS_DEBIT_WSC', "Success: WSC has been debited.");
define('ERROR_DTU_CREDIT_WSC_PAYMENT_TYPE_FAILED', "Failed to credit the WSC! Only allow for Pre-payment type purchase order.");
define('ERROR_PO_DEBIT_WSC_PAYMENT_TYPE_FAILED', "Failed to debit the WSC! Only allow for Pre-payment type purchase order.");
define('ERROR_PO_DEBIT_WSC_PAYMENT_FOUND_FAILED', "Failed to debit the WSC! Payment has been made.");
define('ERROR_PO_DEBIT_WSC_FUND_FAILED', "Failed to debit the WSC! Insufficient WSC fund.");

define('ERROR_DTU_CANCEL_PENDING_RECEIVE_FAILED', "Failed to cancel for the DTU Payment Request! Missing DTU Request ID.");
define('ERROR_DTU_CANCEL_PENDING_RECEIVE_PAID_STATUS_FAILED', "Failed to cancel for the DTU Payment Request!");
define('ERROR_PO_CANCEL_PENDING_RECEIVE_PREPAYMENT_STATUS_FAILED', "Failed to cancel pending stock receive for the purchase order! Pre-payment Purchase Order is not allow to perform cancellation. Please Credit WSC if it is haven't done so.");

define('WARNING_TEXT_DTU_FORM_DTU_PARTIAL_PAID', "Please make this Purchase Order fully paid before can refund it by cash.");
?>