<?
if (isset($_REQUEST['subaction'])) {
	if ($_REQUEST['subaction']=='create_po' || $_REQUEST['subaction']=='create_blank_po') {
		define('HEADING_TITLE', 'Purchase Order Form');
	} else if ($_REQUEST['subaction']=='preview_po') {
		define('HEADING_TITLE', 'Preview Purchase Order Form');
	} else if ($_REQUEST['subaction']=='edit_po') {
		define('HEADING_TITLE', 'CDK Purchase Order');
	} else {
		define('HEADING_TITLE', 'CDK Purchase Orders List');
	}
} else {
	define('HEADING_TITLE', 'CDK Purchase Orders List');
}

define('TABLE_HEADING_PO_PAYMENT_INFO', 'Payment Information');
define('TABLE_HEADING_PO_SUPPLIER_INFO', 'Supplier Information');
define('TABLE_HEADING_PO_PRODUCT_DETAILS', 'Product Details');

define('ENTRY_SEARCH_PO_ID', 'PO Number');

// label for create new PO form
define('ENTRY_PO_FORM_CONTACT_PERSON', 'Contact Person');
define('ENTRY_PO_FORM_CREDIT_NOTE', 'Credit Note');
define('ENTRY_PO_FORM_CREDIT_NOTE_AVAIL_AMOUNT', 'Credit Note Available Amount');
define('ENTRY_PO_FORM_CURRENCY', 'Currency');
define('ENTRY_PO_FORM_CURRENCY_CONFIRM_RATE', 'Confirm Rate');
define('ENTRY_PO_FORM_CURRENCY_SUGGEST_RATE', 'Suggested Rate');
define('ENTRY_PO_FORM_DISBURSEMENT_METHOD', 'Disbursement Method');
define('ENTRY_PO_FORM_ISSUE_DATE', 'PO Issue Date');
define('ENTRY_PO_FORM_PAYMENT_TERM', 'Payment Term');
define('ENTRY_PO_FORM_BILLING_STATUS', 'Billing Status');
define('ENTRY_PO_FORM_WITHDRAW_STATUS', 'Withdraw Status');
define('ENTRY_PO_FORM_PAYMENT_TRANS', 'Payment Transaction(s)');
define('ENTRY_PO_FORM_PO_HISTORY', 'PO History');
define('ENTRY_PO_FORM_PO_REF_ID', 'PO Number');
define('ENTRY_PO_FORM_PO_STATUS', 'PO Status');
define('ENTRY_PO_FORM_PO_STATISTICS', 'PO Statistics');
define('ENTRY_PO_FORM_SUGGESTED_SUPPLIER', 'Suggested Supplier');
define('ENTRY_PO_FORM_SUPPLIER', 'Supplier');
define('ENTRY_PO_FORM_SUPPLIER_ADDRESS', 'Supplier Address');
define('ENTRY_PO_FORM_SUPPLIER_PAYMENT_TERM', 'Payment Term');
define('ENTRY_PO_FORM_DELIVERY_ADDRESS', 'Delivery Address');

// heading for PO items in new PO form
define('TABLE_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_PRODUCT_AVAIL_QTY', 'Available Qty');
define('TABLE_HEADING_PRODUCT_ACTUAL_QTY', 'Actual Qty');
define('TABLE_HEADING_MULTIPLIER', 'Multiplier');
define('TABLE_HEADING_LAST_N_DAYS', 'Last N Days');
define('TABLE_HEADING_SUGGEST_RESTOCK_AMOUNT', 'Suggested Restock Amount');
define('TABLE_HEADING_SELLING_PRICE', "Selling Price<br>%s");
define('TABLE_HEADING_STOCK_PRICE', "Unit Price<br>%s");
define('TABLE_HEADING_SUBTOTAL', "Subtotal<br>%s");
define('TABLE_HEADING_TOTAL', "Total");
define('TABLE_HEADING_BOTTOM_SUBTOTAL', "Sub-Total");
define('TABLE_HEADING_BOTTOM_GST', "Include GST %s&percnt;");
define('TABLE_HEADING_BOTTOM_TOTAL_AFTER_TAX', "Total After Tax");
define('TABLE_HEADING_BOTTOM_CREDIT_NOTE', "Credit Note");
// define('TABLE_HEADING_BOTTOM_TOTAL_AFTER_REBATE', "Total After Rebate");
define('TABLE_HEADING_BOTTOM_TOTAL_AFTER_REBATE', "Total");
define('TABLE_HEADING_BOTTOM_BANK_CHARGES', "Include Overseas Bank Charges");
define('TABLE_HEADING_BOTTOM_ADJUSTMENT', 'Include Adjustment');
define('TABLE_HEADING_BOTTOM_TOTAL_PAYABLE_AMOUNT', "Total Payable Amount");
define('TABLE_HEADING_BOTTOM_AFTER_CONVERSION_RATE', "After Conversion Rate");

// heading for PO items in Pending status
define('TABLE_HEADING_PO_PENDING', "PO In Pending Processing");
define('TABLE_HEADING_PRODUCT_AVAIL_STOCK', 'Available Stock');
define('TABLE_HEADING_PRODUCT_ACTUAL_STOCK', 'Actual Stock');
define('TABLE_HEADING_REQUEST_QTY', 'Request Qty');
define('TABLE_HEADING_RECEIVED_QTY', 'RECV');
define('TABLE_HEADING_PENDING_RECEIVE_QTY', 'Pending RECV Qty');

// heading for PO Statistic table
define('STAT_TABLE_HEADING_PO_STATUS', '&nbsp;');
define('STAT_TABLE_HEADING_TOTAL_PO', 'Total #');
define('STAT_TABLE_HEADING_TOTAL_AMOUNT', 'Total %s');
define('STAT_TABLE_ENTRY_PO_1_DAY', 'PO in 1 day');
define('STAT_TABLE_ENTRY_TOTAL_PO', 'Total PO');

// heading for PO Status History table
define('STATUS_TABLE_HEADING_DATE_ADDED', 'Date Added');
define('STATUS_TABLE_HEADING_NOTIFY_SUPPLIER', 'Show to Supplier');
define('STATUS_TABLE_HEADING_STATUS', 'Status');
define('STATUS_TABLE_HEADING_COMMENTS', 'Comments');
define('STATUS_TABLE_HEADING_MODIFIED_BY', 'Changed By');
define('STATUS_TABLE_HEADING_ACTION', 'Action');

// label for PO Listing search criteria
define('ENTRY_HEADING_PO_LIST_START_DATE', 'Start Date (YYYY-MM-DD /YYYY-MM-DD HH:MM)');
define('ENTRY_HEADING_PO_LIST_END_DATE', 'End Date (YYYY-MM-DD /YYYY-MM-DD HH:MM)');
define('ENTRY_HEADING_PO_LIST_PO_NO', 'PO Number (Formatted Version)');
define('ENTRY_HEADING_PO_LIST_PO_STATUS', 'PO Status');
define('ENTRY_HEADING_PO_LIST_PAYMENT_STATUS', 'Payment Transaction Status');
define('ENTRY_HEADING_RECORDS_PER_PAGE', 'Records per page');

// heading for PO List show PO details table
define('TABLE_HEADING_PO_LIST_PO_NO', "PO No.");
define('TABLE_HEADING_PO_LIST_TERM', "Term");
define('TABLE_HEADING_PO_LIST_DELIVERY_STATUS', "Delivery Status");
define('TABLE_HEADING_PO_LIST_TAG', "Tag");
define('TABLE_HEADING_PO_LIST_SUPPLIER', "Suplier");
define('TABLE_HEADING_PO_LIST_PAYMENT_STATUS', "Payment Status");
define('TABLE_HEADING_PO_LIST_DATE', "Date");
define('TABLE_HEADING_PO_LIST_TOTAL_PURCHASED', "Total Purchased");
define('TABLE_HEADING_PO_LIST_TOTAL_UPLOADED', "Total Uploaded");
define('TABLE_HEADING_PO_LIST_BILLING_STATUS', "Billing Status");
define('TABLE_HEADING_PO_LIST_VERIFIED', "Verified?");
define('TABLE_HEADING_PO_LIST_LOCKED', "Locked");
define('TABLE_HEADING_PO_LIST_ACTION', "Action");

// table heading for PO List show PO details
define('TABLE_HEADING_PRODUCTS', "Product Name");
define('TABLE_HEADING_STOCK_QUANTITY', "Actual Stock");
define('TABLE_HEADING_QUANTITY', "Ordered Qty");
define('TABLE_HEADING_PREV_RECEIVED', "Received Qty");
define('TABLE_HEADING_BALANCE', "Pending Delivery");
define('TABLE_HEADING_PRICE', "Unit Price");
define('TABLE_HEADING_TOTAL_EXCLUDING_TAX', "Amount (ex)");
define('TABLE_HEADING_TOTAL_INCLUDING_TAX', "Total (inc)");

// heading for product last purchase cost history
define('TABLE_HEADING_COT_PRICE_PO_NO', "PO No.");
define('TABLE_HEADING_COT_PRICE_PO_DATE', "Date");
define('TABLE_HEADING_COT_PRICE_SUPPLIER', "Previous Supplier");
define('TABLE_HEADING_COT_PRICE_UNIT_COST', "Unit Cost Price");
define('TABLE_HEADING_COT_PRICE_PO_STATUS', "Status");
define('TABLE_HEADING_COT_PRICE_PO_ISSUE_BY', "Issue By");

// heading for printable PO
define('TABLE_HEADING_PO_PRINT_NO', "No.");
define('TABLE_HEADING_PO_PRINT_PRODUCT_ID', "Product ID");
define('TABLE_HEADING_PO_PRINT_PARTICULARS', "Particulars");
define('TABLE_HEADING_PO_PRINT_QUANTITY', "Quantity");
define('TABLE_HEADING_PO_PRINT_UNIT_PRICE', "Unit Price");
define('TABLE_HEADING_PO_PRINT_DISCOUNT', "Discount");
define('TABLE_HEADING_PO_PRINT_TOTAL', "Total");

// label text
define('TEXT_SUPPLIER_CREDIT_NOTE_UTILISE', 'Include credit note');
define('TEXT_SUPPLIER_CREDIT_NOTE_UTILISED', 'Utilised');
define('TEXT_SUPPLIER_CREDIT_NOTE_NOT_UTILISED', 'Not Utilising');
define('TEXT_SUPPLIER_PRE_PAYMENT', 'Pre-Payment');
define('TEXT_SUPPLIER_DAY_TERM', 'day-Term');
define('TEXT_SUPPLIER_CONSIGNMENT', 'Consignment');
define('TEXT_PAYMENT_BILLED', 'Billed');
define('TEXT_PAYMENT_PENDING_BILLING', 'Pending Billing');
define('TEXT_PAYMENT_PENDING_WITHDRAW', 'Pending Withdraw');
define('TEXT_PAYMENT_FULLY_WITHDRAW', 'Fully Withdraw');
define('TEXT_PAYMENT_PARTIAL_WITHDRAW', 'Partial Withdraw');
define('TEXT_PAYMENT_PAID', 'Paid');
define('TEXT_PAYMENT_PENDING_PAYMENT', 'Pending Payment');
define('TEXT_PAYMENT_NO_PAYMENT', 'No Payment Required');
define('TEXT_PO_ANY', '<i>Any</i>');
define('TEXT_NOTIFY_SUPPLIER', 'Show to Supplier');
define('TEXT_SET_AS_REMARK', 'Set as Important Remark');
define('TEXT_NO_PO_HISTORY', 'No Purchase Order History Available');
define('TEXT_PRICE_UP', "Cost Price Higher Than Previous Purchase");
define('TEXT_PRICE_EQUAL', "Cost Price Equal To Previous Purchase");
define('TEXT_PRICE_DOWN', "Cost Price Lower Than Previous Purchase");
define('TEXT_NO_PRICE', "No Previous Purchase Price");
define('TEXT_REMARK_MODIFIED_BY', 'Last changed by ');
define('TEXT_VERIFIED', ' <b>[Verified]</b>');
define('TEXT_UNVERIFIED', ' <b>[Unverified]</b>');
define('TEXT_DELIVERED_AMOUNT', 'Delivered Amount');
define('TEXT_REFUNDED_AMOUNT', 'Refunded Amount');
define('TEXT_REMAINING_AMOUNT', 'Remaining Amount');
define('TEXT_REFUNDED_BANKCHARGES', 'Refunded Bank Charges');
define('TEXT_PRODUCTS_ID', 'Product ID:');

// button text
define('IMAGE_BUTTON_ADD_ITEM', 'Add Item');
define('IMAGE_BUTTON_RESET', 'Reset');
define('BUTTON_PREVIEW_PO', 'Preview PO');
define('BUTTON_GENERATE_PO', 'Generate PO');
define('BUTTON_ADD_REMARK', 'Add Remark');
define('BUTTON_COMPLETE', 'Manual Complete');
define('BUTTON_ROLLBACK_TO_PROCESS', 'Rollback to Process');
define('BUTTON_REFUND_CREDIT', 'Refund Credit Note');
define('BUTTON_REFUND_CASH', 'Refund for Cash');
define('BUTTON_CANCEL_PENDING_RECEIVE', 'Cancel Pending Receive');
define('BUTTON_VERIFY', 'Mark as Verified');
define('BUTTON_UNVERIFY', 'Mark as Unverified');
define('BUTTON_MAKE_PAYMENT', 'Credit to WSC');
define('BUTTON_DEBIT_PAYMENT', 'Debit from WSC');
define('ALT_BUTTON_PREVIEW_PO', ' Preview PO ');
define('ALT_BUTTON_GENERATE_PO', ' Generate PO ');
define('ALT_BUTTON_ADD_REMARK', ' Add Remark ');
define('ALT_BUTTON_COMPLETE', ' Manual Complete ');
define('ALT_ROLLBACK_TO_PROCESS', ' Rollback to Process ');
define('ALT_BUTTON_REFUND_CREDIT', ' Refund Credit Note ');
define('ALT_BUTTON_REFUND_CASH', ' Refund for Cash ');
define('ALT_BUTTON_CANCEL_PENDING_RECEIVE', ' Cancel Pending Receive ');
define('ALT_BUTTON_VERIFY', ' Mark as Verified ');
define('ALT_BUTTON_UNVERIFY', ' Mark as Unverified ');
define('ALT_BUTTON_MAKE_PAYMENT', ' Credit to WSC ');
define('ALT_BUTTON_DEBIT_PAYMENT', ' Debit from WSC ');

// Status Remark
define('HEADER_ADD_REMARK', 'Add Remark');
define('TEXT_SET_IMPORTANT_REMARK', 'Set as Important Remark');

// text for PO locking in various stage
define('TEXT_ORDER_NOT_BEEN_LOCKED', '<span class="redIndicator">This Purchase Order has not been locked. Product Location information is hidden to prevent double-delivery of Products. Click the "LOCK" button on your right if you would like to edit this Purchase Order.</span>');
define('TEXT_LOCKED_ORDER_SEEN_BY_OWNER', 'This Purchase Order has been locked by you on %s at %s. If you are not going to edit this Purchase Order or if you have finished your Partial Delivery, please remember to click "UNLOCK" button on your right so that others may lock it.');
define('TEXT_ORDER_LOCKED_BY_OTHER', '<span class="redIndicator">This Purchase Order has been locked by %s on %s at %s. Product Location information is hidden to prevent double delivery of Products. You cannot edit this Purchase Order unless you lock the Order. To do so, you must first contact this person or any of the person in the following Admin Goups to unlock the Order. %s</span>');
define('TEXT_UNLOCK_OTHER_PEOPLE_ORDER', '<span class="redIndicator">This Purchase Order has been locked by %s on %s at %s. Product Location information is hidden to prevent double-delivery of Products. You have the Permission to unlock it by clicking the "UNLOCK" button on your right. Please note that unlocking this Order may result in double-delivery of Products.</span>');
define('TEXT_LOCKED_ORDER_NOT_VALID_STATUS', '<span class="redIndicator">This order cannot be locked since the order status is not in "Processing".</span>');
define('TEXT_LOCKED_OUTDATED_ORDER', '<span class="redIndicator">Someone has changed this order before you manage to lock it.</span>');
define('TEXT_DUPLICATE_LOCKED_ORDER_SEEN_BY_OWNER', '<span class="redIndicator">This order has been opened and locked by you on another browser window. This window will close after you click OK.</span>');

define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');
define('ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER', '##ul_2##%s:~:%s');
define('ORDERS_LOG_LOCK_ORDER', '##l_1##');

// success or error messages
define('SUCCESS_PO_FORM_ADDED', "Success: Purchase Order, %s, has been successfully added.");
define('ERROR_PO_LOAD_EMPTY_ID', "Error: Can't retrieve purchase order with empty ID.");
define('ERROR_PO_SEARCH_INVALID_ID', "Error: Can't find purchase order with PO Number = %s.");
define('ERROR_PO_FORM_EMPTY_PO_DATE', "Error: Please select date for this purchase order.");
define('ERROR_PO_FORM_EMPTY_DELIVERY_ADDRESS', "Error: Please select a delivery address for this purchase order.");
define('ERROR_PO_FORM_EMPTY_SUPPLIER', "Error: Please select a supplier for this purchase order.");
define('ERROR_PO_FORM_EMPTY_PAYMENT_METHOD', "Error: Please select a payment method for this purchase order.");
define('ERROR_PO_FORM_EMPTY_CURRENCY', "Error: Please select a currency for this purchase order.");
define('ERROR_PO_FORM_EMPTY_CURRENCY_CONFIRM_RATE', "Error: Please enter a confirm currency rate for this purchase order.");
define('ERROR_PO_FORM_EMPTY_PRODUCTS', "Error: Please enter a product for this purchase order.");
define('ERROR_PO_FORM_EMPTY_SUGGEST_QUANTITY', "Error: Please enter suggested quantity for %s.");
define('ERROR_PO_FORM_EMPTY_STOCK_PRICE', "Error: Please enter stock price for %s.");

define('ERROR_PO_FORM_CREATE_PERMISSION', "Error: You are not allow to create new Purchase Order.");
define('ERROR_PO_FORM_PO_APPROVE_PERMISSION', "Error: You are not allow to approve/cancel the Purchase Order.");
define('ERROR_PO_FORM_ADD_REMARK_PERMISSION', "Error: You are not allow to add remark for Purchase Order.");
define('ERROR_PO_FORM_PO_REFUND_CREDIT_NOTE_PERMISSION', "Error: You are not allow to refund credit note for Purchase Order.");
define('ERROR_PO_FORM_PO_REFUND_CASH_PERMISSION', "Error: You are not allow to refund cash for Purchase Order.");
define('ERROR_PO_FORM_PO_CANCEL_PENDING_RECEIVE_PERMISSION', "Error: You are not allow to cancel pending stock receive for Purchase Order.");
define('ERROR_PO_FORM_VERIFY_PERMISSION', "Error: You are not allow to set verify/unverify for Purchase Order.");
define('ERROR_PO_FORM_PO_MAKE_PAYMENT_PERMISSION', "Error: You are not allow to make payment for Purchase Order.");

define('ERROR_PO_CANCELLATION_FAILED', "Failed to Cancel the purchase order! Missing Purchase Order ID.");
define('ERROR_PO_APPROVE_FAILED', "Failed to Approve the purchase order! Missing Purchase Order ID.");
define('ERROR_PO_COMPLETE_FAILED', "Failed to Complete the purchase order! Missing Purchase Order ID.");
define('ERROR_PO_COMPLETE_FAILED_PENDING_RECEIVE', "Failed to Complete the purchase order! There are items still having pending receive quantity.");
define('ERROR_PO_ROLLBACK_COMPLETE_FAILED', "Failed to rollback Completed purchase order! Missing Purchase Order ID.");
define('ERROR_PO_STATUS_CHANGE_FAILED', "Failed to change purchase order status from %s to %s! Status Change Permission denied.");
define('ERROR_PO_VERIFY_FAILED', "Failed to Verify the purchase order! Missing Purchase Order ID.");
define('ERROR_PO_ADD_REMARK_FAILED', "Failed to add remark for the purchase order! Missing Purchase Order ID.");

define('SUCCESS_CREDIT_WSC', "Success: WSC has been credited.");
define('SUCCESS_DEBIT_WSC', "Success: WSC has been debited.");
define('ERROR_PO_CREDIT_WSC_PAYMENT_TYPE_FAILED', "Failed to credit the WSC! Only allow for Pre-payment type purchase order.");
define('ERROR_PO_DEBIT_WSC_PAYMENT_TYPE_FAILED', "Failed to debit the WSC! Only allow for Pre-payment type purchase order.");
define('ERROR_PO_DEBIT_WSC_PAYMENT_FOUND_FAILED', "Failed to debit the WSC! Payment has been made.");
define('ERROR_PO_DEBIT_WSC_FUND_FAILED', "Failed to debit the WSC! Insufficient WSC fund.");

define('ERROR_PO_REFUND_CREDIT_NOTE_FAILED', "Failed to refund credit note for the purchase order! Missing Purchase Order ID.");
define('ERROR_PO_REFUND_CREDIT_NOTE_PAID_STATUS_FAILED', "Failed to refund credit note for the purchase order! Purchase Order is not fully paid.");
define('ERROR_PO_REFUND_CASH_FAILED', "Failed to refund cash for the purchase order! Missing Purchase Order ID.");
define('ERROR_PO_REFUND_CASH_PAID_STATUS_FAILED', "Failed to refund cash for the purchase order! Purchase Order is not fully paid.");
define('ERROR_PO_CANCEL_PENDING_RECEIVE_FAILED', "Failed to cancel pending stock receive for the purchase order! Missing Purchase Order ID.");
define('ERROR_PO_CANCEL_PENDING_RECEIVE_PAID_STATUS_FAILED', "Failed to cancel pending stock receive for the purchase order! Purchase Order is either fully paid or partially paid.");
define('ERROR_PO_CANCEL_PENDING_RECEIVE_PREPAYMENT_STATUS_FAILED', "Failed to cancel pending stock receive for the purchase order! Pre-payment Purchase Order is not allow to perform cancellation. Please Credit WSC if it is haven't done so.");

define('WARNING_TEXT_PO_FORM_PO_PARTIAL_PAID', "Please make this Purchase Order fully paid before can refund it by cash.");
?>