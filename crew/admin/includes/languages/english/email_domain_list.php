﻿<?
define('HEADING_TITLE', 'High Risk Email Domain List');

define('ENTRY_GROUP_NAME', 'Email Domain Group Name');
define('ENTRY_UPLOAD_ICONS', 'Upload Verification Icons');
define('ENTRY_UPLOAD_EMAIL_VERIFIED', 'Verify');
define('ENTRY_UPLOAD_EMAIL_UNVERIFY', 'Not verify');
define('ENTRY_EMAIL_DOMAIN_LIST', 'Email Domain List');
define('ENTRY_UPDATED_EMAIL_DOMAIN_LIST', 'Email Domain List');
define('ENTRY_DOMAIN_LIST_HEADER', 'Email Domains');

define('TABLE_HEADING_GROUP_NAME', 'Email Domain Group Name');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_MEDIUM_RISK_COUNTRIES', 'Medium Risk Countries');
define('TABLE_HEADING_LOW_RISK_COUNTRIES', 'Low Risk Countries');
define('TABLE_HEADING_PREFERRED_COUNTRIES', 'Preferred Countries');
define('TABLE_HEADING_NOTE', '<font size="-1">NRP Countries:<br> This will filter out RP from checkout page</font>');
define('TABLE_HEADING_UPDATED_LIST_NO', 'No');
define('TABLE_HEADING_UPDATED_LIST_EMAIL_DOMAIN', 'Email Domain');
define('TABLE_HEADING_UPDATED_LIST_ACTION', 'Action');

define('TEXT_NO_EMAIL_GROUP', 'No Email Domain Group Found');

define('LINK_ADD_GROUP', 'Add Email Domain Group');
define('BTN_ADD_DOMAIN', 'Add Domain');

define('SUCCESS_INSERT_EMAIL_DOMAIN_GROUP', 'Success: Email domain group has been successfully inserted.');
define('SUCCESS_UPDATE_EMAIL_DOMAIN_GROUP', 'Success: Email domain group has been successfully updated.');
define('SUCCESS_REMOVE_EMAIL_DOMAIN_GROUP', 'Success: Email domain group has been successfully removed.');
define('ERROR_EMPTY_EMAIL_DOMAIN_GROUP', 'Error: Empty email domain group.');
define('ERROR_EMAIL_DOMAIN_GROUP_USED', 'Error: Email domain group has already been used!');

define('ERROR_MESSAGE', 'Some error occurred (click for details)');
define('ERROR_INSERT_EMAIL_DOMAIN', 'Error : Failed To Insert This Domain.');
define('ERROR_EMAIL_DOMAIN', 'Error domain(s) :');

define('COMFIRM_DELETE_DOMAIN', 'Are you sure you want to delete?');
?>