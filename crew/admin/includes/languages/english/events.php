<?
/*
  	$Id: events.php,v 1.4 2010/09/02 05:24:32 henry.chow Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
define('HEADING_TITLE', 'Events');
define('TEXT_NEW_EVENTS', 'New Events');
define('FIELDSET_ORDER_TITLE_SETTING', 'Order Setting');
/************************************************************
	Bracket Page
************************************************************/
define('TEXT_ADD_NEW_OPTION', 'Add New Option');
define('TEXT_BRACKET_GROUP_SORT_ORDER', 'Sort Order');
define('TEXT_BRACKET_GROUP_DESCRIPTION', 'Description');
define('TEXT_SCREEN_SHOT', 'screen shot');

/************************************************************
	Subselections section
************************************************************/
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_EVENTS_NAME', 'Events Name');
define('TABLE_HEADING_EVENTS_SETTING_INFO', 'Events Setiing Info');
define('TABLE_HEADING_EVENTS_OPTIONS', 'Options');
define('TABLE_HEADING_ADD_NEW', 'Add New');
define('TABLE_HEADING_OPTIONS_TITLE', 'Title');
define('TABLE_HEADING_OPTIONS_INPUT_TYPE', 'Input Type');
define('TABLE_HEADING_OPTIONS_ID', 'ID');
define('TABLE_HEADING_OPTIONS_SORT_ORDER', 'Sort Order');
define('TABLE_HEADING_STATUS', 'Status');
define('SUBTITLE_EDIT_EVENTS', 'Edit Event');
define('SUBTITLE_EDIT_OPTIONS', 'Edit Option');
define('SUBTITLE_EMAIL_TEMPLATES', 'Email Template');
/*****************************************************************************************
	Events ERROR & WARNING
*****************************************************************************************/
define('ERROR_CANNOT_LEFT_BLANK', 'cannot be left blank');
define('ERROR_NEWS_EXISTED', 'News was existed, please choose another news');
define('ERROR_CANNOT_LEFT_BLANK_EMAIL_SENDER', 'EMAIL SENDER '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_EMAIL_RECIPIENT', 'EMAIL RECIPIENT '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_EVENT_NAME', 'EVENT NAME '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_SIZE', 'SIZE '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_OPTION_TITLE', 'OPTION TITLE '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_MAX_CHARACTER', 'MAX. CHARACTERs '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_ROW', 'ROW cannot be left blank');
define('ERROR_CANNOT_LEFT_BLANK_COLUMN', 'COLUMN '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_OPTION_FIELD', 'OPTION FIELD '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_SORT_ORDER', 'SORT ORDER '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_DISPLAY_TEXT', 'DISPLAY TEXT '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_CANNOT_LEFT_BLANK_MAX_UPLOAD_SIZE', 'MAX UPLOAD SIZE '.ERROR_CANNOT_LEFT_BLANK);
define('ERROR_AT_LEAST_TWO_VALUES', 'At least have 2 options value for the setting');
define('UPDATE_STATUS_FAILED', 'Update status failed!');
define('ERROR_LESS_THAN_START_LEVEL', 'Level must be greater than the starting level.');
define('WARNING_INVALID_PRICE_VALUE', 'Price has the value less than or equal zero.');
define('SUCCESS_INSERT_EVENT', 'This Events had been successfully insert to database.');
define('SUCCESS_INSERT_EVENT_OPTION', 'This Event Option had been successfully insert to database.');
define('SUCCESS_UPDATE_EVENT_OPTION', 'This Event Option had been successfully update to database.');
define('SUCCESS_UPDATE_EMAIL_TEMPLATE', 'This Email Template had been successfully update to database.');
define('SUCCESS_DELETE_EVENT_OPTION', 'This Event Option had been successfully delete to database.');
define('SUCCESS_DELETE_EVENT', 'This Event had been successfully delete to database.');
define('SUCCESS_UPDATE_EVENT', 'This Event had been successfully update to database.');
define('SUCCESS_UPDATE_EVENT_STATUS', 'This Event status had been successfully update to database.');
define('SUCCESS_UPDATE_OPTIONS_STATUS', 'This Option status had been successfully update to database.');
/*****************************************************************************************
	Events Options Section
*****************************************************************************************/
define('ENTRY_EVENTS_START_DATE', 'Start Date');
define('ENTRY_EVENTS_END_DATE', 'End Date');
define('ENTRY_EVENTS_DATE_MARK', '<small>(YYYY-MM-DD<br>/YYYY-MM-DD HH:MM)</small>');
define('ENTRY_OPTIONS_ERROR_MSG', 'Error Message');
define('ENTRY_EVENT_NOTE', 'Note');
define('ENTRY_EVENT_INPUT_DOESNT_PROVIDED', 'Input doesn\'t provided');
define('ENTRY_EVENT_INVALID_ORDER_RANGE', 'Invalid Order Period');
define('ENTRY_EVENT_VALIDATION_PERIOD', 'Validation Period');
define('ENTRY_EMAIL_SENDER_CHECK_ERROR', 'The EMAIL SENDER address doesn\'t appear to be valid!');
define('ENTRY_EMAIL_RECIPIENT_CHECK_ERROR', 'The EMAIL RECIPIENT address doesn\'t appear to be valid!');
define('ENTRY_OPTIONS_EVENT_SENDER_EMAIL', 'Sender\'s Email');
define('ENTRY_OPTIONS_EVENT_ADMIN_COPY_EMAIL', 'Admin Copy Email');
define('ENTRY_OPTIONS_EVENT_NAME', 'Event Name');
define('ENTRY_OPTIONS_LATEST_NEWS', 'Latest News');
define('ENTRY_OPTIONS_ACTIVE', 'Active');
define('ENTRY_OPTIONS_INACTIVE', 'Inactive');
define('ENTRY_OPTIONS_INPUT_TYPE', 'Input Type');
define('ENTRY_OPTIONS_LANGUAGES', 'Languages');
define('ENTRY_OPTIONS_MANDATORY', 'Mandatory Option');
define('ENTRY_OPTIONS_NOTE', 'Note');
define('ENTRY_OPTIONS_TITLE', 'Option Title');
define('ENTRY_OPTIONS_SETTING', 'Setting');
define('ENTRY_OPTIONS_STATUS', 'Status');
define('ENTRY_OPTIONS_EVENT_REMARK', 'Event Remark');
define('ENTRY_OPTION_ORDER', 'Sort Order');
/*****************************************************************************************
	Events Options Input Type
*****************************************************************************************/
define('ENTRY_OPTIONS_COLUMN', 'Column');
define('ENTRY_OPTIONS_DISPLAY_TEXT', 'Display Text');
define('ENTRY_OPTIONS_MAX_CHAR', 'Max. Character');
define('ENTRY_OPTIONS_SIZE', 'Size');
define('ENTRY_OPTIONS_ROW', 'Row');
define('ENTRY_OPTIONS_MAX_UPLOAD_SIZE', 'Max Upload Size');
define('ENTRY_OPTIONS_YES', 'Yes');
define('ENTRY_OPTIONS_NO', 'No');
define('BTN_SUBMIT', 'Submit');
define('BTN_INSERT', 'Insert');
define('BTN_UPDATE', 'Update');
define('BTN_CANCEL', 'Cancel');
define('TEXT_SENDER_EMAIL_DESCRIPTION', "(In \"Name <Email>\" format.)");
define('TEXT_ADMIN_COPY_EMAIL_DESCRIPTION', "(In \"Name <Email>\" format. Use ',' as delimiter for multiple recipient)");
define('TEXT_EMAIL_TEMP_DESCRIPTION_ROW_1', '<b>##id##</b> is the option value that you want to display to your email.');
define('TEXT_EMAIL_TEMP_DESCRIPTION_ROW_2', 'E.g: MY name is <b>##123##</b>. ');
define('TEXT_EMAIL_TEMP_DESCRIPTION_ROW_3', 'HTML tag is allow in the email template.');
define('TEXT_EMAIL_TEMP_DESCRIPTION_ROW_4', 'E.g:  <a href=\"www.offgamers.com\">OGM</a>, <b>Bold<\/b>, <i>Italic</i>, <u>Underline</u>');
define('CUSTOMER_NOTIFICATION_EVENT_CONTENT_ENGLISH', '***************************************************' . "\n" . 'THIS IS AN AUTOMATED EMAIL - PLEASE DO NOT REPLY.' . "\n" . '***************************************************' . "\n" . "Congratulations,\n\n" . "Your [ %s ]". "\n" . 'You have successfully uploaded your %s' . "\n" . 'For more promotions and events news, please visit <a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>' . "\n" . 'Should you require any assistance, please do not hesitate to contact us at <a href="mailto:' . EMAIL_TO . '">' . EMAIL_TO . '</a>' . "\n\n" . 'Thank you for participating' . "\n\n" . 'Regards,' . "\n" . 'OffGamers - Your Gaming Alliance' . "\n" . '<a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>');
define('CUSTOMER_NOTIFICATION_EVENT_CONTENT_SIMPLIFIED', '***************************************************' . "\n" . '这是一个自发得的电子邮件-请别回复.' . "\n" . '***************************************************' . "\n" . "恭喜,\n\n" . "你的 [ %s ]". "\n" . '您已经成功上载您的 %s' . "\n" . '更多的促销和宣传活动新闻，请访问 <a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>' . "\n" . '如果您需要任何求助 ，请联络 <a href="mailto:' . EMAIL_TO . '">' . EMAIL_TO . '</a>' . "\n\n" . '感谢您参与' . "\n\n" . '谨,' . "\n" . 'OffGamers - 您的游戏联盟' . "\n" . '<a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>');
define('CUSTOMER_NOTIFICATION_EVENT_CONTENT_TRADITIONAL', '******************************************* ********' . "\n" . '這是一個自發得的電子郵件-請別回复.' . "\n" . '************* **************************************' . "\n" . "恭喜,\n \n" . "你的[ %s ]". "\n" . '您已經成功上載您的%s' . "\n" . '更多的促銷和宣傳活動新聞，請訪問<a href= "http:\\www.offgamers.com">' . STORE_NAME . '</a>' . "\n" . '如果您需要任何求助，請聯絡<a href="mailto:' . EMAIL_TO . '" >' . EMAIL_TO . '</a>' . "\n\n" . '感謝您參與' . "\n\n" . '謹,' . "\n" . 'OffGamers -您的遊戲聯盟' . "\n" . '<a href="http:\\www.offgamers.com">' . STORE_NAME . '</a>'); 
?>