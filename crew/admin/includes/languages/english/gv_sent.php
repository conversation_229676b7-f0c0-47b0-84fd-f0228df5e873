<?php
/*
  	$Id: gv_sent.php,v 1.5 2006/07/05 13:22:55 nickyap Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Gift Voucher System v1.0
  	Copyright (c) 2001,2002 <PERSON>
  	http://www.phesis.org
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Gift Vouchers Sent');
define('HEADING_INPUT_TITLE', 'Gift Vouchers Criteria');
define('HEADING_DELETE_VOUCHER', 'Cancellation of Gift Voucher');
define('HEADING_DELETE_VOUCHER_PREVIEW', 'Preview Customer Notification');

define('TABLE_HEADING_DATE_SENT', 'Date Sent');
define('TABLE_HEADING_SENDERS_NAME', 'Senders Name');
define('TABLE_HEADING_RECEIVER_EMAIL', 'E-mail');
define('TABLE_HEADING_VOUCHER_VALUE', 'Voucher Value');
define('TABLE_HEADING_VOUCHER_CODE', 'Voucher Code');
define('TABLE_HEADING_VOUCHER_STATUS', 'Voucher Status');
define('TABLE_HEADING_VOUCHER_ACTION', 'Action');

define('TEXT_SUBJECT', 'Subject:');
define('TEXT_FROM', 'From:');
define('TEXT_TO', 'Email To:');
define('TEXT_MESSAGE', 'Message:');

define('ENTRY_CUSTOMER_EMAIL', 'Customer Email');
define('ENTRY_START_DATE', 'Start Date');
define('ENTRY_END_DATE', 'End Date');
define('ENTRY_GV_STATUS', 'Voucher Status');

define('TEXT_INFO_SENDERS_ID', 'Senders ID:');
define('TEXT_INFO_AMOUNT_SENT', 'Amount Sent:');
define('TEXT_INFO_DATE_SENT', 'Date Sent:');
define('TEXT_INFO_VOUCHER_CODE', 'Voucher Code:');
define('TEXT_INFO_EMAIL_ADDRESS', 'Email Addr:');
define('TEXT_INFO_DATE_REDEEMED', 'Date Redeemed:');
define('TEXT_INFO_IP_ADDRESS', 'IP Address:');
define('TEXT_INFO_CUSTOMERS_ID', 'Customer Id:');
define('TEXT_INFO_VOUCHER_STATUS', 'Voucher Status:');
define('TEXT_INFO_CODE', ' (Code: %s)');

//define('TEXT_INFO_NOT_REDEEMED', 'Not Redeemed');

define('TEXT_STATUS_REDEEMED', 'Redeemed');
define('TEXT_STATUS_NOT_REDEEMED', 'Not Redeemed');
define('TEXT_TOOLTIP_DELETE', 'Cancel this voucher');
define('TEXT_CONFIRM_DELETE', 'Are you sure you wish to continue?');

define('EMAIL_SUBJECT_DELETE_COUPON', 'Cancellation of Gift Voucher');
define('EMAIL_SUBJECT_DELETE_REDEEMED_COUPON', 'Cancellation of Redeemed Gift Voucher');
define('EMAIL_TEXT_FRIEND_TITLE', 'Friend');
define('EMAIL_TEXT_DELETED_GV_CODE', 'Cancellation of Gift Voucher');
define('EMAIL_TEXT_CLOSING', "\n\n" . 'Thank you for all your support! For any enquiries or assistance, you may e-mail your enquiries to ' . EMAIL_TO . '. Please remember to include your Gift Voucher Code when contacting us to expedite the process.');

define('EMAIL_TEXT_DELETED_SHOW_LOSS_ADMIN', "\n" . 'Below are the Gift Voucher cancellation details which resulting in a loss :'.
											'<table border="0" cellspacing="2" cellpadding="2">'.
											'<tr>'.
											'	<td style="background-color:#FFD073; color:#000099;">Voucher Code</td>'.
											'	<td style="background-color:#DFDFDF; color:#000;"><b>%s</b></td>'.
											'</tr>'.
											'<tr>'.
											'	<td style="background-color:#FFD073; color:#000099;">Admin User</td>'.
											'	<td style="background-color:#DFDFDF; color:#000;">%s [ %s ]</td>'.
											'</tr>'.
											'<tr>'.
											'	<td style="background-color:#FFD073; color:#000099;">Customer Name</td>'.
											'	<td style="background-color:#DFDFDF; color:#000;">%s &lt;<a href="mailto:%s">%s</a>&gt; </td>'.
											'</tr>'.
											'<tr>'.
											'	<td style="background-color:#FFFFFF;" colspan="2"><hr></td>'.
											'</tr>'.
											'<tr>'.
											'	<td style="background-color:#FFD073; color:#000099;">Customer Balance</td>'.
											'	<td style="background-color:#DFDFDF; color:#000;" align="right">%s</td>'.
											'</tr>'.
											'<tr>'.
											'	<td style="background-color:#FFD073; color:#000099;">(-) Cancelled Voucher Amount</td>'.
											'	<td style="background-color:#DFDFDF; color:#000;" align="right">%s</td>'.
											'</tr>'.
											'<tr>'.
											'	<td style="background-color:#FFD073; color:#000099;"><b>Loss Amount</b></td>'.
											'	<td style="background-color:#DFDFDF; color:#CC0000;" align="right"><b>( %s )</b></td>'.
											'</tr>'.
											'<tr>'.
											'	<td style="background-color:#FFFFFF;" colspan="2"><hr></td>'.
											'</tr>'.
											'<tr>'.
											'	<td style="background-color:#FFD073; color:#000099;" colspan="2">Message sent to customer :</td>'.
											'</tr>'.
											'<tr>'.
											'	<td style="background-color:#DFDFDF; color:#000;" colspan="2">%s</td>'.
											'</tr>'.
											'</table>');

define('MESSAGE_DELETE_CODE_OK', 'Success: Gift Voucher has been successfully deleted');
define('MESSAGE_DELETE_CODE_FAILED', 'Failed: Gift Voucher has been successfully deleted');
define('MESSAGE_COUPON_IS_REDEEMED', 'Failed: Gift Voucher has NOT been successfully deleted. This voucher has just been redeemed.');

define('MESSAGE_EMAIL_NOTIFY_DELETE', 'Notice: Notification email sent to %s');

?>