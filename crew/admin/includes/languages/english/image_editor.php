<?
define('HEADING_TITLE', 'Image Editor Toolbars');

define('BUTTON_ROTATE', 'Rotate - Mirror');
define('BUTTON_CROP', 'Cropping');
define('BUTTON_INSERT_TEXT', 'Insert Text');
define('BUTTON_MODIFY_COLOR', 'Modify Colors');
define('BUTTON_SAVE_CHANGES', 'Save Changes');
define('BUTTON_CLOSE', 'Close');
define('BUTTON_APPLY', 'APPLY');

define('ENTRY_ROTATE', 'Rotate');
define('ENTRY_COLOR_MASKING', 'Color correction');
define('ENTRY_CROP', 'Cropping');
define('ENTRY_TEXT', 'Text');

define('ROTATE_OPTION_ROTATE', 'Rotate');
define('ROTATE_OPTION_LEFT', 'Left (-90&#176;)');
define('ROTATE_OPTION_FLIP', 'Flip (180&#176;)');
define('ROTATE_OPTION_RIGHT', 'Right (+90&#176;)');

define('CHECKBOX_MIRROR', 'Mirror');

define('TEXT_COLOR', 'Color');
define('TEXT_LEFT', 'Left');
define('TEXT_TOP', 'Top');
define('TEXT_RIGHT', 'Right');
define('TEXT_BOTTOM', 'Bottom');
define('TEXT_TEXT', 'Text');
define('TEXT_FONT', 'Font');
define('TEXT_SIZE', 'Size');
define('TEXT_FONT_COLOR', 'Font color');
define('TEXT_ANGLE', 'Angle');

define('SYMBOLS_TERM_DEGREES', 'degrees');

define('SUCCESS_FILE_UPDATE_SAVED_SUCCESSFULLY', 'Success: File saved successfully.');
define('SUCCESS_FILE_UPDATE_NOT_SAVED', '<font color=red>Error: File not saved.</font>');
define('ERROR_EDITOR_ACCESS_DENIED', 'Error: You do not have permission to access image editor.');
define('ERROR_EDITOR_LOAD_IMAGE', 'Error: Failed to load image, please try again.');
?>