<?
/*
  	$Id: image_upload.php,v 1.3 2011/01/28 05:40:21 sionghuat.chng Exp $
	
	Developer: Siong Huat Ch'ng
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Image Directories');
define('TEXT_INFO_HEADING_UPLOAD_TO', 'Upload to ');
define('TEXT_INFO_HEADING_UPLOAD_TITLE', 'Multimedia Files Upload');
define('TEXT_INFO_HEADING_GALLERY_TITLE', 'Image Gallery');
define('TEXT_INFO_HEADING_UPLOADED_FILES_TITLE', 'Uploaded files:');
define('TEXT_INFO_HEADING_NEW_IMAGES_GALLERY', 'New Images Gallery:');
define('TEXT_INFO_HEADING_OLD_IMAGES_GALLERY', 'Old Images Gallery:');
define('TEXT_INFO_HEADING_GALLERY_INSTRUCTION', '<li>Use the link above to select either new images gallery or old images gallery</li><li>Click the file name to activate the gallery. You may use keyboard arrow key (left,right) to nagivate the gallery.</li><li>Beware, deleted images cannot be restore, please use in caution.</li><li>If you open the images in the old images section, means you accessed the images, and this mean the image will be move to new images section after you refresh the page.');

define('TEXT_INFO_NOTICE_MAX_UPLOAD_FILES', 'You can only upload a maximun of 20 files in a time.');
define('TEXT_INFO_NOTICE_MAX_UPLOAD_SIZES', 'Maximum file size for each file is <b> 5MB </b>.');
define('TEXT_INFO_NOTICE_UPLOAD_SUPPORTED_FILE_TYPE', 'Only supported file type: <b>*.jpg </b><b>*.jpeg </b><b>*.gif </b><b>*.png </b><b>*.swf </b><b>*.flv</b>.');
define('TEXT_INFO_NOTICE_UPLOAD_TO_THIS_DIRECTORY', 'Upload images for this directory');
define('TEXT_INFO_NOTICE_MAINTAIN_IMAGES_IN_THIS_DIRECTORY', 'Maintain images for this directory');
define('TEXT_INFO_NOTICE_CONFIRM_DELETE', 'Delete this image from this directory');
define('TEXT_INFO_NOTICE_FILE_UPLOAD_SUCCESSFUL', 'Files uploaded successfully.');

define('TEXT_INFO_NOTICE_JAVASCRIPT_WARNING', 'You have a problem with your javascript');
define('TEXT_INFO_NOTICE_SUPPORTED_TYPE_WARNING', '<br/>Please only upload files with supported file types<br>');
define('TEXT_INFO_NOTICE_DELETE_CONFIRMATION', 'Are you sure you want to delete ');

define('TEXT_INFO_ERROR_NO_PERMISSION_UPLOAD', 'No permission to upload.');
define('TEXT_INFO_ERROR_LARGE_FILE_SIZE', 'ERROR: Large File Size');
define('TEXT_INFO_ERROR_FILE_TYPE', 'ERROR: Invalid support file type');
define('TEXT_INFO_ERROR_EMPTY_FILE_UPLOAD', 'ERROR: Please upload something');

define('TEXT_LINK_TITLE_START_UPLOAD', 'Start Upload');
define('TEXT_LINK_TITLE_CLEAR_QUEUE', 'Clear Queue');
define('TEXT_LINK_TITLE_GO_BACK', 'Go Back');
define('TEXT_LINK_TITLE_LATEST_IMAGES', 'Latest Images');
define('TEXT_LINK_TITLE_OLD_IMAGES', 'Old Image( Not accessed last 6 months)');
define('TEXT_LINK_TITLE_SIMPLE_UPLOAD', '[Simple Uploader]');
define('TEXT_LINK_TITLE_ADVANCED_UPLOAD', '[Mass Uploader]');

define('TEXT_INFO_TITLE_KB', 'KB');

define('TEXT_CACHE_CONTROL', 'Cache Control:');

define('TABLE_HEADING_DIRECTORIES_URL', 'Directories');
define('TABLE_HEADING_SECTION', 'Section');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_LAST_ACCESS', 'Last Access Date & Time');
define('TABLE_HEADING_FILE_NAME', 'File Name');
define('TABLE_HEADING_FILE_SIZE', 'File Size');
define('TABLE_HEADING_FILE_DELETE', 'File Delete');
?>