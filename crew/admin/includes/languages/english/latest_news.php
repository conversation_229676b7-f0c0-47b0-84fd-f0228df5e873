<?php
/*
  latest_news.php v1.1.4 (by J0J0)

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 Will Mays

  Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Latest News');

define('HEADING_TITLE_SEARCH', 'News Group');

define('TABLE_HEADING_LATEST_NEWS_HEADLINE', 'Headline');
define('TABLE_HEADING_LATEST_NEWS_ACTION', 'Action');
define('TABLE_HEADING_LATEST_NEWS_STATUS', 'Status');
define('TABLE_HEADING_LATEST_NEWS_TYPE', 'Type');
define('TABLE_HEADING_LATEST_NEWS_CATEGORIES', 'Categories');
define('TABLE_HEADING_LATEST_NEWS_URL', 'URL');
define('TABLE_HEADING_DISPLAY_SITE', 'Display Site');

define('TEXT_I_WANT_SELL', 'I want sell');
define('TEXT_NEWS_ITEMS', 'News Items:');
define('TEXT_INFO_HEADING_DELETE_ITEM', 'Delete Item');
define('TEXT_DELETE_ITEM_INTRO', 'Are you sure you want to permanently delete this item?');
define('TEXT_LATEST_NEWS_HEADLINE_TAB', 'Headline:');
define('TEXT_LATEST_NEWS_SEO_ALIAS', 'SEO Alias:');
define('TEXT_LATEST_NEWS_AUTO', 'Auto Generate SEO');
define('TEXT_LATEST_NEWS_SUMMARY_TAB', 'Summary:');
define('TEXT_LATEST_NEWS_CONTENT_TAB', 'Content:');
define('TEXT_INFO_DATE_ADDED', 'Info Date Added');
define('TEXT_INFO_REMARK', 'Info Remark');
define('TEXT_INFO_ADDED_BY', 'Added By');
define('TEXT_INFO_CHANGED_HISTORY_NEWS', 'news');
define('IMAGE_NEW_NEWS_ITEM', 'New news item');

define('TEXT_LATEST_NEWS_DATE', 'Date');
define('TEXT_DEFAULT_LANGUAGE', 'Default Language');
define('TEXT_LATEST_NEWS_GROUPS', 'News Groups');
define('TEXT_NO_CHILD_CATEGORIES_OR_PRODUCTS', 'No Items found!');
define('TEXT_CATEGORIES_CHECKBOX_LABEL', 'Include this categories');
define('EMPTY_CATEGORY', '');

define('WARNING_LATEST_NEWS_URL_NAME_DUPLICATE', 'Warning: The URL Name is duplicate!');
define('WARNING_LATEST_NEWS_URL_ALIAS_CHANGED', 'Warning: The SEO URL Alias changed FROM %s TO %s!');

define('JS_TEXT_MESSAGE_LEAVE_COMMENT', 'Leave any message for this update?');
define('JS_TEXT_SEO_ALIAS_REQUESTED', 'The SEO Alias is requested!');
define('JS_TEXT_SEO_SPECIAL_CHARACTER', 'The SEO Alias has special characters or space (accept minus "-"). These are not allowed. \nPlease remove them and try again.');

define('LINK_HIDE_INFO_HISTORY_BOX', 'Hide Info Changed History');
define('LINK_SHOW_INFO_HISTORY_BOX', 'Show Info Changed History');

// New Addition 23 April 2010

define('TEXT_LATEST_NEWS_UPLOAD_THUMBNAIL', 'Upload Thumbnail:');
define('TEXT_LATEST_NEWS_UPLOAD_BANNER', 'Upload Banner:');
define('TEXT_LATEST_NEWS_UPLOAD_IMAGE', 'Upload Extra Image:');
define('TEXT_LATEST_NEWS_VIEW_IMAGE', 'View Image:');
define('TEXT_LATEST_NEWS_TAGS', 'Tags:');
define('TEXT_LATEST_NEWS_QUICK_TAG', 'Quick Tag:');
define('TEXT_LATEST_NEWS_NO_IMAGE_UPLOADED', '(no image uploaded yet)');
define('TEXT_LATEST_NEWS_NO_REMOVE_IMAGE', 'Remove Image');

define('ERROR_IMAGE_DIRECTORY_NOT_WRITEABLE', 'Error: Images directory is not writeable: ' . DIR_WS_IMAGES . 'news/');
define('ERROR_IMAGE_DIRECTORY_DOES_NOT_EXIST', 'Error: Images directory does not exist: ' . DIR_WS_IMAGES . 'news/');
define('ERROR_FAIL_TO_CHANGE_DEFAULT_LANGUAGE', 'Error: Failed to change the default language');

define('OPTION_LATEST_NEWS_TAG_0', '--- Please Select ---');
define('OPTION_LATEST_NEWS_TAG_1', 'Events & Promotions');
define('OPTION_LATEST_NEWS_TAG_2', 'Site Notice');
define('OPTION_LATEST_NEWS_TAG_3', 'Payment Method');
define('OPTION_LATEST_NEWS_TAG_4', 'Achieve');
define('OPTION_LATEST_NEWS_TAG_5', 'Gaming News');
define('OPTION_LATEST_NEWS_TAG_6', 'Marketplace');
define('OPTION_LATEST_NEWS_TAG_7', 'Game Guides');
define('OPTION_LATEST_NEWS_TAG_8', 'Others');

?>