<?
/*
  	$Id: wu.lng.php, v1.0 2005/12/09 11:30:21
  	Author : <PERSON><PERSON> (<EMAIL>)
	
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

define('ENTRY_MAXMIND_HISTORY_DATE', 'Requested On:');
define('ENTRY_DISTANCE', 'Distance between IP Address and Billing Address:');
define('ENTRY_COUNTRY_MATCH', 'IP Country Match:');
define('ENTRY_IP_COUNTRY_CODE', 'IP Country Code:');
define('ENTRY_FREE_MAIL', 'Free E-mail Provider:');
define('ENTRY_ANONYMOUS_PROXY', 'Anonymous Proxy:');
define('ENTRY_SCORE', 'MaxMind Fraud Score:<span class="redIndicator">*</span>');
define('ENTRY_BIN_MATCH', 'BIN Match:');
define('ENTRY_BIN_COUNTRY_CODE', 'BIN Country Code:');
define('ENTRY_ERROR', 'Error:');
define('ENTRY_PROXY_SCORE', 'Open Proxy Score:<span class="redIndicator">*</span>');
define('ENTRY_SPAM_SCORE', 'Spam Source Score:<span class="redIndicator">*</span>');
define('ENTRY_IP_REGION', 'IP Region:');
define('ENTRY_IP_CITY', 'IP City:');
define('ENTRY_IP_LATITUDE', 'IP Latitude:');
define('ENTRY_IP_LONGITUDE', 'IP Longitude:');
define('ENTRY_BIN_NAME', 'BIN Name:');
define('ENTRY_IP_ISP', 'IP ISP:');
define('ENTRY_IP_ORGNIZATION', 'IP Organization:');
define('ENTRY_BIN_NAME_MATCH', 'BIN Name Match:');
define('ENTRY_BIN_PHONE_MATCH', 'BIN Phone Match:');
define('ENTRY_BIN_PHONE', 'BIN Phone:');
define('ENTRY_CUSTOMER_PHONE_IN_BILLING_LOCATION', 'Customer Phone in Billing Location:');
define('ENTRY_HIGH_RISK_COUNTRY', 'High Risk Country');
define('ENTRY_QUERIES_REMAINING', 'Queries Remaining:');
define('ENTRY_CITY_POSTAL_MATCH', 'City Postal Match:');
define('ENTRY_SHIPPING_CITY_POSTAL_MATCH', 'Shipping City Postal Match:');
define('ENTRY_MAXMIND_ID', 'MaxMind ID:');
define('ENTRY_IP_AREA_CODE', 'IP Area Code:');
define('ENTRY_IP_POSTAL_CODE', 'IP Postal Code:');
define('ENTRY_IP_TIME_ZONE', 'IP Time Zone:');
define('ENTRY_TRANSPROXY', 'TransProxy:');
define('ENTRY_CORPORATE_PROXY', 'Corporate Proxy:');
define('ENTRY_IP_NET_SPEED_CELL', 'IP netSpeedCell:');
define('ENTRY_IP_USER_TYPE', 'IP User Type:');
define('ENTRY_IP_ACCURACY_RADIUS', 'IP Accuracy Radius:');

//Field
define('TEXT_BIN_NUMBER', 'BIN Number: (Optional)');
define('TEXT_BIN_NAME', 'BIN Name: (Optional)');
define('TEXT_BIN_PHONE', 'BIN Phone: (Optional)');

//Information Message
define('CUSTOMERS_INFO_MESSAGE', 'Billing Address Taken From:');
define('RISK_INFO_MESSAGE', '<span class="redIndicator">*</span>0 low risk, 10 high risk');
define('ERROR_MESSAGE', ".</span>&nbsp;Please manually check at" . ' <a href="https://www.maxmind.com/app/ccv2" class="actionLink" target="blank">' . "Maxmind's</a> website");
?>