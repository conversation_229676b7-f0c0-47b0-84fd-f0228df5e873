<?
if ($action == 'new_discount') {
	define('HEADING_TITLE', 'Add Customers Group Discount');
} else if ($action == 'edit_discount') {
	define('HEADING_TITLE', 'Edit Customers Group Discount');
} else {
	define('HEADING_TITLE', 'Customers Grouping');
}
define('HEADING_TITLE_SEARCH', 'Search:');

define('TABLE_HEADING_NAME', 'Name');
define('TABLE_HEADING_DISCOUNT_SETTING', 'Discount Setting');
define('TABLE_HEADING_CATEGORY', 'Category');
define('TABLE_HEADING_DISCOUNT', 'Discount Rate');
define('TABLE_HEADING_ACTION', 'Action');

define('ENTRY_CUSTOMER_GROUP_NAME', 'Group Name');
define('ENTRY_CATEGORY', 'Category');
define('ENTRY_DEFAULT_DISCOUNT', 'Discount Rate');

define('TEXT_DELETE_INTRO', 'Are you sure you want to delete this group?');
define('TEXT_INFO_HEADING_DELETE_GROUP', 'Delete Group');

define('LINK_ADD_CUSTOMER_GROUP', 'Add Customer Group');

define('ERROR_CUSTOMERS_GROUPS_NAME', 'Please enter a Group Name');
define('ERROR_GROUPS_NAME_USED', 'Error: Group name has already been used!');

define('ERROR_NO_CATEGORY_SELECTED', 'Please select the category.');
define('ERROR_EMPTY_DISCOUNT', 'No discount is entered.');
define('ERROR_CAT_DISCOUNT_EXISTS', 'Error: The category discount for this customer group has already exists.');
define('SUCCESS_CAT_DISCOUNT_DELETED', 'Success: The category discount setting for this customer group has been successfully deleted.');

define('JS_ERROR_NO_CATEGORY_SELECTED', '* Please select the category.\n');
define('JS_ERROR_EMPTY_DISCOUNT', '* No discount is entered.\n');
?>