<?
/*
  	$Id: order_comment.php,v 1.3 2008/11/07 04:41:15 keepeng.foong Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Order Comments');

define('TEXT_ADD_COMMENTS', 'Add Order Comments');

define('ENTRY_COMMENT_COMMENT_ID', 'Comment ID: ');
define('ENTRY_COMMENT_TITLE', 'Comment Title: ');
define('ENTRY_COMMENT_SORT_ORDER', 'Sort Order: ');
define('ENTRY_COMMENT_ACTIVE', 'Active: ');
define('ENTRY_COMMENT_TEXT', 'Comment Text: ');
define('ENTRY_COMMENT_RADIO_ACTIVE', 'Active');
define('ENTRY_COMMENT_RADIO_INACTIVE', 'Inactive');
define('ENTRY_COMMENT_FILENAME', 'Filename');
define('ENTRY_ERROR_MISSING_FIELDS', 'Please fill in all the fields maked with an asterisk(*)');

define('TABLE_HEADING_COMMENT_ID', 'Comment ID');
define('TABLE_HEADING_COMMENT_TITLE', 'Comment Title');
define('TABLE_HEADING_COMMENT_SORT_ORDER', 'Sort Order');
define('TABLE_HEADING_COMMENT_STATUS', 'Status');
define('TABLE_HEADING_COMMENT_USE_FOR', 'Use For');
define('TABLE_HEADING_COMMENT_ACTION', 'Action');

define('SUCCESS_COMMENT_UPDATED', 'Comment has been successfully updated!');
define('SUCCESS_COMMENT_INSERTED', 'Comment has been successfully insert!');
define('SUCCESS_COMMENT_DELETED', 'Comment has been successfully deleted!');
define('SUCCESS_COMMENT_STATUS_UPDATED', 'Comment Status has been successfully updated!');

define('TEXT_CUSTOMER_ORDER', 'Customer Order');
define('TEXT_PWL_ORDER', 'PWL Order');
define('TEXT_BUYBACK_ORDER', 'Buyback Order');

define('LINK_NEW_COMMENT', 'New Comment');

define('NUMBER_DEFAULT_SORT_ORDER', '50000');
?>
