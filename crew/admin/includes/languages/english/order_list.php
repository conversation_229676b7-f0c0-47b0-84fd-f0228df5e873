<?
/*
  	$Id: order_list.php,v 1.6 2012/01/11 08:28:28 weesiong Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Order Lists 2');
define('HEADING_INPUT_TITLE', 'Order Lists 2');

define('ENTRY_HEADING_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small>');
define('ENTRY_HEADING_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD<br>/YYYY-MM-DD HH:MM)</small>');
define('ENTRY_HEADING_ORDER_FOLLOW_UP_DATE', 'Follow-up Date<br><small>(YYYY-MM-DD<br>/YYYY-MM-DD HH:MM)</small>');
define('ENTRY_DATE_TYPE', 'Date Type');
define('ENTRY_HEADING_ORDER_STATUS', 'Order Status<br>(Real Time)');
define('ENTRY_HEADING_PAYMENT_METHOD', 'Payment Method');
define('ENTRY_CATEGORY', 'Category');
define('ENTRY_ORDER_SITE', 'Order Site');
define('ENTRY_ORDER_SITE_PULLDOWN_DEFAULT', 'Select Order Site');

define('TEXT_LAST_STATUS_DATE', 'Last %s Date');
define('TEXT_ANY', '<i>Any</i>');
define('TEXT_OPTION_NOT_APPLICABLE', 'n/a');

define('TABLE_HEADING_ACTION_DATE', 'Action Date');
define('TABLE_HEADING_ORDER_DATE', 'Order Date');
define('TABLE_HEADING_ORDER_ID', 'Order ID');
define('TABLE_HEADING_ORDER_PRODUCT', 'Order Product');
define('TABLE_HEADING_ORDER_CATEGORY_PATH', 'Category Path');
define('TABLE_HEADING_ORDER_SITE', 'Order Site');
define('TABLE_HEADING_ORDER_STATUS', 'Order Status <br>(Real Time)');
define('TABLE_HEADING_ACTION_BY', 'Action By');
define('TABLE_HEADING_CUSTOMER_ID', 'Customer ID');
define('TABLE_HEADING_CUSTOMER_NAME', 'Customer Name');
define('TABLE_HEADING_CUSTOMER_COUNTRY', 'Country');
define('TABLE_HEADING_FLAG', 'Flag');
define('TABLE_HEADING_CUSTOMER_GROUP', 'Customer Group');
define('TABLE_HEADING_AFT_GROUP', 'AFT Group Level');
define('TABLE_HEADING_ORDER_TAG', 'Order Tag');
define('TABLE_HEADING_PAYMENT_STATUS', 'Payment Status & Returned Code (Bibit Only)');
define('TABLE_HEADING_PRICE_AMOUNT', 'Price Amount (USD)');
define('TABLE_HEADING_DELIVERED_VALUE', 'Delivered Value (USD)');
define('TABLE_HEADING_REFUNDED_VALUE', 'Refunded Value (USD)');
define('TABLE_HEADING_REVERSED_VALUE', 'Reversed Value (USD)');

define('BTN_EXPORT_REPORT', 'Export to CSV');
define('BTN_SHOW_REPORT', 'Show Report');
define('BTN_RESET', 'Reset');
?>