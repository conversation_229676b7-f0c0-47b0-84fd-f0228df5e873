<?
/*
  	$Id: order_tags.php,v 1.3 2010/07/29 10:36:06 wilson.sun Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

if ($_REQUEST['action']=='new_tags') {
	define('HEADING_TITLE', 'Add Tag');
} else {
	define('HEADING_TITLE', 'Order Tags');
}
define('HEADING_TITLE_SEARCH', 'Status:');
define('HEADING_TITLE_FILENAME', 'Filename:');

define('TABLE_HEADING_ORDER_TAG_ACTION', 'Action');
define('TABLE_HEADING_ORDER_TAG_NAME', 'Tag Name');
define('TABLE_HEADING_ORDER_STATUS', 'Order Status');
define('TABLE_HEADING_FILENAME', 'Filename');

define('LINK_ADD_ORDER_TAG', 'Add Tag');

define('ENTRY_TAG_NAME', 'Tag Name');
define('ENTRY_ORDER_STATUS', 'Order Status');
define('ENTRY_FILENAME', 'Filename');

define('TEXT_ALL_ORDER_STATUS', 'All Status');
define('TEXT_ALL_FILES', 'All Files');
?>