<?
/*
  	$Id: orders.php,v 1.121 2015/05/28 10:52:43 darren.ng Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Orders');
define('HEADING_TITLE_SEARCH', 'Order ID:');
define('HEADING_TITLE_TRANSACTION_SEARCH', 'Transaction ID');
define('HEADING_TITLE_STATUS', 'Status:');

define('SECTION_HEADING_COMPENSATION_DETAILS', 'Compensation/Loss Tracking');
define('SECTION_HEADING_G2G_AFFILIATE', 'G2G Affiliate');

define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_BALANCE', 'Pending Delivery');
define('TABLE_HEADING_MODIFIED_BY', 'Changed By');
define('TABLE_HEADING_COMMENTS', 'Comments');
define('TABLE_HEADING_ADD_COMPENSATION', 'Add Item for Compensation/Loss');
define('TABLE_HEADING_CUSTOMERS', 'Customers');
define('TABLE_HEADING_CUSTOMERS_EMAIL', 'Email');
define('TABLE_HEADING_CUSTOMERS_INFO', 'Customer Information');
define('TABLE_HEADING_CUSTOMER_NOTIFIED', 'Customer Notified');
define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_DATE_PURCHASED', 'Date Purchased');
define('TABLE_HEADING_DELIVERY_DETAILS', 'Delivery Details');
define('TABLE_HEADING_DELIVER', 'Deliver / Refund');
define('TABLE_HEADING_IP', 'IP');
define('TABLE_HEADING_ORDER_TOTAL', 'Order Total');
define('TABLE_HEADING_PAYMENT_INFO', 'Payment Information');
define('TABLE_HEADING_VIEW_INVOICE_CN', 'View Invoice and Credit Note');
define('TABLE_HEADING_PREV_DELIVERY', 'Delivered Qty');
define('TABLE_HEADING_OP_ORDERED', 'OP Ordered');
define('TABLE_HEADING_OP_DELIVERED', 'OP Delivered');
define('TABLE_HEADING_PRICE', 'Price');
define('TABLE_HEADING_PRICE_EXCLUDING_TAX', 'Price (ex)');
define('TABLE_HEADING_PRICE_INCLUDING_TAX', 'Price (inc)');
define('TABLE_HEADING_PRODUCTS', 'Product Name');
define('TABLE_HEADING_PRODUCTS_DESC', 'Path > Item [Product Type; Product ID]<br>(SPK=Static Package; DPK=Dynamic Package; SIN=Single Product; PWL=Powerleveling; CDK=CD-Key)');
define('TABLE_HEADING_PRODUCTS_LOCATION', 'SIN Location/<br>PWL Assignment');
define('TABLE_HEADING_PURCHASE_ETA', 'Purchase ETA (Countdown - Day/Hour)');
define('TABLE_HEADING_PRODUCTS_MODEL', 'Model');
define('TABLE_HEADING_QUANTITY', 'Ordered Qty');
define('TABLE_HEADING_REMARKS', 'Remarks');
define('TABLE_HEADING_SHOPPING_CART_TYPE', 'Cart Type/<br>Extra Item Details');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_STAT_DELIVERED_AMT', 'Delivered Value');
define('TABLE_HEADING_STAT_CANCELED_AMT', 'Refunded Value');
define('TABLE_HEADING_STAT_REVERSED_AMT', 'Reversed Value');
define('TABLE_HEADING_STOCK_QUANTITY', 'Actual Stock');
define('TABLE_HEADING_TAX', 'Tax');
define('TABLE_HEADING_TOTAL', 'Total');
define('TABLE_HEADING_TOTAL_EXCLUDING_TAX', 'Amount (ex)');
define('TABLE_HEADING_TOTAL_INCLUDING_TAX', 'Amount (inc)');
define('TABLE_HEADING_MAXMIND_INFO', 'MaxMind Information');
define('TABLE_HEADING_DEVICE_INFORMATION', 'Device Information');
define('TABLE_HEADING_PIPWAVE_PREDICTION', 'pipwave Prediction');

// Compensation/Loss Tracking
define('TABLE_HEADING_COMPENSATE_FOR', 'For');
define('TABLE_HEADING_COMPENSATE_ITEM', 'Product ID<br>(Single / Custom Product or Store Credit)');
define('TABLE_HEADING_COMPENSATE_QTY', 'Qty');
define('TABLE_HEADING_COMPENSATE_DELIVERED_QTY', 'Delivered Qty');
define('TABLE_HEADING_COMPENSATE_UNIT_PRICE', 'Price');
define('TABLE_HEADING_COMPENSATE_TOTAL_AMOUNT', 'Amount');
define('TABLE_HEADING_COMPENSATE_ACCIDENT_VALUE', 'Accident');
define('TABLE_HEADING_COMPENSATE_NON_ACCIDENT_VALUE', 'Non-Accident');
define('TABLE_HEADING_COMPENSATE_BY_SUPPLIER_VALUE', 'Supplier');

define('TEXT_COMPENSATE_ISSUE_STORE_CREDIT', 'Issue Store Credit');
define('TEXT_COMPENSATE_ACCIDENT_VALUE', 'Accident: %s');
define('TEXT_COMPENSATE_NON_ACCIDENT_VALUE', 'Non-Accident: %s');
define('TEXT_COMPENSATE_SUPPLIER_VALUE', 'Supplier: %s');
// End of Compensation/Loss Tracking

define('SUB_TABLE_HEADING_ORDER_STATUS', '&nbsp;');
define('SUB_TABLE_HEADING_TOTAL_ORDER', 'Total #');
define('SUB_TABLE_HEADING_TOTAL_AMOUNT', 'Total $');

define('ENTRY_CURRENT_AFFILIATE_INFO', 'Current Affiliate:');
define('ENTRY_OLD_AFFILIATE_INFO', 'Old Affiliate:');
define('ENTRY_BILLING_ADDRESS', 'Billing Address:');
define('ENTRY_CREDIT_CARD_EXPIRES', 'Credit Card Expires:');
define('ENTRY_CREDIT_CARD_NUMBER', 'Credit Card Number:');
define('ENTRY_CREDIT_CARD_OWNER', 'Credit Card Owner:');
define('ENTRY_CREDIT_CARD_TYPE', 'Credit Card Type:');
define('ENTRY_CUSTOMER', 'Customer:');
define('ENTRY_G2G_USERNAME', 'G2G Username:');
define('ENTRY_CUSTOMER_DISCOUNT', 'Customer Discount:');
define('ENTRY_CUSTOMER_DOB', 'Date of Birth:');
define('ENTRY_CUSTOMER_FIRST_COMPLETED_ORDER', 'First Completed Order:');
define('ENTRY_CUSTOMER_GENDER', 'Gender:');
define('ENTRY_CUSTOMER_GROUPS_NAME', 'Customer Group:');
define('ENTRY_CUSTOMER_PHONE_VERIFICATION', 'Phone Verification:');
define('ENTRY_CUSTOMER_SIGNUP_DATE', 'Sign Up Date:');
define('ENTRY_CUSTOMER_STATUS', 'Account Status:');
define('ENTRY_DATE_LAST_MODIFIED', 'Last Modified On:');
define('ENTRY_OP_INFO', 'OP Information:');
define('ENTRY_G2G_INFO', 'G2G Points Information:');
define('ENTRY_DATE_LAST_UPDATED', 'Date Last Updated:');
define('ENTRY_DATE_PURCHASED', 'Date Purchased:');
define('ENTRY_DELIVERY_TO', 'Delivery To:');
define('ENTRY_FLAG', 'Flagged:');
define('ENTRY_NEW_COMPLETION_PERCENT', 'New Completed%');
define('ENTRY_NOTIFY_COMMENTS', 'Append Comments:');
define('ENTRY_NOTIFY_CUSTOMER', 'Notify Customer');
define('ENTRY_NOTIFY_SUPPLIER', 'Notify Supplier:');
define('ENTRY_ORDER_COMMENTS', 'Comments:');
define('ENTRY_ORDER_ID', 'Order Number:');
define('ENTRY_ORDER_DATETIME', 'Ordered On:');
define('ENTRY_ORDER_FOLLOW_UP_DATETIME', 'Follow-up:');
define('ENTRY_ORDER_STATUS', 'Order Status:');
define('ENTRY_ORDERING_IP', 'Ordering IP:');
define('ENTRY_PAYMENT_METHOD', 'Payment Method:');
define('ENTRY_REQUEST_METHOD', 'Refund Method Requested:');
define('ENTRY_PRINTABLE', 'Print Invoice');
define('ENTRY_SHIP_TO', 'SHIP TO:');
define('ENTRY_SHIPPING', 'Shipping:');
define('ENTRY_SHIPPING_ADDRESS', 'Shipping Address:');
define('ENTRY_SOLD_TO', 'SOLD TO:');
define('ENTRY_STATUS', 'Status:');
define('ENTRY_STORE_SC_BALANCE', 'OG SC Balance:');
define('ENTRY_STORE_SC_BALANCE_G2G', 'G2G SC Balance:');
define('ENTRY_STORE_RC_BALANCE', 'Reversible SC (RSC):');
define('ENTRY_STORE_NRC_BALANCE', 'Non-Reversible SC (NRSC):');
define('ENTRY_STORE_ACCOUNT_BALANCE', 'Withdrawable SC (WSC):');
define('ENTRY_OP_BALANCE', 'OP Balance:');
define('ENTRY_G2G_TOKEN_BALANCE', 'G2G Points Balance:');
define('ENTRY_AFT_WHITELISTED', 'AFT Whitelisted:');
define('ENTRY_SUB_TOTAL', 'Sub-Total:');
define('ENTRY_TAX', 'Tax:');
define('ENTRY_TOTAL', 'Total:');
define('ENTRY_PHONE_TYPE', 'Phone Type:');
define('ENTRY_RISK_LEVEL', 'Risk Level:');
define('ENTRY_REQUESTED_DATE', 'Requested Date:');
define('ENTRY_INVOICDE', 'Invoice:');
define('ENTRY_CREDIT_NOTE', 'Credit Note:');

// custom product
define('ENTRY_ACCOUNT_USERNAME', 'Username');
define('ENTRY_ACCOUNT_PASSWORD', 'Password');
define('ENTRY_NEW_ACCOUNT_PASSWORD', 'New Password');
define('ENTRY_CHAR_NAME', 'Character Name');
define('ENTRY_REALM', 'Realm');
define('ENTRY_FACTION', 'Faction');
define('ENTRY_GAME', 'Game');
define('ENTRY_APPLICABLE_IP_ZONE', 'Applicable IP Zone:');

// Buttons
define('BUTTON_CUSTOMER_TIMEZONE', 'Get Customer TimeZone');
define('ALT_BUTTON_CUSTOMER_TIMEZONE', 'Get Customer TimeZone');
define('BUTTON_ORDER_STAT', 'Get Order Statistics');
define('ALT_BUTTON_ORDER_STAT', 'Get Order Statistics');

define('SELECT_OPTION_UPDATE_COMMENT', 'Update Comment');

define('TEXT_DATE_ORDER_CREATED', 'Date Created:');
define('TEXT_DATE_ORDER_LAST_MODIFIED', 'Last Modified:');
define('TEXT_INFO_HEADING_DELETE_ORDER', 'Delete Order');
define('TEXT_INFO_DELETE_INTRO', 'Are you sure you want to delete this order?');
define('TEXT_INFO_RESET_SOLD_CDKEY', 'Do you want to make these CD Keys / Time Cards available for purchase again?');
define('TEXT_INFO_RESTOCK_PRODUCT_QUANTITY', 'Restock product quantity');
define('TEXT_INFO_PAYMENT_METHOD', 'Payment Method:');
define('TEXT_INFO_ADD_TO_CART_ORDER', 'Add-to-Cart');
define('TEXT_INFO_PRE_ORDER', 'Pre-Order');
define('TEXT_INFO_PWL_ORDER_ID', 'PWL Order ID: <a href="%s" target="_blank">%s-%d</a>');
define('TEXT_INFO_EQUIVALENT_TO', 'Equivalent to:');
define('TEXT_ORDER_INFO_CDKEY_ID', 'ID: %s');
define('TEXT_ORDER_INFO_CDKEY_FILE', 'File: %s');
define('TEXT_ORDER_INFO_CDKEY_VIEWED', '<span class="redIndicator"><b>Delivered to Customer\'s OffGamers account</b></span>');
define('TEXT_ORDER_INFO_CDKEY_NOT_VIEWED', '<span class="greenIndicator"><b>Not Viewed</b></span>');
define('TEXT_ORDER_INFO_HLA_VIEWED', '<span class="redIndicator"><b>Delivered to Customer\'s G2G account</b></span>');
define('TEXT_ORDER_INFO_HLA_NOT_VIEWED', '<span class="greenIndicator"><b>Not Viewed</b></span>');
define('TEXT_ORDER_INFO_SC_TOTAL', 'SC:');
define('TEXT_ORDER_INFO_REFUND_TOTAL', 'Payment Refund: %s(%s)');
define('TEXT_ORDER_COMPENSATE_INFO', 'Added by: %s<br>Comments: %s');
define('TEXT_ORDER_PRODUCT_EXTRA_INFO', 'In-game ETA: %s<br>In-game Duration: %s');
define('TEXT_ORDER_PRODUCT_ID_INFO', 'PIMA ID: %s');
define('TEXT_ORDER_PRODUCT_CUSTOMER_ACC_INFO', 'Character Name: %s<br>Account Name: %s<br>Password: %s<br>WOW Account Name: %s');
define('TEXT_ORDER_WOW_ACCOUNT_NAME', 'WOW Account Name: %s');
define('TEXT_ALL_ORDERS', 'All Orders');
define('TEXT_NO_ORDER_HISTORY', 'No Order History Available');
define('TEXT_SET_AS_REMARK', 'Set as Order Remark');
define('TEXT_OPTION_NOT_APPLICABLE', 'n/a');
define('TEXT_PRODUCTS_ID', 'Product ID:');
define('TEXT_REMARK_MODIFIED_BY', 'Last changed by ');
define('TEXT_DELIVERY_NOTIFICATION', 'Delivery Notification');
if (!defined('TEXT_ORDER_NOT_BEEN_LOCKED'))	define('TEXT_ORDER_NOT_BEEN_LOCKED', '<span class="redIndicator">This Order has not been locked. Product Location information is hidden to prevent double-delivery of Products. Click the "LOCK" button on your right if you would like to edit this Order.</span>');
if (!defined('TEXT_LOCKED_ORDER_SEEN_BY_OWNER'))  	define('TEXT_LOCKED_ORDER_SEEN_BY_OWNER', 'This order has been locked by you on %s at %s. If you are not going to edit this Order or if you have finished your Partial Delivery, please remember to click "UNLOCK" button on your right so that others may lock it.');
if (!defined('TEXT_ORDER_LOCKED_BY_OTHER'))	define('TEXT_ORDER_LOCKED_BY_OTHER', '<span class="redIndicator">This order has been locked by %s on %s at %s. Product Location information is hidden to prevent double delivery of Products. You cannot edit this Order unless you lock the Order. To do so, you must first contact this person or any of the person in the following Admin Goups to unlock the Order. %s</span>');
if (!defined('TEXT_UNLOCK_OTHER_PEOPLE_ORDER'))	define('TEXT_UNLOCK_OTHER_PEOPLE_ORDER', '<span class="redIndicator">This order has been locked by %s on %s at %s. Product Location information is hidden to prevent double-delivery of Products. You have the Permission to unlock it by clicking the "UNLOCK" button on your right. Please note that unlocking this Order may result in double-delivery of Products.</span>');

define('TEXT_ORDER_OWNER_INACTIVE', '<span class="redIndicator">This order is not available for delivery due to customer is deactived / flagged as chargeback.</span>');
define('TEXT_INACTIVE_OWNER_CANNOT_DELIVER', '<span class="redIndicator">Please contact Shift Supervisor should you need to access ordered products information.</span>');

define('TEXT_CONFIRM_COMPLETE', 'Confirmed Completed');
define('TEXT_HIDE_INFO', '-Hidden-');
define('TEXT_ACCOUNT_CLOSURE_CONFIRMED', '*Account Deleted.');
define('TEXT_REAL_TIME_STAT', '<span class="fieldRequired">*</span>');
define('TEXT_REAL_TIME_STAT_DESC', '<span class="fieldRequired">*</span> Denotes Real Time Data');
define('TEXT_SYSTEM_HOLD_PURCHASE_QTY', '<span class="redIndicator">HOLD</span>');
define('TEXT_CHAR_INFO_CONTACT_SUPERVISOR', '<span class="redIndicator">** transfer directly into account contact SS</span>');

define('TEXT_IMAGE_MOUSEOVER_FLAG', "Information has been flagged. Click to edit Customer\'s information");

define('TEXT_COUNTRY_NOT_MATCH_PROFILE_COUNTRY', 'Country does not match profile country.');
define('TEXT_STATE_NOT_MATCH_PROFILE_STATE', 'State does not match profile state.');
define('TEXT_TELEPHONE_COUNTRY_NOT_MATCH_BILLING_COUNTRY', 'Telephone network country(%s) does not match billing address country.');
define('TEXT_NONE_SUPPLIERS_ARE_AVAILABLE', 'None Suppliers are Available');
define('TEXT_TASK_ASSIGNED_TO', 'Task assigned to: ');

define('TEXT_NO_SUPPLIERS_ARE_AVAILABLE', 'No suppliers are available');
define('TEXT_NOT_ASSIGN_TO_SUPPLIER', 'Not assigned to supplier');
define('TEXT_OP_CREDITED_ON', 'OP has been credited on %s');
define('TEXT_G2G_CREDITED_ON', 'G2G Points has been credited on %s');
define('TEXT_OP_WILL_BE_CREDITED_ON', 'OP will be credited in %d day(s)');
define('TEXT_G2G_WILL_BE_CREDITED_ON', 'G2G Points will be credited in %d day(s)');

define('TEXT_PIPWAVE_PREDICTION_ORDER', 'Order');
define('TEXT_PIPWAVE_PREDICTION_RESULT_LABEL', 'Result Label');
define('TEXT_PIPWAVE_PREDICTION_RESULT_SCORE', 'Result Score');
define('TEXT_PIPWAVE_PREDICTION_VERSION', 'Version');
define('TEXT_PIPWAVE_LAST_MODIFIED', 'Last Modified');
define('TEXT_PIPWAVE_CREATED_AT', 'Created At');

define('TEXT_TRANSACTION_IDENTIFIER', 'Order');
define('TEXT_DEVICE_ID', 'Device ID');
define('TEXT_VERIFICATION_RESULT', 'Verification Result');
define('TEXT_PROXY_DETECTED', 'Proxy Detected');
define('TEXT_PROXY_TYPE', 'Proxy Type');
define('TEXT_PROXY_IP', 'Proxy IP');
define('TEXT_PROXY_IP_CITY', 'Proxy IP City');
define('TEXT_PROXY_IP_COUNTRY', 'Proxy IP Country');
define('TEXT_PROXY_IP_COUNTRY_CODE', 'Proxy IP Country Code');
define('TEXT_PROXY_IP_ISP', 'Proxy IP ISP');
define('TEXT_PROXY_IP_ORGANIZATION', 'Proxy IP Organization');
define('TEXT_PROXY_IP_ATTRIBUTES', 'Proxy IP Attributes');
define('TEXT_TRUE_IP', 'IP');
define('TEXT_TRUE_IP_CITY', 'IP City');
define('TEXT_TRUE_IP_ISP', 'IP ISP');
define('TEXT_TRUE_IP_COUNTRY', 'IP Country');
define('TEXT_TRUE_IP_COUNTRY_CODE', 'IP Country Code');
define('TEXT_TRUE_IP_ORGANIZATION', 'IP Organization');
define('TEXT_TRUE_IP_ATTRIBUTES', 'IP Attributes');
define('TEXT_TOTAL_DEVICES', 'Total Devices Detected From This Account');
define('TEXT_DEVICE_FIRST_SEEN', 'Device First Seen');
define('TEXT_NUMBER_OF_DEVICES_EXIST', 'No Of Devices Exist In This Account');
define('TEXT_YES', 'Yes');
define('TEXT_NO', 'No');
define('TEXT_SUCCESS', 'Success');
define('TEXT_FAILED', 'Failed');

// Locking / Unlocking log tag
if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))	define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');

define('LINK_ORDER_HISTORY', 'Order history');
define('LINK_PROGRESS_REPORT', 'Progress Report');
define('LINK_UNDELIVER_CDKEY', 'Release/Unassign this code');
define('LINK_UNDELIVER_CDKEY_MOUSEOVER', "Click to reset delivery of this code and set its' status to Available.");
define('INFO_VERIFIED', 'Information Been Verified');
define('INFO_NOT_VERIFIED', 'Information Not Been Verified');
define('INFO_VERIFIED_LINK', 'Verify Information Link');
define('INFO_UNVERIFIED_LINK', 'Unverify Information Link');
define('INFO_CHANGED', 'Information Changed By Customer');

// JavaScript Message
define('JS_WARNING_QTY_DEDUCTION_NA', 'Please note that &quot;Undeliver&quot; option is not applicable for this product');
define('JS_ERROR_PURCHASE_ETA_MISSING', 'Please kindly enter the Purchase ETA (days) for this order since either this order has powerleveling package or this customer currently has powerleveling package in other processing orders');
define('JS_ERROR_SC_ISSUE_SC', 'Cannot issue store credit for Store Credit product type! Deliver / Refund box value has been reset to 0.');
define('JS_ERROR_INVALID_ONECARD_VOUCHER_CARD', "Please enter a valid OneCard's voucher code.");


define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_SUBJECT', 'Affiliate Sales #%d (Order Amount: %s)');
define('EMAIL_TEXT_AFFILIATE_NOTIFICATION_INFO', 'There is sales referred by you as below:'."\n\n".'Order Date: %s'."\n".'Order ID: %s'."\n".'Order Amount: %s'."\n");
define('EMAIL_TEXT_PAYMENT_INFO_NOTIFICATION_SUBJECT', '%s #%d %s (%s)');
define('EMAIL_TEXT_PAYMENT_INFO', 'Order Date: %s'."\n".'Order ID: %s'."\n".'Order Amount: %s'."\n".'Order Status: %s'."\n".'Payment Method: %s'."\n".'Customer Name: %s' . "\n".'Update User: %s (%s)' . "\n\n" . "There is an update in payment information for this order:\n" . '%s');

define('EMAIL_TEXT_MANUAL_POST_AUTH_NOTIFICATION_SUBJECT', 'WorldPay Manual Post Auth #%d %s');
define('EMAIL_TEXT_MANUAL_POST_AUTH_INFO', 'Order Date: %s'."\n".'Order ID: %s'."\n".'Order Amount: %s'."\n".'Order Status: %s'."\n".'Payment Method: %s'."\n".'Manual Authorised By: %s' . "\n\n\n" . "Manually post captured this order\n");

define('EMAIL_TEXT_MANUAL_POST_CAPTURE_NOTIFICATION_SUBJECT', 'Bibit Manual Post Capture #%d %s');
define('EMAIL_TEXT_MANUAL_POST_CAPTURE_INFO', 'Order Date: %s'."\n".'Order ID: %s'."\n".'Order Amount: %s'."\n".'Order Status: %s'."\n".'Payment Method: %s'."\n".'Manual Captured By: %s' . "\n\n\n" . "Manually post captured this order\n");

define('EMAIL_TEXT_ORDER_SUMMARY', 'Order Summary');
define('EMAIL_TEXT_PRODUCTS', 'Products');

define('EMAIL_TEXT_COMMENTS_UPDATE', 'Comment:' . "\n%s\n");

define('EMAIL_SUBJECT_CDKEY_NOT_RELEASED', EMAIL_SUBJECT_PREFIX . ' Sold CD Key(s) not released by order reversal.');
define('EMAIL_TEXT_CDKEY_NOT_RELEASED', "\n" . EMAIL_SEPARATOR .
										"\n Admin User : %s" .
										"\n Date and Time : %s" .
										"\n" . EMAIL_SEPARATOR .
										"\n Order ID : %s " .
										"\n Status Change : %s => %s" .
										"\n\n Assigned CD Key(s) / Time Card(s) in this order ;" .
										"%s");
define('EMAIL_TEXT_CDKEY_FILE_DETAIL', "\n %s X %s ");

// Compensation notification
define('EMAIL_TEXT_COMPENSATE_SUBJECT', 'Compensation/Loss Tracking #CO-%d');
define('EMAIL_TEXT_COMPENSATE_CONTENT', '<table border="0" cellspacing="2" cellpadding="2">'.
										'	<tr>' .
										'		<td height="5">&nbsp;</td>' .
										'	</tr>' .
										'	<tr>' .
										'		<td style="color:#000099";" align="center">Compensation for Order #CO-%d</td>' .
										'	</tr>' .
										'	<tr>' .
										'		<td>' .
										'			<table border="0" cellspacing="2" cellpadding="2">'.
										'				<tr>'.
										'					<td style="background-color:#FFD073; color:#000099";">Compensate For</td>'.
										'					<td style="background-color:#FFD073; color:#000099";">Product(ID)</td>'.
										'					<td style="background-color:#FFD073; color:#000099";" align="center">Quantity</td>'.
										'					<td style="background-color:#FFD073; color:#000099";" align="center">Delivered Qty</td>'.
										'					<td style="background-color:#FFD073; color:#000099";" align="center">Price</td>'.
										'					<td style="background-color:#FFD073; color:#000099";">Amount</td>'.
										'					<td style="background-color:#FFD073; color:#000099";">Accident</td>'.
										'					<td style="background-color:#FFD073; color:#000099";">Non-Accident</td>'.
										'					<td style="background-color:#FFD073; color:#000099";">Supplier</td>'.
										'					<td style="background-color:#FFD073; color:#000099";">Order Amount</td>'.
										'				</tr>'.
										'				<tr>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s(%s)</td>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="center">%d</td>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="center">%d</td>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="right" nowrap>%s</td>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="right" nowrap>%s</td>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="right" nowrap>%s</td>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;" align="right" nowrap>%s</td>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										'					<td style="background-color:#DFDFDF; color:#000"; font-size: 10px;">%s</td>'.
										'				</tr>'.
										'			</table>'.
										'		</td>'.
										'	</tr>'.
										'	<tr>' .
										'		<td><b>Update By:</b> %s</td>'.
										'	</tr>'.
										'	<tr>' .
										'		<td><b>Update IP:</b> %s</td>'.
										'	</tr>'.
										'	<tr>' .
										'		<td><b>Comments:</b>'."\n".'%s</td>'.
										'	</tr>'.
										'</table>');

define('ERROR_ORDER_ALREADY_AUTHORISED', 'Error: This order had been authorised.');
define('ERROR_ORDER_ALREADY_CAPTURED', 'Error: This order had been captured.');
define('ERROR_ORDER_CANNOT_BE_CAPTURED', 'Error: This order cannot be captured.');
define('ERROR_ORDER_CANNOT_COMPENSATE_PACKAGE', 'Error: You are not allowed to compensate package product.');
define('ERROR_ORDER_CD_KEY_VIEWED', 'Error: You are not allow to release this viewed CD Key.');
define('ERROR_ORDER_COMPENSATE_AMOUNT_NOT_TALLY', 'Error: Compensate value (Accident + Non-Accident + Supplier), %s, does not match total amount, %s.');
define('ERROR_ORDER_COMPENSATE_MISSING_PWL_INFO', 'Error: Please fill in Power Leveling info.');
define('ERROR_ORDER_COMPENSATE_MISSING_SUPPLIER', 'Error: Please select supplier from the drop down list.');
define('ERROR_ORDER_COMPENSATE_MISSING_SUPPLIER_VALUE', 'Error: Please enter supplier value.');
define('ERROR_ORDER_COMPENSATE_INVALID_STATUS', 'Error: Order is not in Processing status. Your compensation has not been successfully added.');
define('ERROR_ORDER_COMPENSATE_FAILED_SC', 'Error: Your compensation has not been successfully added.');
define('ERROR_ORDER_DOES_NOT_EXIST', 'Error: Order does not exist.');
define('ERROR_ORDER_INVALID_COMPENSATE_ACCIDENT_VALUE', 'Error: Invalid accident value.');
define('ERROR_ORDER_INVALID_COMPENSATE_DELIVERED_QTY', 'Error: The delivered quantity must be at least 1.');
define('ERROR_ORDER_INVALID_COMPENSATE_NON_ACCIDENT_VALUE', 'Error: Invalid non-accident value.');
define('ERROR_ORDER_INVALID_COMPENSATE_PRODUCT', 'Error: The selected product does not exists.');
define('ERROR_ORDER_INVALID_COMPENSATE_PRODUCT_PRICE', 'Error: Invalid product price.');
define('ERROR_ORDER_INVALID_COMPENSATE_QTY', 'Error: The compensate quantity must be at least 1.');
define('ERROR_ORDER_INVALID_COMPENSATE_SUPPLIER', 'Error: The selected supplier does not exists.');
define('ERROR_ORDER_INVALID_LOCK', 'Error: This order is not locked by you! Please lock it if you would like to edit this order.');
define('ERROR_ORDER_INVALID_CB_INACTIVE_STATUS', 'Error: This order is not available for delivery due to customer is deactived / flagged as chargeback.');
define('ERROR_ORDER_INVALID_PURCHASED_PRODUCT', 'Error: The compensate for product does not exists in this order.');
define('ERROR_ORDER_INVALID_ROLLBACK_COUPON_EXIST', 'Error: Order with coupon is not allow to rollback');
define('ERROR_ORDER_INSUFFICIENT_STORE_CREDIT', 'Error: The customer\'s existing store credit amount is insufficient for this order.');
define('ERROR_ORDER_PAYMENT_REFUND_NOT_SUCCESS', 'Error: Refund is failed.');
define('ERROR_ORDER_OVER_COMPENSATE_DELIVERED_QTY', 'Error: The delivered quantity cannot be greater than compensate quantity.');
define('ERROR_ORDER_POST_AUTHORISED', 'Error: This order has failed to post-authorise.');
define('ERROR_ORDER_PARTIAL_DELIVER_ON_DIRECT_TOPUP', 'Error: No partial delivery available for Direct Topup Product');
define('ERROR_ONLY_UNDELIVERY_DIRECT_TOPUP_ON_FAILED_STATUS', 'Error: Unable to undelivery un-processed or non-failed top-up');
define('ERROR_REMOTE_ADMINISTRATION_SETUP', 'Failed: Incomplete configuration for remote administration account.');
define('ERROR_TRANSACTION_ID_EXISTS', 'Failed: Transaction ID exists for this order.');
define('WARNING_ORDER_NOT_UPDATED', 'Warning: Nothing to change. The order was not updated.');
define('WARNING_PURCHASE_ETA_NOT_UPDATED', 'Warning: The purchase eta was not updated.');
define('WARNING_ORDER_NOT_ASSIGNED', 'Warning: The orders was not assigned.');
define('WARNING_ORDER_NOT_VIP_MODE', 'Warning: The game is not set to VIP mode.');
define('WARNING_CANCEL_NOT_PRE_AUTH_ORDER', 'Warning: Your are not allow to cancel an order which is not in pre-authorised status.');
define('WARNING_NOTHING_TO_INSERT', 'Warning: Nothing to insert.');
define('WARNING_CDKEY_DELIVERY_STOCK_NOT_ENOUGH', 'Warning: Insufficient Stock. Doing partial delivery on available keys.');
define('WARNING_CDKEY_OVER_DELIVERY', 'Warning: CD Key over delivery detected.');
//define('WARNING_REVERSE_STATUS_DENIED', 'Warning: You are not allowed to reverse this order.');
define('WARNING_MAXMIND_PHONE_INFO_UPDATED_FAIL', 'Warning: %s phone information update failed');
define('WARNING_CANNOT_COMPLETING_ORDER', 'Warning: This order has not been fully delivered. You cannot change the order status to "Completed". Please fully deliver the order and try again.');
define('WARNING_CANNOT_UNDELIVER_INQUEUE_TOPUP', 'Warning: Unable undeliver order, item in top-up queue.');
define('SUCCESS_ORDER_UPDATED', 'Success: Order has been successfully updated.');
if (!defined('SUCCESS_GENESIS_PROJECT_TURNED_OFF'))    define('SUCCESS_GENESIS_PROJECT_TURNED_OFF', 'Genesis Project is turned off.');
define('SUCCESS_GENESIS_PROJECT_TURNED_ON', 'Genesis Project is turned on.');
define('SUCCESS_ORDER_POST_AUTHORISED', 'Success: This order has been successfully post-authorised.');
define('SUCCESS_ORDER_POST_CAPTURED', 'Success: This order has been successfully post-captured.');
define('SUCCESS_ORDER_MANUAL_AUTHORISED', 'Success: This order has been successful authorised manually.');
define('SUCCESS_ORDER_PAYMENT_REFUNDED', 'Success: This order has been successful refunded.');
define('SUCCESS_PURCHASE_ETA_NOT_UPDATED', 'Success: The purchase eta has been successfully updated. Changes is logged in the order comment.');
define('SUCCESS_ORDER_ASSIGNED', 'Success: Order has been successfully assigned. Changes is logged in the order comment.');
define('SUCCESS_TRANSACTION_ID_INSERTED', 'Success: Transaction ID has been successfully inserted.');
define('SUCCESS_MAXMIND_INFO_UPDATED', 'Success: Maxmind info has been successfully updated.');
define('SUCCESS_MAXMIND_PHONE_INFO_UPDATED', 'Success: %s phone information has been successfully updated.');
define('SUCCESS_PAYMENT_INFO_INSERTED', 'Success: Payment info has been successfully inserted.');
define('SUCCESS_PAYMENT_INFO_UPDATED', 'Success: Payment info has been successfully updated.');
define('SUCCESS_CDKEY_DELIVERED', 'Success: CD Key successfully delivered. ID %s');
define('SUCCESS_CDKEY_UNDELIVERED', 'Success: CD Key successfully undelivered. ID %s');
define('SUCCESS_COMPENSATE_ITEM', 'Success: Compensate item has been successfully performed.');

// Store Credit
define('ERROR_SC_STAT_USER_NOT_EXISTS', 'Error: Customer does not exists');
define('ERROR_SC_STAT_SC_TYPE_NOT_EXISTS', 'Error: Invalid store credit type');
define('ERROR_EXCEED_DAILY_CREDIT_LIMIT', 'Error: Exceed daily credit limit.');
define('ERROR_SC_DELIVER', 'Error: Store Credit Failed to Deliver. Please Re-try.');
define('ERROR_SC_REVERSE', 'Error: Store Credit Failed to Reverse. Please Re-try.');

// Paypal Account Verification E-mail
define('TEXT_PAYPAL_EMAIL_VERIFY_INSTRUCTION_CONTENT', "\n\n\n" . 'Please kindly verify this e-mail account by clicking on the following link or copy and paste the link to your browser.  This link will confirm that you are the owner of this e-mail address and we will not ask for any PayPal  information in the page.  Please understand that this step is taken to protect PayPal account owners from unauthorized charges and this is a one-time process unless you change your PayPal e-mail.' . "\n\n");
define('TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT1', 'You have made a PayPal payment of ');
define('TEXT_PAYPAL_PAYMENT_MAKE_EMAIL_CONTENT2', ' to %s using the following account:' . "\n");
define('TEXT_PAYPAL_EMAIL_VERIFY_ENDING_CONTENT', "\n\n\n" . 'If you have not made any purchase at %s, please report this fraudulent use to %s immediately and we also advise you to report the case to PayPal at their website and change your PayPal password immediately.  Thank you for shopping at %s.');
define('TEXT_PAYPAL_VERIFICATION_TITLE', 'PayPal Account Verification');
define('TEXT_NOTICE_OF_PAYPAL_VERIFICATION_SEND_TITLE', 'PayPal Account Verification Notice');
define('TEXT_PAYPAL_EMAIL_VERIFICATION_NOTICE_HEADING_CONTENT', "\n\n" . 'In order to protect the owner of the PayPal account from fraudulent use, a verification e-mail with "Subject: ');
define('TEXT_PAYPAL_EMAIL_VERIFICATION_NOTICE_ENDING_CONTENT', '" has been sent to the e-mail address shown above.  Please kindly check the e-mail account and follow the instructions stated to verify your PayPal e-mail account.  Thank you for helping us to serve you better.');
define('REMARK_PAYPAL_VERIFICATION_EMAIL_SENT', 'Paypal verification e-mail sent.');
define('REMARK_PAYPAL_VERIFICATION_EMAIL_NOTICE_SENT', 'Paypal verification e-mail notice sent.');

//direct top-up
define('BUTTON_CHECK_TOPUP_STATUS','Check Top-up status');
define('LINK_CHECK_TOPUP_STATUS','Check Top-up status');

// Products Supplier list
define('TABLE_HEADING_LOG_DATE', 'Log Date');
define('TABLE_HEADING_MESSAGE', 'Message');
define('TABLE_HEADING_ADDED_BY', 'Added By');

// G2G Product Info
define('TEXT_G2G_PRODUCT_LISTING_INFO', 'G2G Product Listing Information');
define('TEXT_C2C_PRODUCTS_LISTING_ID', 'Listing ID');
define('TEXT_PRODUCTS_TITLE', 'Product Title');
define('TEXT_PRODUCTS_DESCRIPTION', 'Product Description');
define('TEXT_TRADE_URL', 'Trade Url');
define('TEXT_BUSINESS_HR_FROM', 'Business Hr From');
define('TEXT_BUSINESS_HR_TO', 'Business Hr To');
define('TEXT_ONLINE_HR', 'Online Hr');
define('TEXT_OFFLINE_HR', 'Offline Hr');
define('TEXT_NON_BUSINESS_HR', 'None Business Hr');
define('TEXT_LISTING_ATTRIBUTES', 'Listing Attributes');
define('TEXT_DELIVERY_INFO', 'Delivery Information');
define('TEXT_ORDER_ESCALATED', 'Order Escalated');

// HLA, Currency Automailling template
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_CONTECT1', 'The seller has been notified to prepare your order. You may also <a href="https://www.g2g.com/order/buyOrder/index">login to your G2G account</a> order details page and click "Chat Now" to contact the seller for delivery arrangement. The seller will respond to you as soon possible.<br><br>');
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_CONTECT2', 'Upon receiving the delivery, remember to login to your order page and confirm the amount received. Our system will automatically confirm the delivery on your behalf if there is no response after 72 hours.<br><br>');
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_MYACCOUNT_LINK', '<a href="https://www.g2g.com/order/buyOrder/index"><b>https://www.g2g.com/order/buyOrder/index</b></a><br><br>');
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_IMPORTANT_NOTICE', '<p style="color:#99999; padding:6px 0 6px 0; border-bottom:2px dotted #999999; border-top:2px dotted #999999;"><b><font color=\'red\'>IMPORTANT NOTE:</font></b><br /><br />DON’T return the in-game items under any circumstances after you have received it.<br/>DON’T purchase the product listing as a form of payment or as an exchange for other goods.<br/>Read more about <a href="https://support.g2g.com/support/solutions/articles/**********">trading safety guidelines</a>.<br><br>');
define('TEXT_CUR_DELIVERY_AUTOMAILING_EMAIL_MORE_INFO_LINK', '<a href="https://support.g2g.com/support/solutions/folders/**********">Read more</a> about buying at G2G.');

define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_CONTECT1', 'The seller has been notified to prepare your order. You may also <a href="https://www.g2g.com/order/buyOrder/index">login to your G2G account</a> order details page and click "Chat Now" to contact the seller for delivery arrangement. The seller will respond to you as soon possible.<br><br>');
define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_CONTECT2', 'Please ensure the account you received is in working condition and secure all access and recovery options before confirming receipt of your order. Our system will automatically confirm the delivery on your behalf if there is no response after 72 hours.');
define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_MYACCOUNT_LINK', '<a href =\'https://www.g2g.com/order/buyOrder/index\'>https://www.g2g.com/order/buyOrder/index</a><br><br>');
define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_IMPORTANT_NOTICE',  '<p style="color:#99999; padding:6px 0 6px 0; border-bottom:2px dotted #999999; border-top:2px dotted #999999;"><b><font color=\'red\'>IMPORTANT NOTE:</font></b><br /><br />DON’T return the in-game items under any circumstances after you have received it.<br/>DON’T purchase the product listing as a form of payment or as an exchange for other goods.<br/>Read more about <a href="https://support.g2g.com/support/solutions/articles/**********">trading safety guidelines</a>.<br><br>');
define('TEXT_HLA_DELIVERY_AUTOMAILING_EMAIL_MORE_INFO_LINK', '<a href="https://support.g2g.com/support/solutions/folders/**********">Read more</a> about buying at G2G.');
?>