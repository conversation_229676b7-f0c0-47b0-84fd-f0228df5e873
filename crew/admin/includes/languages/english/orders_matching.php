<?php
/*
 	$Id: orders_matching.php,v 1.6 2010/01/18 03:51:29 wilson.sun Exp $

 	Copyright (c) 2007 Dynamic Podium
	
 	Released under the GNU General Public License
*/


define('HEADER_FORM_ORDERS_MATCHING_TITLE', 'Orders Matching');

define('ENTRY_ORDERS_MATCHING_REPORT_TYPE', 'Report Type');
define('ENTRY_CSV_SOURCE_FILE', 'Source CSV File');
define('ENTRY_ORDERS_MATCHING_START_LAST_VERIFIED_DATE', 'Start Last Verified Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDERS_MATCHING_END_LAST_VERIFIED_DATE', 'End Last Verified Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDERS_MATCHING_PAYMENT_GATEWAY', 'Payment Gateway');

define('TABLE_HEADING_ORDERS_MATCHING_DATETIME', 'Date/Time');
define('TABLE_HEADING_ORDERS_MATCHING_ORDER_ID', 'Order ID');
define('TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_RECEIVED', 'Order Amount Received');
define('TABLE_HEADING_ORDERS_MATCHING_CURRENCY', 'Currency');
define('TABLE_HEADING_ORDERS_MATCHING_PG_CODE', 'PG Code');
define('TABLE_HEADING_ORDERS_MATCHING_CUSTOMER_ID', 'Customer ID');
define('TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_BREAKDOWN', 'Order Amount Breakdown');
define('TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_PG', 'PG');
define('TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_SUBTOTAL', 'Sub-Total');
define('TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_RSC', 'RSC');
define('TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_NRSC', 'NRSC');
define('TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_SC', 'SC');
define('TABLE_HEADING_ORDERS_MATCHING_ORDER_AMOUNT_TOTAL', 'Total');

define('TABLE_HEADING_ORDERS_MATCHING_ORDER_STATUS', 'Order Status');
define('TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES', 'Result Codes');
define('TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES_01', '01');
define('TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES_02', '02');
define('TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES_03', '03');
define('TABLE_HEADING_ORDERS_MATCHING_RESULT_CODES_04', '04');

define('TABLE_HEADING_ACTIVITIES_MATCHING_ORDER_DATE', 'Order Date');
define('TABLE_HEADING_ACTIVITIES_MATCHING_ORDER_ID', 'Order ID');
define('TABLE_HEADING_ACTIVITIES_MATCHING_CURRENCY', 'Currency');
define('TABLE_HEADING_ACTIVITIES_MATCHING_PG_CODE', 'PG Code');
define('TABLE_HEADING_ACTIVITIES_MATCHING_PG_AMOUNT', 'PG Amount');
define('TABLE_HEADING_ACTIVITIES_MATCHING_PG_SURCHARGE', 'PG Surcharge');
define('TABLE_HEADING_ACTIVITIES_MATCHING_CURRENT_DELIVERIES', 'Current Deliveries');
define('TABLE_HEADING_ACTIVITIES_MATCHING_PREVIOUS_DELIVERIES', 'Previous Deliveries');
define('TABLE_HEADING_ACTIVITIES_MATCHING_UNDELIVERIES', 'Undeliveries');
define('TABLE_HEADING_ACTIVITIES_MATCHING_REFUND', 'Refund');
define('TABLE_HEADING_ACTIVITIES_MATCHING_CHARGE_BACK', 'Charge Back');
define('TABLE_HEADING_ACTIVITIES_MATCHING_PAYMENT_GATEWAY', 'Payment Gateway');
define('TABLE_HEADING_ACTIVITIES_MATCHING_ORDER_INFO', 'Order Info');
define('TABLE_HEADING_ACTIVITIES_MATCHING_TOTAL', 'Total');
define('TABLE_HEADING_ACTIVITIES_MATCHING_COMPENSATED_DELIVERIES', 'Compensated Deliveries');

define('TABLE_HEADING_ACTIVITIES_MATCHING_CURRENT_TOTAL_SC', 'Total SC');
define('TABLE_HEADING_ACTIVITIES_MATCHING_CURRENT_TOTAL_PG', 'Total PG');
define('TABLE_HEADING_ACTIVITIES_MATCHING_CURRENT_TOTAL_DC', 'Total Discount Coupon');
define('TABLE_HEADING_ACTIVITIES_MATCHING_UNDELIVER_SC', 'SC');
define('TABLE_HEADING_ACTIVITIES_MATCHING_UNDELIVER_PG', 'PG');
define('TABLE_HEADING_ACTIVITIES_MATCHING_UNDELIVER_DC', 'Discount Coupon');
define('TABLE_HEADING_ACTIVITIES_MATCHING_REFUND_PG', 'in PG');
define('TABLE_HEADING_ACTIVITIES_MATCHING_REFUND_SC', 'in SC');
define('TABLE_HEADING_ACTIVITIES_MATCHING_REFUND_DC', 'in Discount Coupon');
define('TABLE_HEADING_ACTIVITIES_MATCHING_CB_RESOLVE', 'Resolve');
define('TABLE_HEADING_ACTIVITIES_MATCHING_CB_WIN', 'Win');
define('TABLE_HEADING_ACTIVITIES_MATCHING_CB_LOST', 'Lost');
define('TABLE_HEADING_ACTIVITIES_MATCHING_OT_USD_RATE', 'USD Rate');
define('TABLE_HEADING_ACTIVITIES_MATCHING_OT_TOTAL', 'Amount');
define('TABLE_HEADING_ACTIVITIES_MATCHING_OT_SURCHARGE', 'Surcharge');
define('TABLE_HEADING_ACTIVITIES_MATCHING_OT_GV', 'SC');
define('TABLE_HEADING_ACTIVITIES_MATCHING_OT_DC', 'Discount Coupon');
define('TABLE_HEADING_ACTIVITIES_MATCHING_OT_SUBTOTAL', 'Total');

// JavaScript Message
define('JS_ERROR_ORDERS_MATCHING_SEARCH_CRITERIA_NOT_ENTERED', 'Please kindly provide these search criteria: \n\n1. Source CSV File (Mandatory) \n2. Start Date and End Date (Mandatory)');
?>