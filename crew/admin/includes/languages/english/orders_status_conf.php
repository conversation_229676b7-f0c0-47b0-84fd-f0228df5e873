<?
/*
  	$Id: orders_status_conf.php,v 1.2 2009/06/02 08:36:58 weesiong Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Status Configuration');

define('TABLE_HEADING_STATUS_CONF_TRANSACTION_TYPE', 'Transaction Type');
define('TABLE_HEADING_STATUS_CONF_FROM_STATUS', 'From Status');
define('TABLE_HEADING_STATUS_CONF_TO_STATUS', 'To Status');
define('TABLE_HEADING_STATUS_CONF_USER_GROUPS', 'User Group');
define('TABLE_HEADING_STATUS_CONF_MANUAL_NOTIFICATION', 'Manual Notification');
define('TABLE_HEADING_STATUS_CONF_AUTO_NOTIFICATION', 'Auto Notification');
define('TABLE_HEADING_ACTION', 'Action');

define('ENTRY_TRANS_TYPE', 'Transaction Type');
define('ENTRY_CONF_FROM_STATUS', 'From Status');
define('ENTRY_CONF_TO_STATUS', 'To Status');
define('ENTRY_CONF_USER_GROUPS', 'User Group');
define('ENTRY_CONF_MANUAL_NOTIFICATION', 'Manual Notification<br><small>(In "Name &lt;Email&gt;" format. Use \',\' as delimiter for multiple recipient)</small>');
define('ENTRY_CONF_AUTO_NOTIFICATION', 'Auto Notification<br><small>(In "Name &lt;Email&gt;" format. Use \',\' as delimiter for multiple recipient)</small>');
define('ENTRY_CONF_PAYMENT_METHODS', 'Payment Methods');

define('ENTRY_COMMENT_STATUS', 'Comment Status');
define('ENTRY_COMMENT_ORDER', 'Sort Order');

define('TEXT_STATUS_ACTIVE', 'Active');
define('TEXT_STATUS_INACTIVE', 'Inactive');
define('TEXT_ICON_MANDATORY', 'Mandatory');
define('TEXT_ICON_SET_MANDATORY', 'Set Mandatory');
define('TEXT_ICON_OPTIONAL', 'Optional');
define('TEXT_ICON_SET_OPTIONAL', 'Set Optional');

define('LINK_ADD_CART_COMMENT', 'Add Comments');

//User Comments Statistic
define('HEADING_STATISTIC_TITLE', "Customer Comment's Statistics");

define('TABLE_HEADING_CART_COMMENTS_STATISTIC_OPTION', 'Options');
define('TABLE_HEADING_CART_COMMENTS_STATISTIC_COUNT', 'Count (%)');

define('ENTRY_COMMENTS_STATISTIC_EMPTY', 'Empty Comment');
define('ENTRY_COMMENTS_STATISTIC_NOTEMPTY', 'Comments');
define('ENTRY_COMMENTS_STATISTIC_TOTAL', 'Total');
?>