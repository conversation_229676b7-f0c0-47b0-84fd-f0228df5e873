<?php
/*
  	$Id: payment.php,v 1.25 2015/06/22 10:09:50 chingyen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_PAYMENT_LIST_CRITERIA_TITLE', 'Payment Lists Criteria');
define('HEADER_FORM_PAYMENT_LIST', 'Payment Lists');
define('HEADER_FORM_PAYMENT_EDIT', 'Edit Payment');

define('TABLE_HEADING_PAYMENT_ID', 'Payment ID');
define('TABLE_HEADING_PAYMENT_USER', 'Supplier/Customer Name');
define('TABLE_HEADING_PAYMENT_USER_EMAIL', 'Supplier/Customer Email');
define('TABLE_HEADING_PAYMENT_SUPPLIER_AGING', 'Supplier Aging (Month)');
define('TABLE_HEADING_PAYMENT_BAD_ORDERS_RATIO', 'CB Orders Ratio');
define('TABLE_HEADING_PAYMENT_ACC_STAT', 'Account Statement');
define('TABLE_HEADING_PAYMENT_REQUEST_AMOUNT', 'Request Amount');
define('TABLE_HEADING_PAYMENT_EX_RATE', 'Exchange Rate');
define('TABLE_HEADING_PAYMENT_PAYOUT_CURRENCY', 'Amount (%s)');
define('TABLE_HEADING_PAYMENT_PAYOUT_TAX_CURRENCY', 'Withdraw Fee Tax (%s)');
define('TABLE_HEADING_PAYMENT_NET_PAYABLE_AMOUNT', 'Net Payable Amount (%s)');
define('TABLE_HEADING_PAYMENT_WIDTHDRAW_AMOUNT', 'Withdraw Amount (%s)');
define('TABLE_HEADING_PAYMENT_WIDTHDRAW_FEE', 'Withdraw fee (%s)');
define('TABLE_HEADING_PAYMENT_PAYOUT_AMOUNT', 'Amount');
define('TABLE_HEADING_PAYMENT_REMARK', 'Remarks');
define('TABLE_HEADING_PAYMENT_REFERENCE', 'Payment Reference');
define('TABLE_HEADING_PAYMENT_ACTION', 'Action');
define('TABLE_HEADING_PAYMENT_BENEFICIARY_INFO', 'Beneficiary Information');
define('TABLE_HEADING_PAYMENT_INFO', 'Payment Information');

define('LINK_PAYMENT_AGING_STATISTIC', '[<a href="javascript:;" onClick="%s">Show Aging</a>]');

define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_BENEFICIARY_NOTIFIED', 'Beneficiary Notified');
define('TABLE_HEADING_COMMENTS', 'Comments');
define('TABLE_HEADING_CHANGED_BY', 'Changed By');
define('TABLE_HEADING_ACTION', 'Action');

define('ENTRY_PAYMENT_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', 'Payment ID');
define('ENTRY_PAYMENT_STATUS', 'Payment Status');
define('ENTRY_PAYMENT_METHOD', 'Payment Method');
define('ENTRY_PAYMENT_REFERENCE', 'Payment Reference');
define('ENTRY_PAYMENT_DATE_TIME', 'Payment Date');
define('ENTRY_DATE_LAST_MODIFIED', 'Last Modified On');
define('ENTRY_PAYMENT_BENEFICIARY', 'Beneficiary');
define('ENTRY_PAYMENT_BENEFICIARY_EMAIL', 'Beneficiary E-mail');
define('ENTRY_PAYMENT_TELEPHONE', 'Telephone Number');
define('ENTRY_PAYMENT_MOBILE', 'Mobile Phone');
define('ENTRY_PAYMENT_WITHDRAW_AMOUNT', 'Withdraw Amount');
define('ENTRY_PAYMENT_PAY_AMOUNT', 'Pay Amount');
define('ENTRY_PAYMENT_PO_NUMBER', 'PO Number');
define('ENTRY_PAYMENT_WITHDRAW_DETAILS', 'Withdraw Details');
define('ENTRY_PAYMENT_REMARK', 'Payment Remark');
define('ENTRY_PAYMENT_NOTIFY_BENEFICIARY', 'Show to Beneficiary');
define('ENTRY_CURRENT_PAYMENT_STATUS', 'Current Payment Status');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');

define('TEXT_PAYMENT_REMARKS', 'OffGamers Payment %s');
define('TEXT_G2G_PAYMENT_REMARKS', 'Payment %s');
define('TEXT_TOTAL_OVERALL_AMOUNT', 'Overall Total:');
define('TEXT_TOTAL_SELECTED_AMOUNT', 'Selected Total:');
define('TEXT_TOTAL_SELECTED_RECORDS', '#:');
define('TEXT_PAYMENT_NOT_APPLICABLE', '-');

define('TEXT_ANY', '<i>Any</i>');
define('TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED', 'Please be informed that your payment has been successfully processed by our Payment Institution service provider, please allow 1-7 business working days for the funds to show up in your account.<br>Kindly contact <a href="mailto:<EMAIL>"><EMAIL></a> if you require further assistance. Thank you.');

define('LINK_PAYMENT_ACCOUNT_STATEMENT', '<a href="%s" target="_blank" %s>Account Statement</a>');

define('SUCCESS_PAYMENT_BATCH_ACTION_EXRATE', 'Success: Batch action of exchange rate has been successfully performed.');
define('SUCCESS_PAYMENT_BATCH_ACTION_PAYREF', 'Success: Batch action of payment reference has been successfully performed.');
define('SUCCESS_PAYMENT_UPDATE', 'Success: The payment #%d has been successfully updated.');
define('SUCCESS_PAYMENT_CANCELLATION', 'Success: The payment #%d has been successfully cancelled.');
define('WARNING_PAYMENT_UPDATED_BY_SOMEONE', 'Warning: This payment just update by someone! Therefore no any update from you is done!');
define('WARNING_PAYMENT_TRANSACTION_NOT_UPDATED', "Warning: The payment #%d is not updated. \n这个提款没有更新");
define('ERROR_PAYMENT_BATCH_MASS_PAYMENT_NOT_SUPPORTED', 'Error: Mass payment is not available for the selected payment method.');
define('ERROR_PAYMENT_TRANSACTION_MISSING_PAYMENT_REFERENCE', 'Error: Payment transaction is missing payment reference.');
define('ERROR_PAYMENT_TRANSACTION_INSUFFICIENT_BALANCE', "Error: Not enough balance to proceed with payment transaction #%d. \n余额不足够  \n\n(Total available balance = total current balance - total reserve amount) ");
define('ERROR_PAYMENT_TRANSACTION_DISABLED_WITHDRAWAL', "Error: Payment withdrawal has been disabled.");
define('ERROR_PAYMENT_TRANSACTION_NO_ACCOUNT', "Error: User account not found for payment #%d.");
define('ERROR_PAYMENT_TRANSACTION_CUSTOMER_INACTIVE', "Error: User account inactive for payment #%d.");
define('ERROR_PAYMENT_BATCH_MASS_PAYMENT_MISSING_FILE', 'Error: Missing file for mass payment import.');

define('WARNING_MASS_PAYMENT_IMPORT_FAILED_TRANSACTION', "Warning: The payment #%d has failed in payment gateway.");
define('WARNING_MASS_PAYMENT_IMPORT_FAILED_PAYMENT_METHOD_FIELDS', "Warning: The payment #%d does not have matching payment method info.");
define('WARNING_MASS_PAYMENT_IMPORT_FAILED_AMOUNT_CHECKING', "Warning: The payment #%d does not have the matching payment amount transacted.");
define('WARNING_MASS_PAYMENT_IMPORT_FAILED_STATUS', "Warning: The payment #%d couldn't get processed due to wrong status.");
define('WARNING_MASS_PAYMENT_IMPORT_FAILED_UNKNOWN_STATUS', "Warning: The payment #%d not processed due to unknown status.");

define('JS_ERROR_PAYMENT_NO_PERMISSION', 'You have no permission for this action');
define('JS_ERROR_PAYMENT_TRANS_NOT_EXISTS', 'The payment transaction does not exists');
define('JS_ERROR_PAYMENT_TRANS_MODIFIED', "This payment just update by someone \n这笔款项已被某人更新");
define('JS_ERROR_PAYMENT_TRANS_INVALID_EX_RATE', 'Please provide a valid exchange rate');
define('JS_ERROR_PAYMENT_TRANS_UNKNOWN_STATUS', 'Unknown transaction status');
define('JS_ALERT_PAYMENT_CONFIRM_UPDATE', 'Are you sure to complete this payment?\n你确定要完成这笔款项吗？');


// Email definitions
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT', "Payment Update #%s");

define('EMAIL_PAYMENT_TEXT_TITLE', 'Your payment has been updated to the following status.');
define('EMAIL_PAYMENT_TEXT_SUMMARY_TITLE', 'Payment Summary:');
define('EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER', 'Payment Number: %s');
define('EMAIL_PAYMENT_TEXT_PAID_AMOUNT', 'Amount Paid: %s');
define('EMAIL_PAYMENT_TEXT_PAYMENT_DATE', 'Payment Date: %s');
define('EMAIL_PAYMENT_TEXT_COMMENTS', 'Comment:' . "\n%s\n");
define('EMAIL_PAYMENT_TEXT_STATUS_UPDATE', "Status: %s");

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', G2G_EMAIL_FOOTER);

// CronJob
define('ENTRY_CRONJOB_WITHDRAW_CURRENT_BALANCE', 'Current Balance:');
define('ENTRY_CRONJOB_WITHDRAW_RESERVE_AMOUNT', 'Reserve:');
define('ENTRY_CRONJOB_WITHDRAW_AVAILABLE_BALANCE', 'Available for Withdrawal:');
define('ENTRY_CRONJOB_WITHDRAW_AMOUNT', 'Withdrawal Amount:');
define('ENTRY_CRONJOB_WITHDRAW_PAYMENT_ACCOUNT', 'Withdraw To:');
define('ENTRY_CRONJOB_WITHDRAW_FINAL_AMOUNT', 'Receivable Amount:');

define('ENTRY_CRONJOB_WITHDRAW_PAYMENT_METHOD', 'Payment Method:');
define('ENTRY_CRONJOB_WITHDRAW_MIN_AMT', '&nbsp;- Minimum Amount:');
define('ENTRY_CRONJOB_WITHDRAW_MAX_AMT', '&nbsp;- Maximum Amount:');
define('ENTRY_CRONJOB_WITHDRAW_FEES', '&nbsp;- Withdrawal Fees:');

define('COMMENT_CRONJOB_WITHDRAW_FUNDS_REQUESTED', 'Request funds withdraw to %s(%s)');

define('TEXT_CRONJOB_NO_WITHDRAW_LIMIT', 'Any');
define('TEXT_CRONJOB_WITHDRAW_FEES_FREE', 'No Fees');
define('TEXT_LAST_STATUS_DATE', 'Last %s Date');
define('TEXT_ENTRY_DATE', 'Entry Date');

define('TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G', 'If you do not receive this payment by %s you must contact <a href="mailto:<EMAIL>"><EMAIL></a> by %s or we will assume that the full payment has been received by the intended beneficiary and we will not hold any further responsibility and/or liability.');

?>