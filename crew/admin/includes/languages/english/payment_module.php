<?
/*
  	$Id: payment_module.php,v 1.3 2010/01/11 04:18:54 wilson.sun Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

if ($_REQUEST['action']=='new_pm') {
	define('HEADING_TITLE', 'Add Payment Method');
} else {
	define('HEADING_TITLE', 'Payment Method');
}

define('TABLE_HEADING_PAYMENT_METHOD_TITLE', 'Payment Method');
define('TABLE_HEADING_PAYMENT_METHOD_STATUS', 'Status');
define('TABLE_HEADING_PAYMENT_FEES_WITHDRAW_MIN', 'Minimum Payment');
define('TABLE_HEADING_PAYMENT_FEES_WITHDRAW_MAX', 'Maximum Payment');
define('TABLE_HEADING_PAYMENT_FEES_BEARER', 'Who bear fees?');
define('TABLE_HEADING_PAYMENT_TRANSACTION_FEES', 'Transaction Fees');
define('TABLE_HEADING_PAYMENT_METHOD_REQUIRED_INFO', 'Information Needed');
define('TABLE_HEADING_PAYMENT_METHOD_INFO', 'Info');
define('TABLE_HEADING_PAYMENT_METHOD_FIELD_TITLE', 'Field Title');
define('TABLE_HEADING_PAYMENT_METHOD_FIELD_TYPE', 'Field Type');
define('TABLE_HEADING_PAYMENT_METHOD_FIELD_STATUS', 'Status');
define('TABLE_HEADING_PAYMENT_METHOD_FIELD_MANDATORY', 'Mandarory');
define('TABLE_HEADING_PAYMENT_METHOD_FIELD_SORT_ORDER', 'Sort Order');

define('SECTION_PAYMENT_METHOD_SETTING', 'Payment Method Configuration');
define('SECTION_PAYMENT_FEES_SETTING', 'Payment Fees Configuration');

// Payment Method
define('ENTRY_PAYMENT_METHOD_SEND_TYPE', 'Payment Method Type');
define('ENTRY_PAYMENT_METHOD_SEND_TITLE', 'Payment Method Title');
define('ENTRY_PAYMENT_METHOD_SEND_STATUS', 'Status');
define('ENTRY_PAYMENT_METHOD_REQUIRED_INFO', 'Information Needed');
define('ENTRY_PAYMENT_METHOD_CURRENCY', 'Currency');
define('ENTRY_PAYMENT_METHOD_RECEIVED_PERIOD', 'Estimate Payment Received Period<br><small>(mins)</small>');
define('ENTRY_PAYMENT_METHOD_EXCHANGE_RATE', 'Exchange Rate');
define('ENTRY_PAYMENT_METHOD_SUPPORT_MASS_PAYMENT', 'Export to Mass Payment Format');
define('ENTRY_PAYMENT_METHOD_ORDER', 'Sort Order');

// Payment Fees
define('ENTRY_PAYMENT_FEES_WITHDRAW_MIN', 'Minimum Payment (US $)');
define('ENTRY_PAYMENT_FEES_WITHDRAW_MAX', 'Maximum Payment (US $)');
define('ENTRY_PAYMENT_TRANSACTION_FEES', 'Transaction Fees (US $)');
define('ENTRY_PAYMENT_FEES_BEARER', 'Who bear the fees');

define('TEXT_STATUS_ACTIVE', 'Active');
define('TEXT_STATUS_INACTIVE', 'Inactive');
define('TEXT_PER_TRANSACTION_FEES', '(Per Transaction Fee, $)');
define('TEXT_AMOUNT_BASED_FEES', '(Amount Based Fee, %)');
define('TEXT_AMOUNT_BASED_FEES_MIN', '(min)');
define('TEXT_AMOUNT_BASED_FEES_MAX', '(max)');
define('TEXT_ANY', 'Any');
define('TEXT_PAYMENT_BELOW_MIN', 'Below min: %s');
define('TEXT_PAYMENT_CURRENCY_NOT_EDITABLE', '<span class="redIndicator">(Once added not editable)</span>');
define('TEXT_PAYMENT_FEES_BELOW_MAX', '(Above minimum)');
define('TEXT_PAYMENT_FEES_BELOW_MIN', '(Below minimum)');

// Payment Fields
define('ENTRY_PAYMENT_METHOD_FIELD_TITLE', 'Field Title');
define('ENTRY_PAYMENT_METHOD_FIELD_TYPE', 'Field Type');
define('ENTRY_PAYMENT_METHOD_FIELD_SETTING', 'Settings');
define('ENTRY_PAYMENT_METHOD_FIELD_STATUS', 'Status');
define('ENTRY_PAYMENT_METHOD_FIELD_MANDATORY', 'Mandatory Field');
define('ENTRY_PAYMENT_METHOD_FIELD_ORDER', 'Sort Order');
define('ENTRY_PAYMENT_METHOD_FIELD_PRE_TEXT', 'Pre Text');
define('ENTRY_PAYMENT_METHOD_FIELD_POST_TEXT', 'Post Text');

define('LINK_ADD_PAYMENT_METHOD', 'Add Payment Method');

define('SUCCESS_PAYMENT_METHOD_DELETE', 'Success: Payment method has been successfully deleted.');

define('SUCCESS_PAYMENT_FIELD_INSERT', 'Success: Payment info has been successfully added.');
define('SUCCESS_PAYMENT_FIELD_UPDATE', 'Success: Payment info has been successfully updated.');
define('SUCCESS_PAYMENT_FIELD_DELETE', 'Success: Payment info has been successfully deleted.');
define('ERROR_DUPLICATE_OPTION_VALUE', 'Error: Some option values is duplicated. Please change accordingly.');
define('ERROR_MISSING_SEND_PAYMENT_TYPE', 'Error: Please choose a payment method type.');
define('ERROR_MISSING_SEND_PAYMENT_TITLE', 'Error: Please enter payment method title.');
define('ERROR_MISSING_PAYOUT_CURRENCY', 'Error: Please select currency used in this payment method.');
?>