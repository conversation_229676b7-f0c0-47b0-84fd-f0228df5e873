<?
/*
  	$Id: payment_module_info.php,v 1.1 2011/06/21 03:03:55 wilson.sun Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('ENTRY_FORM_PM_ALIAS', 'Payment Alias:');
define('ENTRY_FORM_PM_SELECT_CURRENCY', 'Payment Currency:');
define('ENTRY_FORM_PM_SELECT_CURRENCY_INFO', '(Leave this empty for Store Credit Payment)');
define('ENTRY_FORM_PM_SELECT_PM', 'Payment Method:');

define('BUTTON_ADD_PM', ' Add Method ');
define('ALT_BUTTON_ADD_PM', 'Add Method');

define('SUCCESS_PM_ACCOUNT_BOOK_DELETED', 'PO Supplier\'s payment account book successfully deleted.');
define('ERROR_PM_ACCOUNT_BOOK_FULL', 'PO Supplier\'s payment account book is full.');
define('ERROR_PM_FIELD_INFO_REQUIRED', 'Error: Please provide "%s" information!');
define('ERROR_INVALID_PM_SELECTION', 'Error: Please select valid payment method.');
define('ERROR_INVALID_PM_BOOK_SELECTION', 'Error: You are editing an invalid payment account.');
define('ERROR_PM_ACCOUNT_BOOK_DELETE', 'Error: Failed to delete the PO Supplier\'s payment account book.');

?>