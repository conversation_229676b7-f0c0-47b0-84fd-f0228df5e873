<?
if ($_REQUEST['action']=='new_company') {
	define('HEADING_TITLE', 'Add PO Company');
} else if ($_REQUEST['action']=='edit_company') {
	define('HEADING_TITLE', 'Edit PO Company');
} else {
	define('HEADING_TITLE', 'PO Company List');
}

define('CATEGORY_GST', 'GST');
define('CATEGORY_PO_FOOTER_TEXT', 'PO Footer Text');

define('TABLE_HEADING_PO_COMPANY_ID', 'PO Company ID');
define('TABLE_HEADING_PO_COMPANY_CODE', 'Company Code');
define('TABLE_HEADING_PO_COMPANY_NAME', 'Company Name');
define('TABLE_HEADING_PO_COMPANY_STREET_ADDRESS', 'Street Address');
define('TABLE_HEADING_PO_COMPANY_SUBURB', 'Suburb');
define('TABLE_HEADING_PO_COMPANY_CITY', 'City');
define('TABLE_HEADING_PO_COMPANY_POSTCODE', 'Postcode');
define('TABLE_HEADING_PO_COMPANY_STATE', 'State');
define('TABLE_HEADING_PO_COMPANY_COUNTRY', 'Country');
define('TABLE_HEADING_PO_COMPANY_TEL', 'Telephone');
define('TABLE_HEADING_PO_COMPANY_FAX', 'Fax');
define('TABLE_HEADING_PO_COMPANY_GST', 'GST %');
define('TABLE_HEADING_PO_COMPANY_STATUS', 'Status');
define('TABLE_HEADING_ACTION', 'Actions');

define('ENTRY_PO_COMPANY_ID', 'PO Company ID:');
define('ENTRY_PO_COMPANY_CODE', 'Company Code:');
define('ENTRY_PO_COMPANY_NAME', 'Company Name:');
define('ENTRY_PO_COMPANY_CONTACT_NAME', 'Company Contact Name:');
define('ENTRY_PO_COMPANY_STATUS', 'Company Status:');
define('ENTRY_PO_COMPANY_STREET_ADDRESS', 'Street Address:');
define('ENTRY_PO_COMPANY_SUBURB', 'Suburb:');
define('ENTRY_PO_COMPANY_CITY', 'City:');
define('ENTRY_PO_COMPANY_POSTCODE', 'Postcode:');
define('ENTRY_PO_COMPANY_STATE', 'State:');
define('ENTRY_PO_COMPANY_COUNTRY', 'Country:');
define('ENTRY_PO_COMPANY_TEL', 'Telephone:');
define('ENTRY_PO_COMPANY_FAX', 'Fax:');
define('ENTRY_PO_COMPANY_GST', 'GST:');
define('ENTRY_PO_COMPANY_PO_FOOTER_TEXT', 'PO Footer Text:');

define('ENTRY_COMPANY_GST_ERROR', '&nbsp;<span class="errorText">Error in PO Company GST value!</span>');
define('ENTRY_COMPANY_CODE_ERROR', '&nbsp;<span class="errorText">Empty PO Company Code!</span>');
define('ENTRY_COMPANY_CODE_EXIST_ERROR', '&nbsp;<span class="errorText">Duplicate PO Company Code!</span>');
define('ENTRY_COMPANY_NAME_ERROR', '&nbsp;<span class="errorText">Empty PO Company Name!</span>');

define('SUCCESS_PO_COMPANY_ADDED', "Success: PO Company, %s, has been successfully added.");
define('SUCCESS_PO_COMPANY_UPDATED', "Success: PO Company, %s, has been successfully updated.");
define('ERROR_PO_COMPANY_INSERT_PERMISSION', "Error: You don't have permission to add new PO Company.");
define('ERROR_PO_COMPANY_INVALID_DATA_INSERT', "Error: Some PO Company's info is not complete or has issue.");
define('ERROR_PO_COMPANY_STATUS_EDIT', "Error: You don't have the permission to change company status.");
?>