<?
define('HEADING_TITLE', 'CDK Suppliers Payment Withdrawal');

define('ENTRY_SUPPLIER', 'Supplier:');
define('ENTRY_CURRENCY', 'Currency:');
define('ENTRY_AVAIL_AMOUNT', 'Available Amount:');
define('ENTRY_DISBURSEMENT_METHOD', 'Disbursement Method:');
define('ENTRY_DISBURSE_FOR_PO', 'Disburse for Purchase Orders:');
define('ENTRY_TOTAL', 'Total:');

define('TABLE_HEADING_PO_SELECT', 'Select');
define('TABLE_HEADING_PO_PO_REF_NO', 'PO Number');
define('TABLE_HEADING_PO_PO_AMOUNT', 'PO Amount');

define('ENTRY_WITHDRAW_PAYMENT_METHOD', 'Payment Method');
define('ENTRY_WITHDRAW_MIN_AMT', 'Minimum Amount');
define('ENTRY_WITHDRAW_MAX_AMT', 'Maximum Amount');
define('ENTRY_WITHDRAW_FINAL_AMOUNT', 'Final Amount');
define('ENTRY_TOTAL', 'Total:');

define('TEXT_NO_WITHDRAW_LIMIT', 'Any');
define('TEXT_SELECT_PO_SUPPLIER', 'Please select a supplier');
define('TEXT_SELECT_CURRENCY', 'Please select a currency');
define('TEXT_ZERO_AMOUNT', '0.00');
define('BUTTON_LOCK', 'Lock');
define('BUTTON_UNLOCK', 'Unlock');
define('ALT_BUTTON_LOCK', ' Lock ');
define('ALT_BUTTON_UNLOCK', ' Unlock ');

define('TABLE_HEADING_PAYMENT_BATCH', 'Transaction Batch');
define('TABLE_HEADING_PAYMENT_AMOUNT', 'Payment Amount');
define('TABLE_HEADING_PAYMENT_FEES', 'Payment Fees');
define('TABLE_HEADING_PAYMENT_PO_LIST', 'PO List');

define('SUCCESS_PO_PAYMENT_ADDED', "Success: Payment for CDKey supplier has been successfully added.");
define('ERROR_PO_PAYMENT_PO_SUPPLIER', "Error: Please select a CDKey Supplier.");
define('ERROR_PO_PAYMENT_PO_CURRENCY', "Error: Please select a Currency.");
define('ERROR_PO_PAYMENT_PO_DISBURSEMENT', "Error: Please select a disbursement method.");
define('ERROR_PO_PAYMENT_PO_LIST', "Error: Please select at least 1 purchase order to pay for.");
?>