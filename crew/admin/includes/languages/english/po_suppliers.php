<?
if ($_REQUEST['action']=='new_supplier') {
	define('HEADING_TITLE', 'Add CDK Supplier');
} else if ($_REQUEST['action']=='edit_payment') {
	define('HEADING_TITLE', 'Edit CDK Supplier Payment Info');
} else {
	define('HEADING_TITLE', 'CDK Suppliers List');
}

define('CATEGORY_PAYMENT_STATISTIC', 'Outstanding Stock &amp; Payment');

define('TABLE_HEADING_SUPPLIER_ID', 'CDK Supplier ID');
define('TABLE_HEADING_SUPPLIER_CODE', 'Supplier Alias');
define('TABLE_HEADING_SUPPLIER_REFERENCE_RUNNING_YEAR', 'PO Reference Year');
define('TABLE_HEADING_SUPPLIER_REFERENCE_RUNNING_COUNT', 'PO Reference Running No.');
define('TABLE_HEADING_SUPPLIER_GENDER', 'Gender');
define('TABLE_HEADING_SUPPLIER_LASTNAME', 'Last Name');
define('TABLE_HEADING_SUPPLIER_FIRSTNAME', 'First Name');
define('TABLE_HEADING_SUPPLIER_DOB', 'DOB');
define('TABLE_HEADING_SUPPLIER_ADDRESS', 'Address');
define('TABLE_HEADING_SUPPLIER_TELEPHONE', 'Contact Num.');
define('TABLE_HEADING_SUPPLIER_FAX', 'Fax Num.');
define('TABLE_HEADING_SUPPLIER_AGREEMENT', 'Agreement Info');
define('TABLE_HEADING_SUPPLIER_PAYMENT_TYPE', 'Payment Term');
define('TABLE_HEADING_SUPPLIER_PAYMENT_INFO', 'Payment Info');
define('TABLE_HEADING_DATE_CREATED', 'Account Created');
define('TABLE_HEADING_SUPPLIER_STATUS', 'Status');
define('TABLE_HEADING_ACTION', 'Actions');

define('ENTRY_SUPPLIER_ID', 'Supplier ID:');
define('ENTRY_SUPPLIER_CODE', 'Supplier Alias:');
define('ENTRY_SUPPLIER_STATUS', 'Supplier Status:');
define('ENTRY_SUPPLIER_REFERENCE_RUNNING_YEAR', 'PO Reference Year');
define('ENTRY_SUPPLIER_REFERENCE_RUNNING_COUNT', 'PO Reference Running No.');
define('ENTRY_SUPPLIER_REFERENCE_COUNTER_DESC', 'If this Supplier current manual PO running number is 10, enter 10. For new supplier with no previous PO, enter 0.');

define('ENTRY_SUPPLIER_STATISTIC_STOCK_RECEIVED_NO_PAYMENT', 'Stock Received, Payment Not Send');
define('ENTRY_SUPPLIER_STATISTIC_PAYMENT_SENT_NO_STOCK', 'Payment Sent, Stock Not Received');
define('ENTRY_SUPPLIER_STATISTIC_NET_OUTSTANDING_PAYMENT', 'Net Outstanding Payment');
define('ENTRY_SUPPLIER_STATISTIC_PO_PENDING_PAYMENT', 'Total PO Pending Payment');
define('ENTRY_SUPPLIER_STATISTIC_SUPPLIER_CREDIT', 'Supplier Credit');
define('ENTRY_SUPPLIER_STATISTIC_TEXT_INCLUDING_SUPPLIER_CREDIT', '&nbsp;&nbsp;&nbsp;<i>(Including Supplier Credit)</i>');
define('ENTRY_SUPPLIER_STATISTIC_TEXT_OUTSTANDING_STOCK', '&nbsp;&nbsp;&nbsp;<i>(-ve to be Outstanding Stock to be received)</i>');

define('ENTRY_SUPPLIER_REMARKS', 'Remark:');
define('ENTRY_SUPPLIER_PAYMENT_TERM', 'Payment Term:');
define('ENTRY_SUPPLIER_PAYMENT_TERM_ERROR', '&nbsp;<span class="errorText">required</span>');
define('ENTRY_SUPPLIER_AGREEMENT_START_DATE', 'Agreement Start Date:');
define('ENTRY_SUPPLIER_AGREEMENT_END_DATE', 'Agreement Expiry Date:');
define('ENTRY_SUPPLIER_AGREEMENT_DISCOUNT_TERMS', 'Agreement Discount Terms:');

define('TEXT_SUPPLIER_PRE_PAYMENT', 'Pre-Payment');
define('TEXT_SUPPLIER_DTU_PAYMENT', 'DTU Payment');
define('TEXT_SUPPLIER_API_PAYMENT', 'API Replenish Payment');
define('TEXT_SUPPLIER_CONSIGNMENT_PAYMENT', 'Consignment Payment');
define('TEXT_SUPPLIER_DAY_TERM', 'day-Term');
define('TEXT_SUPPLIER_CONSIGNMENT', 'Consignment');
define('TEXT_SUPPLIER_ACTUAL_WSC_CREDIT', 'Actual Days to credit WSC');

define('TABLE_HEADING_PAYMENT_ACCOUNT', 'Payment Account');
define('TABLE_HEADING_PAYMENT_INFO', 'Payment Account Details');
define('TABLE_HEADING_PAYMENT_ACTION', 'Actions');

define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', 'No payment disbursement account in record.');
define('TEXT_PM_REACHED_MAX', 'Maximum %d payment disbursement methods reached.');

define('SUCCESS_PO_SUPPLIER_ADDED', "Success: CDKey Supplier, %s, has been successfully added.");
define('SUCCESS_PO_SUPPLIER_ACCOUNT_UPDATED', "Success: CDKey Supplier's account successfully updated.");
define('SUCCESS_PO_SUPPLIER_PAYMENT_UPDATED', "Success: CDKey Supplier's payment info successfully updated.");
define('SUCCESS_PO_SUPPLIER_REMARK_UPDATED', "Success: CDKey Supplier's remark successfully added.");
define('SUCCESS_PO_SUPPLIER_COMPANY_UPDATED', "Success: CDKey Supplier's delivery address successfully updated.");
define('SUCCESS_PO_SUPPLIER_AGREEMENT_UPDATED', "Success: CDKey Supplier's discount agreement successfully updated.");
define('SUCCESS_PO_SUPPLIER_PAYMENT_ACCOUNT_DELETED', "Success: Payment account successfully deleted.");
define('ERROR_PO_SUPPLIER_PAYMENT_UPDATE_FAILED', "Error: CDKey Supplier's payment info update failed.");
define('ERROR_PO_SUPPLIER_INSERT_PERMISSION', "Error: You don't have permission to add new CDKey Supplier.");
define('ERROR_PO_SUPPLIER_INVALID_DATA_INSERT', "Error: Some CDKey Supplier's info is not complete or has issue.");
define('ERROR_PO_SUPPLIER_ACCOUNT_EDIT', "Error: You don't have the permission to change supplier account info.");
define('ERROR_PO_SUPPLIER_ACCOUNT_PERMISSION_UPDATED', "Error: You don't have permission to change CDKey Supplier's account.");
define('ERROR_PO_SUPPLIER_PAYMENT_PERMISSION_UPDATED', "Error: You don't have permission to change CDKey Supplier's payment info.");
define('ERROR_PO_SUPPLIER_REMARK_PERMISSION_UPDATED', "Error: You don't have permission to add remark for CDKey Supplier.");
define('ERROR_PO_SUPPLIER_COMPANY_PERMISSION_UPDATED', "Error: You don't have permission to change CDKey Supplier's delivery address.");
define('ERROR_PO_SUPPLIER_AGREEMENT_PERMISSION_UPDATED', "Error: You don't have permission to change CDKey Supplier's discount agreement.");
define('ERROR_PO_SUPPLIER_NOT_EXIST', "Error: CDKey Supplier does not exist.");
define('ERROR_PO_SUPPLIER_PAYMENT_ACCOUNT_DELETE_FAILED', "Error: Payment account delete failed.");
define('ERROR_PO_SUPPLIER_PAYMENT_ACCOUNT_IN_USE', "Error: Failed deleting payment account. Payment account currently in use in purchase order.");

define('SUCCESS_PO_PAYMENT_WITHDRAW_SUCCESS', 'CDKey Supplier\'s funds has been successfully withdraw.');
define('ERROR_PO_PAYMENT_INCOMPLETE_FIELD_INFO', "Error: CDKey Supplier is having incomplete payment disbursement fields info.");
define('ERROR_PO_PAYMENT_NO_AVAILABLE_FUND', "Error: CDKey Supplier have no available funds to withdraw or withdraw amount exceed the available funds.");
define('ERROR_PO_PAYMENT_NO_WITHDRAW_AMOUNT', "Error: Please select at least one Purchase Order to issue payment.");
define('ERROR_PO_PAYMENT_NO_PAYMENT_ACCOUNT_BOOK', "Error: CDKey Supplier does not have WSC account.");
define('ERROR_PO_PAYMENT_WITHDRAW_NOT_SUCCESS', 'Error: CDKey Supplier\'s funds withdraw is failed.');
define('WARNING_PO_PAYMENT_WITHDRAW_LESS_THAN_MININUM_ALLOWED', "Warning: Withdraw amount is less than minimum required amount %s");
define('WARNING_PO_PAYMENT_WITHDRAW_FEES_HIGHER_THAN_WITHDRAW_AMOUNT', 'Withdrawal fees are higher than the withdrawal amount.');

define('ENTRY_PO_PAYMENT_WITHDRAW_CURRENT_BALANCE', 'Current Balance:');
define('ENTRY_PO_PAYMENT_WITHDRAW_RESERVE_AMOUNT', 'Reserve:');
define('ENTRY_PO_PAYMENT_WITHDRAW_AVAILABLE_BALANCE', 'Available for Withdrawal:');
define('ENTRY_PO_PAYMENT_WITHDRAW_AMOUNT', 'Payment Amount:');
define('ENTRY_PO_PAYMENT_WITHDRAW_PAYMENT_ACCOUNT', 'Pay To:');
define('ENTRY_PO_PAYMENT_WITHDRAW_PAYMENT_FOR', 'Pay For:');
define('ENTRY_PO_PAYMENT_WITHDRAW_FINAL_AMOUNT', 'Payable Amount:');
define('ENTRY_PO_PAYMENT_WITHDRAW_PAYMENT_METHOD', 'Payment Method:');
define('ENTRY_PO_PAYMENT_WITHDRAW_MIN_AMT', '&nbsp;- Minimum Amount:');
define('ENTRY_PO_PAYMENT_WITHDRAW_MAX_AMT', '&nbsp;- Maximum Amount:');


define('ERROR_PO_PAYMENT_MISSING_STORE_PAYMENT_ID', "Error: Missing store payment ID to cancel Purchase Order's payment status.");
define('ENTRY_SUPPLIER_CODE_ERROR', '&nbsp;<span class="errorText">Empty Supplier Code!</span>');
define('ENTRY_SUPPLIER_CODE_EXIST_ERROR', '&nbsp;<span class="errorText">Duplicate Supplier Code!</span>');
define('ENTRY_SUPPLIER_REFERENCE_YEAR_ERROR', '&nbsp;<span class="errorText">Must be in 2 numerical value!</span>');
define('ENTRY_SUPPLIER_REFERENCE_COUNTER_ERROR', '&nbsp;<span class="errorText">Not more than 4 numerical value!</span>');

define('COMMENT_WITHDRAW_FUNDS_REQUESTED', 'Request for funds to be withdrawn to %s(%s)');
define('COMMENT_PO_PAYMENT_WITHDRAW_SUCCESS', 'Payment request generated: %s'."\n".'Pay amount: %s');
define('COMMENT_PO_PAYMENT_WITHDRAW_CANCEL', 'Payment request cancelled: %s'."\n".'Pay amount: %s');
define('TEXT_NO_WITHDRAW_LIMIT', 'Any');
?>