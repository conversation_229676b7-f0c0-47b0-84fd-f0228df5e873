<?
/*
    Developer: <PERSON><PERSON>ri
*/

if ($_REQUEST['action']=='cdk_list') {
    define('TABLE_HEADING_CDK_DETAILS', 'PO Ref ID : ');
} else {
    define('TABLE_HEADING_CDK_DETAILS', '');
}

define('MAX_DISPLAY_CDKEY_RESULTS', '200');

define('TABLE_HEADING_CDK_SELECT', 'Select');
define('TABLE_HEADING_CDK_DATE', 'Sales Date');
define('TABLE_HEADING_CDK_DATE_MODIFY', 'Modify Date');
define('TABLE_HEADING_CDK_NUMBER', 'CDKEY');
define('TABLE_HEADING_CDK_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_CDK_PRODUCT_NAME', 'Products Name');
define('TABLE_HEADING_CDK_ORDER_ID', 'Order #');
define('TABLE_HEADING_CDK_PO_ID', 'PO #<br>(Payment)');
define('TABLE_HEADING_CDK_SELLING_PRICE', 'Selling<br>Price %s');
define('TABLE_HEADING_CDK_SUBTOTAL_PRICE', 'Subtotal %s');
define('TABLE_HEADING_CDK_BILLING_STATUS', 'CDKEY<br>Payment Status');
define('TABLE_HEADING_CDK_AMOUNT', 'Selected CDKEY Subtotal %s');

define('TABLE_HEADING_BOTTOM_TOTAL_CDK_SELECT', 'Selected CDK Amount');

define('LINK_FILTER_BY_ACTUAL', 'Actual');
define('LINK_FILTER_BY_ON_HOLD', 'On Hold');
define('LINK_FILTER_BY_SOLD', 'Sold');
define('LINK_FILTER_BY_DISABLED', 'Disabled');
define('LINK_FILTER_BY_PAID', 'Paid');
define('LINK_FILTER_BY_CB', 'Charge Back');
define('LINK_FILTER_BY_DN', 'Debit Note');
define('LINK_FILTER_BY_PROCESSING', 'Processing');
define('LINK_FILTER_BY_ISSUE', 'CDK Issue');
define('LINK_FILTER_BY_QUEUE', 'Queue');

define('BUTTON_STATUS_CDK', 'Add to Queue');
define('BUTTON_STATUS_REMOVED_CDK', 'Removed from Queue');
define('BUTTON_ALT_STATUS_CDK', 'Add to Queue');
define('BUTTON_ALT_STATUS_REMOVED_CDK', 'Removed from Queue');

define('LABEL_BATCH_ACTION', 'With selected:');
define('LABEL_SET_SOLD', 'Set as sold');
define('LABEL_SET_PAID', 'Set as paid');
define('LABEL_SET_CB', 'Set as Charge Back');
define('LABEL_SET_DN', 'Set as Debit Note');
define('LABEL_SET_ISSUE', 'Set as CDK Issue');
define('LABEL_SET_REMOVE', 'Remove from List');

define('SUCCESS_CDK_FORM_ADDED', 'Succes: Added %s CDKEY to the Payment Request');
define('SUCCESS_CDK_FORM_REMOVED', 'Succes: Removed %s CDKEY to the Payment Request');

define('ERROR_CDK_FORM_CREATE_PERMISSION', 'Error: You are not allow to add CDKEY to the Payment Request.');
define('ERROR_CDK_FORM_CDKEY_MISSING', "Error: Please select CDKs.");
define('ERROR_CDK_FORM_CDKEY_STATUS_MISSING', "Error: Please select status CDKs.");
define('ERROR_FILE_NOT_FOUND', 'File not found');

?>