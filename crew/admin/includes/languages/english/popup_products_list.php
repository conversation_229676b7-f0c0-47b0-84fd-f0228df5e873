<?
/*
  	$Id: popup_products_list.php,v 1.1 2005/12/12 03:53:55 weichen Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

if ($_REQUEST['action']=='add_price_set') {
	define('HEADING_TITLE', 'Add Price Set');
} else if ($_REQUEST['action']=='edit_price_set') {
	define('HEADING_TITLE', 'Edit Price Set');
} else {
	define('HEADING_TITLE', '');
}
/*
define('TABLE_HEADING_PRICE_TAGS', 'Batch Update Tags');
define('TABLE_HEADING_PRICE_TAGS_FIELD', 'Matching Field');
define('TABLE_HEADING_PRICE_TAGS_NAME', 'Matching Value');
define('TABLE_HEADING_PRICE_TAGS_UPDATING_FIELD', 'Updating Field');
define('TABLE_HEADING_TAGS_UPDATING_VALUE', 'Updating Value');
define('TABLE_HEADING_TAGS_LANGUAGE', 'Language');
define('TABLE_HEADING_PRICE_TAGS_DESCRIPTION', 'Comments');
*/
define('TABLE_HEADING_PRICE_SET_ID', 'Unique ID');
define('TABLE_HEADING_PRICE_SET_NAME', 'Price Set Name');
define('TABLE_HEADING_PRICE_SET_ORDER', 'Sort Order');
define('TABLE_HEADING_ACTION', 'Action');

define('ENTRY_UNIQUE_PRICE_SET_ID', 'Unique ID');
define('ENTRY_PRICE_SET_NAME', 'Price Set Name');
define('ENTRY_PRICE_SET_ORDER', 'Price Set Sort Order');
define('ENTRY_PRICE_SET', 'Price Set');

define('ENTRY_TAGS_UPDATING_VALUE', 'Updating Value');
define('ENTRY_TAGS_LANGUAGE', 'For Language');
define('ENTRY_PRICE_TAGS_FIELD', 'Matching Field');
define('ENTRY_PRICE_TAGS_UPDATE_FIELD', 'Updating Field');
define('ENTRY_PRICE_TAGS_ORDER', 'Priority Ordering');

define('TEXT_NUMBER_ONLY', '(number only)');

?>