<?
/*
  	$Id: price_automation.php,v 1.5 2008/12/05 04:59:14 keepeng.foong Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/
define('HEADING_TITLE', 'Price Automation System');
define('TABLE_HEADING_MAIN_CATEGORY', 'Main Category');
define('TABLE_HEADING_PRODUCT', 'Product Name');
define('TABLE_HEADING_COMPETITOR_CODE', 'Competitor');

define('ENTRY_BUYBACK', 'Buyback');
define('ENTRY_SELLING', 'Selling');
define('ENTRY_SUGGESTED_PRICE', 'Suggested Price');
define('ENTRY_DAY', 'Day');
define('ENTRY_DAYS', 'Days');
define('ENTRY_WEEKS', 'Weeks');
define('ENTRY_MONTHS', 'Months');

define('ENTRY_PACKAGE_QUANTITY', 'Package Quantity');
define('ENTRY_SETTING', 'Setting');

define('TEXT_MULTI_QTY_ENTRIES', "(Use ';' as delimiter for multiple quantity)");
/***********************************************************************
	CSV Text
************************************************************************/
define('CSV_HEADING_BUYING_PRICE_IMPORT_CURRENCY', 'Currency (USD/CNY):');
define('CSV_HEADING_COMPETITOR_ID', 'COMPETITOR_ID');
define('CSV_HEADING_COMPETITOR_PRODUCT_ID', 'PRODUCT_ID');
define('CSV_HEADING_COMPETITOR_PRODUCT_PRICE', 'PRICE');
define('CSV_HEADING_COMPETITOR_PRODUCT_STATUS', 'STATUS');

define('CSV_HEADING_BUYBACK_PRODUCT_ID', 'PRODUCT_ID');
define('CSV_HEADING_BUYBACK_PRODUCT_NAME', 'PRODUCT NAME');
define('CSV_HEADING_BUYBACK_SUGGESTED_PRICE', 'SUGGESTED PRICE');
define('CSV_HEADING_BUYBACK_OVERWRITE_PRICE', 'OVERWRITE PRICE');

define('CSV_HEADING_MARKUP_PERCENTAGE', 'Markup Percentage');
define('CSV_HEADING_INCREMENT_VALUE', 'Increment Value');
define('CSV_HEADING_SUGGESTED_PRICE', 'Suggested Price');
define('CSV_HEADING_CURRENT_PRICE', 'Current Price');
define('CSV_HEADING_OVERWRITE_PRICE', 'Overwrite Price');
define('CSV_HEADING_SELLING_RANK', 'Rank');

define('CSV_HEADING_SERVER_NAME', 'Product Name');
define('CSV_HEADING_BUYBACK_COMPETITOR', "BuyBack's Competitor");
define('CSV_HEADING_BUYBACK_SETTING', 'BuyBack Setting');
define('CSV_HEADING_AGING_BO_AND_COMPLETED', 'Aging ( BO / Completed )');
define('CSV_HEADING_SELLING_SETTING', 'Selling Setting');
define('CSV_HEADING_SELLING_COMPETITOR', "Selling's Competitor");
define('CSV_HEADING_UPDATE', 'Update');

define('HEADING_ID', 'ID');
define('HEADING_NAME', 'NAME');
define('HEADING_PRICE', 'PRICE');
define('HEADING_STATUS', 'STATUS');

define('ERROR_IMPORT_FAILED', 'Error: Import aborted. Please check your csv file for errors.');
define('ERROR_NO_COMPETITOR_IN_THIS_CATEGORY', 'Error: No competitor on this category. <a href="%s">Add competitors</a> ?');
define('SUCCESS_AVG_BUYBACK_PRICE_IMPORT_FILE', 'Success: Buyback Imported file is valid.');
define('SUCCESS_AVG_SELLING_PRICE_IMPORT_FILE', 'Success: Selling Imported file is valid.');
define('MESSAGE_MISMATCHED_COLUMNS', 'Mismatched Columns detected. Competitor count does not match price columns.');
define('ERROR_AVG_PRICE_IMPORTED_PRODUCT_NOT_MATCH', "Error: Product #%d does not match any of this game's products!");
define('SUCCESS_INSERT_N_UPDATE_PRICE_AUTOMATION_SETTING', 'Success: Alert Price Setting had been successfully insert/update to database.');
define('ERROR_AVG_PRICE_INVALID_GAME', 'Error: Invalid game specified.');
define('WARNING_PRODUCT_DOES_NOT_EXISTS', 'Warning: The product id(%s) for %s is invalid.');

define('JS_MSG_PLS_SELECT_THE_CATEGORY', 'Please select the Category!');
define('JS_MSG_PLS_SELECT_THE_HISTORY_DATE', 'Please select the history date.');
define('JS_MSG_PLS_ENTER_THE_PCKG_QTY', '* Please enter the package quantity or select all packages.');
define('JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK', 'Inputbox cannot left blank.');
define('JS_MSG_PLS_SELECT_THE_BUYBACK_COMPETITORS', 'Please select the buyback competitors!');
define('JS_MSG_BUYBACK_PREFERRED_RANK_CANNOT_BE_LEFT_BLANK', 'Buyback preferred rank cannot be blank');
define('JS_MSG_PLS_SELECT_THE_CHECKBOX', 'Please select the checkbox.');
define('JS_MSG_OVERWRITE_FIELD_CANNOT_BE_BLANK', 'Overwrite field cannot be blank');
define('JS_MSG_INCREMENT_FIELD_CANNOT_BE_BLANK', 'Increment field cannot be blank');

define('TAB_HEADING_ALERT_SETTING', 'Alert Setting');
define('TAB_HEADING_BUYBACK_COMPETITOR', "BuyBack's Competitor");
define('TAB_HEADING_BUYBACK', 'BuyBack');
define('TAB_HEADING_SELLING_COMPETITOR', "Selling's Competitor");
define('TAB_HEADING_HIDE_SERVER_SETTING', 'Hide Server Setting');
define('TAB_HEADING_BATCH_UPDATE_SETTING', 'Batch Update Setting');
define('TAB_HEADING_BATCH_HISTORY', 'History');

define('TAB_PRODUCT_SETTING', 'Product Setting');
define('TAB_BUYBACK_COMPETITOR_PRICE_SETTING', "Competitor's BuyBack Price Setting");
define('TAB_BUYBACK_PRICE_SETTING', "BuyBack Price Setting");
define('TAB_BACKORDER_SETTING', 'Back-Order Setting');
define('TAB_SELLING_PRICE_SETTING', "Selling Price Setting");
define('TAB_SELLING_COMPETITOR_PRICE_SETTING', "Competitor's Selling Price Setting");
define('TAB_AGING_BO_N_COMPLETED', 'Aging ( BO / Completed )');
define('TAB_SUBTXT_DAY', 'Day');
define('TAB_SUBTXT_DAYS', 'Days');
define('TAB_SUBTXT_WEEKS', 'Weeks');
define('TAB_SUBTXT_MONTHS', 'Months');

define('DELETE', 'Delete');
define('BTN_UPDATE', 'Update');

define('TAB_SUB_HEADING_BO_DAYS', 'BO Days');
define('TAB_SUB_HEADING_BO_AMOUNT', 'BO Amount');
define('TAB_SUB_HEADING_BO_MARGIN', 'Margin');
define('TAB_SUB_HEADING_BO_SELLING_RANK', 'Selling Rank');
define('TAB_SUB_HEADING_BO_SUGGESTED_PRICE', 'Suggested Price');
define('TAB_SUB_HEADING_BO_CURRENT_PRICE', 'Current Price');
define('TAB_SUB_HEADING_BO_OVERWRITE_PRICE', 'Overwrite Price');
define('TAB_SUB_HEADING_SELLING_MARKUP_PERCENTAGE', 'Markup Percentage');
define('TAB_SUB_HEADING_SELLING_INCREMENT_VALUE', 'Increment Value');
define('TAB_SUB_HEADING_SELLING_SUGGESTED_PRICE', 'Suggested Price');
define('TAB_SUB_HEADING_SELLING_CURRENT_PRICE', 'Current Price');
define('TAB_SUB_HEADING_SELLING_RANK', 'Rank');

define('TEXT_NO_RECORD', 'No Record');

define('BO_HIDE_IF', 'Hide If');
define('BO_HIDE_NOW', 'Hide Now');

define('HISTORY_SHOW', 'Show');

define('BTN_EXPORT_CSV_TEMPLATE', 'Export CSV Template');
define('BTN_IMPORT', 'Import');
define('BTN_SERVER_NAME', 'Server Name');
define('BTN_COMPETITOR_NAME', 'Competitor Name');
define('BTN_CALCULATE', 'Calculate');
define('BTN_CLEAR', 'Clear');
define('BTN_UPDATE', 'Update');
define('BTN_IMPORT', 'Import');
define('BTN_EXPORT', 'Export');
define('BTN_UPDATE_BUYBACK_PRICE', 'Update Buyback Price');
define('BTN_UPDATE_SELLING_PRICE', 'Update Selling Price');
define('BTN_BATCH_FILL', 'Batch Fill');

define('TOTAL_BUYBACK_COMPETITORS', 'Total Buyback Competitors');
define('VIEW_DETAILS', 'View Details');
?>