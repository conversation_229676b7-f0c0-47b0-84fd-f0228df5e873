<?
/*
  	$Id: price_tags.php,v 1.13 2007/10/11 04:13:23 wailai Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

if ($_REQUEST['action']=='new_price_groups') {
	define('HEADING_TITLE', 'Add New Batch Update Group');
} else {
	define('HEADING_TITLE', 'Batch Update');
}

define('TABLE_HEADING_PRICE_GROUPS_NAME', 'Batch Update Groups Name');
define('TABLE_HEADING_PRICE_GROUPS_DESCRIPTION', 'Description');
define('TABLE_HEADING_PRICE_TAGS', 'Batch Update Tags');
define('TABLE_HEADING_PRICE_TAGS_FIELD', 'Matching Field');
define('TABLE_HEADING_PRICE_TAGS_NAME', 'Matching Value');
define('TABLE_HEADING_PRICE_TAGS_UPDATING_FIELD', 'Updating Field');
define('TABLE_HEADING_TAGS_UPDATING_VALUE', 'Updating Value');
define('TABLE_HEADING_TAGS_LANGUAGE', 'Language');
define('TABLE_HEADING_PRICE_TAGS_DESCRIPTION', 'Comments');

// Purchase Mode
define('TABLE_HEADING_AUTO_MODE', 'Auto<br><small>(Based on Available Qty)</small>');
define('TABLE_HEADING_MANUAL_MODE', 'Manual');

define('TEXT_PRUCHASE_MODE_ADD_TO_CART', 'Add to Cart:');
define('TEXT_PRUCHASE_MODE_PRE_ORDER', 'Pre-Order:');
define('TEXT_PRUCHASE_MODE_OUT_OF_STOCK', 'Out of Stock:');
define('TEXT_PRODUCTS_DEFAULT_PRE_ORDER_LEVEL', '(Default: %d)');
define('TEXT_PRODUCTS_DEFAULT_ETA', '(Default: Follow category configuration)');
define('TEXT_PRODUCTS_ETA', 'ETA (hours):');
define('TEXT_PRODUCTS_PRE_ORDER_LEVEL', 'Pre-Order Level:');
define('TEXT_PRODUCTS_OUT_OF_STOCK_LEVEL', 'Out of Stock Level:');
define('TEXT_FOLLOW_SYSTEM', 'DEFAULT');
define('TEXT_INFO_CHANGED_HISTORY_PRODUCTS', 'products');
define('TEXT_EDIT_CATEGORIES_AUTO_URL_ALIAS', 'Auto Generate SEO');

define('ENTRY_PRICE_GROUPS_NAME', 'Batch Update Group Name');
define('ENTRY_PRICE_GROUPS_DESCRIPTION', 'Batch Update Group Description');
define('ENTRY_PRICE_TAGS_NAME', 'Matching Value');
define('ENTRY_PRICE_TAGS_DESCRIPTION', 'Batch Update Tag Description');
define('ENTRY_TAGS_UPDATING_VALUE', 'Updating Value');
define('ENTRY_TAGS_LANGUAGE', 'For Language');
define('ENTRY_PRICE_TAGS_FIELD', 'Matching Field');
define('ENTRY_PRICE_TAGS_UPDATE_FIELD', 'Updating Field');
define('ENTRY_PRICE_TAGS_ORDER', 'Priority Ordering');

define('TEXT_TOTAL_LINKED_CATEGORIES', '(Total categories: %d)');
define('TEXT_OPTION_NOT_APPLICABLE', 'n/a');

define('LINK_ADD_PRICE_CLASS', 'Add Batch Update Groups');

define('SUCCESS_BATCH_UPDATE', 'Success: Batch update has been successfully performed.');
define('WARNING_UPDATE_IMAGE', 'Warning: %s for %s is not updated.');
define('WARNING_REFERENCE_IMAGE_NOT_EXISTS', 'Warning: Reference %s(%s with id #%s) does not exists.');
define('WARNING_REFERENCE_PRODUCT_NOT_EXISTS', 'Warning: Reference %s\'s product with id #%s does not exists.');
define('WARNING_BATCH_UPDATE_URL_ALIAS_CHANGED', 'Warning: The SEO URL Alias changed FROM %s TO %s!');
define('ERROR_SEO_URL_ACCESS_DENIED', 'Error: You do not have permission for the seo url alias update.');
define('ERROR_UPDATE_PRICE_ACCESS_DENIED', 'Error: You do not have permission for the price update.');
define('ERROR_UPDATE_PURCHASE_MODE_ACCESS_DENIED', 'Error: You do not have permission for the purchase mode.');

define('JS_TEXT_SEO_SPECIAL_CHARACTER', 'The SEO Alias has special characters or space (accept minus "-"). These are not allowed. \nPlease remove them and try again.');

?>