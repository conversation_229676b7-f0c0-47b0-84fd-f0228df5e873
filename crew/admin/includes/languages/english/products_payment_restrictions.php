<?php
/*
  	<PERSON><PERSON><PERSON> 15 Sept 2017
*/

// Header
define('HEADER_FORM_PRODUCTS_PAYMENT_RESTRICTIONS_TITLE', 'Products Payment Methods Restrictions List');

// Table list display setting
define('MAX_DISPLAY_SEARCH_RESULTS', '50');

// Product List
define('TABLE_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_CATEGORY_PATH', 'Category Path');
define('TABLE_HEADING_RESTRICTION_MODE', 'Restriction Mode');
define('TABLE_HEADING_RESTRICTION_INFO', 'Restriction Info');
define('TABLE_HEADING_UPDATE_DATE', 'Date');
define('TABLE_HEADING_CHANGED_BY', 'Changed By');
define('TABLE_HEADING_RESTRICTION_ACTION', 'Action');
?>