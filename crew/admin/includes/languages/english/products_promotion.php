<?php
/*
  $Id: products_promotion.php,v 1.3 2011/06/10 09:26:11 dennis.chang Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

define('HEADING_PRODUCT_PROMOTION_TITLE', 'Product Promotions');
define('HEADING_PRODUCT_PROMOTION_NEW_PROMOTION', 'New Product Promotions');
define('HEADING_PRODUCT_PROMOTION_EDIT_PROMOTION', 'Edit Product Promotion');

define('TABLE_HEADING_PRODUCT_PROMOTION_CATEGORY', 'Catagory Path');
define('TABLE_HEADING_PRODUCT_PROMOTION_PRODUCT', 'Product');
define('TABLE_HEADING_PRODUCT_PROMOTION_ACTION', 'Action');
define('TABLE_HEADING_PRODUCT_PROMOTION_START_DATE', 'Start Date');
define('TABLE_HEADING_PRODUCT_PROMOTION_END_DATE', 'End Date');

define('TABLE_PRODUCTS_PROMOTION', 'products_promotion');
define('TABLE_PRODUCTS_PROMOTION_DESCRIPTION', 'products_promotion_description');
define('TABLE_PRODUCTS_BUNDLES', 'products_bundles');

define('LABEL_PRODUCT_PROMOTION_PRODUCT_ID', 'Product ID: ');
define('LABEL_PRODUCT_PROMOTION_START_DATE', 'Start Date: ');
define('LABEL_PRODUCT_PROMOTION_END_DATE', 'End Date: ');
define('LABEL_PRODUCT_PROMOTION_IMAGE', 'Promotion Image: ');
define('LABEL_PRODUCT_PROMOTION_IMAGE_TITLE', 'Promotion Image Title: ');
define('LABEL_PRODUCT_PROMOTION_ONLY_IN_PROMO_BOX', 'Only in promotion box');
define('LABEL_PRODUCT_PROMOTION_LIMITED_STOCK', 'Limited Stock');
define('LABEL_PRODUCT_PROMOTION_NO_STATUS', 'No Status');
define('LABEL_PRODUCT_PROMOTION_FAST_SELLING', 'Fast Selling');
define('LABEL_PRODUCT_PROMOTION_PRICE_SLASH', 'Price Slash');

define('TEXT_PRODUCT_PROMOTION_BOX', 'Promotion Box: ');
define('TEXT_PRODUCT_PROMOTION_STATUS', 'Promotion Status: ');
define('TEXT_PRODUCT_PROMOTION_LIST', '(Product List)');
define('TEXT_PRODUCT_PROMOTION_REQUIRED', 'Required');
define('TEXT_PRODUCT_PROMOTION_OPTIONAL', 'Optional');
define('TEXT_PRODUCT_PROMOTION_EMPTY', 'Empty');
define('TEXT_PRODUCT_PROMOTION_ICON_SUCCESS', 'Delete Successful');

define('TEXT_PRODUCT_PROMOTION_LIMITED_STOCK_QUANTITY', 'Limited Stock Quantity < ');
define('TEXT_PRODUCT_PROMOTION_LIMITED_STOCK_COMMENT', '(Apply if product do not skip inventory)');
define('TEXT_PRODUCT_PROMOTION_LOW_PRIORITY_THAN_LIMITED_STOCK', 'Low priority than "Limited Stock"');

define('TEXT_PRODUCT_PROMOTION_JS_DELETE_CONFIRMATION', 'Are you sure you want to delete ');

define('TEXT_PRODUCT_PROMOTION_ERROR_PRODUCT_ID_EXIST', 'Selected product id already in promotion.');
define('TEXT_PRODUCT_PROMOTION_ERROR_PRODUCT_ID_MISSING', 'Product ID not found.');
define('TEXT_PRODUCT_PROMOTION_ERROR_PRODUCT_ID_EMPTY', 'Product ID cannot be empty.');
define('TEXT_PRODUCT_PROMOTION_ERROR_INVALID_DATE_RANGE', 'Invalid promotion date range.');
define('TEXT_PRODUCT_PROMOTION_ERROR_INVALID_PRODUCT_ID', 'Invalid product id.');
define('TEXT_PRODUCT_PROMOTION_ERROR_START_DATE_EMPTY', 'Promotion start date cannot be empty.');
define('TEXT_PRODUCT_PROMOTION_ERROR_LIMITED_QUANTITY_OUT_OF_RANGE', 'Limited stock quantity cannot more than 5 digits.');

define('TEXT_PRODUCT_PROMOTION_ERROR_JS_PRODUCT_ID_MISSING', 'Please Enter Product ID.');
define('TEXT_PRODUCT_PROMOTION_ERROR_JS_START_DATE_MISSING', 'Please Select Promotion Start Date.');
define('TEXT_PRODUCT_PROMOTION_ERROR_JS_INVALID_DATE_RANGE', 'Incorrect Promotion Date Range.');
define('TEXT_PRODUCT_PROMOTION_ERROR_JS_LIMITED_QUANTITY_OUT_OF_RANGE', 'Limited stock quantity cannot more than 5 digits.');
define('TEXT_PRODUCT_PROMOTION_ERROR_JS_LIMITED_QUANTITY_MISSING', 'Please fill in limited stock quantity.');
define('TEXT_PRODUCT_PROMOTION_STATIC_PRODUCT_NOT_FOUND', 'Static Product not found for selected product id.');
define('TEXT_PRODUCT_PROMOTION_TITLE', 'Static Product List');

define('MAX_DISPLAY_PRODUCT_PROMOTION_SEARCH_RESULTS_PROMO_PRODUCT', '20');

define('BUTTON_PRODUCT_PROMOTION_CLEANUP', 'Cleanup Expired Promotion');
define('BUTTON_PRODUCT_PROMOTION_NEW_PROMO', 'New Product Promotions');
define('BUTTON_PRODUCT_PROMOTION_SHOW_STATIC_PRODUCT', 'Show Static Product');
?>