<?
if ($action == "edit_acc_set" || $action == "manage_rstk" || $action == "add_rstk") {
	define('HEADING_TITLE', 'Restock Characters Sets');
} else {
	define('HEADING_TITLE', 'Suppliers Purchase Lists');
}

define('TABLE_HEADING_LIST_NAME', 'Purchase Lists Name');
define('TABLE_HEADING_MAIN_CAT', 'Main Category');
define('TABLE_HEADING_QTY_ROUND_UP', 'Quantity Round Up');
define('TABLE_HEADING_SALES_START_DATE', 'Sales Start Date');
define('TABLE_HEADING_SALES_END_DATE', 'Sales End Date');
define('TABLE_HEADING_DAYS_INVENTORY', 'No. Days Inventory');
define('TABLE_HEADING_SORT_ORDER', 'Sort Order');
define('TABLE_HEADING_NEW_LIST_DATE', 'New List Date');
define('TABLE_HEADING_LAST_MODIFY_DATE', 'Last Modify Date');
define('TABLE_HEADING_RESTOCK_CHARACTERS', 'Restock Characters Set');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_SALES_RETRIEVAL_METHOD', 'Get Average Sales');
define('TABLE_HEADING_SALES_RETRIEVE_BY_DATE_RANGE', 'By Date Range');
define('TABLE_HEADING_SALES_RETRIEVE_BY_LAST', 'By Last');

define('TABLE_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_PRODUCT', 'Product Name');
define('TABLE_HEADING_MAX_QUANTITY', 'Maximum Qty');
define('TABLE_HEADING_OVERWRITE_MAX_QTY', 'Overwrite Maximum Qty');
define('TABLE_HEADING_AVAILABLE_QUANTITY', 'Forecast Available Qty');
define('TABLE_HEADING_ACTUAL_QUANTITY', 'Forecast Actual Qty');
define('TABLE_HEADING_SUGGEST_QUANTITY', 'Suggest Qty');
define('TABLE_HEADING_PURCHASE_QUANTITY', 'Purchase Qty');
define('TABLE_HEADING_DISABLED', 'Disabled');

define('TABLE_CSV_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_CSV_HEADING_PRODUCT', 'Product Name');
define('TABLE_CSV_HEADING_NEW_RSTK_CHAR_SET', 'NEW SET');
define('TABLE_CSV_HEADING_RSTK_CHAR', 'Restock Character');

define('ENTRY_PURCHASE_LIST_NAME', 'Purchase List Name:');
define('ENTRY_MAIN_CAT', 'Main Category:');
define('ENTRY_QTY_ROUND_UP', 'Quantity Round Up:');
define('ENTRY_SORT_ORDER', 'Sort Order:');
define('ENTRY_RESTOCK_CHARACTERS', 'Restock Characters Set:');
define('ENTRY_RESTOCK_CHARACTER_SETS_NAME', 'Restock Characters Set Name:');

define('TEXT_CLEAR', 'Clear');
define('TEXT_APPLY_TO_SELECTED', 'Apply to Selected');
define('TEXT_SHOW_BATCH_FILL', 'Show Batch Fill');
define('TEXT_HIDE_BATCH_FILL', 'Hide Batch Fill');
define('TEXT_SHOW_DISABLED', 'Show Disabled');
define('TEXT_HIDE_DISABLED', 'Hide Disabled');
define('TEXT_SHOW_PRODUCT_NAME', 'Show Product Name');
define('TEXT_HIDE_PRODUCT_NAME', 'Hide Product Name');
define('TEXT_USER_NOTES', '<div style="float: left;"><span class="redIndicator">Note: </span></div><div style="float: left;">(1) Leave the "'.TABLE_HEADING_PURCHASE_QUANTITY.'" box blank if using the "'.TABLE_HEADING_SUGGEST_QUANTITY.'".<br>(2) Click on "Latest Suggest Quantity" will update your "'.TABLE_HEADING_MAX_QUANTITY.'" and "'.TABLE_HEADING_DISABLED.'" settings as well.<br>(3) Remember to click "Update" if you want to use the new "'.TABLE_HEADING_SUGGEST_QUANTITY.'" and "'.TABLE_HEADING_PURCHASE_QUANTITY.'" settings.</div>');
define('TEXT_DAYS', 'days');

define('LINK_ADD_PURCHASE_LIST', 'Add Purchase List');
define('LINK_ADD_RESTOCK_CHARACTER_SET', 'Add Restock Charaters Set');
define('LINK_UNASSIGNED_RESTOCK_CHARACTERS', 'Restock Characters Set from this Purchase Lists');
define('LINK_MANAGE', 'Manage');

define('ERROR_IMPORTED_PRODUCT_NOT_MATCH', 'Error: Product #%d does not match any of this Supplier Purchase List\'s products!');
define('ERROR_IMPORTED_PRODUCT_NOT_MATCH_RSTK', 'Error: Product #%d does not match any of this Restock Characters Set\'s products!');
define('ERROR_IMPORTED_PRODUCT_IS_DISABLED', 'Error: Product #%d is currently disabled in this Supplier Purchase List!');
define('ERROR_INVALID_MAXIMUM_QUANTITY', 'Error: Maximum Quantity for %s must be an integer value!');
define('ERROR_INVALID_PURCHASE_QUANTITY', 'Error: Purchase Quantity for %s must be an integer value!');
define('ERROR_EMPTY_LIST_NAME', 'Error: The purchase list name is empty.');
define('ERROR_LIST_NOT_EXISTS', 'Error: Purchase list does not exists!');
define('ERROR_LIST_NAME_USED', 'Error: Purchase list name has already been used!');
define('ERROR_LIST_CAT_USED', 'Error: Main category has already exists in another purchase list or one of its sub-categories has been used as Main Category in another purchase list!');
define('ERROR_CHARACTER_SET_NAME_USED', 'Error: Characters set name (%s) has already been used!');
define('ERROR_LIST_MAIN_CAT_ACCESS_DENIED', 'Error: You do not have permission for the purchase list\'s main category.');
define('WARNING_NO_PRODUCTS_TO_UPDATE', 'Warning: There is no any products to be updated.');
define('WARNING_CHARACTER_SET_PURCHASE_LIST_DELETED', 'Warning: The restock characters set (%s) has been successfully removed from purchases list (%s).');
define('SUCCESS_LIST_UPDATE', 'Success: The products purchase quantity list has been successfully updated.');
define('SUCCESS_GET_FRESH_LIST', 'Success: Fresh list is now available to suppliers.');
define('SUCCESS_LIST_ADDED', 'Success: The purchase list info has been successfully created.');
define('SUCCESS_LIST_UPDATED', 'Success: The purchase list info has been successfully updated.');
define('SUCCESS_LIST_DELETED', 'Success: The purchase list has been successfully deleted.');
define('SUCCESS_CHARACTER_SET_ADDED', 'Success: The new characters set (%s) has been successfully created.');
define('SUCCESS_CHARACTER_SET_UPDATED', 'Success: The characters set (%s) has been successfully updated.');
define('SUCCESS_CHARACTER_SET_NO_STRING_UPDATED', 'Success: The characters set has been successfully updated.');
define('SUCCESS_CHARACTER_SET_DELETED', 'Success: The characters set has been successfully deleted.');
define('SUCCESS_CHARACTER_SET_REMOVED', 'Success: The characters set (%s) has been successfully remove from purchase lists (%s).');

define('JS_CONFIRM_FRESH_LIST', 'Are you sure to start using a fresh list?');

define('LINK_VIEW_LOGS', 'View Logs');
?>