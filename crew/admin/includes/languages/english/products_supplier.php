<?
if ($_REQUEST['action']=='edit_products_supplier') {
	define('HEADING_TITLE', 'Edit Products Supplier');
} else if ($_REQUEST['action']=='new_products_supplier') {
	define('HEADING_TITLE', 'New Products Supplier');
} else {
	define('HEADING_TITLE', 'Products Supplier List');
}

// Products Supplier list
define('TABLE_HEADING_PRODUCTS_SUPPLIER_ID', 'Supplier ID');
define('TABLE_HEADING_PRODUCTS_SUPPLIER_CODE', 'Supplier Code');
define('TABLE_HEADING_PAYOUT_GRACE_PERIOD_OFFSET', 'Payout Grace Period Offset (min)');
define('TABLE_HEADING_RESERVE_ACCOUNT_API', 'Reserve Account API');
define('TABLE_HEADING_RETRIEVE_ACCOUNT_INFO_API', 'Retrieve Account Info API');
define('TABLE_HEADING_PRODUCTS_SUPPLIER_STATUS', 'Status');
define('TABLE_HEADING_ACTION', 'Action');

// Products Supplier detail
define('TEXT_HEADING_SUPPLIER', 'Supplier');
define('ENTRY_PRODUCTS_SUPPLIER_STATUS', 'Status:');
define('ENTRY_PRODUCTS_SUPPLIER_CODE', 'Supplier Code:');
define('ENTRY_PRODUCTS_SUPPLIER_ID', 'Supplier ID:');
define('ENTRY_PAYOUT_PERCENTAGE', 'Payout Percentage:');
define('ENTRY_PAYOUT_GRACE_PERIOD_OFFSET', 'Payout Grace Period Offset:');

define('TEXT_HEADING_PRODUCT', 'Product');
define('TABLE_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_FOLDER_NAME', 'Folder Name');
define('TABLE_HEADING_RSS_LINK', 'RSS Link');

define('TEXT_HEADING_API', 'API');
define('ENTRY_RESERVE_ACCOUNT_API', 'Reserve Account API:');
define('ENTRY_RETRIEVE_ACCOUNT_INFO_API', 'Retrieve Account Info API:');
define('ENTRY_API_KEY', 'API Key:');

define('TEXT_ACTIVE', 'Active');
define('TEXT_INACTIVE', 'Inactive');
define('TEXT_MINUTE', 'minute');

define('ERROR_CUSTOMER_ID_NOT_EXIST', 'Error: Products Supplier ID (Customer ID) %s does not exist.');
define('ERROR_DUPLICATE_SUPPLIER_ID', 'Error: Fail to update record, dupplicate Products Supplier ID (Customer ID) %s.');
?>