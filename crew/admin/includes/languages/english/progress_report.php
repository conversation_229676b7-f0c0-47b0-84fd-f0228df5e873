<?
define('LINK_ADVANCED_SEARCH', 'Advanced Search');

define('HEADING_INPUT_TITLE', 'Progress Report %s');
define('HEADING_LIST_CRITERIA', 'List Criteria');
define('HEADING_PROGRESS_REPORT', '');
define('HEADING_ADVANCED_SEARCH', 'Advanced Search');

define('ENTRY_HEADING_ORDER_PRODUCT_SUBMIT_START_DATE', 'Start Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small>');
define('ENTRY_HEADING_ORDER_PRODUCT_SUBMIT_END_DATE', 'End Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small>');
define('ENTRY_HEADING_CP_FOLLOW_UP_DATE', 'Follow-up Date<br><small>(YYYY-MM-DD<br>/YYYY-MM-DD HH:MM)</small>');
define('ENTRY_HEADING_ORDER_ID', 'Order ID');
define('ENTRY_HEADING_PRODUCT_NAME', 'Product Name');
define('ENTRY_HEADING_PROGRESS_STATUS', 'Progress Status');
define('ENTRY_HEADING_SUPPLIER', 'Supplier');
define('ENTRY_HEADING_RATIO', 'Ratio');
define('ENTRY_PWL_FOLLOW_UP_DATETIME', 'Follow-up:');
define('ENTRY_TOTAL', 'Total:');
define('ENTRY_SHOW_SUPPLIER', 'Show Supplier');
define('ENTRY_AMOUNT_ADJUST', 'Amount Adjust:');
define('ENTRY_RATIO_ADJUST', 'Ratio Overwrite:');
define('ENTRY_MEMBER_GROUP', 'Member Group');
define('ENTRY_CATEGORY', 'Category');

define('ENTRY_ACCOUNT_USERNAME', 'Account Username');
define('ENTRY_ACCOUNT_PASSWORD', 'Account Password');
define('ENTRY_NEW_ACCOUNT_PASSWORD', 'New Account Password');
define('ENTRY_CHAR_NAME', 'Character Name');
define('ENTRY_REALM', 'Realm');
define('ENTRY_FACTION', 'Faction');
define('ENTRY_GAME', 'Game');
define('ENTRY_CHARACTER', 'Character:');
define('ENTRY_DATE', 'Date:');

define('TEXT_HOURS_PASS', 'Hours passed: ');
define('TEXT_CURRENT_LEVEL', 'Current level: ');
define('TEXT_HOURS', 'hours');
define('TEXT_DISPLAY_TO_SUPPLIER_SIGN', '<span class="redIndicator">*</span>');
define('TEXT_PROGRESS_BAR_NOT_AVAILABLE', 'progress bar n/a');
define('TEXT_CHANGE_STATUS', 'Change Status');
define('TEXT_SELECTED_ORDERS_AMOUNT', '');
define('TEXT_ANY', 'Any');
define('TEXT_LAST_UPDATE', 'Last Update: ');
define('TEXT_INCLUDE_SUBCATEGORY', 'Include Sub Categories');
define('TEXT_TAG', '<b>Tag: </b>');
define('TEXT_CHARACTER_INFO', "<b>Character <br> Information: </b>");
define('TEXT_DURATION', 'Duration: ');
define('TEXT_PWL_ORDER_ID', 'PWL Order ID: ');
define('TEXT_LEVELER', 'Leveler: ');
define('TEXT_ADJUSTMENT_DETAIL', 'Adjustment Detail');
define('TEXT_NOT_ASSIGN_TO_SUPPLIER', '<span class="redIndicator">Not assigned</span>');
define('TEXT_UI_HISTORY', 'History');;
define('TEXT_ORDER_ID', 'Order ID: ');
define('TEXT_ADJUSTED_BY', 'Adjusted by: ');

define('TEXT_ORDER_NOT_FOUND', 'Order cannot be found!');
define('TEXT_GOLD_IN', 'Gold-In'); 
define('TEXT_GOLD_OUT', 'Gold-Out'); 
define('TEXT_ITEM_IN', 'Item-In'); 
define('TEXT_ITEM_OUT', 'Item-Out');

define('TEXT_GEAR', 'Gear');
define('TEXT_BAGS_AND_BANK', 'Bags and Bank'); 
define('TEXT_LEVEL', 'Level');
define('TEXT_STRENGTH', 'Strength: ');
define('TEXT_AGILITY', 'Agility: '); 
define('TEXT_STAMINA', 'Stamima: ');
define('TEXT_INTELLECT', 'Intellect: ');
define('TEXT_SPIRIT', 'Spirit: ');
define('TEXT_ATTACK', 'Attack: ');
define('TEXT_POWER', 'Power: ');
define('TEXT_DAMAGE', 'Damage: ');
define('TEXT_DEFENSE', 'Defense: ');
define('TEXT_ARMOR', 'Armor: ');

define('TABLE_HEADING_CUSTOMERS_EMAIL', 'Customer E-Mail');
define('TABLE_HEADING_CUSTOMER_GROUP', 'Member Group');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name'); 
define('TABLE_HEADING_TAG', 'Tag');
define('TABLE_HEADING_PROGRESS_STATUS', 'Hours Passed/Progress'); 
define('TABLE_HEADING_GAME', 'Game');
define('TABLE_HEADING_TOTAL', 'Total');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_NUMBER', 'No.'); 
define('TABLE_HEADING_SUBMITTED_DATE', 'Submitted Date');
define('TABLE_HEADING_CP_BILLING_STATUS', 'Billing Status');
define('TABLE_HEADING_CP_VERIFY_STATUS', 'Verified?');
define('TABLE_HEADING_TOTAL_PAYABLE', 'Total Payable');
define('TABLE_HEADING_RATIO', 'Ratio');
define('TABLE_HEADING_AMOUNT_ADJUST', 'Amount Adjust'); 
define('TABLE_HEADING_RATIO_ADJUST', 'Ratio Overwrite');
define('TABLE_HEADING_AVAILABLE_SLOT', 'Available Slot');
define('TABLE_HEADING_PROGRESS_REPORT_STATISTIC', 'Progress Report Statistic'); 
define('TABLE_HEADING_SUPPLIER', 'Supplier');
define('TABLE_HEADING_SUPPLIER_CODE', 'Supplier Code'); 
define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_COMMENTS', 'Comments'); 
define('TABLE_HEADING_MODIFIED_BY', 'Changed By');
define('TABLE_HEADING_SHOW_TO_SUPPLIER', 'Show to supplier');
 
define('TABLE_HEADING_COMPLETED_DATE', 'Completed Date');

define('TABLE_AMOUNT_ADJUST', 'Amount Adjust');

define('ERROR_NON_PROCESSING_ORDERS_PRODUCTS', 'Error: There is some custom products which is not in Complete status.');

define('EMAIL_SUBJECT_CUSTOM_PRODUCT_SUBMITED', 'New Powerleveling order submited');
define('EMAIL_CONTENT_CUSTOM_PRODUCT_SUBMITED', 'Hello %s' . "\n\n" . 'A new powerleveling order has been submited. Please work on it as soon as possibles (#ds)' . "\n" . '新的代练单子已提交，请你们尽快开始' . "\n\n" . 'If you having problems with this powerleveling order, please send an <NAME_EMAIL> along with the order number and a brief description of your problem.' . "\n" . '如果您有任何代练单子问题，请联络我们的采购部 <EMAIL> ，信件内容必须附有单子编号和问题检阅' . "\n\n\n" . 'Regards,' . "\n" . '-------------------------------------' . "\n" . 'OffGamers - Your Gaming Alliance' . "\n" . '-------------------------------------' . "\n\n" . '- This is an automated message. Do not reply. -' . "\n" . '这是自动回答信息。请不要回复。');

define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_PAYMENT_SUBJECT', implode(' ',array(EMAIL_SUBJECT_PREFIX, "Supplier New Payment #%s")));
define('EMAIL_TEXT_PAYMENT_TITLE', 'New payment has been paid to you.');
define('EMAIL_TEXT_PAYMENT_SUMMARY_TITLE', 'Custom Product Payment Summary:');
define('EMAIL_TEXT_PAYMENT_NUMBER', 'Payment Number: %s');
define('EMAIL_TEXT_PAYMENT_AMOUNT', 'Amount Paid: %s');
define('EMAIL_TEXT_PAYMENT_DATE', 'Payment Date: %s');
define('EMAIL_TEXT_PAYMENT_COMMENTS', 'Comment:' . "\n%s\n");
define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.'); 
define('EMAIL_SUBJECT_PWL_PRICE_ADJUST', 'Power Leveling Price Adjustment #%s'); 

define('IMAGE_BUTTON_SEARCH', 'Search');
define('IMAGE_BUTTON_RESET', 'Reset');
 
define('SUCCESS_MAKE_PAYMENT', 'Success: New payment #%d has been successfully created.'); 

define('LEGEND_MSG_SENT', 'Message Sent');
define('LEGEND_MSG_RECEIVED', 'Message Received');

define('WARNING_SUPPLIER_TASKS_BILLED_RATIO', 'Warning: No ratio adjustment is allowed as this task has been billed.');
define('WARNING_SUPPLIER_TASKS_BILLED_PRICE', 'Warning: No price adjustment is allowed as this task has been billed.');
?>