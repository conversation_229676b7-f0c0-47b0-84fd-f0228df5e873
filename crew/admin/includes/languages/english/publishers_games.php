<?
/*
  	$Id: publishers_games.php,v 1.7 2012/04/12 10:50:36 weichen Exp $
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Publishers Games');
define('HEADING_TITLE_PUBLISHERS_GAMES', 'Publishers Games Listing');
define('HEADING_TITLE_INSERT', 'Insert Publishers Games');
define('HEADING_TITLE_UPDATE', 'Update Publishers Games');
define('HEADING_TITLE_GAMES_CONFIGURATION', 'Games Configuration');
define('HEADING_TITLE_GAMES_CONFIGURATION_INSERT', 'Insert Games Configuration');
define('HEADING_TITLE_GAMES_CONFIGURATION_UPDATE', 'Update Games Configuration');

define('LINK_ADD_PUBLISHERS_GAMES', 'Add Publishers Games');
define('LINK_BACK_PUBLISHERS_GAMES', 'Back to Publishers Games Listing');
define('LINK_ADD_NEW_PRODUCTS', 'Add products');
define('LINK_RESET_TOP_UP_AMOUNT', 'Reset Top-up Amount');

define('TEXT_INFO_PUBLISHER_INSERTED', 'Publisher inserted');
define('TEXT_INFO_PUBLISHER_UPDATED', 'Publisher updated');
define('TEXT_INFO_PUBLISHER_DELETED', 'Publisher deleted');
define('TEXT_INFO_PUBLISHER_GAMES_DELETED', 'Publisher game deleted');
define('TEXT_INFO_PUBLISHER_GAMES_CONFIGURATION_INSERTED', 'Publisher games configuration inserted');
define('TEXT_INFO_PUBLISHER_GAMES_CONFIGURATION_UPDATED', 'Publisher games configuration updated');
define('TEXT_INFO_PUBLISHER_GAMES_CONFIGURATION_DELETED', 'Publisher games configuration deleted');

define('TABLE_HEADING_ID', 'ID');
define('TABLE_HEADING_PUBLISHERS_NAME', 'Publishers Name');
define('TABLE_HEADING_GAMES', 'Games');
define('TABLE_HEADING_GAMES_CONFIGURATION', 'Games Configurations');
define('TABLE_HEADING_GAMES_PRODUCTS', 'Games Products');
define('TABLE_HEADING_SORT_ORDER', 'Sort Order');
define('TABLE_HEADING_SYSTEM_TYPE', 'System Type');
define('TABLE_HEADING_UNIQUE_PRODUCT_CODE', 'Unique Product Code');
define('TABLE_HEADING_TOP_UP_AMOUNT', 'Top-up Amount');
define('TABLE_HEADING_SUPPORT_DIRECT_TOP_UP', 'Support Direct Top-up Mode');

define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_LAST_MODIFIED', 'Last Modified');
define('TABLE_HEADING_ACTION', 'Action');

define('ERROR_PUBLISHERS_NOT_FOUND', 'Publisher not found!');
define('ERROR_INVALID_PUBLISHERS_DATA', 'Invalid publisher data');
define('ERROR_INVALID_PUBLISHERS_GAMES_DATA', 'Invalid publisher game data');
define('ERROR_DUPLICATE_PRODUCT_ASSIGNED', 'This Product been assigned to other publisher.');
define('ERROR_INVALID_DELIVERY_MODE', 'Invalid product\'s delivery mode, only Direct Top-up allowed.');
define('ERROR_INVALID_TOP_UP_AMOUNT', 'Invalid top-up amount');
define('ERROR_INVALID_TOP_UP_INFO_SETTING', 'Invalid top-up info setting');
define('ERROR_UNABLE_UPDATE_DUE_TO_DIFF_SUB_BUNDLE', 'Unable to update, other product which share under same budle has different setting.');

define('TEXT_INFO_NO_RECORD', 'No record found');

define('ENTRY_GAME_NAME', 'Game Name');
define('ENTRY_GAME_MAPPING', 'Game Mapping');
define('ENTRY_GAME_SERVER_LIST', 'Game Server List');
define('ENTRY_GAME_PENDING_MESSAGE', 'Pending Message');
define('ENTRY_GAME_RELOADED_MESSAGE', 'Reloaded Message');
define('ENTRY_GAME_FAILED_MESSAGE', 'Failed Message');
define('ENTRY_GAME_REMARK', 'Remark');
define('ENTRY_TOP_UP_DAILY_LIMIT', 'Daily Top-up Limit');
define('ENTRY_DAILY_TOPPED_AMOUNT', 'Today Top-up Amount');
define('ENTRY_GAME_STATUS', 'Status');
define('ENTRY_VALIDATE_ACCOUNT_URL', 'Validate Account URL');

define('ENTRY_PRODUCT_ID', 'Product ID');
define('ENTRY_PRODUCT_CODE', 'Product Code');
define('ENTRY_AMOUNT_TYPE', 'Amount Type');
define('ENTRY_AMOUNT', 'Amount');
define('ENTRY_MERCHANT_ID', 'Merchant ID');
define('ENTRY_ACCOUNT', 'Account');
define('ENTRY_SERVER', 'Server');
define('ENTRY_CHARACTER', 'Character');
define('ENTRY_ACCOUNT_PLATFORM', 'Account Platform');
define('ENTRY_ACCOUNT_SUBID', 'Account Sub ID');
define('ENTRY_INFO_LABEL', 'Info Label');

define('JS_CONFIRM_DELETE', 'Are you sure to perform delete?');
?>