<?
/*
 * Development Date: 13/03/2017
 * Developer: <PERSON><PERSON><PERSON>
 * Task: PO System Phase 2 - Task 3
 */

define('HEADING_TITLE', 'Publishers Listing (API Replenish)');
define('HEADING_TITLE_INSERT', 'Publishers Insert');
define('HEADING_TITLE_UPDATE', 'Publishers Update');

define('LINK_ADD_PUBLISHERS', 'Add Publishers');

define('TABLE_HEADING_ID', 'ID');
define('TABLE_HEADING_NAME', 'Name');
define('TABLE_HEADING_SORT_ORDER', 'Sort Order');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_LAST_MODIFIED', 'Last Modified');
define('TABLE_HEADING_ACTION', 'Action');

define('ERROR_PUBLISHER_HAS_PENDING_TOPUP', 'Publisher has pending top-up (%s)');
define('ERROR_PUBLISHERS_NOT_FOUND', 'Publisher not found!');
define('ERROR_INVALID_PUBLISHERS_DATA', 'Invalid publisher data');
define('ERROR_INVALID_TOP_UP_URL', 'Invalid Top-up URL');
define('ERROR_INVALID_PUBLISHERS_PUB_KEY', 'Invalid publisher public key');
define('ERROR_INVALID_PUBLISHERS_PRI_KEY', 'Invalid publisher private key');
define('ERROR_INVALID_PUBLISHERS_SECRET_KEY', 'Invalid publisher secret key');
define('ERROR_INVALID_PUBLISHERS_MERCHANT_ID', 'Invalid publisher merchant ID');
define('ERROR_PUBLISHERS_NAME', 'Invalid publisher name');
define('ERROR_PUBLISHERS_SUPPLIER_ID', 'Please select supplier');
define('ERROR_PUBLISHERS_SKU', 'Invalid publisher sku');
define('ERROR_PUBLISHERS_API_PROVIDER', 'Invalid publisher api provider');
define('ERROR_PUBLISHERS_API_METHOD', 'Invalid publisher api method');
define('ERROR_PUBLISHERS_API_ID', 'Invalid Publisher Api ID');
define('ERROR_DUPLICATE_PUBLISHERS_API_ID', 'Duplicate Publisher Api ID');

define('TEXT_INFO_PUBLISHER_INSERTED', 'Publishers inserted. Click <a href="'.tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'subaction')).'action=edit&pID=%d').'">here</a> to edit publishers\' info and click <a href="'.tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&pID=%d').'">here</a> to edit publishers\' games info.');
define('TEXT_INFO_PUBLISHER_UPDATED', 'Publishers updated');
define('TEXT_INFO_PUBLISHER_DELETED', 'Publishers deleted');

define('ENTRY_PUBLISHERS_ID', 'Publishers ID');
define('ENTRY_PUBLISHERS_NAME', 'Publishers Name');
define('ENTRY_PUBLISHERS_SUPPLIER', 'Publishers Supplier');
define('ENTRY_PUBLISHERS_STATUS', 'Publishers Status');
define('ENTRY_PUBLISHERS_TERMS', 'Publishers Terms');
define('ENTRY_PUBLISHERS_SECRET_KEY', 'Secret Key');
define('ENTRY_PUBLISHERS_PUBLIC_KEY_FILENAME', 'Public Key Filename');
define('ENTRY_PUBLISHERS_PRIVATE_KEY_FILENAME', 'Private Key Filename');
define('ENTRY_PUBLISHERS_TOP_UP_URL', 'Top-up URL');
define('ENTRY_PUBLISHERS_STATUS_URL', 'Top-up Status URL');
define('ENTRY_PUBLISHERS_SERVER_URL', 'Get Server URL');
define('ENTRY_PUBLISHERS_SECRET_KEY', 'Secret Key');
define('ENTRY_PUBLISHERS_OGM_MERCHANT_ID', 'OffGamers Merchant ID');
define('ENTRY_PUBLISHER_API_ID', 'Publisher API ID');
define('ENTRY_PUBLISHERS_MIN_BALANCE_CURRENCY', 'Min Balance Currency');
define('ENTRY_PUBLISHERS_MIN_BALANCE', 'Min Balance');
define('ENTRY_PUBLISHERS_LOW_BALANCE_NOTIFICATION', 'Low balance notification');
define('ENTRY_PUBLISHERS_TOP_UP_LIMIT', 'Top-up Limit');
define('ENTRY_PUBLISHERS_TOP_UP_MODE', 'Top-up Mode');

define('ENTRY_SKU', 'SKU');
define('ENTRY_API_PROVIDER', 'API Provider');
define('ENTRY_API_METHOD', 'API Method');
define('ENTRY_SKU', 'SKU');
define('ENTRY_SORT_ORDER', 'Sort Order');
define('ENTRY_REMARK', 'Remark');
?>