<?
define('HEADER_FORM_REDEEM_LIST_CRITERIA_TITLE', 'Redemption Lists Criteria');
define('HEADER_FORM_REDEEM_LIST', 'Redemption Lists');
define('HEADER_FORM_REDEEM_EDIT', 'Edit Redemption');

define('TABLE_HEADING_REDEEM_ID', 'Redemption ID');
define('TABLE_HEADING_REDEEM_DATE', 'Redemption Date');
define('TABLE_HEADING_REDEEM_USER', 'Supplier/Customer Name');
define('TABLE_HEADING_REDEEM_SP_STATEMENT', 'OP Statement');
define('TABLE_HEADING_REDEEM_TOTAL_POINTS_REDEEM', 'Total OP Redeemed');
define('TABLE_HEADING_REDEEM_AMOUNT', 'Amount');
define('TABLE_HEADING_REDEEM_REMARK', 'Remarks');
define('TABLE_HEADING_REDEEM_REFERENCE', 'Redemption Reference');
define('TABLE_HEADING_REDEEM_ACTION', 'Action');
define('TABLE_HEADING_REDEEM_BENEFICIARY_INFO', 'Beneficiary Information');
define('TABLE_HEADING_REDEEM_INFO', 'Redemption Information');

define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_BENEFICIARY_NOTIFIED', 'Beneficiary Notified');
define('TABLE_HEADING_COMMENTS', 'Comments');
define('TABLE_HEADING_CHANGED_BY', 'Changed By');
define('TABLE_HEADING_ACTION', 'Action');

define('ENTRY_REDEEM_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_REDEEM_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_REDEEM_ID', 'Redemption ID');
define('ENTRY_REDEEM_STATUS', 'Redemption Status');
define('ENTRY_REDEEM_REFERENCE', 'Redemption Reference');
define('ENTRY_REDEEM_DATE_TIME', 'Redemption Date');
define('ENTRY_DATE_LAST_MODIFIED', 'Last Modified On');
define('ENTRY_REDEEM_TELEPHONE', 'Telephone Number');
define('ENTRY_REDEEM_MOBILE', 'Mobile Phone');
define('ENTRY_POINTS_REDEEM_AMOUNT', 'OP Redeem Amount');
define('ENTRY_REDEEM_SC_REQUEST_AMOUNT', 'Store Credit Request Amount');
define('ENTRY_REDEEM_SC_PAID_AMOUNT', 'Store Credit Paid Amount');
define('ENTRY_REDEEM_REMARK', 'Redemption Remark');
define('ENTRY_REDEEM_NOTIFY_BENEFICIARY', 'Show to Beneficiary');
define('ENTRY_CURRENT_REDEEM_STATUS', 'Current Redemption Status');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');

define('TEXT_REDEEM_REMARKS', 'OffGamers Redemption %s');
define('TEXT_TOTAL_OVERALL_AMOUNT', 'Overall Total:');
define('TEXT_TOTAL_SELECTED_AMOUNT', 'Selected Total:');
define('TEXT_TOTAL_SELECTED_RECORDS', '#:');
define('TEXT_REDEEM_NOT_APPLICABLE', '-');

define('TEXT_ANY', '<i>Any</i>');
define('TEXT_REDEEM_ESTIMATE_REDEEM_RECEIVED', 'If you do not receive this Redemption by %s you must contact <a href="mailto:<EMAIL>"><EMAIL></a> by %s or we will assume that the full redeem has been received by the intended beneficiary and we will not hold any further responsibility and/or liability.');

define('LINK_REDEEM_SP_STATEMENT', '<a href="%s" target="_blank">OP Statement</a>');

define('SUCCESS_REDEEM_BATCH_ACTION_PAYREF', 'Success: Batch action of Redemption reference has been successfully performed.');
define('SUCCESS_REDEEM_UPDATE', 'Success: The Redemption #%d has been successfully updated.');
define('SUCCESS_REDEEM_CANCELLATION', 'Success: The Redemption #%d has been successfully cancelled.');
define('WARNING_REDEEM_UPDATED_BY_SOMEONE', 'Warning: This Redemption just update by someone! Therefore no any update from you is done!');
define('WARNING_REDEEM_TRANSACTION_NOT_UPDATED', "Warning: The redeem #%d is not updated. \n这个兑换没有更新");
define('ERROR_REDEEM_TRANSACTION_DISABLED_WITHDRAWAL', "Error: Redeem withdrawal has been disabled.");

define('JS_ERROR_REDEEM_NO_PERMISSION', 'You have no permission for this action');
define('JS_ERROR_REDEEM_TRANS_NOT_EXISTS', 'The Redemption transaction does not exists');
define('JS_ERROR_REDEEM_TRANS_MODIFIED', "This Redemption just update by someone \n这兑换已被某人更新");
define('JS_ERROR_REDEEM_TRANS_UNKNOWN_STATUS', 'Unknown transaction status');
define('JS_ALERT_REDEEM_CONFIRM_UPDATE', 'Are you sure to complete this Redemption?\n你确定要完成这兑换吗？');

// Email definitions
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_REDEEM_REDEEM_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "OP Redemption request RDN-%s is now Credited")));

define('EMAIL_REDEEM_TEXT_TITLE', 'Your OP Redemption has been successfully credited into your store credit account and it is available for use immediately.');
define('EMAIL_REDEEM_TEXT_SUMMARY_TITLE', 'Redemption Summary:');
define('EMAIL_REDEEM_TEXT_REDEEM_NUMBER', 'Redemption no.: RDN-%s');
define('EMAIL_REDEEM_TEXT_PAID_AMOUNT', 'Amount credited: %s');
define('EMAIL_REDEEM_TEXT_COMMENTS', 'Comment:' . "\n%s\n");

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('TEXT_CRONJOB_NO_WITHDRAW_LIMIT', 'Any');
define('TEXT_LAST_STATUS_DATE', 'Last %s Date');
define('TEXT_ENTRY_DATE', 'Entry Date');
?>