<?php
/*
  	$Id: refund.php,v 1.6 2011/04/06 05:19:27 chingyen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADER_FORM_REFUND_LIST_CRITERIA_TITLE', 'Refund Lists Criteria');
define('HEADER_FORM_REFUND_LIST', 'Refund Lists');
define('HEADER_FORM_REFUND_EDIT', 'Edit Refund');

define('TABLE_HEADING_REFUND_ID', 'Refund ID');
define('TABLE_HEADING_REFUND_ORDER_ID', 'Order ID');
define('TABLE_HEADING_REFUND_USER', 'Supplier/Customer Name');
define('TABLE_HEADING_REFUND_USER_EMAIL', 'Supplier/Customer Email');
define('TABLE_HEADING_REFUND_PAYMENT_ORG_REF', 'Transaction ID');
define('TABLE_HEADING_REFUND_BENEFICIARY_INFO', 'Beneficiary Acct');
define('TABLE_HEADING_REFUND_PAYMENT_METHOD', 'Payment Method');
define('TABLE_HEADING_REFUND_PAYMENT_ENTITY', 'Payment Entity');
define('TABLE_HEADING_REFUND_PAYMENT_CURRENCY', 'Payment Currency');
define('TABLE_HEADING_REFUND_ORDER_TOTAL_AMOUNT', 'Ordered Amount');
define('TABLE_HEADING_REFUND_AMOUNT', 'Refund Amount');
define('TABLE_HEADING_REFUND_REFERENCE', 'Payment Reference');
define('TABLE_HEADING_REFUND_ACTION', 'Action');

define('TABLE_SECTION_HEADING_REFUND_BENEFICIARY_INFO', 'Beneficiary Information');
define('TABLE_SECTION_HEADING_REFUND_INFO', 'Refund Payment Information');

define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_BENEFICIARY_NOTIFIED', 'Beneficiary Notified');
define('TABLE_HEADING_COMMENTS', 'Comments');
define('TABLE_HEADING_CHANGED_BY', 'Changed By');
define('TABLE_HEADING_ACTION', 'Action');

define('ENTRY_REFUND_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_REFUND_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_REFUND_ID', 'Refund ID');
define('ENTRY_REFUND_STATUS', 'Refund Status');
define('ENTRY_REFUND_CHECKOUT_METHOD', 'Checkout Payment Method');
define('ENTRY_REFUND_PAYMENT_METHOD','Refund Payment Method');
define('ENTRY_REFUND_DATE_TIME', 'Refund Date');
define('ENTRY_REFUND_DATE_LAST_MODIFIED', 'Last Modified On');

define('ENTRY_REFUND_BENEFICIARY', 'Beneficiary');
define('ENTRY_REFUND_BENEFICIARY_EMAIL', 'Beneficiary E-mail');
define('ENTRY_REFUND_PAYMENT_ORG_REF', 'Transaction ID');
define('ENTRY_REFUND_PAYMENT_REFERENCE', 'Payment Reference');
define('ENTRY_REFUND_PAYPAL_TRANS_ID', 'Paypal Refund Transaction ID');
define('ENTRY_REFUND_ORDER_ID', 'Order ID');
define('ENTRY_REFUND_ORDER_TOTAL_AMOUNT', 'Ordered Amount');
define('ENTRY_REFUND_AVAILABLE_TOTAL_REFUND_AMOUNT', 'Available Refund Amount');
define('ENTRY_REFUND_AMOUNT', 'Refund Amount');
define('ENTRY_REFUND_FEES_BEAR_BY_CUSTOMER','Refund fees bear by customer');

define('ENTRY_REFUND_REMARK', 'Refund Payment Remark');
define('ENTRY_REFUND_NOTIFY_BENEFICIARY', 'Show to Beneficiary');

define('ENTRY_RECORDS_PER_PAGE', 'Records per page');

define('WARNING_REFUND_UPDATED_BY_SOMEONE', 'Warning: This refund payment just update by someone! Therefore no any update from you is done!');
define('SUCCESS_REFUND_PAYMENT_UPDATE', 'Success: The refund payment #%d has been successfully updated.');
define('SUCCESS_REFUND_PAYMENT_CANCELLATION', 'Success: The refund payment #%d has been successfully cancelled.');

define('EMAIL_PAYPAL_REFUND_SUBJECT', 'PayPal Payment Refunded');
define('EMAIL_PAYPAL_REFUND_CONTENT', 'This is to notify you that we have successfully refunded your Paypal payment and the Paypal Refund ID is %s. You may login to your Paypal account and search for the Paypal Refund ID to confirm the refund transaction.' . "\n\n" . 'If this refund is a mistake, please contact us immediately by replying to this email.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
/*

define('TEXT_REFUND_REMARKS', 'OffGamers Payment %s');
define('TEXT_REFUND_NOT_APPLICABLE', '-');

define('TEXT_REFUND_ESTIMATE_REFUND_RECEIVED', 'If you do not receive this payment by %s you must contact <a href="mailto:<EMAIL>"><EMAIL></a> by %s or we will assume that the full payment has been received by the intended beneficiary and we will not hold any further responsibility and/or liability.');

define('LINK_REFUND_ACCOUNT_STATEMENT', '<a href="%s" target="_blank">Account Statement</a>');

define('ERROR_REFUND_BATCH_MASS_REFUND_NOT_SUPPORTED', 'Error: Mass payment is not available for the selected payment method.');


// Email definitions
define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_REFUND_PAYMENT_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Payment Update #%s")));

define('EMAIL_REFUND_TEXT_TITLE', 'Your payment has been updated to the following status.');
define('EMAIL_REFUND_TEXT_SUMMARY_TITLE', 'Payment Summary:');
define('EMAIL_REFUND_TEXT_REFUND_NUMBER', 'Payment Number: %s');
define('EMAIL_REFUND_TEXT_PAID_AMOUNT', 'Amount Paid: %s');
define('EMAIL_REFUND_TEXT_REFUND_DATE', 'Payment Date: %s');
define('EMAIL_REFUND_TEXT_COMMENTS', 'Comment:' . "\n%s\n");
define('EMAIL_REFUND_TEXT_STATUS_UPDATE', "Status: %s");

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);
*/

define('TEXT_TOTAL_OVERALL_AMOUNT', 'Overall Total:');
define('TEXT_TOTAL_SELECTED_AMOUNT', 'Selected Total:');
define('TEXT_TOTAL_SELECTED_RECORDS', '#:');

define('TEXT_ANY', '<i>Any</i>');
define('TEXT_LAST_STATUS_DATE', 'Last %s Date');
define('TEXT_ENTRY_DATE', 'Entry Date');
define('TEXT_GST', '(GST %s)');
define('TEXT_SURCHARGE', '(Surcharge %s)');

// JavaScript Message
define('JS_ERROR_REFUND_NO_PERMISSION', 'You have no permission for this action');
define('JS_ERROR_REFUND_TRANS_NOT_EXISTS', 'The refund transaction does not exists');
define('JS_ERROR_REFUND_TRANS_MODIFIED', 'This refund transaction just update by someone');
define('JS_ERROR_REFUND_INVALID_REFUND_AMOUNT', 'The refund amount is either invalid or greater than order total amount');
define('JS_ERROR_REFUND_TRANS_UNKNOWN_STATUS', 'Unknown transaction status');

define('SUCCESS_REFUND_BATCH_ACTION_PAYREF', 'Success: Batch action of payment reference has been successfully performed.');
define('ERROR_REFUND_BATCH_ACTION_API_REFUND', 'Error: Refund #%d refund fail. (Error Code: %d) %s');
?>