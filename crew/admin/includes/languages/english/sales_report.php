<?php
/*
  	$Id: sales_report.php,v 1.30 2015/09/04 02:58:41 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Sales Report');
define('HEADING_INPUT_TITLE', 'Sales Report Criteria');

/*********************************************
	Input section
*********************************************/
define('ENTRY_PRODUCT_TYPE', 'Product Type');
define('ENTRY_CATEGORY', 'Categories');
define('ENTRY_ORDER_STATUS', 'Order Status');
define('ENTRY_PAYMENT_METHOD', 'Payment Method');
define('ENTRY_REPORT_TYPE', 'Report Type');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');
define('ENTRY_PRODUCTS_QUANTITY', 'Products Quantity');
define('ENTRY_SALES_PERIOD', 'Sales Period');
define('ENTRY_SORT', 'Sort by');
define('ENTRY_DATE_TYPE', 'Date Type');
define('ENTRY_ORDER_SITE', 'Order Site');

define('TEXT_INCLUDE_SUBCATEGORY', 'Include Sub Categories');
define('TEXT_CATEGORY_LEVEL', 'Category Level');
define('TEXT_AVAILABLE_QTY', 'Available Quantity');
define('TEXT_ACTUAL_QTY', 'Actual Quantity');

define('REPORT_TYPE_HOURLY'             , 'Hourly');
define('REPORT_TYPE_DAILY'              , 'Daily');
define('REPORT_TYPE_WEEKLY'             , 'Weekly');
define('REPORT_TYPE_MONTHLY'            , 'Monthly');
define('REPORT_TYPE_YEARLY'             , 'Yearly');
define('REPORT_TYPE_CATEGORY'           , 'Category');
define('REPORT_TYPE_CATEGORY_WITH_STOCK', 'Category (Sales + Stock)');
define('REPORT_TYPE_COUNTRY'            , 'Country');
define('REPORT_TYPE_CUSTOMER'           , 'Customers Discount Group'); 
define('REPORT_TYPE_NEW_SIGN_UP'        , 'New Sign Up');

define('TEXT_SORT_COUNTRY', 'Countries');
define('TEXT_SORT_CUSTOMER_COUNT', '# of Customers');
define('TEXT_SORT_ORDER_COUNT', '# of Orders');
define('TEXT_SORT_AVG_SALES', 'Avg. Order');
define('TEXT_SORT_TOTAL_SALES', 'Total Sales');
define('TEXT_SORT_CUSTOMER_GROUP', 'Customer Group');

define('TEXT_DATE_TYPE_PENDING_DATE', 'Pending Date');
define('TEXT_DATE_TYPE_FIRST_STATUS_DATE', 'First %s Date');

define('TEXT_SALES_BY_DAY', 'Daily Sales');
define('TEXT_SALES_BY_MONTH', 'Monthly Sales');

define('TEXT_ASC', 'Ascending');
define('TEXT_DESC', 'Descending');
/*********************************************
	Result section
*********************************************/
define('TABLE_HEADING_DATE'       , 'Date');
define('TABLE_HEADING_HOUR'       , 'Hours');
define('TABLE_HEADING_WEEKLY'     , 'Date (Mon to Sun)');
define('TABLE_HEADING_MONTH'      , 'Months');
define('TABLE_HEADING_YEAR'       , 'Years');
define('TABLE_HEADING_CATEGORY'   , 'Categories');
define('TABLE_HEADING_COUNTRY'    , 'Countries');
define('TABLE_HEADING_CUSTOMER'   , 'Customer Group');
define('TABLE_HEADING_NEW_SIGN_UP', 'New Sign Up');

define('TABLE_HEADING_CUSTOMERS_COUNT', '# of Customers');
define('TABLE_HEADING_ORDERS', '# of Orders');
define('TABLE_HEADING_SALES_PER_ORDER', 'Avg. Order');
define('TABLE_HEADING_DAILY_SALES', 'Daily Sales');
define('TABLE_HEADING_MONTHLY_SALES', 'Monthly Sales');
define('TABLE_HEADING_SALES', 'Total Sales');
define('TABLE_HEADING_VARIANCE', '+/- %');
define('TABLE_HEADING_AVAILABLE_QTY', 'Available Qty');
define('TABLE_HEADING_ACTUAL_QTY', 'Actual Qty');
define('TABLE_HEADING_TYPE', 'Type');
define('TABLE_HEADING_NEW_SIGN_UP_TOTAL', '# of New Sign Up (S)');
define('TABLE_HEADING_NEW_SIGN_UP_PURCHASE', '# of New Sign Up with Purchase (SWP)');
define('TABLE_HEADING_NEW_SIGN_UP_SUB_TOTAL', 'S');
define('TABLE_HEADING_NEW_SIGN_UP_SUB_PURCHASE', 'SWP');

define('ENTRY_AVERAGE_ORDER', 'Average Order:');

define('AVERAGE_HOURLY_TOTAL', 'Average Hourly Total:');
define('AVERAGE_DAILY_TOTAL', 'Average Daily Total:');
define('AVERAGE_MONTHLY_TOTAL', 'Average Monthly Total:');
define('AVERAGE_WEEKLY_TOTAL', 'Average Weekly Total:');
define('AVERAGE_YEARLY_TOTAL', 'Average Yearly Total:');

define('TODAY_TO_DATE', 'Daily Total:');
define('WEEK_TO_DATE', 'Total:');
define('MONTH_TO_DATE', 'Total:');
define('YEAR_TO_DATE', 'Total:');
define('YEARLY_TOTAL', 'Total Sales:');
define('TOTAL_SALES', 'Total Sales:');

define('TEXT_RANGE_OPERATOR', ' to ');
define('TEXT_COUNTRY_DEFINITION', "NOTE: Customer's country is based on the Order's IP Country.<br><br>");
define('TEXT_CUSTOMER_DEFINITION', "<b>NOTE:</b> Customer Discount Group based on Order made time since 2010-12-23");
define('TEXT_NEW_SIGN_UP', 'New Sign Up Member');

define('EXPORT_TEXT_DISCOUNT', 'DC');
define('EXPORT_TEXT_FULL_STORE_CREDIT', 'Full Store Credit');
define('EXPORT_TEXT_PARTIAL_STORE_CREDIT', 'SC');
define('EXPORT_TEXT_PARTIAL_GIFT_CARD', 'OGC');
define('EXPORT_TEXT_ORDER', '#Order');
define('EXPORT_TEXT_TOTAL', '#Total');
define('EXPORT_TEXT_QTY', '#Qty');
define('EXPORT_TEXT_SUB_TOTAL', 'SUB-TOTAL');

define('EXPORT_TEXT_ORDER_DATE', 'Order Date');
define('EXPORT_TEXT_ORDER_ID', 'Order #');
define('EXPORT_TEXT_PG', 'PG');
define('EXPORT_TEXT_PG_TXN_ID', 'Payment Transaction ID');
define('EXPORT_TEXT_SITE', 'Site');
define('EXPORT_TEXT_ENTITY', 'Entity');
define('EXPORT_TEXT_INV_NO', 'INV #');
define('EXPORT_TEXT_INV_TYPE', 'INV Type');
define('EXPORT_TEXT_CURRENCY', 'Currency');
define('EXPORT_TEXT_SUBTOTAL_ORIGINAL_CURRENCY', 'Subtotal Original Currency');
define('EXPORT_TEXT_SURCHARGE', 'Surcharge (+)');
define('EXPORT_TEXT_GST', 'GST (+)');
define('EXPORT_TEXT_DC', 'DC (-)');
define('EXPORT_TEXT_SC', 'SC (-)');
define('EXPORT_TEXT_OGC', 'OGC (-)');
define('EXPORT_TEXT_PG_AMOUNT', 'PG Amount');
define('EXPORT_TEXT_ORDER_RATE', 'Order Rate');
define('EXPORT_TEXT_SUBTOTAL_USD', 'Subtotal USD');
define('EXPORT_TEXT_GST_TAX_COUNTRY', 'GST Tax Country');
define('EXPORT_TEXT_GST_PERCENTAGE', 'GST %');
define('EXPORT_TEXT_PRODUCT_CATEGORIES_USD', 'Product Categories (USD)');
define('EXPORT_TEXT_GIFT_CARD', 'Gift Card');
define('EXPORT_TEXT_MULTI_GAME_CARD', 'Multi Game Card');
?>