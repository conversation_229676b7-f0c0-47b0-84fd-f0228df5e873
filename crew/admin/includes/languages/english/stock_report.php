<?
/*
  	$Id: stock_report.php,v 1.13 2009/11/06 07:26:54 henry.chow Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Stock Report');
define('HEADING_MOVEMENT_TITLE', 'Movement Stock Report');
define('HEADING_OUTSTANDING_PAYMENT_TITLE', 'Outstanding Payment Report');

define('HEADING_INPUT_TITLE', 'Stock Report Criteria');

define('ENTRY_REPORT_TYPE', 'Report Type');
define('ENTRY_HEADING_CATEGORY', 'Categories');
define('ENTRY_HEADING_PRODUCT_STATUS', 'Product Status');
define('ENTRY_HEADING_ALL_ITEMS', 'Show All Items');
define('ENTRY_HEADING_STOCK_LEVEL', 'Stock Level');
define('ENTRY_PRODUCTS_QUANTITY', 'Products Quantity');
define('ENTRY_HEADING_SORT', 'Sort by');
define('ENTRY_HEADING_RECORDS_PER_PAGE', 'Records per page');

define('TABLE_HEADING_OUTSTANDING_PAYMENT_USERS', 'Customer / Supplier');
define('TABLE_HEADING_PRODUCTS_PATH', 'Product Category Path');
define('TABLE_HEADING_PRODUCTS', 'Product Name');
define('TABLE_HEADING_PRODUCTS_LOCATION', 'Location');
define('TABLE_HEADING_PRODUCTS_STATUS', 'Status');
define('TABLE_HEADING_VERIFYING_QTY', 'Verifying Qty');
define('TABLE_HEADING_PENDING_PROCESSING_QTY', 'Processing Qty');
define('TABLE_HEADING_PURCHASING_QTY', 'Purchasing Qty');
define('TABLE_HEADING_QTY_LEFT', 'Available Qty');
define('TABLE_HEADING_QTY_LEFT_WITH_FIFO', 'Available Qty<br>(FIFO cost)');
define('TABLE_HEADING_ACTUAL_QTY', 'Actual Qty');
define('TABLE_HEADING_ACTUAL_QTY_WITH_FIFO', 'Actual Qty<br>(FIFO cost)');
define('TABLE_FOOTER_TOTAL', 'Total :');

define('TEXT_INCLUDE_SUBCATEGORY', 'Include Sub Categories');
define('TEXT_STATUS_ACTIVE', 'Active');
define('TEXT_STATUS_INACTIVE', 'Inactive');
define('TEXT_USE_PRODUCT_REORDER', 'Use "Reorder Level" Setting in Product List');
define('TEXT_AVAILABLE_QTY', 'Available Quantity');
define('TEXT_ACTUAL_QTY', 'Actual Quantity');
define('TEXT_ASC', 'Ascending');
define('TEXT_DESC', 'Descending');

define('TEXT_DISPLAY_CUSTOMER', 'Customer #%s');
define('TEXT_DISPLAY_SUPPLIER', 'Supplier #%s');

define('REPORT_TYPE_REAL_TIME_STOCK', 'Real Time Stock');
define('REPORT_TYPE_STOCK_MOVEMENT','Stock Movement');
define('REPORT_TYPE_OUTSTANDING_PAYMENT','Outstanding Payment');

define('JS_STOCK_REPORT_START_DATE','Please select start date!');
define('JS_STOCK_REPORT_END_DATE','Please select end date!');
define('JS_STOCK_REPORT_GREATER_DATE','Start Date is greater than End Date!');
define('JS_STOCK_REPORT_CATEGORY','Please select category!');
define('JS_STOCK_REPORT_PRODUCTS_STATUS','Please select at least one product status!');
define('JS_STOCK_REPORT_STOCK_LEVEL','Stock level must be an integer value!');
define('JS_STOCK_REPORT_PRODUCTS_QUANTITY','Please select at least one option for products quantity!');

define('BUTTON_STOCK_REPORT_EXPORT_REPORT', 'Export to CSV');
define('BUTTON_STOCK_REPORT_SHOW_REPORT', 'Show Report');
?>