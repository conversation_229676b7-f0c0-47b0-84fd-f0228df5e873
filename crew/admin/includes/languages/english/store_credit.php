<?php
/*
  	$Id: store_credit.php,v 1.21 2013/08/30 11:11:47 chingyen Exp $

  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue

  	Released under the GNU General Public License
*/

define('HEADER_FORM_SC_STAT_TITLE', 'Store Credit Statement');

define('TABLE_SECTION_HEADING_SC_STAT', '<b>Store Credit statement for %s (ID: %s)</b>');

define('TABLE_HEADING_SC_STAT_TRANS_ID', 'No.');
define('TABLE_HEADING_SC_STAT_DATETIME', 'Date/Time');
define('TABLE_HEADING_SC_STAT_ACTIVITY', 'Activity');
define('TABLE_HEADING_SC_STAT_ADDED_BY', 'By');
define('TABLE_HEADING_SC_STAT_COMMENT', 'Comment');
define('TABLE_HEADING_SC_STAT_RESERVE', 'Reserve');
define('TABLE_HEADING_SC_STAT_SC_TYPE', 'SC Type');
define('TABLE_HEADING_SC_STAT_DEBIT', 'Debit');
define('TABLE_HEADING_SC_STAT_CREDIT', 'Credit');
define('TABLE_HEADING_SC_STAT_USD_EQUIV', 'USD Equiv.');
define('TABLE_HEADING_SC_STAT_BALANCE', 'Balance');
define('TABLE_HEADING_SC_STAT_CUST_BALANCE', 'Customer Balance');
define('TABLE_HEADING_SC_STAT_CUST_EMAIL', 'Customer Email');
define('TABLE_HEADING_SC_STAT_MANUAL_ADD', 'Manual Addition');
define('TABLE_HEADING_SC_STAT_MANUAL_DEDUCT', 'Manual Deduction');
define('TABLE_HEADING_SC_STAT_CUST_ID', 'Customer ID');
define('TABLE_HEADING_SC_STAT_PG', 'Payment Gateway');
define('TABLE_HEADING_SC_STAT_CUST_EMAIL', 'Customer Email');
define('TABLE_HEADING_SC_STAT_CURRENCY', 'Currency');
define('TABLE_HEADING_SC_STAT_RSC', 'RSC');
define('TABLE_HEADING_SC_STAT_NRSC', 'NRSC');
define('TABLE_HEADING_SC_STAT_WSC', 'WSC');
define('TABLE_HEADING_SC_STAT_MINUS', '(-)');
define('TABLE_HEADING_SC_STAT_PLUS', '(+)');

define('ENTRY_SC_STAT_QUICK_SEARCH', 'Quick Search');
define('ENTRY_SC_STAT_ADVANCED_SEARCH', 'Advanced Search');

define('ENTRY_SC_STAT_REPORT_TYPE', 'Report Type');
define('ENTRY_SC_STAT_USER_ID', 'Customer ID');
define('ENTRY_SC_STAT_USER_EMAIL', 'Customer Email');
define('ENTRY_SC_STAT_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_SC_STAT_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_SC_TRANSACTION_ID', 'Transaction ID');
define('ENTRY_SC_TYPE', 'SC Type');
define('ENTRY_SC_ACTIVITY', 'Show');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');

define('ENTRY_SC_STAT_SC_TYPE', 'Store Credit Type');
define('ENTRY_SC_STAT_DEDUCT_AMOUNT', 'Deduct Amount');
define('ENTRY_SC_STAT_ADD_AMOUNT', 'Addition Amount');
define('ENTRY_SC_STAT_MANUAL_ADJUST_COMMENT', 'Comment');
define('ENTRY_SC_STAT_NOTIFY_USERS', 'Show Customer:');

define('TEXT_SC_STAT_NOT_APPLICABLE', '-');
define('TEXT_SC_STAT_TRANS_ID', '(Transaction ID: SC%s)');
define('TEXT_SC_STAT_DISABLE_WITHDRAWAL', 'Disable Withdrawal');
define('TEXT_SC_STAT_REVERSIBLE_RESERVED', 'RSC Reserve');
define('TEXT_SC_STAT_IRREVERSIBLE_RESERVED', 'NRSC Reserve');
define('TEXT_SC_STAT_WITHDRAWABLE_RESERVED', 'WSC Reserve');
define('TEXT_SC_STAT_SC_BALANCE', 'RC: <span class="%s">%s</span><br>NRC: <span class="%s">%s</span>');
define('TEXT_SC_STAT_MANUAL_ACTIVITY_SHOW', 'Show Customer');
define('TEXT_SC_STAT_MANUAL_ACTIVITY_HIDE', 'Not Show Customer');

define('TEXT_SC_STAT_ACTIVITY_AFFILIATE', 'Affiliate');
define('TEXT_SC_STAT_ACTIVITY_CANCEL', 'Cancellation');
define('TEXT_SC_STAT_ACTIVITY_COMPENSATE', 'Compensate');
define('TEXT_SC_STAT_ACTIVITY_PURCHASE', 'Purchase');
define('TEXT_SC_STAT_ACTIVITY_REDEEM', 'OP Redemption');
define('TEXT_SC_STAT_ACTIVITY_REFUND', 'Refund');
define('TEXT_SC_STAT_ACTIVITY_CONVERT', 'Currency Conversion');
define('TEXT_SC_STAT_ACTIVITY_SC', 'Store Credit Top Up');
define('TEXT_SC_STAT_ACTIVITY_EXTRA_SC', 'Extra Store Credit');

define('TEXT_SC_STAT_ACTIVITY_BUYBACK', 'Buyback Order');
define('TEXT_SC_STAT_ACTIVITY_G2G_SELL_ORDER', 'G2G Sell Order');
define('TEXT_SC_STAT_ACTIVITY_G2G_AFFILIATE', 'G2G Affiliate');
define('TEXT_SC_STAT_ACTIVITY_PAYMENT', 'Payment');
define('TEXT_SC_STAT_ACTIVITY_PAYMENT_WITHDRAW', 'OffGamers Payment');
define('TEXT_SC_STAT_ACTIVITY_PWL', 'PWL Order');
define('TEXT_SC_STAT_ACTIVITY_PO', 'Purchase Order');
define('TEXT_SC_STAT_ACTIVITY_DTU', 'DTU Payment Request');
define('TEXT_SC_STAT_ACTIVITY_API', 'API Replenish Payment Request');
define('TEXT_SC_STAT_ACTIVITY_CDK', 'PO Consignment CDK Payment Request');

define('TEXT_DEDUCT', ' (Deduct)');

define('TEXT_SC_STAT_EXPORT_CLOSING_BALANCE', 'export closing balance to csv');

define('LINK_SC_STAT_RESERVE', '[<a href="javascript:;" onClick="%s">Add to Reserve</a>]');
define('LINK_SC_STAT_LIFT_RESERVE', '[<a href="javascript:;" onClick="%s">Remove from Reserve</a>]');

define('ERROR_SC_STAT_INVALID_DEDUCTION_AMOUNT', 'Error: Invalid manual deduction amount');
define('ERROR_SC_STAT_INVALID_ADDITION_AMOUNT', 'Error: Invalid manual addition amount');
define('ERROR_SC_STAT_USER_NOT_EXISTS', 'Error: Customer does not exists');
define('ERROR_SC_STAT_SC_TYPE_NOT_EXISTS', 'Error: Invalid store credit type');
define('ERROR_SC_STAT_SC_CURRENCY_CHANGED', 'Error: Customer\'s store credit currency has been changed from %s to %s');
define('SUCCESS_SC_STAT_MANUAL_DEDUCTION', 'Success: Manual deduction has been successfully performed');
define('SUCCESS_SC_STAT_MANUAL_ADDITION', 'Success: Manual addition has been successfully performed');
define('SUCCESS_SC_STAT_SC_CREDITED', 'Success: Store credit has been successfully credited');
define('SUCCESS_SC_STAT_SC_DEBITED', 'Success: Store credit has been successfully debited');

define('JS_ERROR_SC_STAT_NO_PERMISSION', 'You have no permission for this action');
define('JS_ERROR_SC_STAT_SC_TYPE_NOT_EXISTS', 'This user does not has store credit account yet');
define('JS_ERROR_SC_STAT_TRANS_NOT_EXISTS', 'The transaction does not exists');
define('JS_ERROR_SC_STAT_TRANS_MODE_CHANGED', 'The transaction reserve status has been modified');
define('JS_ERROR_SC_STAT_TRANS_INVALID_MODE', 'Unknown operation');
define('JS_ERROR_SC_STAT_EMPTY_USER_ID', 'Please enter Customer ID');
define('JS_ERROR_SC_STAT_EMPTY_EMAIL', 'Please enter Customer Email');
define('JS_ERROR_SC_STAT_EMPTY_TRANSACTION_ID', 'Please enter Transaction ID');

// Report
define('REPORT_SC_STAT_TYPE_INDIVIDUAL', 'By Customer');
define('REPORT_SC_STAT_TYPE_SC_FLOW', 'Store Credit Movement');

// Email definitions
define('EMAIL_SEPARATOR', '--------------------------------------------------------------');

define('EMAIL_SC_STAT_MANUAL_UPDATE_SUBJECT', "Store Credit %s #SC-%s");
define('EMAIL_SC_STAT_MANUAL_ACTION_CONTENT', "Transaction ID: %s\nStore Credit Type: %s\n\n%s Amount: %s\nCustomer E-mail: %s\n\nUpdate Date: %s\nUpdate IP: %s\nUpdate User: %s\n\nUpdate Comment:\n%s");
define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
?>