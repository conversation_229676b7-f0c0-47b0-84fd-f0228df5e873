<?php
define('HEADER_FORM_SP_STAT_TITLE', 'OP Statement');

define('TABLE_SECTION_HEADING_SP_STAT', '<b>OP statement for %s (ID: %s)</b>');

define('TABLE_HEADING_SP_STAT_TRANS_ID', 'No.');
define('TABLE_HEADING_SP_STAT_DATETIME', 'Date/Time');
define('TABLE_HEADING_SP_STAT_ACTIVITY', 'Activity');
define('TABLE_HEADING_SP_STAT_PAYMENT_GATEWAY', 'Payment Gateway');
define('TABLE_HEADING_SP_STAT_ADDED_BY', 'By');
define('TABLE_HEADING_SP_STAT_COMMENT', 'Comment');
define('TABLE_HEADING_SP_STAT_DEBIT', 'Debit');
define('TABLE_HEADING_SP_STAT_CREDIT', 'Credit');
define('TABLE_HEADING_SP_STAT_BALANCE', 'Balance');
define('TABLE_HEADING_SP_STAT_MANUAL_ADD', 'Manual Addition');
define('TABLE_HEADING_SP_STAT_MANUAL_DEDUCT', 'Manual Deduction');
define('TABLE_HEADING_SP_STAT_CUST_ID', 'Customer ID');
define('TABLE_HEADING_SP_STAT_CUST_EMAIL', 'Customer Email');
define('TABLE_HEADING_SP_STAT_CURRENCY', 'Currency');
define('TABLE_HEADING_SP_STAT_OP', 'OP');
define('TABLE_HEADING_SP_STAT_NRSP', 'NRSP');
define('TABLE_HEADING_SP_STAT_WSP', 'WSP');
define('TABLE_HEADING_SP_STAT_MINUS', '(-)');
define('TABLE_HEADING_SP_STAT_PLUS', '(+)');

define('ENTRY_SP_STAT_QUICK_SEARCH', 'Quick Search');
define('ENTRY_SP_STAT_ADVANCED_SEARCH', 'Advanced Search');

define('ENTRY_SP_STAT_REPORT_TYPE', 'Report Type');
define('ENTRY_SP_STAT_USER_ID', 'Customer ID');
define('ENTRY_SP_STAT_USER_EMAIL', 'Customer Email');
define('ENTRY_SP_STAT_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_SP_STAT_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_SP_TRANSACTION_ID', 'Transaction ID');
define('ENTRY_SP_TYPE', 'SP Type');
define('ENTRY_SP_ACTIVITY', 'Show');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');

define('ENTRY_SP_STAT_SP_TYPE', 'Store Point Type');
define('ENTRY_SP_STAT_DEDUCT_AMOUNT', 'Deduct Amount');
define('ENTRY_SP_STAT_ADD_AMOUNT', 'Addition Amount');
define('ENTRY_SP_STAT_MANUAL_ADJUST_COMMENT', 'Comment');
define('ENTRY_SP_STAT_NOTIFY_USERS', 'Show Customer:');

define('TEXT_SP_STAT_NOT_APPLICABLE', '-');
define('TEXT_SP_STAT_TRANS_ID', '(Transaction ID: SP%s)');
define('TEXT_SP_STAT_DISABLE_WITHDRAWAL', 'Disable Withdrawal');
define('TEXT_SP_STAT_REVERSIBLE_RESERVED', 'RSP Reserve');
define('TEXT_SP_STAT_IRREVERSIBLE_RESERVED', 'NRSP Reserve');
define('TEXT_SP_STAT_WITHDRAWABLE_RESERVED', 'WSP Reserve');
define('TEXT_SP_STAT_SP_BALANCE', 'RP: <span class="%s">%s</span><br>NRP: <span class="%s">%s</span>');
define('TEXT_SP_STAT_MANUAL_ACTIVITY_SHOW', 'Show Customer');
define('TEXT_SP_STAT_MANUAL_ACTIVITY_HIDE', 'Not Show Customer');

define('TEXT_SP_STAT_ACTIVITY_REDEEM', 'Redemption');
define('TEXT_SP_STAT_ACTIVITY_BONUS', 'Bonus');
define('TEXT_SP_STAT_ACTIVITY_CANCEL', 'Cancellation');
define('TEXT_SP_STAT_ACTIVITY_PURCHASE', 'Purchase');
define('TEXT_SP_STAT_ACTIVITY_ORDER_ROLLBACK', 'Order Rollback');
define('TEXT_SP_STAT_EXPORT_CLOSING_BALANCE', 'export closing balance to csv');

define('ERROR_SP_STAT_INVALID_DEDUCTION_AMOUNT', 'Error: Invalid manual deduction amount');
define('ERROR_SP_STAT_INVALID_ADDITION_AMOUNT', 'Error: Invalid manual addition amount');
define('ERROR_SP_STAT_USER_NOT_EXISTS', 'Error: Customer does not exists');
define('ERROR_SP_STAT_SP_TYPE_NOT_EXISTS', 'Error: Invalid store point type');
define('SUCCESS_SP_STAT_MANUAL_DEDUCTION', 'Success: Manual deduction has been successfully performed');
define('SUCCESS_SP_STAT_MANUAL_ADDITION', 'Success: Manual addition has been successfully performed');
define('SUCCESS_SP_STAT_SP_CREDITED', 'Success: Store point has been successfully credited');
define('SUCCESS_SP_STAT_SP_DEBITED', 'Success: Store point has been successfully debited');

define('JS_ERROR_SP_STAT_NO_PERMISSION', 'You have no permission for this action');
define('JS_ERROR_SP_STAT_SP_TYPE_NOT_EXISTS', 'This user does not has store point account yet');
define('JS_ERROR_SP_STAT_TRANS_NOT_EXISTS', 'The transaction does not exists');
define('JS_ERROR_SP_STAT_TRANS_MODE_CHANGED', 'The transaction reserve status has been modified');
define('JS_ERROR_SP_STAT_TRANS_INVALID_MODE', 'Unknown operation');
define('JS_ERROR_SP_STAT_EMPTY_ID', 'Please enter Customer ID');
define('JS_ERROR_SP_STAT_EMPTY_EMAIL', 'Please enter Customer Email');
define('JS_ERROR_SP_STAT_EMPTY_TRANSACTION_ID', 'Please enter Transaction ID');

// Report
define('REPORT_SP_STAT_TYPE_INDIVIDUAL', 'By Customer');
define('REPORT_SP_STAT_TYPE_SP_FLOW', 'Store Point Movement');

// Email definitions
define('EMAIL_SEPARATOR', '--------------------------------------------------------------');

define('EMAIL_SP_STAT_MANUAL_UPDATE_SUBJECT', "OP %s #RBT-%s");
define('EMAIL_SP_STAT_MANUAL_ACTION_CONTENT', "Transaction ID: RBT-%s\n\n%s Amount: %s\nCustomer ID: %s\nCustomer E-mail: %s\n\nUpdate Date: %s\nUpdate IP: %s\nUpdate User: %s\n\nUpdate Comment:\n%s");
define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
?>