<?
define('TEXT_BLANK_KEY', 'Empty Key!');
define('TEXT_ACCOUNT_ACTIVATED', 'Account successfully activated and the user has been notified.');
define('TEXT_KEY_DOES_NOT_EXIST', 'The key does not exist.');

define('MAIL_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Supplier Account Activated")));
define('MAIL_BODY', 'Congratulation! Your account, %s, has been activated and you may now sign in to our supplier module at the following link,

'.'<a href="'.tep_supplier_href_link('login.php', '', 'SSL').'">'.tep_supplier_href_link('login.php', '', 'SSL').'</a>' .'

We are looking forward to a long term relationship with you.  Thanks again for supporting '.STORE_NAME.'.');
define('MAIL_SUPPLIER_FOOTER', "\n\n".STORE_EMAIL_SIGNATURE);

?>