<?
if ($_REQUEST['action']=='edit_supplier') {
	define('HEADING_TITLE', 'Edit Supplier');
} else {
	define('HEADING_TITLE', 'Suppliers List');
}

define('TABLE_HEADING_SUPPLIER_CODE', 'Code');
define('TABLE_HEADING_SUPPLIER_LASTNAME', 'Last Name');
define('TABLE_HEADING_SUPPLIER_FIRSTNAME', 'First Name');
define('TABLE_HEADING_SUPPLIER_EMAIL', 'E-mail Address');
define('TABLE_HEADING_DATE_CREATED', 'Account Created');
define('TABLE_HEADING_SUPPLIER_GROUP', 'Group Level');
define('TABLE_HEADING_OUTSTANDING_AMOUNT', 'Processing (Outstanding Amount)');
define('TABLE_HEADING_SUPPLIER_STATUS', 'Status');
define('TABLE_HEADING_PURCHASE_MODE', 'Purchase Mode');
define('TABLE_HEADING_DEDUCT_FROM_ORDER', 'Deduct From Order ID');
define('TABLE_HEADING_ACTION', 'Actions');

define('CATEGORY_PAYMENT_DETAILS','Supplier get paid by:');
define('CATEGORY_TASKS', 'Task:');

define('TEXT_ON', 'On');
define('TEXT_OFF', 'Off');
define('TEXT_GROUP', 'Follow Group Mode');
define('TEXT_NOT_ASSIGNED_GROUP', 'No Group Assigned');

define('ENTRY_SUPPLIER_CODE', 'Supplier Code:');
define('ENTRY_SUPPLIERS_GROUPS_NAME', 'Group Name:');

define('ENTRY_SUPPLIER_WITHDRAW_RESERVE_AMOUNT', 'Reserve Amount:');
define('ENTRY_SUPPLIER_PAYMENT_PAYPAL','PayPal Account E-mail:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_NAME','Bank Name:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS','Bank Address:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER','Bank Phone Number:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME','Beneficiary Name:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER','Beneficiary Account Number:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_BRANCH_NUMBER','ABA/BSB number (branch number)');
define('ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE','Bank SWIFT Code ');
define('ENTRY_SUPPLIER_COMPANY','Company');
define('ENTRY_SUPPLIER_MAXIMUM_TASKS', 'Maximum Tasks:');
define('ENTRY_SUPPLIER_MAXIMUM_TASKS_ERROR', '&nbsp;<span class="errorText">Maximum Tasks cannot be less than Available Slots.</span>');
define('ENTRY_SUPPLIER_AVAILABLE_SLOT', 'Available Slots:');
define('ENTRY_SUPPLIER_AVAILABLE_SLOT_ERROR', '&nbsp;<span class="errorText">Available Slots cannot be greater than Maximum Tasks.</span>');
define('ENTRY_SUPPLIER_RATIO', 'Ratio:');
define('ENTRY_USE_REMOTE_ACCOUNT_LOGIN_API', 'Use Remote Account Login API:');
define('ENTRY_SUPPLIER_REMARKS', 'Remark:');

define('SUCCESS_SUPPLIER_UPDATED', 'Success: Supplier, %s, has been successfully updated.');
define('SUCCESS_SUPPLIER_DELETED', 'Success: Supplier has been successfully deleted.');

define('EMAIL_TEXT_SUBJECT_NEW_LIST', implode(' ',array(EMAIL_SUBJECT_PREFIX, "New Supplier List")));
define('EMAIL_TEXT_CLOSING', 'Thank you for supporting '.STORE_NAME.'.');
define('EMAIL_FOOTER', "\n\n".STORE_EMAIL_SIGNATURE);
?>