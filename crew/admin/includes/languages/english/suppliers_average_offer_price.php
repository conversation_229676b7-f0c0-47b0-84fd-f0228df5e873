<?php
/*
  	$Id: suppliers_average_offer_price.php,v 1.16 2010/04/06 04:01:57 keepeng.foong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', BOX_SUPPLIERS_AVERAGE_OFFER_PRICE);

define('TABLE_HEADING_MAIN_CATEGORY', 'Main Category');
define('TABLE_HEADING_SITE', 'Site');
define('TABLE_HEADING_SALES_RETRIEVAL_METHOD', 'Get Average Sales');
define('TABLE_HEADING_SALES_RETRIEVE_BY_DATE_RANGE', 'By Date Range');
define('TABLE_HEADING_SALES_RETRIEVE_BY_LAST', 'By Last');
define('TABLE_HEADING_SALES_START_DATE', 'Sales Start Date');
define('TABLE_HEADING_SALES_END_DATE', 'Sales End Date');
define('TABLE_HEADING_DAYS_INVENTORY', 'No. Days Inventory');
define('TABLE_HEADING_ROW_COLOUR', 'Row Colour');
define('TABLE_HEADING_PROFIT', 'Profit (&#37;)');
define('TABLE_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_MIN', 'Min');
define('TABLE_HEADING_MAX', 'Max');
define('TABLE_HEADING_ERROR_MSG', 'Error Message');

define('TABLE_HEADING_MAIN_CATEGORY', 'Main Category');
define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_SELLING_PRICE', 'Selling Price');
define('TABLE_HEADING_CN_BUYBACK_PRICE', 'CN Buyback Price');
define('TABLE_HEADING_RATIO_PRICE', '% / $');
define('TABLE_HEADING_PRICE_ADJUST', 'Price Adjust');

define('TABLE_HEADING_BRACKET', 'Bracket(%s)');
define('TABLE_HEADING_BRACKET_NUMBER', 'Bracket No.');
define('TABLE_HEADING_BRACKET_NEW', 'New Bracket');
define('TABLE_HEADING_PRODUCT', 'Product Name');
define('TABLE_HEADING_PERCENTAGE_OF_QUANTITY', 'Percentage of Max Inventory Qty');

define('TABLE_HEADING_MAX_QUANTITY', 'Maximum Qty');
define('TABLE_HEADING_OVERWRITE_MAX_QTY', 'Overwrite Maximum Qty');
define('TABLE_HEADING_AVAILABLE_QUANTITY', 'Forecast Available Qty');
define('TABLE_HEADING_ACTUAL_QUANTITY', 'Forecast Actual Qty');
define('TABLE_HEADING_SUGGEST_QUANTITY', 'Suggest Qty');
define('TABLE_HEADING_CUSTOMER_PRICE', 'Customer Price');
define('TABLE_HEADING_AVERAGE_MARKET_PRICE', 'Market Price (Avg)');
define('TABLE_HEADING_AVERAGE_OFFER_PRICE', 'Offer Price (Avg)');

define('TABLE_HEADING_BRACKET_SET_ID', 'Offer Price Bracket Set ID');
define('TABLE_HEADING_ACTIVE_BRACKET', 'Offer Price Active Bracket');
define('TABLE_HEADING_ASSIGN_BRACKET_SET', 'Assign Bracket Set');
define('TEXT_CLEAR', 'Clear');

define('ENTRY_PREF_DO_NOT_BUYBACK', 'Do not buyback: ');
define('ENTRY_PREF_BUYBACK_LIST_CONTROLLER', 'Buyback List Controller: ');
define('ENTRY_PREF_BUYBACK_LIST_CLOSE_CONTROLLER', '<span class="redIndicator">CLOSE</span> buyback list if Pending Orders >= ');
define('ENTRY_PREF_BUYBACK_LIST_OPEN_CONTROLLER', '<span class="greenIndicator">OPEN</span> buyback list if Pending Orders <= ');
define('ENTRY_PREF_PURCHASE_ETA_OFFSET', 'Purchase ETA Offset (+/- hour): ');
define('ENTRY_PREF_TRADING_PLACE', '(Trading Place)');
define('ENTRY_PREF_DELIVERY_OPTION', 'Delivery Option: ');
define('ENTRY_PREF_DEALING_ON_GAME', 'F2F ');
define('ENTRY_PREF_DEALING_ON_MAIL', 'Mail ');
define('ENTRY_PREF_CUSTOMER_CONFIRMATION_DURATION', 'CO Received Confirmation Duration');
define('ENTRY_PREF_VIP_TRADE_MODE', 'VIP Trade Mode: ');
define('ENTRY_PREF_VIP_TRADE_WITH_US', 'Trade With OffGamers');
define('ENTRY_PREF_VIP_TRADE_WITH_CUSTOMERS', 'Trade With Customer');
define('ENTRY_PREF_VIP_ORDERS_EXPIRY_DURATION', 'VIP Order Expired Duration');
define('ENTRY_PREF_VIP_REQUEST_CANCELLATION_DURATION', 'VIP Request Cancellation Duration');
define('ENTRY_PREF_VIP_ORDERS_EXPIRY_DURATION_IN_MINUTES', 'Minutes');

define('TEXT_UNCHECK_ALL_TO_DISABLE_VIP', 'Uncheck all to disable VIP');

define('TEXT_PCT_MAX_INVENTORY', 'Max Inventory');
define('TEXT_PCT_MARKET_PRICE', 'Market Price');

define('TEXT_DELETE', 'Delete');
define('TEXT_OFP_TIME_RANGE', 'to');
define('TEXT_OFP_NOT_BUYBACK_ANYTIME', '(Leave blank if not buyback anytime)');

define('TEXT_PREFERENCES_FOR', 'Preferences for');
define('TEXT_OFFER_PRICE_BRACKETS_FOR', 'Offer Price Brackets for: ');
define('TEXT_PRICES_FOR', 'Prices for');
define('TEXT_DAYS', 'days');
define('TEXT_SYSTEM_DEFINED', 'System Defined');

define('MESSAGE_INVALID_MAX_INV_SPACE', 'Invalid Maximum Inventory Space.');
define('MESSAGE_MAXQTY_USAGE', 'The overwrite value is only used when ; <br>- there is value entered ');
define('ERROR_IMPORTED_PRODUCT_NOT_CAT', 'Error: Product #%d does not match any of this Main Category\'s products!');
define('ERROR_IMPORT_FAILED', 'Error: Import failed.');

define('IMAGE_BUTTON_UPDATE_BRACKETS', 'Update Brackets');
define('IMAGE_BUTTON_UPDATE_OFFER_PRICES', 'Update Offer Prices');
define('IMAGE_BUTTON_UPDATE_PREFERENCES', 'Update Preferences');

define('ENTRY_SELLING_PRICE', 'Selling Price (%)');
define('ENTRY_CN_BUYBACK_PRICE', 'CN Buyback Price (%)');
define('ENTRY_OVERWRITE_PRICE', 'Overwrite Price ($)');

define('TEXT_APPLY_TO_SELECTED', 'Apply to selected');
?>