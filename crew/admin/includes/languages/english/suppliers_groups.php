<?
if ($_REQUEST['action']=='new_supplier_group') {
	define('HEADING_TITLE', 'New Supplier Group');
} else if ($_REQUEST['action']=='edit_supplier_group') {
	define('HEADING_TITLE', 'Edit Supplier Group');
} else {
	define('HEADING_TITLE', 'Suppliers Grouping');
}

define('TABLE_HEADING_PURCHASE_LIST', 'Purchase Lists Name');
define('TABLE_HEADING_GROUPS_NAME', 'Groups Name');
define('TABLE_HEADING_ACTIVE_SUPPLIER', 'Active Supplier');
define('TABLE_HEADING_GROUPS_STATUS', 'Status');
define('TABLE_HEADING_PURCHASE_MODE', 'Purchase Mode');
define('TABLE_HEADING_ACTION', 'Actions');

define('ENTRY_GROUP_NAME', 'Group Name');
define('ENTRY_GROUP_STATUS', 'Status');
define('ENTRY_PRODUCT_PURCHASE_STATUS', 'Show Server Status');

define('TEXT_SUPPLIER_LIST_STATUS', 'Supplier Page Status');
define('TEXT_CURRENT_TIME', 'Server Time');
define('TEXT_CURRENT_STATUS', 'Current Status');
define('TEXT_PURCHASE_MODE', 'Purchase Mode');
define('TEXT_ON', 'On');
define('TEXT_OFF', 'Off');
define('TEXT_AUTO', 'Auto');
define('TEXT_YES', '&nbsp;Yes');
define('TEXT_NO', '&nbsp;No');
define('TEXT_FIRST_LIST_TIME', 'First List: ');
define('TEXT_FIRST_LIST_EDITABLE_TIME', 'Editable since:&nbsp;');
define('TEXT_SECOND_LIST_TIME', 'Confirmation List: ');
define('TEXT_AUTO_ON_PERIOD', 'Start Time');
define('TEXT_AUTO_OFF_PERIOD', 'End Time');
define('TEXT_TO', ' to ');
define('TEXT_ANYTIME', 'Anytime');
define('TEXT_AUTO_ON', 'Auto turn ON if the module is OFF during operating hours');
define('TEXT_AUTO_OFF', 'Auto turn OFF if the module is ON outside operating hours');

define('LINK_ADD_SUPPLIER_GROUP', 'Add Supplier Group');
define('LINK_RESET_SUPPLIER_PURCHASE_MODE', 'Reset Suppliers Purchase Mode to Follow Group Mode');
define('LINK_HIDE_RSTK_CHAR', 'Do not show');

define('TEXT_ACTIVE', '&nbsp;Active &nbsp;');
define('TEXT_INACTIVE', '&nbsp;Inactive &nbsp;');

define('TEXT_INFO_GROUPS_NAME_FALSE', '<b>ERROR:</b> At least the group name must have more than 1 character!');
define('TEXT_INFO_GROUPS_NAME_USED', '<b>ERROR:</b> Group name has already been used!');

define('SUCCESS_GROUP_INSERTED', 'Success: Supplier group, %s, has been successfully created.');
define('SUCCESS_GROUP_UPDATED', 'Success: Supplier group, %s, has been successfully updated.');
define('SUCCESS_GROUP_DELETED', 'Success: Supplier group has been successfully deleted.');
define('WARNING_INVALID_FIRST_LIST_EDIT_TIME', 'Warning: The editable time setting for %s\'s first list was set to Anytime since it is not in the first list time range.');

define('JS_CONFIRM_RESET_SUP_PMODE', 'Are you sure to reset the purchase mode of all the active suppliers in this group to &quot;Follow Group Mode&quot;?\nA new list notification e-mail will be send to these suppliers.');
define('JS_CONFIRM_HIDE_RSTK_CHAR', 'Are you sure to hide all the restock characters from suppliers in this group?\n\nNOTE: You need to manually check the &quot;Show it to suppliers&quot; checkbox from Supplier Pricing page when confirm your latest restock characters.');

define('EMAIL_TEXT_SUBJECT_NEW_LIST', implode(' ',array(EMAIL_SUBJECT_PREFIX, "New Supplier List")));
define('EMAIL_TEXT_CONTENT', 'New supplier list (%s) is available now!' . "\n" . 'Please login from <a href="%s">%s</a> to submit the list.');
define('EMAIL_TEXT_CLOSING', 'Thank you for supporting '.STORE_NAME.'.');
define('EMAIL_FOOTER', "\n\n".STORE_EMAIL_SIGNATURE);
?>