<?
/*
  	$Id: suppliers_order_track.php,v 1.6 2007/01/25 10:11:11 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Suppliers Order Lists');
define('HEADING_INPUT_TITLE', 'Suppliers Order Lists Criteria');
define('HEADING_TITLE_SEARCH', 'Order ID:');

define('TABLE_HEADING_ORDER_NO', 'No.');
define('TABLE_HEADING_LIST_TITLE', 'Order List Title');
define('TABLE_HEADING_ORDER_DATE', 'Date');
define('TABLE_HEADING_SUP_NAME', 'Name');
define('TABLE_HEADING_SUP_EMAIL', 'Email');
define('TABLE_HEADING_ORDER_BILLING_STATUS', 'Billing Status');
define('TABLE_HEADING_ORDER_VERIFY_STATUS', 'Verified?');
define('TABLE_HEADING_TOTAL_AMT', 'Total Payable');
define('TABLE_HEADING_TOTAL_PAID', 'Total Paid');
define('TABLE_HEADING_TOTAL_BAL', 'Balance');
define('TABLE_HEADING_PARTIAL_PAY', '<span title="Leave blank for full payment">Partial Pay</span>');

define('ENTRY_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small>');
define('ENTRY_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD<br>/YYYY-MM-DD HH:MM)</small>');
define('ENTRY_SUPPLIER_GROUP', 'Supplier Group');
define('ENTRY_SUPPLIER', 'Supplier');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_ORDER_STATUS', 'Order Status');
define('ENTRY_CATEGORY', 'Categories');
define('ENTRY_PRODUCT_ID', 'Product ID');
define('ENTRY_SORT', 'Sort by');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');

define('TEXT_ANY', '<i>Any</i>');
define('TEXT_SELECTED_ORDERS_AMOUNT', '');

define('TEXT_SORT_FACTION_SERVERS', 'Faction then Server');
define('TEXT_SORT_SERVERS', 'Server');

define('TEXT_ASC', 'Ascending');
define('TEXT_DESC', 'Descending');

define('TEXT_INCLUDE_SUBCATEGORY', 'Include Sub Categories');

define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_PAYMENT_SUBJECT', implode(' ',array(EMAIL_SUBJECT_PREFIX, "Supplier New Payment #%s")));
define('EMAIL_TEXT_PAYMENT_TITLE', 'New payment has been paid to you.');
define('EMAIL_TEXT_PAYMENT_SUMMARY_TITLE', 'Supplier Payment Summary:');
define('EMAIL_TEXT_PAYMENT_NUMBER', 'Payment Number: %s');
define('EMAIL_TEXT_PAYMENT_AMOUNT', 'Amount Paid: %s');
define('EMAIL_TEXT_PAYMENT_DATE', 'Payment Date: %s');
define('EMAIL_TEXT_PAYMENT_COMMENTS', 'Comment:' . "\n%s\n");
define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('ERROR_NO_ORDERS_SELECTED', 'Error: There is no any supplier orders been selected for new payment.');
define('ERROR_NON_PROCESSING_ORDERS', 'Error: There is some orders which is not in Processing status.');
define('ERROR_MULTIPLE_SUPPLIER_PAYMENT', 'Error: Your selections includes orders for more than one supplier.');
define('ERROR_NEW_PAYMENT_FAILED', 'Error: Failed to make payment.');
define('SUCCESS_MAKE_PAYMENT', 'Success: New payment #%d has been successfully created.');
define('ERROR_NO_SUPPLIER_ORDERS_SELECTED', 'Error: There is no any supplier orders been selected.');
?>