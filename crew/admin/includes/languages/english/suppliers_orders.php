<?
/*
  	$Id: suppliers_orders.php,v 1.14 2007/10/19 07:36:15 chan Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Supplier Order %s');
define('HEADING_TITLE_SEARCH', 'Order ID:');
define('TABLE_HEADING_SUPPLIERS_INFO', 'Supplier Information');
define('TABLE_HEADING_PAYMENT_INFO', 'Payment Information');
define('TABLE_HEADING_SELECTED_ORDERS', 'Your Selected Orders');

define('ENTRY_SUPPLIER_ORDER_ID', 'Order Number:');
define('ENTRY_ORDER_STATUS', 'Order Status:');
define('ENTRY_ORDER_LIST_NAME', 'Order List Title:');
define('ENTRY_ORDER_DATE_TIME', 'First List Submitted On:');
define('ENTRY_DATE_LAST_MODIFIED', 'Last Modified On:');
define('ENTRY_SUPPLIER', 'Supplier:');
define('ENTRY_SUPPLIER_CODE', 'Supplier Code:');
define('ENTRY_SUPPLIER_SIGNUP_DATE', 'Sign Up Date:');
define('ENTRY_SUPPLIER_GENDER', 'Gender:');
define('ENTRY_SUPPLIER_DOB', 'Date of Birth:');
define('ENTRY_ORDERING_IP', 'Ordering IP:');

define('ENTRY_SUPPLIER_PAYMENT_PAYPAL','PayPal Account E-mail:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_NAME','Bank Name:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS','Bank Address:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER','Bank Phone Number:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME','Beneficiary Name:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER','Beneficiary Account Number:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE','Bank SWIFT Code ');

define('ENTRY_ADMIN_COMMENT', 'Admin Comment:');
define('ENTRY_STATUS', 'Status:');
define('ENTRY_NOTIFY_SUPPLIER', 'Notify:');

define('TEXT_REAL_TIME_STAT', '<span class="fieldRequired">*</span>');
define('TEXT_REAL_TIME_STAT_DESC', '<span class="fieldRequired">*</span> denotes real time data');
define('TEXT_OPTION_NOT_APPLICABLE', 'n/a');
define('TEXT_PRODUCTS_ID', 'Product ID:');
define('TEXT_TOTAL_AMOUNT', 'Total: %s');
define('TEXT_BALANCE_AMOUNT', 'Balance: %s');
define('TEXT_PAYMENT_RECORDS', '%s Payment #%s: %s');
define('TEXT_PARTIAL_PAYMENT', 'Partial');
define('TEXT_FULL_PAYMENT', 'Full');
define('TEXT_SET_AS_REMARK', 'Set as Remark');
define('TEXT_SHOW_PRODUCT_NAME', 'Show Product Name');
define('TEXT_HIDE_PRODUCT_NAME', 'Hide Product Name');

define('SELECT_OPTION_UPDATE_COMMENT', 'Update Comment');

define('LINK_BACK_ORDER', 'Show');

define('TABLE_HEADING_PRODUCT_NAME', 'Product Name');
define('TABLE_HEADING_BACK_ORDER', 'Back Orders');
define('TABLE_HEADING_NORMAL_ORDER', 'Current Orders');
define('TABLE_HEADING_PRODUCT_LOCATION', 'Location');
define('TABLE_HEADING_PRODUCT_ACTUAL_QTY', 'Actual Stock');
define('TABLE_HEADING_PRODUCT_DEMAND_STATUS', '<span title="Server Status">SS</span>');
define('TABLE_HEADING_PRODUCT_ADMIN_COMMENT', '<span title="Restock Character">RSTK CHAR</span>');
define('TABLE_HEADING_PRODUCT_SUPPLIER_COMMENT', 'Comments');
define('TABLE_HEADING_MIN_QTY','Min Qty');
define('TABLE_HEADING_MAX_QTY','Max Qty');
define('TABLE_HEADING_OVER_LIMIT_MAX_QTY','Max Over Limit Quantity');
define('TABLE_HEADING_UNIT_PRICE','Unit Price (USD)');
define('TABLE_HEADING_UNIT_OVER_LIMIT_PRICE','Unit Over Limit Price (USD)');
define('TABLE_HEADING_SUGGESTED_SELLING_QUANTITY', '<span title="First List Selling Quantity">1st List Qty</span>');
define('TABLE_HEADING_SELLING_QUANTITY', '<span title="Confirmation List Selling Quantity">2nd List Qty</span>');
define('TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED', '<span title="Received">RECV*</span>');
define('TABLE_HEADING_PRODUCT_BALANCE', '<span title="Balance">BAL</span>');
define('TABLE_HEADING_PRODUCT_RECEIVE', '<span title="Receive">RECV</span>');
define('TABLE_HEADING_EXTRA_QTY', '<span title="Extra quantity received for this product">EXTRA</span>');
define('TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT', 'Payable Amount');

define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_ORDER_ID', 'Order Number');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_SUPPLIER_NOTIFIED', 'Supplier Notified');
define('TABLE_HEADING_COMMENTS', 'Comments');
define('TABLE_HEADING_CHANGED_BY', 'Changed By');
define('TABLE_HEADING_ACTION', 'Action');

define('TABLE_HEADING_POPUP_ORDER_NO', 'Order Number');
define('TABLE_HEADING_POPUP_PAYABLE_AMOUNT', 'Payable Amount');
define('TABLE_HEADING_POPUP_BAL', 'Balance');
define('TABLE_HEADING_POPUP_PAY', 'Pay Amount');

define('TEXT_PARTIAL_PAID_ORDER', '[partial]');

define('SUCCESS_ORDER_UPDATED', 'Success: Order(%s) has been successfully updated.');
define('ERROR_ORDER_DOES_NOT_EXIST', 'Error: Order does not exists.');
define('WARNING_ORDERS_NOT_UPDATED', 'Warning: Nothing to change. The order(s) was not updated.');
define('WARNING_ORDER_NOT_UPDATED', 'Warning: Nothing to change. The order(%s) was not updated.');
define('WARNING_RESTOCK_NON_PENDING_ORDERS', 'Warning: Restock is not done. %s status is not "%s"!');
define('SUCCESS_EXTRA_QUANTITY_UPDATED', 'Success: Extra %d of product quantities for Product with ID: %d has been successfully updated to stock.');
define('WARNING_TEXT_NO_RESTK_CHAR_ASSIGN', '<span class="redIndicator">No Restock Character</span>');

define('TABLE_HEADING_PRODUCT_PRICE', 'Amount');
define('TABLE_HEADING_PAYABLE_TOTAL', 'Payable Total');

define('TEXT_SAVE', 'Save Comment');

define('TEXT_PRINTABLE_VERSION', 'Printable Version');
define('TEXT_SUPPLIER_ORDER_VERIFIED_STATUS', '[%s]');
define('TEXT_SUPPLIER_ORDER_VERIFIED', 'Verified');
define('TEXT_SUPPLIER_ORDER_NOT_VERIFIED', 'Unverify');

define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_TEXT_SUBJECT_SUPPLIER', implode(' ',array(EMAIL_SUBJECT_PREFIX, "Supplier Order Update #%s (%s)")));
define('EMAIL_TEXT_BODY_SUPPLIER', 'Your order has been updated to the following status.

Supplier Order Summary:
'.EMAIL_SEPARATOR."\n");
define('EMAIL_TEXT_COMMENTS_UPDATE', 'Comment:' . "\n%s\n");

define('EMAIL_TEXT_ORDER_NUMBER', 'Supplier Order Number: %s');
define('EMAIL_TEXT_ORDER_TITLE', 'Supplier Order Title: %s');
define('EMAIL_TEXT_DATE_ORDERED', 'Supplier Order Date: %s');
define('EMAIL_TEXT_PRODUCTS', "\n\n".'Products');
define('EMAIL_TEXT_BUYBACK_TOTAL', 'Total: ');
define('EMAIL_RECV_PRODUCT', 'Products received from this order:' . "\n" . EMAIL_SEPARATOR . "\n" . "%s");

define('EMAIL_TEXT_EXTRA', 'Extra Information:');
define('EMAIL_TEXT_CLOSING', 'Thank you for supporting '.STORE_NAME.'.');
define('EMAIL_TEXT_STATUS_UPDATE', "Status: %s");
?>