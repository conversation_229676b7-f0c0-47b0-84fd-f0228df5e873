<?
/*
  	$Id: suppliers_payment.php,v 1.5 2006/08/08 04:37:34 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Suppliers Payments');
define('HEADING_EDIT_PAYMENT_TITLE', 'Supplier Payment');
define('HEADING_INPUT_TITLE', 'Suppliers Payments Criteria');
define('HEADING_TITLE_SEARCH', 'Order ID:');

define('TABLE_HEADING_SUPPLIERS_INFO', 'Supplier Information');
define('TABLE_HEADING_PAYMENT_INFO', 'Payment Information');

define('TABLE_HEADING_TRANS_NO', 'Payment ID');
define('TABLE_HEADING_PAYMENT_NO', 'Payment ID');
define('TABLE_HEADING_TRANS_DATE', 'Transaction Date');
define('TABLE_HEADING_PAYMENT_DATE', 'Payment Date');
define('TABLE_HEADING_REMARK', 'Remarks');
define('TABLE_HEADING_PAYEE', 'Name');
define('TABLE_HEADING_PAYEE_EMAIL', 'Email');
define('TABLE_HEADING_TRANS_AMOUNT', 'Payment Amount');

define('TABLE_HEADING_ORDER_NO', 'Order No.');
define('TABLE_HEADING_LIST_NAME', 'Order List Title');
define('TABLE_HEADING_ORDER_DATE', 'Date');
define('TABLE_HEADING_ORDER_AMOUNT', 'Amount');
define('TABLE_HEADING_PAID_AMOUNT', 'Paid Amount');

define('TABLE_HEADING_DATE_ADDED', 'Date Added');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_SUPPLIER_NOTIFIED', 'Supplier Notified');
define('TABLE_HEADING_COMMENTS', 'Payment Remarks');
define('TABLE_HEADING_CHANGED_BY', 'Changed By');

define('ENTRY_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small>');
define('ENTRY_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD<br>/YYYY-MM-DD HH:MM)</small>');
define('ENTRY_SUPPLIER_GROUP', 'Supplier Group');
define('ENTRY_SUPPLIER', 'Supplier');
define('ENTRY_PAYMENT_ID', 'Payment ID');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_VIEW_MODE', 'View Mode');
define('ENTRY_SORT', 'Sort by');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');

define('ENTRY_EDITED_PAYMENT_ID', 'Payment ID:');
define('ENTRY_PAYMENT_STATUS', 'Payment Status:');
define('ENTRY_PAYMENT_DATE_TIME', 'Payment Date:');
define('ENTRY_DATE_LAST_MODIFIED', 'Last Modified On:');

define('ENTRY_SUPPLIER_PAYMENT_PAYPAL','PayPal Account E-mail:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_NAME','Bank Name:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS','Bank Address:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER','Bank Phone Number:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME','Beneficiary Name:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER','Beneficiary Account Number:');
define('ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE','Bank SWIFT Code ');
define('ENTRY_PAYMENT_AMOUNT', 'Payment Amount:');

define('ENTRY_ADMIN_COMMENT', 'Payment Remark:');
define('ENTRY_NOTIFY_SUPPLIER', 'Show to Supplier:');

define('SELECT_OPTION_UPDATE_COMMENT', 'Update Comment');

define('TEXT_MODE_P', 'Payment History');
define('TEXT_MODE_T', 'Transaction History');
define('TEXT_PARTIAL_PAID_ORDER', '[partial]');
define('TEXT_ASC', 'Ascending');
define('TEXT_DESC', 'Descending');

define('TEXT_REAL_TIME_STAT', '<span class="fieldRequired">*</span>');
define('TEXT_REAL_TIME_STAT_DESC', '<span class="fieldRequired">*</span> denotes real time data');

define('EMAIL_SEPARATOR', '------------------------------------------------------');
define('EMAIL_PAYMENT_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Supplier Payment Update #%s")));
define('EMAIL_REVERSE_SUPPLIER_PAYMENT_NOTIFICATION_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Reversing Supplier Payment #%s")));

define('EMAIL_TEXT_PAYMENT_TITLE', 'Your payment has been updated to the following status.');
define('EMAIL_TEXT_PAYMENT_SUMMARY_TITLE', 'Supplier Payment Summary:');
define('EMAIL_TEXT_PAYMENT_NUMBER', 'Payment Number: %s');
define('EMAIL_TEXT_PAYMENT_AMOUNT', 'Amount Paid: %s');
define('EMAIL_TEXT_PAYMENT_DATE', 'Payment Date: %s');
define('EMAIL_TEXT_PAYMENT_COMMENTS', 'Comment:' . "\n%s\n");
define('EMAIL_TEXT_STATUS_UPDATE', "Status: %s");

define('EMAIL_TEXT_REVERSE_SUPPLIER_PAYMENT_INFO', "Date: %s\n\nAction: Reversing Supplier Payment\nRemark: %s\n\nReversed by: %s\n\n\nAdditional Info:\nPayment ID: %s\nSupplier: %s\nAmount: %s\n");

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_FOOTER', STORE_EMAIL_SIGNATURE);

define('ERROR_PAYMENT_DOES_NOT_EXIST', 'Error: Payment does not exists.');
define('ERROR_REVERSE_PAYMENT_DENIED', 'Error: You do not have permission to reverse supplier payment.');
define('ERROR_NOT_REVERSIBLE_PAYMENT_STATUS', 'Error: This payment cannot be reversed.');
define('SUCCESS_REVERSE_PAYMENT', 'Success: The payment #%d has been successfully reversed.');
define('SUCCESS_UPDATE_PAYMENT', 'Success: The payment #%d has been successfully updated.');
define('WARNING_ORDERS_UPDATED_BY_SOMEONE', 'Warning: This payment just update by someone! Therefore no any update from you is done!');
?>