<?
define('HEADING_TITLE', 'Suppliers Pricing');
define('HEADING_TITLE_PREVIEW', '<span class="redIndicator">PREVIEW of %s First List Page</span>');

define('TABLE_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_HEADING_PRODUCT', 'Product Name');
define('TABLE_HEADING_BUYBACK_PRICE', 'Avg Buyback Price');
define('TABLE_HEADING_STATUS', 'Status');
define('TABLE_HEADING_MIN_QTY', 'Minimum Quantity');
define('TABLE_HEADING_MAX_QTY', 'Maximum Qty');
define('TABLE_HEADING_OVERWRITE_MAX_QTY', 'Overwrite Maximum Qty');
define('TABLE_HEADING_UNIT_PRICE', 'Unit Price');
define('TABLE_SERVER_FULL', 'Server Full');
define('TABLE_HEADING_DISABLED', 'Disabled');
define('TABLE_HEADING_COMMENT', 'Restock Character');
define('TABLE_HEADING_SHOW_COMMENT', '(Check to show it to suppliers)');

define('TABLE_CSV_HEADING_PRODUCT_ID', 'Product ID');
define('TABLE_CSV_HEADING_STATUS', 'Status');
define('TABLE_CSV_HEADING_OVERWRITE_MAX_QTY', 'Overwrite Maximum Qty');
define('TABLE_CSV_HEADING_UNIT_PRICE', 'Unit Price');
define('TABLE_CSV_HEADING_COMMENT', 'Restock Character');

define('TEXT_SELECT_SUPPLIER_GROUP', 'Select Supplier Group');
define('TEXT_SELECT_PURCHASE_LIST', 'Select Purchase List');
define('TEXT_SELECT_RSTK_CHAR_SET', 'Select Restock Character Set');
define('TEXT_COPY_FROM_GROUP', 'Copy quantity and price from');
define('TEXT_APPLY_TO_SELECTED', 'Apply to Selected');
define('TEXT_ACTIVE_SUPPLIERS', 'Active Suppliers in this Group: %d');
define('TEXT_SHOW_BATCH_FILL', 'Show Batch Fill');
define('TEXT_HIDE_BATCH_FILL', 'Hide Batch Fill');
define('TEXT_SHOW_DISABLED', 'Show Disabled');
define('TEXT_HIDE_DISABLED', 'Hide Disabled');
define('TEXT_SHOW_PRODUCT_NAME', 'Show Product Name');
define('TEXT_HIDE_PRODUCT_NAME', 'Hide Product Name');

define('TEXT_CLEAR', 'Clear');
define('TEXT_NOT_EXISTS', 'NOT EXISTS');

define('ENTRY_RSTK_CHAR_SET', 'Restock Character Set');
define('ENTRY_MAX_LIMIT', 'Maximum Limit');
define('ENTRY_OVER_LIMIT_DISCOUNT', 'Over Limit Discount');
define('ENTRY_QUANTITY_RATIO', 'Purchase Quantity Ratio');
define('ENTRY_MIN_QTY', 'Minimum Quantity');
define('ENTRY_DEDUCT_FROM_PREVIOUS_LIST', 'Deduct from the latest list within 24 hours');
define('ENTRY_USE_MFC_SUPPLY', 'Use manufacturer supply(MS)');
define('ENTRY_MS_MAX_QTY', 'MS Maximum Quantity');

define('LINK_REMOVE_LIST', 'Remove %s from %s');

define('KEY_SPS_MAX_LIMIT', "sps_max_limit");
define('KEY_SPS_OVER_LIMIT_DISCOUNT', "sps_over_limit_discount");
define('KEY_SPS_QUANTITY_RATIO', "sps_quantity_ratio");
define('KEY_SPS_MIN_QTY', "sps_min_quantity");
define('KEY_SPS_DEDUCT_PREVIOUS_LIST', "sps_deduct_previous_list");
define('KEY_SPS_USE_MFC_SUPPLY', "sps_use_mfc_supply");
define('KEY_SPS_MFC_SUPPLY_MAX_QTY', "sps_mfc_supply_max_qty");
define('KEY_SPS_RSTK_CHAR_SET', "rstk_char_set");

define('WARNING_NO_PRODUCTS_TO_UPDATE', 'Warning: There is no any products to be updated.');
define('WARNING_NO_SUCH_SUPPLIER_GROUP', 'Warning: The supplier group does not exists.');
define('SUCCESS_SUPPLIER_PRICING_UPDATE', 'Success: The supplier pricing has been successfully updated.');
define('SUCCESS_COPY_LIST', 'Success: The supplier pricing unit prices has been successfully copied and updated.');
define('SUCCESS_REMOVE_LIST', 'Success: The purchase list has been successfully removed from the supplier group.');
define('ERROR_PRODUCT_DISABLED', 'Error: %s is currently disabled for purchase!');
define('ERROR_INVALID_SERVER_STATUS', 'Error: This status (\'%s\') for %s is not a valid server status!');
define('ERROR_INVALID_OVERWRITE_MAXIMUM_QUANTITY', 'Error: Overwrite Maximum Quantity for %s must be an integer value or leave it blank!');
define('ERROR_INVALID_UNIT_PRICE', 'Error: Unit Price for %s must be a currency value!');
define('ERROR_LIST_NOT_EXISTS', 'Error: Purchase list does not exists.');
define('ERROR_LIST_ACCESS_DENIED', 'Error: You do not have permission to access the purchase list.');

define('JS_CONFIRM_COPY_LIST', 'Are you sure to copy the pricing list from this supplier group?');
define('JS_CONFIRM_REMOVE_LIST', 'Are you sure to remove this purchase list from this supplier group?');

// Preview Section
define('TABLE_PREVIEW_HEADING_PRODUCT','Product Name');
define('TABLE_PREVIEW_HEADING_STATUS', 'Server Status');
define('TABLE_PREVIEW_HEADING_MIN_MAX_QTY','Max Quantity');
define('TABLE_PREVIEW_HEADING_UNIT_PRICE','Unit Price (USD)');
define('TABLE_PREVIEW_HEADING_SELLING_QTY','Selling Quantity');
define('TABLE_PREVIEW_HEADING_AMOUNT','Amount(USD)');
define('TABLE_PREVIEW_HEADING_SUPPLIER_COMMENT', 'Comments');

define('TEXT_PREVIEW_MIN_QTY', 'Minimum Quantity: %d');
define('TEXT_PREVIEW_CURRENTLY_NOT_BUYING', 'Server Full');
?>