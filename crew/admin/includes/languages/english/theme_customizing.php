<?
/*
  	$Id: theme_customizing.php,v 1.1 2006/07/19 10:01:20 weichen Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

define('HEADING_TITLE', 'Theme Customizing');
define('TEXT_INFO_HEADING_NEW_THEME', 'New Theme');
define('TEXT_INFO_HEADING_EDIT_THEME', 'Edit Theme');
define('TEXT_INFO_HEADING_UPLOAD_THEME', 'Uploading Theme Files');

define('TABLE_HEADING_THEME_ID', 'Theme ID');
define('TABLE_HEADING_THEME_TITLE', 'Theme Title');
define('TABLE_HEADING_THEME_LANGUAGE', 'Language');
define('TABLE_HEADING_THEME_DESCRIPTION', 'Theme Description');
define('TABLE_HEADING_THEME_CREATION_DATE', 'Date Created');
define('TABLE_HEADING_THEME_MODIFIED_DATE', 'Last Modified');

define('ENTRY_COPY_FROM_THEME', 'Copy From');

define('ENTRY_THEME_TITLE', 'Theme Title');
define('ENTRY_THEME_LANGUAGE', 'Language');
define('ENTRY_THEME_DESCRIPTION', 'Theme Description');

define ('ENTRY_HOME_DIRECTORY', 'Home Directory');

define('TEXT_STATUS_ACTIVE', 'Active');
define('TEXT_STATUS_INACTIVE', 'Inactive');
define('TEXT_ICON_MANDATORY', 'Mandatory');
define('TEXT_ICON_SET_MANDATORY', 'Set Mandatory');
define('TEXT_ICON_OPTIONAL', 'Optional');

define('TEXT_NO_THEME', 'No theme is defined');

define('LINK_ADD_THEME', 'Add Theme');
?>