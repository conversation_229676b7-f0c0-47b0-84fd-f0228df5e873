<?
define('HEADING_INPUT_TITLE', 'Trade & Mail Log Lists %s');
define('HEADING_CRITERIA', 'Criteria');
//define('HEADING_PROGRESS_REPORT', '');

define('ENTRY_HEADING_LOG_SUBMIT_START_DATE', 'Start Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small>');
define('ENTRY_HEADING_LOG_SUBMIT_END_DATE', 'End Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small>');
define('ENTRY_HEADING_LOG_ORDER_ID', 'Order ID');
define('ENTRY_HEADING_LOG_ACCOUNT_NAME', 'Account Name');
define('ENTRY_HEADING_LOG_SERVER', 'Server');
define('ENTRY_HEADING_LOG_REALM', 'Realm');
define('ENTRY_HEADING_LOG_CHAR_NAME', 'Character Name');
define('ENTRY_HEADING_LOG_FACTION', 'Faction');
define('ENTRY_HEADING_MAIL_TRADE', 'Mail / Trade');
define('ENTRY_HEADING_SEND_RECEIVED', 'Send / Received');
define('ENTRY_HEADING_LOG_PC_NAME', 'PC Name');
define('ENTRY_HEADING_LOG_PIC', 'PIC Email');
define('ENTRY_HEADING_LOG_FROM', 'Log From');
define('ENTRY_HEADING_LOG_GOLD_ITEM', 'Gold / Item');

define('TABLE_HEADING_DATE', 'Date');
define('TABLE_HEADING_ORDER_ID', 'Order ID');
define('TABLE_HEADING_ACC_NAME', 'Account Name');
define('TABLE_HEADING_SERVER', 'Server');
define('TABLE_HEADING_REALM', 'Realm');
define('TABLE_HEADING_FACTION', 'Faction');
define('TABLE_HEADING_CHAR', 'Character Name');
define('TABLE_HEADING_ACTION', 'Actions');
define('TABLE_HEADING_SENDER_RECEIVER', 'Sender / Receiver');
define('TABLE_HEADING_SUBJECT', 'Subject');
define('TABLE_HEADING_MESSAGE', 'Message');
define('TABLE_HEADING_SYSTEM_MSG', 'System Comment');
define('TABLE_HEADING_ITEM_MONEY_SEND_RECEIVED', 'Item/Money (Send/Received)');
define('TABLE_HEADING_PRE_BAL', 'Previous Balance');
define('TABLE_HEADING_CURR_BAL', 'Current Balance');
define('TABLE_HEADING_TYPE', 'Type');
define('TABLE_HEADING_PC_NAME', 'PC Name');
define('TABLE_HEADING_PIC', 'PIC');
define('TABLE_HEADING_USER_ROLE', 'User Role');

define('TEXT_SEND', 'Send');
define('TEXT_RECEIVED', 'Received');
define('TEXT_MAIL', 'Mail');
define('TEXT_TRADE', 'Trade');
define('TEXT_SUPPLIER', 'Supplier');
define('TEXT_ADMIN', 'Admin');
define('TEXT_HORDE', 'Horde');
define('TEXT_ALLIANCE', 'Alliance');
define('TEXT_HUMAN', 'Human');
define('TEXT_DRAWF', 'Drawf');
define('TEXT_NIGHT_ELF', 'Night Elf');
Define('TEXT_DRAENEI', 'Draenei');
define('TEXT_GNOME', 'Gnome');
define('TEXT_ORC', 'Orc');
define('TEXT_UNDEAD', 'Undead');
define('TEXT_TAUREN', 'Tauren');
define('TEXT_TROLL', 'Troll');
define('TEXT_BLOOD_ELF', 'Blood Elf');
define('TEXT_US', 'US');
define('TEXT_EU', 'EU');
define('TEXT_GOLD', 'Gold');
define('TEXT_ITEM', 'Item');

define('TEXT_DISPLAY_NUMBER_OF_LOGS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> logs)');

define('IMAGE_BUTTON_SEARCH', 'Search');
define('IMAGE_BUTTON_RESET', 'Reset');
?>