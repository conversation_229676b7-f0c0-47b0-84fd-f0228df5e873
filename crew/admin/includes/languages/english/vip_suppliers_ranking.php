<?
define('HEADER_FORM_VIP_RANKING', 'VIP Ranking List');
define('HEADER_FORM_VIP_RANKING_NEW_REGION', 'New Ranking Region');
define('HEADER_FORM_VIP_RANKING_EDIT_REGION', 'Edit Ranking Region');
define('HEADER_FORM_VIP_RANKING_NEW_RANK', 'New Ranking');
define('HEADER_FORM_VIP_RANKING_EDIT_RANK', 'Edit Ranking');

define('ENTRY_COUNTRY', 'Country:');
define('ENTRY_OTHERS_COUNTRIES', 'Others Countries');
define('ENTRY_PRICE_PERCENTAGE', 'Price Percentage:');
define('ENTRY_REGION_NAME', 'Region Name:');
define('ENTRY_RANKING_ORDER', 'Rank Level:');
define('ENTRY_SORT_ORDER', 'Sort Order:');
define('ENTRY_SUPPLIER_RANKING_NAME', 'Rank Name:');
define('ENTRY_SUPPLIER_REGION', 'Supplier Region:');
define('ENTRY_SUPPLIER_STATUS', 'Supplier Status:');
define('ENTRY_SUPPLIER_VVIP_STATUS', 'VVIP');
define('ENTRY_VIP_RANKING_POINT', 'Point:');
define('ENTRY_VIP_RANKING_UPGRADE_POINT', '(Upgrade)');
define('ENTRY_VIP_RANKING_DOWNGRADE_POINT', '(Downgrade)');

define('ERROR_NO_RANK_NAME', 'Error: Please Enter Rank Name');
define('ERROR_INVALID_FORM', 'Error: Invalid Form');
define('ERROR_INVALID_UPGRADE_DOWNGRADE_POINT', 'Error: Downgrade Point can\'t higher than Upgrade Point');
define('ERROR_NO_REGION_NAME', 'Error: Please Enter Region Name');
define('ERROR_NO_COUNTRIES_SELECTED', 'Error: Please Select Country');
define('ERROR_NO_SORT_ORDER', 'Error: Please Enter Sort Order');
define('ERROR_NO_RANK_NAME', 'Error: Please Enter Rank Name');
define('ERROR_NO_RANK_LEVEL', 'Error: Please Enter Rank Level');
define('ERROR_NO_PERCENTAGE', 'Error: Please Enter Percentage in numeric');
define('ERROR_NO_UPGRADE_POINT', 'Error: Please Enter Upgrade Point in numeric');
define('ERROR_NO_DOWNGRADE_POINT', 'Error: Please Enter Downgrade Point in numeric');

define('LINK_ADD_REGION', 'Add Region');
define('LINK_ADD_NEW', 'Add New');

define('TABLE_HEADING_ACTION', 'Action');
define('TABLE_HEADING_COUNTRIES', 'Countries');
define('TABLE_HEADING_DOWNGRADE_POINT', 'Downgrade Point');
define('TABLE_HEADING_PRICE_PERCENTAGE', 'Price Percentage(%)');
define('TABLE_HEADING_RANKING', 'Ranking');
define('TABLE_HEADING_RANKING_NAME', 'Rank Name (Priority');
define('TABLE_HEADING_REGION', 'Region');
define('TABLE_HEADING_UPGRADE_POINT', 'Upgrade Point');

define('NOTE_THE_LOWEST_HIGH_PRIORITY', '*The Lowest Number The Highest Priority');
?>