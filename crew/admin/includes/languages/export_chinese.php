<?php
// Alipay Template
define('T_1_CSV_HEADER_BATCH_ID', '批次号');
define('T_1_CSV_HEADER_PAYMENT_DATE', '付款日期');
define('T_1_CSV_HEADER_PAYER_EMAIL', '付款人email');
define('T_1_CSV_HEADER_PAYER_ACCOUNT_NAME', '账户名称');
define('T_1_CSV_HEADER_TOTAL_PAY_AMOUNT', '总金额（元）');
define('T_1_CSV_HEADER_TOTAL_PAYMENT_TRANSACTION', '总笔数');
define('T_1_CSV_HEADER_PAYER_EMAIL_VALUE', '<EMAIL>');
define('T_1_CSV_HEADER_PAYER_ACCOUNT_NAME_VALUE', 'OFFGAMERS LIMITED');
define('T_1_CSV_HEADER_PAYMENT_ID', '商户流水号');
define('T_1_CSV_HEADER_PAYEE_EMAIL', '收款人email');
define('T_1_CSV_HEADER_PAYEE_NAME', '收款人姓名');
define('T_1_CSV_HEADER_PAY_AMOUNT', '付款金额');
define('T_1_CSV_HEADER_PAYMENT_NOTE', '付款理由');

// Kuai Qian Template
define('T_2_CSV_HEADER_BANK_NAME', '银行名称');
define('T_2_CSV_HEADER_PAYEE_NAME', '收款方姓名');
define('T_2_CSV_HEADER_PAYEE_ACCOUNT_NO', '收款方银行卡号');
define('T_2_CSV_HEADER_PAY_AMOUNT', '金额');
define('T_2_CSV_HEADER_PAYMENT_NOTE', '备注');
define('T_2_CSV_HEADER_PAYMENT_GATEWAY_ID', '快钱交易号');
define('T_2_CSV_HEADER_PAYMENT_FEES', '费用');
define('T_2_CSV_HEADER_PAYMENT_STATUS', '状态');
define('T_2_CSV_HEADER_PAYMENT_DATE', '交易时间');
define('T_2_CSV_BODY_PAYEE_BANK', '中国工商银行');
?>