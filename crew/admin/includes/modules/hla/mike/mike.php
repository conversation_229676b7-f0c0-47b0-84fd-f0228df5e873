<?php

class mike 
{
	var $ignored_ref_id_path;
	var $ignored_acc_id_path;
	var $profilelist_path;
	var $html_file_path;
	
	var $default_language_id;
	var $default_currency;
	var $default_product_status;
	var $default_product_display;
	
	var $language_arr;
	var $products_hla_id_arr;
	var $ref_id_arr;
	var $acc_id_arr;
	
	var $profile_type;
	var $selling_price_percentage;
	
    function mike ($seller_id, $product_id, $default_language_id, $default_currency, $default_product_status, $default_product_display, $html_file_path, $profilelist_path, $ignored_ref_id_path, $ignored_acc_id_path, $profile_type = 0) {
		$this->profilelist_path = $profilelist_path;
		$this->ignored_ref_id_path = $ignored_ref_id_path;
		$this->ignored_acc_id_path = $ignored_acc_id_path;
		$this->html_file_path = $html_file_path;
		
		$this->seller_id = $seller_id;
		$this->product_id = $product_id;
		
		$this->default_language_id = $default_language_id;
		$this->default_currency = $default_currency;
		$this->default_product_status = $default_product_status; // -1 = Inactive, 0 = Sold, 1 = Active
		$this->default_product_display = $default_product_display; // 0=hide, 1=show
		
		$this->ref_id_arr = array();
		$this->acc_id_arr = array();
		$this->language_arr = array('en-US' => 1);
		$this->products_hla_id_arr = array();
		$this->selling_price_percentage = 1;
		
		$this->profile_type = $profile_type == 0 ? 'direct_link' : 'download';
    }
    
    function parsing_xml($xml_path) {
    	if ($xml = @simplexml_load_file($xml_path)) {
			if (tep_not_null((string)$this->language_arr[(string)$xml->channel->language])) {
				$language_id = $this->language_arr[(string)$xml->channel->language];
			} else {
				$language_id = $this->default_language_id;
			}
			
			foreach ($xml->channel->item as $item) {
				// single character
				$ref_id = (string)$item->refid;
				$character_name = (string)$item->title;
				$description = strip_tags((string)$item->description);
				
				if (isset($this->ref_id_arr[$ref_id])) {
					write_in_file($this->ignored_ref_id_path, $ref_id);
				} else {
					$this->ref_id_arr[$ref_id] = '';
					
					// Character Attribute
					$attr_arr = store_attributes($item);
					
					$char_info_arr = array( 'product_id' => $this->product_id, 
											'default_currency' => (tep_not_null((string)$item->salescurrency) ? $item->salescurrency : $this->default_currency), 
											'products_status' => $this->default_product_status, 
											'products_display' => $this->default_product_display, 
											'language_id' => $language_id, 
										   	'ref_id' => $ref_id,
										    'character_name' => $character_name, 
										    'description' => $description, 
										    'products_price' => (string)$item->salesprice * $this->selling_price_percentage, 
										    'products_original_price' => (string)$item->salesprice, 
										    'profile' => $this->profile_type,
										    'profile_link' => (string)$item->mywowarmory,
										    'attr_arr' => $attr_arr);
					
					$product_exist_select_sql = "	SELECT chrt.products_hla_id, chrt.products_hla_characters_id, hla.products_status 
													FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS chrt 
													INNER JOIN " . TABLE_PRODUCTS_HLA . " AS hla 
														ON (chrt.products_hla_id = hla.products_hla_id) 
													WHERE chrt.products_ref_id = '".tep_db_input($ref_id)."' 
													AND hla.seller_id = '".tep_db_input($this->seller_id)."'";
											
					$product_exist_result_sql = tep_db_query($product_exist_select_sql);
					if ($product_exist_row = tep_db_fetch_array($product_exist_result_sql)) {
						if ($product_exist_row['products_status'] == '-1') {
							write_in_file($this->ignored_ref_id_path, $ref_id);
						}
						// Character Exist
						$char_info_arr['characters_id'] = $product_exist_row['products_hla_characters_id'];
						$char_info_arr['products_hla_id'] = $product_exist_row['products_hla_id'];
						$this->products_hla_id_arr[] = $product_exist_row['products_hla_id'];
						
						if (database_process('single', 'update', $char_info_arr, $this->products_hla_id_arr)) {
							; // No need insert into profilelist.txt
						}
					} else {
						// Character not exist in database
						if (database_process('single', 'insert', $char_info_arr, $this->products_hla_id_arr)) {
							; // No need insert into profilelist.txt
						}
					}
				}
			}
		}
    }
}
?>