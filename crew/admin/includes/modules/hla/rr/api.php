<?php

class api {

    var $action, $added_by, $api_url, $exception, $product_instance_id, $soap_client, $use_proxy, $accMarkSold_counter, $accMarkSold_max;

    function api($product_instance_id, $added_by = '') {
        $this->action = '';
        $this->added_by = $added_by;
        $this->use_proxy = false;
        $this->accMarkSold_counter = 0;
        $this->accMarkSold_max = 2;

        $this->product_instance_id = $product_instance_id;
        $this->api_url = 'https://dv.randyrun.com/wbsrvc/server_og.php';
    }

    function soap_connection() {
        if ($this->use_proxy) {
            $this->soap_client = new SoapClient(null, array('location' => $this->api_url,
                'uri' => $this->api_url,
                'trace' => 1,
                'exceptions' => 1,
                'httpAuthType' => 'Basic',
                array('proxy_user' => 'devbackend',
                    'proxy_pass' => 'devonly'
                )
                    )
            );
        } else {
            $this->soap_client = new SoapClient(null, array('location' => $this->api_url,
                'uri' => $this->api_url,
                'trace' => 1,
                'exceptions' => 1,
                'httpAuthType' => 'Basic'
                    )
            );
        }
    }

    function get_reserve_account_api_status() {
        $reserve_account_api_status = false;

        $products_supplier_select_sql = "	SELECT ps.reserve_account_api
											FROM " . TABLE_PRODUCTS_SUPPLIER . " AS ps 
											INNER JOIN " . TABLE_PRODUCTS_HLA . " AS ph 
												ON ps.supplier_id = ph.seller_id 
											WHERE ph.products_hla_id = '" . (int) $this->product_instance_id . "'";
        $products_supplier_result_sql = tep_db_query($products_supplier_select_sql);
        if ($products_supplier_row = tep_db_fetch_array($products_supplier_result_sql)) {
            if (tep_not_null($products_supplier_row['reserve_account_api'])) {
                $reserve_account_api_status = $products_supplier_row['reserve_account_api'];
            }
        }

        return $reserve_account_api_status;
    }

    function get_retrieve_account_info_api_status() {
        $retrieve_account_info_api_status = false;

        $products_supplier_select_sql = "	SELECT ps.retrieve_account_info_api
											FROM " . TABLE_PRODUCTS_SUPPLIER . " AS ps 
											INNER JOIN " . TABLE_PRODUCTS_HLA . " AS ph 
												ON ps.supplier_id = ph.seller_id 
											WHERE ph.products_hla_id = '" . (int) $this->product_instance_id . "'";
        $products_supplier_result_sql = tep_db_query($products_supplier_select_sql);
        if ($products_supplier_row = tep_db_fetch_array($products_supplier_result_sql)) {
            if (tep_not_null($products_supplier_row['retrieve_account_info_api'])) {
                $retrieve_account_info_api_status = $products_supplier_row['retrieve_account_info_api'];
            }
        }

        return $retrieve_account_info_api_status;
    }

    function get_api_key() {
        $api_key = '';

        $products_supplier_select_sql = "	SELECT ps.api_key
											FROM " . TABLE_PRODUCTS_SUPPLIER . " AS ps 
											INNER JOIN " . TABLE_PRODUCTS_HLA . " AS ph 
												ON ps.supplier_id = ph.seller_id 
											WHERE ph.products_hla_id = '" . (int) $this->product_instance_id . "'";
        $products_supplier_result_sql = tep_db_query($products_supplier_select_sql);
        if ($products_supplier_row = tep_db_fetch_array($products_supplier_result_sql)) {
            if (tep_not_null($products_supplier_row['api_key'])) {
                $api_key = $products_supplier_row['api_key'];
            }
        }

        return $api_key;
    }

    function get_products_reservation_id() {
        $reservationID = '';

        $products_hla_select_sql = "SELECT products_hla_reserve_id FROM " . TABLE_PRODUCTS_HLA . "
									WHERE products_hla_id = '" . (int) $this->product_instance_id . "'";
        $products_hla_result_sql = tep_db_query($products_hla_select_sql);
        if ($products_hla_row = tep_db_fetch_array($products_hla_result_sql)) {
            $reservationID = $products_hla_row['products_hla_reserve_id'];
        }

        return $reservationID;
    }

    function get_products_ref_id() {
        $accountID = '';

        $products_hla_select_sql = "SELECT products_ref_id FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . "
									WHERE products_hla_id = '" . (int) $this->product_instance_id . "'";
        $products_hla_result_sql = tep_db_query($products_hla_select_sql);
        if ($products_hla_row = tep_db_fetch_array($products_hla_result_sql)) {
            $accountID = $products_hla_row['products_ref_id'];
        }

        return $accountID;
    }

    function get_products_price() {
        $products_price = array();

        $products_hla_select_sql = "SELECT products_original_price, products_base_currency FROM " . TABLE_PRODUCTS_HLA . "
									WHERE products_hla_id = '" . (int) $this->product_instance_id . "'";
        $products_hla_result_sql = tep_db_query($products_hla_select_sql);
        if ($products_hla_row = tep_db_fetch_array($products_hla_result_sql)) {
            $products_price['products_original_price'] = $products_hla_row['products_original_price'];
            $products_price['products_base_currency'] = $products_hla_row['products_base_currency'];
        }

        return $products_price;
    }

    function remove_products_reservation_id() {
        $products_hla_select_sql = "UPDATE " . TABLE_PRODUCTS_HLA . " SET products_hla_reserve_id = ''
									WHERE products_hla_id = '" . (int) $this->product_instance_id . "'";
        $products_hla_result_sql = tep_db_query($products_hla_select_sql);
    }

    function account_reservation() {
        $status = 0;
        $message = '';

        $accReserve = '';
        $accReserve = $this->accReserve();

        if (isset($accReserve->errorID)) {
            if (($accReserve->errorID == 0) && !empty($accReserve->response->reservationID)) { // success
                $this->account_reservation_success($accReserve->response->reservationID);
                $status = 1;
                $message = 'Account reserved successfully';
            } else { // fail
                $status = 0;
                $message = '[errorID:' . $accReserve->errorID . '] ' . $accReserve->errorMsg;
            }
        } else if (isset($this->exception)) {
            $status = 0;
            $message = 'SOAP exception : [' . $this->exception->faultcode . '] ' . $this->exception->faultstring;
        }

        $this->orders_products_hla_log($status, $message);
    }

    function account_reservation_success($reservationID) {
        $products_hla_sql_data = array();

        $products_hla_sql_data = array('products_hla_reserve_id' => tep_db_prepare_input($reservationID));
        tep_db_perform(TABLE_PRODUCTS_HLA, $products_hla_sql_data, 'update', 'products_hla_id = "' . (int) $this->product_instance_id . '"');
    }

    function accReserve($minutesReserved = 1440) {
        if ($this->get_reserve_account_api_status()) {
            $this->action = 'accReserve';
            $this->exception = '';

            if (!isset($this->soap_client)) {
                $this->soap_connection();
            }

            $api_data = array();
            $accReserve = '';

            $api_key = (string) $this->get_api_key();
            $reservationID = (string) $this->get_products_reservation_id();

            if (!tep_not_null($reservationID) && tep_not_null($api_key)) {
                $api_data['apiKey'] = $api_key;
                $api_data['accountID'] = $this->get_products_ref_id();
                $api_data['minutesReserved'] = $minutesReserved;

                try {
                    $accReserve = $this->soap_client->accReserve($api_data);
                    return $accReserve;
                } catch (SoapFault $exception) {
                    $this->exception = $exception;
                }
            }
        }
    }

    function account_retrieve_info($buyback_request_group_id, $seller_id) {
        if ($this->accMarkSold_max > $this->accMarkSold_counter) {
            $status = 0;
            $message = '';
            $accMarkSold = '';
            $hla_product_info = array();

            $accMarkSold = $this->accMarkSold($buyback_request_group_id);
            if (isset($accMarkSold->errorID)) {
                if (($accMarkSold->errorID == 0) && !empty($accMarkSold->response->accountData)) { // success
                    $status = 1;
                    $message = 'Account Marked as Sold Successfully';

                    // Storing HLA Info
                    $battle_email = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->accountnameBnet));
                    $battle_password = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->passwordBnet));
                    $account_id = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->accountname));
                    $password = '';
                    $first_name = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->firstName));
                    $account_country = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->countryCodeISO2));
                    $account_city = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->city));
                    $account_state = '';
                    $account_zip = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->zip));
                    $account_street = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->street));
                    $phone_number = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->accountnameBnet));
                    $last_name = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->lastName));
                    $question = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->securityQuestionBnet));
                    $answer = tep_db_prepare_input(htmlspecialchars($accMarkSold->response->accountData->securityAnswerBnet));

                    $emailpassword = $accMarkSold->response->accountData->emailPassword;
                    $cdkey = $accMarkSold->response->accountData->CDKey;
                    $cdkeyburningcrusade = $accMarkSold->response->accountData->CDKeyBurningCrusade;
                    $cdkeywotlk = $accMarkSold->response->accountData->CDKeyWotLK;

                    $extra_comment = "Email Password: " . $emailpassword . "\r\n";
                    $extra_comment .= 'CD Key: ' . $cdkey . "\r\n";
                    $extra_comment .= 'CD Key Burning Crusade: ' . $cdkeyburningcrusade . "\r\n";
                    $extra_comment .= 'CD Key WotLK: ' . $cdkeywotlk;

                    $extra_comment = tep_db_prepare_input(htmlspecialchars($extra_comment));

                    $get_op_id_sql = "	SELECT br.orders_id, br.orders_products_id, br.buyback_amount, br.products_id, bs.buyback_status_name
										FROM " . TABLE_BUYBACK_REQUEST . " AS br 
										INNER JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
											ON (br.buyback_request_group_id = brg.buyback_request_group_id) 
										LEFT JOIN " . TABLE_BUYBACK_STATUS . " AS bs 
											ON (brg.buyback_status_id = bs.buyback_status_id)
										WHERE brg.buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "'
											AND brg.customers_id = '" . (int) $seller_id . "'
											AND bs.language_id = '1'";
                    $get_op_id_result = tep_db_query($get_op_id_sql);

                    if ($get_op_id_row = tep_db_fetch_array($get_op_id_result)) {
                        $orders_id = $get_op_id_row['orders_id'];
                        $orders_products_id = $get_op_id_row['orders_products_id'];
                        $buyback_amount = $get_op_id_row['buyback_amount'];
                        $products_id = $get_op_id_row['products_id'];
                        $buyback_status_name = $get_op_id_row['buyback_status_name'];
                        $history_content = '';
                        $products_templates_id = 0;
                        $email_supplier_hla_info = $email_product_path = '';
                        $customer_email = tep_get_customers_email($seller_id);
                        $old_data_info = $new_data_info = array();
                        $first_time_submit = false;

                        $edit_order_obj = new edit_order($_SESSION['login_id'], $this->added_by, $orders_id);
                        $products_cat_path_sql = "	SELECT p.products_cat_path, pd.products_name
													FROM " . TABLE_PRODUCTS . " AS p 
													INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
													ON (p.products_id = pd.products_id) 
													WHERE pd.products_id = '" . tep_db_input($products_id) . "' 
													AND language_id = '1'";
                        $products_cat_path_result = tep_db_query($products_cat_path_sql);
                        if ($products_cat_path_row = tep_db_fetch_array($products_cat_path_result)) {
                            $order_products_item_select_sql = "	SELECT orders_products_item_info
																FROM " . TABLE_ORDERS_PRODUCTS_ITEM . " 
																WHERE orders_products_id = '" . $orders_products_id . "' 
																ORDER BY products_hla_characters_id LIMIT 1";
                            $order_products_item_result_sql = tep_db_query($order_products_item_select_sql);
                            if ($order_products_item_row = tep_db_fetch_array($order_products_item_result_sql)) {
                                $orders_products_item_info = tep_array_unserialize($order_products_item_row['orders_products_item_info']);
                                $ref_id = $orders_products_item_info['products_ref_id'];

                                // Insert log in edit order page
                                $history_content = TEXT_BUYBACK_ORDER_ITEMS_DELIVERED_REMARK . "\n";
                                $history_content .= "&raquo; " . $products_cat_path_row["products_name"] . ' > REF#' . $ref_id . "\tx 1";
                                $email_product_path = $products_cat_path_row["products_cat_path"] . ' > ' . $products_cat_path_row["products_name"] . ' > REF#' . $ref_id;
                            }
                        }

                        $hla_info_stage_1_arr = tep_draw_products_extra_info($orders_products_id, 'hla_info_stage_1');

                        if (!tep_not_null($hla_info_stage_1_arr)) {
                            $first_time_submit = true;
                        }

                        if (!$first_time_submit) {
                            $hla_info_stage_1 = tep_array_unserialize($hla_info_stage_1_arr['hla_info_stage_1']);
                            foreach ($hla_info_stage_1 as $stage_1_info) {
                                foreach ($stage_1_info['info'] as $stage_1_key => $stage_1_value) {
                                    $old_data_info[$stage_1_key] = $stage_1_value;
                                }
                            }

                            $hla_info_stage_2_arr = tep_draw_products_extra_info($orders_products_id, 'hla_info_stage_2');

                            $hla_info_stage_2 = tep_array_unserialize($hla_info_stage_2_arr['hla_info_stage_2']);
                            foreach ($hla_info_stage_2 as $stage_2_key => $stage_2_value) {
                                $old_data_info[$stage_2_key] = $stage_2_value;
                            }

                            $new_data_info['battle_email'] = $battle_email;
                            $new_data_info['battle_password'] = $battle_password;
                            $new_data_info['account_id'] = $account_id;
                            $new_data_info['password'] = $password;
                            $new_data_info['first_name'] = $first_name;
                            $new_data_info['last_name'] = $last_name;
                            $new_data_info['extra_comment'] = $extra_comment;
                            $new_data_info['account_country'] = $account_country;
                            $new_data_info['account_city'] = $account_city;
                            $new_data_info['account_state'] = $account_state;
                            $new_data_info['account_zip'] = $account_zip;
                            $new_data_info['account_street'] = $account_street;
                            $new_data_info['phone_number'] = $phone_number;
                            $new_data_info['secret_question'] = $question;
                            $new_data_info['secret_answer'] = $answer;

                            $diff_data_info = array_diff($new_data_info, $old_data_info);
                            if (count($diff_data_info) > 0) {
                                $changes_str = 'Changes made on type info:<br />';
                                foreach ($diff_data_info as $diff_key => $diff_value) {
                                    $changes_str .= $diff_key . '<br />';
                                }
                            }

                            if (tep_not_null($changes_str)) {
                                // Insert log to CO
                                tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $orders_id . "', now(), '0', '" . tep_db_input('##BO##' . $buyback_request_group_id . '## - Supplier Update\'s Info:<br />' . $changes_str) . "', 2, '" . $this->added_by . "')");

                                // Insert log to BO
                                $buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
                                    'buyback_status_id' => 0,
                                    'date_added' => 'now()',
                                    'customer_notified' => 0,
                                    'comments' => $changes_str,
                                    'set_as_buyback_remarks' => 0,
                                    'changed_by' => $this->added_by
                                );
                                tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
                            }
                        }

                        $hla_product_info['hla_info_stage_1'][] = array('title' => 'BATTLENET INFORMATION',
                            'info' => array('battle_email' => $battle_email,
                                'battle_password' => $battle_password));

                        $hla_product_info['hla_info_stage_1'][] = array('title' => 'WORLD OF WARCRAFT INFORMATION',
                            'info' => array('account_id' => $account_id,
                                'password' => $password));

                        $hla_product_info['hla_info_stage_1'][] = array('title' => 'REGISTRATION INFORMATION',
                            'info' => array('first_name' => $first_name,
                                'last_name' => $last_name,
                                'account_country' => $account_country,
                                'account_city' => $account_city,
                                'account_state' => $account_state,
                                'account_zip' => $account_zip,
                                'account_street' => $account_street,
                                'phone_number' => $phone_number));

                        $hla_product_info['hla_info_stage_1'][] = array('title' => 'EXTRA COMMENT',
                            'info' => array('extra_comment' => $extra_comment));

                        $hla_product_info['hla_info_stage_2'][] = array('title' => 'SECRET QUESTION & ANSWER',
                            'info' => array('secret_question' => $question,
                                'secret_answer' => $answer));

                        $email_supplier_hla_info = '<b>BATTLENET INFORMATION</b>' . "\n";
                        $email_supplier_hla_info .= 'Battlenet Email :' . $battle_email . "\n";
                        $email_supplier_hla_info .= 'Battlenet Password :' . $battle_password . "\n\n";
                        $email_supplier_hla_info .= '<b>WORLD OF WARCRAFT INFORMATION</b>' . "\n";
                        $email_supplier_hla_info .= 'Account ID :' . $account_id . "\n";
                        $email_supplier_hla_info .= 'Password :' . $password . "\n\n";
                        $email_supplier_hla_info .= '<b>REGISTRATION INFORMATION</b>' . "\n";
                        $email_supplier_hla_info .= 'First Name :' . $first_name . "\n";
                        $email_supplier_hla_info .= 'Last Name :' . $last_name . "\n";
                        $email_supplier_hla_info .= 'Account Country :' . $account_country . "\n";
                        $email_supplier_hla_info .= 'Account City :' . $account_city . "\n";
                        $email_supplier_hla_info .= 'Account State :' . $account_state . "\n";
                        $email_supplier_hla_info .= 'Account Zip Code :' . $account_zip . "\n";
                        $email_supplier_hla_info .= 'Account Street :' . $account_street . "\n";
                        $email_supplier_hla_info .= 'Phone Number :' . $phone_number . "\n\n";
                        $email_supplier_hla_info .= '<b>EXTRA COMMENT</b>' . "\n";
                        $email_supplier_hla_info .= 'Extra Comment :' . $extra_comment . "\n\n";
                        $email_supplier_hla_info .= '<b>SECRET QUESTION</b>' . "\n";
                        $email_supplier_hla_info .= 'Secret Question :' . $question . "\n";
                        $email_supplier_hla_info .= 'Answer :' . $answer . "\n";

                        foreach ($hla_product_info as $hla_product_info_key => $hla_product_info_value) {
                            $serialize_hla_info = tep_array_serialize($hla_product_info[$hla_product_info_key]);
                            $hla_product_info_sql = "	INSERT INTO " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . "
														SET orders_products_id = '" . tep_db_input($orders_products_id) . "',
														orders_products_extra_info_key = '" . tep_db_input($hla_product_info_key) . "',
														orders_products_extra_info_value = '" . tep_db_input($serialize_hla_info) . "'
															ON DUPLICATE KEY 
														UPDATE orders_products_extra_info_value = '" . tep_db_input($serialize_hla_info) . "'";
                            tep_db_query($hla_product_info_sql);
                        }

                        $insert_orders_products_history = array('buyback_request_group_id' => $buyback_request_group_id,
                            'orders_id' => $orders_id,
                            'orders_products_id' => $orders_products_id,
                            'date_added' => 'now()',
                            'last_updated' => 'now()',
                            'date_confirm_delivered' => date("Y-m-d H:i:s", mktime(date("H") + 72, date("i"), date("s"), date("m"), date("d"), date("Y"))),
                            'delivered_amount' => '1',
                            'changed_by' => tep_get_customers_email($seller_id)
                        );

                        $check_receive_sql = "	SELECT received FROM " . TABLE_ORDERS_PRODUCTS_HISTORY . "
												WHERE buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "'
												ORDER BY date_added DESC LIMIT 1";
                        $check_receive_result = tep_db_query($check_receive_sql);
                        if ($check_receive_row = tep_db_fetch_array($check_receive_result)) {
                            if ($check_receive_row['received'] == '0') {
                                tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_orders_products_history);
                            }
                        }

                        if ($first_time_submit) {
                            tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_orders_products_history);

                            // If first time submit
                            $products_hla_info = tep_draw_products_extra_info($orders_products_id, 'products_hla');
                            if (count($products_hla_info) > 0) {
                                $products_hla_info = tep_array_unserialize($products_hla_info['products_hla']);
                                if (tep_not_null($products_hla_info['products_hla_id'])) {
                                    // Qty Received
                                    $update_confirmed_qty_array = array('buyback_quantity_confirmed' => '1',
                                        'buyback_quantity_received' => '1');
                                    tep_db_perform(TABLE_BUYBACK_REQUEST, $update_confirmed_qty_array, 'update', "buyback_request_group_id='$buyback_request_group_id'");

                                    // Update Buyback Order to PROCESSING
                                    $update_request_group_status_array = array('buyback_status_id' => '2',
                                        'show_restock' => '0',
                                        'buyback_request_group_last_modified' => 'now()');
                                    tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $update_request_group_status_array, 'update', "buyback_request_group_id='$buyback_request_group_id'");

                                    // Insert buyback history comment
                                    $buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
                                        'buyback_status_id' => '2',
                                        'date_added' => 'now()',
                                        'customer_notified' => '0',
                                        'comments' => 'Supplier submit info'
                                    );
                                    tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);

                                    // Insert order history comment
                                    if (tep_not_null($history_content)) {
                                        tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int) $orders_id . "', now(), '1', '" . tep_db_input($history_content) . "', 2, '" . $this->added_by . "')");
                                    }

                                    // Update delivered qty
                                    $delived_qty_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . "
																SET products_delivered_quantity = products_delivered_quantity + 1,
																	products_good_delivered_quantity = products_good_delivered_quantity + 1,
																	products_good_delivered_price = '" . tep_db_input($buyback_amount) . "'
																WHERE orders_id='" . tep_db_input($orders_id) . "' 
																	AND products_id = '" . tep_db_input($products_id) . "' 
																	AND orders_products_id = '" . tep_db_input($orders_products_id) . "'";
                                    tep_db_query($delived_qty_update_sql);
                                    // Insert sales activity
                                    $edit_order_obj->update_delivered_price($orders_products_id);

                                    $final_price_select_sql = '	SELECT final_price
																FROM ' . TABLE_ORDERS_PRODUCTS . '
																WHERE orders_products_id = "' . $orders_products_id . '"';
                                    $final_price_result_sql = tep_db_query($final_price_select_sql);
                                    $final_price_row = tep_db_fetch_array($final_price_result_sql);

                                    $edit_order_obj->insert_sales_activity($orders_products_id, $products_id, 'D', $final_price_row['final_price'], 1, 1);

                                    // Update HLA product status to SOLD
                                    $update_hla_status_sql = "	UPDATE " . TABLE_PRODUCTS_HLA . "
																SET products_status = 0 
																WHERE products_hla_id 	='" . tep_db_input($products_hla_info['products_hla_id']) . "'";
                                    tep_db_query($update_hla_status_sql);

                                    // Email to notify customer
                                    $get_order_status_sql = "	SELECT o.customers_id, o.date_purchased, os.orders_status_name
																FROM " . TABLE_ORDERS . " AS o 
																INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
																ON (o.orders_status = os.orders_status_id) 
				 												WHERE o.orders_id = '" . tep_db_input($orders_id) . "' 
				 												AND os.language_id = '1'";
                                    $get_order_status_result = tep_db_query($get_order_status_sql);
                                    if ($get_order_status_row = tep_db_fetch_array($get_order_status_result)) {
                                        $orders_status_name = $get_order_status_row['orders_status_name'];
                                        $customers_id = $get_order_status_row['customers_id'];
                                        $date_purchased = $get_order_status_row['date_purchased'];

                                        $customer_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, c.customers_gender, ci.customer_info_selected_language_id
															FROM " . TABLE_CUSTOMERS . " AS c 
															LEFT JOIN " . TABLE_CUSTOMERS_INFO . " ci 
																ON (c.customers_id = ci.customers_info_id)
															WHERE c.customers_id = '" . tep_db_input($customers_id) . "'";
                                        $customer_result = tep_db_query($customer_sql);
                                        if ($customer_row = tep_db_fetch_array($customer_result)) {
                                            $customer_name = $customer_row['customers_firstname'] . " " . $customer_row['customers_lastname'];
                                            if (eregi_dep('^[a-zA-Z0-9._-]+@[a-zA-Z0-9-]+\.[a-zA-Z.]{2,5}$', $customer_row['customers_email_address'])) {
                                                include_once(DIR_WS_LANGUAGES . 'email_customers.php');

                                                $email_text_order_number = EN_EMAIL_TEXT_ORDER_NUMBER;
                                                $email_text_date_ordered = EN_EMAIL_TEXT_DATE_ORDERED;
                                                $email_text_invoice_url = EN_EMAIL_TEXT_INVOICE_URL;
                                                $email_text_updated_status = EN_EMAIL_TEXT_UPDATED_STATUS;
                                                $email_text_subject = EN_EMAIL_TEXT_SUBJECT;

                                                //$orders_status_array = $this->get_customer_order_status();
                                                $email_greeting = tep_get_email_greeting_english($customer_row['customers_firstname'], $customer_row['customers_lastname'], $customer_row['customers_gender']);

                                                $email = $email_greeting . $email_text_order_number . ' ' . (int) $orders_id . "\n"
                                                        . $email_text_date_ordered . ' ' . tep_date_long($date_purchased) . "\n" . $email_text_invoice_url . ' ' . tep_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $orders_id, 'SSL') . "\n\n";

                                                $email .= sprintf($email_text_updated_status, $orders_status_name) .
                                                        str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $history_content) . "\n\n";

                                                $email .= EMAIL_HLA_CUSTOMER_GUIDE . "\n\n" . EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER;

                                                tep_mail($customer_name, $customer_row['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf($email_text_subject, (int) $orders_id))), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS, 'iso-8859-1');
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        $customer_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_gender
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_id = '" . tep_db_input($seller_id) . "'";
                        $customer_result = tep_db_query($customer_sql);
                        if ($customer_row = tep_db_fetch_array($customer_result)) {
                            $customer_name = $customer_row['customers_firstname'] . " " . $customer_row['customers_lastname'];
                            $customer_greeting_name = tep_get_email_greeting($customer_row['customers_firstname'], $customer_row['customers_lastname'], $customer_row['customers_gender']);

                            if (eregi_dep('^[a-zA-Z0-9._-]+@[a-zA-Z0-9-]+\.[a-zA-Z.]{2,5}$', $customer_row['customers_email_address'])) {
                                include_once(DIR_WS_CLASSES . 'currencies.php');

                                $currencies = new currencies();
                                $buyback_product_list = $email_product_path . ' = ' . $currencies->format((double) $buyback_amount, true, DEFAULT_CURRENCY, '', 'buy') . "\n";

                                $email_content = $customer_greeting_name .
                                        EMAIL_HLA_NEW_BUYBACK_BODY .
                                        sprintf(EMAIL_HLA_NEW_BUYBACK_ORDER_NUMBER, $buyback_request_group_id) . "\n" .
                                        sprintf(EMAIL_HLA_NEW_BUYBACK_DATE_ORDERED, tep_date_long(date("Y-m-d") . " 00:00:00")) . "\n" .
                                        sprintf(EMAIL_HLA_NEW_BUYBACK_CUSTOMER_EMAIL, $customer_row['customers_email_address']) . "\n\n" .
                                        EMAIL_HLA_NEW_BUYBACK_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . $buyback_product_list .
                                        EMAIL_SEPARATOR . "\n" .
                                        sprintf(EMAIL_HLA_NEW_BUYBACK_ORDER_TOTAL, $currencies->format($buyback_amount, true, DEFAULT_CURRENCY, '', 'buy')) . "\n\n" .
                                        EMAIL_HLA_NEW_BUYBACK_COMMENTS . "\n\n" .
                                        sprintf(EMAIL_HLA_NEW_BUYBACK_STATUS, $buyback_status_name) . "\n" .
                                        EMAIL_HLA_SUPPLIER_SUBMITTED_INFO . "\n\n" .
                                        $email_supplier_hla_info . "\n\n" .
                                        EMAIL_HLA_NEW_BUYBACK_ORDER_FOOTER;
                                tep_mail($customer_name, $customer_row['customers_email_address'], sprintf(EMAIL_HLA_NEW_BUYBACK_SUBJECT, $buyback_request_group_id), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                            }
                        }
                    }
                } else { // fail
                    if ($accMarkSold->errorID == 12) { // account reservation missing or expired
                        $this->accMarkSold_counter++;
                        $this->remove_products_reservation_id();
                        $this->account_reservation();
                        $reservationID = (string) $this->get_products_reservation_id();

                        if (tep_not_null($reservationID)) {
                            return $this->account_retrieve_info($buyback_request_group_id, $seller_id); // Prevent first call from continue processing when the second call done and return
                        } else {
                            $status = 0;
                            $message = '[errorID:' . $accMarkSold->errorID . '] ' . $accMarkSold->errorMsg;
                        }
                    } else {
                        $status = 0;
                        $message = '[errorID:' . $accMarkSold->errorID . '] ' . $accMarkSold->errorMsg;
                    }
                }
            } else if (isset($this->exception)) {
                $status = 0;
                $message = 'SOAP exception : [' . $this->exception->faultcode . '] ' . $this->exception->faultstring;
            } else {
                $status = 0;
                $message = 'Retrieve Account Info API status is Off';
            }

            if ($message != '') {
                $this->orders_products_hla_log($status, $message);
            }
        }
    }

    function accMarkSold($bo_id) {
        if ($this->get_retrieve_account_info_api_status()) {
            $this->action = 'accMarkSold';
            $this->exception = '';

            if (!isset($this->soap_client)) {
                $this->soap_connection();
            }

            $api_data = array();
            $accMarkSold = '';

            $api_key = (string) $this->get_api_key();
            $reservationID = (string) $this->get_products_reservation_id();
            $accountID = (int) $this->get_products_ref_id();
            $products_price = $this->get_products_price();

            if (tep_not_null($api_key) && tep_not_null($accountID)) {
                if (!tep_not_null($reservationID)) {
                    $this->account_reservation();
                    $reservationID = (string) $this->get_products_reservation_id();

                    $this->action = 'accMarkSold';
                    $this->exception = '';
                }

                if (tep_not_null($reservationID)) {
                    $api_data['apiKey'] = $api_key;
                    $api_data['accountID'] = $accountID;
                    $api_data['reservationID'] = $reservationID;
                    $api_data['buybackID'] = $bo_id;

                    if ($products_price['products_base_currency'] == 'EUR') {
                        $api_data['soldForEUR'] = $products_price['products_original_price'];
                    } else {
                        $api_data['soldForUSD'] = $products_price['products_original_price'];
                    }

                    $api_data['shopperFirstName'] = 'first';
                    $api_data['shopperLastName'] = 'last';
                    $api_data['shopperEmailAddress'] = '<EMAIL>';
                    $api_data['shopperCountryCode'] = '60';

                    try {
                        $accMarkSold = $this->soap_client->accMarkSold($api_data);
                        return $accMarkSold;
                    } catch (SoapFault $exception) {
                        $this->exception = $exception;
                    }
                }
            }
        }
    }

    function orders_products_hla_log($status = 0, $message = '') {
        $products_hla_log_sql_data = array();

        $products_hla_log_sql_data = array('products_hla_id' => (int) $this->product_instance_id,
            'log_date' => 'now()',
            'action' => $this->action,
            'status' => $status,
            'message' => $message,
            'added_by' => $this->added_by);
        tep_db_perform(TABLE_PRODUCTS_HLA_LOG, $products_hla_log_sql_data);
    }

}

?>
