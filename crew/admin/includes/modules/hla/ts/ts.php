<?php

class ts 
{
	var $single_xmlns_path;
	var $multiple_xmlns_path;
	var $ignored_ref_id_path;
	var $ignored_acc_id_path;
	var $html_file_path;
	
	var $default_language_id;
	var $default_currency;
	var $default_product_status;
	var $default_product_display;
	
	var $language_arr;
	var $products_hla_id_arr;
	var $ref_id_arr;
	var $acc_id_arr;
	
	var $profile_type;
	var $selling_price_percentage;
    
    function ts ($seller_id, $product_id, $default_language_id, $default_currency, $default_product_status, $default_product_display, $html_file_path, $profilelist_path, $ignored_ref_id_path, $ignored_acc_id_path, $profile_type = 0) {
		$this->single_xmlns_path = 'http://products.toonstorm.com/ns/1.0';
		$this->multiple_xmlns_path = 'http://toonstorm.com/ns/1.0';
		//$this->single_xmlns_path = 'http://toonstorm.com/ns/1.0';
		//$this->multiple_xmlns_path = 'http://toonstorm.com/ns/1.0';
		$this->ignored_ref_id_path = $ignored_ref_id_path;
		$this->ignored_acc_id_path = $ignored_acc_id_path;
		$this->html_file_path = $html_file_path;
		
		$this->seller_id = $seller_id;
		$this->product_id = $product_id;
		
		$this->default_language_id = $default_language_id;
		$this->default_currency = $default_currency;
		$this->default_product_status = $default_product_status; // -1 = Inactive, 0 = Sold, 1 = Active
		$this->default_product_display = $default_product_display; // 0=hide, 1=show
		
		$this->ref_id_arr = array();
		$this->acc_id_arr = array();
		$this->language_arr = array('en-US' => 1);
		$this->products_hla_id_arr = array();
		
        $this->selling_price_percentage = 0.9333;
        
		$this->profile_type = $profile_type == 0 ? 'direct_link' : 'download';
    }
    
    function parsing_xml($xml_path) {
    	if ($xml = @simplexml_load_file($xml_path)) {
    		
			if (tep_not_null((string)$this->language_arr[(string)$xml->channel->language])) {
				$language_id = $this->language_arr[(string)$xml->channel->language];
			} else {
				$language_id = $this->default_language_id;
			}
			
			foreach ($xml->channel->item as $item) {
				$accountid = (string) $item->accountid; // Account ID
				
				if (tep_not_null((string)$accountid)) {
					// multiple characters
					if (isset($this->acc_id_arr[$accountid])) {
						write_in_file($this->ignored_acc_id_path, $accountid);
					} else {
						$this->acc_id_arr[$accountid] = '';
						
						$ts = $item->children($this->multiple_xmlns_path);
						$total_characters = count($ts->refid);
						$products_price = (double)$ts->accountsaleprice;
						
						if ($total_characters > 0 && tep_not_null((string)$products_price)) {
							$product_exist_select_sql = "	SELECT products_hla_id, products_status 
															FROM " . TABLE_PRODUCTS_HLA . " 
															WHERE products_account_id = '".tep_db_input($accountid)."'";
							$product_exist_result_sql = tep_db_query($product_exist_select_sql);
							if ($product_exist_row = tep_db_fetch_array($product_exist_result_sql)) {
								if ($product_exist_row['products_status'] == '-1') {
									write_in_file($this->ignored_acc_id_path, $accountid);
								}
								
								// Account Exist
								$products_hla_id = $product_exist_row['products_hla_id'];
								$this->products_hla_id_arr[] = $products_hla_id;
								
								$products_hla_ref_id_arr = array();
								
								$hla_sql_data_array = array('products_price' => $products_price * $this->selling_price_percentage, 
															'products_original_price' => $products_price);
								tep_db_perform(TABLE_PRODUCTS_HLA, $hla_sql_data_array, 'update', "products_hla_id = '".tep_db_input($products_hla_id)."'");
								
								remove_old_data($products_hla_id, '', false, $language_id);
								
								for ($ref_id_cnt = 0; $ref_id_cnt < $total_characters; $ref_id_cnt++) {
									$ref_id = substr(strrchr($ts->refid[$ref_id_cnt], '#'), 1);
									$character_name = (string)$item->title[$ref_id_cnt];
									$description = strip_tags((string)$item->description[$ref_id_cnt]);
									$profile_link = (string)$ts->product_link[$ref_id_cnt];
									$products_hla_ref_id_arr[] = $ref_id;
									
									filter_character_name($character_name);
									
									if (isset($this->ref_id_arr[$ref_id])) {
										write_in_file($this->ignored_ref_id_path, $ref_id);
									} else {
										$this->ref_id_arr[$ref_id] = '';
										
										// Character Attributes
										$attr_arr = store_attributes($ts, $ref_id_cnt);
										
										$char_info_arr = array( 'product_id' => $this->product_id, 
																'default_currency' => $this->default_currency, 
																'products_status' => $this->default_product_status, 
																'products_display' => $this->default_product_display, 
																'language_id' => $language_id, 
															   	'ref_id' => $ref_id, 
															    'character_name' => $character_name, 
															    'description' => $description, 
															    'products_price' => $products_price * $this->selling_price_percentage, 
															    'products_original_price' => $products_price,
															    'profile' => $this->profile_type,
															    'profile_link' => $profile_link, 
															    'products_hla_id' => $products_hla_id, 
															    'attr_arr' => $attr_arr);
			
										// Character not exist in database
										if (database_process('multiple', 'insert', $char_info_arr, $this->products_hla_id_arr)) {
											// Making profilelist.txt
										}
									}
								}
								
								clean_old_character($products_hla_id, $products_hla_ref_id_arr);
							} else {
								// Account not exist in database
								$hla_sql_data_array = array('seller_id' => $this->seller_id, 
															'products_id' => $this->product_id,
															'products_type' => '2',
															'available_quantity' => '1',
															'actual_quantity' => '1',
															'products_account_id' => $accountid,
															'products_price' => $products_price * $this->selling_price_percentage,
															'products_original_price' => $products_price,
															'products_base_currency' => $this->default_currency,
															'products_status' => $this->default_product_status,
															'products_display' => $this->default_product_display,
															'created_date' => 'now()');
															
								tep_db_perform(TABLE_PRODUCTS_HLA, $hla_sql_data_array);
								$products_hla_id = tep_db_insert_id();
								$this->products_hla_id_arr[] = $products_hla_id;
								
								for ($ref_id_cnt = 0; $ref_id_cnt < $total_characters; $ref_id_cnt++) {
									$ref_id = substr(strrchr($ts->refid[$ref_id_cnt], '#'), 1);
									$character_name = (string)$item->title[$ref_id_cnt];
									$description = strip_tags((string)$item->description[$ref_id_cnt]);
									$profile_link = (string)$ts->product_link[$ref_id_cnt];
									
									filter_character_name($character_name);
									
									if (isset($this->ref_id_arr[$ref_id])) {
										write_in_file($this->ignored_ref_id_path, $ref_id);
									} else {
										$this->ref_id_arr[$ref_id] = '';
			
										// Character Attribute
										$attr_arr = store_attributes($ts, $ref_id_cnt);
										
										$char_info_arr = array( 'product_id' => $this->product_id, 
																'default_currency' => $this->default_currency, 
																'products_status' => $this->default_product_status, 
																'products_display' => $this->default_product_display, 
																'language_id' => $language_id, 
															   	'ref_id' => $ref_id,
															    'character_name' => $character_name, 
															    'description' => $description, 
															    'products_price' => $products_price * $this->selling_price_percentage, 
															    'products_original_price' => $products_price,
															    'profile' => $this->profile_type,
															    'profile_link' => $profile_link, 
															    'products_hla_id' => $products_hla_id, 
															    'attr_arr' => $attr_arr);
			
										if (database_process('multiple', 'insert', $char_info_arr, $this->products_hla_id_arr)) {
											// Making profilelist.txt
										}
									}
								}
							}
						}
					}
				} else {
					// single character
					$ts = $item->children($this->single_xmlns_path);
					$ref_id = substr(strrchr($ts->refid, '#'), 1);
					$character_name = (string)$item->title;
					$description = strip_tags((string)$item->description);
					
					if (isset($this->ref_id_arr[$ref_id])) {
						write_in_file($this->ignored_ref_id_path, $ref_id);
					} else {
						$this->ref_id_arr[$ref_id] = '';
						
						// Character Attribute
						$attr_arr = store_attributes($ts);
						
						$char_info_arr = array( 'product_id' => $this->product_id, 
												'default_currency' => $this->default_currency, 
												'products_status' => $this->default_product_status, 
												'products_display' => $this->default_product_display, 
												'language_id' => $language_id, 
											   	'ref_id' => $ref_id,
											    'character_name' => $character_name, 
											    'description' => $description, 
											    'products_price' => $ts->saleprice * $this->selling_price_percentage, 
											    'products_original_price' => $ts->saleprice,
											    'profile' => $this->profile_type,
											    'profile_link' => $ts->product_link, 
											    'attr_arr' => $attr_arr);
												
						$product_exist_select_sql = "	SELECT chrt.products_hla_id, chrt.products_hla_characters_id, hla.products_status 
														FROM " . TABLE_PRODUCTS_HLA_CHARACTERS . " AS chrt 
														INNER JOIN " . TABLE_PRODUCTS_HLA . " AS hla 
															ON (chrt.products_hla_id = hla.products_hla_id) 
														WHERE chrt.products_ref_id = '".tep_db_input($ref_id)."' 
															AND hla.seller_id = '".tep_db_input($this->seller_id)."'";
						$product_exist_result_sql = tep_db_query($product_exist_select_sql);
						if ($product_exist_row = tep_db_fetch_array($product_exist_result_sql)) {
							if ($product_exist_row['products_status'] == '-1') {
								write_in_file($this->ignored_ref_id_path, $ref_id);
							}
							// Character Exist
							$char_info_arr['characters_id'] = $product_exist_row['products_hla_characters_id'];
							$char_info_arr['products_hla_id'] = $product_exist_row['products_hla_id'];
							$this->products_hla_id_arr[] = $product_exist_row['products_hla_id'];
							
							if (database_process('single', 'update', $char_info_arr, $this->products_hla_id_arr)) {
								// Making profilelist.txt
							}
						} else {
							// Character not exist in database
							if (database_process('single', 'insert', $char_info_arr, $this->products_hla_id_arr)) {
								// Making profilelist.txt
							}
						}
					}
				}
			}
		}
    }
}
?>