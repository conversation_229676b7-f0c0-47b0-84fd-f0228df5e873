<?php
/*
  $Id: newsletter.php,v 1.5 2006/07/19 08:12:38 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

class newsletter {
	var $id, $show_choose_audience, $title, $content;
	
    function newsletter($id, $title, $content) {
      	$this->show_choose_audience = false;
      	$this->id = $id;
      	$this->title = $title;
      	$this->content = $content;
    }
	
    function choose_audience() {
      	return false;
    }
	
    function confirm() {
      	global $HTTP_GET_VARS;
		
		$selected_newsletter_grp_select_sql = "	SELECT ng.newsletters_groups_id, ng.newsletters_groups_name 
												FROM " . TABLE_NEWSLETTERS . " AS n 
												INNER JOIN " . TABLE_NEWSLETTERS_GROUPS . " AS ng 
													ON n.newsletters_groups_id=ng.newsletters_groups_id 
												WHERE n.newsletters_id = '" . $this->id . "'";
		$selected_newsletter_grp_result_sql = tep_db_query($selected_newsletter_grp_select_sql);
		$selected_newsletter_grp_row = tep_db_fetch_array($selected_newsletter_grp_result_sql);
		
		$newsletter_group_array = array();
		$newsletter_group_select_sql = "SELECT newsletters_groups_id, newsletters_groups_name 
										FROM " . TABLE_NEWSLETTERS_GROUPS . " 
										WHERE module = 'newsletter' 
											AND newsletters_groups_id <> '" . tep_db_input($selected_newsletter_grp_row['newsletters_groups_id']) . "'
										ORDER BY newsletters_groups_sort_order";
		$newsletter_group_result_sql = tep_db_query($newsletter_group_select_sql);
		
		while ($newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql)) {
			$newsletter_group_array[$newsletter_group_row['newsletters_groups_id']] = $newsletter_group_row['newsletters_groups_name'];
		}
		
		/*
      	$mail_query = tep_db_query("select count(*) as count from " . TABLE_CUSTOMERS . " where customers_newsletter = '1' and account_activated ='1'");
      	$mail = tep_db_fetch_array($mail_query);
		*/
		$confirm_string = 	'<form name="send_newsletter_form" action="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $this->id . '&action=confirm_send') . '" method="post" >' . "\n" .
							tep_draw_hidden_field('newsletters_groups[]', $selected_newsletter_grp_row['newsletters_groups_id']);
		
      	$confirm_string .= 	'<table border="0" cellspacing="0" cellpadding="2">' . "\n" .
                        	'  	<tr>' . "\n" .
	                        '    	<td class="main"><b>' . sprintf(TEXT_SELECTED_NEWSLETTER_GROUP, $selected_newsletter_grp_row['newsletters_groups_name']) . '</b></td>' . "\n" .
	                        '  	</tr>' . "\n";
		
		if (count($newsletter_group_array)) {
			$confirm_string .=  '  	<tr>
										<td>
											<table border="0" cellspacing="0" cellpadding="2">
												<tr>
													<td class="main" valign="top"><b>'.TEXT_EXTRA_NEWSLETTER_GROUP.'</b></td>
													<td valign="top">
														<table border="0" cellspacing="0" cellpadding="0">
				                							<tr>';
			
			$ng_cnt = 0;
			foreach ($newsletter_group_array as $news_grp_id => $news_grp_title) {
				if ($ng_cnt % 2 == 0) {
					if ($ng_cnt > 0)	echo '</tr>';
				}
				
				$confirm_string .= '<td class="main">' . tep_draw_checkbox_field('newsletters_groups[]', $news_grp_id, false, '', 'id="ng_'.$news_grp_id.'"') . '</td><td class="main"><label for="ng_'.$news_grp_id.'">' . $news_grp_title . '</label></td><td width="5px"></td>';
				
				$ng_cnt++;
			}
			
			$confirm_string .=  '  							</tr>
				                						</table>
													</td>
												</tr>
											</table>
										</td>
									</tr>';
		}
		
	  	$confirm_string .=  '  	<tr>' . "\n" .
	                        '    	<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>' . "\n" .
	                        '  	</tr>' . "\n" .
	                        '  	<tr>' . "\n" .
	                        '    	<td class="main"><b>' . $this->title . '</b></td>' . "\n" .
	                        '  	</tr>' . "\n" .
	                        '  	<tr>' . "\n" .
	                        '    	<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>' . "\n" .
	                        '  	</tr>' . "\n" .
	                        ' 	<tr>' . "\n" .
	                        '    	<td class="main"><tt>' . nl2br($this->content) . '</tt></td>' . "\n" .
	                        '  	</tr>' . "\n" .
	                        '  	<tr>' . "\n" .
	                        '    	<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>' . "\n" .
	                        '  	</tr>' . "\n" .
	                        '  	<tr>' . "\n" .
	                        '    	<td align="right">'. tep_image_submit('button_send.gif', IMAGE_SEND) . '&nbsp;<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $HTTP_GET_VARS['nID']) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a></td>' . "\n" .
	                        '  	</tr>' . "\n" .
	                        '</table>' . "\n" . 
	                        '</form>';
      	return $confirm_string;
    }
	
	function send($newsletter_id) {
		global $HTTP_POST_VARS;
		
		if (isset($HTTP_POST_VARS['newsletters_groups']) && count($HTTP_POST_VARS['newsletters_groups'])) {
			$newsletter_groups_array = array();
			foreach ($HTTP_POST_VARS['newsletters_groups'] as $sel_grp_id) {
				$newsletter_groups_array[] = " FIND_IN_SET($sel_grp_id, customers_newsletter) ";
			}
			$newsletter_groups_where_str = count($newsletter_groups_array) ? implode(" OR ", $newsletter_groups_array) : ' 1 ';
			
			$mail_customer_select_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address 
											FROM " . TABLE_CUSTOMERS . " 
											WHERE account_activated  = '1'
												AND " . $newsletter_groups_where_str;
	    	$mail_customer_result_sql = tep_db_query($mail_customer_select_sql);
	    	
			while ($mail_customer_row = tep_db_fetch_array($mail_customer_result_sql)) {
	      		tep_mail($mail_customer_row['customers_firstname'] . ' ' . $mail_customer_row['customers_lastname'], $mail_customer_row['customers_email_address'], $this->title, $this->content, '', EMAIL_FROM);
	      	}
		}
		
      	$newsletter_id = tep_db_prepare_input($newsletter_id);
      	tep_db_query("update " . TABLE_NEWSLETTERS . " set date_sent = now(), status = '1' where newsletters_id = '" . tep_db_input($newsletter_id) . "'");
    }
}
?>