	.tabTitle {
		color: white;
		background-color: #adb1be;
		padding: 5px 0px 5px 8px;
	}
	
	.tabContent {
		border: solid 1px #97A5B0;
		display: none;
	}
	
	.tabSetting {
		display: block;
	}
	
	.tabSetting_hide {
		display: none;

	}
	
	.padding_10 {
		padding:10px;
	}
	
	.padding_15 {
		padding:15px;
	}
	
	.tabsTd {
		padding:10px;
	}
	
	.priceAutomationBasicHeading {
		font-family: Verdana, Arial, sans-serif;
	  	font-size: 10px;
	  	font-weight: bold;
	  	color: #000; 
	  	background: #D3DCE3;
	}
	
	.priceAutomationHeading {
		font-family: Verdana, Arial, sans-serif;
	  	font-size: 10px;
	  	font-weight: bold;
	  	color: #000; 
	  	background: #D3DCE3;
	  	height:85px;
	}
	
	.buybackPaddingTop {
		padding-top: 6px;
	}
	
	.buybackCompetitorHeadingColumn {
		padding:10px;
		height:25px;
	}
	
	.buybackCompetitorColumn {
		padding:0px 10px 0px 10px;
		font-family: <PERSON><PERSON><PERSON>, <PERSON>l, sans-serif;
	  	font-size: 10px;
	  	color : #000;
	}
	
	.sellingCompetitorHeadingColumn {
		padding:10px;
		height:25px;
	}
	
	.sellingCompetitorColumn {
		padding:0px 10px 0px 10px;
		font-family: Verdana, Arial, sans-serif;
	  	font-size: 10px;
	  	color : #000;
	}

	.averageRow {
		padding: 7px 0px 7px 0px;
		background: #d3eaf8;
		font-weight: bold;
		font-size: 10px;
	}
	
	.ui-tabs-nav .toggleTabSelected span {
		font-weight: bold;
		position: relative;
	    z-index: 2;
	    margin-top: 0;
	    color: green;
	}