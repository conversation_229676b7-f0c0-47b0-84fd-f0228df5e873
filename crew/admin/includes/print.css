/****************************************
	Page Layout							 
****************************************/
body { background-color: #ffffff; color: #000000; margin: 0px; }

/* forms */
CHECKBOX, INPUT, RADIO, SELECT, TEXTAREA, FILE { font-family: "Times New Roman", Times, serif; font-size: 10px; }
FORM { display: inline; }
table, tr, td {font-family: "Times New Roman", Times, serif; font-size: 10px; }

@page { size: A4 portrait; }

/****************************************
	Result Table					     
****************************************/
table.printViewTable {
	border-spacing: 0px;
  	border-top-width: 1px;
  	border-right-width : 1px;
  	border-bottom-width : 1px;
  	border-left-width : 1px;
  	border-collapse: collapse;
}

.invoiceBoxHeading, .ordersBoxHeading, .reportBoxHeading {
  	font-family: Tahoma, Verdana, Arial, sans-serif;
  	font-size: 11px;
  	font-weight: bold;
  	color: #000;
  	border: 1px;
  	border-style: solid;
  	border-color: #000;
  	line-height: 1.5;
}

td.invoiceRecords, td.ordersRecords, td.reportRecords {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	color: #000;
  	border: 1px;
  	border-style: solid;
  	border-color: #000;
  	padding-bottom: 10px;
}