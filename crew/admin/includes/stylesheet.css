/*
  $Id: stylesheet.css,v 1.56 2011/06/17 06:03:34 wilson.sun Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2002 osCommerce

  Released under the GNU General Public License
*/

/* links */
a:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: normal; text-decoration: underline; }
a:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: normal; text-decoration: underline; }
a:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: normal; text-decoration: underline; }
a:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: normal; text-decoration: none; }

a.headerLink:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ffffff; font-weight: bold; text-decoration: none; }
a.headerLink:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ffffff; font-weight: bold; text-decoration: none; }
a.headerLink:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ffffff; font-weight: bold; text-decoration: none; }
a.headerLink:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ffffff; font-weight: bold; text-decoration: underline; }

a.menuBoxHeadingLink:link { font-size: 10px; color: #616060; font-weight: bold; text-decoration: none; }
a.menuBoxHeadingLink:visited { font-size: 10px; color: #616060; font-weight: bold; text-decoration: none; }
a.menuBoxHeadingLink:active { font-size: 10px; color: #616060; font-weight: bold; text-decoration: none; }
a.menuBoxHeadingLink:hover { font-size: 10px; color: #616060; font-weight: bold; text-decoration: none; }

a.menuBoxContentLink:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; font-weight: normal; text-decoration: none; }
a.menuBoxContentLink:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; font-weight: normal; text-decoration: none; }
a.menuBoxContentLink:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; font-weight: normal; text-decoration: none; }
a.menuBoxContentLink:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; font-weight: normal; text-decoration: underline; }

a.splitPageLink:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #0000FF; font-weight: normal; text-decoration: none; }
a.splitPageLink:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #0000FF; font-weight: normal; text-decoration: none; }
a.splitPageLink:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #0000FF; font-weight: normal; text-decoration: none; }
a.splitPageLink:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #0000FF; font-weight: normal; text-decoration: underline; background-color: #FFFF33; }

a.highlightLink:link { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: red; font-weight: bold; text-decoration: underline; }
a.highlightLink:visited { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: red; font-weight: bold; text-decoration: underline; }
a.highlightLink:active { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: red; font-weight: bold; text-decoration: underline; }
a.highlightLink:hover { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: red; font-weight: bold; text-decoration: underline; }

a.actionLink:link, a.actionLink:visited, a.actionLink:active{ font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; font-weight: normal; text-decoration: underline; }
a.actionLink:hover { text-decoration: none; }

/* menu box */
.menuBoxHeading { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; background-color: #ffffff; }
.menuBoxContent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #616060; }

/* page */
body { background-color: #ffffff; color: #000000; margin: 0px; }
.headerBar { background-color: #B3BAC5; }
.headerBarContent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ffffff; font-weight: bold; padding: 2px; }
.columnLeft { background-color: #F0F1F1; border-color: #999999; border-width: 1px; border-style: solid; padding: 2px; }
.pageHeading { font-family: Verdana, Arial, sans-serif; font-size: 18px; color: #727272; font-weight: bold; }
.dottedLine { margin: 5px 0px; border-width:0px;border-bottom-width:1px;border-style:dotted;border-color: #A4A4A4;clear:both;}

/* data table */
.dataTableHeadingRow { background-color: #C9C9C9; }
.dataTableHeadingContent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ffffff; font-weight: bold; }
.dataTableRow { background-color: #F0F1F1; }
.dataTableRowSelected { background-color: #DEE4E8; }
.dataTableRowOver { background-color: #FFFFFF; cursor: pointer; cursor: hand; }
.dataTableContent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; }

/* info box */
.infoBoxHeading { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ffffff; background-color: #B3BAC5; }
.infoBoxContent { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #000000; background-color: #DEE4E8; }

/* message box */

.messageBox { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
.messageStackError, .messageStackWarning { font-family: Verdana, Arial, sans-serif; font-size: 10px; background-color: #ffb3b5; }
.messageStackSuccess { font-family: Verdana, Arial, sans-serif; font-size: 10px; background-color: #99ff00; }

/* forms */
CHECKBOX, INPUT, RADIO, SELECT, TEXTAREA, FILE { font-family: Verdana, Arial, sans-serif; font-size: 11px; }
FORM { display: inline; }

input.redInputBox {
	color: #FFFFFF;
	background-color: #B71C0C;
}

/* account */
.formArea { background-color: #f1f9fe; border-color: #7b9ebd; border-style: solid; border-width: 1px; }
.formAreaTitle { font-family: Tahoma, Verdana, Arial, sans-serif; font-size: 12px; font-weight: bold; }
.formAreaTitleBg { font-family: Tahoma, Verdana, Arial, sans-serif; font-size: 12px; font-weight: bold; background: #D3DCE3; border-color: #7b9ebd; border-style: solid; border-top-width: 1px; border-bottom-width: 0.01px; border-right-width: 1px; border-left-width: 1px; }


/* customers */
.customerFormAreaTitle { font-family: Tahoma, Verdana, Arial, sans-serif; font-size: 12px; font-weight: bold; background: #D3DCE3; border-color: #7b9ebd; border-style: solid; border-top-width: 1px; border-bottom-width: 0.01px; border-right-width: 1px; border-left-width: 1px; }

/* attributes */
.attributes-odd { background-color: #f4f7fd; }
.attributes-even { background-color: #ffffff; }

/* miscellaneous */
.specialPrice { color: #ff0000; }
.oldPrice { text-decoration: line-through; }
.fieldRequired { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ff0000; }
.smallText { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
.main { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
.errorText { font-family: Verdana, Arial, sans-serif; font-size: 10px; color: #ff0000; }

/* Orders stock */
span.sufficientStock {
	font-weight: bold;
	font-size: 18px;
	color: #007700;
}

span.lowStock {
	font-weight: bold;
	font-size: 18px;
	color:#ff0000;
}

.preOrderText {
	font-family: Verdana, Arial, sans-serif;
	font-size: 10px;
	font-weight: bold;
	font-style: italic;
	color: #ff0000;
}

/* Button Effect */
.inputButton {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	background-color: #e3ebf9;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.inputButtonOver {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	background-color: #f2f7ff;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.inputButton:hover, .inputButton:focus { background-color: #f2f7ff; }

.attButton {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	background-color: #CC91E6;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.attButtonOver {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	background-color: #EE9CFA;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.attButton:hover, .attButton:focus { background-color: #EE9CFA; }

.redButton {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	color: white;
	background-color: #FF0000;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.redButtonOver {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	color: white;
	background-color: #FF3333;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.redButton:hover, .redButton:focus { background-color: #FF3333; }

.greenButton {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	color: black;
	background-color: #66FF66;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.greenButtonOver {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	color: black;
	background-color: #99FF99;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.greenButton:hover, .greenButton:focus { background-color: #99FF99; }

.noticeButton {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	background-color: #B5EDBC;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.noticeButtonOver {
	cursor: pointer;
	cursor: hand;
	font-weight: bold;
	background-color: #E0FFC0;
	border: 1px solid #32528c;
	padding: 2px 5px 2px 5px;
}

.noticeButton:hover, .noticeButton:focus { background-color: #E0FFC0; }

/* Rollover Effect */
.moduleRow { }

.moduleRowOver {
	background-color: #336699; 
	//cursor: pointer;
	//cursor: hand;
}

.moduleRowSelected {
	background-color: #336699; 
}

.invoiceBoxHeading, .ordersBoxHeading, .reportBoxHeading, .commonBoxHeading, .cfgBoxHeading {
 	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	font-weight: bold;
  	color: #000; 
  	background: #D3DCE3;
}

.subInvoiceBoxHeading, .subRecordsBoxHeading {
  	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	color: #000; 
}

td.invoiceRecords, td.ordersRecords, td.reportRecords, td.cfgRecords {
	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	color : #000; 
}

td.invoiceRecordsRightBorder {
	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	color : #000;
  	border-right: white 1px solid;
}

tr.invoiceListingEven, tr.ordersListingEven, tr.reportListingEven {
	background: #E5E5E5; 
}

tr.invoiceListingOdd, tr.ordersListingOdd, tr.reportListingOdd {
	background: #D5D5D5; 
}

tr.invoiceListingRowOver, tr.ordersListingRowOver, tr.reportListingRowOver {
	background: #CCFFCC;
}

tr.reportListingSummary {
	background: #8FBC8F; 
}

tr.rowSelected {
	background: #FFCC99;
}

td.bracketListingOdd {
	background: #D7D5D0;
}

td.bracketListingEven {
	background: #FFFFCC;
}

tr.ordersListingMsgOut {
	background: #FAA298;
}

tr.ordersListingMsgIn {
	background: #7187BB;
}

span.categoryPath {
	color: blue;
}

/***************************************
	Message Style
***************************************/
#helptxtDiv {
	font-size: 11.5px;
	color: #000000;
	padding-left: 5px;
	padding-right: 5px;
}

#floatingTopCenterMsg
{
	border-bottom: 1px solid white;
	color: white;
	background-color: #790619;
	/*opacity: 0.8;
	-moz-opacity: 0.8;*/
	filter:progid:DXImageTransform.Microsoft.Alpha(opacity=80);
	
	position: fixed;
	top: 0px;
	margin-left: 5px;
	left: 550px;
	margin-top: 1px;
	padding: 2px;
	z-index: 99;
	
	_position: absolute;
	_top: expression((dummy = document.documentElement.scrollTop) + "px");
	_left: expression((dummy = 550 - document.documentElement.scrollLeft * 2) + "px");
}

/***************************************
	Text Style
***************************************/
span.redIndicator {
	color: red;
}

span.redIndicatorBold {
	color: red;
	font-weight: bold;
}

span.greenIndicator {
	color: green;
}

span.greenIndicatorBold {
	color: green;
	font-weight: bold;
}

span.blueIndicator {
	color: blue;
}

span.darkOrangeIndicator {
	color: darkorange;
}

span.orangeIndicator {
	color: orange;
}

span.blackIndicator {
	color: black;
}

span.flagOrange {
	color: #FF8000;
}

span.flagPink {
	color: #CC6186;
}

span.flagCyan {
	color: #009999;
}

span.flagRed {
	color: red;
}

.boldText {
	font-family: Verdana, Arial, sans-serif;
  	font-size: 10px;
  	font-weight: bold;
  	color : #000;
}

/***************************************
	Table Style
***************************************/
tbody.show {
	display: block;
	display: table-row-group;
}

tbody.hide {
	display: none;
}

span.show, div.show, div.show_row {
	display: inline;
	display: table-cell-group;
}

span.hide, div.hide, div.hide_row {
	display: none;
}

table.spacing {
 	border           : 0px solid #fb9500;
   	width            : 100%;
   	margin           : auto;
   	border-spacing	 : 1px;
}

td.cell_separator {
	font-size: 1px;
	background-color: #FF0000;
	border: 0px solid #FFFFFF;
	width: 0.1em;
}

td.storeBoxLine {
	background-color: #BD9830; 
}

.orderRemarkSelectedRow, .paymentRemarkSelectedRow { background-color: #FFFFCC; }
.orderCommentSystem { background-color: #C4CEE4; }
.orderCommentCrew { background-color: #ECC2C4; }
.orderCommentDelivery { background-color: #DCEADC; }

.selectedFieldSet { border-color: #100F37; }

.selectedNuetralBgFieldSet { border-color: #100F37; background-color: #F2F7FF; color: #0000FF; }
.selectedAlertBgFieldSet { border-color: #100F37; background-color: #F49BB2; color: #FF0000; }

ul.extraInfo {
	text-align: left;
	margin: 0 0 0 2em;
	padding: 1;
/*	list-style-type: none;*/
	position: relative;

} 
ul.extraInfo li {
    padding: 0px;
    margin-left: -10px;
    margin: 0;
}

/***************************************
	xmlhttp								
***************************************/
.xmlHttpMainInfo {
	font-family: Verdana, Arial, sans-serif;
  	font-size: 14px;
  	font-weight: bold;
  	color : #000000;
  	background-color: #ffb3b5;
}

/***************************************
	Maxmind								
***************************************/
div.maxmind_0_score {
	background-color: rgb(3, 65, 1);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
}

div.maxmind_1_score {
	background-color: rgb(0, 110, 0);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
}

div.maxmind_2_score {
	background-color: rgb(11, 150, 17);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
}

div.maxmind_3_score {
	background-color: rgb(92, 206, 17);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
}

div.maxmind_4_score {
	background-color: rgb(173, 242, 45);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
}

div.maxmind_5_score {
	background-color: rgb(242, 247, 60);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
}

div.maxmind_6_score{
	background-color: rgb(242, 219, 55);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
}

div.maxmind_7_score{
	background-color: rgb(247, 207, 51);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
	}
	
div.maxmind_8_score{
	background-color: rgb(252, 113, 29);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
}

div.maxmind_9_score{
	background-color: rgb(238, 72, 43);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	padding-top: 1px;
	font-weight: bold;
	}
	
div.maxmind_10_score{
	background-color: rgb(249, 18, 16);
	width: 26px;
	height: 18px;
	text-align: center;
	color: white;
	font-weight: bold;
	padding-top: 1px;
}

div.maxmind_0_selected {
	background-color: rgb(3, 65, 1);
}

div.maxmind_1_selected {
	background-color: rgb(0, 110, 0);
}
	
div.maxmind_2_selected {
	background-color: rgb(11, 150, 17);
}
	
div.maxmind_3_selected {
	background-color: rgb(92, 206, 17);
}
	
div.maxmind_4_selected {
	background-color: rgb(173, 242, 45);
}
	
div.maxmind_5_selected {
	background-color: rgb(242, 247, 60);
}

div.maxmind_6_selected {
	background-color: rgb(242, 219, 55);
}
	
div.maxmind_7_selected {
	background-color: rgb(247, 207, 51);
}
	
div.maxmind_8_selected {
	background-color: rgb(252, 113, 29);
}

div.maxmind_9_selected {
	background-color: rgb(238, 72, 43);
}

div.maxmind_10_selected {
	background-color: rgb(249, 18, 16);
}

div.maxmind_0_selected, div.maxmind_1_selected, div.maxmind_2_selected, div.maxmind_3_selected, div.maxmind_4_selected,
div.maxmind_5_selected, div.maxmind_6_selected, div.maxmind_7_selected, div.maxmind_8_selected, div.maxmind_9_selected, div.maxmind_10_selected {
	width: 26px;
	text-align: center;
	font-weight: bold;
	display: table;
	height: 27px;
	_position: relative;
	overflow: hidden;
}

#dhtmlTooltip {
	position: absolute;
	width: 150px;
	border: 1px solid black;
	padding: 2px;
	background-color: lightyellow;
	visibility: hidden;
	z-index: 100;
	/*Remove below line to remove shadow. Below line should always appear last within this CSS*/
	filter: progid:DXImageTransform.Microsoft.Shadow(color=gray,direction=135);
	opacity: .90;
	filter:Alpha(opacity = 95);
}

div.bar_time {
	background-color: #7187BB;
	width: 6px;
}

div.bar_time_empty {
	background-color: #00000;
	width: 6px;
}

div.bar {
	background-color: #00F000;
	width: 6px;
}

div.bar_alert_red {
	background-color: #F44676;
	width: 6px;
}

table.bar_border {
	border: 1;
}

.jwindow_popup_title_blue {
 	font-family: Verdana, Arial, sans-serif;
  	font-size: 12px;
  	font-weight: bold;
  	color: #000; 
  	background: #E0EDFD;
}

.jwindow_popup_content_blue {
 	font-family: Verdana, Arial, sans-serif;
  	font-size: 12px;
  	font-weight: bold;
  	color: #000; 
  	background: #96BDFF;
}

.jwindow_popup_title_red {
 	font-family: Verdana, Arial, sans-serif;
  	font-size: 12px;
  	font-weight: bold;
  	color: #000; 
  	background: #FDF6F9;
}

.jwindow_popup_content_red {
 	font-family: Verdana, Arial, sans-serif;
  	font-size: 12px;
  	font-weight: bold;
  	color: #000; 
  	background: #F4D0DA;
}

.rp_icon {
	padding-bottom: 5px; 
	margin:0 5 0 0; 
	font-size: 10px;
	min-width: 25px;
	height: 8px;  
	border:4px solid #D7B517;
	float:left; 
	vertical-align:top; 
	text-align:center;
}

.nrp_icon {
	padding-bottom: 5px; 
	margin:0 5 0 0; 
	font-size: 10px; 
	width: 25px; 
	height: 8px;  
	border:4px solid #13AF25;
	float:left; 
	vertical-align:top; 
	text-align:center;
}


.flag  {
    display: block;
    float: left;
    width: 16px;
    height: 11px;
    line-height: 11px;
    font: 1px monospace;
    background-image: url(../images/flags_matrix.gif);
    margin: 2px 4px 2px 0;
}

.flag.AD { background-position:-16px -44px; }
.flag.AE { background-position:-16px -55px; }
.flag.AF { background-position:-16px -66px; }
.flag.AG { background-position:-16px -77px; }
.flag.AI { background-position:-16px -99px; }
.flag.AL { background-position:-16px -132px; }
.flag.AM { background-position:-16px -143px; }
.flag.AN { background-position:-16px -154px; }
.flag.AO { background-position:-16px -165px; }
.flag.AQ { background-position:-16px -187px; }
.flag.AR { background-position:-16px -198px; }
.flag.AS { background-position:-16px -209px; }
.flag.AT { background-position:-16px -220px; }
.flag.AU { background-position:-16px -231px; }
.flag.AW { background-position:-16px -253px; }
.flag.AX { background-position:-16px -264px; }
.flag.AZ { background-position:-16px -286px; }
.flag.BA { background-position:-32px -11px; }
.flag.BB { background-position:-32px -22px; }
.flag.BD { background-position:-32px -44px; }
.flag.BE { background-position:-32px -55px; }
.flag.BF { background-position:-32px -66px; }
.flag.BG { background-position:-32px -77px; }
.flag.BH { background-position:-32px -88px; }
.flag.BI { background-position:-32px -99px; }
.flag.BJ { background-position:-32px -110px; }
.flag.BM { background-position:-32px -143px; }
.flag.BN { background-position:-32px -154px; }
.flag.BO { background-position:-32px -165px; }
.flag.BR { background-position:-32px -198px; }
.flag.BS { background-position:-32px -209px; }
.flag.BT { background-position:-32px -220px; }
.flag.BV { background-position:-32px -242px; }
.flag.BW { background-position:-32px -253px; }
.flag.BY { background-position:-32px -275px; }
.flag.BZ { background-position:-32px -286px; }
.flag.CA { background-position:-48px -11px; }
.flag.CC { background-position:-48px -33px; }
.flag.CD { background-position:-48px -44px; }
.flag.CF { background-position:-48px -66px; }
.flag.CG { background-position:-48px -77px; }
.flag.CH { background-position:-48px -88px; }
.flag.CI { background-position:-48px -99px; }
.flag.CK { background-position:-48px -121px; }
.flag.CL { background-position:-48px -132px; }
.flag.CM { background-position:-48px -143px; }
.flag.CN { background-position:-48px -154px; }
.flag.CO { background-position:-48px -165px; }
.flag.CR { background-position:-48px -198px; }
.flag.CS { background-position:-48px -209px; }
.flag.CU { background-position:-48px -231px; }
.flag.CV { background-position:-48px -242px; }
.flag.CX { background-position:-48px -264px; }
.flag.CY { background-position:-48px -275px; }
.flag.CZ { background-position:-48px -286px; }
.flag.DE { background-position:-64px -55px; }
.flag.DJ { background-position:-64px -110px; }
.flag.DK { background-position:-64px -121px; }
.flag.DM { background-position:-64px -143px; }
.flag.DO { background-position:-64px -165px; }
.flag.DZ { background-position:-64px -286px; }
.flag.EC { background-position:-80px -33px; }
.flag.EE { background-position:-80px -55px; }
.flag.EG { background-position:-80px -77px; }
.flag.EH { background-position:-80px -88px; }
.flag.ER { background-position:-80px -198px; }
.flag.ES { background-position:-80px -209px; }
.flag.ET { background-position:-80px -220px; }
.flag.FI { background-position:-96px -99px; }
.flag.FJ { background-position:-96px -110px; }
.flag.FK { background-position:-96px -121px; }
.flag.FM { background-position:-96px -143px; }
.flag.FO { background-position:-96px -165px; }
.flag.FR { background-position:-96px -198px; }
.flag.FX { background-position:-96px -264px; }
.flag.GA { background-position:-112px -11px; }
.flag.GB { background-position:-112px -22px; }
.flag.GD { background-position:-112px -44px; }
.flag.GE { background-position:-112px -55px; }
.flag.GF { background-position:-112px -66px; }
.flag.GH { background-position:-112px -88px; }
.flag.GI { background-position:-112px -99px; }
.flag.GL { background-position:-112px -132px; }
.flag.GM { background-position:-112px -143px; }
.flag.GN { background-position:-112px -154px; }
.flag.GP { background-position:-112px -176px; }
.flag.GQ { background-position:-112px -187px; }
.flag.GR { background-position:-112px -198px; }
.flag.GS { background-position:-112px -209px; }
.flag.GT { background-position:-112px -220px; }
.flag.GU { background-position:-112px -231px; }
.flag.GW { background-position:-112px -253px; }
.flag.GY { background-position:-112px -275px; }
.flag.HK { background-position:-128px -121px; }
.flag.HM { background-position:-128px -143px; }
.flag.HN { background-position:-128px -154px; }
.flag.HR { background-position:-128px -198px; }
.flag.HT { background-position:-128px -220px; }
.flag.HU { background-position:-128px -231px; }
.flag.ID { background-position:-144px -44px; }
.flag.IE { background-position:-144px -55px; }
.flag.IL { background-position:-144px -132px; }
.flag.IN { background-position:-144px -154px; }
.flag.IO { background-position:-144px -165px; }
.flag.IQ { background-position:-144px -187px; }
.flag.IR { background-position:-144px -198px; }
.flag.IS { background-position:-144px -209px; }
.flag.IT { background-position:-144px -220px; }
.flag.JM { background-position:-160px -143px; }
.flag.JO { background-position:-160px -165px; }
.flag.JP { background-position:-160px -176px; }
.flag.KE { background-position:-176px -55px; }
.flag.KG { background-position:-176px -77px; }
.flag.KH { background-position:-176px -88px; }
.flag.KI { background-position:-176px -99px; }
.flag.KM { background-position:-176px -143px; }
.flag.KN { background-position:-176px -154px; }
.flag.KP { background-position:-176px -176px; }
.flag.KR { background-position:-176px -198px; }
.flag.KW { background-position:-176px -253px; }
.flag.KY { background-position:-176px -275px; }
.flag.KZ { background-position:-176px -286px; }
.flag.LA { background-position:-192px -11px; }
.flag.LB { background-position:-192px -22px; }
.flag.LC { background-position:-192px -33px; }
.flag.LI { background-position:-192px -99px; }
.flag.LK { background-position:-192px -121px; }
.flag.LR { background-position:-192px -198px; }
.flag.LS { background-position:-192px -209px; }
.flag.LT { background-position:-192px -220px; }
.flag.LU { background-position:-192px -231px; }
.flag.LV { background-position:-192px -242px; }
.flag.LY { background-position:-192px -275px; }
.flag.MA { background-position:-208px -11px; }
.flag.MC { background-position:-208px -33px; }
.flag.MD { background-position:-208px -44px; }
.flag.ME { background-position:-208px -55px; }
.flag.MG { background-position:-208px -77px; }
.flag.MH { background-position:-208px -88px; }
.flag.MK { background-position:-208px -121px; }
.flag.ML { background-position:-208px -132px; }
.flag.MM { background-position:-208px -143px; }
.flag.MN { background-position:-208px -154px; }
.flag.MO { background-position:-208px -165px; }
.flag.MP { background-position:-208px -176px; }
.flag.MQ { background-position:-208px -187px; }
.flag.MR { background-position:-208px -198px; }
.flag.MS { background-position:-208px -209px; }
.flag.MT { background-position:-208px -220px; }
.flag.MU { background-position:-208px -231px; }
.flag.MV { background-position:-208px -242px; }
.flag.MW { background-position:-208px -253px; }
.flag.MX { background-position:-208px -264px; }
.flag.MY { background-position:-208px -275px; }
.flag.MZ { background-position:-208px -286px; }
.flag.NA { background-position:-224px -11px; }
.flag.NC { background-position:-224px -33px; }
.flag.NE { background-position:-224px -55px; }
.flag.NF { background-position:-224px -66px; }
.flag.NG { background-position:-224px -77px; }
.flag.NI { background-position:-224px -99px; }
.flag.NL { background-position:-224px -132px; }
.flag.NO { background-position:-224px -165px; }
.flag.NP { background-position:-224px -176px; }
.flag.NR { background-position:-224px -198px; }
.flag.NU { background-position:-224px -231px; }
.flag.NZ { background-position:-224px -286px; }
.flag.OM { background-position:-240px -143px; }
.flag.PA { background-position:-256px -11px; }
.flag.PE { background-position:-256px -55px; }
.flag.PF { background-position:-256px -66px; }
.flag.PG { background-position:-256px -77px; }
.flag.PH { background-position:-256px -88px; }
.flag.PK { background-position:-256px -121px; }
.flag.PL { background-position:-256px -132px; }
.flag.PM { background-position:-256px -143px; }
.flag.PN { background-position:-256px -154px; }
.flag.PR { background-position:-256px -198px; }
.flag.PS { background-position:-256px -209px; }
.flag.PT { background-position:-256px -220px; }
.flag.PW { background-position:-256px -253px; }
.flag.PY { background-position:-256px -275px; }
.flag.QA { background-position:-272px -11px; }
.flag.RE { background-position:-288px -55px; }
.flag.RO { background-position:-288px -165px; }
.flag.RS { background-position:-288px -209px; }
.flag.RU { background-position:-288px -231px; }
.flag.RW { background-position:-288px -253px; }
.flag.SA { background-position:-304px -11px; }
.flag.SB { background-position:-304px -22px; }
.flag.SC { background-position:-304px -33px; }
.flag.SD { background-position:-304px -44px; }
.flag.SE { background-position:-304px -55px; }
.flag.SG { background-position:-304px -77px; }
.flag.SH { background-position:-304px -88px; }
.flag.SI { background-position:-304px -99px; }
.flag.SJ { background-position:-304px -110px; }
.flag.SK { background-position:-304px -121px; }
.flag.SL { background-position:-304px -132px; }
.flag.SM { background-position:-304px -143px; }
.flag.SN { background-position:-304px -154px; }
.flag.SO { background-position:-304px -165px; }
.flag.SR { background-position:-304px -198px; }
.flag.SS { background-position:-304px -209px; }
.flag.ST { background-position:-304px -220px; }
.flag.SV { background-position:-304px -242px; }
.flag.SY { background-position:-304px -275px; }
.flag.SZ { background-position:-304px -286px; }
.flag.TC { background-position:-320px -33px; }
.flag.TD { background-position:-320px -44px; }
.flag.TF { background-position:-320px -66px; }
.flag.TG { background-position:-320px -77px; }
.flag.TH { background-position:-320px -88px; }
.flag.TJ { background-position:-320px -110px; }
.flag.TK { background-position:-320px -121px; }
.flag.TL { background-position:-320px -132px; }
.flag.TM { background-position:-320px -143px; }
.flag.TN { background-position:-320px -154px; }
.flag.TO { background-position:-320px -165px; }
.flag.TP { background-position:-320px -176px; }
.flag.TR { background-position:-320px -198px; }
.flag.TT { background-position:-320px -220px; }
.flag.TV { background-position:-320px -242px; }
.flag.TW { background-position:-320px -253px; }
.flag.TZ { background-position:-320px -286px; }
.flag.UA { background-position:-336px -11px; }
.flag.UG { background-position:-336px -77px; }
.flag.UM { background-position:-336px -143px; }
.flag.US { background-position:-336px -209px; }
.flag.UY { background-position:-336px -275px; }
.flag.UZ { background-position:-336px -286px; }
.flag.VA { background-position:-352px -11px; }
.flag.VC { background-position:-352px -33px; }
.flag.VE { background-position:-352px -55px; }
.flag.VG { background-position:-352px -77px; }
.flag.VI { background-position:-352px -99px; }
.flag.VN { background-position:-352px -154px; }
.flag.VU { background-position:-352px -231px; }
.flag.WF { background-position:-368px -66px; }
.flag.WS { background-position:-368px -209px; }
.flag.YE { background-position:-400px -55px; }
.flag.YT { background-position:-400px -220px; }
.flag.YU { background-position:-400px -231px; }
.flag.ZA { background-position:-416px -11px; }
.flag.ZM { background-position:-416px -143px; }
.flag.ZW { background-position:-416px -253px; }
.flag.ZR { background-position:-416px -198px; }

div#lockingBtn { vertical-align: top }
div#navBtn { float: left; background-color: #fff; text-align:right; }
div#navBtn ul { margin: 10px 0; padding: 0; border: 0; }
div#navBtn ul li { list-style-type: none; width: 60px; padding: 0; margin: 2; display: block; float: left; background-color: #F3F4F8; font: 12px/20px  "Lucida Grande", Verdana, Arial, sans-serif; text-align: center; }
div#navBtn ul li a	{ color: #333; text-decoration: none; display: block; padding: 2; margin: 10; border: 1px solid #A8B090; }
div#navBtn ul a:hover { background-color: #F5B252 }

div#navBtn ul li#unlock_btn a	{ color: #333; text-decoration: none; font-weight: bold; display: block; padding: 2; margin: 0; border: 1px solid #A8B090; background-color: #5CE400; }
div#navBtn ul li#unlock_btn a:hover { color: #FFF; background-color: #878F78 }

div#navBtn ul li#lock_btn a	{ color: #333; text-decoration: none; font-weight: bold; display: block; padding: 2; margin: 0; border: 1px solid #A8B090; background-color: #EA0101; }
div#navBtn ul li#lock_btn a:hover { color: #FFF; background-color: #878F78 }

.footerPopupTitle {
	padding:0 20px 0px 3px;
	font-size:15px;
	color:#296c97;
	font-weight:bold;
	text-align:left;
}

div.fancy_box {
	position: absolute;
    padding: 20px;
    margin: 0;
    width:450px;
	left:439;
	visibility:hidden;
	z-index:999;
}

div.fancy_inner {
	position: relative;
	width:100%;
	height:100%;
	background: #FFF;
}

div.fancy_content {
	border:3px solid #94CEF7;
	bottom:1px;
	display:block;
	height:auto;
	left:1px;
	right:1px;
	top:1px;
	width:auto;
	
	margin:0;
	position:absolute;
	z-index:100;
}

div.fancy_close {
	position: absolute;
	top: -12px;
	right: -15px;
	height: 31px;
	width: 31px;
	background: url('../images/fancybox/fancy_closebox.png') top left no-repeat;
	cursor: pointer;
	z-index: 181;
	display: none;
}

div.fancy_close_footer {
	position: absolute;
	top: 6px;
	right: 7px;
	height: 31px;
	width: 31px;
	background: url('../images/fancybox/fancy_closebox.png') top left no-repeat;
	cursor: pointer;
	z-index: 181;
}

div.fancy_frame_bg {
	position: absolute;
	top: 0; left: 0;
	width: 100%;
	height: 100%;
	z-index: 70;
	border: 0;
	padding: 0;
	margin: 0;
}
	
div.fancy_bg {
	position: absolute;
	display: block;
	z-index: 70;
	border: 0;
	padding: 0;
	margin: 0;
}

div.fancy_bg_n {
	top: -20px;
	left: 0;
	width: 100%;
	height: 20px;
	background: transparent url('../images/fancybox/fancy_shadow_n.png') repeat-x;
}

div.fancy_bg_ne {
	top: -20px;
	right: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('../images/fancybox/fancy_shadow_ne.png') no-repeat;
}

div.fancy_bg_e {
	right: -20px;
	height: 100%;
	width: 20px;
	background: transparent url('../images/fancybox/fancy_shadow_e.png') repeat-y;
}

div.fancy_bg_se {
	bottom: -20px;
	right: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('../images/fancybox/fancy_shadow_se.png') no-repeat;
}

div.fancy_bg_s {
	bottom: -20px;
	left: 0;
	width: 100%;
	height: 20px;
	background: transparent url('../images/fancybox/fancy_shadow_s.png') repeat-x;
}

div.fancy_bg_sw {
	bottom: -20px;
	left: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('../images/fancybox/fancy_shadow_sw.png') no-repeat;
}

div.fancy_bg_w {
	left: -20px;
	height: 100%;
	width: 20px;
	background: transparent url('../images/fancybox/fancy_shadow_w.png') repeat-y;
}

div.fancy_bg_nw {
	top: -20px;
	left: -20px;
	width: 20px;
	height: 20px;
	background: transparent url('../images/fancybox/fancy_shadow_nw.png') no-repeat;
}

div.theLayerBg {
	visibility:hidden;
	border: medium none ;
	margin: 0pt;
	padding: 0pt;
	z-index: 998;
	width: 100%;
	top: 0pt;
	left: 0pt;
	background-color: rgb(0, 0, 0);
	opacity:0.5;
	filter: alpha(opacity=50);
	-moz-opacity: 0.5;
	position: fixed;
	height: 100%;
}

.green_button, .green_button_fix_width {
	display:block;
}

.green_button, .green_button span, .green_button a,.green_button_fix_width, .green_button_fix_width span, .green_button_fix_width a {
	height: 29px;
	margin:0;
	cursor: pointer;
} 

.green_button span, .green_button a,.green_button_fix_width span, .green_button_fix_width a  {	background:url('../images/buttons.gif') no-repeat; text-decoration:none; color:#FFFFFF;}
.green_button a,.green_button_fix_width a {			float:left;	background-position: left 0px;}
.green_button span,.green_button_fix_width span {		float:left;	background-position: right -29px;}
.green_button a:hover,.green_button_fix_width a:hover  {		float:left;	background-position: left -58px;text-decoration: none;}
.green_button a:hover span,.green_button_fix_width a:hover span {float:left;	background-position: right -87px;}
.green_button font,.green_button_fix_width font { text-align:center;display:block; font-size:11px; font-weight:bold; padding: 6px 15px 0 30px;}


.green_button_fix_width, .green_button_fix_width span, .green_button_fix_width a{
	width:110px;
}

.select2-container {
	min-width: 13em;
	font-family: Verdana, Arial, sans-serif;
	font-size: 11px;
}

select.is-dualselect {
	height: 250px;
	overflow-x: hidden;
}

div #game_region_section {
	margin-top: 10px;
}

#_dualselect_avl_category_id_filter_text {
	width: 106%;
}

#_dualselect_sel_category_id_filter_text {
	width: 106%;
}

input .stock_qty_reference {
	min-width: 14em;
}

.dualselect-wrapper-avl-filter input, .dualselect-wrapper-sel-filter input {
	min-width: 18em;
}

.select2-container--default .select2-selection--single {
	min-width:14em;
}

.select2-container--default .select2-results>.select2-results__options {
	min-width: 14em;
}

.select2-container {
	min-width:15em;
}

#geo_zone_id {
	min-width: 22em;
}

.subproduct_weight {
	margin-right:1px;
}