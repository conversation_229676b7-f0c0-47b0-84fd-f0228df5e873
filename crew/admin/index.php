<?
/*
  $Id: index.php,v 1.60 2013/06/07 12:41:12 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');

$languages = tep_get_languages();
$languages_array = array();
$languages_selected = DEFAULT_LANGUAGE;
for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
    $languages_array[] = array('id' => $languages[$i]['code'],
        'text' => $languages[$i]['name']);
    if ($languages[$i]['directory'] == $language) {
        $languages_selected = $languages[$i]['code'];
    }
}

$selected_box = ''; // collapse all the side mnu
$menu_box_width = "20%";
define('MENU_COL_COUNT', 3);
define('MENU_ROW_COUNT', 8);

$menu_box_array = array();
$goto_array = array(array('id' => '', 'text' => 'Goto >>>>>'));
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
        <style type="text/css">
            a { color:#080381; text-decoration:none; }
            a:link, a:visited { color:#080381; text-decoration:none; }
            a:hover { color:#000000; text-decoration:underline; }

            div.dashLine { font-size: 1px; padding-top: 5px; padding-bottom: 3px;}

            a.mainMenuHeadingLink:link { font-size: 12px; color: #ffffff; font-weight: bold; text-decoration: none; }
            a.mainMenuHeadingLink:visited { font-size: 12px; color: #ffffff; font-weight: bold; text-decoration: none; }
            a.mainMenuHeadingLink:active { font-size: 12px; color: #ffffff; font-weight: bold; text-decoration: none; }
            a.mainMenuHeadingLink:hover { font-size: 12px; color: #ffffff; font-weight: bold; text-decoration: underline; }

            .heading { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 20px; font-weight: bold; line-height: 1.5; color: #7187BB; }
            .main { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 13px; font-weight: bold; line-height: 1.5; color: #ffffff; }
            .sub { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 11px; line-height: 1.5; color: #080381; }
            .text { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 11px; font-weight: bold; line-height: 1.5; color: #000000; }
            .mainMenuBoxHeading { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 12px; color: #ffffff; font-weight: bold; background-color: #7187bb; border-color: #7187bb; border-style: solid; border-width: 1px; }
            .infoBox { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 10px; color: #080381; background-color: #f2f4ff; border-color: #7187bb; border-style: solid; border-width: 1px; }
            .smallText { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
        </style>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->
        <table border="0" width="100%" height="100%" cellspacing="0" cellpadding="2" align="center" valign="middle">
            <tr>
                <td width="<?= BOX_WIDTH ?>" align="left" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" height="440" cellspacing="2" cellpadding="2" align="center" bgcolor="#ffffff">
                        <?
                        if (tep_admin_check_boxes(FILENAME_ADMINISTRATOR) == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_ADMIN_FILES => BOX_ADMINISTRATOR_BOXES,
                                FILENAME_DEFINE_LANGUAGE => BOX_TOOLS_DEFINE_LANGUAGE,
                                FILENAME_LOG_FILES => BOX_ADMINISTRATOR_LOG,
                                FILENAME_SYSTEM_LOG => BOX_ADMINISTRATOR_SYSTEM_LOG,
                                FILENAME_ADMIN_MEMBERS => BOX_ADMINISTRATOR_MEMBERS,
                                FILENAME_ORDERS_STATUS_CONF => BOX_ADMINISTRATOR_ORDERS_STATUS_CONF,
                                FILENAME_PAYMENT_GATEWAY => BOX_ADMINISTRATOR_PAYMENT_GATEWAY);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_ADMINISTRATOR,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=administrator'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $admin_contents_array = $goto_array;
                                $admin_contents = $statistic_contents = '';
                                /*
                                  if (in_array(FILENAME_ADMIN_MEMBERS, $filtered_files_array)) {
                                  $admin_count_query = tep_db_query("SELECT COUNT(admin_id) AS total_admin FROM " . TABLE_ADMIN );
                                  $admin_count_row = tep_db_fetch_array($admin_count_query);

                                  $admin_group_count_query = tep_db_query("SELECT COUNT(admin_groups_id) AS total_admin_group FROM " . TABLE_ADMIN_GROUPS );
                                  $admin_group_count_row = tep_db_fetch_array($admin_group_count_query);

                                  $statistic_contents .= 	'<a href="' . tep_href_link(FILENAME_ADMIN_MEMBERS, 'selected_box=administrator').'">Admin Members</a>:&nbsp;' . $admin_count_row["total_admin"] . '<br>' .
                                  '<a href="' . tep_href_link(FILENAME_ADMIN_MEMBERS, 'selected_box=administrator&gID=groups').'">Admin Groups</a>:&nbsp;' . $admin_group_count_row["total_admin_group"] . '<br>';
                                  }
                                 */
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $admin_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=administrator'), 'text' => $title);
                                    }
                                }
                                if (count($admin_contents_array) > 1) {
                                    $admin_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_ADMINISTRATOR, $admin_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($statistic_contents) && tep_not_null($admin_contents)) {
                                    $admin_contents = $admin_contents . '<div class="dashLine"></div>' . $statistic_contents;
                                } else {
                                    $admin_contents = $admin_contents . $statistic_contents;
                                }

                                if (tep_not_null($admin_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $admin_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('buyback.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_BUYBACK_PRODUCTS => BOX_BUYBACK_PRODUCTS,
                                FILENAME_BUYBACK_GROUPS => BOX_BUYBACK_GROUPS,
                                FILENAME_BUYBACK_REQUESTS => BOX_BUYBACK_REQUESTS,
                                FILENAME_VIP_INVENTORY_REPORT => BOX_VIP_BUYBACK_FOLLOW_UP,
                                FILENAME_SUPPLIERS_RESTOCK_CHARACTERS => BOX_SUPPLIERS_RESTOCK_CHARACTERS,
                                FILENAME_SUPPLIERS_GROUPS => BOX_SUPPLIERS_GROUPS,
                                FILENAME_SUPPLIERS_LIST => BOX_SUPPLIERS_LIST,
                                FILENAME_PRODUCTS_PURCHASE_QUANTITY => BOX_SUPPLIERS_PURCHASE_LISTS,
                                FILENAME_SUPPLIERS_PRICING => BOX_SUPPLIER_PRICING,
                                FILENAME_SUPPLIERS_ORDERS_TRACKING => BOX_SUPPLIERS_ORDERS_TRACKING,
                                FILENAME_SUPPLIERS_PAYMENT => BOX_SUPPLIER_PAYMENT,
                                FILENAME_SUPPLIERS_REPORT => BOX_SUPPLIER_REPORT,
                                FILENAME_COMPETITORS => BOX_COMPETITORS,
                                FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE => BOX_SUPPLIERS_AVERAGE_OFFER_PRICE,
                                FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS => BOX_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS,
                                FILENAME_VIP_SUPPLIER_RANKING => BOX_VIP_SUPPLIER_RANKING,
                                FILENAME_VIP_RULES => BOX_VIP_RULES,
                                FILENAME_PRICE_AUTOMATION => BOX_PRICE_AUTOMATION,
                                FILENAME_PRODUCTS_SUPPLIER => BOX_PRODUCTS_SUPPLIER
                            );

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_BUYBACK,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=buyback'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $buyback_contents_array = $goto_array;
                                $buyback_contents = '';
                                $statistic_contents = '';
                                /*
                                  if (in_array(FILENAME_BUYBACK_REQUESTS, $filtered_files_array)) {
                                  $buyback_request_select_sql = "	SELECT COUNT(brg.buyback_request_group_id) AS status_count, bs.buyback_status_id, bs.buyback_status_name
                                  FROM " . TABLE_BUYBACK_STATUS . " AS bs
                                  LEFT JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
                                  ON bs.buyback_status_id=brg.buyback_status_id
                                  WHERE bs.language_id = '" . (int)$languages_id . "'
                                  GROUP BY bs.buyback_status_id";
                                  $buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
                                  while ($buyback_request_row = tep_db_fetch_array($buyback_request_result_sql)) {
                                  $statistic_contents .= 	'<a href="' . tep_href_link(FILENAME_BUYBACK_REQUESTS, 'selected_box=buyback&filter='.$buyback_request_row["buyback_status_id"]).'">'.$buyback_request_row["buyback_status_name"].' Request</a>:&nbsp;' . $buyback_request_row["status_count"] . '<br>';
                                  }
                                  }
                                 */
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $buyback_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=buyback'), 'text' => $title);
                                    }
                                }

                                if (count($buyback_contents_array) > 1) {
                                    $buyback_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_BUYBACK, $buyback_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($statistic_contents) && tep_not_null($buyback_contents)) {
                                    $buyback_contents = $buyback_contents . '<div class="dashLine"></div>' . $statistic_contents;
                                } else {
                                    $buyback_contents = $buyback_contents . $statistic_contents;
                                }

                                if (tep_not_null($buyback_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $buyback_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('purchase_orders.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_PRODUCTS_LOW_STOCK => BOX_REPORTS_PRODUCTS_LOW_STOCK,
                                FILENAME_PO_COMPANY => BOX_PO_COMPANY,
                                FILENAME_EDIT_PURCHASE_ORDERS => BOX_EDIT_PURCHASE_ORDERS,
                                FILENAME_PO_SUPPLIERS_LIST => BOX_PO_SUPPLIERS_LIST,
                                FILENAME_PO_PAYMENT => BOX_PO_PAYMENT
                            );

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_PO,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=po'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $po_contents_array = $goto_array;
                                $po_contents = '';
                                $statistic_contents = '';

                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $po_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=po'), 'text' => $title);
                                    }
                                }

                                if (count($po_contents_array) > 1) {
                                    $po_contents = tep_draw_pull_down_menu('goto_' . BOX_PO_SUPPLIERS_LIST, $po_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($statistic_contents) && tep_not_null($po_contents)) {
                                    $po_contents = $po_contents . '<div class="dashLine"></div>' . $statistic_contents;
                                } else {
                                    $po_contents = $po_contents . $statistic_contents;
                                }

                                if (tep_not_null($po_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $po_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('catalog.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_CATEGORIES => BOX_CATALOG_CATEGORIES_PRODUCTS, FILENAME_PRODUCTS_EXPECTED => BOX_CATALOG_PRODUCTS_EXPECTED,
                                FILENAME_PRICE_TAGS => BOX_CATALOG_PRICE_TAGS, FILENAME_BATCH_UPDATE_PRICES => BOX_CATALOG_BATCH_UPDATE_PRICES, FILENAME_BATCH_UPDATE_PRICES2 => BOX_CATALOG_BATCH_UPDATE_PRICES2, FILENAME_PRINTABLE_LIST => BOX_CATALOG_PRINTABLE_LIST,
                                FILENAME_PRODUCTS_FLAG => 'Product Flags', FILENAME_NEW_ATTRIBUTES => BOX_CATALOG_ATTRIBUTE_MANAGER,
                                FILENAME_PRODUCTS_ATTRIBUTES => BOX_CATALOG_CATEGORIES_PRODUCTS_ATTRIBUTES, FILENAME_MANUFACTURERS => 'Realms',
                                FILENAME_PRODUCT_PROMOTIONS => BOX_MODULES_PRODUCT_PROMOTIONS, FILENAME_REVIEWS => BOX_CATALOG_REVIEWS, FILENAME_EASYPOPULATE => BOX_CATALOG_EASYPOPULATE);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_CATALOG,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=catalog'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $catalog_contents_array = $goto_array;
                                $catalog_contents = '';
                                $statistic_contents = '';
                                /*
                                  if (in_array(FILENAME_CATEGORIES, $filtered_files_array)) {
                                  $products_query = tep_db_query("SELECT COUNT(*) AS total_active_product FROM " . TABLE_PRODUCTS . " WHERE products_status = '1'");
                                  $products = tep_db_fetch_array($products_query);

                                  $statistic_contents .= '<a href="' . tep_href_link(FILENAME_CATEGORIES, 'selected_box=catalog').'">'.'Products'.'</a>:&nbsp;' . $products["total_active_product"] . '<br>';
                                  }
                                 */
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $catalog_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=catalog'), 'text' => $title);
                                    }
                                }

                                if (count($catalog_contents_array) > 1) {
                                    $catalog_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_CATALOG, $catalog_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($statistic_contents) && tep_not_null($catalog_contents)) {
                                    $catalog_contents = $catalog_contents . '<div class="dashLine"></div>' . $statistic_contents;
                                } else {
                                    $catalog_contents = $catalog_contents . $statistic_contents;
                                }

                                if (tep_not_null($catalog_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $catalog_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('configuration.php') == true) {
                            $heading = array();
                            $contents = array();
                            $configuration_groups_array = $goto_array;

                            $configuration_groups_query = tep_db_query("SELECT configuration_group_id AS cgID, configuration_group_title AS cgTitle FROM " . TABLE_CONFIGURATION_GROUP . " WHERE visible = '1' ORDER BY configuration_group_title ASC");

                            if (tep_db_num_rows($configuration_groups_query)) {
                                $count = 0;
                                while ($configuration_groups = tep_db_fetch_array($configuration_groups_query)) {
                                    $count++;
                                    if ($count == 1) {
                                        $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                            'text' => BOX_HEADING_CONFIGURATION,
                                            'link' => tep_href_link(FILENAME_CONFIGURATION, 'selected_box=configuration&gID=' . $configuration_groups['cgID'], 'NONSSL'),
                                            'link_class' => 'class="mainMenuHeadingLink"');
                                    }

                                    $configuration_groups_array[] = array('id' => tep_href_link(FILENAME_CONFIGURATION, 'selected_box=configuration&gID=' . $configuration_groups['cgID'], 'NONSSL'), 'text' => $configuration_groups['cgTitle']);
                                }
                                if (count($configuration_groups_array) > 1) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => tep_draw_pull_down_menu('goto_' . BOX_HEADING_CONFIGURATION, $configuration_groups_array, '', 'onChange="gotoPage(this.value)"')
                                    );
                                }
                            }

                            $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                        }

                        if (tep_admin_check_boxes('customers.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_CUSTOMERS_GROUPS => BOX_CUSTOMERS_GROUPS, FILENAME_CUSTOMERS => BOX_CUSTOMERS_CUSTOMERS,
                                FILENAME_ORDERS => BOX_CUSTOMERS_ORDERS, FILENAME_MAIL => BOX_TOOLS_MAIL,
                                FILENAME_NEWSLETTERS => BOX_TOOLS_NEWSLETTER_MANAGER, FILENAME_STATS_CUSTOMERS => BOX_REPORTS_ORDERS_TOTAL,
                                FILENAME_WHOS_ONLINE => BOX_TOOLS_WHOS_ONLINE);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_CUSTOMERS,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=customers'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $customers_contents_array = $goto_array;
                                $customers_contents = '';
                                $statistic_contents = '';
                                /*
                                  if (in_array(FILENAME_CUSTOMERS_GROUPS, $filtered_files_array)) {
                                  $cust_group_count_query = tep_db_query("SELECT COUNT(customers_groups_id) AS total_cust_group FROM " . TABLE_CUSTOMERS_GROUPS );
                                  $cust_group_count_row = tep_db_fetch_array($cust_group_count_query);

                                  $statistic_contents .= 	'<a href="' . tep_href_link(FILENAME_CUSTOMERS_GROUPS, 'selected_box=customers').'">'.$filenames_array[FILENAME_CUSTOMERS_GROUPS].'</a>:&nbsp;' . $cust_group_count_row["total_cust_group"] . '<br>';
                                  }

                                  if (in_array(FILENAME_CUSTOMERS, $filtered_files_array)) {
                                  $cust_count_query = tep_db_query("SELECT COUNT(customers_id) AS total_cust FROM " . TABLE_CUSTOMERS );
                                  $cust_count_row = tep_db_fetch_array($cust_count_query);

                                  $statistic_contents .= 	'<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'selected_box=customers').'">'.$filenames_array[FILENAME_CUSTOMERS].'</a>:&nbsp;' . $cust_count_row["total_cust"] . '<br>';
                                  }
                                 */
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $customers_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=customers'), 'text' => $title);
                                    }
                                }

                                if (count($customers_contents_array) > 1) {
                                    $customers_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_CUSTOMERS, $customers_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($statistic_contents) && tep_not_null($customers_contents)) {
                                    $customers_contents = $customers_contents . '<div class="dashLine"></div>' . $statistic_contents;
                                } else {
                                    $customers_contents = $customers_contents . $statistic_contents;
                                }

                                if (tep_not_null($customers_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $customers_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('data_pool.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_DATA_POOL => BOX_DATA_POOL_TEMPLATE,
                                FILENAME_HLA_TEMPLATE => BOX_HLA_TEMPLATE,
                                FILENAME_CDKEY => BOX_CDKEY_TEMPLATE,
                                FILENAME_PUBLISHERS => BOX_PUBLISHERS_TEMPLATE,
                                FILENAME_PUBLISHERS_GAMES => BOX_PUBLISHERS_GAMES_TEMPLATE);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_CUSTOM_PRODUCT,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=data_pool'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $custom_product_contents_array = $goto_array;
                                $custom_product_contents = '';
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $custom_product_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=data_pool'), 'text' => $title);
                                    }
                                }

                                if (count($custom_product_contents_array) > 1) {
                                    $custom_product_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_CUSTOM_PRODUCT, $custom_product_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($custom_product_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $custom_product_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('infolinks.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_INFOLINKS_INDEX => BOX_INFOLINKS_INDEX,
                                FILENAME_DEFINE_MAINPAGE => BOX_DEFINE_MAIN_PAGE,
                                FILENAME_GAME_DATABASE_CONFIG => BOX_GAME_DATABASE_CONFIG,
                                FILENAME_MENU_MANAGEMENT => BOX_MENU_MANAGEMENT
                            );

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_INFOLINKS,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=infolinks'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $info_contents_array = $goto_array;
                                $info_contents = $statistic_contents = '';

                                $info_group_count_query = tep_db_query("SELECT COUNT(infolinks_groups_id) AS total_info_group FROM " . TABLE_INFOLINKS_GROUPS);
                                $info_group_count_row = tep_db_fetch_array($info_group_count_query);
                                //$statistic_contents = 	'<a href="' . tep_href_link(FILENAME_INFOLINKS_INDEX, 'selected_box=infolinks').'">'.$filenames_array[FILENAME_INFOLINKS_INDEX].'</a>:&nbsp;' . $info_group_count_row["total_info_group"] . '<br>';

                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $info_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=infolinks'), 'text' => $title);
                                    }
                                }

                                if (count($info_contents_array) > 1) {
                                    $info_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_INFOLINKS, $info_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($statistic_contents) && tep_not_null($info_contents)) {
                                    $info_contents = $info_contents . '<div class="dashLine"></div>' . $statistic_contents;
                                } else {
                                    $info_contents = $info_contents . $statistic_contents;
                                }

                                if (tep_not_null($info_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $info_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('latest_news.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_LATEST_NEWS => BOX_HEADING_LATEST_NEWS, FILENAME_POLLING => BOX_LATEST_NEWS_POLLING, FILENAME_EVENTS => BOX_EVENTS);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_LATEST_NEWS,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=news'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $latest_news_contents_array = $goto_array;
                                $latest_news_contents = $statistic_contents = '';

                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $latest_news_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=news'), 'text' => $title);
                                    }
                                }

                                if (count($latest_news_contents_array) > 1) {
                                    $latest_news_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_LATEST_NEWS, $latest_news_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($statistic_contents) && tep_not_null($latest_news_contents)) {
                                    $latest_news_contents = $latest_news_contents . '<div class="dashLine"></div>' . $statistic_contents;
                                } else {
                                    $latest_news_contents = $latest_news_contents . $statistic_contents;
                                }

                                if (tep_not_null($latest_news_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $latest_news_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('localization.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_COUNTRIES => BOX_TAXES_COUNTRIES, FILENAME_CURRENCIES => BOX_LOCALIZATION_CURRENCIES,
                                FILENAME_GEO_ZONES => BOX_TAXES_GEO_ZONES, 
                                FILENAME_LANGUAGES => BOX_LOCALIZATION_LANGUAGES, FILENAME_ZONES => BOX_TAXES_ZONES);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_LOCALIZATION,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=localization'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $localization_contents_array = $goto_array;
                                $localization_contents = $statistic_contents = '';
                                /*
                                  if (in_array(FILENAME_COUNTRIES, $filtered_files_array)) {
                                  $country_count_query = tep_db_query("SELECT COUNT(countries_id) AS total_country FROM " . TABLE_COUNTRIES );
                                  $country_count_row = tep_db_fetch_array($country_count_query);
                                  $statistic_contents .= '<a href="' . tep_href_link(FILENAME_COUNTRIES, 'selected_box=localization').'">'.$filenames_array[FILENAME_COUNTRIES].'</a>:&nbsp;' . $country_count_row["total_country"] . '<br>';
                                  }

                                  if (in_array(FILENAME_CURRENCIES, $filtered_files_array)) {
                                  $currency_count_query = tep_db_query("SELECT COUNT(currencies_id) AS total_currency FROM " . TABLE_CURRENCIES );
                                  $currency_count_row = tep_db_fetch_array($currency_count_query);
                                  $statistic_contents .= '<a href="' . tep_href_link(FILENAME_CURRENCIES, 'selected_box=localization').'">'.$filenames_array[FILENAME_CURRENCIES].'</a>:&nbsp;' . $currency_count_row["total_currency"] . '<br>';
                                  }

                                  if (in_array(FILENAME_GEO_ZONES, $filtered_files_array)) {
                                  $defined_zones_count_query = tep_db_query("SELECT COUNT(geo_zone_id) AS total_defined_zone FROM " . TABLE_GEO_ZONES );
                                  $defined_zones_count_row = tep_db_fetch_array($defined_zones_count_query);
                                  $statistic_contents .= '<a href="' . tep_href_link(FILENAME_GEO_ZONES, 'selected_box=localization').'">'.$filenames_array[FILENAME_GEO_ZONES].'</a>:&nbsp;' . $defined_zones_count_row["total_defined_zone"] . '<br>';
                                  }

                                  if (in_array(FILENAME_LANGUAGES, $filtered_files_array)) {
                                  $language_count_query = tep_db_query("SELECT COUNT(languages_id) AS total_language FROM " . TABLE_LANGUAGES );
                                  $language_count_row = tep_db_fetch_array($language_count_query);
                                  $statistic_contents .= '<a href="' . tep_href_link(FILENAME_LANGUAGES, 'selected_box=localization').'">'.$filenames_array[FILENAME_LANGUAGES].'</a>:&nbsp;' . $language_count_row["total_language"] . '<br>';
                                  }

                                  if (in_array(FILENAME_ZONES, $filtered_files_array)) {
                                  $zone_count_query = tep_db_query("SELECT COUNT(zone_id) AS total_zone FROM " . TABLE_ZONES );
                                  $zone_count_row = tep_db_fetch_array($zone_count_query);
                                  $statistic_contents .= '<a href="' . tep_href_link(FILENAME_ZONES, 'selected_box=localization').'">'.$filenames_array[FILENAME_ZONES].'</a>:&nbsp;' . $zone_count_row["total_zone"] . '<br>';
                                  }
                                 */
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $localization_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=localization'), 'text' => $title);
                                    }
                                }

                                if (count($localization_contents_array) > 1) {
                                    $localization_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_LOCALIZATION, $localization_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($statistic_contents) && tep_not_null($localization_contents)) {
                                    $localization_contents = $localization_contents . '<div class="dashLine"></div>' . $statistic_contents;
                                } else {
                                    $localization_contents = $localization_contents . $statistic_contents;
                                }

                                if (tep_not_null($localization_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $localization_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('modules.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_MODULES => BOX_MODULES_ORDER_TOTAL, FILENAME_SHOPPING_CART_COMMENTS => BOX_MODULES_SHOPPING_CART_COMMENTS);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_MODULES,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=modules' . ($filtered_files_array[0] == FILENAME_MODULES ? '&set=ordertotal' : '')),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $modules_contents_array = $goto_array;
                                $modules_contents = '';
                                if (in_array(FILENAME_MODULES, $filtered_files_array)) {
                                    $modules_contents_array[] = array('id' => tep_href_link(FILENAME_MODULES, 'set=ordertotal&selected_box=modules'), 'text' => BOX_MODULES_ORDER_TOTAL);
                                    $modules_contents_array[] = array('id' => tep_href_link(FILENAME_PAYMENT_METHODS, 'selected_box=modules'), 'text' => BOX_MODULES_PAYMENT_METHODS);
                                    $modules_contents_array[] = array('id' => tep_href_link(FILENAME_MODULES, 'set=shipping&selected_box=modules'), 'text' => BOX_MODULES_SHIPPING);
                                }

                                if (in_array(FILENAME_SHOPPING_CART_COMMENTS, $filtered_files_array)) {
                                    $modules_contents_array[] = array('id' => tep_href_link(FILENAME_SHOPPING_CART_COMMENTS, 'selected_box=modules'), 'text' => $filenames_array[FILENAME_SHOPPING_CART_COMMENTS]);
                                }

                                if (count($modules_contents_array) > 1) {
                                    $modules_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_MODULES, $modules_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($modules_contents)) {
                                    $contents[] = array('params' => 'class="infoBox" nowrap',
                                        'text' => $modules_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('payments.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_ACCOUNT_STATEMENT => BOX_PAYMENTS_ACCOUNT_STATEMENT,
                                FILENAME_PAYMENT => BOX_PAYMENTS_PAYMENT,
                                FILENAME_REFUND => BOX_PAYMENTS_REFUND,
                                FILENAME_REDEEM => BOX_POINTS_REDEEM,
                                FILENAME_STORE_CREDIT => BOX_PAYMENTS_STORE_CREDIT,
                                FILENAME_STORE_POINT => BOX_PAYMENTS_STORE_POINT,
                                FILENAME_CREDIT_NOTES_STATEMENT => BOX_PAYMENTS_CREDIT_NOTES_STATEMENT,
                                FILENAME_PO_PROVISION_PAYMENTS => BOX_PO_PROVISION_PAYMENTS
                            );
                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_PAYMENTS,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=payments'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $payment_contents_array = $goto_array;

                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $payment_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=payments'), 'text' => $title);
                                    }
                                }

                                if (count($payment_contents_array) > 1) {
                                    $payment_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_PAYMENTS, $payment_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($payment_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $payment_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('paypalipn.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_PAYPAL => BOX_CUSTOMERS_PAYPAL);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_PAYPALIPN_ADMIN,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=paypalipn'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $paypalipn_contents_array = $goto_array;

                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $paypalipn_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=paypalipn'), 'text' => $title);
                                    }
                                }

                                if (count($paypalipn_contents_array) > 1) {
                                    $paypalipn_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_PAYPALIPN_ADMIN, $paypalipn_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($paypalipn_contents)) {
                                    $contents[] = array('params' => 'class="infoBox"',
                                        'text' => $paypalipn_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('promotions.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_SPECIALS => BOX_MODULES_SPECIAL, FILENAME_PROMOTIONS => BOX_MODULES_PROMOTIONS);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_PROMOTIONS,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=promotions'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $promotion_contents_array = $goto_array;
                                $promotion_contents = '';

                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $promotion_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=promotions'), 'text' => $title);
                                    }
                                }

                                if (count($promotion_contents_array) > 1) {
                                    $promotion_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_PROMOTIONS, $promotion_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($promotion_contents)) {
                                    $contents[] = array('params' => 'class="infoBox" nowrap',
                                        'text' => $promotion_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('reports.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_API_REPORT => BOX_REPORTS_API,
                                FILENAME_PAP_AFFILIATE_REPORT => BOX_TOOLS_PAP_AFFILIATE_REPORT,
                                FILENAME_STATS_PRODUCTS_PURCHASED => BOX_REPORTS_PRODUCTS_PURCHASED, FILENAME_STATS_PRODUCTS_VIEWED => BOX_REPORTS_PRODUCTS_VIEWED,
                                FILENAME_STATS_SALES_REPORT => BOX_REPORTS_SALES, FILENAME_SALES_REPORT => BOX_REPORTS_SALES_NEW,
                                FILENAME_STOCK_REPORT => BOX_REPORTS_STOCK, FILENAME_MGC_SALES_REPORT => BOX_REPORTS_MGC_SALES
                            );

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_REPORTS,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=reports'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $report_contents_array = $goto_array;
                                $report_contents = '';
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $report_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=reports'), 'text' => $title);
                                    }
                                }

                                if (count($report_contents_array) > 1) {
                                    $report_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_REPORTS, $report_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($report_contents)) {
                                    $contents[] = array('params' => 'class="infoBox" nowrap',
                                        'text' => $report_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('sales.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_STATS_ORDERS_TRACKING => BOX_SALES_ACTIVE_ORDERS,
                                FILENAME_STATS_ORDERS_COMMENT => BOX_SALES_PREDEFINED_COMMENTS,
                                FILENAME_ORDERS_TAGS => BOX_SALES_ORDERS_TAGS,
                                FILENAME_CUSTOMERS_ORDER_ACTIVITIES => BOX_SALES_CUSTOMERS_ORDER_ACTIVITIES,
                                FILENAME_ORDERS_MATCHING => BOX_SALES_ORDERS_MATCHING
                            );

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_SALES,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=sales'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $sales_contents_array = $goto_array;
                                $sales_contents = $statistic_contents = '';
                                /*
                                  if (tep_admin_check_boxes(FILENAME_ORDERS, 'sub_boxes') == true) {
                                  $orders_status_select_sql = "	SELECT COUNT(o.orders_id) AS orders_count, os.orders_status_id, os.orders_status_name
                                  FROM " . TABLE_ORDERS_STATUS . " AS os
                                  LEFT JOIN " . TABLE_ORDERS . " AS o
                                  ON os.orders_status_id=o.orders_status
                                  WHERE language_id = '" . $languages_id . "'
                                  GROUP BY os.orders_status_id
                                  ORDER BY os.orders_status_sort_order";
                                  $orders_status_result_sql = tep_db_query($orders_status_select_sql);
                                  while ($orders_status_row = tep_db_fetch_array($orders_status_result_sql)) {
                                  $statistic_contents .= tep_draw_form('order_lists_status_'.$orders_status_row["orders_status_id"], FILENAME_STATS_ORDERS_TRACKING, tep_get_all_get_params(array('action')) . 'selected_box=sales&action=show_report&subaction=sl_status', 'post', '') . "\n";
                                  $statistic_contents .= tep_draw_hidden_field('order_status', tep_array_serialize(array($orders_status_row["orders_status_id"]))) . "\n";
                                  $statistic_contents .= tep_draw_hidden_field('include_subcategory', 1) . "\n";
                                  $statistic_contents .= tep_draw_hidden_field('show_records', ($orders_status_row["orders_status_id"]==2 ? 'ALL' : 50)) . "\n";
                                  $statistic_contents .= '<a href="javascript: document.order_lists_status_'.$orders_status_row["orders_status_id"].'.submit();">' . $orders_status_row["orders_status_name"] . '</a>: ' . $orders_status_row["orders_count"] . '<br>';
                                  $statistic_contents .= '</form>' . "\n";
                                  }
                                  }
                                 */
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $sales_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=sales'), 'text' => $title);
                                    }
                                }

                                if (in_array(FILENAME_STATS_ORDERS_TRACKING, $filtered_files_array)) {
                                    $search_criteria_select_sql = "SELECT search_criteria_id, search_criteria_name FROM " . TABLE_SEARCH_CRITERIA . " WHERE filename='" . FILENAME_STATS_ORDERS_TRACKING . "' ORDER BY search_criteria_name";
                                    $search_criteria_result_sql = tep_db_query($search_criteria_select_sql);
                                    $search_criteria_count = 0;
                                    while ($search_criteria_row = tep_db_fetch_array($search_criteria_result_sql)) {
                                        if (!$search_criteria_count)
                                            $sales_contents_array[] = array('id' => ' ', 'text' => "Saved Criteria >>>>>");
                                        $search_criteria_count++;
                                        $sales_contents_array[] = array('id' => tep_href_link(FILENAME_STATS_ORDERS_TRACKING, 'selected_box=sales&action=show_report&subaction=goto_search&criteria_id=' . $search_criteria_row["search_criteria_id"]), "text" => $search_criteria_row["search_criteria_name"]);
                                    }
                                }

                                if (count($sales_contents_array) > 1) {
                                    $sales_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_SALES, $sales_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($statistic_contents) && tep_not_null($sales_contents)) {
                                    $sales_contents = $sales_contents . '<div class="dashLine"></div>' . $statistic_contents;
                                } else {
                                    $sales_contents = $sales_contents . $statistic_contents;
                                }

                                if (tep_not_null($sales_contents)) {
                                    $contents[] = array('params' => 'class="infoBox" nowrap',
                                        'text' => $sales_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }


                        if (tep_admin_check_boxes(FILENAME_OGC) == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_OGC_REDEEM_STATUS => BOX_OGC_REDEEM_STATUS);
                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));
                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_OGC,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=ogc'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $ogc_contents_array = $goto_array;
                                $ogc_contents = '';
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $ogc_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=ogc'), 'text' => $title);
                                    }
                                }

                                if (count($ogc_contents_array) > 1) {
                                    $ogc_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_OGC, $ogc_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($ogc_contents)) {
                                    $contents[] = array('params' => 'class="infoBox" nowrap',
                                        'text' => $ogc_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('stock.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_STATS_LOW_STOCK => BOX_REPORTS_STOCK_LEVEL, FILENAME_STATS_PRODUCTS => BOX_REPORTS_PRODUCTS, FILENAME_PRODUCTS_LOW_STOCK => BOX_REPORTS_PRODUCTS_LOW_STOCK);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_STOCK,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=stocks'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $stock_contents_array = $goto_array;
                                $stock_contents = '';
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $stock_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=stocks'), 'text' => $title);
                                    }
                                }

                                if (count($stock_contents_array) > 1) {
                                    $stock_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_STOCK, $stock_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($stock_contents)) {
                                    $contents[] = array('params' => 'class="infoBox" nowrap',
                                        'text' => $stock_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('taxes.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_TAX_CLASSES => BOX_TAXES_TAX_CLASSES, FILENAME_TAX_RATES => BOX_TAXES_TAX_RATES);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_LOCATION_AND_TAXES,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=taxes'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $taxes_contents_array = $goto_array;
                                $taxes_contents = '';
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $taxes_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=taxes'), 'text' => $title);
                                    }
                                }

                                if (count($taxes_contents_array) > 1) {
                                    $taxes_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_LOCATION_AND_TAXES, $taxes_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($taxes_contents)) {
                                    $contents[] = array('params' => 'class="infoBox" nowrap',
                                        'text' => $taxes_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes('tools.php') == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_CACHE => BOX_TOOLS_CACHE, FILENAME_CSV_MANAGER => BOX_TOOLS_CSV_MANAGER,
                                FILENAME_DB_ARCHIVE => BOX_TOOLS_DB_ARCHIVE, FILENAME_BACKUP => BOX_TOOLS_BACKUP,
                                FILENAME_BANNER_MANAGER => BOX_TOOLS_BANNER_MANAGER, FILENAME_DEFINE_LANGUAGE => BOX_TOOLS_DEFINE_LANGUAGE,
                                FILENAME_DOWNLOAD_CENTER => BOX_TOOLS_DOWNLOAD_CENTER,
                                FILENAME_IMAGE_UPLOAD => BOX_TOOLS_IMAGE_UPLOAD,
                                FILENAME_SERVER_INFO => BOX_TOOLS_SERVER_INFO,
                                FILENAME_UPGRADE => BOX_TOOLS_UPGRADE, FILENAME_WHOS_ONLINE => BOX_TOOLS_WHOS_ONLINE);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_TOOLS,
                                    'link' => tep_href_link((in_array(FILENAME_UPGRADE, $filtered_files_array) ? FILENAME_UPGRADE : $filtered_files_array[0]), 'selected_box=tools'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $tools_contents_array = $goto_array;
                                $tools_contents = '';
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $tools_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=tools'), 'text' => $title);
                                    }
                                }

                                if (count($tools_contents_array) > 1) {
                                    $tools_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_TOOLS, $tools_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($tools_contents)) {
                                    $contents[] = array('params' => 'class="infoBox" nowrap',
                                        'text' => $tools_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }

                        if (tep_admin_check_boxes(FILENAME_C2C) == true) {
                            $heading = array();
                            $contents = array();

                            $filenames_array = array(FILENAME_C2C_SELLER_GROUP => BOX_C2C_SELLER_GROUP,
                                FILENAME_C2C_SELLER_PRODUCT_LISTING => BOX_C2C_SELLER_PRODUCT_LISTING,
                                FILENAME_C2C_SELLER_VERIFICATION_SETTING => BOX_C2C_SELLER_VERIFICATION_SETTING,
                                FILENAME_C2C_BUYBACK_ORDER => BOX_C2C_BUYBACK_ORDER,
                                FILENAME_C2C_DELIVERY_SPEED => BOX_C2C_DELIVERY_SPEED,
                                FILENAME_C2C_PRODUCT_CONFIGURATION => BOX_C2C_PRODUCT_CONFIGURATION,
                                FILENAME_C2C_CONFIGURATION => BOX_C2C_CONFIGURATION);

                            $filtered_files_array = tep_admin_check_files(array_keys($filenames_array));

                            if (count($filtered_files_array)) {
                                $heading[] = array('params' => 'class="mainMenuBoxHeading"',
                                    'text' => BOX_HEADING_C2C,
                                    'link' => tep_href_link($filtered_files_array[0], 'selected_box=c2c'),
                                    'link_class' => 'class="mainMenuHeadingLink"');

                                $c2c_contents_array = $goto_array;
                                $c2c_contents = '';
                                foreach ($filenames_array as $filename => $title) {
                                    if (in_array($filename, $filtered_files_array)) {
                                        $c2c_contents_array[] = array('id' => tep_href_link($filename, 'selected_box=c2c'), 'text' => $title);
                                    }
                                }

                                if (count($c2c_contents_array) > 1) {
                                    $c2c_contents = tep_draw_pull_down_menu('goto_' . BOX_HEADING_C2C, $c2c_contents_array, '', 'onChange="gotoPage(this.value)"');
                                }

                                if (tep_not_null($c2c_contents)) {
                                    $contents[] = array('params' => 'class="infoBox" nowrap',
                                        'text' => $c2c_contents);
                                }

                                $menu_box_array[] = array('title' => $heading, 'text' => $contents);
                            }
                        }
                        ?>
                        <tr>
                            <td>
                                <table border="0" align="center" cellspacing="2" cellpadding="10">
                                    <?
                                    $box = new box;
                                    $box->setCellPadding(3);
                                    for ($row = 0; $row < MENU_ROW_COUNT; $row++) {
                                        echo '<tr>';
                                        for ($col = 0; $col < MENU_COL_COUNT; $col++) {
                                            echo '<td valign="top" >';
                                            $cur_index = ($col * MENU_ROW_COUNT) + $row;
                                            if (isset($menu_box_array[$cur_index])) {
                                                echo $box->menuBox($menu_box_array[$cur_index]['title'], $menu_box_array[$cur_index]['text']);
                                            } else {
                                                echo '&nbsp;';
                                            }
                                            echo '</td>';
                                            echo '<td>&nbsp;</td>';
                                        }
                                        echo '</tr>';
                                    }
                                    ?>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td><?= tep_draw_separator('pixel_trans.gif', '1', '20') ?></td>
                        </tr>
                        <tr>
                            <td height="1">
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td width="20%">&nbsp;</td>
                                        <td width="60%" valign="bottom">
                                            <?
                                            $contents = array();
                                            if (getenv('HTTPS') == 'on') {
                                                $size = ((getenv('SSL_CIPHER_ALGKEYSIZE')) ? getenv('SSL_CIPHER_ALGKEYSIZE') . '-bit' : '<i>' . BOX_CONNECTION_UNKNOWN . '</i>');
                                                $contents[] = array('params' => 'class="infoBox"',
                                                    'text' => tep_image(DIR_WS_ICONS . 'locked.gif', ICON_LOCKED, '', '', 'align="right"') . sprintf(BOX_CONNECTION_PROTECTED, $size));
                                            } else {
                                                $contents[] = array('params' => 'class="infoBox"',
                                                    'text' => tep_image(DIR_WS_ICONS . 'unlocked.gif', ICON_UNLOCKED, '', '', 'align="right"') . BOX_CONNECTION_UNPROTECTED);
                                            }
                                            $box = new box;
                                            echo $box->tableBlock($contents);
                                            ?>
                                        <td width="20%">&nbsp;</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <? require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
                </td>
            </tr>
        </table>
    </body>
</html>