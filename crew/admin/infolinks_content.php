<?php

require('includes/application_top.php');
$seo_url_alias_infolink_permission = tep_admin_files_actions(FILENAME_INFOLINKS_CONTENT, 'SEO_URL_ALIAS_INFOLINK');

function populatePages($id,$select=0,$language_id,$content_id,&$count) {
	if ($select == 1) {
		return '';
	} else {
		$infolinks_contents_select_sql = "SELECT * FROM infolinks_contents WHERE infolinks_id = '" . $id . "' ORDER BY infolinks_contents_page ASC;";
		$infolinks_contents_result_sql = tep_db_query($infolinks_contents_select_sql);
		
		$count = tep_db_num_rows($infolinks_contents_result_sql);
		
		if ($count == 1) {
			$output = "[ Page: ";
		} else if ($count>1) {
			$output = "[ Pages: ";
		} else {
			$output = '';
		}
		
		if ($count) {
			for ($i=0; $i < $count; ++$i) {
				$row = tep_db_fetch_array($infolinks_contents_result_sql);
				
				if (($count-1) == $i) {
					$output.='<a href="' . tep_href_link('infolinks_content.php', "info_lang=".$language_id."&page_id=".(int)$row['infolinks_contents_id']."&content_id=".$content_id, 'NONSSL') . '" class="menuBoxContentLink">'.(int)$row['infolinks_contents_page'].'</a>';
				} else {
					$output.='<a href="' . tep_href_link('infolinks_content.php', "info_lang=".$language_id."&page_id=".(int)$row['infolinks_contents_id']."&content_id=".$content_id, 'NONSSL') . '" class="menuBoxContentLink">'.(int)$row['infolinks_contents_page'].'</a>'." | ";
				}
			}
			
			$output .= " ]";
			return $output;
		} else {
			return "";
		}
	}
}

function generateAddPageAfterMenu($limit=0) {
	global $row_two;
	
	$limit = (int)$limit;
	
	if ($limit < 0) {
		return '';
	} else if($limit==0) {
		return '<a href="' . tep_href_link(basename($_SERVER[PHP_SELF]), "info_lang=$row_two[language_id]&content_id=$row_two[infolinks_id]&addnewpage", 'NONSSL') . '" class="menuBoxContentLink">Add Page</a>';
	} else {
		$link = tep_href_link(basename($_SERVER[PHP_SELF]), "info_lang=$row_two[language_id]&content_id=$row_two[infolinks_id]&", 'NONSSL');
		echo "<select name='addnewpage' onchange='javascript:cmbRedirectInfoLinks(this,\"$link\");'>";
		echo "<option value='0' selected>Add page after</option>";
		
		for ($i=1; $i<=$limit; ++$i) {
			echo "<option value='".$i."'>Page $i </option>";
		}
		echo "</select>";
	}
}

function getLID() {
	$result = tep_db_query("select LAST_INSERT_ID();");
	if (tep_db_num_rows($result)) {
		$row = tep_db_fetch_array($result);
		$lastid=$row[0];
	} else {
		$lastid=0;
	}
	
	return $lastid;
}

function countPages($id) {
	$result = tep_db_query("select * from infolinks_contents where infolinks_id='$id';");
	$count = tep_db_num_rows($result);
	
	return (int)($count);
}

function tep_get_sub_group($groups_id,$group_hierarchy_arr,$group_hierarchy_id,$prefix) {
	foreach ($group_hierarchy_id as $group_hierarchy_id_row) {
		if ($group_hierarchy_id_row['infolinks_groups_parent_id'] == $groups_id) {
			$group_hierarchy_arr[] = array('id'=> $group_hierarchy_id_row['infolinks_groups_id'],'text'=> $prefix.' '.$group_hierarchy_id_row['infolinks_groups_title']);
			$group_hierarchy_arr = tep_get_sub_group($group_hierarchy_id_row['infolinks_groups_id'],$group_hierarchy_arr,$group_hierarchy_id,$prefix."___");
		}
	}
	return $group_hierarchy_arr;
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<script language="Javascript1.2"><!-- // load htmlarea
// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Newsletter <head>
      _editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
        var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
         if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
          if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
           if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
       <?php if (HTML_AREA_WYSIWYG_BASIC_NEWSLETTER == 'Basic'){ ?>  if (win_ie_ver >= 5.5) {
       document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
       document.write(' language="Javascript1.2"></scr' + 'ipt>');
          } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
       <?php } else{ ?> if (win_ie_ver >= 5.5) {
       document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
       document.write(' language="Javascript1.2"></scr' + 'ipt>');
          } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
       <?php }?>
// --></script>
<script language="JavaScript" src="includes/javascript/jquery.js"></script>
<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
<script language="JavaScript" src="includes/javascript/tiny_mce/tiny_mce.js"></script>
<script language="javascript" type="text/javascript">
	tinyMCE.init({
//		mode : "exact",
		mode : "none",
		elements : "content_text",
		theme : "advanced",
		skin : "o2k7",
		verify_html : false,
		relative_urls : false,
		convert_urls : false,
		forced_root_block : false,
		force_br_newlines : true,
		force_p_newlines : false,
		verify_css_classes : false,
		nowrap : true,
		cleanup : false,
		plugins : "inlinepopups,safari,style,layer,table,advhr,advimage,advlink,insertdatetime,preview,searchreplace,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras",
		theme_advanced_buttons1_add : "fontselect,fontsizeselect",
		theme_advanced_buttons2_add_before: "cut,copy,paste,pastetext,separator",
		theme_advanced_buttons2_add : "preview,separator,forecolor,backcolor,separator,visualchars,iespell,advhr,separator,fullscreen",
		theme_advanced_buttons3_add_before : "tablecontrols,separator",
		theme_advanced_buttons3_add : "insertlayer,moveforward,movebackward,absolute,separator,styleprops",
		theme_advanced_buttons4 : "",
		theme_advanced_toolbar_location : "top",
		theme_advanced_toolbar_align : "left",
		theme_advanced_statusbar_location : "bottom",
		theme_advanced_resizing : true,
		nonbreaking_force_tab : true,
		apply_source_formatting : true,
		relative_urls : false,
		remove_script_host : false
	});
	
</script>

<?php
require(DIR_WS_INCLUDES . 'header.php');

$info_lang = (int)$info_lang;

$infolinks_id = $content_id=(int)$_GET['content_id'];

$sql = mysql_query('SELECT * FROM infolinks where language_id='. $info_lang.' and infolinks_id='.$content_id);
if (mysql_num_rows($sql))  $row=mysql_fetch_array($sql);

$total_pages = 0;
$addnewpage = (int)$_GET['addnewpage'];

if ((int)($_GET['page_id'])>0) {
	$page_id = (int)$_GET['page_id'];
	$subtitle = "Update Content";
	$res = mysql_query("select * from infolinks_contents where infolinks_contents_id='$page_id';");
   	
	if (mysql_num_rows($res)) {
		$rowContent=mysql_fetch_array($res);
	} else {
		$rowContent=array();
	}
} else {
	$subtitle = "Update Link";
	$rowContent = array();
	$page_id=0;
}
?>

<table border="0" width="100%" cellspacing="2" cellpadding="2">
  	<tr>
    	<td width="<?php echo BOX_WIDTH; ?>" valign="top" height="27">
    		<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
			<?	require(DIR_WS_INCLUDES . 'column_left.php');?>
    		</table>
    	</td>
     	<td class="pageHeading" valign="top">
<?		echo $row['infolinks_title']."<br><font class='formAreaTitle'>".$subtitle." [<a href=\"infolinks_index.php?".SID."&info_lang=$info_lang\"> Home </a>]</font>";
		
		if ((int)($row['infolinks_select'])==1) {
			$rad1 = "checked";
		} else {
			$rad2="checked";
			$txtURL_state="disabled";
		}
		
		if ($addnewpage>0 && $addnewpage<=countPages($content_id)) {
			mysql_query("UPDATE infolinks_contents SET infolinks_contents_page=infolinks_contents_page+1 WHERE infolinks_id='". $content_id ."' AND infolinks_contents_page>$addnewpage;");
			$addnewpage++;

			$sql_data_array = array('infolinks_contents_id' => '',
									'infolinks_id' => $infolinks_id,
									'infolinks_contents' => '',
			                        'infolinks_contents_page' => $addnewpage);
			tep_db_perform(TABLE_INFOLINKS_CONTENTS, $sql_data_array);
		} else if (countPages($content_id)==0) {
			$sql_data_array = array('infolinks_contents_id' => '',
									'infolinks_id' => $infolinks_id,
									'infolinks_contents' => '',
			                        'infolinks_contents_page' => '1');
			tep_db_perform(TABLE_INFOLINKS_CONTENTS, $sql_data_array);
		}
		
		$res = mysql_query('SELECT * FROM infolinks where language_id=' . $info_lang.' and infolinks_id='.$content_id);
		if (mysql_num_rows($res))   $row=mysql_fetch_array($res);
?>

<script>
	var current_auto_seo_url_infolink = '<?=$row['infolinks_url_alias']?>';
	function doRestore() {
		document.frmUpdate.txtURL.value='<? echo HTTP_SERVER."/content.php?content_id=$content_id";?>';
	}
	
	function chkImageChk(a) {
		var x=a.value;
		
		if (x.length>3) {
			document.frmUpdate.chkImageOnly.disabled=false;
		} else {
			document.frmUpdate.chkImageOnly.disabled=true;
			document.frmUpdate.chkImageOnly.checked=false;
		}
	}
</script>
<div class="Title"><hr></div>

<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="main">
	<form name="frmUpdate" enctype="multipart/form-data" action="<?=tep_href_link('infolinks_update.php', "info_lang=".$info_lang."&content_id=".$content_id."&page_id=".$page_id)?>" method="post"  onSubmit="return check_form();">
<?
if ($page_id==0) {
?>
	<tr>
		<td width=20% height="34">InfoLink Title (or alt text):</td>
		<td colspan="4">
			<input name="txtTitle" type="text" id="txtTitle" value="<?=$row['infolinks_title']?>" size="50">
  			<input name="chkImageOnly" type="checkbox" id="chkImageOnly" value="1" <? if((int)$row['infolinks_imageonly']==1) echo "checked";?>>Hide Title
  		</td>
	</tr>
<? if ($seo_url_alias_infolink_permission) { ?>
	<tbody id="infolinks_url_name_section" class="show">
	<tr>
		<td width=20% height="34">SEO Alias:</td>
		<td colspan="4">
			<input name="infolinks_url_alias" type="text" id="infolinks_url_alias" value="<?=$row['infolinks_url_alias']?>" size="50">
			<input name="auto_seo" type="checkbox" id="auto_seo" value="1" onclick="auto_generate_seo()";>Auto Generate SEO
  		</td>
	</tr>
	</tbody>
<?		if ($row['infolinks_url_alias']) { ?>
	<tr>
		<td align="top" colspan="5">
			<div id="criteriaSHLink" align="top">
				<a href="javascript:;" onClick="showHideInfoHistory('criteriaBoxDiv', 'criteriaSHLink', '<?=(tep_not_null($content_id) ? 'show' : 'hide')?>');"><?=(tep_not_null($action) ? LINK_HIDE_INFO_HISTORY_BOX : LINK_SHOW_INFO_HISTORY_BOX)?></a>				
			</div>
			<div id="criteriaBoxDiv" class="<?=(tep_not_null($content_id) ? 'hide' : 'show')?>">
				<table border="1" width="50%" cellspacing="0" cellpadding="2">
					<tr>
						<td class="main"><?=TEXT_INFO_DATE_ADDED?></td>
						<td class="main"><?=TEXT_INFO_REMARK?></td>
						<td class="main"><?=TEXT_INFO_ADDED_BY?></td>
					</tr>
<?
			$info_history_select_sql = "SELECT * FROM ". TABLE_INFO_CHANGED_HISTORY ." 
										WHERE info_changed_history_type='infolinks'
											AND info_changed_history_type_id='". $content_id ."'
										ORDER BY info_changed_history_date_added DESC";
			$info_history_result_sql = tep_db_query($info_history_select_sql);
			while ($info_history_row = tep_db_fetch_array($info_history_result_sql)) {
?>
					<tr>
						<td class="main"><?=$info_history_row['info_changed_history_date_added']?></td>
						<td class="main"><?=$info_history_row['info_changed_history_remark']?></td>
						<td class="main"><?=$info_history_row['info_changed_history_added_by']?></td>
					</tr>
<?
			}
?>
			</table>
			</div>
		</td>
	</tr>
	<tr>
		<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	</tr>
<?		}
	} else {
		echo tep_draw_hidden_field('infolinks_url_alias', $row['infolinks_url_alias'], 'id="infolinks_url_alias"');
	}
?>
	<tr>
		<td><?=TEXT_INFO_INFOLINK_GROUP?>:</td>
		<td colspan="4" align="left" valign="top">
<?
	$group_hierarchy = array();
	$group_hierarchy_id = array();
	
	$group_select_sql = "	SELECT  infolinks_groups_id,infolinks_groups_title,infolinks_groups_parent_id  
								FROM " . TABLE_INFOLINKS_GROUPS . "
								ORDER BY infolinks_groups_title ASC";
	$group_result_sql = tep_db_query($group_select_sql);
	
	while ($group_result_row = tep_db_fetch_array($group_result_sql)) {
		$temp_group_array = array();
		$temp_group_array = array(	'infolinks_groups_id'=>$group_result_row['infolinks_groups_id'],
									'infolinks_groups_title'=>$group_result_row['infolinks_groups_title'],
									'infolinks_groups_parent_id'=>$group_result_row['infolinks_groups_parent_id'] );
	
		$group_hierarchy_id[$group_result_row['infolinks_groups_id']] = $temp_group_array;
	
		if (empty($group_result_row['infolinks_groups_parent_id'])) {
			$group_hierarchy[] = $temp_group_array; 
		}
	}
	
	$group_hierarchy_arr[] = array('id'=> "",'text'=> "[ Top ]");
	
	foreach ($group_hierarchy as $group_hierarchy_row) {
		$group_hierarchy_arr[] = array('id'=> $group_hierarchy_row['infolinks_groups_id'],'text'=> $group_hierarchy_row['infolinks_groups_title']);
		$group_hierarchy_arr = tep_get_sub_group($group_hierarchy_row['infolinks_groups_id'],$group_hierarchy_arr,$group_hierarchy_id,"___");
	}

	echo tep_draw_pull_down_menu("infolinks_groups_id", $group_hierarchy_arr, $row['infolinks_groups_id'], " id='infolinks_groups_id' ");
?>
		</td>
	</tr>
	<tr>
		<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	</tr>
	<tr>
		<td height="32" valign="top">InfoLink Image:</td>
		<td colspan="4">
			<input name="txtImage" type="file" id="txtImage" size="50">
<?
	if (trim($row['infolinks_image'])=="") {
		echo "<br>(There is no image uploaded yet for this infolink)<br><br></td></tr>";
	} else {
		echo "<input type='checkbox' value='1' name='deleteimg'> Remove Infolink Image";
		echo "<br>(Current image: <a href='".HTTP_SERVER.$row['infolinks_image']."' target='_blank'>".DIR_FS_CATALOG.$row['infolinks_image']."</a>)<br><br></td></tr>";
	}
?>
	<tr>
		<td height="38">InfoLink Type:</td>
		<td width="3%" align="center" valign="middle">
			<div align="left">
				<input name="radContent" type="radio" id="radContent1" value="1" <?=$rad1?> onClick="swap_fields(0)">
			  	<a href="javascript:doRestore();"><!--Restore--></a>
			</div>
		</td>
		<td width="77%" colspan="3">URL: <input name="txtURL" type="text" id="txtURL" value="<?=$row['infolinks_URL']?>" size="50" <?=$txtURL_state?>></td>
	</tr>
	<tr>
  		<td height="30" valign="top">&nbsp;</td>
  		<td align="center" valign="middle">
  			<div align="left">
  		  		<input name="radContent" type="radio" id="radContent2" value="2" <?=$rad2?> onClick="swap_fields(1)">
		  	</div>
		</td>
  		<td colspan="3" valign="middle">Content</td>
	</tr>
	<tr>
	  	<td height="30" valign="top">Infolink Image Placement: </td>
	  	<td colspan="4" valign="top">
	  		<select name="cmbAlignment" id="cmbAlignment">
<?
	$sel = array("","","");
	$tmp = trim($row['infolinks_align']);
	
	if ($tmp=="left") {
	  	$sel[0]="selected";
	} else if ($tmp=="right") {
	  	$sel[2]="selected";
	} else {
	  	$sel[1]="selected";
	}	  
?>
	        	<option value="left" selected <?=$sel[0]?>>Left</option>
	        	<option value="default" <?=$sel[1]?>>Default</option>
	        	<option value="right" <?=$sel[2]?>>Right</option>
			</select>
		</td>
	</tr>
<?
} else {
?>
	<tr>
		<td height="30" valign="top">InfoLink Title: </td>
		<td colspan="4" valign="top"><?=$row['infolinks_title']?></td>
	</tr>
	<tr>
	  	<td height="30" valign="top">InfoLink Page Number: </td>
	  	<td colspan="4" valign="top"><?=$rowContent['infolinks_contents_page']?></td>
	  	<input type="hidden" name="pgNo" value="<?=$rowContent['infolinks_contents_page']?>">
	</tr>
	<tr>
		<td height="30" valign="top">InfoLink Page Content: </td>
  		<td colspan="4" valign="top">
			<div id="content_text_start" style="padding:5px">
				<a href="javascript:starteditor('content_text')">HTML Editor</a>
			</div>
			<div style="display: none" id="content_text_stop" style="padding:5px">
				<a href="javascript:stopeditor('content_text')">Text Editor</a>
			</div>
		  <? echo tep_draw_textarea_field('content_text', 'soft', '100%', '110', htmlspecialchars(stripslashes($rowContent['infolinks_contents'])) ,'id="content_text"'); ?>
			<script>
	        	var config = new Object();  // create new config object
		        config.width = "550px";
		        config.height = "200px";
		        config.bodyStyle = 'background-color: <?php echo HTML_AREA_WYSIWYG_BG_COLOUR; ?>; font-family: "<?php echo HTML_AREA_WYSIWYG_FONT_TYPE; ?>"; color: <?php echo HTML_AREA_WYSIWYG_FONT_COLOUR; ?>; font-size: <?php echo HTML_AREA_WYSIWYG_FONT_SIZE; ?>pt;';
		        config.debug = <?php echo HTML_AREA_WYSIWYG_DEBUG; ?>;
		        editor_generate('content_text',config);
		    </script>
		</td>
	</tr>
	<tr>
		<td height="30">Delete This Page: </td>
		<td colspan="4"><input name="delpage" type="checkbox" id="delpage" value="1"></td>
	</tr>
<?
}

if ($page_id==0) {
?>
	<tr>
		<td class="main" valign="top"><?='Categories:'?></td>
		<td class="main" colspan="4">
			<table border="0" cellspacing="0" cellpadding="2">
				<tr>
					<td class="main">
						<?=tep_draw_pull_down_menu('infolinks_cat_id[]', tep_get_eligible_category_tree(FILENAME_INFOLINKS_INDEX, 0, '___', '', $cat_tree_array), '', ' multiple="multiple" size=10 onChange="validateSelect();"')?>
					</td>
					<td>&nbsp;</td>
					<td class="main" valign="top"><?=tep_draw_checkbox_field("include_subcategory", 1, ($row['infolinks_include_subcat'] ? true : false), "", ' id="include_subcategory" ') . 'Include Sub Categories'?></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	</tr>
	<tr>
		<td height="30">Open Link in New Window: </td>
		<td colspan="4"><input name="chkNewWin" type="checkbox" id="chkNewWin" value="1" <? if((int)$row['infolinks_new_window']==1) echo "checked";?>></td>
	</tr>
	
	<tbody id="right_navigation_section">
	<tr>
		<td height="30">Display Right Column: </td>
		<td colspan="4">
<?php
    echo tep_draw_selection_field('right_navigation', 'radio', '1', $row['infolinks_right_navigation'] == "0" ? false : true) . 'Yes'; 
    echo tep_draw_selection_field('right_navigation', 'radio', '0', $row['infolinks_right_navigation'] == "0" ? true : false) . 'No';
?>
		</td>
	</tr>
	</tbody>
	
	<tr>
		<td height="30"><?=TEXT_INFO_DISPLAY_BACK_BUTTON?>: </td>
		<td colspan="4">
<?php
	echo tep_draw_selection_field('infolinks_back_button', 'radio', '1', $row['infolinks_back_button'] == "0" ? false : true) . 'Yes'; 
    echo tep_draw_selection_field('infolinks_back_button', 'radio', '0', $row['infolinks_back_button'] == "0" ? true : false) . 'No';
?>
		</td>
	</tr>
	<tr>
		<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	</tr>
	
	<tr>
		<td height="30">InfoLink Sort Order: </td>
		<td colspan="4"><input name="txtSO" type="text" id="txtSO" value="<?=$row['infolinks_sort_order']?>"></td>
	</tr>
	<tr>
		<td height="30">Active:</td>
		<td colspan="4"><input name="chkLinkActive" type="checkbox" id="chkLinkActive" value="1" <? if((int)$row['infolinks_active']==1) echo "checked";?>> </td>
	</tr>
	<script>
	<!--
		/*************************************************************
			Highlight the slected category for multiple selection box 
			when form is loaded if it is selected previously.		  
		*************************************************************/
		var cur_selected = ',' + '<?=$row["infolinks_cat_id"]?>' + ',';
		var reset_infolinks_url_alias = '<?=$row['infolinks_url_alias']?>'; //store data and keep for reseting
		
		var multiSelect = document.frmUpdate.elements['infolinks_cat_id[]'];
		for (i=0; i < multiSelect.length; i++) {
			opt_val = ','+ multiSelect.options[i].value +',';
			if (cur_selected.indexOf(opt_val) != -1) {
				multiSelect.options[i].selected = true;
			}
		}
		
		function validateSelect() {
			var multiSelect = document.frmUpdate.elements['infolinks_cat_id[]'];
			if (multiSelect.options[0].selected == true) {
				for (i=1; i < multiSelect.length; i++) {
					multiSelect.options[i].selected = false;
				}
				document.getElementById('include_subcategory').disabled = true;
				document.getElementById('include_subcategory').checked = false;
			} else {
				document.getElementById('include_subcategory').disabled = false;
			}
		}
		validateSelect();
		
		function check_form() {
			if (document.getElementById('radContent2').checked) {
				<? if ($seo_url_alias_infolink_permission) {?>
					var seo_url_alias = document.getElementById('infolinks_url_alias');
					
					if (seo_url_alias.value == "") {
						alert('<?=JS_TEXT_SEO_ALIAS_REQUESTED?>');
						seo_url_alias.focus();
						seo_url_alias.select();
						return false;
					}
					
					if (validate_seo(seo_url_alias)) {
						alert ('<?=JS_TEXT_SEO_SPECIAL_CHARACTER?>');
						seo_url_alias.focus();
						seo_url_alias.select();
						return false;
					}
			<? } ?>
			}
		}

		function swap_fields(mode) {
			if (mode) {
				<? if ($seo_url_alias_infolink_permission) { ?>
					document.getElementById('infolinks_url_name_section').className = "show";
				<? } ?>
				document.getElementById('txtURL').disabled = true;
				document.getElementById('right_navigation_section').style.display = "";

			} else {
				<? if ($seo_url_alias_infolink_permission) { ?>
					document.getElementById('infolinks_url_name_section').className = "hide";
				<? } ?>
				document.getElementById('txtURL').disabled = false;
				document.getElementById('infolinks_url_alias').value = reset_infolinks_url_alias; //reset infolinks_url_alia to original value
				document.getElementById('right_navigation_section').style.display = "none";
			}
		}
		
		function auto_generate_seo() { //auto generate SEO URL alias
			var infolinks_title = document.getElementById('txtTitle');
			var infolinks_url_alias = document.getElementById('infolinks_url_alias');
			
			if (document.getElementById('auto_seo').checked == true) {
				var str = filter_special_char(infolinks_title.value);
				infolinks_url_alias.value = str.toLowerCase(); //to lowercase
			} else {
				infolinks_url_alias.value = current_auto_seo_url_infolink;
			}
		}
		
		function showHideInfoHistory(criteria_div, criteria_link_div, classtype) {
			DOMCall(criteria_div).className = classtype;
			
			if (classtype == 'show') {
				DOMCall(criteria_link_div).innerHTML = '<a href="javascript:;" onclick="showHideInfoHistory(\''+criteria_div+'\', \''+criteria_link_div+'\', \'hide\');">'+'<?=LINK_HIDE_INFO_HISTORY_BOX?>'+'</a>';
			} else {
				DOMCall(criteria_link_div).innerHTML = '<a href="javascript:;" onclick="showHideInfoHistory(\''+criteria_div+'\', \''+criteria_link_div+'\', \'show\');">'+'<?=LINK_SHOW_INFO_HISTORY_BOX?>'+'</a>';
			}
		}
		
		if (document.getElementById('radContent1').checked) { //if radContent 1 is checked
			swap_fields(0);
		} else {
			swap_fields(1);
		}
	//-->
	</script>
<?
}
?>
	<tr>
  		<td height="30">&nbsp;</td>
  		<td colspan="4"><input type="submit" name="Submit" value="Update"></td>
	</tr>
	</form>
</table>
<br>
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
</div>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>