<?php
require('includes/application_top.php');

$link_id=(int)$_REQUEST['link_id'];
$type=(int)$_REQUEST['type'];
$info_lang=(int)$_REQUEST['info_lang'];

if($type==1) {	// is link
	mysql_query("delete from infolinks where infolinks_id='$link_id';");
	mysql_query("delete from infolinks_contents where infolinks_id='$link_id';");
	tep_db_query("DELETE FROM " . TABLE_INFO_CHANGED_HISTORY . " WHERE info_changed_history_type_id = '" . $link_id . "' AND info_changed_history_type='infolinks'"); //delete info changed history
} elseif($type==2) {	// is group
	
	$result=mysql_query("select infolinks_id from infolinks where infolinks_groups_id='$link_id';");
	
	while($row=mysql_fetch_array($result)) {
		$infolink_id=(int)$row[0];
		mysql_query("delete from infolinks_contents where infolinks_id='$infolink_id';");
	}
	
	tep_db_query("DELETE FROM " . TABLE_INFO_CHANGED_HISTORY . " WHERE info_changed_history_type_id = '" . $infolink_id . "' AND info_changed_history_type='infolinks'"); //delete info changed history
	mysql_query("delete from infolinks_groups where infolinks_groups_id='$link_id';");
	mysql_query("delete from infolinks where infolinks_groups_id='$link_id';");
}

header("Location: infolinks_index.php?".SID."&info_lang=$info_lang");

require(DIR_WS_INCLUDES . 'application_bottom.php'); 
?>