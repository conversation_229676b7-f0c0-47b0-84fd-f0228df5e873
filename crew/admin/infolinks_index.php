<?
require('includes/application_top.php');

function populatePages($id,$select=0,$language_id,$content_id,&$count)
{
	if ($select==1)
		return "";
	else {
	    $infolink_content_result = tep_db_query("SELECT * FROM ". TABLE_INFOLINKS_CONTENTS ." WHERE infolinks_id='$id' ORDER BY infolinks_contents_page ASC");
        $count = tep_db_num_rows($infolink_content_result);
		//$result=mysql_query("select * from infolinks_contents where infolinks_id='$id' order by infolinks_contents_page asc;");
		//$count=mysql_num_rows($result);		

		//if (mysql_num_rows($result)) {
		if ($count > 0) {
		    /*
			for ($i=0; $i<$count; ++$i) {
				$row = mysql_fetch_array($result);
				
				if (($count-1)==$i)
					$output.='<a href="' . tep_href_link('infolinks_content.php', "info_lang=".$language_id."&page_id=".(int)$row['infolinks_contents_id']."&content_id=".$content_id, 'NONSSL') . '" class="menuBoxContentLink">'.(int)$row['infolinks_contents_page'].'</a>';
				else
					$output.='<a href="' . tep_href_link('infolinks_content.php', "info_lang=".$language_id."&page_id=".(int)$row['infolinks_contents_id']."&content_id=".$content_id, 'NONSSL') . '" class="menuBoxContentLink">'.(int)$row['infolinks_contents_page'].'</a>'." | ";
			}
			*/
			if ($count==1)
    			$output="[ Page: ";
    		elseif($count>1)
    			$output="[ Pages: ";
    		else
    			$output="";
			
			while ($infolink_row = tep_db_fetch_array($infolink_content_result)) {
				$output .= '<a href="'. tep_href_link('infolinks_content.php', "info_lang=".$language_id."&page_id=".(int)$infolink_row['infolinks_contents_id']."&content_id=".$content_id, 'NONSSL') . '" class="menuBoxContentLink">'.(int)$infolink_row['infolinks_contents_page'].'</a>'." | ";
			}

			$output = substr($output, 0, -3);
			$output.=" ]";
			return $output;
		} else {
			return "";
		}
	}
}

function generateAddPageAfterMenu($limit=0, $infolink_row)
{
	$limit = (int)$limit;
	//global $row_two;
	
	if ($limit<0)
		return "";
	else if ($limit==0) {
		return '<a href="' . tep_href_link(basename($_SERVER[PHP_SELF]), "info_lang=". $infolink_row['language_id'] ."&content_id=". $infolink_row['infolinks_id'] ."&addnewpage", 'NONSSL') . '" class="menuBoxContentLink">Add Page</a>';
	} else {
		$link=tep_href_link(basename($_SERVER[PHP_SELF]), "info_lang=". $infolink_row['language_id'] ."&content_id=". $infolink_row[infolinks_id] ."&", 'NONSSL');
		echo "<select name='addnewpage' onchange='javascript:cmbRedirectInfoLinks(this,\"$link\");'>";		
		echo "<option value='0' selected>Add page after</option>";
		
		for($i=1;$i<=$limit;++$i) {
			echo "<option value='".$i."'>Page $i </option>";			
		}			
		echo "</select>";
	}
}

//this can delete
function getLID()
{
	$result = mysql_query("select LAST_INSERT_ID();");
	
	if (mysql_num_rows($result)) {
		$row=mysql_fetch_array($result);
		$lastid=$row[0];
	} else {
		$lastid = 0;
	}
	return $lastid;
}


function countPages($id)
{
    $infolink_content_result = tep_db_query("SELECT * FROM ". TABLE_INFOLINKS_CONTENTS ." WHERE infolinks_id='$id';");
    $count = tep_db_num_rows($infolink_content_result);
    return (int) $count;

    /*
	$result = mysql_query("select * from infolinks_contents where infolinks_id='$id';");
	$count = mysql_num_rows($result);			
	
	return (int)($count);
	*/
}

function tep_infolinks_validateImage($fileArray,&$filename,&$filename_tmp,&$ext)
{
	$allowed=array("gif","jpg","png","bmp");
	$filename = strtolower(trim(basename($fileArray['name'])));
	$filename_tmp = $fileArray['tmp_name'];
	$filesize = (int)$fileArray['size'];
	$parts = explode(".",$filename);
	
	if (sizeof($parts)>0)
		$ext=$parts[sizeof($parts)-1];
	else
		$ext="";
	
	if ($ext!="" && $filesize>0) {
		if ($ext==$allowed[0] || $ext==$allowed[1] || $ext==$allowed[2] || $ext==$allowed[3]) {
			$upload = true;
		} else {
			$upload = false;
		}
	} else {
		$upload = false;
	}
	return $upload;
}

function tep_get_sub_group($groups_id,$group_hierarchy_arr,$group_hierarchy_id,$prefix) {
	foreach ($group_hierarchy_id as $group_hierarchy_id_row) {
		if ($group_hierarchy_id_row['infolinks_groups_parent_id'] == $groups_id) {
			$group_hierarchy_arr[] = array('id'=> $group_hierarchy_id_row['infolinks_groups_id'],'text'=> $prefix.' '.$group_hierarchy_id_row['infolinks_groups_title']);
			$group_hierarchy_arr = tep_get_sub_group($group_hierarchy_id_row['infolinks_groups_id'],$group_hierarchy_arr,$group_hierarchy_id,$prefix."___");
		}
	}
	return $group_hierarchy_arr;
}

function tep_check_is_sub_group($groups_id,$subgroup_id,$group_hierarchy_id,$flag) {
	foreach ($group_hierarchy_id as $group_hierarchy_id_row) {
		if ($group_hierarchy_id_row['infolinks_groups_parent_id'] == $groups_id) {
			if ($group_hierarchy_id_row['infolinks_groups_id'] == $subgroup_id) {
				$flag = true;
				break;
			}

			$flag = tep_check_is_sub_group($group_hierarchy_id_row['infolinks_groups_id'],$subgroup_id,$group_hierarchy_id,$flag);
		}
	}
	
	return $flag;
}

$info_lang=(int)$languages_id;
$doaddlink=(int)$doaddlink;
$showall=(int)$showall;
$do=trim($do);
$dotype=(int)$dotype;
$id=(int)$id;
$content_id=$infolinks_id=(int)$_GET['content_id'];
$total_pages=0;
$addnewpage=(int)$_GET['addnewpage'];
$selected_group_id=(int)$_GET['selected_group_id'];
$add_into_group_id=(int)$_GET['add_into_group_id'];
$action = $_GET['action'];

// Extract Group Hierarchy
$group_hierarchy = array();
$group_hierarchy_id = array();

$group_select_sql = "	SELECT  infolinks_groups_id,infolinks_groups_title,infolinks_groups_parent_id  
							FROM " . TABLE_INFOLINKS_GROUPS . "
							ORDER BY infolinks_groups_title ASC";
$group_result_sql = tep_db_query($group_select_sql);

while ($group_result_row = tep_db_fetch_array($group_result_sql)) {
	$temp_group_array = array();
	$temp_group_array = array(	'infolinks_groups_id'=>$group_result_row['infolinks_groups_id'],
								'infolinks_groups_title'=>$group_result_row['infolinks_groups_title'],
								'infolinks_groups_parent_id'=>$group_result_row['infolinks_groups_parent_id'] );

	$group_hierarchy_id[$group_result_row['infolinks_groups_id']] = $temp_group_array;

	if (empty($group_result_row['infolinks_groups_parent_id'])) {
		$group_hierarchy[] = $temp_group_array; 
	}
}

$group_hierarchy_arr[] = array('id'=> "",'text'=> TEXT_TOP);

foreach ($group_hierarchy as $group_hierarchy_row) {
	$group_hierarchy_arr[] = array('id'=> $group_hierarchy_row['infolinks_groups_id'],'text'=> $group_hierarchy_row['infolinks_groups_title']);
	$group_hierarchy_arr = tep_get_sub_group($group_hierarchy_row['infolinks_groups_id'],$group_hierarchy_arr,$group_hierarchy_id,"___");
}

if ($action == "movegroup") {
	$move_group_id = $_POST['move_group_id'];
	$moveto_group_id = $_POST['moveto_group_id'];
	
	$subgroupflag = tep_check_is_sub_group($move_group_id,$moveto_group_id,$group_hierarchy_id,false);

	if ($subgroupflag) {
	 	echo "error";
	}
	else {
	 	tep_db_query ("UPDATE infolinks_groups SET infolinks_groups_parent_id='".$moveto_group_id."' WHERE infolinks_groups_id='".$move_group_id."'");
	 	echo "success";
	}

	exit;
}

if (empty($selected_group_id)){
	$selected_group_id = "0";
}

if (sizeof($_FILES)>0) {
	$uploaddir = DIR_FS_CATALOG.DIR_WS_IMAGES;
	
	if(tep_infolinks_validateImage($_FILES['txtImage'],$txtImageFilename,$txtImageFilenameTmp,$txtImageFilenameExt)) {
		$uploadtxtImage=true;
	} else 
		$uploadtxtImage=false;
	
	if(tep_infolinks_validateImage($_FILES['txtSeperator'],$txtSeperatorFilename,$txtSeperatorFilenameTmp,$txtSeperatorFilenameExt))
		$uploadtxtSeperator=true;
	else
		$uploadtxtSeperator=false;
}

if($dotype==0 && $do=="deactivate")
	tep_db_query ("UPDATE infolinks_groups SET infolinks_groups_active=0 WHERE infolinks_groups_id=$id");
elseif($dotype==0 && $do=="activate")
	tep_db_query ("UPDATE infolinks_groups SET infolinks_groups_active=1 WHERE infolinks_groups_id=$id");
elseif($dotype==1 && $do=="deactivate")
	tep_db_query ("UPDATE infolinks SET infolinks_active=0 WHERE infolinks_id=$id");
elseif($dotype==1 && $do=="activate")
	tep_db_query ("UPDATE infolinks SET infolinks_active=1 WHERE infolinks_id=$id");


if ($REQUEST_METHOD=="POST") {
	$info_lang=(int)$info_lang;
	$txtSO=(int)$txtSO;
	$chkShowTitle=(int)$chkShowTitle;
	$active=(int)$chkActive;
	$deletetxtImage = (int)$deletetxtImage;
	$deletetxtSeperator = (int)$deletetxtSeperator;

	if (trim($txtTitle)=="")
		$txtTitle="Untitled";

	if ((int)$type==1)			// add group
	{
		//mysql_query("insert into infolinks_groups values ('','$info_lang','$txtTitle','$txtSO','','$chkShowTitle','$cmbAlignment','$active','');");
		//$lid=getLID();

		$sql_data_array = array('infolinks_groups_id' => '',
								'language_id' => $info_lang,
								'infolinks_groups_title' => $txtTitle,
		                        'infolinks_groups_sort_order' => $txtSO,
		                        'infolinks_groups_bg_image' => '',
		                        'infolinks_groups_show_title' => $chkShowTitle,
		                        'infolinks_groups_align' => $cmbAlignment,
		                        'infolinks_groups_active' => $active,
		                        'infolinks_groups_seperator_image' => '',
		                        'infolinks_groups_main_page_id' => $group_main_page,
		                        'infolinks_groups_parent_id' => $infolinks_groups_parent_id);
		tep_db_perform(TABLE_INFOLINKS_GROUPS, $sql_data_array);
		$lid = tep_db_insert_id();
		
		if ($deletetxtImage==1)	{
			//mysql_query("update infolinks_groups set infolinks_groups_bg_image='' where infolinks_groups_id='$lid';");
			tep_db_query("UPDATE infolinks_groups SET infolinks_groups_bg_image='' WHERE infolinks_groups_id='$lid'");
		}
		
		if ($uploadtxtImage && $deletetxtImage!=1) {				
			$filename_new = $uploaddir. "infolinks_groups_". $lid .".". $txtImageFilenameExt;
			
			if (file_exists($filename_new)) {
				unlink($filename_new);
			}

			if (move_uploaded_file($txtImageFilenameTmp, $filename_new)) {			
				$imagebasename=DIR_WS_IMAGES.basename($filename_new);			
				//mysql_query("update infolinks_groups set infolinks_groups_bg_image='$imagebasename' where infolinks_groups_id='$lid';");
				tep_db_query("UPDATE infolinks_groups SET infolinks_groups_bg_image='$imagebasename' WHERE infolinks_groups_id='$lid'");
			}
		}

		if ($deletetxtSeperator==1) {
            //mysql_query("update infolinks_groups set infolinks_groups_seperator_image='' where infolinks_groups_id='$lid';");
            tep_db_query("UPDATE infolinks_groups SET infolinks_groups_seperator_image='' WHERE infolinks_groups_id='$lid'");
		}
		
		if ($uploadtxtImage && $deletetxtSeperator!=1) {				
			$filename_new = $uploaddir ."infolinks_groups_seperator_". $lid .".". $txtSeperatorFilenameExt;

			if (file_exists($filename_new)) {
				unlink($filename_new);
			}	

			if (move_uploaded_file($txtSeperatorFilenameTmp, $filename_new)) {			
				$imagebasename=DIR_WS_IMAGES . basename($filename_new);			
				//mysql_query("update infolinks_groups set infolinks_groups_seperator_image='$imagebasename' where infolinks_groups_id='$lid';");
				tep_db_query("UPDATE infolinks_groups SET infolinks_groups_seperator_image='$imagebasename' WHERE infolinks_groups_id='$lid'");
			}
		}
		
       	tep_redirect(tep_href_link(FILENAME_INFOLINKS_INDEX , 'selected_group_id='.$infolinks_groups_parent_id));
	} else if ((int)$type==2) {	// update group
		//mysql_query("update infolinks_groups set infolinks_groups_title='$txtTitle', infolinks_groups_sort_order='$txtSO', infolinks_groups_show_title='$chkShowTitle', infolinks_groups_align='$cmbAlignment', infolinks_groups_active='$active' where infolinks_groups_id='$doedit';");	
		tep_db_query("UPDATE infolinks_groups SET infolinks_groups_main_page_id = '$group_main_page', infolinks_groups_title='$txtTitle', infolinks_groups_sort_order='$txtSO', infolinks_groups_show_title='$chkShowTitle', infolinks_groups_align='$cmbAlignment', infolinks_groups_active='$active' WHERE infolinks_groups_id='$doedit'");	
		
		if ($deletetxtImage==1) {
			//mysql_query("update infolinks_groups set infolinks_groups_bg_image='' where infolinks_groups_id='$doedit';");
			tep_db_query("UPDATE infolinks_groups SET infolinks_groups_bg_image='' WHERE infolinks_groups_id='$doedit'");
		}
		
		if ($uploadtxtImage && $deletetxtImage!=1) {		
			$filename_new = $uploaddir ."infolinks_groups_". $doedit .".". $txtImageFilenameExt;
			
			if (file_exists($filename_new)) {
				unlink($filename_new);
			}
			
			if (move_uploaded_file($txtImageFilenameTmp, $filename_new)) {
				$imagebasename = DIR_WS_IMAGES . basename($filename_new);
				//mysql_query("update infolinks_groups set infolinks_groups_bg_image='$imagebasename' where infolinks_groups_id='$doedit';");
				tep_db_query("UPDATE infolinks_groups SET infolinks_groups_bg_image='$imagebasename' WHERE infolinks_groups_id='$doedit'");
			}
		}
		
		// ---------------------------
		
		if ($deletetxtSeperator==1) {
            //mysql_query("update infolinks_groups set infolinks_groups_seperator_image='' where infolinks_groups_id='$doedit';");
            tep_db_query("UPDATE infolinks_groups SET infolinks_groups_seperator_image='' WHERE infolinks_groups_id='$doedit'");
		}
		
		if ($uploadtxtSeperator && $deletetxtSeperator!=1) {		
			
			$filename_new = $uploaddir."infolinks_groups_seperator_".$doedit.".".$txtSeperatorFilenameExt;
			
			if (file_exists($filename_new)) {
				unlink($filename_new);
			}
			
			if (move_uploaded_file($txtSeperatorFilenameTmp, $filename_new)) {
				$imagebasename=DIR_WS_IMAGES.basename($filename_new);
				//mysql_query("update infolinks_groups set infolinks_groups_seperator_image='$imagebasename' where infolinks_groups_id='$doedit';");
				tep_db_query("UPDATE infolinks_groups SET infolinks_groups_seperator_image='$imagebasename' WHERE infolinks_groups_id='$doedit'");
			}
		}
	}
}

if ($doaddlink > 0) {
	$infolinks_sql_data_array = array(	'language_id' => $info_lang,
										'infolinks_groups_id' => tep_db_prepare_input($doaddlink),
										'infolinks_title' => 'Untitled',
										'infolinks_image' => '',
										'infolinks_URL' => $file,
										'infolinks_new_window' => '',
										'infolinks_sort_order' => 50000,
										'infolinks_active' => 0,
										'infolinks_imageonly' => '',
		                                'infolinks_select' => '2',
		                                'infolinks_align' => 'default'
									);
	tep_db_perform(TABLE_INFOLINKS, $infolinks_sql_data_array);
	$lastid = tep_db_insert_id();
	$file = '';
	
	$infolinks_contents_sql_data_array = array(	'infolinks_id' => $lastid,
												'infolinks_contents_page' => 1
											);
	tep_db_perform(TABLE_INFOLINKS_CONTENTS, $infolinks_contents_sql_data_array);
	tep_redirect(tep_href_link('infolinks_content.php', "info_lang=". $info_lang ."&content_id=".$lastid));
	exit;
}

if ($addnewpage>0 && $addnewpage<=countPages($content_id)) {
	/*
	mysql_query("update infolinks_contents set infolinks_contents_page=infolinks_contents_page+1 where infolinks_contents_page>$addnewpage;");
	$addnewpage++;
	mysql_query("insert into infolinks_contents values('','$infolinks_id','','$addnewpage');");			
	*/

	tep_db_query("UPDATE infolinks_contents SET infolinks_contents_page=infolinks_contents_page+1 WHERE infolinks_id='". $content_id ."' AND infolinks_contents_page>$addnewpage;");
	$addnewpage++;

	$sql_data_array = array('infolinks_contents_id' => '',
							'infolinks_id' => $infolinks_id,
							'infolinks_contents' => '',
	                        'infolinks_contents_page' => $addnewpage);
	tep_db_perform(TABLE_INFOLINKS_CONTENTS, $sql_data_array);
} else if (countPages($content_id)==0) {
	//mysql_query("insert into infolinks_contents values('','$infolinks_id','','1');");

	$sql_data_array = array('infolinks_contents_id' => '',
							'infolinks_id' => $infolinks_id,
							'infolinks_contents' => '',
	                        'infolinks_contents_page' => '1');
	tep_db_perform(TABLE_INFOLINKS_CONTENTS, $sql_data_array);
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script src="includes/general.js"></script>
<script language="JavaScript" src="includes/javascript/jquery.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>

<table border="0" width="100%" cellspacing="2" cellpadding="2">
  <tr>
    <td width="<?php echo BOX_WIDTH; ?>" valign="top" height="27">
    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
    <?php require(DIR_WS_INCLUDES . 'column_left.php');?>
    </table></td>
     <td class="pageHeading" valign="top"><?php
       echo BOX_HEADING_INFOLINKS."<br>";
	   
$doedit=(int)$doedit;

if($doedit==0) {
	$doc_title="Add New Group";
	$doc_post=$_SERVER['PHP_SELF']."?".$_SERVER['QUERY_STRING']."&type=1";
	$doc_button="Submit";
} else {
    /*
	$res=mysql_query("select * from infolinks_groups where infolinks_groups_id='$doedit';");
	if(mysql_num_rows($res))
		$row=mysql_fetch_array($res);
    */

    $infolink_group_result= tep_db_query("SELECT * FROM infolinks_groups WHERE infolinks_groups_id='$doedit';");
    $row = tep_db_fetch_array($infolink_group_result);
    
	$doc_title="Update Group";
	$doc_post=$_SERVER['PHP_SELF']."?".$_SERVER['QUERY_STRING']."&type=2";
	$doc_button="Update";
}
if($showall>0)
{
?>
<font class='formAreaTitle'><?=$doc_title?></font><hr>
<table width="98%" border="0" align="center" cellpadding="0" cellspacing="0" class="main">

  <form enctype="multipart/form-data" name="frmUpdate" action="<? echo $doc_post;?>" method="post">
    <tr>
      <td width=20% height="34">Group Name: </td>
      <td><input name="txtTitle" type="text" id="txtTitle" value="<?=$row['infolinks_groups_title']?>" size="50">
        <input name="chkShowTitle" type="checkbox" id="chkShowTitle" value="1" checked <? if((int)$row['infolinks_groups_show_title']==1) echo "checked";?>>
        Show Group Name</td>
    </tr>
<?

if ($row['infolinks_groups_id']) {
	$group_page_arr = array ();

	$group_page_arr[] = array('id'=> "",'text'=> PULL_DOWN_DEFAULT);

	$infolink_select_sql = "	SELECT i.infolinks_id, c.infolinks_contents_id, c.infolinks_contents_page ,i.infolinks_title  
								FROM " . TABLE_INFOLINKS . " i, " . TABLE_INFOLINKS_CONTENTS . " c 
								WHERE i.infolinks_groups_id  = '".$row['infolinks_groups_id']."'
									AND i.infolinks_id = c.infolinks_id
								ORDER BY i.infolinks_title, c.infolinks_contents_page ASC";
	$infolink_result_sql = tep_db_query($infolink_select_sql);

	while ($infolink_result_row = tep_db_fetch_array($infolink_result_sql)) {
		$group_page_arr[] = array('id'=>$infolink_result_row['infolinks_id'],'text'=> strip_tags($infolink_result_row['infolinks_title']) .' Page '.$infolink_result_row['infolinks_contents_page']);
	}



?>
    <tr>
      <td height="32" valign="top"><?=TITLE_GROUP_MAIN_PAGE ?></td>
      <td>
	  	<?=tep_draw_pull_down_menu('group_main_page', $group_page_arr, $row['infolinks_groups_main_page_id'], ""); ?>
	  </td>
    </tr>
<?	
}
?>
    <tr>
      <td height="32" valign="top"><?=TITLE_GROUP_BACKGROUND_IMAGE?></td>
      <td><input name="txtImage" type="file" id="txtImage" size="50">
        <?
			if(trim($row['infolinks_groups_bg_image'])=="")
				echo "<br>(There is no background image uploaded yet for this group)<br><br></td></tr>";
			else
			{
				echo "<input type='checkbox' value='1' name='deletetxtImage'> Remove Group Background Image";
				echo "<br>(Current image: <a href='".HTTP_SERVER.$row['infolinks_groups_bg_image']."' target='_blank'>".DIR_FS_CATALOG.$row['infolinks_groups_bg_image']."</a>)<br><br></td></tr>";

			}
	?>
    <tr>
      <td height="38">Infolinks Seperator Image: </td>
      <td><input name="txtSeperator" type="file" id="txtSeperator" size="50">
        <?
			if(trim($row['infolinks_groups_seperator_image'])=="")
				echo "<br>(There is no seperator image uploaded yet for this group)<br><br></td></tr>";
			else
			{
				echo "<input type='checkbox' value='1' name='deletetxtSeperator'> Remove Seperator Image";
				echo "<br>(Current image: <a href='".HTTP_SERVER.$row['infolinks_groups_seperator_image']."' target='_blank'>".DIR_FS_CATALOG.$row['infolinks_groups_seperator_image']."</a>)<br><br></td></tr>";

			}
	?></td>
    </tr>
    <tr>
      <td height="38">Infolinks Alignment:</td>
      <td><select name="cmbAlignment" id="cmbAlignment">
	  <?
	  $sel=array("","","");
	  $tmp=trim($row['infolinks_groups_align']);
	  
	  if($tmp=="left")
	  {
	  $sel[0]="selected";
	  }
	  elseif($tmp=="right")
	  {
	  $sel[2]="selected";
	  }
	  else
	  {
	  $sel[1]="selected";
	  }	  
	  ?>
	  
        <option value="left" <?=$sel[0]?>>Left</option>
        <option value="center" <?=$sel[1]?>>Center</option>
        <option value="right" <?=$sel[2]?>>Right</option>
                                                      </select></td>
    </tr>
    <tr>
      <td height="30">Sort Order: </td>
	  <?
	  if($row['infolinks_groups_sort_order']=="")
	  	$sortOrder=50000;
	  else
	  	$sortOrder=(int)$row['infolinks_groups_sort_order'];
	  ?>
      <td><input name="txtSO" type="text" id="txtSO" value="<?=$sortOrder?>">
      </td>
    </tr>
    <tr>
      <td height="30">Group Is Active:</td>
      <td><input name="chkActive" type="checkbox" id="chkActive" value="1" checked <? if((int)$row['infolinks_groups_active']==1) echo "checked";?>></td>
    </tr>
    <tr>
      <td height="30">&nbsp;</td>
      <td>
	  	<input type="submit" name="Submit" value="<?=$doc_button?>">
	  	<input type="button" name="Cancel" onclick="document.location.href='<?=tep_href_link(FILENAME_INFOLINKS_INDEX,'selected_group_id='.$selected_group_id) ?>'" value="<?=BUTTON_CANCEL ?>">
	  </td>
    </tr>
	<?=tep_draw_hidden_field('infolinks_groups_parent_id', $add_into_group_id, ""); ?>
  </form>
</table>
<br>
<?
}
?>
<hr>
<table width="100%" align="center" border="0" cellpadding="5" cellspacing="2" class="main">
<tr>
	<td colspan="100%">Go To : <?=tep_draw_pull_down_menu('selected_group_id', $group_hierarchy_arr, $selected_group_id, 'id="selected_group_id" onchange="javascript:goto_groupid()"'); ?></td>
</tr>
<TR>
<TD class="reportBoxHeading"><font size=2><b>Current InfoLinks</b></font></TD>
<TD class="reportBoxHeading" align="center"><font size=2><b>Sort</b></font></TD>
<TD class="reportBoxHeading" align="center"><font size=2><b>Status</b></font></TD>
<TD class="reportBoxHeading" align=right><font size=2><b>Action</b></font></TD>
</TR>
<?
  /*
  $res_one=mysql_query("select * from infolinks_groups order by infolinks_groups_sort_order,infolinks_groups_title asc;");
  if(mysql_num_rows($res_one))
  {
  */
$parent_gorup_id_statement = "infolinks_groups_parent_id = '$selected_group_id'";
$infolinks_group_result = tep_db_query("SELECT infolinks_groups_id, infolinks_groups_active, infolinks_groups_title, infolinks_groups_sort_order
										FROM infolinks_groups 
										WHERE $parent_gorup_id_statement
										ORDER BY infolinks_groups_sort_order,infolinks_groups_title ASC");
if (tep_db_num_rows($infolinks_group_result)) {
  	while($row_one=tep_db_fetch_array($infolinks_group_result))
	{
		$group_id=(int)$row_one['infolinks_groups_id'];
		if((int)$row_one['infolinks_groups_active']!=1) {
			$grp_state="<a href=\"infolinks_index.php?info_lang=$info_lang&".SID."&do=activate&dotype=0&id=$group_id&showall=$showall&selected_group_id=$selected_group_id\"><img src=\"".DIR_WS_IMAGES."icon_status_green_light.gif\" border=\"0\" alt=\"Set Active\"></a> <img src='".DIR_WS_IMAGES."icon_status_red.gif' alt=\"Inactive\">";
		} else {
			$grp_state="<img src='".DIR_WS_IMAGES."icon_status_green.gif' alt=\"Active\"> <a href=\"infolinks_index.php?info_lang=$info_lang&".SID."&do=deactivate&dotype=0&id=$group_id&showall=$showall&selected_group_id=$selected_group_id\"><img src=\"".DIR_WS_IMAGES."icon_status_red_light.gif\" border=\"0\" alt=\"Set Inactive\"></a>";
		}
		
		$countitem++;
		
		if ($countitem % 2 == "1") {
			$tr_classname = "reportListingEven";
		}
		else {
			$tr_classname = "reportListingOdd";
		}
		
		echo '<tr class="'.$tr_classname.'" id="tr_moveto_group_id_'.$countitem.'"><td><a href="'.tep_href_link(FILENAME_INFOLINKS_INDEX, 'selected_group_id=' . $row_one['infolinks_groups_id']).'">'.tep_image(DIR_WS_ICONS . 'folder.gif', ICON_FOLDER , '' , '' , ' align="absmiddle" border="0"').'</a> <b><a href="'.tep_href_link(FILENAME_INFOLINKS_INDEX, 'selected_group_id=' . $row_one['infolinks_groups_id']).'">'.$row_one['infolinks_groups_title'].'</a></b></td><td align="center"><font class="dataTableContent">'.$row_one['infolinks_groups_sort_order'].'</font></td><td align="center">'.$grp_state.'</td><td align=right>Move To : '.tep_draw_pull_down_menu('moveto_group_id_'.$countitem, $group_hierarchy_arr, $selected_group_id, 'id="moveto_group_id_'.$countitem.'" onchange="javascript:moveto_groupid(\''.$group_id.'\',\'moveto_group_id_'.$countitem.'\')"').' | <a href="infolinks_index.php?'.SID.'&info_lang='.$info_lang.'&showall=1&selected_group_id='.$group_id.'&add_into_group_id='.$group_id.'">Add Group</a> | <a href="infolinks_index.php?'.SID.'&info_lang='.$info_lang.'&doaddlink='.$group_id.'" target="_blank">Add Link</a> <font size=1>|</font> <a href="infolinks_index.php?'.SID.'&showall=1&info_lang='.$info_lang.'&doedit='.$row_one['infolinks_groups_id'].'&selected_group_id='.$selected_group_id.'">Edit</a> <font size=1> | </font> <a href="infolinks_delete.php?'.SID.'&info_lang='.$info_lang.'&type=2&link_id='.$row_one['infolinks_groups_id'].'&selected_group_id='.$selected_group_id.'">Delete</a></td></tr>';
		
		/*
		$res_two=mysql_query("select * from infolinks where infolinks_groups_id='$group_id' order by infolinks_sort_order,infolinks_title asc;");
		if(mysql_num_rows($res_two)) {
			while($row_two=mysql_fetch_array($res_two))
			{
			    $lnk_id=(int)$row_two['infolinks_id'];
				if((int)$row_two['infolinks_active']!=1) {
					// inactive
					$lnk_state="<a href=\"infolinks_index.php?info_lang=$info_lang&".SID."&do=activate&dotype=1&id=$lnk_id&showall=$showall\"><img src=\"".DIR_WS_IMAGES."icon_status_green_light.gif\" alt=\"Set Active\" border=\"0\"></a> <img src='".DIR_WS_IMAGES."icon_status_red.gif' alt='Inactive'>";
				} else {
					$lnk_state="<img src='".DIR_WS_IMAGES."icon_status_green.gif' alt='Active'> <a href=\"infolinks_index.php?info_lang=$info_lang&".SID."&do=deactivate&dotype=1&id=$lnk_id&showall=$showall\"><img src=\"".DIR_WS_IMAGES."icon_status_red_light.gif\" border=\"0\" alt=\"Set Inactive\"></a>";
				}
				
						echo '<tr><td><font size=1 class="menuBoxContentLink"><a href="' . tep_href_link('infolinks_content.php', "info_lang=$row_two[language_id]&content_id=$row_two[infolinks_id]", 'NONSSL') . '" class="menuBoxContentLink">'.$row_two['infolinks_title'].'</a> '. populatePages($row_two['infolinks_id'],$row_two['infolinks_select'],$row_two['language_id'],$row_two['infolinks_id'],$total_pages) .' </font></td><td align="center"><font class="dataTableContent" align="center">'.$row_two['infolinks_sort_order'].'</font></td><td align="center">'.$lnk_state.'</td><td align="right"><font size=1>';if((int)$row_two['infolinks_select']!=1){ echo generateAddPageAfterMenu($total_pages).' | '; } echo '<a href="' . tep_href_link('infolinks_content.php', "info_lang=$row_two[language_id]&content_id=$row_two[infolinks_id]", 'NONSSL') . '" class="menuBoxContentLink">Update</a> | <a href="infolinks_delete.php?'.SID.'&info_lang='.$row_two[language_id].'&type=1&link_id='.$row_two[0].'">Delete</a></td></tr>';
			}
		}
		*/
	}  
  
  }
  
$infolink_result = tep_db_query("	SELECT * FROM infolinks 
									WHERE infolinks_groups_id='$selected_group_id' 
									ORDER BY infolinks_sort_order,infolinks_title ASC");
if (tep_db_num_rows($infolink_result)) {
	while($infolink_row = tep_db_fetch_array($infolink_result)) {
		$countitem++;
		
		if ($countitem % 2 == "1") {
			$tr_classname = "reportListingEven";
		}
		else {
			$tr_classname = "reportListingOdd";
		}

	    $lnk_id=(int)$infolink_row['infolinks_id'];
		if((int)$infolink_row['infolinks_active']!=1) {
			// inactive
			$lnk_state="<a href=\"infolinks_index.php?info_lang=$info_lang&".SID."&do=activate&dotype=1&id=$lnk_id&showall=$showall&selected_group_id=$selected_group_id\"><img src=\"".DIR_WS_IMAGES."icon_status_green_light.gif\" alt=\"Set Active\" border=\"0\"></a> <img src='".DIR_WS_IMAGES."icon_status_red.gif' alt='Inactive'>";
		} else {
			$lnk_state="<img src='".DIR_WS_IMAGES."icon_status_green.gif' alt='Active'> <a href=\"infolinks_index.php?info_lang=$info_lang&".SID."&do=deactivate&dotype=1&id=$lnk_id&showall=$showall&selected_group_id=$selected_group_id\"><img src=\"".DIR_WS_IMAGES."icon_status_red_light.gif\" border=\"0\" alt=\"Set Inactive\"></a>";
		}
		echo '<tr class="'.$tr_classname.'"><td><a href="' . tep_href_link('infolinks_content.php', "info_lang=". $infolink_row['language_id'] ."&content_id=". $infolink_row['infolinks_id'], 'NONSSL') . '">'.tep_image(DIR_WS_ICONS . 'preview.gif', ICON_PREVIEW ,'' , '' , ' align="absmiddle" border="0"').'</a> <font size=1 class="menuBoxContentLink"><a href="' . tep_href_link('infolinks_content.php', "info_lang=". $infolink_row['language_id'] ."&content_id=". $infolink_row['infolinks_id'], 'NONSSL') . '" class="menuBoxContentLink">'.$infolink_row['infolinks_title'].'</a> '. populatePages($infolink_row['infolinks_id'],$infolink_row['infolinks_select'],$infolink_row['language_id'],$infolink_row['infolinks_id'],$total_pages) .' </font></td><td align="center"><font class="dataTableContent" align="center">'.$infolink_row['infolinks_sort_order'].'</font></td><td align="center">'.$lnk_state.'</td><td align="right"><font size=1>';if((int)$infolink_row['infolinks_select']!=1){ echo generateAddPageAfterMenu($total_pages, $infolink_row).' | '; } echo '<a href="' . tep_href_link('infolinks_content.php', "info_lang=". $infolink_row['language_id'] ."&content_id=". $infolink_row['infolinks_id'], 'NONSSL') . '" class="menuBoxContentLink" target="_blank">Update</a> | <a href="infolinks_delete.php?'.SID.'&info_lang='.$info_lang.'&type=1&link_id='.$infolink_row['infolinks_id'].'">Delete</a></td></tr>';
	}
}
  
 ?>

</table>

<font class='formAreaTitle'><br><br>[<a href="infolinks_index.php?info_lang=<?=$info_lang?>&showall=1&selected_group_id=<?=$selected_group_id ?>&add_into_group_id=<?=$selected_group_id ?>">Add New Group</a>]</font><br><br>
<br>


<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>

<p></p>
<p></p></body>

<script language="javascript">

function goto_groupid () {
	var selected_group_id = jQuery("#selected_group_id").val();
	document.location.href='<?=FILENAME_INFOLINKS_INDEX ?>?selected_group_id='+selected_group_id;
}

function moveto_groupid (move_group_id,idname) {
	var moveto_group_id = jQuery("#"+idname).val();

	if (move_group_id == moveto_group_id) {
		alert("You can not move the group to itself.");
	}
	else {
		var respond = confirm ("Are you sure you want to move this group?");
		if (respond == true) {
			jQuery.post('<?=FILENAME_INFOLINKS_INDEX ?>?action=movegroup', {move_group_id : move_group_id , moveto_group_id : moveto_group_id} ,function(data){
				if (data == "success") {
					document.location.reload(true);
				}
				else {
					alert("You can not move inside the group's own sub group.");
				}
			});
		}
	}
}

</script>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>