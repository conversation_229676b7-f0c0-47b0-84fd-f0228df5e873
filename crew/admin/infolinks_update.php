<?php
require('includes/application_top.php');
$seo_url_alias_infolink_permission = tep_admin_files_actions(FILENAME_INFOLINKS_CONTENT, 'SEO_URL_ALIAS_INFOLINK');

$content_id = (int)$_GET['content_id'];
$info_lang = (int)$_GET['info_lang'];

$txtSO = (int)$txtSO;
$chkLinkActive = (int)$chkLinkActive;
$chkImageOnly = (int)$chkImageOnly;
$type = (int)$type;
$newWindow = (int)$chkNewWin;
$page_id = (int)$_GET['page_id'];
$radContent = (int)$radContent;
$delimg = (int)$deleteimg;
$pgNo = (int)$pgNo;
$delpage = (int)$delpage;

if ($delpage == 1) {
	mysql_query("delete from infolinks_contents where infolinks_contents_id='$page_id';");
	
	mysql_query("update infolinks_contents set infolinks_contents_page=infolinks_contents_page-1 where infolinks_id='$content_id' and infolinks_contents_page>$pgNo;");
	
	header("Location: infolinks_content.php?".SID."&info_lang=$info_lang&content_id=$content_id");
	exit;
}

if (sizeof($_FILES) > 0) {
	$uploaddir = DIR_FS_CATALOG.DIR_WS_IMAGES;
	$filename = strtolower(trim(basename($_FILES['txtImage']['name'])));
	$filename_tmp = $_FILES['txtImage']['tmp_name'];
	
	$filesize = (int)$_FILES['txtImage']['size'];
	$parts = explode(".", $filename);
	$uploadfile = $uploaddir . $filename;
	$allowed = array("gif", "jpg", "png", "bmp");
	
	if (sizeof($parts) > 0) {
		$ext = $parts[sizeof($parts)-1];
	} else {
		$ext = '';
	}
	
	if ($ext != '' && $filesize>0) {
		if ($ext==$allowed[0] || $ext==$allowed[1] || $ext==$allowed[2] || $ext==$allowed[3]) {
			$upload = true;	
			$filename_new = $uploaddir."infolinks_".$content_id.".".$ext;
		} else {
			$upload = false;
		}
	} else {
		$upload = false;
	}
} else {
	$upload = false;
}

if ($page_id > 0) {
	$content_text = mysql_escape_string($content_text);
	mysql_query("update infolinks_contents set infolinks_contents='$content_text' where infolinks_contents_id='$page_id';");
} else {
	$all_cat_accessible = true;
	
	if (count($HTTP_POST_VARS['infolinks_cat_id'])) {
		$eligible_categories_array = tep_get_eligible_categories(FILENAME_INFOLINKS_INDEX, $eligible_categories_array, 0, true);
		
		$unauth_cat_array = array_diff($HTTP_POST_VARS['infolinks_cat_id'], $eligible_categories_array);
		if (count($unauth_cat_array)) {
			$all_cat_accessible = false;
			$messageStack->add_session(ERROR_CAT_ACCESS_DENIED, 'error');
		}
	}
	
	//validate infolinks_url_alias
	if ($seo_url_alias_infolink_permission) {
		include_once(DIR_WS_CLASSES . 'seo.php');
		$seo_url = new seo_url();
		
		if (tep_not_null($infolinks_url_alias)) {
			$info_changed_infolinks_url_alias = $seo_url->tep_translate_special_character($infolinks_url_alias);
			$info_changed_infolinks_url_alias = $seo_url->tep_validate_special_characters($info_changed_infolinks_url_alias);
			
			if ($infolinks_url_alias != $info_changed_infolinks_url_alias) {
				$messageStack->add_session(sprintf('Warning: The SEO URL Alias changed FROM %s TO %s!', $infolinks_url_alias, $info_changed_infolinks_url_alias), 'warning');
			}
		} else { //if empty
			$info_changed_infolinks_url_alias = $seo_url->tep_translate_special_character($txtTitle);
			$info_changed_infolinks_url_alias = $seo_url->tep_validate_special_characters($info_changed_infolinks_url_alias);
			
			$messageStack->add_session(sprintf('Warning: The SEO URL Alias changed FROM %s TO %s!', $txtTitle, $info_changed_infolinks_url_alias), 'warning');
		}
	} else {
		$info_changed_infolinks_url_alias = $infolinks_url_alias;
	}
	
	if ($all_cat_accessible) {
		if ($radContent == 1) {
			$infolinks_sql_data_array = array(	'infolinks_title' => tep_db_prepare_input($txtTitle),
												'infolinks_url_alias' => $info_changed_infolinks_url_alias,
												'infolinks_imageonly' => tep_db_prepare_input($chkImageOnly),
												'infolinks_URL' => tep_db_prepare_input($txtURL),
				                        		'infolinks_sort_order' => tep_db_prepare_input($txtSO),
				                                'infolinks_active' => tep_db_prepare_input($chkLinkActive),
				                                'infolinks_select' => '1',
				                                'infolinks_new_window' => tep_db_prepare_input($newWindow),
				                                'infolinks_align' => tep_db_prepare_input($cmbAlignment),
				                                'infolinks_cat_id' => isset($HTTP_POST_VARS['infolinks_cat_id']) && count($HTTP_POST_VARS['infolinks_cat_id']) ? tep_db_prepare_input(implode(',',$HTTP_POST_VARS['infolinks_cat_id'])) : '',
				                                'infolinks_include_subcat' => tep_db_prepare_input($HTTP_POST_VARS['include_subcategory']),
				                                'infolinks_right_navigation' => (int)$_POST['right_navigation'],
				                                'infolinks_groups_id' => (int)$_POST['infolinks_groups_id'],
				                                'infolinks_back_button' => (int)$_POST['infolinks_back_button']
											);
			tep_db_perform(TABLE_INFOLINKS, $infolinks_sql_data_array, 'update', "language_id='".tep_db_input($info_lang)."' and infolinks_id='".tep_db_input($content_id)."'");
		} else {
			
			$do_url_name = false;
			// check existing infolink url name
			if ($seo_url_alias_infolink_permission) {
				$infolinks_url_name_check_select_sql = "SELECT infolinks_url_alias FROM ". TABLE_INFOLINKS ."
														WHERE infolinks_url_alias='". $info_changed_infolinks_url_alias ."'
															AND infolinks_id<>'". tep_db_input($content_id) ."'";
				$infolinks_url_name_check_result_sql = tep_db_query($infolinks_url_name_check_select_sql);
				
				if (tep_db_num_rows($infolinks_url_name_check_result_sql) > 0) {
					$messageStack->add_session("Warning: The Infolinks URL Name is duplicate!", 'warning'); // warning message
				} else {
					$infolinks_url_name_array = array ('infolinks_url_alias' => $info_changed_infolinks_url_alias);
					$do_url_name = true;
				}
				
				$exist_infolinks_url_name_select_sql = "SELECT infolinks_url_alias FROM ". TABLE_INFOLINKS ."
													WHERE infolinks_id='". tep_db_input($content_id) ."'";
				$exist_infolinks_url_name_result_sql = tep_db_query($exist_infolinks_url_name_select_sql);
				$exist_infolinks_url_name_row = tep_db_fetch_array($exist_infolinks_url_name_result_sql);
				
				if ($exist_infolinks_url_name_row['infolinks_url_alias'] != $info_changed_infolinks_url_alias) { //check existing infolinks_url_alias
					$info_changed_history_array = array('info_changed_history_type' => 'infolinks',
			  											'info_changed_history_type_id' => tep_db_prepare_input($content_id),
			  											'info_changed_history_remark' => $info_changed_infolinks_url_alias,
			  											'info_changed_history_date_added' => 'now()',
			  											'info_changed_history_added_by' => $login_email_address
			  											);
			  		
			  		tep_db_perform(TABLE_INFO_CHANGED_HISTORY, $info_changed_history_array);
			  	}
			}
		  	
		  	if (mysql_num_rows(mysql_query("select * from infolinks_contents where infolinks_id='$content_id';")) < 1) {
				$sql_data_array = array('infolinks_contents_id' => '',
										'infolinks_id' => $content_id,
										'infolinks_contents' => '',
				                        'infolinks_contents_page' => '1');
				tep_db_perform(TABLE_INFOLINKS_CONTENTS, $sql_data_array);
			}
			
			$infolinks_sql_data_array = array(	'infolinks_title' => tep_db_prepare_input($txtTitle),
												'infolinks_imageonly' => tep_db_prepare_input($chkImageOnly),
												'infolinks_URL' => '',
				                        		'infolinks_sort_order' => tep_db_prepare_input($txtSO),
				                                'infolinks_active' => tep_db_prepare_input($chkLinkActive),
				                                'infolinks_select' => '2',
				                                'infolinks_new_window' => tep_db_prepare_input($newWindow),
				                                'infolinks_align' => tep_db_prepare_input($cmbAlignment),
				                                'infolinks_cat_id' => isset($HTTP_POST_VARS['infolinks_cat_id']) && count($HTTP_POST_VARS['infolinks_cat_id']) ? tep_db_prepare_input(implode(',',$HTTP_POST_VARS['infolinks_cat_id'])) : '',
				                                'infolinks_include_subcat' => tep_db_prepare_input($HTTP_POST_VARS['include_subcategory']),
				                                'infolinks_right_navigation' => (int)$_POST['right_navigation'],
				                                'infolinks_groups_id' => (int)$_POST['infolinks_groups_id'],
				                                'infolinks_back_button' => (int)$_POST['infolinks_back_button']
											);
			if ($do_url_name == true) {
				$infolinks_sql_data_array = array_merge($infolinks_sql_data_array, $infolinks_url_name_array);
			}
			tep_db_perform(TABLE_INFOLINKS, $infolinks_sql_data_array, 'update', "language_id='".tep_db_input($info_lang)."' and infolinks_id='".tep_db_input($content_id)."'");
		}
	}
}

if ($delimg==1)	{
	mysql_query("update infolinks set infolinks_image='' where language_id='$info_lang' and infolinks_id='$content_id';");
}

if ($upload && $delimg!=1) {
	if (file_exists($filename_new)) 	unlink($filename_new);
	
	if (move_uploaded_file($filename_tmp, $filename_new)) {
		$imagebasename=DIR_WS_IMAGES.basename($filename_new);
		mysql_query("update infolinks set infolinks_image='$imagebasename' where language_id='$info_lang' and infolinks_id='$content_id';");
	}
}

tep_redirect(tep_href_link('infolinks_content.php', "info_lang=$info_lang&content_id=$content_id&page_id=".(int)$page_id));

require(DIR_WS_INCLUDES . 'application_bottom.php'); 
?>