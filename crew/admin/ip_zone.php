<?php
require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');
$subaction = (isset($HTTP_GET_VARS['subaction']) ? $HTTP_GET_VARS['subaction'] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "delete_zone":
			$zone_ip_delete_sql = "	DELETE FROM " . TABLE_IP_TO_DEFINED_IP_ZONES . " 
									WHERE defined_ip_zones_id = '" . tep_db_input($_REQUEST['gID']) . "'";
			tep_db_query($zone_ip_delete_sql);
			
			$zone_delete_sql = "	DELETE FROM " . TABLE_DEFINED_IP_ZONES . " 
									WHERE defined_ip_zones_id = '" . tep_db_input($_REQUEST['gID']) . "'";
			tep_db_query($zone_delete_sql);
			
			$messageStack->add_session('The zone has been successfully deleted!', 'success');
			
			tep_redirect(tep_href_link(FILENAME_IP_ZONE, isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] : ''));
			
			break;
		case "delete_ip":
			$zone_ip_delete_sql = "	DELETE FROM " . TABLE_IP_TO_DEFINED_IP_ZONES . " 
									WHERE ip_to_defined_ip_zones_id = '" . tep_db_input($_REQUEST['ipID']) . "'";
			tep_db_query($zone_ip_delete_sql);
			
			$messageStack->add_session('The IP has been successfully deleted!', 'success');
			
			tep_redirect(tep_href_link(FILENAME_IP_ZONE, isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] : ''));
			
			break;
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
		case "insert_zone":
		case "update_zone":
			$error = false;
			
			$defined_zones_description = tep_db_prepare_input($HTTP_POST_VARS['zones_description']);
			
            if ($subaction == "insert_zone") {
            	$zone_name = ucwords(strtolower(tep_db_prepare_input($HTTP_POST_VARS['zone_name'])));
            	$name_replace = ereg_replace_dep(" ", "", strtolower($zone_name));
	        	
	        	if (($zone_name == '' || NULL)) {
	          		$messageStack->add(TEXT_INFO_ZONE_NAME_FALSE);
					$error = true;
	        	} else {
	        		$check_name_query = tep_db_query("select defined_ip_zones_name from " . TABLE_DEFINED_IP_ZONES . " where LOWER(REPLACE(defined_ip_zones_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
	          		$check_duplicate = tep_db_num_rows($check_name_query);
	          		if ($check_duplicate > 0){
	          			$messageStack->add(TEXT_INFO_ZONE_NAME_USED);
						$error = true;
	          		} else {
	            		$sql_data_array = array('defined_ip_zones_name' => $zone_name,
	            								'defined_ip_zones_description' => $defined_zones_description,
	            								'defined_ip_zones_date_added' => 'now()');
	            		tep_db_perform(TABLE_DEFINED_IP_ZONES, $sql_data_array);
						
						$messageStack->add_session('Zone : '.$HTTP_POST_VARS["zone_name"].' has been successfully created!', 'success');
	          		}
	        	}
			} else {
				$zone_name = ucwords(strtolower(tep_db_prepare_input($HTTP_POST_VARS['zone_name'])));
				$name_replace = ereg_replace_dep(" ", "", strtolower($zone_name));
	        	
	        	if (($zone_name == '' || NULL)) {
	          		$messageStack->add(TEXT_INFO_ZONE_NAME_FALSE);
					$error = true;
	        	} else {
	        		$check_groups_name_query = tep_db_query("select defined_ip_zones_name from " . TABLE_DEFINED_IP_ZONES . " where defined_ip_zones_id <> " . (int)$HTTP_POST_VARS['gID'] . " and LOWER(REPLACE(defined_ip_zones_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
	          		$check_duplicate = tep_db_num_rows($check_groups_name_query);
	          		if ($check_duplicate > 0){
	            		$messageStack->add(TEXT_INFO_ZONE_NAME_USED);
						$error = true;
	          		} else {
	            		tep_db_query("update " . TABLE_DEFINED_IP_ZONES . " set defined_ip_zones_name = '" . tep_db_input($zone_name) . "', defined_ip_zones_description = '" . $defined_zones_description . "', defined_ip_zones_last_modified = now() where defined_ip_zones_id = '" . (int)$HTTP_POST_VARS['gID'] . "'");
	            		$messageStack->add_session('Zone : ' . $zone_name . ' has been successfully updated!', 'success');
	          		}
	        	}
			}
			
			if (!$error) {
				tep_redirect(tep_href_link(FILENAME_IP_ZONE, isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] : ''));
			}
			break;
		case "insert_ip":
		case "update_ip":
			$ip_address_array = array();
			
			for ($i=0; $i < 4; $i++) {
				if ((int)$HTTP_POST_VARS['ip_address'][$i] >= 0 && (int)$HTTP_POST_VARS['ip_address'][$i] <= 255) {
					$ip_address_array[$i] = (int)$HTTP_POST_VARS['ip_address'][$i];
				} else {
					$ip_address_array[$i] = '0';
				}
			}
			
			$ip_data_array = array(	'defined_ip_zones_id' => tep_db_prepare_input($HTTP_POST_VARS['gID']),
									'ip_address' => implode('.', $ip_address_array),
									'subnet' => tep_db_prepare_input($HTTP_POST_VARS["subnet"])
									);
          	
            if ($subaction == "insert_ip") {
            	$ip_insert_data_array = array ('ip_to_defined_ip_zones_date_added' => 'now()');
				$sql_data_array = array_merge($ip_data_array, $ip_insert_data_array);
				
				tep_db_perform(TABLE_IP_TO_DEFINED_IP_ZONES, $sql_data_array);
			} else {
				$ip_update_data_array = array ('ip_to_defined_ip_zones_last_modified' => 'now()');
				$sql_data_array = array_merge($ip_data_array, $ip_update_data_array);
				
				tep_db_perform(TABLE_IP_TO_DEFINED_IP_ZONES, $sql_data_array, 'update', ' ip_to_defined_ip_zones_id="'.(int)$_REQUEST["ipID"].'"');
			}
			
			tep_redirect(tep_href_link(FILENAME_IP_ZONE, isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] : ''));
			
			break;
	}
}

$mac_status_array = array(	'1'=> array('name' => 'icon_status_green', 'alt' => IMAGE_ICON_STATUS_GREEN, 'alt2' => IMAGE_ICON_STATUS_GREEN_LIGHT),
							'2'=>  array('name' => 'icon_status_yellow', 'alt' => IMAGE_ICON_STATUS_YELLOW, 'alt2' => IMAGE_ICON_STATUS_YELLOW_LIGHT),
							'0'=> array('name' => 'icon_status_red', 'alt' => IMAGE_ICON_STATUS_RED, 'alt2' => IMAGE_ICON_STATUS_RED_LIGHT)
						);

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->    
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<!-- body_text //-->
					<tr>
            			<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
if ($action == "new_zone" || $action == "edit_zone") {
	echo tep_draw_form('zone_form', FILENAME_IP_ZONE, tep_get_all_get_params(array('subaction')) . 'subaction='.($action=="new_zone" ? 'insert_zone' : 'update_zone'), 'post', 'onSubmit="return zone_form_checking();"');
	if ($_REQUEST["gID"]) {
		$zone_select_sql = "	SELECT * 
								FROM " . TABLE_DEFINED_IP_ZONES . " 
								WHERE defined_ip_zones_id='" . $_REQUEST["gID"] . "'";
		
		$zone_result_sql = tep_db_query($zone_select_sql);
		$zone_row = tep_db_fetch_array($zone_result_sql);
		echo tep_draw_hidden_field("gID", $_REQUEST["gID"]);
	}
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_ZONE_NAME?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('zone_name', $zone_row["defined_ip_zones_name"], ' id="zone_name"')?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			      				</tr>
			      				<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_ZONE_DESCRIPTION?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('zones_description', $zone_row["defined_ip_zones_description"], ' size="50" id="zones_description"')?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			      				</tr>
								<tr>
									<td class="main"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
									<td align="left">
										<?=($action=="new_zone" ? tep_submit_button(IMAGE_INSERT, IMAGE_INSERT, 'name="submitBtn"') : tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="submitBtn"')) . '&nbsp;&nbsp;' . tep_button(IMAGE_CANCEL, IMAGE_CANCEL, tep_href_link(FILENAME_IP_ZONE))?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        		</form>
        		<script>
					<!--
					function zone_form_checking() {
						if (document.getElementById('zone_name').value == "") {
							alert('Please enter zone name!');
							document.getElementById('zone_name').focus();
							return false;
						}
						
						document.zone_form.submitBtn.disabled = true;
						document.zone_form.submitBtn.className = 'disabledSubmitBtn';
						document.zone_form.submitBtn.value = 'Please wait...';
						
						return true;
					}
					//-->
				</script>
<?
} else if ($action == "new_ip" || $action == "edit_ip") {
	echo tep_draw_form('ip_form', FILENAME_IP_ZONE, tep_get_all_get_params(array('subaction')) . 'subaction='.($action=="new_ip" ? 'insert_ip' : 'update_ip'), 'post', 'onSubmit="return ip_form_checking();"');
	$zone_id = 0;
	
	if ($_REQUEST["ipID"]) {
		$ip_select_sql = "	SELECT * 
							FROM " . TABLE_IP_TO_DEFINED_IP_ZONES . " 
							WHERE ip_to_defined_ip_zones_id='" . tep_db_input($_REQUEST["ipID"]) . "'";
		
		$ip_result_sql = tep_db_query($ip_select_sql);
		$ip_row = tep_db_fetch_array($ip_result_sql);
		
		$ip_list_array = explode('.', $ip_row['ip_address']);
		
		$zone_id = $ip_row["defined_ip_zones_id"];
		
		echo tep_draw_hidden_field("ipID", $_REQUEST["ipID"]);
	} else {
		$zone_id = $_REQUEST["gID"];
	}
	
	$zone_select_sql = "SELECT defined_ip_zones_name 
						FROM " . TABLE_DEFINED_IP_ZONES . " 
						WHERE defined_ip_zones_id='" . tep_db_input($zone_id) . "'";
	
	$zone_result_sql = tep_db_query($zone_select_sql);
	$zone_row = tep_db_fetch_array($zone_result_sql);
	echo tep_draw_hidden_field("gID", $zone_id);
	
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_ZONE_NAME?></td>
									<td class="main" valign="top"><?=$zone_row["defined_ip_zones_name"]?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			      				</tr>
			      				<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_NETWORK_ADDRESS?></td>
									<td class="main" valign="top">
									<?
										for ($ip_cnt=0; $ip_cnt < 4; $ip_cnt++) {
											echo tep_draw_input_field('ip_address[]', isset($ip_list_array) ? $ip_list_array[$ip_cnt] : '0', ' size="2" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && (!validateInteger(trim_str(this.value)) || this.value < 0 || this.value > 255) ) ) { this.value = \'\'; } "') . ($ip_cnt < 3 ? ' . ' : '');
										}
										
										echo ' / ' . tep_draw_input_field('subnet', tep_not_null($ip_row['subnet']) ? $ip_row['subnet'] : '32', ' size="2" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && (!validateInteger(trim_str(this.value)) || this.value < 0 || this.value > 32) ) ) { this.value = \'\'; } "');
									?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
			      				</tr>
								<tr>
									<td class="main"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
									<td align="left">
										<?=($action=="new_ip" ? tep_submit_button(IMAGE_INSERT, IMAGE_INSERT, 'name="submitBtn"') : tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="submitBtn"')) . '&nbsp;&nbsp;' . tep_button(IMAGE_CANCEL, IMAGE_CANCEL, tep_href_link(FILENAME_IP_ZONE))?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        		</form>
        		<script>
					<!--
					function zone_form_checking() {
						if (document.getElementById('zone_name').value == "") {
							alert('Please enter zone name!');
							document.getElementById('zone_name').focus();
							return false;
						}
						
						document.zone_form.submitBtn.disabled = true;
						document.zone_form.submitBtn.className = 'disabledSubmitBtn';
						document.zone_form.submitBtn.value = 'Please wait...';
						
						return true;
					}
					//-->
				</script>
<?
} else {
	$zone_select_sql = "SELECT defined_ip_zones_id, defined_ip_zones_name, defined_ip_zones_description 
						FROM " . TABLE_DEFINED_IP_ZONES . " 
						ORDER BY defined_ip_zones_name ";
	
	$zone_result_sql = tep_db_query($zone_select_sql);
	if (tep_db_num_rows($zone_result_sql)) {
?>
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
					<tr>
            			<td valign="top">
            				<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
			   						<td width="5%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_ZONE_NAME?></td>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_ZONE_DESCRIPTION?></td>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_NETWORK_SETTING?></td>
								</tr>
<?
		$row_count = 0;
		while ($zone_row = tep_db_fetch_array($zone_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top" nowrap>
										<a href="<?=tep_href_link(FILENAME_IP_ZONE, 'action=edit_zone&gID='.$zone_row["defined_ip_zones_id"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
										<a href="javascript:void(confirm_delete('<?=$zone_row["defined_ip_zones_name"]?>', '', '<?=tep_href_link(FILENAME_IP_ZONE, 'action=delete_zone&gID='.$zone_row["defined_ip_zones_id"])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
									</td>
									<td class="reportRecords" valign="top"><?=$zone_row["defined_ip_zones_name"]?></td>
									<td class="reportRecords" valign="top"><?=$zone_row["defined_ip_zones_description"]?></td>
									<td class="reportRecords" valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
			          							<td colspan="2"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
			          						</tr>
			               					<tr>
			               						<td class="subRecordsBoxHeading" valign="top" width="8%">
													[<a href="<?=tep_href_link(FILENAME_IP_ZONE, 'action=new_ip&gID='.$zone_row["defined_ip_zones_id"])?>">Add New</a>]
												</td>
												<td class="subRecordsBoxHeading" width="12%"><?=TABLE_HEADING_NETWORK_ADDRESS?></td>
						   					</tr>
						   					<tr>
			          							<td colspan="2"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px"></div></td>
			          						</tr>
<?
			$zones_ip_select_sql = "SELECT ip_to_defined_ip_zones_id, ip_address, subnet 
									FROM " . TABLE_IP_TO_DEFINED_IP_ZONES . " 
									WHERE defined_ip_zones_id = '" . tep_db_input($zone_row['defined_ip_zones_id']) . "'";
			
			$zones_ip_result_sql = tep_db_query($zones_ip_select_sql);
			
			while ($zones_ip_row = tep_db_fetch_array($zones_ip_result_sql)) {
				echo '						<tr>
												<td class="reportRecords" width="20%" valign="top" nowrap>
													<a href="'.tep_href_link(FILENAME_IP_ZONE, 'action=edit_ip&ipID='.$zones_ip_row["ip_to_defined_ip_zones_id"]).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>
													<a href="javascript:void(confirm_delete(\''.$zones_ip_row["ip_address"].'\', \'\', \''.tep_href_link(FILENAME_IP_ZONE, 'action=delete_ip&ipID='.$zones_ip_row["ip_to_defined_ip_zones_id"]).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"').'</a>
												</td>
												<td width="100%" class="reportRecords" valign="top">'.$zones_ip_row["ip_address"].'/'.$zones_ip_row["subnet"].'</td>
											</tr>';
			}
?>
										</table>
									</td>
								</tr>
<?			$row_count++;
		}
?>
							</table>
			   			</td>
			   		</tr>
<?	} ?>
					<tr>
						<td><?='[ <a href="'.tep_href_link(FILENAME_IP_ZONE, 'action=new_zone').'" class="actionLink">'.LINK_ADD_ZONE.'</a> ]'?></td>
					</tr>
<?
}
?>
					
          		</table>
          	</td>
		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>