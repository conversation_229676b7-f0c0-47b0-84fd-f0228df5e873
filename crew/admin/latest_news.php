<? 
/*
  	$Id: latest_news.php,v 1.36 2011/07/13 08:39:41 sionghuat.chng Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 Will Mays
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require_once('includes/functions/image_upload.php');
require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

$aws_obj = new ogm_amazon_ws();
$aws_obj->set_bucket_key('BUCKET_STATIC');
$aws_obj->set_filepath('images/news/');

$seo_url_alias_latest_news_permission = tep_admin_files_actions(FILENAME_LATEST_NEWS, 'SEO_URL_ALIAS_LATEST_NEWS');

$latest_news_grp_select_array = array();
$latest_news_grp_array = array();

$latest_news_grp_select_sql = "	SELECT news_groups_id, news_groups_name 
								FROM " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION . " 
								WHERE language_id = ". $_SESSION['languages_id'] .""; 
$latest_news_grp_result_sql = tep_db_query($latest_news_grp_select_sql);
while ($latest_news_grp_row = tep_db_fetch_array($latest_news_grp_result_sql)) {
	$latest_news_grp_array[$latest_news_grp_row["news_groups_id"]] = $latest_news_grp_row["news_groups_name"];
	$latest_news_grp_select_array[] = array("id"=>$latest_news_grp_row["news_groups_id"], "text" => $latest_news_grp_row["news_groups_name"]);
}

$latest_news_prodtype_array = array();
$latest_news_prodtype_select_sql = "SELECT cl.custom_products_type_child_id, cl.custom_products_type_child_name 
									FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG . " as cl
									INNER JOIN " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . " as c 
										ON c.custom_products_type_child_id=cl.custom_products_type_child_id
									WHERE cl.languages_id = " . $_SESSION['languages_id'] . "
									ORDER BY c.sort_order";
$latest_news_prodtype_result_sql = tep_db_query($latest_news_prodtype_select_sql);
while ($latest_news_prodtype_row = tep_db_fetch_array($latest_news_prodtype_result_sql)) {
	$latest_news_prodtype_array[$latest_news_prodtype_row["custom_products_type_child_id"]] = $latest_news_prodtype_row["custom_products_type_child_name"];
}

$latest_news_grp_id = isset($_REQUEST["grp_id"]) ? (int)$_REQUEST["grp_id"] : 0;

if ($latest_news_grp_id) {
	$latest_news_top_title = $latest_news_grp_array[$latest_news_grp_id];
	$sql_where_str = "where news_groups_id = '".$latest_news_grp_id."' and language_id = '1'";
} else {
	$latest_news_top_title = PULL_DOWN_DEFAULT;
	$sql_where_str = '';
}

if ($_GET['action']) {
	switch ($_GET['action']) {
      	case 'setflag': //set the status of a news item.
        	if ( ($_GET['flag'] == '0') || ($_GET['flag'] == '1') ) {
          		if ($_GET['latest_news_id']) {
            		tep_db_query("UPDATE " . TABLE_LATEST_NEWS . " SET status = '" . $_GET['flag'] . "' WHERE news_id = '" . $_GET['latest_news_id'] . "'");
          		}
        	}
			
        	tep_redirect(tep_href_link(FILENAME_LATEST_NEWS, 'grp_id='.$latest_news_grp_id));
        	break;
      	case 'delete_latest_news_confirm': //user has confirmed deletion of news article.
        	if ($_POST['latest_news_id']) {
          		$latest_news_id = tep_db_prepare_input($_POST['latest_news_id']);
          		$languages = tep_get_languages();
          		for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) {
					$aws_obj->delete_file($latest_news_id .'_1_'.$languages[$lang_cnt]['id'].'.jpg');
					$aws_obj->delete_file($latest_news_id .'_2_'.$languages[$lang_cnt]['id'].'.jpg');
					$aws_obj->delete_file($latest_news_id .'_3_'.$languages[$lang_cnt]['id'].'.jpg');
					if (file_exists($uploaddir. "news/". $latest_news_id ."_1_".$languages[$lang_cnt]['id'].".jpg")) {
						unlink($uploaddir. "news/". $latest_news_id ."_1_".$languages[$lang_cnt]['id'].".jpg");
					}
					
					if (file_exists($uploaddir. "news/". $latest_news_id ."_2_".$languages[$lang_cnt]['id'].".jpg")) {
						unlink($uploaddir. "news/". $latest_news_id ."_2_".$languages[$lang_cnt]['id'].".jpg");
					}
	
					if (file_exists($uploaddir. "news/". $latest_news_id ."_3_".$languages[$lang_cnt]['id'].".jpg")) {
						unlink($uploaddir. "news/". $latest_news_id ."_3_".$languages[$lang_cnt]['id'].".jpg");
					}
				}
          		tep_db_query("DELETE FROM " . TABLE_LATEST_NEWS . " WHERE news_id = '" . tep_db_input($latest_news_id) . "'");
          		tep_db_query("DELETE FROM " . TABLE_LATEST_NEWS_DESCRIPTION . " WHERE news_id = '" . tep_db_input($latest_news_id) . "'");
          		tep_db_query("DELETE FROM " . TABLE_INFO_CHANGED_HISTORY . " WHERE info_changed_history_type_id = '" . tep_db_input($latest_news_id) . "' AND info_changed_history_type='". TEXT_INFO_CHANGED_HISTORY_NEWS ."'"); //delete info changed history
        		tep_db_query("DELETE FROM ".TABLE_LATEST_NEWS_CATEGORIES." WHERE news_id = '" . tep_db_input($latest_news_id) . "'");
        		
        	}
        	tep_redirect(tep_href_link(FILENAME_LATEST_NEWS, 'grp_id='.$latest_news_grp_id));
        	break;
      	case 'insert_latest_news': //insert a new news article.
      		for ($i=1;$i<=count($_POST['headline_tab']);$i++) {
	      		if ($_POST['headline_tab'][$i] == '') {
	      			$_POST['headline_tab'][1] = $_POST['headline_tab'][$i+1];
	      		} else {
	      			break;
	      		}
	  		}
	        
        	if (tep_not_null($_POST['headline_tab'][1])) {	// Must have headline in EN to enable listing
        		$all_cat_accessible = true;
     			
        		if (tep_not_null($_POST['news_groups_cat_id'])) {
        			if (isset($_POST['check_all_top'])) {
        				$unauth_cat_array = tep_get_ineligible_categories(FILENAME_LATEST_NEWS, $eligible_categories_array, 0, true);
        			} else {
	        			$eligible_categories_array = tep_get_eligible_categories(FILENAME_LATEST_NEWS, $eligible_categories_array, 0, true);
						$unauth_cat_array = array_diff($_POST['news_groups_cat_id'], $eligible_categories_array);
					}
	        		
					if (tep_not_null($unauth_cat_array)) {
						$all_cat_accessible = false;
						$messageStack->add_session(ERROR_CAT_ACCESS_DENIED, 'error');
					}
				}
				
				//validate infolinks_url_alias
				if ($seo_url_alias_latest_news_permission) {
					include_once(DIR_WS_CLASSES . 'seo.php');
					$seo_url = new seo_url();
					
					if (tep_not_null($_POST['latest_news_url_alias'])) {
						$info_changed_latest_news_url_alias = $seo_url->tep_translate_special_character($_POST['latest_news_url_alias']);
						$info_changed_latest_news_url_alias = $seo_url->tep_validate_special_characters($info_changed_latest_news_url_alias);

						if ($_POST['latest_news_url_alias'] != $info_changed_latest_news_url_alias) {
							$messageStack->add_session(sprintf(WARNING_LATEST_NEWS_URL_ALIAS_CHANGED, $_POST['latest_news_url_alias'], $info_changed_latest_news_url_alias), 'warning');
						}
					} else { //if empty
						$info_changed_latest_news_url_alias = $seo_url->tep_translate_special_character($_POST['latest_news_url_alias']);
						$info_changed_latest_news_url_alias = $seo_url->tep_validate_special_characters($info_changed_latest_news_url_alias);
						
						$messageStack->add_session(sprintf(WARNING_LATEST_NEWS_URL_ALIAS_CHANGED, $_POST['headline_tab'], $info_changed_latest_news_url_alias), 'warning');
					}
					
					// check existing url name 
					$url_name_check_select_sql = "	SELECT latest_news_url_alias 
													FROM ". TABLE_LATEST_NEWS ."
													WHERE latest_news_url_alias='". $info_changed_latest_news_url_alias ."'
														AND news_groups_id='". $_POST['news_groups_id'] ."'";
					$url_name_check_result_sql = tep_db_query($url_name_check_select_sql);
					
					if (tep_db_num_rows($url_name_check_result_sql) > 0) {
						$messageStack->add_session(WARNING_LATEST_NEWS_URL_NAME_DUPLICATE, 'warning');
						$all_cat_accessible = false;
						$latest_news_link .= 'action=new_latest_news&';
						$cpt_id_str = implode(',', $_POST['custom_products_type_id']);
					}
				} else{
					$info_changed_latest_news_url_alias = '';
				}

				if ($all_cat_accessible) {
					$display_site_str = '';
					$cpt_id_str = '';

		          	if (isset($_POST['site_id'])) {
						for ($site_id_array = 0; $site_id_array < sizeof($_POST['site_id']); $site_id_array++) {
							if ($_POST['site_id'][$site_id_array] == 'on') {
								$_POST['site_id'][$site_id_array] = 0;
							}
						}
						$display_site_str = implode(',', $_POST['site_id']);
		          	}

					if (isset($_POST['custom_products_type_id'])) {
						$cpt_id_str = implode(',', $_POST['custom_products_type_id']);
					}
					
					$sql_data_array = array(  'latest_news_url_alias' => $info_changed_latest_news_url_alias,
	          								  'custom_products_type' => tep_db_prepare_input($cpt_id_str),
	                                  		  'date_added'=> 'now()', //uses the inbuilt mysql function 'now'
	                                  		  'status'    => '0',
	                                  		  'news_groups_id'     => tep_db_prepare_input($_POST['news_groups_id']),
	                                  		  'url'     	=> tep_db_prepare_input($_POST['url']),
	                                  		  'news_display_sites' => tep_db_prepare_input($display_site_str),
	                                  		  'extra_news_display_sites' => ((tep_not_null($_POST['extra_news_display_sites']) && $_POST['extra_news_display_sites'] == 1) ? '1' : '0')
	                                  		);
	          		tep_db_perform(TABLE_LATEST_NEWS, $sql_data_array);
	          		$news_id = tep_db_insert_id();

	          		foreach ($_POST['headline_tab'] as $news_lang_id => $news_heading_title) {
						if (tep_not_null($news_heading_title)) {
							$news_desc_data_array = array(	'news_id' => $news_id,
															'language_id' => (int)$news_lang_id,
															'headline' => tep_db_prepare_input($news_heading_title),
						          							'latest_news_summary' => tep_db_prepare_input($_POST['summary_tab'][$news_lang_id]),
						                              		'content' => tep_db_prepare_input(str_replace(array("\r\n"), array(''), $_POST['content_tab'][$news_lang_id])),
						                                  );
				           	tep_db_perform(TABLE_LATEST_NEWS_DESCRIPTION, $news_desc_data_array);
				        }
				        
				        // Upload 3 Pictures
				        if (sizeof($_FILES)>0) {
							$uploadthumbnail=false;
							$uploadbanner=false;
							$uploadimage=false;
							$latest_news_thumbnail = $_FILES['latest_news_thumbnail']['tmp_name'][$news_lang_id];
							$latest_news_banner = $_FILES['latest_news_banner']['tmp_name'][$news_lang_id];
							$latest_news_image = $_FILES['latest_news_image']['tmp_name'][$news_lang_id];
							$uploaddir = DIR_FS_CATALOG.DIR_WS_IMAGES;
							
							if (tep_not_null($latest_news_thumbnail)){
								if(tep_verify_file_mime($latest_news_thumbnail)) {
									$uploadthumbnail=true;
								} else {
									$uploadthumbnail=false;
								}
							}
							
							if (tep_not_null($latest_news_banner)) {
								if(tep_verify_file_mime($latest_news_banner)) {
									$uploadbanner=true;
								} else {
									$uploadbanner=false;
								}
							}
							
							if (tep_not_null($latest_news_image)) {
								if(tep_verify_file_mime($latest_news_image)) {
									$uploadimage=true;
								} else { 
									$uploadimage=false;
								}
							}
						
							$thumbnail_filename = $news_id .'_1_'.$news_lang_id.'.jpg';
							$banner_filename = $news_id .'_2_'.$news_lang_id.'.jpg';
							$image_filename = $news_id .'_3_'.$news_lang_id.'.jpg';
							
							if ($aws_obj->is_aws_s3_enabled()) {
								$aws_obj->set_acl('ACL_PUBLIC');
								$aws_obj->set_storage('STORAGE_STANDARD');
								if ($uploadthumbnail) {
									$aws_obj->set_file(array('tmp_name' => $latest_news_thumbnail));
									$aws_obj->set_filename($thumbnail_filename);
									$aws_obj->save_file();
								}
								
								if ($uploadbanner) {
									$aws_obj->set_file(array('tmp_name' => $latest_news_banner));
									$aws_obj->set_filename($banner_filename);
									$aws_obj->save_file();
								}
								
								if ($uploadimage) {
									$aws_obj->set_file(array('tmp_name' => $latest_news_image));
									$aws_obj->set_filename($image_filename);
									$aws_obj->save_file();
								}
							} else {
								if ($uploadthumbnail)  {
									$filename_new = $uploaddir. "news/". $thumbnail_filename;
								
									if (file_exists($filename_new)) {
										unlink($filename_new);
									}
									move_uploaded_file($latest_news_thumbnail, $filename_new);
								}
								if ($uploadbanner) {
									$filename_new = $uploaddir. "news/". $banner_filename;
								
									if (file_exists($filename_new)) {
										unlink($filename_new);
									}
									move_uploaded_file($latest_news_banner, $filename_new);
								}
								if ($uploadimage) {
									$filename_new = $uploaddir. "news/". $image_filename;
								
									if (file_exists($filename_new)) {
										unlink($filename_new);
									}
									move_uploaded_file($latest_news_image, $filename_new);
								}
							}
						}
					}
					
	          		if (tep_not_null($_POST['news_groups_cat_id']) && $news_id) {
	          			$selected_categories_id_array = isset($_POST['check_all_top']) ? array(-999) : $_POST['news_groups_cat_id'];
	          			if(isset($_POST['check_top']))	$selected_categories_id_array[] = 0;
	          		
		          		$insert_sql = "	INSERT INTO ".TABLE_LATEST_NEWS_CATEGORIES." (news_id, categories_id)
											VALUES (".$news_id.",".tep_db_prepare_input(implode("),(".$news_id.",", $selected_categories_id_array)).")";
			          	tep_db_query($insert_sql);		          	
		          	}
		          	
	          		if ($seo_url_alias_latest_news_permission) {
		          		if ($seo_url_alias_latest_news_permission) {
			          		$info_changed_history_array = array('info_changed_history_type' => TEXT_INFO_CHANGED_HISTORY_NEWS,
			          											'info_changed_history_type_id' => $news_id,
			          											'info_changed_history_remark' => $info_changed_latest_news_url_alias,
			          											'info_changed_history_date_added' => 'now()',
			          											'info_changed_history_added_by' => $login_email_address
			          											);
			          		tep_db_perform(TABLE_INFO_CHANGED_HISTORY, $info_changed_history_array);
			          	}
			        }
	          	}
				
				if ($news_id) {
					// Add tags
					if (tep_not_null($_POST['latest_news_tags'])) {
						$latest_news_tags = tep_db_prepare_input($_POST['latest_news_tags']);
						$tag_array = array();
						$tag_array = array_unique(explode(',', $latest_news_tags));
						
		          		tep_db_query("DELETE FROM " . TABLE_LATEST_NEWS_TAG_CONTENT . " WHERE content_id = '" . tep_db_input($news_id) . "'");
						
						foreach ($tag_array as $tag_row) {
							$tag_row = trim($tag_row);
							
							$tag_name_check_select_sql = "	SELECT tag_id 
															FROM ". TABLE_LATEST_NEWS_TAG_CATEGORIES ."
															WHERE tag_name ='". tep_db_input($tag_row) ."'";
							$tag_name_check_result_sql = tep_db_query($tag_name_check_select_sql);
							
							if ($tag_id_row = tep_db_fetch_array($tag_name_check_result_sql)) {
								$tag_id = $tag_id_row['tag_id'];
							} else {
								$sql_data_array = array(  'tag_name' => tep_db_prepare_input($tag_row) );
				          		tep_db_perform(TABLE_LATEST_NEWS_TAG_CATEGORIES, $sql_data_array);
				          		$tag_id = tep_db_insert_id();
							}
							
							$sql_data_array1 = array(  	'content_id' => tep_db_prepare_input($news_id),
			          								  	'tag_id' => $tag_id,
			                                  		  	'content_type'=> 'ln');
			          		tep_db_perform(TABLE_LATEST_NEWS_TAG_CONTENT, $sql_data_array1);
		          			tep_db_query('UPDATE ' . TABLE_LATEST_NEWS_TAG_CATEGORIES . " SET tag_counter = tag_counter + 1 WHERE tag_id = '" . tep_db_input($tag_id) . "'");
						}
					}
				}
        	} else {
          		$messageStack->add_session(ERROR_NEWS_HEADLINE, 'error');
          	}
			
			$latest_news_link .= 'grp_id='.$_POST['news_groups_id'].'&latest_news_id='.$news_id;
        	tep_redirect(tep_href_link(FILENAME_LATEST_NEWS, $latest_news_link));   	
        	break;
      	case 'update_latest_news': //user wants to modify a news article.
      		$news_id = (int)$_GET['latest_news_id'];
        	if ($news_id) {
        		$all_cat_accessible = true;
				
        		if (tep_not_null($_POST['news_groups_cat_id'])) {
        			if (isset($_POST['check_all_top'])) {
        				$unauth_cat_array = tep_get_ineligible_categories(FILENAME_LATEST_NEWS, $eligible_categories_array, 0, true);
        			} else {
	        			$eligible_categories_array = tep_get_eligible_categories(FILENAME_LATEST_NEWS, $eligible_categories_array, 0, true);
						$unauth_cat_array = array_diff($_POST['news_groups_cat_id'], $eligible_categories_array);
					}
					
					if (tep_not_null($unauth_cat_array)) {
						$all_cat_accessible = false;
						$messageStack->add_session(ERROR_CAT_ACCESS_DENIED, 'error');
					}
				}
				
				//validate infolinks_url_alias
				if ($seo_url_alias_latest_news_permission) {
					include_once(DIR_WS_CLASSES . 'seo.php');
					$seo_url = new seo_url();
					
					if (tep_not_null($_POST['latest_news_url_alias'])) {
						$info_changed_latest_news_url_alias = $seo_url->tep_translate_special_character($_POST['latest_news_url_alias']);
						$info_changed_latest_news_url_alias = $seo_url->tep_validate_special_characters($info_changed_latest_news_url_alias);
						
						if ($_POST['latest_news_url_alias'] != $info_changed_latest_news_url_alias) {
							$messageStack->add_session(sprintf(WARNING_LATEST_NEWS_URL_ALIAS_CHANGED, $_POST['latest_news_url_alias'], $info_changed_latest_news_url_alias), 'warning');
						}
					} else { //if empty
						$info_changed_latest_news_url_alias = $seo_url->tep_translate_special_character($_POST['headline_tab']);
						$info_changed_latest_news_url_alias = $seo_url->tep_validate_special_characters($info_changed_latest_news_url_alias);
						
						$messageStack->add_session(sprintf(WARNING_LATEST_NEWS_URL_ALIAS_CHANGED, $_POST['headline_tab'], $info_changed_latest_news_url_alias), 'warning');
					}
					// check existing url name 
					$url_name_check_select_sql = "	SELECT latest_news_url_alias 
													FROM ". TABLE_LATEST_NEWS ."
													WHERE latest_news_url_alias='". $info_changed_latest_news_url_alias ."'
														AND news_groups_id='". $_POST['news_groups_id'] ."'
														AND news_id<>'". $news_id ."'";
					$url_name_check_result_sql = tep_db_query($url_name_check_select_sql);

					if (tep_db_num_rows($url_name_check_result_sql) > 0) {
						$messageStack->add_session(WARNING_LATEST_NEWS_URL_NAME_DUPLICATE, 'warning');
						$all_cat_accessible = false;
						$latest_news_link .= 'action=new_latest_news&';
					}
				} else {
					$info_changed_latest_news_url_alias = $_POST['hidden_latest_news_url_alias'];
				}
				
				if ($all_cat_accessible) {
					if ($seo_url_alias_latest_news_permission) {
						$exist_news_url_name_select_sql = "	SELECT latest_news_url_alias 
															FROM ". TABLE_LATEST_NEWS ."
															WHERE news_id='". tep_db_prepare_input($news_id) ."'";
						$exist_news_url_name_result_sql = tep_db_query($exist_news_url_name_select_sql);
						$exist_news_url_name_row = tep_db_fetch_array($exist_news_url_name_result_sql);

						if ($exist_news_url_name_row['latest_news_url_alias'] != $info_changed_latest_news_url_alias) { //check existing latest_news_url_alias
							$info_changed_history_array = array('info_changed_history_type' => TEXT_INFO_CHANGED_HISTORY_NEWS,
			          											'info_changed_history_type_id' => tep_db_prepare_input($news_id),
			          											'info_changed_history_remark' => $info_changed_latest_news_url_alias,
			          											'info_changed_history_date_added' => 'now()',
			          											'info_changed_history_added_by' => $login_email_address
			          											);
							
			          		tep_db_perform(TABLE_INFO_CHANGED_HISTORY, $info_changed_history_array);
			          	}
		          	}
		         	
		          	$display_site_str = '';
					$cpt_id_str = '';
					
		          	if (isset($_POST['site_id'])) {
						for ($site_id_array = 0; $site_id_array < sizeof($_POST['site_id']); $site_id_array++) {
							if ($_POST['site_id'][$site_id_array] == 'on') {
								$_POST['site_id'][$site_id_array] = 0;
							}
						}
						$display_site_str = implode(',', $_POST['site_id']);
		          	}
					
					if (isset($_POST['custom_products_type_id'])) {
						$cpt_id_str = implode(',', $_POST['custom_products_type_id']);
					}
					
		          	$sql_data_array = array('latest_news_url_alias' => $info_changed_latest_news_url_alias,
											'custom_products_type' => tep_db_prepare_input($cpt_id_str),
		                                  	'date_added' => tep_db_prepare_input($_POST['date_added']),
		                                  	'news_groups_id' => tep_db_prepare_input($_POST['news_groups_id']),
		                                  	'url' => tep_db_prepare_input($_POST['url']),
		                                  	'news_display_sites' => $display_site_str,
		                                  	'extra_news_display_sites' => ((tep_not_null($_POST['extra_news_display_sites']) && $_POST['extra_news_display_sites'] == 1) ? '1' : '0')
		                                  	);
		          	tep_db_perform(TABLE_LATEST_NEWS, $sql_data_array, 'update', "news_id = '" . $news_id . "'");
					
					//update news categories id
	          		if(isset($_POST['check_all_top'])) {
	          			$selected_categories_id_array = array(-999);
	          		} else if (tep_not_null($_POST['news_groups_cat_id'])) {
	          			$selected_categories_id_array = $_POST['news_groups_cat_id'];
	          		}
	          		
	          		if(isset($_POST['check_top']))	$selected_categories_id_array[] = 0;
	          		
	          		if ((int)$_GET['latest_news_id']) {
		          		if (tep_not_null($selected_categories_id_array)) {
							tep_db_query("DELETE FROM ".TABLE_LATEST_NEWS_CATEGORIES." WHERE news_id='".tep_db_prepare_input($_GET['latest_news_id'])."' AND categories_id NOT IN ('".implode("','", $selected_categories_id_array)."')");

		          			$db_categories_id_array = array();
		          			
		          			$exist_categories_id_select_sql = "	SELECT categories_id 
																FROM ".TABLE_LATEST_NEWS_CATEGORIES."
																WHERE news_id='". tep_db_prepare_input($_GET['latest_news_id']) ."'";
							$exist_categories_id_result_sql = tep_db_query($exist_categories_id_select_sql);
							while ($exist_categories_id_row = tep_db_fetch_array($exist_categories_id_result_sql)) {
								$db_categories_id_array[] = $exist_categories_id_row['categories_id'];
				          	}
		          		
		          			$insert_categories_id_array = array_diff($selected_categories_id_array, $db_categories_id_array);
		          			
		          			if (tep_not_null($insert_categories_id_array)) {
			          			$insert_sql = "	INSERT INTO ".TABLE_LATEST_NEWS_CATEGORIES." (news_id, categories_id)
												VALUES (".$_GET['latest_news_id'].",".tep_db_prepare_input(implode("),(".$_GET['latest_news_id'].",", $insert_categories_id_array)).")";
				          		tep_db_query($insert_sql);	
				          	}
			          	} else {
			          		tep_db_query("DELETE FROM ".TABLE_LATEST_NEWS_CATEGORIES." WHERE news_id='".tep_db_prepare_input($_GET['latest_news_id'])."'");	
			          	}
		          	}
		          	
		          	for ($i=1;$i<=count($_POST['headline_tab']);$i++) {
			      		if ($_POST['headline_tab'][$i] == '') {
			      			$_POST['headline_tab'][1] = $_POST['headline_tab'][$i+1];
			      		} else {
			      			break;
			      		}
	  				}

					foreach ($_POST['headline_tab'] as $news_lang_id => $news_heading_title) {
						if (tep_not_null($news_heading_title)) {
							if(tep_not_null($_POST['content_tab'][$news_lang_id]) && $news_lang_id == $_POST['lang_id']) {
								$sql_data_array = array('headline' => tep_db_prepare_input($news_heading_title),
														'latest_news_summary' => tep_db_prepare_input($_POST['summary_tab'][$news_lang_id]),
														'content' => tep_db_prepare_input(str_replace(array("\r\n"), array(''), $_POST['content_tab'][$news_lang_id])),
														'is_default' => 1
														);
							} else {
								$sql_data_array = array('headline' => tep_db_prepare_input($news_heading_title),
														'latest_news_summary' => tep_db_prepare_input($_POST['summary_tab'][$news_lang_id]),
														'content' => tep_db_prepare_input(str_replace(array("\r\n"), array(''), $_POST['content_tab'][$news_lang_id])),
														'is_default' => 0
														);
							}
							$news_id_check_select_sql = "	SELECT news_id 
															FROM " . TABLE_LATEST_NEWS_DESCRIPTION . "
															WHERE news_id = '" . tep_db_input($news_id) . "' 
																AND language_id = '" . (int)$news_lang_id . "'";
							$news_id_check_result_sql = tep_db_query($news_id_check_select_sql);
							
							if (tep_db_num_rows($news_id_check_result_sql) > 0) {
								tep_db_perform(TABLE_LATEST_NEWS_DESCRIPTION, $sql_data_array, 'update', "news_id = '" . (int)$news_id . "' AND language_id = '" . (int)$news_lang_id . "'");
							} else {
								$sql_data_array['news_id'] = (int)$news_id;
								$sql_data_array['language_id'] = $news_lang_id;
								tep_db_perform(TABLE_LATEST_NEWS_DESCRIPTION, $sql_data_array);
							}
							
							$uploaddir = DIR_FS_CATALOG.DIR_WS_IMAGES;
							$deletethumbnail = $_POST['delete_thumbnail'][$news_lang_id];
							$deletebanner = $_POST['delete_banner'][$news_lang_id];
							$deleteimage = $_POST['delete_image'][$news_lang_id];
							
							if ($aws_obj->is_aws_s3_enabled()) {
								$aws_obj->set_acl('ACL_PUBLIC');
								$aws_obj->set_storage('STORAGE_STANDARD');
			
								if ($deletethumbnail) {
									$aws_obj->delete_file($news_id .'_1_'.$news_lang_id.'.jpg');
								}
								
								if ($deletebanner) {
									$aws_obj->delete_file($news_id .'_2_'.$news_lang_id.'.jpg');
								} 
								
								if ($deleteimage) {
									$aws_obj->delete_file($news_id .'_3_'.$news_lang_id.'.jpg');
								}
							} else {
								if ($deletethumbnail) {
									if (file_exists($uploaddir. "news/". $news_id ."_1_".$news_lang_id.".jpg")) {
										unlink($uploaddir. "news/". $news_id ."_1_".$news_lang_id.".jpg");
									}
								}
								
								if ($deletebanner) {
									if (file_exists($uploaddir. "news/". $news_id ."_2_".$news_lang_id.".jpg")) {
										unlink($uploaddir. "news/". $news_id ."_2_".$news_lang_id.".jpg");
									}
								}
			
								if ($deleteimage) {
									if (file_exists($uploaddir. "news/". $news_id ."_3_".$news_lang_id.".jpg")) {
										unlink($uploaddir. "news/". $news_id ."_3_".$news_lang_id.".jpg");
									}
								}
							}
							
							if (sizeof($_FILES)>0) {
								$uploadthumbnail=false;
								$uploadbanner=false;
								$uploadimage=false;
								$latest_news_thumbnail = $_FILES['latest_news_thumbnail']['tmp_name'][$news_lang_id];
								$latest_news_banner = $_FILES['latest_news_banner']['tmp_name'][$news_lang_id];
								$latest_news_image = $_FILES['latest_news_image']['tmp_name'][$news_lang_id];

								if (tep_not_null($latest_news_thumbnail)){
									if(tep_verify_file_mime($latest_news_thumbnail)) {
										$uploadthumbnail=true;
									} else {
										$uploadthumbnail=false;
									}
								}
								
								if (tep_not_null($latest_news_banner)) {
									if(tep_verify_file_mime($latest_news_banner)) {
										$uploadbanner=true;
									} else {
										$uploadbanner=false;
									}
								}
								
								if (tep_not_null($latest_news_image)) {
									if(tep_verify_file_mime($latest_news_image)) {
										$uploadimage=true;
									} else { 
										$uploadimage=false;
									}
								}
							
								$thumbnail_filename = $news_id .'_1_'.$news_lang_id.'.jpg';
								$banner_filename = $news_id .'_2_'.$news_lang_id.'.jpg';
								$image_filename = $news_id .'_3_'.$news_lang_id.'.jpg';
								
								if ($aws_obj->is_aws_s3_enabled()) {
									if ($uploadthumbnail && $deletethumbnail !=1) {
										$aws_obj->set_file(array('tmp_name' => $latest_news_thumbnail));
										$aws_obj->set_filename($thumbnail_filename);
										$aws_obj->save_file();
									}
									
									if ($uploadbanner && $deletebanner !=1) {
										$aws_obj->set_file(array('tmp_name' => $latest_news_banner));
										$aws_obj->set_filename($banner_filename);
										$aws_obj->save_file();
									}
									
									if ($uploadimage && $deleteimage !=1) {
										$aws_obj->set_file(array('tmp_name' => $latest_news_image));
										$aws_obj->set_filename($image_filename);
										$aws_obj->save_file();
									}
								} else {
									if ($uploadthumbnail && $deletethumbnail !=1)  {
										$filename_new = $uploaddir. "news/". $thumbnail_filename;
									
										if (file_exists($filename_new)) {
											unlink($filename_new);
										}
										move_uploaded_file($latest_news_thumbnail, $filename_new);
									}
									if ($uploadbanner && $deletebanner !=1) {
										$filename_new = $uploaddir. "news/". $banner_filename;
									
										if (file_exists($filename_new)) {
											unlink($filename_new);
										}
										move_uploaded_file($latest_news_banner, $filename_new);
									}
									if ($uploadimage && $deleteimage !=1) {
										$filename_new = $uploaddir. "news/". $image_filename;
									
										if (file_exists($filename_new)) {
											unlink($filename_new);
										}
										move_uploaded_file($latest_news_image, $filename_new);
									}
								}
							}
						} else { 
							if ($news_lang_id > 1) {	// Can delete any language except English
								$news_desc_delete_sql = "	DELETE FROM " . TABLE_LATEST_NEWS_DESCRIPTION . " 
															WHERE news_id = '" . tep_db_input($news_id) . "' 
																AND language_id = '".$news_lang_id."'";
								tep_db_query($news_desc_delete_sql);
							}
						}
					}
	          	}
				// Add tags
				if (tep_not_null($_POST['latest_news_tags'])) {
					$latest_news_tags = tep_db_prepare_input($_POST['latest_news_tags']);
					$tag_array = array();
					$tag_array = array_unique(explode(',', $latest_news_tags));
					
					$reset_tag_id_select_sql = " 	SELECT tag_id
													FROM ". TABLE_LATEST_NEWS_TAG_CONTENT ."
													WHERE content_id = '" . tep_db_input($news_id) . "'";
					$reset_tag_id_result_sql = tep_db_query($reset_tag_id_select_sql);
					while ($tag_id_reset_row = tep_db_fetch_array($reset_tag_id_result_sql)) {
						tep_db_query('UPDATE ' . TABLE_LATEST_NEWS_TAG_CATEGORIES . " SET tag_counter = tag_counter - 1 WHERE tag_id = '" . $tag_id_reset_row['tag_id'] . "'");					
					}
	          		tep_db_query("DELETE FROM " . TABLE_LATEST_NEWS_TAG_CONTENT . " WHERE content_id = '" . tep_db_input($news_id) . "'");
	          		
					foreach ($tag_array as $tag_row) {
						$tag_row = trim($tag_row);
						$tag_name_check_select_sql = "	SELECT tag_id 
														FROM ". TABLE_LATEST_NEWS_TAG_CATEGORIES ."
														WHERE tag_name ='". tep_db_input($tag_row) ."'";
						$tag_name_check_result_sql = tep_db_query($tag_name_check_select_sql);

						if ($tag_id_row = tep_db_fetch_array($tag_name_check_result_sql)) {
							$tag_id = $tag_id_row['tag_id'];
						} else {
							$sql_data_array = array(  'tag_name' => tep_db_prepare_input($tag_row) );
			          		tep_db_perform(TABLE_LATEST_NEWS_TAG_CATEGORIES, $sql_data_array);
			          		$tag_id = tep_db_insert_id();
						}
						$sql_data_array1 = array(  	'content_id' => tep_db_prepare_input($news_id),
		          								  	'tag_id' => $tag_id,
		                                  		  	'content_type'=> 'ln');
		          		tep_db_perform(TABLE_LATEST_NEWS_TAG_CONTENT, $sql_data_array1);
		          		tep_db_query('UPDATE ' . TABLE_LATEST_NEWS_TAG_CATEGORIES . " SET tag_counter = tag_counter + 1 WHERE tag_id = '" . tep_db_prepare_input($tag_id) . "'");
					}
				}
        	}
        	$latest_news_link .= 'grp_id='.$_POST['news_groups_id'].'&latest_news_id='.$news_id;
			tep_redirect(tep_href_link(FILENAME_LATEST_NEWS, $latest_news_link));
        	break;
	}
}

if (!$aws_obj->is_aws_s3_enabled()) {
	if (is_dir(DIR_WS_IMAGES . 'news/')) {
		if (!is_writeable(DIR_WS_IMAGES . 'news/')) $messageStack->add(ERROR_IMAGE_DIRECTORY_NOT_WRITEABLE, 'error');
	} else {
	 	$messageStack->add(ERROR_IMAGE_DIRECTORY_DOES_NOT_EXIST, 'error');
	}
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
<link rel="stylesheet" type="text/css" href="includes/javascript/checktree/checktree.css">
<script language="javascript" src="includes/general.js"></script>
<script language="javascript" src="includes/javascript/jquery.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
 
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">			
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>  			
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top"> 			
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ($_GET['action'] == 'new_latest_news') {
	//insert or edit a news item
	$languages = tep_get_languages();

	$latest_news = array();
	$latest_news_array = array();
	
    if ( isset($_GET['latest_news_id']) ) { 
    	$latest_news_headline_select_sql = "SELECT lnd.news_id, lnd.language_id, lnd.headline, lnd.latest_news_summary, lnd.content, 
    												ln.latest_news_url_alias, ln.date_added, ln.custom_products_type, ln.news_groups_id, 
    												ln.url, ln.news_display_sites, 
    												ln.extra_news_display_sites 
											FROM ". TABLE_LATEST_NEWS." as ln
											LEFT JOIN " . TABLE_LATEST_NEWS_DESCRIPTION. " as lnd
												ON (ln.news_id = lnd.news_id)
											WHERE lnd.news_id ='" . (int)$_GET['latest_news_id'] . "'";
    	$latest_news_headline_result_sql = tep_db_query($latest_news_headline_select_sql);
      	while ($latest_news_headline_row = tep_db_fetch_array($latest_news_headline_result_sql)) {
      		$latest_news = $latest_news_headline_row;
			$latest_news_array[$latest_news_headline_row['language_id']]['headline_tab'] = $latest_news_headline_row['headline'];
      		$latest_news_array[$latest_news_headline_row['language_id']]['latest_news_summary_tab'] = $latest_news_headline_row['latest_news_summary'];
      		$latest_news_array[$latest_news_headline_row['language_id']]['content_tab'] = $latest_news_headline_row['content'];
      	}

		$printtag = '';
    	$latest_news_tag_select_sql = "	SELECT c.tag_name
										FROM ". TABLE_LATEST_NEWS_TAG_CONTENT." as d 
										INNER JOIN ". TABLE_LATEST_NEWS_TAG_CATEGORIES." as c
											ON c.tag_id = d.tag_id  
										WHERE d.content_id ='" . (int)$_GET['latest_news_id'] . "'";
    	$latest_news_tag_result_sql = tep_db_query($latest_news_tag_select_sql);
    	
      	while ($latest_news_tag_row = tep_db_fetch_array($latest_news_tag_result_sql)) {
			if (tep_not_null($printtag))
 	     		$printtag .= ','.$latest_news_tag_row['tag_name'];
 	     	else
 	     		$printtag = $latest_news_tag_row['tag_name'];
      	}
		
		$default_language_select_sql = "SELECT language_id 
										FROM " . TABLE_LATEST_NEWS_DESCRIPTION. "
										WHERE news_id ='" . (int)$_GET['latest_news_id'] . "'
											AND is_default = 1";
		$default_language_result_sql = tep_db_query($default_language_select_sql);
		if($default_language_row = tep_db_fetch_array($default_language_result_sql)) {
			$default_language_id = $default_language_row['language_id'];
		}
		$default_language_id = tep_not_null($default_language_id) ?  $default_language_id : '1';
    }
?>
      				<tr>
						<?=tep_draw_form('new_latest_news', FILENAME_LATEST_NEWS, isset($_GET['latest_news_id']) ? 'latest_news_id=' . $_GET['latest_news_id'] . '&action=update_latest_news' : 'action=insert_latest_news', 'post', 'enctype="multipart/form-data" onSubmit="return check_form();"'); ?>
      					<?=tep_draw_hidden_field('grp_id', $latest_news_grp_id)?>
        				<td> 
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
								<tr>
									<td class="smallText" align="left"><?=TEXT_DEFAULT_LANGUAGE . ' ' . tep_draw_pull_down_menu('lang_id', $languages, $default_language_id,'onChange="validateNewsContent(this.options[this.selectedIndex].value,' . $default_language_id . ')" id="lang_dd" ')?></td>
								</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
      					<td>
     						<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td colspan="2">
										<table border="0" width="62%" cellspacing="0" cellpadding="0">
											<tr>
												<td>
													<div id="languages_tab">
											            <ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
															<li id="languages_li_<?=$lang_cnt?>"><a href="#languages_tab_<?=$lang_cnt?>"><span><?=$languages[$lang_cnt]['name']?></span></a></li>
<?	} ?>
														</ul>
<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
														<div id="languages_tab_<?=$lang_cnt?>" style="border:1px solid #C9C9C9;">
															<table border="0" width="100%" cellspacing="0" cellpadding="4">
																<tr>							
							            							<td class="main" valign="top"><?=TEXT_LATEST_NEWS_HEADLINE_TAB ?></td>
							            							<td class="main"><?=tep_draw_input_field('headline_tab[' . $languages[$lang_cnt]['id'] . ']', (isset($latest_news_array[$languages[$lang_cnt]['id']]['headline_tab'])?$latest_news_array[$languages[$lang_cnt]['id']]['headline_tab']:''), 'id="headline_tab_'.$languages[$lang_cnt]['id'].'" maxlength="100"', true )?></td>
		
																</tr>
																<tr>
							            						    <td class="main" valign="top"><?=TEXT_LATEST_NEWS_SUMMARY_TAB ?></td>
																	<td class="main"><?=tep_draw_textarea_field('summary_tab[' . $languages[$lang_cnt]['id'] . ']','soft', '70', '6', (isset($latest_news_array[$languages[$lang_cnt]['id']]['latest_news_summary_tab'])?tep_db_output($latest_news_array[$languages[$lang_cnt]['id']]['latest_news_summary_tab']):''), 'id="latest_news_summary_tab"')?></td>
																</tr>
																<tr>
								            						<td class="main" valign="top"><?=TEXT_LATEST_NEWS_CONTENT_TAB?></td>
																	<td class="main"><?=tep_draw_textarea_field('content_tab[' . $languages[$lang_cnt]['id'] . ']','soft', '70', '15', (isset($latest_news_array[$languages[$lang_cnt]['id']]['content_tab'])?tep_db_output($latest_news_array[$languages[$lang_cnt]['id']]['content_tab']):''), 'id="content_tab_'.$languages[$lang_cnt]['id'].'"')?></td>
																</tr>
																<tr>
								            						<td class="main" width="10%"><?=TEXT_LATEST_NEWS_UPLOAD_THUMBNAIL?></td>
								            						<td class="main">
																		<?php
																			echo tep_draw_file_field('latest_news_thumbnail[' . $languages[$lang_cnt]['id'] . ']', 'size="40"');
																			
																			if ($aws_obj->is_image_exists(tep_db_prepare_input($_GET['latest_news_id']) . '_1_'.$languages[$lang_cnt]['id'].'.jpg')) {
																				echo tep_draw_checkbox_field("delete_thumbnail[" . $languages[$lang_cnt]['id'] . "]", 1, "", "", '')." ". TEXT_LATEST_NEWS_NO_REMOVE_IMAGE . ' (<a target="_blank" href="'.$aws_obj->get_image_url_by_instance().'">'.TEXT_LATEST_NEWS_VIEW_IMAGE.'</a>)';
																			} else if (file_exists(DIR_WS_IMAGES . 'news/' . tep_db_prepare_input($_GET['latest_news_id']) . '_1_'.$languages[$lang_cnt]['id'].'.jpg')) {
																				echo tep_draw_checkbox_field("delete_thumbnail[" . $languages[$lang_cnt]['id'] . "]", 1, "", "", '')." ". TEXT_LATEST_NEWS_NO_REMOVE_IMAGE . ' (<a target="_blank" href="'.HTTP_CATALOG_SERVER.'/'.DIR_WS_IMAGES . '/news/' . $_GET['latest_news_id'].'_1.jpg">'.TEXT_LATEST_NEWS_VIEW_IMAGE.'</a>)';
																			}
																			else {
																				echo TEXT_LATEST_NEWS_NO_IMAGE_UPLOADED;
																			}
																		?>
								            						</td>
								          						</tr>
								          						<tr>
								            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								          						</tr>
								          						<tr>
								            						<td class="main" width="10%"><?=TEXT_LATEST_NEWS_UPLOAD_BANNER?></td>
								            						<td class="main">
																		<?php 
																			echo tep_draw_file_field('latest_news_banner[' . $languages[$lang_cnt]['id'] . ']', 'size="40"');
								
																			if ($aws_obj->is_image_exists(tep_db_prepare_input($_GET['latest_news_id']) . '_2_'.$languages[$lang_cnt]['id'].'.jpg')) {
																				echo tep_draw_checkbox_field("delete_banner[" . $languages[$lang_cnt]['id'] . "]", 1, "", "", '')." ". TEXT_LATEST_NEWS_NO_REMOVE_IMAGE . ' (<a target="_blank" href="'.$aws_obj->get_image_url_by_instance().'">'.TEXT_LATEST_NEWS_VIEW_IMAGE.'</a>)';
																			} else if (file_exists(DIR_WS_IMAGES . 'news/' . tep_db_prepare_input($_GET['latest_news_id']) . '_2_'.$languages[$lang_cnt]['id'].'.jpg')) {
																				echo tep_draw_checkbox_field("delete_banner[" . $languages[$lang_cnt]['id'] . "]", 1, "", "", '')." ". TEXT_LATEST_NEWS_NO_REMOVE_IMAGE . ' (<a target="_blank" href="'.HTTP_CATALOG_SERVER.'/'.DIR_WS_IMAGES . '/news/' . $_GET['latest_news_id'].'_1.jpg">'.TEXT_LATEST_NEWS_VIEW_IMAGE.'</a>)';
																			}
																			else {
																				echo TEXT_LATEST_NEWS_NO_IMAGE_UPLOADED;
																			}
																		?>
								            						</td>
								          						</tr>
								          						<tr>
								            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								          						</tr>
								          						<tr>
								            						<td class="main" width="10%"><?=TEXT_LATEST_NEWS_UPLOAD_IMAGE?></td>
								            						<td class="main">
																		<?php 
																			echo tep_draw_file_field('latest_news_image[' . $languages[$lang_cnt]['id'] . ']', 'size="40"');
																			if ($aws_obj->is_image_exists(tep_db_prepare_input($_GET['latest_news_id']) . '_3_'.$languages[$lang_cnt]['id'].'.jpg')) {
																				echo tep_draw_checkbox_field("delete_image[" . $languages[$lang_cnt]['id'] . "]", 1, "", "", '')." ". TEXT_LATEST_NEWS_NO_REMOVE_IMAGE . ' (<a target="_blank" href="'.$aws_obj->get_image_url_by_instance().'">'.TEXT_LATEST_NEWS_VIEW_IMAGE.'</a>)';
																			} else if (file_exists(DIR_WS_IMAGES . 'news/' . tep_db_prepare_input($_GET['latest_news_id']) . '_3_'.$languages[$lang_cnt]['id'].'.jpg')) {
																				echo tep_draw_checkbox_field("delete_image[" . $languages[$lang_cnt]['id'] . "]", 1, "", "", '')." ". TEXT_LATEST_NEWS_NO_REMOVE_IMAGE . ' (<a target="_blank" href="'.HTTP_CATALOG_SERVER.'/'.DIR_WS_IMAGES . '/news/' . $_GET['latest_news_id'].'_1.jpg">'.TEXT_LATEST_NEWS_VIEW_IMAGE.'</a>)';
																			}
																			else {
																				echo TEXT_LATEST_NEWS_NO_IMAGE_UPLOADED;
																			}
																		?>
								            						</td>
								          						</tr>
								          						<tr>
								            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								          						</tr>
															</table>
														</div>
<? } ?>
											        </div><!--languages_tab-->
												</td>
											</tr>
										</table>
										<script type="text/javascript">
											jQuery(document).ready(function() {
												jQuery.noConflict();
												jQuery("#languages_tab > ul").tabs();
											});
										</script>
									</td>
								</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
<?	if ($seo_url_alias_latest_news_permission) {  /* SEO Alias */ ?>
          						<tr>
            						<td class="main" width="10%"><?=TEXT_LATEST_NEWS_SEO_ALIAS?></td>
            						<td class="main">
            							<?=tep_draw_input_field('latest_news_url_alias', $latest_news['latest_news_url_alias'], ' size="40" id="latest_news_url_alias"', true)?> 
            							<?=tep_draw_checkbox_field("auto_seo", 1, "", "", ' id="auto_seo" onClick="auto_generate_seo()";') . TEXT_LATEST_NEWS_AUTO ?>	
            						</td>
          						</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
            						<td class="main" width="10%"><?=TEXT_LATEST_NEWS_QUICK_TAG ?></td>
            						<td class="main">
            							<select name="latest_news_quick_tag" onChange="javascript:addTag();" id="latest_news_quick_tag">
            								<option value=""><?=OPTION_LATEST_NEWS_TAG_0 ?>
            								<option value="<?=OPTION_LATEST_NEWS_TAG_1 ?>"><?=OPTION_LATEST_NEWS_TAG_1 ?>
            								<option value="<?=OPTION_LATEST_NEWS_TAG_2 ?>"><?=OPTION_LATEST_NEWS_TAG_2 ?>
            								<option value="<?=OPTION_LATEST_NEWS_TAG_3 ?>"><?=OPTION_LATEST_NEWS_TAG_3 ?>
            								<option value="<?=OPTION_LATEST_NEWS_TAG_4 ?>"><?=OPTION_LATEST_NEWS_TAG_4 ?>
            								<option value="<?=OPTION_LATEST_NEWS_TAG_5 ?>"><?=OPTION_LATEST_NEWS_TAG_5 ?>
            								<option value="<?=OPTION_LATEST_NEWS_TAG_6 ?>"><?=OPTION_LATEST_NEWS_TAG_6 ?>
            								<option value="<?=OPTION_LATEST_NEWS_TAG_7 ?>"><?=OPTION_LATEST_NEWS_TAG_7 ?>
            								<option value="<?=OPTION_LATEST_NEWS_TAG_8 ?>"><?=OPTION_LATEST_NEWS_TAG_8 ?>
            							</select>
            						</td>
          						</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
            						<td class="main" width="10%"><?=TEXT_LATEST_NEWS_TAGS?></td>
            						<td class="main">
            							<?=tep_draw_input_field('latest_news_tags', $printtag, ' size="40" id="latest_news_tags"', false)?> 
            						</td>
          						</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<script language="JavaScript">
          						<!--
      							function addTag () {
									if (jQuery("#latest_news_quick_tag").val()) {
	      								if (jQuery("#latest_news_tags").val()) {
											latest_news_tags = jQuery("#latest_news_tags").val();
											latest_news_tags = latest_news_tags + "," + jQuery("#latest_news_quick_tag").val();
											jQuery("#latest_news_tags").val(latest_news_tags);
										}
										else {
											jQuery("#latest_news_tags").val(jQuery("#latest_news_quick_tag").val());
										}
									}
								}
          						//-->
          						</script>

<?		if ($_GET['latest_news_id']) { /* Show Info Changed */ ?>
          						<tr>
        							<td class="main" align="top" colspan="2">
        								<div id="criteriaSHLink">
											<a href="javascript:;" onClick="showHideInfoHistory('criteriaBoxDiv', 'criteriaSHLink', '<?=(tep_not_null($_GET['latest_news_id']) ? 'show' : 'hide')?>');"><?=(tep_not_null($action) ? LINK_SHOW_INFO_HISTORY_BOX : LINK_HIDE_INFO_HISTORY_BOX)?></a></div>
										</div>
										<div id="criteriaBoxDiv" class="<?=(tep_not_null($_GET['latest_news_id']) ? 'hide' : 'show')?>">
 											<!-- Show Info Changed -->
        									<table border="1" width="50%" cellspacing="0" cellpadding="2">
        										<tr>
        											<td class="main"><?=TEXT_INFO_DATE_ADDED?></td>
        											<td class="main"><?=TEXT_INFO_REMARK?></td>
        											<td class="main"><?=TEXT_INFO_ADDED_BY?></td>
        										</tr>
<!-- ############################################# DISPLAY INFO CHANGE HISTORY FOR TAB ################################################## -->
<?
			$info_history_select_sql = "SELECT * FROM ". TABLE_INFO_CHANGED_HISTORY ." 
										WHERE info_changed_history_type='". TEXT_INFO_CHANGED_HISTORY_NEWS ."'
											AND info_changed_history_type_id='". (int)$_GET['latest_news_id'] ."'
										ORDER BY info_changed_history_date_added DESC";
			$info_history_result_sql = tep_db_query($info_history_select_sql);
			while ($info_history_row = tep_db_fetch_array($info_history_result_sql)) {
				// Show Info Changed
?>
												<tr>
													<td class="main"><?=$info_history_row['info_changed_history_date_added']?></td>
													<td class="main"><?=$info_history_row['info_changed_history_remark']?></td>
													<td class="main"><?=$info_history_row['info_changed_history_added_by']?></td>
												</tr>
<?			} ?>
										</table>
        								</div>
        							</td>
     							</tr>
     							<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
<!-- ############################################# DISPLAY INFO CHANGE HISTORY FOR TAB ################################################## -->
<?
		}
	} else {
		echo tep_draw_hidden_field('hidden_latest_news_url_alias', $latest_news['latest_news_url_alias'], 'id="latest_news_url_alias"');
	}
?>
          						<tr>
       								<td class="main"><?=TABLE_HEADING_LATEST_NEWS_TYPE?></td>
        							<td class="main"><?=tep_draw_pull_down_menu('news_groups_id', $latest_news_grp_select_array, (isset($_GET['latest_news_id'])) ? $latest_news['news_groups_id'] : $latest_news_grp_id)?></td>
     							</tr>
     							<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
		  						<tr>
            						<td class="main" valign="top"><?=TABLE_HEADING_DISPLAY_SITE?></td>
            						<td class="main">
            						<?
										$site_name_list = tep_get_site_name_list();
            							$latest_news_site_ids = explode(',', $latest_news['news_display_sites']);
            							foreach ($site_name_list as $site_id => $site_name) {
											if ($site_id == 2) {
												echo tep_draw_checkbox_field('site_id['.$site_id.']', ''.$site_id, (tep_not_null($latest_news_site_ids[0]) ? in_array($site_id, $latest_news_site_ids) : false), '', '') . $site_name . '<br>';
											} else if ($site_id == 1) { //china buyback - i want sell
												echo tep_draw_checkbox_field('site_id['.$site_id.']', ''.$site_id, (tep_not_null($latest_news_site_ids[0]) ? in_array($site_id, $latest_news_site_ids) : false), '', ' id="buyback_site_display" onClick="extraLatestNewsCheck();" ') . $site_name;
            								    echo '&nbsp;'. tep_draw_checkbox_field('extra_news_display_sites', '1', (tep_not_null($latest_news['extra_news_display_sites']) && $latest_news['extra_news_display_sites'] == 1 ? true : false), '', 'id="extra_news_display_sites"') . TEXT_I_WANT_SELL . '<br>';
											} else if ($site_id == 0) {
												$cpt_id_array = array();
												if (tep_not_null($latest_news['custom_products_type'])) {
													$cpt_id_array = explode(',', $latest_news['custom_products_type']);
												}

												echo '	<table border="0" cellspacing="0" cellpadding="0">
															<tr>
																<td valign="top">' . tep_draw_checkbox_field('site_id['.$site_id.']', ''.$site_id, (tep_not_null($latest_news_site_ids[0]) ? in_array($site_id, $latest_news_site_ids) : false), '', 'onClick="verify_site_selection(this);"') . '</td>
																<td>
																	<table border="0" cellspacing="0" cellpadding="2">
																		<tr>
																			<td class="main" valign="top">
																				<fieldset class="selectedFieldSet">
																					<legend align=left class=SectionHead>' . $site_name . '</legend>
																					<table border="0" cellspacing="0" cellpadding="0">
																						<tr>
																							<td class="smallText" valign="top">';
												foreach($latest_news_prodtype_array as $key => $value) {
													echo tep_draw_checkbox_field('custom_products_type_id[]', ''.$key, (in_array($key, $cpt_id_array) ? true : false), '', (in_array($site_id, $latest_news_site_ids) && $_GET['latest_news_id'] != '' ? '' : 'disabled')) . $value . "<br>";
												}
												echo										'</td>
																							<td class="smallText"></td>
																						</tr>
																					</table>
																				</fieldset>
																			</td>
																		</tr>
																	</table>
																</td>
															</tr>
														</table>';
											}
	            						}
            						?>
            						</td>
          						</tr>
          						<tr>
            						<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          						</tr>
          						<tr>
            						<td class="main" valign="top"><?=TABLE_HEADING_LATEST_NEWS_CATEGORIES?></td>
            						<td class="main">
            							<table border="0" cellspacing="0" cellpadding="2">
					    					<tr>
					    						<td class="main">
<?php
	$next_level = 0;
	$selected_categories_array = array();
	
	$category_select_sql = "SELECT categories_id
							FROM ".TABLE_LATEST_NEWS_CATEGORIES." 
							WHERE news_id = " . (int)$_GET['latest_news_id'];
	$category_result_sql = tep_db_query($category_select_sql);
	while ($category_row = tep_db_fetch_array($category_result_sql)) {		
		$selected_categories_array[$category_row['categories_id']] = 1;
	}
	
	$category_lists_array[0] = array ('auto_selected' => (isset($selected_categories_array[-999]) ? 1 : 0), 'selected' => (isset($selected_categories_array[0]) ? 1 : 0));
	tep_get_eligible_sub_cat_tree2(FILENAME_LATEST_NEWS, 0, $category_lists_array, 1, $selected_categories_array);
	
	$ttl_array = count($category_lists_array);
	$array_keys = array_keys($category_lists_array);
?>
													<ul id="tree-checkmenu" class="checktree" style="border: 1px solid #ccc; padding: 6px; overflow-y: scroll; height: 300px; width: 610px;">
													<li id="show-0">
														<input id="check2-0" type="checkbox" name="check_all_top" <?php echo $category_lists_array[0]['auto_selected'] ? "checked='checked'" : ""; ?>>Top 
														(<?=TEXT_CATEGORIES_CHECKBOX_LABEL?> <input id="check3-0" type="checkbox" name="check_top" <?php echo $category_lists_array[0]['selected'] ? "checked='checked'" : ""; ?> />) 
														<span id="count-0" class="count"></span>
										  				<ul id="tree-0">
<?php
	for ($arr_index=1; $arr_index < $ttl_array; $arr_index++) {
		$c_id = $array_keys[$arr_index];
		$content_arr = $category_lists_array[$c_id];
		$current_level = $content_arr['level'];
		$category_selected = $content_arr['selected'] ? "checked='checked'" : "";
		
		if(($arr_index+1) < $ttl_array) {
			$next_level = $category_lists_array[$array_keys[$arr_index+1]]['level'];
		} else {
			$next_level = 1;	
		}
		
		if ($content_arr['level_type'] == 'parent') {
			$auto_selected = $content_arr['auto_selected'] ? "checked='checked'" : "";
			
			echo  											'<li id="show-'.$c_id.'"'.$content_arr['last_child'].'><input id="check2-'.$c_id.'" type="checkbox" name="check_all[]" value="'.$c_id.'" '.$auto_selected.'/>'.$content_arr['text']
														. 	' ('.TEXT_CATEGORIES_CHECKBOX_LABEL.' <input id="check3-'.$c_id.'" type="checkbox" name="news_groups_cat_id[]" value="'.$c_id.'" '.$category_selected.'/>)'
														. 	' <span id="count-'.$c_id.'" class="count"></span>'
														. 	'<ul id="tree-'.$c_id.'">';
		} else if ($content_arr['last_child'] == '') {
			echo 												'<li><input id="check-'.$c_id.'" type="checkbox" name="news_groups_cat_id[]" value="'.$c_id.'" '.$category_selected.'/>'.$content_arr['text'].'</li>';
		} else {
			echo 												'<li'.$content_arr['last_child'].'><input id="check-'.$c_id.'" type="checkbox" name="news_groups_cat_id[]" value="'.$c_id.'" '.$category_selected.'/>'.$content_arr['text'].'</li>';
			
			for ($ttl=0; $ttl < ($current_level - $next_level); $ttl++) {
				echo 										'</ul></li>';
			}
		}
	}
?>
														</ul>
														</li>
													</ul>
					    						</td>
					    						<td>&nbsp;</td>
					    						<td class="main" valign="top"></td> 
					    					</tr>
					    				</table>
            						</td>
          						</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
<?	if (isset($_GET['latest_news_id'])) { ?>
          						<tr>
            						<td class="main"><?=TEXT_LATEST_NEWS_DATE?></td>
            						<td class="main"><?=tep_draw_input_field('date_added', $latest_news['date_added'], '', true)?></td>
          						</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          						</tr>
<?	} ?>
		  						<tr>
            						<td class="main"><?=TABLE_HEADING_LATEST_NEWS_URL?></td>
            						<td class="main"><?=tep_draw_input_field('url', $latest_news['url'], '', false)?><a href="<?=HTTP_CATALOG_SERVER.'-ln-'.$_GET['latest_news_id'].'.ogm?preview=1'?>">Preview News</a></td> 
          						</tr>
          						<tr>
            						<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          						</tr>
        					</table>
							<script>
								<!--
								/*************************************************************
									Highlight the slected category for multiple selection box 
									when form is loaded if it is selected previously.		  
								*************************************************************/
								
								jQuery(document).ready(function() {
									/*************************************************************
										For content editor.										  
									*************************************************************/
									var config = new Object();  // create new config object
									config.width = "70%";
							        config.height = "200px";
							        config.bodyStyle = 'background-color: <?php echo HTML_AREA_WYSIWYG_BG_COLOUR; ?>; font-family: "<?php echo HTML_AREA_WYSIWYG_FONT_TYPE; ?>"; color: <?php echo HTML_AREA_WYSIWYG_FONT_COLOUR; ?>; font-size: <?php echo HTML_AREA_WYSIWYG_FONT_SIZE; ?>pt;';
							        config.debug = <?php echo HTML_AREA_WYSIWYG_DEBUG; ?>;
							        editor_generate('content',config);
							        /*************************************************************
										For summary editor.										  
									*************************************************************/
							        config.width = "70%";
							        config.height = "90px";
							        editor_generate('summary',config);
								});
								function validateNewsContent(lang_id,default_lang) {
									var headline_tab = jQuery("#headline_tab_"+ lang_id +"").val();
									var content_tab = jQuery("#content_tab_"+ lang_id +"").val();
									if (headline_tab === '') {
										jQuery("#lang_dd").val(default_lang);
										alert('Please fill in the headline for language '+ lang_id +' !');
									} else if (content_tab === '') {
										jQuery("#lang_dd").val(default_lang);
										alert('Please fill in the content for language '+ lang_id +' !');
									}
								}
								function check_form() {
						        	if (document.getElementById('headline_tab_1').value == "" && document.getElementById('headline_tab_2').value == "" && document.getElementById('headline_tab_3').value == "" ) {
										alert("The Headline is requested!");
										document.getElementById('headline_tab_1').focus();
										document.getElementById('headline_tab_1').select();
										return false;
									}
	
									<? if ($seo_url_alias_latest_news_permission) { ?>
										var seo_url_alias = document.getElementById('latest_news_url_alias');
	
										if (seo_url_alias.value == "") {
											alert('<?=JS_TEXT_SEO_ALIAS_REQUESTED?>');
											seo_url_alias.focus();
											seo_url_alias.select();
											return false;
										}
	
										if (validate_seo(seo_url_alias)) {
											alert ('<?=JS_TEXT_SEO_SPECIAL_CHARACTER?>');
											seo_url_alias.focus();
											seo_url_alias.select();
											return false;
										}
									<? }?>
								}
								function auto_generate_seo() { //auto generate SEO URL alias
									var latest_new_title;
									var latest_new_url_name = document.getElementById('latest_news_url_alias');
									
									if (document.getElementById('headline_tab_1').value !== "") {
										latest_new_title = document.getElementById('headline_tab_1');
									} else if (document.getElementById('headline_tab_2').value !== "") {
										latest_new_title = document.getElementById('headline_tab_2');
									} else {
										latest_new_title = document.getElementById('headline_tab_3');
									}
									
									if (document.getElementById('auto_seo').checked == true) {
										var str = filter_special_char(latest_new_title.value);
										latest_new_url_name.value = str.toLowerCase(); //to lowercase
									} else {
										latest_new_url_name.value = "";
									}
								}
	
								function showHideInfoHistory(criteria_div, criteria_link_div, classtype) {
									DOMCall(criteria_div).className = classtype;
									if (classtype == 'show') {
										DOMCall(criteria_link_div).innerHTML = '<a href="javascript:;" onclick="showHideInfoHistory(\''+criteria_div+'\', \''+criteria_link_div+'\', \'hide\');">'+'<?=LINK_HIDE_INFO_HISTORY_BOX?>'+'</a>';
									} else {
										DOMCall(criteria_link_div).innerHTML = '<a href="javascript:;" onclick="showHideInfoHistory(\''+criteria_div+'\', \''+criteria_link_div+'\', \'show\');">'+'<?=LINK_SHOW_INFO_HISTORY_BOX?>'+'</a>';
									}
								}
						       	//-->
							</script>
        				</td>
      				</tr>
      				<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>				
        				<td class="main" align="right">
          				<?=(isset($_GET['latest_news_id']) ? tep_image_submit('button_update.gif', IMAGE_UPDATE) : tep_image_submit('button_insert.gif', IMAGE_INSERT)) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_LATEST_NEWS, 'grp_id='.$latest_news_grp_id.'&latest_news_id=' . $_GET['latest_news_id']) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
        				</td>
      				</form>
      				</tr>
<?
} else { 
?>
      				<tr>
        				<td>
          					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<? echo tep_draw_form('filter_news_grp', FILENAME_LATEST_NEWS, '', 'post'); ?>
            					<tr>
              						<td class="pageHeading" valign="top"><?=HEADING_TITLE. " - ".$latest_news_top_title?></td>
              						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
              						<td class="smallText" align="right"><?=HEADING_TITLE_SEARCH . ' ' . tep_draw_pull_down_menu('grp_id', array_merge(array(array('id' => '0', 'text' => PULL_DOWN_DEFAULT)), $latest_news_grp_select_array), $latest_news_grp_id, 'onChange="this.form.submit();"')?></td>
            					</tr>
            					</form>
          					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
                								<td class="dataTableHeadingContent">#</td>
                								<td class="dataTableHeadingContent"><?=TABLE_HEADING_LATEST_NEWS_HEADLINE?></td>
								                <td class="dataTableHeadingContent"><?=TEXT_LATEST_NEWS_DATE?></td>
								                <td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_LATEST_NEWS_STATUS?></td>
<?	if (!$latest_news_grp_id) { ?>
								                <td class="dataTableHeadingContent"><?=TABLE_HEADING_LATEST_NEWS_TYPE?></td>
<?	} ?>
								                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_LATEST_NEWS_ACTION?></td>
              								</tr>
<?	if (!isset($lng) || (isset($lng) && !is_object($lng))) {
  		include(DIR_WS_CLASSES . 'language.php');
  		$lng = new language;
	}
	
	while (list($key, $value) = each($lng->catalog_languages)) {
		$languages_ids[$value['id']] = $key;
	}
	
    $rows = 0;
    $latest_news_count = 0;
	
	if ($latest_news_grp_id) {
		$latest_news_select_sql = '	SELECT lnd.news_id, lnd.headline, lnd.content, ln.custom_products_type, ln.date_added, lnd.language_id, 
											ln.status, ln.news_groups_id, ln.url 
									FROM ' . TABLE_LATEST_NEWS .' AS ln 
									INNER JOIN ' . TABLE_LATEST_NEWS_DESCRIPTION .' AS lnd 
										ON (ln.news_id = lnd .news_id) ' . 
									$sql_where_str . 
									'ORDER BY date_added DESC';
		$latest_news_query = tep_db_query($latest_news_select_sql);
		while ($latest_news = tep_db_fetch_array($latest_news_query)) {
			$latest_news_count++;
			$rows++;
			
			if ($latest_news ['language_id'] == '1') {
				if (($_REQUEST['latest_news_id']=="" || (isset($_REQUEST['latest_news_id']) && ($_REQUEST['latest_news_id'] == $latest_news['news_id']))) && !isset($newsInfo)) {
					$newsInfo = new objectInfo($latest_news);
				}
				
				if (isset($newsInfo) && is_object($newsInfo) && ($latest_news['news_id'] == $newsInfo->news_id)) {
					echo '             			<tr class="dataTableRowSelected" onmouseover="this.style.cursor=\'hand\'" onclick="document.location.href=\'' . tep_href_link(FILENAME_LATEST_NEWS, 'grp_id='.$latest_news_grp_id.'&latest_news_id=' . $latest_news['news_id']) . '\'">' . "\n";
				} else {
					echo '             			<tr class="dataTableRow" onmouseover="this.className=\'dataTableRowOver\';this.style.cursor=\'hand\'" onmouseout="this.className=\'dataTableRow\'" onclick="document.location.href=\'' . tep_href_link(FILENAME_LATEST_NEWS, 'grp_id='.$latest_news_grp_id.'&latest_news_id=' . $latest_news['news_id']) . '\'">' . "\n";
				}
?> 
													<td class="dataTableContent"><?='&nbsp;' . $latest_news['news_id']?></td>
													<td class="dataTableContent"><?=$latest_news['headline']?></td>
													<td class="dataTableContent"><?=tep_date_short($latest_news["date_added"])?></td>
													<td class="dataTableContent" align="center">
<?
				if ($latest_news['status'] == '1') {
					echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_LATEST_NEWS, 'action=setflag&flag=0&grp_id='.$latest_news_grp_id.'&latest_news_id=' . $latest_news['news_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
				} else {
					echo '<a href="' . tep_href_link(FILENAME_LATEST_NEWS, 'action=setflag&flag=1&grp_id='.$latest_news_grp_id.'&latest_news_id=' . $latest_news['news_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
				}
?>
													</td>
<?
				if (!$latest_news_grp_id) {
					echo '							<td class="dataTableContent">'.$latest_news_grp_array[$latest_news["news_groups_id"]].'</td>';
				}
?>
													<td class="dataTableContent" align="right">
													<?
													if (isset($newsInfo) && is_object($newsInfo) && ($latest_news['news_id'] == $newsInfo->news_id)) {
														echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif', '');
													} else {
														echo '<a href="' . tep_href_link(FILENAME_LATEST_NEWS, 'grp_id='.$latest_news_grp_id.'&latest_news_id=' . $latest_news['news_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>';
													}
?>&nbsp;
													</td>
												</tr>
<?			} 
		} 
	}
?> 
              								<tr>
                								<td colspan="6">               									
                									<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  										<tr>
                    										<td class="smallText"><?='<br>' . TEXT_NEWS_ITEMS . '&nbsp;' . $latest_news_count?></td>
                    										<td align="right" class="smallText">
                    											<?='<a href="' . tep_href_link(FILENAME_LATEST_NEWS, 'grp_id='.$latest_news_grp_id.'&action=new_latest_news') . '">' . tep_image_button('button_new_news_item.gif', IMAGE_NEW_NEWS_ITEM) . '</a>'?>
                    										</td>
                  										</tr>
                									</table>
                								</td>
              								</tr>
            							</table>
            						</td> 
<?
	$heading = array();
    $contents = array();
    switch ($_GET['action']) {
      	case 'delete_latest_news': //generate box for confirming a news article deletion
        	$heading[] = array('text'   => '<b>' . TEXT_INFO_HEADING_DELETE_ITEM . '</b>');   
        	$contents = array('form'    => tep_draw_form('news', FILENAME_LATEST_NEWS, 'action=delete_latest_news_confirm') . tep_draw_hidden_field('latest_news_id', $_GET['latest_news_id']). tep_draw_hidden_field('grp_id', $latest_news_grp_id));
        	$contents[] = array('text'  => TEXT_DELETE_ITEM_INTRO);
        	$contents[] = array('text'  => '<br><b>' . $newsInfo->headline . '</b>');
        	$contents[] = array('align' => 'center',
                            	'text'  => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_LATEST_NEWS, 'grp_id='.$latest_news_grp_id.'&latest_news_id=' . $newsInfo->news_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
        	break;
      	default:
        	if ($rows > 0) {
          		if (isset($newsInfo) && is_object($newsInfo)) { //an item is selected, so make the side box
            		$heading[] = array('text' => '<b>' . $newsInfo->headline . '</b>');
            		$contents[] = array('align' => 'center', 
                                		'text' => '<a href="' . tep_href_link(FILENAME_LATEST_NEWS, 'grp_id='.$latest_news_grp_id.'&latest_news_id=' . $newsInfo->news_id . '&action=new_latest_news') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_LATEST_NEWS, 'latest_news_id=' . $newsInfo->news_id . '&grp_id='.$latest_news_grp_id.'&action=delete_latest_news') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');
            		$contents[] = array('text' => '<a href="'.HTTP_CATALOG_SERVER.'-ln-'.$newsInfo->news_id.'.ogm?preview=1" target="blank">Preview News</a>');
            		$contents[] = array('text' => '<br>' . $newsInfo->content);
          		}
        	} else { // create category/product info
          		$heading[] = array('text' => '<b>' . EMPTY_CATEGORY . '</b>');

          		$contents[] = array('text' => sprintf(TEXT_NO_CHILD_CATEGORIES_OR_PRODUCTS, $parent_categories_name));
        	}
        	break;
	}
	
    if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
      	echo '            			<td width="25%" valign="top">' . "\n";
		
      	$box = new box;
      	echo $box->infoBox($heading, $contents);
      	echo '            			</td>' . "\n";
    }
?>
          						</tr>
        					</table>
        				</td>
      				</tr>
<?
}
?>
				</table>
			</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<script language="javascript" src="includes/javascript/modal_win.js"></script>
<script language="javascript" src="includes/javascript/jquery.selectboxes.js"></script>
<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
<script language="javascript" src="includes/javascript/checktree/checktree.js"></script>
<script language="Javascript1.2">
	<!-- // load htmlarea
	// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Newsletter <head>
    _editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
    var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
	if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
	if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
	if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
	<?
	if (HTML_AREA_WYSIWYG_BASIC_NEWSLETTER == 'Basic') {
	?>
		if (win_ie_ver >= 5.5) {
       		document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
       		document.write(' language="Javascript1.2"></scr' + 'ipt>');
		} else {
			document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>');
		}
	<?
	} else {
	?>
		if (win_ie_ver >= 5.5) {
	       	document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
       		document.write(' language="Javascript1.2"></scr' + 'ipt>');
		} else {
			document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>');
		}
	<?}?>

	function verify_site_selection(site_obj) {
		if (site_obj != null) {
			var cur_site_id = site_obj.value;
			var disabled_mode = site_obj.checked ? false : true;

			if (cur_site_id == '0') {
				var sub_status_select = document.new_latest_news.elements['custom_products_type_id[]'];

				if (typeof(sub_status_select) != 'undefined') {
					if (typeof(sub_status_select.length) != 'undefined') {
						for (sub_cnt=0; sub_cnt < sub_status_select.length; sub_cnt++) {
							sub_status_select[sub_cnt].disabled = disabled_mode;
							sub_status_select[sub_cnt].checked = !disabled_mode;
						}
					} else {
						sub_status_select.disabled = disabled_mode;
						sub_status_select.checked = !disabled_mode;
					}
				}
			}
		}
	}

	function extraLatestNewsCheck() {
        if (DOMCall('buyback_site_display').checked == true) {
	        DOMCall('extra_news_display_sites').disabled = false;
	    } else {
	        DOMCall('extra_news_display_sites').checked = false;
	        DOMCall('extra_news_display_sites').disabled = true;
	    }
	}
	// -->
</script>
<script language="javascript">
    extraLatestNewsCheck();
</script>
<script type="text/javascript"><!--

 // USAGE NOTES: Create a new CheckTree() object like so, and pass it its own name.
 var checkmenu = new CheckTree('checkmenu');
 // You can create several such tree objects, just give each a unique name.

 // One optional property: whether to count all checkboxes beneath the current level,
 // or just to count the checkboxes immediately beneath the current level (the default).
 //checkmenu.countAllLevels = true;
 SetFocus();
 //--></script>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>