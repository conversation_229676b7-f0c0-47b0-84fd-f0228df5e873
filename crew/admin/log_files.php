<?
/*
  	$Id: log_files.php,v 1.33 2010/03/15 09:22:58 henry.chow Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'log.php');

$log_object = new log_files($login_id);

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if ($_REQUEST['action'] == 'show_log') {
	if ($_REQUEST['cont'] && !isset($_SESSION['ori_log_select_sql'])) {
		tep_redirect(tep_href_link(FILENAME_LOG_FILES));
	}
}

if (tep_not_null($action))
{
	switch ($action) {
     	case 'deleteconfirm':
        	$log_id = tep_db_prepare_input($_REQUEST['cID']);
        	tep_db_query("delete from " . TABLE_LOG_FILES . " where log_id= " . $log_id);      
        	tep_redirect(tep_href_link('log_files.php', tep_get_all_get_params(array('cID', 'action')))); 
        	break;
        case 'reset_session':
        	unset($_SESSION['post_var']);
        	tep_redirect(tep_href_link(FILENAME_LOG_FILES));
        	break;
	}
}

$orders_status_array = array();
$orders_status_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '" . (int)$languages_id . "'");
while ($orders_status = tep_db_fetch_array($orders_status_query)) {
    $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];                       
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<META Http-Equiv="Cache-Control" Content="no-cache">
	<META Http-Equiv="Pragma" Content="no-cache">
	<META Http-Equiv="Expires" Content="0"> 
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
  	if ($_REQUEST['action'] == 'show_log') {
  		if (!$_REQUEST['cont']) {
  			unset($_SESSION['ori_log_select_sql']);
  			
	  		$log_query_str = "select log.*, p.products_id, p.products_cat_path, pd.products_name, pc.categories_id from " . TABLE_LOG_TABLE . " as log left join " . TABLE_PRODUCTS . " as p on log.log_products_id=p.products_id left join " . TABLE_PRODUCTS_DESCRIPTION . " as pd on p.products_id=pd.products_id left join " . TABLE_PRODUCTS_TO_CATEGORIES . " as pc on pd.products_id=pc.products_id ";
	  		$where_str = " where (pc.categories_id IS NULL OR pc.products_is_link=0) and (pd.language_id = $languages_id or pd.language_id IS NULL)";
	  		
	  		if ($_REQUEST["product_id"]) {
	  			$where_str .= " and IF(LOCATE('##',log.log_products_id), log.log_products_id LIKE '" . $_REQUEST["product_id"] . "##%', log.log_products_id = '" . $_REQUEST["product_id"] . "') ";
	  		}
	  		
	  		if ($_REQUEST["modified_by"]) {
	  			$where_str .= " and log_admin_id='" . $_REQUEST["modified_by"] . "' ";
	  		}
	  		
	  		$admin_file_cat_ids_array = tep_get_admin_file_cat_permissions(FILENAME_LOG_FILES);
	  		if (!in_array(0, $admin_file_cat_ids_array)) {
	  			$admin_permitted_cat_array = tep_get_eligible_categories(FILENAME_LOG_FILES, '', 0);
	  			
	  			$where_str .= " and pc.categories_id IN ('" . implode("', '", $admin_permitted_cat_array) . "') ";
	  		}
	  		
	  		$qty_adjustment_type = array();
	  		if (count($_REQUEST["restock_opt"])) {
  				$qty_adjustment = array();
  				if (in_array("1", $_REQUEST["restock_opt"])) {
  					if (count($_REQUEST["qty_types"]) == 2) {
  						$qty_adjustment[] = " CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, 1, INSTR(log.log_from_value, ':~:' ) -1 ), log.log_from_value) AS SIGNED) < CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, 1, INSTR(log.log_to_value, ':~:' ) -1 ), log.log_to_value) AS SIGNED) or CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, INSTR(log.log_from_value, ':~:' )+3 ), 0) AS SIGNED) < CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, INSTR(log.log_to_value, ':~:' )+3 ), 0) AS SIGNED)";
  					} else if (in_array("available_qty", $_REQUEST["qty_types"])) {
  						$qty_adjustment[] = " CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, 1, INSTR(log.log_from_value, ':~:' ) -1 ), log.log_from_value) AS SIGNED) < CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, 1, INSTR(log.log_to_value, ':~:' ) -1 ), log.log_to_value) AS SIGNED) ";
  					} else if (in_array("actual_qty", $_REQUEST["qty_types"])) {
  						$qty_adjustment[] = " CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, INSTR(log.log_from_value, ':~:' )+3 ), 0) AS SIGNED) < CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, INSTR(log.log_to_value, ':~:' )+3 ), 0) AS SIGNED) ";
  					}
  				}
  				
  				if (in_array("2", $_REQUEST["restock_opt"])) {
  					if (count($_REQUEST["qty_types"]) == 2) {
  						$qty_adjustment[] = " CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, 1, INSTR(log.log_from_value, ':~:' )-1 ), log.log_from_value) AS SIGNED) > CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, 1, INSTR(log.log_to_value, ':~:' )-1 ), log.log_to_value) AS SIGNED) or CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, INSTR(log.log_from_value, ':~:' )+3 ), 0) AS SIGNED) > CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, INSTR(log.log_to_value, ':~:' )+3 ), 0) AS SIGNED)";
  					} else if (in_array("available_qty", $_REQUEST["qty_types"])) {
  						$qty_adjustment[] = " CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, 1, INSTR(log.log_from_value, ':~:' )-1 ), log.log_from_value) AS SIGNED) > CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, 1, INSTR(log.log_to_value, ':~:' )-1 ), log.log_to_value) AS SIGNED) ";
  					} else if (in_array("actual_qty", $_REQUEST["qty_types"])) {
  						$qty_adjustment[] = " CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, INSTR(log.log_from_value, ':~:' )+3 ), 0) AS SIGNED) > CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, INSTR(log.log_to_value, ':~:' )+3 ), 0) AS SIGNED) ";
  					}
  				}
  				
  				if (count($qty_adjustment)) {
  					$qty_adjustment_type[] = ' (trim(log.log_system_messages) like "Quantity Adjustment%" and (' . implode(" or ", $qty_adjustment) . ')) ';
  				}
  				
  				if (in_array("3", $_REQUEST["restock_opt"])) {
  					if (count($_REQUEST["qty_types"]) == 2) {
  						$qty_adjustment_type[] = " log.log_field_name = 'products_quantity' and (log.log_system_messages REGEXP \"^##[0-9]+##: (Refund|Compensation|Compensate Delivery|Partial Delivery Sales|##[0-9]+## ##[0-9]+##)$\" or log.log_system_messages REGEXP \"^##(BUYBACK:|SUPPLIER_ORDER:)[0-9]+##$\") ";
  					} else if (in_array("available_qty", $_REQUEST["qty_types"])) {
  						$qty_adjustment_type[] = " log.log_field_name = 'products_quantity' and " . " (log.log_system_messages REGEXP \"^##[0-9]+##: ##[0-9]+## ##[0-9]+##$\" or (log.log_system_messages REGEXP \"^(Product Added|Product Deleted).*$\") or (log.log_system_messages REGEXP \"^##(BUYBACK:|SUPPLIER_ORDER:)[0-9]+##$\") ) and CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, 1, INSTR(log.log_from_value, ':~:' )-1 ), log.log_from_value) AS SIGNED) <> CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, 1, INSTR(log.log_to_value, ':~:' )-1 ), log.log_to_value) AS SIGNED) ";
  					} else if (in_array("actual_qty", $_REQUEST["qty_types"])) {
  						$qty_adjustment_type[] = " log.log_field_name = 'products_quantity' and " . " (log.log_system_messages REGEXP \"^##[0-9]+##: (Partial Delivery Sales|##[0-9]+## ##[0-9]+##)$\" or (log.log_system_messages REGEXP \"^##(BUYBACK:|SUPPLIER_ORDER:)[0-9]+##$\") or (log.log_system_messages REGEXP \"^(Product Added|Product Deleted).*$\")) and ( CAST( IF(LOCATE(':~:', log.log_from_value), substring(log.log_from_value, INSTR(log.log_from_value, ':~:' )+3 ), 0) AS SIGNED) <> CAST( IF(LOCATE(':~:', log.log_to_value), substring(log.log_to_value, INSTR(log.log_to_value, ':~:' )+3 ), 0) AS SIGNED) )";
  					}
  				}	
	  		}
	  		
	  		if (!tep_not_null($_REQUEST["all_changes"])) {
	  			$changes_array = array();
	  			
	  			if (tep_not_null($_REQUEST["products_price"])) {
	  				$changes_array[] = " log_field_name='" . $_REQUEST["products_price"] . "'";
	  			}
	  			
	  			if (tep_not_null($_REQUEST["products_quantity"])) {
	  				// added here
	  				$changes_array[] = " log_field_name='" . $_REQUEST["products_quantity"] . "' " . 
	  									(count($qty_adjustment_type) ? ' and (' . implode('or', $qty_adjustment_type) .')': '');
	  			}
	  			
	  			if (count($changes_array)) {
	  				$where_str .= " and (" . implode(" or ", $changes_array) . ") ";
	  			}
	  		}
			
	  		if ($_REQUEST["start_date"]) {
	  			if (strpos($_REQUEST["start_date"], ':') !== false) {
	  				$startDateObj = explode(' ', trim($_REQUEST["start_date"]));
	  				list($yr, $mth, $day) = explode('-', $startDateObj[0]);
	  				list($hr, $min) = explode(':', $startDateObj[1]);
	  				$where_str .= " and (log.log_time >= '".date('Y-m-d H:i:s', mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."')";
	  			} else {
	  				list($yr, $mth, $day) = explode('-', trim($_REQUEST["start_date"]));
	  				$where_str .= " and (log.log_time >= '".date('Y-m-d H:i:s', mktime(0, 0, 0, $mth, $day, $yr))."')";
	  			}
	  		}
	  		
	  		if ($_REQUEST["end_date"]) {
	  			if (strpos($_REQUEST["end_date"], ':') !== false) {
	  				$endDateObj = explode(' ', trim($_REQUEST["end_date"]));
	  				list($yr, $mth, $day) = explode('-', $endDateObj[0]);
	  				list($hr, $min) = explode(':', $endDateObj[1]);
	  				$where_str .= " and (log.log_time <= '".date('Y-m-d H:i:s', mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."')";
	  			} else {
	  				list($yr, $mth, $day) = explode('-', $_REQUEST["end_date"]);
	  				$where_str .= " and (log.log_time <= '".date('Y-m-d H:i:s', mktime(23, 59, 59, $mth, $day, $yr))."')";
	  			}
	  		}
			
	  		if ($_REQUEST["sort_order"]) {
	  			$sort_str = " order by log_time " . $_REQUEST["sort_order"];
	  		}
	  		$log_select_sql = $log_query_str . $where_str . $sort_str;
	  		
	  		$_SESSION['ori_log_select_sql'] = $log_select_sql;
	  		
	  		$_SESSION['post_var']['product_id'] = $_REQUEST["product_id"];
	  		$_SESSION['post_var']['modified_by'] = $_REQUEST["modified_by"];
	  		$_SESSION['post_var']['start_date'] = trim($_REQUEST["start_date"]);
	  		$_SESSION['post_var']['end_date'] = trim($_REQUEST["end_date"]);
	  		$_SESSION['post_var']['all_changes'] = $_REQUEST["all_changes"];
	  		$_SESSION['post_var']['products_price'] = $_REQUEST["products_price"];
	  		$_SESSION['post_var']['products_quantity'] = $_REQUEST["products_quantity"];
	  		$_SESSION['post_var']['restock_opt'] = $_REQUEST["restock_opt"];
	  		$_SESSION['post_var']['qty_types'] = $_REQUEST["qty_types"];	// available or actual quantity
	  		$_SESSION['post_var']['sort_order'] = $_REQUEST["sort_order"];
	  	} else {
	  		$log_select_sql = $_SESSION['ori_log_select_sql'];
	  	}
		
		$filter_by_prod = ($_SESSION['post_var']['product_id']) ? 1 : 0;
		
		$DeletedProductPattern = "/([^#]+)(?:##)(.*?)(\(Deleted:)(.*)/is";
		
		if ($filter_by_prod) {
			ob_start();
			$prod_info_select_sql = "	SELECT p.products_cat_path, pd.products_name 
		  								FROM " . TABLE_PRODUCTS . " AS p 
		  								LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
		  									ON p.products_id=pd.products_id 
		  								WHERE p.products_id='".$_SESSION['post_var']['product_id']."' AND (pd.language_id = $languages_id or pd.language_id IS NULL) ";
			$prod_info_result_sql = tep_db_query($prod_info_select_sql);
			if ($prod_info_row = tep_db_fetch_array($prod_info_result_sql)) {
				$product_id_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path(tep_get_actual_product_cat_id($_SESSION['post_var']['product_id'])) . '&pID=' . $_SESSION['post_var']['product_id'] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-size:12px; font-weight:bold;" title="'.TEXT_PRODUCTS_ID.' '.$_SESSION['post_var']['product_id'].'">' . $_SESSION['post_var']['product_id'] . '</a>';
				$product_cat_path = $prod_info_row["products_cat_path"];
				$product_name = $prod_info_row["products_name"];
?>
					<tr>
						<td class="main" width="10%" valign="top">Product ID:</td>
						<td class="main"><?=$product_id_link?></td>
					</tr>
					<tr>
						<td class="main" valign="top">Category Path:</td>
						<td class="main"><?=$product_cat_path?></td>
					</tr>
					<tr>
						<td class="main" valign="top">Product Name:</td>
						<td class="main"><?=$product_name?></td>
					</tr>
<?
			} else {
				$log_prod_select_sql = "	SELECT log_products_id 
			  								FROM " . TABLE_LOG_TABLE . " 
			  								WHERE 1 AND IF(LOCATE('##',log_products_id), log_products_id LIKE '" . $_SESSION['post_var']['product_id'] . "##%', log_products_id = '" . $_SESSION['post_var']['product_id'] . "')";
				$log_prod_result_sql = tep_db_query($log_prod_select_sql);
				if ($log_prod_row = tep_db_fetch_array($log_prod_result_sql)) {
					// this product had been deleted
					if(preg_match($DeletedProductPattern, $log_prod_row["log_products_id"], $regs)) {
?>
						<tr>
							<td class="main" width="10%" valign="top">Product ID:</td>
							<td class="main"><?=$regs[1]?></td>
						</tr>
						<tr>
							<td class="main" valign="top">Product Name:</td>
							<td class="main"><?=$regs[2].$regs[3].$regs[4]?></td>
						</tr>
<?
					} else {
						$products_info = $log_prod_row["log_products_id"];
?>
						<tr>
							<td class="main" width="10%" valign="top">Product ID:</td>
							<td class="main"><?=$_SESSION['post_var']['product_id']?></td>
						</tr>
						<tr>
							<td class="main" valign="top">Product Name:</td>
							<td class="main">This product had been deleted.<br>Unable to retrive information on product deletion.</td>
						</tr>
<?
					}
				}
			}
			$single_search_prod_info = ob_get_contents();
			ob_end_clean();
		}
?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
			        				<td>
			        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
			          						<tr>
			            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
			            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
			          						</tr>
			        					</table>
			        				</td>
			      				</tr>
			      				<?
			      				if ($filter_by_prod) {
			      				?>
			      				<tr>
			      					<td>
			      						<table border="0" width="100%" cellspacing="1" cellpadding="2">
			      							<?=$single_search_prod_info?>
			      						</table>
			      					</td>
			      				</tr>
			      				<?}?>
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="1" cellpadding="1">
               								<tr>
			       								<td class="reportBoxHeading" width="8%"><?=TABLE_HEADING_TIME?></td>
			       								<?
			       								if (!$filter_by_prod) {
			       									echo '<td class="reportBoxHeading">'.TABLE_HEADING_PRODUCTS.'</td>';
			       								}
			       								?>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_SYSTEM_MSG?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_OLD?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_NEW?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_ADMIN?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_IP?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_USER_MSG?></td>
			   								</tr>
<?
		$log_split_object = new splitPageResults($_REQUEST["page"], MAX_DISPLAY_SEARCH_RESULTS, $log_select_sql, $log_sql_numrows);
    	$log_query = tep_db_query($log_select_sql);
    	
		$row_count = 0;
		
		$SalesPattern = "/(?:##)(\d+)(?:##:)(?:\s?)(?:##)(\d+)(?:##)(?:\s?)(?:##)(\d+)(?:##)/is";
		$SalesOrderLink = "<a href=" . tep_href_link(FILENAME_ORDERS, "action=edit&oID=##oID##") . " target='_blank'>Order ID (##oID##)</a>";
		
		$PartialDeliveryPattern = "/(?:##)(\d+)(?:##:)(?:\s?)(Refund|Compensation|Compensate Delivery|Partial Delivery Sales)/is";
		
		$BuybackPattern = "/(?:##)(BUYBACK|SUPPLIER_ORDER)(?::)(\d+)(?:##)/is";
		$BuybackOrderLink = '<a href="%s" target="_blank">%s (%s)</a>';
		/*
		$SalesPattern2 = "/([^#]+)(?:##)(\d+)(?:##)([^#]*)/is";
		$SalesReplacement2 = "\$1 <a href=" . tep_href_link(FILENAME_ORDERS, "action=edit&oID=$2") . " target='_blank'>Order ID ($2)</a> \$3";
		*/
    	while ($log_row = tep_db_fetch_array($log_query)) {
    		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
    		if (is_numeric($log_row["log_admin_id"])) {
    			$admin_query = tep_db_query("SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $log_row["log_admin_id"] . "'");
    			if ($admin_info = tep_db_fetch_array($admin_query))
    				$admin_email_address = $admin_info["admin_email_address"];
    			else
    				$admin_email_address = $log_row["log_admin_id"];
    		} else {
    			$admin_email_address = $log_row["log_admin_id"];
    		}
    		
    		$from_value_str = $from_value = $log_row["log_from_value"];
    		$to_value_str = $to_value = $log_row["log_to_value"];
    		
    		$system_comment = $log_row["log_system_messages"];
    		if ($log_row["log_field_name"] == "products_quantity") {
    			if (strpos($from_value, ':~:')) {
    				$cell_1_1 = $cell_1_2 = $cell_2_1 = $cell_2_2 = '&nbsp;';
    				list($available_from_value, $actual_from_value) = explode(':~:', $from_value);
    				if (isset($_SESSION['post_var']['qty_types']) && is_array($_SESSION['post_var']['qty_types'])) {
    					if (in_array("available_qty", $_SESSION['post_var']['qty_types'])) {
    						$cell_1_1 = '##avail_from##Available:##end_avail_from##';
    						$cell_1_2 = '##avail_from##'.$available_from_value.'##end_avail_from##';
    					}
    					if (in_array("actual_qty", $_SESSION['post_var']['qty_types'])) {
    						$cell_2_1 = '##actual_from##Actual:##end_actual_from##';
    						$cell_2_2 = '##actual_from##'.$actual_from_value.'##end_actual_from##';
    					}
    				} else {
    					$cell_1_1 = '##avail_from##Available:##end_avail_from##';
    					$cell_1_2 = '##avail_from##'.$available_from_value.'##end_avail_from##';
    					$cell_2_1 = '##actual_from##Actual:##end_actual_from##';
    					$cell_2_2 = '##actual_from##'.$actual_from_value.'##end_actual_from##';
    				}
    				$from_value_str = '<table width="100%" cellspacing="0" cellpadding="0"><tr><td class="reportRecords" align="left">'.$cell_1_1.'</td><td class="reportRecords" align="right" nowrap>'.$cell_1_2.'</td></tr><tr><td class="reportRecords" align="left">'.$cell_2_1.'</td><td class="reportRecords" align="right" nowrap>'.$cell_2_2.'</td></tr></table>';
				} else {
					$from_value_str = $available_from_value = $from_value;
					$actual_from_value = '';
				}
    			
    			if (strpos($to_value, ':~:')) {
    				$cell_1_1 = $cell_1_2 = $cell_2_1 = $cell_2_2 = '&nbsp;';
    				list($available_to_value, $actual_to_value) = explode(':~:', $to_value);
    				if (isset($_SESSION['post_var']['qty_types']) && is_array($_SESSION['post_var']['qty_types'])) {
    					if (in_array("available_qty", $_SESSION['post_var']['qty_types'])) {
    						$cell_1_1 = '##avail_to##Available:##end_avail_to##'; 
    						$cell_1_2 = '##avail_to##'.$available_to_value.'##end_avail_to##';
    					}
    					if (in_array("actual_qty", $_SESSION['post_var']['qty_types'])) {
    						$cell_2_1 = '##actual_to##Actual:##end_actual_to##'; 
    						$cell_2_2 = '##actual_to##'.$actual_to_value.'##end_actual_to##';
    					}
    				} else {
    					$cell_1_1 = '##avail_to##Available:##end_avail_to##';
    					$cell_1_2 = '##avail_to##'.$available_to_value.'##end_avail_to##';
    					$cell_2_1 = '##actual_to##Actual:##end_actual_to##';
    					$cell_2_2 = '##actual_to##'.$actual_to_value.'##end_actual_to##';
    				}
    				$to_value_str = '<table width="100%" cellspacing="0" cellpadding="0"><tr><td class="reportRecords" align="left">'.$cell_1_1.'</td><td class="reportRecords" align="right" nowrap>'.$cell_1_2.'</td></tr><tr><td class="reportRecords" align="left">'.$cell_2_1.'</td><td class="reportRecords" align="right" nowrap>'.$cell_2_2.'</td></tr></table>';
				} else {
					$to_value_str = $available_to_value = $to_value;
					$actual_to_value = '';
				}
				
				if (tep_not_null($available_to_value) && tep_not_null($available_from_value)) {
					if ($available_to_value != $available_from_value) {
						$from_value_str = str_replace(array('##avail_from##', '##end_avail_from##'), array('<b>', '</b>'), $from_value_str);
						$to_value_str = str_replace(array('##avail_to##', '##end_avail_to##'), array('<b>', '</b>'), $to_value_str);
					} else {
						$from_value_str = str_replace(array('##avail_from##', '##end_avail_from##'), array('', ''), $from_value_str);
						$to_value_str = str_replace(array('##avail_to##', '##end_avail_to##'), array('', ''), $to_value_str);
					}
				} else {
					$from_value_str = str_replace(array('##avail_from##', '##end_avail_from##'), array('', ''), $from_value_str);
					$to_value_str = str_replace(array('##avail_to##', '##end_avail_to##'), array('', ''), $to_value_str);
				}
				
				if (tep_not_null($actual_to_value) && tep_not_null($actual_from_value)) {
					if ($actual_to_value != $actual_from_value) {
						$from_value_str = str_replace(array('##actual_from##', '##end_actual_from##'), array('<b>', '</b>'), $from_value_str);
						$to_value_str = str_replace(array('##actual_to##', '##end_actual_to##'), array('<b>', '</b>'), $to_value_str);
					} else {
						$from_value_str = str_replace(array('##actual_from##', '##end_actual_from##'), array('', ''), $from_value_str);
						$to_value_str = str_replace(array('##actual_to##', '##end_actual_to##'), array('', ''), $to_value_str);
					}
				} else {
					$from_value_str = str_replace(array('##actual_from##', '##end_actual_from##'), array('', ''), $from_value_str);
					$to_value_str = str_replace(array('##actual_to##', '##end_actual_to##'), array('', ''), $to_value_str);
				}
				
				$show_qty_changes_detail = false;
				if (strpos($system_comment, 'Quantity Adjustment') === 0) {
					$show_qty_changes_detail = true;
				} else if (preg_match($SalesPattern, $system_comment, $regs)) {
					$show_qty_changes_detail = true;
					$system_comment = str_replace('##oID##', $regs[1], $SalesOrderLink) . ':&nbsp;' . $orders_status_array[$regs[2]] . '&nbsp;->&nbsp;' . $orders_status_array[$regs[3]];
					//$system_comment = preg_replace($SalesPattern, $SalesReplacement, $system_comment);
				} else if (preg_match($PartialDeliveryPattern, $system_comment, $partial_regs)) {
					$show_qty_changes_detail = true;
					$system_comment = str_replace('##oID##', $partial_regs[1], $SalesOrderLink) . ':&nbsp;' . $partial_regs[2];
				} else if (preg_match($BuybackPattern, $system_comment, $buyback_regs)) {
					$show_qty_changes_detail = true;
					if ($buyback_regs[1] == 'SUPPLIER_ORDER') {
						$system_comment = sprintf($BuybackOrderLink, tep_href_link(FILENAME_SUPPLIERS_ORDERS, 'oID='.$buyback_regs[2].'&action=edit'), 'Supplier Order ID', $buyback_regs[2]);
					} else {
						$system_comment = sprintf($BuybackOrderLink, tep_href_link(FILENAME_BUYBACK_REQUESTS_INFO, 'buyback_request_group_id='.$buyback_regs[2]), 'Buyback ID', $buyback_regs[2]);
					}
				}
				
    			if($show_qty_changes_detail) {
					$available_order_qty = $available_to_value - $available_from_value;
					$available_order_qty_display = ($available_order_qty > 0 ? '<span class="greenIndicator">+'.$available_order_qty.'</span>' : ($available_order_qty < 0 ? '<span class="redIndicator">'.$available_order_qty.'</span>' : $available_order_qty) );
					
					if (isset($_SESSION['post_var']['qty_types']) && is_array($_SESSION['post_var']['qty_types'])) {
						if (in_array("available_qty", $_SESSION['post_var']['qty_types']))
							$system_comment .= "<br>Available Qty: $available_order_qty_display";
					} else {
						$system_comment .= "<br>Available Qty: $available_order_qty_display";
					}
					if (tep_not_null($actual_from_value)) {
						$actual_order_qty = $actual_to_value - $actual_from_value;
						$actual_order_qty_display = ($actual_order_qty > 0 ? '<span class="greenIndicator">+'.$actual_order_qty.'</span>' : ($actual_order_qty < 0 ? '<span class="redIndicator">'.$actual_order_qty.'</span>' : $actual_order_qty) );
						
						if (isset($_SESSION['post_var']['qty_types']) && is_array($_SESSION['post_var']['qty_types'])) {
							if (in_array("actual_qty", $_SESSION['post_var']['qty_types']))
								$system_comment .= "<br>Actual Qty: $actual_order_qty_display";
						} else {
							$system_comment .= "<br>Actual Qty: $actual_order_qty_display";
						}
					}
				}
    		}
?>
											<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
												<td class="reportRecords" valign="top"><?=$log_row["log_time"]?></td>
												<?
			       								if (!$filter_by_prod) {
			       									if ($log_row["products_id"]) {
			       										$products_info = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($log_row["categories_id"]) . '&pID=' . $log_row["products_id"] ) . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="'.TEXT_PRODUCTS_ID.' '.$log_row["products_id"].'">' . $log_row["products_name"] . '</a><br><span class="categoryPath">['.$log_row["products_cat_path"].']</span>';
			       									} else {
			       										if(preg_match($DeletedProductPattern, $log_row["log_products_id"], $regs)) {
			       											$products_info = '<span title="'.TEXT_PRODUCTS_ID.' '.$regs[1].'" class="boldText">'.$regs[2].'</span>'.$regs[3].$regs[4];
			       										} else {
			       											$products_info = $log_row["log_products_id"];
			       										}
			       									}
			       									
			       									echo '<td class="reportRecords" valign="top">'.$products_info.'</td>';
			       								}
			       								?>
												<td class="reportRecords" valign="top"><?=$system_comment?></td>
												<td class="reportRecords" valign="top"><?=$from_value_str?></td>
								                <td class="reportRecords" valign="top"><?=$to_value_str?></td>
								                <td class="reportRecords" valign="top"><?=$admin_email_address?></td>
								                <td class="reportRecords" valign="top"><?=$log_row["log_ip"]?></td>
								                <td class="reportRecords" valign="top"><?=$log_row["log_user_messages"]?></td>
											</tr>
<?
			$row_count++;
    	}
?>
			   							</table>
			   						</td>
			   					</tr>
			   					<tr>
			   						<td>
			   							<table border="0" width="100%" cellspacing="1" cellpadding="2">
			   								<tr>
												<td class="smallText" valign="top"><?=$log_split_object->display_count($log_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_LOGS)?></td>
												<td class="smallText" align="right"><?=$log_split_object->display_links($log_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y', 'cID', 'cont'))."cont=1")?></td>
											</tr>
										</table>
									</td>
								</tr>
			   				</table>
			   			</td>
			   		</tr>
<? 	} else {
		unset($_SESSION['ori_log_select_sql']);
?>
			  		<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
									<!--td class="smallText" align="right"><?=HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('search')?></td-->
								</tr>
							</table>
						</td>
					</tr>
<?
  		echo tep_draw_form('log_input', FILENAME_LOG_FILES, tep_get_all_get_params(array('action')) . 'action=show_log', 'post', 'onSubmit="return form_checking();"');
  		
  		$admin_users = array( array ('id' => 0, "text" => "All Admin Users"));
  		$admin_users_select_sql = "	SELECT  admin_id, admin_firstname, admin_lastname, admin_email_address 
  									FROM " . TABLE_ADMIN . "
  									ORDER BY admin_lastname";
  		$admin_users_result_sql = tep_db_query($admin_users_select_sql);
    	while ($admin_users_row = tep_db_fetch_array($admin_users_result_sql)) {
    		$admin_users[] = array	(	"id" => $admin_users_row["admin_id"],
    									"text" => $admin_users_row["admin_lastname"] . ', ' . $admin_users_row["admin_firstname"] . ' (' . $admin_users_row["admin_email_address"] . ')'
    								);
    	}
    	
    	$changes_option = array ( 	array("id" => "all_changes", "text" => "All", "checked" => isset($_SESSION['post_var']) ? ($_SESSION['post_var']["all_changes"]=="All" ? true : false) : true, "params" => 'id="all_changes" ' . "onClick=\"validateCheck('all_changes');\""),
    								array("id" => "products_price", "value" => "products_price", "text" => "Price", "checked" => isset($_SESSION['post_var']) ? ($_SESSION['post_var']["products_price"]=="products_price" ? true : false) : false, "params" => 'id="products_price" ' . "onClick=\"validateCheck('products_price');\""),
    								array("id" => "products_quantity", "value" => "products_quantity", "text" => "Quantity", "checked" => isset($_SESSION['post_var']) ? ($_SESSION['post_var']["products_quantity"]=="products_quantity" ? true : false) : false, "params" => 'id="products_quantity" ' . " onClick=\"validateCheck('products_quantity_default');\"")
    							);
    	$changes_js = '<script language="javascript"><!--
    						function validateCheck(check_option) {
    							if (check_option == "all_changes") {
    								if (document.getElementById(\'all_changes\').checked == true) {
    									document.getElementById(\'products_price\').checked = false;
    									document.getElementById(\'products_quantity\').checked = false;
    								}
    							} else if (check_option == "products_price") {
    								if (document.getElementById(\'products_price\').checked == true) {
    									document.getElementById(\'all_changes\').checked = false;
    								}
    							} else if (check_option == "products_quantity") {
    								if (document.getElementById(\'products_quantity\').checked == true) {
    									document.getElementById(\'all_changes\').checked = false;
    								}
    							} else if (check_option == "products_quantity_default") {
    								if (document.getElementById(\'products_quantity\').checked == true) {
    									document.getElementById(\'all_changes\').checked = false;
    									
    									var restockSelect = document.log_input.elements[\'restock_opt[]\'];
										for (i=0;i<restockSelect.length;i++) {
											restockSelect[i].checked = true;
										}
										
										var qtyTypesSelect = document.log_input.elements[\'qty_types[]\'];
										for (i=0;i<qtyTypesSelect.length;i++) {
											qtyTypesSelect[i].checked = true;
										}
    								}
    							}
    							
    							if (document.getElementById(\'products_quantity\').checked == true) {
    								var restockSelect = document.log_input.elements[\'restock_opt[]\'];
									for (i=0;i<restockSelect.length;i++) {
										restockSelect[i].disabled = false;
									}
									
									var qtyTypesSelect = document.log_input.elements[\'qty_types[]\'];
									for (i=0;i<qtyTypesSelect.length;i++) {
										qtyTypesSelect[i].disabled = false;
									}
    							} else {
    								var restockSelect = document.log_input.elements[\'restock_opt[]\'];
									for (i=0;i<restockSelect.length;i++) {
										restockSelect[i].disabled = true;
										restockSelect[i].checked = false;
									}
									
									var qtyTypesSelect = document.log_input.elements[\'qty_types[]\'];
									for (i=0;i<qtyTypesSelect.length;i++) {
										qtyTypesSelect[i].disabled = true;
										qtyTypesSelect[i].checked = false;
									}
    							}
    						}
						//--></script>';
		
		$product_qty_types_selection = '<br>'.tep_draw_separator('pixel_trans.gif', '108', '1').'Show changes in ' . tep_draw_checkbox_field('qty_types[]', 'available_qty', isset($_SESSION['post_var']['qty_types']) ? (in_array('available_qty', $_SESSION['post_var']['qty_types']) ? true : false) : true ) . '&nbsp;Available Quantity&nbsp;&nbsp;' . tep_draw_checkbox_field('qty_types[]', 'actual_qty', isset($_SESSION['post_var']['qty_types']) ? (in_array('actual_qty', $_SESSION['post_var']['qty_types']) ? true : false) : false ). '&nbsp;Actual Quantity';
    	$restock_options = array( 	array ('value' => "1", "text" => "Positve Adjustments"),
    								array ('value' => "2", "text" => "Negative Adjustments"),
    								array ('value' => "3", "text" => "System Adjustments (from Orders)" . $product_qty_types_selection)
    							);
    	
    	$sort_option = array ( 	array("name" => "sort_order", "value" => "ASC", "text" => "Ascending", "checked" => isset($_SESSION['post_var']) ? ($_SESSION['post_var']["sort_order"]=="ASC" ? true : false) : false, "params" => ''),
    							array("name" => "sort_order", "value" => "DESC", "text" => "Descending", "checked" => isset($_SESSION['post_var']) ? ($_SESSION['post_var']["sort_order"]=="DESC" ? true : false) : true, "params" => '')
    						);
  		$input_array = array ( 	"product_id" => array ("title" => "Product ID", "type" => "text", "default_value" => $_SESSION['post_var']['product_id'], "params" => 'size=10 id="product_id"', "lookup" => array("link" => "(Product List)", "file" => FILENAME_POPUP_PRODUCTS_LIST, "params" => 'fname='.FILENAME_LOG_FILES), "required" => 0),
  								"modified_by" => array("title" => "Modified by", "type" => "select", "source" => $admin_users, "default_value" => $_SESSION['post_var']['modified_by']),
  								"start_date" => array ("title" => "Start Date", "type" => "date", "format" => "yyyy-MM-dd /<br>yyyy-MM-dd HH:MM", "required" => 0, "default_value" => $_SESSION['post_var']['start_date'], "extra_msg" => "&nbsp;(2005-02-02)", "calendar" => "PopCal"),
  								"end_date" => array ("title" => "End Date", "type" => "date", "format" => "yyyy-MM-dd /<br>yyyy-MM-dd HH:MM", "required" => 0, "default_value" => $_SESSION['post_var']['end_date'], "calendar" => "PopCal"),
  								"changes" => array("title" => "Changes in", "type" => "checkbox", "format" => "horizontal", "source" => $changes_option, "js" => "$changes_js", "required" => 0),
  								"restock_opt[]" => array("title" => "&nbsp;", "type" => "checkbox", "format" => "vertical", "source" => $restock_options, "spacer" => 108, "use_key" => 1, "js" => "$restock_js", "params" => ' id="restock_selections" onChange="validateSelect();"'),
  								"sort_by" => array("title" => "Sort by date", "type" => "radio", "format" => "horizontal", "source" => $sort_option)
  								);
  		$log_object->draw_inputs("log_input", $input_array);
	  		echo '	<tr>
	  					<td>
	  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
	  							<tr>
	  								<td width="30%">&nbsp;</td>
	  								<td align="right">' .
	  									tep_image_submit('button_report.gif', IMAGE_REPORT) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_LOG_FILES, 'action=reset_session') . '">' . tep_image_button('button_reset.gif', IMAGE_RESET) . '</a>  
	  								</td>
	  							</tr>
	  						</table>
	  					</td>
	  				</tr>';
?>
					<tr>
						<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
					</tr>
					</form>
					<script language="javascript"><!--
						function form_checking() {
							if (document.getElementById('product_id').value == "") {
								;
							} else if (!validateInteger(document.getElementById('product_id').value)) {
								alert('Product id must be an integer!');
								document.getElementById('product_id').focus();
								document.getElementById('product_id').select();
								return false;
							}
							
							var start_date = document.getElementById('start_date').value;
							if(start_date.length > 0){
			     				if (!validateDate(start_date)) {
			     					alert('Start date is not a valid date format as requested!');
									document.getElementById('start_date').focus();
									document.getElementById('start_date').select();
									return false;
			     				}
			   				} else {
			   					alert('Please choose start date!');
			   					return false;
			   				}
			   				
			   				var end_date = document.getElementById('end_date').value;
							if(end_date.length > 0){
			     				if (!validateDate(end_date)) {
			     					alert('End date is not a valid date format as requested!');
									document.getElementById('end_date').focus();
									document.getElementById('end_date').select();
									return false;
			     				}
			   				}
			   				
			   				if (start_date.length > 0 && end_date.length > 0) {
			   					if (!validStartAndEndDate(start_date, end_date)) {
			   						alert('Start Date is greater than End Date!');
									document.getElementById('start_date').focus();
									document.getElementById('start_date').select();
									return false;
			   					}
			   				}
			   				
			   				if (!document.getElementById('all_changes').checked && !document.getElementById('products_price').checked && !document.getElementById('products_quantity').checked) {
			   					alert('Please state at least one changes option!');
								document.getElementById('all_changes').focus();
								return false;
			   				}
			   				
			   				if (document.getElementById('products_quantity').checked) {
				   				var multiSelect = document.log_input.elements['restock_opt[]'];
				   				var qty_option = false;
								for (i=0;i<multiSelect.length;i++) {
									if (multiSelect[i].checked) {
										qty_option = true;
										break;
									}
								}
								
								if (!qty_option) {
									alert('Please select at least one type of qunatity adjustment!');
									multiSelect[0].focus();
									return false;
								}
								
								var qtyTypesSelect = document.log_input.elements['qty_types[]'];
								var qty_type_option = false;
								for (i=0;i<qtyTypesSelect.length;i++) {
									if (qtyTypesSelect[i].checked) {
										qty_type_option = true;
										break;
									}
								}
								
								if (!qty_type_option) {
									alert('Please state changes in which product quantity to be shown!');
									qtyTypesSelect[0].focus();
									return false;
								}
							}
							
							return true;
			    		}
						
						function getReturnedValue(received_val) {
							document.getElementById('product_id').value = received_val;
						}
						
						function default_restock_opt() {
						<?	if (count($_SESSION['post_var']['restock_opt'])) {?>
								var cur_selected = "<?=','.implode(',', $_SESSION['post_var']['restock_opt']).','?>";
								var multiSelect = document.log_input.elements['restock_opt[]'];
								for (i=0; i < multiSelect.length; i++) {
									opt_val = ','+ multiSelect[i].value +',';
									if (cur_selected.indexOf(opt_val) != -1) {
										multiSelect[i].checked = true;
									}
								}
						<?	}?>
						}
						
						function init() {
							validateCheck("all_changes");
							validateCheck("products_price");
							validateCheck("products_quantity");
							default_restock_opt();
						}
						init();
						//-->
					</script>
        			</tr>
<?	} ?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>