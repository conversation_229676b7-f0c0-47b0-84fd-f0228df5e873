<?php
/*
  	$Id: login.php,v 1.7 2009/10/13 07:44:48 weesiong Exp $
	
	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2002 osCommerce
	
	Released under the GNU General Public License
*/

require('includes/application_top.php');
include_once(DIR_WS_FUNCTIONS . 'admin_members.php');

define('LOGIN_FAILED_LIMIT', 10);

if (isset($_GET['action']) && ($_GET['action'] == 'process')) {
	$email_address = tep_db_prepare_input($_POST['email_address']);
    $password = tep_db_prepare_input($_POST['password']);
	// Check if email exists
	$check_admin_select = "	SELECT admin_id as login_id, admin_groups_id as login_groups_id, admin_firstname as login_firstname, 
									admin_email_address as login_email_address, admin_password as login_password, 
									admin_modified as login_modified, admin_logdate as login_logdate, admin_lognum as login_lognum,
									admin_login_attempt 
								FROM " . TABLE_ADMIN . " 
								WHERE admin_email_address = '" . tep_db_input($email_address) . "' AND admin_deletion_flag <> 1";
    $check_admin_query = tep_db_query($check_admin_select);
    if (!tep_db_num_rows($check_admin_query)) {
    	$_GET['login'] = 'fail';
    } else {
      	$check_admin = tep_db_fetch_array($check_admin_query);
      	// Check that password is good
      	if ($check_admin['admin_login_attempt'] >= LOGIN_FAILED_LIMIT) {
      		tep_db_query("UPDATE " . TABLE_ADMIN . " SET admin_login_attempt = admin_login_attempt+1 WHERE admin_id = " . (int)$check_admin['login_id']);
      		$_GET['login'] = 'exceeded_attempt_limit';
      	} else if (!tep_validate_password($password, $check_admin['login_password'])) {
            // if empty password then don't update the login attempt
            if (!empty($password)) {
                tep_db_query("UPDATE " . TABLE_ADMIN . " SET admin_login_attempt = admin_login_attempt+1 WHERE admin_id = " . (int)$check_admin['login_id']);
            }
        	$_GET['login'] = 'fail';
        	
        	if ($check_admin['admin_login_attempt'] == (LOGIN_FAILED_LIMIT-1)) {
	        	$_GET['login'] = 'exceeded_attempt_limit';
	      	}
        } else {
        	if (isset($_SESSION['password_forgotten'])) {
          		unset($_SESSION['password_forgotten']);
        	}
			
        	$_SESSION['login_id'] = $check_admin['login_id'];
	        $_SESSION['login_groups_id'] = $check_admin['login_groups_id'];
	        $_SESSION['login_first_name'] = $check_admin['login_firstname'];
	        $_SESSION['login_email_address'] = $check_admin['login_email_address'];
            
	        $login_logdate = $check_admin['login_logdate'];
	        $login_lognum = $check_admin['login_lognum'];
	        $login_modified = $check_admin['login_modified'];
			
//        	tep_session_register('login_id');
//        	tep_session_register('login_groups_id');
//        	tep_session_register('login_first_name');
//        	tep_session_register('login_email_address');
			
        	//$date_now = date('Ymd');
        	tep_db_query("update " . TABLE_ADMIN . " set admin_logdate = now(), admin_lognum = admin_lognum+1, admin_login_attempt = 0 where admin_id = '" . $check_admin['login_id'] . "'");

        	if (($login_lognum == 0) || !($login_logdate) || ($login_email_address == 'admin@localhost') || ($login_modified == '0000-00-00 00:00:00')) {
          		tep_redirect(tep_href_link(FILENAME_ADMIN_ACCOUNT));
        	} else {
          		tep_redirect(tep_href_link(FILENAME_DEFAULT));
        	}
      	}
	}
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGIN);
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<style type="text/css"><!--
		a { color:#080381; text-decoration:none; }
		a:hover { color:#aabbdd; text-decoration:underline; }
		a.text:link, a.text:visited { color: #ffffff; text-decoration: none; }
		a:text:hover { color: #000000; text-decoration: underline; }
		a.sub:link, a.sub:visited { color: #080381; text-decoration: none; }
		A.sub:hover { color: #080381; text-decoration: underline; }
		.sub { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 10px; font-weight: bold; line-height: 1.5; color: #dddddd; }
		.text { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 11px; font-weight: bold; color: #000000; }
		.smallText { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
		.login_heading { font-family: Verdana, Arial, sans-serif; font-size: 12px; color: #080381;}
		.login { font-family: Verdana, Arial, sans-serif; font-size: 12px; color: #000000;}
        .login.label { white-space: nowrap; }
		//-->
	</style>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
	<table border="0" width="600" height="100%" cellspacing="0" cellpadding="0" align="center" valign="middle">
  		<tr>
    		<td>
    			<table border="0" width="600" height="440" cellspacing="0" cellpadding="1" align="center" valign="middle">
      				<tr bgcolor="#000000">
        				<td>
        					<table border="0" width="600" height="440" cellspacing="0" cellpadding="0">
          						<tr bgcolor="#ffffff" height="50">
            						<td align="left">&nbsp;</td>
            						<!--td height="50"><?php //echo '<a href="http://www.themoofarm.com">' . tep_image(DIR_WS_IMAGES . 'oscommerce.gif', 'themoofarm.com', '600', '70') . '</a>'; ?></td-->
          						</tr>
          						<tr bgcolor="#ffffff">
            						<td align="center" valign="middle">
<?php									echo tep_draw_form('login', FILENAME_LOGIN, 'action=process'); ?>
                            			<table width="300" border="0" cellspacing="0" cellpadding="2">
                              				<tr>
                                				<td class="login_heading" valign="top">&nbsp;<b><?php echo HEADING_RETURNING_ADMIN; ?></b></td>
                              				</tr>
                              				<tr>
                                				<td height="100%" valign="top" align="center">
                                					<table border="0" height="100%" cellspacing="0" cellpadding="1" bgcolor="#666666">
                                  						<tr>
                                  							<td>
                                  								<table border="0" width="100%" height="100%" cellspacing="3" cellpadding="2" bgcolor="#F0F0FF">
<?php
  																if ($_GET['login'] == 'fail') {
    																$info_message = TEXT_LOGIN_ERROR;
  																} else if ($_GET['login'] == 'exceeded_attempt_limit') {
                                                                    $info_message = sprintf(TEXT_EXCEEDED_LOGIN_ATTEMPT_LIMIT_ERROR, tep_href_link(FILENAME_PASSWORD_FORGOTTEN, '', 'SSL') );
  																}
  																if (isset($info_message)) { ?>
                                    								<tr>
                                      									<td colspan="2" class="smallText" align="left"><?php echo $info_message; ?></td>
	                                   								</tr>
<?php  															} else { ?>
                                    								<tr>
                                      									<td colspan="2" class="smallText">&nbsp;</td>
                                    								</tr>
<?php															} ?>                                    
                                    								<tr>
                                      									<td class="login label"><?php echo ENTRY_EMAIL_ADDRESS; ?></td>
                                      									<td class="login"><?php echo tep_draw_input_field('email_address', '', 'required'); ?></td>
                                    								</tr>
                                    								<tr>
                                      									<td class="login label"><?php echo ENTRY_PASSWORD?></td>
                                      									<td class="login"><?php echo tep_draw_password_field('password', '', false, 'maxlength="40" required'); ?></td>
                                    								</tr>
                                    								<tr>
                                      									<td colspan="2" align="right" valign="top"><?php echo tep_image_submit('button_confirm.gif', IMAGE_BUTTON_LOGIN); ?></td>
                                    								</tr>
                                  								</table>
                                  							</td>
                                  						</tr>
                                					</table>
                                				</td>
                              				</tr>
                              				<tr>
                                				<td valign="top" align="right"><?php echo '<a class="sub" href="' . tep_href_link(FILENAME_PASSWORD_FORGOTTEN, '', 'SSL') . '">' . TEXT_PASSWORD_FORGOTTEN . '</a><span class="sub">&nbsp;</span>'; ?></td>
                              				</tr>
                            			</table>
                          				</form>
            						</td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td><?php require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
      				</tr>
    			</table>
    		</td>
  		</tr>
	</table>
<?php
	if (tep_not_null(GOOGLE_ANALYTICS_ACCOUNT_ID)) { // Google Analytics Tracking 
?>
		<script type="text/javascript">
			var gaJsHost = (("https:" == document.location.protocol) ? "https://ssl." : "http://www.");
			document.write(unescape("%3Cscript src='" + gaJsHost + "google-analytics.com/ga.js' type='text/javascript'%3E%3C/script%3E"));
		</script>
		<script type="text/javascript">
			var pageTracker = _gat._getTracker("UA-318255-16");
			pageTracker._initData();
			pageTracker._trackPageview();
        </script>
<?php	
	if ($content == CONTENT_CHECKOUT_SUCCESS) {
		if (file_exists(DIR_WS_JAVASCRIPT . 'google_analytics.js.php')) { include_once (DIR_WS_JAVASCRIPT . 'google_analytics.js.php'); }
	}
}
?>
</body>
</html>