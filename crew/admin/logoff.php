<?
/*
  	$Id: logoff.php,v 1.5 2005/07/07 03:56:51 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_LOGOFF);

// uncomment to do not release those locked order when admin staff log off
//tep_release_locked_order($login_id);

//tep_session_destroy();
tep_session_unregister('login_id');
tep_session_unregister('login_firstname');
tep_session_unregister('login_groups_id');

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
	<style type="text/css"><!--
		a { color:#080381; text-decoration:none; }
		a:hover { color: #000000; color:#aabbdd; text-decoration:underline; }
		a.text:link, a.text:visited { color: #000000; text-decoration: none; }
		a:text:hover { color: #000000; text-decoration: underline; }
		A.login_heading:link, A.login_heading:visited { underline; font-family: Verdana, Arial, sans-serif; font-size: 12px; color: #F0F0FF;}
		A.login_heading: hover { color: #F0F0FF;}
		.heading { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 20px; font-weight: bold; line-height: 1.5; color: #D3DBFF; }
		.text { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 11px; font-weight: bold; line-height: 1.5; color: #000000; }
		.smallText { font-family: Verdana, Arial, sans-serif; font-size: 10px; }
		.login_heading { font-family: Verdana, Arial, sans-serif; font-size: 12px; color: #080381;}
		.login { font-family: Verdana, Arial, sans-serif; font-size: 12px; color: #000000;}
		//-->
	</style>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
	<table border="0" width="600" height="100%" cellspacing="0" cellpadding="0" align="center" valign="middle">
  		<tr>
    		<td>
    			<table border="0" width="600" height="440" cellspacing="0" cellpadding="1" align="center" valign="middle">
      				<tr bgcolor="#000000">
        				<td>
        					<table border="0" width="600" height="440" cellspacing="0" cellpadding="0">
          						<tr bgcolor="#ffffff" height="50">
          							<td align="left">&nbsp;</td>
          						</tr>
          						<tr bgcolor="#ffffff">
            						<td colspan="1" align="center" valign="middle">
                            			<table width="280" border="0" cellspacing="0" cellpadding="2">
                              				<tr>
                                				<td class="login_heading" valign="top"><b><?=HEADING_TITLE?></b></td>
                              				</tr>
                              				<tr>
                                				<td class="login_heading"><?=TEXT_MAIN?></td>
                              				</tr>
                              				<tr>
                                				<td class="login_heading" align="right"><? echo '<a class="login_heading" href="' . tep_href_link(FILENAME_LOGIN, '', 'SSL') . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'; ?></td>
                              				</tr>
                              				<tr>
                                				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '100%', '30')?></td>
                              				</tr>
                            			</table>
            						</td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td><? require(DIR_WS_INCLUDES . 'footer.php'); ?></td>
      				</tr>
    			</table>
    		</td>
  		</tr>
	</table>
</body>
</html>