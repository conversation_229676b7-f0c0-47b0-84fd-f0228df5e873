<?php
/*
  	$Id: mail.php,v 1.12 2007/05/15 09:00:58 sunny Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

$batch_email_cust_id_array = array();

if ($_REQUEST["customers_email_id"]) {
	$batch_email_cust_id_array = tep_array_unserialize($_REQUEST["customers_email_id"]);
	unset($_SESSION['mail_param']["customer"]);
} else if ($_REQUEST['customer']) {
	$_SESSION['mail_param']["customer"] = $_REQUEST['customer'];
}

if ($action == 'send_email_to_user') {
	if (isset($HTTP_POST_VARS['CancelBtn'])) {
		;
	} else if (isset($HTTP_POST_VARS['customers_email_address']) && !isset($HTTP_POST_VARS['back_x'])) {
		switch ($HTTP_POST_VARS['customers_email_address']) {
	      	/*case '***':
	        	$mail_query = tep_db_query("select customers_firstname, customers_lastname, customers_email_address from " . TABLE_CUSTOMERS);
	        	$mail_sent_to = TEXT_ALL_CUSTOMERS;
	        	break;
	      	case '**D':
	        	$mail_query = tep_db_query("select customers_firstname, customers_lastname, customers_email_address from " . TABLE_CUSTOMERS . " where customers_newsletter <> '0' and customers_newsletter IS NOT NULL and account_activated ='1'");
	        	$mail_sent_to = TEXT_NEWSLETTER_CUSTOMERS;
	        	break;
	        */
	        case 'selected':
	        	$mail_sent_to = TEXT_TOTAL.count($batch_email_cust_id_array);
	        	break;
	      	default:
	        	$customers_email_address = tep_db_prepare_input($HTTP_POST_VARS['customers_email_address']);
				
	        	$mail_query = tep_db_query("select customers_firstname, customers_lastname, customers_email_address from " . TABLE_CUSTOMERS . " where customers_email_address = '" . tep_db_input($customers_email_address) . "'");
	        	$mail_sent_to = $HTTP_POST_VARS['customers_email_address'];
	        	break;
	    }
		
	    $from = tep_db_prepare_input($HTTP_POST_VARS['from']);
	    $subject = tep_db_prepare_input($HTTP_POST_VARS['subject']);
	    $message = tep_db_prepare_input($HTTP_POST_VARS['message']);
		$message = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $message);
		
	    //Let's build a message object using the email class
	    $mimemessage = new email(array('X-Mailer: osCommerce'));
	    // add the message to the object
	    
		// MaxiDVD Added Line For WYSIWYG HTML Area: BOF (Send TEXT Email when WYSIWYG Disabled)
	    if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Disable') {
	    	$mimemessage->add_text($message);
	    } else {
	    	$mimemessage->add_html($message);
	    }
		// MaxiDVD Added Line For WYSIWYG HTML Area: EOF (Send HTML Email when WYSIWYG Enabled)
		
	    $mimemessage->build_message();
	    if (count($batch_email_cust_id_array)) {
	    	foreach ($batch_email_cust_id_array as $customers_id) {
	    		$mail_select_sql = "select customers_firstname, customers_lastname, customers_email_address from " . TABLE_CUSTOMERS . " where customers_id = '" . $customers_id . "'";
	    		$mail_result_sql = tep_db_query($mail_select_sql);
	    		$mail_row = tep_db_fetch_array($mail_result_sql);
	    		
	    		tep_mail($mail_row['customers_firstname'].' '.$mail_row['customers_lastname'], $mail_row['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, $subject)), $message, $from, $from);
	    	}
	    } else {
		    while ($mail = tep_db_fetch_array($mail_query)) {
		    	$mimemessage->send($mail['customers_firstname'] . ' ' . $mail['customers_lastname'], $mail['customers_email_address'], '', $from, $subject);
		    }
		}
		unset($_SESSION['mail_param']);
		
	    //tep_redirect(tep_href_link(FILENAME_MAIL, 'mail_sent_to=' . urlencode($mail_sent_to)));
	    $messageStack->add_session(sprintf(NOTICE_EMAIL_SENT_TO, $mail_sent_to), 'success');
	    tep_redirect(tep_href_link(FILENAME_CUSTOMERS, tep_get_all_get_params(array('cID', 'action')) . 'action=show_report'));
	}
}

if ( ($action == 'preview') && !$HTTP_POST_VARS['subject'] ) {
	$messageStack->add(ERROR_NO_SUBJECT_ENTERED, 'error');
}

if ( ($action == 'preview') && !$HTTP_POST_VARS['customers_email_address']) {
	$messageStack->add(ERROR_NO_SELECTED_CUSTOMER, 'error');
}

if (isset($HTTP_GET_VARS['mail_sent_to'])) {
    $messageStack->add(sprintf(NOTICE_EMAIL_SENT_TO, $HTTP_GET_VARS['mail_sent_to']), 'success');
    tep_redirect(tep_href_link(FILENAME_CUSTOMERS, 'action=show_report'));
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="Javascript1.2"><!-- // load htmlarea
	// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email HTML - <head>
    _editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
    var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
    if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
    if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
    if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
	<?php if (HTML_AREA_WYSIWYG_BASIC_EMAIL == 'Basic'){ ?>  if (win_ie_ver >= 5.5) {
			document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
			document.write(' language="Javascript1.2"></scr' + 'ipt>');
		} else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
<? 	} else{ ?> if (win_ie_ver >= 5.5) {
       		document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
       		document.write(' language="Javascript1.2"></scr' + 'ipt>');
		} else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
<? }?>
// --></script>
<script language="JavaScript" src="htmlarea/validation.js"></script>
<script language="JavaScript">
<!-- Begin
	function init() {
		define('customers_email_address', 'string', 'Customer or Newsletter Group');
	}
	
	function form_checking(form_obj, action) {
		if (action=='send_email') {
			var formSubject = document.getElementById('subject').value;
			if (!formSubject) {
				alert("<?=JS_SUBJECT?>");
				document.getElementById('subject').focus();
				document.getElementById('subject').select();
				return false;
			}
		}
	}
	
//  End -->
</script>
</head>
<body OnLoad="init()" marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
      				<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
						            <td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
						            <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ( ($action == 'preview') && isset($HTTP_POST_VARS['customers_email_address']) && $HTTP_POST_VARS['subject'] ) {
	$_SESSION['mail_param']["customers_email_address"] = $HTTP_POST_VARS['customers_email_address'];
  	$_SESSION['mail_param']["subject"] = $HTTP_POST_VARS['subject'];
  	$_SESSION['mail_param']["message"] = $HTTP_POST_VARS['message'];
  	
    switch ($HTTP_POST_VARS['customers_email_address']) {
		/*case '***':
        	$mail_sent_to = TEXT_ALL_CUSTOMERS;
        	break;
      	case '**D':
        	$mail_sent_to = TEXT_NEWSLETTER_CUSTOMERS;
        	break;
      	*/
      	case 'selected':
       		$mail_sent_to = TEXT_TOTAL.count($batch_email_cust_id_array);
        	break;
      	default:
	        $mail_sent_to = $HTTP_POST_VARS['customers_email_address'];
    	    break;
	}
?>
          						<tr>
            						<td>
            							<?=tep_draw_form('mail', FILENAME_MAIL, 'action=send_email_to_user')?>
            							<table border="0" width="100%" cellpadding="0" cellspacing="2">
              								<tr>
                								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?php echo TEXT_CUSTOMER; ?></b><br><?php echo $mail_sent_to; ?></td>
              								</tr>
              								<tr>
                								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?php echo TEXT_FROM; ?></b><br><?php echo htmlspecialchars(stripslashes($HTTP_POST_VARS['from'])); ?></td>
              								</tr>
              								<tr>
                								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?php echo TEXT_SUBJECT; ?></b><br><?php echo htmlspecialchars(stripslashes($HTTP_POST_VARS['subject'])); ?></td>
              								</tr>
              								<tr>
                								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
              								</tr>
              								<tr>
                								<td class="smallText"><b><?php echo TEXT_MESSAGE; ?></b><br><?php if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') { echo (stripslashes($HTTP_POST_VARS['message'])); } else { echo htmlspecialchars(stripslashes($HTTP_POST_VARS['message'])); } ?></td>
              								</tr>
              								<tr>
                								<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
              								</tr>
              								<tr>
                								<td>
<?
	/* Re-Post all POST'ed variables */
    reset($HTTP_POST_VARS);
    while (list($key, $value) = each($HTTP_POST_VARS)) {
      	if (!is_array($HTTP_POST_VARS[$key])) {
        	echo tep_draw_hidden_field($key, htmlspecialchars(stripslashes($value)));
      	}
	}
?>
	                    						</td>
	                    					</tr>
                    						<tr>
                    							<td align="right"><?=tep_submit_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, 'name="CancelBtn"', 'inputButton') . '&nbsp;&nbsp;' . tep_submit_button(IMAGE_SEND_EMAIL, IMAGE_SEND_EMAIL, '', 'inputButton')?></td>
                    						</tr>
                    						<tr>
                    							<td class="smallText">
<?	if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Disable') {
		echo tep_image_submit('button_back.gif', IMAGE_BACK, 'name="back"');
  	}
	if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Disable') {
		echo(TEXT_EMAIL_BUTTON_HTML);
	} else {
		echo(TEXT_EMAIL_BUTTON_TEXT);
	}
?>
                    							</td>
                  							</tr>
                						</table>
                					</td>
             					</tr>
           	 				</table>
           	 				</form>
            			</td>
         			</tr>
<?
} else {
?>
          			<tr>
            			<td>
            			<?=tep_draw_form('mail', FILENAME_MAIL, 'action=preview')?>
            				<table border="0" cellpadding="0" cellspacing="2">
	              				<tr>
	                				<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
	              				</tr>
<?
	$customers = array();
	
	if ($_REQUEST["action"] == "send_all") {
		echo tep_draw_hidden_field('customers_email_address', 'selected');
		echo tep_draw_hidden_field('action', $_REQUEST["action"]);
		echo tep_draw_hidden_field("customers_email_id", tep_array_serialize($batch_email_cust_id_array));
?>
				  				<tr>
	                				<td class="main" width="20%"><?php echo TEXT_TOTAL; ?></td>
	                				<td class="main" width="80%"><b><?=count($batch_email_cust_id_array)?></b></td>
	              				</tr>
<? 	} else if ($_REQUEST['action'] == "send_one") { ?>
				  				<tr>
	                				<td class="main" width="20%"><?php echo TEXT_CUSTOMER; ?></td>
	                				<td width="80%">
	                					<b>
	                				<?php 
	                					echo $_SESSION['mail_param']["customer"];
	                					echo tep_draw_hidden_field('customers_email_address', $_SESSION['mail_param']["customer"]);
	                					echo tep_draw_hidden_field('action', $_REQUEST["action"]);
	                				?>
	                					</b>
	                				</td>
	              				</tr>
<?	} else { ?>
				  				<tr>
	                				<td class="main" width="20%"><?php echo TEXT_CUSTOMER; ?></td>
	                				<td class="main" width="80%"><b><?=TEXT_NO_SELECTED_CUSTOMER?></b></td>
	              				</tr>
<?	} ?>
	              				<tr>
	                				<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
	              				</tr>
	              				<tr>
	                				<td class="main"><?php echo TEXT_FROM; ?></td>
	                				<td><?php echo tep_draw_input_field('from', EMAIL_FROM); ?></td>
	              				</tr>
	              				<tr>
	                				<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
	              				</tr>
	              				<tr>
	                				<td class="main"><?php echo TEXT_SUBJECT; ?></td>
	                				<td><?php echo tep_draw_input_field('subject', $_SESSION['mail_param']["subject"],'id="subject"'); ?></td>
	              				</tr>
	              				<tr>
	                				<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
	              				</tr>
	              				<tr>
	                				<td valign="top" class="main"><?php echo TEXT_MESSAGE; ?></td>
	                				<td><?php echo tep_draw_textarea_field('message','soft', '60', '15', $_SESSION['mail_param']["message"]); ?></td>
<?
	if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable') { ?>
		<script language="JavaScript1.2" defer>
			// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email HTML - <body>
			var config = new Object();  // create new config object
           	config.width = "<?php echo EMAIL_AREA_WYSIWYG_WIDTH; ?>px";
           	config.height = "<?php echo EMAIL_AREA_WYSIWYG_HEIGHT; ?>px";
           	config.bodyStyle = 'background-color: <?php echo HTML_AREA_WYSIWYG_BG_COLOUR; ?>; font-family: "<?php echo HTML_AREA_WYSIWYG_FONT_TYPE; ?>"; color: <?php echo HTML_AREA_WYSIWYG_FONT_COLOUR; ?>; font-size: <?php echo HTML_AREA_WYSIWYG_FONT_SIZE; ?>pt;';
           	config.debug = <?php echo HTML_AREA_WYSIWYG_DEBUG; ?>;
           	editor_generate('message',config);
      	</script>
<?	}
	// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Email HTML - <body>
?>
	              				</tr>
	              				<tr>
	                				<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
	              				</tr>
	              				<tr>              
                					<td colspan="2" align="right">
<?
	echo '<a href="' . tep_href_link(FILENAME_CUSTOMERS, tep_get_all_get_params(array('cID', 'action')) . 'action=show_report') .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>&nbsp;&nbsp;';				 	
	if (HTML_AREA_WYSIWYG_DISABLE_EMAIL == 'Enable'){
		echo tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL, 'onClick="return form_checking(this.form, \'send_email\');"');				 	
	} else {
		echo tep_image_submit('button_send_mail.gif', IMAGE_SEND_EMAIL, 'onClick="return form_checking(this.form, \'send_email\');"');
	}
?>
                					</td>
              					</tr>
            				</table>
            			</form>
            			</td>
          			</tr>
<?
}
?>
<!-- body_text_eof //-->
        		</table>
        	</td>
      	</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>