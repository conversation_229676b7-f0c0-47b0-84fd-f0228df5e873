<?
/*
  	$Id: maxmind_history.php,v 1.15 2015/10/21 09:28:39 sionghuat.chng Exp $
	
  	Developer: <PERSON><PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/
require('includes/application_top.php');

$date = $_REQUEST['maxmind_date'];
$order_code = $_REQUEST['order_id'];

$customer_maxmind_history_select_sql = "select * from " . TABLE_MAXMIND_HISTORY . " where orders_id = '" . tep_db_input($order_code) ."' and maxmind_history_date ='" .$date."'";
$customer_maxmind_history_result_sql = tep_db_query($customer_maxmind_history_select_sql);
$maxmind_history_row = tep_db_fetch_array($customer_maxmind_history_result_sql);

if ($maxmind_history_row['maxmind_history_source'] == "Customer's Billing Address"){
	$country_select_sql = "select billing_country as country_name from " . TABLE_ORDERS . " where orders_id = '" . tep_db_input($order_code) ."'";
	$country_result = tep_db_query($country_select_sql);
	$country_row = tep_db_fetch_array($country_result);
} else if ($maxmind_history_row['maxmind_history_source'] == "Customer Information"){
	$country_select_sql = "select customers_country as country_name from " . TABLE_ORDERS . " where orders_id = '" . tep_db_input($order_code) ."'";
	$country_result = tep_db_query($country_select_sql);
	$country_row = tep_db_fetch_array($country_result);
} else if($maxmind_history_row['maxmind_history_source'] == "Paypal"){
	$country_select_sql = "select address_country as country_name from " . TABLE_PAYPAL . " where invoice = '" . tep_db_input($order_code) ."'";
	$country_result = tep_db_query($country_select_sql);
	$country_row = tep_db_fetch_array($country_result);
} else if($maxmind_history_row['maxmind_history_source'] == "PaypalEC") {
	$country_select_sql = "SELECT address_country AS country_name FROM " . TABLE_PAYPALEC . " WHERE paypal_order_id = '" . tep_db_input($order_code) . "'";
	$country_result = tep_db_query($country_select_sql);
	$country_row = tep_db_fetch_array($country_result);
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript"><!--
		var Nav4 = ((navigator.appName == "Netscape") && (parseInt(navigator.appVersion) >= 4))
		
		function centerWin() {
			var NS = false;
			if (document.all) {
			   /* the following is only available after onLoad */
			   w = document.body.clientWidth;
			   h = document.body.clientHeight;
			   NS = true;
			} else if (document.layers) {
			  	;
			}
			
	     	if (!NS) {
	     		self.moveTo((self.screen.width - self.outerWidth) / 2, (self.screen.height - self.outerHeight) / 2);
	     	} else {
	     		self.moveTo((self.screen.width-document.body.clientWidth) / 2, (self.screen.height-document.body.clientHeight) / 2);
	    	}
	    }
	    
		// Close the dialog
		function closeme() {
			window.close()
		}
		
		// Handle click of OK button
		function handleOK() {
			if (opener && !opener.closed) {
				opener.dialogWin.returnFunc();
			} else {
				alert("You have closed the main window.\n\nNo action will be taken on the choices in this dialog box.")
			}
			closeme();
			return false;
		}
		//-->
	</script>
	
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" onLoad="centerWin(); if (opener) opener.blockEvents()" onUnload="if (opener) opener.unblockEvents()">
<br>
<table border="0" cellspacing="1" cellpadding="0" align="center">
<tr>
<?
//for ($maxmind_score_loop=0; $maxmind_score_loop<=10; $maxmind_score_loop++) {
//	echo '<td class="main">';
//	
//	if((int)$maxmind_history_row['score'] == $maxmind_score_loop){
//		echo '<div class="maxmind_'.$maxmind_score_loop.'_selected"><div style="_position:absolute; _top:50%; display:table-cell; vertical-align:middle;"><div style="_position:relative; _top:-50%; _left:-50%">'.$maxmind_score_loop.'</div></div></div>';
//	} else {
//		echo '<div class="maxmind_'.$maxmind_score_loop.'_score">'.$maxmind_score_loop.'</div>';
//	}
//	echo '</td>';
//}
?>
</tr>
</table>

<table border="0" cellspacing="0" cellpadding="0">
	<tr>
		<td class="main"><b><?=ENTRY_MAXMIND_ID?></b></td>
		<td class="main"><?='&nbsp;' . $maxmind_history_row['maxmind_id']?></td>
	</tr>
	<tr>
		<td class="main"><b><?=ENTRY_MAXMIND_HISTORY_DATE?></b></td>
		<td class="main"><?='&nbsp;' . tep_datetime_short($maxmind_history_row['maxmind_history_date'], PREFERRED_DATE_TIME_FORMAT)?></td>
	</tr>
	<tr>
		<td class="main"><b><?=CUSTOMERS_INFO_MESSAGE?></b></td>
		<td class="main"><?='&nbsp;' . $maxmind_history_row['maxmind_history_source']?></td>
	</tr>
    <tr>
        <td class="main"><b><?=ENTRY_QUERIES_REMAINING?></b></td>
        <td class="main"><?='&nbsp;' . $maxmind_history_row['queries_remaining']?></td>
    </tr>
	<?
		if(tep_not_null($maxmind_history_row['error'])) {
	?>
			<tr>
				<td class="main"><b><?=ENTRY_ERROR?></b></td>
				<td class="main"><? echo '<span class="redIndicator">&nbsp;' . $maxmind_history_row['error'] . ERROR_MESSAGE?></td>
			</tr>
	<?
		}
	?>
			
</table>

<br>
<table border="0" width="100%" cellspacing="3" cellpadding="0">
	<tr>
		<td class = "main" valign="top">
			<table border="1" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_IP_COUNTRY_CODE?></b>
					</td>
					<td class="main" align="center" nowrap>
						<?
						if ($maxmind_history_row['ip_country_code']=="No" || $maxmind_history_row['country_match']=="NA"){
							echo '<span class="redIndicator">No</span>';
						} else {
							$coun = tep_get_countries_info($maxmind_history_row['ip_country_code'], "countries_iso_code_2");
							echo $maxmind_history_row['ip_country_code']. ' - ' .$coun['countries_name'];
						}
						?>
					</td>
				</tr>
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_IP_REGION?></b>
					</td>
					<td class="main" align="center" nowrap>
						<?
						if ($maxmind_history_row['ip_region'] == "Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else if ($maxmind_history_row['ip_region']=="NA"){
							echo '<span class="redIndicator">NA</span>';
						} else {
							echo $maxmind_history_row['ip_region'];
						}
						?>
					</td>
				</tr>
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_IP_CITY?></b>
					</td>
					<td class="main" align="center" nowrap>
						<?
						if ($maxmind_history_row['ip_city'] == "Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else if ($maxmind_history_row['ip_city']=="NA"){
							echo '<span class="redIndicator">NA</span>';
						} else {
							echo $maxmind_history_row['ip_city'];
						}
						?>
					</td>
				</tr>
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_COUNTRY_MATCH?></b>
					</td>
					<td class="main" align="center" nowrap>
						<?
						if ($maxmind_history_row['country_match']=="No" || $maxmind_history_row['country_match']=="NA"){
							echo '<span class="redIndicator">'.$maxmind_history_row['country_match'].'</span>';
						} else if ($maxmind_history_row['country_match']=="Yes") {
							echo'<span class="greenIndicator">'.$maxmind_history_row['country_match'].'</span>';
						} else if ($maxmind_history_row['country_match']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['country_match'];
						}
						?>
					</td>
				</tr>
				<tr>
                    <td class="main" nowrap>
                        <b><?=ENTRY_IP_AREA_CODE?></b>
                    </td>
                    <td class="main" align="center" nowrap>
                        <?
                        if ($maxmind_history_row['ip_area_code']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['ip_area_code'];
						}
                        ?>
                    </td>
                </tr>
                <tr>
                    <td class="main" nowrap>
                        <b><?=ENTRY_IP_POSTAL_CODE?></b>
                    </td>
                    <td class="main" align="center" nowrap>
                        <?
                        if ($maxmind_history_row['ip_postal_code']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['ip_postal_code'];
						}
                        ?>
                    </td>
                </tr>
                <tr>
                    <td class="main" nowrap>
                        <b><?=ENTRY_IP_TIME_ZONE?></b>
                    </td>
                    <td class="main" align="center" nowrap>
                        <?
                        if ($maxmind_history_row['ip_time_zone']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['ip_time_zone'];
						}
                        ?>
                    </td>
                </tr>
			</table>
		</td>
		<td class = "main" valign="top">
			<table border="1" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td class = "main" nowrap>
						<b><?=ENTRY_ANONYMOUS_PROXY?></b>
					</td>
					<td class="main" align="center" nowrap>		
					<?
					if ($maxmind_history_row['anonymous_proxy']=="No" || $maxmind_history_row['anonymous_proxy']=="NA") {
						echo'<span class="greenIndicator">'.$maxmind_history_row['anonymous_proxy'].'</span>';
					} else if ($maxmind_history_row['anonymous_proxy']=="Yes") {
						echo '<span class="redIndicator">'.$maxmind_history_row['anonymous_proxy'].'</span>';
					} else if ($maxmind_history_row['anonymous_proxy']=="Not Check") {
						echo '<span class="redIndicator">Not checked</span>';
					} else {
						echo $maxmind_history_row['anonymous_proxy'];
					}
					?>
					</td>
				</tr>
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_PROXY_SCORE?></b>
					</td>
					<td class="main" align="center" nowrap>
					<?
						if($maxmind_history_row['proxy_score'] == 0) {
							echo '<span class="greenIndicator">'.$maxmind_history_row['proxy_score'].'</span>';
						} else if($maxmind_history_row['proxy_score'] > 0 || $maxmind_history_row['country_match']=="NA") {
							echo '<span class="redIndicator">'.$maxmind_history_row['proxy_score'].'</span>';
						} else if ($maxmind_history_row['proxy_score'] == "Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						}
					?>
					</td>
				</tr>
				<tr>
                    <td class="main" nowrap>
                        <b><?=ENTRY_TRANSPROXY?></b>
                    </td>
                    <td class="main" align = "center" nowrap>
                        <?
                        if ($maxmind_history_row['is_trans_proxy']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['is_trans_proxy'];
						}
                        ?>
                    </td>
                </tr>
                <tr>
                    <td class="main" nowrap>
                        <b><?=ENTRY_CORPORATE_PROXY?></b>
                    </td>
                    <td class="main" align = "center" nowrap>
                        <?
                        if ($maxmind_history_row['ip_corporate_proxy']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['ip_corporate_proxy'];
						}
                        ?>
                    </td>
                </tr>
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_IP_ISP?></b>
					</td>
					<td class="main" align="center" nowrap>
						<?
						if ($maxmind_history_row['ip_isp']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else if($maxmind_history_row['ip_isp']=="NA"){
							echo '<span class="redIndicator">'.$maxmind_history_row['ip_isp'].'</span>';
						} else {
							echo $maxmind_history_row['ip_isp'];
						}
						?>
					</td>
				</tr>
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_IP_ORGNIZATION?></b>
					</td>
					<td class="main" align="center" nowrap>
						<?
						if ($maxmind_history_row['ip_organization']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else if($maxmind_history_row['ip_isp']=="NA"){
							echo '<span class="redIndicator">'.$maxmind_history_row['ip_organization'].'</span>';
						} else {
							echo $maxmind_history_row['ip_organization'];
						}
						?>
					</td>
				</tr>
			</table>
			<?=RISK_INFO_MESSAGE?>
		</td>
		<td class="main" valign="top">
			<table border="1" width="100%" cellspacing="0" cellpadding="0">
                <tr>
                    <td class="main" nowrap>
                        <b><?=ENTRY_IP_NET_SPEED_CELL?></b>
                    </td>
                    <td class="main" align="center" nowrap>
                        <?
                        if ($maxmind_history_row['ip_net_speed_cell']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['ip_net_speed_cell'];
						}
                        ?>
                    </td>
                </tr>
                <tr>
                    <td class="main" nowrap>
                        <b><?=ENTRY_IP_USER_TYPE?></b>
                    </td>
                    <td class="main" align="center" nowrap>
                        <?
                        if ($maxmind_history_row['ip_user_type']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['ip_user_type'];
						}
                        ?>
                    </td>
                </tr>
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_DISTANCE?></b>
					</td>
					<td class="main" align="center">
						<?
						if ($maxmind_history_row['distance'] == "Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else if ($maxmind_history_row['distance']=="NA"){
							echo '<span class="redIndicator">NA</span>';
						} else {
							echo $maxmind_history_row['distance'] . " km";
						}
						?>
					</td>
				</tr>
				<tr>
                    <td class="main" nowrap>
                        <b><?=ENTRY_IP_ACCURACY_RADIUS?></b>
                    </td>
                    <td class="main" align="center" nowrap>
                        <?
                        if ($maxmind_history_row['ip_accuracy_radius']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['ip_accuracy_radius'];
						}
                        ?>
                    </td>
                </tr>
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_SCORE?></b>
					</td>
					<td class="main" align="center" nowrap>
					<?
						if($maxmind_history_row['score'] < 0.3){
							echo '<span class="greenIndicator"><b>'.$maxmind_history_row['score'].'</b></span>';
						} else if($maxmind_history_row['score'] < 0.8){
							echo '<span class="orangeIndicator"><b>'.$maxmind_history_row['score'].'</b></span>';
						} else if($maxmind_history_row['score'] <= 10  || $maxmind_history_row['country_match']=="NA" || $maxmind_history_row['country_match']=="Not checked") {
							echo '<span class="redIndicator"><b>'.$maxmind_history_row['score'].'</b></span>';
						}
					?>
					</td>
				</tr>
				<tr>
					<td class="main" nowrap>
						<b><?=ENTRY_HIGH_RISK_COUNTRY?>&nbsp;-&nbsp;<?echo $country_row['country_name']?>:</b>
					</td>
					<td class="main" align="center" nowrap>
						<?
						if ($maxmind_history_row['high_risk_country']=="No" || $maxmind_history_row['high_risk_country']=="NA") {
							echo'<span class="greenIndicator">'.$maxmind_history_row['high_risk_country'].'</span>';
						} else if ($maxmind_history_row['high_risk_country']=="Yes") {
							echo'<span class="redIndicator">'.$maxmind_history_row['high_risk_country'].'</span>';
						}  else if ($maxmind_history_row['high_risk_country']=="Not Check") {
							echo '<span class="redIndicator">Not checked</span>';
						} else {
						  	echo $maxmind_history_row['high_risk_country'];
						}
						?>
					</td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	</tr>
</table>
</body>
</html>