<?php
include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . FILENAME_MGC_SALES_REPORT);
require_once(DIR_WS_CLASSES . 'custom_product_code.php');
require_once(DIR_WS_CLASSES . 'curl.php');

$action = (tep_not_null($_REQUEST['action']) ? $_REQUEST['action'] : '');

$func = new mgc_sales_report();
switch ($action) {
    case "report":
        $func->start_date = (tep_not_null($_REQUEST['f_start']) ? $_REQUEST['f_start'] : '');
        $func->end_date = (tep_not_null($_REQUEST['f_end']) ? $_REQUEST['f_end'] : '');
        $func->order_status = (tep_not_null($_REQUEST['f_status']) ? $_REQUEST['f_status'] : '');
        $f_action = (tep_not_null($_REQUEST['f_action']) ? strtolower($_REQUEST['f_action']) : '');

        if (!empty($func->start_date) && !empty($func->end_date)) {
            if (strpos($func->start_date, ":") == false) {
                $func->start_date .= " 00:00:00";
            }

            if (strpos($func->end_date, ":") == false) {
                $func->end_date .= " 23:59:59";
            }
            $form_content = $func->searchResult($f_action);
        } else {
            $form_content = $func->searchForm();
        }
        break;

    default:
        $form_content = $func->searchForm();
        break;
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/javascript/jquery.js"></script>
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
        <script language="javascript" src="includes/javascript/php.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->
        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td class="pageHeading" valign="top">
                                MGC Sales Report
                            </td>
                            <td class="pageHeading" align="right"><img src="images/pixel_trans.gif" alt="" border="0" height="40" width="1"></td>
                            </td>
                        </tr>
                        <tr>
                            <td width="100%" valign="top"><?= $form_content ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <!-- footer //-->
        <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
    </body>
</html>
