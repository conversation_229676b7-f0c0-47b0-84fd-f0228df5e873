<?
/*
  	$Id: modules_classes.php,v 1.5 2006/03/16 09:18:28 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

$module_directory = DIR_FS_CATALOG_MODULES . $_REQUEST["set"] .'/';
if (file_exists($module_directory . $_REQUEST["module"] . '.php')) {
	include($module_directory . $_REQUEST["module"] . '.php');
	if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/' . $_REQUEST["set"] . '/' . $_REQUEST["module"] . '.php'))
		include(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/' . $_REQUEST["set"] . '/' . $_REQUEST["module"] . '.php');
	$module = new $_REQUEST["module"];
}

if (tep_not_null($action)) {
	switch ($action) {
		case "insert_contents":
		case "update_contents":
			if ($_REQUEST["set"] == "shipping") {
				switch ($_REQUEST["module"]) {
					case "table":
						$key_array = array();
						if (isset($_REQUEST["weight"]) && count($_REQUEST["weight"])) {
							$temp_weight = array_filter($_REQUEST["weight"], "filter_empty_val");
							$temp_weight = array_unique($temp_weight);
							asort($temp_weight);
							reset($temp_weight);
							
							$rate = array();
							foreach ($temp_weight as $id=>$weight) {
								$rate[] = $weight . ':' . (tep_not_null($_REQUEST["charge"][$id]) ? $_REQUEST["charge"][$id] : '0.00');
							}
							
							$rate_string = implode(',', $rate);
						}
						
						$shipping_class_data_array = array(	'shipping_classes_name' => tep_db_prepare_input($_REQUEST["class_name"]),
															'shipping_module' => tep_db_prepare_input($_REQUEST["module"]) );
            	        
						if ($action == "insert_contents") {
							tep_db_perform(TABLE_SHIPPING_CLASSES, $shipping_class_data_array);
							$new_inserted_class_id = tep_db_insert_id();
							$class_id = $new_inserted_class_id;
							$classes_values_insert_sql = "	INSERT INTO " . TABLE_SHIPPING_CLASSES_VALUES . " (shipping_classes_id, shipping_classes_key, shipping_classes_values) 
																VALUES (".$new_inserted_class_id.", 'shipping_rate','".tep_db_input($rate_string)."'),
																	(".$new_inserted_class_id.", 'over_max_charge','".tep_db_input(number_format($_REQUEST["over_max_charge"], 4))."'),
																	(".$new_inserted_class_id.", 'over_max_unit','".tep_db_input($_REQUEST["over_max_unit"])."'),
																	(".$new_inserted_class_id.", 'handling_fee','".tep_db_input(number_format($_REQUEST["handling_fee"], 4))."'),
																	(".$new_inserted_class_id.", 'shipping_zone','".tep_db_input($_REQUEST["shipping_zone"])."')";
							tep_db_query($classes_values_insert_sql);
						} else {
							tep_db_perform(TABLE_SHIPPING_CLASSES, $shipping_class_data_array, 'update', "shipping_classes_id = '" . $_REQUEST["cID"] . "'");
							$class_id = $_REQUEST["cID"];
							tep_db_query("DELETE FROM " . TABLE_SHIPPING_CLASSES_VALUES . " WHERE shipping_classes_id = '" . $_REQUEST["cID"] . "'");
							$classes_values_insert_sql = "	INSERT INTO " . TABLE_SHIPPING_CLASSES_VALUES . " (shipping_classes_id, shipping_classes_key, shipping_classes_values) 
																VALUES (".$_REQUEST["cID"].", 'shipping_rate','".tep_db_input($rate_string)."'),
																	(".$_REQUEST["cID"].", 'over_max_charge','".tep_db_input(number_format($_REQUEST["over_max_charge"], 4))."'),
																	(".$_REQUEST["cID"].", 'over_max_unit','".tep_db_input($_REQUEST["over_max_unit"])."'),
																	(".$_REQUEST["cID"].", 'handling_fee','".tep_db_input(number_format($_REQUEST["handling_fee"], 4))."'),
																	(".$_REQUEST["cID"].", 'shipping_zone','".tep_db_input($_REQUEST["shipping_zone"])."')";
							tep_db_query($classes_values_insert_sql);
						}
						
						tep_redirect(tep_href_link(FILENAME_MODULES_CLASSES, 'action=edit_class&cID='.$class_id.'&set='.$_REQUEST["set"].'&module='.$_REQUEST["module"]));
						break;
				}
			}
			break;
		case "delete_class":
			if ($_REQUEST["set"] == "shipping") {
				switch ($_REQUEST["module"]) {
					case "table":
						if ($_REQUEST["cID"]) {
							tep_db_query("DELETE FROM " . TABLE_SHIPPING_CLASSES . " WHERE shipping_classes_id = '" . $_REQUEST["cID"] . "'");
							tep_db_query("DELETE FROM " . TABLE_SHIPPING_CLASSES_VALUES . " WHERE shipping_classes_id = '" . $_REQUEST["cID"] . "'");
						}
						
						tep_redirect(tep_href_link(FILENAME_MODULES, 'set=' . $_REQUEST["set"] . '&module=' . $_REQUEST['module']));
						break;
				}
			}
			break;
		case "delete_bracket":
			if ($_REQUEST["set"] == "shipping") {
				switch ($_REQUEST["module"]) {
					case "table":
						$shipping_rate_select_sql = "SELECT shipping_classes_values FROM " . TABLE_SHIPPING_CLASSES_VALUES . " WHERE shipping_classes_id = '" . $_REQUEST["cID"] . "' and shipping_classes_key = 'shipping_rate'";
						$shipping_rate_result_sql = tep_db_query($shipping_rate_select_sql);
						
						if ($shipping_rate_row = tep_db_fetch_array($shipping_rate_result_sql)) {
							$RatePattern = "/(,)?".$_REQUEST['key'].":[\d|\.]+(,)?/is";
							$new_rate = preg_replace($RatePattern, ',', $shipping_rate_row["shipping_classes_values"]);
							
							if (substr($new_rate, 0, 1) == ',')		$new_rate = substr($new_rate, 1);
							if (substr($new_rate, -1) == ',')		$new_rate = substr($new_rate, 0, -1);
							
							tep_db_query("UPDATE " . TABLE_SHIPPING_CLASSES_VALUES . " SET shipping_classes_values = '" . $new_rate . "' WHERE shipping_classes_id = '" . $_REQUEST["cID"] . "' and shipping_classes_key = 'shipping_rate'");
						}
						
						tep_redirect(tep_href_link(FILENAME_MODULES_CLASSES, 'action=edit_class&cID='.$_REQUEST["cID"].'&set='.$_REQUEST["set"].'&module='.$_REQUEST["module"]));
						break;
				}
			}
			break;
	}
}

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=sprintf(HEADING_TITLE, ucfirst($_REQUEST["set"]), $module->title)?></td>
            					</tr>
            					<tr>
    								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  								</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?
if ($action == "new_class" || $action == "edit_class") {
	echo tep_draw_form('module_class_form', FILENAME_MODULES_CLASSES, tep_get_all_get_params(array('action')) . 'action=' . ($action=="new_class" ? 'insert_contents' : 'update_contents'), 'post', 'onSubmit="return module_class_form_checking();"');
	echo tep_draw_hidden_field("set", $_REQUEST["set"]);
	echo tep_draw_hidden_field("module", $_REQUEST["module"]);
	echo tep_draw_hidden_field("cID", $_REQUEST["cID"]);
	echo '	<tr>
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="3">';
	if ($_REQUEST["set"] == "shipping") {
		switch ($_REQUEST["module"]) {
			case "table":
				$tabindex = 1;
				
				$class_values_select_sql = "SELECT c.shipping_classes_name, v.shipping_classes_key, v.shipping_classes_values FROM " . TABLE_SHIPPING_CLASSES . ' AS c LEFT JOIN ' . TABLE_SHIPPING_CLASSES_VALUES . " AS v ON c.shipping_classes_id=v.shipping_classes_id WHERE c.shipping_classes_id = '" . $_REQUEST["cID"] . "'";
				$class_values_result_sql = tep_db_query($class_values_select_sql);
				
				$class_key_value = array();
				
				while ($class_values_row = tep_db_fetch_array($class_values_result_sql)) {
					$class_name = $class_values_row["shipping_classes_name"];
					$class_key_value[$class_values_row["shipping_classes_key"]] = $class_values_row["shipping_classes_values"];
				}
				
				if (tep_not_null($class_key_value["shipping_rate"]))	{
					$rate_pair = explode(',', $class_key_value["shipping_rate"]);
				} else {
					$rate_pair = array();
				}
				
				echo '	<tr>
							<td>
								<table>
									<tr><td>Shipping Name: </td><td>'.tep_draw_input_field('class_name', $class_name, 'size="40" tabindex="'.($tabindex++).'"').'</td></tr>
								</table>
							</td>
						</tr>';
				
				echo '	<tr>
							<td>
								<table border="0" width="1" cellspacing="0" cellpadding="3">';
				
				$titleRowU = $titleRow='<tr class="ordersBoxHeading"><td align="center"></td>';
				$row1U = $row1 ='<tr><td width="20%" align="left" class="ordersBoxHeading" nowrap>Weight ('.SHIPPING_WEIGHT_UNIT.')</td>';
				$row2U = $row2 ='<tr><td width="20%" align="left" class="ordersBoxHeading">Charge</td>';
				$row3U = $row3 ='<tr><td width="20%" align="left" class="ordersBoxHeading">&nbsp;</td>';
				
				$max_x_factor = 0;
				$item_count = 0;
				for ($item_count=0; $item_count <= count($rate_pair); $item_count++) {
					list($x_factor, $y_factor) = explode(':', $rate_pair[$item_count]);
					
					if ($item_count < count($rate_pair))
						$max_x_factor = $x_factor;
					
					if ($item_count % 8 == 0) {
						if ($item_count > 0) {
							$titleRow.= '</tr>';
							$row1.= '</tr>';
							$row2.= '</tr>';
							$row3.= '</tr>';
							echo $titleRow.$row1.$row2.$row3;
						}
						
						$titleRow = "<tr><td colspan='9'>".tep_draw_separator('pixel_trans.gif', '1', '3')."</td></tr>".$titleRowU;
						$row1 = $row1U;
						$row2 = $row2U;
						$row3 = $row3U;
					}
					
					$titleRow.= '<td width="100" align="center">Bracket '.($item_count+1).'</td>';
					
					$cell_style = (($item_count % 2)==0) ? "bracketListingOdd" : "bracketListingEven";
					
					if($item_count == count($rate_pair)) {
						$row1.= '<td align="center" class="'.$cell_style.'" >'.tep_draw_input_field('weight[0]', '', 'size="8" onKeyUp="checkDecimalValue(this);" tabindex="'.($tabindex++).'"').'</td>';
						$row2.= '<td align="center" class="'.$cell_style.'" >'.tep_draw_input_field('charge[0]', '0.00', 'size="8" onKeyUp="checkDecimalValue(this);" tabindex="'.($tabindex++).'"').'</td>';
						$row3.= '<td align="center" class="'.$cell_style.'" >&nbsp;</td>';
					} else {
						$row1.= '<td align="center" class="'.$cell_style.'" >'.tep_draw_input_field('weight['.($item_count+1).']', $x_factor, 'size="8" onKeyUp="checkDecimalValue(this);" tabindex="'.($tabindex++).'"').'</td>';
						$row2.= '<td align="center" class="'.$cell_style.'" >'.tep_draw_input_field('charge['.($item_count+1).']', $y_factor, 'size="8" onKeyUp="checkDecimalValue(this);" tabindex="'.($tabindex++).'"').'</td>';
						$row3.= '<td align="center" class="'.$cell_style.'" ><a href="'.tep_href_link(FILENAME_MODULES_CLASSES,tep_get_all_get_params(array("action"))."action=delete_bracket&key=".$x_factor).'">Remove</a></td>';
					}
				}
				
				$titleRow.= '</tr>';
				$row1.= '</tr>';
				$row2.='</tr>';
				$row3.= '</tr><tr><td colspan="9">'.tep_draw_separator('pixel_trans.gif', '1', '3').'</td></tr>';
				
				echo $titleRow.$row1.$row2.$row3;
				
				echo '			</table>
							</td>
						</tr>';
				echo '	<tr>
							<td>
								<table>
									<tr><td>Charge after '.$max_x_factor.'&nbsp;'.SHIPPING_WEIGHT_UNIT.': </td><td>'.tep_draw_input_field('over_max_charge', $class_key_value['over_max_charge'], 'size="8" onKeyUp="checkDecimalValue(this);" tabindex="'.($tabindex++).'"').'&nbsp;per&nbsp;'.tep_draw_input_field('over_max_unit', $class_key_value['over_max_unit'], 'size="8" onKeyUp="checkDecimalValue(this);" tabindex="'.($tabindex++).'"').'&nbsp;'.SHIPPING_WEIGHT_UNIT.'</td></tr>
									<tr><td>Handling Fee: </td><td>'.tep_draw_input_field('handling_fee', $class_key_value['handling_fee'], 'size="8" onKeyUp="checkDecimalValue(this);" tabindex="'.($tabindex++).'"').'</td></tr>
									<tr><td>Shipping Zone: </td><td>'.tep_geo_zones_pull_down('shipping_zone', $class_key_value['shipping_zone'], 'tabindex="'.($tabindex++).'"').'</td></tr>
								</table>
							</td>
						</tr>';
				
				$js_script = 	'	if (document.module_class_form.over_max_charge.value != "") {' . "\n" . 
								'		if (document.module_class_form.over_max_unit.value == "") {' . "\n" .
								'			alert(\'Please enter the per '.SHIPPING_WEIGHT_UNIT.' value for over maximum charge!\');' . "\n" .
								'			document.module_class_form.over_max_unit.focus();' . "\n" . 
								'			return false;' . "\n" .
								'		}' . "\n" .
								'	}' . "\n";
				
				break;
		}
	}
	echo '				<tr><td>'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td></tr>
						<tr><td align="left">'.($action == "new_class" ? tep_image_submit("button_insert.gif", IMAGE_INSERT, 'tabindex="'.($tabindex++).'"') : tep_image_submit("button_update.gif", IMAGE_UPDATE, 'tabindex="'.($tabindex++).'"') ).'&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_MODULES, 'set=' . $_REQUEST["set"] . '&module=' . $_REQUEST['module']) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a></td></tr>
					</table>
				</td>
        	</tr>
        	</form>';
}
?>
        		</table>
        	</td>
        </tr>
        <script>
        <!--
        	if (document.module_class_form.class_name != null) {
        		document.module_class_form.class_name.focus();
        	}
        	function module_class_form_checking() {
        		<?=$js_script?>
        		return true;
        	}
        	
        	function checkDecimalValue(obj) {
				if (obj.value == "" || !currencyValidation(obj.value)) {
					obj.value= "";
				}
			}
		//-->
        </script>
	</table>
</body>
</html>