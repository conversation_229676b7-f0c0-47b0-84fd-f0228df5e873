<?php
/*
	Latest News Group
*/
require('includes/application_top.php');

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$id = (isset($_REQUEST['id']) ? $_REQUEST['id'] : '');

if (tep_not_null($action)) {
	switch ($action) {

	case 'update':
		if (tep_not_null($_POST['news_groups_edit_tab'][1])) {
			$error = false;
			$news_groups_edit_id = tep_db_prepare_input($_GET['cID']);
			$news_groups_edit_name = tep_db_prepare_input($_POST['news_groups_edit_tab'][1]);
			$news_groups_edit_display_status = tep_db_prepare_input($_POST['news_groups_display_status']);
			$news_groups_edit_sort_order = tep_db_prepare_input($_POST['news_groups_sort_order']);
			
			tep_db_query("update " . TABLE_LATEST_NEWS_GROUPS . " set news_groups_display_status ='". $news_groups_edit_display_status ."', news_groups_sort_order ='". $news_groups_edit_sort_order ."' where news_groups_id = " . $news_groups_edit_id);
			foreach ($_POST['news_groups_edit_tab'] as $news_groups_edit_lang_id => $news_groups_edit_news_groups_title) {
				if (tep_not_null($news_groups_edit_news_groups_title)) {
					$news_groups_edit_description_array = array( 'news_groups_id' => $news_groups_edit_id,
																 'language_id' => $news_groups_edit_lang_id,
																 'news_groups_name' => tep_db_prepare_input($news_groups_edit_news_groups_title));
					$news_id_check_select_sql = "	SELECT news_groups_id
													FROM " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION . "
													WHERE news_groups_id = '" . $news_groups_edit_id . "' 
													AND language_id = '" . $news_groups_edit_lang_id . "'";
					$news_id_check_result_sql = tep_db_query($news_id_check_select_sql);
					if (tep_db_num_rows($news_id_check_result_sql) > 0) {
						tep_db_perform(TABLE_LATEST_NEWS_GROUPS_DESCRIPTION, $news_groups_edit_description_array, 'update', "news_groups_id= '" .(int)$news_groups_edit_id. "' AND language_id = '" .(int)$news_groups_edit_lang_id. "'");
					} else {
						tep_db_perform(TABLE_LATEST_NEWS_GROUPS_DESCRIPTION, $news_groups_edit_description_array);
					}
				} else {
					if ($news_groups_edit_lang_id > 1) {	// Can delete any language except English
						$news_groups_desc_delete_sql = "DELETE FROM " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION . "
														WHERE news_groups_id = '" . $news_groups_edit_id . "'
														AND language_id = '".$news_groups_edit_lang_id."'";
						tep_db_query($news_groups_desc_delete_sql);
					}
				}
			}
			tep_redirect(tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $news_groups_edit_id));
		}
		break;

	case 'deleteconfirm':
		$group_id = tep_db_prepare_input($_GET['cID']);
		tep_db_query("delete from " . TABLE_LATEST_NEWS_GROUPS . " where news_groups_id= " . $group_id);
		tep_db_query("delete from " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION . " where news_groups_id= " . $group_id);
		$news_id_query = tep_db_query("select news_id from " . TABLE_LATEST_NEWS . " where news_groups_id=" . $group_id);
		while($news_id = tep_db_fetch_array($news_id_query)) {
			tep_db_query("UPDATE " . TABLE_LATEST_NEWS . " set news_groups_id=1 where news_id=" . $news_id['news_id']);
		}
			tep_redirect(tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('cID', 'action')))); 
		break;

	case 'newconfirm' :
		if(tep_not_null($_POST['news_groups_tab'][1])){
			$news_groups_display_status = tep_db_prepare_input($_POST['news_groups_display_status']);
			$news_groups_edit_sort_order = tep_db_prepare_input($_POST['news_groups_sort_order']);
			tep_db_query("insert into " . TABLE_LATEST_NEWS_GROUPS . " set news_groups_display_status ='". $news_groups_display_status ."', news_groups_sort_order ='". $news_groups_edit_sort_order ."'");
			$news_id = tep_db_insert_id();
			  	foreach ($_POST['news_groups_tab'] as $news_groups_lang_id => $news_groups_heading_title) {
			  		if (tep_not_null($news_groups_heading_title)) {
			      		$news_groups_description_array = array( 'news_groups_id' => $news_id,
			      												'language_id' => (int)$news_groups_lang_id,
																'news_groups_name' => tep_db_prepare_input($news_groups_heading_title));
						tep_db_perform(TABLE_LATEST_NEWS_GROUPS_DESCRIPTION, $news_groups_description_array);
					}
			    }
			tep_redirect(tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('action'))));
		}
		break;
		
	case 'changestatus' :
		echo changeStatus($id);
		exit;
	break;
	}
}

function changeStatus($id='')
{
	if ($id) {
		$sql = "SELECT news_groups_display_status 
				FROM ".TABLE_LATEST_NEWS_GROUPS." 
				WHERE news_groups_id = '".$id."'";
		$result_sql = tep_db_query($sql);
 		$rows = tep_db_fetch_array($result_sql);
 		$group_status = $rows['news_groups_display_status'];
 		
 		if ($group_status == "1")
 			 $Group_obj = array('news_groups_display_status' => 0);
		else
 			 $Group_obj = array('news_groups_display_status' => 1);

		tep_db_perform(TABLE_LATEST_NEWS_GROUPS, $Group_obj, 'update', "news_groups_id='".$id."'");
		
		if ($group_status == "1")
			 return "<a href=\"javascript:change_status('$id')\"><img src=\"images/icon_status_green_light.gif\" border=0></a> <img src=\"images/icon_status_red.gif\" border=0>";
	 	else
			 return "<img src=\"images/icon_status_green.gif\" border=0> <a href=\"javascript:change_status('$id')\"><img src=\"images/icon_status_red_light.gif\" border=0></a>";
	}
	else {
		return '';
	}
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
<script language="JavaScript" src="includes/javascript/jquery.js"></script>
<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
  <tr>
    <td width="<?php echo BOX_WIDTH; ?>" valign="top"><table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
    </table></td>
<!-- body_text //-->
    <td width="100%" valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">

<?php
  if ($_GET['action'] == 'edit') {
	$languages = tep_get_languages();
	$news_groups_edit_array = array();
	$news_groups_edit_sort_order = array();
    if ( isset($_GET['cID']) ) { 
    	$latest_news_groups_select_sql = "SELECT lnd.news_groups_id, lnd.news_groups_display_status, lnd.news_groups_sort_order,
											ln.news_groups_id, ln.language_id, ln.news_groups_name
											FROM ". TABLE_LATEST_NEWS_GROUPS_DESCRIPTION." as ln
											LEFT JOIN " . TABLE_LATEST_NEWS_GROUPS. " as lnd
											ON (ln.news_groups_id = lnd.news_groups_id)
											WHERE lnd.news_groups_id ='" . (int)$_GET['cID'] . "'";
    	$latest_news_groups_result_sql = tep_db_query($latest_news_groups_select_sql);
      	while ($latest_news_groups_row = tep_db_fetch_array($latest_news_groups_result_sql)) {
			$news_groups_edit_array[$latest_news_groups_row['language_id']]['news_groups_edit_tab'] = $latest_news_groups_row['news_groups_name'];
			$news_groups_edit_sort_order = $latest_news_groups_row['news_groups_sort_order'];
      	}
    }
?>

<script language="javascript"><!--
function check_form() {
  var error = 0;

  var news_groups_name = document.getElementById('news_groups_edit_array').value;

  if (news_groups_name == "") {
    error_message = "<?php echo ERROR_NEWS_GROUPS_NAME; ?>";
    error = 1;
  }

  if (error == 1) {
    alert(error_message);
    return false;
  } else {
    return true;
  }
}
//--></script>

      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr><?php echo tep_draw_form('news', FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('action')) . 'action=update', 'post', 'onSubmit="return check_form();"'); ?>
        <td class="formAreaTitle"><?php echo CATEGORY_PERSONAL; ?></td>
      </tr>
      <tr>	
        <td class="formArea"><table border="0" cellspacing="2" cellpadding="2">
          <tr>
			<td>
          	<div id="languages_tab">
	        <ul>
<?	
		for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { 
?>
				<li id="languages_li_<?=$lang_cnt?>"><a href="#languages_tab_<?=$lang_cnt?>"><span><?=$languages[$lang_cnt]['name']?></span></a></li>
<?	
		} 
?>
			</ul> 
<?	
		for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { 
?>
				<div id="languages_tab_<?=$lang_cnt?>" style="border:1px solid #C9C9C9;">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td class="main"><?=TEXT_LATEST_NEWS_GROUPS ?></td>
						<td class="main"><?=tep_draw_input_field('news_groups_edit_tab[' . $languages[$lang_cnt]['id'] . ']', (isset($news_groups_edit_array[$languages[$lang_cnt]['id']]['news_groups_edit_tab'])?$news_groups_edit_array[$languages[$lang_cnt]['id']]['news_groups_edit_tab']:'') , 'id="news_groups_edit_tab" maxlength="32"', true )?></td>
					</tr> 
					</td>
			  	</table>
			  	</div>
<? 
		} 
?>
			</div><!--languages_tab-->
					<tr>
						<td class="main"><?php echo ENTRY_SORT_ORDER; ?></td>
		           		<td class="main"> <?=tep_draw_input_field('news_groups_sort_order', $news_groups_edit_sort_order)?></td>
					</tr>
					<tr>
						<td class="main"><?php echo ENTRY_DISPLAY_STATUS; ?></td>
		           		<td class="main"> <?=tep_draw_selection_field('news_groups_display_status', 'radio', '1', true)?>
		            	<?php echo ENTRY_VALUE_DISPLAY; ?><?=tep_draw_selection_field('news_groups_display_status', 'radio', '0', false)?><?php echo ENTRY_VALUE_NODISPLAY; ?></td>
					</tr>
            </td>
          </tr>
	    <script type="text/javascript">
			jQuery(document).ready(function() {
				jQuery.noConflict();
				jQuery("#languages_tab > ul").tabs();
			});
		</script>
		</table>
        </td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr>
        <td align="right" class="main"><?php echo tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('action','cID'))) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'; ?></td>
      </tr>
      
      </form>
	  <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '70'); ?></td>
      </tr>
      
<?php
  } else if($_GET['action'] == 'new') {
     $languages = tep_get_languages();
     $news_groups_array = array();
?>

<script language="javascript"><!--
function check_form() {
  var error = 0;

  var news_groups_name = document.getElementById('news_groups_tab').value;

  if (news_groups_name == "") {
    error_message = "<?php echo ERROR_NEWS_GROUPS_NAME; ?>";
    error = 1;
  }

  if (error == 1) {
    alert(error_message);
    return false;
  } else {
    return true;
  }
}
//--></script>

      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr><?php echo tep_draw_form('news', FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('action')) . 'action=newconfirm', 'post', 'onSubmit="return check_form();"'); ?>
        <td class="formAreaTitle"><?php echo CATEGORY_PERSONAL; ?></td>
      </tr>
      <tr>	
        <td class="formArea"><table border="0" cellspacing="2" cellpadding="2">
          <tr>
			<td>
          	<div id="languages_tab">
	        <ul>
	<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
				<li id="languages_li_<?=$lang_cnt?>"><a href="#languages_tab_<?=$lang_cnt?>"><span><?=$languages[$lang_cnt]['name']?></span></a></li>
	<?	} ?>
			</ul> 
		<?	for ($lang_cnt=0, $n=sizeof($languages); $lang_cnt<$n; $lang_cnt++) { ?>
				<div id="languages_tab_<?=$lang_cnt?>" style="border:1px solid #C9C9C9;">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td class="main"><?=TEXT_LATEST_NEWS_GROUPS ?></td>
						<td class="main"><?=tep_draw_input_field('news_groups_tab[' . $languages[$lang_cnt]['id'] . ']', (isset($news_groups_array[$languages[$lang_cnt]['id']]['news_groups_tab'])?$latest_news_array[$languages[$lang_cnt]['id']]['news_groups_tab']:'') , 'id="news_groups_tab" maxlength="32"', true )?></td>
					</tr> 
					</td>
			  	</table>
			  	</div>
	<? } ?>
			</div><!--languages_tab-->
					<tr>
						<td class="main"><?php echo ENTRY_SORT_ORDER; ?></td>
		           		<td class="main"> <?=tep_draw_input_field('news_groups_sort_order')?></td>
					</tr>
					<tr>
						<td class="main"><?php echo ENTRY_DISPLAY_STATUS; ?></td>
		           		<td class="main"> <?=tep_draw_selection_field('news_groups_display_status', 'radio', '1', true)?>
		            	<?php echo ENTRY_VALUE_DISPLAY; ?><?=tep_draw_selection_field('news_groups_display_status', 'radio', '0', false)?><?php echo ENTRY_VALUE_NODISPLAY; ?></td>
					</tr>
            </td>
          </tr>
	    <script type="text/javascript">
		jQuery(document).ready(function() {
			jQuery.noConflict();
			jQuery("#languages_tab > ul").tabs();
		});
		</script>
		</table>
        </td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      </tr>
      <tr>
        <td align="right" class="main"><?php echo tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('action','cID'))) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'; ?></td>
      </tr>
      
      </form>
<?php 
  } else {
?>
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr><?php echo tep_draw_form('search', FILENAME_LATEST_NEWS_GROUPS, '', 'get'); ?>
            <td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
            <td class="smallText" align="right"><?php echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('search'); ?></td>
          </form></tr>
        </table></td>
      </tr>
      <tr>

<?
          switch ($listing) {
              case "id-asc":
              $order = "lng.news_groups_id";
              break;
              case "group":
              $order = "lngd.news_groups_name";
              break;
              case "group-desc":
              $order = "lngd.news_groups_name DESC";
              break;
              default:
              $order = "lng.news_groups_sort_order";
          }
?>
	    <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
               <tr class="dataTableHeadingRow">
			       <td class="dataTableHeadingContent" width="30%"><?php echo TABLE_HEADING_NAME; ?>&nbsp;<a href="<?php echo "$PHP_SELF?listing=group"; ?>"><b>Asc</b></a>&nbsp;<a href="<?php echo "$PHP_SELF?listing=group-desc"; ?>"><b>Desc</b></a></td>
			       <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_SORT; ?>&nbsp;</td>
                   <td class="dataTableHeadingContent" align="center"><?php echo TABLE_HEADING_STATUS; ?>&nbsp;</td>
                   <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</td>
			   </tr>

<?php
    $search = '';
    if ( ($_GET['search']) && (tep_not_null($_GET['search'])) ) {
      $keywords = tep_db_input(tep_db_prepare_input($_GET['search']));
      $search = " AND lngd.news_groups_name like '%" . $keywords . "%'";
    }

	$news_groups_query_raw = "	SELECT lng.news_groups_id, lng.news_groups_display_status,lng.news_groups_sort_order, lngd.news_groups_id, lngd.language_id, lngd.news_groups_name from " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION . " as lngd
    							INNER JOIN " . TABLE_LATEST_NEWS_GROUPS. " as lng  
    								ON (lngd.news_groups_id = lng.news_groups_id)
    							WHERE lngd.language_id = '" . $_SESSION['languages_id'] . "' " . $search . " 
    							ORDER BY $order";
    $news_groups_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $news_groups_query_raw, $news_groups_query_numrows);
    $news_groups_query = tep_db_query($news_groups_query_raw);

    while ($news_groups = tep_db_fetch_array($news_groups_query)) {
	  $info_query = tep_db_query("select lnd.news_id, lnd.headline, lnd.language_id, lnd.content, ln.date_added, ln.news_groups_id from " . TABLE_LATEST_NEWS . " as ln inner join ".TABLE_LATEST_NEWS_DESCRIPTION." as lnd on ln.news_id = lnd.news_id where lnd.news_id = '" . $_GET['news_groups_id'] . "'");	
      
      $info = tep_db_fetch_array($info_query);

      if (((!$_GET['cID']) || (@$_GET['cID'] == $news_groups['news_groups_id'])) && (!$cInfo)) {
        $cInfo = new objectInfo($news_groups);
      }

      if ( (is_object($cInfo)) && ($news_groups['news_groups_id'] == $cInfo->news_groups_id) ) {
        echo '          <tr class="dataTableRowSelected"">' . "\n";
      } else {
        echo '          <tr class="dataTableRow" onmouseout="this.className=\'dataTableRow\'" onmouseover="this.className=\'dataTableRowOver\'" style="cursor:default">' . "\n";
      }
?>
                <td class="dataTableContent"><?php echo $news_groups['news_groups_name']; ?></td>
                <td class="dataTableContent"><?php echo $news_groups['news_groups_sort_order']; ?></td>
				<td class="dataTableContent" valign="top">
					<div style="text-align: center;" id="status-<?php echo $news_groups['news_groups_id']; ?>">
<?php
					if ($news_groups['news_groups_display_status'] == "1")	{
?>
						<img src="images/icon_status_green.gif" border=0> <a href="javascript:change_status('<?php echo $news_groups['news_groups_id']; ?>')"><img src="images/icon_status_red_light.gif" border=0></a>
<?php
					}
					else {
?>
						<a href="javascript:change_status('<?php echo $news_groups['news_groups_id']; ?>')"><img src="images/icon_status_green_light.gif" border=0></a> <img src="images/icon_status_red.gif" border=0>
<?php
					}
?>
					</div>
				</td>
                <td class="dataTableContent" align="right">
<?php
      if ( (is_object($cInfo)) && ($news_groups['news_groups_id'] == $cInfo->news_groups_id) ) {
        echo '          <a href="' . tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->news_groups_id . '&action=edit') . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . "</a>\n";
      } else {
        echo '          <a href="' . tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('cID')) . 'cID=' . $news_groups['news_groups_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . "</a>\n";
      }
?>
				</td>
              </tr>
<?php
    }
?>
              <tr>
                <td colspan="4"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                  <tr>
                    <td class="smallText" valign="top"><?php echo $news_groups_split->display_count($news_groups_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_GROUPS); ?></td>
                    <td class="smallText" align="right"><?php echo $news_groups_split->display_links($news_groups_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y', 'cID'))); ?></td>
                  </tr>
<?php
    if (tep_not_null($_GET['search'])) {
?>
                  <tr>
                    <td align="right" colspan="2"><?php echo '<a href="' . tep_href_link(FILENAME_LATEST_NEWS_GROUPS) . '">' . tep_image_button('button_reset.gif', IMAGE_RESET) . '</a>'; ?></td>
                  </tr>
<?php
    } else {
?>
			      <tr>
                    <td align="right" colspan="2" class="smallText"><?php echo '<a href="' . tep_href_link(FILENAME_LATEST_NEWS_GROUPS, 'page=' . $_GET['page'] . '&action=new') . '">' . tep_image_button('button_insert.gif', IMAGE_INSERT) . '</a>'; ?></td>
                  </tr>
<?php
	}
?>
                </table></td>
              </tr>
            </table></td>
<?php
  $heading = array();
  $contents = array();
  switch ($_GET['action']) {
    case 'confirm':
        if ($_GET['cID'] != 1) {
            $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_GROUP . '</b>');
            $contents = array('form' => tep_draw_form('news_groups', FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->news_groups_id . '&action=deleteconfirm'));
            $contents[] = array('text' => TEXT_DELETE_INTRO . '<br><br><b>' . $cInfo->news_groups_name . ' </b>');
            $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->news_groups_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
        } else {
            $heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_GROUP . '</b>');
            $contents[] = array('text' => 'Non e\' consentito cancellare il gruppo:<br><br><b>' . $cInfo->news_groups_name . ' </b>');
        }
      break;
    default:
      if (is_object($cInfo)) {
        $heading[] = array('text' => '<b>' . $cInfo->news_groups_name . ' </b>');
        $contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->news_groups_id . '&action=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_LATEST_NEWS_GROUPS, tep_get_all_get_params(array('cID', 'action')) . 'cID=' . $cInfo->news_groups_id . '&action=confirm') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');

      }
      break;
  }

  if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
    echo '            <td width="25%" valign="top">' . "\n";

    $box = new box;
    echo $box->infoBox($heading, $contents);

    echo '            </td>' . "\n";
  }
?>
          </tr>
        </table></td>
      </tr>
<?php
  }
?>
    </table></td>
<!-- body_text_eof //-->
  </tr>
</table>
<script language="javascript">
<!--
jQuery.noConflict();

function change_status(id) {
	jQuery.get("?action=changestatus&id="+id, function(data){
		if (data)
			jQuery('#status-'+id).html(data);
	});
}

//-->
</script>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>