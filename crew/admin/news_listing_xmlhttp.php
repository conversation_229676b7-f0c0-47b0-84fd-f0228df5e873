<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

require('includes/application_top.php');

$action = isset($_GET['action']) ? $_GET['action'] : '';
$news_id = isset($_GET['news_id']) ? (int)$_GET['news_id'] : '';
$language_id = isset($_GET['lang']) ? (int)$_GET['lang'] : '';
$content_text = '';

if (isset($language_id) && tep_not_null($language_id)) {
	$language_dir_select_sql = "SELECT directory FROM " . TABLE_LANGUAGES . " WHERE  languages_id = '" . tep_db_input($language_id) . "'";
	$language_dir_result_sql = tep_db_query($language_dir_select_sql);
	$language_dir_row = tep_db_fetch_array($language_dir_result_sql);

	if (file_exists(DIR_WS_LANGUAGES . $language_dir_row["directory"] . ".php")) {
		include_once(DIR_WS_LANGUAGES . $language_dir_row["directory"] . ".php");
	}
}

$news_id_where_str = tep_not_null($news_id) ? " news_groups_id = '" . tep_db_input($news_id) . "'" : ' 1 ';

echo '<response>';
	switch($action) {
		case "update_event_status" :
				$events_update_sql = " 	UPDATE " . TABLE_EVENTS . " 
										SET events_status = '" . tep_db_input($_GET['flag']) . "'
				    					WHERE events_id = '" . tep_db_input($_GET['eID']) . "'";
				tep_db_query($events_update_sql);

				$events_select_sql = " 	SELECT events_id, events_status 
										FROM " . TABLE_EVENTS . " 
										WHERE events_id = '" . tep_db_input($_GET['eID']) . "'";
				$events_result_sql = tep_db_query($events_select_sql);
		 		$events_row = tep_db_fetch_array($events_result_sql);
				
				echo "<item>
						<image_status><![CDATA[";
				if ($events_row['events_status'] == '1') {
					$content_text = tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="javascript:change_status(\''.$_GET['eID'].'\',\'0\',\'update_event_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
				} else {
				    $content_text = '<a href="javascript:change_status(\''.$_GET['eID'].'\',\'1\',\'update_event_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
				}
				echo $content_text;
				echo "]]></image_status>
					</item>";
			break;
			
		case "update_option_status" :
				$options_update_sql = " UPDATE " . TABLE_EVENTS_OPTIONS . " 
										SET events_options_status = '" . tep_db_input($_GET['flag']) . "'
					    				WHERE events_options_id = '" . tep_db_input($_GET['oID']) . "'";
				tep_db_query($options_update_sql);
				
				$options_select_sql = " SELECT events_options_id, events_options_status 
										FROM " . TABLE_EVENTS_OPTIONS . " 
										WHERE events_options_id = '" . tep_db_input($_GET['oID']) . "'";
				$options_result_sql = tep_db_query($options_select_sql);
		 		$options_row = tep_db_fetch_array($options_result_sql);
				echo "<item>
						<image_status><![CDATA[";
				if ($options_row['events_options_status'] == '1') {
					$content_text = tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="javascript:change_status(\''.$_GET['oID'].'\',\'0\',\'update_option_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
				} else {
				    $content_text = '<a href="javascript:change_status(\''.$_GET['oID'].'\',\'1\',\'update_option_status\')">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
				}
				echo $content_text;
				echo "]]></image_status>
					</item>";
			break;
			
		default:
			echo "<selection>";
				$news_select_sql = "SELECT ln.news_id, lnd.headline 
									FROM " . TABLE_LATEST_NEWS . " AS ln
									INNER JOIN ".TABLE_LATEST_NEWS_DESCRIPTION." AS lnd
										ON ln.news_id = lnd.news_id 
									WHERE ".$news_id_where_str." 
										AND ln.status = '1' 
										AND lnd.language_id = '".(int)$language_id."' 
									ORDER BY ln.date_added DESC";
				$news_result_sql = tep_db_query($news_select_sql);
				
				while ($news_row = tep_db_fetch_array($news_result_sql)) {
					echo "<option index='".$news_row['news_id']."'><![CDATA[".strip_tags($news_row['headline'])."]]></option>";
				}
			echo "</selection>";
		break;
	}
echo '</response>';
?>