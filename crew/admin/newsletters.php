<?php
/*
  	$Id: newsletters.php,v 1.8 2006/07/17 07:37:15 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

if (tep_not_null($action)) {
    switch ($action) {
      	case 'lock':
      	case 'unlock':
        	$newsletter_id = tep_db_prepare_input($HTTP_GET_VARS['nID']);
        	$status = (($action == 'lock') ? '1' : '0');
			
        	tep_db_query("update " . TABLE_NEWSLETTERS . " set locked = '" . $status . "' where newsletters_id = '" . (int)$newsletter_id . "'");
			
        	tep_redirect(tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $HTTP_GET_VARS['nID']));
        	break;
      	case 'insert':
      	case 'update':
        	if (isset($HTTP_POST_VARS['newsletter_id'])) $newsletter_id = tep_db_prepare_input($HTTP_POST_VARS['newsletter_id']);
        	$newsletters_groups_id = tep_db_prepare_input($HTTP_POST_VARS['newsletters_groups_id']);
        	$title = tep_db_prepare_input($HTTP_POST_VARS['title']);
        	$content = tep_db_prepare_input($HTTP_POST_VARS['content']);
			
        	$newsletter_error = false;
        	if (empty($title)) {
          		$messageStack->add(ERROR_NEWSLETTER_TITLE, 'error');
          		$newsletter_error = true;
        	}
			
        	if (empty($newsletters_groups_id)) {
          		$messageStack->add(ERROR_NEWSLETTER_GROUP, 'error');
          		$newsletter_error = true;
        	}
			
        	if ($newsletter_error == false) {
          		$sql_data_array = array('title' => $title,
                                  		'content' => $content,
                                  		'newsletters_groups_id' => $newsletters_groups_id);
          		if ($action == 'insert') {
            		$sql_data_array['date_added'] = 'now()';
            		$sql_data_array['status'] = '0';
            		$sql_data_array['locked'] = '0';
					
            		tep_db_perform(TABLE_NEWSLETTERS, $sql_data_array);
            		$newsletter_id = tep_db_insert_id();
          		} else if ($action == 'update') {
            		tep_db_perform(TABLE_NEWSLETTERS, $sql_data_array, 'update', "newsletters_id = '" . (int)$newsletter_id . "'");
          		}
				
          		tep_redirect(tep_href_link(FILENAME_NEWSLETTERS, (isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] . '&' : '') . 'nID=' . $newsletter_id));
        	} else {
          		$action = 'new';
        	}
        	break;
      	case 'deleteconfirm':
        	$newsletter_id = tep_db_prepare_input($HTTP_GET_VARS['nID']);
			
        	tep_db_query("delete from " . TABLE_NEWSLETTERS . " where newsletters_id = '" . (int)$newsletter_id . "'");
			
        	tep_redirect(tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page']));
        	break;
      	case 'delete':
      	case 'new': if (!isset($HTTP_GET_VARS['nID'])) break;
      	case 'send':
      	case 'confirm_send':
        	$newsletter_id = tep_db_prepare_input($HTTP_GET_VARS['nID']);
			
        	$check_query = tep_db_query("select locked from " . TABLE_NEWSLETTERS . " where newsletters_id = '" . (int)$newsletter_id . "'");
        	$check = tep_db_fetch_array($check_query);
			
        	if ($check['locked'] < 1) {
          		switch ($action) {
            		case 'delete': $error = ERROR_REMOVE_UNLOCKED_NEWSLETTER; break;
            		case 'new': $error = ERROR_EDIT_UNLOCKED_NEWSLETTER; break;
            		case 'send': $error = ERROR_SEND_UNLOCKED_NEWSLETTER; break;
            		case 'confirm_send': $error = ERROR_SEND_UNLOCKED_NEWSLETTER; break;
          		}
				
          		$messageStack->add_session($error, 'error');
				
          		tep_redirect(tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $HTTP_GET_VARS['nID']));
        	}
        	
        	break;
        case 'insert_group':
      	case 'update_group':
			if (isset($HTTP_POST_VARS['newsletters_groups_id'])) $newsletters_groups_id = tep_db_prepare_input($HTTP_POST_VARS['newsletters_groups_id']);
        	
        	$group_name = tep_db_prepare_input($HTTP_POST_VARS['newsletters_groups_name']);
        	$module = tep_db_prepare_input($HTTP_POST_VARS['module']);
        	$sort_order = tep_db_prepare_input($HTTP_POST_VARS['newsletters_groups_sort_order']);
			
			$name_replace = ereg_replace_dep(" ", "", strtolower($group_name));
			
        	$newsletter_error = false;
        	if (empty($group_name)) {
          		$messageStack->add(ERROR_NEWSLETTER_GROUP_NAME, 'error');
          		$newsletter_error = true;
        	}
			
        	if (empty($module)) {
          		$messageStack->add(ERROR_NEWSLETTER_MODULE, 'error');
          		$newsletter_error = true;
        	}
			
			$check_groups_name_select_sql = "	SELECT newsletters_groups_name 
												FROM " . TABLE_NEWSLETTERS_GROUPS . " 
												WHERE LOWER(REPLACE(newsletters_groups_name, ' ', '')) = '" . tep_db_input($name_replace) . "' " .
													($action == 'update_group' ? " AND newsletters_groups_id <> '" . (int)$newsletters_groups_id ."'" : '') ;
			$check_groups_name_result_sql = tep_db_query($check_groups_name_select_sql);
      		$check_duplicate = tep_db_num_rows($check_groups_name_result_sql);
      		if ($check_duplicate > 0) {
      			$messageStack->add(ERROR_NEWSLETTER_GROUPS_NAME_USED);
				$newsletter_error = true;
      		}
      			
        	if ($newsletter_error == false) {
          		$sql_data_array = array('newsletters_groups_name' => $group_name,
                                  		'module' => $module,
                                  		'newsletters_groups_sort_order' => $sort_order);
          		if ($action == 'insert_group') {
            		tep_db_perform(TABLE_NEWSLETTERS_GROUPS, $sql_data_array);
            		$newsletters_groups_id = tep_db_insert_id();
          		} else if ($action == 'update_group') {
            		tep_db_perform(TABLE_NEWSLETTERS_GROUPS, $sql_data_array, 'update', "newsletters_groups_id = '" . (int)$newsletters_groups_id . "'");
          		}
				
          		tep_redirect(tep_href_link(FILENAME_NEWSLETTERS, (isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] . '&' : '') . 'ngID=' . $newsletters_groups_id));
        	} else {
          		$action = 'new_group';
        	}
        	break;
        case 'confirm_delete_group':
        	$newsletters_groups_id = tep_db_prepare_input($HTTP_GET_VARS['ngID']);
			
			$unlocked_newsletter_select_sql = "SELECT locked FROM " . TABLE_NEWSLETTERS . " WHERE newsletters_groups_id = '" . (int)$newsletters_groups_id . "' AND locked < 1";
			$unlocked_newsletter_result_sql = tep_db_query($unlocked_newsletter_select_sql);
			
        	if (tep_db_num_rows($unlocked_newsletter_result_sql)) {
        		$messageStack->add_session(ERROR_REMOVE_UNLOCKED_NEWSLETTER, 'error');
        	} else {
				$newsletters_delete_sql = "DELETE FROM " . TABLE_NEWSLETTERS . " WHERE newsletters_groups_id = '" . (int)$newsletters_groups_id . "'";
				tep_db_query($newsletters_delete_sql);
				
				$newsletters_group_delete_sql = "DELETE FROM " . TABLE_NEWSLETTERS_GROUPS . " WHERE newsletters_groups_id = '" . (int)$newsletters_groups_id . "'";
				tep_db_query($newsletters_group_delete_sql);
			}
        	tep_redirect(tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page']));
        	break;
    }
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">

<script language="Javascript1.2"><!-- // load htmlarea
// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Newsletter <head>
      _editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
        var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
         if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
          if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
           if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
       <?php if (HTML_AREA_WYSIWYG_BASIC_NEWSLETTER == 'Basic'){ ?>  if (win_ie_ver >= 5.5) {
       document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
       document.write(' language="Javascript1.2"></scr' + 'ipt>');
          } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
       <?php } else{ ?> if (win_ie_ver >= 5.5) {
       document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
       document.write(' language="Javascript1.2"></scr' + 'ipt>');
          } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
       <?php }?>
// --></script>

<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
					<!-- left_navigation_eof //-->
	    		</table>
	    	</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
						            <td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
								</tr>
        					</table>
        				</td>
      				</tr>
<?
if ($action == 'new_group') {
	$form_action = 'insert_group';
	
    $parameters = array('newsletters_groups_name' => '',
                        'module' => '',
                        'newsletters_groups_sort_order' => '');
	
    $ngInfo = new objectInfo($parameters);
	
    if (isset($HTTP_GET_VARS['ngID'])) {
      	$form_action = 'update_group';
		
      	$ngID = tep_db_prepare_input($HTTP_GET_VARS['ngID']);
		
		$newsletter_group_select_sql = "SELECT newsletters_groups_name, module, newsletters_groups_sort_order 
										FROM " . TABLE_NEWSLETTERS_GROUPS . " 
										WHERE newsletters_groups_id = '" . (int)$ngID . "'";
      	$newsletter_group_result_sql = tep_db_query($newsletter_group_select_sql);
      	$newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql);
		
      	$ngInfo->objectInfo($newsletter_group_row);
    } else if ($HTTP_POST_VARS) {
      	$ngInfo->objectInfo($HTTP_POST_VARS);
    }
    
    $file_extension = substr($PHP_SELF, strrpos($PHP_SELF, '.'));
    $directory_array = array();
    if ($dir = dir(DIR_WS_MODULES . 'newsletters/')) {
      	while ($file = $dir->read()) {
        	if (!is_dir(DIR_WS_MODULES . 'newsletters/' . $file)) {
          		if (substr($file, strrpos($file, '.')) == $file_extension) {
            		$directory_array[] = $file;
          		}
        	}
      	}
      	sort($directory_array);
      	$dir->close();
    }
	
    for ($i=0, $n=sizeof($directory_array); $i<$n; $i++) {
      	$modules_array[] = array('id' => substr($directory_array[$i], 0, strrpos($directory_array[$i], '.')), 'text' => substr($directory_array[$i], 0, strrpos($directory_array[$i], '.')));
    }
?>
					<tr>
        				<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      				</tr>
      				<tr>
        				<td>
        				<?
        					echo tep_draw_form('newsletter_group_form', FILENAME_NEWSLETTERS, (isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] . '&' : '') . 'action=' . $form_action);
        					if ($form_action == 'update_group') echo tep_draw_hidden_field('newsletters_groups_id', $ngID);
        				?>
        					<table border="0" cellspacing="0" cellpadding="2">
        						<tr>
						            <td class="main"><?=ENTRY_NEWSLETTER_GROUP_NAME?></td>
						            <td class="main"><?=tep_draw_input_field('newsletters_groups_name', $ngInfo->newsletters_groups_name, '', true)?></td>
          						</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
						            <td class="main"><?=ENTRY_NEWSLETTER_MODULE?></td>
						            <td class="main"><?=tep_draw_pull_down_menu('module', $modules_array, $ngInfo->module)?></td>
          						</tr>
          						<tr>
            						<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          						</tr>
          						<tr>
						            <td class="main"><?=ENTRY_NEWSLETTER_GROUP_SORT_ORDER?></td>
						            <td class="main"><?=tep_draw_input_field('newsletters_groups_sort_order', $ngInfo->newsletters_groups_sort_order)?></td>
          						</tr>
          					</table>
          				</td>
      				</tr>
      				<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
          						<tr>
            						<td class="main" align="right"><? echo (($form_action == 'insert_group') ? tep_image_submit('button_save.gif', IMAGE_SAVE) : tep_image_submit('button_update.gif', IMAGE_UPDATE)). '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_NEWSLETTERS, (isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] . '&' : '') . (isset($HTTP_GET_VARS['nID']) ? 'nID=' . $HTTP_GET_VARS['nID'] : '')) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'; ?></td>
          						</tr>
        					</table>
        				</td>
      					</form>
      				</tr>
<?
} else if ($action == 'new') {
	$groups_array = array();
	
	$form_action = 'insert';
	
    $parameters = array('title' => '',
                        'content' => '',
                        'newsletters_groups_id' => $HTTP_GET_VARS['ngID']);
	
    $nInfo = new objectInfo($parameters);
	
    if (isset($HTTP_GET_VARS['nID'])) {
      	$form_action = 'update';
		
      	$nID = tep_db_prepare_input($HTTP_GET_VARS['nID']);
		
      	$newsletter_query = tep_db_query("select title, content, newsletters_groups_id from " . TABLE_NEWSLETTERS . " where newsletters_id = '" . (int)$nID . "'");
      	$newsletter = tep_db_fetch_array($newsletter_query);
		
      	$nInfo->objectInfo($newsletter);
    } else if ($HTTP_POST_VARS) {
      	$nInfo->objectInfo($HTTP_POST_VARS);
    }
    
    $newsletters_groups_select_sql = "	SELECT newsletters_groups_id, newsletters_groups_name 
    									FROM " . TABLE_NEWSLETTERS_GROUPS . " 
    									ORDER BY newsletters_groups_sort_order, newsletters_groups_name";
    $newsletters_groups_result_sql = tep_db_query($newsletters_groups_select_sql);
    while ($newsletters_groups_row = tep_db_fetch_array($newsletters_groups_result_sql)) {
    	$groups_array[] = array('id' => $newsletters_groups_row['newsletters_groups_id'], 'text' => $newsletters_groups_row['newsletters_groups_name']);
    }
?>
      				<tr>
        				<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      				</tr>
      				<tr>
        				<td>
        				<?
        					echo tep_draw_form('newsletter', FILENAME_NEWSLETTERS, (isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] . '&' : '') . 'action=' . $form_action);
        					if ($form_action == 'update') echo tep_draw_hidden_field('newsletter_id', $nID);
        				?>
        					<table border="0" cellspacing="0" cellpadding="2">
          						<tr>
						            <td class="main"><?=ENTRY_NEWSLETTER_GROUP?></td>
						            <td class="main"><?=tep_draw_pull_down_menu('newsletters_groups_id', $groups_array, $nInfo->newsletters_groups_id)?></td>
          						</tr>
          						<tr>
            						<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          						</tr>
          						<tr>
						            <td class="main"><?php echo TEXT_NEWSLETTER_TITLE; ?></td>
						            <td class="main"><?php echo tep_draw_input_field('title', $nInfo->title, '', true); ?></td>
          						</tr>
          						<tr>
            						<td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          						</tr>
          						<tr>
            						<td class="main" valign="top"><?php echo TEXT_NEWSLETTER_CONTENT; ?></td>
            						<td class="main"><?php echo tep_draw_textarea_field('content', 'soft', '100%', '20', $nInfo->content); ?></td>
<?	if (HTML_AREA_WYSIWYG_DISABLE_NEWSLETTER == 'Enable') { ?>
		<script language="JavaScript1.2" defer>
			// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 HTML Newsletter <body>
           	var config = new Object();  // create new config object
           	config.width = "<?php echo NEWSLETTER_EMAIL_WYSIWYG_WIDTH; ?>px";
           	config.height = "<?php echo NEWSLETTER_EMAIL_WYSIWYG_HEIGHT; ?>px";
           	config.bodyStyle = 'background-color: <?php echo HTML_AREA_WYSIWYG_BG_COLOUR; ?>; font-family: "<?php echo HTML_AREA_WYSIWYG_FONT_TYPE; ?>"; color: <?php echo HTML_AREA_WYSIWYG_FONT_COLOUR; ?>; font-size: <?php echo HTML_AREA_WYSIWYG_FONT_SIZE; ?>pt;';
           	config.debug = <?php echo HTML_AREA_WYSIWYG_DEBUG; ?>;
           	editor_generate('content',config);
		</script>
<?	}
	// MaxiDVD Added HTML is ON when WYSIWYG BOX Enabled, HTML is OFF when WYSIWYG Disabled
?>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
          						<tr>
            						<td class="main" align="right"><?php echo (($form_action == 'insert') ? tep_image_submit('button_save.gif', IMAGE_SAVE) : tep_image_submit('button_update.gif', IMAGE_UPDATE)). '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_NEWSLETTERS, (isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] . '&' : '') . (isset($HTTP_GET_VARS['nID']) ? 'nID=' . $HTTP_GET_VARS['nID'] : '')) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'; ?></td>
          						</tr>
        					</table>
        				</td>
      					</form>
      				</tr>
<?
} else if ($action == 'preview') {
    $nID = tep_db_prepare_input($HTTP_GET_VARS['nID']);
	
    $newsletter_query = tep_db_query("select content from " . TABLE_NEWSLETTERS . " where newsletters_id = '" . (int)$nID . "'");
    $newsletter = tep_db_fetch_array($newsletter_query);
	
    $nInfo = new objectInfo($newsletter);
?>
      				<tr>
        				<td><tt><?=$nInfo->content?></tt></td>
      				</tr>
      				<tr>
        				<td align="right"><?php echo '<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $HTTP_GET_VARS['nID']) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'; ?></td>
      				</tr>
<?
} else if ($action == 'send') {
    $nID = tep_db_prepare_input($HTTP_GET_VARS['nID']);
	
	$newsletter_select_sql = "	SELECT n.title, n.content, ng.module 
								FROM " . TABLE_NEWSLETTERS . " AS n 
								INNER JOIN " . TABLE_NEWSLETTERS_GROUPS . " AS ng 
									ON n.newsletters_groups_id=ng.newsletters_groups_id 
								WHERE n.newsletters_id = '" . (int)$nID . "'";
    $newsletter_result_sql = tep_db_query($newsletter_select_sql);
    $newsletter_row = tep_db_fetch_array($newsletter_result_sql);
    
    $nInfo = new objectInfo($newsletter_row);
	
    include(DIR_WS_LANGUAGES . $language . '/modules/newsletters/' . $nInfo->module . substr($PHP_SELF, strrpos($PHP_SELF, '.')));
    include(DIR_WS_MODULES . 'newsletters/' . $nInfo->module . substr($PHP_SELF, strrpos($PHP_SELF, '.')));
    $module_name = $nInfo->module;
    $nInfo->content = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $nInfo->content);
    $module = new $module_name((int)$nID, $nInfo->title, $nInfo->content);
?>
      				<tr>
        				<td><? if ($module->show_choose_audience) { echo $module->choose_audience(); } else { echo $module->confirm(); } ?></td>
      				</tr>
<?
} else if ($action == 'confirm') {
	$nID = tep_db_prepare_input($HTTP_GET_VARS['nID']);
	
	$newsletter_select_sql = "	SELECT n.title, n.content, ng.module 
								FROM " . TABLE_NEWSLETTERS . " AS n 
								INNER JOIN " . TABLE_NEWSLETTERS_GROUPS . " AS ng 
									ON n.newsletters_groups_id=ng.newsletters_groups_id 
								WHERE n.newsletters_id = '" . (int)$nID . "'";
    $newsletter_result_sql = tep_db_query($newsletter_select_sql);
    $newsletter_row = tep_db_fetch_array($newsletter_result_sql);
	
    $nInfo = new objectInfo($newsletter_row);
	
    include(DIR_WS_LANGUAGES . $language . '/modules/newsletters/' . $nInfo->module . substr($PHP_SELF, strrpos($PHP_SELF, '.')));
    include(DIR_WS_MODULES . 'newsletters/' . $nInfo->module . substr($PHP_SELF, strrpos($PHP_SELF, '.')));
    $module_name = $nInfo->module;
    $nInfo->content = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $nInfo->content);
    $module = new $module_name((int)$nID, $nInfo->title, $nInfo->content);
?>
					<tr>
						<td><? echo $module->confirm(); ?></td>
					</tr>
<?
} else if ($action == 'confirm_send') {
	$nID = tep_db_prepare_input($HTTP_GET_VARS['nID']);
	
	$newsletter_select_sql = "	SELECT n.title, n.content, ng.module 
								FROM " . TABLE_NEWSLETTERS . " AS n 
								INNER JOIN " . TABLE_NEWSLETTERS_GROUPS . " AS ng 
									ON n.newsletters_groups_id=ng.newsletters_groups_id 
								WHERE n.newsletters_id = '" . (int)$nID . "'";
    $newsletter_result_sql = tep_db_query($newsletter_select_sql);
    $newsletter_row = tep_db_fetch_array($newsletter_result_sql);
    
    $nInfo = new objectInfo($newsletter_row);

    include(DIR_WS_LANGUAGES . $language . '/modules/newsletters/' . $nInfo->module . substr($PHP_SELF, strrpos($PHP_SELF, '.')));
    include(DIR_WS_MODULES . 'newsletters/' . $nInfo->module . substr($PHP_SELF, strrpos($PHP_SELF, '.')));
    $module_name = $nInfo->module;
    $nInfo->content = str_replace(array("<br>", "<BR>", "\r\n"), array("\n", "\n", ''), $nInfo->content);
    $module = new $module_name((int)$nID, $nInfo->title, $nInfo->content);
?>
      				<tr>
        				<td>
        					<table border="0" cellspacing="0" cellpadding="2">
          						<tr>
            						<td class="main" valign="middle"><?php echo tep_image(DIR_WS_IMAGES . 'ani_send_email.gif', IMAGE_ANI_SEND_EMAIL); ?></td>
            						<td class="main" valign="middle"><b><?php echo TEXT_PLEASE_WAIT; ?></b></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
<?
  	tep_set_time_limit(0);
  	flush();
  	$module->send($nInfo->newsletters_id);
?>
      				<tr>
        				<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      				</tr>
      				<tr>
        				<td class="main"><font color="#ff0000"><b><?php echo TEXT_FINISHED_SENDING_EMAILS; ?></b></font></td>
      				</tr>
      				<tr>
        				<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      				</tr>
      				<tr>
        				<td><?php echo '<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $HTTP_GET_VARS['nID']) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'; ?></td>
      				</tr>
<?
} else {
	$newsletters_groups_select_sql = "select newsletters_groups_id, newsletters_groups_name, module from " . TABLE_NEWSLETTERS_GROUPS . " order by newsletters_groups_sort_order, newsletters_groups_name";
    $newsletters_groups_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $newsletters_groups_select_sql, $newsletters_groups_sql_numrows);
    $newsletters_groups_result_sql = tep_db_query($newsletters_groups_select_sql);
?>
      				<tr>
						<td align="left"><?='[ <a href="'.tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&action=new_group').'" >'.LINK_ADD_NEWSLETTERS_GROUP.'</a> ]'?></td>
					</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="1" cellpadding="2">
        						<tr>
	           						<td width="5%" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
			       					<td width="20%" class="reportBoxHeading"><?=TABLE_HEADING_GROUP_NAME?></td>
			       					<td width="10%" class="reportBoxHeading"><?=TABLE_HEADING_MODULE?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_NEWSLETTERS?></td>
			   					</tr>
<?
	$row_count = 0;
    while ($newsletters_groups_row = tep_db_fetch_array($newsletters_groups_result_sql)) {
    	$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
    	
    	$safe_grp_name = htmlspecialchars(addslashes($newsletters_groups_row["newsletters_groups_name"]), ENT_QUOTES);
?>
    							<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top">
										<a href="<?=tep_href_link(FILENAME_NEWSLETTERS, tep_get_all_get_params(array('nID', 'ngID', 'action', 'subaction')) . 'ngID='.$newsletters_groups_row['newsletters_groups_id'].'&action=new_group')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
										<a href="javascript:void(confirm_delete('<?=$safe_grp_name?>', 'Newsletter Group', '<?=tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&ngID=' . $newsletters_groups_row['newsletters_groups_id'] . '&action=confirm_delete_group')?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
									</td>
									<td class="reportRecords" valign="top"><?=$newsletters_groups_row['newsletters_groups_name']?></td>
									<td class="reportRecords" valign="top"><?=$newsletters_groups_row['module']?></td>
									<td class="reportRecords" valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
			          							<td colspan="5"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
			          						</tr>
			               					<tr>
			               						<td class="subRecordsBoxHeading" valign="top" width="10%">
													[<a href="<?=tep_href_link(FILENAME_NEWSLETTERS, tep_get_all_get_params(array('nID', 'ngID', 'action', 'subaction')) . 'action=new&ngID='.$newsletters_groups_row['newsletters_groups_id'])?>">Add New</a>]
												</td>
												<td class="subRecordsBoxHeading"><?=TABLE_HEADING_NEWSLETTERS?></td>
						       					<td width="25%" align="right" class="subRecordsBoxHeading"><?=TABLE_HEADING_SIZE?></td>
						       					<td width="10%" align="center" class="subRecordsBoxHeading"><?=TABLE_HEADING_SENT?></td>
						       					<td width="10%" align="center" class="subRecordsBoxHeading"><?=TABLE_HEADING_STATUS?></td>
						   					</tr>
						   					<tr>
			          							<td colspan="5"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px"></div></td>
			          						</tr>
<?
		$newsletters_select_sql = "	SELECT newsletters_id, title, length(content) AS content_length, status, locked 
									FROM " . TABLE_NEWSLETTERS . " 
									WHERE newsletters_groups_id = '" . (int)$newsletters_groups_row['newsletters_groups_id'] . "' 
									ORDER BY date_added DESC, title";
		$newsletters_result_sql = tep_db_query($newsletters_select_sql);
		
		while ($newsletters_row = tep_db_fetch_array($newsletters_result_sql)) {
			$safe_newsletter_title = htmlspecialchars(addslashes($newsletters_row['title']), ENT_QUOTES);
			$safe_newsletter_record_title = htmlspecialchars(addslashes(TABLE_HEADING_NEWSLETTERS), ENT_QUOTES);
			
			echo '							<tr>
												<td class="reportRecords" valign="top" nowrap>';
			
			if ($newsletters_row['locked'] > 0) {
				echo '<a href="'.tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $newsletters_row['newsletters_id'] . '&ngID='.$newsletters_groups_row['newsletters_groups_id'].'&action=new').'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>&nbsp;<a href="javascript:void(confirm_delete(\''.$safe_newsletter_title.'\', \''.$safe_newsletter_record_title.'\', \''.tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $newsletters_row['newsletters_id'] . '&action=deleteconfirm').'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", 'Delete', '', '', 'align="top"').'</a>&nbsp;';
				
          		echo '<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $newsletters_row['newsletters_id'] . '&action=preview') . '">' . tep_image(DIR_WS_ICONS."preview.gif", IMAGE_PREVIEW, '', '', 'align="top"') . '</a>&nbsp;<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $newsletters_row['newsletters_id'] . '&action=send') . '">' . tep_image(DIR_WS_ICONS."email.gif", IMAGE_SEND, '', '', 'align="top"') . '</a>&nbsp;<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $newsletters_row['newsletters_id'] . '&action=unlock') . '">' . tep_image(DIR_WS_ICONS."unlocked.gif", IMAGE_UNLOCK, '', '', 'align="top"') . '</a>';
          		
        	} else {
          		echo '<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $newsletters_row['newsletters_id'] . '&action=preview') . '">' . tep_image(DIR_WS_ICONS."preview.gif", IMAGE_PREVIEW, '', '', 'align="top"') . '</a>&nbsp;<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $newsletters_row['newsletters_id'] . '&action=lock') . '">' . tep_image(DIR_WS_ICONS."locked.gif", IMAGE_LOCK, '', '', 'align="top"') . '</a>';
        	}
        	
			echo '								</td>
												<td class="reportRecords" valign="top">'.$newsletters_row['title'].'</td>
												<td align="right" class="reportRecords" valign="top" nowrap>'.(number_format($newsletters_row['content_length']) . ' bytes').'</td>
												<td align="center" class="reportRecords" valign="top">'.($newsletters_row['status'] == '1' ? tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) : tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS)).'</td>
												<td align="center" class="reportRecords" valign="top">'.($newsletters_row['locked'] > 0 ? tep_image(DIR_WS_ICONS . 'locked.gif', ICON_LOCKED) : tep_image(DIR_WS_ICONS . 'unlocked.gif', ICON_UNLOCKED)).'</td>
											</tr>';
		}
?>
										</table>
									</td>
								</tr>
<?		$row_count++;
	}
?>
							</table>
		   				</td>
		   			</tr>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
				                    <td class="smallText" valign="top"><?php echo $newsletters_groups_split->display_count($newsletters_groups_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_NEWSLETTERS); ?></td>
				                    <td class="smallText" align="right"><?php echo $newsletters_groups_split->display_links($newsletters_groups_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'nID', 'ngID'))); ?></td>
								</tr>
							</table>
						</td>
					</tr>
<?/*
	$heading = array();
	$contents = array();
	
  	switch ($action) {
    	case 'delete':
      		$heading[] = array('text' => '<b>' . $nInfo->title . '</b>');
			
      		$contents = array('form' => tep_draw_form('newsletters', FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $nInfo->newsletters_id . '&action=deleteconfirm'));
      		$contents[] = array('text' => TEXT_INFO_DELETE_INTRO);
      		$contents[] = array('text' => '<br><b>' . $nInfo->title . '</b>');
      		$contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $HTTP_GET_VARS['nID']) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      		break;
    	default:
      		if (is_object($nInfo)) {
        		$heading[] = array('text' => '<b>' . $nInfo->title . '</b>');
				
        		if ($nInfo->locked > 0) {
          			$contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $nInfo->newsletters_id . '&action=new') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $nInfo->newsletters_id . '&action=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a> <a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $nInfo->newsletters_id . '&action=preview') . '">' . tep_image_button('button_preview.gif', IMAGE_PREVIEW) . '</a> <a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $nInfo->newsletters_id . '&action=send') . '">' . tep_image_button('button_send.gif', IMAGE_SEND) . '</a> <a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $nInfo->newsletters_id . '&action=unlock') . '">' . tep_image_button('button_unlock.gif', IMAGE_UNLOCK) . '</a>');
        		} else {
          			$contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $nInfo->newsletters_id . '&action=preview') . '">' . tep_image_button('button_preview.gif', IMAGE_PREVIEW) . '</a> <a href="' . tep_href_link(FILENAME_NEWSLETTERS, 'page=' . $HTTP_GET_VARS['page'] . '&nID=' . $nInfo->newsletters_id . '&action=lock') . '">' . tep_image_button('button_lock.gif', IMAGE_LOCK) . '</a>');
        		}
        		$contents[] = array('text' => '<br>' . TEXT_NEWSLETTER_DATE_ADDED . ' ' . tep_date_short($nInfo->date_added, PREFERRED_DATE_FORMAT));
        		if ($nInfo->status == '1') $contents[] = array('text' => TEXT_NEWSLETTER_DATE_SENT . ' ' . tep_date_short($nInfo->date_sent, PREFERRED_DATE_FORMAT));
      		}
      		break;
  	}
	
  	if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
    	echo '            			<td width="25%" valign="top">' . "\n";
    	$box = new box;
    	echo $box->infoBox($heading, $contents);
		
    	echo '            			</td>' . "\n";
	}*/
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>