<?
require('includes/application_top.php');

//$_POST['txtText'] = tep_clean_data($_POST['txtText'],true);
//$_POST['txtTitle'] = tep_clean_data($_POST['txtTitle'],true);

$title = HEADING_TITLE;

if ((int)$_GET['doEdit'] > 0) {
	$title = HEADING_TITLE_EDIT_COMMENT;
	
	if (strtolower($_SERVER['REQUEST_METHOD']) == "post") {
		if (!tep_not_null($_POST['txtText']) || !tep_not_null($_POST['txtTitle'])) {
			tep_redirect(tep_href_link(FILENAME_STATS_ORDERS_ADD_COMMENT,"error=1".tep_get_all_get_params(array("addComment","error"))));
			exit;
		}		
		$sql_data_array = array(	'orders_comments_title' => tep_db_prepare_input($_POST["txtTitle"]),
           							'orders_comments_text' => tep_db_prepare_input($_POST["txtText"]),
           							'orders_comments_sort_order' => (int)$_POST['txtSort'],
           							'orders_comments_status' => (int)$_POST['chkActive'] );
		tep_db_perform(TABLE_ORDERS_COMMENTS, $sql_data_array, 'update', ' orders_comments_id="'.(int)$_GET['doEdit'].'"');
	}
	
	$result = tep_db_query("SELECT * FROM " . TABLE_ORDERS_COMMENTS . " WHERE orders_comments_id=".(int)$_GET['doEdit'].";");		
	$row=tep_db_fetch_array($result);
} else if((int)$_GET['addComment'] == 1) {
	if (!tep_not_null($_POST['txtText']) || !tep_not_null($_POST['txtTitle'])) {
		tep_redirect(tep_href_link(FILENAME_STATS_ORDERS_ADD_COMMENT,"error=1".tep_get_all_get_params(array("addComment","error"))));
	} else {
		$sql_data_array = array('orders_comments_title' => tep_db_prepare_input($_POST["txtTitle"]),
           						'orders_comments_text' => tep_db_prepare_input($_POST["txtText"]),
           						'orders_comments_sort_order' => (int)$_POST['txtSort'],
           						'orders_comments_status' => (int)$_POST['chkActive'] );
		tep_db_perform(TABLE_ORDERS_COMMENTS, $sql_data_array);
		
		tep_redirect(tep_href_link(FILENAME_STATS_ORDERS_ADD_COMMENT,tep_get_all_get_params(array("addComment"))));
	}
	exit;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
			<table width="100%"  border="0" cellspacing="0" cellpadding="3">
<?
if((int)$_GET['doEdit'] > 0) {
	echo tep_draw_form("form1",FILENAME_STATS_ORDERS_ADD_COMMENT,"doEdit=".(int)$_GET['doEdit'].tep_get_all_get_params(array("addComment","doEdit")));
} else {
	echo tep_draw_form("form1",FILENAME_STATS_ORDERS_ADD_COMMENT,"addComment=1".tep_get_all_get_params(array("addComment","doEdit")));
}
?>
				<tr>
					<td colspan="2">
						<span class="pageHeading"><?=$title?></span><br><a href="<?=tep_href_link(FILENAME_STATS_ORDERS_COMMENT,tep_get_all_get_params(array("addComment","doEdit")))?>"><?=TEXT_VIEW_COMMENTS?></a>
					</td>
				</tr>
<?
if ((int)$_GET['error'] == 1) {	
?>
				<tr class="messageStackError">
					<td colspan="2"><?=TEXT_ERROR_MISSING_FIELDS?></td>
				</tr>
<?
}
?>
				<tr>
    				<td width="20%" valign="top" class="main"><?=TEXT_COMMENT_TITLE?></td>
  					<td class="dataTableContent" valign="top"><?=tep_draw_input_field("txtTitle", $row['orders_comments_title'])?>*</td>
  				</tr>
  				<tr>
  		  			<td valign="top" class="main"><?=TEXT_COMMENT_SORT_ORDER?></td>
  		  			<td class="dataTableContent">
<?
if (trim((string)$row['orders_comments_sort_order'])!="") {
	echo tep_draw_input_field("txtSort",$row['orders_comments_sort_order'],"size='6'");
} else {
	echo tep_draw_input_field("txtSort",NUMBER_DEFAULT_SORT_ORDER,"size='6'");
}
?>
					</td>
		  		</tr>
  				<tr>
  		  			<td valign="top" class="main"><?=TEXT_COMMENT_ACTIVE?></td>
  		  			<td class="main">
				<?
					if ((int)$row['orders_comments_status']==1 && (int)$_GET['doEdit']>0) {
						$active = true;
						$inactive = false;		
					} else if((int)$row['orders_comments_status']==0 && (int)$_GET['doEdit']>0) {
						$inactive=true;
						$active = false;
					} else {
						$active=true;	
					}
					echo tep_draw_radio_field("chkActive","1",$active);
					echo TEXT_COMMENT_RADIO_ACTIVE;
					echo tep_draw_radio_field("chkActive","0",$inactive);
					echo TEXT_COMMENT_RADIO_INACTIVE;
				?>
		 			</td>
		  		</tr>
  				<tr>
  		  			<td valign="top" class="main"><?=TEXT_COMMENT_TEXT?></td>
  		  			<td valign="top" class="dataTableContent"><?=tep_draw_textarea_field("txtText", "soft", 40, 10, $row['orders_comments_text'])?>*</td>
		  		</tr>
  				<tr>
  		  			<td valign="top" class="dataTableContent"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  		  			<td class="dataTableContent" align="right">
				<?	if((int)$_GET['doEdit'] > 0) {
		  				echo tep_image_submit("button_update.gif", IMAGE_UPDATE);
		  		  	} else {
		  				echo tep_image_submit("button_insert.gif", IMAGE_INSERT);
		  			}
            		echo "&nbsp;&nbsp;".tep_image_button("button_reset.gif",IMAGE_RESET," onClick='javascript:document.form1.reset();'");
				?>
					</td>
		  		</tr>
				</form>
			</table>
		</td>
	</tr>
</table>			
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>