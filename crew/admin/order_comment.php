<?
require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');
$cmdID = (isset($HTTP_GET_VARS['cmdID']) ? $HTTP_GET_VARS['cmdID'] : '');
$subaction = (isset($_REQUEST["subaction"]) ? $_REQUEST["subaction"] : '');
$cmd_active = (isset($_REQUEST["cmd_active"]) ? $_REQUEST["cmd_active"] : '');

$use_for_array = array(	FILENAME_ORDERS => TEXT_CUSTOMER_ORDER,
						FILENAME_PROGRESS_REPORT => TEXT_PWL_ORDER,
						FILENAME_BUYBACK_REQUESTS_INFO => TEXT_BUYBACK_ORDER
				 );

if (tep_not_null($subaction)) {
	$redirect = false;
	
	switch ($subaction) {
		case 'new_order_comment':
			$sql_data_array = array('orders_comments_title' => tep_db_prepare_input($HTTP_POST_VARS['cmd_title']),
									'orders_comments_text' => tep_db_prepare_input($HTTP_POST_VARS['cmd_content']),
									'orders_comments_sort_order' => tep_db_prepare_input($HTTP_POST_VARS['cmd_sort']),
									'orders_comments_status' => tep_db_prepare_input($HTTP_POST_VARS['cmd_active']),
									'orders_comments_filename' => tep_db_prepare_input($HTTP_POST_VARS['cmd_filename'])
									);
			
			tep_db_perform(TABLE_ORDERS_COMMENTS, $sql_data_array);
			
			$messageStack->add_session(SUCCESS_COMMENT_INSERTED, 'success');
			
			$redirect = true;
			
			break;
		case 'edit_order_comment':
			$sql_data_array = array('orders_comments_title' => tep_db_prepare_input($HTTP_POST_VARS['cmd_title']),
									'orders_comments_text' => tep_db_prepare_input($HTTP_POST_VARS['cmd_content']),
									'orders_comments_sort_order' => tep_db_prepare_input($HTTP_POST_VARS['cmd_sort']),
									'orders_comments_status' => tep_db_prepare_input($HTTP_POST_VARS['cmd_active']),
									'orders_comments_filename' => tep_db_prepare_input($HTTP_POST_VARS['cmd_filename'])
									);
			
			tep_db_perform(TABLE_ORDERS_COMMENTS, $sql_data_array, 'update', "orders_comments_id ='" . tep_db_input($HTTP_POST_VARS['cmdID']) . "'");
			
			$messageStack->add_session(SUCCESS_COMMENT_UPDATED, 'success');
			
			$redirect = true;
			
			break;
		case 'delete_order_comment':
			tep_db_query("DELETE FROM ".TABLE_ORDERS_COMMENTS." WHERE orders_comments_id = '" . (int)$cmdID . "'");
			
			$messageStack->add(SUCCESS_COMMENT_DELETED, 'success');
			
			break;
		case 'set_comment_status':
			$sql_data_array = array('orders_comments_status' => (int)$cmd_active);
			
			tep_db_perform(TABLE_ORDERS_COMMENTS, $sql_data_array, 'update', "orders_comments_id ='" . (int)$cmdID . "'");
			
			$messageStack->add_session(SUCCESS_COMMENT_STATUS_UPDATED, 'success');
			
			$redirect = true;
			
			break;
	}
	
	$comments_arr = array('co' => array(), 'bo' => array(), 'pwl' => array());
	
	$order_comment_select_sql = "	SELECT orders_comments_id, orders_comments_title, orders_comments_filename 
									FROM " . TABLE_ORDERS_COMMENTS . " 
									WHERE orders_comments_status = '1' 
										ORDER BY orders_comments_sort_order";
	$order_comment_result_sql = tep_db_query($order_comment_select_sql);
	
	while ($order_comment_row = tep_db_fetch_array($order_comment_result_sql)) {
		switch ($order_comment_row['orders_comments_filename']) {
			case 'orders.php':
				$comments_arr['co'][] = '{ name: "'.$order_comment_row['orders_comments_id'].'", to: "'.htmlentities($order_comment_row['orders_comments_title'], ENT_QUOTES, "UTF-8").'"}';
			break;
			
			case 'buyback_requests_info.php':
				$comments_arr['bo'][] = '{ name: "'.$order_comment_row['orders_comments_id'].'", to: "'.htmlentities($order_comment_row['orders_comments_title'], ENT_QUOTES, "UTF-8").'"}';
			break;
			
			case 'progress_report.php':
				$comments_arr['pwl'][] = '{ name: "'.$order_comment_row['orders_comments_id'].'", to: "'.htmlentities($order_comment_row['orders_comments_title'], ENT_QUOTES, "UTF-8").'"}';
			break;
		}
	}

	
	if (isset($comments_arr['co'])) {
		$generate_comment_data = "var order_comments = [".implode("," , $comments_arr['co'])."];";
	}

	if (isset($comments_arr['bo'])) {
		$generate_comment_data .= "var buyback_comments = [".implode("," , $comments_arr['bo'])."];";
	}
	
	if (isset($comments_arr['pwl'])) {
		$generate_comment_data .= "var pwl_comments = [".implode("," , $comments_arr['pwl'])."];";
	}
	
	unset($comments_arr);
	
	$filename = 'load_comment_data.js';
	$file_location = DIR_FS_ADMIN.'repository/'.$filename;
	
	if (file_exists($file_location)) {
		if (file_exists($file_location.'.bak')) {
			@unlink($file_location.'.bak');
		}
		$oldPermission = @umask(0);
		@chmod($file_location, 0755);
		@umask($oldPermission);
		@rename($file_location, $file_location.'.bak');
	}

	if (!$handle = @fopen($file_location, 'w')) {
	     exit;
	}
	
	// Write to our opened file.
	if (fwrite($handle, $generate_comment_data) === FALSE) {
		fclose($handle);
	    exit;
	}
	fclose($handle);
	
	if ($redirect) {
		tep_redirect(tep_href_link(FILENAME_STATS_ORDERS_COMMENT, tep_get_all_get_params(array('subaction','cmdID','cmd_active'))));
	}
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<td valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="0">
					<tr>
						<td class="main"><span class="pageHeading"><?=HEADING_TITLE?></span></td>
					</tr>
					<tr>
						<td class="main"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
switch ($action) {
	case 'add_comment':
	case 'edit_comment':
		
		$filename_array = array();
		$filename_array[] = array('id' => FILENAME_ORDERS, 'text' => TEXT_CUSTOMER_ORDER);
		$filename_array[] = array('id' => FILENAME_PROGRESS_REPORT, 'text' => TEXT_PWL_ORDER);
		$filename_array[] = array('id' => FILENAME_BUYBACK_REQUESTS_INFO, 'text' => TEXT_BUYBACK_ORDER);
		
		
		$orders_comments_info_select_sql = "SELECT * FROM " . TABLE_ORDERS_COMMENTS . " WHERE orders_comments_id ='" . tep_db_input($cmdID) . "'";
		$orders_comments_info_result_sql = tep_db_query($orders_comments_info_select_sql);
		$orders_comments_info_row = tep_db_fetch_array($orders_comments_info_result_sql);
?>
					<tr>
						<td>
<?
	echo tep_draw_form('order_comment_form', FILENAME_STATS_ORDERS_COMMENT);
?>
							<table width="100%"  border="0" cellspacing="0" cellpadding="3">
<?		if ($action == 'edit_comment') { ?>
								<tr>
									<td valign="top" class="main"><?=ENTRY_COMMENT_COMMENT_ID?></td>
									<td class="main"><?=$orders_comments_info_row['orders_comments_id']?></td>
								</tr>
								<tr>
									<td colspan="2" valign="top"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								</tr>
<?		} ?>
								<tr>
				    				<td width="29%" valign="top" class="main"><?=ENTRY_COMMENT_TITLE?></td>
				  					<td class="dataTableContent" valign="top"><?=tep_draw_input_field('cmd_title', $orders_comments_info_row['orders_comments_title'], ' id="txtTitle"')?><span class="redIndicator">*</span></td>
				  				</tr>
				  				<tr>
									<td colspan="2" valign="top"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								</tr>
								<tr>
									<td valign="top" class="main"><?=ENTRY_COMMENT_FILENAME?></td>
									<td class="dataTableContent"><?=tep_draw_pull_down_menu('cmd_filename', $filename_array, $orders_comments_info_row['orders_comments_filename'])?></td>
								</tr>
								<tr>
									<td colspan="2" valign="top"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								</tr>
  		  						<tr>
  		  							<td valign="top" class="main"><?=ENTRY_COMMENT_ACTIVE?></td>
  		  							<td class="dataTableContent">
<?		
										if (tep_not_null($orders_comments_info_row['orders_comments_status'])) {
											if ((int)$orders_comments_info_row['orders_comments_status'] == 1) {
												$active = true;
												$inactive = false;
											} else {
												$active = false;
												$inactive = true;
											}
										} else {
											$active = true;
											$inactive = false;
										}
										
										echo tep_draw_radio_field('cmd_active', '1', $active) . ENTRY_COMMENT_RADIO_ACTIVE . '&nbsp;' . tep_draw_radio_field('cmd_active', '0', $inactive) . ENTRY_COMMENT_RADIO_INACTIVE;
?>
									</td>
  		  						</tr>
  		  						<tr>
									<td colspan="2" valign="top"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								</tr>
  		  						<tr>
				  		  			<td valign="top" class="main"><?=ENTRY_COMMENT_TEXT?></td>
				  		  			<td valign="top" class="dataTableContent"><?=tep_draw_textarea_field('cmd_content', 'soft', 40, 10, $orders_comments_info_row['orders_comments_text'], ' id="txtText"')?><span class="redIndicator">*</span></td>
						  		</tr>
						  		<tr>
									<td colspan="2" valign="top"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								</tr>
								<tr>
  		  							<td valign="top" class="main"><?=ENTRY_COMMENT_SORT_ORDER?></td>
  		  							<td class="dataTableContent"><?=tep_draw_input_field("cmd_sort",(tep_not_null($orders_comments_info_row['orders_comments_sort_order']) ? $orders_comments_info_row['orders_comments_sort_order'] : NUMBER_DEFAULT_SORT_ORDER),'size="6"')?></td>
  		  						</tr>
  		  						<tr>
									<td colspan="2" valign="top"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								</tr>
								<tr>
									<td>&nbsp;</td>
									<td>
<?
										if ($action == 'add_comment') {
											echo tep_draw_hidden_field('subaction', 'new_order_comment');
											echo tep_image_button('button_insert.gif', IMAGE_INSERT, ' onClick="insert_edit_comment_form_checking()" style="cursor: pointer;"') . '&nbsp;';
										} else if ($action  == 'edit_comment') {
											echo tep_draw_hidden_field('subaction', 'edit_order_comment') . tep_draw_hidden_field('cmdID', $cmdID);
											echo tep_image_button('button_update.gif', IMAGE_INSERT, ' onClick="insert_edit_comment_form_checking()" style="cursor: pointer;"') . '&nbsp;';
										}
										echo '<a href="' . tep_href_link(FILENAME_STATS_ORDERS_COMMENT) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>';
?>
									</td>
								</tr>
								<tr>
									<td colspan="2" valign="top"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
								</tr>
							</table>
						</td>
					</tr>
<?
		break;
	default:
?>
					<tr>
						<td><a href="<?=tep_href_link(FILENAME_STATS_ORDERS_COMMENT, 'action=add_comment')?>"><?=LINK_NEW_COMMENT?></a></td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
		break;
}
?>
					<tr>
						<td>
							<table width="100%" border="0" cellspacing="1" cellpadding="2">
			  					<tr class="ordersBoxHeading">
			  						<td width="5%" align="center"><?=TABLE_HEADING_COMMENT_ACTION?></td>
							    	<td width="10%"><?=TABLE_HEADING_COMMENT_ID?></td>
							  		<td nowrap><?=TABLE_HEADING_COMMENT_TITLE?></td>
							  		<td width="12%"><?=TABLE_HEADING_COMMENT_SORT_ORDER?></td>
							  		<td width="7%" align="center"><?=TABLE_HEADING_COMMENT_STATUS?></td>
							  		<td width="17%"><?=TABLE_HEADING_COMMENT_USE_FOR?></td>
							  	</tr>
<?
		$orders_comments_info_select_sql = "SELECT * FROM orders_comments ORDER BY orders_comments_filename, orders_comments_sort_order";
		
		if ($show_records != "ALL") {
			$orders_comments_info_object = new splitPageResults($HTTP_GET_VARS['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $orders_comments_info_select_sql, $orders_comments_info_select_sql_numrows, true);
		}
		
		$result = tep_db_query($orders_comments_info_select_sql);
		while($row = tep_db_fetch_array($result)) {
			$order_comment_style = ($order_comment_row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
?>
						  		<tr class="<?=$order_comment_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$order_comment_style?>')" onclick="rowClicked(this, '<?=$order_comment_style?>')">
						  			<td class="ordersRecords" align="center">
										<a href="<?=tep_href_link(FILENAME_STATS_ORDERS_COMMENT, tep_get_all_get_params(array("cmdID","action", "subaction", "cmd_active")) . 'action=edit_comment&cmdID='.$row['orders_comments_id'])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
<?
										echo '<a href="javascript:void(confirm_delete(\''.htmlspecialchars(addslashes($row['orders_comments_title']), ENT_QUOTES).'\', \'Comment\', \'' . tep_href_link(FILENAME_STATS_ORDERS_COMMENT, tep_get_all_get_params(array("cmdID","action", "subaction", "cmd_active")) . 'subaction=delete_order_comment&cmdID=' . $row['orders_comments_id']) . '\'))">' . tep_image(DIR_WS_ICONS."delete.gif", "Delete Comment", "", "", 'align="top"') . '</a>';
?>
									</td>
									<td class="ordersRecords"><?=$row['orders_comments_id']?></td>
									<td class="ordersRecords"><?=strip_tags($row['orders_comments_title'])?></td>
									<td class="ordersRecords"><?=$row['orders_comments_sort_order']?></td>
									<td class="ordersRecords" align="center"><?=tep_switch_image((int)$row['orders_comments_status'], tep_href_link(FILENAME_STATS_ORDERS_COMMENT, tep_get_all_get_params(array('subaction','cmdID','cmd_active')).'subaction=set_comment_status&cmdID='.$row['orders_comments_id'].'&cmd_active=1'), tep_href_link(FILENAME_STATS_ORDERS_COMMENT, tep_get_all_get_params(array('subaction','cmdID','cmd_active')).'subaction=set_comment_status&cmdID='.$row['orders_comments_id'].'&cmd_active=0'))?></td>
									<td class="ordersRecords"><?=$use_for_array[$row['orders_comments_filename']]?></td>
								</tr>
<?
			$order_comment_row_count++;
		}
?>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_COMMENTS, tep_db_num_rows($orders_comments_info_select_sql) > 0 ? "1" : "0", tep_db_num_rows($orders_comments_info_select_sql), tep_db_num_rows($orders_comments_info_select_sql)) : $orders_comments_info_object->display_count($orders_comments_info_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_COMMENTS)?></td>
									<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $orders_comments_info_object->display_links($orders_comments_info_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
	</table>		
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<script language="javascript"><!--
	function trim(str) {
		str = str.replace(/^\s*|\s*$/g,"");
		
		return str;
	}
	
	function insert_edit_comment_form_checking () {
		var comment_title = trim(document.getElementById('txtTitle').value);
		var comment_value = trim(document.getElementById('txtText').value);
		
		if (comment_title == '' || comment_value == '') {
			alert('Please fill in all the required fields');
		} else {
			document.order_comment_form.submit();
		}
	}
//-->
</script>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>
