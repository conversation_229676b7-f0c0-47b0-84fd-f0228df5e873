<?
/*
  	$Id: order_cp_info.php,v 1.4 2011/01/21 02:17:53 weesiong Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_FUNCTIONS . 'custom_product.php');

$currencies = new currencies();

$products_id = tep_db_prepare_input($_REQUEST['products_id']);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

$custom_cart = isset($_SESSION[FILENAME_ORDERS_CP_INFO][$products_id]) ? $_SESSION[FILENAME_ORDERS_CP_INFO][$products_id] : array();

switch($action) {
	case "update":
		$level_dependent_package = true;
		
		$range_resource_array = tep_get_bracket_range((int)$HTTP_POST_VARS['datapool_parent_id']);
		
		if (count($range_resource_array["range"]) < 1) {
			$current_level = $desired_level = 0;
			$level_dependent_package = false;
		} else {
			$current_level = (int)$HTTP_POST_VARS["cmbCurrentLevel"];
			$desired_level = (int)$HTTP_POST_VARS["cmbDesiredLevel"];
		    
		    if ($current_level < 1)	$error = true;
		    
		    if ($desired_level < 1)	$error = true;
		    
		    if ($current_level >= $desired_level)	$error = true;
		}
		
	    if (!$error) {
		    $option_html_array = tep_get_options_html($products_id, $desired_level);
		    $system_option_array = array();
		    
		    if (count($option_html_array)) {
			    foreach ($option_html_array as $option_key => $option_res) {
			    	if ($option_res["data_pool_options_required"] == '1' && $option_res["data_pool_options_input_type"] != 999) {
			    		if (!isset($HTTP_POST_VARS["cp_option"][$option_key]) || trim($HTTP_POST_VARS["cp_option"][$option_key]) == '') {
			    			$error = true;
			    			break;
			    		}
			    	}
			    	
			    	if ($option_res["data_pool_options_input_type"] == 999) {
			    		$system_option_array[$option_res["data_pool_options_input_type"]] = $option_res["data_pool_options_show_supplier"];
			    	}
			    }
			}
		}
		
		if (!$error) {
			$level_label = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_LEVEL_LABEL);
			$class_label = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_CLASS_LABEL);
			
			tep_calculate_bracket_price((int)$HTTP_POST_VARS['datapool_parent_id'], $current_level, $desired_level, $price, $eta, $msg, $custom_tags);
			
			$base_price_value = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_BASE_PRICE);
			$base_time_value = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_BASE_TIME);
			$min_time_value = tep_get_bracket_value((int)$HTTP_POST_VARS['datapool_parent_id'], KEY_PL_MIN_TIME);
			
			$total_price_info = tep_calculate_grand_total_price($base_price_value, $base_time_value, $price, $eta, $min_time_value, $option_html_array, $HTTP_POST_VARS["cp_option"]);
			
			$final_array['info'] = array('current_level' => $current_level,
										 'desired_level' => $desired_level,
										 'current_class' => (int)$HTTP_POST_VARS['cmbCurrentClass'],
										 'desired_class' => (int)$HTTP_POST_VARS['cmbDesiredClass']
										); 
			
			$final_array['db_info'] = array();
			if ($level_dependent_package) {
				$final_array['db_info']['current_level'] = array('label' => sprintf(TEXT_CURRENT_LABEL, $level_label), 'value' => tep_get_level_alias((int)$HTTP_POST_VARS['datapool_parent_id'], $current_level) . "##1##"); // Will get setting from admin page, in future
				$final_array['db_info']['desired_level'] = array('label' => sprintf(TEXT_DESIRED_LABEL, $level_label), 'value' => tep_get_level_alias((int)$HTTP_POST_VARS['datapool_parent_id'], $desired_level) . "##1##"); // Will get setting from admin page, in future
			}
			
			if ((int)$HTTP_POST_VARS['cmbCurrentClass'] > 0) {
				$final_array['db_info']['current_class'] = array('label' => sprintf(TEXT_CURRENT_LABEL, $class_label), 'value' => tep_get_level_name_path((int)$HTTP_POST_VARS['cmbCurrentClass'], ' > '));
			}
			
			if ((int)$HTTP_POST_VARS['cmbDesiredClass'] > 0) {
				$final_array['db_info']['desired_class'] = array('label' => sprintf(TEXT_DESIRED_LABEL, $class_label), 'value' => tep_get_level_name_path((int)$HTTP_POST_VARS['cmbDesiredClass'], ' > '));
			}
			
			if (count($custom_tags) > 0) {
				foreach ($custom_tags as $custom_bracket_tags_res) {
					$final_array['db_info'][$custom_bracket_tags_res['key']] = array('label' => $custom_bracket_tags_res["display_label"], 'value' => $custom_bracket_tags_res["value"]);
				}
			}
			
			if (count($range_resource_array) > 0) {
				$final_array['db_bracket_info']['cp_bracket'] = array('label' => 'cp_bracket', 'value' => serialize($range_resource_array));
			}
			
			// Storing label for all custom types.
			$final_array['option'] = array();
			if (count($HTTP_POST_VARS["cp_option"])) {
				foreach ($HTTP_POST_VARS["cp_option"] as $key => $val) {
					$final_array['option'][$key] = tep_db_prepare_input($val);
					$sel_option_info_array = tep_grab_option_title_and_value($option_html_array, $key, $val);
					
					if ($sel_option_info_array != false && is_array($sel_option_info_array)) {
						$final_array['db_info']['opt_'.$key] = array('label' => $sel_option_info_array['label'], 'value' => $sel_option_info_array['value'], 'show_supplier' => $option_html_array[$key]["data_pool_options_show_supplier"], 'class' => $option_html_array[$key]["data_pool_options_class"]);
					}
				}
			}
			
			$final_array['calculated'] = array(	'price' => $total_price_info["price"],
												'price_text' => $total_price_info["price_text"],
											   	'eta' => $total_price_info["eta"],
											   	'eta_text' => $total_price_info["eta_text"],
											   	'show_supplier' => $system_option_array[999],
											   	'msg' => $msg
											  );
			
			unset($_SESSION[FILENAME_ORDERS_CP_INFO][$products_id]);
			$_SESSION[FILENAME_ORDERS_CP_INFO][$products_id] = $final_array;
		}
		
		tep_redirect(tep_href_link(FILENAME_ORDERS_CP_INFO, tep_get_all_get_params(array('action')) . 'action=success'));
		
		break;
	case "success":
?>
		<script language="javascript">
			alert('<?=JS_PWL_CUSTOMISING_SUCCESS?>');
			//opener.compensate_form.compensate_unit_price.value = '<?=$_SESSION[FILENAME_ORDERS_CP_INFO][$products_id]['calculated']['price']?>';
			opener.update_returned_unit_price('<?=$_SESSION[FILENAME_ORDERS_CP_INFO][$products_id]['calculated']['price']?>', '<?=DEFAULT_CURRENCY?>');
			self.close();
		</script>
<?
		break;
	default:
		$header_title = HEADING_TITLE;
		
		$datapool_parent_id = '';
		$parent_id = $products_id;
		
		$data_pool_level_root_select_sql = "SELECT data_pool_level_id 
											FROM " . TABLE_DATA_POOL_LEVEL . " 
											WHERE data_pool_level_parent_id=0 
												AND products_id ='" . $products_id . "'";
		$data_pool_level_root_result_sql = tep_db_query($data_pool_level_root_select_sql);
		if ($data_pool_level_root_row = tep_db_fetch_array($data_pool_level_root_result_sql)) {
			$datapool_parent_id = (int)$data_pool_level_root_row['data_pool_level_id'];
		}
		
		$html_framework = '';
		$range_resource_array = tep_get_bracket_range($datapool_parent_id);
		$start_level_range = tep_get_start_level($range_resource_array["start"], $range_resource_array["range"], $range_resource_array["mode"]);
		$level_label = tep_get_bracket_value($datapool_parent_id, KEY_PL_LEVEL_LABEL);
		$class_label = tep_get_bracket_value($datapool_parent_id, KEY_PL_CLASS_LABEL);
		$base_price_value = tep_get_bracket_value($datapool_parent_id, KEY_PL_BASE_PRICE);
		$base_time_value = tep_get_bracket_value($datapool_parent_id, KEY_PL_BASE_TIME);
		$min_time_value = tep_get_bracket_value($datapool_parent_id, KEY_PL_MIN_TIME);
		
		break;
}
$custom_type_select_sql = " SELECT pd.products_image, pd.products_name, pd.products_description, p.custom_products_type_id 
							FROM " . TABLE_PRODUCTS . " AS p 
							INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
								ON (p.products_id=pd.products_id AND pd.language_id='".(int)$languages_id."') 
							WHERE p.products_id = '". tep_db_input($products_id) ."'";
$custom_type_result_sql = tep_db_query($custom_type_select_sql);
$custom_type_row = tep_db_fetch_array($custom_type_result_sql);

if (is_array($custom_cart) && count($custom_cart)) {
	if (count($range_resource_array["range"]) <= 1) {
		if ($custom_cart["info"]["current_level"] != $start_level_range[1]['id']) {
			$custom_cart["info"]["current_level"] = $start_level_range[1]['id'];
		}
		if ($custom_cart["info"]["desired_level"] != $range_resource_array["range"][0]['level']) {
			$custom_cart["info"]["desired_level"] = $range_resource_array["range"][0]['level'];
		}
		
		$current_level_html = $start_level_range[1]['text'] . tep_draw_hidden_field('cmbCurrentLevel', $start_level_range[1]['id']);
		$desired_level_html = (trim($range_resource_array["range"][0]['alias']) != '' ? $range_resource_array["range"][0]['alias'] : $range_resource_array["range"][0]['level']) . tep_draw_hidden_field('cmbDesiredLevel', $range_resource_array["range"][0]['level']);
	} else {
		$desired_level_array = array( array('id' => '', 'text' => 'Please Select') );
		$desired_level_array = array_merge($desired_level_array, tep_get_desired_level_array($datapool_parent_id, $custom_cart["info"]["current_level"]));
		
		$current_level_html = tep_draw_pull_down_menu("cmbCurrentLevel", $start_level_range, $custom_cart["info"]["current_level"], ' id="cmbCurrentLevel" onChange="invoke_start_level_tasks(\''.$datapool_parent_id.'\', \'cmbCurrentLevel\', \'cmbDesiredLevel\', \'current_class_div\', \'desired_class_div\', \'options_div\')" ');
		
		if ($custom_cart["info"]["current_level"] < 1)	$custom_cart["info"]["desired_level"] = 0;
		
		$desired_level_html = tep_draw_pull_down_menu("cmbDesiredLevel", $desired_level_array, $custom_cart["info"]["desired_level"], ' id="cmbDesiredLevel" onChange="invoke_desired_level_tasks(\''.$datapool_parent_id.'\', \'cmbCurrentLevel\', \'cmbDesiredLevel\', \'desired_class_div\', \'options_div\')" ');
	}
	
	$option_html_array = tep_get_options_html($products_id, $custom_cart["info"]["desired_level"]);
	
	$starting_path = tep_get_level_name_path($datapool_parent_id, ' > ');
	$level_tree_array = array();
	tep_get_catalog_datapool_subtree_array($datapool_parent_id, $level_tree_array, 0, $starting_path);
	
	//	Get current class selection if any
	tep_display_selection_option_html($level_tree_array, $custom_cart["info"]["current_level"], $current_class_selection);
	
	//	Get desired class selection if any
	$sel_current_class = $custom_cart["info"]["current_class"] > 0 ? $custom_cart["info"]["current_class"] : $datapool_parent_id;
	$desired_class_starting_path = tep_get_level_name_path($sel_current_class, ' > ');
	$desired_class_level_tree_array = array();
	tep_get_catalog_datapool_subtree_array($sel_current_class, $desired_class_level_tree_array, 0, $desired_class_starting_path);
	
	if ($sel_current_class != $datapool_parent_id) {
		$data_pool_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id = '" . (int)$sel_current_class . "'";
		$data_pool_level_result_sql = tep_db_query($data_pool_level_select_sql);
		if ($data_pool_level_row = tep_db_fetch_array($data_pool_level_result_sql)) {
			$desired_class_level_tree_array = array( array(	'id' => (int)$data_pool_level_row['data_pool_level_id'], 
													   		'parent_id' => (int)$data_pool_level_row['data_pool_level_parent_id'],
															'ref_id' => (int)$data_pool_level_row['data_pool_ref_id'],
															'name' => $data_pool_level_row['data_pool_level_name'], 
															'ident' => 0, 
															'path' => $desired_class_starting_path,
															'data_pool_max_level' => $data_pool_level_row['data_pool_max_level'],
															'data_pool_min_level' => $data_pool_level_row['data_pool_min_level'],
															'data_pool_level_value' => $data_pool_level_row['data_pool_level_value'],
															'data_pool_input_field' => $data_pool_level_row['data_pool_input_field'],
															'data_pool_level_class' => $data_pool_level_row['data_pool_level_class'],
															'data_pool_level_class_name' => $data_pool_level_row['data_pool_level_class_name'],
															'child' => $desired_class_level_tree_array)
												);
		}
	}
	
	tep_display_selection_option_html($desired_class_level_tree_array, $custom_cart["info"]["desired_level"], $desired_class_selection);
	
	tep_calculate_bracket_price($datapool_parent_id, $custom_cart["info"]["current_level"], $custom_cart["info"]["desired_level"], &$latest_price, &$latest_eta, &$latest_msg, &$custom_tags);
	
	$total_price_info = tep_calculate_grand_total_price($base_price_value, $base_time_value, $latest_price, $latest_eta, $min_time_value, $option_html_array, $custom_cart["option"]);
	
	if (count($range_resource_array["range"]) > 0) {
		$html_framework .= '	<div style="padding-top:1em;">
								<table width="100%" cellspacing="0" cellpadding="2">
									<tr>
			 							<td width="40%" class="inputLabel">'.sprintf(TEXT_CURRENT_LABEL, $level_label).'</td>
										<td class="inputField">'.$current_level_html.'&nbsp;<span id="current_field_loading"></span></td>
			 						</tr>
								</table>
							</div>';
	}
	$html_framework .= '	<div id="current_class_div" style="padding-top:0.1em;'.(count($current_class_selection) ? '' : ' display: none;').'">
							<table width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td width="40%" class="inputLabel">'.sprintf(TEXT_CURRENT_LABEL, $class_label).'</td>
									<td class="inputField">'.tep_draw_pull_down_menu("cmbCurrentClass", $current_class_selection, $custom_cart["info"]["current_class"], 'id="cmbCurrentClass" onChange="invoke_class_tasks(\''.$datapool_parent_id.'\', \'cmbDesiredLevel\', \'cmbCurrentClass\', \'cmbDesiredClass\', \'desired_class_div\')" ').'<span id="current_class_loading"></span><noscript>&nbsp;'.tep_image_submit(THEMA.'button_update.gif', IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"').'</noscript></td>
								</tr>
							</table>
						</div>';
	if (count($range_resource_array["range"]) > 0) {
		$html_framework .= '	<div style="padding-top:0.1em;">
								<table width="100%" cellspacing="0" cellpadding="2">
									<tr>
			 							<td width="40%" class="inputLabel">'.sprintf(TEXT_DESIRED_LABEL, $level_label).'</td>
										<td valign="top" class="inputField">'.$desired_level_html.'
											<span id="end_level_suggestion_section" class="messageStackWarning">'.$latest_msg.'</span>
											&nbsp;<span id="desired_field_loading"></span>
										</td>
			 						</tr>
								</table>
							</div>';
	}
	$html_framework .= '<div id="desired_class_div" style="padding-top:0.1em;'.(count($desired_class_selection) ? '' : ' display: none;').'">
							<table width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td width="40%" class="inputLabel">'.sprintf(TEXT_DESIRED_LABEL, $class_label).'</td>
									<td class="inputField">'.tep_draw_pull_down_menu("cmbDesiredClass", $desired_class_selection, $custom_cart["info"]["desired_class"], ' id="cmbDesiredClass"').'</td>
								</tr>
							</table>
						</div>';
} else {	// New customising
	// Start grab all template setting
	$latest_price = $latest_eta = 0;
	
	if ($datapool_parent_id > 0) {	// must at least has one bracket to work
		if (count($range_resource_array["range"]) <= 1) {	// Only one or no bracket implies no need to waste time for level selection
			$desired_level_value = count($range_resource_array["range"]) < 1 ? 0 : $range_resource_array["range"][0]['level'];
			
			$current_level_html = $start_level_range[1]['text'] . tep_draw_hidden_field('cmbCurrentLevel', $start_level_range[1]['id'], 'id="cmbCurrentLevel"');
			$desired_level_html = (trim($range_resource_array["range"][0]['alias']) != '' ? $range_resource_array["range"][0]['alias'] : $desired_level_value) . tep_draw_hidden_field('cmbDesiredLevel', $desired_level_value, 'id="cmbDesiredLevel"');
			
			$option_html_array = tep_get_options_html($products_id, $desired_level_value);
			$starting_path = tep_get_level_name_path($datapool_parent_id, ' > ');
			$level_tree_array = array();
			tep_get_catalog_datapool_subtree_array($datapool_parent_id, $level_tree_array, 0, $starting_path);
			//	Get current class selection if any
			tep_display_selection_option_html($level_tree_array, $start_level_range[1]['id'], $current_class_selection);
			
			//	Get desired class selection if any
			$sel_current_class = (int)$HTTP_POST_VARS["cmbCurrentClass"] > 0 ? (int)$HTTP_POST_VARS["cmbCurrentClass"] : ($current_class_selection[0]['id'] > 0 ? $current_class_selection[0]['id'] : $datapool_parent_id);
			//$selected_current_class = isset($HTTP_POST_VARS["cmbCurrentClass"]) ? (int)$HTTP_POST_VARS["cmbCurrentClass"] : $current_class_selection[0]['id'];
			$desired_class_starting_path = tep_get_level_name_path($sel_current_class, ' > ');
			$desired_class_level_tree_array = array();
			tep_get_catalog_datapool_subtree_array($sel_current_class, $desired_class_level_tree_array, 0, $desired_class_starting_path);
			
			if ($sel_current_class != $datapool_parent_id) {
				$data_pool_level_select_sql = "SELECT * FROM " . TABLE_DATA_POOL_LEVEL . " WHERE data_pool_level_id = '" . $sel_current_class . "'";
				$data_pool_level_result_sql = tep_db_query($data_pool_level_select_sql);
				if ($data_pool_level_row = tep_db_fetch_array($data_pool_level_result_sql)) {
					$desired_class_level_tree_array = array( array(	'id' => (int)$data_pool_level_row['data_pool_level_id'], 
															   		'parent_id' => (int)$data_pool_level_row['data_pool_level_parent_id'],
																	'ref_id' => (int)$data_pool_level_row['data_pool_ref_id'],
																	'name' => $data_pool_level_row['data_pool_level_name'], 
																	'ident' => 0, 
																	'path' => $desired_class_starting_path,
																	'data_pool_max_level' => $data_pool_level_row['data_pool_max_level'],
																	'data_pool_min_level' => $data_pool_level_row['data_pool_min_level'],
																	'data_pool_level_value' => $data_pool_level_row['data_pool_level_value'],
																	'data_pool_input_field' => $data_pool_level_row['data_pool_input_field'],
																	'data_pool_level_class' => $data_pool_level_row['data_pool_level_class'],
																	'data_pool_level_class_name' => $data_pool_level_row['data_pool_level_class_name'],
																	'child' => $desired_class_level_tree_array)
														);
				}
			}
			
			tep_display_selection_option_html($desired_class_level_tree_array, $desired_level_value, $desired_class_selection);
			
			tep_calculate_bracket_price($datapool_parent_id, $start_level_range[1]['id'], $desired_level_value, &$latest_price, &$latest_eta, &$latest_msg, &$custom_tags);
		} else {
			$current_level_html = 	tep_draw_pull_down_menu("cmbCurrentLevel", $start_level_range, $HTTP_POST_VARS["cmbCurrentLevel"], ' id="cmbCurrentLevel" onChange="invoke_start_level_tasks(\''.$datapool_parent_id.'\', \'cmbCurrentLevel\', \'cmbDesiredLevel\', \'current_class_div\', \'desired_class_div\', \'options_div\')" ');
			$current_class_selection = array();
			$desired_level_array = array(array('id' => '', 'text' => '---'));
			
			$desired_level_html = tep_draw_pull_down_menu("cmbDesiredLevel", $desired_level_array, $HTTP_POST_VARS["cmbDesiredLevel"], ' id="cmbDesiredLevel" onChange="invoke_desired_level_tasks(\''.$datapool_parent_id.'\', \'cmbCurrentLevel\', \'cmbDesiredLevel\', \'desired_class_div\', \'options_div\')" ');
			
			$option_html_array = tep_get_options_html($products_id, tep_not_null($HTTP_POST_VARS["cmbDesiredLevel"]) ? $HTTP_POST_VARS["cmbDesiredLevel"] : 0);
		}
		
		$total_price_info = tep_calculate_grand_total_price($base_price_value, $base_time_value, $latest_price, $latest_eta, $min_time_value, $option_html_array, $HTTP_POST_VARS["cp_option"]);
		
		$current_class_html = '
				<div id="current_class_div" style="padding-top:0.1em;'.(count($current_class_selection) ? '' : ' display: none;').'">
					<table width="100%" cellspacing="0" cellpadding="2">
						<tr>
							<td width="40%" class="inputLabel">'.sprintf(TEXT_CURRENT_LABEL, $class_label).'</td>
							<td class="inputField">'.tep_draw_pull_down_menu("cmbCurrentClass", $current_class_selection, '', ' id="cmbCurrentClass" onChange="invoke_class_tasks(\''.$datapool_parent_id.'\', \'cmbDesiredLevel\', \'cmbCurrentClass\', \'cmbDesiredClass\', \'desired_class_div\')"').'<span id="current_class_loading"></span><noscript>&nbsp;'.tep_image_submit(THEMA.'button_update.gif', IMAGE_BUTTON_UPDATE, 'name="NonJSUpdate"').'</noscript></td>
						</tr>
					</table>
				</div>';
		$desired_class_html = '
				<div id="desired_class_div" style="padding-top:0.1em;'.(count($desired_class_selection) ? '' : ' display: none;').'">
					<table width="100%" cellspacing="0" cellpadding="2">
						<tr>
							<td width="40%" class="inputLabel">'.sprintf(TEXT_DESIRED_LABEL, $class_label).'</td>
							<td class="inputField">'.tep_draw_pull_down_menu("cmbDesiredClass", $desired_class_selection, '', ' id="cmbDesiredClass"').'</td>
						</tr>
					</table>
				</div>';
		
		if (count($range_resource_array["range"]) > 0) {
			$html_framework .= '	<div style="padding-top:1em;">
									<table width="100%" cellspacing="0" cellpadding="2">
										<tr>
				 							<td width="40%" class="inputLabel">'.sprintf(TEXT_CURRENT_LABEL, $level_label).'</td>
											<td class="inputField">'.$current_level_html.'&nbsp;<span id="current_field_loading"></span></td>
				 						</tr>
									</table>
								</div>';
		}
		
		$html_framework .= $current_class_html;
		
		if (count($range_resource_array["range"]) > 0) {
			$html_framework .= '<div style="padding-top:0.1em;">
									<table width="100%" cellspacing="0" cellpadding="2">
										<tr>
				 							<td width="40%" class="inputLabel">'.sprintf(TEXT_DESIRED_LABEL, $level_label).'</td>
											<td valign="top" class="inputField">'.$desired_level_html.'
												<span id="end_level_suggestion_section" class="messageStackWarning"></span>
												&nbsp;<span id="desired_field_loading"></span>
											</td>
				 						</tr>
									</table>
								</div>';
		}
		
		$html_framework .= $desired_class_html;
	}
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript">
		var selectionTitle = Array();
		selectionTitle['id'] = '';
		selectionTitle['text'] = 'Please Select';
		
		//	By Wei Chen
		function invoke_start_level_tasks(id, selObj1, selObj2, divClass1, divClass2, divOptions) {
			var start_level_obj = DOMCall(selObj1);
			var end_level_obj = DOMCall(selObj2);
			var div_class_obj = DOMCall(divClass1);
			var div_desired_class_obj = DOMCall(divClass2);
			var div_option_obj = DOMCall(divOptions);
			var level_suggest_obj = DOMCall('end_level_suggestion_section');
			var current_loading_span_obj = DOMCall('current_field_loading');
			
			start_level_obj.disabled = true;
			div_class_obj.innerHTML = '';
			div_desired_class_obj.innerHTML = '';
			level_suggest_obj.innerHTML = '';
			current_loading_span_obj.innerHTML = 'Loading...';
			
			if (start_level_obj.value > 0) {
				var ref_url = "custom_product_xmlhttp.php?action=start_level_task&level_id="+id+"&s_level="+start_level_obj.value+'&lang='+'<?=$languages_id?>';
				xmlhttp.open("GET", ref_url);
			    xmlhttp.onreadystatechange = function() { 
			      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
			      		//res = xmlhttp.responseText;
			      		
			      		clearOptionList(end_level_obj);
			      		var selection = xmlhttp.responseXML.getElementsByTagName("desired_selection")[0];
			      		
			      		if (typeof (selection) != 'undefined' && selection != null) {
			      			appendToSelect(end_level_obj, selectionTitle['id'], selectionTitle['text']);
			      			
			      			var option_sel = '';
				      		for (var i=0; i < selection.childNodes.length; i++) {
				      			option_sel = selection.getElementsByTagName("option")[i];
				      			appendToSelect(end_level_obj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
						    }
			      		}
			      		
			      		if (end_level_obj.options.length < 2) {
			      			clearOptionList(end_level_obj);
			      			appendToSelect(end_level_obj, '', '---');
			      		}
			      		
			      		selection = xmlhttp.responseXML.getElementsByTagName("class_selection")[0];
			      		if (typeof (selection) != 'undefined' && selection != null && selection.childNodes.length > 0) {
				      		var dTable = document.createElement('TABLE');
				      		dTable.setAttribute('width', '100%');
				      		dTable.setAttribute('cellspacing', '0');
				      		dTable.setAttribute('cellpadding', '2');
				      		
				      		var dTbody = document.createElement('TBODY');
				      		var dRow = document.createElement('TR'); 
							var dCell1 = document.createElement('TD');
							dCell1.setAttribute('width', '40%');
							dCell1.setAttribute('class', 'inputLabel');
							dCell1.setAttribute('className', 'inputLabel');
							
							var dCell2 = document.createElement('TD'); 
							
							var dClassLabel = document.createTextNode('<?=sprintf(TEXT_CURRENT_LABEL, $class_label)?>');
							dCell1.appendChild(dClassLabel);
							
							class_sel_obj = document.createElement("SELECT");
					    	class_sel_obj.name = 'cmbCurrentClass';
					    	class_sel_obj.id = 'cmbCurrentClass';
					    	
					    	class_sel_obj.onchange=function() {
								invoke_class_tasks(id, selObj2, 'cmbCurrentClass', 'cmbDesiredClass', divClass2);
							}
							
			      			var option_sel = '';
				      		for (var i=0; i < selection.childNodes.length; i++) {
				      			option_sel = selection.getElementsByTagName("option")[i];
				      			appendToSelect(class_sel_obj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
						    }
				      		
				      		var loading_span_obj = document.createElement('SPAN');
						    loading_span_obj.setAttribute('id', 'current_class_loading');
						    loading_span_obj.setAttribute('className', 'inputField');
							loading_span_obj.setAttribute('class', 'inputField');
						    
						    dCell2.appendChild(class_sel_obj);
						    dCell2.appendChild(loading_span_obj);
						    
							dRow.appendChild(dCell1); 
							dRow.appendChild(dCell2); 
							
							dTbody.appendChild(dRow);
							dTable.appendChild(dTbody);
							
				      		div_class_obj.appendChild(dTable);
				      		div_class_obj.style.display = 'block';
			      		}
			      		
			      		if (typeof (xmlhttp.responseXML.getElementsByTagName("option_selection")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("option_selection")[0] != null) {
							div_option_obj.innerHTML = xmlhttp.responseXML.getElementsByTagName("option_selection")[0].firstChild.data
						} else {
			      			div_option_obj.innerHTML = '';
			      		}
			      		
			      		reset_price();
			      		current_loading_span_obj.innerHTML = '';
			      		
			      		start_level_obj.disabled = false;
			      	}
			    }
			    xmlhttp.send(null);
			} else {
				clearOptionList(end_level_obj);
				appendToSelect(end_level_obj, '', '---');
				
				// Reset Price and ETA, and show global options, do it first here for safe in case the xmlhttp doesn't work
				reset_price();
				
				var ref_url = "custom_product_xmlhttp.php?action=list_global_options&level_id="+id+'&lang='+'<?=$languages_id?>';
				xmlhttp.open("GET", ref_url);
			    xmlhttp.onreadystatechange = function() {
			      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
						if (typeof (xmlhttp.responseXML.getElementsByTagName("option_selection")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("option_selection")[0] != null) {
							div_option_obj.innerHTML = xmlhttp.responseXML.getElementsByTagName("option_selection")[0].firstChild.data
						} else {
				  			div_option_obj.innerHTML = '';
				  		}
				  		
				  		start_level_obj.disabled = false;
				  		
				  		// Reset Price and ETA, and show global options
						reset_price();
						
						current_loading_span_obj.innerHTML = '';
				  	}
				}
				xmlhttp.send(null);
			}
		}
		
		function invoke_desired_level_tasks(id, selObj1, selObj2, divClass, divOptions) {
			var start_level_obj = DOMCall(selObj1);
			var end_level_obj = DOMCall(selObj2);
			var div_class_obj = DOMCall(divClass);
			var div_option_obj = DOMCall(divOptions);
			var hidden_price_obj = DOMCall('hidden_quote_price');
			var hidden_eta_obj = DOMCall('hidden_quote_eta');
			var level_suggest_obj = DOMCall('end_level_suggestion_section');
			var desired_loading_span_obj = DOMCall('desired_field_loading');
			
			var current_class_obj = DOMCall('cmbCurrentClass');
			
			if (current_class_obj != null) {
				var sel_class_value = current_class_obj.value;
			} else {
				var sel_class_value = id;
			}
			
			start_level_obj.disabled = true;
			end_level_obj.disabled = true;
			
			div_class_obj.innerHTML = '';
			level_suggest_obj.innerHTML = '';
			desired_loading_span_obj.innerHTML = 'Loading...';
			
			if (end_level_obj.value > 0) {
				var ref_url = "custom_product_xmlhttp.php?action=end_level_task&level_id="+id+"&s_level="+start_level_obj.value+"&e_level="+end_level_obj.value+"&class_root_id="+sel_class_value+"&lang="+'<?=$languages_id?>';
				xmlhttp.open("GET", ref_url);
			    xmlhttp.onreadystatechange = function() { 
			      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
			      		//res = xmlhttp.responseText;
			      		
			      		var selection = xmlhttp.responseXML.getElementsByTagName("class_selection")[0];
			      		if (typeof (selection) != 'undefined' && selection != null && selection.childNodes.length > 0) {
				      		var dTable = document.createElement('TABLE');
				      		dTable.setAttribute('width', '100%');
				      		dTable.setAttribute('cellspacing', '0');
				      		dTable.setAttribute('cellpadding', '2');
				      		
				      		var dTbody = document.createElement('TBODY');
				      		var dRow = document.createElement('TR'); 
							var dCell1 = document.createElement('TD');
							dCell1.setAttribute('width', '40%');
							dCell1.setAttribute('class', 'inputLabel');
							dCell1.setAttribute('className', 'inputLabel');
							
							var dCell2 = document.createElement('TD'); 
							
							var dClassLabel = document.createTextNode('<?=sprintf(TEXT_DESIRED_LABEL, $class_label)?>');
							dCell1.appendChild(dClassLabel);
							
							class_sel_obj = document.createElement("SELECT");
					    	class_sel_obj.name = 'cmbDesiredClass';
					    	class_sel_obj.id = 'cmbDesiredClass';
					    	
			      			var option_sel = '';
				      		for (var i=0; i < selection.childNodes.length; i++) {
				      			option_sel = selection.getElementsByTagName("option")[i];
				      			appendToSelect(class_sel_obj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
						    }
				      		
				      		dCell2.appendChild(class_sel_obj);
				      		
							dRow.appendChild(dCell1); 
							dRow.appendChild(dCell2); 
							
							dTbody.appendChild(dRow);
							dTable.appendChild(dTbody);
							
				      		div_class_obj.appendChild(dTable);
				      		div_class_obj.style.display = 'block';
			      		}
			      		
			      		if (typeof (xmlhttp.responseXML.getElementsByTagName("option_selection")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("option_selection")[0] != null) {
							div_option_obj.innerHTML = xmlhttp.responseXML.getElementsByTagName("option_selection")[0].firstChild.data
						} else {
			      			div_option_obj.innerHTML = '';
			      		}
			      		
			      		var bracket_info = xmlhttp.responseXML.getElementsByTagName("bracket_info")[0];
			      		if (typeof (bracket_info) != 'undefined' && bracket_info != null) {
			      			if (typeof (bracket_info.getElementsByTagName("price")[0]) != "undefined" && bracket_info.getElementsByTagName("price")[0] != null) {
								var price = bracket_info.getElementsByTagName("price")[0].firstChild.data;
								hidden_price_obj.value = price;
							}
							if (typeof (bracket_info.getElementsByTagName("eta")[0]) != "undefined" && bracket_info.getElementsByTagName("eta")[0] != null) {
								var eta = bracket_info.getElementsByTagName("eta")[0].firstChild.data;
								hidden_eta_obj.value = eta;
							}
							
							if (typeof (bracket_info.getElementsByTagName("msg")[0]) != "undefined" && bracket_info.getElementsByTagName("msg")[0] != null) {
								var msg = bracket_info.getElementsByTagName("msg")[0].firstChild.data;
								
								if (msg.length > 0) {
									level_suggest_obj.innerHTML = msg;
								}
							}
							
							show_price_and_eta_info(price, 0, eta, 0);
			      		}
			      		
			      		desired_loading_span_obj.innerHTML = '';
			      		start_level_obj.disabled = false;
						end_level_obj.disabled = false;
			      	}
			    }
			    xmlhttp.send(null);
			} else {
				// Reset Price and ETA, and show global options, do it first here for safe in case the xmlhttp doesn't work
				reset_price();
				
				var ref_url = "custom_product_xmlhttp.php?action=list_global_options&level_id="+id+'&lang='+'<?=$languages_id?>';
				xmlhttp.open("GET", ref_url);
			    xmlhttp.onreadystatechange = function() {
			      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
						if (typeof (xmlhttp.responseXML.getElementsByTagName("option_selection")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("option_selection")[0] != null) {
							div_option_obj.innerHTML = xmlhttp.responseXML.getElementsByTagName("option_selection")[0].firstChild.data
						} else {
				  			div_option_obj.innerHTML = '';
				  		}
				  		start_level_obj.disabled = false;
						end_level_obj.disabled = false;
						
						// Reset Price and ETA, and show global options
						reset_price();
						desired_loading_span_obj.innerHTML = '';
				  	}
				}
				xmlhttp.send(null);
			}
		}
		
		function invoke_class_tasks(id, selObj2, classSelObj1, classSelObj2, divClass) {
			var end_level_obj = DOMCall(selObj2);
			var current_class_obj = DOMCall(classSelObj1);
			var desired_class_obj = DOMCall(classSelObj2);
			var div_class_obj = DOMCall(divClass);
			var current_class_loading_span_obj = DOMCall('current_class_loading');
			
			if (end_level_obj != null && end_level_obj.value > 0) {
				end_level_obj.disabled = true;
				current_class_obj.disabled = true;
				
				current_class_loading_span_obj.innerHTML = ' Loading...';
				div_class_obj.innerHTML = '';
				
				var ref_url = "custom_product_xmlhttp.php?action=class_task&level_id="+id+"&e_level="+end_level_obj.value+"&class_root_id="+current_class_obj.value+'&lang='+'<?=$languages_id?>';
				xmlhttp.open("GET", ref_url);
			    xmlhttp.onreadystatechange = function() { 
			      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
			      		//res = xmlhttp.responseText;
			      		//alert(res);
			      		
			      		var selection = xmlhttp.responseXML.getElementsByTagName("class_selection")[0];
			      		if (typeof (selection) != 'undefined' && selection != null && selection.childNodes.length > 0) {
				      		var dTable = document.createElement('TABLE');
				      		dTable.setAttribute('width', '100%');
				      		dTable.setAttribute('cellspacing', '0');
				      		dTable.setAttribute('cellpadding', '2');
				      		
				      		var dTbody = document.createElement('TBODY');
				      		var dRow = document.createElement('TR'); 
							var dCell1 = document.createElement('TD');
							dCell1.setAttribute('width', '40%');
							dCell1.setAttribute('class', 'inputLabel');
							dCell1.setAttribute('className', 'inputLabel');
							
							var dCell2 = document.createElement('TD'); 
							
							var dClassLabel = document.createTextNode('<?=sprintf(TEXT_DESIRED_LABEL, $class_label)?>');
							dCell1.appendChild(dClassLabel);
							
							class_sel_obj = document.createElement("SELECT");
					    	class_sel_obj.name = 'cmbDesiredClass';
					    	class_sel_obj.id = 'cmbDesiredClass';
					    	
			      			var option_sel = '';
				      		for (var i=0; i < selection.childNodes.length; i++) {
				      			option_sel = selection.getElementsByTagName("option")[i];
				      			appendToSelect(class_sel_obj, option_sel.getAttribute("index"), option_sel.firstChild.nodeValue);
						    }
				      		
				      		dCell2.appendChild(class_sel_obj);
				      		
							dRow.appendChild(dCell1); 
							dRow.appendChild(dCell2); 
							
							dTbody.appendChild(dRow);
							dTable.appendChild(dTbody);
							
				      		div_class_obj.appendChild(dTable);
				      		div_class_obj.style.display = 'block';
			      		}
			      		
			      		current_class_loading_span_obj.innerHTML = '';
			      		end_level_obj.disabled = false;
						current_class_obj.disabled = false;
			      	}
			    }
			    xmlhttp.send(null);
			}
		}
		
		function reset_price() {
			var buy_price_price_obj = DOMCall('calced_price');
			var quote_price_obj = DOMCall('quote_price_div');
			var quote_eta_obj = DOMCall('quote_eta_div');
			var hidden_price_obj = DOMCall('hidden_quote_price');
			var hidden_eta_obj = DOMCall('hidden_quote_eta');
			
			buy_price_price_obj.innerHTML = display_currency(0);
			quote_price_obj.innerHTML = quote_eta_obj.innerHTML = '';
			hidden_price_obj.value = hidden_eta_obj.value = 0;
			
			show_price_and_eta_info(0, 0, 0, 0);	// Reset to base price and time
		}
		
		function show_price_and_eta_info(base_price, extra_price, base_eta, extra_eta) {
			var ind_discount = 0;
			var grp_discount = 0;
			var start_price = <?=trim($base_price_value) != '' ? $base_price_value : 0 ?>;
			var start_time = <?=trim($base_time_value) != '' ? $base_time_value : 0 ?>;
			var min_time = <?=trim($min_time_value) != '' ? $min_time_value : 0 ?>;
			var approximate_str = '<?=TEXT_APPROXIMATE_ETA?>';
			
			var buy_price_price_obj = DOMCall('calced_price');
			var quote_price_obj = DOMCall('quote_price_div');
			var quote_eta_obj = DOMCall('quote_eta_div');
			
			if (base_price == null || trim_str(base_price) == '') base_price = 0;
			if (extra_price == null) extra_price = 0;
			if (base_eta == null || trim_str(base_eta) == '') base_eta = 0;
			if (extra_eta == null) extra_eta = 0;
			
			var total_price = parseFloat(start_price) + parseFloat(base_price) + parseFloat(extra_price);
			var total_eta = parseFloat(start_time) + parseFloat(base_eta) + parseFloat(extra_eta);
			total_eta = (total_eta < min_time) ? min_time : total_eta;
			
			var eta_days = Math.floor(total_eta/24);
			var eta_hours = 0;
			if (total_eta < 24 || total_eta % 24 > 0) {
				eta_hours = Math.ceil( total_eta - (eta_days * 24));
				
				if (eta_hours == 24) {
					eta_days++;
					eta_hours = 0;
				}
			}
			
			var days_str = (eta_days > 0) ? eta_days + ' day' + (eta_days > 1 ? 's' : '') : '';
			var hrs_str = (eta_hours > 0) ? ' ' + eta_hours + ' hour' + (eta_hours > 1 ? 's' : '') : '';
			
			var original_price = total_price;
			var total_discount = ind_discount + grp_discount;
			var got_discount = false;
			
			if (Math.abs(total_discount) > 0) {
				//	Got discount
				got_discount = true;
				if (total_discount > 0) {
					total_price = total_price + total_price * Math.abs(total_discount) / 100;
				} else {
					total_price = total_price - total_price * Math.abs(total_discount) / 100;
				}
			}
			
			if (got_discount) {
				quote_price_obj.innerHTML = '<s>' + display_currency(original_price) + '</s>&nbsp;&nbsp;' + display_currency(total_price) + (Math.abs(ind_discount) > 0 ? '<span class="requiredInfo">*</span>' : '');
			} else {
				quote_price_obj.innerHTML = display_currency(total_price);
			}
			buy_price_price_obj.innerHTML = display_currency(total_price);
			if (days_str.length < 0 && hrs_str.length < 0) {
				quote_eta_obj.innerHTML = approximate_str + ' 0 hour.';
			} else {
				quote_eta_obj.innerHTML = approximate_str + ' ' + days_str + hrs_str;
			}
		}
		
		function update_option_price_and_eat(sel_obj, id) {
			var hidden_price_obj = DOMCall('hidden_quote_price');
			var hidden_eta_obj = DOMCall('hidden_quote_eta');
			
			var opts_price = 0;
			var opts_eta = 0;
			
			var optionRegExp  = /cp_option\[(\d+)\]/i;
			
			var form_ele_num = document.mainForm.elements.length;
			for (i=0; i < form_ele_num; i++) {
				var ele_obj = document.mainForm.elements[i];
				var res_array = optionRegExp.exec(ele_obj.name);
				
				if (res_array != null && res_array.length > 0) {
					var opt_id = res_array[1];
					
					if (cp_option_price[opt_id] != null || cp_option_eta[opt_id] != null) {
						switch(ele_obj.type) {
							case 'text':
							case 'textarea':
								if (trim_str(ele_obj.value) != '') {
									if (cp_option_price[opt_id][0] != null)	opts_price += parseFloat(cp_option_price[opt_id][0]);
									if (cp_option_eta[opt_id][0] != null)	opts_eta += parseFloat(cp_option_eta[opt_id][0]);
								}
								break;
							case 'radio':
								if (ele_obj.checked) {
									var sub_id = ele_obj.value;
									if (trim_str(sub_id) != '') {
										if (cp_option_price[opt_id][sub_id] != null)	opts_price += parseFloat(cp_option_price[opt_id][sub_id]);
										if (cp_option_eta[opt_id][sub_id] != null)	opts_eta += parseFloat(cp_option_eta[opt_id][sub_id]);
									}
								}
								break;
							default :
								var sub_id = ele_obj.value;
								if (trim_str(sub_id) != '') {
									if (cp_option_price[opt_id][sub_id] != null)	opts_price += parseFloat(cp_option_price[opt_id][sub_id]);
									if (cp_option_eta[opt_id][sub_id] != null)	opts_eta += parseFloat(cp_option_eta[opt_id][sub_id]);
								}
								break;
						}
					}
				}
			}
			
			show_price_and_eta_info(hidden_price_obj.value, opts_price, hidden_eta_obj.value, opts_eta);
		}
		
		function submitValidate() {
			var current_level_obj = DOMCall('cmbCurrentLevel');
			var desired_level_obj = DOMCall('cmbDesiredLevel');
			
			var errorcount = 0;
			var errormsg = '<?=JS_ERROR?>';
			
			if (current_level_obj != null && current_level_obj.selectedIndex < 1) {
				errorcount++;
				errormsg = errormsg + "* Current " + '<?=$level_label?>' + " is missing.\n";
			}
			
			if (desired_level_obj != null && desired_level_obj.selectedIndex < 1) {
				errorcount++;
				errormsg = errormsg + "* Desired " + '<?=$level_label?>' + " is missing.\n";
			}
			
			var options_checking_result = options_validation();
			if (options_checking_result != true) {
				errorcount++;
				errormsg += "\n" + 'Other Info Error:' + "\n" + options_checking_result;
			}
			
			if (errorcount == 0) {
				return true;;
				//document.mainForm.submit();
			} else {
				alert(errormsg);
				return false;
			}
		}
		
		function display_currency(anynum) {
			return currency(anynum, '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_left"]?>', '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_right"]?>', 2);
		}
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top"></td>
			<!-- body_text //-->
			<td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%" class="pageHeading"><b><?=$header_title?></b></td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td width="100%" valign="top">
<?	if ($custom_type_row['custom_products_type_id'] == '1') {
		echo tep_draw_form('mainForm', FILENAME_ORDERS_CP_INFO, tep_get_all_get_params(array('action')).'action=update', 'post', 'onSubmit="return submitValidate();"');
?>
							<table width="99%" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<td align="center">
										<table width="99%" border="0" cellpadding="0" cellspacing="0">
						        			<tr>
								          		<td valign="top" class="main"><b><?=$custom_type_row['products_name']?></b></td>
								           		<td class="main" align="right" valign="top"><?="<span id='calced_price'>".$currencies->display_price($total_price_info["price"], 0)."</span>"?></td>
								        	</tr>
								        </table>
								    </td>
								</tr>
								<tr>
									<td align="center">
<?	if (tep_not_null($custom_type_row['products_image'])) {
		echo tep_image(DIR_WS_CATALOG_IMAGES . 'products/' . $custom_type_row['products_image'], $custom_type_row['products_name'], '', '', 'hspace="5" vspace="5"');
	}
	
	$product_description_align  = tep_not_null(PRODUCT_DESCRIPTION_ALIGN) ? PRODUCT_DESCRIPTION_ALIGN : "center";
    $product_description_width  = tep_not_null(PRODUCT_DESCRIPTION_WIDTH) ? PRODUCT_DESCRIPTION_WIDTH : "170%";
?>
									</td>
								</tr>
								<tr>
	    							<td class="main" align="<?=$product_description_align?>">
        								<div style="width: <?=$product_description_width?>;"><?=stripslashes($custom_type_row['products_description'])?></div>
    								</td>
								</tr>
								<tr class="infoBoxContents">
									<td>
<?
	echo $html_framework;
	
	echo tep_draw_hidden_field('datapool_parent_id', $datapool_parent_id);
	echo tep_draw_hidden_field('hidden_quote_price', $latest_price, 'id="hidden_quote_price"');
	echo tep_draw_hidden_field('hidden_quote_eta', $latest_eta, 'id="hidden_quote_eta"');
	
	if (count($option_html_array)) {
		$custom_bracket_tags_shown = false;
		echo '							<div class="main">'.TEXT_ENTER_OTHER_INFO.'</div>
										<div id="options_div" style="padding-top:0.2em; display: block;">';
		
		foreach ($option_html_array as $option_key => $option_res) {
			if ($option_res["data_pool_options_input_type"] == 999) {
				echo '						<table width="100%" cellspacing="2" cellpadding="0">
												<tr><td colspan="2"></td></tr>
												<tr>
													<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
					      						</tr>
					      						<tr>
					      							<td colspan="2">
					      								<div id="custom_bracket_tags_div">';
      			
      			if (!$custom_bracket_tags_shown && count($custom_tags)) {
      				$custom_bracket_tags_html = '			<table width="100%" cellspacing="0" cellpadding="0">';
      				foreach ($custom_tags as $custom_bracket_tags_res) {
      					$custom_bracket_tags_html .= '			<tr>
																	<td width="40%" valign="top" class="inputLabel">'.$custom_bracket_tags_res["display_label"].'</td>
																	<td valign="top" class="inputField">'.$custom_bracket_tags_res["value"].'</td>
																</tr>';
      				}
      				$custom_bracket_tags_shown = true;
      				$custom_bracket_tags_html .= '			</table>';
      			}
      			echo $custom_bracket_tags_html;
      			
				echo '									</div>
													</td>
												</tr>
												<tr>
													<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_PRICE.'</td>
													<td valign="top" class="shoppingCartTotal"><div id="quote_price_div">'.(isset($total_price_info) && is_array($total_price_info) ? ($got_discount ? '<s>'.$total_price_info["price_text"].'</s>&nbsp;&nbsp;'.$currencies->display_price($product_info['products_id'], $total_price_info["price"], $product_tax).(abs($cust_discount_array['individual']) > 0 ? '<span class="requiredInfo">*</span>' : '') : $total_price_info["price_text"]) : '').'</div></td>
												</tr>
												<tr>
													<td width="40%" valign="top" class="shoppingCartTotal">'.TEXT_OPTION_ETA.'</td>
													<td valign="top" class="shoppingCartTotal"><div id="quote_eta_div">'.(isset($total_price_info) && is_array($total_price_info) ? $total_price_info["eta_text"] : '').'</div></td>
												</tr>
												<tr>
      												<td width="1" class="storeBoxLine" colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="1"></td>
      											</tr>
      											<tr><td colspan="2"></td></tr>
											</table>';
			} else {
				$field_resource = tep_draw_option_field($option_key, $option_res, $custom_cart["option"][$option_key]);
				echo '						<table width="100%" cellspacing="0" cellpadding="2">
												<tr>
													<td width="40%" valign="top" class="inputLabel">'.$option_res["data_pool_options_title"].'</td>
													<td valign="top" class="inputField">'.$field_resource["field"].'</td>
												</tr>
											</table>';
			}
		}
	}
?>
										</div>
<?="\n" . '<script type="text/javascript" language="javascript">' . tep_preload_options_js($products_id) . '</script>' . "\n"?>
										<div id="price_info_div" style="padding-top:0.2em;"></div>
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?	if (tep_not_null($html_framework)) {
	} else {
?>
											<tr>
												<td valign="top" align="center" class="productSmallText" colspan="2"><font color="#FF0000"><?=TEXT_PRODUCT_NOT_AVAILABLE?></font></td>
											</tr>
<?	} ?>
										</table>
									</td>
				  				</tr>
				  				<tr>
	           						<td align ="center" class="main"><br>
	           							<?=tep_submit_button(BUTTON_CONFIRM, ALT_BUTTON_CONFIRM, '', 'inputButton')?>&nbsp;
			  							<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, '', 'onClick="self.close(); opener.focus();"', 'inputButton')?>
			  						</td>
			  					</tr>
							</table>
						</form>
<?
	} else {
		echo TEXT_NOT_A_PWL_PRODUCT;
	}
?>
        				</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>