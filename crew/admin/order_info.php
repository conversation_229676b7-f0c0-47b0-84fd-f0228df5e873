<?
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'order.php');
require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

$status_options = array();
$order_status_select_sql = "SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id='" . $languages_id  . "' ORDER BY orders_status_sort_order";
$order_status_result_sql = tep_db_query($order_status_select_sql);
while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
	$status_options[$order_status_row["orders_status_id"]] = $order_status_row["orders_status_name"];
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
    			</table>
    		</td>
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
<!-- body_text //-->
    				<tr>
        				<td width="100%">
	    					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
	   								<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
	   								<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
								</tr>
							</table>
						</td>
					</tr>
<?
	$order = new order($id);
	$order_no = $id;
?>
					<tr>
	        			<td width="100%">
							<table border="0" width="100%" cellspacing="2" cellpadding="2" class="formArea">
			  					<tr>
			    					<td colspan="3" width="35%">
			    						<span class="pageHeading"><?=TABLE_HEADING_ORDER_INFO?></span>
			    					</td>
			    					<td>&nbsp;</td>
			  					</tr>
			  					<tr>
			  						<td class="smallText" width="25%"><?=ENTRY_ORDER_ID?></td>
			  						<td width="50%" class="smallText">
			  							<?=tep_db_input($order_no)?>
			  						</td>
			  					</tr>
			  					<tr>
			  						<td class="smallText"><?=ENTRY_CUSTOMER?></td>
			  						<td class="smallText"><?=$order->customer["name"]?></td>
			  					</tr>
			  					<tr>
			  						<td class="smallText"><?=ENTRY_EMAIL?></td>
			  						<td class="smallText"><?=$order->customer["email_address"]?></td>
			  					</tr>
			  					<tr>
			  						<td class="smallText"><?=ENTRY_CUSTOMER_SIGNUP_DATE?></td>
			  						<td class="smallText"><?=$order->customer["customers_info_date_account_created"]?></td>
			  					</tr>
			  					<tr>
			  						<td class="smallText"><?=ENTRY_DATE_ORDER_CREATED?></td>
			  						<td class="smallText"><?=$order->info["date_purchased"]?></td>
			  					</tr>
			  					<tr>
			  						<td class="smallText"><?=ENTRY_ORDERING_IP?></td>
			  						<td class="smallText"><?=tep_show_ip($order->info["remote_addr"])?></td>
			  					</tr>
			  					<tr>
			  						<td class="smallText"><?=ENTRY_STATUS?></td>
			  						<td class="smallText">
			  						<?
			  							switch ($order->info["orders_status"]) {
			  								case "99999":
			  									$status="Paypal Processing";
			  									break;
			  								case "100000":
			  									$status="Updated";
			  									break;
			  								default:
			  									$status = $status_options[$order->info["orders_status"]];
			  									break;
			  							}
										echo $status."&nbsp;&nbsp;&nbsp;".'[<a href="' . tep_href_link("orders.php", 'oID='.$order_no.'&action=edit', 'NONSSL') . '"><font color=red>Edit</font></a>]';
			    					?>
			  						</td>
			  					</tr>
			  					<tr>
			  						<td class="smallText"><?=ENTRY_PAYMENT_METHOD?></td>
			  						<td class="smallText"><?=$order->info["payment_method"]?></td>
			  					</tr>
							</table>
						</td>
					</tr>
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
      				</tr>
					<tr>
	        			<td width="100%">
							<table border="0" width="100%" cellspacing="1" cellpadding="2">
			  					<tr>
									<td class="invoiceBoxHeading" ><?=TABLE_HEADING_PRODUCTS?></td>
									<!--td class="invoiceBoxHeading" align="center"><?=TABLE_HEADING_PRODUCTS_LOCATION?></td-->
									<td class="invoiceBoxHeading" align="center"><?=TABLE_HEADING_STOCK_QUANTITY?></td>
									<td class="invoiceBoxHeading" align="center"><?=TABLE_HEADING_QUANTITY?></td>
									<td class="invoiceBoxHeading" align="right"><?=TABLE_HEADING_PRICE?></td>
									<td class="invoiceBoxHeading" align="right"><?=TABLE_HEADING_TOTAL_EXCLUDING_TAX?></td>
									<td class="invoiceBoxHeading" align="right"><?=TABLE_HEADING_TOTAL_INCLUDING_TAX?></td>
								</tr>
<?
	for ($i=0; $i<count($order->products); $i++) {
	
		$sql_query = "SELECT orders_custom_products_value from ".TABLE_ORDERS_CUSTOM_PRODUCTS." WHERE orders_products_id='".(int)$order->products[$i]['order_products_id']."';";
		$result = tep_db_query($sql_query);
		if ($row = tep_db_fetch_array($result)) {
			$custom_product_info = $row['orders_custom_products_value'];
		} else {
			$custom_product_info = "";
		}
		
		$result_query_maincat = tep_db_query("SELECT products_id, products_quantity, products_bundle_dynamic, products_bundle, products_skip_inventory FROM " . TABLE_PRODUCTS . " WHERE products_id = " . $order->products[$i]['id']);
		$row_maincat = tep_db_fetch_array($result_query_maincat);
		
		$cat2_name_query = tep_db_query("SELECT categories_id FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " WHERE products_id = " . $order->products[$i]['id'] . " ORDER BY products_id");
		$cat2_name = tep_db_fetch_array($cat2_name_query);
		$prod_maincatpath = tep_output_generated_category_path_sq($cat2_name['categories_id']);
		$prod_qty_store = ($row_maincat['products_skip_inventory'] ? TEXT_OPTION_NOT_APPLICABLE : ($row_maincat['products_quantity'] < 0 ? "<span style='color:red;'>" . $row_maincat['products_quantity'] . "</span>" : $row_maincat['products_quantity'] ));
		if ($row_maincat['products_id'] != "") {
			$prod_loc_result_sql = tep_db_query("SELECT products_location FROM " . TABLE_PRODUCTS_DESCRIPTION . " WHERE products_id =". $row_maincat['products_id']);
			while ($prod_loc_row = tep_db_fetch_array($prod_loc_result_sql)) {
				$prod_loc = $prod_loc_row['products_location'];
			}
		}
		
		$subprod_name = "";
		$subprod_loc = "";
		$subprod_stock = "";
		$subprod_qty = "";
		if ($row_maincat['products_bundle_dynamic'] == "yes") {
			$prod_qty_store = TEXT_OPTION_NOT_APPLICABLE;
			$ordered_qty_text = $order->products[$i]['qty'];
			for ($pbd_loop=0; $pbd_loop < count($order->products[$i]["bundle"]); $pbd_loop++) {
				$subprod_info_select_sql = "SELECT p.products_id, p.products_quantity, pd.products_location, pc.categories_id 
											FROM " . TABLE_PRODUCTS . " AS p 
											LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
												ON p.products_id=pd.products_id 
											LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
												ON pd.products_id=pc.products_id 
											WHERE p.products_id = " . $order->products[$i]["bundle"][$pbd_loop]['id'] . " AND pc.products_is_link=0 ORDER BY pc.products_id ";
				$subprod_info_result_sql = tep_db_query($subprod_info_select_sql);
				$subprod_info = tep_db_fetch_array($subprod_info_result_sql);
				if ($subprod_info["products_id"]) {
					$subprod_name .= '&nbsp;&nbsp;' . '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($subprod_info['categories_id']) . '&pID=' . $subprod_info["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="'.TEXT_PRODUCTS_ID.' '.$subprod_info["products_id"].'">' . $order->products[$i]["bundle"][$pbd_loop]["name"] . '</a>';
				} else {
					$subprod_name .= '&nbsp;&nbsp;' . $order->products[$i]["bundle"][$pbd_loop]["name"];
				}
				$subprod_maincatpath =  ($subprod_info["products_id"]) ? "<span class='categoryPath'>[".tep_output_generated_category_path_sq($subprod_info['categories_id'])."]</span>" : "**--This product is no longer existing in db--**";
				$subprod_name .= "<br>&nbsp;&nbsp;".$subprod_maincatpath;
				$subprod_loc .= ($subprod_info["products_location"] ? $subprod_info["products_location"] : TEXT_OPTION_NOT_APPLICABLE) . "<br><br>";
				$subprod_stock .= (tep_check_product_skip_inventory($order->products[$i]["bundle"][$pbd_loop]['id']) ? TEXT_OPTION_NOT_APPLICABLE : ($subprod_info["products_quantity"] < 0 ? "<span style='color:red;'>" . $subprod_info["products_quantity"] . "</span>" : $subprod_info["products_quantity"] )) . "<br><br>";
				//$subprod_total_qty = $order->products[$i]["bundle"][$pbd_loop]["qty"] * $order->products[$i]["bundle"][$pbd_loop]['subproduct_qty'];
				$subprod_total_qty = $order->products[$i]["bundle"][$pbd_loop]["qty"];
				$subprod_qty .= "<b>" . $subprod_total_qty . "</b><br><br>";
				$subprod_name .= "<br>";
			}
		} else if ($row_maincat['products_bundle'] == "yes") {
			$prod_qty_store = TEXT_OPTION_NOT_APPLICABLE;
			$ordered_qty_text = $order->products[$i]['qty'];
			for ($static_loop=0; $static_loop < count($order->products[$i]["static"]); $static_loop++) {
				$subprod_info_select_sql = "SELECT p.products_id, p.products_quantity, pd.products_location, pc.categories_id 
											FROM " . TABLE_PRODUCTS . " AS p 
											LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
												ON p.products_id=pd.products_id 
											LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
												ON pd.products_id=pc.products_id 
											WHERE p.products_id = " . $order->products[$i]["static"][$static_loop]['id'] . " AND pc.products_is_link=0 ORDER BY pc.products_id ";
				$subprod_info_result_sql = tep_db_query($subprod_info_select_sql);
				$subprod_info = tep_db_fetch_array($subprod_info_result_sql);
				if ($subprod_info["products_id"]) {
					$subprod_name .= '&nbsp;&nbsp;' . '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($subprod_info['categories_id']) . '&pID=' . $subprod_info["products_id"] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="'.TEXT_PRODUCTS_ID.' '.$subprod_info["products_id"].'">' . $order->products[$i]["static"][$static_loop]["name"] . '</a>';
				} else {
					$subprod_name .= '&nbsp;&nbsp;' . $order->products[$i]["static"][$static_loop]["name"];
				}
				$subprod_maincatpath = ($subprod_info["products_id"]) ? "<span class='categoryPath'>[".tep_output_generated_category_path_sq($subprod_info['categories_id'])."]</span>" : "**--This product is no longer existing in db--**";
				$subprod_name .= "<br>&nbsp;&nbsp;".$subprod_maincatpath;
				$subprod_loc .= ($subprod_info["products_location"] ? $subprod_info["products_location"] : TEXT_OPTION_NOT_APPLICABLE) . "<br><br>";
				$subprod_stock .= (tep_check_product_skip_inventory($order->products[$i]["bundle"][$pbd_loop]['id']) ? TEXT_OPTION_NOT_APPLICABLE : ($subprod_info["products_quantity"] < 0 ? "<span style='color:red;'>" . $subprod_info["products_quantity"] . "</span>" : $subprod_info["products_quantity"] )) . "<br><br>";
				//$subprod_total_qty = $order->products[$i]["static"][$static_loop]["qty"] * $order->products[$i]["static"][$static_loop]['subproduct_qty'];
				$subprod_total_qty = $order->products[$i]["static"][$static_loop]["qty"];
				$subprod_qty .= "<b>" . $subprod_total_qty . "</b><br><br>";
				$subprod_name .= "<br>";
			}
		} else {
			$ordered_qty_text = "<b>" . $order->products[$i]['qty'] . "</b>";
		}
		
		$row_style = ($i%2) ? 'invoiceListingEven' : 'invoiceListingOdd' ;
		
		echo '          <tr ' . "class=$row_style" . " onmouseover=\"rowOverEffect(this, 'invoiceListingRowOver')\" onmouseout=\"rowOutEffect(this, '".$row_style."')\" onclick=\"rowClicked(this, '".$row_style."')\" >" . "\n" .
			'            <td class="invoiceRecords" valign="top">'. ($row_maincat['products_id'] ? '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($cat2_name['categories_id']) . '&pID=' . $order->products[$i]['id'] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="'.TEXT_PRODUCTS_ID.' '.$order->products[$i]['id'].'">' . strip_tags($order->products[$i]['name']) . '</a>' : "<b>" . strip_tags($order->products[$i]['name']) . "</b>") .
			($order->products[$i]['pre_order'] ? '&nbsp;<span class="preOrderText">'.TEXT_INFO_PRE_ORDER.'</span>' : '' ) . '<br>';
		if ($row_maincat['products_id'] == "") {
			echo "**--This product is no longer existing in db--**";
		} else {
			echo "<span class='categoryPath'>[".$prod_maincatpath."]</span>";
		}
		if (isset($order->products[$i]['attributes']) && (sizeof($order->products[$i]['attributes']) > 0)) {
			for ($j = 0, $k = sizeof($order->products[$i]['attributes']); $j < $k; $j++) {
				echo '<br><nobr><small>&nbsp;<i> - ' . $order->products[$i]['attributes'][$j]['option'] . ': ' . $order->products[$i]['attributes'][$j]['value'];
				if ($order->products[$i]['attributes'][$j]['price'] != '0') echo ' (' . $order->products[$i]['attributes'][$j]['prefix'] . $currencies->format($order->products[$i]['attributes'][$j]['price'] * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . ')';
					echo '</i></small></nobr>';
			}
		}
		if(trim($custom_product_info)!="")
		echo "<br>$subprod_name<br>More Information:<br>--------------------<br>".$custom_product_info;
		else
		echo "<br>$subprod_name";
		
		echo 	'            </td>' . "\n" .
				'            <!--td class="invoiceRecords" align="center" valign="top"><span style="color:blue;">' . ($prod_loc ? $prod_loc : TEXT_OPTION_NOT_APPLICABLE) . '<br><br>' . $subprod_loc . '</span></td-->' . "\n" .
				'            <td class="invoiceRecords" align="center" valign="top"><span style="color:blue;">' . $prod_qty_store . '<br><br>' . $subprod_stock . '</span></td>' . "\n" .
				'            <td class="invoiceRecords" align="center" valign="top">' . $ordered_qty_text . '<br><br>' . $subprod_qty . '</td>' . "\n" .
				'            <td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->format($order->products[$i]['final_price'], true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n" .
				'            <td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->format($order->products[$i]['final_price'] * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
		if (strpos($order->products[$i]["name"], '(~1 Free Items - worth') !== false) {
			echo '            <td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->format(tep_add_tax($order->products[$i]['final_price'], $order->products[$i]['tax']) * ($order->products[$i]['qty']-1), true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
		} else {
			echo '            <td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->format(tep_add_tax($order->products[$i]['final_price'], $order->products[$i]['tax']) * ($order->products[$i]['qty']), true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
		}
		echo '          </tr>' . "\n";
	}
?>
								<tr>
									<td align="right" colspan="6">
										<table border="0" cellspacing="0" cellpadding="2">
<?								
					for ($i = 0, $n = sizeof($order->totals); $i < $n; $i++) {
						echo '              <tr>' . "\n" .
							'                <td align="right" class="smallText">' . $order->totals[$i]['title'] . '</td>' . "\n" .
							'                <td align="right" class="smallText">' . $order->totals[$i]['text'] . '</td>' . "\n" .
							'              </tr>' . "\n";
					}
?>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
<!-- body_text_eof //-->
				</table>
  			</td>
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>
