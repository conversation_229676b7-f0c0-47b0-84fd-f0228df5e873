<?php
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');

define('SYSTEM_PAYMENT_STORE_CREDITS', 'OGM_CREDITS');

//tep_db_connect() or die('Unable to connect to database server!');
$read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

$datetime_format = '%m/%d/%y %h:%i %p';
$status_options = array();
$payment_methods_array = array();
$payment_gateways_array = array();
$orders_amt_breakdown_array = array('2' => array('0' => array('id' => 'UD', 'text' => 'Undelivered'),
        '1' => array('id' => 'D', 'text' => 'Delivered'),
        '2' => array('id' => 'C', 'text' => 'Refunded')
    ),
    '3' => array('0' => array('id' => 'D', 'text' => 'Delivered'),
        '1' => array('id' => 'C', 'text' => 'Refunded'),
        '2' => array('id' => 'RV-WIN', 'text' => 'Reversed WIN'),
        '3' => array('id' => 'RV-LOST', 'text' => 'Reversed LOST'),
        '4' => array('id' => 'RV-RESOLVED', 'text' => 'Reversed RESOLVED')
    )
);

$currencies = new currencies();
$show_records = isset($_REQUEST['show_records']) ? (int) $_REQUEST['show_records'] : 15;
$action = isset($_REQUEST["action"]) ? $_REQUEST["action"] : '';
$page = isset($_GET['page']) ? $_GET['page'] : 1;

$order_status_select_sql = "SELECT orders_status_id, orders_status_name 
                            FROM " . TABLE_ORDERS_STATUS . " 
                            WHERE language_id=" . (int) $languages_id . " 
                            ORDER BY orders_status_sort_order";
$order_status_result_sql = tep_db_query($order_status_select_sql, 'read_db_link');
while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
    $status_options[$order_status_row["orders_status_id"]] = $order_status_row["orders_status_name"];
}

/* -- payment method -- */
$payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_title, payment_methods_parent_id, payment_methods_legend_color 
                                FROM " . TABLE_PAYMENT_METHODS . "
                                WHERE payment_methods_receive_status = '1' 
                                ORDER BY payment_methods_sort_order";
$payment_methods_result_sql = tep_db_query($payment_methods_select_sql, 'read_db_link');

$payment_gateways_array['method'][SYSTEM_PAYMENT_STORE_CREDITS] = 'Store Credits';

while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
    if ($payment_methods_row['payment_methods_parent_id'] > 0) {
        $payment_methods_array[$payment_methods_row['payment_methods_parent_id']][$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
    } else {
        $payment_gateways_array['method'][$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
        $payment_gateways_array['display_colour'][$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_legend_color'] ? $payment_methods_row['payment_methods_legend_color'] : "#E1E1E2";
    }
}

if (tep_not_null($action)) {
    switch ($action) {
        case "do_export":
            $file_location = $_SESSION['order_lists2_param']['filename'];

            if (file_exists($file_location)) {
                $filename = basename($file_location);
                $mime_type = 'text/x-csv';

                // Download
                header('Content-Type: ' . $mime_type);
                header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');

                // IE need specific headers
                if (PMA_USR_BROWSER_AGENT == 'IE') {
                    header('Content-Disposition: attachment; filename="' . $filename . '"');
                    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                    header('Pragma: public');
                } else {
                    header('Content-Disposition: attachment; filename="' . $filename . '"');
                    header('Pragma: no-cache');
                }

                readfile($file_location);
                exit();
            }
            break;
        case "export_report":
            $datetime_format = '%m/%d/%y';
            $show_records = "ALL";
            $file_location = DIR_FS_DOCUMENT_ROOT . 'download/order_list2' . date('YmdHis') . '.csv';
            $_SESSION['order_lists2_param']['filename'] = $file_location;
        case "show_report":
            if (isset($_GET['page'])) {
                $o_start_date = $_SESSION['order_lists2_param']['start_date'];
                $o_end_date = $_SESSION['order_lists2_param']['end_date'];
                $o_follow_up_date = $_SESSION['order_lists2_param']['follow_up_date'];
                $o_date_type = $_SESSION['order_lists2_param']['date_type'];
                $o_order_status = $_SESSION['order_lists2_param']['order_status'];
                $o_sub_status[2] = $_SESSION['order_lists2_param']["order_sub_status_2"];
                $o_sub_status[3] = $_SESSION['order_lists2_param']["order_sub_status_3"];
                $o_payment_gateways = $_SESSION['order_lists2_param']['payment_gateways_id'];
                $o_payment_method = $_SESSION['order_lists2_param']['payment_methods_id'];
                $o_cat_id = $_SESSION['order_lists2_param']['cat_id'];
                $o_site_id = $_SESSION['order_lists2_param']["site_id"];
            } else {
                $o_start_date = $_REQUEST['start_date'];
                $o_end_date = $_REQUEST['end_date'];
                $o_follow_up_date = $_REQUEST['follow_up_date'];
                $o_date_type = $_REQUEST['date_type'];
                $o_order_status = $_REQUEST['order_status'];
                $o_sub_status[2] = $_REQUEST['order_sub_status_2'];
                $o_sub_status[3] = $_REQUEST['order_sub_status_3'];
                $o_payment_gateways = (array) $_REQUEST['payment_gateways_id'];
                $o_payment_method = (array) $_REQUEST['payment_methods_id'];
                $o_cat_id = isset($_REQUEST["cat_id"]) ? (int) $_REQUEST["cat_id"] : 0;
                $o_site_id = isset($_REQUEST["site_id"]) ? (int) $_REQUEST["site_id"] : 0;

                $_SESSION['order_lists2_param']['start_date'] = $o_start_date;
                $_SESSION['order_lists2_param']['end_date'] = $o_end_date;
                $_SESSION['order_lists2_param']['follow_up_date'] = $o_follow_up_date;
                $_SESSION['order_lists2_param']['date_type'] = $o_date_type;
                $_SESSION['order_lists2_param']['order_status'] = $o_order_status;
                $_SESSION['order_lists2_param']["order_sub_status_2"] = $o_sub_status[2];
                $_SESSION['order_lists2_param']["order_sub_status_3"] = $o_sub_status[3];
                $_SESSION['order_lists2_param']['payment_gateways_id'] = $o_payment_gateways;
                $_SESSION['order_lists2_param']['payment_methods_id'] = $o_payment_method;
                $_SESSION['order_lists2_param']["cat_id"] = $o_cat_id;
                $_SESSION['order_lists2_param']["site_id"] = $o_site_id;
            }

            #pre-define variable
            $where_sql_str = " 1 ";
            $having_sql_str = " 1 ";
            $report_array = array();
            $bibit_id_array = array();
            $customer_group_array = array();
            $aft_group_array = array();

            #pre-load variable
            $flag_icon_array = array(
                'flagOrange' => array('on' => 'icon_status_orange_yes.gif', 'off' => 'icon_status_no.gif'),
                'flagPink' => array('on' => 'icon_status_pink_yes.gif', 'off' => 'icon_status_no.gif'),
                'flagCyan' => array('on' => 'icon_status_cyan_yes.gif', 'off' => 'icon_status_no.gif'),
                'flagRed' => array('on' => 'icon_status_red_yes.gif', 'off' => 'icon_status_no.gif')
            );

            $customer_group_select_sql = "	SELECT customers_groups_id, customers_groups_name 
  											FROM " . TABLE_CUSTOMERS_GROUPS;
            $customer_group_result_sql = tep_db_query($customer_group_select_sql, 'read_db_link');
            while ($customer_group_row = tep_db_fetch_array($customer_group_result_sql)) {
                $customer_group_array[$customer_group_row['customers_groups_id']] = $customer_group_row['customers_groups_name'];
            }

            $aft_group_select_sql = "	SELECT customers_aft_groups_id, customers_aft_groups_name 
                                        FROM " . TABLE_CUSTOMERS_AFT_GROUPS;
            $aft_group_result_sql = tep_db_query($aft_group_select_sql, 'read_db_link');
            while ($aft_group_row = tep_db_fetch_array($aft_group_result_sql)) {
                $aft_group_array[$aft_group_row['customers_aft_groups_id']] = $aft_group_row['customers_aft_groups_name'];
            }

            #SQL Filter
            if (count($o_order_status)) {
                if (in_array(2, $o_order_status)) {
                    if (!count($o_sub_status[2])) {
                        unset($_SESSION['order_lists2_param']["order_status"][array_search(2, $o_order_status)]);
                        unset($o_sub_status[2]);
                    }
                } else {
                    unset($o_sub_status[2]);
                }

                if (in_array(3, $o_order_status)) {
                    if (!count($o_sub_status[3])) {
                        unset($_SESSION['order_lists2_param']["order_status"][array_search(3, $o_order_status)]);
                        unset($o_sub_status[3]);
                    }
                } else {
                    unset($o_sub_status[3]);
                }

                if (count($o_order_status)) {
                    $where_sql_str .= " AND o.orders_status IN (" . implode(", ", $o_order_status) . ")";
                }

                if (count($o_sub_status)) {
                    $having_sql_str .= " AND (o.orders_status NOT IN (2, 3)";
                    $where_sql_str .= " AND (o.orders_status NOT IN (2, 3)";

                    foreach ($o_sub_status AS $order_status => $order_sub_status_array) {
                        $orders_status_d = false;
                        $orders_status_c = false;

                        foreach ($order_sub_status_array AS $order_sub_status) {
                            switch ($order_sub_status) {
                                case 'UD': // Completed order does not has Undelivered part
                                    $where_sql_str .= " OR (orders_status = {$order_status})";
                                    $having_sql_str .= " OR (orders_status = {$order_status} 
                                                        AND ((full_amount - (delivered_amount+refunded_amount+reversed_amount) > 0)
                                                                OR (products_quantity - (products_good_delivered_quantity+products_canceled_quantity+products_reversed_quantity) > 0)))";
                                    break;
                                case 'D':
                                    $orders_status_d = true;
                                    $having_sql_str .= " OR (orders_status = {$order_status} AND orders_cb_status is NULL
                                                        AND (products_good_delivered_quantity > 0 OR delivered_amount > 0))";
                                    break;
                                case 'C':
                                    $orders_status_c = true;
                                    $having_sql_str .= " OR (orders_status = {$order_status} AND orders_cb_status is NULL
                                                        AND (products_canceled_quantity > 0 OR refunded_amount > 0))";
                                    break;
                                case 'RV-WIN':
                                    $orders_cb_status_array[] = 1;
                                    break;
                                case 'RV-LOST':
                                    $orders_cb_status_array[] = 2;
                                    break;
                                case 'RV-RESOLVED':
                                    $orders_cb_status_array[] = 3;
                                    break;
                            }
                        }//inner loop

                        if ($orders_status_c || $orders_status_d) {
                            $where_sql_str .= " OR (orders_status = {$order_status} AND orders_cb_status is NULL)";
                        }

                        if (count($orders_cb_status_array)) {
                            $where_sql_str .= " OR (orders_status = {$order_status} 
                                                AND orders_cb_status IN (" . implode(", ", $orders_cb_status_array) . "))";
                            $having_sql_str .= " OR (orders_status = {$order_status} 
                                                AND orders_cb_status IN (" . implode(", ", $orders_cb_status_array) . ")
                                                AND (products_reversed_quantity > 0 OR reversed_amount > 0))";
                        }
                        unset($orders_cb_status_array);
                    }//outer loop
                    $where_sql_str .= ")";
                    $having_sql_str .= ")";
                }
            }

            if (tep_not_null($o_start_date)) {
                if (strpos($o_start_date, ':') !== false) {
                    $startDateObj = explode(' ', trim($o_start_date));
                    list($yr, $mth, $day) = explode('-', $startDateObj[0]);
                    list($hr, $min) = explode(':', $startDateObj[1]);
                    $where_sql_str .= " AND (oss.latest_date >= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "')";
                } else {
                    list($yr, $mth, $day) = explode('-', trim($o_start_date));
                    $where_sql_str .= " AND (oss.latest_date >= '" . date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day, $yr)) . "')";
                }
            }

            if (tep_not_null($o_end_date)) {
                if (strpos($o_end_date, ':') !== false) {
                    $endDateObj = explode(' ', trim($o_end_date));
                    list($yr, $mth, $day) = explode('-', $endDateObj[0]);
                    list($hr, $min) = explode(':', $endDateObj[1]);
                    $where_sql_str .= " AND (oss.latest_date <= '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 59, $mth, $day, $yr)) . "')";
                } else {
                    list($yr, $mth, $day) = explode('-', trim($o_end_date));
                    $where_sql_str .= " AND (oss.latest_date <= '" . date("Y-m-d H:i:s", mktime(23, 59, 59, $mth, $day, $yr)) . "')";
                }
            }

            if (tep_not_null($o_follow_up_date)) {
                if (strpos($o_follow_up_date, ':') !== false) {
                    $dateObj = explode(' ', trim($o_follow_up_date));
                    list($yr, $mth, $day) = explode('-', $dateObj[0]);
                    list($hr, $min) = explode(':', $dateObj[1]);
                    $where_sql_str .= " AND (o.orders_follow_up_datetime = '" . date("Y-m-d H:i:s", mktime((int) $hr, (int) $min, 0, $mth, $day, $yr)) . "')";
                } else {
                    list($yr, $mth, $day) = explode('-', trim($o_follow_up_date));
                    $where_sql_str .= " AND (o.orders_follow_up_datetime >= '" . date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day, $yr)) . "' AND o.orders_follow_up_datetime <= '" . date("Y-m-d H:i:s", mktime(23, 59, 59, $mth, $day, $yr)) . "')";
                }
            }

            if (!in_array("any", $o_payment_gateways)) {
                $payment_method_where_str = '';
                $payment_gateway_where_str = '';

                if (in_array("OGM_CREDITS", $o_payment_gateways)) {
                    $search_index_key = array_search('OGM_CREDITS', $o_payment_gateways);
                    $o_payment_method = array_push($o_payment_method, 0);
                    unset($o_payment_gateways[$search_index_key]);
                }

                if (count($o_payment_method)) {
                    $payment_method_where_str = "o.payment_methods_id IN ('" . implode("', '", $o_payment_method) . "')";
                }

                if (count($o_payment_gateways)) {
                    $payment_gateway_where_str = "o.payment_methods_parent_id IN ('" . implode("', '", $o_payment_gateways) . "')";
                }

                if ($payment_method_where_str != '' AND $payment_gateway_where_str != '') {
                    $where_sql_str .= " AND (" . $payment_method_where_str . " OR " . $payment_gateway_where_str . ")";
                } else if ($payment_method_where_str != '') {
                    $where_sql_str .= " AND " . $payment_method_where_str;
                } else if ($payment_gateway_where_str != '') {
                    $where_sql_str .= " AND " . $payment_gateway_where_str;
                }
            }


            $orders_select_str = '';
            if ($o_cat_id !== '') {
                if (is_numeric($o_cat_id) && $o_cat_id > 0) {
                    if (tep_check_cat_tree_permissions(FILENAME_ORDERS, $o_cat_id) == 1) {
                        $cat_parent_path = tep_get_categories_parent_path($o_cat_id);
                        if (empty($cat_parent_path)) {
                            $cat_parent_path = '_';
                        }

                        $category_path = str_replace('_', '\_', $cat_parent_path . $o_cat_id . '_');

                        $orders_select_str .= " left join " . TABLE_PRODUCTS . " as p on (op.products_id=p.products_id) ";
                        $where_sql_str .= " AND p.products_cat_id_path LIKE '" . $category_path . "%' ";
                    }
                }
            }

            if (tep_not_null($o_site_id)) {
                switch ($o_site_id) {
                    case '1': // OffGamers Orders
                        $where_sql_str .= " AND oei.orders_extra_info_value != '5' ";
                        break;

                    case '2': // G2G Orders
                        $where_sql_str .= " AND oei.orders_extra_info_value = '5' ";
                        break;

                    default: // All Orders
                        $where_sql_str .= "";
                        break;
                }
            }


            $bibit_method_id_select_sql = "	SELECT payment_methods_id 
                                                FROM " . TABLE_PAYMENT_METHODS . " 
                                                WHERE payment_methods_filename = 'bibit.php'";
            $bibit_method_result_sql = tep_db_query($bibit_method_id_select_sql, 'read_db_link');
            while ($bibit_method_row = tep_db_fetch_array($bibit_method_result_sql)) {
                $bibit_id_array[] = $bibit_method_row['payment_methods_id'];
            }

            $select_sql_str = "	SELECT 	o.orders_id, o.orders_status, o.orders_tag_ids, o.customers_name, o.payment_methods_parent_id,
                                    o.orders_cb_status, DATE_FORMAT(o.date_purchased, '" . $datetime_format . "') as date_purchased, o.customers_country,
                                    op.products_name, p2.products_cat_path,
                                    SUM(op.products_good_delivered_price) as delivered_amount,
                                    SUM(op.products_canceled_price) as refunded_amount,
                                    SUM(op.products_reversed_price) as reversed_amount,
                                    SUM(op.final_price*op.products_quantity) as full_amount,
                                    SUM(op.products_quantity) as products_quantity, 
                                    SUM(op.products_good_delivered_quantity) as products_good_delivered_quantity, 
                                    SUM(op.products_canceled_quantity) as products_canceled_quantity, 
                                    SUM(op.products_reversed_quantity) as products_reversed_quantity,
                                    DATE_FORMAT(oss.latest_date, '" . $datetime_format . "') as latest_date, 
                                    oss.changed_by, oei.orders_extra_info_value,
                                    c.customers_flag, c.customers_groups_id, c.customers_aft_groups_id, c.customers_id
                                FROM " . TABLE_ORDERS . " AS o 
                                INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
                                    ON (op.orders_id = o.orders_id AND op.products_bundle_id=0 AND op.orders_products_is_compensate=0)
                                INNER JOIN " . TABLE_ORDERS_STATUS_STAT . " AS oss 
                                    ON (oss.orders_status_id = " . $o_date_type . " AND o.orders_id = oss.orders_id)
                                INNER JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei 
                                    ON (o.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'site_id')
                                LEFT JOIN " . TABLE_CUSTOMERS . " AS c 
                                    ON o.customers_id = c.customers_id
                                LEFT JOIN " . TABLE_PRODUCTS . " AS p2 
                                    ON op.products_id = p2.products_id 
                            " . $orders_select_str . "
                                WHERE " . $where_sql_str . "
                                GROUP BY op.orders_id
                                HAVING " . $having_sql_str . "
                                ORDER BY op.orders_id desc";

            if ($show_records != "ALL") {
                $orders_split_object = new splitPageResults($page, $show_records, $select_sql_str, $orders_select_sql_numrows, true);
            }

            $orders_result_sql = tep_db_query($select_sql_str, 'read_db_link');
            while ($row = tep_db_fetch_array($orders_result_sql)) {
                $tags_array = array();
                $payment_status = "";
                $order_rollbacked = "";

                $order_rollbacked_select_sql = "SELECT orders_id 
                                                FROM " . TABLE_ORDERS_STATUS_STAT . " 
                                                WHERE orders_id = " . (int) $row['orders_id'] . "
                                                        AND ((occurrence > 1 AND orders_status_id IN (2)) OR orders_status_id = 8)
                                                LIMIT 1";
                $order_rollbacked_result_sql = tep_db_query($order_rollbacked_select_sql, 'read_db_link');
                if ($order_rollbacked_row = tep_db_fetch_array($order_rollbacked_result_sql)) {
                    $order_rollbacked = "*";
                }
                unset($order_rollbacked_row);

                if (tep_not_null($row["orders_tag_ids"])) {
                    $assigned_tag_select_sql = "	SELECT orders_tag_name 
                                                FROM " . TABLE_ORDERS_TAG . " 
                                                WHERE FIND_IN_SET(orders_tag_id, '" . $row["orders_tag_ids"] . "') 
                                                    AND filename='" . FILENAME_STATS_ORDERS_TRACKING . "'";
                    $assigned_tag_result_sql = tep_db_query($assigned_tag_select_sql, 'read_db_link');
                    while ($assigned_tag_row = tep_db_fetch_array($assigned_tag_result_sql)) {
                        $tags_array[] = $assigned_tag_row["orders_tag_name"];
                    }
                    unset($assigned_tag_row);
                }

                if (in_array($row['payment_methods_parent_id'], $bibit_id_array)) {
                    $bibit_status_select_sql = "SELECT bb_err_no, bb_err_txt, bb_status 
                                                FROM " . TABLE_BIBIT_PAYMENT_STATUS_HISTORY . " 
                                                WHERE orders_id = " . (int) $row['orders_id'] . "
                                                ORDER BY bb_payment_status_history_id DESC
                                                LIMIT 1";
                    $bibit_status_result_sql = tep_db_query($bibit_status_select_sql, 'read_db_link');
                    if ($bibit_status_row = tep_db_fetch_array($bibit_status_result_sql)) {
                        if ($bibit_status_row['bb_err_no'] === '0') {
                            $payment_status = $bibit_status_row['bb_err_txt'] . ' - ' . $bibit_status_row['bb_status'];
                        } else {
                            $payment_status = $bibit_status_row['bb_err_no'] . ' - ' . $bibit_status_row['bb_err_txt'];
                        }

                        unset($bibit_status_row);
                    }
                }

                $row['orders_tag_name'] = implode(",", $tags_array);
                $row['orders_status_name'] = $status_options[$row['orders_status']];
                $row['customers_flag'] = explode(",", $row['customers_flag']);
                $row['customers_groups_name'] = isset($customer_group_array[$row['customers_groups_id']]) ? $customer_group_array[$row['customers_groups_id']] : TEXT_OPTION_NOT_APPLICABLE;
                $row['aft_groups_name'] = isset($aft_group_array[$row['customers_aft_groups_id']]) ? $aft_group_array[$row['customers_aft_groups_id']] : TEXT_OPTION_NOT_APPLICABLE;
                $row['payment_status'] = $payment_status;
                $row['order_rollbacked'] = $order_rollbacked;
                $report_array[] = $row;

                unset($row);
                unset($tags_array);
            }//end while

            unset($bibit_id_array);

            if ($action == 'export_report') {
                if (count($report_array)) {
                    $report_flags_array = tep_get_user_flags();

                    $export_csv_data = TABLE_HEADING_ACTION_DATE . "," .
                            TABLE_HEADING_ORDER_DATE . "," .
                            TABLE_HEADING_ORDER_ID . "," .
                            TABLE_HEADING_ORDER_CATEGORY_PATH . "," .
                            TABLE_HEADING_ORDER_PRODUCT . "," .
                            TABLE_HEADING_ORDER_SITE . "," .
                            strip_tags(TABLE_HEADING_ORDER_STATUS) . "," .
                            TABLE_HEADING_ACTION_BY . "," .
                            TABLE_HEADING_CUSTOMER_ID . "," .
                            TABLE_HEADING_CUSTOMER_NAME . "," .
                            TABLE_HEADING_CUSTOMER_COUNTRY . "," .
                            TABLE_HEADING_FLAG . "," .
                            TABLE_HEADING_CUSTOMER_GROUP . "," .
                            TABLE_HEADING_AFT_GROUP . "," .
                            TABLE_HEADING_ORDER_TAG . "," .
                            TABLE_HEADING_PAYMENT_STATUS . "," .
                            TABLE_HEADING_PRICE_AMOUNT . "," .
                            TABLE_HEADING_DELIVERED_VALUE . "," .
                            TABLE_HEADING_REFUNDED_VALUE . "," .
                            TABLE_HEADING_REVERSED_VALUE . "," .
                            "\n";

                    foreach ($report_array as $res) {
                        $report_flag = "";
                        foreach ($report_flags_array as $flag_id => $flag_info) {
                            if (in_array($flag_id, $res['customers_flag'])) {
                                $report_flag .= $report_flag == '' ? $flag_info['user_flags_name'] : ' ' . $flag_info['user_flags_name'];
                            }
                        }

                        $tmp_cvs_data_array[] = $res['latest_date'];
                        $tmp_cvs_data_array[] = $res['date_purchased'];
                        $tmp_cvs_data_array[] = $res['orders_id'];
                        $tmp_cvs_data_array[] = $res['products_cat_path'];
                        $tmp_cvs_data_array[] = $res['products_name'];
                        $tmp_cvs_data_array[] = (($res['orders_extra_info_value'] == 5) ? 'G2G' : 'OffGamers');
                        $tmp_cvs_data_array[] = $res['orders_status_name'];
                        $tmp_cvs_data_array[] = $res['changed_by'];
                        $tmp_cvs_data_array[] = $res['customers_id'];
                        $tmp_cvs_data_array[] = $res['customers_name'];
                        $tmp_cvs_data_array[] = $res['customers_country'];
                        $tmp_cvs_data_array[] = $report_flag;
                        $tmp_cvs_data_array[] = $res['customers_groups_name'];
                        $tmp_cvs_data_array[] = $res['aft_groups_name'];
                        $tmp_cvs_data_array[] = $res['orders_tag_name'];
                        $tmp_cvs_data_array[] = $res['payment_status'];
                        $tmp_cvs_data_array[] = $currencies->format($res['full_amount']);
                        $tmp_cvs_data_array[] = $currencies->format($res['delivered_amount']);
                        $tmp_cvs_data_array[] = $currencies->format($res['refunded_amount']);
                        $tmp_cvs_data_array[] = $currencies->format($res['reversed_amount']);

                        for ($i = 0; $i < count($res); $i++) {
                            $tmp_cvs_data_array[$i] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $tmp_cvs_data_array[$i]) . '"';
                        }

                        $export_csv_data .= implode(",", $tmp_cvs_data_array) . "\n";
                        unset($tmp_cvs_data_array);
                    }

                    if (($freport = @fopen($file_location, "w")) !== false) {
                        fwrite($freport, $export_csv_data);
                        fclose($freport);
                    }

                    unset($report_array);
                    tep_redirect(tep_href_link(FILENAME_ORDERS_LISTING . '?action=do_export'));
                }
                $messageStack->add_session('No Record Found.', 'error');
                tep_redirect(tep_href_link(FILENAME_ORDERS_LISTING));
            }
            break;
        case "reset_session":
            unset($_SESSION['order_lists2_param']);
            tep_redirect(tep_href_link(FILENAME_ORDERS_LISTING));
            break;
    }
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/jquery.tree.css">
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="includes/javascript/xmlhttp.js"></script>
        <script language="javascript" src="includes/javascript/orders.js"></script>
        <script language="javascript" src="includes/general.js"></script>
        <script language="JavaScript" src="includes/javascript/jquery.js"></script>
        <script type="text/javascript" src="includes/javascript/jquery.tree.js"></script>
        <script language="javascript" src="includes/javascript/modal_win.js"></script>
        <style type="text/css">
            span.delivered_amount_color {color:	#228B22;}
            span.refunded_amount_color {color:blue;}
            span.reversed_amount_color {color:red;}
        </style>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?= BOX_WIDTH ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <!-- body_text //-->
                        <?
                        if ($_REQUEST['action'] == 'show_report') {
                            $report_flags_array = tep_get_user_flags();
                            ?>
                            <tr>
                                <td valign="top" class="pageHeading"><?= HEADING_TITLE ?></td>
                                <td class="smallText" align="right" valign="top">&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <table border="0" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
                                        <tr>
                                            <td width="4%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_ACTION_DATE ?></td>
                                            <td width="4%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_ORDER_DATE ?></td>
                                            <td width="4%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_ORDER_ID ?></td>
                                            <td width="1%" align="left" class="ordersBoxHeading"></td>
                                            <td width="6%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_ORDER_PRODUCT ?></td>
                                            <td width="4%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_ORDER_SITE ?></td>
                                            <td width="5%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_ORDER_STATUS ?></td>
                                            <td width="4%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_ACTION_BY ?></td>
                                            <td width="5%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_CUSTOMER_ID ?></td>
                                            <td width="9%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_CUSTOMER_NAME ?></td>
                                            <td width="5%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_CUSTOMER_COUNTRY ?></td>
                                            <td width="4%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_FLAG ?></td>
                                            <td width="6%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_CUSTOMER_GROUP ?></td>
                                            <td width="6%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_AFT_GROUP ?></td>
                                            <td align="left" class="ordersBoxHeading"><?= TABLE_HEADING_ORDER_TAG ?></td>
                                            <td width="11%" align="left" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_PAYMENT_STATUS ?></td>
                                            <td width="6%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_PRICE_AMOUNT ?></td>
                                            <td width="6%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_DELIVERED_VALUE ?></td>
                                            <td width="6%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_REFUNDED_VALUE ?></td>
                                            <td width="6%" align="left" class="ordersBoxHeading"><?= TABLE_HEADING_REVERSED_VALUE ?></td>
                                        </tr>
                                        <?
                                        foreach ($report_array as $row => $row_array) {
                                            $report_flag = '';
                                            $cust_name_style = '';
                                            $order_useremail = '';
                                            $cust_member_grp_name = '';

                                            foreach ($report_flags_array as $flag_id => $flag_info) {
                                                if (in_array($flag_id, $row_array['customers_flag'])) {
                                                    $flag_icon_img_src = $flag_icon_array[$flag_info['user_flags_css_style']]['on'];
                                                    $cust_name_style = $flag_info['user_flags_css_style'];
                                                } else {
                                                    $flag_icon_img_src = $flag_icon_array[$flag_info['user_flags_css_style']]['off'];
                                                }
                                                $report_flag .= tep_image(DIR_WS_IMAGES . $flag_icon_img_src, $flag_info['user_flags_name'], 11, 11) . '&nbsp;';
                                            }

                                            $row_style = ($row % 2) ? 'ordersListingEven' : 'ordersListingOdd';
                                            $report_customer = '<span class="' . $cust_name_style . '">' . $row_array['customers_name'] . '<span>';
                                            ?>	
                                            <tr class="<?= $row_style ?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?= $row ?>')" 
                                                onmouseout="showOutEffect(this, '<?= $row_style ?>', '<?= $row ?>')" 
                                                onclick="showClicked(this, '<?= $row_style ?>', '<?= $row ?>')">
                                                <td class="ordersRecords"><?= $row_array['latest_date'] ?></td>
                                                <td class="ordersRecords"><?= $row_array['date_purchased'] ?></td>
                                                <td class="ordersRecords" nowrap>
                                                    <a href="<?= tep_href_link("orders.php", 'oID=' . $row_array['orders_id'] . '&action=edit', 'NONSSL') ?>" target="_blank">
                                                        <?= $row_array['orders_id'] . $row_array['order_rollbacked'] ?>
                                                    </a>
                                                </td>
                                                <td class="ordersRecords">
                                                    <div style="float: left; width:10px; height:12px; border: 1px solid black; margin-right: 2px; 
                                                         background-color:<?= $payment_gateways_array['display_colour'][$row_array['payment_methods_parent_id']] ?>; 
                                                         font-size:9px"/>
                                                </td>
                                                <td class="ordersRecords"><?php echo $row_array["products_name"]; ?></td>
                                                <td class="ordersRecords"><?= (($row_array['orders_extra_info_value'] == 5) ? 'G2G' : 'OffGamers') ?></td>
                                                <td class="ordersRecords"><?= $row_array['orders_status_name'] ?></td>
                                                <td class="ordersRecords"><?= $row_array['changed_by'] ?></td>
                                                <td class="ordersRecords">
                                                    <a href="<?= tep_href_link("customers.php", 'cID=' . $row_array['customers_id'] . '&action=edit', 'NONSSL') ?>" target="_blank">
                                                        <?= $row_array['customers_id'] ?>
                                                    </a>
                                                </td>
                                                <td class="ordersRecords"><?= $report_customer ?></td>
                                                <td class="ordersRecords"><?= $row_array['customers_country'] ?></td>
                                                <td class="ordersRecords" align="left" nowrap><?= $report_flag ?></td>
                                                <td class="ordersRecords"><?= $row_array['customers_groups_name'] ?></td>
                                                <td class="ordersRecords"><?= $row_array['aft_groups_name'] ?></td>
                                                <td class="ordersRecords"><span class="greenIndicator"><?= $row_array['orders_tag_name'] ?></span></td>
                                                <td class="ordersRecords" valign="top"><?= $row_array['payment_status'] ?></td>
                                                <td class="ordersRecords" align="left"><?= $currencies->format($row_array['full_amount'], false) ?></td>
                                                <td class="ordersRecords" align="left"><span class="delivered_amount_color"><?= $currencies->format($row_array['delivered_amount'], false) ?></span></td>
                                                <td class="ordersRecords" align="left"><span class="refunded_amount_color"><?= $currencies->format($row_array['refunded_amount'], false) ?></span></td>
                                                <td class="ordersRecords" align="left"><span class="reversed_amount_color"><?= $currencies->format($row_array['reversed_amount'], false) ?></span></td>
                                            </tr>
                                        <? } ?>
                                        <tr>
                                            <td colspan="14"></td>
                                        </tr>
                                        <tr>
                                            <td colspan="14">
                                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                    <tr>
                                                        <td class="smallText" align="right" valign="top" colspan="2">

                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="smallText" valign="top">
                                                            <?
                                                            echo $show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_ORDERS, tep_db_num_rows($orders_result_sql) > 0 ? "1" : "0", tep_db_num_rows($orders_result_sql), tep_db_num_rows($orders_result_sql)) :
                                                                    $orders_split_object->display_count($orders_select_sql_numrows, $show_records, $page, TEXT_DISPLAY_NUMBER_OF_ORDERS);
                                                            ?>
                                                        </td>
                                                        <td class="smallText" align="right">
                                                            <?
                                                            echo $show_records == "ALL" ? "Page 1 of 1" :
                                                                    $orders_split_object->display_links($orders_select_sql_numrows, $show_records, MAX_DISPLAY_PAGE_LINKS, $page, tep_get_all_get_params(array('page', 'cont')) . "&cont=1&action=show_report", 'page');
                                                            ?>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <script language="javascript"><!--
                            var isProcessing = false;
                                function showOverEffect(object, class_name, extra_row) {
                                    rowOverEffect(object, class_name);
                                    var rowObjArray = extra_row.split('##');
                                    for (var i = 0; i < rowObjArray.length; i++) {
                                        if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                                            rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
                                        }
                                    }
                                }

                                function showOutEffect(object, class_name, extra_row) {
                                    rowOutEffect(object, class_name);
                                    var rowObjArray = extra_row.split('##');
                                    for (var i = 0; i < rowObjArray.length; i++) {
                                        if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                                            rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
                                        }
                                    }
                                }

                                function showClicked(object, class_name, extra_row) {
                                    rowClicked(object, class_name);
                                    var rowObjArray = extra_row.split('##');
                                    for (var i = 0; i < rowObjArray.length; i++) {
                                        if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
                                            rowClicked(document.getElementById(rowObjArray[i]), class_name);
                                        }
                                    }
                                }
                                //-->
                            </script>
                            <?
                        } else {
                            ?>
                            <tr>
                                <td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?= DIR_WS_INCLUDES ?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
                            </tr>
                            <tr>
                                <td>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td class="pageHeading" valign="top"><?= HEADING_INPUT_TITLE ?></td>
                                            <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                            <td class="main" align="right">&nbsp;</td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <?
                            echo tep_draw_form('order_list2', FILENAME_ORDERS_LISTING, tep_get_all_get_params(array('action')), 'post', '');
                            echo tep_draw_hidden_field('action', '', 'id=action');
                            ?>
                            <tr>
                                <td>
                                    <table border="0" width="100%" cellspacing="2" cellpadding="0">
                                        <tr>
                                            <td class="main" width="12%"><?= ENTRY_HEADING_ORDER_START_DATE ?></td>
                                            <td class="main">
                                                <table border="0" cellspacing="2" cellpadding="0">
                                                    <tr>
                                                        <td class="main" valign="top" nowrap><?= tep_draw_input_field('start_date', $_SESSION['order_lists2_param']["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_list2.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_list2.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                                        <td class="main" width="5%">&nbsp;</td>
                                                        <td class="main" valign="top" nowrap><?= ENTRY_HEADING_ORDER_END_DATE ?></td>
                                                        <td class="main" width="1%">&nbsp;</td>
                                                        <td class="main" valign="top" nowrap><?= tep_draw_input_field('end_date', $_SESSION['order_lists2_param']["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_list2.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_list2.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                                        <td class="main" width="5%">&nbsp;</td>
                                                        <td class="main" valign="top" nowrap><?= ENTRY_DATE_TYPE ?></td>
                                                        <td class="main" width="1%">&nbsp;</td>
                                                        <td class="main" valign="top" nowrap>
                                                            <?
                                                            /* -- order status : data retrieve -- */
                                                            $status_options = array();
                                                            $order_status_select_sql = "SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id='" . $languages_id . "' ORDER BY orders_status_sort_order";
                                                            $order_status_result_sql = tep_db_query($order_status_select_sql, 'read_db_link');

                                                            while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
                                                                $status_options[$order_status_row["orders_status_id"]] = $order_status_row["orders_status_name"];
                                                            }

                                                            $payment_status_select_sql = "	SELECT orders_status_id, orders_status_name 
									FROM " . TABLE_ORDERS_STATUS . " 
									WHERE language_id=" . (int) $languages_id . " 
									ORDER BY orders_status_sort_order";
                                                            $payment_status_result_sql = tep_db_query($payment_status_select_sql, 'read_db_link');
                                                            while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
                                                                $date_type_array[] = array('id' => $payment_status_row["orders_status_id"], 'text' => sprintf(TEXT_LAST_STATUS_DATE, $payment_status_row["orders_status_name"]));
                                                            }

                                                            echo tep_draw_pull_down_menu('date_type', $date_type_array, $_SESSION['order_lists2_param']["date_type"], ' id="date_type" ');
                                                            unset($date_type_array);
                                                            ?>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                        </tr>
                                        <tr>
                                            <td class="main"><?= ENTRY_HEADING_ORDER_FOLLOW_UP_DATE ?></td>
                                            <td class="main"><?= tep_draw_input_field('follow_up_date', $_SESSION['order_lists2_param']["follow_up_date"], 'id="follow_up_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_list2.follow_up_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_list2.follow_up_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>' ?></td>
                                        </tr>
                                        <tr>
                                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" width="12%"><?php echo ENTRY_CATEGORY; ?></td>
                                            <td class="main">
                                                <?php
                                                echo tep_draw_input_field('cat_id', $_SESSION['order_lists2_param']["cat_id"], ' id="cat_id" size="15" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"');
                                                echo '&nbsp;<a href="javascript:openDGDialog(\'' . tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=category_cache&fieldname=cat_id') . '\', 600, 250, \'\');">(Category List)</a>';
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" width="12%"><?php echo ENTRY_ORDER_SITE; ?></td>
                                            <td class="main">
                                                <?php
                                                $site_id_array = array(
                                                    array('id' => '1', 'text' => 'OffGamers'),
                                                );
                                                echo tep_draw_pull_down_menu('site_id', $site_id_array, $_SESSION['order_lists2_param']["site_id"], 'id="site_id"');
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
                                        </tr>
                                        <tr>
                                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" width="12%" valign="top"><?= ENTRY_HEADING_ORDER_STATUS ?></td>
                                            <td>
                                                <table border="0" cellspacing="0" cellpadding="2">
                                                    <tr>
                                                        <?
                                                        $order_status_display_str = '';

                                                        foreach ($status_options as $id => $title) {
                                                            $status_selected = isset($_SESSION['order_lists2_param']) ? (is_array($_SESSION['order_lists2_param']["order_status"]) && in_array($id, $_SESSION['order_lists2_param']["order_status"]) ? true : false) : ($id == "7" || $id == "2" || $id == "3" ? true : false);
                                                            ?>	
                                                            <td class="main" valign="top">
                                                                <?= tep_draw_checkbox_field('order_status[]', $id, $status_selected, '', 'id="order_status_' . $id . '" onClick="verify_status_selection(this);"') ?>
                                                            </td>
                                                            <td class="main" valign="top">
                                                                <?
                                                                if ($id == "2" || $id == "3") {
                                                                    ?>
                                                                    <fieldset class="selectedFieldSet">
                                                                        <legend align=left class=SectionHead><?= $title ?></legend>
                                                                        <table border="0" cellspacing="0" cellpadding="0">
                                                                            <?
                                                                            foreach ($orders_amt_breakdown_array[$id] as $sub_status_id => $sub_status_array) {
                                                                                $sub_status_id = $sub_status_array['id'];
                                                                                $sub_status_title = $sub_status_array['text'];
                                                                                $sub_status_selected = isset($_SESSION['order_lists2_param']) ? (is_array($_SESSION['order_lists2_param']['order_sub_status_' . $id]) && in_array($sub_status_id, $_SESSION['order_lists2_param']['order_sub_status_' . $id]) ? true : false) : ($sub_status_id == 'UD' || $sub_status_id == 'D' || $sub_status_id == 'RV-WIN' || $sub_status_id == 'RV-RESOLVED' ? true : false);
                                                                                ?>
                                                                                <tr>
                                                                                    <td class="smallText" valign="top">
                                                                                        <?= tep_draw_checkbox_field('order_sub_status_' . $id . '[]', $sub_status_id, $sub_status_selected, '', $status_selected ? '' : 'disabled') ?>
                                                                                    </td>
                                                                                    <td class="smallText"><?= $sub_status_title ?></td>
                                                                                </tr>
                                                                                <?
                                                                            }
                                                                            ?>
                                                                        </table>
                                                                    </fieldset>
                                                                    <?
                                                                } else {
                                                                    echo $title;
                                                                }
                                                                echo '</td>';
                                                            }
                                                            ?>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                        </tr>
                                        <tr>
                                            <td class="main" width="12%" valign="top"><?= ENTRY_HEADING_PAYMENT_METHOD ?></td>
                                            <td class="main">
                                                <ul class="myTree">
                                                    <li style="-moz-user-select: none;" class="treeItem" id="<?= $payment_gateways_id ?>">
                                                        <span class="textHolder payment_gateways">
                                                            &nbsp;&nbsp;&nbsp;
                                                            <input type="checkbox" class="pg_any" name="payment_gateways_id[]" value="any" <?= ((!in_array('any', (array) $_SESSION['order_lists2_param']['payment_gateways_id']) && count($_SESSION['order_lists2_param']['payment_gateways_id'])) || count($_SESSION['order_lists2_param']['payment_methods_id'])) ? '' : ' checked ' ?>>
                                                            <i>Any</i>
                                                        </span>
                                                    </li>
                                                    <?
                                                    foreach ($payment_gateways_array['method'] as $payment_gateways_id => $payment_gateways_title) {
                                                        ?>
                                                        <li style="-moz-user-select: none;" class="treeItem" id="<?= $payment_gateways_id ?>">
                                                            <?
                                                            if (isset($payment_methods_array[$payment_gateways_id]) && count($payment_methods_array[$payment_gateways_id])) {
                                                                $pm_checked_flag = false;

                                                                if (isset($_SESSION['order_lists2_param']['payment_methods_id']) && count($_SESSION['order_lists2_param']['payment_methods_id'])) {
                                                                    foreach ($payment_methods_array[$payment_gateways_id] as $check_pm_id => $check_pm_text) {
                                                                        if (in_array($check_pm_id, $_SESSION['order_lists2_param']['payment_methods_id'])) {
                                                                            $pm_checked_flag = true;
                                                                            break;
                                                                        }
                                                                    }
                                                                }

                                                                if ($pm_checked_flag) {
                                                                    ?>
                                                                    <img src="images/icon-collapse-small.gif" class="expandImage" width="9" height="7">
                                                                    <span class="textHolder payment_gateways">
                                                                        <input type="checkbox" name="payment_gateways_id[]" value="<?= $payment_gateways_id ?>" <?= (count($_SESSION['order_lists2_param']['payment_gateways_id']) && in_array($payment_gateways_id, $_SESSION['order_lists2_param']['payment_gateways_id']) ? ' checked ' : '') ?>>
                                                                        <?= $payment_gateways_title ?>
                                                                    </span>
                                                                    <ul style="display: display;" class="open">
                                                                        <?
                                                                    } else {
                                                                        ?>
                                                                        <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                                                        <span class="textHolder payment_gateways">
                                                                            <input type="checkbox" name="payment_gateways_id[]" value="<?= $payment_gateways_id ?>" <?= (count($_SESSION['order_lists2_param']['payment_gateways_id']) && in_array($payment_gateways_id, $_SESSION['order_lists2_param']['payment_gateways_id']) ? ' checked ' : '') ?>>
                                                                            <?= $payment_gateways_title ?>
                                                                        </span>
                                                                        <ul style="display: none;">
                                                                            <?
                                                                        }
                                                                        foreach ($payment_methods_array[$payment_gateways_id] as $payment_methods_id => $payment_methods_title) {
                                                                            ?>
                                                                            <li style="-moz-user-select: none;" class="treeItem" id="<?= $payment_methods_id ?>">&nbsp;&nbsp;
                                                                                <span class="textHolder payment_methods">
                                                                                    <input type="checkbox" name="payment_methods_id[]" value="<?= $payment_methods_id ?>" <?= (count($_SESSION['order_lists2_param']['payment_methods_id']) && in_array($payment_methods_id, $_SESSION['order_lists2_param']['payment_methods_id']) ? ' checked ' : '') ?>>
                                                                                    <?= $payment_methods_title ?>
                                                                                </span>
                                                                            </li>
                                                                            <?
                                                                        }
                                                                        ?>
                                                                    </ul>
                                                                    <?
                                                                } else {
                                                                    ?>
                                                                    <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                                                    <span class="textHolder payment_gateways">
                                                                        <input type="checkbox" name="payment_gateways_id[]" value="<?= $payment_gateways_id ?>" <?= (count($_SESSION['order_lists2_param']['payment_gateways_id']) && in_array($payment_gateways_id, $_SESSION['order_lists2_param']['payment_gateways_id']) ? ' checked ' : '') ?>>
                                                                        <?= $payment_gateways_title ?>
                                                                    </span>
                                                                    <?
                                                                }
                                                                ?>
                                                        </li>
                                                        <script>
                                    function getReturnedValue(received_val, input_field) {
                                        jQuery('#' + input_field).val(received_val);
                                    }

                                    jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").click(function () {
                                        if (jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked")) {
                                            if (jQuery("li#<?= $payment_gateways_id ?> ul:visible").length) {
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", true);
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", true);
                                            } else {
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", true);
                                            }
                                        }   else{
                                           
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", false);
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", false);
                                        
                                          
                                        }
                                    });
                                    jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").click(function () {
                                    
                                                parent = $(this).closest(".treeItem").parent(); 
                                                selected = parent.find('input:checked');
                                                total = parent.find('input');
                                                
                                        if (selected.length == total.length) {
                                            jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", true);
                                        }
                                        else{
                                         jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", false);
                                        }
                                        
                                    });
                                    jQuery(document).ready(function () {
                                        if (jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked")) {
                                            jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", '');
                                        }
                                    });
                                                        </script>
                                                    <? } ?>
                                                </ul>

                                                <script>
                                                    jQuery(document).ready(function () {
                                                        tree = jQuery('.myTree');
                                                        jQuery('img.expandImage', tree.get(0)).click(
                                                                function () {
                                                                    if (this.src.indexOf('spacer') == -1) {
                                                                        subbranch = jQuery('ul', this.parentNode).eq(0);
                                                                        if (subbranch.css('display') == 'none') {
                                                                            subbranch.show();
                                                                            this.src = 'images/icon-collapse-small.gif';
                                                                        } else {
                                                                            subbranch.hide();
                                                                            this.src = 'images/icon-expand-small.gif';
                                                                        }
                                                                    }
                                                                }
                                                        );
                                                    });
                                                    jQuery('.myTree li input').click(function () {
                                                        if (jQuery(this).hasClass('pg_any')) {
                                                            jQuery('.myTree li input:not(.pg_any)').attr('checked', false);
                                                            jQuery('.myTree li .pg_any').attr('checked', true);
                                                        } else {
                                                            if (jQuery('.myTree li input:not(.pg_any):checked').length > 0) {
                                                                jQuery('.myTree li .pg_any').attr('checked', false);
                                                            } else {
                                                                jQuery('.myTree li .pg_any').attr('checked', true);
                                                            }
                                                        }
                                                    });
                                                </script>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td class="main" width="12%">&nbsp;</td>
                                            <td nowrap class="main"></td>
                                            <td align="right">
                                                <input type="button" name="export_report" value="<?= BTN_EXPORT_REPORT ?>" class="inputButton" onClick="return form_checking(this.form, 'export_report');">&nbsp;&nbsp;
                                                <input type="button" name="search" value="<?= BTN_SHOW_REPORT ?>" class="inputButton" onClick="return form_checking(this.form, 'show_report');">&nbsp;&nbsp;
                                                <input type="button" name="reset" value="<?= BTN_RESET ?>" class="inputButton" onClick="document.location.href = '<?= tep_href_link(FILENAME_ORDERS_LISTING, 'action=reset_session') ?>'">
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            </form>

                            <script language="javascript"><!--
                                function form_checking(form_obj, form_action) {
                                    var start_date = document.getElementById('start_date').value;
                                    var end_date = document.getElementById('end_date').value;
                                    if (start_date.length == 0) {
                                        alert('Start date is needed for this Report!');
                                        document.getElementById('start_date').focus();
                                        document.getElementById('start_date').select();
                                        return false;
                                    } else if (start_date.length > 0) {
                                        if (!validateDate(start_date)) {
                                            alert('Start date is not a valid date format as requested!');
                                            document.getElementById('start_date').focus();
                                            document.getElementById('start_date').select();
                                            return false;
                                        }
                                    }

                                    if (end_date.length == 0) {
                                        alert('End date is needed for this Report!');
                                        document.getElementById('start_date').focus();
                                        document.getElementById('start_date').select();
                                        return false;
                                    } else if (end_date.length > 0) {
                                        if (!validateDate(end_date)) {
                                            alert('End date is not a valid date format as requested!');
                                            document.getElementById('end_date').focus();
                                            document.getElementById('end_date').select();
                                            return false;
                                        }
                                    }

                                    if (start_date.length > 0 && end_date.length > 0) {
                                        if (!validStartAndEndDate(start_date, end_date)) {
                                            alert('Start Date is greater than End Date!');
                                            document.getElementById('start_date').focus();
                                            document.getElementById('start_date').select();
                                            return false;
                                        }
                                    }

                                    document.getElementById('action').value = form_action;
                                    form_obj.submit();
                                }

                                function set_option(any_obj) {
                                    var multi_pay_select = document.order_list2.elements[any_obj.name.replace(/_any/, "[]")];
                                    if (any_obj.checked == true) {
                                        for (i = 0; i < multi_pay_select.length; i++) {
                                            multi_pay_select[i].checked = false;
                                        }
                                    } else {	// force to check if no any payment option is selected
                                        var selected_count = 0;
                                        for (i = 0; i < multi_pay_select.length; i++) {
                                            if (multi_pay_select[i].checked == true) {
                                                selected_count++;
                                            }
                                        }
                                        if (!selected_count) {
                                            any_obj.checked = true;
                                        }
                                    }
                                }

                                function verify_status_selection(status_obj) {
                                    if (status_obj != null) {
                                        var cur_status_id = status_obj.value;
                                        var disabled_mode = status_obj.checked ? false : true;
                                        if (cur_status_id == '2' || cur_status_id == '3') {
                                            var sub_status_select = document.order_list2.elements['order_sub_status_' + cur_status_id + '[]'];
                                            if (typeof (sub_status_select) != 'undefined') {
                                                if (typeof (sub_status_select.length) != 'undefined') {
                                                    for (sub_cnt = 0; sub_cnt < sub_status_select.length; sub_cnt++) {
                                                        sub_status_select[sub_cnt].disabled = disabled_mode;
                                                        sub_status_select[sub_cnt].checked = !disabled_mode;
                                                    }
                                                } else {
                                                    sub_status_select.disabled = disabled_mode;
                                                    sub_status_select.checked = !disabled_mode;
                                                }
                                            }
                                        }
                                    }
                                }

                                function verify_payment_selection() {
                                    var multi_pay_select = document.order_list2.elements['payment_method[]'];
                                    var selected_count = 0;
                                    for (i = 0; i < multi_pay_select.length; i++) {
                                        if (multi_pay_select[i].checked == true) {
                                            selected_count++;
                                        }
                                    }
                                    if (!selected_count) {
                                        document.getElementById('payment_method_any').checked = true;
                                    } else {
                                        document.getElementById('payment_method_any').checked = false;
                                    }
                                }
                                //-->
                            </script>
                        <? } ?>
                    </table>
                    <!-- body_text_eof //-->
                </td>
            </tr>
        </table>
        <!-- body_eof //-->

        <!-- footer //-->
        <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
    </body>
</html>