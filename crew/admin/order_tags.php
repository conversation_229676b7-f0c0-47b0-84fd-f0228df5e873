<?
/*
  $Id: order_tags.php,v 1.7 2013/10/18 02:30:41 june.see Exp $

  Developer: <PERSON>
  Copyright (c) 2005 SKC Ventrue

  Released under the GNU General Public License
 */

require_once('includes/application_top.php');
require(DIR_WS_CLASSES . 'order_tags.php');

$tags_obj = new order_tags();

if (tep_not_null($_REQUEST["order_status_search"]) && isset($_REQUEST["order_status_search"])) {
    $_SESSION["tag_order_status_search"] = $_REQUEST["order_status_search"];
}

if (tep_not_null($_REQUEST["filename_search"])) {
    $_SESSION["tag_filename_search"] = $_REQUEST["filename_search"];
}

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
    switch ($action) {
        case "insert_tags":
        case "update_tags":
            $error = false;
            $filename_new = tep_db_prepare_input($_REQUEST["filename"]);

            if (!$tags_obj->check_duplicate_order_tag($filename_new, $_REQUEST, $messageStack)) {
                if ($tags_obj->update_order_tag($filename_new, $_REQUEST, $messageStack)) {
                    tep_redirect(tep_href_link(FILENAME_ORDERS_TAGS));
                }
            } else {
                $action = $_REQUEST["from_action"];
            }
            break;
        case "delete_tags":
            $tags_obj->delete_order_tag($_REQUEST, $messageStack);

            tep_redirect(tep_href_link(FILENAME_ORDERS_TAGS));

            break;
    }
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
        <script language="JavaScript" src="includes/javascript/jquery.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?= BOX_WIDTH ?>" valign="top">
                    <table border="0" width="<?= BOX_WIDTH ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td width="100%">
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top" width="70%"><?= HEADING_TITLE ?></td>
                                        <td class="main">
                                            <?= $tags_obj->search_order_tags(); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" colspan="2">
                                            <?
                                            if ($action != "new_tags")
                                                echo '[ <a href="' . tep_href_link(FILENAME_ORDERS_TAGS, 'action=new_tags') . '" >' . LINK_ADD_ORDER_TAG . '</a> ]';
                                            else
                                                echo "&nbsp;";
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                        <?
                        if ($action == "new_tags" || $action == "edit_tags") {
                            echo $tags_obj->order_tags_form($_REQUEST);
                        }

                        echo $tags_obj->show_order_tags($_REQUEST);
                        ?>
                    </table>
                </td>
                <!-- body_text_eof //-->
            </tr>
        </table>
        <!-- body_eof //-->

        <!-- footer //-->
        <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
    </body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>