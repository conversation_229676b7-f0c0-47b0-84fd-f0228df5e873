<?
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
include_once(DIR_WS_CLASSES . 'order.php');
include_once(DIR_WS_CLASSES . 'payment_methods.php');

define('PARTIAL_DELIVERY_STATUS', 2);
define('SYSTEM_PAYMENT_STORE_CREDITS', 'OGM_CREDITS');
//include(DIR_WS_CLASSES . 'order.php');

$currencies = new currencies();

$manage_search_criteria_permission = tep_admin_files_actions(FILENAME_STATS_ORDERS_TRACKING, 'SAVE_ORDER_LISTS_CRITERIA');
$search_by_cat_permission = tep_admin_files_actions(FILENAME_STATS_ORDERS_TRACKING, 'SEARCH_BY_ORDERED_CATEGORIES');

$customer_view_email_listing_permission = tep_admin_files_actions(FILENAME_STATS_ORDERS_TRACKING, 'ORDER_TRACK_VIEW_CUSTOMER_EMAIL');

$perform_unserialize_criteria = false;

$status_options = array();
$order_status_select_sql = "SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY orders_status_sort_order";
$order_status_result_sql = tep_db_query($order_status_select_sql);
while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
	$status_options[$order_status_row["orders_status_id"]] = $order_status_row["orders_status_name"];
}

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "show_report":
			//tep_release_locked_order(tep_session_active_admin(), false);	// releasing those locked orders for group of admin staff other than this group
			//$active_admin_staff_array = tep_session_active_admin();	// array storing all active admin staff
			if (!$_REQUEST['cont']) {
				if ($_REQUEST["subaction"] == "goto_search") {
					$_SESSION['order_lists_param']["criteria_id"] = (int)$_REQUEST["criteria_id"];
					$perform_unserialize_criteria = true;
				} else {
					if ($_REQUEST["subaction"] == "sl_status") {	// serialized status
						if ($_REQUEST["order_status"]) {
							$_REQUEST["order_status"] = tep_array_unserialize(rawurldecode($_REQUEST["order_status"]));
						}
						
						if (count($status_options)) {
							foreach ($status_options as $id => $title) {
								if (isset($_REQUEST["status_".$id])) {
									$_REQUEST["status_".$id] = tep_array_unserialize(rawurldecode($_REQUEST["status_".$id]));
								}
							}
						}
					}
					
					unset($_SESSION['order_lists_param']["criteria_id"]);
					unset($_SESSION['order_lists_param']["cur_criteria_name"]);
					
					// checking for isset to handle the case when the order status link is clicked from the Admin main page
					$_SESSION['order_lists_param']["cat_id"] = $search_by_cat_permission && isset($_REQUEST["cat_id"]) ? (int)$_REQUEST["cat_id"] : 0;
					$_SESSION['order_lists_param']["product_id"] = (int)$_REQUEST["product_id"];
					
					//changes made @ 200808071647 according to Mantis Issue # 0000013: removed (int) from (int)$_REQUEST["product_type"]
					$_SESSION['order_lists_param']["product_type"] = (tep_not_null($_REQUEST["product_type"]) ? $_REQUEST["product_type"] : '');
					$_SESSION['order_lists_param']["order_status"] = $_REQUEST["order_status"];
					$_SESSION['order_lists_param']["start_date"] = $_REQUEST["start_date"];
					$_SESSION['order_lists_param']["end_date"] = $_REQUEST["end_date"];
					$_SESSION['order_lists_param']["follow_up_date"] = $_REQUEST["follow_up_date"];
					$_SESSION['order_lists_param']["search_by_field"] = $_REQUEST["search_by_field"];
					$_SESSION['order_lists_param']["search_by_field_value"] = $_REQUEST["search_by_field_value"];
					$_SESSION['order_lists_param']["customer_group"] = $_REQUEST["customer_group"];
					$_SESSION['order_lists_param']["order_total"] = $_REQUEST["order_total"];
                    $_SESSION['order_lists_param']['payment_methods_id']  = $_REQUEST['payment_methods_id'];
                    $_SESSION['order_lists_param']['payment_gateways_id'] = $_REQUEST["payment_gateways_id"];
					$_SESSION['order_lists_param']["page_refresh"] = $_REQUEST["page_refresh"];
					$_SESSION['order_lists_param']["show_records"] = $_REQUEST["show_records"];
                    
					if (count($status_options)) {
						foreach ($status_options as $id => $title) {
							$_SESSION['order_lists_param']["status_".$id] = $_REQUEST["status_".$id];
						}
					}
					
					if ($_REQUEST["order_list_subaction"] == "save_search" && count($_SESSION['order_lists_param']["show_records"])) {
						if (tep_not_null($_REQUEST["search_name"])) {
							$save_string = array();
							foreach ($_SESSION['order_lists_param'] as $key => $search_input) {
								$serialized_key_value_pair = tep_array_serialize($key) . ':~:' . tep_array_serialize($search_input);
								$save_string[] = $serialized_key_value_pair;
							}
							
							$search_name = tep_db_prepare_input($_REQUEST["search_name"]);
							$search_criteria_id = '';
							$search_criteria_check_select_sql = "SELECT search_criteria_id FROM " . TABLE_SEARCH_CRITERIA . " WHERE filename='" . tep_db_input(basename($PHP_SELF)) . "' AND search_criteria_name = '" . tep_db_input($search_name) . "'";
							$search_criteria_check_result_sql = tep_db_query($search_criteria_check_select_sql);
							if ($search_criteria_check_row = tep_db_fetch_array($search_criteria_check_result_sql)) {
								$search_criteria_id = (int)$search_criteria_check_row["search_criteria_id"];
							}
							
							if (count($save_string)) {
								$search_criteria_sql_data = array(	'filename' => tep_db_prepare_input(basename($PHP_SELF)),
								                       				'search_criteria_name' => $search_name,
								                       				'search_criteria_string' => tep_db_prepare_input(implode('#~#', $save_string))
								                       			);
								
								if (tep_not_null($search_criteria_id)) {
									// update existing search
									$update_sql_data = array(	'date_search_criteria_last_modified' => 'now()',
																'last_modified_by' => $login_id
															);
									$sql_data_array = array_merge($search_criteria_sql_data, $update_sql_data);
									tep_db_perform(TABLE_SEARCH_CRITERIA, $sql_data_array, 'update', "search_criteria_id = '" . $search_criteria_id . "'");
									$messageStack->add('OK, your criteria named &lt;'.$search_name.'&gt; is updated.', 'success');
								} else {
									// insert new searchs
									$insert_sql_data = array(	'date_search_criteria_added' => 'now()',
								    	                   		'search_criteria_created_by' => $login_id
															);
									$sql_data_array = array_merge($search_criteria_sql_data, $insert_sql_data);
									tep_db_perform(TABLE_SEARCH_CRITERIA, $sql_data_array);
									$search_criteria_id = tep_db_insert_id();
									$messageStack->add('OK, your new criteria named &lt;'.$search_name.'&gt; is saved.', 'success');
								}
								$_SESSION['order_lists_param']["criteria_id"] = $search_criteria_id;
								$perform_unserialize_criteria = true;
							}
						}
					}
				}
		  	}
			break;
        case "reset_session":
        	unset($_SESSION['order_lists_param']);
        	tep_redirect(tep_href_link(FILENAME_STATS_ORDERS_TRACKING));
        	break;
        case "delete_search":
        	// check for permission
        	$search_criteria_check_select_sql = "SELECT search_criteria_id, search_criteria_name FROM " . TABLE_SEARCH_CRITERIA . " WHERE search_criteria_id = '" . (int)$_REQUEST["criteria_id"] . "'";
			$search_criteria_check_result_sql = tep_db_query($search_criteria_check_select_sql);
			if ($search_criteria_check_row = tep_db_fetch_array($search_criteria_check_result_sql)) {
				tep_db_query("DELETE FROM " . TABLE_SEARCH_CRITERIA . " WHERE search_criteria_id='" . $search_criteria_check_row["search_criteria_id"] . "'");
				$messageStack->add_session('OK, your criteria named &lt;'.$search_criteria_check_row["search_criteria_name"].'&gt; is deleted.', 'success');
			} else {
				$messageStack->add_session('Your criteria does not exists.', 'error');
			}
        	unset($_SESSION['order_lists_param']);
        	tep_redirect(tep_href_link(FILENAME_STATS_ORDERS_TRACKING));
        	break;
	}
}

$payment_methods_obj = new payment_methods('payment_gateways');
$payment_module_info = array();

foreach($payment_methods_obj->payment_gateways_array as $payment_gateways_id => $payment_gateways_data) {
	$payment_module_info[$payment_gateways_id] = array(	'display_colour'=>$payment_gateways_data->legend_display_colour ? $payment_gateways_data->legend_display_colour : "#E1E1E2" ,
														'method_title'=>$payment_gateways_data->title);

	if (isset($payment_gateways_data->post_auth_time_out)) {
		$payment_module_info[$payment_gateways_id]['post_auth_time_out'] = (int)$payment_gateways_data->post_auth_time_out;
	}
}

$payment_module_info[SYSTEM_PAYMENT_STORE_CREDITS] = array(	'display_colour'=> "#C7DBF0" ,
															'method_title'=>'Store Credits');

$saved_search_options = array( array ('id' => '', "text" => "Saved Criteria >>>>>") );
$search_criteria_select_sql = "SELECT search_criteria_id, search_criteria_name FROM " . TABLE_SEARCH_CRITERIA . " WHERE filename='" . tep_db_input(basename($PHP_SELF)) . "' ORDER BY search_criteria_name";
$search_criteria_result_sql = tep_db_query($search_criteria_select_sql);
while ($search_criteria_row = tep_db_fetch_array($search_criteria_result_sql)) {
	$saved_search_options[] = array ('id' => $search_criteria_row["search_criteria_id"], "text" => $search_criteria_row["search_criteria_name"]);
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="includes/jquery.tree.css">
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<script language="javascript" src="includes/general.js"></script>
<script language="JavaScript" src="includes/javascript/jquery.js"></script>
<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
<script language="javascript" src="includes/javascript/orders.js"></script>
<script language="javascript" src="includes/javascript/modal_win.js"></script>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<!-- body_text //-->
<?
if ($_REQUEST['action'] == 'show_report') {
	$view_payment_details_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_PAYMENT_DETAILS');
	$view_customer_details_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_CUSTOMER_DETAILS');
	$force_date_filtering = true;
    
	if ($perform_unserialize_criteria) {
		if (isset($_SESSION['order_lists_param']["criteria_id"]) && is_numeric($_SESSION['order_lists_param']["criteria_id"])) {
			$search_criteria_select_sql = "SELECT search_criteria_name, search_criteria_string FROM " . TABLE_SEARCH_CRITERIA . " WHERE search_criteria_id = '" . (int)$_SESSION['order_lists_param']["criteria_id"] . "' AND filename = '" . tep_db_input(basename($PHP_SELF)) . "'";
			$search_criteria_result_sql = tep_db_query($search_criteria_select_sql);
			
			if ($search_criteria_row = tep_db_fetch_array($search_criteria_result_sql)) {
				$_SESSION['order_lists_param']["cur_criteria_name"] = $search_criteria_row["search_criteria_name"];
				$criteria_array = array();
				$criteria_array = explode('#~#', $search_criteria_row["search_criteria_string"]);
				
				unset($_SESSION['order_lists_param']["cat_id"]);
				unset($_SESSION['order_lists_param']["product_id"]);
				unset($_SESSION['order_lists_param']["product_type"]);
				unset($_SESSION['order_lists_param']["order_status"]);
				unset($_SESSION['order_lists_param']["start_date"]);
				unset($_SESSION['order_lists_param']["end_date"]);
				unset($_SESSION['order_lists_param']["follow_up_date"]);
				unset($_SESSION['order_lists_param']["search_by_field"]);
				unset($_SESSION['order_lists_param']["search_by_field_value"]);
				unset($_SESSION['order_lists_param']["customer_group"]);
				unset($_SESSION['order_lists_param']["order_total"]);
                unset($_SESSION['order_lists_param']['payment_methods_id']);
                unset($_SESSION['order_lists_param']['payment_gateways_id']);
				unset($_SESSION['order_lists_param']["page_refresh"]);
				unset($_SESSION['order_lists_param']["show_records"]);
				
				if (count($status_options)) {
					foreach ($status_options as $id => $title) {
						unset($_SESSION['order_lists_param']["status_".$id]);
					}
				}
				
				for ($s=0; $s < count($criteria_array); $s++) {
					list($serialized_key, $serialized_value) = explode(':~:', $criteria_array[$s]);
					$unserialized_key = tep_array_unserialize($serialized_key);
					$serialized_key_value = tep_array_unserialize($serialized_value);
					
					$_SESSION['order_lists_param'][$unserialized_key] = $serialized_key_value;
				}
			}
		}
		$perform_unserialize_criteria = false;
	}
	
	$orders_select_str = "select o.orders_id, o.customers_name, o.customers_id, o.remote_addr, o.payment_method, o.payment_methods_id, o.payment_methods_parent_id, o.customers_email_address, date_purchased AS dp, o.date_purchased AS date_purchased, o.last_modified, o.currency, o.currency_value, o.orders_tag_ids, o.orders_read_mode, s.orders_status_name, ot.value as order_total, ot_gv.value as order_sc, c.customers_flag, c.customers_status, o.orders_follow_up_datetime, IF(o.orders_follow_up_datetime <= now(), o.orders_follow_up_datetime, now()) AS follow_up_ordering from " . TABLE_ORDERS . " as o inner join " . TABLE_ORDERS_STATUS . " as s on o.orders_status = s.orders_status_id left join " . TABLE_ORDERS_TOTAL . " as ot on o.orders_id = ot.orders_id left join " . TABLE_CUSTOMERS . " as c on o.customers_id=c.customers_id inner join " . TABLE_ORDERS_EXTRA_INFO . " as site on o.orders_id = site.orders_id AND site.orders_extra_info_key = 'site_id'";
	
	$extra_join_str = '';
	$union_array = array();
	$search_by_where_str = " 1 ";
	if (tep_not_null($_SESSION['order_lists_param']["search_by_field_value"]) && tep_not_null($_SESSION['order_lists_param']["search_by_field"])) {
        $force_date_filtering = false;
		switch($_SESSION['order_lists_param']["search_by_field"]) {
			case 'join_paypal_id':
				$union_array[0]['table'] = " left join " . TABLE_PAYPAL . " AS paypal ON o.orders_id=paypal.invoice ";
				$union_array[0]['condition'] = " and paypal.txn_id = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				$union_array[1]['table'] = " left join " . TABLE_PAYPALEC . " AS paypalec ON o.orders_id=paypalec.paypal_order_id ";
				$union_array[1]['condition'] = " and paypalec.txn_id = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				$union_array[2]['table'] = " left join " . TABLE_PIPWAVE_PAYMENT . " AS pipwave ON o.orders_id=pipwave.orders_id ";
				$union_array[2]['condition'] = " and (pipwave.payment_method_code = 'paypal.ec' or pipwave.payment_method_code = 'paypal.cc') and pipwave.pg_txn_id = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				break;
			case 'join_paypal_email':
				$union_array[0]['table'] = " left join " . TABLE_PAYPAL . " AS paypal ON o.orders_id=paypal.invoice ";
				$union_array[0]['condition'] = " and paypal.payer_email = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				$union_array[1]['table'] = " left join " . TABLE_PAYPALEC . " AS paypalec ON o.orders_id=paypalec.paypal_order_id ";
				$union_array[1]['condition'] = " and paypalec.payer_email = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				$union_array[2]['table'] = " left join " . TABLE_ANALYSIS_PG_INFO . " AS analysisPg ON o.orders_id=analysisPg.orders_id ";
				$union_array[2]['condition'] = " and analysisPg.info_key = 'payer_email' and analysisPg.info_value = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				break;

			case 'join_mb_id':
				$union_array[0]['table'] = " left join " . TABLE_PAYMENT_MONEYBOOKERS . " AS p_mb ON o.orders_id=p_mb.mb_order_id ";
				$union_array[0]['condition'] = " and p_mb.mb_mb_trans_id = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				$union_array[1]['table'] = " left join " . TABLE_PIPWAVE_PAYMENT . " AS pipwave ON o.orders_id=pipwave.orders_id 
				inner join ".TABLE_PIPWAVE_PAYMENT_MAPPER." AS mapper ON mapper.pipwave_payment_code = pipwave.payment_method_code";
				$union_array[1]['condition'] = " and mapper.pg_code = 'moneybookers' and pipwave.pg_txn_id = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				break;
			case 'join_mb_email':
				$union_array[0]['table'] = " left join " . TABLE_PAYMENT_MONEYBOOKERS . " AS p_mb ON o.orders_id=p_mb.mb_order_id ";
				$union_array[0]['condition'] = " and p_mb.mb_payer_email = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				$union_array[1]['table'] = " left join " . TABLE_ANALYSIS_PG_INFO . " AS analysisPg ON o.orders_id=analysisPg.orders_id ";
				$union_array[1]['condition'] = " and analysisPg.info_key = 'moneybooker_email' and analysisPg.info_value = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				break;

				
			case 'join_alipay_trade_no':
				$extra_join_str = " left join " . TABLE_ALIPAY . " AS p_alipay ON o.orders_id=p_alipay.alipay_orders_id ";
				$search_by_where_str = " p_alipay.alipay_trade_no = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				
				break;
			case 'join_cimb_reference_no':
				$extra_join_str = " left join " . TABLE_CIMB . " AS p_cimb ON o.orders_id=p_cimb.orders_id ";
				$search_by_where_str = " p_cimb.cimb_reference_no = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				
				break;
			case 'join_qfpay_id':
				$extra_join_str = " left join " . TABLE_PIPWAVE_PAYMENT . " AS pipwave ON o.orders_id=pipwave.orders_id";
				$search_by_where_str = " pipwave.payment_method_code LIKE 'qfpay.%' and pipwave.pg_txn_id = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				break;
			default:
				$search_by_where_str = ' ' . $_SESSION['order_lists_param']["search_by_field"] . " = '" . tep_db_input($_SESSION['order_lists_param']["search_by_field_value"]) . "'";
				
				break;
		}
	}
	
	$extra_join_str .= " left join " . TABLE_ORDERS_TOTAL . " as ot_gv on (o.orders_id = ot_gv.orders_id and ot_gv.class = 'ot_gv') ";	// Need to be always joined since we need store credit (sc) amount to decide the payment legend
	
	$product_id_str = (isset($_SESSION['order_lists_param']["product_id"]) && tep_not_null($_SESSION['order_lists_param']["product_id"])) ? " and op.products_id=" . $_SESSION['order_lists_param']["product_id"] : '';
  	//changes made @ 200808121643 according to Mantis Issue # 0000013: filter out child in static package, checking only apply in parent stage which op.parent_orders_product_id = 0
  	$product_type_str = (isset($_SESSION['order_lists_param']["product_type"]) && tep_not_null($_SESSION['order_lists_param']["product_type"])) ? " and op.parent_orders_products_id = 0 AND op.custom_products_type_id=" . $_SESSION['order_lists_param']["product_type"] : '';
  	$customer_group_str = (isset($_SESSION['order_lists_param']["customer_group"]) && tep_not_null($_SESSION['order_lists_param']["customer_group"])) ? " and c.customers_groups_id='" . $_SESSION['order_lists_param']["customer_group"] . "'": '';
  	$order_total_str = (isset($_SESSION['order_lists_param']["order_total"]) && tep_not_null($_SESSION['order_lists_param']["order_total"])) ? " and ot.value=" . $_SESSION['order_lists_param']["order_total"] : '';
  	
  	$ot_gv_str = '';
  	$payment_method_str = '';
    
  	if (isset($_SESSION['order_lists_param']["payment_gateways_id"]) && count($_SESSION['order_lists_param']["payment_gateways_id"])) {
  		$actual_selected_payment_method = $_SESSION['order_lists_param']["payment_gateways_id"];
        
        if (in_array("any", $_SESSION['order_lists_param']["payment_gateways_id"])) {
			$payment_method_str = '';
		} else {
            if (in_array("OGM_CREDITS", $_SESSION['order_lists_param']["payment_gateways_id"])) {
                $ot_gv_str = " ot_gv.value > 0 ";
                
                $search_index_key = array_search('OGM_CREDITS', $actual_selected_payment_method);
                unset($actual_selected_payment_method[$search_index_key]);
            }

            if (count($actual_selected_payment_method)) {
                $payment_method_str = " o.payment_methods_parent_id IN ('" . implode("', '", $actual_selected_payment_method) . "')";
                if (tep_not_null($ot_gv_str))	$payment_method_str = $payment_method_str.' OR '.$ot_gv_str;
            } else {
                if (tep_not_null($ot_gv_str))	$payment_method_str = $ot_gv_str;
            }
        }
  	}
  	
    if (isset($_SESSION['order_lists_param']['payment_methods_id']) && count($_SESSION['order_lists_param']['payment_methods_id'])) {
        if (tep_not_null($payment_method_str)) {
            $payment_method_str .= ' OR ' . " o.payment_methods_id IN ('" . implode("', '", $_SESSION['order_lists_param']['payment_methods_id']) . "')";
        } else {
            $payment_method_str = " o.payment_methods_id IN ('" . implode("', '", $_SESSION['order_lists_param']['payment_methods_id']) . "')";
        }
	}
    
    if (tep_not_null($payment_method_str))  $payment_method_str = ' and ( '.$payment_method_str.' )';
    
	$show_order_status = isset($_SESSION['order_lists_param']["order_status"]) ? $_SESSION['order_lists_param']["order_status"] : array();
	if (!count($show_order_status)) {
		$show_order_status = is_array($status_options) ? array_keys($status_options) : array();
	}
	
	$_date_filter_order_status = array (3, 5);	# complete, cancel
	$_date_filter_count = count(array_intersect($_date_filter_order_status, $show_order_status));
    
    if (!count($_date_filter_count))    $force_date_filtering = false;
    
    if ($_REQUEST["subaction"] == "sl_status") {
        $force_date_filtering = false;
    }
    
    $start_date_str = $end_date_str = " 1 ";
    
  	if (tep_not_null($_SESSION['order_lists_param']["start_date"])) {
		if (strpos($_SESSION['order_lists_param']["start_date"], ':') !== false) {
			$startDateObj = explode(' ', trim($_SESSION['order_lists_param']["start_date"]));
			list($yr, $mth, $day) = explode('-', $startDateObj[0]);
			list($hr, $min) = explode(':', $startDateObj[1]);
			$start_date_str = " ( o.date_purchased >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."')";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['order_lists_param']["start_date"]));
			
			$start_date_str = " ( o.date_purchased >= '".date("Y-m-d H:i:s", mktime(0,0,0,$mth,$day,$yr))."')";
		}
	}
	
	if (tep_not_null($_SESSION['order_lists_param']["end_date"])) {
		if (strpos($_SESSION['order_lists_param']["end_date"], ':') !== false) {
			$endDateObj = explode(' ', trim($_SESSION['order_lists_param']["end_date"]));
			list($yr, $mth, $day) = explode('-', $endDateObj[0]);
			list($hr, $min) = explode(':', $endDateObj[1]);
			$end_date_str = " ( o.date_purchased <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 59, $mth, $day, $yr))."' )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['order_lists_param']["end_date"]));
			$end_date_str = " ( o.date_purchased <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."' )";
		}
		
		if (!tep_not_null($_SESSION['order_lists_param']["start_date"]) && $force_date_filtering) {
			$start_date_str = " ( o.date_purchased >= '" . date("Y-m-d H:i:s", mktime(0, 0, 0, $mth, $day, $yr)) . "' )";
		}
	} else if ($force_date_filtering) {
		if (tep_not_null($_SESSION['order_lists_param']["start_date"])) {
			$end = strtotime('+31 days', strtotime($_SESSION['order_lists_param']["start_date"]));
			$end_date_str = " ( o.date_purchased <= '" . date("Y-m-d H:i:s", $end) . "' ) ";
        } else {
			$start_date_str = " ( o.date_purchased >= '" . date("Y-m-d") . " 00:00:00')";
			$end_date_str = " ( o.date_purchased <= '" . date("Y-m-d") . " 23:59:59')";
		}
	}
    
    if (tep_not_null($_SESSION['order_lists_param']["start_date"]) && tep_not_null($_SESSION['order_lists_param']["end_date"]) && $force_date_filtering) {
		$start = strtotime('+31 days', strtotime($_SESSION['order_lists_param']["start_date"]));
		$end = strtotime($_SESSION['order_lists_param']["end_date"]);
		
		if ($end > $start) {
			$start_date_str = " ( o.date_purchased >= '" . date("Y-m-d") . " 00:00:00')";
			$end_date_str = " ( o.date_purchased <= '" . date("Y-m-d") . " 23:59:59')";
		}
	}
    
	if (tep_not_null($_SESSION['order_lists_param']["follow_up_date"])) {
		if (strpos($_SESSION['order_lists_param']["follow_up_date"], ':') !== false) {
			$dateObj = explode(' ', trim($_SESSION['order_lists_param']["follow_up_date"]));
			list($yr, $mth, $day) = explode('-', $dateObj[0]);
			list($hr, $min) = explode(':', $dateObj[1]);
			$follow_date_str = " ( o.orders_follow_up_datetime = '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['order_lists_param']["follow_up_date"]));
			$follow_date_str = " ( o.orders_follow_up_datetime >= '".date("Y-m-d H:i:s", mktime(0,0,0,$mth,$day,$yr))."' AND o.orders_follow_up_datetime <= '".date("Y-m-d H:i:s", mktime(23,59,59,$mth,$day,$yr))."' )";
		}
	} else {
		$follow_date_str = " 1 ";
	}
	
	$categories_str = " 1 ";
  	if (tep_not_null($_SESSION['order_lists_param']["product_id"]) || $_SESSION['order_lists_param']["cat_id"] !== '' || tep_not_null($_SESSION['order_lists_param']["product_type"])) {
		$orders_select_str .= " left join " . TABLE_ORDERS_PRODUCTS . " as op on o.orders_id=op.orders_id ";
		
		if (is_numeric($_SESSION['order_lists_param']["cat_id"]) && $_SESSION['order_lists_param']["cat_id"] > 0) {
			if (tep_check_cat_tree_permissions(FILENAME_ORDERS, $_SESSION['order_lists_param']["cat_id"]) == 1) {
                $cat_parent_path = tep_get_categories_parent_path($_SESSION['order_lists_param']["cat_id"]);
                if (empty($cat_parent_path)) {
                    $cat_parent_path = '_';
                }
                
                $category_path = str_replace('_', '\_', $cat_parent_path . $_SESSION['order_lists_param']["cat_id"] . '_');
                
                $orders_select_str .= " left join " . TABLE_PRODUCTS . " as p on (op.products_id=p.products_id) ";
	  			$categories_str = " p.products_cat_id_path LIKE '".$category_path."%' ";
			}
  		}
  	}

  	$orders_select_str .= $extra_join_str;
  	$orders_where_str = " where site.orders_extra_info_value != '5' and s.language_id = '" . (int)$languages_id . "' and ot.class = 'ot_total' ";
  	$orders_where_str .= " $product_id_str $product_type_str and $search_by_where_str and $categories_str and $start_date_str and $end_date_str and $follow_date_str $customer_group_str $order_total_str $payment_method_str";

  	$orders_group_by_str = " group by o.orders_id ";
  	$orders_order_by_str = " order by follow_up_ordering ASC, dp DESC ";
	
	$show_order_status = isset($_SESSION['order_lists_param']["order_status"]) ? $_SESSION['order_lists_param']["order_status"] : array();
	if (!count($show_order_status)) {
		$show_order_status = is_array($status_options) ? array_keys($status_options) : array();
	}
	
  	$show_records = $_SESSION['order_lists_param']["show_records"];
?>
					<tr>
            			<td valign="top" class="pageHeading"><?=HEADING_TITLE?>
            			<?
            				if ($manage_search_criteria_permission) {	// check for permission
            					if (tep_not_null($_SESSION['order_lists_param']["cur_criteria_name"]) && tep_not_null($_SESSION['order_lists_param']["criteria_id"])) {
									echo '<br><a href="javascript:void(confirm_delete(\'\', \'this search criteria\', \''.tep_href_link(FILENAME_STATS_ORDERS_TRACKING, 'action=delete_search&criteria_id='.$_SESSION['order_lists_param']["criteria_id"]).'\'))">Forget &lt;'.$_SESSION['order_lists_param']["cur_criteria_name"].'&gt; Criteria</a>'; 
								}
							}
						?>
						</td>
            			<td class="smallText" align="right" valign="top">&nbsp;
						<?
							echo tep_draw_form('orders', FILENAME_ORDERS, '', 'post');
							echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('oID', '', 'size="12"') . tep_draw_hidden_field('subaction', 'change_order');
							echo "</form><br>";
							if (count($saved_search_options) > 1) {
								echo tep_draw_form('goto_search_form', FILENAME_STATS_ORDERS_TRACKING, 'action=show_report', 'post');
								echo tep_draw_hidden_field('subaction', 'goto_search');
								echo tep_draw_pull_down_menu("criteria_id", $saved_search_options, tep_not_null($_SESSION['order_lists_param']["criteria_id"]) ? $_SESSION['order_lists_param']["criteria_id"] : '', 'onChange="if(this.value != \'\') { this.form.submit(); }"');
								echo "</form>";
							}
						?>
						</td>
          			</tr>
          			<tr>
          				<td colspan="2">
<?
	$tag_selection_general = array ( array('id' => '', 'text' => 'Order Lists Options ...'),
									 array('id' => 'rd', 'text' => '&nbsp;&nbsp;&nbsp;Mark as read'),
									 array('id' => 'ur', 'text' => '&nbsp;&nbsp;&nbsp;Mark as unread'),
									 array('id' => '', 'text' => 'Apply tag:', 'param' => 'disabled')
								   );
	
	$user_flags_array = tep_get_user_flags();
	$flag_icon_array = array(	'flagOrange' => array('on' => 'icon_status_orange_yes.gif', 'off' => 'icon_status_no.gif'),
								'flagPink' => array('on' => 'icon_status_pink_yes.gif', 'off' => 'icon_status_no.gif'),
								'flagCyan' => array('on' => 'icon_status_cyan_yes.gif', 'off' => 'icon_status_no.gif'),
								'flagRed' => array('on' => 'icon_status_red_yes.gif', 'off' => 'icon_status_no.gif')
							);
	
	$extra_detail = array ();
	$payment_detail = array ();
	
	for ($status_count=0; $status_count < count($show_order_status); $status_count++) {
		$order_status_id = $show_order_status[$status_count];
		$status = preg_replace("/\s/", '_', $status_options[$order_status_id]);
		echo tep_draw_hidden_field($status.'_order_str', '', ' id="'.$status.'_order_str" ');
		
		$total_amount_array = array();
		$mirror_for_delete_tag = array();
		$status_dependent_tag_array = $tag_selection_general;
		$order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('".$order_status_id."', orders_tag_status_ids) AND filename='".FILENAME_STATS_ORDERS_TRACKING."';";
		$order_tag_result_sql = tep_db_query($order_tag_select_sql);
		while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
			$status_dependent_tag_array[] = array('id' => 'otag_'.$order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$order_tag_row["orders_tag_name"]);
			$mirror_for_delete_tag[] = array('id' => 'rmtag_'.$order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$order_tag_row["orders_tag_name"]);
		}
		$status_dependent_tag_array[] = array('id' => 'nt', 'text' => '&nbsp;&nbsp;&nbsp;New tag ...');
		$status_dependent_tag_array[] = array('id' => '', 'text' => 'Remove tag:', 'param' => 'disabled');
		$status_dependent_tag_array = array_merge($status_dependent_tag_array, $mirror_for_delete_tag);
		
		$total_colspan = 13;
		
		if ($order_status_id == 2 || $order_status_id == 3) {
			$total_colspan++;
		}
?>
							<table border="0" width="100%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
  								<tr>
    								<td colspan="<?=$order_status_id==PARTIAL_DELIVERY_STATUS ? 12 : 11?>">
    									<span class="pageHeading"><?=$status_options[$order_status_id]?></span>
<?		echo '<span id="'.$status.'_nav"></span>';
		if ($view_payment_details_permission == true) echo '<span id="'.$status.'_sep"></span><span id="'.$status.'_payment_nav"></span>';
		echo '&nbsp;&nbsp;<span id="'.$status.'_tag_nav" class="hide">'.tep_draw_pull_down_menu($status."_tag_selector", $status_dependent_tag_array, '', ' id="'.$status.'_tag_selector" onChange="orderListsOptions(this, \''.$order_status_id.'\', \''.(int)$languages_id.'\', \'\', true);"').'</span>';
?>
									</td>
			  					</tr>
								<tr>
									<td width="5%" align="center" class="ordersBoxHeading" colspan="2">No.</td>
								    <td width="11%" class="ordersBoxHeading">Date</td>
								    <td width="19%" class="ordersBoxHeading">Tag</td>
								    <td class="ordersBoxHeading">Name</td>
								    <td width="8%" align="center" class="ordersBoxHeading">Flag</td>
									<? if ($customer_view_email_listing_permission) { ?>
								    <td class="ordersBoxHeading">Email</td>
									<? } ?>
								    <td width="6%" class="ordersBoxHeading">Member Group</td>
								    <td width="3%" class="ordersBoxHeading">Customer Status</td>
								<?
									if ($order_status_id == 2 || $order_status_id == 3) {
										echo '<td width="7%" class="ordersBoxHeading">Delivery Status</td>';
									}
								?>
								    <td width="9%" class="ordersBoxHeading" align="right">Total</td>
								    <td width="3%" class="ordersBoxHeading" align="center">&nbsp</td>
								<?	echo '<td width="4%" class="ordersBoxHeading" align="center">'.TABLE_HEADING_LOCKED_BY.'</td>'; ?>
									<td width="4%" class="ordersBoxHeading" align="center">Actions</td>
								</tr>
<?
		$status_tag_array = array();
		if (isset($_SESSION['order_lists_param']["status_".$order_status_id]) && count($_SESSION['order_lists_param']["status_".$order_status_id])) {
			foreach ($_SESSION['order_lists_param']["status_".$order_status_id] as $tag_id) {
				if ($tag_id == 'no_tag') {
					$status_tag_array[] = " o.orders_tag_ids='' ";
				} else {
					$status_tag_array[] = " FIND_IN_SET('".$tag_id."', o.orders_tag_ids) ";
				}
			}
		}
		$order_tag_where_str = count($status_tag_array) ? ' ('.implode(' or ', $status_tag_array).') ' : ' 1 ';
		$orders_select_sql = '';
		if (count($union_array) > 1) {
			$lastElement = end($union_array);
			foreach ($union_array as $extra_query) {
				if(!empty($extra_query['table'])) {
					$temp_select = $orders_select_str;
					$orders_select_str .= $extra_query['table'];
				}
				if(!empty($extra_query['condition'])) {
					$temp_condition = $orders_where_str;
					$orders_where_str .= $extra_query['condition'];
				}
				if($extra_query == $lastElement) {
					$orders_select_sql .= $orders_select_str . $orders_where_str . ' and o.orders_status = ' . $show_order_status[$status_count] . ' and ' . $order_tag_where_str;
				} else {
					$orders_select_sql .= $orders_select_str . $orders_where_str . ' and o.orders_status = ' . $show_order_status[$status_count] . ' and ' . $order_tag_where_str . ' UNION ';
				}
				$orders_where_str = $temp_condition;
				$orders_select_str = $temp_select;
			}
			$orders_select_sql .= $orders_group_by_str . $orders_order_by_str;
		} else {
			$orders_select_sql = $orders_select_str . $orders_where_str . ' and o.orders_status = ' . $show_order_status[$status_count] . ' and ' . $order_tag_where_str . $orders_group_by_str . $orders_order_by_str;
		}

		if ($show_records != "ALL") {
			$orders_split_object = new splitPageResults($HTTP_GET_VARS['page'.$order_status_id], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $orders_select_sql, $orders_select_sql_numrows, true);
		}
		
		$orders_result_sql = tep_db_query($orders_select_sql);
		$row_count = 0;
		while ($row = tep_db_fetch_array($orders_result_sql)) {
			$cust_name_style = '';
			$customer_flags_array = explode(',', $row['customers_flag']);
			$flag_icon_html = '';
			foreach ($user_flags_array as $flag_id => $flag_info) {
				$flag_label = $flag_info['user_flags_name'];
				$flag_icon_img_src = DIR_WS_IMAGES . (in_array($flag_id, $customer_flags_array) ? $flag_icon_array[$flag_info['user_flags_css_style']]['on'] : $flag_icon_array[$flag_info['user_flags_css_style']]['off']);
				$flag_icon_html .= tep_image($flag_icon_img_src, $flag_label, 11, 11) . '&nbsp;';
				
				if (in_array($flag_id, $customer_flags_array))	$cust_name_style = $flag_info['user_flags_css_style'];
			}
			
			if (tep_not_null($row['orders_follow_up_datetime'])) {
				$follow_up_icon = tep_day_diff(date('Y-m-d H:i:s'), $row['orders_follow_up_datetime']) !== FALSE ? tep_image(DIR_WS_ICONS."bell_grey.gif", "Follow Up On ".$row['orders_follow_up_datetime'], "16", "16", 'align="top"') : tep_image(DIR_WS_ICONS."bell.gif", "Follow Up On ".$row['orders_follow_up_datetime'], "16", "16", 'align="top"');
			} else {
				$follow_up_icon = "&nbsp;";
			}
			
			$ordernummer = $row['orders_id'];
  			$remote_addr = $row['remote_addr'];
  			$orderdate = date('Y-m-d H:i', strtotime($row['date_purchased']));
  			$order_user = '<span class="'.$cust_name_style.'">'.$row['customers_name'].'<span>';
  			$order_useremail = $row['customers_email_address'];
  			$amt = $row["order_total"];
  			$customer_status = $row["customers_status"];
			
			$payment_detail[$status]['order_id'][] = $ordernummer;
  			
  			$cust_group_select_sql = "SELECT cg.customers_groups_name FROM " . TABLE_CUSTOMERS . " AS c INNER JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg ON c.customers_groups_id=cg.customers_groups_id WHERE c.customers_id = '" . $row['customers_id'] . "'";
  			$cust_group_result_sql = tep_db_query($cust_group_select_sql);
  			if ($cust_group_row = tep_db_fetch_array($cust_group_result_sql)) {
  				$cust_member_grp_name = $cust_group_row["customers_groups_name"];
  			} else { $cust_member_grp_name = TEXT_OPTION_NOT_APPLICABLE; }
  			
  			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
			
  			$tags_str = '';
  			if (tep_not_null($row["orders_tag_ids"])) {
  				$orders_assigned_tag_select_sql = "SELECT orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET(orders_tag_id, '".$row["orders_tag_ids"]."') AND filename='".FILENAME_STATS_ORDERS_TRACKING."';";
				$orders_assigned_tag_result_sql = tep_db_query($orders_assigned_tag_select_sql);
				while ($orders_assigned_tag_row = tep_db_fetch_array($orders_assigned_tag_result_sql)) {
					$tags_str .= $orders_assigned_tag_row["orders_tag_name"] . ', ';
				}
				if (substr($tags_str, -2) == ', ') 	$tags_str = substr($tags_str, 0, -2);
  			}
  			
  			if ($order_status_id == PARTIAL_DELIVERY_STATUS) {
	  			$lock_orders_select_sql = "SELECT o.orders_locked_by, a.admin_email_address FROM " . TABLE_ORDERS . " AS o inner join " . TABLE_ADMIN . " AS a ON o.orders_locked_by=a.admin_id WHERE orders_id = '" . $ordernummer . "'";
				$lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
				$lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);
				
				if (tep_not_null($lock_orders_row["admin_email_address"])) {
					$order_locked = true;
					$lock_owner_username = str_replace(strstr($lock_orders_row["admin_email_address"], '@'), '', $lock_orders_row["admin_email_address"]);
				} else {
					$order_locked = false;
				}
			} else { $order_locked = false; }
			
			$days_count = '';
			if ($order_status_id == '7') {	// Verifying Status
				if (isset($payment_module_info[$row['payment_methods_id']]['post_auth_time_out']) && $payment_module_info[$row['payment_methods_id']]['post_auth_time_out'] !== '') {
					$authMode = '';
					if ($row['payment_methods_parent_id'] == '17') {	// 17 = worldpay
						$wp_trans_info_select_sql = "SELECT authorisation_mode FROM " . TABLE_PAYMENT_EXTRA_INFO . " WHERE orders_id = '" . $ordernummer . "'";
						$wp_trans_info_result_sql= tep_db_query($wp_trans_info_select_sql);
						$wp_trans_info_row = tep_db_fetch_array($wp_trans_info_result_sql);
						$authMode = $wp_trans_info_row["authorisation_mode"];
					}
					
					if ( ($authMode == 'E' || $authMode == '') && ($days_lapse = tep_day_diff($row['date_purchased'], date('Y-m-d H:i:s'))) != FALSE) {
						$days_count = (int)$days_lapse;
						$days_count = $days_count > $payment_module_info[$row['payment_methods_id']]['post_auth_time_out'] ? '<span class="redIndicator"><b>E</b></span>' : $days_count;
					}
				} else if ($row['payment_methods_parent_id'] == '64') {	// 64 = bibit
					$bibit_status_select_sql = "SELECT bibit_status FROM " . TABLE_BIBIT . " WHERE orders_id = '" . tep_db_input($ordernummer) . "'";
					$bibit_status_result_sql = tep_db_query($bibit_status_select_sql);
					$bibit_status_row = tep_db_fetch_array($bibit_status_result_sql);
					
					if ($bibit_status_row['bibit_status'] == 'AUTHORISED') {
						$days_lapse = tep_day_diff($row['date_purchased'], date('Y-m-d H:i:s'));
						$days_count = (int)$days_lapse;
					}
				}
			}
			
			$total_amount_array[$row['currency']] += $currencies->apply_currency_exchange($amt, $row['currency'], $row['currency_value']);
?>
								<tbody id="read_<?=$ordernummer?>" class="<?=$row["orders_read_mode"] > 0 ? '' : 'boldText'?>">
								<tr id="<?=$status.'_main_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?=$status.'_sub_'.$row_count?>##<?=$status.'_sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?=$status.'_sub_'.$row_count?>##<?=$status.'_sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?=$status.'_sub_'.$row_count?>##<?=$status.'_sub2_'.$row_count?>')">
									<td class="ordersRecords"><?=$follow_up_icon?></td>
									<td class="ordersRecords" nowrap><a href="<?=tep_href_link("orders.php", 'oID='.$ordernummer.'&action=edit', 'NONSSL')?>"><?=$ordernummer?></a></td>
							      	<td class="ordersRecords"><?=$orderdate?></td>
							      	<td class="ordersRecords"><span class="greenIndicator" id="tag_<?=$ordernummer?>"><?=$tags_str?></span>&nbsp;</td>
							      	<td class="ordersRecords"><?= ($customer_status == '9') ? '<span class="redIndicator">'.TEXT_ACCOUNT_CLOSURE_CONFIRMED.'</span>' : $order_user?></td>
							      	<td class="ordersRecords" align="center" nowrap><?=$flag_icon_html?></td>
							      	<? if ($customer_view_email_listing_permission) { ?>
							      	<td class="ordersRecords"><?=($view_customer_details_permission ? (($customer_status == '9') ? TEXT_HIDDEN : $order_useremail) : TEXT_HIDDEN)?></td>
							    	<? } ?>
							      	<td class="ordersRecords"><?=$cust_member_grp_name?></td>
							      	<td class="ordersRecords" align="center">
						      		<?
							      		if (tep_not_null($customer_status)) {
							      			if ($customer_status == '9') {
												echo tep_image(DIR_WS_ICONS . 'locked.gif', IMAGE_ICON_STATUS_LOCKED, 16, 16);
											} else if ($customer_status == '1') {
								      			echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
								      		} else {
								      			echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
								      		}
								      	} else {
								      		echo TEXT_OPTION_NOT_APPLICABLE;
								      	}
						      		?>
							      	</td>
									<? 	//Delivery Status Images
										if ($order_status_id == 2 || $order_status_id == 3) {
											$order = new order((int)$row['orders_id']);
											echo '	<td class="ordersRecords" align="left">';
											foreach ($order->price_info_icons as $sub_status => $icon_info) {
												$icon_img_src = DIR_WS_IMAGES . ($order->price_info[$sub_status] > 0 ? $icon_info['on'] : $icon_info['off']);
												echo '&nbsp;' . tep_image($icon_img_src, $icon_info['label'], 10, 10) . '&nbsp;';
											}
											echo '	</td>';
										}
									?>
			        				<td class="ordersRecords" align="right"><?=$currencies->format($amt, true, $row['currency'], $row['currency_value'])?></td>
			        				<td class="ordersRecords" align="center" valign="top">
		        						<?=(tep_not_null($row['payment_methods_id'])) ? '<div style="float: left; width:10px; height:12px; border: 1px solid black; margin-right: 2px; background-color:'.($payment_module_info[$row['payment_methods_parent_id']]['display_colour'] ? $payment_module_info[$row['payment_methods_parent_id']]['display_colour'] : '#E1E1E2').'; font-size:9px">'.$days_count.'</div>' : ''?>
		        						<?=($row['order_sc'] > 0) ? '<div style="float: left; width:10px; height:12px; border: 1px solid black; background-color:'.($payment_module_info[SYSTEM_PAYMENT_STORE_CREDITS]['display_colour'] ? $payment_module_info[SYSTEM_PAYMENT_STORE_CREDITS]['display_colour'] : '#E1E1E2').'; font-size:9px"></div>' : ''?>
			        				</td>
			  					<? 	//if ($order_status_id == PARTIAL_DELIVERY_STATUS) {
										//echo '<td class="ordersRecords" align="center">'.($order_locked == true ? ($lock_orders_row["orders_locked_by"] == $login_id ? '<b>'.$lock_owner_username.'</b>' : (in_array($lock_orders_row["orders_locked_by"], $active_admin_staff_array) ? $lock_owner_username : '<span class="redIndicator">'.$lock_owner_username.'</span>')) : '&nbsp;').'</td>';
										echo '<td class="ordersRecords" align="center">'.($order_locked == true ? ($lock_orders_row["orders_locked_by"] == $login_id ? '<b>'.$lock_owner_username.'</b>' : $lock_owner_username ) : '&nbsp;').'</td>';
									//}
								?>
									<td class="ordersRecords" align="center">
			  							<?='<a href="' . tep_href_link("orders.php", 'oID='.$ordernummer.'&action=edit', 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "14", "13", 'align="top"').'</a>'?>
			  						</td>
								</tr>
								</tbody>
								<tbody id="<?=$status."_".$ordernummer.'_order_sec'?>" class="hide">
                    				<tr id="<?=$status.'_sub_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?=$status.'_main_'.$row_count?>##<?=$status.'_sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?=$status.'_main_'.$row_count?>##<?=$status.'_sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?=$status.'_main_'.$row_count?>##<?=$status.'_sub2_'.$row_count?>')">
			  							<td class="ordersRecords">&nbsp;</td>
			  							<td colspan="<?=($total_colspan-2)?>">
			  								<div id="<?=$status.'_'.$ordernummer.'_order'?>"></div>
			  							</td>
			  							<td class="ordersRecords">&nbsp;</td>
			  						</tr>
				               	</tbody>
<?			if ($view_payment_details_permission == true) { ?>
				               	<tbody id="<?=$status."_".$ordernummer."_payment_sec"?>" class="hide">
				               		<tr id="<?=$status.'_sub2_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?=$status.'_main_'.$row_count?>##<?=$status.'_sub_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?=$status.'_main_'.$row_count?>##<?=$status.'_sub_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?=$status.'_main_'.$row_count?>##<?=$status.'_sub_'.$row_count?>')">
				               			<td class="ordersRecords">&nbsp;</td>
			  							<td colspan="<?=($total_colspan-2)?>">
			  								<div id="<?=$status.'_'.$ordernummer.'_payment'?>"></div>
			  							</td>
			  							<td class="ordersRecords">&nbsp;</td>
			  						</tr>
				               	</tbody>
<?
			}
			$row_count++;
			unset($amt);
		}
		$extra_detail[$status] = $row_count;
?>
								<tr>
									<td colspan="10" align="right" class="smallText">
										<?
	                						if (count($total_amount_array)) {
	                							ksort($total_amount_array);
	                							foreach ($total_amount_array as $cur_code => $total_cur_amount) {
	                								echo $currencies->format($total_cur_amount, false, $cur_code) . '<br>';
	                							}
	                						}
	                					?>
									</td>
									<td colspan="2">&nbsp;</td>
								</tr>
								<tr>
			            			<td colspan="12">
			            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
			            					<tr>
			                					<td class="smallText" align="right" valign="top" colspan="2">
			                					
			                					</td>
			                				</tr>
			              					<tr>
			                					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_ORDERS, tep_db_num_rows($orders_result_sql) > 0 ? "1" : "0", tep_db_num_rows($orders_result_sql), tep_db_num_rows($orders_result_sql)) : $orders_split_object->display_count($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'.$order_status_id], TEXT_DISPLAY_NUMBER_OF_ORDERS)?></td>
			                					<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $orders_split_object->display_links($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'.$order_status_id], tep_get_all_get_params(array('page'.$order_status_id, 'cont', 'subaction', 'criteria_id'))."cont=1", 'page'.$order_status_id)?></td>
			              					</tr>
			            				</table>
			            			</td>
          						</tr>
							</table>
<?	} //end for loop ?>
						</td>
					</tr>
<?	if (count($payment_module_info) && count($show_order_status)) { ?>
					<tr>
          				<td colspan="2">
          					<table border="0" cellspacing="1" cellpadding="0">
<?
								$pm_cnt = 0;
								foreach ($payment_module_info as $payment_id => $payment_res) {
									if ($pm_cnt == 0) {
										echo '	<tr>';
									} else if ($pm_cnt%5 == 0) {
										echo '	</tr>
												<tr>';
									}
									echo '	<td>
												<div style="width:10px; height:10px; border: 1px solid black; background-color:'.$payment_res["display_colour"].'; font-size:5px"></div>
											</td>
											<td class="smallText" NOWRAP>&nbsp;'.$payment_res['method_title'].'&nbsp;&nbsp;</td>';
									$pm_cnt++;
								}
?>
								</tr>
							</table>
          				</td>
          			</tr>
<?	} ?>
	          		<script language="javascript">
					<!--
					var isProcessing = false;
					<?	foreach ($payment_detail as $status_name => $res) {
							$order_str = count($res['order_id']) ? implode(',', $res['order_id']) : '';?>
							document.getElementById('<?=$status_name?>'+'_order_str').value = "<?=$order_str?>";
							//var payment_state = GetCookie('<?=$status_name.'_payment_info_link'?>');
					<?		if ($view_payment_details_permission == true && count($payment_detail)) { ?>
								document.getElementById('<?=$status_name?>'+'_sep').innerHTML = "&nbsp;|&nbsp;";
								paymentInfo('<?=$status_name?>', 0, false, '<?=(int)$languages_id?>');
					<?		}
						}
						
						foreach ($extra_detail as $key => $count) { ?>
							var <?=$key?>_count = <?=$count?>;
							if (eval(<?=$key?>_count) > 0) {
								/*
								document.getElementById('<?=$key?>'+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('<?=$key?>', 'show')\">Show Ordered Details</a>";
								var state = GetCookie('<?=$key?>');
								if (state != '') {
									hideShow('<?=$key?>', (state == 1 ? 'show' : 'hide'));
								}
								*/
								document.getElementById('<?=$key?>_tag_nav').className = 'show';
							}
							/* else {
								document.getElementById('<?=$key?>'+'_nav').innerHTML = "";
								SetCookie('<?=$key?>', '', -1);	// erasing this status's cookie
							}*/
							
							orderInfo('<?=$key?>', 0, false, '<?=(int)$languages_id?>', '<?=SID?>');
					<?	} ?>
						
						function hideShow(groupName, styleClass) {
							var row_count = eval(groupName+"_count");
							for (var i=0; i<row_count; i++) {
								document.getElementById(groupName+"_"+i).className = styleClass;
							}
							
							if (styleClass == "show") {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'hide')\">Hide Ordered Details</a>";
								SetCookie(groupName, '1');
							} else {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'show')\">Show Ordered Details</a>";
								SetCookie(groupName, '0');
							}
						}
						
						function showOverEffect(object, class_name, extra_row) {
							rowOverEffect(object, class_name);
							var rowObjArray = extra_row.split('##');
	  						for (var i = 0; i < rowObjArray.length; i++) {
	  							if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
	  								rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
	  							}
	  						}
						}
						
						function showOutEffect(object, class_name, extra_row) {
							rowOutEffect(object, class_name);
							var rowObjArray = extra_row.split('##');
					  		for (var i = 0; i < rowObjArray.length; i++) {
					  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					  				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
					  			}
					  		}
						}
						
						function showClicked(object, class_name, extra_row) {
							/*if (isProcessing == true) {
								return;
							}*/
							rowClicked(object, class_name);
							/*
							if (object.id.indexOf('_main_') != -1) {
								var temp_array = object.id.split('_');
								refreshOrderListsOptions(temp_array[0], '<?=(int)$languages_id?>');
							}*/
							var rowObjArray = extra_row.split('##');
					  		for (var i = 0; i < rowObjArray.length; i++) {
					  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
					  				/*
					  				if (rowObjArray[i].indexOf('_main_') != -1) {
										var temp_array = rowObjArray[i].split('_');
										refreshOrderListsOptions(temp_array[0], '<?=(int)$languages_id?>');
									}*/
					  			}
	  						}
						}
						
					<?	if (tep_not_null($_SESSION['order_lists_param']["page_refresh"])) { ?>
							var page_url = "<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('oID', 'cont'))."cont=1")?>";
							setAutoRefresh("<?=$_SESSION['order_lists_param']["page_refresh"]?>", page_url);
					<?	} ?>
					//-->
					</script>
<?
} else {
?>
					<tr>
						<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
									<td class="main" align="right">&nbsp;
									<?
									if (count($saved_search_options) > 1) {
										echo tep_draw_form('goto_search_form', FILENAME_STATS_ORDERS_TRACKING, 'action=show_report', 'post');
										echo tep_draw_hidden_field('subaction', 'goto_search');
										echo tep_draw_pull_down_menu("criteria_id", $saved_search_options, '', 'onChange="if(this.value != \'\') { this.form.submit(); }"');
										echo "</form>";
									}
									?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<?
						echo tep_draw_form('order_lists_criteria', FILENAME_STATS_ORDERS_TRACKING, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', '');
						echo tep_draw_hidden_field('order_list_subaction', 'do_search', ' id="order_list_subaction" ');
					?>
					<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="2" cellpadding="0">
<?
	$search_by_field_options = array 	(	array ('id' => 'o.orders_id', "text" => "OffGamers Order ID"),
											array ('id' => 'o.customers_email_address', "text" => "OffGamers Email"),
											array ('id' => 'o.customers_id', "text" => "OffGamers Customer ID"),
											array ('id' => 'join_alipay_trade_no', "text" => "Alipay Trade No"),
											array ('id' => 'join_cimb_reference_no', "text" => "CIMB Reference No"),
											array ('id' => 'join_mb_id', "text" => "Moneybookers Transaction ID"),
											array ('id' => 'join_mb_email', "text" => "Moneybookers Email"),
											array ('id' => 'join_paypal_id', "text" => "PayPal Transaction ID"),
											array ('id' => 'join_paypal_email', "text" => "PayPal Email"),
											array ('id' => 'join_qfpay_id', "text" => "QFPay Transaction ID")
										);
	
	$product_type_array = array();
	$product_type_array[] = array ('id' => '', 'text' => "Please Select");
	
	$custom_products_type_info_select_sql = "	SELECT custom_products_type_id, custom_products_type_name FROM " . TABLE_CUSTOM_PRODUCTS_TYPE;
	$custom_products_type_info_result_sql = tep_db_query($custom_products_type_info_select_sql);
	while ($custom_products_type_info_row = tep_db_fetch_array($custom_products_type_info_result_sql)) {
		$product_type_array[] = array ('id' => $custom_products_type_info_row['custom_products_type_id'], 'text' => $custom_products_type_info_row['custom_products_type_name']);
	}
	
	$refresh_options = array 	(	array ('id' => '', "text" => "No Refresh"),
									array ('id' => '1:00', "text" => "1"),
									array ('id' => '5:00', "text" => "5"),
									array ('id' => '15:00', "text" => "15")
								);
	
	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
								array ('id' => '10', "text" => "10"),
								array ('id' => '20', "text" => "20"),
								array ('id' => '50', "text" => "50"),
								array ('id' => 'ALL', "text" => "All")
							);
	
	$customer_group_options = array( array ('id' => '', "text" => "All Groups") );
	
    $cust_group_select_sql = "SELECT customers_groups_id, customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS . " ORDER BY sort_order, customers_groups_name ASC";
	
	$cust_group_result_sql = tep_db_query($cust_group_select_sql);
	while ($cust_group_row = tep_db_fetch_array($cust_group_result_sql)) {
		$customer_group_options[] = array ('id' => $cust_group_row["customers_groups_id"], "text" => $cust_group_row["customers_groups_name"]);
	}
?>
								<tr>
									<td class="main" width="12%"><?=ENTRY_HEADING_ORDER_START_DATE?></td>
					    			<td class="main">
					    				<table border="0" cellspacing="2" cellpadding="0">
					    					<tr>
					    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('start_date', $_SESSION['order_lists_param']["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_lists_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_lists_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
					    						<td class="main" width="5%">&nbsp;</td>
					    						<td class="main" valign="top" nowrap><?=ENTRY_HEADING_ORDER_END_DATE?></td>
					    						<td class="main" width="1%">&nbsp;</td>
					    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('end_date', $_SESSION['order_lists_param']["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_lists_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_lists_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
					    						<td class="main" width="5%">&nbsp;</td>
					    						<td class="main" valign="top" nowrap><?=ENTRY_HEADING_ORDER_FOLLOW_UP_DATE?></td>
					    						<td class="main" width="1%">&nbsp;</td>
					    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('follow_up_date', $_SESSION['order_lists_param']["follow_up_date"], 'id="follow_up_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.order_lists_criteria.follow_up_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.order_lists_criteria.follow_up_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
					    					</tr>
					    				</table>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
								<tr>
									<td class="main"><?=ENTRY_SEARCH_BY_FIELD?></td>
					    			<td class="main">
					    				<?=tep_draw_input_field('search_by_field_value', $_SESSION['order_lists_param']["search_by_field_value"], ' id="search_by_field_value" size="40" onKeyUP="if (trim_str(this.value)==\'\') { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\') { resetControls(this); }" onKeyPress="return noEnterKey(event)" onkeydown="if ((event.which && event.which == 13) || (event.keyCode && event.keyCode == 13)){document.getElementById(\'OrderListsBtn\').click();return false;}else return true;"')?>&nbsp;
					    				<?=tep_draw_pull_down_menu("search_by_field", $search_by_field_options, $_SESSION['order_lists_param']["search_by_field"], '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main"><?=ENTRY_HEADING_ORDER_TOTAL?></td>
					    			<td class="main"><?=tep_draw_input_field('order_total', $_SESSION['order_lists_param']["order_total"], ' size="15" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
					    		</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
<?	if ($search_by_cat_permission) { ?>
								<tr>
									<td class="main" width="12%"><?=ENTRY_CATEGORY?></td>
					    			<td class="main">
                                    <?php    
                                        echo tep_draw_input_field('cat_id', $_SESSION['order_lists_param']["cat_id"], ' id="cat_id" size="15" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"');
					    				echo '&nbsp;<a href="javascript:openDGDialog(\''. tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=category_cache&fieldname=cat_id') . '\', 600, 250, \'\');">(Category List)</a>';
                                    ?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
<?	} ?>
								<tr>
									<td class="main"><?=ENTRY_HEADING_PRODUCT_ID?></td>
					    			<td class="main">
					    			<?
					    				echo tep_draw_input_field('product_id', $_SESSION['order_lists_param']["product_id"], ' id="product_id" size="15" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"');
					    				echo '&nbsp;<a href="javascript:openDGDialog(\''. tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'fname=' . FILENAME_ORDERS) . '\', 600, 250, \'\');">(Product List)</a>';
					    			?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main"><?=ENTRY_HEADING_PRODUCT_TYPE?></td>
					    			<td class="main"><?=tep_draw_pull_down_menu('product_type', $product_type_array, $_SESSION['order_lists_param']["product_type"], 'id="product_type"')?></td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
        						<tr>
									<td class="main" width="12%" valign="top"><?=ENTRY_HEADING_ORDER_STATUS?></td>
					    			<td>
					    				<table border="0" cellspacing="2" cellpadding="0">
					    			<?
					    				if (count($status_options)) {
				    						echo '<tr><td class="main">'.tep_draw_checkbox_field('order_status_any', '1', isset($_SESSION['order_lists_param']) && count($_SESSION['order_lists_param']["order_status"]) ? false : true, '', 'id="order_status_any" onClick="set_status_option(this);"') . '</td><td class="main" colspan="'.(count($status_options)*2-1).'">'.TEXT_ANY.'</td></tr>';
				    						echo '<tr>';
				    						foreach ($status_options as $id => $title) {
				    							$order_status_display_str = '';
				    							$order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('".$id."', orders_tag_status_ids) AND filename='".FILENAME_STATS_ORDERS_TRACKING."' ;";
												$order_tag_result_sql = tep_db_query($order_tag_select_sql);
												
					    						$order_status_display_str .= 
					    							'	<td class="main" valign="top">'.
					    									tep_draw_checkbox_field('order_status[]', $id, isset($_SESSION['order_lists_param']) ? (is_array($_SESSION['order_lists_param']["order_status"]) && in_array($id, $_SESSION['order_lists_param']["order_status"]) ? true : false) : (false), '', 'onClick=verify_status_selection("--");') . '
					    								</td>
					    								<td class="main" valign="top">';
				    							$order_status_display_str .= 
													'	<fieldset class="selectedFieldSet">
															<legend align=left class=SectionHead>'.$title.'</legend>
															<table border="0" cellspacing="0" cellpadding="0">
                                                                                                                        <tr><td class="smallText" valign="top" colspan="2" style="padding-bottom: 5px"><a href="javascript:void(0)" onClick="verify_status_selection(\''.$id.'\')">Select All</a></td></tr>
																<tr><td class="smallText" valign="top">' . tep_draw_checkbox_field('status_'.$id.'[]', 'no_tag', isset($_SESSION['order_lists_param']) ? (is_array($_SESSION['order_lists_param']['status_'.$id]) && in_array('no_tag', $_SESSION['order_lists_param']['status_'.$id]) ? true : false) : (false), '', 'disabled') . '</td><td class="smallText">No Tag</td></tr>';
												while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
													$order_status_display_str .= '<tr><td class="smallText" valign="top">' . tep_draw_checkbox_field('status_'.$id.'[]', $order_tag_row["orders_tag_id"], isset($_SESSION['order_lists_param']) ? (is_array($_SESSION['order_lists_param']['status_'.$id]) && in_array($order_tag_row["orders_tag_id"], $_SESSION['order_lists_param']['status_'.$id]) ? true : false) : (false), '', 'disabled') . '</td><td class="smallText">' . $order_tag_row["orders_tag_name"] . '</td></tr>';
												}
												$order_status_display_str .= '
															</table>
														</fieldset>';
					    						$order_status_display_str .= '</td>';
					    						echo $order_status_display_str;
					    					}
					    					echo '</tr>';
					    				}
					    			?>
					    				</table>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main" width="12%" valign="top"><?=ENTRY_HEADING_PAYMENT_METHOD?></td>
					    			<td class="main">
<?
                                        /*-- payment method --*/
                                        $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_title, payment_methods_parent_id 
                                                                        FROM " . TABLE_PAYMENT_METHODS . "
                                                                        WHERE payment_methods_receive_status = '1' 
                                                                        ORDER BY payment_methods_title";
                                        $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
                                        $payment_methods_array      = array();
                                        $payment_gateways_array     = array();

                                        $payment_gateways_array[SYSTEM_PAYMENT_STORE_CREDITS] = 'Store Credits';

                                        while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                                            if ($payment_methods_row['payment_methods_parent_id']>0) {
                                                $payment_methods_array[$payment_methods_row['payment_methods_parent_id']][$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
                                            } else {
                                                $payment_gateways_array[$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
                                            }
                                        }
        ?>
                                        <ul class="myTree">
                                            <li style="-moz-user-select: none;" class="treeItem" id="any_pg">
                                                <span class="textHolder payment_gateways">
                                                    &nbsp;&nbsp;&nbsp;
                                                    <input type="checkbox" class="pg_any" name="payment_gateways_id[]" value="any" <?=(!((isset($_SESSION['order_lists_param']['payment_gateways_id']) && count($_SESSION['order_lists_param']['payment_gateways_id'])) || (isset($_SESSION['order_lists_param']['payment_methods_id']) && count($_SESSION['order_lists_param']['payment_methods_id']))) || (isset($_SESSION['order_lists_param']['payment_gateways_id']) && count($_SESSION['order_lists_param']['payment_gateways_id']) && in_array('any',$_SESSION['order_lists_param']['payment_gateways_id'])) ? ' checked ':'')?>>
                                                    <i>Any</i>
                                                </span>
                                            </li>
        <?
                                        $pg_checkbox_js = '';
                                        
                                        foreach ($payment_gateways_array as $payment_gateways_id => $payment_gateways_title) {
        ?>
                                            <li style="-moz-user-select: none;" class="treeItem" id="<?=$payment_gateways_id?>">
        <?
                                            if (isset($payment_methods_array[$payment_gateways_id]) && count($payment_methods_array[$payment_gateways_id])) {
                                                $pm_checked_flag = false;

                                                if (isset($_SESSION['order_lists_param']['payment_methods_id']) && count($_SESSION['order_lists_param']['payment_methods_id'])) {
                                                    foreach ($payment_methods_array[$payment_gateways_id] as $check_pm_id=>$check_pm_text) {
                                                        if (in_array($check_pm_id, $_SESSION['order_lists_param']['payment_methods_id'])) {
                                                            $pm_checked_flag = true;
                                                            break;
                                                        }
                                                    }
                                                }

                                                if ($pm_checked_flag) {
        ?>
                                                    <img src="images/icon-collapse-small.gif" class="expandImage" width="9" height="7">
                                                    <span class="textHolder payment_gateways">
                                                        <input type="checkbox" name="payment_gateways_id[]" value="<?=$payment_gateways_id?>" <?=(isset($_SESSION['order_lists_param']['payment_gateways_id']) && count($_SESSION['order_lists_param']['payment_gateways_id']) && in_array($payment_gateways_id,$_SESSION['order_lists_param']['payment_gateways_id'])?' checked ':'')?>>
                                                        <?=$payment_gateways_title?>
                                                    </span>
                                                    <ul style="display: display;" class="open">
        <?
                                                } else {
        ?>
                                                    <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                                        <span class="textHolder payment_gateways">
                                                            <input type="checkbox" name="payment_gateways_id[]" value="<?=$payment_gateways_id?>" <?=(isset($_SESSION['order_lists_param']['payment_gateways_id']) && count($_SESSION['order_lists_param']['payment_gateways_id']) && in_array($payment_gateways_id,$_SESSION['order_lists_param']['payment_gateways_id'])?' checked ':'')?>>
                                                            <?=$payment_gateways_title?>
                                                        </span>
                                                    <ul style="display: none;">
        <?
                                                }

                                                foreach ($payment_methods_array[$payment_gateways_id] as $payment_methods_id => $payment_methods_title) {
        ?>
                                                    <li style="-moz-user-select: none;" class="treeItem" id="<?=$payment_methods_id?>">&nbsp;&nbsp;
                                                        <span class="textHolder payment_methods">
                                                            <input type="checkbox" name="payment_methods_id[]" value="<?=$payment_methods_id?>" <?=(isset($_SESSION['order_lists_param']['payment_methods_id']) && count($_SESSION['order_lists_param']['payment_methods_id']) && in_array($payment_methods_id,$_SESSION['order_lists_param']['payment_methods_id'])?' checked ':'')?>>
                                                            <?=$payment_methods_title?>
                                                        </span>
                                                    </li>
        <?
                                                }
        ?>
                                                </ul>
        <?
                                            } else {
        ?>
                                                <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                                <span class="textHolder payment_gateways">
                                                    <input type="checkbox" name="payment_gateways_id[]" value="<?=$payment_gateways_id?>" <?=(isset($_SESSION['order_lists_param']['payment_gateways_id']) && count($_SESSION['order_lists_param']['payment_gateways_id']) && in_array($payment_gateways_id,$_SESSION['order_lists_param']['payment_gateways_id'])?' checked ':'')?>>
                                                    <?=$payment_gateways_title?>
                                                </span>
        <?	
                                            }
        ?>
                                            </li>
              <script>
                                    function getReturnedValue(received_val, input_field) {
                                        jQuery('#' + input_field).val(received_val);
                                    }

                                    jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").click(function () {
                                        if (jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked")) {
                                            if (jQuery("li#<?= $payment_gateways_id ?> ul:visible").length) {
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", true);
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", true);
                                            } else {
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", true);
                                            }
                                        }
                                           else{
                                           
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", false);
                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", false);
                                        
                                          
                                        }
                                    });
                                    jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").click(function () {
                                    
                                                parent = $(this).closest(".treeItem").parent(); 
                                                selected = parent.find('input:checked');
                                                total = parent.find('input');
                                                
                                        if (selected.length == total.length) {
                                            jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", true);
                                        }
                                        else{
                                         jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", false);
                                        }
                                    });
                                    jQuery(document).ready(function () {
                                        if (jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked")) {
                                            jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", '');
                                        }
                                    });
                                                        </script>   <? } ?>                        </ul>

                                        <script>
                                            jQuery(document).ready(function() {
                                                <?php echo $pg_checkbox_js; ?>
                                                tree = jQuery('.myTree');
                                                jQuery('img.expandImage', tree.get(0)).click(
                                                    function() {
                                                        if (this.src.indexOf('spacer') == -1) {
                                                            subbranch = jQuery('ul', this.parentNode).eq(0);

                                                            if (subbranch.css('display') == 'none') {
                                                                subbranch.show();
                                                                this.src = 'images/icon-collapse-small.gif';
                                                            } else {
                                                                subbranch.hide();
                                                                this.src = 'images/icon-expand-small.gif';
                                                            }
                                                        }
                                                    }
                                                );
                                            });

                                            jQuery('.myTree li input').click(function(){
                                                if (jQuery(this).hasClass('pg_any')) {
                                                    jQuery('.myTree li input:not(.pg_any)').attr('checked',false);
                                                    jQuery('.myTree li .pg_any').attr('checked',true);
                                                } else {
                                                    if (jQuery('.myTree li input:not(.pg_any):checked').length > 0 ) {
                                                        jQuery('.myTree li .pg_any').attr('checked',false);
                                                    } else {
                                                        jQuery('.myTree li .pg_any').attr('checked',true);
                                                    }
                                                }
                                            });
                                        </script>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main" width="12%"><?=ENTRY_HEADING_CUSTOMER_GROUP?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("customer_group", $customer_group_options, tep_not_null($_SESSION['order_lists_param']["customer_group"]) ? $_SESSION['order_lists_param']["customer_group"] : '', '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main" width="12%"><?=ENTRY_HEADING_PAGE_REFRESH?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("page_refresh", $refresh_options, tep_not_null($_SESSION['order_lists_param']["page_refresh"]) ? $_SESSION['order_lists_param']["page_refresh"] : '', '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main"><?=ENTRY_HEADING_RECORDS_PER_PAGE?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION['order_lists_param']["show_records"]) ? $_SESSION['order_lists_param']["show_records"] : '', '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
        					</table>
        				</td>
        			</tr>
        			<tr>
	  					<td>
	  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
	  							<tr>
									<td class="main" width="12%">&nbsp;</td>
	  								<td nowrap class="main">
	  								<?
	  								if ($manage_search_criteria_permission) {
	  									echo '<input type="submit" name="SaveSearchBtn" value="Save Criteria As" title="Enter the name for this order lists criteria" class="inputButton" onClick="return form_checking(this.form, \'save_search\');">&nbsp;&nbsp;&nbsp;&nbsp;';
	  									echo tep_draw_input_field('search_name', $_SESSION['order_lists_param']["cur_criteria_name"], ' id="search_name" size="40" ');
	  								} else echo '&nbsp;';
	  								?>
	  								
	  								</td>
	  								<td align="right">
	  									<?=tep_image_submit('button_search.gif', IMAGE_SEARCH, " id='OrderListsBtn' onClick=\"return form_checking(this.form, 'do_search');\"")?>&nbsp;&nbsp;
	  									<a href="<?=tep_href_link(FILENAME_STATS_ORDERS_TRACKING, 'action=reset_session')?>"><?=tep_image_button('button_reset.gif', IMAGE_RESET)?></a>
	  								</td>
	  							</tr>
	  						</table>
	  					</td>
	  				</tr>
					</form>
					<script language="javascript"><!--
						function form_checking(form_obj, action) {
							if (action == 'do_search') {
								if (document.getElementById('search_by_field_value').value == '') {
									if (document.getElementById('start_date').value != '' && document.getElementById('end_date').value != '') {
                                                                                if (!diffStartAndEndDate(document.getElementById('start_date').value, document.getElementById('end_date').value, 31)) {
                                                                                    alert('We only allow date range not more than 31 days!'); 
                                                                                    return false;
                                                                                }
									} else {
										alert('Please fill in Start and End Date');
										document.getElementById('start_date').focus();
										return false;
									}
								}
							} else if (action=='save_search') {
								if (trim_str(document.getElementById('search_name').value) != '') {
									document.getElementById('order_list_subaction').value = 'save_search';
									form_obj.SaveSearchBtn.disabled = true;
									form_obj.SaveSearchBtn.value = 'Please wait...';
								} else {
									alert(" You must enter a name for saving your search criteria!");
									document.getElementById('search_name').value = '';
									document.getElementById('search_name').focus();
									return false;
								}
							} else {
								document.getElementById('order_list_subaction').value = 'do_search';
							}
							
						    form_obj.submit();
							return true;
			    		}
			    		
			    		function getReturnedValue(received_val, input_field) {
							jQuery('#'+input_field).val(received_val);
						}
						
						function resetControls(controlObj) {
							document.order_lists_criteria.start_date.value = '';
							document.order_lists_criteria.end_date.value = '';
							document.order_lists_criteria.follow_up_date.value = '';
							document.order_lists_criteria.order_total.value = '';
							jQuery('#cat_id').val('');
							document.order_lists_criteria.product_id.value = '';
							if (document.order_lists_criteria.product_type != null) document.order_lists_criteria.product_type.selectedIndex = '';
                            jQuery("li#any_pg span.payment_gateways input[type='checkbox']").click();
                            jQuery("li#any_pg span.payment_gateways input[type='checkbox']").attr('checked',true);
							document.getElementById('order_status_any').checked = true;
			    			set_status_option(document.getElementById('order_status_any'));
			    			document.order_lists_criteria.customer_group.selectedIndex = 0;
						}
						
			    		function verify_payment_selection() {
			    			var multi_pay_select = document.order_lists_criteria.elements['payment_method[]'];
			    			var selected_count = 0;
		    				for (i=0;i<multi_pay_select.length;i++) {
								if (multi_pay_select[i].checked == true) {
									selected_count++;
								}
							}
							if (!selected_count) {
								document.getElementById('payment_method_any').checked = true;
							} else {
								document.getElementById('payment_method_any').checked = false;
							}
			    		}
			    		
			    		function set_status_option(any_status_obj) {
			    			var multi_status_select = document.order_lists_criteria.elements['order_status[]'];
			    			if (any_status_obj.checked == true) {
								for (i=0;i<multi_status_select.length;i++) {
									multi_status_select[i].checked = false;
									
									var cur_status_id = multi_status_select[i].value;
			    					var multi_tags_select = document.order_lists_criteria.elements['status_'+cur_status_id+'[]'];
									if (typeof(multi_tags_select) != 'undefined') {
										if (typeof(multi_tags_select.length) != 'undefined') {
											for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
												multi_tags_select[tag_cnt].disabled = true;
												multi_tags_select[tag_cnt].checked = false;
											}
										} else {
											multi_tags_select.disabled = true;
											multi_tags_select.checked = false;
										}
										
									}
								}
			    			} else {	// force to check if no any order status option is selected
			    				var selected_count = 0;
			    				for (i=0;i<multi_status_select.length;i++) {
			    					var cur_status_id = multi_status_select[i].value;
			    					var multi_tags_select = document.order_lists_criteria.elements['status_'+cur_status_id+'[]'];
									if (multi_status_select[i].checked == true) {
										selected_count++;
										if (typeof(multi_tags_select) != 'undefined') {
											if (typeof(multi_tags_select.length) != 'undefined') {
												for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
													multi_tags_select[tag_cnt].disabled = false;
												}
											} else {
												multi_tags_select.disabled = false;
											}
										}
									} else {
										if (typeof(multi_tags_select) != 'undefined') {
											if (typeof(multi_tags_select.length) != 'undefined') {
												for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
													multi_tags_select[tag_cnt].disabled = true;
													multi_tags_select[tag_cnt].checked = false;
												}
											} else {
												multi_tags_select.disabled = true;
												multi_tags_select.checked = false;
											}
										}
									}
								}
								if (!selected_count) {
									any_status_obj.checked = true;
								}
			    			}
			    		}
                                        
			    		function verify_status_selection(status_id) {
			    			var multi_status_select = document.order_lists_criteria.elements['order_status[]'];
			    			var selected_count = 0;
		    				for (i=0;i<multi_status_select.length;i++) {
		    					var cur_status_id = multi_status_select[i].value;
			    				var multi_tags_select = document.order_lists_criteria.elements['status_'+cur_status_id+'[]'];
								if (multi_status_select[i].checked == true) {
                                                                        if(status_id == cur_status_id){
                                                                           jQuery(multi_tags_select).attr("checked", true);
                                                                        }
									selected_count++;
									if (typeof(multi_tags_select) != 'undefined') {
										if (typeof(multi_tags_select.length) != 'undefined') {
											for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
												multi_tags_select[tag_cnt].disabled = false;
											}
										} else {
											multi_tags_select.disabled = false;
										}
									}
								} else {
									if (typeof(multi_tags_select) != 'undefined') {
										if (typeof(multi_tags_select.length) != 'undefined') {
											for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
												multi_tags_select[tag_cnt].disabled = true;
												multi_tags_select[tag_cnt].checked = false;
											}
										} else {
											multi_tags_select.disabled = true;
											multi_tags_select.checked = false;
										}
									}
								}
							}
							if (!selected_count) {
								document.getElementById('order_status_any').checked = true;
							} else {
								document.getElementById('order_status_any').checked = false;
							}
			    		}
			    		set_status_option(document.getElementById('order_status_any'));
			    	//-->
					</script>
<?
}
?>
				</table>
<!-- body_text_eof //-->
  			</td>
  		</tr>
	</table>
<!-- body_eof //-->
<script type="text/javascript" src="includes/javascript/jquery.tree.js"></script>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>