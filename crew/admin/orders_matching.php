<?
/*
 	$Id: orders_matching.php,v 1.7 2009/05/15 04:59:24 weichen Exp $
	
 	Copyright (c) 2007 Dynamic Podium
	
 	Released under the GNU General Public License
*/
ini_set("memory_limit", "512M");
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'orders_matching.php');
include(DIR_WS_CLASSES . 'order.php');
include(DIR_WS_CLASSES . 'edit_order.php');
include_once(DIR_WS_CLASSES . 'payment_methods.php');

$currencies = new currencies();
$orders_matching_object = new orders_matching($login_id, $login_email_address);

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

$report_type = (isset($_REQUEST['report_type']) ? $_REQUEST['report_type'] : '');

switch($action) {
	case "show_report":
		// make a connection to the database... now
		$read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');
		
		$header_title = HEADER_FORM_ORDERS_MATCHING_TITLE;
		
		if ($report_type == '2') {
			$form_content = $orders_matching_object->show_activity_matching(FILENAME_ORDERS_MATCHING, 'orders_matching_inputs', $_REQUEST, $messageStack);
		} else {
			$form_content = $orders_matching_object->show_orders_matching(FILENAME_ORDERS_MATCHING, 'orders_matching_inputs', $_REQUEST, $messageStack);
		}
		break;

	case 'export_csv':
		$filename = $_POST['filename'];
		$file_location = "download/".$filename;
		$mime_type = 'text/x-csv';
		// Download
        header('Content-Type: ' . $mime_type);
        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        // IE need specific headers
        if (PMA_USR_BROWSER_AGENT == 'IE') {
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');
        } else {
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Pragma: no-cache');
        }
        readfile($file_location);
		exit();
		
		break;
	case "reset_session":
		// Prepare temp files
        $temp_filename = 'file_temp_matching_' . $_SESSION['login_id'] . '.csv';
        $temp_target = 'download/' . $temp_filename;
        // Do checking if temp file exist for this user?
        if(file_exists($temp_target)) {
            // removed prev file
            unlink($temp_target);
        }
    	unset($_SESSION['orders_matching_inputs']);
    	tep_redirect(tep_href_link(FILENAME_ORDERS_MATCHING));
    	
    	break;
	default:
		$header_title = HEADER_FORM_ORDERS_MATCHING_TITLE;
		$form_content = $orders_matching_object->search_orders_matching(FILENAME_ORDERS_MATCHING, 'orders_matching_inputs');
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/jquery.tree.css">
	<script language="JavaScript" src="includes/javascript/select_box.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/customer_xmlhttp.js"></script>
	<script language="JavaScript" src="includes/javascript/jquery.js"></script>
	<script type="text/javascript" src="includes/javascript/jquery.tree.js"></script>
<?	if (file_exists(DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php')) { include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); } ?>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- Popup //-->
	<script language="JavaScript" src="<?=DIR_WS_INCLUDES?>addon/scriptasylum_popup/popup.js"></script>
	<!-- End of Popup //-->
	
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
		    	<table border="0" width="" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%" class="pageHeading"><b><?=$header_title?></b></td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>
