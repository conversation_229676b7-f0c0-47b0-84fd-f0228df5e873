<?php
/*
  	$Id: orders_status.php,v 1.4 2005/06/08 04:50:35 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

if (tep_not_null($action)) {
	switch ($action) {
    	case 'insert':
      	case 'save':
        	if (isset($HTTP_GET_VARS['oID'])) $orders_status_id = tep_db_prepare_input($HTTP_GET_VARS['oID']);
			
        	$languages = tep_get_languages();
        	for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
          		$orders_status_name_array = $HTTP_POST_VARS['orders_status_name'];
          		$language_id = $languages[$i]['id'];
				
          		$sql_data_array = array('orders_status_name' => tep_db_prepare_input($orders_status_name_array[$language_id]));
				
          		if ($action == 'insert') {
            		if (empty($orders_status_id)) {
              			$next_id_query = tep_db_query("select max(orders_status_id) as orders_status_id from " . TABLE_ORDERS_STATUS . "");
              			$next_id = tep_db_fetch_array($next_id_query);
              			$orders_status_id = $next_id['orders_status_id'] + 1;
            		}
					
            		$insert_sql_data = array(	'orders_status_id' => $orders_status_id,
                                     			'language_id' => $language_id);
					
            		$sql_data_array = array_merge($sql_data_array, $insert_sql_data);
					
            		tep_db_perform(TABLE_ORDERS_STATUS, $sql_data_array);
          		} elseif ($action == 'save') {
            		tep_db_perform(TABLE_ORDERS_STATUS, $sql_data_array, 'update', "orders_status_id = '" . (int)$orders_status_id . "' and language_id = '" . (int)$language_id . "'");
          		}
        	}
			
        	if (isset($HTTP_POST_VARS['default']) && ($HTTP_POST_VARS['default'] == 'on')) {
          		tep_db_query("update " . TABLE_CONFIGURATION . " set configuration_value = '" . tep_db_input($orders_status_id) . "' where configuration_key = 'DEFAULT_ORDERS_STATUS_ID'");
        	}
			
        	tep_redirect(tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $orders_status_id));
        	break;
      	case 'deleteconfirm':
        	$oID = tep_db_prepare_input($HTTP_GET_VARS['oID']);
			
        	$orders_status_query = tep_db_query("select configuration_value from " . TABLE_CONFIGURATION . " where configuration_key = 'DEFAULT_ORDERS_STATUS_ID'");
        	$orders_status = tep_db_fetch_array($orders_status_query);
			
    	    if ($orders_status['configuration_value'] == $oID) {
          		tep_db_query("update " . TABLE_CONFIGURATION . " set configuration_value = '' where configuration_key = 'DEFAULT_ORDERS_STATUS_ID'");
        	}
			
        	tep_db_query("delete from " . TABLE_ORDERS_STATUS . " where orders_status_id = '" . tep_db_input($oID) . "'");
			
        	tep_redirect(tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page']));
        	break;
      	case 'delete':
        	$oID = tep_db_prepare_input($HTTP_GET_VARS['oID']);
			
        	$status_query = tep_db_query("select count(*) as count from " . TABLE_ORDERS . " where orders_status = '" . (int)$oID . "'");
        	$status = tep_db_fetch_array($status_query);
			
        	$remove_status = true;
        	if ($oID == DEFAULT_ORDERS_STATUS_ID) {
          		$remove_status = false;
          		$messageStack->add(ERROR_REMOVE_DEFAULT_ORDER_STATUS, 'error');
        	} elseif ($status['count'] > 0) {
          		$remove_status = false;
          		$messageStack->add(ERROR_STATUS_USED_IN_ORDERS, 'error');
        	} else {
          		$history_query = tep_db_query("select count(*) as count from " . TABLE_ORDERS_STATUS_HISTORY . " where orders_status_id = '" . (int)$oID . "'");
          		$history = tep_db_fetch_array($history_query);
          		if ($history['count'] > 0) {
            		$remove_status = false;
            		$messageStack->add(ERROR_STATUS_USED_IN_HISTORY, 'error');
          		}
        	}
        	break;
	}
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  	<tr>
    	<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    		<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
			<!-- left_navigation //-->
			<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
			<!-- left_navigation_eof //-->
    		</table>
    	</td>
		<!-- body_text //-->
    	<td width="100%" valign="top">
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
      			<tr>
        			<td>
        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          					<tr>
            					<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          					</tr>
        				</table>
        			</td>
      			</tr>
      			<tr>
        			<td valign="top">
        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          					<tr>
            					<td valign="top">
            						<table border="0" width="100%" cellspacing="0" cellpadding="2">
              							<tr class="dataTableHeadingRow">
                							<td class="dataTableHeadingContent"><?=TABLE_HEADING_ORDERS_STATUS?></td>
                							<td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_ACTION?>&nbsp;</td>
              							</tr>
<?
$orders_status_query_raw = "select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '" . (int)$languages_id . "' order by orders_status_id";
$orders_status_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $orders_status_query_raw, $orders_status_query_numrows);
$orders_status_query = tep_db_query($orders_status_query_raw);

while ($orders_status = tep_db_fetch_array($orders_status_query)) {
    if ((!isset($HTTP_GET_VARS['oID']) || (isset($HTTP_GET_VARS['oID']) && ($HTTP_GET_VARS['oID'] == $orders_status['orders_status_id']))) && !isset($oInfo) && (substr($action, 0, 3) != 'new')) {
      	$oInfo = new objectInfo($orders_status);
    }
	
    if (isset($oInfo) && is_object($oInfo) && ($orders_status['orders_status_id'] == $oInfo->orders_status_id)) {
      	echo '                  		<tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $oInfo->orders_status_id . '&action=edit') . '\'">' . "\n";
    } else {
      	echo '                  		<tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $orders_status['orders_status_id']) . '\'">' . "\n";
    }
	
    if (DEFAULT_ORDERS_STATUS_ID == $orders_status['orders_status_id']) {
      	echo '                				<td class="dataTableContent"><b>' . $orders_status['orders_status_name'] . ' (' . TEXT_DEFAULT . ')</b></td>' . "\n";
    } else {
      	echo '                				<td class="dataTableContent">' . $orders_status['orders_status_name'] . '</td>' . "\n";
    }
?>
											<td class="dataTableContent" align="right"><?php if (isset($oInfo) && is_object($oInfo) && ($orders_status['orders_status_id'] == $oInfo->orders_status_id)) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif', ''); } else { echo '<a href="' . tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $orders_status['orders_status_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              							</tr>
<?
}
?>
              							<tr>
                							<td colspan="2">
                								<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  									<tr>
                    									<td class="smallText" valign="top"><? echo $orders_status_split->display_count($orders_status_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_ORDERS_STATUS); ?></td>
                    									<td class="smallText" align="right"><? echo $orders_status_split->display_links($orders_status_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?></td>
                  									</tr>
<?
if (empty($action)) {
?>
                  									<tr>
                    									<td colspan="2" align="right"><? echo '<a href="' . tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&action=new') . '">' . tep_image_button('button_insert.gif', IMAGE_INSERT) . '</a>'; ?></td>
                  									</tr>
<?
}
?>
                								</table>
                							</td>
              							</tr>
            						</table>
            					</td>
<?
$heading = array();
$contents = array();

switch ($action) {
	case 'new':
      	$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_NEW_ORDERS_STATUS . '</b>');
		
      	$contents = array('form' => tep_draw_form('status', FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&action=insert'));
      	$contents[] = array('text' => TEXT_INFO_INSERT_INTRO);
		
      	$orders_status_inputs_string = '';
      	$languages = tep_get_languages();
      	for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
        	$orders_status_inputs_string .= '<br>' . tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('orders_status_name[' . $languages[$i]['id'] . ']');
      	}
		
      	$contents[] = array('text' => '<br>' . TEXT_INFO_ORDERS_STATUS_NAME . $orders_status_inputs_string);
      	$contents[] = array('text' => '<br>' . tep_draw_checkbox_field('default') . ' ' . TEXT_SET_DEFAULT);
      	$contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_insert.gif', IMAGE_INSERT) . ' <a href="' . tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page']) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      	break;
	case 'edit':
      	$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_EDIT_ORDERS_STATUS . '</b>');
		
      	$contents = array('form' => tep_draw_form('status', FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $oInfo->orders_status_id  . '&action=save'));
      	$contents[] = array('text' => TEXT_INFO_EDIT_INTRO);
		
      	$orders_status_inputs_string = '';
      	$languages = tep_get_languages();
      	for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
        	$orders_status_inputs_string .= '<br>' . tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('orders_status_name[' . $languages[$i]['id'] . ']', tep_get_orders_status_name($oInfo->orders_status_id, $languages[$i]['id']));
      	}
		
      	$contents[] = array('text' => '<br>' . TEXT_INFO_ORDERS_STATUS_NAME . $orders_status_inputs_string);
      	if (DEFAULT_ORDERS_STATUS_ID != $oInfo->orders_status_id) $contents[] = array('text' => '<br>' . tep_draw_checkbox_field('default') . ' ' . TEXT_SET_DEFAULT);
      	$contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $oInfo->orders_status_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      	break;
    case 'delete':
      	$heading[] = array('text' => '<b>' . TEXT_INFO_HEADING_DELETE_ORDERS_STATUS . '</b>');
		
      	$contents = array('form' => tep_draw_form('status', FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $oInfo->orders_status_id  . '&action=deleteconfirm'));
      	$contents[] = array('text' => TEXT_INFO_DELETE_INTRO);
      	$contents[] = array('text' => '<br><b>' . $oInfo->orders_status_name . '</b>');
      	if ($remove_status) $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $oInfo->orders_status_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      	break;
	default:
      	if (isset($oInfo) && is_object($oInfo)) {
        	$heading[] = array('text' => '<b>' . $oInfo->orders_status_name . '</b>');
			
        	$contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $oInfo->orders_status_id . '&action=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_ORDERS_STATUS, 'page=' . $HTTP_GET_VARS['page'] . '&oID=' . $oInfo->orders_status_id . '&action=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');
			
        	$orders_status_inputs_string = '';
        	$languages = tep_get_languages();
        	for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
          		$orders_status_inputs_string .= '<br>' . tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']) . '&nbsp;' . tep_get_orders_status_name($oInfo->orders_status_id, $languages[$i]['id']);
        	}
			
        	$contents[] = array('text' => $orders_status_inputs_string);
      	}
      	break;
}

if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
	echo '            			<td width="25%" valign="top">' . "\n";
	
    $box = new box;
    echo $box->infoBox($heading, $contents);
	
    echo '            			</td>' . "\n";
}
?>
          					</tr>
        				</table>
        			</td>
      			</tr>
    		</table>
    	</td>
		<!-- body_text_eof //-->
  	</tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>