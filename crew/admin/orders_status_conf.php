<?php
/*
  	$Id: orders_status_conf.php,v 1.14 2011/11/29 10:24:50 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
	$conf_sql_data_array = array();
	
	if (isset($_POST['manual_notify'])) {
		$manual_notify = tep_db_prepare_input($_POST['manual_notify']);
		if ($manual_notify != '-1')	$conf_sql_data_array['status_configuration_manual_notification'] = $manual_notify;
	}
	    		
	if (isset($_POST['auto_notify'])) {
		$auto_notify = tep_db_prepare_input($_POST['auto_notify']);
		if ($auto_notify != '-1')	$conf_sql_data_array['status_configuration_auto_notification'] = $auto_notify;
	}
	    			
	switch ($action) {
		case "update":
			$user_group_id_array = $_POST['user_grp_to'];
			$user_group_id_string = (isset($user_group_id_array) && count($user_group_id_array)) ? implode(",", $user_group_id_array) : '';
			$conf_sql_data_array['status_configuration_user_groups_id'] = $user_group_id_string;
    		
    		$status_conf_select_sql = "	SELECT status_configuration_trans_type  
										FROM " . TABLE_STATUS_CONFIGURATION . " 
										WHERE status_configuration_trans_type = '" . tep_db_input($_REQUEST["type"]) . "' 
											AND status_configuration_source_status_id = '" . tep_db_input($_REQUEST["from"]) . "' 
											AND status_configuration_destination_status_id = '" . tep_db_input($_REQUEST["to"]) . "'";
			$status_conf_result_sql = tep_db_query($status_conf_select_sql);
			if ($status_conf_row = tep_db_fetch_array($status_conf_result_sql)) {
				# update database
				
	    		if (tep_not_null($conf_sql_data_array)) tep_db_perform(TABLE_STATUS_CONFIGURATION, $conf_sql_data_array, 'update', ' status_configuration_trans_type="'.tep_db_input($_REQUEST["type"]).'" AND status_configuration_source_status_id = "'.tep_db_input($_REQUEST["from"]).'" AND status_configuration_destination_status_id = "'.tep_db_input($_REQUEST["to"]).'"');
			} else {
				# insert new record
				$conf_sql_data_array['status_configuration_trans_type'] = tep_db_prepare_input($_REQUEST["type"]);
				$conf_sql_data_array['status_configuration_source_status_id'] = tep_db_prepare_input($_REQUEST["from"]);
				$conf_sql_data_array['status_configuration_destination_status_id'] = tep_db_prepare_input($_REQUEST["to"]);
				
				if (tep_not_null($conf_sql_data_array)) tep_db_perform(TABLE_STATUS_CONFIGURATION, $conf_sql_data_array);
			}
			
			if($_REQUEST["type"] == 'C' && $_REQUEST["from"] == '1' && $_REQUEST["to"] == '7') { // Only for customer type, status frm Pending to Verify
				if (!tep_not_null($user_group_id_string)) {
					$payment_method_delete_sql = "	DELETE FROM " . TABLE_STATUS_CONFIGURATION_PAYMENT_METHODS . " 
													WHERE status_configuration_trans_type='".tep_db_input($_REQUEST["type"])."' 
														AND status_configuration_source_status_id = '".tep_db_input($_REQUEST["from"])."' 
														AND status_configuration_destination_status_id = '".tep_db_input($_REQUEST["to"])."'";
					tep_db_query($payment_method_delete_sql);
				} else {
					$payment_method_insert_sql_array = array();
					$exist_user_group_id_array = array();
					
					$payment_method_delete_sql = "	DELETE FROM " . TABLE_STATUS_CONFIGURATION_PAYMENT_METHODS . " 
													WHERE status_configuration_trans_type='".tep_db_input($_REQUEST["type"])."' 
														AND status_configuration_source_status_id = '".tep_db_input($_REQUEST["from"])."' 
														AND status_configuration_destination_status_id = '".tep_db_input($_REQUEST["to"])."'
														AND status_configuration_user_groups_id NOT IN (".$user_group_id_string.")";
					tep_db_query($payment_method_delete_sql);
					
					$exist_user_group_select_sql = "	SELECT status_configuration_user_groups_id 
														FROM " . TABLE_STATUS_CONFIGURATION_PAYMENT_METHODS . " 
														WHERE status_configuration_trans_type = '" . tep_db_input($_REQUEST["type"]) . "' 
															AND status_configuration_source_status_id = '" . tep_db_input($_REQUEST["from"]) . "' 
															AND status_configuration_destination_status_id = '" . tep_db_input($_REQUEST["to"]) . "'";
					$exist_user_group_result_sql = tep_db_query($exist_user_group_select_sql);
					while ($exist_user_group_row = tep_db_fetch_array($exist_user_group_result_sql)) {
						$exist_user_group_id_array[] = $exist_user_group_row['status_configuration_user_groups_id'];
					}
					
					foreach($user_group_id_array as $grp_id) {
						if(!in_array($grp_id, $exist_user_group_id_array)) {
							$payment_method_insert_sql_array = array(	'status_configuration_trans_type' => tep_db_prepare_input($_REQUEST["type"]),
																		'status_configuration_source_status_id' => tep_db_prepare_input($_REQUEST["from"]),
																		'status_configuration_destination_status_id' => tep_db_prepare_input($_REQUEST["to"]),
																		'status_configuration_user_groups_id' => (int)$grp_id
																	);
																		
							tep_db_perform(TABLE_STATUS_CONFIGURATION_PAYMENT_METHODS, $payment_method_insert_sql_array);
						}
					}
				}
			}
			
			tep_redirect(tep_href_link(FILENAME_ORDERS_STATUS_CONF));
			
			break;
	}
}

$trans_type_array = array (	'C' => 'Customer Order',
							'S' => 'Supplier Order',
							'B' => 'Buyback Order',
							'PO' => 'Purchase Order',
							'PWL' => 'Powerleveling Task',
							'CGRP' => 'Customer Group Status',
                            'AFTGR' => 'AFT Group Level'); # CGRP is hard-code in customers_groups.php

$status_options = array();

$order_status_select_sql = "SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY orders_status_sort_order";
$order_status_result_sql = tep_db_query($order_status_select_sql);
while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
	$status_options['C'][$order_status_row["orders_status_id"]] = $order_status_row["orders_status_name"];
}

$sup_order_status_select_sql = "SELECT supplier_list_status_id, supplier_list_status_name FROM " . TABLE_SUPPLIER_LIST_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY supplier_list_status_sort_order";
$sup_order_status_result_sql = tep_db_query($sup_order_status_select_sql);
while ($sup_order_status_row = tep_db_fetch_array($sup_order_status_result_sql)) {
	$status_options['S'][$sup_order_status_row["supplier_list_status_id"]] = $sup_order_status_row["supplier_list_status_name"];
}

$buyback_order_status_select_sql = "SELECT buyback_status_id, buyback_status_name FROM " . TABLE_BUYBACK_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY buyback_status_sort_order";
$buyback_order_status_result_sql = tep_db_query($buyback_order_status_select_sql);
while ($buyback_order_status_row = tep_db_fetch_array($buyback_order_status_result_sql)) {
	$status_options['B'][$buyback_order_status_row["buyback_status_id"]] = $buyback_order_status_row["buyback_status_name"];
}

$po_status_select_sql = "SELECT purchase_orders_status_id, purchase_orders_status_name FROM " . TABLE_PURCHASE_ORDERS_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY purchase_orders_status_sort_order";
$po_status_result_sql = tep_db_query($po_status_select_sql);
while ($po_status_row = tep_db_fetch_array($po_status_result_sql)) {
	$status_options['PO'][$po_status_row["purchase_orders_status_id"]] = $po_status_row["purchase_orders_status_name"];
}

$pwl_order_status_select_sql = "SELECT supplier_tasks_status_id, supplier_tasks_status_name FROM " . TABLE_SUPPLIER_TASKS_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY supplier_tasks_status_sort_order";
$pwl_order_status_result_sql = tep_db_query($pwl_order_status_select_sql);
while ($pwl_order_status_row = tep_db_fetch_array($pwl_order_status_result_sql)) {
	$status_options['PWL'][$pwl_order_status_row["supplier_tasks_status_id"]] = $pwl_order_status_row["supplier_tasks_status_name"];
}

$cust_status_select_sql = 'SELECT customers_groups_id, customers_groups_name FROM ' . TABLE_CUSTOMERS_GROUPS . ' ORDER BY sort_order, customers_groups_name ASC';
$cust_status_result_sql = tep_db_query($cust_status_select_sql);
while ($cust_status_row = tep_db_fetch_array($cust_status_result_sql)) {
	$status_options['CGRP'][$cust_status_row['customers_groups_id']] = $cust_status_row['customers_groups_name'];
}

$cust_aft_status_select_sql = 'SELECT customers_aft_groups_id, customers_aft_groups_name FROM ' . TABLE_CUSTOMERS_AFT_GROUPS . ' ORDER BY sort_order';
$cust_aft_status_result_sql = tep_db_query($cust_aft_status_select_sql);
while ($cust_aft_status_row = tep_db_fetch_array($cust_aft_status_result_sql)) {
	$status_options['AFTGR'][$cust_aft_status_row['customers_aft_groups_id']] = $cust_aft_status_row['customers_aft_groups_name'];
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/javascript/admin_xmlhttp.js"></script>
<!-- files for tab menu {start} -->
	<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
	<script language="javascript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
<!-- files for tab menu {end} -->
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/select_box.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?

if ($action == "edit") {
	# select Customer Group Status menu tab
	if (tep_db_input($_REQUEST["type"]) == 'CGRP') {
		$_SESSION['CGRP_tab'] = tep_db_input($_REQUEST["from"]);
	}
	
	$user_groups_from_array = array( array ('id' => '', "text" => 'Select User Groups', "type" => 'optgroup') );
	$user_groups_to_array = array( array ('id' => '', "text" => 'Selected User Groups', "type" => 'optgroup') );
	
	$payment_method_list_array = array();
	
	$status_conf_select_sql = "	SELECT * 
								FROM " . TABLE_STATUS_CONFIGURATION . " 
								WHERE status_configuration_trans_type = '" . tep_db_input($_REQUEST["type"]) . "' 
									AND status_configuration_source_status_id = '" . tep_db_input($_REQUEST["from"]) . "' 
									AND status_configuration_destination_status_id = '" . tep_db_input($_REQUEST["to"]) . "'";
	$status_conf_result_sql = tep_db_query($status_conf_select_sql);
	if (($status_conf_row = tep_db_fetch_array($status_conf_result_sql)) || (in_array($_REQUEST["type"], array('CGRP', 'AFTGR')))) {
		if (tep_not_null($status_conf_row['status_configuration_user_groups_id'])) {
			$unassigned_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id NOT IN (".$status_conf_row['status_configuration_user_groups_id'].") ORDER BY admin_groups_name";
			$unassigned_user_result_sql = tep_db_query($unassigned_user_select_sql);
			while ($unassigned_user_row = tep_db_fetch_array($unassigned_user_result_sql)) {
				$user_groups_from_array[] = array(	'id' => $unassigned_user_row['admin_groups_id'],
													'text' => $unassigned_user_row['admin_groups_name']
													);
			}
			
			$assigned_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id IN (".$status_conf_row['status_configuration_user_groups_id'].") ORDER BY admin_groups_name";
			$assigned_user_result_sql = tep_db_query($assigned_user_select_sql);
			while ($assigned_user_row = tep_db_fetch_array($assigned_user_result_sql)) {
				$user_groups_to_array[] = array(	'id' => $assigned_user_row['admin_groups_id'],
													'text' => $assigned_user_row['admin_groups_name']
													);
			}
		} else {
			$unassigned_user_select_sql = "SELECT admin_groups_id, admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " ORDER BY admin_groups_name";
			$unassigned_user_result_sql = tep_db_query($unassigned_user_select_sql);
			while ($unassigned_user_row = tep_db_fetch_array($unassigned_user_result_sql)) {
				$user_groups_from_array[] = array(	'id' => $unassigned_user_row['admin_groups_id'],
													'text' => $unassigned_user_row['admin_groups_name']
													);
			}
		}
		
		if($_REQUEST["type"] == 'C' && $_REQUEST["from"] == '1' && $_REQUEST["to"] == '7') {
			$payment_user_group_select_sql = "	SELECT ag.admin_groups_id, ag.admin_groups_name
												FROM " . TABLE_STATUS_CONFIGURATION_PAYMENT_METHODS . " as scpm 
												INNER JOIN " . TABLE_ADMIN_GROUPS . " as ag 
													ON ag.admin_groups_id = scpm.status_configuration_user_groups_id
												WHERE scpm.status_configuration_trans_type = '" . tep_db_input($_REQUEST["type"]) . "' 
													AND scpm.status_configuration_source_status_id = '" . tep_db_input($_REQUEST["from"]) . "' 
													AND scpm.status_configuration_destination_status_id = '" . tep_db_input($_REQUEST["to"]) . "'";
			$payment_user_group_result_sql = tep_db_query($payment_user_group_select_sql);
			while ($payment_user_group_row = tep_db_fetch_array($payment_user_group_result_sql)) {
				$payment_method_list_array[$payment_user_group_row['admin_groups_id']] = $payment_user_group_row['admin_groups_name'];
			}
		}
?>
					<tr>
        				<td width="100%">
						<?=tep_draw_form('status_conf_form', FILENAME_ORDERS_STATUS_CONF, tep_get_all_get_params(array('action')) . 'action=update', 'post', '')?>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_TRANS_TYPE?></td>
									<td class="main" valign="top"><?=$trans_type_array[$_REQUEST["type"]]?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" valign="top"><?=ENTRY_CONF_FROM_STATUS?></td>
									<td class="main"><?=$status_options[$_REQUEST["type"]][$_REQUEST["from"]]?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      					<td class="main" valign="top"><?=ENTRY_CONF_TO_STATUS?></td>
									<td class="main"><?=$status_options[$_REQUEST["type"]][$_REQUEST["to"]]?></td>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_CONF_USER_GROUPS?></td>
			        				<td class="main"><?=tep_draw_js_select_boxes('user_grp', $user_groups_from_array, $user_groups_to_array, ' size="10" style="width:20em;"')?></td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_CONF_MANUAL_NOTIFICATION?></td>
			        				<td class="main" valign="top">
			        				<?
			        					if ($status_conf_row["status_configuration_manual_notification"] != '-1') {
			        						echo tep_draw_textarea_field('manual_notify', 'soft', '60', '5', $status_conf_row['status_configuration_manual_notification']);
										} else {
											echo TEXT_NOT_AVAILABLE;
										}
									?>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_CONF_AUTO_NOTIFICATION?></td>
			        				<td class="main" valign="top">
			        				<?
			        					if ($status_conf_row["status_configuration_auto_notification"] != '-1') {
											echo tep_draw_textarea_field('auto_notify', 'soft', '60', '5', $status_conf_row['status_configuration_auto_notification']);
										} else {
											echo TEXT_NOT_AVAILABLE;
										}
									?>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
<? 	if($_REQUEST["type"] == 'C' && $_REQUEST["from"] == '1' && $_REQUEST["to"] == '7') { ?>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_CONF_PAYMENT_METHODS?></td>
			        				<td class="main">
			        					<div style="width: 510px; max-height: 250px; overflow-y: scroll;">
			        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
				        					<tr>
												<td width="8%" align="left" class="ordersBoxHeading">No.</td>	
												<td align="center" class="ordersBoxHeading">User Group</td>	
												<td width="4%" class="ordersBoxHeading" align="center">Actions</td>
											</tr>
<?		if(count($payment_method_list_array)) {
			$num = 1;
			foreach ($payment_method_list_array as $payment_method_list_id => $payment_method_list_name) { 
				$row_style = ($num%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
?>
											<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')">
												<td class="ordersRecords"><?=$num?></td>
												<td class="ordersRecords"><?=$payment_method_list_name?></td>
												<td class="ordersRecords" align="center">
						  							<?='<a href="javascript:payment_method_edit_permission('.$payment_method_list_id.', \''.$_REQUEST["type"].'\', '.$_REQUEST["from"].', '.$_REQUEST["to"].');">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "14", "13", 'align="top"').'</a>'?>
						  						</td>
						  					</tr>
<?
				$num++;
			}
		} else { ?>
	        								<tr>
	        									<td align="center" class="ordersRecords" colspan="3">No user group found.</td>
	        								</tr>
<?		} ?>
										</table>
			        					</div>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
<?	} ?>
			      				<tr>
									<td colspan="2" align="right">
            							<input type="button" name="update" value="Update" class="inputButton" onClick="return button_lock();">&nbsp;&nbsp;
            							<input type="button" name="cancel" value="Cancel" class="inputButton" onClick="document.location.href='<?=tep_href_link(FILENAME_ORDERS_STATUS_CONF)?>'">
									</td>
									<script language="javascript">
										<!--
										function form_checking() {
											var selected_grp = document.status_conf_form.elements['user_grp_to[]'];

											if (selected_grp != null) {
												for (x=0; x<(selected_grp.length); x++) {
							    					selected_grp.options[x].selected = true;
							  					}
						  					}
						  				}
						  				
						  				function button_lock() {
						  					document.status_conf_form.update.disabled = true;
						  					form_checking();
						  					document.status_conf_form.submit();
						  				}
					  					//-->
									</script>
								</tr>
							</table>
							</form>
        				</td>
        			</tr>
<?
	} else {
		echo '<tr><td width="100%">Record Not Found!</td></tr>';
	}
} else {
	echo '			<tr>
    					<td valign="top"><a href="#B">Buyback Order</a> | <a href="#C">Customer Order</a> | <a href="#PO">Purchase Order</a> | <a href="#PWL">PWL Task</a> | <a href="#S">Supplier Order</a> | <a href="#CGRP">Customer Group Status</a> | <a href="#AFTGR">AFT Group Level</a></td>
    				</tr>';
    	
	$trans_type_select_sql = "	SELECT DISTINCT status_configuration_trans_type 
								FROM " . TABLE_STATUS_CONFIGURATION . " 
								ORDER BY status_configuration_trans_type";
	$trans_type_result_sql = tep_db_query($trans_type_select_sql);
	
	while ($trans_type_row = tep_db_fetch_array($trans_type_result_sql)) {
# skip Customer Status Configuration from displaying table {start}
		if (in_array($trans_type_row['status_configuration_trans_type'], array('CGRP', 'AFTGR') )) continue;
# skip Customer Status Configuration from displaying table {end}

		$status_conf_select_sql = "	SELECT sc.* 
									FROM " . TABLE_STATUS_CONFIGURATION . " AS sc 
									WHERE status_configuration_trans_type = '" . $trans_type_row['status_configuration_trans_type'] . "' 
									ORDER BY status_configuration_source_status_id, status_configuration_destination_status_id";
		$status_conf_result_sql = tep_db_query($status_conf_select_sql);
?>
					<tr>
            			<td valign="top">
            				<table border="0" width="100%" cellspacing="1" cellpadding="2">
            					<tr>
            						<td colspan="6" class="pageHeading"><a name='<?=$trans_type_row['status_configuration_trans_type']?>'></a><?=$trans_type_array[strtoupper($trans_type_row['status_configuration_trans_type'])]?></td>
            					</tr>
               					<tr>
			       					<td class="reportBoxHeading" align="center" width="10%"><?=TABLE_HEADING_STATUS_CONF_FROM_STATUS?></td>
					                <td class="reportBoxHeading" align="center" width="10%"><?=TABLE_HEADING_STATUS_CONF_TO_STATUS?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_STATUS_CONF_USER_GROUPS?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_STATUS_CONF_MANUAL_NOTIFICATION?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_STATUS_CONF_AUTO_NOTIFICATION?></td>
					                <td class="reportBoxHeading" align="center" width="3%"><?=TABLE_HEADING_ACTION?></td>
			   					</tr>
<?
		$row_count = 0;
		$current_status='';
		$acc_row_buffer = '';
		$new_section = false;
		
		while ($status_conf_row = tep_db_fetch_array($status_conf_result_sql)) {
    		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
    		
    		$user_group_string_array = array();
    		if (tep_not_null($status_conf_row['status_configuration_user_groups_id'])) {
    			$user_group_select_sql = "SELECT admin_groups_name FROM " . TABLE_ADMIN_GROUPS . " WHERE admin_groups_id IN (".$status_conf_row['status_configuration_user_groups_id'].")";
    			$user_group_result_sql = tep_db_query($user_group_select_sql);
    			while ($user_group_row = tep_db_fetch_array($user_group_result_sql)) {
    				$user_group_string_array[] = $user_group_row['admin_groups_name'];
    			}
    		}
    		
    		if ($current_status != $status_conf_row['status_configuration_source_status_id']) {
    			$current_status = $status_conf_row['status_configuration_source_status_id'];
    			$new_section = true;
    		} else {
    			$new_section = false;
    		}
    		
    		ob_start();
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
<?			if ($new_section) {
				echo '				<td onclick="" rowspan="##MAIN_ROW_SPAN##" class="reportRecords" align="center" valign="top">'.$status_options[$trans_type_row['status_configuration_trans_type']][$status_conf_row["status_configuration_source_status_id"]].'</td>';
			}
?>
									<td class="reportRecords" align="center" valign="top"><?=$status_options[$trans_type_row['status_configuration_trans_type']][$status_conf_row["status_configuration_destination_status_id"]]?></td>
									<td class="reportRecords" valign="top" nowrap><?=implode('<br>', $user_group_string_array)?></td>
									<td class="reportRecords" valign="top">
									<?
										if ($status_conf_row["status_configuration_manual_notification"] != '-1') {
											echo nl2br(htmlentities($status_conf_row["status_configuration_manual_notification"]));
										} else {
											echo '<div align="center">'.TEXT_NOT_AVAILABLE.'</div>';
										}
									?>
									</td>
									<td class="reportRecords" valign="top">
									<?
										if ($status_conf_row["status_configuration_auto_notification"] != '-1') {
											echo nl2br(htmlentities($status_conf_row["status_configuration_auto_notification"]));
										} else {
											echo '<div align="center">'.TEXT_NOT_AVAILABLE.'</div>';
										}
									?>
									</td>
									<td class="reportRecords" align="center" valign="top">
										<a href="<?=tep_href_link(FILENAME_ORDERS_STATUS_CONF, 'action=edit&type='.$trans_type_row['status_configuration_trans_type'].'&from='.$status_conf_row["status_configuration_source_status_id"].'&to='.$status_conf_row["status_configuration_destination_status_id"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
									</td>
								</tr>
<?
			$row_buffer = ob_get_contents();
			ob_end_clean();
			
			if ($new_section) {
				echo str_replace("##MAIN_ROW_SPAN##", $main_row_span, $acc_row_buffer);
				$acc_row_buffer = $row_buffer;
				
				$main_row_span = 1;
			} else {
				$acc_row_buffer .= "\n" . $row_buffer;
				$main_row_span++;
			}
			
			$row_count++;
		}
		
		if (tep_not_null($acc_row_buffer)) {
			echo str_replace("##MAIN_ROW_SPAN##", $main_row_span, $acc_row_buffer);
		}
?>
		    				</table>
    					</td>
    				</tr>
<?
	}
?>
<!-- Customer Status Configuration Menu {start} -->
<?
	if (count($status_options['CGRP'])) {
		if (isset($_SESSION['CGRP_tab'])) {
			$session_tab = $_SESSION['CGRP_tab'];
		}

		unset($_SESSION['CGRP_tab']);
?>
	<tr>
		<td valign="top">
			<table border="0" width="100%" cellspacing="1" cellpadding="2">
				<tr>
	    			<td colspan="6" class="pageHeading"><a name='CGRP'></a><?=$trans_type_array['CGRP']?></td>
	    		</tr>
    		</table>
			<div id="customer_status-tab">
				<ul>
<?    	foreach ($status_options['CGRP'] as $key => $value) {	?>
					<li <?=(isset($session_tab) && ($key == $session_tab) ? 'class="ui-tabs-selected"': '')?>>
    					<a href="#tab-<?=$key?>"><span><?=$value?></span></a>
    				</li>
<?    	} ?>
				</ul>
<?		foreach ($status_options['CGRP'] as $tab_key => $tab_value) {	?>
    			<div id="tab-<?php echo $tab_key; ?>" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">
    			<table border="0" width="100%" cellspacing="1" cellpadding="2">
    				<tr>
    					<td class="reportBoxHeading" align="center" width="10%"><?=TABLE_HEADING_STATUS_CONF_FROM_STATUS?></td>
	    				<td class="reportBoxHeading" align="center" width="10%"><?=TABLE_HEADING_STATUS_CONF_TO_STATUS?></td>
    					<td class="reportBoxHeading"><?=TABLE_HEADING_STATUS_CONF_USER_GROUPS?></td>
    					<td class="reportBoxHeading"><?=TABLE_HEADING_STATUS_CONF_MANUAL_NOTIFICATION?></td>
    					<td class="reportBoxHeading"><?=TABLE_HEADING_STATUS_CONF_AUTO_NOTIFICATION?></td>
						<td class="reportBoxHeading" align="center" width="3%"><?=TABLE_HEADING_ACTION?></td>
    				</tr>

<?
				$row_count = 0;
				$new_section = true;

				foreach ($status_options['CGRP'] as $key => $value) {
					if ($tab_key == $key) continue;
					
    				$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
					<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
<?
					if ($new_section) {
?>
						<td onclick="" rowspan="<?=(count($status_options['CGRP'])-1)?>" class="reportRecords" align="center" valign="top"><?=$status_options['CGRP'][$tab_key]?></td>
<?
					}
?>
						<td class="reportRecords" align="center" valign="top"><?=$value?></td>
<?
						$user_group_string_array = array();
						$status_configuration_manual_notification = '';
						$status_configuration_auto_notification   = '';
				
						$status_conf_select_sql = "	SELECT sc.status_configuration_user_groups_id, sc.status_configuration_manual_notification, 
														sc.status_configuration_auto_notification 
													FROM " . TABLE_STATUS_CONFIGURATION . " AS sc 
													WHERE status_configuration_trans_type = 'CGRP' 
														AND status_configuration_source_status_id = '" . $tab_key . "' 
														AND status_configuration_destination_status_id = '" . $key . "'";
						$status_conf_result_sql = tep_db_query($status_conf_select_sql);
						if ($status_conf_row = tep_db_fetch_array($status_conf_result_sql)) {
    						if (tep_not_null($status_conf_row['status_configuration_user_groups_id'])) {
    							$user_group_select_sql = "	SELECT admin_groups_name 
    														FROM " . TABLE_ADMIN_GROUPS . " 
    														WHERE admin_groups_id IN (".$status_conf_row['status_configuration_user_groups_id'].")";
    							$user_group_result_sql = tep_db_query($user_group_select_sql);
    							while ($user_group_row = tep_db_fetch_array($user_group_result_sql)) {
    								$user_group_string_array[] = $user_group_row['admin_groups_name'];
	    						}
		    				}
		    				
		    				if ($status_conf_row["status_configuration_manual_notification"] != '-1') {
								$status_configuration_manual_notification = nl2br(htmlentities($status_conf_row["status_configuration_manual_notification"]));
							} else {
								$status_configuration_manual_notification = '<div align="center">'.TEXT_NOT_AVAILABLE.'</div>';
							}

							if ($status_conf_row["status_configuration_auto_notification"] != '-1') {
								$status_configuration_auto_notification = nl2br(htmlentities($status_conf_row["status_configuration_auto_notification"]));
							} else {
								$status_configuration_auto_notification = '<div align="center">'.TEXT_NOT_AVAILABLE.'</div>';
							}
						}
?>
						<td class="reportRecords" valign="top" nowrap><?=implode('<br>', $user_group_string_array)?></td>
						<td class="reportRecords" valign="top"><?=$status_configuration_manual_notification?></td>
						<td class="reportRecords" valign="top"><?=$status_configuration_auto_notification?></td>
						<td class="reportRecords" align="center" valign="top">
							<a href="<?=tep_href_link(FILENAME_ORDERS_STATUS_CONF, 'action=edit&type=CGRP&from=' . $tab_key . '&to=' . $key)?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
						</td>
					</tr>
<?
					$new_section = false;
					$row_count++;
				}
?>
				</table>
				</div>
			</div>
<?
}
?>
		</td>
	</tr>
<?php
	}
?>
<!-- Customer Status Configuration Menu {end} -->
<?
	if (count($status_options['AFTGR'])) {
		if (isset($_SESSION['AFTGR_tab'])) {
			$session_tab = $_SESSION['AFTGR_tab'];
		}

		unset($_SESSION['AFTGR_tab']);
?>
	<tr>
		<td valign="top">
			<table border="0" width="100%" cellspacing="1" cellpadding="2">
				<tr>
	    			<td colspan="6" class="pageHeading"><a name='AFTGR'></a><?=$trans_type_array['AFTGR']?></td>
	    		</tr>
    		</table>
			<div id="customer_aft_status-tab">
				<ul>
<?    	foreach ($status_options['AFTGR'] as $key => $value) {	?>
					<li <?=(isset($session_tab) && ($key == $session_tab) ? 'class="ui-tabs-selected"': '')?>>
    					<a href="#tab-aft-<?=$key?>"><span><?=$value?></span></a>
    				</li>
<?    	} ?>
				</ul>
<?		foreach ($status_options['AFTGR'] as $tab_key => $tab_value) {	?>
    			<div id="tab-aft-<?php echo $tab_key; ?>" style="border-style: solid; border-color: #97a5b0; border-width: 1px;">
    			<table border="0" width="100%" cellspacing="1" cellpadding="2">
    				<tr>
    					<td class="reportBoxHeading" align="center" width="10%"><?=TABLE_HEADING_STATUS_CONF_FROM_STATUS?></td>
	    				<td class="reportBoxHeading" align="center" width="10%"><?=TABLE_HEADING_STATUS_CONF_TO_STATUS?></td>
    					<td class="reportBoxHeading"><?=TABLE_HEADING_STATUS_CONF_USER_GROUPS?></td>
    					<td class="reportBoxHeading"><?=TABLE_HEADING_STATUS_CONF_MANUAL_NOTIFICATION?></td>
    					<td class="reportBoxHeading"><?=TABLE_HEADING_STATUS_CONF_AUTO_NOTIFICATION?></td>
						<td class="reportBoxHeading" align="center" width="3%"><?=TABLE_HEADING_ACTION?></td>
    				</tr>

<?
				$row_count = 0;
				$new_section = true;

				foreach ($status_options['AFTGR'] as $key => $value) {
					if ($tab_key == $key) continue;
					
    				$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
					<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
<?
					if ($new_section) {
?>
						<td onclick="" rowspan="<?=(count($status_options['AFTGR'])-1)?>" class="reportRecords" align="center" valign="top"><?=$status_options['AFTGR'][$tab_key]?></td>
<?
					}
?>
						<td class="reportRecords" align="center" valign="top"><?=$value?></td>
<?
						$user_group_string_array = array();
						$status_configuration_manual_notification = '';
						$status_configuration_auto_notification   = '';
                        
						$status_conf_select_sql = "	SELECT sc.status_configuration_user_groups_id, sc.status_configuration_manual_notification, 
														sc.status_configuration_auto_notification 
													FROM " . TABLE_STATUS_CONFIGURATION . " AS sc 
													WHERE status_configuration_trans_type = 'AFTGR' 
														AND status_configuration_source_status_id = '" . $tab_key . "' 
														AND status_configuration_destination_status_id = '" . $key . "'";
						$status_conf_result_sql = tep_db_query($status_conf_select_sql);
						if ($status_conf_row = tep_db_fetch_array($status_conf_result_sql)) {
    						if (tep_not_null($status_conf_row['status_configuration_user_groups_id'])) {
    							$user_group_select_sql = "	SELECT admin_groups_name 
    														FROM " . TABLE_ADMIN_GROUPS . " 
    														WHERE admin_groups_id IN (".$status_conf_row['status_configuration_user_groups_id'].")";
    							$user_group_result_sql = tep_db_query($user_group_select_sql);
    							while ($user_group_row = tep_db_fetch_array($user_group_result_sql)) {
    								$user_group_string_array[] = $user_group_row['admin_groups_name'];
	    						}
		    				}
		    				
		    				if ($status_conf_row["status_configuration_manual_notification"] != '-1') {
								$status_configuration_manual_notification = nl2br(htmlentities($status_conf_row["status_configuration_manual_notification"]));
							} else {
								$status_configuration_manual_notification = '<div align="center">'.TEXT_NOT_AVAILABLE.'</div>';
							}

							if ($status_conf_row["status_configuration_auto_notification"] != '-1') {
								$status_configuration_auto_notification = nl2br(htmlentities($status_conf_row["status_configuration_auto_notification"]));
							} else {
								$status_configuration_auto_notification = '<div align="center">'.TEXT_NOT_AVAILABLE.'</div>';
							}
						}
?>
						<td class="reportRecords" valign="top" nowrap><?=implode('<br>', $user_group_string_array)?></td>
						<td class="reportRecords" valign="top"><?=$status_configuration_manual_notification?></td>
						<td class="reportRecords" valign="top"><?=$status_configuration_auto_notification?></td>
						<td class="reportRecords" align="center" valign="top">
							<a href="<?=tep_href_link(FILENAME_ORDERS_STATUS_CONF, 'action=edit&type=AFTGR&from=' . $tab_key . '&to=' . $key)?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
						</td>
					</tr>
<?
					$new_section = false;
					$row_count++;
				}
?>
				</table>
				</div>
			</div>
<?
}
?>
		</td>
	</tr>
<?php
	}
?>
<!-- AFT Group Level Configuration Menu {end} -->
		</td>
	</tr>
<?php
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<script type="text/javascript">
jQuery(document).ready(function() {
	jQuery("#customer_status-tab > ul").tabs();
    jQuery("#customer_aft_status-tab > ul").tabs();
});
</script>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>