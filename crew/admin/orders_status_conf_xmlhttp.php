<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

$action = isset($_REQUEST['action']) ? tep_db_input($_REQUEST['action']) : '';
$type = isset($_REQUEST['type']) ? tep_db_input($_REQUEST['type']) : '';
$from = (int)(isset($_REQUEST['from']) ? $_REQUEST['from'] : '');
$to = (int)(isset($_REQUEST['to']) ? $_REQUEST['to'] : '');
$group_id = (int)(isset($_REQUEST['group_id']) ? $_REQUEST['group_id'] : '');

echo '<response>';

if (tep_not_null($action)){
	switch($action) {
		case "get_payment_methods":
			echo '<methods>';
			if ($group_id > 0) {
				$payment_gateway_list_array = array();
				$payment_method_list_array = array();
				$payment_method_selected_array = array();
				
				$assigned_payment_select_sql = "SELECT status_configuration_payment_methods_id
												FROM " . TABLE_STATUS_CONFIGURATION_PAYMENT_METHODS . " 
												WHERE status_configuration_trans_type='".$type."' 
													AND status_configuration_source_status_id = '".$from."' 
													AND status_configuration_destination_status_id = '".$to."' 
													AND status_configuration_user_groups_id='".$group_id."'";
				$assigned_payment_result_sql = tep_db_query($assigned_payment_select_sql);
				if ($assigned_payment_row = tep_db_fetch_array($assigned_payment_result_sql)) {
					$payment_method_selected_array = explode (",",$assigned_payment_row['status_configuration_payment_methods_id']);
				}

				$method_parents_select_sql = "	SELECT payment_methods_id, payment_methods_title 
												FROM " . TABLE_PAYMENT_METHODS . " 
												WHERE payment_methods_receive_status = 1 
													AND payment_methods_parent_id = 0
												ORDER BY payment_methods_sort_order";
				$method_parents_result_sql = tep_db_query($method_parents_select_sql);
				while ($method_parents_row = tep_db_fetch_array($method_parents_result_sql)) {
					$payment_gateway_list_array[$method_parents_row['payment_methods_id']] = array(	'id' => $method_parents_row['payment_methods_id'],
																									'text' => $method_parents_row['payment_methods_title'],
																									'parent_id' => 0,
																									'checked' => ''
																								);
				}
				
				$method_child_select_sql = "	SELECT payment_methods_id, payment_methods_title, payment_methods_parent_id 
												FROM " . TABLE_PAYMENT_METHODS . " 
												WHERE payment_methods_parent_id IN ('" . implode("','",array_keys($payment_gateway_list_array)) . "')
													AND payment_methods_receive_status = 1 
												ORDER BY payment_methods_sort_order";
				$method_child_result_sql = tep_db_query($method_child_select_sql);
				while ($method_child_row = tep_db_fetch_array($method_child_result_sql)) {
					$payment_method_list_array[$method_child_row['payment_methods_parent_id']][$method_child_row['payment_methods_id']] = array(	'id' => $method_child_row['payment_methods_id'],
																																					'text' => $method_child_row['payment_methods_title'],
																																					'parent_id' => $method_child_row['payment_methods_parent_id'],
																																					'checked' => in_array($method_child_row['payment_methods_id'],$payment_method_selected_array) ? 'checked=\"\"' : ''
																																				);
				}
				
				foreach($payment_gateway_list_array as $payment_gateway_list_id => $payment_gateway_list_data){
						echo "	<method id='".$payment_gateway_list_id."' parent_id='0' checked='".$payment_gateway_list_data['checked']."'><![CDATA[".strip_tags($payment_gateway_list_data['text'])."]]></method>";
						foreach($payment_method_list_array[$payment_gateway_list_id] as $payment_method_list_id => $payment_method_list_data){
							echo "	<method id='".$payment_method_list_id."' parent_id='".$payment_gateway_list_id."' checked='".$payment_method_list_data['checked']."'><![CDATA[".strip_tags($payment_method_list_data['text'])."]]></method>";
						}
				}
			}
			echo '</methods>';
			break;

		case "update_payment_methods":
			$payment_method_selected_array = array();
			if(isset($_REQUEST['selected_methods'])){
    			$conf_sql_data_array = array('status_configuration_payment_methods_id' => tep_db_prepare_input($_REQUEST['selected_methods']));							
				tep_db_perform(TABLE_STATUS_CONFIGURATION_PAYMENT_METHODS, $conf_sql_data_array, 'update', ' status_configuration_trans_type="'.$type.'" AND status_configuration_source_status_id = "'.$from.'" AND status_configuration_destination_status_id = "'.$to.'" AND status_configuration_user_groups_id="'.$group_id.'"');
			}
			break;
	}
}
echo '</response>';
?>