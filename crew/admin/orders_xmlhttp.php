<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'log.php');
include_once(DIR_WS_CLASSES . 'edit_order.php');
include_once(DIR_WS_CLASSES . 'payment_methods.php');
include_once(DIR_WS_CLASSES . 'process_locking.php');
include_once(DIR_WS_LANGUAGES . $language . '/' . 'orders.php');
include_once(DIR_WS_CLASSES . 'c2c_order.php');
include_once(DIR_WS_CLASSES . 'ms_store_credit.php');
include_once(DIR_WS_CLASSES . 'g2g_serverless.php');

$currencies = new currencies();

define('PARTIAL_DELIVERY_STATUS', 2);
define('PARTIAL_RECEIVE_STATUS', 1);
define('GST_DEFAULT_LANGUAGE_ID', 1);

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$subaction = isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '';
$admin_id = isset($_SESSION['login_id']) ? $_SESSION['login_id'] : '';
$customers_id = isset($HTTP_GET_VARS['customers_id']) ? $HTTP_GET_VARS['customers_id'] : '';
$this_admin_email = isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : '';
$admin_email = isset($HTTP_GET_VARS['adm_email']) ? $HTTP_GET_VARS['adm_email'] : '';
$order_id = isset($HTTP_GET_VARS['oid']) ? (int) $HTTP_GET_VARS['oid'] : '';
$languages_id = isset($_SESSION['languages_id']) ? $_SESSION['languages_id'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : '';
$aft_function_id = isset($_SESSION['aft_function_id']) ? $_SESSION['aft_function_id'] : '';
$customers_groups_id = isset($HTTP_GET_VARS['customers_groups_id']) ? $HTTP_GET_VARS['customers_groups_id'] : '';
$customer_email = isset($HTTP_GET_VARS['customer_email']) ? $HTTP_GET_VARS['customer_email'] : '';

$log_comment = isset($HTTP_GET_VARS['log_comment']) ? tep_db_prepare_input($HTTP_GET_VARS['log_comment']) : '';

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "orders_xmlhttp.php")) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . "orders_xmlhttp.php");
}
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . ".php")) {
    include_once(DIR_WS_LANGUAGES . $language . '/' . ".php");
}

$admin_group_id = isset($_SESSION['login_groups_id']) ? $_SESSION['login_groups_id'] : '';

echo '<response>';

if (tep_not_null($action)) {
    switch ($action) {
        case 'create_bo':
            include_once(DIR_WS_CLASSES . 'products_supplier.php');

            if (isset($_GET['opid']) && tep_not_null($_GET['opid'])) {
                $order_products_id = $_GET['opid'];
                $check_buyback_order_sql = "SELECT br.buyback_request_id
                                            FROM " . TABLE_BUYBACK_REQUEST . " AS br
                                            INNER JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
                                                ON (br.buyback_request_group_id = brg.buyback_request_group_id)
                                            WHERE br.orders_products_id = '" . tep_db_input($order_products_id) . "'
                                                AND brg.buyback_status_id <> 4";
                $check_buyback_order_result = tep_db_query($check_buyback_order_sql);
                if (!tep_db_num_rows($check_buyback_order_result)) {
                    $products_hla_info = tep_draw_products_extra_info($order_products_id, 'products_hla');
                    if (count($products_hla_info) > 0) {
                        $products_hla_info = tep_array_unserialize($products_hla_info['products_hla']);
                        if (tep_not_null($products_hla_info['products_hla_id'])) {
                            $products_hla_id = $products_hla_info['products_hla_id'];
                            $seller_id = $products_hla_info['seller_id'];

                            $get_hla_select_sql = "	SELECT products_hla_id, products_hla_reserve_id
                                                        FROM " . TABLE_PRODUCTS_HLA . "
                                                        WHERE products_hla_id = '" . tep_db_input($products_hla_id) . "'
                                                        AND actual_quantity > 0";

                            $get_hla_result_sql = tep_db_query($get_hla_select_sql);
                            if ($get_hla_row = tep_db_fetch_array($get_hla_result_sql)) {
                                tep_create_buyback_order($order_products_id, $products_hla_info, $buyback_request_group_id);

                                // If successful created BO
                                if (tep_not_null($buyback_request_group_id)) {
                                    $prod_sup_obj = new products_supplier($seller_id);

                                    $api_file_path = DIR_WS_MODULES . 'hla/' . $prod_sup_obj->products_supplier['supplier_code'] . '/api.php';

                                    if (file_exists($api_file_path)) {
                                        include_once($api_file_path);

                                        $supplier_api = new api($products_hla_id, $this_admin_email);
                                        $supplier_api->account_retrieve_info($buyback_request_group_id, $seller_id);
                                    }
                                }
                                echo '<result>done</result>';
                            } else {
                                echo '<error>This hla product has been taken.</error>';
                            }
                        } else {
                            echo '<error>Error1</error>';
                        }
                    } else {
                        echo '<error>Error2</error>';
                    }
                } else {
                    echo '<error>Error3</error>';
                }
            } else {
                echo '<error>Error4</error>';
            }
            break;
        case 'create_c2c_bo':
            $result = '<error>Error</error>';
            if (isset($_GET['opid']) && tep_not_null($_GET['opid'])) {
                include_once(DIR_WS_CLASSES . 'c2c_buyback_order.php');

                $so_res = c2c_buyback_order::_create_buyback_order($_GET['opid']);
                if (is_array($so_res)) {
                    if (isset($so_res['errno'])) {
                        if ($so_res['errno'] > 0) {
                            $result = '<error>' . $so_res['error'] . '</error>';
                        } else {
                            $result = '<result>' . $so_res['result'] . '</result>';
                        }
                    }
                }
            }
            echo $result;
            break;
        case "on_hold_eta":
            if (isset($_GET['opid']) && tep_not_null($_GET['opid'])) {
                $order_products_id = $_GET['opid'];
                $closed_buyback_data_sql = array();

                $get_opid_select_sql = "	SELECT orders_id, orders_products_id, parent_orders_products_id
											FROM " . TABLE_ORDERS_PRODUCTS . "
											WHERE orders_products_id = '" . (int) $order_products_id . "'";
                $get_opid_result_sql = tep_db_query($get_opid_select_sql);
                if ($get_opid_row = tep_db_fetch_array($get_opid_result_sql)) {
                    $get_expiry_hour_select_sql = "	SELECT NOW() as nowtime, expiry_hour, start_time, total_buyback_time
													FROM " . TABLE_ORDERS_PRODUCTS_ETA . "
													WHERE orders_products_id = '" . (int) $order_products_id . "'";
                    $get_expiry_hour_result_sql = tep_db_query($get_expiry_hour_select_sql);
                    if ($get_expiry_hour_row = tep_db_fetch_array($get_expiry_hour_result_sql)) {
                        $insert_record_data_sql['changed_by'] = $this_admin_email;
                        $insert_record_data_sql['date_added'] = 'now()';
                        $insert_record_data_sql['last_updated'] = 'now()';
                        $insert_record_data_sql['orders_id'] = $get_opid_row['orders_id'];
                        $insert_record_data_sql['orders_products_id'] = $order_products_id;
                        $insert_record_data_sql['orders_products_history_record'] = 'TEXT_BUYBACK_CLOSED:~:TEXT_BUYBACK_CLOSED_UNABLE_LOCATE_CUSTOMER:~:Admin';

                        tep_db_perform(TABLE_ORDERS_PRODUCTS_HISTORY, $insert_record_data_sql, 'insert');

                        $total_buyback_time = tep_day_diff($get_expiry_hour_row['start_time'], $get_expiry_hour_row['nowtime'], 'sec');
                        $total_buyback_time += $get_expiry_hour_row['total_buyback_time'];

                        $closed_buyback_data_sql['total_buyback_time'] = $total_buyback_time;
                    }

                    $update_purchase_eta_data_sql = array('orders_products_purchase_eta' => '-999');
                    tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $get_opid_row['orders_products_id'] . '"');
                    if ($get_opid_row['parent_orders_products_id'] > 0) {
                        tep_db_perform(TABLE_ORDERS_PRODUCTS, $update_purchase_eta_data_sql, 'update', ' orders_products_id = "' . (int) $get_opid_row['parent_orders_products_id'] . '"');
                    }

                    $closed_buyback_data_sql['expiry_hour'] = '0';

                    tep_db_perform(TABLE_ORDERS_PRODUCTS_ETA, $closed_buyback_data_sql, 'update', ' orders_products_id = "' . (int) $order_products_id . '"');
                    echo '<result>done</result>';
                }
            } else {
                echo '<error>Error</error>';
            }
            break;
        case "onecard":
            switch ($subaction) {
                case "check_voucher_code_entered":
                    $success_flag = false;
                    $message = 'Invalid Order ID';
                    if (isset($_REQUEST['oID']) && tep_not_null($_REQUEST['oID'])) {
                        $message = 'Voucher Code is missing. Please provide Voucher Code before check Payment Status.';
                        $voucher_code_select_sql = "SELECT onecard_voucher_code
													FROM " . TABLE_ONECARD . "
													WHERE onecard_orders_id = '" . (int) $_REQUEST['oID'] . "'";
                        $voucher_code_result_sql = tep_db_query($voucher_code_select_sql);
                        if ($voucher_code_row = tep_db_fetch_array($voucher_code_result_sql)) {
                            if (tep_not_null($voucher_code_row['onecard_voucher_code'])) {
                                $success_flag = true;
                            }
                        }
                    }
                    echo "	<success><![CDATA[" . ($success_flag ? 1 : 0) . "]]></success>
							<message><![CDATA[" . $message . "]]></message>";
                    break;
                case "update_voucher_code":
                    $success_flag = false;
                    $message = 'Incomplete data submitted.';
                    if (isset($_REQUEST['oID']) && tep_not_null($_REQUEST['oID']) && isset($_REQUEST['voucher_code']) && tep_not_null($_REQUEST['voucher_code'])) {
                        $voucher_code = trim($_REQUEST['voucher_code']);
                        $order_info_select_sql = "	SELECT o.payment_methods_id, o.currency
													FROM " . TABLE_ORDERS . " AS o
													WHERE o.orders_id = '" . (int) $_REQUEST['oID'] . "'";
                        $order_info_result_sql = tep_db_query($order_info_select_sql);
                        if ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
                            require(DIR_FS_CATALOG . DIR_WS_INCLUDES . 'modules/payment/onecard.php');
                            require(DIR_FS_CATALOG . DIR_WS_INCLUDES . 'modules/payment/onecard/classes/onecard_class.php');

                            include_once(DIR_WS_CLASSES . 'order.php');
                            include_once(DIR_WS_CLASSES . 'anti_fraud.php');
                            include_once(DIR_WS_FUNCTIONS . 'custom_product.php');

                            $onecard_obj = new onecard($order_info_row['payment_methods_id']);
                            $log_object = new log_files('system');

                            $voucher_code_select_sql = "SELECT onecard_voucher_code
														FROM " . TABLE_ONECARD . "
														WHERE onecard_orders_id = '" . (int) $_REQUEST['oID'] . "'";
                            $voucher_code_result_sql = tep_db_query($voucher_code_select_sql);
                            if ($voucher_code_row = tep_db_fetch_array($voucher_code_result_sql)) {
                                if ($voucher_code_row['onecard_voucher_code'] != $voucher_code) {
                                    $onecard_data_sql = array("onecard_voucher_code" => tep_db_prepare_input($voucher_code));
                                    tep_db_perform(TABLE_ONECARD, $onecard_data_sql, 'update', " onecard_orders_id = '" . (int) $_REQUEST['oID'] . "' ");

                                    $message = 'Voucher Code Updated.';

                                    $orders_status_history_data_sql = array('onecard_orders_id' => (int) $_REQUEST['oID'],
                                        'onecard_status_history_voucher_code' => tep_db_prepare_input($voucher_code),
                                        'onecard_status_history_datetime' => 'now()',
                                        'onecard_status_history_description' => tep_db_prepare_input("Voucher Code Updated\n" . $voucher_code_row['onecard_voucher_code'] . " -> " . $voucher_code),
                                        'onecard_status_history_status' => '',
                                        'onecard_changed_by' => tep_db_prepare_input($login_email_address));
                                    tep_db_perform(TABLE_ONECARD_STATUS_HISTORY, $orders_status_history_data_sql);
                                    if ($onecard_obj->check_trans_status((int) $_REQUEST['oID'])) {
                                        $onecard_class_obj = new onecard_class();
                                        $onecard_class_obj->process_this_order((int) $_REQUEST['oID'], $onecard_obj, $onecard_obj->order_processing_status);
                                    }
                                    $success_flag = true;
                                } else {
                                    $message = '';  // no changed on voucher code
                                }
                            } else {
                                $onecard_data_sql = array("onecard_orders_id" => (int) $_REQUEST['oID'],
                                    "onecard_voucher_code" => tep_db_prepare_input($voucher_code),
                                    "onecard_expired" => '',
                                    "onecard_date" => 'now()');
                                tep_db_perform(TABLE_ONECARD, $onecard_data_sql);

                                $orders_status_history_data_sql = array('onecard_orders_id' => (int) $_REQUEST['oID'],
                                    'onecard_status_history_voucher_code' => tep_db_prepare_input($voucher_code),
                                    'onecard_status_history_datetime' => 'now()',
                                    'onecard_status_history_description' => tep_db_prepare_input("Voucher Code Inserted\n" . $voucher_code),
                                    'onecard_status_history_status' => '',
                                    'onecard_changed_by' => tep_db_prepare_input($login_email_address));
                                tep_db_perform(TABLE_ONECARD_STATUS_HISTORY, $orders_status_history_data_sql);

                                $message = 'Voucher Code Updated.';
                                $success_flag = true;
                                if ($onecard_obj->check_trans_status((int) $_REQUEST['oID'])) {
                                    $onecard_class_obj = new onecard_class();
                                    $onecard_class_obj->process_this_order((int) $_REQUEST['oID'], $onecard_obj, $onecard_obj->order_processing_status);
                                }
                            }
                        } else {
                            $message = 'Order ID not found.';
                            $success_flag = false;
                        }
                    }
                    echo "	<success><![CDATA[" . ($success_flag ? 1 : 0) . "]]></success>
							<message><![CDATA[" . $message . "]]></message>";
                    break;
            }

            break;
        case "show_lock_btn":
            $log_object = new log_files($admin_id);
            $lock_obj = new process_locking();

            $log_object->set_log_table(TABLE_ORDERS_LOG_TABLE);
            if ($subaction == "ul" || $subaction == "ulo") { // unlocking
                $lock_orders_select_sql = "	SELECT o.orders_locked_by, o.orders_locked_from_ip, o.orders_locked_datetime, a.admin_email_address
											FROM " . TABLE_ORDERS . " AS o
											LEFT JOIN " . TABLE_ADMIN . " AS a
												ON (o.orders_locked_by = a.admin_id)
											WHERE orders_id = '" . $order_id . "'";
                $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);
                $lock_status = $lock_obj->orderIsLocked($admin_id, $order_id, process_locking::MOVE_ORDER, $lock_orders_row);

                if ($lock_status === false) {
                    $unlock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_id = '" . $order_id . "'";
                    tep_db_query($unlock_orders_update_sql);
                    echo '	<result>This order has been successfully unlocked!</result>
							<action>Show Lock Button</action>
							<time>' . date("Y-m-d H:i:s") . '</time>
							<lock_msg><![CDATA[' . TEXT_ORDER_NOT_BEEN_LOCKED . ']]></lock_msg>';

                    $log_object->insert_orders_log($order_id, ORDERS_LOG_UNLOCK_OWN_ORDER, FILENAME_ORDERS);
                } else {
                    if ($lock_status === true) { // locked by other people
                        $admin_group_to_contact = tep_admin_group_unlock_permission();
                        if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
                            $contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
                            $contact_admin_group_id_array = array_keys($admin_group_to_contact);
                        } else {
                            $contact_admin_group_msg = '';
                            $contact_admin_group_id_array = array();
                        }

                        if (in_array($admin_group_id, $contact_admin_group_id_array)) { // this admin has the permission to unlock other orders
                            $timeOver = false;
                            // check history for last processing date time
                            $orderStatHistorySql = " SELECT (DATE_ADD(now(), INTERVAL 1 HOUR) > date_added) as date_added_over
                                                        FROM " . TABLE_ORDERS_STATUS_HISTORY . "
                                                        WHERE orders_id = '" . $order_id . "'
                                                        ORDER BY orders_status_history_id DESC LIMIT 1";
                            $orderStatHistoryResult = tep_db_query($orderStatHistorySql);
                            if ($orderStatHistoryRow = tep_db_fetch_array($orderStatHistoryResult)) {
                                $timeOver = (isset($orderStatHistoryRow['date_added_over']) && $orderStatHistoryRow['date_added_over']) ? true : false;
                            }

                            if (($lock_obj->orderIsLockedBy() !== process_locking::DEFAULT_LOG_IDENTITY || $timeOver) && $subaction == "ulo") {
                                $unlock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_id = '" . $order_id . "'";
                                tep_db_query($unlock_orders_update_sql);

                                if ($timeOver) {
                                    $delete_temp_process = "DELETE FROM " . TABLE_TEMP_PROCESS . "
                                                            WHERE match_case = '" . $order_id ."' LIMIT 1";
                                    tep_db_query($delete_temp_process);
                                }

                                echo "<result>This order has been successfully unlocked!" . $log_comment . "</result>";
                                echo "<action>Show Lock Button</action>";
                                echo "<time>" . date("Y-m-d H:i:s") . "</time>";
                                echo "<lock_msg><![CDATA[" . TEXT_ORDER_NOT_BEEN_LOCKED . "]]></lock_msg>";

                                $log_object->insert_orders_log($order_id, sprintf(ORDERS_LOG_UNLOCK_OTHER_PEOPLE_ORDER, $lock_obj->getOrderIsLockedByEmail(), $log_comment), FILENAME_ORDERS);
                            } else {
                                $locked_by = $lock_obj->orderIsLockedBy() !== process_locking::DEFAULT_LOG_IDENTITY ? 'someone else' : $lock_obj->orderIsLockedBy();
                                echo "<result>This order is locked by " . $locked_by . "!</result>";
                                echo "<action>Show Unlock Button</action>";
                                echo "<subaction>Prompt For Unlocking Msg</subaction>";
                                echo "<lock_msg><![CDATA[" . sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, $lock_obj->getOrderIsLockedByEmail(), $lock_obj->getOrderIsLockedDT(), $lock_obj->getOrderIsLockedIP()) . "]]></lock_msg>";
                            }
                        } else {
                            echo "<result>Unlock order is failed!</result>";
                            echo "<action>Show Failed Lock Msg</action>";
                            echo "<lock_msg><![CDATA[" . sprintf(TEXT_ORDER_LOCKED_BY_OTHER, $lock_obj->getOrderIsLockedByEmail(), $lock_obj->getOrderIsLockedDT(), $lock_obj->getOrderIsLockedIP(), $contact_admin_group_msg) . "]]></lock_msg>";
                        }
                    } else { // nobody lock this order
                        echo "<result>You are not locking this order!</result>";
                        echo "<action>Show Lock Button</action>";
                        echo "<time>" . date("Y-m-d H:i:s") . "</time>";
                        echo "<lock_msg><![CDATA[" . TEXT_ORDER_NOT_BEEN_LOCKED . "]]></lock_msg>";
                    }
                }
            } else if ($subaction == "l") {  // locking
                $lock_orders_select_sql = "	SELECT o.orders_locked_by, o.orders_locked_from_ip, o.orders_locked_datetime, a.admin_email_address
											FROM " . TABLE_ORDERS . " AS o
											LEFT JOIN " . TABLE_ADMIN . "
												AS a ON (o.orders_locked_by = a.admin_id)
											WHERE orders_id = '" . $order_id . "'";
                $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);
                $lock_status = $lock_obj->orderIsLocked($admin_id, $order_id, process_locking::MOVE_ORDER, $lock_orders_row);

                if (!is_null($lock_status)) { // this order currently is locked
                    if ($lock_status == false) {
                        echo "<result>You had been locking this order!</result>";
                        echo "<action>Prompt Alert Message</action>";
                        echo "<close_win>1</close_win>";
                        echo "<lock_msg><![CDATA[" . TEXT_DUPLICATE_LOCKED_ORDER_SEEN_BY_OWNER . "]]></lock_msg>";
                    } else {
                        $admin_group_to_contact = tep_admin_group_unlock_permission();
                        if (is_array($admin_group_to_contact) && count($admin_group_to_contact)) {
                            $contact_admin_group_msg = '<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;', $admin_group_to_contact);
                            $contact_admin_group_id_array = array_keys($admin_group_to_contact);
                        } else {
                            $contact_admin_group_msg = '';
                            $contact_admin_group_id_array = array();
                        }

                        if (in_array($admin_group_id, $contact_admin_group_id_array)) { // this admin has the permission to unlock other orders
                            echo "<result>Lock order is failed!</result>";
                            echo "<action>Show Unlock Button</action>";
                            echo "<subaction>Prompt For Unlocking Msg</subaction>";
                            echo "<lock_msg><![CDATA[" . sprintf(TEXT_UNLOCK_OTHER_PEOPLE_ORDER, $lock_obj->getOrderIsLockedByEmail(), $lock_obj->getOrderIsLockedDT(), $lock_obj->getOrderIsLockedIP()) . "]]></lock_msg>";
                        } else {
                            echo "<result>Lock order is failed!</result>";
                            echo "<action>Show Failed Lock Msg</action>";
                            echo "<lock_msg><![CDATA[" . sprintf(TEXT_ORDER_LOCKED_BY_OTHER, $lock_obj->getOrderIsLockedByEmail(), $lock_obj->getOrderIsLockedDT(), $lock_obj->getOrderIsLockedIP(), $contact_admin_group_msg) . "]]></lock_msg>";
                        }
                    }
                } else {
                    // check order status
                    $order_status_select_sql = "SELECT orders_status FROM " . TABLE_ORDERS . " WHERE orders_id = '" . $order_id . "'";
                    $order_status_result_sql = tep_db_query($order_status_select_sql);
                    $order_status_row = tep_db_fetch_array($order_status_result_sql);

                    if ($order_status_row["orders_status"] != PARTIAL_DELIVERY_STATUS) {
                        echo "<result>This order cannot be locked!</result>";
                        echo "<action>Prompt Alert Message</action>";
                        echo "<lock_msg><![CDATA[" . TEXT_LOCKED_ORDER_NOT_VALID_STATUS . "]]></lock_msg>";
                    } else {
                        $from_time = $HTTP_GET_VARS['from_time'];
                        $check_within_locking_select_sql = "	SELECT orders_log_id
																FROM " . TABLE_ORDERS_LOG_TABLE . "
																WHERE orders_log_orders_id = '" . $order_id . "'
																	AND DATE_FORMAT(orders_log_time, '%Y-%m-%d %H:%i:%s') >= '" . $from_time . "'
																	AND orders_log_admin_id <> '" . $admin_id . "'
																	AND orders_log_filename = '" . tep_db_input(FILENAME_ORDERS) . "'";
                        $check_within_locking_result_sql = tep_db_query($check_within_locking_select_sql);
                        if (tep_db_num_rows($check_within_locking_result_sql)) { // someone lock and unlock before you manage to lock it.
                            echo "<result>This order has been updated by someone!</result>";
                            echo "<action>Prompt Alert Message</action>";
                            echo "<lock_msg><![CDATA[" . TEXT_LOCKED_OUTDATED_ORDER . "]]></lock_msg>";
                        } else {
                            $lock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = '" . $admin_id . "', orders_locked_from_ip='" . tep_db_input(getenv("REMOTE_ADDR")) . "', orders_locked_datetime=now() WHERE orders_id = '" . $order_id . "' AND (orders_locked_by IS NULL OR orders_locked_by='')";
                            tep_db_query($lock_orders_update_sql);

                            $lock_orders_select_sql = "SELECT o.orders_locked_by, o.orders_locked_from_ip, o.orders_locked_datetime, a.admin_email_address FROM " . TABLE_ORDERS . " AS o inner join " . TABLE_ADMIN . " AS a ON o.orders_locked_by=a.admin_id WHERE orders_id = '" . $order_id . "'";
                            $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
                            $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);

                            echo "<result>This order has been successfully locked!</result>";
                            echo "<action>Show Unlock Button</action>";
                            echo "<lock_msg>" . sprintf(TEXT_LOCKED_ORDER_SEEN_BY_OWNER, $lock_orders_row["orders_locked_datetime"], $lock_orders_row["orders_locked_from_ip"]) . "</lock_msg>";

                            $log_object->insert_orders_log($order_id, ORDERS_LOG_LOCK_ORDER, FILENAME_ORDERS);
                        }
                    }
                }
            } else {
                echo "<result>Unknown request!</result>";
            }

            unset($lock_obj);
            break;
        case "get_comments":
            $comment_id = (int) $HTTP_GET_VARS['cmID'];
            if ($comment_id == 0) {
                return false;
            } else {
                $order_comment_select_sql = "SELECT orders_comments_text FROM " . TABLE_ORDERS_COMMENTS . " WHERE orders_comments_id='" . $comment_id . "'";
                $order_comment_result_sql = tep_db_query($order_comment_select_sql);

                if ($order_comment_row = tep_db_fetch_array($order_comment_result_sql)) {
                    echo "<result><![CDATA[" . $order_comment_row["orders_comments_text"] . "]]></result>";
                } else {
                    return false;
                }
            }
            break;
        case "set_order_remark":
            if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "orders.php")) {
                include_once(DIR_WS_LANGUAGES . $language . '/' . "orders.php");
            }
            $order_comment_style_array = array("orderCommentSystem", "orderCommentCrew", "orderCommentDelivery");

            $status_history_id = (int) $HTTP_GET_VARS['shid'];

            $orders_history_checking_sql = "SELECT orders_status_history_id FROM " . TABLE_ORDERS_STATUS_HISTORY . " WHERE orders_id = '" . $order_id . "' AND orders_status_history_id = '" . $status_history_id . "' LIMIT 1";
            $orders_history_result_sql = tep_db_query($orders_history_checking_sql);

            if (tep_db_num_rows($orders_history_result_sql) > 0) {
                tep_db_query("UPDATE " . TABLE_ORDERS_STATUS_HISTORY . " SET set_as_order_remarks=0 WHERE orders_id = '" . $order_id . "'");
                tep_db_query("UPDATE " . TABLE_ORDERS_STATUS_HISTORY . " SET set_as_order_remarks=1 WHERE orders_id = '" . $order_id . "' AND orders_status_history_id = '" . $status_history_id . "'");

                $orders_history_select_sql = "	SELECT osh.*, os.orders_status_name
												FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh
												LEFT JOIN " . TABLE_ORDERS_STATUS . " AS os
													ON (osh.orders_status_id = os.orders_status_id)
												WHERE osh.orders_id = '" . (int) $order_id . "' AND (os.language_id = '" . $languages_id . "' OR os.language_id IS NULL)
												ORDER BY osh.date_added";
                $orders_history_result_sql = tep_db_query($orders_history_select_sql);

                $history_text = '<!--div id="order_comment_toggle_box" style="padding-bottom:5px;"><a href="javascript:;" onClick="hideShowOrderComment(\'order_comment_toggle_box\', \'oc_\', \'' . tep_db_num_rows($orders_history_result_sql) . '\', \'hide\');">Show Order Remark Only</a></div-->
	    						 <table border="1" cellspacing="0" cellpadding="5">
          							<tr>
	            						<td class="smallText" align="center"><b>' . TABLE_HEADING_DATE_ADDED . '</b></td>
	            						<td class="smallText" align="center"><b>' . TABLE_HEADING_CUSTOMER_NOTIFIED . '</b></td>
	            						<td class="smallText" align="center"><b>' . TABLE_HEADING_STATUS . '</b></td>
	            						<td class="smallText" align="center"><b>' . TABLE_HEADING_COMMENTS . '</b></td>
	            						<td class="smallText" align="center"><b>' . TABLE_HEADING_MODIFIED_BY . '</b></td>
	            						<td class="smallText" align="center"><b>' . TABLE_HEADING_ACTION . '</b></td>
	          						</tr>';
                $oc_cnt = 0;
                while ($orders_history_row = tep_db_fetch_array($orders_history_result_sql)) {
                    $comment_row_style = $orders_history_row["set_as_order_remarks"] == 1 ? 'class="orderRemarkSelectedRow"' : 'class="' . $order_comment_style_array[$orders_history_row["comments_type"]] . '"';

                    $formatted_date_comment_added = tep_datetime_short($orders_history_row["date_added"]);

                    $history_text .= '	<tbody id="o_comment_' . $orders_history_row["comments_type"] . '_' . $orders_history_row["orders_status_history_id"] . '">' . "\n" .
                            '	<tr ' . $comment_row_style . '>' . "\n" .
                            '		<td class="smallText" align="center">' . (tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--') . '</td>' . "\n" .
                            '		<td class="smallText" align="center">' . "\n";
                    if ($orders_history_row['customer_notified'] == '1') {
                        $history_text .= tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) . "</td>\n";
                    } else {
                        $history_text .= tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS) . '</td>';
                    }
                    $history_text .= ' 		<td class="smallText">' . (tep_not_null($orders_history_row['orders_status_name']) ? $orders_history_row['orders_status_name'] : '--') . '</td>
											<td class="smallText">' . nl2br(str_replace("\t", '&nbsp;&nbsp;&nbsp;&nbsp;', $orders_history_row['comments'])) . '</td>
											<td class="smallText">' . nl2br(tep_db_output($orders_history_row['changed_by'])) . '</td>
											<td class="smallText"><div id="set_remark_' . $orders_history_row["orders_status_history_id"] . '">' . ($orders_history_row['set_as_order_remarks'] == 1 ? '&nbsp;' : '<a href="javascript:;" onClick="setAsOrderRemark(\'' . $orders_history_row["orders_status_history_id"] . '\', \'' . $order_id . '\', \'' . $languages_id . '\', \'order_comment_history_box\');"><u>Set as Order Remark</u></a>') . '</div></td>
										</tr>
									</tbody>';
                    $history_text .= tep_draw_hidden_field('hidden_o_comment_' . $orders_history_row["comments_type"] . '_' . $orders_history_row['orders_status_history_id'], '');
                    $oc_cnt++;
                }
                $history_text .= '</table>';
                echo "<res_code>1</res_code>";
                echo "<result><![CDATA[" . $history_text . "]]></result>";
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>Record not found!</result>";
            }
            break;
        case "get_order_locking_history":
            $LockingCommentPattern = "/(?:##)(\S+)(?:##)(.*)/is";

            $locking_history_select_sql = "SELECT * FROM " . TABLE_ORDERS_LOG_TABLE . " WHERE orders_log_orders_id = '" . $order_id . "' AND orders_log_filename ='" . tep_db_input(FILENAME_ORDERS) . "' ORDER BY orders_log_time";
            $locking_history_result_sql = tep_db_query($locking_history_select_sql);

            $history_text = '<table border="1" cellspacing="0" cellpadding="5">
      							<tr>
            						<td class="smallText" align="center"><b>Action Date</b></td>
            						<td class="smallText" align="center"><b>Comments</b></td>
            						<td class="smallText" align="center"><b>Admin</b></td>
            						<td class="smallText" align="center"><b>IP</b></td>
          						</tr>';
            $history_count = 0;
            while ($locking_history_row = tep_db_fetch_array($locking_history_result_sql)) {
                $locking_comment_msg = '';
                if (preg_match($LockingCommentPattern, $locking_history_row["orders_log_system_messages"], $regs)) {
                    switch ($regs[1]) {
                        case "l_1":
                            $locking_comment_msg = "Locking order";
                            break;
                        case "ul_1":
                            $locking_comment_msg = "Unlocking order";
                            break;
                        case "ul_2":
                            $other_admin_email = $user_msg = '';
                            if (tep_not_null($regs[2])) {
                                list($other_admin_email, $user_msg) = explode(':~:', $regs[2]);
                            }

                            $locking_comment_msg = "Unlocking other people " . (tep_not_null($other_admin_email) ? "(" . $other_admin_email . ")" : '') . " order" . (tep_not_null($user_msg) ? '<br>' . $user_msg : '');
                            break;
                    }
                }

                if (is_numeric($locking_history_row["orders_log_admin_id"])) {
                    $admin_query = tep_db_query("SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $locking_history_row["orders_log_admin_id"] . "'");
                    if ($admin_info = tep_db_fetch_array($admin_query))
                        $admin_email_address = $admin_info["admin_email_address"];
                    else
                        $admin_email_address = $locking_history_row["orders_log_admin_id"] == 0 ? 'System' : $locking_history_row["orders_log_admin_id"];
                } else {
                    $admin_email_address = $locking_history_row["orders_log_admin_id"];
                }
                $history_text .= '	<tr>
  										<td class="smallText" align="center">' . $locking_history_row['orders_log_time'] . '</td>
  										<td class="smallText">' . $locking_comment_msg . '</td>
  										<td class="smallText">' . $admin_email_address . '</td>
  										<td class="smallText">' . $locking_history_row['orders_log_ip'] . '</td>
  									</tr>';
                $history_count++;
            }

            if ($history_count == 0) {
                $history_text .= '	<tr>
										<td class="smallText" colspan="4">No Order Locking History Available</td>
									</tr>';
            }

            $history_text .= '</table>';
            echo "<res_code>1</res_code>";
            echo "<result><![CDATA[" . $history_text . "]]></result>";
            break;
        case "get_order_info":
            if (tep_not_null($HTTP_GET_VARS['o_str'])) {
                if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "orders.php")) {
                    include_once(DIR_WS_LANGUAGES . $language . '/' . "orders.php");
                }
                include_once(DIR_WS_CLASSES . 'order.php');

                $currencies = new currencies();

                $order_ids_array = explode(',', $HTTP_GET_VARS['o_str']);
                echo "<order_info>";
                for ($order_cnt = 0; $order_cnt < count($order_ids_array); $order_cnt++) {
                    // Instantiate order object
                    $order = new order($order_ids_array[$order_cnt]);
                    $order_remark_select_sql = "SELECT comments, changed_by
												FROM " . TABLE_ORDERS_STATUS_HISTORY . "
												WHERE orders_id = '" . $order_ids_array[$order_cnt] . "'
													AND set_as_order_remarks = 1
												LIMIT 1";
                    $order_remark_result_sql = tep_db_query($order_remark_select_sql);
                    if ($order_remark_row = tep_db_fetch_array($order_remark_result_sql)) {
                        $order_remark = $order_remark_row["comments"];
                        $remark_by_admin_email_address = $order_remark_row["changed_by"];
                    } else {
                        $order_remark = $remark_by_admin_email_address = '';
                    }

                    // check whether this order got any tax on purchased products
                    $product_got_tax = false;
                    for ($prod_cnt = 0; $prod_cnt < count($order->products); $prod_cnt++) {
                        if (abs($order->products[$prod_cnt]['tax']) > 0) {
                            $product_got_tax = true;
                            break;
                        }
                    }

                    $total_colspan = 6;
                    if ($product_got_tax)
                        $total_colspan++;
                    if ($order->info['orders_status'] == PARTIAL_DELIVERY_STATUS)
                        $total_colspan += 2;

                    echo "<order_detail order_id='" . $order_ids_array[$order_cnt] . "'><![CDATA[";
                    echo '		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			  						<tr>
				          				<td colspan="' . $total_colspan . '"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 2px; padding-top: 3px;"></td>
				          			</tr>';
                    if ($order->info['orders_status'] == PARTIAL_DELIVERY_STATUS) {
                        echo '		<tr>
	            						<td class="subInvoiceBoxHeading" colspan="2" width="40%" >' . TABLE_HEADING_PRODUCTS . '</td>
	            						<td class="subInvoiceBoxHeading" align="center" width="6%">' . TABLE_HEADING_STOCK_QUANTITY . '</td>
	            						<td class="subInvoiceBoxHeading" align="center" width="6%">' . TABLE_HEADING_QUANTITY . '</td>
		            					<td class="subInvoiceBoxHeading" align="center" width="6%">' . TABLE_HEADING_PREV_DELIVERY . '</td>
		            					<td class="subInvoiceBoxHeading" align="center" width="6%">' . TABLE_HEADING_BALANCE . '</td>
	            						<td class="subInvoiceBoxHeading" align="right" width="9%">' . TABLE_HEADING_PRICE . '</td>
	            						<td class="subInvoiceBoxHeading" align="right" width="9%">' . TABLE_HEADING_TOTAL_EXCLUDING_TAX . '</td>';
                        if ($product_got_tax) {
                            echo '		<td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_TOTAL_INCLUDING_TAX . '</td>';
                        }
                        echo '     	</tr>';
                    } else {
                        echo '		<tr>
						            	<td class="subInvoiceBoxHeading" colspan="2" width="46%" >' . TABLE_HEADING_PRODUCTS . '</td>
	            						<td class="subInvoiceBoxHeading" align="center" width="8%">' . TABLE_HEADING_STOCK_QUANTITY . '</td>
	            						<td class="subInvoiceBoxHeading" align="center" width="8%">' . TABLE_HEADING_QUANTITY . '</td>
	            						<td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_PRICE . '</td>
	            						<td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_TOTAL_EXCLUDING_TAX . '</td>';
                        if ($product_got_tax) {
                            echo '		<td class="subInvoiceBoxHeading" align="right" width="10%">' . TABLE_HEADING_TOTAL_INCLUDING_TAX . '</td>';
                        }
                        echo '		</tr>';
                    }
                    echo '			<tr>
										<td colspan="' . $total_colspan . '"><div style="border-bottom: 1px solid #996600; "></td>
									</tr>
									<tr><td></td></tr>';

                    for ($i = 0, $n = sizeof($order->products); $i < $n; $i++) {
                        $product_info_select_sql = "SELECT p.products_id, p.products_actual_quantity, p.products_quantity, p.products_bundle_dynamic, p.products_bundle, p.products_skip_inventory, pc.categories_id
		    	 									FROM " . TABLE_PRODUCTS . " AS p
		    	 									LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
		      											ON p.products_id=pc.products_id
		    	 									WHERE p.products_id = '" . $order->products[$i]['id'] . "' AND pc.products_is_link=0 ";
                        $product_info_result_sql = tep_db_query($product_info_select_sql);
                        $row_maincat = tep_db_fetch_array($product_info_result_sql);

                        $prod_maincatpath = tep_output_generated_category_path_sq($row_maincat['categories_id']);
                        $prod_qty_store = ($row_maincat['products_skip_inventory'] ? TEXT_OPTION_NOT_APPLICABLE : ($row_maincat['products_actual_quantity'] < 0 ? "<span style='color:red;'>" . $row_maincat['products_actual_quantity'] . "</span>" : $row_maincat['products_actual_quantity'] ));

                        $sub_table = "";
                        if ($row_maincat['products_bundle_dynamic'] == "yes") {
                            $prod_qty_store = TEXT_OPTION_NOT_APPLICABLE;
                            $ordered_qty_text = $order->products[$i]['qty'];
                            for ($pbd_loop = 0; $pbd_loop < count($order->products[$i]["bundle"]); $pbd_loop++) {
                                $subprod_name = "";
                                $subprod_loc = "";
                                $subprod_stock = "";
                                $subprod_qty = "";
                                $subprod_info_select_sql = "SELECT p.products_id, p.products_actual_quantity, p.products_quantity, pd.products_location, pc.categories_id
															FROM " . TABLE_PRODUCTS . " AS p
															LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
																ON p.products_id=pd.products_id
															LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
																ON pd.products_id=pc.products_id
															WHERE p.products_id = " . $order->products[$i]["bundle"][$pbd_loop]['id'] . " AND pc.products_is_link=0 AND pd.language_id = '" . $languages_id . "' ORDER BY pc.products_id ";
                                $subprod_info_result_sql = tep_db_query($subprod_info_select_sql);
                                $subprod_info = tep_db_fetch_array($subprod_info_result_sql);
                                $subprod_maincatpath = ($subprod_info["products_id"]) ? "<span class='categoryPath'>[" . tep_output_generated_category_path_sq($subprod_info['categories_id']) . "]</span>" : "**--This product is no longer existing in db--**";
                                if ($subprod_info["products_id"]) {
                                    $subprod_name = '&nbsp;&nbsp;' . '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($subprod_info['categories_id']) . '&pID=' . $subprod_info["products_id"] . '&selected_box=catalog&' . $HTTP_GET_VARS['SID']) . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $subprod_info["products_id"] . '">' . $order->products[$i]["bundle"][$pbd_loop]["name"] . '</a>';
                                } else {
                                    $subprod_name = '&nbsp;&nbsp;' . $order->products[$i]["bundle"][$pbd_loop]["name"];
                                }
                                $subprod_name .= "<br>&nbsp;&nbsp;" . $subprod_maincatpath;
                                $subprod_loc = ($subprod_info["products_location"] ? $subprod_info["products_location"] : TEXT_OPTION_NOT_APPLICABLE);
                                $subprod_stock = (tep_check_product_skip_inventory($order->products[$i]["bundle"][$pbd_loop]['id']) ? TEXT_OPTION_NOT_APPLICABLE : ($subprod_info["products_actual_quantity"] < 0 ? "<span style='color:red;'>" . $subprod_info["products_actual_quantity"] . "</span>" : $subprod_info["products_actual_quantity"] ));
                                $subprod_total_qty = $order->products[$i]["bundle"][$pbd_loop]["qty"];
                                $subprod_qty = $subprod_total_qty;

                                $sub_table .= '	<tr>
													<td class="invoiceRecords" valign="top" colspan="2">' . $subprod_name . '</td>
													<td class="invoiceRecords" align="center" valign="top">' . $subprod_stock . '</td>
													<td class="invoiceRecords" align="center" valign="top">' . $subprod_qty . '</td>';
                                if ($order->info['orders_status'] == PARTIAL_DELIVERY_STATUS) {
                                    $prev_balance = ($order->products[$i]["bundle"][$pbd_loop]["delivered_qty"] == (int) $order->products[$i]["bundle"][$pbd_loop]["delivered_qty"]) ? (int) $order->products[$i]["bundle"][$pbd_loop]["delivered_qty"] : $order->products[$i]["bundle"][$pbd_loop]["delivered_qty"];
                                    $balance_qty = $subprod_total_qty - $prev_balance;

                                    if (tep_check_product_skip_inventory($order->products[$i]["bundle"][$pbd_loop]['id'])) {
                                        $balance_qty_text = '<span class="sufficientStock">' . $balance_qty . '</span>';
                                    } else {
                                        if ($subprod_info["products_actual_quantity"] >= $balance_qty) {
                                            $balance_qty_text = '<span class="sufficientStock">' . $balance_qty . '</span>';
                                        } else {
                                            $balance_qty_text = '<span class="lowStock">' . $balance_qty . '</span>';
                                        }
                                    }
                                    $sub_table .= '	<td class="invoiceRecords" align="center" valign="top">' . $prev_balance . '</td>
													<td class="invoiceRecords" align="center" valign="top">' . $balance_qty_text . '</td>';
                                }
                                $sub_table .= '		<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>
													<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>';
                                if ($product_got_tax) {
                                    $sub_table .= '	<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>';
                                }
                                $sub_table .= '	</tr>';
                            }
                        } else if ($row_maincat['products_bundle'] == "yes") {
                            $prod_qty_store = TEXT_OPTION_NOT_APPLICABLE;
                            $ordered_qty_text = $order->products[$i]['qty'];

                            for ($static_loop = 0; $static_loop < count($order->products[$i]["static"]); $static_loop++) {
                                $subprod_name = "";
                                $subprod_loc = "";
                                $subprod_stock = "";
                                $subprod_qty = "";

                                $subprod_info_select_sql = "SELECT p.products_id, p.products_actual_quantity, p.products_quantity, pd.products_location, pc.categories_id
															FROM " . TABLE_PRODUCTS . " AS p
															LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
																ON p.products_id=pd.products_id
															LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
																ON pd.products_id=pc.products_id
															WHERE p.products_id = " . $order->products[$i]["static"][$static_loop]['id'] . " AND pc.products_is_link=0 AND pd.language_id = '" . $languages_id . "' ORDER BY pc.products_id ";
                                $subprod_info_result_sql = tep_db_query($subprod_info_select_sql);
                                $subprod_info = tep_db_fetch_array($subprod_info_result_sql);
                                $subprod_maincatpath = ($subprod_info["products_id"]) ? "<span class='categoryPath'>[" . tep_output_generated_category_path_sq($subprod_info['categories_id']) . "]</span>" : "**--This product is no longer existing in db--**";
                                if ($subprod_info["products_id"]) {
                                    $subprod_name = '&nbsp;&nbsp;' . '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($subprod_info['categories_id']) . '&pID=' . $subprod_info["products_id"] . '&selected_box=catalog&' . $HTTP_GET_VARS['SID']) . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $subprod_info["products_id"] . '">' . $order->products[$i]["static"][$static_loop]["name"] . '</a>';
                                } else {
                                    $subprod_name = '&nbsp;&nbsp;' . $order->products[$i]["static"][$static_loop]["name"];
                                }

                                if ($order->info['orders_status'] == PARTIAL_DELIVERY_STATUS) {
                                    $this_char_online_status = edit_order::get_order_extra_info($order->products[$i]['order_products_id'], 'char_online_status');

                                    if ($this_char_online_status == '1') {
                                        $subprod_name .= '&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_CHAR_ONLINE, 10, 10);
                                    } else if ($this_char_online_status == '0') {
                                        $subprod_name .= '&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_CHAR_OFFLINE, 10, 10);
                                    } else if ($this_char_online_status == '-1') {
                                        $subprod_name .= '&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_yellow.gif', IMAGE_ICON_CHAR_UNKNOWN, 10, 10);
                                    }
                                }

                                $subprod_name .= "<br>&nbsp;&nbsp;" . $subprod_maincatpath;
                                $subprod_loc = ($subprod_info["products_location"] ? $subprod_info["products_location"] : TEXT_OPTION_NOT_APPLICABLE);
                                $subprod_stock = (tep_check_product_skip_inventory($order->products[$i]["static"][$static_loop]['id']) ? TEXT_OPTION_NOT_APPLICABLE : ($subprod_info["products_actual_quantity"] < 0 ? "<span style='color:red;'>" . $subprod_info["products_actual_quantity"] . "</span>" : $subprod_info["products_actual_quantity"] ));
                                $subprod_total_qty = $order->products[$i]["static"][$static_loop]["qty"];
                                $subprod_qty = $subprod_total_qty;

                                $sub_table .= '	<tr>
													<td class="invoiceRecords" valign="top" colspan="2">' . $subprod_name . '</td>
													<td class="invoiceRecords" align="center" valign="top">' . $subprod_stock . '</td>
													<td class="invoiceRecords" align="center" valign="top">' . $subprod_qty . '</td>';
                                if ($order->info['orders_status'] == PARTIAL_DELIVERY_STATUS) {
                                    $prev_balance = ($order->products[$i]["static"][$static_loop]["delivered_qty"] == (int) $order->products[$i]["static"][$static_loop]["delivered_qty"]) ? (int) $order->products[$i]["static"][$static_loop]["delivered_qty"] : $order->products[$i]["static"][$static_loop]["delivered_qty"];
                                    $balance_qty = $subprod_total_qty - $prev_balance;

                                    if (tep_check_product_skip_inventory($order->products[$i]["static"][$static_loop]['id'])) {
                                        $balance_qty_text = '<span class="sufficientStock">' . $balance_qty . '</span>';
                                    } else {
                                        if ($subprod_info["products_actual_quantity"] >= $balance_qty) {
                                            $balance_qty_text = '<span class="sufficientStock">' . $balance_qty . '</span>';
                                        } else {
                                            $balance_qty_text = '<span class="lowStock">' . $balance_qty . '</span>';
                                        }
                                    }
                                    $sub_table .= '	<td class="invoiceRecords" align="center" valign="top">' . $prev_balance . '</td>
													<td class="invoiceRecords" align="center" valign="top">' . $balance_qty_text . '</td>';
                                }
                                $sub_table .= '<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>
													<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>';
                                if ($product_got_tax) {
                                    $sub_table .= '	<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>';
                                }
                                $sub_table .= '	</tr>';
                            }
                        } else {
                            $ordered_qty_text = $order->products[$i]['qty'];
                        }

                        echo '					<tr>
													<td class="invoiceRecords" valign="top" colspan="2">' . ($row_maincat['products_id'] ? '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($row_maincat['categories_id']) . '&pID=' . $order->products[$i]['id'] . '&selected_box=catalog&' . $HTTP_GET_VARS['SID']) . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $order->products[$i]['id'] . '">' . strip_tags($order->products[$i]['name']) . '</a>' : "<b>" . strip_tags($order->products[$i]['name']) . "</b>") .
                        ($order->products[$i]['pre_order'] ? '&nbsp;<span class="preOrderText">' . TEXT_INFO_PRE_ORDER . '</span>' : '' ) . '<br>';
                        if ($row_maincat['products_id'] == "") {
                            echo "**--This product is no longer existing in db--**";
                        } else {
                            echo '<span class="categoryPath">[' . $prod_maincatpath . ']</span>';
                        }
                        if (isset($order->products[$i]['attributes']) && (sizeof($order->products[$i]['attributes']) > 0)) {
                            for ($j = 0, $k = sizeof($order->products[$i]['attributes']); $j < $k; $j++) {
                                echo '<br><nobr><small>&nbsp;<i> - ' . $order->products[$i]['attributes'][$j]['option'] . ': ' . $order->products[$i]['attributes'][$j]['value'];
                                if ($order->products[$i]['attributes'][$j]['price'] != '0')
                                    echo ' (' . $order->products[$i]['attributes'][$j]['prefix'] . $currencies->format($order->products[$i]['attributes'][$j]['price'] * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . ')';
                                echo '</i></small></nobr>';
                            }
                        }

                        echo '						</td>
													<td class="invoiceRecords" align="center" valign="top"><span style="color:blue;">' . $prod_qty_store . '</span></td>
													<td class="invoiceRecords" align="center" valign="top">' . $ordered_qty_text . '</td>' . "\n";
                        if ($order->info['orders_status'] == PARTIAL_DELIVERY_STATUS) {
                            if ($row_maincat['products_bundle_dynamic'] == '' && $row_maincat['products_bundle'] == '') {
                                $prev_balance = ($order->products[$i]['delivered_qty'] == (int) $order->products[$i]['delivered_qty']) ? (int) $order->products[$i]['delivered_qty'] : $order->products[$i]['delivered_qty'];
                                $balance_qty = $order->products[$i]['qty'] - $prev_balance;

                                if ($row_maincat['products_skip_inventory']) {
                                    $balance_qty_text = '<span class="sufficientStock">' . $balance_qty . '</span>';
                                } else {
                                    if ($row_maincat["products_actual_quantity"] >= $balance_qty) {
                                        $balance_qty_text = '<span class="sufficientStock">' . $balance_qty . '</span>';
                                    } else {
                                        $balance_qty_text = '<span class="lowStock">' . $balance_qty . '</span>';
                                    }
                                }

                                echo '				<td class="invoiceRecords" align="center" valign="top">' . $prev_balance . '</td>
													<td class="invoiceRecords" align="center" valign="top">' . $balance_qty_text . '</td>';
                            } else {
                                echo '				<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>
													<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>';
                            }
                        }
                        echo '            		<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->format($order->products[$i]['final_price'], true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                        if ($product_got_tax) {
                            echo '					<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->format($order->products[$i]['final_price'] * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            if (strpos($order->products[$i]["name"], '(~1 Free Items - worth') !== false) {
                                echo '       		<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->format(tep_add_tax($order->products[$i]['final_price'], $order->products[$i]['tax']) * ($order->products[$i]['qty'] - 1), true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            } else {
                                echo '       		<td class="invoiceRecords" align="right" valign="top"><b>' . $currencies->format(tep_add_tax($order->products[$i]['final_price'], $order->products[$i]['tax']) * ($order->products[$i]['qty']), true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            }
                        } else {
                            if (strpos($order->products[$i]["name"], '(~1 Free Items - worth') !== false) {
                                echo '       		<td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->format($order->products[$i]['final_price'] * ($order->products[$i]['qty'] - 1), true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            } else {
                                echo '				<td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->format($order->products[$i]['final_price'] * $order->products[$i]['qty'], true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            }
                        }
                        echo '					</tr>';

                        if (tep_not_null($sub_table))
                            echo $sub_table;
                    }

                    // Order Compensation Sections
                    $order->get_compensate_products();

                    if (count($order->compensate_products)) {
                        echo '					<tr>
													<td colspan="' . $total_colspan . '" class="invoiceRecords"><div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px; margin-top: 10px;">' . SECTION_HEADING_COMPENSATION_DETAILS . '</div></td>
												</tr>';
                    }

                    for ($cps_cnt = 0; $cps_cnt < count($order->compensate_products); $cps_cnt++) {
                        $product_info_select_sql = "SELECT p.products_id, p.products_actual_quantity, p.products_quantity, p.custom_products_type_id, p.products_bundle_dynamic, p.products_bundle, p.products_skip_inventory, pc.categories_id
													FROM " . TABLE_PRODUCTS . " AS p
													LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
														ON p.products_id=pc.products_id
													WHERE p.products_id = '" . $order->compensate_products[$cps_cnt]['id'] . "'
														AND pc.products_is_link=0 ";
                        $product_info_result_sql = tep_db_query($product_info_select_sql);
                        $product_info_row = tep_db_fetch_array($product_info_result_sql);

                        $prod_maincatpath = tep_output_generated_category_path_sq($product_info_row['categories_id']);

                        $prod_qty_store = ($product_info_row['products_skip_inventory'] || $order->compensate_products[$cps_cnt]['id'] == '-1' ? TEXT_OPTION_NOT_APPLICABLE : ($product_info_row['products_actual_quantity'] < 0 ? "<span style='color:red;'>" . $product_info_row['products_actual_quantity'] . "</span>" : $product_info_row['products_actual_quantity'] ));

                        $ordered_qty_text = $order->compensate_products[$cps_cnt]['qty'];

                        $prod_name_link = ($product_info_row['products_id'] ? '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($product_info_row['categories_id']) . '&pID=' . $order->compensate_products[$cps_cnt]['id'] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="' . TEXT_PRODUCTS_ID . ' ' . $order->compensate_products[$cps_cnt]['id'] . '">' . strip_tags($order->compensate_products[$cps_cnt]['name']) . '</a>' : "<b>" . strip_tags($order->compensate_products[$cps_cnt]['name']) . "</b>");

                        $prod_name_link .= ( $order->compensate_products[$cps_cnt]['pre_order'] ? '&nbsp;<span class="preOrderText">' . TEXT_INFO_PRE_ORDER . '</span>' : '' );
                        if ($product_info_row['products_id'] == '') {
                            if ($order->compensate_products[$cps_cnt]['id'] != '-1')
                                $prod_name_link .= '<br>' . "**--This product is no longer existing in db--**";
                        } else {
                            $prod_name_link .= '<br>' . "<span class='categoryPath'>[" . strip_tags($prod_maincatpath) . "]</span>";
                        }


                        echo '	<tr>
			           				<td valign="top" class="invoiceRecords" colspan="2">' . $prod_name_link . '</td>
			           				<td class="invoiceRecords" align="center" valign="top"><span style="color:blue;">' . (tep_not_null($prod_qty_store) ? $prod_qty_store : '&nbsp') . '</span></td>
			           				<td class="invoiceRecords" align="center" valign="top">' . $ordered_qty_text . '</td>';

                        if ($order->info['orders_status'] == PARTIAL_DELIVERY_STATUS) {
                            if ($product_info_row['products_bundle_dynamic'] == '' && $product_info_row['products_bundle'] == '') {
                                $prev_balance = ($order->compensate_products[$cps_cnt]["delivered_qty"] == (int) $order->compensate_products[$cps_cnt]["delivered_qty"]) ? (int) $order->compensate_products[$cps_cnt]["delivered_qty"] : $order->compensate_products[$cps_cnt]["delivered_qty"];
                                $balance_qty = $order->compensate_products[$cps_cnt]['qty'] - $prev_balance;

                                if ($product_info_row['products_skip_inventory']) {
                                    $balance_qty_text = '<span class="sufficientStock">' . $balance_qty . '</span>';
                                } else {
                                    if ($product_info_row["products_actual_quantity"] >= $balance_qty) {
                                        $balance_qty_text = '<span class="sufficientStock">' . $balance_qty . '</span>';
                                    } else {
                                        $balance_qty_text = '<span class="lowStock">' . $balance_qty . '</span>';
                                    }
                                }

                                echo '	<td class="invoiceRecords" align="center" valign="top">' . $prev_balance . '</td>
										<td class="invoiceRecords" align="center" valign="top">' . $balance_qty_text . '</td>';
                            } else {
                                echo '	<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>
										<td class="invoiceRecords" align="center" valign="top">&nbsp;</td>';
                            }
                        }
                        echo '    	<td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->format($order->compensate_products[$cps_cnt]['final_price'], true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";

                        if ($product_got_tax) {
                            echo '  	<td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->format($order->compensate_products[$cps_cnt]['final_price'] * $order->compensate_products[$cps_cnt]['qty'], true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            if (strpos($order->compensate_products[$cps_cnt]["name"], '(~1 Free Items - worth') !== false) {
                                echo '  <td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->format(tep_add_tax($order->compensate_products[$cps_cnt]['final_price'], $order->compensate_products[$cps_cnt]['tax']) * ($order->compensate_products[$cps_cnt]['qty'] - 1), true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            } else {
                                echo '  <td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->format(tep_add_tax($order->compensate_products[$cps_cnt]['final_price'], $order->compensate_products[$cps_cnt]['tax']) * ($order->compensate_products[$cps_cnt]['qty']), true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            }
                        } else {
                            if (strpos($order->compensate_products[$cps_cnt]["name"], '(~1 Free Items - worth') !== false) {
                                echo ' 	<td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->format($order->compensate_products[$cps_cnt]['final_price'] * ($order->compensate_products[$cps_cnt]['qty'] - 1), true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            } else {
                                echo '  <td class="invoiceRecords" align="right" valign="top" nowrap><b>' . $currencies->format($order->compensate_products[$cps_cnt]['final_price'] * $order->compensate_products[$cps_cnt]['qty'], true, $order->info['currency'], $order->info['currency_value']) . '</b></td>' . "\n";
                            }
                        }
                        echo '		</tr>';
                    }

                    echo '			<tr valign="bottom">
			  							<td colspan="' . $total_colspan . '" class="subInvoiceBoxHeading"></td>
									</tr>';
                    if (tep_not_null($order_remark)) {
                        echo '		<tr>
										<td colspan="' . ($total_colspan - 1) . '" class="orderRemarkSelectedRow">
											<table cellpadding="3">
												<tr>
													<td class="dataTableContent">' .
                        nl2br($order_remark) . '<br>';
                        if (tep_not_null($remark_by_admin_email_address))
                            echo "(" . TEXT_REMARK_MODIFIED_BY . $remark_by_admin_email_address . ")";
                        echo '						</td>
												</tr>
											</table>
										</td>
										<td>&nbsp;</td>
									</tr>';
                    }
                    echo '			<tr valign="bottom">
			  						  	<td colspan="' . $total_colspan . '" class="subInvoiceBoxHeading"></td>
									</tr>
		    					</table>
			  				';
                    echo "]]></order_detail>";
                }
                echo "</order_info>";
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>Record not found!</result>";
            }

            break;
        case "get_payment_info":
            if (tep_not_null($HTTP_GET_VARS['o_str'])) {
                if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "orders.php")) {
                    include_once(DIR_WS_LANGUAGES . $language . '/' . "orders.php");
                }
                $order_ids_array = explode(',', $HTTP_GET_VARS['o_str']);
                echo "<payment_info>";

                $payment_methods_obj = new payment_methods('payment_gateways');
                $pg_orders_list_array = array();
                foreach ($payment_methods_obj->payment_gateways_array as $payment_methods_id => $payment_methods_data) {
                    $pg_orders_list_array[$payment_methods_id] = $payment_methods_data->get_orders_list_payment_info_file();
                }

                for ($i = 0; $i < count($order_ids_array); $i++) {
                    $order_select_sql = "	SELECT orders_id, payment_method, payment_methods_parent_id, paypal_ipn_id,
													pm_2CO_cc_owner_firstname, pm_2CO_cc_owner_lastname
											FROM " . TABLE_ORDERS . "
											WHERE orders_id = '" . $order_ids_array[$i] . "'";
                    $order_result_sql = tep_db_query($order_select_sql);
                    $order_row = tep_db_fetch_array($order_result_sql);
                    $order_obj = new objectInfo($order_row);

                    $order_obj->language = $language_dir_row["directory"];
                    echo "<payment_detail order_id='" . $order_obj->orders_id . "'><![CDATA[";
                    if (isset($pg_orders_list_array[$order_obj->payment_methods_parent_id])) {
                        include($pg_orders_list_array[$order_obj->payment_methods_parent_id]);
                    } else {
                        echo '	<table border="0" width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td class="subInvoiceBoxHeading">
											<div style="border-bottom: 1px solid #996600; border-top: 1px solid #996600; padding: 2px;">Payment Details</div>
										</td>
									</tr>';
                        if (tep_not_null($order_obj->pm_2CO_cc_owner_firstname) || tep_not_null($order_obj->pm_2CO_cc_owner_lastname)) {
                            echo '	<tr>
										<td class="main">
								      		<table border="0" cellspacing="0" cellpadding="2">
								      			<tr valign="top">
        											<td class="invoiceRecords">' . ENTRY_CREDIT_CARD_OWNER . '</td>
        											<td class="invoiceRecords" valign="top">:&nbsp;</td>
				            						<td class="invoiceRecords">' . $order_obj->pm_2CO_cc_owner_firstname . " " . $order_obj->pm_2CO_cc_owner_lastname . '</td>
				          						</tr>
				          					</table>
				          				</td>
				          			</tr>';
                        } else {
                            echo '	<tr>
										<td class="invoiceRecords">No further payment information is available.</td>
									</tr>';
                        }
                        echo '	</table>';
                    }
                    echo "]]></payment_detail>";
                }
                echo "</payment_info>";
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>Record not found!</result>";
            }

            break;
        case "perform_tagging":
            $order_status_id = (int) $HTTP_GET_VARS['status_id'];
            $setting_value = $HTTP_GET_VARS['setting'];
            $order_ids_array = explode(',', $HTTP_GET_VARS['o_str']);
            $list_mode = (int) $HTTP_GET_VARS['list_mode'];
            echo "<tag_info>";
            if ($subaction == 'nt') {
                if (tep_not_null($setting_value)) {
                    $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_name = '" . tep_db_input($setting_value) . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_STATS_ORDERS_TRACKING . "';";
                    $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                    if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                        $orders_tag_id = (int) $tag_verify_row["orders_tag_id"];
                    } else {
                        $insert_sql_data = array('orders_tag_name' => tep_db_prepare_input($setting_value),
                            'orders_tag_status_ids' => $order_status_id);
                        tep_db_perform(TABLE_ORDERS_TAG, $insert_sql_data);
                        $orders_tag_id = tep_db_insert_id();
                    }

                    // update all the selected orders with this tag
                    $assign_orders_tag_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_tag_ids = IF (orders_tag_ids='', '" . $orders_tag_id . "', CONCAT_WS(',', orders_tag_ids, '" . $orders_tag_id . "')) WHERE orders_id IN (" . implode(',', $order_ids_array) . ") AND NOT FIND_IN_SET('" . $orders_tag_id . "', orders_tag_ids)";
                    tep_db_query($assign_orders_tag_update_sql);

                    generateTagString($order_ids_array);
                }
            } else if ($subaction == 'at') {
                $tag_verify_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $setting_value . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_STATS_ORDERS_TRACKING . "' ;";
                $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                    // update all the selected orders with this tag
                    $temp_order_ids_array = array();

                    foreach ($order_ids_array as $oid) {
                        if ($tag_verify_row['orders_tag_name'] == 'Flag LL') {
                            $order_select_sql = "   SELECT customers_id
                                                    FROM " . TABLE_ORDERS . "
                                                    WHERE orders_id = '" . $oid . "'";
                            $order_result_sql = tep_db_query($order_select_sql);
                            if ($order_row = tep_db_fetch_array($order_result_sql)) {
                                $check_sql = "  SELECT customers_id 
                                                FROM " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . "
                                                WHERE customers_id = " . $order_row['customers_id'];
                                $check_query = tep_db_query($check_sql);
                                if (!tep_db_num_rows($check_query)) {
                                    if (tep_db_perform(TABLE_CUSTOMERS_VERIFICATION_DOCUMENT, array(
                                                'customers_id' => $order_row['customers_id'],
                                                'files_001_locked' => 0,
                                                'files_002_locked' => 0,
                                                'files_003_locked' => 0,
                                                'files_004_locked' => 0,
                                                'files_005_locked' => 0
                                            ))) {
                                        $temp_order_ids_array[] = $oid;
                                    }
                                } else {
                                    $temp_order_ids_array[] = $oid;
                                }
                            }
                        } else {
                            $temp_order_ids_array[] = $oid;
                        }

                        if ($temp_order_ids_array) {
                            $assign_orders_tag_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_tag_ids = IF (orders_tag_ids='', '" . (int) $setting_value . "', CONCAT_WS(',', orders_tag_ids, '" . (int) $setting_value . "')) WHERE orders_id IN (" . implode(',', $temp_order_ids_array) . ") AND NOT FIND_IN_SET('" . (int) $setting_value . "', orders_tag_ids)";
                            tep_db_query($assign_orders_tag_update_sql);
                        }
                    }

                    generateTagString($order_ids_array);
                }
            } else if ($subaction == 'rt') {
                $tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int) $setting_value . "' AND FIND_IN_SET('" . $order_status_id . "', orders_tag_status_ids) AND filename='" . FILENAME_STATS_ORDERS_TRACKING . "' ;";
                $tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
                if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
                    // update all the selected orders by removing this tag from them
                    $unassign_orders_tag_select_sql = "SELECT orders_id, orders_tag_ids FROM " . TABLE_ORDERS . " WHERE orders_id IN (" . implode(',', $order_ids_array) . ") AND FIND_IN_SET('" . (int) $setting_value . "', orders_tag_ids)";
                    $unassign_orders_tag_result_sql = tep_db_query($unassign_orders_tag_select_sql);
                    while ($unassign_orders_tag_row = tep_db_fetch_array($unassign_orders_tag_result_sql)) {
                        $TagRemovePattern = "/(,)?" . (int) $setting_value . "(,)?/is";
                        $new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_orders_tag_row["orders_tag_ids"]);
                        if (substr($new_tag_string, 0, 1) == ',')
                            $new_tag_string = substr($new_tag_string, 1);
                        if (substr($new_tag_string, -1) == ',')
                            $new_tag_string = substr($new_tag_string, 0, -1);

                        tep_db_query("UPDATE " . TABLE_ORDERS . " SET orders_tag_ids='" . $new_tag_string . "' WHERE orders_id='" . $unassign_orders_tag_row["orders_id"] . "'");
                    }

                    generateTagString($order_ids_array);
                }
            } else if ($subaction == 'rd' || $subaction == 'ur') {
                if (tep_not_null($setting_value) && count($order_ids_array)) {
                    $orders_read_mode_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_read_mode = '" . $setting_value . "' WHERE orders_id IN (" . implode(',', $order_ids_array) . "); ";
                    tep_db_query($orders_read_mode_update_sql);
                    generateReadModeString($order_ids_array);
                }
            }
            generateTagSelectionOptions($order_status_id, $HTTP_GET_VARS['o_str'], ($list_mode == "2" ? true : false));
            echo "</tag_info>";
            break;
        case "retrieve_status_tags":
            generateTagSelectionOptions($HTTP_GET_VARS['status'], '', '', true);
            break;
        case "refresh_tag_selection":
            generateTagSelectionOptions($HTTP_GET_VARS['status'], $HTTP_GET_VARS['o_str']);
            break;
        case "update_transID":
            $verify_order_select_sql = "SELECT orders_id FROM " . TABLE_PAYMENT_EXTRA_INFO . " WHERE orders_id='" . $order_id . "'";
            $verify_order_result_sql = tep_db_query($verify_order_select_sql);
            if (tep_db_num_rows($verify_order_result_sql)) {
                $trans_id_update_sql = "UPDATE " . TABLE_PAYMENT_EXTRA_INFO . " SET transaction_id ='" . tep_db_input($HTTP_GET_VARS['transID']) . "*' WHERE orders_id='" . $order_id . "'";
                tep_db_query($trans_id_update_sql);

                tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, set_as_order_remarks, changed_by) VALUES ('" . $order_id . "', '', now(), '0', 'Manually update WorldPay Transaction ID: " . tep_db_input($HTTP_GET_VARS['transID']) . "', 1, '0', '" . $admin_email . "')");

                $trans_id_select_sql = "SELECT transaction_id FROM " . TABLE_PAYMENT_EXTRA_INFO . " WHERE orders_id='" . $order_id . "'";
                $trans_id_result_sql = tep_db_query($trans_id_select_sql);
                $trans_id_row = tep_db_fetch_array($trans_id_result_sql);

                echo "<res_code>1</res_code>";
                echo "<result><![CDATA[" . $trans_id_row["transaction_id"] . "]]></result>";
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>Order not found!</result>";
            }
            break;
        case "retrieve_levelers":
            $orders_product_id = isset($HTTP_GET_VARS['op_id']) ? $HTTP_GET_VARS['op_id'] : '';

            $custom_product_select_sql = "	SELECT orders_custom_products_value
											FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . "
											WHERE orders_products_id='" . tep_db_input($orders_product_id) . "'
												AND orders_custom_products_key='power_leveling_info'";
            $custom_product_result_sql = tep_db_query($custom_product_select_sql);

            if ($custom_product_row = tep_db_fetch_array($custom_product_result_sql)) {
                $assigned_leveler_array = array();
                preg_match_all('/(?:Supplier Assigned)(?:[^:]*?)(?::)(?:\s*)([^\n]*)/i', $custom_product_row['orders_custom_products_value'], $assigned_leveler_array);

                echo "<res_code>1</res_code>";

                echo "<leveler_info>";
                if (isset($assigned_leveler_array[1]) && count($assigned_leveler_array[1])) {
                    echo "<selection>";
                    $sup_select_sql = "	SELECT supplier_id, supplier_code, supplier_firstname, supplier_lastname
										FROM " . TABLE_SUPPLIER . "
										WHERE supplier_code IN ('" . implode("', '", $assigned_leveler_array[1]) . "')
										ORDER BY supplier_firstname";
                    $sup_result_sql = tep_db_query($sup_select_sql);

                    while ($sup_row = tep_db_fetch_array($sup_result_sql)) {
                        echo "<option index='" . $sup_row['supplier_id'] . "'><![CDATA[" . $sup_row['supplier_firstname'] . '  ' . $sup_row['supplier_lastname'] . (tep_not_null($sup_row['supplier_code']) ? ' [' . $sup_row['supplier_code'] . ']' : '') . "]]></option>";
                    }
                    echo "</selection>";
                }
                echo "</leveler_info>";
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>This is not PWL Product!</result>";
            }

            break;
        case "retrieve_price":
            $product_id = isset($HTTP_GET_VARS['p_id']) ? $HTTP_GET_VARS['p_id'] : '';
            $selected_product_currency = isset($HTTP_GET_VARS['p_cur']) ? $HTTP_GET_VARS['p_cur'] : '';

            if (tep_not_null($product_id)) {
                $currencies = new currencies();
                $product_price_array = $currencies->get_product_prices_info($product_id);

                if (!tep_not_null($selected_product_currency)) {
                    $selected_product_currency = $product_price_array['base_cur'];
                }

                if ($product_price_array['base_cur'] == $selected_product_currency) {
                    $products_price = $product_price_array['price'];
                } else if (isset($product_price_array['defined_price'][$selected_product_currency])) {
                    $products_price = $product_price_array['defined_price'][$selected_product_currency];
                } else {
                    $products_price = $product_price_array['price'] * $currencies->currencies[$selected_product_currency]['value'] / $currencies->currencies[$product_price_array['base_cur']]['value'];
                }

                $product_type_select_sql = "	SELECT custom_products_type_id
												FROM " . TABLE_PRODUCTS . "
												WHERE products_id = '" . tep_db_input($product_id) . "'";
                $product_type_result_sql = tep_db_query($product_type_select_sql);
                $product_type_row = tep_db_fetch_array($product_type_result_sql);

                echo "<result currency='" . $selected_product_currency . "'>" . ($product_type_row['custom_products_type_id'] == '1' ? 'N/A' : (double) $products_price) . "</result>";
            }

            break;
        case "get_product_type":
            $product_id = isset($HTTP_GET_VARS['p_id']) ? $HTTP_GET_VARS['p_id'] : '';

            $product_type_select_sql = "SELECT custom_products_type_id
										FROM " . TABLE_PRODUCTS . "
										WHERE products_id = '" . tep_db_input($product_id) . "'";
            $product_type_result_sql = tep_db_query($product_type_select_sql);

            if ($product_type_row = tep_db_fetch_array($product_type_result_sql)) {
                echo "<result>" . (double) $product_type_row['custom_products_type_id'] . "</result>";
            } else {
                echo "<result>-1</result>";
            }

            break;
        case "update_follow_up":
            $order_select_sql = "SELECT orders_id, DATE_FORMAT(orders_follow_up_datetime, '%Y-%m-%d %H:%i') AS current_follow_date FROM " . TABLE_ORDERS . " WHERE orders_id='" . $order_id . "'";
            $order_result_sql = tep_db_query($order_select_sql);
            if ($order_result_row = tep_db_fetch_array($order_result_sql)) {
                if (!tep_not_null($HTTP_GET_VARS['follow_date']) || tep_day_diff(date('Y-m-d H:i:s'), $HTTP_GET_VARS['follow_date']) !== FALSE) {
                    if ($order_result_row['current_follow_date'] != $HTTP_GET_VARS['follow_date']) {
                        $follow_date_update_sql = "	UPDATE " . TABLE_ORDERS . "
													SET orders_follow_up_datetime = " . (tep_not_null($HTTP_GET_VARS['follow_date']) ? "'" . tep_db_input($HTTP_GET_VARS['follow_date']) . "'" : 'NULL') . "
													WHERE orders_id='" . $order_id . "'";
                        tep_db_query($follow_date_update_sql);

                        $follow_date_select_sql = "SELECT orders_follow_up_datetime FROM " . TABLE_ORDERS . " WHERE orders_id='" . $order_id . "'";
                        $follow_date_result_sql = tep_db_query($follow_date_select_sql);
                        $follow_date_row = tep_db_fetch_array($follow_date_result_sql);

                        $order_comment_str = tep_not_null($HTTP_GET_VARS['follow_date']) ? 'Set order follow-up date to ' . tep_db_input($follow_date_row["orders_follow_up_datetime"]) : 'Clear the follow-up date';
                        tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, set_as_order_remarks, changed_by) VALUES ('" . $order_id . "', '', now(), '0', '" . $order_comment_str . "', 1, '0', '" . $this_admin_email . "')");

                        echo "<res_code>1</res_code>";
                        echo "<result><![CDATA[Follow-up date successfully updated to " . $follow_date_row["orders_follow_up_datetime"] . "]]></result>";
                    } else {
                        echo "<res_code>0</res_code>";
                        echo "<result>No date changes!</result>";
                    }
                } else {
                    echo "<res_code>0</res_code>";
                    echo "<result>Invalid date!</result>";
                }
            } else {
                echo "<res_code>0</res_code>";
                echo "<result>Order not found!</result>";
            }
            break;

        case "update_aft_whitelisted_date":
            $whitelist_customer_account_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_AFT_ACCOUNT_WHITELIST');

            if ($whitelist_customer_account_permission) {
                $customers_id = isset($_REQUEST['cid']) ? $_REQUEST['cid'] : '';
                $aft_whitelist_date = isset($_REQUEST['aft_whitelisted_date']) ? $_REQUEST['aft_whitelisted_date'] : '';

                if (!date('Y-m-d H:i:s', strtotime($aft_whitelist_date)) == $aft_whitelist_date) {
                    echo "<res_code>0</res_code>";
                    echo "<result>Sorry,this is not a valid date time !</result>";
                } else {
                    $aft_whitelist_select_sql = "SELECT customers_setting_value FROM " . TABLE_CUSTOMERS_SETTING . " WHERE customers_id = '" . tep_db_input($customers_id) . "' 
                                            AND customers_setting_key = 'aft_genesis_whitelisted'";

                    $aft_whitelist_result_sql = tep_db_query($aft_whitelist_select_sql);
                    if (tep_db_num_rows($aft_whitelist_result_sql) == 0) {
                        $aft_whitelist_date = date('Y-m-d H:i:s', strtotime($aft_whitelist_date));
                        $aft_sql_data = array('customers_id' => (int) $customers_id,
                            'customers_setting_key' => tep_db_prepare_input('aft_genesis_whitelisted'),
                            'customers_setting_value' => tep_db_prepare_input($aft_whitelist_date),
                            'created_datetime' => 'now()'
                        );
                        tep_db_perform(TABLE_CUSTOMERS_SETTING, $aft_sql_data);
                        $log_object = new log_files($login_id);
                        $log_object->insert_customer_history_log($customers_id, 'Added AFT whitelist with expiry date : ' . $aft_whitelist_date);

                        echo "<res_code>1</res_code>";
                        echo "<res_code_value>$aft_whitelist_date</res_code_value>";
                        echo "<result>This customer has whitelisted successfully !</result>";
                    } else {
                        echo "<res_code>0</res_code>";
                        echo "<result>Sorry,this customer has already whitelisted !</result>";
                    }
                }
            }

            break;

        case "get_supplier_order_info":
            if (tep_not_null($HTTP_GET_VARS['o_str'])) {
                $server_status_array = array('urgent' => array('name' => 'icon_status_urgent'),
                    'important' => array('name' => 'icon_status_important'),
                    'normal' => array('name' => 'icon_status_normal')
                );

                if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "suppliers_orders.php")) {
                    include_once(DIR_WS_LANGUAGES . $language . '/' . "suppliers_orders.php");
                }

                include_once(DIR_WS_CLASSES . 'supplier_order.php');
                include_once(DIR_WS_FUNCTIONS . 'supplier.php');

                $currencies = new currencies();
                $currencies->set_decimal_places(4);

                $files_actions_select_sql = "	SELECT afa.admin_files_actions_id
												FROM " . TABLE_ADMIN_FILES_ACTIONS . " AS afa
												INNER JOIN " . TABLE_ADMIN_FILES . " AS af
													ON afa.admin_files_id=af.admin_files_id
												INNER JOIN " . TABLE_ADMIN . " AS adm
													ON adm.admin_id = '" . $admin_id . "'
												WHERE af.admin_files_name='" . FILENAME_SUPPLIERS_ORDERS . "'
													AND af.admin_files_is_boxes=0
													AND afa.admin_files_actions_key='SUPPLIER_ORDER_PAYMENT_INFO'
													AND FIND_IN_SET(adm.admin_groups_id, afa.admin_groups_id)";
                $files_actions_result_sql = tep_db_query($files_actions_select_sql);
                $view_payment_info_permission = tep_db_num_rows($files_actions_result_sql) ? true : false;

                $order_ids_array = explode(',', $HTTP_GET_VARS['o_str']);
                echo "<order_info>";

                for ($order_cnt = 0; $order_cnt < count($order_ids_array); $order_cnt++) {
                    $show_purchase_status = false;

                    $supplier_order = new supplier_order($order_ids_array[$order_cnt]);

                    for ($i = 0; $i < count($supplier_order->products); $i++) {
                        if (is_array($supplier_order->products[$i]['confirm_list']) && count($supplier_order->products[$i]['confirm_list']) > 0) {
                            $list_ref = 'confirm_list';
                        } else {
                            $list_ref = 'first_list';
                        }
                        if (tep_not_null($supplier_order->products[$i][$list_ref]['products_purchase_status'])) {
                            $show_purchase_status = true;
                            break;
                        }
                    }

                    $total_colspan = 7;
                    if ($show_purchase_status) {
                        $total_colspan++;
                    }

                    if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
                        $total_colspan += 6;
                    } else {
                        $total_colspan++; // For suggested qty
                        if ($view_payment_info_permission)
                            $total_colspan += 3;
                    }

                    echo "<order_detail order_id='" . $order_ids_array[$order_cnt] . "'><![CDATA[";
                    echo '		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			  						<tr>
				          				<td colspan="' . $total_colspan . '"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 2px; padding-top: 3px;"></td>
				          			</tr>
				          			<tr>
										<td width="35%" class="subInvoiceBoxHeading">' . TABLE_HEADING_PRODUCT_NAME . '</td>
										<td width="7%" align="center" class="subInvoiceBoxHeading" >' . TABLE_HEADING_PRODUCT_ACTUAL_QTY . '</td>';

                    if ($show_purchase_status) {
                        echo '  		<td width="2%" align="center" class="subInvoiceBoxHeading">' . TABLE_HEADING_PRODUCT_DEMAND_STATUS . '</td>';
                    }

                    if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS || $supplier_order->info['orders_status'] == 5) { // For draft show the real time
                        echo '			<td width="8%" class="subInvoiceBoxHeading" nowrap>' . TABLE_HEADING_PRODUCT_ADMIN_COMMENT . '</td>';
                    }

                    echo '				<td width="10%" class="subInvoiceBoxHeading">' . TABLE_HEADING_PRODUCT_SUPPLIER_COMMENT . '</td>
										<td width="5%" align="center" class="subInvoiceBoxHeading">' . TABLE_HEADING_MIN_QTY . '</td>';

                    if ($supplier_order->info['orders_status'] != PARTIAL_RECEIVE_STATUS) {
                        if ($view_payment_info_permission) {
                            echo '		<td width="6%" align="center" class="subInvoiceBoxHeading">' . TABLE_HEADING_MAX_QTY . '</td>
										<td width="6%" align="center" class="subInvoiceBoxHeading">' . TABLE_HEADING_UNIT_PRICE . '</td>';
                        }

                        echo '			<td width="5%" align="center" class="subInvoiceBoxHeading">' . TABLE_HEADING_SUGGESTED_SELLING_QUANTITY . '</td>';
                    }

                    echo '				<td width="5%" align="center" class="subInvoiceBoxHeading">' . TABLE_HEADING_SELLING_QUANTITY . '</td>
								  		<td width="5%" align="center" class="subInvoiceBoxHeading">' . TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED . '</td>';

                    if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
                        echo '			<td width="5%" align="center" class="subInvoiceBoxHeading">' . TABLE_HEADING_PRODUCT_BALANCE . '</td>';
                    }

                    if ($supplier_order->info['orders_status'] != PARTIAL_RECEIVE_STATUS && $view_payment_info_permission) {
                        echo '		  	<td width="8%" align="right" class="subInvoiceBoxHeading">' . TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT . '</td>';
                    }

                    echo '			</tr>
									<tr>
				          				<td colspan="' . $total_colspan . '"><div style="border-bottom: 1px solid #996600;"></td>
			          				</tr>
			          				<tr><td></td></tr>';

                    for ($i = 0; $i < count($supplier_order->products); $i++) {
                        $this_pid = $supplier_order->products[$i]['id'];
                        $this_product_need_restock = false;
                        $unique_row_reference = $this_pid;

                        $product_info_select_sql = "SELECT p.products_id, p.products_actual_quantity, p.products_quantity, p2c.categories_id, sp.supplier_pricing_show_comment
													FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
													INNER JOIN " . TABLE_PRODUCTS . " AS p
														ON p2c.products_id=p.products_id
													INNER JOIN " . TABLE_SUPPLIER_PRICING . " AS sp
														ON (sp.products_purchases_lists_id = '" . $supplier_order->info['products_purchases_lists_id'] . "' AND p.products_id=sp.products_id AND sp.supplier_groups_id='" . $supplier_order->supplier['supplier_groups_id'] . "')
													WHERE p.products_id = '" . $this_pid . "' AND p2c.products_is_link=0 ";
                        $product_info_result_sql = tep_db_query($product_info_select_sql);
                        $product_info_row = tep_db_fetch_array($product_info_result_sql);

                        if (tep_not_null($product_info_row['products_id'])) {
                            $prod_loc_result_sql = tep_db_query("SELECT products_name, products_location FROM " . TABLE_PRODUCTS_DESCRIPTION . " WHERE products_id =" . $product_info_row['products_id'] . " AND language_id = '" . (int) $languages_id . "'");
                            if ($prod_loc_row = tep_db_fetch_array($prod_loc_result_sql)) {
                                $prod_loc = $prod_loc_row['products_location'];
                            }
                        }

                        echo '		<tr>
										<td valign="top" class="invoiceRecords">';

                        if (tep_not_null($product_info_row['products_id'])) {
                            $prod_maincatpath = tep_output_generated_category_path_sq($product_info_row['categories_id']);
                            if (tep_not_null($prod_maincatpath)) {
                                echo '<span class="categoryPath">' . strip_tags($prod_maincatpath) . '>' . strip_tags($prod_loc_row['products_name']) . '</span>';
                            }
                        } else {
                            echo "**--This product is no longer existing in db--**";
                        }
                        echo '			</td>
										<td align="center" valign="top" class="ordersRecords">' . (tep_not_null($product_info_row['products_actual_quantity']) ? (int) $product_info_row['products_actual_quantity'] : '&nbsp') . '</td>';

                        if (is_array($supplier_order->products[$i]['confirm_list']) && count($supplier_order->products[$i]['confirm_list']) > 0) {
                            $list_ref = 'confirm_list';
                        } else {
                            $list_ref = 'first_list';
                        }

                        $suggested_quantity = $supplier_order->products[$i]['first_list']['products_quantity'];
                        $selling_quantity = $supplier_order->products[$i]['confirm_list']['products_quantity'];
                        $received_quantity = $supplier_order->products[$i]['confirm_list']['products_received_quantity'];
                        $balance = $selling_quantity - $received_quantity;


                        if ($show_purchase_status) {
                            echo '			<td align="center" valign="top" class="ordersRecords">';
                            $server_status_index = trim($supplier_order->products[$i][$list_ref]['products_purchase_status']);
                            if (isset($server_status_array[$server_status_index])) {
                                echo tep_image(DIR_WS_IMAGES . $server_status_array[$server_status_index]['name'] . '.gif', ucfirst($supplier_order->products[$i][$list_ref]['products_purchase_status']));
                            }
                            echo '			</td>';
                        }

                        if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS || $supplier_order->info['orders_status'] == 5) {
                            echo '			<td valign="top" class="ordersRecords" nowrap>';

                            $rstk_char_to_show_str = '';

                            if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
                                if ($list_ref == 'confirm_list') { // Supplier has submit the confirmation qty for this server, show the rstk char he saw when during submission otherwise show real time rstk char
                                    $rstk_char_to_show_str = $supplier_order->products[$i][$list_ref]['products_restock_comment'];
                                } else {
                                    $rstk_char_to_show_str = tep_not_null($supplier_order->products[$i][$list_ref]['real_time_rstk_char']) ? '<span class="redIndicator">' . $supplier_order->products[$i]['first_list']['real_time_rstk_char'] . '</span>' : WARNING_TEXT_NO_RESTK_CHAR_ASSIGN; // Here $list_ref since carries the value 'confirm_list' (See above). Show in red colour to indicate this is the real time
                                }
                            } else if ($supplier_order->info['orders_status'] == 5) {
                                $rstk_char_to_show_str = tep_not_null($supplier_order->products[$i][$list_ref]['real_time_rstk_char']) ? $supplier_order->products[$i][$list_ref]['real_time_rstk_char'] : WARNING_TEXT_NO_RESTK_CHAR_ASSIGN;
                            }

                            echo $rstk_char_to_show_str;

                            if ($product_info_row['supplier_pricing_show_comment']) { //show restock character with red and green icon
                                echo '&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_green.gif');
                            } else {
                                echo '&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif');
                            }

                            echo '</td>';
                        }

                        echo '				<td valign="top" class="ordersRecords">' . $supplier_order->products[$i][$list_ref]['supplier_order_lists_products_comment'] . '</td>
											<td align="center" valign="top" class="ordersRecords">' . $supplier_order->products[$i][$list_ref]['min_quantity'] . '</td>';

                        if ($supplier_order->info['orders_status'] != PARTIAL_RECEIVE_STATUS) {
                            if ($view_payment_info_permission) {
                                echo '		<td align="center" valign="top" class="ordersRecords">' . $supplier_order->products[$i][$list_ref]['first_max_quantity'] . '</td>
											<td align="right" valign="top" class="ordersRecords">' . $supplier_order->products[$i][$list_ref]['first_max_unit_price'] . '</td>';
                            }

                            echo '			<td align="center" valign="top" class="ordersRecords">' . $suggested_quantity . '</td>';
                        }

                        echo '				<td align="center" valign="top" class="ordersRecords">' . $selling_quantity . '</td>
											<td align="center" valign="top" class="ordersRecords">' . $received_quantity . '</td>';

                        if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
                            echo '			<td align="center" valign="top" class="ordersRecords">' . $balance . '</td>';
                        } else if ($view_payment_info_permission) {
                            echo '			<td align="right" valign="top" class="ordersRecords" nowrap>' . $currencies->format($supplier_order->products[$i]['payable_amount'], true, $supplier_order->info['currency'], $supplier_order->info['currency_value']) . '</td>';
                        }

                        echo '			</tr>';
                    }
                    echo '</table>';

                    echo "]]></order_detail>";
                }
                echo "</order_info>";
            }

            break;
        case "get_supplier_cp_payment_products":
            if (tep_not_null($HTTP_GET_VARS['pay_id'])) {
                if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "custom_product_payment.php")) {
                    include_once(DIR_WS_LANGUAGES . $language . '/' . "custom_product_payment.php");
                }

                include_once(DIR_WS_CLASSES . 'custom_product.php');

                $currencies = new currencies();
                $currencies->set_decimal_places(3);

                $payment_id = tep_db_prepare_input($HTTP_GET_VARS['pay_id']);

                $payment_select_sql = "	SELECT supplier_cp_payments_status, supplier_cp_payments_date, currency, currency_value
										FROM " . TABLE_SUPPLIER_CP_PAYMENTS . "
										WHERE supplier_cp_payments_id='" . tep_db_input($payment_id) . "' ";
                $payment_result_sql = tep_db_query($payment_select_sql);
                if ($payment_row = tep_db_fetch_array($payment_result_sql)) {
                    echo '<payment_records>';
                    echo '<reverse_detail><![CDATA[';
                    if (isset($HTTP_GET_VARS['tid']) && $HTTP_GET_VARS['tid'] > 0) {
                        $payment_history_select_sql = "	SELECT supplier_cp_payments_status
														FROM " . TABLE_SUPPLIER_CP_PAYMENTS_HISTORY . "
														WHERE supplier_cp_payments_history_id='" . tep_db_input($HTTP_GET_VARS['tid']) . "' ";
                        $payment_history_result_sql = tep_db_query($payment_history_select_sql);
                        $payment_history_row = tep_db_fetch_array($payment_history_result_sql);
                        if ($payment_history_row['supplier_cp_payments_status'] == '3') {
                            echo '<span class="redIndicator">Payment #' . $payment_id . ' made on ' . $payment_row['supplier_cp_payments_date'] . '</span>';
                        }
                    } else if ($payment_row['supplier_cp_payments_status'] == '3') {
                        echo '<span class="redIndicator">Payment #' . $payment_id . ' made on ' . $payment_row['supplier_cp_payments_date'] . '</span>';
                    }
                    echo ']]></reverse_detail>';

                    echo '<order_detail><![CDATA[';

                    echo '	<table width="100%" border="0" cellspacing="1" cellpadding="0">
		  						<tr>
			          				<td colspan="4"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 2px; padding-top: 3px;"></td>
			          			</tr>
			          			<tr>
			        				<td width="25%" align="left" class="subInvoiceBoxHeading">' . TABLE_HEADING_ORDER_NO . '</td>
			        				<td width="25%" align="left" class="subInvoiceBoxHeading">' . TABLE_HEADING_SUBMITTED_DATE . '</td>
									<!--td width="25%" align="right" class="subInvoiceBoxHeading">' . TABLE_HEADING_ORDER_AMOUNT . '</td-->
									<td width="25%" align="right" class="subInvoiceBoxHeading">' . TABLE_HEADING_PAID_AMOUNT . '</td>
								</tr>
								<tr>
			          				<td colspan="4"><div style="border-bottom: 1px solid #996600; "></td>
		          				</tr>
		          				<tr><td colspan="4"></td></tr>';

                    $payment_orders_select_sql = "	SELECT scpp.orders_products_id, scpp.supplier_cp_payments_products_paid_amount, scpp.supplier_cp_payments_type, sta.supplier_tasks_start_time, op.final_price, op.orders_id
		        									FROM " . TABLE_ORDERS_PRODUCTS . " AS op
		        									INNER JOIN " . TABLE_SUPPLIER_CP_PAYMENTS_PRODUCTS . " AS scpp
		        										ON (scpp.orders_products_id = op.orders_products_id)
		        									INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta
		        										ON scpp.orders_products_id=sta.orders_products_id
		        									WHERE scpp.supplier_cp_payments_id = '" . tep_db_input($payment_id) . "'
		        									ORDER BY scpp.orders_products_id";
                    $payment_orders_result_sql = tep_db_query($payment_orders_select_sql);
                    while ($payment_orders_row = tep_db_fetch_array($payment_orders_result_sql)) {
                        $order_payable_amount = custom_product::get_order_total_payable_amount($payment_orders_row['orders_products_id']);

                        echo '	<tr>
				  					<td align="left" valign="top" class="invoiceRecords">
				  						<a href="' . tep_href_link(FILENAME_PROGRESS_REPORT, 'orders_product_id=' . $payment_orders_row['orders_products_id'] . '&action=report&' . $HTTP_GET_VARS['SID'], 'NONSSL') . '" target="_blank">' . $payment_orders_row['orders_id'] . '</a>';
                        if ($payment_orders_row['supplier_payments_type'] == '1') {
                            echo '&nbsp;' . TEXT_PARTIAL_PAID_ORDER;
                        }
                        echo '		</td>
				  					<td align="left" valign="top" class="invoiceRecords" nowrap>' . $payment_orders_row['supplier_tasks_start_time'] . '</td>
				  					<!--td align="right" valign="top" class="invoiceRecords">' . $currencies->format($payment_orders_row['final_price']) . '</td-->
				  					<td align="right" valign="top" class="invoiceRecords">' . $currencies->format($payment_orders_row['supplier_cp_payments_products_paid_amount']) . '</td>
				  				</tr>';
                    }

                    echo '	</table>';

                    echo ']]></order_detail>';
                    echo '</payment_records>';
                }
            }
            break;
        case "get_supplier_payment_orders":
            if (tep_not_null($HTTP_GET_VARS['pay_id'])) {
                if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "suppliers_payment.php")) {
                    include_once(DIR_WS_LANGUAGES . $language . '/' . "suppliers_payment.php");
                }

                include_once(DIR_WS_CLASSES . 'supplier_order.php');

                $currencies = new currencies();
                $currencies->set_decimal_places(4);

                $payment_id = tep_db_prepare_input($HTTP_GET_VARS['pay_id']);

                $payment_select_sql = "	SELECT supplier_payments_status, supplier_payments_date, currency, currency_value
										FROM " . TABLE_SUPPLIER_PAYMENTS . "
										WHERE supplier_payments_id='" . tep_db_input($payment_id) . "' ";
                $payment_result_sql = tep_db_query($payment_select_sql);
                if ($payment_row = tep_db_fetch_array($payment_result_sql)) {
                    echo '<payment_records>';
                    echo '<reverse_detail><![CDATA[';
                    if (isset($HTTP_GET_VARS['tid']) && $HTTP_GET_VARS['tid'] > 0) {
                        $payment_history_select_sql = "	SELECT supplier_payments_status
														FROM " . TABLE_SUPPLIER_PAYMENTS_HISTORY . "
														WHERE supplier_payments_history_id='" . tep_db_input($HTTP_GET_VARS['tid']) . "' ";
                        $payment_history_result_sql = tep_db_query($payment_history_select_sql);
                        $payment_history_row = tep_db_fetch_array($payment_history_result_sql);
                        if ($payment_history_row['supplier_payments_status'] == '3') {
                            echo '<span class="redIndicator">Payment #' . $payment_id . ' made on ' . $payment_row['supplier_payments_date'] . '</span>';
                        }
                    } else if ($payment_row['supplier_payments_status'] == '3') {
                        echo '<span class="redIndicator">Payment #' . $payment_id . ' made on ' . $payment_row['supplier_payments_date'] . '</span>';
                    }
                    echo ']]></reverse_detail>';

                    echo '<order_detail><![CDATA[';

                    echo '	<table width="100%" border="0" cellspacing="0" cellpadding="2">
		  						<tr>
			          				<td colspan="5"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 2px; padding-top: 3px;"></td>
			          			</tr>
			          			<tr>
			        				<td width="10%" align="left" class="subInvoiceBoxHeading" nowrap>' . TABLE_HEADING_ORDER_NO . '</td>
			        				<td width="15%" align="left" class="subInvoiceBoxHeading" nowrap>' . TABLE_HEADING_LIST_NAME . '</td>
			        				<td width="25%" align="left" class="subInvoiceBoxHeading">' . TABLE_HEADING_ORDER_DATE . '</td>
									<td width="25%" align="right" class="subInvoiceBoxHeading">' . TABLE_HEADING_ORDER_AMOUNT . '</td>
									<td width="25%" align="right" class="subInvoiceBoxHeading">' . TABLE_HEADING_PAID_AMOUNT . '</td>
								</tr>
								<tr>
			          				<td colspan="5"><div style="border-bottom: 1px solid #996600; "></td>
		          				</tr>
		          				<tr><td colspan="5"></td></tr>';

                    $payment_orders_select_sql = "	SELECT spo.supplier_order_lists_id, spo.supplier_payments_orders_paid_amount, spo.supplier_payments_type, sol.products_purchases_lists_name, sol.supplier_order_lists_date, sol.currency, sol.currency_value
		        									FROM " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo
		        									INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol
		        										ON spo.supplier_order_lists_id=sol.supplier_order_lists_id
		        									WHERE spo.supplier_payments_id = '" . tep_db_input($payment_id) . "'
		        									ORDER BY spo.supplier_order_lists_id";
                    $payment_orders_result_sql = tep_db_query($payment_orders_select_sql);

                    while ($payment_orders_row = tep_db_fetch_array($payment_orders_result_sql)) {
                        $order_payable_amount = supplier_order::get_order_total_payable_amount($payment_orders_row['supplier_order_lists_id']);

                        echo '	<tr>
				  					<td align="left" valign="top" class="invoiceRecords">
				  						<a href="' . tep_href_link(FILENAME_SUPPLIERS_ORDERS, 'oID=' . $payment_orders_row['supplier_order_lists_id'] . '&action=edit&' . $HTTP_GET_VARS['SID'], 'NONSSL') . '" target="_blank">' . $payment_orders_row['supplier_order_lists_id'] . '</a>';
                        if ($payment_orders_row['supplier_payments_type'] == '1') {
                            echo '&nbsp;' . TEXT_PARTIAL_PAID_ORDER;
                        }
                        echo '		</td>
				  					<td align="left" valign="top" class="invoiceRecords" nowrap>' . $payment_orders_row['products_purchases_lists_name'] . '</td>
				  					<td align="left" valign="top" class="invoiceRecords" nowrap>' . $payment_orders_row['supplier_order_lists_date'] . '</td>
				  					<td align="right" valign="top" class="invoiceRecords">' . $currencies->format($order_payable_amount, true, $payment_orders_row['currency'], $payment_orders_row['currency_value']) . '</td>
				  					<td align="right" valign="top" class="invoiceRecords">' . $currencies->format($payment_orders_row['supplier_payments_orders_paid_amount'], true, $payment_row['currency'], $payment_row['currency_value']) . '</td>
				  				</tr>';
                    }

                    echo '	</table>';

                    echo ']]></order_detail>';
                    echo '</payment_records>';
                }
            }

            break;
        case "show_discount":
            if (tep_not_null($_GET['cust_grp_id'])) {
                if (file_exists(DIR_WS_LANGUAGES . $language . '/' . "orders.php")) {
                    include_once(DIR_WS_LANGUAGES . $language . '/' . "orders.php");
                }

                $discount_list = '';

                $group_discount_select_sql = "  SELECT customers_groups_discount, categories_name
                                                FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " AS cgd
                                                INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
                                                    ON (cgd.categories_id=cd.categories_id AND cd.language_id ='" . (int) $languages_id . "' )
                                                WHERE cgd.customers_groups_id=" . tep_db_input($_GET['cust_grp_id']) . "
                                                ORDER BY cd.categories_name";
                $group_discount_result_sql = tep_db_query($group_discount_select_sql);
                while ($group_discount_row = tep_db_fetch_array($group_discount_result_sql)) {
                    $discount_list .= $group_discount_row['customers_groups_discount'] . " (" . $group_discount_row['categories_name'] . ")<br />\n";
                } //end while

                if (!tep_not_null($discount_list)) {
                    $discount_list = TEXT_OPTION_NOT_APPLICABLE;
                }

                echo "<result><![CDATA[" . $discount_list . "]]></result>";
            }
            break;
        case 'get_order_statistic':
            $stat_html = '';

            $view_customer_purchases_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_CUSTOMER_PURCHASES');
            if ($view_customer_purchases_permission) {
                include_once(DIR_WS_CLASSES . 'order.php');

                $currencies = new currencies();
                $order = new order($order_id);
                $edit_order_obj = new edit_order($admin_id, $this_admin_email, $order_id);
                $payment_methods_obj = new payment_methods('payment_methods');
                $payment_info_array = $payment_methods_obj->payment_methods_array;

                $stat_html = '	<tr>
                                    <td class="main" valign="top" width="25%">
                                    <b>' . ENTRY_ORDER_STAT . '</b>' . TEXT_REAL_TIME_STAT . '<br>
                                    </td>
                                    <td class="main" valign="top" align="left">
                                        <table border="1" width="100%" align="left" cellspacing="0" cellpadding="5">';

                $stat_orders_status_array = array(
                    1 => 'Pending',
                    7 => 'Verifying',
                    8 => 'On Hold'
                );
                $stat_this_order_amount = 0;

                for ($ot_cnt = 0; $ot_cnt < count($order->totals); $ot_cnt++) {
                    if ($order->totals[$ot_cnt]['class'] == 'ot_total') {
                        $stat_this_order_amount = $order->totals[$ot_cnt]['value'];
                    }
                }

                $stat_html .= ' <tr>
                                    <td class="main">' . SUB_TABLE_HEADING_ORDER_STATUS . '</td>
                                    <td class="main" align="center">' . SUB_TABLE_HEADING_TOTAL_ORDER . '</td>
                                    <td class="main" align="right">' . SUB_TABLE_HEADING_TOTAL_AMOUNT . '</td>
                                </tr>';

                foreach ($stat_orders_status_array as $status_id => $status_name) {
                    $count_order_select_sql = "SELECT COUNT(o.orders_id) AS total_count, SUM(ot.value) total_amt
                                                FROM " . TABLE_ORDERS . " AS o
                                                LEFT JOIN " . TABLE_ORDERS_TOTAL . " AS ot
                                                    ON o.orders_id = ot.orders_id
                                                WHERE customers_id = '" . (int) $customers_id . "'
                                                    AND orders_status = '" . (int) $status_id . "'
                                                    AND ot.class = 'ot_total'";
                    $count_order_result_sql = tep_db_query($count_order_select_sql);
                    $count_order_row = tep_db_fetch_array($count_order_result_sql);

                    $order_stat_style = '';

                    if ($status_id == 7) {
                        if ($count_order_row['total_count'] > 1)
                            $order_stat_style = ' class="redIndicator" ';
                    } else if ($status_id == 8) {
                        if ($count_order_row['total_count'] > 0)
                            $order_stat_style = ' class="redIndicator" ';
                    }

                    $stat_html .= ' <tr>
                                        <td class="main" valign="top" nowrap><span ' . $order_stat_style . '>' . $status_name . '</span></td>
                                        <td class="main" align="center" valign="top" nowrap><span ' . $order_stat_style . '>' . $count_order_row['total_count'] . '</span>' . ($status_id == 3 ? $completed_order_per_period_str : '') . '</td>
                                        <td class="main" align="right" valign="top" nowrap><span ' . $order_stat_style . '>' . $currencies->format($count_order_row["total_amt"]) . '</span>' . ($status_id == 3 ? $completed_order_sales_per_period_str : '') . '</td>
                                    </tr>';
                }

                for ($loop_status_id = 2; $loop_status_id <= 3; $loop_status_id++) {
                    $total_order_column = array();
                    $total_amount_column = array();
                    $stat_array_index = 0;
                    $total_order_column[$stat_array_index] = 0;
                    $total_amount_column[$stat_array_index] = 0;

                    $good_delivered_stat_select_sql = " SELECT SUM(op.products_good_delivered_price) total_delivered
                                                        FROM " . TABLE_ORDERS . " AS o
                                                        INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                                            ON o.orders_id = op.orders_id
                                                        WHERE o.customers_id = '" . (int) $customers_id . "'
                                                            AND o.orders_status = '" . tep_db_input($loop_status_id) . "'
                                                            AND op.orders_products_is_compensate = 0
                                                            AND (o.orders_cb_status IS NULL or o.orders_cb_status = 0)
                                                        GROUP BY op.orders_id
                                                        HAVING total_delivered > 0";
                    $good_delivered_stat_result_sql = tep_db_query($good_delivered_stat_select_sql);
                    while ($good_delivered_stat_row = tep_db_fetch_array($good_delivered_stat_result_sql)) {
                        $total_order_column[$stat_array_index] ++;
                        $total_amount_column[$stat_array_index] += $good_delivered_stat_row['total_delivered'];
                    }

                    $stat_array_index++;

                    $payment_canceled_stat_select_sql = "SELECT COUNT(DISTINCT o.orders_id) AS total_count, SUM(sr.store_refund_amount) total_refunded
                                                        FROM " . TABLE_ORDERS . " AS o
                                                        INNER JOIN " . TABLE_STORE_REFUND . " AS sr
                                                            ON (o.orders_id=sr.store_refund_trans_id AND sr.store_refund_status IN (1, 2, 3))
                                                        WHERE sr.user_id = '" . (int) $customers_id . "'
                                                            AND o.orders_status = '" . tep_db_input($loop_status_id) . "' ";
                    $payment_canceled_stat_result_sql = tep_db_query($payment_canceled_stat_select_sql);
                    $payment_canceled_stat_row = tep_db_fetch_array($payment_canceled_stat_result_sql);
                    $total_order_column[$stat_array_index] = $payment_canceled_stat_row['total_count'];
                    $total_amount_column[$stat_array_index] = $payment_canceled_stat_row['total_refunded'];

                    $stat_array_index++;

                    if ($loop_status_id == 3) {
                        $total_order_column[$stat_array_index] = 0;
                        $total_amount_column[$stat_array_index] = 0;

                        $cb_resolved_stat_select_sql = "SELECT SUM(op.products_reversed_price) total_resolved, SUM(op.products_good_delivered_price) as total_deliver
                                                        FROM " . TABLE_ORDERS . " AS o
                                                        INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                                            ON o.orders_id = op.orders_id
                                                        WHERE o.customers_id = '" . (int) $customers_id . "'
                                                            AND o.orders_status = '" . tep_db_input($loop_status_id) . "'
                                                            AND o.orders_cb_status = 3
                                                            AND op.orders_products_is_compensate = 0
                                                        GROUP BY op.orders_id";
                        $cb_resolved_stat_result_sql = tep_db_query($cb_resolved_stat_select_sql);
                        while ($cb_resolved_stat_row = tep_db_fetch_array($cb_resolved_stat_result_sql)) {
                            $total_order_column[$stat_array_index] ++;
                            $total_amount_column[$stat_array_index] += $cb_resolved_stat_row['total_resolved'] + $cb_resolved_stat_row['total_deliver'];
                        }

                        $stat_array_index++;
                        $total_order_column[$stat_array_index] = 0;
                        $total_amount_column[$stat_array_index] = 0;

                        $cb_win_stat_select_sql = "SELECT SUM(op.products_reversed_price) total_win, SUM(op.products_good_delivered_price) as total_deliver
                                                    FROM " . TABLE_ORDERS . " AS o
                                                    INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                                        ON o.orders_id = op.orders_id
                                                    WHERE o.customers_id = '" . (int) $customers_id . "'
                                                        AND o.orders_status = '" . tep_db_input($loop_status_id) . "'
                                                        AND o.orders_cb_status = 1
                                                        AND op.orders_products_is_compensate = 0
                                                    GROUP BY op.orders_id";
                        $cb_win_stat_result_sql = tep_db_query($cb_win_stat_select_sql);
                        while ($cb_win_stat_row = tep_db_fetch_array($cb_win_stat_result_sql)) {
                            $total_order_column[$stat_array_index] ++;
                            $total_amount_column[$stat_array_index] += $cb_win_stat_row['total_win'] + $cb_win_stat_row['total_deliver'];
                        }

                        $stat_array_index++;

                        $total_order_column[$stat_array_index] = 0;
                        $total_amount_column[$stat_array_index] = 0;

                        $cb_lost_stat_select_sql = "SELECT SUM(op.products_reversed_price) total_lost, SUM(op.products_good_delivered_price) as total_deliver
                                                    FROM " . TABLE_ORDERS . " AS o
                                                    INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
                                                        ON o.orders_id = op.orders_id
                                                    WHERE o.customers_id = '" . (int) $customers_id . "'
                                                        AND o.orders_status = '" . tep_db_input($loop_status_id) . "'
                                                        AND o.orders_cb_status = 2
                                                        AND op.orders_products_is_compensate = 0
                                                    GROUP BY op.orders_id";
                        $cb_lost_stat_result_sql = tep_db_query($cb_lost_stat_select_sql);
                        while ($cb_lost_stat_row = tep_db_fetch_array($cb_lost_stat_result_sql)) {
                            $total_order_column[$stat_array_index] ++;
                            $total_amount_column[$stat_array_index] += $cb_lost_stat_row['total_lost'] + $cb_lost_stat_row['total_deliver'];
                        }
                    }

                    $breakdown_stat_array = array(
                        'Delivered',
                        'Refunded (Payment Gateway)',
                        'Reversed - Resolved',
                        'Reversed - Win',
                        'Reversed - Loss'
                    );
                    $first_col_string = $second_col_string = $third_col_string = '';

                    for ($nf_cnt = 0; $nf_cnt < count($total_amount_column); $nf_cnt++) {
                        $order_stat_style = '';
                        if ($total_order_column[$nf_cnt] > 0) {
                            if ($nf_cnt == 2) {
                                $order_stat_style = ' class="orangeIndicator" ';
                            } else if ($nf_cnt > 2) {
                                $order_stat_style = ' class="redIndicator" ';
                            }
                        }

                        $first_col_string .= '&nbsp;&nbsp;&nbsp;<span ' . $order_stat_style . '>' . $breakdown_stat_array[$nf_cnt] . '</span><br>';
                        $second_col_string .= '<span ' . $order_stat_style . '>' . (int) $total_order_column[$nf_cnt] . '</span><br>';
                        $third_col_string .= '<span ' . $order_stat_style . '>' . $currencies->format($total_amount_column[$nf_cnt]) . '</span><br>';
                    }

                    $stat_html .= ' <tr>
                                        <td class="main" valign="top" nowrap><b>' . (($loop_status_id == 2) ? 'Processing' : 'Completed') . '</b></td>
                                        <td class="main" align="center" valign="top" nowrap>&nbsp;</td>
                                        <td class="main" align="right" valign="top" nowrap>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top" nowrap>' . $first_col_string . '</td>
                                        <td class="main" align="center" valign="top" nowrap>' . $second_col_string . '</td>
                                        <td class="main" align="right" valign="top" nowrap>' . $third_col_string . '</td>
                                    </tr>';
                }

                $purchase_limit_per = array(
                    'day' => '-1',
                    'week' => '-1',
                    'month' => '-1'
                );
                $_sql = "SELECT orders_extra_info_value FROM " . TABLE_ORDERS_EXTRA_INFO . " WHERE orders_id = " . $order_id . " AND orders_extra_info_key = 'ip_country'";
                $_res = tep_db_query($_sql);
                if ($_row = tep_db_fetch_array($_res)) {
                    $ip_country_info_array = tep_get_countries_info($_row["orders_extra_info_value"], "countries_id");
                    $purchase_limit_select_sql = "  SELECT purchase_limit_per_day, purchase_limit_per_week, purchase_limit_per_month, purchase_limit_used
                                                    FROM " . TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT . "
                                                    WHERE customers_aft_groups_id = " . (int) $customers_groups_id . "
                                                        AND aft_countries_risk_type = '" . tep_db_input($ip_country_info_array['aft_risk_type']) . "'";
                    $purchase_limit_result_sql = tep_db_query($purchase_limit_select_sql);
                    if ($purchase_limit_row = tep_db_fetch_array($purchase_limit_result_sql)) {
                        if ($purchase_limit_row['purchase_limit_used']) {
                            $purchase_limit_per['day'] = $purchase_limit_row['purchase_limit_per_day'];
                            $purchase_limit_per['week'] = $purchase_limit_row['purchase_limit_per_week'];
                            $purchase_limit_per['month'] = $purchase_limit_row['purchase_limit_per_month'];
                        }
                    }
                }

                $stat_RP_method_array = array();
                if (isset($payment_info_array) && is_array($payment_info_array)) {
                    foreach ($payment_info_array as $pm_id => $pm_info) {
                        if ((int) $pm_info->confirm_complete_days > 0) {
                            $stat_RP_method_array[] = $pm_id;
                        }
                    }
                }

                $completed_per_period_array = array(
                    'day' => '1 DAY',
                    'week' => '7 DAY',
                    'month' => '1 MONTH'
                );
                foreach ($completed_per_period_array as $period_id => $completed_period_sql) {
                    $periodical_sales_total = 0;
                    $periodical_sales_total_amount = 0;

                    $sql = "SELECT o.orders_id, o.orders_cb_status, ot.value
                            FROM " . TABLE_ORDERS . " AS o
                            INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
                                ON ot.orders_id = o.orders_id
                                AND ot.class = 'ot_total'
                            WHERE o.customers_id = " . (int) $customers_id . "
                                AND o.orders_status IN (2, 3)
                                AND o.date_purchased >= DATE_SUB(NOW(), INTERVAL " . $completed_period_sql . ")
                                AND o.payment_methods_id IN ('" . implode("', '", $stat_RP_method_array) . "')
                                AND ot.value > 0";
                    $res = tep_db_query($sql);
                    while ($row = tep_db_fetch_array($res)) {
                        $canceled_amt = 0;

                        $_sql = "SELECT SUM(products_canceled_price) AS canceled_amt, SUM(products_reversed_price) AS reversed_amt
                                FROM " . TABLE_ORDERS_PRODUCTS . "
                                WHERE orders_id = " . $row["orders_id"];
                        $_res = tep_db_query($_sql);
                        if ($_row = tep_db_fetch_array($_res)) {
                            $canceled_amt = $_row["canceled_amt"];
                            if ($row["orders_cb_status"] == 1 || $row["orders_cb_status"] == 2) {
                                $canceled_amt += $_row["reversed_amt"];
                            }
                        }

                        if ($row["value"] > $canceled_amt) {
                            $periodical_sales_total++;
                            $periodical_sales_total_amount += ($row["value"] - $canceled_amt);
                        }
                    }

                    if ($order->info['orders_status'] == 7) { // Including this order
                        $periodical_sales_total++;
                        $periodical_sales_total_amount += $stat_this_order_amount;
                    }

                    if ($purchase_limit_per[$period_id] == -1) {
                        $purchase_limit_status = ' <span class="darkOrangeIndicator">(not checked)</span>';
                    } else if ($periodical_sales_total_amount > $purchase_limit_per[$period_id]) {
                        $purchase_limit_status = ' <span class="redIndicatorBold">(overbought)</span>';
                    } else {
                        $purchase_limit_status = ' <span class="blackIndicator">(within limit)</span>';
                    }

                    switch ($period_id) {
                        case 'day':
                            $periodical_sales_title .= '<b>total RP received in 1 day##TITLE_EXTENSION##' . $purchase_limit_status . '</b>';
                            $completed_order_per_period_str .= '<b>' . $periodical_sales_total . '</b><br><br>';
                            $completed_order_sales_per_period_str .= '<b>' . $currencies->format($periodical_sales_total_amount) . '</b><br><br>';

                            break;
                        case 'week':
                        case 'month':
                            $periodical_sales_title .= '<br><br>total RP received in 1 ' . $period_id . '##TITLE_EXTENSION##' . $purchase_limit_status;
                            $completed_order_per_period_str .= $periodical_sales_total . '<br><br>';
                            $completed_order_sales_per_period_str .= $currencies->format($periodical_sales_total_amount) . '<br><br>';

                            break;
                    }
                }

                if ($order->info['orders_status'] == 7) { // Including this order
                    $periodical_sales_title = str_ireplace('##TITLE_EXTENSION##', ' + this', $periodical_sales_title);
                } else {
                    $periodical_sales_title = str_ireplace('##TITLE_EXTENSION##', '', $periodical_sales_title);
                }

                // Confirmed completed
                $total_confirmed_completed_order = $total_confirmed_completed_sales = 0;

                $customer_payment_methods_select_sql = "SELECT DISTINCT payment_methods_id
                                                        FROM " . TABLE_ORDERS . "
                                                        WHERE customers_id = '" . (int) $customers_id . "'
                                                            AND orders_status IN (2, 3)";
                $customer_payment_methods_result_sql = tep_db_query($customer_payment_methods_select_sql);
                while ($customer_payment_methods_row = tep_db_fetch_array($customer_payment_methods_result_sql)) {
                    $confirm_complete_day = (int) $payment_info_array[$customer_payment_methods_row['payment_methods_id']]->confirm_complete_days > 0 ? (int) $payment_info_array[$customer_payment_methods_row['payment_methods_id']]->confirm_complete_days : 0;

                    $sql = "SELECT o.orders_id, o.orders_cb_status, ot.value
                            FROM " . TABLE_ORDERS . " AS o
                            INNER JOIN " . TABLE_ORDERS_TOTAL . " AS ot
                                ON ot.orders_id = o.orders_id
                                AND ot.class = 'ot_total'
                            WHERE o.customers_id = " . (int) $customers_id . "
                                AND o.orders_status IN (2, 3)
                                AND o.date_purchased <= DATE_SUB(NOW(), INTERVAL " . $confirm_complete_day . " DAY)
                                AND o.payment_methods_id = " . tep_db_input($customer_payment_methods_row["payment_methods_id"]) . "
                                AND ot.value > 0";
                    $res = tep_db_query($sql);
                    while ($row = tep_db_fetch_array($res)) {
                        $canceled_amt = 0;

                        $_sql = "SELECT SUM(products_canceled_price) AS canceled_amt, SUM(products_reversed_price) AS reversed_amt
                                FROM " . TABLE_ORDERS_PRODUCTS . "
                                WHERE orders_id = " . $row["orders_id"];
                        $_res = tep_db_query($_sql);
                        if ($_row = tep_db_fetch_array($_res)) {
                            $canceled_amt = $_row["canceled_amt"];
                            if ($row["orders_cb_status"] == 1 || $row["orders_cb_status"] == 2) {
                                $canceled_amt += $_row["reversed_amt"];
                            }
                        }

                        if ($row["value"] > $canceled_amt) {
                            $total_confirmed_completed_order++;
                            $total_confirmed_completed_sales += ($row["value"] - $canceled_amt);
                        }
                    }
                }

                $stat_html .= '                         </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                    <td></td>
                                                    <td class="main" valign="top" align="right">
                                                        <table border="0" width="100%" align="left" cellspacing="0" cellpadding="5">
                                                        <tr>
                                                            <td class="main" valign="top" nowrap>this order</td>
                                                            <td class="main" align="center" valign="top" nowrap></td>
                                                            <td class="main" align="right" valign="top" nowrap>' . $currencies->format($stat_this_order_amount) . '</td>
                                                        </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top" width="25%"><b>Payment Received Statistics:</b></td>
                                                    <td class="main" valign="top" align="left">
                                                        <table border="1" width="100%" align="left" cellspacing="0" cellpadding="5">
                                                            <tr>
                                                                <td class="main" valign="top" nowrap>' . $periodical_sales_title . '</td>
                                                                <td class="main" align="center" valign="top" nowrap>' . $completed_order_per_period_str . '</td>
                                                                <td class="main" align="right" valign="top" nowrap>' . $completed_order_sales_per_period_str . '</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="main" valign="top" nowrap>total NRP + Confirmed RP received</td>
                                                                <td class="main" align="center" valign="top" nowrap>' . $total_confirmed_completed_order . '</td>
                                                                <td class="main" align="right" valign="top" nowrap>' . $currencies->format($total_confirmed_completed_sales) . '</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top" nowrap><b>' . ENTRY_CUSTOMER_FIRST_COMPLETED_ORDER . '</b>' . TEXT_REAL_TIME_STAT . '</td>
                                                    <td class="main"><div id="first_order_div"></div></td>
                                                </tr>';
            }

            echo '  <stat_result>
                        <stat><![CDATA[' . $stat_html . ']]></stat>
                    </stat_result>';
            break;
        case 'get_first_completed_order':
            $first_completed_order_str = TEXT_NOT_AVAILABLE;

            $view_customer_details_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_CUSTOMER_DETAILS');
            if ($view_customer_details_permission) {
                $sql = "SELECT orders_id, date_purchased FROM " . TABLE_ORDERS . " WHERE customers_id = " . (int) $customers_id . " AND orders_status = 3 ORDER BY orders_id LIMIT 1";
                $res = tep_db_query($sql);
                if ($row = tep_db_fetch_array($res)) {
                    $first_completed_order_str = "#" . $row["orders_id"] . " (" . $row['date_purchased'] . ")";
                }
            }
            echo '  <first_order_result>
                        <first_order><![CDATA[' . $first_completed_order_str . ']]></first_order>
                    </first_order_result>';
            break;
        case 'check_order_locking_status':
            $log_object = new log_files($admin_id);
            $lock_obj = new process_locking();

            $log_object->set_log_table(TABLE_ORDERS_LOG_TABLE);
            $lock_orders_select_sql = "	SELECT orders_locked_by
                                        FROM " . TABLE_ORDERS . "
                                        WHERE orders_id = '" . $order_id . "'
                                            AND orders_locked_datetime >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)";
            $lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
            $lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);
            if (isset($lock_orders_row["orders_locked_by"]) && tep_not_null($lock_orders_row["orders_locked_by"])) { // this order currently is locked
                if ($lock_orders_row["orders_locked_by"] == $admin_id) {
                    echo '<result>Not Locked</result>';
                } else {
                    echo '<result>Locked</result>';
                }
            } else {
                if ($lock_obj->isLocked($order_id, process_locking::MOVE_ORDER, FALSE) === FALSE) {
                    $lock_order_update_sql = "  UPDATE " . TABLE_ORDERS . "
                                                SET orders_locked_by = '" . $admin_id . "', orders_locked_from_ip = '" . tep_db_input(tep_get_ip_address()) . "', orders_locked_datetime = now()
                                                WHERE orders_id = '" . $order_id . "'";
                    tep_db_query($lock_order_update_sql);

                    $log_object->insert_orders_log($order_id, ORDERS_LOG_LOCK_ORDER, FILENAME_ORDERS);
                    echo '<result>Not Locked</result>';
                } else {
                    echo '<result>Locked</result>';
                }
            }

            unset($lock_obj);
            break;
        case 'unlock_verifying_order':
            $log_object = new log_files($admin_id);
            $lock_obj = new process_locking();

            $log_object->set_log_table(TABLE_ORDERS_LOG_TABLE);
            $unlock_orders_select_sql = "	SELECT orders_locked_by
                                            FROM " . TABLE_ORDERS . "
                                            WHERE orders_id = '" . $order_id . "'";
            $unlock_orders_result_sql = tep_db_query($unlock_orders_select_sql);
            $unlock_orders_row = tep_db_fetch_array($unlock_orders_result_sql);
            $lock_status = $lock_obj->orderIsLocked($admin_id, $order_id, process_locking::MOVE_ORDER, $unlock_orders_row);

            if ($lock_status === false) { // this order currently is locked
                $unlock_orders_update_sql = "UPDATE " . TABLE_ORDERS . " SET orders_locked_by = NULL, orders_locked_from_ip = NULL, orders_locked_datetime = NULL WHERE orders_id = '" . $order_id . "'";
                tep_db_query($unlock_orders_update_sql);
                $log_object->insert_orders_log($order_id, ORDERS_LOG_UNLOCK_OWN_ORDER, FILENAME_ORDERS);
            }

            unset($lock_obj);
            break;
        case 'update_actual_received_amount':
            $alert_msg = '';

            if ($order_id) {
                if ($edit_order_amount_permission = tep_admin_files_actions(FILENAME_ORDERS, 'EDIT_ORDER_MODIFY_PENDING_ORDER')) {
                    $old_order_extra_array = array();
                    $old_ot_info_array = array();
                    $new_order_extra_array = array();
                    $new_ot_info_array = array();


                    $order_obj = new order($order_id);

                    $select_sql = "	SELECT *
                                    FROM " . TABLE_ORDERS_EXTRA_INFO . "
                                    WHERE orders_id = '" . $order_id . "'";
                    $result_sql = tep_db_query($select_sql);
                    while ($row = tep_db_fetch_array($result_sql)) {
                        $old_order_extra_array[$row['orders_extra_info_key']] = $row['orders_extra_info_value'];
                    }

                    $site_id = $old_order_extra_array['site_id'];

                    foreach ($order_obj->totals as $idx => $ot_info) {
                        $old_ot_info_array[$ot_info['class']] = $ot_info['value'];

                        if ($ot_info['class'] == 'ot_total') {
                            $checkout_total_value_formatted = $ot_info['text'];
                        }
                    }

                    $scale = 8;
                    $new_total_amount = isset($_GET['new_amt']) ? tep_db_prepare_input($_GET['new_amt']) : 0;

                    $checkout_currency = $order_obj->info['currency'];
                    $checkout_currency_value = $order_obj->info['currency_value'];

                    $checkout_total_value = bcmul($old_ot_info_array['ot_total'], $checkout_currency_value, $scale);
                    $checkout_subtotal_value = bcmul($old_ot_info_array['ot_subtotal'], $checkout_currency_value, $scale);

                    $checkout_tax_enabled = isset($old_order_extra_array['tax_percentage']);
                    # value could be 0 or 6 for msia
                    $checkout_tax_rate = $checkout_tax_enabled ? (int) $old_order_extra_array['tax_percentage'] : 0;

                    $checkout_coupon_enabled = isset($old_ot_info_array['ot_coupon']);
                    $checkout_coupon_value = $checkout_coupon_enabled ? bcmul($old_ot_info_array['ot_coupon'], $checkout_currency_value, $scale) : 0;

                    $checkout_sc_enabled = isset($old_ot_info_array['ot_gv']);
                    $checkout_sc_value = $checkout_sc_enabled ? bcmul($old_ot_info_array['ot_gv'], $checkout_currency_value, $scale) : 0;

                    $compare_checkout_total_value = round($checkout_total_value, 2);
                    $gst_selected_check = '';

                    if (isset($old_order_extra_array['payment_ip_country']) && $old_order_extra_array['payment_ip_country']) {
                        $tax_country = $old_order_extra_array['payment_ip_country'];
                    } else {
                        $country_info = isset($old_order_extra_array['payment_ip']) ? tep_get_ip_country_info($old_order_extra_array['payment_ip']) : array();
                        $tax_country = isset($country_info['countries_iso_code_2']) ? $country_info['countries_iso_code_2'] : '';
                    }

                    if ($checkout_tax_rate == 0 && $tax_country) {
                        $gst_sel_sql = "SELECT otc.orders_tax_percentage                                                                                                                          
                                        FROM " . TABLE_ORDERS_TAX_CONFIGURATION . " AS otc 
                                        INNER JOIN " . TABLE_ORDERS_TAX_CONFIGURATION_DESCRIPTION . " AS otcd 
                                            ON otcd.orders_tax_id = otc.orders_tax_id 
                                        LEFT JOIN " . TABLE_COUNTRIES . " AS c 
                                            ON c.countries_iso_code_2 = otc.country_code 
                                        WHERE otcd.language_id = '" . GST_DEFAULT_LANGUAGE_ID . "'
                                            AND c.countries_iso_code_2 = '" . $tax_country . "'";
                        $tax_percentage_query = tep_db_query($gst_sel_sql);
                        if ($result = tep_db_fetch_array($tax_percentage_query)) {
                            $checkout_tax_rate = $result['orders_tax_percentage'];
                        }
                    }

                    if (!isset($old_ot_info_array['ot_surcharge'])) {
                        $log_message = '';

                        if ($new_total_amount > $compare_checkout_total_value) {
                            # ------------------------------------------    Paid Amount > Order Amount    ------------------------------------------
                            # re-calculate ot_subtotal = new_total_amount + ot_coupon + ot_gv

                            $new_subtotal_value = bcadd(bcadd($new_total_amount, $checkout_coupon_value, $scale), $checkout_sc_value, $scale);

                            // echo $new_subtotal_value.'<br>';
                            if ($checkout_tax_enabled && $site_id != 0) {
                                # ========================================    [Paid Amount > Order Amount] Order Occur GST    ========================================

                                if (isset($_GET['radio_with_gst']) && $_GET['radio_with_gst'] == "0") {
                                    $gst_selected_check = 'Product GST chargeable:No';
                                    $new_subtotal_value = bcadd(bcadd($new_total_amount, $checkout_coupon_value, $scale), $checkout_sc_value, $scale);
                                    $non_gst_checkout_subtotal = getNonGstAmount($order_id);
                                    $non_gst_checkout_subtotal = bcmul($non_gst_checkout_subtotal, $checkout_currency_value, $scale);
                                    $new_sub_total_amount = bcsub($new_subtotal_value, $non_gst_checkout_subtotal, $scale);
                                    $new_tax_value = $currencies->advance_currency_conversion(bcdiv(bcmul($new_sub_total_amount, $checkout_tax_rate, $scale), bcadd(100, $checkout_tax_rate, $scale), $scale), $checkout_currency, $checkout_currency);

                                    $new_sub_total_amount1 = bcsub($new_sub_total_amount, $new_tax_value, $scale);

                                    $sub1 = bcsub($checkout_subtotal_value, $non_gst_checkout_subtotal, $scale);
                                    $sub2 = bcsub($new_sub_total_amount1, $sub1, $scale);


                                    $order_total_sub_total_new_value = bcsub($new_subtotal_value, $new_tax_value, $scale);

                                    $order_total_gst_new_value = $new_tax_value;
                                    $order_total_total_new_value = $new_total_amount;

                                    $sc_checkout_value = $sub2;
                                } else if (isset($_GET['radio_with_gst']) && $_GET['radio_with_gst'] == "1") {
                                    $gst_selected_check = 'Product GST chargeable:Yes';

                                    $new_tax_value = $currencies->advance_currency_conversion(bcdiv(bcmul($new_total_amount, $checkout_tax_rate, $scale), bcadd(100, $checkout_tax_rate, $scale), $scale), $checkout_currency, $checkout_currency);
                                    # re-calculate GST  = (new_total_amount * GST rate) / (100 + GST rate)
                                    //$checkout_tax_rate=6.00;
                                    // $new_tax_value = $currencies->advance_currency_conversion(bcdiv(bcmul($new_total_amount, $checkout_tax_rate, $scale), bcadd(100, $checkout_tax_rate, $scale), $scale), $checkout_currency, $checkout_currency);
                                    //$new_tax_value = $currencies->advance_currency_conversion(bcdiv(bcmul($new_subtotal_value, $checkout_tax_rate, $scale), bcadd(100, $checkout_tax_rate, $scale), $scale), $checkout_currency, $checkout_currency);
                                    # re-calculate New ot_subtotal = ot_subtotal - GST
                                    $new_subtotal_value = bcsub($new_subtotal_value, $new_tax_value, $scale);

                                    $sc_checkout_value = bcsub($new_subtotal_value, $checkout_subtotal_value, $scale);
                                    //$sc_checkout_value = $new_subtotal_value;
                                    # Add New SC product's Qty = New ot_subtotal - ot_subtotal
                                    $order_total_sub_total_new_value = $new_subtotal_value;
                                    $order_total_gst_new_value = $new_tax_value;
                                    $order_total_total_new_value = $new_total_amount;
                                }

                                list($sc_checkout_unit_price, $sc_checkout_qty) = addStoreCredit_product($order_id, $order_obj->customer['id'], $checkout_currency, $checkout_currency_value, $sc_checkout_value);

                                if ($sc_checkout_qty) {
                                    # Update ot_gst, ot_subtotal & ot_total - convert all the value to base currency

                                    if (isset($_GET['radio_with_gst']) && $_GET['radio_with_gst'] == "0" || $_GET['radio_with_gst'] == "1") {

                                        $gst_selected_check = ($_GET['radio_with_gst'] == "1") ? 'Product GST chargeable:Yes' : 'Product GST chargeable:No';
                                        $new_ot_info_array = array(
                                            'ot_total' => array(
                                                'text' => '<b>' . $currencies->format($order_total_total_new_value, false, $checkout_currency) . '</b>',
                                                'value' => $order_total_total_new_value / $checkout_currency_value,
                                            ),
                                            'ot_subtotal' => array(
                                                'text' => $currencies->format($order_total_sub_total_new_value, false, $checkout_currency),
                                                'value' => $order_total_sub_total_new_value / $checkout_currency_value,
                                            ),
                                            'ot_gst' => array(
                                                'title' => ('GST (' . $checkout_tax_rate . '%):'),
                                                'text' => '<b>' . $currencies->format($order_total_gst_new_value, false, $checkout_currency) . '</b>',
                                                'value' => ($order_total_gst_new_value > 0 ? ($order_total_gst_new_value / $checkout_currency_value) : 0),
                                                'output' => array(
                                                    'tax_amount' => $currencies->advance_currency_conversion($order_total_gst_new_value, $checkout_currency, $checkout_currency),
                                                    'tax_percentage' => $checkout_tax_rate,
                                                ),
                                            ),
                                        );
                                    }
                                }
                            } else {
                                # ========================================    [Paid Amount > Order Amount] Order Without GST    ========================================
                                # Add New SC product's Qty = New ot_subtotal - ot_subtotal
                                # OG order checkout need to deduct gst amount before credit sc
                                if ($site_id == 0) {
                                    $tax_amount = $old_order_extra_array['tax_amount'];
                                    $new_subtotal_value = bcsub($new_subtotal_value, $tax_amount, $scale);
                                }

                                $sc_checkout_value = bcsub($new_subtotal_value, $checkout_subtotal_value, $scale);
                                list($sc_checkout_unit_price, $sc_checkout_qty) = addStoreCredit_product($order_id, $order_obj->customer['id'], $checkout_currency, $checkout_currency_value, $sc_checkout_value);

                                if ($sc_checkout_qty) {
                                    # Update ot_subtotal & ot_total
                                    $new_ot_info_array = array(
                                        'ot_total' => array(
                                            'text' => '<b>' . $currencies->format($new_total_amount, false, $checkout_currency) . '</b>',
                                            'value' => $new_total_amount / $checkout_currency_value,
                                        ),
                                        'ot_subtotal' => array(
                                            'text' => $currencies->format($new_subtotal_value, false, $checkout_currency),
                                            'value' => $new_subtotal_value / $checkout_currency_value,
                                        ),
                                    );
                                }
                            }

                            $log_message .= '<br><br>Order Total amount changes:<br>';
                            $log_message .= $checkout_total_value_formatted . ' >> ' . $currencies->format($new_total_amount, false, $checkout_currency);
                        } else if ($new_total_amount < $compare_checkout_total_value) {
                            # ------------------------------------------    Paid Amount < Order Amount    ------------------------------------------
                            # Got ot_coupon, remove
                            # Got ot_gv, credit back
                            ///////////////////////////////////////////////////// CREDIT BACK SC //////////////////////////////////////////////////////////////////////////
                            if ($checkout_sc_enabled) {
                                # If order change from pending to cancel, sc will credit back to customer again due to cancel will return sc based in STORE_CREDIT_HISTORY.
                                $edit_order_obj = new edit_order($login_id, $login_email_address, $order_id);
                                $sc_result_array = $edit_order_obj->manage_order_sc(1, 5, $messageStack);
                                $order_comment = 'Store Credit used during checkout has been credited to Customer Store Credit balance.';

                                for ($sc_cnt = 0; $sc_cnt < count($sc_result_array); $sc_cnt++) {
                                    if ($sc_cnt == 0)
                                        $order_comment .= "\n" . TEXT_ORDER_INFO_SC_TOTAL . ' ' . $currencies->format($sc_result_array[$sc_cnt]['sc_amount'], false, $sc_result_array[$sc_cnt]['sc_currency']) . '(' . $sc_result_array[$sc_cnt]['sc_trans_id'] . ')';
                                }

                                $orders_status_history_sql_data = array(
                                    'orders_id' => $order_id,
                                    'orders_status_id' => 0,
                                    'date_added' => 'now()',
                                    'customer_notified' => 0,
                                    'comments' => $order_comment,
                                    'comments_type' => '0',
                                    'changed_by' => 'system'
                                );
                                tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_sql_data);
                                tep_db_query("DELETE FROM " . TABLE_ORDERS_TOTAL . " WHERE orders_id = '" . $order_id . "' AND class = 'ot_gv'");
                            }

                            if ($checkout_tax_enabled && $site_id != '0') {
                                # ========================================    [Paid Amount < Order Amount] Order Occur GST    ========================================
                                # re-calculate GST  = (new_total_amount * GST rate) / (100 + GST rate)
                                $new_tax_value = $currencies->advance_currency_conversion(bcdiv(bcmul($new_total_amount, $checkout_tax_rate, $scale), bcadd(100, $checkout_tax_rate, $scale), $scale), $checkout_currency, $checkout_currency);

                                # re-calculate ot_subtotal = new_total_amount - GST
                                $new_subtotal_value = bcsub($new_total_amount, $new_tax_value, $scale);

                                # Change product to SC product's Qty = ot_subtotal
                                $sc_checkout_value = $new_subtotal_value;

                                list($sc_checkout_unit_price, $sc_checkout_qty) = addStoreCredit_product($order_id, $order_obj->customer['id'], $checkout_currency, $checkout_currency_value, $sc_checkout_value, true);

                                if ($sc_checkout_qty) {
                                    if (isset($_GET['radio_with_gst']) && $_GET['radio_with_gst'] == "1" || $_GET['radio_with_gst'] == "0") {
                                        //$new_tax_value=$order_total_gst_new_value;
                                        $gst_selected_check = ($_GET['radio_with_gst'] == "1") ? 'Product GST chargeable:Yes' : 'Product GST chargeable:No';
                                    }

                                    //var_dump($new_subtotal_value,$new_tax_value,$new_total_amount);exit;
                                    $new_ot_info_array = array(
                                        'ot_coupon' => array(
                                            'remove' => 1
                                        ),
                                        'ot_total' => array(
                                            'text' => '<b>' . $currencies->format($new_total_amount, false, $checkout_currency) . '</b>',
                                            'value' => $new_total_amount / $checkout_currency_value,
                                        ),
                                        'ot_subtotal' => array(
                                            'text' => $currencies->format($new_subtotal_value, false, $checkout_currency),
                                            'value' => $new_subtotal_value / $checkout_currency_value,
                                        ),
                                        'ot_gst' => array(
                                            'title' => ('GST (' . $checkout_tax_rate . '%):'),
                                            'text' => '<b>' . $currencies->format($new_tax_value, false, $checkout_currency) . '</b>',
                                            'value' => ($new_tax_value > 0 ? ($new_tax_value / $checkout_currency_value) : 0),
                                            'output' => array(
                                                'tax_amount' => $currencies->advance_currency_conversion($new_tax_value, $checkout_currency, $checkout_currency),
                                                'tax_percentage' => $checkout_tax_rate,
                                            ),
                                        ),
                                    );
                                }
                            } else {
                                # ========================================    [Paid Amount < Order Amount] Order Without GST    ========================================
                                $new_subtotal_value = $new_total_amount;

                                # Change product to SC product's Qty = new_total_amount
                                $sc_checkout_value = $new_subtotal_value;
                                list($sc_checkout_unit_price, $sc_checkout_qty) = addStoreCredit_product($order_id, $order_obj->customer['id'], $checkout_currency, $checkout_currency_value, $sc_checkout_value, true);

                                if ($sc_checkout_qty) {
                                    # Update ot_subtotal & ot_total
                                    $new_ot_info_array = array(
                                        'ot_coupon' => array(
                                            'remove' => 1
                                        ),
                                        'ot_total' => array(
                                            'text' => '<b>' . $currencies->format($new_total_amount, false, $checkout_currency) . '</b>',
                                            'value' => $new_total_amount / $checkout_currency_value,
                                        ),
                                        'ot_subtotal' => array(
                                            'text' => $currencies->format($new_subtotal_value, false, $checkout_currency),
                                            'value' => $new_subtotal_value / $checkout_currency_value,
                                        ),
                                    );
                                }

                                if ($site_id == 0) {
                                    $new_ot_info_array['ot_gst'] = array(
                                        'remove' => 1
                                    );
                                }

                                # Update ot_subtotal & ot_total
                            }

                            $log_message .= '<br><br>Order Total amount changes:<br>';
                            $log_message .= $checkout_total_value_formatted . ' >> ' . $currencies->format($new_total_amount, false, $checkout_currency);
                        } else {
                            $alert_msg = 'Received amt == Order amt ( ' . $new_total_amount . ' == ' . $compare_checkout_total_value . ' ).' . "\n" . 'No need to use this feature.';
                        }

                        if ($log_message) {
                            $orders_status_history_sql_data = array(
                                'orders_id' => $order_id,
                                'orders_status_id' => 0,
                                'date_added' => 'now()',
                                'customer_notified' => 0,
                                'comments' => $log_message . '<br><br>' . $gst_selected_check,
                                'comments_type' => '0',
                                'changed_by' => $this_admin_email
                            );
                            tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_sql_data);
                        }
                    } else {
                        $alert_msg = 'Order with PG surcharge, Unable to proceed.';
                    }

                    foreach ($new_ot_info_array as $key => $info_array) {
                        if (isset($info_array['value'])) {
                            $sql_data_array = array(
                                'text' => $info_array['text'],
                                'value' => $info_array['value']
                            );

                            if ($key == 'ot_gst') {
                                $sql_data_array['text'] = $info_array['text'];
                            }

                            tep_db_perform(TABLE_ORDERS_TOTAL, $sql_data_array, 'update', " orders_id = '" . $order_id . "' AND class = '" . $key . "'");
                        }

                        /* -- GST :: record GST percentage -- */
                        if ($key == 'ot_gst' && !isset($info_array['remove'])) {
                            foreach ($info_array['output'] as $key2 => $val2) {
                                $orders_extra_info = array(
                                    'orders_extra_info_value' => $val2
                                );
                                tep_db_perform(TABLE_ORDERS_EXTRA_INFO, $orders_extra_info, 'update', " orders_id='" . $order_id . "' AND orders_extra_info_key = '" . $key2 . "'");
                            }
                        }

                        if (isset($info_array['remove']) && $info_array['remove'] === 1) {
                            tep_db_query("DELETE FROM " . TABLE_ORDERS_TOTAL . " WHERE orders_id = '" . $order_id . "' AND class = '" . $key . "'");

                            if (isset($info_array['output'])) {
                                foreach ($info_array['output'] as $key2) {
                                    tep_db_query("DELETE FROM " . TABLE_ORDERS_EXTRA_INFO . " WHERE orders_id='" . $order_id . "' AND orders_extra_info_key = '" . $key2 . "'");
                                }
                            }
                        }
                    }
                } else {
                    $alert_msg = 'No permission.';
                }
            } else {
                $alert_msg = 'Invalid Request.';
            }

            if ($alert_msg) {
                echo '<alert>' . $alert_msg . '</alert>';
            } else {
                // $messageStack->add_session(SUCCESS_ORDER_UPDATED, 'success');
                echo '<reload>1</reload>';
            }
            break;

        case 'resolve_escalate':
            $resolve_escalate_permission = tep_admin_files_actions(FILENAME_ORDERS, 'RESOLVE_ESCALATE');
            $order_escalated_query = "SELECT * FROM " . TABLE_ORDERS_EXTRA_INFO . " WHERE  orders_id = " . $_GET['oid'] . " AND orders_extra_info_key = 'escalate_created'";
            $order_escalated_result_sql = tep_db_query($order_escalated_query);

            if ($order_escalated_row = tep_db_fetch_array($order_escalated_result_sql)) {

                tep_db_query("DELETE FROM " . TABLE_ORDERS_EXTRA_INFO . " WHERE orders_id = " . $_GET['oid'] . " AND orders_extra_info_key = 'escalate_created'");
                $orders_extra_info = array('orders_id' => $_GET['oid'],
                    'orders_extra_info_key' => 'escalate_closed',
                    'orders_extra_info_value' => time());
                tep_db_perform(TABLE_ORDERS_EXTRA_INFO, $orders_extra_info);
                //update order remark for crew
                $comments = 'Escalated issue has been resolved.';
                $changed_by = $_SESSION['login_email_address'];

                $orders_status_history_update_sql = array('orders_id' => (int) $_GET['oid'],
                    'orders_status_id' => 0,
                    'date_added' => 'now()',
                    'customer_notified' => 0,
                    'comments' => $comments,
                    'comments_type' => '1',
                    'set_as_order_remarks' => 0,
                    'changed_by' => $changed_by
                );
                tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_update_sql);
                $comments = ['action' => 'escalate_resolve_crew'];
                $orders_status_history_update_sql = array('orders_id' => (int) $_GET['oid'],
                    'orders_status_id' => 0,
                    'date_added' => 'now()',
                    'customer_notified' => 1,
                    'comments' => json_encode($comments),
                    'comments_type' => '5',
                    'set_as_order_remarks' => 0,
                    'changed_by' => $changed_by
                );
                tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_update_sql);
                break;
            }
        case 'get_og_storecredit_op':
            $og_sc = "NA";
            $og_op = "NA";

            // store credit
            $sc_balance_array = ms_store_credit::getScBalance($customers_id);
            if ($sc_balance_array) {
                $og_sc = '<a href="' . CREW2_PATH . '/store-credit/index?report_type=1&customers_id=' . $customers_id . '&start_date=' . (date('Y-m-d', mktime(0, 0, 0, date("m"), date("d") - 7, date("Y")))) . '&end_date=' . date('Y-m-d') . '" target="_blank">' . $currencies->format($sc_balance_array['amount'], false, $sc_balance_array['currency']) . '</a> (' . (($sc_balance_array['status']) ? "ACTIVE" : "SUSPENDED") . ')';
            }

            include_once(DIR_WS_CLASSES . 'store_point.php');
            $sp_balance_array = store_point::get_current_points_balance($customers_id);
            if (isset($sp_balance_array['sp_amount'])) {
                $og_op = '<a href="' . tep_href_link(FILENAME_STORE_POINT, 'action=show_report&customer_id=' . $customers_id . '&start_date=' . (date('Y-m-d', mktime(0, 0, 0, date('m'), date('d') - 7, date('Y')))), 'SSL') . '" target="_blank">' . $sp_balance_array['sp_amount'] . '</a>';
            }

            echo '  <stat_result>
                        <og_storecredit><![CDATA[' . $og_sc . ']]></og_storecredit>
                        <og_op><![CDATA[' . $og_op . ']]></og_op>
                    </stat_result>';
            break;
        default:
            echo '<result>Unknown request!</result>';
            break;
    }
}

echo '</response>';

function getNonGstAmount($order_id) {

    $sc_product_id = 0;
    $sel_sql = "SELECT products_id FROM `products` WHERE `products_status` =1 AND `custom_products_type_id` =3";
    $sel_query = tep_db_query($sel_sql);
    if ($result = tep_db_fetch_array($sel_query)) {
        $sc_product_id = $result['products_id'];
    }


    $sub_total = 0;
    $_sel = "SELECT orders_products_id, products_id,final_price,products_quantity FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_id = " . $order_id;
    $_res = tep_db_query($_sel);

    while ($_row = tep_db_fetch_array($_res)) {
        if ($_row['products_id'] != $sc_product_id) {

            $sub_total += $_row['final_price'] * (int) $_row['products_quantity'];
        }
    }

    return $sub_total;
}

function addStoreCredit_product($order_id, $customer_id, $checkout_currency, $checkout_currency_value, $checkout_amout, $replace_flag = false) {
    global $currencies, $log_message;
    $payment_methods_id = 0;
    $final_price = 0;
    $qty = 0;

    if ((c2c_order::orderSiteID($order_id) == 5)) {
        $sel_sql = "SELECT products_id FROM `products` WHERE `custom_products_type_id` =3 AND `products_model` = 'g2g-store-credit'";
    } else {
        $sel_sql = "SELECT products_id FROM `products` WHERE `products_status` =1 AND `custom_products_type_id` =3";
    }

    $sel_query = tep_db_query($sel_sql);
    if ($result = tep_db_fetch_array($sel_query)) {
        $products_id = $result['products_id'];

        $products_select_sql = "SELECT p.products_id, p.products_model, pd.products_image, p.products_price, p.products_base_currency, p.products_bundle, 
                                    p.products_bundle_dynamic, p.products_weight, p.products_tax_class_id,p.custom_products_type_id 
                                FROM " . TABLE_PRODUCTS . " p
                                INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " pd 
                                    ON p.products_id=pd.products_id
                                WHERE p.products_id = '" . (int) $products_id . "'
                                    AND pd.language_id = '1'";
        $products_query = tep_db_query($products_select_sql);
        if ($_actual_item_info = tep_db_fetch_array($products_query)) {
            # Get Product Price Setting - convert the store credit product price based on the customer store credit currency
            # store credit base currency alwasy same with checkout currency
            // $product_base_currency = $checkout_currency;
            // get all transaction related to orders_id and activity
            if ((c2c_order::orderSiteID($order_id) == 5)) {
                $sc_balance_array = g2g_serverless::getScBalance($customer_id);
            } else {
                $sc_balance_array = ms_store_credit::getScBalance($customer_id);
            }

            $store_credit_currency_id = isset($sc_balance_array['currency']) ? $currencies->get_id_by_code($sc_balance_array['currency']) : $currencies->get_id_by_code($checkout_currency);
            // $store_credit_currency_code = isset($sc_balance_array['currency_id']) ? $currencies->get_code_by_id($sc_balance_array['currency_id']) : $checkout_currency;
            # product price
            $price = $checkout_amout; //$_actual_item_info['products_price'];
            # before discount
            // $normal_price = $price;
            // if ($store_credit_currency_code != $checkout_currency) {
            //     $price = $currencies->advance_currency_conversion($price, $store_credit_currency_code, $checkout_currency, false, 'buy');
            // }
            # after discount
            $final_price = $price;


            if (round($final_price, 2) > 0) {
                # Purchase QTY
                $qty = 1; //bcdiv($checkout_amout, $price, 0);

                $storage_price = $price / $checkout_currency_value; //$currencies->get_value($checkout_currency, 'sell');
                $storage_normal_price = $storage_price;
                $storage_final_price = $final_price / $checkout_currency_value; //$currencies->get_value($checkout_currency, 'sell');
                # retrieve parent cateogry id for `products_categories_id`
                $prod_cat_path = tep_get_product_path2($products_id, true);

                if (tep_not_null($prod_cat_path)) {
                    $prod_cat_path_array = explode('_', $prod_cat_path);
                    $_pci = $prod_cat_path_array[0];
                } else {
                    $_pci = 0;
                }

                # rebate
                # storage price ( USD )
                list($rebate_point, $rebate_point_extra, $rebate_point_formula) = $currencies->get_sc_rebate($customer_id, $products_id, $price, $checkout_currency, $qty, $payment_methods_id);

                # delete order product extra info - delivery_info
                if ($replace_flag) {
                    $log_message .= 'The following product has been deleted:<br>';

                    // G2G : update available-qty
                    if ((c2c_order::orderSiteID($order_id) == 5)) {
                        $order = new order($order_id);
                        $order->get_product_order_info(); // retrieve EXTRA_INFO

                        for ($i = 0, $n = sizeof($order->products); $i < $n; $i++) {
                            $_listing = tep_array_unserialize($order->products[$i]['extra_info']['listing']);

                            $c2c_obj = new c2c_order();
                            $c2c_obj->postOrderRules($_listing['c2c_products_listing_id'], 1, 5, ($order->products[$i]['qty'] - $order->products[$i]['delivered_qty']), $order_id, 'orders_xmlhttp');
                        }
                    }

                    $_sel = "SELECT orders_products_id, products_id, products_categories_id FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_id = " . $order_id;
                    $_res = tep_db_query($_sel);
                    while ($_row = tep_db_fetch_array($_res)) {
                        $op_id = $_row['orders_products_id'];
                        $log_message .= ' [' . tep_output_generated_category_path_sq(tep_get_actual_product_cat_id($_row['products_id'])) . ']<br>';

                        tep_db_query("DELETE FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_products_id = '" . $op_id . "'");
                        tep_db_query("DELETE FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " WHERE orders_products_id = '" . $op_id . "' AND orders_products_extra_info_key IN ('OP_EXTRA', 'OP_FORMULA')");
                        tep_db_query("DELETE FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " WHERE orders_products_id = '" . $op_id . "' AND orders_custom_products_key IN ('store_credit_currency', 'store_credit_promotion_percentage')");
                    }

                    $log_message .= '<br>';
                }

                $log_message .= 'The following product has been added:<br>';
                $log_message .= '[' . tep_output_generated_category_path_sq(tep_get_actual_product_cat_id($products_id)) . ']';

                # Add / Change Order Product
                $m_attr = array(
                    'orders_id' => $order_id,
                    'products_id' => $products_id,
                    'products_model' => $_actual_item_info['products_model'],
                    'products_name' => tep_get_products_name($products_id, 1),
                    'orders_products_store_price' => $storage_normal_price,
                    'products_price' => $storage_price,
                    'final_price' => $storage_final_price,
                    'op_rebate' => ($rebate_point + $rebate_point_extra),
                    'op_rebate_delivered' => 0,
                    'products_quantity' => (int) $qty,
                    'products_pre_order' => (in_array($products_id, array()) ? 1 : 0),
                    'custom_products_type_id' => $_actual_item_info['custom_products_type_id'],
                    'products_categories_id' => $_pci,
                );

                tep_db_perform(TABLE_ORDERS_PRODUCTS, $m_attr);
                $opid = tep_db_insert_id();
                unset($m_attr);

                #Extra OP
                if ($rebate_point_extra > 0) {
                    $rebate_point_formula = $rebate_point_formula . '<br /><br />Extra OP: ' . $rebate_point_extra;

                    $data_sql_array = array(
                        'orders_products_id' => $opid,
                        'orders_products_extra_info_key' => 'OP_EXTRA',
                        'orders_products_extra_info_value' => $rebate_point_extra
                    );

                    tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $data_sql_array);
                }

                #OP
                $data_sql_array = array(
                    'orders_products_id' => $opid,
                    'orders_products_extra_info_key' => 'OP_FORMULA',
                    'orders_products_extra_info_value' => $rebate_point_formula
                );

                tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $data_sql_array);

                # Store Credit
                $m_attr = array(
                    'products_id' => $products_id,
                    'orders_products_id' => $opid,
                    'orders_custom_products_key' => 'store_credit_currency',
                    'orders_custom_products_value' => $store_credit_currency_id,
                    'orders_custom_products_number' => 0
                );

                tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $m_attr);

                // get all transaction related to orders_id and activity
                # Store Credit promotion
                if ((c2c_order::orderSiteID($order_id) == 5)) {
                    $sc_promotion_percentage = g2g_serverless::getScPromotionPercentage($customer_id);
                } else {
                    $sc_promotion_percentage = ms_store_credit::getScPromotionPercentage($customer_id);
                }

                if ($sc_promotion_percentage > 0) {
                    $store_credit_currency_array = array(
                        'products_id' => $products_id,
                        'orders_products_id' => $opid,
                        'orders_custom_products_key' => 'store_credit_promotion_percentage',
                        'orders_custom_products_value' => $sc_promotion_percentage,
                        'orders_custom_products_number' => 0
                    );

                    tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $store_credit_currency_array);
                }
            }
        }
    }

    return array($final_price, $qty);
}

function generateTagSelectionOptions($status, $order_str, $whole_list = false, $apply_tag_sec_only = false) {
    global $languages_id;
    $order_ids_array = tep_not_null($order_str) ? explode(',', $order_str) : array();

    echo '<selection>';

    $order_status_id_select_sql = "	SELECT orders_status_id
									FROM " . TABLE_ORDERS_STATUS . "
									WHERE " . (is_numeric($status) ? "orders_status_id = '" . (int) $status . "'" : "orders_status_name = '" . tep_db_input($status) . "'") . " AND language_id = '" . $languages_id . "';";
    $order_status_id_result_sql = tep_db_query($order_status_id_select_sql);
    if ($order_status_id_row = tep_db_fetch_array($order_status_id_result_sql)) {
        if (!$apply_tag_sec_only) {
            echo "<option index=''><![CDATA[Order Lists Options ...]]></option>";
            echo "<option index='rd'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as read]]></option>";
            echo "<option index='ur'><![CDATA[&nbsp;&nbsp;&nbsp;Mark as unread]]></option>";
        }
        echo "<option index='' " . (!$apply_tag_sec_only ? "disabled='1'" : '') . "><![CDATA[Apply tag:]]></option>";

        $mirror_for_delete_tag_str = '';

        $order_tag_select_sql = "	SELECT orders_tag_id, orders_tag_name
									FROM " . TABLE_ORDERS_TAG . "
									WHERE FIND_IN_SET('" . $order_status_id_row["orders_status_id"] . "', orders_tag_status_ids) AND filename = '" . FILENAME_STATS_ORDERS_TRACKING . "' ORDER BY orders_tag_name;";
        $order_tag_result_sql = tep_db_query($order_tag_select_sql);
        while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
            echo "<option index='" . 'otag_' . $order_tag_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . strip_tags($order_tag_row["orders_tag_name"]) . "]]></option>";
            if ($whole_list == true) {
                $mirror_for_delete_tag_str .= "<option index='" . 'rmtag_' . $order_tag_row["orders_tag_id"] . "'><![CDATA[&nbsp;&nbsp;&nbsp;" . strip_tags($order_tag_row["orders_tag_name"]) . "]]></option>";
            }
        }

        if (!$apply_tag_sec_only) {
            echo "<option index='nt'><![CDATA[&nbsp;&nbsp;&nbsp;New tag ...]]></option>";

            if ($whole_list == true && tep_not_null($mirror_for_delete_tag_str)) {
                echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
                echo $mirror_for_delete_tag_str;
            } else {
                // select the common tags among those selected orders
                if (count($order_ids_array)) {
                    $orders_tag_remove_select_sql = "SELECT DISTINCT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_ORDERS . " AS o WHERE orders_id IN (" . implode(',', $order_ids_array) . ") AND FIND_IN_SET(otag.orders_tag_id, o.orders_tag_ids) AND filename='" . FILENAME_STATS_ORDERS_TRACKING . "' ORDER BY orders_tag_name; ";
                    $orders_tag_remove_result_sql = tep_db_query($orders_tag_remove_select_sql);
                    if (tep_db_num_rows($orders_tag_remove_result_sql) > 0)
                        echo '<option index="" disabled="1"><![CDATA[Remove tag:]]></option>';
                    while ($orders_tag_remove_row = tep_db_fetch_array($orders_tag_remove_result_sql)) {
                        echo '<option index="rmtag_' . $orders_tag_remove_row['orders_tag_id'] . '"><![CDATA[&nbsp;&nbsp;&nbsp;' . strip_tags($orders_tag_remove_row['orders_tag_name']) . ']]></option>';
                    }
                }
            }
        }
    }
    echo "</selection>";
}

function generateTagString($order_ids_array) {
    echo "<tag_details>";
    for ($i = 0; $i < count($order_ids_array); $i++) {
        $orders_tag_select_sql = "SELECT otag.orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_ORDERS . " AS o WHERE o.orders_id = '" . (int) $order_ids_array[$i] . "' AND FIND_IN_SET(otag.orders_tag_id, o.orders_tag_ids) AND filename='" . FILENAME_STATS_ORDERS_TRACKING . "';";
        $orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
        $tags_str = '';
        while ($orders_tag_row = tep_db_fetch_array($orders_tag_result_sql)) {
            $tags_str .= $orders_tag_row["orders_tag_name"] . ', ';
        }
        if (substr($tags_str, -2) == ', ')
            $tags_str = substr($tags_str, 0, -2);
        echo "<order_tags order_id='" . (int) $order_ids_array[$i] . "'><![CDATA[" . $tags_str . "]]></order_tags>";
    }
    echo "</tag_details>";
}

function generateReadModeString($order_ids_array) {
    echo "<read_mode>";
    for ($i = 0; $i < count($order_ids_array); $i++) {
        $orders_read_mode_select_sql = "SELECT orders_read_mode FROM " . TABLE_ORDERS . " WHERE orders_id = '" . (int) $order_ids_array[$i] . "' ;";
        $orders_read_mode_result_sql = tep_db_query($orders_read_mode_select_sql);
        while ($orders_read_mode_row = tep_db_fetch_array($orders_read_mode_result_sql)) {
            echo "<order_mode order_id='" . (int) $order_ids_array[$i] . "'><![CDATA[" . $orders_read_mode_row["orders_read_mode"] . "]]></order_mode>";
        }
    }
    echo "</read_mode>";
}

?>