<?php

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'out_of_stock_rule.php');

$out_of_stock_rule_object = new out_of_stock_rule($login_id, $login_email_address);
$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

switch ($subaction) {
    case "insert_rule":
        $subaction_res = $out_of_stock_rule_object->insert_rule($_REQUEST, $messageStack);
        tep_redirect(tep_href_link(FILENAME_OUT_OF_STOCK_RULE, tep_get_all_get_params(array('action', 'subaction'))));

        break;
    case "update_rule":
        $subaction_res_return_rule_id = $out_of_stock_rule_object->update_rule($_REQUEST, $messageStack);
        
        if ($subaction_res_return_rule_id) {
            require(DIR_WS_CLASSES . 'purchase_control_tool.php');
            $purchase_control_tool_object = new purchase_control_tool();
            $purchase_control_tool_object->recalculate_by_rule_id($subaction_res_return_rule_id, $messageStack);
        }
        tep_redirect(tep_href_link(FILENAME_OUT_OF_STOCK_RULE, tep_get_all_get_params(array('action', 'subaction'))));

        break;
    default:
        // Nothing to perform
        break;
}

switch ($action) {
    case "new_rule":
        $header_title = HEADER_FORM_OUT_OF_STOCK_ADD_TITLE;
        $form_content = $out_of_stock_rule_object->add_rule(FILENAME_OUT_OF_STOCK_RULE);

        break;
    case "edit_rule":
        $header_title = HEADER_FORM_OUT_OF_STOCK_EDIT_TITLE;
        $form_content = $out_of_stock_rule_object->edit_rule(FILENAME_OUT_OF_STOCK_RULE, $_REQUEST['id']);

        break;
    case "delete_rule":
        $subaction_res = $out_of_stock_rule_object->delete_rule($_REQUEST, $messageStack);
        tep_redirect(tep_href_link(FILENAME_OUT_OF_STOCK_RULE, tep_get_all_get_params(array('action', 'subaction'))));

        break;
    case "show_monitor_page":
        $header_title = HEADER_SHOW_MONITOR_PAGE;
        $form_content = $out_of_stock_rule_object->show_monitor_page(FILENAME_OUT_OF_STOCK_RULE);

        break;
    case "remove_product_from_out_of_stock_rule":
        $subaction_res = $out_of_stock_rule_object->remove_product_from_out_of_stock_rule($_REQUEST, $messageStack);
        tep_redirect(tep_href_link(FILENAME_OUT_OF_STOCK_RULE, 'action=show_rules_supported_page&rules_id=' . $_REQUEST['rules_id']));

        break;
    case "show_rules_supported_page":
        $header_title = HEADER_SHOW_RULES_SUPPORTED_PAGE;
        $form_content = $out_of_stock_rule_object->show_rules_supported_page(FILENAME_OUT_OF_STOCK_RULE, $_REQUEST['rules_id']);

        break;
    default:
        $header_title = HEADER_FORM_OUT_OF_STOCK_RULE_TITLE;
        $form_content = $out_of_stock_rule_object->show_rules_list(FILENAME_OUT_OF_STOCK_RULE);

        break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="includes/javascript/jquery.js"></script>
        <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
        <script language="javascript" src="includes/javascript/jquery.form.js"></script>
        <script language="javascript" src="includes/javascript/customer_xmlhttp.js"></script>
        <script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/out_of_stock_rule.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <!-- header //-->
        <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?= $header_title ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                    <tr>
                                        <td width="100%" valign="top"><?php echo $form_content; ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
</html>