<?
/*
  	$Id: page_view_module.php,v 1.12 2010/09/22 08:41:27 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

	require('includes/application_top.php');
	
	$read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');
	
	include_once(DIR_WS_CLASSES . "page_view_module.php");
	if (isset($_REQUEST['action'])) {
		switch($_REQUEST['action']) {
			case 'batch_delete':
				if (isset($_REQUEST['chk_ip']) && count($_REQUEST['chk_ip'])) {
					$ip_list_array = array();
					foreach ($_REQUEST['chk_ip'] as $ip_list_id) {
						if ((int)$ip_list_id>0) $ip_list_array[] = (int)$ip_list_id;
					}
					if (count($ip_list_array)>0) {
						$delete_ip_list_sql = "	DELETE FROM " . TABLE_PAGE_VIEW_IP_LIST . " 
												WHERE page_view_ip_list_id IN ('".implode("','",$ip_list_array)."')";
						tep_db_query($delete_ip_list_sql, 'db_link');
						$messageStack->add_session('IP list\'s deleted.', 'success');
					} else {
						$messageStack->add_session('Nothing been deleted.', 'error');
					}
				} else {
					$messageStack->add_session('Nothing been deleted.', 'error');
				}
				tep_redirect(tep_href_link(FILENAME_PAGE_VIEW_MODULE));
				break;
				break;
			case 'add_ip':
				if (isset($_REQUEST['list']) && tep_not_null($_REQUEST['list'])) {
					if (isset($_REQUEST['txt_ip']) && tep_check_is_ip($_REQUEST['txt_ip'], $request_ip_array)) {
						$ip_in_binary = tep_ip_in_binary_form($request_ip_array['ip']);
						if ((int)$request_ip_array['subnet']>0) {
							$ip_in_binary = substr($ip_in_binary,0,(int)$request_ip_array['subnet']);
						}
						
						$input_ip_str = $request_ip_array['ip'].(isset($request_ip_array['subnet']) && tep_not_null($request_ip_array['subnet'])?'/'.$request_ip_array['subnet']:'');
						
						$check_ip_select_sql = "	SELECT page_view_ip_list_id
													FROM " . TABLE_PAGE_VIEW_IP_LIST . " 
													WHERE page_view_ip_list_ip = '".tep_db_input($input_ip_str)."'";	
						$check_ip_result_sql = tep_db_query($check_ip_select_sql, 'read_db_link');
						if (!tep_db_num_rows($check_ip_result_sql)) {
							$page_view_ip_list_data_sql = array('page_view_ip_list_ip'=>tep_db_prepare_input($input_ip_str),
																'page_view_ip_list_ip_binary'=>tep_db_prepare_input($ip_in_binary),
																'page_view_ip_list_ip_subnet'=>tep_db_prepare_input((tep_not_null($request_ip_array['subnet'])?$request_ip_array['subnet']:'32')),
																'page_view_ip_list_last_update'=>'now()',
																'page_view_ip_list_last_url'=>'',
																'page_view_ip_list_mode'=>'t',
																'page_view_ip_list_remark'=>tep_db_prepare_input((isset($_REQUEST['txt_remark']) && tep_not_null($_REQUEST['txt_remark']) ? $_REQUEST['txt_remark'] : '[Manual Add]')));
							if ($_REQUEST['list'] == 'b') {
								$page_view_ip_list_data_sql['page_view_ip_list_list'] = 'b';
							} else {
								$page_view_ip_list_data_sql['page_view_ip_list_list'] = 'w';
							}
							tep_db_perform(TABLE_PAGE_VIEW_IP_LIST, $page_view_ip_list_data_sql);
							$messageStack->add_session('IP Address, '.tep_db_input($input_ip_str).' added.', 'success');
						} else {
							$messageStack->add_session('IP Address, '.tep_db_input($input_ip_str).' is existing.', 'error');
						}
					} else {
						$messageStack->add_session('IP Address, '.tep_db_input($_REQUEST['txt_ip']).' invalid.', 'error');
					}
				}
				tep_redirect(tep_href_link(FILENAME_PAGE_VIEW_MODULE));
				break;
			case 'delete':
				if (isset($_REQUEST['lid']) && (int)$_REQUEST['lid'] > 0) {
					$check_ip_select_sql = "	SELECT page_view_ip_list_ip
												FROM " . TABLE_PAGE_VIEW_IP_LIST . " 
												WHERE page_view_ip_list_id = '".(int)$_REQUEST['lid']."'";
					$check_ip_result_sql = tep_db_query($check_ip_select_sql, 'read_db_link');
					if ($check_ip_row = tep_db_fetch_array($check_ip_result_sql)) {
						$delete_ip_sql = "	DELETE FROM " . TABLE_PAGE_VIEW_IP_LIST . "
											WHERE page_view_ip_list_id = '".(int)$_REQUEST['lid']."'";
						tep_db_query($delete_ip_sql, 'db_link');
						
						$delete_ip_tags_stats_sql = "	DELETE FROM " . TABLE_IP_TAGS_STATS . "
														WHERE page_view_ip_list_id = '".(int)$_REQUEST['lid']."'";
						tep_db_query($delete_ip_tags_stats_sql, 'db_link');
						
						$delete_ip_list_history_sql = "	DELETE FROM " . TABLE_IP_LIST_HISTORY . "
														WHERE page_view_ip_list_id = '".(int)$_REQUEST['lid']."'";
						tep_db_query($delete_ip_list_history_sql, 'db_link');
						$messageStack->add_session('IP Address, '.$check_ip_row['page_view_ip_list_ip'].' deleted.', 'success');
					}
				}
				tep_redirect(tep_href_link(FILENAME_PAGE_VIEW_MODULE));
				break;
			case 'mode':
				if (isset($_REQUEST['mode']) && tep_not_null($_REQUEST['mode']) && isset($_REQUEST['lid']) && (int)$_REQUEST['lid']>0) {
					if (in_array(tep_db_input($_REQUEST['mode']), array('b','t'))) {
						
						$mode = tep_db_input($_REQUEST['mode']);
						$lid = (int)$_REQUEST['lid'];
						
						$mode_array = array("b"=>"Blocking",
											"t"=>"Tracking");
						$check_ip_select_sql = "	SELECT page_view_ip_list_ip
													FROM " . TABLE_PAGE_VIEW_IP_LIST . " 
													WHERE page_view_ip_list_id = '".$lid."'";
						$check_ip_result_sql = tep_db_query($check_ip_select_sql, 'read_db_link');
						if ($check_ip_row = tep_db_fetch_array($check_ip_result_sql)) {
							$update_mode_data_sql = array("page_view_ip_list_mode"=>tep_db_prepare_input($mode));
							if ($mode=='b') {
								$update_mode_data_sql['page_view_ip_list_last_blocked'] = 'now()';
							}
							tep_db_perform(TABLE_PAGE_VIEW_IP_LIST, $update_mode_data_sql, 'update', "page_view_ip_list_id = '".$lid."'");
							$messageStack->add_session('IP Address, '.$check_ip_row['page_view_ip_list_ip'].'\'s mode been changed to \''.$mode_array[$mode].'\'.', 'success');
						}
					}
				}
				tep_redirect(tep_href_link(FILENAME_PAGE_VIEW_MODULE));
				break;
			case 'move_list':
				if (isset($_REQUEST['sel_list']) && tep_not_null($_REQUEST['sel_list']) && isset($_REQUEST['chk_ip'])  && in_array(tep_db_input($_REQUEST['sel_list']), array('w','c','b')) && count($_REQUEST['chk_ip'])) {
					$ip_list_array = array();
					foreach ($_REQUEST['chk_ip'] as $ip_list_id) {
						if ((int)$ip_list_id>0) $ip_list_array[] = (int)$ip_list_id;
					}
					if (count($ip_list_array)>0) {
						$update_ip_list_sql = "	UPDATE " . TABLE_PAGE_VIEW_IP_LIST . " 
												SET page_view_ip_list_list = '".tep_db_input($_REQUEST['sel_list'])."',
													page_view_ip_list_mode = 't'
												WHERE page_view_ip_list_id IN ('".implode("','",$ip_list_array)."')";
						tep_db_query($update_ip_list_sql, 'db_link');
						$messageStack->add_session('IP list\'s updated.', 'success');
					} else {
						$messageStack->add_session('Nothing been updated.', 'error');
					}
				} else {
					$messageStack->add_session('Nothing been updated.', 'error');
				}
				tep_redirect(tep_href_link(FILENAME_PAGE_VIEW_MODULE));
				break;
			default:
				break;
		}
	}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
	<script language="javascript" src="includes/javascript/jquery.form.js"></script>
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
	<div id="div_information" style="width:900px;padding:5px;display:none;position:absolute;top:50px;left:170px;background:white;border:4px solid grey;"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading">
            							<?=HEADING_TITLE?>
<?
	$page_view_module_obj = new page_view_module();
	$tags_names_array = $page_view_module_obj->get_tags_names();
	
/*	$page_view_module_obj->set_tag_configurations_mode();
	$tag_configurations_mode_array = $page_view_module_obj->get_tag_configurations_mode();*/
?>
            							<table border="0" width="100%" cellspacing="0" cellpadding="0">
            								<tr>
			            						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			            					</tr>
			          						<tr>
			            						<td class="pageHeading">
			            					</tr>
<?
	$list_array = array('w','c','b');
	foreach ($list_array as $list_loop) {
?>
			          						<tr>
			            						<td class="pageHeading">
<?
		switch ($list_loop) {
			case 'w':
				$table_heading_title = TABLE_HEADING_WHITE_LIST;
				break;
			case 'b':
				$table_heading_title = TABLE_HEADING_BLACK_LIST;
				break;
			default:
				$table_heading_title = TABLE_HEADING_CURRENT_LIST;
				break;
		}
?>
													<?=$table_heading_title?>
			            							<table border="0" width="<?=(1250 + (count($tags_names_array) * 40))?>px" cellspacing="0" cellpadding="0">
			            								<tr>
						            						<td>
						            							<?=tep_draw_form('list_frm', FILENAME_PAGE_VIEW_MODULE, 'list='.$list_loop, 'post', ' id="list_frm_'.$list_loop.'" ')?>
						            							<?=tep_draw_hidden_field('action', '', ' id="hidden_action_'.$list_loop.'" ')?>
						            							<?=tep_draw_hidden_field('txt_remark', '', ' id="hidden_remark_'.$list_loop.'" ')?>
						            							<table border="0" width="100%" cellspacing="2" cellpadding="0">
																	<tr valign="top">
								                						<td class="reportBoxHeading" width="100px" align="center"><?=TABLE_HEADING_IP?></td>
								                						<td class="reportBoxHeading" width="100px" align="center"><?=TABLE_HEADING_NSLOOKUP?></td>
								                						<td class="reportBoxHeading" width="*%" align="center"><?=TABLE_HEADING_REMARK?></td>
<? foreach ($tags_names_array as $tags_name => $tags_text) {
		if ($page_view_module_obj->tags_configurations[$tags_name]['mode']=='o') continue;
?>
		
																		<td class="reportBoxHeading" width="40px" align="center"><?=$tags_text?></td>
<? } ?>
																		<td class="reportBoxHeading" width="130px" align="center"><?=TABLE_HEADING_LAST_VISIT?></td>
																		<td class="reportBoxHeading" width="170px" align="center"><?=TABLE_HEADING_LAST_VISITOR?></td>
<? if ($list_loop == 'b') { ?>											<td class="reportBoxHeading" width="130px" align="center"><?=TABLE_HEADING_LAST_BLOCKED?></td>  <? } ?>
																		<td class="reportBoxHeading" width="145px" align="center"><?=TABLE_HEADING_ACTION?></td>
																		<td class="reportBoxHeading" width="10px" align="center"><?=tep_draw_checkbox_field('chk_all', '', false, '', ' id="chk_all_'.$list_loop.'" onclick="toggle_chk_ip(\''.$list_loop.'\')" ')?></td>
									            					</tr>
<?
	$page_view_ip_list_select_sql = "	SELECT pvil.page_view_ip_list_id, pvil.page_view_ip_list_ip, pvil.page_view_ip_list_ip_subnet, 
											DATE_FORMAT(pvil.page_view_ip_list_last_update, '%Y-%m-%d %H:%i') as page_view_ip_list_last_update, 
											pvil.page_view_ip_list_last_url, pvil.page_view_ip_list_mode, pvil.page_view_ip_list_last_blocked, 
											pvil.page_view_ip_list_list, pvil.page_view_ip_list_remark, pvil.page_view_ip_list_host 
										FROM " . TABLE_PAGE_VIEW_IP_LIST . " as pvil 
										WHERE page_view_ip_list_list = '".$list_loop."' ";
	if ($list_loop == 'b') {
		$page_view_ip_list_select_sql .= "	ORDER BY page_view_ip_list_last_blocked DESC";
	} else {
		//$page_view_ip_list_select_sql .= "	ORDER BY page_view_ip_list_ip";
	}
	$page_view_ip_list_result_sql = tep_db_query($page_view_ip_list_select_sql, 'read_db_link');
	$page_view_ip_list_array = array();
	while ($page_view_ip_list_row = tep_db_fetch_array($page_view_ip_list_result_sql)) {
		$page_view_ip_list_array[$page_view_ip_list_row['page_view_ip_list_id']] = array(	'page_view_ip_list_ip'=> $page_view_ip_list_row['page_view_ip_list_ip'],
																							'page_view_ip_list_ip_subnet'=> $page_view_ip_list_row['page_view_ip_list_ip_subnet'],
																							'page_view_ip_list_host'=> $page_view_ip_list_row['page_view_ip_list_host'],
																							'page_view_ip_list_last_blocked'=> $page_view_ip_list_row['page_view_ip_list_last_blocked'],
																							'page_view_ip_list_last_update'=> $page_view_ip_list_row['page_view_ip_list_last_update'],
																							'page_view_ip_list_last_url'=> $page_view_ip_list_row['page_view_ip_list_last_url'],
																							'page_view_ip_list_mode'=> $page_view_ip_list_row['page_view_ip_list_mode'],
																							'page_view_ip_list_list'=> $page_view_ip_list_row['page_view_ip_list_list'],
																							'page_view_ip_list_remark'=> $page_view_ip_list_row['page_view_ip_list_remark']);
	}
	if (count($page_view_ip_list_array)) {
		$page_view_ip_list_id_str = array_keys($page_view_ip_list_array);
		$page_view_ip_list_id_str = implode("','",$page_view_ip_list_id_str);
		$ip_tags_stats_select_sql = "	SELECT page_view_ip_list_id, script_tags_name, ip_tags_stats_counter, 
											DATE_FORMAT(ip_tags_stats_last_visit, '%Y-%m-%d %H:%i') as ip_tags_stats_last_visit
										FROM " . TABLE_IP_TAGS_STATS . " 
										WHERE page_view_ip_list_id IN ('".$page_view_ip_list_id_str."')";
		$ip_tags_stats_result_sql = tep_db_query($ip_tags_stats_select_sql, 'read_db_link');
		$ip_tags_stats_array = array();
		while ($ip_tags_stats_row = tep_db_fetch_array($ip_tags_stats_result_sql)) {
			$ip_tags_stats_array[$ip_tags_stats_row['page_view_ip_list_id']][$ip_tags_stats_row['script_tags_name']] = array(	'ip_tags_stats_counter'=> $ip_tags_stats_row['ip_tags_stats_counter'],
																																'ip_tags_stats_last_visit'=> $ip_tags_stats_row['ip_tags_stats_last_visit']);
		}
		
		$ip_history_select_sql = "	SELECT c.customers_email_address, ilh.page_view_ip_list_id, ilh.customers_id  
									FROM " . TABLE_IP_LIST_HISTORY . " as ilh 
									LEFT JOIN " . TABLE_CUSTOMERS . " as c
										ON c.customers_id = ilh.customers_id
									WHERE ilh.page_view_ip_list_id IN ('".$page_view_ip_list_id_str."')
									GROUP BY ilh.page_view_ip_list_id
									ORDER BY ilh.ip_list_history_datatime DESC";
		$ip_history_result_sql = tep_db_query($ip_history_select_sql, 'read_db_link');
		$ip_history_array = array();
		while ($ip_history_row = tep_db_fetch_array($ip_history_result_sql)) {
			$ip_history_array[$ip_history_row['page_view_ip_list_id']] = $ip_history_row['customers_email_address'];
		}
		
		$row_count = 0;
		foreach ($page_view_ip_list_array as $page_view_ip_list_id_loop => $page_view_ip_list_data_loop) {
			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
			
			$ip_str = $page_view_ip_list_data_loop['page_view_ip_list_ip'];
			if (preg_match("/[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}/",$ip_str, $ip_matched)) {
				$ip_str = $ip_matched[0];
			}
			
			echo 	"<tr id='main_".$page_view_ip_list_id_loop."' class='".$row_style." ".($page_view_ip_list_data_loop['page_view_ip_list_mode']=='b'?" ip_blocked":"")."' onmouseover=\"showOverEffect(this, 'ordersListingRowOver', 'sub_".$page_view_ip_list_id_loop."##".$status."_sub2_".$page_view_ip_list_id_loop."')\" onmouseout=\"showOutEffect(this, '".$row_style."', '".$status."_sub_".$page_view_ip_list_id_loop."##".$status."_sub2_".$page_view_ip_list_id_loop."')\" onclick=\"showClicked(this, '".$row_style."', '".$status."_sub_".$page_view_ip_list_id_loop."##".$status."_sub2_".$page_view_ip_list_id_loop."')\">";
			echo "		<td class='dataTableContent'>&nbsp;".tep_show_ip($ip_str, $page_view_ip_list_data_loop['page_view_ip_list_ip'])."</td>";
			if ((int)$page_view_ip_list_data_loop['page_view_ip_list_ip_subnet']==32 ) {
				if (tep_not_null($page_view_ip_list_data_loop['page_view_ip_list_host'])) {
					$linux_host_result_str = $page_view_ip_list_data_loop['page_view_ip_list_host'];
				} else {
					$linux_host_result_str = exec("host " . $page_view_ip_list_data_loop['page_view_ip_list_ip']);
					$linux_host_result_str = trim($linux_host_result_str);
					$linux_host_result_array = preg_split("/ /u",$linux_host_result_str);
					$linux_host_result_str = $linux_host_result_array[count($linux_host_result_array)-1];
					
					$update_host_data_sql = array("page_view_ip_list_host"=> tep_db_prepare_input($linux_host_result_str));
					tep_db_perform(TABLE_PAGE_VIEW_IP_LIST, $update_host_data_sql, 'update', " page_view_ip_list_id = '".$page_view_ip_list_id_loop."'");
				}
			} else {
				$linux_host_result_str = 'N/A';
			}
			echo "		<td class='dataTableContent'>&nbsp;".$linux_host_result_str."</td>";
			
			$remark_str = '';
			if (preg_match('/(.+)(\<br)/',nl2br($page_view_ip_list_data_loop['page_view_ip_list_remark']), $matched_str)) {
				$remark_str = $matched_str[1] . "...[<a class='jq_ogm_remark' ip_list_ip='".$page_view_ip_list_data_loop['page_view_ip_list_ip']."' ip_list_id='".$page_view_ip_list_id_loop."' onclick='load_remark(".$page_view_ip_list_id_loop.")' style='cursor:pointer;text-decoration:underline;'>more</a>]";
			} else if (strlen($page_view_ip_list_data_loop['page_view_ip_list_remark'])>50) {
				$remark_str = substr($page_view_ip_list_data_loop['page_view_ip_list_remark'],0,50)."...[<a class='jq_ogm_remark'  ip_list_ip='".$page_view_ip_list_data_loop['page_view_ip_list_ip']."' ip_list_id='".$page_view_ip_list_id_loop."' onclick='load_remark(".$page_view_ip_list_id_loop.")' style='cursor:pointer;text-decoration:underline;'>more</a>]";
			} else {
				$remark_str = $page_view_ip_list_data_loop['page_view_ip_list_remark'];
			}
			
			echo "		<td class='dataTableContent'>&nbsp;<a class='jq_ogm_remark' onclick='load_remark(".$page_view_ip_list_id_loop.")' ip_list_ip='".$page_view_ip_list_data_loop['page_view_ip_list_ip']."' ip_list_id='".$page_view_ip_list_id_loop."' style='cursor:pointer;text-decoration:underline;'>".tep_image('images/icons/edit.gif', 'remark', 10, 10)."</a>&nbsp;<span id='span_remark_".$page_view_ip_list_id_loop."'>".$remark_str."</span></td>";
			foreach ($tags_names_array as $tags_name => $tags_text) {
				if ($page_view_module_obj->tags_configurations[$tags_name]['mode']=='o') continue;
				if (isset($ip_tags_stats_array[$page_view_ip_list_id_loop][$tags_name]['ip_tags_stats_counter'])) {
					echo "	<td class='dataTableContent' align='center' title='Last Access: ".$ip_tags_stats_array[$page_view_ip_list_id_loop][$tags_name]['ip_tags_stats_last_visit']."'><a class=\"jq_ogm_tooltips\" onclick=\"load_tooltips(".$page_view_ip_list_id_loop.", '".$tags_name."');\" ip_list_ip=\"".$page_view_ip_list_data_loop['page_view_ip_list_ip']."\" ip_list_id=\"".$page_view_ip_list_id_loop."\" tag=\"".$tags_name."\" style=\"text-decoration:underline;cursor:pointer;\">".$ip_tags_stats_array[$page_view_ip_list_id_loop][$tags_name]['ip_tags_stats_counter']."</a></td>";
				} else {
					echo "	<td class='dataTableContent' align='center'>0</td>";
				}
			}
			echo "		<td class='dataTableContent' align='center'>".$page_view_ip_list_data_loop['page_view_ip_list_last_update']."</td>
						<td class='dataTableContent' align='center'>".(isset($ip_history_array[$page_view_ip_list_id_loop]) && tep_not_null($ip_history_array[$page_view_ip_list_id_loop])?$ip_history_array[$page_view_ip_list_id_loop]:'Anonymous')."</td>";
			if ($list_loop == 'b') {
				echo "	<td class='dataTableContent' align='center'>".(tep_not_null($page_view_ip_list_data_loop['page_view_ip_list_last_blocked']) && $page_view_ip_list_data_loop['page_view_ip_list_last_blocked'] != '0000-00-00 00:00:00'?$page_view_ip_list_data_loop['page_view_ip_list_last_blocked']:'&nbsp;')."</td>";
			}
			echo "		<td class='dataTableContent' align='center'>";
			if ($list_loop=='b') {
				echo tep_draw_pull_down_menu('sel_mode', array(array('id'=>'t','text'=>'Set as Tracking'),array('id'=>'b','text'=>'Set as Blocking')), $page_view_ip_list_data_loop['page_view_ip_list_mode'], ' id="sel_mode_'.$list_loop.'" onchange="document.getElementById(\'hidden_action_'.$list_loop.'\').value=\'change_mode\';confirm_mode_form(\''.$page_view_ip_list_id_loop.'\',this.value,\''.$page_view_ip_list_data_loop['page_view_ip_list_ip'].'\')" ')."&nbsp;&nbsp";
			}
			echo "			<a href='javascript:delete_ip(\"".$page_view_ip_list_id_loop."\",\"".$page_view_ip_list_data_loop['page_view_ip_list_ip']."\")'><img src='images/icons/delete.gif' style='border:0px;'></a></td>
						<td class='dataTableContent' align='center'>".tep_draw_checkbox_field('chk_ip[]', $page_view_ip_list_id_loop, false, '', ' class="chk_ip_'.$list_loop.'" onclick="reset_check_all(\''.$list_loop.'\')"')."</td>
					</tr>";
			$row_count++;
		}
		echo "		<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '3')?></td>
					</tr>";
	}
	$sel_list_array = array();
	$sel_list_array[] = array(	'id'=>'', 'text'=>'[Please Select]');
	switch ($list_loop) {
		case 'w':
			$sel_list_array[] = array(	'id'=>'c', 'text'=>'Move to Current List');
			$sel_list_array[] = array(	'id'=>'b', 'text'=>'Move to Watch List');
			break;
		case 'b':
			$sel_list_array[] = array(	'id'=>'w', 'text'=>'Move to White List');
			$sel_list_array[] = array(	'id'=>'c', 'text'=>'Move to Current List');				
			break;
		default:
			$sel_list_array[] = array(	'id'=>'w', 'text'=>'Move to White List');
			$sel_list_array[] = array(	'id'=>'b', 'text'=>'Move to Watch List');
			break;
	}
	$sel_list_array[] = array(	'text'=>'Action', 'type'=>'optgroup');
	$sel_list_array[] = array(	'id'=>'batch_delete', 'text'=>'Delete');
	echo "			<tr>
						<td class='dataTableContent' align='left' colspan='3'>";
		if ($list_loop == 'w' || $list_loop == 'b') {
   							echo tep_draw_input_field('txt_ip', '', ' id="txt_'.$list_loop.'_ip" size="20" class="txt_ip" maxlength="18" ', false, 'text', false) . '&nbsp;&nbsp;';
   							echo tep_button('Add IP', '', '', ' id="btn_add" class="inputButton" onclick="add_ip(\''.$list_loop.'\');"');
   							echo "<br>eg. *************** or ***************/24";
		}
		echo "			</td>
						<td class='dataTableContent' align='right' colspan='".(4+count($tags_names_array))."'>".tep_draw_pull_down_menu('sel_list', $sel_list_array, '', ' id="sel_list_'.$list_loop.'" onchange="document.getElementById(\'hidden_action_'.$list_loop.'\').value=\'move_list\';confirm_submit_form(\''.$list_loop.'\')" ')."</td>
					</tr>";
?>
									            				</table>
									            				<script>
									            					jQuery("tr.ip_blocked td, tr.ip_blocked a").css("color","red");
									            				</script>
									            				</form>
						            						</td>
						            					</tr>
						            				</table>
						            			</td>
			            					</tr>
            								<tr>
			            						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			            					</tr>
<?
	}
?>
			            				</table>
	  								</td>
          						</tr>
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<script>
	
	textboxes = jQuery("input.txt_ip");
	if (jQuery.browser.mozilla) {
		jQuery(textboxes).keypress(checkForEnter);
	} else {
		jQuery(textboxes).keydown(checkForEnter);
	}
		
	function checkForEnter(event) {
		if (event.keyCode == 13) {
			return false;
		}
	}
	
	function add_ip(pass_list) {
		if (validateIp(document.getElementById('txt_'+pass_list+'_ip').value)) {
			
			var display_html = 	'<table id="table_add_ip">'+
								'	<tr><td class="dataTableContent">Remark:</td></tr>'+
								'	<tr><td class="dataTableContent"><textarea id="txt_remark" name="txt_remark" cols="100" rows="5"></textarea></td></tr>'+
								'</table>';
			jquery_confirm_box(display_html, 2, 0, "Add IP - " + document.getElementById('txt_'+pass_list+'_ip').value);
			jQuery("#jconfirm_submit").click(function(){
				document.getElementById('hidden_action_'+pass_list).value='add_ip';
				document.getElementById('hidden_remark_'+pass_list).value= jQuery("table#table_add_ip textarea#txt_remark").val();
				document.getElementById('list_frm_'+pass_list).submit();
			});
			jQuery("#jconfirm_cancel").click(function(){
				document.getElementById('txt_'+pass_list+'_ip').value = '';
			});
		} else {
			jquery_confirm_box("Invalid IP/Subnet", 1, 1, "Warning");
			document.getElementById('txt_'+pass_list+'_ip').value = '';
		}
	}
	
	function load_tooltips(pass_id, pass_tag) {
		jQuery("div#div_information").css("top",((window.pageYOffset*1)+10) + "px");
		jQuery("div#div_information").html('<img src="images/loading.gif">').show();
		var selected_target = jQuery("a.jq_ogm_tooltips[ip_list_id="+pass_id+"][tag="+pass_tag+"]");
		var ip_list_ip = selected_target.attr('ip_list_ip');
		
		jQuery.ajax({
		   type: "GET",
		   url: "page_view_module_xmlhttp.php",
		   data: "action=history&ipId="+selected_target.attr('ip_list_id')+"&tag="+selected_target.attr('tag'),
		   success: function(xml){
				if (jQuery(xml).find("history").length>0) {
			   		var display_html = 	'<div style="width:100%;height:500px;overflow:auto;">'+
			   							'	<table width="100%">'+
			   							'		<tr>'+
			   							'			<td class="pageHeading">'+ip_list_ip+' - History:</td><td align="right"><a href="javascript:hide_information()"><img src="images/cal_close_small.gif"></a></td>'+
			   							'		</tr>'+
			   							'		<tr>'+
			   							'			<td colspan="2">'+
										'				<table width="100%">'+
										'					<tr class="reportBoxHeading">'+
										'						<td>Date</td>'+
										'						<td>IP</td>'+
										'						<td>URL</td>'+
										'						<td>Customer</td>'+
										'						<td>Remark</td>'+
										'					</tr>';
					jQuery(xml).find("history").each(function(){
						display_html +=	'      				<tr class="dataTableRow" onmouseover="this.className=\'dataTableRowOver\';this.style.cursor=\'hand\'" onmouseout="this.className=\'dataTableRow\'">'+
										'						<td class="dataTableContent">'+jQuery(this).find('ip_list_history_datatime').text()+'</td>'+
										'						<td class="dataTableContent">'+jQuery(this).find('ip_list_history_ip_address').text()+'</td>'+
										'						<td class="dataTableContent">'+jQuery(this).find('scripts_name').text()+'</td>'+
										'						<td class="dataTableContent">'+jQuery(this).find('customer').text()+'</td>'+
										'						<td class="dataTableContent">'+jQuery(this).find('ip_list_history_remark').text()+'</td>'+
										'					</tr>';
					});
					display_html +=		'				</table>'+
										'			</td>'+
										'		</tr>'+
										'	</table>';
										'</div>';
					jQuery("div#div_information").html(display_html);
				} else {
					jQuery("div#div_information").hide().html();
				}
			}
		 })		
	}
	
	function load_remark(pass_id) {
		var selected_target = jQuery("a.jq_ogm_remark[ip_list_id="+pass_id+"]");
		
		var ip_list_id = selected_target.attr('ip_list_id');
		var ip_list_ip = selected_target.attr('ip_list_ip');
		
		jQuery("div#div_information").css("top",((window.pageYOffset*1)+10) + "px");
		jQuery("div#div_information").html('<img src="images/loading.gif">').show();
		jQuery.ajax({
		   type: "GET",
		   url: "page_view_module_xmlhttp.php",
		   data: "action=remark&ipId="+ip_list_id,
		   success: function(xml){
				if (jQuery(xml).find("remark").length>0) {
			   		var display_html = 	'<div>'+
			   							'	<form id="frm_remark">'+
			   							'	<table width="100%">'+
			   							'		<tr><td class="pageHeading">'+ip_list_ip+' - Remark:</td><td align="right"><a href="javascript:hide_information()"><img src="images/cal_close_small.gif"></a></td></tr>'+
			   							'		<tr><td colspan="2"><textarea name="txt_remark" id="txt_remark" rows="5" cols="150">'+jQuery(xml).find('remark').text()+'</textarea></td></tr>'+
			   							'		<tr><td colspan="2"><input id="btn_save" class="generalBtn" type="button" onmouseout="this.className=\'generalBtn\'" onmouseover="this.className=\'generalBtnOver\'" onclick="save_remark(\''+ip_list_id+'\')" value="Save"/>&nbsp;&nbsp;<input id="btn_close" class="generalBtn" type="button" onmouseout="this.className=\'generalBtn\'" onmouseover="this.className=\'generalBtnOver\'" onclick="hide_information()" value="Close"/></td></tr>'+
			   							'	</table>'+
			   							'	</form>'+
			   							'</div>';
					jQuery("div#div_information").html(display_html);
				} else {
					jQuery("div#div_information").hide().html();
				}
			}
		 })		
	}
	
	function save_remark(pass_id) {
		jQuery("#frm_remark #btn_save").attr('value','saving...').attr('disabled',true);
		jQuery('#frm_remark').ajaxSubmit({ 
			url: "page_view_module_xmlhttp.php?action=save_remark&ipId=" + pass_id,
			dataType: 'xml',
			timeout: 30000,
			type: "POST",
		  	success: function(xml){
		  		if (jQuery(xml).find('success').text()!='1') {
		  			jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
		  		}
		  		jQuery("#frm_remark #btn_save").attr('value','save').attr('disabled',false);
		  		jQuery("span#span_remark_"+pass_id).html(jQuery(xml).find('remark').text());
				
				jQuery("a.jq_ogm_remark[ip_list_id="+pass_id+"]").click(function(){
					var ip_list_id = jQuery(this).attr('ip_list_id');
					var ip_list_ip = jQuery(this).attr('ip_list_ip');
					
					jQuery("div#div_information").css("top",((window.pageYOffset*1)+10) + "px");
					jQuery("div#div_information").html('<img src="images/loading.gif">').show();
					jQuery.ajax({
					   type: "GET",
					   url: "page_view_module_xmlhttp.php",
					   data: "action=remark&ipId="+ip_list_id,
					   success: function(xml){
							if (jQuery(xml).find("remark").length>0) {
						   		var display_html = 	'<div>'+
						   							'	<form id="frm_remark">'+
						   							'	<table width="100%">'+
						   							'		<tr><td class="pageHeading">'+ip_list_ip+' - Remark:</td><td align="right"><a href="javascript:hide_information()"><img src="images/cal_close_small.gif"></a></td></tr>'+
						   							'		<tr><td colspan="2"><textarea name="txt_remark" id="txt_remark" rows="5" cols="150">'+jQuery(xml).find('remark').text()+'</textarea></td></tr>'+
						   							'		<tr><td colspan="2"><input id="btn_save" class="generalBtn" type="button" onmouseout="this.className=\'generalBtn\'" onmouseover="this.className=\'generalBtnOver\'" onclick="save_remark(\''+ip_list_id+'\')" value="Save"/>&nbsp;&nbsp;<input id="btn_close" class="generalBtn" type="button" onmouseout="this.className=\'generalBtn\'" onmouseover="this.className=\'generalBtnOver\'" onclick="hide_information()" value="Close"/></td></tr>'+
						   							'	</table>'+
						   							'	</form>'+
						   							'</div>';
								jQuery("div#div_information").html(display_html);
							} else {
								jQuery("div#div_information").hide().html();
							}
						}
					 })
				});
		  	},
		  	error: function(xhr,err,e) { 
		  		jquery_confirm_box("Error Failed to submit data, please try again", 1, 1, "Warning");
		  		jQuery("#frm_remark #btn_save").attr('value','save').attr('disabled',false);
		  	}
		});
	}
	
	function delete_ip(pass_id, pass_ip) {
		var display_html = "Are you sure to delete following IP, "+pass_ip+"?<BR>";
		jquery_confirm_box(display_html, 2, 0, "Confirm");
		jQuery("#jconfirm_submit").click(function(){
			location.href="?action=delete&lid="+pass_id;
		});
	}
	
	function confirm_submit_form(pass_list) {
		if (jQuery("input.chk_ip_" + pass_list + ":checked").length) {
			if (jQuery("#sel_list_" + pass_list).val()=='batch_delete') {
				var display_html = "Are you sure to delete selected IP(s)?<BR>";
				jquery_confirm_box(display_html, 2, 0, "Confirm");
				jQuery("#jconfirm_submit").click(function(){
					jQuery("#hidden_action_"+pass_list).val('batch_delete');
					jQuery("form#list_frm_"+pass_list).submit();
				});
			} else {
				var to_list_name = 'Current';
				if (jQuery("#sel_list_" + pass_list).val()=='w') {
					to_list_name = 'White';
				} else if(jQuery("#sel_list_" + pass_list).val()=='b') {
					to_list_name = 'Watch';
				}
				var display_html = "Are you sure to move selected IP(s) to " + to_list_name + " List?<BR>";
				jquery_confirm_box(display_html, 2, 0, "Confirm");
				jQuery("#jconfirm_submit").click(function(){
					jQuery("form#list_frm_"+pass_list).submit();
				});
			}
		} else {
			jQuery("#sel_list_" + pass_list).val("");
		}
	}
	
	function confirm_mode_form(pass_id, pass_mode, pass_ip) {
		var mode_name = 'Tracking';
		if (pass_mode=='b') {
			mode_name = 'Blocking';
		}
		var display_html = "Are you sure to change following IP, "+pass_ip+" to '"+mode_name+"'?<BR>";
		jquery_confirm_box(display_html, 2, 0, "Confirm");
		jQuery("#jconfirm_submit").click(function(){
			location.href="?action=mode&mode="+pass_mode+"&lid="+pass_id;
		});
		
	}
	
	function hide_information() {
		jQuery("div#div_information").hide().html();
	}
	
	function toggle_chk_ip(pass_list) {
		if (jQuery("input#chk_all_"+pass_list).attr('checked')) {
			jQuery("input.chk_ip_" + pass_list ).attr('checked',true);
		} else {
			jQuery("input.chk_ip_" + pass_list ).attr('checked',false);
		}
	}
	
	function reset_check_all(pass_list) {
		jQuery("input#chk_all_"+pass_list).attr('checked',false);
	}
	
	function showOverEffect(object, class_name, extra_row) {
		rowOverEffect(object, class_name);
		var rowObjArray = extra_row.split('##');
		for (var i = 0; i < rowObjArray.length; i++) {
			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
				rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
			}
		}
	}
	
	function showOutEffect(object, class_name, extra_row) {
		rowOutEffect(object, class_name);
		var rowObjArray = extra_row.split('##');
  		for (var i = 0; i < rowObjArray.length; i++) {
  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
  				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
  			}
  		}
	}
	
	function showClicked(object, class_name, extra_row) {
		rowClicked(object, class_name);
		var rowObjArray = extra_row.split('##');
  		for (var i = 0; i < rowObjArray.length; i++) {
  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
  			}
		}
	}

</script>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>