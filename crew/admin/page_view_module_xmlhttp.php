<?

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

$read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');

echo '<response>';
if (tep_not_null($action)) {
	switch ($action) {
		case 'history':
			if (isset($_REQUEST['ipId'])) {
				$ipId = $_REQUEST['ipId'];
				$tag = '';
				if (isset($_REQUEST['tag'])) $tag = $_REQUEST['tag'];
				$ip_list_history_select_sql = "	SELECT ilh.ip_list_history_tags, ilh.scripts_name, c.customers_email_address, 
													ilh.ip_list_history_datatime, ilh.ip_list_history_remark, ilh.ip_list_history_ip_address 
												FROM " . TABLE_IP_LIST_HISTORY . " as ilh 
												INNER JOIN " . TABLE_PAGE_VIEW_IP_LIST . " as pvip 
													ON pvip.page_view_ip_list_id = ilh.page_view_ip_list_id 
												LEFT JOIN " . TABLE_CUSTOMERS . " as c 
													ON ilh.customers_id = c.customers_id 
												WHERE pvip.page_view_ip_list_id = '".tep_db_input($ipId)."' ";
				if (tep_not_null($tag)) {
					$ip_list_history_select_sql .= " AND ilh.ip_list_history_tags = '".tep_db_input($tag)."' ";
				}
				$ip_list_history_select_sql .= " ORDER BY ilh.ip_list_history_datatime DESC";
				$ip_list_history_result_sql = tep_db_query($ip_list_history_select_sql, 'read_db_link');
				while ($ip_list_history_row = tep_db_fetch_array($ip_list_history_result_sql)) {
					echo "	<history>
								<ip_list_history_tags><![CDATA[".$ip_list_history_row['ip_list_history_tags']."]]></ip_list_history_tags>
								<scripts_name><![CDATA[".$ip_list_history_row['scripts_name']."]]></scripts_name>
								<ip_list_history_ip_address><![CDATA[".$ip_list_history_row['ip_list_history_ip_address']."]]></ip_list_history_ip_address>
								<customer><![CDATA[".(tep_not_null($ip_list_history_row['customers_email_address'])?$ip_list_history_row['customers_email_address']:'Anonymous')."]]></customer>
								<ip_list_history_datatime><![CDATA[".$ip_list_history_row['ip_list_history_datatime']."]]></ip_list_history_datatime>
								<ip_list_history_remark><![CDATA[".$ip_list_history_row['ip_list_history_remark']."]]></ip_list_history_remark>
							</history>";
				}
			}
			break;
		case 'remark':
			if (isset($_REQUEST['ipId'])) {
				$ipId = $_REQUEST['ipId'];
				$ip_remark_select_sql = "	SELECT pvip.page_view_ip_list_remark 
											FROM " . TABLE_PAGE_VIEW_IP_LIST . " as pvip 
											WHERE pvip.page_view_ip_list_id = '".tep_db_input($ipId)."' 
											LIMIT 1";
				$ip_remark_result_sql = tep_db_query($ip_remark_select_sql, 'read_db_link');
				while ($ip_remark_row = tep_db_fetch_array($ip_remark_result_sql)) {
					echo "	<remark><![CDATA[".$ip_remark_row['page_view_ip_list_remark']."]]></remark>";
				}
			}
			break;
		case 'save_remark':
			if (isset($_REQUEST['ipId']) && (int)$_REQUEST['ipId'] > 0 && isset($_REQUEST['txt_remark'])) {
				$update_remark_data_sql = array('page_view_ip_list_remark'=>tep_db_prepare_input($_REQUEST['txt_remark']));
				tep_db_perform(TABLE_PAGE_VIEW_IP_LIST, $update_remark_data_sql, 'update', " page_view_ip_list_id = '".(int)$_REQUEST['ipId']."' ");
				
				$ip_remark_select_sql = "	SELECT pvip.page_view_ip_list_remark, pvip.page_view_ip_list_ip 
											FROM " . TABLE_PAGE_VIEW_IP_LIST . " as pvip 
											WHERE pvip.page_view_ip_list_id = '".tep_db_input($ipId)."' 
											LIMIT 1";
				$ip_remark_result_sql = tep_db_query($ip_remark_select_sql, 'read_db_link');
				while ($ip_remark_row = tep_db_fetch_array($ip_remark_result_sql)) {
					$remark_str = '';
					if (preg_match('/(.+)(\<br)/',nl2br($ip_remark_row['page_view_ip_list_remark']), $matched_str)) {
						$remark_str = $matched_str[1] . "...[<a class='jq_ogm_remark' ip_list_ip='".$page_view_ip_list_data_loop['page_view_ip_list_ip']."' ip_list_id='".$page_view_ip_list_id_loop."' onclick='load_remark(".$ipId.")' style='cursor:pointer;text-decoration:underline;'>more</a>]";
					} else if (strlen($ip_remark_row['page_view_ip_list_remark'])>50) {
						$remark_str = substr($ip_remark_row['page_view_ip_list_remark'],0,50)."...[<a class='jq_ogm_remark'  ip_list_ip='".$page_view_ip_list_data_loop['page_view_ip_list_ip']."' ip_list_id='".$page_view_ip_list_id_loop."' src='javascript:void(0)' style='cursor:pointer;text-decoration:underline;'>more</a>]";
					} else {
						$remark_str = $ip_remark_row['page_view_ip_list_remark'];
					}
					echo "	<remark><![CDATA[".$remark_str."]]></remark>";
				}
				
				echo "<success><![CDATA[1]]></success>";
			}
			break;
		default:
			break;
	}
}
echo '</response>';
?>