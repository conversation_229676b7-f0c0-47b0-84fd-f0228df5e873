<?
/*
  $Id: payment_methods.php,v 1.5 2014/05/07 07:33:29 ahsan.atiq Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

require('includes/application_top.php');

$payment_gateway_array = array();
$payment_methods_array = array();

$payment_methods_select_sql = "	SELECT pmt.payment_methods_types_name, pm.payment_methods_filename,
										pm.payment_methods_title, pm.payment_methods_parent_id,
										pm.payment_methods_id, pm.payment_methods_sort_order, pm.payment_methods_send_status_mode,
										pm.payment_methods_legend_color, pm.payment_methods_receive_status_mode,
										pm.payment_methods_send_mode_name, pm.payment_methods_receive_status,
										pm.payment_methods_send_status
								FROM " . TABLE_PAYMENT_METHODS . " as pm
								LEFT JOIN " . TABLE_PAYMENT_METHODS_TYPES . " as pmt
									ON pm.payment_methods_types_id = pmt.payment_methods_types_id
								ORDER BY pm.payment_methods_sort_order, pm.payment_methods_title ";
$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
    if ($payment_methods_row['payment_methods_parent_id'] == 0) {
        $payment_gateway_array[$payment_methods_row['payment_methods_filename']] = array("payment_methods_types_name" => $payment_methods_row['payment_methods_types_name'],
            "payment_methods_id" => $payment_methods_row['payment_methods_id'],
            "payment_methods_title" => $payment_methods_row['payment_methods_title'],
            "payment_methods_sort_order" => $payment_methods_row['payment_methods_sort_order'],
            "payment_methods_legend_color" => $payment_methods_row['payment_methods_legend_color'],
            "payment_methods_receive_status" => $payment_methods_row['payment_methods_receive_status'],
            "payment_methods_send_status" => $payment_methods_row['payment_methods_send_status'],
            "payment_methods_receive_status_mode" => $payment_methods_row['payment_methods_receive_status_mode'],
            "payment_methods_send_status_mode" => $payment_methods_row['payment_methods_send_status_mode'],
            "payment_methods_send_mode_name" => $payment_methods_row['payment_methods_send_mode_name']);
    } else {
        $payment_methods_array[$payment_methods_row['payment_methods_parent_id']][] = array("payment_methods_types_name" => $payment_methods_row['payment_methods_types_name'],
            "payment_methods_id" => $payment_methods_row['payment_methods_id'],
            "payment_methods_title" => $payment_methods_row['payment_methods_title'],
            "payment_methods_sort_order" => $payment_methods_row['payment_methods_sort_order'],
            "payment_methods_legend_color" => $payment_methods_row['payment_methods_legend_color'],
            "payment_methods_receive_status" => $payment_methods_row['payment_methods_receive_status'],
            "payment_methods_send_status" => $payment_methods_row['payment_methods_send_status'],
            "payment_methods_receive_status_mode" => $payment_methods_row['payment_methods_receive_status_mode'],
            "payment_methods_send_status_mode" => $payment_methods_row['payment_methods_send_status_mode'],
            "payment_methods_send_mode_name" => $payment_methods_row['payment_methods_send_mode_name']);
    }
}

$module_directory = DIR_FS_CATALOG_MODULES . 'payment/';
$module_type = 'payment';
$file_extension = substr($PHP_SELF, strrpos($PHP_SELF, '.'));
$directory_array = array();
if ($dir = @dir($module_directory)) {
    while ($file = $dir->read()) {
        if (!is_dir($module_directory . $file)) {
            if (substr($file, strrpos($file, '.')) == $file_extension) {
                if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/' . $module_type . '/' . $file)) {
                    include(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/' . $module_type . '/' . $file);
                }
                include($module_directory . $file);
                $class = substr($file, 0, strrpos($file, '.'));
                if (tep_class_exists($class)) {
                    if (!isset($payment_gateway_array[$file])) {
                        $payment_gateway_array[$file] = array("payment_methods_types_name" => '',
                            "payment_methods_id" => $class,
                            "payment_methods_title" => $file,
                            "payment_methods_sort_order" => '',
                            "payment_methods_legend_color" => '',
                            "payment_methods_receive_status_mode" => '');
                    }
                }
            }
        }
    }
    $dir->close();
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="includes/javascript/xmlhttp.js"></script>
        <script language="javascript" src="includes/javascript/jquery.js"></script>
        <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
        <script language="javascript" src="includes/javascript/jquery.selectboxes.js"></script>
        <script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
        <script language="javascript" src="includes/javascript/jquery.form.js"></script>
        <script language="javascript" src="includes/javascript/ajaxfileupload.js"></script>
        <script language="javascript" src="includes/javascript/payment_methods.js"></script>
        <script language="javascript">
            jQuery.noConflict();
            jQuery(document).ready(function() {
                initInfoCaptions();
                jQuery("#payment_method_tab > ul").tabs();
                jQuery("#payment_method_tab_1").css({border: '1px solid #C9C9C9'});
                jQuery("#payment_method_tab_2").css({border: '1px solid #C9C9C9'});
                jQuery("#payment_method_tab_3").css({border: '1px solid #C9C9C9'});
                jQuery("#payment_method_tab_4").css({border: '1px solid #C9C9C9'});
                jQuery("#payment_method_tab_5").css({border: '1px solid #C9C9C9'});
                jQuery("#payment_method_tab_6").css({border: '1px solid #C9C9C9'});
                jQuery("#payment_method_tab_7").css({border: '1px solid #C9C9C9'});
                jQuery("#payment_method_tab_8").css({border: '1px solid #C9C9C9'});
            });
        </script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td width="100%">
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?= HEADING_TITLE ?></td>
                                        <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <? //if ($set == "payment_method") {  ?>
                        <tr>
                            <td>[ <a href="<?= tep_href_link(FILENAME_PAYMENT_MODULE) ?>" ><?= LINK_SEND_PAYMENT_METHOD ?></a> ]</td>
                        </tr>
                        <? //}  ?>
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td valign="top">
                                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <tr class="dataTableHeadingRow">
                                                    <td class="reportBoxHeading" width="*"><?= TABLE_HEADING_PAYMENT_METHODS_NAME ?></td>
                                                    <td class="reportBoxHeading" width="*"><?= TABLE_HEADING_SEND_PAYMENT_METHODS_NAME ?></td>
                                                    <td class="reportBoxHeading" align="left" width="15%"><?= TABLE_HEADING_PAYMENT_METHODS_TYPE ?>&nbsp;</td>
                                                    <td class="ordersBoxHeading" align="center" width="8%"><?= TABLE_HEADING_SORT_ORDER ?></td>
                                                    <td class="ordersBoxHeading" align="center" width="10%"><?= TABLE_HEADING_LEGEND_COLOUR ?></td>
                                                    <td class="ordersBoxHeading" align="center" width="5%"><?= TABLE_HEADING_ENABLED ?></td>
                                                    <td class="ordersBoxHeading" align="center" width="5%"><?= TABLE_HEADING_SEND_ENABLED ?></td>
                                                    <td class="ordersBoxHeading" align="right" width="5%"><?= TABLE_HEADING_ACTION ?>&nbsp;</td>
                                                </tr>
                                                <?
                                                foreach ($payment_gateway_array as $file => $data) {
                                                    $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                    $row_count++;

                                                    if ($data['payment_methods_receive_status'] == '1') {
                                                        $display_receive_style = 'greenIndicatorBold';
                                                    } else {
                                                        $display_receive_style = 'redIndicatorBold';
                                                    }

                                                    if ($data['payment_methods_send_status'] == '1') {
                                                        $display_send_style = 'greenIndicatorBold';
                                                    } else {
                                                        $display_send_style = 'redIndicatorBold';
                                                    }
                                                    ?>

                                                    <tr <?= (is_numeric($data['payment_methods_id']) ? "id='tr_for_pm_" . $data['payment_methods_id'] . "'" : "") ?> class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" <?= (isset($payment_methods_array[$data['payment_methods_id']]) && count($payment_methods_array[$data['payment_methods_id']]) ? ' style="cursor: pointer;" ' : '') ?>>
                                                        <td class="dataTableContent" onclick="tongle_child_payment_methods('<?= $data['payment_methods_id'] ?>')"><span class="<?= $display_receive_style ?>"><?= $data['payment_methods_title'] ?></span></td>
                                                        <td class="dataTableContent" onclick="tongle_child_payment_methods('<?= $data['payment_methods_id'] ?>')"><span class="<?= $display_send_style ?>"><?= $data['payment_methods_send_mode_name'] ?></span></td>
                                                        <td class="dataTableContent" onclick="tongle_child_payment_methods('<?= $data['payment_methods_id'] ?>')" align="left"><?= $data['payment_methods_types_name'] ?></td>
                                                        <td class="dataTableContent" onclick="tongle_child_payment_methods('<?= $data['payment_methods_id'] ?>')" align="center"><?= $data['payment_methods_sort_order']; ?> </td>
                                                        <td class="dataTableContent" onclick="tongle_child_payment_methods('<?= $data['payment_methods_id'] ?>')" align="center"><?= (isset($data['payment_methods_legend_color']) && tep_not_null($data['payment_methods_legend_color']) ? '<hr style=" border: 1px solid black; color:' . $data['payment_methods_legend_color'] . '; background-color:' . $data['payment_methods_legend_color'] . '; height:10px; width:40%; text-align:center;">' : '') ?></td>
                                                        <td class="dataTableContent" onclick="tongle_child_payment_methods('<?= $data['payment_methods_id'] ?>')" align="center">
                                                            <?
                                                            if (tep_not_null($data['payment_methods_receive_status_mode'])) {
                                                                if ((int) $data['payment_methods_receive_status_mode'] > 0) {
                                                                    echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
                                                                } else if (tep_not_null($data['payment_methods_receive_status_mode']) && (int) $data['payment_methods_receive_status_mode'] == 0) {
                                                                    echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                                                                } else {
                                                                    echo tep_image(DIR_WS_IMAGES . 'icon_status_yellow.gif', IMAGE_ICON_STATUS_YELLOW, 10, 10);
                                                                }
                                                            } else {
                                                                echo '&nbsp;';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td class="dataTableContent" onclick="tongle_child_payment_methods('<?= $data['payment_methods_id'] ?>')" align="center" id="td_for_pm_send_status_<?= $data['payment_methods_id'] ?>">
                                                            <?
                                                            if (tep_not_null($data['payment_methods_send_status_mode'])) {
                                                                if ((int) $data['payment_methods_send_status_mode'] > 0) {
                                                                    echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
                                                                } else {
                                                                    echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                                                                }
                                                            } else {
                                                                echo '&nbsp;';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td class="dataTableContent" align="left">
                                                            <?
                                                            echo '<a href="javascript:load_payment_methods(\'' . $data['payment_methods_id'] . '\')">' . tep_image(DIR_WS_IMAGES . 'icons/edit.gif', IMAGE_ICON_INFO) . '</a>';
                                                            if ((int) $data['payment_methods_id'] > 0) {
                                                                echo "&nbsp;&nbsp;<a href='javascript:display_insert_payment_methods_tab(" . $data['payment_methods_id'] . ");'>" . tep_image(DIR_WS_IMAGES . 'icons/add_item.gif', IMAGE_ICON_INFO) . '</a>';
                                                            }
                                                            echo "</td></tr>";

                                                            //List Child
                                                            if (is_numeric($data['payment_methods_id']) && isset($payment_methods_array[$data['payment_methods_id']])) {
                                                                foreach ($payment_methods_array[$data['payment_methods_id']] as $child) {
                                                                    if ($child['payment_methods_receive_status'] == '1') {
                                                                        $display_receive_style = 'greenIndicatorBold';
                                                                    } else {
                                                                        $display_receive_style = 'redIndicatorBold';
                                                                    }

                                                                    if ($child['payment_methods_send_status'] == '1') {
                                                                        $display_send_style = 'greenIndicatorBold';
                                                                    } else {
                                                                        $display_send_style = 'redIndicatorBold';
                                                                    }
                                                                    ?>
                                                            <tr id="tr_for_pm_<?= $child['payment_methods_id'] ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, 'bracketListingOdd tr_for_pm_<?= $data['payment_methods_id'] ?>')" class="bracketListingOdd tr_for_pm_<?= $data['payment_methods_id'] ?>" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" valign="top" style="display:none">
                                                                <td class="dataTableContent">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="<?= $display_receive_style ?>"><?= $child['payment_methods_title'] ?></span></td>
                                                                <td class="dataTableContent" onclick="tongle_child_payment_methods('<?= $data['payment_methods_id'] ?>')"><span class="<?= $display_send_style ?>"><?= $child['payment_methods_send_mode_name'] ?></span></td>
                                                                <td class="dataTableContent" align="left"><?= $child['payment_methods_types_name'] ?></td>
                                                                <td class="dataTableContent" align="center"><? if (is_numeric($child['payment_methods_sort_order'])) echo $child['payment_methods_sort_order'] ?></td>
                                                                <td class="dataTableContent" align="center"><?= ($child['payment_methods_legend_color'] ? '<hr style=" border: 1px solid black; color:' . $child['payment_methods_legend_color'] . '; background-color:' . $child['payment_methods_legend_color'] . '; height:10px; width:40%; text-align:center;">' : '') ?></td>
                                                                <td class="dataTableContent" align="center">
                                                                    <?
                                                                    if (tep_not_null($child['payment_methods_receive_status_mode'])) {
                                                                        if ((int) $child['payment_methods_receive_status_mode'] > 0) {
                                                                            echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
                                                                        } else if (tep_not_null($child['payment_methods_receive_status_mode']) && (int) $child['payment_methods_receive_status_mode'] == 0) {
                                                                            echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                                                                        } else {
                                                                            echo tep_image(DIR_WS_IMAGES . 'icon_status_yellow.gif', IMAGE_ICON_STATUS_YELLOW, 10, 10);
                                                                        }
                                                                    } else {
                                                                        echo '&nbsp;';
                                                                    }
                                                                    ?>
                                                                </td>
                                                                <td class="dataTableContent" align="center" id="td_for_pm_send_status_<?= $child['payment_methods_id'] ?>">
                                                                    <?
                                                                    if (tep_not_null($child['payment_methods_send_status_mode'])) {
                                                                        if ((int) $child['payment_methods_send_status_mode'] > 0) {
                                                                            echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
                                                                        } else {
                                                                            echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                                                                        }
                                                                    } else {
                                                                        echo '&nbsp;';
                                                                    }
                                                                    ?>
                                                                </td>
                                                                <td>
                                                                    <?
                                                                    echo '<a href="javascript:load_payment_methods(' . $child['payment_methods_id'] . ')">' . tep_image(DIR_WS_IMAGES . 'icons/edit.gif', IMAGE_ICON_INFO) . '</a>';
                                                                    ?>
                                                                </td>
                                                            </tr>
                                                            <?
                                                        }
                                                    }
                                                }
                                                ?>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                    </tr>
                                    <?= tep_draw_hidden_field('payment_methods_id', '', '  id="payment_methods_id" '); ?>
                                    <tr id="payment_method_tab_zone" style="display:none">
                                        <td>
                                            <table width="100%" border='0'>
                                                <tr>
                                                    <td class="pageHeading" align="left" class="formAreaTitle" id="payment_method_tab_header"></td>
                                                </tr>
                                                <tr>
                                                    <td width="100%">
                                                        <div id="payment_method_tab" width="100%">
                                                            <ul>
                                                                <li id='payment_method_li_1'><a href="#payment_method_tab_1"><span onclick="load_tab('display_status_tab')">Status</span></a></li>
                                                                <li id='payment_method_li_2'><a href="#payment_method_tab_2"><span onclick="load_tab('display_settings_tab')">Setting</span></a></li>
                                                                <li id='payment_method_li_3'><a href="#payment_method_tab_3"><span onclick="load_tab('display_currencies_tab')">Currencies</span></a></li>
                                                                <li id='payment_method_li_4'><a href="#payment_method_tab_4"><span onclick="load_tab('display_surcharge_tab')">Surcharge</span></a></li>
                                                                <li id='payment_method_li_8'><a href="#payment_method_tab_8"><span onclick="load_tab('display_sc_surcharge_tab')">SC Surcharge</span></a></li>
                                                                <li id='payment_method_li_7'><a href="#payment_method_tab_7"><span onclick="load_tab('display_send_currencies_tab')" style='color: #9C009C;'>Currencies [Outgoing]</span></a></li>
                                                                <li id='payment_method_li_5'><a href="#payment_method_tab_5"><span onclick="load_tab('display_send_payment_tab')" style='color: #9C009C;'>Payment Fee [Outgoing]</span></a></li>
                                                                <li id='payment_method_li_6'><a href="#payment_method_tab_6"><span onclick="load_tab('display_send_payment_info_tab')" style='color: #9C009C;'>Payment Info [Outgoing]</span></a></li>
                                                            </ul>
                                                            <div id="payment_method_tab_1"></div>
                                                            <div id="payment_method_tab_2"></div>
                                                            <div id="payment_method_tab_3"></div>
                                                            <div id="payment_method_tab_4"></div>
                                                            <div id="payment_method_tab_8"></div>
                                                            <div id="payment_method_tab_5"></div>
                                                            <div id="payment_method_tab_6"></div>
                                                            <div id="payment_method_tab_7"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
                <!-- body_text_eof //-->
            </tr>
        </table>
        <script language="javascript">
<?
if (isset($_REQUEST['action']) && isset($_REQUEST['pmID']) && (int) $_REQUEST['pmID'] > 0) {
    switch ($_REQUEST['action']) {
        case "display_new_child":
            echo "tongle_child_payment_methods(" . $_REQUEST['pmID'] . ");";
            break;
        case "display_pg":
            echo "load_payment_methods(" . $_REQUEST['pmID'] . ");";
            break;
    }
}
?>
        </script>

        <script language="JavaScript1.2" defer>
            var modalWin = null;
            function popUpColorLab(item) {
                if (window.showModalDialog) {
                    var url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
                    var selectedColor = window.showModalDialog(url + "popups/select_color.html", null, "resizable:no;help:no;status:no;scroll:no;");
                    if (selectedColor) {
                        document.getElementById(item).value = "#" + selectedColor;
                        return true;
                    }
                } else {
                    var url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
                    var selectedColor = window.open(url + "popups/select_color.html", 'colour', 'width=230,height=165');
                    //opener.blockEvents();
                }
                return false;
            }
            function selectAll(theField) {
                var textContainer = document.getElementById(theField);
                textContainer.focus();
                textContainer.select();
            }
        </script>
        <!-- body_eof //-->

        <!-- footer //-->
        <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <br>
    </body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>