<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

define('EMAIL_TEXT_SUBJECT_CREW_NOTIFICATION', "Receiving Payment Method Changes Notification");

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'payment_methods.php');
include_once(DIR_WS_CLASSES . 'currencies.php');

require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

// Prevent bypass access
$payment_methods_permission = tep_admin_check_boxes(FILENAME_PAYMENT_METHODS, 'sub_boxes');
if (!$payment_methods_permission)
    exit;

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$pm_id = (int) (isset($_REQUEST['pm_id']) ? $_REQUEST['pm_id'] : '');
$pmi_id = (int) (isset($_REQUEST['pmi_id']) ? $_REQUEST['pmi_id'] : '');
$pmf_id = (int) (isset($_REQUEST['pmf_id']) ? $_REQUEST['pmf_id'] : '');

$receive_pm_email_array = array();

echo '<response>';
if (tep_not_null($action)) {
    $payment_methods_obj = new payment_methods($pm_id);
    echo "	<payment_methods>";
    echo "		<payment_methods_title><![CDATA[" . tep_db_output($payment_methods_obj->get_title()) . "]]></payment_methods_title>";
    echo "		<payment_methods_display_title><![CDATA[" . $payment_methods_obj->get_title() . "]]></payment_methods_display_title>";
    echo "		<payment_methods_send_mode_name><![CDATA[" . tep_db_output($payment_methods_obj->get_send_title()) . "]]></payment_methods_send_mode_name>";
    echo "		<payment_methods_parent_id><![CDATA[" . $payment_methods_obj->get_parent_id() . "]]></payment_methods_parent_id>";
    echo "		<payment_methods_code><![CDATA[" . tep_db_output($payment_methods_obj->get_code()) . "]]></payment_methods_code>";
    echo "		<display_title><![CDATA[" . $payment_methods_obj->get_pm_display_title() . "]]></display_title>";
    echo "		<number_of_child><![CDATA[" . $payment_methods_obj->get_number_of_child() . "]]></number_of_child>";
    echo "		<payment_methods_receive_status><![CDATA[" . tep_db_output($payment_methods_obj->get_receive_flag()) . "]]></payment_methods_receive_status>";
    echo "		<payment_methods_receive_status_mode><![CDATA[" . tep_db_output($payment_methods_obj->get_receive_status()) . "]]></payment_methods_receive_status_mode>";
    echo "		<payment_methods_send_status><![CDATA[" . tep_db_output($payment_methods_obj->get_send_flag()) . "]]></payment_methods_send_status>";
    echo "		<payment_methods_send_status_mode><![CDATA[" . tep_db_output($payment_methods_obj->get_send_status()) . "]]></payment_methods_send_status_mode>";
    echo "		<payment_methods_send_mass_payment><![CDATA[" . tep_db_output($payment_methods_obj->payment_methods_send_mass_payment) . "]]></payment_methods_send_mass_payment>";
    echo "		<payment_methods_send_applicable_sites><![CDATA[" . tep_db_output($payment_methods_obj->payment_methods_send_available_sites) . "]]></payment_methods_send_applicable_sites>";
    echo "	</payment_methods>";

    if ($action == "display_status_tab" || $action == "display_all_tab") {
        if ($payment_methods_obj != 0) {
            $aws_obj = new ogm_amazon_ws();
            $aws_obj->set_bucket_key('BUCKET_STATIC');
            $aws_obj->set_filepath('images/payment/');

            echo "	<display_status_tab>";
            echo "		<payment_methods_types_id><![CDATA[" . $payment_methods_obj->get_types_id() . "]]></payment_methods_types_id>";
            echo "		<payment_methods_types_name><![CDATA[" . tep_db_output($payment_methods_obj->get_types_name()) . "]]></payment_methods_types_name>";
            echo "		<payment_methods_description_title><![CDATA[" . tep_db_output($payment_methods_obj->get_display_title()) . "]]></payment_methods_description_title>";
            echo "		<payment_methods_sort_order><![CDATA[" . $payment_methods_obj->get_sort_order() . "]]></payment_methods_sort_order>";
            echo "		<payment_methods_legend_color><![CDATA[" . $payment_methods_obj->get_legend_color() . "]]></payment_methods_legend_color>";
            echo "		<payment_methods_status_message><![CDATA[" . tep_db_output($payment_methods_obj->get_status_message()) . "]]></payment_methods_status_message>";
            echo "		<payment_methods_status_send_message><![CDATA[" . tep_db_output($payment_methods_obj->get_send_status_message()) . "]]></payment_methods_status_send_message>";
            echo "		<payment_methods_send_code><![CDATA[" . tep_db_output($payment_methods_obj->get_send_code()) . "]]></payment_methods_send_code>";
            echo "		<payment_methods_receive_featured_status><![CDATA[" . tep_db_output($payment_methods_obj->payment_methods_receive_featured_status) . "]]></payment_methods_receive_featured_status>";

            $image_info_array = $aws_obj->get_image_info($payment_methods_obj->get_logo());
            if (tep_not_null($image_info_array)) { // Success get from S3
                $pm_img = $image_info_array['src'] . '?' . time();
            } else if (tep_not_null($payment_methods_obj->get_logo()) && file_exists(DIR_FS_CATALOG_IMAGES . 'payment/' . $payment_methods_obj->get_logo())) {
                $pm_img = DIR_WS_CATALOG_IMAGES . 'payment/' . $payment_methods_obj->get_logo() . '?' . time();
            } else {
                $pm_img = '';
            }
            echo "		<payment_methods_logo><![CDATA[" . ($pm_img) . "]]></payment_methods_logo>";

            echo "		<payment_methods_types>";
            foreach ($payment_methods_obj->get_payment_methods_types() as $id => $text) {
                echo "	<payment_methods_type id='" . $id . "'><![CDATA[" . $text . "]]></payment_methods_type>";
            }
            echo "		</payment_methods_types>";
            echo "		<payment_methods_send_types>";
            foreach ($payment_methods_obj->get_payment_methods_types('SEND') as $id => $text) {
                echo "	<payment_methods_send_type id='" . $id . "'><![CDATA[" . $text . "]]></payment_methods_send_type>";
            }
            echo "		</payment_methods_send_types>";

            echo "		<payment_methods_send_available_sites>";
            foreach ($payment_methods_obj->get_payment_methods_send_available_sites() as $id => $text) {
                echo "	<payment_methods_send_available_site id='" . $id . "'><![CDATA[" . $text . "]]></payment_methods_send_available_site>";
            }
            echo "		</payment_methods_send_available_sites>";

            $currencies_select_sql = "	SELECT c.code
										FROM " . TABLE_CURRENCIES . " as c 
										WHERE c.currencies_id = '" . (int) $payment_methods_obj->payment_methods_send_currency . "'";
            $currencies_resut_sql = tep_db_query($currencies_select_sql);
            $currencies_row = tep_db_fetch_array($currencies_resut_sql);
            echo "		<payment_methods_send_currency><![CDATA[" . $currencies_row['code'] . "]]></payment_methods_send_currency>";

            if (!(int) $payment_methods_obj->payment_methods_send_currency > 0) {
                echo "	<currencies>";
                $currencies_select_sql = "	SELECT currencies_id, code
											FROM " . TABLE_CURRENCIES . "
											ORDER BY code";
                $currencies_result_sql = tep_db_query($currencies_select_sql);
                while ($currencies_row = tep_db_fetch_array($currencies_result_sql)) {
                    echo "	<currency id='" . $currencies_row['currencies_id'] . "'><![CDATA[" . $currencies_row['code'] . "]]></currency>";
                }
                echo "	</currencies>";
            }
            echo "	</display_status_tab>";
            unset($aws_obj);
        }
    }

    if ($action == "display_settings_tab" || $action == "display_all_tab") {
        $payment_configuration_info_array = array();
        $payment_configuration_info_array = $payment_methods_obj->get_payment_method_configuration_key();
        $payment_configuration_info_found = count($payment_configuration_info_array);
        if ($payment_methods_obj->get_parent_id() > 0) {
            foreach ($payment_configuration_info_array as $key => $data) {
                $payment_configuration_info_array[$key]['payment_configuration_info_value'] = "";
            }
            //get child value
            $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_key, pcid.payment_configuration_info_value 
														FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
														LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
															ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id 
																AND pcid.languages_id = '1'
														WHERE pci.payment_methods_id = '" . (int) $pm_id . "' 
														ORDER BY pci.payment_configuration_info_sort_order ";
            $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
            $payment_configuration_info_found = tep_db_num_rows($payment_configuration_info_result_sql);
            while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                if (isset($payment_configuration_info_array[$payment_configuration_info_row['payment_configuration_info_key']])) {
                    $payment_configuration_info_array[$payment_configuration_info_row['payment_configuration_info_key']]['payment_configuration_info_value'] = $payment_configuration_info_row['payment_configuration_info_value'];
                }
            }
        }

        echo "<display_customers_groups>";
        $customers_groups_array = array();
        $customers_groups_select_sql = "SELECT customers_groups_id, customers_groups_name, customers_groups_payment_methods 
										FROM " . TABLE_CUSTOMERS_GROUPS . "
										ORDER BY sort_order, customers_groups_name";
        $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
        while ($customers_groups_row = tep_db_fetch_array($customers_groups_result_sql)) {
            echo "	<customers_groups id='" . $customers_groups_row['customers_groups_id'] . "' selected='" . (tep_not_null($customers_groups_row['customers_groups_payment_methods']) && in_array((int) $pm_id, explode(",", $customers_groups_row['customers_groups_payment_methods'])) ? 1 : 0) . "'><![CDATA[" . $customers_groups_row['customers_groups_name'] . "]]></customers_groups>";
            $customers_groups_array[] = $customers_groups_row['customers_groups_id'];
        }
        echo "</display_customers_groups>";

        echo "<display_settings_tab found='" . $payment_configuration_info_found . "'>";
        foreach ($payment_configuration_info_array as $key => $data) {
            echo "	<payment_methods_setting>";
            echo "		<payment_configuration_info_title><![CDATA[" . tep_db_output($data['payment_configuration_info_title']) . "]]></payment_configuration_info_title>";
            echo "		<payment_configuration_info_description><![CDATA[" . $data['payment_configuration_info_description'] . "]]></payment_configuration_info_description>";
            echo "		<display><![CDATA[";
            if ($data['set_function']) {
                if (strpos($data['set_function'], 'tep_cfg_textarea') === 0) {
                    echo tep_draw_textarea_field("configuration[" . $key . "]", "", "66", "5", tep_db_output($data['payment_configuration_info_value']));
                } else {
                    eval('echo ' . tep_db_prepare_input($data['set_function']) . '"' . tep_db_output($data['payment_configuration_info_value']) . '", "' . $key . '");');
                }
            } else {
                echo tep_draw_input_field('configuration[' . $key . ']', tep_db_output($data['payment_configuration_info_value']));
            }
            echo "	]]></display>";
            echo "	</payment_methods_setting>";
        }
        echo "</display_settings_tab>";

        echo "<display_languages>";
        for ($i = 0; $i < sizeof($languages); $i++) {
            echo "<display_language id='" . $languages[$i]['id'] . "' default='" . ($languages[$i]['id'] == 1 ? 1 : 0) . "'><![CDATA[" . $languages[$i]['name'] . "]]></display_language>";
        }
        echo "</display_languages>";
    }

    if ($action == "display_currencies_tab" || $action == "display_all_tab") {

        if (tep_not_null($payment_methods_obj->get_filename()) && file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_filename())) {
            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_filename());
        }

        if (tep_class_exists($payment_methods_obj->get_payment_methods_class_name())) {
            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_payment_methods_class_name() . '/admin/languages/english/' . $payment_methods_obj->get_payment_methods_class_name() . '.lng.php')) {
                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_payment_methods_class_name() . '/admin/languages/english/' . $payment_methods_obj->get_payment_methods_class_name() . '.lng.php');
            }

            eval('$module = new ' . $payment_methods_obj->get_payment_methods_class_name() . '();');

            echo "<display_currencies_tab>";
            $merchant_information_keys = array();
            if (method_exists($module, 'merchant_information_keys')) {
                $merchant_information_keys = $module->merchant_information_keys();
                if (isset($merchant_information_keys) && count($merchant_information_keys)) {
                    foreach ($merchant_information_keys as $key => $data) {
                        echo "	<payment_methods_instance_setting_key key='" . $key . "' field='" . $data['field'] . "'>";
                        echo "	<mapping><![CDATA[";
                        eval("echo " . $data['mapping'] . ";");
                        echo "]]></mapping>";
                        echo "	<default><![CDATA[";
                        echo $data['default'];
                        echo "]]></default>";
                        switch ($data['field']) {
                            case "select":
                                echo "	<select>";
                                if (count($data['select'])) {
                                    foreach ($data['select'] as $select_value => $select_label) {
                                        echo "<option value='" . $select_value . "'><![CDATA[" . $select_label . "]]></option>";
                                    }
                                }
                                echo "	</select>";
                                break;
                            case "textarea":
                            case "text":
                                if (isset($data['cols'])) {
                                    echo "<cols><![CDATA[" . $data['cols'] . "]]></cols>";
                                }
                                if (isset($data['rows'])) {
                                    echo "<rows><![CDATA[" . $data['rows'] . "]]></rows>";
                                }
                                break;
                        }
                        echo "	</payment_methods_instance_setting_key>";
                    }
                }
            }
            echo "	<payment_methods_settlement_currencies>";
            $payment_methods_instance_select_sql = "	SELECT pgi.payment_methods_instance_id, pgi.currency_code, pgi.payment_methods_instance_default, c.code, pgi.payment_methods_instance_follow_default 
														FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pgi 
														INNER JOIN " . TABLE_CURRENCIES . " as c
															ON c.code = pgi.currency_code
														WHERE payment_methods_id = '" . (int) $pm_id . "'
															ORDER BY c.code";
            $payment_methods_instance_result_sql = tep_db_query($payment_methods_instance_select_sql);
            $assigned_currencies = array();
            $assigned_setting_value = array();
            $payment_methods_instance_array = array();
            $payment_methods_instance_setting_array = array();
            while ($payment_methods_instance_row = tep_db_fetch_array($payment_methods_instance_result_sql)) {
                $assigned_currencies[] = $payment_methods_instance_row['currency_code'];
                $payment_methods_instance_array[$payment_methods_instance_row['payment_methods_instance_id']] = array("currency_code" => $payment_methods_instance_row['currency_code'],
                    "code" => $payment_methods_instance_row['code'],
                    "payment_methods_instance_default" => $payment_methods_instance_row['payment_methods_instance_default'],
                    "payment_methods_instance_follow_default" => $payment_methods_instance_row['payment_methods_instance_follow_default']);

                $payment_methods_instance_setting_select_sql = "SELECT pgis.payment_methods_instance_setting_id, pgis.payment_methods_instance_setting_key, pgis.payment_methods_instance_setting_value
																FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . " as pgis 
																WHERE pgis.payment_methods_instance_id = '" . (int) $payment_methods_instance_row['payment_methods_instance_id'] . "'
																	AND pgis.payment_methods_instance_setting_key IN ('" . implode("','", array_keys($merchant_information_keys)) . "')";
                $payment_methods_instance_setting_result_sql = tep_db_query($payment_methods_instance_setting_select_sql);
                while ($payment_methods_instance_setting_row = tep_db_fetch_array($payment_methods_instance_setting_result_sql)) {
                    $payment_methods_instance_setting_array[$payment_methods_instance_setting_row['payment_methods_instance_setting_key']] = array("id" => (int) $payment_methods_instance_setting_row['payment_methods_instance_setting_id'],
                        "value" => $payment_methods_instance_setting_row['payment_methods_instance_setting_value']);
                    $assigned_setting_value[(int) $payment_methods_instance_row['payment_methods_instance_id']][$payment_methods_instance_setting_row['payment_methods_instance_setting_key']] = $payment_methods_instance_setting_row['payment_methods_instance_setting_value'];
                }
            }

            foreach ($payment_methods_instance_array as $key => $data) {
                echo "	<payment_methods_settlement_currency>";
                echo "		<payment_methods_instance_id><![CDATA[" . $key . "]]></payment_methods_instance_id>";
                echo "		<payment_methods_instance_follow_default><![CDATA[" . (int) $data['payment_methods_instance_follow_default'] . "]]></payment_methods_instance_follow_default>";
                echo "		<currency_code><![CDATA[" . $data['currency_code'] . "]]></currency_code>";
                echo "		<code><![CDATA[" . $data['code'] . "]]></code>";

                foreach ($module->merchant_information_keys() as $mkey => $mdata) {
                    echo "	<payment_methods_instance_setting field='" . $mdata['field'] . "' id='" . $payment_methods_instance_setting_array[$mkey]['id'] . "' key='" . $mkey . "'><![CDATA[";

                    if ((int) $data['payment_methods_instance_follow_default'] > 0) {
                        if (isset($mdata['default']) && tep_not_null($mdata['default']) && !tep_not_null($assigned_setting_value[(int) $data['payment_methods_instance_follow_default']][$mkey])) {
                            echo tep_db_output($mdata['default']);
                        } else {
                            echo tep_db_output($assigned_setting_value[(int) $data['payment_methods_instance_follow_default']][$mkey]);
                        }
                    } else if (isset($payment_methods_instance_setting_array[$mkey])) {
                        if (isset($mdata['default']) && tep_not_null($mdata['default']) && !tep_not_null($assigned_setting_value[$key][$mkey])) {
                            echo tep_db_output($mdata['default']);
                        } else {
                            echo tep_db_output($assigned_setting_value[$key][$mkey]);
                        }
                    } else if (isset($mdata['default']) && tep_not_null($mdata['default']) && !tep_not_null($assigned_setting_value[$key][$mkey])) {
                        echo tep_db_output($mdata['default']);
                    }

                    echo "]]></payment_methods_instance_setting>";
                }
                echo "		<payment_methods_instance_default><![CDATA[" . ($data['payment_methods_instance_default'] ? '1' : '0') . "]]></payment_methods_instance_default>";
                echo "	</payment_methods_settlement_currency>";
            }
            echo "	</payment_methods_settlement_currencies>";

            // Settlement Currency
            echo "	<currencies>";

            $currencies_select_sql = "	SELECT currencies_id, code
 										FROM " . TABLE_CURRENCIES . "
 										WHERE FIND_IN_SET( 'SELL',currencies_used_for) 
 										ORDER BY code";
            $currencies_result_sql = tep_db_query($currencies_select_sql);
            while ($currencies_row = tep_db_fetch_array($currencies_result_sql)) {
                if (!in_array($currencies_row['code'], $assigned_currencies)) {
                    echo "	<currency id='" . $currencies_row['code'] . "'><![CDATA[" . $currencies_row['code'] . "]]></currency>";
                }
            }

            echo "	</currencies>";
            echo "</display_currencies_tab>";
        }
    }

    if ($action == "display_send_currencies_tab" || $action == "display_all_tab") {

        if (tep_not_null($payment_methods_obj->get_filename()) && file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_filename())) {
            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_filename());
        }

        if (tep_class_exists($payment_methods_obj->get_payment_methods_class_name())) {
            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_payment_methods_class_name() . '/admin/languages/english/' . $payment_methods_obj->get_payment_methods_class_name() . '.lng.php')) {
                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_payment_methods_class_name() . '/admin/languages/english/' . $payment_methods_obj->get_payment_methods_class_name() . '.lng.php');
            }

            eval('$module = new ' . $payment_methods_obj->get_payment_methods_class_name() . '();');

            echo "<display_send_currencies_tab>";
            $merchant_information_keys = array();
            if (method_exists($module, 'outgoing_merchant_information_keys')) {
                $merchant_information_keys = $module->outgoing_merchant_information_keys();
                if (isset($merchant_information_keys) && count($merchant_information_keys)) {
                    foreach ($merchant_information_keys as $key => $data) {
                        echo "	<payment_methods_instance_setting_key key='" . $key . "' field='" . $data['field'] . "'>";
                        echo "	<mapping><![CDATA[";
                        eval("echo " . $data['mapping'] . ";");
                        echo "]]></mapping>";
                        echo "	<default><![CDATA[";
                        echo $data['default'];
                        echo "]]></default>";
                        switch ($data['field']) {
                            case "select":
                                echo "	<select>";
                                if (count($data['select'])) {
                                    foreach ($data['select'] as $select_value => $select_label) {
                                        echo "<option value='" . $select_value . "'><![CDATA[" . $select_label . "]]></option>";
                                    }
                                }
                                echo "	</select>";
                                break;
                            case "textarea":
                            case "text":
                                if (isset($data['cols'])) {
                                    echo "<cols><![CDATA[" . $data['cols'] . "]]></cols>";
                                }
                                if (isset($data['rows'])) {
                                    echo "<rows><![CDATA[" . $data['rows'] . "]]></rows>";
                                }
                                break;
                        }
                        echo "	</payment_methods_instance_setting_key>";
                    }
                }
            }

            echo "	<payment_methods_settlement_currencies>";
            $payment_methods_instance_select_sql = "	SELECT pgi.payment_methods_outgoing_instance_id, pgi.currency_code, 
															pgi.payment_methods_outgoing_instance_default, c.code, 
															pgi.payment_methods_outgoing_instance_follow_default 
														FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pgi 
														INNER JOIN " . TABLE_CURRENCIES . " as c
															ON c.code = pgi.currency_code
														WHERE payment_methods_id = '" . (int) $pm_id . "'
															ORDER BY c.code";
            $payment_methods_instance_result_sql = tep_db_query($payment_methods_instance_select_sql);
            $assigned_currencies = array();
            $assigned_setting_value = array();
            $payment_methods_instance_array = array();
            $payment_methods_instance_setting_array = array();
            while ($payment_methods_instance_row = tep_db_fetch_array($payment_methods_instance_result_sql)) {
                $assigned_currencies[] = $payment_methods_instance_row['currency_code'];
                $payment_methods_instance_array[$payment_methods_instance_row['payment_methods_outgoing_instance_id']] = array("currency_code" => $payment_methods_instance_row['currency_code'],
                    "code" => $payment_methods_instance_row['code'],
                    "payment_methods_instance_default" => $payment_methods_instance_row['payment_methods_outgoing_instance_default'],
                    "payment_methods_instance_follow_default" => $payment_methods_instance_row['payment_methods_outgoing_instance_follow_default']);

                $payment_methods_instance_setting_select_sql = "SELECT pgis.payment_methods_outgoing_instance_setting_id, pgis.payment_methods_outgoing_instance_setting_key, 
																		pgis.payment_methods_outgoing_instance_setting_value
																FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE_SETTING . " as pgis 
																WHERE pgis.payment_methods_outgoing_instance_id = '" . (int) $payment_methods_instance_row['payment_methods_outgoing_instance_id'] . "'
																	AND pgis.payment_methods_outgoing_instance_setting_key IN ('" . implode("','", array_keys($merchant_information_keys)) . "')";
                $payment_methods_instance_setting_result_sql = tep_db_query($payment_methods_instance_setting_select_sql);
                while ($payment_methods_instance_setting_row = tep_db_fetch_array($payment_methods_instance_setting_result_sql)) {
                    $payment_methods_instance_setting_array[$payment_methods_instance_setting_row['payment_methods_outgoing_instance_setting_key']] = array("id" => (int) $payment_methods_instance_setting_row['payment_methods_outgoing_instance_setting_id'],
                        "value" => $payment_methods_instance_setting_row['payment_methods_outgoing_instance_setting_value']);
                    $assigned_setting_value[(int) $payment_methods_instance_row['payment_methods_outgoing_instance_id']][$payment_methods_instance_setting_row['payment_methods_outgoing_instance_setting_key']] = $payment_methods_instance_setting_row['payment_methods_outgoing_instance_setting_value'];
                }
            }

            foreach ($payment_methods_instance_array as $key => $data) {
                echo "	<payment_methods_settlement_currency>";
                echo "		<payment_methods_instance_id><![CDATA[" . $key . "]]></payment_methods_instance_id>";
                echo "		<payment_methods_instance_follow_default><![CDATA[" . (int) $data['payment_methods_outgoing_instance_follow_default'] . "]]></payment_methods_instance_follow_default>";
                echo "		<currency_code><![CDATA[" . $data['currency_code'] . "]]></currency_code>";
                echo "		<code><![CDATA[" . $data['code'] . "]]></code>";

                foreach ($merchant_information_keys as $mkey => $mdata) {
                    echo "	<payment_methods_instance_setting field='" . $mdata['field'] . "' id='" . $payment_methods_instance_setting_array[$mkey]['id'] . "' key='" . $mkey . "'><![CDATA[";

                    if ((int) $data['payment_methods_instance_follow_default'] > 0) {
                        if (isset($mdata['default']) && tep_not_null($mdata['default']) && !tep_not_null($assigned_setting_value[(int) $data['payment_methods_outgoing_instance_follow_default']][$mkey])) {
                            echo tep_db_output($mdata['default']);
                        } else {
                            echo tep_db_output($assigned_setting_value[(int) $data['payment_methods_outgoing_instance_follow_default']][$mkey]);
                        }
                    } else if (isset($payment_methods_instance_setting_array[$mkey])) {
                        if (isset($mdata['default']) && tep_not_null($mdata['default']) && !tep_not_null($assigned_setting_value[$key][$mkey])) {
                            echo tep_db_output($mdata['default']);
                        } else {
                            echo tep_db_output($assigned_setting_value[$key][$mkey]);
                        }
                    } else if (isset($mdata['default']) && tep_not_null($mdata['default']) && !tep_not_null($assigned_setting_value[$key][$mkey])) {
                        echo tep_db_output($mdata['default']);
                    }

                    echo "]]></payment_methods_instance_setting>";
                }
                echo "		<payment_methods_instance_default><![CDATA[" . ($data['payment_methods_outgoing_instance_default'] ? '1' : '0') . "]]></payment_methods_instance_default>";
                echo "	</payment_methods_settlement_currency>";
            }
            echo "	</payment_methods_settlement_currencies>";
            echo "</display_send_currencies_tab>";
        }
    }

    if ($action == "display_surcharge_tab" || $action == "display_all_tab") {
        $cgrp_id = (isset($_REQUEST['cgrp_id'])) ? (int) $_REQUEST['cgrp_id'] : 1; // default Guest group
        $payment_fees_found = 0;
        $payment_methods_obj = new payment_methods($pm_id);
        $payment_fees_arrays = $payment_methods_obj->get_payment_fees_by_customer_group($pm_id, $cgrp_id, $payment_fees_found);
        $groups_followed = $payment_methods_obj->get_payment_fees_groups_been_followed($pm_id);
        $customers_groups = $payment_methods_obj->get_payment_fees_customers_groups($pm_id);

        reset($payment_fees_arrays);
        echo "<display_surcharge_tab>";
        echo "<payment_fees found='" . $payment_fees_found . "'>";
        echo "	<parent_id><![CDATA[" . $payment_methods_obj->get_parent_id() . "]]></parent_id>";
        echo "	<payment_methods_id><![CDATA[" . htmlentities($pm_id) . "]]></payment_methods_id>";
        foreach ($payment_fees_arrays as $this_pmid => $groups_list) {
            foreach ($groups_list as $this_group_id => $currencies_list) {
                $payment_fees_follows = $payment_methods_obj->get_payment_fees_following_group($pm_id, $this_group_id);
                echo "	<payment_customer_group id='" . htmlentities($this_group_id) . "'>";
                echo "		<payment_follow_customer_group><![CDATA[" . htmlentities($payment_fees_follows['payment_fees_follow_group']) . "]]></payment_follow_customer_group>";
                foreach ($currencies_list as $this_currency_code => $payment_fees_setting_array) {
                    echo "		<payment_fees_setting curr_code='" . $this_currency_code . "'>";
                    if (count($payment_fees_setting_array) > 0) {
                        echo "			<payment_methods_mode><![CDATA[" . htmlentities($payment_fees_setting_array['payment_methods_mode']) . "]]></payment_methods_mode>";
                        echo "			<payment_fees_currency_code><![CDATA[" . htmlentities($payment_fees_setting_array['currency_code']) . "]]></payment_fees_currency_code>";
                        echo "			<payment_fees_id><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_id']) . "]]></payment_fees_id>";
                        echo "			<payment_fees_operator><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_operator']) . "]]></payment_fees_operator>";
                        echo "			<payment_fees_min><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_min']) . "]]></payment_fees_min>";
                        echo "			<payment_fees_cost_value><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_cost_value']) . "]]></payment_fees_cost_value>";
                        echo "			<payment_fees_cost_percent><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_cost_percent']) . "]]></payment_fees_cost_percent>";
                        echo "			<payment_fees_cost_percent_min><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_cost_percent_min']) . "]]></payment_fees_cost_percent_min>";
                        echo "			<payment_fees_cost_percent_max><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_cost_percent_max']) . "]]></payment_fees_cost_percent_max>";
                    } else {
                        echo "			<payment_methods_mode /><currency_code /><payment_fees_id /><payment_fees_operator /><payment_fees_min />";
                        echo "<payment_fees_cost_value /><payment_fees_cost_percent /><payment_fees_cost_percent_min /><payment_fees_cost_percent_max />";
                    }
                    echo "		</payment_fees_setting>";
                }
                echo "	</payment_customer_group>";
            }
        }
        echo "</payment_fees>";

        // Settlement Currency
        echo "<currencies>";
        $currencies_obj = new currencies();
        foreach ($currencies_obj->currencies as $currencies_code => $currencies_data) {
            echo "	<currency id='" . $currencies_code . "'><![CDATA[" . $currencies_code . "]]></currency>";
        }
        echo "</currencies>";

        // customers groups
        echo "<customers_groups>";
        foreach ($customers_groups as $grp_data) {
            echo "	<customers_group id='" . $grp_data['id'] . "' legend_color='" . $grp_data['legend_color'] . "' has_setting='" . ($grp_data['has_setting'] === true ? 1 : 0) . "'><![CDATA[" . $grp_data['name'] . "]]></customers_group>";
        }
        echo "</customers_groups>";

        // customers groups setting been followed by
        echo "<followed_groups>";
        if (count($groups_followed) > 0) {
            foreach ($groups_followed as $grp_id => $grp_name) {
                echo "	<followed_group id='" . $grp_id . "'><![CDATA[" . $grp_name . "]]></followed_group>";
            }
        }
        echo "</followed_groups>";

        echo "</display_surcharge_tab>";
    }

    if ($action == "display_sc_surcharge_tab" || $action == "display_all_tab") {
        $cgrp_id = (isset($_REQUEST['cgrp_id'])) ? (int) $_REQUEST['cgrp_id'] : 1; // default Guest group
        $payment_fees_found = 0;
        $payment_methods_obj = new payment_methods($pm_id);
        $payment_fees_arrays = $payment_methods_obj->get_payment_fees_by_customer_group($pm_id, $cgrp_id, $payment_fees_found, 'sc_topup');
        $groups_followed = $payment_methods_obj->get_payment_fees_groups_been_followed($pm_id, 'sc_topup');
        $customers_groups = $payment_methods_obj->get_payment_fees_customers_groups($pm_id, 'sc_topup');

        reset($payment_fees_arrays);
        echo "<display_sc_surcharge_tab>";
        echo "<payment_fees found='" . $payment_fees_found . "'>";
        echo "	<parent_id><![CDATA[" . $payment_methods_obj->get_parent_id() . "]]></parent_id>";
        echo "	<payment_methods_id><![CDATA[" . htmlentities($pm_id) . "]]></payment_methods_id>";
        foreach ($payment_fees_arrays as $this_pmid => $groups_list) {
            foreach ($groups_list as $this_group_id => $currencies_list) {
                $payment_fees_follows = $payment_methods_obj->get_payment_fees_following_group($pm_id, $this_group_id, 'sc_topup');
                echo "	<payment_customer_group id='" . htmlentities($this_group_id) . "'>";
                echo "		<payment_follow_customer_group><![CDATA[" . htmlentities($payment_fees_follows['payment_fees_follow_group']) . "]]></payment_follow_customer_group>";
                foreach ($currencies_list as $this_currency_code => $payment_fees_setting_array) {
                    echo "		<payment_fees_setting curr_code='" . $this_currency_code . "'>";
                    if (count($payment_fees_setting_array) > 0) {
                        echo "			<payment_methods_mode><![CDATA[" . htmlentities($payment_fees_setting_array['payment_methods_mode']) . "]]></payment_methods_mode>";
                        echo "			<payment_fees_currency_code><![CDATA[" . htmlentities($payment_fees_setting_array['currency_code']) . "]]></payment_fees_currency_code>";
                        echo "			<payment_fees_id><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_id']) . "]]></payment_fees_id>";
                        echo "			<payment_fees_operator><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_operator']) . "]]></payment_fees_operator>";
                        echo "			<payment_fees_min><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_min']) . "]]></payment_fees_min>";
                        echo "			<payment_fees_cost_value><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_cost_value']) . "]]></payment_fees_cost_value>";
                        echo "			<payment_fees_cost_percent><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_cost_percent']) . "]]></payment_fees_cost_percent>";
                        echo "			<payment_fees_cost_percent_min><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_cost_percent_min']) . "]]></payment_fees_cost_percent_min>";
                        echo "			<payment_fees_cost_percent_max><![CDATA[" . htmlentities($payment_fees_setting_array['payment_fees_cost_percent_max']) . "]]></payment_fees_cost_percent_max>";
                    } else {
                        echo "			<payment_methods_mode /><currency_code /><payment_fees_id /><payment_fees_operator /><payment_fees_min />";
                        echo "<payment_fees_cost_value /><payment_fees_cost_percent /><payment_fees_cost_percent_min /><payment_fees_cost_percent_max />";
                    }
                    echo "		</payment_fees_setting>";
                }
                echo "	</payment_customer_group>";
            }
        }
        echo "</payment_fees>";

        // Settlement Currency
        echo "<currencies>";
        $currencies_obj = new currencies();
        foreach ($currencies_obj->currencies as $currencies_code => $currencies_data) {
            echo "	<currency id='" . $currencies_code . "'><![CDATA[" . $currencies_code . "]]></currency>";
        }
        echo "</currencies>";

        // customers groups
        echo "<customers_groups>";
        foreach ($customers_groups as $grp_data) {
            echo "	<customers_group id='" . $grp_data['id'] . "' legend_color='" . $grp_data['legend_color'] . "' has_setting='" . ($grp_data['has_setting'] === true ? 1 : 0) . "'><![CDATA[" . $grp_data['name'] . "]]></customers_group>";
        }
        echo "</customers_groups>";

        // customers groups setting been followed by
        echo "<followed_groups>";
        if (count($groups_followed) > 0) {
            foreach ($groups_followed as $grp_id => $grp_name) {
                echo "	<followed_group id='" . $grp_id . "'><![CDATA[" . $grp_name . "]]></followed_group>";
            }
        }
        echo "</followed_groups>";

        echo "</display_sc_surcharge_tab>";
    }

    if ($action == "display_send_payment_tab" || $action == "display_all_tab") {
        echo "	<display_send_payment_tab>
					<payment_methods_send_status_message><![CDATA[" . tep_db_output($payment_methods_obj->get_send_status_message()) . "]]></payment_methods_send_status_message>
					<payment_methods_send_code><![CDATA[" . tep_db_output($payment_methods_obj->get_send_code()) . "]]></payment_methods_send_code>";

        $payment_fees_info_array = array();
        $payment_fees_select_sql = "SELECT pf.payment_fees_max, pf.payment_fees_min, pf.payment_fees_cost_value, 
											pf.payment_fees_cost_percent, pf.payment_fees_cost_percent_min, 
											pf.payment_fees_cost_percent_max, pf.payment_fees_bear_by,
											pf.payment_fees_below_min 
									FROM " . TABLE_PAYMENT_FEES . " AS pf 
									WHERE pf.payment_methods_id = '" . (int) $pm_id . "'
										AND pf.payment_methods_mode = 'SEND'";
        $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
        if ($payment_fees_row = tep_db_fetch_array($payment_fees_result_sql)) {
            $payment_fees_info_array = $payment_fees_row;
        }

        $payment_fees_select_sql = "SELECT pm.payment_methods_send_required_info, payment_methods_send_mode_name, payment_methods_send_currency, payment_methods_estimated_receive_period
									FROM " . TABLE_PAYMENT_METHODS . " AS pm 
									WHERE pm.payment_methods_id = '" . (int) $pm_id . "'";
        $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
        $payment_fees_row = tep_db_fetch_array($payment_fees_result_sql);
        echo "		<send_mode_name><![CDATA[" . $payment_fees_row['payment_methods_send_mode_name'] . "]]></send_mode_name>";
        echo "		<estimated_receive_period><![CDATA[" . $payment_fees_row['payment_methods_estimated_receive_period'] . "]]></estimated_receive_period>";

        if (count($payment_fees_info_array)) {
            echo "	<payment_fees>";
            $payment_fees_info_return_array = array('payment_fees_max',
                'payment_fees_min',
                'payment_fees_cost_value',
                'payment_fees_cost_percent',
                'payment_fees_cost_percent_min',
                'payment_fees_cost_percent_max',
                'payment_fees_bear_by',
                'payment_fees_below_min',
                'payment_methods_send_required_info');
            $payment_fees_info_array['payment_methods_send_required_info'] = $payment_fees_row['payment_methods_send_required_info'];
            foreach ($payment_fees_info_return_array as $payment_fees_info_return_data_loop) {
                echo "	<" . strtolower($payment_fees_info_return_data_loop) . "><![CDATA[" . (isset($payment_fees_info_array[$payment_fees_info_return_data_loop]) ? $payment_fees_info_array[$payment_fees_info_return_data_loop] : '') . "]]></" . strtolower($payment_fees_info_return_data_loop) . ">";
            }
            echo "	</payment_fees>";
        }
        echo "		<currencies>";
        $currencies_select_sql = "	SELECT currencies_id, code
									FROM " . TABLE_CURRENCIES . "
									WHERE currencies_id = '" . (int) $payment_fees_row['payment_methods_send_currency'] . "' 
									ORDER BY code";
        $currencies_result_sql = tep_db_query($currencies_select_sql);
        if ($currencies_row = tep_db_fetch_array($currencies_result_sql)) {
            echo "		<currency id='" . $currencies_row['currencies_id'] . "'><![CDATA[" . $currencies_row['code'] . "]]></currency>";
        }
        echo "		</currencies>";
        echo "	</display_send_payment_tab>";
    }

    if ($action == "display_send_payment_info_tab" || $action == "display_all_tab") {
        $send_payment_info_array = $payment_methods_obj->get_send_payment_info();
        $field_type_array = array('1' => 'Text Box',
            '2' => 'Text Area',
            '3' => 'Dropdown Menu',
            '4' => 'Radio Button',
            '5' => 'Date Selection',
            '6' => 'Display Label',
            '7' => 'Information Text');

        echo "<display_send_payment_info_tab>";
        if (count($send_payment_info_array)) {
            foreach ($send_payment_info_array as $send_payment_info_id_loop => $send_payment_info_data_loop) {
                echo "<send_payment_info id='" . $send_payment_info_id_loop . "'>";
                foreach ($send_payment_info_data_loop as $send_payment_info_loop_key_loop => $send_payment_info_loop_data_loop) {
                    echo "<" . strtolower($send_payment_info_loop_key_loop) . "><![CDATA[";
                    switch ($send_payment_info_loop_key_loop) {
                        case 'payment_methods_fields_type':
                            echo (isset($field_type_array[$send_payment_info_loop_data_loop]) ? $field_type_array[$send_payment_info_loop_data_loop] : 'n/a');
                            break;
                        case 'payment_methods_fields_sort_order':
                            echo '<input type="text" name="sort_order[' . $send_payment_info_id_loop . ']" sort_order_id="' . $send_payment_info_id_loop . '" class="payment_methods_fields_sort_order" id="sort_order[' . $send_payment_info_id_loop . ']" value="' . $send_payment_info_loop_data_loop . '">';
                            break;
                        case 'payment_methods_fields_status':
                        case 'payment_methods_fields_required':
                            if ($send_payment_info_data_loop['payment_methods_fields_system_type'] == '') {
                                if ($send_payment_info_loop_data_loop == 1) {
                                    echo '<img src="images/icon_status_red_light.gif" alt="" border="0" width="10" height="10" style="cursor:pointer" onclick="update_send_payment(\'' . $send_payment_info_loop_key_loop . '\', \'' . $pm_id . '\',\'' . $send_payment_info_id_loop . '\',  0)">&nbsp;<img src="images/icon_status_green.gif" alt="" border="0" width="10" height="10">';
                                } else {
                                    echo '<img src="images/icon_status_red.gif" alt="" border="0" width="10" height="10">&nbsp;<img src="images/icon_status_green_light.gif" alt="" border="0" width="10" height="10" style="cursor:pointer" onclick="update_send_payment(\'' . $send_payment_info_loop_key_loop . '\', \'' . $pm_id . '\',\'' . $send_payment_info_id_loop . '\', 1)">';
                                }
                            } else {
                                if ($send_payment_info_loop_data_loop == 1) {
                                    echo '<img src="images/icon_status_green.gif" alt="" border="0" width="10" height="10">';
                                } else {
                                    echo '<img src="images/icon_status_green_light.gif" alt="" border="0" width="10" height="10">';
                                }
                            }
                            break;
                        default:
                            echo $send_payment_info_loop_data_loop;
                            break;
                    }
                    echo "]]></" . strtolower($send_payment_info_loop_key_loop) . ">";
                }
                echo "</send_payment_info>";
            }
        } else {
            echo "<![CDATA[no info found]]>";
        }
        echo "</display_send_payment_info_tab>";
    }

    switch ($action) {
        case 'follow_parent_send_payment_info':
            if (isset($_REQUEST['flag'])) {
                $record_created_flag = 0;
                $check_not_parent_select_sql = "SELECT payment_methods_parent_id 
													FROM " . TABLE_PAYMENT_METHODS . "
													WHERE payment_methods_id = '" . $pm_id . "'
														AND payment_methods_parent_id > 0";
                $check_not_parent_result_sql = tep_db_query($check_not_parent_select_sql);
                if ($check_not_parent_row = tep_db_fetch_array($check_not_parent_result_sql)) {
                    if ($_REQUEST['flag'] == 1) {
                        tep_db_query("DELETE FROM " . TABLE_PAYMENT_METHODS_FIELDS . " WHERE payment_methods_id = '" . $pm_id . "'");
                    } else {
                        $payment_methods_fields_array = array();
                        $payment_methods_fields_select_sql = "	SELECT * 
																	FROM " . TABLE_PAYMENT_METHODS_FIELDS . "
																	WHERE payment_methods_id = '" . (int) $check_not_parent_row['payment_methods_parent_id'] . "'";
                        $payment_methods_fields_select_sql = tep_db_query($payment_methods_fields_select_sql);
                        while ($payment_methods_fields_row = tep_db_fetch_array($payment_methods_fields_select_sql)) {
                            $payment_methods_fields_array = $payment_methods_fields_row;
                            unset($payment_methods_fields_array['payment_methods_fields_id']);
                            $payment_methods_fields_array['payment_methods_id'] = $pm_id;
                            tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $payment_methods_fields_array);
                            $record_created_flag = 1;
                        }
                    }
                }
                echo "	<record_created>" . $record_created_flag . "</record_created>";
                echo "	<success>1</success>";
                echo "	<message><![CDATA[Update done]]></message>";
            } else {
                echo "	<success>0</success>";
                echo "	<message><![CDATA[Update failed]]></message>";
            }
            break;

        case 'submit_status_tab';
            $error_flag = true;
            $message = TEXT_INFO_AT_LEAST_ENABLE_ONE;

            // UPDATE 'TABLE_PAYMENT_METHODS'
            $update_payment_methods_sql_data = array();

            // RECEIVE PAYMENT
            if (isset($_POST['rd_receive_payment']) && (int) $_POST['rd_receive_payment']) {
                $update_payment_methods_sql_data['payment_methods_receive_status'] = 1;

                if ((int) $payment_methods_obj->get_parent_id() > 0 && (!isset($_POST['payment_methods_type']) || !tep_not_null($_POST['payment_methods_type']))) {
                    $error_flag = true;
                    $message = TEXT_INFO_SELECT_PAYMENT_METHOD_TYPE;
                } else if (!isset($_POST['payment_methods_title']) || !tep_not_null($_POST['payment_methods_title'])) {
                    $error_flag = true;
                    $message = TEXT_INFO_ENTER_PAYMENT_METHOD_NAME;
                    $error_flag = true;
                } else if (!isset($_POST['payment_methods_description_title']) || !tep_not_null($_POST['payment_methods_description_title'])) {
                    $error_flag = true;
                    $message = TEXT_INFO_ENTER_PAYMENT_METHOD_DISPLAY_NAME;
                } else if (!isset($_POST['payment_methods_status']) || !tep_not_null($_POST['payment_methods_status'])) {
                    $error_flag = true;
                    $message = TEXT_INFO_SELECT_PAYMENT_METHOD_STATUS;
                } else {
                    $error_flag = false;

                    $update_payment_methods_sql_data['payment_methods_receive_status_mode'] = (int) $_POST['payment_methods_status'];
                    $update_payment_methods_sql_data['payment_methods_title'] = tep_db_prepare_input($_POST['payment_methods_title']);
                    $update_payment_methods_sql_data['payment_methods_types_id'] = (int) $_POST['payment_methods_type'];
                    $update_payment_methods_sql_data['payment_methods_receive_featured_status'] = (int) $_POST['payment_methods_receive_featured_status'];

                    // UPDATE 'TABLE_PAYMENT_METHODS_DESCRIPTION'
                    $update_payment_methods_display_sql_data = array();
                    $update_payment_methods_display_sql_data['payment_methods_description_title'] = (isset($_POST['payment_methods_description_title']) ? tep_db_prepare_input($_POST['payment_methods_description_title']) : '');

                    $receive_pm_email_status_array = array();

                    $pm_display_exist_select_sql = "SELECT payment_methods_id, payment_methods_description_title 
													FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
													WHERE payment_methods_id = '" . (int) $pm_id . "'
														AND languages_id = '1'";
                    $pm_display_exist_result_sql = tep_db_query($pm_display_exist_select_sql);
                    if ($pm_display_exist_row = tep_db_fetch_array($pm_display_exist_result_sql)) {
                        if (strcmp($pm_display_exist_row['payment_methods_description_title'], tep_db_prepare_input($_POST['payment_methods_description_title'])) != 0) {
                            $receive_pm_email_status_array[] = "<td valign='top'>Description Title</td><td valign='top'>" . $pm_display_exist_row['payment_methods_description_title'] . "</td><td valign='top'>" . (isset($_POST['payment_methods_description_title']) ? tep_db_prepare_input($_POST['payment_methods_description_title']) : '') . "</td>";
                        }
                        tep_db_perform(TABLE_PAYMENT_METHODS_DESCRIPTION, $update_payment_methods_display_sql_data, 'update', "	payment_methods_id = '" . (int) $pm_id . "' and languages_id='1' ");
                    } else {
                        $receive_pm_email_status_array[] = "<td valign='top'>Description Title</td><td valign='top'>null</td><td valign='top'>" . (isset($_POST['payment_methods_description_title']) ? tep_db_prepare_input($_POST['payment_methods_description_title']) : '') . "</td>";

                        $update_payment_methods_display_sql_data['payment_methods_id'] = (int) $pm_id;
                        $update_payment_methods_display_sql_data['languages_id'] = 1;
                        tep_db_perform(TABLE_PAYMENT_METHODS_DESCRIPTION, $update_payment_methods_display_sql_data);
                    }

                    // UPDATE 'TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION'
                    $update_status_sql_data = array();
                    if (isset($_POST['payment_methods_status_message'])) {
                        $update_status_sql_data['payment_methods_status_message'] = tep_db_prepare_input($_POST['payment_methods_status_message']);
                        $pm_status_exist_select_sql = "	SELECT payment_methods_id
														FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
														WHERE payment_methods_id = '" . (int) $pm_id . "' 
															AND languages_id = '1' 
															AND payment_methods_mode = 'RECEIVE' 
															AND payment_methods_status = '-1'";
                        $pm_status_exist_result_sql = tep_db_query($pm_status_exist_select_sql);
                        if ($pm_status_exist_row = tep_db_fetch_array($pm_status_exist_result_sql)) {
                            if ($pm_status_exist_row['payment_methods_status_message'] != tep_db_input($_POST['payment_methods_status_message'])) {
                                $receive_pm_email_status_array[] = "<td valign='top'>Inactive Message</td><td valign='top'>" . $pm_status_exist_row['payment_methods_status_message'] . "</td><td valign='top'>" . (isset($_POST['payment_methods_status_message']) ? tep_db_prepare_input($_POST['payment_methods_status_message']) : '') . "</td>";
                            }
                            tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_sql_data, 'update', "	payment_methods_id = '" . (int) $pm_id . "' and 
																															languages_id='1' and 
																															payment_methods_mode='RECEIVE' and 
																															payment_methods_status = '-1'");
                        } else {
                            $receive_pm_email_status_array[] = "<td valign='top'>Inactive Message</td><td valign='top'>null</td><td valign='top'>" . (isset($_POST['payment_methods_status_message']) ? tep_db_prepare_input($_POST['payment_methods_status_message']) : '') . "</td>";

                            $update_status_sql_data['payment_methods_id'] = (int) $pm_id;
                            $update_status_sql_data['languages_id'] = 1;
                            $update_status_sql_data['payment_methods_status'] = '-1';
                            $update_status_sql_data['payment_methods_mode'] = 'RECEIVE';
                            tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_sql_data);
                        }
                    }
                }
            } else {
                $update_payment_methods_sql_data['payment_methods_receive_status'] = 0;
            }

            // OUTGOING PAYMENT
            if (($update_payment_methods_sql_data['payment_methods_receive_status'] == 0 || !$error_flag)) {
                if (isset($_POST['rd_outgoing_payment']) && (int) $_POST['rd_outgoing_payment']) {
                    $update_payment_methods_sql_data['payment_methods_send_status'] = 1;

                    if (!isset($_POST['payment_methods_send_status'])) {
                        $error_flag = true;
                        $message = TEXT_INFO_SELECT_PAYMENT_METHOD_SEND_STATUS;
                    } else if (!isset($_POST['send_mode_name']) || !tep_not_null($_POST['send_mode_name'])) {
                        $error_flag = true;
                        $message = TEXT_INFO_ENTER_PAYMENT_METHOD_SEND_MODE_NAME;
                    } else if ((int) $payment_methods_obj->get_parent_id() > 0 && (!isset($_POST['payment_methods_type']) || !tep_not_null($_POST['payment_methods_type']))) {
                        $error_flag = true;
                        $message = TEXT_INFO_SELECT_PAYMENT_METHOD_TYPE;
                    } else {
                        $error_flag = false;

                        $update_payment_methods_sql_data['payment_methods_types_id'] = (int) $_POST['payment_methods_type'];

                        if ((int) $_POST['payment_methods_type'] == 6) {
                            $update_payment_methods_sql_data['payment_methods_send_currency'] = 0;
                            $delete_outgoing_instance_data_sql = "  DELETE FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . "
                                                                    WHERE payment_methods_id = '" . (int) $pm_id . "'";
                            tep_db_query($delete_outgoing_instance_data_sql);
                        }

                        if (isset($_REQUEST['sel_outgoing_currency'])) {
                            if ((int) $_POST['payment_methods_type'] != 6) {
                                if ((int) $_REQUEST['sel_outgoing_currency'] > 0) {
                                    //prevent of changing currency
                                    $pm_select_sql = "  SELECT payment_methods_send_currency 
                                                        FROM " . TABLE_PAYMENT_METHODS . "
                                                        WHERE payment_methods_id = '" . (int) $pm_id . "'";
                                    $pm_result_sql = tep_db_query($pm_select_sql);
                                    $pm_row = tep_db_fetch_array($pm_result_sql);
                                    if ($pm_row['payment_methods_send_currency'] == 0) {
                                        $update_payment_methods_sql_data['payment_methods_send_currency'] = (int) $_REQUEST['sel_outgoing_currency'];
                                    }

                                    // insert to payment methods outgoing instance table  // will remove after multiple currency supported for outgoing 
                                    $payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_outgoing_instance_id, pmi.payment_methods_outgoing_instance_follow_default, pmi.currency_code 
                                                                            FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pmi
                                                                            WHERE pmi.payment_methods_id = '" . (int) $pm_id . "'";
                                    $payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
                                    if (!tep_db_num_rows($payment_gateway_instance_result_sql)) {
                                        $currency_select_sql = "SELECT c.code 
                                                                FROM " . TABLE_CURRENCIES . " as c
                                                                WHERE currencies_id = '" . (int) $_REQUEST['sel_outgoing_currency'] . "'";
                                        $currency_result_sql = tep_db_query($currency_select_sql);
                                        $currency_row = tep_db_fetch_array($currency_result_sql);

                                        $payment_gateway_instance_data_sql = array('payment_methods_id' => (int) $pm_id,
                                            'currency_code' => tep_db_prepare_input($currency_row['code']));
                                        tep_db_perform(TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE, $payment_gateway_instance_data_sql);
                                    }
                                } else {
                                    $error_flag = true;
                                    $message = "Please select outgoing payment's currency.";
                                }
                            }
                        }

                        if (!$error_flag) {
                            $update_payment_methods_sql_data['payment_methods_send_status_mode'] = (int) $_POST['payment_methods_send_status'];
                            $update_payment_methods_sql_data['payment_methods_send_mode_name'] = tep_db_prepare_input($_POST['send_mode_name']);
                            $update_payment_methods_sql_data['payment_methods_send_mass_payment'] = (int) (isset($_POST['payment_methods_send_mass_payment']) ? $_POST['payment_methods_send_mass_payment'] : 0);
                            $update_payment_methods_sql_data['payment_methods_send_available_sites'] = (isset($_POST['payment_methods_send_sites']) ? implode(",", $_POST['payment_methods_send_sites']) : '0');

                            // UPDATE 'TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION'  
                            $update_status_sql_data = array();
                            if (isset($_POST['payment_methods_send_status_message'])) {
                                $update_status_sql_data['payment_methods_status_message'] = tep_db_prepare_input($_POST['payment_methods_send_status_message']);
                                $pm_status_exist_select_sql = "	SELECT payment_methods_id 
																FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
																WHERE payment_methods_id = '" . (int) $pm_id . "' 
																	AND languages_id = '1' 
																	AND payment_methods_mode = 'SEND' 
																	AND payment_methods_status = '-1'";
                                $pm_status_exist_result_sql = tep_db_query($pm_status_exist_select_sql);
                                if (tep_db_num_rows($pm_status_exist_result_sql)) {
                                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_sql_data, 'update', "	payment_methods_id = '" . (int) $pm_id . "' and 
																																	languages_id='1' and 
																																	payment_methods_mode='SEND' and 
																																	payment_methods_status = '-1'");
                                } else {
                                    $update_status_sql_data['payment_methods_id'] = (int) $pm_id;
                                    $update_status_sql_data['languages_id'] = 1;
                                    $update_status_sql_data['payment_methods_status'] = '-1';
                                    $update_status_sql_data['payment_methods_mode'] = 'SEND';
                                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_sql_data);
                                }
                            }
                            $update_status_code_sql_data = array();
                            if (isset($_POST['payment_methods_send_code'])) {
                                $update_status_code_sql_data['payment_methods_status_message'] = tep_db_prepare_input($_POST['payment_methods_send_code']);
                                $pm_status_exist_select_sql = "	SELECT payment_methods_id 
																FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
																WHERE payment_methods_id = '" . (int) $pm_id . "' 
																	AND languages_id = '1' 
																	AND payment_methods_mode = 'SEND_CODE' 
																	AND payment_methods_status = '-1'";
//                                echo $pm_status_exist_select_sql;die;
                                $pm_status_exist_result_sql = tep_db_query($pm_status_exist_select_sql);
                                if (tep_db_num_rows($pm_status_exist_result_sql)) {
                                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_code_sql_data, 'update', "	payment_methods_id = '" . (int) $pm_id . "' and 
																																	languages_id='1' and 
																																	payment_methods_mode='SEND_CODE' and 
																																	payment_methods_status = '-1'");
                                } else {
                                    $update_status_code_sql_data['payment_methods_id'] = (int) $pm_id;
                                    $update_status_code_sql_data['languages_id'] = 1;
                                    $update_status_code_sql_data['payment_methods_status'] = '-1';
                                    $update_status_code_sql_data['payment_methods_mode'] = 'SEND_CODE';
                                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_code_sql_data);
                                }
                            }
                        }
                    }
                } else {
                    $update_payment_methods_sql_data['payment_methods_send_status'] = 0;
                }
            }

            if ($error_flag) {
                echo "	<success>0</success>";
            } else {
                $update_payment_methods_sql_data['last_modified'] = 'now()';
                if (isset($_POST['payment_methods_sort_order']))
                    $update_payment_methods_sql_data['payment_methods_sort_order'] = (int) $_POST['payment_methods_sort_order'];
                if (isset($_POST['payment_methods_legend_color']))
                    $update_payment_methods_sql_data['payment_methods_legend_color'] = $_POST['payment_methods_legend_color'];
                if (isset($_POST['remove_logo']) && $_POST['remove_logo']) {
                    $aws_obj = new ogm_amazon_ws();

                    $payment_methods_logo_select_sql = "	SELECT payment_methods_logo 
															FROM " . TABLE_PAYMENT_METHODS . "
															WHERE payment_methods_id = '" . $pm_id . "'";
                    $payment_methods_logo_result_sql = tep_db_query($payment_methods_logo_select_sql);
                    if ($payment_methods_logo_row = tep_db_fetch_array($payment_methods_logo_result_sql)) {
                        if ($aws_obj->is_aws_s3_enabled()) {
                            // Remove from S3
                            $aws_obj->delete_file($payment_methods_logo_row['payment_methods_logo'], 'images/payment/', 'BUCKET_STATIC');
                            $pg_logo_filepath = $aws_obj->get_image_url_by_instance();
                        } else {
                            @unlink(DIR_FS_CATALOG_IMAGES . 'payment/' . $payment_methods_logo_row['payment_methods_logo']);
                            $pg_logo_filepath = DIR_FS_CATALOG_IMAGES . 'payment/' . $payment_methods_logo_row['payment_methods_logo'];
                        }
                        $receive_pm_email_array[] = 'Remove Payment\'s Logo: ' . $pg_logo_filepath;
                    }
                    $update_payment_methods_sql_data['payment_methods_logo'] = '';

                    unset($aws_obj);
                }

                $payment_methods_select_sql = "	SELECT payment_methods_title, payment_methods_types_id, payment_methods_receive_status_mode, 
													payment_methods_sort_order, payment_methods_legend_color, payment_methods_receive_status 
												FROM " . TABLE_PAYMENT_METHODS . "
												WHERE payment_methods_id = '" . $pm_id . "'";
                $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
                $payment_methods_row = tep_db_fetch_array($payment_methods_result_sql);
                if ($update_payment_methods_sql_data['payment_methods_receive_status'] == 1) {

                    $payment_methods_types_array = array();
                    $payment_methods_types_sql = "	SELECT payment_methods_types_id, payment_methods_types_name 
													FROM " . TABLE_PAYMENT_METHODS_TYPES . "";
                    $payment_methods_types_result_sql = tep_db_query($payment_methods_types_sql);
                    while ($payment_methods_types_row = tep_db_fetch_array($payment_methods_types_result_sql)) {
                        $payment_methods_types_array[$payment_methods_types_row['payment_methods_types_id']] = $payment_methods_types_row['payment_methods_types_name'];
                    }

                    if ($payment_methods_row['payment_methods_receive_status'] != tep_db_input($update_payment_methods_sql_data['payment_methods_receive_status']))
                        $receive_pm_email_status_array[] = "<td valign='top'>Receive Payment</td><td valign='top'>Disable</td><td valign='top'>Enable</td>";
                    if ($payment_methods_row['payment_methods_title'] != tep_db_input($_POST['payment_methods_title']))
                        $receive_pm_email_status_array[] = "<td valign='top'>Title</td><td valign='top'>" . $payment_methods_row['payment_methods_title'] . "</td><td valign='top'>" . tep_db_input($_POST['payment_methods_title']) . '</td>';
                    if ($payment_methods_row['payment_methods_types_id'] != (int) $_POST['payment_methods_type'])
                        $receive_pm_email_status_array[] = "<td valign='top'>Type</td><td valign='top'>" . ( isset($payment_methods_types_array[$payment_methods_row['payment_methods_types_id']]) ? $payment_methods_types_array[$payment_methods_row['payment_methods_types_id']] : $payment_methods_row['payment_methods_types_id']) . "</td><td valign='top'>" . ( isset($payment_methods_types_array[$_POST['payment_methods_type']]) ? $payment_methods_types_array[$_POST['payment_methods_type']] : $_POST['payment_methods_type']) . '</td>';
                    if ($payment_methods_row['payment_methods_receive_status_mode'] != (int) $_POST['payment_methods_status'])
                        $receive_pm_email_status_array[] = "<td valign='top'>Receive Status</td><td valign='top'>" . $payment_methods_row['payment_methods_receive_status_mode'] . "</td><td valign='top'>" . tep_db_input($_POST['payment_methods_status']) . '</td>';
                    if ($payment_methods_row['payment_methods_sort_order'] != tep_db_input($_POST['payment_methods_sort_order']))
                        $receive_pm_email_status_array[] = "<td valign='top'>Sort Order</td><td valign='top'>" . $payment_methods_row['payment_methods_sort_order'] . "</td><td valign='top'>" . tep_db_input($_POST['payment_methods_sort_order']) . '</td>';
                    if (isset($_POST['payment_methods_legend_color'])) {
                        if ($payment_methods_row['payment_methods_legend_color'] != tep_db_input($_POST['payment_methods_legend_color']))
                            $receive_pm_email_status_array[] = "<td valign='top'>Legend Color</td><td valign='top'>" . $payment_methods_row['payment_methods_legend_color'] . "</td><td valign='top'>" . tep_db_input($_POST['payment_methods_legend_color']) . '</td>';
                    }
                } else {
                    if ($payment_methods_row['payment_methods_receive_status'] != tep_db_input($update_payment_methods_sql_data['payment_methods_receive_status']))
                        $receive_pm_email_status_array[] = "<td valign='top'>Receive Payment</td><td valign='top'>Enable</td><td valign='top'>Disable</td>";
                }

                tep_db_perform(TABLE_PAYMENT_METHODS, $update_payment_methods_sql_data, 'update', "payment_methods_id = '" . (int) $pm_id . "'");

                $message = TEXT_INFO_DATA_UPDATED;

                echo "	<success>1</success>";
            }
            echo "	<message><![CDATA[" . $message . "]]></message>";

            if (count($receive_pm_email_status_array)) {
                $receive_pm_email_array[] = "Changes Of Payment Status:<br><table border='1' width='100%' cellspacing='0' cellpadding='0'><tr><td>&nbsp;</td><td><b>Old</b></td><td><b>New</b></td></tr><tr valign='top'>" . implode("</tr><tr>", $receive_pm_email_status_array) . "</tr></table>";
            }

            break;
        case 'upload_logo':
            $error_flag = true;
            $msg = '';
            if (isset($_REQUEST['file_name']) && $pm_id > 0) {
                $fileElementName = $_REQUEST['file_name'];
                if (!empty($_FILES[$fileElementName]['error'])) {
                    switch ($_FILES[$fileElementName]['error']) {
                        case '1':
                            $msg = 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
                            break;
                        case '2':
                            $msg = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
                            break;
                        case '3':
                            $msg = 'The uploaded file was only partially uploaded';
                            break;
                        case '4':
                            $msg = 'No file was uploaded.';
                            break;
                        case '6':
                            $msg = 'Missing a temporary folder';
                            break;
                        case '7':
                            $msg = 'Failed to write file to disk';
                            break;
                        case '8':
                            $msg = 'File upload stopped by extension';
                            break;
                        case '999':
                        default:
                            $msg = 'No error code avaiable';
                    }
                } elseif (empty($_FILES[$fileElementName]['tmp_name']) || $_FILES[$fileElementName]['tmp_name'] == 'none') {
                    $msg = 'No file was uploaded..';
                } else {
                    $error_flag = false;
                    $file_details = pathinfo($_FILES[$fileElementName]['name']);
                    $aws_obj = new ogm_amazon_ws();

                    // Upload to S3
                    $_FILES['tmp_name'] = $_FILES[$fileElementName]['tmp_name'];

                    if ($aws_obj->is_aws_s3_enabled()) {
                        $aws_obj->set_bucket_key('BUCKET_STATIC');
                        $aws_obj->set_filepath('images/payment/');
                        $aws_obj->set_storage('STORAGE_STANDARD');
                        $aws_obj->set_acl('ACL_PUBLIC');

                        //delete old image
                        $logo_select_sql = "	SELECT payment_methods_logo
													FROM " . TABLE_PAYMENT_METHODS . "
													WHERE payment_methods_id = '" . $pm_id . "'";
                        $logo_result_sql = tep_db_query($logo_select_sql);
                        if ($logo_row = tep_db_fetch_array($logo_result_sql)) {
                            $aws_obj->delete_file($logo_row['payment_methods_logo']);
                        }

                        $aws_obj->set_filename($_GET['pm_id'] . "_logo." . $file_details['extension']);
                        $aws_obj->set_file($_FILES);
                        $aws_obj->save_file();
                        $pg_logo_filepath = $aws_obj->get_image_url_by_instance();
                    } else {
                        //delete old image
                        $logo_select_sql = "	SELECT payment_methods_logo
													FROM " . TABLE_PAYMENT_METHODS . "
													WHERE payment_methods_id = '" . $pm_id . "'";
                        $logo_result_sql = tep_db_query($logo_select_sql);
                        if ($logo_row = tep_db_fetch_array($logo_result_sql)) {
                            if (file_exists(DIR_FS_CATALOG_IMAGES . 'payment/' . $logo_row['payment_methods_logo'])) {
                                @unlink(DIR_FS_CATALOG_IMAGES . 'payment/' . $logo_row['payment_methods_logo']);
                            }
                        }

                        move_uploaded_file($_FILES['tmp_name'], DIR_FS_CATALOG_IMAGES . 'payment/' . $_GET['pm_id'] . "_logo." . $file_details['extension']);
                        $pg_logo_filepath = DIR_FS_CATALOG_IMAGES . 'payment/' . $pm_id . "_logo." . $file_details['extension'];
                    }

                    //update db
                    $update_payment_method_logo_sql = "	UPDATE " . TABLE_PAYMENT_METHODS . "
															SET payment_methods_logo = '" . tep_db_input($_GET['pm_id'] . "_logo." . $file_details['extension']) . "'
															WHERE payment_methods_id = '" . $pm_id . "'";
                    tep_db_query($update_payment_method_logo_sql);

                    @unlink($_FILES[$fileElementName]);

                    $msg = "<path><![CDATA[" . $pg_logo_filepath . "]]></path>";
                    $receive_pm_email_array[] = 'New Payment\'s Logo: ' . $pg_logo_filepath;
                    unset($aws_obj);
                }
            }
            if ($error_flag) {
                echo "<success>0</success>";
            } else {
                echo "<success>1</success>";
            }
            break;
        case 'submit_setting_tab':
            $error_flag = true;
            if (!isset($_POST['payment_methods_code']) || !tep_not_null($_POST['payment_methods_code'])) {
                $msg = "System code couldn't be empty.";
            } else {
                $code = $_POST['payment_methods_code'];
                $code_select_sql = "	SELECT payment_methods_code
										FROM " . TABLE_PAYMENT_METHODS . "
										WHERE payment_methods_code = '" . tep_db_input($code) . "'
											AND payment_methods_id != '" . (int) $pm_id . "'
											AND (payment_methods_parent_id = '" . (int) $payment_methods_obj->get_parent_id() . "')";
                $code_result_sql = tep_db_query($code_select_sql);
                if (tep_db_num_rows($code_result_sql)) {
                    $msg = "Duplicated system code.";
                } else {

                    $payment_methods_title_array = array();
                    $payment_methods_title_select_sql = "	SELECT payment_methods_id, payment_methods_title 
															FROM " . TABLE_PAYMENT_METHODS . " AS pm";
                    $payment_methods_title_result_sql = tep_db_query($payment_methods_title_select_sql);
                    while ($payment_methods_title_row = tep_db_fetch_array($payment_methods_title_result_sql)) {
                        $payment_methods_title_array[$payment_methods_title_row['payment_methods_id']] = $payment_methods_title_row['payment_methods_title'];
                    }

                    $error_flag = false;
                    $receive_pm_email_setting_array = array();

                    $current_code_select_sql = "SELECT payment_methods_code
												FROM " . TABLE_PAYMENT_METHODS . "
												WHERE payment_methods_id = '" . (int) $pm_id . "'";
                    $current_code_result_sql = tep_db_query($current_code_select_sql);
                    $current_code_row = tep_db_fetch_array($current_code_result_sql);
                    if ($current_code_row['payment_methods_code'] != tep_db_input($code)) {
                        $receive_pm_email_setting_array[] = "System code: " . $current_code_row['payment_methods_code'] . " ->" . $code;
                    }

                    $update_code_sql_data = array("payment_methods_code" => tep_db_prepare_input($code));
                    tep_db_perform(TABLE_PAYMENT_METHODS, $update_code_sql_data, "update", " payment_methods_id = '" . (int) $pm_id . "'");

                    $selected_customers_groups_array = array();
                    if (isset($_REQUEST['chk_customers_groups']) && count($_REQUEST['chk_customers_groups'])) {
                        $selected_customers_groups_array = $_REQUEST['chk_customers_groups'];
                    }

                    $existing_customers_groups_with_pm_array = array();
                    $customers_groups_with_pm_select_sql = "SELECT customers_groups_id 
															FROM " . TABLE_CUSTOMERS_GROUPS . "
															WHERE FIND_IN_SET(" . (int) $pm_id . ", customers_groups_payment_methods)";
                    $customers_groups_with_pm_result_sql = tep_db_query($customers_groups_with_pm_select_sql);
                    while ($customers_groups_with_pm_row = tep_db_fetch_array($customers_groups_with_pm_result_sql)) {
                        $existing_customers_groups_with_pm_array[] = $customers_groups_with_pm_row['customers_groups_id'];
                    }

                    $customers_groups_array = array();
                    $customers_groups_select_sql = "	SELECT customers_groups_id, customers_groups_payment_methods, customers_groups_name
														FROM " . TABLE_CUSTOMERS_GROUPS;
                    $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
                    while ($customers_groups_row = tep_db_fetch_array($customers_groups_result_sql)) {
                        $customers_groups_array[$customers_groups_row['customers_groups_id']] = $customers_groups_row['customers_groups_name'];
                        $selected_payment_methods_array = array();
                        if (tep_not_null($customers_groups_row['customers_groups_payment_methods'])) {
                            $selected_payment_methods_array = explode(",", $customers_groups_row['customers_groups_payment_methods']);
                        }

                        if (in_array($customers_groups_row['customers_groups_id'], $selected_customers_groups_array)) {
                            if (!in_array((int) $pm_id, $selected_payment_methods_array)) {
                                $selected_payment_methods_array[] = (int) $pm_id;
                                $payment_methods_data_sql = array('customers_groups_payment_methods' => implode(",", $selected_payment_methods_array));
                                tep_db_perform(TABLE_CUSTOMERS_GROUPS, $payment_methods_data_sql, 'update', " customers_groups_id = '" . (int) $customers_groups_row['customers_groups_id'] . "' ");
                            }
                        } else {
                            if (in_array((int) $pm_id, $selected_payment_methods_array)) {
                                $new_payment_methods_array = array();
                                foreach ($selected_payment_methods_array as $selected_payment_methods_loop) {
                                    if ($selected_payment_methods_loop != $pm_id) {
                                        $new_payment_methods_array[] = $selected_payment_methods_loop;
                                    }
                                }
                                $payment_methods_data_sql = array('customers_groups_payment_methods' => implode(",", $new_payment_methods_array));
                                tep_db_perform(TABLE_CUSTOMERS_GROUPS, $payment_methods_data_sql, 'update', " customers_groups_id = '" . (int) $customers_groups_row['customers_groups_id'] . "' ");
                            }
                        }
                    }

                    $new_selected_customers_groups_array = array();
                    foreach ($selected_customers_groups_array as $selected_customers_groups_id_loop) {
                        if (!in_array($selected_customers_groups_id_loop, $existing_customers_groups_with_pm_array)) {
                            $new_selected_customers_groups_array[] = $selected_customers_groups_id_loop;
                        }
                    }
                    if (count($new_selected_customers_groups_array)) {
                        $receive_pm_email_customers_groups_str = 'New Customers Groups Applied: <ul>';
                        foreach ($new_selected_customers_groups_array as $new_selected_customers_groups_id_loop) {
                            $receive_pm_email_customers_groups_str .= '<li>' . (isset($customers_groups_array[$new_selected_customers_groups_id_loop]) ? $customers_groups_array[$new_selected_customers_groups_id_loop] : '') . '(' . $new_selected_customers_groups_id_loop . ')</li>';
                        }
                        $receive_pm_email_customers_groups_str .= '</ul>';
                        $receive_pm_email_setting_array[] = $receive_pm_email_customers_groups_str;
                    }

                    $removed_customers_groups_array = array();
                    foreach ($existing_customers_groups_with_pm_array as $existing_customers_groups_id_loop) {
                        if (!in_array($existing_customers_groups_id_loop, $selected_customers_groups_array)) {
                            $removed_customers_groups_array[] = $existing_customers_groups_id_loop;
                        }
                    }
                    if (count($removed_customers_groups_array)) {
                        $receive_pm_email_customers_groups_str = 'Removed Customers Groups: <ul>';
                        foreach ($removed_customers_groups_array as $removed_customers_groups_id_loop) {
                            $receive_pm_email_customers_groups_str .= '<li>' . (isset($customers_groups_array[$removed_customers_groups_id_loop]) ? $customers_groups_array[$removed_customers_groups_id_loop] : '') . '(' . $removed_customers_groups_id_loop . ')</li>';
                        }
                        $receive_pm_email_customers_groups_str .= '</ul>';
                        $receive_pm_email_setting_array[] = $receive_pm_email_customers_groups_str;
                    }

                    if (count($receive_pm_email_setting_array)) {
                        $receive_pm_email_array[] = implode("<br>", $receive_pm_email_setting_array);
                    }
                    unset($receive_pm_email_setting_array);

                    if (isset($_REQUEST['use_parent']) && $_REQUEST['use_parent'] == 1) {

                        $payment_configuration_select_sql = "	SELECT payment_configuration_info_id
																FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . "
																WHERE payment_methods_id = '" . (int) $pm_id . "'";
                        $payment_configuration_result_sql = tep_db_query($payment_configuration_select_sql);
                        if (tep_db_num_rows($payment_configuration_result_sql)) {
                            $receive_pm_email_array[] = "Remove Payment Method's Configuration: Follow Parent Setting";
                        }

                        while ($payment_configuration_row = tep_db_fetch_array($payment_configuration_result_sql)) {
                            $delete_payment_configuration_display_sql = "	DELETE FROM " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . "
																			WHERE payment_configuration_info_id = '" . (int) $payment_configuration_row['payment_configuration_info_id'] . "'";
                            tep_db_query($delete_payment_configuration_display_sql);
                        }
                        $delete_payment_configuration_sql = "	DELETE FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . "
																WHERE payment_methods_id = '" . (int) $pm_id . "'";
                        tep_db_query($delete_payment_configuration_sql);
                    } else {

                        $message = array();
                        $receive_pm_email_configuration_array = array();

                        $payment_configuration_info_array = $payment_methods_obj->get_payment_method_configuration_key();
                        foreach ($payment_configuration_info_array as $key => $data) {
                            $pci_id = 0;
                            $payment_methods_configuration_select_sql = "	SELECT payment_configuration_info_id
																			FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . "
																			WHERE payment_methods_id = '" . (int) $pm_id . "'
																				AND payment_configuration_info_key = '" . $key . "'";
                            $payment_methods_configuration_result_sql = tep_db_query($payment_methods_configuration_select_sql);
                            if ($payment_methods_configuration_row = tep_db_fetch_array($payment_methods_configuration_result_sql)) {
                                $pci_id = $payment_methods_configuration_row['payment_configuration_info_id'];
                            } else {
                                $payment_methods_configuration_sql_data = array('payment_configuration_info_title' => $data['payment_configuration_info_title'],
                                    'payment_configuration_info_description' => $data['payment_configuration_info_description'],
                                    'use_function' => $data['use_function'],
                                    'set_function' => $data['set_function'],
                                    'payment_methods_id' => (int) $pm_id,
                                    'payment_configuration_info_key' => $key
                                );
                                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $payment_methods_configuration_sql_data);
                                $pci_id = tep_db_insert_id();
                            }
                            $configuration_value_select_sql = "	SELECT pcid.payment_configuration_info_id, payment_configuration_info_value 
																FROM " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
																WHERE pcid.payment_configuration_info_id = '" . (int) $pci_id . "'
																	AND pcid.languages_id = '1'";
                            $configuration_value_result_sql = tep_db_query($configuration_value_select_sql);
                            if (is_array($_POST["configuration"][$key])) {
                                $insert_value = implode(", ", tep_db_prepare_input($_POST["configuration"][$key]));
                            } else {
                                $insert_value = tep_db_prepare_input($_POST["configuration"][$key]);
                            }
                            if ($configuration_value_row = tep_db_fetch_array($configuration_value_result_sql)) {

                                if (tep_db_input($configuration_value_row['payment_configuration_info_value']) != tep_db_input($insert_value)) {
                                    $receive_pm_email_configuration_array[] = "<td>" . $data['payment_configuration_info_title'] . '</td><td>' . $configuration_value_row['payment_configuration_info_value'] . '</td><td>' . $insert_value . '</td>';
                                }

                                $configuration_value_sql_data = array('payment_configuration_info_value' => $insert_value);
                                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $configuration_value_sql_data, 'update', "payment_configuration_info_id = '" . (int) $pci_id . "' AND languages_id = '1'");
                            } else {

                                $receive_pm_email_configuration_array[] = "<td>" . $data['payment_configuration_info_title'] . '</td><td>[copy from parent]</td><td>' . $data['payment_configuration_info_value'] . '</td>';

                                $configuration_value_sql_data = array('payment_configuration_info_value' => tep_db_prepare_input($data['payment_configuration_info_value']),
                                    'languages_id' => 1,
                                    'payment_configuration_info_id' => (int) $pci_id);

                                tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $configuration_value_sql_data);
                            }
                        }

                        if (count($receive_pm_email_configuration_array)) {
                            $receive_pm_email_array[] = "Payment Configuration<br><table border='1' width='100%' cellspacing='0' cellpadding='0'><tr><td>&nbsp;</td><td><b>Old</b></td><td><b>New</b></td></tr><tr valign='top'>" . implode("</tr><tr valign='top'>", $receive_pm_email_configuration_array) . "</tr></table>";
                        }
                    }
                    $msg = TEXT_INFO_DATA_UPDATED;
                }
            }
            if ($error_flag) {
                echo "	<success>0</success>";
            } else {
                echo "	<success>1</success>";
            }
            echo "	<message><![CDATA[" . $msg . "]]></message>";
            break;
        case 'currency_dependency':
            $error_flag = false;
            $payment_methods_instance_select_sql = "	SELECT pmi.payment_methods_instance_id
														FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi 
														WHERE pmi.payment_methods_id = '" . (int) $pm_id . "'";
            $payment_methods_instance_result_sql = tep_db_query($payment_methods_instance_select_sql);
            $payment_methods_instance_array = array();
            while ($payment_methods_instance_row = tep_db_fetch_array($payment_methods_instance_result_sql)) {
                $payment_methods_instance_array[] = $payment_methods_instance_row['payment_methods_instance_id'];
            }
            if (count($payment_methods_instance_array)) {
                $receive_pm_email_array[] = 'Delete Merchant Setting: Follow Parent';

                $payment_methods_instance_str = implode("','", $payment_methods_instance_array);

                //delete 'TABLE_PAYMENT_METHODS_INSTANCE_SETTING'
                $delete_payment_methods_instance_setting_sql = "DELETE FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . "
																WHERE payment_methods_instance_id IN ('" . $payment_methods_instance_str . "')";
                tep_db_query($delete_payment_methods_instance_setting_sql);

                //delete 'TABLE_PAYMENT_METHODS_INSTANCE'
                $delete_payment_methods_instance_sql = "DELETE FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
                tep_db_query($delete_payment_methods_instance_sql);
            }
            echo "<success>1</success>";
            break;
        case 'display_insert_payment_methods_tab':
            echo "<payment_methods_title><![CDATA[" . $payment_methods_obj->get_title() . "]]></payment_methods_title>";
            foreach ($payment_methods_obj->get_payment_methods_types() as $id => $text) {
                echo "	<payment_methods_type id='" . $id . "' select='" . (int) ($payment_methods_obj->get_types_id() == $id ? 1 : 0) . "'>" . $text . "</payment_methods_type>";
            }
            echo "		<payment_methods_send_types>";
            foreach ($payment_methods_obj->get_payment_methods_types('SEND') as $id => $text) {
                echo "	<payment_methods_send_type id='" . $id . "'><![CDATA[" . $text . "]]></payment_methods_send_type>";
            }
            echo "		</payment_methods_send_types>";
            echo "<display_customers_groups>";
            $customers_groups_array = array();
            $customers_groups_select_sql = "SELECT customers_groups_id, customers_groups_name, customers_groups_payment_methods 
												FROM " . TABLE_CUSTOMERS_GROUPS . "
												ORDER BY sort_order, customers_groups_name";
            $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
            while ($customers_groups_row = tep_db_fetch_array($customers_groups_result_sql)) {
                echo "	<customers_groups id='" . $customers_groups_row['customers_groups_id'] . "' selected='" . (tep_not_null($customers_groups_row['customers_groups_payment_methods']) && in_array((int) $pm_id, explode(",", $customers_groups_row['customers_groups_payment_methods'])) ? 1 : 0) . "'><![CDATA[" . $customers_groups_row['customers_groups_name'] . "]]></customers_groups>";
                $customers_groups_array[] = $customers_groups_row['customers_groups_id'];
            }
            echo "</display_customers_groups>";

            echo "		<payment_methods_send_available_sites>";
            foreach ($payment_methods_obj->get_payment_methods_send_available_sites() as $id => $text) {
                echo "	<payment_methods_send_available_site id='" . $id . "'><![CDATA[" . $text . "]]></payment_methods_send_available_site>";
            }
            echo "		</payment_methods_send_available_sites>";

            echo "	<currencies>";
            $currencies_select_sql = "	SELECT currencies_id, code
											FROM " . TABLE_CURRENCIES . "
											ORDER BY code";
            $currencies_result_sql = tep_db_query($currencies_select_sql);
            while ($currencies_row = tep_db_fetch_array($currencies_result_sql)) {
                echo "	<currency id='" . $currencies_row['currencies_id'] . "'><![CDATA[" . $currencies_row['code'] . "]]></currency>";
            }
            echo "	</currencies>";
            break;
        case 'insert_payment_method':
            $error_flag = true;
            $message = array();

            $insert_payment_method_sql_data = array();
            $insert_payment_method_sql_data["payment_methods_parent_id"] = (int) $pm_id;
            if (isset($_REQUEST['rd_receive_payment']) && $_REQUEST['rd_receive_payment'] == 1) {
                $insert_payment_method_sql_data["payment_methods_receive_status"] = 1;

                $check_code_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . "
											WHERE payment_methods_id != '" . $pm_id . "'
												AND payment_methods_parent_id = '" . $pm_id . "' 
												AND payment_methods_code = '" . $_REQUEST['payment_methods_code'] . "'";
                $check_code_result_sql = tep_db_query($check_code_select_sql);
                if ($check_code_row = tep_db_fetch_array($check_code_result_sql)) {
                    $message[] = "Payment Method's Code is currently existing.";
                } else if (!isset($_REQUEST['payment_methods_code']) || !tep_not_null($_REQUEST['payment_methods_code'])) {
                    $message[] = "Payment Method's Code can't be empty.";
                } else if (!isset($_REQUEST['payment_methods_title']) || !tep_not_null($_REQUEST['payment_methods_title'])) {
                    $message[] = "Payment Method's Title can't be empty.";
                } else if (!isset($_REQUEST['payment_methods_status']) || !tep_not_null($_REQUEST['payment_methods_status'])) {
                    $message[] = "Please select Payment Method's status.";
                } else if (!isset($_REQUEST['payment_methods_types_id']) || !tep_not_null($_REQUEST['payment_methods_types_id'])) {
                    $message[] = "Please select payment Method's type.";
                } else {
                    $error_flag = false;
                    $insert_payment_method_sql_data["payment_methods_title"] = tep_db_prepare_input($_REQUEST['payment_methods_title']);
                    $insert_payment_method_sql_data["payment_methods_types_id"] = (int) $_REQUEST['payment_methods_types_id'];
                    $insert_payment_method_sql_data["payment_methods_receive_status_mode"] = (int) $_REQUEST['payment_methods_status'];
                    $insert_payment_method_sql_data["payment_methods_code"] = tep_db_prepare_input($_REQUEST['payment_methods_code']);
                }
            } else {
                $insert_payment_method_sql_data["payment_methods_receive_status"] = 0;
            }

            if ($insert_payment_method_sql_data["payment_methods_receive_status"] == 0 || !$error_flag) {
                if (isset($_REQUEST['rd_outgoing_payment']) && $_REQUEST['rd_outgoing_payment'] == 1) {
                    $insert_payment_method_sql_data["payment_methods_send_status"] = 1;

                    if (!isset($_REQUEST['send_mode_name']) || !tep_not_null($_REQUEST['send_mode_name'])) {
                        $error_flag = true;
                        $message[] = TEXT_INFO_ENTER_PAYMENT_METHOD_SEND_MODE_NAME;
                    } else if (!isset($_REQUEST['payment_methods_send_status'])) {
                        $message[] = "Please select outgoing payment's status.";
                    } else if (!isset($_REQUEST['payment_methods_types_id']) || !tep_not_null($_REQUEST['payment_methods_types_id'])) {
                        $message[] = "Please select payment Method's type.";
                    } else {
                        $error_flag = false;

                        $insert_payment_method_sql_data['payment_methods_send_status_mode'] = (int) $_REQUEST['payment_methods_send_status'];
                        $insert_payment_method_sql_data['payment_methods_send_mass_payment'] = (int) (isset($_REQUEST['payment_methods_send_mass_payment']) ? $_POST['payment_methods_send_mass_payment'] : 0);
                        $insert_payment_method_sql_data['payment_methods_send_mode_name'] = tep_db_prepare_input($_REQUEST['send_mode_name']);
                        $insert_payment_method_sql_data['payment_methods_send_currency'] = (int) $_REQUEST['sel_outgoing_currency'];
                        $insert_payment_method_sql_data['payment_methods_send_available_sites'] = (isset($_REQUEST['payment_methods_send_sites']) ? implode(",", $_REQUEST['payment_methods_send_sites']) : '0');
                        $insert_payment_method_sql_data["payment_methods_types_id"] = (int) $_REQUEST['payment_methods_types_id'];

                        if ((int) $_REQUEST['payment_methods_types_id'] == 6) {
                            $insert_payment_method_sql_data['payment_methods_send_currency'] = 0;
                        }

                        if (isset($_REQUEST['sel_outgoing_currency'])) {
                            if ((int) $_REQUEST['payment_methods_types_id'] != 6) {
                                if ((int) $_REQUEST['sel_outgoing_currency'] > 0) {
                                    $insert_payment_method_sql_data['payment_methods_send_currency'] = (int) $_REQUEST['sel_outgoing_currency'];
                                } else {
                                    $error_flag = true;
                                    $message[] = "Please select outgoing payment's currency.";
                                }
                            }
                        }

                        if (!$error_flag) {
                            // UPDATE 'TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION'  
                            $update_status_sql_data = array();
                            if (isset($_POST['payment_methods_send_status_message'])) {
                                $update_status_sql_data['payment_methods_status_message'] = tep_db_prepare_input($_POST['payment_methods_send_status_message']);
                                $pm_status_exist_select_sql = "	SELECT payment_methods_id 
																FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
																WHERE payment_methods_id = '" . (int) $pm_id . "' 
																	AND languages_id = '1' 
																	AND payment_methods_mode = 'SEND' 
																	AND payment_methods_status = '-1'";
                                $pm_status_exist_result_sql = tep_db_query($pm_status_exist_select_sql);
                                if (tep_db_num_rows($pm_status_exist_result_sql)) {
                                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_sql_data, 'update', "	payment_methods_id = '" . (int) $pm_id . "' and 
																																	languages_id='1' and 
																																	payment_methods_mode='SEND' and 
																																	payment_methods_status = '-1'");
                                } else {
                                    $update_status_sql_data['payment_methods_id'] = (int) $pm_id;
                                    $update_status_sql_data['languages_id'] = 1;
                                    $update_status_sql_data['payment_methods_status'] = '-1';
                                    $update_status_sql_data['payment_methods_mode'] = 'SEND';
                                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_sql_data);
                                }
                            }
                            $update_status_sql_data = array();
                            if (isset($_POST['payment_methods_send_code'])) {
                                $update_status_sql_data['payment_methods_status_message'] = tep_db_prepare_input($_POST['payment_methods_send_code']);
                                $pm_status_exist_select_sql = "	SELECT payment_methods_id 
																FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " 
																WHERE payment_methods_id = '" . (int) $pm_id . "' 
																	AND languages_id = '1' 
																	AND payment_methods_mode = 'SEND_CODE' 
																	AND payment_methods_status = '-1'";
                                $pm_status_exist_result_sql = tep_db_query($pm_status_exist_select_sql);
                                if (tep_db_num_rows($pm_status_exist_result_sql)) {
                                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_sql_data, 'update', "	payment_methods_id = '" . (int) $pm_id . "' and 
																																	languages_id='1' and 
																																	payment_methods_mode='SEND_CODE' and 
																																	payment_methods_status = '-1'");
                                } else {
                                    $update_status_sql_data['payment_methods_id'] = (int) $pm_id;
                                    $update_status_sql_data['languages_id'] = 1;
                                    $update_status_sql_data['payment_methods_status'] = '-1';
                                    $update_status_sql_data['payment_methods_mode'] = 'SEND_CODE';
                                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $update_status_sql_data);
                                }
                            }
                        }
                    }
                } else {
                    $insert_payment_method_sql_data["payment_methods_send_status"] = 0;
                }
            }

            if (!$error_flag && !$insert_payment_method_sql_data["payment_methods_receive_status"] && !$insert_payment_method_sql_data["payment_methods_receive_status"]) {
                $message[] = TEXT_INFO_AT_LEAST_ENABLE_ONE;
            }

            if ($error_flag) {
                echo "<success>0</success>";
            } else {
                $insert_payment_method_sql_data["payment_methods_sort_order"] = (int) $_REQUEST['payment_methods_sort_order'];
                $insert_payment_method_sql_data["payment_methods_legend_color"] = (int) $_REQUEST['payment_methods_sort_order'];

                tep_db_perform(TABLE_PAYMENT_METHODS, $insert_payment_method_sql_data);

                $new_pm_id = tep_db_insert_id();

                $pg_fields_array = array();
                $pg_fields_select_sql = "	SELECT *
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . "
											WHERE payment_methods_id = '" . (int) $pm_id . "'";
                $pg_fields_result_sql = tep_db_query($pg_fields_select_sql);
                while ($pg_fields_row = tep_db_fetch_array($pg_fields_result_sql)) {
                    $pg_fields_array[$pg_fields_row['payment_methods_fields_id']] = $pg_fields_row;
                    $pg_fields_array[$pg_fields_row['payment_methods_fields_id']]['payment_methods_id'] = $new_pm_id;
                    unset($pg_fields_array[$pg_fields_row['payment_methods_fields_id']]['payment_methods_fields_id']);
                }

                foreach ($pg_fields_array as $pg_fields_id_loop => $pg_fields_data_loop) {
                    tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $pg_fields_data_loop);
                }

                if (isset($_REQUEST['rd_outgoing_payment']) && $_REQUEST['rd_outgoing_payment'] == 1) {
                    if (isset($_REQUEST['sel_outgoing_currency'])) {
                        if ((int) $_REQUEST['payment_methods_types_id'] != 6) {
                            $currency_select_sql = "SELECT c.code 
													FROM " . TABLE_CURRENCIES . " as c
													WHERE currencies_id = '" . (int) $_REQUEST['sel_outgoing_currency'] . "'";
                            $currency_result_sql = tep_db_query($currency_select_sql);
                            $currency_row = tep_db_fetch_array($currency_result_sql);

                            $payment_gateway_instance_data_sql = array('payment_methods_id' => (int) $new_pm_id,
                                'currency_code' => tep_db_prepare_input($currency_row['code']));
                            tep_db_perform(TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE, $payment_gateway_instance_data_sql);
                        }
                    }
                }

                $display_payment_method_sql_data = array("payment_methods_id" => $new_pm_id,
                    "payment_methods_description_title" => tep_db_prepare_input($_REQUEST['payment_methods_description_title']),
                    "languages_id" => 1);
                tep_db_perform(TABLE_PAYMENT_METHODS_DESCRIPTION, $display_payment_method_sql_data);

                $display_status_tab_sql_data = array("payment_methods_id" => $new_pm_id,
                    "languages_id" => 1,
                    "payment_methods_mode" => "RECEIVE",
                    "payment_methods_status" => "-1",
                    "payment_methods_status_message" => tep_db_prepare_input($_REQUEST['payment_methods_status_message']));
                tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $display_status_tab_sql_data);

                $display_status_tab_sql_data = array("payment_methods_id" => $new_pm_id,
                    "languages_id" => 1,
                    "payment_methods_mode" => "SEND",
                    "payment_methods_status" => "-1",
                    "payment_methods_status_message" => tep_db_prepare_input($_REQUEST['payment_methods_send_status_message']));
                tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $display_status_tab_sql_data);

                $display_status_tab_sql_data = array("payment_methods_id" => $new_pm_id,
                    "languages_id" => 1,
                    "payment_methods_mode" => "SEND_CODE",
                    "payment_methods_status" => "-1",
                    "payment_methods_status_message" => tep_db_prepare_input($_REQUEST['payment_methods_send_code']));
                tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $display_status_tab_sql_data);
                
                //create dynamic form based on pipwavePG.php file
                $payment_filename_select_sql = "SELECT payment_methods_filename
                                                FROM " . TABLE_PAYMENT_METHODS . "
                                                WHERE payment_methods_id = '" . $pm_id . "'";
                $payment_filename_result_sql = tep_db_query($payment_filename_select_sql);
                if ($payment_file_row = tep_db_fetch_array($payment_filename_result_sql)) {
                    switch ($payment_file_row['payment_methods_filename']) {
                        case 'pipwavePG.php':
                            require_once(DIR_FS_CATALOG_MODULES . 'payment/pipwavePG.php');
                            $pipwave_obj = new pipwavePG($new_pm_id);
                            $pipwave_obj->default_form(tep_db_prepare_input($_REQUEST['payment_methods_send_code']));

                        break;
                    }
                }
                //end

                $selected_customers_groups_array = array();
                if (isset($_REQUEST['chk_customers_groups']) && count($_REQUEST['chk_customers_groups'])) {
                    $selected_customers_groups_array = $_REQUEST['chk_customers_groups'];
                }

                $customers_groups_select_sql = "	SELECT customers_groups_id, customers_groups_payment_methods
													FROM " . TABLE_CUSTOMERS_GROUPS;
                $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
                while ($customers_groups_row = tep_db_fetch_array($customers_groups_result_sql)) {
                    $selected_payment_methods_array = (tep_not_null($customers_groups_row['customers_groups_payment_methods']) ? explode(",", $customers_groups_row['customers_groups_payment_methods']) : array());
                    if (in_array($customers_groups_row['customers_groups_id'], $selected_customers_groups_array)) {
                        $selected_payment_methods_array[] = (int) $new_pm_id;
                        $payment_methods_data_sql = array('customers_groups_payment_methods' => implode(",", $selected_payment_methods_array));
                        tep_db_perform(TABLE_CUSTOMERS_GROUPS, $payment_methods_data_sql, 'update', " customers_groups_id = '" . (int) $customers_groups_row['customers_groups_id'] . "' ");
                    }
                }

                $message[] = TEXT_INFO_DATA_INSERTED;

                $receive_pm_email_insert_str = "Insert Payment Method:<br>";
                $receive_pm_email_insert_str .= "<table border='1' width='100%' cellspacing='0' cellpadding='0'>";
                $receive_pm_email_insert_str .= "<tr valign='top'><td>&nbsp;</td><td><b>Value</b></td></tr>";
                $receive_pm_email_insert_str .= "<tr valign='top'><td>ID</td><td>" . $new_pm_id . "</td></tr>";
                $receive_pm_email_insert_str .= "<tr valign='top'><td>Code</td><td>" . tep_db_input($_POST['payment_methods_code']) . "</td></tr>";
                $receive_pm_email_insert_str .= "<tr valign='top'><td>Title</td><td>" . tep_db_input($_POST['payment_methods_title']) . "</td></tr>";
                $receive_pm_email_insert_str .= "<tr valign='top'><td>Types</td><td>" . (int) $_POST['payment_methods_types_id'] . "</td></tr>";
                $receive_pm_email_insert_str .= "<tr valign='top'><td>Receive Status Mode</td><td>" . tep_db_input($_POST['payment_methods_status']) . "</td></tr>";
                $receive_pm_email_insert_str .= "<tr valign='top'><td>Sort Order</td><td>" . tep_db_input($_POST['payment_methods_sort_order']) . "</td></tr>";
                $receive_pm_email_insert_str .= "<tr valign='top'><td>Inactive Status Message</td><td>" . tep_db_input($_POST['payment_methods_status_message']) . "</td></tr>";
                $receive_pm_email_insert_str .= "<tr valign='top'><td>Payment Method's Customers Groups Setting:</td><td><ul><li>" . implode("</li><li>", $selected_customers_groups_array) . "</li></ul></td></tr>";
                $receive_pm_email_insert_str .= "</table>";
                $receive_pm_email_array[] = $receive_pm_email_insert_str;

                echo "<success>1</success>";
                echo "<new_payment_method_id>" . $new_pm_id . "</new_payment_method_id>";
            }
            foreach ($message as $msg) {
                echo "	<message><![CDATA[" . $msg . "]]></message>";
            }
            break;
        case 'insert_send_payment_method':
            $error_flag = true;
            $error_message = array();
            $check_code_select_sql = "	SELECT payment_methods_id
											FROM " . TABLE_PAYMENT_METHODS . "
											WHERE payment_methods_id != '" . $pm_id . "'
												AND payment_methods_parent_id = '" . $pm_id . "' 
												AND payment_methods_code = '" . $_POST['payment_methods_code'] . "'";
            $check_code_result_sql = tep_db_query($check_code_select_sql);
            if ($check_code_row = tep_db_fetch_array($check_code_result_sql)) {
                $message[] = "Payment Method's Code is currently existing.";
            } else if (!isset($_POST['payment_methods_code']) || !tep_not_null($_POST['payment_methods_code'])) {
                $message[] = "Payment Method's Code can't be empty.";
            } else if (!isset($_POST['send_mode_name']) || !tep_not_null($_POST['send_mode_name'])) {
                $message[] = "Payment Method's Title can't be empty.";
            } else if (!isset($_POST['send_status_mode']) || !tep_not_null($_POST['send_status_mode'])) {
                $message[] = "Please select Payment Method's status.";
            } else if (!isset($_POST['payment_methods_send_sites']) || !tep_not_null($_POST['payment_methods_send_sites'])) {
                $message[] = "Please select Payment Method's available sites.";
            } else if (!isset($_POST['send_currency']) || !tep_not_null($_POST['send_currency'])) {
                $message[] = "Please select Payment Method's currency.";
            } else {
                $error_flag = false;
                // insert payment method as child
                $insert_payment_method_sql_data = array("payment_methods_parent_id" => $pm_id,
                    "payment_methods_send_mode_name" => tep_db_prepare_input($_POST['send_mode_name']),
                    "payment_methods_send_status" => 1,
                    "payment_methods_send_currency" => (int) $_POST['send_currency'],
                    "payment_methods_send_status_mode" => (int) $_POST['send_status_mode'],
                    "payment_methods_send_available_sites" => (isset($_POST['payment_methods_send_sites']) ? implode(",", $_POST['payment_methods_send_sites']) : '0'),
                    "payment_methods_code" => tep_db_prepare_input($_POST['payment_methods_code']));
                tep_db_perform(TABLE_PAYMENT_METHODS, $insert_payment_method_sql_data);
                $new_pm_id = tep_db_insert_id();

                $display_status_tab_sql_data = array("payment_methods_id" => $new_pm_id,
                    "languages_id" => 1,
                    "payment_methods_mode" => "SEND",
                    "payment_methods_status" => "-1",
                    "payment_methods_status_message" => tep_db_prepare_input($_POST['payment_methods_status_message']));
                tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $display_status_tab_sql_data);
                $message[] = TEXT_INFO_DATA_INSERTED;
            }
            if ($error_flag) {
                echo "<success>0</success>";
            } else {
                echo "<success>1</success>";
                echo "<new_payment_method_id>" . $new_pm_id . "</new_payment_method_id>";
            }
            foreach ($message as $msg) {
                echo "	<message><![CDATA[" . $msg . "]]></message>";
            }
            break;
        case 'install_payment_gateway':
            if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $_REQUEST['filename'])) {
                include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $_REQUEST['filename']);
                $class = substr($_REQUEST['filename'], 0, strrpos($_REQUEST['filename'], '.'));
                if (!tep_class_exists($class)) {
                    $msg = "Class not found.";
                    echo "<success>0</success>";
                } else {
                    if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $class . '/admin/languages/english/' . $class . '.lng.php')) {
                        include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $class . '/admin/languages/english/' . $class . '.lng.php');
                    }
                    eval('$module = new ' . $class . '();');
                    if ($module->install()) {
                        $receive_pm_email_array[] = 'New Payment Gateway Installed, ' . $module->title;
                        $msg = TEXT_INFO_PAYMENT_GATEWAY_INSTALLED;
                        echo "<success>1</success>";
                        echo "<payment_gateway_id>" . $module->get_pg_id() . "</payment_gateway_id>";
                    } else {
                        echo "<success>0</success>";
                        echo "<message><![CDATA[" . TEXT_INFO_INSTALLATION_FAILED . "]]></message>";
                    }
                }
            } else {
                $msg = TEXT_INFO_FILE_NOT_EXIST;
                echo "<success>0</success>";
            }
            echo "<message><![CDATA[" . $msg . "]]></message>";
            break;
        case 'remove_payment_method':
            // insert remove_log
            $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_code, payment_methods_title
											FROM " . TABLE_PAYMENT_METHODS . "
											WHERE payment_methods_id = '" . (int) $pm_id . "'
												OR payment_methods_parent_id = '" . (int) $pm_id . "'";
            $payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
            while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                //delete 'TABLE_PAYMENT_METHODS_DESCRIPTION'
                $delete_payment_methods_display_sql = "	DELETE FROM " . TABLE_PAYMENT_METHODS_DESCRIPTION . "
														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                tep_db_query($delete_payment_methods_display_sql);

                //delete 'TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION'
                $delete_payment_methods_display_sql = "	DELETE FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . "
														WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                tep_db_query($delete_payment_methods_display_sql);

                $payment_methods_instance_select_sql = "	SELECT pmi.payment_methods_instance_id
															FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pmi 
															WHERE pmi.payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $payment_methods_instance_result_sql = tep_db_query($payment_methods_instance_select_sql);
                $payment_methods_instance_array = array();
                while ($payment_methods_instance_row = tep_db_fetch_array($payment_methods_instance_result_sql)) {
                    $payment_methods_instance_array[] = $payment_methods_instance_row['payment_methods_instance_id'];
                }

                if (count($payment_methods_instance_array)) {
                    $payment_methods_instance_str = implode("','", $payment_methods_instance_array);

                    //delete 'TABLE_PAYMENT_METHODS_INSTANCE_SETTING'
                    $delete_payment_methods_instance_setting_sql = "DELETE FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . "
																	WHERE payment_methods_instance_id IN ('" . $payment_methods_instance_str . "')";
                    tep_db_query($delete_payment_methods_instance_setting_sql);

                    //delete 'TABLE_PAYMENT_METHODS_INSTANCE'
                    $delete_payment_methods_instance_sql = "DELETE FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
															WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                    tep_db_query($delete_payment_methods_instance_sql);
                }

                $payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_id
															FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci 
															WHERE pci.payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                $payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
                $payment_configuration_info_array = array();
                while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
                    $payment_methods_configuration_array[] = $payment_methods_configuration_row['payment_configuration_info_id'];
                }
                $payment_configuration_info_str = implode("','", $payment_configuration_info_array);

                //delete 'TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION'
                $delete_payment_configuration_display_setting_sql = "	DELETE FROM " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . "
																		WHERE payment_configuration_info_id IN ('" . $payment_configuration_info_str . "')";
                tep_db_query($delete_payment_configuration_display_setting_sql);

                //delete 'TABLE_PAYMENT_METHODS_INSTANCE'
                $delete_payment_configuration_info_sql = "	DELETE FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . "

															WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                tep_db_query($delete_payment_configuration_info_sql);

                $insert_log_sql_data = array('payment_methods_code' => $payment_methods_row['payment_methods_code'],
                    'payment_methods_id' => (int) $payment_methods_row['payment_methods_id'],
                    'payment_methods_title' => $payment_methods_row['payment_methods_title'],
                    'payment_methods_history_date' => 'now()');
                tep_db_perform(TABLE_PAYMENT_METHODS_HISTORY, $insert_log_sql_data);

                //delete 'TABLE_PAYMENT_METHODS_INSTANCE'
                $delete_payment_method_sql = "	DELETE FROM " . TABLE_PAYMENT_METHODS . "
												WHERE payment_methods_id = '" . (int) $payment_methods_row['payment_methods_id'] . "'";
                tep_db_query($delete_payment_method_sql);

                $receive_pm_email_array[] = "Payment Method Removed (" . $payment_methods_row['payment_methods_title'] . ")";
            }
            break;
        case 'insert_currencies':
            $error_flag = true;
            $insert_currencies_sql_data = array();
            if (isset($_POST['new']['currencies']) && tep_not_null($_POST['new']['currencies'])) {
                $check_duplicate_currency_select_sql = "SELECT currency_code, payment_methods_instance_id 
														FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
														WHERE payment_methods_id = '" . (int) $pm_id . "'";
                $check_duplicate_currency_result_sql = tep_db_query($check_duplicate_currency_select_sql);
                $existing_currencies = array();
                while ($check_duplicate_currency_row = tep_db_fetch_array($check_duplicate_currency_result_sql)) {
                    $existing_currencies[$check_duplicate_currency_row['currency_code']] = $check_duplicate_currency_row['payment_methods_instance_id'];
                }
                if (!isset($existing_currencies[$_POST['new']['currencies']])) {
                    $insert_currencies_sql_data['currency_code'] = $_POST['new']['currencies'];
                    if (count($existing_currencies)) {
                        $insert_currencies_sql_data['payment_methods_instance_default'] = 0;
                        if (isset($_REQUEST['new']['copy_default']) && $_REQUEST['new']['copy_default']) {
                            $default_select_sql = "	SELECT payment_methods_instance_id, currency_code 
													FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
													WHERE payment_methods_id = '" . (int) $pm_id . "'
														AND payment_methods_instance_default = '1'";
                            $default_result_sql = tep_db_query($default_select_sql);
                            if ($default_row = tep_db_fetch_array($default_result_sql)) {
                                $insert_currencies_sql_data['payment_methods_instance_follow_default'] = (int) $default_row['payment_methods_instance_id'];
                                $receive_pm_email_array[] = "Insert Payment Methods Merchant's Currency: " . $_POST['new']['currencies'] . ", follow " . $default_row['currency_code'] . ".";
                            }
                        } else {
                            $receive_pm_email_array[] = "Insert Payment Methods Merchant's Currency: " . $_POST['new']['currencies'];
                        }
                    } else {
                        $insert_currencies_sql_data['payment_methods_instance_default'] = 1;
                        $receive_pm_email_array[] = "Insert Payment Methods Merchant's Currency: " . $_POST['new']['currencies'];
                    }
                    $insert_currencies_sql_data['payment_methods_id'] = (int) $pm_id;
                    tep_db_perform(TABLE_PAYMENT_METHODS_INSTANCE, $insert_currencies_sql_data);
                    $error_flag = false;
                    $new_pmi_id = tep_db_insert_id();

                    if (!(isset($_REQUEST['new']['copy_default']) && $_REQUEST['new']['copy_default'])) {
                        $payment_methods_obj->set_instance_key_info();
                        $payment_methods_instance_array = $payment_methods_obj->get_instance_key_info();

                        if (count($payment_methods_instance_array)) {
                            foreach ($payment_methods_instance_array as $key => $data) {
                                $check_settlement_select_sql = "SELECT payment_methods_instance_setting_id 
																FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . "
																WHERE payment_methods_instance_id = '" . (int) $new_pmi_id . "' 
																	AND payment_methods_instance_setting_key = '" . $key . "'";
                                $check_settlement_result_sql = tep_db_query($check_settlement_select_sql);
                                if (!$check_settlement_row = tep_db_fetch_array($check_settlement_result_sql)) {
                                    $insert_currencies_setting_sql_data = array();
                                    $insert_currencies_setting_sql_data['payment_methods_instance_id'] = (int) $new_pmi_id;
                                    $insert_currencies_setting_sql_data['payment_methods_instance_setting_key'] = $key;
                                    $insert_currencies_setting_sql_data['payment_methods_instance_setting_value'] = (isset($_REQUEST['new'][$key]) ? tep_db_prepare_input($_REQUEST['new'][$key]) : '');
                                    tep_db_perform(TABLE_PAYMENT_METHODS_INSTANCE_SETTING, $insert_currencies_setting_sql_data);
                                }
                            }
                        }
                    }
                } else {
                    $msg = TEXT_INFO_DUPLICATED_CURRENCY;
                }
            }
            if ($error_flag) {
                $msg = TEXT_INFO_INSERTING_FAILED;
                echo "	<success>0</success>";
            } else {
                $msg = TEXT_INFO_DATA_INSERTED;
                echo "	<success>1</success>";
            }
            echo "	<message><![CDATA[" . $msg . "]]></message>";
            break;
        case 'submit_currencies_tab':
            $error_flag = true;
            $message = array();

            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_filename());
            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_payment_methods_class_name() . '/admin/languages/english/' . $payment_methods_obj->get_payment_methods_class_name() . '.lng.php');

            if (tep_class_exists($payment_methods_obj->get_payment_methods_class_name())) {
                $error_flag = false;
                eval('$module = new ' . $payment_methods_obj->get_payment_methods_class_name() . '();');
                if (count($module->merchant_information_keys())) {
                    if (isset($_POST['payment_methods_instance_default']) && (int) $_POST['payment_methods_instance_default'] > 0) {

                        $pm_instance_array = array();
                        $payment_methods_instance_default = '';
                        $pm_instance_log_array = array();

                        $pm_instance_log_select_sql = "	SELECT pgi.payment_methods_instance_id, pgi.currency_code, pgi.payment_methods_instance_follow_default, pgi.payment_methods_instance_default 
														FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pgi
														WHERE pgi.payment_methods_id = '" . (int) $pm_id . "'
														GROUP BY pgi.payment_methods_instance_id";
                        $pm_instance_log_result_sql = tep_db_query($pm_instance_log_select_sql);
                        while ($pm_instance_log_row = tep_db_fetch_array($pm_instance_log_result_sql)) {
                            if ($pm_instance_log_row['payment_methods_instance_follow_default'] == 0) {
                                $pm_instance_log_array[] = $pm_instance_log_row['payment_methods_instance_id'];
                            }
                            if ($pm_instance_log_row['payment_methods_instance_default']) {
                                $payment_methods_instance_default = $pm_instance_log_row['payment_methods_instance_id'];
                            }
                            $pm_instance_array[$pm_instance_log_row['payment_methods_instance_id']] = $pm_instance_log_row['currency_code'];
                        }

                        if ($payment_methods_instance_default != (int) $_POST['payment_methods_instance_default']) {
                            $receive_pm_email_array[] = "Update Merchant Setting Default Currency: " . $pm_instance_array[$payment_methods_instance_default] . "-> " . $pm_instance_array[(int) $_POST['payment_methods_instance_default']];
                        }

                        $update_instance_sql_data = array();
                        $update_instance_sql_data['payment_methods_instance_default'] = 0;
                        $update_instance_sql_data['payment_methods_instance_follow_default'] = 0;
                        tep_db_perform(TABLE_PAYMENT_METHODS_INSTANCE, $update_instance_sql_data, 'update', "payment_methods_id = '" . (int) $pm_id . "'");

                        $update_instance_sql_data = array();
                        $update_instance_sql_data['payment_methods_instance_default'] = 1;
                        tep_db_perform(TABLE_PAYMENT_METHODS_INSTANCE, $update_instance_sql_data, 'update', "payment_methods_instance_id = '" . (int) $_POST['payment_methods_instance_default'] . "'");

                        $payment_methods_instance_select_sql = "	SELECT pgi.payment_methods_instance_id, pgi.currency_code, pgi.payment_methods_instance_follow_default 
																	FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " as pgi
																	WHERE pgi.payment_methods_id = '" . (int) $pm_id . "'
																	GROUP BY pgi.payment_methods_instance_id";
                        $payment_methods_instance_result_sql = tep_db_query($payment_methods_instance_select_sql);
                        while ($payment_methods_instance_row = tep_db_fetch_array($payment_methods_instance_result_sql)) {
                            $receive_pm_email_instance_array = array();
                            if (isset($_POST['copy_default'][(int) $payment_methods_instance_row['payment_methods_instance_id']]) && $_POST['copy_default'][(int) $payment_methods_instance_row['payment_methods_instance_id']]) {

                                if (in_array($payment_methods_instance_row['payment_methods_instance_id'], $pm_instance_log_array)) {
                                    $receive_pm_email_instance_array[] = "<td>Follow Default</td><td align='center'>-</td><td align='center'>Follow Default</td>";
                                }

                                $update_instance_sql_data = array();
                                $update_instance_sql_data['payment_methods_instance_follow_default'] = (int) $_POST['payment_methods_instance_default'];
                                tep_db_perform(TABLE_PAYMENT_METHODS_INSTANCE, $update_instance_sql_data, 'update', "payment_methods_instance_id = '" . (int) $payment_methods_instance_row['payment_methods_instance_id'] . "'");

                                $delete_instance_settingvalue_sql = "	DELETE FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . "
																		WHERE payment_methods_instance_id = '" . (int) $payment_methods_instance_row['payment_methods_instance_id'] . "'";
                                tep_db_query($delete_instance_settingvalue_sql);
                            } else {
                                foreach ($module->merchant_information_keys() as $key => $data) {
                                    if (isset($_POST[$key][$payment_methods_instance_row['payment_methods_instance_id']])) {
                                        eval('$instance_key_name = ' . $data['mapping'] . ';');
                                        $update_instance_setting_sql_data = array("payment_methods_instance_setting_value" => tep_db_prepare_input($_POST[$key][$payment_methods_instance_row['payment_methods_instance_id']]));
                                        $check_instance_setting_select_sql = "	SELECT payment_methods_instance_setting_id, payment_methods_instance_setting_value 
																				FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . "
																				WHERE payment_methods_instance_id = '" . (int) $payment_methods_instance_row['payment_methods_instance_id'] . "' 
																					AND payment_methods_instance_setting_key = '" . $key . "'";
                                        $check_instance_setting_result_sql = tep_db_query($check_instance_setting_select_sql);
                                        if ($check_instance_setting_row = tep_db_fetch_array($check_instance_setting_result_sql)) {
                                            if ($check_instance_setting_row['payment_methods_instance_setting_value'] != $_POST[$key][$payment_methods_instance_row['payment_methods_instance_id']]) {
                                                $receive_pm_email_instance_array[] = "<td>" . $instance_key_name . "</td><td>" . $check_instance_setting_row['payment_methods_instance_setting_value'] . "</td><td>-- hidden --</td>";
                                            }
                                            tep_db_perform(TABLE_PAYMENT_METHODS_INSTANCE_SETTING, $update_instance_setting_sql_data, 'update', "payment_methods_instance_setting_id = '" . (int) $check_instance_setting_row['payment_methods_instance_setting_id'] . "'");
                                        } else {
                                            $receive_pm_email_instance_array[] = "<td>" . $instance_key_name . "</td><td>&nbsp;</td><td>-- hidden --</td>";
                                            $update_instance_setting_sql_data['payment_methods_instance_setting_key'] = $key;
                                            $update_instance_setting_sql_data['payment_methods_instance_id'] = (int) $payment_methods_instance_row['payment_methods_instance_id'];
                                            tep_db_perform(TABLE_PAYMENT_METHODS_INSTANCE_SETTING, $update_instance_setting_sql_data);
                                        }
                                    }
                                }
                            }

                            if (count($receive_pm_email_instance_array)) {
                                $receive_pm_email_array[] = "Update Merchant Setting (" . $payment_methods_instance_row['currency_code'] . "):<br><table border='1' width='100%' cellspacing='0' cellpadding='0'><tr><td>&nbsp;</td><td><b>Old</b></td><td><b>New</b></td></tr><tr valign='top'>" . implode("</tr><tr>", $receive_pm_email_instance_array) . "</tr></table>";
                            }
                        }
                    }
                }
                $message[] = TEXT_INFO_DATA_UPDATED;
            }
            echo "<success>" . ($error_flag ? '0' : '1') . "</success>";
            foreach ($message as $msg) {
                echo "	<message><![CDATA[" . $msg . "]]></message>";
            }
            break;
        case 'submit_outgoing_currencies_tab':
            $error_flag = true;
            $message = array();

            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_filename());
            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_payment_methods_class_name() . '/admin/languages/english/' . $payment_methods_obj->get_payment_methods_class_name() . '.lng.php');

            // get master currency list from currencies tab for selected payment method
            $master_currency = array();
            $pmi_select_sql = "SELECT DISTINCT currency_code
								FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . "
								WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'";
            $pmi_result_sql = tep_db_query($pmi_select_sql);
            if (tep_db_num_rows($pmi_result_sql) > 0) {
                while ($pmi_row = tep_db_fetch_array($pmi_result_sql)) {
                    $master_currency[] = $pmi_row['currency_code'];
                }
            } else {
                // get master currency list from selected payment method's parent setting
                $parent_id = $payment_methods_obj->payment_methods_parent_id;
                $parent_pmi_select_sql = "	SELECT DISTINCT currency_code
											FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . "
											WHERE payment_methods_id = '" . tep_db_input($parent_id) . "'";
                $parent_pmi_result_sql = tep_db_query($parent_pmi_select_sql);
                while ($parent_pmi_row = tep_db_fetch_array($parent_pmi_result_sql)) {
                    $master_currency[] = $parent_pmi_row['currency_code'];
                }
            }

            if (tep_class_exists($payment_methods_obj->get_payment_methods_class_name())) {
                $error_flag = false;
                eval('$module = new ' . $payment_methods_obj->get_payment_methods_class_name() . '();');
                $merchant_information_keys = array();
                if (method_exists($module, 'outgoing_merchant_information_keys')) {
                    $merchant_information_keys = $module->outgoing_merchant_information_keys();
                    if (count($merchant_information_keys)) {
                        //if (isset($HTTP_POST_VARS['payment_methods_instance_default']) && (int)$HTTP_POST_VARS['payment_methods_instance_default']>0) {
                        /* $update_instance_sql_data = array();
                          $update_instance_sql_data['payment_methods_outgoing_instance_default'] = 0;
                          $update_instance_sql_data['payment_methods_outgoing_instance_follow_default'] = 0;
                          tep_db_perform(TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE, $update_instance_sql_data, 'update', "payment_methods_id = '" . (int)$pm_id . "'");

                          $update_instance_sql_data = array();
                          $update_instance_sql_data['payment_methods_outgoing_instance_default'] = 1;
                          tep_db_perform(TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE, $update_instance_sql_data, 'update', "payment_methods_outgoing_instance_id = '" . (int)$HTTP_POST_VARS['payment_methods_instance_default'] . "'"); */

                        $payment_methods_instance_select_sql = "	SELECT pgi.payment_methods_outgoing_instance_id
																		FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pgi
																		WHERE pgi.payment_methods_id = '" . (int) $pm_id . "'
																		GROUP BY pgi.payment_methods_outgoing_instance_id";
                        $payment_methods_instance_result_sql = tep_db_query($payment_methods_instance_select_sql);
                        while ($payment_methods_instance_row = tep_db_fetch_array($payment_methods_instance_result_sql)) {
                            /* if (isset($HTTP_POST_VARS['copy_default'][(int)$payment_methods_instance_row['payment_methods_outgoing_instance_id']]) && $HTTP_POST_VARS['copy_default'][(int)$payment_methods_instance_row['payment_methods_outgoing_instance_id']]) {
                              $update_instance_sql_data = array();
                              $update_instance_sql_data['payment_methods_outgoing_instance_follow_default'] = (int)$HTTP_POST_VARS['payment_methods_instance_default'];
                              tep_db_perform(TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE, $update_instance_sql_data, 'update', "payment_methods_outgoing_instance_id = '" . (int)$payment_methods_instance_row['payment_methods_outgoing_instance_id'] . "'");
                              $delete_instance_settingvalue_sql = "	DELETE FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE_SETTING . "
                              WHERE payment_methods_outgoing_instance_id = '".(int)$payment_methods_instance_row['payment_methods_outgoing_instance_id']."'";
                              tep_db_query($delete_instance_settingvalue_sql);
                              } else { */
                            foreach ($merchant_information_keys as $key => $data) {
                                if (isset($HTTP_POST_VARS[$key][$payment_methods_instance_row['payment_methods_outgoing_instance_id']])) {
                                    $update_instance_setting_sql_data = array("payment_methods_outgoing_instance_setting_value" => tep_db_prepare_input($HTTP_POST_VARS[$key][$payment_methods_instance_row['payment_methods_outgoing_instance_id']]));

                                    $check_instance_setting_select_sql = "	SELECT payment_methods_outgoing_instance_setting_id 
																					FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE_SETTING . "
																					WHERE payment_methods_outgoing_instance_id = '" . (int) $payment_methods_instance_row['payment_methods_outgoing_instance_id'] . "' 
																						AND payment_methods_outgoing_instance_setting_key = '" . $key . "'";
                                    $check_instance_setting_result_sql = tep_db_query($check_instance_setting_select_sql);
                                    if ($check_instance_setting_row = tep_db_fetch_array($check_instance_setting_result_sql)) {
                                        tep_db_perform(TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE_SETTING, $update_instance_setting_sql_data, 'update', "payment_methods_outgoing_instance_setting_id = '" . (int) $check_instance_setting_row['payment_methods_outgoing_instance_setting_id'] . "'");
                                    } else {
                                        $update_instance_setting_sql_data['payment_methods_outgoing_instance_setting_key'] = $key;
                                        $update_instance_setting_sql_data['payment_methods_outgoing_instance_id'] = (int) $payment_methods_instance_row['payment_methods_outgoing_instance_id'];
                                        tep_db_perform(TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE_SETTING, $update_instance_setting_sql_data);
                                    }
                                }
                            }
                            //}
                        }
                        //}
                    }
                }
                $message[] = TEXT_INFO_DATA_UPDATED;
            }
            echo "<success>" . ($error_flag ? '0' : '1') . "</success>";
            foreach ($message as $msg) {
                echo "	<message><![CDATA[" . $msg . "]]></message>";
            }
            break;
        case 'submit_surcharge_tab':
        case 'submit_sc_surcharge_tab':
            $error_flag = true;
            $message = array();
            $payment_methods_obj = new payment_methods($pm_id);

            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_filename());
            include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_payment_methods_class_name() . '/admin/languages/english/' . $payment_methods_obj->get_payment_methods_class_name() . '.lng.php');

            if ($action == 'submit_sc_surcharge_tab') {
                $payment_fees_table_name = TABLE_PAYMENT_FEES_SC;
            } else {
                $payment_fees_table_name = TABLE_PAYMENT_FEES;
            }

            // get master currency list from currencies tab for selected payment method
            $master_currency = array();
            $pmi_select_sql = "	SELECT DISTINCT currency_code
								FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
								WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'";
            $pmi_result_sql = tep_db_query($pmi_select_sql);
            if (tep_db_num_rows($pmi_result_sql) > 0) {
                while ($pmi_row = tep_db_fetch_array($pmi_result_sql)) {
                    $master_currency[] = $pmi_row['currency_code'];
                }
            } else {
                // get master currency list from selected payment method's parent setting
                $parent_id = $payment_methods_obj->payment_methods_parent_id;
                $parent_pmi_select_sql = "	SELECT DISTINCT currency_code
											FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
											WHERE payment_methods_id = '" . tep_db_input($parent_id) . "'";
                $parent_pmi_result_sql = tep_db_query($parent_pmi_select_sql);
                while ($parent_pmi_row = tep_db_fetch_array($parent_pmi_result_sql)) {
                    $master_currency[] = $parent_pmi_row['currency_code'];
                }
            }

            if (tep_class_exists($payment_methods_obj->get_payment_methods_class_name())) {
                $error_flag = false;
                eval('$module = new ' . $payment_methods_obj->get_payment_methods_class_name() . '();');

                if (count($module->merchant_information_keys())) {
                    $error_flag = ($payment_methods_obj->check_payment_fees_fields($_POST, $message) ? false : true);

                    $customers_groups_array = array();
                    $customers_groups_select_sql = "SELECT customers_groups_id, customers_groups_name 
													FROM " . TABLE_CUSTOMERS_GROUPS;
                    $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
                    while ($customers_groups_row = tep_db_fetch_array($customers_groups_result_sql)) {
                        $customers_groups_array[$customers_groups_row['customers_groups_id']] = $customers_groups_row['customers_groups_name'];
                    }

                    if (!isset($_POST['follow_parent']) && !$error_flag) {

                        $follow_group = (isset($_POST['follow_grouping']) ? $_POST['follow_grouping'] : 0);
                        if ($follow_group > 0) {

                            $payment_methods_id = $_POST['payment_methods_id'];
                            $customer_group_id = $_POST['payment_methods_customer_group_id'];

                            $receive_pm_email_array[] = "Update Surcharge:<br><ul><li>Follow Group (" . (isset($customers_groups_array[$customer_group_id]) ? $customers_groups_array[$customer_group_id] : $customer_group_id) . "): Follow From " . (isset($customers_groups_array[$follow_group]) ? $customers_groups_array[$follow_group] : $follow_group) . "</li></ul>";

                            $existing_currency = $updated_currency = $cleanup_currency = array();

                            // for purpose of currency cleaning up
                            $pm_currency_select_sql = "	SELECT payment_methods_currency_code FROM " . $payment_fees_table_name . "
														WHERE payment_methods_id='" . tep_db_prepare_input($payment_methods_id) . "'
															AND payment_methods_mode='RECEIVE'
															AND payment_fees_customers_groups_id='" . tep_db_input($customer_group_id) . "'";
                            $pm_currency_result_sql = tep_db_query($pm_currency_select_sql);
                            while ($pm_currency_row = tep_db_fetch_array($pm_currency_result_sql)) {
                                $existing_currency[] = $pm_currency_row['payment_methods_currency_code'];
                            }

                            $pm_follow_select_sql = "	SELECT * FROM " . $payment_fees_table_name . "
														WHERE payment_methods_id='" . tep_db_prepare_input($payment_methods_id) . "'
															AND payment_methods_mode='RECEIVE'
															AND payment_fees_customers_groups_id='" . tep_db_input($follow_group) . "'
														ORDER BY payment_fees_customers_groups_id, payment_methods_currency_code";
                            $pm_follow_result_sql = tep_db_query($pm_follow_select_sql);
                            if (tep_db_num_rows($pm_follow_result_sql) > 0) {
                                while ($default_data = tep_db_fetch_array($pm_follow_result_sql)) {
                                    if (in_array($default_data['payment_methods_currency_code'], $master_currency)) {
                                        $update_surcharge_sql_data = array();
                                        $update_surcharge_sql_data['payment_methods_currency_code'] = tep_db_prepare_input($default_data['payment_methods_currency_code']);
                                        $update_surcharge_sql_data['payment_fees_operator'] = tep_db_prepare_input($default_data['payment_fees_operator']);
                                        $update_surcharge_sql_data['payment_fees_min'] = tep_db_prepare_input($default_data['payment_fees_min']);
                                        $update_surcharge_sql_data['payment_fees_cost_value'] = tep_db_prepare_input($default_data['payment_fees_cost_value']);
                                        $update_surcharge_sql_data['payment_fees_cost_percent'] = tep_db_prepare_input($default_data['payment_fees_cost_percent']);
                                        $update_surcharge_sql_data['payment_fees_cost_percent_min'] = tep_db_prepare_input($default_data['payment_fees_cost_percent_min']);
                                        $update_surcharge_sql_data['payment_fees_cost_percent_max'] = tep_db_prepare_input($default_data['payment_fees_cost_percent_max']);
                                        $update_surcharge_sql_data['currency_code'] = tep_db_prepare_input($default_data['currency_code']);
                                        $update_surcharge_sql_data['payment_fees_customers_groups_id'] = tep_db_prepare_input($customer_group_id);
                                        $update_surcharge_sql_data['payment_fees_follow_group'] = tep_db_prepare_input($follow_group);
                                        $update_surcharge_sql_data['payment_methods_id'] = tep_db_prepare_input($payment_methods_id);
                                        $update_surcharge_sql_data['payment_methods_mode'] = 'RECEIVE';

                                        $payment_fees_select_sql = "SELECT payment_fees_id
																	FROM " . $payment_fees_table_name . "
																	WHERE payment_fees_customers_groups_id = '" . tep_db_input($customer_group_id) . "'
																	AND payment_methods_currency_code='" . tep_db_input($default_data['payment_methods_currency_code']) . "'
																	AND payment_methods_id='" . tep_db_input($payment_methods_id) . "'
																	AND payment_methods_mode='RECEIVE'";
                                        $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
                                        if (tep_db_num_rows($payment_fees_result_sql) > 0) {
                                            $data_array = tep_db_fetch_array($payment_fees_result_sql);
                                            $fees_id = $data_array['payment_fees_id'];
                                            tep_db_perform($payment_fees_table_name, $update_surcharge_sql_data, 'update', "payment_fees_id = '" . tep_db_input($fees_id) . "'");
                                        } else {
                                            tep_db_perform($payment_fees_table_name, $update_surcharge_sql_data, 'insert');
                                        }
                                        $updated_currency[] = $default_data['payment_methods_currency_code'];
                                    }
                                }
                            } else {
                                $error_flag = true;
                                $message[] = "No default setting to follow!";
                            }

                            // unwanted currency clean up, if there is any
                            $cleanup_currency = array_diff($existing_currency, $updated_currency);
                            if (count($cleanup_currency) > 0) {
                                $cleanup_currency_str = implode("','", $cleanup_currency);
                                $cleanup_sql = "DELETE FROM " . $payment_fees_table_name . "
												WHERE payment_methods_id='" . tep_db_prepare_input($payment_methods_id) . "'
												AND payment_fees_customers_groups_id='" . tep_db_input($customer_group_id) . "'
												AND payment_methods_currency_code IN ('" . tep_db_input($cleanup_currency_str) . "')
												AND payment_methods_mode='RECEIVE'";
                                tep_db_query($cleanup_sql);
                            }
                        } else {
                            $payment_fees_currency_pair_array = $_POST['payment_methods_fees_set'];
                            $receive_pm_surcharge_array = array();

                            if (is_array($payment_fees_currency_pair_array)) {
                                sort($payment_fees_currency_pair_array);  // <-- keep each currency in pair
                                foreach ($payment_fees_currency_pair_array as $key => $value) {
                                    list ($cgrp_id, $curr_code, $fees_id) = explode('-', $value);

                                    $payment_fee_array = array();
                                    $payment_fee_array[1] = '<';
                                    $payment_fee_array[2] = '<=';
                                    $payment_fee_array[3] = '>';
                                    $payment_fee_array[4] = '>=';

                                    $receive_pm_surcharge_str = "";

                                    // delete off 2-set setting
                                    $payment_fees_select_sql = "SELECT * 
																FROM " . $payment_fees_table_name . " 
																WHERE payment_methods_id='" . tep_db_input($pm_id) . "' 
																	AND payment_fees_customers_groups_id='" . tep_db_input($cgrp_id) . "' 
																	AND payment_methods_currency_code='" . tep_db_input($curr_code) . "' 
																	AND payment_methods_mode='RECEIVE'";
                                    $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
                                    if ($payment_fees_row = tep_db_fetch_array($payment_fees_result_sql)) {
                                        $delete_fees_sql = "DELETE FROM " . $payment_fees_table_name . "
															WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'
																AND payment_fees_customers_groups_id = '" . tep_db_input($cgrp_id) . "'
																AND payment_methods_currency_code = '" . tep_db_input($curr_code) . "'
																AND payment_methods_mode = 'RECEIVE'";
                                        tep_db_query($delete_fees_sql);

                                        $receive_pm_surcharge_str = '';
                                        if (isset($_POST['currency_code-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]) && $payment_fees_row['currency_code'] != $_POST['currency_code-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id])
                                            $receive_pm_surcharge_str .= "<tr><td valign='top'>Based On</td><td valign='top'>" . $payment_fees_row['currency_code'] . "</td><td valign='top'>" . $_POST['currency_code-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        if (isset($_POST['payment_fees_operator-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]) && $payment_fees_row['payment_fees_operator'] != $_POST['payment_fees_operator-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id])
                                            $receive_pm_surcharge_str .= "<tr><td valign='top'>Operator</td><td valign='top'>" . (isset($payment_fee_array[$payment_fees_row['payment_fees_operator']]) ? $payment_fee_array[$payment_fees_row['payment_fees_operator']] : $payment_fees_row['payment_fees_operator']) . "</td><td valign='top'>" . (isset($payment_fee_array[$_POST['payment_fees_operator-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]]) ? $payment_fee_array[$_POST['payment_fees_operator-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]] : $_POST['payment_fees_operator-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]) . "</td></tr>";
                                        if (isset($_POST['payment_fees_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]) && $payment_fees_row['payment_fees_min'] != $_POST['payment_fees_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id])
                                            $receive_pm_surcharge_str .= "<tr><td valign='top'>Min Fee</td><td valign='top'>" . $payment_fees_row['payment_fees_min'] . "</td><td valign='top'>" . $_POST['payment_fees_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        if (isset($_POST['payment_fees_cost_value-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]) && $payment_fees_row['payment_fees_cost_value'] != $_POST['payment_fees_cost_value-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id])
                                            $receive_pm_surcharge_str .= "<tr><td valign='top'>Cost</td><td valign='top'>" . $payment_fees_row['payment_fees_cost_value'] . "</td><td valign='top'>" . $_POST['payment_fees_cost_value-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        if (isset($_POST['payment_fees_cost_percent-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]) && $payment_fees_row['payment_fees_cost_percent'] != $_POST['payment_fees_cost_percent-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id])
                                            $receive_pm_surcharge_str .= "<tr><td valign='top'>Cost (%)</td><td valign='top'>" . $payment_fees_row['payment_fees_cost_percent'] . "</td><td valign='top'>" . $_POST['payment_fees_cost_percent-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        if (isset($_POST['payment_fees_cost_percent_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]) && $payment_fees_row['payment_fees_cost_percent_min'] != $_POST['payment_fees_cost_percent_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id])
                                            $receive_pm_surcharge_str .= "<tr><td valign='top'>Min</td><td valign='top'>" . $payment_fees_row['payment_fees_cost_percent_min'] . "</td><td valign='top'>" . $_POST['payment_fees_cost_percent_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        if (isset($_POST['payment_fees_cost_percent_max-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]) && $payment_fees_row['payment_fees_cost_percent_max'] != $_POST['payment_fees_cost_percent_max-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id])
                                            $receive_pm_surcharge_str .= "<tr><td valign='top'>Max</td><td valign='top'>" . $payment_fees_row['payment_fees_cost_percent_max'] . "</td><td valign='top'>" . $_POST['payment_fees_cost_percent_max-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        if (tep_not_null($receive_pm_surcharge_str)) {
                                            $receive_pm_surcharge_str = "<tr><td valign='top'></td><td valign='top'></td><td valign='top'></td></tr>" . $receive_pm_surcharge_str;
                                        }
                                    } else {
                                        $receive_pm_surcharge_str = "<tr><td valign='top'>Based On</td><td valign='top'>&nbsp;</td><td valign='top'>" . $_POST['currency_code-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        $receive_pm_surcharge_str .= "<tr><td valign='top'>Operator</td><td valign='top'>&nbsp;</td><td valign='top'>" . (isset($payment_fee_array[$_POST['payment_fees_operator-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]]) ? $payment_fee_array[$_POST['payment_fees_operator-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]] : $_POST['payment_fees_operator-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]) . "</td></tr>";
                                        $receive_pm_surcharge_str .= "<tr><td valign='top'>Min Fee</td><td valign='top'>&nbsp;</td><td valign='top'>" . $_POST['payment_fees_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        $receive_pm_surcharge_str .= "<tr><td valign='top'>Per Transaction Fees ($)</td><td valign='top'>&nbsp;</td><td valign='top'>" . $_POST['payment_fees_cost_value-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        $receive_pm_surcharge_str .= "<tr><td valign='top'>Per Transaction Fees (%)</td><td valign='top'>&nbsp;</td><td valign='top'>" . $_POST['payment_fees_cost_percent-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        $receive_pm_surcharge_str .= "<tr><td valign='top'>Min</td><td valign='top'>&nbsp;</td><td valign='top'>" . $_POST['payment_fees_cost_percent_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                        $receive_pm_surcharge_str .= "<tr><td valign='top'>Max</td><td valign='top'>&nbsp;</td><td valign='top'>" . $_POST['payment_fees_cost_percent_max-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id] . "</td></tr>";
                                    }

                                    if (tep_not_null($receive_pm_surcharge_str)) {
                                        $receive_pm_surcharge_str = "<li>Update " . $curr_code . " (" . (isset($customers_groups_array[$cgrp_id]) ? $customers_groups_array[$cgrp_id] : $cgrp_id) . ")<br><table border='1' width='100%' cellspacing='0' cellpadding='0'><tr><td>&nbsp;</td><td><b>Old</b></td><td><b>New</b></td></tr>" . $receive_pm_surcharge_str . "</table></li>";
                                        $receive_pm_surcharge_array[] = $receive_pm_surcharge_str;
                                    }

                                    if (in_array($curr_code, $master_currency)) {

                                        $update_surcharge_sql_data = array();
                                        $update_surcharge_sql_data['payment_methods_currency_code'] = tep_db_prepare_input($_POST['payment_methods_currency_code-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]);
                                        $update_surcharge_sql_data['payment_fees_operator'] = tep_db_prepare_input($_POST['payment_fees_operator-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]);
                                        $update_surcharge_sql_data['payment_fees_min'] = tep_db_prepare_input($_POST['payment_fees_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]);
                                        $update_surcharge_sql_data['payment_fees_cost_value'] = tep_db_prepare_input($_POST['payment_fees_cost_value-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]);
                                        $update_surcharge_sql_data['payment_fees_cost_percent'] = tep_db_prepare_input($_POST['payment_fees_cost_percent-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]);
                                        $update_surcharge_sql_data['payment_fees_cost_percent_min'] = tep_db_prepare_input($_POST['payment_fees_cost_percent_min-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]);
                                        $update_surcharge_sql_data['payment_fees_cost_percent_max'] = tep_db_prepare_input($_POST['payment_fees_cost_percent_max-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]);
                                        $update_surcharge_sql_data['currency_code'] = tep_db_prepare_input($_POST['currency_code-' . $cgrp_id . '-' . $curr_code . '-' . $fees_id]);
                                        $update_surcharge_sql_data['payment_fees_customers_groups_id'] = tep_db_prepare_input($_POST['payment_methods_customer_group_id']);
                                        $update_surcharge_sql_data['payment_fees_follow_group'] = 0;
                                        $update_surcharge_sql_data['payment_methods_id'] = tep_db_prepare_input($_POST['payment_methods_id']);
                                        $update_surcharge_sql_data['payment_methods_mode'] = 'RECEIVE';

                                        if ($fees_id == 0) {
                                            tep_db_perform($payment_fees_table_name, $update_surcharge_sql_data, 'insert');
                                        } else {
                                            $payment_fees_select_sql = "SELECT payment_fees_id
																		FROM " . $payment_fees_table_name . "
																		WHERE payment_fees_id = '" . tep_db_input($fees_id) . "'";
                                            $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
                                            if (tep_db_num_rows($payment_fees_result_sql) > 0) {
                                                tep_db_perform($payment_fees_table_name, $update_surcharge_sql_data, 'update', "payment_fees_id = '" . tep_db_input($fees_id) . "'");
                                            } else {
                                                $update_surcharge_sql_data['payment_fees_id'] = tep_db_prepare_input($fees_id);
                                                tep_db_perform($payment_fees_table_name, $update_surcharge_sql_data, 'insert');
                                            }
                                        }
                                    } else {
                                        $receive_pm_surcharge_array[] = "<li>Delete " . $curr_code;
                                        // extra currency setting, delete it off
                                        $delete_currency_sql = "DELETE FROM " . $payment_fees_table_name . "
																WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'
																	AND payment_methods_currency_code = '" . tep_db_input($curr_code) . "'
																	AND payment_methods_mode = 'RECEIVE'";
                                        tep_db_query($delete_currency_sql);
                                    }
                                }

                                if (count($receive_pm_surcharge_array)) {
                                    $receive_pm_email_array[] = "Update Surcharge:<br><ul>" . implode("", $receive_pm_surcharge_array) . "</ul>";
                                }
                            }
                        } // end group follow default
                    } else if (isset($_POST['follow_parent'])) {

                        $receive_pm_email_array[] = "Update Surcharge:<br><ul><li>Surcharge Dependency: Follow Parent</li></ul>";

                        //delete '$payment_fees_table_name'
                        $delete_fees_sql = "DELETE FROM " . $payment_fees_table_name . "
											WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'
												AND payment_methods_mode='RECEIVE'";
                        tep_db_query($delete_fees_sql);
                    }
                }
                if (!$error_flag) {
                    $message[] = "Data had been updated.";
                }
            }
            if ($error_flag) {
                echo "	<success>0</success>";
            } else {
                echo "	<success>1</success>";
            }
            foreach ($message as $msg) {
                echo "	<message>" . $msg . "</message>";
            }
            break;
        case 'update_surcharge_dependency':
        case 'update_sc_surcharge_dependency':
            $error_flag = false;

            if ($action == 'update_sc_surcharge_dependency') {
                $payment_fees_table_name = TABLE_PAYMENT_FEES_SC;
            } else {
                $payment_fees_table_name = TABLE_PAYMENT_FEES;
            }

            if (isset($_POST['follow_parent']) && $_POST['follow_parent'] == 'on') {
                // all surcharge setting follow parent's settings
                $payment_fees_select_sql = "SELECT payment_methods_id
											FROM " . $payment_fees_table_name . "
											WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'
											AND payment_methods_mode='RECEIVE'";
                $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
                if (tep_db_num_rows($payment_fees_result_sql) > 0) {

                    $receive_pm_email_instance_array[] = "Surcharge Dependency: Follow Parent";

                    $delete_fees_sql = "DELETE FROM " . $payment_fees_table_name . "
										WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'
										AND payment_methods_mode='RECEIVE'";
                    tep_db_query($delete_fees_sql);
                }

                if (count($receive_pm_email_instance_array)) {
                    $receive_pm_email_array[] = "Update Surcharge:<br><ul><li>" . implode("</li><li>", $receive_pm_email_instance_array) . "</li></ul>";
                }
            } else {
                // not following parent's settings
                $customer_groups = array();
                $currency_list = array();

                $delete_exist_sql = "DELETE FROM " . $payment_fees_table_name . " WHERE payment_methods_id='" . tep_db_input($pm_id) . "' AND payment_methods_mode='RECEIVE'";
                tep_db_query($delete_exist_sql);

                $cust_grp_select_sql = "SELECT DISTINCT customers_groups_id 
										FROM " . TABLE_CUSTOMERS_GROUPS . "
										ORDER BY customers_groups_id";
                $cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
                while ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
                    $customer_groups[] = $cust_grp_row['customers_groups_id'];
                }

                $payment_methods_instance_select_sql = "SELECT DISTINCT currency_code
														FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
														WHERE payment_methods_id = '" . tep_db_input($pm_id) . "'
														ORDER BY currency_code";
                $payment_methods_instance_result_sql = tep_db_query($payment_methods_instance_select_sql);
                if (tep_db_num_rows($payment_methods_instance_result_sql) > 0) {
                    while ($payment_methods_instance_row = tep_db_fetch_array($payment_methods_instance_result_sql)) {
                        $currency_list[] = $payment_methods_instance_row['currency_code'];
                    }
                } else {
                    // No record in payment_methods_instance table, search from parent
                    $parent_pm_instance_select_sql = "	SELECT DISTINCT pmi.currency_code
														FROM " . TABLE_PAYMENT_METHODS_INSTANCE . " AS pmi
														WHERE pmi.payment_methods_id = (
															SELECT pm.payment_methods_parent_id
															FROM " . TABLE_PAYMENT_METHODS . " AS pm
															WHERE pm.payment_methods_id='" . tep_db_input($pm_id) . "')";
                    $parent_pm_instance_result_sql = tep_db_query($parent_pm_instance_select_sql);
                    if (tep_db_num_rows($parent_pm_instance_result_sql) > 0) {
                        while ($parent_pm_instance_row = tep_db_fetch_array($parent_pm_instance_result_sql)) {
                            $currency_list[] = $parent_pm_instance_row['currency_code'];
                        }
                    }
                }

                foreach ($customer_groups as $grp_id) {
                    foreach ($currency_list as $currency_code) {
                        $payment_fees_array = array();
                        $payment_fees_array['payment_methods_id'] = tep_db_prepare_input($pm_id);
                        $payment_fees_array['payment_methods_mode'] = 'RECEIVE';
                        $payment_fees_array['payment_methods_currency_code'] = tep_db_prepare_input($currency_code);
                        $payment_fees_array['payment_fees_customers_groups_id'] = tep_db_prepare_input($grp_id);
                        tep_db_perform($payment_fees_table_name, $payment_fees_array, 'insert');
                    }
                }

                $receive_pm_email_instance_array[] = "Surcharge Dependency: Remove Follow Parent";
                if (count($receive_pm_email_instance_array)) {
                    $receive_pm_email_array[] = "Update Surcharge:<br><ul><li>" . implode("</li><li>", $receive_pm_email_instance_array) . "</li></ul>";
                }
            }

            echo "<success>1</success>";
            break;
        case 'copy_group_surcharge':
        case 'copy_group_sc_surcharge':
            $error_flag = false;

            if ($action == 'copy_group_sc_surcharge') {
                $payment_fees_table_name = TABLE_PAYMENT_FEES_SC;
            } else {
                $payment_fees_table_name = TABLE_PAYMENT_FEES;
            }

            $current_cgrp_id = $_POST['payment_methods_customer_group_id'];
            $copy_from_cgrp_id = $_POST['copy_grouping'];

            $customers_groups_array = array();
            $customers_groups_select_sql = "SELECT customers_groups_id, customers_groups_name
											FROM " . TABLE_CUSTOMERS_GROUPS . "
											WHERE customers_groups_id IN ('" . (int) $current_cgrp_id . "','" . (int) $copy_from_cgrp_id . "')";
            $customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
            while ($customers_groups_row = tep_db_fetch_array($customers_groups_result_sql)) {
                $customers_groups_array[$customers_groups_row['customers_groups_id']] = $customers_groups_row['customers_groups_name'];
            }

            $copy_from_select_sql = "SELECT * FROM " . $payment_fees_table_name . " WHERE payment_methods_id='" . tep_db_input($pm_id) . "' AND payment_fees_customers_groups_id='" . tep_db_input($copy_from_cgrp_id) . "' AND payment_methods_mode='RECEIVE'";
            $copy_from_result_sql = tep_db_query($copy_from_select_sql);
            if (tep_db_num_rows($copy_from_result_sql)) {
                $receive_pm_email_array[] = "Copy Surcharge (" . (isset($customers_groups_array[$current_cgrp_id]) ? $customers_groups_array[$current_cgrp_id] : $current_cgrp_id) . "): Copy From '" . (isset($customers_groups_array[$copy_from_cgrp_id]) ? $customers_groups_array[$copy_from_cgrp_id] : $copy_from_cgrp_id) . "'";
            }

            while ($copy_from_row = tep_db_fetch_array($copy_from_result_sql)) {
                $currency_code = $copy_from_row['payment_methods_currency_code'];

                $check_existing_select_sql = "	SELECT payment_fees_id 
												FROM " . $payment_fees_table_name . "
												WHERE payment_methods_id='" . tep_db_input($pm_id) . "'
												AND payment_fees_customers_groups_id='" . tep_db_input($current_cgrp_id) . "'
												AND payment_methods_currency_code='" . tep_db_input($currency_code) . "'
												AND payment_methods_mode='RECEIVE'";
                $check_existing_result_sql = tep_db_query($check_existing_select_sql);
                if (tep_db_num_rows($check_existing_result_sql) > 0) {
                    $data_row = tep_db_fetch_array($check_existing_result_sql);
                    $fees_id = $data_row['payment_fees_id'];

                    $payment_fees_array = array();
                    $payment_fees_array['payment_methods_id'] = tep_db_prepare_input($pm_id);
                    $payment_fees_array['payment_methods_currency_code'] = tep_db_prepare_input($currency_code);
                    $payment_fees_array['payment_fees_customers_groups_id'] = tep_db_prepare_input($current_cgrp_id);
                    $payment_fees_array['payment_methods_mode'] = 'RECEIVE';
                    $payment_fees_array['payment_fees_operator'] = tep_db_prepare_input($copy_from_row['payment_fees_operator']);
                    $payment_fees_array['payment_fees_min'] = tep_db_prepare_input($copy_from_row['payment_fees_min']);
                    $payment_fees_array['payment_fees_cost_value'] = tep_db_prepare_input($copy_from_row['payment_fees_cost_value']);
                    $payment_fees_array['payment_fees_cost_percent'] = tep_db_prepare_input($copy_from_row['payment_fees_cost_percent']);
                    $payment_fees_array['payment_fees_cost_percent_min'] = tep_db_prepare_input($copy_from_row['payment_fees_cost_percent_min']);
                    $payment_fees_array['payment_fees_cost_percent_max'] = tep_db_prepare_input($copy_from_row['payment_fees_cost_percent_max']);
                    $payment_fees_array['currency_code'] = tep_db_prepare_input($copy_from_row['currency_code']);
                    $payment_fees_array['payment_fees_follow_group'] = '0';
                    tep_db_perform($payment_fees_table_name, $payment_fees_array, 'update', "payment_fees_id = '" . tep_db_input($fees_id) . "'");
                } else {
                    $payment_fees_array = array();
                    $payment_fees_array['payment_methods_id'] = tep_db_prepare_input($pm_id);
                    $payment_fees_array['payment_methods_currency_code'] = tep_db_prepare_input($currency_code);
                    $payment_fees_array['payment_fees_customers_groups_id'] = tep_db_prepare_input($current_cgrp_id);
                    $payment_fees_array['payment_methods_mode'] = 'RECEIVE';
                    $payment_fees_array['payment_fees_operator'] = tep_db_prepare_input($copy_from_row['payment_fees_operator']);
                    $payment_fees_array['payment_fees_min'] = tep_db_prepare_input($copy_from_row['payment_fees_min']);
                    $payment_fees_array['payment_fees_cost_value'] = tep_db_prepare_input($copy_from_row['payment_fees_cost_value']);
                    $payment_fees_array['payment_fees_cost_percent'] = tep_db_prepare_input($copy_from_row['payment_fees_cost_percent']);
                    $payment_fees_array['payment_fees_cost_percent_min'] = tep_db_prepare_input($copy_from_row['payment_fees_cost_percent_min']);
                    $payment_fees_array['payment_fees_cost_percent_max'] = tep_db_prepare_input($copy_from_row['payment_fees_cost_percent_max']);
                    $payment_fees_array['currency_code'] = tep_db_prepare_input($copy_from_row['currency_code']);
                    $payment_fees_array['payment_fees_follow_group'] = '0';
                    tep_db_perform($payment_fees_table_name, $payment_fees_array, 'insert');
                }
            }

            echo "<success>1</success>";
            break;
        case 'delete_settlement':
            $message = array();
            $check_is_primary_select_sql = "SELECT payment_methods_instance_default, currency_code 
											FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
											WHERE payment_methods_instance_id = '" . (int) $pmi_id . "'";
            $check_is_primary_result_sql = tep_db_query($check_is_primary_select_sql);
            if (tep_db_num_rows($check_is_primary_result_sql)) {
                $check_is_primary_row = tep_db_fetch_array($check_is_primary_result_sql);
                if (!$check_is_primary_row['payment_methods_instance_default']) {
                    $delete_settlement_sql = "	DELETE FROM " . TABLE_PAYMENT_METHODS_INSTANCE . "
												WHERE payment_methods_instance_id = '" . (int) $pmi_id . "'";
                    tep_db_query($delete_settlement_sql);
                    $delete_settlement_setting_sql = "	DELETE FROM " . TABLE_PAYMENT_METHODS_INSTANCE_SETTING . "
														WHERE payment_methods_instance_id = '" . (int) $pmi_id . "'";
                    tep_db_query($delete_settlement_setting_sql);

                    $receive_pm_email_array[] = "Delete Merchant Setting (" . $check_is_primary_row['currency_code'] . ")";
                } else {
                    $error_flag = true;
                    $message[] = TEXT_INFO_UNABLE_DETELE_PRIMARY_CURRENCY;
                }
            } else {
                $error_flag = true;
                $message[] = TEXT_INFO_RECORD_NOT_FOUND;
            }
            if ($error_flag) {
                echo "	<success>0</success>";
            } else {
                $message[] = TEXT_INFO_DATA_DELETED;
                echo "	<success>1</success>";
            }
            foreach ($message as $msg) {
                echo "	<message><![CDATA[" . $msg . "]]></message>";
            }
            break;
        case 'update_send_payment':
            if (isset($_REQUEST['type'])) {
                switch ($_REQUEST['type']) {
                    case 'payment_methods_fields_status':
                        $payment_methods_fields_data_sql = array('payment_methods_fields_status' => (int) $_REQUEST['value']);
                        tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $payment_methods_fields_data_sql, 'update', " payment_methods_fields_id = '" . (int) $_REQUEST['pmfID'] . "'");
                        break;
                    case 'payment_methods_fields_required':
                        $payment_methods_fields_data_sql = array('payment_methods_fields_required' => (int) $_REQUEST['value']);
                        tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $payment_methods_fields_data_sql, 'update', " payment_methods_fields_id = '" . (int) $_REQUEST['pmfID'] . "'");
                        break;
                    case 'sort_order':
                        if (isset($_REQUEST['sort_order']) && count($_REQUEST['sort_order'])) {
                            foreach ($_REQUEST['sort_order'] as $payment_methods_fields_id_loop => $payment_methods_fields_sort_order_loop) {
                                if (!tep_not_null($payment_methods_fields_sort_order_loop)) {
                                    $payment_methods_fields_sort_order_loop = 50000;
                                }
                                $payment_methods_fields_data_sql = array('payment_methods_fields_sort_order' => (int) $payment_methods_fields_sort_order_loop);
                                tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $payment_methods_fields_data_sql, 'update', " payment_methods_fields_id = '" . (int) $payment_methods_fields_id_loop . "'");
                            }
                        }
                        break;
                }
                echo "	<success>1</success>";
                $msg = "Updated";
            } else {
                echo "	<success>0</success>";
                $msg = "Update failed";
            }
            echo "	<message><![CDATA[" . $msg . "]]></message>";

            break;
        case 'insert_pm_info':
        case 'update_pm_info':
            $error = false;
            $field_type = tep_db_prepare_input($_REQUEST["fields_type"]);

            switch ($field_type) {
                case "1" : // text box
                    $field_size = $_REQUEST["box_size"] . ',' . $_REQUEST["box_text_max_len"];
                    break;
                case "2" : // text area
                    $field_size = $_REQUEST["box_row"] . ',' . $_REQUEST["box_col"];
                    break;
                case "3" : // dropdown
                    $field_size = $_REQUEST["box_from"] . ',' . $_REQUEST["box_period"];
                    break;
                case "4" : // radio
                    $field_size = $_REQUEST["box_from"] . ',' . $_REQUEST["box_period"];
                    break;
                case "5" : // Date Selection
                    $field_size = $_REQUEST["box_from"] . ',' . $_REQUEST["box_period"];
                    break;
                default :
                    $field_size = 'NULL';
                    break;
            }

            if (isset($_REQUEST["selection"]) && tep_not_null($_REQUEST["selection"])) {
                $option_array = array();
                $option_array = explode("\n", tep_db_prepare_input($_REQUEST["selection"]));
                $option_array = array_filter($option_array, "filter_empty_val");
                $option_values = tep_db_prepare_input(implode(':~:', $option_array));

                $check_duplicate = array_unique($option_array);
                if (sizeof($check_duplicate) != sizeof($option_array)) {
                    $error = true;
                }
            }

            if (!$error) {
                $pm_field_sql_data_array = array('payment_methods_fields_title' => tep_db_prepare_input($_REQUEST["fields_title"]),
                    'payment_methods_id' => $pm_id,
                    'payment_methods_fields_type' => tep_db_prepare_input($_REQUEST["fields_type"]),
                    'payment_methods_fields_size' => $field_size,
                    'payment_methods_fields_option' => $option_values,
                    'payment_methods_fields_options_title' => $_REQUEST["option_title"],
                    'payment_methods_fields_pre_info' => tep_db_prepare_input($_REQUEST["fields_pre_info"]),
                    'payment_methods_fields_post_info' => tep_db_prepare_input($_REQUEST["fields_post_info"]),
                    'payment_methods_fields_required' => $_REQUEST["fields_required"],
                    'payment_methods_fields_status' => $_REQUEST["fields_status"],
                    'payment_methods_fields_sort_order' => $_REQUEST["fields_sort_order"]
                );
                if ($action == "insert_pm_info") {
                    tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $pm_field_sql_data_array);
                    echo "	<message><![CDATA[" . SUCCESS_PAYMENT_FIELD_INSERT . "]]></message>";
                } else {
                    $check_pmf_select_sql = "	SELECT payment_methods_fields_system_type
													FROM " . TABLE_PAYMENT_METHODS_FIELDS . "
													WHERE payment_methods_fields_id = '" . tep_db_input($pmf_id) . "'";
                    $check_pmf_result_sql = tep_db_query($check_pmf_select_sql);
                    $check_pmf_row = tep_db_fetch_array($check_pmf_result_sql);
                    if (tep_not_null($check_pmf_row['payment_methods_fields_system_type'])) {
                        $pm_field_sql_data_array['payment_methods_fields_required'] = 1;
                        $pm_field_sql_data_array['payment_methods_fields_status'] = 1;
                    }
                    tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $pm_field_sql_data_array, 'update', ' payment_methods_fields_id = "' . tep_db_input($pmf_id) . '"');
                    echo "	<message><![CDATA[" . SUCCESS_PAYMENT_FIELD_UPDATE . "]]></message>";
                }
                echo "	<success>1</success>";
            } else {
                echo "	<message><![CDATA[" . ERROR_DUPLICATE_OPTION_VALUE . "]]></message>";
                echo "	<success>0</success>";
            }
            break;
        case 'delete_send_payment_info':
            if (isset($_REQUEST['pmf_id'])) {
                $payment_methods_field_select_sql = "	SELECT 	payment_methods_fields_system_type
														FROM " . TABLE_PAYMENT_METHODS_FIELDS . " as pmf
														WHERE payment_methods_fields_id = '" . $pmf_id . "'";
                $payment_methods_field_result_sql = tep_db_query($payment_methods_field_select_sql);
                $payment_methods_field_row = tep_db_fetch_array($payment_methods_field_result_sql);

                if (!tep_not_null($payment_methods_field_row['payment_methods_fields_system_type'])) {
                    tep_db_query("DELETE FROM " . TABLE_PAYMENT_METHODS_FIELDS . " WHERE payment_methods_fields_id = '" . (int) $_REQUEST['pmf_id'] . "' AND payment_methods_fields_system_type = ''");
                    echo "	<message><![CDATA[" . SUCCESS_PAYMENT_FIELD_DELETE . "]]></message>";
                    echo "	<success>1</success>";
                } else {
                    echo "	<success>0</success>";
                    echo "	<message><![CDATA[" . ERROR_INVALID_PAYMENT_METHOD_FIELD . "]]></message>";
                }
            } else {
                echo "	<success>0</success>";
                echo "	<message><![CDATA[" . ERROR_INVALID_PAYMENT_METHOD_FIELD . "]]></message>";
            }
            break;
        case 'load_send_payment_info':
            echo "<payment_methods_fields>";
            $payment_methods_field_select_sql = "	SELECT *
														FROM " . TABLE_PAYMENT_METHODS_FIELDS . " as pmf
														WHERE payment_methods_fields_id = '" . $pmf_id . "'";
            $payment_methods_field_result_sql = tep_db_query($payment_methods_field_select_sql);
            if ($payment_methods_field_row = tep_db_fetch_array($payment_methods_field_result_sql)) {
                foreach ($payment_methods_field_row as $payment_methods_field_key_loop => $payment_methods_field_data_loop) {
                    echo "<" . $payment_methods_field_key_loop . "><![CDATA[" . $payment_methods_field_data_loop . "]]></" . $payment_methods_field_key_loop . ">";
                }
                switch ($payment_methods_field_row['payment_methods_fields_type']) {
                    case '1': // textbox
                        list($box_size, $box_text_max_len) = explode(",", $payment_methods_field_row['payment_methods_fields_size']);
                        echo "	<box_size><![CDATA[" . $box_size . "]]></box_size>
									<box_text_max_len><![CDATA[" . $box_text_max_len . "]]></box_text_max_len>";
                        break;
                    case '2': // textarea
                        list($box_row, $box_col) = explode(",", $payment_methods_field_row['payment_methods_fields_size']);
                        echo "	<box_row><![CDATA[" . $box_row . "]]></box_row>
									<box_col><![CDATA[" . $box_col . "]]></box_col>";
                        break;
                    case '3': // dropdown
                        if (tep_not_null($payment_methods_field_row["payment_methods_fields_option"])) {
                            $selection_array = explode(':~:', addslashes($payment_methods_field_row["payment_methods_fields_option"]));
                            echo "<selection><![CDATA[" . implode("\n", $selection_array) . "]]></selection>";
                        } else {
                            echo "<selection><![CDATA[]]></selection>";
                        }
                        list($box_from, $box_period) = explode(",", $payment_methods_field_row['payment_methods_fields_size']);
                        echo "	<option_title><![CDATA[" . $payment_methods_field_row['payment_methods_fields_options_title'] . "]]></option_title>
									<box_from><![CDATA[" . $box_from . "]]></box_from>
									<box_period><![CDATA[" . $box_period . "]]></box_period>";

                        break;
                    case '4': // radio
                        if (tep_not_null($payment_methods_field_row["payment_methods_fields_option"])) {
                            $selection_array = explode(':~:', addslashes($payment_methods_field_row["payment_methods_fields_option"]));
                            echo "<selection><![CDATA[" . implode("\n", $selection_array) . "]]></selection>";
                        } else {
                            echo "<selection><![CDATA[]]></selection>";
                        }
                        list($box_from, $box_period) = explode(",", $payment_methods_field_row['payment_methods_fields_size']);
                        echo "	<selection><![CDATA[" . $selection . "]]></selection>
									<box_from><![CDATA[" . $box_from . "]]></box_from>
									<box_period><![CDATA[" . $box_period . "]]></box_period>";
                        break;
                    case '5': // date
                        list($box_from, $box_period) = explode(",", $payment_methods_field_row['payment_methods_fields_size']);
                        echo "	<box_from><![CDATA[" . $box_from . "]]></box_from>
									<box_period><![CDATA[" . $box_period . "]]></box_period>";
                        break;
                    case '6': // Display Label
                        break;
                    case '7': // information text
                        if (tep_not_null($payment_methods_field_row["payment_methods_fields_option"])) {
                            $selection_array = explode(':~:', addslashes($payment_methods_field_row["payment_methods_fields_option"]));
                            echo "<selection><![CDATA[" . implode("\n", $selection_array) . "]]></selection>";
                        } else {
                            echo "<selection><![CDATA[]]></selection>";
                        }
                        break;
                }
            }
            echo "</payment_methods_fields>";
            break;
        case 'active_receive_payment_method':
        case 'inactive_receive_payment_method':
            if ($action == 'active_receive_payment_method') {
                if (tep_not_null($payment_methods_obj->get_filename()) && file_exists(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_filename())) {
                    include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $payment_methods_obj->get_filename());
                }
                if (tep_class_exists($payment_methods_obj->get_payment_methods_class_name())) {
                    $payment_methods_data_sql = array("payment_methods_receive_status" => 1);
                    tep_db_perform(TABLE_PAYMENT_METHODS, $payment_methods_data_sql, "update", " payment_methods_id = '" . $pm_id . "' ");
                    echo "<success>1</success>";
                    echo "<message><![CDATA[" . TEXT_INFO_DATA_UPDATED . "]]></message>";
                } else {
                    echo "<success>0</success>";
                    echo "<message><![CDATA[Update failed, no receive payment class file created for this.]]></message>";
                }
            } else {
                $payment_methods_data_sql = array("payment_methods_receive_status" => 0);
                tep_db_perform(TABLE_PAYMENT_METHODS, $payment_methods_data_sql, "update", " payment_methods_id = '" . $pm_id . "' ");
                echo "<success>1</success>";
                echo "<message><![CDATA[" . TEXT_INFO_DATA_UPDATED . "]]></message>";
            }

            break;
        case 'active_send_payment_method':
        case 'inactive_send_payment_method':
            if ($action == 'active_send_payment_method') {
                $payment_methods_data_sql = array("payment_methods_send_status" => 1);
            } else {
                $payment_methods_data_sql = array("payment_methods_send_status" => 0);
            }
            tep_db_perform(TABLE_PAYMENT_METHODS, $payment_methods_data_sql, "update", " payment_methods_id = '" . $pm_id . "' ");
            echo "<success>1</success>";
            echo "<message><![CDATA[" . TEXT_INFO_DATA_UPDATED . "]]></message>";
            break;
        case 'send_payment_tab':
            $payment_methods_data_sql = array();
            //$payment_methods_data_sql['payment_methods_send_status_mode'] = (int)(isset($_REQUEST['payment_methods_send_status_mode'])?$_REQUEST['payment_methods_send_status_mode']:'');
            if (isset($_REQUEST['payment_methods_send_required_info'])) {
                $payment_methods_data_sql['payment_methods_send_required_info'] = tep_db_prepare_input($_REQUEST['payment_methods_send_required_info']);
            }
            if (isset($_REQUEST['send_mode_name'])) {
                $payment_methods_data_sql['payment_methods_send_mode_name'] = tep_db_prepare_input($_REQUEST['send_mode_name']);
            }
            if (count($payment_methods_data_sql)) {
                tep_db_perform(TABLE_PAYMENT_METHODS, $payment_methods_data_sql, 'update', " payment_methods_id = '" . (int) $pm_id . "' ");
            }

            if (isset($_REQUEST['payment_methods_status_message'])) {
                $payment_methods_status_description_array = array();
                $payment_methods_status_description_array['payment_methods_status_message'] = tep_db_prepare_input($_REQUEST['payment_methods_status_message']);

                $payment_methods_status_description_select_sql = "	SELECT payment_methods_id 
																		FROM " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . "
																		WHERE payment_methods_id = '" . (int) $pm_id . "'";
                $payment_methods_status_description_result_sql = tep_db_query($payment_methods_status_description_select_sql);
                if (tep_db_num_rows($payment_methods_status_description_result_sql)) {
                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $payment_methods_status_description_array, 'update', " payment_methods_id = '" . (int) $pm_id . "' AND payment_methods_mode = 'SEND' AND payment_methods_status = '-1' AND languages_id = '1'");
                } else {
                    $payment_methods_status_description_array['payment_methods_status'] = '-1';
                    $payment_methods_status_description_array['languages_id'] = '1';
                    $payment_methods_status_description_array['payment_methods_mode'] = 'SEND';
                    $payment_methods_status_description_array['payment_methods_id'] = (int) $pm_id;
                    tep_db_perform(TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION, $payment_methods_status_description_array);
                }
            }

            if (isset($_REQUEST['use_parent'])) {
                tep_db_query("DELETE FROM " . TABLE_PAYMENT_FEES . " WHERE payment_methods_id = '" . (int) $pm_id . "' ");
            } else {
                $payment_fees_array = array();
                $payment_fees_select_sql = "SELECT payment_methods_id 
												FROM " . TABLE_PAYMENT_FEES . "
												WHERE payment_methods_id = '" . (int) $pm_id . "' 
													AND payment_methods_currency_code = 'USD'
													AND payment_methods_mode = 'SEND'";
                $payment_fees_result_sql = tep_db_query($payment_fees_select_sql);
                if ($payment_fees_row = tep_db_fetch_array($payment_fees_result_sql) || $payment_methods_obj->get_parent_id() == 0) {
                    $payment_fees_array['payment_fees_max'] = (isset($_REQUEST['payment_fees_max']) ? $_REQUEST['payment_fees_max'] : 0);
                    $payment_fees_array['payment_fees_min'] = (isset($_REQUEST['payment_fees_min']) ? $_REQUEST['payment_fees_min'] : 0);
                    $payment_fees_array['payment_fees_cost_value'] = (isset($_REQUEST['payment_fees_cost_value']) ? $_REQUEST['payment_fees_cost_value'] : 0);
                    $payment_fees_array['payment_fees_cost_percent'] = (isset($_REQUEST['payment_fees_cost_percent']) ? $_REQUEST['payment_fees_cost_percent'] : 0);
                    $payment_fees_array['payment_fees_cost_percent_min'] = (isset($_REQUEST['payment_fees_cost_percent_min']) ? $_REQUEST['payment_fees_cost_percent_min'] : 0);
                    $payment_fees_array['payment_fees_cost_percent_max'] = (isset($_REQUEST['payment_fees_cost_percent_max']) ? $_REQUEST['payment_fees_cost_percent_max'] : 0);
                    $payment_fees_array['payment_fees_bear_by'] = (isset($_REQUEST['payment_fees_bear_by']) ? $_REQUEST['payment_fees_bear_by'] : 0);
                    $payment_fees_array['payment_fees_below_min'] = (isset($_REQUEST['payment_fees_below_min']) ? $_REQUEST['payment_fees_below_min'] : 0);

                    if ($payment_methods_obj->get_parent_id() == 0 && !$payment_fees_row) {
                        $payment_fees_array['payment_methods_id'] = (int) $pm_id;
                        $payment_fees_array['payment_methods_mode'] = 'SEND';
                        $payment_fees_array['payment_methods_currency_code'] = 'USD';
                        tep_db_perform(TABLE_PAYMENT_FEES, $payment_fees_array);
                    } else {
                        tep_db_perform(TABLE_PAYMENT_FEES, $payment_fees_array, 'update', " payment_methods_id = '" . $pm_id . "' AND payment_methods_mode = 'SEND' AND payment_methods_currency_code = 'USD'");
                    }

                    $payment_methods_data_sql = array();
                    $payment_methods_data_sql['payment_methods_estimated_receive_period'] = (int) (isset($_REQUEST['estimated_receive_period']) ? $_REQUEST['estimated_receive_period'] : 0);

                    tep_db_perform(TABLE_PAYMENT_METHODS, $payment_methods_data_sql, 'update', " payment_methods_id = '" . (int) $pm_id . "' ");
                } else {
                    $payment_methods_data_sql = array();
                    $payment_method_select_sql = "	SELECT pf.*, pg.payment_methods_send_required_info, pg.payment_methods_estimated_receive_period
														FROM " . TABLE_PAYMENT_METHODS . " as pm 
														INNER JOIN " . TABLE_PAYMENT_FEES . " as pf 
															ON pm.payment_methods_parent_id = pf.payment_methods_id
														LEFT JOIN " . TABLE_PAYMENT_METHODS . " as pg 
															ON pm.payment_methods_parent_id = pg.payment_methods_id
														WHERE pm.payment_methods_id = '" . (int) $pm_id . "' 
															AND pf.payment_methods_currency_code  = 'USD'
															AND pf.payment_methods_mode = 'SEND'";
                    $payment_method_result_sql = tep_db_query($payment_method_select_sql);
                    if ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
                        $payment_fees_array = $payment_method_row;
                        $payment_methods_data_sql['payment_methods_send_required_info'] = $payment_method_row['payment_methods_send_required_info'];
                        $payment_methods_data_sql['payment_methods_estimated_receive_period'] = $payment_method_row['payment_methods_estimated_receive_period'];
                    }

                    unset($payment_fees_array['payment_fees_id']);
                    unset($payment_fees_array['payment_methods_send_required_info']);
                    unset($payment_fees_array['payment_methods_estimated_receive_period']);

                    $payment_fees_array['payment_methods_id'] = $pm_id;
                    $payment_fees_array['payment_methods_mode'] = 'SEND';
                    $payment_fees_array['payment_methods_currency_code'] = 'USD';
                    tep_db_perform(TABLE_PAYMENT_FEES, $payment_fees_array);

                    if (count($payment_methods_data_sql)) {
                        tep_db_perform(TABLE_PAYMENT_METHODS, $payment_methods_data_sql, 'update', " payment_methods_id = '" . (int) $pm_id . "' ");
                    }
                }
            }
            echo "<success>1</success>";
            echo "<message><![CDATA[" . TEXT_INFO_DATA_UPDATED . "]]></message>";
            break;
        default:
            if ($action != 'display_all_tab' && $action != 'display_status_tab' && $action != 'display_settings_tab' &&
                    $action != 'display_currencies_tab') {
                echo "<result><![CDATA[" . TEXT_INFO_UNKNOWN_REQUEST . "]]></result>";
            }
            break;
    }
}
echo '</response>';

if (count($receive_pm_email_array)) {
    $receive_pm_email_str = '	Payment Method: ' . $payment_methods_obj->get_title() . '(' . $pm_id . ')
								<ul>';
    foreach ($receive_pm_email_array as $receive_pm_email_body_loop) {
        $receive_pm_email_str .= '<li>' . $receive_pm_email_body_loop . '</li>';
    }
    $receive_pm_email_str .= '	</ul>';
    $email_to_array = tep_parse_email_string(STORE_RECEIVE_PAYMENT_METHODS_CHANGES_NOTIFICATION_EMAIL);

    $display_admin_group = tep_get_admin_group_name($_SESSION['login_email_address']);
    if (tep_not_null($display_admin_group)) {
        $display_admin_group = ' [' . $display_admin_group . ']';
    }

    for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
        @tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], EMAIL_TEXT_SUBJECT_CREW_NOTIFICATION, "Update by: " . $_SESSION['login_email_address'] . $display_admin_group . "<BR>" . $receive_pm_email_str, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
    }
}
?>