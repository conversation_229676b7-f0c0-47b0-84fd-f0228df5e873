<?
/*
  	$Id: payment_module.php,v 1.5 2010/10/18 11:38:04 boonhock Exp $
	
  	Developer: <PERSON> (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');

$currencies = new currencies();

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

$field_type_key_val_array = array(	'1' => 'Text Box',
									'2' => 'Text Area',
									'3' => 'Dropdown Menu',
									'4' => 'Radio Button',
									'5' => 'Date Selection',
									'6' => 'Display Label',
									'7' => 'Information Text'
								);
						
$field_type_array = array(	array	('id'=>'1', 'text'=>'Text Box'),
							array	('id'=>'2', 'text'=>'Text Area'),
							array	('id'=>'3', 'text'=>'Dropdown Menu'),
							array	('id'=>'4', 'text'=>'Radio Button'),
							array	('id'=>'5', 'text'=>'Date Selection'),
							array	('id'=>'6', 'text'=>'Display Label'),
							array	('id'=>'7', 'text'=>'Information Text')
						);

$fee_bearer_display_array = array(	'payer' => 'OffGamers',
									'beneficiary' => 'Beneficiary'
								);

$fee_bearer_array = array(	array	('id'=>'payer', 'text'=>'OffGamers'),
							array	('id'=>'beneficiary', 'text'=>'Beneficiary')
						);

$below_min_fee_bearer_array = array(	array	('id'=>'na', 'text'=>'Not allowed'),
										array	('id'=>'payer', 'text'=>'OffGamers'),
										array	('id'=>'beneficiary', 'text'=>'Beneficiary')
									);

$system_defined_type_id = 0;			
if ($pmt_row = tep_db_fetch_array(tep_db_query("SELECT payment_methods_types_id FROM " . TABLE_PAYMENT_METHODS_TYPES . " WHERE payment_methods_types_system_define='1'"))) {
	$system_defined_type_id = $pmt_row['payment_methods_types_id'];
}

if (tep_not_null($action)) {
	switch ($action) {
		case "insert_pm":
		case "update_pm":
			$error = false;
			
			if (!tep_not_null($HTTP_POST_VARS["send_type"])) {
				$error = true;
				$messageStack->add_session(ERROR_MISSING_SEND_PAYMENT_TYPE, 'error');
			}
			
			if (!tep_not_null($HTTP_POST_VARS["send_title"])) {
				$error = true;
				$messageStack->add_session(ERROR_MISSING_SEND_PAYMENT_TITLE, 'error');
			}
			
			if ($action == "insert_pm" && $HTTP_POST_VARS["send_type"] != $system_defined_type_id) {
				if (!tep_not_null($HTTP_POST_VARS["pm_currency"])) {
					$error = true;
					$messageStack->add_session(ERROR_MISSING_PAYOUT_CURRENCY, 'error');
				}
			}
			
			if (!$error) {
				$pm_sql_data_array = array(	'payment_methods_send_mode_name' => tep_db_prepare_input($HTTP_POST_VARS["send_title"]),
	    		   							'payment_methods_send_status_mode' => tep_db_prepare_input($HTTP_POST_VARS["send_status"]),
	    		   							'payment_methods_send_required_info' => tep_db_prepare_input($HTTP_POST_VARS["required_info"]),
	    		   							'payment_methods_estimated_receive_period' => (int)$HTTP_POST_VARS["pm_received_period"],
	    		   							'payment_methods_send_mass_payment' => (int)$HTTP_POST_VARS["opt_mass_payment"],
	    	 	   							'payment_methods_sort_order' => (int)$HTTP_POST_VARS["pm_order"],
	    	 	   							'payment_methods_types_id' =>  tep_db_prepare_input($HTTP_POST_VARS["send_type"])
		    	       						);
	        	
	        	$pm_fee_sql_data_array = array(	'payment_fees_max' => (double)$HTTP_POST_VARS["fee_max_amount"],
	    		   								'payment_fees_min' => (double)$HTTP_POST_VARS["fee_min_amount"],
	    	 	   								'payment_fees_cost_value' => (double)$HTTP_POST_VARS["fee_per_trans_fee"],
	    	 	   								'payment_fees_cost_percent' => (double)$HTTP_POST_VARS["fee_amt_based_fee"],
	    	 	   								'payment_fees_cost_percent_min' => (double)$HTTP_POST_VARS["fee_amt_based_fee_min"],
	    	 	   								'payment_fees_cost_percent_max' => (double)$HTTP_POST_VARS["fee_amt_based_fee_max"],
	    	 	   								'payment_fees_bear_by' => tep_db_prepare_input($HTTP_POST_VARS["fee_bear_by"]),
	    	 	   								'payment_fees_below_min' => tep_db_prepare_input($HTTP_POST_VARS["fee_below_min_bear_by"])
		    	       							);
		    	
	            if ($action == "insert_pm") {	// Send currency cannot be editable once payment method is created
	            	$pm_sql_data_array['payment_methods_send_currency'] = (($HTTP_POST_VARS["send_type"] != $system_defined_type_id) ? tep_db_prepare_input($HTTP_POST_VARS["pm_currency"]) : 0);
	            	
					tep_db_perform(TABLE_PAYMENT_METHODS, $pm_sql_data_array);
					$payment_method_id = tep_db_insert_id();
				} else {
					tep_db_perform(TABLE_PAYMENT_METHODS, $pm_sql_data_array, 'update', ' payment_methods_id ="'.(int)$_REQUEST["pmID"].'"');
					$payment_method_id = (int)$_REQUEST["pmID"];
				}
				
				$send_payment_fee_checking_select_sql = "	SELECT payment_methods_id 
															FROM " . TABLE_PAYMENT_FEES . " 
															WHERE payment_methods_id = '" . tep_db_input($payment_method_id) . "' 
																AND payment_methods_mode='SEND' ";
				$send_payment_fee_checking_result_sql = tep_db_query($send_payment_fee_checking_select_sql);
				
				if (tep_db_num_rows($send_payment_fee_checking_result_sql)) {	// Already has the setting record
					tep_db_perform(TABLE_PAYMENT_FEES, $pm_fee_sql_data_array, 'update', ' payment_methods_id ="'.tep_db_input($payment_method_id).'" AND payment_methods_mode="SEND"');
				} else {
					$pm_fee_sql_data_array['payment_methods_id'] = $payment_method_id;
					$pm_fee_sql_data_array['payment_methods_mode'] = 'SEND';
					
					tep_db_perform(TABLE_PAYMENT_FEES, $pm_fee_sql_data_array);
				}
				
				tep_redirect(tep_href_link(FILENAME_PAYMENT_MODULE));
			} else {
				if ($action == "insert_pm") {
					tep_redirect(tep_href_link(FILENAME_PAYMENT_MODULE, "action=new_pm"));
				} else {
					tep_redirect(tep_href_link(FILENAME_PAYMENT_MODULE, "action=edit_pm&pmID=".(int)$_REQUEST["pmID"]));
				}
			}
			
			break;
		case "insert_pm_info":
		case "update_pm_info":
			$error = false;
			
			$pmFID = (int)$HTTP_POST_VARS["pmFID"];
			$field_type = tep_db_prepare_input($HTTP_POST_VARS["field_type"]);
			
			switch ($field_type) {
				case "1" : // text box
					$field_size = $_REQUEST["box_size"] . ',' . $_REQUEST["box_text_max_len"];
					break;
				case "2" :	// text area
					$field_size = $_REQUEST["box_row"] . ',' . $_REQUEST["box_col"];
					break;
				case "5" :	// Date Selection
					$field_size = $_REQUEST["box_from"] . ',' . $_REQUEST["box_period"];
					break;
				default :
					$field_size = 'NULL';
					break;
			}
			
			if (isset($_REQUEST["selection"]) && tep_not_null($_REQUEST["selection"])) {
				$option_array = array();
				$option_array = explode("\r\n", $_REQUEST["selection"]);
				$option_array = array_filter($option_array, "filter_empty_val");
				$option_values = tep_db_prepare_input(implode(':~:', $option_array));
				
				$check_duplicate = array_unique($option_array);
				if (sizeof($check_duplicate) != sizeof($option_array)){
					$error = true;
				}
			}
			
			if (!$error) {
				$pm_field_sql_data_array = array(	'payment_methods_fields_title' => tep_db_prepare_input($_REQUEST["field_title"]),
	    		           							'payment_methods_fields_type' => tep_db_prepare_input($_REQUEST["field_type"]),
	    	 	          							'payment_methods_fields_size' => $field_size,
	    	 	          							'payment_methods_fields_option' => $option_values,
	    	 	          							'payment_methods_fields_options_title' => $_REQUEST["option_title"],
	    	 	          							'payment_methods_fields_pre_info' => tep_db_prepare_input($_REQUEST["field_pre_text"]),
	    	 	          							'payment_methods_fields_post_info' => tep_db_prepare_input($_REQUEST["field_pre_text"]),
	    	  	         							'payment_methods_fields_required' => $_REQUEST["mandatory_field"],
	    	  	         							'payment_methods_fields_status' => $_REQUEST["field_status"],
	    	  	         							'payment_methods_fields_sort_order' => $_REQUEST["field_order"]
	        	    	       						);
				
	            if ($action == "insert_pm_info") {
	            	$pm_field_sql_data_array['payment_methods_id'] = (int)$_REQUEST["pmID"];
	            	$messageStack->add_session(SUCCESS_PAYMENT_FIELD_INSERT, 'success');
	            	
					tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $pm_field_sql_data_array);
				} else {
					$messageStack->add_session(SUCCESS_PAYMENT_FIELD_UPDATE, 'success');
					
					tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $pm_field_sql_data_array, 'update', ' payment_methods_fields_id = "'.tep_db_input($pmFID).'"');
				}
				tep_redirect(tep_href_link(FILENAME_PAYMENT_MODULE));
			} else {
				$messageStack->add_session(ERROR_DUPLICATE_OPTION_VALUE, 'error');
				if ($action == "insert_pm_info") {
					tep_redirect(tep_href_link(FILENAME_PAYMENT_MODULE, "action=new_pm_info&pmID=".(int)$_REQUEST["pmID"]));
				} else {
					tep_redirect(tep_href_link(FILENAME_PAYMENT_MODULE, "action=edit_pm_info&pmFID=".tep_db_input($pmFID)));
				}
			}
			
			break;
		case "delete_pm":
			if (tep_not_null($_REQUEST["pmID"])) {
				$pm_field_delete_sql = "DELETE FROM " . TABLE_PAYMENT_METHODS_FIELDS . " WHERE payment_methods_id = '" . tep_db_input($_REQUEST["pmID"]) . "'";
				tep_db_query($pm_field_delete_sql);
				
				$pm_delete_sql = "DELETE FROM " . TABLE_PAYMENT_METHODS . " WHERE payment_methods_id = '" . tep_db_input($_REQUEST["pmID"]) . "'";
				tep_db_query($pm_delete_sql);
				
				$messageStack->add_session(SUCCESS_PAYMENT_METHOD_DELETE, 'success');
			}
			
			tep_redirect(tep_href_link(FILENAME_PAYMENT_MODULE));
			
			break;
		case "delete_pm_info":
			if (tep_not_null($_REQUEST["pmFID"])) {
				$pm_field_delete_sql = "DELETE FROM " . TABLE_PAYMENT_METHODS_FIELDS . " WHERE payment_methods_fields_id = '" . tep_db_input($_REQUEST["pmFID"]) . "'";
				tep_db_query($pm_field_delete_sql);
				
				$messageStack->add_session(SUCCESS_PAYMENT_FIELD_DELETE, 'success');
			}
			
			tep_redirect(tep_href_link(FILENAME_PAYMENT_MODULE));
			
			break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					</tr>
            					<tr>
    								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  								</tr>
            					<tr>
            						<td class="main" valign="top">
            						<?
            							if ($action != "new_pm") {
            								echo '[ <a href="'.tep_href_link(FILENAME_PAYMENT_MODULE, 'action=new_pm').'" >'.LINK_ADD_PAYMENT_METHOD.'</a> ]';
            							} else {
            								echo "&nbsp;";
            							}
            						?>
            						</td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?
if ($action == "new_pm" || $action == "edit_pm") {
	if ($_REQUEST["pmID"]) {
		$pm_select_sql = "	SELECT * 
							FROM " . TABLE_PAYMENT_METHODS . " 
							WHERE payment_methods_id = '" . tep_db_input($_REQUEST["pmID"]) . "'
								AND payment_methods_send_status ='1'";
		$pm_result_sql = tep_db_query($pm_select_sql);
		$pm_row = tep_db_fetch_array($pm_result_sql);
		
		$pm_send_fees_select_sql = "SELECT * 
									FROM " . TABLE_PAYMENT_FEES . " 
									WHERE payment_methods_id = '" . tep_db_input($_REQUEST["pmID"]) . "' 
										AND payment_methods_mode = 'SEND'";
		$pm_send_fees_result_sql = tep_db_query($pm_send_fees_select_sql);
		$pm_send_fees_row = tep_db_fetch_array($pm_send_fees_result_sql);
	}
	
	$currency_selection_array = array( array('id'=>'', 'text'=>PULL_DOWN_DEFAULT) );
	$currency_selection_array = array_merge($currency_selection_array, tep_get_currencies_array(array('AUG')));
	
	$pm_type_array = array();
	$pm_type_select_sql = "	SELECT payment_methods_types_id, payment_methods_types_name 
							FROM " . TABLE_PAYMENT_METHODS_TYPES . "
							WHERE payment_methods_types_mode = 'SEND'
							ORDER BY payment_methods_types_sort_order";
	$pm_type_result_sql = tep_db_query($pm_type_select_sql);
	while ($pm_type_row = tep_db_fetch_array($pm_type_result_sql)) {
		$pm_type_array[] = array('id' => $pm_type_row['payment_methods_types_id'], 'text' => $pm_type_row['payment_methods_types_name']);
	}
	
	if ($action == "edit_pm") {
		$uneditable_payout_currency = '';
		for ($cur_cnt=0; $cur_cnt < count($currency_selection_array); $cur_cnt++) {
			if ($currency_selection_array[$cur_cnt]['id'] == $pm_row["payment_methods_send_currency"]) {
				$uneditable_payout_currency = $currency_selection_array[$cur_cnt]['text'];
			}
		}
	}
?>
					<tr>
        				<td width="100%">
<?	echo tep_draw_form('payment_method_form', FILENAME_PAYMENT_MODULE, tep_get_all_get_params(array('action')) . 'action='.($action=="new_pm" ? 'insert_pm' : 'update_pm'), 'post', 'onSubmit="return payment_method_form_checking()"');
	echo tep_draw_hidden_field("pmID", $_REQUEST["pmID"]);
?>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td>
										<fieldset class="selectedFieldSet">
					      					<legend class="SectionHead"><?=SECTION_PAYMENT_METHOD_SETTING?></legend>
			        						<table border="0" width="100%" cellspacing="0" cellpadding="2">
												<tr>
													<td class="main" width="21%" valign="top"><?=ENTRY_PAYMENT_METHOD_SEND_TYPE?></td>
													<td class="main" valign="top">
														<?
														foreach ($pm_type_array as $idx => $pm_type) {
															echo ($idx==0 ? "" : "&nbsp;") . tep_draw_radio_field('send_type', $pm_type['id'], $pm_row["payment_methods_types_id"]==$pm_type['id'] ? true : false, '', 'id="send_type" onClick="showhide_currency(this.value);"') . "&nbsp;" . $pm_type['text'];
														}
														?>
													</td>
												</tr>
												<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
												<tr>
													<td class="main" width="21%" valign="top"><?=ENTRY_PAYMENT_METHOD_SEND_TITLE?></td>
													<td class="main" valign="top"><?=tep_draw_input_field('send_title', isset($pm_row["payment_methods_send_mode_name"]) ? $pm_row["payment_methods_send_mode_name"] : '', 'size="40" id="send_title"')?></td>
												</tr>
												<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
							      				<tr>
							      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_SEND_STATUS?></td>
							        				<td class="main">
							        					<?=tep_draw_radio_field('send_status', '1', $pm_row["payment_methods_send_status_mode"]=="1" ? true : false) . "&nbsp;" . TEXT_STATUS_ACTIVE . "&nbsp;" . tep_draw_radio_field('send_status', '0', $pm_row["payment_methods_send_status_mode"]=="1" ? false : true) . "&nbsp;" . TEXT_STATUS_INACTIVE?>
							        				</td>
							      				</tr>
							      				<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
							      				<tr>
							      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_REQUIRED_INFO?></td>
							        				<td class="main">
							        					<?=tep_draw_textarea_field('required_info', 'soft', '60', '5', isset($pm_row["payment_methods_send_required_info"]) ? $pm_row["payment_methods_send_required_info"] : '')?>
							        				</td>
							      				</tr>
							      				<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
							      				<tr>
							      					<td colspan="2">
							      						<div id="currency_info">
							      							<table border="0" width="100%" cellspacing="0" cellpadding="0">
							      								<tr>
																	<td class="main" width="21%" valign="top"><?=ENTRY_PAYMENT_METHOD_CURRENCY?></td>
																	<td class="main">
																		<?=($action == "new_pm" ? tep_draw_pull_down_menu("pm_currency", $currency_selection_array, $pm_row["payment_methods_send_currency"], '') : $uneditable_payout_currency) . '&nbsp;' . TEXT_PAYMENT_CURRENCY_NOT_EDITABLE?>
																	</td>
							      								</tr>
							      							</table>
							      						</div>
							      					</td>
							      				</tr>
							      				<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
							      				<tr>
							      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_RECEIVED_PERIOD?></td>
							        				<td class="main" valign="top">
							        					<?=tep_draw_input_field('pm_received_period', tep_not_null($pm_row["payment_methods_estimated_receive_period"]) ? $pm_row["payment_methods_estimated_receive_period"] : 0, 'size="6"')?>
							        				</td>
							      				</tr>
							      				<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
							      				<tr>
							      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_SUPPORT_MASS_PAYMENT?></td>
							        				<td class="main">
							        					<?=tep_draw_checkbox_field('opt_mass_payment', '1', isset($pm_row['payment_methods_send_mass_payment']) && $pm_row['payment_methods_send_mass_payment'] == '1' ? true : false, '', '')?>
							        				</td>
							      				</tr>
							      				<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
							      				<tr>
							      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_ORDER?></td>
							        				<td class="main">
							        					<?=tep_draw_input_field('pm_order', $pm_row["payment_methods_sort_order"] ? $pm_row["payment_methods_sort_order"] : 50000, 'size="6"')?>
							        				</td>
							      				</tr>
											</table>
										</fieldset>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td>
										<fieldset class="selectedFieldSet">
					      					<legend class="SectionHead"><?=SECTION_PAYMENT_FEES_SETTING?></legend>
			        						<table border="0" width="100%" cellspacing="0" cellpadding="2">
												<tr>
													<td class="main" width="21%" valign="top"><?=ENTRY_PAYMENT_FEES_WITHDRAW_MIN?></td>
													<td class="main" valign="top"><?=tep_draw_input_field('fee_min_amount', isset($pm_send_fees_row["payment_fees_min"]) ? $pm_send_fees_row["payment_fees_min"] : '', 'size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ')?></td>
												</tr>
												<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
							      				<tr>
							      					<td class="main" valign="top"><?=ENTRY_PAYMENT_FEES_WITHDRAW_MAX?></td>
							        				<td class="main" valign="top"><?=tep_draw_input_field('fee_max_amount', isset($pm_send_fees_row["payment_fees_max"]) ? $pm_send_fees_row["payment_fees_max"] : '', 'size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ')?></td>
							      				</tr>
							      				<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
							      				<tr>
							      					<td class="main" valign="top"><?=ENTRY_PAYMENT_TRANSACTION_FEES?></td>
							        				<td>
							        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
							        						<tr>
							        							<td class="main"><?=tep_draw_input_field('fee_per_trans_fee', $pm_send_fees_row["payment_fees_cost_value"] ? $pm_send_fees_row["payment_fees_cost_value"] : '', 'size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ') . TEXT_PER_TRANSACTION_FEES?></td>
							        						</tr>
							        						<tr>
							        							<td class="main">
							        							<?
							        								echo tep_draw_input_field('fee_amt_based_fee', $pm_send_fees_row["payment_fees_cost_percent"] ? $pm_send_fees_row["payment_fees_cost_percent"] : '', 'size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ') . TEXT_AMOUNT_BASED_FEES . '&nbsp;&nbsp;&nbsp;';
							        								echo tep_draw_input_field('fee_amt_based_fee_min', $pm_send_fees_row["payment_fees_cost_percent_min"] ? $pm_send_fees_row["payment_fees_cost_percent_min"] : '', 'size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ') . TEXT_AMOUNT_BASED_FEES_MIN . '&nbsp;/&nbsp;';
							        								echo tep_draw_input_field('fee_amt_based_fee_max', $pm_send_fees_row["payment_fees_cost_percent_max"] ? $pm_send_fees_row["payment_fees_cost_percent_max"] : '', 'size="10" onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ') . TEXT_AMOUNT_BASED_FEES_MAX;
							        							?>
							        							</td>
							        						</tr>
							        					</table>
							        				</td>
							      				</tr>
							      				<tr>
							        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
							      				</tr>
							      				<tr>
							      					<td class="main" valign="top"><?=ENTRY_PAYMENT_FEES_BEARER?></td>
							      					<td>
							        					<table border="0" cellspacing="0" cellpadding="2">
							        						<tr>
										        				<td class="main" valign="top">
										        					<?=tep_draw_pull_down_menu("fee_bear_by", $fee_bearer_array, $pm_send_fees_row["payment_fees_bear_by"], '').TEXT_PAYMENT_FEES_BELOW_MAX.'&nbsp;&nbsp;&nbsp;'?>
										        				</td>
										        				<td class="main" valign="top">
										        					<?=tep_draw_pull_down_menu("fee_below_min_bear_by", $below_min_fee_bearer_array, $pm_send_fees_row["payment_fees_below_min"], '').TEXT_PAYMENT_FEES_BELOW_MIN?>
										        				</td>
										        			</tr>
										        		</table>
										        	</td>
							      				</tr>
											</table>
										</fieldset>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td align="right">
										<?=($action=="new_pm" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PAYMENT_MODULE) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
							</form>
        				</td>
        			</tr>
	        		<script language="Javascript">
		        		<!--
						function payment_method_form_checking() {
							if (document.getElementById('send_type').value == "") {
								alert('Please enter the title for this payment type!');
								document.getElementById('send_type').focus();
								return false;
							}
							if (document.getElementById('send_title').value == "") {
								alert('Please enter the title for this payment method!');
								document.getElementById('send_title').focus();
								return false;
							}
							return true;
						}
						
						function showhide_currency(btn_selected) {
							if (btn_selected == 6) {
								document.getElementById('currency_info').innerHTML = '';
							} else {
								html_str = '<table border="0" width="100%" cellspacing="0" cellpadding="0">';
								html_str = html_str + '<tr><td class="main" width="21%" valign="top"><?=ENTRY_PAYMENT_METHOD_CURRENCY?></td>';
								html_str = html_str + '<td class="main">' + <?=($action == "new_pm" ? '\''.tep_draw_pull_down_menu("pm_currency", $currency_selection_array, $pm_row["payment_methods_send_currency"], '').'\'+' : '\''.$uneditable_payout_currency.'\'+') . '\'&nbsp;\'+' . '\''.TEXT_PAYMENT_CURRENCY_NOT_EDITABLE.'\''?>;
								html_str = html_str + '</td></tr></table>';
								document.getElementById('currency_info').innerHTML = html_str;
							}
							return true;
						}
						
						function init() {
							var radio_obj = document.forms['payment_method_form'].elements['send_type'];
							if (radio_obj) {
								var radio_length = radio_obj.length;
								if (radio_length != undefined) {
									for (var i=0; i < radio_length; i++) {
										if (radio_obj[i].checked)
											showhide_currency(radio_obj[i].value);
									}
								}
							}
						}
						init();
						//-->
					</script>
<?
} else if ($action == "new_pm_info" || $action == "edit_pm_info") {
	if ($_REQUEST["pmFID"]) {
		$pm_field_select_sql = "SELECT * 
								FROM " . TABLE_PAYMENT_METHODS_FIELDS . " 
								WHERE payment_methods_fields_id = '" . tep_db_input($_REQUEST["pmFID"]) . "'";
		$pm_field_result_sql = tep_db_query($pm_field_select_sql);
		$pm_field_row = tep_db_fetch_array($pm_field_result_sql);
	}
?>
					<tr>
        				<td width="100%">
<?	echo tep_draw_form('payment_method_field_form', FILENAME_PAYMENT_MODULE, tep_get_all_get_params(array('action')) . 'action='.($action=="new_pm_info" ? 'insert_pm_info' : 'update_pm_info'), 'post', 'onSubmit="return payment_method_field_form_checking()"');
	echo tep_draw_hidden_field("pmFID", $_REQUEST["pmFID"]);
?>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_PAYMENT_METHOD_FIELD_TITLE?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('field_title', $pm_field_row["payment_methods_fields_title"], 'size="40" id="field_title"')?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_FIELD_TYPE?></td>
									<td class="main">
										<?=tep_draw_pull_down_menu("field_type", $field_type_array, $pm_field_row["payment_methods_fields_type"], ' id="field_type" onChange="Todo(\'TypeFile\', \'ForeignKeyDisplayField\', \'div_dynamic\', value);"')?>
									</td>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_FIELD_SETTING?></td>
			        				<td class="main"><div id="div_dynamic"></div></td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top">&nbsp;</td>
			        				<td>
			        					<table border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_FIELD_PRE_TEXT?></td>
												<td class="main" valign="top"><?=tep_draw_input_field('field_pre_text', $pm_field_row["payment_methods_fields_pre_info"], 'size="40"')?></td>
												<td class="main" valign="top">&nbsp;</td>
												<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_FIELD_POST_TEXT?></td>
												<td class="main" valign="top"><?=tep_draw_input_field('field_post_text', $pm_field_row["payment_methods_fields_post_info"], 'size="40"')?></td>
											</tr>
										</table>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_FIELD_STATUS?></td>
			        				<td class="main">
			        					<?=tep_draw_radio_field('field_status', '1', $pm_field_row["payment_methods_fields_status"]=="1" ? true : false) . "&nbsp;" . TEXT_STATUS_ACTIVE . "&nbsp;" . tep_draw_radio_field('field_status', '0', $pm_field_row["payment_methods_fields_status"]=="1" ? false : true) . "&nbsp;" . TEXT_STATUS_INACTIVE?>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_FIELD_MANDATORY?></td>
			        				<td class="main">
			        					<?=tep_draw_radio_field('mandatory_field', '1', $pm_field_row["payment_methods_fields_required"]=="1" ? true : false) . "&nbsp;" . 'Yes' . "&nbsp;" . tep_draw_radio_field('mandatory_field', '0', $pm_field_row["payment_methods_fields_required"]=="1" ? false : true) . "&nbsp;" . 'No'?>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_PAYMENT_METHOD_FIELD_ORDER?></td>
			        				<td class="main">
			        					<?=tep_draw_input_field('field_order', $pm_field_row["payment_methods_fields_sort_order"] ? $pm_field_row["payment_methods_fields_sort_order"] : 50000, 'size="6" id="field_order"')?>
			        				</td>
			      				</tr>
								<tr>
									<td colspan="2" align="right">
										<?=($action=="new_pm_info" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PAYMENT_MODULE) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
							</form>
        				</td>
        			</tr>
        			<script>
	        		<!--
		        	<?
		        		if (tep_not_null($pm_field_row["payment_methods_fields_size"])) {
		        			list($first_val, $second_val) = explode(',', $pm_field_row["payment_methods_fields_size"]);
		        	?>
		        			var first_box_val = '<?=$first_val?>';
		        			var second_box_val = '<?=$second_val?>';
		        	<?	} else { ?>
		        			var first_box_val = '';
		        			var second_box_val = '';
		        	<?	}
		        		
		        		if (tep_not_null($pm_field_row["payment_methods_fields_option"])) {
		        			$selection_array = explode(':~:', addslashes($pm_field_row["payment_methods_fields_option"]));
		        	?>
		        			var selection_option_str = '<?=implode('\r\n', $selection_array)?>';
		        	<?	} else { ?>
		        			var selection_option_str = "";
		        	<?	} 
		        		
		        		if ($pm_field_row["payment_methods_fields_options_title"]) {
		        	?>		var is_title = 1;
		        	<?	} else { ?>
		        			var is_title = 0;
		        	<?	} ?>
		        		
						function Todo(name1, name2, name, val) {
							var out = '';
							switch(val) {
								case "1":
									out = getTextBoxParam();
									document.getElementById(name).innerHTML = out;
									break;
								case "2":
									out = getTextAreaParam();
									document.getElementById(name).innerHTML = out;
									break;
								case "3":
								case "4":
									out = getSelectBoxParam(val);
									document.getElementById(name).innerHTML = out;
									break;
								case "5":	// Date selection box
									out = getDateSelectionBoxParam();
									document.getElementById(name).innerHTML = out;
									break;
								case "6":	// Display label
									//out = getDateSelectionBoxParam();
									document.getElementById(name).innerHTML = 'No setting needed! This is just a normal text label.';
									break;
								case "7":	// Information text
									out = getInfoTextParam();
									document.getElementById(name).innerHTML = out;
									break;
							}
						}
						
						function getTextBoxParam() {
							var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" cellspacing="0" cellpadding="2"><tr><td class="main">Size</td><td class="main"><input name="box_size" id="box_size" type="text" value="'+first_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td><td class="main">Maximum Character</td><td class="main"><input name="box_text_max_len" id="box_text_max_len" type="text" value="'+second_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
							dyn_html += '</table></div>';
							
							return dyn_html;
						}
						
						function getTextAreaParam() {
							var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" cellspacing="0" cellpadding="2"><tr><td class="main">Row</td><td class="main"><input name="box_row" id="box_row" type="text" value="'+first_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td><td class="main">Column</td><td class="main"><input name="box_col" id="box_col" type="text" value="'+second_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
							dyn_html += '</table></div>';
							
							return dyn_html;
						}
						
						function getSelectBoxParam(input_type) {
							var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" cellspacing="0" cellpadding="2"><tr><td valign="top" class="main">Option values:</td><td><textarea name="selection" id="selection" style=width:370px;height:100px>'+selection_option_str+'</textarea></td></tr>'+
										'<tr><td>&nbsp;</td><td class="main">Each option values must be in a new line.</td></tr>';
										
							if (input_type == '3')	dyn_html += '<tr><td colspan="2" class="main">First option is just a title?&nbsp;<input type="radio" name="option_title" value="1" '+(is_title ? 'CHECKED' : '')+'>&nbsp;Yes&nbsp;<input type="radio" name="option_title" value="0" ' +(is_title ? '' : 'CHECKED')+'>&nbsp;No</td></tr>';
							
							dyn_html += '</table></div>';
							
							return dyn_html;
						}
						
						function getDateSelectionBoxParam() {
							var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" cellspacing="0" cellpadding="2"><tr><td class="main" valign="top">From</td><td class="main"><input name="box_from" id="box_from" type="text" value="'+first_box_val+'" size="12" onBlur="if (trim_str(this.value) != \'\' && (this.value != \'TODAY\' && !validateDate(trim_str(this.value))) ) { this.value = \'TODAY\'; }"><br><small>Options:<br>YYYY-MM-DD<br>"TODAY" to use system current date</small></td><td class="main" valign="top">Periods (days)</td><td class="main" valign="top"><input name="box_period" id="box_period" type="text" value="'+second_box_val+'" size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }"></td></tr>';
							
							dyn_html += '</table></div>';
							
							return dyn_html;
						}
						
						function getInfoTextParam() {
							var dyn_html = '<div style="padding-bottom: 0.5em;"><table border="0" cellspacing="0" cellpadding="2"><tr><td valign="top" class="main">Display Text</td><td><textarea name="selection" id="selection" style=width:370px;height:100px>'+selection_option_str+'</textarea></td></tr>';
							
							dyn_html += '</table></div>';
							
							return dyn_html;
						}
						
						var cur_sel = document.getElementById('field_type').selectedIndex + 1;
						
						Todo('', '', 'div_dynamic', cur_sel.toString(10));
						
						function payment_method_field_form_checking() {
							return true;
						}
					//-->
					</script>
<?
}

$pm_select_sql = "	SELECT * 
					FROM " . TABLE_PAYMENT_METHODS . " 
					WHERE payment_methods_send_status ='1' 
						AND payment_methods_parent_id <> '0' 
					ORDER BY payment_methods_sort_order";
$pm_result_sql = tep_db_query($pm_select_sql);

if (tep_db_num_rows($pm_result_sql)) {
?>
					<tr>
            			<td valign="top">
            				<table border="0" width="100%" cellspacing="1" cellpadding="1">
               					<tr>
               						<td width="3%" class="reportBoxHeading"><?="Action"?></td>
			       					<td width="10%" class="reportBoxHeading"><?=TABLE_HEADING_PAYMENT_METHOD_TITLE?></td>
					                <td width="3%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PAYMENT_METHOD_STATUS?></td>
					                
					                <td width="5%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PAYMENT_FEES_WITHDRAW_MIN?></td>
					                <td width="5%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PAYMENT_FEES_WITHDRAW_MAX?></td>
					                <td width="6%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PAYMENT_FEES_BEARER?></td>
					                <td width="10%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PAYMENT_TRANSACTION_FEES?></td>
					                <td width="12%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PAYMENT_METHOD_REQUIRED_INFO?></td>
					                
					                <td class="reportBoxHeading"><?=TABLE_HEADING_PAYMENT_METHOD_INFO?></td>
			   					</tr>
<?
	$row_count = 0;
	while ($pm_row = tep_db_fetch_array($pm_result_sql)) {
    	$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
    	
    	$trans_fee_array = array();
    	$trans_based_fee_limit_array = array();
    	
    	$pm_send_fees_select_sql = "SELECT * 
									FROM " . TABLE_PAYMENT_FEES . " 
									WHERE payment_methods_id = '" . tep_db_input($pm_row["payment_methods_id"]) . "' 
										AND payment_methods_mode = 'SEND'";
		$pm_send_fees_result_sql = tep_db_query($pm_send_fees_select_sql);
		$pm_send_fees_row = tep_db_fetch_array($pm_send_fees_result_sql);
		
		if ($pm_send_fees_row['payment_fees_cost_value'] > 0) 	$trans_fee_array[] = $currencies->format($pm_send_fees_row['payment_fees_cost_value']);
		
		if ($pm_send_fees_row['payment_fees_cost_percent'] > 0) {
			if ($pm_send_fees_row['payment_fees_cost_percent_min'] > 0)		$trans_based_fee_limit_array[] = 'min.' . $currencies->format($pm_send_fees_row['payment_fees_cost_percent_min']);
			if ($pm_send_fees_row['payment_fees_cost_percent_max'] > 0)		$trans_based_fee_limit_array[] = 'max.' . $currencies->format($pm_send_fees_row['payment_fees_cost_percent_max']);
			
			$trans_fee_array[] = $pm_send_fees_row['payment_fees_cost_percent'] . '%' . (count($trans_based_fee_limit_array) ? ' ('.implode(', ', $trans_based_fee_limit_array).')' : '');
		}
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top">
										<a href="<?=tep_href_link(FILENAME_PAYMENT_MODULE, 'action=edit_pm&pmID='.$pm_row["payment_methods_id"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
										<a href="javascript:void(confirm_delete('<?=addslashes($pm_row["payment_methods_send_mode_name"])?>', 'Payment Method', '<?=tep_href_link(FILENAME_PAYMENT_MODULE, 'action=delete_pm&pmID='.$pm_row["payment_methods_id"])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
									</td>
									<td class="reportRecords" valign="top"><?=$pm_row["payment_methods_send_mode_name"]?></td>
									<td class="reportRecords" align="center" valign="top">
									<?
										if ($pm_row['payment_methods_send_status_mode'] == '1') {
											echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
										} else {
											echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
										}
									?>
									</td>
									<td class="reportRecords" align="center" valign="top"><?=$pm_send_fees_row['payment_fees_min'] > 0 ? $pm_send_fees_row['payment_fees_min'] : TEXT_ANY?></td>
									<td class="reportRecords" align="center" valign="top"><?=$pm_send_fees_row['payment_fees_max'] > 0 ? $pm_send_fees_row['payment_fees_max'] : TEXT_ANY?></td>
									<td class="reportRecords" align="center" valign="top"><?=$fee_bearer_display_array[$pm_send_fees_row['payment_fees_bear_by']]?></td>
									<td class="reportRecords" align="center" valign="top"><?=(count($trans_fee_array) ? implode(' + ', $trans_fee_array) : 'Free').'<br>'.sprintf(TEXT_PAYMENT_BELOW_MIN, ucwords($pm_send_fees_row['payment_fees_below_min']))?></td>
									<td class="reportRecords" align="center" valign="top"><?=$pm_row['payment_methods_send_required_info']?></td>
									
									<td class="reportRecords" align="center" valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
			          							<td colspan="6"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
			          						</tr>
			               					<tr>
			               						<td class="subRecordsBoxHeading" valign="top" width="8%">
													[<a href="<?=tep_href_link(FILENAME_PAYMENT_MODULE, 'action=new_pm_info&pmID='.$pm_row["payment_methods_id"])?>">Add New</a>]
												</td>
												<td class="subRecordsBoxHeading"><?=TABLE_HEADING_PAYMENT_METHOD_FIELD_TITLE?></td>
						       					<td class="subRecordsBoxHeading"><?=TABLE_HEADING_PAYMENT_METHOD_FIELD_TYPE?></td>
						       					<td class="subRecordsBoxHeading" align="center"><?=TABLE_HEADING_PAYMENT_METHOD_FIELD_STATUS?></td>
								                <td class="subRecordsBoxHeading" align="center"><?=TABLE_HEADING_PAYMENT_METHOD_FIELD_MANDATORY?></td>
								                <td class="subRecordsBoxHeading" align="center"><?=TABLE_HEADING_PAYMENT_METHOD_FIELD_SORT_ORDER?></td>
						   					</tr>
						   					<tr>
			          							<td colspan="6"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px"></div></td>
			          						</tr>
<?
		$pm_field_select_sql = "SELECT * 
								FROM " . TABLE_PAYMENT_METHODS_FIELDS . " 
								WHERE payment_methods_id = '" . tep_db_input($pm_row["payment_methods_id"]) . "' 
								ORDER BY payment_methods_fields_sort_order";
		$pm_field_result_sql = tep_db_query($pm_field_select_sql);
		
		while ($pm_field_row = tep_db_fetch_array($pm_field_result_sql)) {
			if ($pm_field_row['payment_methods_fields_status'] == '1') {
				$field_status_html = tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
			} else {
				$field_status_html = tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
			}
			
			if ($pm_field_row['payment_methods_fields_required'] == '1') {
				$field_required_html = tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
			} else {
				$field_required_html = tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
			}
			
			echo '							<tr>
												<td class="reportRecords" valign="top" nowrap>
													<a href="'.tep_href_link(FILENAME_PAYMENT_MODULE, 'action=edit_pm_info&pmFID='.$pm_field_row["payment_methods_fields_id"]).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>
													<a href="javascript:void(confirm_delete(\''.$pm_field_row["payment_methods_fields_title"].'\', \'Payment Info\', \''.tep_href_link(FILENAME_PAYMENT_MODULE, 'action=delete_pm_info&pmFID='.$pm_field_row["payment_methods_fields_id"]).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"').'</a>
												</td>
												<td class="reportRecords" valign="top">'.$pm_field_row["payment_methods_fields_title"].'</td>
												<td class="reportRecords" valign="top">'.$field_type_key_val_array[$pm_field_row["payment_methods_fields_type"]].'</td>
												<td class="reportRecords" align="center" valign="top">'.$field_status_html.'</td>
												<td class="reportRecords" align="center" valign="top">'.$field_required_html.'</td>
												<td class="reportRecords" align="center" valign="top">'.$pm_field_row['payment_methods_fields_sort_order'].'</td>
											</tr>';
		}
?>
			          					</table>
									</td>
								</tr>
<?
		$row_count++;
	}
?>
		    				</table>
    					</td>
    				</tr>
<?
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>