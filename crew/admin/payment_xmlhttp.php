<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'store_credit.php');
include_once(DIR_WS_CLASSES . 'order.php');
require_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'payment_module_info.php');
include_once(DIR_WS_CLASSES . 'pipwave.php');
include_once(DIR_WS_CLASSES . 'payment_methods.php');
include_once(DIR_WS_CLASSES . 'c2c_invoice.php');
require_once(DIR_WS_CLASSES . 'ms_store_credit.php');
require_once(DIR_WS_CLASSES . 'g2g_serverless.php');

$currencies = new currencies();

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$admin_id = isset($_SESSION['login_id']) ? $_SESSION['login_id'] : '';

if (isset($_SESSION['language']) && tep_not_null($_SESSION['language'])) {
    if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '.php')) {
        include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '.php');
    }
}

echo '<response>';
if (tep_not_null($action)) {
    switch ($action) {
        case "unlock_store_payment":
            if (isset($_REQUEST['spID']) && tep_not_null($_REQUEST['spID'])) {
                $payment_manual_unlock_payment_permission = tep_admin_files_actions(FILENAME_PAYMENT, 'PAYMENT_MANUAL_UNLOCK_PAYMENT');
                if ($payment_manual_unlock_payment_permission) {
                    tep_db_query("UPDATE " . TABLE_STORE_PAYMENTS . " SET store_payments_lock = '0' WHERE store_payments_id = '" . (int) $_REQUEST['spID'] . "'");
                    // Insert payment history
                    $payment_history_sql_data_array = array('store_payments_id' => (int) $_REQUEST['spID'],
                        'date_added' => 'now()',
                        'payee_notified' => '0',
                        'comments' => 'Payment Unlock',
                        'changed_by' => $_SESSION['login_email_address'],
                        'changed_by_role' => 'admin'
                    );
                    tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
                    echo "<result>1</result>";
                } else {
                    echo "<result>0</result>";
                }
            } else {
                echo "<result>0</result>";
            }
            break;
        case "get_customer_aging":
            if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/account_statement.php')) {
                include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/account_statement.php');
            }

            $report_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'report_link');

            $res_code = '';
            $res_message = '';

            $bo_str = isset($_REQUEST['bo_str']) ? $_REQUEST['bo_str'] : '';
            $bo_id_array = explode(',', $bo_str);

            if (!tep_admin_check_boxes(FILENAME_ACCOUNT_STATEMENT, 'sub_boxes')) {
                $res_code = '-1';
                $res_message = JS_ERROR_ACC_STAT_NO_PERMISSION;
            } else {

                echo "<customer_aging_statistic>";
                foreach ($bo_id_array as $buyback_order_id) {
                    if ($buyback_order_id != '') {
                        $buyback_customer_id = $buyback_complete_date = $buyback_customer_order_id = '';
                        $customer_aging_str = $order_status_str = $order_id_str = '';

                        $buyback_customer_select_sql = "SELECT brg.customers_id, br.orders_id, bsh.date_added 
														FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
														INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
															ON (brg.buyback_request_group_id = br.buyback_request_group_id)
														INNER JOIN " . TABLE_BUYBACK_STATUS_HISTORY . " AS bsh
															ON (brg.buyback_request_group_id = bsh.buyback_request_group_id AND bsh.buyback_status_id=3)
														WHERE brg.buyback_request_group_id = '" . tep_db_input($buyback_order_id) . "'";
                        $buyback_customer_result_sql = tep_db_query($buyback_customer_select_sql, 'report_link');

                        if ($buyback_customer_row = tep_db_fetch_array($buyback_customer_result_sql)) {
                            $buyback_customer_id = $buyback_customer_row['customers_id'];
                            $buyback_customer_order_id = $buyback_customer_row['orders_id'];
                            $buyback_complete_date = $buyback_customer_row['date_added'];

                            if (isset($buyback_customer_order_id) && $buyback_customer_order_id > 0) {
                                // order id link
                                $order_id_str = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $buyback_customer_order_id . '&action=edit', 'NONSSL') . '" target="_blank">' . $buyback_customer_order_id . '</a>';

                                // calculate customer aging
                                $orders_select_sql = "	SELECT osh.date_added
														FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS osh
														INNER JOIN " . TABLE_ORDERS . " AS ord
															ON (osh.orders_id=ord.orders_id AND ord.orders_status IN (2,3))
														INNER JOIN " . TABLE_ORDERS . " AS b_ord
															ON (ord.customers_id =b_ord.customers_id AND b_ord.orders_id = '" . tep_db_input($buyback_customer_order_id) . "')
														WHERE osh.orders_status_id in (2,3)
														ORDER BY osh.orders_status_history_id";

                                $orders_result_sql = tep_db_query($orders_select_sql, 'report_link');
                                if ($orders_row = tep_db_fetch_array($orders_result_sql)) {
                                    $first_process_date = $orders_row['date_added'];

                                    if (isset($buyback_complete_date) && tep_not_null($buyback_complete_date)) {
                                        $customer_aging_str = floor((strtotime($buyback_complete_date) - strtotime($first_process_date)) / (60 * 60 * 24 * 30));
                                    }
                                }

                                // get order current status
                                $order = new order((int) $buyback_customer_order_id);
                                foreach ($order->price_info_icons as $sub_status => $icon_info) {
                                    $icon_img_src = DIR_WS_IMAGES . ($order->price_info[$sub_status] > 0 ? $icon_info['on'] : $icon_info['off']);
                                    $order_status_str .= '&nbsp;' . tep_image($icon_img_src, $icon_info['label'], 10, 10) . '&nbsp;';
                                }
                            }
                        }

                        echo '<buyback_order id="' . $buyback_order_id . '">';
                        echo "	<order_id><![CDATA[" . $order_id_str . "]]></order_id>";
                        echo "	<order_status><![CDATA[" . $order_status_str . "]]></order_status>";
                        echo "	<customer_aging><![CDATA[" . $customer_aging_str . "]]></customer_aging>";
                        echo "</buyback_order>";
                    }
                }
                $res_code = '1';
                echo "</customer_aging_statistic>";
            }

            echo "<res_code>" . $res_code . "</res_code>";
            echo "<res_message><![CDATA[" . $res_message . "]]></res_message>";

            tep_db_close('report_link');

            break;
        case "get_payment_aging":
            if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/payment.php')) {
                include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/payment.php');
            }

            require_once(DIR_WS_CLASSES . 'currencies.php');
            $currencies = new currencies();

            require_once(DIR_WS_CLASSES . 'payments.php');
            $xmlhttp_payments_object = new payments($admin_id, $_SESSION['login_email_address']);

            $res_code = '';
            $res_message = '';

            $payment_id = isset($_REQUEST['p_id']) ? $_REQUEST['p_id'] : '';

            if (!tep_admin_check_boxes(FILENAME_ACCOUNT_STATEMENT, 'sub_boxes')) {
                $res_code = '-1';
                $res_message = JS_ERROR_ACC_STAT_NO_PERMISSION;
            } else {
                $xmlhttp_payments_object->get_payment_aging_info($payment_id, false, $supplier_aging_str, $supplier_cb_ratio_str);
                echo "<payment_aging_statistic>";
                echo '<payment_aging id="' . $payment_id . '">';
                echo "	<supplier_payment_aging><![CDATA[" . $supplier_aging_str . "]]></supplier_payment_aging>";
                echo "	<cb_order_ratio><![CDATA[" . $supplier_cb_ratio_str . "]]></cb_order_ratio>";
                echo "</payment_aging>";
                echo "</payment_aging_statistic>";
                $res_code = '1';
            }

            echo "<res_code>" . $res_code . "</res_code>";
            echo "<res_message><![CDATA[" . $res_message . "]]></res_message>";

            break;
        case "reserve_trans":
            if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/account_statement.php')) {
                include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/account_statement.php');
            }

            /*             * *************************************************
              1. Check if admin user has permission
              2. This transaction belong to this user
              3. The mode is in correct mode
              4. Add comment to the transaction
              5. Update reserve pool accordingly
             * ************************************************* */

            $res_code = '';
            $res_message = '';

            $trans_id = isset($_REQUEST['t_id']) ? $_REQUEST['t_id'] : '';
            $trans_type = isset($_REQUEST['t_type']) ? $_REQUEST['t_type'] : '';
            $user_id = isset($_REQUEST['u_id']) ? $_REQUEST['u_id'] : '';
            $user_role = isset($_REQUEST['u_type']) ? $_REQUEST['u_type'] : '';
            $reserve_mode = isset($_REQUEST['r_mode']) ? $_REQUEST['r_mode'] : '';
            $reserve_remark = isset($_REQUEST['remark']) ? tep_db_prepare_input($_REQUEST['remark']) : '';

            if (!tep_admin_check_boxes(FILENAME_ACCOUNT_STATEMENT, 'sub_boxes')) {
                $res_code = '-1';
                $res_message = JS_ERROR_ACC_STAT_NO_PERMISSION;
            } else {
                $statement_select_sql = "	SELECT store_account_history_id, store_account_history_credit_amount, store_account_transaction_reserved, store_account_history_trans_type, store_account_history_currency 
											FROM " . TABLE_STORE_ACCOUNT_HISTORY . "
											WHERE user_id = '" . tep_db_input($user_id) . "' 
												AND user_role = '" . tep_db_input($user_role) . "' 
												AND store_account_history_trans_id = '" . tep_db_input($trans_id) . "' 
												AND store_account_history_trans_type = '" . tep_db_input($trans_type) . "'";
                $statement_result_sql = tep_db_query($statement_select_sql);

                if ($statement_row = tep_db_fetch_array($statement_result_sql)) {
                    $store_account_select_sql = "	SELECT store_account_balance_amount 
													FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
					 								WHERE user_id = '" . tep_db_input($user_id) . "' 
					 									AND user_role = '" . tep_db_input($user_role) . "' 
					 									AND store_account_balance_currency = '" . tep_db_input($statement_row['store_account_history_currency']) . "' ";
                    $store_account_result_sql = tep_db_query($store_account_select_sql);

                    if ($store_account_row = tep_db_fetch_array($store_account_result_sql)) {
                        if (tep_not_null($statement_row['store_account_history_trans_type']) && $statement_row['store_account_history_trans_type'] != 'P') {
                            if ($reserve_mode != $statement_row['store_account_transaction_reserved']) {
                                switch ($reserve_mode) {
                                    case "1": // Add to reserve
                                        tep_xmlhttp_update_trans_comment($trans_type, $trans_id, TEXT_COMMENT_ORDER_AMOUNT_RESERVED); // Add comment to the corresponding transaction

                                        $reserved_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . "
										 									SET store_account_reserve_amount = store_account_reserve_amount + " . (double) $statement_row['store_account_history_credit_amount'] . ",
										 										store_account_last_modified = now() 
										 									WHERE user_id = '" . tep_db_input($user_id) . "' 
										 										AND user_role = '" . tep_db_input($user_role) . "' 
										 										AND store_account_balance_currency = '" . tep_db_input($statement_row['store_account_history_currency']) . "' ";
                                        tep_db_query($reserved_balance_update_sql);

                                        // Update the reserve status
                                        $reserve_status_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_HISTORY . " 
																		SET store_account_transaction_reserved = '1' 
																		WHERE store_account_history_id = '" . tep_db_input($statement_row['store_account_history_id']) . "' ";
                                        tep_db_query($reserve_status_update_sql);

                                        // Insert comment
                                        $account_comment_data_array = array('store_account_history_id' => $statement_row['store_account_history_id'],
                                            'store_account_comments' => 'Add to Reserve' . "\n" . $reserve_remark,
                                            'store_account_comments_date_added' => 'now()',
                                            'store_account_comments_notified' => 0,
                                            'store_account_comments_added_by' => $_SESSION['login_email_address']
                                        );
                                        tep_db_perform(TABLE_STORE_ACCOUNT_COMMENTS, $account_comment_data_array);

                                        $res_code = '1';
                                        if($trans_type == 'SO'){
                                            $c_sql = "SELECT cc.username, csg.seller_group_name, csg.seller_group_id, c.customers_email_address
                                                FROM " . TABLE_C2C_CUSTOMERS . " AS cc
                                                INNER JOIN " . TABLE_C2C_SELLER_GROUPS . " AS csg
                                                    ON csg.seller_group_id = cc.seller_group_id
                                                INNER JOIN " . TABLE_CUSTOMERS . " AS c
                                                    ON cc.customers_id = c.customers_id
                                                WHERE cc.customers_id = '" . tep_db_input($user_id) . "'";                                            
                                            $c_res = tep_db_query($c_sql);
                                            if ($c_row = tep_db_fetch_array($c_res)) {
                                                
                                                $sell_order_id = $trans_id;
                                                $service_sql = "SELECT custom_products_type_child_id, orders_id
                                                        FROM " . TABLE_C2C_BUYBACK_PRODUCT . "
                                                        WHERE c2c_buyback_id = '".tep_db_input($sell_order_id) ."'";
                           
                                                $service_res = tep_db_query($service_sql);
                                                if ($service_row = tep_db_fetch_array($service_res)) {
                                                    if(($service_row['custom_products_type_child_id'] == 19) || ($c_row['seller_group_id'] == 6)) {
                                                        
                                                        $customer_order_id = $service_row['orders_id'];
                                                        $order_id_link = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $customer_order_id . '&action=edit') . '" target="_blank">' . $customer_order_id . '</a>';
                                                        $sell_order_id_link = '<a href="' . tep_href_link(FILENAME_C2C_BUYBACK_ORDER, 'selected_box=c2c&action=add_form&id='.$sell_order_id) . '" target="_blank">' . $sell_order_id . '</a>';

                                                        //email to G2G
                                                        $mail_content = 'Hi G2G Support' . ",\n\n";
                                                        $mail_content .= 'Seller credit @ '. $statement_row['store_account_history_currency'] . ' '. $statement_row['store_account_history_credit_amount'].
                                                                ' for Sell Order #' . $sell_order_id_link . ' (' . $c_row['customers_email_address'] . ')  and Purchase Order #'.$order_id_link .' has been placed on hold by ' . $_SESSION['login_email_address'] . 
                                                                ' on ' . date("Y-F-d") ." \n\n";
                                                        $mail_content .= 'Remark: ' . $reserve_remark . "\n";
                                                        //end  
                                                        
                                                        $email = explode(',', G2G_ADD_RESERVE_EMAIL);
                                                        $mail_subject = '[Seller Credit On Hold]#'.$sell_order_id.'-#'.$customer_order_id.' '.$c_row['seller_group_name'] . ' ' . $c_row['username'];
                                                    
                                                        for ($email_to_cnt=0; $email_to_cnt < count($email); $email_to_cnt++) {
                                                           tep_mail('G2G Support', $email[$email_to_cnt], $mail_subject, $mail_content, STORE_OWNER, G2G_NOREPLY_SENDER_EMAIL);
                                                        }
                                                        // tep_mail('G2G BDT', G2G_BDT_EMAIL, $mail_subject, $mail_content, STORE_OWNER, G2G_NOREPLY_SENDER_EMAIL);
                                                        
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    case "0": // Remove from reserve
                                        tep_xmlhttp_update_trans_comment($trans_type, $trans_id, TEXT_COMMENT_ORDER_AMOUNT_REMOVED_FROM_RESERVED); // Add comment to the corresponding transaction

                                        $reserved_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . "
										 									SET store_account_reserve_amount = store_account_reserve_amount - " . (double) $statement_row['store_account_history_credit_amount'] . ",
										 										store_account_last_modified = now() 
										 									WHERE user_id = '" . tep_db_input($user_id) . "' 
										 										AND user_role = '" . tep_db_input($user_role) . "' 
										 										AND store_account_balance_currency = '" . tep_db_input($statement_row['store_account_history_currency']) . "' ";
                                        tep_db_query($reserved_balance_update_sql);

                                        // Update the reserve status
                                        $reserve_status_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_HISTORY . " 
																		SET store_account_transaction_reserved = '0' 
																		WHERE store_account_history_id = '" . tep_db_input($statement_row['store_account_history_id']) . "' ";
                                        tep_db_query($reserve_status_update_sql);

                                        // Insert comment
                                        $account_comment_data_array = array('store_account_history_id' => $statement_row['store_account_history_id'],
                                            'store_account_comments' => 'Remove from Reserve' . "\n" . $reserve_remark,
                                            'store_account_comments_date_added' => 'now()',
                                            'store_account_comments_notified' => 0,
                                            'store_account_comments_added_by' => $_SESSION['login_email_address']
                                        );
                                        tep_db_perform(TABLE_STORE_ACCOUNT_COMMENTS, $account_comment_data_array);

                                        $res_code = '1';

                                        break;
                                    default: // Invalid mode
                                        $res_code = '-1';
                                        $res_message = JS_ERROR_ACC_STAT_TRANS_INVALID_MODE;

                                        break;
                                }
                            } else {
                                $res_code = '-1';
                                $res_message = JS_ERROR_ACC_STAT_TRANS_MODE_CHANGED;
                            }
                        } else {
                            $res_code = '-1';
                            $res_message = JS_ERROR_ACC_STAT_TRANS_NOT_EXISTS;
                        }
                    } else {
                        $res_code = '-1';
                        $res_message = JS_ERROR_ACC_STAT_CURRENCY_ACC_NOT_EXISTS;
                    }
                } else {
                    $res_code = '-1';
                    $res_message = JS_ERROR_ACC_STAT_TRANS_NOT_EXISTS;
                }
            }
                
            echo "<res_code>" . $res_code . "</res_code>";
            echo "<res_message><![CDATA[" . $res_message . "]]></res_message>";

            break;
        case "update_payment":
            if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/payment.php')) {
                include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/payment.php');
            }

            $payment_manual_unlock_payment_permission = tep_admin_files_actions(FILENAME_PAYMENT, 'PAYMENT_MANUAL_UNLOCK_PAYMENT');
            $payment_manual_complete_payment_permission = tep_admin_files_actions(FILENAME_PAYMENT, 'PAYMENT_MANUAL_COMPLETE_PAYMENT');

            require_once(DIR_WS_CLASSES . 'currencies.php');
            $currencies = new currencies();

            require_once(DIR_WS_CLASSES . 'payments.php');
            $xmlhttp_payments_object = new payments($admin_id, $_SESSION['login_email_address']);

            /*             * *************************************************
              1. Check if admin user has permission
              2. This payment still in its original status
              3. Add comment to the payment transation
              4. Email the payee

              $res_code
              - '-1' : Do not refresh the row contents
              - '1' : Refresh the row contents but doesn't mean positive action
             * ************************************************* */
            $email_beneficiary = false;
            $email_comments_str = '';

            $res_code = '';
            $res_message = '';

            $payment_id = isset($HTTP_GET_VARS['p_id']) ? $HTTP_GET_VARS['p_id'] : '';
            $from_status = isset($HTTP_GET_VARS['fr_status']) ? $HTTP_GET_VARS['fr_status'] : '';
            $manual_complete = isset($HTTP_GET_VARS['manual_complete']) ? $HTTP_GET_VARS['manual_complete'] : '';
            $to_status = isset($HTTP_GET_VARS['to_status']) ? $HTTP_GET_VARS['to_status'] : '';
            $filename = isset($HTTP_GET_VARS['filename']) ? $HTTP_GET_VARS['filename'] : '';
            $show_currency_ex_colum = isset($HTTP_GET_VARS['cur_ex']) && $HTTP_GET_VARS['cur_ex'] == '1' ? true : false;
            $latest_ex_rate = isset($HTTP_GET_VARS['ex_rate']) ? tep_db_prepare_input($HTTP_GET_VARS['ex_rate']) : '';
            $payment_reference = isset($HTTP_GET_VARS['pay_ref']) ? tep_db_prepare_input($HTTP_GET_VARS['pay_ref']) : '';

            $auto_complete_sc_withdraw_permission = tep_admin_files_actions(FILENAME_PAYMENT, 'PAYMENT_AUTO_CREDIT_WSC_TO_NRSC');

            // Move this function to top and use var
            $is_g2g = c2c_invoice::check_g2g_withdraw($payment_id);

            if (!tep_admin_check_boxes(FILENAME_PAYMENT, 'sub_boxes')) {
                $res_code = '-1';
                $res_message = JS_ERROR_PAYMENT_NO_PERMISSION;
            } else {
                $payment_current_info_select_sql = "SELECT sp.store_payments_status, sp.store_payments_request_currency, 
														sp.store_payments_after_fees_amount, sp.store_payments_paid_currency, 
														sp.store_payments_methods_id, sp.user_id, sp.user_role, pm.payment_methods_send_mass_payment,
														pm.payment_methods_parent_id , pm.payment_methods_id, pg.payment_methods_filename, 
														sp.store_payments_request_amount, sp.store_payments_id, sp.store_payments_fees,
														sp.store_payments_paid_currency_value, sp.store_payment_account_book_id 
													FROM " . TABLE_STORE_PAYMENTS . " as sp 
													LEFT JOIN " . TABLE_PAYMENT_METHODS . " as pm 
														ON sp.store_payments_methods_id = pm.payment_methods_id 
													LEFT JOIN " . TABLE_PAYMENT_METHODS . " as pg 
														ON pg.payment_methods_id = pm.payment_methods_parent_id 
													WHERE sp.store_payments_id = '" . tep_db_input($payment_id) . "'";
                if (!($payment_manual_complete_payment_permission && $manual_complete)) {
                    $payment_current_info_select_sql .= "AND ( ( ( pg.payment_methods_filename <> 'paypal.php' 
																	OR pm.payment_methods_send_mass_payment <>1 ) 
																OR (pg.payment_methods_filename = 'paypal.php' 
																	AND pm.payment_methods_send_mass_payment =1 
																	AND sp.store_payments_lock = '0' ) 
																)
																OR
																( ( pg.payment_methods_filename <> 'paypalEC.php' 
																	OR pm.payment_methods_send_mass_payment <>1 ) 
																OR (pg.payment_methods_filename = 'paypalEC.php' 
																	AND pm.payment_methods_send_mass_payment =1 
																	AND sp.store_payments_lock = '0' ) 
																) )";
                }

                $payment_current_info_result_sql = tep_db_query($payment_current_info_select_sql);
                if ($payment_current_info_row = tep_db_fetch_array($payment_current_info_result_sql)) {
                    if ($payment_current_info_row['store_payments_status'] == $from_status) {

                        $payment_methods_file_name = $payment_current_info_row['payment_methods_filename'];

                        $payment_methods_class_name = '';
                        if (tep_not_null($payment_methods_file_name)) {
                            $payment_methods_class_name = substr($payment_methods_file_name, 0, strrpos($payment_methods_file_name, '.'));
                        }

                        switch ($payment_current_info_row['store_payments_status']) {
                            case "1":
                                if ($to_status == '3' && (!$auto_complete_sc_withdraw_permission || $payment_current_info_row['user_role'] == 'supplier')) {
                                    // do not auto complete for :
                                    // a. no auto complete sc withdrawal permission
                                    // b. is a supplier's payment withdraw into store credit
                                    $to_status = '2';
                                }

                                if ($to_status == '2') { // From Pending -> Processing
                                    $user_id = $payment_current_info_row['user_id'];
                                    $user_role = $payment_current_info_row['user_role'];
                                    $user_info_row = $xmlhttp_payments_object->_get_user_particulars($user_id, $user_role);
                                    $paid_currency = $payment_current_info_row['store_payments_request_currency'];
                                    if (($user_info_row['disable_withdrawal'] == '0') && ($xmlhttp_payments_object->_check_store_acc_got_balance($user_id, $user_role, $paid_currency, $store_balance_amount, $total_reserve_amount) === true)) {
                                        $payment_update_sql_data_array = array('store_payments_status' => $to_status,
                                            'store_payments_last_modified' => 'now()');

                                        // Get latest exchange rate and automatically save it
                                        //if ($payment_current_info_row['store_payments_request_currency'] != $payment_current_info_row['store_payments_paid_currency']) {
                                        //	$payment_update_sql_data_array['store_payments_paid_currency_value'] = $currencies->advance_currency_conversion_rate($payment_current_info_row['store_payments_request_currency'], $payment_current_info_row['store_payments_paid_currency']);
                                        //}
                                        tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . tep_db_input($payment_id) . "'");

                                        // Insert payment history
                                        $payment_history_sql_data_array = array('store_payments_id' => $payment_id,
                                            'store_payments_status' => $to_status,
                                            'date_added' => 'now()',
                                            'payee_notified' => '1',
                                            'changed_by' => $_SESSION['login_email_address'],
                                            'changed_by_role' => 'admin'
                                        );
                                        tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);

                                        // Email to beneficiary
                                        $email_beneficiary = false;
                                        $res_code = '1';
                                    } else {
                                        $processing_error = true;

                                        $res_code = '1';
                                        if ($user_info_row['disable_withdrawal'] == '1') {
                                            $res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $payment_id) . "\n\r" . ERROR_PAYMENT_TRANSACTION_DISABLED_WITHDRAWAL;
                                        } else {
                                            $res_message = sprintf(ERROR_PAYMENT_TRANSACTION_INSUFFICIENT_BALANCE, $pay_id) . "\n\r" . "\n\r" . 'Total available balance = ' . $paid_currency . $store_balance_amount . "\n\r";
                                        }
                                    }
                                } else if ($to_status == '3') { // From Pending -> Completed, only for issue store credit
                                    $processing_error = false;
                                    $user_inactive_flag = false;
                                    $notification_type_text = '';

                                    // Check if Store Credit pay to Supplier
                                    $po_supplier_exist_sql = "	SELECT po_suppliers_id 
																FROM " . TABLE_PO_SUPPLIERS . " 
																WHERE po_suppliers_id = '" . tep_db_input($payment_current_info_row['user_id']) . "'";
                                    $po_supplier_exist_result = tep_db_query($po_supplier_exist_sql);
                                    $po_supplier_exist = tep_db_num_rows($po_supplier_exist_result);

                                    if ($po_supplier_exist > 0) {
                                        $third_user_info = $xmlhttp_payments_object->_get_withdraw_sc_supplier_info($payment_current_info_row['store_payment_account_book_id']);
                                        $user_account_id = $third_user_info['user_id'];
                                        $user_role = $third_user_info['user_role'];
                                        $notification_type_text = 'PO';
                                    } else {
                                        $user_account_id = $payment_current_info_row['user_id'];
                                        $user_role = $payment_current_info_row['user_role'];
                                        // get third party user account
                                        $third_user_info = $xmlhttp_payments_object->_get_withdraw_into_third_party_account_info($user_account_id, $user_role, $payment_current_info_row['store_payments_methods_id'], $payment_current_info_row['store_payment_account_book_id']);
                                    }

                                    if (is_array($third_user_info)) {
                                        $user_id = $third_user_info['user_id'];
                                        $user_role = $third_user_info['user_role'];
                                        $user_email = $third_user_info['user_email'];
                                    } else {
                                        $processing_error = true;
                                        $user_inactive_flag = true;
                                        $res_code = '-1';

                                        $res_message = sprintf(ERROR_PAYMENT_TRANSACTION_CUSTOMER_INACTIVE, $payment_id);
                                    }

                                    if (!$processing_error) {
                                        if ($user_info_row = $xmlhttp_payments_object->_get_user_particulars($user_id, $user_role)) {
                                            if (!in_array('4', $user_info_row['flags'])) { // Only process if not CB Flag
                                                $request_currency = $payment_current_info_row['store_payments_request_currency'];
                                                $request_currency_id = array_search($request_currency, $currencies->internal_currencies);
                                                $paid_currency = $payment_current_info_row['store_payments_paid_currency'];
                                                $paid_currency_id = array_search($paid_currency, $currencies->internal_currencies);
                                                $after_fees_amount = $payment_current_info_row['store_payments_after_fees_amount'];

                                                if (($user_info_row['disable_withdrawal'] == '0') && ($xmlhttp_payments_object->_check_store_acc_got_balance($user_id, $user_role, $paid_currency, $store_balance_amount, $total_reserve_amount) === true)) {
                                                    // Manual issue NR store credit
                                                    $gv_user_id = $user_id;
                                                    if ($user_role == 'supplier') { // Manual process for Supplier withdraw
                                                        $processing_error = true;

                                                        // if user Id is taken from supplier table, we need to search through customer table with email to get the customer Id to get the correct store credit account
                                                        /*
                                                          $user_info_select_sql = "	SELECT customers_id, customers_email_address
                                                          FROM " . TABLE_CUSTOMERS . "
                                                          WHERE customers_email_address = '" . tep_db_input($user_email) . "'";
                                                          $user_info_result_sql = tep_db_query($user_info_select_sql);
                                                          if ($user_info_row = tep_db_fetch_array($user_info_result_sql)) {
                                                          $gv_user_id = $user_info_row['customers_id'];
                                                          }
                                                         */
                                                    } else {
                                                        $sc_conversion_str = '';
                                                        if($is_g2g == true){
                                                            $scArrayResult = g2g_serverless::getScBalance($gv_user_id);
                                                        }
                                                        else{
                                                            $scArrayResult = ms_store_credit::getScBalance($gv_user_id);
                                                        }
                                                        
                                                        if ($scArrayResult) {
                                                            $to_customers_sc_currency_code = $scArrayResult['currency'];
                                                        } else { // if customer have no store credit account, use purchase currency
                                                            $to_customers_sc_currency_code = $paid_currency;
                                                        }

                                                        if ($request_currency_id == $paid_currency_id) {
                                                            $req_ex_rate = 1;
                                                        } else {
                                                            $req_ex_rate = $payment_current_info_row['store_payments_paid_currency_value'];
                                                            if (is_null($req_ex_rate)) { // means withdraw from currency same as selected payment method send currency
                                                                $req_ex_rate = 1;
                                                            }
                                                        }
                                                        $converted_from_qty = round($after_fees_amount * $req_ex_rate, 2);

                                                        $sc_deliver_qty = $currencies->advance_currency_conversion($converted_from_qty, $paid_currency, $to_customers_sc_currency_code, true, 'sell');

                                                        // Discrepancy checking mechanism
                                                        $usd_delivery_amount = $currencies->advance_currency_conversion($after_fees_amount, $request_currency, DEFAULT_CURRENCY, true, 'sell');
                                                        
                                                        // Get current Store Credit balance
                                                        $sc_current_balance = $scArrayResult['amount'];
                                                        $usd_sc_amount = $currencies->advance_currency_conversion($sc_current_balance, $to_customers_sc_currency_code, DEFAULT_CURRENCY, true, 'sell');

                                                        // Conversion string for payment history & po history
                                                        $sc_conversion_str .= "\n\n" . 'Payment Amount: ' . $to_customers_sc_currency_code . $sc_deliver_qty;

                                                        $sc_deliver_str = "Store credit for Payment " . $payment_id;

                                                        $trans_array = array(
                                                            'orders_id' => (string) $payment_id,
                                                            'reference_id' => (string) $payment_id,
                                                            'customers_id' => $gv_user_id,
                                                            'customers_role' => $user_role,
                                                            'requesting_id' => $_SESSION['login_email_address'],
                                                            'requesting_role' => 'admin',
                                                            'amount' => floatval($sc_deliver_qty),
                                                            'total_amount' => floatval($sc_deliver_qty),
                                                            'currency' => $to_customers_sc_currency_code,
                                                            'activity' => LOG_SC_ACTIVITY_TYPE_PAYMENT_WITHDRAW,
                                                            'message' => $sc_deliver_str,
                                                            'show_customer' => '1',
                                                        );

                                                        // Add Store Credit
                                                        if($is_g2g == true){
                                                            
                                                            $trans_array_g2g = array(
                                                                'request_id' =>  'disbursement-co-' . $gv_user_id . '-' . $payment_id,
                                                                'order_id' => (string)$payment_id,
                                                                'user_id' => $gv_user_id,
                                                                'user_role' => $user_role,
                                                                'requesting_id' => $_SESSION['login_email_address'],
                                                                'requesting_role' => 'admin',
                                                                'amount' => floatval($sc_deliver_qty),
                                                                'currency' => $to_customers_sc_currency_code,
                                                                'activity' => LOG_SC_ACTIVITY_TYPE_PAYMENT_WITHDRAW,
                                                                'param_1' => 'show_customer',
                                                            );   
        
                                                            $sc_delivery_result = g2g_serverless::setScTransaction($trans_array_g2g, 'ADD_CREDIT');
                                                        }
                                                        else{
                                                            $sc_og_delivery_result = ms_store_credit::setScTransaction($trans_array, 'ADD_CREDIT');
                                                            $sc_delivery_result = $sc_og_delivery_result['request_id'];
                                                        }
                                                        
                                                        if ($sc_delivery_result) {
                                                            $payment_reference = tep_db_prepare_input($sc_delivery_result);
                                                            $payment_reference_str = sprintf(TITLE_TRANS_STORE_CREDIT, $payment_reference);

                                                            // Discrepancy checking mechanism cont.
                                                            // Get currenct NRSC balance after the addition
                                                            if($is_g2g == true){
                                                                $scArrayResult2 = g2g_serverless::getScBalance($gv_user_id);
                                                            }
                                                            else{
                                                                $scArrayResult2 = ms_store_credit::getScBalance($gv_user_id);
                                                            }
                                                            $sc_current_balance = $scArrayResult2['amount'];
                                                            $usd_sc_new_amount = $currencies->advance_currency_conversion($sc_current_balance, $to_customers_sc_currency_code, DEFAULT_CURRENCY, true, 'sell');
                                                            // Notification for discrepancy checking
                                                            if (($usd_sc_new_amount - $usd_sc_amount - $usd_delivery_amount) > 5) {
                                                                $res_message = 'Latest Store Credit balance USD ' . $usd_sc_new_amount . ' - USD ' . $usd_sc_amount . ' exceed discrepancy threshold (USD 5) after crediting ' . $notification_type_text . ' amount (~ USD ' . $usd_delivery_amount . ').';
                                                                // Send Email notifications
                                                                $mail_subject = 'Discrepancy checking for Payment #' . (int) $payment_id;
                                                                $mail_content = 'Hi Admin,' . "\n";
                                                                $mail_content .= 'Alert on crediting Store Credit for Payment #' . (int) $payment_id . "\n";
                                                                $mail_content .= 'Action Date: ' . date('Y-m-d H:i:s') . "\n";
                                                                $mail_content .= 'Action: Store Credit Payment' . "\n";
                                                                $mail_content .= 'Action by: ' . $_SESSION['login_email_address'] . "\n";
                                                                $mail_content .= 'IP: ' . getenv("REMOTE_ADDR") . "\n";
                                                                $mail_content .= 'Alert message: ' . $res_message . "\n";

                                                                $email_to_array = tep_parse_email_string(NRSC_DISCREPANCY_CHECKING_EMAIL);
                                                                for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                                                    tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $mail_subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                                                }
                                                            } else {
                                                                // $res_message = 'Latest Store Credit balance USD '.$usd_sc_new_amount.' - USD '.$usd_sc_amount.' exceed discrepancy threshold (USD 5) after crediting '.$notification_type_text.' amount (~ USD '.$usd_delivery_amount.').';
                                                                // // Send Email notifications
                                                                //                   $mail_subject = 'Discrepancy checking for Payment #' . (int) $payment_id;
                                                                //                   $mail_content = 'Hi Admin,'."\n";
                                                                //                   $mail_content .= 'Alert on crediting Store Credit for Payment #' . (int) $payment_id . "\n";
                                                                //                   $mail_content .= 'Action Date: ' . date('Y-m-d H:i:s') . "\n";
                                                                //                   $mail_content .= 'Action: NRSC Payment' . "\n";
                                                                //                   $mail_content .= 'Action by: ' . $_SESSION['login_email_address'] . "\n";
                                                                //                   $mail_content .= 'IP: ' . getenv("REMOTE_ADDR") . "\n";
                                                                //                   $mail_content .= 'Alert message: ' . $res_message . "\n";
                                                                //                   $email_to_array = tep_parse_email_string(NRSC_DISCREPANCY_CHECKING_EMAIL);
                                                                //                      for ($email_to_cnt = 0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
                                                                //                          tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], $mail_subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                                                                //                      }
                                                            }
                                                        } else {
                                                            $processing_error = true;
                                                            $res_code = '1';

                                                            if (count($messageToStack) > 0) {
                                                                $err_msg = array();
                                                                for ($err_i = 0, $err_n = sizeof($messageToStack); $err_i < $err_n; $err_i++) {
                                                                    $err_msg[] = $messageToStack[$err_i]['text'];
                                                                }
                                                                $res_message = implode("\n", $err_msg);
                                                            }
                                                        }
                                                    }
                                                } else {
                                                    $processing_error = true;
                                                    $res_code = '-1';

                                                    if ($user_info_row['disable_withdrawal'] == '1') {
                                                        $res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $payment_id) . "\n\r" . ERROR_PAYMENT_TRANSACTION_DISABLED_WITHDRAWAL;
                                                    } else {
                                                        $res_message = sprintf(ERROR_PAYMENT_TRANSACTION_INSUFFICIENT_BALANCE, $payment_id) . "\n\r" . "\n\r" . 'Total available balance = ' . $paid_currency . $store_balance_amount . "\n\r";
                                                    }
                                                }
                                            } else {
                                                $processing_error = true;
                                                $res_code = '-1';
                                                $flags_array = tep_get_user_flags();

                                                $res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $payment_id);
                                                foreach ($user_info_row['flags'] as $flag) {
                                                    switch ($flag) {
                                                        case '4':
                                                            $res_message .= "\n\rThis account has been flag as " . $flags_array[$flag]['user_flags_name'] . ".";
                                                            break;
                                                    }
                                                }
                                            }
                                        } else {
                                            $processing_error = true;
                                            $res_code = '-1';

                                            $res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $payment_id) . "\n\r" . sprintf(ERROR_PAYMENT_TRANSACTION_NO_ACCOUNT, $payment_id);
                                        }
                                    }

                                    if (!$processing_error) {
                                        $payment_update_sql_data_array = array('store_payments_status' => $to_status,
                                            'store_payments_paid_amount' => $converted_from_qty,
                                            'store_payments_reference' => $payment_reference,
                                            'store_payments_last_modified' => 'now()');

                                        tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . tep_db_input($payment_id) . "'");

                                        //Update If DTU OR API Replenish Payment involved
                                        $payment_trans_select_sql = "SELECT store_payments_reimburse_id, store_payments_reimburse_table 
																	FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " 
																	WHERE store_payments_id = '" . tep_db_input($payment_id) . "'";
                                        $payment_trans_result_sql = tep_db_query($payment_trans_select_sql);
                                        while ($payment_trans_row = tep_db_fetch_array($payment_trans_result_sql)) {
                                            if ($payment_trans_row['store_payments_reimburse_table'] == TABLE_PURCHASE_ORDERS) {
                                                // DTU Payment
                                                $po_ref_select_sql = "SELECT purchase_orders_id FROM " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . " WHERE purchase_orders_id='" . $payment_trans_row['store_payments_reimburse_id'] . "'";
                                                $po_ref_result_sql = tep_db_query($po_ref_select_sql);
                                                if ($po_ref_row = tep_db_fetch_array($po_ref_result_sql)) {
                                                    $update_dtu_item = array('top_up_withdrawal_status' => '11', 'changed_by' => $_SESSION['login_email_address']);
                                                    tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $update_dtu_item, 'update', "purchase_orders_id = '" . (int) $po_ref_row['purchase_orders_id'] . "'");
                                                    tep_db_perform(TABLE_ORDERS_TOP_UP_PURCHASE_ORDERS, $update_dtu_item, 'update', "purchase_orders_id = '" . (int) $po_ref_row['purchase_orders_id'] . "'");
                                                }
                                                // API Payment
                                                $po_api_ref_sql = "SELECT purchase_orders_id FROM " . TABLE_LOG_API_RESTOCK . " WHERE purchase_orders_id='" . $payment_trans_row['store_payments_reimburse_id'] . "'";
                                                $po_api_ref_result = tep_db_query($po_api_ref_sql);
                                                if ($po_api_ref_row = tep_db_fetch_array($po_api_ref_result)) {
                                                    $update_api_item = array('api_withdrawal_status' => '4', 'changed_by' => $_SESSION['login_email_address']);
                                                    tep_db_perform(TABLE_LOG_API_RESTOCK, $update_api_item, 'update', "purchase_orders_id = '" . (int) $po_api_ref_row['purchase_orders_id'] . "'");
                                                    tep_db_perform(TABLE_API_REPLENISH_PURCHASE_ORDERS, $update_api_item, 'update', "purchase_orders_id = '" . (int) $po_api_ref_row['purchase_orders_id'] . "'");
                                                }
                                                // Consignment Payment
                                                $po_cdk_ref_sql = "SELECT purchase_orders_id FROM " . TABLE_CUSTOM_PRODUCTS_WITHDRAWAL . " WHERE purchase_orders_id='" . $payment_trans_row['store_payments_reimburse_id'] . "'";
                                                $po_cdk_ref_result = tep_db_query($po_cdk_ref_sql);
                                                if ($po_cdk_ref_row = tep_db_fetch_array($po_cdk_ref_result)) {
                                                    $update_cdk_item = array('cdk_withdrawal_status' => '4', 'changed_by' => $_SESSION['login_email_address']);
                                                    tep_db_perform(TABLE_CUSTOM_PRODUCTS_WITHDRAWAL, $update_cdk_item, 'update', "purchase_orders_id = '" . (int) $po_cdk_ref_row['purchase_orders_id'] . "'");
                                                    tep_db_perform(TABLE_CUSTOM_PRODUCTS_PURCHASE_ORDERS, $update_cdk_item, 'update', "purchase_orders_id = '" . (int) $po_cdk_ref_row['purchase_orders_id'] . "'");
                                                }

                                                // Insert PO History
                                                $sql_data_array = array(
                                                    'purchase_orders_id' => tep_db_input($payment_trans_row['store_payments_reimburse_id']),
                                                    'date_added' => 'now()',
                                                    'comments' => 'Payment ' . $payment_id . ' successful' . $sc_conversion_str,
                                                    'comments_type' => '1',
                                                    'changed_by' => $_SESSION['login_email_address']);
                                                tep_db_perform(TABLE_PURCHASE_ORDERS_STATUS_HISTORY, $sql_data_array);
                                            }
                                        }
                                        // END Update DTU OR API Payment
                                        // 
                                        // Update cron_po_dayterm if dayterm PO
                                        $daytem_sql = "SELECT sp.store_payments_id, sp.store_payments_reimburse_id, po.payment_type 
                                                                        FROM store_payments_transaction_info AS sp 
                                                                        INNER JOIN purchase_orders AS po 
                                                                                ON sp.store_payments_reimburse_id = po.purchase_orders_id 
                                                                                AND po.payment_type = 't' 
                                                                        WHERE sp.store_payments_id = '" . tep_db_input($payment_id) . "'";
                                        $dayterm_result = tep_db_query($daytem_sql);
                                        while ($dayterm_row = tep_db_fetch_array($dayterm_result)) {
                                            $po_dayterm_delete_sql = "DELETE FROM " . TABLE_CRON_PO_DAYTERM . "
                                                              WHERE cron_po_dayterm_trans_id = '" . $dayterm_row['store_payments_reimburse_id'] . "'";
                                            tep_db_query($po_dayterm_delete_sql);
                                        }

                                        $payment_received_by_str = '';

                                        $estimated_payment_receive_time_select_sql = "	SELECT payment_methods_estimated_receive_period 
					          															FROM " . TABLE_PAYMENT_METHODS . "
					          															WHERE payment_methods_id = '" . tep_db_input($payment_current_info_row['store_payments_methods_id']) . "'";
                                        $estimated_payment_receive_time_result_sql = tep_db_query($estimated_payment_receive_time_select_sql);
                                        
                                        if ($estimated_payment_receive_time_row = tep_db_fetch_array($estimated_payment_receive_time_result_sql)) {
                                            $payment_received_by_timestamp = mktime(date("H"), (int) date("i") + (int) $estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int) date("s"), date("m"), date("d"), date("Y"));
                                            $payment_notice_due_timestamp = mktime(date("H") + 24, (int) date("i") + (int) $estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int) date("s"), date("m"), date("d"), date("Y")); // Hard code 24 for now..Need global configuration

                                            $payment_received_by_date = date("Y-m-d H:i:s T", $payment_received_by_timestamp);
                                            $payment_notice_due_date = date("Y-m-d H:i:s T", $payment_notice_due_timestamp);
                                            
                                            if($is_g2g == true){
                                                $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G, $payment_received_by_date, $payment_notice_due_date);                                        
                                            }
                                            else{
                                                $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);                                        
                                            }
                                        }

                                        // Insert payment history
                                        $payment_history_sql_data_array = array('store_payments_id' => $payment_id,
                                            'store_payments_status' => $to_status,
                                            'date_added' => 'now()',
                                            'payee_notified' => '1',
                                            'comments' => $sc_deliver_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '') . $sc_conversion_str . $payment_received_by_str,
                                            'changed_by' => $_SESSION['login_email_address'],
                                            'changed_by_role' => 'admin'
                                        );
                                        tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
                                        
                                        if($is_g2g == true){
                                            $withdraw_datetime = c2c_invoice::get_store_payment_datetime($payment_id); 
                                        }
                                        // Email to beneficiary
                                        $email_beneficiary = true;
                                        $email_comments_str = $sc_deliver_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '');
                                        $res_code = '1';
                                    }
                                }

                                break;
                            case "2":
                                $processing_error = false;
                                $exRate_calculation_str = '';

                                $payment_manual_complete_payment_permission = tep_admin_files_actions(FILENAME_PAYMENT, 'PAYMENT_MANUAL_COMPLETE_PAYMENT');
                                if ($to_status == '3') { // From Processing -> Completed
                                    switch (strtolower($payment_methods_class_name)) {
                                        case 'paypal' :
                                            if ($payment_current_info_row['payment_methods_send_mass_payment']) {
                                                if ($payment_manual_complete_payment_permission && $manual_complete) {
                                                    // Insert exchange rate, update paid amount
                                                    $payment_update_sql_data_array = array('store_payments_status' => $to_status,
                                                        'store_payments_reference' => (isset($payment_reference) && tep_not_null($payment_reference) ? tep_db_prepare_input($payment_reference) : ''),
                                                        'store_payments_last_modified' => 'now()');

                                                    if ($payment_current_info_row['store_payments_request_currency'] != $payment_current_info_row['store_payments_paid_currency']) {
                                                        if (is_numeric($latest_ex_rate) && $latest_ex_rate > 0) {
                                                            $latest_ex_rate = (double) $latest_ex_rate;
                                                            $actual_payout_amount = $latest_ex_rate * $payment_current_info_row['store_payments_after_fees_amount'];

                                                            $payment_update_sql_data_array['store_payments_paid_currency_value'] = $latest_ex_rate;
                                                            $payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;

                                                            $exRate_calculation_str = $currencies->format($payment_current_info_row['store_payments_after_fees_amount'], false, $payment_current_info_row['store_payments_request_currency']) . ' x ' .
                                                                    $latest_ex_rate . ' = ' . $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
                                                        } else {
                                                            $processing_error = true;

                                                            $res_code = '1';
                                                            $res_message = JS_ERROR_PAYMENT_TRANS_INVALID_EX_RATE;
                                                        }
                                                    } else {
                                                        $actual_payout_amount = $payment_current_info_row['store_payments_after_fees_amount'];
                                                        $payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;

                                                        $exRate_calculation_str = $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
                                                    }
                                                } else {
                                                    $processing_error = false;
                                                    require(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPAL);
                                                    $paypal_obj = new paypal($payment_current_info_row['payment_methods_id']);
                                                    //$paypal_obj->get_merchant_account($payment_current_info_row['store_payments_request_currency']);
                                                    $result_array = $paypal_obj->mass_pay(array($payment_current_info_row['store_payments_id']));

                                                    // this 1 only for single transaction, so I check like this
                                                    foreach ($result_array as $result_data_loop) {
                                                        if (!$result_data_loop) {
                                                            $processing_error = true;
                                                        }
                                                    }

                                                    $payment_update_sql_data_array = array();
                                                }
                                                break;
                                            }
                                        case 'paypalec' :
                                            if ($payment_current_info_row['payment_methods_send_mass_payment']) {
                                                if ($payment_manual_complete_payment_permission && $manual_complete) {
                                                    // Insert exchange rate, update paid amount
                                                    $payment_update_sql_data_array = array('store_payments_status' => $to_status,
                                                        'store_payments_reference' => (isset($payment_reference) && tep_not_null($payment_reference) ? tep_db_prepare_input($payment_reference) : ''),
                                                        'store_payments_last_modified' => 'now()');

                                                    if ($payment_current_info_row['store_payments_request_currency'] != $payment_current_info_row['store_payments_paid_currency']) {
                                                        if (is_numeric($latest_ex_rate) && $latest_ex_rate > 0) {
                                                            $latest_ex_rate = (double) $latest_ex_rate;
                                                            $actual_payout_amount = $latest_ex_rate * $payment_current_info_row['store_payments_after_fees_amount'];

                                                            $payment_update_sql_data_array['store_payments_paid_currency_value'] = $latest_ex_rate;
                                                            $payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;

                                                            $exRate_calculation_str = $currencies->format($payment_current_info_row['store_payments_after_fees_amount'], false, $payment_current_info_row['store_payments_request_currency']) . ' x ' .
                                                                    $latest_ex_rate . ' = ' . $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
                                                        } else {
                                                            $processing_error = true;

                                                            $res_code = '1';
                                                            $res_message = JS_ERROR_PAYMENT_TRANS_INVALID_EX_RATE;
                                                        }
                                                    } else {
                                                        $actual_payout_amount = $payment_current_info_row['store_payments_after_fees_amount'];
                                                        $payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;

                                                        $exRate_calculation_str = $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
                                                    }
                                                } else {
                                                    $processing_error = false;
                                                    require(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPALEC);
                                                    $paypal_obj = new paypalEC($payment_current_info_row['payment_methods_id']);
                                                    $result_array = $paypal_obj->massPay(array($payment_current_info_row['store_payments_id']));

                                                    // this 1 only for single transaction, so I check like this
                                                    foreach ($result_array as $result_data_loop) {
                                                        if (!$result_data_loop) {
                                                            $processing_error = true;
                                                        }
                                                    }

                                                    $payment_update_sql_data_array = array();
                                                }
                                                break;
                                            }
                                        //disbursement-g2g
                                        case 'pipwavepg' :
                                            if ($payment_current_info_row['payment_methods_send_mass_payment']) {
                                                if ($payment_manual_complete_payment_permission && $manual_complete) {
                                                    // Insert exchange rate, update paid amount
                                                    $payment_update_sql_data_array = array('store_payments_status' => $to_status,
                                                        'store_payments_reference' => (isset($payment_reference) && tep_not_null($payment_reference) ? tep_db_prepare_input($payment_reference) : ''),
                                                        'store_payments_last_modified' => 'now()');

                                                    if ($payment_current_info_row['store_payments_request_currency'] != $payment_current_info_row['store_payments_paid_currency']) {
                                                        if (is_numeric($latest_ex_rate) && $latest_ex_rate > 0) {
                                                            $latest_ex_rate = (double) $latest_ex_rate;
                                                            $actual_payout_amount = $latest_ex_rate * $payment_current_info_row['store_payments_after_fees_amount'];

                                                            $payment_update_sql_data_array['store_payments_paid_currency_value'] = $latest_ex_rate;
                                                            $payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;

                                                            $exRate_calculation_str = $currencies->format($payment_current_info_row['store_payments_after_fees_amount'], false, $payment_current_info_row['store_payments_request_currency']) . ' x ' .
                                                                    $latest_ex_rate . ' = ' . $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
                                                        } else {
                                                            $processing_error = true;

                                                            $res_code = '1';
                                                            $res_message = JS_ERROR_PAYMENT_TRANS_INVALID_EX_RATE;
                                                        }
                                                    } else {
                                                        $actual_payout_amount = $payment_current_info_row['store_payments_after_fees_amount'];
                                                        $payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;

                                                        $exRate_calculation_str = $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
                                                    }
                                                } else {
                                                    $processing_error = true;
                                                    require(DIR_FS_CATALOG_MODULES . 'payment/pipwavePG.php');
                                                    $pipwavePG_obj = new pipwavePG($payment_current_info_row['payment_methods_id']);
                                                    $result_array = $pipwavePG_obj->massPay(array($payment_current_info_row['store_payments_id']));

                                                    // this 1 only for single transaction, so I check like this
                                                    foreach ($result_array as $result_data_loop_pm_id => $result_data_loop) {
                                                        if ($result_data_loop != 1) {
                                                            // $processing_error = true;
                                                            $res_code = '1';
                                                            $res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $result_data_loop_pm_id) . "\n\r"  . $result_data_loop;
                                                        } 
                                                    }
                                                    $payment_update_sql_data_array = array();

                                                   
                                                }
                                                break;
                                            }
                                        default :
                                            if (isset($payment_reference) && tep_not_null($payment_reference)) {
                                                // Insert exchange rate, update paid amount
                                                $payment_update_sql_data_array = array('store_payments_status' => $to_status,
                                                    'store_payments_reference' => $payment_reference,
                                                    'store_payments_last_modified' => 'now()');

                                                if ($payment_current_info_row['store_payments_request_currency'] != $payment_current_info_row['store_payments_paid_currency']) {
                                                    if (is_numeric($latest_ex_rate) && $latest_ex_rate > 0) {
                                                        $latest_ex_rate = (double) $latest_ex_rate;
                                                        $actual_payout_amount = $latest_ex_rate * $payment_current_info_row['store_payments_after_fees_amount'];

                                                        $payment_update_sql_data_array['store_payments_paid_currency_value'] = $latest_ex_rate;
                                                        $payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;

                                                        $exRate_calculation_str = $currencies->format($payment_current_info_row['store_payments_after_fees_amount'], false, $payment_current_info_row['store_payments_request_currency']) . ' x ' .
                                                                $latest_ex_rate . ' = ' . $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
                                                    } else {
                                                        $processing_error = true;

                                                        $res_code = '1';
                                                        $res_message = JS_ERROR_PAYMENT_TRANS_INVALID_EX_RATE;
                                                    }
                                                } else {
                                                    $actual_payout_amount = $payment_current_info_row['store_payments_after_fees_amount'];
                                                    $payment_update_sql_data_array['store_payments_paid_amount'] = $actual_payout_amount;

                                                    $exRate_calculation_str = $currencies->format($actual_payout_amount, false, $payment_current_info_row['store_payments_paid_currency']);
                                                }
                                            } else {
                                                $processing_error = true;

                                                $res_code = '1';
                                                $res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $payment_id) . "\n\r" . ERROR_PAYMENT_TRANSACTION_MISSING_PAYMENT_REFERENCE;
                                            }
                                            break;
                                    }

                                    if (!$processing_error) {
                                        if (count($payment_update_sql_data_array))
                                            tep_db_perform(TABLE_STORE_PAYMENTS, $payment_update_sql_data_array, 'update', "store_payments_id = '" . tep_db_input($payment_id) . "'");

                                        //Update If DTU OR API Replenish Payment involved
                                        $payment_trans_select_sql = "SELECT store_payments_reimburse_id, store_payments_reimburse_table 
	  										FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " 
	  										WHERE store_payments_id = '" . tep_db_input($payment_id) . "'";
                                        $payment_trans_result_sql = tep_db_query($payment_trans_select_sql);
                                        while ($payment_trans_row = tep_db_fetch_array($payment_trans_result_sql)) {
                                            if ($payment_trans_row['store_payments_reimburse_table'] == TABLE_PURCHASE_ORDERS) {
                                                // DTU Payment
                                                $po_ref_select_sql = "SELECT purchase_orders_id FROM " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . " WHERE purchase_orders_id='" . $payment_trans_row['store_payments_reimburse_id'] . "'";
                                                $po_ref_result_sql = tep_db_query($po_ref_select_sql);
                                                if ($po_ref_row = tep_db_fetch_array($po_ref_result_sql)) {
                                                    $update_dtu_item = array('top_up_withdrawal_status' => '11', 'changed_by' => $_SESSION['login_email_address']);
                                                    tep_db_perform(TABLE_ORDERS_TOP_UP_WITHDRAWAL, $update_dtu_item, 'update', "purchase_orders_id = '" . (int) $po_ref_row['purchase_orders_id'] . "'");
                                                    tep_db_perform(TABLE_ORDERS_TOP_UP_PURCHASE_ORDERS, $update_dtu_item, 'update', "purchase_orders_id = '" . (int) $po_ref_row['purchase_orders_id'] . "'");
                                                }
                                                // API Payment
                                                $po_api_ref_sql = "SELECT purchase_orders_id FROM " . TABLE_LOG_API_RESTOCK . " WHERE purchase_orders_id='" . $payment_trans_row['store_payments_reimburse_id'] . "'";
                                                $po_api_ref_result = tep_db_query($po_api_ref_sql);
                                                if ($po_api_ref_row = tep_db_fetch_array($po_api_ref_result)) {
                                                    $update_api_item = array('api_withdrawal_status' => '4', 'changed_by' => $_SESSION['login_email_address']);
                                                    tep_db_perform(TABLE_LOG_API_RESTOCK, $update_api_item, 'update', "purchase_orders_id = '" . (int) $po_api_ref_row['purchase_orders_id'] . "'");
                                                    tep_db_perform(TABLE_API_REPLENISH_PURCHASE_ORDERS, $update_api_item, 'update', "purchase_orders_id = '" . (int) $po_api_ref_row['purchase_orders_id'] . "'");
                                                }
                                                // Consignment Payment
                                                $po_cdk_ref_sql = "SELECT purchase_orders_id FROM " . TABLE_CUSTOM_PRODUCTS_WITHDRAWAL . " WHERE purchase_orders_id='" . $payment_trans_row['store_payments_reimburse_id'] . "'";
                                                $po_cdk_ref_result = tep_db_query($po_cdk_ref_sql);
                                                if ($po_cdk_ref_row = tep_db_fetch_array($po_cdk_ref_result)) {
                                                    $update_cdk_item = array('cdk_withdrawal_status' => '4', 'changed_by' => $_SESSION['login_email_address']);
                                                    tep_db_perform(TABLE_CUSTOM_PRODUCTS_WITHDRAWAL, $update_cdk_item, 'update', "purchase_orders_id = '" . (int) $po_cdk_ref_row['purchase_orders_id'] . "'");
                                                    tep_db_perform(TABLE_CUSTOM_PRODUCTS_PURCHASE_ORDERS, $update_cdk_item, 'update', "purchase_orders_id = '" . (int) $po_cdk_ref_row['purchase_orders_id'] . "'");
                                                }
                                            }
                                        }
                                        // END Update DTU OR API Payment

                                        $payment_received_by_str = '';

                                        $store_payments_info_select_sql = "	SELECT store_payments_id
					          												FROM " . TABLE_STORE_PAYMENTS . "
					          												WHERE store_payments_id = '" . $payment_id . "'
					          													AND store_payments_status = '" . $to_status . "'";
                                        $store_payments_info_result_sql = tep_db_query($store_payments_info_select_sql);
                                        if (tep_db_num_rows($store_payments_info_result_sql)) {
                                            $estimated_payment_receive_time_select_sql = "	SELECT payment_methods_estimated_receive_period 
						          															FROM " . TABLE_PAYMENT_METHODS . "
						          															WHERE payment_methods_id = '" . tep_db_input($payment_current_info_row['store_payments_methods_id']) . "'";
                                            $estimated_payment_receive_time_result_sql = tep_db_query($estimated_payment_receive_time_select_sql);

                                            if ($estimated_payment_receive_time_row = tep_db_fetch_array($estimated_payment_receive_time_result_sql)) {
                                                $payment_received_by_timestamp = mktime(date("H"), (int) date("i") + (int) $estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int) date("s"), date("m"), date("d"), date("Y"));
                                                $payment_notice_due_timestamp = mktime(date("H") + 24, (int) date("i") + (int) $estimated_payment_receive_time_row['payment_methods_estimated_receive_period'], (int) date("s"), date("m"), date("d"), date("Y")); // Hard code 24 for now..Need global configuration

                                                $payment_received_by_date = date("Y-m-d H:i:s T", $payment_received_by_timestamp);
                                                $payment_notice_due_date = date("Y-m-d H:i:s T", $payment_notice_due_timestamp);
                                          
                                                if($is_g2g == true){
                                                    $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_G2G, $payment_received_by_date, $payment_notice_due_date);                                        
                                                }
                                                else{
                                                    $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED, $payment_received_by_date, $payment_notice_due_date);                                        
                                                }
                                                // $payment_received_by_str = "\n\n" . sprintf(TEXT_PAYMENT_ESTIMATE_PAYMENT_RECEIVED_2, $payment_received_by_date, $payment_notice_due_date);
                                            }

                                            // Insert payment history
                                            $payment_history_sql_data_array = array('store_payments_id' => $payment_id,
                                                'store_payments_status' => $to_status,
                                                'date_added' => 'now()',
                                                'payee_notified' => '1',
                                                'comments' => $exRate_calculation_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '') . $payment_received_by_str,
                                                'changed_by' => $_SESSION['login_email_address'],
                                                'changed_by_role' => 'admin'
                                            );
                                            tep_db_perform(TABLE_STORE_PAYMENTS_HISTORY, $payment_history_sql_data_array);
                                            
                                            if($is_g2g == true){
                                                $withdraw_datetime = c2c_invoice::get_store_payment_datetime($payment_id); 
                                            }
                                        
                                            // Email to beneficiary
                                            $email_beneficiary = true;
                                            $email_comments_str = $exRate_calculation_str . (tep_not_null($payment_reference) ? "\n" . 'Payment Reference: ' . $payment_reference : '');
                                        }
                                    }
                                }

                                $res_code = '1';

                                break;
                            default:
                                $res_code = '1';
                                $res_message = JS_ERROR_PAYMENT_TRANS_UNKNOWN_STATUS;

                                break;
                        }
                    } else { // This payment has been modified
                        $res_code = '1';
                        $res_message = JS_ERROR_PAYMENT_TRANS_MODIFIED;
                    }

                    // Get the latest payment status and generate the cell html
                    $payment_updated_info_select_sql = "SELECT *, pm.payment_methods_send_mass_payment as mass_payment
														FROM " . TABLE_STORE_PAYMENTS . " as sp 
														INNER JOIN " . TABLE_PAYMENT_METHODS . " as pm 
															on sp.store_payments_methods_id = pm.payment_methods_id 
														WHERE store_payments_id = '" . tep_db_input($payment_id) . "'";
                    $payment_updated_info_result_sql = tep_db_query($payment_updated_info_select_sql);

                    if ($payment_updated_info_row = tep_db_fetch_array($payment_updated_info_result_sql)) {
                        $payment_input_fields_array = $xmlhttp_payments_object->_get_payment_methods_fields($payment_updated_info_row['store_payments_methods_id'], array('1')); // Just look for those input fields

                        $live_exchange_rate = false;
                        $payment_batch_available = false;

                        $payment_details_array = $xmlhttp_payments_object->get_payment_details($payment_id); // Just look for those input fields
                        $user_info_array = $xmlhttp_payments_object->_get_user_particulars($payment_updated_info_row['user_id'], $payment_updated_info_row['user_role']);

                        // check the current store_payment record payment method fields value has successful pass payment				
                        if (in_array((int) $payment_updated_info_row['store_payments_status'], array(1, 2))) {
                            $pm_validated = $xmlhttp_payments_object->_compare_pass_completed_withdraw_method($payment_updated_info_row['user_id'], $payment_updated_info_row['store_payments_methods_id'], $payment_input_fields_array, $payment_details_array);
                        } else {
                            $pm_validated = true;
                        }
                        $payment_validated_style = ($pm_validated === true) ? '' : ($pm_validated === 2 ? 'class="orangeIndicator"' : 'class="redIndicator"');

                        $acc_stat_link = sprintf(LINK_PAYMENT_ACCOUNT_STATEMENT, tep_href_link(FILENAME_ACCOUNT_STATEMENT, 'action=show_report&user_role=' . urlencode($payment_updated_info_row['user_role']) . '&user_email=' . urlencode($payment_updated_info_row['user_email_address']) . '&start_date=' . (date('Y-m-d', mktime(0, 0, 0, date("m"), date("d") - 7, date("Y")))), 'SSL'), '');
                        $edit_link = '<a href="' . tep_href_link($filename, 'payID=' . $payment_id . '&action=edit', 'NONSSL') . '">' . tep_image(DIR_WS_ICONS . "edit.gif", "Edit", "14", "13", 'align="top"') . '</a>';

                        if ($payment_updated_info_row['user_role'] == 'supplier') {
                            $user_name_link = '<a href="' . tep_href_link(FILENAME_SUPPLIERS_LIST, 'action=edit_supplier&sID=' . $payment_updated_info_row['user_id']) . '" target="_blank">' . $payment_updated_info_row['user_firstname'] . ' ' . $payment_updated_info_row['user_lastname'] . (tep_not_null($user_info_array['sign_up_from']) ? ' [' . $user_info_array['sign_up_from'] . ']' : '') . '</a>';
                        } else {
                            $user_name_link = '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $payment_updated_info_row['user_id'] . '&action=edit') . '" target="_blank">' . $payment_updated_info_row['user_firstname'] . ' ' . $payment_updated_info_row['user_lastname'] . (tep_not_null($user_info_array['sign_up_from']) ? ' [' . $user_info_array['sign_up_from'] . ']' : '') . '</a>';
                        }

                        //$request_amount = number_format(tep_round($payment_updated_info_row['store_payments_after_fees_amount'], $xmlhttp_payments_object->currency_display_decimal), $xmlhttp_payments_object->currency_display_decimal, '.', '');
                        $request_amount = $payment_updated_info_row['store_payments_after_fees_amount'];

                        if ($payment_updated_info_row['store_payments_status'] == '2') { // Only editable when it is Processing status
                            // style - red colour
                            if (tep_not_null($payment_updated_info_row['store_payments_paid_currency_value'])) {
                                $rate_input_style = '';
                                $stored_currency_rate = $payment_updated_info_row['store_payments_paid_currency_value'];
                            } else {
                                $live_exchange_rate = true;
                                $rate_input_style = 'class="redInputBox"';
                                $stored_currency_rate = $currencies->advance_currency_conversion_rate($payment_updated_info_row['store_payments_request_currency'], $payment_updated_info_row['store_payments_paid_currency']);
                            }
                            $exchange_rate_str = tep_draw_input_field('exchange_rate[' . $payment_id . ']', $stored_currency_rate, ' id="exchange_rate_' . $payment_id . '" size="12" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; } refresh_payout_price(this, \'' . $payment_id . '\', \'' . $request_amount . '\', \'' . $xmlhttp_payments_object->currency_display_decimal . '\'); " onBlur="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value)) ) { this.value = \'\'; } refresh_payout_price(this, \'' . $payment_id . '\', \'' . $request_amount . '\', \'' . $xmlhttp_payments_object->currency_display_decimal . '\'); " onKeyPress="return noEnterKey(event)" ' . $rate_input_style);

                            if ($payment_current_info_row['payment_methods_filename'] == 'paypal.php' && $payment_updated_info_row['mass_payment']) {
                                $payment_reference_str = '';
                            } else if ($payment_current_info_row['payment_methods_filename'] == 'paypalEC.php' && $payment_updated_info_row['mass_payment']) {
                                $payment_reference_str = '';
                            } else if ($payment_current_info_row['payment_methods_filename'] == 'pipwavePG.php' && $payment_updated_info_row['mass_payment']) {
                                $payment_reference_str = tep_draw_input_field('payment_reference[' . $payment_id . ']', $payment_updated_info_row['store_payments_reference'], ' id="payment_reference_' . $payment_id . '" size="16" onKeyPress="return noEnterKey(event)" ');
                            } else {
                                $payment_reference_str = tep_draw_input_field('payment_reference[' . $payment_id . ']', $payment_updated_info_row['store_payments_reference'], ' id="payment_reference_' . $payment_id . '" size="16" onKeyPress="return noEnterKey(event)" ');
                            }
                        } else {
                            if (tep_not_null($payment_updated_info_row['store_payments_paid_currency_value'])) {
                                $exchange_rate_str = $payment_updated_info_row['store_payments_paid_currency_value'];
                            } else {
                                $live_exchange_rate = true;
                                $exchange_rate_str = $currencies->advance_currency_conversion_rate($payment_updated_info_row['store_payments_request_currency'], $payment_updated_info_row['store_payments_paid_currency']);
                            }

                            $stored_currency_rate = $exchange_rate_str;
                            if ($xmlhttp_payments_object->_is_payment_methods_type_mode_system_define($payment_updated_info_row['store_payments_methods_id'])) {
                                if($is_g2g == true) {
                                    $payment_reference_str = '<a href="' . CREW2_PATH_G2G . '/store-credit/report?request_id=' . $payment_updated_info_row['store_payments_reference'] .'" target="_blank">' .sprintf(TITLE_TRANS_STORE_CREDIT, $payment_updated_info_row['store_payments_reference']).'</a>' ;                                                                                   
                                } else{
                                    $payment_reference_str = "<a href=\"" . CREW2_PATH . '/store-credit/index?transaction_id=' . $payment_updated_info_row['store_payments_reference'] .'&start_date=' . date('Y-m-d', strtotime($payment_updated_info_row['store_payments_date'])) . "\" target=\"_blank\">" . sprintf(TITLE_TRANS_STORE_CREDIT, $payment_updated_info_row['store_payments_reference']) . "</a>";
                                }
                            } else {
                                $payment_reference_str = $payment_updated_info_row['store_payments_reference'];
                            }
                        }

                        if ($xmlhttp_payments_object->_is_payment_methods_type_mode_system_define($payment_updated_info_row['store_payments_methods_id']) == '1') {
                            $show_currency_ex_colum = true;
                            $this_payment_need_need_ex_rate = true;
                            $is_system_defined_method = true;
                        } else {
                            $this_payment_need_need_ex_rate = $payment_updated_info_row['store_payments_request_currency'] != $payment_updated_info_row['store_payments_paid_currency'] ? true : false;
                            $is_system_defined_method = false;
                        }

                        $actual_payout_amount = $request_amount * $stored_currency_rate;
                        $rounded_actual_payout_amount = number_format(tep_round($actual_payout_amount, $xmlhttp_payments_object->currency_display_decimal), $xmlhttp_payments_object->currency_display_decimal, '.', '');

                        // FETCH WITHDRAW TAX IF EXIST
                        $tax_query = "SELECT * FROM ".TABLE_STORE_PAYMENTS_EXTRA_INFO." WHERE store_payments_id = ".$payment_id." AND store_payments_extra_info_key = 'tax_amount'  ";
                        $tax_query_result = tep_db_query($tax_query);
                        if ($tax_query_row = tep_db_fetch_array($tax_query_result)) {
                            $tax_amount = $tax_query_row['store_payment_extra_info_value'];
                        }else{
                            $tax_amount = 0;
                        }
                        //FORMAT TAX AMOUNT
                        $formatted_tax_amount = number_format(tep_round($tax_amount, $xmlhttp_payments_object->currency_display_decimal), $xmlhttp_payments_object->currency_display_decimal, '.', '');

                        $actual_payment_style = ($payment_updated_info_row['store_payments_request_currency'] != $payment_updated_info_row['store_payments_paid_currency'] && $live_exchange_rate) ? 'class="redIndicator"' : '';

                        $lock_button_html = '';
                        if ($payment_updated_info_row['store_payments_lock'] == 1 && $payment_updated_info_row['store_payments_status'] != 3) {
                            $lock_button_html = '<img border="0" class="img_lock" pid="' . $payment_id . '" title=" Locked " alt="Locked" src="images/icons/locked.gif">';
                        }

                        switch ($payment_updated_info_row['store_payments_status']) {
                            case '1': // Pending
                                if ($payment_updated_info_row['user_role'] == 'customers' && $xmlhttp_payments_object->_is_payment_methods_type_mode_system_define($payment_updated_info_row['store_payments_methods_id'])) {
                                    $action_button_html = tep_button('Issue NRSC', 'Process this payment', '', ' name="ProcessBtn_' . $payment_id . '" onClick="updatePayment(\'' . $filename . '\', this, \'' . $payment_id . '\', \'1\', \'3\', \'' . $payment_updated_info_row['store_payments_methods_id'] . '\');" ', 'inputButton') . '&nbsp;';
                                } else {
                                    $action_button_html = tep_button('Process', 'Process this payment', '', ' name="ProcessBtn_' . $payment_id . '" onClick="updatePayment(\'' . $filename . '\', this, \'' . $payment_id . '\', \'1\', \'2\', \'' . $payment_updated_info_row['store_payments_methods_id'] . '\');" ', 'inputButton') . '&nbsp;';
                                }

                                break;
                            case '2': // Processing
                                if (($payment_current_info_row['payment_methods_filename'] == 'paypal.php' && $payment_updated_info_row['mass_payment'])) {
                                    if ($payment_updated_info_row['store_payments_lock'] == 1) {
                                        $action_button_html = 'Processing&nbsp;';
                                    } else {
                                        $action_button_html = tep_button('Mass Payment API', 'Complete this payment', '', ' name="CompleteBtn_' . $payment_id . '" onClick=" if (confirm(\'' . JS_ALERT_PAYMENT_CONFIRM_UPDATE . '\') != \'0\') { updatePayment(\'' . $filename . '\', this, \'' . $payment_id . '\', \'2\', \'3\', \'' . $payment_updated_info_row['store_payments_methods_id'] . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    }

                                    if ($payment_manual_complete_payment_permission) {
                                        $action_button_html .= tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_' . $payment_id . '" onClick=" if (confirm(\'' . JS_ALERT_PAYMENT_CONFIRM_UPDATE . '\') != \'0\') { updatePayment(\'' . $filename . '\', this, \'' . $payment_id . '\', \'2\', \'3\', \'' . $payment_updated_info_row['store_payments_methods_id'] . '\', 1); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    }
                                    $payment_reference_str = $payment_updated_info_row['store_payments_reference'];

                                    if ($payment_manual_unlock_payment_permission) {
                                        $payment_batch_available = true;
                                    }
                                } else if (($payment_current_info_row['payment_methods_filename'] == 'paypalEC.php' && $payment_updated_info_row['mass_payment'])) {
                                    if ($payment_updated_info_row['store_payments_lock'] == 1) {
                                        $action_button_html = 'Processing&nbsp;';
                                    } else {
                                        $action_button_html = tep_button('PaypalEC Mass Payment API', 'Complete this payment', '', ' name="CompleteBtn_' . $payment_id . '" onClick=" if (confirm(\'' . JS_ALERT_PAYMENT_CONFIRM_UPDATE . '\') != \'0\') { updatePayment(\'' . $filename . '\', this, \'' . $payment_id . '\', \'2\', \'3\', \'' . $payment_updated_info_row['store_payments_methods_id'] . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    }

                                    if ($payment_manual_complete_payment_permission) {
                                        $action_button_html .= tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_' . $payment_id . '" onClick=" if (confirm(\'' . JS_ALERT_PAYMENT_CONFIRM_UPDATE . '\') != \'0\') { updatePayment(\'' . $filename . '\', this, \'' . $payment_id . '\', \'2\', \'3\', \'' . $payment_updated_info_row['store_payments_methods_id'] . '\', 1); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    }
                                    $payment_reference_str = $payment_updated_info_row['store_payments_reference'];

                                    if ($payment_manual_unlock_payment_permission) {
                                        $payment_batch_available = true;
                                    }
                                } else if (($payment_current_info_row['payment_methods_filename'] == 'pipwavePG.php' && $payment_updated_info_row['mass_payment'])) {
                                    if ($payment_updated_info_row['store_payments_lock'] == 1) {
                                        $action_button_html = 'Processing&nbsp;';
                                    } else {
                                        $action_button_html = tep_button('Payment API complete', 'Complete this payment', '', ' name="CompleteBtn_' . $payment_id . '" onClick=" if (confirm(\'' . JS_ALERT_PAYMENT_CONFIRM_UPDATE . '\') != \'0\') { updatePayment(\'' . $filename . '\', this, \'' . $payment_id . '\', \'2\', \'3\', \'' . $payment_updated_info_row['store_payments_methods_id'] . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    }

                                    if ($payment_manual_complete_payment_permission) {
                                        $action_button_html .= tep_button('Manual Complete', 'Complete this payment', '', ' name="CompleteBtn_' . $payment_id . '" onClick=" if (confirm(\'' . JS_ALERT_PAYMENT_CONFIRM_UPDATE . '\') != \'0\') { updatePayment(\'' . $filename . '\', this, \'' . $payment_id . '\', \'2\', \'3\', \'' . $payment_updated_info_row['store_payments_methods_id'] . '\', 1); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    }
                                    // $payment_reference_str = $payment_updated_info_row['store_payments_reference'];

                                    if ($payment_manual_unlock_payment_permission) {
                                        $payment_batch_available = true;
                                    }
                                } else {
                                    $action_button_html = tep_button('Complete', 'Complete this payment', '', ' name="CompleteBtn_' . $payment_id . '" onClick=" if (confirm(\'' . JS_ALERT_PAYMENT_CONFIRM_UPDATE . '\') != \'0\') { updatePayment(\'' . $filename . '\', this, \'' . $payment_id . '\', \'2\', \'3\', \'' . $payment_updated_info_row['store_payments_methods_id'] . '\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    $payment_batch_available = true;
                                }
                                break;
                            case '3': // Completed
                                $action_button_html = 'Completed';

                                break;
                            case '4': // Canceled
                                $action_button_html = 'Canceled';

                                break;
                            default:

                                break;
                        }

                        $supplier_aging_str = '';
                        $supplier_cb_ratio_str = '';
                        if ($payment_updated_info_row['user_role'] == 'customers') {
                            $supplier_aging_str = '<div id="supp_aging_' . $payment_id . '">' . sprintf(LINK_PAYMENT_AGING_STATISTIC, "getSuppAgingStats('" . $payment_id . "');") . '</div>';
                            $supplier_cb_ratio_str = '<div id="cb_order_' . $payment_id . '"></div>';
                        }
                        $show_fraud_link = '<a href="'.tep_href_link(FILENAME_SHOW_IMAGE, 'action=show_fraud&customer_id='.$payment_updated_info_row['user_id']) .'" target="_blank">view</a>';
                        echo "<table_cell>";

                        echo "<cell property='nowrap=1'><![CDATA[" . $payment_id . "]]></cell>";
                        echo "<cell property='nowrap=1'><![CDATA[" . $user_name_link . "]]></cell>";
                        echo "<cell property='nowrap=1'><![CDATA[" . $user_info_array['email'] . "]]></cell>";
                        echo "<cell property='nowrap=1'><![CDATA[" . $show_fraud_link . "]]></cell>";
                        echo "<cell property='align=center&amp;nowrap=1'><![CDATA[" . $supplier_aging_str . "]]></cell>";
                        echo "<cell property='align=center&amp;nowrap=1'><![CDATA[" . $supplier_cb_ratio_str . "]]></cell>";
                        echo "<cell property='align=center&amp;nowrap=1'><![CDATA[" . $acc_stat_link . "]]></cell>";

                        if (is_array($payment_input_fields_array) && count($payment_input_fields_array)) {
                            foreach ($payment_input_fields_array as $field_id => $field_info) {
                                echo "<cell property='nowrap=1'><![CDATA[<span " . $payment_validated_style . ' id="span_pm_' . $payment_id . '">' . nl2br($payment_details_array[$field_id]['payment_methods_fields_value']) . "</span>]]></cell>";
                            }
                        }

                        if ($show_currency_ex_colum) {
                            echo "<cell property='width=5&amp;align=center&amp;nowrap=1'><![CDATA[" . $payment_updated_info_row['store_payments_request_currency'] . "]]></cell>";
                            echo "<cell property='align=right'><![CDATA[" . $request_amount . "]]></cell>";
                            echo "<cell property='align=right&amp;nowrap=1'><![CDATA[" . ($this_payment_need_need_ex_rate ? $exchange_rate_str : TEXT_PAYMENT_NOT_APPLICABLE) . "]]></cell>";
                        }

                        if ($is_system_defined_method) {
                            echo "<cell property='align=right'><![CDATA[" . $payment_updated_info_row['store_payments_paid_currency'] . "]]></cell>";
                        } else if ($payment_current_info_row['payment_methods_filename'] == 'paypal.php') {
                            echo "<cell property='align=right'><![CDATA[" . number_format(tep_round($payment_current_info_row['store_payments_request_amount'], $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places']), $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places'], '.', '') . "]]></cell>";
                            echo "<cell property='align=right'><![CDATA[" . number_format(tep_round($payment_current_info_row['store_payments_fees'], $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places']), $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places'], '.', '') . "]]></cell>";
                            echo "<cell property='align=right'><![CDATA[" . $formatted_tax_amount . "]]></cell>";
                        } else if ($payment_current_info_row['payment_methods_filename'] == 'paypalEC.php') {
                            echo "<cell property='align=right'><![CDATA[" . number_format(tep_round($payment_current_info_row['store_payments_request_amount'], $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places']), $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places'], '.', '') . "]]></cell>";
                            echo "<cell property='align=right'><![CDATA[" . number_format(tep_round($payment_current_info_row['store_payments_fees'], $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places']), $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places'], '.', '') . "]]></cell>";
                            echo "<cell property='align=right'><![CDATA[" . $formatted_tax_amount . "]]></cell>";
                        } else if ($payment_current_info_row['payment_methods_filename'] == 'pipwavePG.php') {
                            echo "<cell property='align=right'><![CDATA[" . number_format(tep_round($payment_current_info_row['store_payments_request_amount'], $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places']), $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places'], '.', '') . "]]></cell>";
                            echo "<cell property='align=right'><![CDATA[" . number_format(tep_round($payment_current_info_row['store_payments_fees'], $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places']), $currencies->currencies[$payment_current_info_row['store_payments_request_currency']]['decimal_places'], '.', '') . "]]></cell>";
                            echo "<cell property='align=right'><![CDATA[" . $formatted_tax_amount . "]]></cell>";
                        }
                        echo "<cell property='align=right'><![CDATA[<span " . $actual_payment_style . ' id="span_payout_' . $payment_id . '">' . $rounded_actual_payout_amount . "</span>]]></cell>";

                        //echo "<cell property='align=right&amp;nowrap=1'><![CDATA[".($this_payment_need_need_ex_rate ? $exchange_rate_str : TEXT_PAYMENT_NOT_APPLICABLE)."]]></cell>";
                        if($is_g2g == true){
                            echo "<cell property='nowrap=1'><![CDATA[" . sprintf(TEXT_G2G_PAYMENT_REMARKS, $payment_id) . "]]></cell>";
                        }
                        else{
                            echo "<cell property='nowrap=1'><![CDATA[" . sprintf(TEXT_PAYMENT_REMARKS, $payment_id) . "]]></cell>";
                        }	
                        echo "<cell property='align=center&amp;nowrap=1'><![CDATA[" . $payment_reference_str . "]]></cell>";
                        echo "<cell property='align=center'><![CDATA[" . $edit_link . "]]></cell>";
                        echo "<cell property='align=center&amp;nowrap=1'><![CDATA[" . $action_button_html . "]]></cell>";
                        echo "<cell><![CDATA[" . tep_draw_checkbox_field('payments_batch[]', $payment_id, false, '', 'id="' . $payment_id . '" onClick="update_selected_price(this.form, \'' . $payment_updated_info_row['store_payments_methods_id'] . '\', \'payments_batch\');" ' . (!$payment_batch_available ? '  ' : '')) . "]]></cell>";
                        echo "<cell><![CDATA[" . $lock_button_html . "]]></cell>";
                        echo "</table_cell>";
                    }
                } else {
                    $res_code = '-1';
                    $res_message = JS_ERROR_PAYMENT_TRANS_NOT_EXISTS;
                }
            }

            echo "<res_code><![CDATA[" . $res_code . "]]></res_code>";
            echo "<res_message><![CDATA[" . $res_message . "]]></res_message>";

            if ($email_beneficiary)
            {
                //if move completed status only
                //Insert to temp db and fire API to g2g lan-api to create g2g-invoice queue

                if(isset($withdraw_datetime) && ($withdraw_datetime != false)){
                  c2c_invoice::create_c2c_invoice_queue($payment_id, 'withdrawal', $withdraw_datetime);
                }
                $xmlhttp_payments_object->send_payment_status_email($payment_id, $email_comments_str);
            }

            break;
        case "reserve_sc_trans":
            if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/store_credit.php')) {
                include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/store_credit.php');
            }

            /*             * *************************************************
              1. Check if admin user has permission
              2. This transaction belong to this user
              3. The mode is in correct mode
              4. Add comment to the transaction
              5. Update reserve pool accordingly
             * ************************************************* */

            $res_code = '';
            $res_message = '';

            $history_id = isset($HTTP_GET_VARS['h_id']) ? $HTTP_GET_VARS['h_id'] : '';
            $user_id = isset($HTTP_GET_VARS['u_id']) ? $HTTP_GET_VARS['u_id'] : '';
            $reserve_mode = isset($HTTP_GET_VARS['r_mode']) ? $HTTP_GET_VARS['r_mode'] : '';

            if (!tep_admin_check_boxes(FILENAME_STORE_CREDIT, 'sub_boxes')) {
                $res_code = '-1';
                $res_message = JS_ERROR_SC_STAT_NO_PERMISSION;
            } else {
                $statement_select_sql = "	SELECT store_credit_history_credit_amount, store_credit_transaction_reserved, store_credit_history_trans_type, store_credit_account_type 
											FROM " . TABLE_STORE_CREDIT_HISTORY . "
											WHERE store_credit_history_id = '" . tep_db_input($history_id) . "' 
												AND customer_id = '" . tep_db_input($user_id) . "'";
                $statement_result_sql = tep_db_query($statement_select_sql);

                if ($statement_row = tep_db_fetch_array($statement_result_sql)) {
                    $store_credit_select_sql = "	SELECT customer_id 
													FROM " . TABLE_COUPON_GV_CUSTOMER . "
					 								WHERE customer_id = '" . tep_db_input($user_id) . "' ";
                    $store_credit_result_sql = tep_db_query($store_credit_select_sql);

                    if ($store_credit_row = tep_db_fetch_array($store_credit_result_sql)) {
                        if (tep_not_null($statement_row['store_credit_history_trans_type'])) {
                            if ($reserve_mode != $statement_row['store_credit_transaction_reserved']) {
                                switch ($reserve_mode) {
                                    case "1": // Add to reserve
                                        $reserved_balance_update_sql = '';

                                        if ($statement_row['store_credit_account_type'] == 'NR') {
                                            $reserved_balance_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . "
											 									SET sc_irreversible_reserve_amount = sc_irreversible_reserve_amount + " . (double) $statement_row['store_credit_history_credit_amount'] . ",
											 										sc_last_modified = now() 
											 									WHERE customer_id = '" . tep_db_input($user_id) . "' ";
                                        } else if ($statement_row['store_credit_account_type'] == 'R') {
                                            $reserved_balance_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . "
											 									SET sc_reversible_reserve_amount = sc_reversible_reserve_amount + " . (double) $statement_row['store_credit_history_credit_amount'] . ",
											 										sc_last_modified = now() 
											 									WHERE customer_id = '" . tep_db_input($user_id) . "' ";
                                        }

                                        if (tep_not_null($reserved_balance_update_sql)) {
                                            tep_db_query($reserved_balance_update_sql);

                                            // Update the reserve status
                                            $reserve_status_update_sql = "	UPDATE " . TABLE_STORE_CREDIT_HISTORY . " 
																			SET store_credit_transaction_reserved = '1' 
																			WHERE store_credit_history_id = '" . tep_db_input($history_id) . "' ";
                                            tep_db_query($reserve_status_update_sql);

                                            $res_code = '1';
                                        }

                                        break;
                                    case "0": // Remove from reserve
                                        $reserved_balance_update_sql = '';

                                        if ($statement_row['store_credit_account_type'] == 'NR') {
                                            $reserved_balance_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . "
											 									SET sc_irreversible_reserve_amount = sc_irreversible_reserve_amount - " . (double) $statement_row['store_credit_history_credit_amount'] . ",
											 										sc_last_modified = now() 
											 									WHERE customer_id = '" . tep_db_input($user_id) . "' ";
                                        } else if ($statement_row['store_credit_account_type'] == 'R') {
                                            $reserved_balance_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . "
											 									SET sc_reversible_reserve_amount = sc_reversible_reserve_amount - " . (double) $statement_row['store_credit_history_credit_amount'] . ",
											 										sc_last_modified = now() 
											 									WHERE customer_id = '" . tep_db_input($user_id) . "' ";
                                        }

                                        if (tep_not_null($reserved_balance_update_sql)) {
                                            tep_db_query($reserved_balance_update_sql);

                                            // Update the reserve status
                                            $reserve_status_update_sql = "	UPDATE " . TABLE_STORE_CREDIT_HISTORY . " 
																			SET store_credit_transaction_reserved = '0' 
																			WHERE store_credit_history_id = '" . tep_db_input($history_id) . "' ";
                                            tep_db_query($reserve_status_update_sql);

                                            $res_code = '1';
                                        }

                                        break;
                                    default: // Invalid mode
                                        $res_code = '-1';
                                        $res_message = JS_ERROR_SC_STAT_TRANS_INVALID_MODE;

                                        break;
                                }
                            } else {
                                $res_code = '-1';
                                $res_message = JS_ERROR_SC_STAT_TRANS_MODE_CHANGED;
                            }
                        } else {
                            $res_code = '-1';
                            $res_message = JS_ERROR_SC_STAT_TRANS_NOT_EXISTS;
                        }
                    } else {
                        $res_code = '-1';
                        $res_message = JS_ERROR_SC_STAT_SC_TYPE_NOT_EXISTS;
                    }
                } else {
                    $res_code = '-1';
                    $res_message = JS_ERROR_SC_STAT_TRANS_NOT_EXISTS;
                }
            }

            echo "<res_code>" . $res_code . "</res_code>";
            echo "<res_message><![CDATA[" . $res_message . "]]></res_message>";

            break;
        case "reserve_wc_trans":
            if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/store_credit.php')) {
                include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/store_credit.php');
            }

            /*             * *************************************************
              1. Check if admin user has permission
              2. This transaction belong to this user
              3. The mode is in correct mode
              4. Add comment to the transaction
              5. Update reserve pool accordingly
             * ************************************************* */

            $res_code = '';
            $res_message = '';

            $history_id = isset($HTTP_GET_VARS['h_id']) ? $HTTP_GET_VARS['h_id'] : '';
            $user_id = isset($HTTP_GET_VARS['u_id']) ? $HTTP_GET_VARS['u_id'] : '';
            $reserve_mode = isset($HTTP_GET_VARS['r_mode']) ? $HTTP_GET_VARS['r_mode'] : '';

            if (!tep_admin_check_boxes(FILENAME_STORE_CREDIT, 'sub_boxes')) {
                $res_code = '-1';
                $res_message = JS_ERROR_SC_STAT_NO_PERMISSION;
            } else {
                $statement_select_sql = "	SELECT store_account_history_currency, store_account_history_credit_amount, store_account_transaction_reserved, store_account_history_trans_type, 'W' as store_account_history_account_type 
											FROM " . TABLE_STORE_ACCOUNT_HISTORY . "
											WHERE store_account_history_id = '" . tep_db_input($history_id) . "' 
												AND user_id = '" . tep_db_input($user_id) . "'";
                $statement_result_sql = tep_db_query($statement_select_sql);

                if ($statement_row = tep_db_fetch_array($statement_result_sql)) {
                    $store_credit_select_sql = "	SELECT user_id 
													FROM " . TABLE_STORE_ACCOUNT_BALANCE . "
					 								WHERE user_id = '" . tep_db_input($user_id) . "' ";
                    $store_credit_result_sql = tep_db_query($store_credit_select_sql);

                    if ($store_credit_row = tep_db_fetch_array($store_credit_result_sql)) {
                        if (tep_not_null($statement_row['store_account_history_trans_type'])) {
                            if ($reserve_mode != $statement_row['store_account_transaction_reserved']) {
                                switch ($reserve_mode) {
                                    case "1": // Add to reserve
                                        $reserved_balance_update_sql = '';

                                        //if ($statement_row['store_account_history_account_type'] == 'W') {
                                        $reserved_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . "
											 									SET store_account_reserve_amount = store_account_reserve_amount + " . (double) $statement_row['store_account_history_credit_amount'] . ",
											 										store_account_last_modified = now() 
											 									WHERE user_id = '" . tep_db_input($user_id) . "' 
											 										AND store_account_balance_currency = '" . tep_db_input($statement_row['store_account_history_currency']) . "'";
                                        //}

                                        if (tep_not_null($reserved_balance_update_sql)) {
                                            tep_db_query($reserved_balance_update_sql);

                                            // Update the reserve status
                                            $reserve_status_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_HISTORY . " 
																			SET store_account_transaction_reserved = '1' 
																			WHERE store_account_history_id = '" . tep_db_input($history_id) . "' ";
                                            tep_db_query($reserve_status_update_sql);

                                            $res_code = '1';
                                        }

                                        break;
                                    case "0": // Remove from reserve
                                        $reserved_balance_update_sql = '';

                                        //if ($statement_row['store_account_history_account_type'] == 'W') {
                                        $reserved_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . "
											 									SET store_account_reserve_amount = store_account_reserve_amount - " . (double) $statement_row['store_account_history_credit_amount'] . ",
											 										store_account_last_modified = now() 
											 									WHERE user_id = '" . tep_db_input($user_id) . "' 
											 										AND store_account_balance_currency = '" . tep_db_input($statement_row['store_account_history_currency']) . "'";
                                        //}

                                        if (tep_not_null($reserved_balance_update_sql)) {
                                            tep_db_query($reserved_balance_update_sql);

                                            // Update the reserve status
                                            $reserve_status_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_HISTORY . " 
																			SET store_account_transaction_reserved = '0' 
																			WHERE store_account_history_id = '" . tep_db_input($history_id) . "' ";
                                            tep_db_query($reserve_status_update_sql);

                                            $res_code = '1';
                                        }

                                        break;
                                    default: // Invalid mode
                                        $res_code = '-1';
                                        $res_message = JS_ERROR_SC_STAT_TRANS_INVALID_MODE;

                                        break;
                                }
                            } else {
                                $res_code = '-1';
                                $res_message = JS_ERROR_SC_STAT_TRANS_MODE_CHANGED;
                            }
                        } else {
                            $res_code = '-1';
                            $res_message = JS_ERROR_SC_STAT_TRANS_NOT_EXISTS;
                        }
                    } else {
                        $res_code = '-1';
                        $res_message = JS_ERROR_SC_STAT_SC_TYPE_NOT_EXISTS;
                    }
                } else {
                    $res_code = '-1';
                    $res_message = JS_ERROR_SC_STAT_TRANS_NOT_EXISTS;
                }
            }

            echo "<res_code>" . $res_code . "</res_code>";
            echo "<res_message><![CDATA[" . $res_message . "]]></res_message>";

            break;
        case "update_refund":
            if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/refund.php')) {
                include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/refund.php');
            }

            require_once(DIR_WS_CLASSES . 'currencies.php');
            $currencies = new currencies();

            require_once(DIR_WS_CLASSES . 'payments.php');
            $xmlhttp_payments_object = new payments($admin_id, $_SESSION['login_email_address']);

            /*             * *************************************************
              1. Check if admin user has permission
              2. This refund transaction still in its original status]
              3. Ensure refund amount <= total order payment amount
              4. Add comment to the refund transation
              5. Email the payee

              $res_code
              - '-1' : Do not refresh the row contents
              - '1' : Refresh the row contents but doesn't mean positive action
             * ************************************************* */
            $email_beneficiary = false;
            $email_comments_str = '';

            $res_code = '';
            $res_message = '';

            $refund_id = isset($HTTP_GET_VARS['r_id']) ? $HTTP_GET_VARS['r_id'] : '';
            $from_status = isset($HTTP_GET_VARS['fr_status']) ? $HTTP_GET_VARS['fr_status'] : '';
            $to_status = isset($HTTP_GET_VARS['to_status']) ? $HTTP_GET_VARS['to_status'] : '';
            $filename = isset($HTTP_GET_VARS['filename']) ? $HTTP_GET_VARS['filename'] : '';
            $payment_reference = isset($HTTP_GET_VARS['pay_ref']) ? tep_db_prepare_input($HTTP_GET_VARS['pay_ref']) : '';
            $row_cnt = isset($HTTP_GET_VARS['row_cnt']) ? $HTTP_GET_VARS['row_cnt'] : '';
            $auto_refund = isset($HTTP_GET_VARS['auto_refund']) ? $HTTP_GET_VARS['auto_refund'] : '';

            if (!tep_admin_check_boxes(FILENAME_REFUND, 'sub_boxes')) {
                $res_code = '-1';
                $res_message = JS_ERROR_REFUND_NO_PERMISSION;
            } else {

                $refund_current_info_select_sql = "	SELECT sr.store_refund_status, sr.store_refund_trans_id, sr.store_refund_trans_total_amount, 
                                                        o.payment_methods_id, 
														sr.store_refund_amount, o.currency, o.currency_value, o.payment_methods_parent_id 
													FROM " . TABLE_STORE_REFUND . " AS sr
													INNER JOIN " . TABLE_ORDERS . " AS o 
														ON o.orders_id = sr.store_refund_trans_id 
													WHERE store_refund_id = '" . tep_db_input($refund_id) . "'";
                $refund_current_info_result_sql = tep_db_query($refund_current_info_select_sql);

                if ($refund_current_info_row = tep_db_fetch_array($refund_current_info_result_sql)) {
                    $ot_sql = "SELECT text, value, class FROM " . TABLE_ORDERS_TOTAL . " WHERE orders_id = " . $refund_current_info_row['store_refund_trans_id'];
                    $ot_res = tep_db_query($ot_sql);
                    while ($ot_row = tep_db_fetch_array($ot_res)) {
                        $ot[$ot_row['class']] = array(
                                'text' => preg_replace("/[^0-9.]/", "", str_replace(',', '', preg_replace('~&#([0-9]+);~e', '', strip_tags($ot_row['text'])))),
                                'value' => $ot_row['value']
                                );
                    }

                    if ($refund_current_info_row['store_refund_status'] == $from_status) {
                        if ($refund_current_info_row['store_refund_amount'] >= $ot['ot_total']['value']) {
                            $formatted_refund_amount = $ot['ot_total']['text'];
                        } else {
                            $formatted_refund_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_amount'], $refund_current_info_row['currency'], $refund_current_info_row['currency_value']);
                        }

                        if ($refund_current_info_row['store_refund_trans_total_amount'] >= $ot['ot_total']['value']) {
                            $formatted_total_order_amount = $ot['ot_total']['text'];
                        } else {
                            $formatted_total_order_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_trans_total_amount'], $refund_current_info_row['currency'], $refund_current_info_row['currency_value']);
                        }

                        if ($formatted_refund_amount <= $formatted_total_order_amount) {
                            switch ($refund_current_info_row['store_refund_status']) {
                                case "1":
                                    if ($to_status == '2') { // From Pending -> Processing
                                        $refund_update_sql_data_array = array('store_refund_status' => $to_status,
                                            'store_refund_last_modified' => 'now()');
                                        tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");

                                        // Insert refund history
                                        $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                            'store_refund_status' => $to_status,
                                            'date_added' => 'now()',
                                            'payee_notified' => '1',
                                            'changed_by' => $_SESSION['login_email_address'],
                                            'changed_by_role' => 'admin'
                                        );
                                        tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);

                                        // Email to beneficiary
                                        $email_beneficiary = true;
                                    }

                                    $res_code = '1';

                                    break;
                                case "2":
                                    $processing_error = false;

                                    if ($to_status == '3') { // From Processing -> Completed
                                        $payment_methods_filename_select_sql = "	SELECT payment_methods_filename 
																					FROM " . TABLE_PAYMENT_METHODS . "
																					WHERE payment_methods_id = '" . $refund_current_info_row['payment_methods_parent_id'] . "'";
                                        $payment_methods_filename_result_sql = tep_db_query($payment_methods_filename_select_sql);
                                        $payment_methods_filename_row = tep_db_fetch_array($payment_methods_filename_result_sql);

                                        if ($payment_methods_filename_row['payment_methods_filename'] == 'paypal.php') {
                                            if ($auto_refund == '1') {
                                                require(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPAL);

                                                $transaction_id = $xmlhttp_payments_object->_get_payment_gateway_trans_id($refund_current_info_row['store_refund_trans_id'], $refund_current_info_row['payment_methods_parent_id']);

                                                $refund_type = 'Full';
                                                $memo = '';

                                                $nvpStr = '&TRANSACTIONID=' . $transaction_id . '&NOTE=' . $memo;

                                                $paypal = new paypal($refund_current_info_row['payment_methods_id']);

                                                $currency_select_sql = "SELECT currency, currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($refund_current_info_row['store_refund_trans_id']) . "'";
                                                $currency_result_sql = tep_db_query($currency_select_sql);
                                                $currency_row = tep_db_fetch_array($currency_result_sql);

                                                if ($refund_current_info_row['store_refund_amount'] >= $ot['ot_total']['value']) {
                                                    $formatted_refund_amount = $ot['ot_total']['text'];
                                                } else {
                                                    $formatted_refund_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                }

                                                if ($refund_current_info_row['store_refund_trans_total_amount'] >= $ot['ot_total']['value']) {
                                                    $formatted_total_order_amount = $ot['ot_total']['text'];
                                                } else {
                                                    $formatted_total_order_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_trans_total_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                }
                                                
                                                if ($formatted_refund_amount < $formatted_total_order_amount) {
                                                    $refund_type = 'Partial';
                                                    $nvpStr .= '&AMT=' . $formatted_refund_amount . '&CURRENCYCODE=' . $currency_row['currency'];
                                                }

                                                $nvpStr .= '&REFUNDTYPE=' . $refund_type;

                                                $res_array = $paypal->hash_call("RefundTransaction", $nvpStr, $currency_row['currency']);

                                                $ack = strtoupper($res_array['ACK']);

                                                if ($ack != 'SUCCESS') {
                                                    $processing_error = true;
                                                    $res_message = sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, $res_array['L_ERRORCODE0'], $res_array['L_LONGMESSAGE0']);
                                                }
                                            }

                                            if (!$processing_error) {
                                                // Wei Chen: On top already had checking on PG Filename, no need another check over here. Can remove after 2014-04-30
                                                //if (strtolower($refund_current_info_row['store_refund_payments_methods_name']) == 'paypal') {
                                                if ($auto_refund == '1') {
                                                    $payment_reference = $res_array['REFUNDTRANSACTIONID'];
                                                }
                                                //}
                                                // Insert exchange rate, update paid amount
                                                $refund_update_sql_data_array = array('store_refund_status' => $to_status,
                                                    'store_refund_payments_reference' => $payment_reference,
                                                    'store_refund_last_modified' => 'now()');

                                                tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");

                                                $comments = '';

                                                //if ($refund_current_info_row['store_refund_payments_methods_name'] == 'PayPal' && $auto_refund == '1') {
                                                if ($auto_refund == '1') {
                                                    $comments = "PayPal Refund Transaction ID: " . $payment_reference . "\n" .
                                                            "Gross Refund Amount: " . $res_array['GROSSREFUNDAMT'] . "\n" .
                                                            "Fee Refund Amount: " . $res_array['FEEREFUNDAMT'] . "\n" .
                                                            "Net Refund Amount: " . $res_array['NETREFUNDAMT'];

                                                    $orders_status_history_sql_data_array = array('orders_id' => $refund_current_info_row['store_refund_trans_id'],
                                                        'orders_status_id' => 0,
                                                        'date_added' => 'now()',
                                                        'customer_notified' => 0,
                                                        'comments' => $comments,
                                                        'comments_type' => 1,
                                                        'set_as_order_remarks' => 0,
                                                        'changed_by' => $_SESSION['login_email_address']
                                                    );
                                                    tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_sql_data_array);
                                                }

                                                // Insert refund payment history
                                                $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                    'store_refund_status' => $to_status,
                                                    'date_added' => 'now()',
                                                    'payee_notified' => '1',
                                                    'comments' => $comments,
                                                    'changed_by' => $_SESSION['login_email_address'],
                                                    'changed_by_role' => 'admin'
                                                );
                                                tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                            }
                                        } else if ($payment_methods_filename_row['payment_methods_filename'] == 'paypalEC.php') {
                                            if ($auto_refund == '1') {
                                                require_once(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_PAYPALEC);

                                                $transaction_id = $xmlhttp_payments_object->_get_payment_gateway_trans_id($refund_current_info_row['store_refund_trans_id'], 'paypalEC.php');

                                                $paypalEC = new paypalEC($refund_current_info_row['payment_methods_id']);
                                                $memo = '';

                                                $currency_select_sql = "SELECT currency, currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($refund_current_info_row['store_refund_trans_id']) . "'";
                                                $currency_result_sql = tep_db_query($currency_select_sql);
                                                $currency_row = tep_db_fetch_array($currency_result_sql);
                                                $paypalEC->get_merchant_account($currency_row['currency']);

                                                if ($refund_current_info_row['store_refund_amount'] >= $ot['ot_total']['value']) {
                                                    $formatted_refund_amount = $ot['ot_total']['text'];
                                                } else {
                                                    $formatted_refund_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                }

                                                if ($refund_current_info_row['store_refund_trans_total_amount'] >= $ot['ot_total']['value']) {
                                                    $formatted_total_order_amount = $ot['ot_total']['text'];
                                                } else {
                                                    $formatted_total_order_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_trans_total_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                }

                                                //====================pipwave / crew refund API ====================
                                                $pipwave = new pipwave($refund_current_info_row['store_refund_trans_id']);
                                                if (isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
                                                    $result = $pipwave->refundAPI($formatted_refund_amount);
                                                    if (isset($result['pg_raw_data']) && !empty($result['pg_raw_data'])) {
                                                        $rawData = json_decode($result['pg_raw_data'], true);

                                                        // no need to create functions for multiple format for now, improve when we have sample with full data
                                                        // just compute for REFUNDTRANSACTIONID for now
                                                        $refund_transaction_id = null;
                                                        if(isset($rawData['result']['id'])){
                                                            $refund_transaction_id = $rawData['result']['id'];
                                                        }else if(isset($rawData['RefundTransactionID'])){
                                                            $refund_transaction_id = $rawData['RefundTransactionID'];
                                                        }else if(isset($rawData['RefundInfo']['encryptedRefundTransactionId'])){
                                                            $refund_transaction_id = $rawData['RefundInfo']['encryptedRefundTransactionId'];
                                                        }

                                                        $res_array = array(
                                                            'REFUNDTRANSACTIONID' => $refund_transaction_id,
                                                            'GROSSREFUNDAMT' => (isset($rawData['GrossRefundAmount']['value']) ? $rawData['GrossRefundAmount']['value'] : $rawData['RefundInfo']['refundGrossAmount']),
                                                            'FEEREFUNDAMT' => (isset($rawData['FeeRefundAmount']['value']) ? $rawData['FeeRefundAmount']['value'] : $rawData['RefundInfo']['refundFeeAmount']),
                                                            'NETREFUNDAMT' => (isset($rawData['NetRefundAmount']['value']) ? $rawData['NetRefundAmount']['value'] : $rawData['RefundInfo']['refundNetAmount']),
                                                        );
                                                    }
                                                    if (isset($result['status']) && $result['status'] == 200) {

                                                    } else {
                                                        $processing_error = true;
                                                        if (isset($result['message'])) {
                                                            $res_message = sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, $result['status'], $result['message']);
                                                        }
                                                    }
                                                } else {
                                                    if ($formatted_refund_amount < $formatted_total_order_amount) {
                                                        $res_array = $paypalEC->refundAPI('Partial', $transaction_id, $memo, $formatted_refund_amount, $currency_row['currency']);
                                                    } else {
                                                        $res_array = $paypalEC->refundAPI('Full', $transaction_id, $memo);
                                                    }

                                                    $ack = strtoupper($res_array['ACK']);

                                                    if ($ack != 'SUCCESS') {
                                                        $processing_error = true;
                                                        $res_message = sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, $res_array['L_ERRORCODE0'], $res_array['L_LONGMESSAGE0']);
                                                    }
                                                }
                                                //====================pipwave / crew refund API ====================
                                            }

                                            if (!$processing_error) {
                                                if ($auto_refund == '1') {
                                                    $payment_reference = $res_array['REFUNDTRANSACTIONID'];
                                                }

                                                // Insert exchange rate, update paid amount
                                                $refund_update_sql_data_array = array('store_refund_status' => $to_status,
                                                    'store_refund_payments_reference' => $payment_reference,
                                                    'store_refund_last_modified' => 'now()');

                                                tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");

                                                $comments = '';

                                                if ($auto_refund == '1') {
                                                    $comments = "PayPal Refund Transaction ID: " . $payment_reference . "\n" .
                                                            "Gross Refund Amount: " . $res_array['GROSSREFUNDAMT'] . "\n" .
                                                            "Fee Refund Amount: " . $res_array['FEEREFUNDAMT'] . "\n" .
                                                            "Net Refund Amount: " . $res_array['NETREFUNDAMT'];

                                                    $orders_status_history_sql_data_array = array('orders_id' => $refund_current_info_row['store_refund_trans_id'],
                                                        'orders_status_id' => 0,
                                                        'date_added' => 'now()',
                                                        'customer_notified' => 0,
                                                        'comments' => $comments,
                                                        'comments_type' => 1,
                                                        'set_as_order_remarks' => 0,
                                                        'changed_by' => $_SESSION['login_email_address']
                                                    );
                                                    tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_sql_data_array);
                                                }

                                                // Insert refund payment history
                                                $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                    'store_refund_status' => $to_status,
                                                    'date_added' => 'now()',
                                                    'payee_notified' => '1',
                                                    'comments' => $comments,
                                                    'changed_by' => $_SESSION['login_email_address'],
                                                    'changed_by_role' => 'admin'
                                                );
                                                tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                            }
                                        } else if ($payment_methods_filename_row['payment_methods_filename'] == 'alipay.php') {
                                            if ($auto_refund == '1') {

                                                require(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_ALIPAY);
                                                //lock * remember
                                                $alipay_seq_select_sql = "	SELECT alipay_refund_seq
																			FROM " . TABLE_ALIPAY_REFUND_SEQ;
                                                $alipay_seq_result_sql = tep_db_query($alipay_seq_select_sql);
                                                $alipay_seq_row = tep_db_fetch_array($alipay_seq_result_sql);
                                                $alipay_seq = (int) $alipay_seq_row['alipay_refund_seq'] + 1;

                                                tep_db_query("DELETE FROM " . TABLE_ALIPAY_REFUND_SEQ . " WHERE 1;");
                                                tep_db_perform(TABLE_ALIPAY_REFUND_SEQ, array('alipay_refund_seq' => $alipay_seq));
                                                //unlock

                                                $alipay_sql_str = date("Ymd") . str_repeat("0", (3 - strlen($alipay_seq))) . $alipay_seq;

                                                $alipay_info_select_sql = "	SELECT alipay_trade_no  
																			FROM " . TABLE_ALIPAY . " 
																			WHERE alipay_orders_id = '" . tep_db_input($refund_current_info_row['store_refund_trans_id']) . "'";
                                                $alipay_info_result_sql = tep_db_query($alipay_info_select_sql);
                                                $alipay_info_row = tep_db_fetch_array($alipay_info_result_sql);

                                                $transaction_id = $alipay_info_row['alipay_trade_no'];
                                                $transaction_user_id = $alipay_info_row['alipay_buyer_id'];

                                                $refund_type = 'Full';

                                                $currency_select_sql = "SELECT currency, currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($refund_current_info_row['store_refund_trans_id']) . "'";
                                                $currency_result_sql = tep_db_query($currency_select_sql);
                                                $currency_row = tep_db_fetch_array($currency_result_sql);

                                                if ($refund_current_info_row['store_refund_amount'] >= $ot['ot_total']['value']) {
                                                    $formatted_refund_amount = $ot['ot_total']['text'];
                                                } else {
                                                    $formatted_refund_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                }

                                                if ($refund_current_info_row['store_refund_trans_total_amount'] >= $ot['ot_total']['value']) {
                                                    $formatted_total_order_amount = $ot['ot_total']['text'];
                                                } else {
                                                    $formatted_total_order_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_trans_total_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                }

                                                if ($formatted_refund_amount < $formatted_total_order_amount) {
                                                    $refund_type = 'Partial';
                                                }
                                                //====================pipwave / crew refund API ====================
                                                $pipwave = new pipwave($refund_current_info_row['store_refund_trans_id']);
                                                if (isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
                                                    $result = $pipwave->refundAPI($formatted_refund_amount);
                                                    if (isset($result['status']) && $result['status'] == 200) {
                                                        $refund_update_sql_data_array = array(
                                                            'store_refund_is_processed' => 1,
                                                            'store_refund_payments_reference' => (isset($result['reverse_pw_id']) ? $result['reverse_pw_id'] : ''),
                                                            'store_refund_last_modified' => 'now()');
                                                        tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");
                                                    } else {
                                                        $processing_error = true;
                                                        if (isset($result['message'])) {
                                                            $res_message = sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, '-', $result['message']);
                                                        }
                                                    }
                                                } else {
                                                    $alipay_obj = new alipay();
                                                    $alipay_obj->get_merchant_account($currency_row['currency']);

                                                    $alipay_parameter = array("service" => "refund_fastpay_by_platform_nopwd",
                                                        "partner" => $alipay_obj->partner_id,
                                                        "notify_url" => tep_catalog_href_link(FILENAME_ALIPAY_IPN),
                                                        "_input_charset" => $alipay_obj->input_charset,
                                                        "batch_no" => $alipay_sql_str,
                                                        "refund_date" => date("Y-m-d H:i:s"),
                                                        "batch_num" => "1",
                                                        "detail_data" => $transaction_id . "^" . $formatted_refund_amount . "^" . $refund_id,
                                                        "return_type" => "xml"
                                                    );

                                                    require_once(DIR_FS_CATALOG_MODULES . 'payment/alipay/alipay_service.php');

                                                    $alipay_service_obj = new alipay_service($alipay_parameter, $alipay_obj->security_code, $alipay_obj->sign_type);
                                                    $alipay_link = $alipay_service_obj->create_url();
                                                    $response = $alipay_obj->curl_connect($alipay_link);

                                                    require_once(DIR_WS_CLASSES . 'ogm_xml_to_ary.php');
                                                    $xml_array_obj = new ogm_xml_to_ary($response, 'content');
                                                    $xml_data_array = $xml_array_obj->get_ogm_xml_to_ary();

                                                    if (isset($xml_data_array['alipay']['_c']['is_success']['_v']) && strtolower($xml_data_array['alipay']['_c']['is_success']['_v']) == 't') {
                                                        $refund_update_sql_data_array = array('store_refund_is_processed' => 1,
                                                            'store_refund_payments_reference' => $alipay_sql_str,
                                                            'store_refund_last_modified' => 'now()');
                                                        tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");
                                                    } else {
                                                        $processing_error = true;
                                                        $res_message = sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, '-', $xml_data_array['alipay']['_c']['error']['_v']);
                                                    }
                                                }
                                            }

                                            if (!$processing_error) {
                                                if ($auto_refund == '1') {
                                                    $comments = "Alipay Refund Submitted, waiting response from IPN";
                                                } else {
                                                    $refund_update_sql_data_array = array('store_refund_is_processed' => 0,
                                                        'store_refund_status' => (int) $to_status,
                                                        'store_refund_payments_reference' => tep_db_prepare_input($payment_reference),
                                                        'store_refund_last_modified' => 'now()');
                                                    tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . (int) $refund_id . "'");
                                                    $comments = "Alipay Refund manual complete.<BR>Payment Reference: " . $payment_reference;
                                                }

                                                // Insert refund payment history
                                                $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                    /* 'store_refund_status' => $to_status, */
                                                    'date_added' => 'now()',
                                                    'payee_notified' => '1',
                                                    'comments' => $comments,
                                                    'changed_by' => $_SESSION['login_email_address'],
                                                    'changed_by_role' => 'admin'
                                                );
                                                tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                            }
                                        } else if ($payment_methods_filename_row['payment_methods_filename'] == 'global_collect.php') {
                                            if ($auto_refund == '1') {

                                                require(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_GLOBAL_COLLECT);

                                                $global_collect_select_sql = "	SELECT global_collect_status_id
																			FROM " . TABLE_GLOBAL_COLLECT . "
																			WHERE global_collect_orders_id = '" . tep_db_input($refund_current_info_row['store_refund_trans_id']) . "'";
                                                $global_collect_result_sql = tep_db_query($global_collect_select_sql);
                                                $global_collect_row = tep_db_fetch_array($global_collect_result_sql);

                                                $global_collect_obj = new global_collect($refund_current_info_row['payment_methods_id']);
                                                if ($global_collect_row['global_collect_status_id'] < 900) {
                                                    if ($global_collect_row['global_collect_status_id'] < 800 || ($currencies->apply_currency_exchange($refund_current_info_row['store_refund_amount'], $refund_current_info_row['currency'], $refund_current_info_row['currency_value']) == $currencies->apply_currency_exchange($refund_current_info_row['store_refund_trans_total_amount'], $refund_current_info_row['currency'], $refund_current_info_row['currency_value']))) {
                                                        $global_collect_refund_returned_array = $global_collect_obj->do_cancel_payment($refund_current_info_row['store_refund_trans_id'], $refund_id);
                                                    } else if ($currencies->apply_currency_exchange($refund_current_info_row['store_refund_amount'], $refund_current_info_row['currency'], $refund_current_info_row['currency_value']) != $currencies->apply_currency_exchange($refund_current_info_row['store_refund_trans_total_amount'], $refund_current_info_row['currency'], $refund_current_info_row['currency_value'])) {
                                                        $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                            'date_added' => 'now()',
                                                            'payee_notified' => '0',
                                                            'comments' => "Cancel Payment Failed:<BR>Unable to cancel partial refund payment.",
                                                            'changed_by' => $_SESSION['login_email_address'],
                                                            'changed_by_role' => 'admin'
                                                        );
                                                        tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                                    }
                                                } else {
                                                    $global_collect_refund_returned_array = $global_collect_obj->do_refund($refund_current_info_row['store_refund_trans_id'], $refund_id);
                                                }

                                                if (strtoupper($global_collect_refund_returned_array['RESULT']) == 'OK') {
                                                    if ($global_collect_row['global_collect_status_id'] < 900) {
                                                        $comments = "Global Collect Cancel Payment Submitted, waiting response from IPN<br><br>";
                                                    } else {
                                                        $comments = "Global Collect Refund Submitted, waiting response from IPN<br><br>";
                                                    }
                                                    $comments .= "Response:<br>Request ID: " . $global_collect_refund_returned_array['REQUESTID'];

                                                    $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                        /* 'store_refund_status' => $to_status, */
                                                        'date_added' => 'now()',
                                                        'payee_notified' => '0',
                                                        'comments' => $comments,
                                                        'changed_by' => $_SESSION['login_email_address'],
                                                        'changed_by_role' => 'admin'
                                                    );
                                                    tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);

                                                    $refund_update_sql_data_array = array('store_refund_is_processed' => 1);
                                                    tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . (int) $refund_id . "'");
                                                } else {
                                                    if (isset($global_collect_refund_returned_array['CODE']) && isset($global_collect_refund_returned_array['MESSAGE'])) {
                                                        if ($global_collect_row['global_collect_status_id'] < 900) {
                                                            $comments = "Global Collect Cancel Payment Submitted, waiting response from IPN<br><br>";
                                                        } else {
                                                            $comments = "Global Collect Refund Submitted, waiting response from IPN<br><br>";
                                                        }
                                                        $comments .= "Response:<br>";
                                                        if (isset($global_collect_refund_returned_array['CODE'])) {
                                                            $comments .= "Code: " . $global_collect_refund_returned_array['CODE'] . "<br>";
                                                        }
                                                        if (isset($global_collect_refund_returned_array['MESSAGE'])) {
                                                            $comments .= "Message: " . $global_collect_refund_returned_array['MESSAGE'] . "<br>";
                                                        }
                                                        $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                            /* 'store_refund_status' => $to_status, */
                                                            'date_added' => 'now()',
                                                            'payee_notified' => '0',
                                                            'comments' => $comments,
                                                            'changed_by' => $_SESSION['login_email_address'],
                                                            'changed_by_role' => 'admin'
                                                        );
                                                        tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                                    }
                                                    $processing_error = true;
                                                }
                                            }

                                            if (!$processing_error) {
                                                if ($auto_refund != '1') {
                                                    $pipwave = new pipwave($refund_current_info_row['store_refund_trans_id']);
                                                    if (isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
                                                        $currency_select_sql = "SELECT currency, currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($refund_current_info_row['store_refund_trans_id']) . "'";
                                                        $currency_result_sql = tep_db_query($currency_select_sql);
                                                        $currency_row = tep_db_fetch_array($currency_result_sql);

                                                        if ($refund_current_info_row['store_refund_amount'] >= $ot['ot_total']['value']) {
                                                            $formatted_refund_amount = $ot['ot_total']['text'];
                                                        } else {
                                                            $formatted_refund_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                        }

                                                        $result = $pipwave->refundAPI($formatted_refund_amount);
                                                        if (isset($result['status']) && $result['status'] == 200) {
                                                            $payment_reference = (isset($result['reverse_pw_id']) ? $result['reverse_pw_id'] : '');
                                                            $refund_update_sql_data_array = array(
                                                                'store_refund_is_processed' => 1,
                                                                'store_refund_payments_reference' => $payment_reference,
                                                                'store_refund_last_modified' => 'now()');
                                                            tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");
                                                        } else {
                                                            if (isset($result['message'])) {
                                                                $res_message = sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, '-', $result['message']);
                                                            }
                                                        }
                                                    }
                                                    $refund_update_sql_data_array = array('store_refund_is_processed' => 0,
                                                        'store_refund_status' => (int) $to_status,
                                                        'store_refund_payments_reference' => tep_db_prepare_input($payment_reference),
                                                        'store_refund_last_modified' => 'now()');
                                                    tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . (int) $refund_id . "'");
                                                    if ($global_collect_row['global_collect_status_id'] < 900) {
                                                        $comments = "Global Collect Cancel Payment manual complete.<BR>Payment Reference: " . $payment_reference;
                                                    } else {
                                                        $comments = "Global Collect Refund Manual complete.<BR>Payment Reference: " . $payment_reference;
                                                    }

                                                    // Insert refund payment history
                                                    $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                        'store_refund_status' => $to_status,
                                                        'date_added' => 'now()',
                                                        'payee_notified' => '1',
                                                        'comments' => $comments,
                                                        'changed_by' => $_SESSION['login_email_address'],
                                                        'changed_by_role' => 'admin'
                                                    );
                                                    tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                                }
                                            }
                                        } else if ($payment_methods_filename_row['payment_methods_filename'] == 'bibit.php') {
                                            if ($auto_refund == '1') {
                                                require(DIR_FS_CATALOG_MODULES . 'payment/' . FILENAME_BIBIT);

                                                $bibit_obj = new bibit($refund_current_info_row['payment_methods_id']);
                                                $bibit_refund_returned_array = $bibit_obj->do_refund($refund_current_info_row['store_refund_trans_id'], $refund_id);

                                                if (strtoupper($bibit_refund_returned_array['code']) == 'SUCCESS') {
                                                    $refund_update_sql_data_array = array('store_refund_is_processed' => 1,
                                                        'store_refund_status' => $to_status,
                                                        'store_refund_payments_reference' => tep_db_prepare_input($payment_reference),
                                                        'store_refund_last_modified' => 'now()');
                                                    tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");

                                                    $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                        'store_refund_status' => $to_status,
                                                        'date_added' => 'now()',
                                                        'payee_notified' => '0',
                                                        'comments' => $bibit_refund_returned_array['text'],
                                                        'changed_by' => $_SESSION['login_email_address'],
                                                        'changed_by_role' => 'admin'
                                                    );
                                                    tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                                } else {
                                                    $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                        /* 'store_refund_status' => $to_status, */
                                                        'date_added' => 'now()',
                                                        'payee_notified' => '0',
                                                        'comments' => $bibit_refund_returned_array['text'],
                                                        'changed_by' => $_SESSION['login_email_address'],
                                                        'changed_by_role' => 'admin'
                                                    );
                                                    tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);

                                                    $processing_error = true;
                                                }
                                            }

                                            if (!$processing_error) {
                                                if ($auto_refund != '1') {
                                                    $pipwave = new pipwave($refund_current_info_row['store_refund_trans_id']);
                                                    if (isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
                                                        $currency_select_sql = "SELECT currency, currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($refund_current_info_row['store_refund_trans_id']) . "'";
                                                        $currency_result_sql = tep_db_query($currency_select_sql);
                                                        $currency_row = tep_db_fetch_array($currency_result_sql);

                                                        if ($refund_current_info_row['store_refund_amount'] >= $ot['ot_total']['value']) {
                                                            $formatted_refund_amount = $ot['ot_total']['text'];
                                                        } else {
                                                            $formatted_refund_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                        }

                                                        $result = $pipwave->refundAPI($formatted_refund_amount);
                                                        if (isset($result['status']) && $result['status'] == 200) {
                                                            $payment_reference = (isset($result['reverse_pw_id']) ? $result['reverse_pw_id'] : '');
                                                            $refund_update_sql_data_array = array(
                                                                'store_refund_is_processed' => 1,
                                                                'store_refund_payments_reference' => $payment_reference,
                                                                'store_refund_last_modified' => 'now()');
                                                            tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");
                                                        } else {
                                                            $processing_error = true;
                                                            if (isset($result['message'])) {
                                                                $res_message = sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, '-', $result['message']);
                                                            }
                                                        }
                                                    }
                                                    $refund_update_sql_data_array = array('store_refund_is_processed' => 0,
                                                        'store_refund_status' => $to_status,
                                                        'store_refund_payments_reference' => tep_db_prepare_input($payment_reference),
                                                        'store_refund_last_modified' => 'now()');
                                                    tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");

                                                    $comments = "Manual Refund is complete.<BR>Payment Reference: " . $payment_reference;

                                                    // Insert refund payment history
                                                    $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                        'store_refund_status' => $to_status,
                                                        'date_added' => 'now()',
                                                        'payee_notified' => '1',
                                                        'comments' => $comments,
                                                        'changed_by' => $_SESSION['login_email_address'],
                                                        'changed_by_role' => 'admin'
                                                    );
                                                    tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                                }
                                            }
                                        } else {
                                            switch ($payment_methods_filename_row['payment_methods_filename']) {
                                                case 'adyen.php':
                                                case 'paymaster24.php':
                                                case 'indomog.php':
                                                case 'moneybookers.php':
                                                case 'smart2pay_globalpay.php':
                                                    //DO NOT TRIGGER PIPWAVE REFUND API FOR THESE AS THE NOTIFICATION FROM PG WILL UPDATE PIPWAVE STATUS TO REFUND
                                                    //PREVENT DOUBLE REFUND
                                                    break;
                                                default:
                                                    //TRIGGER PIPWAVE REFUND API TO MARK PIPWAVE TRANSACTION AS REFUNDED AS PG WON'T INFORM PIPWAVE FOR STATUS CHANGED BY NOTIFICATION
                                                    if ($auto_refund == '1') {
                                                        $pipwave = new pipwave($refund_current_info_row['store_refund_trans_id']);
                                                        if (isset($pipwave->checkoutSite) && $pipwave->checkoutSite == 'pipwave') {
                                                            $currency_select_sql = "SELECT currency, currency_value FROM " . TABLE_ORDERS . " WHERE orders_id = '" . tep_db_input($refund_current_info_row['store_refund_trans_id']) . "'";
                                                            $currency_result_sql = tep_db_query($currency_select_sql);
                                                            $currency_row = tep_db_fetch_array($currency_result_sql);

                                                            if ($refund_current_info_row['store_refund_amount'] >= $ot['ot_total']['value']) {
                                                                $formatted_refund_amount = $ot['ot_total']['text'];
                                                            } else {
                                                                $formatted_refund_amount = $currencies->apply_currency_exchange($refund_current_info_row['store_refund_amount'], $currency_row['currency'], $currency_row['currency_value']);
                                                            }
    
                                                            $result = $pipwave->refundAPI($formatted_refund_amount);
                                                            if (isset($result['status']) && $result['status'] == 200) {
                                                                $payment_reference = (isset($result['reverse_pw_id']) ? $result['reverse_pw_id'] : '');
                                                                $refund_update_sql_data_array = array(
                                                                    'store_refund_is_processed' => 1,
                                                                    'store_refund_payments_reference' => $payment_reference,
                                                                    'store_refund_last_modified' => 'now()');
                                                                tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");
                                                            } else {
                                                                if (isset($result['message'])) {
                                                                    $res_message = sprintf(ERROR_REFUND_BATCH_ACTION_API_REFUND, $refund_id, '-', $result['message']);
                                                                }
                                                            }
                                                        }
                                                    }
                                                    break;
                                            }
                                            // Insert exchange rate, update paid amount
                                            $refund_update_sql_data_array = array('store_refund_status' => $to_status,
                                                'store_refund_payments_reference' => $payment_reference,
                                                'store_refund_last_modified' => 'now()');

                                            tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($refund_id) . "'");

                                            $comments = '';

                                            // Insert refund payment history
                                            $refund_history_sql_data_array = array('store_refund_id' => $refund_id,
                                                'store_refund_status' => $to_status,
                                                'date_added' => 'now()',
                                                'payee_notified' => '1',
                                                'comments' => $comments,
                                                'changed_by' => $_SESSION['login_email_address'],
                                                'changed_by_role' => 'admin'
                                            );
                                            tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
                                        }
                                    }

                                    $res_code = '1';

                                    break;
                                default:
                                    $res_code = '1';
                                    $res_message = JS_ERROR_REFUND_TRANS_UNKNOWN_STATUS;

                                    break;
                            }
                        } else {
                            $res_code = '1';
                            $res_message = JS_ERROR_REFUND_INVALID_REFUND_AMOUNT;
                        }
                    } else { // This payment has been modified
                        $res_code = '1';
                        $res_message = JS_ERROR_REFUND_TRANS_MODIFIED;
                    }

                    // Get the latest refund status and generate the cell html
                    $refund_updated_info_select_sql = "	SELECT sr.*, o.currency, o.currency_value, 
                                                        o.payment_methods_parent_id, o.payment_method, o.payment_methods_id
														FROM " . TABLE_STORE_REFUND . " as sr 
														INNER JOIN " . TABLE_ORDERS . " AS o 
															ON sr.store_refund_trans_id = o.orders_id 
														WHERE sr.store_refund_id = '" . tep_db_input($refund_id) . "'";
                    $refund_updated_info_result_sql = tep_db_query($refund_updated_info_select_sql);

                    if ($refund_updated_info_row = tep_db_fetch_array($refund_updated_info_result_sql)) {
                        $refund_batch_available = false;

                        $order_id = $refund_updated_info_row['store_refund_trans_id']; // Group by trans id
                        $order_total_amount = $refund_updated_info_row['store_refund_trans_total_amount'];
                        $refund_amount = $refund_updated_info_row['store_refund_amount'];

                        $user_info_array = $xmlhttp_payments_object->_get_user_particulars($refund_updated_info_row['user_id'], 'customers');
                        $might_be_rollback = $xmlhttp_payments_object->_check_refund_rollback($refund_updated_info_row['store_refund_trans_id'], $refund_updated_info_row['store_refund_date']);

                        $order_id_link = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $order_id . '&action=edit') . '" target="_blank">' . $refund_updated_info_row['store_refund_trans_id'] . '</a>';
                        $user_name_link = '<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'cID=' . $refund_updated_info_row['user_id'] . '&action=edit') . '" target="_blank">' . $refund_updated_info_row['user_firstname'] . ' ' . $refund_updated_info_row['user_lastname'] . (tep_not_null($user_info_array['sign_up_from']) ? ' [' . $user_info_array['sign_up_from'] . ']' : '') . '</a>';
                        $edit_link = '<a href="' . tep_href_link($filename, 'refID=' . $refund_id . '&action=edit', 'NONSSL') . '">' . tep_image(DIR_WS_ICONS . "edit.gif", "Edit", "14", "13", 'align="top"') . '</a>';

                        $original_payment_gateway_trans_id = $xmlhttp_payments_object->_get_payment_gateway_trans_id($order_id, $refund_updated_info_row['payment_methods_parent_id']);

                        $payment_methods_filename_select_sql = "	SELECT payment_methods_filename 
																	FROM " . TABLE_PAYMENT_METHODS . "
																	WHERE payment_methods_id = '" . $refund_updated_info_row['payment_methods_parent_id'] . "'";
                        $payment_methods_filename_result_sql = tep_db_query($payment_methods_filename_select_sql);
                        $payment_methods_filename_row = tep_db_fetch_array($payment_methods_filename_result_sql);

                        if (isset($payment_methods_filename_row['payment_methods_filename']) && $payment_methods_filename_row['payment_methods_filename'] == 'paypalEC.php') {
                            $payment_required_fields = $xmlhttp_payments_object->_get_required_receive_payment_info($order_id, $refund_updated_info_row['payment_methods_parent_id'], 'paypalEC.php');
                        } else {
                            $payment_required_fields = $xmlhttp_payments_object->_get_required_receive_payment_info($order_id, $refund_updated_info_row['payment_methods_parent_id']);
                        }

                        $store_refund_is_processed = (int) $refund_updated_info_row['store_refund_is_processed'];

                        if ($refund_updated_info_row['store_refund_status'] == '2' && !$store_refund_is_processed) {
                            $payment_reference_str = tep_draw_input_field('payment_reference[' . $refund_id . ']', $refund_updated_info_row['store_refund_payments_reference'], ' id="payment_reference_' . $refund_id . '" size="16" onKeyPress="return noEnterKey(event)" ');
                            $payment_reference_input = true;
                        } else {
                            $payment_reference_str = $refund_updated_info_row['store_refund_payments_reference'];
                        }

                        $store_refund_is_processed = $refund_updated_info_row['store_refund_is_processed'];

                        switch ($refund_updated_info_row['store_refund_status']) {
                            case '1': // Pending
                                $action_button_html = tep_button('Process', 'Process this refund payment', '', ' name="ProcessBtn_' . $refund_id . '" onClick="updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'1\', \'2\', \'' . $row_cnt . '\');" ', 'inputButton') . '&nbsp;';

                                break;
                            case '2': // Processing
                                $pwUrl = '';
                                $pipwave = new pipwave($order_id);
                                if (isset($pipwave->pw_id) && !empty($pipwave->pw_id)) {
                                    $pwUrl = PIPWAVE_PAYMENT_TRANSACTION_REPORT_URL . '?pw_id=' . $pipwave->pw_id;
                                }
                                if ($payment_methods_filename_row['payment_methods_filename'] == 'paypal.php') {
                                    $paypal_manual_refund_permission = tep_admin_files_actions(FILENAME_REFUND, 'REFUND_MANUAL_PAYPAL_REFUND');
                                    if (!empty($pwUrl)) {
                                        $action_button_html = tep_button('pipwave Refund API', 'Complete this refund payment via pipwave', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    } else {
                                        $action_button_html = tep_button('PayPal Refund API', 'Complete this refund payment using PayPal Refund API', '', ' name="CompleteBtn_' . $refund_id . '_API" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    }

                                    if ($paypal_manual_refund_permission) {
                                        $action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\'); } else { return false; }" ', 'inputButton');
                                    }
                                } else if ($payment_methods_filename_row['payment_methods_filename'] == 'paypalEC.php') {
                                    $paypal_manual_refund_permission = tep_admin_files_actions(FILENAME_REFUND, 'REFUND_MANUAL_PAYPAL_REFUND');
                                    if (!empty($pwUrl)) {
                                        $action_button_html = tep_button('pipwave Refund API', 'Complete this refund payment via pipwave', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    } else {
                                        $action_button_html = tep_button('PayPalEC Refund API', 'Complete this refund payment using PayPalEC Refund API', '', ' name="CompleteBtn_' . $refund_id . '_API" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    }

                                    if ($paypal_manual_refund_permission) {
                                        $action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\'); } else { return false; }" ', 'inputButton');
                                    }
                                } else if ($payment_methods_filename_row['payment_methods_filename'] == 'alipay.php') {
                                    if (!empty($pwUrl)) {
                                        $action_button_html = tep_button('pipwave Refund API', 'Complete this refund payment via pipwave', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    } else {
                                        $action_button_html = tep_button('Alipay Refund API', 'Alipay Refund API', '', ' name="CompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
                                    }

                                    /* $alipay_manual_refund_permission = tep_admin_files_actions(FILENAME_REFUND, 'REFUND_MANUAL_ALIPAY_REFUND');
                                      if ($alipay_manual_refund_permission) { */
                                    //}
                                    $action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton');
                                    $action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton');
                                } else if ($payment_methods_filename_row['payment_methods_filename'] == 'bibit.php') {
                                    $bibit_select_sql = "	SELECT bibit_status, bibit_payment_method
                                                            FROM " . TABLE_BIBIT . "
                                                            WHERE orders_id = '" . (int) $order_id . "'";
                                    $bibit_result_sql = tep_db_query($bibit_select_sql);
                                    $bibit_row = tep_db_fetch_array($bibit_result_sql);

                                    if ($bibit_row['bibit_status'] == 'CAPTURED' && in_array($bibit_row['bibit_payment_method'], array('VISA-SSL', 'ECMC-SSL'))) { // Only Captured status and Visa/Master card can be refunded via API
                                        $action_button_html = tep_button('Refund API', 'Refund API', '', ' name="CompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
                                    }

                                    $action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\'); } else { return false; }" ' . ((int) $store_refund_is_processed ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
                                    if (!empty($pwUrl)) {
										// $action_button_html .= tep_button('Manual Complete via pipwave', 'Complete this refund payment manually via pipwave', '', ' name="MCompleteBtn_'.$refund_id.'" id="MCompleteBtn_'.$refund_id.'" onClick="pipwavePopUp(\''.$pwUrl.'\')" ', 'inputButton');
                                    }
                                } else if ($payment_methods_filename_row['payment_methods_filename'] == 'global_collect.php') {
                                    $global_collect_select_sql = "	SELECT global_collect_status_id
																	FROM " . TABLE_GLOBAL_COLLECT . "
																	WHERE global_collect_orders_id = '" . (int) $order_id . "'";
                                    $global_collect_result_sql = tep_db_query($global_collect_select_sql);
                                    $global_collect_row = tep_db_fetch_array($global_collect_result_sql);

                                    $action_button_html = tep_button('Refund API', 'Refund API', '', ' name="CompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\'); } else { return false; }" ' . ((int) $store_refund_is_processed || ($global_collect_row['global_collect_status_id'] >= 800 && $global_collect_row['global_collect_status_id'] < 900 && ($trans_row['store_refund_trans_total_amount'] != $trans_row['store_refund_amount'])) ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
                                    $action_button_html .= tep_button('Manual Complete', 'Complete this refund payment manually', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\'); } else { return false; }" ' . ((int) $store_refund_is_processed || ($global_collect_row['global_collect_status_id'] >= 800 && $global_collect_row['global_collect_status_id'] < 900 && ($trans_row['store_refund_trans_total_amount'] != $trans_row['store_refund_amount'])) ? ' disabled ' : ''), 'inputButton') . '&nbsp;';
                                    if (!empty($pwUrl)) {
										// $action_button_html .= tep_button('Manual Complete via pipwave', 'Complete this refund payment manually via pipwave', '', ' name="MCompleteBtn_'.$refund_id.'" id="MCompleteBtn_'.$refund_id.'" onClick="pipwavePopUp(\''.$pwUrl.'\')" ', 'inputButton');
                                    }
                                } else {
                                    if (!empty($pwUrl)) {
                                        $action_button_html = tep_button('pipwave Refund API', 'Complete this refund payment via pipwave', '', ' name="MCompleteBtn_' . $refund_id . '" id="MCompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'1\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                    }

                                    $action_button_html .= tep_button('Complete', 'Complete this refund payment', '', ' name="CompleteBtn_' . $refund_id . '" onClick=" if (confirm(\'Are you sure to complete this refund payment?\') != \'0\') { updateRefund(\'' . $filename . '\', this, \'' . $refund_id . '\', \'2\', \'3\', \'' . $pm_cnt . '\', \'0\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
                                }

                                $refund_batch_available = true;

                                break;
                            case '3': // Completed
                                $action_button_html = 'Completed';

                                break;
                            case '4': // Canceled
                                $action_button_html = 'Canceled';

                                break;
                            default:
                                $action_button_html = '';

                                break;
                        }

                        if ($might_be_rollback || $currencies->apply_currency_exchange($refund_amount, $refund_updated_info_row['currency'], $refund_updated_info_row['currency_value']) < $currencies->apply_currency_exchange($order_total_amount, $refund_updated_info_row['currency'], $refund_updated_info_row['currency_value'])) {
                            $custom_style_css = 'class="redIndicator"';
                        } else {
                            $custom_style_css = '';
                        }

                        // Payment methods name
                        $payment_methods_name = '';
                        $payment_gateway_select_sql = "	SELECT pg_display_name
                                                        FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . "
                                                        WHERE pg_id = '" . $refund_updated_info_row['payment_methods_parent_id'] . "'
                                                            AND pm_id = '" . $refund_updated_info_row['payment_methods_id'] . "'
                                                        LIMIT 1";
                        $payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql);
                        if ($payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql)) {
                            $payment_methods_name = $payment_gateway_row['pg_display_name'] . ' - ';
                        }
                        
                        if ($refund_updated_info_row['payment_method']) {
                            $payment_methods_name .= $refund_updated_info_row['payment_method'];
                        } else {
                            $pm_child_obj = new payment_methods($refund_updated_info_row['payment_methods_id']);
                            $pm_child_obj = $pm_child_obj->payment_method_array;
                            $payment_methods_name .= $pm_child_obj->title;
                            unset($pm_child_obj);
                        }

                        echo "<table_cell>";

                        echo "<cell property='nowrap=1'><![CDATA[<span " . $custom_style_css . ">" . $refund_id . "</span>]]></cell>";
                        echo "<cell property='nowrap=1'><![CDATA[<span " . $custom_style_css . ">" . $order_id_link . "</span>]]></cell>";
                        echo "<cell property='nowrap=1'><![CDATA[<span " . $custom_style_css . ">" . $user_name_link . "</span>]]></cell>";
                        echo "<cell property='nowrap=1'><![CDATA[<span " . $custom_style_css . ">" . $user_info_array['email'] . "</span>]]></cell>";
                        echo "<cell property='align=center&amp;nowrap=1'><![CDATA[<span " . $custom_style_css . ">" . $original_payment_gateway_trans_id . "</span>]]></cell>";

                        echo "<cell property='align=center&amp;nowrap=1'><![CDATA[<span " . $custom_style_css . ">";
                        for ($field_cnt = 0; $field_cnt < count($payment_required_fields); $field_cnt++) {
                            //echo $payment_required_fields[$field_cnt]['title'] . ': ' . $payment_required_fields[$field_cnt]['value'] . '<br>';
                            echo $payment_required_fields[$field_cnt]['value'] . '<br>';
                        }
                        echo '</span>]]></cell>';

                        if ($refund_updated_info_row['store_refund_amount'] >= $ot['ot_total']['value']) {
                            $refund_amount = $ot['ot_total']['text'];
                        } else {
                            $refund_amount = $currencies->apply_currency_exchange($refund_updated_info_row['store_refund_amount'], $refund_updated_info_row['currency'], $refund_updated_info_row['currency_value']);
                        }

                        if ($refund_updated_info_row['store_refund_trans_total_amount'] >= $ot['ot_total']['value']) {
                            $order_total_amount = $ot['ot_total']['text'];
                        } else {
                            $order_total_amount = $currencies->apply_currency_exchange($refund_updated_info_row['store_refund_trans_total_amount'], $refund_updated_info_row['currency'], $refund_updated_info_row['currency_value']);
                        }
                        
                        echo "<cell property='align=left'><![CDATA[<span " . $custom_style_css . ">" . $payment_methods_name . "</span>]]></cell>";
                        echo "<cell property='align=left'><![CDATA[<span " . $custom_style_css . ">" . $xmlhttp_payments_object->_get_settlement_account($order_id) . "</span>]]></cell>";
                        echo "<cell property='align=left'><![CDATA[<span " . $custom_style_css . ">" . $refund_updated_info_row['currency'] . "</span>]]></cell>";
                        echo "<cell property='align=right'><![CDATA[<span " . $custom_style_css . ">" . $currencies->custom_format($order_total_amount, false, $refund_updated_info_row['currency']) . "</span>]]></cell>";
                        echo "<cell property='align=right&amp;nowrap=1'><![CDATA[<span " . $custom_style_css . ">" . $currencies->custom_format($refund_amount, false, $refund_updated_info_row['currency']) . "</span>]]></cell>";
                        echo "<cell property='align=center&amp;nowrap=1'><![CDATA[<span " . $custom_style_css . ">" . $payment_reference_str . "</span>]]></cell>";
                        echo "<cell property='align=center'><![CDATA[<span " . $custom_style_css . ">" . $edit_link . "</span>]]></cell>";
                        echo "<cell property='align=center&amp;nowrap=1'><![CDATA[" . $action_button_html . "]]></cell>";
                        echo "<cell><![CDATA[" . tep_draw_checkbox_field('refund_batch[]', $refund_id, false, '', 'id="' . $refund_id . '" onClick="update_selected_price(this.form, \'' . $row_cnt . '\', \'refund_batch\');" ' . (!$refund_batch_available ? ' DISABLED ' : '')) . "]]></cell>";

                        echo "</table_cell>";
                    }
                } else {
                    $res_code = '-1';
                    $res_message = JS_ERROR_PAYMENT_TRANS_NOT_EXISTS;
                }
            }

            echo "<res_code><![CDATA[" . $res_code . "]]></res_code>";
            echo "<res_message><![CDATA[" . $res_message . "]]></res_message>";
            /*
              if ($email_beneficiary)	$xmlhttp_payments_object->send_payment_status_email($payment_id, $email_comments_str);
             */
            break;
        case "refresh_po_pm_fields":
            $res_html = '';
            $pm_options = '<option value="">' . PULL_DOWN_DEFAULT . '</option>';
            $system_defined_pm_array = array();

            echo "<option_selection><![CDATA[";

            $payment_cur_code = isset($_GET['cur_code']) ? $_GET['cur_code'] : '';
            $payment_pm_id = isset($_GET['pm_id']) ? $_GET['pm_id'] : '';
            $payment_book_id = isset($_GET['b_id']) ? $_GET['b_id'] : '';
            $user_id = isset($_GET['u_id']) ? $_GET['u_id'] : '';
            $sc_currency = '';

            if (isset($user_id)) {
                if (tep_not_null($payment_cur_code)) {
                    $payment_currency_id = array_search($payment_cur_code, $currencies->internal_currencies);
                } else {
                    // Check for Store Credit Payment Methods
                    $payment_currency_id = 0;
                }

                $payment_method_select_sql = "	SELECT payment_methods_id, payment_methods_send_mode_name, payment_methods_send_available_sites 
												FROM " . TABLE_PAYMENT_METHODS . "
												WHERE payment_methods_send_status = 1
													AND payment_methods_send_status_mode = 1 
													AND payment_methods_parent_id <> '0' 
													AND payment_methods_send_currency = '" . tep_db_input($payment_currency_id) . "'
												ORDER BY payment_methods_sort_order";

                $payment_method_result_sql = tep_db_query($payment_method_select_sql);

                while ($payment_method_row = tep_db_fetch_array($payment_method_result_sql)) {
                    if (strstr($payment_method_row['payment_methods_send_available_sites'], '3') !== false) {
                        $pm_options .= '<option value="' . $payment_method_row['payment_methods_id'] . '">' . $payment_method_row['payment_methods_send_mode_name'] . '</option>';
                    }
                }

                $res_html = $pm_options;

                echo $res_html;
            }
            echo "]]></option_selection>";

            break;
        case "add_po_pm_fields":
        case "edit_po_pm_fields":
            $res_html = '';
            $info_html = '';

            echo "<option_selection><![CDATA[";

            $payment_method_id = isset($_GET['pm_id']) ? $_GET['pm_id'] : '';
            $payment_book_id = isset($_GET['b_id']) ? $_GET['b_id'] : '';
            $user_id = isset($_GET['u_id']) ? $_GET['u_id'] : '';

            $payment_fields_select_sql = "	SELECT pmf.*, spabd.payment_methods_fields_value 
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
											LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " AS spabd 
												ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id AND spabd.store_payment_account_book_id = '" . tep_db_input($payment_book_id) . "')
											LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
												ON (spabd.store_payment_account_book_id = spab.store_payment_account_book_id" . (tep_not_null($user_id) ? " AND spab.user_id = '" . (int) $user_id . "'" : "") . ") 
											WHERE pmf.payment_methods_id = '" . tep_db_input($payment_method_id) . "' 
												AND pmf.payment_methods_mode = 'SEND' 
												AND pmf.payment_methods_fields_status = 1 
											ORDER BY pmf.payment_methods_fields_sort_order";
            $payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
            if (tep_db_num_rows($payment_fields_result_sql)) {
                
            } else {
                $payment_fields_select_sql = "	SELECT pmf.*
												FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
												WHERE payment_methods_id = '" . tep_db_input($payment_method_id) . "' 
													AND payment_methods_mode = 'SEND' 
													AND payment_methods_fields_status = 1 
												ORDER BY payment_methods_fields_sort_order";
                $payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
            }

            while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
                $input_section = tep_xmlhttp_draw_payment_fields($payment_fields_row, $payment_fields_row['payment_methods_fields_value']);

                if ($payment_fields_row['payment_methods_fields_type'] == '7') {
                    $info_html .= '<tr class="inputBoxContents">
									<td class="main" width="20%" valign="top">' . $payment_fields_row['payment_methods_fields_title'] . '</td>
									<td class="main">' . nl2br($input_section['field']) . '</td>
								  </tr>';
                } else {
                    $res_html .= '<tr class="inputBoxContents">
									<td class="main" width="20%" valign="top">' . $payment_fields_row['payment_methods_fields_title'] . '</td>
									<td class="main">' . $input_section['field'] . '</td>
								  </tr>';
                }
            }

            echo '<table border="0" width="100%" cellspacing="0" cellpadding="2">' . $info_html . '<tr><td colspan="2"><div class="dottedLine"><!-- --></div></td></tr>' . $res_html . '</table>';
            echo "]]></option_selection>";

            break;
        case "add_po_pm_book":
            $user_id = isset($_GET['u_id']) ? $_GET['u_id'] : '';

            $pm_info_object = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $user_id, '3');
            $pm_html = $pm_info_object->new_payment_account();

            echo "<option_selection><![CDATA[";
            echo '<table border="0" width="100%" cellspacing="0" cellpadding="2"><tr><td class="formArea">';
            echo '<table border="0" width="100%" cellspacing="0" cellpadding="2">' . $pm_html . '</table>';
            echo '</td></tr></table>';
            echo "]]></option_selection>";

            break;
        case "edit_po_pm_book":
            $user_id = isset($_GET['u_id']) ? $_GET['u_id'] : '';
            $book_id = isset($_GET['b_id']) ? $_GET['b_id'] : '';

            $pm_info_object = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $user_id, '3');
            $pm_html = $pm_info_object->edit_payment_account($book_id);

            echo "<option_selection><![CDATA[";
            echo '<table border="0" width="100%" cellspacing="0" cellpadding="2"><tr><td class="formArea">';
            echo '<table border="0" width="100%" cellspacing="0" cellpadding="2">' . $pm_html . '</table>';
            echo '</td></tr></table>';
            echo "]]></option_selection>";

            break;
        default:
            echo "<result>Unknown request!</result>";

            break;
    }
}

echo '</response>';

function tep_xmlhttp_update_trans_comment($trans_type, $trans_id, $comment) {
    global $login_email_address;

    switch ($trans_type) {
        case "S": // Supplier Order
            $trans_history_data_array = array('supplier_order_lists_id' => $trans_id,
                'supplier_order_lists_status' => 0,
                'date_added' => 'now()',
                'supplier_notified' => 0,
                'comments' => $comment,
                'set_as_order_list_remarks' => 0,
                'changed_by' => $login_email_address
            );
            tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $trans_history_data_array);

            break;
    }
}

function tep_xmlhttp_draw_payment_fields($field_info_array, $default = '') {
    $field_name = 'pm_field[' . $field_info_array['payment_methods_fields_id'] . ']';
    $field_id = 'pm_field' . $field_info_array['payment_methods_fields_id'];
    $required = $field_info_array["payment_methods_fields_required"] == 1 ? true : false;
    $js_html = '';

    switch ($field_info_array["payment_methods_fields_type"]) {
        case "1": // Text Box
            if (tep_not_null($field_info_array["payment_methods_fields_size"])) {
                list($size, $max_len) = explode(',', $field_info_array["payment_methods_fields_size"]);
            }

            $param = ' id="' . $field_id . '" SIZE=' . ($size > 0 ? $size : '20') . (isset($max_len) && $max_len > 0 ? " MAXLENGTH=" . $max_len : '');

            return array('field' => tep_draw_input_field($field_name, $default, $param) . ($required ? TEXT_FIELD_REQUIRED : ''), 'js' => $js_html);

            break;
        case "2": // Text Area
            if (tep_not_null($field_info_array["payment_methods_fields_size"])) {
                list($row_val, $col_val) = explode(',', $field_info_array["payment_methods_fields_size"]);
            }

            $param = ' id="' . $field_id . '" ';

            $field_html = '<div style="float: left;">' . tep_draw_textarea_field($field_name, "soft", $col_val, $row_val, $default, $param, false) . '</div>' .
                    '<div>' . ($required ? TEXT_FIELD_REQUIRED : '&nbsp;') . '</div>';

            return array('field' => $field_html, 'js' => $js_html);

            break;
        case "3": // Dropdown Menu
            $selection_array = array();
            if (tep_not_null($field_info_array["payment_methods_fields_option"])) {
                $selection_list = explode(':~:', $field_info_array["payment_methods_fields_option"]);
                foreach ($selection_list as $val) {
                    $selection_array[] = array('id' => tep_db_input($val), 'text' => $val);
                }
            }

            $param = ' id="' . $field_id . '" ';

            return array('field' => tep_draw_pull_down_menu($field_name, $selection_array, $default, $param) . ($required ? TEXT_FIELD_REQUIRED : ''), 'js' => $js_html);

            break;
        case "4": // Radio Button
            $js_html = '';
            $update_on_change = false;
            $field_html = '';

            $selection_list = explode(':~:', $field_info_array["payment_methods_fields_option"]);
            foreach ($selection_list as $val) {
                $field_html .= tep_draw_radio_field($field_name, tep_db_input($val), ($val == $default ? true : false), '') . '&nbsp;' . $val . '<br>';
            }

            if ($required)
                $field_html .= TEXT_FIELD_REQUIRED;

            return array('field' => $field_html, 'js' => $js_html);

            break;
        case "5": // Date Selection
            if (tep_not_null($field_info_array["payment_methods_fields_size"])) {
                list($from_date, $period) = explode(',', $field_info_array["payment_methods_fields_size"]);
                if ($from_date == 'TODAY') {
                    $from_date = date('Y-m-d');
                }
                return array('field' => tep_draw_date_box($field_name, $from_date, $period, $default) . ($required ? TEXT_FIELD_REQUIRED : ''));
            }

            break;
        case "7":
            $field_info_array["payment_methods_fields_option"] = str_replace(':~:', "\r\n", $field_info_array["payment_methods_fields_option"]);

            $hidden_value = tep_draw_hidden_field($field_name, $field_info_array["payment_methods_fields_option"]); // Use hidden field to pass info of this type. need it to be saved in database for shown in "Edit Order" page as well.
            return array('field' => $field_info_array["payment_methods_fields_option"] . $hidden_value, 'js' => '');

            break;
    }
}

function tep_xmlhttp_calculate_fees($payment_method_id, $withdraw_currency, $withdraw_amount) {
    global $currencies;

    $trans_fee_array = array('cost' => 0, 'percent' => 0);

    $payment_fee_select_sql = "	SELECT * 
								FROM " . TABLE_PAYMENT_FEES . "	
								WHERE payment_methods_id = '" . tep_db_input($payment_method_id) . "' 
									AND payment_methods_mode = 'SEND'";
    $payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
    if (!tep_db_num_rows($payment_fee_result_sql)) {
        $payment_fee_select_sql = "	SELECT pf.* 
									FROM " . TABLE_PAYMENT_FEES . "	AS pf 
									INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
										ON pf.payment_methods_id = pm.payment_methods_parent_id
									WHERE pm.payment_methods_id = '" . tep_db_input($payment_method_id) . "' 
										AND pf.payment_methods_mode = 'SEND'";
        $payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
    }
    $payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);

    if ($payment_fee_row['payment_fees_cost_value'] > 0)
        $trans_fee_array['cost'] = $payment_fee_row['payment_fees_cost_value'];

    if ($payment_fee_row['payment_fees_cost_percent'] > 0) {
        $percent_fees_not_in_range = false;

        $percent_fees = ($withdraw_amount * $payment_fee_row['payment_fees_cost_percent']) / 100;

        if ($payment_fee_row['payment_fees_cost_percent_min'] > 0) {
            $w_currency_min_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_min'], $withdraw_currency, '', 'sell');
            if ($percent_fees < $w_currency_min_fee) {
                $percent_fees = $payment_fee_row['payment_fees_cost_percent_min'];
                $percent_fees_not_in_range = true;
            }
        }

        if ($payment_fee_row['payment_fees_cost_percent_max'] > 0) {
            $w_currency_max_fee = $currencies->apply_currency_exchange($payment_fee_row['payment_fees_cost_percent_max'], $withdraw_currency, '', 'sell');

            if ($percent_fees > $w_currency_max_fee) {
                $percent_fees = $payment_fee_row['payment_fees_cost_percent_max'];
                $percent_fees_not_in_range = true;
            }
        }

        if ($percent_fees_not_in_range) {
            $trans_fee_array['cost'] += $percent_fees;
        } else {
            $trans_fee_array['percent'] += $payment_fee_row['payment_fees_cost_percent'];
        }
    }

    return $trans_fee_array;
}

?>