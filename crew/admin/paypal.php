<?
/*
  	$Id: paypal.php,v 1.9 2015/10/21 09:33:03 sionghuat.chng Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	DevosC, Developing open source Code
  	http://www.devosc.com
	
  	Copyright (c) 2003 osCommerce
  	Copyright (c) 2004 DevosC.com
	
  	Released under the GNU General Public License
*/
require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

require_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/functions/paypal.fnc.php');
paypal_include_lng(DIR_FS_CATALOG_MODULES . 'payment/paypal/admin/languages/', $language, 'paypal.lng.php');

if ($action == 'test') {
	include_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/admin/ipn_test_panel.php');
    exit;
}

require(DIR_FS_CATALOG_MODULES . 'payment/paypal.php');
require_once(DIR_FS_CATALOG_MODULES . 'payment/paypal/database_tables.inc.php');

$payment_statuses = array();
$payment_status_query = tep_db_query("select payment_status_name as payment_status from " . TABLE_PAYPAL_PAYMENT_STATUS );
while ($payment_status = tep_db_fetch_array($payment_status_query)) {
	$payment_statuses[] = array('id' => $payment_status['payment_status'],
                               	'text' => $payment_status['payment_status']);
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_ADMIN_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
            						<td class="smallText" align="right">
									<?
									echo tep_draw_form('payment_status', FILENAME_PAYPAL, '', 'post') . HEADING_PAYMENT_STATUS . ' ' . tep_draw_pull_down_menu('payment_status', array_merge(array(array('id' => '', 'text' => TEXT_ALL_IPNS)), $payment_statuses), $_REQUEST['payment_status'], 'onChange="this.form.submit();"').'</form>';
									?>
            						</td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
								                <td class="dataTableHeadingContent"><?=TABLE_HEADING_ORDER_NUMBER?></td>
								                <td class="dataTableHeadingContent"><?=TABLE_HEADING_TXN_TYPE?></td>
								                <td class="dataTableHeadingContent"><?=TABLE_HEADING_PAYMENT_STATUS?></td>
								                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_PAYMENT_AMOUNT?></td>
								                <td class="dataTableHeadingContent" align="right"><?=TABLE_HEADING_ACTION?>&nbsp;</td>
              								</tr>
<?
	if (tep_check_cat_tree_permissions(FILENAME_ORDERS, 0) != 1) {
		$sub_cat_array = tep_get_eligible_categories(FILENAME_ORDERS, $sub_cat_array, 0, true);
		
		$extra_join_str = " left join " . TABLE_ORDERS_PRODUCTS . " as op on o.orders_id=op.orders_id ";
		$extra_join_str .= " left join " . TABLE_PRODUCTS_TO_CATEGORIES . " as pc on (op.products_id=pc.products_id and pc.products_is_link=0) ";
		
  		$filter_str = " AND pc.categories_id IN ('" . implode("', '", $sub_cat_array) . "') ";
	}
	
	if (isset($_REQUEST['payment_status']) && tep_not_null($_REQUEST['payment_status']) ) {
		$ipn_search = "and p.payment_status = '" . tep_db_prepare_input($_REQUEST['payment_status']) . "'";
		switch($_REQUEST['payment_status']) {
			case 'Pending':
      		case 'Completed':
      		default:
        		$ipn_query_raw = "select p.invoice, p.txn_type, p.payment_type, p.payment_status, p.pending_reason, p.mc_currency, p.payer_status, p.mc_currency, p.date_added, p.mc_gross, o.orders_id from " . TABLE_PAYPAL . " as p, " .TABLE_ORDERS . " as o " . $extra_join_str . " where o.orders_id = p.invoice " . $ipn_search . $filter_str . " order by o.orders_id DESC";
      		break;
    	}
  	} else {
        $ipn_query_raw = "select p.invoice, p.txn_type, p.payment_type, p.payment_status, p.pending_reason, p.mc_currency, p.payer_status, p.mc_currency, p.date_added, p.mc_gross, o.orders_id from " . TABLE_PAYPAL . " as p left join " .TABLE_ORDERS . " as o on p.invoice=o.orders_id " . $extra_join_str . " where 1 " . $filter_str . " order by o.orders_id DESC";
  	}
  	
  	$ipn_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $ipn_query_raw, $ipn_query_numrows);
  	$ipn_query = tep_db_query($ipn_query_raw);
  	while ($ipn_trans = tep_db_fetch_array($ipn_query)) {
    	if ((!isset($_REQUEST['ipnID']) || (isset($_REQUEST['ipnID']) && ($_REQUEST['ipnID'] == $ipn_trans['invoice']))) && !isset($ipnInfo) ) {
      		$ipnInfo = new objectInfo($ipn_trans);
    	}
		
    	if (isset($ipnInfo) && is_object($ipnInfo) && ($ipn_trans['invoice'] == $ipnInfo->orders_id) ) {
      		echo '              			<tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_ORDERS, 'page=' . $HTTP_GET_VARS['page'] . '&ipnID=' . $ipnInfo->invoice . '&oID=' . $ipnInfo->orders_id . '&action=edit' . '&referer=ipn') . '\'">' . "\n";
    	} else {
      		echo '         					<tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_PAYPAL, 'page=' . $HTTP_GET_VARS['page'] . '&ipnID=' . $ipn_trans['invoice']) . '\'">' . "\n";
    	}
?>
								                <td class="dataTableContent"> <?=$ipn_trans['orders_id']?> </td>
								                <td class="dataTableContent"> <?=$ipn_trans['txn_type']?>
								                <td class="dataTableContent"><?=$ipn_trans['payment_status_name'] . ' '. $ipn_trans['payment_status']?></td>
								                <td class="dataTableContent" align="right"><?=$ipn_trans['mc_currency'] . ' '.number_format($ipn_trans['mc_gross'], 2)?></td>
								                <td class="dataTableContent" align="right"><? if (isset($ipnInfo) && is_object($ipnInfo) && ($ipn_trans['invoice'] == $ipnInfo->orders_id) ) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif'); } else { echo '<a href="' . tep_href_link(FILENAME_PAYPAL, 'page=' . $HTTP_GET_VARS['page'] . '&ipnID=' . $ipn_trans['invoice']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              								</tr>
<?	} ?>
              								<tr>
                								<td colspan="5">
                									<table border="0" width="100%" cellspacing="0" cellpadding="2">
                  										<tr>
                    										<td class="smallText" valign="top"><? echo $ipn_split->display_count($ipn_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_IPN_TRANSACTIONS); ?></td>
                    										<td class="smallText" align="right"><? echo $ipn_split->display_links($ipn_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?></td>
                  										</tr>
                									</table>
                								</td>
              								</tr>
            							</table>
            						</td>
<?
	$heading = array();
  	$contents = array();
	
  	switch ($action) {
    	case 'new':
      		break;
    	case 'edit':
      		break;
    	case 'delete':
      		break;
    	default:
      		if (is_object($ipnInfo)) {
        		$heading[] = array('text' => '<b>' . TEXT_INFO_PAYPAL_IPN_HEADING.' #' . $ipnInfo->invoice . '</b>');
				
        		$contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_ORDERS, tep_get_all_get_params(array('ipnID', 'action')) . 'oID=' . $ipnInfo->orders_id .'&' . 'ipnID=' . $ipnInfo->invoice .'&action=edit' . '&referer=ipn') . '">' . tep_image_button('button_orders.gif', IMAGE_ORDERS) . '</a>');
        		$contents[] = array('text' => '<br>' . TABLE_HEADING_DATE_ADDED . ': '. tep_datetime_short($ipnInfo->date_added));
      		}
      		break;
  	}
	
  	if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
    	echo '            <td width="25%" valign="top">' . "\n";
		
    	$box = new box;
    	echo $box->infoBox($heading, $contents);
    	echo '            </td>' . "\n";
  	}
?>
          						</tr>
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
			<!-- body_text_eof //-->
  		</tr>
	</table>
	<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>