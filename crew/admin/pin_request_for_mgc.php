<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

error_reporting(E_ALL & ~E_NOTICE);

require_once('includes/configure.php');
require_once(DIR_WS_INCLUDES . 'database_tables.php');
require_once(DIR_WS_FUNCTIONS . 'database.php');
require_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');
require_once(DIR_WS_CLASSES . 'curl.php');
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'custom_product_code.php');
include_once(DIR_WS_CLASSES . 'slack_notification.php');

define('LOG_QTY_ADJUST', "Quantity Adjustment \n%s");
define('LOG_CDKEY_ID_STR', 'CD Key ID: %s');

tep_db_connect() or die('Unable to connect to database server!');

$log_object = new log_files('system');

$hash_key = MGC_PIN_REQUEST_SECRET_KEY;

$request_qty = 25;

$product_select_sql = " SELECT products_id, products_actual_quantity, products_quantity_order, products_model
                        FROM " . TABLE_PRODUCTS . "
                        WHERE products_model LIKE 'MGC_%'
                            AND products_status = 1
                            AND custom_products_type_id = 2";
$product_result_sql = tep_db_query($product_select_sql);

while ($product_row = tep_db_fetch_array($product_result_sql)) {
    if ((int) $product_row['products_actual_quantity'] <= (int) $product_row['products_quantity_order']) {
        $pin_currency = 'USD';
        $skuParts = explode('_', $product_row['products_model']);
        $pin_amount = (isset($skuParts[1]) && (float) $skuParts[1]) ? number_format((float) $skuParts[1], 2, '.', '') : 0;
        $exclusive = (isset($skuParts[2]) ? $skuParts[2] : "");

        if ($pin_amount) {
            $pin_request_date = date('Y-m-d H:i:s');

            $pin_request_data_array = array(
                'products_id' => $product_row['products_id'],
                'pin_reseller_id' => MGC_PIN_REQUEST_MERCANT_CODE,
                'products_quantity_order' => (int) $product_row['products_quantity_order'],
                'pin_request_qty' => $request_qty,
                'pin_currency' => $pin_currency,
                'pin_amount' => $pin_amount,
                'pin_request_date' => $pin_request_date
            );
            tep_db_perform(TABLE_PIN_REQUEST, $pin_request_data_array);
            $trans_id = tep_db_insert_id();

            $sha_string = base64_encode(sha1($hash_key . $trans_id . $pin_amount, true));
            $response = '';
            $pin_url = HTTPS_PIN_SERVER . "api_request_pin.php";

            $data_array = array(
                'mm_cmd' => 'GP_REQ',
                'tran_id' => $trans_id,
                'req' => array(
                    array(
                        'softpin_currency' => $pin_currency,
                        'softpin_amt' => $pin_amount,
                        'quantity' => $request_qty,
                        'reseller_id' => MGC_PIN_REQUEST_MERCANT_CODE,
                        'description' => 'Restock for ' . $pin_currency . ' ' . $pin_amount,
                        'sub_date' => $pin_request_date,
                        'hash_data' => urlencode($sha_string),
                        'exclusive' => $exclusive
                    )
                )
            );

            $json_string = json_encode(array('GP_REQUEST' => $data_array));

            $curl_obj = new curl();
            $response = $curl_obj->curl_post($pin_url, $json_string);
            $pin_result_array = json_decode($response, true);   // In array format

            if ($pin_result_array["tran_status"] != 1) {
                $_sql = "SELECT products_name FROM " . TABLE_PRODUCTS_DESCRIPTION . " WHERE products_id = " . $product_row['products_id'] . " AND language_id = 1";
                $_res = tep_db_query($_sql);
                $_row = tep_db_fetch_array($_res);

                $slack = new slack_notification();
                $data = json_encode(array(
                    'text' => '[OGCREW] MGC Pin Request API Result',
                    'attachments' => array(
                        array(
                            'color' => 'warning',
                            'text' => "Product : " . (isset($_row["products_name"]) ? $_row["products_name"] : $product_row['products_id']) . "\n" .
                            "Transaction ID : " . $trans_id . "\n" .
                            "Product ID : " . $product_row['products_id'] . "\n" .
                            "Qty Order : " . (int) $product_row['products_quantity_order'] . "\n" .
                            "Request Qty : " . $request_qty . "\n" .
                            "Response : " . json_encode($pin_result_array)
                        )
                    )
                ));
                $slack->send(SLACK_WEBHOOK_ANB, $data);
            }

            if ($pin_result_array['tran_id'] == $trans_id) {    // If match transaction id
                $pin_request_update_data_array = array(
                    'pin_module_trans_id' => $pin_result_array['offgamers_tran_id'],
                    'pin_receive_date' => $pin_result_array['process_date'],
                    'pin_request_status' => $pin_result_array['tran_status'],
                    'pin_request_message' => $pin_result_array['tran_errcode'],
                    'pin_request_log' => $json_string
                );
                tep_db_perform(TABLE_PIN_REQUEST, $pin_request_update_data_array, 'update', 'pin_request_id="' . $trans_id . '"');

                if ($pin_result_array['tran_status'] == 1) {
                    if (is_array($pin_result_array['req'])) {
                        $cdkey_array = array();

                        foreach ($pin_result_array['req'] as $pin_array) {
                            $key_filename = 'MGC_' . $trans_id . '_' . $pin_array['id'] . '.csv';

                            $key_string = "Serial: " . $pin_array['serial_number'] . "<br>" . "PIN: " . $pin_array['softpin_number'] . "<br>" . "Expiry: " . $pin_array['Info'];

                            $key_string = base64_encode($key_string);
                            $key_string = tep_encrypt_data($key_string);

                            // Insert new cd key record in On Hold status
                            $code_select_sql = sprintf('INSERT INTO ' . TABLE_CUSTOM_PRODUCTS_CODE . ' (products_id, status_id, file_name, file_type, code_date_added, code_date_modified, code_uploaded_by, remarks, purchase_orders_id) ' . '
                                                         VALUES ("%s", "%s", "%s", "%s", "' . $pin_request_date . '", "' . $pin_request_date . '", "system", "", "%s")', $product_row['products_id'], -2, $key_filename, 'soft', 0);
                            $code_result = tep_db_query($code_select_sql);

                            if ($code_result) {
                                $custom_products_code_id = tep_db_insert_id();

                                $pin_request_info_data_array = array(
                                    'pin_request_id' => $trans_id,
                                    'custom_products_code_id' => $custom_products_code_id,
                                    'pin_module_pin_id' => $pin_array['id']
                                );
                                tep_db_perform(TABLE_PIN_REQUEST_INFO, $pin_request_info_data_array);

                                $cdkey_array[] = $custom_products_code_id;

                                // Create physical file
                                $cpc_obj = new custom_product_code();
                                $cpc_obj->uploadCode($key_string, $custom_products_code_id, $product_row['products_id'], $pin_request_date);

                                // Move to Actual status and update Product quantity
                                $cdkey_status_update_sql = "UPDATE " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                                            SET status_id=1
                                                            WHERE custom_products_code_id = '" . $custom_products_code_id . "'";
                                tep_db_query($cdkey_status_update_sql);
                            }
                        }

                        // Update product qty
                        tep_pin_request_update_product_stock($product_row['products_id'], $cdkey_array);
                    }
                }
            } else {
                $pin_request_update_data_array = array(
                    'pin_module_trans_id' => $pin_result_array['offgamers_tran_id'],
                    'pin_receive_date' => $pin_result_array['process_date'],
                    'pin_request_status' => $pin_result_array['tran_status'],
                    'pin_request_message' => $pin_result_array['tran_errcode'],
                    'pin_request_log' => $json_string
                );
                tep_db_perform(TABLE_PIN_REQUEST, $pin_request_update_data_array, 'update', 'pin_request_id="' . $trans_id . '"');
            }
        }
    }
}

echo 'done';

function tep_pin_request_update_product_stock($product_id, $cdkey_id_array) {
    global $log_object;

    if (count($cdkey_id_array) > 0) {
        $offset = '+' . count($cdkey_id_array);

        $products_quantity_select_sql = "	SELECT products_quantity, products_actual_quantity 
                                            FROM " . TABLE_PRODUCTS . " 
                                            WHERE products_id = " . $product_id;
        $products_quantity_result_sql = tep_db_query($products_quantity_select_sql);

        if ($products_quantity_row = tep_db_fetch_array($products_quantity_result_sql)) {
            $old_prod_available_qty = $products_quantity_row['products_quantity'];
            $old_prod_actual_qty = $products_quantity_row['products_actual_quantity'];
            $new_prod_available_qty = $old_prod_available_qty + ($offset);
            $new_prod_actual_qty = $old_prod_actual_qty + ($offset);

            $product_sql = "UPDATE " . TABLE_PRODUCTS . " 
                            SET products_quantity=IF(products_quantity IS NULL, 0, products_quantity) $offset, 
                                products_actual_quantity=IF(products_actual_quantity IS NULL, 0, products_actual_quantity) $offset 
                            WHERE products_id = " . $product_id;
            $status = tep_db_query($product_sql);

            if ($status) {
                $log_object->insert_log((int) $product_id, 'products_quantity', $old_prod_available_qty . ':~:' . $old_prod_actual_qty, $new_prod_available_qty . ':~:' . $new_prod_actual_qty, sprintf(LOG_QTY_ADJUST, ''), sprintf(LOG_CDKEY_ID_STR, implode(', ', $cdkey_id_array)));
            }
        }
    }
}

?>