<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

error_reporting(E_ALL & ~E_NOTICE);

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'compatibility.php');

tep_db_connect() or die('Unable to connect to database server!');

$raw_data = file_get_contents("php://input");
$input_array = json_decode($raw_data, true);

if (isset($input_array['GP_REQUEST'])) {
    $pin_verify_array = $input_array['GP_REQUEST'];
    
    $pin_request_id = tep_db_prepare_input($pin_verify_array['tran_id']);
    $pin_currency = tep_db_prepare_input($pin_verify_array['req'][0]['softpin_currency']);
    $pin_amount = tep_db_prepare_input($pin_verify_array['req'][0]['softpin_amt']);
    $pin_qty = tep_db_prepare_input($pin_verify_array['req'][0]['quantity']);
    
    $pin_request_select_sql = " SELECT pin_request_qty, pin_currency, pin_amount
                                FROM " . TABLE_PIN_REQUEST . "
                                WHERE pin_request_id = '".tep_db_input($pin_request_id)."'";
    $pin_request_result_sql = tep_db_query($pin_request_select_sql);
    $pin_request_row = tep_db_fetch_array($pin_request_result_sql);
    
    $data_array = array (   'mm_cmd' => 'GP_VRF',
                            'tran_id' => $pin_request_id,
                            'process_date' => date('Y-m-d H:i:s')
                        );
    
    if (    $pin_request_row['pin_request_qty'] == $pin_qty &&
            $pin_request_row['pin_currency'] == $pin_currency &&
            $pin_request_row['pin_amount'] == $pin_amount ) {
        $data_array['tran_errcode'] = 'Verification Success!';
        $data_array['tran_status'] = '1';
    } else {
        $data_array['tran_errcode'] = 'Request data mismatch!';
        $data_array['tran_status'] = '2';
    }
    
    echo json_encode($data_array);
} else {
    $data_array['tran_errcode'] = 'Request data not found!';
    $data_array['tran_status'] = '3';

    echo json_encode($data_array);
}
?>