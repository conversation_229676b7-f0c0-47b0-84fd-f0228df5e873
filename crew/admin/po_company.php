<?php
/*
  	$Id: po_company.php,v 1.2 2012/06/07 04:31:49 chingyen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'general.php');

$insert_po_company_permission = true; // tep_admin_files_actions(FILENAME_PO_COMPANY, 'PO_NEW_COMPANY');
$edit_po_company_permission = true; // tep_admin_files_actions(FILENAME_PO_COMPANY, 'PO_EDIT_COMPANY');
$edit_po_company_status_permission = true; // tep_admin_files_actions(FILENAME_PO_COMPANY, 'PO_EDIT_STATUS_COMPANY');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

$error = false;
$error_section = "";

if ($action == "new_company") {
	if (!$insert_po_company_permission) {
		$messageStack->add_session(ERROR_PO_COMPANY_INSERT_PERMISSION, 'error');
		tep_redirect(tep_href_link(FILENAME_PO_COMPANY, 'page=' . $_GET['page']));
	}
}

if (tep_not_null($action)) {
	switch ($action) {
		case "set_company_status":
			if ( ($_REQUEST['flag'] == '0') || ($_REQUEST['flag'] == '1') ) {
          		if (isset($_REQUEST['cID'])) {
          			if ($edit_po_company_status_permission) {
          				tep_set_company_status($_REQUEST['cID'], $_REQUEST['flag']);
						
						// add action remark
						$remarks_data_array = array(
							'po_company_id' => $_REQUEST['cID'],
							'date_remarks_added' => 'now()',
							'remarks' => 'Change of Status',
							'remarks_added_by' => $_SESSION['login_id']
						);
						tep_db_perform(TABLE_PO_COMPANY_REMARKS_HISTORY, $remarks_data_array);
          			} else {
          				$messageStack->add_session(ERROR_PO_COMPANY_STATUS_EDIT, 'error');
          			}
          		}
        	}
        	tep_redirect(tep_href_link(FILENAME_PO_COMPANY, 'page=' . $_GET['page']));
        	
			break;
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
		case "insert_po_company":
		case "update_po_company":
			if ($insert_po_company_permission || $edit_po_company_permission) {
				$po_company_code = tep_db_prepare_input($_POST['po_company_code']);
				$po_company_name = tep_db_prepare_input($_POST['po_company_name']);
				$po_company_contact_name = tep_db_prepare_input($_POST['po_company_contact_name']);
				$po_company_status = tep_db_prepare_input($_POST['po_company_status']);
				$po_company_street_address = tep_db_prepare_input($_POST['po_company_street_address']);
				$po_company_suburb = tep_db_prepare_input($_POST['po_company_suburb']);
				$po_company_postcode = tep_db_prepare_input($_POST['po_company_postcode']);
				$po_company_city = tep_db_prepare_input($_POST['po_company_city']);
				$po_company_state = tep_db_prepare_input($_POST['po_company_state']);
				$po_company_country_id = tep_db_prepare_input($_POST['po_company_country_id']);
		    	$po_company_state = tep_db_prepare_input($_POST['po_company_state']);
				$po_company_telephone = tep_db_prepare_input($_POST['po_company_telephone']);
				$po_company_fax = tep_db_prepare_input($_POST['po_company_fax']);
				$po_company_gst_percentage = tep_db_prepare_input($_POST['po_company_gst_percentage']);
				$po_company_footer_text = tep_db_prepare_input($_POST['po_company_footer_text']);
	      		
	      		if (tep_not_null($po_company_code)) {
	      			$company_code_error = false;
	        	} else {
	          		$error = true;
		          	$company_code_error = true;
	        	}
				
				if ($company_code_error == false) {
					$check_company_code = tep_db_query("select po_company_code from " . TABLE_PO_COMPANY . " where po_company_code = '" . tep_db_input($po_company_code) . "'");
		      		if (tep_db_num_rows($check_company_code)) {
		        		$company_code_exists = true;
		      		} else {
		        		$company_code_exists = false;
		      		}
		      	}
			    
		        if (strlen($po_company_name) < ENTRY_FIRST_NAME_MIN_LENGTH) {
	          		$error = true;
	          		$company_name_error = true;
	        	} else {
	          		$company_name_error = false;
	        	}
				
	        	if (strlen($po_company_street_address) < ENTRY_STREET_ADDRESS_MIN_LENGTH) {
	          		$error = true;
	          		$entry_street_address_error = true;
	        	} else {
	          		$entry_street_address_error = false;
	        	}
	        	
	        	if (!tep_not_null($po_company_country_id)) {
	          		$error = true;
	          		$entry_country_error = true;
	        	} else {
	          		$entry_country_error = false;
					
	          		$address_format_query = tep_db_query("select address_format_id as format_id from " . TABLE_COUNTRIES . " where countries_id = '" . (int)$po_company_country_id . "'");
					if ($address_format = tep_db_fetch_array($address_format_query)) {
						$_POST['po_company_format_id'] = $address_format['format_id'];
					}
					
					$entry_state_error = true;
					$check_query = tep_db_query("select count(zone_country_id) as total from " . TABLE_ZONES . " where zone_country_id = '" . (int)$po_company_country_id . "'");
					$check_value = tep_db_fetch_array($check_query);
					$entry_state_has_zones = ($check_value['total'] > 0);

					if ($entry_state_has_zones == true) {
						$entry_state_error = true;
						$zone_query = tep_db_query("select zone_id, zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int)$po_company_country_id . "' and zone_id = '" . (int)$po_company_state . "'");
						if (tep_db_num_rows($zone_query) == 1) {
							$zone_values = tep_db_fetch_array($zone_query);
							$po_company_zone_id = $zone_values['zone_id'];
							$po_company_zone_name = $zone_values['zone_name'];
							$_POST['po_company_zone_id'] = $po_company_zone_id;
							$_POST['po_company_zone_name'] = $po_company_zone_name;
							$entry_state_error = false;
						} else {
							$error = true;
							$entry_state_error = true;
						}
					}
	        	}
	      		
	      		if (strlen($po_company_telephone) < ENTRY_TELEPHONE_MIN_LENGTH) {
	        		$error = true;
	        		$entry_telephone_error = true;
	      		} else {
	        		$entry_telephone_error = false;
	      		}
				
				if (tep_not_null($po_company_gst_percentage)) {
					if ($po_company_gst_percentage < 0 || $po_company_gst_percentage > 99) {
						$error = true;
						$entry_gst_error = true;
					} else {
						$entry_gst_error = false;
					}
				} else {
					$entry_gst_error = false;
				}
      			
	      		if (!$error) {
					if ($subaction == "insert_po_company") {
						$po_company_data_array = array(	'po_company_code' => tep_db_prepare_input($_POST['po_company_code']),
														'po_company_name' => tep_db_prepare_input($_POST['po_company_name']),
														'po_company_contact_name' => tep_db_prepare_input($_POST['po_company_contact_name']),
														'po_company_street_address' => tep_db_prepare_input($_POST['po_company_street_address']),
														'po_company_suburb' => tep_db_prepare_input($_POST['po_company_suburb']),
														'po_company_city' => tep_db_prepare_input($_POST['po_company_city']),
														'po_company_postcode' => tep_db_prepare_input($_POST['po_company_postcode']),
														'po_company_country_id' => tep_db_prepare_input($_POST['po_company_country_id']),
														'po_company_format_id' => tep_db_prepare_input($_POST['po_company_format_id']),
														'po_company_telephone' => tep_db_prepare_input($_POST['po_company_telephone']),
														'po_company_fax' => tep_db_prepare_input($_POST['po_company_fax']),
														'po_company_invoice_footer' => tep_db_prepare_input($_POST['po_company_invoice_footer']),
														'po_company_gst_percentage' => tep_db_prepare_input($_POST['po_company_gst_percentage'])
														);
						if ($_POST['po_company_zone_id'] > 0) {
							$po_company_data_array['po_company_zone_id'] = tep_db_prepare_input($_POST['po_company_zone_id']);
							$po_company_data_array['po_company_state'] = '';
						} else {
							$po_company_data_array['po_company_zone_id'] = '0';
							$po_company_data_array['po_company_state'] = tep_db_prepare_input($_POST['po_company_state']);
						}
						tep_db_perform(TABLE_PO_COMPANY, $po_company_data_array);

						$messageStack->add_session(sprintf(SUCCESS_PO_COMPANY_ADDED, $po_company_name), 'success');
						tep_redirect(tep_href_link(FILENAME_PO_COMPANY, tep_get_all_get_params(array('cID', 'action', 'subaction')) . '&page=' . $_REQUEST['page']));
						
					} else if ($subaction == "update_po_company") {
						$po_company_data_array = array(	'po_company_code' => tep_db_prepare_input($_POST['po_company_code']),
														'po_company_name' => tep_db_prepare_input($_POST['po_company_name']),
														'po_company_contact_name' => tep_db_prepare_input($_POST['po_company_contact_name']),
														'po_company_status' => tep_db_prepare_input($_POST['po_company_status']),
														'po_company_street_address' => tep_db_prepare_input($_POST['po_company_street_address']),
														'po_company_suburb' => tep_db_prepare_input($_POST['po_company_suburb']),
														'po_company_city' => tep_db_prepare_input($_POST['po_company_city']),
														'po_company_postcode' => tep_db_prepare_input($_POST['po_company_postcode']),
														'po_company_country_id' => tep_db_prepare_input($_POST['po_company_country_id']),
														'po_company_format_id' => tep_db_prepare_input($_POST['po_company_format_id']),
														'po_company_telephone' => tep_db_prepare_input($_POST['po_company_telephone']),
														'po_company_fax' => tep_db_prepare_input($_POST['po_company_fax']),
														'po_company_invoice_footer' => tep_db_prepare_input($_POST['po_company_invoice_footer']),
														'po_company_gst_percentage' => tep_db_prepare_input($_POST['po_company_gst_percentage'])
														);
						if ($_POST['po_company_zone_id'] > 0) {
							$po_company_data_array['po_company_zone_id'] = tep_db_prepare_input($_POST['po_company_zone_id']);
							$po_company_data_array['po_company_state'] = '';
						} else {
							$po_company_data_array['po_company_zone_id'] = '0';
							$po_company_data_array['po_company_state'] = tep_db_prepare_input($_POST['po_company_state']);
						}
						tep_db_perform(TABLE_PO_COMPANY, $po_company_data_array, 'update', "po_company_id = '" . (int)$_POST['po_company_id'] . "'");
						
						$messageStack->add_session(sprintf(SUCCESS_PO_COMPANY_UPDATED, $po_company_name), 'success');
						tep_redirect(tep_href_link(FILENAME_PO_COMPANY, tep_get_all_get_params(array('cID', 'action', 'subaction')) . '&page=' . $_REQUEST['page']));
					}
				} else {
					$error_section = 'info';
					
					if ($subaction == "update_po_company") {
						$messageStack->add_session(ERROR_PO_COMPANY_INVALID_DATA_INSERT, 'error');
						tep_redirect(tep_href_link(FILENAME_PO_COMPANY, 'action=edit_company&cID='.(int)$_POST['po_company_id'].'&page='.$_REQUEST["page"]));
					}
				}
			}
			break;
	}
	
	if ($error) {
		$sInfo = new objectInfo($_POST, false);
		$processed = true;
	}
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/customer_xmlhttp.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
            			<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
<?
if ($action == "new_company") {
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
	if (($insert_po_company_permission) && (!$error || $error_section=='info')) {
?>
					<tr>
        				<td width="100%">
<?	echo tep_draw_form('po_company_form', FILENAME_PO_COMPANY, tep_get_all_get_params(array('subaction','sID')) . 'subaction=insert_po_company', 'post', 'onSubmit="return company_check_form();"'); ?>
        					<fieldset class="selectedFieldSet">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="customerFormAreaTitle"><?=CATEGORY_ACCOUNT?></td>
								</tr>
								<tr>
									<td class="formArea">
										<table border="0" width="100%" cellspacing="2" cellpadding="2">
											<tr>
												<td class="main" width="20%"><?=ENTRY_PO_COMPANY_CODE?></td>
												<td class="main">
<?		if ($error == true) {
	    	if ($company_code_error == true) {
	      		echo tep_draw_input_field('po_company_code', $sInfo->po_supplier_code, 'maxlength="2" id="po_company_code"') . '&nbsp;' . ENTRY_COMPANY_CODE_ERROR;
			} else if ($company_code_exists == true) {
	      		echo tep_draw_input_field('po_company_code', $sInfo->po_supplier_code, 'maxlength="2" id="po_company_code"') . '&nbsp;' . ENTRY_COMPANY_CODE_EXIST_ERROR;
	    	} else {
	      		echo $sInfo->po_company_code . tep_draw_hidden_field('po_company_code');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_company_code', $sInfo->po_company_code, 'maxlength="2" id="po_company_code"', true);
	  	}
?>
												</td>
											</tr>
											<tr>
												<td class="main" width="20%"><?=ENTRY_PO_COMPANY_NAME?></td>
												<td class="main">
<?		if ($error == true) {
	    	if ($company_name_error == true) {
	      		echo tep_draw_input_field('po_company_name', $sInfo->po_company_name, 'id="po_company_name" size="32"') . '&nbsp;' . ENTRY_COMPANY_NAME_ERROR;
	    	} else {
	      		echo $sInfo->po_company_name . tep_draw_hidden_field('po_company_name');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_company_name', $sInfo->po_company_name, 'maxlength="32" size="32" id="po_company_name"', true);
	  	}
?>
												</td>
											</tr>
											<tr>
												<td class="main" width="20%"><?=ENTRY_PO_COMPANY_CONTACT_NAME?></td>
												<td class="main">
<?
	    echo tep_draw_input_field('po_company_contact_name', $sInfo->po_company_contact_name, 'maxlength="64" size="64" id="po_company_contact_name"');
?>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_ADDRESS?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				          					<tr>
				            					<td class="main" width="20%"><?=ENTRY_STREET_ADDRESS?></td>
				            					<td class="main">
<?		if ($error == true) {
	  		if ($entry_street_address_error == true) {
	   			echo tep_draw_input_field('po_company_street_address', $sInfo->po_company_street_address, 'maxlength="64" size="64" id="po_company_street_address"') . '&nbsp;' . ENTRY_STREET_ADDRESS_ERROR;
	  		} else {
	      		echo $sInfo->po_company_street_address . tep_draw_hidden_field('po_company_street_address');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_company_street_address', $sInfo->po_company_street_address, 'maxlength="64" size="64" id="po_company_street_address"', true);
	  	}
?>
												</td>
			          						</tr>
<?		if (ACCOUNT_SUBURB == 'true') { ?>
			          						<tr>
			            						<td class="main"><?=ENTRY_SUBURB?></td>
			            						<td class="main">
<?
	      		echo tep_draw_input_field('po_company_suburb', $sInfo->po_company_suburb, 'maxlength="32" size="32" id="po_company_suburb"');
?>
												</td>
          									</tr>
<?		} ?>
			          						<tr>
				            					<td class="main"><?=ENTRY_POST_CODE?></td>
				            					<td class="main">
<?
	    	echo tep_draw_input_field('po_company_postcode', $sInfo->po_company_postcode, 'maxlength="10" size="10" id="po_company_postcode"');
?>
												</td>
				          					</tr>
				          					<tr>
	            								<td class="main"><?=ENTRY_CITY?></td>
	            								<td class="main">
<?
	   		echo tep_draw_input_field('po_company_city', $sInfo->po_company_city, 'maxlength="32" size="32" id="po_company_city"');
?>
												</td>
          									</tr>
          									<tr>
				            					<td class="main"><?=ENTRY_COUNTRY?></td>
				            					<td class="main">
<?
  		if ($error == true) {
		 	if ($entry_country_error == false) {
	   			echo tep_get_country_name($sInfo->po_company_country_id) . tep_draw_hidden_field('po_company_country_id');
	   		} else {
	   			echo tep_get_country_list('po_company_country_id') . '&nbsp;' . '<span class="requiredInfo">' . ENTRY_COUNTRY_ERROR . '</span>';
	   		}
		} else {
			echo tep_get_country_list('po_company_country_id', $sInfo->po_company_country_id, ' id="po_company_country_id" onChange="refreshDynamicSelectOptions(this, \'state_div\', \'po_company_state\', \''.(int)$languages_id.'\', true);"') . '&nbsp;' . (tep_not_null(ENTRY_COUNTRY_TEXT) ? '<span class="requiredInfo">' . ENTRY_COUNTRY_ERROR . '</span>': '');
	  	}
?>
												</td>
	          								</tr>
<?		if (ACCOUNT_STATE == 'true') { ?>
				          					<tr>
				            					<td class="main"><?=ENTRY_STATE?></td>
				            					<td class="main">
				            						<div id="state_div" style="float:left;">
<?
	    	$zones_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
	    	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$sInfo->po_company_country_id . "' ORDER BY zone_name";
	    	$zones_result_sql = tep_db_query($zones_select_sql);
	    	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
	      		$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
	    	}
	    	if ($error == true) {
	    		if ($entry_state_error == true) {
					if ($entry_state_has_zones == true) {
		    			echo tep_draw_pull_down_menu('po_company_state', $zones_array, $sInfo->po_company_zone_id). '&nbsp;' . ENTRY_STATE_ERROR;
		    		} else {	    		
		    			echo tep_draw_input_field('po_company_state', $sInfo->po_company_state) . '&nbsp;' . ENTRY_STATE_ERROR;
		    		}
		    	} else {
					if (count($zones_array) > 1) {
						echo tep_draw_pull_down_menu('po_company_state', $zones_array, $sInfo->po_company_zone_id, 'id="po_company_state"');
					} else {
						echo tep_draw_input_field('po_company_state', $sInfo->po_company_state, 'id="po_company_state" size="32"');
					}
		    	}
	    	} else {
	    		if (count($zones_array) > 1) {
	    			echo tep_draw_pull_down_menu('po_company_state', $zones_array, $sInfo->po_company_zone_id, 'id="po_company_state"');
	      		} else {
		    	    echo tep_draw_input_field('po_company_state', $sInfo->po_company_state, 'id="po_company_state" size="32"');
				}
			}
			echo '									</div>';
?>
												</td>
         									</tr>
<?		} ?>
										</table>
									</td>
          						</tr>
          						<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_CONTACT?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				          					<tr>
				            					<td class="main" width="20%"><?=ENTRY_TELEPHONE_NUMBER?></td>
				            					<td class="main">
<?		if ($error == true) {
	    	if ($entry_telephone_error == true) {
	     		echo tep_draw_input_field('po_company_telephone', $sInfo->po_company_telephone, 'maxlength="32" size="32" id="po_company_telephone"') . '&nbsp;' . ENTRY_TELEPHONE_NUMBER_ERROR;
	    	} else {
	     		echo $sInfo->po_company_telephone . tep_draw_hidden_field('po_company_telephone');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_company_telephone', $sInfo->po_company_telephone, 'maxlength="32" size="32" id="po_company_telephone"', true);
	  	}
?>
												</td>
			  								</tr>
			  								<tr>
				            					<td class="main"><?=ENTRY_FAX_NUMBER?></td>
				            					<td class="main">
<?
	    	echo tep_draw_input_field('po_company_fax', $sInfo->po_company_fax, 'maxlength="32" size="32" id="po_company_fax"');
?>
												</td>
	          								</tr>
										</table>
									</td>
								</tr>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_GST?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				          					<tr>
				            					<td class="main" width="20%"><?=ENTRY_PO_COMPANY_GST?></td>
				            					<td class="main">
<?
	    	echo tep_draw_input_field('po_company_gst_percentage', $sInfo->po_company_gst_percentage, 'maxlength="2" size="10" id="po_company_gst_percentage"') . '&nbsp;%';
?>
												</td>
			  								</tr>
										</table>
									</td>
								</tr>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_PO_FOOTER_TEXT?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				        					<tr>
				            					<td class="main" width="20%" valign="top"><?=ENTRY_PO_COMPANY_PO_FOOTER_TEXT?></td>
				            					<td class="main">
<?													echo tep_draw_textarea_field('po_company_invoice_footer', 'soft', '100', '4', ''); ?>
												</td>
			  								</tr>
				            			</table>
				        			</td>
				      			</tr>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
				        			<td align="right" class="main"><?=tep_button(BUTTON_INSERT, ALT_BUTTON_INSERT, '', ' name="insert" onClick="javascript:document.po_company_form.submit()"', 'inputButton')."&nbsp;&nbsp;&nbsp;".tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_COMPANY, tep_get_all_get_params(array('action', 'subaction', 'cID'))), '', 'inputButton')?></td>
				      			</tr>
				      		</table>
				      		</form>
				      		</fieldset>
				      	</td>
					</tr>
<?	} ?>
	      			<tr>
	      				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
<script language="javascript">
<!--
		function company_check_form() {
			var error = 0;
		  	var error_message = "<?php echo JS_ERROR; ?>";
		  	
		  	var po_company_code = DOMCall('po_company_code');
		  	var po_company_name = DOMCall('po_company_name');
		  	var po_company_street_address = DOMCall('po_company_street_address');
		  	var po_company_postcode = DOMCall('po_company_postcode');
		  	var po_company_city = DOMCall('po_company_city');
		  	var po_company_country_id = DOMCall('po_company_country_id');
			var po_company_state = DOMCall('po_company_state');
		  	var po_company_telephone = DOMCall('po_company_telephone');
			
		  	if (po_company_code != null) {
			  	if (po_company_code.value.length < 1) {
			    	error_message = error_message + "<?php echo JS_PO_COMPANY_CODE; ?>";
			    	error = 1;
			  	}
			}
		  	
			if (po_company_name != null) {
			  	if (po_company_name.value.length < <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_FIRST_NAME; ?>";
			    	error = 1;
			  	}
			}
			
			if (po_company_street_address != null) {
			  	if (po_company_street_address.value.length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_ADDRESS; ?>";
			    	error = 1;
			  	}
			}
			
			if (po_company_postcode != null) {
			  	if (po_company_postcode.value.length < <?php echo ENTRY_POSTCODE_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_POST_CODE; ?>";
			    	error = 1;
			  	}
			}
			
			if (po_company_city != null) {
			  	if (po_company_city.value.length < <?php echo ENTRY_CITY_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_CITY; ?>";
			    	error = 1;
			  	}
			}
 			
			if (po_company_country_id.value == '') {
				error_message = error_message + "<?php echo JS_COUNTRY; ?>";
		      	error = 1;
		    } else {
		<?		if (ACCOUNT_STATE == 'true') { ?>
					if(po_company_state != null) {
				  		if (po_company_state.value == '') {
				       		error_message = error_message + "<?php echo JS_STATE; ?>";
				       		error = 1;
				    	}
				    }
		<?		} ?>
		    }
		  	
		  	if (po_company_telephone != null) {
			  	if (po_company_telephone.value.length < <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_TELEPHONE; ?>";
			    	error = 1;
			  	}
			}
			
		  	if (error == 1) {
		    	alert(error_message);
		    	return false;
		  	} else {
		    	return true;
		  	}
		}
//-->
</script>
<?
} else if ($action == "edit_company") {
	$po_company_select_sql = "SELECT * FROM " . TABLE_PO_COMPANY . " WHERE po_company_id='".tep_db_input($_REQUEST['cID'])."'";
	$po_company_result_sql = tep_db_query($po_company_select_sql);
	if ($po_company_row = tep_db_fetch_array($po_company_result_sql)) {
		$sInfo = new objectInfo($po_company_row, false);
	}
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
        				<td width="100%">
							<?=tep_draw_form('po_company_form', FILENAME_PO_COMPANY, tep_get_all_get_params(array('subaction','cID')) . 'subaction=update_po_company', 'POST'); ?>
<script language="javascript">
<!--
function po_form_check() {
	document.po_company_form.submit();
	return true;
}
//-->
</script>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="customerFormAreaTitle"><?=CATEGORY_ACCOUNT?></td>
								</tr>
								<tr>
									<td class="formArea">
										<table border="0" width="100%" cellspacing="2" cellpadding="2">
											<tr>
												<td class="main" width="20%"><?=ENTRY_PO_COMPANY_ID?></td>
												<td class="main">
<?										      		echo $sInfo->po_company_id . tep_draw_hidden_field('po_company_id', $sInfo->po_company_id); ?>
												</td>
											</tr>
											<tr>
												<td class="main" width="20%"><?=ENTRY_PO_COMPANY_CODE?></td>
												<td class="main">
<?										      		echo $sInfo->po_company_code . tep_draw_hidden_field('po_company_code', $sInfo->po_company_code); ?>
												</td>
											</tr>
											<tr>
												<td class="main" width="20%"><?=ENTRY_PO_COMPANY_NAME?></td>
												<td class="main">
<?										      		echo tep_draw_input_field('po_company_name', $sInfo->po_company_name, 'maxlength="32" size="32" id="po_company_name"'); ?>
												</td>
											</tr>
											<tr>
												<td class="main" width="20%"><?=ENTRY_PO_COMPANY_CONTACT_NAME?></td>
												<td class="main">
<?										      		echo tep_draw_input_field('po_company_contact_name', $sInfo->po_company_contact_name, 'maxlength="64" size="64" id="po_company_contact_name"'); ?>
												</td>
											</tr>
											<tr>
												<td class="main" width="20%"><?=ENTRY_PO_COMPANY_STATUS?></td>
												<td class="main" valign="top" colspan="2">
<?													echo tep_draw_radio_field('po_company_status', '1', false, $sInfo->po_company_status) . '&nbsp;&nbsp;' . ACTIVE . '&nbsp;&nbsp;' . tep_draw_radio_field('po_company_status', '0', ($sInfo->po_company_status=='0'? true:false), $sInfo->po_company_status) . '&nbsp;&nbsp;' . NOT_ACTIVE; ?>
												</td>
											</tr>
						      				<tr>
							        			<td colspan="2" align="right" class="main"><?=(($edit_po_company_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check();"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_COMPANY, tep_get_all_get_params(array('action', 'subaction', 'cID'))), '', 'inputButton')?></td>
							      			</tr>
										</table>
									</td>
								</tr>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_ADDRESS?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				          					<tr>
				            					<td class="main" width="20%"><?=ENTRY_STREET_ADDRESS?></td>
				            					<td class="main">
<?													echo tep_draw_input_field('po_company_street_address', $sInfo->po_company_street_address, 'maxlength="64" size="64" id="po_company_street_address"'); ?>
												</td>
			          						</tr>
			          						<tr>
			            						<td class="main"><?=ENTRY_SUBURB?></td>
			            						<td class="main">
<?													echo tep_draw_input_field('po_company_suburb', $sInfo->po_company_suburb, 'maxlength="32" size="32" id="po_company_suburb"'); ?>
												</td>
          									</tr>
			          						<tr>
				            					<td class="main"><?=ENTRY_POST_CODE?></td>
				            					<td class="main">
<?													echo tep_draw_input_field('po_company_postcode', $sInfo->po_company_postcode, 'maxlength="10" size="10" id="po_company_postcode"'); ?>
												</td>
				          					</tr>
				          					<tr>
	            								<td class="main"><?=ENTRY_CITY?></td>
	            								<td class="main">
<?													echo tep_draw_input_field('po_company_city', $sInfo->po_company_city, 'maxlength="32" size="32" id="po_company_city"'); ?>
												</td>
          									</tr>
          									<tr>
				            					<td class="main"><?=ENTRY_COUNTRY?></td>
				            					<td class="main">
<?													echo tep_get_country_list('po_company_country_id', $sInfo->po_company_country_id, ' id="po_company_country_id" onChange="refreshDynamicSelectOptions(this, \'state_div\', \'po_company_state\', \''.(int)$languages_id.'\', true);"'); ?>
												</td>
	          								</tr>
				          					<tr>
				            					<td class="main"><?=ENTRY_STATE?></td>
				            					<td class="main">
				            						<div id="state_div" style="float:left;">
<?
	    	$zones_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
	    	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$sInfo->po_company_country_id . "' ORDER BY zone_name";
	    	$zones_result_sql = tep_db_query($zones_select_sql);
	    	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
	      		$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
	    	}
			if (count($zones_array) > 1) {
				echo tep_draw_pull_down_menu('po_company_state', $zones_array, $sInfo->po_company_zone_id, 'id="po_company_state"');
			} else {
				echo tep_draw_input_field('po_company_state', $sInfo->po_company_state, 'id="po_company_state" size="32"');
			}			
			echo '									</div>';
?>
												</td>
         									</tr>
						      				<tr>
							        			<td colspan="2" align="right" class="main"><?=(($edit_po_company_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check();"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_COMPANY, tep_get_all_get_params(array('action', 'subaction', 'cID'))), '', 'inputButton')?></td>
							      			</tr>
				            			</table>
				        			</td>
				      			</tr>
          						<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_CONTACT?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				          					<tr>
				            					<td class="main" width="20%"><?=ENTRY_TELEPHONE_NUMBER?></td>
				            					<td class="main">
<?													echo tep_draw_input_field('po_company_telephone', $sInfo->po_company_telephone, 'maxlength="32" size="32" id="po_company_telephone"'); ?>
												</td>
			  								</tr>
			  								<tr>
				            					<td class="main"><?=ENTRY_FAX_NUMBER?></td>
				            					<td class="main">
<?													echo tep_draw_input_field('po_company_fax', $sInfo->po_company_fax, 'maxlength="32" size="32" id="po_company_fax"'); ?>
												</td>
	          								</tr>
						      				<tr>
							        			<td colspan="2" align="right" class="main"><?=(($edit_po_company_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check();"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_COMPANY, tep_get_all_get_params(array('action', 'subaction', 'cID'))), '', 'inputButton')?></td>
							      			</tr>
										</table>
									</td>
								</tr>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_GST?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				        					<tr>
				            					<td class="main" width="20%" valign="top"><?=ENTRY_PO_COMPANY_GST?></td>
				            					<td class="main">
<?													echo tep_draw_input_field('po_company_gst_percentage', $sInfo->po_company_gst_percentage, 'maxlength="2" size="10" id="po_company_gst_percentage"').'&nbsp;%'; ?>
												</td>
			  								</tr>
						      				<tr>
							        			<td colspan="2" align="right" class="main"><?=(($edit_po_company_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check();"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_COMPANY, tep_get_all_get_params(array('action', 'subaction', 'cID'))), '', 'inputButton')?></td>
							      			</tr>
				            			</table>
				        			</td>
				      			</tr>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_PO_FOOTER_TEXT?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				        					<tr>
				            					<td class="main" width="20%" valign="top"><?=ENTRY_PO_COMPANY_PO_FOOTER_TEXT?></td>
				            					<td class="main">
<?													echo tep_draw_textarea_field('po_company_invoice_footer', 'soft', '100', '4', $sInfo->po_company_invoice_footer); ?>
												</td>
			  								</tr>
						      				<tr>
							        			<td colspan="2" align="right" class="main"><?=(($edit_po_company_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check();"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_COMPANY, tep_get_all_get_params(array('action', 'subaction', 'cID'))), '', 'inputButton')?></td>
							      			</tr>
				            			</table>
				        			</td>
				      			</tr>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
				      		</table>
				      		</form>
				      	</td>
					</tr>
	      			<tr>
	      				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
<?
} else {  /* LISTING PAGE */
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<? if ($insert_po_company_permission) { ?>
					<tr>
						<td>
							[<a href="<?=tep_href_link(FILENAME_PO_COMPANY, 'action=new_company')?>" target="_blank">Add PO Company</a>]
						</td>
					</tr>
<? } ?>
<?
	
	$po_company_select_sql = "	select pc.* 
							    from " . TABLE_PO_COMPANY . " as pc 
								order by pc.po_company_id";
	
	$page_split_object = new splitPageResults($_REQUEST["page"], MAX_DISPLAY_SEARCH_RESULTS, $po_company_select_sql, $sql_numrows);
	$po_company_result_sql = tep_db_query($po_company_select_sql);

	// PO Company status icon settings
	$po_company_status_array = array(
		'1'=> array('name' => 'icon_status_green', 'alt' => IMAGE_ICON_STATUS_GREEN, 'alt2' => IMAGE_ICON_STATUS_GREEN_LIGHT),
		'0'=> array('name' => 'icon_status_red', 'alt' => IMAGE_ICON_STATUS_RED, 'alt2' => IMAGE_ICON_STATUS_RED_LIGHT)
	);
?>
					<tr>
						<td valign="top">
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_ID?></td>
									<td valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_CODE?></td>
									<td valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_NAME?></td>
									<td valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_STREET_ADDRESS?></td>
									<td valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_SUBURB?></td>
									<td valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_CITY?></td>
									<td valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_POSTCODE?></td>
									<td valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_STATE?></td>
									<td valign="top" align="left" class="reportBoxHeading" nowrap><?=TABLE_HEADING_PO_COMPANY_COUNTRY?></td>
									<td valign="top" align="left" class="reportBoxHeading" nowrap><?=TABLE_HEADING_PO_COMPANY_TEL?></td>
									<td valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_FAX?></td>
									<td valign="top" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_GST?></td>
									<td valign="top" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PO_COMPANY_STATUS?></td>
									<td width="3%" valign="top" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
								</tr>
<?							echo tep_draw_form('po_company_list_form', FILENAME_PO_COMPANY, tep_get_all_get_params(array('subaction')), 'post', ''); ?>
<?
	if (tep_db_num_rows($po_company_result_sql)) {
		$row_count = 0;
		while ($po_company_row = tep_db_fetch_array($po_company_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
			
			$po_company_id = $po_company_row["po_company_id"];
			
			$zone_name = "";
			if (tep_not_null($po_company_row["po_company_state"])) {
				$zone_name = $po_company_row["po_company_state"];
			} else {
				$zone_query = tep_db_query("select zone_id, zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int)$po_company_row['po_company_country_id'] . "' and zone_id = '" . (int)$po_company_row['po_company_zone_id'] . "'");
				if ($zone_values = tep_db_fetch_array($zone_query)) {
					$zone_name = $zone_values['zone_name'];
				}
			}
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top"><?=$po_company_row["po_company_id"]?></td>
									<td class="reportRecords" valign="top"><?=$po_company_row["po_company_code"]?></td>
									<td class="reportRecords" valign="top"><?=$po_company_row["po_company_name"]?></td>
									<td class="reportRecords" valign="top"><?=$po_company_row["po_company_street_address"]?></td>
									<td class="reportRecords" valign="top"><?=$po_company_row["po_company_suburb"]?></td>
									<td class="reportRecords" valign="top"><?=$po_company_row['po_company_city']?></td>
									<td class="reportRecords" valign="top"><?=$po_company_row["po_company_postcode"]?></td>
									<td class="reportRecords" valign="top"><?=$zone_name?></td>
									<td class="reportRecords" valign="top" nowrap><?=tep_get_country_name($po_company_row['po_company_country_id'])?></td>
									<td class="reportRecords" valign="top" nowrap><?=$po_company_row['po_company_telephone']?></td>
									<td class="reportRecords" valign="top"><?=$po_company_row["po_company_fax"]?></td>
									<td class="reportRecords" align="center" valign="top" nowrap><?=$po_company_row['po_company_gst_percentage']?></td>
									<td class="reportRecords" align="center" valign="top" nowrap>
									<?
										foreach ($po_company_status_array as $status_id => $img_res) {
											if ((int)$po_company_row["po_company_status"] == (int)$status_id) {
												echo tep_image(DIR_WS_IMAGES . $img_res['name'] . '.gif', $img_res['alt'], 10, 10) . '&nbsp;&nbsp;';
											} else {
												echo '<a href="' . tep_href_link(FILENAME_PO_COMPANY, 'action=set_company_status&flag='.(int)$status_id.'&cID='.$po_company_id.'&page='.$_REQUEST["page"]) . '">' . tep_image(DIR_WS_IMAGES . $img_res['name'] . '_light.gif', $img_res['alt2'], 10, 10) . '</a>&nbsp;&nbsp;';
											}
										}
									?>
									</td>
									<td class="reportRecords" valign="top" nowrap>&nbsp;
										<a href="<?=tep_href_link(FILENAME_PO_COMPANY, 'action=edit_company&cID='.$po_company_id.'&page='.$_REQUEST["page"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit PO Company", "", "", 'align="top"')?></a>
									</td>
								</tr>
<?
			$row_count++;
		}
	}
?>
							</form>
							</table>
			   			</td>
			   		</tr>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td class="smallText" valign="top" NOWRAP><?=$page_split_object->display_count($sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_PO_COMPANY)?></td>
									<td class="smallText" align="right"><?=$page_split_object->display_links($sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_REQUEST['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont'))."cont=1")?></td>
								</tr>
							</table>
						</td>
					</tr>
<? } /* LISTING PAGE */ ?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>