<?php
/*
  	$Id: po_payment.php,v 1.2 2011/07/08 07:50:47 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'payment_methods.php');
require(DIR_WS_CLASSES . 'payment_module_info.php');
require(DIR_WS_CLASSES . 'po_suppliers.php');
require(DIR_WS_CLASSES . 'edit_purchase_orders.php');
require(DIR_WS_CLASSES . 'dtu_payment.php');
require(DIR_WS_CLASSES . 'consignment_payment.php');

$currencies = new currencies();
$po_supplier = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);

$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

if (tep_not_null($subaction)) {
	switch ($subaction) {
		case "insert_po_payment":
			$error = false;
			
			if (!isset($_REQUEST['confirm_po_payment_supplier']) || !tep_not_null($_REQUEST['confirm_po_payment_supplier'])) {
				$messageStack->add_session(ERROR_PO_PAYMENT_PO_SUPPLIER, 'error');
				$error = true;
			}
			
			if (!isset($_REQUEST['confirm_po_payment_currency']) || !tep_not_null($_REQUEST['confirm_po_payment_currency'])) {
				$messageStack->add_session(ERROR_PO_PAYMENT_PO_CURRENCY, 'error');
				$error = true;
			}
			
			if (!isset($_REQUEST['confirm_po_payment_method']) || !tep_not_null($_REQUEST['confirm_po_payment_method'])) {
				$messageStack->add_session(ERROR_PO_PAYMENT_PO_DISBURSEMENT, 'error');
				$error = true;
			}
			
			if (!isset($_REQUEST['confirm_pay_po_batch']) || !tep_not_null($_REQUEST['confirm_pay_po_batch'])) {
				$messageStack->add_session(ERROR_PO_PAYMENT_PO_LIST, 'error');
				$error = true;
			}
			
			if (!$error) {
				$po_supplier->insert_po_payment($_REQUEST, $messageStack);
			}
			
			tep_redirect(tep_href_link(FILENAME_PO_PAYMENT, tep_get_all_get_params(array('action', 'subaction'))));
			break;
	}
}

$po_suppliers_array = $po_supplier->get_po_supplier_with_wsc();

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/ogm_jquery.js"></script>
<?	include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); ?>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<div id="fancy_box" class="fancy_box" style="display:none;">
	<div class="fancy_close_footer" style="display: none;" onclick="hideFancyBox();"></div>
	<div class="fancy_inner" style="display: block;">
		<div id="fancy_close" class="fancy_close" style="display: none;"></div>
		<div class="fancy_frame_bg">
			<div class="fancy_bg fancy_bg_n"></div>
			<div class="fancy_bg fancy_bg_ne"></div>
			<div class="fancy_bg fancy_bg_e"></div>
			<div class="fancy_bg fancy_bg_se"></div>
			<div class="fancy_bg fancy_bg_s"></div>
			<div class="fancy_bg fancy_bg_sw"></div>
			<div class="fancy_bg fancy_bg_w"></div>
			<div class="fancy_bg fancy_bg_nw"></div>
		</div>
		<div id="fancy_content" class="fancy_content"></div>
	</div>
</div>
<div id="fancy_box_Bg" class="theLayerBg" style="display:none;"></div>

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
				<table border="0" width="65%" cellspacing="0" cellpadding="2">
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>
							<?=tep_draw_form('po_payment_form_preview', FILENAME_PO_PAYMENT, tep_get_all_get_params(array('action','subaction')) . 'subaction=preview_po_payment', 'post', 'id="po_payment_form_preview" onSubmit="return po_payment_check_form();"')?>
							<table border="0" width="100%" cellspacing="0" cellpadding="6">
								<tr>
									<td align="left" valign="top" class="main" width="40%"><?=ENTRY_SUPPLIER?></td>
									<td align="left" class="main"><table border="0" width="100%" cellspacing="0" cellpadding="0"><tr><td class="main"><?=tep_draw_pull_down_menu('po_payment_supplier', $po_suppliers_array, $po_payment_supplier, 'id="po_payment_supplier" onChange="getPOPayCurrency(this);"')?></td><td class="main"><div id="po_payment_supplier_lock_div"></div></td></tr></table></td>
								</tr>
								<tr>
									<td align="left" valign="top" class="main"><?=ENTRY_CURRENCY?></td>
									<td align="left" class="main"><div id="po_payment_currency_div"><?=TEXT_SELECT_PO_SUPPLIER?></div></td>
								</tr>
								<tr>
									<td align="left" valign="top" class="main"><?=ENTRY_AVAIL_AMOUNT?></td>
									<td align="left" class="main"><div id="po_payment_avail_amount_div"><?=TEXT_ZERO_AMOUNT?></div></td>
								</tr>
								<tr>
									<td align="left" valign="top" class="main"><?=ENTRY_DISBURSEMENT_METHOD?></td>
									<td align="left" class="main"><div id="po_payment_method_div"><?=TEXT_SELECT_CURRENCY?></div></td>
								</tr>
								<tr>
									<td align="left" valign="top" class="main" valign="top"><?=ENTRY_DISBURSE_FOR_PO?></td>
									<td align="left" class="main">
										<div  style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000; border-bottom: 1px solid #000000">
											<div id="po_list_div">
												<table border="0" width="100%" cellspacing="1" cellpadding="4">
													<tr>
														<td class="infoBoxHeading"><?=TABLE_HEADING_PO_SELECT?></td>
														<td class="infoBoxHeading"><?=TABLE_HEADING_PO_PO_REF_NO?></td>
														<td class="infoBoxHeading"><?=TABLE_HEADING_PO_PO_AMOUNT?></td>
													</tr>
												</table>
											</div>
										</div>
									</td>
								</tr>
								<tr>
									<td align="left" class="main"><?=ENTRY_TOTAL?></td>
									<td align="left" class="main">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
											<tr>
												<td class="main" width="80%"></td>
												<td class="main" width="10%" align="right"><div id="withdraw_currency_left_div"></div></td>
												<td class="main" align="right"><div id="withdraw_total_div">0.00</div></td>
												<td class="main" width="10%" align="left"><div id="withdraw_currency_right_div"></div></td>
											</tr>
										</table>
										<?=tep_draw_hidden_field('withdraw_total', '0', 'id="withdraw_total"')?>
										<?=tep_draw_hidden_field('po_payment_type', '', 'id="po_payment_type"')?>
									</td>
								</tr>
								<tr>
									<td align="left" class="main" colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td align="left">&nbsp;</td>
									<td align="left"><?=tep_button(BUTTON_PREVIEW, ALT_BUTTON_PREVIEW, '', ' name="confirm" onClick="show_disbursement_details()"', 'inputButton')?>&nbsp;&nbsp;<?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_PAYMENT, tep_get_all_get_params(array('action', 'subaction'))), '', 'inputButton')?></td>
								</tr>
							</table>
						</form>
						</td>
					</tr>
				</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<script language="javascript">
<!--
function po_payment_check_form() {
   	return true;
}
//-->
</script>

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>