<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml; charset=utf-8');

include_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'po_suppliers.php');
include_once(DIR_WS_CLASSES . 'edit_purchase_orders.php');

$currencies = new currencies();

$languages_id = isset($_SESSION['languages_id']) ? $_SESSION['languages_id'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : '';

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$customer_id = isset($_REQUEST['customer_id']) ? (int)$_REQUEST['customer_id'] : '';

if (file_exists(DIR_WS_LANGUAGES . $language . ".php")) {
	include_once(DIR_WS_LANGUAGES . $language . ".php");
}
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PO_PAYMENT)) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PO_PAYMENT);
}
if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PO_SUPPLIERS_LIST)) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_PO_SUPPLIERS_LIST);
}

$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);

echo '<response>';
if (tep_not_null($action)) {
	switch($action) {
		case "get_po_supplier_payments_statistic" :
			$statistic_data = $po_suppliers->get_po_supplier_stock_statistic($_REQUEST['suppID']);
			echo "<stock_received_pending_payment><![CDATA[".$statistic_data['stock_pending_payment']."]]></stock_received_pending_payment>";
			echo "<payment_sent_pending_stock><![CDATA[".$statistic_data['payment_pending_stock']."]]></payment_sent_pending_stock>";
			echo "<net_outstanding><![CDATA[".$statistic_data['net_outstanding']."]]></net_outstanding>";
			echo "<total_po_pending_payment><![CDATA[".$statistic_data['po_pending_payment']."]]></total_po_pending_payment>";
			echo "<supplier_credit><![CDATA[".$statistic_data['supplier_credit']."]]></supplier_credit>";
			break;
			
		case "lock_po_supplier" :
			$locked_result = $po_suppliers->lock_po_supplier($_REQUEST['s_id'],$_SESSION['login_id']);
			
			echo "<supplier_locked><![CDATA[".tep_button(BUTTON_UNLOCK, ALT_BUTTON_UNLOCK, '', ' name="supplier_lock" onClick="unlock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
			echo "<action_result><![CDATA[This CDK supplier is locked]]></action_result>";
			break;
			
		case "unlock_po_supplier" :
			$locked_result = $po_suppliers->unlock_po_supplier($_REQUEST['s_id']);
			
			echo "<supplier_locked><![CDATA[".tep_button(BUTTON_LOCK, ALT_BUTTON_LOCK, '', ' name="supplier_lock" onClick="lock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
			echo "<action_result><![CDATA[This CDK supplier is unlocked]]></action_result>";
			break;
			
		case "get_po_supplier_pay_currency" :
			$supplier_locked = $po_suppliers->get_po_supplier_lock_status($_REQUEST['s_id'],$_SESSION['login_id']);
			$po_curr_array = $po_suppliers->get_po_supplier_wsc_currency($_REQUEST['s_id']);
			if (is_null($supplier_locked['po_supplier_locked_by']) === true) {
				echo "<supplier_locked><![CDATA[".tep_button(BUTTON_LOCK, ALT_BUTTON_LOCK, '', ' name="supplier_lock" onClick="lock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
			} else if ($supplier_locked['po_supplier_locked_by'] == $_SESSION['login_id']) {
				echo "<supplier_locked><![CDATA[".tep_button(BUTTON_UNLOCK, ALT_BUTTON_UNLOCK, '', ' name="supplier_lock" onClick="unlock_cdk_supplier()"', 'inputButton')."]]></supplier_locked>";
			} else {
				echo "<supplier_locked><![CDATA[]]></supplier_locked>";
			}
			echo "<option_selection><![CDATA[";
			echo tep_draw_pull_down_menu('po_payment_currency', $po_curr_array, '', 'id="po_payment_currency" onChange="getPOPayMethodByCurrency(this);"');
			echo "]]></option_selection>";
			break;
			
		case "get_po_supplier_pay_method" :
			$po_pm_array = $po_suppliers->get_po_supplier_disburse_method_withdrawal($_REQUEST['s_id'], $_REQUEST['c_id']);
			$avail_amt = $po_suppliers->get_po_supplier_available_amount($_REQUEST['s_id'], $_REQUEST['c_id']);
			echo "<option_selection><![CDATA[";
			echo tep_draw_pull_down_menu('po_payment_method', $po_pm_array, '', 'id="po_payment_method" onChange="getPOPayForPO(this);"');
			echo "]]></option_selection>";
			echo "<currency_left_symbol>".$currencies->currencies[$_REQUEST['c_id']]['symbol_left']."</currency_left_symbol>";
			echo "<currency_right_symbol>".$currencies->currencies[$_REQUEST['c_id']]['symbol_right']."</currency_right_symbol>";
			echo "<available_amount>".$currencies->currencies[$_REQUEST['c_id']]['symbol_left'].number_format($avail_amt, $currencies->currencies[$_REQUEST['c_id']]['decimal_places'], $currencies->currencies[$_REQUEST['c_id']]['decimal_point'], $currencies->currencies[$_REQUEST['c_id']]['thousands_point']).$currencies->currencies[$_REQUEST['c_id']]['symbol_right']."</available_amount>";
			break;
			
		case "get_po_supplier_pay_for_po" :
			$po_array = $po_suppliers->get_po_supplier_pay_for_po($_REQUEST['s_id'], $_REQUEST['c_id'], $_REQUEST['pm_id']);
			
			$html_res = '<table border="0" width="100%" cellspacing="1" cellpadding="4">';
			$html_res .= '	<tr>';
			$html_res .= '		<td class="infoBoxHeading">'.TABLE_HEADING_PO_SELECT."</td>";
			$html_res .= '		<td class="infoBoxHeading">'.TABLE_HEADING_PO_PO_REF_NO."</td>";
			$html_res .= '		<td class="infoBoxHeading">'.TABLE_HEADING_PO_PO_AMOUNT."</td>";
			$html_res .= '</tr>';
			
			foreach($po_array as $po_data) {
				$html_res .= '<tr>';
				$html_res .= '<td class="main">'.tep_draw_checkbox_field('pay_po_batch[]', $po_data['id'], false, '', 'class="pay_po_batch" onClick="return calculatePayPOTotal(this, \''.number_format($po_data['amount'], $currencies->currencies[$_REQUEST['c_id']]['decimal_places'], '.', '').'\', \''.$po_data['payment_type'].'\')"').'</td>';
				$html_res .= '<td class="main">'.$po_data['text'].'</td>';
				$html_res .= '<td class="main">'.$currencies->currencies[$_REQUEST['c_id']]['symbol_left'].number_format($po_data['amount'], $currencies->currencies[$_REQUEST['c_id']]['decimal_places'], $currencies->currencies[$_REQUEST['c_id']]['decimal_point'], $currencies->currencies[$_REQUEST['c_id']]['thousands_point']).$currencies->currencies[$_REQUEST['c_id']]['symbol_right'].'</td>';
				$html_res .= '</tr>';
			}
			$html_res .= '</table>';
			
			echo "<option_selection><![CDATA[";
			echo $html_res;
			echo "]]></option_selection>";
			break;
			
		case "get_disburse_details" :
			$html_res = '';
			
			$supplier_locked = $po_suppliers->get_po_supplier_lock_status($_REQUEST['po_payment_supplier'],$_SESSION['login_id']);
			if ($supplier_locked['po_supplier_locked_by'] == $_SESSION['login_id']) {

				// Check for NRSC PO System payment methods
				$po_nrsc_sql = "SELECT pm.payment_methods_id, pm.payment_methods_types_id, pm.payment_methods_send_available_sites 
								FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab
								INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
									ON spab.payment_methods_id=pm.payment_methods_id
								WHERE spab.store_payment_account_book_id = '" . tep_db_input($_REQUEST['po_payment_method']) . "'";
				$po_nrsc_result = tep_db_query($po_nrsc_sql);
				if ($po_nrsc_row = tep_db_fetch_array($po_nrsc_result)) {
					if ((int) $po_nrsc_row['payment_methods_types_id'] == 6 && (int) $po_nrsc_row['payment_methods_send_available_sites'] == 3) {
						$po_send_currency = 0;
					} else {
						$po_send_currency = $currencies->currencies[$_REQUEST['po_payment_currency']]['id'];
					}
				}
				
				$payment_book_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_send_mode_name, pm.payment_methods_send_currency, spab.payment_methods_alias, 
												pm.payment_methods_types_id, spab.store_payment_account_book_id 
											FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS spab 
											INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
												ON spab.payment_methods_id=pm.payment_methods_id 
											WHERE spab.store_payment_account_book_id = '".tep_db_input($_REQUEST['po_payment_method'])."' 
                                                AND pm.payment_methods_send_currency = '".$po_send_currency."'
												AND pm.payment_methods_send_status = 1 
												AND pm.payment_methods_send_status_mode = 1
                                                AND spab.user_id = '".tep_db_input($_REQUEST['po_payment_supplier'])."' 
												AND spab.user_role = 'customers' ";
				$payment_book_result_sql = tep_db_query($payment_book_select_sql);
				$payment_book_row = tep_db_fetch_array($payment_book_result_sql);
				
				$payment_fee_select_sql = "	SELECT * 
											FROM " . TABLE_PAYMENT_FEES . "	
											WHERE payment_methods_id = '".tep_db_input($payment_book_row['payment_methods_id'])."' 
												AND payment_methods_mode = 'SEND'";
				$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
				if (!tep_db_num_rows($payment_fee_result_sql) && $payment_book_row['payment_methods_parent_id']>0) {
					$payment_fee_select_sql = "	SELECT * 
												FROM " . TABLE_PAYMENT_FEES . "	
												WHERE payment_methods_id = '".tep_db_input($payment_book_row['payment_methods_parent_id'])."' 
													AND payment_methods_mode = 'SEND'";
					$payment_fee_result_sql = tep_db_query($payment_fee_select_sql);
				}
				$payment_fee_row = tep_db_fetch_array($payment_fee_result_sql);
				
				$html_res =	'<tr class="inputBoxContents">
								<td width="35%" class="inputLabel" valign="top">'.ENTRY_WITHDRAW_PAYMENT_METHOD.'</td>
								<td class="inputLabel" width="1">:</td>
								<td class="inputField" valign="top">'.$payment_book_row['payment_methods_alias'].'</td>
							 </tr>';
				$html_res .= '<tr class="inputBoxContents">
								<td width="35%" class="inputLabel" valign="top">'.ENTRY_WITHDRAW_MIN_AMT.'</td>
								<td class="inputLabel" width="1">:</td>
								<td class="inputField" valign="top">'.($payment_fee_row['payment_fees_min'] > 0 ? $currencies->format($payment_fee_row['payment_fees_min'], true, $_REQUEST['po_payment_currency']) : TEXT_NO_WITHDRAW_LIMIT).'</td>
							  </tr>
							  <tr class="inputBoxContents">
								<td class="inputLabel" valign="top">'.ENTRY_WITHDRAW_MAX_AMT.'</td>
								<td class="inputLabel" width="1">:</td>
								<td class="inputField" valign="top">'.($payment_fee_row['payment_fees_max'] > 0 ? $currencies->format($payment_fee_row['payment_fees_max'], true, $_REQUEST['po_payment_currency']) : TEXT_NO_WITHDRAW_LIMIT).'</td>
							  </tr>';
				
				$pay_amount_batch = $po_suppliers->alloc_po_payment_part($_REQUEST, $messageStack);
				$html_res .= '<tr><td colspan="3">';
				$html_res .= '<table border="0" cellspacing="1" cellpadding="4" width="100%">';
				$html_res .= '<tr>
								<td class="infoBoxHeading" width="10%" style="border-bottom: 1px solid #B3BAC5;">'.TABLE_HEADING_PAYMENT_BATCH.'</td>
								<td class="infoBoxHeading" width="35%" style="border-bottom: 1px solid #B3BAC5;">'.TABLE_HEADING_PAYMENT_AMOUNT.'</td>
								<td class="infoBoxHeading" width="55%" style="border-bottom: 1px solid #B3BAC5;">'.TABLE_HEADING_PAYMENT_PO_LIST.'</td>
							</tr>';
				if (isset($pay_amount_batch['error']) && tep_not_null($pay_amount_batch['error'])) {
					$html_res .= '<tr><td style="border-bottom: 1px solid #B3BAC5; border-left: 1px solid #B3BAC5; border-right: 1px solid #B3BAC5" colspan="4"><font color="red"><b>'.$pay_amount_batch['error'].'</b></font></td></tr>';
				} else {
					$batch_cnt=0;
					foreach ($pay_amount_batch as $payment_batch) {
						$batch_cnt++;
						if (is_array($payment_batch['po_ref_list'])) { $po_str = implode(', ', $payment_batch['po_ref_list']); } else { $po_str = implode(', ', $payment_batch['po_list']); }
						$html_res .= '<tr>
										<td style="border-bottom: 1px solid #B3BAC5; border-left: 1px solid #B3BAC5; border-right: 1px solid #B3BAC5" class="main">'.$batch_cnt.'</td>
										<td style="border-bottom: 1px solid #B3BAC5; border-right: 1px solid #B3BAC5" class="main">'.$currencies->format($payment_batch['pay_amount'], false, $_REQUEST['po_payment_currency']).'</td>
										<td style="border-bottom: 1px solid #B3BAC5; border-right: 1px solid #B3BAC5" class="main">'.$po_str.'</td>
									</tr>';
					}
				}
				$html_res .= '</table>';
				$html_res .= '</td></tr>';
				
				$payment_fields_select_sql = "	SELECT pmf.*, spabd.payment_methods_fields_value 
												FROM " . TABLE_PAYMENT_METHODS_FIELDS . " AS pmf 
												LEFT JOIN " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS . " spabd 
													ON (pmf.payment_methods_fields_id=spabd.payment_methods_fields_id AND spabd.store_payment_account_book_id = '".tep_db_input($payment_book_row['store_payment_account_book_id'])."')
												WHERE pmf.payment_methods_id = '" . tep_db_input($payment_book_row['payment_methods_id']) . "' 
													AND pmf.payment_methods_mode = 'SEND' 
													AND pmf.payment_methods_fields_status = 1 
												ORDER BY pmf.payment_methods_fields_sort_order";
				$payment_fields_result_sql = tep_db_query($payment_fields_select_sql);
				
				while ($payment_fields_row = tep_db_fetch_array($payment_fields_result_sql)) {
					if ($payment_fields_row['payment_methods_fields_type']=='6' || $payment_fields_row['payment_methods_fields_type']=='7') {
						$update_payment_methods_fields_data_sql = array('payment_methods_fields_value' => tep_db_prepare_input($payment_fields_row['payment_methods_fields_option']));
						tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS, $update_payment_methods_fields_data_sql, 'update', " payment_methods_fields_id = '".$payment_fields_row['payment_methods_fields_id']."' AND store_payment_account_book_id = '".$payment_book_id."'");
					}
					$pm_fields_value_str = ($payment_fields_row['payment_methods_fields_type']=='6' || $payment_fields_row['payment_methods_fields_type']=='7') ? str_replace(':~:', "\r\n", $payment_fields_row['payment_methods_fields_option']) : $payment_fields_row['payment_methods_fields_value'];
					$html_res .= '<tr class="inputBoxContents">
									<td class="inputLabel" valign="top" nowrap><i>'.$payment_fields_row['payment_methods_fields_title'].'</i></td>
									<td class="inputLabel" width="1">:</td>
									<td class="inputField" valign="top">'.nl2br($pm_fields_value_str).'</td>
								  </tr>';
				}
			} else {
				if (is_null($supplier_locked['po_supplier_locked_by'])) {
					$error_mesg = 'Please lock the supplier before proceed to disburse the payment.';
				} else {
					$error_mesg = 'This supplier has been locked by other crew user!';
				}
				$html_res = '<tr class="inputBoxContents"><td class="inputField" valign="top">'.$error_mesg.'</td></tr>';
				echo "<supplier_locked>1</supplier_locked>";
			}
			
			echo "<disburse_details><![CDATA[";
			echo $html_res;
			echo "]]></disburse_details>";
			break;
			
		default:
			echo "<result>Unknown request!</result>";

			break;
	}
}

echo '</response>';
?>