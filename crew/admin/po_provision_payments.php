<?php
/*
	$Id: po_provision_payments.php,v 1.1 2011/06/21 02:16:01 wilson.sun Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'po_suppliers.php');
require(DIR_WS_CLASSES . 'edit_purchase_orders.php');

$currencies = new currencies();
$po_supplier_obj = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
<script language="javascript" src="includes/javascript/jquery.js"></script>
</head>
<body onLoad="initInfoCaptions()" marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">

<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
	<tr>
		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
			</table>
		</td>
<!-- body_text //-->
		<td width="100%" valign="top">
	   		<table border="0" width="100%" cellspacing="0" cellpadding="2">
				<tr>
					<td width="100%">
						<table border="0" width="100%" cellspacing="0" cellpadding="5">
							<tr>
								<td class="pageHeading" colspan="2"><?php echo HEADING_TITLE; ?></td>
							</tr>
							<tr><td class="pageHeading" colspan="2"><hr></td></tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td valign="top">
									<? echo $po_supplier_obj->get_po_supplier_provision_payments(); ?>
								</td>
							</tr>
						</table>
					</td>
<!-- body_text_eof //-->
				</tr>
			</table>
		</td>
	</tr>
</table>
<!-- body_eof //-->
<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>