<?php
/*
  	$Id: po_suppliers.php,v 1.11 2012/03/19 10:09:54 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'payment_module_info.php');
require_once(DIR_WS_CLASSES . 'po_suppliers.php');
require_once(DIR_WS_CLASSES . 'edit_purchase_orders.php');
require_once(DIR_WS_FUNCTIONS . 'user_agent.php');
require_once('pear/Date.php');

define('DISPLAY_PRICE_DECIMAL', 4);

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$po_suppliers_obj = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);

$insert_po_supplier_permission = tep_admin_files_actions(FILENAME_PO_SUPPLIERS_LIST, 'PO_NEW_SUPPLIER');
$edit_po_supplier_account_permission = tep_admin_files_actions(FILENAME_PO_SUPPLIERS_LIST, 'PO_EDIT_SUPPLIER_ACCOUNT');
$view_po_supplier_payment_info_permission = tep_admin_files_actions(FILENAME_PO_SUPPLIERS_LIST, 'PO_VIEW_SUPPLIER_PAYMENT_INFO');
$edit_po_supplier_payment_info_permission = tep_admin_files_actions(FILENAME_PO_SUPPLIERS_LIST, 'PO_EDIT_SUPPLIER_PAYMENT_INFO');
$view_po_supplier_remark_permission = tep_admin_files_actions(FILENAME_PO_SUPPLIERS_LIST, 'PO_VIEW_SUPPLIER_REMARK');
$edit_po_supplier_remark_permission = tep_admin_files_actions(FILENAME_PO_SUPPLIERS_LIST, 'PO_EDIT_SUPPLIER_REMARK');
$view_po_supplier_payment_statistic_permission = tep_admin_files_actions(FILENAME_PO_SUPPLIERS_LIST, 'PO_VIEW_SUPPLIER_PAYMENT_STATISTIC');
$view_po_supplier_discount_terms_permission = tep_admin_files_actions(FILENAME_PO_SUPPLIERS_LIST, 'PO_VIEW_SUPPLIER_DISCOUNT_TERMS');
$edit_po_supplier_discount_terms_permission = tep_admin_files_actions(FILENAME_PO_SUPPLIERS_LIST, 'PO_EDIT_SUPPLIER_DISCOUNT_TERMS');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

$error = false;
$processed = false;
$error_section = "";

if ($action == "new_supplier") {
	if (!$insert_po_supplier_permission) {
		$messageStack->add_session(ERROR_PO_SUPPLIER_INSERT_PERMISSION, 'error');
		tep_redirect(tep_href_link(FILENAME_PO_SUPPLIERS_LIST, 'page=' . $_GET['page']));
	}
}

if (tep_not_null($action)) {
	switch ($action) {
		case "set_supplier_status":
			if ( ($_REQUEST['flag'] == '0') || ($_REQUEST['flag'] == '1') ) {
          		if (isset($_REQUEST['sID'])) {
          			if ($edit_po_supplier_account_permission) {
          				$po_suppliers_obj->update_po_supplier_status($_REQUEST['sID'], $_REQUEST['flag']);
						
						// add action remark
						$remarks_data_array = array('customers_id' => $_REQUEST['sID'],
													'date_remarks_added' => 'now()',
													'remarks' => 'Change of Status',
													'remarks_added_by' => $_SESSION['login_id']);
						tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);
          			} else {
          				$messageStack->add_session(ERROR_PO_SUPPLIER_ACCOUNT_EDIT, 'error');
          			}
          		}
        	}
        	tep_redirect(tep_href_link(FILENAME_PO_SUPPLIERS_LIST, 'page=' . $_GET['page']));
        	
			break;
		case "supplier_code_search":
            $po_supplier_code_search = tep_db_prepare_input($_REQUEST['po_supplier_code_search']);
            $po_supplier_id = $_REQUEST['sID'];
            
            if (tep_not_null($po_supplier_code_search)) {
                $search_supp_id_select_sql = "SELECT po_suppliers_id
                                              FROM ". TABLE_PO_SUPPLIERS ."
                                              WHERE po_supplier_code = '". $po_supplier_code_search ."'";
                $search_supp_id_result_sql = tep_db_query($search_supp_id_select_sql);
    		    
    		    if ($search_supp_id_row = tep_db_fetch_array($search_supp_id_result_sql)) {
    		        $po_supplier_id = $search_supp_id_row['po_suppliers_id'];
    		    } else {
    		        $messageStack->add_session(ERROR_PO_SUPPLIER_NOT_EXIST, 'error');
    		    }
            }
            tep_redirect(tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction', 'sID')) . 'sID=' . $po_supplier_id . '&action=edit_payment'));
            
			break;
		case "delete_pm":
			$book_id = tep_db_prepare_input($_REQUEST['book_id']);
			$po_supplier_id = $_REQUEST['sID'];
			
			if ($edit_po_supplier_payment_info_permission) {
				if (tep_not_null($po_supplier_id) && tep_not_null($book_id)) {
					if (!$po_suppliers_obj->check_disbursement_in_usage_in_po($po_supplier_id, $book_id)) {
						$pm_object = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $po_supplier_id, '3');
						$deleted_pm_name = (isset($pm_object->payment_accounts[$book_id]['pm_name']) ? $pm_object->payment_accounts[$book_id]['pm_name'] : '');
						$update_success = $pm_object->delete_payment_account($book_id, $messageStack);
						if ($update_success) {
							// deactivate the po supplier
							$po_suppliers_obj->update_po_supplier_status($po_supplier_id, '0');
							
							// add action remark
							$remarks_data_array = array('customers_id' => $po_supplier_id,
														'date_remarks_added' => 'now()',
														'remarks' => 'Delete Payment Account Info:'.$deleted_pm_name,
														'remarks_added_by' => $_SESSION['login_id']);
							tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);
							
							// send notification email
							$inactive_po_supplier_email_contents = sprintf(EMAIL_PO_SUPPLIER_INACTIVE_NOTIFICATION_CONTENT, $po_supplier_id, 'Inactive', 'Status', date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'], 'Payment Info Deleted.');

							$email_to_array = tep_parse_email_string(PO_SUPPLIER_INACTIVE_NOTIFICATION_EMAIL);
							for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
								tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, 'Inactive PO Supplier')), $inactive_po_supplier_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
							}

							$messageStack->add_session(SUCCESS_PO_SUPPLIER_PAYMENT_ACCOUNT_DELETED, 'success');
						} else {
							$messageStack->add_session(ERROR_PO_SUPPLIER_PAYMENT_ACCOUNT_DELETE_FAILED, 'error');
						}
					} else {
						$messageStack->add_session(ERROR_PO_SUPPLIER_PAYMENT_ACCOUNT_IN_USE, 'error');
					}
				}
			} else {
				$messageStack->add_session(ERROR_PO_SUPPLIER_PAYMENT_PERMISSION_UPDATED, 'error');
			}
        	tep_redirect(tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction', 'sID', 'book_id')) . 'sID=' . $po_supplier_id . '&action=edit_payment'));
        	
			break;
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
		case "insert_po_supplier":
			if ($insert_po_supplier_permission) {
				$po_supplier_firstname = tep_db_prepare_input($_POST['po_supplier_firstname']);
				$po_supplier_lastname = tep_db_prepare_input($_POST['po_supplier_lastname']);
				$po_supplier_email_address = tep_db_prepare_input($_POST['po_supplier_email_address']);
				$po_supplier_street_address = tep_db_prepare_input($_POST['po_supplier_street_address']);
				$po_supplier_suburb = tep_db_prepare_input($_POST['po_supplier_suburb']);
				$po_supplier_postcode = tep_db_prepare_input($_POST['po_supplier_postcode']);
				$po_supplier_city = tep_db_prepare_input($_POST['po_supplier_city']);
				$po_supplier_state = tep_db_prepare_input($_POST['po_supplier_state']);
				$po_supplier_country_id = tep_db_prepare_input($_POST['po_supplier_country_id']);
                                $po_supplier_company_code = tep_db_prepare_input($_POST['po_supplier_company_code']);
				$po_supplier_telephone = tep_db_prepare_input($_POST['po_supplier_telephone']);
				$po_supplier_fax = tep_db_prepare_input($_POST['po_supplier_fax']);
				$po_supplier_qq = tep_db_prepare_input($_POST['po_supplier_qq']);
				$po_supplier_msn = tep_db_prepare_input($_POST['po_supplier_msn']);	        
				$po_supplier_code = tep_db_prepare_input($_POST['po_supplier_code']);
				$po_supplier_po_ref_counter = tep_db_prepare_input($_POST['po_supplier_po_ref_counter']);
				$po_payment_type = tep_db_prepare_input($_POST['po_payment_type']);
				$po_payment_term = tep_db_prepare_input($_POST['po_payment_term']);
				$po_days_pay_wsc = tep_db_prepare_input($_POST['po_days_pay_wsc']);
				$po_supplier_remark = tep_db_prepare_input($_POST['po_supplier_remark']);
		        
		      	if (ACCOUNT_STATE == 'true') {
			    	$po_supplier_state = tep_db_prepare_input($_POST['po_supplier_state']);
			    }
			    
		        if (strlen($po_supplier_firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
	          		$error = true;
	          		$entry_firstname_error = true;
	        	} else {
	          		$entry_firstname_error = false;
	        	}
				
	        	if (strlen($po_supplier_lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
	          		$error = true;
	          		$entry_lastname_error = true;
	        	} else {
	          		$entry_lastname_error = false;
	        	}
				
	        	if (strlen($po_supplier_email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
		          	$error = true;
		          	$entry_email_address_error = true;
	        	} else {
	          		$entry_email_address_error = false;
	        	}
				
	        	if (!tep_validate_email($po_supplier_email_address)) {
	          		$error = true;
	          		$entry_email_address_check_error = true;
	        	} else {
	          		$entry_email_address_check_error = false;
	        	}
				
	        	if (strlen($po_supplier_street_address) < ENTRY_STREET_ADDRESS_MIN_LENGTH) {
	          		$error = true;
	          		$entry_street_address_error = true;
	        	} else {
	          		$entry_street_address_error = false;
	        	}
				
	        	if (strlen($po_supplier_postcode) < ENTRY_POSTCODE_MIN_LENGTH) {
	          		$error = true;
	          		$entry_post_code_error = true;
	        	} else {
	          		$entry_post_code_error = false;
	        	}
				
	        	if (strlen($po_supplier_city) < ENTRY_CITY_MIN_LENGTH) {
	          		$error = true;
	          		$entry_city_error = true;
	        	} else {
	          		$entry_city_error = false;
	        	}
	        	
	        	if (!tep_not_null($po_supplier_country_id)) {
	          		$error = true;
	          		$entry_country_error = true;
	        	} else {
	          		$entry_country_error = false;
	          		
	          		if (ACCOUNT_STATE == 'true') {
			            $entry_state_error = true;
			            $check_query = tep_db_query("select count(*) as total from " . TABLE_ZONES . " where zone_country_id = '" . (int)$po_supplier_country_id . "'");
			            $check_value = tep_db_fetch_array($check_query);
			            $entry_state_has_zones = ($check_value['total'] > 0);
			            
			            if ($entry_state_has_zones == true) {
			            	$entry_state_error = true;
		          			$zone_query = tep_db_query("select zone_id, zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int)$po_supplier_country_id . "' and zone_id = '" . (int)$po_supplier_state . "'");
		          			if (tep_db_num_rows($zone_query) == 1) {
		            			$zone_values = tep_db_fetch_array($zone_query);
		            			$po_supplier_zone_id = $zone_values['zone_id'];
		            			$po_supplier_zone_name = $zone_values['zone_name'];
		            			$_POST['po_supplier_zone_id'] = $po_supplier_zone_id;
		            			$_POST['po_supplier_zone_name'] = $po_supplier_zone_name;
		            			$entry_state_error = false;
		          			} else {
		            			$error = true;
		            			$entry_state_error = true;
		          			}
		        		}
		      		}
	        	}
                        
                        if (!tep_not_null($po_supplier_company_code)) {
	          		$error = true;
	          		$entry_company_error = true;
	        	} else {
	          		$entry_company_error = false;
	        	}
	      		
	      		if (strlen($po_supplier_telephone) < ENTRY_TELEPHONE_MIN_LENGTH) {
	        		$error = true;
	        		$entry_telephone_error = true;
	      		} else {
	        		$entry_telephone_error = false;
	      		}
	      		
	      		if (tep_not_null($po_supplier_msn) && !tep_validate_email($po_supplier_msn)) {
	              	$error = true;
	              	$entry_msn_error = true;
	            } else {
	                $entry_msn_error = false;
	            }
	      		
	      		if (tep_not_null($po_supplier_qq) && !is_numeric($po_supplier_qq)) {
	                $error = true;
	              	$entry_qq_error = true;
	            } else {
	                $entry_qq_error = false;
	            }

	      		$check_email = tep_db_query("select customers_email_address from " . TABLE_CUSTOMERS . " where customers_email_address = '" . tep_db_input($po_supplier_email_address) . "'");
	      		if (tep_db_num_rows($check_email)) {
	        		$error = true;
	        		$entry_email_address_exists = true;
	      		} else {
	        		$entry_email_address_exists = false;
	      		}
	      		
	      		if (tep_not_null($po_supplier_code)) {
	      			$supplier_code_error = false;
	        	} else {
	          		$error = true;
		          	$supplier_code_error = true;
	        	}
				
				if ($supplier_code_error == false) {
					$check_supplier_code = tep_db_query("select po_supplier_code from " . TABLE_PO_SUPPLIERS . " where po_supplier_code = '" . tep_db_input($po_supplier_code) . "'");
		      		if (tep_db_num_rows($check_supplier_code)) {
		        		$error = true;
		        		$supplier_code_exists = true;
		      		} else {
		        		$supplier_code_exists = false;
		      		}
		      	}
				
				if (strlen($po_supplier_po_ref_counter) > 4) {
	        		$error = true;
	        		$supplier_ref_counter_error = true;
	      		} else {
	        		$supplier_ref_counter_error = false;
				}
      			
          		if ($po_payment_type != 'c' && $po_payment_type != 't' && $po_payment_type != 'g' && $po_payment_type != 'd') {
                            $error = true;
                            $payment_type_error = true;
          		} else {
                            $payment_type_error = false;
                            if ($po_payment_type == 't') {
                                if (tep_not_null($po_payment_term)) {
                                    $payment_term_error = false;
                                } else {
                                    $error = true;
                                    $payment_term_error = true;
                                }

                                if (tep_not_null($po_days_pay_wsc)) {
                                    $payment_pay_wsc_error = false;
                                } else {
                                    $error = true;
                                    $payment_pay_wsc_error = true;
                                }
                            } else {
                                $_POST['po_days_pay_wsc'] = '0';
                            }
          		}
      			
	      		if (!$error) {
	      			$po_supplier_id = $po_suppliers_obj->insert_new_po_supplier($_POST, $messageStack);
					
					// send notification email
					$inactive_po_supplier_email_contents = sprintf(EMAIL_PO_SUPPLIER_INACTIVE_NOTIFICATION_CONTENT, $po_supplier_id, 'Inactive', 'Status', date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'], 'New PO Supplier.');
					
					$email_to_array = tep_parse_email_string(PO_SUPPLIER_INACTIVE_NOTIFICATION_EMAIL);
					for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
		    			tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, 'New PO Supplier')), $inactive_po_supplier_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		    		}
            		
					$messageStack->add_session(sprintf(SUCCESS_PO_SUPPLIER_ADDED, $po_supplier_email_address), 'success');
					tep_redirect(tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('sID', 'action', 'subaction')) . '&page=' . $_REQUEST['page']));
				} else {
					$error_section = 'info';
					$messageStack->add_session(ERROR_PO_SUPPLIER_INVALID_DATA_INSERT, 'error');
				}
			}
			break;
			
        case "update_payment":
        	$po_update_type = isset($_REQUEST['po_supplier_update']) ? $_REQUEST['po_supplier_update'] : '';
        	switch ($po_update_type) {
        		case "status" :
	        		if ($edit_po_supplier_account_permission) {
		        		$po_supplier_id = tep_db_prepare_input($_REQUEST['po_supplier_id']);
				        $po_supplier_status = tep_db_prepare_input($_REQUEST['po_supplier_status']);
				        
				        $po_suppliers_obj->update_po_supplier_status($po_supplier_id, $po_supplier_status);
						
						// add action remark
						$remarks_data_array = array('customers_id' => $po_supplier_id,
													'date_remarks_added' => 'now()',
													'remarks' => 'Change of Status',
													'remarks_added_by' => $_SESSION['login_id']);
						tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);
						
						$messageStack->add_session(SUCCESS_PO_SUPPLIER_ACCOUNT_UPDATED, 'success');
			        } else {
						$messageStack->add_session(ERROR_PO_SUPPLIER_ACCOUNT_PERMISSION_UPDATED, 'error');
					}
        			break;
        		case "payment" :
	        		if ($edit_po_supplier_payment_info_permission) {
	        			$update_success = true;
		        		$po_supplier_id = tep_db_prepare_input($_REQUEST['po_supplier_id']);
		        		
						$select_sql = "SELECT po_payment_type, po_payment_term, po_days_pay_wsc FROM " . TABLE_PO_SUPPLIERS . " WHERE po_suppliers_id='".tep_db_input($po_supplier_id)."'";
						$result_sql = tep_db_query($select_sql);
						if ($data_row = tep_db_fetch_array($result_sql)) {
							$original_data = $data_row;
						}
						
						$po_payment_type = tep_db_prepare_input($_REQUEST['po_payment_type']);
						if ($po_payment_type == 'c' || $po_payment_type == 'g') {
							$po_payment_term = '0';
							$po_days_pay_wsc = '0';
						} else if ($po_payment_type == 't') {
							$po_payment_term = tep_db_prepare_input($_REQUEST['po_payment_term']);
							$po_days_pay_wsc = tep_db_prepare_input($_REQUEST['po_days_pay_wsc']);
						}
						$po_data_array = array(
												'po_payment_type' => $po_payment_type,
												'po_payment_term' => $po_payment_term,
												'po_days_pay_wsc' => $po_days_pay_wsc
												);
						tep_db_perform(TABLE_PO_SUPPLIERS, $po_data_array, 'update', "po_suppliers_id='".tep_db_input($po_supplier_id)."'");
						
						// add action remark
						$action_remark = '';
						if ($original_data['po_payment_type'] != $po_payment_type) {
							$action_remark = 'Change of Payment Type';
						} else if ($original_data['po_payment_type'] == $po_payment_type && $original_data['po_payment_term'] != $po_payment_term) {
							$action_remark = 'Change of Payment Term';
						} else if ($original_data['po_payment_type'] == $po_payment_type && $original_data['po_days_pay_wsc'] != $po_days_pay_wsc) {
							$action_remark = 'Change of Payment Days to credit WSC';
						}
						if (tep_not_null($action_remark)) {
							$remarks_data_array = array('customers_id' => $po_supplier_id,
														'date_remarks_added' => 'now()',
														'remarks' => $action_remark,
														'remarks_added_by' => $_SESSION['login_id']);
							tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);
						}
				        
				        if ((isset($_REQUEST['book_id']) && tep_not_null($_REQUEST['book_id'])) || 
				        	(isset($_REQUEST['pm_id']) && tep_not_null($_REQUEST['pm_id']))) {
							// updatae disbursement payment info
							$pm_object = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $po_supplier_id, '3');
							$update_pm_name = (isset($pm_object->payment_accounts[$_REQUEST['book_id']]['pm_name']) ? $pm_object->payment_accounts[$_REQUEST['book_id']]['pm_name'] : '');
				        	$update_success = $pm_object->update_payment_account($_REQUEST, $messageStack);
							
							// add action remark
							$action_remark = '';
							if (isset($_REQUEST['book_id'])) {
								$action_remark = 'Update of Payment Account Info:'.$update_pm_name . "\n" . $pm_object->change_log;
							} else {
								$pm_select_sql = "	SELECT pm.payment_methods_send_mode_name
													FROM " . TABLE_PAYMENT_METHODS . " AS pm
													WHERE pm.payment_methods_id = '" . tep_db_input($_REQUEST['pm_id']) . "'";
								$pm_result_sql = tep_db_query($pm_select_sql);
								if ($pm_row = tep_db_fetch_array($pm_result_sql)) {
									$action_remark = 'Add new Payment Account Info:'.$pm_row['payment_methods_send_mode_name'];
								}
							}
							if (tep_not_null($action_remark)) {
								$remarks_data_array = array('customers_id' => $po_supplier_id,
															'date_remarks_added' => 'now()',
															'remarks' => $action_remark,
															'remarks_added_by' => $_SESSION['login_id']);
								tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);
							}
						}
						
						if ($update_success) {
							// deactivate the po supplier
							$po_suppliers_obj->update_po_supplier_status($po_supplier_id, '0');
							
							// send notification email
							$inactive_po_supplier_email_contents = sprintf(EMAIL_PO_SUPPLIER_INACTIVE_NOTIFICATION_CONTENT, $po_supplier_id, 'Inactive', 'Status', date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'], 'Changed Payment Info.');
							
							$email_to_array = tep_parse_email_string(PO_SUPPLIER_INACTIVE_NOTIFICATION_EMAIL);
							for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
				    			tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, 'Inactive PO Supplier')), $inactive_po_supplier_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
				    		}
				    		
							$messageStack->add_session(SUCCESS_PO_SUPPLIER_PAYMENT_UPDATED, 'success');
						} else {
							$messageStack->add_session(ERROR_PO_SUPPLIER_PAYMENT_UPDATE_FAILED, 'error');
						}
			        } else {
						$messageStack->add_session(ERROR_PO_SUPPLIER_PAYMENT_PERMISSION_UPDATED, 'error');
					}
        			break;
        		case "agreement" :
	        		if ($edit_po_supplier_discount_terms_permission) {
				        $po_suppliers_obj->update_po_supplier_agreement_terms($_REQUEST);
						
						// add action remark
						$remarks_data_array = array('customers_id' => $po_supplier_id,
													'date_remarks_added' => 'now()',
													'remarks' => 'Change of Discount Agreement',
													'remarks_added_by' => $_SESSION['login_id']);
						tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);
						
						$messageStack->add_session(SUCCESS_PO_SUPPLIER_AGREEMENT_UPDATED, 'success');
			        } else {
						$messageStack->add_session(ERROR_PO_SUPPLIER_AGREEMENT_PERMISSION_UPDATED, 'error');
					}
        			break;
                        case "delivery_address" :
                            if ($edit_po_supplier_discount_terms_permission) {
                                $po_suppliers_obj->update_po_supplier_company_code($_REQUEST);

                                // add action remark
                                $remarks_data_array = array(
                                    'customers_id' => $po_supplier_id,
                                    'date_remarks_added' => 'now()',
                                    'remarks' => 'Change of Delivery Address',
                                    'remarks_added_by' => $_SESSION['login_id']);
                                tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);

                                $messageStack->add_session(SUCCESS_PO_SUPPLIER_COMPANY_UPDATED, 'success');
                            } else {
                                $messageStack->add_session(ERROR_PO_SUPPLIER_COMPANY_PERMISSION_UPDATED, 'error');
                            }
                            break;
        		case "remark" :
	        		if ($edit_po_supplier_payment_info_permission) {
		        		$po_supplier_id = tep_db_prepare_input($_REQUEST['po_supplier_id']);
		        		$po_supplier_remark = tep_db_prepare_input($_REQUEST['po_supplier_remark']);
			        	
		            	// insert po supplier remark
						if (tep_not_null($po_supplier_remark)) {
							$remarks_data_array = array('customers_id' => $po_supplier_id,
														'date_remarks_added' => 'now()',
			        	                       			'remarks' => $po_supplier_remark,
			            	                   			'remarks_added_by' => $_SESSION['login_id']);
							tep_db_perform(TABLE_CUSTOMERS_REMARKS_HISTORY, $remarks_data_array);
						}
						
						$messageStack->add_session(SUCCESS_PO_SUPPLIER_REMARK_UPDATED, 'success');
			        } else {
						$messageStack->add_session(ERROR_PO_SUPPLIER_REMARK_PERMISSION_UPDATED, 'error');
					}
					break;
				default :
					break;
	        }
			tep_redirect(tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('sID', 'action', 'subaction')) . '&page=' . $_REQUEST['page'] . '&sID=' . $_REQUEST['po_supplier_id'] . '&action=edit_payment'));
        	break;
        	
        case "update_list":
        	if ($edit_po_supplier_account_permission) {
				switch ($_POST["batch_action"]) {
					case "Active":
						$po_suppliers_obj->batch_update_po_supplier_status($_POST, '1');
						break;
					case "Inactive":
						$po_suppliers_obj->batch_update_po_supplier_status($_POST, '0');
						break;
				}
			} else {
				$messageStack->add_session(ERROR_PO_SUPPLIER_ACCOUNT_EDIT, 'error');
			}
			tep_redirect(tep_href_link(FILENAME_PO_SUPPLIERS_LIST, 'page=' . $_GET['page']));
        	break;
	}
	
	if ($error) {
		$sInfo = new objectInfo($_POST, false);
		$sInfo->po_supplier_dob = $_POST['dob_month'].'/'.$_POST['dob_day'].'/'.$_POST['dob_year'];
		$processed = true;
	}
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.js"></script>
<?	include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); ?>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/customer_xmlhttp.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
            			<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
<?
if ($action == "new_supplier") {
	$pm_object = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], '', '3');
	$payment_html_content = $pm_object->new_payment_account();
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
	if (($insert_po_supplier_permission) && (!$error || $error_section=='info')) {
?>
					<tr>
        				<td width="100%">
<?	echo tep_draw_form('po_supplier_form', FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('subaction','sID')) . 'subaction=insert_po_supplier', 'post', 'onSubmit="return supplier_check_form();"'); ?>
        					<fieldset class="selectedFieldSet">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="customerFormAreaTitle"><?=CATEGORY_ACCOUNT?></td>
								</tr>
								<tr>
									<td class="formArea">
										<table border="0" width="100%" cellspacing="2" cellpadding="2">
											<tr>
												<td class="main" width="20%"><?=ENTRY_SUPPLIER_CODE?></td>
												<td class="main" width="20%" valign="top" colspan="2">
<?		if ($error == true) {
	    	if ($supplier_code_error == true) {
	      		echo tep_draw_input_field('po_supplier_code', $sInfo->po_supplier_code, 'maxlength="12" id="po_supplier_code"') . '&nbsp;' . ENTRY_SUPPLIER_CODE_ERROR;
			} else if ($supplier_code_exists == true) {
	      		echo tep_draw_input_field('po_supplier_code', $sInfo->po_supplier_code, 'maxlength="12" id="po_supplier_code"') . '&nbsp;' . ENTRY_SUPPLIER_CODE_EXIST_ERROR;
	    	} else {
	      		echo $sInfo->po_supplier_code . tep_draw_hidden_field('po_supplier_code');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_code', $sInfo->po_supplier_code, 'maxlength="12" id="po_supplier_code"', true);
	  	}
?>
												</td>
											</tr>
											<tr>
												<td class="main" width="20%"><?=TABLE_HEADING_SUPPLIER_REFERENCE_RUNNING_YEAR?></td>
												<td class="main" width="20%" valign="top" colspan="2">
<?		echo date('y') . tep_draw_hidden_field('po_supplier_po_ref_year', date('y'), 'id="po_supplier_po_ref_year"'); ?>
												</td>
											</tr>
											<tr>
												<td class="main" width="20%" valign="top"><?=TABLE_HEADING_SUPPLIER_REFERENCE_RUNNING_COUNT?></td>
												<td class="main" width="20%" valign="top">
<?		if ($error == true) {
	    	if ($supplier_ref_counter_error == true) {
	      		echo tep_draw_input_field('po_supplier_po_ref_counter', $sInfo->po_supplier_po_ref_counter, 'maxlength="4" id="po_supplier_po_ref_counter"') . '&nbsp;' . ENTRY_SUPPLIER_REFERENCE_COUNTER_ERROR;
	    	} else {
	      		echo (tep_not_null($sInfo->po_supplier_po_ref_counter) ? $sInfo->po_supplier_po_ref_counter : '0000') . tep_draw_hidden_field('po_supplier_po_ref_counter');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_po_ref_counter', (tep_not_null($sInfo->po_supplier_po_ref_counter) ? $sInfo->po_supplier_po_ref_counter : '0'), 'maxlength="4" id="po_supplier_po_ref_counter"', true);
	  	}
?>
												</td>
												<td class="main" valign="top"><?=ENTRY_SUPPLIER_REFERENCE_COUNTER_DESC?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
        						<tr>
									<td class="customerFormAreaTitle"><?=CATEGORY_PERSONAL?></td>
								</tr>
								<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
						      				<tr>
            									<td class="main" width="20%"><?=ENTRY_FIRST_NAME?></td>
            									<td class="main">
<?		if ($error == true) {
	    	if ($entry_firstname_error == true) {
	      		echo tep_draw_input_field('po_supplier_firstname', $sInfo->po_supplier_firstname, 'maxlength="32" id="po_supplier_firstname"') . '&nbsp;' . ENTRY_FIRST_NAME_ERROR;
	    	} else {
	      		echo $sInfo->po_supplier_firstname . tep_draw_hidden_field('po_supplier_firstname');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_firstname', $sInfo->po_supplier_firstname, 'maxlength="32" id="po_supplier_firstname"', true);
	  	}
?>
												</td>
          									</tr>
			      							<tr>
			            						<td class="main"><?=ENTRY_LAST_NAME?></td>
			            						<td class="main">
<?		if ($error == true) {
	    	if ($entry_lastname_error == true) {
	      		echo tep_draw_input_field('po_supplier_lastname', $sInfo->po_supplier_lastname, 'maxlength="32" id="po_supplier_lastname"') . '&nbsp;' . ENTRY_LAST_NAME_ERROR;
	    	} else {
	      		echo $sInfo->po_supplier_lastname . tep_draw_hidden_field('po_supplier_lastname');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_lastname', $sInfo->po_supplier_lastname, 'maxlength="32" id="po_supplier_lastname"', true);
	  	}
?>
												</td>
          									</tr>
											<tr>
			            						<td class="main"><?=ENTRY_EMAIL_ADDRESS?></td>
			            						<td class="main">
<?		if ($error == true) {
	    	if ($entry_email_address_error == true) {
	      		echo tep_draw_input_field('po_supplier_email_address', $sInfo->po_supplier_email_address, 'maxlength="96" id="po_supplier_email_address"') . '&nbsp;' . ENTRY_EMAIL_ADDRESS_ERROR;
	    	} else if ($entry_email_address_check_error == true) {
	      		echo tep_draw_input_field('po_supplier_email_address', $sInfo->po_supplier_email_address, 'maxlength="96" id="po_supplier_email_address"') . '&nbsp;' . ENTRY_EMAIL_ADDRESS_CHECK_ERROR;
	    	} else if ($entry_email_address_exists == true) {
	      		echo tep_draw_input_field('po_supplier_email_address', $sInfo->po_supplier_email_address, 'maxlength="96" id="po_supplier_email_address"') . '&nbsp;' . ENTRY_EMAIL_ADDRESS_ERROR_EXISTS;
	    	} else {
	      		echo $sInfo->po_supplier_email_address . tep_draw_hidden_field('po_supplier_email_address');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_email_address', $sInfo->po_supplier_email_address, 'maxlength="96" id="po_supplier_email_address"', true);
	  	}
			?>									</td>
				          					</tr>
				        				</table>
				        			</td>
				      			</tr>
				      			<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_ADDRESS?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				          					<tr>
				            					<td class="main" width="20%"><?=ENTRY_STREET_ADDRESS?></td>
				            					<td class="main">
<?		if ($error == true) {
	  		if ($entry_street_address_error == true) {
	   			echo tep_draw_input_field('po_supplier_street_address', $sInfo->po_supplier_street_address, 'maxlength="64" id="po_supplier_street_address"') . '&nbsp;' . ENTRY_STREET_ADDRESS_ERROR;
	  		} else {
	      		echo $sInfo->po_supplier_street_address . tep_draw_hidden_field('po_supplier_street_address');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_street_address', $sInfo->po_supplier_street_address, 'maxlength="64" id="po_supplier_street_address"', true);
	  	}
?>
												</td>
			          						</tr>
<?		if (ACCOUNT_SUBURB == 'true') { ?>
			          						<tr>
			            						<td class="main"><?=ENTRY_SUBURB?></td>
			            						<td class="main">
	<? 		if ($error == true) {
	      		if ($entry_suburb_error == true) {
	        		echo tep_draw_input_field('po_supplier_suburb', $sInfo->po_supplier_suburb, 'maxlength="64"') . '&nbsp;' . ENTRY_SUBURB_ERROR;
	      		} else {
	        		echo $sInfo->po_supplier_suburb . tep_draw_hidden_field('po_supplier_suburb');
	      		}
	    	} else {
	      		echo tep_draw_input_field('po_supplier_suburb', $sInfo->po_supplier_suburb, 'maxlength="64"');
	    	}
?>
												</td>
          									</tr>
<?		} ?>
			          						<tr>
				            					<td class="main"><?=ENTRY_POST_CODE?></td>
				            					<td class="main">
<?		if ($error == true) {
	    	if ($entry_post_code_error == true) {
	      		echo tep_draw_input_field('po_supplier_postcode', $sInfo->po_supplier_postcode, 'maxlength="10" id="po_supplier_postcode"') . '&nbsp;' . ENTRY_POST_CODE_ERROR;
	    	} else {
	      		echo $sInfo->po_supplier_postcode . tep_draw_hidden_field('po_supplier_postcode');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_postcode', $sInfo->po_supplier_postcode, 'maxlength="10" id="po_supplier_postcode"', true);
	  	}
?>
												</td>
				          					</tr>
				          					<tr>
	            								<td class="main"><?=ENTRY_CITY?></td>
	            								<td class="main">
<?		if ($error == true) {
	   		if ($entry_city_error == true) {
	   			echo tep_draw_input_field('po_supplier_city', $sInfo->po_supplier_city, 'maxlength="32" id="po_supplier_city"') . '&nbsp;' . ENTRY_CITY_ERROR;
	  		} else {
	   			echo $sInfo->po_supplier_city . tep_draw_hidden_field('po_supplier_city');
	  		}
	  	} else {
	   		echo tep_draw_input_field('po_supplier_city', $sInfo->po_supplier_city, 'maxlength="32" id="po_supplier_city"', true);
	  	}
?>
												</td>
          									</tr>
          									<tr>
				            					<td class="main"><?=ENTRY_COUNTRY?></td>
				            					<td class="main">
<?
  		if ($error == true) {
		 	if ($entry_country_error == false) {
	   			echo tep_get_country_name($sInfo->po_supplier_country_id) . tep_draw_hidden_field('po_supplier_country_id');
	   		} else {
	   			echo tep_get_country_list('po_supplier_country_id') . '&nbsp;' . '<span class="requiredInfo">' . ENTRY_COUNTRY_ERROR . '</span>';
	   		}
		} else {
			echo tep_get_country_list('po_supplier_country_id', $sInfo->po_supplier_country_id, ' id="po_supplier_country_id" onChange="refreshDynamicSelectOptions(this, \'state_div\', \'po_supplier_state\', \''.(int)$languages_id.'\', true);"') . '&nbsp;' . (tep_not_null(ENTRY_COUNTRY_TEXT) ? '<span class="requiredInfo">' . ENTRY_COUNTRY_ERROR . '</span>': '');
	  	}
?>
												</td>
	          								</tr>
<?		if (ACCOUNT_STATE == 'true') { ?>
				          					<tr>
				            					<td class="main"><?=ENTRY_STATE?></td>
				            					<td class="main">
				            						<div id="state_div" style="float:left;">
<?
	    	$zones_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
	    	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$sInfo->po_supplier_country_id . "' ORDER BY zone_name";
	    	$zones_result_sql = tep_db_query($zones_select_sql);
	    	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
	      		$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
	    	}
	    	if ($error == true) {
	    		if ($entry_state_error == true) {
					if ($entry_state_has_zones == true) {
		    			echo tep_draw_pull_down_menu('po_supplier_state', $zones_array, $sInfo->po_supplier_zone_id). '&nbsp;' . ENTRY_STATE_ERROR;
		    		} else {	    		
		    			echo tep_draw_input_field('po_supplier_state', $sInfo->po_supplier_state) . '&nbsp;' . ENTRY_STATE_ERROR;
		    		}
		    	} else {
		    		if($po_supplier_zone_name){
		    			echo $po_supplier_zone_name . tep_draw_hidden_field('po_supplier_state');
		    		} else {
		    			echo tep_draw_hidden_field('po_supplier_state');
		    		}
		    	}
	    	} else {
	    		if (count($zones_array) > 1) {
	    			echo tep_draw_pull_down_menu('po_supplier_state', $zones_array, $sInfo->po_supplier_zone_id, 'id="po_supplier_state"');
	      		} else {
		    	    echo tep_draw_input_field('po_supplier_state', $sInfo->po_supplier_state, 'id="po_supplier_state"');
				}			
			}
			echo '									</div>';
?>
												</td>
         									</tr>
<?		} ?>
										</table>
									</td>
          						</tr>
          						<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_CONTACT?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				          					<tr>
				            					<td class="main" width="20%"><?=ENTRY_TELEPHONE_NUMBER?></td>
				            					<td class="main">
<?		if ($error == true) {
	    	if ($entry_telephone_error == true) {
	     		echo tep_draw_input_field('po_supplier_telephone', $sInfo->po_supplier_telephone, 'maxlength="32" id="po_supplier_telephone"') . '&nbsp;' . ENTRY_TELEPHONE_NUMBER_ERROR;
	    	} else {
	     		echo $sInfo->po_supplier_telephone . tep_draw_hidden_field('po_supplier_telephone');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_telephone', $sInfo->po_supplier_telephone, 'maxlength="32" id="po_supplier_telephone"', true);
	  	}
?>
												</td>
			  								</tr>
			  								<tr>
				            					<td class="main"><?=ENTRY_FAX_NUMBER?></td>
				            					<td class="main">
<?		if ($processed == true) {
	    	echo $sInfo->po_supplier_fax . tep_draw_hidden_field('po_supplier_fax');
	  	} else {
	    	echo tep_draw_input_field('po_supplier_fax', $sInfo->po_supplier_fax, 'maxlength="32"');
	  	}
?>
												</td>
	          								</tr>
	          								<tr>
				            					<td class="main"><?=ENTRY_QQ_NUMBER?></td>
				            					<td class="main">
<?		if ($error == true) {
	    	if ($entry_qq_error == true) {
	     		echo tep_draw_input_field('po_supplier_qq', $sInfo->po_supplier_qq, 'maxlength="32" id="po_supplier_qq"') . '&nbsp;' . ENTRY_QQ_NUMBER_ERROR;
	    	} else {
	     		echo $sInfo->po_supplier_qq . tep_draw_hidden_field('po_supplier_qq');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_qq', $sInfo->po_supplier_qq, 'maxlength="32" id="po_supplier_qq"');
	  	}
?>
												</td>
	          								</tr>
	          								<tr>
				            					<td class="main"><?=ENTRY_MSN_ADDRESS?></td>
				            					<td class="main">
<?		if ($error == true) {
	    	if ($entry_msn_error == true) {
	     		echo tep_draw_input_field('po_supplier_msn', $sInfo->po_supplier_msn, 'maxlength="32" id="po_supplier_msn"') . '&nbsp;' . ENTRY_MSN_ADDRESS_ERROR;
	    	} else {
	     		echo $sInfo->po_supplier_msn . tep_draw_hidden_field('po_supplier_msn');
	    	}
	  	} else {
	    	echo tep_draw_input_field('po_supplier_msn', $sInfo->po_supplier_msn, 'maxlength="32" id="po_supplier_msn"');
	  	}
?>
												</td>
	          								</tr>
										</table>
									</td>
								</tr>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_PAYMENT_INFO?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				        					<tr>
				            					<td class="main" width="20%" valign="top"><?=ENTRY_SUPPLIER_PAYMENT_TERM?></td>
				            					<td class="main">
<?		if ($error == true) {
      		if ($payment_type_error == true || $payment_term_error == true || $payment_pay_wsc_error == true) {
                    echo tep_draw_radio_field('po_payment_type', 'g', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_CONSIGNMENT . '</br>' . 
                    tep_draw_radio_field('po_payment_type', 'c', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_PRE_PAYMENT . '</br>' . 
                    tep_draw_radio_field('po_payment_type', 'd', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_DTU_PAYMENT . '</br>' .
                    tep_draw_radio_field('po_payment_type', 't', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . tep_draw_input_field('po_payment_term', $sInfo->po_payment_term, 'maxlength="10" id="po_payment_term"') . TEXT_SUPPLIER_DAY_TERM . '&nbsp;&nbsp;&nbsp;&nbsp;' . tep_draw_input_field('po_days_pay_wsc', $sInfo->po_days_pay_wsc, 'size="5" maxlength="10" id="po_days_pay_wsc"') . TEXT_SUPPLIER_ACTUAL_WSC_CREDIT . '&nbsp;' . ENTRY_SUPPLIER_PAYMENT_TERM_ERROR;
      		} else {
      			switch($sInfo->po_payment_type) {
      				case 'g':
      					echo TEXT_SUPPLIER_CONSIGNMENT;
      					break;
      				case 'c':
      					echo TEXT_SUPPLIER_PRE_PAYMENT;
      					break;
                                case 'd':
      					echo TEXT_SUPPLIER_DTU_PAYMENT;
      					break;
      				case 't':
      					echo $sInfo->po_payment_term . ' ' . TEXT_SUPPLIER_DAY_TERM;
      					break;
      			}
        		echo tep_draw_hidden_field('po_payment_type');
        		echo tep_draw_hidden_field('po_payment_term');
      		}
    	} else {
            echo tep_draw_radio_field('po_payment_type', 'g', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_CONSIGNMENT . '</br>' . 
                 tep_draw_radio_field('po_payment_type', 'c', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_PRE_PAYMENT . '</br>' .
                 tep_draw_radio_field('po_payment_type', 'd', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_DTU_PAYMENT . '</br>' .
                 tep_draw_radio_field('po_payment_type', 't', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . tep_draw_input_field('po_payment_term', $sInfo->po_payment_term, 'maxlength="10" id="po_payment_term"') . TEXT_SUPPLIER_DAY_TERM . '&nbsp;&nbsp;&nbsp;&nbsp;' . tep_draw_input_field('po_days_pay_wsc', $sInfo->po_days_pay_wsc, 'size="5" maxlength="10" id="po_days_pay_wsc"') . TEXT_SUPPLIER_ACTUAL_WSC_CREDIT;
    	}
?>
												</td>
			  								</tr>
<?		echo $payment_html_content; ?>
				            			</table>
				        			</td>
				      			</tr>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
                                                        <tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_DELIVERY_ADDRESS?></td>
				      			</tr>
                                                        <tr>
                                                            <td class="formArea">
                                                                <table border="0" width="100%" cellspacing="2" cellpadding="2">
                                                                    <tr>
                                                                        <td class="main" width="20%"><?=ENTRY_DELIVERY_ADDRESS?></td>
                                                                        <td class="main">
<?php
    if ($error == true) {
        if ($entry_company_error == false) {
            echo tep_get_po_company_name($sInfo->po_supplier_company_code) . tep_draw_hidden_field('po_supplier_company_code');
        } else {
            echo tep_get_po_company_list('po_supplier_company_code') . '&nbsp;' . '<span class="requiredInfo">' . ENTRY_DELIVERY_ADDRESS_ERROR . '</span>';
        }
    } else {
        echo tep_get_po_company_list('po_supplier_company_code', $sInfo->po_supplier_company_code, ' id="po_supplier_company_code" ') . '&nbsp;' . (tep_not_null(ENTRY_DELIVERY_ADDRESS_TEXT) ? '<span class="requiredInfo">' . ENTRY_DELIVERY_ADDRESS_ERROR . '</span>': '');
    }
?>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_DISCOUNT_AGREEMENT?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
											<tr>
												<td colspan="2"><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
											</tr>
				        					<tr>
				            					<td class="main" width="20%"><?=ENTRY_SUPPLIER_AGREEMENT_START_DATE?></td>
				            					<td class="main">
<?		if ($error == true) {
			echo $sInfo->agreement_start_date;
			echo tep_draw_hidden_field('agreement_start_date');
		} else {
			echo tep_draw_input_field('agreement_start_date', $sInfo->agreement_start_date, 'id="agreement_start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.po_supplier_form.agreement_start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.po_supplier_form.agreement_start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>';
		}
?>
												</td>
			  								</tr>
				        					<tr>
				            					<td class="main" width="20%"><?=ENTRY_SUPPLIER_AGREEMENT_END_DATE?></td>
				            					<td class="main">
<?		if ($error == true) {
			echo $sInfo->agreement_end_date;
			echo tep_draw_hidden_field('agreement_end_date');
		} else {
			echo tep_draw_input_field('agreement_end_date', $sInfo->agreement_end_date, 'id="agreement_end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.po_supplier_form.agreement_end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.po_supplier_form.agreement_end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>';
		}
?>
												</td>
			  								</tr>
				        					<tr>
				            					<td class="main" valign="top" width="20%"><?=ENTRY_SUPPLIER_AGREEMENT_DISCOUNT_TERMS?></td>
				            					<td class="main">
<?		if ($error == true) {
			echo $sInfo->agreement_discount_terms;
			echo tep_draw_hidden_field('agreement_discount_terms');
		} else {
			echo tep_draw_textarea_field('agreement_discount_terms', 'soft', '60', '8', '');
		}
?>
												</td>
			  								</tr>
				            			</table>
				        			</td>
				      			</tr>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
            	      		    <tr>
            	        			<td class="customerFormAreaTitle" colspan="2"><?=CATEGORY_REMARKS?></td>
            	      			</tr>
            	      			<tr>
	            	        		<td class="formArea">
	            	        			<table border="0" width="100%" cellspacing="2" cellpadding="2">
	            	        				<tr>
            	            					<td class="main" valign="top" width="20%"><?=ENTRY_SUPPLIER_REMARKS?></td>
            	            					<td class="main">
            	            						<table border="0" width="95%" cellspacing="2" cellpadding="2">
            	            							<tr>
            	            								<td class="main"><?=tep_draw_textarea_field('po_supplier_remark', 'soft', '60', '8', '')?></td>
            	            							</tr>
            	            						</table>
            									</td>
            	          					</tr>
            	          				</table>
            	          			</td>
            	          		</tr>
            	          		<tr>
				    				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				  				</tr>
			      				<tr>
				        			<td align="right" class="main"><?=tep_button(BUTTON_INSERT, ALT_BUTTON_INSERT, '', ' name="insert" onClick="javascript:document.po_supplier_form.submit()"', 'inputButton')."&nbsp;&nbsp;&nbsp;".tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction', 'sID'))), '', 'inputButton')?></td>
				      			</tr>
				      		</table>
				      		</form>
				      		</fieldset>
				      	</td>
					</tr>
<?	} ?>
	      			<tr>
	      				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
<script language="javascript">
<!--
		function supplier_check_form() {
			var error = 0;
		  	var error_message = "<?=JS_ERROR?>";
		  	
		  	var po_supplier_firstname = DOMCall('po_supplier_firstname');
		  	var po_supplier_lastname = DOMCall('po_supplier_lastname');
		  	var po_supplier_email_address = DOMCall('po_supplier_email_address');
		  	var po_supplier_street_address = DOMCall('po_supplier_street_address');
		  	var po_supplier_postcode = DOMCall('po_supplier_postcode');
		  	var po_supplier_city = DOMCall('po_supplier_city');
		  	var po_supplier_telephone = DOMCall('po_supplier_telephone');
		  	var po_supplier_code = DOMCall('po_supplier_code');
		  	var po_supplier_country_id = DOMCall('po_supplier_country_id');
		  	
			if (po_supplier_firstname != null) {
			  	if (po_supplier_firstname.value.length < <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_FIRST_NAME; ?>";
			    	error = 1;
			  	}
			}
			
			if(po_supplier_lastname != null) {
			  	if (po_supplier_lastname.value.length < <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_LAST_NAME; ?>";
			    	error = 1;
			  	}
			}
		  	
			if (po_supplier_email_address != null) {
			  	if (po_supplier_email_address.value.length < <?php echo ENTRY_EMAIL_ADDRESS_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_EMAIL_ADDRESS; ?>";
			    	error = 1;
			  	}
			}
			
			if (po_supplier_street_address != null) {
			  	if (po_supplier_street_address.value.length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_ADDRESS; ?>";
			    	error = 1;
			  	}
			}
			
			if (po_supplier_postcode != null) {
			  	if (po_supplier_postcode.value.length < <?php echo ENTRY_POSTCODE_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_POST_CODE; ?>";
			    	error = 1;
			  	}
			}
			
			if (po_supplier_city != null) {
			  	if (po_supplier_city.value.length < <?php echo ENTRY_CITY_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_CITY; ?>";
			    	error = 1;
			  	}
			}
 				
			if (po_supplier_country_id.value == '') {
				error_message = error_message + "<?php echo JS_COUNTRY; ?>";
		      	error = 1;
		    } else {
		<?		if (ACCOUNT_STATE == 'true') { ?>
					if(po_supplier_state != null) {
				  		if (po_supplier_state.value == '') {
				       		error_message = error_message + "<?php echo JS_STATE; ?>";
				       		error = 1;
				    	}
				    }
		<?		} ?>
		    }
		  	
		  	if (po_supplier_telephone != null) {
			  	if (po_supplier_telephone.value.length < <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_TELEPHONE; ?>";
			    	error = 1;
			  	}
			}
		  	
		  	if (po_supplier_code != null) {
			  	if (po_supplier_code.value.length < 1) {
			    	error_message = error_message + "<?php echo JS_SUPPLIER_CODE; ?>";
			    	error = 1;
			  	}
			}
			
		  	if (error == 1) {
		    	alert(error_message);
		    	return false;
		  	} else {
		    	return true;
		  	}
		}
//-->
</script>
<?
} else if ($action == "edit_payment") {
	
	$sInfo = $po_suppliers_obj->get_po_supplier_payment_info($sID);
	
	$pm_object = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $sID, 'ALL');
	$payment_html_content = $pm_object->list_payment_account_book(FILENAME_PO_SUPPLIERS_LIST, '3');
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<td align="left">
									[<a href="<?=tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, 'suppID='.$sInfo->customers_id.'&subaction=show_po_list', 'NONSSL')?>" target="_blank">Purchase Order History</a>]
                                                                        [<a href="<?=tep_href_link(FILENAME_DTU_PAYMENT, 'suppID='.$sInfo->customers_id.'&subaction=show_dtu_list', 'NONSSL')?>" target="_blank">DTU Payment Request History</a>]
                                                                        [<a href="<?=tep_href_link(FILENAME_API_REPLENISH_PAYMENT, 'suppID='.$sInfo->customers_id.'&subaction=show_po_list', 'NONSSL')?>" target="_blank">API Replenish Payment Request History</a>]
                                    [<a href="<?=tep_href_link(FILENAME_CDK_PAYMENT, 'suppID='.$sInfo->customers_id.'&subaction=show_cdk_list', 'NONSSL')?>" target="_blank">PO CDK Payment Request History</a>]
								</td>
								<td align="right">
									<?=tep_draw_form('po_supplier_code_search', FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction')) . 'action=supplier_code_search', 'post')?>
									<?=ENTRY_SUPPLIER_CODE .'&nbsp;'. tep_draw_input_field('po_supplier_code_search', '', 'id="po_supplier_code_search" size="25"')?>
									</form>
								</td>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
        				<td width="100%">
							<?=tep_draw_form('po_pm_form', FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('subaction','sID')) . 'subaction=update_payment', 'POST'); ?>
							<?=tep_draw_hidden_field('po_supplier_update', '', ' id="po_supplier_update"'); ?>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="customerFormAreaTitle"><?=CATEGORY_ACCOUNT?></td>
								</tr>
								<tr>
									<td class="formArea">
										<table border="0" width="100%" cellspacing="2" cellpadding="2">
											<tr>
												<td class="main" valign="top" width="20%"><?=ENTRY_SUPPLIER_ID?></td>
												<td class="main" valign="top" colspan="2">
<?										      		echo $sInfo->customers_id . tep_draw_hidden_field('po_supplier_id', $sInfo->customers_id); ?>
												</td>
											</tr>
											<tr>
												<td class="main" valign="top" width="20%"><?=ENTRY_SUPPLIER_CODE?></td>
												<td class="main" valign="top" colspan="2">
<?										      		echo $sInfo->po_supplier_code; ?>
												</td>
											</tr>
											<tr>
												<td class="main" valign="top" width="20%"><?=ENTRY_SUPPLIER_REFERENCE_RUNNING_YEAR?></td>
												<td class="main" valign="top" colspan="2">
<?										      		echo $sInfo->po_supplier_po_ref_year; ?>
												</td>
											</tr>
											<tr>
												<td class="main" valign="top" width="20%"><?=ENTRY_SUPPLIER_REFERENCE_RUNNING_COUNT?></td>
												<td class="main" valign="top" width="20%">
<?										      		echo $sInfo->po_supplier_po_ref_counter; ?>
												</td>
												<td class="main"><?=ENTRY_SUPPLIER_REFERENCE_COUNTER_DESC?></td>
											</tr>
											<tr>
												<td class="main" width="20%"><?=ENTRY_SUPPLIER_STATUS?></td>
												<td class="main" valign="top" colspan="2">
<?													echo tep_draw_radio_field('po_supplier_status', '1', false, $sInfo->po_supplier_status) . '&nbsp;&nbsp;' . ACTIVE . '&nbsp;&nbsp;' . tep_draw_radio_field('po_supplier_status', '0', ($sInfo->po_supplier_status=='0'? true:false), $sInfo->po_supplier_status) . '&nbsp;&nbsp;' . NOT_ACTIVE; ?>
												</td>
											</tr>
						      				<tr>
							        			<td colspan="3" align="right" class="main"><?=(($edit_po_supplier_account_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check(\'status\');"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction', 'sID'))), '', 'inputButton')?></td>
							      			</tr>
										</table>
									</td>
								</tr>
<?
	if ($view_po_supplier_payment_info_permission || $edit_po_supplier_payment_info_permission) {
?>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_PAYMENT_INFO?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				        					<tr>
				            					<td class="main" width="20%" valign="top"><?=ENTRY_SUPPLIER_PAYMENT_TERM?></td>
				            					<td class="main">
<?		if ($error == true) {
      		if ($payment_type_error == true || $payment_term_error == true || $payment_pay_wsc_error == true) {
                    echo tep_draw_radio_field('po_payment_type', 'g', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_CONSIGNMENT . '</br>' . 
                         tep_draw_radio_field('po_payment_type', 'c', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_PRE_PAYMENT . '</br>' . 
                         tep_draw_radio_field('po_payment_type', 'd', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_DTU_PAYMENT . '</br>' . 
                         tep_draw_radio_field('po_payment_type', 't', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . tep_draw_input_field('po_payment_term', $sInfo->po_payment_term, 'size="5" maxlength="10" id="po_payment_term"') . TEXT_SUPPLIER_DAY_TERM . '&nbsp;&nbsp;&nbsp;&nbsp;' . tep_draw_input_field('po_days_pay_wsc', $sInfo->po_days_pay_wsc, 'size="5" maxlength="10" id="po_days_pay_wsc"') . TEXT_SUPPLIER_ACTUAL_WSC_CREDIT .
					'&nbsp;' . ENTRY_SUPPLIER_PAYMENT_TERM_ERROR;
      		} else {
      			switch ($sInfo->po_payment_type) {
      				case 'g':
      						echo TEXT_SUPPLIER_CONSIGNMENT;
      						break;
      				case 'c':
      						echo TEXT_SUPPLIER_PRE_PAYMENT;
      						break;
                                case 'd':
      						echo TEXT_SUPPLIER_DTU_PAYMENT;
      						break;
      				case 't':
      						echo $sInfo->po_payment_term . ' ' . TEXT_SUPPLIER_DAY_TERM;
      						break;
      			}
        		echo tep_draw_hidden_field('po_payment_type');
        		echo tep_draw_hidden_field('po_payment_term');
      		}
    	} else {
            echo tep_draw_radio_field('po_payment_type', 'g', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_CONSIGNMENT . '</br>' . 
                 tep_draw_radio_field('po_payment_type', 'c', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_PRE_PAYMENT . '</br>' . 
                 tep_draw_radio_field('po_payment_type', 'd', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . TEXT_SUPPLIER_DTU_PAYMENT . '</br>' . 
                 tep_draw_radio_field('po_payment_type', 't', false, $sInfo->po_payment_type) . '&nbsp;&nbsp;' . tep_draw_input_field('po_payment_term', $sInfo->po_payment_term, 'size="5" maxlength="10" id="po_payment_term"') . TEXT_SUPPLIER_DAY_TERM . '&nbsp;&nbsp;&nbsp;&nbsp;' . tep_draw_input_field('po_days_pay_wsc', $sInfo->po_days_pay_wsc, 'size="5" maxlength="10" id="po_days_pay_wsc"') . TEXT_SUPPLIER_ACTUAL_WSC_CREDIT;
    	}
?>
												</td>
			  								</tr>
						      				<tr>
						      					<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						      				</tr>
						      				<tr>
						      					<td colspan="2">
<?		echo $payment_html_content; ?>
												</td>
											</tr>
						      				<tr>
							        			<td colspan="2" align="right" class="main"><?=(($edit_po_supplier_payment_info_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check(\'payment\');"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction', 'sID'))), '', 'inputButton')?></td>
							      			</tr>
				            			</table>
				        			</td>
				      			</tr>
<?	} ?>
<?
	if ($view_po_supplier_discount_terms_permission || $edit_po_supplier_discount_terms_permission) {
?>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
                                                        <tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_DELIVERY_ADDRESS?></td>
				      			</tr>
                                                        <tr>
                                                            <td class="formArea">
                                                                <table border="0" width="100%" cellspacing="2" cellpadding="2">
                                                                    <tr>
                                                                        <td class="main" width="20%"><?=ENTRY_DELIVERY_ADDRESS?></td>
                                                                        <td class="main">
<?php
    if ($error == true) {
        if ($entry_company_error == false) {
            echo tep_get_po_company_name($sInfo->po_supplier_company_code) . tep_draw_hidden_field('po_supplier_company_code');
        } else {
            echo tep_get_po_company_list('po_supplier_company_code') . '&nbsp;' . '<span class="requiredInfo">' . ENTRY_DELIVERY_ADDRESS_ERROR . '</span>';
        }
    } else {
        echo tep_get_po_company_list('po_supplier_company_code', $sInfo->po_supplier_company_code, ' id="po_supplier_company_code" ') . '&nbsp;' . (tep_not_null(ENTRY_DELIVERY_ADDRESS_TEXT) ? '<span class="requiredInfo">' . ENTRY_DELIVERY_ADDRESS_ERROR . '</span>': '');
    }
?>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td colspan="2" align="right" class="main">
<?=(($edit_po_supplier_discount_terms_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check(\'delivery_address\');"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction', 'sID'))), '', 'inputButton')?>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_DISCOUNT_AGREEMENT?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
											<tr>
												<td colspan="2"><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
											</tr>
				        					<tr>
				            					<td class="main" width="20%"><?=ENTRY_SUPPLIER_AGREEMENT_START_DATE?></td>
				            					<td class="main">
<?		if ($error == true) {
			echo $sInfo->po_supplier_agreement_start_date;
			echo tep_draw_hidden_field('po_supplier_agreement_start_date');
		} else {
			echo tep_draw_input_field('po_supplier_agreement_start_date', $sInfo->po_supplier_agreement_start_date, 'id="po_supplier_agreement_start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.po_pm_form.po_supplier_agreement_start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.po_pm_form.po_supplier_agreement_start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>';
		}
?>
												</td>
			  								</tr>
				        					<tr>
				            					<td class="main" width="20%"><?=ENTRY_SUPPLIER_AGREEMENT_END_DATE?></td>
				            					<td class="main">
<?		if ($error == true) {
			echo $sInfo->po_supplier_agreement_end_date;
			echo tep_draw_hidden_field('po_supplier_agreement_end_date');
		} else {
			echo tep_draw_input_field('po_supplier_agreement_end_date', $sInfo->po_supplier_agreement_end_date, 'id="po_supplier_agreement_end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.po_pm_form.po_supplier_agreement_end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.po_pm_form.po_supplier_agreement_end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>';
		}
?>
												</td>
			  								</tr>
				        					<tr>
				            					<td class="main" valign="top" width="20%"><?=ENTRY_SUPPLIER_AGREEMENT_DISCOUNT_TERMS?></td>
				            					<td class="main">
<?		if ($error == true) {
			echo $sInfo->po_supplier_agreement_discount_terms;
			echo tep_draw_hidden_field('po_supplier_agreement_discount_terms');
		} else {
			echo tep_draw_textarea_field('po_supplier_agreement_discount_terms', 'soft', '60', '8', $sInfo->po_supplier_agreement_discount_terms);
		}
?>
												</td>
			  								</tr>
						      				<tr>
							        			<td colspan="2" align="right" class="main"><?=(($edit_po_supplier_discount_terms_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check(\'agreement\');"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction', 'sID'))), '', 'inputButton')?></td>
							      			</tr>
				            			</table>
				        			</td>
				      			</tr>
<?	} ?>
<?
	if ($view_po_supplier_payment_statistic_permission) {
?>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
            	      		    <tr>
            	        			<td class="customerFormAreaTitle" colspan="2"><?=CATEGORY_PAYMENT_STATISTIC?></td>
            	      			</tr>
            	      			<tr>
	            	        		<td class="formArea" colspan="2">
	            	        			<table border="0" width="100%" cellspacing="2" cellpadding="2">
											<tr><td class="main" valign="top" colspan="4" align="left">[<?='<a href="javascript:void();" onClick="getPOSupplierPaymentsStatistic('.$sID.');">Show Statistic Data</a>'?>]</td></tr>
	            	        				<tr>
            	            					<td class="main" valign="top" width="25%"><?=ENTRY_SUPPLIER_STATISTIC_STOCK_RECEIVED_NO_PAYMENT?></td>
            	            					<td class="main" valign="top" width="1%" align="center">:</td>
            	            					<td class="main" valign="top"><div id="stock_received_div">0.00</div></td>
            	            					<td class="main" valign="top" width="50%">&nbsp;</td>
            	            				</tr>
	            	        				<tr>
            	            					<td class="main" valign="top" width="25%"><?=ENTRY_SUPPLIER_STATISTIC_PAYMENT_SENT_NO_STOCK?></td>
            	            					<td class="main" valign="top" width="1%" align="center">:</td>
            	            					<td class="main" valign="top"><div id="payment_sent_div">0.00</div></td>
            	            					<td class="main" valign="top" width="50%">&nbsp;</td>
            	            				</tr>
	            	        				<tr>
            	            					<td class="main" valign="top" width="25%"><?=ENTRY_SUPPLIER_STATISTIC_TEXT_INCLUDING_SUPPLIER_CREDIT?></td>
            	            					<td class="main" valign="top" width="1%" align="center">(-)</td>
            	            					<td class="main" valign="top">&nbsp;</td>
            	            					<td class="main" valign="top" width="50%">&nbsp;</td>
            	            				</tr>
	            	        				<tr>
            	            					<td class="main" valign="top" colspan="3"><?=tep_draw_separator('pixel_black.gif', '100%', '1')?></td>
            	            					<td class="main" valign="top" width="50%"></td>
            	            				</tr>
	            	        				<tr>
            	            					<td class="main" valign="top" width="25%"><?=ENTRY_SUPPLIER_STATISTIC_NET_OUTSTANDING_PAYMENT?></td>
            	            					<td class="main" valign="top" width="1%" align="center">:</td>
            	            					<td class="main" valign="top"><div id="net_outstanding_div">0.00</div></td>
            	            					<td class="main" valign="top" width="50%">&nbsp;</td>
            	            				</tr>
	            	        				<tr>
            	            					<td class="main" valign="top" colspan="3"><?=tep_draw_separator('pixel_black.gif', '100%', '1')?></td>
            	            					<td class="main" valign="top" width="50%"></td>
            	            				</tr>
	            	        				<tr>
            	            					<td class="main" valign="top" colspan="3"><?=ENTRY_SUPPLIER_STATISTIC_TEXT_OUTSTANDING_STOCK?></td>
            	            					<td class="main" valign="top" width="50%">&nbsp;</td>
            	            				</tr>
	            	        				<tr>
            	            					<td class="main" valign="top" colspan="4"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
            	            				</tr>
	            	        				<tr>
            	            					<td class="main" valign="top" width="25%"><?=ENTRY_SUPPLIER_STATISTIC_PO_PENDING_PAYMENT?></td>
            	            					<td class="main" valign="top" width="1%" align="center">:</td>
            	            					<td class="main" valign="top"><div id="po_pending_pay_div">0.00</div></td>
            	            					<td class="main" valign="top" width="50%">&nbsp;</td>
            	            				</tr>
	            	        				<tr>
            	            					<td class="main" valign="top" width="25%"><?=ENTRY_SUPPLIER_STATISTIC_SUPPLIER_CREDIT?></td>
            	            					<td class="main" valign="top" width="1%" align="center">:</td>
            	            					<td class="main" valign="top"><div id="supplier_credit_div">0.00</div></td>
            	            					<td class="main" valign="top" width="50%">&nbsp;</td>
            	            				</tr>
						      				<tr>
							        			<td align="right" class="main" colspan="4"><?=tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction', 'sID'))), '', 'inputButton')?></td>
							      			</tr>
            	            			</table>
            	          			</td>
            	          		</tr>
<?	} ?>
<?
	if ($view_po_supplier_remark_permission || $edit_po_supplier_remark_permission) {
?>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
            	      		    <tr>
            	        			<td class="customerFormAreaTitle" colspan="2"><?=CATEGORY_REMARKS?></td>
            	      			</tr>
            	      			<tr>
	            	        		<td class="formArea" colspan="2">
	            	        			<table border="0" width="100%" cellspacing="2" cellpadding="2">
	            	        				<tr>
            	            					<td class="main" valign="top" width="15%"><?=ENTRY_SUPPLIER_REMARKS?></td>
            	            					<td class="main">
            	            						<table border="0" width="95%" cellspacing="2" cellpadding="2">
<?
    	$po_supplier_remarks_select_sql = "SELECT date_remarks_added, remarks, remarks_added_by FROM " . TABLE_CUSTOMERS_REMARKS_HISTORY . " WHERE customers_id = '" . $sInfo->customers_id . "' ORDER BY date_remarks_added ASC";
    	$po_supplier_remarks_result_sql = tep_db_query($po_supplier_remarks_select_sql);
    	while($po_supplier_remarks_row = tep_db_fetch_array($po_supplier_remarks_result_sql)) {
    		echo '							            <tr>
    											            <td class="main">';
    		if (tep_not_null($po_supplier_remarks_row["date_remarks_added"])) {
    			if (is_numeric($po_supplier_remarks_row["remarks_added_by"])) {
        			$admin_query = tep_db_query("SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $po_supplier_remarks_row["remarks_added_by"] . "'");
        			if ($admin_info = tep_db_fetch_array($admin_query))
        				$remark_by_admin_email_address = $admin_info["admin_email_address"];
        			else
        				$remark_by_admin_email_address = $po_supplier_remarks_row["remarks_added_by"];
        		} else {
        			$remark_by_admin_email_address = $po_supplier_remarks_row["remarks_added_by"];
        		}
       			if($remark_by_admin_email_address == "customer") {
   					$remark_by_admin_email_address = "Customer";
   				}
   				echo '<b>' . $po_supplier_remarks_row["date_remarks_added"].'&nbsp;(by '.$remark_by_admin_email_address.')</b><br>';
    		}
    		echo nl2br($po_supplier_remarks_row["remarks"]);
    		echo '								            </td>
    										            </tr>';
    	}
?>
            	            							<tr>
            	            								<td class="main"><?=tep_draw_textarea_field('po_supplier_remark', 'soft', '60', '8', '')?></td>
            	            							</tr>
            	            						</table>
            									</td>
            	          					</tr>
						      				<tr>
							        			<td align="right" class="main" colspan="2"><?=(($edit_po_supplier_remark_permission)?tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' onClick="javascript:return po_form_check(\'remark\');"', 'inputButton')."&nbsp;&nbsp;&nbsp;":'').tep_button(BUTTON_CANCEL, ALT_BUTTON_CANCEL, tep_href_link(FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction', 'sID'))), '', 'inputButton')?></td>
							      			</tr>
            	          				</table>
            	          			</td>
            	          		</tr>
<?	} ?>
				      		</table>
				      		</form>
<script language="javascript">
<!--
function po_form_check(update_type) {
	jQuery('#po_supplier_update').val(update_type);
	document.po_pm_form.submit();
	return true;
}
//-->
</script>
				      	</td>
					</tr>
	      			<tr>
	      				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
<?
} else {  /* LISTING PAGE */
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<? if ($insert_po_supplier_permission) { ?>
					<tr>
						<td>
							[<a href="<?=tep_href_link(FILENAME_PO_SUPPLIERS_LIST, 'action=new_supplier')?>" target="_blank">Add Supplier</a>]
						</td>
					</tr>
<? } ?>
<?
	$list_colspan_count = 14;
	if ($view_po_supplier_discount_terms_permission) {  $list_colspan_count++; }
	
	$po_supplier_id_search_str = '1';
	if ($action == 'id_search' && tep_not_null($_REQUEST['po_supplier_id_search'])) {
		$search_supp_id = tep_db_prepare_input($_REQUEST['po_supplier_id_search']);
		$po_supplier_id_search_str = " ps.po_suppliers_id = '".$search_supp_id."'";
	}
	
	$po_supplier_code_search_str = '1';
	if ($action == 'code_search' && tep_not_null($_REQUEST['po_supplier_code_search'])) {
		$search_supp_code = tep_db_prepare_input($_REQUEST['po_supplier_code_search']);
		$po_supplier_code_search_str = " ps.po_supplier_code = '".$search_supp_code."'";
	}
	
	$po_supplier_select_sql = "	select ps.po_suppliers_id, ps.po_supplier_code, ps.po_supplier_po_ref_year, ps.po_supplier_po_ref_counter, ps.po_payment_type, ps.po_payment_term, ps.po_supplier_status, 
									ps.po_supplier_agreement_start_date, ps.po_supplier_agreement_end_date, ps.po_supplier_agreement_discount_terms 
							    from " . TABLE_PO_SUPPLIERS . " as ps 
								where " . $po_supplier_id_search_str . "
								and " . $po_supplier_code_search_str . "
								order by ps.po_suppliers_id";
	
	$page_split_object = new splitPageResults($_REQUEST["page"], MAX_DISPLAY_SEARCH_RESULTS, $po_supplier_select_sql, $sql_numrows);
	$po_supplier_result_sql = tep_db_query($po_supplier_select_sql);
	
	$po_supplier_status_array = array(	'1'=> array('name' => 'icon_status_green', 'alt' => IMAGE_ICON_STATUS_GREEN, 'alt2' => IMAGE_ICON_STATUS_GREEN_LIGHT), 
										'0'=> array('name' => 'icon_status_red', 'alt' => IMAGE_ICON_STATUS_RED, 'alt2' => IMAGE_ICON_STATUS_RED_LIGHT)
									);
?>
					<tr>
						<td valign="top">
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td width="12%" valign="top" class="reportBoxHeading">
										<?=TABLE_HEADING_SUPPLIER_ID?><br>
									</td>
									<td width="12%" valign="top" class="reportBoxHeading">
										<?=TABLE_HEADING_SUPPLIER_CODE?>
									</td>
									<td rowspan="2" width="12%" valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_REFERENCE_RUNNING_COUNT?></td>
									<td rowspan="2" width="10%" valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_LASTNAME?></td>
									<td rowspan="2" width="10%" valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_FIRSTNAME?></td>
									<td rowspan="2" width="10%" valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_ADDRESS?></td>
									<td rowspan="2" width="8%" valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_TELEPHONE?></td>
									<td rowspan="2" width="8%" valign="top" align="left" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_FAX?></td>
<? if ($view_po_supplier_discount_terms_permission) { ?>
									<td rowspan="2" valign="top" align="left" class="reportBoxHeading" nowrap><?=TABLE_HEADING_SUPPLIER_AGREEMENT?></td>
<? } ?>
									<td rowspan="2" valign="top" align="left" class="reportBoxHeading" nowrap><?=TABLE_HEADING_SUPPLIER_PAYMENT_TYPE?></td>
									<td rowspan="2" width="5%" valign="top" align="center" class="reportBoxHeading"><?=TABLE_HEADING_DATE_CREATED?></td>
									<td rowspan="2" width="5%" valign="top" align="center" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_STATUS?></td>
									<td rowspan="2" width="5%" valign="top" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
									<td rowspan="2" width="1%" valign="top" class="reportBoxHeading"><?=tep_draw_checkbox_field('select_all', '', false, '', 'id="select_all" title="Select or deselect all suppliers" onclick="javascript:void(setCheckboxes(\'po_suppliers_list_form\',\'select_all\',\'po_supplier_batch\'));"')?></td>
								</tr>
								<tr>
									<td width="12%" valign="top" class="reportBoxHeading">
										<?=tep_draw_form('po_supplier_id_search', FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction')) . 'action=id_search', 'post', 'onSubmit="check_id_search_form(); return false;"')?>
										<?=tep_draw_input_field('po_supplier_id_search', '', 'id="po_supplier_id_search" size="7"')?>
										</form>
									</td>
									<td width="12%" valign="top" class="reportBoxHeading">
										<?=tep_draw_form('po_supplier_code_search', FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction')) . 'action=code_search', 'post', 'onSubmit="check_code_search_form(); return false;"')?>
										<?=tep_draw_input_field('po_supplier_code_search', '', 'id="po_supplier_code_search" size="7"')?>
										</form>
									</td>
								<tr>
<?							echo tep_draw_form('po_suppliers_list_form', FILENAME_PO_SUPPLIERS_LIST, tep_get_all_get_params(array('subaction')) . 'subaction=update_list', 'post', ''); ?>
<?
	if (tep_db_num_rows($po_supplier_result_sql)) {
		$row_count = 0;
		while ($po_supplier_row = tep_db_fetch_array($po_supplier_result_sql)) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
			
			$customer_info_select_sql = "SELECT s.customers_id, s.customers_firstname, s.customers_lastname, 
                                                    s.customers_telephone, s.customers_fax, s.customers_msn, s.customers_qq, 
                                                    si.customers_info_date_account_created, count(pr.publishers_replenish_id) as total_api_publishers,
                                                    ab.entry_street_address, ab.entry_suburb, ab.entry_postcode, ab.entry_city, 
                                                    ab.entry_state, ab.entry_country_id, ab.entry_zone_id 
                                                    FROM " . TABLE_CUSTOMERS . " AS s
                                                    LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS si
                                                        ON si.customers_info_id = s.customers_id
                                                    LEFT JOIN " . TABLE_ADDRESS_BOOK . " AS ab
                                                        ON (ab.customers_id = s.customers_id AND ab.address_book_id = s.customers_default_address_id)
                                                    LEFT JOIN " . TABLE_PUBLISHERS_REPLENISH . " AS pr
                                                        ON pr.publishers_supplier_id = s.customers_id
                                                    WHERE s.customers_id = '".$po_supplier_row['po_suppliers_id']."'";
			$customer_info_result_sql = tep_db_query($customer_info_select_sql);
			$customer_info_row = tep_db_fetch_array($customer_info_result_sql);
			
			$po_supplier_id = $po_supplier_row["po_suppliers_id"];
			$po_supplier_address = $customer_info_row["entry_street_address"] .' '. $customer_info_row["entry_suburb"] .' '. $customer_info_row["entry_postcode"] .' '. $customer_info_row["entry_city"] .' '. tep_get_zone_name($customer_info_row["entry_country_id"], $customer_info_row["entry_state"], '') .' '. tep_get_country_name($customer_info_row["entry_country_id"]);
			
			switch($po_supplier_row['po_payment_type']) {
                            case 'g':
                                $po_supplier_payment_term = 'consignment';
                                $supplier_po_history = '<a href="' . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, 'subaction=show_po_list&suppID=' . $po_supplier_id) . '" target="_blank">' . tep_image(DIR_WS_ICONS."orders.gif", "View PO History") . '</a>';
                                break;
                            case 'c':
                                $po_supplier_payment_term = 'pre-payment';
                                $supplier_po_history = '<a href="' . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, 'subaction=show_po_list&suppID=' . $po_supplier_id) . '" target="_blank">' . tep_image(DIR_WS_ICONS."orders.gif", "View PO History") . '</a>';
                                break;
                            case 't':
                                $po_supplier_payment_term = $po_supplier_row['po_payment_term'].' day-term';
                                $supplier_po_history = '<a href="' . tep_href_link(FILENAME_EDIT_PURCHASE_ORDERS, 'subaction=show_po_list&suppID=' . $po_supplier_id) . '" target="_blank">' . tep_image(DIR_WS_ICONS."orders.gif", "View PO History") . '</a>';
                                break;
                            case 'd':
                                $po_supplier_payment_term = 'dtu payment';
                                $supplier_po_history = '<a href="' . tep_href_link(FILENAME_DTU_PAYMENT, 'subaction=show_dtu_list&suppID=' . $po_supplier_id) . '" target="_blank">' . tep_image(DIR_WS_ICONS."dtu_orders.gif", "View DTU PO History") . '</a>';
                                break;
			}
                        
                        // Set for API Replenish PO History link
                        $supplier_api_history = "";
                        if ($customer_info_row['total_api_publishers'] > 0) {
                            $supplier_api_history = '<a href="' . tep_href_link(FILENAME_API_REPLENISH_PAYMENT, 'subaction=show_po_list&suppID=' . $po_supplier_id) . '" target="_blank">' . tep_image(DIR_WS_ICONS."api_orders.gif", "View API PO History") . '</a>';
                        }
			
			$po_supplier_agreement_info = '';
			if ($po_supplier_row["po_supplier_agreement_start_date"] != '0000-00-00 00:00:00') {
				$po_supplier_agreement_info .= '<b>Deal Start Date:</b> '.$po_supplier_row["po_supplier_agreement_start_date"].'<br>';
				$po_supplier_agreement_info .= '<b>Deal Expiry Date:</b> '.$po_supplier_row["po_supplier_agreement_end_date"].'<br>';
				$po_supplier_agreement_info .= '<b>Discount Terms:</b><br>'.nl2br($po_supplier_row["po_supplier_agreement_discount_terms"]);
			}
			
			/*$po_supplier_payment_info = '';
			$pm_object = new payment_module_info($_SESSION['login_id'], $_SESSION['login_email_address'], $po_supplier_id, '3');
			$supplier_payment_accounts = $pm_object->_get_current_payment_accounts($po_supplier_id, 'customers', '3');
			if (count($supplier_payment_accounts)) {
				foreach ($supplier_payment_accounts as $book_id => $pm_data_array) {
					$po_supplier_payment_info .= (strlen($po_supplier_payment_info)>0 ? '<br>' : '').'<b>'.$pm_data_array['pm_name'].'</b>';
					foreach ($pm_data_array['field'] as $pmf_data) {
						$po_supplier_payment_info .= '<br>'.$pmf_data['payment_methods_fields_title'].':'.$pmf_data['payment_methods_fields_value'];
					}
				}
			}*/
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top"><?=$po_supplier_row["po_suppliers_id"]?></td>
									<td class="reportRecords" valign="top"><?=$po_supplier_row["po_supplier_code"]?></td>
									<td class="reportRecords" valign="top"><?=sprintf("%02s-%04s",$po_supplier_row["po_supplier_po_ref_year"],$po_supplier_row["po_supplier_po_ref_counter"])?></td>
									<td class="reportRecords" valign="top"><?=$customer_info_row["customers_lastname"]?></td>
									<td class="reportRecords" valign="top"><?=$customer_info_row["customers_firstname"]?></td>
									<td class="reportRecords" valign="top"><?=$po_supplier_address?></td>
									<td class="reportRecords" valign="top"><?=$customer_info_row["customers_telephone"]?></td>
									<td class="reportRecords" valign="top"><?=$customer_info_row["customers_fax"]?></td>
<? if ($view_po_supplier_discount_terms_permission) { ?>
									<td class="reportRecords" valign="top" nowrap><?=$po_supplier_agreement_info?></td>
<? } ?>
									<td class="reportRecords" valign="top" nowrap><?=$po_supplier_payment_term?></td>
									<td class="reportRecords" align="center" valign="top"><?=tep_date_short($customer_info_row["customers_info_date_account_created"], PREFERRED_DATE_FORMAT)?></td>
									<td class="reportRecords" align="center" valign="top" nowrap>
									<?
										foreach ($po_supplier_status_array as $status_id => $img_res) {
											if ((int)$po_supplier_row["po_supplier_status"] == (int)$status_id) {
												echo tep_image(DIR_WS_IMAGES . $img_res['name'] . '.gif', $img_res['alt'], 10, 10) . '&nbsp;&nbsp;';
											} else {
												echo '<a href="' . tep_href_link(FILENAME_PO_SUPPLIERS_LIST, 'action=set_supplier_status&flag='.(int)$status_id.'&sID='.$po_supplier_id.'&page='.$_REQUEST["page"]) . '">' . tep_image(DIR_WS_IMAGES . $img_res['name'] . '_light.gif', $img_res['alt2'], 10, 10) . '</a>&nbsp;&nbsp;';
											}
										}
									?>
									</td>
									<td align="left" class="reportRecords" valign="top" nowrap>&nbsp;
                                                                            <a href="<?=tep_href_link(FILENAME_CUSTOMERS, 'action=edit&cID='.$po_supplier_id.'&page='.$_REQUEST["page"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit Supplier Profile", "", "", 'align="top"')?></a>
                                                                            <a href="<?=tep_href_link(FILENAME_PO_SUPPLIERS_LIST, 'action=edit_payment&sID='.$po_supplier_id.'&page='.$_REQUEST["page"])?>"><?=tep_image(DIR_WS_ICONS."edit_payment.gif", "Edit Supplier Payment", "", "", 'align="top"')?></a>
                                                                            <?=$supplier_po_history?>
                                                                            <?=$supplier_api_history?>
										<? 
											if ($po_supplier_row['po_payment_type']=='g') {
												echo '<a href="'. tep_href_link(FILENAME_CDK_PAYMENT, 'subaction=show_cdk_list&suppID=' . $po_supplier_id) . '" target="_blank">' . tep_image(DIR_WS_ICONS."consignment_orders.gif", "View Consignment Payment History") .'</a>';
											}
										?>
									</td>
									<td class="reportRecords" valign="top">
                                                                            <?=tep_draw_checkbox_field('po_supplier_batch[]', $po_supplier_id, false)?>
									</td>
								</tr>
<?
			$row_count++;
		}
		
		if ($row_count) {
?>
								<tr>
									<td colspan="<?=$list_colspan_count?>" align="right">
									<?
										$batch_action_array = array(array('id' => '', 'text' => 'With selected:'),
																	array('id' => 'Active', 'text' => 'Set as active'),
																	array('id' => 'Inactive', 'text' => 'Set as inactive')
																	);
										echo tep_draw_pull_down_menu('batch_action', $batch_action_array) . '&nbsp;';
										echo tep_submit_button('Go', 'Go', 'name="multi_submit_btn" onClick="return confirmBatchAction()"', 'inputButton');
									?>
									</td>
								</tr>
							</form>
								<script>
								<!--
									document.getElementById("po_supplier_id_search").focus();
									
									function confirmBatchAction() {
										if (trim_str(document.po_suppliers_list_form.batch_action.value) == '') {
											alert('Please select your batch action!');
											return false;
										} else {
											answer = confirm('Are you sure to perform "' + document.po_suppliers_list_form.batch_action[document.po_suppliers_list_form.batch_action.selectedIndex]['text'] + '" batch action?');
											if (answer !=0) {
												return true;
											}
											return false;
										}
									}
									
									function check_id_search_form() {
									    var supp_id_search = trim_str(document.getElementById('po_supplier_id_search').value);
										if (supp_id_search != '') {
											if (!validateInteger(supp_id_search)) {
												alert("The supplier ID is not a valid interger value!");
												document.getElementById('po_supplier_id_search').focus();
												document.getElementById('po_supplier_id_search').select();
												return false;
											} else {
												document.po_supplier_id_search.submit();
											}
										}
									}
									
									function check_code_search_form() {
									    var supp_code_search = trim_str(document.getElementById('po_supplier_code_search').value);
										if (supp_code_search == '') {
											alert("The supplier code is empty!");
											document.getElementById('po_supplier_code_search').focus();
											document.getElementById('po_supplier_code_search').select();
											return false;
										} else {
											document.po_supplier_code_search.submit();
										}
									}
								//-->
								</script>
<?		}
	}
?>
							</table>
			   			</td>
			   		</tr>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td class="smallText" valign="top" NOWRAP><?=$page_split_object->display_count($sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_SUPPLIERS)?></td>
									<td class="smallText" align="right"><?=$page_split_object->display_links($sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_REQUEST['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont'))."cont=1")?></td>
								</tr>
							</table>
						</td>
					</tr>
<? } /* LISTING PAGE */ ?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>