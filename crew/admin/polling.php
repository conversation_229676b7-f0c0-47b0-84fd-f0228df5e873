<?

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'polling.php');
require(DIR_WS_FUNCTIONS . 'custom_product.php');

$poll_object = new polling($login_id, $login_email_address);
$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

switch($subaction) {
	case "insert_poll":
		$subaction_res = $poll_object->insert_poll($_REQUEST, $messageStack);
		tep_redirect(tep_href_link(FILENAME_POLLING, tep_get_all_get_params(array('action', 'subaction'))));
		break;
	case "update_poll":
		$subaction_res = $poll_object->update_poll($_REQUEST, $messageStack);
		tep_redirect(tep_href_link(FILENAME_POLLING, tep_get_all_get_params(array('action', 'subaction'))));
		break;
	case "delete_comment":
		$subaction_res = $poll_object->delete_comment($_REQUEST, $messageStack);
		tep_redirect(tep_href_link(FILENAME_POLLING, tep_get_all_get_params(array('subaction'))));
		break;
	default:
		// Nothing to perform
		break;
}

switch($action) {
	case "poll_flag":
		$subaction_res = $poll_object->set_poll_status($_REQUEST, $messageStack);
		tep_redirect(tep_href_link(FILENAME_POLLING, tep_get_all_get_params(array('action', 'subaction'))));
		break;
	case "new_poll":
		$header_title = HEADER_FORM_ADD_POLL_TITLE;
		$form_content = $poll_object->add_poll(FILENAME_POLLING);
		break;
	case "edit_poll":
		$header_title = HEADER_FORM_EDIT_POLL_TITLE;
		$form_content = $poll_object->edit_poll(FILENAME_POLLING, $_REQUEST['id']);
		break;
	case "view_poll_stat":
		$header_title = HEADER_FORM_VIEW_POLL_STAT_TITLE;
		$form_content = $poll_object->show_result(FILENAME_POLLING, $_REQUEST['id']);
		break;
	case "delete_poll":
		$subaction_res = $poll_object->delete_poll($_REQUEST, $messageStack);
		tep_redirect(tep_href_link(FILENAME_POLLING, tep_get_all_get_params(array('action', 'subaction'))));
		break;
	default:
		$header_title = HEADER_FORM_POLL_TITLE;
		$form_content = $poll_object->show_poll_list(FILENAME_POLLING);
		break;
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
		<title><?php echo TITLE; ?></title>
		<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
		<script language="javascript" src="includes/general.js"></script>
		<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
		<script language="javascript" src="includes/javascript/polling_xmlhttp.js"></script>
	</head>
	<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
		<!-- header //-->
		<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
		<!-- header_eof //-->
		
		<!-- body //-->
		<table border="0" width="100%" cellspacing="2" cellpadding="2">
  			<tr>
    			<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    				<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
					<!-- left_navigation_eof //-->
    				</table>
    			</td>
				<!-- body_text //-->
    			<td width="100%" valign="top">
    				<table border="0" width="100%" cellspacing="0" cellpadding="2">
      					<tr>
        					<td>
        						<table border="0" width="100%" cellspacing="0" cellpadding="0">
          							<tr>
							            <td class="pageHeading" valign="top"><?=$header_title?></td>
	            					</tr>
	            					<tr>
				        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      				</tr>
	            					<tr>
				        				<td width="100%" valign="top"><?=$form_content?></td>
									</tr>
        						</table>
        					</td>
      					</tr>
      				</table>
      			</td>
      		</tr>
      	</table>
	</body>
</html>