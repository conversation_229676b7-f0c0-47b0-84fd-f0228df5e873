<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/xml');

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'polling.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_POLLING);

$uid = (isset($_REQUEST['uid']) ? $_REQUEST['uid'] : '');
$pid = (isset($_REQUEST['pid']) ? $_REQUEST['pid'] : '');
$cid = (isset($_REQUEST['cid']) ? $_REQUEST['cid'] : '');
$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$poll_object = new polling($login_id, $login_email_address);

if (tep_not_null($action)) {
	switch($action) {
		case 'display':
			
			break;
		case 'delete':
			break;
		default:
			break;
	}
}
echo '<response>';
echo '<comments><![CDATA[';
echo '<table border="0" width="100%" align="left" cellspacing="0" cellpadding="0">';
echo '<tr>';
echo '<td align="center">';
echo '<table border="0" width="90%" cellspacing="0" cellpadding="2">';
echo '<tr>';
echo '<td class="systemBoxText">';
echo $poll_object->display_comments(FILENAME_POLLING, $pid);
echo '</td>';
echo '</tr>';
echo '</table>';
echo '</td>';
echo '</tr>';
echo '</table>';
echo ']]></comments>';
echo '</response>';
?>