<?
include_once('../includes/configure.php');
include_once('../includes/database_tables.php');
include_once('../includes/filenames.php');
include_once('../includes/functions/database.php');
include_once('../includes/functions/html_output.php');
include_once('../includes/classes/currencies.php');
include_once('../includes/classes/supplier_order.php');
include_once('../includes/classes/supplier_payment.php');
include_once('../includes/classes/custom_product.php');

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

$currencies = new currencies();
$currencies->set_decimal_places(4);

$action = $_REQUEST['action'];

$admin_id = tep_db_prepare_input($_REQUEST['aid']);
$language_id = (int)$_REQUEST['_lang'];

if (isset($language_id) && $language_id > 0) {
	$language_dir_select_sql = "SELECT directory FROM " . TABLE_LANGUAGES . " WHERE languages_id = '" . $language_id . "'";
	$language_dir_result_sql = tep_db_query($language_dir_select_sql);
	$language_dir_row = tep_db_fetch_array($language_dir_result_sql);
	
	if (file_exists('../includes/languages/' . $language_dir_row["directory"] . ".php")) {
		include_once('../includes/languages/' . $language_dir_row["directory"] . ".php");
	}
	
	if (file_exists('../includes/languages/' . $language_dir_row["directory"] . '/' . "suppliers_orders.php")) {
		include_once('../includes/languages/' . $language_dir_row["directory"] . '/' . "suppliers_orders.php");
	}
}

function tep_not_null($value) {
	if (is_array($value)) {
    	if (sizeof($value) > 0) {
        	return true;
      	} else {
        	return false;
      	}
    } else {
    	if ( (is_string($value) || is_int($value)) && ($value != '') && ($value != 'NULL') && (strlen(trim($value)) > 0)) {
        	return true;
      	} else {
        	return false;
      	}
	}
}

function tep_round($value, $precision) {
    if (PHP_VERSION < 4) {
      	$exp = pow(10, $precision);
      	return round($value * $exp) / $exp;
    } else {
      	return round($value, $precision);
    }
}

function tep_array_serialize($arr) {
	return urlencode(serialize($arr));
}

function tep_array_unserialize($val) {
	return unserialize(urldecode(stripslashes($val)));
}

if ($action == 'new_payment') {
	$files_actions_select_sql = "	SELECT afa.admin_files_actions_id 
									FROM " . TABLE_ADMIN_FILES_ACTIONS . " AS afa 
									INNER JOIN " . TABLE_ADMIN_FILES . " AS af 
										ON afa.admin_files_id=af.admin_files_id 
									INNER JOIN " . TABLE_ADMIN . " AS adm 
										ON adm.admin_id = '" . tep_db_input($admin_id) . "' 
									WHERE af.admin_files_name='" . FILENAME_SUPPLIERS_ORDERS . "' 
										AND af.admin_files_is_boxes=0 
										AND afa.admin_files_actions_key='SUPPLIER_ORDER_PAYMENT_INFO' 
										AND FIND_IN_SET(adm.admin_groups_id, afa.admin_groups_id)";
	$files_actions_result_sql = tep_db_query($files_actions_select_sql);
	$view_payment_info_permission = tep_db_num_rows($files_actions_result_sql) ? true : false;

	$payment_orders_array = (isset($_REQUEST['s_o']) && count($_REQUEST['s_o']) ? tep_array_unserialize($_REQUEST['s_o']) : array());
	
	$supplier_personal_select_sql = "	SELECT s.supplier_firstname, s.supplier_lastname, s.supplier_code, s.supplier_payment_paypal, s.supplier_payment_bank_name, s.supplier_payment_bank_swift_code, s.supplier_payment_bank_address, s.supplier_payment_bank_telephone, s.supplier_payment_bank_account_name, s.supplier_payment_bank_account_number, sg.supplier_groups_name 
										FROM " . TABLE_SUPPLIER . " AS s 
										LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
											ON s.supplier_groups_id = sg.supplier_groups_id 
										WHERE s.supplier_id = '" . tep_db_input($_REQUEST["sid"]) . "'";
	$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
	$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
	
	for ($i=0; $i < count($payment_orders_array); $i++) {
		$payable_amount = supplier_order::get_order_total_payable_amount($payment_orders_array[$i]['id']);
		$paid_amount = supplier_payment::get_order_paid_amount($payment_orders_array[$i]['id']);
		
		$unpaid_amount = (double)$payable_amount - (double)$paid_amount;
		
		$payment_orders_array[$i]['payable'] = (double)$payable_amount;
		$payment_orders_array[$i]['balance'] = (double)$unpaid_amount;
		
		if (strtolower($payment_orders_array[$i]['amt']) != 'f') {	// Partial Payment
			if ($payment_orders_array[$i]['amt'] > 0 && $payment_orders_array[$i]['amt'] < (double)$unpaid_amount) {
				;
			} else {
				$payment_orders_array[$i]['amt'] = $payment_orders_array[$i]['balance'];
			}
		} else {
			$payment_orders_array[$i]['amt'] = $payment_orders_array[$i]['balance'];
		}
		
		if ($payment_orders_array[$i]['amt'] < $payable_amount) {
			$payment_orders_array[$i]['pay_type'] = 'p';
		} else {
			$payment_orders_array[$i]['pay_type'] = 'f';
		}
	}
} else if ($action == 'custom_product_new_payment') {
	$files_actions_select_sql = "	SELECT afa.admin_files_actions_id 
									FROM " . TABLE_ADMIN_FILES_ACTIONS . " AS afa 
									INNER JOIN " . TABLE_ADMIN_FILES . " AS af 
										ON afa.admin_files_id=af.admin_files_id 
									INNER JOIN " . TABLE_ADMIN . " AS adm 
										ON adm.admin_id = '" . tep_db_input($admin_id) . "' 
									WHERE af.admin_files_name='" . FILENAME_PROGRESS_REPORT . "' 
										AND af.admin_files_is_boxes=0 
										AND afa.admin_files_actions_key='SUPPLIER_CP_PAYMENT_DETAILS' 
										AND FIND_IN_SET(adm.admin_groups_id, afa.admin_groups_id)";
	$files_actions_result_sql = tep_db_query($files_actions_select_sql);
	$view_cp_payment_info_permission = tep_db_num_rows($files_actions_result_sql) ? true : false;
	
	$payment_orders_array = (isset($_REQUEST['s_o']) && count($_REQUEST['s_o']) ? tep_array_unserialize($_REQUEST['s_o']) : array());
	
	$supplier_personal_select_sql = "	SELECT s.supplier_firstname, s.supplier_lastname, s.supplier_code, s.supplier_payment_paypal, s.supplier_payment_bank_name, s.supplier_payment_bank_swift_code, s.supplier_payment_bank_address, s.supplier_payment_bank_telephone, s.supplier_payment_bank_account_name, s.supplier_payment_bank_account_number, sg.supplier_groups_name 
										FROM " . TABLE_SUPPLIER . " AS s 
										LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
											ON s.supplier_groups_id = sg.supplier_groups_id 
										WHERE s.supplier_id = '" . tep_db_input($_REQUEST["sid"]) . "'";
	$supplier_personal_result_sql = tep_db_query($supplier_personal_select_sql);
	$supplier_personal_row = tep_db_fetch_array($supplier_personal_result_sql);
	
	for ($i=0; $i < count($payment_orders_array); $i++) {
		$payable_amount = custom_product::get_order_total_payable_amount($payment_orders_array[$i]['id']);
		$payment_orders_array[$i]['payable'] = $payable_amount;
		$payment_orders_array[$i]['amt'] = $payable_amount;
		$payment_orders_array[$i]['pay_type'] = 'f';
		
		
		//$paid_amount = supplier_payment::get_order_paid_amount($payment_orders_array[$i]['id']);
		
		//$unpaid_amount = (double)$payable_amount - (double)$paid_amount;
		/*
		$payment_orders_array[$i]['payable'] = (double)$payable_amount;
		$payment_orders_array[$i]['balance'] = (double)$unpaid_amount;
		
		if (strtolower($payment_orders_array[$i]['amt']) != 'f') {	// Partial Payment
			if ($payment_orders_array[$i]['amt'] > 0 && $payment_orders_array[$i]['amt'] < (double)$unpaid_amount) {
				;
			} else {
				$payment_orders_array[$i]['amt'] = $payment_orders_array[$i]['balance'];
			}
		} else {
			$payment_orders_array[$i]['amt'] = $payment_orders_array[$i]['balance'];
		}
		
		if ($payment_orders_array[$i]['amt'] < $payable_amount) {
			$payment_orders_array[$i]['pay_type'] = 'p';
		} else {
			$payment_orders_array[$i]['pay_type'] = 'f';
		}
		*/
	}
}

?>
<html>
<head>
	<title>Make Payment</title>
	<meta http-equiv="pragma" content="no-cache">
	<style>
		body {color:#000000;margin-top:2px;}
		input, textarea, table, td, tr {font-size:11px;font-family:tahoma,sans-serif}
		h2 {padding:0px; margin: 1px}
		.pageHeading {font-family: Verdana, Arial, sans-serif; font-size: 14px; color: #8D847F; font-weight: bold;}
		.labelText { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
		.valueText { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
	</style>
	<script language="JavaScript">
		var Nav4 = ((navigator.appName == "Netscape") && (parseInt(navigator.appVersion) >= 4));
		function centerWin() {var NS = false; if (document.all) { w = document.body.clientWidth; h = document.body.clientHeight; NS = true; } else if (document.layers) { ;	} if (!NS) { self.moveTo((self.screen.width - self.outerWidth) / 2, (self.screen.height - self.outerHeight) / 2); } else { self.moveTo((self.screen.width-document.body.clientWidth) / 2, (self.screen.height-document.body.clientHeight) / 2); } }
		function closeme() { window.close()	}
		function handleCANCEL() {
			if (opener) {
				opener.payment_init(); 
			} else if (window.showModalDialog) {
				var caller_win = window.dialogArguments;
				caller_win.payment_init();
			}
			
			closeme();
		}
		function handleOK() {
			var r = document.getElementById('remark').value;
			var n = document.getElementById('notify').checked ? '1' : '0';
			
			if (opener) {
				opener.payment_info(r, n);
			} else if (window.showModalDialog) {
				var caller_win = window.dialogArguments;
				caller_win.payment_info(r, n);
			} else {
				alert("No action is taken.")
			}
			
			closeme();
		}
	</script>
</head>
<body onLoad="centerWin(); if (opener) opener.blockEvents()" onUnload="if (opener) opener.unblockEvents()">
	<table border="0" width="95%" cellspacing="0" cellpadding="0">
		<tr>
			<td width="10px">&nbsp;</td>
			<td>
<?
if ($action == 'new_payment' && $view_payment_info_permission) {
?>
				<table width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr><td><h2>New Payment</h2></td></tr>
					<tr>
						<td>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td valign="top">
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
			    								<td width="40%" class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER?></b></td>
			    								<td class="valueText"><?=$supplier_personal_row["supplier_firstname"] . ' ' . $supplier_personal_row["supplier_lastname"] . ' ['.$supplier_personal_row["supplier_groups_name"].']'?></td>
			  								</tr>
			  							</table>
			  						</td>
			  					</tr>
								<tr>
									<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_PAYMENT_INFO?></b></td>
								</tr>
								<tr>
									<td valign="top">
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
				          					<tr>
												<td valign="top">
													<table width="100%" border="0" cellspacing="0" cellpadding="2">
														<tr>
				            								<td width="40%" class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_PAYPAL?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row["supplier_payment_paypal"]?></td>
				          								</tr>
				          								<tr>
				            								<td class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_NAME?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row["supplier_payment_bank_name"]?></td>
				          								</tr>
				          								<tr>
							                				<td class="labelText" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE?></b></td>
							                				<td class="valueText"><?=$supplier_personal_row["supplier_payment_bank_swift_code"]?></td>
				          								</tr>
				          								<tr>
				            								<td class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row["supplier_payment_bank_address"]?></td>
				          								</tr>
				          								<tr>
				            								<td class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row["supplier_payment_bank_telephone"]?></td>
				          								</tr>
				          								<tr>
				            								<td class="labelText" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row['supplier_payment_bank_account_name']?></td>
				          								</tr>
				          								<tr>
											                <td class="labelText" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER?></b></td>
											                <td class="valueText"><?=$supplier_personal_row['supplier_payment_bank_account_number']?></td>
				          								</tr>
				        							</table>
				        						</td>
				        					</tr>
				        				</table>
				        			</td>
				        		</tr>
				        		<tr>
									<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_SELECTED_ORDERS?></b></td>
								</tr>
								<tr>
									<td valign="top">
										<table width="100%" border="0" cellspacing="0" cellpadding="0">
											<tr>
											    <td class="labelText" nowrap><?=TABLE_HEADING_POPUP_ORDER_NO?></td>
											    <td align="right" class="labelText" nowrap><?=TABLE_HEADING_POPUP_PAYABLE_AMOUNT?></td>
											    <td align="right" class="labelText" nowrap><?=TABLE_HEADING_POPUP_BAL?></td>
											    <td align="right" class="labelText" nowrap><?=TABLE_HEADING_POPUP_PAY?></td>
											</tr>
											<tr>
<?
	$total_payment_amt = 0;
	for ($i=0; $i < count($payment_orders_array); $i++) {		
		$payable_amt = $payment_orders_array[$i]['payable'];
		$balance_amt = $payment_orders_array[$i]['balance'];
		$pay_amt = $payment_orders_array[$i]['amt'];
		
		$total_payment_amt += $pay_amt;
		
		echo '								<tr>
												<td class="labelText" nowrap>'.$payment_orders_array[$i]['id'] . (strtolower($payment_orders_array[$i]['pay_type']) == 'p' ? '&nbsp;' . TEXT_PARTIAL_PAID_ORDER : '') .'</td>
												<td align="right" class="labelText" nowrap>'.$currencies->format($payable_amt).'</td>
												<td align="right" class="labelText" nowrap>'.$currencies->format($balance_amt).'</td>
												<td align="right" class="labelText" nowrap>'.$currencies->format($pay_amt).'</td>
											</tr>';
   	}
   	
   	if ($i > 0) {
   		echo '								<tr>
												<td class="labelText" colspan="2">&nbsp;</td>
												<td align="right" class="labelText" nowrap><b>'.sprintf(TEXT_TOTAL_AMOUNT, '').'</b></td>
												<td align="right" class="labelText" nowrap><b>'.$currencies->format($total_payment_amt).'</b></td>
											</tr>';
   	}
?>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr><td>&nbsp;</td></tr>
					<tr>
						<td>
							<form name="frm_p">
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td valign="top">Payment Remark:</td>
									<td><textarea name="remark" wrap="soft" cols="60" rows="5" id="remark"></textarea></td>
			    				</tr>
			    				<tr>
									<td>Show to Supplier:</td>
									<td><input type="checkbox" name="notify" value="1" id="notify"></td>
			    				</tr>
			    				<tr>
			    					<td colspan="2" align="center"><input type="button" value="Submit" title="Confirm make payment" onclick="handleOK();">&nbsp;&nbsp;<input type="button" value="Cancel" title="Cancel make payment" onclick="handleCANCEL();"></td>
				    			</tr>
				    		</table>
				    		</form>
				    	</td>
				    </tr>
				</table>
<?
} else if ($action == 'custom_product_new_payment' && $view_cp_payment_info_permission) {
	$currencies->set_decimal_places(3);	// for powerleveling payment
?>
				<table width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr><td><h2>New Payment</h2></td></tr>
					<tr>
						<td>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td valign="top">
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
			    								<td width="40%" class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER?></b></td>
			    								<td class="valueText"><?=$supplier_personal_row["supplier_firstname"] . ' ' . $supplier_personal_row["supplier_lastname"] . ' ['.$supplier_personal_row["supplier_groups_name"].']'?></td>
			  								</tr>
			  							</table>
			  						</td>
			  					</tr>
								<tr>
									<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_PAYMENT_INFO?></b></td>
								</tr>
								<tr>
									<td valign="top">
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
				          					<tr>
												<td valign="top">
													<table width="100%" border="0" cellspacing="0" cellpadding="2">
														<tr>
				            								<td width="40%" class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_PAYPAL?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row["supplier_payment_paypal"]?></td>
				          								</tr>
				          								<tr>
				            								<td class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_NAME?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row["supplier_payment_bank_name"]?></td>
				          								</tr>
				          								<tr>
							                				<td class="labelText" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE?></b></td>
							                				<td class="valueText"><?=$supplier_personal_row["supplier_payment_bank_swift_code"]?></td>
				          								</tr>
				          								<tr>
				            								<td class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row["supplier_payment_bank_address"]?></td>
				          								</tr>
				          								<tr>
				            								<td class="labelText" valign="top" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row["supplier_payment_bank_telephone"]?></td>
				          								</tr>
				          								<tr>
				            								<td class="labelText" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME?></b></td>
				            								<td class="valueText"><?=$supplier_personal_row['supplier_payment_bank_account_name']?></td>
				          								</tr>
				          								<tr>
											                <td class="labelText" nowrap><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER?></b></td>
											                <td class="valueText"><?=$supplier_personal_row['supplier_payment_bank_account_number']?></td>
				          								</tr>
				        							</table>
				        						</td>
				        					</tr>
				        				</table>
				        			</td>
				        		</tr>
				        		<tr>
									<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_SELECTED_ORDERS?></b></td>
								</tr>
								<tr>
									<td valign="top">
										<table width="100%" border="1" cellspacing="0" cellpadding="0">
											<tr>
											    <td class="labelText" nowrap><?=TABLE_HEADING_POPUP_ORDER_NO?></td>
											    <td align="right" class="labelText" nowrap><?=TABLE_HEADING_POPUP_PAYABLE_AMOUNT?></td>
											    <td align="right" class="labelText" nowrap><?=TABLE_HEADING_POPUP_PAY?></td>
											</tr>
											<tr>
<?
	$total_payment_amt = 0;
	for ($i=0; $i < count($payment_orders_array); $i++) {
		
		$orders_id_select_sql = "SELECT orders_id FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_products_id ='" . (int)$payment_orders_array[$i]['id'] . "'";
		$orders_id_result_sql = tep_db_query($orders_id_select_sql);
		$orders_id_row = tep_db_fetch_array($orders_id_result_sql);
		
		$payable_amt = $payment_orders_array[$i]['payable'];
		$pay_amt = $payment_orders_array[$i]['amt'];
		
		$total_payment_amt += $pay_amt;
		
		echo '								<tr>
												<td class="labelText" nowrap>'.$orders_id_row['orders_id'] . (strtolower($payment_orders_array[$i]['pay_type']) == 'p' ? '&nbsp;' . TEXT_PARTIAL_PAID_ORDER : '') .'</td>
												<td align="right" class="labelText" nowrap>'.$currencies->format($payable_amt).'</td>
												<td align="right" class="labelText" nowrap>'.$currencies->format($pay_amt).'</td>
											</tr>';
   	}
   	
   	if ($i > 0) {
   		echo '								<tr>
												<td class="labelText" colspan="1">&nbsp;</td>
												<td align="right" class="labelText" nowrap><b>'.sprintf(TEXT_TOTAL_AMOUNT, '').'</b></td>
												<td align="right" class="labelText" nowrap><b>'.$currencies->format($total_payment_amt).'</b></td>
											</tr>';
   	}
?>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr><td>&nbsp;</td></tr>
					<tr>
						<td>
							<form name="frm_p">
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td valign="top">Payment Remark:</td>
									<td><textarea name="remark" wrap="soft" cols="60" rows="5" id="remark"></textarea></td>
			    				</tr>
			    				<tr>
									<td>Show to Supplier:</td>
									<td><input type="checkbox" name="notify" value="1" id="notify"></td>
			    				</tr>
			    				<tr>
			    					<td colspan="2" align="center"><input type="button" value="Submit" title="Confirm make payment" onclick="handleOK();">&nbsp;&nbsp;<input type="button" value="Cancel" title="Cancel make payment" onclick="handleCANCEL();"></td>
				    			</tr>
				    		</table>
				    		</form>
				    	</td>
				    </tr>
				</table>
<?
}
?>
			</td>
		</tr>
	</table>
</body>
</html>