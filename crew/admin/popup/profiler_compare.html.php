<?
include_once('../includes/configure.php');
include_once('../includes/database_tables.php');
include_once('../includes/filenames.php');
include_once('../includes/functions/database.php');
include_once('../includes/functions/html_output.php');
include_once('../includes/classes/custom_product.php');

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

function tep_not_null($value) {
	if (is_array($value)) {
    	if (sizeof($value) > 0) {
        	return true;
      	} else {
        	return false;
      	}
    } else {
    	if ( (is_string($value) || is_int($value)) && ($value != '') && ($value != 'NULL') && (strlen(trim($value)) > 0)) {
        	return true;
      	} else {
        	return false;
      	}
	}
}

if (!function_exists('array_intersect_key')) {
	function array_intersect_key($arr1, $arr2) {
	   $res = array();
	   foreach($arr1 as $key=>$value) {
	       $push = true;
	       for ($i = 1; $i < func_num_args(); $i++) {
	           $actArray = func_get_arg($i);
	           if (gettype($actArray) != 'array') return false;
	           if (!array_key_exists($key, $actArray)) $push = false;
	       }
	       if ($push) $res[$key] = $arr1[$key];
	   }
	   return $res;
	}
}

$action = isset($_REQUEST['action']) ? tep_db_prepare_input($_REQUEST['action']) : '';
$currProfilerViewDate = isset($_REQUEST['currProfilerViewDate']) ? tep_db_prepare_input($_REQUEST['currProfilerViewDate']) : '';
$game_char_id = isset($_REQUEST['game_char_id']) ? tep_db_prepare_input($_REQUEST['game_char_id']) : '';
$date_compare_id = isset($_REQUEST['date_compare_id']) ? tep_db_prepare_input($_REQUEST['date_compare_id']) : '';
$path = isset($_REQUEST['path']) ? tep_db_prepare_input($_REQUEST['path']) : '';

$game_char_history_info_select_sql = "	SELECT game_char_history_id, game_char_history_date 
										FROM " . TABLE_GAME_CHAR_HISTORY. " 
										WHERE game_char_id ='" . (int)$game_char_id . "'";
$game_char_history_info_result_sql = tep_db_query($game_char_history_info_select_sql);

if (tep_db_num_rows($game_char_history_info_result_sql) > 0) {
	$history_count = 1;
	
	while ($game_char_history_info_row = tep_db_fetch_array($game_char_history_info_result_sql)) {
		if ($history_count <= $date_compare_id) {
			$profiler_date_list_array[$history_count] = array('game_char_history_id' => $game_char_history_info_row['game_char_history_id'], 'game_char_history_date' => $game_char_history_info_row['game_char_history_date']);
			$history_count++;
		} else {
			break;	
		}
	}
}

/*******************
	Compare bags
********************/

$game_char_history_id_select_sql = "	SELECT game_char_history_id, game_char_history_date 
										FROM " . TABLE_GAME_CHAR_HISTORY . " 
										WHERE game_char_id ='" . tep_db_input($game_char_id) . "' 
											AND game_char_history_date ='" . tep_db_input($currProfilerViewDate) . "'";
$game_char_history_id_result_sql = tep_db_query($game_char_history_id_select_sql);
$game_char_history_id_row = tep_db_fetch_array($game_char_history_id_result_sql);

$bag_compare_from_array = array();
$bag_compare_with_array = array();
$bag_item_remain_array = array();

$bags_compare_from_select_sql = "	SELECT * 
									FROM " . TABLE_CHAR_ITEM_HISTORY . " 
									WHERE game_char_id ='" . tep_db_input($game_char_id) . "' 
										AND game_char_history_id ='" . tep_db_input($game_char_history_id_row['game_char_history_id']) . "' 
										AND char_item_name != 'Keyring' 
										AND char_item_parent REGEXP \"(bank)?(Bag)([0-9])\"";

$bags_compare_from_result_sql = tep_db_query($bags_compare_from_select_sql);
while ($bags_compare_from_row = tep_db_fetch_array($bags_compare_from_result_sql)) {
	if (array_key_exists($bags_compare_from_row['char_item_texture'] . "'~'" . $bags_compare_from_row['char_item_name'] . "'~'" . $bags_compare_from_row['char_item_color'], $bag_compare_from_array)) {
		$bag_compare_from_array[$bags_compare_from_row['char_item_texture'] . "'~'" . $bags_compare_from_row['char_item_name'] . "'~'" . $bags_compare_from_row['char_item_color']]['qty'] = $bag_compare_from_array[$bags_compare_from_row['char_item_texture'] . "'~'" . $bags_compare_from_row['char_item_name'] . "'~'" . $bags_compare_from_row['char_item_color']]['qty'] + 1;
	} else {
		$bag_compare_from_array[$bags_compare_from_row['char_item_texture'] . "'~'" . $bags_compare_from_row['char_item_name'] . "'~'" . $bags_compare_from_row['char_item_color']] = array('qty' => 1);
	}
}

$bags_compare_with_select_sql = "	SELECT * 
									FROM " . TABLE_CHAR_ITEM_HISTORY . " 
									WHERE game_char_id ='" . tep_db_input($game_char_id) . "' 
										AND game_char_history_id='" . tep_db_input($profiler_date_list_array[$date_compare_id]['game_char_history_id']) . "' 
										AND char_item_name != 'Keyring' 
										AND char_item_parent REGEXP \"(bank)?(Bag)([0-9])\"";

$bags_compare_with_result_sql = tep_db_query($bags_compare_with_select_sql);
while ($bags_compare_with_row = tep_db_fetch_array($bags_compare_with_result_sql)) {
	if (array_key_exists($bags_compare_with_row['char_item_texture'] . "'~'" . $bags_compare_with_row['char_item_name'] . "'~'" . $bags_compare_with_row['char_item_color'], $bag_compare_with_array)) {
		$bag_compare_with_array[$bags_compare_with_row['char_item_texture'] . "'~'" . $bags_compare_with_row['char_item_name'] . "'~'" . $bags_compare_with_row['char_item_color']]['qty'] = $bag_compare_with_array[$bags_compare_with_row['char_item_texture'] . "'~'" . $bags_compare_with_row['char_item_name'] . "'~'" . $bags_compare_with_row['char_item_color']]['qty'] + 1;
	} else {
		$bag_compare_with_array[$bags_compare_with_row['char_item_texture'] . "'~'" . $bags_compare_with_row['char_item_name'] . "'~'" . $bags_compare_with_row['char_item_color']] = array('qty' => 1);
	}
}

/*******************
	Compare items
********************/
$item_compare_from_array = array();
$item_compare_with_array = array();
$bag_item_remain_array = array();

$char_item_storage_info_from_select_sql = "	SELECT cis.char_item_storage_name, cis.char_item_storage_color, cis.char_item_storage_texture, cis.char_item_storage_quantity 
											FROM " . TABLE_CHAR_ITEM_HISTORY . " AS cih 
											INNER JOIN " . TABLE_CHAR_ITEM_STORAGE . " AS cis 
												ON (cis.char_item_history_id = cih.char_item_history_id AND cis.char_item_storage_slot != 'ammo') 
											WHERE cih.game_char_id = '" . tep_db_input($game_char_id) . "' 
												AND game_char_history_id ='" . tep_db_input($game_char_history_id_row['game_char_history_id']) . "'";

$char_item_storage_info_from_result_sql = tep_db_query($char_item_storage_info_from_select_sql);
while ($char_item_storage_info_from_row = tep_db_fetch_array($char_item_storage_info_from_result_sql)) {
	if (array_key_exists($char_item_storage_info_from_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_color'], $item_compare_from_array)) {
		if (tep_not_null($char_item_storage_info_from_row['char_item_storage_quantity'])) {
			$item_compare_from_array[$char_item_storage_info_from_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_color']]['qty'] += $char_item_storage_info_from_row['char_item_storage_quantity'];
		} else {
			$item_compare_from_array[$char_item_storage_info_from_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_color']]['qty'] += 1;
		}
	} else {
		if (tep_not_null($char_item_storage_info_from_row['char_item_storage_quantity'])) {
			$item_compare_from_array[$char_item_storage_info_from_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_color']]['qty'] = $char_item_storage_info_from_row['char_item_storage_quantity'];
		} else {
			$item_compare_from_array[$char_item_storage_info_from_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_from_row['char_item_storage_color']]['qty'] = 1;
		}
	}
}

$char_item_storage_info_with_select_sql = "	SELECT cis.char_item_storage_name, cis.char_item_storage_color, cis.char_item_storage_texture, cis.char_item_storage_quantity 
											FROM " . TABLE_CHAR_ITEM_HISTORY . " AS cih 
											INNER JOIN " . TABLE_CHAR_ITEM_STORAGE . " AS cis 
												ON (cis.char_item_history_id = cih.char_item_history_id AND cis.char_item_storage_slot != 'ammo') 
											WHERE cih.game_char_id = '" . tep_db_input($game_char_id) . "' 
												AND game_char_history_id ='" . tep_db_input($profiler_date_list_array[$date_compare_id]['game_char_history_id']) . "'";

$char_item_storage_info_with_result_sql = tep_db_query($char_item_storage_info_with_select_sql);
while ($char_item_storage_info_with_row = tep_db_fetch_array($char_item_storage_info_with_result_sql)) {
	if (array_key_exists($char_item_storage_info_with_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_color'], $item_compare_with_array)) {
		if (tep_not_null($char_item_storage_info_with_row['char_item_storage_quantity'])) {
			$item_compare_with_array[$char_item_storage_info_with_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_color']]['qty'] += $char_item_storage_info_with_row['char_item_storage_quantity'];
		} else {
			$item_compare_with_array[$char_item_storage_info_with_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_color']]['qty'] += 1;
		}
	} else {
		if (tep_not_null($char_item_storage_info_with_row['char_item_storage_quantity'])) {
			$item_compare_with_array[$char_item_storage_info_with_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_color']]['qty'] = $char_item_storage_info_with_row['char_item_storage_quantity'];
		} else {
			$item_compare_with_array[$char_item_storage_info_with_row['char_item_storage_texture'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_name'] . "'~'" . $char_item_storage_info_with_row['char_item_storage_color']]['qty'] = 1;
		}
	}
}

if (date($profiler_date_list_array[$date_compare_id]['game_char_history_date']) > date($currProfilerViewDate)) {
	$date_before = $currProfilerViewDate;
	$date_after = $profiler_date_list_array[$date_compare_id]['game_char_history_date'];
	
	$bag_compare_with_result_array = array_diff(array_keys($bag_compare_with_array), array_keys($bag_compare_from_array));
	$bag_compare_from_result_array = array_diff(array_keys($bag_compare_from_array), array_keys($bag_compare_with_array));
	$item_compare_with_result_array = array_diff(array_keys($item_compare_with_array), array_keys($item_compare_from_array));
	$item_compare_from_result_array = array_diff(array_keys($item_compare_from_array), array_keys($item_compare_with_array));
	
	$bag_intersect_with_array = array_intersect_key($bag_compare_from_array, $bag_compare_with_array);
	$bag_intersect_from_array = array_intersect_key($bag_compare_with_array, $bag_compare_from_array);
	$item_intersect_with_array = array_intersect_key($item_compare_from_array, $item_compare_with_array);
	$item_intersect_from_array = array_intersect_key($item_compare_with_array, $item_compare_from_array);
	
	foreach ($bag_intersect_from_array as $bag_key => $bag_info_array) {
		if ($bag_intersect_with_array[$bag_key]['qty'] > $bag_intersect_from_array[$bag_key]['qty']) {
			$bag_item_remain_array[] = $bag_key;
			$bag_item_remain_qty_array[$item_key] = $bag_intersect_from_array[$bag_key]['qty'];
			$bag_compare_from_result_array[] = $bag_key;
			$bag_compare_from_result_qty_array[$bag_key] = $bag_intersect_with_array[$bag_key]['qty'] - $bag_intersect_from_array[$bag_key]['qty'];
		} else if ($bag_intersect_with_array[$bag_key]['qty'] < $bag_intersect_from_array[$bag_key]['qty']) {
			$bag_item_remain_array[] = $bag_key;
			$bag_item_remain_qty_array[$item_key] = $bag_intersect_with_array[$bag_key]['qty'];
			$bag_compare_with_result_array[] = $bag_key;
			$bag_compare_with_result_qty_array[$bag_key] = $bag_intersect_from_array[$bag_key]['qty'] - $bag_intersect_with_array[$bag_key]['qty'];
		} else {
			$bag_item_remain_array[] = $bag_key;
			$bag_intersect_with_array[$bag_key]['qty'] = $bag_intersect_with_array[$bag_key]['qty'];
		}
	}
	
	foreach ($item_intersect_from_array as $item_key => $item_info_array) {
		if ($item_intersect_with_array[$item_key]['qty'] > $item_intersect_from_array[$item_key]['qty']) {
			$bag_item_remain_array[] = $item_key;
			$bag_item_remain_qty_array[$item_key] = $item_intersect_from_array[$item_key]['qty'];
			$item_compare_from_result_array[] = $item_key;
			$item_compare_from_result_qty_array[$item_key] = $item_intersect_with_array[$item_key]['qty'] - $item_intersect_from_array[$item_key]['qty'];
		} else if ($item_intersect_with_array[$item_key]['qty'] < $item_intersect_from_array[$item_key]['qty']) {
			$bag_item_remain_array[] = $item_key;
			$bag_item_remain_qty_array[$item_key] = $item_intersect_with_array[$item_key]['qty'];
			$item_compare_with_result_array[] = $item_key;
			$item_compare_with_result_qty_array[$item_key] = $item_intersect_from_array[$item_key]['qty'] - $item_intersect_with_array[$item_key]['qty'];
		} else {
			$bag_item_remain_array[] = $item_key;
			$bag_item_remain_qty_array[$item_key] = $item_intersect_with_array[$item_key]['qty'];
		}
	}
} else {
	$date_before = $profiler_date_list_array[$date_compare_id]['game_char_history_date'];
	$date_after = $currProfilerViewDate;
	
	$bag_compare_with_result_array = array_diff(array_keys($bag_compare_from_array), array_keys($bag_compare_with_array));
	$bag_compare_from_result_array = array_diff(array_keys($bag_compare_with_array), array_keys($bag_compare_from_array));
	$item_compare_with_result_array = array_diff(array_keys($item_compare_from_array), array_keys($item_compare_with_array));
	$item_compare_from_result_array = array_diff(array_keys($item_compare_with_array), array_keys($item_compare_from_array));
	
	$bag_intersect_with_array = array_intersect_key($bag_compare_with_array, $bag_compare_from_array);
	$bag_intersect_from_array = array_intersect_key($bag_compare_from_array, $bag_compare_with_array);
	$item_intersect_with_array = array_intersect_key($item_compare_with_array, $item_compare_from_array);
	$item_intersect_from_array = array_intersect_key($item_compare_from_array, $item_compare_with_array);
	
	foreach ($bag_intersect_with_array as $bag_key => $bag_info_array) {
		if ($bag_intersect_with_array[$bag_key]['qty'] > $bag_intersect_from_array[$bag_key]['qty']) {
			$bag_item_remain_array[] = $bag_key;
			$bag_item_remain_qty_array[$bag_key] = $bag_intersect_from_array[$bag_key]['qty'];
			$bag_compare_from_result_array[] = $bag_key;
			$bag_compare_from_result_qty_array[$bag_key] = $bag_intersect_with_array[$bag_key]['qty'] - $bag_intersect_from_array[$bag_key]['qty'];
		} else if ($bag_intersect_with_array[$bag_key]['qty'] < $bag_intersect_from_array[$bag_key]['qty']) {
			$bag_item_remain_array[] = $bag_key;
			$bag_item_remain_qty_array[$bag_key] = $bag_intersect_with_array[$bag_key]['qty'];
			$bag_compare_with_result_array[] = $bag_key;
			$bag_compare_with_result_qty_array[$bag_key] = $bag_intersect_from_array[$bag_key]['qty'] - $bag_intersect_with_array[$bag_key]['qty'];
		} else {
			$bag_item_remain_array[] = $bag_key;
			$bag_item_remain_qty_array[$bag_key] = $bag_intersect_with_array[$bag_key]['qty'];
		}
	}
	
	foreach ($item_intersect_from_array as $item_key => $item_info_array) {
		if ($item_intersect_with_array[$item_key]['qty'] > $item_intersect_from_array[$item_key]['qty']) {
			$bag_item_remain_array[] = $item_key;
			$bag_item_remain_qty_array[$item_key] = $item_intersect_from_array[$item_key]['qty'];
			$item_compare_from_result_array[] = $item_key;
			$item_compare_from_result_qty_array[$item_key] = $item_intersect_with_array[$item_key]['qty'] - $item_intersect_from_array[$item_key]['qty'];
		} else if ($item_intersect_with_array[$item_key]['qty'] < $item_intersect_from_array[$item_key]['qty']) {
			$bag_item_remain_array[] = $item_key;
			$bag_item_remain_qty_array[$item_key] = $item_intersect_with_array[$item_key]['qty'];
			$item_compare_with_result_array[] = $item_key;
			$item_compare_with_result_qty_array[$item_key] = $item_intersect_from_array[$item_key]['qty'] - $item_intersect_with_array[$item_key]['qty'];
		} else {
			$bag_item_remain_array[] = $item_key;
			$bag_item_remain_qty_array[$item_key] = $item_intersect_with_array[$item_key]['qty'];
		}
	}
}
?>
<html>
<head>
	<title>Make Payment</title>
	<meta http-equiv="pragma" content="no-cache">
	<style>
		body {color:#000000;margin-top:2px;}
		input, textarea, table, td, tr {font-size:11px;font-family:tahoma,sans-serif}
		h2 {padding:0px; margin: 1px}
		.pageHeading {font-family: Verdana, Arial, sans-serif; font-size: 14px; color: #8D847F; font-weight: bold;}
		.labelText { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
		.valueText { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
	</style>
	<link rel="stylesheet" type="text/css" href="../includes/stylesheet.css">
	<script language="JavaScript">
		var Nav4 = ((navigator.appName == "Netscape") && (parseInt(navigator.appVersion) >= 4));
		function centerWin() {var NS = false; if (document.all) { w = document.body.clientWidth; h = document.body.clientHeight; NS = true; } else if (document.layers) { ;	} if (!NS) { self.moveTo((self.screen.width - self.outerWidth) / 2, (self.screen.height - self.outerHeight) / 2); } else { self.moveTo((self.screen.width-document.body.clientWidth) / 2, (self.screen.height-document.body.clientHeight) / 2); } }
	</script>
</head>
<body onLoad="centerWin(); if (opener) opener.blockEvents()" onUnload="if (opener) opener.unblockEvents()">
	<table border="0" width="90%" cellspacing="2" cellpadding="0" align="center">
		<tr>
			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
		<tr>
			<td class="ordersBoxHeading" align="center" width="50%"><?=$date_before?></td>
			<td class="ordersBoxHeading" align="center" width="50%"><?=$date_after?></td>
		</tr>
<?	
if (count($bag_compare_from_result_array) > 0) {
	if (ENABLE_SSL == 'true') {
    	$path = HTTPS_CATALOG_SERVER . DIR_WS_CATALOG . DIR_WS_INTERFACE;
  	} else {
    	$path = HTTP_CATALOG_SERVER . DIR_WS_CATALOG . DIR_WS_INTERFACE;
  	}
	
	foreach ($bag_compare_from_result_array as $num => $bags_array) {
		list($bag_image_path, $bag_name, $bag_color) = explode("'~'", $bags_array);
		
		if (tep_not_null($bag_color)) {
			if (strlen($bag_color) > 6) {
				$bag_color = substr($bag_color, 2, 6);
			}
		} else {
			$bag_color = 'ffffff';
		}
?>
		<tr>
			<td align="center">
				<table border="0" width="100%" cellspacing="2" cellpadding="2" bgcolor="#1B1916">
					<tr>
						<td class="ordersRecords" width="65%"><font color="<?='#'.$bag_color?>"><?=$bag_name?></font></td>
						<td width="21%"><?=tep_image($path. 'img/' . preg_replace("|\\\\|","/", $bag_image_path) . '.jpg')?></td>
						<td class="ordersRecords"><font color="white">x <?=(tep_not_null($bag_compare_from_result_qty_array[$bags_array])) ? $bag_compare_from_result_qty_array[$bags_array] : '1'?></font></td>
					</tr>
				</table>
			</td>
			<td align="center" bgcolor="#1B1916">&nbsp;</td>
		</tr>
<?
	}
}

if (count($item_compare_from_result_array) > 0) {
	foreach ($item_compare_from_result_array as $num => $items_array) {
		list($item_image_path, $item_name, $item_color) = explode("'~'", $items_array);
		
		if (tep_not_null($item_color)) {
			if (strlen($item_color) > 6) {
				$item_color = substr($item_color, 2, 6);
			}
		} else {
			$item_color = 'ffffff';
		}
?>
		<tr>
			<td align="center">
				<table border="0" width="100%" cellspacing="2" cellpadding="0" bgcolor="#1B1916">
					<tr>
						<td class="ordersRecords" width="65%"><font color="<?='#'.$item_color?>"><?=$item_name?></font></td>
						<td width="21%"><?=tep_image($path . 'img/' . preg_replace("|\\\\|","/", $item_image_path) . '.jpg')?></td>
						<td class="ordersRecords"><font color="white">x <?=(tep_not_null($item_compare_from_result_qty_array[$items_array])) ? $item_compare_from_result_qty_array[$items_array] : '1'?></font></td>
					</tr>
				</table>
			</td>
			<td align="center" bgcolor="#1B1916">&nbsp;</td>
		</tr>
<?
	}
}
/*
if (count($bag_item_remain_array) > 0) {
	foreach ($bag_item_remain_array as $num => $items_array) {
		list($item_image_path, $item_name, $item_color) = explode("'~'", $items_array);
		
		if (tep_not_null($item_color)) {
			if (strlen($item_color) > 6) {
				$item_color = substr($item_color, 2, 6);
			}
		} else {
			$item_color = 'ffffff';
		}
?>
		<tr>
			<td align="center">
				<table border="0" width="100%" cellspacing="2" cellpadding="0" bgcolor="#1B1916">
					<tr>
						<td class="ordersRecords" width="65%"><font color="<?='#'.$item_color?>"><?=$item_name?></font></td>
						<td width="21%"><?=tep_image($path . DIR_WS_INTERFACE . 'img/' . preg_replace("|\\\\|","/", $item_image_path) . '.jpg')?></td>
						<td class="ordersRecords"><font color="white">x <?=(tep_not_null($bag_item_remain_qty_array[$items_array])) ? $bag_item_remain_qty_array[$items_array] : '1'?></font></td>
					</tr>
				</table>
			</td>
			<td align="center">
				<table border="0" width="100%" cellspacing="2" cellpadding="0" bgcolor="#1B1916">
					<tr>
						<td class="ordersRecords" width="65%"><font color="<?='#'.$item_color?>"><?=$item_name?></font></td>
						<td width="21%"><?=tep_image($path . DIR_WS_INTERFACE . 'img/' . preg_replace("|\\\\|","/", $item_image_path) . '.jpg')?></td>
						<td class="ordersRecords"><font color="white">x <?=(tep_not_null($bag_item_remain_qty_array[$items_array])) ? $bag_item_remain_qty_array[$items_array] : '1'?></font></td>
					</tr>
				</table>
			</td>
		</tr>
<?
	}
}
*/
if (count($bag_compare_with_result_array) > 0) {
	foreach ($bag_compare_with_result_array as $num => $bags_array) {
		list($bag_image_path, $bag_name, $bag_color) = explode("'~'", $bags_array);
		
		if (tep_not_null($bag_color)) {
			if (strlen($bag_color) > 6) {
				$bag_color = substr($bag_color, 2, 6);
			}
		} else {
			$bag_color = 'ffffff';
		}
?>
		<tr>
			<td align="center" bgcolor="#1B1916">&nbsp;</td>
			<td align="center">
				<table border="0" width="100%" cellspacing="2" cellpadding="0" bgcolor="#1B1916">
					<tr>
						<td class="ordersRecords" width="65%"><font color="<?='#'.$bag_color?>"><?=$bag_name?></font></td>
						<td width="21%"><?=tep_image($path . 'img/' . preg_replace("|\\\\|","/", $bag_image_path) . '.jpg')?></td>
						<td class="ordersRecords"><font color="white">x <?=(tep_not_null($bag_compare_from_result_qty_array[$bags_array])) ? $bag_compare_from_result_qty_array[$bags_array] : '1'?></font></td>
					</tr>
				</table>
			</td>
		</tr>
<?
	}
}

if (count($item_compare_with_result_array) > 0) {
	foreach ($item_compare_with_result_array as $num => $items_array) {
		list($item_image_path, $item_name, $item_color) = explode("'~'", $items_array);
		
		if (tep_not_null($item_color)) {
			if (strlen($item_color) > 6) {
				$item_color = substr($item_color, 2, 6);
			}
		} else {
			$item_color = 'ffffff';
		}
?>
		<tr>
			<td align="center" bgcolor="#1B1916">&nbsp;</td>
			<td>
				<table border="0" width="100%" cellspacing="2" cellpadding="0" bgcolor="#1B1916">
					<tr>
						<td class="ordersRecords" width="65%"><font color="<?='#'.$item_color?>"><?=$item_name?></font></td>
						<td width="21%"><?=tep_image($path . 'img/' . preg_replace("|\\\\|","/", $item_image_path) . '.jpg')?></td>
						<td class="ordersRecords"><font color="white">x <?=(tep_not_null($item_compare_with_result_qty_array[$items_array])) ? $item_compare_with_result_qty_array[$items_array] : '1'?></font></td>
					</tr>
				</table>
			</td>
		</tr>
<?
	}
}
?>
	</table>
</body>
</html>