<?
/*
    Developer: Azri
*/

require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'po_suppliers.php');
require_once(DIR_WS_CLASSES . 'consignment_payment.php');
require_once(DIR_WS_CLASSES . 'custom_product_code.php');
require(DIR_WS_CLASSES . 'log.php');

define('DISPLAY_PRICE_DECIMAL', 4);

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);
$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
$cdk_obj = new consignment_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
$cpc_obj = new custom_product_code();
$log_object = new log_files($_SESSION['login_id']);

/**
 * Add remarks to CD Key
 */
function tep_add_Remarks($custom_product_id,$remarks)
{
    global $messageStack, $log_object;

    $log_object->insert_cdkey_history_log('admin', $custom_product_id, $remarks);
}

// Check Permission
$allow_create_cdk_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_NEW_PO');
$allow_set_cdk_status_permission = tep_admin_files_actions(FILENAME_CDK_PAYMENT, 'CDK_PO_SET_PAID_STATUS');
$view_cdkey_images_permission = tep_admin_files_actions(FILENAME_CDKEY, 'CP_VIEW_CDKEY_IMAGES');

// Set all POST
$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');
$supplier_id = (isset($_REQUEST['s_id']) ? $_REQUEST['s_id'] : '');
$page = (isset($_REQUEST['page']) ? $_REQUEST['page'] : 1);
$sortby = (isset($_REQUEST['sortby']) ? $_REQUEST['sortby'] : 'sort_by_date');
$po_refid = (isset($_REQUEST['po_refid']) ? $_REQUEST['po_refid'] : '');
$po_currency = (isset($_REQUEST['curr']) ? $_REQUEST['curr'] : '');
$filterby_s = (isset($_REQUEST['filterby_s']) ? $_REQUEST['filterby_s'] : '');

if (tep_not_null($action)) {
    switch ($action) {
        case 'showcdk':
            $cdk_id = tep_db_prepare_input($_REQUEST['cdk_id']);
            $ajax_remark = tep_db_prepare_input($_REQUEST['remark']);
            $ajax_result = 0;
            $ajax_data = ERROR_FILE_NOT_FOUND;
            $permissionView = TRUE;
            $userID = $_SESSION["login_id"];
            $currentDate = date("Y-m-d H:i:s");
            
            $cdkey_image_select_sql = " SELECT file_type, code_uploaded_by, products_id, to_s3, code_date_added
                                        FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                        WHERE custom_products_code_id = '".$cdk_id."'";
            $cdkey_image_result_sql = tep_db_query($cdkey_image_select_sql);
            $cdkey_image_row = tep_db_fetch_array($cdkey_image_result_sql);

            //Mohamad - CDKey view limit and captured
            if ($_SESSION['login_email_address'] != $cdkey_image_row['code_uploaded_by'] AND ($view_cdkey_images_permission AND isset($ajax_remark))) {
                
                $getCurrentTotalSql = "SELECT cdkey_total_view, cdkey_limit FROM " . TABLE_CDKEY_VIEW . " WHERE cdkey_user_id = '" . $userID . "'";
                $getCurrentTotalView = tep_db_query($getCurrentTotalSql);
                if ($totalViewFetch = tep_db_fetch_array($getCurrentTotalView)) {
                    $totalView = $totalViewFetch['cdkey_total_view'];
                    $cdkey_limit = $totalViewFetch['cdkey_limit'];

                    if ($totalView >= $cdkey_limit) {
                        $permissionView = FALSE;
                    } else {
                        $newTotalView = $totalView + 1;
                        $update_sql_data_array = array('cdkey_total_view' => $newTotalView, 'cdkey_updated_datetime' => $currentDate);
                        tep_db_perform(TABLE_CDKEY_VIEW, $update_sql_data_array, 'update', "cdkey_user_id = '".$userID."'");
                    }
                } else {
                    $permissionView = FALSE;
                }

            } else if ($_SESSION['login_email_address'] != $cdkey_image_row['code_uploaded_by'] AND ($view_cdkey_images_permission AND !isset($ajax_remark))) {

                $getCurrentTotalSql = "SELECT cdkey_total_view, cdkey_limit FROM " . TABLE_CDKEY_VIEW . " WHERE cdkey_user_id = '" . $userID . "'";
                $getCurrentTotalView = tep_db_query($getCurrentTotalSql);
                if ($totalViewFetch = tep_db_fetch_array($getCurrentTotalView)) {
                    $totalView = $totalViewFetch['cdkey_total_view'];
                    $cdkey_limit = $totalViewFetch['cdkey_limit'];

                    if ($totalView >= $cdkey_limit) {
                        $permissionView = FALSE;
                    } else {
                        $permissionView = TRUE;
                    }
                } else {
                    $permissionView = FALSE;
                }
            }

            //checking for non owner permission
            if ( $_SESSION['login_email_address'] != $cdkey_image_row['code_uploaded_by'] AND
                    ($view_cdkey_images_permission AND tep_not_null($ajax_remark) AND $permissionView === TRUE) ) {

                if (tep_not_null($ajax_remark)) {
                    tep_add_Remarks($cdk_id, $ajax_remark);
                }
                
                if (strtolower($cdkey_image_row['file_type']) == "soft") {
                    $theData = $cpc_obj->getCode($cdk_id, $cdkey_image_row['to_s3'], $cdkey_image_row['products_id'], $cdkey_image_row['code_date_added']);
                    
                    if ($theData !== FALSE) {
                        $ajax_result = 1;
                        $ajax_data = tep_decrypt_data($theData);
                    }
                } else {
                    $ajax_result = 1;
                    $ajax_data = '<img src="?action=cdk_image&cdk_id='.$cdk_id.'">';
                }
                
            //checking for owner
            } else if ($_SESSION['login_email_address'] == $cdkey_image_row['code_uploaded_by']) {

                $cdkey_image_select_sql = " SELECT file_type, code_uploaded_by, products_id, to_s3, code_date_added
                                            FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                            WHERE custom_products_code_id = '".$cdk_id."'";
                $cdkey_image_result_sql = tep_db_query($cdkey_image_select_sql);
                $cdkey_image_row = tep_db_fetch_array($cdkey_image_result_sql);

                if (tep_not_null($ajax_remark))     tep_add_Remarks($cdk_id, $ajax_remark);
                
                if (strtolower($cdkey_image_row['file_type']) == "soft") {
                    $theData = $cpc_obj->getCode($cdk_id, $cdkey_image_row['to_s3'], $cdkey_image_row['products_id'], $cdkey_image_row['code_date_added']);
                    
                    if ($theData !== FALSE) {
                        $ajax_result = 1;
                        $ajax_data = tep_decrypt_data($theData);
                    }
                } else {
                    $ajax_result = 1;
                    $ajax_data = '<img src="?action=cdk_image&cdk_id='.$cdk_id.'">';
                }
            } else {
                if ($permissionView === FALSE) {
                    $ajax_result = 3;
                    $ajax_data = 'CDKey View limit has been reached. Please come back tomorrow.';
                } else {
                    if ($view_cdkey_images_permission) {
                        $ajax_result = 2;
                        $ajax_data = 'Request for remark';
                    } else {
                        $ajax_data = ERROR_NO_PERMISSION;
                    }   
                }
            }

            echo json_encode(array('result' => $ajax_result, 'value' => $ajax_data));
            
            exit;
            break;
        case 'cdk_image':
            $cdk_id = tep_db_prepare_input($_REQUEST['cdk_id']);
            $userID = $_SESSION["login_id"];
            
            $cdkey_image_select_sql = " SELECT code_uploaded_by, products_id, to_s3, code_date_added
                                        FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                                        WHERE custom_products_code_id = '".$cdk_id."'";
            $cdkey_image_result_sql = tep_db_query($cdkey_image_select_sql);
            $cdkey_image_row = tep_db_fetch_array($cdkey_image_result_sql);
            
            if ($cdkey_image_row['code_uploaded_by'] != $_SESSION['login_email_address'] AND $view_cdkey_images_permission) {
                
                $getCurrentTotalSql = "SELECT cdkey_total_view, cdkey_limit FROM " . TABLE_CDKEY_VIEW . " WHERE cdkey_user_id = '".$userID."'";
                $getCurrentTotalView = tep_db_query($getCurrentTotalSql);
                if ($totalViewFetch = tep_db_fetch_array($getCurrentTotalView)) {
                    $totalView = $totalViewFetch['cdkey_total_view'];
                    $cdkey_limit = $totalViewFetch['cdkey_limit'];
                    
                    if ($totalView >= $cdkey_limit) {
                        echo 'CDKey View limit has been reached. Please come back tomorrow.';
                    } else {
                        $theData = $cpc_obj->getCode($cdk_id, $cdkey_image_row['to_s3'], $cdkey_image_row['products_id'], $cdkey_image_row['code_date_added']);
                
                        if ($theData !== FALSE) {
                            $theData = tep_decrypt_data($theData);
                            
                            header('Content-type: image/jpg');
                            header("Expires: Mon, 02 May 2001 23:00:00 GMT");
                            header("Cache-Control: no-store, no-cache, must-revalidate");
                            header("Cache-Control: post-check=0, pre-check=0", false);
                            header("Pragma: no-cache");

                            echo $theData;
                        }
                    }
                } else {
                    echo 'CDKey View limit has been reached. Please come back tomorrow.';
                }
                
            } else if ($cdkey_image_row['code_uploaded_by'] == $_SESSION['login_email_address']) {
                
                $theData = $cpc_obj->getCode($cdk_id, $cdkey_image_row['to_s3'], $cdkey_image_row['products_id'], $cdkey_image_row['code_date_added']);
                
                if ($theData !== FALSE) {
                    $theData = tep_decrypt_data($theData);
                            
                    header('Content-type: image/jpg');
                    header("Expires: Mon, 02 May 2001 23:00:00 GMT");
                    header("Cache-Control: no-store, no-cache, must-revalidate");
                    header("Cache-Control: post-check=0, pre-check=0", false);
                    header("Pragma: no-cache");

                    echo $theData;
                }
            }

            exit;
            break;
    }
}

if (isset($_REQUEST['poid']) && !empty($_REQUEST['poid'])) {
    $po_id = $_REQUEST['poid'];
} else {
    $action = '';
}

// Check Supplier locked?
$po_locked = $po_suppliers->get_po_supplier_lock_validation($supplier_id, $_SESSION['login_id']);
if (empty($po_locked)) {
    $error = true;
    $messageStack->add(ERROR_CDK_FORM_NOT_LOCKED_SUPPLIER, 'Error');
    $action = 'supplier_unlocked';
} else if ($po_locked != $_SESSION['login_id']) {
    $error = true;
    $messageStack->add(ERROR_CDK_FORM_LOCKED_SUPPLIER, 'Error');
    $action = 'supplier_unlocked';
}

$check_all_box = false;
$error = false;

if (tep_not_null($subaction)) {
    switch ($subaction) {
        case "process_cdk":
            if ($allow_create_cdk_permission) {
                if (!isset($_REQUEST['cdkey_item']) || (isset($_REQUEST['cdkey_item']) && count($_REQUEST['cdkey_item']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_CDKEY_MISSING, 'Error');
                }
                
                if (!$error) {
                    foreach ($_REQUEST['cdkey_item'] as $cdkey_id) {
                        $cdk_obj->set_cdk_temp_status($cdkey_id, '', true);
                    }
                    
                    $messageStack->add(sprintf(SUCCESS_CDK_FORM_ADDED, $cdkey_count), 'success');
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_CDK_FORM_CREATE_PERMISSION, 'Error');
            }
            break;
        
        case "removed_cdk":
            if ($allow_create_cdk_permission) {
                if (!isset($_REQUEST['cdkey_item']) || (isset($_REQUEST['cdkey_item']) && count($_REQUEST['cdkey_item']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_CDKEY_MISSING, 'Error');
                }
                
                if (!$error) {
                    foreach ($_REQUEST['cdkey_item'] as $cdkey_id) {
                        $cdk_obj->set_cdk_temp_status($cdkey_id, '', false);
                    }
                    
                    $messageStack->add(sprintf(SUCCESS_CDK_FORM_REMOVED, $cdkey_count), 'success');
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_CDK_FORM_CREATE_PERMISSION, 'Error');
            }
            break;
        
        case "process_status_cdk":
            if ($allow_create_cdk_permission) {
                if (!isset($_REQUEST['cdkey_item']) || (isset($_REQUEST['cdkey_item']) && count($_REQUEST['cdkey_item']) == 0)) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_CDKEY_MISSING, 'Error');
                }
                
                if (!isset($_REQUEST['cb_status']) || (isset($_REQUEST['cb_status']) && count($_REQUEST['cb_status']) == 0) || empty($_REQUEST['cb_status'])) {
                    $error = true;
                    $messageStack->add(ERROR_CDK_FORM_CDKEY_STATUS_MISSING, 'Error');
                }
                
                if (!$error) {
                    foreach ($_REQUEST['cdkey_item'] as $cdkey_id) {
                        $cdkey_allow_status_change = true;
                        if ($cdkey_allow_status_change) {
                            $cdk_obj->set_cdk_status($cdkey_id, $filterby_s, $_REQUEST['cb_status']);
                        }
                    }
                    $messageStack->add(sprintf(SUCCESS_CDK_FORM_ADDED, $cdkey_count), 'success');
                }
            } else {
                $error = true;
                $messageStack->add(ERROR_CDK_FORM_CREATE_PERMISSION, 'Error');
            }
            
            break;
            
        default:
            break;
    }
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
    <title><?=TITLE?></title>
    <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
    <script language="javascript" src="includes/general.js"></script>
    <script language="javascript" src="includes/javascript/xmlhttp.js"></script>
    <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.js"></script>
    <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.selectboxes.js"></script>
    <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.tabs.js"></script>
    <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/ogm_jquery.js"></script>
    <script language="javascript" src="includes/javascript/cdkey_listing.js"></script>
    <script language="JavaScript"><!--
        var Nav4 = ((navigator.appName == "Netscape") && (parseInt(navigator.appVersion) >= 4))

        function centerWin() {
            var NS = false;
            if (document.all) {
               /* the following is only available after onLoad */
               w = document.body.clientWidth;
               h = document.body.clientHeight;
               NS = true;
            } else if (document.layers) {
                ;
            }

            if (!NS) {
                self.moveTo((self.screen.width - self.outerWidth) / 2, (self.screen.height - self.outerHeight) / 2);
            } else {
                self.moveTo((self.screen.width-document.body.clientWidth) / 2, (self.screen.height-document.body.clientHeight) / 2);
            }
        }

        // Close the dialog
        function closeme() {
            window.close()
        }

        // Handle click of OK button
        function handleOK() {
            if (opener && !opener.closed) {
                opener.dialogWin.returnFunc();
            } else {
                alert("You have closed the main window.\n\nNo action will be taken on the choices in this dialog box.")
            }
            closeme();
            return false;
        }
        //-->
    </script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" onLoad="centerWin(); if (opener) opener.blockEvents()" onUnload="if (opener) opener.unblockEvents()">
<?
if ($messageStack->size > 0) {
    echo $messageStack->output();
}

if ($action == "cdk_list") {
    
    // Start Tab header
    $gridHTML = '';
    
    // Filter by tab
    if ($filterby_s == "Queue") {
        $queue_class = "ui-tabs-selected";
        $custom_code_qry = "";
        $withdrawal_qry = "and cpw.cdk_withdrawal_temp_status = '1'";
        $subaction = "removed_cdk";
        $batch_action_arr['1'] = BUTTON_STATUS_REMOVED_CDK;
    } else if ($filterby_s == "Actual") {
        $actual_class = "ui-tabs-selected";
        $custom_code_qry = "and cpc.orders_products_id <= 0 and cpc.status_id = 1";
        $withdrawal_qry = "and cpw.cdk_withdrawal_temp_status = '0' and cpw.cdk_withdrawal_status in ('0','3') and cpw.cdk_cb_status = '0'";
        $subaction = "process_cdk";
        $batch_action_arr['1'] = BUTTON_STATUS_CDK;
    } else if ($filterby_s == "On Hold") {
        $onhold_class = "ui-tabs-selected";
        $custom_code_qry = "and cpc.status_id = -2";
        $withdrawal_qry = "and cpw.cdk_withdrawal_temp_status = '0' and cpw.cdk_withdrawal_status in ('0','3') and cpw.cdk_cb_status = '0'";
        $subaction = "process_cdk";
        $batch_action_arr['1'] = BUTTON_STATUS_CDK;
    } else if ($filterby_s == "Sold") {
        $sold_class = "ui-tabs-selected";
        $custom_code_qry = "and cpc.orders_products_id > 0 and cpc.status_id = 0";
        $withdrawal_qry = "and cpw.cdk_withdrawal_temp_status = '0' and cpw.cdk_withdrawal_status in ('0','3') and cpw.cdk_cb_status = '0'";
        $subaction = "process_cdk";
        $batch_action_arr['1'] = BUTTON_STATUS_CDK;
    } else if ($filterby_s == "Disabled") {
        $disabled_class = "ui-tabs-selected";
        $custom_code_qry = "and cpc.status_id = -1";
        $withdrawal_qry = "and cpw.cdk_withdrawal_temp_status = '0' and cpw.cdk_withdrawal_status in ('0','3') and cpw.cdk_cb_status = '0'";
        $subaction = "process_cdk";
        $batch_action_arr['1'] = BUTTON_STATUS_CDK;
    } else if ($filterby_s == "Charge Back") {
        $charge_back_class = "ui-tabs-selected";
        $custom_code_qry = "";
        $withdrawal_qry = "and cpw.cdk_withdrawal_temp_status = '0' and cpw.cdk_cb_status = '1' and cpw.cdk_cb_deduction_status = '0'";
        $subaction = "process_cdk";
        $batch_action_arr['1'] = BUTTON_STATUS_CDK;
    } else if ($filterby_s == "Debit Note") {
        $debit_note_class = "ui-tabs-selected";
        $custom_code_qry = "";
        $withdrawal_qry = "and cpw.cdk_withdrawal_temp_status = '0' and cpw.cdk_cb_status = '2' and cpw.cdk_cb_deduction_status = '0'";
        $subaction = "process_cdk";
        $batch_action_arr['1'] = BUTTON_STATUS_CDK;
    }

    $batch_opt_arr[] = array('id'=>'','text'=>LABEL_BATCH_ACTION);

    if ($batch_action_arr) {
        foreach ($batch_action_arr as $key => $val) {
            $batch_opt_arr[] = array('id'=>$key,'text'=>$val); 
        }
    }
    
    // Get cdkey list
    $warning_query_raw = $cdk_obj->cdk_list_query($po_id, $withdrawal_qry, $custom_code_qry);
    switch ($sortby) {
        case 'sort_by_date'         : $warning_query_raw .= ' order by o.date_purchased'             ; break;
        case 'sort_by_date_modify'  : $warning_query_raw .= ' order by cpc.code_date_modified'       ; break;
        case 'sort_by_prod'         : $warning_query_raw .= ' order by pd.products_id'               ; break;
        case 'sort_by_name'         : $warning_query_raw .= ' order by pd.products_name'             ; break;
        case 'sort_by_order'        : $warning_query_raw .= ' order by op.orders_id'                 ; break;
        case 'sort_by_po'           : $warning_query_raw .= ' order by cpw.cdk_withdrawal_ref_id'    ; break;
        case 'sort_by_cdkey'        : $warning_query_raw .= ' order by cpc.custom_products_code_id'  ; break;
        default                     : $warning_query_raw .= ' order by o.date_purchased asc'         ; 
    }

    $warning_split = new splitPageResults($page, MAX_DISPLAY_CDKEY_RESULTS, $warning_query_raw, $warning_query_numrows);
    $cdkey_result = tep_db_query($warning_query_raw);
    while ($cdkey_row = tep_db_fetch_array($cdkey_result)) {
        $cdkey_array[$cdkey_row['custom_products_code_id']] = $cdkey_row;
    }
    
    // Set url base
    $product_status_url = "&action=cdk_list&po_refid=".urlencode($po_refid)."&curr=".urlencode($po_currency)."&poid=".urlencode($po_id)."&s_id=".urlencode($supplier_id);
    
    // Sold & Queue Tabs
    $gridHTML .= tep_draw_separator('pixel_trans.gif', '1', '15').'
        <div id="ListingTab">
            <ul class="ui-tabs-nav">
                <li id="tab-Actual" class="'.$actual_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ACTUAL)).'"><span><font color="green">'.LINK_FILTER_BY_ACTUAL.' ('.$cdk_obj->get_status_qty($po_id, 'actual').')</font></span></a></li>
                <li id="tab-Sold" class="'.$sold_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_SOLD)).'"><span><font color="blue">'.LINK_FILTER_BY_SOLD.' ('.$cdk_obj->get_status_qty($po_id, 'sold').')</font></span></a></li>
                <li id="tab-Disabled" class="'.$disabled_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_DISABLED)).'"><span><font color="black">'.LINK_FILTER_BY_DISABLED.' ('.$cdk_obj->get_status_qty($po_id, 'disabled').')</font></span></a></li>
                <li id="tab-Charge-Back" class="'.$charge_back_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_CB)).'"><span><font color="magenta">'.LINK_FILTER_BY_CB.' ('.$cdk_obj->get_status_qty($po_id, 'cb').')</font></span></a></li>
                <li id="tab-Debit-Note" class="'.$debit_note_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_DN)).'"><span><font color="brown">'.LINK_FILTER_BY_DN.' ('.$cdk_obj->get_status_qty($po_id, 'dn').')</font></span></a></li>
                <li id="tab-Queue" class="'.$queue_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_QUEUE)).'"><span><font color="orange">'.LINK_FILTER_BY_QUEUE.' ('.$cdk_obj->get_status_qty($po_id, 'queue').')</font></span></a></li>
            </ul>
        </div>
        <div class="ui-tabs-panel" style="display: block;">';
    
    // Start Table
    $html_res = '';
    
    if ($cdkey_array) {
        $row_count = 0;
        foreach ($cdkey_array as $cdkey) {
            $selling_price = $cdk_obj->getProductSellPrice($cdkey['products_id'], $currencies, $po_currency);
            
            $checked_status = false;
            
            $row_style = ($row_count%2) ? 'reportListingOdd' : 'reportListingEven' ;
            $html_res .= '  <tr class="'.$row_style.'">';
            $html_res .= '      <td class="main" align="center">' . tep_draw_checkbox_field('cdkey_item[]', $cdkey['custom_products_code_id'], $checked_status, '', 'class="cdkey_item" id="cdkey_item_'.$cdkey['custom_products_code_id'].'" onClick="return calculateCdkeyTotal(this)" '.$disabled_checkbox) . '</td>';
            $html_res .= '      <td class="main" align="center">' . ((empty($cdkey['date_purchased'])) ? 'NA' : $cdkey['date_purchased']) . '</td>';
            $html_res .= '      <td class="main" align="center">' . ((empty($cdkey['code_date_modified'])) ? 'NA' : $cdkey['code_date_modified']) . '</td>';
            $html_res .= '      <td class="main" align="center">' . $cdkey['products_id'] . '</td>';
            $html_res .= '      <td class="main">' . $cdkey['products_name'] . '</td>';
            $html_res .= '      <td class="main" align="center">' . ((empty($cdkey['orders_id'])) ? 'NA' : $cdkey['orders_id']) . '</td>';
            $html_res .= '      <td class="main" align="center">' . ((empty($cdkey['po_payment_refid'])) ? 'NA' : $cdkey['po_payment_refid']) . '</td>';
            $html_res .= '      <td class="main" align="center"><div id="Show_CDK_image_'.$cdkey['custom_products_code_id'].'"><a href="#" onClick="Show_CDK_Image(\''.$cdkey['custom_products_code_id'].'\')">' . $cdkey['custom_products_code_id'] . '</a></div><div id="CDK_image_'.$cdkey['custom_products_code_id'].'" style="display: none"></div></td>';
            $html_res .= '      <td class="main" align="center"><div id="selling_price_'.$cdkey['custom_products_code_id'].'_div">' . number_format($selling_price, 4, $currencies->currencies[$po_currency]['decimal_point'], $currencies->currencies[$po_currency]['thousands_point']) . '</div></td>';
            $html_res .= '  </tr>';
            $row_count++;
        }
    }
    
    ?>
    <?= tep_draw_form('cdk_item_form', FILENAME_POPUP_CDKEY_LIST, tep_get_all_get_params(array('subaction')) . 'subaction=' . $subaction, 'post', ''); ?>
    <div style="margin-left: 10px; margin-right: 10px;">
    
        <table border="0" width="100%" cellspacing="1" cellpadding="4" bgcolor="#ffffff">
            <tbody>
            <tr>
                <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
            </tr>    
            <tr>
                <td valign="top" class="pageHeading" colspan="5"><b><?= TABLE_HEADING_CDK_DETAILS . $po_refid ?></b></td>
            </tr>
            <tr><td colspan="8"><?= $gridHTML ?></td></tr>
            <tr>
                <td class="invoiceBoxHeading" align="center" width="5%"><?= tep_draw_checkbox_field('cdkey_selectall', '', $check_all_box,''," onClick=\"toggleCheckBox(this)\" ".$disabled_checkbox) ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_date') ? TABLE_HEADING_CDK_DATE : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_date&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_DATE.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_date_modify') ? TABLE_HEADING_CDK_DATE_MODIFY : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_date_modify&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_DATE_MODIFY.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_prod') ? TABLE_HEADING_CDK_PRODUCT_ID : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_prod&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_PRODUCT_ID.'</a>') ?></td>
                <td class="invoiceBoxHeading"><?= (($sortby=='sort_by_name') ? TABLE_HEADING_CDK_PRODUCT_NAME : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_name&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_PRODUCT_NAME.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_order') ? TABLE_HEADING_CDK_ORDER_ID : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_order&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_ORDER_ID.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_po') ? TABLE_HEADING_CDK_PO_ID : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_po&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_PO_ID.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_cdkey') ? TABLE_HEADING_CDK_NUMBER : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_cdkey&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_NUMBER.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><div id="selling_price"><?=sprintf(TABLE_HEADING_CDK_SELLING_PRICE,(isset($po_currency)? $po_currency : DEFAULT_CURRENCY))?></div></td>
            </tr>
            <? 
            echo $html_res;
            echo '<tr>';
            echo '  <td align="right" colspan="9">';
            echo '      <table width="100%" cellspacing="0" cellpadding="2" border="0">';
            echo '          <tbody id="total_row" class="show">';
            echo '              <tr class="invoiceListingOdd">';
            echo '                  <td width="90%" align="right" class="boldText" colspan="3">'. TABLE_HEADING_BOTTOM_TOTAL_CDK_SELECT .':</td>';
            echo '                  <td width="1%" align="center" class="boldText">&nbsp;</td>';
            echo '                  <td width="2%" align="right" class="boldText"><div name="cdk_total_currency" id="cdk_total_currency">'. (isset($po_currency) ? $po_currency : DEFAULT_CURRENCY) .'</div></td>';
            echo '                  <td width="6%" align="right" class="boldText">'. tep_draw_hidden_field('cdk_select_total', (isset($total_select_prices) ? $total_select_prices : '0.0000'), 'id="cdk_select_total"') .'<div name="cdk_select_total_div" id="cdk_select_total_div">'. number_format($total_select_prices, 4, $currencies->currencies[$po_currency]['decimal_point'], $currencies->currencies[$po_currency]['thousands_point']) .'</div></td>';
            echo '                  <td width="1%" align="center" class="boldText">&nbsp;</td>';
            echo '              </tr>';
            echo '          </tbody>';
            echo '      </table>';
            echo '  </td>';
            echo '</tr>';
            
            ?>          
            <tr>
                <td align="left" colspan="9">
                    <?= 
                        tep_draw_pull_down_menu('cdk_item_form', $batch_opt_arr, '', "id='cb_status' onChange=\"this.form.submit()\" ");
                    ?>
                </td>
            </tr>
            </tbody>
        </table>
    </form>
        <table border="0" width="100%" cellspacing="1" cellpadding="4" bgcolor="#ffffff">
            <tbody>
            <tr>
                <td colspan="4" class="smallText" align="left">(Selected : <span id="select_count" style="font-weight: bold"><?= tep_draw_hidden_field('select_count_cdkey', (isset($withdrawable_status) ? $withdrawable_status : '0'), 'id="select_count_cdkey"') ?>0 entry</span>)</td>
            </tr>
            <tr>
                <td colspan="7">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td class="smallText">&nbsp;<?php if (is_object($warning_split)) { echo $warning_split->display_count($warning_query_numrows, MAX_DISPLAY_CDKEY_RESULTS, $page, TEXT_DISPLAY_NUMBER_OF_CDKEY); } ?>&nbsp;</td>
                            <td align="right" class="smallText">&nbsp;<?php if (is_object($warning_split)) { echo $warning_split->display_links($warning_query_numrows, MAX_DISPLAY_CDKEY_RESULTS, MAX_DISPLAY_PAGE_LINKS, $page, tep_get_all_get_params(array('page', 'subaction')) . 'subaction=cdkey_pagination'); } ?>&nbsp;</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="8"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
            </tr>
            <tr>
                <td align="center" colspan="8">
                    <?= tep_button('Close', 'Close', 'javascript: call_back();', '', 'inputButton') ?>
                    <script>
                        function call_back() {
                            opener.updateTablePOList('<?=$po_id?>');
                            self.close();
                        }
                    </script>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <?
} else if ($action == "cdk_list_cb") {
    
    // Start Tab header
    $gridHTML = '';
    
    // Filter by tab
    $custom_code_qry = "";
    $withdrawal_qry = "";
    if ($filterby_s == "Actual") {
        $actual_class = "ui-tabs-selected";
        $custom_code_qry = "and cpc.orders_products_id <= 0 and cpc.status_id = 1";
        $withdrawal_qry = "and cpw.cdk_withdrawal_status in ('0','1','2','3') and cpw.cdk_cb_status = '0' and cpw.cdk_cb_temp_status = '0'";
        if ($allow_set_cdk_status_permission) {
            $batch_action_arr['-2'] = LABEL_SET_PAID;
        }
        $batch_action_arr['1'] = LABEL_SET_CB;
    } else if ($filterby_s == "On Hold") {
        $onhold_class = "ui-tabs-selected";
        $custom_code_qry = "and cpc.status_id = -2";
    } else if ($filterby_s == "Sold") {
        $sold_class = "ui-tabs-selected";
        $custom_code_qry = "and cpc.orders_products_id > 0 and cpc.status_id = 0";
        $withdrawal_qry = "and cpw.cdk_withdrawal_status in ('0','1','2','3') and cpw.cdk_cb_status = '0' and cpw.cdk_cb_temp_status = '0'";
        if ($allow_set_cdk_status_permission) {
            $batch_action_arr['-2'] = LABEL_SET_PAID;
        }
        $batch_action_arr['1'] = LABEL_SET_CB;
    } else if ($filterby_s == "Paid") {
        $paid_class = "ui-tabs-selected";
        $withdrawal_qry = "and cpw.cdk_withdrawal_status = '4' and cpw.cdk_cb_status = '0' and cpw.cdk_cb_temp_status = '0'";
        if ($allow_set_cdk_status_permission) {
            $batch_action_arr['-1'] = LABEL_SET_SOLD;
        }
        $batch_action_arr['2'] = LABEL_SET_DN;
    } else if ($filterby_s == "Disabled") {
        $disabled_class = "ui-tabs-selected";
        $custom_code_qry = "and cpc.status_id = -1";
        $withdrawal_qry = "and cpw.cdk_cb_status = '0' and cpw.cdk_cb_temp_status = '0'";
        if ($allow_set_cdk_status_permission) {
            $batch_action_arr['-2'] = LABEL_SET_PAID;
        }
        $batch_action_arr['1'] = LABEL_SET_CB;
    } else if ($filterby_s == "Charge Back") {
        $charge_back_class = "ui-tabs-selected";
        $custom_code_qry = "";
        $withdrawal_qry = "and cpw.cdk_cb_status = '1' and cpw.cdk_cb_temp_status = '0'";
        $batch_action_arr['-3'] = LABEL_SET_REMOVE;
    } else if ($filterby_s == "Debit Note") {
        $debit_note_class = "ui-tabs-selected";
        $withdrawal_qry = "and cpw.cdk_cb_status = '2' and cpw.cdk_cb_temp_status = '0'";
        $batch_action_arr['-3'] = LABEL_SET_REMOVE;
    }
    
    $batch_opt_arr[] = array('id'=>'','text'=>LABEL_BATCH_ACTION);

    if ($batch_action_arr) {
        foreach ($batch_action_arr as $key => $val) {
            $batch_opt_arr[] = array('id'=>$key,'text'=>$val); 
        }
    }
    
    // Get cdkey list
    $warning_query_raw = $cdk_obj->cdk_list_query($po_id, $withdrawal_qry, $custom_code_qry);
    switch ($sortby) {
        case 'sort_by_date'         : $warning_query_raw .= ' order by o.date_purchased'             ; break;
        case 'sort_by_date_modify'  : $warning_query_raw .= ' order by cpc.code_date_modified'       ; break;
        case 'sort_by_prod'         : $warning_query_raw .= ' order by pd.products_id'               ; break;
        case 'sort_by_name'         : $warning_query_raw .= ' order by pd.products_name'             ; break;
        case 'sort_by_order'        : $warning_query_raw .= ' order by op.orders_id'                 ; break;
        case 'sort_by_po'           : $warning_query_raw .= ' order by cpw.cdk_withdrawal_ref_id'    ; break;
        case 'sort_by_cdkey'        : $warning_query_raw .= ' order by cpc.custom_products_code_id'  ; break;
        default                     : $warning_query_raw .= ' order by o.date_purchased asc'         ; 
    }

    $warning_split = new splitPageResults($page, MAX_DISPLAY_CDKEY_RESULTS, $warning_query_raw, $warning_query_numrows);
    $cdkey_result = tep_db_query($warning_query_raw);
    while ($cdkey_row = tep_db_fetch_array($cdkey_result)) {
        $cdkey_array[$cdkey_row['custom_products_code_id']] = $cdkey_row;
    }
    
    // Set url base
    $product_status_url = "&action=cdk_list_cb&po_refid=".urlencode($po_refid)."&curr=".urlencode($po_currency)."&poid=".urlencode($po_id)."&s_id=".urlencode($supplier_id);
    
    // Sold & Queue Tabs
    $gridHTML .= tep_draw_separator('pixel_trans.gif', '1', '15').'
        <div id="ListingTab">
            <ul class="ui-tabs-nav">
                <li id="tab-Actual" class="'.$actual_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ACTUAL)).'"><span><font color="green">'.LINK_FILTER_BY_ACTUAL.' ('.$cdk_obj->get_cb_status_qty($po_id, 'actual').')</font></span></a></li>
                <li id="tab-On-Hold" class="'.$onhold_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_ON_HOLD)).'"><span><font color="red">'.LINK_FILTER_BY_ON_HOLD.' ('.$cdk_obj->get_cb_status_qty($po_id, 'onhold').')</font></span></a></li>
                <li id="tab-Sold" class="'.$sold_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_SOLD)).'"><span><font color="blue">'.LINK_FILTER_BY_SOLD.' ('.$cdk_obj->get_cb_status_qty($po_id, 'sold').')</font></span></a></li>
                <li id="tab-Paid" class="'.$paid_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_PAID)).'"><span><font color="grey">'.LINK_FILTER_BY_PAID.' ('.$cdk_obj->get_cb_status_qty($po_id, 'paid').')</font></span></a></li>
                <li id="tab-Disabled" class="'.$disabled_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_DISABLED)).'"><span><font color="black">'.LINK_FILTER_BY_DISABLED.' ('.$cdk_obj->get_cb_status_qty($po_id, 'disabled').')</font></span></a></li>
                <li id="tab-Charge-Back" class="'.$charge_back_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_CB)).'"><span><font color="magenta">'.LINK_FILTER_BY_CB.' ('.$cdk_obj->get_cb_status_qty($po_id, 'cb').')</font></span></a></li>
                <li id="tab-Debit-Note" class="'.$debit_note_class.'"><a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST,$product_status_url.'&filterby_s='.urlencode(LINK_FILTER_BY_DN)).'"><span><font color="brown">'.LINK_FILTER_BY_DN.' ('.$cdk_obj->get_cb_status_qty($po_id, 'dn').')</font></span></a></li>
            </ul>
        </div>
        <div class="ui-tabs-panel" style="display: block;">';
    
    // Start Table
    $html_res = '';
    
    if ($cdkey_array) {
        $row_count = 0;
        foreach ($cdkey_array as $cdkey) {
            $selling_price = $cdk_obj->getProductSellPrice($cdkey['products_id'], $currencies, $po_currency);
            
            $checked_status = false;
            $disabled_checkbox = '';
            $payment_status_text = 'OK';

            switch ($cdkey['cdk_withdrawal_status']) {
                case '0':
                    if ($cdkey['cdk_cb_deduction_status']=='1') {
                        $disabled_checkbox = 'disabled';
                        $deduction_po_refid = $cdk_obj->get_po_ref_num_by_id($cdkey['cdk_cb_deduction_po_id']);
                        $payment_status_text = 'Used:<br>' . $deduction_po_refid;
                    }
                    break;
                case '1':
                    $disabled_checkbox = 'disabled';
                    $payment_status_text = 'Processing:<br>' . $cdkey['po_payment_refid'];
                    break;
                case '2':
                    $disabled_checkbox = 'disabled';
                    $payment_status_text = 'Processing:<br>' . $cdkey['po_payment_refid'];
                    break;
                case '3':
                    if ($cdkey['cdk_cb_deduction_status']=='1') {
                        $disabled_checkbox = 'disabled';
                        $deduction_po_refid = $cdk_obj->get_po_ref_num_by_id($cdkey['cdk_cb_deduction_po_id']);
                        $payment_status_text = 'Used:<br>' . $deduction_po_refid;
                    }
                    break;
                case '4':
                    if ($cdkey['cdk_cb_deduction_status']=='1') {
                        $disabled_checkbox = 'disabled';
                        $deduction_po_refid = $cdk_obj->get_po_ref_num_by_id($cdkey['cdk_cb_deduction_po_id']);
                        $payment_status_text = 'Used:<br>' . $deduction_po_refid;
                    }
                    break;
            }
            
            $row_style = ($row_count%2) ? 'reportListingOdd' : 'reportListingEven' ;
            $html_res .= '  <tr class="'.$row_style.'">';
            $html_res .= '      <td class="main" align="center">' . tep_draw_checkbox_field('cdkey_item[]', $cdkey['custom_products_code_id'], $checked_status, '', $disabled_checkbox . ' class="cdkey_item" id="cdkey_item_'.$cdkey['custom_products_code_id'].'" onClick="return calculateCdkeyTotal(this)"') . '</td>';
            $html_res .= '      <td class="main" align="center">' . ((empty($cdkey['date_purchased'])) ? 'NA' : $cdkey['date_purchased']) . '</td>';
            $html_res .= '      <td class="main" align="center">' . ((empty($cdkey['code_date_modified'])) ? 'NA' : $cdkey['code_date_modified']) . '</td>';
            $html_res .= '      <td class="main" align="center">' . $cdkey['products_id'] . '</td>';
            $html_res .= '      <td class="main">' . $cdkey['products_name'] . '</td>';
            $html_res .= '      <td class="main" align="center">' . ((empty($cdkey['orders_id'])) ? 'NA' : $cdkey['orders_id']) . '</td>';
            $html_res .= '      <td class="main" align="center">' . ((empty($cdkey['po_payment_refid'])) ? 'NA' : $cdkey['po_payment_refid']) . '</td>';
            $html_res .= '      <td class="main" align="center"><div id="Show_CDK_image_'.$cdkey['custom_products_code_id'].'"><a href="#" onClick="Show_CDK_Image(\''.$cdkey['custom_products_code_id'].'\')">' . $cdkey['custom_products_code_id'] . '</a></div><div id="CDK_image_'.$cdkey['custom_products_code_id'].'" style="display: none"></div></td>';
            $html_res .= '      <td class="main" align="center"><div id="selling_price_'.$cdkey['custom_products_code_id'].'_div">' . number_format($selling_price, 4, $currencies->currencies[$po_currency]['decimal_point'], $currencies->currencies[$po_currency]['thousands_point']) . '</div></td>';
            $html_res .= '      <td class="main" align="center">' . $payment_status_text . '</td>';
            $html_res .= '  </tr>';
            $row_count++;
        }
    }
    
    $subaction = 'process_status_cdk';
    
    ?>
    <?= tep_draw_form('cdk_item_form', FILENAME_POPUP_CDKEY_LIST, tep_get_all_get_params(array('subaction')) . 'subaction=' . $subaction, 'post', ''); ?>
    <div style="margin-left: 10px; margin-right: 10px;">
    
        <table border="0" width="100%" cellspacing="1" cellpadding="4" bgcolor="#ffffff">
            <tbody>
            <tr>
                <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
            </tr>    
            <tr>
                <td valign="top" class="pageHeading" colspan="5"><b><?= TABLE_HEADING_CDK_DETAILS . $po_refid ?></b></td>
            </tr>
            <tr><td colspan="9"><?= $gridHTML ?></td></tr>
            <tr>
                <td class="invoiceBoxHeading" align="center" width="5%"><?= tep_draw_checkbox_field('cdkey_selectall', '', $check_all_box,''," onClick=\"toggleCheckBox(this)\" ".$disabled_checkbox) ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_date') ? TABLE_HEADING_CDK_DATE : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_date&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_DATE.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_date_modify') ? TABLE_HEADING_CDK_DATE_MODIFY : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_date_modify&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_DATE_MODIFY.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_prod') ? TABLE_HEADING_CDK_PRODUCT_ID : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_prod&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_PRODUCT_ID.'</a>') ?></td>
                <td class="invoiceBoxHeading"><?= (($sortby=='sort_by_name') ? TABLE_HEADING_CDK_PRODUCT_NAME : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_name&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_PRODUCT_NAME.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_order') ? TABLE_HEADING_CDK_ORDER_ID : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_order&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_ORDER_ID.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_po') ? TABLE_HEADING_CDK_PO_ID : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_po&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_PO_ID.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= (($sortby=='sort_by_cdkey') ? TABLE_HEADING_CDK_NUMBER : '<a href="'.tep_href_link(FILENAME_POPUP_CDKEY_LIST, 'sortby=sort_by_cdkey&filterby_s=' . $filterby_s . $product_status_url).'">'.TABLE_HEADING_CDK_NUMBER.'</a>') ?></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><div id="selling_price"><?=sprintf(TABLE_HEADING_CDK_SELLING_PRICE,(isset($po_currency)? $po_currency : DEFAULT_CURRENCY))?></div></td>
                <td class="invoiceBoxHeading" align="center" width="10%"><?= TABLE_HEADING_CDK_BILLING_STATUS ?></td>
            </tr>
            <?= $html_res ?>
            <tr>
                <td align="right" colspan="10">
                    <table width="100%" cellspacing="0" cellpadding="2" border="0">
                        <tbody id="total_row" class="show">
                            <tr class="invoiceListingOdd">
                                <td width="90%" align="right" class="boldText" colspan="3"><?= TABLE_HEADING_BOTTOM_TOTAL_CDK_SELECT ?>:</td>
                                <td width="1%" align="center" class="boldText">&nbsp;</td>
                                <td width="2%" align="right" class="boldText"><div name="cdk_total_currency" id="cdk_total_currency"><?= (isset($po_currency) ? $po_currency : DEFAULT_CURRENCY) ?></div></td>
                                <td width="6%" align="right" class="boldText"><?= tep_draw_hidden_field('cdk_select_total', (isset($total_select_prices) ? $total_select_prices : '0.0000'), 'id="cdk_select_total"') ?><div name="cdk_select_total_div" id="cdk_select_total_div"><?= number_format($total_select_prices, 4, $currencies->currencies[$po_currency]['decimal_point'], $currencies->currencies[$po_currency]['thousands_point']) ?></div></td>
                                <td width="1%" align="center" class="boldText">&nbsp;</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr><tr>
                <td align="left" colspan="8">
                    <?
                        echo tep_draw_pull_down_menu('cb_status', $batch_opt_arr, '', "id='cb_status' onChange=\"this.form.submit()\" ");
                    ?>
                </td>
            </tr>
            </tbody>
        </table>
    </form>
        <table border="0" width="100%" cellspacing="1" cellpadding="4" bgcolor="#ffffff">
            <tbody>
            <tr>
                <td colspan="4" class="smallText" align="left">(Selected : <span id="select_count" style="font-weight: bold"><?= tep_draw_hidden_field('select_count_cdkey', (isset($withdrawable_status) ? $withdrawable_status : '0'), 'id="select_count_cdkey"') ?>0 entry</span>)</td>
            </tr>
            <tr>
                <td colspan="7">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td class="smallText">&nbsp;<?php if (is_object($warning_split)) { echo $warning_split->display_count($warning_query_numrows, MAX_DISPLAY_CDKEY_RESULTS, $page, TEXT_DISPLAY_NUMBER_OF_CDKEY); } ?>&nbsp;</td>
                            <td align="right" class="smallText">&nbsp;<?php if (is_object($warning_split)) { echo $warning_split->display_links($warning_query_numrows, MAX_DISPLAY_CDKEY_RESULTS, MAX_DISPLAY_PAGE_LINKS, $page, tep_get_all_get_params(array('page', 'subaction')) . 'subaction=cdkey_pagination'); } ?>&nbsp;</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="8"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
            </tr>
            <tr>
                <td align="center" colspan="8">
                    <?= tep_button('Close', 'Close', 'javascript: call_back();', '', 'inputButton') ?>
                    <script>
                        function call_back() {
                            self.close();
                        }
                    </script>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <?
} else if ($action == "supplier_unlocked") {
    ?>
    <table border="0" width="100%" cellspacing="1" cellpadding="4" bgcolor="#ffffff">
        <tbody>
            <tr><td align="center">Supplier is unlocked. Cannot proceed with this process.</td></tr>
            <tr>
                <td align="center">
                    <?= tep_button('Close', 'Close', 'javascript: call_back();', '', 'inputButton') ?>
                    <script>
                        function call_back() {
                            self.close();
                        }
                    </script>
                </td>
            </tr>
        </tbody>
    </table>
    <?
} else {
    ?>
    <table border="0" width="100%" cellspacing="1" cellpadding="4" bgcolor="#ffffff">
        <tbody>
            <tr><td align="center">No Data</td></tr>
            <tr>
                <td align="center">
                    <?= tep_button('Close', 'Close', 'javascript: call_back();', '', 'inputButton') ?>
                    <script>
                        function call_back() {
                            self.close();
                        }
                    </script>
                </td>
            </tr>
        </tbody>
    </table>
    <?
} 
?> 
<?php
if ($subaction == 'process_status_cdk') {
    echo '<script type="text/javascript">';
    echo '  jQuery().ready(function() {';
    echo "      jQuery('#cb_status').val('');";
    echo '  });';
    echo '</script>';
}
?>
</body>
</html>