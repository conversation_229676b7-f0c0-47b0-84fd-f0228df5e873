<?
/*
  	$Id: popup_products_list.php,v 1.20 2013/10/07 11:15:17 chingyen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

function filter_selection($var) {
	return ($var && $var >= 1);
}

$action = $_REQUEST["action"];
switch($action) {
	case "multiple_categories_submission":
		if ($_REQUEST["browser_id"] == "IE") {
			$selected_cat_array = $_REQUEST["SelectedItem"];
		} else {	// for Non-IE browser
			$selected_cat_array = array_keys(array_filter($_REQUEST["HiddenCat"], "filter_selection"));
		}
?>
		<script language="JavaScript"><!--
			opener.getReturnedValue('<?=count($selected_cat_array) ? implode(',', $selected_cat_array) : ''?>');
			self.close();
			opener.focus();
		//-->
		</script>
<?
		break;
	case "insert_price_set":
		$unique_id = (int)$HTTP_POST_VARS["unique_price_set_id"] > 0 ? (int)$HTTP_POST_VARS["unique_price_set_id"] : 0;
		$set_name = tep_db_prepare_input($HTTP_POST_VARS["price_set_name"]);
		$sort_order = (int)$HTTP_POST_VARS["price_set_order"] > 0 ? (int)$HTTP_POST_VARS["price_set_order"] : 50000;
		$price_set_array = $HTTP_POST_VARS["price_set"];
		
		if ($unique_id > 0) {
			$duplicate_price_set_select_sql = "SELECT batch_update_price_sets_id FROM " . TABLE_BATCH_UPDATE_PRICE_SETS . " WHERE batch_update_price_sets_id = '" . tep_db_input($unique_id) . "'";
			$duplicate_price_set_result_sql = tep_db_query($duplicate_price_set_select_sql);
			if (tep_db_num_rows($duplicate_price_set_result_sql) > 0) {
				$messageStack->add_session('Duplicate Unique ID', 'error');
			} else {
  				$sql_data_array = array('batch_update_price_sets_id' => $unique_id,
										'batch_update_price_sets_name' => $set_name,
										'batch_update_price_sets_sort_order' => $sort_order);
				tep_db_perform(TABLE_BATCH_UPDATE_PRICE_SETS, $sql_data_array);
		  		
		  		if (count($price_set_array)) {
		  			foreach ($price_set_array as $qty => $price) {
		  				$sql_data_array = array('batch_update_price_sets_id' => $unique_id,
												'batch_update_qty' => $qty,
												'batch_update_price' => (double)$price);
						tep_db_perform(TABLE_BATCH_UPDATE_PRICE_SETS_VALUES, $sql_data_array);
		  			}
		  		}
		  		$messageStack->add_session('New price set has been successfully added', 'success');
		  		
		  		tep_redirect(tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=show_success_page'));
		  		exit;
			}
		} else {
			$messageStack->add_session('Invalid Unique ID', 'error');
		}
		
		tep_redirect(tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=add_price_set&qty_str='.$_REQUEST["qty_str"]));
		
		break;
	case "update_price_set":
		//$update_price_error = false;
		if (isset($HTTP_POST_VARS["set_info"]) && count($HTTP_POST_VARS["set_info"])) {
			$row_count = 1;
			foreach ($HTTP_POST_VARS["set_info"] as $set_id => $set_res) {
				$this_round_error = false;
				
				$new_set_id = tep_db_prepare_input($set_res["set_id"]);
				
				if (!tep_not_null($new_set_id) || $new_set_id == 0 || !is_numeric($new_set_id)) {
					$messageStack->add_session('Invalid Unique ID for row: '.$row_count.'!', 'error');
					//$update_price_error = true;
					$this_round_error = true;
				}
				
				if (!tep_not_null($set_res["set_name"])) {
					$messageStack->add_session('Please enter Price Set Name for row: '.$row_count.'!', 'error');
					//$update_price_error = true;
					$this_round_error = true;
				}
				
				if ($new_set_id != $set_id) {	// modify the unique id
					$duplicate_price_set_select_sql = "SELECT batch_update_price_sets_id FROM " . TABLE_BATCH_UPDATE_PRICE_SETS . " WHERE batch_update_price_sets_id = '" . tep_db_input($new_set_id) . "'";
					$duplicate_price_set_result_sql = tep_db_query($duplicate_price_set_select_sql);
					if (tep_db_num_rows($duplicate_price_set_result_sql) > 0) {
						$messageStack->add_session('Duplicate Unique ID for row: '.$row_count.'!', 'error');
						//$update_price_error = true;
						$this_round_error = true;
					}
				}
				
				if (!$this_round_error) {
					$sql_data_array = array('batch_update_price_sets_id' => $new_set_id,
											'batch_update_price_sets_name' => tep_db_prepare_input($set_res["set_name"]),
											'batch_update_price_sets_sort_order' => tep_not_null($set_res["sort_order"]) ? (int)$set_res["sort_order"] : 50000
											);
					tep_db_perform(TABLE_BATCH_UPDATE_PRICE_SETS, $sql_data_array, 'update', "batch_update_price_sets_id = '" . tep_db_input($set_id) . "'");
					
					if (isset($HTTP_POST_VARS["price_set"][$set_id]) && count($HTTP_POST_VARS["price_set"][$set_id])) {
						tep_db_query("DELETE FROM " . TABLE_BATCH_UPDATE_PRICE_SETS_VALUES . " WHERE batch_update_price_sets_id = '" . tep_db_input($set_id) . "' AND batch_update_qty IN ('" . implode("', '", array_keys($HTTP_POST_VARS["price_set"][$set_id])) . "')");
						
						foreach ($HTTP_POST_VARS["price_set"][$set_id] as $qty => $price) {
							if (tep_not_null($price)) {
				  				$sql_data_array = array('batch_update_price_sets_id' => $new_set_id,
														'batch_update_qty' => $qty,
														'batch_update_price' => (double)$price);
								tep_db_perform(TABLE_BATCH_UPDATE_PRICE_SETS_VALUES, $sql_data_array);
							}
			  			}
					}
					
					$messageStack->add_session('Price sets has been successfully updated for row: '.$row_count, 'success');
				}
				
				$row_count++;
			}
		}
		
		tep_redirect(tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=edit_price_set'));
		
		break;
	case "delete_price_set":
		if (tep_not_null($_REQUEST["sID"])) {
			tep_db_query("DELETE FROM " . TABLE_CATEGORIES_TO_PRICE_SETS . " WHERE batch_update_price_sets_id = '" . (int)$_REQUEST["sID"] . "'");
			tep_db_query("DELETE FROM " . TABLE_BATCH_UPDATE_PRICE_SETS_VALUES . " WHERE batch_update_price_sets_id = '" . (int)$_REQUEST["sID"] . "'");
			tep_db_query("DELETE FROM " . TABLE_BATCH_UPDATE_PRICE_SETS . " WHERE batch_update_price_sets_id = '" . (int)$_REQUEST["sID"] . "'");
			
			$messageStack->add_session('Price set has been successfully deleted', 'success');
		} else {
			$messageStack->add_session('Nothing to delete', 'warning');
		}
		tep_redirect(tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=edit_price_set'));
		
		break;
	default:
		break;
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
	<script language="javascript" src="includes/javascript/product_listing.js"></script>
	<script language="JavaScript"><!--
		var Nav4 = ((navigator.appName == "Netscape") && (parseInt(navigator.appVersion) >= 4))
		
		function centerWin() {
			var NS = false;
			if (document.all) {
			   /* the following is only available after onLoad */
			   w = document.body.clientWidth;
			   h = document.body.clientHeight;
			   NS = true;
			} else if (document.layers) {
			  	;
			}
			
	     	if (!NS) {
	     		self.moveTo((self.screen.width - self.outerWidth) / 2, (self.screen.height - self.outerHeight) / 2);
	     	} else {
	     		self.moveTo((self.screen.width-document.body.clientWidth) / 2, (self.screen.height-document.body.clientHeight) / 2);
	    	}
	    }
	    
		// Close the dialog
		function closeme() {
			window.close()
		}
		
		// Handle click of OK button
		function handleOK() {
			if (opener && !opener.closed) {
				opener.dialogWin.returnFunc();
			} else {
				alert("You have closed the main window.\n\nNo action will be taken on the choices in this dialog box.")
			}
			closeme();
			return false;
		}
		//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" onLoad="centerWin(); if (opener) opener.blockEvents()" onUnload="if (opener) opener.unblockEvents()">
<?
if ($messageStack->size > 0) {
	echo $messageStack->output();
}
?>
	<table border="0" width="100%" cellspacing="2" cellpadding="0" bgcolor="#ffffff">
<?
if (!tep_not_null($action)) {
	$by_filename = tep_db_prepare_input($_REQUEST['fname']);
	$by_fieldname = (isset($_REQUEST['fieldname']) ? tep_db_prepare_input($_REQUEST['fieldname']) : '');
	$prod_filter = (isset($_REQUEST['prod_filter']) ? tep_db_prepare_input($_REQUEST['prod_filter']) : 'all');
	$direct_top_up = (isset($_REQUEST['direct_top_up']) ? tep_db_prepare_input($_REQUEST['direct_top_up']) : '');
	$categories_array = array();
	if (tep_not_null($by_filename)) {
		$categories_array = tep_get_eligible_category_tree($by_filename, 0, '___', '', $categories_array, false, 0, true);
	} else {
		$categories_array = tep_get_category_tree(0, '___', '', $categories_array, false, 0, true);
	}
?>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
		</tr>
		<tr>
			<td class="main" valign="top">Categories:</td>
			<td class="main" valign="top">
				<?=tep_draw_pull_down_menu('cat_sel', $categories_array, '','onChange="refreshDynamicSelectOptions(this, \'prod_sel\', \''.(int)$languages_id.'\', \''.$prod_filter.'\', false, \''.$by_filename.'\', \''.$login_id.'\', \''.$direct_top_up.'\');" id="cat_sel" ')?>
			</td>
		</tr>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
		<tr>
			<td class="main" valign="top">Products :</td>
			<td class="main" valign="top">
				<select name="prod_sel" id="prod_sel">
					<option value="">---Select Product---</option> 
				</select>
			</td>
		</tr>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
		</tr>
		<tr>
  			<td align="center" colspan="2">
				<a href="javascript: call_back();"><?=tep_image_button('button_select.gif', IMAGE_SELECT)?></a>
				<script>
					function call_back() {
						var tmpVal = document.getElementById('prod_sel').value;
						if (tmpVal != '') {
<?	if (tep_not_null($by_fieldname)) { ?>
							opener.getReturnedValue(tmpVal, '<?=$by_fieldname?>');
<?	} else { ?>
							opener.getReturnedValue(tmpVal);
<?	} ?>
							self.close();
							opener.focus();
						} else {
							alert('No product is selected!')
						}
					}
				</script>
			</td>
		</tr>
<?
} else if ($action == "multiple_categories_selection") {
	$by_filename = tep_db_prepare_input($_REQUEST['fname']);
	
	function tep_show_list_items($ListItems, $Level=0) {
		global $languages_id, $HiddenItems, $g2c_array, $by_filename;
		$SubTotal=0;
		
		foreach ($ListItems as $ListItem) {
			$NewListItems = array() ;
			$parent_to_child = array();
			
			$p_rank = tep_check_cat_tree_permissions($by_filename, $ListItem["categories_id"]);
			
			if ($p_rank > 0) {
				$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
									FROM " . TABLE_CATEGORIES . " AS c 
									INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id = cd.categories_id 
									WHERE c.parent_id='" . $ListItem["categories_id"] . "' 
										AND cd.language_id='" . (int)$languages_id ."' 
									ORDER BY sort_order, cd.categories_name " ;
				$cat_result_sql = tep_db_query($cat_select_sql);
				while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
					$NewListItems[] = $cat_row ;
					$parent_to_child[] = $cat_row["categories_id"];
				}
				
				$SubTotal += 1 ;
				$DisplayName = strip_tags($ListItem["categories_name"]);
				if (!$DisplayName) $DisplayName = "" ;
			    
			    if (count($NewListItems)) {
			    	$DisplayName .= '&nbsp;&nbsp;[<a href="javascript:;" onClick="expandTree(aux'.$ListItem["categories_id"].'); setTreeCheckbox(\'multiple_categories_sel\', \'MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"].'\', true, \''.implode(",", $parent_to_child).'\');">Check All</a>]&nbsp;&nbsp;[<a href="javascript:;" onClick="expandTree(aux'.$ListItem["categories_id"].'); setTreeCheckbox(\'multiple_categories_sel\', \'MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"].'\', false, \''.implode(",", $parent_to_child).'\');">Uncheck All</a>]';
			    }
				if ($p_rank == 1) {
					$checked_value = (in_array($ListItem["categories_id"], $g2c_array) ? " checked " : "");
					$DisplayName = '<input type="checkbox" name=SelectedItem[] value="'.$ListItem["categories_id"].'" onClick="assign_selection(this); if (this.checked) { expandTree(aux'.$ListItem["categories_id"].');}" id=MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"]. $checked_value . '>' . $DisplayName;
					$HiddenItems[] = array('id' => $ListItem["categories_id"], 'value' => (tep_not_null($checked_value) ? 1 : 0));
				}
				?>
				
				aux<?=$ListItem["categories_id"]?> = insFld(<?=($Level==1?"foldersTree":"aux".$ListItem["parent_id"])?>, gFld("<?
				// Text
				echo addslashes($DisplayName);?>",<?
				// Link
				if ($url) {?>
					"javascript:MyNewWindow(\"<?=$url?>\",\"Open\",<?=$this->PopupWinWidth?>,<?=$this->PopupWinHeight?>,\"yes\")"))
				<?} else {?>
					"javascript:undefined"))
				<?}?>
				aux<?=$ListItem["categories_id"]?>._readonly = 0;
				<?
				
				$SubTotal += tep_show_list_items($NewListItems, $Level+1) ;
			}
		}
		return $SubTotal ;
	}
	
	$ListItems = array();
	$HiddenItems = array();
	$parent_to_child = array();
	$g2c_array = tep_not_null($_REQUEST["default_tree_selection"]) ? explode(',', $_REQUEST["default_tree_selection"]) : array();
	$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
						FROM " . TABLE_CATEGORIES . " AS c 
						INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
							ON c.categories_id = cd.categories_id 
						WHERE c.parent_id=0 
							AND cd.language_id='" . (int)$languages_id ."' 
						ORDER BY sort_order, cd.categories_name " ;
	$cat_result_sql = tep_db_query($cat_select_sql);
	while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
		$ListItems[] = $cat_row ;
		$parent_to_child[] = $cat_row["categories_id"];
	}
	
	echo tep_draw_form('multiple_categories_sel', FILENAME_POPUP_PRODUCTS_LIST, tep_get_all_get_params(array('action')) . 'action=multiple_categories_submission');
	echo tep_draw_hidden_field("browser_id", '', ' id="browser_id" ');
?>
		<tr>
			<td>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
        				<td width="100%">
    	   				<script language="javascript">
						<!--
						var browserName = navigator.appName; 
						if (browserName == 'Microsoft Internet Explorer')
							document.getElementById('browser_id').value = "IE";
						else
							document.getElementById('browser_id').value = "Non-IE";
						//-->
						</script>
							<script language="javascript" src="includes/javascript/Treeview/ua.js"></script>
							<script language="javascript" src="includes/javascript/Treeview/ftiens4.js"></script>
							<script>
								function checkAll(folderObj) {
									var childObj;
								    var i;
								
								    // Open folder
								    if (!folderObj.isOpen) {
								      	clickOnNodeObj(folderObj)
								    }
								}
								
								function expandTree(folderObj)
								{
								    var childObj;
								    var i;
								
								    // Open folder
								    if (!folderObj.isOpen)
								      	clickOnNodeObj(folderObj)
									
								    // Call this function for all folder children
								    for (i=0 ; i < folderObj.nChildren; i++)  {
								      childObj = folderObj.children[i]
								      if (typeof childObj.setState != "undefined" && !childObj._readonly) {//is folder
								        expandTree(childObj)
								      }
								    }
								}
										
								// Close all folders
								function collapseTree()
								{
									//hide all folders
									clickOnNodeObj(foldersTree)
									//restore first level
									clickOnNodeObj(foldersTree)
								}
								
								//Environment variables are usually set at the top of this file.
								USELINKFORFOLDER = 0
								USETEXTLINKS = 0
								STARTALLOPEN = 0
								USEFRAMES = 0
								USEICONS = 0
								WRAPTEXT = 1
								PRESERVESTATE = 1
								ICONPATH = 'includes/javascript/Treeview/'
								BUILDALL = 0
								HIGHLIGHT = 1;
								foldersTree = gFld("Categories Tree View&nbsp;&nbsp;[<a href=\"javascript:;\" onClick=\"setTreeCheckbox('multiple_categories_sel', 'MSel_0_0', true, '<?=implode(",", $parent_to_child)?>');\">Check All</a>]&nbsp;&nbsp;[<a href=\"javascript:;\" onClick=\"setTreeCheckbox('multiple_categories_sel', 'MSel_0_0', false, '<?=implode(",", $parent_to_child)?>');\">Uncheck All</a>]", "")
								<?  $SubTotal = tep_show_list_items($ListItems, 1); ?>
							</script>
							<a style="font-size:7pt;text-decoration:none;color:silver" href=http://www.treemenu.net/ target=_blank></a>
							<span class=formvalue>
								<script>initializeDocument()</script>
								<noscript>
								A tree for site navigation will open here if you enable JavaScript in your browser.
								</noscript>
							</span>
						</td>
					</tr>
				</table>
				<?
					for ($i=0; $i<count($HiddenItems); $i++) {
						echo "<input type='hidden' name=HiddenCat[".$HiddenItems[$i]['id']."] value=".$HiddenItems[$i]['value']." id=HiddenCat_".$HiddenItems[$i]['id'].">\n";
					}
				?>
			</td>
		</tr>
		<tr>
  			<td align="center" colspan="2">
				<a href="javascript: call_back();"><?=tep_image_button('button_select.gif', IMAGE_SELECT)?></a>
				<script>
					function call_back() {
						document.multiple_categories_sel.submit();
					}
					
					function assign_selection(obj) {
						if (obj.checked == true) {
							document.getElementById('HiddenCat_'+obj.value).value = 1;
						} else {
							document.getElementById('HiddenCat_'+obj.value).value = 0;
						}
					}
				</script>
			</td>
		</tr>
	</form>
<?
} else if ($action == "add_price_set") {
	$multi_oID = tep_array_unserialize(rawurldecode($_REQUEST['s_orders_batch']));
	if (tep_not_null($_REQUEST["qty_str"])) {
		$package_qty_array = explode(';', $_REQUEST["qty_str"]);
	} else {
		$package_qty_array = array();
	}
	$price_set_header = $price_set_body = '';
	foreach ($package_qty_array as $qty) {
		$price_set_header .= '<td align="center" class="reportBoxHeading">'.$qty.'</td><td width="1px" class="reportBoxHeading">&nbsp;</td>';
		$price_set_body .= '<td align="center" class="reportRecords">'.tep_draw_input_field('price_set['.$qty.']', '', 'size="10" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ').'</td><td width="1px" class="reportRecords">&nbsp;</td>';
	}
?>
		<tr>
			<td width="100%">
				<table border="0" width="100%" cellspacing="0" cellpadding="0">
					<tr>
						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
		<tr>
			<td width="100%">
<?			echo tep_draw_form('add_price_set_form', FILENAME_POPUP_PRODUCTS_LIST, tep_get_all_get_params(array('action')) . 'action=insert_price_set', 'post', 'onSubmit="return add_price_set_form_checking();"');
			echo tep_draw_hidden_field("qty_str", $_REQUEST["qty_str"]);
?>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td class="main" width="20%" valign="top"><?=ENTRY_UNIQUE_PRICE_SET_ID?></td>
						<td class="main" valign="top"><?=tep_draw_input_field('unique_price_set_id', '', 'size="40" id="unique_price_set_id" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }"') . '&nbsp;' . TEXT_NUMBER_ONLY?></td>
					</tr>
					<tr>
        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
					<tr>
						<td class="main" valign="top"><?=ENTRY_PRICE_SET_NAME?></td>
						<td class="main"><?=tep_draw_input_field('price_set_name', '', 'size="40" id="price_set_name"')?></td>
					</tr>
					<tr>
        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
						<td class="main" valign="top"><?=ENTRY_PRICE_SET_ORDER?></td>
						<td class="main"><?=tep_draw_input_field('price_set_order', 50000, 'size="40" id="price_set_order" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }"')?></td>
					</tr>
					<tr>
        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
						<td class="main" valign="top"><?=ENTRY_PRICE_SET?></td>
						<td class="main">
							<table border="0" cellspacing="0" cellpadding="2">
								<tr><?=$price_set_header?></tr>
								<tr class="reportListingOdd"><?=$price_set_body?></tr>
							</table>
						</td>
					</tr>
					<tr>
        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
					<tr>
						<td colspan="2" align="right">
							<?=tep_image_submit('button_insert.gif', IMAGE_INSERT, "") . '&nbsp;&nbsp;<a href="javascript:;" onClick="closeme();">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'?>
						</td>
					</tr>
				</table>
			</form>
			</td>
		</tr>
		<script language="javascript">
		<!--
			function add_price_set_form_checking() {
				var error_message = '<?=JS_ERROR?>';
				var error = false;
				var focus_field = '';
				
				var unique_price_set_id = DOMCall('unique_price_set_id');
				if (trim_str(unique_price_set_id.value) == "") {
					error_message += '* Please enter the Unique ID.' + "\n";
					focus_field = 'unique_price_set_id';
					error = true;
				} else if (!validateInteger(trim_str(unique_price_set_id.value)) || unique_price_set_id.value == 0) {
					error_message += '* Unique ID must be a positive number.' + "\n";
					focus_field = 'unique_price_set_id';
					error = true;
				}
				
				var price_set_name = DOMCall('price_set_name');
				if (trim_str(price_set_name.value) == "") {
					error_message += '* Please enter the Price Set Name.' + "\n";
					if (focus_field.length < 1) focus_field = 'price_set_name';
					error = true;
				}
				
				if (error == true) {
					alert(error_message);
					document.getElementById(focus_field).focus();
					return false;
				} else {
					return true;
				}
			}
		//-->
		</script>
<?
} else if ($action == "edit_price_set") {
	//serialized_package_qty_array
	if (tep_not_null($_REQUEST["qty_str"])) {
		$package_qty_array = explode(';', $_REQUEST["qty_str"]);
		$_SESSION["s_package_qty"] = $package_qty_array;
	} else if (isset($_SESSION["s_package_qty"])) {
		$package_qty_array = $_SESSION["s_package_qty"];
	} else {
		$package_qty_array = array();
	}
	
	$distinct_qty_set = array();
	/*
	$unique_qty_select_sql = "SELECT DISTINCT batch_update_qty FROM " . TABLE_BATCH_UPDATE_PRICE_SETS_VALUES . " ORDER BY batch_update_qty";
	$unique_qty_result_sql = tep_db_query($unique_qty_select_sql);
	while($unique_qty_row = tep_db_fetch_array($unique_qty_result_sql)) {
		$distinct_qty_set[] = $unique_qty_row["batch_update_qty"];
	}
	*/
	//	Provide the chances to add new qty price
	foreach($package_qty_array as $user_define_qty) {
		if (!in_array($user_define_qty, $distinct_qty_set)) {
			$distinct_qty_set[] = $user_define_qty;
		}
	}
	
	$price_set_array = array();
	$price_set_select_sql = "SELECT batch_update_price_sets_id, batch_update_price_sets_name, batch_update_price_sets_sort_order FROM " . TABLE_BATCH_UPDATE_PRICE_SETS . " ORDER BY batch_update_price_sets_sort_order, batch_update_price_sets_name";
	$price_set_result_sql = tep_db_query($price_set_select_sql);
	while($price_set_row = tep_db_fetch_array($price_set_result_sql)) {
		$price_set_array[$price_set_row["batch_update_price_sets_id"]] = array(	'name' => $price_set_row["batch_update_price_sets_name"],
																				'sort_order' => $price_set_row["batch_update_price_sets_sort_order"],
																				'set' => array()
																			);
		
		$price_set_values_select_sql = "SELECT batch_update_qty, batch_update_price FROM " . TABLE_BATCH_UPDATE_PRICE_SETS_VALUES . " WHERE batch_update_price_sets_id ='" . $price_set_row["batch_update_price_sets_id"] . "' ORDER BY batch_update_qty";
		$price_set_values_result_sql = tep_db_query($price_set_values_select_sql);
		while($price_set_values_row = tep_db_fetch_array($price_set_values_result_sql)) {
			$price_set_array[$price_set_row["batch_update_price_sets_id"]]['set'][$price_set_values_row["batch_update_qty"]] = $price_set_values_row["batch_update_price"];
		}
	}
?>
		<tr>
			<td width="100%">
				<table border="0" width="100%" cellspacing="0" cellpadding="0">
					<tr>
						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
		<tr>
			<td width="100%">
<?			echo tep_draw_form('edit_price_set_form', FILENAME_POPUP_PRODUCTS_LIST, tep_get_all_get_params(array('action')) . 'action=update_price_set', 'post', 'onSubmit="return edit_price_set_form_checking();"');?>
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td width="10%" class="reportBoxHeading"><?=TABLE_HEADING_PRICE_SET_ID?></td>
   						<td class="reportBoxHeading"><?=TABLE_HEADING_PRICE_SET_NAME?></td>
       					<td width="10%" class="reportBoxHeading"><?=TABLE_HEADING_PRICE_SET_ORDER?></td>
<?
	foreach ($distinct_qty_set as $qty_str_val) {
		echo '<td align="center" class="reportBoxHeading">'.$qty_str_val.'</td>';
	}
?>
						<td width="5%" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
       				</tr>
<?
	$row_count = 0;
	foreach ($price_set_array as $set_id => $set_res) {
		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
		
		echo '		<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
						<td class="reportRecords">'.tep_draw_input_field('set_info['.$set_id.'][set_id]', $set_id, 'size="10" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }"').'</td>
   						<td class="reportRecords">'.tep_draw_input_field('set_info['.$set_id.'][set_name]', $set_res["name"], 'size="25" id="price_set_name"').'</td>
       					<td class="reportRecords">'.tep_draw_input_field('set_info['.$set_id.'][sort_order]', $set_res["sort_order"], 'size="10" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }"').'</td>';
       	foreach ($distinct_qty_set as $qty_str_val) {
			echo '<td align="center" class="reportRecords">'.tep_draw_input_field('price_set['.$set_id.']['.$qty_str_val.']', $set_res["set"][$qty_str_val], 'size="10" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" ').'</td>';
		}
		echo '<td align="center" class="reportRecords"><a href="javascript:void(confirm_delete(\''.$set_id.'\', \'Price Set\', \''.tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'action=delete_price_set&sID='.$set_id).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"').'</a></td>';
		
       	echo '		</tr>';
       	
       	$row_count++;
	}
?>
					<tr>
        				<td colspan="<?=(4+count($distinct_qty_set))?>"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>	
					<tr>
						<td colspan="<?=(4+count($distinct_qty_set))?>" align="right">
							<?=tep_image_submit('button_update.gif', IMAGE_UPDATE, "") . '&nbsp;&nbsp;<a href="javascript:;" onClick="closeme();">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>'?>
						</td>
					</tr>
				</table>
			</form>
			</td>
		</tr>
		<script language="javascript">
		<!--
			function edit_price_set_form_checking() {
				return true;
			}
		//-->
		</script>
<?
} else if ($action == "show_success_page") {
?>
		<tr>
			<td width="100%" align="center"><br><br>
				<h3>Action had been successfully performed. This page will close soon.</h3>
			</td>
		</tr>
		<script language="javascript">
		<!--
			setTimeout('closeme()', 3000);
		//-->
		</script>
<?
} else if ($action == "category_list") {
	$categories_array = array();
	$categories_array = tep_get_category_tree(0, '___', '', $categories_array, false, 0, true);
?>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
		</tr>
		<tr>
			<td class="main" valign="top">Categories:</td>
			<td class="main" valign="top">
				<?=tep_draw_pull_down_menu('cat_sel', $categories_array, '',' id="cat_sel" ')?>
			</td>
		</tr>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
		</tr>
		<tr>
  			<td align="center" colspan="2">
				<a href="javascript: call_back();"><?=tep_image_button('button_select.gif', IMAGE_SELECT)?></a>
				<script>
					function call_back() {
						var tmpVal = document.getElementById('cat_sel').value;
						if (tmpVal != '') {
<?	if (tep_not_null($by_fieldname)) { ?>
							opener.getReturnedValue(tmpVal, '<?=$by_fieldname?>');
<?	} else { ?>
							opener.getReturnedValue(tmpVal);
<?	} ?>
							self.close();
							opener.focus();
						} else {
							alert('No category is selected!')
						}
					}
				</script>
			</td>
		</tr>	
<?
} else if ($action == "category_cache") {
	$by_fieldname = (isset($_REQUEST['fieldname']) ? tep_db_prepare_input($_REQUEST['fieldname']) : '');
	$categories_array = array();
	$categories_array = tep_get_category_tree_cacheable(0, '___', '', $categories_array, false, 0, true);
?>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
		</tr>
		<tr>
			<td class="main" valign="top">Categories:</td>
			<td class="main" valign="top">
				<?=tep_draw_pull_down_menu('cat_sel', $categories_array, '',' id="cat_sel" ')?>
			</td>
		</tr>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
		</tr>
		<tr>
  			<td align="center" colspan="2">
				<a href="javascript: call_back();"><?=tep_image_button('button_select.gif', IMAGE_SELECT)?></a>
				<script>
					function call_back() {
						var tmpVal = document.getElementById('cat_sel').value;
						if (tmpVal != '') {
<?	if (tep_not_null($by_fieldname)) { ?>
							opener.getReturnedValue(tmpVal, '<?=$by_fieldname?>');
<?	} else { ?>
							opener.getReturnedValue(tmpVal);
<?	} ?>
							self.close();
							opener.focus();
						} else {
							alert('No category is selected!')
						}
					}
				</script>
			</td>
		</tr>	
<?
} else if ($action == "products_cache") {
	$by_fieldname = (isset($_REQUEST['fieldname']) ? tep_db_prepare_input($_REQUEST['fieldname']) : '');
	$categories_array = array();
	$categories_array = tep_get_category_tree_cacheable(0, '___', '', $categories_array, false, 0, true);
?>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
		</tr>
		<tr>
			<td class="main" valign="top">Categories:</td>
			<td class="main" valign="top">
				<?=tep_draw_pull_down_menu('cat_sel', $categories_array, '','onChange="refreshDynamicSelectOptions(this, \'prod_sel\', \''.(int)$languages_id.'\', \'all\', false, \'\', \''.$_SESSION['login_id'].'\');" id="cat_sel" ')?>
			</td>
		</tr>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
		<tr>
			<td class="main" valign="top">Products :</td>
			<td class="main" valign="top">
				<select name="prod_sel" id="prod_sel">
					<option value="">---Select Product---</option> 
				</select>
			</td>
		</tr>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
		</tr>
		<tr>
  			<td align="center" colspan="2">
				<a href="javascript: call_back();"><?=tep_image_button('button_select.gif', IMAGE_SELECT)?></a>
				<script>
					function call_back() {
						var tmpVal = document.getElementById('prod_sel').value;
						if (tmpVal != '') {
<?	if (tep_not_null($by_fieldname)) { ?>
							opener.getReturnedValue(tmpVal, '<?=$by_fieldname?>');
<?	} else { ?>
							opener.getReturnedValue(tmpVal);
<?	} ?>
							self.close();
							opener.focus();
						} else {
							alert('No product is selected!')
						}
					}
				</script>
			</td>
		</tr>
<?
}
?>
		<tr>
        	<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
		</tr>
	</table>
</body>
</html>