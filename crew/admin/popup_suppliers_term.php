<?
/*
 * Created: 16/12/2016
 * By:      Azri
 */

require('includes/application_top.php');

if(empty($_REQUEST['po_supplier'])) {
    $po_term_content = "Error: No content to show.";
} else {
    $supplier_id = tep_db_prepare_input($_REQUEST['po_supplier']);
    
    $supplier_sql = "SELECT po_supplier_agreement_discount_terms "
            . "FROM " . TABLE_PO_SUPPLIERS . " "
            . "WHERE po_suppliers_id = '" . tep_db_input($supplier_id) . "'";
    $supplier_result = tep_db_query($supplier_sql);
    $supplier_info = tep_db_fetch_array($supplier_result);
    $po_term_content = $supplier_info['po_supplier_agreement_discount_terms'];
}

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
	<script language="javascript" src="includes/javascript/product_listing.js"></script>
	<script language="JavaScript">
        <!--
            var Nav4 = ((navigator.appName == "Netscape") && (parseInt(navigator.appVersion) >= 4))
		
            function centerWin() {
                var NS = false;
                if (document.all) {
                   /* the following is only available after onLoad */
                   w = document.body.clientWidth;
                   h = document.body.clientHeight;
                   NS = true;
                } else if (document.layers) {
                    ;
                }
			
	     	if (!NS) {
                    self.moveTo((self.screen.width - self.outerWidth) / 2, (self.screen.height - self.outerHeight) / 2);
	     	} else {
                    self.moveTo((self.screen.width-document.body.clientWidth) / 2, (self.screen.height-document.body.clientHeight) / 2);
	    	}
	    }
	    
            // Close the dialog
            function closeme() {
                window.close()
            }
		
            // Handle click of OK button
            function handleOK() {
                if (opener && !opener.closed) {
                    opener.dialogWin.returnFunc();
                } else {
                    alert("You have closed the main window.\n\nNo action will be taken on the choices in this dialog box.")
                }
                closeme();
                return false;
            }
        //-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" onLoad="centerWin(); if (opener) opener.blockEvents()" onUnload="if (opener) opener.unblockEvents()">
    <div style="margin: 20px;">
        <h4>Agreement Discount Terms:</h4>    
        <pre><?= $po_term_content; ?></pre>
    </div>
    <div align="center">
        <?=tep_submit_button('Close', '', 'onclick="self.close()"', 'inputButton');?>
        <?=tep_submit_button('Preview PO', '', 'onclick="doTheSubmit();"', 'inputButton');?>
    </div>
    <script language="JavaScript" type="text/javascript">
        function doTheSubmit() {
          window.opener.document.forms["po_form"].submit();
          window.close();
        }
      </script>
</body>
</html>