<?
header("Expires: Mon, 02 May 2001 23:00:00 GMT");
header("Cache-Control: no-store, no-cache, must-revalidate");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
if( !function_exists('memory_get_usage') )
{
    function memory_get_usage()
    {
        //If its Windows
        //Tested on Win XP Pro SP2. Should work on Win 2003 Server too
        //Doesn't work for 2000
        //If you need it to work for 2000 look at http://us2.php.net/manual/en/function.memory-get-usage.php#54642
        if ( substr(PHP_OS,0,3) == 'WIN')
        {
               if ( substr( PHP_OS, 0, 3 ) == 'WIN' )
                {
                    $output = array();
                    exec( 'tasklist /FI "PID eq ' . getmypid() . '" /FO LIST', $output );
       				
       				$memory_usage = preg_replace( '/[\D]/', '', $output[5] ) * 1024;
                    return $memory_usage;
                }
        }else
        {
            //We now assume the OS is UNIX
            //Tested on Mac OS X 10.4.6 and Linux Red Hat Enterprise 4
            //This should work on most UNIX systems
            $pid = getmypid();
            exec("ps -eo%mem,rss,pid | grep $pid", $output);
            $output = explode(" ", $output[0]);
            //rss is given in 1024 byte units
            $memory_usage = $output[1] * 1024;
            return $memory_usage;
        }
    }
}

require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'price_automation.php');
require_once(DIR_WS_CLASSES . 'json.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_FUNCTIONS . 'buyback.php');
require_once(DIR_WS_LANGUAGES . $language . '/price_automation.php');

tep_set_time_limit(0);

define('DISPLAY_PRICE_DECIMAL', 6);

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$game_cat_id_arr = tep_get_game_list_arr();

$action = (isset($_GET['action']) ? $_GET['action'] : '');

$main_cat_id = 0;

if (isset($_REQUEST['game_cat_id'])) {
	$main_cat_id = $_REQUEST['game_cat_id'];
}

if ($main_cat_id > 0) {
    $PriceAutomationObj = new price_automation($main_cat_id);
    if (!$PriceAutomationObj->is_buyback_game()) {
    	$messageStack->add_session(ERROR_AVG_PRICE_INVALID_GAME, 'error');
    	tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('game_cat_id', 'action'))));
    } else {
    	$PriceAutomationObj->set_products();
    }
    
    if (isset($_SESSION['perferred_margin'])) {
		$PriceAutomationObj->perferred_margin = $_SESSION['perferred_margin'];
	}
	if (isset($_SESSION['perferred_rank'])) {
		$PriceAutomationObj->pa_bb_preferred_rank = $_SESSION['perferred_rank'];
	}
}

$json = new Services_JSON();
//******************Defination********************
$colour_picker_arr = array('yellow'=>'#FFFF99', 'blue'=>'#66FFFF', 'green'=>'#00FF99', 'red'=>'#FF6699', 'grey'=>'#E5E5E5');
$bo_days_colour_arr = tep_set_alert_color($main_cat_id, 'bo_days');
$margin_colour_arr = tep_set_alert_color($main_cat_id, 'margin');
$min_row_height = 'height:38px;';
$import_buyback = '';
$import_buyback_csv = false;
$import_selling_csv = false;

switch($action) {
	case 'download_history_html':
		$get_contents = '';
		if (isset($_GET['file_name']) && tep_not_null($_GET['file_name'])) {
			$file_path = 'download/html/'.$main_cat_id.'/'.$_GET['file_name'];
			if (file_exists($file_path)) {
				readfile($file_path);
				exit;
			}
		}
		break;
	case 'batch_update_setting':
		if (isset($_POST['package_quantities'])) {
			if ($_POST['package_quantities'] == 'all') {
				$cat_cfg_update_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'] = 'all';
			} else {
				$cat_cfg_update_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'] = tep_db_prepare_input($_POST['package_quantities']);
			}
			tep_save_categories_configuration($main_cat_id, $cat_cfg_update_array, $messageStack);
		}
		
		break;
	case 'csv_import':
		if (tep_not_null($main_cat_id)) {
			if ($_POST['import_action'] == 'yes') {
// Import Buyback
				if ( tep_not_null($_FILES['import_buyback']['tmp_name']) && ($_FILES['import_buyback']['tmp_name'] != 'none') && is_uploaded_file($_FILES['import_buyback']['tmp_name']) ) {
					if ($_FILES['import_buyback']["size"] > 0) {
						
						$import_error = false;
						$filename = ($_FILES['import_buyback']['tmp_name']);
					    $handle = fopen($filename, 'r+');
						
					    $overallRowNo = 0;
					    $rowNo = 0;
					    $Product_RowNo = 0;
					    
					    $exist_product_id_array = array_keys($PriceAutomationObj->products_arr);
					    
					    $is_competitor_data = true;
					    $is_product_data = false;
						
				    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
				    		$num_cols = count($data);
				    		if ($num_cols > 1 && trim($data[0]) != '') { //Ignore blank lines
								$overallRowNo ++;
								
					    		if ($overallRowNo == 1) {
									$data[1] = strtoupper($data[1]);
									if (isset($currencies->currencies[$data[1]]))	$csv_currency_used = $data[1];
									continue;
								} else {
									$rowNo++;
								}
								
								if (substr($data[0], 0, 3) == '###') {	// Separator between competitors and products
								    $is_competitor_data = false;
								    $is_product_data = true;
								    $rowNo = 0; //reInit the rowno so we know where is the column heading for next set of data
								    continue;
								}
								
								if ($is_competitor_data) {
									//todo: check correct column headers if $rowno ==1
									if ($rowNo > 1) {
										$PriceAutomationObj->pa_bb_assign_competitor(trim($data[0]), trim($data[1]));
									}
								} else {
									//todo: check correct column headers if $rowno ==1
									if ($rowNo == 1) {
										$num_prices_columns = ($num_cols-2)/2;
										if ($num_prices_columns != $PriceAutomationObj->pa_bb_total_competitors) {
											$messageStack->add_session(MESSAGE_MISMATCHED_COLUMNS);
											$import_error = true;
											break;
										}
									} else if ($rowNo > 1) {
										$products_id = $data[0];
										$products_cat_path = $data[1];
										
										$prod_category_select_sql = "	SELECT categories_id 
																		FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " 
																		WHERE products_id = '".tep_db_input($products_id)."' 
																		AND products_is_link=0";
										$prod_category_result_sql = tep_db_query($prod_category_select_sql);
								    	if ($prod_category_row = tep_db_fetch_array($prod_category_result_sql)) {
								    		if (in_array($products_id, $exist_product_id_array)) {
								    			$display_game_name_array = tep_display_category_path($products_cat_path, $prod_category_row['categories_id']);
									    		$PriceAutomationObj->pa_bb_products_array[$products_id]['cat_id'] = $prod_category_row['categories_id'];
									    		
									    		$PriceAutomationObj->pa_bb_products_array[$products_id]['products_cat_path'] = $display_game_name_array;
									    		
									    		$PriceAutomationObj->pa_bb_total_products = count($PriceAutomationObj->pa_bb_products_array);
									    		
									    		$PriceAutomationObj->game_product_id[] = $products_id;
									    		
												$competitor_id_arr = array_keys($PriceAutomationObj->pa_bb_competitors_array);
												$colNo = 2;
												
												$buyback_highest_price = $buyback_full_highest_price = 0.00;
												
												foreach ($competitor_id_arr as $competitor_id) {
													$statusStr = trim(strtolower($data[$colNo+1]));
													$competitor_buying_price = $currencies->advance_currency_conversion((double)$data[$colNo], $csv_currency_used, DEFAULT_CURRENCY, true, 'buy');	// Must same rate as when we show our buying price in I want to sell page
													
													switch ($statusStr) {
														case 'full':
															$is_buyback = 0;
															$buyback_full_highest_price = $competitor_buying_price > $buyback_full_highest_price ? $competitor_buying_price : $buyback_full_highest_price;
															break;
														default:
															$is_buyback = 1;
															$buyback_highest_price = $competitor_buying_price > $buyback_highest_price ? $competitor_buying_price : $buyback_highest_price;
															break;
													}
													
													$PriceAutomationObj->pa_bb_assign_competitors_price($products_id, $competitor_id, $competitor_buying_price, $is_buyback);
													$colNo += 2;
												}
												$PriceAutomationObj->set_competitor_highest_price($products_id, $buyback_highest_price, $buyback_full_highest_price, 'buyback');
												unset($buyback_highest_price);
												unset($buyback_full_highest_price);
											} else {
												$messageStack->add_session(sprintf(ERROR_AVG_PRICE_IMPORTED_PRODUCT_NOT_MATCH, $products_id), 'warning');
												$import_error = true;
											}
										} else {
											$messageStack->add_session(sprintf(WARNING_PRODUCT_DOES_NOT_EXISTS, $products_id, $products_cat_path), 'warning');
											$import_error = true;
										}
									}
								}
								$overallRowNo ++;
							}
				    	}
				    	
					    if (!$import_error) {
							//no errors
							$import_buyback_csv = true;
					    	fclose($handle);
					    	$messageStack->add(SUCCESS_AVG_BUYBACK_PRICE_IMPORT_FILE, 'success');
					    } else {
					    	fclose($handle);
					    	$messageStack->add_session(ERROR_IMPORT_FAILED);
					    	tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=csv_import'));
					    }
					    unset($data);
					} else {
						$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
				    	tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=csv_import'));
					}
				} elseif ( tep_not_null($_FILES['import_buyback']['tmp_name']) && ($_FILES['import_buyback']['tmp_name'] != 'none') && is_uploaded_file($_FILES['import_buyback']['tmp_name']) ) {
					$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
			    	tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=csv_import'));
				}
// Import Selling
				unset($exist_product_id_array);
				if ( tep_not_null($_FILES['import_selling']['tmp_name']) && ($_FILES['import_selling']['tmp_name'] != 'none') && is_uploaded_file($_FILES['import_selling']['tmp_name']) ) {
					if ($_FILES['import_selling']["size"] > 0) {
						
						$import_error = false;
						$filename = ($_FILES['import_selling']['tmp_name']);
					    $handle = fopen($filename, 'r+');
						
					    $overallRowNo = 0;
					    $rowNo = 0;
					    $Product_RowNo = 0;
					    
					    if (count($PriceAutomationObj->pa_bb_products_array) > 0) {
					    	$exist_product_id_array = array_keys($PriceAutomationObj->pa_bb_products_array);
					    } else {
					    	$exist_product_id_array = array_keys($PriceAutomationObj->products_arr);
					    }
					    
					    $is_competitor_data = true;
					    $is_product_data = false;

				    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
				    		$num_cols = count($data);
				    		if ($num_cols > 1 && trim($data[0]) != '') { //Ignore blank lines
								$overallRowNo ++;
								
					    		if ($overallRowNo == 1) {
									$data[1] = strtoupper($data[1]);
									if (isset($currencies->currencies[$data[1]]))	$csv_currency_used = $data[1];
									continue;
								} else {
									$rowNo++;
								}
								
								if (substr($data[0], 0, 3) == '###') {	// Separator between competitors and products
								    $is_competitor_data = false;
								    $is_product_data = true;
								    $rowNo = 0; //reInit the rowno so we know where is the column heading for next set of data
								    continue;
								}
								
								if ($is_competitor_data) {
									//todo: check correct column headers if $rowno ==1
									if ($rowNo > 1) {
										$PriceAutomationObj->pa_selling_assign_competitor(trim($data[0]), trim($data[1]));
									}
								} else {
									//todo: check correct column headers if $rowno ==1
									if ($rowNo == 1) {
										$num_prices_columns = ($num_cols-2)/2;
										if ($num_prices_columns != $PriceAutomationObj->pa_selling_total_competitors) {
											$messageStack->add_session(MESSAGE_MISMATCHED_COLUMNS);
											$import_error = true;
											break;
										}
									} else if ($rowNo > 1) {
										$products_id = $data[0];
										$products_cat_path = $data[1];
										
										
							    		if (in_array($products_id, $exist_product_id_array)) {
							    			$display_game_name_array = $PriceAutomationObj->products_arr[$products_id]['game_name'];
							    			
								    		$PriceAutomationObj->pa_selling_products_array[$products_id]['cat_id'] = $PriceAutomationObj->products_arr[$products_id]['categories_id'];
								    		$PriceAutomationObj->pa_selling_products_array[$products_id]['products_cat_path'] = $display_game_name_array;
								    		$PriceAutomationObj->pa_selling_total_products = count($PriceAutomationObj->pa_selling_products_array);
											$competitor_id_arr = array_keys($PriceAutomationObj->pa_selling_competitors_array);
											$colNo = 2;
											
											$selling_highest_price = $selling_full_highest_price = 0.00;
											
											foreach ($competitor_id_arr as $competitor_id) {
												$statusStr = trim(strtolower($data[$colNo+1]));
												$competitor_buying_price = $currencies->advance_currency_conversion((double)$data[$colNo], $csv_currency_used, DEFAULT_CURRENCY, true, 'buy');	// Must same rate as when we show our buying price in I want to sell page
												
												switch ($statusStr) {
													case 'full':
														$is_buyback = 0;
														$selling_full_highest_price = $competitor_buying_price > $selling_full_highest_price ? $competitor_buying_price : $selling_full_highest_price;
														break;
													default:
														$is_buyback = 1;
														$selling_highest_price = $competitor_buying_price > $selling_highest_price ? $competitor_buying_price : $selling_highest_price;
														break;
												}
												
												$PriceAutomationObj->pa_selling_assign_competitors_price($products_id, $competitor_id, $competitor_buying_price, $is_buyback);
												$colNo += 2;
											}
											$PriceAutomationObj->set_competitor_highest_price($products_id, $selling_highest_price, $selling_full_highest_price, 'selling');
											unset($selling_highest_price);
											unset($selling_full_highest_price);
										} else {
											$messageStack->add_session(sprintf(WARNING_PRODUCT_DOES_NOT_EXISTS, $products_id, $products_cat_path), 'warning');
											$import_error = true;
										}
										
									}
								}
								$overallRowNo ++;
							}
				    	}
					    if (!$import_error) {
							//no errors
							$import_selling_csv = true;
					    	fclose($handle);
					    	$messageStack->add(SUCCESS_AVG_SELLING_PRICE_IMPORT_FILE, 'success');
					    } else {
					    	fclose($handle);
					    	$messageStack->add_session(ERROR_IMPORT_FAILED);
					    	tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=csv_import'));
					    }
					    unset($data);
					} else {
						$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
				    	tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=csv_import'));
					}
				} elseif ( tep_not_null($_FILES['import_selling']['tmp_name']) && ($_FILES['import_selling']['tmp_name'] != 'none') && is_uploaded_file($_FILES['import_selling']['tmp_name']) ) {
					$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
			    	tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=csv_import'));
				}
				unset($exist_product_id_array);
			}
		} else {
			echo JS_MSG_PLS_SELECT_THE_CATEGORY;
		}
		break;
		
// CSV EXPORT

	case 'csv_export':
		if (tep_not_null($main_cat_id)) {
			//Clicked export
			
			$competitors_select_sql = "	SELECT c.competitors_id, c.competitors_name 
										FROM " . TABLE_COMPETITORS . " AS c 
										LEFT JOIN " . TABLE_COMPETITORS_TO_CATEGORIES  . " AS c2c 
											ON (c.competitors_id = c2c.competitors_id) 
										WHERE c2c.categories_id = '" . tep_db_input($main_cat_id) . "'";
			$competitors_result_sql = tep_db_query($competitors_select_sql);
			$competitors_num = tep_db_num_rows($competitors_result_sql);
			if ($competitors_num > 0) {
				while ($competitors_row = tep_db_fetch_array($competitors_result_sql)) {
					$PriceAutomationObj->pa_bb_competitors_array[$competitors_row['competitors_id']] = array('code' => $competitors_row['competitors_name']);
					$PriceAutomationObj->pa_bb_total_competitors = count($PriceAutomationObj->pa_bb_competitors_array);
				}
			} else {
				$messageStack->add_session(sprintf(ERROR_NO_COMPETITOR_IN_THIS_CATEGORY, FILENAME_COMPETITORS), 'warning');
				tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=csv_export'));
			}
			
			if (tep_not_null($PriceAutomationObj->pa_bb_competitors_array) && tep_not_null($PriceAutomationObj->pa_bb_total_competitors)) {
				$export_csv_data = $PriceAutomationObj->pa_get_data_csv();
				$filename = 'price_automation_export.csv';
				$mime_type = 'text/x-csv';
				// Download
		        header('Content-Type: ' . $mime_type);
		        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
		        // IE need specific headers
		        if (PMA_USR_BROWSER_AGENT == 'IE') {
		            header('Content-Disposition: inline; filename="' . $filename . '"');
		            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		            header('Pragma: public');
		        } else {
		            header('Content-Disposition: attachment; filename="' . $filename . '"');
		            header('Pragma: no-cache');
		        }
				echo $export_csv_data;
				exit();
			} else {
				$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
				tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=csv_export'));
			}
		} else {
			echo '<script>alert("'.JS_MSG_PLS_SELECT_THE_CATEGORY.'");</script>';
		}
		break;
		
// BUYBACK CSV EXPORT

	case 'buyback_csv_export':
		if (tep_not_null($main_cat_id)) {
			$bb_suggested_price = '';
			$path = '';
			
			if (tep_not_null($_POST['bb_suggested_price']) && tep_not_null($_POST['buyback_overwrite_price'])) {
				$export_csv_data = $PriceAutomationObj->pa_get_bb_data_csv($_POST['bb_suggested_price'], $_POST['buyback_overwrite_price']);
				
				$filename = 'pa_buyback_export_'.date('YmdHis').'.csv';
				$mime_type = 'text/x-csv';
				// Download
		        header('Content-Type: ' . $mime_type);
		        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
		        // IE need specific headers
		        if (PMA_USR_BROWSER_AGENT == 'IE') {
		            header('Content-Disposition: inline; filename="' . $filename . '"');
		            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		            header('Pragma: public');
		        } else {
		            header('Content-Disposition: attachment; filename="' . $filename . '"');
		            header('Pragma: no-cache');
		        }
				echo $export_csv_data;
				exit();
			} else {
				$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
				tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=buyback_csv_export'));
			}
		} else {
			echo '<script>alert("'.JS_MSG_PLS_SELECT_THE_CATEGORY.'");</script>';
		}
		break;

// SELLING CSV EXPORT

	case 'selling_csv_export':
		if (tep_not_null($main_cat_id)) {
			$bb_suggested_price = '';
			$path = '';
			
			if (tep_not_null($_POST['selling_suggested_price']) && tep_not_null($_POST['selling_overwrite_price'])) {
				$export_csv_data = $PriceAutomationObj->pa_get_bb_data_csv($_POST['selling_suggested_price'], $_POST['selling_overwrite_price']);
				
				$filename = 'pa_selling_export_'.date('YmdHis').'.csv';
				$mime_type = 'text/x-csv';
				// Download
		        header('Content-Type: ' . $mime_type);
		        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
		        // IE need specific headers
		        if (PMA_USR_BROWSER_AGENT == 'IE') {
		            header('Content-Disposition: inline; filename="' . $filename . '"');
		            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		            header('Pragma: public');
		        } else {
		            header('Content-Disposition: attachment; filename="' . $filename . '"');
		            header('Pragma: no-cache');
		        }
				echo $export_csv_data;
				exit();
			} else {
				$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
				tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=selling_csv_export'));
			}
		} else {
			echo '<script>alert("'.JS_MSG_PLS_SELECT_THE_CATEGORY.'");</script>';
		}
		break;
		
// ALERT SETTING

	case 'alert_setting':
		$row_colour_value_arr = array();
		$prev_row_colour_value_arr = array();
		$delete_colour_setting_arr = array();
		$varified = false;
		$alert_setting_type = '';
		
		if (tep_not_null($_POST['bo_days_alert_color'])) {
			if (tep_not_null($_POST['as_bo_days'])) {
				$varified = true;
				$alert_setting_type = 'bo_days';
				$row_colour_value_arr = $_POST['as_bo_days'];
				$prev_row_colour_value_arr = $_POST['as_bo_days_prev'];
				$delete_colour_setting_arr = $_POST['bo_days_delete_colour_setting'];
			}
		} elseif (tep_not_null($_POST['margin_alert_color'])) {
			if (tep_not_null($_POST['as_margin'])) {
				$varified = true;
				$alert_setting_type = 'margin';
				$row_colour_value_arr = $_POST['as_margin'];
				$prev_row_colour_value_arr = $_POST['as_margin_prev'];
				$delete_colour_setting_arr = $_POST['margin_delete_colour_setting'];
			}
		}

		if ($varified) {
			foreach ($row_colour_value_arr as $id => $row_colour_value) {
				
				$is_delete = false;
				$alert_setting_data_array = array();
				
				if (isset($delete_colour_setting_arr[$id])) {
					$is_delete = true;
				}
				$row_colour = $_POST[$alert_setting_type.'_row_colour'][$id];
				$row_colour_value = tep_db_prepare_input((string)$row_colour_value);
				$prev_row_colour_value = tep_db_prepare_input((string)$prev_row_colour_value_arr[$id]);
				if (!$is_delete && $row_colour && strlen($row_colour_value)) {
					$alert_setting_select_sql = "	SELECT automate_alert_id, automate_alert_value 
													FROM " . TABLE_AUTOMATE_ALERT_COLOR . " 
													WHERE categories_id = '" . tep_db_input($main_cat_id) . "' 
														AND automate_alert_type = '" . tep_db_input($alert_setting_type) . "'
														AND automate_alert_value = '" . tep_db_input($prev_row_colour_value) . "'";
					$alert_setting_result_sql = tep_db_query($alert_setting_select_sql);
					$alert_setting_num = tep_db_num_rows($alert_setting_result_sql);
					$alert_setting_data_array = array(	'categories_id' => tep_db_prepare_input($main_cat_id),
											 			'automate_alert_type' => tep_db_prepare_input($alert_setting_type),
											 			'automate_alert_value' => tep_db_prepare_input($row_colour_value),
											 			'automate_alert_color' => tep_db_prepare_input($row_colour),
									     		);
					
					if ($alert_setting_num > 0) {
						tep_db_perform(TABLE_AUTOMATE_ALERT_COLOR, $alert_setting_data_array, 'update', "categories_id ='" . tep_db_input($main_cat_id) . "' AND automate_alert_type = '" . tep_db_input($alert_setting_type) . "' AND automate_alert_value = '" . tep_db_input($prev_row_colour_value) . "'");
					} else {
	                    tep_db_perform(TABLE_AUTOMATE_ALERT_COLOR, $alert_setting_data_array);
					}
				} else if ($is_delete) {
					$delete_alert_setting_sql = "	DELETE FROM " . TABLE_AUTOMATE_ALERT_COLOR . " 
													WHERE categories_id = '" . tep_db_input($main_cat_id) ."' 
														AND automate_alert_type = '" . tep_db_input($alert_setting_type) . "' 
														AND automate_alert_value = '" . tep_db_input($row_colour_value) . "'";
					tep_db_query($delete_alert_setting_sql);
				}
				unset($alert_setting_data_array);
			}
			$messageStack->add_session(SUCCESS_INSERT_N_UPDATE_PRICE_AUTOMATION_SETTING, 'success');
			tep_redirect(tep_href_link(FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=alert_setting'));
		}
		break;
		
		case 'export_csv':
			$filename = $_GET['file_name'];
			$cat_id = $_GET['cat_id'];
			
			$file_location = "download/csv/".$cat_id."/".$filename;
			$mime_type = 'text/x-csv';
			// Download
	        header('Content-Type: ' . $mime_type);
	        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
	        // IE need specific headers
	        if (PMA_USR_BROWSER_AGENT == 'IE') {
	            header('Content-Disposition: attachment; filename="' . $filename . '"');
	            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
	            header('Pragma: public');
	        } else {
	            header('Content-Disposition: attachment; filename="' . $filename . '"');
	            header('Pragma: no-cache');
	        }
	        readfile($file_location);
			exit();
		break; 
}

$cat_cfg_array = tep_get_cfg_setting((int)$main_cat_id, 'catalog', '9', 'group_id');

// Defination
$JSHTML = '';
$ProductSettingHTML = '';
$BuybackCompetitorHTML = '';
$BuybackPriceHTML = '';
$BackOrderHTML = '';
$SellingPriceSettingHTML = '';
$SellingPriceSettingRow = '';
$SellingCompetitorHeadingHTML = '';
$SellingCompetitorHTML = '';
$row_count = 0;
$row_cnt = 0;

// Selling Price Setting
$product_quantity_arr = array();
$avg_current_selling_price_array = array();
$selling_price_setting_permission = false;
// View Selling's Price Setting tab requirement;
if (tep_not_null($PriceAutomationObj->pa_preferred_margin) && (count($PriceAutomationObj->pa_bb_products_array) > 0)) {
	$selling_price_setting_permission = true;
	$PriceAutomationObj->get_current_selling_price();
} else {
	$selling_price_setting_permission = false;
}

$product_quantity_arr = explode(";", $cat_cfg_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE']);
sort($product_quantity_arr);
// Selling Price Setting EOF

// Selling Competitor's Price Setting
	$scps_row = 0;
	$scps_column = 0;
	$scps_td_width = 0;
	$scps_td_mod = 0;
	$scps_table_width = 100;
	$Average_selling_price = array();
	
	$scps_row = $PriceAutomationObj->pa_selling_total_products;
	$scps_column = $PriceAutomationObj->pa_selling_total_competitors;
	
	if ($PriceAutomationObj->pa_selling_total_competitors > 0) {
		$scps_table_width = $PriceAutomationObj->pa_selling_total_competitors * 100;
		$scps_td_width = round(100 / $PriceAutomationObj->pa_selling_total_competitors) . '%';
		$scps_td_mod = 100 % 3;
	}
// Selling Competitor's Price Setting EOF

if ($main_cat_id > 0) {
	if (count($PriceAutomationObj->pa_bb_products_array) > 0) {
		unset($PriceAutomationObj->products_arr);
		// ProductSetting 
		$PriceAutomationObj->pa_assign_backorder_oldest_order_day();
		// ProductSetting EOF
		
		// BuyBack Competitor Price Setting
		$bcps_row 			= 0;
		$bcps_column 		= 0;
		$bcps_td_width		= 0;
		$bcps_td_mod 		= 0;
		$bcps_table_width	= 100;
		$Average_bb_price 	= array();
		
		$BuybackCompetitorHeadingHTML = '';
		
		$bcps_row = $PriceAutomationObj->pa_bb_total_products;
		$bcps_column = $PriceAutomationObj->pa_bb_total_competitors;
		
		if ($PriceAutomationObj->pa_bb_total_competitors > 0) {
			$bcps_table_width = $PriceAutomationObj->pa_bb_total_competitors * 100;
			$bcps_td_width = round(100 / $PriceAutomationObj->pa_bb_total_competitors) . '%';
			$bcps_td_mod = 100 % 3;
		}
		if ($bcps_column > 0) {
			$BuybackCompetitorHeadingHTML .= '<tr>';
			$competitor_cnt = 0;
			foreach ($PriceAutomationObj->pa_bb_competitors_array as $buyback_competitor_key => $buyback_competitor_value) {
				$BuybackCompetitorHeadingHTML .= tep_draw_hidden_field('buyback_competitor_heading_column', $buyback_competitor_value['code'], 'id="buyback_competitor_heading_column_'.$competitor_cnt.'" class="buyback_competitor_heading_column"');
				$BuybackCompetitorHeadingHTML .= tep_draw_hidden_field('buyback_competitor_id', $buyback_competitor_key, 'id="buyback_competitor_id_'.$competitor_cnt.'"');
				$BuybackCompetitorHeadingHTML .= '<td class="buybackCompetitorHeadingColumn" width="'.$bcps_td_width.'" align="center">'.tep_draw_checkbox_field("bb_competitor_selection", $buyback_competitor_key, '', '', 'id="buyback_competitor_checkbox_'.$buyback_competitor_key.'"').'<br/>'.$buyback_competitor_value['code'].'</td>';
				$competitor_cnt ++;
			}
		    $BuybackCompetitorHeadingHTML .= '		<td width='.$bcps_td_mod.'>&nbsp;</td>
		    									</tr>';
		}
		// BuyBack Competitor Price Setting EOF
		
		// BuyBack Price Setting
		$buyback_current_price_arr = $PriceAutomationObj->get_buyback_current_price();
		// BuyBack Price Setting EOF
		
		// BackOrder Setting
		$completed_delivered_qty = 0;
		$backorder_days = 0;
		$PriceAutomationObj->get_backorder();
		$PriceAutomationObj->get_completed_delivered_qty();
		
		$JSHTML =  "var packages_products_id_arr = new Array(); \n";
		// BackOrder Setting EOF
		foreach ($PriceAutomationObj->pa_bb_products_array as $game_product_id => $game_info) {
			// ProductSetting 
			$row_color = '';
			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
			$row_cnt = $row_count;
			++$row_count;
			
			if ($PriceAutomationObj->backorder_oldest_order_day_arr[$game_product_id]) {
				$row_color = $PriceAutomationObj->bo_alert_color_setting($PriceAutomationObj->backorder_oldest_order_day_arr[$game_product_id]);
			}
			
			$ProductSettingHTML .= '<tr id="product_setting_game_server_id_'.$game_product_id.'" style="'.$min_row_height.'background:'.$row_color.';" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'ordersListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
			    						<td width="1" class="ordersRecords" align="center">';
			$ProductSettingHTML .= 		tep_draw_checkbox_field('game_product_id', $game_product_id, '', '', 'id="game_product_id_'.$game_product_id.'" onClick="storeCheckboxValue('.$game_product_id.')"');
			$ProductSettingHTML .=      tep_draw_hidden_field('game_product_name', $game_info['products_cat_path'], 'id="game_product_name_'.$game_product_id.'"');
		    $ProductSettingHTML .= '	</td>
			      						<td class="ordersRecords">'.$game_info['products_cat_path'].'</td>
			    					</tr>';
			// ProductSetting -EOF- 
			
			// BuyBack Competitor Price Setting
			$BuybackCompetitorHTML .= '<tr id="bb_competitor_game_server_id_'.$game_product_id.'" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'ordersListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\');">
											<td>
											<table border="0" width="100%" cellspacing="0" cellpadding="0">
											<tr style="'.$min_row_height.'">';
			
			$highest_price_without_full = $PriceAutomationObj->pa_highest_competitor_price($game_product_id, 'buyback', 1);
			$highest_price_with_full = $PriceAutomationObj->pa_highest_competitor_price($game_product_id, 'buyback');
			if (isset($PriceAutomationObj->pa_bb_competitors_price_array[$game_product_id])) {
				foreach ($PriceAutomationObj->pa_bb_competitors_price_array[$game_product_id] as $pa_competitors_price_key => $pa_competitors_price_value) {
					if ($pa_competitors_price_key != 'highest_price' && $pa_competitors_price_key != 'full_highest_price') {
						$buyback_price_color = 'color:black;';
						if ($highest_price_without_full == $pa_competitors_price_value['price']) {
							$buyback_price_color = 'color:green;font-weight:bold;';
						} elseif ($highest_price_with_full == $pa_competitors_price_value['price']) {
							$buyback_price_color = 'color:red;font-weight:bold;';
						}
						$BuybackCompetitorHTML .= '	<td id="bb_competitor_price_'.$game_product_id.'_'.$pa_competitors_price_key.'" class="buybackCompetitorColumn" width="'.$bcps_td_width.'" align="center" style="'.$buyback_price_color.'">'.$pa_competitors_price_value['price'].'</td>';
						$count_a = count($PriceAutomationObj->pa_bb_competitors_price_array[$game_product_id]);
						
						$Average_bb_price[$pa_competitors_price_key][] = $pa_competitors_price_value['price'];
					}
				}
			}
			
			$BuybackCompetitorHTML .= '			<td width="'.$bcps_td_mod.'">&nbsp;</td>
											</tr>
											</table>
											</td>
										</tr>';
			// BuyBack Competitor Price Setting EOF
			
			// BuyBack Price Setting
			$BuybackPriceHTML .= '	<tr id="bb_price_game_server_id_'.$game_product_id.'" width="450" align="center" style="'.$min_row_height.'" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'ordersListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\');">
								    	<td id="bb_suggested_price_text_'.$game_product_id.'" style="min-width:150px" class="ordersRecords" align="center">'. tep_draw_hidden_field('bb_suggested_price['.$game_product_id.']', '', 'id="bb_suggested_price_'.$game_product_id.'"') .'</td>
							    		<td id="bb_current_price_text_'.$game_product_id.'" style="min-width:150px" class="ordersRecords">'.$buyback_current_price_arr[$game_product_id].'</td>
								      	<td style="min-width:150px" class="ordersRecords">'.tep_draw_input_field('buyback_overwrite_price['.$game_product_id.']', $buyback_current_price_arr[$game_product_id], 'size=10 class="overwrite_buyback" id="overwrite_bb_price_'.$game_product_id.'" onchange="price_records(\'buyback\', this.id, this.value)" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }" ').'</td>
								    </tr>';
			
			// BuyBack Price Setting EOF
			
			// Back Order Setting
				$total_bo_days = 0;
				
				$BackOrderHTML .= '<tr id="backorder_game_server_id_'.$game_product_id.'" class="'.$row_style.'" style="'.$min_row_height.'" onmouseover="rowOverEffect(this, \'ordersListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\');">
										<td colspan="7">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
										<tr style="'.$min_row_height.'">';
				for ( $days_cnt = 0; $days_cnt < 7; $days_cnt++ ) {
					/*
						< 1 Day 		= "$days_cnt = 0"
						< 2 Day 		= "$days_cnt = 1"
						< 3 Day 		= "$days_cnt = 2"
						< 4 Day 		= "$days_cnt = 3"
						< 5 Day 		= "$days_cnt = 4"
						< 2 Weeks 		= "$days_cnt = 5"
						< 1 - 3 Months	= "$days_cnt = 6"
					*/
					$backorder_days = tep_not_null($PriceAutomationObj->backorder_days[$game_product_id][$days_cnt]) ? $PriceAutomationObj->backorder_days[$game_product_id][$days_cnt] : 0;
					$completed_delivered_qty = tep_not_null($PriceAutomationObj->completed_delivered_qty[$game_product_id][$days_cnt]) ? $PriceAutomationObj->completed_delivered_qty[$game_product_id][$days_cnt] : 0;
					
					$BackOrderHTML .= '	<td id="backorder_days_text_'.$game_product_id.'_'.$days_cnt.'" class="sellingCompetitorColumn" width="'.(100/7).'%" align="center">'.$backorder_days.'/'.$completed_delivered_qty.'</td>';
					$total_bo_days += $backorder_days;
				}	
					  	
				$BackOrderHTML .= '		<input type="hidden" name="backorder_amount['.$game_product_id.']" value="'.$total_bo_days.'" id="backorder_amount_'.$game_product_id.'" />
										</tr>
										</table>
										</td>
									</tr>';
				if (isset($PriceAutomationObj->backorder_oldest_order_day_arr[$game_product_id])) {
					$BackOrderHTML .= tep_draw_hidden_field('bo_oldest_order_day['.$game_product_id.']', $PriceAutomationObj->backorder_oldest_order_day_arr[$game_product_id], 'id="bo_oldest_order_day_'.$game_product_id.'"');
				}
				
			// Back Order Setting EOF
			
			// Selling Price Setting
			if ($selling_price_setting_permission) {
				$avg_current_selling_price_array[] = $PriceAutomationObj->current_selling_price[$game_product_id];
				
				$SellingPriceSettingRow .= '<tr id="selling_price_game_server_id_'.$game_product_id.'" class="'.$row_style.'" style="'.$min_row_height.'" onmouseover="rowOverEffect(this, \'ordersListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\');">
												<td>
												<table border="0" width="100%" cellspacing="0" cellpadding="0">
												<tr style="'.$min_row_height.'" id="selling_price_row_'.$game_product_id.'">';
				for ( $selling_price_setting_cnt = 1; $selling_price_setting_cnt <= 6; $selling_price_setting_cnt++ ) {	
					switch($selling_price_setting_cnt) {
						case 1:
							$SellingPriceSettingRow .= '<td class="sellingCompetitorColumn" width="21%" align="center">
														'.tep_draw_input_field('selling_price_margin['.$game_product_id.']', $PriceAutomationObj->pa_preferred_margin, 'id="selling_price_margin_'.$game_product_id.'" maxlength="3" size="2" class="selling_price_margin_class" onkeydown="if(event.keyCode == 13) {Update_Margin(\''.$game_product_id.'\', \''.$row_cnt.'\');}" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"').'
														%</td>';
							break;
						case 2:
							$SellingPriceSettingRow .= '<td class="sellingCompetitorColumn" width="20%" align="center">
														'.tep_draw_input_field('increment_val_arr['.$game_product_id.']', '', 'id="increment_val_'.$game_product_id.'" maxlength="6" size="5" class="increment_val" onkeydown="if(event.keyCode == 13) {blockUI_disable();calculate_price_row(\'\', \''.$game_product_id.'\');}"').'</td>';
							break;
						case 3:
							$SellingPriceSettingRow .= '<td class="sellingCompetitorColumn" width="16%" align="center">
														<div id="selling_suggested_price_text_'.$game_product_id.'">&nbsp;</div>
														</td>';
							$SellingPriceSettingRow .=  tep_draw_hidden_field('selling_suggested_price['.$game_product_id.']', '', 'id="selling_suggested_price_'.$game_product_id.'"');
							break;
						case 4:
							$SellingPriceSettingRow .= '<td id="selling_current_price_'.$game_product_id.'" class="sellingCompetitorColumn" width="14%" align="center">
														'.$PriceAutomationObj->current_selling_price[$game_product_id].'
														</td>';
							break;
						case 5:
							//$JSHTML .= "packages_products_id_arr['".$game_product_id."'] = new Array();";
							
							$SellingPriceSettingRow .= '<td class="sellingCompetitorColumn" width="17%" align="center">
														'.tep_draw_input_field('selling_overwrite_price['.$game_product_id.']', $PriceAutomationObj->current_selling_price[$game_product_id], 'id="selling_overwrite_price_'.$game_product_id.'" size="10" class="overwrite_selling_price" onkeydown="if(event.keyCode == 13) {blockUI_disable();calculate_price_row(\'\', \''.$game_product_id.'\');Update_Selling_OverWritePrice(\''.$game_product_id.'\', \''.$row_cnt.'\');Avg_Selling_Price();}" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"').'
														</td>';
							break;
						case 6:
							$SellingPriceSettingRow .= '<td class="sellingCompetitorColumn" width="12%" align="center">
														<div id="selling_competitors_rank_text_'.$game_product_id.'">&nbsp;</div>
														</td>';
							$SellingPriceSettingRow .=  tep_draw_hidden_field('selling_competitors_rank_value['.$game_product_id.']', '', 'id="selling_competitors_rank_value_'.$game_product_id.'"');
							break;
					}													
				}
				$SellingPriceSettingRow .= '      </tr>
												</table>
												</td>
											</tr>';
			}
			// Selling Price Setting EOF
			
			// Selling Competitor's Price Setting
				if(count($PriceAutomationObj->pa_selling_products_array) > 0) {
					$SellingCompetitorHTML .= '<tr id="selling_competitor_game_server_id_'.$game_product_id.'" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'ordersListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\');">
													<td>
													'.tep_draw_form('selling_price_competitors_form', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=selling_price_competitors_form', 'POST').'
													<table border="0" width="100%" cellspacing="0" cellpadding="0">
													<tr style="'.$min_row_height.'">';
					if (isset($PriceAutomationObj->pa_selling_products_array[$game_product_id])) {
						$highest_price_without_full = $PriceAutomationObj->pa_highest_competitor_price($game_product_id, 'selling', 1);
						$highest_price_with_full = $PriceAutomationObj->pa_highest_competitor_price($game_product_id, 'selling');
						if (isset($PriceAutomationObj->pa_selling_competitors_price_array[$game_product_id])) {
							foreach ($PriceAutomationObj->pa_selling_competitors_price_array[$game_product_id] as $pa_competitors_price_key => $pa_competitors_price_value) {
								if ($pa_competitors_price_key != 'highest_price' && $pa_competitors_price_key != 'full_highest_price') {
									$selling_price_color = 'color:black;';
									if ($highest_price_without_full == $pa_competitors_price_value['price']) {
										$selling_price_color = 'color:green;font-weight:bold;';
									} elseif ($highest_price_with_full == $pa_competitors_price_value['price']) {
										$selling_price_color = 'color:red;font-weight:bold;';
									}
									$SellingCompetitorHTML .= '	<td class="sellingCompetitorColumn" width="'.$scps_td_width.'" align="center" style="'.$selling_price_color.'">'
																.$pa_competitors_price_value['price']
																.tep_draw_hidden_field('selling_price_competitors_'.$game_product_id, $pa_competitors_price_value['price'], 'id="selling_price_competitors_'.$game_product_id.'_'.$pa_competitors_price_key.'"').
																'</td>';
																
									$Average_selling_price[$pa_competitors_price_key][] = $pa_competitors_price_value['price'];
								}
							} 
						} else {
							$SellingCompetitorHTML .= tep_draw_hidden_field('selling_price_competitors_'.$game_product_id, '', '');
						}
					} else {
						$SellingCompetitorHTML .= tep_draw_hidden_field('selling_price_competitors_'.$game_product_id, '', '');
					}    	
					$SellingCompetitorHTML .= '		<td width='.$scps_td_mod.'>&nbsp;</td>
													</tr>
													</table>
													</form>
													</td>
												</tr>';
				}
			// Selling Competitor's Price Setting EOF
		}
		
		// After For Each Looping
			// BuyBack Competitor Price Setting
			$BuybackCompetitorHTML .= '<tr>
										<td align="center">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
											<tr style="'.$min_row_height.'">';
			foreach ($Average_bb_price as $Average_bb_key => $Average_bb_value) {
				$BuybackAveragePrice = round($PriceAutomationObj->array_average($Average_bb_price[$Average_bb_key]), 6);
				$BuybackCompetitorHTML .= '	<td class="averageRow" width="'.$bcps_td_width.'" align="center">'.$BuybackAveragePrice.'</td>';
			}								
			$BuybackCompetitorHTML .= ' 	<td class="averageRow" width="'.$bcps_td_mod.'">&nbsp;</td>
											</tr>
										</table>
										</td>
									   </tr>';
			// BuyBack Competitor Price Setting EOF
			
			// BuyBack Price Setting				   
			$BuybackPriceHTML .= '<tr width="450" align="center" style="'.$min_row_height.'" class="averageRow">
										<td id="average_buyback_suggested_price">&nbsp;</td>
										<td>&nbsp;</td>
										<td id="average_buyback_overwrited_price">&nbsp;</td>
									  </tr>';
			// BuyBack Price Setting EOF
			
		//Clear Up Memory Usage
		unset($buyback_current_price_arr);
		//unset($PriceAutomationObj->pa_bb_products_array);
		unset($completed_delivered_qty);
		unset($PriceAutomationObj->completed_delivered_qty);
		unset($PriceAutomationObj->backorder_oldest_order_day_arr);
	} else {
		foreach ($PriceAutomationObj->products_arr as $game_product_id => $game_info) {
			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
			++$row_count;
			
			// Product Setting
			$ProductSettingHTML .= '<tr id="product_setting_game_server_id_'.$game_product_id.'" style="'.$min_row_height.'background:'.$row_color.';" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'ordersListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
			    						<td width="1" class="ordersRecords" align="center">';
			$ProductSettingHTML .= 	tep_draw_checkbox_field('game_product_id', $game_product_id, '', '', 'id="game_product_id_'.$game_product_id.'"');
		    $ProductSettingHTML .= 		'</td>
			      						<td class="ordersRecords">'.$game_info['game_name'].'</td>
			    					</tr>';
			// Product Setting EOF
			
			// BuyBack Price Setting
			$BuybackPriceHTML .= '	<tr id="bb_price_game_server_id_'.$game_product_id.'" width="450" align="center" style="'.$min_row_height.'" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'ordersListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\');">
								    	<td id="bb_suggested_price_text_'.$game_product_id.'" style="min-width:150px" class="ordersRecords" align="center">'. tep_draw_hidden_field('bb_suggested_price['.$game_product_id.']', '', 'id="bb_suggested_price_'.$game_product_id.'" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"') .'</td>
							    		<td id="bb_current_price_text_'.$game_product_id.'" style="min-width:150px" class="ordersRecords">'.$game_info['game_price'].'</td>
								      	<td style="min-width:150px" class="ordersRecords">'.tep_draw_input_field('buyback_overwrite_price['.$game_product_id.']', $game_info['game_price'], 'size=10 class="overwrite_buyback" id="overwrite_bb_price_'.$game_product_id.'" ').'</td>
								    </tr>';
			// BuyBack Price Setting EOF
		}
		// After For Each Looping
			// BuyBack Price Setting
			$BuybackPriceHTML .= '<tr width="450" align="center" style="'.$min_row_height.'" class="averageRow">
								<td id="average_buyback_suggested_price">&nbsp;</td>
								<td>&nbsp;</td>
								<td id="average_buyback_overwrited_price">&nbsp;</td>
							  </tr>';
			// BuyBack Price Setting EOF
	}
}
//}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
<link rel="stylesheet" type="text/css" href="includes/price_automation.css">
<script language="javascript" src="includes/general.js"></script>
<script language="javascript" src="includes/javascript/jquery.js"></script>
<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
<script language="javascript" src="includes/javascript/ajaxfileupload.js"></script>
<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
<script language="javascript" src="includes/javascript/php.packed.js"></script>
<script language="javascript" src="includes/javascript/jquery.form.js"></script>
<script language="javascript" src="includes/javascript/jquery.filestyle.js"></script>

<script type="text/javascript">
	// pre-submit callback 
	function showRequest(formData, jqForm, options) { 
	    // formData is an array; here we use $.param to convert it to a string to display it 
	    // but the form plugin does this for you automatically when it submits the data 
	    var queryString = jQuery.param(formData);
	    return true; 
	} 
	 
	// post-submit callback 
	function showResponse(responseText, statusText)  { 
	    // for normal html responses, the first argument to the success callback 
	    // is the XMLHttpRequest object's responseText property 
	 
	    // if the ajaxSubmit method was passed an Options Object with the dataType 
	    // property set to 'xml' then the first argument to the success callback 
	    // is the XMLHttpRequest object's responseXML property 
	 
	    // if the ajaxSubmit method was passed an Options Object with the dataType 
	    // property set to 'json' then the first argument to the success callback 
	    // is the json data object returned by the server 
	 //jQuery("#test").text(responseText);
	 alert('Congratulation - DONE');
	 jQuery.unblockUI();
	}
	
	function built_csv (type) {
		if (type == 'selling' || type == 'buyback') {
			jQuery("#csv_content").empty();
			
			var total_bb_competitors_heading = 0;
	    	var total_package_heading_cnt = 0;
	    	var total_selling_competitors_heading = 0;
	    	
	    	var csv_gap_num = 1;
			var csv_gap_string = ',';
			var csv_data = '';
			var csv_heading_package_qty_2nd_rows = '';
			var csv_heading_buyback_competitor_2nd_rows = '';
			var csv_heading_selling_competitor_2nd_rows = '';
			var csv_heading_bb_setting_2nd_rows = '';
			var csv_heading_bo_setting_2nd_rows = '';
			var csv_heading_selling_setting_2nd_rows = '';
			var csv_break_line = '\n';
			
			var game_product_id_arr = '';
			
	    	
			total_bb_competitors_heading = jQuery(".buyback_competitor_heading_column").length;
			if (total_bb_competitors_heading > 0) {
				jQuery(".buyback_competitor_heading_column").each (function() {
					csv_heading_buyback_competitor_2nd_rows += this.value + csv_gap_string;
				});
			}
			
			total_selling_competitors_heading = jQuery(".selling_competitor_heading_column").length;
			if (total_selling_competitors_heading > 0) {
				jQuery(".selling_competitor_heading_column").each (function() {
					csv_heading_selling_competitor_2nd_rows += this.value + csv_gap_string;
				});
			}
			
			//var check_selling_package_value = trim(jQuery("#package_table tr td").html());
			//if (check_selling_package_value != '') {
				jQuery(".package_heading_cnt").each (function() {
					csv_heading_package_qty_2nd_rows += this.value + csv_gap_string;
				});
				total_package_heading_cnt = jQuery(".package_heading_cnt").length;
			//}		
			
			//CSV HEADING xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
			//1st ROW xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
			var csv_heading_server_name = "<?=htmlspecialchars(CSV_HEADING_SERVER_NAME)?>";
			var csv_heading_bb_competitor = "<?=htmlspecialchars(CSV_HEADING_BUYBACK_COMPETITOR)?>";
			var csv_heading_bb_setting = "<?=htmlspecialchars(CSV_HEADING_BUYBACK_SETTING)?>";
			var csv_heading_aging_bo_n_completed = "<?=htmlspecialchars(CSV_HEADING_AGING_BO_AND_COMPLETED)?>";
			var csv_heading_selling_setting = "<?=htmlspecialchars(CSV_HEADING_SELLING_SETTING)?>";
			var csv_heading_selling_competitor = "<?=htmlspecialchars(CSV_HEADING_SELLING_COMPETITOR)?>";
			
			var csv_heading_server_name_size = 1; // fixed size
			var csv_heading_bb_competitor_size = total_bb_competitors_heading; // variable size
			var csv_heading_bb_setting_size = 3; // fixed size
			var csv_heading_aging_bo_n_completed_size = 7; // fixed size
			var csv_heading_selling_setting_size = 6; // fixed size
			var csv_heading_selling_package_size = total_package_heading_cnt; // variable size
			var csv_heading_selling_competitor_size = total_selling_competitors_heading; // variable size
			//var csv_heading_server_updated_size = 1; // fixed size
			
			csv_data += csv_heading_server_name + csv_gap_string;
			csv_data += csv_gap_string + csv_heading_bb_competitor + str_repeat(csv_gap_string, csv_heading_bb_competitor_size);
			csv_data += csv_gap_string + csv_heading_bb_setting + str_repeat(csv_gap_string, csv_heading_bb_setting_size);
			csv_data += csv_gap_string + csv_heading_aging_bo_n_completed + str_repeat(csv_gap_string, csv_heading_aging_bo_n_completed_size);
			csv_data += csv_gap_string + csv_heading_selling_setting + str_repeat(csv_gap_string, csv_heading_selling_setting_size);
			if (csv_heading_selling_package_size > 0) {
				csv_data += str_repeat(csv_gap_string, csv_heading_selling_package_size);
			}
			csv_data += csv_gap_string + csv_heading_selling_competitor + str_repeat(csv_gap_string, csv_heading_selling_competitor_size);
			//csv_data += csv_gap_string + csv_heading_server_updated + str_repeat(csv_gap_string, csv_heading_server_updated_size);
			//1st ROW EOF xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
			
			//2nd ROW xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
			csv_heading_bb_setting_2nd_rows += "<?=TAB_SUB_HEADING_BO_SUGGESTED_PRICE?>"  + csv_gap_string;
			csv_heading_bb_setting_2nd_rows += "<?=TAB_SUB_HEADING_BO_CURRENT_PRICE?>" + csv_gap_string;
			csv_heading_bb_setting_2nd_rows += "<?=TAB_SUB_HEADING_BO_OVERWRITE_PRICE?>" + csv_gap_string;
			
			csv_heading_bo_setting_2nd_rows += "1 <?=TAB_SUBTXT_DAY?>" + csv_gap_string;
			csv_heading_bo_setting_2nd_rows += "2 <?=TAB_SUBTXT_DAYS?>" + csv_gap_string;
			csv_heading_bo_setting_2nd_rows += "3 <?=TAB_SUBTXT_DAYS?>" + csv_gap_string;
			csv_heading_bo_setting_2nd_rows += "4 <?=TAB_SUBTXT_DAYS?>" + csv_gap_string;
			csv_heading_bo_setting_2nd_rows += "5 <?=TAB_SUBTXT_DAYS?>" + csv_gap_string;
			csv_heading_bo_setting_2nd_rows += "< 2 <?=TAB_SUBTXT_WEEKS?>" + csv_gap_string;
			csv_heading_bo_setting_2nd_rows += "< 1 - 3 <?=TAB_SUBTXT_MONTHS?>" + csv_gap_string;
			
			csv_heading_selling_setting_2nd_rows += "<?=TAB_SUB_HEADING_SELLING_MARKUP_PERCENTAGE?>" + csv_gap_string;
			csv_heading_selling_setting_2nd_rows += "<?=TAB_SUB_HEADING_SELLING_INCREMENT_VALUE?>" + csv_gap_string;
			csv_heading_selling_setting_2nd_rows += "<?=TAB_SUB_HEADING_SELLING_SUGGESTED_PRICE?>" + csv_gap_string;
			csv_heading_selling_setting_2nd_rows += "<?=TAB_SUB_HEADING_SELLING_CURRENT_PRICE?>" + csv_gap_string;
			csv_heading_selling_setting_2nd_rows += "<?=TAB_SUB_HEADING_BO_OVERWRITE_PRICE?>" + csv_gap_string;
			csv_heading_selling_setting_2nd_rows += "<?=TAB_SUB_HEADING_SELLING_RANK?>";
									      			
			csv_data += csv_break_line;
			csv_data += str_repeat(csv_gap_string, csv_heading_server_name_size) + csv_gap_string;
			csv_data += csv_heading_buyback_competitor_2nd_rows + csv_gap_string;
			csv_data += csv_heading_bb_setting_2nd_rows + csv_gap_string;
			csv_data += csv_heading_bo_setting_2nd_rows + csv_gap_string;
			csv_data += csv_heading_selling_setting_2nd_rows + csv_gap_string;
			csv_data += csv_heading_package_qty_2nd_rows + csv_gap_string;
			csv_data += csv_heading_selling_competitor_2nd_rows + csv_gap_string;
			//2nd ROW EOF xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
			//CSV HEADING EOFxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
			
			var product_cnt = 0;
			var store_in_arr_cnt = 0;
			//var split_product_id_arr = new Array();
			
			jQuery(".gameProductSelected").each (function() {
				if ((product_cnt % 100) == 0) {
					if (store_in_arr_cnt == 0) {
						game_product_id_arr = this.value;
					} else {
						game_product_id_arr += '|' + this.value;
					}
					store_in_arr_cnt ++;
				} else {
					game_product_id_arr += ',' + this.value;
				}
				
				//csv_data += csv_gap_string + csv_server_selected;
				
				product_cnt++;
			});
			
			//split_product_id_arr = game_product_id_arr.split('|');
			jQuery("#csv_content").append(csv_data);
			
			split_build_csv(type, game_product_id_arr);
		}
	}
	
	function split_build_csv (type, game_product_id_arr, split_product_id_cnt) {
		
		var product_id_arr = new Array();
		var split_product_id_arr = new Array();
		
		if (split_product_id_cnt == undefined || split_product_id_cnt == '') {
			var split_product_id = 0;
		} else {
			var split_product_id = split_product_id_cnt;
		}
		
		product_id_arr = game_product_id_arr.split('|');
		product_id_arr = product_id_arr[split_product_id];
		
		if (product_id_arr != undefined) {
			if (product_id_arr.length > eval(split_product_id + '+' + 1)) {
				var total_bb_competitors_heading = 0;
		    	var total_package_heading_cnt = 0;
		    	var total_selling_competitors_heading = 0;
		    	
		    	var csv_gap_num = 1;
				var csv_gap_string = ',';
				var csv_data = '';
				var csv_heading_package_qty_2nd_rows = '';
				var csv_heading_buyback_competitor_2nd_rows = '';
				var csv_heading_selling_competitor_2nd_rows = '';
				var csv_heading_bb_setting_2nd_rows = '';
				var csv_heading_bo_setting_2nd_rows = '';
				var csv_heading_selling_setting_2nd_rows = '';
				var csv_break_line = '\n';
				
				var competitor_id = '';
				var competitor_price = '';
				
				var game_product_id = '';
				var game_product_name = '';
				var game_suggested_price = '';
				var game_current_price = '';
				var game_overwrited_price = '';
				var backorder_text = '';
				
				var selling_price_margin = '';
				var selling_suggested_price = '';
				var selling_current_price = '';
				var selling_overwrite_price = '';
				var selling_competitors_rank = '';
				
				total_bb_competitors_heading = jQuery(".buyback_competitor_heading_column").length;
				total_selling_competitors_heading = jQuery(".selling_competitor_heading_column").length;
				total_package_heading_cnt = jQuery(".package_heading_cnt").length;

				product_id_arr = product_id_arr.split(',');
				for (var product_id_cnt = 0; product_id_cnt < product_id_arr.length; product_id_cnt++) {
					game_product_id = product_id_arr[product_id_cnt];
					game_product_name = jQuery("#game_product_name_"+game_product_id).val();

					var csv_bb_competitor_price = '';
					var csv_package_price = '';
					var csv_selling_competitor_price = '';
					var csv_server_selected = '0';
					
					if (total_bb_competitors_heading > 0) {
						for (var competitor_cnt=0; competitor_cnt < total_bb_competitors_heading; competitor_cnt++) {
							competitor_id = jQuery("#buyback_competitor_id_"+competitor_cnt).val();
							competitor_price = jQuery("#bb_competitor_price_"+game_product_id+"_"+competitor_id).text();
							
							csv_bb_competitor_price += trim(competitor_price) + csv_gap_string;
						}
					}
					
					game_suggested_price = jQuery("#bb_suggested_price_text_"+game_product_id).text();
					game_current_price = jQuery("#bb_current_price_text_"+game_product_id).text();
					game_overwrited_price = jQuery("#overwrite_bb_price_"+game_product_id).val();
					
					backorder_days_0 = jQuery("#backorder_days_text_" + game_product_id + "_0").text();
					backorder_days_1 = jQuery("#backorder_days_text_" + game_product_id + "_1").text();
					backorder_days_2 = jQuery("#backorder_days_text_" + game_product_id + "_2").text();
					backorder_days_3 = jQuery("#backorder_days_text_" + game_product_id + "_3").text();
					backorder_days_4 = jQuery("#backorder_days_text_" + game_product_id + "_4").text();
					backorder_days_5 = jQuery("#backorder_days_text_" + game_product_id + "_5").text();
					backorder_days_6 = jQuery("#backorder_days_text_" + game_product_id + "_6").text();
		
					selling_price_margin = jQuery("#selling_price_margin_"+game_product_id).val();
					increment_val = jQuery("#increment_val_"+game_product_id).val();
					selling_suggested_price = jQuery("#selling_suggested_price_text_"+game_product_id).text();
					selling_current_price = jQuery("#selling_current_price_"+game_product_id).text();
					selling_overwrite_price = jQuery("#selling_overwrite_price_"+game_product_id).val();
					selling_competitors_rank = jQuery("#selling_competitors_rank_text_"+game_product_id).text();
					
					if (total_package_heading_cnt > 0) {
						
						jQuery(".package_id_"+game_product_id).each (function() {
							var package_id = this.value;
							if (package_id != '') {
								var package_price = jQuery("#package_price_"+game_product_id+"_"+package_id).val();
							} else {
								var package_price = '';
							}
							
							csv_package_price += trim(package_price) + csv_gap_string;
						});
					}
					
					if (total_selling_competitors_heading > 0) {
						for (var competitor_cnt=0; competitor_cnt < total_selling_competitors_heading; competitor_cnt++) {
							competitor_id = jQuery("#selling_competitor_id_"+competitor_cnt).val();
							competitor_price = jQuery("#selling_price_competitors_"+game_product_id+"_"+competitor_id).val();
							
							csv_selling_competitor_price += trim(competitor_price) + csv_gap_string;
						}
					}
					
					csv_data += csv_break_line;
					csv_data += trim(game_product_name) + csv_gap_string;
					csv_data += csv_gap_string + csv_bb_competitor_price;
					csv_data += csv_gap_string + game_suggested_price;
					csv_data += csv_gap_string + game_current_price;
					csv_data += csv_gap_string + game_overwrited_price + csv_gap_string;
					
					csv_data += csv_gap_string + backorder_days_0;
					csv_data += csv_gap_string + backorder_days_1;
					csv_data += csv_gap_string + backorder_days_2;
					csv_data += csv_gap_string + backorder_days_3;
					csv_data += csv_gap_string + backorder_days_4;
					csv_data += csv_gap_string + backorder_days_5;
					csv_data += csv_gap_string + backorder_days_6 + csv_gap_string;
					
					csv_data += csv_gap_string + trim(selling_price_margin);
					csv_data += csv_gap_string + trim(increment_val);
					csv_data += csv_gap_string + trim(selling_suggested_price);
					csv_data += csv_gap_string + trim(selling_current_price);
					csv_data += csv_gap_string + trim(selling_overwrite_price);
					csv_data += csv_gap_string + trim(selling_competitors_rank) + csv_gap_string;
					
					if (csv_package_price != '') {
						csv_data += csv_package_price;
					} else {
						csv_data += str_repeat(csv_gap_string, total_package_heading_cnt);
					}
					
					csv_data += csv_gap_string + csv_selling_competitor_price;
				}
				
				jQuery("#csv_content").append(csv_data);
				split_product_id_cnt = eval(split_product_id + '+' + 1);
				var t = setTimeout("split_build_csv('"+type+"', '"+game_product_id_arr+"', '"+split_product_id_cnt+"');",1000);
			}
		} else {
			var csv_content = jQuery("#csv_content").text();
			jQuery.post("price_automation_xmlhttp.php?cat_id=<?=$main_cat_id?>&type="+type+"&action=history", {csv_content: csv_content},function(data){
				alert('Congratulation - DONE');
				jQuery.unblockUI();
			});
		}
	}
	
	function csv_string_replace (content) {
		//content = str_replace("'", "", content);
		return content;
	}
	
	jQuery(document).ready(function() {
		jQuery("input[type=file]").filestyle({ 
		    image: "<?=tep_href_link(DIR_WS_IMAGES.'browse.gif')?>",
		    imageheight : 22,
		    imagewidth : 82,
		    width : 180
		});
		
		jQuery("input").attr({ onkeypress: "return noEnterKey(event)" });
		
		var selling_options = { 
		   target: '',
		   beforeSubmit:  '',  // pre-submit callback 
    	   success:       showResponse,  // post-submit callback 
	       url: 'price_automation_xmlhttp.php?cat_id=<?=$main_cat_id?>&type=selling&action=update_current_price'
	    };
	    
	    var buyback_options = { 
		   target: '',
		   beforeSubmit:  '',  // pre-submit callback 
    	   success:       showResponse,  // post-submit callback 
	       url: 'price_automation_xmlhttp.php?cat_id=<?=$main_cat_id?>&type=buyback&action=update_current_price'
	    };
	    
	    var buyback_html_options = { 
		   target: '',
		   beforeSubmit:  '',  // pre-submit callback 
    	   success:       showResponse,  // post-submit callback 
    	   url: 'price_automation_xmlhttp.php?cat_id=<?=$main_cat_id?>&type=buyback&action=history'
	    };
	    
	    var selling_html_options = { 
		   target: '',
		   beforeSubmit:  '',  // pre-submit callback 
    	   success:       showResponse,  // post-submit callback 
    	   url: 'price_automation_xmlhttp.php?cat_id=<?=$main_cat_id?>&type=selling&action=history'
	    };
	    

        jQuery('#selling_price_form').submit(function() { 
        	blockUI_disable();
        	var game_product_id = '';
        	
        	
        	var total_selected = jQuery(".gameProductSelected").length;
			
			if (total_selected > 0) {
				
				//built_csv('selling');
					
				jQuery(".gameProductSelected").each (function() {
					game_product_id += this.value + ',';
					var overwrite_price = jQuery("#selling_overwrite_price_"+this.value).val();
					jQuery("#selling_current_price_"+this.value).text(overwrite_price);
				});
				
				jQuery("#selling_hidden_pid_content").val(game_product_id);
				//jQuery("#selling_hidden_html_content").val(file_content);
		        jQuery(this).ajaxSubmit(selling_options);
		        
		        //var file_content = jQuery("#store_record").html();
		        //jQuery("#hidden_html_content").val(file_content);
		        //jQuery('#history_html').ajaxSubmit(selling_html_options);
			} else {
				alert('<?=JS_MSG_PLS_SELECT_THE_CHECKBOX?>');
				jQuery.unblockUI();
			}
	        return false; 
	    });
	    
	    jQuery('#buyback_price_form').submit(function() { 
        	blockUI_disable();
        	var game_product_id = '';
        	
			var total_selected = jQuery(".gameProductSelected").length;
			
			if (total_selected > 0) {
				
				//built_csv('buyback');
				
				jQuery(".gameProductSelected").each (function() {
					game_product_id += this.value + ',';
					var overwrite_price = jQuery("#overwrite_bb_price_"+this.value).val();
					jQuery("#bb_current_price_text_"+this.value).text(overwrite_price);
				});
				
				jQuery("#bb_hidden_pid_content").val(game_product_id);
				//jQuery("#bb_hidden_html_content").val(file_content);
		        jQuery(this).ajaxSubmit(buyback_options);
		        //jQuery("#csv_content_form").ajaxSubmit(csv_buyback_options);
		        //var file_content = jQuery("#store_record").html();
		        //jQuery("#hidden_html_content").val(file_content);
		        //jQuery('#history_html').ajaxSubmit(buyback_html_options);
			} else {
				alert('<?=JS_MSG_PLS_SELECT_THE_CHECKBOX?>');
				jQuery.unblockUI();
			}
	        return false;
	    });
		
		jQuery("#export_import-tab > ul").tabs();
		jQuery("#setting-tab > ul").tabs();
		jQuery("#buyback_competitor_price_setting-tab > ul").tabs();
		jQuery("#buyback_price_setting-tab > ul").tabs();
		jQuery("#backorder_setting-tab > ul").tabs();
		jQuery("#selling_price_setting-tab > ul").tabs();
		jQuery("#selling_competitor_setting-tab > ul").tabs();
		jQuery("#hide-server-tab > ul").tabs();
		jQuery("#batch-update-tab > ul").tabs();
		
		jQuery.fn.clearForm = function() {
		  return this.each(function() {
			var type = this.type, tag = this.tagName.toLowerCase();
			if (tag == 'form')
			  return jQuery(':input',this).clearForm();
			if (type == 'text' || type == 'password' || tag == 'textarea')
			  this.value = '';
			else if (type == 'checkbox' || type == 'radio')
			  this.checked = false;
			else if (tag == 'select')
			  this.selectedIndex = -1;
		  });
		};
				
		jQuery('a#buyback_competitor_price_setting_tab').click(function() {
			jQuery("#buyback_competitor_price_setting_toggle").toggle();
			jQuery("#buyback_competitor_price_setting_tab").toggleClass("toggleTabSelected");
			return false;
		})
		
		jQuery('a#buyback_competitor_price_setting_sub_tab').click(function() {
			jQuery("#buyback_competitor_price_setting_toggle").toggle();
			jQuery("#buyback_competitor_price_setting_tab").toggleClass("toggleTabSelected");
			return false;
		});
		
		jQuery('a#buyback_price_setting_tab').click(function() {
			jQuery("#buyback_price_setting_toggle").toggle();
			jQuery("#buyback_price_setting_tab").toggleClass("toggleTabSelected");
			return false;
		});
		
		jQuery('a#buyback_price_setting_sub_tab').click(function() {
			jQuery("#buyback_price_setting_toggle").toggle();
			jQuery("#buyback_price_setting_tab").toggleClass("toggleTabSelected");
			return false;
		});
		
		jQuery('a#backorder_setting_tab').click(function() {
			jQuery("#backorder_setting_toggle").toggle();
			jQuery("#backorder_setting_tab").toggleClass("toggleTabSelected");
			return false;
		});
		
		jQuery('a#backorder_setting_sub_tab').click(function() {
			jQuery("#backorder_setting_toggle").toggle();
			jQuery("#backorder_setting_tab").toggleClass("toggleTabSelected");
			return false;
		});
		
		jQuery('a#selling_price_setting_tab').click(function() {
			jQuery("#selling_price_setting_toggle").toggle();
			jQuery("#selling_price_setting_tab").toggleClass("toggleTabSelected");
			return false;
		});
		
		jQuery('a#selling_price_setting_sub_tab').click(function() {
			jQuery("#selling_price_setting_toggle").toggle();
			jQuery("#selling_price_setting_tab").toggleClass("toggleTabSelected");
			return false;
		});
		
		jQuery('a#selling_competitor_setting_tab').click(function() {
			jQuery("#selling_competitor_setting_toggle").toggle();
			jQuery("#selling_competitor_setting_tab").toggleClass("toggleTabSelected");
			return false;
		});
		
		jQuery('a#selling_competitor_setting_sub_tab').click(function() {
			jQuery("#selling_competitor_setting_toggle").toggle();
			jQuery("#selling_competitor_setting_tab").toggleClass("toggleTabSelected");
			return false;
		});
		
		jQuery('a#view_selling_competitor_details').click(function() {
			jQuery("#selling_competitor_setting_toggle").toggle();
			return false;
		});
	})
	
	function ajaxFileUpload (file_id, loading_id, format)
	{
		var row_count = 0;
		var row_style = '';
		var content_HTML = '';
		var Avg_suggested_price = 0;
		var Avg_overwrited_price = 0;
		
		if (format == '') {
			format = 1;
		}
		
		jQuery("#"+loading_id)
		.ajaxStart(function(){
			jQuery(this).show();
		})
		.ajaxComplete(function(){
			jQuery(this).hide();
		});
		
		jQuery.ajaxFileUpload
		(
			{
				url:'price_automation_xmlhttp.php?action=ajax_upload&file_name='+file_id,
				secureuri:false,
				fileElementId:file_id,
				dataType: 'xml',
				success: function(xml){
			     	jQuery(xml).find('buyback_price_info').each(function(){
			     		rank = 0;
			     		row_style = eval(row_count + '%2') ? 'ordersListingEven' : 'ordersListingOdd' ;
			     		
						var game_product_id = jQuery("game_product_id", this).text();
						var suggested_price = jQuery("suggested_price", this).text();
				        var current_price = jQuery("current_price", this).text();
				        var overwrited_price = jQuery("overwrited_price", this).text();
				        
				        if (suggested_price != '') {
				        	Avg_suggested_price = eval(Avg_suggested_price + '+' + suggested_price);
				    	}
				    	
				    	if (overwrited_price != '') {
				        	Avg_overwrited_price = eval(Avg_overwrited_price + '+' + overwrited_price);
				    	}
				    	
				        <?php if(count($PriceAutomationObj->pa_selling_products_array) > 0) { ?>
				        rank = Get_Selling_Rank(game_product_id, row_count, overwrited_price);
				    	<?php } else { ?>
				    	rank = 0;	
				    	<?php }?>
				        
				        switch (format) {
				        	case '1':
				        			jQuery("#bb_suggested_price_"+game_product_id).val(suggested_price);
				        			jQuery("#bb_suggested_price_text_"+game_product_id).text(suggested_price);
				        			jQuery("#overwrite_bb_price_"+game_product_id).val(overwrited_price);
							break;
							
							case '2':
								jQuery("#selling_suggested_price_"+game_product_id).val(suggested_price);
				        		jQuery("#selling_suggested_price_text_"+game_product_id).text(suggested_price);
				        		jQuery("#selling_overwrite_price_"+game_product_id).val(overwrited_price);
				        		jQuery("#selling_competitors_rank_text_"+game_product_id).text(rank);
				        		jQuery("#selling_competitors_rank_value_"+game_product_id).val(rank);
							break;
						}
						++row_count;
			     	 });
			     	 
			     	 Avg_suggested_price = Math.round(eval(Avg_suggested_price + '/' + row_count)*1000000)/1000000;
			     	 Avg_overwrited_price = Math.round(eval(Avg_overwrited_price + '/' + row_count)*1000000)/1000000;
			     	 
			     	 switch (format) {
			        	case '1':
							jQuery("#average_buyback_suggested_price").text(Avg_suggested_price);
			     	 		jQuery("#average_buyback_overwrited_price").text(Avg_overwrited_price);
						break;
						
						case '2':
							jQuery("#avg_selling_suggested_price").text(Avg_suggested_price);
			     	 		jQuery("#avg_selling_overwrite_price").text(Avg_overwrited_price);
			     	 		<?php if(count($PriceAutomationObj->pa_selling_products_array) > 0) { ?>
					        	Avg_Selling_Rank();
					    	<?php }?>
						break;
					} 
			  }
			}
		)
		return false;
	}
</script>
</head>

<div id="store_record2">

<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table width="900"  border="0" cellspacing="0" cellpadding="3">
					<tr>
						<td class="pageHeading"><?=HEADING_TITLE?></td>
					</tr>
				</table>
				<?=tep_draw_form('game_selection', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . '', 'GET')?>
				<table width="100%"  border="0" cellspacing="0" cellpadding="3">
					<tr>
						<td class="main">
							<b><?=TABLE_HEADING_MAIN_CATEGORY?></b>&nbsp;<?=tep_draw_pull_down_menu('game_cat_id', $game_cat_id_arr, $main_cat_id) . '&nbsp;&nbsp;'?>
						<?=tep_submit_button("Select", "Select", ' onClick="return submitDrowpDownForm(this);"', 'inputButton')?></td>
					</tr>
				</table>
				</form>
<?php if ($main_cat_id > 0) { ?>
				<table width="100%"  border="0" cellspacing="0" cellpadding="0">
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      			</table>
<!-- Alert Setting //-->
				<?=tep_draw_form('alert_setting', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . "action=alert_setting", 'POST')?>	
				<table width="900"  border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td>
						<div class="tabTitle" onclick="tongle_description('alert_setting');">
							<?=tep_image(tep_href_link(DIR_WS_ICONS.'expand.gif'), '', '', '', 'id="alert_setting_icon"')?> <?=TAB_HEADING_ALERT_SETTING?>
						</div>
						<div class="tabContent" id="alert_setting">
							<div style="font-size:12px;">
								<table border="0" width="280">
<?php
		    //-----Start Row of BackOrder Days Alert Colour---------------------------------------------------
			$bo_days_colourHTML = '<tr>
									<td class="dataTableContent" colspan="3">
				                   		<table cellpadding="3" cellspacing="0" border="0" id="alert_setting_backorder">
				                			<tr valign="top">
				                				<td class="ordersBoxHeading" nowrap><div style="padding:3px 3px 2px 3px;">Bo Days</div><div style="padding:3px 3px 2px 3px;">Row Colour</div></td>';
			$bo_days_cnt = 0;
			$bo_days_color = "#FFFFCC";
			
			if (tep_not_null($bo_days_colour_arr)) {
				foreach ($bo_days_colour_arr as $as_bo_days => $bo_days_colour_code) {
					$bo_days_colourHTML .= tep_draw_hidden_field("as_bo_days_prev[$bo_days_cnt]", "$as_bo_days").tep_draw_hidden_field("as_backorder_days", "$bo_days_colour_code", 'id="as_backorder_days_'.$bo_days_cnt.'"');
					$bo_days_colourHTML .= ' 		<td class="dataTableContent" bgcolor="'.$bo_days_color.'">'.tep_draw_input_field("as_bo_days[$bo_days_cnt]", "$as_bo_days", 'size="5" onKeyUP="if (trim(this.value) != \'\' && !validateInteger(trim(this.value))) { this.value = \'\'; }"').'
														<br>'.tep_draw_colour_picker($bo_days_cnt, $bo_days_colour_code, 'bo_days', $colour_picker_arr).'
														<br>'.tep_draw_checkbox_field("bo_days_delete_colour_setting[$bo_days_cnt]", '1').' ' . DELETE . '
													</td>';
					$bo_days_color = (($bo_days_cnt % 2)==0) ? "#D7D5D0" : "#FFFFCC";
					
					$PriceAutomationObj->alert_setting_backorder_days_arr[] = array("bo_days" => $as_bo_days, "bo_color" => $bo_days_colour_code);
					$bo_days_cnt++;
				}
			}
			
			$bo_days_colourHTML .= ' 			<td class="dataTableContent" bgcolor="'.$bo_days_color.'">'.tep_draw_input_field("as_bo_days[$bo_days_cnt]", '', 'size="5" onKeyUP="if (trim(this.value) != \'\' && !validateInteger(trim(this.value))) { this.value = \'\'; }"').'<br>'.tep_draw_colour_picker($bo_days_cnt, '', 'bo_days', $colour_picker_arr).'</td>';
			$bo_days_colourHTML .= ' 		</tr>
								   		</table>
								  	</td>
								  	<td class="dataTableContent">'.tep_submit_button('Update', '', 'name="bo_days_alert_color"', 'InputButton').'</td>
								 </tr>';
?>
								<?=$bo_days_colourHTML?>
								<? unset($bo_days_colourHTML) ?>
								</table>
								<table border="0" width="280">
<?php
			//-----Start Row of Margin Alert Colour---------------------------------------------------
			$margin_colourtHTML = '<tr>
									<td class="dataTableContent" colspan="3">
				                   		<table cellpadding="3" cellspacing="0" border="0" id="alert_setting_margin">
				                			<tr valign="top">
				                				<td class="ordersBoxHeading" nowrap><div style="padding:3px 3px 2px 3px;">Margin</div><div style="padding:3px 3px 2px 3px;">Row Colour</div></td>';
			$margin_cnt = 0;
			$margin_color = "#FFFFCC";
			
			if (tep_not_null($margin_colour_arr)) {
				foreach ($margin_colour_arr as $as_margin => $margin_colour_code) {
					$margin_colourtHTML .= tep_draw_hidden_field("as_margin_prev[$margin_cnt]", "$as_margin", 'id="as_margin_prev_'.$margin_cnt.'"').tep_draw_hidden_field("alert_color_setting_margin", "$margin_colour_code", 'id="alert_color_setting_margin_'.$margin_cnt.'"');
					$margin_colourtHTML .= ' 		<td class="dataTableContent" bgcolor="'.$margin_color.'">'.tep_draw_input_field("as_margin[$margin_cnt]", "$as_margin", 'size="5" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"').'&#37;
														<br>'.tep_draw_colour_picker($margin_cnt, $margin_colour_code, 'margin', $colour_picker_arr).'
														<br>'.tep_draw_checkbox_field("margin_delete_colour_setting[$margin_cnt]", '1').' ' . DELETE . '
													</td>';
					$margin_color = (($margin_cnt % 2)==0) ? "#D7D5D0" : "#FFFFCC";
					$margin_cnt++;
				}
			}
			
			$margin_colourtHTML .= ' 			<td class="dataTableContent" bgcolor="'.$margin_color.'">'.tep_draw_input_field("as_margin[$margin_cnt]", '', 'size="5"').'&#37;<br>'.tep_draw_colour_picker($margin_cnt, '', 'margin', $colour_picker_arr).'</td>';
			$margin_colourtHTML .= ' 		</tr>
								   		</table>
								  	</td>
								  	<td class="dataTableContent">'.tep_submit_button(BTN_UPDATE, '', 'name="margin_alert_color"', 'InputButton').'</td>
								 </tr>';
?>
								<?=$margin_colourtHTML?>
								<? unset($margin_colourtHTML)?>
								</table>
							</div>
						</div>
					</td>
				</tr>
				</table>
				</form>
				<table width="100%"  border="0" cellspacing="0" cellpadding="0">
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      			</table>
<!-- Hide Server Setting //-->
      			<table width="900"  border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td>
					<div class="tabTitle" onclick="tongle_description('hide_setting');">
						<?=tep_image(tep_href_link(DIR_WS_ICONS.'expand.gif'), '', '', '', 'id="hide_setting_icon"')?> <?=TAB_HEADING_HIDE_SERVER_SETTING?>
					</div>
					<div class="tabContent" id="hide_setting">
						<div style="font-size:12px;">
							<?=tep_draw_form('hide_server', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . "action=hide_server", 'POST')?>
							<table border="0" width="500" height="200">
								<tr>
									<td valign="top">
									<div id="hide-server-tab">
								        <ul>
								           <li><a href="#hs_bo_days"><span><?=TAB_SUB_HEADING_BO_DAYS?></span></a></li>
								           <li><a href="#hs_bo_amount"><span><?=TAB_SUB_HEADING_BO_AMOUNT?></span></a></li>
								           <li><a href="#hs_margin"><span><?=TAB_SUB_HEADING_BO_MARGIN?> %</span></a></li>
								           <li><a href="#hs_selling_rank"><span><?=TAB_SUB_HEADING_BO_SELLING_RANK?></span></a></li>
								        </ul>
								        <div id="hs_bo_days">
								        	<table border="0" cellpadding="7">
											<tr align="center">
												<td width="80" class="boldText"><?=BO_HIDE_IF?></td>
												<td width="1">></td>
												<td width="80"><?=tep_draw_input_field('bo_days_greater', '', 'size="5" maxlength="5" id="bo_days_greater" onKeyUP="if (trim(this.value) != \'\' && !validateInteger(trim(this.value))) { this.value = \'\'; }"');?></td>
												<td width="80"><?=tep_draw_checkbox_field('bo_days_greater_checkbox', '', '', '', 'id="bo_days_greater_checkbox"');?></td>
											</tr>
											<tr align="center">
												<td width="80" class="boldText"><?=BO_HIDE_IF?></td>
												<td width="1"><</td>
												<td width="80"><?=tep_draw_input_field('bo_days_lesser', '', 'size="5" maxlength="5" id="bo_days_lesser" onKeyUP="if (trim(this.value) != \'\' && !validateInteger(trim(this.value))) { this.value = \'\'; }"');?></td>
												<td width="80"><?=tep_draw_checkbox_field('bo_days_lesser_checkbox', '', '', '', 'id="bo_days_lesser_checkbox"');?></td>
											</tr>
											</table>
									     	<?=tep_button(BO_HIDE_NOW, '', '', 'onClick="HideServer(\'bo_days\')"', 'InputButton');?>
									    </div>
									    <div id="hs_bo_amount">
									    	<table border="0" cellpadding="7">
									    	<tr align="center">
												<td width="80" class="boldText"><?=BO_HIDE_IF?></td>
												<td width="1">></td>
												<td width="80"><?=tep_draw_input_field('bo_amount_greater', '', 'size="5" maxlength="5" id="bo_amount_greater" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"');?></td>
												<td width="80"><?=tep_draw_checkbox_field('bo_amount_greater_checkbox', '', '', '', 'id="bo_amount_greater_checkbox"');?></td>
											</tr>
											<tr align="center">
												<td width="80" class="boldText"><?=BO_HIDE_IF?></td>
												<td width="1"><</td>
												<td width="80"><?=tep_draw_input_field('bo_amount_lesser', '', 'size="5" maxlength="5" id="bo_amount_lesser" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"');?></td>
												<td width="80"><?=tep_draw_checkbox_field('bo_amount_lesser_checkbox', '', '', '', 'id="bo_amount_lesser_checkbox"');?></td>
											</tr>
											</table>
									     	<?=tep_button(BO_HIDE_NOW, '', '', 'onClick="HideServer(\'bo_amount\')"', 'InputButton');?>
									    </div>
									    <div id="hs_margin">
									    	<table border="0" cellpadding="7">
									    	<tr align="center">
												<td width="80" class="boldText"><?=BO_HIDE_IF?></td>
												<td width="1">></td>
												<td width="80"><?=tep_draw_input_field('margin_greater', '', 'size="5" maxlength="5" id="margin_greater" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"');?>%</td>
												<td width="80"><?=tep_draw_checkbox_field('margin_greater_checkbox', '', '', '', 'id="margin_greater_checkbox"');?></td>
											</tr>
											<tr align="center">
												<td width="80" class="boldText"><?=BO_HIDE_IF?></td>
												<td width="1"><</td>
												<td width="80"><?=tep_draw_input_field('margin_lesser', '', 'size="5" maxlength="5" id="margin_lesser" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"');?>%</td>
												<td width="80"><?=tep_draw_checkbox_field('margin_lesser_checkbox', '', '', '', 'id="margin_lesser_checkbox"');?></td>
											</tr>
											</table>
									     	<?=tep_button(BO_HIDE_NOW, '', '', 'onClick="HideServer(\'margin\')"', 'InputButton');?>
									    </div>
									    <div id="hs_selling_rank">
									    	<table border="0" cellpadding="7">
									    	<tr align="center">
												<td width="80" class="boldText"><?=BO_HIDE_IF?></td>
												<td width="1">></td>
												<td width="80"><?=tep_draw_input_field('selling_rank_greater','','size="5" maxlength="3" id="selling_rank_greater" onKeyUP="if (trim(this.value) != \'\' && !validateInteger(trim(this.value))) { this.value = \'\'; }"')?></td>
												<td width="80"><?=tep_draw_checkbox_field('selling_rank_greater_checkbox', '', '', '', 'id="selling_rank_greater_checkbox"');?></td>
											</tr>
											<tr align="center">
												<td width="80" class="boldText"><?=BO_HIDE_IF?></td>
												<td width="1"><</td>
												<td width="80"><?=tep_draw_input_field('selling_rank_lesser','','size="5" maxlength="3" id="selling_rank_lesser" onKeyUP="if (trim(this.value) != \'\' && !validateInteger(trim(this.value))) { this.value = \'\'; }"')?></td>
												<td width="80"><?=tep_draw_checkbox_field('selling_rank_lesser_checkbox', '', '', '', 'id="selling_rank_lesser_checkbox"');?></td>
											</tr>
											</table>
									     	<?=tep_button(BO_HIDE_NOW, '', '', 'onClick="HideServer(\'selling_rank\')"', 'InputButton');?>
									    </div>
								     </div>
									</td>
								</tr>
							</table>
							</form>
						</div>
					</div>
					</td>
				</tr>
				</table>
				
				<table width="100%"  border="0" cellspacing="0" cellpadding="0">
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      			</table>
<!-- Batch Update Setting //-->

      			<table width="900"  border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td>
					<div class="tabTitle" onclick="tongle_description('batch_update_setting');">
						<?=tep_image(tep_href_link(DIR_WS_ICONS.'expand.gif'), '', '', '', 'id="batch_update_setting_icon"')?> <?=TAB_HEADING_BATCH_UPDATE_SETTING?>
					</div>
					<div class="tabContent" id="batch_update_setting">
						<div style="font-size:12px;">
							<?=tep_draw_form('batch_update_setting', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . "action=batch_update_setting", 'POST')?>
							<table border="0" width="100%" height="70">
								<tr>
									<td valign="top">
									<div id="batch-update-tab">
										<table border="0" width="100%" height="70" valign="top" style="font-size:12px;">
											<tr valign="top">
												<td width="130" class="padding_15">
												<?=ENTRY_PACKAGE_QUANTITY?>
												<?=tep_draw_input_field('package_quantities', $cat_cfg_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE']!='all' ? $cat_cfg_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'] : '', ' size="40" id="package_quantities"') . '&nbsp;' . TEXT_MULTI_QTY_ENTRIES?>
												</td>
											</tr>
											<tr>
												<td>
													<?=tep_button('Save Settings', 'Save Settings', '', 'onClick="savePackageQuantitySetting(\''.$main_cat_id.'\');" ', 'inputButton')?>
												</td>
											</tr>
										</table>
								        
								     </div>
									</td>
								</tr>
							</table>
							</form>
						</div>
					</div>
					</td>
				</tr>
				</table>
				
				<table width="100%"  border="0" cellspacing="0" cellpadding="0">
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      			</table>
<!-- History Setting //-->
      			<table width="900"  border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td>
					<div class="tabTitle" onclick="tongle_description('history');">
						<?=tep_image(tep_href_link(DIR_WS_ICONS.'expand.gif'), '', '', '', 'id="history_icon"')?> <?=TAB_HEADING_BATCH_HISTORY?>
					</div>
					<div class="tabContent" id="history">
						<div style="font-size:12px;">
							<?=tep_draw_form('history', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . "action=history", 'POST')?>
							<table border="0" width="100%">
								<tr>
									<td colspan="10">
										<div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div>
									</td>
								</tr>
								<tr>
									<td colspan="10">
										<?=tep_draw_radio_field('history_format', 'csv', true, '', 'id="history_csv_format"')?>CSV
										<?=tep_draw_radio_field('history_format', 'html', '', '', 'id="history_html_format"')?>HTML
									</td>
								</tr>
								<tr>
									<td class="tabsTd" width="150" nowrap valign="top">
										Date : <?=tep_draw_input_field('history_date', '', 'id="history_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.history.history_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.history.history_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="'.tep_href_link("includes/javascript/PopCalendarXp/calbtn.gif").'" width="34" height="22" border="0" alt=""></a>'?>
										<br/><br/>
										<?=tep_button(HISTORY_SHOW, '', '', 'onClick="ShowHistoryFile();"', 'InputButton');?>
									</td>
								</tr>
								<tr>
									<td class="tabsTd" width="333" align="left" nowrap valign="top">
										<div id="display_history_files"></div>
									</td>
								</tr>
							</table>
							</form>
						</div>
					</div>
					</td>
				</tr>
				</table>
				<table width="100%"  border="0" cellspacing="0" cellpadding="0">
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      			</table>
<!-- Import / Export  //-->
      			<table width="800"  border="0" cellspacing="0" cellpadding="0">
					<tr>
        				<td>
      					<div id="export_import-tab">
					        <ul>
					           <li><a href="#tab-export"><span>Export</span></a></li>
					           <li><a href="#tab-import"><span>Import</span></a></li>
					        </ul>
					     </div>
					     <div id="tab-export" style="border-style: solid; border-color: 97a5b0; border-width: 1px;">
					     	<?=tep_draw_form('export', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . "action=csv_export", 'POST', 'enctype="multipart/form-data"')?>
							<table border="0" cellspacing="0" cellpadding="2">
							  	<tr>
							      	<td><?=tep_submit_button(BTN_EXPORT_CSV_TEMPLATE, '', 'name="btn_csv_export"', 'inputButton');?></td>
							    </tr>
							</table>
							</form>
						</div>
						<div id="tab-import" style="border-style: solid; border-color: 97a5b0; border-width: 1px;">
							<?=tep_draw_form('import', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . "action=csv_import", 'POST', 'enctype="multipart/form-data"')?>
							<?=tep_draw_hidden_field('import_action', '');?>
							<table border="0" cellspacing="0" cellpadding="2" class="main">
							  	<tr>
							  		<td><?=ENTRY_BUYBACK?></td>
							  		<td>:</td>
							      	<td><input type="file" name="import_buyback" /></td>
							    </tr>
							    <tr>
							    	<td><?=ENTRY_SELLING?></td>
							    	<td>:</td>
							      	<td><?=tep_draw_file_field('import_selling');?></td>
							    </tr>
							    <tr>
							    	<td colspan="3"><?=tep_button(BTN_IMPORT, '', '','name="btn_csv_import" onClick=\'import_checking();\'', 'inputButton');?></td>
							    </tr>
							</table>
							</form>
						</div>
						</td>
      				</tr>
      			</table>
      			<table width="100%"  border="0" cellspacing="0" cellpadding="0">
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      			</table>
<!-- Product Setting //-->
<div id="store_record3">
      			<table width="100%"  border="0" cellspacing="0" cellpadding="0">
					<tr>
        				<td valign="top">
      					<div id="setting-tab">
					        <ul>
					           <li id="tab_0"><a href="#"><span><?=TAB_PRODUCT_SETTING?></span></a></li>
					           <li id="tab_1"><a href="#buyback_competitor_price_setting" id="buyback_competitor_price_setting_tab"><span><?=TAB_BUYBACK_COMPETITOR_PRICE_SETTING?></span></a></li>
					           <li id="tab_2"><a href="#buyback_price_setting" id="buyback_price_setting_tab"><span><?=TAB_BUYBACK_PRICE_SETTING?></span></a></li>
					           <li id="tab_3"><a href="#backorder_setting" id="backorder_setting_tab"><span><?=TAB_BACKORDER_SETTING?></span></a></li>
					           <li id="tab_4"><a href="#selling_price_setting" id="selling_price_setting_tab"><span><?=TAB_SELLING_PRICE_SETTING?></span></a></li>
					           <li id="tab_5"><a href="#selling_competitor_setting" id="selling_competitor_setting_tab"><span><?=TAB_SELLING_COMPETITOR_PRICE_SETTING?></span></a></li>
					        </ul>
					     </div>
<!--Product Setting //-->
					     <div class="tabSetting">
								<table width="980" border="0" cellspacing="0" cellpadding="0">
								  	<tr align="right">
								      	<td width="100%">&nbsp;</td>
								      	<td width="300" valign="top">
								      		<div style="font-size:12px;padding-top:45px;">
												<?=tep_draw_form('general_setting', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=general_setting', 'POST')?>
												<table border="0" width="100%">
													<tr>
														<td class="tabsTd" width="150" nowrap>Perferred Margin</td>
														<td align="center">:</td>
														<td nowrap><?=tep_draw_input_field('perferred_margin', $PriceAutomationObj->pa_preferred_margin, 'size="5" onkeydown="if(event.keyCode == 13) {AssignMargin(this.value);Calculate_Selling_Price();}" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"',true)?></td>
														<td width="100%">&nbsp;</td>
													</tr>
													<tr>
														<td class="tabsTd" width="150" nowrap>Perferred Rank</td>
														<td align="center">:</td>
														<td nowrap><?=tep_draw_input_field('perferred_rank', $PriceAutomationObj->pa_bb_preferred_rank, 'size="5" onkeydown="if(event.keyCode == 13) {calculate_bb_price();}" onKeyUP="if (trim(this.value) != \'\' && !validateInteger(trim(this.value))) { this.value = \'\'; }"',true)?></td>
														<td width="100%">&nbsp;</td>
													</tr>
												</table>
												</form>
											</div>
								      	</td>
								      	<td valign="top">
								      		<?=tep_draw_form('products_info', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=products_info', 'POST')?>
									      	<table width="500" border="0" cellspacing="0" cellpadding="2">
											  	<tr>
											      	<td style="padding:13px;" colspan="2" valign="bottom">
														<?=TOTAL_BUYBACK_COMPETITORS?>: <?=$PriceAutomationObj->pa_bb_total_competitors?>
													&nbsp;
													</td>
											    </tr>
											    <tr class="priceAutomationHeading">
											    	<td width="1" class="padding_10">
											    		All<br/>
											    		<?=tep_draw_checkbox_field('check_all', 'all', '', '', 'onclick="CheckAllGameProduct();" id="check_all"');?>
											    		</td>
											      	<td class="padding_10"><?=TABLE_HEADING_PRODUCT?></td>
											    </tr>
											<?=$ProductSettingHTML?>
											<? unset($ProductSettingHTML)?>
											</table>
											</form>
								      	</td>
								    </tr>
								    <tr style="<?=$min_row_height?>">
								    	<td colspan="2">&nbsp;</td>
								    	<td align="center" class="averageRow" colspan="2">Average</td>
								    </tr>
								</table>
							</div>
						</td>
<!--BuyBack Competitor Price Setting-->
					 	<td width="1" valign="top">
						 	<div id="buyback_competitor_price_setting_toggle" class="tabSetting_hide" valign="top">
						 		<div id="buyback_competitor_price_setting-tab">
						        <ul>
						           <li id="tab_1"><a href="#buyback_competitor_price_setting" id="buyback_competitor_price_setting_sub_tab"><span><?=TAB_BUYBACK_COMPETITOR_PRICE_SETTING?></span></a></li>
						        </ul>
						    	</div>
								<table border="0" cellspacing="0" cellpadding="2" valign="top" class="ui-tabs-panel">
								  	<tr>
								      	<td nowrap>
								      	<table width="<?=$bcps_table_width?>" border="0" cellspacing="0" cellpadding="0" style="margin-top:-17px;">
										  	<tr>
										      	<td style="padding:13px;" colspan="2">&nbsp;</td>
										    </tr>
										    <tr class="priceAutomationHeading">
										      	<td valign="top">
										      	<table border="0" cellspacing="0" cellpadding="0" width="100%" class="priceAutomationBasicHeading">
										      		<tr align="center"><td class="padding_10"><?=TAB_HEADING_BUYBACK_COMPETITOR?></td></tr>
										      		<tr>
										      			<td>
										      			<?=tep_draw_form('buyback_competitor_form', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . 'action=products_info', 'POST', 'id="buyback_competitor_form"')?>	
										      			<table width="100%" border="0" cellspacing="0" cellpadding="0" class="priceAutomationBasicHeading">
														<?=$BuybackCompetitorHeadingHTML?>
														<? unset($BuybackCompetitorHeadingHTML)?>	
														</table>
														</form>
														</td>
													</tr>
										      	</table>	
										      	</td>
										    </tr>
										<?=$BuybackCompetitorHTML?>
										<? unset($BuybackCompetitorHTML)?>
										</table>	
								      	</td>
								    </tr>
								</table>
								<div id="buyback_competitor_price_setting" class="tabSetting_hide"></div>
							</div>
						</td>
<!--BuyBack's Price Setting-->
						<td width="1" valign="top">
							<div id="buyback_price_setting_toggle" class="tabSetting_hide">
						 		<div id="buyback_price_setting-tab">
							        <ul>
							           <li id="tab_2"><a href="#buyback_price_setting" id="buyback_price_setting_sub_tab"><span><?=TAB_BUYBACK_PRICE_SETTING?></span></a></li>
							        </ul>
						    	</div>
						 	
								<table border="0" width="100" cellspacing="0" cellpadding="2" valign="top" class="ui-tabs-panel">
								  	<tr>
								      	<td nowrap>
								      	<?=tep_draw_form('buyback_price_form', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . '', 'POST', 'enctype="multipart/form-data" id="buyback_price_form"')?>
								      	<?=tep_draw_hidden_field('bb_hidden_html_content', '', 'id="bb_hidden_html_content"')?>
								      	<?=tep_draw_hidden_field('buyback_csv_content', '', 'id="buyback_csv_content"')?>
										<?=tep_draw_hidden_field('bb_hidden_pid_content', '', 'id="bb_hidden_pid_content"')?>
								      	<table width="450" border="0" cellspacing="0" cellpadding="0" style="margin-top:-17px;">
										  	<tr>
										      	<td style="padding:13px;" colspan="2">&nbsp;</td>
										    </tr>
										    <tr class="priceAutomationHeading">
										      	<td valign="top">
										      	<table border="0" cellspacing="0" cellpadding="0" width="450" class="priceAutomationBasicHeading">
										      		<tr align="center"><td class="padding_10" colspan="10"><?=TAB_HEADING_BUYBACK?></td></tr>
										      		<tr align="center" valign="top" height="53">
										      			<td width="150" class="buybackPaddingTop">
										      				<?=TAB_SUB_HEADING_BO_SUGGESTED_PRICE?><br/>
										      				<?=tep_button(BTN_CALCULATE, '', '', 'onclick="calculate_bb_price();"', 'InputButton');?>
										      			</td>
										      			<td width="150" class="buybackPaddingTop"><?=TAB_SUB_HEADING_BO_CURRENT_PRICE?></td>
										      			<td width="150" class="buybackPaddingTop">
										      				<?=TAB_SUB_HEADING_BO_OVERWRITE_PRICE?><br/>
										      				<?=tep_button(BTN_CLEAR, '', '', 'onclick="ClearFormJS(\'overwrite_buyback\');"', 'InputButton');?>
										      			</td>
										      		</tr>
										      	<tbody id="buyback_price_setting_ajax" width="450">
												<?=$BuybackPriceHTML?>
												<? unset($BuybackPriceHTML)?>
										    	</tbody>
										      	</table>
										    	</td>	
										    </tr>
										    <tr>
										    	<td align="left">
										    	<?=tep_draw_input_field('buyback_overwrite_all_price', '', 'id="buyback_overwrite_all_price" size="10" onkeydown="if(event.keyCode == 13) {update_overwrite_all_price(\'buyback_overwrite_all_price\', \'overwrite_buyback\');}" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"')?>
												<?=tep_button(BTN_BATCH_FILL, '', '', 'onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }" onclick="update_overwrite_all_price(\'buyback_overwrite_all_price\', \'overwrite_buyback\');"', 'InputButton');?>
										    	</td>
										    </tr>
										    
										    <tr><td align="right" style="padding-top:20px;"><?=tep_submit_button(BTN_UPDATE_BUYBACK_PRICE, '', '', 'InputButton');?></td></tr>
										</table>
										</form>
								      	</td>
								    </tr>
								    <tr>
								    	<td align="left">
								    	<?=tep_image(DIR_WS_IMAGES.'ajax_loading.gif', '', '', '', 'id="bb_loading" style="display:none;"')?>
								    	<?=tep_draw_file_field('import_buyback_price_info', 'id="import_buyback_price_info"');?><br/>
								    	<?=tep_button(BTN_IMPORT, '', '', 'id="buttonUpload" onclick="return ajaxFileUpload(\'import_buyback_price_info\', \'bb_loading\', \'1\');"', 'InputButton');?>
										<?=tep_button(BTN_EXPORT, '', '', 'onClick="export_buyback_price(\''.FILENAME_PRICE_AUTOMATION.'?'.tep_get_all_get_params(array('action')) . 'action=buyback_csv_export'.'\');"', 'InputButton');?>
								    	</td>
								    </tr>
								</table>
								<div id="backorder_setting" class="tabSetting_hide"></div>
							</div>
						</td>
<!--Back Order Setting-->
						<td width="1" valign="top">
						 	<div id="backorder_setting_toggle" class="tabSetting_hide">
						 		<div id="backorder_setting-tab">
							        <ul>
							           <li id="tab_3"><a href="#backorder_setting" id="backorder_setting_sub_tab"><span><?=TAB_BACKORDER_SETTING?></span></a></li>
							        </ul>
						    	</div>
								<table border="0" cellspacing="0" cellpadding="0" valign="top" class="ui-tabs-panel">
								  	<tr>
								      	<td nowrap>
								      	<table width="500" border="0" cellspacing="0" cellpadding="0" style="margin-top:-17px;">
										  	<tr>
										      	<td style="padding:14px;" colspan="2">&nbsp;</td>
										    </tr>
										    <tr>
										    	<td>
										    	<table width="100%" border="0" cellspacing="0" cellpadding="0" class="priceAutomationHeading">
										    		<tr>
										      			<td class="padding_15" align="center" colspan="7"><?=TAB_AGING_BO_N_COMPLETED?></td>
										      		</tr>
										      		<tr align="center">
										      			<td class="padding_10" width="<?=(100/7)?>%">< 1 <?=TAB_SUBTXT_DAY?></td>
										      			<td class="padding_10" width="<?=(100/7)?>%">< 2 <?=TAB_SUBTXT_DAYS?></td>
										      			<td class="padding_10" width="<?=(100/7)?>%">< 3 <?=TAB_SUBTXT_DAYS?></td>
										      			<td class="padding_10" width="<?=(100/7)?>%">< 4 <?=TAB_SUBTXT_DAYS?></td>
										      			<td class="padding_10" width="<?=(100/7)?>%">< 5 <?=TAB_SUBTXT_DAYS?></td>
										      			<td class="padding_10" width="<?=(100/7)?>%">< 2 <?=TAB_SUBTXT_WEEKS?></td>
										      			<td class="padding_10" width="<?=(100/7)?>%">< 1 - 3 <?=TAB_SUBTXT_MONTHS?></td>
										      		</tr>
										      	</table>
										    	</td>
										    </tr>
										<?=$BackOrderHTML?>
										<? unset($BackOrderHTML)?>
										</table>	
								      	</td>
								    </tr>
								</table>
								<div id="buyback_price_setting" class="tabSetting_hide"></div>
							</div>
						</td>
<!--Selling's Price Setting-->

						<td width="1" valign="top">
							<div id="store_record">
						 	<div id="selling_price_setting_toggle" class="tabSetting_hide">
						 		<div id="selling_price_setting-tab">
							        <ul>
							           <li id="tab_4"><a href="#selling_price_setting" id="selling_price_setting_sub_tab"><span>Selling's Price Setting</span></a></li>
							        </ul>
						    	</div>
						    	<?=tep_draw_form('selling_price_form', FILENAME_PRICE_AUTOMATION, tep_get_all_get_params(array('action')) . '', 'POST', 'enctype="multipart/form-data" id="selling_price_form"')?>
						    	<?=tep_draw_hidden_field('selling_hidden_html_content', '', 'id="selling_hidden_html_content"')?>
						    	<?=tep_draw_hidden_field('selling_csv_content', '', 'id="selling_csv_content"')?>
								<?=tep_draw_hidden_field('selling_hidden_pid_content', '', 'id="selling_hidden_pid_content"')?>
								<table border="0" cellspacing="0" cellpadding="0" valign="top" class="ui-tabs-panel">
								  	<tr>
								      	<td nowrap>
								      	<table width="1200" border="0" cellspacing="0" cellpadding="0" style="margin-top:-9px;">
										  	<tr>
										      	<td style="padding:10px;" colspan="2">&nbsp;</td>
										    </tr>
										    <tr>
										    	<td>
										    	<table width="100%" border="0" cellspacing="0" cellpadding="0" class="priceAutomationHeading">
										    		<tr>
										      			<td class="padding_10" align="center" colspan="6"><?=ENTRY_SELLING?></td>
										      		</tr>
										      		<tr align="center">
										      			<td class="padding_10" width="21%" nowrap><?=TAB_SUB_HEADING_SELLING_MARKUP_PERCENTAGE?> %</td>
										      			<td class="padding_10" width="20%" nowrap><?=TAB_SUB_HEADING_SELLING_INCREMENT_VALUE?>
										      				<br />
										      				<?=tep_draw_input_field('increment_value', '0.5%', 'id="increment_value" maxlength="6" size="5" onkeydown="if(event.keyCode == 13) {update_increment_value();}"')?>
										      				<?=tep_button(BTN_UPDATE, '', '', 'onclick="blockUI_disable();update_increment_value();calculate_price_row(\'batch_update\');"', 'InputButton');?>
										      			</td>
										      			<td class="padding_10" width="16%" nowrap><?=TAB_SUB_HEADING_SELLING_SUGGESTED_PRICE?><br/>
										      				<?=tep_button(BTN_UPDATE, '', '', 'onclick="Calculate_Selling_Price();"', 'InputButton');?></td>
										      			<td class="padding_10" width="14%" nowrap><?=TAB_SUB_HEADING_SELLING_CURRENT_PRICE?></td>
										      			<td class="padding_10" width="17%" nowrap><?=TAB_SUB_HEADING_BO_OVERWRITE_PRICE?><br/>
										      				<?=tep_button(BTN_CLEAR, '', '', 'onclick="ClearFormJS(\'overwrite_selling_price\');"', 'InputButton');?></td>
										      			<td class="padding_10" width="12%" nowrap><?=TAB_SUB_HEADING_SELLING_RANK?><br/><a href="#"  id="view_selling_competitor_details"><?=VIEW_DETAILS?></a></td>
										      		</tr>
										      	</table>
										    	</td>
										    	<td>
										    		<table width="100%" border="0" cellspacing="0" cellpadding="0" class="priceAutomationHeading">
											    		<tr>
											      			<td class="padding_10" align="center" colspan="6"></td>
											      		</tr>
											      		<tr align="center" id="package_product_heading">
<?php
	$product_qty_heading_cnt = 0;
	
	$product_quantity_arr = explode(";", $cat_cfg_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE']);
	sort($product_quantity_arr);
	foreach ($product_quantity_arr as $package_quantity) {
		echo '<td id="package_heading" class="padding_10" width="100" nowrap>'.$package_quantity.tep_draw_hidden_field('package_heading_cnt', $package_quantity, 'id="package_heading_'.$product_qty_heading_cnt.'" class="package_heading_cnt"').'</td>';
		$product_qty_heading_cnt ++;
	}
?>
											      		</tr>
											      	</table>
										    	</td>
										    </tr>
<?php
$row_count = 0;
	if($selling_price_setting_permission) {
		$SellingPriceSettingHTML .= '<tr>
										<td>
											<table width="100%" border="0" cellspacing="0" cellpadding="0">';
		$SellingPriceSettingHTML .= 		$SellingPriceSettingRow;
		$SellingPriceSettingHTML .= '		</table>
										</td>';
		
		$SellingPriceSettingHTML .= '	<td>
											<input type="hidden" name="checking_package_table" id="checking_package_table" value="" />
											<table width="100%" border="0" cellspacing="0" cellpadding="0" id="package_table">
											<tr>
												<td>
											'.$SellingPricePackage.'
												</td>
											</tr>
											</table>
										</td>';
		$SellingPriceSettingHTML .= '</tr>';
		
		$SellingPriceSettingHTML .= '<tr>
										<td>
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
										<tr width="450" align="center" style="'.$min_row_height.'" class="averageRow">
											<td width="21%"></td>
											<td width="20%"></td>
											<td width="16%"><div id="avg_selling_suggested_price"></div></td>
											<td width="14%">'.number_format(round($PriceAutomationObj->array_average($avg_current_selling_price_array), 6), 6, '.', '').'</td>
											<td width="17%"><div id="avg_selling_overwrite_price"></div></td>
											<td width="12%"><div id="avg_selling_rank"></div></td>
										</tr>
										</table>
										</td>
										<td>
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
											<tr width="450" align="center" style="'.$min_row_height.'" class="averageRow">
												<td>&nbsp;</td>
											</tr>
										</table>
										</td>	
									  </tr>';
	}
?>
										<?=$SellingPriceSettingHTML?>
										<? unset($SellingPriceSettingHTML)?>
												<tr>
											    	<td align="left">
											    	<?=tep_draw_input_field('selling_overwrite_all_price', '', 'id="selling_overwrite_all_price" size="10" onkeydown="if(event.keyCode == 13) {update_overwrite_all_price(\'selling_overwrite_all_price\', \'overwrite_selling_price\');Calculate_Selling_Price(\'true\');}" onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }"')?>
													<?=tep_button(BTN_BATCH_FILL, '', '', 'onKeyUP="if (trim(this.value) != \'\' && !validateMayHvPtDecimal(trim(this.value))) { this.value = \'\'; }" onclick="update_overwrite_all_price(\'selling_overwrite_all_price\', \'overwrite_selling_price\');Calculate_Selling_Price(\'true\');"', 'InputButton');?>
											    	</td>
											    </tr>
											    
											    <tr><td align="right" style="padding-top:20px;"><?=tep_submit_button(BTN_UPDATE_SELLING_PRICE, '', '', 'InputButton');?></td></tr>											</table>
										</form>
										<?=tep_draw_form('history_html', FILENAME_PRICE_AUTOMATION, '', 'POST', 'id="history_html"')?>
											<?=tep_draw_hidden_field('hidden_html_content', '', 'id="hidden_html_content"')?>
										</form>
										</td>
									</tr>
									<tr>
								    	<td align="left">
								    	<img id="selling_loading" src="<?=tep_href_link(DIR_WS_IMAGES."ajax_loading.gif")?>" style="display:none;" />
								    	<?=tep_draw_file_field('import_selling_price_info', 'id="import_selling_price_info"');?><br/>
								    	<?=tep_button(BTN_IMPORT, '', '', 'id="buttonUpload" onclick="return ajaxFileUpload(\'import_selling_price_info\', \'selling_loading\', \'2\');"', 'InputButton');?>
										<?=tep_button(BTN_EXPORT, '', '', 'onClick="export_selling_price(\''.FILENAME_PRICE_AUTOMATION.'?'.tep_get_all_get_params(array('action')) . 'action=selling_csv_export'.'\');"', 'InputButton');?>
								    	</td>
								    </tr>
								</table>
								<div id="selling_price_setting" class="tabSetting_hide"></div>
							</div></div>
						</td>

<!--Selling Competitor Price Setting //-->
						<td width="1" valign="top">
						 	<div id="selling_competitor_setting_toggle" class="tabSetting_hide">
						 		<div id="selling_competitor_setting-tab">
							        <ul>
							           <li id="tab_5"><a href="#selling_competitor_setting" id="selling_competitor_setting_sub_tab"><span><?=TAB_SELLING_COMPETITOR_PRICE_SETTING?></span></a></li>
							        </ul>
						    	</div>
						    	
						    	<table border="0" cellspacing="0" cellpadding="2" valign="top" class="ui-tabs-panel">
								  	<tr>
								      	<td nowrap>
								      	<table width="<?=$scps_table_width?>" border="0" cellspacing="0" cellpadding="0" style="margin-top:-17px;">
										  	<tr>
										      	<td style="padding:13px;" colspan="2">&nbsp;</td>
										    </tr>
										    <tr class="priceAutomationHeading">
										      	<td valign="top">
										      	<table border="0" cellspacing="0" cellpadding="0" width="100%" class="priceAutomationBasicHeading">
										      		<tr align="center"><td class="padding_15"><?=TAB_HEADING_SELLING_COMPETITOR?></td></tr>
										      		<tr>
										      			<td>
										      			<?=tep_draw_form('selling_competitor_form', FILENAME_PRICE_AUTOMATION, 'id="selling_competitor_form"', 'POST')?>	
										      			<table width="100%" border="0" cellspacing="0" cellpadding="0" class="priceAutomationBasicHeading">
<?php
	if ($scps_column > 0) {
		$SellingCompetitorHeadingHTML .= '<tr>';
		$SellingCompetitor_cnt = 0;
		foreach ($PriceAutomationObj->pa_selling_competitors_array as $selling_competitor_key => $selling_competitor_value) {
			$SellingCompetitorHeadingHTML .= tep_draw_hidden_field('selling_competitor_heading_column', $selling_competitor_value['code'], 'id="selling_competitor_heading_column_'.$SellingCompetitor_cnt.'" class="selling_competitor_heading_column"');
			$SellingCompetitorHeadingHTML .= tep_draw_hidden_field('selling_competitor_id', $selling_competitor_key, 'id="selling_competitor_id_'.$SellingCompetitor_cnt.'"');
			$SellingCompetitorHeadingHTML .= '<td class="sellingCompetitorHeadingColumn" width="'.$scps_td_width.'" align="center">'.$selling_competitor_value['code'].'</td>';
			$SellingCompetitor_cnt ++;
		}
	    $SellingCompetitorHeadingHTML .= '  <td width='.$scps_td_mod.'>&nbsp;</td>
	    									</tr>';
	}
?>
														<?=$SellingCompetitorHeadingHTML?>
														<? unset($SellingCompetitorHeadingHTML)?>
														</table>
														</form>
														</td>
													</tr>
										      	</table>	
										      	</td>
										    </tr>
<?php
	$row_count = 0;
	if(count($PriceAutomationObj->pa_selling_products_array) > 0) {
		$SellingCompetitorHTML .= '<tr>
									<td align="center">
									<table border="0" width="100%" cellspacing="0" cellpadding="0">
										<tr>';
		foreach ($Average_selling_price as $Average_selling_key => $Average_selling_value) {
			$SellingAveragePrice = round($PriceAutomationObj->array_average($Average_selling_price[$Average_selling_key]), 6);
			$SellingCompetitorHTML .= '	<td class="averageRow" width="'.$scps_td_width.'" align="center">'.$SellingAveragePrice.'</td>';
		}								
		$SellingCompetitorHTML .= ' 	<td class="averageRow" width='.$scps_td_mod.'>&nbsp;</td>
										</tr>
									</table>
									</td>
								   </tr>';
	}
?>										
										<?=$SellingCompetitorHTML?>
										<? unset($SellingCompetitorHTML)?>
										</table>	
								      	</td>
								    </tr>
								</table>
								
								<div id="selling_competitor_setting" class="tabSetting_hide"></div>
							</div>
						</td>
						
      				</tr>
      			</table>
      		</div>
   			<?php } ?> <!-- [eof] if(main_cat_id > 0)  //-->   
			</td>
  		</tr>
	</table>
<?php
//$PriceAutomationObj_json = str_replace('\'', ' ', $json->encode($PriceAutomationObj));

$bb_competitors_price_json = str_replace('\'', ' ', $json->encode($PriceAutomationObj->pa_bb_competitors_price_array));
$bb_products_json = str_replace('\'', ' ', $json->encode($PriceAutomationObj->pa_bb_products_array));
$pa_bb_total_products_json = str_replace('\'', ' ', $json->encode($PriceAutomationObj->pa_bb_total_products));
		
?>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
<form id="myform">
	<input type="hidden" value="abc123" name="abc" />
</form>

<script type="text/javascript">
	<?=$JSHTML?>
	
	function HideServer (hiding_type) {
		switch (hiding_type) {
			case 'bo_days':
				if(jQuery("#bo_days_greater_checkbox").attr('checked') == true) {
					var bo_days_greater = jQuery("#bo_days_greater").val();
					var game_product_id = '';
					var bo_days = 0;
					if(bo_days_greater == '') {
						alert('<?=JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK?>');
					} else {
						for (var i=0; i < document.products_info.game_product_id.length; i++) {
							game_product_id = document.products_info.game_product_id[i].value;
							bo_days = 0;
							
							bo_days = jQuery("#bo_oldest_order_day_"+game_product_id).val();
							if (bo_days != undefined) {
								if (eval(bo_days + '<' + bo_days_greater)) {
									jQuery("#product_setting_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_competitor_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#backorder_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_competitor_game_server_id_"+game_product_id).fadeOut('slow');
								}
							}
						}
					}
				}
				
				if(jQuery("#bo_days_lesser_checkbox").attr('checked') == true) {
					var bo_days_lesser = jQuery("#bo_days_lesser").val();
					var game_product_id = '';
					var bo_days = 0;
					if(bo_days_lesser == '') {
						alert('<?=JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK?>');
					} else {
						for (var i=0; i < document.products_info.game_product_id.length; i++) {
							game_product_id = document.products_info.game_product_id[i].value;
							bo_days = 0;
							
							bo_days = jQuery("#bo_oldest_order_day_"+game_product_id).val();
							if (bo_days != undefined) {
								if (eval(bo_days + '>' + bo_days_lesser)) {
									jQuery("#product_setting_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_competitor_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#backorder_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_competitor_game_server_id_"+game_product_id).fadeOut('slow');
								}
							}
						}
					}
				}
				break;
				
			case 'bo_amount':
				if(jQuery("#bo_amount_greater_checkbox").attr('checked') == true) {
					var bo_amount_greater = jQuery("#bo_amount_greater").val();
					var game_product_id = '';
					var backorder_amount = 0;
					if(bo_amount_greater == '') {
						alert('<?=JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK?>');
					} else {
						for (var i=0; i < document.products_info.game_product_id.length; i++) {
							game_product_id = document.products_info.game_product_id[i].value;
							backorder_amount = 0;
							
							backorder_amount = jQuery("#backorder_amount_"+game_product_id).val();
							if (backorder_amount != undefined) {
								if (eval(backorder_amount + '>' + bo_amount_greater)) {
									jQuery("#product_setting_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_competitor_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#backorder_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_competitor_game_server_id_"+game_product_id).fadeOut('slow');
								}
							}
						}
					}
				}
				
				if(jQuery("#bo_amount_lesser_checkbox").attr('checked') == true) {
					var bo_amount_lesser = jQuery("#bo_amount_lesser").val();
					var game_product_id = '';
					var backorder_amount = 0;
					if(bo_amount_lesser == '') {
						alert('<?=JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK?>');
					} else {
						for (var i=0; i < document.products_info.game_product_id.length; i++) {
							game_product_id = document.products_info.game_product_id[i].value;
							backorder_amount = 0;
							
							backorder_amount = jQuery("#backorder_amount_"+game_product_id).val();
							if (backorder_amount != undefined) {
								if (eval(backorder_amount + '<' + bo_amount_lesser)) {
									jQuery("#product_setting_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_competitor_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#backorder_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_competitor_game_server_id_"+game_product_id).fadeOut('slow');
								}
							}
						}
					}
				}
				break;
				
			case 'margin':
				if(jQuery("#margin_greater_checkbox").attr('checked') == true) {
					var margin_greater = jQuery("#margin_greater").val();
					var game_product_id = '';
					var margin = 0;
					if(margin_greater == '') {
						alert('<?=JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK?>');
					} else {
						for (var i=0; i < document.products_info.game_product_id.length; i++) {
							game_product_id = document.products_info.game_product_id[i].value;
							margin = 0;
							
							margin = jQuery("#selling_price_margin_"+game_product_id).val();
							if (margin != undefined) {
								if (eval(margin + '>' + margin_greater)) {
									jQuery("#product_setting_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_competitor_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#backorder_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_competitor_game_server_id_"+game_product_id).fadeOut('slow');
								}
							}
						}
					}
				}
				
				if(jQuery("#margin_lesser_checkbox").attr('checked') == true) {
					var margin_lesser = jQuery("#margin_lesser").val();
					var game_product_id = '';
					var margin = 0;
					if(margin_lesser == '') {
						alert('<?=JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK?>');
					} else {
						for (var i=0; i < document.products_info.game_product_id.length; i++) {
							game_product_id = document.products_info.game_product_id[i].value;
							margin = 0;
							
							margin = jQuery("#selling_price_margin_"+game_product_id).val();
							if (margin != undefined) {
								if (eval(margin + '<' + margin_lesser)) {
									jQuery("#product_setting_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_competitor_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#backorder_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_competitor_game_server_id_"+game_product_id).fadeOut('slow');
								}
							}
						}
					}
				}
				break;
				
			case 'selling_rank':
				if(jQuery("#selling_rank_greater_checkbox").attr('checked') == true) {
					var selling_rank_greater = jQuery("#selling_rank_greater").val();
					var game_product_id = '';
					var selling_rank = 0;
					if(selling_rank_greater == '') {
						alert('<?=JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK?>');
					} else {
						for (var i=0; i < document.products_info.game_product_id.length; i++) {
							game_product_id = document.products_info.game_product_id[i].value;
							selling_rank = 0;
							
							selling_rank = jQuery("#selling_competitors_rank_value_"+game_product_id).val();
							if (selling_rank != undefined) {
								if (eval(selling_rank + '>' + selling_rank_greater)) {
									jQuery("#product_setting_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_competitor_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#backorder_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_competitor_game_server_id_"+game_product_id).fadeOut('slow');
								}
							}
						}
					}
				}
				
				if(jQuery("#selling_rank_lesser_checkbox").attr('checked') == true) {
					var selling_rank_lesser = jQuery("#selling_rank_lesser").val();
					var game_product_id = '';
					var selling_rank = 0;
					if(selling_rank_lesser == '') {
						alert('<?=JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK?>');
					} else {
						for (var i=0; i < document.products_info.game_product_id.length; i++) {
							game_product_id = document.products_info.game_product_id[i].value;
							selling_rank = 0;
							
							selling_rank = jQuery("#selling_competitors_rank_value_"+game_product_id).val();
							if (selling_rank != undefined) {
								if (eval(selling_rank + '<' + selling_rank_lesser)) {
									jQuery("#product_setting_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_competitor_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#bb_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#backorder_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_price_game_server_id_"+game_product_id).fadeOut('slow');
									jQuery("#selling_competitor_game_server_id_"+game_product_id).fadeOut('slow');
								}
							}
						}
					}
				}
				break;
		}
	}
	
	function import_checking () {
		blockUI_disable();
		document.import.import_action.value = 'yes';
		document.import.submit();
	}
	
	function tongle_description (id) {
		if (jQuery('#'+id).css('display') == "none") {
				jQuery('#'+id).slideDown();
			jQuery('#'+id+'_icon').attr('src','<?=tep_href_link(DIR_WS_ICONS."collapse.gif")?>');
		} else {
			jQuery('#'+id).slideUp();
			jQuery('#'+id+'_icon').attr('src','<?=tep_href_link(DIR_WS_ICONS."expand.gif")?>');
		}
	}
	
	function toggleQuantities() {
	    if (jQuery("#all_package_quantities").attr('checked') == true) {
	      jQuery("#package_quantities").attr('disabled', 'disabled');
	    } else {
	       jQuery("#package_quantities").attr('disabled', '');
	    }
	}
	
	function savePackageQuantitySetting (main_cat_id) {
		blockUI_disable();
		if (jQuery("#package_quantities").val() == '' && jQuery("#all_package_quantities").attr('checked') != true) {
			alert('<?=JS_MSG_PLS_ENTER_THE_PCKG_QTY?>');
			jQuery.unblockUI();
		} else {
			if (jQuery("#all_package_quantities").attr('checked') == true) {
				jQuery("#package_quantities").val('all');
				jQuery("#package_quantities").attr('disabled', '');
			}
			document.batch_update_setting.submit();
		}
	}
	
	function calculate_bb_price () {
		var bb_competitors = new Array();
		var bb_rank = '';
		
		bb_rank = document.general_setting.perferred_rank.value;
		if (bb_rank != '') {
			blockUI_disable();
			if(isset(document.buyback_competitor_form.bb_competitor_selection.length)) {
				for (var i=0; i < document.buyback_competitor_form.bb_competitor_selection.length; i++) {
					competitor_id = document.buyback_competitor_form.bb_competitor_selection[i].value;
					if(jQuery("#buyback_competitor_checkbox_" + competitor_id).attr('checked') == true) {
						bb_competitors.push(jQuery("#buyback_competitor_checkbox_" + competitor_id).val());
					}
				}
			} else {
				if (isset(document.buyback_competitor_form.bb_competitor_selection.value)) {
					competitor_id = document.buyback_competitor_form.bb_competitor_selection.value;
					if(jQuery("#buyback_competitor_checkbox_" + competitor_id).attr('checked') == true) {
						bb_competitors.push(jQuery("#buyback_competitor_checkbox_" + competitor_id).val());
					}
				}
			}
			
			if (bb_competitors == '') {
				jQuery.unblockUI();
				alert('<?=JS_MSG_PLS_SELECT_THE_BUYBACK_COMPETITORS?>');
			} else {
				jQuery().ajaxStop(jQuery.unblockUI);
				
				var bb_competitors_price_json = '<?=$bb_competitors_price_json?>';
				var bb_products_json = '<?=$bb_products_json?>';
				var pa_bb_total_products_json = '<?=$pa_bb_total_products_json?>';
				
				var cat_id = '<?=$main_cat_id?>';
				jQuery.post("price_automation_xmlhttp.php?cat_id="+cat_id+"&action=buyback_price_setting&bb_rank="+bb_rank+"&bb_competitor_selected="+bb_competitors, {BB_competitors_priceObj: bb_competitors_price_json, BB_productsObj: bb_products_json, PA_bb_total_productsObj: pa_bb_total_products_json},function(data){
					
				  jQuery("#buyback_price_setting_ajax").html(data);
				});
			}
		} else {
			alert('<?=JS_MSG_BUYBACK_PREFERRED_RANK_CANNOT_BE_LEFT_BLANK?>');
		}
	}
	
	function alert_color_setting_row (selling_price_margin, game_product_id) {
		var total_alert_setting_margin = 0;
		
		if (isset(document.alert_setting.alert_color_setting_margin)) {
			total_alert_setting_margin = document.alert_setting.alert_color_setting_margin.length;
		} else {
			total_alert_setting_margin = 0;
		}
		
		if (total_alert_setting_margin != undefined || total_alert_setting_margin != 0) {
			var last_record = eval(total_alert_setting_margin + '-' + 1);
			for (var i=0; i < total_alert_setting_margin; i++) {
				var margin_percentage = jQuery("#as_margin_prev_"+i).val();
				var alert_color = jQuery("#alert_color_setting_margin_"+i).val();
				
				if (i != last_record) {
					if (eval(margin_percentage + '>' + selling_price_margin)) {
						jQuery("#selling_price_row_"+game_product_id).css({background:alert_color});
						break;
					}
				} else {
					jQuery("#selling_price_row_"+game_product_id).css({background:alert_color});
					break;
				}
			}
		}
	}
	
	function Calculate_Selling_Price(overwrite_all) {
		var game_id = '';
		var total_selling_suggested_price = '';
		var avg_selling_price_string = '';
		var avg_selling_price = '';
		var avg_rank = 0;
		blockUI_disable();
		
		var products_array = new Array();
		
		if (document.products_info.game_product_id.length == undefined) {
			products_array.push(document.products_info.game_product_id.value)
		} else {
			for (var i=0; i < document.products_info.game_product_id.length; i++) {
				products_array.push(document.products_info.game_product_id[i].value);
			}
		}
		
		for (var i=0; i < products_array.length; i++) {
			var overwrited_price = 0;
			var selling_suggested_price = 0;
			var selling_price_margin = 0;

		    game_id = products_array[i];
		      
		    selling_suggested_price = Get_Selling_Suggested_Price(game_id);

		    jQuery("#selling_suggested_price_text_"+game_id).text(selling_suggested_price);
		    jQuery("#selling_overwrite_price_"+game_id).text(selling_suggested_price);
		    jQuery("#selling_suggested_price_"+game_id).val(selling_suggested_price);
		    
		    selling_price_margin = jQuery("#selling_price_margin_"+game_id).val();
		    alert_color_setting_row(selling_price_margin, game_id);
		    
		    total_selling_suggested_price = eval(total_selling_suggested_price + '+' + selling_suggested_price);
		    
		    if (overwrite_all=='true') {
		    	overwrited_price = jQuery("#selling_overwrite_price_"+game_id).val();
		    } else {
		    	jQuery("#selling_overwrite_price_"+game_id).val(selling_suggested_price);
		    	overwrited_price = jQuery("#selling_overwrite_price_"+game_id).val();
		    }
	    	<?php if(count($PriceAutomationObj->pa_selling_products_array) > 0) { ?>
			    rank = Get_Selling_Rank(game_id, i, overwrited_price);
	
			    if (rank == 0) {
			    	jQuery("#selling_competitors_rank_text_"+game_id).text(0);
			    	jQuery("#selling_competitors_rank_value_"+game_id).val(0);
			    } else {
			    	jQuery("#selling_competitors_rank_text_"+game_id).text(rank);
			    	jQuery("#selling_competitors_rank_value_"+game_id).val(rank);
			    }
			<?php } ?>
		}
		
		avg_selling_price_string = total_selling_suggested_price + '/' + products_array.length;
		avg_selling_price = Math.round(eval(avg_selling_price_string)*1000000)/1000000;
		jQuery("#avg_selling_suggested_price").text(avg_selling_price);
		jQuery("#avg_selling_overwrite_price").text(avg_selling_price);
		
		<?php if(count($PriceAutomationObj->pa_selling_products_array) > 0) { ?>
        	Avg_Selling_Rank();
    	<?php }?>
    	jQuery.unblockUI();
	}
	
	function AssignMargin(margin_value) {
		jQuery('.selling_price_margin_class').val(margin_value);
	}
	
	function Update_Margin(game_id, game_cnt) {
		var total_competitors = 0;
		var selling_suggested_price = 0;
		var overwrited_price = 0;
		var rank = 0;
		var bb_overwrited_price = '';
		
		var bb_overwrited_price = jQuery("#overwrite_bb_price_"+game_id).val();
		
		if (bb_overwrited_price == '') { 
			alert('<?=ENTRY_BUYBACK." ".JS_MSG_INPUTBOX_CANNOT_LEFT_BLANK?>');
		} else {
			selling_suggested_price = Get_Selling_Suggested_Price(game_id);
		    jQuery("#selling_suggested_price_text_"+game_id).text(selling_suggested_price);
		    jQuery("#selling_overwrite_price_"+game_id).text(selling_suggested_price);
		    jQuery("#selling_overwrite_price_"+game_id).val(selling_suggested_price);
			overwrited_price = jQuery("#selling_overwrite_price_"+game_id).val();
			
			selling_price_margin = jQuery("#selling_price_margin_"+game_id).val();
		    alert_color_setting_row(selling_price_margin, game_id);
			
			<?php if(count($PriceAutomationObj->pa_selling_products_array) > 0) { ?>
			rank = Get_Selling_Rank(game_id, game_cnt, overwrited_price);
			if (rank == 0) {
		    	jQuery("#selling_competitors_rank_text_"+game_id).text(0);
		    	jQuery("#selling_competitors_rank_value_"+game_id).val(0);
		    } else {
		    	jQuery("#selling_competitors_rank_text_"+game_id).text(rank);
		    	jQuery("#selling_competitors_rank_value_"+game_id).val(rank);
		    }
		    Avg_Selling_Rank();
			<?php } ?>
		    Avg_Selling_Price();
		}
	}
	
	function Update_Selling_OverWritePrice(game_id, game_cnt) {
		var total_competitors = 0;
		var overwrited_bb_price = 0;
		var overwrited_selling_price = 0;
		var margin = 0;

		overwrited_selling_price = jQuery("#selling_overwrite_price_"+game_id).val();
		
		overwrited_bb_price = jQuery("#overwrite_bb_price_"+game_id).val();
		
		margin = eval(parseFloat(overwrited_bb_price) + '/' + overwrited_selling_price);
		margin = parseFloat(Math.round(eval(100 - (margin * 100))));
		
		jQuery("#selling_price_margin_"+game_id).val(margin);
		alert_color_setting_row(margin, game_id);
		<?php if(count($PriceAutomationObj->pa_selling_products_array) > 0) { ?>
		rank = Get_Selling_Rank(game_id, game_cnt, overwrited_selling_price);
		if (rank == 0) {
	    	jQuery("#selling_competitors_rank_text_"+game_id).text(0);
	    	jQuery("#selling_competitors_rank_value_"+game_id).val(0);
	    } else {
	    	jQuery("#selling_competitors_rank_text_"+game_id).text(rank);
	    	jQuery("#selling_competitors_rank_value_"+game_id).val(rank);
	    }
	    Avg_Selling_Rank();
		<?php } ?>
	}
	
	function Get_Selling_Suggested_Price (game_id) { // FORMULA: Margin = (Selling - Buyback) / Selling 
		var bb_products_price = 0;
		var margin = 0;
		var margin_string = 0;
		var margin_percentage = 0;
		var selling_suggested_price_string = 0;
		var selling_suggested_price = 0;
		
		if (jQuery("#overwrite_bb_price_"+game_id).val() == 0) {
			bb_products_price = 0;
		} else {
			bb_products_price = jQuery("#overwrite_bb_price_"+game_id).val();
		}
		
	    margin = jQuery("#selling_price_margin_"+game_id).val();
	    margin_string = '(100 - ' + margin + ')' + '/100'; // (a - b) / 100 
	    margin_percentage = eval(margin_string); 
	    selling_suggested_price_string = bb_products_price + '/' + margin_percentage; // d = (a / b)
	    selling_suggested_price = Math.round(eval(selling_suggested_price_string)*1000000)/1000000;
	    
	    return selling_suggested_price;
	}
	
	function Get_Selling_Rank (game_id, game_cnt, compare_price) {
		var total_competitors = 0;
		var rank = 0;
		var selling_product_rank = 0;
		var competitor_price = 0;
		
		if (document.products_info.game_product_id.length != undefined) {
			var form_ref = document.selling_price_competitors_form[game_cnt];
		} else {
			var form_ref = document.selling_price_competitors_form;
		}
		
		total_competitors = form_ref.elements['selling_price_competitors_'+game_id].length;
		
		if (total_competitors != undefined) {
			for (var j=0; j < total_competitors; j++) {
				competitor_price = form_ref.elements['selling_price_competitors_'+game_id][j].value;
				if (!empty(competitor_price)) {
					compare_price = parseFloat(compare_price);
					competitor_price = parseFloat(competitor_price);
					if (eval(compare_price + '<' + competitor_price)) { // Rank 1 - Cheapest | Rank 10 - Most expensive
			    		rank ++;
			    	}
				} else {
					rank ++;
				}
		    }
		    selling_product_rank = eval((total_competitors + '-' + rank) + '+' + 1);
		}
	    return selling_product_rank;
	}
	
	function Avg_Selling_Rank () {
		var avg_rank = 0;
		var total_available_rank = 0;
		var available_rank = 0;
		var avg_rank_round = 0;
		
		for (var i=0; i < document.products_info.game_product_id.length; i++) {
			var rank = 0;
			var game_id = 0;
			
			game_id = document.products_info.game_product_id[i].value;
			rank = jQuery("#selling_competitors_rank_value_"+game_id).val();
			if (rank != 0) {
		    	total_available_rank ++;
		    	available_rank = eval(available_rank + '+' + rank);
		    }
		}
		avg_rank = eval(available_rank + '/' + total_available_rank);
		avg_rank_round = Math.round(eval(avg_rank)*1)/1;
		jQuery("#avg_selling_rank").text(avg_rank_round);
	}
	
	function Avg_Selling_Price () {
		var total_selling_suggested_price = 0;
		var total_selling_overwrited_price = 0;
		var avg_selling_price_string = 0;
		var avg_selling_price = 0;
		
		for (var i=0; i < document.products_info.game_product_id.length; i++) {
			var rank = 0;
			var overwrited_price = 0;
			var selling_suggested_price = 0;
			var selling_overwrited_price = 0;
			
			game_id = document.products_info.game_product_id[i].value;
			
			selling_suggested_price = trim(jQuery("#selling_suggested_price_text_"+game_id).text());
			selling_overwrited_price = jQuery("#selling_overwrite_price_"+game_id).val();
			
			total_selling_suggested_price = eval(total_selling_suggested_price + '+' + (selling_suggested_price != '' ? selling_suggested_price : 0));
			total_selling_overwrited_price = eval(total_selling_overwrited_price + '+' + (selling_overwrited_price != '' ? selling_overwrited_price : 0));
		}
		
		avg_selling_suggested_price_string = total_selling_suggested_price + '/' + document.products_info.game_product_id.length;
		avg_selling_suggested_price = Math.round(eval(avg_selling_suggested_price_string)*1000000)/1000000;
		
		avg_selling_overwrited_price_string = total_selling_overwrited_price + '/' + document.products_info.game_product_id.length;
		avg_selling_overwrited_price = Math.round(eval(avg_selling_overwrited_price_string)*1000000)/1000000;
		
		jQuery("#avg_selling_suggested_price").text(avg_selling_suggested_price);
		jQuery("#avg_selling_overwrite_price").text(avg_selling_overwrited_price);
	}
	
	function update_current_price(type) {
		var status	= confirm('Confirm to update?');
		
		if(status == true) {
			//blockUI_disable();
			var JsonObjText = '';
			var Pre_Json_Text = new Array();
			var checked_cnt = 1;
			var checked_product_total_cnt = 0;
			var cat_id = '<?=$main_cat_id?>';
			
			JsonObjText = '{"PriceObj": [';
			
			checked_product_total_cnt = jQuery(".gameProductSelected").length;
			
			jQuery(".gameProductSelected").each (function() {
				var game_product_id = this.value;
				switch(type) {
					case 'buyback':
						var buyback_price = document.buyback_price_form.elements['overwrite_bb_price_' + game_product_id].value;
						if (buyback_price != '') {
							JsonObjText = JsonObjText + '{"game_product_id": "' + game_product_id +'", "buyback_price": "' + buyback_price + '"}';
							if(checked_cnt != checked_product_total_cnt) {
								JsonObjText = JsonObjText + ",";
							}
							jQuery("#bb_current_price_text_"+game_product_id).text(buyback_price);
							checked_cnt ++;
						}
					break;
					
					case 'selling':
						var selling_price = document.selling_price_form.elements['selling_overwrite_price_' + game_product_id].value;
						if (selling_price != '') {
							JsonObjText = JsonObjText + '{"game_product_id": "' + game_product_id +'", "selling_price": "' + selling_price + '"}';
							if(checked_cnt != checked_product_total_cnt) {
								JsonObjText = JsonObjText + ",";
							}
							jQuery("#selling_current_price_"+game_product_id).text(selling_price);
							checked_cnt ++;
						}
					break;
				}
			});
			
			JsonObjText = JsonObjText + ']}';
			
			if (checked_product_total_cnt == 0) {
				jQuery.unblockUI();
				alert('<?=JS_MSG_PLS_SELECT_THE_CHECKBOX?>');
			} else {
				jQuery().ajaxStop(jQuery.unblockUI);
				
				var SellingPackage = '';
				var Csv_Package = '';
				
				var file_content = jQuery("#store_record").html();
				
				if (jQuery(".gameProductSelected").length > 0) {
					var competitor_id = '';
					var competitor_price = '';
					var total_game_server = 0;
					var total_competitors_heading = 0;
					var heading_cnt = 0;
					var selling_total_competitors_heading = 0;
					
					total_game_server = jQuery(".gameProductSelected").length;
					
					if (isset(document.buyback_competitor_form.buyback_competitor_heading_column)) {
						if (document.buyback_competitor_form.buyback_competitor_heading_column.length == undefined) {
							total_competitors_heading = 1;
						} else {
							total_competitors_heading = document.buyback_competitor_form.buyback_competitor_heading_column.length;
						}
					}
					
					if (isset(document.selling_price_form.package_heading_cnt)) {
						if (document.selling_price_form.package_heading_cnt.length == undefined) {
							heading_cnt = 1;
						} else {
							heading_cnt = document.selling_price_form.package_heading_cnt.length;
						}
					}
					
					if (isset(document.selling_competitor_form.selling_competitor_heading_column)) {
						if (document.selling_competitor_form.selling_competitor_heading_column.length == undefined) {
							selling_total_competitors_heading = 1;
						} else {
							selling_total_competitors_heading = document.selling_competitor_form.selling_competitor_heading_column.length;
						}
					}
					
					var last_cnt = eval(total_game_server + '-' + 1);
					var game_product_id = '';
					var game_product_name = '';
					var game_suggested_price = '';
					var game_current_price = '';
					var game_overwrited_price = '';
					var total_buyback_competitors = '';
					var total_selling_competitors = '';
					var backorder_text = '';
					
					var selling_price_margin = '';
					var selling_suggested_price = '';
					var selling_current_price = '';
					var selling_overwrite_price = '';
					var selling_competitors_rank = '';
					
					var selling_competitor_id = '';
					var selling_competitor_price = 0;
					
					
					var selling_total_competitors = 0;
					
					var JsonBuyBackCompetitorData = '';
					var JsonSellingCompetitorData = '';
					var JsonGameInfoObj = '';
					var JsonObjServerName = '';
					var JsonObjBuybackCompetitorPrice = '';
					var JsonObjSellingCompetitorPrice = '';
					var JsonObjBuybackPrice = '';
					var JsonObjBackOrder = '';
					var JsonObjSellingPrice = '';
					var JsonSellingPackageHeadingData = '';
					var JsonObjSellingPackage = '';
					var JsonObjValueSelected = '';
					//var JsonServerSelected = '';
					
					JsonGameInfoObj = '{"GameInfoObj": [';
					JsonObjBuybackPrice = '{"BuybackPriceObj": [';
					JsonObjBackOrder = '{"BackOrderObj": [';
					JsonObjSellingPrice = '{"SellingPriceObj": [';
					//JsonObjSellingPackage = '{"SellingPackageObj": {';
					JsonObjBuybackCompetitorPrice = '{"BuybackCompetitorPriceObj": {';
					JsonObjSellingCompetitorPrice = '{"SellingCompetitorPriceObj": {';
					//////// BuyBackCompetitorName START
					if (total_competitors_heading > 0) {
						for (var competitor_cnt=0; competitor_cnt < total_competitors_heading; competitor_cnt++) {
							var competitor_name = jQuery("#buyback_competitor_heading_column_"+competitor_cnt).val();
							
							var last_heading_cnt = eval(total_competitors_heading + '-' + 1);
							
							if(competitor_cnt == 0) {
								JsonBuyBackCompetitorData += '{"buyback_competitors": "' + trim(competitor_name) + '"}';
							} else {
								JsonBuyBackCompetitorData += ',{"buyback_competitors": "' + trim(competitor_name) + '"}';
							}
						}
					}
					//////// BuyBackCompetitorName EOF
					
					//////// SellingCompetitorName START	
					if (selling_total_competitors_heading > 0) {
						for (var selling_competitor_cnt=0; selling_competitor_cnt < selling_total_competitors_heading; selling_competitor_cnt++) {
							var selling_competitor_name = jQuery("#selling_competitor_heading_column_"+selling_competitor_cnt).val();
							var selling_last_heading_cnt = eval(selling_total_competitors_heading + '-' + 1);
							
							if(selling_competitor_cnt == 0) {
								JsonSellingCompetitorData += '{"selling_competitors": "' + trim(selling_competitor_name) + '"}';
							} else {
								JsonSellingCompetitorData += ',{"selling_competitors": "' + trim(selling_competitor_name) + '"}';
							}
						}
					}
					//////// SellingCompetitorName EOF
					
					//////// SellingPackageHeading START
					if (heading_cnt > 0) {
						for (var package_heading_cnt=0; package_heading_cnt < heading_cnt; package_heading_cnt++) {
							package_qty = jQuery("#package_heading_"+package_heading_cnt).val();
							
							if (package_heading_cnt == 0) {
								JsonSellingPackageHeadingData += '{"package_qty" : "' + trim(package_qty) + '"}';
							} else {
								JsonSellingPackageHeadingData += ',{"package_qty" : "' + trim(package_qty) + '"}';
							}
						}
					}
					//////// SellingPackageHeading EOF
					
					//CSV HEADINGxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
					//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
					var csv_gap_num = '1';
					var csv_gap_string = ',';
					var csv_total_columns_num = '0';
					var csv_data = '';

					var csv_heading_server_name = "<?=CSV_HEADING_SERVER_NAME?>"
					var csv_heading_bb_competitor = "<?=CSV_HEADING_BUYBACK_COMPETITOR?>"
					var csv_heading_bb_setting = "<?=CSV_HEADING_BUYBACK_SETTING?>";
					var csv_heading_aging_bo_n_completed = "<?=CSV_HEADING_AGING_BO_AND_COMPLETED?>";
					var csv_heading_selling_setting = "<?=CSV_HEADING_SELLING_SETTING?>";
					var csv_heading_selling_competitor = "<?=CSV_HEADING_SELLING_COMPETITOR?>";
					var csv_heading_server_updated = "<?=CSV_HEADING_UPDATE?>";
					
					var csv_heading_server_name_size = '1';
					var csv_heading_bb_competitor_size = total_competitors_heading;
					var csv_heading_bb_setting_size = '3';
					var csv_heading_aging_bo_n_completed_size = '7';
					var csv_heading_selling_setting_size = '6';
					var csv_heading_selling_competitor_size = selling_total_competitors_heading;
					var csv_heading_server_updated_size = '1';
					
					csv_total_columns_num += eval(csv_heading_server_name_size + '+' + csv_gap);
					csv_total_columns_num += eval(csv_heading_bb_competitor_size + '+' + csv_gap);
					csv_total_columns_num += eval(csv_heading_bb_setting_size + '+' + csv_gap);
					csv_total_columns_num += eval(csv_heading_aging_bo_n_completed_size + '+' + csv_gap);
					csv_total_columns_num += eval(csv_heading_selling_setting_size + '+' + csv_gap);
					csv_total_columns_num += eval(csv_heading_selling_competitor_size + '+' + csv_gap);
					csv_total_columns_num += eval(csv_heading_server_updated_size + '+' + csv_gap);
					
					csv_data += str_repeat(',', csv_heading_server_name) + csv_gap_string;
					csv_data += csv_gap_string + str_repeat(',', csv_heading_bb_competitor) + csv_gap_string;
					csv_data += csv_gap_string + str_repeat(',', csv_heading_bb_setting) + csv_gap_string;
					csv_data += csv_gap_string + str_repeat(',', csv_heading_aging_bo_n_completed) + csv_gap_string;
					csv_data += csv_gap_string + str_repeat(',', csv_heading_selling_setting) + csv_gap_string;
					csv_data += csv_gap_string + str_repeat(',', csv_heading_selling_competitor) + csv_gap_string;
					csv_data += csv_gap_string + str_repeat(',', csv_heading_server_updated) + csv_gap_string;
					
					alert(csv_data);
					jQuery("#test").text(csv_data);
					//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
					//CSV HEADING EOFxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
					
					var product_cnt = 0;
					jQuery(".gameProductSelected").each (function() {
						var game_product_id = this.value;
						var JsonBuyBackCompetitorPrice = '';
						var JsonSellingCompetitorPrice = '';
						var JsonSellingPackage = '';
						
						game_product_name = jQuery("#product_setting_game_server_id_"+game_product_id).text();
						if (isset(document.buyback_competitor_form.buyback_competitor_id)) {
							if (document.buyback_competitor_form.buyback_competitor_id.length == undefined) {
								total_buyback_competitors = 1;
							} else {
								total_buyback_competitors = document.buyback_competitor_form.buyback_competitor_id.length;
							}
						}
						
						game_suggested_price = jQuery("#bb_suggested_price_text_"+game_product_id).text();
						game_current_price = jQuery("#bb_current_price_text_"+game_product_id).text();
						game_overwrited_price = jQuery("#overwrite_bb_price_"+game_product_id).val();
						
						backorder_days_0 = jQuery("#backorder_days_text_" + game_product_id + "_0").text();
						backorder_days_1 = jQuery("#backorder_days_text_" + game_product_id + "_1").text();
						backorder_days_2 = jQuery("#backorder_days_text_" + game_product_id + "_2").text();
						backorder_days_3 = jQuery("#backorder_days_text_" + game_product_id + "_3").text();
						backorder_days_4 = jQuery("#backorder_days_text_" + game_product_id + "_4").text();
						backorder_days_5 = jQuery("#backorder_days_text_" + game_product_id + "_5").text();
						backorder_days_6 = jQuery("#backorder_days_text_" + game_product_id + "_6").text();
						
						selling_price_margin = jQuery("#selling_price_margin_"+game_product_id).val();
						increment_val = jQuery("#increment_val_"+game_product_id).val();
						selling_suggested_price = jQuery("#selling_suggested_price_text_"+game_product_id).text();
						selling_current_price = jQuery("#selling_current_price_"+game_product_id).text();
						selling_overwrite_price = jQuery("#selling_overwrite_price_"+game_product_id).val();
						selling_competitors_rank = jQuery("#selling_competitors_rank_text_"+game_product_id).text();
						
						// Competitor's selling price
						selling_total_competitors = selling_total_competitors_heading;
						
						for (var selling_competitor_cnt=0; selling_competitor_cnt < selling_total_competitors; selling_competitor_cnt++) {
							selling_competitor_id = jQuery("#selling_competitor_id_"+selling_competitor_cnt).val();
							selling_competitor_price = jQuery("#selling_price_competitors_"+game_product_id+"_"+selling_competitor_id).val();
							
							if (selling_competitor_price == undefined) {
								selling_competitor_price = '';
							}
							
							if(selling_competitor_cnt == 0) {
								JsonSellingCompetitorPrice += '{"selling_competitors_price": "' + trim(selling_competitor_price) + '"}';
							} else {
								JsonSellingCompetitorPrice += ',{"selling_competitors_price": "' + trim(selling_competitor_price) + '"}';
							}
						}
						// Competitor's selling price EOF
						
						if (total_buyback_competitors > 0) {
							for (var competitor_cnt=0; competitor_cnt < total_buyback_competitors; competitor_cnt++) {
								competitor_id = jQuery("#buyback_competitor_id_"+competitor_cnt).val();
								competitor_price = jQuery("#bb_competitor_price_"+game_product_id+"_"+competitor_id).text();
								
								if(competitor_cnt == 0) {
									JsonBuyBackCompetitorPrice += '{"buyback_competitors_price": "' + trim(competitor_price) + '"}';
								} else {
									JsonBuyBackCompetitorPrice += ',{"buyback_competitors_price": "' + trim(competitor_price) + '"}';
								}
							}
						}
						
						var last_product_cnt = eval(total_game_server + '-' + 1);
						
						var check_selling_package_value = trim(jQuery("#package_table tr td").html());
						if (check_selling_package_value != '') {
							if (heading_cnt > 0) {
								var last_package_cnt = eval(heading_cnt + '-' + 1);
								for (var package_heading_cnt=0; package_heading_cnt < heading_cnt; package_heading_cnt++) {
									var package_price = document.selling_price_form.elements['package_quantity_value_'+game_product_id][package_heading_cnt].value;
									var package_id = document.selling_price_form.elements['package_id_'+game_product_id][package_heading_cnt].value;
								
									JsonObjSellingPackage += '["' + trim(package_id) + '" : "' + trim(package_price) + '"]';
									if (package_heading_cnt != last_product_cnt || package_heading_cnt != last_package_cnt) {
										JsonObjSellingPackage += ',';
									}
								}
							}
						}
						
						JsonObjBuybackPrice += '{"game_suggested_price": "' + trim(game_suggested_price) + '", "game_current_price": "' + trim(game_current_price) + '", "game_overwrited_price": "' + trim(game_overwrited_price) + '"}';
						JsonObjBackOrder += '{"backorder_days_0": "' + trim(backorder_days_0) + '", "backorder_days_1": "' + trim(backorder_days_1) + '", "backorder_days_2": "' + trim(backorder_days_2) + '", "backorder_days_3": "' + trim(backorder_days_3) + '", "backorder_days_4": "' + trim(backorder_days_4) + '", "backorder_days_5": "' + trim(backorder_days_5) + '", "backorder_days_6": "' + trim(backorder_days_6) + '"}';
						JsonObjSellingPrice += '{"selling_price_margin": "' + trim(selling_price_margin) + '", "increment_val": "' + trim(increment_val) + '", "selling_suggested_price": "' + trim(selling_suggested_price) + '", "selling_current_price": "' + trim(selling_current_price) + '", "selling_overwrite_price": "' + trim(selling_overwrite_price) + '", "selling_competitors_rank": "' + trim(selling_competitors_rank) + '"}';
						
						JsonObjBuybackCompetitorPrice += '"buyback_competitor_price_'+game_product_id+'": [' + trim(JsonBuyBackCompetitorPrice) + ']';
						JsonObjSellingCompetitorPrice += '"selling_competitor_price_'+game_product_id+'": [' + trim(JsonSellingCompetitorPrice) + ']';
						
						JsonObjServerName += '"game_product_id": "' + trim(game_product_id) + '", "game_product_name": "' + trim(game_product_name) + '"';
						JsonGameInfoObj += '{' + JsonObjServerName + '}';
						
						if (product_cnt != last_cnt) {
							JsonGameInfoObj += ',';
							JsonObjBuybackCompetitorPrice += ',';
							JsonObjSellingCompetitorPrice += ',';
							JsonObjBuybackPrice += ',';
							JsonObjBackOrder += ',';
							JsonObjSellingPrice += ',';
						}
						product_cnt ++;
					});
						
					JsonGameInfoObj += ']}';
					JsonObjBuybackPrice += ']}';
					JsonObjBackOrder += ']}';
					JsonObjSellingPrice += ']}';
					JsonObjBuybackCompetitorPrice += '}}';
					JsonObjSellingCompetitorPrice += '}}';
					
					JsonObjBuyBackCompetitor = '{"BuyBackCompetitorObj": [' + trim(JsonBuyBackCompetitorData) + ']}';
					
					JsonObjSellingCompetitor = '{"SellingCompetitorObj": [' + trim(JsonSellingCompetitorData) + ']}';
					
					JsonSellingPackageHeadingObj = '{"SellingPackageHeadingObj": [' + trim(JsonSellingPackageHeadingData) + ']}';
					
				}
			}
		}
	}
	
	function submitDrowpDownForm(doSubmit) {
		if (document.game_selection.game_cat_id.value==0) {
			alert("<?=JS_MSG_PLS_SELECT_THE_CATEGORY?>");
			return false;
		}
		
		if (doSubmit)	document.game_selection.submit();
	}
	
	function ClearFormJS(class_name) {
		jQuery('.'+class_name).clearForm();
	}
	
	function update_overwrite_all_price(input_value, class_name) {
		var overwrited_price = jQuery("#"+input_value).val();
		jQuery("."+class_name).val(overwrited_price);
	}
	
	function update_increment_value () {
		var increment_value = jQuery("#increment_value").val();
		jQuery(".increment_val").val(increment_value);
	}
	
	function CheckAllGameProduct() {
		
		if (document.products_info.game_product_id.length == undefined && document.products_info.game_product_id.value != '') {
			var game_product_id = document.products_info.game_product_id.value
			
			if(jQuery("#check_all").attr('checked') == true) {
				jQuery("#game_product_id_"+game_product_id).attr('checked', true);
				jQuery("#game_product_id_"+game_product_id).addClass('gameProductSelected')
			} else {
				jQuery("#game_product_id_"+game_product_id).attr('checked', false);
				jQuery("#game_product_id_"+game_product_id).removeClass('gameProductSelected')
			}
		}
		for (var product_cnt=0; product_cnt < document.products_info.game_product_id.length; product_cnt++) {
			var game_product_id = document.products_info.game_product_id[product_cnt].value;
			if(jQuery("#check_all").attr('checked') == true) {
				jQuery("#game_product_id_"+game_product_id).attr('checked', true);
				jQuery("#game_product_id_"+game_product_id).addClass('gameProductSelected')
			} else {
				jQuery("#game_product_id_"+game_product_id).attr('checked', false);
				jQuery("#game_product_id_"+game_product_id).removeClass('gameProductSelected')
			}
		}
	}
	
	function calculate_price_row (action, game_product_id, product_cont_cnt) {
		
		var cat_id = '<?=$main_cat_id?>';
		if (action == 'batch_update') {
			
			var increment_value = jQuery("#increment_value").val();
			if (increment_value == '' || increment_value == undefined) {
				alert('<?=ENTRY_SELLING." ".JS_MSG_INCREMENT_FIELD_CANNOT_BE_BLANK?>');
				jQuery.unblockUI();
			} else {
					if (product_cont_cnt == '' || product_cont_cnt == undefined) {
						product_cont_cnt = 0;
					}
					var game_product_id_arr = new Array();
					if (document.products_info.game_product_id.length == undefined) {
						var game_product_id = document.products_info.game_product_id.value;
						game_product_id_arr.push(game_product_id);
					} else {
						for (var product_cnt = product_cont_cnt; product_cnt < document.products_info.game_product_id.length; product_cnt++) {
							var game_product_id = document.products_info.game_product_id[product_cnt].value;
							game_product_id_arr.push(game_product_id);
						}
					}
					
					//listing_package_heading (action, cat_id, game_product_id_arr);
					listing_package(action, cat_id, game_product_id_arr);
				//}
			}
		} else {
			//var check_selling_package_value = trim(jQuery("#package_table tr td").html());
			if(jQuery("#checking_package_table").val() == 'got_package_product') {
				
			//if (check_selling_package_value != '') {
				var increment_value = jQuery("#increment_val_"+game_product_id).val();
				var unit_price = document.selling_price_form.elements['selling_overwrite_price_'+game_product_id].value;
				
				if (increment_value == '' || increment_value == undefined) {
					alert('<?=ENTRY_SELLING." ".JS_MSG_INCREMENT_FIELD_CANNOT_BE_BLANK?>');
				} else {
					if (unit_price == undefined || unit_price == '') {
						alert('<?=ENTRY_SELLING." ".JS_MSG_OVERWRITE_FIELD_CANNOT_BE_BLANK?>');
					} else {
						var num_packages = packages_products_id_arr[game_product_id].length;
						var package_id = '';
						var weight = 1;
						var package_qty = '';
						var package_qty_arr = new Array();
						
						for (var package_cnt = num_packages-1; package_cnt >= 0; package_cnt--) {
							//weight = eval(num_packages + '-' + package_cnt);
							package_qty = document.selling_price_form.elements['package_quantity_'+game_product_id][package_cnt].value;
							package_id = document.selling_price_form.elements['package_id_'+game_product_id][package_cnt].value;
							package_price =  get_package_price(unit_price, weight, package_qty, increment_value);
							
							if (!in_array(package_qty, package_qty_arr)) {
								//if (package_price != 'NaN' && package_price != '' && package_price > 0) {
								if (validateMayHvPtDecimal(package_price)) {
									document.getElementById('package_price_'+game_product_id+'_'+packages_products_id_arr[game_product_id][package_cnt]).value = package_price;
									jQuery("#package_quantity_"+package_id).val(package_price);
									weight = eval(weight + '+' + 1);
								}
							} else {
								var temp_weight = eval(weight + '-' + 1);
								package_price =  get_package_price(unit_price, temp_weight, package_qty, increment_value);
								if (validateMayHvPtDecimal(package_price)) {
									document.getElementById('package_price_'+game_product_id+'_'+packages_products_id_arr[game_product_id][package_cnt]).value = package_price;
									jQuery("#package_quantity_"+package_id).val(package_price);
								}
							}
							package_qty_arr.push(package_qty);
						}
					}
				}
			}
			jQuery.unblockUI();
		}
	}

	function listing_package (action, cat_id, game_product_id_arr, product_qty_arr, split_product_id_cnt) {
		if (action == 'batch_update') {
			var product_id_arr = new Array();
			var split_product_id_arr = new Array();
			var product_qty = '';
			var product_id = '';
			
			
			if(is_array(game_product_id_arr)) {
				product_id_arr = game_product_id_arr;
			} else {
				product_id_arr = game_product_id_arr.split(',');
			}
			
			if (split_product_id_cnt == '' || split_product_id_cnt == undefined) {
				split_product_id_cnt = 0;
			}
			
			if (product_qty_arr == undefined || product_qty_arr == '') {
				var product_qty_arr = '';
				var package_heading_length = document.selling_price_form.package_heading_cnt.length;
			
				for (var package_qty_cnt = 0; package_qty_cnt < package_heading_length; package_qty_cnt++) {
					package_qty = jQuery("#package_heading_"+package_qty_cnt).val();
					product_qty_arr += package_qty;
					if (eval(package_qty_cnt + '+' + 1) != package_heading_length) {
						product_qty_arr += '#';
					}
				}
			} else {
				product_qty_arr = product_qty_arr;
			}
			
			var store_in_arr_cnt = 0;
			for (var product_id_cnt = 0; product_id_cnt < product_id_arr.length; product_id_cnt++) {
				if ((product_id_cnt % 100) == 0) {
					if (store_in_arr_cnt == 0) {
						unit_price = jQuery("#selling_overwrite_price_"+product_id_arr[product_id_cnt]).val();
						product_id = product_id_arr[product_id_cnt];
					} else {
						unit_price = unit_price + '|' + jQuery("#selling_overwrite_price_"+product_id_arr[product_id_cnt]).val();
						product_id = product_id + '|' + product_id_arr[product_id_cnt];
					}
					store_in_arr_cnt ++;
				} else {
					unit_price = unit_price + ',' + jQuery("#selling_overwrite_price_"+product_id_arr[product_id_cnt]).val();
					product_id = product_id + ',' + product_id_arr[product_id_cnt];
				}
			}
			
			split_product_id_arr = product_id.split('|');
			if (split_product_id_cnt == undefined || split_product_id_cnt == '') {
				var split_product_id = 0;
			} else {
				var split_product_id = split_product_id_cnt;
			}
			
			if (split_product_id_arr.length > split_product_id) {
				// After called display_package_price() will recall the listing_package();
				var t = setTimeout("display_package_price('"+cat_id+"', '"+product_id_arr+"', '"+product_qty_arr+"', '"+split_product_id_cnt+"');",3000);
			} else {
				jQuery.unblockUI();
			}
		}
	}
	
	function display_package_price (cat_id, product_id_arr, product_qty_arr, split_product_id_cnt) {
		product_id_arr = product_id_arr.split(',');
		var store_in_arr_cnt = 0;
		for (var product_id_cnt = 0; product_id_cnt < product_id_arr.length; product_id_cnt++) {
			if ((product_id_cnt % 100) == 0) {
				if (store_in_arr_cnt == 0) {
					unit_price = jQuery("#selling_overwrite_price_"+product_id_arr[product_id_cnt]).val();
					product_id = product_id_arr[product_id_cnt];
				} else {
					unit_price = unit_price + '|' + jQuery("#selling_overwrite_price_"+product_id_arr[product_id_cnt]).val();
					product_id = product_id + '|' + product_id_arr[product_id_cnt];
				}
				store_in_arr_cnt ++;
			} else {
				unit_price = unit_price + ',' + jQuery("#selling_overwrite_price_"+product_id_arr[product_id_cnt]).val();
				product_id = product_id + ',' + product_id_arr[product_id_cnt];
			}
		}
		
		if (split_product_id_cnt == 0) {
			jQuery("#package_table tr").remove();
			jQuery("#checking_package_table").val('');
		}
		split_product_id_arr = product_id.split('|');
		split_unit_price_arr = unit_price.split('|');
		
		var increment_value = jQuery("#increment_value").val();
		jQuery.post("price_automation_xmlhttp.php?cat_id="+cat_id+"&action=display_package_product&increment_value="+increment_value, {product_id_arr : split_product_id_arr[split_product_id_cnt], product_qty_arr : product_qty_arr, split_unit_price_arr : split_unit_price_arr[split_product_id_cnt]},function(data){
			jQuery("#package_table").append(data);
			jQuery("#checking_package_table").val('got_package_product');
		});
		split_product_id_cnt = eval(split_product_id_cnt + '+' + 1);
		listing_package('batch_update', cat_id, product_id_arr, product_qty_arr, split_product_id_cnt);
	}
	
	function get_package_price(unit_price, weight, subproduct_qty, increment_value) {
        if (increment_value.substring(increment_value.length, (increment_value.length-1)) == '%') {
            var price_to_use = (parseFloat(unit_price) * (Math.pow((1 + parseFloat(increment_value.substring(0, (increment_value.length-1)) / 100)), (parseInt(weight)-1)))) * parseInt(subproduct_qty);
        } else {
            var price_to_use = (((parseInt(weight)-1) * parseFloat(increment_value)) + parseFloat(unit_price)) * parseInt(subproduct_qty);
        }
        return price_to_use.toFixed(6);
	}
	
	function ShowHistoryFile () {
		var history_date = jQuery("#history_date").val();
		var type = '';
		var cat_id = '<?=$main_cat_id?>';
		
		if (history_date == '') {
			alert('<?=JS_MSG_PLS_SELECT_THE_HISTORY_DATE?>');
		} else {
			if(jQuery("#history_csv_format").attr('checked') == true) {
				type = jQuery("#history_csv_format").val();
			} else if(jQuery("#history_html_format").attr('checked') == true) {
				type = jQuery("#history_html_format").val();
			}
			
			jQuery.get("price_automation_xmlhttp.php?cat_id="+cat_id+"&action=get_history_list&type="+type+"&history_date="+history_date, function(data){
				jQuery("#display_history_files").html(data).fadeIn('slow');
			});
		}
	}
	
	function DownloadHistory (type, file_location) {
		var cat_id = '<?=$main_cat_id?>';
		switch (type) {
			case 'csv':
				location.href = '?cat_id='+cat_id+'&action=export_csv&file_name='+file_location;
				break;
			case 'html':
				newwindow=window.open(file_location,'History','');
				if (window.focus) {newwindow.focus()}
				break;
		}
	}
	
	function storeCheckboxValue (game_product_id) {
		if(jQuery("#game_product_id_"+game_product_id).attr('checked') == true) {
			jQuery("#game_product_id_"+game_product_id).addClass('gameProductSelected')
		} else {
			jQuery("#game_product_id_"+game_product_id).removeClass('gameProductSelected')
		}
	}
	
	function price_records (type, id, value) {
		jQuery("#"+id).text(value);
		jQuery("#"+id).val(value);
	}
	
	function export_selling_price (path) {
		document.selling_price_form.action = path;
		document.selling_price_form.submit();
	}
	
	function export_buyback_price (path) {
		document.buyback_price_form.action = path;
		document.buyback_price_form.submit();
	}
	
</script>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</div>
<?php
	echo '<div align="center" style="color:red">Memory Usage:<b> '.memory_get_usage() .' bytes</b></div>';
?>
<div id="test"></div>
<div id="csv_content" class="hide"></div>
</body>
</html>