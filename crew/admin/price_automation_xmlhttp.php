<?php
header("Expires: Mon, 02 May 2001 23:00:00 GMT");
header("Cache-Control: no-store, no-cache, must-revalidate");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'price_automation.php');
require_once(DIR_WS_CLASSES . 'json.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'probabilistic.php');
require_once(DIR_WS_LANGUAGES . $language . '/price_automation.php');

tep_set_time_limit(0);

$main_cat_id = (int)$_GET['cat_id'];
$bb_rank = isset($_GET['bb_rank'])? $_GET['bb_rank'] : '';
$row_count = 0;
$split_bb_competitor_selected = isset($_GET['bb_competitor_selected']) ? split_dep(',', $_GET['bb_competitor_selected']) : '';

$min_row_height = 'height:38px;';

if ($main_cat_id > 0) {
    $json = new Services_JSON();
    //$json->use = SERVICES_JSON_LOOSE_TYPE;
}

if (tep_not_null($_GET['action'])) {
	switch ($_GET['action']) {
		case 'buyback_price_setting':
			//$filter_PriceAutomationObj = str_replace('\\"', '"', $_POST['PriceAutomationObj']);
			$filter_BB_competitors_priceObj = str_replace('\\"', '"', $_POST['BB_competitors_priceObj']);
			$filter_BB_productsObj = str_replace('\\"', '"', $_POST['BB_productsObj']);
			$filter_PA_bb_total_productsObj = str_replace('\\"', '"', $_POST['PA_bb_total_productsObj']);
			
			
    		//$PriceAutomationObj = $json->decode($filter_PriceAutomationObj);
    		$BB_competitors_priceObj = $json->decode($filter_BB_competitors_priceObj);
    		$BB_productsObj = $json->decode($filter_BB_productsObj);
    		$PA_bb_total_productsObj = $json->decode($filter_PA_bb_total_productsObj);
    		
    		
    		$bcps_row = $PA_bb_total_productsObj;
			$suggested_price_arr = array();
			$suggested_price = '';
			$avg_suggested_price = '';
			
			$suggested_price_arr = calculate_suggested_price ($BB_competitors_priceObj, $split_bb_competitor_selected, $bb_rank);
			$avg_suggested_price = round(array_average($suggested_price_arr), 6);
			
    		if (count($BB_productsObj) > 0) {
				foreach ($BB_productsObj as $game_product_id => $game_info) {
					$suggested_price = round($suggested_price_arr[$game_product_id], 6);
					$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
					++$row_count;
					$get_buyback_price_select_sql = "	SELECT buyback_price, products_id 
														FROM " . TABLE_AUTOMATE_BUYBACK_PRICE . " 
														WHERE products_id = '" . tep_db_input($game_product_id)  . "'";
					$get_buyback_price_result_sql = tep_db_query($get_buyback_price_select_sql);
					$get_buyback_price_row = tep_db_fetch_array($get_buyback_price_result_sql);
					
					echo '<tr id="bb_price_game_server_id_'.$game_product_id.'" width="450" align="center" style="'.$min_row_height.'" class="'.$row_style.'" onmouseover="rowOverEffect(this, \'PriceAutomation_RowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\');">
					    	<td id="bb_suggested_price_text_'.$game_product_id.'" style="min-width:150px" class="ordersRecords" align="center">' . $suggested_price . tep_draw_hidden_field('bb_suggested_price['.$game_product_id.']', ''.$suggested_price.'', 'id="bb_suggested_price_'.$game_product_id.'"') .'</td>
				    		<td id="bb_current_price_text_'.$game_product_id.'" style="min-width:150px" class="ordersRecords">' . $get_buyback_price_row['buyback_price'] . '</td>
					      	<td style="min-width:150px" class="ordersRecords">'.tep_draw_input_field('buyback_overwrite_price['.$game_product_id.']', ''.$suggested_price.'', 'size=10 class="overwrite_buyback" id="overwrite_bb_price_'.$game_product_id.'" onchange="price_records(\'buyback\', this.id, this.value)" onKeyPress="return noEnterKey(event)" onKeyUP="if (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value))) { this.value = \'\'; }"').'</td>
					    </tr>';
				}
				echo '<tr width="450" align="center" style="'.$min_row_height.'" class="averageRow">
						<td id="average_buyback_suggested_price">'.$avg_suggested_price.'</td>
						<td>&nbsp;</td>
						<td id="average_buyback_overwrited_price">&nbsp;</td>
					  </tr>';
			}
?>
<?php
			break;
		case 'ajax_upload':
			if (isset($_GET['file_name'])) {
				$error = "";
				$fileElementName = $_GET['file_name'];
				if (!empty($_FILES[$fileElementName]['error'])) {
					switch($_FILES[$fileElementName]['error']) {
						case '1':
							$error = 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
							break;
						case '2':
							$error = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
							break;
						case '3':
							$error = 'The uploaded file was only partially uploaded';
							break;
						case '4':
							$error = 'No file was uploaded.';
							break;
						case '6':
							$error = 'Missing a temporary folder';
							break;
						case '7':
							$error = 'Failed to write file to disk';
							break;
						case '8':
							$error = 'File upload stopped by extension';
							break;
						case '999':
						default:
							$error = 'No error code avaiable';
					}
				} elseif (empty($_FILES[$fileElementName]['tmp_name']) || $_FILES[$fileElementName]['tmp_name'] == 'none') {
					$error = 'No file was uploaded..';
				} else {
					header ("content-type: text/xml");

					echo '<?xml version="1.0" encoding="utf-8"?>';

					$filename = ($_FILES[$fileElementName]['tmp_name']);
				    $handle = fopen($filename, 'r+');
				    
				    $overallRowNo = 0;
				    $rowNo = 0;  
				    
				    $game_product_id = '';
				    $suggested_price = '';
				    $current_price = '';
				    $overwrited_priced = '';
				    
					echo '<buyback_price>';

				    while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
				     	$game_product_id = $data[0];
				    	$suggested_price = $data[2];
					    $current_price = '';
					    $overwrited_priced = $data[3];
				    	 
				    	$num_cols = count($data);
 
				    	if ($rowNo > 0) {
					    	echo '<buyback_price_info>';
 
					    	echo '	<game_product_id>'.$game_product_id.'</game_product_id>';
				    		echo '	<suggested_price>'.$suggested_price.'</suggested_price>';
					    	echo '	<current_price>1</current_price>';
					    	echo '	<overwrited_price>'.$overwrited_priced.'</overwrited_price>';
 
					    	echo '</buyback_price_info>';
 
						} 
						$rowNo ++;
			    	}
			    	echo '</buyback_price>';
					
					//for security reason, we force to remove all uploaded file
					@unlink($_FILES[$fileElementName]);
				}
			}
			break;
 		 
 		 case 'display_package_product':
 		 	$JSHTML = '';
 		 	
 		 	$product_id = $_POST['product_id_arr'];
 		 	$product_qty = $_POST['product_qty_arr'];

 		 	$unit_price = $_POST['split_unit_price_arr'];
 		 	$increment_value = $_GET['increment_value'];
 		 	
 		 	if (tep_not_null($product_id)) {
 		 		$product_id_arr = explode(",", $product_id);
 		 	}
 		 	
 		 	if (tep_not_null($product_qty)) {
 		 		$product_qty_arr = explode("#", $product_qty);
 		 		$unique_product_qty_arr = array_unique($product_qty_arr);
 		 		$qty_array = array();
 		 		foreach ($product_qty_arr as $product_qty) {
 		 			if (isset($qty_array[$product_qty])) {
 		 				$qty_array[$product_qty] = $qty_array[$product_qty] + 1;
 		 			} else {
 		 				$qty_array[$product_qty] = 1;
 		 			}
 		 		}
 		 	}
 		 	
 		 	if (tep_not_null($unit_price)) {
 		 		$unit_price_arr = explode(",", $unit_price);
 		 	}
 		 	
 		 	$row_count = 0;
			$row_cnt = 0;
			
 		 	foreach ($product_id_arr as $product_id) {
 		 		$unit_price = $unit_price_arr[$row_count];
 		 		$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
				$row_cnt = $row_count;
				++$row_count;
				
				// for everytime recalculate will redefined the packages_products_id_arr[] array;									
				$JSHTML .= "packages_products_id_arr['".$product_id."'] = new Array();";
 		 		echo '<tr class="'.$row_style.'" style="'.$min_row_height.'" id="package_row_'.$product_id.'">
 		 				<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
							<tr style="'.$min_row_height.'" id="selling_price_row_'.$product_id.'">';
				
				
				$weight = 1;
				$temp_output_html_array = array();
				$temp_output_js_array = array();
				
				arsort($unique_product_qty_arr);
				foreach ($unique_product_qty_arr as $product_qty) {
					$package_product_select_sql = "	SELECT pb.subproduct_id, pb.bundle_id, pb.subproduct_qty, p.products_price 
													FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb 
													LEFT JOIN " . TABLE_PRODUCTS . " AS p 
														ON (pb.bundle_id = p.products_id) 
													WHERE pb.subproduct_id = '" . tep_db_input($product_id) . "' 
														AND pb.subproduct_qty = '" . tep_db_input($product_qty) . "'
													ORDER BY p.products_sort_order DESC";
					$package_product_result_sql = tep_db_query($package_product_select_sql);
					$package_product_num = tep_db_num_rows($package_product_result_sql);
					
					if ($package_product_num > 0) {
						$product_qty_arr = array();
						
						for($qty_cnt = 0; $qty_cnt < $qty_array[$product_qty]; $qty_cnt++) {
							if ($package_product_row = tep_db_fetch_array($package_product_result_sql)) {
								$temp_output_js_array[] = "packages_products_id_arr[".$product_id."].push('".$package_product_row['bundle_id']."'); \n";
								
								if (!in_array($product_qty, $product_qty_arr)) {
									$package_price = tep_xmlhttp_get_package_price($unit_price, $weight, $package_product_row['subproduct_qty'], $increment_value); 
									$temp_output_html_array[] = '<td width="100" align="right" style="font-size:10px;">'.$package_product_row['products_price'].'<br />'.tep_draw_input_field('package_price['.$product_id.']['.$package_product_row['bundle_id'].']', round($package_price, 6), 'id="package_price_'.$product_id.'_'.$package_product_row['bundle_id'].'" size="13" onkeypress="return noEnterKey(event)" onchange="price_records(\'package\', this.id, this.value)"').'</td>' .
																tep_draw_hidden_field('package_quantity_'.$product_id, $package_product_row['subproduct_qty'], '') .
																tep_draw_hidden_field('package_id_'.$product_id, $package_product_row['bundle_id'], 'id="package_id_'.$product_id.'" class="package_id_'.$product_id.'"');
									$weight += 1;
								} else {
									$package_price = tep_xmlhttp_get_package_price($unit_price, $weight-1, $package_product_row['subproduct_qty'], $increment_value); 
									$temp_output_html_array[] = '<td width="100" align="right" style="font-size:10px;">'.$package_product_row['products_price'].'<br />'.tep_draw_input_field('package_price['.$product_id.']['.$package_product_row['bundle_id'].']', round($package_price, 6), 'id="package_price_'.$product_id.'_'.$package_product_row['bundle_id'].'" size="13" onkeypress="return noEnterKey(event)" onchange="price_records(\'package\', this.id, this.value)"').'</td>' .
																tep_draw_hidden_field('package_quantity_'.$product_id, $package_product_row['subproduct_qty'], '') .
																tep_draw_hidden_field('package_id_'.$product_id, $package_product_row['bundle_id'], 'id="package_id_'.$product_id.'" class="package_id_'.$product_id.'"');
								}
								$product_qty_arr[] = $product_qty;
							} else {
								$temp_output_html_array[] = '<td width="100" align="center" style="font-size:10px;">&nbsp;N/A&nbsp;</td>' .
													tep_draw_hidden_field('package_id_'.$product_id, '', 'id="package_id_'.$product_id.'" class="package_id_'.$product_id.'"');	
							}
						}
					} else {
						for($qty_cnt = 0; $qty_cnt < $qty_array[$product_qty]; $qty_cnt++) {
							$temp_output_html_array[] = '<td width="100" align="center" style="font-size:10px;">&nbsp;N/A&nbsp;</td>' .
													tep_draw_hidden_field('package_id_'.$product_id, '', 'id="package_id_'.$product_id.'" class="package_id_'.$product_id.'"');	
						}
					}
				}
 		 		
 		 		krsort($temp_output_html_array);
 		 		echo implode("\n", $temp_output_html_array);
 		 		unset($temp_output_html_array);
 		 		
 		 		krsort($temp_output_js_array);
 		 		$JSHTML .= implode("\n", $temp_output_js_array);
 		 		unset($temp_output_js_array);
 		 		
 		 		echo '		</tr>
 		 					</table>
 		 				</td>
 		 			</tr>';
 		 	}
			echo '<script type="text/javascript">' . $JSHTML . '</script>';
			
 		 	break;
		 case 'update_current_price': 
		 	$type = $_GET['type'];
		 	
		 	
		 	
		 	if (!tep_not_null($type)) { die(); }
		  	
		  	$update_success = false;
		  	$categories_id = $_GET['cat_id'];
		  	$selected_pid_array = array();
		  	
			switch ($type) {
				case 'selling':
					if (tep_not_null($_REQUEST['selling_hidden_pid_content'])) {
				  		$selected_pid_array = explode(',', substr($_REQUEST['selling_hidden_pid_content'], 0, -1));
						$total_selected_product = count($selected_pid_array);
						for ($p_cnt=0; $p_cnt < $total_selected_product; $p_cnt++) {
							$product_id = $selected_pid_array[$p_cnt];
							if (isset($_REQUEST['selling_overwrite_price'][$product_id]) && $_REQUEST['selling_overwrite_price'][$product_id] > 0) {
								// Selling Single Product
				  				$update_package_price_sql = "	UPDATE " . TABLE_PRODUCTS . " 
				 												SET products_price = '". tep_db_input($_REQUEST['selling_overwrite_price'][$product_id]) ."',
				 													products_last_modified = now()
				 												WHERE products_id = '" . tep_db_input($product_id) . "'";
								tep_db_query($update_package_price_sql);
								$update_success = true;
							}
							
							if (isset($_REQUEST['package_price'][$product_id]) && is_array($_REQUEST['package_price'][$product_id])) {
								// Selling Package Product
						  		foreach ($_REQUEST['package_price'][$product_id] as $bundle_id => $price) {
					  				$update_package_price_sql = "	UPDATE " . TABLE_PRODUCTS . " 
					 												SET products_price = '". tep_db_input($price) ."',
					 													products_last_modified = now()
					 												WHERE products_id = '" . tep_db_input($bundle_id) . "'";
									tep_db_query($update_package_price_sql);
									$update_success = true;
						  		}
							}
						}
				  	}
				  	$FileContent = $_REQUEST['selling_hidden_html_content'];
				  	$FileContentCSV = $_REQUEST['selling_csv_content'];
					break;
				case 'buyback':
					if (tep_not_null($_REQUEST['bb_hidden_pid_content'])) {
				  		$selected_pid_array = explode(',', substr($_REQUEST['bb_hidden_pid_content'], 0, -1));
				  		
						$total_selected_product = count($selected_pid_array);
						for ($p_cnt=0; $p_cnt < $total_selected_product; $p_cnt++) {
							$product_id = $selected_pid_array[$p_cnt];
							echo $product_id.'<br/>';
							if (isset($_REQUEST['buyback_overwrite_price'][$product_id]) && $_REQUEST['buyback_overwrite_price'][$product_id] > 0) {
								$define_query_type_sql = "	SELECT products_id 
				 	 										FROM " . TABLE_AUTOMATE_BUYBACK_PRICE . " 
				 	 										WHERE products_id = '" . tep_db_input($product_id) . "'";
				 	 			$define_query_type_result_sql = tep_db_query($define_query_type_sql);
				 	 			if (tep_db_num_rows($define_query_type_result_sql) > 0) {
				 	 				$update_buyback_price_sql = "	UPDATE " . TABLE_AUTOMATE_BUYBACK_PRICE . " 
				 	 												SET buyback_price = '". tep_db_input($_REQUEST['buyback_overwrite_price'][$product_id]) ."',
				 	 													last_modified = now(),
				 	 													last_modified_by = '" . tep_db_input($_SESSION['login_email_address']) . "' 
				 	 												WHERE products_id = '" . tep_db_input($product_id) . "'";
				 	 				tep_db_query($update_buyback_price_sql);
				 	 				$update_success = true;
				 	 			} else {
				 	 				$insert_buyback_price_sql = "	INSERT INTO " . TABLE_AUTOMATE_BUYBACK_PRICE . " 
				 	 												SET products_id = '". tep_db_input($product_id) ."',
				 	 													buyback_price = '". tep_db_input($_REQUEST['buyback_overwrite_price'][$product_id]) ."',
				 	 													last_modified = now(),
				 	 													date_added =now(),
				 	 													last_modified_by = '" . tep_db_input($_SESSION['login_email_address']) . "'";
				 	 				tep_db_query($insert_buyback_price_sql);
				 	 				$update_success = true;
				 	 			}
							}
						}
				  	}
				  	$FileContent = $_REQUEST['bb_hidden_html_content'];
				  	$FileContentCSV = $_REQUEST['buyback_csv_content'];
					break;
			}
		  	
	 	 	if ($update_success && tep_not_null($FileContentCSV)) {
	 	 		if (!is_dir("download/csv/".$categories_id."/")) {
					mkdir("download/csv/".$categories_id."/", 0755);
				}
				
				$filename_csv = $type.'_'.date('YmdHis').'.csv';
				$file_location_csv = 'download/csv/'.$categories_id.'/'.$filename_csv;
				
				if (!$handle = fopen($file_location_csv, 'w')) {
				     exit;
				}
				
				// Write to our opened file.
				if (fwrite($handle, $FileContentCSV) === FALSE) {
					fclose($handle);
				    exit;
				}
				fclose($handle);
	 	 	}

	 	 	if ($update_success && tep_not_null($FileContent)) {
// HTML History
	 	 		$FileContentHTML = generate_history_html ($FileContent); //$ServerInfoObj
				
				if (tep_not_null($FileContent)) {
					if (!is_dir("download/html/".$categories_id."/")) {
						mkdir("download/html/".$categories_id."/", 0755);
					}
					
					$filename_html = $type.'_'.date('YmdHis').'.html';
					$file_location_html = 'download/html/'.$categories_id.'/'.$filename_html;
					
					if (!$handle = fopen($file_location_html, 'w')) {
					     exit;
					}
					
					// Write to our opened file.
					if (fwrite($handle, $FileContentHTML) === FALSE) {
						fclose($handle);
					    exit;
					}
					fclose($handle);
				}
				unset($FileContentHTML);
// HTML History EOF

// CSV History
				//$FileContentCSV = generate_csv_history ($ServerInfoObj, $BuybackCompetitorInfoObj, $BuybackPriceInfoObj, $BackOrderInfoObj, $SellingPriceInfoObj, $SellingCompetitorInfoObj, $CheckedValueInfoObj, $Csv_PackageInfoObj);
				//$FileContentCSV = generate_csv_history ($GameInfoCsv, $BuybackPriceCsv, $BackOrderCsv, $BuyBackCompetitorCsv, $SellingPriceCsv, $SellingCompetitorCsv, $SellingPackageHeadingCsv, $BuybackCompetitorPriceCsv, $SellingCompetitorPriceCsv, $SellingPackageCsv);
				
//				if (!is_dir("download/csv/".$categories_id."/")) {
//					mkdir("download/csv/".$categories_id."/", 0755);
//				}
//				
//				$filename_csv = $type.'_'.date('YmdHis').'.csv';
//				$file_location_csv = 'download/csv/'.$categories_id.'/'.$filename_csv;
//				
//				if (!$handle = fopen($file_location_csv, 'w')) {
//				     exit;
//				}
//				
//				// Write to our opened file.
//				if (fwrite($handle, $FileContentCSV) === FALSE) {
//					fclose($handle);
//				    exit;
//				}
//				fclose($handle);
// CSV History EOF
	 	 	}
		break;
		
		case 'history':
			$type = $_REQUEST['type'];
		 	$categories_id = $_REQUEST['cat_id'];
		 	
		 	if (!tep_not_null($type)) { die(); }

			$FileContent = $_REQUEST['csv_content'];
			
			if (tep_not_null($FileContent)) {
				if (!is_dir("download/csv/".$categories_id."/")) {
					mkdir("download/csv/".$categories_id."/", 0755);
				}
				
				$filename_html = $type.'_'.date('YmdHis').'.csv';
				$file_location_html = 'download/csv/'.$categories_id.'/'.$filename_html;
				
				if (!$handle = fopen($file_location_html, 'w')) {
				     exit;
				}
				
				// Write to our opened file.
				if (fwrite($handle, $FileContent) === FALSE) {
					fclose($handle);
				    exit;
				}
				fclose($handle);
			}
			unset($FileContent);

		break;
		
		case 'get_history_list':
			if (tep_not_null($_GET['type']) && tep_not_null($_GET['history_date'])) {
				$type = $_GET['type'];
				$history_date = $_GET['history_date'];
				$categories_id = $_GET['cat_id'];
				$match_date = '';
				
				switch ($type) {
					case 'csv':
						$dir = 'download/csv/'.$categories_id.'/';
						$num_record = 0;
						if (is_dir($dir)) {
						    if ($dh = opendir($dir)) {
						    	$file_name_arr = array();
						    	
						    	echo '<div style="border:dotted 3px silver;font-size:15px;padding:10px;">';
						        while (($file = readdir($dh)) !== false) {
						        	if (is_file($dir.$file)) {
							        	list($filename_section, $ext_section) =  explode('.', $file);
							        	$yr = substr($filename_section, -14, 4);
							        	$mth = substr($filename_section, -10, 2);
							        	$day = substr($filename_section, -8, 2);
							        	$hr = substr($filename_section, -6, 2);
							        	$min = substr($filename_section, -4, 2);
							        	$sec = substr($filename_section, -2, 2);
							        	
							        	$created_date = date("Y-m-d H:i:s", mktime($hr, $min, $sec, $mth, $day, $yr));
							        	$file_name_arr[$created_date] = $file;
							        }
						        }
						        
						        krsort($file_name_arr);
						        if (count($file_name_arr) > 0) {
						        	foreach ($file_name_arr as $file_date => $file_name) {
						        		$file_type = substr(strrchr($file_name, '.'), 1);
							        	if ($file_type == 'csv') {
						        			$file_created_date_arr = explode("-", $file_date);
							        		$match_date = date("Y-m-d", mktime (0,0,0,$file_created_date_arr[1],$file_created_date_arr[2],$file_created_date_arr[0]));
							        		if ($history_date == $match_date) {
							        			$num_record ++;
							        			echo '<div style="padding:2px;">'.$num_record.') '.$file_name.' | <b>'.$file_date.' </b>| '.
								        				tep_button('View', '', '', 'onClick="DownloadHistory(\'csv\',\''.$file_name.'\')"', 'InputButton').
								        			  '</div>';
											}
							        	}
						        	}
						        } else {
						        	echo '<div style="padding:2px;"><b>'.TEXT_NO_RECORD.' </b></div>';
						        }
						        
						        if ($num_record == 0) { echo 'No Record!'; }
						        echo '</div>';
						        closedir($dh);
						    }
						}
						break;
						
					case 'html':
						$dir = 'download/html/'.$categories_id.'/';
						$num_record = 0;
						if (is_dir($dir)) {
						    if ($dh = opendir($dir)) {
						    	$file_name_arr = array();
						    	echo '<div style="border:dotted 3px silver;font-size:15px;padding:10px;">';
						        while (($file = readdir($dh)) !== false) {
						        	if (is_file($dir.$file)) {
							        	list($filename_section, $ext_section) =  explode('.', $file);
							        	$yr = substr($filename_section, -14, 4);
							        	$mth = substr($filename_section, -10, 2);
							        	$day = substr($filename_section, -8, 2);
							        	$hr = substr($filename_section, -6, 2);
							        	$min = substr($filename_section, -4, 2);
							        	$sec = substr($filename_section, -2, 2);
							        	
							        	$created_date = date("Y-m-d H:i:s", mktime($hr, $min, $sec, $mth, $day, $yr));
							        	$file_name_arr[$created_date] = $file;
							        }
						        }
						        
						        krsort($file_name_arr);
						        if (count($file_name_arr) > 0) {
						        	foreach ($file_name_arr as $file_date => $file_name) {
						        		$file_type = substr(strrchr($file_name, '.'), 1);
							        	if ($file_type == 'html') {
						        			$file_created_date_arr = explode("-", $file_date);
							        		$match_date = date("Y-m-d", mktime (0,0,0,$file_created_date_arr[1],$file_created_date_arr[2],$file_created_date_arr[0]));
							        		if ($history_date == $match_date) {
							        			$num_record ++;
							        			echo '<div style="padding:2px;">'.$num_record.') '.$file_name.' | <b>'.$file_date.' </b>| 
							        					<a href="'.tep_href_link(FILENAME_PRICE_AUTOMATION, 'action=download_history_html&game_cat_id='.$categories_id.'&file_name='.$file_name.'').'" target="_blank" class="InputButton">VIEW</a>
								        			  </div>';
											}
							        	}
						        	}
						        } else {
						        	echo '<div style="padding:2px;"><b>'.TEXT_NO_RECORD.' </b></div>';
						        }
						        
						        if ($num_record == 0) { echo 'No Record!'; }
						        echo '</div>';
						        closedir($dh);
						    }
						}
						break;
				}
			}
		break;
	}	
}

function calculate_suggested_price ($competitors_price_array, $competitor_selected, $bb_rank) {
	$suggested_price_arr = array();
	$final_suggested_price_arr = array();
	$ProbabilisticObj = new Probabilistic();

	foreach ($competitors_price_array as $product_id => $competitors_price_id) {
		$total_competitors = 0;
		$total_competitors_plus = 0;
		$total = 0;
		$pre_Z = 0;
		$area = 0;
		$standard_deviation = 0;
		$ztable = array();
		$average = 0;
		$X = 0;
		$Z = 0;
		$formula = '';
		
		foreach ($competitors_price_id as $competitors_price_key => $competitors_price_value) {
			if(in_array($competitors_price_key, $competitor_selected)) {
				if ($competitors_price_value->price != 0) {
					$suggested_price_arr[$product_id][] = $competitors_price_value->price;
				}
			}
		}
		
		$sizeof_suggester_price_arr = sizeof($suggested_price_arr[$product_id]);
		
		if ($sizeof_suggester_price_arr > 1) {
			$total = array_sum($suggested_price_arr[$product_id]);
		} else {
			$total = $suggested_price_arr[$product_id][0];
		}
		
		$total_competitors = sizeof($suggested_price_arr[$product_id]);
		$total_competitors_plus = $total_competitors + 1;
		
		if($bb_rank >= $total_competitors_plus) { // Rank 1 - Cheapest | Rank 10 - Most expensive
			$formula = 'highest_price'; 
		} elseif($bb_rank == 1) {
			$formula = 'lowest_price';
		} else {
			$formula = 'rank_price';
		}
		
		switch ($formula) {
			case 'rank_price':
				$pre_Z = $bb_rank / $total_competitors_plus;
				if ($pre_Z > 0.5) {
					$area =  $pre_Z - 0.5;
				} else {
					$area = 0.5 - $pre_Z;
				}
			
				$average =  $total / $total_competitors;
				
				$standard_deviation = $ProbabilisticObj->deviation($suggested_price_arr[$product_id]);
				$Z = $ProbabilisticObj->getZval($area);

				if (tep_not_null($Z)) {
					if (!is_int($Z) && !is_float($Z)) {
						$Z = 0;
					}
				} else {
					$Z = 0;
				}
				
				if (tep_not_null($standard_deviation)) {
					if (!is_int($standard_deviation) && !is_float($standard_deviation)) {
						$standard_deviation = 0;
					}
				} else {
					$standard_deviation = 0;
				}
				
				if($pre_Z > 0.5) {
					$X = ($Z * $standard_deviation) + $average;
				} else {
					$X = $average - ($Z * $standard_deviation);
				}
				break;
			case 'lowest_price': // Get the lowest competitor's price
				if ($sizeof_suggester_price_arr > 1) {
					asort($suggested_price_arr[$product_id]);
					$X = current($suggested_price_arr[$product_id]);
				} else {
					$X = $suggested_price_arr[$product_id][0];
				}
				break;
			case 'highest_price': // Get the highest competitor's price
				if ($sizeof_suggester_price_arr > 1) {
					arsort($suggested_price_arr[$product_id]);
					$X = current($suggested_price_arr[$product_id]);
				} else {
					$X = $suggested_price_arr[$product_id][0];
				}
				break;
		}
		$final_suggested_price_arr[$product_id] = $X;
	}
	return $final_suggested_price_arr;
}

function array_average ($price_value) {
  	//variable and initializations
  	$the_result = 0.0;
  	$the_array_sum = array_sum($price_value); //sum the elements
  	$number_of_elements = count($price_value); //count the number of elements

 	//calculate the result
  	$the_result = $the_array_sum / $number_of_elements;

  	//return the value
  	return $the_result;
}

function generate_history_html ($file_content) {
	$FileContentHTML = '';
	
	$FileContentHTML = '	<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
							<html '.HTML_PARAMS.' >
							<head>
							<meta http-equiv="Content-Type" content="text/html; charset='.CHARSET.'">
							<title>'.TITLE.'</title>
							<script language="javascript" src="'.tep_href_link('includes/javascript/xmlhttp.js').'"></script>
							<link rel="stylesheet" type="text/css" href="'.tep_href_link('includes/stylesheet.css').'">
							<link rel="stylesheet" type="text/css" href="'.tep_href_link('includes/ui.tabs.css').'">
							<link rel="stylesheet" type="text/css" href="'.tep_href_link('includes/price_automation.css').'">
							<script language="javascript" src="'.tep_href_link('includes/general.js').'"></script>
							<script language="javascript" src="'.tep_href_link('includes/javascript/jquery.js').'"></script>
							<script language="javascript" src="'.tep_href_link('includes/javascript/jquery.tabs.js').'"></script>
							
							<script>
								jQuery(document).ready(function() {
									jQuery("#export_import-tab > ul").tabs();
									jQuery("#setting-tab > ul").tabs();
									jQuery("#buyback_competitor_price_setting-tab > ul").tabs();
									jQuery("#buyback_price_setting-tab > ul").tabs();
									jQuery("#backorder_setting-tab > ul").tabs();
									jQuery("#selling_price_setting-tab > ul").tabs();
									jQuery("#selling_competitor_setting-tab > ul").tabs();
									jQuery("#hide-server-tab > ul").tabs();
									
									jQuery.fn.clearForm = function() {
									  return this.each(function() {
										var type = this.type, tag = this.tagName.toLowerCase();
										if (tag == "form")
										  return jQuery(":input",this).clearForm();
										if (type == "text" || type == "password" || tag == "textarea")
										  this.value = "";
										else if (type == "checkbox" || type == "radio")
										  this.checked = false;
										else if (tag == "select")
										  this.selectedIndex = -1;
									  });
									};
											
									jQuery("a#buyback_competitor_price_setting_tab").click(function() {
										jQuery("#buyback_competitor_price_setting_toggle").toggle();
										return false;
									})
									
									jQuery("a#buyback_competitor_price_setting_sub_tab").click(function() {
										jQuery("#buyback_competitor_price_setting_toggle").toggle();
									});
									
									jQuery("a#buyback_price_setting_tab").click(function() {
										jQuery("#buyback_price_setting_toggle").toggle();
										return false;
									});
									
									jQuery("a#buyback_price_setting_sub_tab").click(function() {
										jQuery("#buyback_price_setting_toggle").toggle();
										return false;
									});
									
									jQuery("a#backorder_setting_tab").click(function() {
										jQuery("#backorder_setting_toggle").toggle();
										return false;
									});
									
									jQuery("a#backorder_setting_sub_tab").click(function() {
										jQuery("#backorder_setting_toggle").toggle();
										return false;
									});
									
									jQuery("a#selling_price_setting_tab").click(function() {
										jQuery("#selling_price_setting_toggle").toggle();
										return false;
									});
									
									jQuery("a#selling_price_setting_sub_tab").click(function() {
										jQuery("#selling_price_setting_toggle").toggle();
										return false;
									});
									
									jQuery("a#selling_competitor_setting_tab").click(function() {
										jQuery("#selling_competitor_setting_toggle").toggle();
										return false;
									});
									
									jQuery("a#selling_competitor_setting_sub_tab").click(function() {
										jQuery("#selling_competitor_setting_toggle").toggle();
										return false;
									});
	
									
									jQuery("a#view_selling_competitor_details").click(function() {
										jQuery("#selling_competitor_setting_toggle").toggle();
										return false;
									});
								})
								
							</script>
							</head>';
		
		$FileContentHTML .= str_replace('\\"', '"', $file_content);
		$FileContentHTML = str_replace("\'", "'", $FileContentHTML);
		
		return $FileContentHTML;
}

function generate_csv_history ($GameInfoCsv, $BuybackPriceCsv, $BackOrderCsv, $BuyBackCompetitorCsv, $SellingPriceCsv, $SellingCompetitorCsv, $SellingPackageHeadingCsv, $BuybackCompetitorPriceCsv, $SellingCompetitorPriceCsv, $SellingPackageCsv) {
	
//	$line_break = "\n";
//	$gap = ',';
//	$gap_column = 1;
//	$csv_heading = '';
//	$generate_csv_data = '';
//	$export_csv_product = '';
//	$game_id_arr = array();
//	
//	$rows_of_heading = 0; // 1st rows for heading
//	$rows_of_sub_heading = 1; // 2nd rows for sub heading
//	$total_buyback_competitors = 0;
//	$total_selling_competitors = 0;
//	$total_selling_package = 0;
//	$game_product_array = array();
	
	// Update Package Prices

//	if (isset($SellingPackageCsv)) {
//		foreach ($SellingPackageCsv as $product_id => $product_price) {
//			if (tep_not_null($product_id) && tep_not_null($product_price)) {
//				$update_package_price_sql = "	UPDATE " . TABLE_PRODUCTS . " 
// 												SET products_price = '". tep_db_input($product_price) ."',
// 													products_last_modified = now()
// 												WHERE products_id = '" . tep_db_input($product_id) . "'";
//				tep_db_query($update_package_price_sql);
//			}
//		}
//	}
//	if (isset($SellingPackageCsv)) {
//		$selling_package_price_cnt = 1;
//		foreach($SellingPackageCsv->SellingPackageObj as $SellingPackagePriceArray) {
//			$selling_package_price_column_cnt = 0;
//			foreach ($SellingPackagePriceArray as $SellingPackagePriceObj) {
//				if (tep_not_null($SellingPackagePriceObj->package_price) && tep_not_null($SellingPackagePriceObj->package_id)) {
//					$update_package_price_sql = "	UPDATE " . TABLE_PRODUCTS . " 
// 	 												SET products_price = '". tep_db_input($SellingPackagePriceObj->package_price) ."',
// 	 													products_last_modified = now()
// 	 												WHERE products_id = '" . tep_db_input($SellingPackagePriceObj->package_id) . "'";
// 					tep_db_query($update_package_price_sql);
//				}
//				$csv_data[$rows_of_heading + $rows_of_sub_heading + $selling_package_price_cnt][$selling_package_data_size + $selling_package_price_column_cnt] = $SellingPackagePriceObj->package_price.',';
//				$selling_package_price_column_cnt ++;
//			}
//			$selling_package_price_cnt ++;
//		}
//	}
 			
//	if(isset($GameInfoCsv)) {
//		$total_servers = sizeof($GameInfoCsv);
//		
//		//echo $total_servers.'<pre>';print_r($HistoryContentCsv);
//		if (isset($GameInfoCsv->GameInfoObj)) {
//			foreach ($GameInfoCsv->GameInfoObj as $JsonObj) {
//				$column_cnt = 0;
//				
//				// Buyback Competitor's Heading
//				if (isset($BuyBackCompetitorCsv->BuyBackCompetitorObj)) {
//					$bb_competitor_column_cnt = 0;
//					foreach($BuyBackCompetitorCsv->BuyBackCompetitorObj as $BuyBackCompetitor) {
//	 					$csv_data[$rows_of_heading + $rows_of_sub_heading][2 + $bb_competitor_column_cnt] = $BuyBackCompetitor->buyback_competitors.',';
//		 				$bb_competitor_column_cnt ++;
//		 			}
//				}
//				
//				if ($total_buyback_competitors == 0) {
//					$total_buyback_competitors = sizeof($BuyBackCompetitorCsv->BuyBackCompetitorObj);
//				}
//	
//				// Buyback Price Setting Heading
//				$buyback_setting_data_size = 2 + $total_buyback_competitors + $gap_column;
//				
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$buyback_setting_data_size] = CSV_HEADING_SUGGESTED_PRICE . ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$buyback_setting_data_size + 1] = CSV_HEADING_CURRENT_PRICE . ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$buyback_setting_data_size + 2] = CSV_HEADING_OVERWRITE_PRICE . ',';
//				
//				$aging_bo_n_completed_data_size = 3 + $buyback_setting_data_size + $gap_column;
//			
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$aging_bo_n_completed_data_size] = '< 1' . ENTRY_DAY . ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$aging_bo_n_completed_data_size + 1] = '< 2' . ENTRY_DAYS . ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$aging_bo_n_completed_data_size + 2] = '< 3' . ENTRY_DAYS . ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$aging_bo_n_completed_data_size + 3] = '< 4' . ENTRY_DAYS . ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$aging_bo_n_completed_data_size + 4] = '< 5' . ENTRY_DAYS . ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$aging_bo_n_completed_data_size + 5] = '< 2' . ENTRY_WEEKS . ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$aging_bo_n_completed_data_size + 6] = '< 1 - 6' . ENTRY_MONTHS . ',';
//				
//				$selling_setting_data_size = 7 + $aging_bo_n_completed_data_size + $gap_column;
//			
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$selling_setting_data_size] = CSV_HEADING_MARKUP_PERCENTAGE. ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$selling_setting_data_size + 1] = CSV_HEADING_INCREMENT_VALUE. ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$selling_setting_data_size + 2] = CSV_HEADING_SUGGESTED_PRICE. ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$selling_setting_data_size + 3] = CSV_HEADING_CURRENT_PRICE. ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$selling_setting_data_size + 4] = CSV_HEADING_OVERWRITE_PRICE. ',';
//				$csv_data[$rows_of_heading + $rows_of_sub_heading][$selling_setting_data_size + 5] = CSV_HEADING_SELLING_RANK. ',';
//				
//				$selling_package_data_size = 6 + $selling_setting_data_size;
//				
//				if (isset($SellingPackageHeadingCsv->SellingPackageHeadingObj)) {
//					if ($total_selling_package == 0) {
//						$total_selling_package = sizeof($SellingPackageHeadingCsv->SellingPackageHeadingObj);
//					}
//				}
//				
//				$selling_competitor_data_size = $total_selling_package + $selling_package_data_size + $gap_column;
//				
//				if ($total_selling_competitors == 0) {
//					$total_selling_competitors = sizeof($SellingCompetitorCsv->SellingCompetitorObj);
//				}
//				
//				$server_selected_data_size = $total_selling_competitors + $selling_competitor_data_size + $gap_column;
//					
//				if (isset($GameInfoCsv->GameInfoObj)) {
//					$game_cnt = 1;
//					foreach ($GameInfoCsv->GameInfoObj as $ServerObj) {
//						$game_product_array[] = $ServerObj->game_product_id;
//						$csv_data[$rows_of_heading + $rows_of_sub_heading + $game_cnt][$column_cnt] = $ServerObj->game_product_name. ',';
//
//		 			
//			 			if (isset($ServerObj->selling_competitor_price)) {
//							$selling_competitor_price_column_cnt = 0;
//							foreach($ServerObj->selling_competitor_price as $SellingCompetitorPrice) {
//			 					$csv_data[$rows_of_heading + $rows_of_sub_heading + $game_cnt][$selling_competitor_data_size + $selling_competitor_price_column_cnt] = $SellingCompetitorPrice->selling_competitors_price.',';
//				 				$selling_competitor_price_column_cnt ++;
//				 			}
//						}
//						
//						$csv_data[$rows_of_heading + $rows_of_sub_heading + $game_cnt][$server_selected_data_size] = $ServerObj->ValueSelected.',';
//			 			$game_cnt ++;
//					}
//				}
//		 		$column_cnt ++;
//		 	}
//		 	
//			// Buyback Price Setting
//			if (isset($BuybackPriceCsv->BuybackPriceObj)) {
//				$buyback_price_cnt = 1;
//				foreach ($BuybackPriceCsv->BuybackPriceObj as $BuybackPriceObj) {
//					$csv_data[$rows_of_heading + $rows_of_sub_heading + $buyback_price_cnt][$buyback_setting_data_size] = $BuybackPriceObj->game_suggested_price. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $buyback_price_cnt][$buyback_setting_data_size + 1] = $BuybackPriceObj->game_current_price. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $buyback_price_cnt][$buyback_setting_data_size + 2] = $BuybackPriceObj->game_overwrited_price. ',';
//		 			$buyback_price_cnt ++;
//				}
//			}
//			
//			// Back Order
//			if (isset($BackOrderCsv->BackOrderObj)) {
//				$backorder_cnt = 1;
//				foreach ($BackOrderCsv->BackOrderObj as $BackOrderObj) {
//					$csv_data[$rows_of_heading + $rows_of_sub_heading + $backorder_cnt][$aging_bo_n_completed_data_size] = $BackOrderObj->backorder_days_0. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $backorder_cnt][$aging_bo_n_completed_data_size + 1] = $BackOrderObj->backorder_days_1. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $backorder_cnt][$aging_bo_n_completed_data_size + 2] = $BackOrderObj->backorder_days_2. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $backorder_cnt][$aging_bo_n_completed_data_size + 3] = $BackOrderObj->backorder_days_3. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $backorder_cnt][$aging_bo_n_completed_data_size + 4] = $BackOrderObj->backorder_days_4. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $backorder_cnt][$aging_bo_n_completed_data_size + 5] = $BackOrderObj->backorder_days_5. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $backorder_cnt][$aging_bo_n_completed_data_size + 6] = $BackOrderObj->backorder_days_6. ',';
//		 			$backorder_cnt ++;
//				}
//			}
//			
//			// Selling Price
//			if (isset($SellingPriceCsv->SellingPriceObj)) {
//				$selling_price_cnt = 1;
//				foreach ($SellingPriceCsv->SellingPriceObj as $SellingPriceObj) {
//					$csv_data[$rows_of_heading + $rows_of_sub_heading + $selling_price_cnt][$selling_setting_data_size] = $SellingPriceObj->selling_price_margin. '%,';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $selling_price_cnt][$selling_setting_data_size + 1] = $SellingPriceObj->increment_val. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $selling_price_cnt][$selling_setting_data_size + 2] = $SellingPriceObj->selling_suggested_price. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $selling_price_cnt][$selling_setting_data_size + 3] = $SellingPriceObj->selling_current_price. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $selling_price_cnt][$selling_setting_data_size + 4] = $SellingPriceObj->selling_overwrite_price. ',';
//		 			$csv_data[$rows_of_heading + $rows_of_sub_heading + $selling_price_cnt][$selling_setting_data_size + 5] = $SellingPriceObj->selling_competitors_rank. ',';
//		 			$selling_price_cnt ++;
//				}
//			}
//			
//	
//			// Buyback Competitor Price
//			if (isset($BuybackCompetitorPriceCsv)) {
//				$bb_competitor_row_cnt = 1;
//				foreach($BuybackCompetitorPriceCsv->BuybackCompetitorPriceObj as $BuyBackCompetitorPriceArray) {
//					$bb_competitor_price_cnt = 0;
//					foreach ($BuyBackCompetitorPriceArray as $BuyBackCompetitorPriceObj) {
//						$csv_data[$rows_of_heading + $rows_of_sub_heading + $bb_competitor_row_cnt][2 + $bb_competitor_price_cnt] = $BuyBackCompetitorPriceObj->buyback_competitors_price.',';
//	 					$bb_competitor_price_cnt ++;
//					}
//					$bb_competitor_row_cnt ++;
//				}
// 			}
//			
//			// Selling Package Heading
//			if (isset($SellingPackageHeadingCsv->SellingPackageHeadingObj)) {
//				$SellingPackageHeading_column_cnt = 0;
//				foreach($SellingPackageHeadingCsv->SellingPackageHeadingObj as $SellingPackageHeading) {
// 					$csv_data[$rows_of_heading + $rows_of_sub_heading][$selling_package_data_size + $SellingPackageHeading_column_cnt] = $SellingPackageHeading->package_qty.',';
//	 				$SellingPackageHeading_column_cnt ++;
//	 			}
//			}
//			
//			
////			if (isset($SellingCompetitorPriceCsv)) {
////				$selling_competitor_row_cnt = 1;
////				foreach($SellingCompetitorPriceCsv->SellingCompetitorPriceObj as $SellingCompetitorPriceArray) {
////					$selling_competitor_name_cnt = 0;
////					foreach ($SellingCompetitorPriceArray as $SellingCompetitorPriceObj) {
////						$csv_data[$rows_of_heading + $rows_of_sub_heading + $selling_competitor_row_cnt][$selling_competitor_data_size + $selling_competitor_name_cnt] = $SellingCompetitorPriceObj->selling_competitors_price.',';
////	 					$selling_competitor_name_cnt ++;
////					}
////					$selling_competitor_row_cnt ++;
////				}
//// 			}
//			
// 			
//// 			if (isset($SellingPackageCsv->selling_package)) {
//// 				$selling_package_price_cnt = 0;
//// 				foreach($SellingPackageCsv->selling_package as $SellingPackagePriceObj) {
//// 					if (tep_not_null($SellingPackagePriceObj->package_price) && tep_not_null($SellingPackagePriceObj->package_id)) {
////						$update_package_price_sql = "	UPDATE " . TABLE_PRODUCTS . " 
////	 	 												SET products_price = '". tep_db_input($SellingPackagePriceObj->package_price) ."',
////	 	 													products_last_modified = now()
////	 	 												WHERE products_id = '" . tep_db_input($SellingPackagePriceObj->package_id) . "'";
//// 	 					tep_db_query($update_package_price_sql);
////					}
////	 				$csv_data[$rows_of_heading + $rows_of_sub_heading + $game_cnt][$selling_package_data_size + $selling_package_price_cnt] = $SellingPackagePriceObj->package_price.',';
////	 				$selling_package_price_cnt ++;
////	 			}
//// 			}
//			
//			// Selling Competitor Heading
//			if (isset($SellingCompetitorCsv->SellingCompetitorObj)) {
//				$selling_competitor_column_cnt = 0;
//				foreach($SellingCompetitorCsv->SellingCompetitorObj as $SellingCompetitor) {
// 					$csv_data[$rows_of_heading + $rows_of_sub_heading][$selling_competitor_data_size + $selling_competitor_column_cnt] = $SellingCompetitor->selling_competitors.',';
//	 				$selling_competitor_column_cnt ++;
//	 			}
//			}
//			
//			// Selling Competitor Price
//			if (isset($SellingCompetitorPriceCsv)) {
//				$selling_competitor_row_cnt = 1;
//				foreach($SellingCompetitorPriceCsv->SellingCompetitorPriceObj as $SellingCompetitorPriceArray) {
//					$selling_competitor_name_cnt = 0;
//					foreach ($SellingCompetitorPriceArray as $SellingCompetitorPriceObj) {
//						$csv_data[$rows_of_heading + $rows_of_sub_heading + $selling_competitor_row_cnt][$selling_competitor_data_size + $selling_competitor_name_cnt] = $SellingCompetitorPriceObj->selling_competitors_price.',';
//	 					$selling_competitor_name_cnt ++;
//					}
//					$selling_competitor_row_cnt ++;
//				}
// 			}
//		}
//	}
//	
//	$total_column = 2 + 
//					$buyback_setting_data_size + 
//					$aging_bo_n_completed_data_size + 
//					$selling_setting_data_size + 
//					$selling_package_data_size +
//					$selling_competitor_data_size + 
//					$server_selected_data_size;
//					
//	$csv_data[$rows_of_heading][0] = CSV_HEADING_SERVER_NAME . ',';
//	$csv_data[$rows_of_heading][$bb_competitor_data_size] = CSV_HEADING_BUYBACK_COMPETITOR . ',';
//	$csv_data[$rows_of_heading][$buyback_setting_data_size] = CSV_HEADING_BUYBACK_SETTING . ',';
//	$csv_data[$rows_of_heading][$aging_bo_n_completed_data_size] = CSV_HEADING_AGING_BO_AND_COMPLETED . ',';
//	$csv_data[$rows_of_heading][$selling_setting_data_size] = CSV_HEADING_SELLING_SETTING . ',';
//	$csv_data[$rows_of_heading][$selling_competitor_data_size] = CSV_HEADING_SELLING_COMPETITOR . ',';
//	$csv_data[$rows_of_heading][$server_selected_data_size] = CSV_HEADING_UPDATE . ',';
//	
//	ksort($csv_data);
//	foreach ($csv_data as $csv_data_key => $csv_data_rows) {
//		for ($csv_data_cnt = 0; $csv_data_cnt <= $total_column; $csv_data_cnt++) {
//			if (isset($csv_data_rows[$csv_data_cnt])) {
//				$generate_csv_data .= $csv_data_rows[$csv_data_cnt];
//			} else {
//				$generate_csv_data .= $gap;
//			}
//		}
//		$generate_csv_data .= $line_break;
//	}
//	
//	return $generate_csv_data;
}

function tep_xmlhttp_get_package_price($unit_price, $weight, $subproduct_qty, $increment_value) {
	if (substr($increment_value, -1) == '%') {
		$price_to_use = ( $unit_price * pow((1 + (substr($increment_value, 0, -1) / 100)), ($weight - 1)) ) * $subproduct_qty;
    } else {
    	$price_to_use = ((($weight-1) * $increment_value) + $unit_price) * $subproduct_qty;
    }
    return $price_to_use;
}

?>