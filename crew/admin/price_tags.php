<?/*
  	$Id: price_tags.php,v 1.35 2013/10/07 11:15:28 chingyen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');

tep_set_time_limit(0);

$currencies = new currencies();
$seo_url_alias_category_permission = tep_admin_files_actions(FILENAME_CATEGORIES, 'SEO_URL_ALIAS_CATEGORY');
$edit_price_permission = tep_admin_files_actions(FILENAME_CATEGORIES, 'CATALOG_EDIT_PRICE');
$edit_product_purchase_mode_permission = tep_admin_files_actions(FILENAME_CATEGORIES, 'CATALOG_EDIT_PURCHASE_MODE');

function filter_selection($var) {
	return ($var && $var >= 1);
}

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

$price_tags_field_array = array	( 	"products_name" => "Product Name",
									"products_price" => "Product Price",
									"products_status" => "Product Status",
									"products_display" => "Product Display",
									"products_description" => "Product Description",
									"products_image" => "Small Image",
									"products_description_image" => "Detail Image",
									"products_sort_order" => "Product Sort Order",
									"products_purchase_mode" => "Purchase Mode",
									"products_pre_order_level" => "Pre-Order Level",
									"products_eta" => "ETA (hours)",
									"products_add_to_cart_msg" => "Delivery Message - Add to Cart",
									"products_preorder_msg" => "Delivery Message - Pre-Order",
									"products_out_of_stock_msg" => "Delivery Message - Out of Stock",
									"products_payment_mature_period" => "Payment Mature Period (mins)",
									"products_url_alias" => "SEO Url Alias",
									"products_skip_inventory" => "Do not keep inventory"
								);

$updating_token_array = array( 	array ('id' => '', "text" => "--- Apply Token ---"),
								array ('id' => "##categories_heading_title##", "text" => "Category Heading Title"),
								array ('id' => "##categories_name##", "text" => "Category Name"),
								array ('id' => "##products_name##", "text" => "Product Name")
							);

$updating_value_translation = array('products_status' => array ("0" => "Inactive", "1" => "Active"),
									'products_display' => array ("0" => "Hide", "1" => "Show"),
									'products_purchase_mode' => array ("1" => 'Always "Add to Cart"', "2" => 'Always "Pre-Order"', "3" => 'Always "Out of Stock"', "4" => 'Auto')
									);

if (tep_not_null($action)) {
	switch ($action) {
		case "insert_price_groups":
		case "update_price_groups":
			$price_groups_sql_data_array = array(	'price_groups_name' => tep_db_prepare_input($_REQUEST["price_groups_name"]),
            	           							'price_groups_description' => tep_db_prepare_input($_REQUEST["price_groups_description"]));
			
            if ($action == "insert_price_groups") {
				tep_db_perform(TABLE_PRICE_GROUPS, $price_groups_sql_data_array);
			} else {
				tep_db_perform(TABLE_PRICE_GROUPS, $price_groups_sql_data_array, 'update', ' price_groups_id="'.$_REQUEST["gID"].'"');
			}
			tep_redirect(tep_href_link(FILENAME_PRICE_TAGS));
			
			break;
		case "delete_price_groups":
			if (tep_not_null($_REQUEST["gID"])) {
				$price_groups_delete_sql = "DELETE FROM " . TABLE_PRICE_GROUPS . " WHERE price_groups_id='" . $_REQUEST["gID"] . "'";
				tep_db_query($price_groups_delete_sql);
				
				$g2c_delete_sql = "DELETE FROM " . TABLE_PRICE_GROUPS_TO_CATEGORIES . " WHERE price_groups_id='" . $_REQUEST["gID"] . "'";
				tep_db_query($g2c_delete_sql);
				
				$price_tags_delete_sql = "DELETE FROM " . TABLE_PRICE_TAGS . " WHERE price_groups_id='" . $_REQUEST["gID"] . "'";
				tep_db_query($price_tags_delete_sql);
			}
			tep_redirect(tep_href_link(FILENAME_PRICE_TAGS));
			
			break;
		case "insert_price_tags":
		case "update_price_tags":
			if ($_REQUEST["price_tags_update_field"] == "products_url_alias" && !$seo_url_alias_category_permission) {
				$messageStack->add_session(ERROR_SEO_URL_ACCESS_DENIED, 'error');
				tep_redirect(tep_href_link(FILENAME_PRICE_TAGS));
			}
			
			if ($_REQUEST["price_tags_update_field"] == "products_price" && !$edit_price_permission) {
				$messageStack->add_session(ERROR_UPDATE_PRICE_ACCESS_DENIED, 'error');
				tep_redirect(tep_href_link(FILENAME_PRICE_TAGS));
			}
			
			if ($_REQUEST["price_tags_update_field"] == "products_purchase_mode" && !$edit_product_purchase_mode_permission) {
				$messageStack->add_session(ERROR_UPDATE_PURCHASE_MODE_ACCESS_DENIED, 'error');
				tep_redirect(tep_href_link(FILENAME_PRICE_TAGS));
			}
			
			if ($_REQUEST["price_tags_update_field"] == "products_status" || $_REQUEST["price_tags_update_field"] == "products_skip_inventory") {
				$updating_value = intval($HTTP_POST_VARS["tags_price"]);
				$updating_value = $updating_value > 0 ? 1 : 0;
			} else if ($_REQUEST["price_tags_update_field"] == "products_purchase_mode" && $edit_product_purchase_mode_permission) {
				$updating_value = intval($HTTP_POST_VARS["tags_price"]);
				$updating_value = ($updating_value < 1 || $updating_value > 4) ? 1 : $updating_value;
				$updating_value .= ':~:' . (tep_not_null($HTTP_POST_VARS["products_eta"]) ? (double)$HTTP_POST_VARS["products_eta"] : '') . ':~:' . (tep_not_null($HTTP_POST_VARS["products_pre_order_level"]) ? (int)$HTTP_POST_VARS["products_pre_order_level"] : '') . ':~:' . (tep_not_null($HTTP_POST_VARS["products_out_of_stock_level"]) ? (int)$HTTP_POST_VARS["products_out_of_stock_level"] : '');
			} else if ($_REQUEST["price_tags_update_field"] == "products_eta") {
				$updating_value = $HTTP_POST_VARS["tags_price"];
				$updating_value = is_numeric($updating_value) ? $updating_value : "DEFAULT";
			} else if ($_REQUEST["price_tags_update_field"] == "products_pre_order_level") {
				$updating_value = $HTTP_POST_VARS["tags_price"];
				$updating_value = is_numeric($updating_value) ? (int)$updating_value : '';
			} else if ($_REQUEST["price_tags_update_field"] == "products_image" || $_REQUEST["price_tags_update_field"] == "products_description_image") {
				$updating_value = (int)$HTTP_POST_VARS["tags_price"];
				$updating_value = $updating_value > 0 ? $updating_value : 0;
			} else if ($_REQUEST["price_tags_update_field"] == "products_url_alias") {
				include_once(DIR_WS_CLASSES . 'seo.php');
				$seo_url = new seo_url();
				
				$updating_value = $seo_url->tep_translate_special_character($HTTP_POST_VARS['tags_price']);
				$updating_value = $seo_url->tep_validate_special_characters($updating_value);
				
				if ($HTTP_POST_VARS['tags_price'] != $updating_value) {
					$messageStack->add_session(sprintf(WARNING_BATCH_UPDATE_URL_ALIAS_CHANGED, $HTTP_POST_VARS['tags_price'], $updating_value), 'warning');
				}
			} else {
				$updating_value = tep_db_prepare_input($HTTP_POST_VARS["tags_price"]);
			}
			
			$price_tags_sql_data_array = array(	'price_groups_id' => $_REQUEST["gID"],
												'price_tags_name' => tep_db_prepare_input($_REQUEST["price_tags_name"]),
												'price_tags_description' => tep_db_prepare_input($_REQUEST["price_tags_description"]),
						               			'tags_price' => $updating_value,
						               			'price_tags_field' => tep_db_prepare_input($_REQUEST["price_tags_field"]),
						               			'price_tags_update_field' => tep_db_prepare_input($_REQUEST["price_tags_update_field"]),
						               			'tags_update_language_id' => (int)$HTTP_POST_VARS["updating_language_id"],
						               			'price_tags_order' => $_REQUEST["price_tags_order"]
						               			);
			
            if ($action == "insert_price_tags") {
				tep_db_perform(TABLE_PRICE_TAGS, $price_tags_sql_data_array);
			} else {
				tep_db_perform(TABLE_PRICE_TAGS, $price_tags_sql_data_array, 'update', ' price_tags_id="'.$_REQUEST["tID"].'"');
			}
			tep_redirect(tep_href_link(FILENAME_PRICE_TAGS));
			
			break;
		case "delete_price_tags":
			if (tep_not_null($_REQUEST["tID"])) {
				$price_tags_delete_sql = "DELETE FROM " . TABLE_PRICE_TAGS . " WHERE price_tags_id='" . $_REQUEST["tID"] . "'";
				tep_db_query($price_tags_delete_sql);
			}
			tep_redirect(tep_href_link(FILENAME_PRICE_TAGS));
			
			break;
		case "assign_tags_confirm":
			$price_tags_array = array();
			$browser_id = '';
			
			$g2c_delete_sql = "DELETE FROM " . TABLE_PRICE_GROUPS_TO_CATEGORIES . " WHERE price_groups_id = '" . $_REQUEST["gID"] . "'";
			tep_db_query($g2c_delete_sql);
			
			$selected_cat_array = array();
			if ($_REQUEST["browser_id"] == "IE") {
				$selected_cat_array = $_REQUEST["SelectedItem"];
			} else {	// for Non-IE browser
				$selected_cat_array = array_keys(array_filter($_REQUEST["HiddenCat"], "filter_selection"));
			}
			
			if (count($selected_cat_array)) {
				foreach($selected_cat_array as $cat_id) {
					$g2c_insert_sql = "INSERT INTO " . TABLE_PRICE_GROUPS_TO_CATEGORIES . " (price_groups_id, categories_id) VALUES (".$_REQUEST["gID"].", ".$cat_id.")";
					tep_db_query($g2c_insert_sql);
				}
			}
			tep_redirect(tep_href_link(FILENAME_PRICE_TAGS));
			
			break;
		case "update_product_price":
			require_once(DIR_WS_FUNCTIONS.'custom_product.php');
			require_once(DIR_WS_CLASSES . 'log.php');
			$log_object = new log_files($login_id);
			
			$user_comment = $_REQUEST["user_comment_".$_REQUEST["selected_gID"]];
			
			$price_tags_array = array();
			$assigned_cat_array = array();
			
			$price_tags_select_sql = "SELECT * FROM " . TABLE_PRICE_TAGS . " WHERE price_groups_id = '" . $_REQUEST["selected_gID"] . "' ";
			$price_tags_order_by_sql = " ORDER BY price_tags_order";
			
			if(!$seo_url_alias_category_permission){
				$price_tags_select_sql .= " AND price_tags_update_field<>'products_url_alias' "	;
			}
			
			if(!$edit_price_permission){
				$price_tags_select_sql .= " AND price_tags_update_field<>'products_price' "	;
			}
			
			if(!$edit_product_purchase_mode_permission){
				$price_tags_select_sql .= " AND price_tags_update_field<>'products_purchase_mode' "	;
			}
			
			$price_tags_select_sql .= $price_tags_order_by_sql;
			/*
			if ($seo_url_alias_category_permission) {
				$price_tags_select_sql = "SELECT * FROM " . TABLE_PRICE_TAGS . " WHERE price_groups_id = '" . $_REQUEST["selected_gID"] . "' ORDER BY price_tags_order";
			} else {
				$price_tags_select_sql = "	SELECT * 
											FROM " . TABLE_PRICE_TAGS . " 
											WHERE price_groups_id = '" . $_REQUEST["selected_gID"] . "' 
											AND price_tags_update_field<>'products_url_alias'
											ORDER BY price_tags_order";
			}*/
			
			$price_tags_result_sql = tep_db_query($price_tags_select_sql);
			while ($price_tags_row = tep_db_fetch_array($price_tags_result_sql)) {
				$price_tags_array[$price_tags_row["price_tags_id"]] = array("name" => $price_tags_row["price_tags_name"],
																			"match_field" => $price_tags_row["price_tags_field"],
																			"price" => $price_tags_row["tags_price"],
																			"update_field" => $price_tags_row["price_tags_update_field"],
																			"tags_update_language_id" => $price_tags_row["tags_update_language_id"],
																			);
			}
			
			if (tep_check_cat_tree_permissions(FILENAME_PRICE_TAGS, 0) != 1) {
				$temp_cat_array = tep_get_eligible_categories(FILENAME_PRICE_TAGS, $temp_cat_array, '0', true);
				$categories_where_str = " categories_id IN ('" . implode("', '", $temp_cat_array) . "') ";
			} else {
				$categories_where_str = " 1 ";
			}
			
			$g2c_select_sql = "SELECT categories_id FROM " . TABLE_PRICE_GROUPS_TO_CATEGORIES . " WHERE price_groups_id='" . $_REQUEST["selected_gID"] . "' AND " . $categories_where_str;
			$g2c_result_sql = tep_db_query($g2c_select_sql);
			while ($g2c_row = tep_db_fetch_array($g2c_result_sql)) {
				$assigned_cat_array[] = $g2c_row["categories_id"];
			}
			
			$do_batch_update_products_count = false;
			
			foreach ($price_tags_array as $tag_id=>$res) {
				$product_select_sql = '';
				$select_update_field_str = '';
				if ($res["update_field"] == "products_price" || $res["update_field"] == "products_status" || $res["update_field"] == "products_display" || $res["update_field"] == "products_image" || $res["update_field"] == "products_description_image" || $res["update_field"] == "products_url_alias") {
					$select_update_field_str = 'p.'.$res["update_field"];
				} else if ($res["update_field"] == "products_description") {
					$select_update_field_str = 'pd.'.$res["update_field"];
				}
				$where_update_field_str = tep_not_null($select_update_field_str) ? $select_update_field_str . " <> '" . $res["price"] . "' "  : ' 1 ';
				if ($res["update_field"] == "products_display") {
					$where_update_field_str .= ' AND (p.products_bundle="" AND p.products_bundle_dynamic="") ';
				}
				
				switch ($res["match_field"]) {
					case "products_name":
						$product_name_where_str = $res["name"] == '#ALL_PRODUCT#' ? ' 1 ' : " pd.products_name LIKE '" . tep_db_input($res["name"]) . "' ";
						
						$product_select_sql = "	SELECT ".(tep_not_null($select_update_field_str) ? $select_update_field_str . ',' : '')." p.products_id, p.products_cat_path, p2c.categories_id 
												FROM " . TABLE_PRODUCTS . " AS p 
												INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
													ON p.products_id=pd.products_id 
												INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
													ON pd.products_id=p2c.products_id 
												WHERE p2c.categories_id IN ('" . implode("', '", $assigned_cat_array) . "') 
													AND p2c.products_is_link=0
													AND pd.language_id='".(int)$languages_id."' 
													AND " . $product_name_where_str . " 
													AND " . $where_update_field_str;
						break;
					case "products_price":
						$product_select_sql = "	SELECT ".(tep_not_null($select_update_field_str) ? $select_update_field_str . ',' : '')." p.products_id, p.products_cat_path, p2c.categories_id
												FROM " . TABLE_PRODUCTS . " AS p 
												INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
													ON p.products_id=pd.products_id 
												INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
													ON p.products_id=p2c.products_id 
												WHERE p2c.categories_id IN ('" . implode("', '", $assigned_cat_array) . "') 
													AND p2c.products_is_link=0
													AND pd.language_id='".(int)$languages_id."' 
													AND p.products_price = '".sprintf("%01.4f", $res["name"])."'
													AND " . $where_update_field_str;
						break;
				}
				
				if (tep_not_null($product_select_sql)) {
					$product_result_sql = tep_db_query($product_select_sql);
					
					while ($product_row = tep_db_fetch_array($product_result_sql)) {
						if ($res["update_field"] == "products_status" || $res["update_field"] == "products_display")	$do_batch_update_products_count = true;
						
						if ($res["update_field"] == "products_price" || $res["update_field"] == "products_status" || $res["update_field"] == "products_display" || $res["update_field"] == "products_sort_order" || $res["update_field"] == "products_url_alias") {
							if ($res["update_field"] == "products_price") {
								$log_object->insert_log($product_row["products_id"], 'products_price', $product_row["products_price"], $res["price"], LOG_PRICE_ADJUST, $user_comment);
							} else if ($res["update_field"] == "products_status") {
								$log_object->insert_log($product_row["products_id"], 'products_status', $product_row["products_status"], $res["price"], LOG_STATUS_ADJUST, $user_comment);
							} else if ($res["update_field"] == "products_display") {
								$log_object->insert_log($product_row["products_id"], 'products_display', $product_row["products_display"], $res["price"], LOG_DISPLAY_ADJUST, $user_comment);
							} else if ($res["update_field"] == "products_url_alias") {
								$log_object->insert_log($product_row["products_id"], 'products_url_alias', $product_row["products_url_alias"], $res["price"], LOG_URL_ALIAS_ADJUST, $user_comment);
								
								//check existing products_url_alias
								$exist_url_alias_check_select_sql = "	SELECT info_changed_history_remark 
																		FROM ". TABLE_INFO_CHANGED_HISTORY ."
																		WHERE info_changed_history_type_id='". $product_row["products_id"] ."'
																		ORDER BY info_changed_history_id DESC LIMIT 1";
								$exist_url_alias_check_result_sql = tep_db_query($exist_url_alias_check_select_sql);
								$exist_url_alias_check_row = tep_db_fetch_array($exist_url_alias_check_result_sql);
								
								if ($exist_url_alias_check_row['info_changed_history_remark'] != $res["price"]) {
									//add to info_changed_history
						  			$info_changed_history_array = array('info_changed_history_type' => TEXT_INFO_CHANGED_HISTORY_PRODUCTS,
							  											'info_changed_history_type_id' => (int)$product_row["products_id"],
							  											'info_changed_history_remark' => $res["price"],
							  											'info_changed_history_date_added' => 'now()',
							  											'info_changed_history_added_by' => $login_email_address
							  											);
									
									tep_db_perform(TABLE_INFO_CHANGED_HISTORY, $info_changed_history_array);
								}
							}
							
							$products_price_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
															SET ".$res["update_field"]."='".tep_db_input($res["price"])."', 
															products_last_modified=now() 
															WHERE products_id='" . $product_row["products_id"] . "'";
							tep_db_query($products_price_update_sql);
						} else if ($res["update_field"] == "products_purchase_mode") {
							$do_update = false;
							
							// In tis format: pmode:~:ETA:~:Pre-Order Level:~:Out of Stock Level
							$pmode_array = explode(':~:', $res["price"]);
							
							$extra_where_str = '';
							
							$update_str_array = array();
							$update_str_array[] = $res["update_field"]."='".(int)$pmode_array[0]."' ";
							$update_str_array[] = " products_last_modified=now() ";
							
							if ($pmode_array[0] == 1 || $pmode_array[0] == 3) {	// Always Add to Cart or Always Out of Stock
								$update_str_array[] = " products_eta = NULL ";
								$update_str_array[] = " products_pre_order_level = NULL ";
								$update_str_array[] = " products_out_of_stock_level = NULL ";
								
								$extra_where_str = " AND products_bundle='' AND products_bundle_dynamic='' AND custom_products_type_id <= 1 ";
								
								$do_update = true;
							} else if ($pmode_array[0] == 2) {	// Always Pre-Order
								$update_str_array[] = " products_eta = " . (tep_not_null($pmode_array[1]) ? "'" . (double)$pmode_array[1] . "'" : 'NULL');
								$update_str_array[] = " products_pre_order_level = NULL ";
								$update_str_array[] = " products_out_of_stock_level = NULL ";
								
								$extra_where_str = " AND products_bundle='' AND products_bundle_dynamic='' AND custom_products_type_id <= 1 ";
								
								$do_update = true;
							} else if ($pmode_array[0] == 4) {	// Auto Mode
								$update_str_array[] = " products_eta = " . (tep_not_null($pmode_array[1]) ? "'" . (double)$pmode_array[1] . "'" : 'NULL');
								$update_str_array[] = " products_pre_order_level = " . (tep_not_null($pmode_array[2]) ? "'" . (double)$pmode_array[2] . "'" : 'NULL');
								$update_str_array[] = " products_out_of_stock_level = " . (tep_not_null($pmode_array[3]) ? "'" . (double)$pmode_array[3] . "'" : 'NULL');
								
								$extra_where_str = " AND products_skip_inventory=0 AND products_bundle='' AND products_bundle_dynamic='' AND custom_products_type_id=0 ";
								
								$do_update = true;
							}
							
							if ($do_update && count($update_str_array)) {
								$products_price_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
																SET ".implode(',', $update_str_array)." 
																WHERE products_id='" . $product_row["products_id"] . "' " . 
																	$extra_where_str;
								tep_db_query($products_price_update_sql);
							}
						} else if ($res["update_field"] == "products_pre_order_level") {
							if (is_numeric($res["price"])) {
								$update_value = intval($res["price"]);
							} else {
								$update_value = "NULL";
							}
							
							$products_price_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
															SET ".$res["update_field"]."=".$update_value.", 
															products_last_modified=now() 
															WHERE products_id='" . $product_row["products_id"] . "'
																AND products_purchase_mode=4 AND products_bundle='' AND products_bundle_dynamic='' AND custom_products_type_id=0";
							tep_db_query($products_price_update_sql);
						} else if ($res["update_field"] == "products_eta") {
							if (strtoupper($res["price"]) == "DEFAULT") {
								$update_value = "NULL";
							} else {
								$update_value = (double)$res["price"];
							}
							
							$products_price_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
															SET ".$res["update_field"]."=".$update_value.", 
																products_last_modified=now() 
															WHERE products_id='" . $product_row["products_id"] . "'
																AND products_purchase_mode IN (2, 4) AND products_bundle='' AND products_bundle_dynamic='' AND custom_products_type_id=0";
							tep_db_query($products_price_update_sql);
						} else if ($res["update_field"] == "products_add_to_cart_msg" || $res["update_field"] == "products_preorder_msg" || $res["update_field"] == "products_out_of_stock_msg") {
							$update_value = $res["price"];
							
							$products_price_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
															SET ".$res["update_field"]."= '".tep_db_input($update_value)."', 
															products_last_modified=now() 
															WHERE products_id='" . $product_row["products_id"] . "'";
							tep_db_query($products_price_update_sql);
						} else if ($res["update_field"] == "products_payment_mature_period") {
							$update_value = intval($res["price"]);
							
							$products_price_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
															SET ".$res["update_field"]."=".$update_value.", 
															products_last_modified=now() 
															WHERE products_id='" . $product_row["products_id"] . "'";
							tep_db_query($products_price_update_sql);
						} else if ($res["update_field"] == "products_skip_inventory") {
							$update_str_array = array();
							$update_str_array[] = $res["update_field"]."='".$res["price"]."' ";
							$update_str_array[] = " products_last_modified=now() ";
							
							if ($res["price"] == 1) {
								$update_str_array[] = " products_quantity=NULL ";
								$update_str_array[] = " products_actual_quantity=NULL ";
								$update_str_array[] = " products_pre_order_level=NULL ";
							}
							$products_price_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
															SET ".implode(',', $update_str_array)." 
															WHERE products_id='" . $product_row["products_id"] . "'
																AND products_purchase_mode IN (1, 2, 3) AND products_bundle='' AND products_bundle_dynamic='' AND custom_products_type_id = 0";
							tep_db_query($products_price_update_sql);
						} else if ($res["update_field"] == "products_description") {
							$token_array = array();
							$update_to_desc_str = $res["price"];
							
							for ($t_cnt=0; $t_cnt < count($updating_token_array); $t_cnt++) {
								if (tep_not_null($updating_token_array[$t_cnt]['id'])) {
									if (strpos($update_to_desc_str, $updating_token_array[$t_cnt]['id']) !== false) {
										$token_field_name = str_replace('##', '', $updating_token_array[$t_cnt]['id']);
										
										switch($token_field_name) {
											case "categories_heading_title":
											case "categories_name":
												$cat_title_query = tep_db_query("SELECT " . $token_field_name . " FROM " . TABLE_CATEGORIES_DESCRIPTION . " WHERE categories_id = '" . $product_row['categories_id'] . "' AND language_id='".(int)$res["tags_update_language_id"]."';");
												$cat_title_row = tep_db_fetch_array($cat_title_query);
												$update_to_desc_str = str_replace($updating_token_array[$t_cnt]['id'], $cat_title_row[$token_field_name], $update_to_desc_str);
												break;
											case "products_name":
												$prod_title_query = tep_db_query("SELECT products_name FROM " . TABLE_PRODUCTS_DESCRIPTION . " WHERE products_id = '" . $product_row["products_id"] . "' AND language_id='".(int)$res["tags_update_language_id"]."';");
												$prod_title_row = tep_db_fetch_array($prod_title_query);
												$update_to_desc_str = str_replace($updating_token_array[$t_cnt]['id'], $prod_title_row["products_name"], $update_to_desc_str);
												break;
											default:
												break;
										}
									}
								}
							}
							$tag_update_filed_update_sql = "UPDATE " . TABLE_PRODUCTS_DESCRIPTION . " 
															SET ".$res["update_field"]."='".tep_db_input($update_to_desc_str)."' 
															WHERE products_id='" . $product_row["products_id"] . "' AND language_id='".(int)$res["tags_update_language_id"]."';";
							tep_db_query($tag_update_filed_update_sql);
							//$res["price"] = preg_replace("/([^#]*?)(##)([^#]+)(##)([^#]*?)/is", "\$1\$4", $res["price"]);
						} else if ($res["update_field"] == "products_image" || $res["update_field"] == "products_description_image") {
							require_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
							$aws_obj = new ogm_amazon_ws();
							
							$product_image_path = DIR_FS_CATALOG_IMAGES . "products/";
							$permis = "0644";
							
							if ($res["price"] > 0) {
								$reference_image_select_sql = "	SELECT " . $res["update_field"] . ", products_cat_path
																FROM " . TABLE_PRODUCTS . "
																WHERE products_id='" . tep_db_input($res["price"]) . "'";
								$reference_image_result_sql = tep_db_query($reference_image_select_sql);
								if ($reference_image_row = tep_db_fetch_array($reference_image_result_sql)) {
									if (tep_not_null($reference_image_row[$res["update_field"]])) {
										$path_parts = pathinfo($reference_image_row[$res["update_field"]]);
										$ext = $path_parts['extension'];
										
										if ($res["update_field"] == "products_image") {
											$des_image_name = $product_row["products_id"].'.'.$ext;
										} else {
											$des_image_name = $product_row["products_id"].'_large.'.$ext;
										}
										
										if ($aws_obj->is_image_exists($des_image_name, 'images/products/', 'BUCKET_STATIC')) {
											$aws_obj->delete_file();
					        			} else if (file_exists($product_image_path.$des_image_name)){
											$oldPermission = @umask(0);
											@chmod($product_image_path.$des_image_name, 0777);
											@umask($oldPermission);
											@unlink($product_image_path.$des_image_name);
										}
										
										$products_price_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
																		SET ".$res["update_field"]."='".tep_db_input($reference_image_row[$res["update_field"]])."', 
																		products_last_modified=now() 
																		WHERE products_id='" . $product_row["products_id"] . "'";
										tep_db_query($products_price_update_sql);
									} else {
										$messageStack->add_session(sprintf(WARNING_REFERENCE_IMAGE_NOT_EXISTS, $price_tags_field_array[$res["update_field"]], $reference_image_row['products_cat_path'], $res["price"]), 'warning');
									}
								} else {
									$messageStack->add_session(sprintf(WARNING_REFERENCE_PRODUCT_NOT_EXISTS, $price_tags_field_array[$res["update_field"]], $res["price"]), 'warning');
								}
							} else {
								if (tep_not_null($product_row[$res["update_field"]])) {
									if ($aws_obj->is_image_exists($product_row[$res["update_field"]], 'images/products/', 'BUCKET_STATIC')) {
										$aws_obj->delete_file();
					        		} else if (file_exists($product_image_path.$product_row[$res["update_field"]])) {
										$oldPermission = @umask(0);
										@chmod($product_image_path.$product_row[$res["update_field"]], 0777);
										@umask($oldPermission);
										@unlink($product_image_path.$product_row[$res["update_field"]]);
									}
									
									$products_price_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
																	SET ".$res["update_field"]."='', 
																	products_last_modified=now() 
																	WHERE products_id='" . $product_row["products_id"] . "'";
									tep_db_query($products_price_update_sql);
								}
							}
							
							unset($aws_obj);
						}
						
						# Zero Price Product Notification
						$prod_status_sel_sql = "SELECT products_id FROM " . TABLE_PRODUCTS . " 
												WHERE products_id='" . $product_row["products_id"] . "' 
													AND custom_products_type_id != 1 
													AND products_status = 1 
													AND products_price <= 0";
						$prod_status_res_sql = tep_db_query($prod_status_sel_sql);
						if ($prod_status_row = tep_db_fetch_array($prod_status_res_sql)) {
							$products_id = (int)$product_row["products_id"];
							
							// Send notification email
							$email_product_id_link = '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path(tep_get_actual_product_cat_id($products_id)) . '&pID=' . $products_id . '&selected_box=catalog') . '"  target="_blank">'.$products_id.'</a>';
							$zero_price_email_contents = sprintf(EMAIL_PRODUCT_ZERO_AMOUNT_NOTIFICATION_CONTENT, $email_product_id_link, tep_get_products_name($products_id), 'From Edit Product Page', 'Price is 0.00 and Active status', date("Y-m-d H:i:s"), tep_get_ip_address(), $_SESSION['login_email_address'], tep_get_admin_group_name($_SESSION['login_email_address']));
							
							$cat_cfg_array = tep_get_cfg_setting($products_id, 'product', 'STOCK_ZERO_PRICE_PRODUCT_EMAIL');
							$email_to_array = tep_parse_email_string($cat_cfg_array['STOCK_ZERO_PRICE_PRODUCT_EMAIL']);
							for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
								tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, 'Zero Price Product')), $zero_price_email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
							}
						}
					}
				}
			}
			
			/*
			if ($do_batch_update_products_count) {
				tep_update_products_count(0);
			}
			*/
			$messageStack->add_session(SUCCESS_BATCH_UPDATE, 'success');
			tep_redirect(tep_href_link(FILENAME_PRICE_TAGS));
			
			break;
	}
}

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
	<script language="Javascript1.2"><!-- // load htmlarea
		// MaxiDVD Added WYSIWYG HTML Area Box + Admin Function v1.7 - 2.2 MS2 Products Description HTML - Head
		        _editor_url = "<?php echo (($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . DIR_WS_ADMIN; ?>htmlarea/";  // URL to htmlarea files
		          var win_ie_ver = parseFloat(navigator.appVersion.split("MSIE")[1]);
		           if (navigator.userAgent.indexOf('Mac')        >= 0) { win_ie_ver = 0; }
		            if (navigator.userAgent.indexOf('Windows CE') >= 0) { win_ie_ver = 0; }
		             if (navigator.userAgent.indexOf('Opera')      >= 0) { win_ie_ver = 0; }
		         <?php if (HTML_AREA_WYSIWYG_BASIC_PD == 'Basic'){ ?>  if (win_ie_ver >= 5.5) {
		         document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_basic.js"');
		         document.write(' language="Javascript1.2"></scr' + 'ipt>');
		            } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
		         <?php } else{ ?> if (win_ie_ver >= 5.5) {
		         document.write('<scr' + 'ipt src="' +_editor_url+ 'editor_advanced.js"');
		         document.write(' language="Javascript1.2"></scr' + 'ipt>');
		            } else { document.write('<scr'+'ipt>function editor_generate() { return false; }</scr'+'ipt>'); }
		         <?php }?>
		
		var config = new Object();  // create new config object
	    config.width = "<?php echo HTML_AREA_WYSIWYG_WIDTH; ?>px";
	    config.height = "<?php echo HTML_AREA_WYSIWYG_HEIGHT; ?>px";
	    config.bodyStyle = 'background-color: <?php echo HTML_AREA_WYSIWYG_BG_COLOUR; ?>; font-family: "<?php echo HTML_AREA_WYSIWYG_FONT_TYPE; ?>"; color: <?php echo HTML_AREA_WYSIWYG_FONT_COLOUR; ?>; font-size: <?php echo HTML_AREA_WYSIWYG_FONT_SIZE; ?>pt;';
		config.debug = <?php echo HTML_AREA_WYSIWYG_DEBUG; ?>;
	// --></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					</tr>
            					<tr>
    								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  								</tr>
            					<tr>
            						<td class="main" valign="top">
            						<?
            							if ($action != "new_price_groups")
            								echo '[ <a href="'.tep_href_link(FILENAME_PRICE_TAGS, 'action=new_price_groups').'" >'.LINK_ADD_PRICE_CLASS.'</a> ]';
            							else
            								echo "&nbsp;";
            						?>
            						</td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?
if ($action == "new_price_groups" || $action == "edit_price_groups") {
?>
					<tr>
        				<td width="100%">
<?
	echo tep_draw_form('price_groups', FILENAME_PRICE_TAGS, tep_get_all_get_params(array('action')) . 'action='.($action=="new_price_groups" ? 'insert_price_groups' : 'update_price_groups'), 'post', 'onSubmit="return price_groups_form_checking();"');
	if ($_REQUEST["gID"]) {
		$price_groups_select_sql = "SELECT * FROM " . TABLE_PRICE_GROUPS . " WHERE price_groups_id='" . $_REQUEST["gID"] . "'";
		$price_groups_result_sql = tep_db_query($price_groups_select_sql);
		$price_groups_row = tep_db_fetch_array($price_groups_result_sql);
		
		echo tep_draw_hidden_field("gID", $_REQUEST["gID"]);
	}
?>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_PRICE_GROUPS_NAME?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('price_groups_name', $price_groups_row["price_groups_name"], 'size="40" id="price_groups_name"').'&nbsp;<span class="fieldRequired">*</span>'?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" valign="top"><?=ENTRY_PRICE_GROUPS_DESCRIPTION?></td>
									<td class="main">
										<?=tep_draw_textarea_field('price_groups_description', 'soft', '70', '15', $price_groups_row["price_groups_description"], ' id="class_description" ')?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td colspan="2" align="right">
										<?=($action=="new_price_groups" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PRICE_TAGS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
						</form>
        				</td>
        			</tr>
<?
	if (HTML_AREA_WYSIWYG_DISABLE == 'Disable') {} else { ?>
		<script language="JavaScript1.2" defer>
	       	editor_generate('price_groups_description',config);
		</script>
<?
	}
} else if ($action == "new_price_tags" || $action == "edit_price_tags") {
	$parameters = array('price_groups_name' => '',
						'price_groups_description' => '',
						'price_tags_name' => '',
						'price_tags_description' => '',
               			'tags_price' => '',
               			'price_tags_field' => '',
               			'price_tags_update_field' => '',
               			'tags_update_language_id' => '',
               			'price_tags_order' => ''
               			);
    $tInfo = new objectInfo($parameters);
?>
					<tr>
        				<td width="100%">
<?
	echo tep_draw_form('price_tags', FILENAME_PRICE_TAGS, tep_get_all_get_params(array('action')) . 'action='.($action=="new_price_tags" ? 'insert_price_tags' : 'update_price_tags'), 'post', 'onSubmit="return price_tags_form_checking();"');
	if ($_REQUEST["tID"]) {
		$price_tags_select_sql = "	SELECT g.price_groups_name, g.price_groups_description, t.* 
									FROM " . TABLE_PRICE_GROUPS . " AS g 
									INNER JOIN " . TABLE_PRICE_TAGS . " AS t 
										ON g.price_groups_id=t.price_groups_id 
									WHERE t.price_tags_id='" . $_REQUEST["tID"] . "'";
		$price_tags_result_sql = tep_db_query($price_tags_select_sql);
		$price_tags_row = tep_db_fetch_array($price_tags_result_sql);
		
		$tInfo->objectInfo($price_tags_row);
		
		echo tep_draw_hidden_field("gID", $price_tags_row["price_groups_id"]);
		echo tep_draw_hidden_field("tID", $_REQUEST["tID"]);
	} else {
		$price_groups_select_sql = "SELECT price_groups_name 
									FROM " . TABLE_PRICE_GROUPS . " 
									WHERE price_groups_id='" . $_REQUEST["gID"] . "'";
		$price_groups_result_sql = tep_db_query($price_groups_select_sql);
		$price_groups_row = tep_db_fetch_array($price_groups_result_sql);
		
		$tInfo->objectInfo($price_groups_row);
		
		echo tep_draw_hidden_field("gID", $_REQUEST["gID"]);
	}
?>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_PRICE_GROUPS_NAME?></td>
									<td class="main"><?=$tInfo->price_groups_name?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_PRICE_TAGS_FIELD?></td>
									<td class="main">
									<?
										$matching_field_array = array( 	array ('id' => 0, "text" => "--------------- Select Field ---------------"),
																		array ('id' => "products_name", "text" => "Product Name"),
																		array ('id' => "products_price", "text" => "Product Price")
																	);
										echo tep_draw_pull_down_menu("price_tags_field", $matching_field_array, $tInfo->price_tags_field, ' id="price_tags_field" onChange="if (this.options[this.selectedIndex].value == \'products_name\') { document.getElementById(\'matching_field_info\').className = \'show\'; } else { document.getElementById(\'matching_field_info\').className = \'hide\'; } " ').'&nbsp;<span class="fieldRequired">*</span>';
									?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_PRICE_TAGS_NAME?></td>
									<td class="main">
										<?=tep_draw_input_field('price_tags_name', $tInfo->price_tags_name, 'size="40" id=" price_tags_name" maxlength="64"').'&nbsp;<span class="fieldRequired">*</span>'?> 									
										<span id="matching_field_info" class="<?=$tInfo->price_tags_field=='products_name' ? 'show' : 'hide'?>">Enter "#ALL_PRODUCT#" if update apply to all products of the selected categories</span>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_PRICE_TAGS_UPDATE_FIELD?></td>
									<td class="main">
									<?
										$updating_field_array = array( 	array ('id' => 0, "text" => "--------------- Select Field ---------------"),
																		array ('id' => "products_status", "text" => "Product Status"),
																		array ('id' => "products_display", "text" => "Product Display"),
																		array ('id' => "products_description", "text" => "Product Description"),
																		array ('id' => "products_image", "text" => "Product Small Image"),
																		array ('id' => "products_description_image", "text" => "Product Detail Image"),
																		array ('id' => "products_sort_order", "text" => "Product Sort Order"),																		array ('id' => "products_pre_order_level", "text" => "Pre-Order Level"),
																		array ('id' => "products_eta", "text" => "ETA (hours)"),
																		array ('id' => "products_add_to_cart_msg", "text" => "Delivery Message - Add to Cart"),
																		array ('id' => "products_preorder_msg", "text" => "Delivery Message - Pre-Order"),
																		array ('id' => "products_out_of_stock_msg", "text" => "Delivery Message - Out of Stock"),
																		array ('id' => "products_payment_mature_period", "text" => "Payment Mature Period (mins)")
																	);
										if ($seo_url_alias_category_permission) {
											$updating_field_array[] = array ('id' => "products_url_alias", "text" => "SEO Url Alias");
										}
										
										if($edit_price_permission) {
											$updating_field_array[] = array ('id' => "products_price", "text" => "Product Price");
										}
										
										if($edit_product_purchase_mode_permission) {
											$updating_field_array[] = array ('id' => "products_purchase_mode", "text" => "Purchase Mode");
										}
										
										echo tep_draw_pull_down_menu("price_tags_update_field", $updating_field_array, $tInfo->price_tags_update_field, ' id="price_tags_update_field" onChange="showHelpControls(this);"').'&nbsp;<span class="fieldRequired">*</span>';
									?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_TAGS_UPDATING_VALUE?></td>
									<td class="dataTableRow">
										<table border="0" cellspacing="0" cellpadding="1">
											<tr>
												<td class="main" valign="top">
													<div id="updating_value_div" style="white-space: nowrap;">
													<?
														if ($tInfo->price_tags_update_field == 'products_description') {
															echo tep_draw_textarea_field('tags_price', 'soft', '60', '10', $tInfo->tags_price, 'id="tags_price"');
														} else {
															echo tep_draw_input_field('tags_price', $tInfo->tags_price, 'size="15" id="tags_price"');
														}
													?>
													<div>
												</td>
												<td class="main" valign="top">
													<div style="float: left;"><span class="fieldRequired">*</span></div>
													<div id="updating_token_div" class="hide">
													<?=tep_draw_pull_down_menu("updating_value_token", $updating_token_array, '', ' onChange="if (this.value != \'\') { appendToken(this); this.selectedIndex=0}"')?>
													</div>
												</td>
												<td class="main" valign="top">
													<div id="auto_seo_div" class="hide">
														<?=tep_draw_checkbox_field('batch_update_auto_seo', '1', '', '', 'id="batch_update_auto_seo" onclick="auto_generate_seo()"') . TEXT_EDIT_CATEGORIES_AUTO_URL_ALIAS?>
													</div>
												</td>
											</tr>
											<tr>
												<td colspan="3">
													<div id="helpTxtDiv"></div>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
			      				</tr>
			      				<tr>
									<td class="main" valign="top"><?=ENTRY_TAGS_LANGUAGE?></td>
									<td class="main">
										<?=tep_draw_pull_down_menu("updating_language_id", tep_get_languages(), $tInfo->tags_update_language_id, 'id="updating_language_id"')?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" valign="top"><?=ENTRY_PRICE_TAGS_DESCRIPTION?></td>
									<td class="main">
										<?=tep_draw_textarea_field('price_tags_description', 'soft', '70', '15', $tInfo->price_tags_description)?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" valign="top"><?=ENTRY_PRICE_TAGS_ORDER?></td>
									<td class="main">
										<?=tep_draw_input_field('price_tags_order', $tInfo->price_tags_order, 'size="10" id="price_tags_order"').'&nbsp;<span class="fieldRequired">*</span>'?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td colspan="2" align="right">
										<?=($action=="new_price_tags" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PRICE_TAGS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
						</form>
        				</td>
        			</tr>
        		<script>
        		<!--
        			function showHelpControls(update_field_sel) {
        				var div_objRef = DOMCall('updating_value_div');
						var objRef = DOMCall('tags_price');
						if (objRef != null) {
							var tmp_val = objRef.value;
						}
						var update_val_html_str = '';
						
						if (update_field_sel.value == "products_description") {
							document.getElementById('updating_token_div').className = 'show';
							document.getElementById('auto_seo_div').className = 'hide';
							
							objRef = document.createElement('TEXTAREA');
							objRef.id = 'tags_price';
							objRef.name = 'tags_price';
							objRef.value = tmp_val;
							objRef.rows = '10';
							objRef.cols = '60';
							
							div_objRef.innerHTML = '';
							div_objRef.appendChild(objRef);
							
							document.getElementById('updating_language_id').disabled = false;
							document.getElementById('helpTxtDiv').style.display = 'none';
						} else if (update_field_sel.value == "products_status" || update_field_sel.value == "products_display") {
							document.getElementById('updating_token_div').className = 'hide';
							document.getElementById('auto_seo_div').className = 'hide';
							
							objRef = document.createElement("SELECT");
					    	objRef.name = 'tags_price';
					    	objRef.id = 'tags_price';
					    	clearOptionList(objRef);
					    	
					    	var sel_option_array = new Array();
					    	if (update_field_sel.value == "products_status") {
								sel_option_array["0"] = "Inactive";
								sel_option_array["1"] = "Active";
							} else {
								sel_option_array["0"] = "Hide";
								sel_option_array["1"] = "Show";
							}
							
					    	for (a in sel_option_array) {
					    		var is_selected = (tmp_val == a) ? true : false;
				      			appendToSelect(objRef, a, sel_option_array[a], is_selected);
						    }
						    
						    div_objRef.innerHTML = '';
							div_objRef.appendChild(objRef);
							
							document.getElementById('updating_language_id').disabled = true;
							document.getElementById('helpTxtDiv').style.display = 'none';
						} else if (update_field_sel.value == "products_purchase_mode") {
							document.getElementById('updating_token_div').className = 'hide';
							document.getElementById('auto_seo_div').className = 'hide';
							
							var pmode_setting_array = tmp_val.split(':~:');
							
							var main_table = document.createElement('TABLE');
							main_table.setAttribute('cellpadding', '1');
							main_table.setAttribute('cellspacing', '1');
							main_table.setAttribute('width', '100%');
							
							var tbody = document.createElement('TBODY');
							
							var row1 = document.createElement('TR');
							row1.setAttribute('height', '40px');
							var first_cell = document.createElement('TD');
							var second_cell = document.createElement('TD');
							var third_cell = document.createElement('TD');
							
							first_cell.setAttribute('className', 'commonBoxHeading');
							first_cell.setAttribute('class', 'commonBoxHeading');
							first_cell.innerHTML = '&nbsp;';
							
							second_cell.setAttribute('className', 'commonBoxHeading');
							second_cell.setAttribute('class', 'commonBoxHeading');
							second_cell.innerHTML = '<table border="0" cellspacing="0" cellpadding="2"><tr><td class="commonBoxHeading"><input name="tags_price" id="tags_price" value="4" onclick="purchaseModeSetting();" type="radio"' + (pmode_setting_array[0]=='4' ? ' Checked ' : '') + '></td><td class="commonBoxHeading">' + '<?=TABLE_HEADING_AUTO_MODE?></td></tr></table>';
							second_cell.setAttribute('align', 'left');
							
							third_cell.setAttribute('className', 'commonBoxHeading');
							third_cell.setAttribute('class', 'commonBoxHeading');
							third_cell.innerHTML = '<table border="0" cellspacing="0" cellpadding="2"><tr><td class="commonBoxHeading"><input name="manual_purchase_mode" onclick="if(document.price_tags.tags_price[1].checked || document.price_tags.tags_price[2].checked || document.price_tags.tags_price[3].checked) {this.checked=true;} else {this.checked=false;}" type="radio"></td><td class="commonBoxHeading"><?=TABLE_HEADING_MANUAL_MODE?></td></tr></table>';
							third_cell.setAttribute('align', 'left');
							
							row1.appendChild(first_cell);
							row1.appendChild(second_cell);
							row1.appendChild(third_cell);
							
							var row2 = document.createElement('TR');
							row2.setAttribute('className', 'reportListingOdd');
							row2.setAttribute('class', 'reportListingOdd');
							row2.setAttribute('height', '40px');
							var first_cell = document.createElement('TD');
							var second_cell = document.createElement('TD');
							var third_cell = document.createElement('TD');
							
							first_cell.setAttribute('className', 'main');
							first_cell.setAttribute('class', 'main');
							first_cell.innerHTML = '<?=TEXT_PRUCHASE_MODE_ADD_TO_CART?>';
							first_cell.setAttribute('align', 'right');
							
							second_cell.setAttribute('className', 'main');
							second_cell.setAttribute('class', 'main');
							second_cell.innerHTML = '&nbsp;';
							
							third_cell.setAttribute('className', 'main');
							third_cell.setAttribute('class', 'main');
							third_cell.innerHTML = '<input name="tags_price" id="tags_price" value="1" onclick="purchaseModeSetting();" type="radio"' + (pmode_setting_array[0]=='1' ? ' Checked ' : '') + '>';
							third_cell.setAttribute('align', 'center');
							
							row2.appendChild(first_cell);
							row2.appendChild(second_cell);
							row2.appendChild(third_cell);
							
							var row3 = document.createElement('TR');
							row3.setAttribute('className', 'reportListingEven');
							row3.setAttribute('class', 'reportListingEven');
							row3.setAttribute('height', '40px');
							var first_cell = document.createElement('TD');
							var second_cell = document.createElement('TD');
							var third_cell = document.createElement('TD');
							
							first_cell.setAttribute('className', 'main');
							first_cell.setAttribute('class', 'main');
							first_cell.innerHTML = '<?=TEXT_PRUCHASE_MODE_PRE_ORDER?><br><?=TEXT_PRODUCTS_ETA?>' + '<input name="products_eta" id="products_eta" size="10" type="text" value="'+(pmode_setting_array[1] != null ? pmode_setting_array[1] : '')+'" onKeyUP="if (trim_str(this.value) != \'\' && !validateMayHvPtDecimal(trim_str(this.value), false)) { this.value = \'\'; }">' + '<br><small><?=sprintf(TEXT_PRODUCTS_DEFAULT_ETA, tep_get_eta_string(SYSTEM_PRODUCT_ETA))?></small>';
							first_cell.setAttribute('align', 'right');
							
							second_cell.setAttribute('className', 'main');
							second_cell.setAttribute('class', 'main');
							second_cell.innerHTML = '<input name="products_pre_order_level" id="products_pre_order_level" size="8" type="text" value="'+(pmode_setting_array[2] != null ? pmode_setting_array[2] : '')+'" onKeyUP="if (trim_str(this.value) != \'\' && (trim_str(this.value) != \'-\' && !validateSignInteger(trim_str(this.value))) ) { this.value = \'\'; }">';
							second_cell.setAttribute('align', 'center');
							
							third_cell.setAttribute('className', 'main');
							third_cell.setAttribute('class', 'main');
							third_cell.innerHTML = '<input name="tags_price" id="tags_price" value="2" onclick="purchaseModeSetting();" type="radio"' + (pmode_setting_array[0]=='2' ? ' Checked ' : '') + '>';
							third_cell.setAttribute('align', 'center');
							
							row3.appendChild(first_cell);
							row3.appendChild(second_cell);
							row3.appendChild(third_cell);
							
							var row4 = document.createElement('TR');
							row4.setAttribute('className', 'reportListingOdd');
							row4.setAttribute('class', 'reportListingOdd');
							row4.setAttribute('height', '40px');
							var first_cell = document.createElement('TD');
							var second_cell = document.createElement('TD');
							var third_cell = document.createElement('TD');
							
							first_cell.setAttribute('className', 'main');
							first_cell.setAttribute('class', 'main');
							first_cell.innerHTML = '<?=TEXT_PRUCHASE_MODE_OUT_OF_STOCK?>';
							first_cell.setAttribute('align', 'right');
							
							second_cell.setAttribute('className', 'main');
							second_cell.setAttribute('class', 'main');
							second_cell.innerHTML = '<input name="products_out_of_stock_level" id="products_out_of_stock_level" size="8" type="text" value="'+(pmode_setting_array[3] != null ? pmode_setting_array[3] : '')+'" onKeyUP="if (trim_str(this.value) != \'\' && (trim_str(this.value) != \'-\' && !validateSignInteger(trim_str(this.value))) ) { this.value = \'\'; }">';
							second_cell.setAttribute('align', 'center');
							
							third_cell.setAttribute('className', 'main');
							third_cell.setAttribute('class', 'main');
							third_cell.innerHTML = '<input name="tags_price" id="tags_price" value="3" onclick="purchaseModeSetting();" type="radio"' + (pmode_setting_array[0]=='3' ? ' Checked ' : '') + '>';
							third_cell.setAttribute('align', 'center');
							
							row4.appendChild(first_cell);
							row4.appendChild(second_cell);
							row4.appendChild(third_cell);
							
							tbody.appendChild(row1);
							tbody.appendChild(row2);
							tbody.appendChild(row3);
							tbody.appendChild(row4);
							main_table.appendChild(tbody);
							
							div_objRef.innerHTML = '';
							div_objRef.appendChild(main_table);
							
							document.getElementById('updating_language_id').disabled = true;
							
							with (document.getElementById('helpTxtDiv')) {
								innerHTML = '<div style="float:left;">Note:&nbsp;</div><div style="float:left;">Leave blank for ETA if follow system default.<br>Leave blank for Pre-Order level or Out-of-Stock level if it is not applicable.</div>';
								style.display = 'block';
							}
							
							purchaseModeSetting();
						} else {
							document.getElementById('updating_token_div').className = 'hide';
							document.getElementById('auto_seo_div').className = 'hide';
							
							objRef = document.createElement('INPUT');
							objRef.id = 'tags_price';
							objRef.value = tmp_val;
							objRef.name = 'tags_price';
							objRef.size = 40;
							if (update_field_sel.value == "products_url_alias") {
								document.getElementById('auto_seo_div').className = 'show';
							}
							
							div_objRef.innerHTML = '';
							div_objRef.appendChild(objRef);
							
							document.getElementById('updating_language_id').disabled = true;
							
							if (update_field_sel.value == "products_pre_order_level") {
								var helpTxt = 'Options:<br>Any integer value, or<br>Leave blank if not applicable';
							} else if (update_field_sel.value == "products_eta") {
								var helpTxt = 'Options:<br>Any positive numeric value, or<br>"DEFAULT" to follow system default';
							} else if (update_field_sel.value == "products_payment_mature_period") {
								var helpTxt = 'Options:<br>Any positive numeric value';
							} else if (update_field_sel.value == "products_skip_inventory") {
								var helpTxt = 'Options: (Key in the number)<br>0: No<br>1: Yes';
							} else if (update_field_sel.value == "products_image" || update_field_sel.value == "products_description_image") {
								var helpTxt = 'Click <a href="javascript:openDGDialog(\'<?=tep_href_link(FILENAME_POPUP_PRODUCTS_LIST)?>\', 600, 250, \'\');">here</a> to select the reference image\'s product.<br>Or enter "0" to remove the image.';
							}
							
							if (helpTxt != null) {
								with (document.getElementById('helpTxtDiv')) {
									innerHTML = helpTxt;
									style.display = 'block';
								}
							} else {
								document.getElementById('helpTxtDiv').style.display = 'none';
							}
						}
					}
					
					function appendToken(token_obj) {
						var updatee_obj = document.getElementById('tags_price');
						//IE support
				        if (document.selection) {
				            updatee_obj.focus();
				            sel = document.selection.createRange();
				            sel.text = token_obj.value;
				        } else if (updatee_obj.selectionStart || updatee_obj.selectionStart == "0") {
				            var startPos = updatee_obj.selectionStart;
				            var endPos = updatee_obj.selectionEnd;
				            var oldText = updatee_obj.value;
						
				            updatee_obj.value = oldText.substring(0, startPos) + token_obj.value + oldText.substring(endPos, oldText.length);
				        } else {
				            updatee_obj.value += token_obj.value;
				        }
					}
					
					function purchaseModeSetting() {
						if (document.price_tags.tags_price[1].checked == true || document.price_tags.tags_price[3].checked == true) {
							document.getElementById('products_pre_order_level').disabled=true;
							document.getElementById('products_eta').disabled=true;
							document.getElementById('products_out_of_stock_level').disabled=true;
							
							document.price_tags.manual_purchase_mode.checked = true;
						} else if (document.price_tags.tags_price[2].checked == true) {
							document.getElementById('products_pre_order_level').disabled=true;
							document.getElementById('products_eta').disabled=false;
							document.getElementById('products_out_of_stock_level').disabled=true;
							
							document.price_tags.manual_purchase_mode.checked = true;
						} else if (document.price_tags.tags_price[0].checked == true) {	// Auto is select
							document.getElementById('products_pre_order_level').disabled=false;
							document.getElementById('products_eta').disabled=false;
							document.getElementById('products_out_of_stock_level').disabled=false;
							
							document.price_tags.manual_purchase_mode.checked = false;
						}
					}
					
        			function init() {
        				showHelpControls(document.price_tags.price_tags_update_field);
        			}
        			init();
        			
        			// add item to select element the less elegant, but compatible way.
					function appendToSelect(select, value, content, is_selected) {
						var opt = document.createElement('option');
						opt.value = value;
						opt.text = content;
						if (is_selected == true) {
							opt.selected = true;
						}
						select.options.add(opt);
					}
					
					function clearOptionList(obj) {
						while (obj.options.length > 0) {
							obj.remove(0);
						}
					}
					
					function getReturnedValue(received_val) {
						var updated_field = DOMCall('tags_price');
						
						if (updated_field != null) {
							updated_field.value = received_val;
						}
					}

					var current_auto_seo_url_batch_update = '<?=$tInfo->tags_price?>';// current seo url alias for batch update
					function auto_generate_seo() { //auto generate SEO URL alias
						var title = document.getElementById('price_tags_name');
						var url_alias = document.getElementById('tags_price');
						var auto_seo = document.getElementById('batch_update_auto_seo');
						
						if (auto_seo.checked == true) {
							var str = filter_special_char(title.value);
							url_alias.value = str.toLowerCase(); //to lowercase
						} else {
							url_alias.value = current_auto_seo_url_batch_update;
						}
					}
				//-->
        		</script>
<?
	if (HTML_AREA_WYSIWYG_DISABLE == 'Disable') {} else { ?>
		<script language="JavaScript1.2" defer>
	       	editor_generate('price_tags_description',config);
		</script>
<?
	}
} else if ($action == "linking_to_cat") {
	function tep_show_list_items($ListItems, $Level=0) {
		global $languages_id, $HiddenItems, $g2c_array;
		$SubTotal=0;
		
		foreach ($ListItems as $ListItem)
		{
			$NewListItems = array() ;
			$parent_to_child = array();
			
			$p_rank = tep_check_cat_tree_permissions(FILENAME_PRICE_TAGS, $ListItem["categories_id"]);
			
			if ($p_rank > 0) {
				$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
									FROM " . TABLE_CATEGORIES . " AS c 
									INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id = cd.categories_id 
									WHERE c.parent_id='" . $ListItem["categories_id"] . "' 
										AND cd.language_id='" . (int)$languages_id ."' 
									ORDER BY sort_order, cd.categories_name " ;
				$cat_result_sql = tep_db_query($cat_select_sql);
				while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
					$NewListItems[] = $cat_row ;
					$parent_to_child[] = $cat_row["categories_id"];
				}
				
				$SubTotal += 1 ;
				
				$DisplayName = strip_tags($ListItem["categories_name"]);
				if (!$DisplayName) $DisplayName = "" ;
			    
			    if (count($NewListItems)) {
			    	$DisplayName .= '&nbsp;&nbsp;[<a href="javascript:;" onClick=" expandTree(aux'.$ListItem["categories_id"].'); setTreeCheckbox(\'assign_tag\', \'MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"].'\', true, \''.implode(",", $parent_to_child).'\');">Check All</a>]&nbsp;&nbsp;[<a href="javascript:;" onClick="expandTree(aux'.$ListItem["categories_id"].'); setTreeCheckbox(\'assign_tag\', \'MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"].'\', false, \''.implode(",", $parent_to_child).'\');">Uncheck All</a>]';
			    }
			    
			    if ($p_rank == 1) {
				    $prod_select_sql = "SELECT products_id 
										FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " 
										WHERE categories_id='" . $ListItem["categories_id"] . "' 
											AND products_is_link=0 " ;
					$prod_result_sql = tep_db_query($prod_select_sql);
					if (tep_db_num_rows($prod_result_sql)) {
						$checked_value = (in_array($ListItem["categories_id"], $g2c_array) ? " checked " : "");
						$DisplayName = '<input type="checkbox" name=SelectedItem[] value="'.$ListItem["categories_id"].'" onClick="assign_selection(this); if (this.checked) { expandTree(aux'.$ListItem["categories_id"].');}" id=MSel_'.$ListItem["parent_id"].'_'.$ListItem["categories_id"]. $checked_value . '>' . $DisplayName;
						
						$HiddenItems[] = array('id' => $ListItem["categories_id"], 'value' => (tep_not_null($checked_value) ? 1 : 0));
					}
				}
				?>
				
				aux<?=$ListItem["categories_id"]?> = insFld(<?=($Level==1?"foldersTree":"aux".$ListItem["parent_id"])?>, gFld("<?
				// Text
				echo addslashes($DisplayName);?>",<?
				// Link
				if ($url) {?>
					"javascript:MyNewWindow(\"<?=$url?>\",\"Open\",<?=$this->PopupWinWidth?>,<?=$this->PopupWinHeight?>,\"yes\")"))
				<?} else {?>
					"javascript:undefined"))
				<?}?>
				aux<?=$ListItem["categories_id"]?>._readonly = 0;
				<?
				
				$SubTotal += tep_show_list_items($NewListItems, $Level+1) ;
			}
		}
		return $SubTotal ;
	}
	$g2c_array = array();
	$g2c_select_sql = "	SELECT categories_id 
						FROM " . TABLE_PRICE_GROUPS_TO_CATEGORIES . "
						WHERE price_groups_id='" . $_REQUEST["gID"] . "'";
	$g2c_result_sql = tep_db_query($g2c_select_sql);
	while ($g2c_row = tep_db_fetch_array($g2c_result_sql)) {
		$g2c_array[] = $g2c_row["categories_id"];
	}
	
	$ListItems = array() ;
	$HiddenItems = array();
	$parent_to_child = array();
	
	$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
						FROM " . TABLE_CATEGORIES . " AS c 
						INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
							ON c.categories_id = cd.categories_id 
						WHERE c.parent_id='0' 
							AND cd.language_id='" . (int)$languages_id ."' 
						ORDER BY sort_order, cd.categories_name " ;
	$cat_result_sql = tep_db_query($cat_select_sql);
	while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
		$ListItems[] = $cat_row ;
		$parent_to_child[] = $cat_row["categories_id"];
	}
	
	$matching_field_array = array( 	array ('id' => 0, "text" => "--------------- Select Field ---------------"),
									array ('id' => "products_name", "text" => "Products Name")
								);
?>
					<tr>
						<td>
<?
	echo tep_draw_form('assign_tag', FILENAME_PRICE_TAGS, tep_get_all_get_params(array('action')) . 'action=assign_tags_confirm', 'post', 'onSubmit="return assign_tag_form_checking();"');
	echo tep_draw_hidden_field("gID", $_REQUEST["gID"]);
	echo tep_draw_hidden_field("browser_id", '', ' id="browser_id" ');
?>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
			        				<td width="100%">
			    	   				<script language="javascript">
									<!--
									var browserName = navigator.appName; 
									if (browserName == 'Microsoft Internet Explorer')
										document.getElementById('browser_id').value = "IE";
									else
										document.getElementById('browser_id').value = "Non-IE";
									//-->
									</script>
										<script language="javascript" src="includes/javascript/Treeview/ua.js"></script>
										<script language="javascript" src="includes/javascript/Treeview/ftiens4.js"></script>
										<script>
											function checkAll(folderObj) {
												var childObj;
											    var i;
											
											    // Open folder
											    if (!folderObj.isOpen) {
											      	clickOnNodeObj(folderObj)
											    }
											}
											
											function expandTree(folderObj)
											{
											    var childObj;
											    var i;
											
											    // Open folder
											    if (!folderObj.isOpen)
											      	clickOnNodeObj(folderObj)
												/*
											    // Call this function for all folder children
											    for (i=0 ; i < folderObj.nChildren; i++)  {
											      childObj = folderObj.children[i]
											      if (typeof childObj.setState != "undefined" && !childObj._readonly) {//is folder
											        expandTree(childObj)
											      }
											    }*/
											}
													
											// Close all folders
											function collapseTree()
											{
												//hide all folders
												clickOnNodeObj(foldersTree)
												//restore first level
												clickOnNodeObj(foldersTree)
											}
											
											//Environment variables are usually set at the top of this file.
											USELINKFORFOLDER = 0
											USETEXTLINKS = 0
											STARTALLOPEN = 0
											USEFRAMES = 0
											USEICONS = 0
											WRAPTEXT = 1
											PRESERVESTATE = 1
											ICONPATH = 'includes/javascript/Treeview/'
											BUILDALL = 0
											HIGHLIGHT = 0;
											foldersTree = gFld("<?=TEXT_TOP?>&nbsp;&nbsp;[<a href=\"javascript:;\" onClick=\"setTreeCheckbox('assign_tag', 'MSel_-1_0', true, '<?=implode(",", $parent_to_child)?>');\">Check All</a>]&nbsp;&nbsp;[<a href=\"javascript:;\" onClick=\"setTreeCheckbox('assign_tag', 'MSel_-1_0', false, '<?=implode(",", $parent_to_child)?>');\">Uncheck All</a>]", "")
											<?
												$SubTotal = tep_show_list_items($ListItems, 1);
											?>
										</script>
										<a style="font-size:7pt;text-decoration:none;color:silver" href=http://www.treemenu.net/ target=_blank></a>
										<span class=formvalue>
											<script>initializeDocument()</script>
											<noscript>
											A tree for site navigation will open here if you enable JavaScript in your browser.
											</noscript>
										</span>
									</td>
								</tr>
								<tr>
									<td colspan="2" align="right">
										<?=tep_image_submit('button_update.gif', IMAGE_UPDATE, "") . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PRICE_TAGS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
							<?
								for ($i=0; $i<count($HiddenItems); $i++) {
									echo "<input type='hidden' name=HiddenCat[".$HiddenItems[$i]['id']."] value=".$HiddenItems[$i]['value']." id=HiddenCat_".$HiddenItems[$i]['id'].">\n";
								}
							?>
						</form>
						</td>
					</tr>
<?
}

$price_groups_select_sql = "SELECT * FROM " . TABLE_PRICE_GROUPS ;
$price_groups_result_sql = tep_db_query($price_groups_select_sql);

if (tep_db_num_rows($price_groups_result_sql)) {
?>
								<tr>
			            			<td valign="top">
			            				<table border="0" width="100%" cellspacing="1" cellpadding="2">
			               					<tr>
			               						<td class="reportBoxHeading" width="5%"><?="Action"?></td>
						       					<td class="reportBoxHeading"><?=TABLE_HEADING_PRICE_GROUPS_NAME?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_PRICE_GROUPS_DESCRIPTION?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_PRICE_TAGS?></td>
						   					</tr>
<?
	$row_count = 0;
	while ($price_groups_row = tep_db_fetch_array($price_groups_result_sql)) {
    	$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
    	
    	$count_cat_select_sql = " SELECT COUNT(categories_id) AS total_linked_cat FROM " . TABLE_PRICE_GROUPS_TO_CATEGORIES . " WHERE price_groups_id='" . $price_groups_row["price_groups_id"] . "'";
    	$count_cat_result_sql = tep_db_query($count_cat_select_sql);
    	if ($count_cat_row = tep_db_fetch_array($count_cat_result_sql)) {
    		$linked_cat_count = $count_cat_row["total_linked_cat"];
    	} else {
    		$linked_cat_count = 0;
    	}
?>
											<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
												<td class="reportRecords" valign="top">
													<table border="0" width="100%" cellspacing="0" cellpadding="0">
														<tr>
															<td valign="top">
																<a href="<?=tep_href_link(FILENAME_PRICE_TAGS, 'action=edit_price_groups&gID='.$price_groups_row["price_groups_id"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
																<a href="<?=tep_href_link(FILENAME_PRICE_TAGS, 'action=linking_to_cat&gID='.$price_groups_row["price_groups_id"])?>"><?=tep_image(DIR_WS_ICONS."linking.gif", "Assign categories to tag", "", "", 'align="top"')?></a>
																<a href="javascript:void(confirm_delete('<?=$price_groups_row["price_groups_name"]?>', 'Price Group', '<?=tep_href_link(FILENAME_PRICE_TAGS, 'action=delete_price_groups&gID='.$price_groups_row["price_groups_id"])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
															</td>
														</tr>
														<tr>
									        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									      				</tr>
														<tr>
															<td valign="bottom">
															<?
																if ($linked_cat_count) {	// got at least one category is linked
																	echo tep_draw_form('form_'.$price_groups_row["price_groups_id"], FILENAME_PRICE_TAGS, tep_get_all_get_params(array('action')) . 'action=update_product_price', 'post', 'onSubmit="return update_price_form_checking(\''.$price_groups_row["price_groups_id"].'\');"');
																	echo tep_draw_hidden_field("selected_gID", $price_groups_row["price_groups_id"]);
																	echo tep_draw_hidden_field("user_comment_".$price_groups_row["price_groups_id"], '', 'id="user_comment_'.$price_groups_row["price_groups_id"].'"');
																	echo tep_image_submit('button_update.gif', 'Update product\'s price for all categories assigned to this price group');
																	echo "</form>";
																}
															?>
															</td>
														</tr>
													</table>
												</td>
												<td class="reportRecords" valign="top"><?=$price_groups_row["price_groups_name"]?><br><br><?=sprintf(TEXT_TOTAL_LINKED_CATEGORIES, $linked_cat_count)?></td>
												<td class="reportRecords" valign="top"><?=$price_groups_row["price_groups_description"]?></td>
												<td class="reportRecords" valign="top">
													<table border="0" width="100%" cellspacing="0" cellpadding="2">
														<tr>
						          							<td colspan="7"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
						          						</tr>
						               					<tr>
						               						<td class="subRecordsBoxHeading" valign="top" width="8%">
																[<a href="<?=tep_href_link(FILENAME_PRICE_TAGS, 'action=new_price_tags&gID='.$price_groups_row["price_groups_id"])?>">Add New</a>]
															</td>
															<td class="subRecordsBoxHeading" width="12%"><?=TABLE_HEADING_PRICE_TAGS_FIELD?></td>
									       					<td class="subRecordsBoxHeading" width="22%"><?=TABLE_HEADING_PRICE_TAGS_NAME." (Priority)"?></td>
									       					<td class="subRecordsBoxHeading" width="17%"><?=TABLE_HEADING_PRICE_TAGS_UPDATING_FIELD?></td>
											                <td class="subRecordsBoxHeading" align="left" width="15%"><?=TABLE_HEADING_TAGS_UPDATING_VALUE?></td>
											                <td class="subRecordsBoxHeading" align="center" width="6%"><?=TABLE_HEADING_TAGS_LANGUAGE?></td>
											                <td class="subRecordsBoxHeading" width="20%"><?=TABLE_HEADING_PRICE_TAGS_DESCRIPTION?></td>
									   					</tr>
									   					<tr>
						          							<td colspan="7"><div style="border-bottom: 1px solid #996600; font-size: 1px; height: 1px"></div></td>
						          						</tr>
<?
		/*
		if ($seo_url_alias_category_permission) {
			$price_tags_select_sql = "SELECT * FROM " . TABLE_PRICE_TAGS . " WHERE price_groups_id = '" . $price_groups_row["price_groups_id"] . "' ORDER BY price_tags_order";
		} else {
			$price_tags_select_sql = "	SELECT * 
										FROM " . TABLE_PRICE_TAGS . " 
										WHERE price_groups_id = '" . $price_groups_row["price_groups_id"] . "'
											AND price_tags_update_field<>'products_url_alias' 
										ORDER BY price_tags_order";
		}*/
		$price_tags_select_sql = "SELECT * FROM " . TABLE_PRICE_TAGS . " WHERE price_groups_id = '" . $price_groups_row["price_groups_id"] . "' ";
		$price_tags_order_by_sql = "ORDER BY price_tags_order";
		if (!$seo_url_alias_category_permission){
			$price_tags_select_sql .= " AND price_tags_update_field<>'products_url_alias' ";
		}
		
		if (!$edit_price_permission){
			$price_tags_select_sql .= " AND price_tags_update_field<>'products_price' ";
		}
		
		if (!$edit_product_purchase_mode_permission){
			$price_tags_select_sql .= " AND price_tags_update_field<>'products_purchase_mode' ";
		}
		
		$price_tags_select_sql .= $price_tags_order_by_sql;
		
		$price_tags_result_sql = tep_db_query($price_tags_select_sql);
		$price_tag_count = 0;
		while ($price_tags_row = tep_db_fetch_array($price_tags_result_sql)) {
			$for_language = TEXT_OPTION_NOT_APPLICABLE;
			if ($price_tags_row["price_tags_update_field"] == "products_price") {
				$updating_value = $price_tags_row["tags_price"];
			} else {
				if ($price_tags_row["price_tags_update_field"] == 'products_purchase_mode') {
					$pmode_array = explode(':~:', $price_tags_row["tags_price"]);
					$updating_value = $updating_value_translation[$price_tags_row["price_tags_update_field"]][$pmode_array[0]];
					if ($pmode_array[0] == '2') {
						$updating_value .= '<br>' . TEXT_PRODUCTS_ETA . ' ' . (tep_not_null($pmode_array[1]) ? $pmode_array[1] : TEXT_FOLLOW_SYSTEM);
					} else if ($pmode_array[0] == '4') {
						$updating_value .= '<br>' . TEXT_PRODUCTS_ETA . ' ' . (tep_not_null($pmode_array[1]) ? $pmode_array[1] : TEXT_FOLLOW_SYSTEM);
						$updating_value .= '<br>' . TEXT_PRODUCTS_PRE_ORDER_LEVEL . ' ' . (tep_not_null($pmode_array[2]) ? $pmode_array[2] : TEXT_OPTION_NOT_APPLICABLE);
						$updating_value .= '<br>' . TEXT_PRODUCTS_OUT_OF_STOCK_LEVEL . ' ' . (tep_not_null($pmode_array[3]) ? $pmode_array[3] : TEXT_OPTION_NOT_APPLICABLE);
					}
				} else if ($price_tags_row["price_tags_update_field"] == 'products_pre_order_level') {
					$updating_value = is_numeric($price_tags_row["tags_price"]) ? $price_tags_row["tags_price"] : TEXT_OPTION_NOT_APPLICABLE;
				} else if ($price_tags_row["price_tags_update_field"] == 'products_image' || $price_tags_row["price_tags_update_field"] == 'products_description_image') {
					$updating_value = $price_tags_row["tags_price"] > 0 ? $price_tags_row["tags_price"] : 'REMOVE';
				} else if ( in_array($price_tags_row["price_tags_update_field"], array_keys($updating_value_translation)) ) {
					$updating_value = $updating_value_translation[$price_tags_row["price_tags_update_field"]][$price_tags_row["tags_price"]];
				} else {
					$updating_value = $price_tags_row["tags_price"];
				}
				
				if ($price_tags_row["price_tags_update_field"] == "products_description") {
					$tmp_lang_array = tep_get_language_info($price_tags_row["tags_update_language_id"]);
					if (isset($tmp_lang_array['text']) && tep_not_null($tmp_lang_array['text']))
						$for_language = $tmp_lang_array['text'];
					else
						$for_language = $price_tags_row["tags_update_language_id"];
				}
			}
			
			echo '										<tr>
															<td class="reportRecords" valign="top" nowrap>
																<a href="'.tep_href_link(FILENAME_PRICE_TAGS, 'action=edit_price_tags&tID='.$price_tags_row["price_tags_id"]).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"').'</a>
																<a href="javascript:void(confirm_delete(\''.$price_tags_row["price_tags_name"].'\', \'Price Tag\', \''.tep_href_link(FILENAME_PRICE_TAGS, 'action=delete_price_tags&tID='.$price_tags_row["price_tags_id"]).'\'))">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"').'</a>
															</td>
															<td class="reportRecords" valign="top">'.$price_tags_field_array[$price_tags_row["price_tags_field"]].'</td>
															<td class="reportRecords" valign="top">'.$price_tags_row["price_tags_name"].' ('.$price_tags_row["price_tags_order"].')</td>
															<td class="reportRecords" valign="top">'.$price_tags_field_array[$price_tags_row["price_tags_update_field"]].'</td>
															<td class="reportRecords" align="left" valign="top" nowrap>'.$updating_value.'</td>
															<td class="reportRecords" align="center" valign="top" nowrap>'.$for_language.'</td>
															<td class="reportRecords" valign="top">'.(tep_not_null($price_tags_row["price_tags_description"]) ? $price_tags_row["price_tags_description"] : "&nbsp;").'</td>
														</tr>';
			$price_tag_count++;
		}
		if (!$price_tag_count) {
			echo '										<tr><td colspan="6" class="reportRecords">No price tags is defined!</td></tr>';
		}
?>
													</table>
												</td>
											</tr>
<?		$row_count++;
    }
?>
            							</table>
            						</td>
            					</tr>
<?
}
?>
							<script language="javascript"><!--
								function price_groups_form_checking() {
									if (document.getElementById('price_groups_name').value == "") {
										alert('Please assign name to this price group!');
										return false;
									}
									return true;
								}
								
								function price_tags_form_checking() {
									if (document.getElementById('price_tags_field').selectedIndex < 1) {
										alert('Please select your matching field!');
										document.getElementById('price_tags_field').focus();
										return false;
									}
									
									if (document.getElementById('price_tags_name').value == "") {
										alert('Please assign name to this price tag!');
										document.getElementById('price_tags_name').focus();
										return false;
									}
									
									var updating_field = document.getElementById('price_tags_update_field');
									if (updating_field.selectedIndex < 1) {
										alert('Please select your updating field!');
										updating_field.focus();
										return false;
									}
									
									var updating_value = document.getElementById('tags_price');
									<? if ($seo_url_alias_category_permission) {?>
										var price_tags_update_field = document.getElementById('price_tags_update_field');
										if (price_tags_update_field.value == "products_url_alias") {
										
											if (validate_seo(updating_value)) {
												alert ('<?=JS_TEXT_SEO_SPECIAL_CHARACTER?>');
												updating_value.focus();
												updating_value.select();
												return false;
											}
										}
									<? } ?>
									
									if ( (updating_field.value != 'products_pre_order_level' && updating_field.value != 'products_add_to_cart_msg' && updating_field.value != 'products_preorder_msg' && updating_field.value != 'products_out_of_stock_msg') 
										 && (updating_value == null || trim_str(updating_value.value) == '')) {
										alert('Please enter updating value for this tag!');
										if (updating_value != null) updating_value.focus();
										return false;
									}
									
									if (updating_field.value == 'products_price') {
										if (!currencyValidation(updating_value.value)) {
											alert("'"+updating_value.value + "' is not a valid price amount!");
											updating_value.focus();
											updating_value.select();
											return false;
										}
									} else if (updating_field.value == 'products_status') {
										if (updating_value.value != 0 && updating_value.value != 1) {
											alert("Product status updating value must be either \"0\" (Inactive) or \"1\" (Active)!");
											updating_value.focus();
											updating_value.select();
											return false;
										}
									} else if (updating_field.value == 'products_sort_order' || updating_field.value == 'products_image' || updating_field.value == 'products_description_image') {
										if (!validateInteger(updating_value.value)) {
											if (updating_field.value == 'products_sort_order') {
												alert("Product sort order updating value must be non-negative integer value!");
											} else if (updating_field.value == 'products_image') {
												alert("Product small image updating value must be non-negative integer value!");
											} else if (updating_field.value == 'products_description_image') {
												alert("Product detail image updating value must be non-negative integer value!");
											}
											updating_value.focus();
											updating_value.select();
											return false;
										}
									} else if (updating_field.value == 'products_purchase_mode') {
										var purchase_mode = document.price_tags.tags_price;
										var selected_value = 0;
										for (var x=0; x < purchase_mode.length; x++) {
											if (purchase_mode[x].checked) {
												selected_value = purchase_mode[x].value;
											}
										}
										if (selected_value < 1 || selected_value > 4) {
											alert("Please select your product purchase mode updating value!");
											updating_value.focus();
											updating_value.select();
											return false;
										}
									} else if (updating_field.value == 'products_pre_order_level') {
										if (!validateSignInteger(updating_value.value) && trim_str(updating_value.value) != '') {
											alert("Product pre-order level updating value must be either integer or leave it blank!");
											updating_value.focus();
											updating_value.select();
											return false;
										}
									} else if (updating_field.value == 'products_eta') {
										if ( (!validateMayHvPtDecimal(updating_value.value, true) && updating_value.value.toUpperCase() != 'DEFAULT') || updating_value.value <= 0) {
											alert("Product ETA updating value must be either positive numeric value or \"DEFAULT\"!");
											updating_value.focus();
											updating_value.select();
											return false;
										}
									} else if (updating_field.value == 'products_payment_mature_period') {
										if ( !validateInteger(updating_value.value) || updating_value.value < 0) {
											alert("Product payment mature period updating value must be non-negative integer value!");
											updating_value.focus();
											updating_value.select();
											return false;
										}
									} else if (updating_field.value == 'products_negative_qty' || updating_field.value == 'products_skip_inventory') {
										if (updating_value.value != 0 && updating_value.value != 1) {
											alert( (updating_field.value == 'products_negative_qty' ? "Allow negative product quantity" : "Do not keep inventory") + " updating value must be either \"0\" (No) or \"1\" (Yes)!");
											updating_value.focus();
											updating_value.select();
											return false;
										}
									}
									
									if (document.getElementById('price_tags_order').value == "") {
										alert('Please assign priority value for this price tag!');
										document.getElementById('price_tags_order').focus();
										return false;
									} else if (!validateInteger(document.getElementById('price_tags_order').value) || document.getElementById('price_tags_order').value < 1) {
										alert('Priority value must be a positive integer!');
										document.getElementById('price_tags_order').focus();
										document.getElementById('price_tags_order').select();
										return false;
									}
									
									return true;
								}
								
								function assign_tag_form_checking() {
									return true;
								}
								
								function update_price_form_checking(selected_grp) {
									var user_msg = prompt("Leave any message for this update?","");
									if (user_msg != null) {
										document.getElementById('user_comment_'+selected_grp).value = user_msg;
										return true;
									} else {
										return false;
									}
								}
								
								function assign_selection(obj) {
									if (obj.checked == true) {
										document.getElementById('HiddenCat_'+obj.value).value = 1;
									} else {
										document.getElementById('HiddenCat_'+obj.value).value = 0;
									}
								}
								//-->
							</script>
            				</table>
            			</td>
            		</tr>
		    	</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>