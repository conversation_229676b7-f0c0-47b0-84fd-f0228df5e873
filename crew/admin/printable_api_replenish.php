<?
/*
  	$Id: printable_purchase_orders.php,v 1.1 2011/06/21 02:31:01 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
require_once(DIR_WS_CLASSES . 'payment_module_info.php');
require_once(DIR_WS_CLASSES . 'po_suppliers.php');
require_once(DIR_WS_CLASSES . 'api_replenish_payment.php');
require_once(DIR_WS_CLASSES . 'api_replenish_publishers.php');

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DTU_PAYMENT)) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_DTU_PAYMENT);
}

$currencies = new currencies();
//$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);

$edit_api_obj = new api_replenish_payment($_SESSION['login_id'], $_SESSION['login_email_address']);
if (!$edit_api_obj->load_po($_REQUEST, $messageStack)) {
	tep_redirect(tep_href_link(FILENAME_DTU_PAYMENT, tep_get_all_get_params(array('subaction'))));
}

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/print.css">
</head>

<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" width="100%">
<!-- header //-->
<!-- header_eof //-->

<!-- body //-->
<?
echo $edit_api_obj->printable_po_form();
?>
<!-- body_eof //-->

<!-- footer //-->
<script>
	window.print();
</script>
<!-- footer_eof //-->
<?
if (isset($_REQUEST['po_id'])) {
	// update printed status
	$edit_api_obj->update_po_print_status($_REQUEST['po_id']);
}
?>
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>