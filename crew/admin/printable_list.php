<?
/*
  	$Id: printable_list.php,v 1.3 2013/10/07 11:15:37 chingyen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
<? if ($action != "print_view") { ?>
		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
            					</tr>
            					<tr>
    								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  								</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?
}

if ($action == "print_view") {
	$prod_types_array = array();
	$prod_types_where_str = "1";
	if ($_REQUEST["product_types"] && is_array($_REQUEST["product_types"])) {
		if (in_array("ind", $_REQUEST["product_types"])) {
			$prod_types_array[] = "(p.products_bundle='' AND p.products_bundle_dynamic='')";
		}
		if (in_array("static", $_REQUEST["product_types"])) {
			$prod_types_array[] = "p.products_bundle='yes'";
		}
		if (in_array("dynamic", $_REQUEST["product_types"])) {
			$prod_types_array[] = "p.products_bundle_dynamic='yes'";
		}
		$prod_types_where_str = (count($prod_types_array)) ? "(".implode(" OR ", $prod_types_array).")" : " 1 ";
	}
	
	function tep_show_list_items($ListItems, $Level=0) {
		global $languages_id, $prod_types_where_str;
		$SubTotal=0;
		
		foreach ($ListItems as $ListItem)
		{
			$NewListItems = array() ;
			
			$p_rank = tep_check_cat_tree_permissions(FILENAME_CATEGORIES, $ListItem["categories_id"]);
			
			if ($p_rank > 0) {
				$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
									FROM " . TABLE_CATEGORIES . " AS c 
									INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON c.categories_id = cd.categories_id 
									WHERE c.parent_id='" . $ListItem["categories_id"] . "' 
										AND cd.language_id='" . (int)$languages_id ."' 
									ORDER BY sort_order, cd.categories_name " ;
				$cat_result_sql = tep_db_query($cat_select_sql);
				while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
					$NewListItems[] = $cat_row ;
				}
				
				$SubTotal += 1 ;
				
				$DisplayName = '<span class="boldText"><b>'.htmlspecialchars(strip_tags($ListItem["categories_name"])).'</b></span>';
				if (!$DisplayName) $DisplayName = "" ;
				?>
				
				aux<?=$ListItem["categories_id"]?> = insFld(<?=($Level==1?"foldersTree":"aux".$ListItem["parent_id"])?>, gFld("<?
				// Text
				echo addslashes($DisplayName);?>", "javascript:undefined"))
				aux<?=$ListItem["categories_id"]?>._readonly = 0;
				<?
				
				if ($p_rank == 1) {
					$prod_select_sql = "SELECT pd.products_id, pd.products_name 
										FROM " . TABLE_PRODUCTS . " AS p 
										INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
											ON p.products_id=pd.products_id 
										INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
											ON pd.products_id=p2c.products_id 
										WHERE p2c.categories_id='" . $ListItem["categories_id"] . "' 
											AND p2c.products_is_link=0 AND $prod_types_where_str " ;
					$prod_result_sql = tep_db_query($prod_select_sql);
					
					while ($prod_row = tep_db_fetch_array($prod_result_sql)) {
						$product_display_name = '<table width="80%"><tr><td width="30%" class="smallText">'.$prod_row["products_name"].'</td><td align="right" width="5%">'.$prod_row["products_id"].'</td><td class="smallText" width="65%">&nbsp;</tr></table>';
				?>
						aux_p<?=$prod_row["products_id"]?> = insFld(<?="aux".$ListItem["categories_id"]?>, gFld("<?
						// Text
						echo addslashes($product_display_name);
						?>", "javascript:undefined"))
						aux_p<?=$prod_row["products_id"]?>._readonly = 0;
				<?
					}
				}
				
				$SubTotal += tep_show_list_items($NewListItems, $Level+1) ;
			}
		}
		return $SubTotal ;
	}
	
	$ListItems = array() ;
	if ($_REQUEST["cat_id"] > 0) {
		$root_cat_select_sql = "SELECT c.categories_id, cd.categories_name, c.parent_id 
								FROM categories AS c, categories_description AS cd 
								WHERE c.categories_id = '" . $_REQUEST["cat_id"] . "'
		                        	AND c.categories_id = cd.categories_id 
		                        	AND cd.language_id='" . (int)$languages_id ."' order by sort_order, cd.categories_name";
	} else {
		$root_cat_select_sql = "SELECT c.categories_id, cd.categories_name, c.parent_id 
								FROM categories AS c, categories_description AS cd 
								WHERE c.parent_id = '" . $_REQUEST["cat_id"] . "'
		                        	AND c.categories_id = cd.categories_id 
		                        	AND cd.language_id='" . (int)$languages_id ."' order by sort_order, cd.categories_name";
	}
	
	$categories_query = tep_db_query($root_cat_select_sql);
	
	while ($categories = tep_db_fetch_array($categories_query)) {
		$ListItems[] = $categories ;
	}
	
	/*
	$cat_select_sql = "	SELECT c.categories_id, c.parent_id, cd.categories_name 
						FROM " . TABLE_CATEGORIES . " AS c 
						INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
							ON c.categories_id = cd.categories_id 
						WHERE c.parent_id='" . $categories["categories_id"] . "' 
							AND cd.language_id='" . (int)$languages_id ."' 
						ORDER BY sort_order, cd.categories_name " ;
	$cat_result_sql = tep_db_query($cat_select_sql);
	while ($cat_row = tep_db_fetch_array($cat_result_sql)) {
		$ListItems[] = $cat_row ;
	}*/
?>
			  		<tr>
			  			<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
			        				<td width="100%">
										<script language="javascript" src="includes/javascript/Treeview/ua.js"></script>
										<script language="javascript" src="includes/javascript/Treeview/ftiens4.js"></script>
										<script>
											function checkAll(folderObj) {
												var childObj;
											    var i;
											
											    // Open folder
											    if (!folderObj.isOpen) {
											      	clickOnNodeObj(folderObj)
											    }
											}
											
											function expandTree(folderObj)
											{
											    var childObj;
											    var i;
											
											    // Open folder
											    if (!folderObj.isOpen)
											      	clickOnNodeObj(folderObj)
											}
													
											// Close all folders
											function collapseTree()
											{
												//hide all folders
												clickOnNodeObj(foldersTree)
												//restore first level
												clickOnNodeObj(foldersTree)
											}
											
											//Environment variables are usually set at the top of this file.
											USELINKFORFOLDER = 0
											USETEXTLINKS = 0
											STARTALLOPEN = 1
											USEFRAMES = 0
											USEICONS = 0
											WRAPTEXT = 1
											PRESERVESTATE = 0
											ICONPATH = 'includes/javascript/Treeview/'
											BUILDALL = 0
											HIGHLIGHT = 0;
											foldersTree = gFld("Printable List", "")
											<?
												$SubTotal = tep_show_list_items($ListItems, 1);
											?>
										</script>
										<a style="font-size:7pt;text-decoration:none;color:silver" href=http://www.treemenu.net/ target=_blank></a>
										<span class=formvalue>
											<script>initializeDocument()</script>
											<noscript>
											A tree for site navigation will open here if you enable JavaScript in your browser.
											</noscript>
										</span>
									</td>
								</tr>
							</table>
						</td>
  					</tr>
<?
} else {
	$categories_array = tep_get_eligible_category_tree(FILENAME_CATEGORIES, 0, '___', '', $categories_array);
?>
					<?=tep_draw_form('printable_list_form', FILENAME_PRINTABLE_LIST, tep_get_all_get_params(array('action')) . 'action=print_view', 'post', ' target="printViewWin" onSubmit="return form_checking();"');?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="2" cellpadding="0">
								<tr>
									<td class="main" width="15%"><?=ENTRY_CATEGORY?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("cat_id", $categories_array, $_SESSION['best_param']["cat_id"], ' id="cat_id" ')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td class="main"><?=ENTRY_PRODUCT_TYPES?></td>
									<td class="main">
									<?	
										echo tep_draw_checkbox_field('product_types[]', 'ind', true, '', 'id="type_ind"') . "&nbsp;" . TEXT_UNIT_PRODUCT;
										echo "&nbsp;" . tep_draw_checkbox_field('product_types[]', 'static', true, '', 'id="type_static"') . "&nbsp;" . TEXT_STATIC_PRODUCT;
										echo "&nbsp;" . tep_draw_checkbox_field('product_types[]', 'dynamic', true, '', 'id="type_dynamic"') . "&nbsp;" . TEXT_DYNAMIC_PRODUCT;
									?>
									</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
							</table>
						</td>
					</tr>
					<tr>
	  					<td>
	  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
	  							<tr>
	  								<td width="20%">&nbsp;</td>
	  								<td align="right">
	  									<input type="submit" name="Print" value="Print List" title="Print product list based on criteria">
	  								</td>
	  							</tr>
	  						</table>
	  					</td>
	  				</tr>
	  				</form>
	  				<script language="javascript"><!--
						function form_checking() {
							if (document.getElementById('cat_id').value == '') {
								alert('Please select category!');
								document.getElementById('cat_id').focus();
								return false;
							}
							
							if (!document.getElementById('type_ind').checked && !document.getElementById('type_static').checked && !document.getElementById('type_dynamic').checked) {
								alert('Please include at least one product type!');
								return false;
							}
							
							return true;
						}
						//-->
					</script>
				</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
<?
}
?>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<?
if ($action == "print_view") {
?>
	<script>
		window.print();
	</script>
<?
} else {
	require(DIR_WS_INCLUDES . 'footer.php');
}
?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>