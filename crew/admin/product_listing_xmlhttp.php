<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');
require('includes/application_top.php');

$action = isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '';
$admin_id = isset($HTTP_GET_VARS['adm']) ? (int)$HTTP_GET_VARS['adm'] : '';
$cat_id = isset($HTTP_GET_VARS['cat_id']) ? (int)$HTTP_GET_VARS['cat_id'] : '';
$cp_id = isset($HTTP_GET_VARS['cp_id']) ? $HTTP_GET_VARS['cp_id'] : '';
$language_id = isset($HTTP_GET_VARS['lang']) ? (int)$HTTP_GET_VARS['lang'] : '';

if (isset($language_id) && tep_not_null($language_id)) {
	$language_dir_select_sql = "SELECT directory FROM " . TABLE_LANGUAGES . " WHERE  languages_id = '" . $language_id . "'";
	$language_dir_result_sql = tep_db_query($language_dir_select_sql);
	$language_dir_row = tep_db_fetch_array($language_dir_result_sql);

	if (file_exists(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/' . "orders_xmlhttp.php")) {
		include_once(DIR_WS_LANGUAGES . $language_dir_row["directory"] . '/' . "orders_xmlhttp.php");
	}
}

if (isset($admin_id) && $admin_id > 0) {
	$admin_group_select_sql = "SELECT admin_groups_id FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $admin_id . "'";
	$admin_group_result_sql = tep_db_query($admin_group_select_sql);
	$admin_group_row = tep_db_fetch_array($admin_group_result_sql);
	$admin_group_id = $admin_group_row["admin_groups_id"];
}

function save_categories_configuration($cat_str, $cat_cfg_update_array) {
    global $messageStack;

    if (is_numeric($cat_str)) {
		foreach ($cat_cfg_update_array as $cfg_key => $cfg_val) {
			$cat_configuration_select_sql = "	SELECT categories_configuration_id
												FROM " . TABLE_CATEGORIES_CONFIGURATION . "
												WHERE categories_configuration_key = '" . tep_db_input($cfg_key) . "'
													AND categories_id = '" . tep_db_input($cat_str) . "'";
			$cat_configuration_result_sql = tep_db_query($cat_configuration_select_sql);

			if (!tep_db_num_rows($cat_configuration_result_sql)) {
				// insert
				$cfg_default_value_select_sql = "SELECT *
												 FROM " . TABLE_CATEGORIES_CONFIGURATION . "
												 WHERE categories_configuration_key = '" . tep_db_input($cfg_key) . "'
												 	AND categories_id = 0 ";
				$cfg_default_value_result_sql = tep_db_query($cfg_default_value_select_sql);

				if ($cfg_default_value_row = tep_db_fetch_array($cfg_default_value_result_sql)) {
					$sql_data_array = array('categories_id' => $cat_str,
		                      				'categories_configuration_title' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_title"]),
		                      				'categories_configuration_key' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_key"]),
		                      				'categories_configuration_value' => $cfg_val,
		                      				'categories_configuration_description' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_description"]),
		                      				'categories_configuration_group_id' => $cfg_default_value_row["categories_configuration_group_id"],
		                      				'sort_order' => tep_db_prepare_input($cfg_default_value_row["sort_order"]),
		                      				'last_modified' => 'NULL',
		                      				'date_added' => 'now()',
		                      				'use_function' => tep_db_prepare_input($cfg_default_value_row["use_function"]),
		                      				'set_function' => tep_db_prepare_input($cfg_default_value_row["set_function"])
		                      				);

					tep_db_perform(TABLE_CATEGORIES_CONFIGURATION, $sql_data_array);
				}
			} else if (tep_db_num_rows($cat_configuration_result_sql) == 1) {
				// update
				$cat_configuration_update_sql = "	UPDATE " . TABLE_CATEGORIES_CONFIGURATION . "
													SET categories_configuration_value = '" . $cfg_val . "'
													WHERE categories_configuration_key = '" . tep_db_input($cfg_key) . "'
														AND categories_id = '" . tep_db_input($cat_str) . "'";
				tep_db_query($cat_configuration_update_sql);
			} else {
				$messageStack->add_session('More than one records found for this key: ' . $cfg_key, 'error');
			}
		}
	}
}

echo '<response>';

if (tep_not_null($action)) {
	switch($action) {
		case "custom_product_list":
			echo "<selection>";
			$categories_array = tep_get_category_tree_cacheable(0, '___', '', $categories_array, '', '', '', $cp_id);
			
			if (empty($default) && isset($GLOBALS[$name])) {
				$default = stripslashes($GLOBALS[$name]);
			}
			
			if (!is_array($default) && strcmp($default, (int)$default) === 0) {
				$default = (int)$default;
			}
			
			$selected = false;
			for ($i=0, $n=sizeof($categories_array); $i<$n; $i++) {
				if (isset($categories_array[$i]['type']) && $categories_array[$i]['type'] == 'optgroup') {
					echo  '<optgroup label="'.tep_output_string($categories_array[$i]['text'], array('"' => '&quot;', '\'' => '&#039;', '<' => '&lt;', '>' => '&gt;')).'"></optgroup>';
				} else {
					echo  '<option index="' . tep_output_string($categories_array[$i]['id']) . '"';
					if (strcmp($categories_array[$i]['id'], (int)$categories_array[$i]['id']) === 0) {
						$categories_array[$i]['id'] = (int)$categories_array[$i]['id'];
					}
					
					if (is_array($default)) {
						if (in_array($categories_array[$i]['id'], $default))	echo ' SELECTED';
					} else if ($default === $categories_array[$i]['id'] && !$selected) {
						echo ' SELECTED';
						$selected = true;
					}
					
					if (isset($categories_array[$i]['param']) && tep_not_null($categories_array[$i]['param']))	echo ' ' . $categories_array[$i]['param'];
					echo '><![CDATA[' . $categories_array[$i]['text'] . ']]></option>';
				}
			}
			echo "</selection>";

			break;
		case "product_list":
			echo "<selection>";

			$by_filename = tep_db_prepare_input($HTTP_GET_VARS['fname']);

			if (!tep_not_null($by_filename) || (tep_not_null($by_filename) && tep_check_cat_tree_permissions($by_filename, $cat_id, $admin_group_id) == 1)) {
				$products_values = tep_get_products_list_by_category($cat_id);
				foreach ($products_values as $product_array) {
					echo "<option index='".$product_array['id']."'><![CDATA[".strip_tags($product_array['name'])."]]></option>";
				}
			}

			echo "</selection>";
			break;
		case "single_product_list":
		case "non_package_product_list":
			echo "<selection>";
			$by_filename = tep_db_prepare_input($HTTP_GET_VARS['fname']);
			
			if (!tep_not_null($by_filename) || (tep_not_null($by_filename) && tep_check_cat_tree_permissions($by_filename, $cat_id, $admin_group_id) == 1)) {
				//filtering out static, dynamic and linked product
				$product_select_sql = "	SELECT p.products_id, pd.products_name
										FROM " . TABLE_CATEGORIES . " AS c, " . TABLE_CATEGORIES_DESCRIPTION . " AS cd, " . TABLE_PRODUCTS . " AS p, " . TABLE_PRODUCTS_DESCRIPTION . " AS pd, " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc
										WHERE pc.categories_id='".$cat_id."' 
											AND c.categories_id = cd.categories_id 
											AND pc.categories_id=c.categories_id AND pc.products_id=p.products_id
											AND p.products_id=pd.products_id 
											AND p.products_bundle<>'yes' 
											AND p.products_bundle_dynamic<>'yes' " . 
											($action == 'non_package_product_list' ? ' AND (p.custom_products_type_id<>3) ' : '') .
											($action == 'single_product_list' ? ' AND (p.custom_products_type_id=0 OR p.custom_products_type_id=2 OR p.custom_products_type_id=3) ' : '') . "
											AND cd.language_id ='" . (int)$language_id . "' 
											AND pd.language_id ='" . (int)$language_id . "' 
											AND pc.products_is_link=0
										ORDER BY p.products_sort_order, pd.products_name";
				$products = tep_db_query($product_select_sql);
				
				while ($products_values = tep_db_fetch_array($products)) {
					echo "<option index='".$products_values['products_id']."'><![CDATA[".strip_tags($products_values['products_name'])."]]></option>";
				}
			}
			echo "</selection>";
			
			break;
		case "get_package_qty":
			$cat_cfg_array = tep_get_cfg_setting($cat_id, 'catalog', '9', 'group_id');	// Get configuration values for Stock Options group

			if (count($cat_cfg_array)) {
				foreach ($cat_cfg_array as $cfg_key => $cfg_val) {
					echo "<cfg_key key='".$cfg_key."'><![CDATA[".$cfg_val."]]></cfg_key>";
				}
			}

			break;
		case "update_package_qty_2":
			$all_package_quantities = isset($HTTP_GET_VARS['all_package_quantities']) && trim($HTTP_GET_VARS['all_package_quantities']) == 'true' ? 1 : 0;
		    if ($all_package_quantities) {
		        $cat_cfg_update_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'] = 'all';
		    } else {
				$cat_cfg_update_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'] = tep_db_prepare_input($HTTP_GET_VARS['qty_str']);
		    }
			$cat_cfg_update_array['PRODUCT_NAME_FOR_BATCH_UPDATE'] = tep_db_prepare_input($HTTP_GET_VARS['prod_name']);
			save_categories_configuration($cat_id, $cat_cfg_update_array);
		    break;
		case "update_package_qty":
			$cat_str = $cat_id;
			$prod_str = tep_db_prepare_input($HTTP_GET_VARS['prod_name']);
			$qty_str = tep_db_prepare_input($HTTP_GET_VARS['qty_str']);
			$cat_cfg_update_array['PRODUCT_NAME_FOR_BATCH_UPDATE'] = $prod_str;
			$cat_cfg_update_array['PACKAGE_QUANTITY_FOR_BATCH_UPDATE'] = $qty_str;
			save_categories_configuration($cat_str, $cat_cfg_update_array);

			break;
		case "get_price_set":
			$price_set_id = tep_db_prepare_input($HTTP_GET_VARS['price_set_id']);
			$prod_str = tep_db_prepare_input($HTTP_GET_VARS['prod_str']);
			$requested_prod_array = explode(',', $prod_str);
			$configured_prod_array = array();

			$price_set_select_sql = "	SELECT psv.batch_update_price, pb.bundle_id
										FROM " . TABLE_BATCH_UPDATE_PRICE_SETS_VALUES . " AS psv
										INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
											ON psv.batch_update_qty=pb.subproduct_qty
										WHERE psv.batch_update_price_sets_id='".tep_db_input($price_set_id)."'
											AND pb.bundle_id IN (".$prod_str.")";
			$price_set_result_sql = tep_db_query($price_set_select_sql);
			echo "<price_set>";
			while ($price_set_row = tep_db_fetch_array($price_set_result_sql)) {
				$configured_prod_array[] = $price_set_row['bundle_id'];
				echo "<price id='".$price_set_row['bundle_id']."'><![CDATA[".$price_set_row['batch_update_price']."]]></price>";
			}
			$undefined_prod_array = array_diff($requested_prod_array, $configured_prod_array);	// NOTE: key are preserved so cant loop using for
			if (count($undefined_prod_array)) {
				foreach ($undefined_prod_array as $unsettle_prod) {
					echo "<price id='".$unsettle_prod."'><![CDATA[]]></price>";
				}
			}
			echo "</price_set>";
			break;
		case "get_price_selection":
			echo "<selection>";

			echo "<option index='0'><![CDATA[Custom]]></option>";

			$price_set_select_sql = "SELECT batch_update_price_sets_id, batch_update_price_sets_name FROM " . TABLE_BATCH_UPDATE_PRICE_SETS . " ORDER BY batch_update_price_sets_sort_order";
			$price_set_result_sql = tep_db_query($price_set_select_sql);
			while($price_set_row = tep_db_fetch_array($price_set_result_sql)) {
				echo "<option index='".$price_set_row["batch_update_price_sets_id"]."'><![CDATA[".$price_set_row["batch_update_price_sets_name"] . '(' .$price_set_row["batch_update_price_sets_id"]. ')'."]]></option>";
			}

			echo "<option index='comment' disabled='1'><![CDATA[----------]]></option>";
			echo "<option index='nps'><![CDATA[New price set ...]]></option>";
			echo "<option index='eps'><![CDATA[Edit price set ...]]></option>";
			echo "<option index='rps'><![CDATA[Refresh price set ...]]></option>";

			echo "</selection>";
			break;
                case "populate_catgory":
                        $categories_array = tep_get_eligible_category_tree(FILENAME_CATEGORIES, 0, '___', '', $categories_array, false, 0, true);
                        echo "<selection>";
                        foreach ($categories_array as $category) {
                            echo "<option value='" . $category['id'] . "'><![CDATA[" . strip_tags($category['text']) . "]]></option>";
                        }
                        echo "</selection>";
                        break;
        default:
            echo "<result>Unknown request!</result>";
            break;
    }
}

echo '</response>';
?>