<?php
/*
  $Id: products_flag.php,v 1.3 2006/03/21 05:38:02 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');

  $action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

  if (tep_not_null($action)) {
    switch ($action) {
      case 'insert':
      case 'save':
        $error = false;
        if (isset($HTTP_GET_VARS['mID'])) $products_flag_id = tep_db_prepare_input($HTTP_GET_VARS['mID']);
        $products_flag_name = tep_db_prepare_input($HTTP_POST_VARS['products_flag_name']);

        $sql_data_array = array('products_flag_name' => $products_flag_name);
       
        if (tep_not_null($products_flag_name)){
        $check_flags_name_query = tep_db_query("SELECT products_flag_name FROM " . TABLE_PRODUCTS_FLAG. " WHERE products_flag_name = '" . tep_db_input($products_flag_name) . "'");
        $check_duplicate = tep_db_num_rows($check_flags_name_query);
        if($check_duplicate < 1){
        if ($action == 'insert') {
          
          $insert_sql_data = array('date_added' => 'now()');

          $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

          tep_db_perform(TABLE_PRODUCTS_FLAG, $sql_data_array);
          $products_flag_id = tep_db_insert_id();
        } elseif ($action == 'save') {
          $update_sql_data = array('last_modified' => 'now()');

          $sql_data_array = array_merge($sql_data_array, $update_sql_data);

          tep_db_perform(TABLE_PRODUCTS_FLAG, $sql_data_array, 'update', "products_flag_id = '" . (int)$products_flag_id . "'");
        }

        if ($products_flag_image = new upload('products_flag_image', DIR_FS_CATALOG_IMAGES)) {
          tep_db_query("update " . TABLE_PRODUCTS_FLAG . " set products_flag_image = '" . $products_flag_image->filename . "' where products_flag_id = '" . (int)$products_flag_id . "'");
        }

        $languages = tep_get_languages();
        for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
          //$products_flag_url_array = $HTTP_POST_VARS['products_flag_url'];
          $language_id = $languages[$i]['id'];

          //$sql_data_array = array('products_flag_url' => tep_db_prepare_input($products_flag_url_array[$language_id]));

          if ($action == 'insert') {
            $insert_sql_data = array('products_flag_id' => $products_flag_id,
                                     'languages_id' => $language_id);

            $sql_data_array = array_merge($sql_data_array, $insert_sql_data);

            //tep_db_perform(TABLE_PRODUCTS_FLAG_INFO, $sql_data_array);
          } elseif ($action == 'save') {
            tep_db_perform(TABLE_PRODUCTS_FLAG, $sql_data_array, 'update', "products_flag_id = '" . (int)$products_flag_id . "' and languages_id = '" . (int)$language_id . "'");
          }
        }

        if (USE_CACHE == 'true') {
          tep_reset_cache_block('products_flag');
        }
        }else{
            $messageStack->add("Flags Name: " .$products_flag_name. " has been used");
            $error = true;
        }
        }else{
            $messageStack->add("Flags Name must be fill in"); 
            $error = true;
        }
        if(!$error){
        tep_redirect(tep_href_link(FILENAME_PRODUCTS_FLAG, (isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] . '&' : '') . 'mID=' . $products_flag_id));
        }
        break;
      case 'deleteconfirm':
        $products_flag_id = tep_db_prepare_input($HTTP_GET_VARS['mID']);

        if (isset($HTTP_POST_VARS['delete_image']) && ($HTTP_POST_VARS['delete_image'] == 'on')) {
          $products_flag_query = tep_db_query("select products_flag_image from " . TABLE_PRODUCTS_FLAG . " where products_flag_id = '" . (int)$products_flag_id . "'");
          $products_flag = tep_db_fetch_array($products_flag_query);

          $image_location = DIR_FS_DOCUMENT_ROOT . DIR_WS_CATALOG_IMAGES . $products_flag['products_flag_image'];

          if (file_exists($image_location)) @unlink($image_location);
        }

        tep_db_query("delete from " . TABLE_PRODUCTS_FLAG . " where products_flag_id = '" . (int)$products_flag_id . "'");
        tep_db_query("delete from " . TABLE_PRODUCTS_FLAG. " where products_flag_id = '" . (int)$products_flag_id . "'");

        if (isset($HTTP_POST_VARS['delete_products']) && ($HTTP_POST_VARS['delete_products'] == 'on')) {
          $products_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_FLAG . " where products_flag_id = '" . (int)$products_flag_id . "'");
          while ($products = tep_db_fetch_array($products_query)) {
            tep_remove_product($products['products_id']);
          }
        } else {
          tep_db_query("update " . TABLE_PRODUCTS_FLAG . " set products_flag_id = '' where products_flag_id = '" . (int)$products_flag_id . "'");
        }

        if (USE_CACHE == 'true') {
          tep_reset_cache_block('products_flag');
        }

        tep_redirect(tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page']));
        break;
    }
  }
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
  <tr>
    <td width="<?php echo BOX_WIDTH; ?>" valign="top"><table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
    </table></td>
<!-- body_text //-->
    <td width="100%" valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td width="100%"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr class="dataTableHeadingRow">
                <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_PRODUCTS_FLAG; ?></td>
                <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</td>
              </tr>
<?php
  $products_flag_query_raw = "select products_flag_id, products_flag_name, products_flag_image, date_added, last_modified from " . TABLE_PRODUCTS_FLAG . " order by products_flag_name";
  $products_flag_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $products_flag_query_raw, $products_flag_query_numrows);
  $products_flag_query = tep_db_query($products_flag_query_raw);
  while ($products_flag = tep_db_fetch_array($products_flag_query)) {
    if ((!isset($HTTP_GET_VARS['mID']) || (isset($HTTP_GET_VARS['mID']) && ($HTTP_GET_VARS['mID'] == $products_flag['products_flag_id']))) && !isset($mInfo) && (substr($action, 0, 3) != 'new')) {
      $products_flag_products_query = tep_db_query("select count(*) as products_count from " . TABLE_PRODUCTS_FLAG . " where products_flag_id = '" . (int)$products_flag['products_flag_id'] . "'");
      $products_flag_products = tep_db_fetch_array($products_flag_products_query);

      $mInfo_array = array_merge($products_flag, $products_flag_products);
      $mInfo = new objectInfo($mInfo_array);
    }

    if (isset($mInfo) && is_object($mInfo) && ($products_flag['products_flag_id'] == $mInfo->products_flag_id)) {
      echo '              <tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $products_flag['products_flag_id'] . '&action=edit') . '\'">' . "\n";
    } else {
      echo '              <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $products_flag['products_flag_id']) . '\'">' . "\n";
    }
?>
                <td class="dataTableContent"><?php echo $products_flag['products_flag_name']; ?></td>
                <td class="dataTableContent" align="right"><?php if (isset($mInfo) && is_object($mInfo) && ($products_flag['products_flag_id'] == $mInfo->products_flag_id)) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif'); } else { echo '<a href="' . tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $products_flag['products_flag_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              </tr>
<?php
  }
?>
              <tr>
                <td colspan="2"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                  <tr>
                    <td class="smallText" valign="top"><?php echo $products_flag_split->display_count($products_flag_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_PRODUCTS_FLAG); ?></td>
                    <td class="smallText" align="right"><?php echo $products_flag_split->display_links($products_flag_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?></td>
                  </tr>
                </table></td>
              </tr>
<?php
  if (empty($action)) {
?>
              <tr>
                <td align="right" colspan="2" class="smallText"><?php echo '<a href="' . tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->products_flag_id . '&action=new') . '">' . tep_image_button('button_insert.gif', IMAGE_INSERT) . '</a>'; ?></td>
              </tr>
<?php
  }
?>
            </table></td>
<?php
  $heading = array();
  $contents = array();

  switch ($action) {
    case 'new':
      $heading[] = array('text' => '<b>' . TEXT_HEADING_NEW_PRODUCTS_FLAG . '</b>');

      $contents = array('form' => tep_draw_form('products_flag', FILENAME_PRODUCTS_FLAG, 'action=insert', 'post', 'enctype="multipart/form-data"'));
      $contents[] = array('text' => TEXT_NEW_INTRO);
      $contents[] = array('text' => '<br>' . TEXT_PRODUCTS_FLAG_NAME . '<br>' . tep_draw_input_field('products_flag_name'));
      $contents[] = array('text' => '<br>' . TEXT_PRODUCTS_FLAG_IMAGE . '<br>' . tep_draw_file_field('products_flag_image'));

      $products_flag_inputs_string = '';
      $languages = tep_get_languages();
      for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
        $products_flag_inputs_string .= '<br>' . tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_flag_url[' . $languages[$i]['id'] . ']');
      }

      //$contents[] = array('text' => '<br>' . TEXT_PRODUCTS_FLAG_URL . $products_flag_inputs_string);
      $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_save.gif', IMAGE_SAVE) . ' <a href="' . tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $HTTP_GET_VARS['mID']) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      break;
    case 'edit':
      $heading[] = array('text' => '<b>' . TEXT_HEADING_EDIT_PRODUCTS_FLAG . '</b>');

      $contents = array('form' => tep_draw_form('products_flag', FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->products_flag_id . '&action=save', 'post', 'enctype="multipart/form-data"'));
      $contents[] = array('text' => TEXT_EDIT_INTRO);
      $contents[] = array('text' => '<br>' . TEXT_PRODUCTS_FLAG_NAME . '<br>' . tep_draw_input_field('products_flag_name', $mInfo->products_flag_name));
      $contents[] = array('text' => '<br>' . TEXT_PRODUCTS_FLAG_IMAGE . '<br>' . tep_draw_file_field('products_flag_image') . '<br>' . $mInfo->products_flag_image);

      $products_flag_inputs_string = '';
      $languages = tep_get_languages();
      for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
    //  $products_flag_inputs_string .= '<br>' . tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_flag_url[' . $languages[$i]['id'] . ']', tep_get_products_flag_url($mInfo->products_flag_id, $languages[$i]['id']));
      }

     //$contents[] = array('text' => '<br>' . TEXT_PRODUCTS_FLAG_URL . $products_flag_inputs_string);
      $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_save.gif', IMAGE_SAVE) . ' <a href="' . tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->products_flag_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      break;
    case 'delete':
      $heading[] = array('text' => '<b>' . TEXT_HEADING_DELETE_PRODUCTS_FLAG . '</b>');

      $contents = array('form' => tep_draw_form('products_flag', FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->products_flag_id . '&action=deleteconfirm'));
      $contents[] = array('text' => TEXT_DELETE_INTRO);
      $contents[] = array('text' => '<br><b>' . $mInfo->products_flag_name . '</b>');
      $contents[] = array('text' => '<br>' . tep_draw_checkbox_field('delete_image', '', true) . ' ' . TEXT_DELETE_IMAGE);

      if ($mInfo->products_count > 0) {
        //$contents[] = array('text' => '<br>' . tep_draw_checkbox_field('delete_products') . ' ' . TEXT_DELETE_PRODUCTS);
        $contents[] = array('text' => '<br>' . sprintf(TEXT_DELETE_WARNING_PRODUCTS, $mInfo->products_count));
      }

      $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->products_flag_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      break;
    default:
      if (isset($mInfo) && is_object($mInfo)) {
        $heading[] = array('text' => '<b>' . $mInfo->products_flag_name . '</b>');

        $contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->products_flag_id . '&action=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_PRODUCTS_FLAG, 'page=' . $HTTP_GET_VARS['page'] . '&mID=' . $mInfo->products_flag_id . '&action=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');
        $contents[] = array('text' => '<br>' . TEXT_DATE_ADDED . ' ' . tep_date_short($mInfo->date_added, PREFERRED_DATE_FORMAT));
        if (tep_not_null($mInfo->last_modified)) $contents[] = array('text' => TEXT_LAST_MODIFIED . ' ' . tep_date_short($mInfo->last_modified, PREFERRED_DATE_FORMAT));
        //$contents[] = array('text' => '<br>' . tep_info_image($mInfo->products_flag_image, $mInfo->products_flag_name));
        $contents[] = array('text' => '<br>' . TEXT_PRODUCTS . ' ' . $mInfo->products_count);
      }
      break;
  }

  if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
    echo '            <td width="25%" valign="top">' . "\n";

    $box = new box;
    echo $box->infoBox($heading, $contents);

    echo '            </td>' . "\n";
  }
?>
          </tr>
        </table></td>
      </tr>
    </table></td>
<!-- body_text_eof //-->
  </tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
