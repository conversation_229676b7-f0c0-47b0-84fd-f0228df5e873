<?php
require('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'json.php');

$product_instance_id = tep_db_prepare_input($_GET['product_instance_id']);
$hla_log = array();

if (tep_not_null($product_instance_id)) {
	if ( !isset($_SERVER['HTTP_REFERER']) ||
		(strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
		) {
		echo "You are not allowed to access from outside";
		exit;
	}
	
	$json = new Services_JSON();
	
	$log_select_sql = "	SELECT log_date, action, status, message, added_by 
						FROM " . TABLE_PRODUCTS_HLA_LOG . "
						WHERE products_hla_id = '" . tep_db_input($product_instance_id) . "' 
						ORDER BY products_hla_log_id";
	$log_result_sql = tep_db_query($log_select_sql);
	
	while ($log_row = tep_db_fetch_array($log_result_sql)) {
		$hla_log[] = array ('log_date' => $log_row['log_date'], 
							'action' => $log_row['action'], 
							'status' => $log_row['status'], 
							'message' => $log_row['message'], 
							'added_by' => $log_row['added_by'] 
							);
	}
	
	echo $json->encode($hla_log);
}

?>