<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$subaction = isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '';
$language_id = isset($_REQUEST['lang']) ? (int)$_REQUEST['lang'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : '';
$admin_id = isset($_SESSION['login_id']) ? $_SESSION['login_id'] : '';
$this_admin_email = isset($_SESSION['login_email_address']) ? $_SESSION['login_email_address'] : '';

$results = '';

if (file_exists(DIR_WS_LANGUAGES . $language . '/' . ".php")) {
	include_once(DIR_WS_LANGUAGES . $language . '/' . ".php");
}

if (tep_not_null($action)) {
	switch($action) {
		case "perform_tagging":
			$order_status_id = (int)$_REQUEST['status_id'];
			$setting_value = $_REQUEST['setting'];
			$order_ids_array = explode(',', $_REQUEST['o_str']);
			$list_mode = (int)$_REQUEST['list_mode'];
			echo "<tag_info>";
			if ($subaction == 'nt') {
				if (tep_not_null($setting_value)) {
					$tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_name = '" . tep_db_input($setting_value) . "' AND FIND_IN_SET('".$order_status_id."', orders_tag_status_ids) AND filename='".FILENAME_PRODUCTS_LOW_STOCK."';";
					$tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
					if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
						$lowstock_tag_id = (int)$tag_verify_row["orders_tag_id"];
					} else {
						$insert_sql_data = array(	'orders_tag_name' => tep_db_prepare_input($setting_value),
	                                   				'orders_tag_status_ids' => $order_status_id,
	                                   				'filename' => FILENAME_PRODUCTS_LOW_STOCK
	                                   			);
						tep_db_perform(TABLE_ORDERS_TAG, $insert_sql_data);
						$lowstock_tag_id = tep_db_insert_id();
					}
					
					$assign_lowstock_tag_update_sql = "UPDATE " . TABLE_PRODUCTS_LOW_STOCK . " SET low_stock_tag_ids = IF (low_stock_tag_ids='', '".$lowstock_tag_id."', CONCAT_WS(',', low_stock_tag_ids, '".$lowstock_tag_id."')) WHERE products_id IN (".implode(',', $order_ids_array).") AND NOT FIND_IN_SET('".$lowstock_tag_id."', low_stock_tag_ids)";
					tep_db_query($assign_lowstock_tag_update_sql);
					
					generateTagString($order_ids_array);
				}
			} else if ($subaction == 'at') {
				$tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int)$setting_value . "' AND FIND_IN_SET('".$order_status_id."', orders_tag_status_ids) AND filename='".FILENAME_PRODUCTS_LOW_STOCK."';";
				$tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
				if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
					// update all the selected orders with this tag
					$assign_lowstock_tag_update_sql = "UPDATE " . TABLE_PRODUCTS_LOW_STOCK . " SET low_stock_tag_ids = IF (low_stock_tag_ids='', '".(int)$setting_value."', CONCAT_WS(',', low_stock_tag_ids, '".(int)$setting_value."')) WHERE products_id IN (".implode(',', $order_ids_array).") AND NOT FIND_IN_SET('".(int)$setting_value."', low_stock_tag_ids)";
					tep_db_query($assign_lowstock_tag_update_sql);
					
					generateTagString($order_ids_array);
				}
			} else if ($subaction == 'rt') {
				$tag_verify_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id = '" . (int)$setting_value . "' AND FIND_IN_SET('".$order_status_id."', orders_tag_status_ids) AND filename='".FILENAME_PRODUCTS_LOW_STOCK."';";
				$tag_verify_result_sql = tep_db_query($tag_verify_select_sql);
				if ($tag_verify_row = tep_db_fetch_array($tag_verify_result_sql)) {
					// update all the selected orders by removing this tag from them
					$unassign_lowstock_tag_select_sql = "SELECT products_id, low_stock_tag_ids FROM " . TABLE_PRODUCTS_LOW_STOCK . " WHERE products_id IN (".implode(',', $order_ids_array).") AND FIND_IN_SET('".(int)$setting_value."', low_stock_tag_ids)";
					$unassign_lowstock_tag_result_sql = tep_db_query($unassign_lowstock_tag_select_sql);
					while ($unassign_lowstock_tag_row = tep_db_fetch_array($unassign_lowstock_tag_result_sql)) {
						$TagRemovePattern = "/(,)?".(int)$setting_value."(,)?/is";
						$new_tag_string = preg_replace($TagRemovePattern, ',', $unassign_lowstock_tag_row["low_stock_tag_ids"]);
						if (substr($new_tag_string, 0, 1) == ',') 	$new_tag_string = substr($new_tag_string, 1);
						if (substr($new_tag_string, -1) == ',') 	$new_tag_string = substr($new_tag_string, 0, -1);
						
						tep_db_query("UPDATE " . TABLE_PRODUCTS_LOW_STOCK . " SET low_stock_tag_ids='".$new_tag_string."' WHERE products_id='" . $unassign_lowstock_tag_row["products_id"] . "'");
					}
					
					generateTagString($order_ids_array);
				}
			}
			generateTagSelectionOptions($order_status_id, $_REQUEST['o_str'], ($list_mode == "2" ? true : false));
			echo "</tag_info>";
			break;
		case "retrieve_status_tags":
			generateTagSelectionOptions($_REQUEST['status'], '', '', true);
			break;
		case "refresh_tag_selection":
			$orders_tag_id = substr($_REQUEST['status'], 2);
			generateTagSelectionOptions($orders_tag_id, $_REQUEST['o_str']);
			break;
		default:
			echo "<result>Unknown request!</result>";
			
			break;
	}
}

function generateTagSelectionOptions($status, $order_pid_str, $whole_list=false, $apply_tag_sec_only = false) {
	global $language_id;
	$order_ids_array = tep_not_null($order_pid_str) ? explode(',', $order_pid_str) : array();
	echo "<selection>";
	$lowstock_status_id_select_sql = "SELECT low_stock_status FROM " . TABLE_PRODUCTS_LOW_STOCK . " WHERE low_stock_status = '" . (int)$status . "'";
	$lowstock_status_id_result_sql = tep_db_query($lowstock_status_id_select_sql);
	if ($lowstock_status_id_row = tep_db_fetch_array($lowstock_status_id_result_sql)) {
		if (!$apply_tag_sec_only) {
			echo "<option index=''><![CDATA[Order Lists Options ...]]></option>";
		}
		
		echo "<option index='' ".(!$apply_tag_sec_only ? "disabled='1'" : '')."><![CDATA[Apply tag:]]></option>";
		
		$mirror_for_delete_tag_str = '';
		$order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('".$lowstock_status_id_row["low_stock_status"]."', orders_tag_status_ids) AND filename='".FILENAME_PRODUCTS_LOW_STOCK."' ORDER BY orders_tag_name;";
		$order_tag_result_sql = tep_db_query($order_tag_select_sql);
		while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
			echo "<option index='".'otag_'.$order_tag_row["orders_tag_id"]."'><![CDATA[&nbsp;&nbsp;&nbsp;".$order_tag_row["orders_tag_name"]."]]></option>";
			if ($whole_list == true) {
				$mirror_for_delete_tag_str .= "<option index='".'rmtag_'.$order_tag_row["orders_tag_id"]."'><![CDATA[&nbsp;&nbsp;&nbsp;".$order_tag_row["orders_tag_name"]."]]></option>";
			}
		}
		
		if (!$apply_tag_sec_only) {
			echo "<option index='nt'><![CDATA[&nbsp;&nbsp;&nbsp;New tag ...]]></option>";
			
			if ($whole_list == true && tep_not_null($mirror_for_delete_tag_str)) {
				echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
				echo $mirror_for_delete_tag_str;
			} else {
				// select the common tags among those selected orders
				if (count($order_ids_array)) {
					$orders_tag_remove_select_sql = "SELECT DISTINCT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_PRODUCTS_LOW_STOCK . " AS sta WHERE products_id IN (".implode(',', $order_ids_array).") AND FIND_IN_SET(otag.orders_tag_id, sta.low_stock_tag_ids) AND filename='".FILENAME_PRODUCTS_LOW_STOCK."' ORDER BY orders_tag_name; ";
					$orders_tag_remove_result_sql = tep_db_query($orders_tag_remove_select_sql);
					if (tep_db_num_rows($orders_tag_remove_result_sql) > 0)
						echo "<option index='' disabled='1'><![CDATA[Remove tag:]]></option>";
					while ($orders_tag_remove_row = tep_db_fetch_array($orders_tag_remove_result_sql)) {
						echo "<option index='".'rmtag_'.$orders_tag_remove_row["orders_tag_id"]."'><![CDATA[&nbsp;&nbsp;&nbsp;".$orders_tag_remove_row["orders_tag_name"]."]]></option>";
					}
				}
			}
		}
	}
	echo "</selection>";
}

function generateTagString($order_ids_array) {
	echo "<tag_details>";
	for ($i=0; $i < count($order_ids_array); $i++) {
		$orders_tag_select_sql = "SELECT otag.orders_tag_name FROM " . TABLE_ORDERS_TAG . " AS otag, " . TABLE_PRODUCTS_LOW_STOCK . " AS sta WHERE sta.products_id  = '" . (int)$order_ids_array[$i] . "' AND FIND_IN_SET(otag.orders_tag_id, sta.low_stock_tag_ids) AND filename='".FILENAME_PRODUCTS_LOW_STOCK."';";
		$orders_tag_result_sql = tep_db_query($orders_tag_select_sql);
		$tags_str = '';
		while ($orders_tag_row = tep_db_fetch_array($orders_tag_result_sql)) {
			$tags_str .= $orders_tag_row["orders_tag_name"] . ', ';
		}
		if (substr($tags_str, -2) == ', ') 	$tags_str = substr($tags_str, 0, -2);
		echo "<order_tags order_id='".(int)$order_ids_array[$i]."'><![CDATA[".$tags_str."]]></order_tags>";
	}
	echo "</tag_details>";
}
?>