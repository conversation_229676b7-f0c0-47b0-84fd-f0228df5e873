<?php

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'products_payment_restrictions.php');

$pm_restrictions_obj = new products_payment_restrictions_rules($login_id, $login_email_address);
$header_title = HEADER_FORM_PRODUCTS_PAYMENT_RESTRICTIONS_TITLE;
$form_content = $pm_restrictions_obj->show_rules_list(FILENAME_PRODUCTS_PAYMENT_RESTRICTIONS);

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="includes/javascript/jquery.js"></script>
        <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
        <script language="javascript" src="includes/javascript/jquery.form.js"></script>
        <script language="javascript" src="includes/javascript/customer_xmlhttp.js"></script>
        <script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <!-- header //-->
        <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?= $header_title ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                    <tr>
                                        <td width="100%" valign="top"><?php echo $form_content; ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
</html>