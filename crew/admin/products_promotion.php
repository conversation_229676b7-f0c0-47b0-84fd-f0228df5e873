<?php
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'products_promotion.php');

global $messageToStack;

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$id = (isset($_REQUEST['id']) ? (int)$_REQUEST['id'] : '');
$prod_promo_obj = new products_promotion(); // Products Promotion Functions
$sel_action = (isset($_REQUEST['sel_action']) ? $_REQUEST['sel_action'] : '');	// 1. new - from new page || 2. edit - from edit page
$sid_array = ((isset($_REQUEST['sid']) && tep_not_null($_REQUEST["sid"])) ? explode(",", $_REQUEST["sid"]) : array());	// Static Product ID list base on selected single product

switch($action) {
	case "get_static_products":		// Window Popup - Display Static Product List
		$static_product_array = array();
		$product_id_array = array();
		
		// Get Static Product Name List
		$products_bundles_select_sql = " SELECT bundle_id 
									     FROM " . TABLE_PRODUCTS_BUNDLES . "
									     WHERE subproduct_id = " . $id;
		$products_bundles_result_sql = tep_db_query($products_bundles_select_sql);
		while ($products_bundles_row = tep_db_fetch_array($products_bundles_result_sql)) {
			$static_product_array[$products_bundles_row['bundle_id']] = strip_tags(tep_get_products_name($products_bundles_row['bundle_id']));
		}
		
		echo '<table cellpadding="10" align="left">';
		echo '<tr><td align="left"><dl>';
		
		if (tep_not_null($static_product_array)) {
			foreach($static_product_array as $product_id => $product_path) {
                // Check whether exist in selected static product list
				if (in_array($product_id, $sid_array)) {
					// Checked the previously selected checkbox
					echo '<div>' . tep_draw_checkbox_field('static_product', $product_id, true, '', 'id="static_product"') . $product_path . '</div>';
				} else {
                    // Check whether exist in product promotion table 
					if ($sel_action == "new") {
                        // Check whether static product already added in promotion
                        $products_id_select_sql = " SELECT products_id
                                                    FROM " . TABLE_PRODUCTS_PROMOTION . "
                                                    WHERE products_id = " . $product_id . "
                                                    LIMIT 1";
                        $products_id_result_sql = tep_db_query($products_id_select_sql);
                        
                        if (tep_db_num_rows($products_id_result_sql)) {
                            // Already Add To Promotion - disabled checkbox
                            echo '<div>' . tep_draw_checkbox_field('static_product', $product_id, false, '', 'disabled') . $product_path . '</div>';
                        } else {
                            echo '<div>' . tep_draw_checkbox_field('static_product', $product_id, false, '', 'id="static_product"') . $product_path . '</div>';
                        }
					} else {
						echo '<div>' . tep_draw_checkbox_field('static_product', $product_id, false, '', 'id="static_product"') . $product_path . '</div>';
					}
				}
			}
		} else {
			echo TEXT_PRODUCT_PROMOTION_STATIC_PRODUCT_NOT_FOUND;
		}
		
		echo '</dl></td></tr>';
		echo '</table>';
		return;
		
		break;
		
	case "add_new_product":
		// Perform Add Product Promotion
		$result = $prod_promo_obj->addEditProduct('add');
		if ($result == "success") {
			unset($_SESSION['promotion_lists_param']);
    		$form_content = $prod_promo_obj->menuListing();
		} else {
			$messageStack->add_session($result, 'error');
			$_SESSION['promotion_lists_param']['product_id'] = $_REQUEST['product_id'];
			$_SESSION['promotion_lists_param']['promotions_image_title1'] = $_REQUEST['promotions_image_title'][1];
			$_SESSION['promotion_lists_param']['promotions_image_title2'] = $_REQUEST['promotions_image_title'][2];
			$_SESSION['promotion_lists_param']['promotions_image_title3'] = $_REQUEST['promotions_image_title'][3];
			$_SESSION['promotion_lists_param']['start_date'] = $_REQUEST['start_date'];
			$_SESSION['promotion_lists_param']['end_date'] = $_REQUEST['end_date'];
			$_SESSION['promotion_lists_param']['only_promo_box'] = $_REQUEST['only_promo_box'];
			$_SESSION['promotion_lists_param']['limited_stock'] = $_REQUEST['limited_stock'];
			$_SESSION['promotion_lists_param']['limited_stock_quatity'] = $_REQUEST['limited_stock_quatity'];
			$_SESSION['promotion_lists_param']['promotion_status'] = $_REQUEST['promotion_status'];
		    tep_redirect(tep_href_link(FILENAME_PRODUCT_PROMOTIONS, tep_get_all_get_params(array('action')) . 'action=new_product_promotion'));
		}
		break;
	
	case "update_product":
		// Perform Edit Product Promotion
		$result = $prod_promo_obj->addEditProduct('edit');
		if ($result == "success") {
			$form_content = $prod_promo_obj->menuListing();
		} else {
			$messageStack->add_session($result, 'error');
			tep_redirect(tep_href_link(FILENAME_PRODUCT_PROMOTIONS, tep_get_all_get_params(array('action')) . 'action=edit_product_promotion'));
		}
		break;
		
	case "new_product_promotion":
	    // Display Add Product Promotion Page
		$result = "success";
		$form_content = $prod_promo_obj->productPromotion('new', $result, $id);
		break;
		
	case "edit_product_promotion":
		// Display Edit Product Promotion Page
		$result = "success";
		$form_content = $prod_promo_obj->productPromotion('edit', $result, $id);
		break;
		
	case "delete":
	 	// Perform Delete Product Promotion
		$result = $prod_promo_obj->deleteEntry($id);
		$form_content = $prod_promo_obj->menuListing();
		break;
		
	case "clean_exp_product":
		// Perform Delete Expired Product 
		$result = $prod_promo_obj->delExpProductPromotion();
		$form_content = $prod_promo_obj->menuListing();
		break;
		
	default:
		// Display Product Promotion Page
		$form_content = $prod_promo_obj->menuListing();
		break;
}

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
	<script language="javascript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
	<script language="javascript" src="includes/javascript/php.js"></script>
	<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
	<script language="javascript" src="includes/javascript/jquery.selectboxes.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
			<td width="<?php echo BOX_WIDTH; ?>" valign="top">
				<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
			<!-- left_navigation //-->
			<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
			<!-- left_navigation_eof //-->
				</table>
			</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	 				<tr>
		 				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>