<?php
/*
  	$Id: products_purchase_quantity.php,v 1.28 2009/04/22 04:30:22 chan Exp $

  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture

  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require(DIR_WS_CLASSES . 'log.php');

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$subaction = (isset($_GET['subaction']) ? $_GET['subaction'] : '');

$restock_account_info_permission = tep_admin_files_actions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'SUPPLIER_RESTOCK_ACCOUNT_INFO');
$purchase_list_permission = tep_admin_files_actions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'SUPPLIER_PURCHASE_LIST');

$log_object = new log_files($login_id);

if (tep_not_null($action)) {
	switch ($action) {
		case "edit_acc_set":
		case "delete_acc_set":
			if (!$restock_account_info_permission) {
				tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
			} else {
				$list_cat_select_sql = "SELECT restock_character_sets_id
										FROM " . TABLE_RESTOCK_CHARACTER_SETS . "
										WHERE restock_character_sets_id='".(int)$_REQUEST["rcsID"]."'";
				$list_cat_result_sql = tep_db_query($list_cat_select_sql);

				if ($list_cat_row = tep_db_fetch_array($list_cat_result_sql)) {
					if (tep_check_cat_tree_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, $list_cat_row["products_purchases_lists_cat_id"]) != 1) {
						$messageStack->add_session(ERROR_LIST_MAIN_CAT_ACCESS_DENIED, 'error');
						tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
					}
				} else {
					$messageStack->add_session(ERROR_LIST_NOT_EXISTS, 'error');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
				}
			}

			break;
		case "manage_rstk":
		case "add_rstk":
		case "edit_rstk":
			if (!$restock_account_info_permission) {
				tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
			}
			
			break;
		case "edit_list":
		case "delete_list":
			if (!$purchase_list_permission) {
				tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
			} else {
				$list_cat_select_sql = "SELECT products_purchases_lists_cat_id
										FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . "
										WHERE products_purchases_lists_id='".(int)$_REQUEST["lID"]."'";
				$list_cat_result_sql = tep_db_query($list_cat_select_sql);

				if ($list_cat_row = tep_db_fetch_array($list_cat_result_sql)) {
					if (tep_check_cat_tree_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, $list_cat_row["products_purchases_lists_cat_id"]) != 1) {
						$messageStack->add_session(ERROR_LIST_MAIN_CAT_ACCESS_DENIED, 'error');
						tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
					}
				} else {
					$messageStack->add_session(ERROR_LIST_NOT_EXISTS, 'error');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
				}
			}

			break;
		case "add_list":
			if (!$purchase_list_permission) {
				tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
			}

			break;
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
		case "confirm_delete_list":
			$supplier_pricing_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PRICING . " WHERE products_purchases_lists_id = '" . (int)$_GET["lID"] . "'";
			tep_db_query($supplier_pricing_delete_sql);

			$supplier_pricing_setting_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PRICING_SETTING . " WHERE products_purchases_lists_id = '" . (int)$_GET["lID"] . "'";
			tep_db_query($supplier_pricing_setting_delete_sql);

			$supplier_group_time_setting_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_LIST_TIME_SETTING . " WHERE products_purchases_lists_id = '" . (int)$_GET["lID"] . "'";
			tep_db_query($supplier_group_time_setting_delete_sql);

			$supplier_purchase_mode_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PURCHASE_MODES . " WHERE products_purchases_lists_id = '" . (int)$_GET["lID"] . "'";
			tep_db_query($supplier_purchase_mode_delete_sql);
			
			
			$products_purchases_lists_delete_sql = "	DELETE FROM " . TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS . "
														WHERE products_purchases_lists_id = '" . (int)$_GET["lID"] . "'";
			tep_db_query($products_purchases_lists_delete_sql);
			
			$product_purchase_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_PURCHASES . " WHERE products_purchases_lists_id = '" . (int)$_GET["lID"] . "'";
			tep_db_query($product_purchase_delete_sql);

			$purchase_list_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id = '" . (int)$_GET["lID"] . "'";
			tep_db_query($purchase_list_delete_sql);
			
			$purchase_list_setting_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_PURCHASES_LISTS_SETTING . " WHERE products_purchases_lists_id = '" . (int)$_GET["lID"] . "'";
            tep_db_query($purchase_list_setting_delete_sql);
			
			$supplier_cart_basket_delete_sql = "DELETE FROM " . TABLE_BUYBACK_BASKET . " WHERE products_purchases_lists_id = '" . (int)$_GET["lID"] . "'";
			tep_db_query($supplier_cart_basket_delete_sql);
			
			$messageStack->add_session(SUCCESS_LIST_DELETED, 'success');
			tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));

			break;
		case "confirm_delete_rstk":
			$rstk_characters_set_delete_sql = "	DELETE FROM " . TABLE_RESTOCK_CHARACTER_SETS . "
												WHERE restock_character_sets_id = '" . (int)$_REQUEST["rcsID"] . "'";
			tep_db_query($rstk_characters_set_delete_sql);

			$rstk_characters_info_delete_sql = "DELETE FROM " . TABLE_RESTOCK_CHARACTER_INFO . "
												WHERE restock_character_sets_id = '" . (int)$_REQUEST["rcsID"] . "'";
			tep_db_query($rstk_characters_info_delete_sql);

			$supplier_pricing_setting_update_sql = "UPDATE ". TABLE_SUPPLIER_PRICING_SETTING ." SET supplier_pricing_setting_value='' 
													WHERE supplier_pricing_setting_key='rstk_char_set' 
														AND supplier_pricing_setting_value='". $_REQUEST['rcsID'] ."'";
			tep_db_query($supplier_pricing_setting_update_sql);

			$lists_name_rstk_name_select_sql = "	SELECT ppl.products_purchases_lists_name
												 	FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ." AS rcs2pl
												 	INNER JOIN ". TABLE_PRODUCTS_PURCHASES_LISTS ." AS ppl
												 		ON (rcs2pl.products_purchases_lists_id=ppl.products_purchases_lists_id)
												 	WHERE restock_character_sets_id='". (int)$_REQUEST["rcsID"] ."'
												 	ORDER BY ppl.products_purchases_lists_name";
			$lists_name_rstk_name_result_sql = tep_db_query($lists_name_rstk_name_select_sql);
			while ($lists_name_rstk_name_row = tep_db_fetch_array($lists_name_rstk_name_result_sql)) {
				$messageStack->add_session(sprintf(WARNING_CHARACTER_SET_PURCHASE_LIST_DELETED, $_REQUEST['rstk_name'], $lists_name_rstk_name_row['products_purchases_lists_name']), 'warning');
			}

			$restock_character_to_products_purchases_lists_delete_sql = "	DELETE FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ."
																	 		WHERE restock_character_sets_id='". (int)$_REQUEST["rcsID"] ."'";
			tep_db_query($restock_character_to_products_purchases_lists_delete_sql);
			
			$messageStack->add_session(SUCCESS_CHARACTER_SET_DELETED, 'success');
			tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=manage_rstk'));

			break;
		case "insert_list_info":
		case "update_list_info":
			if (tep_not_null($_POST["purchase_list_name"])) {
				$name_replace = ereg_replace_dep(" ", "", strtolower($_POST["purchase_list_name"]));

				if ($subaction == "insert_list_info") {
					$check_list_name_select_sql = "SELECT products_purchases_lists_name FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE LOWER(REPLACE(products_purchases_lists_name, ' ', '')) = '" . tep_db_input($name_replace) . "'";
					$check_list_name_result_sql = tep_db_query($check_list_name_select_sql);
	          		$check_duplicate = tep_db_num_rows($check_list_name_result_sql);
	          		if ($check_duplicate > 0) {
	          			$messageStack->add(ERROR_LIST_NAME_USED);
	          		} else {
	          			if (tep_check_cat_tree_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, $_POST["purchases_lists_cat_id"]) != 1) {
							$messageStack->add(ERROR_LIST_MAIN_CAT_ACCESS_DENIED, 'error');
						} else {
		          			/****************************************************************************************
		          				Commented below lines to allow overlapping of categories.
		          			****************************************************************************************/
		          			/*
		          			$main_cat_path = tep_get_generated_category_path_ids($_POST["purchases_lists_cat_id"]);
		          			$path_array = tep_not_null($main_cat_path) ? explode('_', $main_cat_path) : array();

		          			$category_array = array($_POST["purchases_lists_cat_id"]);
		          			tep_get_subcategories($category_array, $_POST["purchases_lists_cat_id"]);

		          			$merged_category_array = array_merge($path_array, $category_array);

		          			$check_list_cat_select_sql = "	SELECT COUNT(products_purchases_lists_id) AS occur
		          											FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . "
		          											WHERE products_purchases_lists_cat_id IN ('".implode("', '", $merged_category_array)."')";
							$check_list_cat_result_sql = tep_db_query($check_list_cat_select_sql);
							$check_list_cat_row = tep_db_fetch_array($check_list_cat_result_sql);
			          		if ($check_list_cat_row['occur'] > 0) {
			          			$messageStack->add(ERROR_LIST_CAT_USED);
			          		} else {
			          		*/
								$purchase_list_data_array = array(	'products_purchases_lists_name' => tep_db_prepare_input($_POST["purchase_list_name"]),
																	'products_purchases_lists_cat_id' => $_POST["purchases_lists_cat_id"],
																	'products_purchases_lists_qty_round_up' => tep_db_prepare_input($_POST["products_purchases_lists_qty_round_up"]),
																	'products_purchases_lists_sort_order' => tep_not_null($_POST["products_purchases_lists_sort_order"]) ? (int)$_POST["products_purchases_lists_sort_order"] : 50000,
																	'products_purchases_lists_reference_date' => 'now()',
																 	'products_purchases_lists_last_modified' => 'now()'
																 );
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS, $purchase_list_data_array);
								$new_list_id = tep_db_insert_id();

								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $new_list_id,
								                                           'products_purchases_lists_setting_key' => 'ppls_inventory_days',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["ppls_inventory_days"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $new_list_id,
								                                           'products_purchases_lists_setting_key' => 'ppls_sales_start_date',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["ppls_sales_start_date"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $new_list_id,
								                                           'products_purchases_lists_setting_key' => 'ppls_sales_end_date',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["ppls_sales_end_date"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $new_list_id,
								                                           'products_purchases_lists_setting_key' => 'ppls_last_n_days_sales',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["ppls_last_n_days_sales"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $new_list_id,
								                                           'products_purchases_lists_setting_key' => 'ppls_sales_retrieval_method',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["rdoSalesRetrievalMethod"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								$messageStack->add_session(SUCCESS_LIST_ADDED, 'success');

								tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=edit_list&lID='. $new_list_id));
							//}
						}
					}
				} else {
					$check_list_name_select_sql = "SELECT products_purchases_lists_name FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id <> " . (int)$_GET["lID"] . " and LOWER(REPLACE(products_purchases_lists_name, ' ', '')) = '" . tep_db_input($name_replace) . "'";
					$check_list_name_result_sql = tep_db_query($check_list_name_select_sql);
	          		$check_duplicate = tep_db_num_rows($check_list_name_result_sql);
	          		if ($check_duplicate > 0) {
	          			$messageStack->add(ERROR_LIST_NAME_USED, 'error');
	          		} else {
	          			if (tep_check_cat_tree_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, $_POST["purchases_lists_cat_id"]) != 1) {
							$messageStack->add(ERROR_LIST_MAIN_CAT_ACCESS_DENIED, 'error');
						} else {
		          			/****************************************************************************************
		          				Commented below lines to allow overlapping of categories.
		          			****************************************************************************************/
		          			/*
		          			$main_cat_path = tep_get_generated_category_path_ids($_POST["purchases_lists_cat_id"]);
		          			$path_array = tep_not_null($main_cat_path) ? explode('_', $main_cat_path) : array();
		          			*/

		          			$category_array = array($_POST["purchases_lists_cat_id"]);
							tep_get_subcategories($category_array, $_POST["purchases_lists_cat_id"]);
							/*
		          			$merged_category_array = array_merge($path_array, $category_array);

		          			$check_list_cat_select_sql = "	SELECT COUNT(products_purchases_lists_id) AS occur
		          											FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . "
		          											WHERE products_purchases_lists_id<>'" . (int)$_GET["lID"] . "' AND products_purchases_lists_cat_id IN ('".implode("', '", $merged_category_array)."')";
							$check_list_cat_result_sql = tep_db_query($check_list_cat_select_sql);
							$check_list_cat_row = tep_db_fetch_array($check_list_cat_result_sql);
			          		if ($check_list_cat_row['occur'] > 0) {
			          			$messageStack->add(ERROR_LIST_CAT_USED);
			          		} else {
			          		*/
		          				$syn_purchase_product_select_sql = "SELECT pp.products_id
																	FROM " . TABLE_PRODUCTS_PURCHASES . " as pp
																	LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
																		ON (pp.products_id=p2c.products_id)
																	WHERE pp.products_purchases_lists_id='" . (int)$_GET["lID"] . "' AND p2c.categories_id NOT IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 ;";
								$syn_purchase_product_result_sql = tep_db_query($syn_purchase_product_select_sql);
								while ($syn_purchase_product_row = tep_db_fetch_array($syn_purchase_product_result_sql)) {
									tep_db_query("DELETE FROM " . TABLE_PRODUCTS_PURCHASES . " WHERE products_id = '" . tep_db_input($syn_purchase_product_row["products_id"]) . "' AND products_purchases_lists_id = '" . (int)$_GET["lID"] . "'");
								}
								
								tep_get_parent_categories($parent_categories_array, $_REQUEST["purchases_lists_cat_id"]); //check category that have same parent
								$parent_categories_array = array_merge($_REQUEST["purchases_lists_cat_id"], $parent_categories_array);
								
								$check_category_select_sql = "	SELECT rcs2pl.restock_character_sets_id, ppl.products_purchases_lists_name, rcs.restock_character_sets_name
																FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ." AS rcs2pl
																INNER JOIN ". TABLE_RESTOCK_CHARACTER_SETS ." AS rcs
																	ON (rcs2pl.restock_character_sets_id=rcs.restock_character_sets_id)
																INNER JOIN	". TABLE_PRODUCTS_PURCHASES_LISTS ." AS ppl
																	ON (rcs2pl.products_purchases_lists_id=ppl.products_purchases_lists_id )
																WHERE rcs2pl.products_purchases_lists_id = '". (int)$_REQUEST["lID"] ."'
																	AND rcs.restock_character_sets_cat_id NOT IN ('" . implode("', '", $parent_categories_array) ."')";
								$check_category_result_sql = tep_db_query($check_category_select_sql);
								
								while ($check_category_row = tep_db_fetch_array($check_category_result_sql)) {
									tep_db_query("DELETE FROM " . TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS . " WHERE restock_character_sets_id = '" . $check_category_row["restock_character_sets_id"] . "' AND products_purchases_lists_id = '" . (int)$_REQUEST["lID"] . "'"); //remove rstk character from purchase lists
									$messageStack->add_session(sprintf(WARNING_CHARACTER_SET_PURCHASE_LIST_DELETED, $check_category_row["restock_character_sets_name"], $check_category_row["products_purchases_lists_name"]), 'warning');
								}
								
								$purchase_list_data_array = array(	'products_purchases_lists_name' => tep_db_prepare_input($_POST["purchase_list_name"]),
																	'products_purchases_lists_cat_id' => $_POST["purchases_lists_cat_id"],
																	'products_purchases_lists_qty_round_up' => tep_db_prepare_input($_POST["products_purchases_lists_qty_round_up"]),
																	'products_purchases_lists_sort_order' => tep_not_null($_POST["products_purchases_lists_sort_order"]) ? (int)$_POST["products_purchases_lists_sort_order"] : 50000,
																 	'products_purchases_lists_last_modified' => 'now()'
																 );
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS, $purchase_list_data_array, 'update', "products_purchases_lists_id = '" . tep_db_input($_GET["lID"]) . "'");
								
								$purchase_list_setting_delete_sql = "DELETE FROM " . TABLE_PRODUCTS_PURCHASES_LISTS_SETTING . " WHERE products_purchases_lists_id='{$_GET["lID"]}' AND products_purchases_lists_setting_key IN ('ppls_inventory_days', 'ppls_sales_start_date', 'ppls_sales_end_date', 'ppls_sales_retrieval_method', 'ppls_last_n_days_sales')";
								$purchase_list_setting_result_sql = tep_db_query($purchase_list_setting_delete_sql);

								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $_GET["lID"],
								                                           'products_purchases_lists_setting_key' => 'ppls_inventory_days',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["ppls_inventory_days"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $_GET["lID"],
								                                           'products_purchases_lists_setting_key' => 'ppls_sales_start_date',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["ppls_sales_start_date"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $_GET["lID"],
								                                           'products_purchases_lists_setting_key' => 'ppls_sales_end_date',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["ppls_sales_end_date"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $_GET["lID"],
								                                           'products_purchases_lists_setting_key' => 'ppls_last_n_days_sales',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["ppls_last_n_days_sales"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								$purchase_list_setting_data_array = array ('products_purchases_lists_id' => $_GET["lID"],
								                                           'products_purchases_lists_setting_key' => 'ppls_sales_retrieval_method',
								                                           'products_purchases_lists_setting_value' => tep_db_prepare_input($_POST["rdoSalesRetrievalMethod"]));
								tep_db_perform(TABLE_PRODUCTS_PURCHASES_LISTS_SETTING, $purchase_list_setting_data_array);
								
								// Synchronise supplier pricing
								$syn_supplier_pricing_select_sql = "SELECT DISTINCT sp.products_id
																	FROM " . TABLE_SUPPLIER_PRICING . " AS sp
																	LEFT JOIN " . TABLE_PRODUCTS_PURCHASES . " AS pp
																		ON (sp.products_purchases_lists_id=pp.products_purchases_lists_id AND sp.products_id=pp.products_id)
																	WHERE sp.products_purchases_lists_id='" . (int)$_GET["lID"] . "' AND pp.products_id IS NULL";
								$syn_supplier_pricing_result_sql = tep_db_query($syn_supplier_pricing_select_sql);
								while ($syn_supplier_pricing_row = tep_db_fetch_array($syn_supplier_pricing_result_sql)) {
									tep_db_query("DELETE FROM " . TABLE_SUPPLIER_PRICING . " WHERE products_id = '" . tep_db_input($syn_supplier_pricing_row["products_id"]) . "' AND products_purchases_lists_id = '" . (int)$_GET["lID"] . "'");
								}

								$messageStack->add_session(SUCCESS_LIST_UPDATED, 'success');
								tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=edit_list&lID=' . $_GET["lID"]));
							//}
						}
					}
				}
			} else {
				if ($subaction == "insert_list_info") {
					$messageStack->add(ERROR_EMPTY_LIST_NAME, 'error');
				} else {
					$messageStack->add_session(ERROR_EMPTY_LIST_NAME, 'error');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=edit_list&lID='. $_GET["lID"]));
				}
			}

			break;
		case "update_quantity":
			$list_cat_select_sql = "SELECT products_purchases_lists_cat_id
									FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . "
									WHERE products_purchases_lists_id='".(int)$_REQUEST["lID"]."'";
			$list_cat_result_sql = tep_db_query($list_cat_select_sql);

			if ($list_cat_row = tep_db_fetch_array($list_cat_result_sql)) {
				if (tep_check_cat_tree_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, $list_cat_row["products_purchases_lists_cat_id"]) != 1) {
					$messageStack->add_session(ERROR_LIST_MAIN_CAT_ACCESS_DENIED, 'error');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
					break;
				}
			} else {
				$messageStack->add_session(ERROR_LIST_NOT_EXISTS, 'error');
				tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY));
				break;
			}

			if ($HTTP_POST_VARS['btn_csv_import']) {
				if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
					if ($_FILES['csv_import']["size"] > 0) {
						$import_error = false;
						$filename = ($_FILES['csv_import']['tmp_name']);
					    $handle = fopen($filename, 'r+');

					    $must_have_field = array(TABLE_HEADING_PRODUCT_ID => 0, TABLE_HEADING_OVERWRITE_MAX_QTY => 0, TABLE_HEADING_PURCHASE_QUANTITY => 0);
					    if (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    	$header_exists_count = 0;
					    	for ($i=0; $i < count($data); $i++) {
					    		if (in_array(trim($data[$i]), array_keys($must_have_field))) {
					    			$must_have_field[trim($data[$i])] = $i;
					    			$header_exists_count++;
					    		}
					    	}

					    	if ($header_exists_count != count($must_have_field)) {
					    		$messageStack->add_session("Some required fields does not exists. Please ensure your imported csv file contains " . implode(", ", array_keys($must_have_field)) . ".", 'error');
					    		$import_error = true;
					    	}
					    }

					    if (!$import_error) {
					    	$exist_product_id_array = array();
					    	$imported_products_array = array();

					    	$list_info_select_sql = "	SELECT products_purchases_lists_cat_id
														FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id='".(int)$_REQUEST["lID"]."'";
							$list_info_result_sql = tep_db_query($list_info_select_sql);
					    	if ($list_info_row = tep_db_fetch_array($list_info_result_sql)) {
						    	$category_array = array($list_info_row["products_purchases_lists_cat_id"]);
								tep_get_subcategories($category_array, $list_info_row["products_purchases_lists_cat_id"]);
								
						    	$products_list_select_product = "	SELECT p.products_id as prd_id, p.products_quantity, p.products_actual_quantity, pd.products_name, p.products_cat_path, p.products_main_cat_id, pp.*
																	FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
																	INNER JOIN " . TABLE_PRODUCTS . " AS p
																		ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
																	INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
																		ON p2c.products_id=pd.products_id
																	LEFT JOIN ".TABLE_PRODUCTS_PURCHASES." as pp
																		ON (p2c.products_id=pp.products_id AND pp.products_purchases_lists_id='".(int)$_REQUEST["lID"]."')
																	WHERE p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "'
																	ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
								$products_list_result_product = tep_db_query($products_list_select_product);
						  		while ($products_list_row = tep_db_fetch_array($products_list_result_product)) {
						  			$exist_product_id_array[$products_list_row["prd_id"]] = $products_list_row;
								}
							}

					    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    		if (trim($data[0]) == '') {	// Assume this row is useless
				    				continue;
				    			}
								
					    		$product_id_str = $data[$must_have_field[TABLE_HEADING_PRODUCT_ID]];
								$overwrite_max_qty_str = $data[$must_have_field[TABLE_HEADING_OVERWRITE_MAX_QTY]];
								$purchase_qty_str = $data[$must_have_field[TABLE_HEADING_PURCHASE_QUANTITY]];
								
					    		if (!in_array((int)$product_id_str, array_keys($exist_product_id_array))) {
					    			$messageStack->add(sprintf(ERROR_IMPORTED_PRODUCT_NOT_MATCH, $product_id_str), 'error');
					    			$import_error = true;
					    		} else {
					    			$imported_products_array[] = array(	'prd_id' => (int)$product_id_str,
											    						'overwrite_maximum_quantity' => (int)$overwrite_max_qty_str,
											    						'purchase_quantity' => $purchase_qty_str,
											    						'resource' => $exist_product_id_array[(int)$product_id_str]
									    								);

					    			$product_name = $exist_product_id_array[(int)$product_id_str]['products_cat_path']." > ".$exist_product_id_array[(int)$product_id_str]['products_name'];
									
						    		if (tep_not_null($purchase_qty_str)) {
						    			if (!preg_match('/[1-9][0-9]*/is', $purchase_qty_str) && $purchase_qty_str != '0') {
							    			$messageStack->add(sprintf(ERROR_INVALID_PURCHASE_QUANTITY, $product_name), 'error');
							    			$import_error = true;
							    		}
						    		}
						    	}
					    	}
							
					    	fclose($handle);
					    } else {
					    	fclose($handle);
					    	tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
					    }
					} else {
						$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
						tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
					}
				} else {
					$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
				}
			} else if ($HTTP_POST_VARS['btn_csv_export']) {
				$export_csv_array = tep_array_unserialize($HTTP_POST_VARS["serialized_export_csv_array"]);
				$export_csv_data = '';
				
				if (count($export_csv_array)) {
					foreach ($export_csv_array as $pid => $res) {
						$tmp_cvs_data_array = array();
						for ($i=0; $i < count($res); $i++) {
							$tmp_cvs_data_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res[$i]) . '"';
						}
						$export_csv_data .= implode(',', $tmp_cvs_data_array) . "\n";
					}
				}
				
				if (tep_not_null($export_csv_data)) {
					$filename = 'purchase_list_'.date('YmdHis').'.csv';
					$mime_type = 'text/x-csv';
					// Download
			        header('Content-Type: ' . $mime_type);
			        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			        // IE need specific headers
			        if (PMA_USR_BROWSER_AGENT == 'IE') {
			            header('Content-Disposition: inline; filename="' . $filename . '"');
			            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
			            header('Pragma: public');
			        } else {
			            header('Content-Disposition: attachment; filename="' . $filename . '"');
			            header('Pragma: no-cache');
			        }
					echo $export_csv_data;
					exit();
				} else {
					$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
				}
			} else if ($HTTP_POST_VARS['btn_latest_qty']) {
				$maxQtyArr = $_POST['maxQty'];
				$arrDisabled = $_POST['chkDisabled'];
				
				if (count($maxQtyArr)) {
					$exist_product_id_array = array();
					
					$list_info_select_sql = "	SELECT products_purchases_lists_cat_id
												FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id='".(int)$_REQUEST["lID"]."'";
					$list_info_result_sql = tep_db_query($list_info_select_sql);
			    	if ($list_info_row = tep_db_fetch_array($list_info_result_sql)) {
				    	$category_array = array($list_info_row["products_purchases_lists_cat_id"]);
						tep_get_subcategories($category_array, $list_info_row["products_purchases_lists_cat_id"]);

				    	$valid_products_select_sql = "	SELECT p.products_id
														FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
														INNER JOIN " . TABLE_PRODUCTS . " AS p
															ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
														WHERE p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 ;";
						$valid_products_result_sql = tep_db_query($valid_products_select_sql);
				  		while ($valid_products_row = tep_db_fetch_array($valid_products_result_sql)) {
				  			$exist_product_id_array[] = $valid_products_row["products_id"];
						}
					}
					
				  	foreach ($maxQtyArr as $product_id => $max_qty) {
				  		if (in_array($product_id, $exist_product_id_array)) {
					  		$pricing_product_select_sql = "SELECT products_id FROM " . TABLE_PRODUCTS_PURCHASES . " WHERE products_purchases_lists_id = '" . tep_db_input($_REQUEST["lID"]) . "' AND products_id = '" . tep_db_input($product_id) . "'";
							$pricing_product_result_sql = tep_db_query($pricing_product_select_sql);
							$pricing_product_exists = tep_db_num_rows($pricing_product_result_sql) ? true : false;

							$isdisabled = (int)$arrDisabled[$product_id];
                            $max_quantity_isoverwrite = '';
                            if (tep_not_null((string)$max_qty) && !$isdisabled) {
				  		        $max_quantity_isoverwrite = 1;
				  		    }
							
							$purchase_data_array = array(	'products_purchases_lists_id' => (int)$_REQUEST["lID"],
															'products_id' => $product_id,
															'products_purchases_max_quantity' => (int)$max_qty,
															'products_purchases_max_quantity_overwrite' => $max_quantity_isoverwrite,
														 	'products_purchases_disabled' => $isdisabled
														 );
							
							if ($pricing_product_exists) {
								tep_db_perform(TABLE_PRODUCTS_PURCHASES, $purchase_data_array, 'update', "products_purchases_lists_id = '" . tep_db_input($_REQUEST["lID"]) . "' AND products_id = '" . tep_db_input($product_id) . "'");
							} else {
								tep_db_perform(TABLE_PRODUCTS_PURCHASES, $purchase_data_array);
							}
						}
				  	}
				}
				tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction')) . 'subaction=suggest_qty'));
			} else {
				$product_ids = $_POST['product_ids'];
				$maxQtyArr = $_POST['maxQty'];
				$sugQtyArr = $_POST['sugQty'];
				$sellingQtyArr = $_POST['sellingQty'];
				$arrDisabled = $_POST['chkDisabled'];

				// save to database
				if (count($product_ids)) {
					$exist_product_id_array = array();

					$list_info_select_sql = "	SELECT products_purchases_lists_cat_id
												FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id='".(int)$_REQUEST["lID"]."'";
					$list_info_result_sql = tep_db_query($list_info_select_sql);
			    	if ($list_info_row = tep_db_fetch_array($list_info_result_sql)) {
				    	$category_array = array($list_info_row["products_purchases_lists_cat_id"]);
						tep_get_subcategories($category_array, $list_info_row["products_purchases_lists_cat_id"]);

				    	$valid_products_select_sql = "	SELECT p.products_id
														FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
														INNER JOIN " . TABLE_PRODUCTS . " AS p
															ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
														WHERE p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 ;";
						$valid_products_result_sql = tep_db_query($valid_products_select_sql);
				  		while ($valid_products_row = tep_db_fetch_array($valid_products_result_sql)) {
				  			$exist_product_id_array[] = $valid_products_row["products_id"];
						}
					}

				  	foreach ($product_ids as $product_id) {
				  		if (in_array($product_id, $exist_product_id_array)) {
							$pricing_product_select_sql = "SELECT products_id FROM " . TABLE_PRODUCTS_PURCHASES . " WHERE products_purchases_lists_id = '" . tep_db_input($_REQUEST["lID"]) . "' AND products_id = '" . tep_db_input($product_id) . "'";
							$pricing_product_result_sql = tep_db_query($pricing_product_select_sql);
							$pricing_product_exists = tep_db_num_rows($pricing_product_result_sql) ? true : false;

							$max_qty = $maxQtyArr[$product_id];
							$suggest_qty = (int)$sugQtyArr[$product_id];
							if (tep_not_null($sellingQtyArr[$product_id])) {
								$selling_qty = (int)$sellingQtyArr[$product_id];
								$qty_overwrite = 1;
							} else {
								$selling_qty = $suggest_qty;
								$qty_overwrite = 0;
							}

							$isdisabled = (int)$arrDisabled[$product_id];
                            $max_quantity_isoverwrite = '';
                            if (tep_not_null((string)$max_qty) && !$isdisabled) {
				  		        $max_quantity_isoverwrite = 1;
				  		    }

							$purchase_data_array = array(	'products_purchases_lists_id' => (int)$_REQUEST["lID"],
															'products_id' => $product_id,
															'products_purchases_max_quantity' => (int)$max_qty,
															'products_purchases_suggest_quantity' => $suggest_qty,
															'products_purchases_selling_quantity' => $selling_qty,
															'products_purchases_quantity_overwrite' => $qty_overwrite,
															'products_purchases_max_quantity_overwrite' => $max_quantity_isoverwrite,
														 	'products_purchases_disabled' => $isdisabled
														 );

							if ($pricing_product_exists) {
								tep_db_perform(TABLE_PRODUCTS_PURCHASES, $purchase_data_array, 'update', "products_purchases_lists_id = '" . tep_db_input($_REQUEST["lID"]) . "' AND products_id = '" . tep_db_input($product_id) . "'");
							} else {
								tep_db_perform(TABLE_PRODUCTS_PURCHASES, $purchase_data_array);
							}
						}
					}

					if (isset($_POST["reset_list_reference_date"]) && $_POST["reset_list_reference_date"] == '1') {
						// Updating the reference date
						tep_db_query("UPDATE " . TABLE_PRODUCTS_PURCHASES_LISTS . " SET products_purchases_lists_reference_date = '" . tep_db_input(date('Y-m-d H:i:s')) . "', products_purchases_lists_last_modified = now() WHERE products_purchases_lists_id = '".tep_db_input($_REQUEST["lID"])."'");
						$messageStack->add_session(SUCCESS_GET_FRESH_LIST, 'success');
					}

					$messageStack->add_session(SUCCESS_LIST_UPDATE, 'success');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
				} else {
					$messageStack->add_session(WARNING_NO_PRODUCTS_TO_UPDATE, 'warning');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY), tep_get_all_get_params(array('subaction')));
				}
			}

			break;
		case "update_acc_set":
			if ($HTTP_POST_VARS['btn_csv_import']) {
				if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
					if ($_FILES['csv_import']["size"] > 0) {
						$import_error = false;
						$filename = ($_FILES['csv_import']['tmp_name']);
					    $handle = fopen($filename, 'r+');

					    $must_have_field = array(TABLE_CSV_HEADING_PRODUCT_ID => 0);
					    $set_header_array = array();
					    if (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    	$header_exists_count = 0;
					    	for ($i=0; $i < count($data); $i++) {
					    		if (in_array(trim($data[$i]), array_keys($must_have_field))) {
					    			$must_have_field[trim($data[$i])] = $i;
					    			$header_exists_count++;
					    		} else {
					    			if ($data[$i] != TABLE_CSV_HEADING_PRODUCT && count($set_header_array) < 1) {
					    				//$set_header_array[$data[$i]] = $i;
					    				$set_header_array[$_REQUEST["rcsID"]] = $i;
					    			}
					    		}
					    	}
							
					    	if ($header_exists_count != count($must_have_field)) {
					    		$messageStack->add_session("Some required fields does not exists. Please ensure your imported csv file contains " . implode(", ", array_keys($must_have_field)) . ".", 'error');
					    		$import_error = true;
					    	}

					    	if (!count($set_header_array)) {
					    		$messageStack->add_session("There is no any restock character set to be imported!", 'error');
					    		$import_error = true;
					    	} else {
					    		foreach ($set_header_array as $set_name => $index) {
					    			if ($set_name != 'NEW SET') {
						    			//$character_set_select_sql = "SELECT restock_character_sets_id FROM " . TABLE_RESTOCK_CHARACTER_SETS . " WHERE products_purchases_lists_id = '" . (int)$_REQUEST["lID"] . "' AND restock_character_sets_name = '" . tep_db_input($set_name) . "'";
						    			$character_set_select_sql = "SELECT restock_character_sets_id FROM " . TABLE_RESTOCK_CHARACTER_SETS . " WHERE restock_character_sets_id = '" . (int)$_REQUEST["rcsID"] . "'";
		    							$character_set_result_sql = tep_db_query($character_set_select_sql);

		    							if (!tep_db_num_rows($character_set_result_sql)) {
		    								//$messageStack->add_session($set_name . " restock character set does not exists!", 'error');
		    								$messageStack->add_session("The restock character set does not exists!", 'error');
						    				$import_error = true;
		    							}
		    						}
	    						}
	    					}
					    }

					    if (!$import_error) {
					    	$exist_product_id_array = array();
					    	$imported_products_array = array();

					    	//$list_info_select_sql = "SELECT products_purchases_lists_cat_id FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id='".(int)$_REQUEST["lID"]."'";
					    	$list_info_select_sql = "SELECT restock_character_sets_cat_id FROM " . TABLE_RESTOCK_CHARACTER_SETS . " WHERE restock_character_sets_id = '" . (int)$_REQUEST["rcsID"] . "'";
							$list_info_result_sql = tep_db_query($list_info_select_sql);
					    	
					    	if ($list_info_row = tep_db_fetch_array($list_info_result_sql)) {
						    	/*
						    	$category_array = array($list_info_row["products_purchases_lists_cat_id"]);
								tep_get_subcategories($category_array, $list_info_row["products_purchases_lists_cat_id"]);
								*/
								tep_get_subcategories($category_array, $list_info_row['restock_character_sets_cat_id']);
								$category_array = array_merge($list_info_row['restock_character_sets_cat_id'], $category_array);
								/*
						    	$products_list_select_product = "	SELECT p.products_id as prd_id, pd.products_name, p.products_cat_path, pp.products_purchases_disabled
																	FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
																	INNER JOIN " . TABLE_PRODUCTS . " AS p
																		ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
																	INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
																		ON p2c.products_id=pd.products_id
																	LEFT JOIN ".TABLE_PRODUCTS_PURCHASES." as pp
																		ON (p2c.products_id=pp.products_id AND pp.products_purchases_lists_id='".(int)$_REQUEST["lID"]."')
																	WHERE p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "'
																	ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
								*/
								$products_list_select_product = " 	SELECT p2c.products_id as prd_id, p.products_cat_path
																	FROM ". TABLE_PRODUCTS_TO_CATEGORIES ." AS p2c
																	INNER JOIN ". TABLE_PRODUCTS ." AS p
																		ON (p.products_id=p2c.products_id)
																	INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
																		ON (p.products_id=pd.products_id)
																	WHERE p.products_bundle='' 
																		AND p.products_bundle_dynamic=''
																		AND p.custom_products_type_id=0
																		AND pd.language_id = '" . $languages_id . "'
																		AND p2c.categories_id IN ('" . implode("', '", $category_array) ."')
																	ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order";
								$products_list_result_product = tep_db_query($products_list_select_product);
						  		while ($products_list_row = tep_db_fetch_array($products_list_result_product)) {
						  			$exist_product_id_array[$products_list_row["prd_id"]] = $products_list_row;
								}
							}

					    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    		if (trim($data[0]) == '') {	// Assume this row is useless
				    				continue;
				    			}

					    		$product_id_str = $data[$must_have_field[TABLE_HEADING_PRODUCT_ID]];
					    		if (!in_array((int)$product_id_str, array_keys($exist_product_id_array))) {
					    			$messageStack->add(sprintf(ERROR_IMPORTED_PRODUCT_NOT_MATCH_RSTK, $product_id_str), 'error');
					    			$import_error = true;
					    		} else if ($exist_product_id_array[(int)$product_id_str]['products_purchases_disabled'] == '1') {
					    			$messageStack->add(sprintf(ERROR_IMPORTED_PRODUCT_IS_DISABLED, $product_id_str), 'warning');
					    			$import_error = true;
					    		} else {
					    			$set_info_array = array();

					    			foreach ($set_header_array as $set_name => $index) {
					    				$set_info_array[$set_name] = $data[$index];
					    			}

					    			$set_info_array[] = $data[$index];

					    			$imported_products_array[] = array(	'prd_id' => (int)$product_id_str,
											    						'resource' => $exist_product_id_array[(int)$product_id_str],
											    						'set_info' => $set_info_array
									    								);
						    	}
					    	}

					    	fclose($handle);
					    	/*
					    	if ($import_error) {
					    		tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
					    	}*/
					    } else {
					    	fclose($handle);
					    	tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
					    }
					} else {
						$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
						tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
					}
				} else {
					$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
				}
			} else if ($HTTP_POST_VARS['btn_csv_export']) {
				$export_csv_array = tep_array_unserialize($HTTP_POST_VARS["serialized_export_csv_array"]);
				$export_csv_data = '';
				
				str_replace(" ", '',$HTTP_POST_VARS['restock_character_sets_name']);
				
				if (count($export_csv_array)) {
					foreach ($export_csv_array as $pid => $res) {
						$tmp_cvs_data_array = array();
						for ($i=0; $i < count($res); $i++) {
							$tmp_cvs_data_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res[$i]) . '"';
						}
						$export_csv_data .= implode(',', $tmp_cvs_data_array) . "\n";
					}
				}

				if (tep_not_null($export_csv_data)) {
					$filename = 'rstk_characters_' . str_replace(" ", '',$HTTP_POST_VARS['restock_character_sets_name']) .'_'. date('YmdHis').'.csv';
					$mime_type = 'text/x-csv';
					// Download
			        header('Content-Type: ' . $mime_type);
			        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			        // IE need specific headers
			        if (PMA_USR_BROWSER_AGENT == 'IE') {
			            header('Content-Disposition: inline; filename="' . $filename . '"');
			            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
			            header('Pragma: public');
			        } else {
			            header('Content-Disposition: attachment; filename="' . $filename . '"');
			            header('Pragma: no-cache');
			        }
					echo $export_csv_data;
					exit();
				} else {
					$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
					tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
				}
			} else {
				if (isset($_POST["set_name"]) && is_array($_POST["set_name"]) && count($_POST["set_name"])) {
					foreach ($_POST["set_name"] as $set_id => $set_name) {
						$restock_character_log_select_sql = "	SELECT * 
																FROM " . TABLE_RESTOCK_CHARACTER_INFO . " 
																WHERE restock_character_sets_id = '" . tep_db_input($set_id) . "' 
																	AND products_id IN ('" . implode("', '", array_keys($_POST["set_character"][$set_id])) . "')";
						$restock_character_old_result_sql = tep_db_query($restock_character_log_select_sql);
						while ($restock_character_old_row = tep_db_fetch_array($restock_character_old_result_sql)) {
							$old_restock_character_array[$restock_character_old_row['restock_character_sets_id'] . '_' . $restock_character_old_row['products_id']] = $restock_character_old_row['restock_character'];
						}
						
						if (tep_not_null($set_name)) {
							$name_replace = ereg_replace_dep(" ", "", strtolower($set_name));
							/*
							$check_set_name_select_sql = "SELECT restock_character_sets_name FROM " . TABLE_RESTOCK_CHARACTER_SETS . " WHERE restock_character_sets_id <> " . tep_db_input($set_id) . " AND LOWER(REPLACE(restock_character_sets_name, ' ', '')) = '" . tep_db_input($name_replace) . "'";
							$check_set_name_result_sql = tep_db_query($check_set_name_select_sql);
			          		$check_duplicate = tep_db_num_rows($check_set_name_result_sql);
			          		if ($check_duplicate > 0) {
			          			$messageStack->add_session(sprintf(ERROR_CHARACTER_SET_NAME_USED, $set_name));
			          		} else {
			          		*/
							$disabled_prod_char_delete_sql = "	DELETE " . TABLE_RESTOCK_CHARACTER_INFO . " 
																FROM " . TABLE_RESTOCK_CHARACTER_INFO . ", " . TABLE_PRODUCTS_PURCHASES . "
																WHERE " . TABLE_RESTOCK_CHARACTER_INFO . ".products_id=" . TABLE_PRODUCTS_PURCHASES . ".products_id 
																	AND " . TABLE_RESTOCK_CHARACTER_INFO . ".restock_character_sets_id = '" . tep_db_input($set_id) . "' 
																	AND " . TABLE_PRODUCTS_PURCHASES . ".products_purchases_lists_id = '" . (int)$_REQUEST["lID"] . "' 
																	AND " . TABLE_PRODUCTS_PURCHASES . ".products_purchases_disabled=1";
							tep_db_query($disabled_prod_char_delete_sql);

							$non_exists_prod_char_delete_sql = "DELETE " . TABLE_RESTOCK_CHARACTER_INFO . "
																FROM " . TABLE_RESTOCK_CHARACTER_INFO . "
																LEFT JOIN " . TABLE_PRODUCTS_PURCHASES . "
																	ON ( ".TABLE_RESTOCK_CHARACTER_INFO.".products_id=".TABLE_PRODUCTS_PURCHASES.".products_id 
																		AND ".TABLE_PRODUCTS_PURCHASES.".products_purchases_lists_id = '" . (int)$_REQUEST["lID"] . "')
																WHERE ".TABLE_RESTOCK_CHARACTER_INFO.".restock_character_sets_id = '" . tep_db_input($set_id) . "' 
																	AND ".TABLE_PRODUCTS_PURCHASES.".products_purchases_lists_id IS NULL ";
							tep_db_query($non_exists_prod_char_delete_sql);

							if (is_array($_POST["set_character"][$set_id]) && count($_POST["set_character"][$set_id])) {
								$clear_set_character_info_sql = "	DELETE 
																	FROM " . TABLE_RESTOCK_CHARACTER_INFO . " 
																	WHERE restock_character_sets_id = '" . tep_db_input($set_id) . "' 
																		AND products_id IN ('".implode("', '", array_keys($_POST["set_character"][$set_id]))."')";
								tep_db_query($clear_set_character_info_sql);

								foreach ($_POST["set_character"][$set_id] as $pid => $character_name) {
									$characters_info_data_array = array('restock_character_sets_id' => $set_id,
																		'products_id' => $pid,
																		'restock_character' => $character_name
																	 );
									tep_db_perform(TABLE_RESTOCK_CHARACTER_INFO, $characters_info_data_array);
								}
							}
							$restock_character_new_result_sql = tep_db_query($restock_character_log_select_sql);
							while ($restock_character_new_row = tep_db_fetch_array($restock_character_new_result_sql)) {
								$new_restock_character_array[$restock_character_new_row['restock_character_sets_id'] . '_' . $restock_character_new_row['products_id']] = $restock_character_new_row['restock_character'];
							}
							
							foreach ($old_restock_character_array as $key => $new_restock_character) {
								if ($old_restock_character_array[$key] != $new_restock_character_array[$key]) {
									$log_info_array = explode('_', $key);
									$set_id = $log_info_array[0];
									$products_id = $log_info_array[1];
									
									$restock_characters_log_data_array = array(	'restock_character_sets_id' => $set_id,
																				'products_id' => $products_id,
																				'restock_character_before' => $old_restock_character_array[$key],
																				'restock_character_after' => $new_restock_character_array[$key],
																				'restock_character_log_admin_id' => $login_id,
																				'restock_characters_log_date' => 'now()',
																				'restock_characters_log_ip' => tep_get_ip_address()
																				);
									
									tep_db_perform(TABLE_RESTOCK_CHARACTER_LOG, $restock_characters_log_data_array);
								}
							}
							
							$messageStack->add_session(sprintf(SUCCESS_CHARACTER_SET_UPDATED, $set_name), 'success');
			          		//}
			          	}
					}
				}

				if (tep_not_null($_POST["new_set_name"])) {	// Insert new list
					$name_replace = ereg_replace_dep(" ", "", strtolower($_POST["new_set_name"]));
					/*
					$check_set_name_select_sql = "SELECT restock_character_sets_name FROM " . TABLE_RESTOCK_CHARACTER_SETS . " WHERE LOWER(REPLACE(restock_character_sets_name, ' ', '')) = '" . tep_db_input($name_replace) . "'";
					$check_set_name_result_sql = tep_db_query($check_set_name_select_sql);
	          		$check_duplicate = tep_db_num_rows($check_set_name_result_sql);
	          		if ($check_duplicate > 0) {
	          			$messageStack->add_session(sprintf(ERROR_CHARACTER_SET_NAME_USED, $_POST["new_set_name"]));
	          		} else {
						
						$characters_set_data_array = array(	'products_purchases_lists_id' => (int)$_REQUEST["lID"],
															'restock_character_sets_name' => $_POST["new_set_name"]
														 );
						tep_db_perform(TABLE_RESTOCK_CHARACTER_SETS, $characters_set_data_array);
						$new_set_id = tep_db_insert_id();
					*/
						if (is_array($_POST["new_character"]) && count($_POST["new_character"])) {
							foreach ($_POST["new_character"] as $pid => $character_name) {
								/*
								$characters_info_data_array = array('restock_character_sets_id' => $new_set_id,
																	'products_id' => $pid,
																	'restock_character' => $character_name
																   );
								*/
								$characters_info_data_array = array('restock_character_sets_id' => (int)$_REQUEST["rcsID"],
																	'products_id' => $pid,
																	'restock_character' => $character_name
																   );
								tep_db_perform(TABLE_RESTOCK_CHARACTER_INFO, $characters_info_data_array);
							}
						}

						$messageStack->add_session(sprintf(SUCCESS_CHARACTER_SET_ADDED, $_POST["new_set_name"]), 'success');
					//}
				}

				tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction'))));
			}

			break;
		/*
		case "confirm_delete_acc_set":
			if (isset($_GET["setID"]) && tep_not_null($_GET["setID"])) {
				$clear_character_set_sql = "DELETE FROM " . TABLE_RESTOCK_CHARACTER_SETS . " WHERE restock_character_sets_id = '" . tep_db_input($_GET["setID"]) . "'";
				tep_db_query($clear_character_set_sql);

				$clear_set_character_info_sql = "DELETE FROM " . TABLE_RESTOCK_CHARACTER_INFO . " WHERE restock_character_sets_id = '" . tep_db_input($_GET["setID"]) . "'";
				tep_db_query($clear_set_character_info_sql);

				$messageStack->add_session(SUCCESS_CHARACTER_SET_DELETED, 'success');
			}

			tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('action', 'subaction'))));

			break;
		*/
		case "add_rstk":
			if ($_REQUEST["new_restock_character_sets_name"]) {
				$check_set_name_select_sql = "SELECT restock_character_sets_name FROM " . TABLE_RESTOCK_CHARACTER_SETS . " WHERE LOWER(REPLACE(restock_character_sets_name, ' ', '')) = '" . $_REQUEST["new_restock_character_sets_name"] . "'";
				$check_set_name_result_sql = tep_db_query($check_set_name_select_sql);
          	    
          		if (tep_db_num_rows($check_set_name_result_sql) > 0) {
          			$messageStack->add_session(sprintf(ERROR_CHARACTER_SET_NAME_USED, $_REQUEST["new_restock_character_sets_name"]));
          		} else {
					$characters_set_data_array = array(	'restock_character_sets_name' => $_REQUEST["new_restock_character_sets_name"],
	      												'restock_character_sets_cat_id' => $_REQUEST["new_restock_character_sets_cat_id"],
	      												'restock_character_sets_sort_order'=> $_REQUEST["new_restock_character_sets_sort_order"],
	      												'restock_character_sets_last_modified' => 'now()'
	      											  );
					tep_db_perform(TABLE_RESTOCK_CHARACTER_SETS, $characters_set_data_array);
					
					$messageStack->add_session(sprintf(SUCCESS_CHARACTER_SET_ADDED, $_REQUEST["new_restock_character_sets_name"]), 'success');
				}
			}
			tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=manage_rstk'));
			
			break;
		case "update_rstk_list":
			$restock_character_delete_sql = "	DELETE FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ."
										 		WHERE products_purchases_lists_id='". $_REQUEST['products_purchases_lists_id'] ."'";
			tep_db_query($restock_character_delete_sql);
			
			$restock_character_sets_data_array = array();
			if (count($_REQUEST["restock_character_sets_id_to"])) {
				foreach($_REQUEST["restock_character_sets_id_to"] AS $restock_character_sets_id) {
					$restock_character_sets_data_array = array('products_purchases_lists_id' => $_REQUEST['products_purchases_lists_id'],
															   'restock_character_sets_id' => $restock_character_sets_id);
					tep_db_perform(TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS, $restock_character_sets_data_array);
				}
			}
			
			$messageStack->add_session(SUCCESS_CHARACTER_SET_NO_STRING_UPDATED, 'success');
			tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('action', 'subaction'))));
			
			break;
		case "confirm_delete_rstk_set":
			$restock_character_delete_sql = "	DELETE FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ."
												WHERE products_purchases_lists_id='". $_REQUEST['lID'] ."'
													AND restock_character_sets_id='". $_REQUEST['rcsID'] ."'";
			tep_db_query($restock_character_delete_sql);
			
			$supplier_pricing_setting_update_sql = "UPDATE ". TABLE_SUPPLIER_PRICING_SETTING ." SET supplier_pricing_setting_value='' 
													WHERE supplier_pricing_setting_key='rstk_char_set' 
													AND products_purchases_lists_id='". $_REQUEST['lID'] ."'";
			tep_db_query($supplier_pricing_setting_update_sql);
			
			$messageStack->add_session(sprintf(SUCCESS_CHARACTER_SET_REMOVED, $_REQUEST['rstk'], $_REQUEST['purchase']), 'success');
			tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('action', 'subaction'))));
			
			break;
		case "edit_rstk_character":
			if ($_REQUEST["restock_character_sets_name"]) {
				$check_set_name_select_sql = "SELECT restock_character_sets_name FROM " . TABLE_RESTOCK_CHARACTER_SETS . " WHERE restock_character_sets_id <> '". $_REQUEST["rcsID"] ."' AND LOWER(REPLACE(restock_character_sets_name, ' ', '')) = '" . $_REQUEST["restock_character_sets_name"] . "'";
				$check_set_name_result_sql = tep_db_query($check_set_name_select_sql);

	      		if (tep_db_num_rows($check_set_name_result_sql) > 0) {
	      			$messageStack->add_session(sprintf(ERROR_CHARACTER_SET_NAME_USED, $_REQUEST["restock_character_sets_name"]));
	      		} else {
					$characters_set_data_array = array(	'restock_character_sets_name' => $_REQUEST["restock_character_sets_name"],
														'restock_character_sets_cat_id' => $_REQUEST["restock_character_sets_cat_id"],
		  												'restock_character_sets_sort_order'=> $_REQUEST["restock_character_sets_sort_order"],
		  												'restock_character_sets_last_modified' => 'now()'
		  											  );
					tep_db_perform(TABLE_RESTOCK_CHARACTER_SETS, $characters_set_data_array, 'update', "restock_character_sets_id = '" . $_REQUEST["rcsID"] . "'");

					tep_get_subcategories($subcategories_array, $_REQUEST["restock_character_sets_cat_id"]);
					$subcategories_array = array_merge($_REQUEST["restock_character_sets_cat_id"], $subcategories_array);

					//delete restock character
					$products_id_select_sql = "	SELECT p2c.products_id
												FROM ". TABLE_PRODUCTS_TO_CATEGORIES ." AS p2c
												INNER JOIN ". TABLE_PRODUCTS ." AS p
													ON (p.products_id=p2c.products_id)
												INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
													ON (p.products_id=pd.products_id)
												WHERE p.products_bundle='' 
													AND p.products_bundle_dynamic=''
													AND p.custom_products_type_id=0
													AND pd.language_id = '" . $languages_id . "'
													AND p2c.categories_id NOT IN ('" . implode("', '", $subcategories_array) ."')
												ORDER BY p2c.products_id";
					$products_id_result_sql = tep_db_query($products_id_select_sql);

					while ($products_id_row = tep_db_fetch_array($products_id_result_sql)) {
						$restock_character_info_delete_sql = "	DELETE FROM ". TABLE_RESTOCK_CHARACTER_INFO ."
																WHERE restock_character_sets_id = '". $_REQUEST['rcsID'] ."'
																	AND products_id = '". $products_id_row['products_id'] ."'";
						tep_db_query($restock_character_info_delete_sql);
					}

					//tep_get_parent_categories($parent_categories_array, $_REQUEST["restock_character_sets_cat_id"]); //check category that have same parent
					tep_get_subcategories($parent_categories_array, $_REQUEST["restock_character_sets_cat_id"]); //check category that have same parent
					$parent_categories_array = array_merge($_REQUEST["restock_character_sets_cat_id"], $parent_categories_array);

					$check_category_select_sql = "	SELECT rcs2pl.products_purchases_lists_id, ppl.products_purchases_lists_name, rcs.restock_character_sets_name
													FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ." AS rcs2pl
													INNER JOIN ". TABLE_RESTOCK_CHARACTER_SETS ." AS rcs
														ON (rcs2pl.restock_character_sets_id=rcs.restock_character_sets_id)
													INNER JOIN ". TABLE_PRODUCTS_PURCHASES_LISTS ." AS ppl
														ON (rcs2pl.products_purchases_lists_id=ppl.products_purchases_lists_id)
													WHERE rcs2pl.restock_character_sets_id = '". $_REQUEST["rcsID"] ."'
														AND ppl.products_purchases_lists_cat_id NOT IN ('" . implode("', '", $parent_categories_array) ."')";
					$check_category_result_sql = tep_db_query($check_category_select_sql);

					while ($check_category_row = tep_db_fetch_array($check_category_result_sql)) {
						tep_db_query("DELETE FROM " . TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS . " WHERE restock_character_sets_id = '" . $_REQUEST["rcsID"] . "' AND products_purchases_lists_id = '" . $check_category_row["products_purchases_lists_id"] . "'"); //remove rstk character from purchase lists
						$messageStack->add_session(sprintf(WARNING_CHARACTER_SET_PURCHASE_LIST_DELETED, $check_category_row["restock_character_sets_name"], $check_category_row["products_purchases_lists_name"]), 'warning');
					}
					
					$messageStack->add_session(sprintf(SUCCESS_CHARACTER_SET_UPDATED, $_REQUEST["restock_character_sets_name"]), 'success');
				}
			}
			tep_redirect(tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=edit_acc_set&rcsID='. $_REQUEST["rcsID"]));
			
			break;
	}
}
$export_csv_array = array();
?>
<?
    /***
     * Note: This function is *adapted* from includes/classes/buyback.php
     **/
    //Check user settings on get method
    function get_inventory_space_in_range($products_id) {
        global $messageStack, $purchase_list_setting_array;
        
        //depending on db, do either of the following
        $inventory_space = 0;
        switch ($purchase_list_setting_array['ppls_sales_retrieval_method']) {
            case 'by_date_range':
                //If Sales end date is not saved, use today's date. So it calculates realtime everytime we need inventory space.
                //Will of course affect the average sales cos more days involved as time goes by.
                $end_date = (trim($purchase_list_setting_array['ppls_sales_end_date']) ? $purchase_list_setting_array['ppls_sales_end_date'] : date('Y-m-d'));
                if (tep_day_diff($purchase_list_setting_array['ppls_sales_start_date'], $end_date) > 0) {
                    $inventory_space = get_inventory_space_by_date_range($products_id, $purchase_list_setting_array['ppls_sales_start_date'], $end_date);
                } else {
                    $messageStack->add_session(sprintf(MESSAGE_INVALID_START_END_DATE, ''));
                }
                break;
            case 'by_last_n_days';
                $inventory_space = get_inventory_space_by_num_days($products_id, $purchase_list_setting_array['ppls_last_n_days_sales']);
                break;
        }
        return $inventory_space;
    }
    /***
     * Note: This function is *adapted* from includes/classes/buyback.php
     **/
    //For reuse and standardisation, we eventually call get_inventory_space_by_date_range()
    function get_inventory_space_by_num_days($products_id, $num_days) {
        $start_date = '';
        $end_date = '';

        //Here end_date will always be midnite last nite (dont want to mess with partials of today). 
        //We want to find out the start date based on num of days to count backwards. 
        $gm_end_date  = mktime(24, 0, 0, date("m"), date("d")-1, date("Y"));
        $end_date = date('Y-m-d H:i:s', $gm_end_date);
        $gm_start_date = $gm_end_date - ((int)$num_days * 86400);
        $start_date = date('Y-m-d H:i:s', $gm_start_date);
        return get_inventory_space_by_date_range($products_id, $start_date, $end_date); 
    }
    /***
     * Note: This function is *adapted* from includes/classes/buyback.php
     **/
    function get_inventory_space_by_date_range($products_id, $start_date, $end_date) {
        global $purchase_list_setting_array;
        
        $inventory_space_in_range = 0;
		if ($purchase_list_setting_array['ppls_inventory_days'] && $start_date && $end_date) {
    	    //Calculate now.
            $purchase_list_calculate_inventory_space_settings = array(
                                                            'ppls_inventory_days' => $purchase_list_setting_array['ppls_inventory_days'], 
                                                            'ppls_sales_start_date' => $start_date, 
                                                            'ppls_sales_end_date' => $end_date);
            $inventory_space_in_range = tep_get_suggested_max_purchase_qty($products_id, $purchase_list_calculate_inventory_space_settings);		
		}
        return $inventory_space_in_range;
    }
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script type="text/javascript" src="includes/general.js"></script>
	<script type="text/javascript" src="includes/javascript/select_box.js"></script>
	<script language="javascript" src="includes/javascript/apopwin.js"></script>
	<script type="text/javascript">
	<!--
		var products_ids = new Array();

		function disableTextBoxes(id, obj, sn) {
			var maxQtyObj = DOMCall("maxQty" + id);
			var sellingQtyObj = DOMCall("sellingQty" + id);

			bl = obj.checked;

			if (maxQtyObj != null) maxQtyObj.disabled = bl;
			if (sellingQtyObj != null) sellingQtyObj.disabled = bl;

			if (bl) {
				if (maxQtyObj != null) 	maxQtyObj.style.background = '#D4D0C8';
				if (sellingQtyObj != null)	sellingQtyObj.style.background = '#D4D0C8';
			} else {
				if (maxQtyObj != null) 	maxQtyObj.style.background = '#ffffff';
				if (sellingQtyObj != null)	sellingQtyObj.style.background = '#ffffff';
			}
		}

		function select_all_checkbox_click(selectAllObj, ctrlName, skipDisabled, prefix) {
			if (products_ids != null) {
				var size_of_products_ids = products_ids.length;
				var checked_state = (selectAllObj.checked) ? true : false;

				for (i=0; i < size_of_products_ids; i++) {
					var chk_box = DOMCall(ctrlName + products_ids[i]);

					if (chk_box != null && !chk_box.disabled) {
						if (skipDisabled == true) {
							var disabled_state = GetCookie(prefix + '_purchase_show_hide_disabled');

							if (disabled_state != 1) {	// Currently the view exclude disabled rows
								var disabled_row = DOMCall("row" + products_ids[i]);

								if (disabled_row != null && disabled_row.className == 'show') {
									chk_box.checked = checked_state;
								}
							} else {
								chk_box.checked = checked_state;
							}
						} else {
							chk_box.checked = checked_state;
						}

						if (selectAllObj.value > 0) {
							disableTextBoxes(products_ids[i], chk_box, selectAllObj.value);
						}
					}
				}
			}
		}

		function clear_input_boxes(ctrlObjName) {
			if (products_ids != null) {
				var size_of_products_ids = products_ids.length;

				for (i=0; i < size_of_products_ids; i++) {
					var input_box = DOMCall(ctrlObjName + products_ids[i]);

					if (input_box != null) {
						input_box.value = '';
					}
				}
			}
		}

		function show_hide_disabled(show, prefix) {
			var ele = document.getElementById('show_disabled');
			var temp_ele;
			var size_of_products_ids = products_ids.length;
			var count =0 ;
			var tempPool = new Array();

			ele.innerHTML = '';

			for (i=0; i < size_of_products_ids; i++) {
				chk_box = document.getElementById("chkDisabled" + products_ids[i]);

				if (chk_box.checked) {
					if (show) {
						document.getElementById("row" + products_ids[i]).className = 'show';
					} else {
				 		document.getElementById("row" + products_ids[i]).className = 'hide';
				 	}
				} else {
					document.getElementById("row" + products_ids[i]).className = 'show';
				}
			}

			// reaarange
			for (i=0; i < size_of_products_ids; i++) {
				objrow = document.getElementById("row" + products_ids[i]);
				if (show) {
					strclass = ( count%2 ) ? "ordersListingEven" : "ordersListingOdd";
					tempPool[products_ids[i]] = strclass;

					custrow = document.getElementById("prodRow_" + products_ids[i]);
					objrow.className = 'show';
					custrow.className = strclass;
					custrow.onclick=function() {
							var tempStr = this.id.split('_');
							rowClicked(this, tempPool[tempStr[1]], '');
						}
					custrow.onmouseout=function() {
							var tempStr = this.id.split('_');
							rowOutEffect(this, tempPool[tempStr[1]], '');
						}

					count++;
				} else {
					if (objrow.className == 'show') {
						strclass = ( count%2 ) ? "ordersListingEven" : "ordersListingOdd";
						tempPool[products_ids[i]] = strclass;

						custrow= document.getElementById("prodRow_" + products_ids[i]);
						custrow.className = strclass;
						custrow.onclick=function() {
								var tempStr = this.id.split('_');
								rowClicked(this, tempPool[tempStr[1]], '');
							}
						custrow.onmouseout=function() {
								var tempStr = this.id.split('_');
								rowOutEffect(this, tempPool[tempStr[1]], '');
							}
						count++;
					}
				}
			}

			if (show) {
				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_disabled(false, ' + prefix + ')"><?=TEXT_HIDE_DISABLED?></a>';
				SetCookie(prefix + '_purchase_show_hide_disabled', '1');
			} else {
				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_disabled(true, ' + prefix + ')"><?=TEXT_SHOW_DISABLED?></a>';
				SetCookie(prefix + '_purchase_show_hide_disabled', '0');
			}
		}

		function show_hide_product_name(show, prefix) {
			var show_hide_prod_name_div = DOMCall('show_product_name');
			var size_of_products_ids = products_ids.length;

			show_hide_prod_name_div.innerHTML = '';

			for (var i=0; i < size_of_products_ids; i++) {
				var prod_name_span_obj = DOMCall('span_prod_name_' + products_ids[i]);

				prod_name_span_obj.className = show ? 'show' : 'hide';
			}

			if (show) {
				show_hide_prod_name_div.innerHTML = '<a href="javascript:;" onclick="show_hide_product_name(false, ' + prefix + ')"><?=TEXT_HIDE_PRODUCT_NAME?></a>';
				SetCookie(prefix + '_purchase_show_hide_pname', '1');
			} else {
				show_hide_prod_name_div.innerHTML = '<a href="javascript:;" onclick="show_hide_product_name(true, ' + prefix + ')"><?=TEXT_SHOW_PRODUCT_NAME?></a>';
				SetCookie(prefix + '_purchase_show_hide_pname', '0');
			}
		}

		function show_hide_batch_fill(show) {
			var ele = document.getElementById('show_batch_fill');
			var ele2 = document.getElementById('batch_fill');

			if (show) {
				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_batch_fill(false)"><?=TEXT_HIDE_BATCH_FILL?></a>';
				ele2.className = "show";
			} else {
				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_batch_fill(true)"><?=TEXT_SHOW_BATCH_FILL?></a>';
				ele2.className = "hide";
			}

			return;
		}

		function apply_batch(btnBatchObj) {
			var frm = btnBatchObj.form;

			if (products_ids != null) {
				var size_of_products_ids = products_ids.length;

				var ischecked, temp_obj;
				var product_status, min, max, up, is_disabled, comment;
				var disable_inputs_mode = 0;

				var max_qty = parseInt(frm.txtBatchMaxQty.value);
				var purchase_qty = frm.txtBatchPurchaseQty.value;
				is_disabled = frm.chkBatchDisabled.checked;

				if (isNaN(max_qty))			max_qty = 0;
				if (isNaN(purchase_qty)) 	purchase_qty = '';

				for	(var i=0; i < size_of_products_ids; i++) {
					temp_obj = document.getElementById("chkSelected" + products_ids[i]);

					if (temp_obj != null) {
						ischecked = temp_obj.checked;

						if (ischecked) {
							if (document.getElementById("maxQty" + products_ids[i]) != null) {
								document.getElementById("maxQty" + products_ids[i]).value = max_qty;
							}

							if (document.getElementById("sellingQty" + products_ids[i]) != null) {
								document.getElementById("sellingQty" + products_ids[i]).value = purchase_qty;
							}

							if (document.getElementById("chkDisabled" + products_ids[i]) != null) {
								document.getElementById("chkDisabled" + products_ids[i]).checked = is_disabled;
							}

							disableTextBoxes(products_ids[i], document.getElementById("chkDisabled" + products_ids[i]), 2);
						}
					}
				}
			}
			return;
		}

		function checkMaxQty(id) {
			var obj = DOMCall("txtMaxQty" + id);
			var global_min_qty_obj = DOMCall('<?=KEY_SPS_MIN_QTY?>');

			var valMax = parseInt(obj.value);
			var valMin = (global_min_qty_obj != null) ? global_min_qty_obj.value : 0;

			if (isNaN(valMin)) 	valMin = 0;
			if (isNaN(valMax))	valMax = 0;

			if (valMin > valMax) {
				alert("Maximum quantity cannot be less than maximum quantity.");
				obj.value = '';
				obj.focus();
			}
			return;
		}

		function form_checking(form_obj) {
			var selected_restock_character_sets = document.set_restock_character_form.elements['restock_character_sets_id_to[]'];
			if (selected_restock_character_sets != null) {
				for (x=0; x<(selected_restock_character_sets.length); x++) {
					selected_restock_character_sets.options[x].selected = true;
				}
			}
			form_obj.submit();
			return true;
		}

    	function enableFieldSet(setName) {
    	    var fld_id_enable_arr = new Array();
    	    var fld_id_disable_arr = new Array();
            switch (setName) {
                case 'rdoSalesRetrievalMethod1':
                    fld_id_disable_arr.push('ppls_last_n_days_sales');
                    fld_id_enable_arr.push('ppls_sales_start_date', 'ppls_sales_end_date');
                    break;
                case 'rdoSalesRetrievalMethod2':
                    fld_id_enable_arr.push('ppls_last_n_days_sales');
                    fld_id_disable_arr.push('ppls_sales_start_date', 'ppls_sales_end_date');
                    break;
            }
            setFieldState(fld_id_enable_arr, false);
            setFieldState(fld_id_disable_arr, true);
    	}
    	
    	function setFieldState(fld_id_arr, isDisable) {
            for (i=0;i<fld_id_arr.length;i++) {
                document.getElementById(fld_id_arr[i]).disabled = isDisable;
            }
    	}		
	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
            			<td>
            				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="main" align="right">
            						<?
            							if (tep_not_null($action)) {
            								if ($action == "add_rstk" || $action == "edit_acc_set") {
            									echo tep_button(IMAGE_BUTTON_BACK, IMAGE_BUTTON_BACK, tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=manage_rstk'), '', 'inputButton');
            								} else {
	            								echo tep_button(IMAGE_BUTTON_BACK, IMAGE_BUTTON_BACK, tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY), '', 'inputButton');
	            							}
            							}
            						?>
            						</td>
          						</tr>
          					</table>
            			</td>
          			</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
if ($action == "add_list") {
	$all_categories_array = tep_get_eligible_category_tree(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 0, '___', '', $all_categories_array);
?>
					<tr>
						<td>
<?		echo tep_draw_form('purchase_list_form', FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction')) . 'subaction=insert_list_info', 'post', 'onSubmit="return purchase_list_form_checking();"');?>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
        						<tr>
            						<td class="main" width="15%"><?=ENTRY_PURCHASE_LIST_NAME?></td>
            						<td class="main"><?=tep_draw_input_field('purchase_list_name', '', 'size="50" id="purchase_list_name"')?></td>
        						</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
				      			<tr>
            						<td class="main" width="15%"><?=ENTRY_MAIN_CAT?></td>
            						<td class="main"><?=tep_draw_pull_down_menu("purchases_lists_cat_id", $all_categories_array, '', '')?></td>
        						</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
				      			<tr>
            						<td class="main" width="15%"><?=ENTRY_QTY_ROUND_UP?></td>
            						<td class="main"><?=tep_draw_input_field('products_purchases_lists_qty_round_up', '1', 'size="10" id="products_purchases_lists_qty_round_up" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
        						</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
				      			<tr>
            						<td class="main" width="15%"><?=ENTRY_SORT_ORDER?></td>
            						<td class="main"><?=tep_draw_input_field('products_purchases_lists_sort_order', '50000', 'size="10" id="products_purchases_lists_sort_order" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
        						</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
				      			<tr>
            						<td class="main" colspan="2">
                						<table border="0" cellspacing="0" cellpadding="2">
                							<tr valign="top" bgcolor="#d7d5d0">
                							 <td class="dataTableContent">
                        						<table border="0" cellspacing="0" cellpadding="2">
                							     <tr valign="top"><td colspan="3" class="dataTableContent"><b><?=TABLE_HEADING_SALES_RETRIEVAL_METHOD?></b></td></tr>
                							     <tr valign="top"><td colspan="3" class="dataTableContent"><?=tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_date_range', true , '', ' id="rdoSalesRetrievalMethod1" onClick="enableFieldSet(this.id)" ')?>&nbsp;<?=TABLE_HEADING_SALES_RETRIEVE_BY_DATE_RANGE?></td></tr>
                                                 <tr valign="top">
                                                    <td>&nbsp;&nbsp;&nbsp;</td>
                                                    <td class="dataTableContent" align="left"><?=TABLE_HEADING_SALES_START_DATE?></td>
                                                    <td class="dataTableContent" align="left"><?=tep_draw_input_field('ppls_sales_start_date', '', ' id="ppls_sales_start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.purchase_list_form.ppls_sales_start_date); }" onKeyPress="return noEnterKey(event)" ')?>
                                                      <a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.purchase_list_form.ppls_sales_start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>&nbsp;<span class="fieldRequired">*</span></td>
                                                 </tr>
                                                 <tr valign="top">
                                                    <td>&nbsp;&nbsp;&nbsp;</td>                                                      
                                                    <td class="dataTableContent" align="left"><?=TABLE_HEADING_SALES_END_DATE?><br><i>Default: Current Date</i></td>
                                                    <td class="dataTableContent" align="left"><?=tep_draw_input_field('ppls_sales_end_date', '', ' id="ppls_sales_end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.purchase_list_form.ppls_sales_end_date); }" onKeyPress="return noEnterKey(event)" ')?>
                                                      <a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.purchase_list_form.ppls_sales_end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a></td>
                                                 </tr>
                							     <tr valign="top"><td colspan="3" class="dataTableContent"><?=tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_last_n_days', false, '', ' id="rdoSalesRetrievalMethod2" onClick="enableFieldSet(this.id)" ')?>&nbsp;<?=TABLE_HEADING_SALES_RETRIEVE_BY_LAST?>
                                                      &nbsp;<?=tep_draw_input_field('ppls_last_n_days_sales', '', ' id="ppls_last_n_days_sales" size="4" maxlength="4"')?>
                                                      &nbsp;<?=TEXT_DAYS?></td></tr>
                        						<tr>
                				        			<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
                				      			</tr>
                							     <tr valign="top">
                							         <td colspan="2" class="dataTableContent"><b><?=TABLE_HEADING_DAYS_INVENTORY?></b></td>
                							         <td class="dataTableContent"><?=tep_draw_input_field('ppls_inventory_days', '1', 'size="10" id="ppls_inventory_days" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td></tr>
                                                </table>
                							 </td>
                							</tr>
                						</table>        				     
            						</td>
        						</tr>
            					<tr>
            						<td colspan="2"><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
            					</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
        						<tr>
            						<td class="main" width="15%">&nbsp;</td>
            						<td class="main"><?=tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, '', 'inputButton')?></td>
        						</tr>
        					</table>
        				</form>
        				</td>
        			</tr>
        			<?php
                        //default for new add form
                        $js = '';
                        $js .= "enableFieldSet('rdoSalesRetrievalMethod1');";
        			?>
        			<script>
					<!--
					   	<?=$js?>
						
						function purchase_list_form_checking() {
							var error_message = '<?=JS_ERROR?>';
							var error = false;
							var focus_field = '';

							var list_name = DOMCall('purchase_list_name');
							if (trim_str(list_name.value) == '') {
								error_message += '* Please enter the name for purchase list.' + "\n";
								focus_field = 'purchase_list_name';
								error = true;
							}

                            var sales_retrieval_method1 = DOMCall('rdoSalesRetrievalMethod1');
                            var sales_retrieval_method2 = DOMCall('rdoSalesRetrievalMethod2');
                            if (sales_retrieval_method1.checked) {
                                //by date range    
    							var sales_start_date = DOMCall('ppls_sales_start_date');
    							if (trim_str(sales_start_date.value) == '') {
    								error_message += '* Please enter the sales start date.' + "\n";
    								focus_field = 'ppls_sales_start_date';
    								error = true;
    							}
    							
    							var sales_end_date_temp = DOMCall('ppls_sales_end_date').value;
                                if (trim_str(sales_end_date_temp) == '') {
                                    sales_end_date_temp = '<?=date('Y-m-d')?>';
                                }
    							
    							if (sales_start_date.value > sales_end_date_temp) {
    								error_message += '* Sales Start Date is not after Sales End Date.' + "\n";
    								focus_field = 'ppls_sales_start_date';
    								error = true;
    							}                                
                            } else if (sales_retrieval_method2.checked){
                                //by last N days
    							var ppls_last_n_days_sales = DOMCall('ppls_last_n_days_sales');
    							if (!parseInt(ppls_last_n_days_sales.value) > 0) {
    								error_message += '* Please enter the last number days of sales .' + "\n";
    								focus_field = 'ppls_last_n_days_sales';
    								error = true;
    							}
                            }

							var inventory_days = DOMCall('ppls_inventory_days');
							if (trim_str(inventory_days.value) == '') {
								error_message += '* Please enter the inventory days.' + "\n";
								focus_field = 'ppls_inventory_days';
								error = true;
							}

							if (error == true) {
								alert(error_message);
								document.getElementById(focus_field).focus();
								return false;
							} else {
								return true;
							}
						}
					//-->
					</script>
<?
} else if ($action == "edit_list") {
	$list_info_select_sql = "	SELECT products_purchases_lists_name, products_purchases_lists_cat_id, products_purchases_lists_qty_round_up, products_purchases_lists_sort_order, products_purchases_lists_reference_date
								FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id='".(int)$_REQUEST["lID"]."'";
	$list_info_result_sql = tep_db_query($list_info_select_sql);
	if ($list_info_row = tep_db_fetch_array($list_info_result_sql)) {
		$list_id = (int)$_REQUEST["lID"];

        $purchase_list_setting_array = array();
        $purchase_list_setting_select_sql = " SELECT products_purchases_lists_setting_key, products_purchases_lists_setting_value
                                     FROM " . TABLE_PRODUCTS_PURCHASES_LISTS_SETTING . "
                                     WHERE products_purchases_lists_id = '$list_id'";
        $purchase_list_setting_result_sql = tep_db_query($purchase_list_setting_select_sql);
        while ($purchase_list_setting_row = tep_db_fetch_array($purchase_list_setting_result_sql)) {
            $purchase_list_setting_array[$purchase_list_setting_row['products_purchases_lists_setting_key']] = $purchase_list_setting_row['products_purchases_lists_setting_value'];
        }

		$parent_cat_id = $list_info_row["products_purchases_lists_cat_id"];

		$all_categories_array = tep_get_eligible_category_tree(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 0, '___', '', $all_categories_array);

		$category_array = array($parent_cat_id);
		tep_get_subcategories($category_array, $parent_cat_id);
?>
					<tr>
						<td>
<?		echo tep_draw_form('purchase_list_form', FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction')) . 'subaction=update_list_info', 'post', 'onSubmit="return purchase_list_form_checking();"');?>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
        						<tr>
            						<td class="main" width="15%"><?=ENTRY_PURCHASE_LIST_NAME?></td>
            						<td class="main"><?=tep_draw_input_field('purchase_list_name', $list_info_row["products_purchases_lists_name"], 'size="50" id="purchase_list_name"')?></td>
        						</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
				      			<tr>
            						<td class="main" width="15%"><?=ENTRY_MAIN_CAT?></td>
            						<td class="main"><?=tep_draw_pull_down_menu("purchases_lists_cat_id", $all_categories_array, $list_info_row["products_purchases_lists_cat_id"], '')?></td>
        						</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
				      			<tr>
            						<td class="main" width="15%"><?=ENTRY_QTY_ROUND_UP?></td>
            						<td class="main"><?=tep_draw_input_field('products_purchases_lists_qty_round_up', $list_info_row['products_purchases_lists_qty_round_up'], 'size="10" id="products_purchases_lists_qty_round_up" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
        						</tr>
        						<tr>
            						<td class="main" width="15%"><?=ENTRY_SORT_ORDER?></td>
            						<td class="main"><?=tep_draw_input_field('products_purchases_lists_sort_order', $list_info_row["products_purchases_lists_sort_order"], 'size="10" id="products_purchases_lists_sort_order" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
        						</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
				      			<tr>
            						<td class="main" colspan="2">
                						<table border="0" cellspacing="0" cellpadding="2">
                							<tr valign="top" bgcolor="#d7d5d0">
                							 <td class="dataTableContent">
                        						<table border="0" cellspacing="0" cellpadding="2">
                							     <tr valign="top"><td colspan="3" class="dataTableContent"><b><?=TABLE_HEADING_SALES_RETRIEVAL_METHOD?></b></td></tr>
                							     <tr valign="top"><td colspan="3" class="dataTableContent"><?=tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_date_range', (isset($purchase_list_setting_array['ppls_sales_retrieval_method']) ? (($purchase_list_setting_array['ppls_sales_retrieval_method'] == 'by_date_range') ? true : false) : true), '', ' id="rdoSalesRetrievalMethod1" onClick="enableFieldSet(this.id)" ')?>&nbsp;<?=TABLE_HEADING_SALES_RETRIEVE_BY_DATE_RANGE?></td></tr>
                                                 <tr valign="top">
                                                    <td>&nbsp;&nbsp;&nbsp;</td>
                                                    <td class="dataTableContent" align="left"><?=TABLE_HEADING_SALES_START_DATE?></td>
                                                    <td class="dataTableContent" align="left"><?=tep_draw_input_field('ppls_sales_start_date', (isset($purchase_list_setting_array['ppls_sales_start_date']) ? $purchase_list_setting_array['ppls_sales_start_date'] : ''), ' id="ppls_sales_start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.purchase_list_form.ppls_sales_start_date); }" onKeyPress="return noEnterKey(event)" ')?>
                                                      <a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.purchase_list_form.ppls_sales_start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>&nbsp;<span class="fieldRequired">*</span></td>
                                                 </tr>
                                                 <tr valign="top">
                                                    <td>&nbsp;&nbsp;&nbsp;</td>                                                      
                                                    <td class="dataTableContent" align="left"><?=TABLE_HEADING_SALES_END_DATE?><br><i>Default: Current Date</i></td>
                                                    <td class="dataTableContent" align="left"><?=tep_draw_input_field('ppls_sales_end_date', (isset($purchase_list_setting_array['ppls_sales_end_date']) ? $purchase_list_setting_array['ppls_sales_end_date'] : ''), ' id="ppls_sales_end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.purchase_list_form.ppls_sales_end_date); }" onKeyPress="return noEnterKey(event)" ')?>
                                                      <a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.purchase_list_form.ppls_sales_end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a></td>
                                                 </tr>
                							     <tr valign="top"><td colspan="3" class="dataTableContent"><?=tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_last_n_days', false, (($purchase_list_setting_array['ppls_sales_retrieval_method'] == 'by_last_n_days') ? true : false), ' id="rdoSalesRetrievalMethod2" onClick="enableFieldSet(this.id)" ')?>&nbsp;<?=TABLE_HEADING_SALES_RETRIEVE_BY_LAST?>
                                                      &nbsp;<?=tep_draw_input_field('ppls_last_n_days_sales', (isset($purchase_list_setting_array['ppls_last_n_days_sales']) ? $purchase_list_setting_array['ppls_last_n_days_sales'] : ''), ' id="ppls_last_n_days_sales" size="4" maxlength="4"')?>
                                                      &nbsp;<?=TEXT_DAYS?></td></tr>
                        						<tr>
                				        			<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
                				      			</tr>
                							     <tr valign="top">
                							         <td colspan="2" class="dataTableContent"><b><?=TABLE_HEADING_DAYS_INVENTORY?></b></td>
                							         <td class="dataTableContent"><?=tep_draw_input_field('ppls_inventory_days', (isset($purchase_list_setting_array['ppls_inventory_days']) ? $purchase_list_setting_array['ppls_inventory_days'] : '' ), 'size="10" id="ppls_inventory_days" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td></tr>
                                                </table>
                							 </td>
                							</tr>
                						</table>        				     
            						</td>
        						</tr>				      			
            					<tr>
            						<td colspan="2"><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
            					</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
        						<tr>
            						<td class="main" width="15%">&nbsp;</td>
            						<td class="main"><?=tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, '', 'inputButton')?></td>
        						</tr>
        					</table>
        				</form>
        				</td>
        			</tr>
        			<tr>
	        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
        			<tr>
						<td><?=tep_draw_separator()?></td>
					</tr>
					<tr>
	        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
					<tr>
						<td>
<?		echo tep_draw_form('purchase_qty_form', FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction')) . 'subaction=update_quantity', 'post', 'enctype="multipart/form-data"');
		if ($subaction == 'suggest_qty') {
			echo tep_draw_hidden_field("reset_list_reference_date", '1');
		}
?>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
								<tr>
        							<td class="main"><?=TEXT_USER_NOTES?></td>
        						</tr>
								<tr>
									<td>
										<table width="100%" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td valign="bottom">
												<?=tep_submit_button('Latest Suggest Quantity', 'Retrieve Latest Suggest Quantity', 'name="btn_latest_qty"', 'inputButton')?>
												</td>
												<td>
													<table border="0" cellspacing="2" cellpadding="2">
														<TBODY id="batch_fill" class="hide">
															<tr>
																<td>
																	<table width="100%" border="0" cellspacing="0" cellpadding="2">
																		<tr>
																			<td class="ordersBoxHeading">&nbsp;</td>
																			<td width="8%" class="ordersBoxHeading" align="center"><?=TABLE_HEADING_OVERWRITE_MAX_QTY?></td>
																			<td class="ordersBoxHeading" align="center"><?=TABLE_HEADING_PURCHASE_QUANTITY?></td>
																			<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_DISABLED?></td>
																		</tr>
																		<tr class="ordersListingEven">
																			<td class="ordersRecords"><?=TEXT_APPLY_TO_SELECTED?></td>
																			<td class="ordersRecords" align="center"><?=tep_draw_input_field("txtBatchMaxQty", '', 'size="7" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
																			<td class="ordersRecords" align="center"><?=tep_draw_input_field("txtBatchPurchaseQty", '', 'size="7" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
																			<td class="ordersRecords" align="right"><?=tep_draw_checkbox_field("chkBatchDisabled")?></td>
																		</tr>
																		<tr>
																			<td colspan="4" align="right">
																				<?=tep_button(IMAGE_BUTTON_BATCH_APPLY, IMAGE_BUTTON_BATCH_APPLY, '', 'onClick="apply_batch(this);"')?>
																			</td>
																		</tr>
																</table>
																</td>
															</tr>
														</TBODY>
													</table>
												</td>
												<td align="right" valign="top">
													<div id="show_batch_fill"><a href="javascript:;" onclick="show_hide_batch_fill(true)"><?=TEXT_SHOW_BATCH_FILL?></a></div>
													<div id="show_disabled"><!--a href="javascript:;" onclick="show_hide_disabled(false, '<?=$list_id?>')"><?=TEXT_HIDE_DISABLED?></a--></div>
													<div id="show_product_name"><!--a href="javascript:;" onclick="show_hide_product_name(false)"><?=TEXT_HIDE_PRODUCT_NAME?></a--></div>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width="100%"  border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td class="ordersBoxHeading"><?=tep_draw_checkbox_field('checkAll', '', false, '', ' onclick="select_all_checkbox_click(this, \'chkSelected\', true, \''.$list_id.'\')" ')?></td>
												<td class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT?></td>
												<td class="ordersBoxHeading" align="center"><?=TABLE_HEADING_MAX_QUANTITY?></td>
												<td width="8%" class="ordersBoxHeading" align="center">
												    <?=TABLE_HEADING_OVERWRITE_MAX_QTY?><br/>
												    <a href="javascript:;" onclick="clear_input_boxes('maxQty')" class="highlightLink"><?=TEXT_CLEAR?></a>
												</td>
												<td width="8%" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_AVAILABLE_QUANTITY?></td>
												<td width="8%" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_ACTUAL_QUANTITY?></td>
												<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_SUGGEST_QUANTITY?></td>
												<td class="ordersBoxHeading" align="center">
													<?=TABLE_HEADING_PURCHASE_QUANTITY?><br>
													<a href="javascript:;" onclick="clear_input_boxes('sellingQty')" class="highlightLink"><?=TEXT_CLEAR?></a>
												</td>
												<td class="ordersBoxHeading" align="right">
													<?=TABLE_HEADING_DISABLED?>
												</td>
												<td width="1" class="ordersBoxHeading" align="right">
													<?=tep_draw_checkbox_field('select_all_disabled', '1', false, '', 'id="select_all" title="Select or deselect all disabled" onclick="javascript:void(select_all_checkbox_click(this, \'chkDisabled\', true, \''.$list_id.'\'));"')?>
												</td>
											</tr>
<?
			$export_csv_array['HEADER'] = array(TABLE_HEADING_PRODUCT_ID, TABLE_HEADING_PRODUCT, TABLE_HEADING_MAX_QUANTITY, TABLE_HEADING_OVERWRITE_MAX_QTY, TABLE_HEADING_AVAILABLE_QUANTITY, TABLE_HEADING_ACTUAL_QUANTITY, TABLE_HEADING_SUGGEST_QUANTITY, TABLE_HEADING_PURCHASE_QUANTITY);

			if ($HTTP_POST_VARS['btn_csv_import']) {
				$js = '';
				for ($i=0; $i < count($imported_products_array); $i++) {
					$row = $imported_products_array[$i];
					$js .= "products_ids.push(".$row['prd_id'].");\n";
					$row_style = ($i%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
					
					// Grab on hold ordered qty
					$product_floating_qty = tep_calculate_floating_available_qty($row['prd_id']);
					
                    //Get total first list qty for this product in supplier orders where status = draft and no partial receive yet.
                    $prod_first_list_qty = 0;
                    $forecast_available_qty = (int)$row['resource']['products_quantity'] + $product_floating_qty;
                    $forecast_actual_qty = (int)$row['resource']['products_actual_quantity'];
                    $product_first_list_qty_select_sql = "	SELECT sum(solp.products_quantity) as first_list_qty_total
							                            	FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
							                            	INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
							                                	ON (sol.supplier_order_lists_id = solp.supplier_order_lists_id 
							                                		AND solp.products_id = '".$row['prd_id']."') 
							                            	WHERE sol.supplier_order_lists_status IN (1, 5) 
							                            		AND IF(sol.supplier_order_lists_status=1, solp.supplier_order_lists_type='2' AND solp.products_received_quantity IS NULL, solp.supplier_order_lists_type='1')";
                    $product_first_list_qty_result_sql = tep_db_query($product_first_list_qty_select_sql);
                    if (($row2 = tep_db_fetch_array($product_first_list_qty_result_sql)) && ($first_list_qty_total = (int)$row2['first_list_qty_total'])) {
                        $forecast_available_qty += $first_list_qty_total;
                        $forecast_actual_qty += $first_list_qty_total;
                    }

					//This max quantity is suggested from the list settings.
					$product_max_quantity = get_inventory_space_in_range($row['prd_id']);
                    if ((string)$row['overwrite_maximum_quantity']) {
                        $product_overwrite_max_quantity = (string)$row['overwrite_maximum_quantity'];
                    } else {
                        //We want $product_overwrite_max_quantity to always have a value, to calculate $prod_suggest_qty
                        $product_overwrite_max_quantity = $product_max_quantity;
                    }
                    
					$no_entry = false;
					if ($subaction == 'suggest_qty') {
					    //Suggest new.
					    //We use $product_overwrite_max_quantity because it will always have the max we are using.
						if ($forecast_available_qty < 0) {
							$prod_suggest_qty = (int)$product_overwrite_max_quantity - $forecast_available_qty;
						} else {
							$prod_suggest_qty = (int)$product_overwrite_max_quantity - $forecast_actual_qty;
						}
					} else {
					    //Get saved.
						$prod_suggest_qty = (int)$row['products_purchases_suggest_quantity'];
					}                    

					$product_display_name = $row['resource']['products_cat_path'] . '<span id="span_prod_name_'.$row['prd_id'].'" class="show"> > ' . $row['resource']['products_name'] . ' ('.$row['prd_id'] . ')</span>';

					$product_name = $row['resource']['products_cat_path']." > ". $row['resource']['products_name'];

					$disabled = $row['resource']['products_purchases_disabled'] == '1' ? 'DISABLED' : '';
					$disabled_style = $row['resource']['products_purchases_disabled'] == '1' ? ' style="background:#D4D0C8"' : '';

					if ((int)$row['resource']['products_purchases_disabled'] != 1) {
						$export_csv_array[$row['prd_id']] = array($row['prd_id'], $product_name, $product_max_quantity, (string)$row['overwrite_maximum_quantity'], $forecast_available_qty, $forecast_actual_qty, $prod_suggest_qty);
						$export_csv_array[$row['prd_id']][] = $row['resource']['products_purchases_quantity_overwrite']=='1' ? $row['resource']['products_purchases_selling_quantity'] : '';
					}
?>
											<tbody id="row<?=$row['prd_id']?>">
												<tr class="<?=$row_style?>" id="<?="prodRow_".$row['prd_id']?>" onmouseover="rowOverEffect(this, 'ordersListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
													<td class="ordersRecords" valign="top"><?=tep_draw_checkbox_field("chkSelected[".$row['prd_id']."]", '1', false, '', ' id="chkSelected'.$row['prd_id'].'" ')?></td>
													<td class="ordersRecords"><?=$product_display_name?></td>
													<td class="ordersRecords" align="center"><?=$product_max_quantity?></td>
													<td class="ordersRecords" align="center">
														<?=tep_draw_input_field("maxQty[{$row['prd_id']}]", ((string)$row['overwrite_maximum_quantity'] ? (string)$product_overwrite_max_quantity : ''), 'size="7" id="maxQty'.$row['prd_id'].'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" ' . $disabled . $disabled_style)?>
													</td>
													<td class="ordersRecords" align="right"><?=$forecast_available_qty?></td>
													<td class="ordersRecords" align="right"><?=$forecast_actual_qty?></td>
													<td class="ordersRecords" align="right">
														<?=$prod_suggest_qty . tep_draw_hidden_field("sugQty[".$row['prd_id']."]", $prod_suggest_qty)?>
													</td>
													<td class="ordersRecords" align="center">
														<?=tep_draw_input_field("sellingQty[".$row['prd_id']."]", tep_not_null($row['purchase_quantity']) ? $row['purchase_quantity'] : '', 'size="7" id="sellingQty'.$row['prd_id'].'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" ' . $disabled . $disabled_style)?>
													</td>
													<td class="ordersRecords" align="right" colspan="2"><?=tep_draw_checkbox_field("chkDisabled[".$row['prd_id']."]", '1', ((int)$row['resource']['products_purchases_disabled']==1) ? true : false,'',' id="chkDisabled'.$row['prd_id'].'" onclick="disableTextBoxes('.$row['prd_id'].', this, 2)" ')?></td>
													<?=tep_draw_hidden_field("product_ids[]", $row['prd_id'])?>
												</tr>
											</tbody>
<?				}
			} else {
				$products_list_select_product = "	SELECT p.products_id as prd_id, p.products_quantity, p.products_actual_quantity, pd.products_name, p.products_cat_path, p.products_main_cat_id, pp.*
													FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
													INNER JOIN " . TABLE_PRODUCTS . " AS p
														ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
													INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
														ON p2c.products_id=pd.products_id
													LEFT JOIN ".TABLE_PRODUCTS_PURCHASES." as pp
														ON (p2c.products_id=pp.products_id AND pp.products_purchases_lists_id='".$list_id."')
													WHERE p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "'
													ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
				$products_list_result_product = tep_db_query($products_list_select_product);
				$i=0;
				$js = '';
				while ($row = tep_db_fetch_array($products_list_result_product)) {
					$js .= "products_ids.push(".$row['prd_id'].");\n";

					$row_style = ($i%2) ? 'ordersListingEven' : 'ordersListingOdd' ;

					if (!tep_not_null($row['products_purchases_lists_id'])) {
						// Newly created product default to disabled
						$product_name_style = "redIndicator";
						$row['products_purchases_disabled'] = 1;
					} else {
						$product_name_style = '';
					}
					
					// Grab on hold ordered qty
					$product_floating_qty = tep_calculate_floating_available_qty($row['prd_id']);
					
                    //Get total first list qty for this product in supplier orders where status = draft and no partial receive yet.
                    $prod_first_list_qty = 0;
                    $forecast_available_qty = (int)$row['products_quantity'] + $product_floating_qty;
                    $forecast_actual_qty = (int)$row['products_actual_quantity'];
                    
                    $product_first_list_qty_select_sql = "	SELECT sum(solp.products_quantity) as first_list_qty_total
                            								FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
								                            INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
								                                ON (sol.supplier_order_lists_id = solp.supplier_order_lists_id 
								                                	AND solp.products_id = '".$row['prd_id']."') 
								                            WHERE sol.supplier_order_lists_status IN (1, 5) 
								                            	AND IF(sol.supplier_order_lists_status=1, solp.supplier_order_lists_type='2' AND solp.products_received_quantity IS NULL, solp.supplier_order_lists_type='1')";
                    $product_first_list_qty_result_sql = tep_db_query($product_first_list_qty_select_sql);
                    if (($row2 = tep_db_fetch_array($product_first_list_qty_result_sql)) && ($first_list_qty_total = (int)$row2['first_list_qty_total'])) {
                        $forecast_available_qty += $first_list_qty_total;
                        $forecast_actual_qty += $first_list_qty_total;
                    }

        			//This max quantity is suggested from the list settings.
                    $product_max_quantity = get_inventory_space_in_range($row['prd_id']);
                    if ((int)$row['products_purchases_max_quantity_overwrite']) {
                        $product_overwrite_max_quantity = $row['products_purchases_max_quantity'];
                    } else {
                        //We want $product_overwrite_max_quantity to always have a value, to calculate $prod_suggest_qty
                        $product_overwrite_max_quantity = $product_max_quantity;
                    }
                    
					$no_entry = false;
					if ($subaction == 'suggest_qty') {
					    //Suggest new.
					    //We use $product_overwrite_max_quantity because it will always have the max we are using.
						if ($forecast_available_qty < 0) {
							$prod_suggest_qty = (int)$product_overwrite_max_quantity - $forecast_available_qty;
						} else {
							$prod_suggest_qty = (int)$product_overwrite_max_quantity - $forecast_actual_qty;
						}
					} else {
					    //Get saved.
						$prod_suggest_qty = (int)$row['products_purchases_suggest_quantity'];
					}

					$product_display_name = $row['products_cat_path'] . '<span id="span_prod_name_'.$row['prd_id'].'" class="show"> > ' . $row['products_name'] . ' ('.$row['prd_id'] . ')</span>';

					$product_name = $row['products_cat_path']." > ". $row['products_name'];

					$disabled = $row['products_purchases_disabled'] == '1' ? 'DISABLED' : '';
					$disabled_style = $row['products_purchases_disabled'] == '1' ? ' style="background:#D4D0C8"' : '';

					if ((int)$row['products_purchases_disabled'] != 1) {
						$export_csv_array[$row['prd_id']] = array($row['prd_id'], $product_name, $product_max_quantity, (int)$row['products_purchases_max_quantity'], $forecast_available_qty, $forecast_actual_qty, $prod_suggest_qty);
						$export_csv_array[$row['prd_id']][] = $row['products_purchases_quantity_overwrite']=='1' ? $row['products_purchases_selling_quantity'] : '';
					}
?>
											<tbody id="row<?=$row['prd_id']?>">
												<tr class="<?=$row_style?>" id="<?="prodRow_".$row['prd_id']?>" onmouseover="rowOverEffect(this, 'ordersListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
													<td class="ordersRecords" valign="top"><?=tep_draw_checkbox_field("chkSelected[".$row['prd_id']."]", '1', false, '', ' id="chkSelected'.$row['prd_id'].'" ')?></td>
													<td class="ordersRecords"><span class="<?=$product_name_style?>"><?=$product_display_name?></span></td>
													<td class="ordersRecords" align="center"><?=$product_max_quantity ?></td>
													<td class="ordersRecords" align="center">
														<?=tep_draw_input_field("maxQty[{$row['prd_id']}]", ((int)$row['products_purchases_max_quantity_overwrite'] ? (string)$product_overwrite_max_quantity : ''), 'size="7" id="maxQty'.$row['prd_id'].'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" ' . $disabled . $disabled_style)?>
													</td>
													<td class="ordersRecords" align="right"><?=$forecast_available_qty?></td>
													<td class="ordersRecords" align="right"><?=$forecast_actual_qty?></td>
													<td class="ordersRecords" align="right">
														<?=$prod_suggest_qty . tep_draw_hidden_field("sugQty[".$row['prd_id']."]", $prod_suggest_qty)?>
													</td>
													<td class="ordersRecords" align="center">
														<?=tep_draw_input_field("sellingQty[".$row['prd_id']."]", $row['products_purchases_quantity_overwrite']=='1' ? $row['products_purchases_selling_quantity'] : '', 'size="7" id="sellingQty'.$row['prd_id'].'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" ' . $disabled . $disabled_style)?>
													</td>
													<td class="ordersRecords" align="right" colspan="2"><?=tep_draw_checkbox_field("chkDisabled[".$row['prd_id']."]", '1', ((int)$row['products_purchases_disabled']==1) ? true : false,'',' id="chkDisabled'.$row['prd_id'].'" onclick="disableTextBoxes('.$row['prd_id'].', this, 2)" ')?></td>
													<?=tep_draw_hidden_field("product_ids[]", $row['prd_id'])?>
												</tr>
											</tbody>
<? 					$i++;
				}
			}

			echo tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($export_csv_array)) . "\n";
?>
										</table>
									</td>
								</tr>
								<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
								</tr>
								<tr>
									<td>
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td align="left">
													<?=tep_draw_file_field('csv_import', 'size="50"')?>
													<input type="submit" name="btn_csv_import" value="Import" title="Import csv file" class="inputButton">
												</td>
												<td align="right">
													<input type="submit" name="btn_csv_export" value="Export" title="Export as csv file" class="inputButton">
													<input type="submit" name="btn_db_update" value="<?=IMAGE_BUTTON_UPDATE?>" title="<?=IMAGE_BUTTON_UPDATE?>" class="inputButton">
												</td>
											</tr>
										</table>
									</td>
								</tr>
								</form>
							</table>
						</td>
					</tr>
        			<?php
                        if ($purchase_list_setting_array['ppls_sales_retrieval_method'] == 'by_last_n_days') {
                            $js .= "\n enableFieldSet('rdoSalesRetrievalMethod2');";
                        } else {
                            //if date range selected, or nothing set.
                            $js .= "\n enableFieldSet('rdoSalesRetrievalMethod1');";
                        }
        			?>
					<script>
					<!--
						<?=$js?>
						var disabled_state = GetCookie('<?=$list_id?>' + '_purchase_show_hide_disabled');
						show_hide_disabled(disabled_state == 1 ? true : false, '<?=$list_id?>');
						
						var pname_state = GetCookie('<?=$list_id?>' + '_purchase_show_hide_pname');
						show_hide_product_name(pname_state == 1 ? true : false, '<?=$list_id?>');
						
						function purchase_list_form_checking() {
							var error_message = '<?=JS_ERROR?>';
							var error = false;
							var focus_field = '';
							
							var list_name = DOMCall('purchase_list_name');
							if (trim_str(list_name.value) == '') {
								error_message += '* Please enter the name for purchase list.' + "\n";
								focus_field = 'purchase_list_name';
								error = true;
							}

                            var sales_retrieval_method1 = DOMCall('rdoSalesRetrievalMethod1');
                            var sales_retrieval_method2 = DOMCall('rdoSalesRetrievalMethod2');
                            if (sales_retrieval_method1.checked) {
                                //by date range    
    							var sales_start_date = DOMCall('ppls_sales_start_date');

    							if (trim_str(sales_start_date.value) == '') {
    								error_message += '* Please enter the sales start date.' + "\n";
    								focus_field = 'ppls_sales_start_date';
    								error = true;
    							}
    							
    							var sales_end_date_temp = DOMCall('ppls_sales_end_date').value;
                                if (trim_str(sales_end_date_temp) == '') {
                                    sales_end_date_temp = '<?=date('Y-m-d')?>';
                                }
    							
    							if (sales_start_date.value > sales_end_date_temp) {
    								error_message += '* Sales Start Date is not after Sales End Date.' + "\n";
    								focus_field = 'ppls_sales_start_date';
    								error = true;
    							}
    							
                            } else if (sales_retrieval_method2.checked){
                                //by last N days
    							var ppls_last_n_days_sales = DOMCall('ppls_last_n_days_sales');
    							if (!parseInt(ppls_last_n_days_sales.value) > 0) {
    								error_message += '* Please enter the last number days of sales .' + "\n";
    								focus_field = 'ppls_last_n_days_sales';
    								error = true;
    							}
                            }
							
							var inventory_days = DOMCall('ppls_inventory_days');
							if (trim_str(inventory_days.value) == '') {
								error_message += '* Please enter the inventory days.' + "\n";
								focus_field = 'ppls_inventory_days';
								error = true;
							}
							
							if (error == true) {
								alert(error_message);
								document.getElementById(focus_field).focus();
								return false;
							} else {
								return true;
							}
						}
					//-->
					</script>
<?
	}
} else if ($action == "edit_acc_set") {
	/*
	$list_info_select_sql = "	SELECT products_purchases_lists_name
								FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id='".(int)$_REQUEST["lID"]."'";
	*/
	$list_info_select_sql = "	SELECT restock_character_sets_name
								FROM " . TABLE_RESTOCK_CHARACTER_SETS . " WHERE restock_character_sets_id='".(int)$_REQUEST["rcsID"]."'";
	$list_info_result_sql = tep_db_query($list_info_select_sql);
	
	if ($list_info_row = tep_db_fetch_array($list_info_result_sql)) {
		$rstk_id = (int)$_REQUEST["rcsID"];

		$character_set_info_array = array();
		/*
		$character_sets_select_sql = "	SELECT distinct(rcs.restock_character_sets_id), rcs.restock_character_sets_name
									  	FROM " . TABLE_RESTOCK_CHARACTER_SETS . " AS rcs
								  	  	INNER JOIN ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ." AS rcs2pl
								  	  		ON (rcs.restock_character_sets_id=rcs2pl.restock_character_sets_id)
									  	WHERE rcs.restock_character_sets_id = '" . $list_id . "' 
									  	ORDER BY rcs.restock_character_sets_name";
		*/
		$character_sets_select_sql = "	SELECT restock_character_sets_id, restock_character_sets_name
									  	FROM " . TABLE_RESTOCK_CHARACTER_SETS . "
									  	WHERE restock_character_sets_id = '" . $rstk_id . "'
									  	ORDER BY restock_character_sets_name";
		$character_sets_result_sql = tep_db_query($character_sets_select_sql);
		
		$rstk_character_info_select_sql = "	SELECT restock_character_sets_cat_id, restock_character_sets_sort_order, restock_character_sets_name
										  	FROM " . TABLE_RESTOCK_CHARACTER_SETS . "
										  	WHERE restock_character_sets_id = '" . $rstk_id . "'";
		$rstk_character_info_result_sql = tep_db_query($rstk_character_info_select_sql);
		$rstk_character_info_row = tep_db_fetch_array($rstk_character_info_result_sql);
		
		$categories_array = array($rstk_character_info_row['restock_character_sets_cat_id']);
		tep_get_subcategories($categories_array, $rstk_character_info_row['restock_character_sets_cat_id']);
		//$categories_array = array_merge($rstk_character_info_row['restock_character_sets_cat_id'], $subcategories_array);
		/*
		$products_list_select_sql = "	SELECT p.products_cat_path, pp.*
										FROM " . TABLE_PRODUCTS . " AS p
										INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
											ON p.products_id=pd.products_id
										INNER JOIN ".TABLE_PRODUCTS_PURCHASES." as pp
											ON (p.products_id=pp.products_id AND pp.products_purchases_lists_id='".$list_id."' AND products_purchases_disabled <> 1)
										WHERE p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0 AND pd.language_id = '" . $languages_id . "'
										ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
		*/
		$products_list_select_sql = "	SELECT p2c.products_id, p.products_cat_path
										FROM ". TABLE_PRODUCTS_TO_CATEGORIES ." AS p2c
										INNER JOIN ". TABLE_PRODUCTS ." AS p 
											ON (p.products_id = p2c.products_id)
										INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
											ON (p.products_id = pd.products_id)
										WHERE p.products_bundle = '' 
											AND p.products_bundle_dynamic = ''
											AND p.custom_products_type_id = 0
											AND pd.language_id = '" . $languages_id . "'
											AND p2c.categories_id IN ('" . implode("', '", $categories_array) ."')
										ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order";
		$products_list_result_sql = tep_db_query($products_list_select_sql);
		
		$export_csv_array['HEADER'] = array(TABLE_CSV_HEADING_PRODUCT_ID, TABLE_CSV_HEADING_PRODUCT);

		$all_categories_array = tep_get_eligible_category_tree(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 0, '___', '', $all_categories_array);
		$set_name_to_index_array = array();
?>
					<!--tr>
						<td class="main"><?=ENTRY_PURCHASE_LIST_NAME . '&nbsp;' . $list_info_row["products_purchases_lists_name"]?></td>
					</tr-->
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td>
<?		echo tep_draw_form('edit_restock_character_form', FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction')) . 'subaction=edit_rstk_character', 'post', 'enctype="multipart/form-data"');?>
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
											<tr>
												<td class="main" width="20%"><?=ENTRY_RESTOCK_CHARACTER_SETS_NAME?></td>
												<td class="main"><?=tep_draw_input_field('restock_character_sets_name', $rstk_character_info_row["restock_character_sets_name"], 'size="20" id="restock_character_sets_name"')?></td>
											</tr>
											<tr>
							        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
							      			</tr>
											<tr>
												<td class="main" width="15%"><?=ENTRY_MAIN_CAT?></td>
												<td class="main"><?=tep_draw_pull_down_menu("restock_character_sets_cat_id", $all_categories_array, $rstk_character_info_row['restock_character_sets_cat_id'], '')?></td>
											</tr>
											<tr>
							        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
							      			</tr>
											<tr>
			            						<td class="main" width="15%"><?=ENTRY_SORT_ORDER?></td>
			            						<td class="main"><?=tep_draw_input_field('restock_character_sets_sort_order', !empty($rstk_character_info_row['restock_character_sets_sort_order']) ? $rstk_character_info_row['restock_character_sets_sort_order'] : '50000', 'size="10" id="restock_character_sets_sort_order" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
			        						</tr>
			        						<tr>
							        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
							      			</tr>
			        						<tr>
			        							<td>&nbsp;</td>
			        							<td>
				        							<input type="submit" name="btn_db_update" value="<?=IMAGE_BUTTON_UPDATE?>" title="<?=IMAGE_BUTTON_UPDATE?>" class="inputButton">
				        						</td>
			        						</tr>
										</table>
									</form>
									</td>
								</tr>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
				      			</tr>
								<tr>
									<td><?=tep_draw_separator()?></td>
								</tr>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
				      			</tr>
								<tr>
									<td>
<?		echo tep_draw_form('restock_character_form', FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction')) . 'subaction=update_acc_set', 'post', 'enctype="multipart/form-data"'); ?>
										<table border="0" width="100" cellspacing="1" cellpadding="2">
						   					<tr>
											    <td class="reportBoxHeading" nowrap><?=TABLE_HEADING_PRODUCT?></td>
<?		while ($character_sets_row = tep_db_fetch_array($character_sets_result_sql)) {
			if ($HTTP_POST_VARS['btn_csv_import']) {
				//if (!in_array($character_sets_row["restock_character_sets_name"], array_keys($set_header_array))) {
				if (!in_array($character_sets_row["restock_character_sets_id"], array_keys($set_header_array))) {
					continue;
				}
			}
			
			echo tep_draw_hidden_field('set_name['.$character_sets_row["restock_character_sets_id"].']', $character_sets_row["restock_character_sets_name"]);
			echo tep_draw_hidden_field('restock_character_sets_name', $character_sets_row["restock_character_sets_name"]);
			//echo '							<td class="reportBoxHeading">'.tep_draw_input_field('set_name['.$character_sets_row["restock_character_sets_id"].']', $character_sets_row["restock_character_sets_name"], 'size="18"').'</td>';
			echo '								<td class="reportBoxHeading" align="center" nowrap>'. TABLE_CSV_HEADING_RSTK_CHAR .'</td>';
			$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_RSTK_CHAR;

			$set_name_to_index_array[$character_sets_row["restock_character_sets_id"]] = $character_sets_row["restock_character_sets_id"];
			$character_set_info_array[$character_sets_row["restock_character_sets_id"]] = array();

			$character_info_select_sql = "SELECT products_id, restock_character FROM " . TABLE_RESTOCK_CHARACTER_INFO . " WHERE restock_character_sets_id = '" . $character_sets_row["restock_character_sets_id"] . "'";
			$character_info_result_sql = tep_db_query($character_info_select_sql);
			while ($character_info_row = tep_db_fetch_array($character_info_result_sql)) {
				$character_set_info_array[$character_sets_row["restock_character_sets_id"]][$character_info_row["products_id"]] = $character_info_row["restock_character"];
			}
		}
		
		if (count($character_set_info_array) == 0) {
			//echo '							<td class="reportBoxHeading">'.tep_draw_input_field('new_set_name', ($HTTP_POST_VARS['btn_csv_import'] && in_array('NEW SET', array_keys($set_header_array)) ? 'NEW SET' : ''), 'size="18"', false, 'text', false).'</td>';
			echo '								<td class="reportBoxHeading">'.tep_draw_input_field('new_set_name', ($HTTP_POST_VARS['btn_csv_import'] && in_array('NEW SET', array_keys($set_header_array)) ? 'NEW SET' : $list_info_row["restock_character_sets_name"]), 'size="18"', false, 'text', false).'</td>';
			$export_csv_array['HEADER'][] = TABLE_CSV_HEADING_RSTK_CHAR;
		} else {
?>
												<td class="reportBoxHeading" align="center" nowrap><!--a href="<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params()).'#top'?>" onClick="showMe('<?=$_REQUEST['rcsID']?>', '');document.href='#top'"><?=LINK_VIEW_LOGS?></a--></td>
<?		} ?>
											</tr>
<?		if ($HTTP_POST_VARS['btn_csv_import']) {
			for ($i=0; $i < count($imported_products_array); $i++) {
				$products_list_row = $imported_products_array[$i];

				$row_style = ($i%2) ? 'ordersListingEven' : 'ordersListingOdd' ;

				$pid = $products_list_row["prd_id"];

				$product_name = $products_list_row["resource"]["products_cat_path"];

				$export_csv_array[$pid] = array($pid, $product_name);

				echo '						<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
												<td class="reportRecords" valign="top" nowrap>'.$products_list_row["resource"]["products_cat_path"].'</td>';

				if (count($set_header_array)) {
					foreach ($set_header_array as $set_name => $index) {
						if ($set_name != 'NEW SET') {
							$set_id = $set_name_to_index_array[$set_name];
							echo '				<td class="reportRecords">'.tep_draw_input_field('set_character['.$set_id.']['.$pid.']', $products_list_row["set_info"][$set_name], 'size="18" ' . (isset($character_set_info_array[$set_id][$pid]) || tep_not_null($products_list_row["set_info"][$set_name]) ? '' : 'class="redInputBox"')).'</td>';
?>
												<td nowrap><a href="<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params()).'#top'?>" onClick="showMe('<?=$set_id?>', '<?=$pid?>');"><?=LINK_VIEW_LOGS?></a></td>
<?
							$export_csv_array[$pid][] = $products_list_row["set_info"][$set_name];
						} 
					}
				} else {
					echo '						<td class="reportRecords">'.tep_draw_input_field('new_character['.$pid.']', $products_list_row["set_info"]['NEW SET'], 'size="18"').'</td>';
				}
				echo '						</tr>';
			}
		} else {
			$row_count = 0;
			while ($products_list_row = tep_db_fetch_array($products_list_result_sql)) {
				$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
				$pid = $products_list_row["products_id"];

				$product_name = $products_list_row["products_cat_path"];

				$export_csv_array[$pid] = array($pid, $product_name);

				echo '						<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">';
				echo '							<td class="reportRecords" valign="top" nowrap>'. $products_list_row["products_cat_path"] .'</td>';

				if (count($character_set_info_array)) {
					foreach ($character_set_info_array as $set_id => $set_res) {
						echo '					<td class="reportRecords">'.tep_draw_input_field('set_character['.$set_id.']['.$pid.']', $set_res[$pid], 'size="18" ' . (isset($set_res[$pid]) ? '' : 'class="redInputBox"')).'</td>';
?>
												<td nowrap><a href="<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params()).'#top'?>" onClick="showMe('<?=$set_id?>', '<?=$pid?>');"><?=LINK_VIEW_LOGS?></a></td>
<?
						$export_csv_array[$pid][] = $set_res[$pid];
					}
				} else {
					echo '						<td class="reportRecords">'.tep_draw_input_field('new_character['.$pid.']', '', 'size="18"').'</td>';
				}
				echo '						</tr>';

				$row_count++;
			}
		}

		echo tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($export_csv_array)) . "\n";
?>
										</table>
										<div id="theLayer" style="position:absolute;width:450px;left:120;top:200;visibility:hidden;"></div>
<?	}	?>
									</td>
								</tr>
								<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
								</tr>
								<tr>
									<td>
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td align="left">
													<?=tep_draw_file_field('csv_import', 'size="50"')?>
													<input type="submit" name="btn_csv_import" value="Import" title="Import csv file" class="inputButton">
												</td>
												<td align="right">
													<input type="submit" name="btn_csv_export" value="Export" title="Export as csv file" class="inputButton">
													<input type="submit" name="btn_db_update" value="<?=IMAGE_BUTTON_UPDATE?>" title="<?=IMAGE_BUTTON_UPDATE?>" class="inputButton">
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
							</form>
						</td>
					</tr>
<?
} else if ($action == "manage_rstk") {
?>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
								    <td width="30%" class="reportBoxHeading"><?=TABLE_HEADING_RESTOCK_CHARACTERS?></td>
								    <td width="30%" align="left" class="reportBoxHeading"><?=TABLE_HEADING_MAIN_CAT?></td>
								    <td width="15%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_SORT_ORDER?></td>
								    <td width="20%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_LAST_MODIFY_DATE?></td>
<?	if ($restock_account_info_permission) { ?>
								    <td width="12%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
<?	} ?>
								</tr>
<?
	$rstk_character_sets_select_sql = "	SELECT rcs.*, cd.categories_name
										FROM ". TABLE_RESTOCK_CHARACTER_SETS ." AS rcs
										LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
												ON (rcs.restock_character_sets_cat_id=cd.categories_id AND cd.language_id='".$languages_id."')								  
										  ORDER BY rcs.restock_character_sets_sort_order, cd.categories_name, rcs.restock_character_sets_name";
	$rstk_character_sets_result_sql = tep_db_query($rstk_character_sets_select_sql);
	
	$row_count = 0;
	while ($rstk_character_sets_row = tep_db_fetch_array($rstk_character_sets_result_sql)) {
		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
		
		$rstk_list_id = $rstk_character_sets_row['restock_character_sets_id'];
		$safe_list_name = htmlspecialchars(addslashes($rstk_character_sets_row["restock_character_sets_name"]), ENT_QUOTES);
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
								    <td class="reportRecords"><?=$rstk_character_sets_row['restock_character_sets_name']?></td>
								    <td align="left" class="reportRecords"><?=($rstk_character_sets_row['restock_character_sets_cat_id'] > 0) ? $rstk_character_sets_row['categories_name'] : TEXT_TOP ?></td>
								    <td align="center" class="reportRecords"><?=$rstk_character_sets_row['restock_character_sets_sort_order']?></td>
								    <td align="center" class="reportRecords"><?=$rstk_character_sets_row['restock_character_sets_last_modified']?></td>
<?
		if ($restock_account_info_permission) {
?>
									<td align="center" class="reportRecords" valign="top" nowrap>&nbsp;
										<a href="<?=tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=edit_acc_set&rcsID='.$rstk_list_id)?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit restock character set", "", "", 'align="top"')?></a>
										<a href="javascript:void(confirm_delete('<?=$safe_list_name?>', 'Restock Characters Set', '<?=tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'subaction=confirm_delete_rstk&rcsID='.$rstk_list_id.'&rstk_name='.$rstk_character_sets_row['restock_character_sets_name'])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete restock character set", "", "", 'align="top"')?></a>
									</td>
<?		} ?>
								</tr>
<?		$row_count++;
	}
?>
								<tr>
									<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
								</tr>
								<tr>
									<td colspan="5"><? echo '[ <a href="'.tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=add_rstk').'" class="actionLink">'.LINK_ADD_RESTOCK_CHARACTER_SET.'</a> ]';?></td>
								</tr>
							</table>
						</td>
					</tr>
<?
} else if ($action == "add_rstk") {
	$all_categories_array = tep_get_eligible_category_tree(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 0, '___', '', $all_categories_array);
?>
					<tr>
						<td>
<?	echo tep_draw_form('add_restock_character_form', FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction')) . 'subaction=add_rstk', 'post', 'enctype="multipart/form-data"'); ?>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td class="main" width="20%"><?=ENTRY_RESTOCK_CHARACTER_SETS_NAME?></td>
									<td class="main"><?=tep_draw_input_field('new_restock_character_sets_name', '', 'size="20" id="restock_character_sets_name"')?></td>
								</tr>
								<tr>
									<td class="main"><?=ENTRY_MAIN_CAT?></td>
									<td class="main"><?=tep_draw_pull_down_menu("new_restock_character_sets_cat_id", $all_categories_array, '', '')?></td>
								</tr>
								<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
								<tr>
            						<td class="main"><?=ENTRY_SORT_ORDER?></td>
            						<td class="main"><?=tep_draw_input_field('new_restock_character_sets_sort_order', '50000', 'size="10" id="restock_character_sets_sort_order" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
        						</tr>
        						<tr>
				        			<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				      			</tr>
				      			<tr>
				      				<td>&nbsp;</td>
	        						<td>
	        							<input type="submit" name="btn_db_update" value="<?=IMAGE_BUTTON_UPDATE?>" title="<?=IMAGE_BUTTON_UPDATE?>" class="inputButton">
	        						</td>
        						</tr>
							</table>
						</form>
						</td>
					</tr>
<?
} else if ($action == "edit_rstk") {
	$restock_character_sets_list_from_array = array( array ('id' => '', "text" => 'Select Restock Character Lists', "type" => 'optgroup') );
	$restock_character_sets_list_to_array = array( array ('id' => '', "text" => 'Selected Restock Character Lists', "type" => 'optgroup') );
	
	$products_purchases_lists_select_sql = "SELECT products_purchases_lists_name, products_purchases_lists_cat_id
											FROM ". TABLE_PRODUCTS_PURCHASES_LISTS ." 
											WHERE products_purchases_lists_id='". (int)$_REQUEST["lID"] ."'";
	$products_purchases_lists_result_sql = tep_db_query($products_purchases_lists_select_sql);
	$products_purchases_lists_row = tep_db_fetch_array($products_purchases_lists_result_sql);
	
	$rstk_character_selected_select_sql = "	SELECT rcs2pl.restock_character_sets_id, rcs.restock_character_sets_name
											FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ." AS rcs2pl
											INNER JOIN ". TABLE_RESTOCK_CHARACTER_SETS ." AS rcs
											  	ON(rcs2pl.restock_character_sets_id=rcs.restock_character_sets_id)
											WHERE products_purchases_lists_id='". (int)$_REQUEST["lID"] ."'
											ORDER BY rcs.restock_character_sets_name";
	$rstk_character_selected_result_sql = tep_db_query($rstk_character_selected_select_sql);
	
	while ($rstk_character_selected_row = tep_db_fetch_array($rstk_character_selected_result_sql)) {
		$restock_character_sets_list_to_array[] = array(	'id' => $rstk_character_selected_row['restock_character_sets_id'],
													     	'text' => $rstk_character_selected_row['restock_character_sets_name']
											    			);
	}
	
	tep_get_parent_categories($parent_categories_array, $products_purchases_lists_row['products_purchases_lists_cat_id']); //check category that have same parent
	$parent_categories_array = array_merge($products_purchases_lists_row['products_purchases_lists_cat_id'], $parent_categories_array);
	
	$unassigned_rstk_character_select_sql = "	SELECT rcs.restock_character_sets_id, rcs.restock_character_sets_name
											  	FROM ". TABLE_RESTOCK_CHARACTER_SETS ." AS rcs 
											  	LEFT JOIN " . TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ." AS rcs2pl 
													ON (rcs.restock_character_sets_id=rcs2pl.restock_character_sets_id AND rcs2pl.products_purchases_lists_id='".(int)$_REQUEST["lID"]."')
												WHERE rcs.restock_character_sets_cat_id IN ('" . implode("', '", $parent_categories_array) ."') 
													AND rcs2pl.restock_character_sets_id IS NULL 
												ORDER BY rcs.restock_character_sets_name";
	$unassigned_rstk_character_result_sql = tep_db_query($unassigned_rstk_character_select_sql);
	
	while ($unassigned_rstk_character_row = tep_db_fetch_array($unassigned_rstk_character_result_sql)) {
		$restock_character_sets_list_from_array[] = array(	'id' => $unassigned_rstk_character_row['restock_character_sets_id'],
													     	'text' => $unassigned_rstk_character_row['restock_character_sets_name']
											    			);
	}
?>
					<tr>
						<td>
<?	
	echo tep_draw_form('set_restock_character_form', FILENAME_PRODUCTS_PURCHASE_QUANTITY, tep_get_all_get_params(array('subaction')) . 'subaction=update_rstk_list', 'post', 'enctype="multipart/form-data"'); 
	echo tep_draw_hidden_field("products_purchases_lists_id", (int)$_REQUEST["lID"]);
?>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
            						<td class="main" width="15%"><?=ENTRY_PURCHASE_LIST_NAME?></td>
            						<td class="main"><?=$products_purchases_lists_row['products_purchases_lists_name']?></td>
        						</tr>
        						<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
								</tr>
        						<tr>
            						<td class="main" valign="top"><?=ENTRY_RESTOCK_CHARACTERS?></td>
            						<td class="main"><?=tep_draw_js_select_boxes('restock_character_sets_id', $restock_character_sets_list_from_array, $restock_character_sets_list_to_array, ' size="10" style="width:20em;"');?></td>
        						</tr>
        						<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
								</tr>
        						<tr>
	        						<td colspan="2" align="right">
	        							<input type="submit" name="btn_db_update" value="<?=IMAGE_BUTTON_UPDATE?>" title="<?=IMAGE_BUTTON_UPDATE?>" class="inputButton" onClick="return form_checking(this.form);">
	        						</td>
        						</tr>
							</table>
						</form>
						</td>
					</tr>
<?
} else {
	// Clean up of non-existed products
	$deleted_products_select_sql = "SELECT pp.products_id
									FROM " . TABLE_PRODUCTS_PURCHASES . " AS pp
									LEFT JOIN " . TABLE_PRODUCTS . " AS p
										ON pp.products_id = p.products_id
									WHERE p.products_id IS NULL ";
	$deleted_products_result_sql = tep_db_query($deleted_products_select_sql);
	while ($deleted_products_row = tep_db_fetch_array($deleted_products_result_sql)) {
		tep_db_query("DELETE FROM " . TABLE_PRODUCTS_PURCHASES . " WHERE products_id = '" . tep_db_input($deleted_products_row["products_id"]) . "'");
	}

	$admin_file_cat_ids_array = tep_get_admin_file_cat_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY);
	if (!in_array(0, $admin_file_cat_ids_array)) {
		$admin_permitted_cat_array = tep_get_eligible_categories(FILENAME_PRODUCTS_PURCHASE_QUANTITY, '', 0);

		$cat_where_str = " ppl.products_purchases_lists_cat_id IN ('" . implode("', '", $admin_permitted_cat_array) . "') ";
	} else {
		$cat_where_str = " 1 ";
	}

	$purchase_list_select_sql = "	SELECT ppl.*, cd.categories_name
									FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " AS ppl
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
										ON (ppl.products_purchases_lists_cat_id=cd.categories_id AND cd.language_id='".$languages_id."')
									WHERE " . $cat_where_str . "
									ORDER BY ppl.products_purchases_lists_sort_order, ppl.products_purchases_lists_name";
	$purchase_list_result_sql = tep_db_query($purchase_list_select_sql);

	if (tep_db_num_rows($purchase_list_result_sql)) {
?>
					<tr>
            			<td valign="top">
            				<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_LIST_NAME?></td>
								    <td align="left" class="reportBoxHeading"><?=TABLE_HEADING_MAIN_CAT?></td>
								    <td width="10%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_QTY_ROUND_UP?></td>
								    <td width="8%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_SORT_ORDER?></td>
								    <td width="12%" align="left" class="reportBoxHeading"><?=TABLE_HEADING_NEW_LIST_DATE?></td>
<?		if ($restock_account_info_permission) {
			echo '				    <td width="20%" align="center" class="reportBoxHeading">'.TABLE_HEADING_RESTOCK_CHARACTERS;
			echo ' 						<br>[<a href="'.tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=manage_rstk').'" class="highlightLink">'.LINK_MANAGE.'</a>]</td>';
		}

		if ($purchase_list_permission) {
			echo '					<td width="5%" align="center" class="reportBoxHeading">'.TABLE_HEADING_ACTION.'</td>';
		}
?>
								</tr>
<?
		$row_count = 0;
		while ($purchase_list_row = tep_db_fetch_array($purchase_list_result_sql)) {
	   		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;

	    	$list_id = $purchase_list_row["products_purchases_lists_id"];
	    	$safe_list_name = htmlspecialchars(addslashes($purchase_list_row["products_purchases_lists_name"]), ENT_QUOTES);

	    	$list_character_set_select_sql = "SELECT rcs.restock_character_sets_id, rcs.restock_character_sets_name
	    									  FROM " . TABLE_RESTOCK_CHARACTER_SETS . " AS rcs
	    									  INNER JOIN ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ." AS rcs2pl
	    									  		ON (rcs.restock_character_sets_id=rcs2pl.restock_character_sets_id)
	    									  WHERE rcs2pl.products_purchases_lists_id = '" . tep_db_input($list_id) . "'
	    									  ORDER BY rcs.restock_character_sets_name";
	    	$list_character_set_result_sql = tep_db_query($list_character_set_select_sql);
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top"><?=$purchase_list_row["products_purchases_lists_name"]?></td>
									<td class="reportRecords" valign="top"><?=($purchase_list_row["products_purchases_lists_cat_id"] > 0) ? $purchase_list_row["categories_name"] : TEXT_TOP?></td>
									<td align="center" class="reportRecords" valign="top"><?=$purchase_list_row["products_purchases_lists_qty_round_up"]?></td>
									<td align="center" class="reportRecords" valign="top"><?=$purchase_list_row["products_purchases_lists_sort_order"]?></td>
									<td class="reportRecords" valign="top"><?=$purchase_list_row["products_purchases_lists_reference_date"]?></td>
<?			if ($restock_account_info_permission) { ?>
									<td class="reportRecords" valign="top">
										<table border="0" width="100%" align="center" cellspacing="0" cellpadding="2">
											<tr>
												<td class="reportRecords" valign="top"><a href="<?=tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=edit_rstk&lID='.$list_id)?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Add restock characters", "", "", 'align="top"')?></a>&nbsp;</td>
												<td width="100%" class="reportRecords" valign="top">
<?				while ($list_character_set_row = tep_db_fetch_array($list_character_set_result_sql)) {
					$safe_set_name = htmlspecialchars(addslashes($list_character_set_row["restock_character_sets_name"]), ENT_QUOTES);

					echo '<a href="'.tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=edit_acc_set&rcsID='.$list_character_set_row["restock_character_sets_id"]).'" class="actionLink">'. $list_character_set_row["restock_character_sets_name"] .'</a>&nbsp;[<a href="javascript:void(confirm_delete(\''.$safe_set_name.'\', \''.LINK_UNASSIGNED_RESTOCK_CHARACTERS.'\', \''.tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'subaction=confirm_delete_rstk_set&rcsID='.$list_character_set_row["restock_character_sets_id"].'&rstk='.$list_character_set_row["restock_character_sets_name"].'&purchase='.$purchase_list_row["products_purchases_lists_name"].'&lID='.$list_id).'\'))">unassigned</a>]<br>';
				}
?>
												</td>
											</tr>
										</table>
									</td>
<?			}

			if ($purchase_list_permission) {
?>
									<td align="left" class="reportRecords" valign="top" nowrap>&nbsp;
										<a href="<?=tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=edit_list&lID='.$list_id)?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit purchase list", "", "", 'align="top"')?></a>
										<a href="javascript:void(confirm_delete('<?=$safe_list_name?>', 'Purchase List', '<?=tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=delete_list&subaction=confirm_delete_list&lID='.$list_id)?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete purchase list", "", "", 'align="top"')?></a>
									</td>
<?			} ?>
								</tr>
<?
			$row_count++;
		}
?>
							</table>
						</td>
					</tr>
<?	}

	if ($purchase_list_permission) {
?>
					<tr>
						<td>
<?
							echo '[ <a href="'.tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=add_list').'" class="actionLink">'.LINK_ADD_PURCHASE_LIST.'</a> ]';							
?>
						</td>
					</tr>
<?
	}
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>