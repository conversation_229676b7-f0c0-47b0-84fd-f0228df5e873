<?php
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'products_supplier.php');

$action = $_REQUEST['action'];
$supplier_id = (int)$_REQUEST['sID'];
$supplier_status = (int)$_GET['flag'];

$products_supplier_sql_data = array();

$folder_name_arr = array(
						array('id' => '', 'text' => 'Please Select'),
						array('id' => 'wow', 'text' => 'WOW'),
						array('id' => 'aion', 'text' => 'AION'),
						array('id' => 'aoc', 'text' => 'AOC'),
						array('id' => 'warhammer', 'text' => 'Warhammer'),
                        array('id' => 'rift', 'text' => 'RIFT')
					  );

if (tep_not_null($action)) {
	switch ($action) {
		case 'set_products_supplier_status':
			$products_supplier_sql_data = array ( 'supplier_status' => $supplier_status );
			tep_db_perform(TABLE_PRODUCTS_SUPPLIER, $products_supplier_sql_data, 'update', 'supplier_id = "'. $supplier_id . '"');
			
			tep_redirect(tep_href_link(FILENAME_PRODUCTS_SUPPLIER, 'page=' . (int)$_GET['page']));
			break;
			
		case 'delete_products_supplier':
			if (tep_not_null($supplier_id)) {
				$psInfo = new products_supplier($supplier_id);
				$psInfo->remove_products_supplier();
				
				tep_redirect(tep_href_link(FILENAME_PRODUCTS_SUPPLIER));
			}
			break;
			
		case 'update_products_supplier':
			$error = false;
			$products_rss = array();
			$products_rss_links = array();
			
			$supplier_id = (int)$_POST['supplier_id'];
			$hidden_supplier_id = tep_not_null($_POST['hidden_supplier_id']) ? (int)$_POST['hidden_supplier_id'] : '';
			
			if(is_array($_POST['product_id'])) {
				foreach ($_POST['product_id'] as $key => $val) {
					$product_id = $val;
					$folder_name = $_POST['folder_name'][$key];
						
					$products_id_select_sql = "SELECT products_id FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_db_input($product_id) . "'";
					$products_id_result_sql = tep_db_query($products_id_select_sql);
					if (tep_db_num_rows($products_id_result_sql) > 0) {
						foreach ($_POST['url'][$key] as $url_key => $url_value) {
							$products_rss['products_id'][] = $product_id;
							$products_rss['url'][] = $url_value;
							$products_rss['folder_name'][] = $folder_name;
						}
					}
				}
			}
			
			if (tep_not_null($supplier_id)) {
				$verify_supplier_select_sql = "SELECT customers_id FROM " . TABLE_CUSTOMERS . " WHERE customers_id = '" . (int)$supplier_id . "'";
				$verify_supplier_result_sql = tep_db_query($verify_supplier_select_sql);
				if (tep_db_num_rows($verify_supplier_result_sql) == 0) {
					$messageStack->add_session(sprintf(ERROR_CUSTOMER_ID_NOT_EXIST, $supplier_id), 'error');
				} else {
					if (tep_not_null($hidden_supplier_id)) {
						if ($supplier_id != $hidden_supplier_id) {
							$new_psInfo = new products_supplier($supplier_id);
							if (tep_not_null($new_psInfo->products_supplier)) {
								$error = true;
								$messageStack->add_session(sprintf(ERROR_DUPLICATE_SUPPLIER_ID, $supplier_id), 'error');
							}
						}
						
						if ($error == false) {
							$psInfo = new products_supplier($hidden_supplier_id);
						}
					} else {
						if (isset($_POST['hidden_supplier_id'])) {
							$hidden_supplier_id = $supplier_id;
						}
						
						$psInfo = new products_supplier($supplier_id);
						
						if (tep_not_null($psInfo->products_supplier)) {
							$error = true;
							$messageStack->add_session(sprintf(ERROR_DUPLICATE_SUPPLIER_ID, $supplier_id), 'error');
						}
					}
					
					if ($error == false) {
						$products_supplier_sql_data = array (	'supplier_id' => $supplier_id, 
																'supplier_code' => tep_db_prepare_input($_POST['supplier_code']), 
																'payout_percentage' => (int)$_POST['payout_percentage'], 
																'payout_grace_period_offset' => $_POST['payout_sign'] . (int)$_POST['payout_grace_period_offset'], 
																'reserve_account_api' => (int)$_POST['reserve_account_api'], 
																'retrieve_account_info_api' => (int)$_POST['retrieve_account_info_api'], 
																'api_key' => tep_db_prepare_input($_POST['api_key']), 
																'supplier_status' => (int)$_POST['supplier_status'] );
						
						if (tep_not_null($psInfo->products_supplier) && tep_not_null($hidden_supplier_id)) {
							tep_db_perform(TABLE_PRODUCTS_SUPPLIER, $products_supplier_sql_data, 'update', 'supplier_id = "' . $hidden_supplier_id . '"');
							
							if ($supplier_id != $hidden_supplier_id) {
								$products_to_supplier_sql_data = array ( 'supplier_id' => $supplier_id );
								tep_db_perform(TABLE_PRODUCTS_TO_SUPPLIER, $products_to_supplier_sql_data, 'update', 'supplier_id = "' . $hidden_supplier_id . '"');
							}
							
							$psInfo->get_products_rss_link();
							$products_rss_links = $psInfo->construct_products_array($products_rss);
							
							if (count($psInfo->products_rss_link) > 0) {
								// remove products_id and/or rss_link from database
								foreach ($psInfo->products_rss_link as $products_id => $rss_link) {
									$products_to_supplier_id = '';
									
									if (tep_not_null($products_id)) {
										$products_to_supplier_id = $psInfo->get_products_to_supplier_id((int)$products_id);
										
										if (isset($products_rss_links[$products_id])) {
											foreach ($rss_link as $cnt => $info) {
												if (!in_array($url, $products_rss_links[$products_id])) {
													if (tep_not_null($products_to_supplier_id)) {
														$psInfo->remove_rss_link($products_to_supplier_id, $info['url']);
													}
												}
											}
										} else {
											$psInfo->remove_products_rss_link($products_id);
										}
									}
								}
								
								// add new products_id and/or rss_link
								$psInfo->update_products_info($products_rss_links);
							} else if (tep_not_null($products_rss)) {
								$products_rss_links = $psInfo->construct_products_array($products_rss);
								$psInfo->update_products_info($products_rss_links);
							}
						} else { // new products supplier
							tep_db_perform(TABLE_PRODUCTS_SUPPLIER, $products_supplier_sql_data);
							
							$products_rss_links = $psInfo->construct_products_array($products_rss);
							$psInfo->update_products_info($products_rss_links);
						}
					}
				}
			}
				
			tep_redirect(tep_href_link(FILENAME_PRODUCTS_SUPPLIER));
			break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
		<title><?=TITLE?></title>
		<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
		
		<script language="javascript" src="includes/javascript/jquery.js"></script>
		<script language="javascript" src="includes/general.js"></script>
		<script type="text/javascript"><!--
			function addTableRow(jTable){
			    var tds = '';
		        var r = jQuery('#product_supplier_row').val();
		        
		        // For making unique ID
		        var update_r = eval(r + '+' + 1);
		        jQuery('#product_supplier_row').val(update_r);
		        
	       		tds = '<tr id="product_row_'+r+'">';
				tds += '	<td>';
	        	tds += '	<table border="0" width="100%" cellspacing="0" cellpadding="5">';
	        	tds += '	<tr>';
	        	tds += '		<td>';
	        	tds += '		<table border="0" width="100%" cellspacing="0" cellpadding="1">';
	        	tds += '		<tr>';
	        	tds += '			<td class="main" width="135">';
	        	tds += '				<?=TABLE_HEADING_PRODUCT_ID;?> : <?=tep_draw_input_field("product_id['+r+']", "", " id=\"product_id_'+r+'\" size=\"20\"");  ?>';
	        	tds += '				<br />';
	        	tds += '				<a href="javascript:openDGDialog(\'<?=tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, "fname=" . FILENAME_PRODUCTS_SUPPLIER . "&fieldname=product_id_'+r+'");?>\', 600, 250);">(Product List)</a>';
	        	tds += '			</td>';
	        	tds += '			<td class="main" width="135">';
	        	tds += '				<?=TABLE_HEADING_FOLDER_NAME;?> : <?=tep_draw_pull_down_menu("folder_name['+r+']", $folder_name_arr, ""); ?>';
	        	tds += '			</td>';
	        	tds += '			<td width="5" align="right"><a href="javascript:removeRow(\'#product_row_'+r+'\');"><?=tep_image(DIR_WS_ICONS."off.gif", "Delete Product Supplier", "16", "16")?></a></td>';
	        	tds += '		</tr>';
	        	tds += '		</table>';
	        	tds += '		</td>';
	        	tds += '	</tr>';
	        	tds += '	<tr><td align="center">RSS Link</td></tr>';
	        	tds += '	<tr>';
	        	tds += '		<td>';
	        	tds += '		<input type="hidden" name="rss_row_num_'+r+'" id="rss_row_num_'+r+'" value="1" />';
	        	tds += '		<table id="rss_row_'+r+'" border="0" width="100%" cellspacing="0" cellpadding="5">';
	        	tds += '		<tr id="url_'+r+'_0">';
	        	tds += '			<td class="main" align="center">';
	        	tds += '			<?=tep_draw_input_field("url['+r+'][0]", "", " size=\"70\""); ?>';
	        	tds += '			<input type="button" value="Delete" onclick="removeRow(\'#url_'+r+'_0\')">';
	        	tds += '			</td>';
	        	tds += '		</tr>';
	        	tds += '		</table>';
	        	tds += '		</td>';
	        	tds += '	</tr>';
	        	tds += '	<tr>';
	        	tds += '		<td align="center">';
	        	tds += '		<input type="button" value="Add New Rss" onclick="addRssRow(\'#rss_row_'+r+'\', \''+r+'\', \'0\')">';
	        	tds += '		</td>';
	        	tds += '	</tr>';
	        	tds += '	<tr><td class="dottedLine">&nbsp;</td></tr>';
	        	tds += '	</table>';
	        	tds += '	</td>';
	        	tds += '</tr>';
	        	
		       jQuery(jTable).append(tds);
			}
			
			function addRssRow(jTable, cnt){
				var tds = '';
		        var r = jQuery('#rss_row_num_'+cnt).val();
		        
		        // For making unique ID
		        var update_r = eval(r + '+' + 1);
		        jQuery('#rss_row_num_'+cnt).val(update_r);
				
			    tds += '<tr id="url_'+cnt+'_'+r+'">';
				tds += '<td class="main" align="center">';
				tds += '	<input type="text" name="url['+cnt+']['+r+']" size="70">';
				tds += '	<input type="button" value="Delete" onclick="removeRow(\'#url_'+cnt+'_'+r+'\')">';
				tds += '</td>';
	        	tds += '</tr>';
			    
			     jQuery(jTable).append(tds);
			}
			
			function removeRow(jTable){
				jQuery(jTable).remove();
			}
			
			function form_submit() {
				if (document.getElementById('supplier_id').value == "") {
					alert("Supplier ID is required!");
					document.getElementById('supplier_id').focus();
					return false;
				}
				
				if (document.getElementById('supplier_code').value == "") {
					alert("Supplier Code is required!");
					document.getElementById('supplier_code').focus();
					return false;
				}
			}
			
			function getReturnedValue(received_val, fieldname) {
				document.getElementById(fieldname).value = received_val;
			}
		--></script>
		<script language="javascript" src="includes/javascript/modal_win.js"></script>
	</head>
	
	<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" >
	<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
	<!-- header_eof //-->
	
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
			<td width="<?php echo BOX_WIDTH; ?>" valign="top">
				<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
					<!-- left_navigation_eof //-->
				</table>
			</td>
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_TITLE;?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?php
if (tep_not_null($action)) {
	$supplier_status_active = false; 
	$supplier_status_inactive = true;
	$payout_sign_minus = '';
	$payout_sign_plus = ' selected';
	
	$products_num_rows = 0;
	
	
	if ($action == 'edit_products_supplier') {
		$psInfo = new products_supplier($supplier_id);
		$psInfo->get_products_rss_link();
		
		if ($psInfo->products_supplier['supplier_status'] == true) {
			$supplier_status_active = true;
			$supplier_status_inactive = false;
		}
		
		if ($psInfo->products_supplier['payout_grace_period_offset'] < 0) {
			$payout_sign_minus = ' selected';
			$payout_sign_plus = '';
		}
	}
?>
					<tr>
						<td>
							<fieldset class="selectedFieldSet">
								<?php echo tep_draw_form('products_supplier', FILENAME_PRODUCTS_SUPPLIER, 'action=update_products_supplier', 'post'); ?>
								<table border="0" width="100%" cellspacing="0" cellpadding="2">
									<tr>
										<td class="customerFormAreaTitle"><?=TEXT_HEADING_SUPPLIER;?></td>
									</tr>
									<tr>
					    				<td class="formArea">
											<table border="0" width="100%" cellspacing="0" cellpadding="2">
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_PRODUCTS_SUPPLIER_STATUS;?></td>
													<td class="main">
														<?php echo tep_draw_radio_field('supplier_status', '1', $supplier_status_active, '', 'id="supplier_status_active"') . '&nbsp;' . TEXT_ACTIVE . '&nbsp;' . tep_draw_radio_field('supplier_status', '0', $supplier_status_inactive, '', 'id="supplier_status_inactive"') . '&nbsp;' . TEXT_INACTIVE; ?>
													</td>
												</tr>
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_PRODUCTS_SUPPLIER_ID;?></td>
													<td class="main">
														<?php 
															echo tep_draw_input_field('supplier_id', $psInfo->products_supplier['supplier_id'], 'id="supplier_id" size="20"', true); 
															
															if (tep_not_null($supplier_id)) {
																echo tep_draw_hidden_field('hidden_supplier_id', $supplier_id);
															}
														?>
													</td>
												</tr>
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_PRODUCTS_SUPPLIER_CODE;?></td>
													<td class="main">
														<?php echo tep_draw_input_field('supplier_code', $psInfo->products_supplier['supplier_code'], 'id="supplier_code" size="20"', true); ?>
													</td>
												</tr>
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_PAYOUT_PERCENTAGE;?></td>
													<td class="main">
														<?php echo tep_draw_input_field('payout_percentage', $psInfo->products_supplier['payout_percentage'], 'id="payout_percentage" size="12" maxlength="3"') . '&nbsp;%'; ?>
													</td>
												</tr>
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_PAYOUT_GRACE_PERIOD_OFFSET;?></td>
													<td class="main">
														<select name="payout_sign">
															<option name="minus" value="-" <?=$payout_sign_minus;?>>-</option>
															<option name="plus" value="+" <?=$payout_sign_plus;?>>+</option>
														</select>
														<?php echo tep_draw_input_field('payout_grace_period_offset', ltrim($psInfo->products_supplier['payout_grace_period_offset'], '-'), 'id="payout_grace_period_offset" size="12" maxlength="9"') . '&nbsp;' . TEXT_MINUTE; ?>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
									<tr>
										<td class="customerFormAreaTitle"><?=TEXT_HEADING_PRODUCT;?></td>
									</tr>
									<tr><td class="formArea"><input type="button" value="Add New Row" onclick="addTableRow('#products_supplier')"></td></tr>
									<tr>
					    				<td class="formArea">
<?
$get_products_id_select_sql = " SELECT products_to_supplier_id, products_id, folder_name
								FROM " . TABLE_PRODUCTS_TO_SUPPLIER . " 
								WHERE supplier_id = '".tep_db_input($supplier_id)."' 
								ORDER BY products_to_supplier_id";
$get_products_id_result_sql = tep_db_query($get_products_id_select_sql);
$get_products_id_num = tep_db_num_rows($get_products_id_result_sql);
?>
<input type="hidden" name="product_supplier_row" id="product_supplier_row" value="<?=$get_products_id_num?>" />
<table id="products_supplier" border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ($get_products_id_num > 0) {
	
	$get_products_id_cnt = 0;
	while ($get_products_id_row = tep_db_fetch_array($get_products_id_result_sql)) {
		$products_to_supplier_id = $get_products_id_row['products_to_supplier_id'];
		$products_id = $get_products_id_row['products_id'];
		$folder_name = $get_products_id_row['folder_name'];
?>												
	<tr id="product_row_<?=$get_products_id_cnt?>">
		<td>
		<table id="products_supplier_<?=$get_products_id_cnt?>" border="0" width="100%" cellspacing="0" cellpadding="5">
			<tr>
				<td>
				<table border="0" width="100%" cellspacing="0" cellpadding="1">
					<tr>	
						<td class="main" width="135">
							<?=TABLE_HEADING_PRODUCT_ID;?> : <?=tep_draw_input_field("product_id[".$get_products_id_cnt."]", $products_id, ' id="product_id_'.$get_products_id_cnt.'" size="20"');  ?>
							<br />
							<a href="javascript:openDGDialog('<?=tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, "fname=" . FILENAME_PRODUCTS_SUPPLIER . "&fieldname=product_id_".$get_products_id_cnt);?>', 600, 250, '');">(Product List)</a>
						</td>
						<td class="main" width="135">
							<?=TABLE_HEADING_FOLDER_NAME;?> : <?=tep_draw_pull_down_menu("folder_name[".$get_products_id_cnt."]", $folder_name_arr, $folder_name); ?>
						</td>
						<td width="5" align="right"><a href="javascript:removeRow('#product_row_<?=$get_products_id_cnt?>');"><?=tep_image(DIR_WS_ICONS."off.gif", "Delete Product Supplier", "16", "16")?></a></td>
					</tr>
				</table>
				</td>
			</tr>
			<tr><td align="center">RSS Link</td></tr>
			<tr>
				<td>
			<?
				$get_rss_link_select_sql = "SELECT products_rss_link_id, url
											FROM " . TABLE_PRODUCTS_RSS_LINK . " 
											WHERE products_to_supplier_id = '".tep_db_input($products_to_supplier_id)."' 
											ORDER BY products_rss_link_id";
				$get_rss_link_result_sql = tep_db_query($get_rss_link_select_sql);
				$get_rss_link_num = tep_db_num_rows($get_rss_link_result_sql);
			?>
				<input type="hidden" name="rss_row_num_<?=$get_products_id_cnt;?>" id="rss_row_num_<?=$get_products_id_cnt;?>" value="<?=$get_rss_link_num?>" />
			<?
				if ($get_rss_link_num > 0) {
			?>
				<table id="rss_row_<?=$get_products_id_cnt;?>" border="0" width="100%" cellspacing="0" cellpadding="5">
			<?	
					$get_rss_link_cnt = 0;
					while ($get_rss_link_row = tep_db_fetch_array($get_rss_link_result_sql)) {
					
					$rss_products_rss_link_id = $get_rss_link_row['products_rss_link_id'];
					$rss_url = $get_rss_link_row['url'];
			?>
					<tr id="url_<?=$get_products_id_cnt?>_<?=$get_rss_link_cnt?>">
						<td class="main" align="center">
							<?=tep_draw_input_field("url[".$get_products_id_cnt."][".$get_rss_link_cnt."]", $rss_url, 'size="70"'); ?>
							<input type="button" value="Delete" onclick="removeRow('#url_<?=$get_products_id_cnt?>_<?=$get_rss_link_cnt?>')">
						</td>
					</tr>
			<?
					$get_rss_link_cnt++;
					}
			?>
				</table>
			<? } ?>
				</td>
			</tr>
			<tr>
				<td align="center">
					<input type="button" value="Add New Rss" onclick="addRssRow('#rss_row_<?=$get_products_id_cnt;?>', '<?=$get_products_id_cnt;?>')">
				</td>
			</tr>
			<tr>
				<td class="dottedLine">&nbsp;</td>
			</tr>
		</table>
		</td>
	</tr>
	
<?
	$get_products_id_cnt++;
	}
}
?>
</table>
										</td>
									</tr>
									<tr>
										<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
									<tr>
										<td class="customerFormAreaTitle"><?=TEXT_HEADING_API;?></td>
									</tr>
									<tr>
					    				<td class="formArea">
											<table border="0" width="100%" cellspacing="0" cellpadding="2">
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_RESERVE_ACCOUNT_API;?></td>
													<td class="main">
														<?php echo tep_draw_checkbox_field('reserve_account_api', '1', $psInfo->products_supplier['reserve_account_api'], 'id="reserve_account_api"'); ?>
													</td>
												</tr>
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_RETRIEVE_ACCOUNT_INFO_API;?></td>
													<td class="main">
														<?php echo tep_draw_checkbox_field('retrieve_account_info_api', '1', $psInfo->products_supplier['retrieve_account_info_api'], 'id="retrieve_account_info_api"'); ?>
													</td>
												</tr>
												<tr>
													<td class="main" style="height:15px" width="20%"><?=ENTRY_API_KEY;?></td>
													<td class="main">
														<?php echo tep_draw_input_field('api_key', $psInfo->products_supplier['api_key'], 'id="api_key" size="72"'); ?>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
									<tr>
										<td align="right" class="main">
										<?php
											echo tep_image_submit('button_update.gif', IMAGE_UPDATE, 'onClick="return form_submit();"');
											echo '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PRODUCTS_SUPPLIER) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>';
										?>
										</td>
									</tr>
								</table>
								</form>
							</fieldset>
						</td>
					</tr>
<?php
} else {
	$row_count = 0;
	$products_supplier_status_array = array('1'=> array('name' => 'icon_status_green', 'alt' => IMAGE_ICON_STATUS_GREEN, 'alt2' => IMAGE_ICON_STATUS_GREEN_LIGHT), 
											'0'=> array('name' => 'icon_status_red', 'alt' => IMAGE_ICON_STATUS_RED, 'alt2' => IMAGE_ICON_STATUS_RED_LIGHT)
										);
	
	$products_supplier_select_sql = "select supplier_id from " . TABLE_PRODUCTS_SUPPLIER;
	$page_split_object = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $products_supplier_select_sql, $products_supplier_numrows);
?>
					<tr>
            			<td valign="top">
            				<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
								    <td width="12%" class="reportBoxHeading"><?=TABLE_HEADING_PRODUCTS_SUPPLIER_ID;?></td>
								    <td width="12%" class="reportBoxHeading"><?=TABLE_HEADING_PRODUCTS_SUPPLIER_CODE;?></td>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_PAYOUT_GRACE_PERIOD_OFFSET;?></td>
								    <td align="center" class="reportBoxHeading"><?=TABLE_HEADING_RESERVE_ACCOUNT_API;?></td>
								    <td align="center" class="reportBoxHeading"><?=TABLE_HEADING_RETRIEVE_ACCOUNT_INFO_API;?></td>
								    <td align="center" class="reportBoxHeading"><?=TABLE_HEADING_PRODUCTS_SUPPLIER_STATUS;?></td>
								    <td align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION;?></td>
								</tr>
								<?php
									$products_supplier_result_sql = tep_db_query($products_supplier_select_sql);
									if (tep_db_num_rows($products_supplier_result_sql) > 0) {
										while ($products_supplier_row = tep_db_fetch_array($products_supplier_result_sql)) {
											$supplier_id = $products_supplier_row['supplier_id'];
											$supplier_select_sql = "SELECT supplier_id, supplier_code, payout_grace_period_offset, 
																		reserve_account_api, retrieve_account_info_api, supplier_status 
																	FROM " . TABLE_PRODUCTS_SUPPLIER . " 
																	WHERE supplier_id = '" . $supplier_id . "'";
											$supplier_result_sql = tep_db_query($supplier_select_sql);
											if ($supplier_rows = tep_db_fetch_array($supplier_result_sql)) {
												$row_style = ($row_count % 2) ? 'ordersListingEven' : 'ordersListingOdd';
								?>
												<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
													<td class="ordersRecords"><?=$supplier_rows['supplier_id'];?></td>
													<td class="ordersRecords"><?=$supplier_rows['supplier_code'];?></td>
													<td class="ordersRecords"><?=$supplier_rows['payout_grace_period_offset'];?></td>
													<td align="center" class="ordersRecords">
													<?php
														if ($supplier_rows['reserve_account_api'] == '1') {
															echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
														} else if ($supplier_rows['reserve_account_api'] == '0') {
															echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
														}
													?>
													</td>
													<td align="center" class="ordersRecords">
													<?php
														if ($supplier_rows['retrieve_account_info_api'] == '1') {
															echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
														} else if ($supplier_rows['retrieve_account_info_api'] == '0') {
															echo tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
														}
													?>
													</td>
													<td align="center" class="ordersRecords">&nbsp;&nbsp;
													<?php
														foreach ($products_supplier_status_array as $status_id => $img_res) {
															if ((int)$supplier_rows['supplier_status'] == (int)$status_id) {
																echo tep_image(DIR_WS_IMAGES . $img_res['name'] . '.gif', $img_res['alt'], 10, 10) . '&nbsp;&nbsp;';
															} else {
																echo '<a href="' . tep_href_link(FILENAME_PRODUCTS_SUPPLIER, 'action=set_products_supplier_status&flag=' . (int)$status_id . '&sID=' . $supplier_id . '&page='.$_GET["page"]) . '">' . tep_image(DIR_WS_IMAGES . $img_res['name'] . '_light.gif', $img_res['alt2'], 10, 10) . '</a>&nbsp;&nbsp;';
															}
														}
													?>
													</td>
													<td align="center" class="ordersRecords" valign="top" nowrap>
														<a href="<?=tep_href_link(FILENAME_PRODUCTS_SUPPLIER, 'action=edit_products_supplier&sID=' . $supplier_id); ?>"><?=tep_image(DIR_WS_ICONS . 'edit.gif', 'Edit Products Supplier', '', '', 'align="top"');?></a>
														<a href="javascript:void(confirm_delete('<?=$supplier_id;?>', 'Products Supplier', '<?=tep_href_link(FILENAME_PRODUCTS_SUPPLIER, 'action=delete_products_supplier&sID=' . $supplier_id);?>'))"><?=tep_image(DIR_WS_ICONS . 'delete.gif', 'Delete Products Supplier', '', '', 'align="top"')?></a>
													</td>
												</tr>
								<?php
												$row_count++;
											}
										}
									} else {
								?>
										<tr class="reportListingEven">
											<td class="reportRecords" align="center" colspan="7"><i>Empty</i></td>
										</tr>
								<?php
									}
								?>
							</table>
						</td>
					</tr>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td class="smallText" valign="top" nowrap><?=$page_split_object->display_count($products_supplier_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_SUPPLIERS)?></td>
									<td class="smallText" align="right"><?=$page_split_object->display_links($products_supplier_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_GET['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont')) . "cont=1")?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr align="right">
						<td>
							<a href="<?=tep_href_link(FILENAME_PRODUCTS_SUPPLIER, 'action=new_products_supplier'); ?>" style="text-decoration: none;"><?php echo tep_submit_button('New Products Supplier', 'New Products Supplier', '', 'inputButton'); ?></a>
						</td>
					</tr>
<?php
}
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
	
	<!-- footer //-->
	<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
	<!-- footer_eof //-->
	<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
	
	</body>
</html>