<?
require('includes/application_top.php');
require('includes/functions/progress_report_task.php');
require_once('includes/functions/custom_product.php');
require_once('includes/functions/luaparser.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'custom_product_payment.php');
require(DIR_WS_CLASSES . 'custom_product.php');

define('DISPLAY_PRICE_DECIMAL', 3);

$orders_product_id = $_REQUEST["orders_product_id"];

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$supplier_cp_payment_detail_permission = tep_admin_files_actions(FILENAME_PROGRESS_REPORT, 'SUPPLIER_CP_PAYMENT_DETAILS');
$supplier_cp_payable_manual_adjust = tep_admin_files_actions(FILENAME_PROGRESS_REPORT, 'SUPPLIER_CP_PAYABLE_MANUAL_ADJUST');
$adjust_completed_prod_payable_amt_permission = tep_admin_files_actions(FILENAME_PROGRESS_REPORT, 'SUPPLIER_CP_COMPLETED_PAYABLE_MANUAL_ADJUST');
$view_pwl_details_permission = tep_admin_files_actions(FILENAME_PROGRESS_REPORT, 'VIEW_PWL_DETAILS');
$view_sensitive_pwl_details_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_PWL_DETAILS');
$view_clear_text_password_permission = tep_admin_files_actions(FILENAME_ORDERS, 'ORDER_PWL_CLEAR_TEXT_PWD');
$verify_pwl_order_permission = tep_admin_files_actions(FILENAME_PROGRESS_REPORT, 'PWL_ORDER_VERIFY_STATUS');

$status_name = array();

$supplier_tasks_status_select_sql = "	SELECT supplier_tasks_status_id, supplier_tasks_status_name 
										FROM " . TABLE_SUPPLIER_TASKS_STATUS . " 
										WHERE language_id='" . (int)$languages_id . "' 
										ORDER BY supplier_tasks_status_sort_order";
$supplier_tasks_status_result_sql = tep_db_query($supplier_tasks_status_select_sql);
while ($supplier_tasks_status_row = tep_db_fetch_array($supplier_tasks_status_result_sql)) {
	$status_name[$supplier_tasks_status_row['supplier_tasks_status_id']] = $supplier_tasks_status_row['supplier_tasks_status_name'];
}

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

if (tep_not_null($action)) {
	if (isset($HTTP_POST_VARS["search"])) {
		$_SESSION['custom_product_param']["custom_product_order_id"] = (int)tep_db_prepare_input($HTTP_POST_VARS["custom_product_order_id"]);
		$_SESSION['custom_product_param']["order_status"] = $_REQUEST["order_status"];
		$_SESSION['custom_product_param']["custom_product_product_name"] = tep_db_prepare_input($HTTP_POST_VARS["custom_product_product_name"]);
		$_SESSION['custom_product_param']["custom_product_start_date"] = tep_db_prepare_input($HTTP_POST_VARS["custom_product_start_date"]);
		$_SESSION['custom_product_param']["custom_product_end_date"] = tep_db_prepare_input($HTTP_POST_VARS["custom_product_end_date"]);
		$_SESSION['custom_product_param']["follow_up_date"] = tep_db_prepare_input($HTTP_POST_VARS["follow_up_date"]);
		$_SESSION['custom_product_param']["custom_product_supplier"] = tep_db_prepare_input($HTTP_POST_VARS["custom_product_supplier"]);
		$_SESSION['custom_product_param']["custom_product_supplier_code"] = tep_db_prepare_input($HTTP_POST_VARS["custom_product_supplier_code"]);
		$_SESSION['custom_product_param']["custom_product_not_assign"] = tep_db_prepare_input($HTTP_POST_VARS["custom_product_not_assign"]);
		$_SESSION['custom_product_param']["customer_member_group"] = tep_db_prepare_input($HTTP_POST_VARS["customer_member_group"]);
		$_SESSION['custom_product_param']["cat_id"] = tep_db_prepare_input($HTTP_POST_VARS["cat_id"]);
		$_SESSION['custom_product_param']["include_subcategory"] = (int)$_REQUEST["include_subcategory"];
		
		if (count($status_name)) {
			foreach ($status_name as $id => $title) {
				$_SESSION['custom_product_param']["status_".$id] = $_REQUEST["status_".$id];
			}
		}
	} else if (isset($HTTP_POST_VARS["reset"])) {
		unset($_SESSION['custom_product_param']);
        tep_redirect(tep_href_link(FILENAME_PROGRESS_REPORT, 'action=advanced_search'));
        break;
	} else if (isset($HTTP_POST_VARS["btn_csv_export"])) {
		$export_csv_array = tep_array_unserialize($HTTP_POST_VARS["serialized_export_csv_array"]);
		$export_csv_data = '';
		
		if (count($export_csv_array)) {
			foreach ($export_csv_array as $pid => $res) {
				$tmp_cvs_data_array = array();
				for ($i=0; $i < count($res); $i++) {
					$tmp_cvs_data_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res[$i]) . '"';
				}
				$export_csv_data .= implode(',', $tmp_cvs_data_array) . "\n";
			}
		}
		
		if (tep_not_null($export_csv_data)) {
			$filename = 'supplier_pricing_'.date('YmdHis').'.csv';
			$mime_type = 'text/x-csv';
			// Download
			header('Content-Type: ' . $mime_type);
			header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			// IE need specific headers
			if (PMA_USR_BROWSER_AGENT == 'IE') {
				header('Content-Disposition: inline; filename="' . $filename . '"');
				header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
				header('Pragma: public');
			} else {
				header('Content-Disposition: attachment; filename="' . $filename . '"');
				header('Pragma: no-cache');
			}
				echo $export_csv_data;
				exit();
			}
		tep_redirect(tep_href_link(FILENAME_PROGRESS_REPORT, tep_get_all_get_params()));
		break;
	} else if (isset($_POST["batch_action"])) {
		switch($_POST["batch_action"]) {
			case "MakePayment":
				break; // Depreciated
				$custom_product_payment = new custom_product_payment();
				$custom_product_payment->set_remarks(tep_db_prepare_input($_POST['payment_remark']));
				
				$custom_product_payment->set_show_supplier_remark($_POST['show_supplier_remark']=='1' ? true : false);
				
				$payment_result = $custom_product_payment->make_payment($_POST['orders_batch'], $_POST['partial_pay']);
				
				if (is_array($payment_result) && count($payment_result)) {
					$messageStack->add_session($payment_result['text'], $payment_result['type']);
				}
				
				tep_redirect(tep_href_link(FILENAME_PROGRESS_REPORT, tep_get_all_get_params(array(''))));
				break;
		}
	} else if (isset($HTTP_POST_VARS["post_comment"])) {
		foreach($HTTP_POST_VARS["post_comment"] as $key => $value) {
			$level_update = false;
			$comment = '';
			$email_contents = '';
			$cat_cfg_array = tep_get_cfg_setting($key, 'product');
			
			$task_info_select_sql = "	SELECT supplier_tasks_status, supplier_tasks_billing_status, supplier_payable_adjust, replace(rtrim(replace(replace(rtrim(replace(supplier_tasks_allocation_ratio_adjust,'0',' ')),' ','0'),'.',' ')),' ','.') AS previous_ratio 
										FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " 
										WHERE orders_products_id ='" . (int)$key . "'";
			$task_info_result_sql = tep_db_query($task_info_select_sql);
			$task_info_row = tep_db_fetch_array($task_info_result_sql);
			
			// Check if this user has the permission for this update
			if ($HTTP_POST_VARS["task_status"][$key] != $task_info_row['supplier_tasks_status'] && !tep_check_status_update_permission('PWL', $login_groups_id, $task_info_row['supplier_tasks_status'], $HTTP_POST_VARS["task_status"][$key])) {
	    		$messageStack->add_session(ERROR_TRANS_UPDATE_DENIED, 'error');
				
				tep_redirect(tep_href_link(FILENAME_PROGRESS_REPORT, tep_get_all_get_params()));
				break;
	    	}
	    	
			if (tep_not_null($HTTP_POST_VARS["task_status"][$key]) && $HTTP_POST_VARS["task_status"][$key] != $task_info_row['supplier_tasks_status']) {
				if ($task_info_row['supplier_tasks_status'] == 4 && $HTTP_POST_VARS["task_status"][$key] == 2) {
					if ($task_info_row['supplier_tasks_billing_status'] == 0) {
						$cron_pending_credit_trans_select_sql = "	SELECT cron_pending_credit_trans_completed_date, cron_pending_credit_mature_period 
																	FROM " . TABLE_CRON_PENDING_CREDIT. " 
																	WHERE cron_pending_credit_trans_type = 'PWL' 
																		AND cron_pending_credit_trans_id ='" . (int)$key . "' 
																		AND NOW() < DATE_SUB(DATE_ADD(cron_pending_credit_trans_completed_date, INTERVAL CAST(cron_pending_credit_mature_period AS UNSIGNED) MINUTE), INTERVAL 1 HOUR) ";
						$cron_pending_credit_trans_result_sql = tep_db_query($cron_pending_credit_trans_select_sql);
						
						if (tep_db_num_rows($cron_pending_credit_trans_result_sql) > 0) {
							if (tep_check_status_update_permission('PWL', $login_groups_id, $task_info_row['supplier_tasks_status'], $HTTP_POST_VARS["task_status"][$key])) {
								$level_update = true;
								
								tep_remove_cron_pending_credit('PWL', $key);
								
								$update_current_status_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_status ='" . tep_db_input($HTTP_POST_VARS["task_status"][$key]) . "' WHERE orders_products_id ='" . (int)$key . "'";
								tep_db_query($update_current_status_sql);
							}
						}
					}
				} else {
					$level_update = true;
					$update_current_status_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_status ='" . tep_db_input($HTTP_POST_VARS["task_status"][$key]) . "' WHERE orders_products_id ='" . (int)$key . "'";
					tep_db_query($update_current_status_sql);
				}
				
				if ($HTTP_POST_VARS["task_status"][$key] == 2 || $HTTP_POST_VARS["task_status"][$key] == 3) {
					$time_diff_array = tep_get_time_diff($key);
					$time_diff_min = $time_diff_array['minute_format'];
					$tasks_time_taken = $HTTP_POST_VARS["tasks_time_taken"][$key] + $time_diff_min;
					
					$update_supplier_tasks_time_taken_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_time_taken ='" . tep_db_input($tasks_time_taken) . "' WHERE orders_products_id ='" . (int)$key . "'";
					tep_db_query($update_supplier_tasks_time_taken_sql);
				} else if ($HTTP_POST_VARS["task_status"][$key] == 1) {
					$update_supplier_tasks_time_reference_sql = "UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION . " SET supplier_tasks_time_reference = now() WHERE orders_products_id ='" . (int)$key . "'";
					tep_db_query($update_supplier_tasks_time_reference_sql);
				}
				
				if (tep_not_null($HTTP_POST_VARS["new_status_tag"])) {
					$new_status_tag = intval(str_replace('otag_', '', $HTTP_POST_VARS["new_status_tag"]));
					$new_status_tag_checking_select_sql = "SELECT orders_tag_id FROM " . TABLE_ORDERS_TAG . " WHERE orders_tag_id ='".$new_status_tag."' AND FIND_IN_SET('".(int)$HTTP_POST_VARS["task_status"][$key]."', orders_tag_status_ids) AND filename='".FILENAME_PROGRESS_REPORT."'";
					$new_status_tag_checking_result_sql = tep_db_query($new_status_tag_checking_select_sql);
					
					if (!tep_db_num_rows($new_status_tag_checking_result_sql)) {
						$new_status_tag = '';
					}
				} else { $new_status_tag = ''; }
				
				tep_update_record_tags(FILENAME_PROGRESS_REPORT, (int)$key, (int)$HTTP_POST_VARS["task_status"][$key], $new_status_tag);
			}
			
			if (isset($HTTP_POST_VARS["adjust_ratio"])) {
				if ($task_info_row['supplier_tasks_billing_status'] == 0) {
					if ($HTTP_POST_VARS["adjust_ratio"][$key] != $task_info_row["previous_ratio"]) {
						if (tep_not_null($HTTP_POST_VARS["adjust_ratio"][$key])) {
							$email_contents .= ENTRY_RATIO_ADJUST . " " . (tep_not_null($task_info_row['previous_ratio']) ? $task_info_row['previous_ratio'] . "%" : "No Overwrite") . ' -> ' . $HTTP_POST_VARS["adjust_ratio"][$key] . "%";
							$comment .= ENTRY_RATIO_ADJUST . " " . (tep_not_null($task_info_row['previous_ratio']) ? $task_info_row['previous_ratio'] . "%" : "No Overwrite") . ' -> ' . $HTTP_POST_VARS["adjust_ratio"][$key] . "%";
							
							$sql_data_array = array('supplier_tasks_allocation_ratio_adjust' => $HTTP_POST_VARS["adjust_ratio"][$key]);
						} else {
							$email_contents .= ENTRY_RATIO_ADJUST . " " . $task_info_row['previous_ratio'] . "% -> No overwrite";
							$comment .= ENTRY_RATIO_ADJUST . " " . $task_info_row['previous_ratio'] . "% -> No overwrite";
							
							$sql_data_array = array('supplier_tasks_allocation_ratio_adjust' => "NULL");
						}
						tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION, $sql_data_array, 'update', "orders_products_id = '" . (int)$key . "'");
					}
				} else {
					$messageStack->add_session(WARNING_SUPPLIER_TASKS_BILLED_RATIO, 'warning');
				}
			}
			
			if (isset($HTTP_POST_VARS["adjust_price"])) {
				if ($task_info_row['supplier_tasks_billing_status'] == 0) {
					if (tep_not_null($HTTP_POST_VARS["adjust_price"][$key])) {
						$adjust_price = ($HTTP_POST_VARS["adjust_sign"][$key] == '-' ? $HTTP_POST_VARS["adjust_sign"][$key] : '') . $HTTP_POST_VARS["adjust_price"][$key];
						
						if ($task_info_row["supplier_payable_adjust"] != $adjust_price) {
							$email_contents .= (tep_not_null($comment) ? "\n" : "") . ENTRY_AMOUNT_ADJUST . " " . (tep_not_null($task_info_row["supplier_payable_adjust"]) ? $task_info_row["supplier_payable_adjust"] : '0.00') . ' -> ' . $adjust_price;
							$comment .= (tep_not_null($comment) ? "\n\n" : "") . ENTRY_AMOUNT_ADJUST . " " . (tep_not_null($task_info_row["supplier_payable_adjust"]) ? $task_info_row["supplier_payable_adjust"] : '0.00') . ' -> ' . $adjust_price;
							
							$update_supplier_payable_adjust_array = array(	'supplier_payable_adjust' => $adjust_price);
					        tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION, $update_supplier_payable_adjust_array, 'update', "orders_products_id = '" . (int)$key . "'");
						}
					}
				} else {
					$messageStack->add_session(WARNING_SUPPLIER_TASKS_BILLED_PRICE, 'warning');
				}
			}
			
			$sql_data_array = array('supplier_tasks_allocation_history_show' => 0);
		
			tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $sql_data_array, 'update', "orders_products_id = '" . (int)$key . "'");
			
			if (isset($HTTP_POST_VARS["show_supplier"][$key]) && is_array($HTTP_POST_VARS["show_supplier"][$key])) {
				$task_allocation_history_update_sql = "	UPDATE " . TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY . " 
														SET supplier_tasks_allocation_history_show=1 
														WHERE supplier_tasks_allocation_history_id IN ('" . implode("', '", array_keys($HTTP_POST_VARS["show_supplier"][$key])) . "')";
				
				tep_db_query($task_allocation_history_update_sql);
			}
			
			if (tep_not_null($comment)) {
				$comment .= "\n\n" . $HTTP_POST_VARS["comments"][$key];
			} else {
				$comment = $HTTP_POST_VARS["comments"][$key];
			}
			
			if ($level_update == true || tep_not_null($comment)) {
				$sql_data_array = array('orders_products_id' => tep_db_prepare_input($key),
										'supplier_tasks_status' => ($level_update == true ? tep_db_input($HTTP_POST_VARS["task_status"][$key]) : 'NULL'),
										'date_added' => 'now()',
										'comments' => (tep_not_null($comment) ? tep_db_prepare_input($comment) : 'NULL'),
										'changed_by' => tep_db_prepare_input($login_email_address),
										'user_role' => 'admin',
										'notify_recipient' => (tep_not_null($HTTP_POST_VARS["comments"][$key]) ? 1 : 0)
										);
				
				if (!isset($HTTP_POST_VARS["show_supplier_latest"][$key])) {
					$sql_data_array['supplier_tasks_allocation_history_show'] = 0;
				}
				
				tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $sql_data_array);
			}
			
			if ($level_update)	{
				if ($task_info_row['supplier_tasks_status'] == '2' && $HTTP_POST_VARS["task_status"][$key] == '4') {	// Changed from On Hold to Completed
					/*************************************************************************************
						Preparing data for scheduled cron job
						This step has to perform before the PWL Order actually get updated to Completed
					*************************************************************************************/
					$cron_order_product_info_select_sql = "	SELECT o.date_purchased, op.products_id 
															FROM " . TABLE_ORDERS . " AS o 
															INNER JOIN " . TABLE_ORDERS_PRODUCTS . " op 
																ON o.orders_id=op.orders_id 
															WHERE op.orders_products_id = '" . tep_db_input($key) . "'";
					$cron_order_product_info_result_sql = tep_db_query($cron_order_product_info_select_sql);
					$cron_order_product_info_row = tep_db_fetch_array($cron_order_product_info_result_sql);
					
					$this_prod_mature_period = tep_get_products_payment_mature_period($cron_order_product_info_row['products_id']);
					
					tep_insert_cron_pending_credit('PWL', $key, $cron_order_product_info_row['date_purchased'], $this_prod_mature_period, $HTTP_POST_VARS["task_status"][$key]);
					// End of preparing data for scheduled cron job
				}
				
				tep_status_update_notification('PWL', $key, $login_email_address, $task_info_row['supplier_tasks_status'], $HTTP_POST_VARS["task_status"][$key], 'M', $comment);
			}
			
			if (tep_not_null($email_contents)) {
				$email_to_array = tep_parse_email_string($cat_cfg_array['POWERLEVELING_PRICE_ADJUSTMENT_NOTIFICATION']);
				
				$pwl_info_select_sql = "	SELECT o.orders_id, ocp.orders_custom_products_number, sta.supplier_firstname, sta.supplier_lastname, s.supplier_code 
											FROM " . TABLE_ORDERS . " AS o 
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
												ON (o.orders_id=op.orders_id AND op.custom_products_type_id='1') 
											INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
												ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
											INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
												ON (op.orders_products_id=sta.orders_products_id) 
											LEFT JOIN " . TABLE_SUPPLIER . " AS s 
												ON (sta.suppliers_id=s.supplier_id) 
											WHERE sta.orders_products_id ='" . (int)$key . "'";
				$pwl_info_result_sql = tep_db_query($pwl_info_select_sql);
				$pwl_info_row = tep_db_fetch_array($pwl_info_result_sql);
				
				$email_contents = TEXT_PWL_ORDER_ID . $pwl_info_row['orders_id'] . "-" . $pwl_info_row['orders_custom_products_number'] . "\n" . TEXT_LEVELER . $pwl_info_row['supplier_firstname'] . ' ' . $pwl_info_row['supplier_lastname'] . ((tep_not_null($pwl_info_row['supplier_code'])) ? ' (' . $pwl_info_row['supplier_code'] . ')' . "\n\n" : "\n\n") . TEXT_ADJUSTMENT_DETAIL . "\n" . $email_contents . "\n" . TEXT_ADJUSTED_BY . $login_email_address;
				
				if (tep_not_null($HTTP_POST_VARS["comments"][$key])) {
					$email_contents .= "\n\n" . TEXT_COMMENT . "\n" . $HTTP_POST_VARS["comments"][$key];
				}
				
				for ($email_to_cnt=0; $email_to_cnt < count($email_to_array); $email_to_cnt++) {
        			tep_mail($email_to_array[$email_to_cnt]['name'], $email_to_array[$email_to_cnt]['email'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_SUBJECT_PWL_PRICE_ADJUST, $pwl_info_row['orders_id'] . "-" . $pwl_info_row['orders_custom_products_number']))), $email_contents, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        		}
			}
		}
		
		tep_redirect(tep_href_link(FILENAME_PROGRESS_REPORT, tep_get_all_get_params()));
		break;
	} else if ($action == "verify_order") {
		$oProdID = (int)$_REQUEST["orders_product_id"];
		
		$task_allocation_verify_update_sql_data = array('supplier_tasks_verify_mode' => (int)$_REQUEST["v_mode"]);
		tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION, $task_allocation_verify_update_sql_data, 'update', "orders_products_id = '" . tep_db_input($oProdID) . "' AND supplier_tasks_status=4");
		
		$task_allocation_history_data_array = array('orders_products_id' => tep_db_prepare_input($oProdID),
													'supplier_tasks_status' => 'NULL',
													'date_added' => 'now()',
													'comments' => ((int)$_REQUEST["v_mode"] == '1' ? 'Mark as verified' : 'Mark as unverify'),
													'changed_by' => tep_db_prepare_input($login_email_address),
													'user_role' => 'admin',
													'notify_recipient' => 0,
													'supplier_tasks_allocation_history_show' => 0
													);
		tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $task_allocation_history_data_array);
		
		tep_redirect(tep_href_link(FILENAME_PROGRESS_REPORT, tep_get_all_get_params(array('action', 'subaction', 'v_mode')).'action=report'));
		
		break;
	} else if ($action == "delete_character") {
		$game_char_id = (int)$_REQUEST['game_char_id'];
		
		$orders_products_id_select_sql = "	SELECT orders_products_id, name FROM " . TABLE_GAME_CHAR . " WHERE game_char_id = '" . $game_char_id . "'";
		$orders_products_id_result_sql = tep_db_query($orders_products_id_select_sql);
		$orders_products_id_row = tep_db_fetch_array($orders_products_id_result_sql);
		
		$task_allocation_history_data_array = array('orders_products_id' => (int)$orders_products_id_row['orders_products_id'],
													'supplier_tasks_status' => 'NULL',
													'date_added' => 'now()',
													'comments' => 'Deletion of ' . tep_db_prepare_input($orders_products_id_row['name']) . ' Character',
													'changed_by' => tep_db_prepare_input($login_email_address),
													'user_role' => 'admin',
													'notify_recipient' => 0,
													'supplier_tasks_allocation_history_show' => 0
													);
		
		tep_db_perform(TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY, $task_allocation_history_data_array);
		
		$char_item_history_id_select_sql = " SELECT char_item_history_id FROM " . TABLE_CHAR_ITEM_HISTORY  . " WHERE game_char_id ='" . $game_char_id . "'";
		$char_item_history_id_result_sql = tep_db_query($char_item_history_id_select_sql);
		while ($char_item_history_id_row = tep_db_fetch_array($char_item_history_id_result_sql)) {
			tep_db_query("DELETE FROM " . TABLE_CHAR_ITEM_STORAGE . " WHERE char_item_history_id = '" . (int)$char_item_history_id_row['char_item_history_id'] . "'");
		}
		
		$char_pet_id_select_sql = " SELECT char_pet_id FROM " . TABLE_CHAR_PET . " WHERE game_char_id ='" . $game_char_id . "'";
		$char_pet_id_result_sql = tep_db_query($char_pet_id_select_sql);
		while ($char_pet_id_row = tep_db_fetch_array($char_pet_id_result_sql)) {
			tep_db_query("DELETE FROM " . TABLE_CHAR_PET_HISTORY . " WHERE char_pet_id = '" . (int)$char_pet_id_row['char_pet_id'] . "'");
		}
		
		$char_reputation_history_id_select_sql = " SELECT char_reputation_history_id FROM " . TABLE_CHAR_REPUTATION_HISTORY . " WHERE game_char_id = '" . (int)$game_char_id . "'";
		$char_reputation_history_id_result_sql = tep_db_query($char_reputation_history_id_select_sql);
		while ($char_reputation_history_id_row = tep_db_fetch_array($char_reputation_history_id_result_sql)) {
			tep_db_query("DELETE FROM " . TABLE_CHAR_REPUTATION_DETAIL . " WHERE char_reputation_history_id = '" . (int)$char_reputation_history_id_row['char_reputation_history_id'] . "'");
		}
		
		tep_db_query("DELETE FROM " . TABLE_GAME_CHAR . " WHERE game_char_id = '" . $game_char_id . "'");
		tep_db_query("DELETE FROM " . TABLE_GAME_CHAR_HISTORY . " WHERE game_char_id = '" . $game_char_id . "'");
		tep_db_query("DELETE FROM " . TABLE_CHAR_HONOR_HISTORY . " WHERE game_char_id = '" . $game_char_id . "'");
		tep_db_query("DELETE FROM " . TABLE_CHAR_ITEM_HISTORY . " WHERE game_char_id = '" . $game_char_id . "'");
		tep_db_query("DELETE FROM " . TABLE_CHAR_PET . " WHERE game_char_id = '" . $game_char_id . "'");
		tep_db_query("DELETE FROM " . TABLE_CHAR_QUEST_HISTORY . " WHERE game_char_id = '" . $game_char_id . "'");
		tep_db_query("DELETE FROM " . TABLE_CHAR_REPUTATION_HISTORY . " WHERE game_char_id = '" . $game_char_id . "'");
		tep_db_query("DELETE FROM " . TABLE_CHAR_SKILL_HISTORY . " WHERE game_char_id = '" . $game_char_id . "'");
		
		$messageStack->add_session('Success: Character has been successfully deleted', 'success');
		
		tep_redirect(tep_href_link(FILENAME_PROGRESS_REPORT, 'orders_product_id=' . $orders_products_id_row['orders_products_id'] . '&action=report'));
		
		break;
	}
}

$export_csv_array = array();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/javascript/jquery.js"></script>
	<script language="JavaScript" src="includes/javascript/ogm_jquery.js"></script>
<?
	if ($_REQUEST['action'] == 'char_info') {
		if (ENABLE_SSL == 'true') {
        	$path = HTTPS_CATALOG_SERVER . DIR_WS_CATALOG . DIR_WS_INTERFACE;
      	} else {
        	$path = HTTP_CATALOG_SERVER . DIR_WS_CATALOG . DIR_WS_INTERFACE;
      	}
?>
	<link rel="Stylesheet" href="includes/wow_stylesheet.php?path=<?=$path?>" type="text/css" media="Screen" />
<? } ?>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/php_serializer.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
	<script language="javascript" src="includes/javascript/custom_product_xmlhttp.js"></script>
	<!--<script language="JavaScript" src="<?=DIR_WS_INCLUDES?>addon/scriptasylum_popup/popup.js"></script>-->
	<script language="javascript">
		<!--
		var pageLoaded = false;
		
		function init() {
			// quit if this function has already been called
	       	if (arguments.callee.done) return;
			
	       	// flag this function so we don't do the same thing twice
	       	arguments.callee.done = true;
			
	       	initInfoCaptions();
	       	pageLoaded = true;	// Control when a javascript event in this page can be triggered
		};
		
	   	/* for Mozilla */
	   	if (document.addEventListener) {
	       	document.addEventListener("DOMContentLoaded", init, null);
	   	}
		
	   	/* for other browsers */
	   	window.onload = init;
		//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<div id="dhtmlTooltip"></div>
<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/dhtml_tooltip.js"></script>
<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
	<tr>
		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    		<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
			</table>
		</td>
<!-- body_text //-->
		<td width="100%" valign="top">
<?
	if ($_REQUEST['action'] == 'search' || $_REQUEST['action'] == 'adv_search') {
?>
				<table border="0" width="100%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
					<tr>
						<td><span class="pageHeading"><?=sprintf(HEADING_INPUT_TITLE, HEADING_LIST_CRITERIA)?></span></td>
					</tr>
<?
		if ($_REQUEST['action'] == 'search') {
			if (tep_not_null($_REQUEST['searchby'])) {
				$show_order_status[] = $_REQUEST['searchby'];
				
				$order_id_where_str = " 1 ";
				$product_name_where_str = " 1 ";
				$start_date_str = " 1 ";
				$end_date_str = " 1 ";
				$follow_up_date_str = " 1 ";
				$supplier_where_str = " 1 ";
				$customer_id_where_str = " 1 ";
				$categories_where_str = " 1 ";
			}
			
			if (tep_not_null($_REQUEST['sid'])) {
				$supplier_where_str = " sta.suppliers_id ='" . tep_db_input($_REQUEST['sid']) . "'";
			} else {
				$active_suppliers_array = array();
				
				$suppliers_id_active_select_sql = "SELECT suppliers_id FROM " . TABLE_SUPPLIER_TASKS_SETTING . " WHERE custom_products_type_id = 1";
				$suppliers_id_active_result_sql = tep_db_query($suppliers_id_active_select_sql);
				
				while ($suppliers_id_active_row = tep_db_fetch_array($suppliers_id_active_result_sql)) {
					$active_suppliers_array[] = $suppliers_id_active_row['suppliers_id'];
				}
				
				if (sizeof($active_suppliers_array > 0)) {
					$supplier_id_str = implode("', '", $active_suppliers_array);
				
					$supplier_where_str = " sta.suppliers_id IN ('" . $supplier_id_str . "')";
				}
			}
		} else if ($_REQUEST['action'] == 'adv_search') {
			if (tep_not_null($_SESSION['custom_product_param']["custom_product_order_id"])) {
				$order_id_where_str = " o.orders_id ='" . tep_db_input($_SESSION['custom_product_param']["custom_product_order_id"]) . "'";
			} else {
				$order_id_where_str = " 1 ";
			}
			
			if (tep_not_null($_SESSION['custom_product_param']["custom_product_product_name"])) {
				$product_name_where_str = " op.products_name LIKE '%" . tep_db_input($_SESSION['custom_product_param']["custom_product_product_name"]) . "%'";
			} else {
				$product_name_where_str = " 1 ";
			}
			
			if (tep_not_null($_SESSION['custom_product_param']["custom_product_start_date"])) {
				if (strpos($_SESSION['custom_product_param']["custom_product_start_date"], ':') !== false) {
					$startDateObj = explode(' ', trim($_SESSION['custom_product_param']["custom_product_start_date"]));
					list($yr, $mth, $day) = explode('-', $startDateObj[0]);
					list($hr, $min) = explode(':', $startDateObj[1]);
					$start_date_str = " ( DATE_FORMAT(sta.supplier_tasks_start_time, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION['custom_product_param']["custom_product_start_date"]));
					$start_date_str = " ( DATE_FORMAT(sta.supplier_tasks_start_time, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
				}
			} else {
				$start_date_str = " 1 ";
			}
			
			if (tep_not_null($_SESSION['custom_product_param']["custom_product_end_date"])) {
				if (strpos($_SESSION['custom_product_param']["end_date"], ':') !== false) {
					$endDateObj = explode(' ', trim($_SESSION['custom_product_param']["custom_product_end_date"]));
					list($yr, $mth, $day) = explode('-', $endDateObj[0]);
					list($hr, $min) = explode(':', $endDateObj[1]);
					$end_date_str = " ( DATE_FORMAT(sta.supplier_tasks_start_time, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION['custom_product_param']["custom_product_end_date"]));
					$end_date_str = " ( DATE_FORMAT(sta.supplier_tasks_start_time, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
				}
			} else {
				$end_date_str = " 1 ";
			}
			
			if (tep_not_null($_SESSION['custom_product_param']["follow_up_date"])) {
				if (strpos($_SESSION['custom_product_param']["follow_up_date"], ':') !== false) {
					$dateObj = explode(' ', trim($_SESSION['custom_product_param']["follow_up_date"]));
					list($yr, $mth, $day) = explode('-', $dateObj[0]);
					list($hr, $min) = explode(':', $dateObj[1]);
					$follow_up_date_str = " ( DATE_FORMAT(sta.supplier_tasks_follow_up_datetime, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
				} else {
					list($yr, $mth, $day) = explode('-', trim($_SESSION['custom_product_param']["follow_up_date"]));
					$follow_up_date_str = " ( DATE_FORMAT(sta.supplier_tasks_follow_up_datetime, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
				}
			} else {
				$follow_up_date_str = " 1 ";
			}
			
			$supplier_where_str = " 1 ";
			if (tep_not_null($_SESSION['custom_product_param']["custom_product_not_assign"])) {
				$supplier_where_str = " sta.suppliers_id ='" . tep_db_input($_SESSION['custom_product_param']["custom_product_not_assign"]) . "'";
			} else {
				if (tep_not_null($_SESSION['custom_product_param']["custom_product_supplier"])) {
					$supplier_where_str = " sta.suppliers_id ='" . tep_db_input($_SESSION['custom_product_param']["custom_product_supplier"]) . "'";
				}
			}
			
			if (tep_not_null($_SESSION['custom_product_param']["customer_member_group"])) {
				$customers_id_array = array();
				
				$customers_id_select_sql = "SELECT customers_id 
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_groups_id = '" . tep_db_input($_SESSION['custom_product_param']["customer_member_group"]) . "'";
				
				$customers_id_result_sql = tep_db_query($customers_id_select_sql);
				while ($customers_id_row = tep_db_fetch_array($customers_id_result_sql)) {
					$customers_id_array[] .= $customers_id_row['customers_id'];
				}
				
				if (sizeof($customers_id_array) > 0) {
					$customers_id_list = implode(",", $customers_id_array);
					$customer_id_where_str = " o.customers_id IN (" .$customers_id_list. ") ";
				} else {
					$customer_id_where_str = " 1 ";
				}
			} else {
				$customer_id_where_str = " 1 ";
			}
			
			$categories_where_str = " 1 ";
			
			if (is_numeric($_SESSION['custom_product_param']["cat_id"]) && $_SESSION['custom_product_param']["cat_id"] > '0') {
				$category_array = array($_SESSION['custom_product_param']["cat_id"]);
				if ($_SESSION['custom_product_param']["include_subcategory"]) {
			  		tep_get_subcategories($category_array, $_SESSION['custom_product_param']["cat_id"]);
			  	}
			  	
			  	$category_select_str .= " LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc ON (op.products_id=pc.products_id and pc.products_is_link=0) ";
		  		$categories_where_str = " pc.categories_id IN ('" . implode("', '", $category_array) . "') ";
			}
			
			$auto_orders_products_id_select_sql = " SELECT DISTINCT(sta.supplier_tasks_status) 
													FROM " . TABLE_ORDERS . " AS o 
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
														ON (o.orders_id=op.orders_id AND op.custom_products_type_id='1') 
													INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
														ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
													INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
														ON (op.orders_products_id=sta.orders_products_id) 
													LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpB 
														ON (sta.orders_products_id=ocpB.orders_products_id AND ocpB.orders_custom_products_key='power_leveling_bracket_info') 
													$category_select_str
													WHERE $order_id_where_str 
														AND $supplier_where_str 
														AND $product_name_where_str 
														AND $start_date_str 
														AND $end_date_str 
														AND $follow_up_date_str 
														AND $customer_id_where_str 
														AND $categories_where_str 
													ORDER BY supplier_tasks_status ";
			
			$auto_orders_products_id_result_sql = tep_db_query($auto_orders_products_id_select_sql);
			
			$show_order_status = isset($_SESSION['custom_product_param']["order_status"]) ? $_SESSION['custom_product_param']["order_status"] : array();
			
			if (!count($show_order_status)) {
				while ($auto_orders_products_id_row = tep_db_fetch_array($auto_orders_products_id_result_sql)) {
					$show_order_status[] = $auto_orders_products_id_row["supplier_tasks_status"];
				}
			}
		}
		
		$js = '';
		$tag_selection_general = array ( array('id' => '', 'text' => 'Progress Report Lists Options ...'),
										// array('id' => 'rd', 'text' => '&nbsp;&nbsp;&nbsp;Mark as read'),
										// array('id' => 'ur', 'text' => '&nbsp;&nbsp;&nbsp;Mark as unread'),
										 array('id' => '', 'text' => 'Apply tag:', 'param' => 'disabled')
									   );
									   
		$extra_detail = Array ();
		$payment_detail = Array ();
		
		for ($status = 0; $status < count($show_order_status); $status++) {
			$form_name = 'progress_report_'.$show_order_status[$status].'_lists_form';
			$total = 0;
			echo tep_draw_hidden_field('s_'.$show_order_status[$status].'_order_str', '', ' id="s_'.$show_order_status[$status].'_order_str" ');
			
			$mirror_for_delete_tag = array();
			$status_dependent_tag_array = $tag_selection_general;
			$order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('".$show_order_status[$status]."', orders_tag_status_ids) AND filename='".FILENAME_PROGRESS_REPORT."';";
			
			$order_tag_result_sql = tep_db_query($order_tag_select_sql);
			while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
				$status_dependent_tag_array[] = array('id' => 'otag_'.$order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$order_tag_row["orders_tag_name"]);
				$mirror_for_delete_tag[] = array('id' => 'rmtag_'.$order_tag_row["orders_tag_id"], 'text' => '&nbsp;&nbsp;&nbsp;'.$order_tag_row["orders_tag_name"]);
			}
			
			$status_dependent_tag_array[] = array('id' => 'nt', 'text' => '&nbsp;&nbsp;&nbsp;New tag ...');
			$status_dependent_tag_array[] = array('id' => '', 'text' => 'Remove tag:', 'param' => 'disabled');
			$status_dependent_tag_array = array_merge($status_dependent_tag_array, $mirror_for_delete_tag);
			
			if ($show_order_status[$status] == 4) {
				$export_csv_array['HEADER'] = array(TABLE_HEADING_NUMBER, TABLE_HEADING_SUBMITTED_DATE, TABLE_HEADING_SUPPLIER_CODE, TABLE_HEADING_CUSTOMERS_EMAIL, TABLE_HEADING_CUSTOMER_GROUP, TABLE_HEADING_GAME, TABLE_HEADING_PRODUCT_NAME, TABLE_HEADING_TOTAL);
			}
			
			if ($_REQUEST['action'] == 'adv_search') {
				$status_tag_array = array();
				if (isset($_SESSION['custom_product_param']["status_".$show_order_status[$status]]) && count($_SESSION['custom_product_param']["status_".$show_order_status[$status]])) {
					foreach ($_SESSION['custom_product_param']["status_".$show_order_status[$status]] as $tag_id) {
						if ($tag_id == 'no_tag') {
							$status_tag_array[] = " sta.orders_tag_ids='' ";
						} else {
							$status_tag_array[] = " FIND_IN_SET('".$tag_id."', sta.orders_tag_ids) ";
						}
					}
				}
			}
			$order_tag_where_str = count($status_tag_array) ? ' ('.implode(' or ', $status_tag_array).') ' : ' 1 ';
			
			$orders_orders_id_select_sql = "	SELECT distinct(o.orders_id) 
												FROM " . TABLE_ORDERS . " AS o 
												INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON (o.orders_id=op.orders_id AND op.custom_products_type_id='1') 
												INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
													ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
												INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
													ON (op.orders_products_id=sta.orders_products_id) 
												LEFT JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocpB 
													ON (sta.orders_products_id=ocpB.orders_products_id AND ocpB.orders_custom_products_key='power_leveling_bracket_info') 
												$category_select_str
												WHERE sta.supplier_tasks_status ='" . (int)$show_order_status[$status] . "' 
													AND $order_id_where_str 
													AND $supplier_where_str 
													AND $product_name_where_str 
													AND $start_date_str 
													AND $end_date_str 
													AND $follow_up_date_str 
													AND $order_tag_where_str 
													AND $customer_id_where_str 
													AND $categories_where_str";
			
			if ($show_order_status[$status] == 4 && tep_not_null($_REQUEST['bill'])) {
				$orders_orders_id_select_sql .= " AND sta.supplier_tasks_billing_status ='" . (int)$_REQUEST['bill'] . "'";
			}
			
			$orders_orders_id_select_sql .= "ORDER BY o.orders_id DESC";
			
			if ($show_records != "ALL") {
				$orders_split_object = new splitPageResults($HTTP_GET_VARS['page'.$show_order_status[$status]], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $orders_orders_id_select_sql, $orders_select_sql_numrows, true);
			}
			
			$orders_orders_id_result_sql = tep_db_query($orders_orders_id_select_sql);
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td>
							<span class="pageHeading"><?=$status_name[$show_order_status[$status]]?></span>
							<span id="showhidedetails_<?=$show_order_status[$status]?>"><a href="javascript:;" onclick="showHideDetails_<?=$show_order_status[$status]?>(this)"><?=TEXT_SHOW_DETAILS?></a></span>
<?
								echo '&nbsp;&nbsp;<span id="s_'.$show_order_status[$status].'_tag_nav" class="hide">'.tep_draw_pull_down_menu("s_".$show_order_status[$status]."_tag_selector", $status_dependent_tag_array, '', ' id="s_'.$show_order_status[$status].'_tag_selector" onChange="orderListsOptions(this, \''.$show_order_status[$status].'\', \''.(int)$languages_id.'\', \'\', true);"').'</span>';
?>
						</td>
					</tr>
					<tr>
						<td>
<?							echo tep_draw_form($form_name, FILENAME_PROGRESS_REPORT, tep_get_all_get_params(array('action')) . 'action=batch_action', 'post');?>
								<table border="0" width="100%" cellspacing="1" cellpadding="2">
									<tr>
										<td class="ordersBoxHeading"><?=ENTRY_HEADING_ORDER_ID?></td>
										<td class="ordersBoxHeading">
											<table border="0" width="100%" cellspacing="0" cellpadding="1" style="border-collapse: collapse;">
												<tr>
													<td class="ordersBoxHeading" width="29px"><?=TABLE_HEADING_NUMBER?></td>
<?								if ($show_order_status[$status] != 4) { ?>
													<td class="ordersBoxHeading" width="93px"><?=TABLE_HEADING_SUBMITTED_DATE?></td>
<?								} else { ?>
													<td class="ordersBoxHeading" width="93px"><?=TABLE_HEADING_COMPLETED_DATE?></td>
<?								} ?>
													<td class="ordersBoxHeading" width="100px"><?=TABLE_HEADING_TAG?></td>
													<td class="ordersBoxHeading" width="100px"><?=TABLE_HEADING_SUPPLIER_CODE?></td>
<?								if ($show_order_status[$status] != 4) { ?>
													<td class="ordersBoxHeading" width="110px"><?=TABLE_HEADING_CUSTOMER_GROUP?></td>
<?								} ?>
													<td class="ordersBoxHeading" width="55px"><?=TABLE_HEADING_GAME?></td>
													<td class="ordersBoxHeading" width="160px"><?=TABLE_HEADING_PRODUCT_NAME?></td>
<?								if ($show_order_status[$status] != 4 && $show_order_status[$status] != 0) { ?>
													<td class="ordersBoxHeading" width="120px" align="center"><?=TABLE_HEADING_PROGRESS_STATUS?></td>
<?								} ?>
													<td class="ordersBoxHeading" width="70px" align="right"><?=TABLE_HEADING_TOTAL?></td>
<?
								if ($show_order_status[$status] == 4) {
									if ($supplier_cp_payment_detail_permission || $supplier_cp_payable_manual_adjust) {
?>
													<td width="70px" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_CP_BILLING_STATUS?></td>
													<td class="ordersBoxHeading" width="70px" align="right"><?=ENTRY_HEADING_RATIO?></td>
													<td class="ordersBoxHeading" width="70px" align="right"><?=TABLE_AMOUNT_ADJUST?></td>
													<td class="ordersBoxHeading" width="72px" align="right"><?=TABLE_HEADING_TOTAL_PAYABLE?></td>
<?				
									}
								}
								if ($show_order_status[$status] == 4 && $supplier_cp_payment_detail_permission) {
?>
													<td class="ordersBoxHeading" align="center" width="20px"><?=tep_draw_checkbox_field('select_all_'.$show_order_status[$status], '', false, '', 'id="select_all_'.$show_order_status[$status].'" onClick="javascript:void(setCheckboxes(\''.$form_name.'\',\'select_all_'.$show_order_status[$status].'\',\'orders_batch\')); '.($show_order_status[$status] == 4 ? 'update_selected_price(this.form, \''.$show_order_status[$status].'\', \'orders_batch\');' : '').'"')?></td>
<?								} ?>
													<td class="ordersBoxHeading" width="44px" align="center"><?=TABLE_HEADING_ACTION?></td>
												</tr>
											</table>
										</td>
									</tr>
<?
				$row_count = 0;
				$tasks_count = 0;
				
				while ($orders_orders_id_row = tep_db_fetch_array($orders_orders_id_result_sql)) {
					if (tep_not_null($orders_orders_id_row["orders_id"])) {
						$orders_id_where = " o.orders_id ='" . (int)$orders_orders_id_row["orders_id"] . "'";
					} else {
						$orders_id_where = " 1 ";
					}
					
					$orders_orders_info_select_sql = "	SELECT IF(op.orders_products_store_price IS NULL, (((op.final_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust), (((op.orders_products_store_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust)) AS payable_price, sta.suppliers_id, SEC_TO_TIME( (TO_DAYS(now())*24*3600+TIME_TO_SEC(now())) - (TO_DAYS(sta.supplier_tasks_time_reference)*24*3600+TIME_TO_SEC(sta.supplier_tasks_time_reference)) ) AS hours, sta.orders_products_id, DATE_FORMAT(sta.supplier_tasks_start_time, '%Y-%m-%d') AS supplier_tasks_start_time, 
														sta.supplier_tasks_time_reference, sta.supplier_tasks_time_taken, sta.supplier_firstname, sta.supplier_lastname, sta.orders_tag_ids, replace(rtrim(replace(replace(rtrim(replace(supplier_tasks_allocation_ratio_adjust,'0',' ')),' ','0'),'.',' ')),' ','.') AS previous_ratio, sta.supplier_payable_adjust, sta.supplier_tasks_billing_status, sta.supplier_tasks_verify_mode, 
														s.supplier_code, op.final_price, ocp.orders_custom_products_value AS task_info, ocp.orders_custom_products_number, sta.supplier_tasks_allocation_info, sta.supplier_tasks_allocation_progress, sta.supplier_tasks_status, o.orders_id, o.customers_id, o.customers_email_address, o.currency, o.currency_value, op.products_name, op.products_id, op.orders_products_store_price 
														FROM " . TABLE_ORDERS . " AS o 
														INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
															ON (o.orders_status=os.orders_status_id) 
														INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
															ON (o.orders_id=op.orders_id AND op.custom_products_type_id = 1) 
														INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
															ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
														INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
															ON (op.orders_products_id=sta.orders_products_id) 
														LEFT JOIN " . TABLE_SUPPLIER . " AS s 
															ON (sta.suppliers_id=s.supplier_id) 
														$category_select_str
														WHERE os.language_id = '1' 
															AND sta.supplier_tasks_status ='" . (int)$show_order_status[$status] . "' 
															AND $orders_id_where 
															AND $product_name_where_str 
															AND $start_date_str 
															AND $end_date_str 
															AND $follow_up_date_str 
															AND $order_tag_where_str 
															AND $supplier_where_str 
															AND $customer_id_where_str 
															AND $categories_where_str 
														ORDER BY orders_custom_products_number";
					
					$orders_orders_info_result_sql = tep_db_query($orders_orders_info_select_sql);
?>
									<tr class="ordersListingEven">
										<td class="main" valign="top"><a href="<?=tep_href_link("orders.php", 'oID='.$orders_orders_id_row["orders_id"].'&action=edit', 'NONSSL')?>"><?=$orders_orders_id_row["orders_id"]?></a></td>
										<td>
											<table border="0" width="100%" cellspacing="0" cellpadding="1" style="border-collapse: collapse;">
<?
					while ($orders_orders_info_row = tep_db_fetch_array($orders_orders_info_result_sql)) {				
						$time_progress_ratio = 0;
						$bracket_info_array = custom_product::order_product_bracket_info($orders_orders_info_row['orders_products_id']);
						if ($show_order_status[$status] != 4 && $show_order_status[$status] != 0) {
							$level_progress_ratio = custom_product::calculate_level_percentage($orders_orders_info_row['orders_products_id']);
						}
						
						if (sizeof($bracket_info_array) > 0) {
							$total_time = $bracket_info_array['total_time'];
							$time_pass = $bracket_info_array['time_pass'];
							$start_level = $bracket_info_array['start_level'];
							$desired_level = $bracket_info_array['desired_level'];
							$start_level_display = $bracket_info_array['start_level_display'];
							$desired_level_display = $bracket_info_array['desired_level_display'];
							$level_label = $bracket_info_array['level_label'];
							
							if (tep_not_null($total_time) && tep_not_null($time_pass)) {
								$time_progress_ratio = round(($time_pass / $total_time) * 100);
							}
						}
						
						$tasks_count++;
						$game_name = tep_chunk_string($orders_orders_info_row['products_id'], 'products_id', '>', '1');
						
						$total += tep_not_null($orders_orders_info_row['orders_products_store_price']) ? $orders_orders_info_row['orders_products_store_price'] : $orders_orders_info_row['final_price'];
						$time_pass = 0;
						$orderproductnumber = $orders_orders_info_row['orders_products_id'];
						$payment_detail[$show_order_status[$status]]['order_id'][] = $orderproductnumber;
						
						$time_array = explode(':', $orders_products_id_row['hours']);
						
						if ($orders_orders_info_row['supplier_tasks_status'] == 2 || $orders_orders_info_row['supplier_tasks_status'] == 3 || $orders_products_id_row['supplier_tasks_status'] == 4 || $orders_products_id_row['supplier_tasks_status'] == 5) {
							$time_pass = (int)((int)$orders_orders_info_row['supplier_tasks_time_taken'] / 60);
						} else if ($orders_orders_info_row['supplier_tasks_status'] == 1) {
							$time_pass = (int)((int)$time_array[0] + ((int)$orders_orders_info_row['supplier_tasks_time_taken'] / 60));
						}
						
						$cust_group_select_sql = "SELECT cg.customers_groups_name FROM " . TABLE_CUSTOMERS . " AS c INNER JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg ON c.customers_groups_id=cg.customers_groups_id WHERE c.customers_id = '" . $orders_orders_info_row['customers_id'] . "'";
						$cust_group_result_sql = tep_db_query($cust_group_select_sql);
						if ($cust_group_row = tep_db_fetch_array($cust_group_result_sql)) {
							$cust_member_grp_name = $cust_group_row["customers_groups_name"];
						} else { 
							$cust_member_grp_name = '&nbsp;';
						}
						
						$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
						$task_row_style = $row_style;
						
						$tags_str = '';
						$js.= "orders_products_ids_$show_order_status[$status].push(".$orders_orders_info_row['orders_products_id'].");";
						
			  			if (tep_not_null($orders_orders_info_row["orders_tag_ids"])) {
			  				$orders_assigned_tag_select_sql = "SELECT orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET(orders_tag_id, '".$orders_orders_info_row["orders_tag_ids"]."') AND filename='".FILENAME_PROGRESS_REPORT."';";
							$orders_assigned_tag_result_sql = tep_db_query($orders_assigned_tag_select_sql);
							while ($orders_assigned_tag_row = tep_db_fetch_array($orders_assigned_tag_result_sql)) {
								$tags_str .= $orders_assigned_tag_row["orders_tag_name"] . ', ';
							}
							if (substr($tags_str, -2) == ', ') 	$tags_str = substr($tags_str, 0, -2);
			  			}
						
						if ($show_order_status[$status] == 4) {
							$export_csv_array[$orders_orders_info_row['orders_products_id']] = array($orders_orders_info_row['orders_id'], $orders_orders_info_row['supplier_tasks_start_time'], $orders_orders_info_row['supplier_code'], $orders_orders_info_row['customers_email_address'], $cust_member_grp_name, $game_name, $orders_orders_info_row['products_name'], $currencies->format( tep_not_null($orders_orders_info_row['orders_products_store_price']) ? $orders_orders_info_row['orders_products_store_price'] : $orders_orders_info_row['final_price'], true, $orders_orders_info_row['currency'], $orders_orders_info_row['currency_value']));
						}
						
						$user_role_select_sql = "	SELECT stah.user_role, stah.comments, stah.changed_by, stah.date_added, stah.user_role, s.supplier_code 
													FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY . " AS stah 
													LEFT JOIN " . TABLE_SUPPLIER . " AS s 
														ON (stah.changed_by = s.supplier_email_address AND stah.user_role = 'supplier') 
													WHERE orders_products_id = '" . tep_db_input($orders_orders_info_row["orders_products_id"]) . "' 
													ORDER BY date_added desc, supplier_tasks_allocation_history_id desc LIMIT 1";
						$user_role_result_sql = tep_db_query($user_role_select_sql);
						$user_role_row = tep_db_fetch_array($user_role_result_sql);
						
						if ($show_order_status[$status] != 4 && $show_order_status[$status] != 5) {
							if ($user_role_row["user_role"] == 'admin') {
								$task_row_style = "ordersListingMsgOut";
							} else if ($user_role_row["user_role"] == 'supplier') {
								$task_row_style = "ordersListingMsgIn";
							}
						} else {
							$completed_date_select_sql = "	SELECT DATE_FORMAT(date_added, '%Y-%m-%d') AS date_added 
															FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY . " 
															WHERE orders_products_id = '" . tep_db_input($orders_orders_info_row["orders_products_id"]) . "' 
																AND user_role = 'admin' 
																AND supplier_tasks_status = 4 
															ORDER BY date_added desc LIMIT 1";
							$completed_date_result_sql = tep_db_query($completed_date_select_sql);
							$completed_date_row = tep_db_fetch_array($completed_date_result_sql);
						}
?>
												<tr id="<?='s_'.$show_order_status[$status].'_main_'.$row_count?>" class="<?=$task_row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?='s_'.$status.'_sub_'.$row_count?>##<?='s1'.$status.'_sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$task_row_style?>', '<?='s_'.$status.'_sub_'.$row_count?>##<?='s_'.$status.'_sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$task_row_style?>', '<?='s_'.$status.'_sub_'.$row_count?>##<?='s_'.$status.'_sub2_'.$row_count?>')">
													<td>
														<table border="0" width="100%" cellspacing="0" cellpadding="0">
															<tr>
																<td class="main" width="29px">
<?
														if ($show_order_status[$status] == 1) {
															$game_char_history_date_select_sql = "	SELECT MAX(gch.game_char_history_date) AS last_update_date 
																									FROM " . TABLE_GAME_CHAR . " AS gc 
																									INNER JOIN " . TABLE_GAME_CHAR_HISTORY . " AS gch 
																										ON (gc.game_char_id = gch.game_char_id) 
																									WHERE orders_products_id ='" . (int)$orders_orders_info_row['orders_products_id'] . "' ORDER BY gc.game_char_id LIMIT 1";
															$game_char_history_date_result_sql = tep_db_query($game_char_history_date_select_sql);
															$game_char_history_date_row = tep_db_fetch_array($game_char_history_date_result_sql);
															
															if (tep_not_null($game_char_history_date_row['last_update_date'])) {
																$duration_show_style = array();
																$duration_show_style[] = array('time' => 0, 'color' => "blackIndicator");
																$duration_show_style[] = array('time' => CP_PROGRESS_PROFILE_ALERT_TIME, 'color' => "redIndicator");	
																
																echo '<b>' .tep_string_output($orders_orders_info_row['orders_custom_products_number'], (int)(tep_day_diff($game_char_history_date_row['last_update_date'], date('Y-m-d H:i:s'), 'sec') / 60), $duration_show_style) . '</b>';
															} else {
																echo $orders_orders_info_row['orders_custom_products_number'];
															}
														} else {
															echo $orders_orders_info_row['orders_custom_products_number'];
														}
?>
																</td>
<?						if ($show_order_status[$status] != 4) { ?>
																<td class="ordersRecords" width="93px"><?=$orders_orders_info_row['supplier_tasks_start_time']?></td>
<?						} else { ?>
																<td class="ordersRecords" width="93px"><?=(tep_not_null($completed_date_row['date_added']) ? $completed_date_row['date_added'] : TEXT_NOT_AVAILABLE)?></td>
<?						} ?>
																<td class="ordersRecords" width="100px"><span class="greenIndicator" id="tag_<?=$orderproductnumber?>"><?=$tags_str?></span>&nbsp;</td>
																<td class="ordersRecords" width="100px"><?=($orders_orders_info_row['suppliers_id'] ? (tep_not_null($orders_orders_info_row['supplier_code']) ? $orders_orders_info_row['supplier_code'] : $orders_orders_info_row['supplier_firstname'] . ' ' . $orders_orders_info_row['supplier_lastname']) : TEXT_NOT_ASSIGN_TO_SUPPLIER)?></td>
<?						if ($show_order_status[$status] != 4) { ?>
																<td class="ordersRecords" width="110px"><?=$cust_member_grp_name?></td>
<?						} ?>
																<td class="ordersRecords" width="55px"><?=$game_name?></td>
																<td class="ordersRecords" width="160px"><?=$orders_orders_info_row['products_name']?></td>
<?						if ($show_order_status[$status] != 4 && $show_order_status[$status] != 0) { ?>
																<td class="ordersRecords" width="120px" align="center"><?=(tep_not_null($total_time) ? $time_progress_ratio . '%' : TEXT_NOT_AVAILABLE) . ' / ' . ((tep_not_null($desired_level)) ? $level_progress_ratio . '%' : TEXT_NOT_AVAILABLE)?></td>
<?						}	?>
																<td class="ordersRecords" width="70px" align="right"><?=$currencies->format(tep_not_null($orders_orders_info_row['orders_products_store_price']) ? $orders_orders_info_row['orders_products_store_price'] : $orders_orders_info_row['final_price'], true, $orders_orders_info_row['currency'], $orders_orders_info_row['currency_value'])?></td>
<?
						if ($show_order_status[$status] == 4) {
							if ($supplier_cp_payment_detail_permission || $supplier_cp_payable_manual_adjust) {
								$total_price_pay_for_supplier = $orders_orders_info_row['payable_price'];
								
								$price_adjust = (substr($orders_orders_info_row['supplier_payable_adjust'], 0, 1) == '-' ? substr($orders_orders_info_row['supplier_payable_adjust'], 1) : $orders_orders_info_row['supplier_payable_adjust']);
								$price_adjust_style = ($price_adjust == 0) ? "blackIndicator" : "redIndicator";
								
								$js .= "orderAmountArray['".$orders_orders_info_row['orders_products_id']."'] = ".(double)$total_price_pay_for_supplier.";\n";
?>
																<td class="ordersRecords" width="70px" align="center">
																	<?=($orders_orders_info_row['supplier_tasks_billing_status']=='1' ? TEXT_TRANS_BILLED : TEXT_TRANS_NOT_BILLED)?>
																	<?=($orders_orders_info_row['supplier_tasks_verify_mode'] == '1' ? tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', 'Verified', 10, 10) : tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', 'Unverify', 10, 10))?>
																</td>
																<td class="ordersRecords" width="70px" align="right"><?=$orders_orders_info_row['previous_ratio']?>%</td>
																<td class="ordersRecords" width="70px" align="right"><span class="<?=$price_adjust_style?>"><?=(substr($orders_orders_info_row['supplier_payable_adjust'], 0, 1) == '-' ? '-' : '') . $currencies->format($price_adjust)?></span></td>
																<td class="ordersRecords" width="72px" align="right"><?=$currencies->format($total_price_pay_for_supplier)?></td>
<?							}
						}
						
						if ($show_order_status[$status] == 4 && $supplier_cp_payment_detail_permission) {
?>
																<td class="ordersRecords" align="center" width="20px"><?=tep_draw_checkbox_field('orders_batch[]', $orders_orders_info_row['orders_products_id'], false, '', 'id="'.$orders_orders_info_row['suppliers_id'].'_'.$orders_orders_info_row['orders_products_id'].'" onClick="update_selected_price(this.form, \''.$show_order_status[$status].'\', \'orders_batch\');"')?></td>
<?						} ?>
																<td class="ordersRecords" width="44px" align="center"><a href="<?=tep_href_link(FILENAME_PROGRESS_REPORT, 'orders_product_id=' . $orders_orders_info_row['orders_products_id'] . '&action=report')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "14", "13", 'align="top"')?></a></td>
															</tr>
															<tr>
																<td></td>
<?
						$colspan_num = 9;
						
						if ($show_order_status[$status] == 4) {
							$colspan_num = 12;
						} else if ($show_order_status[$status] == 0) {
							$colspan_num = 8;
						}
?>
																<td colspan="<?=$colspan_num?>">
																	<table cellspacing="0" cellpadding="2" border="0" width="100%">
																		<tbody class="hide" id="progressinfo<?=$orders_orders_info_row['orders_products_id']?>">
<?						if (tep_not_null($user_role_row['comments'])) { ?>
																			<tr>
																				<td class="ordersRecords" width="100%"><div style="border-bottom: 1px solid #996600;"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></div></td>
															  				</tr>
															  				<tr>
															  					<td class="ordersRecords">
															  						<?
															  						echo TEXT_COMMENT . ' ( ';
															  						
															  						if ($user_role_row["user_role"] == 'supplier') {
															  							if (tep_not_null($user_role_row["supplier_code"])) {
															  								echo $user_role_row["supplier_code"];
															  							} else {
															  								preg_match('/([0-9a-zA-Z]+)(@)([0-9a-zA-Z]+)(.com)?/', $user_role_row["changed_by"], $reply_array);
															  								echo $reply_array[1];
															  							}
																					} else if ($user_role_row["user_role"] == 'admin') {
																						echo $user_role_row["changed_by"];
																					}
																					
																					echo ' - ' . $user_role_row['date_added'];
															  						?>
															  						)</td>
															  				</tr>
																			<tr>
																				<td bgcolor="#FFFFCC" colspan="<?=$list_colspan_count?>" class="ordersRecords" style="padding:5px;"><?=tep_get_cp_password($orders_orders_info_row['products_id'], $user_role_row['comments'], $view_clear_text_password_permission)?></td>
																			</tr>
																			<tr height="10"><td></td></tr>
<?						} ?>
																		</tbody>
																	</table>
																</td>
															</tr>
														</table>
													</td>
												</tr>
<?						$row_count++;
					}
					$extra_detail[$show_order_status[$status]] = $row_count;
?>
											</table>
										</td>
									</tr>
<?				}
				
				$batch_action_array = array(array('id' => '', 'text' => 'With selected:'));
				echo tep_draw_hidden_field('payment_remark', '');
				echo tep_draw_hidden_field('show_supplier_remark', '');
				
				echo '				<tr>
										<td class="main"><b>'.TABLE_HEADING_TOTAL.'</b></td>
										<td>
											<table border="0" width="100%" border="0" cellspacing="0" cellpadding="0">
												<tr>
													<td class="ordersRecords" width="29px"><b>'.$tasks_count.'<b></td>
													<td class="ordersRecords" width="93px">&nbsp;</td>
													<td class="ordersRecords" width="100px">&nbsp;</td>
													<td class="ordersRecords" width="100px">&nbsp;</td>';
				if ($show_order_status[$status] != 4) {
					echo '							<td class="ordersRecords" width="110px">&nbsp;</td>';
				}
				echo '								<td class="ordersRecords" width="55px">&nbsp;</td>
													<td class="ordersRecords" width="160px">&nbsp;</td>';
				if ($show_order_status[$status] != 4) {
					echo '							<td class="ordersRecords" width="120px">&nbsp;</td>';
				} else {
					echo '							<td class="ordersRecords" width="70px" align="right">';
					if (count($batch_action_array) > 0) {
						echo '							<b>'.$currencies->format($total).'</b>';
					} else {
						echo '							&nbsp;';
					}
					echo '							</td>';
				}
				
				if ($show_order_status[$status] == 4) {
					if ($supplier_cp_payment_detail_permission || $supplier_cp_payable_manual_adjust) {
						echo '						<td width="70px">&nbsp;</td>
													<td width="70px">&nbsp;</td>
													<td width="70px">&nbsp;</td>
													<td align="right" width="72px" class="ordersRecords" nowrap>
														<span id="total_sel_amt_'.$show_order_status[$status].'" class="redIndicator"></span>
													</td>';
					}
				}
				
				if ($show_order_status[$status] == 4 && $supplier_cp_payment_detail_permission) {
					echo '
													<td width="20px">&nbsp;</td>';
				}
				echo '								<td width="44px">&nbsp;</td>
												</tr>
											</table>
										</td>
									</tr>';

				if ($show_order_status[$status] == 4) {
?>
									<tr>
										<td align="right" colspan="2">
											<table border="0" cellspacing="0" cellpadding="2">
												<tr>
													<td align="right">
													<?
														echo tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($export_csv_array)) . "\n";
														echo tep_submit_button('Export', 'Export as csv file', 'name="btn_csv_export"', 'inputButton') . '&nbsp;';
													?>
													</td>
												</tr>
											</table>
										</td>
									</tr>
<?				} ?>
								</table>
							</form>
						</td>
					</tr>
					<tr>
						<td colspan="2">
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<tr>
                					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_ORDERS, tep_db_num_rows($orders_orders_id_select_sql) > 0 ? "1" : "0", tep_db_num_rows($orders_orders_id_select_sql), tep_db_num_rows($orders_orders_id_select_sql)) : $orders_split_object->display_count($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'.$show_order_status[$status]], TEXT_DISPLAY_NUMBER_OF_ORDERS)?></td>
                					<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $orders_split_object->display_links($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'.$show_order_status[$status]], tep_get_all_get_params(array('page'.$show_order_status[$status], 'cont', 'criteria_id'))."cont=1", 'page'.$show_order_status[$status])?></td>
              					</tr>
            				</table>
            			</td>
            		</tr>
            		<script type="text/javascript">
            			var orders_products_ids_<?=$show_order_status[$status]?> = new Array();
            			
						function showHideDetails_<?=$show_order_status[$status]?>(link_obj) {
							var current_show = '';
							if (typeof(link_obj.innerText) != 'undefined') {
								current_show = link_obj.innerText == '<?=TEXT_SHOW_DETAILS?>' ? true : false;
							} else {
								current_show = link_obj.text == '<?=TEXT_SHOW_DETAILS?>' ? true : false;
							}

							var progressinfo;

							if (current_show) {
								link_obj.innerHTML = '<?=TEXT_HIDE_DETAILS?>';
								for (i=0; i < orders_products_ids_<?=$show_order_status[$status]?>.length; i++) {
									progressinfo = document.getElementById('progressinfo'+orders_products_ids_<?=$show_order_status[$status]?>[i]);
									progressinfo.className = "show";
								}
							} else {
								link_obj.innerHTML = '<?=TEXT_SHOW_DETAILS?>';

								for (i=0; i < orders_products_ids_<?=$show_order_status[$status]?>.length; i++) {
									progressinfo = document.getElementById('progressinfo'+orders_products_ids_<?=$show_order_status[$status]?>[i]);
									progressinfo.className = "hide";
								}
							}
						}
					</script>
<?		} ?>
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
            			<td colspan="2">
            				<table border="0" cellspacing="0" cellpadding="2">
            					<tr>
            						<td>
            							<div style="width:10px; height:10px; border: 1px solid black; background-color: #FAA298; font-size:5px"></div>
            						</td>
            						<td class="smallText" nowrap="nowrap">&nbsp;<?=LEGEND_MSG_SENT?>&nbsp;&nbsp;</td>
            						<td>
            							<div style="width:10px; height:10px; border: 1px solid black; background-color: #7187BB; font-size:5px"></div>
            						</td>
            						<td class="smallText" nowrap="nowrap">&nbsp;<?=LEGEND_MSG_RECEIVED?>&nbsp;&nbsp;</td>
            					</tr>
            				</td>
            			</td>
            		</tr>
            		<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '50')?></td>
					</tr>
				</table>
			<!--/form-->
<?	} else if ($_REQUEST['action'] == 'report') {
		$orders_products_id_select_sql = "	SELECT IF(op.orders_products_store_price IS NULL, (((op.final_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust), (((op.orders_products_store_price * sta.supplier_tasks_allocation_ratio_adjust) / 100) + sta.supplier_payable_adjust)) AS payable_price, replace(rtrim(replace(replace(rtrim(replace(sta.supplier_tasks_allocation_ratio_adjust,'0',' ')),' ','0'),'.',' ')),' ','.') AS previous_ratio, 
												sta.supplier_payable_adjust, sta.supplier_tasks_time_reference, sta.supplier_tasks_time_taken, sta.suppliers_id, sta.supplier_firstname, sta.supplier_lastname, sta.supplier_tasks_follow_up_datetime, s.supplier_code, ocp.orders_products_id, ocp.orders_custom_products_value AS task_info, sta.supplier_tasks_start_time, sta.orders_products_id, sta.supplier_tasks_allocation_info, sta.supplier_tasks_allocation_progress, sta.supplier_tasks_status, sta.orders_tag_ids, sta.supplier_tasks_verify_mode, sta.supplier_tasks_billing_status, 
												o.orders_id, op.products_name, op.products_id, op.final_price, op.orders_products_store_price, o.currency, o.currency_value 
											FROM " . TABLE_ORDERS . " AS o 
											INNER JOIN " . TABLE_ORDERS_STATUS . " AS os 
												ON (o.orders_status=os.orders_status_id) 
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
												ON (o.orders_id=op.orders_id AND op.custom_products_type_id = 1) 
											INNER JOIN " . TABLE_ORDERS_CUSTOM_PRODUCTS . " AS ocp 
												ON (op.orders_products_id=ocp.orders_products_id AND ocp.orders_custom_products_key='power_leveling_info') 
											INNER JOIN " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
												ON (op.orders_products_id=sta.orders_products_id) 
											LEFT JOIN " . TABLE_SUPPLIER . " AS s 
												ON (sta.suppliers_id=s.supplier_id) 
											WHERE sta.orders_products_id='" . (int)$orders_product_id . "'";
		
		if (tep_not_null($orders_product_id)) {
			$orders_product_id_str = " AND sta.orders_products_id ='" . (int)$orders_product_id . "'";
		} else {
			$orders_product_id_str = " AND 1 ";
		}
		
		$orders_products_id_select_sql .= $orders_product_id_str;
		
		$orders_products_id_result_sql = tep_db_query($orders_products_id_select_sql);
		
		if (tep_db_num_rows($orders_products_id_result_sql) > 0) {
			while ($orders_products_id_row = tep_db_fetch_array($orders_products_id_result_sql)) {
				$bracket_info = new custom_product();
				$bracket_info_array = $bracket_info->order_product_bracket_info($orders_products_id_row['orders_products_id'], true);
				$cat_cfg_array = tep_get_cfg_setting($orders_products_id_row['products_id'], 'product');
				
				if (sizeof($bracket_info_array) > 0) {
					$total_time = $bracket_info_array['total_time'];
					$time_pass = $bracket_info_array['time_pass'];
					$start_level = $bracket_info_array['start_level'];
					$desired_level = $bracket_info_array['desired_level'];
					$start_level_display = $bracket_info_array['start_level_display'];
					$desired_level_display = $bracket_info_array['desired_level_display'];
					$level_label = $bracket_info_array['level_label'];
					$level_total_unit_bar = $bracket_info_array['level_total_unit_bar'];
				}
?>
				<table width="100%" border="0" cellspacing="0" cellpadding="2">
					<tr>
						<td colspan="2"><span class="pageHeading"><?=sprintf(HEADING_INPUT_TITLE, HEADING_PROGRESS_REPORT)?></span></td>
					</tr>
					<tr>
						<td width="17%">&nbsp;</td>
						<td class="main">
							<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="15%"><?=TEXT_TAG?></td>
									<td class="main"><?=tep_draw_pull_down_menu("s_".$orders_products_id_row['supplier_tasks_status']."_tag_selector", '', '', ' id="s_'.$orders_products_id_row['supplier_tasks_status'].'_tag_selector" onChange="orderListsOptions(this, \''.$orders_products_id_row['supplier_tasks_status'].'\', \''.(int)$languages_id.'\', \''.$orders_product_id.'\');"')?></td>
									<td class="smallText" nowrap>&nbsp;&nbsp;
<?
				$the_orders_tag_ids = $orders_products_id_row["orders_tag_ids"];
				$tags_str = '';
				if (tep_not_null($the_orders_tag_ids)) {
					$orders_assigned_tag_select_sql = "SELECT orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET(orders_tag_id, '".$the_orders_tag_ids."') AND filename='".FILENAME_PROGRESS_REPORT."';";
					$orders_assigned_tag_result_sql = tep_db_query($orders_assigned_tag_select_sql);
					while ($orders_assigned_tag_row = tep_db_fetch_array($orders_assigned_tag_result_sql)) {
						$tags_str .= $orders_assigned_tag_row["orders_tag_name"] . ', ';
					}
					if (substr($tags_str, -2) == ', ') 	$tags_str = substr($tags_str, 0, -2);
				}
				echo '			<span class="greenIndicator" id="tag_'.$orders_product_id.'">'.$tags_str.'</span>';
?>
									</td>
									<td align="right" width="70%" class="main">
<?
				if ($orders_products_id_row['supplier_tasks_status'] == 4) {
					if ($verify_pwl_order_permission) {
						echo tep_draw_form('cp_order_verify_form', FILENAME_PROGRESS_REPORT, tep_get_all_get_params(array('action')) . 'action=verify_order&v_mode='.($orders_products_id_row['supplier_tasks_verify_mode'] ? '0' : '1'), 'POST', '');
						
						if ($orders_products_id_row['supplier_tasks_verify_mode']) {
							echo tep_submit_button('Mark as Unverified', 'Mark as Unverify', '', 'inputButton');
						} else {
							echo tep_submit_button('Mark as Verified', 'Mark as Verified', '', 'inputButton');
						}
			  			echo '</form>';
			  		}
				}
?>
									</td>
								</tr>
<?
				$profiler_url = tep_get_profiler_link($orders_products_id_row['orders_products_id']);
				$view_trade_mail_log_permission = tep_admin_check_boxes(FILENAME_TRADE_MAIL_LOG, 'sub_boxes');
				
				$currency_array = array();
?>
								<tr>
									<td class="main"><?=TEXT_CHARACTER_INFO?></td>
									<td class="main" colspan="3">
										<table border="0" cellspacing="0" cellpadding="0" width="60%">
											<tr>
<?				if (tep_not_null($profiler_url)) { ?>
												<td class="main"><?=$profiler_url?></td>
<?
				}
				
				if ($view_trade_mail_log_permission) {
					$currency_array[] = array('currency' => 'Golds', 'value' => '10000');
					$currency_array[] = array('currency' => 'Silvers', 'value' => '100');
					$currency_array[] = array('currency' => 'Coppers', 'value' => '1');
?>
												<td class="main">
<?
													$currencies_receive_result_array = tep_generate_game_currencies(tep_get_trade_mail_log($orders_products_id_row["orders_id"], 'currency', 'receive'), $currency_array, "Golds");
													
													echo '<a href="' . tep_href_link(FILENAME_TRADE_MAIL_LOG, "action=search&search=search&trade_mail_log_send_receive=2&trade_mail_log_gold_item=1&trade_mail_log_from=Supplier&trade_mail_log_order_id=".$orders_products_id_row["orders_id"]) . '">' . TEXT_GOLD_IN . ' (' . $currencies_receive_result_array[0]['Golds'] . ') </a>';
?>
												</td>
												<td class="main">
<?
													$currencies_send_result_array = tep_generate_game_currencies(tep_get_trade_mail_log($orders_products_id_row["orders_id"], 'currency', 'send'), $currency_array, "Golds");
													
													echo '<a href="' . tep_href_link(FILENAME_TRADE_MAIL_LOG, "action=search&search=search&trade_mail_log_send_receive=1&trade_mail_log_gold_item=1&trade_mail_log_from=Supplier&trade_mail_log_order_id=".$orders_products_id_row["orders_id"]) . '">' . TEXT_GOLD_OUT . ' (' . $currencies_send_result_array[0]['Golds'] . ') </a>';
?>
												</td>
												<td class="main">
<?
													echo '<a href="' . tep_href_link(FILENAME_TRADE_MAIL_LOG, "action=search&search=search&trade_mail_log_send_receive=2&trade_mail_log_gold_item=2&trade_mail_log_from=Supplier&trade_mail_log_order_id=".$orders_products_id_row["orders_id"]) . '">' . TEXT_ITEM_IN . ' (' . tep_get_trade_mail_log($orders_products_id_row["orders_id"], 'item', 'receive') . ') </a>';
?>
												</td>
												<td class="main">
<?
													echo '<a href="' . tep_href_link(FILENAME_TRADE_MAIL_LOG, "action=search&search=search&trade_mail_log_send_receive=1&trade_mail_log_gold_item=2&trade_mail_log_from=Supplier&trade_mail_log_order_id=".$orders_products_id_row["orders_id"]) . '">' . TEXT_ITEM_OUT . ' (' . tep_get_trade_mail_log($orders_products_id_row["orders_id"], 'item', 'send') . ') </a>';
?>
												</td>
<?				} ?>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
					</tr>
					<tr>
						<? echo tep_draw_form('update_task_info', FILENAME_PROGRESS_REPORT, tep_get_all_get_params(array('action')) . 'action=report', 'post'); ?>
						<td width="17%">&nbsp;</td>
						<td align="left">
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td>
										<table border="0" cellspacing="0" cellpadding="3" bgcolor="#F0F0FF">
											<tr bgcolor="#D3DCE3" rowspan="2">
												<td colspan="3" class="ordersBoxHeading">
												<?
													echo ENTRY_HEADING_ORDER_ID . ':&nbsp;<a href="' . tep_href_link("orders.php", "oID=".$orders_products_id_row["orders_id"]."&action=edit", "NONSSL") . '">' . $orders_products_id_row['orders_id'] . '</a>' . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
													echo TABLE_HEADING_PRODUCT_NAME . ':&nbsp;' . $orders_products_id_row['products_name'] . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
													if ($orders_products_id_row['suppliers_id'] == 0) {
														echo '(' . TEXT_NOT_ASSIGN_TO_SUPPLIER . ')';
													} else {
														echo (tep_not_null($orders_products_id_row['supplier_code']) ? '(' . $orders_products_id_row['supplier_code'] . ')' : '(' . $orders_products_id_row['supplier_firstname'] . ' ' . $orders_products_id_row['supplier_lastname'] . ')');
													}
												?>
												</td>
											</tr>
											<tr>
												<td valign="top">
													<table border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
														<tr>
															<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
														</tr>
														<tr>
															<td colspan="3" class="invoiceRecordsRightBorder" align="center">
																<?=TEXT_HOURS_PASS .(int)$time_pass?>
															</td>
														</tr>
														<tr>
<?				if (tep_not_null($total_time) && $total_time != 0) { ?>
												<td align="right" class="invoiceRecordsRightBorder" nowrap>
													<?=tep_draw_separator('pixel_trans.gif', '61', '1') . '<br>'?>
													<?='0 ' . TEXT_HOURS?>
												</td>
												<td>
													<table border="1" cellspacing="0" cellpadding="0" align="center" bordercolor="black" class="bar_border" width="96%">
														<tr>
															<td bgcolor="white">
																<table border="0" cellspacing="1" cellpadding="1" align="left">
																	<tr>
																		<?
																		$time_bar_unit = 20 / $total_time;
																		$total_bar_unit = ($time_pass * $time_bar_unit);
																			
																		for ($unit_count=0; $unit_count < 20; $unit_count++) {
																			echo '<td class="main" valign="middle">';
																			echo '<div class="'.($unit_count < $total_bar_unit ? 'bar_time' : 'bar_time_empty').'">'.'&nbsp;'.'</div>';
																			echo '</td>';
																		}
																		?>
																	</tr>
																</table>
															</td>
														</tr>
													</table>
												</td>
												<td align="left" class="invoiceRecordsRightBorder" nowrap>
													<?=tep_draw_separator('pixel_trans.gif', '61', '1') . '<br>'?>
													<?=$total_time . ' ' . TEXT_HOURS;?>
												</td>
<?				} else { ?>
															<td align="center" class="invoiceRecordsRightBorder" colspan="3">
																<?=TEXT_PROGRESS_BAR_NOT_AVAILABLE?><br>
																<?=tep_draw_separator('pixel_trans.gif', '320', '1') . '<br>'?>
															</td>
<?				} ?>
														</tr>
														<tr>
															<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
														</tr>
														<tr>
															<td colspan="3" class="invoiceRecordsRightBorder" align="center">
																<?=TEXT_CURRENT_LEVEL . $orders_products_id_row['supplier_tasks_allocation_progress'] . '&nbsp;'?>
															</td>
														</tr>
														<tr>
<?						if (tep_not_null($desired_level)) { ?>
															<td align="right" class="invoiceRecordsRightBorder">
																<?=$start_level_display . ' ' . $level_label?>
															</td>
															<td>
																<table border="1" cellspacing="0" cellpadding="0" align="center" bordercolor="black" class="bar_border" width="96%">
																	<tr>
																		<td bgcolor="white">
																			<table border="0" cellspacing="1" cellpadding="1" align="left">
																				<tr>
																					<?
																						for ($unit_count=0; $unit_count < 20; $unit_count++) {
																							echo '<td class="main" valign="middle">';
																							echo '	<div class="'.($unit_count < $level_total_unit_bar ? 'bar_alert_red' : 'bar_time_empty').'">'.'&nbsp;'.'</div>';
																							echo '</td>';
																						}
																					?>
																				</tr>
																			</table>
																		</td>
																	</tr>
																</table>
															</td>
															<td align="left" class="invoiceRecordsRightBorder" nowrap>
																<?=$desired_level_display  . ' ' . $level_label?>
															</td>
<?						} else { ?>
															<td align="center" class="invoiceRecordsRightBorder" colspan="3" nowrap><?=TEXT_PROGRESS_BAR_NOT_AVAILABLE?></td>
<?						} ?>
														</tr>
														<tr>
															<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
														</tr>
<?
				if ($orders_products_id_row['supplier_tasks_status'] != 4 && $orders_products_id_row['supplier_tasks_status'] != 5 && $orders_products_id_row['supplier_tasks_status'] != 0) {
					$date_added_select_sql = "	SELECT date_added, comments, user_role, supplier_tasks_allocation_history_show 
												FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY . " 
												WHERE orders_products_id='" . (int)$orders_product_id . "' 
													AND comments REGEXP \"([0-9a-zA-Z]+ )(\-\-\>)( [0-9a-zA-Z]+)\" 
													AND user_role = 'supplier' 
												ORDER BY date_added DESC LIMIT 1";
					$date_added_result_sql = tep_db_query($date_added_select_sql);
					$date_added_row = tep_db_fetch_array($date_added_result_sql);
?>
											
														<tr>
															<td colspan="3" align="center">
																<table border="0" cellspacing="0" cellpadding="0" width="72%">
																	<tr>
																		<td class="invoiceRecordsRightBorder" width="38%"><b><?=TEXT_LAST_UPDATE?></b></td>
																		<td class="invoiceRecordsRightBorder"><?=(tep_not_null($date_added_row['date_added']) ? $date_added_row['date_added'] : TEXT_NOT_AVAILABLE)?></td>
																	</tr>
<?
					if (tep_not_null($date_added_row['date_added'])) {
						$duration_show_style = array();
						$duration_show_style[] = array('time' => 0, 'color' => "blackIndicator");
						$duration_show_style[] = array('time' => CP_PROGRESS_ALERT_TIME, 'color' => "redIndicator");
?>
																	<tr>
																		<td class="invoiceRecordsRightBorder"><b><?=TEXT_DURATION?></b></td>
																		<td class="invoiceRecordsRightBorder"><?=tep_string_output(tep_datetime_to_string(tep_day_diff($date_added_row['date_added'], date('Y-m-d H:i:s'), 'sec')), (int)(tep_day_diff($date_added_row['date_added'], date('Y-m-d H:i:s'), 'sec') / 60), $duration_show_style)?></td>
																	</tr>
<?					} ?>
																</table>
															</td>
														</tr>
														<tr>
															<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
														</tr>
<?				} ?>
													</table>
												</td>
												<td valign="top">
													<table border="0" cellspacing="0" cellpadding="0" align="center">
														<tr>
															<td class="invoiceRecordsRightBorder">
															<?
																if ($view_pwl_details_permission || $view_sensitive_pwl_details_permission) {
																	$customer_product_info_array = explode("\n", $orders_products_id_row['task_info']);
																	$info_display = '';
																	$info_display_array = array();
																	$info_display_count = 0;
																	
																	foreach ($customer_product_info_array as $content => $content_value) {
																		$content_value = trim($content_value);
																		
																		$pattern = '/(##1##)$/i';
																		$replacement = '';
																		if (preg_match($pattern, $content_value)) {
																			$customer_product_info_array[$content] = preg_replace($pattern, $replacement, $content_value) . "\n";
																			$info_display = $customer_product_info_array[$content];
																			$info_display_array[$info_display_count][] = $info_display;
																		}
																		if (preg_match('/\d{4}\-\d{2}\-\d{2}(\s*?)\d{2}\:\d{2}\:\d{2} \(by/', $content_value) && !preg_match('/(---------)/', $content_value) && tep_not_null($content_value)) {
																			if (tep_not_null($info_display)) {
																				$info_display_count++;
																				$info_display = "";
																			}
																		}
																	}
																	
																	if (tep_not_null($orders_products_id_row['supplier_tasks_allocation_info'])) {
																		echo '----------------------' . '<br>';
																		echo nl2br($orders_products_id_row['supplier_tasks_allocation_info']);
																		echo '<br>' . '----------------------' . '<br><br>';
																	}
																	
																	for ($count=0; $count < count($info_display_array); $count++) {
																		$info_display_final = implode("<br>", $info_display_array[$count]);
																		echo ($count == (count($info_display_array)-1) ? "<b>" : "") . $info_display_final . ($count == (count($info_display_array)-1) ? "</b>" : "");
																		echo "<br>";
																	}
																	
																	if ($view_sensitive_pwl_details_permission) {
																		// Display account name, account password, character name, faction & realm is exist
																		$account_non_general_info_array = tep_get_custom_product_info_option_class($orders_products_id_row['orders_products_id']);
																		
																		foreach ($account_non_general_info_array as $key => $info_array) {
																			switch($key) {
																				case 'account_password': // Only display where it is not in paid & completed status
																					if ($orders_products_id_row['supplier_tasks_billing_status'] == 0) {
																						if ($view_clear_text_password_permission) {
																							echo $info_array['label'] . ': ' . $info_array['value'] . '<br>';
																						} else {
																							echo $info_array['label'] . ': ' . 'xxxxxx' . '<br>';
																						}
																					}
																					break;
																				case 'game':
																					// WOW game list
																					$wow_game_list_array[''] = 'Not Selected';
																					$wow_game_list_array[2299] = 'World of Warcraft US';
																					$wow_game_list_array[2522] = 'World of Warcraft EU';
																					
																					echo $info_array['label'] . ': ' . $wow_game_list_array[$info_array['value']] . '<br>';
																					break;
																				default:
																					echo $info_array['label'] . ': ' . $info_array['value'] . '<br>';
																					break;
																			}
																		}
																	}
																} else {
																	echo '&nbsp;';	
																}
															?>
															</td>
														</tr>
													</table>
												</td>
												<td>
													<table border="0" cellspacing="0" cellpadding="0" align="center">
														<tr>
															<td class="invoiceRecordsRightBorder" nowrap>
<?
																echo tep_draw_separator('pixel_trans.gif', '130', '1') . '<br>';
																
																$task_status_array = array();
																
																if ($orders_products_id_row['supplier_tasks_status'] == 0) {
																	echo '<center>' . $status_name[$orders_products_id_row['supplier_tasks_status']] . '</center>';
																} else if ($orders_products_id_row['supplier_tasks_status'] == 1) {
																	$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
																	$task_status_array[] = array('id' => 1, 'text' => $status_name[1]);
																	$task_status_array[] = array('id' => 2, 'text' => $status_name[2]);
																} else if ($orders_products_id_row['supplier_tasks_status'] == 2) {
																	$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
																	
																	if (tep_check_status_update_permission('PWL', $login_groups_id, 2, 1)) {
																		$task_status_array[] = array('id' => 1, 'text' => $status_name[1]);
																	}
																	
																	$task_status_array[] = array('id' => 2, 'text' => $status_name[2]);
																	
																	if (tep_check_status_update_permission('PWL', $login_groups_id, 2, 4)) {
																		$task_status_array[] = array('id' => 4, 'text' => $status_name[4]);
																	}
																	
																	if (count($task_status_array) <= 2) {
																		$task_status_array = array();
																		echo '<center>'.$status_name[$orders_products_id_row['supplier_tasks_status']] . '</center>';
																	}
																} else if ($orders_products_id_row['supplier_tasks_status'] == 3) {
																	$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
																	
																	if (tep_check_status_update_permission('PWL', $login_groups_id, 3, 1)) {
																		$task_status_array[] = array('id' => 1, 'text' => $status_name[1]);
																	}
																	
																	if (tep_check_status_update_permission('PWL', $login_groups_id, 3, 2)) {
																		$task_status_array[] = array('id' => 2, 'text' => $status_name[2]);
																	}
																	
																	$task_status_array[] = array('id' => 3, 'text' => $status_name[3]);
																	
																	if (count($task_status_array) <= 2) {
																		$task_status_array = array();
																		echo $status_name[$orders_products_id_row['supplier_tasks_status']];
																	}
																} else if ($orders_products_id_row['supplier_tasks_status'] == 4) {
																	if ($orders_products_id_row['supplier_tasks_billing_status'] == 0) {
																		$cron_pending_credit_trans_select_sql = "	SELECT cron_pending_credit_trans_completed_date, cron_pending_credit_mature_period 
																													FROM " . TABLE_CRON_PENDING_CREDIT. " 
																													WHERE cron_pending_credit_trans_type = 'PWL' 
																														AND cron_pending_credit_trans_id ='" . (int)$orders_products_id_row['orders_products_id'] . "' 
																														AND NOW() < DATE_SUB(DATE_ADD(cron_pending_credit_trans_completed_date, INTERVAL CAST(cron_pending_credit_mature_period AS UNSIGNED) MINUTE), INTERVAL 1 HOUR) ";
																		$cron_pending_credit_trans_result_sql = tep_db_query($cron_pending_credit_trans_select_sql);
																		
																		if (tep_db_num_rows($cron_pending_credit_trans_result_sql) > 0) {
																			if (tep_check_status_update_permission('PWL', $login_groups_id, $orders_products_id_row['supplier_tasks_status'], 2)) {
																				$task_status_array[] = array('id' => '', 'text' => TEXT_CHANGE_STATUS);
																				$task_status_array[] = array('id' => $orders_products_id_row['supplier_tasks_status'], 'text' => $status_name[$orders_products_id_row['supplier_tasks_status']]);
																				$task_status_array[] = array('id' => 2, 'text' => $status_name[2]);
																			} else {
																				echo '<center>'. $status_name[$orders_products_id_row['supplier_tasks_status']] . '</center>';
																			}
																		} else {
																			echo '<center>'. $status_name[$orders_products_id_row['supplier_tasks_status']] . '</center>';
																		}
																	} else {
																		echo '<center>'. $status_name[$orders_products_id_row['supplier_tasks_status']] . '</center>';
																	}
																} else if ($orders_products_id_row['supplier_tasks_status'] == 5) {
																	echo '<center>' . $status_name[$orders_products_id_row['supplier_tasks_status']] . '</center>';
																}
																
																if (sizeof($task_status_array) > 0) {
																	echo '<center>' . tep_draw_pull_down_menu('task_status['.$orders_products_id_row['orders_products_id'].']', $task_status_array, $orders_products_id_row['supplier_tasks_status'],' id="status_now" onChange="return confirm_update_status(\''.$orders_products_id_row['supplier_tasks_status'].'\');"') . '</center>';
																	echo tep_draw_hidden_field('tasks_time_taken['.$orders_products_id_row['orders_products_id'].']', $orders_products_id_row['supplier_tasks_time_taken']);
																}
?>
															</td>
														</tr>
														<tr>
															<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
														</tr>
														<tr>
															<td class="main"><div id="destination_tag_selector_div" class="hide"><?=tep_draw_pull_down_menu("new_status_tag", array(array('id' => '0', 'text' => 'Apply tag:')), '', 'id="new_status_tag" disabled')?></div></td>
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</td>
									<td valign="top">
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td align="right" valign="top">
													<b><?=ENTRY_PWL_FOLLOW_UP_DATETIME?></b>
													<?=tep_draw_input_field('follow_up_date', $orders_products_id_row['supplier_tasks_follow_up_datetime'], 'id="follow_up_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.getElementById(\'follow_up_date\')); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.getElementById(\'follow_up_date\'));return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?><br><br>
													<?=tep_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, '', ' name="OrderFollowUpBtn" onClick="updateFollowUpDate(\''.$orders_product_id.'\', \'follow_up_date\', this);" ', 'inputButton')?>
												</td>
											</tr>
											<tr>
												<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>		
					</tr>
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
				if ($orders_products_id_row['supplier_tasks_billing_status'] == 0 && $orders_products_id_row['supplier_tasks_status'] != 0) {
					$received_qty_adjust_array = array( array('id'=>"+", 'text'=>"+"),
														array('id'=>"-", 'text'=>"-")
														);
?>
					<tr>
						<td>&nbsp;</td>
						<td align="left">
							<table width="75%" border="0" cellspacing="1" cellpadding="2">
								<tr>
									<td class="ordersBoxHeading" align="center" nowrap="nowrap"><?=TABLE_HEADING_TOTAL?></td>
<?						if ( ($orders_products_id_row['supplier_tasks_status'] == 4 && $adjust_completed_prod_payable_amt_permission) || 
							 ($orders_products_id_row['supplier_tasks_status'] != 4 && $supplier_cp_payable_manual_adjust) ) { ?>
									<td class="ordersBoxHeading" align="center" nowrap="nowrap"><?=TABLE_HEADING_RATIO?></td>
									<td class="ordersBoxHeading" align="center" nowrap="nowrap"><?=TABLE_HEADING_AMOUNT_ADJUST?></td>
									<td class="ordersBoxHeading" align="center" nowrap="nowrap" ><?=TABLE_HEADING_TOTAL_PAYABLE?></td>
<?						} ?>
								</tr>
								<tr class="ordersListingOdd">
									<td class="ordersRecords" align="center"><?=$currencies->format(tep_not_null($orders_products_id_row['orders_products_store_price']) ? $orders_products_id_row['orders_products_store_price'] : $orders_products_id_row['final_price'], true, $orders_products_id_row['currency'], $orders_products_id_row['currency_value'])?></td>
<?						if ( ($orders_products_id_row['supplier_tasks_status'] == 4 && $adjust_completed_prod_payable_amt_permission) || 
							 ($orders_products_id_row['supplier_tasks_status'] != 4 && $supplier_cp_payable_manual_adjust)) { ?>
									<td class="ordersRecords" align="center"><?=tep_draw_input_field('adjust_ratio['.$orders_products_id_row['orders_products_id'].']',$orders_products_id_row['previous_ratio'], 'size="5" id="adjust_ratio_'.$orders_products_id_row['orders_products_id'].'" onKeyUp="checkRatio(\''.$orders_products_id_row['orders_products_id'].'\')" onKeyPress="return noEnterKey(event)"')?>%</td>
									<td class="ordersRecords" align="center">
									<?
									echo tep_draw_pull_down_menu('adjust_sign['.$orders_products_id_row['orders_products_id'].']', $received_qty_adjust_array, (tep_not_null($orders_products_id_row['supplier_payable_adjust']) ? (substr($orders_products_id_row['supplier_payable_adjust'], 0, 1) == '-' ? '-' : '+' ) : '-'));
									echo tep_draw_input_field('adjust_price['.$orders_products_id_row['orders_products_id'].']', (substr($orders_products_id_row['supplier_payable_adjust'], 0, 1) == '-' ? substr($orders_products_id_row['supplier_payable_adjust'], 1) : $orders_products_id_row['supplier_payable_adjust']), 'size="6"');
									?>
									</td>
									<td class="ordersRecords" align="center">
										<?=$currencies->format($orders_products_id_row['payable_price'])?>
									</td>
<?						} ?>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
				} else if ($orders_products_id_row['supplier_tasks_billing_status'] > 0) {
					if ($supplier_cp_payment_detail_permission) {
						$custom_product_payments_array = custom_product_payment::get_order_paid_history($orders_products_id_row['orders_products_id'], false);
?>
					<tr>
						<td>&nbsp;</td>
						<td align="left">
							<table border="0" width="50%" cellspacing="1" cellpadding="2">
								<tr>
									<td class="ordersBoxHeading" align="right" nowrap="nowrap" width="12%"><?=TABLE_HEADING_TOTAL?></td>
									<td class="ordersBoxHeading" align="center" nowrap="nowrap" width="12%"><?=TABLE_HEADING_RATIO?></td>
									<td class="ordersBoxHeading" align="right" nowrap="nowrap" width="14%"><?=TABLE_HEADING_AMOUNT_ADJUST?></td>
									<td class="ordersBoxHeading" align="right" nowrap="nowrap" width="12%"><?=TABLE_HEADING_TOTAL_PAYABLE?></td>
								</tr>
								<tr class="ordersListingOdd">
									<td class="ordersRecords" align="right"><?=$currencies->format(tep_not_null($orders_products_id_row['orders_products_store_price']) ? $orders_products_id_row['orders_products_store_price'] : $orders_products_id_row['final_price'], true, $orders_products_id_row['currency'], $orders_products_id_row['currency_value'])?></td>
									<td class="ordersRecords" align="center"><?=$orders_products_id_row['previous_ratio'] . '%'?></td>
									<td class="ordersRecords" align="right"><?=$orders_products_id_row['supplier_payable_adjust']?></td>
									<td class="ordersRecords" align="right">
										<?=$currencies->format($orders_products_id_row['payable_price'])?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
					}
				}
				
				$supplier_tasks_allocation_history_select_sql = "	SELECT supplier_tasks_allocation_history_id, supplier_tasks_status, date_added, comments, user_role, changed_by, supplier_tasks_allocation_history_show, supplier_code 
																	FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY . " AS stah 
																	LEFT JOIN " . TABLE_SUPPLIER . " AS s 
																		ON (stah.changed_by = s.supplier_email_address AND stah.user_role = 'supplier') 
																	WHERE orders_products_id='" . (int)$orders_product_id . "' ORDER BY date_added";
				$supplier_tasks_allocation_history_result_sql = tep_db_query($supplier_tasks_allocation_history_select_sql);
?>
					<tr>
						<td>&nbsp;</td>
						<td align="left">
							<table border="1" width="80%" cellspacing="0" cellpadding="5">
								<tr>
									<td class="smallText" width="13%"><b><?=TABLE_HEADING_DATE_ADDED?></b></td>
									<td class="smallText" align="center" width="13%"><b><?=TABLE_HEADING_STATUS?></b></td>
									<td class="smallText" align="center"><b><?=TABLE_HEADING_COMMENTS?></b></td>
									<td class="smallText" align="center" width="19%"><b><?=TABLE_HEADING_MODIFIED_BY?></b></td>
									<td class="smallText" align="center" width="10%"><b><?=TABLE_HEADING_SHOW_TO_SUPPLIER?></b></td>
								</tr>
<?
								while ($supplier_tasks_allocation_history_row = tep_db_fetch_array($supplier_tasks_allocation_history_result_sql)) {
									if ($supplier_tasks_allocation_history_row["user_role"] == 'supplier') {
										preg_match('/([0-9a-zA-Z]+)(@)([0-9a-zA-Z]+)(.com)?/', $supplier_tasks_allocation_history_row["changed_by"], $email_array);
										$modifled_by = $email_array[1];
									} else if ($supplier_tasks_allocation_history_row["user_role"] == 'admin') {
										$modifled_by = $supplier_tasks_allocation_history_row["changed_by"];
									}
									
									if ((preg_match('/(Ratio Overwrite:\s)([0-9]+)(\.[0-9]+)?(\%\s\-\>\s)([0-9]+)(\.[0-9]+)?(\%)/', $supplier_tasks_allocation_history_row["comments"]) || preg_match('/(Amount Adjust:\s)(\-)?([0-9]+)(\.[0-9]+)?(\s\-\>\s)(\-)?([0-9]+)(\.[0-9]+)?/', $supplier_tasks_allocation_history_row["comments"])) && $supplier_tasks_allocation_history_row["user_role"] == 'admin') {
										$comments_table_row_style = "orderRemarkSelectedRow";
									} else {
										$comments_table_row_style = "";
									}
?>
									<tr class="<?=$comments_table_row_style?>">
										<td class="smallText"><?=$supplier_tasks_allocation_history_row["date_added"]?></td>
										<td class="smallText"><?=(tep_not_null($supplier_tasks_allocation_history_row["supplier_tasks_status"]) ? $status_name[$supplier_tasks_allocation_history_row["supplier_tasks_status"]] : '--')?></td>
										<td class="smallText">
<?
											if (tep_not_null($supplier_tasks_allocation_history_row["comments"])) {
												echo tep_get_cp_password($orders_products_id_row['products_id'], $supplier_tasks_allocation_history_row["comments"], $view_clear_text_password_permission);
											} else {
												echo '&nbsp;';	
											}
?>
										</td>
										<td class="smallText" nowrap><?=(tep_not_null($supplier_tasks_allocation_history_row["supplier_code"]) ? $supplier_tasks_allocation_history_row["supplier_code"] : $modifled_by)?></td>
										<td class="smallText" align="center"><?=tep_draw_checkbox_field('show_supplier['.$orders_products_id_row['orders_products_id'].']['.$supplier_tasks_allocation_history_row['supplier_tasks_allocation_history_id'].']', 1, ($supplier_tasks_allocation_history_row["supplier_tasks_allocation_history_show"] == 1) ? true : false)?></td>
									</tr>
<?
								}
?>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td>&nbsp;</td>
						<td align="left">
							<table border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td colspan="2"><?=tep_draw_comment_select('', FILENAME_PROGRESS_REPORT)?></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td><?=tep_draw_textarea_field('comments['.$orders_products_id_row['orders_products_id'].']', 'soft', '60', '5', '', 'id="comments['.$orders_products_id_row['orders_products_id'].']"')?></td>
									<td class="main" valign="bottom"><?=tep_draw_checkbox_field('show_supplier_latest['.$orders_products_id_row['orders_products_id'].']', 1, true). "&nbsp;" . ENTRY_SHOW_SUPPLIER?></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td align="right"><?=tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="post_comment['.$orders_products_id_row['orders_products_id'].']"', 'inputButton')?></td>
									<td>&nbsp;</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
					<script language="javascript"><!--
						refreshOrderListsOptions('<?="s_".$orders_products_id_row["supplier_tasks_status"]?>', '<?=(int)$languages_id?>', '<?=$orders_products_id_row["orders_products_id"]?>');
					//--></script>
<?
			}
		} else {
			$custom_products_type_id_select_sql = "	SELECT custom_products_type_id FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_products_id ='" . (int)$orders_product_id . "'";
			$custom_products_type_id_result_sql = tep_db_query($custom_products_type_id_select_sql);
			$custom_products_type_id_row = tep_db_fetch_array($custom_products_type_id_result_sql);
?>
					<table border="0" cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<td><span class="pageHeading"><?=sprintf(HEADING_INPUT_TITLE, HEADING_PROGRESS_REPORT)?></span></td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						</tr>
						<tr>
							<td class="main"><?=(($custom_products_type_id_row['custom_products_type_id'] == 1) ? TEXT_NOT_ASSIGN_TO_SUPPLIER : TEXT_ORDER_NOT_FOUND)?></td>
						</tr>
						<tr>
							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '390')?></td>
						</tr>
					</table>
<?		} ?>
				</form>
<?
	} else if ($_REQUEST['action'] == 'advanced_search') {
		$customer_group_list_array = array();
		$customer_group_list_array[] = array('id' => '' , 'text' => 'Please select member group');
		
		$customers_groups_select_sql = "SELECT customers_groups_id, customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS;
		$customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
		while ($customers_groups_row = tep_db_fetch_array($customers_groups_result_sql)) {
			$customer_group_list_array[] = array('id' => $customers_groups_row['customers_groups_id'], 'text' => $customers_groups_row['customers_groups_name']);
		}
		
		$supplier_list_array = array();
		$supplier_list_array[] = array('id' => '', 'text' => 'Please select supplier');
		
		$suppliers_id_select_sql = "SELECT DISTINCT sta.suppliers_id, sta.supplier_firstname, sta.supplier_lastname, s.supplier_code FROM " . TABLE_SUPPLIER_TASKS_ALLOCATION . " AS sta 
									LEFT JOIN " . TABLE_SUPPLIER. " AS s 
										ON (s.supplier_id = sta.suppliers_id) 
									WHERE suppliers_id != 0";
		
		$suppliers_id_result_sql = tep_db_query($suppliers_id_select_sql);
		while ($suppliers_id_row = tep_db_fetch_array($suppliers_id_result_sql)) {
			$supplier_list_array[] = array('id' => $suppliers_id_row['suppliers_id'], 'text' => (tep_not_null($suppliers_id_row['supplier_code']) ? $suppliers_id_row['supplier_code'] : $suppliers_id_row['supplier_firstname'] . ' ' . $suppliers_id_row['supplier_lastname']));
		}
?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
				</tr>
				<tr>
					<td>
<?						echo tep_draw_form('custom_product_criteria', FILENAME_PROGRESS_REPORT, 'action=adv_search', 'post'); ?>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td colspan="2"><span class="pageHeading"><?=sprintf(HEADING_INPUT_TITLE, HEADING_ADVANCED_SEARCH)?></span></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td width="11%">
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" width="12%"><?=ENTRY_HEADING_ORDER_PRODUCT_SUBMIT_START_DATE?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap width="20%"><?=tep_draw_input_field('custom_product_start_date', $_SESSION['custom_product_param']["custom_product_start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.custom_product_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.custom_product_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
												<td class="main" width="12%"><?=ENTRY_HEADING_ORDER_PRODUCT_SUBMIT_END_DATE?></td>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('custom_product_end_date', $_SESSION['custom_product_param']["custom_product_end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.custom_product_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.custom_product_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main"><?=ENTRY_HEADING_ORDER_ID?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('custom_product_order_id', $_SESSION['custom_product_param']["custom_product_order_id"], ' id="custom_product_order_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main"><?=ENTRY_CATEGORY?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap>
													<?=tep_draw_pull_down_menu("cat_id", tep_get_category_tree(0, '___'), $_SESSION['custom_product_param']["cat_id"], ' id="cat_id" ')?>
					    							<?=tep_draw_checkbox_field("include_subcategory", 1, isset($_SESSION['custom_product_param']["include_subcategory"]) && $_SESSION['custom_product_param']["include_subcategory"] == 0 ? false : true, '', ' id="include_subcategory" ') . '&nbsp;' . TEXT_INCLUDE_SUBCATEGORY?>	
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" width="43%"><?=ENTRY_HEADING_PRODUCT_NAME?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('custom_product_product_name', $_SESSION['custom_product_param']["custom_product_product_name"], ' id="custom_product_product_name" size="26" onKeyUP="if (trim_str(this.value)!=\'\') { resetControls(this); }" onBlur="if (trim_str(this.value)!=\'\') { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main"><?=ENTRY_MEMBER_GROUP?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap><?=tep_draw_pull_down_menu('customer_member_group', $customer_group_list_array, $_SESSION['custom_product_param']["customer_member_group"], 'id="customer_member_group"')?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" width="45%"><?=ENTRY_HEADING_SUPPLIER?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap>
													<?=tep_draw_input_field('custom_product_supplier_code', $_SESSION['custom_product_param']["custom_product_supplier_code"], ' id="custom_product_supplier_code" size="30" ')?>
													<?=tep_draw_hidden_field('custom_product_supplier', $_SESSION['custom_product_param']["custom_product_supplier"], 'id="custom_product_supplier"')?>
						    						<a href="javascript:;" onClick="searchSuppliersPopUp('custom_product_supplier_code', 'custom_product_supplier')"><?=tep_image(DIR_WS_ICONS . 'search.jpg', IMAGE_ICON_SEARCH, 16, 16)?></a>
													<?='&nbsp;Or&nbsp;' . tep_draw_checkbox_field('custom_product_not_assign', 0, (tep_not_null($_SESSION['custom_product_param']["custom_product_not_assign"]) ? true : false), '', ' id="custom_product_not_assign" onClick="resetControls(this);"') . TEXT_NOT_ASSIGN_TO_SUPPLIER?>	
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
<?		if (count($status_name)) { ?>
								<tr>
									<td valign="top">
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" width="13%"><?=ENTRY_HEADING_PROGRESS_STATUS?></td>
											</tr>
										</table>
									</td>
									<td valign="top">
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top">
													<table border="0" cellspacing="2" cellpadding="0" width="100%">
														<tr>
															<td valign="top"><?=tep_draw_checkbox_field('order_status_any', '1', isset($_SESSION['custom_product_param']) && count($_SESSION['custom_product_param']["order_status"]) ? false : true, '', 'id="order_status_any" onClick="set_status_option(this);"')?></td><td class="main" valign="top" colspan="<?=count($status_name)*2?>"><?=TEXT_ANY?></td>
														</tr>
														<tr>
														<?
														foreach ($status_name as $id => $title) {
															$order_status_display_str = '';
															$order_tag_select_sql = "SELECT orders_tag_id, orders_tag_name FROM " . TABLE_ORDERS_TAG . " WHERE FIND_IN_SET('".$id."', orders_tag_status_ids) AND filename='".FILENAME_PROGRESS_REPORT."';";
															$order_tag_result_sql = tep_db_query($order_tag_select_sql);
																
															$order_status_display_str .= 
															'<td class="main" valign="top">'.
																tep_draw_checkbox_field('order_status[]', ''.$id, isset($_SESSION['custom_product_param']) ? (is_array($_SESSION['custom_product_param']["order_status"]) && in_array($id, $_SESSION['custom_product_param']["order_status"]) ? true : false) : (false), '', 'onClick=verify_status_selection();') . '
															</td>
															<td class="main" valign="top">';
															$order_status_display_str .= 
															'	<fieldset class="selectedFieldSet">
																	<legend align=left class=SectionHead>'.$title.'</legend>
																	<table border="0" cellspacing="0" cellpadding="0">
																		<tr><td class="smallText" valign="top">' . tep_draw_checkbox_field('status_'.$id.'[]', 'no_tag', isset($_SESSION['custom_product_param']) ? (is_array($_SESSION['custom_product_param']['status_'.$id]) && in_array('no_tag', $_SESSION['custom_product_param']['status_'.$id]) ? true : false) : (false), '', 'disabled') . '</td><td class="smallText" nowrap>No Tag</td></tr>';
															while ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
																$order_status_display_str .= '<tr><td class="smallText" valign="top" nowrap>' . tep_draw_checkbox_field('status_'.$id.'[]', $order_tag_row["orders_tag_id"], isset($_SESSION['custom_product_param']) ? (is_array($_SESSION['custom_product_param']['status_'.$id]) && in_array($order_tag_row["orders_tag_id"], $_SESSION['custom_product_param']['status_'.$id]) ? true : false) : (false), '', 'disabled') . '</td><td class="smallText">' . $order_tag_row["orders_tag_name"] . '</td></tr>';
															}
															$order_status_display_str .= '
																	</table>
																</fieldset>';
															$order_status_display_str .= '</td>';
															echo $order_status_display_str;
														}
														?>
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
<?		} ?>
								<tr>
									<td colspan="2" align="right">
										<?=tep_submit_button(IMAGE_BUTTON_SEARCH, IMAGE_BUTTON_SEARCH, 'name="search"', 'inputButton')?>
										<?=tep_submit_button(IMAGE_BUTTON_RESET, IMAGE_BUTTON_RESET, 'name="reset"', 'inputButton')?>
									</td>
								</tr>
							</table>
						</form>
					</td>
				</tr>
			</table>
<?
	} else if ($_REQUEST['action'] == 'char_info') {
		require_once(DIR_WS_CLASSES . 'wow/bag.php');
		require_once(DIR_WS_CLASSES . 'wow/char.php');
		require_once(DIR_WS_CLASSES . 'wow/item.php');
		require_once(DIR_WS_CLASSES . 'wow/skill.php');
		require_once(DIR_WS_CLASSES . 'wow/quest.php');
		require_once(DIR_WS_CLASSES . 'wow/bank.php');
		
		$game_char_id = tep_db_prepare_input($_REQUEST['game_char_id']);
		$char_info_date = tep_db_prepare_input($_REQUEST['date']);
		$orders_products_id = 0;
		$character_array = array();
		$compare_date_array = array();
		
		$char_info_select_str = "	SELECT gc.*, gch.game_char_history_id, gch.game_char_id, gch.stat_int, gch.stat_agl, gch.stat_sta, gch.stat_str, gch.stat_spr, gch.guild_name, gch.guild_title, gch.guild_rank, gch.race, gch.res_holy, gch.res_frost, gch.res_arcane, gch.res_fire, gch.res_shadow, gch.res_nature, gch.armor, gch.level, gch.defense, gch.talent_points, gch.money_c, gch.money_s, gch.money_g, gch.exp, gch.class, gch.health, gch.melee_power, gch.melee_rating, gch.melee_range, gch.melee_range_tooltip, gch.melee_power_tooltip, gch.ranged_power, gch.ranged_rating, gch.ranged_range, gch.ranged_range_tooltip, gch.ranged_power_tooltip, gch.version, gch.game_char_history_date 
									FROM " . TABLE_GAME_CHAR . " AS gc 
									INNER JOIN " . TABLE_GAME_CHAR_HISTORY . " AS gch 
										ON (gc.game_char_id = gch.game_char_id) ";
		
		if (tep_not_null($char_info_date)) {
			$char_info_where_str .= " WHERE gch.game_char_history_date ='" . tep_db_input($char_info_date) . "' AND gc.game_char_id ='" . tep_db_input($game_char_id) . "'";
		} else {
			$char_info_where_str .= " WHERE gc.game_char_id='" . tep_db_input($game_char_id) . "' ORDER BY gch.game_char_history_date DESC LIMIT 1";
		}
		
		$char_info_select_sql = $char_info_select_str . $char_info_where_str;
		
		$char_info_result_sql = tep_db_query($char_info_select_sql);
		$char_info_row = tep_db_fetch_array($char_info_result_sql);
		
		$char_info = new char($char_info_row);
		
		$game_char_history_date_select_sql = "	SELECT gch.game_char_history_date, gc.orders_products_id 
												FROM " . TABLE_GAME_CHAR . " AS gc 
												INNER JOIN " . TABLE_GAME_CHAR_HISTORY . " AS gch 
													ON (gc.game_char_id = gch.game_char_id) 
												WHERE gc.game_char_id ='" . tep_db_input($game_char_id) . "' ORDER BY gch.game_char_history_date ASC";
		
		$game_char_history_date_result_sql = tep_db_query($game_char_history_date_select_sql);
		
		if (tep_db_num_rows($game_char_history_date_result_sql) > 0) {
			$profiler_date_count = 1;
			$compare_date_array[] = array('id' => 0, 'text' => 'Please Select');
			
			while ($game_char_history_date_row = tep_db_fetch_array($game_char_history_date_result_sql)) {
				$compare_date_array[] = array('id' => $profiler_date_count, 'text' => $game_char_history_date_row["game_char_history_date"]);
				$profiler_date_count++;
				$orders_products_id = $game_char_history_date_row['orders_products_id'];
			}

		}
		
		$game_char_id_select_sql = "SELECT game_char_id, name FROM " . TABLE_GAME_CHAR . " WHERE orders_products_id ='" . (int)$orders_products_id . "'";
		$game_char_id_result_sql = tep_db_query($game_char_id_select_sql);
		if (tep_db_num_rows($game_char_id_result_sql) > 0) {
			while ($game_char_id_row = tep_db_fetch_array($game_char_id_result_sql)) {
				$character_array[] = array('id' => $game_char_id_row['game_char_id'], 'text' => $game_char_id_row['name']);
			}
		}
?>
			<script language="JavaScript" src="includes/javascript/menu.js"></script>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td>
<?						echo tep_draw_form('compareDate', FILENAME_PROGRESS_REPORT, tep_get_all_get_params(), 'post', ''); ?>
							<table border="0" cellspacing="0" cellpadding="0" width="38%">
								<tr>
									<td class="main"><?=ENTRY_CHARACTER?></td>
									<td><?=tep_draw_pull_down_menu('character', $character_array, $game_char_id, ' id="character" onChange="refreshDynamicSelectOptions(this, \'profiler_compare_div\', \'profiler_compare\', \''.(int)$languages_id.'\', true, \''.(int)$game_char_id.'\');"')?></td>
									<td colspan="2"><?=tep_submit_button('Delete', 'Delete', 'id="delete" onClick="return delete_char();"', 'inputButton');?></td>
								</tr>
								<tr>
									<td colspan="4"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td class="main"><?=ENTRY_DATE?></td>
									<td>
										<div id="profiler_compare_div"><?=tep_draw_pull_down_menu('profiler_compare', $compare_date_array, '',' id="profiler_compare" ')?></div>
									</td>
									<td><?=tep_submit_button('view', 'View', 'id="view" onClick="return view_character_profiler();"', 'inputButton')?></td>
									<td><?=tep_submit_button('Compare', 'Compare', 'id="compare" onClick="return profilerCompare(this.form, \''.$char_info_row['game_char_history_date'].'\', \''.$char_info_row['game_char_id'].'\')";', 'inputButton')?></td>
								</tr>
							</table>
						</form>
					</td>
				</tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '1', '90')?></td>
				</tr>
				<tr>
					<td>
<?			if ($_REQUEST['view_info'] == 'bag_bank') { ?>
						<table border="0" cellspacing="2" cellpadding="0" width="100%">
							<tr>
								<td valign="top" width="35%">
<?
									$bank = bank_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "bank");
									
									if (isset($bank)) {
										$bank->out();
									}
?>
								</td>
								<td valign="top">
<?
									$default_bank_bag_slot = 6;
									$bank_bag_slot = bank_bag_num($char_info_row['game_char_id'], $char_info_row["game_char_history_id"]);
									
									if ($bank_bag_slot > $default_bank_bag_slot) {
										$default_bank_bag_slot = $bank_bag_slot;
									}
									
									for ($bank_bag_count = 1; $bank_bag_count <= $default_bank_bag_slot; $bank_bag_count++) {
										
										$bankbag = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "bankBag".$bank_bag_count);
										
										if (isset($bankbag)) {
											$bankbag_div = "bankBag" . $bank_bag_count . "_div_id";
?>
											<div id="<?=$bankbag_div?>" class="hide"><?=$bankbag->out()?></div>
<?
										}
									}
?>
									</div>
								</td>
							</tr>
						</table>
						<table border="0" cellspacing="2" cellpadding="0">
							<tr>
								<td valign="top">
<?
									$bag0 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag0");
									if (isset($bag0)) {
										$bag0->out();
									}
?>
								</td>
								<td valign="top">
<?
									$bag1 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag1");
									if (isset($bag1)) {
										$bag1->out();
									}
?>
								</td>
								<td valign="top">
<?
									$bag2 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag2");
									if (isset($bag2)) {
										$bag2->out();
									}
?>
								</td>
								<td valign="top">
<?
									$bag3 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag3");
									if (isset($bag3)) {
										$bag3->out();
									}
?>
								</td>
								<td valign="top">
<?
									$bag4 = bag_get($char_info_row['game_char_id'], "", $char_info_row["game_char_history_id"], "Bag4");
									if (isset($bag4)) {
										$bag4->out();
									}
?>
								</td>
							</tr>
						</table>
<?		} else { ?>
						<table border="0" cellspacing="2" cellpadding="0" width="100%">
							<tr>
								<td valign="top" width="38%">
									<?
									if (isset($char_info)) {
										echo $char_info->out();
									}
									?>
								</td>
							</tr>
						</table>
<?		} ?>
					</td>
				</tr>
			</table>
			<script language="javascript"><!--
				function delete_char() {
					var game_char_id = document.getElementById('character').value;
					var goto_link = '<?=FILENAME_PROGRESS_REPORT?>'+'?action=delete_character&game_char_id='+game_char_id;
					var char_name = document.getElementById('character')[document.getElementById('character').selectedIndex]['text'];
					
					//for (a in document.getElementById('character').selectedIndex) {
					//	alert(a);
					//}
					
					answer = confirm('Are you sure to delete ' + char_name + '?');
					
					if (answer != 0) { 
						window.location.href = goto_link;
					}
					
					return false;
				}
				
				function view_character_profiler() {
					var game_char_id = document.getElementById('character').value;
					var get_date_id = document.getElementById('profiler_compare').value;
					var goto_link = '<?=FILENAME_PROGRESS_REPORT?>'+'?action=char_info&game_char_id='+game_char_id;
					
					if (get_date_id == 0 || get_date_id == null) {
						alert('Please Select a Date!');
					} else {
						var get_date_value = document.getElementById('profiler_compare').getElementsByTagName("option")[get_date_id].firstChild.nodeValue;
						goto_link += '&date='+get_date_value;
						window.location.href = goto_link;
					}
					
					return false;
				}
				
				function hidebag(id) {
					if (DOMCall(id+"_div_id") != null) {
						if (document.getElementById(id+"_div_id").className == "hide") {
							document.getElementById(id+"_div_id").className = "show";
						} else if (document.getElementById(id+"_div_id").className == "show") {
							document.getElementById(id+"_div_id").className = "hide";
						}
					}
				}
				
				var MENU_ITEMS = [
					['<?=TEXT_GEAR?>', '<?=tep_href_link(FILENAME_PROGRESS_REPORT, tep_get_all_get_params(array("view_info")))?>', null, null],
					['<?=TEXT_BAGS_AND_BANK?>', '<?=tep_href_link(FILENAME_PROGRESS_REPORT, tep_get_all_get_params()."view_info=bag_bank")?>', null, null],
				];

				var MENU_POS = [{
					// item sizes
					'height': 24,
					'width': 130,
					// menu block offset from the origin:
					//	for root level origin is upper left corner of the page
					//	for other levels origin is upper left corner of parent item
					'block_top': 120,
					'block_left': 171,
					// offsets between items of the same level
					'top': 0,
					'left': 131,
					// time in milliseconds before menu is hidden after cursor has gone out
					// of any items
					'hide_delay': 200,
					'expd_delay': 200,
					'css' : {
						'outer' : ['m0l0oout', 'm0l0oover'],
						'inner' : ['m0l0iout', 'm0l0iover']
					}
				},{
					'height': 24,
					'width': 170,
					'block_top': 25,
					'block_left': 0,
					'top': 23,
					'left': 0,
					'css' : {
						'outer' : ['m0l1oout', 'm0l1oover'],
						'inner' : ['m0l1iout', 'm0l1iover']
					}
				},{
					'block_top': 5,
					'block_left': 160
				}
				]

				new menu (MENU_ITEMS, MENU_POS);
				
				function profilerCompare(frmObj, currProfilerViewDate, game_char_id) {
					var s_index = 0;
					var win_height = 500 + ( (s_index + 1) * 14 ) + 10;
					var m_win_height = 410 + ( (s_index + 1) * 15 ) + 20;
					
					//if (trim_str(frmObj.profiler_compare.value) == 0) {
					if (document.getElementById('profiler_compare').value == 0) {
						alert('Please Select a date!');
						return false;
					} else {
						if (window.showModalDialog) {	// For IE
							window.showModalDialog('popup/profiler_compare.html.php?action=profiler_diff'+'&_lang='+'<?=$languages_id?>'+'&currProfilerViewDate='+currProfilerViewDate+'&game_char_id='+game_char_id+'&date_compare_id='+frmObj.profiler_compare.value,window,"dialogWidth:600px;dialogHeight:"+m_win_height+"px;center:yes;help:no;status:no");
						} else {
							openDGDialog('popup/profiler_compare.html.php?action=profiler_diff'+'&_lang='+'<?=$languages_id?>'+'&currProfilerViewDate='+currProfilerViewDate+'&game_char_id='+game_char_id+'&date_compare_id='+frmObj.profiler_compare.value+'&path='+'<?=$path?>', 600, win_height, '', ',scrollbars=yes');
						}
						return false;
					}
				}
			//--></script>

<?	} else { ?>
			<table border="0" cellspacing="0" cellpadding="0" width="100%">
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
				</tr>
				<tr>
					<td><?=custom_product::progress_report_statistic($status_name);?></td>
				</tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				</tr>
				<tr>
					<td class="ordersRecords" align="center" colspan="7">
						<a href="<?=tep_href_link(FILENAME_PROGRESS_REPORT, 'action=advanced_search')?>"><?=LINK_ADVANCED_SEARCH?></a>
					</td>
				</tr>
				<tr>
					<td><?=tep_draw_separator('pixel_trans.gif', '1', '100')?></td>
				</tr>
			</table>
<?	} ?>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<script language="javascript"><!--
	
	var orderAmountArray = new Array();
	
<?
	echo $js;
	
	if (count($payment_detail)) {
		foreach ($payment_detail as $status_name => $res) {
			$order_str = count($res['order_id']) ? implode(',', $res['order_id']) : '';?>
			document.getElementById('s_'+'<?=$status_name?>'+'_order_str').value = "<?=$order_str?>";			
<?		
		}
	}

	if (count($extra_detail)) {
		foreach ($extra_detail as $key => $count) { ?>
			var s_<?=$key?>_count = <?=$count?>;
			
			if (eval(s_<?=$key?>_count) > 0) {
				document.getElementById('s_<?=$key?>_tag_nav').className = 'show';
			}
<?
		}
	}
?>
	
	function showOverEffect(object, class_name, extra_row) {
		rowOverEffect(object, class_name);
		var rowObjArray = extra_row.split('##');
	  	for (var i = 0; i < rowObjArray.length; i++) {
	  		if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
	  			rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
	  		}
	  	}
	}
	
	function showOutEffect(object, class_name, extra_row) {
		rowOutEffect(object, class_name);
		var rowObjArray = extra_row.split('##');
		for (var i = 0; i < rowObjArray.length; i++) {
			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
			}
		}
	}
	
	function showClicked(object, class_name, extra_row) {
		rowClicked(object, class_name);
		var rowObjArray = extra_row.split('##');
  		for (var i = 0; i < rowObjArray.length; i++) {
  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
  			}
		}
	}
						
	function verify_status_selection() {
		var multi_status_select = document.custom_product_criteria.elements['order_status[]'];
		var selected_count = 0;
		for (i=0;i<multi_status_select.length;i++) {
		    var cur_status_id = multi_status_select[i].value;
			var multi_tags_select = document.custom_product_criteria.elements['status_'+cur_status_id+'[]'];
			if (multi_status_select[i].checked == true) {
				selected_count++;
				if (typeof(multi_tags_select) != 'undefined') {
					if (typeof(multi_tags_select.length) != 'undefined') {
						for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
							multi_tags_select[tag_cnt].disabled = false;
						}
					} else {
						multi_tags_select.disabled = false;
					}
				}
			} else {
				if (typeof(multi_tags_select) != 'undefined') {
					if (typeof(multi_tags_select.length) != 'undefined') {
						for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
							multi_tags_select[tag_cnt].disabled = true;
							multi_tags_select[tag_cnt].checked = false;
						}
					} else {
						multi_tags_select.disabled = true;
						multi_tags_select.checked = false;
					}
				}
			}
		}
		if (!selected_count) {
			document.getElementById('order_status_any').checked = true;
		} else {
			document.getElementById('order_status_any').checked = false;
		}
	}
	
	function set_status_option(any_status_obj) {
		var multi_status_select = document.custom_product_criteria.elements['order_status[]'];
		if (any_status_obj.checked == true) {
			for (i=0;i<multi_status_select.length;i++) {
				multi_status_select[i].checked = false;
									
				var cur_status_id = multi_status_select[i].value;
			    var multi_tags_select = document.custom_product_criteria.elements['status_'+cur_status_id+'[]'];
				if (typeof(multi_tags_select) != 'undefined') {
					if (typeof(multi_tags_select.length) != 'undefined') {
						for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
							multi_tags_select[tag_cnt].disabled = true;
							multi_tags_select[tag_cnt].checked = false;
						}
					} else {
						multi_tags_select.disabled = true;
						multi_tags_select.checked = false;
					}
										
				}
			}
		} else {	// force to check if no any order status option is selected
			var selected_count = 0;
			for (i=0;i<multi_status_select.length;i++) {
				var cur_status_id = multi_status_select[i].value;
			    var multi_tags_select = document.custom_product_criteria.elements['status_'+cur_status_id+'[]'];
				if (multi_status_select[i].checked == true) {
					selected_count++;
					if (typeof(multi_tags_select) != 'undefined') {
						if (typeof(multi_tags_select.length) != 'undefined') {
							for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
								multi_tags_select[tag_cnt].disabled = false;
							}
						} else {
							multi_tags_select.disabled = false;
						}
					}
				} else {
					if (typeof(multi_tags_select) != 'undefined') {
						if (typeof(multi_tags_select.length) != 'undefined') {
							for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
								multi_tags_select[tag_cnt].disabled = true;
								multi_tags_select[tag_cnt].checked = false;
							}
						} else {
							multi_tags_select.disabled = true;
							multi_tags_select.checked = false;
						}
					}
				}
			}
			if (!selected_count) {
				any_status_obj.checked = true;
			}
		}
	}
	
	function update_selected_price(frmObj, frmStatus, checkName) {
	<?	if (!$supplier_cp_payment_detail_permission) { echo 'return true;'; } ?>
								
			var total_sel_amount = 0;
			var any_box_selected = false;
			var elts      = (typeof(frmObj.elements[checkName+'[]']) != 'undefined')
					 		? frmObj.elements[checkName+'[]']
					 		: "";
    		var elts_cnt  = (typeof(elts.length) != 'undefined')
                  			? elts.length
                  			: 0;
                  			
        if (elts_cnt) {
			for (var i=0; i < elts_cnt; i++) {
				e = elts[i];
				
				if (e.type=='checkbox' && e.checked) {
					any_box_selected = true;
					/*
					if (orderAmountArray[e.value] != null) {
						var pay_obj = document.getElementById('partial_pay_' + e.value);
							        		
						if (pay_obj != null) {
							var pay_amt = parseFloat(pay_obj.value);
							if (pay_amt > 0 && pay_amt < orderAmountArray[e.value]) {
							    total_sel_amount += pay_amt;
							} else {
							    total_sel_amount += orderAmountArray[e.value];
							    pay_obj.value = '';
							}
						} else {
							total_sel_amount += orderAmountArray[e.value];
						}
					}
					*/
					total_sel_amount += orderAmountArray[e.value];
				}
			} // end for
		} else if (elts != '') {
			e = elts;
			if (e.type=='checkbox' && e.checked) {
				any_box_selected = true;
				/*
				if (orderAmountArray[e.value] != null) {
					var pay_obj = document.getElementById('partial_pay_' + e.value);
						        		
					if (pay_obj != null) {
						var pay_amt = parseFloat(pay_obj.value);
						if (pay_amt > 0 && pay_amt < orderAmountArray[e.value]) {
						    total_sel_amount += pay_amt;
						} else {
						    total_sel_amount += orderAmountArray[e.value];
						    pay_obj.value = '';
						}
					} else {
						total_sel_amount += orderAmountArray[e.value];
					}
				}
				*/
				total_sel_amount += orderAmountArray[e.value];
			}
		}
					    
		var span_obj = DOMCall('total_sel_amt_' + frmStatus);
		
		if (any_box_selected) {
			span_obj.innerHTML = '<?=TEXT_SELECTED_ORDERS_AMOUNT?>' + currency(total_sel_amount, '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_left"]?>', '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_right"]?>', '<?=DISPLAY_PRICE_DECIMAL?>');
			//span_obj.innerHTML = total_sel_amount;
		} else {
			span_obj.innerHTML = '';
		}
			
	}
	
	function confirmBatchAction(frmObj, frmStatus, checkName) {
		if (trim_str(frmObj.batch_action.value) == '') {
			alert('Please select your batch action!');
			return false;
		} else {
			if (frmObj.batch_action.value == 'MakePayment') {
				/* For showModalDialog return value */
				var myObj = new Object();
				myObj.m_r = '';
				myObj.m_n = '';
				
				var m_r = '';
				var m_n = '';
										
				var s_ordersArray = new Array();
				var s_index = 0;
										
				var first_sup = '';
				var total_sel_amount = 0;
				var any_box_selected = false;
										
				var elts      = (typeof(frmObj.elements[checkName+'[]']) != 'undefined')
								? frmObj.elements[checkName+'[]']
								: "";
			    var elts_cnt  = (typeof(elts.length) != 'undefined')
			                  	? elts.length
			                  	: 0;
			                  			
			    if (elts_cnt) {
					for (var i=0; i < elts_cnt; i++) {
						e = elts[i];
									            
						 if (e.type=='checkbox' && e.checked) {
							s_ordersArray[s_index] = new Array();
										            
							checkbox_id_array = e.id.split('_');
							if (!any_box_selected) {
								first_sup = checkbox_id_array[0];
							} else {
								if (first_sup != checkbox_id_array[0]) {
									alert('Your selections includes orders for more than one supplier!');
									frmObj.batch_action.selectedIndex = 0;
									return false;
								}
							}
							if (orderAmountArray[e.value] != null) {
								s_ordersArray[s_index]['id'] = e.value;
									            		
								var pay_obj = document.getElementById('partial_pay_' + e.value);
								if (pay_obj != null) {
									var pay_amt = parseFloat(pay_obj.value);
									if (pay_amt > 0 && pay_amt < orderAmountArray[e.value]) {
										total_sel_amount += pay_amt;
										s_ordersArray[s_index]['amt'] = pay_amt;
									} else {
										total_sel_amount += orderAmountArray[e.value];
										pay_obj.value = '';
										s_ordersArray[s_index]['amt'] = 'f';
									}
								} else {
									total_sel_amount += orderAmountArray[e.value];
									s_ordersArray[s_index]['amt'] = 'f';
								}
								s_index++;
							}
							any_box_selected = true;
						}
					} // end for
				 } else if (elts != '') {
					e = elts;
					if (e.type=='checkbox' && e.checked) {
						s_ordersArray[s_index] = new Array();
						
						checkbox_id_array = e.id.split('_');
						first_sup = checkbox_id_array[0];
									        	
						any_box_selected = true;
						if (orderAmountArray[e.value] != null) {
							s_ordersArray[s_index]['id'] = e.value;
									            	
							var pay_obj = document.getElementById('partial_pay_' + e.value);
									        		
							if (pay_obj != null) {
								var pay_amt = parseFloat(pay_obj.value);
								if (pay_amt > 0 && pay_amt < orderAmountArray[e.value]) {
									total_sel_amount += pay_amt;
									s_ordersArray[s_index]['amt'] = pay_amt;
								} else {
									total_sel_amount += orderAmountArray[e.value];
									pay_obj.value = '';
									s_ordersArray[s_index]['amt'] = 'f';
									        			}
							} else {
								total_sel_amount += orderAmountArray[e.value];
								s_ordersArray[s_index]['amt'] = 'f';
							}
						}
					}
				}
									    
				if (!any_box_selected) {
					alert('Please select at least one order for your payment!');
					frmObj.batch_action.selectedIndex = 0;
					return false;
				} else {
					answer = confirm('Are you sure to make payment with the amount of ' + currency(total_sel_amount, '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_left"]?>', '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_right"]?>', '<?=DISPLAY_PRICE_DECIMAL?>') + ' ?');
					if (answer != 0) {
						var php = new PHP_Serializer();
						var s_order_str = php.serialize(s_ordersArray);
										        
						var win_height = 400 + ( (s_index + 1) * 14 ) + 10;
						var m_win_height = 415 + ( (s_index + 1) * 15 ) + 20;
										        
						if (window.showModalDialog) {	// For IE
							window.showModalDialog('popup/payment_info.html.php?action=custom_product_new_payment&aid='+'<?=$login_id?>'+'&sid='+first_sup+'&s_o='+s_order_str+'&_lang='+'<?=$languages_id?>',window,"dialogWidth:600px;dialogHeight:"+m_win_height+"px;center:yes;help:no;status:no");
						} else {
							openDGDialog('popup/payment_info.html.php?action=custom_product_new_payment&aid='+'<?=$login_id?>'+'&sid='+first_sup+'&s_o='+s_order_str+'&_lang='+'<?=$languages_id?>', 600, win_height, '', ',scrollbars=yes');
						}
					} else {
						frmObj.batch_action.selectedIndex = 0;
						return false;
					}
				}
									    
				return false;
			} else {
				return true;
			}
		}
	}
						
	function payment_info(remark, notify) {
		if (document.progress_report_4_lists_form.payment_remark != null) {
			document.progress_report_4_lists_form.payment_remark.value = remark;
		}
		if (document.progress_report_4_lists_form.show_supplier_remark != null) {
			document.progress_report_4_lists_form.show_supplier_remark.value = notify;
		}
							
		document.progress_report_4_lists_form.submit();
	}
	
	function payment_init() {
		document.progress_report_4_lists_form.batch_action.selectedIndex = 0;
	}
	
	function resetControls(controlObj) {
		if (controlObj.id == 'custom_product_order_id') {
			document.custom_product_criteria.custom_product_start_date.value = '';
			document.custom_product_criteria.custom_product_end_date.value = '';
			document.custom_product_criteria.custom_product_product_name.value = '';
			document.custom_product_criteria.custom_product_supplier.value = '';
			document.custom_product_criteria.custom_product_not_assign.checked = false;
			document.custom_product_criteria.custom_product_supplier.disabled = false;
			if (document.custom_product_criteria.cat_id != null)	document.custom_product_criteria.cat_id.selectedIndex = 0;
			if (document.custom_product_criteria.include_subcategory != null)	document.custom_product_criteria.include_subcategory.checked = true;
		} else if (controlObj.id == 'custom_product_product_name') {
			document.custom_product_criteria.custom_product_order_id.value = '';
		} else if (controlObj.id == 'custom_product_not_assign') {
			document.custom_product_criteria.custom_product_supplier.value = '';
			if (document.custom_product_criteria.custom_product_not_assign.checked == true) {
				document.custom_product_criteria.custom_product_supplier.disabled = true;
			} else {
				document.custom_product_criteria.custom_product_supplier.disabled = false;
			}
		}
	}
	
	function checkRatio(obj_id) {
		var obj = eval('document.update_task_info.adjust_ratio_' + obj_id);
		
		if (trim_str(obj.value) != '') {
			if (obj.value > 100) {
				alert('Please entered integer between 0 to 100!');
				obj.focus();
				obj.select();
				return false;
			}
		}
		return true;
	}
	
<?	if ($_REQUEST['action'] == 'advanced_search') { ?>
		set_status_option(document.getElementById('order_status_any'));
<?	} ?>

<?	if ($_REQUEST['action'] == 'report') { ?>
	function confirm_update_status(status_curr) {
		var new_status = parseInt(document.getElementById('status_now').value);
		
		if (new_status != status_curr) {
			fetchStatusTagSelections(document.getElementById('status_now').value, 'destination_tag_selector_div', 'new_status_tag', '<?=(int)$languages_id?>');
		
			return true;
		} else {
			div_objRef = DOMCall('destination_tag_selector_div');
			div_objRef.className = "hide";
			return false;	
		}
	}
	
	function setOrderComment(obj) {
		obj.disabled = true;
		
		var ref_url = "orders_xmlhttp.php?action=get_comments&cmID="+obj.value;
		
		xmlhttp.open("GET", ref_url); 
	    xmlhttp.onreadystatechange = function() { 
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
	      		var R = (typeof (xmlhttp.responseXML.getElementsByTagName("result")[0]) != 'undefined' && xmlhttp.responseXML.getElementsByTagName("result")[0] != null) ? xmlhttp.responseXML.getElementsByTagName("result")[0].firstChild.data : '';
	      		
	      		document.getElementById('comments[<?=$_REQUEST['orders_product_id']?>]').value = R
	      		obj.disabled = false;
	      		obj.selectedIndex = 0;
	      	}
	    }
	    
	    xmlhttp.send(null);
		
	   	setTimeout('hideMainInfo()', 3000);
	}
<?	} ?>
//-->
</script>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>