<?php

  require('includes/application_top.php');

  $action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

  if (tep_not_null($action)) {
    switch ($action) {
      case 'setflag':
        tep_set_promotions_status($HTTP_GET_VARS['pmID'], $HTTP_GET_VARS['flag']);

        tep_redirect(tep_href_link(FILENAME_PROMOTIONS, (isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] . '&' : '') . 'pmID=' . $HTTP_GET_VARS['pmID'], 'NONSSL'));
        break;
      case 'insert':
      case 'save':
        if (isset($HTTP_GET_VARS['pmID'])) 
        $promotions_id = tep_db_prepare_input($HTTP_GET_VARS['pmID']);
       	$date_f = date('Y-m-d', mktime(0, 0, 0, $HTTP_POST_VARS['promotions_from_month'],$HTTP_POST_VARS['promotions_from_day'] ,$HTTP_POST_VARS['promotions_from_year'] ));
       	$date_t = date('Y-m-d', mktime(0, 0, 0, $HTTP_POST_VARS['promotions_to_month'],$HTTP_POST_VARS['promotions_to_day'] ,$HTTP_POST_VARS['promotions_to_year'] ));
        $sql_data_array = array('promotions_title' => $HTTP_POST_VARS['promotions_title'],
        						'promotions_description' => $HTTP_POST_VARS['promotions_description'],
        						'promotions_from' => $date_f,
        						'promotions_to' => $date_t,
        						'promotions_min_value' => $HTTP_POST_VARS['promotions_min_value']);

        if ($action == 'insert') {

          tep_db_perform(TABLE_PROMOTIONS, $sql_data_array);
          $promotions_id = tep_db_insert_id();
        } elseif ($action == 'save') {

          tep_db_perform(TABLE_PROMOTIONS, $sql_data_array, 'update', "promotions_id = '" . (int)$promotions_id . "'");
        }

        tep_redirect(tep_href_link(FILENAME_PROMOTIONS, (isset($HTTP_GET_VARS['page']) ? 'page=' . $HTTP_GET_VARS['page'] . '&' : '') . 'pmID=' . $promotions_id));
        break;
      case 'deleteconfirm':
        $promotions_id = tep_db_prepare_input($HTTP_GET_VARS['pmID']);
		
        tep_db_query("delete from " . TABLE_PROMOTIONS . " where promotions_id = '" . (int)$promotions_id . "'");
        tep_redirect(tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page']));
        break;
    }
  }
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
<script language="javascript">
  var dateAvailable = new ctlSpiffyCalendarBox("dateAvailable", "new_product", "products_date_available","btnDate1","<?php echo $pInfo->products_date_available; ?>",scBTNMODE_CUSTOMBLUE);
</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
  <tr>
    <td width="<?php echo BOX_WIDTH; ?>" valign="top"><table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
    </table></td>
<!-- body_text //-->
    <td width="100%" valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td width="100%"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading" valign="top"><?=HEADING_TITLE?><p>**Only allow 1 PROMOTION Green Status</td>
            <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr class="dataTableHeadingRow">
                <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_PROMOTIONS; ?></td>
                <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_STATUS; ?></td>
                <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_ACTION; ?>&nbsp;</td>
              </tr>
<?php
  $promotions_query_raw = "select promotions_id, promotions_title, promotions_description, promotions_from, promotions_to, promotions_min_value, promotions_status from " . TABLE_PROMOTIONS . " order by promotions_title";
  //$promotions_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $prmotions_query_raw, $manufacturers_query_numrows);
  $promotions_query = tep_db_query($promotions_query_raw);
  while ($promotions = tep_db_fetch_array($promotions_query)) {
	if ((!isset($HTTP_GET_VARS['pmID']) || (isset($HTTP_GET_VARS['pmID']) && ($HTTP_GET_VARS['pmID'] == $promotions['promotions_id']))) && !isset($pmInfo) && (substr($action, 0, 3) != 'new')) {
  		$pmInfo = new objectInfo($promotions);
	}
    
    if (isset($pmInfo) && is_object($pmInfo) && ($promotions['promotions_id'] == $pmInfo->promotions_id)) {
      echo '              <tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $promotions['promotions_id'] . '&action=edit') . '\'">' . "\n";
    } else {
      echo '              <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $promotions['promotions_id']) . '\'">' . "\n";
    }
    
?>
                <td class="dataTableContent"><?php echo $promotions['promotions_title']; ?></td>
                <td  class="dataTableContent" align="left">
<?php
      if ($promotions['promotions_status'] == '1') {
        echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PROMOTIONS, 'action=setflag&flag=0&pmID=' . $promotions['promotions_id'], 'NONSSL') . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
      } else {
        echo '<a href="' . tep_href_link(FILENAME_PROMOTIONS, 'action=setflag&flag=1&pmID=' . $promotions['promotions_id'], 'NONSSL') . '">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
      }
?></td>
                <td class="dataTableContent" align="right"><?php if (isset($pmInfo) && is_object($pmInfo) && ($promotions['promotions_id'] == $pmInfo->promotions_id)) { echo tep_image(DIR_WS_IMAGES . 'icon_arrow_right.gif'); } else { echo '<a href="' . tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $promotions['promotions_id']) . '">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', IMAGE_ICON_INFO) . '</a>'; } ?>&nbsp;</td>
              </tr>
<?php
  }
?>
              <!--tr>
                <td colspan="2"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                  <tr>
                    <td class="smallText" valign="top"><?php //echo $promotions_split->display_count($promotions_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], '10'); ?></td>
                    <td class="smallText" align="right"><?php //echo $promotions_split->display_links($promotions_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?></td>
                  </tr>
                </table></td>
              </tr-->
<?php
  if (empty($action)) {
?>
              <tr>
                <td align="right" colspan="3" class="smallText"><?php echo '<a href="' . tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $pmInfo->promotions_id . '&action=new') . '">' . tep_image_button('button_insert.gif', IMAGE_INSERT) . '</a>'; ?></td>
              </tr>
<?php
  }
?>
            </table></td>
<?php
  $heading = array();
  $contents = array();

  switch ($action) {
    case 'new':
      $heading[] = array('text' => '<b>Add New Promotion</b>');

      $contents = array('form' => tep_draw_form('promotions', FILENAME_PROMOTIONS, 'action=insert', 'post', 'enctype="multipart/form-data"'));
      $contents[] = array('text' => TEXT_NEW_INTRO);
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_NAME . '<br>' . tep_draw_input_field('promotions_title'));
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_DESCRIPTION . '<br>' . tep_draw_input_field('promotions_description'));
        if (!$HTTP_POST_VARS['promotions_from']) {
	      $promotions_from = split_dep("[-]", date('Y-m-d'));
	    } else {
	      $promotions_from = split_dep("[-]", $HTTP_POST_VARS['promotions_from']);
	    }
	    if (!$HTTP_POST_VARS['promotions_to']) {
	      $promotions_to = split_dep("[-]", date('Y-m-d'));
	      $promotions_to[0] = $promotions_to[0] + 1;
	    } else {
	      $promotions_to = split_dep("[-]", $HTTP_POST_VARS['promotions_to']);
	    }
      
      
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_FROM . '<br>' . tep_draw_date_selector('promotions_from', mktime(0,0,0, $promotions_from[1], $promotions_from[2], $promotions_from[0], 0)));
      
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_TO . '<br>' . tep_draw_date_selector('promotions_to', mktime(0,0,0, $promotions_to[1], $promotions_to[2], $promotions_to[0], 0)));
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_MIN_VALUE . '<br>' . tep_draw_input_field('promotions_min_value'));

      /*
      $manufacturer_inputs_string = '';
      $languages = tep_get_languages();
      for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
        $manufacturer_inputs_string .= '<br>' . tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('manufacturers_url[' . $languages[$i]['id'] . ']');
      }

      $contents[] = array('text' => '<br>' . TEXT_MANUFACTURERS_URL . $manufacturer_inputs_string);
      */
      $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_save.gif', IMAGE_SAVE) . ' <a href="' . tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $HTTP_GET_VARS['pmID']) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      break;
    case 'edit':
      $heading[] = array('text' => '<b>Edit Promotion</b>');

      $contents = array('form' => tep_draw_form('promotions', FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $pmInfo->promotions_id . '&action=save', 'post', 'enctype="multipart/form-data"'));
      $contents[] = array('text' => TEXT_EDIT_INTRO);
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_NAME . '<br>' . tep_draw_input_field('promotions_title', $pmInfo->promotions_title));
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_DESCRIPTION . '<br>' . tep_draw_input_field('promotions_description', $pmInfo->promotions_description));
        if (!$pmInfo->promotions_from) {
	      $promotions_from = split_dep("[-]", date('Y-m-d'));
	    } else {
	      $promotions_from = split_dep("[-]", $pmInfo->promotions_from);
	    }

	    if (!$pmInfo->promotions_to) {
	      $promotions_to = split_dep("[-]", date('Y-m-d'));
	      $promotions_to[0] = $promotions_to[0] + 1;
	    } else {
	      $promotions_to = split_dep("[-]", $pmInfo->promotions_to);
	    }
	    
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_FROM . '<br>' . tep_draw_date_selector('promotions_from', mktime(0,0,0, $promotions_from[1], $promotions_from[2], $promotions_from[0], 0)));
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_TO . '<br>' . tep_draw_date_selector('promotions_to', mktime(0,0,0, $promotions_to[1], $promotions_to[2], $promotions_to[0], 0)));
      $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_MIN_VALUE . '<br>' . tep_draw_input_field('promotions_min_value', $pmInfo->promotions_min_value));
		
      $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_save.gif', IMAGE_SAVE) . ' <a href="' . tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $pmInfo->promotions_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      
      break;
    case 'delete':
      $heading[] = array('text' => '<b>Delete Promotion</b>');

      $contents = array('form' => tep_draw_form('promotions', FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $pmInfo->promotions_id . '&action=deleteconfirm'));
      $contents[] = array('text' => TEXT_DELETE_INTRO);
      $contents[] = array('text' => '<br><b>' . $pmInfo->promotions_name . '</b>');
      
      $contents[] = array('align' => 'center', 'text' => '<br>' . tep_image_submit('button_delete.gif', IMAGE_DELETE) . ' <a href="' . tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $pmInfo->promotions_id) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>');
      break;
    default:
      if (isset($pmInfo) && is_object($pmInfo)) {
        $heading[] = array('text' => '<b>' . $pmInfo->promotions_title . '</b>');

        $contents[] = array('align' => 'center', 'text' => '<a href="' . tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $pmInfo->promotions_id . '&action=edit') . '">' . tep_image_button('button_edit.gif', IMAGE_EDIT) . '</a> <a href="' . tep_href_link(FILENAME_PROMOTIONS, 'page=' . $HTTP_GET_VARS['page'] . '&pmID=' . $pmInfo->promotions_id . '&action=delete') . '">' . tep_image_button('button_delete.gif', IMAGE_DELETE) . '</a>');
        $contents[] = array('text' => '<br><b>' . $pmInfo->promotions_description.'</b><br>');
        $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_FROM . ' ' . tep_date_short($pmInfo->promotions_from, PREFERRED_DATE_FORMAT).'<br>');
        $contents[] = array('text' => '<br>' . TEXT_PROMOTIONS_TO . ' ' . tep_date_short($pmInfo->promotions_to, PREFERRED_DATE_FORMAT).'<br>');
        
      }
      break;
  }

  if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
    echo '            <td width="25%" valign="top">' . "\n";

    $box = new box;
    echo $box->infoBox($heading, $contents);

    echo '            </td>' . "\n";
  }
?>
          </tr>
        </table></td>
      </tr>
    </table></td>
<!-- body_text_eof //-->
  </tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
