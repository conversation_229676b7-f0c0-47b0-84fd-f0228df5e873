<?php
/*
  	Group Discount
  	by hOZONE, <EMAIL>, http://hozone.cjb.net
  	
  	visit osCommerceITalia, http://www.oscommerceitalia.com
	
  	derived by:
  	Discount_Groups_v1.1, by <PERSON>, 2003/5/22
	
  	for:
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2003 osCommerce
  	
  	Released under the GNU General Public License 
*/

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'po_suppliers.php');

$system_log_object = new log_files($login_id);
$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$subaction = (isset($_GET['subaction']) ? $_GET['subaction'] : '');

if (tep_not_null($action)) {
    switch ($action) {
        case 'batch_upd':
            if (isset($_REQUEST['event_record']) && count($_REQUEST['event_record'])) {
                foreach ($_REQUEST['event_record'] as $publishers_id_loop => $publishers_sort_order_loop) {
                    $publishers_data_sql = array(
                        'sort_order' => (int)$publishers_sort_order_loop['sort_order'],
                        'last_modified' => 'now()'
                    );
                    tep_db_perform(TABLE_PUBLISHERS, $publishers_data_sql, 'update', " publishers_id = '" . $publishers_id_loop . "' ");
                }
                $messageStack->add_session(TEXT_INFO_PUBLISHER_UPDATED, 'success');
            }
            tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('pID', 'action', 'subaction', 'flag'))));
            break;
        case 'setflag':
            if (isset($_REQUEST['pID']) && (int)$_REQUEST['pID'] > 0) {
                $publishers_data_sql = array(
                    'publishers_status' => (int)$_REQUEST['flag'],
                    'last_modified' => 'now()'
                );
                tep_db_perform(TABLE_PUBLISHERS, $publishers_data_sql, 'update', " publishers_id = '" . (int)$_REQUEST['pID'] . "' ");
                $messageStack->add_session(TEXT_INFO_PUBLISHER_UPDATED, 'success');
            }
            tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('pID', 'action', 'subaction', 'flag'))));
            break;
    }
}

$publishers_configuration_data_array = array();
$publishers_configuration_data_array['SECRET_KEY'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Publisher Secret Key"),
    'publishers_configuration_key' => "SECRET_KEY",
    'publishers_configuration_description' => tep_db_prepare_input("Publisher's Secret Key"),
);
$publishers_configuration_data_array['PUBLISHER_API_ID'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Publisher Api ID"),
    'publishers_configuration_key' => "PUBLISHER_API_ID",
    'publishers_configuration_description' => tep_db_prepare_input("Publisher's API ID, which given by OffGamers"),
);
$publishers_configuration_data_array['OGM_MERCHANT_ID'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Publisher Merchant ID"),
    'publishers_configuration_key' => "OGM_MERCHANT_ID",
    'publishers_configuration_description' => tep_db_prepare_input("Publisher's merchant ID, which given by publisher"),
);
$publishers_configuration_data_array['TOP_UP_URL'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Publisher Top-up URL"),
    'publishers_configuration_key' => "TOP_UP_URL",
    'publishers_configuration_description' => tep_db_prepare_input("Publisher's top-up url for API call"),
);
$publishers_configuration_data_array['STATUS_URL'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Publisher Status URL"),
    'publishers_configuration_key' => "STATUS_URL",
    'publishers_configuration_description' => tep_db_prepare_input("Publisher's status url for API call"),
);
$publishers_configuration_data_array['SERVER_URL'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Publisher Server URL"),
    'publishers_configuration_key' => "SERVER_URL",
    'publishers_configuration_description' => tep_db_prepare_input("Publisher's Server url for API call"),
);
$publishers_configuration_data_array['MIN_BALANCE_CURRENCY'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Minimim Balance Currency"),
    'publishers_configuration_key' => "MIN_BALANCE_CURRENCY",
    'publishers_configuration_description' => tep_db_prepare_input("This is the minimim balance's currency for email notification."),
);
$publishers_configuration_data_array['MIN_BALANCE'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Minimim Balance For Notification"),
    'publishers_configuration_key' => "MIN_BALANCE",
    'publishers_configuration_description' => tep_db_prepare_input("This is the minimim balance for email notification."),
);
$publishers_configuration_data_array['LOW_BALANCE_NOTIFICATION'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Email Receiver for Minimim Balance Notification"),
    'publishers_configuration_key' => "LOW_BALANCE_NOTIFICATION",
    'publishers_configuration_description' => tep_db_prepare_input("This is the minimim balance for email receiver."),
);
$publishers_configuration_data_array['TOP_UP_LIMIT'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Top-up Limit"),
    'publishers_configuration_key' => "TOP_UP_LIMIT",
    'publishers_configuration_description' => tep_db_prepare_input("Top-up limit given by publishers."),
);
$publishers_configuration_data_array['PRIVATE_KEY'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Private Key Filename"),
    'publishers_configuration_key' => "PRIVATE_KEY",
    'publishers_configuration_description' => tep_db_prepare_input("Private Key Filename, which will be located at '" . DIR_WS_MODULES . "direct_topup/'."),
);
$publishers_configuration_data_array['PUBLIC_KEY'] = array(
    'publishers_configuration_title' => tep_db_prepare_input("Public Key Filename"),
    'publishers_configuration_key' => "PUBLIC_KEY",
    'publishers_configuration_description' => tep_db_prepare_input("Public Key Filename, which will be located at '" . DIR_WS_MODULES . "direct_topup/'."),
);

if (tep_not_null($subaction)) {
    switch ($subaction) {
        case 'newconfirm':
        case 'update':
            $publisher_id = 0;

            include_once(DIR_WS_CLASSES . 'direct_topup.php');
            $direct_topup_obj = new direct_topup();
            $direct_topup_obj->void_include_all_classes();

            $class = (isset($_REQUEST['configuration']['TOP_UP_MODE']) && tep_not_null($_REQUEST['configuration']['TOP_UP_MODE']) ? $_REQUEST['configuration']['TOP_UP_MODE'] : '');

            $top_up_class = '';
            if (isset($_REQUEST['configuration']['TOP_UP_MODE']) && tep_class_exists('dtu_' . $class)) {
                eval('$direct_topup_class_obj = new dtu_' . $class . '();');
            } else {
                $direct_topup_class_obj = new dtu_offgamers();
            }

            $error = false;

            $publishers_name = tep_db_prepare_input($_POST['publishers_name']);
            $publishers_supplier = tep_db_prepare_input($_POST['publishers_supplier']);
            $publisher_api_id = tep_db_prepare_input($_POST['configuration']['PUBLISHER_API_ID']);
            $publishers_status = (isset($_POST['rd_status']) ? (int)$_POST['rd_status'] : 0);
            $sort_order = (int)(isset($_POST['sort_order']) && preg_match("/^[0-9]+$/", $_POST['sort_order']) ? $_POST['sort_order'] : 50000);

            $publishers_remark = tep_db_prepare_input($_POST['publishers_remark']);

            if (tep_not_null($publishers_name)) {
                if (tep_not_null($publisher_api_id)) {

                    if ($subaction == 'update' && (!isset($_REQUEST['pID']) && !((int)$_REQUEST['pID'] > 0))) {
                        $messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
                        tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'subaction', 'pID'))));
                    } elseif (!isset($_REQUEST['configuration']['OGM_MERCHANT_ID']) || !$_REQUEST['configuration']['OGM_MERCHANT_ID']) {
                        $messageStack->add_session(ERROR_INVALID_PUBLISHERS_MERCHANT_ID);
                        tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'subaction', 'pID')) . 'action=new'));
                    } elseif (!$direct_topup_class_obj->validate_admin_update_input($_REQUEST, $error_msg)) {
                        $messageStack->add_session($error_msg);
                        tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'subaction', 'pID')) . 'action=edit&pID=' . (int)$_REQUEST['pID']));
                    } 
                    
                    $publishers_data_array = array(
                        'publishers_name' => tep_db_prepare_input($publishers_name),
                        'publishers_supplier_id' => (int)$publishers_supplier,
                        'sort_order' => (int)$sort_order,
                        'last_modified' => 'now()',
                        'last_modified_by' => $_SESSION['login_id'],
                        'publishers_status' => (int)$publishers_status,
                        'publishers_remark' => $publishers_remark
                    );

                    $check_publisher_key_sql = "SELECT publishers_id
                                        FROM " . TABLE_PUBLISHERS_CONFIGURATION . " 
                                        WHERE publishers_id <> '" . (int)$_REQUEST['pID'] . "'
                                            AND publishers_configuration_key = 'publisher_api_id' 
                                            AND publishers_configuration_value = '" . $publisher_api_id . "'";
                    $check_publisher_key_result = tep_db_query($check_publisher_key_sql);

                    if (isset($_REQUEST['configuration'])) {

                        if ($subaction == 'update') {
                            
                            if ($check_publisher_key_row = tep_db_fetch_array($check_publisher_key_result)) {
                                // Duplicate publisher key ID
                                $messageStack->add_session(ERROR_DUPLICATE_PUBLISHERS_API_ID);
                                tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'subaction', 'pID')) . 'action=new'));
                            }
                            $publisher_id = (int)$_REQUEST['pID'];
                            tep_db_perform(TABLE_PUBLISHERS, $publishers_data_array, 'update', " publishers_id = '" .$publisher_id . "' ");
                            
                            $publishers_conf_select_sql = "SELECT publishers_configuration_id,publishers_configuration_key,publishers_configuration_value
                                                    FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
                                                    WHERE publishers_id = '" . $publisher_id . "'";
                            $publishers_conf_result_sql = tep_db_query($publishers_conf_select_sql);

                            //format an array
                            while ($publishers_conf_row = tep_db_fetch_array($publishers_conf_result_sql)){
                                $publisher_conf_result_row[$publishers_conf_row['publishers_configuration_key']] = array(
                                    "publisher_configuration_value" => $publishers_conf_row['publishers_configuration_value'],
                                    "publishers_configuration_id" => $publishers_conf_row['publishers_configuration_id'],
                                );
                            }

                            if ($diff_arr = array_diff_key($publisher_conf_result_row,$_REQUEST['configuration'])){                                
                                foreach ($diff_arr as $configuration_key => $pub_configuration_data) {
                                    //delete unused setting
                                    tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_CONFIGURATION . " WHERE publishers_configuration_id = '" . $pub_configuration_data['publishers_configuration_id'] . "' ");
                                }
                            }

                            //update data if found not same and missing.
                            foreach ($_REQUEST['configuration'] as $configure_key_loop => $configure_value_loop) {
                                if((isset($publisher_conf_result_row[$configure_key_loop])) && ($publisher_conf_result_row[$configure_key_loop]['publisher_configuration_value'] != $configure_value_loop)){
                                    unset($_REQUEST['configuration'][$configure_key_loop]);
                                    $publishers_configuration_data_sql = array(
                                        "publishers_configuration_key" => tep_db_prepare_input($configure_key_loop),
                                        "publishers_configuration_value" => tep_db_prepare_input($configure_value_loop),
                                        "date_added" => 'now()',
                                        "last_modified_by" => $_SESSION['login_id']
                                    );
                                    tep_db_perform(TABLE_PUBLISHERS_CONFIGURATION, $publishers_configuration_data_sql, $subaction, 'publishers_configuration_id = "'.$publisher_conf_result_row[$configure_key_loop]['publishers_configuration_id'].'"');
                                }

                                //remove updated or unchanged key
                                if(isset($publisher_conf_result_row[$configure_key_loop])){
                                    unset($_REQUEST['configuration'][$configure_key_loop]);
                                }
                            }
                            
                        }else{
                            tep_db_perform(TABLE_PUBLISHERS, $publishers_data_array);
                            $publisher_id = tep_db_insert_id();
                        }

                        //insert configuration
                        foreach ($_REQUEST['configuration'] as $configure_key_loop => $configure_value_loop) {
                            $publishers_configuration_data_sql = array(
                                "publishers_id" => $publisher_id,
                                "publishers_configuration_title" => tep_db_prepare_input((isset($publishers_configuration_data_array[$configure_key_loop]['publishers_configuration_title']) ? $publishers_configuration_data_array[$configure_key_loop]['publishers_configuration_title'] : '')),
                                "publishers_configuration_key" => tep_db_prepare_input($configure_key_loop),
                                "publishers_configuration_value" => tep_db_prepare_input($configure_value_loop),
                                "publishers_configuration_description" => tep_db_prepare_input((isset($publishers_configuration_data_array[$configure_key_loop]['publishers_configuration_description']) ? $publishers_configuration_data_array[$configure_key_loop]['publishers_configuration_description'] : '')),
                                "date_added" => 'now()',
                                "last_modified_by" => $_SESSION['login_id']
                            );
                            tep_db_perform(TABLE_PUBLISHERS_CONFIGURATION, $publishers_configuration_data_sql);
                        }
                    }
                } else {
                    $messageStack->add_session(ERROR_PUBLISHERS_API_ID);
                    $error = true;
                }
            } else {
                $messageStack->add_session(ERROR_PUBLISHERS_NAME);
                $error = true;
            }

            if ($error) {
                tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'subaction')) . 'action=new'));
            }

            tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'subaction', 'pID')) . 'action=edit&pID=' . $publisher_id));
            break;
        case 'deleteconfirm':
            $error = false;

            $publisher_id = tep_db_prepare_input($_GET['pID']);

            $count_orders_id_array = array();
            // check customers groups has payment method setting
            $orders_top_up_select_sql = "	SELECT op.orders_id 
			        						FROM " . TABLE_ORDERS_TOP_UP . " AS otu
			        						INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
			        							ON otu.orders_products_id = op.orders_products_id
											WHERE otu.publishers_id = '" . (int)$publisher_id . "' 
												AND otu.top_up_status = 1
											GROUP BY op.orders_id
											LIMIT 4";
            $orders_top_up_result_sql = tep_db_query($orders_top_up_select_sql);
            while ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result_sql)) {
                $count_orders_id_array[] = '<a href="' . tep_href_link(FILENAME_ORDERS, 'oID=' . $orders_top_up_row['orders_id'] . '&action=edit') . '">' . $orders_top_up_row['orders_id'] . '</a>';
            }

            if (count($count_orders_id_array)) {
                $messageStack->add_session(sprintf(ERROR_PUBLISHER_HAS_PENDING_TOPUP, implode(", ", $count_orders_id_array) . (count($count_orders_id_array) > 3 ? '...' : '')));
                $error = true;
            } else {
                $messageStack->add(TEXT_INFO_PUBLISHER_DELETED, 'success');

                tep_db_query("DELETE FROM " . TABLE_PUBLISHERS . " where publishers_id= " . $publisher_id);

                tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_CONFIGURATION . " WHERE publishers_id= " . $publisher_id);

                $publishers_games_select_sql = "SELECT publishers_games_id
	        									FROM " . TABLE_PUBLISHERS_GAMES . "
	        									WHERE publishers_id = '" . $publisher_id . "'";
                $publishers_games_select_sql = tep_db_query($publishers_games_select_sql);
                while ($publishers_games_row = tep_db_fetch_array($publishers_games_select_sql)) {
                    tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_PRODUCTS . " WHERE publishers_games_id= " . $publishers_games_row['publishers_games_id']);
                }
                tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_GAMES . " WHERE publishers_id= " . $publisher_id);
                tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_CONFIGURATION . " WHERE publishers_id= " . $publisher_id);

                $messageStack->add_session(TEXT_INFO_PUBLISHER_DELETED, 'success');
            }

            tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('pID', 'action', 'subaction'))));

            break;

    }
}
?>

    <!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
    <html <?php echo HTML_PARAMS; ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="includes/javascript/jquery.js"></script>
        <script language="javascript" src="includes/javascript/php.packed.js"></script>
        <script language="javascript" src="includes/javascript/customer_xmlhttp.js"></script>
        <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
        <script>
            function sync_publisher(pid) {
                jQuery.blockUI();
                $.get('sync_publisher.php?pid=' + pid);
                setTimeout(function () {
                    jQuery.unblockUI()
                }, 1000);
            }
        </script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
    <!-- header //-->
    <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
    <!-- header_eof //-->

    <!-- body //-->
    <table border="0" width="100%" cellspacing="2" cellpadding="2">
        <tr>
            <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                    <!-- left_navigation //-->
                    <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                    <!-- left_navigation_eof //-->
                </table>
            </td>
            <!-- body_text //-->
            <td width="100%" valign="top">
                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                    <?
                    if ($_GET['action'] == 'new' || $_GET['action'] == 'edit') {
                        $publisher_api_id = '';
                        $publishers_name = '';
                        $publishers_status = '1';
                        $publishers_sort_order = '';
                        $secret_key = '';
                        $ogm_merchant_id = '';
                        $top_up_url = '';
                        $status_url = '';
                        $min_balance_currency = 'USD';
                        $min_balance = '';
                        $low_balance_notification = '';

                        $supplier_array = $po_suppliers->get_active_po_supplier_list();

                        if ($_GET['action'] == 'edit') {
                            if (isset($_REQUEST['pID']) && (int)$_REQUEST['pID']) {
                                $publishers_select_sql = "	SELECT publishers_name, publishers_status, sort_order, publishers_remark, publishers_supplier_id
										FROM " . TABLE_PUBLISHERS . "
										WHERE publishers_id = '" . (int)$_REQUEST['pID'] . "'";
                                $publishers_result_sql = tep_db_query($publishers_select_sql);
                                if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
                                    $publishers_name = $publishers_row['publishers_name'];
                                    $publishers_status = $publishers_row['publishers_status'];
                                    $publishers_sort_order = $publishers_row['sort_order'];
                                    $publishers_remark = $publishers_row['publishers_remark'];
                                    $publisher_supplier_id = $publishers_row['publishers_supplier_id'];

                                    $publishers_configuration_array = array();
                                    $publishers_configuration_select_sql = "SELECT publishers_configuration_key, publishers_configuration_value
														FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
														WHERE publishers_id = '" . (int)$_REQUEST['pID'] . "'";
                                    $publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
                                    while ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql)) {
                                        $publishers_configuration_array[$publishers_configuration_row['publishers_configuration_key']] = $publishers_configuration_row['publishers_configuration_value'];
                                    }
                                    $publisher_api_id = $publishers_configuration_array['publisher_api_id'];
                                    $secret_key = $publishers_configuration_array['SECRET_KEY'];
                                    $private_key = $publishers_configuration_array['PRIVATE_KEY'];
                                    $public_key = $publishers_configuration_array['PUBLIC_KEY'];
                                    $ogm_merchant_id = $publishers_configuration_array['OGM_MERCHANT_ID'];
                                    $publisher_api_id = $publishers_configuration_array['PUBLISHER_API_ID'];
                                    $top_up_url = $publishers_configuration_array['TOP_UP_URL'];
                                    $status_url = $publishers_configuration_array['STATUS_URL'];
                                    $server_url = $publishers_configuration_array['SERVER_URL'];
                                    $min_balance_currency = $publishers_configuration_array['MIN_BALANCE_CURRENCY'];
                                    $min_balance = number_format((double)$publishers_configuration_array['MIN_BALANCE'], 2, ".", "");
                                    $low_balance_notification = $publishers_configuration_array['LOW_BALANCE_NOTIFICATION'];
                                    $top_up_limit = number_format((double)$publishers_configuration_array['TOP_UP_LIMIT'], 2, ".", "");

                                } else {
                                    $messageStack->add_session(ERROR_PUBLISHERS_NOT_FOUND);
                                    tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'subaction', 'pID'))));
                                }
                            } else {
                                $messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
                                tep_redirect(tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'subaction', 'pID'))));
                            }
                        }

                        require_once(DIR_WS_CLASSES . 'currencies.php');
                        $currencies = new currencies();

                        $currencies_array = array();
                        foreach ($currencies->currencies as $currencies_code_loop => $currencies_data_loop) {
                            $currencies_array[] = array(
                                'id' => $currencies_code_loop,
                                'text' => $currencies_code_loop
                            );
                        }

                        $top_up_mode_array = array();
                        $module_directory = DIR_FS_CATALOG_MODULES . 'direct_topup/';
                        $file_extension = substr($PHP_SELF, strrpos($PHP_SELF, '.'));
                        $directory_array = array();

                        if ($dir = @dir($module_directory)) {
                            while ($file = $dir->read()) {
                                if (!is_dir($module_directory . $file)) {
                                    if (substr($file, strrpos($file, '.')) == $file_extension) {
                                        if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $file)) {
                                            include_once(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $file);
                                        }
                                        include_once($module_directory . $file);
                                        $class = substr($file, 0, strrpos($file, '.'));
                                        eval('$direct_topup_obj = new dtu_' . $class . '();');
                                        if ($direct_topup_obj->get_enable()) {
                                            $top_up_mode_array[] = array('id' => $class, 'text' => $direct_topup_obj->get_title());
                                        }
                                    }
                                }
                            }
                            $dir->close();
                        }

                        ?>
                        <tr>
                            <td>
                                <?= tep_draw_form('publishers', FILENAME_PUBLISHERS, tep_get_all_get_params(array('subaction')) . 'subaction=' . ($_GET['action'] == 'new' ? 'newconfirm' : 'update'), 'post', 'onSubmit="return check_form();"') ?>
                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td>
                                            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td class="pageHeading" valign="top"><?= HEADING_TITLE ?></td>
                                                    <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                    <tr>
                                        <td class="formAreaTitle"><?= ($_GET['action'] == 'new' ? HEADING_TITLE_INSERT : HEADING_TITLE_UPDATE) ?></td>
                                    </tr>
                                    <tr>
                                        <td class="formArea">
                                            <table border="0" cellspacing="2" cellpadding="2">
                                                <? if ($_GET['action'] == 'edit') { ?>

                                                    <tr>
                                                        <td class="main"><?= ENTRY_PUBLISHERS_ID ?>:</td>
                                                        <td class="main"><?= (int)$_REQUEST['pID'] ?></td>
                                                        <td rowspan="15" valign="top">
                                                            <div id="div_content"></div>
                                                        </td>
                                                    </tr>
                                                <? } ?>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHERS_NAME ?>:</td>
                                                    <td class="main"><?= tep_draw_input_field('publishers_name', $publishers_name, 'maxlength="32"', false) ?></td>
                                                    <? if ($_GET['action'] != 'edit') { ?>
                                                        <td rowspan="14" valign="top">
                                                            <div id="div_content"></div>
                                                        </td>
                                                    <? } ?>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHERS_SUPPLIER ?>:</td>
                                                    <td class="main"><?= tep_draw_pull_down_menu('publishers_supplier', $supplier_array, $publisher_supplier_id, 'id="publishers_supplier"', false) ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHERS_STATUS ?>:</td>
                                                    <td class="main"><?
                                                        echo tep_draw_radio_field('rd_status', '1', $publishers_status) . '&nbsp;Active';
                                                        echo "&nbsp;&nbsp;";
                                                        echo tep_draw_radio_field('rd_status', '0', (!$publishers_status ? true : false)) . '&nbsp;Inactive';
                                                        ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHERS_TOP_UP_MODE ?>:</td>
                                                    <td class="main"><?= tep_draw_pull_down_menu('configuration[TOP_UP_MODE]', $top_up_mode_array, ($publishers_configuration_array['TOP_UP_MODE'] ? $publishers_configuration_array['TOP_UP_MODE'] : 'offgamers'), ' id="sel_top_up_mode" onchange="load_mode_content(this.value)" ') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHER_API_ID ?>:</td>
                                                    <td class="main"><?= tep_draw_input_field('configuration[PUBLISHER_API_ID]', $publisher_api_id, '', false) ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHERS_OGM_MERCHANT_ID ?>:</td>
                                                    <td class="main"><?= tep_draw_input_field('configuration[OGM_MERCHANT_ID]', $ogm_merchant_id, '', false) ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHERS_MIN_BALANCE_CURRENCY ?>:</td>
                                                    <td class="main"><?= tep_draw_pull_down_menu('configuration[MIN_BALANCE_CURRENCY]', $currencies_array, $min_balance_currency, false) ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHERS_MIN_BALANCE ?>:</td>
                                                    <td class="main"><?= tep_draw_input_field('configuration[MIN_BALANCE]', $min_balance, ' onKeyPress="return numbersOnly(this, event, 1)" ', false) ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHERS_LOW_BALANCE_NOTIFICATION ?>:</td>
                                                    <td class="main"><?= tep_draw_input_field('configuration[LOW_BALANCE_NOTIFICATION]', $low_balance_notification, '', false) ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_PUBLISHERS_TOP_UP_LIMIT ?>:</td>
                                                    <td class="main"><?= tep_draw_input_field('configuration[TOP_UP_LIMIT]', $top_up_limit, ' onKeyPress="return numbersOnly(this, event)" ', false) ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main"><?= ENTRY_SORT_ORDER ?></td>
                                                    <td class="main"><?= tep_draw_input_field('sort_order', ($publishers_sort_order == '' ? 50000 : $publishers_sort_order), 'size="6"') ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><?= ENTRY_REMARK ?></td>
                                                    <td class="main"><textarea name="publishers_remark" cols="50" rows="5"><?= $publishers_remark ?></textarea></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                </tr>
                                                <tr>
                                                    <td align="left" class="main" colspan="2"><?= ($_GET['action'] == 'new' ? '<input type="submit" class="inputButton" value="Insert" >' : '<input type="submit" class="inputButton" value="Update" >') . '&nbsp;' . ($_GET['action'] == 'new' ? '<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'pID', 'subaction'))) . '\'">' : ' <input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('action', 'pID', 'subaction'))) . '\'">') ?></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                    </tr>
                                </table>
                                </form>
                                <script language="javascript">
                                    
                                    function check_form() {
                                        //get param action 
                                        var queryString = window.location.search;
                                        var urlParams = new URLSearchParams(queryString);
                                        var action = urlParams.get('action');
                                        action = (action == 'new' ? 'add' : action);
                                        
                                        var error = 0;
                                        var publishers_name = document.publishers.publishers_name.value;
                                        var publishers_api_id = document.getElementsByName("configuration[PUBLISHER_API_ID]")[0].value;
                                        var publishers_merchant_id = document.getElementsByName("configuration[OGM_MERCHANT_ID]")[0].value;
                                        
                                        if (trim_str(publishers_name) == '') {
                                            error_message = "Publisher Name is required!";
                                            error = 1;
                                        }
                                        else if (trim_str(publishers_api_id) == '') {
                                            error_message = "Publisher API ID is required!";
                                            error = 1;
                                        }else if (trim_str(publishers_merchant_id) == '') {
                                            error_message = "Publisher Merchant ID is required!";
                                            error = 1;
                                        }

                                        if (error == 1) {
                                            alert(error_message);
                                            return false;
                                        }else{
                                            // confirmation box
                                            jquery_confirm_box("Are you sure to "+action+" this publisher?", 2, 0, "Confirm");
                                            jQuery('#jconfirm_submit').click(function () {
                                                document.forms["publishers"].submit();
                                            });
                                            return false;
                                        }

                                    }

                                    function load_mode_content(pass_class) {
                                        var display_html = '';

                                        jquery_confirm_box('<h1>Please wait...</h1>', 0, 0);
                                        jQuery.ajax({
                                            url: "publishers_xmlhttp.php?action=load_mode_content&pID=<?=(isset($_REQUEST['pID']) && (int)$_REQUEST['pID'] ? (int)$_REQUEST['pID'] : '')?>&class=" + pass_class,
                                            type: 'GET',
                                            dataType: 'json',
                                            timeout: 10000,
                                            error: function () {
                                                jQuery.unblockUI();
                                                jquery_confirm_box("Error loading JSON document", 1, 1, "Warning");
                                            },
                                            success: function (data) {
                                                jQuery.unblockUI();
                                                jQuery("div#div_content").html(data.html);
                                            }
                                        });
                                    }

                                    load_mode_content('<?=$publishers_configuration_array['TOP_UP_MODE']?>');
                                    //-->
                                </script>
                            </td>
                        </tr>
                        <?
                    } else {
                        $publishers_query_raw = "select p.publishers_id, p.publishers_name, p.publishers_status, p.last_modified,p.server_last_sync, p.sort_order from " . TABLE_PUBLISHERS . " p order by p.sort_order, p.publishers_name ASC";
                        $publishers_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $publishers_query_raw, $publishers_query_numrows);
                        $publishers_query = tep_db_query($publishers_query_raw);
                        ?>
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?= HEADING_TITLE ?></td>
                                        <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td valign="top">
                                <?= tep_draw_form('publishers', FILENAME_PUBLISHERS, tep_get_all_get_params(array('action')) . 'action=batch_upd', 'post', "onSubmit=\"return validate_sort_order()\"") ?>
                                <table border="0" width="100%" cellspacing="1" cellpadding="2">
                                    <tr>
                                        <td class="reportBoxHeading" width="50" align="center"><?= TABLE_HEADING_ID ?></td>
                                        <td class="reportBoxHeading" width="*%"><?= TABLE_HEADING_NAME ?></td>
                                        <td class="reportBoxHeading" width="100" align="center"><?= TABLE_HEADING_STATUS ?></td>
                                        <td class="reportBoxHeading" width="100" align="center"><?= TABLE_HEADING_SORT_ORDER ?></td>
                                        <td class="reportBoxHeading" width="130" align="center"><?= TABLE_HEADING_LAST_MODIFIED ?></td>
                                        <td class="reportBoxHeading" width="130" align="center">Last Sync</td>
                                        <td class="reportBoxHeading" width="55" align="center"><?= TABLE_HEADING_ACTION ?></td>
                                    </tr>
                                    <?
                                    $row_count = 0;
                                    while ($publishers_row = tep_db_fetch_array($publishers_query)) {
                                        $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                        ?>
                                        <tr class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                            <td class="reportRecords" valign="top" align="center"><?= $publishers_row['publishers_id'] ?></td>
                                            <td class="reportRecords" valign="top"><?= $publishers_row['publishers_name'] ?></td>
                                            <td class="reportRecords" valign="top" align="center"><?
                                                if ($publishers_row['publishers_status'] == '1') {
                                                    echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="javascript:void(confirmUpdateStatus(\'' . tep_href_link(FILENAME_PUBLISHERS, 'action=setflag&flag=0&pID=' . $publishers_row['publishers_id']) . '\'))">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
                                                } else {
                                                    echo '<a href="javascript:void(confirmUpdateStatus(\'' . tep_href_link(FILENAME_PUBLISHERS, 'action=setflag&flag=1&pID=' . $publishers_row['publishers_id']) . '\'));">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                                                }
                                                ?></td>
                                            <td class="reportRecords" valign="top" align="center"><?= tep_draw_input_field('event_record[' . $publishers_row['publishers_id'] . '][sort_order]', $publishers_row['sort_order'], 'size="6" id="event_record[' . $publishers_row['publishers_id'] . '][sort_order]" class="txt_sort_order" ') ?></td>
                                            <td class="reportRecords" valign="top" align="center"><?= $publishers_row['last_modified'] ?></td>
                                            <td class="reportRecords" valign="top" align="center"><?= $publishers_row['server_last_sync'] ?></td>
                                            <td class="reportRecords" valign="top" align="center">
                                                <a href="javascript:void(0);" onclick="<?= 'sync_publisher(' . $publishers_row['publishers_id'] . ');' ?>"><?= tep_image(DIR_WS_IMAGES . "icon_reset.gif", "Sync Server", "", "", 'align="top"') ?></a> <a href="<?= tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('pID', 'flag', 'action', 'subaction')) . 'pID=' . $publishers_row['publishers_id'] . '&action=edit') ?>"><?= tep_image(DIR_WS_ICONS . "edit.gif", "Edit", "", "", 'align="top"') ?></a> <a href="javascript:void(confirm_delete('<?= $publishers_row['publishers_name'] ?>', 'publisher', '<?= tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('pID', 'flag', 'action', 'subaction')) . 'pID=' . $publishers_row['publishers_id'] . '&subaction=deleteconfirm') ?>'))"><?= tep_image(DIR_WS_ICONS . "delete.gif", "Delete", "", "", 'align="top"') ?></a>
                                            </td>
                                        </tr>
                                        <? $row_count++;
                                    }
                                    ?>
                                    <tr>
                                        <td colspan="3"></td>
                                        <td align="center"><input type="submit" class="inputButton" value="Update"></td>
                                        <td colspan="2"></td>
                                    </tr>
                                </table>
                                </form>
                                <script>
                                    function validate_sort_order() {
                                        var return_flag = true;
                                        jQuery(".txt_sort_order").each(function () {
                                            if (!validateInteger(jQuery(this).val())) {
                                                jQuery(this).val('');
                                                return_flag = false;
                                                jQuery(this).focus();
                                                alert("Invalid sort order");
                                                return false;
                                            }
                                        });
                                        return return_flag;
                                    }

                                    function confirmUpdateStatus(location_url) {
                                        if (confirm('Are you sure to perform this update?')) {
                                            window.location.href = location_url;
                                        }
                                    }
                                </script>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
                                    <tr>
                                        <td class="smallText" valign="top"><?= $publishers_split->display_count($publishers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_PUBLISHERS) ?></td>
                                        <td class="smallText" align="right"><?= $publishers_split->display_links($publishers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y', 'cID', 'dis_id'))) ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td align="left"><?= '[ <a href="' . tep_href_link(FILENAME_PUBLISHERS, 'page=' . $_GET['page'] . '&action=new') . '" >' . LINK_ADD_PUBLISHERS . '</a> ]' ?></td>
                        </tr>
                        <?
                    }
                    ?>
                </table>
            </td>
            <!-- body_text_eof //-->
        </tr>
    </table>
    <!-- body_eof //-->

    <!-- footer //-->
    <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
    <!-- footer_eof //--><br>
    </body>
    </html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php');