<?php
/*
  	Group Discount
  	by hOZONE, <EMAIL>, http://hozone.cjb.net
  	
  	visit osCommerceITalia, http://www.oscommerceitalia.com
	
  	derived by:
  	Discount_Groups_v1.1, by <PERSON>, 2003/5/22
	
  	for:
  	osCom<PERSON><PERSON>, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
  	
  	Copyright (c) 2003 osCommerce
  	
  	Released under the GNU General Public License 
*/

require('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'direct_topup.php');
$direct_topup_obj = new direct_topup();

require(DIR_WS_CLASSES . 'log.php');
$system_log_object = new log_files($login_id);

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$subaction = (isset($_GET['subaction']) ? $_GET['subaction'] : '');
$languages = tep_get_languages();

if (tep_not_null($action)) {
	switch ($action) {
		case 'batch_upd':
			if (isset($_REQUEST['event_record']) && count($_REQUEST['event_record'])) {
				foreach ($_REQUEST['event_record'] as $publishers_id_loop => $publishers_sort_order_loop) {
					$publishers_data_sql = array(	'sort_order' => (int)$publishers_sort_order_loop['sort_order'],
													'last_modified' => 'now()');
					tep_db_perform(TABLE_PUBLISHERS, $publishers_data_sql, 'update', " publishers_id = '".$publishers_id_loop."' ");
				}
				$messageStack->add_session(TEXT_INFO_PUBLISHER_UPDATED, 'success');
			}
        	tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pID', 'action', 'subaction', 'flag'))));
			break;
		case 'setflag':
			if (isset($_REQUEST['pID']) && (int)$_REQUEST['pID'] > 0) {
				$publishers_data_sql = array(	'publishers_status' => (int)$_REQUEST['flag'] ,
													'last_modified' => 'now()');
				tep_db_perform(TABLE_PUBLISHERS, $publishers_data_sql, 'update', " publishers_id = '".(int)$_REQUEST['pID']."' ");
				$messageStack->add_session(TEXT_INFO_PUBLISHER_UPDATED, 'success');
			}
        	tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pID', 'action', 'subaction', 'flag'))));
			break;
	}
}

if (tep_not_null($subaction)) {

	$pID = (isset($_REQUEST['pID']) ? (int)$_REQUEST['pID'] : '' );
	$class = $direct_topup_obj->void_include_class($pID);
	if (tep_not_null($class) && tep_class_exists('dtu_'.$class)) {
		eval('$direct_topup_class_obj = new dtu_'.$class.'();');
	} else {
		$direct_topup_class_obj = new dtu_offgamers();
	}

	switch ($subaction) {
		case 'reset_amount':
			if (!isset($_REQUEST['pgID']) && !((int)$_REQUEST['pgID']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
      		} else {
      			$publishers_games_data_sql = array(	'publishers_games_today_topped_amount' => '0');
      			tep_db_perform(TABLE_PUBLISHERS_GAMES, $publishers_games_data_sql, 'update', " publishers_games_id = '".(int)$_REQUEST['pgID']."' ");
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=edit'));
      		}
			break;
		case 'delete_product':
			if (!isset($_REQUEST['pID']) && !((int)$_REQUEST['pID']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
      		} else if (!isset($_REQUEST['pgID']) && !((int)$_REQUEST['pgID']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
      		} else if (!isset($_REQUEST['product_id']) && !((int)$_REQUEST['product_id']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
      		} else {
      			tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_PRODUCTS . " WHERE publishers_games_id = '".(int)$_REQUEST['pgID']."' AND products_id = '".(int)$_REQUEST['product_id']."'");

      			$top_up_info_select_sql = "	SELECT top_up_info_id
      										FROM " . TABLE_TOP_UP_INFO . "
      										WHERE products_id = '".(int)$_REQUEST['product_id']."'";
				$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
				while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
					tep_db_query("	DELETE FROM " . TABLE_TOP_UP_INFO_LANG . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
				}

      			tep_db_query("DELETE FROM " . TABLE_TOP_UP_INFO . " WHERE products_id = '".(int)$_REQUEST['product_id']."'");

      			for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
					$memcache_obj->delete(TABLE_TOP_UP_INFO . '/products_id/' . (int)$_REQUEST['product_id'] . '/language/' . $languages[$i]['id'], 0);
				}

                #key:top_up_info/products_id/xxx
				$memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_TOP_UP_INFO . '/products_id/' . (int)$_REQUEST['product_id'], 0);

      			$messageStack->add_session(TEXT_INFO_PUBLISHER_GAMES_CONFIGURATION_DELETED, 'success');
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID', 'subaction')).'action=edit'));
      		}
			break;
		case 'delete':
			if (!isset($_REQUEST['pgID']) && !((int)$_REQUEST['pgID']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
      		} else {

      			$publishers_products_select_sql = "	SELECT products_id
      												FROM " . TABLE_PUBLISHERS_PRODUCTS . "
      												WHERE publishers_games_id = '".(int)$_REQUEST['pgID']."'";
				$publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
				while ($publishers_products_row = tep_db_fetch_array($publishers_products_result_sql)) {
	      			$top_up_info_select_sql = "	SELECT top_up_info_id
	      										FROM " . TABLE_TOP_UP_INFO . "
	      										WHERE products_id = '".(int)$publishers_products_row['products_id']."'";
					$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
					while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
						tep_db_query("DELETE FROM " . TABLE_TOP_UP_INFO_LANG . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
					}

	      			tep_db_query("DELETE FROM " . TABLE_TOP_UP_INFO . " WHERE products_id = '".(int)$publishers_products_row['products_id']."'");
      			}
      			tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_PRODUCTS . " WHERE publishers_games_id = '".(int)$_REQUEST['pgID']."'");
      			tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_GAMES . " WHERE publishers_games_id = '".(int)$_REQUEST['pgID']."'");

      			$messageStack->add_session(TEXT_INFO_PUBLISHER_GAMES_DELETED, 'success');
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
      		}

			break;
		case 'insertconfirm':
		case 'updateconfirm':
      		if (!isset($_REQUEST['pID']) || !((int)$_REQUEST['pID']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pID', 'pgID')).'action=edit&subaction=edit'));
      		} else if (!isset($_REQUEST['pgID']) || !((int)$_REQUEST['pgID']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
      		} else if (!isset($_REQUEST['product_id']) || !((int)$_REQUEST['product_id']>0)) {
                $messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=edit_product'));
      		} else if (!isset($_REQUEST['txt_amount']) || !((double)$_REQUEST['txt_amount']>0)) {
      			$messageStack->add_session(ERROR_INVALID_TOP_UP_AMOUNT);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=edit_product'));
      		} else if (!isset($_REQUEST['rd_account']) || ((int)$_REQUEST['rd_account']==0)) {
      			$messageStack->add_session(ERROR_INVALID_TOP_UP_INFO_SETTING);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=edit_product'));
			} else if (!$direct_topup_class_obj->validate_admin_customer_input($_REQUEST, $error_msg)) {
      			$messageStack->add_session($error_msg);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=edit_product'));
      		} else {
                $dtu_product_code = (isset($_REQUEST['txt_product_code']) ? tep_db_prepare_input($_REQUEST['txt_product_code']) : false);

				$rd_account = (isset($_REQUEST['rd_account']) ? $_REQUEST['rd_account'] : '');
				$rd_server = (isset($_REQUEST['rd_server']) ? $_REQUEST['rd_server'] : '');
				$rd_character = (isset($_REQUEST['rd_character']) ? $_REQUEST['rd_character'] : '');
				$rd_account_platform = (isset($_REQUEST['rd_account_platform']) ? $_REQUEST['rd_account_platform'] : '');
				$rd_info = (isset($_REQUEST['rd_info']) ? $_REQUEST['rd_info'] : '');

      			// check product
	      		$publishers_products_select_sql = "	SELECT publishers_games_id
			      									FROM " . TABLE_PUBLISHERS_PRODUCTS . "
			      									WHERE products_id = '".(int)$_REQUEST['product_id']."'";
				$publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
				if ($_REQUEST['subaction'] == 'insertconfirm' && tep_db_num_rows($publishers_products_result_sql)) {
	      			$messageStack->add_session(ERROR_DUPLICATE_PRODUCT_ASSIGNED);
	      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=new_product'));
				} else {
					$error_action = 'edit_product';
					if ($_REQUEST['subaction'] == 'insertconfirm') {
						$error_action = 'new_product';
					}

					// check if under bundle
					$check_share_bundle_select_sql = "	SELECT pb2.subproduct_id, pb2.bundle_id
														FROM " . TABLE_PRODUCTS_BUNDLES . " AS pb1
														INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb2
															ON pb1.bundle_id = pb2.bundle_id
														INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
															ON pdi.products_id = pb2.subproduct_id
																AND pdi.products_delivery_mode_id = '6' 
														WHERE pb1.subproduct_id = '".(int)$_REQUEST['product_id']."'
															AND pb1.subproduct_id <> pb2.subproduct_id ";
					$check_share_bundle_result_sql = tep_db_query($check_share_bundle_select_sql);
					while ($check_share_bundle_row = tep_db_fetch_array($check_share_bundle_result_sql)) {

						$check_top_up_info_array = $direct_topup_obj->get_admin_game_input($check_share_bundle_row['subproduct_id']);

						if (isset($check_top_up_info_array['account']) && (tep_not_null($rd_account) && (int)$rd_account) == $check_top_up_info_array['account']) {
							$messageStack->add_session(ERROR_UNABLE_UPDATE_DUE_TO_DIFF_SUB_BUNDLE . " - " . $check_share_bundle_row['bundle_id'], 'error');
							tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'test=1&action=edit&subaction='.$error_action));
						}

						if (isset($check_top_up_info_array['server']) && (tep_not_null($rd_server) && (int)$rd_server) == $check_top_up_info_array['server']) {
							$messageStack->add_session(ERROR_UNABLE_UPDATE_DUE_TO_DIFF_SUB_BUNDLE . " - " . $check_share_bundle_row['bundle_id'], 'error');
							tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'test=2&action=edit&subaction='.$error_action));
						}

						if (isset($check_top_up_info_array['character']) && (tep_not_null($rd_character) && (int)$rd_character) == $check_top_up_info_array['character']) {
							$messageStack->add_session(ERROR_UNABLE_UPDATE_DUE_TO_DIFF_SUB_BUNDLE . " - " . $check_share_bundle_row['bundle_id'], 'error');
							tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'test=3&action=edit&subaction='.$error_action));
						}

						if (isset($check_top_up_info_array['info_label']) && (tep_not_null($rd_info) && (int)$rd_info) == $check_top_up_info_array['info_label']) {
							$messageStack->add_session(ERROR_UNABLE_UPDATE_DUE_TO_DIFF_SUB_BUNDLE . " - " . $check_share_bundle_row['bundle_id'], 'error');
							tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'test=3&action=edit&subaction='.$error_action));
						}

						if (isset($check_top_up_info_array['account_platform']) && (tep_not_null($rd_account_platform) && (int)$rd_account_platform) == $check_top_up_info_array['account_platform']) {
							$messageStack->add_session(ERROR_UNABLE_UPDATE_DUE_TO_DIFF_SUB_BUNDLE . " - " . $check_share_bundle_row['bundle_id'], 'error');
							tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'test=3&action=edit&subaction='.$error_action));
						}
					}

	      			//check publisher
		      		$publishers_select_sql = "	SELECT publishers_id
		      									FROM " . TABLE_PUBLISHERS_GAMES . "
		      									WHERE publishers_games_id = '".(int)$_REQUEST['pgID']."'
		      										AND publishers_id = '".(int)$_REQUEST['pID']."'";
					$publishers_result_sql = tep_db_query($publishers_select_sql);
					if (tep_db_num_rows($publishers_result_sql)) {

			      		$publishers_products_select_sql = "	SELECT publishers_games_id
					      									FROM " . TABLE_PUBLISHERS_PRODUCTS . "
					      									WHERE publishers_games_id = '".(int)$_REQUEST['pgID']."'
					      										AND products_id = '".(int)$_REQUEST['product_id']."'";
						$publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
						if (!tep_db_num_rows($publishers_products_result_sql)) {
							$publishers_products_data_sql = array(	"publishers_games_id" => (int)$_REQUEST['pgID'],
																	"products_id" => (int)$_REQUEST['product_id']);
							tep_db_perform(TABLE_PUBLISHERS_PRODUCTS, $publishers_products_data_sql);
						}

						$top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'amount_type'";
						$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
						if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
							$top_up_info_array = array(	'top_up_info_value' => tep_db_prepare_input($_REQUEST['sel_amount_type']),
														'top_up_info_type_id' => '1');
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array, 'update', " top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."' ");
						} else {
							$top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
														'top_up_info_value' => tep_db_prepare_input($_REQUEST['sel_amount_type']),
														'top_up_info_type_id' => '1',
														'date_added' => 'now()',
														'top_up_info_key' => 'amount_type',
														'top_up_info_title' => tep_db_prepare_input("Amount Type"),
														'top_up_info_description' => tep_db_prepare_input("Top-up amount type")
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);
						}

						$top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'amount'";
						$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
						if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
							$top_up_info_array = array(	'top_up_info_value' => tep_db_prepare_input($_REQUEST['txt_amount']),
														'top_up_info_type_id' => '1'
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array, 'update', " top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."' ");
						} else {
							$top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
														'top_up_info_value' => tep_db_prepare_input($_REQUEST['txt_amount']),
														'top_up_info_type_id' => '1',
														'date_added' => 'now()',
														'top_up_info_key' => 'amount',
														'top_up_info_title' => tep_db_prepare_input("Base Amount"),
														'top_up_info_description' => tep_db_prepare_input("Top-up base Amount")
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);
						}

                        if ($dtu_product_code !== false) {  // Only Junnet used it
                            $top_up_info_select_sql = "	SELECT top_up_info_id
                                                        FROM ". TABLE_TOP_UP_INFO . " 
                                                        WHERE products_id = '".(int)$_REQUEST['product_id']."'
                                                            AND top_up_info_key = 'product_code'";
                            $top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
                            if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
                                $top_up_info_array = array(	'top_up_info_value' => $dtu_product_code,
                                                            'top_up_info_type_id' => '1'
                                                            );
                                tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array, 'update', " top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."' ");
                            } else {
                                $top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
                                                            'top_up_info_value' => $dtu_product_code,
                                                            'top_up_info_type_id' => '1',
                                                            'date_added' => 'now()',
                                                            'top_up_info_key' => 'product_code',
                                                            'top_up_info_title' => 'Product Code',
                                                            'top_up_info_description' => 'Product reference from Publisher'
                                                            );
                                tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);
                            }
                        }

						$top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'sync_publisher_character_flag'";
						$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
						if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
							$top_up_info_array = array(	'top_up_info_value' => (int)$_REQUEST['chk_sync_publisher_character'],
														'top_up_info_type_id' => '1'
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array, 'update', " top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."' ");
						} else {
							$top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
														'top_up_info_value' => (int)$_REQUEST['chk_sync_publisher_character'],
														'top_up_info_type_id' => '1',
														'date_added' => 'now()',
														'top_up_info_key' => 'sync_publisher_character_flag',
														'top_up_info_title' => tep_db_prepare_input("Sync publisher character"),
														'top_up_info_description' => tep_db_prepare_input("Sync publisher character")
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);
						}

						$memcache_obj->delete(TABLE_TOP_UP_INFO . '/'.(int)$_REQUEST['product_id'].'/sync_publisher_character_flag', 0);

                        $top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'used_as_text_field_flag'";
                        $top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
                        if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
                            $top_up_info_array = array(
                                'top_up_info_value' => (int)$_REQUEST['chk_used_as_text_field'],
                                'top_up_info_type_id' => '1'
                            );
                            tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array, 'update', " top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."' ");
                        } else {
                            $top_up_info_array = array(
                                'products_id' => (int)$_REQUEST['product_id'],
                                'top_up_info_value' => (int)$_REQUEST['chk_used_as_text_field'],
                                'top_up_info_type_id' => '1',
                                'date_added' => 'now()',
                                'top_up_info_key' => 'used_as_text_field_flag',
                                'top_up_info_title' => tep_db_prepare_input("Use this field as text field"),
                                'top_up_info_description' => tep_db_prepare_input("Use this field as text field")
                            );
                            tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);
                        }

                        $memcache_obj->delete(TABLE_TOP_UP_INFO . '/'.(int)$_REQUEST['product_id'].'/used_as_text_field_id_flag', 0);

						$top_up_info_id = 0;
						$top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'account'";
						$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
						if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {

							tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO_LANG . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");

							if ($rd_account==1) {
								tep_db_query("UPDATE ". TABLE_TOP_UP_INFO . " SET sort_order = '".(isset($_REQUEST['txt_sort_order_account']) ? (int)$_REQUEST['txt_sort_order_account'] : 50000 )."', top_up_info_type_id = '2' WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							} else {
								tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							}

							$top_up_info_id = (int)$top_up_info_row['top_up_info_id'];

						} else if ($rd_account==1) {
							$top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
														'top_up_info_value' => '',
														'top_up_info_type_id' => '2',
														'date_added' => 'now()',
														'top_up_info_key' => 'account',
														'sort_order' => (int)$_REQUEST['txt_sort_order_account'],
														'top_up_info_title' => tep_db_prepare_input("Customer's Account"),
														'top_up_info_description' => tep_db_prepare_input("Customer's Account")
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);
							$top_up_info_id = tep_db_insert_id();
						}

						if ($rd_account==1 && $top_up_info_id > 0) {
							for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
								$top_up_info_lang_array = array('top_up_info_id' => (int)$top_up_info_id,
																'top_up_info_display' => tep_db_prepare_input($_REQUEST['txt_label_account'][$languages[$i]['id']]),
																'languages_id' => $languages[$i]['id']
																);
								tep_db_perform(TABLE_TOP_UP_INFO_LANG, $top_up_info_lang_array);
							}
						}

                        $top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'retype_account_flag'";
						$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
						if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
							$top_up_info_array = array(	'top_up_info_value' => (int)$_REQUEST['chk_retype_account'],
														'top_up_info_type_id' => '1'
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array, 'update', " top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."' ");
						} else {
							$top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
														'top_up_info_value' => (int)$_REQUEST['chk_retype_account'],
														'top_up_info_type_id' => '1',
														'date_added' => 'now()',
														'top_up_info_key' => 'retype_account_flag',
														'top_up_info_title' => tep_db_prepare_input("Retype Account"),
														'top_up_info_description' => tep_db_prepare_input("Retype Account")
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);
						}

						$memcache_obj->delete(TABLE_TOP_UP_INFO . '/'.(int)$_REQUEST['product_id'].'/retype_account_flag', 0);

						$top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'server'";
						$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
						if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {

							tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO_LANG . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");

							if ($rd_server==1) {
								tep_db_query("UPDATE ". TABLE_TOP_UP_INFO . " SET sort_order = '".(isset($_REQUEST['txt_sort_order_server']) ? (int)$_REQUEST['txt_sort_order_server'] : 50000 )."', top_up_info_type_id = '2' WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							} else {
								tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							}

							$top_up_info_id = (int)$top_up_info_row['top_up_info_id'];

						} else if ($rd_server==1) {
							$top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
														'top_up_info_value' => '',
														'top_up_info_type_id' => '2',
														'date_added' => 'now()',
														'top_up_info_key' => 'server',
														'sort_order' => (int)$_REQUEST['txt_sort_order_server'],
														'top_up_info_title' => tep_db_prepare_input("Server"),
														'top_up_info_description' => tep_db_prepare_input("Customer's game servers")
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);

							$top_up_info_id = tep_db_insert_id();
						}

						if ($rd_server==1 && $top_up_info_id > 0) {
							for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
								$top_up_info_lang_array = array('top_up_info_id' => (int)$top_up_info_id,
																'top_up_info_display' => tep_db_prepare_input($_REQUEST['txt_label_server'][$languages[$i]['id']]),
																'languages_id' => $languages[$i]['id']
																);
								tep_db_perform(TABLE_TOP_UP_INFO_LANG, $top_up_info_lang_array);
							}
						}

						$top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'character'";
						$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
						if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {

							tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO_LANG . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");

							if ($rd_character==1) {
								tep_db_query("UPDATE ". TABLE_TOP_UP_INFO . " SET sort_order = '".(isset($_REQUEST['txt_sort_order_character']) ? (int)$_REQUEST['txt_sort_order_character'] : 50000 )."', top_up_info_type_id = '2' WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							} else {
								tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							}
							$top_up_info_id = (int)$top_up_info_row['top_up_info_id'];

						} else if ($rd_character==1) {
							$top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
														'top_up_info_value' => '',
														'top_up_info_type_id' => '2',
														'date_added' => 'now()',
														'top_up_info_key' => 'character',
														'sort_order' => (int)$_REQUEST['txt_sort_order_character'],
														'top_up_info_title' => tep_db_prepare_input("Customer's character"),
														'top_up_info_description' => tep_db_prepare_input("Customer's character")
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);

							$top_up_info_id = tep_db_insert_id();
						}

						if ($rd_character==1 && $top_up_info_id > 0) {
							for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
								$top_up_info_lang_array = array('top_up_info_id' => (int)$top_up_info_id,
																'top_up_info_display' => tep_db_prepare_input($_REQUEST['txt_label_character'][$languages[$i]['id']]),
																'languages_id' => $languages[$i]['id']
																);
								tep_db_perform(TABLE_TOP_UP_INFO_LANG, $top_up_info_lang_array);
							}
						}

						$top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'info_label'";
						$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
						if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {

							tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO_LANG . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");

							if ($rd_info==1) {
								tep_db_query("UPDATE ". TABLE_TOP_UP_INFO . " SET sort_order = '".(isset($_REQUEST['txt_sort_order_character']) ? (int)$_REQUEST['txt_sort_order_character'] : 50000 )."', top_up_info_type_id = '2' WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							} else {
								tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							}
							$top_up_info_id = (int)$top_up_info_row['top_up_info_id'];

						} else if ($rd_info==1) {
							$top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
														'top_up_info_value' => '',
														'top_up_info_type_id' => '2',
														'date_added' => 'now()',
														'top_up_info_key' => 'info_label',
														'sort_order' => (int)$_REQUEST['txt_sort_order_info'],
														'top_up_info_title' => tep_db_prepare_input("Information"),
														'top_up_info_description' => tep_db_prepare_input("Information")
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);

							$top_up_info_id = tep_db_insert_id();
						}

						if (isset($_REQUEST['txt_label_info']) && count($_REQUEST['txt_label_info'])) {
							if ($rd_info==1 && $top_up_info_id > 0) {
								for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
									$top_up_info_lang_array = array('top_up_info_id' => (int)$top_up_info_id,
																	'top_up_info_display' => tep_db_prepare_input($_REQUEST['txt_label_info'][$languages[$i]['id']]),
																	'languages_id' => $languages[$i]['id']
																	);
									tep_db_perform(TABLE_TOP_UP_INFO_LANG, $top_up_info_lang_array);
								}
							}
						}

						$top_up_info_select_sql = "	SELECT top_up_info_id
													FROM ". TABLE_TOP_UP_INFO . " 
													WHERE products_id = '".(int)$_REQUEST['product_id']."'
														AND top_up_info_key = 'account_platform'";
						$top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
						if ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {

							tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO_LANG . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");

							if ($rd_account_platform==1) {
								tep_db_query("UPDATE ". TABLE_TOP_UP_INFO . " SET sort_order = '".(isset($_REQUEST['txt_sort_order_account_platform']) ? (int)$_REQUEST['txt_sort_order_account_platform'] : 50000 )."', top_up_info_type_id = '2' WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							} else {
								tep_db_query("DELETE FROM ". TABLE_TOP_UP_INFO . " WHERE top_up_info_id = '".(int)$top_up_info_row['top_up_info_id']."'");
							}
							$top_up_info_id = (int)$top_up_info_row['top_up_info_id'];

						} else if ($rd_account_platform==1) {
							$top_up_info_array = array(	'products_id' => (int)$_REQUEST['product_id'],
														'top_up_info_value' => '',
														'top_up_info_type_id' => '2',
														'date_added' => 'now()',
														'top_up_info_key' => 'account_platform',
														'sort_order' => (int)$_REQUEST['txt_sort_order_account_platform'],
														'top_up_info_title' => tep_db_prepare_input("Account Platform"),
														'top_up_info_description' => tep_db_prepare_input("Account Platform")
														);
							tep_db_perform(TABLE_TOP_UP_INFO, $top_up_info_array);

							$top_up_info_id = tep_db_insert_id();
						}

						if (isset($_REQUEST['txt_label_account_platform']) && count($_REQUEST['txt_label_account_platform'])) {
							if ($rd_account_platform==1 && $top_up_info_id > 0) {
								for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
									$top_up_info_lang_array = array('top_up_info_id' => (int)$top_up_info_id,
																	'top_up_info_display' => tep_db_prepare_input($_REQUEST['txt_label_account_platform'][$languages[$i]['id']]),
																	'languages_id' => $languages[$i]['id']
																	);
									tep_db_perform(TABLE_TOP_UP_INFO_LANG, $top_up_info_lang_array);
								}
							}
						}
					}

					if ($_REQUEST['subaction']=='insertconfirm') {
						$messageStack->add_session(TEXT_INFO_PUBLISHER_GAMES_CONFIGURATION_INSERTED, 'success');
					} else {
						for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
							$memcache_obj->delete(TABLE_TOP_UP_INFO . '/products_id/' . (int)$_REQUEST['product_id'] . '/language/' . $languages[$i]['id'], 0);
						}
						$messageStack->add_session(TEXT_INFO_PUBLISHER_GAMES_CONFIGURATION_UPDATED, 'success');
					}
				}
        	}

            #key:top_up_info/products_id/xxx
			$memcache_obj->delete(CFG_MEMCACHE_KEY_PREFIX . TABLE_TOP_UP_INFO . '/products_id/' . (int)$_REQUEST['product_id'], 0);
			$memcache_obj->delete(TABLE_TOP_UP_INFO . '/' . (int)$_REQUEST['product_id'] . '/sync_publisher_character_flag', 0);

			for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
				$memcache_obj->delete(TABLE_TOP_UP_INFO . '/products_id/' . (int)$_REQUEST['product_id'] . '/language/' . $languages[$i]['id'], 0);
			}

        	tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID', 'product_id')).'action=edit&subaction=edit_product&pgID='.$_REQUEST['pgID'].'&pID='.$_REQUEST['pID'].'&product_id=' . $_REQUEST['product_id']));
        	break;
		case 'edit_product':
			$publishers_game_select_sql = "	SELECT publishers_game
											FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
											WHERE publishers_games_id = '".(int)$_REQUEST['pgID']."'
												AND publishers_id = '".(int)$_REQUEST['pID']."'";
			$publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
			if (!$publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
				$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
				tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction'))));
			}

			$publishers_products_select_sql = "	SELECT products_id
												FROM " . TABLE_PUBLISHERS_PRODUCTS . "
												WHERE products_id = '".(int)$_REQUEST['product_id']."'
													AND publishers_games_id = '".(int)$_REQUEST['pgID']."'";
			$publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
			if (!$publishers_products_row = tep_db_fetch_array($publishers_products_result_sql)) {
				$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
				tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction'))));
			}

			break;
      	case 'update':
      		if (!isset($_REQUEST['pID']) || !((int)$_REQUEST['pID']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pID', 'pgID')).'action=edit&subaction=edit'));
      		} else if (!isset($_REQUEST['sel_categories_games']) || !((int)$_REQUEST['sel_categories_games']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pID', 'pgID')).'action=edit&subaction=new'));
      		} else if (!isset($_REQUEST['pgID']) || !((int)$_REQUEST['pgID']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
      		} else if (!isset($_REQUEST['publishers_game']) || !tep_not_null($_REQUEST['publishers_game'])) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=edit'));
			} else if (!$direct_topup_class_obj->validate_admin_game_input($_REQUEST, $error_msg)) {
      			$messageStack->add_session($error_msg);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=new'));
      		} else {
	      		$error = false;
      			$publishers_game_data_sql = array(	'publishers_game' => tep_db_prepare_input($_REQUEST['publishers_game']),
      												'categories_id' => (int)$_REQUEST['sel_categories_games'],
      												'publishers_games_status' => (int)$_REQUEST['publishers_games_status'],
      												'publishers_games_daily_limit' => (double)$_REQUEST['publishers_top_up_daily_limit'],
      												'publishers_games_pending_message' => tep_db_prepare_input($_REQUEST['txt_pending_message']),
													'publishers_games_reloaded_message' => tep_db_prepare_input($_REQUEST['txt_reloaded_message']),
													'publishers_games_failed_message' => tep_db_prepare_input($_REQUEST['txt_failed_message']),
      												'publishers_games_remark' => tep_db_prepare_input((isset($_REQUEST['txt_remark']) ? $_REQUEST['txt_remark'] : ''))
      											);

                if (!empty($_REQUEST['txt_input_server_list'])) {
                    $servers = (empty($data) ? explode("\r\n", $_REQUEST['txt_input_server_list']) : []);
                    $server_list = [];
                    foreach ($servers as $server) {
                        if ($server !== ''){
                            $server_list[$server] = $server;
                        }
                    }
                    $_REQUEST['txt_server_list'] = json_encode($server_list);
                }

                if(!empty($_REQUEST['txt_server_list'])){
                  $publishers_game_data_sql['publishers_server'] =  $_REQUEST['txt_server_list'];
                }

      			tep_db_perform(TABLE_PUBLISHERS_GAMES, $publishers_game_data_sql , 'update', " publishers_games_id = '".(int)$_REQUEST['pgID']."' ");

				$delete_publishers_games_configuration_sql = "	DELETE FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . " 
																WHERE publishers_games_id = '".(int)$_REQUEST['pgID']."'";
				tep_db_query($delete_publishers_games_configuration_sql);

				if (isset($_REQUEST['publishers_games_configuration']) && count($_REQUEST['publishers_games_configuration'])) {
					foreach ($_REQUEST['publishers_games_configuration'] as $publishers_games_configuration_key_loop => $publishers_games_configuration_data_loop) {
						$publishers_games_configuration_data_sql = array(
                                'publishers_games_configuration_value' => tep_db_prepare_input($publishers_games_configuration_data_loop),
                                'publishers_games_id' => (int)$_REQUEST['pgID'],
                                'publishers_games_configuration_key' => tep_db_prepare_input($publishers_games_configuration_key_loop),
                                'last_modified' => 'now()'
                        );
						tep_db_perform(TABLE_PUBLISHERS_GAMES_CONFIGURATION, $publishers_games_configuration_data_sql);
					}
				}

				$memcache_obj->delete(TABLE_PUBLISHERS_GAMES_CONFIGURATION . '/catagories_id/'.(int)$_REQUEST['sel_categories_games'].'/convert_customer_id_to_email_flag', 0);
				$memcache_obj->delete(TABLE_TOP_UP_INFO . '/publishers_products/'.(int)$_REQUEST['pgID'].'/publisher', 0);

				$messageStack->add_session(TEXT_INFO_PUBLISHER_UPDATED, 'success');
				tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
        	}
        	break;
      	case 'deleteconfirm':
      		$error = false;

      		$publisher_id = tep_db_prepare_input($_GET['pID']);

        	$count_orders_id_array = array();
        	// check customers groups has payment method setting
        	$orders_top_up_select_sql = "	SELECT op.orders_id 
			        						FROM " . TABLE_ORDERS_TOP_UP . " AS otu
			        						INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
			        							ON otu.orders_products_id = op.orders_products_id
											WHERE otu.publishers_id = '".(int)$publisher_id."' 
												AND otu.top_up_status = 1
											GROUP BY op.orders_id
											LIMIT 4";
        	$orders_top_up_result_sql = tep_db_query($orders_top_up_select_sql);
        	while ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result_sql)) {
      			$count_orders_id_array[] = '<a href="'.tep_href_link(FILENAME_ORDERS, 'oID='.$orders_top_up_row['orders_id'].'&action=edit').'">'.$orders_top_up_row['orders_id'].'</a>';
        	}

        	if (count($count_orders_id_array)) {
        		$messageStack->add_session(sprintf(ERROR_PUBLISHER_HAS_PENDING_TOPUP, implode(", ", $count_orders_id_array).( count($count_orders_id_array) > 3 ? '...' : '')));
				$error = true;
			} else {
				$messageStack->add(TEXT_INFO_PUBLISHER_DELETED, 'success');

				tep_db_query("DELETE FROM " . TABLE_PUBLISHERS . " where publishers_id= " . $publisher_id);

	        	tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_CONFIGURATION . " WHERE publishers_id= " . $publisher_id);

	        	$publishers_games_select_sql = "SELECT publishers_games_id
	        									FROM " . TABLE_PUBLISHERS_GAMES . "
	        									WHERE publishers_id = '" . $publisher_id . "'";
	        	$publishers_games_select_sql = tep_db_query($publishers_games_select_sql);
	        	while ($publishers_games_row = tep_db_fetch_array($publishers_games_select_sql)) {
	        		tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_PRODUCTS . " WHERE publishers_games_id= " . $publishers_games_row['publishers_games_id']);
	        	}
	        	tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_GAMES . " WHERE publishers_id= " . $publisher_id);
	        	tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_CONFIGURATION . " WHERE publishers_id= " . $publisher_id);

	        	$messageStack->add_session(TEXT_INFO_PUBLISHER_DELETED, 'success');
			}

			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pID', 'action', 'subaction'))));

        	break;
      	case 'newconfirm':
      		if (!isset($_REQUEST['pID']) && !((int)$_REQUEST['pID']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pID', 'pgID')).'action=edit&subaction=new'));
      		} else if (!isset($_REQUEST['sel_categories_games']) && !((int)$_REQUEST['sel_categories_games']>0)) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pID', 'pgID')).'action=edit&subaction=new'));
      		} else if (!isset($_REQUEST['publishers_game']) && !tep_not_null($_REQUEST['publishers_game'])) {
      			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_GAMES_DATA);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=new'));
      		} else if (!$direct_topup_class_obj->validate_admin_game_input($_REQUEST, $error_msg)) {
      			$messageStack->add_session($error_msg);
      			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')).'action=edit&subaction=new'));
      		} else {
	      		$error = false;

      			$publishers_game_data_sql = array(	'publishers_game' => tep_db_prepare_input($_REQUEST['publishers_game']),
      												'publishers_id' => (int)$_REQUEST['pID'],
      												'categories_id' => (int)$_REQUEST['sel_categories_games'],
      												'publishers_games_status' => (int)$_REQUEST['publishers_games_status'],
      												'publishers_games_daily_limit' => (double)$_REQUEST['publishers_top_up_daily_limit'],
      												//'publishers_games_today_topped_amount' => (double)$_REQUEST['publishers_top_up_daily_topped'],
      												'publishers_games_pending_message' => tep_db_prepare_input($_REQUEST['txt_pending_message']),
													'publishers_games_reloaded_message' => tep_db_prepare_input($_REQUEST['txt_reloaded_message']),
													'publishers_games_failed_message' => tep_db_prepare_input($_REQUEST['txt_failed_message']),
      												'publishers_games_remark' => tep_db_prepare_input((isset($_REQUEST['txt_remark']) ? $_REQUEST['txt_remark'] : '')));
				tep_db_perform(TABLE_PUBLISHERS_GAMES, $publishers_game_data_sql);

				$messageStack->add_session(TEXT_INFO_PUBLISHER_INSERTED, 'success');
				tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pgID')).'action=edit'));
        	}
        	break;
	}
}
$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
<script language="javascript" src="includes/javascript/jquery.js"></script>
<script language="javascript" src="includes/javascript/php.packed.js"></script>
<script language="javascript" src="includes/javascript/customer_xmlhttp.js"></script>
<script language="javascript" src="includes/javascript/modal_win.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  	<tr>
    	<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    		<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
			<!-- left_navigation //-->
			<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
			<!-- left_navigation_eof //-->
    		</table>
    	</td>
		<!-- body_text //-->
    	<td width="100%" valign="top">
    		<table border="0" width="100%" cellspacing="0" cellpadding="2">
<? if ($action == 'edit') {
		if (isset($_REQUEST['pID']) && (int)$_REQUEST['pID'] > 0) {
			$publishers_select_sql = "	SELECT publishers_id, publishers_name, publishers_status, sort_order
										FROM " . TABLE_PUBLISHERS . "
										WHERE publishers_id = '".(int)$_REQUEST['pID']."'";
			$publishers_result_sql = tep_db_query($publishers_select_sql);
			if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) { ?>
				<tr>
        			<td>
        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          					<tr>
            					<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          					</tr>
        				</table>
        			</td>
      			</tr>
<?				if ($subaction == 'new' || $subaction == 'edit') { ?>
      			<tr>
        			<td>
        				<?=tep_draw_form('publishers_games', FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('subaction')) . 'subaction=' . ($_GET['subaction'] == 'new' ? 'newconfirm':'update'), 'post', 'onSubmit="return check_form();"')?>
<?
					  		$direct_topup_array = array('publishers_id'=> (int)$_REQUEST['pID'],
					  									'publishers_games_id'=> (int)$_REQUEST['pgID']);
							echo @$direct_topup_class_obj->draw_admin_game_input(($subaction=='new'?'new_game':'edit_game'), $direct_topup_array);
?>
			      		</form>
        			</td>
      			</tr>
<?				} else if ($subaction == 'new_product' || $subaction == 'edit_product') { ?>
      			<tr>
        			<td>
        				<?=tep_draw_form('publishers', FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('subaction')) . 'subaction=' . ($_GET['subaction'] == 'new_product' ? 'insertconfirm':'updateconfirm'), 'post', 'onSubmit="return check_form();"')?>
        				<table border="0" width="100%" cellspacing="0" cellpadding="2">
			      			<tr>
			        			<td class="formAreaTitle"><?=($subaction == 'new_product' ? HEADING_TITLE_GAMES_CONFIGURATION_INSERT : HEADING_TITLE_GAMES_CONFIGURATION_UPDATE)?>&nbsp;-&nbsp;<?=$publishers_game_name . '&nbsp;(&nbsp;'.$publishers_row['publishers_name'].'&nbsp;)'?></td>
			      			</tr>
			      			<tr>
			        			<td class="formArea">
<?
					  		$direct_topup_array = array('publishers_id'=> (int)$_REQUEST['pID'],
					  									'publishers_games_id'=> (int)$_REQUEST['pgID'],
					  									'products_id'=> (int)$_REQUEST['product_id']);
							echo @$direct_topup_class_obj->draw_admin_customer_input(($subaction=='new_product'?'new_product':'edit_product'), $direct_topup_array);
?>
			        			</td>
			      			</tr>
			      			<tr>
			        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      			</tr>
			      		</table>
			      		</form>
			      		<script>
			      			function load_product_cat_path(pass_id) {
								jQuery.get("custom_product_xmlhttp.php?action=get_full_products_categories_path&pID="+pass_id, function(xml){
									jQuery("div#div_cat_path").text(jQuery(xml).find("products_cat_path").text());
								});
			      			}

							function getReturnedValue(received_val) {
								document.getElementById('games_product_id').value = received_val;
								load_product_cat_path(received_val);
							}
			      		</script>
        			</td>
      			</tr>
<?				} ?>
				<tr>
        			<td valign="top">
        				<table border="0" width="100%" cellspacing="1" cellpadding="2">
			      			<tr>
			        			<td class="formAreaTitle" colspan="4"><?=HEADING_TITLE_PUBLISHERS_GAMES?>&nbsp;-&nbsp;<?=$publishers_row['publishers_name'] . '('.$publishers_row['publishers_id'].')'?></td>
			      			</tr>
           					<tr>
           						<td class="reportBoxHeading" width="50px" align="center"><?=TABLE_HEADING_ID?></td>
           						<td class="reportBoxHeading" width="200px"><?=TABLE_HEADING_GAMES?></td>
           						<td class="reportBoxHeading" width="5%" align="center"><?=TABLE_HEADING_ACTION?></td>
           						<td class="reportBoxHeading" width="*%"><?=TABLE_HEADING_GAMES_CONFIGURATION?></td>
		   					</tr>
<?
	$row_count = 0;
	$publishers_games_select_sql = "SELECT publishers_games_id, publishers_game, cd.categories_name, pg.publishers_id, pg.publishers_games_status
									FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION. " as cd
										ON pg.categories_id = cd.categories_id 
											AND cd.language_id='1'
									WHERE publishers_id = '".$publishers_row['publishers_id']."'";
	$publishers_games_result_sql = tep_db_query($publishers_games_select_sql);
	if (tep_db_num_rows($publishers_games_result_sql)) {
	    while ($publishers_games_row = tep_db_fetch_array($publishers_games_result_sql)) {
	    	$row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
							<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
								<td class="reportRecords" valign="top" align="center">
									<a href="<?=tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('pID', 'flag', 'action', 'subaction')) . 'pID='.$publishers_games_row['publishers_id'].'&action=edit')?>" target="_new"><?=$publishers_games_row['publishers_id']?></a>
								</td>
								<td class="reportRecords" valign="top"><span<?=' class="'.($publishers_games_row['publishers_games_status']? '' : 'redIndicator' ).'" '?>><?=$publishers_games_row['categories_name'] . " (".$publishers_games_row['publishers_game'] .")"?></span></td>
								<td class="reportRecords" valign="top" align="center" valign="top">
									<a href="<?=tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pgID', 'pID', 'flag', 'action', 'subaction', 'product_id')) . 'pID='.$publishers_games_row['publishers_id'].'&pgID='.$publishers_games_row['publishers_games_id'].'&action=edit&subaction=edit')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
									<a href="javascript:void(confirm_action('<?=JS_CONFIRM_DELETE?>', '<?=tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pgID', 'pID', 'flag', 'action', 'subaction', 'product_id')) . 'pID='.$publishers_games_row['publishers_id'].'&pgID='.$publishers_games_row['publishers_games_id'].'&action=edit&subaction=delete')?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
								</td>
								<td class="reportRecords" valign="top">
									<table width="95%">
										<tr>
											<td colspan="5"><div style="border-bottom: 1px solid rgb(153, 102, 0); font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
										</tr>
										<tr>
											<td class="reportRecords" width="80px" align="center"><a href="<?=tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pID', 'pgID', 'flag', 'action', 'subaction', 'product_id')) . 'pID='.$publishers_games_row['publishers_id'].'&pgID='.$publishers_games_row['publishers_games_id'].'&action=edit&subaction=new_product')?>"><?=LINK_ADD_NEW_PRODUCTS?></a></td>
											<td class="reportRecords" width="*%"><?=TABLE_HEADING_GAMES_PRODUCTS?></td>
											<td class="reportRecords" width="120px" align="center"><?=TABLE_HEADING_UNIQUE_PRODUCT_CODE?></td>
											<td class="reportRecords" width="100px" align="center"><?=TABLE_HEADING_TOP_UP_AMOUNT?></td>
											<td class="reportRecords" width="100px" align="center"><?=TABLE_HEADING_SUPPORT_DIRECT_TOP_UP?></td>
										</tr>
										<tr>
											<td colspan="5"><div style="border-bottom: 1px solid rgb(153, 102, 0); font-size: 1px; height: 1px; padding-top: 3px;"></div></td>
										</tr>
<?
			$publishers_products_select_sql = "	SELECT p.products_id, p.products_cat_path, pd.products_name, p.products_cat_id_path 
												FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
												LEFT JOIN " . TABLE_PRODUCTS . " AS p 
													ON pp.products_id = p.products_id
												LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
													ON pp.products_id = pd.products_id
												WHERE pp.publishers_games_id = '".$publishers_games_row['publishers_games_id']."'
													AND pd.language_id = 1 
													AND pd.products_name <> ''";
			$publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
			while ($publishers_products_row = tep_db_fetch_array($publishers_products_result_sql)) {
				$get_top_up_info_array = $direct_topup_obj->get_top_up_info($publishers_products_row['products_id']);
				$is_supported_by_direct_top_up = $direct_topup_obj->check_is_supported_by_direct_top_up($publishers_products_row['products_id']);
?>
										<tr>
											<td class="reportRecords" width="30px" align="center">
												<a href="<?=tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pID', 'pgID', 'flag', 'action', 'subaction', 'product_id')) . 'pID='.$publishers_games_row['publishers_id'].'&pgID='.$publishers_games_row['publishers_games_id'].'&product_id='.$publishers_products_row['products_id'].'&action=edit&subaction=edit_product')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
												<a href="javascript:void(confirm_action('<?=JS_CONFIRM_DELETE?>', '<?=tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pID', 'flag', 'action', 'subaction', 'product_id')) . 'pID='.$publishers_games_row['publishers_id'].'&pgID='.$publishers_games_row['publishers_games_id'].'&product_id='.$publishers_products_row['products_id'].'&action=edit&subaction=delete_product')?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
											</td>
											<td class="reportRecords"><a href="<?=tep_href_link(FILENAME_CATEGORIES, 'cPath='.rtrim(ltrim($publishers_products_row['products_cat_id_path'],'_'),'_') . '&pID=' . $publishers_products_row['products_id'])?>" target="_new"><?
												echo $publishers_products_row['products_cat_path'] . ' > ' . $publishers_products_row['products_name'] . " (".$publishers_products_row['products_id'].")";
											?></a></td>
											<td class="reportRecords" align="center"><?=$get_top_up_info_array['product_code']['top_up_info_value']?></td>
											<td class="reportRecords" align="center"><?=$get_top_up_info_array['amount']['top_up_info_value'] . ( tep_not_null($get_top_up_info_array['amount']['top_up_info_value']) ? '<br>(' . $get_top_up_info_array['amount_type']['top_up_info_value'].')' : '')?></td>
											<td class="reportRecords" align="center"><?=(tep_not_null($is_supported_by_direct_top_up) && $is_supported_by_direct_top_up ?'Yes':'<span class="redIndicator">No</span>')?></td>
										</tr>
<?			} ?>
									</table>
								</td>
							</tr>
<?			$row_count++;
		}
	} else { ?>
							<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
								<td class="reportRecords" valign="top" align="left" colspan="4"><?=TEXT_INFO_NO_RECORD?></td>
							</tr>
<?	} ?>
		   				</table>
			      	</td>
			    </tr>
      			<tr>
        			<td>
        				<?=tep_draw_form('publishers', FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action')) . 'action=edit&subaction=new', 'post', "onSubmit=\"return validate_sort_order()\"")?>
        				<input type="submit" class="inputButton" value="Insert">
		   				</form>
        			</td>
      			</tr>
      			<tr>
        			<td>
        				<a href="<?=tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pID')))?>" >[ <?=LINK_BACK_PUBLISHERS_GAMES?> ]</a>
        			</td>
      			</tr>
<? 			} else {
				$messageStack->add_session(ERROR_PUBLISHERS_NOT_FOUND);
				tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pID'))));
			}
		} else {
			$messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
			tep_redirect(tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction', 'pID'))));
		}
 	} else {

		$publishers_query_raw = "select p.publishers_id, p.publishers_name, p.publishers_status, p.last_modified, p.sort_order from " . TABLE_PUBLISHERS . " p order by p.sort_order, p.publishers_name ASC";
	    $publishers_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $publishers_query_raw, $publishers_query_numrows);
	    $publishers_query = tep_db_query($publishers_query_raw);
?>
      			<tr>
        			<td>
        				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          					<tr>
            					<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          					</tr>
        				</table>
        			</td>
      			</tr>
				<tr>
        			<td valign="top">
        				<?=tep_draw_form('publishers', FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action')) . 'action=batch_upd', 'post', "onSubmit=\"return validate_sort_order()\"")?>
        				<table border="0" width="100%" cellspacing="1" cellpadding="2">
           					<tr>
           						<td class="reportBoxHeading" width="50" align="center"><?=TABLE_HEADING_ID?></td>
           						<td class="reportBoxHeading" width="150px"><?=TABLE_HEADING_PUBLISHERS_NAME?></td>
           						<td class="reportBoxHeading" width="*%"><?=TABLE_HEADING_GAMES?></td>
				                <td class="reportBoxHeading" width="5%" align="center"><?=TABLE_HEADING_ACTION?></td>
		   					</tr>
<?
	$row_count = 0;
    while ($publishers_row = tep_db_fetch_array($publishers_query)) {
    	$row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd' ;
    	$publishers_games_array = array();
    	$publishers_games_select_sql = "SELECT publishers_games_id, publishers_game, cd.categories_name
										FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
										LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION. " as cd
											ON pg.categories_id = cd.categories_id 
										WHERE publishers_id = '".$publishers_row['publishers_id']."'
											AND cd.language_id='1'";
		$publishers_games_result_sql = tep_db_query($publishers_games_select_sql);
		while ($publishers_games_row = tep_db_fetch_array($publishers_games_result_sql)) {
			$publishers_games_array[] = $publishers_games_row['categories_name'] . " (".$publishers_games_row['publishers_game'] .")";
		}
?>
							<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
								<td class="reportRecords" valign="top" align="center">
									<a href="<?=tep_href_link(FILENAME_PUBLISHERS, tep_get_all_get_params(array('pID', 'flag', 'action', 'subaction')) . 'pID='.$publishers_row['publishers_id'].'&action=edit')?>"><?=$publishers_row['publishers_id']?></a>
								</td>
								<td class="reportRecords" valign="top"><?=$publishers_row['publishers_name']?></td>
								<td class="reportRecords" valign="top"><?=implode(", ", $publishers_games_array)?></td>
								<td class="reportRecords" valign="top" align="center">
									<a href="<?=tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('pID', 'flag', 'action', 'subaction')) . 'pID='.$publishers_row['publishers_id'].'&action=edit')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
								</td>
							</tr>
<?		$row_count++;
	}
?>
		   				</table>
		   				</form>
		   				<script>
		   					function validate_sort_order() {
		   						var return_flag = true;
		   						jQuery(".txt_sort_order").each(function(){
		   							if (!validateInteger(jQuery(this).val())) {
		   								jQuery(this).val('');
		   								return_flag = false;
		   								jQuery(this).focus();
		   								alert("Invalid sort order");
		   								return false;
		   							}
		   						});
		   						return return_flag;
		   					}

							function confirmUpdateStatus(location_url) {
								if (confirm('Are you sure to perform this update?')) {
									window.location.href=location_url;
								}
							}
		   				</script>
		   			</td>
		   		</tr>
		   		<tr>
					<td>
						<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
							<tr>
			                    <td class="smallText" valign="top"><?=$publishers_split->display_count($publishers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_PUBLISHERS)?></td>
			                    <td class="smallText" align="right"><?=$publishers_split->display_links($publishers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y', 'cID', 'dis_id')))?></td>
							</tr>
						</table>
					</td>
				</tr>
<?
}
?>
    		</table>
    	</td>
		<!-- body_text_eof //-->
  	</tr>
</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>