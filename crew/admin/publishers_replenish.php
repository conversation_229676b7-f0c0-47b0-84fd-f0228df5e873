<?php 
/*
 * Development Date: 13/03/2017
 * Developer: <PERSON><PERSON><PERSON>
 * Task: PO System Phase 2 - Task 3
 */

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'po_suppliers.php');
require_once(DIR_WS_CLASSES . 'api_replenish_publishers.php');

$system_log_object = new log_files($login_id);
$po_suppliers = new po_suppliers($_SESSION['login_id'], $_SESSION['login_email_address']);
$api_publisher = new api_replenish_publishers();

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$subaction = (isset($_GET['subaction']) ? $_GET['subaction'] : '');

if (tep_not_null($action)) {
    switch ($action) {
        case 'batch_upd':
            if (isset($_REQUEST['event_record']) && count($_REQUEST['event_record'])) {
                foreach ($_REQUEST['event_record'] as $publishers_id_loop => $publishers_sort_order_loop) {
                    $publishers_data_sql = array(	
                        'sort_order' => (int)$publishers_sort_order_loop['sort_order'],
                        'last_modified' => 'now()'
                    );
                    tep_db_perform(TABLE_PUBLISHERS_REPLENISH, $publishers_data_sql, 'update', " publishers_replenish_id = '".$publishers_id_loop."' ");
                }
                $messageStack->add_session(TEXT_INFO_PUBLISHER_UPDATED, 'success');
            }
            tep_redirect(tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('pID', 'action', 'subaction', 'flag')))); 
            break;
        case 'setflag':
            if (isset($_REQUEST['pID']) && (int)$_REQUEST['pID'] > 0) {
                $publishers_data_sql = array(
                    'publishers_status' => (int)$_REQUEST['flag'],
                    'last_modified' => 'now()'
                );
                tep_db_perform(TABLE_PUBLISHERS_REPLENISH, $publishers_data_sql, 'update', " publishers_replenish_id = '".(int)$_REQUEST['pID']."' ");
                $messageStack->add_session(TEXT_INFO_PUBLISHER_UPDATED, 'success');
            }
            tep_redirect(tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('pID', 'action', 'subaction', 'flag')))); 
            break;
    }
}

if (tep_not_null($subaction)) {
    switch ($subaction) {
        case 'newconfirm':
        case 'update':
            $publisher_id = 0;

            if ($subaction == 'update' && (!isset($_REQUEST['pID']) && !((int)$_REQUEST['pID']>0))) {
                $messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
                tep_redirect(tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('action', 'subaction', 'pID'))));
            } else {
                $error = false;

                $publishers_name = tep_db_prepare_input($_POST['publishers_name']);
                $publishers_supplier = tep_db_prepare_input($_POST['publishers_supplier']);
                $publishers_status = (isset($_POST['rd_status']) ? (int)$_POST['rd_status'] : 0);
                $publishers_payment_terms = (isset($_POST['rd_publishers_term']) ? (int)$_POST['rd_publishers_term'] : 0);
                $publishers_sku = tep_db_prepare_input($_POST['publishers_sku']);
                $publishers_api_provider = tep_db_prepare_input($_POST['publishers_api_provider']);
                $publishers_api_method = tep_db_prepare_input($_POST['publishers_api_method']);
                $sort_order = (int)(isset($_POST['sort_order']) && preg_match("/^[0-9]+$/",$_POST['sort_order']) ? $_POST['sort_order'] : 50000);
                $publishers_remark = tep_db_prepare_input($_POST['publishers_remark']);

                if (tep_not_null($publishers_name)) {
                    $publishers_data_array = array(
                        'publishers_name' => tep_db_prepare_input($publishers_name),
                        'publishers_status' => (int)$publishers_status,
                        'publishers_remark' => $publishers_remark,
                        'publishers_supplier_id' => (int)$publishers_supplier,
                        'publishers_payment_term' => (int)$publishers_payment_terms,
                        'publishers_sku_header' => tep_db_prepare_input($publishers_sku),
                        'publishers_api_provider' => tep_db_prepare_input($publishers_api_provider),
                        'publishers_api_method' => tep_db_prepare_input($publishers_api_method),
                        'last_modified' => 'now()',
                        'last_modified_by' => $_SESSION['login_id'],
                        'sort_order' => (int)$sort_order
                    );
                    if ($subaction == 'update') {
                        tep_db_perform(TABLE_PUBLISHERS_REPLENISH, $publishers_data_array , 'update', " publishers_replenish_id = '".(int)$_REQUEST['pID']."' ");
                        $publisher_id = (int)$_REQUEST['pID'];
                    } else {
                        $publishers_data_array['date_added'] = 'now()';
                        tep_db_perform(TABLE_PUBLISHERS_REPLENISH, $publishers_data_array);
                        $publisher_id = tep_db_insert_id();
                    }
                    
                    //Update log_api_restock include publishers_id
                    $api_publishers_update = array(
                        'publishers_id' => $publisher_id
                    );
                    tep_db_perform(TABLE_LOG_API_RESTOCK, $api_publishers_update, 'update', "method = '".$publishers_api_method."' AND api_provider= '".$publishers_api_provider."' ");
                } else {
                    $messageStack->add_session(ERROR_PUBLISHERS_NAME);
                    $error = true;
                }

                if ($error) {
                    tep_redirect(tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('action', 'subaction')).'action=edit'));
                }
            }
            tep_redirect(tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('action', 'subaction', 'pID')).'action=edit&pID='.$publisher_id));
            break;
        case 'deleteconfirm':
            $error = false;

            $publisher_id = tep_db_prepare_input($_GET['pID']);

            $messageStack->add(TEXT_INFO_PUBLISHER_DELETED, 'success');

            tep_db_query("DELETE FROM " . TABLE_PUBLISHERS_REPLENISH . " where publishers_replenish_id= " . $publisher_id); 

            $messageStack->add_session(TEXT_INFO_PUBLISHER_DELETED, 'success');

            tep_redirect(tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('pID', 'action', 'subaction')))); 

            break;
    }
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
    <title><?php echo TITLE; ?></title>
    <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
    <script language="javascript" src="includes/general.js"></script>
    <script language="javascript" src="includes/javascript/jquery.js"></script>
    <script language="javascript" src="includes/javascript/php.packed.js"></script>
    <script language="javascript" src="includes/javascript/customer_xmlhttp.js"></script>
    <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

    <!-- body //-->
    <table border="0" width="100%" cellspacing="2" cellpadding="2">
    <tr>
    <td width="<?php echo BOX_WIDTH; ?>" valign="top">
        <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
            <!-- left_navigation //-->
            <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
            <!-- left_navigation_eof //-->
        </table>
    </td>
    <!-- body_text //-->
    <td width="100%" valign="top">
        <table border="0" width="100%" cellspacing="0" cellpadding="2">
        <?
        if ($_GET['action'] == 'new' || $_GET['action'] == 'edit') {
            $publishers_name = '';
            $publishers_status = '1';
            $publishers_payment_term = '';
            $publishers_sku = '';
            $publishers_api_provider = '';
            $publishers_api_method = '';
            $publishers_sort_order = '';
        
            $supplier_array = $po_suppliers->get_active_po_supplier_list();
            $api_method_array = $api_publisher->get_all_api_method();
	
            if ($_GET['action'] == 'edit') {
		if (isset($_REQUEST['pID']) && (int)$_REQUEST['pID']) {
                    $publishers_select_sql = "SELECT publishers_name, publishers_status, sort_order, 
                                                publishers_remark, publishers_supplier_id, publishers_payment_term,
                                                publishers_sku_header, publishers_api_provider, publishers_api_method
                                              FROM " . TABLE_PUBLISHERS_REPLENISH . "
                                              WHERE publishers_replenish_id = '".(int)$_REQUEST['pID']."'";
                    $publishers_result_sql = tep_db_query($publishers_select_sql);
                    if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
                        $publishers_name = $publishers_row['publishers_name'];
                        $publisher_supplier_id = $publishers_row['publishers_supplier_id'];
                        $publishers_status = $publishers_row['publishers_status'];
                        $publishers_payment_term = $publishers_row['publishers_payment_term'];
                        $publishers_sku = $publishers_row['publishers_sku_header'];
                        $publishers_api_provider = $publishers_row['publishers_api_provider'];
                        $publishers_api_method = $publishers_row['publishers_api_method'];
                        $publishers_sort_order = $publishers_row['sort_order'];
                        $publishers_remark = $publishers_row['publishers_remark'];
                        
                        // Set API Provider Select Options
                        $api_provider_array = $api_publisher->get_all_api_provider((int)$_REQUEST['pID']);
                        
                    } else {
                        $messageStack->add_session(ERROR_PUBLISHERS_NOT_FOUND);
                        tep_redirect(tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('action', 'subaction', 'pID'))));
                    }
		} else {
                    $messageStack->add_session(ERROR_INVALID_PUBLISHERS_DATA);
                    tep_redirect(tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('action', 'subaction', 'pID'))));
		}
            } else {
                // Set API Provider Select Options
                $api_provider_array = $api_publisher->get_all_api_provider();
            }
	
        ?>
            <tr>
                <td>
                    <?=tep_draw_form('publishers', FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('subaction')) . 'subaction=' . ($_GET['action'] == 'new' ? 'newconfirm':'update'), 'post', 'id="publishers_form" onSubmit="return check_form();"')?>
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
                                        <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                        </tr>
                        <tr>
                            <td class="formAreaTitle"><?=($_GET['action'] == 'new' ? HEADING_TITLE_INSERT:HEADING_TITLE_UPDATE)?></td>
                        </tr>
                        <tr>
                            <td class="formArea">
                                <table border="0" cellspacing="2" cellpadding="2">
                                    <?	if ($_GET['action'] == 'edit') { ?>
                                    <tr>
                                        <td class="main"><?=ENTRY_PUBLISHERS_ID?>:</td>
                                        <td class="main"><?=(int)$_REQUEST['pID']?></td>
                                        <td rowspan="15" valign="top">
                                            <div id="div_content"></div>
                                        </td>
                                    </tr>
                                    <?	} ?>
                                    <tr>
                                        <td class="main"><?=ENTRY_PUBLISHERS_NAME?>:</td>
                                        <td class="main"><?=tep_draw_input_field('publishers_name', $publishers_name, 'id="publishers_name" maxlength="32"', true)?></td>
                                    <?	if ($_GET['action'] != 'edit') { ?>
                                        <td rowspan="14" valign="top">
                                            <div id="div_content"></div>
                                        </td>
                                    <?	} ?>
                                    </tr>
                                    <tr>
                                        <td class="main"><?=ENTRY_PUBLISHERS_SUPPLIER?>:</td>
                                        <td class="main"><?=tep_draw_pull_down_menu('publishers_supplier', $supplier_array, $publisher_supplier_id, 'id="publishers_supplier"', true)?></td>
                                    </tr>
                                    <tr>
                                        <td class="main"><?=ENTRY_PUBLISHERS_STATUS?>:</td>
                                        <td class="main"><?
                                            echo tep_draw_radio_field('rd_status', '1', $publishers_status) . '&nbsp;Active';
                                            echo "&nbsp;&nbsp;";
                                            echo tep_draw_radio_field('rd_status', '0', (!$publishers_status ? true : false)) . '&nbsp;Inactive';
                                        ?></td>
                                    </tr>
                                    <tr>
                                        <td class="main"><?=ENTRY_PUBLISHERS_TERMS?>:</td>
                                        <td class="main"><?
                                            echo tep_draw_radio_field('rd_publishers_term', '1', $publishers_payment_term) . '&nbsp;Pre-paid';
                                            echo "&nbsp;&nbsp;";
                                            echo tep_draw_radio_field('rd_publishers_term', '0', (!$publishers_payment_term ? true : false)) . '&nbsp;Consignment';
                                        ?></td>
                                    </tr>
                                    <tr>
                                        <td class="main"><?=ENTRY_SKU?>:</td>
                                        <td class="main"><?=tep_draw_input_field('publishers_sku', ($publishers_sku=='' ? '' : $publishers_sku), 'id="publishers_sku" size="10" placeholder="RXT-"', true)?></td>
                                    </tr>
                                    <tr>
                                        <td class="main"><?=ENTRY_API_PROVIDER?>:</td>
                                        <td class="main"><?=tep_draw_pull_down_menu('publishers_api_provider', $api_provider_array, $publishers_api_provider, 'id="publishers_api_provider"', true)?></td>
                                    </tr>
                                    <tr>
                                        <td class="main"><?=ENTRY_API_METHOD?>:</td>
                                        <td class="main"><?=tep_draw_pull_down_menu('publishers_api_method', $api_method_array, $publishers_api_method, 'id="publishers_api_method"', true)?></td>
                                    </tr>
                                    <tr>
                                        <td class="main"><?=ENTRY_SORT_ORDER?></td>
                                        <td class="main"><?=tep_draw_input_field('sort_order', ($publishers_sort_order=='' ? 50000 : $publishers_sort_order), 'size="6"')?></td>
                                    </tr>
                                    <tr>
                                        <td class="main" valign="top"><?=ENTRY_REMARK?>:</td>
                                        <td class="main"><textarea name="publishers_remark" cols="50" rows="5"><?=$publishers_remark?></textarea></td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                                    </tr>
                                    <tr>
                                        <td align="left" class="main" colspan="2"><?=($_GET['action'] == 'new' ? '<input type="submit" class="inputButton" value="Insert" onclick="return get_confirm(\'publishers_form\');">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;' .($_GET['action'] == 'new' ? '<input type="button" class="inputButton" value="Cancel" onclick="location.href=\''.tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('action', 'pID', 'subaction'))).'\'">' : ' <input type="button" class="inputButton" value="Cancel" onclick="location.href=\''.tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('action', 'pID', 'subaction'))).'\'">')?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
                        </tr>
                    </table>
                </form>
                <script language="javascript">
                <!--
                function get_confirm(form_name) {
                    jquery_confirm_box("Are you sure to update this publisher?", 2, 0 , "Confirm");
                    jQuery('#jconfirm_submit').click(function(){
                        var error = 0;
                        var publishers_name = document.publishers.publishers_name.value;
                        var supplier_id = document.publishers.publishers_supplier.value;
                        var publishers_sku = document.publishers.publishers_sku.value;
                        var publishers_api_provider = document.publishers.publishers_api_provider.value;
                        var publishers_api_method = document.publishers.publishers_api_method.value;

                        if (trim_str(publishers_name) == '') {
                            error_message = "<?=ERROR_PUBLISHERS_NAME; ?>";
                            error_focus = 'publishers_name';
                            error = 1;
                        } else if (trim_str(supplier_id) == '') {
                            error_message = "<?=ERROR_PUBLISHERS_SUPPLIER_ID; ?>";
                            error_focus = 'publishers_supplier';
                            error = 1;
                        } else if (trim_str(publishers_sku) == '') {
                            error_message = "<?=ERROR_PUBLISHERS_SKU; ?>";
                            error_focus = 'publishers_sku';
                            error = 1;
                        } else if (trim_str(publishers_api_provider) == '') {
                            error_message = "<?=ERROR_PUBLISHERS_API_PROVIDER; ?>";
                            error_focus = 'publishers_api_provider';
                            error = 1;
                        } else if (trim_str(publishers_api_method) == '') {
                            error_message = "<?=ERROR_PUBLISHERS_API_METHOD; ?>";
                            error_focus = 'publishers_api_method';
                            error = 1;
                        }

                        if (error == 1) {
                            alert(error_message);
                            console.debug(error_focus);
                            document.getElementById(error_focus).focus();
                            return false;
                        } else {
                            jQuery('#'+form_name).submit();
                            return true;
                        }
                    });
				
                    jQuery("#jconfirm_cancel").click(function() {
                        return false;
                    });
                }
                
                function check_form() {
                    return true;
                }
                //-->
                </script>
            </td>
        </tr>
        <?
        } else {
            $publishers_query_raw = "select p.publishers_replenish_id, p.publishers_name, p.publishers_status, p.last_modified, p.sort_order from " . TABLE_PUBLISHERS_REPLENISH . " p order by p.sort_order, p.publishers_name ASC"; 
        $publishers_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $publishers_query_raw, $publishers_query_numrows);
        $publishers_query = tep_db_query($publishers_query_raw);
        ?>
        <tr>
            <td>
                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
                        <td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td valign="top">
                <?=tep_draw_form('publishers', FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('action')) . 'action=batch_upd', 'post', "onSubmit=\"return validate_sort_order()\"")?>
                    <table border="0" width="100%" cellspacing="1" cellpadding="2">
                        <tr>
                            <td class="reportBoxHeading" width="50" align="center"><?=TABLE_HEADING_ID?></td>
                            <td class="reportBoxHeading" width="*%"><?=TABLE_HEADING_NAME?></td>
                            <td class="reportBoxHeading" width="100" align="center"><?=TABLE_HEADING_STATUS?></td>
                            <td class="reportBoxHeading" width="100" align="center"><?=TABLE_HEADING_SORT_ORDER?></td>
                            <td class="reportBoxHeading" width="130" align="center"><?=TABLE_HEADING_LAST_MODIFIED?></td>
                            <td class="reportBoxHeading" width="5%" align="center"><?=TABLE_HEADING_ACTION?></td>
                        </tr>
                        <?
                            $row_count = 0;
                            while ($publishers_row = tep_db_fetch_array($publishers_query)) {
                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd' ;
                        ?>
                        <tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
                            <td class="reportRecords" valign="top" align="center"><?=$publishers_row['publishers_replenish_id']?></td>
                            <td class="reportRecords" valign="top"><?=$publishers_row['publishers_name']?></td>
                            <td class="reportRecords" valign="top" align="center">
                            <?
                            if ($publishers_row['publishers_status'] == '1') {
                                echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="javascript:void(confirmUpdateStatus(\''.tep_href_link(FILENAME_PUBLISHERS_REPLENISH, 'action=setflag&flag=0&pID=' . $publishers_row['publishers_replenish_id']) . '\'))">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
                            } else {
                                echo '<a href="javascript:void(confirmUpdateStatus(\''.tep_href_link(FILENAME_PUBLISHERS_REPLENISH, 'action=setflag&flag=1&pID=' . $publishers_row['publishers_replenish_id']).'\'));">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
                            }
                            ?>
                            </td>
                            <td class="reportRecords" valign="top" align="center"><?=tep_draw_input_field('event_record[' . $publishers_row['publishers_replenish_id'] . '][sort_order]', $publishers_row['sort_order'], 'size="6" id="event_record[' . $publishers_row['publishers_replenish_id'] . '][sort_order]" class="txt_sort_order" ')?></td>
                            <td class="reportRecords" valign="top" align="center"><?=$publishers_row['last_modified']?></td>
                            <td class="reportRecords" valign="top" align="center">
                                <a href="<?=tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('pID', 'flag', 'action', 'subaction')) . 'pID='.$publishers_row['publishers_replenish_id'].'&action=edit')?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
                                <a href="javascript:void(confirm_delete('<?=$publishers_row['publishers_name']?>', 'publisher', '<?=tep_href_link(FILENAME_PUBLISHERS_REPLENISH, tep_get_all_get_params(array('pID', 'flag', 'action', 'subaction')) . 'pID=' . $publishers_row['publishers_replenish_id'] . '&subaction=deleteconfirm')?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
                            </td>
                        </tr>
                        <?		
                                $row_count++;
                            }
                        ?>
                        <tr>
                            <td colspan="3"></td>
                            <td align="center"><input type="submit" class="inputButton" value="Update"></td>
                            <td colspan="2"></td>
                        </tr>
                    </table>
                </form>
                <script>
                    function validate_sort_order() {
                        var return_flag = true;
                        jQuery(".txt_sort_order").each(function(){
                            if (!validateInteger(jQuery(this).val())) {
                                jQuery(this).val('');
                                return_flag = false;
                                jQuery(this).focus();
                                alert("Invalid sort order");
                                return false;
                            }
                        });
                        return return_flag;
                    }

                    function confirmUpdateStatus(location_url) {
                        if (confirm('Are you sure to perform this update?')) {
                            window.location.href=location_url;
                        }
                    }
                </script>
            </td>
        </tr>
        <tr>
            <td>
                <table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
                    <tr>
                        <td class="smallText" valign="top"><?=$publishers_split->display_count($publishers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_PUBLISHERS)?></td>
                        <td class="smallText" align="right"><?=$publishers_split->display_links($publishers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y', 'cID', 'dis_id')))?></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td align="left"><?='[ <a href="'.tep_href_link(FILENAME_PUBLISHERS_REPLENISH, 'page=' . $_GET['page'] . '&action=new').'" >'.LINK_ADD_PUBLISHERS.'</a> ]'?></td>
        </tr>
        <?
        }
        ?>
        </table>
    </td>
    <!-- body_text_eof //-->
    </tr>
    </table>
    <!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>