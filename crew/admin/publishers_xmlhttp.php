<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

//header('Content-Type: text/xml');

define('EMAIL_TEXT_SUBJECT_CREW_NOTIFICATION', "Receiving Payment Method Changes Notification");

include_once('includes/application_top.php');
include_once(DIR_WS_LANGUAGES . 'english/publishers.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$pID = (isset($_REQUEST['pID']) && (int)$_REQUEST['pID'] > 0 ? (int)$_REQUEST['pID'] : '');

$response_array = array();
$response_array['result'] = 0;
switch($action) {
	case 'js_form_checking':
		include_once(DIR_WS_CLASSES . 'direct_topup.php');
		$direct_topup_obj = new direct_topup();
  		$direct_topup_obj->void_include_all_classes();
		
		$class = (isset($_REQUEST['configuration']['TOP_UP_MODE']) && tep_not_null($_REQUEST['configuration']['TOP_UP_MODE']) ? $_REQUEST['configuration']['TOP_UP_MODE'] : '');
      	
  		$top_up_class = '';
  		if (isset($_REQUEST['configuration']['TOP_UP_MODE']) && tep_class_exists('dtu_'.$class)) {
  			eval('$direct_topup_class_obj = new dtu_'.$class.'();');
  		} else {
  			$direct_topup_class_obj = new dtu_offgamers();
  		}
  		
  		if ($subaction == 'update' && (!isset($_REQUEST['pID']) && !((int)$_REQUEST['pID']>0))) {
  			$response_array['message'] = ERROR_INVALID_PUBLISHERS_DATA;
  		} else if (!isset($_REQUEST['configuration']['OGM_MERCHANT_ID']) || !$_REQUEST['configuration']['OGM_MERCHANT_ID']) {
  			$response_array['message'] = ERROR_INVALID_PUBLISHERS_MERCHANT_ID;
  		} else if (!$direct_topup_class_obj->validate_admin_update_input($_REQUEST, $error_msg)) {
  			$response_array['message'] = $error_msg;
  		} else {
  			$response_array['result'] = 1;
  		}
  		
      	break;
	case 'load_mode_content':
		$error_msg = '';
		
		if (isset($_REQUEST['class'])) {
			$class = $_REQUEST['class'];
			if (tep_not_null($_REQUEST['class']) && file_exists(DIR_FS_CATALOG_MODULES . 'direct_topup/' . $class . '.php')) {
        		if (file_exists(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $class . '.php')) {
				    include_once(DIR_FS_CATALOG_LANGUAGES . $language . '/modules/direct_topup/' . $class . '.php');
				}
			    include_once(DIR_FS_CATALOG_MODULES . 'direct_topup/'. $class . '.php');	
			    
			    if (tep_class_exists('dtu_'.$class)) {
			    	eval('$direct_topup_class_obj = new dtu_'.$class.'();');
			    	if (!$direct_topup_class_obj->get_enable()) {
				    	include_once(DIR_FS_CATALOG_MODULES . 'direct_topup/offgamers.php');	
				    	$direct_topup_class_obj = new dtu_offgamers();
			    	}
			    } else {
			    	include_once(DIR_FS_CATALOG_MODULES . 'direct_topup/offgamers.php');	
			    	$direct_topup_class_obj = new dtu_offgamers();
			    }
				$response_array['html'] = $direct_topup_class_obj->draw_admin_input($pID);
				$response_array['result'] = 1;
			} else {
				include_once(DIR_FS_CATALOG_MODULES . 'direct_topup/offgamers.php');	
				$direct_topup_class_obj = new dtu_offgamers();
				$response_array['html'] = $direct_topup_class_obj->draw_admin_input($pID);
			}
		} else {
			$response_array['message'] = 'Incomplete data';
		}
		
		if (tep_not_null($error_msg)) {
			$response_array['message'] = $error_msg;
		}
		break;
}
echo json_encode($response_array);
?>