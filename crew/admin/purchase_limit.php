<?php
require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'currencies.php');

$row_counter = 0;
$default_currency_type = DEFAULT_CURRENCY;
$country_lists_id_array = array();
$countries_risk_content_array = array();
$purchase_limit_array = array();
$display_content_array = array();

$currencies = new currencies();
$currencies->set_hide_currency_symbol();

$period_type_array = array ('day' => ENTRY_PER_DAY,
							'week' => ENTRY_PER_WEEK,
							'month' => ENTRY_PER_MONTH);
							
$country_risk_type_array = array ( 	'NRP' => TABLE_HEADING_NRP_COUNTRIES,
									'HIGH' => TABLE_HEADING_HIGH_RISK_COUNTRIES,
									'MEDIUM' => TABLE_HEADING_MEDIUM_RISK_COUNTRIES,
									'LOW' => TABLE_HEADING_LOW_RISK_COUNTRIES,
									'PREFERRED' => TABLE_HEADING_PREFERRED_COUNTRIES
									);

$action = (isset($_GET["action"]) ? $_GET["action"] : '');

# Process Action Arguments
if (tep_not_null($action)) {
	switch ($action) {
		case 'update' :
			$action_risk_countries = $_POST['risk_countries'];
			$action_purchase_limit = $_POST['purchase_limit'];
			
			foreach ($action_risk_countries as $risk_id => $countries_id) {
				if (tep_not_null($countries_id)) {
					$update_data_array = array('aft_risk_type' => $risk_id);
					tep_db_perform(TABLE_COUNTRIES, $update_data_array, "update", " countries_id IN (".$countries_id.")");
				}
			}
			
			foreach ($action_purchase_limit as $customer_group_id => $risk_countries_array) {
				$insert_new = false;
				
				$check_exist_select_sql = "	SELECT purchase_limit_id
											FROM ".TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT." 
											WHERE customers_aft_groups_id = ".(int)$customer_group_id;
				$check_exist_result_sql = tep_db_query($check_exist_select_sql);
				if (!tep_db_num_rows($check_exist_result_sql)) {
					$insert_new = true;
				} 
				
				foreach ($risk_countries_array as $risk_id => $purchase_limit_array) {
					$memcache_obj->delete(TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT . '/purchase_limit/customers_aft_groups_id/' . (int)$customer_group_id . '/aft_countries_risk_type/' . $risk_id);
					
					$data_array = array(	'customers_aft_groups_id' => (int)$customer_group_id,
											'aft_countries_risk_type' => $risk_id,
											'purchase_limit_per_day' => $currencies->custom_format($purchase_limit_array['day'], false, $default_currency_type, $currency_value = '', false, '.', ''),
											'purchase_limit_per_week' => $currencies->custom_format($purchase_limit_array['week'], false, $default_currency_type, $currency_value = '', false, '.', ''),
											'purchase_limit_per_month' => $currencies->custom_format($purchase_limit_array['month'], false, $default_currency_type, $currency_value = '', false, '.', '')
											);
					if ($insert_new) {
						tep_db_perform(TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT, $data_array);
					} else {
						tep_db_perform(TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT, $data_array, "update", " customers_aft_groups_id=".$customer_group_id." AND aft_countries_risk_type='".$risk_id."'");
					}
					unset($data_array);
				}
			}
			$messageStack->add_session(SUCCESS_UPDATE_PURCHASE_LIMIT, 'success');
			
			break;
		case 'set_purchase_limit_used' :
			$action_customer_group_id = (int)$_GET['customer_group_id'];
			$action_purchase_limit_used = (int) $_GET['purchase_limit_used'];
			
			$check_exist_select_sql = "	SELECT purchase_limit_id, aft_countries_risk_type
										FROM ".TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT." 
										WHERE customers_aft_groups_id = ".$action_customer_group_id;
			$check_exist_result_sql = tep_db_query($check_exist_select_sql);
			if (tep_db_num_rows($check_exist_result_sql)) {
				
				while ($check_exist_row = tep_db_fetch_array($check_exist_result_sql)) {
					$memcache_obj->delete(TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT . '/purchase_limit/customers_aft_groups_id/' . (int)$action_customer_group_id . '/aft_countries_risk_type/' . $check_exist_row['aft_countries_risk_type']);
				}
				
				$data_array = array('purchase_limit_used' => $action_purchase_limit_used);
				tep_db_perform(TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT, $data_array, "update", " customers_aft_groups_id=".$action_customer_group_id);
				unset($data_array);
			} else {
				foreach ($country_risk_type_array as $risk_id => $risk_name) {
					$data_array = array('customers_aft_groups_id' => $action_customer_group_id,
										'aft_countries_risk_type' => $risk_id,
										'purchase_limit_used' => $action_purchase_limit_used);
					tep_db_perform(TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT, $data_array);
					unset($data_array);
					$memcache_obj->delete(TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT . '/purchase_limit/customers_aft_groups_id/' . (int)$action_customer_group_id . '/aft_countries_risk_type/' . $risk_id);
				}
			}
			
			if ($action_purchase_limit_used) {
				$messageStack->add_session(SUCCESS_PURCHASE_LIMIT_TURN_ON, 'success');
			} else {
				$messageStack->add_session(SUCCESS_PURCHASE_LIMIT_TURN_OFF, 'success');
			}
	}
	tep_redirect(tep_href_link(FILENAME_PURCHASE_LIMIT));
}

# Get Country Risk Type Content List			
foreach ($country_risk_type_array as $risk_id => $risk_name) {
	$country_lists_id_array[$risk_id] = '#sortable'.$risk_id;
	
	$countries_select_sql = "	SELECT c.countries_id, c.countries_name
								FROM " . TABLE_COUNTRIES . " AS c 
								WHERE c.aft_risk_type = '".$risk_id."'
								ORDER BY c.countries_name";
	$countries_result_sql = tep_db_query($countries_select_sql);
	while ($countries_row = tep_db_fetch_array($countries_result_sql)) {
		$countries_risk_content_array[$risk_id][] = array ('id' => $countries_row['countries_id'], 'name' => $countries_row['countries_name']);
	}
}

# Get Purchase Limit Content From Database
$customer_group_select_sql = "	SELECT cgpl.*
								FROM ".TABLE_CUSTOMERS_GROUPS_PURCHASE_LIMIT." AS cgpl";
$customer_group_result_sql = tep_db_query($customer_group_select_sql);
while ($customer_group_row = tep_db_fetch_array($customer_group_result_sql)) {
	$purchase_limit_array[$customer_group_row['customers_aft_groups_id']]['purchase_limit_used'] = $customer_group_row['purchase_limit_used'];
	
	foreach ($period_type_array as $period_id => $period_name) {
		$purchase_limit_array[$customer_group_row['customers_aft_groups_id']][$period_id][$customer_group_row['aft_countries_risk_type']] = $customer_group_row['purchase_limit_per_'.$period_id];
	}
}

# Re-format & Re-order & Predefined New Customer Group Content
$customer_group_select_sql = "	SELECT customers_aft_groups_id , customers_aft_groups_name
								FROM " . TABLE_CUSTOMERS_AFT_GROUPS . " 
								ORDER BY sort_order";
$customer_group_result_sql = tep_db_query($customer_group_select_sql);
while ($customer_group_row = tep_db_fetch_array($customer_group_result_sql)) {
	$customer_group_id = $customer_group_row['customers_aft_groups_id'];
	$display_content_array[$customer_group_id]['name'] = $customer_group_row['customers_aft_groups_name'];
	$display_content_array[$customer_group_id]['purchase_limit_used'] = isset($purchase_limit_array[$customer_group_id]['purchase_limit_used']) ? $purchase_limit_array[$customer_group_id]['purchase_limit_used'] : 0;
	
	foreach ($period_type_array as $period_id => $period_name) {
		foreach ($country_risk_type_array as $risk_type => $risk_name) {
			if (isset($purchase_limit_array[$customer_group_id][$period_id][$risk_type])) {
				$display_content_array[$customer_group_id]['content'][$period_id][$risk_type] = $currencies->custom_format($purchase_limit_array[$customer_group_id][$period_id][$risk_type], false, $default_currency_type, $currency_value = '', false, '.', '');
			} else {
				$display_content_array[$customer_group_id]['content'][$period_id][$risk_type] = $currencies->custom_format(0, false, $default_currency_type, $currency_value = '', false, '.', '');
			}
		}
	}
	unset($purchase_limit_array[$customer_group_id]);
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title>Country Categorization and Frequency Table</title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link type="text/css" href="includes/javascript/jquery_ui/themes/base/ui.all.css" rel="stylesheet" />
	<style type="text/css">
		<?php echo implode(",", $country_lists_id_array); ?> { 
			background: #f7f7f7; 
			border: 1px solid gray;
			list-style-type: none; 
			margin: 1px; 
			padding: 0; 
			float: left;
			height: 180px;
			width: 175px;
			overflow-y: scroll;
		}
		<?php echo implode(" li,", $country_lists_id_array); ?> li { 
			margin: 0 1px 1px 1px; 
			padding: 1px; 
			font-size: 12px; 
			width: 150px; 
		}
	</style>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?php echo HEADING_TITLE; ?></td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
      				</tr>
      				<tr>
    					<td width="100%" valign="top">
    						<?php echo tep_draw_form('purchase_limit_form', FILENAME_PURCHASE_LIMIT, tep_get_all_get_params(array('action')) . 'action=update', 'post', ''); ?>
    						<table border="0" width="100%" cellspacing="0" cellpadding="2">
    							<tr>
    								<td class="reportBoxHeading">&nbsp;</td>
<?php
	foreach ($country_risk_type_array as $risk_name) {
    	echo						'<td align="center" class="reportBoxHeading">'.$risk_name.'</td>';
	} 
?>
    							</tr>
    							<tr class="reportListingEven">
    								<td></td>		
<?php
	foreach ($country_risk_type_array as $risk_id => $risk_name) { 
		echo						'<td width="175px">' . tep_draw_hidden_field('risk_countries['.$risk_id.']', '') .
										'<ul id="sortable'.$risk_id.'" class="connectedSortable">';
		if (isset($countries_risk_content_array[$risk_id])) {
			foreach ($countries_risk_content_array[$risk_id] as $country_array) {
				echo						'<li id="'.$country_array['id'].'" class="ui-state-default">'.$country_array['name'].'</li>';
			}
		}
		echo							'</ul><br/>
										<span class="main">'.ENTRY_PL_TOTAL_COUNTRY.': '.count($countries_risk_content_array[$risk_id]).' </span>
									</td>';
	}
	echo						'</tr>';
	
	foreach ($display_content_array as $customer_group_id => $content_array) {
		$row_style = (($row_counter++)%2) ? 'reportListingEven' : 'reportListingOdd';
?>	
								<tr class="<?php echo $row_style; ?>">
									<td class="main" colspan="5">
										<b><?php echo $content_array['name'] . ' ('.$customer_group_id.')'; ?></b>
									</td>
									<td align="right">
										<?php tep_switch_image($content_array['purchase_limit_used'], tep_href_link(FILENAME_PURCHASE_LIMIT, "action=set_purchase_limit_used&customer_group_id=".$customer_group_id."&purchase_limit_used=1"), tep_href_link(FILENAME_PURCHASE_LIMIT, "action=set_purchase_limit_used&customer_group_id=".$customer_group_id."&purchase_limit_used=0"), ' onclick="return show_confirm()"'); ?>
									</td>
								</tr>
<?php
		foreach ($content_array['content'] as $period_id => $limit_content_array) {
			echo				'<tr class="'.$row_style.'">
									<td class="main">'.$period_type_array[$period_id].'</td>';
			foreach ($country_risk_type_array as $risk_id => $risk_name) {
				$purchase_limit_id = $period_id.'_'.$risk_id.'_'.$customer_group_id;
				echo				'<td>'.tep_draw_input_field('purchase_limit['.$customer_group_id.']['.$risk_id.']['.$period_id.']', $limit_content_array[$risk_id], 'id="'.$purchase_limit_id.'" size="26"').'</td>';
			}
			echo				'</tr>';
		}
	}
?>
								<tr>
			        				<td colspan="6"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
			      				</tr>
								<tr>
									<td align="right" colspan="6"><input type="button" name="update" value="Update" class="inputButton" onClick="return button_lock();"></td>
								</tr>
							</table>
							</form>
						</td>
					</tr>
				</table>
  			</td>
  			<!-- body_text_eof //-->
  		</tr>
	</table>
	<script type="text/javascript" src="includes/javascript/jquery_ui/jquery-1.3.2.js"></script>
	<script type="text/javascript" src="includes/javascript/jquery_ui/ui/ui.core.js"></script>
	<script type="text/javascript" src="includes/javascript/jquery_ui/ui/ui.sortable.js"></script>
	<script language="javascript">
		<!--
		var mylist = listitems = compA = compB = '';
		jQuery.noConflict();
		
		jQuery(function() {
			jQuery("<?php echo implode(",", $country_lists_id_array); ?>").sortable({
				connectWith: '.connectedSortable',
				placeholder: 'ui-state-highlight',
				forcePlaceholderSize: false,
//				revert: true,
				tolerance: 'intersect',
				scroll: true,
				update: function(event, ui) {
					mylist = jQuery(this);
					listitems = mylist.children('li').get();
					listitems.sort(function(a, b) {
					   compA = jQuery(a).text().toUpperCase();
					   compB = jQuery(b).text().toUpperCase();
					   return (compA < compB) ? -1 : (compA > compB) ? 1 : 0;
					})
					jQuery.each(listitems, function(idx, itm) { mylist.append(itm); });
				}
			}).disableSelection();
		});
		
		function show_confirm() {
			return confirm("<?php echo COMFIRM_RESET_PURCHASE_LIMIT; ?>");
		}
		
		function form_checking() {
			return true;
		}
		
		function button_lock() {
			document.purchase_limit_form.update.disabled = true;
<?php
foreach ($country_risk_type_array as $risk_id => $risk_name) {
	echo   "document.purchase_limit_form.elements['risk_countries[".$risk_id."]'].value = jQuery('".$country_lists_id_array[$risk_id]."').sortable('toArray');\n";
}
?>
			if(form_checking()) {
				document.purchase_limit_form.submit();
			}
		}
		//-->
	</script>
	<!-- body_eof //-->

	<!-- footer //-->
	<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
	<!-- footer_eof //-->
	<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>