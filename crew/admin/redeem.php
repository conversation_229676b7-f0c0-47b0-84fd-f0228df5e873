<?
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'redeem.php');
require_once(DIR_WS_CLASSES . 'ms_store_credit.php');
require_once(DIR_WS_CLASSES . 'currencies.php');

$redeem_object = new redeem($login_id, $login_email_address);
$currencies = new currencies();

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

switch($action) {
	case 'show_report':
		$header_title = HEADER_FORM_REDEEM_LIST;
		$form_content = $redeem_object->show_redeem_list(FILENAME_REDEEM, 'redeem_list_inputs', $_REQUEST, $messageStack);
		
		break;
	case 'reset_session':
    	unset($_SESSION['redeem_list_inputs']);
    	tep_redirect(tep_href_link(FILENAME_REDEEM));
    	
    	break;
    case 'batch_action':
		$action_res_array = $redeem_object->do_batch_action(FILENAME_REDEEM, $HTTP_POST_VARS, $messageStack);
		
		tep_redirect(tep_href_link(FILENAME_REDEEM, tep_get_all_get_params(array('action', 'subaction')) . 'action=show_report&cont=1'));
		
    	break;
    case 'edit':
    	$header_title = HEADER_FORM_REDEEM_EDIT;
		$form_content = $redeem_object->edit_redeem(FILENAME_REDEEM, 'redeem_list_inputs', $_REQUEST, $messageStack);
		
		break;
	case 'update_redeem':
		$form_content = $redeem_object->update_redeem(FILENAME_REDEEM, $_REQUEST, $messageStack);
		
		tep_redirect(tep_href_link(FILENAME_REDEEM, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit'));
		
		break;
	default:
		$header_title = HEADER_FORM_REDEEM_LIST_CRITERIA_TITLE;
		$form_content = $redeem_object->search_redeem_list(FILENAME_REDEEM, 'redeem_list_inputs');
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.selectboxes.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.tabs.js"></script>
<?	if (file_exists(DIR_WS_INCLUDES . 'javascript/redeem_xmlhttp.js.php')) { include_once (DIR_WS_INCLUDES . 'javascript/redeem_xmlhttp.js.php'); } ?>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%" class="pageHeading"><b><?=$header_title?></b></td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>