<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$admin_id = isset($_SESSION['login_id']) ? $_SESSION['login_id'] : '';

if (isset($_SESSION['language']) && tep_not_null($_SESSION['language'])) {
	if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '.php')) {
		include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '.php');
	}
}

echo '<response>';
if (tep_not_null($action)) {
	switch($action) {
		case 'update_redeem':
			if (file_exists(DIR_WS_LANGUAGES . $_SESSION['language'] . '/redeem.php')) {
				include_once(DIR_WS_LANGUAGES . $_SESSION['language'] . '/redeem.php');
			}
			
			$currency_display_decimal = 2;
			
			require_once(DIR_WS_CLASSES . 'ms_store_credit.php');
			require_once(DIR_WS_CLASSES . 'redeem.php');
			require_once(DIR_WS_CLASSES . 'currencies.php');
			
			$currencies = new currencies();
			$xmlhttp_redeems_object = new redeem($admin_id, $_SESSION['login_email_address']);
			$xmlhttp_sc_object = new ms_store_credit($admin_id, $_SESSION['login_email_address']);
			
			/***************************************************
			1. Check if admin user has permission
			2. This payment still in its original status
			3. Add comment to the payment transation
			4. Email the payee
			
			$res_code
				- '-1' : Do not refresh the row contents
				- '1' : Refresh the row contents but doesn't mean positive action
			***************************************************/
			$email_beneficiary = false;
			$email_comments_str = '';
			
			$res_code = '';
			$res_message = '';
			
			$redeem_id = isset($HTTP_GET_VARS['r_id']) ? $HTTP_GET_VARS['r_id'] : '';
			$from_status = isset($HTTP_GET_VARS['fr_status']) ? $HTTP_GET_VARS['fr_status'] : '';
			$to_status = isset($HTTP_GET_VARS['to_status']) ? $HTTP_GET_VARS['to_status'] : '';
			$filename = isset($HTTP_GET_VARS['filename']) ? $HTTP_GET_VARS['filename'] : '';
			
			if (!tep_admin_check_boxes(FILENAME_REDEEM, 'sub_boxes')) {
				$res_code = '-1';
				$res_message = JS_ERROR_REDEEM_NO_PERMISSION;
			} else {
				$redeem_current_info_select_sql = "	SELECT spr.*, c.currencies_id 
													FROM " . TABLE_STORE_POINTS_REDEEM . " AS spr 
													INNER JOIN " . TABLE_CURRENCIES . " AS c 
														ON (c.code = spr.store_points_paid_currency) 
													WHERE spr.store_points_redeem_id = '" . (int)$redeem_id . "'";
				$redeem_current_info_result_sql = tep_db_query($redeem_current_info_select_sql);
				if ($redeem_current_info_row = tep_db_fetch_array($redeem_current_info_result_sql)) {
					if ($redeem_current_info_row['store_points_redeem_status'] == $from_status) {
						$store_points_redeem_id = $redeem_current_info_row['store_points_redeem_id'];
						$user_id = $redeem_current_info_row['user_id'];
						$store_points_redeem_amount = $redeem_current_info_row['store_points_redeem_amount'];
						$store_points_request_currency = $redeem_current_info_row['store_points_request_currency'];
						$store_points_request_currency_amount = $redeem_current_info_row['store_points_request_currency_amount'];
						$store_points_paid_currency = $redeem_current_info_row['store_points_paid_currency'];
						$store_points_paid_currency_amount = $redeem_current_info_row['store_points_paid_currency_amount'];
						$redeem_to_currency_id = $redeem_current_info_row['currencies_id'];
						
						switch ($redeem_current_info_row['store_points_redeem_status']) {
							case '1':
								if ($to_status == '2') {	// From Pending -> Processing
									if ((double)$store_points_request_currency_amount > (double)$store_points_paid_currency_amount) {
										//$user_info_row = $xmlhttp_redeems_object->_get_user_particulars($user_id, $user_role);
										//$paid_points = $redeem_current_info_row['store_payments_request_currency'];
										
										//if ($xmlhttp_redeems_object->_check_store_point_got_balance($user_id) === true) {
										$redeem_update_sql_data_array = array(	'store_points_redeem_status' => $to_status,
																				'store_points_redeem_last_modified' => 'now()'
																				);
										
										tep_db_perform(TABLE_STORE_POINTS_REDEEM, $redeem_update_sql_data_array, 'update', "store_points_redeem_id = '" . tep_db_input($redeem_id) . "'");
										
										$redeem_history_sql_data_array = array(	'store_points_redeem_id' => $redeem_id,
							    												'store_points_redeem_status' => $to_status,
								    											'date_added' => 'now()',
								    											'payee_notified' => '1',
								    											'changed_by' => $_SESSION['login_email_address'],
								    											'changed_by_role' => 'admin'
						        											);
										tep_db_perform(TABLE_STORE_POINTS_REDEEM_HISTORY, $redeem_history_sql_data_array);
										
										// Email to beneficiary
										//$email_beneficiary = true;
										$res_code = '1';
									} else {
										$processing_error = true;
										
										$res_code = '1';
										$res_message = sprintf(WARNING_PAYMENT_TRANSACTION_NOT_UPDATED, $redeem_id)."\n\r".ERROR_PAYMENT_TRANSACTION_INSUFFICIENT_BALANCE."\n\r".'store balance amount=$'.$store_balance_amount."\n\r".'total reserve amount=$'.$total_reserve_amount;
									}
								}
								
								break;
							case '2':
								$processing_error = false;
								
								if ($to_status == '3') {	// From Processing -> Completed
									if ($store_points_request_currency == $store_points_paid_currency) {
										if ((double)$redeem_current_info_row['store_points_request_currency_amount'] > (double)$redeem_current_info_row['store_points_paid_currency_amount']) {
											// get SC balance and currency
											$sc_currency_row = $xmlhttp_sc_object->getScBalance($user_id);

											if ($sc_currency_row) {
												$sc_currency_id = $currencies->get_id_by_code($sc_currency_row['currency']);
												$sc_currency_code = $sc_currency_row['currency'];
											} else {
												$sc_currency_id = $redeem_current_info_row['currencies_id'];
												$sc_currency_code = $currencies->get_code_by_id($redeem_current_info_row['currencies_id']);
											}

											$sc_redeem_amt = (double)$store_points_request_currency_amount;
												
											if ($redeem_to_currency_id != $sc_currency_id) {
												$exchange_rate = $currencies->advance_currency_conversion_rate('USD', $sc_currency_code, 'sell');
												$request_currency_amount = (($store_points_redeem_amount / 10000) * $exchange_rate);
												$sc_redeem_amt = $request_currency_amount;
												$redeem_to_currency_id = $sc_currency_id;
												
												$data_array_sql = array();
												
												$data_array_sql['store_points_request_currency'] = $sc_currency_code;
												$data_array_sql['store_points_request_currency_amount'] = $request_currency_amount;
												$data_array_sql['store_points_paid_currency'] = $sc_currency_code;
												$data_array_sql['store_points_exchange_rate'] = $exchange_rate;
												
												tep_db_perform(TABLE_STORE_POINTS_REDEEM, $data_array_sql, 'update', " store_points_redeem_id = '" . (int)$store_points_redeem_id . "'");
												
												unset($data_array_sql);
											}
											
											$trans_array = array();
											
											$trans_array['sp_redeem_id'] = $store_points_redeem_id;
											$trans_array['user_id'] = $user_id;
											$trans_array['sc_redeem_paid_currency'] = $sc_currency_code;
											$trans_array['sc_redeem_amt'] = $sc_redeem_amt;
											$trans_array['currency_id'] = $redeem_to_currency_id;
											
											if ($xmlhttp_sc_object->redeemOnStoreCredit($trans_array)) {
												// Email to beneficiary
												$email_beneficiary = true;
											} else {
												$res_code = '-1';
												$res_message = 'Problem issuing Store credit for this Redeem#';
											}
										} else {
											$res_code = '1';
											$res_message = 'Store credit have already been issue for this Redeem#';
										}
									} else {
										; // TO BE CONTINUE
									}
								}
								
								$res_code = ($res_code == '-1') ? '-1' : '1';
								break;
							default:
								$res_code = '1';
								$res_message = JS_ERROR_REDEEM_TRANS_UNKNOWN_STATUS;
								break;
						}
					} else {	// This redeem has been modified
						$res_code = '1';
						$res_message = JS_ERROR_REDEEM_TRANS_MODIFIED;
					}
					
					$redeem_updated_info_select_sql = "	SELECT * 
														FROM " . TABLE_STORE_POINTS_REDEEM . " 
														WHERE store_points_redeem_id = '" . tep_db_input($redeem_id) . "'";
					$redeem_updated_info_result_sql = tep_db_query($redeem_updated_info_select_sql);
					if ($redeem_updated_info_row = tep_db_fetch_array($redeem_updated_info_result_sql)) {
						if ($redeem_updated_info_row['user_role'] == 'supplier') {
							$user_name_link = '<a href="'.tep_href_link(FILENAME_SUPPLIERS_LIST, 'action=edit_supplier&sID='.$redeem_updated_info_row['user_id']).'" target="_blank">'.$redeem_updated_info_row['user_firstname'] . ' ' . $redeem_updated_info_row['user_lastname'] . (tep_not_null($user_info_array['sign_up_from']) ? ' ['.$user_info_array['sign_up_from'].']' : '').'</a>';
						} else {
							$user_name_link = '<a href="'.tep_href_link(FILENAME_CUSTOMERS, 'cID='.$redeem_updated_info_row['user_id'].'&action=edit').'" target="_blank">'.$redeem_updated_info_row['user_firstname'] . ' ' . $redeem_updated_info_row['user_lastname'] . (tep_not_null($user_info_array['sign_up_from']) ? ' ['.$user_info_array['sign_up_from'].']' : '').'</a>';
						}
						
						switch($redeem_updated_info_row['store_points_redeem_status']) {
							case '1':	// Pending
								$action_button_html = tep_button('Process', 'Process this redeem', '', ' name="ProcessBtn_'.$redeem_id.'" onClick="updateRedeem(\''.$filename.'\', this, \''.$redeem_id.'\', \'1\', \'2\');" ', 'inputButton') . '&nbsp;';
								
								break;
							case '2':	// Processing
								$action_button_html = tep_button('Complete', 'Complete this redeem', '', ' name="CompleteBtn_'.$redeem_id.'" onClick=" if (confirm(\''.JS_ALERT_REDEEM_CONFIRM_UPDATE.'\') != \'0\') { updateRedeem(\''.$filename.'\', this, \''.$redeem_id.'\', \'2\', \'3\'); } else { return false; }" ', 'inputButton') . '&nbsp;';
								$redeem_batch_available = true;
								
								break;
							case '3':	// Completed
								$action_button_html = 'Completed';
								
								break;
							case '4':	// Canceled
								$action_button_html = 'Canceled';
								
								break;
							default:
								
								break;
						}
						
						$edit_link = '<a href="' . tep_href_link($filename, 'redeemID='.$redeem_id.'&action=edit', 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "14", "13", 'align="top"').'</a>';
						$redeem_date = $redeem_updated_info_row['store_points_redeem_date'];
						$request_amt = $redeem_updated_info_row['store_points_redeem_amount'];
						$actual_payout_amount = $redeem_updated_info_row['store_points_request_currency_amount'];
						$rounded_actual_payout_amount = number_format(tep_round($actual_payout_amount, $currency_display_decimal), $currency_display_decimal, '.', '');
						$sp_stat_link = sprintf(LINK_REDEEM_SP_STATEMENT, tep_href_link(FILENAME_STORE_POINT, 'action=show_report&customer_id='.urlencode($redeem_updated_info_row['user_id']).'&start_date='.(date('Y-m-d', mktime(0, 0, 0, date('m')  , date('d')-7, date('Y')))), 'SSL'));
						$reference = $redeem_updated_info_row['store_points_redeem_reference'];
						
						echo "<table_cell><cell property='nowrap=1'><![CDATA[".$redeem_id."]]></cell>";
						echo "<cell property='nowrap=1'><![CDATA[".$redeem_date."]]></cell>";
						echo "<cell property='nowrap=1'><![CDATA[".$user_name_link."]]></cell>";
						echo "<cell property='align=center'><![CDATA[".$sp_stat_link."]]></cell>";
						echo "<cell property='align=right&amp;nowrap=1'><![CDATA[".$request_amt."]]></cell>";
						echo "<cell property='align=right&amp;nowrap=1'><![CDATA[".$currencies->format($rounded_actual_payout_amount, false, $redeem_updated_info_row['store_points_paid_currency'])."]]></cell>";
						echo "<cell property='align=center'><![CDATA[".$reference."]]></cell>";
						echo "<cell property='align=center'><![CDATA[".$edit_link."]]></cell>";
						echo "<cell property='align=center&amp;nowrap=1'><![CDATA[".$action_button_html."]]></cell>";
						echo "<cell property='align=center&amp;nowrap=1'><![CDATA[".tep_draw_checkbox_field('redeems_batch[]', $redeem_id, false, '', 'id="'.$redeem_id.'"'.($redeem_batch_available ? '' : ' DISABLED '))."]]></cell>";
						echo "</table_cell>";
					}
				} else {
					$res_code = '-1';
					$res_message = JS_ERROR_REDEEM_TRANS_NOT_EXISTS;
				}
			}
			
			echo '	<res_code><![CDATA['.$res_code.']]></res_code>
					<res_message><![CDATA['.$res_message.']]></res_message>';
			
			if ($email_beneficiary)	$xmlhttp_redeems_object->send_redeem_status_email($redeem_id, $email_comments_str);
			
			break;
		default:
			echo '<result>Unknown request!</result>';
			
			break;
	}
}

echo '</response>';
?>