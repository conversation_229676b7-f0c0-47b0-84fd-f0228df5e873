<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/configure.php');
// email classes
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
require_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'configuration.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');

$languages_id = 1;

tep_db_connect() or die('Unable to connect to database server!');
$configuration_select_sql = "SELECT configuration_key AS cfgKey, configuration_value AS cfgValue 
							FROM " . TABLE_CONFIGURATION;
$configuration_query = tep_db_query($configuration_select_sql);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}


if (!isset($_REQUEST['gj_key']) || md5($_REQUEST['gj_key']) != '5b8971248b54fe086bc536748927775b') {
	exit("Unable to process your request.");
}

if (!isset($_REQUEST['online_xml_data'])) {
	exit("Missing source.");
}

$online_xml_data = tep_db_prepare_input($_REQUEST['online_xml_data']);

function startElementGJ($parser, $name, $attrs) {
	global $char_id_array;
	
	if (isset($attrs['ID'])) {
		$char_id_array = array(	'id' => $attrs['ID'],
	        					'char_online_status' => '');
	}
}

function contentsGJ($parser, $data) {
	global $char_id_array, $main_array;
	
	if (count($char_id_array)) {
		$char_id_array['char_online_status'] = $data;
		$main_array[] = $char_id_array;
		unset($char_id_array);
	}
}

function endElementGJ($parser, $name){
	;
}

$xml_parser = xml_parser_create();

$char_id_array = array();
$main_array = array();

xml_set_element_handler($xml_parser, "startElementGJ", "endElementGJ");
xml_set_character_data_handler($xml_parser, "contentsGJ");

if (!(xml_parse($xml_parser, $online_xml_data, true))) {
    die("Error on line " . xml_get_current_line_number($xml_parser));
}

xml_parser_free($xml_parser);

$total_result = count($main_array);
for ($res_cnt=0; $res_cnt < $total_result; $res_cnt++) {
	$value = $main_array[$res_cnt];
	
	if (tep_not_null($value['id'])) {
		$check_if_processing_select_sql = " SELECT o.orders_id 
											FROM " . TABLE_ORDERS . " AS o 
											INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
												ON (o.orders_id = op.orders_id) 
											WHERE op.orders_products_id = '".tep_db_input($value['id'])."' 
												AND o.orders_status = '2'";
		$check_if_processing_result_sql = tep_db_query($check_if_processing_select_sql);
		
		if ($check_if_processing_row = tep_db_fetch_array($check_if_processing_result_sql)) {
			$sql_data_array = array('orders_products_id' => tep_db_prepare_input($value['id']),
									'orders_products_extra_info_key' => 'char_online_status',
									'orders_products_extra_info_value' => tep_db_prepare_input($value['char_online_status'])
									);
			
			$check_row_select_sql = " 	SELECT orders_products_extra_info_value 
										FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " 
										WHERE orders_products_id = '" . tep_db_input($value['id']) . "' 
											AND orders_products_extra_info_key = 'char_online_status'";
			$check_row_result_sql = tep_db_query($check_row_select_sql);
			
			if (tep_db_num_rows($check_row_result_sql)) {
				tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $sql_data_array, 'update', "orders_products_id = '" . tep_db_input($value['id']) . "' AND orders_products_extra_info_key = 'char_online_status'");
			} else {
				tep_db_perform(TABLE_ORDERS_PRODUCTS_EXTRA_INFO, $sql_data_array);
			}
			
			if ((int)$value['char_online_status'] == 1) {
				$vipOrderObj = new vip_order($value['id']);
				$vipOrderObj->get_products_bundle_id($value['id']);
				$vipOrderObj->get_orders_details();
				if ($vipOrderObj->check_vip_mode($vipOrderObj->order_detail['buyback_categories_id'])) {
					//start assign the order to supplier
					$vipOrderObj->get_available_supplier();
				}
			}
		}
	}
}

ob_start();

print_r($main_array);
$debug_html = ob_get_contents();
ob_end_clean();
?>