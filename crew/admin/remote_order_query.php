<?
/*******************************************************************
	Error code:
	1 - Invalid IP address
	2 - Order does not exists
	3 - Order is not in Processing status
	4 - Account not exists
	5 - Fully Delivered
*******************************************************************/

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/html');

$HTTP_POST_VARS = $_POST;
$HTTP_GET_VARS = $_GET;

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'rc4crypt.php');

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_query = tep_db_query("SELECT configuration_key as cfgKey, configuration_value AS cfgValue FROM " . TABLE_CONFIGURATION . " WHERE configuration_key IN ('PASSWORD_CONSOLE_IP_ADDRESS')");
while ($configuration = tep_db_fetch_array($configuration_query)) {
	define($configuration['cfgKey'], $configuration['cfgValue']);
}

define('VALID_PROCESSING_STATUS', '2');

$action = "";
if (isset($HTTP_POST_VARS["action"])) {
	$action = trim($HTTP_POST_VARS["action"]);
}

$order_id = "";
if (isset($HTTP_POST_VARS["order_id"])) {
	$order_id = (int)$HTTP_POST_VARS["order_id"];
}

$location = "";
if (isset($HTTP_POST_VARS["location"])) {
	$location = trim(tep_db_prepare_input($HTTP_POST_VARS["location"]));
}

$order_product_id = "";
if (isset($HTTP_POST_VARS["order_product_id"])) {
	$order_product_id = (int)$HTTP_POST_VARS["order_product_id"];
}

$console_ip = getenv("REMOTE_ADDR");

$server_response = array();
$order_products = array();
//$location_pattern = "/(?:\])*?(?:\s)*?([^\[ ]+)(?:\s)*?(?:\[)*?/is";
//$location_pattern = "/(?:\]*?)(?:\s*?)(".preg_quote($location, "/").")(?:\s|-|)+(?:\[*?)/is";
//$location_pattern = "/(?:.*?)(?:\]?)(?:\s?)([^\-\s]+)(?:\s|\-)?(?:\[?)/is";
$location_pattern = "/(\[)([^\]]*?)(\])/is";

$console_ip = PASSWORD_CONSOLE_IP_ADDRESS;
if ($console_ip == PASSWORD_CONSOLE_IP_ADDRESS) {	// Request coming from valid IP
	switch($action) {
		case "pima":
			// check order's status
			$orders_products_select_sql = "	SELECT op.orders_id, o.orders_status, op.products_quantity, op.products_delivered_quantity
											FROM " . TABLE_ORDERS_PRODUCTS . " AS op
											INNER JOIN " . TABLE_ORDERS . "  o
												ON op.orders_id = o.orders_id
											WHERE op.orders_products_id = '" . (int)$order_product_id . "'";
			$orders_products_result_sql = tep_db_query($orders_products_select_sql);
			if ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
				$rc4crypt_obj = new rc4crypt();
				if ($orders_products_row['orders_status'] == VALID_PROCESSING_STATUS) {
					$undelivered_quantity = $orders_products_row["products_quantity"] - $orders_products_row["products_delivered_quantity"];
					if ($undelivered_quantity > 0) {
						$orders_products_login_array = array();

						$orders_products_login_select_sql = "	SELECT opei.orders_products_extra_info_key, opei.orders_products_extra_info_value
																FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO  . " AS opei
																WHERE opei.orders_products_id = '" . (int)$order_product_id . "'
																	AND opei.orders_products_extra_info_key IN ('char_account_name', 'char_account_pwd')";
						$orders_products_login_result_sql = tep_db_query($orders_products_login_select_sql);
						while ($orders_products_login_row = tep_db_fetch_array($orders_products_login_result_sql)) {
							$orders_products_login_array[$orders_products_login_row['orders_products_extra_info_key']] = $orders_products_login_row['orders_products_extra_info_value'];
						}

						if (isset($orders_products_login_array['char_account_name']) && isset($orders_products_login_array['char_account_pwd'])) {
							$server_response['request_result'] = '1';
							$server_response['u_name'] = $rc4crypt_obj->encrypt( PIMA_RC4_KEY, $orders_products_login_array['char_account_name']);
							$server_response['k_name'] = $rc4crypt_obj->encrypt( PIMA_RC4_KEY, $orders_products_login_array['char_account_pwd']);
						} else {
							$server_response["request_result"] = '0';
							$server_response['error_code'] = 3;
						}
					} else {
						$server_response["request_result"] = '0';
						$server_response['error_code'] = 5;
					}
				} else {
		    		$server_response["request_result"] = '0';
					$server_response['error_code'] = 3;
				}
			}
			break;
			
		default:
			$orders_checking_select_sql = "	SELECT orders_id, orders_status
											FROM " . TABLE_ORDERS . "
											WHERE orders_id = '" . $order_id . "'";
			$orders_checking_result_sql = tep_db_query($orders_checking_select_sql);
		    $order_exists = true;
		    if ($orders_checking_row = tep_db_fetch_array($orders_checking_result_sql)) {
		    	if ($orders_checking_row["orders_status"] == VALID_PROCESSING_STATUS) {
		    		$purchased_product_select_sql = "SELECT op.products_id, op.orders_products_id, op.products_quantity, op.products_delivered_quantity, p.products_bundle, p.products_bundle_dynamic, pd.products_location
		    										 FROM " . TABLE_ORDERS_PRODUCTS . " AS op
		    										 INNER JOIN " . TABLE_PRODUCTS . " AS p
		    										 	ON op.products_id=p.products_id
		    										 INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
		    										 	ON ( op.products_id=pd.products_id AND pd.language_id=1 )
		    										 WHERE op.orders_id = '" . $order_id . "' AND op.products_bundle_id = 0 ";
		    		$purchased_product_result_sql = tep_db_query($purchased_product_select_sql);
		    		$index = 0;
		      		while ($purchased_product_row = tep_db_fetch_array($purchased_product_result_sql)) {
		      			$order_products[$index] = array('order_products_id' => $purchased_product_row['orders_products_id'],
														'qty' => $purchased_product_row['products_quantity'],
			                                        	'id' => $purchased_product_row['products_id'],
			                                        	'delivered_qty' => $purchased_product_row['products_delivered_quantity'],
			                                        	'location' => $purchased_product_row['products_location']
			                                        	);

						if ($purchased_product_row["products_bundle_dynamic"] == "yes") {
							$b_index = 0;
							$product_bundle_dynamic_select_sql = "	SELECT op.products_id, op.products_quantity, op.products_delivered_quantity, pd.products_location
																	FROM " . TABLE_ORDERS_PRODUCTS . " AS op
																	INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
		    										 					ON ( op.products_id=pd.products_id AND pd.language_id=1 )
																	WHERE op.orders_id = '" . $order_id . "' AND op.products_bundle_id = '" . $purchased_product_row["products_id"] . "'";
							$product_bundle_dynamic_result_sql = tep_db_query($product_bundle_dynamic_select_sql);
							while ($product_bundle = tep_db_fetch_array($product_bundle_dynamic_result_sql)) {
								$order_products[$index]['bundle'][$b_index] = array('id' => $product_bundle['products_id'],
																					'qty' => $product_bundle['products_quantity'],
				                                        							'delivered_qty' => $product_bundle['products_delivered_quantity'],
				                                        							'location' => $product_bundle['products_location']
				                                        							);
								$b_index++;
							}
						} else if ($purchased_product_row["products_bundle"] == "yes") {
							$s_index = 0;
							$static_bundle_select_sql = "	SELECT op.products_id, op.products_quantity, op.products_delivered_quantity, pd.products_location
															FROM " . TABLE_ORDERS_PRODUCTS . " AS op
															INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd
											 					ON ( op.products_id=pd.products_id AND pd.language_id=1 )
															WHERE op.orders_id = '" . $order_id . "' AND op.products_bundle_id = '" . $purchased_product_row["products_id"] . "'";

							$static_bundle_result_sql = tep_db_query($static_bundle_select_sql);
							while ($static_bundle = tep_db_fetch_array($static_bundle_result_sql)) {
								$order_products[$index]['static'][$s_index] = array('id' => $static_bundle['products_id'],
																					'qty' => $static_bundle['products_quantity'],
				                                        							'delivered_qty' => $static_bundle['products_delivered_quantity'],
				                                        							'location' => $static_bundle['products_location']
				                                        							);

								$s_index++;
							}
						}

						$index++;
		    		}

		    		$location_exists = false;
		    		$product_fully_delivered = false;
		    		$allow_to_deliver = false;

		    		for ($prod_cnt=0; $prod_cnt < count($order_products); $prod_cnt++) {
		    			if (isset($order_products[$prod_cnt]['bundle'])) {
		    				for ($pbd_loop=0; $pbd_loop < count($order_products[$prod_cnt]['bundle']); $pbd_loop++) {
		    					$location_array = array();

		    					$replaced_location = preg_replace($location_pattern, '#~#', $order_products[$prod_cnt]['bundle'][$pbd_loop]['location']);
			    				if (tep_not_null($replaced_location)) {
			    					$location_array = explode('#~#', $replaced_location);
			    				}

								for ($match=0; $match < count($location_array); $match++) {
				    				$sub_string = explode(' ', $location_array[$match]);
									for ($sub_cnt=0; $sub_cnt < count($sub_string); $sub_cnt++) {
										if (strtolower($location) == strtolower(trim($sub_string[$sub_cnt]))) {
											$location_exists = true;
											break;
										}
									}

				    				if ($location_exists) {
				    					if ($order_products[$prod_cnt]['bundle'][$pbd_loop]['delivered_qty'] < $order_products[$prod_cnt]['bundle'][$pbd_loop]['qty']) {
				    						$allow_to_deliver = true;
				    						break;
				    					} else {
				    						$product_fully_delivered = true;
				    					}
				    				}
				    			}
		    				}
		    			} else if (isset($order_products[$prod_cnt]['static'])) {
		    				for ($static_loop=0; $static_loop < count($order_products[$prod_cnt]['static']); $static_loop++) {
			    				$location_array = array();

			    				$replaced_location = preg_replace($location_pattern, '#~#', $order_products[$prod_cnt]['static'][$static_loop]['location']);
			    				if (tep_not_null($replaced_location)) {
			    					$location_array = explode('#~#', $replaced_location);
			    				}

			    				for ($match=0; $match < count($location_array); $match++) {
			    					$sub_string = explode(' ', $location_array[$match]);
									for ($sub_cnt=0; $sub_cnt < count($sub_string); $sub_cnt++) {
										if (strtolower($location) == strtolower(trim($sub_string[$sub_cnt]))) {
											$location_exists = true;
											break;
										}
									}

			    					if ($location_exists) {
				    					if ($order_products[$prod_cnt]['static'][$static_loop]['delivered_qty'] < $order_products[$prod_cnt]['static'][$static_loop]['qty']) {
				    						$allow_to_deliver = true;
				    						break;
				    					} else {
				    						$product_fully_delivered = true;
				    					}
				    				}
				    			}
				    		}
		    			} else {
		    				$location_array = array();

		    				$replaced_location = preg_replace($location_pattern, '#~#', $order_products[$prod_cnt]['location']);
		    				if (tep_not_null($replaced_location)) {
		    					$location_array = explode('#~#', $replaced_location);
		    				}

							/*
							while (preg_match($location_pattern, $order_products[$prod_cnt]['location'], $regs)) {
								$order_products[$prod_cnt]['location'] = str_replace($regs[1], '', $order_products[$prod_cnt]['location']);
								$location_array[] = $regs[1];
							}
							*/

							for ($match=0; $match < count($location_array); $match++) {
								$sub_string = explode(' ', $location_array[$match]);
								for ($sub_cnt=0; $sub_cnt < count($sub_string); $sub_cnt++) {
									if (strtolower($location) == strtolower(trim($sub_string[$sub_cnt]))) {
										$location_exists = true;
										break;
									}
								}

			    				if ($location_exists) {
			    					if ($order_products[$prod_cnt]['delivered_qty'] < $order_products[$prod_cnt]['qty']) {
			    						$allow_to_deliver = true;
			    						break;
			    					} else {
			    						$product_fully_delivered = true;
			    					}
			    				}
			    			}
		    			}
		    		}

		    		if ($allow_to_deliver) {
		    			$server_response["request_result"] = '1';
		    		} else if ($product_fully_delivered) {
		    			$server_response["request_result"] = '0';
						$server_response['error_code'] = 5;
		    		} else if (!$location_exists) {
		    			$server_response["request_result"] = '0';
						$server_response['error_code'] = 4;
		    		}
		    	} else {
		    		$server_response["request_result"] = '0';
					$server_response['error_code'] = 3;
		    	}
		    } else {
		    	$server_response["request_result"] = '0';
				$server_response['error_code'] = 2;
		    }
			break;
	}
} else {
	$server_response["request_result"] = '0';
	$server_response['error_code'] = 1;
}

if (isset($server_response) && count($server_response)) {
	$value_pair_array = array();
	foreach ($server_response as $key => $val) {
		$value_pair_array[] = "$key=".rawurlencode($val);
	}

	echo implode('&', $value_pair_array);
}
?>