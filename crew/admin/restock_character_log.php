<?php
require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');
$rcsID = (isset($_REQUEST['rcsID']) ? $_REQUEST['rcsID'] : '');
$product_id = (isset($_REQUEST['product_id']) ? $_REQUEST['product_id'] : '');


if (tep_not_null($action)) {
	switch($action) {
		case 'report':
			if (isset($HTTP_POST_VARS['search'])) {
				$_SESSION['restock_character_param']["restock_character_start_date"] = tep_db_prepare_input($HTTP_POST_VARS['restock_character_start_date']);
				$_SESSION['restock_character_param']["restock_character_end_date"] = tep_db_prepare_input($HTTP_POST_VARS['restock_character_end_date']);
				$_SESSION['restock_character_param']["rcsID"] = tep_db_prepare_input($HTTP_POST_VARS['rcsID']);
				$_SESSION['restock_character_param']["product_id"] = tep_db_prepare_input($HTTP_POST_VARS['product_id']);
			} else if (isset($HTTP_POST_VARS["reset"])) {
				unset($_SESSION['restock_character_param']);
				tep_redirect(tep_href_link(FILENAME_RESTOCK_CHARACTER_LOG, 'action=search'));
			}
			break;
	}
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
	<tr>
<!-- body_text //-->
		<td width="100%" valign="top">
<?
if ($action == 'search') {
?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
				</tr>
				<tr>
					<td>
<?						echo tep_draw_form('custom_product_criteria', FILENAME_RESTOCK_CHARACTER_LOG, 'action=report', 'post'); ?>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td colspan="2"><span class="pageHeading"><?=sprintf(HEADING_INPUT_TITLE, HEADING_CRITERIA)?></span></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td width="11%">
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" width="12%"><?=ENTRY_HEADING_RSTK_SUBMIT_START_DATE?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap width="20%"><?=tep_draw_input_field('restock_character_start_date', $_SESSION['restock_character_param']["restock_character_start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.custom_product_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.custom_product_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
												<td class="main" width="12%"><?=ENTRY_HEADING_RSTK_SUBMIT_END_DATE?></td>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('restock_character_end_date', $_SESSION['restock_character_param']["restock_character_end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.custom_product_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.custom_product_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
											</tr>
										</table>
<?
										echo tep_draw_hidden_field('rcsID', $rcsID);
										echo tep_draw_hidden_field('product_id', $product_id);
?>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td colspan="2" align="right">
										<?=tep_submit_button(IMAGE_BUTTON_SEARCH, IMAGE_BUTTON_SEARCH, 'name="search"', 'inputButton')?>
										<?=tep_submit_button(IMAGE_BUTTON_RESET, IMAGE_BUTTON_RESET, 'name="reset"', 'inputButton')?>
									</td>
								</tr>
							</table>
						</form>
					</td>
				</tr>
			</table>
<?
} else if ($action == 'report') {
	if (tep_not_null($_SESSION['restock_character_param']["restock_character_start_date"])) {
		if (strpos($_SESSION['restock_character_param']["restock_character_start_date"], ':') !== false) {
			$startDateObj = explode(' ', trim($_SESSION['restock_character_param']["restock_character_start_date"]));
			list($yr, $mth, $day) = explode('-', $startDateObj[0]);
			list($hr, $min) = explode(':', $startDateObj[1]);
			$start_date_str = " ( DATE_FORMAT(restock_characters_log_date, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['restock_character_param']["restock_character_start_date"]));
			$start_date_str = " ( DATE_FORMAT(restock_characters_log_date, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
		}
	} else {
		$start_date_str = " 1 ";
	}
	
	if (tep_not_null($_SESSION['restock_character_param']["restock_character_end_date"])) {
		if (strpos($_SESSION['restock_character_param']["restock_character_end_date"], ':') !== false) {
			$endDateObj = explode(' ', trim($_SESSION['restock_character_param']["restock_character_end_date"]));
			list($yr, $mth, $day) = explode('-', $endDateObj[0]);
			list($hr, $min) = explode(':', $endDateObj[1]);
			$end_date_str = " ( DATE_FORMAT(restock_characters_log_date, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['restock_character_param']["restock_character_end_date"]));
			$end_date_str = " ( DATE_FORMAT(restock_characters_log_date, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
		}
	} else {
		$end_date_str = " 1 ";
	}
	
	if (tep_not_null($_SESSION['restock_character_param']["rcsID"])) {
		$restock_character_id_where_str = " restock_character_sets_id ='" . (int)$_SESSION['restock_character_param']["rcsID"] . "'";
	} else {
		$restock_character_id_where_str = " 1 ";
	}
	
	if (tep_not_null($_SESSION['restock_character_param']["product_id"])) {
		$products_id_where_str = " products_id ='" . (int)$_SESSION['restock_character_param']["product_id"] . "' ";
	} else {
		$products_id_where_str = " 1 ";
	}
	
	$restock_character_log_row_count = 0;
	
	$products_cat_path_select_sql = "SELECT products_cat_path FROM " . TABLE_PRODUCTS . " WHERE products_id='" . (int)$_SESSION['restock_character_param']["product_id"] . "'";
	$products_cat_path_result_sql = tep_db_query($products_cat_path_select_sql);
	$products_cat_path_row = tep_db_fetch_array($products_cat_path_result_sql);
?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td colspan="5"><span class="pageHeading"><?=sprintf(HEADING_INPUT_TITLE, 'For [' . $products_cat_path_row['products_cat_path']) . ']'?></span></td>
				</tr>
				<tr>
					<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				</tr>
				<tr>
					<td class="ordersBoxHeading"><?=TABLE_HEADING_DATE?></td>
					<td class="ordersBoxHeading"><?=TABLE_HEADING_RSTK_CHARACTER_BEFORE?></td>
					<td class="ordersBoxHeading"><?=TABLE_HEADING_RSTK_CHARACTER_AFTER?></td>
					<td class="ordersBoxHeading"><?=TABLE_HEADING_CHANGE_BY?></td>
					<td class="ordersBoxHeading"><?=TABLE_HEADING_IP?></td>
				</tr>
<?
	$restock_character_log_select_sql = "	SELECT rsl.*, a.admin_email_address 
											FROM " . TABLE_RESTOCK_CHARACTER_LOG . " AS rsl 
											LEFT JOIN " . TABLE_ADMIN . " AS a 
												ON (a.admin_id = rsl.restock_character_log_admin_id)
											WHERE $restock_character_id_where_str 
												AND $products_id_where_str 
												AND $start_date_str 
												AND $end_date_str ";
	
	if ($show_records != "ALL") {
		$restock_char_log_split_object = new splitPageResults($HTTP_GET_VARS['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $restock_character_log_select_sql, $restock_character_log_select_sql_numrows, true);
	}
	
	$restock_character_log_result_sql = tep_db_query($restock_character_log_select_sql);
	
	while ($restock_character_log_row = tep_db_fetch_array($restock_character_log_result_sql)) {
		$restock_character_log_row_style = ($restock_character_log_row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
?>
				<tr class="<?=$restock_character_log_row_style?>">
					<td class="ordersRecords"><?=$restock_character_log_row['restock_characters_log_date']?></td>
					<td class="ordersRecords"><?=$restock_character_log_row['restock_character_before']?></td>
					<td class="ordersRecords"><?=$restock_character_log_row['restock_character_after']?></td>
					<td class="ordersRecords"><?=(is_numeric($restock_character_log_row['restock_character_log_admin_id']) ? $restock_character_log_row['admin_email_address'] : $restock_character_log_row['restock_character_log_admin_id'])?></td>
					<td class="ordersRecords"><?=$restock_character_log_row['restock_characters_log_ip']?></td>
				</tr>
<?
		$restock_character_log_row_count++;
	}
?>
				<tr>
					<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				</tr>
				<tr>
					<td colspan="5">
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_LOGS, tep_db_num_rows($restock_character_log_select_sql) > 0 ? "1" : "0", tep_db_num_rows($restock_character_log_select_sql), tep_db_num_rows($restock_character_log_select_sql)) : $restock_char_log_split_object->display_count($restock_character_log_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_LOGS)?></td>
								<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $restock_char_log_split_object->display_links($restock_character_log_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'cont'))."cont=1", 'page')?></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
<?
}
?>
		</td>
	</tr>
	<tr>
		<td><?=tep_draw_separator('pixel_trans.gif', '1', '100')?></td>
	</tr>
</table>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>