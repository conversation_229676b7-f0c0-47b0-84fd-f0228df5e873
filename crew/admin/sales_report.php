<?php
/*
  $Id: sales_report.php,v 1.72 2015/02/12 09:08:10 chingyen Exp $

  Developer: <PERSON>
  Copyright (c) 2005 SKC Ventrue

  Released under the GNU General Public License
 */

/*******************************************************
  2007-01-02 - Do not check for category access permission
*******************************************************/

require('includes/application_top.php');
ini_set("memory_limit", "2048M");
tep_set_time_limit(0);

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

require_once(DIR_WS_CLASSES . 'sales_report.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
require_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
$currencies = new currencies();
$memcache_obj = new OGM_Cache_MemCache();

include_once(DIR_WS_FUNCTIONS . 'custom_product.php');

define('SYSTEM_PAYMENT_STORE_CREDITS', 'OGM_CREDITS');
define('REPORT_MAX_DATE_RANGE', 7);

if (tep_not_null($action)) {
    switch ($action) {
        case 'do_export':
            $file_location = $_SESSION['sales_param']['filename'];

            if (file_exists($file_location)) {
                $filename = basename($file_location);
                $mime_type = 'text/x-csv';

                // Download
                header('Content-Type: ' . $mime_type);
                header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');

                // IE need specific headers
                if (PMA_USR_BROWSER_AGENT == 'IE') {
                    header('Content-Disposition: attachment; filename="' . $filename . '"');
                    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                    header('Pragma: public');
                } else {
                    header('Content-Disposition: attachment; filename="' . $filename . '"');
                    header('Pragma: no-cache');
                }

                unset($_REQUEST['hidden_export_report']);
                readfile($file_location);
                exit();
            }
            break;
        case 'reset_session':
            unset($_SESSION['sales_param']);
            tep_redirect(tep_href_link(FILENAME_SALES_REPORT));
            break;
    }
}

// make a connection to the database... now
$read_db_link = tep_db_connect(DB_RR_SERVER, DB_RR_SERVER_USERNAME, DB_RR_SERVER_PASSWORD, DB_RR_DATABASE, 'read_db_link') or die('Unable to connect to database server!');

/*-- prepare csv file content --*/
if (tep_not_null($_REQUEST['hidden_export_report'])) {
    $report_header_csv = array();
    $report_obj = tep_generate_sales_report($_REQUEST);

    $report_header_csv['HEADER'][] = $report_obj->summary["first_column_title"];
    $report_header_csv['SUB_HEADER_1'][] = '';

    switch ($_REQUEST["report"]) {
        case 2: # Daily
            $mime_type = 'text/x-csv';

            // Download
            header('Content-Type: ' . $mime_type);
            header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');

            // IE need specific headers
            if (PMA_USR_BROWSER_AGENT == 'IE') {
                header('Content-Disposition: attachment; filename="sales_report_' . date('YmdHis') . '.csv"');
                header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                header('Pragma: public');
            } else {
                header('Content-Disposition: attachment; filename="sales_report_' . date('YmdHis') . '.csv"');
                header('Pragma: no-cache');
            }

            $report_obj->generate_csv_file('', $_REQUEST['report']);
            exit();
            break;

        case 7: # Category (Sales + Stock)
            $report_header_csv['HEADER'][] = $report_obj->summary["type"];
            $report_header_csv['SUB_HEADER_1'][] = '';

            $report_header_csv['HEADER'][] = 'Product ID';
            $report_header_csv['SUB_HEADER_1'][] = '';

            if (in_array('available_qty', $report_obj->prod_qty_type)) {
                $report_header_csv['HEADER'][] = $report_obj->summary["second_column_title"];
                $report_header_csv['SUB_HEADER_1'][] = '';
            }

            if (in_array('actual_qty', $report_obj->prod_qty_type)) {
                $report_header_csv['HEADER'][] = $report_obj->summary["third_column_title"];
                $report_header_csv['SUB_HEADER_1'][] = '';
            }

            for ($i = $report_obj->size - 1; $i >= 0; $i--) {
                $display_date = date($report_obj->sales_period == 'day' ? 'Y-m-d' : "M, Y", $report_obj->startDates[$i]);

                if ($i == $report_obj->size - 1) {
                    if ($report_obj->sales_period == 'day') {
                        $display_date = date('Y-m-d', $report_obj->startDates[$i]);
                    } else {
                        if ($report_obj->endDates[$i] > mktime(0, 0, 0, date("m"), date("d"), date("Y"))) {
                            $display_date = 'Up to ' . date('Y-m-d');
                        } else {
                            $display_date = date("M, Y", $report_obj->startDates[$i]);
                        }
                    }
                }

                $report_header_csv['HEADER'][] = $display_date;
                $report_header_csv['HEADER'][] = '';

                $report_header_csv['SUB_HEADER_1'][] = 'Qty Sold';
                $report_header_csv['SUB_HEADER_1'][] = 'Sales';
            }

            $filename = 'download/sales_report_' . date('YmdHis') . '.csv';
            $fp = fopen($filename, 'w');
            if ($fp) {
                foreach ($report_header_csv['HEADER'] as $key => $value) {
                    $header .= '"' . $value . '",';
                }
                fwrite($fp, $header . "\n");

                foreach ($report_header_csv['SUB_HEADER_1'] as $key => $value) {
                    $sub_header_1 .= '"' . $value . '",';
                }
                fwrite($fp, $sub_header_1 . "\n");

                $report_obj->categoryExportCatTree($_REQUEST["cat_level"], $currencies, $fp);
                fclose($fp);
                unset($report_obj->info);
            }
            break;

        case 8: # Country
        case 9: # Customer Grouping
        case 10: # New Sign Up
            $report_header_csv['HEADER'][] = $report_obj->summary["second_column_title"];
            $report_header_csv['SUB_HEADER_1'][] = '';

            if ($_REQUEST["report"] == '10') {
                $report_header_csv['HEADER'][] = $report_obj->summary["after_second_column_title"];
                $report_header_csv['SUB_HEADER_1'][] = '';
            }

            $report_header_csv['HEADER'][] = $report_obj->summary["third_column_title"];
            $report_header_csv['SUB_HEADER_1'][] = '';

            $report_header_csv['HEADER'][] = $report_obj->summary["forth_column_title"];
            $report_header_csv['SUB_HEADER_1'][] = '';

            for ($i = $report_obj->size - 1; $i >= 0; $i--) {
                $display_date = date($report_obj->sales_period == 'day' ? 'Y-m-d' : "M, Y", $report_obj->startDates[$i]);

                if ($i == $report_obj->size - 1) {
                    if ($report_obj->sales_period == 'day') {
                        $display_date = date('Y-m-d', $report_obj->startDates[$i]);
                    } else {
                        if ($report_obj->endDates[$i] > mktime(0, 0, 0, date("m"), date("d"), date("Y"))) {
                            $display_date = 'Up to ' . date('Y-m-d');
                        } else {
                            $display_date = date("M, Y", $report_obj->startDates[$i]);
                        }
                    }
                }

                $report_header_csv['HEADER'][] = $display_date;
                if ($_REQUEST["report"] == '9') {
                    $report_header_csv['HEADER'][] = '';
                    $report_header_csv['SUB_HEADER_1'][] = 'Sales';
                    $report_header_csv['SUB_HEADER_1'][] = '%';
                } else if ($_REQUEST["report"] == '10') {
                    $report_header_csv['HEADER'][] = '';
                    $report_header_csv['HEADER'][] = '';
                    $report_header_csv['SUB_HEADER_1'][] = TABLE_HEADING_NEW_SIGN_UP_SUB_TOTAL;
                    $report_header_csv['SUB_HEADER_1'][] = TABLE_HEADING_NEW_SIGN_UP_SUB_PURCHASE;
                } else {
                    $report_header_csv['SUB_HEADER_1'][] = '';
                    $report_header_csv['SUB_HEADER_1'][] = '';
                }
            }

            /*-- csv data content --*/
            $order_count = 0;
            $cust_count = 0;
            $sum = 0;

            for ($i = 0; $i < count($report_obj->info); $i++) {
                $content = array();

                $order_count += $report_obj->info[$i]['count'];
                $cust_count += $report_obj->info[$i]['total_customer'];
                $sum += $report_obj->info[$i]['sum'];

                if ($report_obj->mode == '8') {
                    $display_name = $report_obj->info[$i]["text"];
                } else if ($report_obj->mode == '9') {
                    $display_name = $report_obj->info[$i]['customers_groups_name'];
                } else if ($report_obj->mode == '10') {
                    $display_name = TEXT_NEW_SIGN_UP;
                }

                $content[] = $display_name;
                if ($_REQUEST["report"] == '10')
                    $content[] = tep_not_null($report_obj->info[$i]["total_sign_up"]) ? $report_obj->info[$i]["total_sign_up"] : '0';
                $content[] = tep_not_null($report_obj->info[$i]["total_customer"]) ? $report_obj->info[$i]["total_customer"] : '0';
                $content[] = tep_not_null($report_obj->info[$i]["count"]) ? $report_obj->info[$i]["count"] : '0';
                $content[] = $currencies->format($report_obj->info[$i]["avg"]);

                for ($j = $report_obj->size - 1; $j >= 0; $j--) {
                    $content[] = $currencies->format($report_obj->info[$i]['subtotal'][$report_obj->startDates[$j]]);

                    if ($_REQUEST["report"] == '9') {
                        $content[] = tep_not_null($report_obj->info[$i]['sales_percentage'][$report_obj->startDates[$j]]) ? $report_obj->info[$i]['sales_percentage'][$report_obj->startDates[$j]] : '0';
                    } else if ($_REQUEST["report"] == '10') {
                        $content[] = (isset($report_obj->info[0]['new_sign_up'][$report_obj->startDates[$j]]) ? $report_obj->info[0]['new_sign_up'][$report_obj->startDates[$j]] : 0);
                        $content[] = (isset($report_obj->info[0]['with_purchase'][$report_obj->startDates[$j]]) ? $report_obj->info[0]['with_purchase'][$report_obj->startDates[$j]] : 0);
                    }
                }

                $report_content_csv[] = $content;
            }

            if (count($report_obj->info)) {
                if ($_REQUEST["report"] != "10") { # exclude New Sign Up
                    /* -- total customer & order -- */
                    $content = array();
                    $content[] = 'Total:';
                    $content[] = $cust_count;
                    $content[] = $order_count;

                    if ($_REQUEST["report"] == '9') {
                        $content[] = ''; # column for avg order

                        for ($i = $report_obj->size - 1; $i >= 0; $i--) {
                            $content[] = $currencies->format($report_obj->subtotal[$report_obj->startDates[$i]]);
                            $content[] = ''; # column for sales percentage
                        }
                    }

                    $report_content_csv[] = $content;
                }

                /* -- average order -- */
                $content = array();
                $report_content_csv[] = $content;
                $content[] = ENTRY_AVERAGE_ORDER;
                $content[] = $currencies->format($sum / $order_count);
                $report_content_csv[] = $content;

                /* -- average daily / monthly total -- */
                $content = array();
                $content[] = $report_obj->summary["mode_summary"];
                $content[] = $currencies->format($sum / $report_obj->size);
                $report_content_csv[] = $content;

                /* -- total sales -- */
                $content = array();
                $content[] = $report_obj->summary["parent_mode_summary"];
                $content[] = $currencies->format($sum);
                $report_content_csv[] = $content;
            }
    }

    if ($_REQUEST["report"] != '7') {
        if (tep_not_null($report_content_csv)) {
            $report_csv_data = array_merge($report_header_csv, $report_content_csv);
        } else {
            $report_csv_data = $report_header_csv;
        }

        unset($report_header_csv);
        unset($report_content_csv);

        $filename = $report_obj->generate_csv_file($report_csv_data);

        /* -- redirect page to prompt download file windows -- */
        $_SESSION['sales_param']['filename'] = 'download/' . $filename;
    } else {
        $_SESSION['sales_param']['filename'] = $filename;
    }

    if ($_REQUEST["report"] != '2') {
        tep_redirect(tep_href_link(FILENAME_SALES_REPORT . '?action=do_export'));
    }
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/jquery.tree.css">
        <link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
        <script language="javascript" src="includes/javascript/xmlhttp.js"></script>
        <script language="javascript" src="includes/javascript/product_listing.js"></script>
        <script language="javascript" src="includes/general.js"></script>
        <script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
        <script language="JavaScript" src="includes/javascript/jquery.js"></script>
        <script type="text/javascript" src="includes/javascript/jquery.tree.js"></script>
        <script language="javascript" src="includes/javascript/ogm_jquery.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
        <SCRIPT LANGUAGE="JavaScript1.2" SRC="jsgraph/graph.js"></SCRIPT>
        <div id="spiffycalendar" class="text"></div>
        <!-- header //-->
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?= BOX_WIDTH ?>" valign="top">
                    <table border="0" width="<?= BOX_WIDTH ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <?php

                        /* -- display result -- */
                        if (($_REQUEST['action'] == 'show_report') && (!tep_not_null($_REQUEST['hidden_export_report']))) {
                            $report_header_csv = array();
                            $report_obj = tep_generate_sales_report($_REQUEST);
                            ?>
                            <tr>
                                <td>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td>
                                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        <td class="pageHeading" valign="top"><?= $report_obj->summary["report_title"] ?></td>
                                                        <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                                    </tr>
                                                    <?
                                                    if ($report_obj->summary["date_heading"]) {
                                                        ?>
                                                        <tr>
                                                            <td class="main" colspan="2"><?= $report_obj->summary["date_heading"] ?></td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                        </tr>
                                                    <? } ?>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td valign="top">
                                                <?
                                                switch ($report_obj->mode) {
                                                    case 1: # Hourly
                                                    case 2: # Daily
                                                    case 3: # Weekly
                                                    case 4: # Monthly
                                                    case 5: # Yearly
                                                        ?>
                                                        <table border="0" width="100%" cellspacing="1" cellpadding="2">
                                                            <tr>
                                                                <td class="reportBoxHeading"><?= $report_obj->summary["first_column_title"] ?></td>
                                                                <td class="reportBoxHeading" align="left"><?= TABLE_HEADING_ORDERS ?></td>
                                                                <td class="reportBoxHeading" align="right"><?= TABLE_HEADING_SALES_PER_ORDER ?></td>
                                                                <td class="reportBoxHeading" align="right"><?= TABLE_HEADING_SALES ?></td>
                                                                <td class="reportBoxHeading" align="center"><?= TABLE_HEADING_VARIANCE ?></td>
                                                            </tr>
                                                            <?
                                                            $row_count = 0;
                                                            $order_count = 0;
                                                            $sum = 0;

                                                            for ($i = 0; $i < count($report_obj->info); $i++) {
                                                                if ($row_count) {
                                                                    if ($last_value > 0) {
                                                                        $percent = 100 * $report_obj->info[$i]['sum'] / $last_value - 100;
                                                                    } else if ($report_obj->info[$i]['sum'] > 0) {
                                                                        $percent = "n/a";
                                                                    } else {
                                                                        $percent = '---';
                                                                    }
                                                                } else {
                                                                    $percent = "n/a";
                                                                }

                                                                $last_value = $report_obj->info[$i]['sum'];
                                                                $order_count += $report_obj->info[$i]['count'];
                                                                $sum += $report_obj->info[$i]['sum'];
                                                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                                                                if ($report_obj->info[$i]["link"] != "" && $report_obj->info[$i]["count"]) {
                                                                    $display_name = '<a href="' . tep_href_link(FILENAME_SALES_REPORT, $report_obj->info[$i]['link'], 'NONSSL') . '">' . $report_obj->info[$i]["text"] . '</a>';
                                                                } else {
                                                                    $display_name = $report_obj->info[$i]["text"];
                                                                }
                                                                ?>
                                                                <tr class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                                                    <td class="reportRecords" valign="top"><?= $display_name ?></td>
                                                                    <td class="reportRecords" align="left" valign="top">
                                                                        <?
                                                                        echo $report_obj->info[$i]["count"];

                                                                        if ($report_obj->mode == "1") { // show all order ids for hourly report
                                                                            if (isset($report_obj->order[$i]["order_id"])) {
                                                                                for ($k = 0; $k < count($report_obj->order[$i]["order_id"]); $k++) {
                                                                                    echo '<br><a href="' . tep_href_link(FILENAME_ORDERS, "action=edit&oID=" . $report_obj->order[$i]["order_id"][$k]) . '" target="_blank">Order ID: ' . $report_obj->order[$i]["order_id"][$k] . '</a>&nbsp;&nbsp;Total: ' . $currencies->format($report_obj->order[$i]["order_amount"][$k]);
                                                                                }
                                                                            }
                                                                        }
                                                                        ?>
                                                                    </td>
                                                                    <td class="reportRecords" align="right" valign="top"><?= $currencies->format($report_obj->info[$i]["avg"]) ?></td>
                                                                    <td class="reportRecords" align="right" valign="top"><?= $currencies->format($report_obj->info[$i]["sum"]) ?></td>
                                                                    <td class="reportRecords" align="center" valign="top"><?= (is_numeric($percent)) ? ($percent == 0 ? "---" : number_format($percent, 0) . "%") : $percent ?></td>
                                                                </tr>
                                                                <?
                                                                $row_count++;
                                                            }
                                                            ?>
                                                        </table>
                                                        <?php
                                                        break;

                                                    case 6: # Category
                                                    case 7: # Category (Sales + Stock)
                                                        echo 'Start: ' . number_format(memory_get_usage(), 0, '.', ',') . " bytes\n";

                                                        if ($report_obj->mode == 6) {
                                                            echo '	<table border="0" width="80%" cellspacing="0" cellpadding="2">
									<tr>
										<td class="reportBoxHeading" width="1%" align="center">&nbsp;</td>
										<td class="reportBoxHeading">' . $report_obj->summary["first_column_title"] . '</td>
										<td class="reportBoxHeading" align="right">' . TABLE_HEADING_SALES . '</td>
									</tr>';
                                                        } else {
                                                            echo '	<table border="0" width="100%" cellspacing="1" cellpadding="2">	
								<tr>
									<td class="reportBoxHeading" rowspan="2" width="1%" align="center">&nbsp;</td>
									<td class="reportBoxHeading" rowspan="2">' . $report_obj->summary["first_column_title"] . '</td>' .
                                                            (in_array('available_qty', $report_obj->prod_qty_type) ? '<td width="5%" align="center" class="reportBoxHeading" rowspan="2">' . $report_obj->summary["second_column_title"] . '</td>' : '') .
                                                            (in_array('actual_qty', $report_obj->prod_qty_type) ? '<td width="5%" align="center" class="reportBoxHeading" rowspan="2">' . $report_obj->summary["third_column_title"] . '</td>' : '') . '
									<td class="reportBoxHeading" align="center" colspan="' . ($report_obj->size * 2) . '">' . $report_obj->summary["sales_category_title"] . '</td>
								</tr>
								<tr>';

                                                            for ($i = $report_obj->size - 1; $i >= 0; $i--) {
                                                                $display_date = date($report_obj->sales_period == 'day' ? 'Y-m-d' : "M, Y", $report_obj->startDates[$i]);

                                                                echo '<td class="reportBoxHeading" align="center" valign="bottom" width="10%" colspan="2" nowrap>';

                                                                if ($i == $report_obj->size - 1) {
                                                                    if ($report_obj->sales_period == 'day') {
                                                                        $display_date = date('Y-m-d', $report_obj->startDates[$i]);
                                                                    } else {
                                                                        if ($report_obj->endDates[$i] > mktime(0, 0, 0, date("m"), date("d"), date("Y"))) {
                                                                            $display_date = 'Up to ' . date('Y-m-d');
                                                                        } else {
                                                                            $display_date = date("M, Y", $report_obj->startDates[$i]);
                                                                        }
                                                                    }
                                                                }

                                                                echo $display_date;
                                                                echo '</td>';
                                                            }

                                                            echo '</tr>';
                                                        }

                                                        if ($report_obj->mode == 6) {
                                                            $report_content_csv = $report_obj->displayCatTree($_REQUEST["cat_level"], $currencies);
                                                        } else {
                                                            $report_obj->categoryDisplayCatTree($_REQUEST['cat_level'], $currencies);
                                                            unset($report_obj->info);  # clear variable to free memory, already display in interface (table)
                                                        }

                                                        echo 'End: ' . number_format(memory_get_usage(), 0, '.', ',') . " bytes\n";

                                                        $sum = $report_obj->totalSales;
                                                        ?>
                                                        <script language="javascript">
            <!--
                                                                var p2C = new Array();
            var level = 0;
            <?
            foreach ($report_obj->parantChild as $parentID => $childs) {
                echo "p2C['" . $parentID . "'] = new Array();\n";
                foreach ($childs as $child) {
                    echo "p2C['" . $parentID . "'].push($child);\n";
                }
            }

            unset($report_obj->parantChild);  # clear variable to free memory, already pipe to javascript
            ?>
            function expandCollapse(parent, styleClass) {
            if (styleClass == "show") {
                level = 1;
                doExpandCollapse(parent, styleClass, 1);
            } else {
                level = 1000;
                doExpandCollapse(parent, styleClass, 1);
            }
            }

            function doExpandCollapse(parent, styleClass, userLevel) {
            if (userLevel > level) {
                return true;
            }

            for (a in p2C[parent]) {
                document.getElementById("row_" + p2C[parent][a]).className = styleClass;
                if (p2C[p2C[parent][a]]) {
                    doExpandCollapse(p2C[parent][a], styleClass, (parseInt(userLevel) + 1))
                }
            }

            if (styleClass == "show") {
                document.getElementById("cell_" + parent).innerHTML = "<a href=\"javascript:;\" onClick=\"expandCollapse('" + parent + "', 'hide')\">" + "<?= addslashes(tep_image(DIR_WS_ICONS . "collapse.gif", '', 10, 10, 'border=0')) ?>" + "</a>";
            } else {
                document.getElementById("cell_" + parent).innerHTML = "<a href=\"javascript:;\" onClick=\"expandCollapse('" + parent + "', 'show')\">" + "<?= addslashes(tep_image(DIR_WS_ICONS . "expand.gif", '', 10, 10, 'border=0')) ?>" + "</a>";
            }
            return true;
            }
            //-->
                                                        </script>
                                                        <?
                                                        break;

                                                    case 8: # Country
                                                        ?>
                                                        <table border="0" width="100%" cellspacing="1" cellpadding="2">
                                                            <tr>
                                                                <td class="reportBoxHeading"><?php echo $report_obj->summary["first_column_title"]; ?></td>
                                                                <td width="10%" class="reportBoxHeading" align="center"><?php echo $report_obj->summary["second_column_title"]; ?></td>
                                                                <td width="10%" class="reportBoxHeading" align="center"><?php echo $report_obj->summary["third_column_title"]; ?></td>
                                                                <td width="10%" class="reportBoxHeading" align="right"><?php echo $report_obj->summary["forth_column_title"]; ?></td>
                                                                <?php
                                                                for ($i = $report_obj->size - 1; $i >= 0; $i--) {
                                                                    ?>
                                                                    <td class="reportBoxHeading" align="center" width="10%" nowrap>
                                                                        <?php echo (($report_obj->sales_period == 'day') ? date('Y-m-d', $report_obj->startDates[$i]) : date("M, Y", $report_obj->startDates[$i])); ?>
                                                                    </td>
                                                                    <?php
                                                                }
                                                                ?>
                                                            </tr>
                                                            <?
                                                            $row_count = 0;
                                                            $order_count = 0;
                                                            $cust_count = 0;
                                                            $sum = 0;

                                                            for ($i = 0; $i < count($report_obj->info); $i++) {
                                                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                                $order_count += $report_obj->info[$i]['count'];
                                                                $cust_count += $report_obj->info[$i]['total_customer'];
                                                                $sum += $report_obj->info[$i]['sum'];
                                                                ?>
                                                                <tr class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                                                    <td class="reportRecords" valign="top" nowrap><?php echo $report_obj->info[$i]["text"]; ?></td>
                                                                    <td class="reportRecords" align="center" valign="top" nowrap><?= tep_not_null($report_obj->info[$i]["total_customer"]) ? $report_obj->info[$i]["total_customer"] : '0' ?></td>
                                                                    <td class="reportRecords" align="center" valign="top" nowrap><?= tep_not_null($report_obj->info[$i]["count"]) ? $report_obj->info[$i]["count"] : '0' ?></td>
                                                                    <td class="reportRecords" align="right" valign="top" nowrap><?= $currencies->format($report_obj->info[$i]["avg"]) ?></td>			
                                                                    <?php
                                                                    for ($j = $report_obj->size - 1; $j >= 0; $j--) {
                                                                        $color_style = '';
                                                                        unset($next);

                                                                        $current = (isset($report_obj->info[$i]['subtotal'][$report_obj->startDates[$j]]) ? $report_obj->info[$i]['subtotal'][$report_obj->startDates[$j]] : 0);
                                                                        if (($j - 1) >= 0) {
                                                                            $next = (isset($report_obj->info[$i]['subtotal'][$report_obj->startDates[($j - 1)]]) ? $report_obj->info[$i]['subtotal'][$report_obj->startDates[($j - 1)]] : 0);
                                                                        }

                                                                        if (isset($next)) {
                                                                            if ($current < $next) {
                                                                                $color_style = 'redIndicator';
                                                                            } else if ($current > $next) {
                                                                                $color_style = 'greenIndicator';
                                                                            }
                                                                        }
                                                                        ?>
                                                                        <td class="reportRecords" align="right" valign="top" width="10%" nowrap>
                                                                            <span class="<?php echo $color_style; ?>"><?php echo $currencies->format($current); ?></span>
                                                                        </td>
                                                                        <?php
                                                                    }
                                                                    ?>						
                                                                </tr>
                                                                <?php
                                                                $row_count++;
                                                            }

                                                            if (count($report_obj->info)) {
                                                                ?>
                                                                <tr>
                                                                    <td class="reportRecords" valign="top">Total:</td>
                                                                    <td class="reportRecords" align="center" valign="top"><?php echo $cust_count; ?></td>
                                                                    <td class="reportRecords" align="center" valign="top"><?php echo $order_count; ?></td>
                                                                    <td class="reportRecords" align="right" valign="top" colspan="<?php echo ($report_obj->size + 2); ?>">&nbsp;</td>
                                                                </tr>
                                                                <?php
                                                            }
                                                            ?>
                                                        </table>
                                                        <?php
                                                        break;

                                                    case 9: # Customer
                                                        ?>
                                                        <table border="0" width="100%" cellspacing="1" cellpadding="2">
                                                            <tr>
                                                                <td class="reportBoxHeading" rowspan="2"><?php echo $report_obj->summary["first_column_title"]; ?></td>
                                                                <td width="10%" class="reportBoxHeading" align="center" rowspan="2"><?php echo $report_obj->summary["second_column_title"]; ?></td>
                                                                <td width="10%" class="reportBoxHeading" align="center" rowspan="2"><?php echo $report_obj->summary["third_column_title"]; ?></td>
                                                                <td width="10%" class="reportBoxHeading" align="right" rowspan="2"><?php echo $report_obj->summary["forth_column_title"]; ?></td>
                                                                <td class="reportBoxHeading" align="center" colspan="<?= ($report_obj->size * 2) ?>"><?= $report_obj->summary["sales_category_title"] ?></td>
                                                            </tr>
                                                            <tr>
                                                                <?php
                                                                for ($i = $report_obj->size - 1; $i >= 0; $i--) {
                                                                    ?>
                                                                    <td class="reportBoxHeading" align="center" width="10%" nowrap>
                                                                        <?php echo (($report_obj->sales_period == 'day') ? date('Y-m-d', $report_obj->startDates[$i]) : date("M, Y", $report_obj->startDates[$i])); ?>
                                                                    </td>
                                                                    <td class="reportBoxHeading" align="center">%</td>
                                                                    <?php
                                                                }
                                                                ?>
                                                            </tr>
                                                            <?
                                                            $row_count = 0;
                                                            $order_count = 0;
                                                            $cust_count = 0;
                                                            $sum = 0;

                                                            for ($i = 0; $i < count($report_obj->info); $i++) {
                                                                $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';
                                                                $order_count += $report_obj->info[$i]['count'];
                                                                $cust_count += $report_obj->info[$i]['total_customer'];
                                                                $sum += $report_obj->info[$i]['sum'];
                                                                ?>
                                                                <tr class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                                                    <td class="reportRecords" valign="top" nowrap><?= $report_obj->info[$i]['customers_groups_name'] ?></td>
                                                                    <td class="reportRecords" align="center" valign="top" nowrap><?= $report_obj->info[$i]["total_customer"] ?></td>
                                                                    <td class="reportRecords" align="center" valign="top" nowrap><?= $report_obj->info[$i]["count"] ?></td>
                                                                    <td class="reportRecords" align="right" valign="top" nowrap><?= $currencies->format($report_obj->info[$i]["avg"]) ?></td>			
                                                                    <?php
                                                                    for ($j = $report_obj->size - 1; $j >= 0; $j--) {
                                                                        $color_style = '';
                                                                        unset($next);

                                                                        $current = (isset($report_obj->info[$i]['subtotal'][$report_obj->startDates[$j]]) ? $report_obj->info[$i]['subtotal'][$report_obj->startDates[$j]] : 0);
                                                                        if (($j - 1) >= 0) {
                                                                            $next = (isset($report_obj->info[$i]['subtotal'][$report_obj->startDates[($j - 1)]]) ? $report_obj->info[$i]['subtotal'][$report_obj->startDates[($j - 1)]] : 0);
                                                                        }

                                                                        if (isset($next)) {
                                                                            if ($current < $next) {
                                                                                $color_style = 'redIndicator';
                                                                            } else if ($current > $next) {
                                                                                $color_style = 'greenIndicator';
                                                                            }
                                                                        }
                                                                        ?>
                                                                        <td class="reportRecords" align="right" valign="top" width="10%" nowrap>
                                                                            <span class="<?php echo $color_style; ?>"><?php echo $currencies->format($current); ?></span>
                                                                        </td>
                                                                        <td class="reportRecords" align="right" valign="top"><?= tep_not_null($report_obj->info[$i]['sales_percentage'][$report_obj->startDates[$j]]) ? $report_obj->info[$i]['sales_percentage'][$report_obj->startDates[$j]] : '0' ?></td>
                                                                        <?php
                                                                    }
                                                                    ?>						
                                                                </tr>
                                                                <?php
                                                                $row_count++;
                                                            }

                                                            if (count($report_obj->info)) {
                                                                ?>
                                                                <tr>
                                                                    <td class="reportRecords" valign="top">Total:</td>
                                                                    <td class="reportRecords" align="center" valign="top"><?php echo $cust_count; ?></td>
                                                                    <td class="reportRecords" align="center" valign="top"><?php echo $order_count; ?></td>
                                                                    <?php for ($i = $report_obj->size - 1; $i >= 0; $i--) { ?>
                                                                        <td class="reportRecords" align="right" valign="top" colspan="2"><?= $currencies->format($report_obj->subtotal[$report_obj->startDates[$i]]) ?></td>
                                                                    <?php } ?>
                                                                    <td class="reportRecords" align="right" valign="top">&nbsp;</td>
                                                                    <td class="reportRecords" align="right" valign="top" colspan="<?php echo ($report_obj->size + 2); ?>">&nbsp;</td>
                                                                </tr>
                                                                <?php
                                                            }
                                                            ?>
                                                        </table>
                                                        <?php
                                                        break;

                                                    case 10: # New Sign Up
                                                        ?>
                                                        <table border="0" width="100%" cellspacing="1" cellpadding="2">
                                                            <tr>
                                                                <td class="reportBoxHeading" rowspan="2"><?php echo $report_obj->summary["first_column_title"]; ?></td>
                                                                <td width="10%" class="reportBoxHeading" align="center" rowspan="2"><?php echo $report_obj->summary["second_column_title"]; ?></td>
                                                                <td width="10%" class="reportBoxHeading" align="center" rowspan="2"><?php echo $report_obj->summary["after_second_column_title"]; ?></td>
                                                                <td width="10%" class="reportBoxHeading" align="center" rowspan="2"><?php echo $report_obj->summary["third_column_title"]; ?></td>
                                                                <td class="reportBoxHeading" align="center" colspan="<?= ($report_obj->size * 5) ?>"><?= $report_obj->summary["sales_category_title"] ?></td>
                                                            </tr>
                                                            <tr>
                                                                <?php
                                                                for ($i = $report_obj->size - 1; $i >= 0; $i--) {
                                                                    ?>
                                                                    <td class="reportBoxHeading" align="center" nowrap>
                                                                        <?php echo (($report_obj->sales_period == 'day') ? date('Y-m-d', $report_obj->startDates[$i]) : date("M, Y", $report_obj->startDates[$i])); ?>
                                                                    </td>
                                                                    <td class="reportBoxHeading" align="center" nowrap><?php echo TABLE_HEADING_NEW_SIGN_UP_SUB_TOTAL; ?></td>
                                                                    <td class="reportBoxHeading" align="center" nowrap><?php echo TABLE_HEADING_NEW_SIGN_UP_SUB_PURCHASE; ?></td>
                                                                    <td class="reportBoxHeading" align="center" nowrap><?php echo '# of Orders'; ?></td>
                                                                    <td class="reportBoxHeading" align="center" nowrap><?php echo $report_obj->summary["forth_column_title"]; ?></td>
                                                                    <?php
                                                                }
                                                                ?>
                                                            </tr>
                                                            <?php
                                                            $row_count = 0;
                                                            $total_signup = 0;
                                                            $order_count = 0;
                                                            $cust_count = 0;
                                                            $sum = 0;

                                                            $row_style = ($row_count % 2) ? 'reportListingEven' : 'reportListingOdd';

                                                            foreach ($report_obj->info as $country_name => $country_sign_up_info) {
                                                                $total_signup += $country_sign_up_info['total_sign_up'];
                                                                $order_count += $country_sign_up_info['count'];
                                                                $cust_count += $country_sign_up_info['total_customer'];
                                                                $sum += $country_sign_up_info['sum'];
                                                                ?>
                                                                <tr class="<?= $row_style ?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?= $row_style ?>')" onclick="rowClicked(this, '<?= $row_style ?>')">
                                                                    <td class="reportRecords" valign="top" nowrap><?php echo $country_name; ?></td>
                                                                    <td class="reportRecords" align="center" valign="top" nowrap><?= (tep_not_null($country_sign_up_info["total_sign_up"]) ? $country_sign_up_info["total_sign_up"] : '0') ?></td>
                                                                    <td class="reportRecords" align="center" valign="top" nowrap><?= (tep_not_null($country_sign_up_info["total_customer"]) ? $country_sign_up_info["total_customer"] : '0') ?></td>
                                                                    <td class="reportRecords" align="center" valign="top" nowrap><?= (tep_not_null($country_sign_up_info["count"]) ? $country_sign_up_info["count"] : '0') ?></td>
                                                                    <?php
                                                                    for ($j = $report_obj->size - 1; $j >= 0; $j--) {
                                                                        $color_style = '';
                                                                        unset($next);

                                                                        $current = (isset($country_sign_up_info['subtotal'][$report_obj->startDates[$j]]) ? $country_sign_up_info['subtotal'][$report_obj->startDates[$j]] : 0);
                                                                        if (($j - 1) >= 0) {
                                                                            $next = (isset($country_sign_up_info['subtotal'][$report_obj->startDates[($j - 1)]]) ? $country_sign_up_info['subtotal'][$report_obj->startDates[($j - 1)]] : 0);
                                                                        }

                                                                        if (isset($next)) {
                                                                            if ($current < $next) {
                                                                                $color_style = 'redIndicator';
                                                                            } else if ($current > $next) {
                                                                                $color_style = 'greenIndicator';
                                                                            }
                                                                        }
                                                                        ?>
                                                                        <td class="reportRecords" align="right" valign="top" width="10%" nowrap>
                                                                            <span class="<?php echo $color_style; ?>"><?php echo $currencies->format($current); ?></span>
                                                                        </td>
                                                                        <td class="reportRecords" align="right" valign="top" nowrap><?php echo (isset($country_sign_up_info['new_sign_up'][$report_obj->startDates[$j]]) ? $country_sign_up_info['new_sign_up'][$report_obj->startDates[$j]] : 0); ?></td>
                                                                        <td class="reportRecords" align="right" valign="top" nowrap><?php echo (isset($country_sign_up_info['with_purchase'][$report_obj->startDates[$j]]) ? $country_sign_up_info['with_purchase'][$report_obj->startDates[$j]] : 0); ?></td>
                                                                        <td class="reportRecords" align="right" valign="top" nowrap><?php echo (isset($country_sign_up_info['count_by_date'][$report_obj->startDates[$j]]) ? $country_sign_up_info['count_by_date'][$report_obj->startDates[$j]] : 0); ?></td>
                                                                        <td class="reportRecords" align="right" valign="top" nowrap><?php echo (isset($country_sign_up_info['count_by_date'][$report_obj->startDates[$j]]) && $country_sign_up_info['count_by_date'][$report_obj->startDates[$j]] > 0 ? $currencies->format($current / $country_sign_up_info['count_by_date'][$report_obj->startDates[$j]]) : $currencies->format(0)); ?></td>
                                                                        <?php
                                                                    }
                                                                    ?>
                                                                </tr>
                                                                <?php
                                                            }

                                                            if (count($report_obj->info)) {
                                                                ?>
                                                                <tr>
                                                                    <td class="reportRecords" valign="top">Total:</td>
                                                                    <td class="reportRecords" align="center" valign="top"><?php echo $total_signup; ?></td>
                                                                    <td class="reportRecords" align="center" valign="top"><?php echo $cust_count; ?></td>
                                                                    <td class="reportRecords" align="center" valign="top"><?php echo $order_count; ?></td>
                                                                    <td class="reportRecords" align="right" valign="top" colspan="<?php echo (($report_obj->size * 3) + 1); ?>">&nbsp;</td>
                                                                </tr>
                                                                <?php
                                                            }
                                                            ?>
                                                        </table>
                                                        <?php
                                                        break;
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                            </tr>
                            <tr>
                                <td>
                                    <table border="0" width="<?= ($report_obj->mode != '6') ? "100%" : "80%" ?>" cellspacing="0" cellpadding="1">
                                        <? if ($order_count) { ?>
                                            <tr class="dataTableRow">
                                                <td class="reportRecords" width="100%" align="right"><?= '<b>' . ENTRY_AVERAGE_ORDER . ' </b>' ?></td>
                                                <td>&nbsp;</td>
                                                <td class="reportRecords" align="right" nowrap><?= $currencies->format($sum / $order_count) ?></td>
                                            </tr>
                                            <?
                                        }

                                        if ($report_obj->size != 0) {
                                            ?>
                                            <tr class="dataTableRow">
                                                <td class="reportRecords" width="100%" align="right"><b><?= $report_obj->summary["mode_summary"] ?></b></td>
                                                <td>&nbsp;</td>
                                                <td class="reportRecords" align="right" nowrap><?= $currencies->format($sum / $report_obj->size) ?></td>
                                            </tr>
                                        <? } ?>
                                        <tr class="dataTableRow">
                                            <td class="reportRecords" width="100%" align="right"><b><?= $report_obj->summary["parent_mode_summary"] ?></b></td>
                                            <td>&nbsp;</td>
                                            <td class="reportRecords" align="right" nowrap><?= $currencies->format($sum) ?></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <?
                                    //define variables for graph
                                    if ($report_obj->mode > 1 && $report_obj->mode <= 5 && $report_obj->size > 0) {
                                        $scale_x = ($sum / $report_obj->size) / 4;
                                        $scale_y = $scale_x + 50;
                                        $scale_z = $scale_y / 100;
                                        $scale = round($scale_z) * 100;
                                        ?>
                                        <SCRIPT LANGUAGE="JavaScript1.2">
        <!--
                                                var g = new Graph(<?php
                                if ($report_obj->size > 2) {
                                    echo '200';
                                } else {
                                    echo ($report_obj->size * 50);
                                }
                                ?>, 100, true);
        g.addRow(<?php
                                for ($i = 0; $i < $report_obj->size; $i++) {
                                    if ($report_obj->info[$i]['sum'] == "") {
                                        echo '0';
                                    } else {
                                        echo $report_obj->info[$i]['sum'] - $report_obj->info[$i]['avg'];
                                    }
                                    if (($i + 1) < $report_obj->size) {
                                        echo ',';
                                    }
                                }
                                echo ');';
                                echo '
                                        ';
                                ?>
        <?
        if ($report_obj->mode == 2) {
            echo 'g.addRow(';
            for ($i = 0; $i < $report_obj->size; $i++) {
                if ($report_obj->info[$i]['sum'] == "") {
                    echo '0';
                } else {
                    echo $report_obj->info[$i]['avg'];
                }
                if (($i + 1) < $report_obj->size) {
                    echo ',';
                }
            }
            echo ');';
            echo '
                                                        ';
            echo 'g.setLegend("daily total","avg. order");';
            echo '
                                                        ';
        }
        ?>
        <?
        echo 'g.setXScaleValues("';
        for ($i = 0; $i < $report_obj->size; $i++) {
            if ($report_obj->mode == 5) {
                echo $report_obj->info[$i]['text'];
            } else if ($report_obj->mode == 4) {
                echo substr($report_obj->info[$i]['text'] . $date_text[$i], 0, 3);
            } else if ($report_obj->mode == 3) {
                echo substr($report_obj->info[$i]['text'], 0, 10);
            } else if ($report_obj->mode == 2) {
                echo substr($report_obj->info[$i]['text'], 8, 2);
            }

            if (($i + 1) < $report_obj->size) {
                echo '","';
            }
        }
        echo '");';
        ?>
        g.scale = <?php echo $scale; ?>;
        g.build();
        //-->
                                        </SCRIPT>
                                    <? } ?>
                                </td>
                            </tr>
                            <?php
                            unset($report_obj->info);
                        } else { /* -- Sales Report Criteria Form -- */
                            $report_obj = new sales_report_new("");
                            $report_obj->clean_up_csv(60, "csv"); // 1st param is minutes of the file to delete; 2nd param is file type to delete;
                            $categories_array = [];

                            $report_type_array = array(array('id' => '1', "text" => REPORT_TYPE_HOURLY),
                                array('id' => '2', "text" => REPORT_TYPE_DAILY),
                                array('id' => '3', "text" => REPORT_TYPE_WEEKLY),
                                array('id' => '4', "text" => REPORT_TYPE_MONTHLY),
                                array('id' => '6', "text" => REPORT_TYPE_CATEGORY),
                                array('id' => '7', "text" => REPORT_TYPE_CATEGORY_WITH_STOCK),
                                array('id' => '8', "text" => REPORT_TYPE_COUNTRY),
                                array('id' => '9', "text" => REPORT_TYPE_CUSTOMER),
                                array('id' => '10', "text" => REPORT_TYPE_NEW_SIGN_UP)
                            );

                            $sales_period_array = array(REPORT_TYPE_CATEGORY_WITH_STOCK => array(array('id' => 'day', "text" => TEXT_SALES_BY_DAY),
                                    array('id' => 'month', "text" => TEXT_SALES_BY_MONTH)
                                )
                            );

                            $sort_by_array = array('country' => array(array('id' => 'sum', "text" => TEXT_SORT_TOTAL_SALES),
                                    array('id' => 'country_name', "text" => TEXT_SORT_COUNTRY),
                                    array('id' => 'total_customer', "text" => TEXT_SORT_CUSTOMER_COUNT),
                                    array('id' => 'count', "text" => TEXT_SORT_ORDER_COUNT),
                                    array('id' => 'avg', "text" => TEXT_SORT_AVG_SALES)
                                ),
                                'customer' => array(array('id' => 'sort_order', "text" => TEXT_SORT_CUSTOMER_GROUP),
                                    array('id' => 'total_customer', "text" => TEXT_SORT_CUSTOMER_COUNT),
                                    array('id' => 'count', "text" => TEXT_SORT_ORDER_COUNT),
                                    array('id' => 'avg', "text" => TEXT_SORT_AVG_SALES)
                                )
                            );
                            
                            $order_site_array = array(
                                    array('id' => '1', 'text' => 'OffGamers'),
                                    array('id' => '5', 'text' => 'G2G')
                            );

                            /* -- order status : data retrieve -- */
                            $status_options = array();
                            $date_type_array = array(array('id' => '1', 'text' => TEXT_DATE_TYPE_PENDING_DATE));

                            $cache_key = TABLE_ORDERS_STATUS . '/language/1/order_status_list';
                            $cache_result = $memcache_obj->fetch($cache_key);
                            if ($cache_result !== FALSE) {
                                $status_options = $cache_result;
                            } else {
                                $order_status_select_sql = "SELECT orders_status_id, orders_status_name FROM " . TABLE_ORDERS_STATUS . " WHERE language_id='" . $languages_id . "' ORDER BY orders_status_sort_order";
                                $order_status_result_sql = tep_db_query($order_status_select_sql, 'read_db_link');
                                while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
                                    if ($order_status_row["orders_status_id"] == 1) {
                                        continue;
                                    }

                                    $status_options[$order_status_row["orders_status_id"]] = $order_status_row["orders_status_name"];
                                }
                                $memcache_obj->store($cache_key, $status_options, 86400);
                            }

                            $order_status_display_str = '<table border="0" cellspacing="0" cellpadding="2">
			                        <tr>';

                            foreach ($status_options as $id => $title) {
                                $date_type_array[] = array(
                                    'id' => $id,
                                    'text' => sprintf(TEXT_DATE_TYPE_FIRST_STATUS_DATE, $title)
                                );

                                $status_selected = isset($_SESSION['sales_param']) ? (is_array($_SESSION['sales_param']["order_status"]) && in_array($id, $_SESSION['sales_param']["order_status"]) ? true : false) : ($id == "7" || $id == "2" || $id == "3" ? true : false);

                                $order_status_display_str .= '<td class="main" valign="top">' .
                                        tep_draw_checkbox_field('order_status[]', $id, $status_selected, '', 'id="order_status_' . $id . '" onClick="verify_status_selection(this);"') . '
									</td>
									<td class="main" valign="top">';

                                if ($id == "2" || $id == "3") {
                                    $order_status_display_str .= '<fieldset class="selectedFieldSet">
											<legend align=left class=SectionHead>' . $title . '</legend>
											<table border="0" cellspacing="0" cellpadding="0">';

                                    if (isset($report_obj->orders_amt_breakdown_array[$id]) && count($report_obj->orders_amt_breakdown_array[$id])) {
                                        foreach ($report_obj->orders_amt_breakdown_array[$id] as $sub_status_id => $sub_status_title) {
                                            $sub_status_selected = isset($_SESSION['sales_param']) ? (is_array($_SESSION['sales_param']['order_sub_status_' . $id]) && in_array($sub_status_id, $_SESSION['sales_param']['order_sub_status_' . $id]) ? true : false) : ($sub_status_id == 'UD' || $sub_status_id == 'D' || $sub_status_id == 'RV-WIN' || $sub_status_id == 'RV-RESOLVED' ? true : false);
                                            $order_status_display_str .= '<tr><td class="smallText" valign="top">' . tep_draw_checkbox_field('order_sub_status_' . $id . '[]', $sub_status_id, $sub_status_selected, '', $status_selected ? '' : 'disabled') . '</td><td class="smallText">' . $sub_status_title . '</td></tr>';
                                        }
                                    }

                                    $order_status_display_str .= '	</table>
										</fieldset>';
                                } else {
                                    $order_status_display_str .= $title;
                                }

                                $order_status_display_str .= '</td>';
                            }

                            $order_status_display_str .= '	</tr>
    								</table>';

                            /* -- product type -- */
                            $custom_pro_id_array = array_merge(array(array("id" => '', "text" => "All", "param" => '')), tep_get_product_type()); //Force to use 999, if empty value will match with game_curreny's value;
                            ?>
                            <tr>
                                <td>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td class="pageHeading" valign="top"><?= HEADING_INPUT_TITLE ?></td>
                                            <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <?= tep_draw_form('sales_report_criteria', FILENAME_SALES_REPORT, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', 'onSubmit="return form_checking();"'); ?>
                                    <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td>
                                                <table border="0" width="100%" cellspacing="2" cellpadding="2">
                                                    <tr>
                                                        <td class="main" width="15%"><?= ENTRY_REPORT_TYPE ?></td>
                                                        <td class="main"><?= tep_draw_pull_down_menu("report", $report_type_array, $_SESSION['sales_param']["report"], ' id="report" onChange="swap_fields(this.value)"') ?></td>
                                                    </tr>
                                                    <tbody id="date_type_tbody" class="hide">
                                                        <tr>
                                                            <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                        </tr>
                                                        <tr>
                                                            <td class="main" width="15%"><?= ENTRY_DATE_TYPE ?></td>
                                                            <td class="main"><?= tep_draw_pull_down_menu('date_type', $date_type_array, $_SESSION['sales_param']['date_type'], ' id="date_type"') ?></td>
                                                        </tr>
                                                    </tbody>
                                                    <tbody id="custom_section_tbody" class="show">
                                                        <tr>
                                                            <td class="main"></td>
                                                            <td class="dataTableRow">
                                                                <div id="pro_type_div" class="hide_row">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td width="20%" class="main"><?= ENTRY_PRODUCT_TYPE ?></td>
                                                                            <td class="main"><?= tep_draw_pull_down_menu("pro_type", $custom_pro_id_array, $_SESSION['sales_param']["pro_type"], ' id="pro_type" onChange="refreshDynamicSelectOptions(this, \'cat_id\', \'' . (int) $languages_id . '\', \'custom_product\', false, \'' . FILENAME_SALES_REPORT . '\', \'' . $login_id . '\');"') ?></td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                                <div id="cat_div" class="hide_row">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td width="20%" class="main"><?= ENTRY_CATEGORY ?></td>
                                                                            <td class="main"><?= tep_draw_pull_down_menu("cat_id", $categories_array, $_SESSION['sales_param']["cat_id"], ' id="cat_id" ') ?></td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                                <div id="cat_level_div" class="hide_row">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td width="20%" class="main"><?= TEXT_CATEGORY_LEVEL ?></td>
                                                                            <td class="main"><?= tep_draw_input_field('cat_level', tep_not_null($_SESSION['sales_param']["cat_level"]) ? $_SESSION['sales_param']["cat_level"] : '1', ' id="cat_level" SIZE="5" MAXLENGTH="3"') ?></td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                                <div id="prod_qty_type_div" class="hide_row">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td width="20%" class="main"><?= ENTRY_PRODUCTS_QUANTITY ?></td>
                                                                            <td class="main"><?= tep_draw_checkbox_field('products_qty_type[]', 'available_qty', isset($_SESSION['sales_param']) ? (is_array($_SESSION['sales_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['sales_param']["products_qty_type"]) ? true : false) : true, '', 'id="available_qty_box"') . "&nbsp;" . TEXT_AVAILABLE_QTY . "&nbsp;" . tep_draw_checkbox_field('products_qty_type[]', 'actual_qty', isset($_SESSION['sales_param']) ? (is_array($_SESSION['sales_param']["products_qty_type"]) && in_array("actual_qty", $_SESSION['sales_param']["products_qty_type"]) ? true : false) : false, '', 'id="actual_qty_box"') . "&nbsp;" . TEXT_ACTUAL_QTY ?></td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                                <div id="g2g_region_type_div" class="hide_row">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td width="20%" class="main">Show G2G Region Breakdown</td>
                                                                            <td class="main"><?= tep_draw_checkbox_field('g2g_region_layer[]', 'g2g_region_layer', isset($_SESSION['sales_param']) ? (is_array($_SESSION['sales_param']["g2g_region_layer"]) && in_array("g2g_region_layer", $_SESSION['sales_param']["g2g_region_layer"]) ? true : false) : false, '', 'id="g2g_region_layer"'); ?></td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                                <div id="sort_country_div" class="hide_row">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td width="20%" class="main"><?= ENTRY_SORT ?></td>
                                                                            <td class="main">
                                                                                <?= tep_draw_pull_down_menu("sort_country_by", $sort_by_array['country'], $_SESSION['sales_param']["sort_by"], '') ?>
                                                                                <?= tep_draw_radio_field('sort_order', 'ASC', isset($_SESSION['sales_param']) ? ($_SESSION['sales_param']["sort_order"] == 'ASC' ? "checked" : '') : '') . "&nbsp;" . TEXT_ASC . "&nbsp;" . tep_draw_radio_field('sort_order', 'DESC', isset($_SESSION['sales_param']) ? ($_SESSION['sales_param']["sort_order"] == 'DESC' ? "checked" : '') : "checked") . "&nbsp;" . TEXT_DESC ?>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                                <div id="sort_customer_div" class="hide_row">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td colspan="2" class="main"><?= TEXT_CUSTOMER_DEFINITION; ?></td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td width="20%" class="main"><?= ENTRY_SORT ?></td>
                                                                            <td class="main">
                                                                                <?= tep_draw_pull_down_menu("sort_customer_by", $sort_by_array['customer'], $_SESSION['sales_param']["sort_by"], '') ?>
                                                                                <?= tep_draw_radio_field('sort_order', 'ASC', isset($_SESSION['sales_param']) ? ($_SESSION['sales_param']["sort_order"] == 'ASC' ? "checked" : '') : '') . "&nbsp;" . TEXT_ASC . "&nbsp;" . tep_draw_radio_field('sort_order', 'DESC', isset($_SESSION['sales_param']) ? ($_SESSION['sales_param']["sort_order"] == 'DESC' ? "checked" : '') : "checked") . "&nbsp;" . TEXT_DESC ?>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                                <div id="sales_period_div" class="hide_row">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td width="20%" class="main"><?= ENTRY_SALES_PERIOD ?></td>
                                                                            <td class="main">							
                                                                                <?= tep_draw_pull_down_menu("sales_period", $sales_period_array[REPORT_TYPE_CATEGORY_WITH_STOCK], isset($_SESSION['sales_param']["sales_period"]) ? $_SESSION['sales_param']["sales_period"] : 'month', '') ?>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                                <div id="order_site_div" class="hide_row">
                                                                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                                        <tr>
                                                                            <td width="20%" class="main"><?= ENTRY_ORDER_SITE ?></td>
                                                                            <td class="main">							
                                                                                <?= tep_draw_pull_down_menu("order_site", $order_site_array, isset($_SESSION['sales_param']["order_site"]) ? $_SESSION['sales_param']["order_site"] : 1, '') ?>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                    <tr>
                                                        <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                    </tr>

                                                    <!-- calendar -->
                                                    <script language="javascript"><!--
                                                            var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "sales_report_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
                                                        calMgr.showHelpAlerts = true;
                                                        //--></script>

                                                    <tr>
                                                        <td class="main" width="12%">Start Date<br><small>(YYYY-MM-DD)</small></td>
                                                        <td class="main" align="laft"><script language="javascript">date_start_date.writeControl();
                                                            date_start_date.dateFormat = "yyyy-MM-dd";
                                                            document.getElementById('start_date').value = '<?= $_SESSION['sales_param']["start_date"] ?>';</script>&nbsp;(2005-02-01)</td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2"><img src="images/pixel_trans.gif" border="0" alt="" width="1" height="10"></td>
                                                    </tr>

                                                    <!-- calendar -->      
                                                    <script language="javascript"><!--
                                                                var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "sales_report_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
                                                        //--></script>	

                                                    <tr>
                                                        <td class="main" width="12%">End Date<br><small>(YYYY-MM-DD)</small></td>
                                                        <td class="main" align="laft"><script language="javascript">date_end_date.writeControl();
                                                            date_end_date.dateFormat = "yyyy-MM-dd";
                                                            document.getElementById('end_date').value = '<?= $_SESSION['sales_param']["end_date"] ?>';</script></td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="main" valign="top"><?= ENTRY_ORDER_STATUS ?></td>
                                                        <td class="main"><?= $order_status_display_str ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2"><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td valign="top" class="main"><?= ENTRY_PAYMENT_METHOD ?></td>
                                                        <td class="main">
                                                            <?
                                                            /* -- payment method -- */
                                                            $payment_methods_array = array();
                                                            $payment_gateways_array = array();

                                                            $cache_key = TABLE_PAYMENT_METHODS . '/payment_methods_receive_status/1/payment_method_list';
                                                            $cache_result = $memcache_obj->fetch($cache_key);
                                                            if ($cache_result !== FALSE) {
                                                                $payment_methods_array = $cache_result["payment_method"];
                                                                $payment_gateways_array = $cache_result["payment_gateway"];
                                                            } else {
                                                                $payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_title, payment_methods_parent_id 
																FROM " . TABLE_PAYMENT_METHODS . "
																WHERE payment_methods_receive_status = '1' 
																ORDER BY payment_methods_sort_order";
                                                                $payment_methods_result_sql = tep_db_query($payment_methods_select_sql,
                                                                    'read_db_link');

                                                                $payment_gateways_array[SYSTEM_PAYMENT_STORE_CREDITS] = 'Store Credits';

                                                                while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
                                                                    if ($payment_methods_row['payment_methods_parent_id'] > 0) {
                                                                        $payment_methods_array[$payment_methods_row['payment_methods_parent_id']][$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
                                                                    } else {
                                                                        $payment_gateways_array[$payment_methods_row['payment_methods_id']] = $payment_methods_row['payment_methods_title'];
                                                                    }
                                                                }
                                                                $memcache_obj->store($cache_key, array("payment_method" => $payment_methods_array, "payment_gateway" => $payment_gateways_array), 600);
                                                            }
                                                            ?>
                                                            <ul class="myTree">
                                                                <li style="-moz-user-select: none;" class="treeItem" id="<?= $payment_gateways_id ?>">
                                                                    <span class="textHolder payment_gateways">
                                                                        &nbsp;&nbsp;&nbsp;
                                                                        <input type="checkbox" class="pg_any" name="payment_gateways_id[]" value="any" <?= ((!(isset($_SESSION['sales_param']['payment_gateways_id']) && count($_SESSION['sales_param']['payment_gateways_id'])) || (isset($_SESSION['sales_param']['payment_methods_id']) && count($_SESSION['sales_param']['payment_methods_id']))) || (isset($_SESSION['sales_param']['payment_gateways_id']) && count($_SESSION['sales_param']['payment_gateways_id']) && in_array('any', $_SESSION['sales_param']['payment_gateways_id'])) ? ' checked ' : '') ?>>
                                                                        <i>Any</i>
                                                                    </span>
                                                                </li>
                                                                <?
                                                                foreach ($payment_gateways_array as $payment_gateways_id => $payment_gateways_title) {
                                                                    ?>
                                                                    <li style="-moz-user-select: none;" class="treeItem" id="<?= $payment_gateways_id ?>">
                                                                        <?
                                                                        if (isset($payment_methods_array[$payment_gateways_id]) && count($payment_methods_array[$payment_gateways_id])) {
                                                                            $pm_checked_flag = false;

                                                                            if (isset($_SESSION['sales_param']['payment_methods_id']) && count($_SESSION['sales_param']['payment_methods_id'])) {
                                                                                foreach ($payment_methods_array[$payment_gateways_id] as $check_pm_id => $check_pm_text) {
                                                                                    if (in_array($check_pm_id, $_SESSION['sales_param']['payment_methods_id'])) {
                                                                                        $pm_checked_flag = true;
                                                                                        break;
                                                                                    }
                                                                                }
                                                                            }

                                                                            if ($pm_checked_flag) {
                                                                                ?>
                                                                                <img src="images/icon-collapse-small.gif" class="expandImage" width="9" height="7">
                                                                                <span class="textHolder payment_gateways">
                                                                                    <input type="checkbox" name="payment_gateways_id[]" value="<?= $payment_gateways_id ?>" <?= (isset($_SESSION['sales_param']['payment_gateways_id']) && count($_SESSION['sales_param']['payment_gateways_id']) && in_array($payment_gateways_id, $_SESSION['sales_param']['payment_gateways_id']) ? ' checked ' : '') ?>>
                                                                                    <?= $payment_gateways_title ?>
                                                                                </span>
                                                                                <ul style="display: display;" class="open">
                                                                                    <?
                                                                                } else {
                                                                                    ?>
                                                                                    <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                                                                    <span class="textHolder payment_gateways">
                                                                                        <input type="checkbox" name="payment_gateways_id[]" value="<?= $payment_gateways_id ?>" <?= (isset($_SESSION['sales_param']['payment_gateways_id']) && count($_SESSION['sales_param']['payment_gateways_id']) && in_array($payment_gateways_id, $_SESSION['sales_param']['payment_gateways_id']) ? ' checked ' : '') ?>>
                                                                                        <?= $payment_gateways_title ?>
                                                                                    </span>
                                                                                    <ul style="display: none;">
                                                                                        <?
                                                                                    }
                                                                                    
                                                                                    $pg_pm_id = implode(',', array_keys($payment_methods_array[$payment_gateways_id]));
                                                                                    foreach ($payment_methods_array[$payment_gateways_id] as $payment_methods_id => $payment_methods_title) {
                                                                                        ?>
                                                                                        <li style="-moz-user-select: none;" class="treeItem" id="<?= $payment_methods_id ?>">&nbsp;&nbsp;
                                                                                            <span class="textHolder payment_methods">
                                                                                                <input type="checkbox" name="payment_methods_id[]" value="<?= $payment_methods_id ?>" <?= (isset($_SESSION['sales_param']['payment_methods_id']) && count($_SESSION['sales_param']['payment_methods_id']) && in_array($payment_methods_id, $_SESSION['sales_param']['payment_methods_id']) ? ' checked ' : '') ?>>
                                                                                                <?= $payment_methods_title ?>
                                                                                            </span>
                                                                                        </li>
                                                                                        <script type="text/javascript">
                                                                                            jQuery("li#<?= $payment_methods_id ?> span.payment_methods input[type='checkbox']").click(function () {
                                                                                                var count_unchecked = 0;
                                                                                                const pg_pm_id = [<?=$pg_pm_id?>];
                                                                                                
                                                                                                pg_pm_id.forEach(element => {
                                                                                                    if (jQuery("li#<?= $payment_gateways_id ?> li#"+element+" span.payment_methods input[type='checkbox']").attr("checked") === false) {
                                                                                                        count_unchecked++;
                                                                                                    }
                                                                                                });
                                                                                                
                                                                                                if (count_unchecked == 0) {
                                                                                                    jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", true);
                                                                                                } else {
                                                                                                    jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked", '');
                                                                                                }
                                                                                            });
                                                                                        </script>
                                                                                        <?
                                                                                    }
                                                                                    ?>
                                                                                </ul>
                                                                                <?
                                                                            } else {
                                                                                ?>
                                                                                <img src="images/icon-expand-small.gif" class="expandImage" width="9" height="7">
                                                                                <span class="textHolder payment_gateways">
                                                                                    <input type="checkbox" name="payment_gateways_id[]" value="<?= $payment_gateways_id ?>" <?= (isset($_SESSION['sales_param']['payment_gateways_id']) && count($_SESSION['sales_param']['payment_gateways_id']) && in_array($payment_gateways_id, $_SESSION['sales_param']['payment_gateways_id']) ? ' checked ' : '') ?>>
                                                                                    <?= $payment_gateways_title ?>
                                                                                </span>
                                                                                <?
                                                                            }
                                                                            ?>
                                                                    </li>

                                                                    <script>
                                                                        jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").click(function() {
                                                                            if (jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked")) {
                                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", true);
                                                                            } else {
                                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", '');
                                                                            }
                                                                        });

                                                                        jQuery(document).ready(function() {
                                                                            if (jQuery("li#<?= $payment_gateways_id ?> span.payment_gateways input[type='checkbox']").attr("checked")) {
                                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", true);
                                                                            } else {
                                                                                jQuery("li#<?= $payment_gateways_id ?> span.payment_methods input[type='checkbox']").attr("checked", '');
                                                                            }
                                                                        });
                                                                    </script>
                                                                    <?
                                                                }
                                                                ?>	
                                                            </ul>

                                                            <script>
                                                                jQuery(document).ready(function() {
                                                                    tree = jQuery('.myTree');
                                                                    jQuery('img.expandImage', tree.get(0)).click(
                                                                            function() {
                                                                                if (this.src.indexOf('spacer') == -1) {
                                                                                    subbranch = jQuery('ul', this.parentNode).eq(0);

                                                                                    if (subbranch.css('display') == 'none') {
                                                                                        subbranch.show();
                                                                                        this.src = 'images/icon-collapse-small.gif';
                                                                                    } else {
                                                                                        subbranch.hide();
                                                                                        this.src = 'images/icon-expand-small.gif';
                                                                                    }
                                                                                }
                                                                            }
                                                                    );
                                                                });

                                                                jQuery('.myTree li input').click(function() {
                                                                    if (jQuery(this).hasClass('pg_any')) {
                                                                        jQuery('.myTree li input:not(.pg_any)').attr('checked', false);
                                                                        jQuery('.myTree li .pg_any').attr('checked', true);
                                                                    } else {
                                                                        if (jQuery('.myTree li input:not(.pg_any):checked').length > 0) {
                                                                            jQuery('.myTree li .pg_any').attr('checked', false);
                                                                        } else {
                                                                            jQuery('.myTree li .pg_any').attr('checked', true);
                                                                        }
                                                                    }
                                                                });
                                                            </script>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2"></td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        <td width="70%">&nbsp;</td>
                                                        <td align="right" valign="top">
                                                            <input type="hidden" name="hidden_export_format" id="hidden_export_format" />
                                                            <input type="hidden" name="hidden_export_report" id="hidden_export_report" />
                                                            <input type="submit" name="export_report" value="Export Report" class="inputButton" onClick="return export_report_type_validation(this);">&nbsp;
                                                            <input type="submit" name="Report" value="Report" class="inputButton" onClick="javascript: document.getElementById('hidden_export_format').value = '';
                                                                        document.getElementById('hidden_export_report').value = '';">&nbsp;
                                                            <input type="button" name="reset" value="Reset" class="inputButton" onClick="document.location.href = '<?php echo tep_href_link(FILENAME_SALES_REPORT, 'action=reset_session'); ?>'">&nbsp;
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                    </form>
                                </td>
                            </tr>

                            <script language="javascript"><!--
                                        // Export Report
                                function export_report_type_validation(bthObj) {
                                    var report_type = document.getElementById('report').value;
                                    document.getElementById('hidden_export_format').value = '';

                                    if ((report_type != 2) && (report_type != 7) && (report_type != 8) && (report_type != 9) && (report_type != 10)) {
                                        alert('Export Report feature is not ready for this Report Type');
                                        return false;
                                    } else {
                                        document.getElementById('hidden_export_report').value = 'export_report';

                                        if (report_type == 2) { // Daily Report
                                            var box_content = '';
                                            box_content += '<br />Please select Daily Sales Report export format: <br />';
                                            box_content += '<table border="0"><tr><td align="left" class="jwindow_popup_content_blue">';
                                            box_content += '<input type="radio" name="pop_export_format" id="pop_export_format_order" value="export_by_order" onClick="javascript: document.getElementById(\'hidden_export_format\').value=this.value;" />&nbsp;<label for="pop_export_format_order">By Order</label><br />';
                                            box_content += '<input type="radio" name="pop_export_format" id="pop_export_format_pg" value="export_by_payment_gateway" onClick="javascript: document.getElementById(\'hidden_export_format\').value=this.value;" checked />&nbsp;<label for="pop_export_format_pg">By Payment Gateway</label>';
                                            box_content += '</td></tr></table>';

                                            jquery_confirm_box(box_content, 2, 0, '');
                                            jQuery('#jconfirm_submit').click(function() {
                                                if (form_checking() == true) {
                                                    bthObj.form.submit();
                                                }
                                            });

                                            jQuery('#jconfirm_cancel').click(function() {
                                                document.getElementById('hidden_export_format').value = '';
                                                document.getElementById('hidden_export_report').value = '';
                                                return false;
                                            });
                                        }

                                        return true;
                                    }
                                }

                                // form input verification
                                function form_checking() {
                                    var order_status_array = new Array();
                                    var REPORT_MAX_DATE_RANGE = <?= REPORT_MAX_DATE_RANGE ?>
                                    //var payment_method_array = new Array();
    <?
    foreach ($status_options as $key => $name) {
        ?>
                                        order_status_array.push("order_status_<?= $key ?>");
        <?
    }
    ?>
                                    var start_date = document.getElementById('start_date').value;
                                    if (start_date.length > 0) {
                                        if (!validateDate(start_date)) {
                                            alert('Start date is not a valid date format as requested!');
                                            document.getElementById('start_date').focus();
                                            document.getElementById('start_date').select();
                                            return false;
                                        }
                                    }

                                    var end_date = document.getElementById('end_date').value;
                                    if (end_date.length > 0) {
                                        if (!validateDate(end_date)) {
                                            alert('End date is not a valid date format as requested!');
                                            document.getElementById('end_date').focus();
                                            document.getElementById('end_date').select();
                                            return false;
                                        }
                                    }

                                    if (start_date.length > 0 && end_date.length > 0) {
                                        if (!validStartAndEndDate(start_date, end_date)) {
                                            alert('Start Date is greater than End Date!');
                                            document.getElementById('start_date').focus();
                                            document.getElementById('start_date').select();
                                            return false;
                                        }
                                    }

                                    var report_type = document.getElementById('report').value;
                                    var date_type = document.getElementById('date_type').value;
                                    if (report_type == '2' && (end_date.length == 0 || !diffStartAndEndDate(start_date, end_date, REPORT_MAX_DATE_RANGE))) {
                                        alert('Maximum date range for current Report Type is ' + REPORT_MAX_DATE_RANGE + ' days only!');
                                        return false;
                                    }

                                    var selected = false;
                                    for (var i = 0; i < order_status_array.length; i++) {
                                        if (document.getElementById(order_status_array[i]).checked) {
                                            selected = true;
                                            break;
                                        }
                                    }

                                    if (!selected) {
                                        alert('Please select at least one order status!');
                                        return false;
                                    }

                                    if (!selected) {
                                        alert('Please select at least one payment method!');
                                        return false;
                                    }

                                    if (document.getElementById('report').value == '6' || document.getElementById('report').value == '7') {
                                        if (!validateInteger(document.getElementById('cat_level').value)) {
                                            alert('Category level must be an integer value!');
                                            document.getElementById('cat_level').focus();
                                            document.getElementById('cat_level').select();
                                            return false;
                                        }
                                    } else if (document.getElementById('report').value == '8') {
                                        ;
                                    } else {
                                        var period_text = document.getElementById('report').options[document.getElementById('report').selectedIndex].text;
                                        if (start_date.length <= 0) {
                                            alert('Start date is needed for ' + period_text + ' Report!');
                                            document.getElementById('start_date').focus();
                                            document.getElementById('start_date').select();
                                            return false;
                                        } else if (end_date.length <= 0) {
                                            if (document.getElementById('report').value == '1') {
                                                // Hourly report does not requires end date. Start date will do the favour.
                                            } else {
                                                alert('End date is needed for ' + period_text + ' Report!');
                                                document.getElementById('end_date').focus();
                                                document.getElementById('end_date').select();
                                                return false;
                                            }
                                        }

                                        if (document.getElementById('report').value == '3') {
                                            var s = start_date.split('-');
                                            var e = end_date.split('-');
                                            var startDateObj = new Date(s[0], s[1] - 1, s[2]);
                                            var endDateObj = new Date(e[0], e[1] - 1, e[2]);

                                            diff = endDateObj.getTime( ) - startDateObj.getTime( );
                                            diff = Math.floor(diff / (1000 * 60 * 60 * 24));

                                            if (diff < 6) {
                                                var foundMonday = false;
                                                var startDay = startDateObj.getDay();
                                                var endDay = endDateObj.getDay();

                                                if (endDay >= startDay) {
                                                    if (startDay <= 1 && endDay >= 1) {
                                                        foundMonday = true;	// monday is included
                                                    }
                                                } else {
                                                    if (startDay <= 1 || endDay >= 1) {
                                                        foundMonday = true;	// monday is included
                                                    }
                                                }

                                                if (!foundMonday) {
                                                    alert('The date range should contains at least one Monday for ' + period_text + ' Report!');
                                                    document.getElementById('start_date').focus();
                                                    document.getElementById('start_date').select();
                                                    return false;
                                                }
                                            }
                                        }
                                    }

                                    return true;
                                }

                                // hiding / display input option
                                function swap_fields(selected) {
                                    DOMCall('sort_country_div').className = 'hide_row';
                                    DOMCall('sort_customer_div').className = 'hide_row';
                                    DOMCall('date_type_tbody').className = 'hide';
                                    DOMCall('order_site_div').className = 'hide_row';

                                    if (selected == '6' || selected == '7') {
                                        DOMCall('cat_div').className = 'show_row';
                                        DOMCall('cat_level_div').className = 'show_row';
                                        DOMCall('sort_country_div').className = 'hide_row';

                                        if (selected == '7') {
                                            DOMCall('pro_type_div').className = 'show_row';
                                            DOMCall('prod_qty_type_div').className = 'show_row';
                                            DOMCall('g2g_region_type_div').className = 'show_row';
                                            DOMCall('sales_period_div').className = 'show_row';
                                        } else {
                                            DOMCall('pro_type_div').className = 'hide_row';
                                            DOMCall('prod_qty_type_div').className = 'hide_row';
                                            DOMCall('g2g_region_type_div').className = 'hide_row';
                                            DOMCall('sales_period_div').className = 'hide_row';
                                        }

                                        DOMCall('custom_section_tbody').className = 'show';
                                    } else if (selected == '8' || selected == '9' || selected == '10') {
                                        DOMCall('cat_div').className = 'hide_row';
                                        DOMCall('cat_level_div').className = 'hide_row';
                                        DOMCall('sales_period_div').className = 'show_row';

                                        if (selected == '8') {
                                            DOMCall('sort_country_div').className = 'show_row';
                                            DOMCall('order_site_div').className = 'show_row';
                                        }
                                        else if (selected == '9') {
                                            DOMCall('sort_customer_div').className = 'show_row';
                                        }

                                        DOMCall('prod_qty_type_div').className = 'hide_row';
                                        DOMCall('g2g_region_type_div').className = 'hide_row';
                                        DOMCall('pro_type_div').className = 'hide_row';
                                        DOMCall('custom_section_tbody').className = 'show';
                                    } else if (selected == '2' || selected == '4') {
                                        DOMCall('date_type_tbody').className = 'show';
                                        DOMCall('custom_section_tbody').className = 'hide';
                                    } else {
                                        DOMCall('custom_section_tbody').className = 'hide';
                                    }
                                }

                                function verify_status_selection(status_obj) {
                                    if (status_obj != null) {
                                        var cur_status_id = status_obj.value;
                                        var disabled_mode = status_obj.checked ? false : true;

                                        if (cur_status_id == '2' || cur_status_id == '3') {
                                            var sub_status_select = document.sales_report_criteria.elements['order_sub_status_' + cur_status_id + '[]'];

                                            if (typeof(sub_status_select) != 'undefined') {
                                                if (typeof(sub_status_select.length) != 'undefined') {
                                                    for (sub_cnt = 0; sub_cnt < sub_status_select.length; sub_cnt++) {
                                                        sub_status_select[sub_cnt].disabled = disabled_mode;
                                                        sub_status_select[sub_cnt].checked = !disabled_mode;
                                                    }
                                                } else {
                                                    sub_status_select.disabled = disabled_mode;
                                                    sub_status_select.checked = !disabled_mode;
                                                }
                                            }
                                        }
                                    }
                                }

                                function init() {
                                    swap_fields(document.getElementById('report').value);
    <? echo "refreshDynamicSelectOptions(jQuery(\"#pro_type\"), 'cat_id', '" . (int) $languages_id . "', 'custom_product', false, '" . FILENAME_SALES_REPORT . "', '" . $login_id . "');"; ?>
                                }

                                init();
                                //-->
                            </script>
                            <?
                        }
                        ?>
                    </table>
                </td>

                <!-- body_text_eof //-->
            </tr>
        </table>
        <!-- body_eof //-->

        <!-- footer //-->
        <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <br>
    </body>
</html>
<?php
function tep_generate_sales_report($input_array) {
    if (!$input_array['cont']) {
        $_SESSION['sales_param']["cat_id"] = $input_array["cat_id"];
        $_SESSION['sales_param']['start_date'] = $input_array["start_date"];
        $_SESSION['sales_param']['end_date'] = $input_array["end_date"];
        $_SESSION['sales_param']['order_status'] = $input_array["order_status"];
        $_SESSION['sales_param']["order_sub_status_2"] = $input_array["order_sub_status_2"];
        $_SESSION['sales_param']["order_sub_status_3"] = $input_array["order_sub_status_3"];
        $_SESSION['sales_param']['payment_methods_id'] = $input_array['payment_methods_id'];
        $_SESSION['sales_param']['payment_gateways_id'] = $input_array["payment_gateways_id"];
        $_SESSION['sales_param']['report'] = $input_array["report"];
        $_SESSION['sales_param']['cat_level'] = $input_array["cat_level"];
        $_SESSION['sales_param']['products_qty_type'] = $input_array["products_qty_type"];
        $_SESSION['sales_param']['g2g_region_layer'] = $input_array["g2g_region_layer"];
        $_SESSION['sales_param']['sales_period'] = $input_array["sales_period"];
        $_SESSION['sales_param']["sort_by"] = $input_array["report"] == '8' ? $input_array["sort_country_by"] : ($input_array["report"] == '9' ? $input_array["sort_customer_by"] : '');
        $_SESSION['sales_param']["sort_order"] = $input_array["sort_order"];
        $_SESSION['sales_param']["pro_type"] = $input_array["pro_type"];
        $_SESSION['sales_param']['date_type'] = $input_array['date_type'];
        $_SESSION['sales_param']["order_site"] = $input_array["order_site"];
    }

    $report_obj = new sales_report_new($input_array["report"], $input_array["start_date"], $input_array["end_date"], $_SESSION['sales_param']["sales_period"], $input_array['cont'], $input_array['hidden_export_report']);
    $report_obj->date_type = tep_not_null($input_array['date_type']) ? $input_array['date_type'] : '';
    $report_obj->export_format = tep_not_null($_REQUEST['hidden_export_format']) ? $_REQUEST['hidden_export_format'] : '';
    $report_obj->set_sorting_info($_SESSION['sales_param']["sort_by"], $_SESSION['sales_param']["sort_order"]);
    $report_obj->order_site = tep_not_null($input_array['order_site']) ? $input_array['order_site'] : '';

    $order_status_where_str = "o.orders_status IN('')"; // Must select any order status else return empty result

    if (isset($_SESSION['sales_param']["order_status"])) {
        if (in_array(2, $_SESSION['sales_param']["order_status"]) && !count($_SESSION['sales_param']["order_sub_status_2"])) {
            unset($_SESSION['sales_param']["order_status"][array_search(2, $_SESSION['sales_param']["order_status"])]);
        }

        if (in_array(3, $_SESSION['sales_param']["order_status"]) && !count($_SESSION['sales_param']["order_sub_status_3"])) {
            unset($_SESSION['sales_param']["order_status"][array_search(3, $_SESSION['sales_param']["order_status"])]);
        }

        if (count($_SESSION['sales_param']["order_status"])) {
            $order_status_where_str = " o.orders_status IN (" . implode(", ", $_SESSION['sales_param']["order_status"]) . ") ";
        }
    }

    $ot_gv_where_str = '';
    $payment_method_where_str = "0";

    if (isset($_SESSION['sales_param']["payment_gateways_id"]) && count($_SESSION['sales_param']["payment_gateways_id"]) ||
        $_SESSION['sales_param']["payment_gateways_id"] == 'any') {
        $actual_selected_payment_method = $_SESSION['sales_param']["payment_gateways_id"];

        if (in_array("any", $_SESSION['sales_param']["payment_gateways_id"])) {
            $payment_method_where_str = 1;
            $report_obj->pg_is_any = true;
        } else {
            if (in_array("OGM_CREDITS", $_SESSION['sales_param']["payment_gateways_id"])) {
                $ot_gv_where_str = " ot_gv.value > 0 ";
                $search_index_key = array_search('OGM_CREDITS', $actual_selected_payment_method);
                unset($actual_selected_payment_method[$search_index_key]);
            }

            if (count($actual_selected_payment_method)) {
                $payment_method_where_str = " o.payment_methods_parent_id IN ('" . implode("', '", $actual_selected_payment_method) . "')";
            }

            if (tep_not_null($ot_gv_where_str))
                $payment_method_where_str = '( ' . $payment_method_where_str . ' OR ' . $ot_gv_where_str . ')';
        }
    }

    if (isset($_SESSION['sales_param']['payment_methods_id']) && count($_SESSION['sales_param']['payment_methods_id'])) {
        $payment_method_where_str .= " OR o.payment_methods_id IN ('" . implode("', '", $_SESSION['sales_param']['payment_methods_id']) . "')";
    }

    $report_obj->set_products_qty_type($_SESSION['sales_param']['products_qty_type']);
    $report_obj->set_order_amount_status(2, $_SESSION['sales_param']['order_sub_status_2']);
    $report_obj->set_order_amount_status(3, $_SESSION['sales_param']['order_sub_status_3']);
    $report_obj->g2g_region_layer = tep_not_null($_SESSION['sales_param']['g2g_region_layer']) ? true : false;

    if ($input_array['report'] == 2 && tep_not_null($input_array['hidden_export_report'])) { // export Daily report
        $report_obj->sql_filter = $order_status_where_str;
    } else {
        $report_obj->sql_filter = $order_status_where_str . " AND (" . $payment_method_where_str . ") ";
    }

    $report_obj->reportQuery();
    $report_obj->getSummaryText();
    return $report_obj;
}
?>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>