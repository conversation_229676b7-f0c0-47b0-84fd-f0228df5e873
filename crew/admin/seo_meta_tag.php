<?php
/*
	$Id: seo_meta_tag.php,v 1.3 2008/10/14 05:41:57 edwin.wang Exp $

	Developer: <PERSON>
	Copyright (c) 2007 SKC Ventrue

	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'seo_functions.php');
require(DIR_WS_CLASSES . 'seo_html.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$id = (isset($_REQUEST['id']) ? $_REQUEST['id'] : '');
$page_type = (isset($_REQUEST['page_type']) ? $_REQUEST['page_type'] : '');
$search_keyword = (isset($_REQUEST['search_keyword']) ? $_REQUEST['search_keyword'] : '');

$function = new SEO(); // Function - Actions
$html = new SEO_HTML(); // HTML printout

switch($action) {
	case "add_form":
		$header_title = "";
		$form_content = $html->addForm($id);
		
		break;
	case "add":
		$function->addEntry($id);
		tep_redirect(tep_href_link(FILENAME_SEO_META_TAG,'selected_box=infolinks&page_type='.urlencode($page_type)));
		exit;
		break;
	case "changestatus":
		echo $function->changeStatus($id);

		exit;
		break;
	case "delete":
		echo $function->deleteEntry($id);

		exit;
		break;
	case "export":
		$result = $function->export($page_type,$search_keyword);
		
		if (!$result) {
			$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
			tep_redirect(tep_href_link(FILENAME_SEO_META_TAG));
		}
		
		break;

	default:
		$header_title = "";
		$form_content = $html->seoListing();

		break;
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="JavaScript" src="includes/javascript/jquery.js"></script>
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
			<tr>
				<td width="<?php echo BOX_WIDTH; ?>" valign="top">
					<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
					</table>
				</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	 				<tr>
		 				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>

