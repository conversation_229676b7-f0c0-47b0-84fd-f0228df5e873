<?php

require('includes/application_top.php');

$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';
$filename = '';

switch ($action) {
    case 'bb_sc':
        $buyback_request_group_id = $_GET['id'];
        list($bo_id, $trade_state) = explode('_', $buyback_request_group_id);

        $screenshot_select_sql = "	SELECT buyback_request_screenshot_before_name, buyback_request_screenshot_after_name 
										FROM " . TABLE_BUYBACK_REQUEST . " 
										WHERE buyback_request_group_id='" . tep_db_input($bo_id) . "'";
        $screenshot_result_sql = tep_db_query($screenshot_select_sql);
        if ($screenshot_row = tep_db_fetch_array($screenshot_result_sql)) {
            switch ($trade_state) {
                case 1:
                    $ss_name = $screenshot_row['buyback_request_screenshot_before_name'];

                    break;
                case 2:
                    $ss_name = $screenshot_row['buyback_request_screenshot_after_name'];

                    break;
            }

            if (tep_not_null($ss_name)) {
                $filename = DIR_FS_CNBB . "download/" . $ss_name;
            }
        }

        break;
    case 'show_bb_ss':
        $buyback_request_group_id = $_GET['req_grp_id'];
        list($bo_id, $trade_state) = explode('_', $buyback_request_group_id);

        $screenshot_select_sql = "	SELECT buyback_request_screenshot_before_name, buyback_request_screenshot_after_name 
									FROM " . TABLE_BUYBACK_REQUEST . " 
									WHERE buyback_request_group_id='" . tep_db_input($bo_id) . "'";
        $screenshot_result_sql = tep_db_query($screenshot_select_sql);
        if ($screenshot_row = tep_db_fetch_array($screenshot_result_sql)) {
            switch ($trade_state) {
                case 1:
                    $ss_name = $screenshot_row['buyback_request_screenshot_before_name'];

                    break;
                case 2:
                    $ss_name = $screenshot_row['buyback_request_screenshot_after_name'];

                    break;
            }

            if (tep_not_null($ss_name)) {
                $filename = tep_upload_href_link("delivery/" . $ss_name, '', 'SSL');
            }
        }
        break;
    case 'show_g2g_bb_ss':
        $buyback_request_group_id = $filename = $_GET['file'];
        $s3_filepath = $_GET['path'];
        include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');

        break;
    case 'show_g2g_seller_reject_proof':
        $filename =  $_GET['path'];
        include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
        $aws_obj = new ogm_amazon_ws();
        $buckets_array = $aws_obj::$buckets_array['BUCKET_UPLOAD'];
        $s3url = $aws_obj->get_object_url($buckets_array['bucket'], $filename, "10 seconds");
        tep_redirect($s3url);
        exit;
        break;
    case 'show_pdf':
        $bucket = tep_db_input($_GET['domain']);
        $filename = tep_db_input($_GET['path']);
        include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
        $aws_obj = new ogm_amazon_ws();
        $s3url = $aws_obj->get_object_url($bucket, $filename, "10 seconds");
        tep_redirect($s3url);
        exit;
        break;
    case 'show_g2g_bb_proof':
        $time = time();
        $data = array(
            'merchant' => G2G_API_OGCREW_MERCHANT,
            'signature' => md5(G2G_API_OGCREW_MERCHANT . $time . '|' . G2G_API_OGCREW_SECRET),
            'filename' => tep_db_input(urlencode($_GET['file'])),
            'time' => $time
        );

        $curl_obj = new curl();
        $curl_res = $curl_obj->curl_post(G2G_API_URL . "/serverless/get-upload-proof-link", $data);
        $response = json_decode($curl_res, true);
        
        if (isset($response["status"]) && ($response["status"] == true)) {
            if (isset($response['result'])) {
                 tep_redirect($response['result']);
                 exit;
                 break;  
            }
        }

        echo 'Not able to get file, please try again';    
        exit;
        break;
    case 'show_fraud':
        include_once(DIR_WS_CLASSES . 'curl.php');
        $curl_obj = new curl();
        $G2G_API_URL = G2G_API_URL . '/user/fraud-flag';
        $data = array(
            'merchant' => G2G_API_OGCREW_MERCHANT,
            'signature' => md5($_GET['customer_id'] . "|" . G2G_API_OGCREW_SECRET),
            'tid' => $_GET['customer_id'],
        );

        $curl_res = $curl_obj->curl_post($G2G_API_URL, $data);
        $response = json_decode($curl_res, true);
        if (isset($response["status"]) && ($response["status"] == true)) {
            if(isset($response["result"])){
                echo "<strong>Customer ID: " . $_GET['customer_id'] . "</strong>";
                echo "<br /><br />";
                echo $response["result"];
            } else {
                echo "Failed to retreive fraud information, please try again";
            }
        } else {
            echo "Failed to retreive fraud information, please try again";
        }
        exit;
        break;
    
}

header("Content-type: image/jpeg");
header("Expires: Mon, 02 May 2001 23:00:00 GMT");
header("Cache-Control: no-store, no-cache, must-revalidate");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Content-Disposition:inline;filename=" . $buyback_request_group_id);

if (tep_not_null($filename)) {
    if ($action == 'show_bb_ss') {
        $url = tep_upload_href_link("show_image.php", '', 'SSL');

        unset($response);
        //$url = "https://upload.offgamers.com/upload.php";
        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_REFERER, basename($_SERVER['SCRIPT_NAME']));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, array('ss_name' => $ss_name, 'ip' => tep_get_ip_address()));
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
        $response = curl_exec($ch);
        curl_close($ch);

        echo $response;
    } else if ($action == 'show_g2g_bb_ss') {
        $s3_bucket = 'BUCKET_UPLOAD';

        $aws_obj = new ogm_amazon_ws();
        $aws_obj->set_bucket_key($s3_bucket);
        $aws_obj->set_filepath($s3_filepath);

        if ($aws_obj->is_aws_s3_enabled()) {
            $file_content = $aws_obj->get_file($filename);
            echo $file_content->body;
        }
    } else {
        if (file_exists($filename) && is_readable($filename)) {
            echo file_get_contents($filename);
        }
    }
}
exit;
