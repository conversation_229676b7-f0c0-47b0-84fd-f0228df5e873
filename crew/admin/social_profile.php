<?php
require('includes/application_top.php');

$social_content='';
if (isset($_GET['email'])) {
    $social_content = file_get_contents('http://api.rainmaker.cc/v2/person.json?email=' . tep_db_prepare_input($_GET['email']) . '&apiKey=7cd7d138a03115f4&timeoutSeconds=30');
    $social_content = json_decode($social_content);
}
?>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <script language="javascript" src="includes/general.js"></script>
        <style>
            #outer{
                width:80%;

                /* Firefox */
                display:-moz-box;
                -moz-box-pack:center;
                -moz-box-align:center;

                /* Safari and Chrome */
                display:-webkit-box;
                -webkit-box-pack:center;
                -webkit-box-align:center;

                /* W3C */
                display:box;
                box-pack:center;
                box-align:center;
            }
            #inner{
                width:50%;
            }
        </style>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <div id="outer">  
            <div id="inner">
                <?php
                if (!is_null($social_content) && isset($social_content->socialProfiles)) {
                    foreach ($social_content->socialProfiles as $info) {
                        echo $info->typeName . '<br>';
                        echo '<a href="' . $info->url . '" target="_blank">' . $info->username . (isset($info->id) ? '(' . $info->id . ')' : '') . '</a><br>';
                        echo '<br><br>';
                    }
                } else {
                    echo "Social profile not found for <b>" . $_GET['email'] . '</b>';
                }
                ?>
            </div>
        </div>
        <!-- footer //-->
        <?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
        <br>
    </body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>