<?
/*
  	$Id: stats_customers.php,v 1.10 2009/12/08 04:10:40 henry.chow Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_CLASSES . 'payment_methods.php');
include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');

$currencies = new currencies();
$log_object = new log_files($login_id);

$action = $_REQUEST['action'];

if (tep_not_null($action)) {
	switch ($action) {
		case 'update_customer_info':
			if (isset($_POST['customers_groups_id']) && count($_POST['customers_groups_id'])) {
				foreach ($_POST['customers_groups_id'] as $cid => $gid) {
					$customer_log_select_sql = "SELECT customers_groups_id FROM " . TABLE_CUSTOMERS . " WHERE customers_id='" . tep_db_input($cid) . "'";
					$customer_log_result_sql = tep_db_query($customer_log_select_sql);
					$customer_old_log_row = tep_db_fetch_array($customer_log_result_sql);
					
					$customer_grp_update_sql = "UPDATE " . TABLE_CUSTOMERS . " SET customers_groups_id = '" . tep_db_input($gid) . "' WHERE customers_id = '" . tep_db_input($cid) . "'";
					tep_db_query($customer_grp_update_sql);
					
					$customer_log_result_sql = tep_db_query($customer_log_select_sql);
					$customer_new_log_row = tep_db_fetch_array($customer_log_result_sql);
					
					$customer_changes_array = $log_object->detect_changes($customer_old_log_row, $customer_new_log_row);
					$customer_changes_formatted_array = $log_object->construct_log_message($customer_changes_array);
					
					if (count($customer_changes_formatted_array)) {
						$changes_str = 'Changes made:' . "\n";
						for ($i=0; $i < count($customer_changes_formatted_array); $i++) {
							if (count($customer_changes_formatted_array[$i])) {
								foreach ($customer_changes_formatted_array[$i] as $field => $res) {
									$changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . ' --> ' . $res['to'] . "\n";
								}
							}
						}
						
						$log_object->insert_customer_history_log($cid, $changes_str);
					}
				}
			}
			
			$messageStack->add_session(SUCCESS_CUSTOMER_GROUP_UPDATE, 'success');
	        tep_redirect(tep_href_link(FILENAME_STATS_CUSTOMERS, tep_get_all_get_params(array('action'))));
	        
			break;
	}
}

$payment_info_array = array();
$payment_methods_obj = new payment_methods('payment_methods');
foreach ($payment_methods_obj->payment_methods_array as $payment_methods_id => $payment_methods_data) {
	$payment_info_array[$payment_methods_id] = new objectInfo($payment_methods_data);
}

$customer_grp_select_sql = "SELECT customers_groups_id, customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS ." ORDER BY sort_order, customers_groups_name ASC";
$customer_grp_result_sql = tep_db_query($customer_grp_select_sql);
while($customer_grp_row = tep_db_fetch_array($customer_grp_result_sql)) {
	$customer_groups_array[] = array(	'id' => $customer_grp_row['customers_groups_id'],
										'text' => $customer_grp_row['customers_groups_name']);
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
	<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
	<!-- header_eof //-->
	
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
					<!-- left_navigation_eof //-->
        		</table>
        	</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
          						<tr>
            						<td valign="top">
            						<?=tep_draw_form('total_purchase_form', FILENAME_STATS_CUSTOMERS, tep_get_all_get_params(array('action')) . 'action=update_customer_info', 'post', ''); ?>
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
                								<td class="reportBoxHeading"><?=TABLE_HEADING_NUMBER?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_NAME?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_EMAIL?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_ACCOUNT_CREATED_DATE?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_CUSTOMER_GROUP?></td>
								                <td class="reportBoxHeading" align="right"><?=TABLE_HEADING_TOTAL_PURCHASED?>&nbsp;</td>
								                <td class="reportBoxHeading" align="right"><?=TABLE_HEADING_TOTAL_CONFIRM_PURCHASED?>&nbsp;</td>
              								</tr>
<?
if (isset($HTTP_GET_VARS['page']) && ($HTTP_GET_VARS['page'] > 1)) $rows = $HTTP_GET_VARS['page'] * MAX_DISPLAY_SEARCH_RESULTS - MAX_DISPLAY_SEARCH_RESULTS;

$user_flags_array = tep_get_user_flags();

//$customers_query_raw = "select c.customers_firstname, c.customers_lastname, c.customers_groups_id, c.customers_flag, SUM(op.products_quantity * op.final_price) as ordersum, ci.customers_info_date_account_created from " . TABLE_CUSTOMERS . " c, " . TABLE_ORDERS_PRODUCTS . " op, " . TABLE_ORDERS . " o, " . TABLE_CUSTOMERS_INFO . " ci where c.customers_id=o.customers_id and o.orders_id=op.orders_id and ci.customers_info_id = c.customers_id and o.orders_status=3 group by c.customers_id order by ordersum DESC";
$customers_query_raw = "select c.customers_id, c.customers_firstname, c.customers_lastname, c.customers_email_address, c.customers_groups_id, c.customers_flag, SUM(op.products_good_delivered_price) as ordersum, ci.customers_info_date_account_created from " . TABLE_CUSTOMERS . " c, " . TABLE_ORDERS . " o, " . TABLE_ORDERS_PRODUCTS . " op, " . TABLE_CUSTOMERS_INFO . " ci where c.customers_id=o.customers_id and o.orders_id=op.orders_id and op.orders_products_is_compensate=0 and ci.customers_info_id=c.customers_id and o.orders_status=3 group by c.customers_id order by ordersum DESC";
$customers_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $customers_query_raw, $customers_query_numrows, true);

$row_count = 0;
$customers_result_sql = tep_db_query($customers_query_raw);
while ($customers_row = tep_db_fetch_array($customers_result_sql)) {
	$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd';
	$row_count++;
	
	$confirm_completed_sales = 0;
	$cust_name_style = '';
	
	if (tep_not_null($customers_row['customers_flag'])) {
		$customer_flags_array = explode(',', $customers_row['customers_flag']);
		arsort($customer_flags_array);
		reset($customer_flags_array);
		
		$cust_name_style = $user_flags_array[end($customer_flags_array)]['user_flags_css_style'];
	}
	
	if ((double)$customers_row['ordersum'] > 0) {
  		$customer_payment_methods_select_sql = "SELECT DISTINCT payment_methods_id 
												FROM " . TABLE_ORDERS . " 
												WHERE customers_id='".tep_db_input($customers_row['customers_id'])."' 
													AND orders_status='3' ";
		$customer_payment_methods_result_sql = tep_db_query($customer_payment_methods_select_sql);
		while ($customer_payment_methods_row = tep_db_fetch_array($customer_payment_methods_result_sql)) {
			
			$confirm_complete_day = (int)$payment_info_array[$customer_payment_methods_row['payment_methods_id']]->confirm_complete_days > 0 ? (int)$payment_info_array[$customer_payment_methods_row['payment_methods_id']]->confirm_complete_days : 0;
			
			$confirm_complete_order_select_sql = "	SELECT SUM(op.products_good_delivered_price) AS confirm_sales 
													FROM " . TABLE_ORDERS . " AS o 
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
														ON o.orders_id=op.orders_id 
													WHERE o.customers_id='".tep_db_input($customers_row['customers_id'])."' 
														AND DATE_FORMAT(o.date_purchased, '%Y-%m-%d %H:%i:%s') <= DATE_SUB(NOW(), INTERVAL ".$confirm_complete_day." DAY) 
														AND o.orders_status='3' 
														AND o.payment_methods_id='" . $customer_payment_methods_row["payment_methods_id"] . "' 
														AND op.orders_products_is_compensate=0";
			$confirm_complete_order_result_sql = tep_db_query($confirm_complete_order_select_sql);
			if ($confirm_complete_order_row = tep_db_fetch_array($confirm_complete_order_result_sql)) {
				$confirm_completed_sales += $confirm_complete_order_row["confirm_sales"];
			}
		}
	}
?>
											<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
								                <td class="dataTableContent"><?=sprintf('%02d.', $row_count)?></td>
								                <td class="dataTableContent"><?=$customers_row['customers_firstname'].' '.$customers_row['customers_lastname']?></td>								              								              
								                <td class="dataTableContent"><?='<a href="' . tep_href_link(FILENAME_CUSTOMERS, 'action=show_report&customer_lists_subaction=do_search&email=' . $customers_row['customers_email_address'], 'NONSSL') . '"><span class="'.$cust_name_style.'">' . $customers_row['customers_email_address'] . '</span></a>'?></td>								              
								                <td class="dataTableContent"><?=$customers_row['customers_info_date_account_created']?></td>
								                <td class="dataTableContent">
								                	<?=tep_draw_pull_down_menu('customers_groups_id['.$customers_row['customers_id'].']', $customer_groups_array, $customers_row['customers_groups_id'])?>
								                </td>
								                <td class="dataTableContent" align="right" nowrap><?=$currencies->format($customers_row['ordersum'])?>&nbsp;</td>
								                <td class="dataTableContent" align="right" nowrap><?=$currencies->format($confirm_completed_sales)?>&nbsp;</td>
              								</tr>
<?
}

if ($row_count) {
	echo '	<tr>
				<td>'.tep_draw_separator('pixel_trans.gif', '1', '1').'</td>
			</tr>
			<tr>
				<td colspan="4"></td>
				<td>'.tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'onClick="if (confirm(\'Are you sure to update customers group setting?\')) { return true; } else { return false; }"', 'inputButton').'</td>
				<td colspan="2"></td>
			</tr>';
}
?>
            							</table>
            						</form>
            						</td>
          						</tr>
          						<tr>
            						<td>
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr>
								                <td class="smallText" valign="top"><? echo $customers_split->display_count($customers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_CUSTOMERS); ?></td>
								                <td class="smallText" align="right"><? echo $customers_split->display_links($customers_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?>&nbsp;</td>
              								</tr>
            							</table>
            						</td>
          						</tr>
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>