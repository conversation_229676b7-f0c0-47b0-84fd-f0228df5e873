<?php
/*
  	$Id: stats_low_stock.php,v 1.4 2015/04/13 11:30:30 weichen Exp $
	(v 1.1 by <PERSON><PERSON><PERSON> 2003/04/24)
	(v 1.11 by <PERSON> 2004/03/30)
	(v 1.12 by <PERSON> 2004/04/01) 
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php');  ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<?
					require(DIR_WS_INCLUDES . 'column_left.php'); 
					$sorted = $HTTP_GET_VARS['sorted'];
					$orderby = $HTTP_GET_VARS['orderby'];
					if ($sorted !== "ASC" and $sorted !== "DESC") $sorted = "ASC"; 
					?>
					<!-- left_navigation_eof //-->
        		</table>
        	</td>
<!-- body_text //-->
   			<td width="100%" valign="top">
   				<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
								                <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_NUMBER; ?></td>
								                <td class="dataTableHeadingContent"><?php  if (!isset($orderby) or ($orderby == "name" and $sorted == "ASC"))  $to_sort = "DESC"; else $to_sort = "ASC"; echo '<a href="' . tep_href_link(FILENAME_STATS_LOW_STOCK, 'orderby=name&sorted='. $to_sort) . '" class="headerLink">' . TABLE_HEADING_PRODUCTS . '</a>';  ?></td>
												<td class="dataTableHeadingContent"><?php  if (!isset($orderby) or ($orderby == "path" and $sorted == "ASC"))  $to_sort = "DESC"; else $to_sort = "ASC"; echo '<a href="' . tep_href_link(FILENAME_STATS_LOW_STOCK, 'orderby=path&sorted='. $to_sort) . '" class="headerLink">Item Path</a>';  ?></td>
								                <td class="dataTableHeadingContent" align="right"><?php  if (!isset($orderby) or ($orderby == "stock" and $sorted == "ASC"))  $to_sort = "DESC"; else $to_sort = "ASC"; echo '<a href="' . tep_href_link(FILENAME_STATS_LOW_STOCK, 'orderby=stock&sorted='. $to_sort) . '" class="headerLink">' .TABLE_HEADING_QTY_LEFT . '</a>'; ?>&nbsp;</td>
              								</tr>
<?php
if ($HTTP_GET_VARS['page'] > 1) $rows = $HTTP_GET_VARS['page'] * 20 - 20;

if (tep_check_cat_tree_permissions(FILENAME_CATEGORIES, 0) != 1) {
	$sub_cat_array = tep_get_eligible_categories(FILENAME_CATEGORIES, $sub_cat_array, 0, true);
	
	$extra_join_str = " left join " . TABLE_PRODUCTS_TO_CATEGORIES . " as pc on (p.products_id=pc.products_id and pc.products_is_link=0) ";
	
	$filter_str = " AND pc.categories_id IN ('" . implode("', '", $sub_cat_array) . "') ";
}

if ($orderby == "name") {
	$db_orderby = "pd.products_name";
} else if ($orderby == "stock") {
	$db_orderby = "p.products_quantity";
} else {
	$db_orderby = "pd.products_name";
}

$products_query_raw = "select p.products_id, p.products_cat_path, p.products_quantity, pd.products_name, p.products_model from " . TABLE_PRODUCTS . " AS p, " . TABLE_PRODUCTS_DESCRIPTION . " AS pd " . $extra_join_str . " where p.products_id = pd.products_id and pd.language_id = '" . $languages_id. "' and p.products_quantity <= " . STOCK_REORDER_LEVEL . $filter_str . " group by pd.products_id order by $db_orderby $sorted";
$products_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $products_query_raw, $products_query_numrows);
$products_query = tep_db_query($products_query_raw);
while ($products = tep_db_fetch_array($products_query)) {
    $rows++;
	
    if (strlen($rows) < 2) {
      	$rows = '0' . $rows;
    }
	
	$products_id = $products['products_id'];
	$last_category_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = $products_id");
	$last_category = tep_db_fetch_array($last_category_query);
	$p_category = $last_category["categories_id"];
	
	do {
		$p_category_array[] = $p_category;
		$last_category_query = tep_db_query("select parent_id from " . TABLE_CATEGORIES . " where categories_id = $p_category");
		$last_category = tep_db_fetch_array($last_category_query);
		$p_category = $last_category["parent_id"];
	} while ($p_category);
	
	$cPath_array = array_reverse($p_category_array);
	$cat_name_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = " . $products['products_id'] . " order by products_id");
	$cat_name = tep_db_fetch_array($cat_name_query);
	$cat_path = tep_output_generated_category_path_sq($cat_name['categories_id']);
?>
											<tr class="datatableRow" onmouseover="this.className='datatableRowOver';this.style.cursor='hand'" onmouseout="this.className='datatableRow'" onclick="document.location.href='<?php echo tep_href_link(FILENAME_CATEGORIES, tep_get_path() . '&pID=' . $products['products_id'] . '&action=new_product', 'NONSSL'); ?>'">
            									<td align="left" class="smallText">&nbsp;<?php echo $rows; ?>.&nbsp;</td>
									            <td class="smallText">&nbsp;<?php echo '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path() . '&pID=' . $products['products_id']) . '" class="blacklink">' . $products['products_name'] . '</a>'; ?>&nbsp;</td>
												<td class="smallText">&nbsp;<?php echo '<a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path() . '&pID=' . $products['products_id'], 'NONSSL') . '&action=new_product">' . $cat_path . '</a>'; ?>&nbsp;</td>
									            <td align="right" class="smallText">&nbsp;<?php echo $products['products_quantity']; ?>&nbsp;</td>
          									</tr>
<?php
  	unset($cPath_array); unset($p_category_array); 
}
?>
          								</table>
          							</td>
								</tr>
          						<tr>
            						<td colspan="3">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr>
                								<td class="smallText" valign="top"><?php echo $products_split->display_count($products_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_PRODUCTS); ?></td>
                								<td class="smallText" align="right"><?php echo $products_split->display_links($products_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?>&nbsp;</td>
              								</tr>
            							</table>
            						</td>
          						</tr>
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
			<!-- body_text_eof //-->
  		</tr>
	</table>
	<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>