<?php
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
	<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
	<!-- header_eof //-->
	
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="0">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
								                <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_NUMBER; ?></td>
								                <td class="dataTableHeadingContent"><?php echo TABLE_HEADING_PRODUCTS_NAME; ?></td>
								                <td class="dataTableHeadingContent" align="left"><?php echo "Products Path"; ?></td>
								                <td class="dataTableHeadingContent" align="right"><?php echo TABLE_HEADING_PRODUCTS_PRICE; ?>&nbsp;</td>
								                <td class="dataTableHeadingContent" align="center"><?php echo TABLE_HEADING_PRODUCTS_QTY; ?>&nbsp;</td>
								                <td class="dataTableHeadingContent" align="left"><?php echo TABLE_HEADING_PRODUCTS_LOC; ?>&nbsp;</td>
								                <td class="dataTableHeadingContent" align="center"><?php echo TABLE_HEADING_PRODUCTS_BUNDLE; ?>&nbsp;</td>
              								</tr>
<?
if (isset($HTTP_GET_VARS['page']) && ($HTTP_GET_VARS['page'] > 1)) $rows = $HTTP_GET_VARS['page'] * MAX_DISPLAY_SEARCH_RESULTS - MAX_DISPLAY_SEARCH_RESULTS;

if (tep_check_cat_tree_permissions(FILENAME_CATEGORIES, 0) != 1) {
	$sub_cat_array = tep_get_eligible_categories(FILENAME_CATEGORIES, $sub_cat_array, 0, true);
	
	$extra_join_str = " left join " . TABLE_PRODUCTS_TO_CATEGORIES . " as pc on (p.products_id=pc.products_id and pc.products_is_link=0) ";
	
	$filter_str = " AND pc.categories_id IN ('" . implode("', '", $sub_cat_array) . "') ";
}

$rows = 0;
$products_query_raw = "select p.products_id, p.products_quantity, p.products_price, p.products_base_currency, p.products_bundle, p.products_cat_path, pd.products_name, pd.products_location, l.name from " . TABLE_PRODUCTS . " AS p, " . TABLE_PRODUCTS_DESCRIPTION . " AS pd, " . TABLE_LANGUAGES . " AS l " . $extra_join_str . " where p.products_id=pd.products_id and l.languages_id=pd.language_id " . $filter_str . " order by pd.products_location ASC";
$products_split = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $products_query_raw, $products_query_numrows);
$products_query = tep_db_query($products_query_raw);
while ($products = tep_db_fetch_array($products_query)) {
	$rows++;
	
    if (strlen($rows) < 2) {
      $rows = '0' . $rows;
    }
?>
              								<!--tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href='<?php echo tep_href_link(FILENAME_CATEGORIES, 'action=new_product&pID=' . $products['products_id'] . '&origin=' . FILENAME_STATS_PRODUCTS . '?page=' . $HTTP_GET_VARS['page'], 'NONSSL'); ?>'"-->
              								<tr class="dataTableRow">
                								<td class="dataTableContent"><?php echo $rows; ?>.</td>
                								<td class="dataTableContent">
<?
	$cat_name_query = tep_db_query("select categories_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = " . $products['products_id'] . " order by products_id");
	$cat_name = tep_db_fetch_array($cat_name_query);
	$cat_path = tep_output_generated_category_path_sq($cat_name['categories_id']);
	
	echo $products['products_name'];
	//echo '<a href="' . tep_href_link(FILENAME_CATEGORIES, 'action=new_product&pID=' . $products['products_id'] . '&origin=' . FILENAME_STATS_PRODUCTS_VIEWED . '?page=' . $HTTP_GET_VARS['page'], 'NONSSL') . '">' . $products['products_name'] . '</a>'; 
?>
												</td>
                								<td class="dataTableContent" align="left"><?php echo $cat_path; ?>&nbsp;</td>
								                <td class="dataTableContent" align="right" nowrap><?php echo $currencies->format($products['products_price'], false, $products['products_base_currency']); ?>&nbsp;</td>
								                <td class="dataTableContent" align="center"><?php echo $products['products_quantity']; ?>&nbsp;</td>
								                <td class="dataTableContent" align="left"><?php echo $products['products_location']; ?>&nbsp;</td>
								                <td class="dataTableContent" align="center"><?php echo $products['products_bundle']; ?>&nbsp;</td>
              								</tr>
<?
}
?>
            							</table>
            						</td>
          						</tr>
          						<tr>
            						<td colspan="3">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr>
                								<td class="smallText" valign="top"><?php echo $products_split->display_count($products_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_PRODUCTS); ?></td>
                								<td class="smallText" align="right"><?php echo $products_split->display_links($products_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page']); ?></td>
              								</tr>
            							</table>
            						</td>
          						</tr>
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
			<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>