<?php
/*
  $Id: stats_sales_report.php,v 1.8 2013/10/31 07:52:49 weichen Exp $

  Released under the GNU General Public License
 */

require('includes/application_top.php');

ini_set("memory_limit", "2048M");
tep_set_time_limit(0);

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

$default_status_array = array('1' => 1, '7' => 1, '2' => 1, '3' => 1, '5' => 1, '8' => 1);
$default_order_amt_status_array = array('2' => array('0' => 1, '1' => 1, '2' => 1),
    '3' => array('0' => 1, '1' => 1, '2' => 1, '3' => 1, '4' => 1)
);

if (tep_not_null($_REQUEST['action'])) {
    if ($_REQUEST['action'] == 'do_filter') {
        if (isset($_REQUEST['order_sub_status_2']) && count($_REQUEST['order_sub_status_2'])) {
            for ($status_cnt = 0; $status_cnt < count($_REQUEST['order_sub_status_2']); $status_cnt++) {
                $default_order_amt_status_array['2'][$_REQUEST['order_sub_status_2'][$status_cnt]] = 0;
            }
            $filter2 = implode($default_order_amt_status_array['2']);
        } else {
            if (in_array(2, $_REQUEST['order_status']))
                unset($_REQUEST["order_status"][array_search(2, $_REQUEST["order_status"])]);
        }

        if (isset($_REQUEST['order_sub_status_3']) && count($_REQUEST['order_sub_status_3'])) {
            for ($status_cnt = 0; $status_cnt < count($_REQUEST['order_sub_status_3']); $status_cnt++) {
                $default_order_amt_status_array['3'][$_REQUEST['order_sub_status_3'][$status_cnt]] = 0;
            }
            $filter3 = implode($default_order_amt_status_array['3']);
        } else {
            if (in_array(3, $_REQUEST['order_status']))
                unset($_REQUEST["order_status"][array_search(3, $_REQUEST["order_status"])]);
        }

        if (isset($_REQUEST['order_status']) && count($_REQUEST['order_status'])) {
            for ($status_cnt = 0; $status_cnt < count($_REQUEST['order_status']); $status_cnt++) {
                $default_status_array[$_REQUEST['order_status'][$status_cnt]] = 0;
            }

            $filter = implode($default_status_array);
        }
    }
}

//Default to status red
if (is_null($filter))
    $sales_report_filter = "100011";
if (is_null($filter2))
    $sales_report_filter2 = "001";
if (is_null($filter3))
    $sales_report_filter3 = "01010";

// default view (daily)
$sales_report_default_view = 2;
// report views (1: hourly 2: daily 3: weekly 4: monthly 5: yearly)
$sales_report_view = $sales_report_default_view;
if (($_REQUEST['report']) && (tep_not_null($_REQUEST['report']))) {
    $sales_report_view = $_REQUEST['report'];
}
if ($sales_report_view > 5) {
    $sales_report_view = $sales_report_default_view;
}

if ($sales_report_view == 2) {
    $report = 2;
}

if ($report == 1) {
    $summary1 = AVERAGE_HOURLY_TOTAL;
    $summary2 = TODAY_TO_DATE;
    $report_desc = REPORT_TYPE_HOURLY;
} else if ($report == 2) {
    $summary1 = AVERAGE_DAILY_TOTAL;
    $summary2 = WEEK_TO_DATE;
    $report_desc = REPORT_TYPE_DAILY;
} else if ($report == 3) {
    $summary1 = AVERAGE_WEEKLY_TOTAL;
    $summary2 = MONTH_TO_DATE;
    $report_desc = REPORT_TYPE_WEEKLY;
} else if ($report == 4) {
    $summary1 = AVERAGE_MONTHLY_TOTAL;
    $summary2 = YEAR_TO_DATE;
    $report_desc = REPORT_TYPE_MONTHLY;
} else if ($report == 5) {
    $summary1 = AVERAGE_YEARLY_TOTAL;
    $summary2 = YEARLY_TOTAL;
    $report_desc = REPORT_TYPE_YEARLY;
}

// check start and end Date
$startDate = "";
if (($HTTP_GET_VARS['startDate']) && (tep_not_null($HTTP_GET_VARS['startDate']))) {
    $startDate = $HTTP_GET_VARS['startDate'];
}
$endDate = "";
if (($HTTP_GET_VARS['endDate']) && (tep_not_null($HTTP_GET_VARS['endDate']))) {
    $endDate = $HTTP_GET_VARS['endDate'];
}

$orders_default_site = (isset($_REQUEST['order_site'])) ? $_REQUEST['order_site'] : '1';

// check filters
$sales_report_filter_link = "&order_site=$orders_default_site";
if (tep_not_null($filter)) {
    $sales_report_filter = $filter;
    $sales_report_filter_link .= "&filter=$sales_report_filter";
}
if (tep_not_null($filter2)) {
    $sales_report_filter2 = $filter2;
    $sales_report_filter_link .= "&filter2=$sales_report_filter2";
}
if (tep_not_null($filter3)) {
    $sales_report_filter3 = $filter3;
    $sales_report_filter_link .= "&filter3=$sales_report_filter3";
}

require(DIR_WS_CLASSES . 'sales_report.php');
$report = new sales_report($sales_report_view, $startDate, $endDate, $sales_report_filter, $sales_report_filter2, $sales_report_filter3, $orders_default_site);

if (strlen($sales_report_filter) == 0) {
    $sales_report_filter = $report->filter;
    $sales_report_filter_link = "";
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
        <title><?php echo TITLE; ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <SCRIPT LANGUAGE="JavaScript1.2" SRC="jsgraph/graph.js"></SCRIPT>
        <!-- header //-->
        <?php require(DIR_WS_INCLUDES . 'header.php'); ?>
        <!-- header_eof //-->

        <!-- body //-->
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <!-- left_navigation //-->
                        <?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                        <!-- left_navigation_eof //-->
                    </table>
                </td>
                <!-- body_text //-->
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td colspan=2>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="pageHeading" valign="top"><?= $report_desc . ' ' . HEADING_TITLE ?></td>
                                        <td class="pageHeading" align="right"><?= tep_draw_separator('pixel_trans.gif', '1', '40') ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan=2>
                                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align=right class="menuBoxHeading">
                                            <?php
                                            echo '<a href="' . tep_href_link(FILENAME_STATS_SALES_REPORT, 'report=1' . $sales_report_filter_link, 'NONSSL') . '">' . REPORT_TYPE_HOURLY . '</a> | ';
                                            echo '<a href="' . tep_href_link(FILENAME_STATS_SALES_REPORT, 'report=2' . $sales_report_filter_link, 'NONSSL') . '">' . REPORT_TYPE_DAILY . '</a> | ';
                                            echo '<a href="' . tep_href_link(FILENAME_STATS_SALES_REPORT, 'report=3' . $sales_report_filter_link, 'NONSSL') . '">' . REPORT_TYPE_WEEKLY . '</a> | ';
                                            echo '<a href="' . tep_href_link(FILENAME_STATS_SALES_REPORT, 'report=4' . $sales_report_filter_link, 'NONSSL') . '">' . REPORT_TYPE_MONTHLY . '</a> | ';
                                            echo '<a href="' . tep_href_link(FILENAME_STATS_SALES_REPORT, 'report=5' . $sales_report_filter_link, 'NONSSL') . '">' . REPORT_TYPE_YEARLY . '</a>';
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td valign=top width=200 align=center>
                                <?php
                                if ($sales_report_view > 1) {
                                    if ($report->size > 1) {
                                        echo tep_draw_separator('pixel_trans.gif', 250, 10) . '<br>';
                                        $last_value = 0;
                                        $order_cnt = 0;
                                        $sum = 0;
                                        for ($i = 0; $i < $report->size; $i++) {
                                            if ($last_value != 0) {
                                                $percent = 100 * $report->info[$i]['sum'] / $last_value - 100;
                                            } else {
                                                $percent = "0";
                                            }
                                            $sum += $report->info[$i]['sum'];
                                            $avg += $report->info[$i]['avg'];
                                            $order_cnt += $report->info[$i]['count'];
                                            $last_value = $report->info[$i]['sum'];
                                        }
                                    }
                                    //define variables for graph
                                    if ($report->size > 1) {
                                        $scale_x = ($sum / $report->size) / 4;
                                        $scale_y = $scale_x + 50;
                                        $scale_z = $scale_y / 100;
                                        $scale = round($scale_z) * 100;
                                        ?>
                                        <SCRIPT LANGUAGE="JavaScript1.2">
                                            var g = new Graph(<?php
                                if ($report->size > 2) {
                                    echo '200';
                                } else {
                                    echo ($report->size * 50);
                                }
                                ?>, 100, true);
                                            g.addRow(<?php
                                for ($i = 0; $i < $report->size; $i++) {
                                    if ($report->info[$i]['sum'] == "") {
                                        echo '0';
                                    } else {
                                        echo $report->info[$i]['sum'] - $report->info[$i]['avg'];
                                    }
                                    if (($i + 1) < $report->size) {
                                        echo ',';
                                    }
                                }
                                echo ');';

                                if ($sales_report_view == 2) {
                                    echo 'g.addRow(';
                                    for ($i = 0; $i < $report->size; $i++) {
                                        if ($report->info[$i]['sum'] == "") {
                                            echo '0';
                                        } else {
                                            echo $report->info[$i]['avg'];
                                        }
                                        if (($i + 1) < $report->size) {
                                            echo ',';
                                        }
                                    }
                                    echo ');';
                                    echo 'g.setLegend("daily total","avg. order");';
                                }

                                echo 'g.setXScaleValues("';
                                for ($i = 0; $i < $report->size; $i++) {
                                    if (($sales_report_view == 5) && ($report->size > 5)) {
                                        echo substr($report->info[$i]['text'] . $date_text[$i], 0, 1);
                                    } else {
                                        if ($sales_report_view == 4) {
                                            echo substr($report->info[$i]['text'] . $date_text[$i], 0, 3);
                                        } else {
                                            if ($report->size > 5) {
                                                echo substr($report->info[$i]['text'] . $date_text[$i], 3, 2);
                                            } else {
                                                //echo ($report->info[$i]['text'] . $date_text[$i]);
                                                echo substr($report->info[$i]['text'] . $date_text[$i], 0, 5);
                                            }
                                        }
                                    }
                                    if (($i + 1) < $report->size) {
                                        echo '","';
                                    }
                                }
                                echo '");';
                                ?>
                                            g.scale = <?php echo $scale; ?>;
                                            g.build();
                                        </SCRIPT>
                                        <?
                                    }
                                }
                                ?>
                            </td>
                            <td width=100% valign=top>
                                <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                    <tr>
                                        <td valign="top">
                                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <tr class="dataTableHeadingRow">
                                                    <td class="dataTableHeadingContent"></td>
                                                    <td class="dataTableHeadingContent" align=center><?php echo TABLE_HEADING_ORDERS; ?></td>
                                                    <td class="dataTableHeadingContent" align=right><?php echo TABLE_HEADING_CONV_PER_ORDER; ?></td>
                                                    <td class="dataTableHeadingContent" align=right><?php echo TABLE_HEADING_CONVERSION; ?></td>
                                                    <td class="dataTableHeadingContent" align=right><?php echo TABLE_HEADING_VARIANCE; ?></td>
                                                </tr>
                                                <?
                                                $last_value = 0;
                                                $sum = 0;
                                                for ($i = 0; $i < $report->size; $i++) {
                                                    if ($last_value != 0) {
                                                        $percent = 100 * $report->info[$i]['sum'] / $last_value - 100;
                                                    } else {
                                                        $percent = "0";
                                                    }
                                                    $sum += $report->info[$i]['sum'];
                                                    $avg += $report->info[$i]['avg'];
                                                    $last_value = $report->info[$i]['sum'];
                                                    ?>
                                                    <tr class="dataTableRow" onmouseover="this.className = 'dataTableRowOver';this.style.cursor = 'hand'" onmouseout="this.className = 'dataTableRow'">
                                                        <td class="dataTableContent">
                                                            <?php
                                                            if (strlen($report->info[$i]['link']) > 0) {
                                                                echo '<a href="' . tep_href_link(FILENAME_STATS_SALES_REPORT, $report->info[$i]['link'] . $sales_report_filter_link, 'NONSSL') . '">';
                                                            }
                                                            echo $report->info[$i]['text'] . $date_text[$i];
                                                            if (strlen($report->info[$i]['link']) > 0) {
                                                                echo '</a>';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td class="dataTableContent" align=center><?= $report->info[$i]['count'] ?></td>
                                                        <td class="dataTableContent"align=right><?= $currencies->format($report->info[$i]['avg']) ?></td>
                                                        <td class="dataTableContent" align=right><?= $currencies->format($report->info[$i]['sum']) ?></td>
                                                        <td class="dataTableContent" align=right><?= ($percent == 0 ? "---" : number_format($percent, 0) . "%") ?></td>
                                                    </tr>
                                                    <?
                                                }

                                                if (strlen($report->previous . " " . $report->next) > 1) {
                                                    ?>
                                                    <tr>
                                                        <td width=100% colspan=5>
                                                            <table width=100%>
                                                                <tr>
                                                                    <td align=left>
                                                                        <?
                                                                        if (strlen($report->previous) > 0) {
                                                                            echo '<a href="' . tep_href_link(FILENAME_STATS_SALES_REPORT, $report->previous . $sales_report_filter_link, 'NONSSL') . '">&lt;&lt;&nbsp;Previous</a>';
                                                                        }
                                                                        ?>
                                                                    </td>
                                                                    <td align=right>
                                                                        <?
                                                                        if (strlen($report->next) > 0) {
                                                                            echo '<a href="' . tep_href_link(FILENAME_STATS_SALES_REPORT, $report->next . $sales_report_filter_link, 'NONSSL') . '">Next&nbsp;&gt;&gt;</a>';
                                                                            echo "";
                                                                        }
                                                                        ?>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                <? } ?>
                                            </table>
                                            <p>
                                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <? if ($order_cnt != 0) { ?>
                                                    <tr class="dataTableRow">
                                                        <td class="dataTableContent" width=100% align=right><?php echo '<b>' . AVERAGE_ORDER . ' </b>' ?></td>
                                                        <td class="dataTableContent" align=right><?php echo $currencies->format($sum / $order_cnt) ?></td>
                                                    </tr>
                                                    <?
                                                }
                                                if ($report->size != 0) {
                                                    ?>
                                                    <tr class="dataTableRow">
                                                        <td class="dataTableContent" width=100% align=right><?php echo '<b>' . $summary1 . ' </b>' ?></td>
                                                        <td class="dataTableContent" align="right" nowrap><?php echo $currencies->format($sum / $report->size) ?></td>
                                                    </tr>
                                                <? } ?>
                                                <tr class="dataTableRow">
                                                    <td class="dataTableContent" width=100% align=right><?php echo '<b>' . $summary2 . ' </b>' ?></td>
                                                    <td class="dataTableContent" align="right" nowrap><?php echo $currencies->format($sum) ?></td>
                                                </tr>
                                                <tr>
                                                    <td class="dataTableContent" colspan="2"><i>*GMV, sales based on order last verifying date</i></td>
                                                </tr>
                                            </table>

                                            <table border="0" width="100%" cellspacing="0" cellpadding="2">
                                                <tr><td colspan="2">&nbsp;</td></tr>
                                                <?
                                                echo tep_draw_form('sales_report_criteria', FILENAME_STATS_SALES_REPORT, tep_get_all_get_params(array('action', 'filter', 'filter2', 'filter3', 'order_site')) . 'action=do_filter', 'post', '');

                                                $order_site = array(
                                                    array('id' => '1', 'text' => 'OffGamers'),
                                                    array('id' => '2', 'text' => 'G2G')
                                                );

                                                if (($sales_report_filter) == 0) {
                                                    for ($i = 0; $i < $report->status_available_size; $i++) {
                                                        $sales_report_filter .= "0";
                                                    }
                                                }

                                                $order_status_display_str = '
		<table border="0" cellspacing="0" cellpadding="2">
			<tr>';
                                                for ($i = 0; $i < $report->status_available_size; $i++) {
                                                    $id = $report->status_available[$i]['index'];
                                                    $title = $report->status_available[$i]['value'];

                                                    $status_selected = (substr($sales_report_filter, $i, 1) == "0" ? true : false);

                                                    $order_status_display_str .= '
				<td class="main" valign="top">' .
                                                            tep_draw_checkbox_field('order_status[]', $id, $status_selected, '', 'onClick="verify_status_selection(this);"') . '
				</td>
					<td class="main" valign="top">';

                                                    if ($id == "2" || $id == "3") {
                                                        $order_status_display_str .= '
						<fieldset class="selectedFieldSet">
							<legend align=left class=SectionHead>' . $title . '</legend>
							<table border="0" cellspacing="0" cellpadding="0">';
                                                        if (isset($report->orders_amt_breakdown_array[$id]) && count($report->orders_amt_breakdown_array[$id])) {
                                                            foreach ($report->orders_amt_breakdown_array[$id] as $sub_status_id => $sub_status_info) {
                                                                $sub_status_selected = (substr(${'sales_report_filter' . $id}, $sub_status_id, 1) === "0" && $status_selected ? true : false);
                                                                $order_status_display_str .= '<tr><td class="smallText" valign="top">' . tep_draw_checkbox_field('order_sub_status_' . $id . '[]', (string) $sub_status_id, $sub_status_selected, '', $status_selected ? '' : 'disabled') . '</td><td class="smallText">' . $sub_status_info['text'] . '</td></tr>';
                                                            }
                                                        }

                                                        $order_status_display_str .= '
							</table>
						</fieldset>';
                                                    } else {
                                                        $order_status_display_str .= $title;
                                                    }
                                                    $order_status_display_str .= '</td>';
                                                }
                                                $order_status_display_str .= '
			</tr>
		</table>';
                                                ?>
                                                <tr>
                                                    <td class="main"><b><?= FILTER_ORDER ?></b></td>
                                                    <td class="main">
                                                        <?= tep_draw_pull_down_menu('order_site', $order_site, $orders_default_site, 'id="order_site"') ?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="main" valign="top"><b><?= FILTER_STATUS ?></b></td>
                                                    <td class="main"><?= $order_status_display_str ?></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <table border="0" width="100%" cellspacing="0" cellpadding="0">
                                                            <tr>
                                                                <td align="right">
                                                                    <?= tep_submit_button(BUTTON_SEARCH, ALT_BUTTON_SEARCH, '', 'inputButton') ?>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                </form>
                                            </table>
                                            <script language="javascript"><!--
                                                    function verify_status_selection(status_obj) {
                                                    if (status_obj != null) {
                                                        var cur_status_id = status_obj.value;
                                                        var disabled_mode = status_obj.checked ? false : true;

                                                        if (cur_status_id == '2' || cur_status_id == '3') {
                                                            var sub_status_select = document.sales_report_criteria.elements['order_sub_status_' + cur_status_id + '[]'];

                                                            if (typeof (sub_status_select) != 'undefined') {
                                                                if (typeof (sub_status_select.length) != 'undefined') {
                                                                    for (sub_cnt = 0; sub_cnt < sub_status_select.length; sub_cnt++) {
                                                                        sub_status_select[sub_cnt].disabled = disabled_mode;
                                                                        sub_status_select[sub_cnt].checked = !disabled_mode;
                                                                    }
                                                                } else {
                                                                    sub_status_select.disabled = disabled_mode;
                                                                    sub_status_select.checked = !disabled_mode;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                //-->
                                            </script>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
                <!-- body_text_eof //-->
            </tr>
        </table>
        <!-- body_eof //-->
        <!-- footer //-->
        <?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <!-- footer_eof //-->
    </body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>