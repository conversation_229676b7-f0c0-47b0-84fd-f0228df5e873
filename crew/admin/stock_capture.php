<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

$stock_capture_datetime = date("Y-m-d H:i:s"); // Set the time for this stock history records
$stock_capture_date = date("Y-m-d"); // Set the time for this stock history records
$first_day_of_month = date("j") == '1' ? true : false;

$stock_captured_checking_select_sql = "	SELECT stock_history_date 
					FROM " . TABLE_STOCK_HISTORY . "
                                        WHERE stock_history_date >= DATE_FORMAT('" . $stock_capture_date . "', '%Y-%m-%d 00:00:00') 
                                            AND stock_history_date <= DATE_FORMAT('" . $stock_capture_date . "', '%Y-%m-%d 23:59:59') 
                                        LIMIT 1";
$stock_captured_checking_result_sql = tep_db_query($stock_captured_checking_select_sql);

if (!tep_db_num_rows($stock_captured_checking_result_sql)) {
    
    // Begin fix products_cat_id_path & products_main_cat_id
    $product_select_sql = "	SELECT p.products_id, c.categories_parent_path, c.categories_id, p.products_cat_id_path, p.products_main_cat_id 
                                FROM products as p
                                INNER JOIN products_to_categories as ptc
                                        ON ptc.products_id = p.products_id
                                INNER JOIN categories as c
                                        ON c.categories_id = ptc.categories_id
                                WHERE ptc.products_is_link = '0'";
    $product_result_sql = tep_db_query($product_select_sql);
    while ($product_row = tep_db_fetch_array($product_result_sql)) {
        $path_str = $product_row['categories_parent_path'];
        if (tep_not_null($path_str)) {
            $path_str .= $product_row['categories_id'] . "_";
            $parent_path_array = explode("_", $product_row['categories_parent_path']);
            $top_parent_id = $parent_path_array[1];
        } else {
            $path_str = "_" . $product_row['categories_id'] . "_";
            $top_parent_id = (int) $product_row['categories_id'];
        }
        if ($top_parent_id != $product_row['products_main_cat_id']) {
            $update_product_data_sql = array('products_main_cat_id' => $top_parent_id);
            tep_db_perform('products', $update_product_data_sql, 'update', " products_id = '" . $product_row['products_id'] . "' ");

            echo $product_row['products_id'] . ' => ID -- ' . $top_parent_id . ' != ' . $product_row['products_main_cat_id'] . "<BR>";
        }

        if ($path_str != $product_row['products_cat_id_path']) {
            $update_product_data_sql = array('products_cat_id_path' => $path_str);
            tep_db_perform('products', $update_product_data_sql, 'update', " products_id = '" . $product_row['products_id'] . "' ");

            echo $product_row['products_id'] . ' => Path ' . $path_str . ' != ' . $product_row['products_cat_id_path'] . "<BR>";
        }
    }
    // End of fix products_cat_id_path & products_main_cat_id

    $product_id_array = array();
    $stock_history_select_sql = "SELECT " . TABLE_PRODUCTS . ".products_id, products_quantity, products_actual_quantity, products_quantity_fifo_cost, products_actual_quantity_fifo_cost, 
                                        products_cat_path, custom_products_type_id, products_name 
                                FROM " . TABLE_PRODUCTS . " 
                                LEFT JOIN " . TABLE_PRODUCTS_DESCRIPTION . " 
                                        ON " . TABLE_PRODUCTS . ".products_id=" . TABLE_PRODUCTS_DESCRIPTION . ".products_id
                                WHERE products_skip_inventory=0 
                                        AND products_bundle='' 
                                        AND products_bundle_dynamic='' 
                                        AND language_id='1' 
                                 ORDER BY products_cat_path, products_name, products_sort_order";

    $stock_history_result_sql = tep_db_query($stock_history_select_sql);
    while ($stock_history_row = tep_db_fetch_array($stock_history_result_sql)) {
        $stock_history_data_array = array(
            'stock_history_date' => $stock_capture_datetime,
            'products_id' => $stock_history_row['products_id'],
            'products_cat_path' => $stock_history_row['products_cat_path'] . ' > ' . $stock_history_row['products_name'],
            'products_quantity' => $stock_history_row['products_quantity'],
            'products_actual_quantity' => $stock_history_row['products_actual_quantity'],
            'products_quantity_fifo_cost' => $stock_history_row['products_quantity_fifo_cost'],
            'products_actual_quantity_fifo_cost' => $stock_history_row['products_actual_quantity_fifo_cost']
        );
        tep_db_perform(TABLE_STOCK_HISTORY, $stock_history_data_array);
        if ($stock_history_row['custom_products_type_id'] == 0) {
            $product_id_array[] = $stock_history_row['products_id'];
        }
    }

    // Begin grab the supplier's stock left quantity
    if (count($product_id_array) > 0) {
        for ($prod_cnt = 0; $prod_cnt < count($product_id_array); $prod_cnt++) {
            $select_stock_history = "SELECT customers_id, vip_supplier_inventory_qty FROM " . TABLE_VIP_SUPPLIER_INVENTORY . " WHERE products_id='" . $product_id_array[$prod_cnt] . "'";
            $select_stock_history_result = tep_db_query($select_stock_history);
            while ($select_stock_history_row = tep_db_fetch_array($select_stock_history_result)) {

                $stock_history_array = array('customers_id' => $select_stock_history_row['customers_id'],
                    'products_id' => $product_id_array[$prod_cnt],
                    'vip_stock_history_date' => $stock_capture_date,
                    'vip_stock_history_qty' => $select_stock_history_row['vip_supplier_inventory_qty']
                );
                tep_db_perform(TABLE_VIP_STOCK_HISTORY, $stock_history_array);
            }
        }
    }
}
?>