<?
/*
  	$Id: stock_report.php,v 1.41 2011/07/13 02:45:49 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'stock_report.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_FUNCTIONS . 'supplier.php');

tep_set_time_limit(400);

$currencies = new currencies();

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
	switch ($action) {
        case 'reset_session':
        	unset($_SESSION['stock_param']);
        	tep_redirect(tep_href_link(FILENAME_STOCK_REPORT));
        	break;
        case 'show_report':
        	// Connect to Reporting DB
			$read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');
			
        	if ($_REQUEST['cont'] && !isset($_SESSION['ori_stock_select_sql'])) {
				tep_redirect(tep_href_link(FILENAME_STOCK_REPORT));
			} else if (!$_REQUEST['cont'] && !isset($_REQUEST["cat_id"])) {
				tep_redirect(tep_href_link(FILENAME_STOCK_REPORT));
			}
        	break;
        case 'export_report':
			// Connect to Reporting DB
			$read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');
        
        	$export_csv_data = '';
        	$export_csv_array = array();
        	
        	if (!$_REQUEST['cont']) {
				unset($_SESSION['ori_stock_select_sql']);
				
				$_SESSION['stock_param']["report"] = $_REQUEST['report'];
				$_SESSION['stock_param']['start_date'] = $_REQUEST["start_date"];
				$_SESSION['stock_param']['end_date'] = $_REQUEST["end_date"];
				$_SESSION['stock_param']["cat_id"] = $_REQUEST["cat_id"];
				$_SESSION['stock_param']["include_subcategory"] = $_REQUEST["include_subcategory"];
				$_SESSION['stock_param']["products_status"] = $_REQUEST["products_status"];
				$_SESSION['stock_param']["show_all"] = $_REQUEST["show_all"];
				$_SESSION['stock_param']["stock_operator"] = $_REQUEST["stock_operator"];
				$_SESSION['stock_param']["stock_level"] = $_REQUEST["stock_level"];
				$_SESSION['stock_param']["use_product_reorder"] = $_REQUEST["use_product_reorder"];
				$_SESSION['stock_param']["stock_qty_reference"] = $_REQUEST["stock_qty_reference"];
				$_SESSION['stock_param']["products_qty_type"] = $_REQUEST["products_qty_type"];
				$_SESSION['stock_param']["sort_by"] = $_REQUEST["sort_by"];
				$_SESSION['stock_param']["sort_order"] = $_REQUEST["sort_order"];
				$_SESSION['stock_param']["show_records"] = $_REQUEST["show_records"];
				$_SESSION['stock_param']["category_id_to"] = $_REQUEST["_dualselect_sel_category_id"];

		  		$status_array = array();
		  		$status_str = "1";
		  		
		  		if ($_REQUEST["products_status"] && is_array($_REQUEST["products_status"])) {
		  			foreach ($_REQUEST["products_status"] as $status_val) {
		  				$status_array[] = "p.products_status=$status_val";
		  			}
		  			$status_str = (count($status_array)) ? "(".implode(" OR ", $status_array).")" : " 1 ";
		  		}
				
		  		if ($_REQUEST["show_all"]) {
		  			$quantity_str = "1";
		  		} else {
		  			$stock_compare_op = $_REQUEST["stock_operator"] == '>=' ? '>=' : '<=';
		  			$stock_compare_to_field = tep_not_null($_REQUEST["stock_qty_reference"]) ? $_REQUEST["stock_qty_reference"] : 'p.products_quantity';
		  			$quantity_str = $stock_compare_to_field . ' ' . $stock_compare_op . ' ' . (isset($_REQUEST["stock_level"]) ? (int)$_REQUEST["stock_level"] : "IF (p.products_quantity_order IS NULL, " . STOCK_REORDER_LEVEL . ", p.products_quantity_order)");
		  		}
				
				$report = new stock_report($_REQUEST["report"], $_REQUEST["start_date"], $_REQUEST["end_date"], $_REQUEST["products_qty_type"]);
				
				if ($_REQUEST["report"] == 'real_time') {
					$report->sql_filter = " $status_str and $quantity_str ";
				}
				
				if ($_REQUEST["sort_by"] == "products_quantity" || $_REQUEST["sort_by"] == "products_actual_quantity") {
					$report->set_sorting_info(array( array($_REQUEST["sort_by"], $_REQUEST["sort_order"]), array("pd.products_name", 'ASC') ));
		  		} else {
		  			$report->set_sorting_info(array( array($_REQUEST["sort_by"], $_REQUEST["sort_order"]), array("p.products_sort_order", 'ASC'), array("pd.products_name", 'ASC') ));
		  		}
		  		
		 		if (isset($_REQUEST["cat_id"]) && tep_not_null($_REQUEST["cat_id"])){ 
					$report->set_main_category_id($_REQUEST["cat_id"], $_REQUEST["include_subcategory"]);
		  		} else if (isset($_REQUEST["_dualselect_sel_category_id"]) && tep_not_null($_REQUEST["_dualselect_sel_category_id"])){
					$report->set_main_category_id_mov($_REQUEST["_dualselect_sel_category_id"], 1);
		  		}
	  			
		  		$report->set_products_qty_type($_REQUEST["products_qty_type"]);
				$report->reportQuery();
  			}
  			
			switch (isset($_REQUEST["report"])) {
				case 'real_time':
					$report->displayCatTree($currencies, 'ALL');
					
					$export_csv_array = array_merge($report->report_header, $report->csv_data);
					break;
					
				case 'movement':
					$stock_select_sql = $_SESSION['ori_stock_select_sql'];
				  			
					$report->displayCatTree($currencies);
					
					if (count($report->info) > 0) {
						$export_csv_array = array_merge($report->report_header, $report->csv_data);
					}
					break;
					
				case 'outstanding':
					$stock_select_sql = $_SESSION['ori_stock_select_sql'];
					
					$report->displayCatTree($currencies);
					
					if (count($report->info) > 0) {
						$export_csv_array = array_merge($report->report_header, $report->csv_data);
					}
					break;
			}
    		
    		if (count($export_csv_array)) {
				foreach ($export_csv_array as $res) {
					$tmp_cvs_data_array = array();
					for ($i=0; $i < count($res); $i++) {
						$tmp_cvs_data_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res[$i]) . '"';
					}
					$export_csv_data .= implode(',', $tmp_cvs_data_array) . "\n";
				}
			}
			unset($export_csv_array);
			
    		if (tep_not_null($export_csv_data)) {
				$filename = $_REQUEST["report"].'_'.date('YmdHis').'.csv';
				$mime_type = 'text/x-csv';
				// Download
		        header('Content-Type: ' . $mime_type);
		        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
		        // IE need specific headers
		        if (PMA_USR_BROWSER_AGENT == 'IE') {
		            header('Content-Disposition: inline; filename="' . $filename . '"');
		            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		            header('Pragma: public');
		        } else {
		            header('Content-Disposition: attachment; filename="' . $filename . '"');
		            header('Pragma: no-cache');
		        }
				echo $export_csv_data;
				exit();
				
			} else {
				$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
				tep_redirect(tep_href_link(FILENAME_STOCK_REPORT));
			}
			
    		break;
	}
}
	
$view_outstanding_report_permission = tep_admin_files_actions(FILENAME_STOCK_REPORT, 'VIEW_OUTSTANDING_REPORT');
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css?v=<?php echo filemtime('includes/stylesheet.css'); ?>">
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<link rel="stylesheet" type="text/css" href="includes/javascript/select2/css/select2.min.css">
	<link rel="stylesheet" href="includes/javascript/multi-dual-select/css/dualselect-1.0.min.css" />
	
	<script language="javascript" src="includes/javascript/jquery/1.11.0/jquery.min.js"></script>
	<script language="javascript" src="includes/javascript/jquery/migrate/jquery-migrate-1.4.1.min.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="javascript" src="includes/javascript/select2/js/select2.min.js"></script>
	<script language="javascript" src="includes/javascript/multi-dual-select/js/dualselect-1.0.min.js"></script>

	<style>
		.select2-container {
			min-width: 17em;
		}
	</style>
	<script>
		$(document).ready(function(){
			$('[name="cat_id"]').select2();
			
			$('[name="stock_qty_reference"]').select2({
				minimumResultsForSearch: -1
			});
			
			$('[name="report"]').select2({
				minimumResultsForSearch: -1
			});
			
			$('[name="sort_by"]').select2({
				minimumResultsForSearch: -1
			});

			const dualselect1 = jQuery('#category_id').dualselect({
				moveOnSelect:false
			});
		});
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ($_REQUEST['action'] == 'show_report') {

	if (!$_REQUEST['cont']) {
		unset($_SESSION['ori_stock_select_sql']);
		
		$_SESSION['stock_param']["report"] = $_REQUEST['report'];
		$_SESSION['stock_param']['start_date'] = $_REQUEST["start_date"];
		$_SESSION['stock_param']['end_date'] = $_REQUEST["end_date"];
		$_SESSION['stock_param']["cat_id"] = $_REQUEST["cat_id"];
		$_SESSION['stock_param']["include_subcategory"] = $_REQUEST["include_subcategory"];
		$_SESSION['stock_param']["products_status"] = $_REQUEST["products_status"];
		$_SESSION['stock_param']["show_all"] = $_REQUEST["show_all"];
		$_SESSION['stock_param']["stock_operator"] = $_REQUEST["stock_operator"];
		$_SESSION['stock_param']["stock_level"] = $_REQUEST["stock_level"];
		$_SESSION['stock_param']["use_product_reorder"] = $_REQUEST["use_product_reorder"];
		$_SESSION['stock_param']["stock_qty_reference"] = $_REQUEST["stock_qty_reference"];
		$_SESSION['stock_param']["products_qty_type"] = $_REQUEST["products_qty_type"];
		$_SESSION['stock_param']["sort_by"] = $_REQUEST["sort_by"];
		$_SESSION['stock_param']["sort_order"] = $_REQUEST["sort_order"];
		$_SESSION['stock_param']["show_records"] = $_REQUEST["show_records"];
		$_SESSION['stock_param']["category_id_to"] = $_REQUEST["_dualselect_sel_category_id"];

  		$status_array = array();
  		$status_str = "1";
  		if ($_REQUEST["products_status"] && is_array($_REQUEST["products_status"])) {
  			foreach ($_REQUEST["products_status"] as $status_val) {
  				$status_array[] = "p.products_status=$status_val";
  			}
  			$status_str = (count($status_array)) ? "(".implode(" OR ", $status_array).")" : " 1 ";
  		}
  		
  		if ($_REQUEST["show_all"]) {
  			$quantity_str = "1";
  		} else {
  			$stock_compare_op = $_REQUEST["stock_operator"] == '>=' ? '>=' : '<=';
  			$stock_compare_to_field = tep_not_null($_REQUEST["stock_qty_reference"]) ? $_REQUEST["stock_qty_reference"] : 'p.products_quantity';
  			$quantity_str = $stock_compare_to_field . ' ' . $stock_compare_op . ' ' . (isset($_REQUEST["stock_level"]) ? (int)$_REQUEST["stock_level"] : "IF (p.products_quantity_order IS NULL, " . STOCK_REORDER_LEVEL . ", p.products_quantity_order)");
  		}
  		
  		$report = new stock_report($_REQUEST['report'], $_REQUEST["start_date"], $_REQUEST["end_date"], $_REQUEST["products_qty_type"]);
		
		if ($_REQUEST["report"] == 'real_time') {
			$report->sql_filter = " $status_str and $quantity_str ";
		}
		
		if ($_REQUEST["sort_by"] == "products_quantity" || $_REQUEST["sort_by"] == "products_actual_quantity") {
			$report->set_sorting_info(array( array($_REQUEST["sort_by"], $_REQUEST["sort_order"]), array("pd.products_name", 'ASC') ));
  		} else {
  			$report->set_sorting_info(array( array($_REQUEST["sort_by"], $_REQUEST["sort_order"]), array("p.products_sort_order", 'ASC'), array("pd.products_name", 'ASC') ));
  		}
		
		if (isset($_REQUEST["cat_id"]) && tep_not_null($_REQUEST["cat_id"])){ 
  			$report->set_main_category_id($_REQUEST["cat_id"], $_REQUEST["include_subcategory"]);
  		} else if (isset($_REQUEST["_dualselect_sel_category_id"]) && tep_not_null($_REQUEST["_dualselect_sel_category_id"])){
  			$report->set_main_category_id_mov($_REQUEST["_dualselect_sel_category_id"], 1);
  		}
  		
  		$report->set_products_qty_type($_REQUEST["products_qty_type"]);
  		
		$report->reportQuery();
		$report->getSummaryText();
  	}
  	
  	$stock_select_sql = $_SESSION['ori_stock_select_sql'];
?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
			        				<td>
			        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
			          						<tr>
			            						<td class="pageHeading" valign="top"><?=is_object($report) ? $report->summary["report_title"] : HEADING_TITLE?></td>
			            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
			          						</tr>
			        					</table>
			        				</td>
			      				</tr>

<?
	switch($_SESSION['stock_param']["report"]) {
		case "real_time":
		
			$report = new stock_report($_SESSION['stock_param']["report"]);
			echo $report->displayCatTree($currencies, $_REQUEST["show_records"]);
			break;
		case "movement":
		case "outstanding":
?>
								<tr>
									<td valign="top"><?=$report->displayCatTree($currencies)?></td>
            					</tr>
<?
			break;
	}
?>
			   				</table>
			   			</td>
			   		</tr>
<?	if (is_object($report)) { ?>
			   		<tr>
						<td><?=$report->display_report_legend()?></td>
					</tr>
<?	}
} else {
	$read_db_link = tep_db_connect(DB_REPORT_SERVER, DB_REPORT_SERVER_USERNAME, DB_REPORT_SERVER_PASSWORD, DB_REPORT_DATABASE, 'read_db_link') or die('Unable to connect to database server!');	
	
	unset($_SESSION['ori_stock_select_sql']);
?>
			  		<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
        				<td>
        					<?=tep_draw_form('stock_report_criteria', FILENAME_STOCK_REPORT, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', ' id="stock_report_criteria" ');?>
        					<?=tep_draw_hidden_field('action', '', ' id="action" ')?>
        					<table border="0" width="100%" cellspacing="2" cellpadding="0">
<?
	$category_list_array = array();
    $cache_key = TABLE_CATEGORIES . '/category_list/array/parent_id/0/language/1/stock_report_category_from_list';
    
	$cache_result = $memcache_obj->fetch($cache_key);
	
	if ($cache_result !== FALSE) {
		$category_list_array = $cache_result;
	} else {
		$categories_id = array(); 
		
		$categories_select_sql = "	SELECT c.categories_id, cd.categories_name
									FROM " . TABLE_CATEGORIES . " AS c
									LEFT JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
										ON c.categories_id = cd.categories_id
									WHERE c.parent_id = '0' 
										AND cd.categories_name <> '' 
										AND cd.language_id='1'
									ORDER BY cd.categories_name ASC";
		$categories_result_sql = tep_db_query($categories_select_sql, 'read_db_link');
		while($categories_row = tep_db_fetch_array($categories_result_sql) ){
			
			$categories_id = tep_check_cat_tree_permissions(FILENAME_STOCK_REPORT, $categories_row['categories_id']);
			
			if ($categories_id == 1){
				$category_list_array[] = array(	'id' => $categories_row['categories_id'],
												'text' => $categories_row['categories_name']);
			}
		}
		
		$memcache_obj->store($cache_key, $category_list_array, 7200);
	}
	
	$categories_list_from_array  = array( array ('id' => '', "text" => 'Select Game', "type" => 'optgroup'));
	$categories_list_to_array  = array( array ('id' => '', "text" => 'Selected Game', "type" => 'optgroup'));
	
	if (isset($_SESSION['stock_param']["category_id_to"]) && count($_SESSION['stock_param']["category_id_to"])) {
		for ($i=0; $i < count($category_list_array); $i++) {
			if (in_array($category_list_array[$i]['id'], $_SESSION['stock_param']["category_id_to"])) {
				$categories_list_to_array[] = array('id' => $category_list_array[$i]['id'],
													'text' => $category_list_array[$i]['text']);
			} else {
				$categories_list_from_array[] = array(	'id' => $category_list_array[$i]['id'],
														'text' => $category_list_array[$i]['text']);
			}
		}
	} else {
		if (is_array($category_list_array) && count($category_list_array)) {
			$categories_list_from_array = array_merge($categories_list_from_array, $category_list_array);
		}
	}
	
	$categories_array = tep_get_eligible_category_tree_cacheable(FILENAME_STOCK_REPORT, 0, '___', '', $categories_array, false, 0, true);
	
	$sort_by_array = array	( 	array ('id' => 'products_cat_path', "text" => "Categories"),
								array ('id' => 'products_quantity', "text" => "Product Available Quantity"),
								array ('id' => 'products_actual_quantity', "text" => "Product Actual Quantity")
						  	);
	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
								array ('id' => '10', "text" => "10"),
								array ('id' => '20', "text" => "20"),
								array ('id' => '50', "text" => "50"),
								array ('id' => 'ALL', "text" => "All")
							);
	$stock_operator_options = array	(	array ('id' => '&lt;=', "text" => "&le;"),
		  								array ('id' => '&gt;=', "text" => "&ge;")
		  							);
	$stock_reference_array = array	( 	array ('id' => 'p.products_quantity', "text" => "Product Available Quantity"),
										array ('id' => 'p.products_actual_quantity', "text" => "Product Actual Quantity")
						  			);
	$report_type_array = array	( 	array ('id' => 'real_time', "text" => REPORT_TYPE_REAL_TIME_STOCK),
  									array ('id' => 'movement', "text" => REPORT_TYPE_STOCK_MOVEMENT)
						  		);
	if ($view_outstanding_report_permission) {
		$report_type_array[] = array ('id' => 'outstanding', "text" => REPORT_TYPE_OUTSTANDING_PAYMENT);
	}
?>
								<tr>
									<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
          							<td class="main" width="15%"><?=ENTRY_REPORT_TYPE?></td>
          							<td class="main">
          								<?=tep_draw_pull_down_menu("report", $report_type_array, $_SESSION['stock_param']["report"], ' id="report" onChange="swap_report_type_fields()"')?>
					    			</td>
          						</tr>
          						<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
      							<tbody id="stock_date_section" class="show">
      								<tr>
	            						<td class="main" width="12%">Start Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small></td>
	            						<td class="main" align="left"><?=tep_draw_input_field('start_date', $_SESSION['stock_param']["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.stock_report_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.stock_report_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
	          						</tr>
	          						<tr>
	            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
									<tr>
	            						<td class="main" width="12%">End Date<br><small>(YYYY-MM-DD /YYYY-MM-DD HH:MM)</small></td>
	            						<td class="main" align="left"><?=tep_draw_input_field('end_date', $_SESSION['stock_param']["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.stock_report_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.stock_report_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
	          						</tr>
	          						<tr>
	            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          					</tbody>
								
	          					<tbody id="cat_section_boxes" class="show">
									<tr>
										<td class="main" valign="top"><?=ENTRY_HEADING_CATEGORY?></td>
						    			<td class="main">
										<?=tep_draw_pull_down_menu('category_id[]', array_merge($categories_list_from_array, $categories_list_to_array), (is_array($categories_list_to_array) && count($categories_list_to_array)) ? $categories_list_to_array : '', ' id="category_id" multiple size=10'); ?>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
		          					<tr>
		          					<td></td>
	            						<td align="left"><?="or"?></td>
	          						</tr>
	          						<tr>
	            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          					</tbody>
	          					        
	          					<tbody id="cat_section" class="show">
									<tr>
										<td class="main"></td> 
						    			<td class="main">
						    				<?=tep_draw_pull_down_menu("cat_id", $categories_array, $_SESSION['stock_param']["cat_id"], ' id="cat_id" OnChange="resetControls();"')?>
						    				<?=tep_draw_checkbox_field("include_subcategory", 1, (isset($_SESSION['stock_param']) ? ($_SESSION['stock_param']["include_subcategory"] ? true : false) : true), "", ' id="include_subcategory" ') . '&nbsp;' . TEXT_INCLUDE_SUBCATEGORY?>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          					</tbody>
          						<tbody id="prod_status_sec" class="show">
	          						<tr>
										<td class="main"><?=ENTRY_HEADING_PRODUCT_STATUS?></td>
						    			<td class="main">
						    				<?=tep_draw_checkbox_field('products_status[]', '1', isset($_SESSION['stock_param']) ? (is_array($_SESSION['stock_param']["products_status"]) && in_array("1", $_SESSION['stock_param']["products_status"]) ? true : false) : true, '', 'id="status_active"') . "&nbsp;" . TEXT_STATUS_ACTIVE . "&nbsp;" . tep_draw_checkbox_field('products_status[]', '0', isset($_SESSION['stock_param']) ? (is_array($_SESSION['stock_param']["products_status"]) && in_array("0", $_SESSION['stock_param']["products_status"]) ? true : false) : false, '', 'id="status_inactive"') . "&nbsp;" . TEXT_STATUS_INACTIVE?>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          					</tbody>
	          					<tbody id="all_stock_sec" class="show">
	          						<tr>
										<td class="main"><?=ENTRY_HEADING_ALL_ITEMS?></td>
						    			<td class="main"><?=tep_draw_radio_field('show_all', '1', isset($_SESSION['stock_param']) ? ($_SESSION['stock_param']["show_all"] ? true : false) : false, '', 'onClick="swap_fields(0)"') . "&nbsp;" . 'Yes' . "&nbsp;" . tep_draw_radio_field('show_all', '0', isset($_SESSION['stock_param']) ? ($_SESSION['stock_param']["show_all"] ? false : true) : true, '', 'onClick="swap_fields(1)"') . "&nbsp;" . 'No'?></td>
									</tr>
									<tr>
	            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
          						</tbody>
          						<tbody id="stock_level_section" class="hide">
	          						<tr>
										<td class="main" id="stock_level_head"><?=ENTRY_HEADING_STOCK_LEVEL?></td>
						    			<td class="main" id="stock_level_field">
						    				<table border="0" cellspacing="0" cellpadding="2">
						    					<td class="main" valign="top">
								    			<?
								    				ob_start();
								    				$default_stock_level = tep_not_null($_SESSION['stock_param']["stock_level"]) ? $_SESSION['stock_param']["stock_level"] : STOCK_REORDER_LEVEL;
								    				echo tep_draw_pull_down_menu("stock_operator", $stock_operator_options, tep_not_null($_SESSION['stock_param']["stock_operator"]) ? htmlentities($_SESSION['stock_param']["stock_operator"]) : '', '');
								    				echo tep_draw_input_field('stock_level', $default_stock_level, ' id="stock_level" SIZE="6" ');
								    				echo tep_draw_hidden_field("stock_level_old", $default_stock_level, ' id="stock_level_old" ');
								    				echo "&nbsp;" . tep_draw_checkbox_field("use_product_reorder", 1, isset($_SESSION['stock_param']) ? ($_SESSION['stock_param']["use_product_reorder"] ? true : false) : false, '', " id='use_product_reorder' onclick=\"resetProductReorder();\"") . "&nbsp;" . TEXT_USE_PRODUCT_REORDER;
								    				$stock_level_contents = ob_get_contents();
													ob_end_clean();
													echo $stock_level_contents;
								    			?>
						    					</td>
						    					<td class="main">&nbsp;&nbsp;</td>
						    					<td class="main">Refering To</td>
						    					<td class="main"><?=tep_draw_pull_down_menu("stock_qty_reference", $stock_reference_array, tep_not_null($_SESSION['stock_param']["stock_qty_reference"]) ? htmlentities($_SESSION['stock_param']["stock_qty_reference"]) : '', '')?></td>
						    				</table>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="2"><div id="stock_level_separator"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></div></td>
	          						</tr>
          						</tbody>
          						<tbody id="prod_qty_section" class="show"> 
          						<tr>
									<td class="main"><?=ENTRY_PRODUCTS_QUANTITY?></td>
					    			<td class="main"><?=tep_draw_checkbox_field('products_qty_type[]', 'available_qty', isset($_SESSION['stock_param']) ? (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("available_qty", $_SESSION['stock_param']["products_qty_type"]) ? true : false) : true, '', 'id="available_qty_box"') . "&nbsp;" . TEXT_AVAILABLE_QTY . "&nbsp;" . tep_draw_checkbox_field('products_qty_type[]', 'actual_qty', isset($_SESSION['stock_param']) ? (is_array($_SESSION['stock_param']["products_qty_type"]) && in_array("actual_qty", $_SESSION['stock_param']["products_qty_type"]) ? true : false) : false, '', 'id="actual_qty_box"') . "&nbsp;" . TEXT_ACTUAL_QTY?></td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						</tbody>
          						<tbody id="report_result_section" class="show"> 
	          						<tr>
										<td class="main"><?=ENTRY_HEADING_SORT?></td>
						    			<td class="main">
						    				<?=tep_draw_pull_down_menu("sort_by", $sort_by_array, tep_not_null($_SESSION['stock_param']["sort_by"]) ? $_SESSION['stock_param']["sort_by"] : 'products_cat_path', '')?>
						    				<?=tep_draw_radio_field('sort_order', 'ASC', isset($_SESSION['stock_param']) ? ($_SESSION['stock_param']["sort_order"]=='ASC' ? "checked" : '') : "checked") . "&nbsp;" . TEXT_ASC . "&nbsp;" . tep_draw_radio_field('sort_order', 'DESC', isset($_SESSION['stock_param']) ? ($_SESSION['stock_param']["sort_order"]=='DESC' ? "checked" : "") : "") . "&nbsp;" . TEXT_DESC?>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
	          						<tr>
										<td class="main"><?=ENTRY_HEADING_RECORDS_PER_PAGE?></td>
						    			<td class="main">
						    				<?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION['stock_param']["show_records"]) ? $_SESSION['stock_param']["show_records"] : '', '')?>
						    			</td>
									</tr>
									<tr>
	            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	          						</tr>
          						</tbody>
          						<tr>
	  								<td width="20%">&nbsp;</td>
	  								<td align="right">
	  									<input type="button" name="export" value="<?=BUTTON_STOCK_REPORT_EXPORT_REPORT?>" class="inputButton" onClick="return form_checking(this.form, 'export_report');">&nbsp;&nbsp;
	  									<input type="button" name="search" value="<?=BUTTON_STOCK_REPORT_SHOW_REPORT?>" class="inputButton" onClick="return form_checking(this.form, 'show_report');">&nbsp;&nbsp;
										<input type="button" name="reset" value="<?=BUTTON_RESET?>" class="inputButton" onClick="document.location.href='<?=tep_href_link(FILENAME_STOCK_REPORT, 'action=reset_session')?>'">
									</td>
	  							</tr>
          					</table>
          					</form>
						</td>
					</tr>
			        
					<script language="javascript"><!--
						function form_checking(form_obj, form_action) {
							switch(DOMCall('report').value) {
								case "real_time":
								
									var num_category = document.stock_report_criteria.elements['_dualselect_sel_category_id[]'];
									
									if (document.getElementById('cat_id').value == ''&& num_category.length == 0) {
										alert('<?=JS_STOCK_REPORT_CATEGORY?>');
										document.getElementById('cat_id').focus();
										return false;
									}
									
									if (!document.getElementById('status_active').checked && !document.getElementById('status_inactive').checked) {
										alert('<?=JS_STOCK_REPORT_PRODUCTS_STATUS?>');
										return false;
									}
									
									if (document.stock_report_criteria.show_all[1].checked) {
										if (!document.getElementById('use_product_reorder').checked && !validateSignInteger(document.getElementById('stock_level').value)) {
											alert('<?=JS_STOCK_REPORT_STOCK_LEVEL?>');
											document.getElementById('stock_level').focus();
											document.getElementById('stock_level').select();
											return false;
										}
									}
									
									if (!document.getElementById('available_qty_box').checked && !document.getElementById('actual_qty_box').checked) {
										alert('<?=JS_STOCK_REPORT_PRODUCTS_QUANTITY?>');
										return false;
									}
									break;
									
								case "movement":
									if (!DOMCall('start_date').value){
										alert('<?=JS_STOCK_REPORT_START_DATE?>');
										return false;
									}
									
									if (!DOMCall('end_date').value){
										alert('<?=JS_STOCK_REPORT_END_DATE?>');
										return false;
									}
									
									if (DOMCall('start_date').value > DOMCall('end_date').value) {
										alert('<?=JS_STOCK_REPORT_GREATER_DATE?>');
										return false;
									}
								
									var num_category = document.stock_report_criteria.elements['_dualselect_sel_category_id[]'];
															
									if (document.getElementById('cat_id').value == ''&& num_category.length == 0) {
										alert('<?=JS_STOCK_REPORT_CATEGORY?>');
										document.getElementById('cat_id').focus();
										return false;
									}
									
									if (!document.getElementById('available_qty_box').checked && !document.getElementById('actual_qty_box').checked) {
										alert('<?=JS_STOCK_REPORT_PRODUCTS_QUANTITY?>');
										return false;
									}
									break;
									
								case "outstanding":
									if (!DOMCall('start_date').value){
										alert('<?=JS_STOCK_REPORT_START_DATE?>');
										return false;
									}
									
									if (!DOMCall('end_date').value){
										alert('<?=JS_STOCK_REPORT_END_DATE?>');
										return false;
									}
									
									if (DOMCall('start_date').value > DOMCall('end_date').value) {
										alert('<?=JS_STOCK_REPORT_GREATER_DATE?>');
										return false;
									}
									break;
							}
	
							var selected_category = document.stock_report_criteria.elements['_dualselect_sel_category_id[]'];
							if (selected_category != null) {
					
							for (cat_cnt=0; cat_cnt<(selected_category.length); cat_cnt++) {
								selected_category.options[cat_cnt].selected = true;
							}
						}
						
							document.getElementById('action').value = form_action;
			    			form_obj.submit();
							
							return true;
			    		}
			    		
			    		function resetProductReorder() {
			    			if (document.getElementById('use_product_reorder').checked) {
			    				document.getElementById('stock_level').disabled=true;
			    				document.getElementById('stock_level_old').value=document.getElementById('stock_level').value;
			    				document.getElementById('stock_level').value='';
			    			} else {
			    				document.getElementById('stock_level').disabled=false;
			    				document.getElementById('stock_level').value=document.getElementById('stock_level_old').value;
			    			}
			    		}
			    		
			    		function resetControls() {
							var selected_category = document.stock_report_criteria.elements['_dualselect_sel_category_id[]'];
							if (document.getElementById('cat_id').value != '' && selected_category.length > 0 ){
								var to = document.getElementById('_dualselect_sel_category_id');
				               	var from = document.getElementById('_dualselect_avl_category_id');
								moveAllOptions(to, from);
							}
						}
						
						function resetControls2() {
							var selected_category = document.stock_report_criteria.elements['_dualselect_sel_category_id[]'];
							if (document.getElementById('cat_id').value != '' && selected_category.length <= 0 ){
								document.getElementById('cat_id').value = '';
							}
			    	   }
		    			
			    		function swap_fields(mode) {
							if (mode) {
								document.getElementById('stock_level_section').className = "show";
							} else {
								document.getElementById('stock_level_section').className = "hide";
							}
						}
						
						function swap_report_type_fields() {
							var report = DOMCall('report');
							
							if (report.value == "real_time") {
								DOMCall('stock_date_section').className = "hide";
								DOMCall('cat_section').className = "show";
								DOMCall('prod_status_sec').className = "show";
								DOMCall('all_stock_sec').className = "show";
								DOMCall('stock_level_section').className = "show";
								DOMCall('prod_qty_section').className = "show";
								DOMCall('report_result_section').className = "show";
								DOMCall('cat_section_boxes').className = "show";
							
							} else if (report.value == "movement") {
								DOMCall('stock_date_section').className = "show";
								DOMCall('cat_section').className = "show";
								DOMCall('prod_status_sec').className = "hide";
								DOMCall('all_stock_sec').className = "hide";
								DOMCall('stock_level_section').className = "hide";
								DOMCall('prod_qty_section').className = "show";
								DOMCall('report_result_section').className = "hide";
								DOMCall('cat_section_boxes').className = "show";
							
							} else if (report.value == "outstanding") {
								DOMCall('stock_date_section').className = "show";
								DOMCall('cat_section').className = "hide";
								DOMCall('prod_status_sec').className = "hide";
								DOMCall('all_stock_sec').className = "hide";
								DOMCall('stock_level_section').className = "hide";
								DOMCall('prod_qty_section').className = "hide";
								DOMCall('report_result_section').className = "hide";
								DOMCall('cat_section_boxes').className = "hide";
							}
						}
						
						function init() {
							var show_all_obj = document.getElementsByName('show_all');
							var report = DOMCall('report');
							
	  						if (report.value == "movement") {
	  							DOMCall('stock_date_section').className = "show";
								DOMCall('cat_section').className = "show";
								DOMCall('prod_status_sec').className = "hide";
								DOMCall('all_stock_sec').className = "hide";
								DOMCall('stock_level_section').className = "hide";
								DOMCall('prod_qty_section').className = "show";
								DOMCall('report_result_section').className = "hide";
								DOMCall('cat_section_boxes').className = "show";
								
							} else if (report.value == "outstanding") {
								DOMCall('stock_date_section').className = "show";
								DOMCall('cat_section').className = "hide";
								DOMCall('prod_status_sec').className = "hide";
								DOMCall('all_stock_sec').className = "hide";
								DOMCall('stock_level_section').className = "hide";
								DOMCall('prod_qty_section').className = "hide";
								DOMCall('report_result_section').className = "hide";
								DOMCall('cat_section_boxes').className = "hide";
								
	  						} else {
	  							DOMCall('stock_date_section').className = "hide";
								DOMCall('cat_section').className = "show";
								DOMCall('prod_status_sec').className = "show";
								DOMCall('all_stock_sec').className = "show";
								DOMCall('stock_level_section').className = "<?=isset($_SESSION['stock_param']) ? ($_SESSION['stock_param']['show_all'] ? 'hide' : 'show') : 'show'?>";
								DOMCall('prod_qty_section').className = "show";
								DOMCall('report_result_section').className = "show";
								DOMCall('cat_section_boxes').className = "show";
	  						}
							
							resetProductReorder();
						}
						
						init();
						//-->
					</script>
<?
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
<script language="JavaScript" src="includes/javascript/select_box.js"></script>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>