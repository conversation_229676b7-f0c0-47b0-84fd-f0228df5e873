<?
/*
  	$Id: store_credit.php,v 1.10 2014/07/08 09:16:20 chingyen Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'store_credit.php');

ini_set("max_execution_time", "120");

$currencies = new currencies();
$sc_object = new store_credit($login_id, $login_email_address);

$view_sc_flow_report_permission = tep_admin_files_actions(FILENAME_STORE_CREDIT, 'STORE_CREDIT_REPORT_SC_FLOW');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

switch($subaction) {
	case "manual_deduct":
		$subaction_res = $sc_object->manual_deduct_amount($_POST, $messageStack);
 		
		tep_redirect(tep_href_link(FILENAME_STORE_CREDIT, tep_get_all_get_params(array('subaction'))));
		
		break;
	case "manual_add":
		$subaction_res = $sc_object->manual_add_amount($_POST, $messageStack);
		
		tep_redirect(tep_href_link(FILENAME_STORE_CREDIT, tep_get_all_get_params(array('subaction'))));
		
		break;
	default:
		;	// Nothing to perform
		break;
}

switch($action) {
	case "show_report":
		$header_title = HEADER_FORM_SC_STAT_TITLE;
		$form_content = $sc_object->show_sc_statement(FILENAME_STORE_CREDIT, 'sc_statement_inputs', $_REQUEST, $messageStack);

		break;
	case "reset_session":
    	unset($_SESSION['sc_statement_inputs']);
    	tep_redirect(tep_href_link(FILENAME_STORE_CREDIT));
    	
    	break;
	case "export_report":
		$export_csv_content = $sc_object->export_store_credit_movement(FILENAME_STORE_CREDIT, 'sc_statement_inputs', $_REQUEST, $messageStack);
		if (tep_not_null($export_csv_content)) {
			$filename = str_replace(".php", "_", FILENAME_STORE_CREDIT) . date('YmdHis') . '.csv';
			$mime_type = 'text/x-csv';
			// Download
			header('Content-Type: ' . $mime_type);
			header('Content-Encoding: utf-8');
			header('Content-Language: zh, en');
			header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			// IE need specific headers
			if (PMA_USR_BROWSER_AGENT == 'IE') {
				header('Content-Disposition: inline; filename="' . $filename . '"');
				header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
				header('Pragma: public');
			} else {
				header('Content-Disposition: attachment; filename="' . $filename . '"');
				header('Pragma: no-cache');
			} //end if
			echo $export_csv_content;
			exit();
		} else {
			$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
			tep_redirect(tep_href_link(FILENAME_STORE_CREDIT));
		} //end if
			
		break;
	case "export_summary":
		$export_zip_content = $sc_object->export_detail_daily_history(FILENAME_STORE_CREDIT, 'sc_statement_inputs', $_REQUEST);
		if (tep_not_null($export_zip_content)) {
			require_once(DIR_WS_CLASSES . "zip.php");
			// zip datetime
			$dt = date('YmdHis');
			// zip file name
			$zipfilename = str_replace(".php", "_", FILENAME_STORE_CREDIT) . $dt . '.zip';
			// directory name in the zip file
			$dirname = str_replace(".php", "", $zipfilename);
			// zipping files on the fly using memory
			$zipObj = new zip($zipfilename);
			$zipObj->add_dir($dirname);
			foreach ($export_zip_content as $currency => $csv_data) {
				$csv_filename = str_replace(".php", "_", FILENAME_STORE_CREDIT) . $currency . '_' . $dt . '.csv';
				$zipObj->add_file(implode("\n",$csv_data), $dirname."/".$csv_filename);
			}
			
			// download the zip file
			$mime_type = 'application/octet-stream';
			// Download
			header('Content-Type: ' . $mime_type);
			header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
			// IE need specific headers
			if (PMA_USR_BROWSER_AGENT == 'IE') {
				header('Content-Disposition: inline; filename="' . $zipfilename . '"');
				header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
				header('Pragma: public');
			} else {
				header('Content-Disposition: attachment; filename="' . $zipfilename . '"');
				header('Pragma: no-cache');
			} //end if
			echo $zipObj->file();
			exit();
		} else {
			$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
			tep_redirect(tep_href_link(FILENAME_STORE_CREDIT));
		} //end if

		break;
	default:
		$header_title = HEADER_FORM_SC_STAT_TITLE;
		$form_content = $sc_object->search_sc_statement(FILENAME_STORE_CREDIT, 'sc_statement_inputs');
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/customer_xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.js"></script>

	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.selectboxes.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/jquery.tabs.js"></script>
<?	if (file_exists(DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php')) { include_once (DIR_WS_INCLUDES . 'javascript/payment_xmlhttp.js.php'); } ?>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- Popup //-->
	<script language="JavaScript" src="<?=DIR_WS_INCLUDES?>addon/scriptasylum_popup/popup.js"></script>
	<!-- End of Popup //-->
	
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
		    		<tr>
        				<td width="100%" class="pageHeading"><b><?=$header_title?></b></td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
<script>
jQuery(document).ready(function() {
	initInfoCaptions();
	jQuery("#search-tab > ul").tabs();
});
</script>
</body>
</html>