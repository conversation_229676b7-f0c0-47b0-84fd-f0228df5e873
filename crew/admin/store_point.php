<?php
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'store_point.php');

tep_set_time_limit(0);

$sp_object = new store_point($login_id, $login_email_address);

$view_sp_flow_report_permission = tep_admin_files_actions(FILENAME_STORE_POINT, 'STORE_POINT_REPORT_SP_FLOW');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

switch ($subaction) {
    case 'manual_deduct':
        $subaction_res = $sp_object->manual_deduct_amount($HTTP_POST_VARS, $messageStack);
        tep_redirect(tep_href_link(FILENAME_STORE_POINT, http_build_query($_SESSION['sp_statement_inputs'])));
        break;

    case 'manual_add':
        $subaction_res = $sp_object->manual_add_amount($HTTP_POST_VARS, $messageStack);
        tep_redirect(tep_href_link(FILENAME_STORE_POINT, http_build_query($_SESSION['sp_statement_inputs'])));
        break;

    default:
        ; // Nothing to perform
        break;
}

switch ($action) {
    case 'show_report':
        $_SESSION['sp_statement_inputs'] = $_REQUEST;
        $header_title = HEADER_FORM_SP_STAT_TITLE;
        break;

    case 'reset_session':
        unset($_SESSION['sp_statement_inputs']);
        array_map('unlink', glob("download/store_point_" . $sp_object->identity . "*"));
        tep_redirect(tep_href_link(FILENAME_STORE_POINT));
        break;

    case 'export_report':
        $_SESSION['sp_statement_inputs'] = $_REQUEST;
        $sp_object->search($_REQUEST);
        break;

    default:
        $header_title = HEADER_FORM_SP_STAT_TITLE;
        break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?= HTML_PARAMS ?> >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=<?= CHARSET ?>">
        <title><?= TITLE ?></title>
        <link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
        <link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
        <script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
        <script language="javascript" src="includes/general.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/xmlhttp.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/customer_xmlhttp.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.selectboxes.js"></script>
        <script language="javascript" src="<?= DIR_WS_INCLUDES ?>javascript/jquery.tabs.js"></script>
    </head>
    <body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
        <div id="spiffycalendar" class="text"></div>
        <? require(DIR_WS_INCLUDES . 'header.php'); ?>
        <script language="JavaScript" src="<?= DIR_WS_INCLUDES ?>addon/scriptasylum_popup/popup.js"></script>
        <table border="0" width="100%" cellspacing="2" cellpadding="2">
            <tr>
                <td width="<?php echo BOX_WIDTH; ?>" valign="top">
                    <table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
                        <? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
                    </table>
                </td>
                <td width="100%" valign="top">
                    <table border="0" width="100%" cellspacing="0" cellpadding="2">
                        <tr>
                            <td width="100%" class="pageHeading"><b><?= $header_title ?></b></td>
                        </tr>
                        <tr>
                            <td><?= tep_draw_separator('pixel_trans.gif', '1', '10') ?></td>
                        </tr>
                        <tr>
                            <td width="100%" valign="top">
                                <?php
                                $sp_object->search_sp_statement(FILENAME_STORE_POINT, 'sp_statement_inputs');

                                switch ($action) {
                                    case 'show_report':
                                        if (tep_not_null($_REQUEST["report"]) && ($_REQUEST["report"] == 1 || $_REQUEST["report"] == 2)) {
                                            $sp_object->search($_REQUEST);
                                        }
                                        break;
                                }
                                ?>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <? require(DIR_WS_INCLUDES . 'footer.php'); ?>
        <? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
        <script>
            jQuery(document).ready(function () {
                initInfoCaptions();
                jQuery("#search-tab > ul").tabs();
            });
        </script>
    </body>
</html>