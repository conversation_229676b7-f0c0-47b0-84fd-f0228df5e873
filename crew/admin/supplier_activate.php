<?
require('includes/application_top.php');

$key = trim($_GET['key']);

if ($key == '')
	die(TEXT_BLANK_KEY);
else {
	$sql = "SELECT * FROM ".TABLE_SUPPLIER." WHERE supplier_activation_code='$key';";
	$result = tep_db_query($sql);
	
	if ($row = tep_db_fetch_array($result)) {
		tep_db_query("UPDATE ".TABLE_SUPPLIER." SET supplier_activation_code='', supplier_status='1' WHERE supplier_id='$row[supplier_id]';");
		
		$aemailbody =  tep_get_email_greeting($row['supplier_firstname'],$row['supplier_lastname'],$row['supplier_gender']) .
	    			 	sprintf(MAIL_BODY, $row['supplier_email_address']) .
	    			 	MAIL_SUPPLIER_FOOTER;
		
	    tep_mail($row['supplier_firstname'].' '.$row['supplier_firstname'], $row['supplier_email_address'], MAIL_SUBJECT, $aemailbody, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		echo TEXT_ACCOUNT_ACTIVATED;
	} else {
		echo TEXT_KEY_DOES_NOT_EXIST;
	}
}
?>