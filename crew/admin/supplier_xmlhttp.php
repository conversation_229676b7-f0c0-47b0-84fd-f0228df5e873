<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');

include_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback.php');

$action = isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '';
$subaction = isset($HTTP_GET_VARS['subaction']) ? $HTTP_GET_VARS['subaction'] : '';
$languages_id = isset($_SESSION['languages_id']) ? $_SESSION['languages_id'] : '';
$language = isset($_SESSION['language']) ? $_SESSION['language'] : '';

$results = '';

echo '<response>';

if (tep_not_null($action)) {
	switch($action) {
		case "calculate_max_inventory_space":
		    $products_id = $_GET['products_id'];
		    $inventory_days = $_GET['inventory_days'];
		    $sales_start_date = $_GET['sales_start_date'];
            $last_n_days_sales = $_GET['last_n_days_sales'];
            $sales_retrieval_method = $_GET['sales_retrieval_method'];
		    
		    //End date default to today if blank
		    $sales_end_date = (trim($_GET['sales_end_date']) ? $_GET['sales_end_date'] : date('Y-m-d'));
	        $buyback_settings_arr = array(
	                                   'bprd_sales_start_date' => $sales_start_date,
	                                   'bprd_sales_end_date' => $sales_end_date,
	                                   'bprd_inventory_days' => $inventory_days,
	                                   'bprd_last_n_days_sales' => $last_n_days_sales,
	                                   'bprd_sales_retrieval_method' => $sales_retrieval_method
	                                   );
          	$buybackProductObj = new buyback_product($products_id);
	        $buybackProductObj->assign_buyback_settings($buyback_settings_arr);
	        $buybackProductObj->calculate();
	        
            echo "<max_inv_space_derived>";
            echo $buybackProductObj->maximum_inventory_space_derived;
            echo "</max_inv_space_derived>";
            echo "<max_buyback_qty>";
            echo $buybackProductObj->max_buyback_qty;
            echo "</max_buyback_qty>";
            unset($buybackProductObj);
    	
    		break;
    	case "qty_stat_by_supplier":
    		$products_id = isset($HTTP_GET_VARS['products_id']) ? tep_db_prepare_input($HTTP_GET_VARS['products_id']) : false;
    		$main_category_id = isset($HTTP_GET_VARS['cat_id']) ? tep_db_prepare_input($HTTP_GET_VARS['cat_id']) : false;
    		$supply_date_from = tep_db_prepare_input($HTTP_GET_VARS['supply_date_from']);
    		$supply_date_to = tep_db_prepare_input($HTTP_GET_VARS['supply_date_to']);
    		$supply_from = tep_db_prepare_input($HTTP_GET_VARS['supply_from']);
    		$supply_status = tep_db_prepare_input($HTTP_GET_VARS['supply_status']);
    		
    		if ($main_category_id) {
    			$category_array = tep_get_eligible_categories(FILENAME_SUPPLIERS_REPORT, $category_array, $main_category_id, true);
    		}
    		
    		if ( ($products_id || $main_category_id) && tep_not_null($supply_date_from) && tep_not_null($supply_date_to) && tep_not_null($supply_from) && tep_not_null($supply_status)) {
    			$supply_from_array = explode(',', $supply_from);
    			
    			if (in_array('sup', $supply_from_array)) {
    				$date_range_where_sql = " sol.supplier_order_lists_date >= '" . tep_db_input($supply_date_from) . " 00:00:00' AND sol.supplier_order_lists_date <= '" . tep_db_input($supply_date_to) . " 23:59:59' ";
	      			
	      			if ($main_category_id) {
	      				$supplier_orders_products_select_sql = "SELECT suppliers_id, SUM(solp2.products_received_quantity) AS recv_qty 
																FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
																INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp2 
																	ON (sol.supplier_order_lists_id=solp2.supplier_order_lists_id AND solp2.supplier_order_lists_type=2) 
																INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
																	ON (solp2.products_id=p2c.products_id AND p2c.products_is_link=0)
																WHERE $date_range_where_sql 
																	AND sol.supplier_order_lists_status IN (".$supply_status.") 
																	AND p2c.categories_id IN ('" . implode("', '", $category_array) . "') 
																GROUP BY suppliers_id 
																HAVING recv_qty > 0";
	      			} else {
			      		$supplier_orders_products_select_sql = "SELECT suppliers_id, SUM(solp2.products_received_quantity) AS recv_qty 
																FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
																INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp2 
																	ON (sol.supplier_order_lists_id=solp2.supplier_order_lists_id AND solp2.supplier_order_lists_type=2 AND solp2.products_id = '" . tep_db_input($products_id) . "') 
																WHERE $date_range_where_sql 
																	AND sol.supplier_order_lists_status IN (".$supply_status.") 
																GROUP BY suppliers_id 
																HAVING recv_qty > 0";
					}
					
					$supplier_orders_products_result_sql = tep_db_query($supplier_orders_products_select_sql);
					
					while ($supplier_orders_products_row = tep_db_fetch_array($supplier_orders_products_result_sql)) {
						$supplier_info_select_sql = "	SELECT supplier_firstname, supplier_lastname, supplier_email_address, supplier_telephone, supplier_qq 
														FROM " . TABLE_SUPPLIER . " 
														WHERE supplier_id = '" . tep_db_input($supplier_orders_products_row['suppliers_id']) . "'";
						$supplier_info_result_sql = tep_db_query($supplier_info_select_sql);
						$supplier_info_row = tep_db_fetch_array($supplier_info_result_sql);
						
						echo "<sup_mod>";
						echo "<name><![CDATA[".$supplier_info_row['supplier_firstname'].' '.$supplier_info_row['supplier_lastname']."]]></name>";
						echo "<email><![CDATA[".$supplier_info_row['supplier_email_address']."]]></email>";
						echo "<mobile><![CDATA[".$supplier_info_row['supplier_telephone']."]]></mobile>";
						echo "<qq><![CDATA[".$supplier_info_row['supplier_qq']."]]></qq>";
						echo "<qty><![CDATA[".$supplier_orders_products_row['recv_qty']."]]></qty>";
						echo "</sup_mod>";
					}
				}
				
				if (in_array('us', $supply_from_array)) {	// Website Buyback
					if ($supply_date_from == $supply_date_to) {
	      				$date_range_where_sql = " DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') = '" . tep_db_input($supply_date_from) . "' ";
	      			} else {
	      				$date_range_where_sql = " DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') >= '" . tep_db_input($supply_date_from) . "' AND DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') <= '" . tep_db_input($supply_date_to) . "' ";
	      			}
					
					if ($main_category_id) {
						$website_buyback_products_select_sql = "SELECT customers_id, SUM(br.buyback_quantity_received) AS recv_qty 
																FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
																INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
																	ON (brg.buyback_request_group_id=br.buyback_request_group_id) 
																INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
																	ON (br.products_id=p2c.products_id AND p2c.products_is_link=0)
																WHERE buyback_request_group_site_id = 0 
																	AND $date_range_where_sql 
																	AND brg.buyback_status_id IN (".$supply_status.") 
																	AND p2c.categories_id IN ('" . implode("', '", $category_array) . "') 
																GROUP BY customers_id 
																HAVING recv_qty > 0";
					} else {
			      		$website_buyback_products_select_sql = "SELECT customers_id, SUM(br.buyback_quantity_received) AS recv_qty 
																FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
																INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
																	ON (brg.buyback_request_group_id=br.buyback_request_group_id AND br.products_id = '" . tep_db_input($products_id) . "') 
																WHERE buyback_request_group_site_id = 0 
																	AND $date_range_where_sql 
																	AND brg.buyback_status_id IN (".$supply_status.") 
																GROUP BY customers_id 
																HAVING recv_qty > 0";
					}
					
					$website_buyback_products_result_sql = tep_db_query($website_buyback_products_select_sql);
					
					while ($website_buyback_products_row = tep_db_fetch_array($website_buyback_products_result_sql)) {
						$supplier_info_select_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_mobile, customers_qq 
														FROM " . TABLE_CUSTOMERS . " 
														WHERE customers_id = '" . tep_db_input($website_buyback_products_row['customers_id']) . "'";
						$supplier_info_result_sql = tep_db_query($supplier_info_select_sql);
						$supplier_info_row = tep_db_fetch_array($supplier_info_result_sql);
						
						echo "<web_us_mod>";
						echo "<name><![CDATA[".$supplier_info_row['customers_firstname'].' '.$supplier_info_row['customers_lastname']."]]></name>";
						echo "<email><![CDATA[".$supplier_info_row['customers_email_address']."]]></email>";
						echo "<mobile><![CDATA[".$supplier_info_row['customers_mobile']."]]></mobile>";
						echo "<qq><![CDATA[".$supplier_info_row['customers_qq']."]]></qq>";
						echo "<qty><![CDATA[".$website_buyback_products_row['recv_qty']."]]></qty>";
						echo "</web_us_mod>";
					}
				}
				
				if (in_array('cn', $supply_from_array)) {	// China Buyback
					if ($supply_date_from == $supply_date_to) {
	      				$date_range_where_sql = " DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') = '" . tep_db_input($supply_date_from) . "' ";
	      			} else {
	      				$date_range_where_sql = " DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') >= '" . tep_db_input($supply_date_from) . "' AND DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') <= '" . tep_db_input($supply_date_to) . "' ";
	      			}
					
					if ($main_category_id) {
						$cn_buyback_products_select_sql = "	SELECT customers_id, SUM(br.buyback_quantity_received) AS recv_qty 
															FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
															INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
																ON (brg.buyback_request_group_id=br.buyback_request_group_id) 
															INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
																ON (br.products_id=p2c.products_id AND p2c.products_is_link=0)
															WHERE buyback_request_group_site_id = 1 
																AND $date_range_where_sql 
																AND brg.buyback_status_id IN (".$supply_status.") 
																AND p2c.categories_id IN ('" . implode("', '", $category_array) . "') 
															GROUP BY customers_id 
															HAVING recv_qty > 0";
					} else {
			      		$cn_buyback_products_select_sql = "	SELECT customers_id, SUM(br.buyback_quantity_received) AS recv_qty 
															FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
															INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
																ON (brg.buyback_request_group_id=br.buyback_request_group_id AND br.products_id = '" . tep_db_input($products_id) . "') 
															WHERE buyback_request_group_site_id = 1 
																AND $date_range_where_sql 
																AND brg.buyback_status_id IN (".$supply_status.") 
															GROUP BY customers_id 
															HAVING recv_qty > 0";
					}
					
					$cn_buyback_products_result_sql = tep_db_query($cn_buyback_products_select_sql);
					
					while ($cn_buyback_products_row = tep_db_fetch_array($cn_buyback_products_result_sql)) {
						$supplier_info_select_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_mobile, customers_qq 
														FROM " . TABLE_CUSTOMERS . " 
														WHERE customers_id = '" . tep_db_input($cn_buyback_products_row['customers_id']) . "'";
						$supplier_info_result_sql = tep_db_query($supplier_info_select_sql);
						$supplier_info_row = tep_db_fetch_array($supplier_info_result_sql);
						
						echo "<cn_buy_mod>";
						echo "<name><![CDATA[".$supplier_info_row['customers_firstname'].' '.$supplier_info_row['customers_lastname']."]]></name>";
						echo "<email><![CDATA[".$supplier_info_row['customers_email_address']."]]></email>";
						echo "<mobile><![CDATA[".$supplier_info_row['customers_mobile']."]]></mobile>";
						echo "<qq><![CDATA[".$supplier_info_row['customers_qq']."]]></qq>";
						echo "<qty><![CDATA[".$cn_buyback_products_row['recv_qty']."]]></qty>";
						echo "</cn_buy_mod>";
					}
				}
    		}
    		
    		break;
		case 'supplier_info':
    		$subaction = isset($HTTP_GET_VARS['subaction']) ? tep_db_prepare_input($HTTP_GET_VARS['subaction']) : false;
    		$products_id = isset($HTTP_GET_VARS['pID']) ? tep_db_prepare_input($HTTP_GET_VARS['pID']) : false;
    		
    		if ($subaction == 'inventory') {
    			$customer_info_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, 
										c.customers_telephone, c.customers_mobile, c.customers_msn, c.customers_qq, 
										c.customers_yahoo, c.customers_icq, vsi.vip_supplier_inventory_qty AS quantity
										FROM " . TABLE_VIP_SUPPLIER_INVENTORY . " AS vsi
										INNER JOIN " . TABLE_CUSTOMERS . " AS c
											ON(vsi.customers_id=c.customers_id) 
										WHERE vsi.products_id='" . (int)$products_id . "'";
    		} else if ($subaction == 'order_assign') {
    			$orders_products_id = isset($HTTP_GET_VARS['opID']) ? tep_db_prepare_input($HTTP_GET_VARS['opID']) : false;
    			$customer_info_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, 
										c.customers_telephone, c.customers_mobile, c.customers_msn, c.customers_qq, 
										c.customers_yahoo, c.customers_icq, vip_order_allocation_quantity AS quantity
										FROM " . TABLE_VIP_ORDER_ALLOCATION . " AS voa
										INNER JOIN " . TABLE_CUSTOMERS . " AS c
											ON(voa.customers_id=c.customers_id) 
										WHERE voa.products_id='" . (int)$products_id . "'
											AND voa.orders_products_id='" . (int) $orders_products_id. "'";
    		} else if ($subaction == 'buyback_history') {
				$customer_info_sql = "	SELECT c.customers_firstname, c.customers_lastname, c.customers_email_address, 
										c.customers_telephone, c.customers_mobile, c.customers_msn, c.customers_qq, 
										c.customers_yahoo, c.customers_icq, brg.buyback_request_group_date, brg.buyback_request_group_site_id,
										IF(brg.buyback_status_id=3, br.buyback_quantity_received, IF(brg.buyback_status_id=2, br.buyback_quantity_confirmed, br.buyback_request_quantity)) AS quantity
										FROM " . TABLE_BUYBACK_REQUEST_GROUP ." AS brg
										INNER JOIN " . TABLE_BUYBACK_REQUEST ." AS br
											ON(brg.buyback_request_group_id=br.buyback_request_group_id)
										INNER JOIN " . TABLE_CUSTOMERS . " AS c
											ON(brg.customers_id=c.customers_id)
										WHERE brg.buyback_status_id <> '4'
											AND br.products_id = '" . $products_id . "'
											AND brg.buyback_request_group_date >= DATE_SUB(CURDATE() , INTERVAL 2 MONTH)
											AND brg.buyback_request_group_date <= CURDATE()
										ORDER BY brg.buyback_request_group_site_id, brg.buyback_request_group_date DESC";
    		}
    		$customer_info_result = tep_db_query($customer_info_sql);
    		while ($customer_info_row = tep_db_fetch_array($customer_info_result)) {
    			echo '<supplier>';
    			echo '<date><![CDATA[' . (isset($customer_info_row['buyback_request_group_date']) ? $customer_info_row['buyback_request_group_date'] : '&nbsp;') . ']]></date>';
    			echo '<name><![CDATA[' . $customer_info_row['customers_firstname'] . ', ' . $customer_info_row['customers_lastname'] . ']]></name>';
				echo '<email><![CDATA['.$customer_info_row['customers_email_address'].']]></email>';
				echo '<phone><![CDATA[' . ($customer_info_row['customers_telephone']== '' ? '&nbsp;' : $customer_info_row['customers_telephone']) . ']]></phone>';
				echo '<mobile><![CDATA[' . ($customer_info_row['customers_mobile']== '' ? '&nbsp;' : $customer_info_row['customers_mobile']) . ']]></mobile>';
				echo '<qq><![CDATA[' . ($customer_info_row['customers_qq']== '' ? '&nbsp;' : $customer_info_row['customers_qq']) . ']]></qq>';
				echo '<msn><![CDATA[' . ($customer_info_row['customers_msn']== '' ? '&nbsp;' : $customer_info_row['customers_msn']) . ']]></msn>';
				echo '<yahoo><![CDATA[' . ($customer_info_row['customers_yahoo']== '' ? '&nbsp;' : $customer_info_row['customers_yahoo']) . ']]></yahoo>';
				echo '<icq><![CDATA[' . ($customer_info_row['customers_icq']== '' ? '&nbsp;' : $customer_info_row['customers_icq']) . ']]></icq>';
				echo '<quantity><![CDATA['.$customer_info_row['quantity'].']]></quantity>';
				echo '<site><![CDATA['.(isset($customer_info_row['buyback_request_group_site_id']) ? $customer_info_row['buyback_request_group_site_id'] : '&nbsp;').']]></site>';
				echo '</supplier>';
    		}
    		break;
		default:
			echo "<result>Unknown request!</result>";
			break;
	}
}

echo '</response>';
?>