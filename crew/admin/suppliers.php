<?php
/*
  	$Id: suppliers.php,v 1.37 2008/11/04 03:23:24 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'payments.php');
require_once(DIR_WS_FUNCTIONS . 'user_agent.php');
require_once('pear/Date.php');

define('DISPLAY_PRICE_DECIMAL', 4);

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$payments_object = new payments($login_id, $login_email_address);

$view_payment_info_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'SUPPLIER_ORDER_PAYMENT_INFO');
$supplier_cp_payment_detail_permission = tep_admin_files_actions(FILENAME_PROGRESS_REPORT, 'SUPPLIER_CP_PAYMENT_DETAILS');
$supplier_cp_slots = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_CP_SLOTS');
$reserve_amount_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_RESERVE_AMOUNT');

$view_supplier_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_VIEW_INFO');
$view_account_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_VIEW_ACCOUNT');
$view_remark_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_VIEW_REMARK');

$edit_supplier_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_EDIT_INFO');
$edit_account_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_EDIT_ACCOUNT');
$edit_remark_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_EDIT_REMARK');
$enable_withdraw_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_ENABLE_WITHDRAW');
$disable_withdraw_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_LIST, 'SUPPLIER_DISABLE_WITHDRAW');

$user_os = tep_get_os($HTTP_USER_AGENT);
$country_selection_mode = ($user_os == "Windows" || $user_os == "Linux") ? 'xmlhttp_mode' : 'refresh_mode';

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$subaction = (isset($_GET['subaction']) ? $_GET['subaction'] : '');

$supplier_grp_where_str = tep_not_null($grp_id) ? "s.supplier_groups_id='".$grp_id."'" : ' 1 ';
if (isset($_REQUEST["grp_id"])) {
	$_SESSION["supplier_param"]['grp_id'] = $_REQUEST["grp_id"];
}

$error = false;
$processed = false;
$error_section = "";

if (tep_not_null($action)) {
	switch ($action) {
		case "set_supplier_status":
			if ( ($_REQUEST['flag'] == '0') || ($_REQUEST['flag'] == '1') ) {
          		if (isset($_REQUEST['sID'])) {
      				$supplier_status_update_sql = "	UPDATE " . TABLE_SUPPLIER . " 
      												SET supplier_status = '".(int)$_REQUEST['flag']."' 
      												WHERE supplier_id='" . (int)$_REQUEST["sID"] . "'";
					tep_db_query($supplier_status_update_sql);
          		}
        	}
        	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_LIST, 'page=' . $HTTP_GET_VARS['page']));
        	
			break;
	}
}

if (tep_not_null($subaction)) {
	$supplier_id = tep_db_prepare_input($HTTP_POST_VARS['sID']);
	$supplier_email_address = tep_db_prepare_input($HTTP_POST_VARS['supplier_email_address']);
	
	switch ($subaction) {
		case "update_supplier":
			if ($edit_supplier_permission) {
				$supplier_gender = tep_db_prepare_input($HTTP_POST_VARS['supplier_gender']);
				$supplier_firstname = tep_db_prepare_input($HTTP_POST_VARS['supplier_firstname']);
				$supplier_lastname = tep_db_prepare_input($HTTP_POST_VARS['supplier_lastname']);
				$supplier_dob = tep_db_prepare_input($HTTP_POST_VARS['supplier_dob']);
				$supplier_street_address = tep_db_prepare_input($HTTP_POST_VARS['supplier_street_address']);
				$supplier_suburb = tep_db_prepare_input($HTTP_POST_VARS['supplier_suburb']);
				$supplier_postcode = tep_db_prepare_input($HTTP_POST_VARS['supplier_postcode']);
				$supplier_city = tep_db_prepare_input($HTTP_POST_VARS['supplier_city']);
				$supplier_country_id = tep_db_prepare_input($HTTP_POST_VARS['supplier_country_id']);
				$supplier_telephone = tep_db_prepare_input($HTTP_POST_VARS['supplier_telephone']);
				$supplier_fax = tep_db_prepare_input($HTTP_POST_VARS['supplier_fax']);
				$supplier_qq = tep_db_prepare_input($HTTP_POST_VARS['supplier_qq']);
				$supplier_msn = tep_db_prepare_input($HTTP_POST_VARS['supplier_msn']);	        
				$supplier_code = tep_db_prepare_input($HTTP_POST_VARS['supplier_code']);
				$supplier_groups_id = tep_db_prepare_input($HTTP_POST_VARS['supplier_groups_id']);
		        
		      	if (ACCOUNT_STATE == 'true') {
			    	$supplier_state = tep_db_prepare_input($HTTP_POST_VARS['supplier_state']);
			    }
			    
		        if (ACCOUNT_GENDER == 'true') {
	          		if ($supplier_gender != 'm' && $supplier_gender != 'f') {
	            		$error = true;
	            		$entry_gender_error = true;
	          		} else {
	            		$entry_gender_error = false;
	          		}
	        	}       
	        	
		        if (strlen($supplier_firstname) < ENTRY_FIRST_NAME_MIN_LENGTH) {
	          		$error = true;
	          		$entry_firstname_error = true;
	        	} else {
	          		$entry_firstname_error = false;
	        	}
				
	        	if (strlen($supplier_lastname) < ENTRY_LAST_NAME_MIN_LENGTH) {
	          		$error = true;
	          		$entry_lastname_error = true;
	        	} else {
	          		$entry_lastname_error = false;
	        	}
	        	
	        	if (ACCOUNT_DOB == 'true') {
	          		if (tep_not_null($supplier_dob) && @checkdate(substr(tep_date_raw($supplier_dob), 4, 2), substr(tep_date_raw($supplier_dob), 6, 2), substr(tep_date_raw($supplier_dob), 0, 4))) {
	            		$entry_date_of_birth_error = false;
	          		} else {
	            		$error = true;
	            		$entry_date_of_birth_error = true;
	          		}
	        	}
	        	
	        	if (strlen($supplier_email_address) < ENTRY_EMAIL_ADDRESS_MIN_LENGTH) {
		          	$error = true;
		          	$entry_email_address_error = true;
	        	} else {
	          		$entry_email_address_error = false;
	        	}
				
	        	if (!tep_validate_email($supplier_email_address)) {
	          		$error = true;
	          		$entry_email_address_check_error = true;
	        	} else {
	          		$entry_email_address_check_error = false;
	        	}
				
	        	if (strlen($supplier_street_address) < ENTRY_STREET_ADDRESS_MIN_LENGTH) {
	          		$error = true;
	          		$entry_street_address_error = true;
	        	} else {
	          		$entry_street_address_error = false;
	        	}
				
	        	if (strlen($supplier_postcode) < ENTRY_POSTCODE_MIN_LENGTH) {
	          		$error = true;
	          		$entry_post_code_error = true;
	        	} else {
	          		$entry_post_code_error = false;
	        	}
				
	        	if (strlen($supplier_city) < ENTRY_CITY_MIN_LENGTH) {
	          		$error = true;
	          		$entry_city_error = true;
	        	} else {
	          		$entry_city_error = false;
	        	}
	        	
	        	if (!tep_not_null($supplier_country_id)) {
	          		$error = true;
	          		$entry_country_error = true;
	        	} else {
	          		$entry_country_error = false;
	          		
	          		if (ACCOUNT_STATE == 'true') {
			            $entry_state_error = true;
			            $check_query = tep_db_query("select count(*) as total from " . TABLE_ZONES . " where zone_country_id = '" . (int)$supplier_country_id . "'");
			            $check_value = tep_db_fetch_array($check_query);
			            $entry_state_has_zones = ($check_value['total'] > 0);
			            
			            if ($entry_state_has_zones == true) {
			            	$entry_state_error = true;
		          			$zone_query = tep_db_query("select zone_id, zone_name from " . TABLE_ZONES . " where zone_country_id = '" . (int)$supplier_country_id . "' and zone_id = '" . (int)$supplier_state . "'");
		          			if (tep_db_num_rows($zone_query) == 1) {
		            			$zone_values = tep_db_fetch_array($zone_query);
		            			$supplier_zone_id = $zone_values['zone_id'];
		            			$supplier_zone_name = $zone_values['zone_name'];
		            			$entry_state_error = false;
		          			} else {
		            			$error = true;
		            			$entry_state_error = true;
		          			}
		        		} else {
		        			if($supplier_state == false) {
		            			$error = true;
		            			$entry_state_error = true;
		          			}
		        		}
		      		}
	        	}
	      		
	      		if (strlen($supplier_telephone) < ENTRY_TELEPHONE_MIN_LENGTH) {
	        		$error = true;
	        		$entry_telephone_error = true;
	      		} else {
	        		$entry_telephone_error = false;
	      		}
	      		
	      		if (tep_not_null($supplier_msn) && !tep_validate_email($supplier_msn)) {
	              	$error = true;
	              	$entry_msn_error = true;
	            } else {
	                $entry_msn_error = false;
	            }
	      		
	      		if (tep_not_null($supplier_qq) && !is_numeric($supplier_qq)) {
	                $error = true;
	              	$entry_qq_error = true;
	            } else {
	                $entry_qq_error = false;
	            }

	      		$check_email = tep_db_query("select supplier_email_address from " . TABLE_SUPPLIER . " where supplier_email_address = '" . tep_db_input($supplier_email_address) . "' and supplier_id != '" . (int)$supplier_id . "'");
	      		if (tep_db_num_rows($check_email)) {
	        		$error = true;
	        		$entry_email_address_exists = true;
	      		} else {
	        		$entry_email_address_exists = false;
	      		}
	      		
	      		if (tep_not_null($supplier_code)) {
	      			$supplier_code_error = false;
	        	} else {
	          		$error = true;
		          	$supplier_code_error = true;
	        	}
				
				$check_supplier_code = tep_db_query("select supplier_code from " . TABLE_SUPPLIER . " where supplier_code = '" . tep_db_input($supplier_code) . "' and supplier_id != '" . (int)$supplier_id . "'");
	      		if (tep_db_num_rows($check_email)) {
	        		$error = true;
	        		$supplier_code_exists = true;
	      		} else {
	        		$supplier_code_exists = false;
	      		}
      		
	      		if (!$error) {
					$supplier_current_info_select_sql = "SELECT supplier_groups_id FROM " . TABLE_SUPPLIER . " WHERE supplier_id = '" . (int)$supplier_id . "'";
					$supplier_current_info_result_sql = tep_db_query($supplier_current_info_select_sql);
					$supplier_current_info_row = tep_db_fetch_array($supplier_current_info_result_sql);
					
					$sql_data_array = array('supplier_code' => $supplier_code,
											'supplier_groups_id' => $supplier_groups_id,
											'supplier_firstname' => $supplier_firstname,
	                                		'supplier_lastname' => $supplier_lastname,
			                                'supplier_email_address' => $supplier_email_address,
			                                'supplier_telephone' => $supplier_telephone,
			                                'supplier_fax' => $supplier_fax,
			                                'supplier_qq' => $supplier_qq,
			                                'supplier_msn' => $supplier_msn,
			                                'supplier_street_address' => $supplier_street_address,
			                                'supplier_suburb' => $supplier_suburb,
			                                'supplier_city' => $supplier_city,
			                                'supplier_postcode' => $supplier_postcode,
			                                'supplier_country_id' => $supplier_country_id,
			                                'supplier_date_account_last_modified' => 'now()'
			                                );
			        
	        		if (ACCOUNT_GENDER == 'true') 	$sql_data_array['supplier_gender'] = $supplier_gender;
	        		if (ACCOUNT_DOB == 'true') 	$sql_data_array['supplier_dob'] = tep_date_raw($supplier_dob);
	        		
	        		if (ACCOUNT_STATE == 'true') {
	          			if ($supplier_zone_id > 0) {
	            			$sql_data_array['supplier_zone_id'] = $supplier_zone_id;
	            			$sql_data_array['supplier_state'] = '';
	          			} else {
	            			$sql_data_array['supplier_zone_id'] = '0';
	            			$sql_data_array['supplier_state'] = $supplier_state;
	          			}
	        		}
					tep_db_perform(TABLE_SUPPLIER, $sql_data_array, 'update', "supplier_id = '" . (int)$supplier_id . "'");
					
					if ($supplier_current_info_row["supplier_groups_id"] != $supplier_groups_id) {
						tep_db_query("DELETE FROM " . TABLE_SUPPLIER_PURCHASE_MODES . " WHERE supplier_id = '" . (int)$supplier_id . "'");
						
						$new_group_purchase_mode_select_sql = "	SELECT products_purchases_lists_id 
			    												FROM " . TABLE_SUPPLIER_LIST_TIME_SETTING . " 
			    												WHERE supplier_groups_id = '" . $supplier_groups_id . "'";
			    		$new_group_purchase_mode_result_sql = tep_db_query($new_group_purchase_mode_select_sql);
			    		while ($new_group_purchase_mode_row = tep_db_fetch_array($new_group_purchase_mode_result_sql)) {
			    			$time_sql_data_array = array(	'supplier_id' => (int)$supplier_id,
															'products_purchases_lists_id' => $new_group_purchase_mode_row['products_purchases_lists_id'],
															'supplier_purchase_mode' => 'STATUS_OFF'
			            								);
		            		tep_db_perform(TABLE_SUPPLIER_PURCHASE_MODES, $time_sql_data_array);
			    		}
					}
					$messageStack->add_session(sprintf(SUCCESS_SUPPLIER_UPDATED, $supplier_email_address), 'success');
					tep_redirect(tep_href_link(FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('sID', 'action', 'subaction')) . 'sID=' . $supplier_id . '&page=' . $_GET['page']));
				}
			}
			if ($error) {
				$error_section = "info";
			}
			break;
		case "update_task":
			if ($edit_account_permission) {
				if (isset($HTTP_POST_VARS["task"]) && is_array($HTTP_POST_VARS["task"])) {
					foreach ($HTTP_POST_VARS["task"] as $id => $selected) {
						$tasks[$id] = $selected;
						
						if ($tasks[$id] == '1') {
							foreach ($HTTP_POST_VARS["task_max"] as $id => $max_value) {
								$tasks_max[$id]	= $max_value;
							}
							
							foreach ($HTTP_POST_VARS["slot_max"] as $id => $max_slot_value) {
								$slots_max[$id]	= $max_slot_value;
							}
							
							foreach ($HTTP_POST_VARS["task_ratio"] as $id => $ratio_value) {
								$tasks_ratio[$id] = $ratio_value;
							}
							
							if (isset($HTTP_POST_VARS["task_game_remote_login_api"])) {
								foreach ($HTTP_POST_VARS["task_game_remote_login_api"] as $id => $task_game_remote_login_api_value) {
									$task_game_remote_login_api[$id] = $task_game_remote_login_api_value;
								}
							} else {
								$task_game_remote_login_api[$id] = 0;
							}
						}
					}
				}

				if (isset($tasks) && is_array($tasks)) {
					foreach ($tasks as $id => $selected) {
						if ($selected != '0' && $selected != '1') {
				    		$error = true;
				    		$task_error[$id] = true;
				  		} else {
				    		$task_error[$id] = false;
				  		}
				  		
				  		if ($selected == '1') {
				  			if (!is_numeric($tasks_max[$id]) || $tasks_max[$id] < 0 || $tasks_max[$id] < $slots_max[$id]) {
				  				$error = true;
				  				$task_max_error[$id] = true;	
				  			} else {
				  				$task_max_error[$id] = false;
				  			}
				  			
				  			if (!is_numeric($slots_max[$id]) || $slots_max[$id] < 0 || $slots_max[$id] > $tasks_max[$id]) {
				  				$error = true;
				  				$slot_max_error[$id] = true;
				  			} else {
				  				$slot_max_error[$id] = false;
				  			}
				  			
				  			if (!is_numeric($tasks_ratio[$id]) || $tasks_ratio[$id] < 0 || $tasks_ratio[$id] > $tasks_ratio[$id]) {
				  				$error = true;
				  				$task_ratio_error[$id] = true;
				  			} else {
				  				$task_ratio_error[$id] = false;
				  			}
				  		}
					}
				}
				if (!$error) {
					if ($supplier_cp_slots && isset($tasks) && is_array($tasks)) {
						$supplier_tasks_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_TASKS_SETTING . " WHERE suppliers_id = '" . (int)$supplier_id . "'";
		      			tep_db_query($supplier_tasks_delete_sql);
						
						foreach ($tasks as $id => $selected) {
		      				if ($selected == '1') {
		      					$supplier_tasks_setting_data_array = array( 'suppliers_id' => (int)$supplier_id,
		      																'custom_products_type_id' => (int)$id,
		      																'supplier_tasks_allocation_slots' => (int)$tasks_max[$id],
		      																'supplier_tasks_allocation_physical_slots' => (int)$slots_max[$id],
		      																'supplier_tasks_ratio' => (int)$tasks_ratio[$id],
		      																'use_remote_account_login_api' => (int)$task_game_remote_login_api[$id]
		      															  );
		      					tep_db_perform(TABLE_SUPPLIER_TASKS_SETTING, $supplier_tasks_setting_data_array);
		      				}
		      			}
		      		}
					$supplier_disable_withdrawal = tep_db_prepare_input($HTTP_POST_VARS['supplier_disable_withdrawal']);
					$supplier_disable_withdrawal_history = tep_db_prepare_input($HTTP_POST_VARS['supplier_disable_withdrawal_history']);
		      		$supplier_payment_bank_name = tep_db_prepare_input($HTTP_POST_VARS['supplier_payment_bank_name']);
		      		$supplier_payment_paypal = tep_db_prepare_input($HTTP_POST_VARS['supplier_payment_paypal']);
		      		$supplier_reserve_amount = tep_db_prepare_input($HTTP_POST_VARS['supplier_reserve_amount']);
		      		//$supplier_payment_bank_branch_number = tep_db_prepare_input($HTTP_POST_VARS['supplier_payment_bank_branch_number']);
					$supplier_payment_bank_swift_code = tep_db_prepare_input($HTTP_POST_VARS['supplier_payment_bank_swift_code']);
					$supplier_payment_bank_address = tep_db_prepare_input($HTTP_POST_VARS['supplier_payment_bank_address']);
					$supplier_payment_bank_telephone = tep_db_prepare_input($HTTP_POST_VARS['supplier_payment_bank_telephone']);
					$supplier_payment_bank_account_name = tep_db_prepare_input($HTTP_POST_VARS['supplier_payment_bank_account_name']);
					$supplier_payment_bank_account_number = tep_db_prepare_input($HTTP_POST_VARS['supplier_payment_bank_account_number']);
			        $supplier_disable_withdrawal = tep_db_prepare_input((isset($HTTP_POST_VARS['supplier_disable_withdrawal']) && (int)$HTTP_POST_VARS['supplier_disable_withdrawal'] == 1 ? 1 : 0));
	
		      		$sql_data_array= array(	'supplier_payment_paypal' => $supplier_payment_paypal,
		      								'supplier_payment_bank_name' => $supplier_payment_bank_name, 
		      								/*'supplier_payment_bank_branch_number' => $supplier_payment_bank_branch_number,*/
		      								'supplier_payment_bank_swift_code' => $supplier_payment_bank_swift_code,
			                                'supplier_payment_bank_address' => $supplier_payment_bank_address,
			                                'supplier_payment_bank_telephone' => $supplier_payment_bank_telephone,
			                                'supplier_payment_bank_account_name' => $supplier_payment_bank_account_name,
			                                'supplier_payment_bank_account_number' => $supplier_payment_bank_account_number,
			                                'supplier_date_account_last_modified' => 'now()'
			                                );
		      		if ($reserve_amount_permission)	$sql_data_array['supplier_reserve_amount'] = $supplier_reserve_amount;

		      		if (($enable_withdraw_permission && ($supplier_disable_withdrawal == '0')) || 
		      			($disable_withdraw_permission && ($supplier_disable_withdrawal == '1'))) {
		      			$sql_data_array['supplier_disable_withdrawal'] = $supplier_disable_withdrawal;
		      		}
					
					if (($enable_withdraw_permission || $disable_withdraw_permission) && $supplier_disable_withdrawal != $supplier_disable_withdrawal_history) {
						$supplier_remarks_select_sql = "SELECT supplier_remarks FROM " . TABLE_SUPPLIER . " WHERE supplier_id = '" . (int)$supplier_id . "'";
						$supplier_remarks_result_sql = tep_db_query($supplier_remarks_select_sql);
						$supplier_remarks_row = tep_db_fetch_array($supplier_remarks_result_sql);
						$supplier_remarks = htmlentities(tep_db_prepare_input($supplier_remarks_row['supplier_remarks']));
						$old_string = ((int)$supplier_disable_withdrawal_history == '1') ? 'On' : 'Off';
						$new_string = ((int)$supplier_disable_withdrawal == '1') ? 'On' : 'Off';
						$changeBy = date('Y-m-d H:i:s') . " (by " . $login_email_address . ")";
						$changes = "Changes made:\nDisable Withdrawal: " . $old_string . " --> " . $new_string;
						$new_supplier_remarks = $supplier_remarks . "\n" . $changeBy . "\n" . $changes;

						$sql_data_array['supplier_remarks'] = $new_supplier_remarks;
					}
	
		      		tep_db_perform(TABLE_SUPPLIER, $sql_data_array, 'update', "supplier_id = '" . (int)$supplier_id . "'");
	
					// save supplier preferences
			    	$preferences_setting_array = array(KEY_SP_TIME_ZONE, KEY_SP_LANG);
					tep_db_query("DELETE FROM " . TABLE_SUPPLIER_PREFERENCES . " WHERE suppliers_id = '" . (int)$supplier_id . "' AND supplier_preferences_key IN ('".implode("', '", $preferences_setting_array)."')");
					
					foreach ($preferences_setting_array as $pref_key) {
						$preferences_setting_data_array = array('suppliers_id' => (int)$supplier_id,
																'supplier_preferences_key' => $pref_key,
															 	'supplier_preferences_value' => tep_db_prepare_input($_POST["$pref_key"])
															 );
						tep_db_perform(TABLE_SUPPLIER_PREFERENCES, $preferences_setting_data_array);
					}
	
					$messageStack->add_session(sprintf(SUCCESS_SUPPLIER_UPDATED, $supplier_email_address), 'success');
					tep_redirect(tep_href_link(FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('sID', 'action', 'subaction')) . 'sID=' . $supplier_id . '&page=' . $_GET['page']));
				}
			}
			if ($error) {
				$error_section = "account";
			}
			break;
		case "update_remark":
			if ($edit_remark_permission) {
	        	$supplier_remarks = htmlentities(tep_db_prepare_input($HTTP_POST_VARS['supplier_remarks']));
	        	$supplier_email_address = tep_db_prepare_input($HTTP_POST_VARS['supplier_email_address']);
	
				$sql_data_array = array('supplier_remarks' => $supplier_remarks,
	                            		'supplier_date_account_last_modified' => 'now()');
				tep_db_perform(TABLE_SUPPLIER, $sql_data_array, 'update', "supplier_id = '" . (int)$supplier_id . "'");
	
				$messageStack->add_session(sprintf(SUCCESS_SUPPLIER_UPDATED, $supplier_email_address), 'success');
	        	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('page','sID', 'action', 'subaction')) . 'sID=' . $supplier_id . '&page=' . $_GET['page']));
			}
			if ($error) {
				$error_section = "remark";
			}
			break;
      	case 'confirm_delete_supplier':
      		$supplier_email_address_select_sql = "	SELECT supplier_email_address FROM " . TABLE_SUPPLIER . " WHERE supplier_id ='" . (int)$_GET['sID'] . "'";
      		$supplier_email_address_result_sql = tep_db_query($supplier_email_address_select_sql);
      		$supplier_email_address_row = tep_db_fetch_array($supplier_email_address_result_sql);
      		
      		$game_char_log_login_user_update_sql_array = array('game_char_log_login_user' => $supplier_email_address_row['supplier_email_address']);
      		
      		tep_db_perform(TABLE_GAME_CHAR_LOG, $game_char_log_login_user_update_sql_array, 'update', "game_char_log_user_role = 'supplier' AND game_char_log_login_user ='" . (int)$_GET['sID'] . "'");
      		
	        $supplier_delete_sql = "DELETE FROM " . TABLE_SUPPLIER . " WHERE supplier_id = '" . (int)$_GET['sID'] . "'";
      		tep_db_query($supplier_delete_sql);
      		
      		$supplier_tasks_setting_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_TASKS_SETTING . " WHERE suppliers_id = '" . (int)$_GET['sID'] . "'";
      		tep_db_query($supplier_tasks_setting_delete_sql);
      		
      		$supplier_purchase_mode_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PURCHASE_MODES . " WHERE supplier_id = '" . (int)$_GET['sID'] . "'";
			tep_db_query($supplier_purchase_mode_delete_sql);
			
			$supplier_preferences_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PREFERENCES . " WHERE suppliers_id = '" . (int)$_GET['sID'] . "'";
			tep_db_query($supplier_preferences_delete_sql);
			
			$supplier_crew_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_CREW . " WHERE supplier_id = '" . (int)$_GET['sID'] . "'";
			tep_db_query($supplier_crew_delete_sql);
			
      		$messageStack->add_session(SUCCESS_SUPPLIER_DELETED, 'success');
      		
	        tep_redirect(tep_href_link(FILENAME_SUPPLIERS_LIST, 'page=' . $_GET['page']));
	        
        	break;
        case "update_list":
			switch ($HTTP_POST_VARS["batch_action"]) {
				case "PurchaseMode":
					for ($i=0; $i < count($HTTP_POST_VARS["supplier_batch"]); $i++) {
						if (isset($HTTP_POST_VARS["supplier_batch"][$i]) && isset($HTTP_POST_VARS["radioStatus"][$HTTP_POST_VARS["supplier_batch"][$i]])) {
							if (is_array($HTTP_POST_VARS["radioStatus"][$HTTP_POST_VARS["supplier_batch"][$i]]) && count($HTTP_POST_VARS["radioStatus"][$HTTP_POST_VARS["supplier_batch"][$i]])) {
								foreach ($HTTP_POST_VARS["radioStatus"][$HTTP_POST_VARS["supplier_batch"][$i]] as $list_id => $purchase_mode) {
									$purchase_mode_info_select_sql = "	SELECT ppl.products_purchases_lists_name, spm.supplier_purchase_mode 
						    											FROM " . TABLE_SUPPLIER_PURCHASE_MODES . " AS spm 
						    											INNER JOIN " . TABLE_PRODUCTS_PURCHASES_LISTS . " AS ppl 
						    												ON spm.products_purchases_lists_id=ppl.products_purchases_lists_id 
						    											WHERE spm.supplier_id='" . (int)$HTTP_POST_VARS["supplier_batch"][$i] . "' AND spm.products_purchases_lists_id = '" . (int)$list_id . "'";
    								$purchase_mode_info_result_sql = tep_db_query($purchase_mode_info_select_sql);
									$purchase_mode_info_row = tep_db_fetch_array($purchase_mode_info_result_sql);
									
									$purchase_mode_update_sql = "	UPDATE " . TABLE_SUPPLIER_PURCHASE_MODES . " 
				      												SET supplier_purchase_mode = '" . tep_db_input($purchase_mode) . "' 
				      												WHERE supplier_id='" . (int)$HTTP_POST_VARS["supplier_batch"][$i] . "' AND products_purchases_lists_id = '" . (int)$list_id . "'";
									tep_db_query($purchase_mode_update_sql);
									
									if ($purchase_mode_info_row["supplier_purchase_mode"] == 'STATUS_OFF') {
										if ($purchase_mode == 'STATUS_GROUP' || $purchase_mode == 'STATUS_ON') {
				  							$email = 	'New supplier list ('.$purchase_mode_info_row["products_purchases_lists_name"].') is available now!' . "\n" . 'Please login from <a href="'.tep_supplier_href_link('login.php', '', 'SSL').'">'.tep_supplier_href_link('login.php', '', 'SSL').'</a> to submit the list.'. "\n\n" . 
				  										EMAIL_TEXT_CLOSING .
														EMAIL_FOOTER;
											
											$supplier_profile_select_sql = "SELECT supplier_gender, supplier_firstname, supplier_lastname, supplier_email_address FROM " . TABLE_SUPPLIER . " WHERE supplier_id = '" . tep_db_input((int)$HTTP_POST_VARS["supplier_batch"][$i]) . "'";
											$supplier_profile_result_sql = tep_db_query($supplier_profile_select_sql);
											if ($supplier_profile_row = tep_db_fetch_array($supplier_profile_result_sql)) {
												$email_firstname = $supplier_profile_row["supplier_firstname"];
												$email_lastname = $supplier_profile_row["supplier_lastname"];
												
												$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $supplier_profile_row['supplier_gender']);
												
												$email = $email_greeting . $email;
												tep_mail($email_firstname.' '.$email_lastname, $supplier_profile_row["supplier_email_address"], EMAIL_TEXT_SUBJECT_NEW_LIST, $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
											}
										}
									}
								}
							}
		    			}
					}
					break;
				case "Active":
					for ($i=0; $i < count($HTTP_POST_VARS["supplier_batch"]); $i++) {
						if (isset($HTTP_POST_VARS["supplier_batch"][$i])) {
							$supplier_status_update_sql = "	UPDATE " . TABLE_SUPPLIER . " 
		      												SET supplier_status = '1' 
		      												WHERE supplier_id='" . (int)$HTTP_POST_VARS["supplier_batch"][$i] . "'";
							tep_db_query($supplier_status_update_sql);
		    			}
					}
					break;
				case "Inactive":
					for ($i=0; $i < count($HTTP_POST_VARS["supplier_batch"]); $i++) {
						if (isset($HTTP_POST_VARS["supplier_batch"][$i])) {
							$supplier_status_update_sql = "	UPDATE " . TABLE_SUPPLIER . " 
		      												SET supplier_status = '0' 
		      												WHERE supplier_id='" . (int)$HTTP_POST_VARS["supplier_batch"][$i] . "'";
							tep_db_query($supplier_status_update_sql);
		    			}
					}
					break;
				case "Delete":
					for ($i=0; $i < count($HTTP_POST_VARS["supplier_batch"]); $i++) {
						if (isset($HTTP_POST_VARS["supplier_batch"][$i])) {
							$supplier_delete_sql = "DELETE FROM " . TABLE_SUPPLIER . " WHERE supplier_id = '" . (int)$HTTP_POST_VARS["supplier_batch"][$i] . "'";
      						tep_db_query($supplier_delete_sql);
      						
      						$supplier_purchase_mode_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PURCHASE_MODES . " WHERE supplier_id = '" . (int)$_GET['sID'] . "'";
							tep_db_query($supplier_purchase_mode_delete_sql);
		    			}
					}
					break;
				case "DeductOrder":
					/*
					for ($i=0; $i < count($HTTP_POST_VARS["supplier_batch"]); $i++) {
						if (isset($HTTP_POST_VARS["supplier_batch"][$i]) && isset($HTTP_POST_VARS["orders"][$HTTP_POST_VARS["supplier_batch"][$i]])) {
							$deduct_order_update_sql = "	UPDATE " . TABLE_SUPPLIER . " 
		      												SET supplier_deduct_from_order_id = '" . (int)$HTTP_POST_VARS["orders"][$HTTP_POST_VARS["supplier_batch"][$i]] . "' 
		      												WHERE supplier_id='" . (int)$HTTP_POST_VARS["supplier_batch"][$i] . "'";
							tep_db_query($deduct_order_update_sql);
		    			}
					}
					*/
					break;
			}
			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_LIST, 'page=' . $_GET['page']));
        	break;
	}
	if ($error) {
		$sInfo = new objectInfo($HTTP_POST_VARS, false);
		$processed = true;
	}
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/xmlhttp.js"></script>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/customer_xmlhttp.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
            			<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
<?
if ($action == "edit_supplier") {
	if (!$processed) {
		$supplier_select_sql = "SELECT s.*, sg.supplier_groups_name, st.custom_products_type_id 
								FROM " . TABLE_SUPPLIER . " AS s 
								LEFT JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
									ON (s.supplier_groups_id=sg.supplier_groups_id) 
								LEFT JOIN " . TABLE_SUPPLIER_TASKS_SETTING . " AS st 
									ON (st.suppliers_id=s.supplier_id) 
								WHERE s.supplier_id = '" . (int)$_REQUEST["sID"] . "'";
		
		$supplier_result_sql = tep_db_query($supplier_select_sql);
		$supplier_row = tep_db_fetch_array($supplier_result_sql);
		
		$pref_setting_select_sql = "SELECT supplier_preferences_key, supplier_preferences_value FROM " . TABLE_SUPPLIER_PREFERENCES . " WHERE suppliers_id = '" . (int)$_REQUEST["sID"] . "'";
		$pref_setting_result_sql = tep_db_query($pref_setting_select_sql);
		
		while ($pref_setting_row = tep_db_fetch_array($pref_setting_result_sql)) {
			$supplier_row[$pref_setting_row["supplier_preferences_key"]] = $pref_setting_row["supplier_preferences_value"];
		}
		
		$supplier_tasks_info_select_sql = "SELECT custom_products_type_id, supplier_tasks_allocation_slots, supplier_tasks_allocation_physical_slots FROM " . TABLE_SUPPLIER_TASKS_SETTING . " WHERE suppliers_id ='" . (int)$_REQUEST["sID"] . "'";
		$supplier_tasks_info_result_sql = tep_db_query($supplier_tasks_info_select_sql);
		while ($supplier_tasks_info_row = tep_db_fetch_array($supplier_tasks_info_result_sql)) {
			$supplier_row["task_max"][$supplier_tasks_info_row["custom_products_type_id"]] = $supplier_tasks_info_row["supplier_tasks_allocation_slots"];
			$supplier_row["slot_max"][$supplier_tasks_info_row["custom_products_type_id"]] = $supplier_tasks_info_row["supplier_tasks_allocation_physical_slots"];
		}
		
		$sInfo = new objectInfo($supplier_row, false);
	}
	
	$time_zone_array = array();
	if (isset($GLOBALS['_DATE_TIMEZONE_DATA']) && count($GLOBALS['_DATE_TIMEZONE_DATA'])) {
		foreach($GLOBALS['_DATE_TIMEZONE_DATA'] as $tid => $tres) {
			if (preg_match('/(?:GMT)[+-]?(\d*?)$/i', $tid, $regs)) {
				if ($regs[1] > 0 || $tid == 'GMT') {
					$time_zone_array[] = array('id' => $tid, 'text' => $tres["shortname"]);
				}
			}
		}
	}
	
	$pref_language_array = array();
	$languages_query = tep_db_query("select languages_id, name from " . TABLE_SUPPLIER_LANGUAGES . " order by sort_order");
	while ($languages = tep_db_fetch_array($languages_query)) {
		$pref_language_array[] = array(	'id' => $languages['languages_id'],
										'text' => $languages['name']);
	}
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
	if (($view_supplier_permission || $edit_supplier_permission) && (!$error || $error_section=='info')) {
		echo tep_draw_form('supplier_form', FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('subaction','sID')) . 'subaction=update_supplier', 'post', 'onSubmit="return supplier_check_form();"');
		echo tep_draw_hidden_field("sID", $_REQUEST["sID"]);
?>
					<tr>
        				<td width="100%">
        					<fieldset class="selectedFieldSet">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="customerFormAreaTitle"><?=CATEGORY_PERSONAL?></td>
								</tr>
								<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
<?		if (ACCOUNT_GENDER == 'true') { ?>
		          							<tr>
				            					<td class="main" width="20%"><?=ENTRY_GENDER?></td>
				            					<td class="main">
	<? 		if ($error == true) {
	      		if ($entry_gender_error == true) {
        			echo tep_draw_radio_field('supplier_gender', 'm', false, $sInfo->supplier_gender) . '&nbsp;&nbsp;' . MALE . '&nbsp;&nbsp;' . tep_draw_radio_field('customers_gender', 'f', false, $sInfo->supplier_gender) . '&nbsp;&nbsp;' . FEMALE . '&nbsp;' . ENTRY_GENDER_ERROR;
	      		} else {
	        		echo ($sInfo->supplier_gender == 'm') ? MALE : FEMALE;
	        		echo tep_draw_hidden_field('supplier_gender');
	      		}
	    	} else {
	      		echo tep_draw_radio_field('supplier_gender', 'm', false, $sInfo->supplier_gender) . '&nbsp;&nbsp;' . MALE . '&nbsp;&nbsp;' . tep_draw_radio_field('supplier_gender', 'f', false, $sInfo->supplier_gender) . '&nbsp;&nbsp;' . FEMALE;
	    	}
?>												</td>
				          					</tr>
<?	} ?>
						      				<tr>
            									<td class="main"><?=ENTRY_FIRST_NAME?></td>
            									<td class="main">
<?		if ($error == true) {
	    	if ($entry_firstname_error == true) {
	      		echo tep_draw_input_field('supplier_firstname', $sInfo->supplier_firstname, 'maxlength="32" id="supplier_firstname"') . '&nbsp;' . ENTRY_FIRST_NAME_ERROR;
	    	} else {
	      		echo $sInfo->supplier_firstname . tep_draw_hidden_field('supplier_firstname');
	    	}
	  	} else {
	    	echo tep_draw_input_field('supplier_firstname', $sInfo->supplier_firstname, 'maxlength="32" id="supplier_firstname"', true);
	  	}
?>
												</td>
          									</tr>
			      							<tr>
			            						<td class="main"><?=ENTRY_LAST_NAME?></td>
			            						<td class="main">
<?		if ($error == true) {
	    	if ($entry_lastname_error == true) {
	      		echo tep_draw_input_field('supplier_lastname', $sInfo->supplier_lastname, 'maxlength="32" id="supplier_lastname"') . '&nbsp;' . ENTRY_LAST_NAME_ERROR;
	    	} else {
	      		echo $sInfo->supplier_lastname . tep_draw_hidden_field('supplier_lastname');
	    	}
	  	} else {
	    	echo tep_draw_input_field('supplier_lastname', $sInfo->supplier_lastname, 'maxlength="32" id="supplier_lastname"', true);
	  	}
?>
												</td>
          									</tr>
<?		if (ACCOUNT_DOB == 'true') { ?>
			          						<tr>
			            						<td class="main"><?=ENTRY_DATE_OF_BIRTH?></td>
			            						<td class="main">
	<?		if ($error == true) {
	      		if ($entry_date_of_birth_error == true) {
	        		echo tep_draw_input_field('supplier_dob', tep_date_short($sInfo->supplier_dob), 'maxlength="10" id="supplier_dob"') . '&nbsp;' . ENTRY_DATE_OF_BIRTH_ERROR;
	      		} else {
	        		echo $sInfo->supplier_dob . tep_draw_hidden_field('supplier_dob');
	      		}
	    	} else {
	      		echo tep_draw_input_field('supplier_dob', tep_date_short($sInfo->supplier_dob), 'maxlength="10" id="supplier_dob"', true);
	    	}
?>												</td>
          									</tr>
<?		} ?>
											<tr>
			            						<td class="main"><?=ENTRY_EMAIL_ADDRESS?></td>
			            						<td class="main">
<?		if ($error == true) {
	    	if ($entry_email_address_error == true) {
	      		echo tep_draw_input_field('supplier_email_address', $sInfo->supplier_email_address, 'maxlength="96" id="supplier_email_address"') . '&nbsp;' . ENTRY_EMAIL_ADDRESS_ERROR;
	    	} else if ($entry_email_address_check_error == true) {
	      		echo tep_draw_input_field('supplier_email_address', $sInfo->supplier_email_address, 'maxlength="96" id="supplier_email_address"') . '&nbsp;' . ENTRY_EMAIL_ADDRESS_CHECK_ERROR;
	    	} else if ($entry_email_address_exists == true) {
	      		echo tep_draw_input_field('supplier_email_address', $sInfo->supplier_email_address, 'maxlength="96" id="supplier_email_address"') . '&nbsp;' . ENTRY_EMAIL_ADDRESS_ERROR_EXISTS;
	    	} else {
	      		echo $sInfo->supplier_email_address . tep_draw_hidden_field('supplier_email_address');
	    	}
	  	} else {
	    	echo tep_draw_input_field('supplier_email_address', $sInfo->supplier_email_address, 'maxlength="96" id="supplier_email_address"', true);
	  	}
			?>									</td>
				          					</tr>
				        				</table>
				        			</td>
				      			</tr>
				      			<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_ADDRESS?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				          					<tr>
				            					<td class="main" width="20%"><?=ENTRY_STREET_ADDRESS?></td>
				            					<td class="main">
<?		if ($error == true) {
	  		if ($entry_street_address_error == true) {
	   			echo tep_draw_input_field('supplier_street_address', $sInfo->supplier_street_address, 'maxlength="64" id="supplier_street_address"') . '&nbsp;' . ENTRY_STREET_ADDRESS_ERROR;
	  		} else {
	      		echo $sInfo->supplier_street_address . tep_draw_hidden_field('supplier_street_address');
	    	}
	  	} else {
	    	echo tep_draw_input_field('supplier_street_address', $sInfo->supplier_street_address, 'maxlength="64" id="supplier_street_address"', true);
	  	}
?>
												</td>
			          						</tr>
<?		if (ACCOUNT_SUBURB == 'true') { ?>
			          						<tr>
			            						<td class="main"><?=ENTRY_SUBURB?></td>
			            						<td class="main">
	<? 		if ($error == true) {
	      		if ($entry_suburb_error == true) {
	        		echo tep_draw_input_field('supplier_suburb', $sInfo->supplier_suburb, 'maxlength="64"') . '&nbsp;' . ENTRY_SUBURB_ERROR;
	      		} else {
	        		echo $sInfo->supplier_suburb . tep_draw_hidden_field('supplier_suburb');
	      		}
	    	} else {
	      		echo tep_draw_input_field('supplier_suburb', $sInfo->supplier_suburb, 'maxlength="64"');
	    	}
?>
												</td>
          									</tr>
<?		} ?>
			          						<tr>
				            					<td class="main"><?=ENTRY_POST_CODE?></td>
				            					<td class="main">
<?		if ($error == true) {
	    	if ($entry_post_code_error == true) {
	      		echo tep_draw_input_field('supplier_postcode', $sInfo->supplier_postcode, 'maxlength="10" id="supplier_postcode"') . '&nbsp;' . ENTRY_POST_CODE_ERROR;
	    	} else {
	      		echo $sInfo->supplier_postcode . tep_draw_hidden_field('supplier_postcode');
	    	}
	  	} else {
	    	echo tep_draw_input_field('supplier_postcode', $sInfo->supplier_postcode, 'maxlength="10" id="supplier_postcode"', true);
	  	}
?>
												</td>
				          					</tr>
				          					<tr>
	            								<td class="main"><?=ENTRY_CITY?></td>
	            								<td class="main">
<?		if ($error == true) {
	   		if ($entry_city_error == true) {
	   			echo tep_draw_input_field('supplier_city', $sInfo->supplier_city, 'maxlength="32" id="supplier_city"') . '&nbsp;' . ENTRY_CITY_ERROR;
	  		} else {
	   			echo $sInfo->supplier_city . tep_draw_hidden_field('supplier_city');
	  		}
	  	} else {
	   		echo tep_draw_input_field('supplier_city', $sInfo->supplier_city, 'maxlength="32" id="supplier_city"', true);
	  	}
?>
												</td>
          									</tr>
          									<tr>
				            					<td class="main"><?=ENTRY_COUNTRY?></td>
				            					<td class="main">
<?
  		if ($error == true) {
		 	if ($entry_country_error == false) {
	   			echo tep_get_country_name($sInfo->supplier_country_id) . tep_draw_hidden_field('supplier_country_id');
	   		} else {
	   			echo tep_get_country_list('supplier_country_id') . '&nbsp;' . '<span class="requiredInfo">' . ENTRY_COUNTRY_ERROR . '</span>';
	   		}
		} else {
			if ($country_selection_mode == 'xmlhttp_mode') {
				echo tep_get_country_list('supplier_country_id', $sInfo->supplier_country_id, ' id="supplier_country_id" onChange="refreshDynamicSelectOptions(this, \'state_div\', \'supplier_state\', \''.(int)$languages_id.'\', true);"') . '&nbsp;' . (tep_not_null(ENTRY_COUNTRY_TEXT) ? '<span class="requiredInfo">' . ENTRY_COUNTRY_ERROR . '</span>': '');				
			}
	  	}
?>
												</td>
	          								</tr>
<?		if (ACCOUNT_STATE == 'true') { ?>
				          					<tr>
				            					<td class="main"><?=ENTRY_STATE?></td>
				            					<td class="main">
				            						<div id="state_div" style="float:left;">
<?
	    	$zones_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
	    	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$sInfo->supplier_country_id . "' ORDER BY zone_name";
	    	$zones_result_sql = tep_db_query($zones_select_sql);
	    	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
	      		$zones_array[] = array('id' => $zones_row['zone_id'], 'text' => $zones_row['zone_name']);
	    	}
	    	//$supplier_zone_name = tep_get_zone_name($sInfo->supplier_country_id, $supplier_state, $sInfo->supplier_state);    	
	    	if ($error == true) {
	    		if ($entry_state_error == true) {
					if ($entry_state_has_zones == true) {
		    			echo tep_draw_pull_down_menu('supplier_state', $zones_array, $sInfo->supplier_zone_id). '&nbsp;' . ENTRY_STATE_ERROR;
		    		} else {	    		
		    			echo tep_draw_input_field('supplier_state', $sInfo->supplier_state) . '&nbsp;' . ENTRY_STATE_ERROR;
		    		}
		    	} else {
		    		if($supplier_zone_name){
		    			echo $supplier_zone_name . tep_draw_hidden_field('supplier_state');
		    		} else {
		    			echo tep_draw_hidden_field('supplier_state');
		    		}
		    	}
	    	} else {
	    		if (count($zones_array) > 1) {
	    			echo tep_draw_pull_down_menu('supplier_state', $zones_array, $sInfo->supplier_zone_id, 'id="supplier_state"');
	      		} else {
		    	    echo tep_draw_input_field('supplier_state', $sInfo->supplier_state, 'id="supplier_state"');
				}			
			}
			echo '									</div>';
?>
												</td>
         									</tr>
<?		} ?>
										</table>
									</td>
          						</tr>
          						<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_CONTACT?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				          					<tr>
				            					<td class="main" width="20%"><?=ENTRY_TELEPHONE_NUMBER?></td>
				            					<td class="main">
<?		if ($error == true) {
	    	if ($entry_telephone_error == true) {
	     		echo tep_draw_input_field('supplier_telephone', $sInfo->supplier_telephone, 'maxlength="32" id="supplier_telephone"') . '&nbsp;' . ENTRY_TELEPHONE_NUMBER_ERROR;
	    	} else {
	     		echo $sInfo->supplier_telephone . tep_draw_hidden_field('supplier_telephone');
	    	}
	  	} else {
	    	echo tep_draw_input_field('supplier_telephone', $sInfo->supplier_telephone, 'maxlength="32" id="supplier_telephone"', true);
	  	}
?>
												</td>
			  								</tr>
			  								<tr>
				            					<td class="main"><?=ENTRY_FAX_NUMBER?></td>
				            					<td class="main">
<?		if ($processed == true) {
	    	echo $sInfo->supplier_fax . tep_draw_hidden_field('supplier_fax');
	  	} else {
	    	echo tep_draw_input_field('supplier_fax', $sInfo->supplier_fax, 'maxlength="32"');
	  	}
?>
												</td>
	          								</tr>
	          								<tr>
				            					<td class="main"><?=ENTRY_QQ_NUMBER?></td>
				            					<td class="main">
<?		if ($error == true) {
	    	if ($entry_qq_error == true) {
	     		echo tep_draw_input_field('supplier_qq', $sInfo->supplier_qq, 'maxlength="32" id="supplier_qq"') . '&nbsp;' . ENTRY_QQ_NUMBER_ERROR;
	    	} else {
	     		echo $sInfo->supplier_qq . tep_draw_hidden_field('supplier_qq');
	    	}
	  	} else {
	    	echo tep_draw_input_field('supplier_qq', $sInfo->supplier_qq, 'maxlength="32" id="supplier_qq"');
	  	}
?>
												</td>
	          								</tr>
	          								<tr>
				            					<td class="main"><?=ENTRY_MSN_ADDRESS?></td>
				            					<td class="main">
<?		if ($error == true) {
	    	if ($entry_msn_error == true) {
	     		echo tep_draw_input_field('supplier_msn', $sInfo->supplier_msn, 'maxlength="32" id="supplier_msn"') . '&nbsp;' . ENTRY_MSN_ADDRESS_ERROR;
	    	} else {
	     		echo $sInfo->supplier_msn . tep_draw_hidden_field('supplier_msn');
	    	}
	  	} else {
	    	echo tep_draw_input_field('supplier_msn', $sInfo->supplier_msn, 'maxlength="32" id="supplier_msn"');
	  	}
?>
												</td>
	          								</tr>
										</table>
									</td>
								</tr>
								<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_OPTIONS?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
				        					<tr>
				            					<td class="main" width="20%"><?=ENTRY_SUPPLIER_CODE?></td>
				            					<td class="main">
<?		if ($error == true) {
			if ($supplier_code_error == true) {
	      		echo tep_draw_input_field('supplier_code', $sInfo->supplier_code, 'maxlength="64" id="supplier_code"') . '&nbsp;' . ENTRY_SUPPLIER_CODE_ERROR;
	    	} else if ($supplier_code_exists == true) {
	    		echo tep_draw_input_field('supplier_code', $sInfo->supplier_code, 'maxlength="64" id="supplier_code"') . '&nbsp;' . ENTRY_SUPPLIER_CODE_ERROR_EXISTS;
	    	} else {
	      		echo $sInfo->supplier_code . tep_draw_hidden_field('supplier_code');
	    	}
	  	} else {
	    	echo tep_draw_input_field('supplier_code', $sInfo->supplier_code, 'maxlength="64" id="supplier_code"', true);
	  	}
?>
												</td>
			  								</tr>
				          					<tr>
	       										<td class="main"><?=ENTRY_SUPPLIERS_GROUPS_NAME?></td>
<?		$groups_array = array (	array(	'text' => TEXT_NOT_ASSIGNED_GROUP,
									'id' => 0)
								);
		$groups_query = tep_db_query("select supplier_groups_id, supplier_groups_name from " . TABLE_SUPPLIER_GROUPS ." order by supplier_groups_name");
		while($groups = tep_db_fetch_array($groups_query)) {
			$groups_array[] = array('text' => $groups['supplier_groups_name'],
									'id' => $groups['supplier_groups_id']);
		}
?>
			        							<td class="main"><?=tep_draw_pull_down_menu('supplier_groups_id', $groups_array, $sInfo->supplier_groups_id)?></td>
			     							</tr>
				            			</table>
				        			</td>
				      			</tr>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
<? 		if ($edit_supplier_permission) { ?>
			      				<tr>
				        			<td align="right" class="main"><?=tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link(FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction'))) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?></td>
				      			</tr>
<? 		} ?>
				      		</table>
				      		</form>
				      		</fieldset>
				      	</td>
					</tr>
<?	} ?>
	      			<tr>
	      				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
<? if (($view_account_permission || $edit_account_permission) && (!$error || $error_section=='account')) { ?>
	      			<tr>
	      				<td>
				      		<fieldset class="selectedFieldSet">
<?
				      		echo tep_draw_form('supplier_task', FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('subaction','sID')) . 'subaction=update_task', 'post', 'onSubmit="return supplier_check_form();"');
				      		echo tep_draw_hidden_field('supplier_email_address', $sInfo->supplier_email_address);
				      		echo tep_draw_hidden_field("sID", $_REQUEST["sID"]);
?>				      		
				      		<table border="0" width="100%" cellspacing="0" cellpadding="2">
				      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
<? 		if ($supplier_cp_payment_detail_permission || $supplier_cp_slots) {?>
				      			<tr>
				        			<td class="customerFormAreaTitle"><?=CATEGORY_TASKS?></td>
				      			</tr>
				      			<tr>
				        			<td class="formArea">
				        				<table border="0" width="100%" cellspacing="2" cellpadding="2">
<?
			$task_select_sql = "SELECT cp.custom_products_type_id, cp.custom_products_type_name, sts.supplier_tasks_allocation_slots, sts.supplier_tasks_allocation_physical_slots, sts.supplier_tasks_ratio, sts.use_remote_account_login_api 
								FROM " . TABLE_CUSTOM_PRODUCTS_TYPE . " AS cp
								LEFT JOIN " . TABLE_SUPPLIER_TASKS_SETTING . " AS sts
									ON (cp.custom_products_type_id=sts.custom_products_type_id AND suppliers_id = '" . ($error==true ? $sInfo->sID : $sInfo->supplier_id) . "')
								WHERE 1";
			$task_result_sql = tep_db_query($task_select_sql);
			while ($task_row = tep_db_fetch_array($task_result_sql)) {
?>
					        				<tr>
					        					<td class="main" width="20%"><?=$task_row['custom_products_type_name'].':'?></td>
					        					<td class="main" colspan="2">
<?
				if ($error == true) {
		      		if ($task_error[$task_row['custom_products_type_id']] == true) {
		      			echo tep_draw_radio_field('task['.$task_row['custom_products_type_id'].']', 1, false, $sInfo->task[$task_row['custom_products_type_id']]) . '&nbsp;&nbsp;' . ACTIVE . '&nbsp;&nbsp;' . tep_draw_radio_field('task['.$task_row['custom_products_type_id'].']', 0, false, $sInfo->task[$task_row['custom_products_type_id']]) . '&nbsp;&nbsp;' . NOT_ACTIVE . '&nbsp;' . ENTRY_TASK_ERROR;
		      		} else {
		        		echo ($sInfo->task[$task_row['custom_products_type_id']] == '1') ? ACTIVE : NOT_ACTIVE;
		        		echo tep_draw_hidden_field('task['.$task_row['custom_products_type_id'].']', $sInfo->task[$task_row['custom_products_type_id']]);
		      		}
		    	} else {
		      		echo tep_draw_radio_field('task['.$task_row['custom_products_type_id'].']', '1', (is_null($task_row['supplier_tasks_allocation_slots']) ? false : true), '', 'onClick="enabletasksfield('.$task_row['custom_products_type_id'].');"') . '&nbsp;&nbsp;' . ACTIVE . '&nbsp;&nbsp;' . tep_draw_radio_field('task['.$task_row['custom_products_type_id'].']', '0', (is_null($task_row['supplier_tasks_allocation_slots']) ? true : false), '', 'onClick="disabletasksfield('.$task_row['custom_products_type_id'].');"') . '&nbsp;&nbsp;' . NOT_ACTIVE;
		    	}
?>
					        					</td>
					        				</tr>
					        				<tr>
					        					<td class="main"></td>
					        					<td class="main" width="13%">
<?
			echo ENTRY_SUPPLIER_MAXIMUM_TASKS;
?>
												</td>
												<td>
<?
				if ($error == true) {
			    	if ($task_max_error[$task_row['custom_products_type_id']] == true) {
			     		echo tep_draw_input_field('task_max['.$task_row['custom_products_type_id'].']', $sInfo->task_max[$task_row['custom_products_type_id']], 'maxlength="32"') . '&nbsp;' . ENTRY_SUPPLIER_MAXIMUM_TASKS_ERROR;
			    	} else {
			    		if ($sInfo->task[$task_row['custom_products_type_id']] == '1') {
			     		 	echo $sInfo->task_max[$task_row['custom_products_type_id']] . tep_draw_hidden_field('task_max['.$task_row['custom_products_type_id'].']', $sInfo->task_max[$task_row['custom_products_type_id']]);
			    		} else {
			    			echo TEXT_NOT_AVAILABLE . tep_draw_hidden_field('task_max['.$task_row['custom_products_type_id'].']');	
			    		}
			    	}
			  	} else {
			    	echo tep_draw_input_field('task_max['.$task_row['custom_products_type_id'].']', $task_row['supplier_tasks_allocation_slots'], 'maxlength="32" id="task_max['.$task_row['custom_products_type_id'].']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ' . (is_null($task_row['supplier_tasks_allocation_slots']) ? 'disabled' : ''));
			  	}
?>
					        					</td>
					        				</tr>
					        				<tr>
					        					<td class="main"></td>
					        					<td class="main" width="20%"><?=ENTRY_SUPPLIER_AVAILABLE_SLOT?></td>
												<td>
<?
				if ($error == true) {
			    	if ($slot_max_error[$task_row['custom_products_type_id']] == true) {
			     		echo tep_draw_input_field('slot_max['.$task_row['custom_products_type_id'].']', $sInfo->slot_max[$task_row['custom_products_type_id']], 'maxlength="32"') . '&nbsp;' . ENTRY_SUPPLIER_AVAILABLE_SLOT_ERROR;
			    	} else {
			    		if ($sInfo->task[$task_row['custom_products_type_id']] == '1') {
			     		 	echo $sInfo->slot_max[$task_row['custom_products_type_id']] . tep_draw_hidden_field('slot_max['.$task_row['custom_products_type_id'].']', $sInfo->slot_max[$task_row['custom_products_type_id']]);
			    		} else {
			    			echo TEXT_NOT_AVAILABLE . tep_draw_hidden_field('slot_max['.$task_row['custom_products_type_id'].']');	
			    		}
			    	}
			  	} else {
			    	echo tep_draw_input_field('slot_max['.$task_row['custom_products_type_id'].']', $task_row['supplier_tasks_allocation_physical_slots'], 'maxlength="32" id="slot_max['.$task_row['custom_products_type_id'].']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ' . (is_null($task_row['supplier_tasks_allocation_physical_slots']) ? 'disabled' : ''));
			  	}
?>
												</td>
											</tr>
					        				<tr>
					        					<td class="main"></td>
					        					<td class="main"><?=ENTRY_SUPPLIER_RATIO?></td>
												<td>
<?
				if ($error == true) {
			    	if ($task_ratio_error[$task_row['custom_products_type_id']] == true) {
			     		echo tep_draw_input_field('task_ratio['.$task_row['custom_products_type_id'].']', $sInfo->task_ratio[$task_row['custom_products_type_id']], 'maxlength="32"') . '&nbsp;';
			    	} else {
			    		if ($sInfo->task[$task_row['custom_products_type_id']] == '1') {
			     		 	echo $sInfo->task_ratio[$task_row['custom_products_type_id']] . tep_draw_hidden_field('task_ratio['.$task_row['custom_products_type_id'].']', $sInfo->task_ratio[$task_row['custom_products_type_id']]);
			    		} else {
			    			echo TEXT_NOT_AVAILABLE . tep_draw_hidden_field('task_ratio['.$task_row['custom_products_type_id'].']');
			    		}
			    	}
			  	} else {
			    	echo tep_draw_input_field('task_ratio['.$task_row['custom_products_type_id'].']', $task_row['supplier_tasks_ratio'], 'maxlength="32" id="task_ratio['.$task_row['custom_products_type_id'].']" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ' . (is_null($task_row['supplier_tasks_ratio']) ? 'disabled' : ''));
			  	}
?>
					        					</td>
					        				</tr>
					        				<tr>
					        					<td class="main"></td>
					        					<td class="main"><?=ENTRY_USE_REMOTE_ACCOUNT_LOGIN_API?></td>
					        					<td>
<?
				if ($error == true) {
					if ($sInfo->task[$task_row['custom_products_type_id']] == '1') {
						echo (($sInfo->task_game_remote_login_api[$task_row['custom_products_type_id']] == 1) ? 'True' : 'False') . tep_draw_hidden_field('task_game_remote_login_api['.$task_row['custom_products_type_id'].']', $sInfo->task_game_remote_login_api[$task_row['custom_products_type_id']]);
					} else {
						echo TEXT_NOT_AVAILABLE . tep_draw_hidden_field('task_game_remote_login_api['.$task_row['custom_products_type_id'].']');
					}
				} else {
					echo tep_draw_checkbox_field('task_game_remote_login_api['.$task_row['custom_products_type_id'].']', 1, (($task_row['use_remote_account_login_api'] == 1) ? true : false), '', 'id="task_game_remote_login_api['.$task_row['custom_products_type_id'].']"' . (is_null($task_row['supplier_tasks_ratio']) ? 'disabled' : ''));
				}
?>
					        					</td>
<?			} ?>
				        				</table>
				        			</td>
				        		</tr>
				      			<tr>
				        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      			</tr>
<? 		} ?>
			      				<tr>
			        				<td class="customerFormAreaTitle"><?=CATEGORY_PAYMENT_DETAILS?></td>
			      				</tr>
			      				<tr>
			        				<td class="formArea">
			        					<table border="0" width="100%" cellspacing="2" cellpadding="2">
            	          					<tr>
            	        						<td class="main"><?=ENTRY_DISABLE_WITHDRAWAL?></td>
            	        						<td class="main">
<?		if ($processed == true) {
    		echo ($sInfo->supplier_disable_withdrawal == 0)? "OFF " : "ON " . tep_draw_hidden_field('supplier_disable_withdrawal');
  		} else {
			echo tep_draw_checkbox_field('supplier_disable_withdrawal', '1', $sInfo->supplier_disable_withdrawal == '1' ? true : false);
			echo tep_draw_hidden_field('supplier_disable_withdrawal_history', $sInfo->supplier_disable_withdrawal);
		}
?>
            	        						</td>
            	        					</tr>
<?		if ($reserve_amount_permission) { ?>
			          						<tr>
			            						<td class="main"><?=ENTRY_SUPPLIER_WITHDRAW_RESERVE_AMOUNT?></td>
			            						<td class="main">
<?			if ($processed == true) {
	    		echo $sInfo->supplier_reserve_amount . tep_draw_hidden_field('supplier_reserve_amount');
	  		} else {
	    		echo tep_draw_input_field('supplier_reserve_amount', $sInfo->supplier_reserve_amount, 'onkeyup="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }"');
	  		}
?>
			          							</td>
			          						</tr>
<?		} ?>
			          						<tr>
			            						<td class="main" width="20%"><?=ENTRY_SUPPLIER_PAYMENT_PAYPAL?></td>
			            						<td class="main">
<?		if ($processed == true) {
	    	echo $sInfo->supplier_payment_paypal . tep_draw_hidden_field('supplier_payment_paypal');
	  	} else {
	    	echo tep_draw_input_field('supplier_payment_paypal', $sInfo->supplier_payment_paypal, 'maxlength="64"');
	  	}
?>
												</td>
			          						</tr>
			          						<tr>
						        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						      				</tr>
			          						<tr>
			            						<td class="main"><?=ENTRY_SUPPLIER_PAYMENT_BANK_NAME?></td>
			            						<td class="main">
<?		if ($processed == true) {
	    	echo $sInfo->supplier_payment_bank_name . tep_draw_hidden_field('supplier_payment_bank_name');
	  	} else {
	    	echo tep_draw_input_field('supplier_payment_bank_name', $sInfo->supplier_payment_bank_name, 'maxlength="64"');
	  	}
?>
			            						</td>
			          						</tr>
			          						<!--tr>
			            						<td class="main"><?=ENTRY_SUPPLIER_PAYMENT_BANK_BRANCH_NUMBER?></td>
			            						<td class="main">
<?	if ($processed == true) {
    	echo $sInfo->supplier_payment_bank_branch_number . tep_draw_hidden_field('supplier_payment_bank_branch_number');
  	} else {
    	echo tep_draw_input_field('supplier_payment_bank_branch_number', $sInfo->supplier_payment_bank_branch_number, 'maxlength="64"');
  	}
?>
			            						</td>
			          						</tr-->
			          						<tr>
			            						<td class="main"><?=ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE?></td>
			            						<td class="main">
<?		if ($processed == true) {
	    	echo $sInfo->supplier_payment_bank_swift_code . tep_draw_hidden_field('supplier_payment_bank_swift_code');
	  	} else {
	    	echo tep_draw_input_field('supplier_payment_bank_swift_code', $sInfo->supplier_payment_bank_swift_code, 'maxlength="64"');
	  	}
?>
			            						</td>
			          						</tr>
			          						<tr>
			            						<td class="main" valign="top"><?=ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS?></td>
			            						<td class="main">
<?		if ($processed == true) {
	    	echo $sInfo->supplier_payment_bank_address . tep_draw_hidden_field('supplier_payment_bank_address');
	  	} else {
	    	echo tep_draw_textarea_field('supplier_payment_bank_address', 'soft', '50', '3', $sInfo->supplier_payment_bank_address);
	  	}
?>
			            						</td>
			          						</tr>
			          						<tr>
			            						<td class="main"><?=ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER?></td>
			            						<td class="main">
<?		if ($processed == true) {
	    	echo $sInfo->supplier_payment_bank_telephone . tep_draw_hidden_field('supplier_payment_bank_telephone');
	  	} else {
	    	echo tep_draw_input_field('supplier_payment_bank_telephone', $sInfo->supplier_payment_bank_telephone, 'maxlength="64"');
	  	}
?>
			            						</td>
			          						</tr>
			          						<tr>
			            						<td class="main"><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME?></td>
			            						<td class="main">
<?		if ($processed == true) {
	    	echo $sInfo->supplier_payment_bank_account_name . tep_draw_hidden_field('supplier_payment_bank_account_name');
	  	} else {
	    	echo tep_draw_input_field('supplier_payment_bank_account_name', $sInfo->supplier_payment_bank_account_name, 'maxlength="64"');
	  	}
?>
			            						</td>
			          						</tr>
			          						<tr>
			            						<td class="main"><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER?></td>
			            						<td class="main">
<?		if ($processed == true) {
	    	echo $sInfo->supplier_payment_bank_account_number . tep_draw_hidden_field('supplier_payment_bank_account_number');
	  	} else {
	    	echo tep_draw_input_field('supplier_payment_bank_account_number', $sInfo->supplier_payment_bank_account_number, 'maxlength="64"');
	  	}
?>
			            						</td>
			          						</tr>
			        					</table>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			        				<td class="customerFormAreaTitle"><?=CATEGORY_PREFERENCES?></td>
			      				</tr>
			      				<tr>
			        				<td class="formArea">
			        					<table border="0" width="100%" cellspacing="2" cellpadding="2">
			        						<tr>
                                                <td class="main" width="20%"><?=ENTRY_PREF_TIME_ZONE?></td>
                                                <td class="main"><?=tep_draw_pull_down_menu(KEY_SP_TIME_ZONE, $time_zone_array, $sInfo->{KEY_SP_TIME_ZONE}, '')?></td>
                          					</tr>
                          					<tr>
                                                <td class="main"><?=ENTRY_PREF_LANGUAGE?></td>
                                                <td class="main"><?=tep_draw_pull_down_menu(KEY_SP_LANG, $pref_language_array, $sInfo->{KEY_SP_LANG}, '')?></td>
                          					</tr>
                        				</table>
                        			</td>
                      			</tr>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
<? 		if ($edit_account_permission) { ?>
			      				<tr>
				        			<td align="right" class="main"><?=tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link(FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction'))) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?></td>
				      			</tr>
<? 		} ?>
			      			</table>
			      			</form>
			      			</fieldset>
				      	</td>
					</tr>
	      			<tr>
	      				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
<? 	} 

	if (($view_remark_permission || $edit_remark_permission) && (!$error || $error_section=='remark')) {
?>
	      			<tr>
	      				<td>
			      			<fieldset class="selectedFieldSet">
<?
				      		echo tep_draw_form('supplier_remark', FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('subaction','sID')) . 'subaction=update_remark', 'post');
				      		echo tep_draw_hidden_field("sID", $_REQUEST["sID"]);
							echo tep_draw_hidden_field('supplier_email_address', $sInfo->supplier_email_address);
?>		
			      			<table border="0" width="100%" cellspacing="0" cellpadding="2">
                      			<tr>
                      				<tr>
			        					<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				      				</tr>
					      			<tr>
					        			<td class="customerFormAreaTitle"><?=CATEGORY_REMARKS?></td>
					      			</tr>
			        				<td class="formArea">
			        					<table border="0" width="100%" cellspacing="2" cellpadding="2">
			        						<tr>
	                                            <td class="main" width="20%" valign="top"><?=ENTRY_SUPPLIER_REMARKS?></td>
	                                            <td class="main">
<?
 			echo tep_draw_textarea_field('supplier_remarks', 'soft', '60', '8', $sInfo->supplier_remarks);
?>
	    										</td>
	                      					</tr>
	                    				</table>
	                    			</td>
                      			</tr>
                      			<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
<? 		if ($edit_remark_permission) { ?>
			      				<tr>
				        			<td align="right" class="main"><?=tep_image_submit('button_update.gif', IMAGE_UPDATE) . ' <a href="' . tep_href_link(FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('action', 'subaction'))) .'">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?></td>
				      			</tr>
<? 		} ?>
				      		</table>
				      		</form>
				      		</fieldset>
	    				</td>
	    			</tr>
<? 	}	?>
<script language="javascript">
<!--
		function supplier_check_form() {
			var error = 0;
		  	var error_message = "<?=JS_ERROR?>";
		  			
		  	var supplier_firstname = DOMCall('supplier_firstname');
		  	var supplier_lastname = DOMCall('supplier_lastname');
		  	var supplier_dob = DOMCall('supplier_dob');
		  	var supplier_email_address = DOMCall('supplier_email_address');
		  	var supplier_street_address = DOMCall('supplier_street_address');
		  	var supplier_postcode = DOMCall('supplier_postcode');
		  	var supplier_city = DOMCall('supplier_city');
		  	var supplier_telephone = DOMCall('supplier_telephone');
		  	var supplier_code = DOMCall('supplier_code');
		  	var supplier_country_id = DOMCall('supplier_country_id');
		  	
		<? 	if (ACCOUNT_GENDER == 'true') { ?>
				if (document.supplier_form.supplier_gender.length > 1) {
			  		if (document.supplier_form.supplier_gender[0].checked || document.supplier_form.supplier_gender[1].checked) {
			  			;
			  		} else {
			    		error_message = error_message + "<?php echo JS_GENDER; ?>";
			    		error = 1;
			  		}
			  	}
		<?	} ?>
		
			if (supplier_firstname != null) {
			  	if (supplier_firstname.value.length < <?php echo ENTRY_FIRST_NAME_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_FIRST_NAME; ?>";
			    	error = 1;
			  	}
			}
			
			if(supplier_lastname != null) {
			  	if (supplier_lastname.value.length < <?php echo ENTRY_LAST_NAME_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_LAST_NAME; ?>";
			    	error = 1;
			  	}
			}
		  	
		<? 	if (ACCOUNT_DOB == 'true') { ?>
				if(supplier_dob != null) {
			  		if (supplier_dob.value.length < <?php echo ENTRY_DOB_MIN_LENGTH; ?>) {
			    		error_message = error_message + "<?php echo JS_DOB; ?>";
			    		error = 1;
			  		}
			  	}
		<? 	} ?>
			if (supplier_email_address != null) {
			  	if (supplier_email_address.value.length < <?php echo ENTRY_EMAIL_ADDRESS_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_EMAIL_ADDRESS; ?>";
			    	error = 1;
			  	}
			}
			
			if (supplier_street_address != null) {
			  	if (supplier_street_address.value.length < <?php echo ENTRY_STREET_ADDRESS_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_ADDRESS; ?>";
			    	error = 1;
			  	}
			}
			
			if (supplier_postcode != null) {
			  	if (supplier_postcode.value.length < <?php echo ENTRY_POSTCODE_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_POST_CODE; ?>";
			    	error = 1;
			  	}
			}
			
			if (supplier_city != null) {
			  	if (supplier_city.value.length < <?php echo ENTRY_CITY_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_CITY; ?>";
			    	error = 1;
			  	}
			}
 				
			if (supplier_country_id.value == '') {
				error_message = error_message + "<?php echo JS_COUNTRY; ?>";
		      	error = 1;
		    } else {
		<?		if (ACCOUNT_STATE == 'true') { ?>
					if(supplier_state != null) {
				  		if (supplier_state.value == '') {
				       		error_message = error_message + "<?php echo JS_STATE; ?>";
				       		error = 1;
				    	}
				    }
		<?		} ?>
		    }
		  	
		  	if (supplier_telephone != null) {
			  	if (supplier_telephone.value.length < <?php echo ENTRY_TELEPHONE_MIN_LENGTH; ?>) {
			    	error_message = error_message + "<?php echo JS_TELEPHONE; ?>";
			    	error = 1;
			  	}
			}
		  	
		  	if (supplier_code != null) {
			  	if (supplier_code.value.length < 1) {
			    	error_message = error_message + "<?php echo JS_SUPPLIER_CODE; ?>";
			    	error = 1;
			  	}
			}
			
		  	if (error == 1) {
		    	alert(error_message);
		    	return false;
		  	} else {
		    	return true;
		  	}
		}
//-->
</script>      			
<?
} else {
	$groups_array = array (	array(	'id' => '', 'text' => 'All Groups')	);
	$groups_query = tep_db_query("select supplier_groups_id, supplier_groups_name from " . TABLE_SUPPLIER_GROUPS ." order by supplier_groups_name");
	while($groups = tep_db_fetch_array($groups_query)) {
		$groups_array[] = array('text' => $groups['supplier_groups_name'],
								'id' => $groups['supplier_groups_id']);
	}
	
	$list_colspan_count = 10;
	if ($view_payment_info_permission) $list_colspan_count ++;
?>
					<tr>
        				<td align="right">
        				<?
        					echo tep_draw_form('goto_search_form', FILENAME_SUPPLIERS_LIST, '', 'post');
							echo tep_draw_pull_down_menu("grp_id", $groups_array, $_SESSION["supplier_param"]['grp_id'], 'onChange="this.form.submit();"');
							echo "</form>";
						?>
        				</td>
      				</tr>
<?
	$supplier_grp_where_str = tep_not_null($_SESSION["supplier_param"]['grp_id']) ? "s.supplier_groups_id='".$_SESSION["supplier_param"]['grp_id']."'" : ' 1 ';
	$supplier_select_sql = "select s.*, sg.supplier_groups_name from " . TABLE_SUPPLIER . " as s left join " . TABLE_SUPPLIER_GROUPS . " as sg on s.supplier_groups_id=sg.supplier_groups_id where " . $supplier_grp_where_str . " order by supplier_status desc, supplier_email_address";
	$page_split_object = new splitPageResults($_REQUEST["page"], MAX_DISPLAY_SEARCH_RESULTS, $supplier_select_sql, $sql_numrows);
	$supplier_result_sql = tep_db_query($supplier_select_sql);
	
	$supplier_status_array = array(	'1'=> array('name' => 'icon_status_green', 'alt' => IMAGE_ICON_STATUS_GREEN, 'alt2' => IMAGE_ICON_STATUS_GREEN_LIGHT), 
									'0'=> array('name' => 'icon_status_red', 'alt' => IMAGE_ICON_STATUS_RED, 'alt2' => IMAGE_ICON_STATUS_RED_LIGHT)
								);
	
	if (tep_db_num_rows($supplier_result_sql)) {
?>
					<tr>
            			<td valign="top">
<?		echo tep_draw_form('suppliers_list_form', FILENAME_SUPPLIERS_LIST, tep_get_all_get_params(array('subaction')) . 'subaction=update_list', 'post', ''); ?>
            				<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
								    <td width="5%" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_CODE?></td>
								    <td width="12%" align="left" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_LASTNAME?></td>
								    <td width="12%" align="left" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_FIRSTNAME?></td>
								    <td align="left" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_EMAIL?></td>
								    <td width="10%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_DATE_CREATED?></td>
								    <td width="8%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_GROUP?></td>
<?		if ($view_payment_info_permission) {
			echo '				    <td width="10%" align="center" class="reportBoxHeading">'.TABLE_HEADING_OUTSTANDING_AMOUNT.'</td>';
		}
?>
								    <td width="5%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_SUPPLIER_STATUS?></td>
								    <td width="17%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PURCHASE_MODE?></td>
								    <td width="5%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
								    <td width="1%" class="reportBoxHeading"><?=tep_draw_checkbox_field('select_all', '', false, '', 'id="select_all" title="Select or deselect all suppliers" onclick="javascript:void(setCheckboxes(\'suppliers_list_form\',\'select_all\',\'supplier_batch\'));"')?></td>
								</tr>
<?
		$total_outstanding_amount_array = array();
		$row_count = 0;
		while ($supplier_row = tep_db_fetch_array($supplier_result_sql)) {
    		$supplier_outstanding_amount = '';
    		
    		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
    		
    		$supplier_id = $supplier_row["supplier_id"];
    		$save_supplier_email = htmlspecialchars(addslashes($supplier_row["supplier_email_address"]), ENT_QUOTES);
    		
    		$all_list_purchase_time_select_sql = "	SELECT ppl.products_purchases_lists_id AS list_id, ppl.products_purchases_lists_name, ppl.products_purchases_lists_cat_id, spm.supplier_id, spm.supplier_purchase_mode 
    												FROM " . TABLE_SUPPLIER_LIST_TIME_SETTING . " AS slts 
    												INNER JOIN " . TABLE_PRODUCTS_PURCHASES_LISTS . " AS ppl 
    													ON slts.products_purchases_lists_id=ppl.products_purchases_lists_id 
    												LEFT JOIN " . TABLE_SUPPLIER_PURCHASE_MODES . " AS spm 
    													ON (slts.products_purchases_lists_id=spm.products_purchases_lists_id AND spm.supplier_id='".$supplier_id."')
    												WHERE slts.supplier_groups_id = '".$supplier_row["supplier_groups_id"]."' 
    												ORDER BY ppl.products_purchases_lists_sort_order ";
    		$all_list_purchase_time_result_sql = tep_db_query($all_list_purchase_time_select_sql);
    		
    		if ($view_payment_info_permission) {
    			$supplier_outstanding_amount_array = $payments_object->get_user_account_balance($supplier_id, 'supplier');
    			
    			for ($bal_cnt=0; $bal_cnt < count($supplier_outstanding_amount_array); $bal_cnt++) {
    				$supplier_outstanding_amount .= $currencies->format($supplier_outstanding_amount_array[$bal_cnt]['text'], false, $supplier_outstanding_amount_array[$bal_cnt]['id']) . '<br>';
    				$total_outstanding_amount_array[$supplier_outstanding_amount_array[$bal_cnt]['id']] += (double)$supplier_outstanding_amount_array[$bal_cnt]['text'];
    			}
    			/*
	    		$payable_amount_select_sql = "	SELECT SUM( (IF(solp.products_received_quantity > solp.first_max_quantity, IF(solp.products_received_quantity > solp.first_max_quantity+solp.second_max_quantity, solp.first_max_quantity*solp.first_max_unit_price + solp.second_max_quantity*solp.second_max_unit_price, solp.first_max_quantity*solp.first_max_unit_price + (solp.products_received_quantity-solp.first_max_quantity)*solp.second_max_unit_price), solp.products_received_quantity*solp.first_max_unit_price)) ) AS total_amount 
	    										FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
	    										INNER JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
	    											ON sol.supplier_order_lists_id=solp.supplier_order_lists_id 
	    										WHERE sol.supplier_order_lists_status=2 
	    											AND sol.suppliers_id = '" . $supplier_id . "' 
	    											AND solp.supplier_order_lists_type=2";
	  			$payable_amount_result_sql = tep_db_query($payable_amount_select_sql);
	  			$payable_amount_row = tep_db_fetch_array($payable_amount_result_sql);
	  			
	  			$paid_amount_select_sql = "	SELECT SUM(spo.supplier_payments_orders_paid_amount) AS total_paid 
											FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
	    									INNER JOIN " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " AS spo 
	    										ON sol.supplier_order_lists_id=spo.supplier_order_lists_id 
											INNER JOIN " . TABLE_SUPPLIER_PAYMENTS . " AS sp 
												ON (spo.supplier_payments_id=sp.supplier_payments_id AND sp.supplier_payments_status IN (1, 2)) 
											WHERE sol.supplier_order_lists_status=2 
	    										AND sol.suppliers_id = '" . $supplier_id . "'";
				$paid_amount_result_sql = tep_db_query($paid_amount_select_sql);
				$paid_amount_row = tep_db_fetch_array($paid_amount_result_sql);
				
				$supplier_outstanding_amount = (double)$payable_amount_row["total_amount"] - (double)$paid_amount_row["total_paid"];
				$total_outstanding_amount += (double)$supplier_outstanding_amount;
				*/
	  		}
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top"><?=$supplier_row["supplier_code"]?></td>
									<td class="reportRecords" valign="top"><?=$supplier_row["supplier_lastname"]?></td>
									<td class="reportRecords" valign="top"><?=$supplier_row["supplier_firstname"]?></td>
									<td class="reportRecords" align="left" valign="top"><?=$supplier_row["supplier_email_address"]?></td>
									<td class="reportRecords" align="center" valign="top"><?=tep_date_short($supplier_row["supplier_date_account_created"], PREFERRED_DATE_FORMAT)?></td>
									<td class="reportRecords" align="center" valign="top"><?=$supplier_row["supplier_groups_name"]?></td>
<?			if ($view_payment_info_permission) {
				echo '				<td class="reportRecords" align="right" valign="top">'.$supplier_outstanding_amount.'</td>';
			}
?>
									<td class="reportRecords" align="center" valign="top" nowrap>
									<?
										foreach ($supplier_status_array as $status_id => $img_res) {
											if ((int)$supplier_row["supplier_status"] == (int)$status_id) {
												echo tep_image(DIR_WS_IMAGES . $img_res['name'] . '.gif', $img_res['alt'], 10, 10) . '&nbsp;&nbsp;';
											} else {
												echo '<a href="' . tep_href_link(FILENAME_SUPPLIERS_LIST, 'action=set_supplier_status&flag='.(int)$status_id.'&sID='.$supplier_id.'&page='.$_REQUEST["page"]) . '">' . tep_image(DIR_WS_IMAGES . $img_res['name'] . '_light.gif', $img_res['alt2'], 10, 10) . '</a>&nbsp;&nbsp;';
											}
										}
									?>
									</td>
									<td class="reportRecords" align="center" valign="top" nowrap>
										<table border="0" width="100%" cellspacing="0" cellpadding="2">
									<?
										while ($all_list_purchase_time_row = tep_db_fetch_array($all_list_purchase_time_result_sql)) {
											if (tep_check_cat_tree_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, $all_list_purchase_time_row["products_purchases_lists_cat_id"]) != 1) {
												continue;
											}
											
											echo '	<tr>
														<td class="reportRecords">'.$all_list_purchase_time_row["products_purchases_lists_name"].'</td>
													</tr>
													<tr>
														<td class="reportRecords" nowrap>';
											if (!tep_not_null($all_list_purchase_time_row["supplier_id"])) {
												$time_sql_data_array = array(	'supplier_id' => $supplier_id,
																				'products_purchases_lists_id' => $all_list_purchase_time_row['list_id'],
																				'supplier_purchase_mode' => 'STATUS_GROUP'
								            								);
								            	
							            		tep_db_perform(TABLE_SUPPLIER_PURCHASE_MODES, $time_sql_data_array);
							            		
							            		echo tep_draw_radio_field('radioStatus['.$supplier_id.']['.$all_list_purchase_time_row['list_id'].']', 'STATUS_ON', false) . TEXT_ON ?>&nbsp;<?=tep_draw_radio_field('radioStatus['.$supplier_id.']['.$all_list_purchase_time_row['list_id'].']', 'STATUS_OFF', false) . TEXT_OFF?>&nbsp;<?=tep_draw_radio_field('radioStatus['.$supplier_id.']['.$all_list_purchase_time_row['list_id'].']', 'STATUS_GROUP', true) . TEXT_GROUP;
											} else {
												echo tep_draw_radio_field('radioStatus['.$supplier_id.']['.$all_list_purchase_time_row['list_id'].']', 'STATUS_ON', ($all_list_purchase_time_row['supplier_purchase_mode']=='STATUS_ON' ? true : false)) . TEXT_ON ?>&nbsp;<?=tep_draw_radio_field('radioStatus['.$supplier_id.']['.$all_list_purchase_time_row['list_id'].']', 'STATUS_OFF', ($all_list_purchase_time_row['supplier_purchase_mode']=='STATUS_OFF' ? true : false)) . TEXT_OFF?>&nbsp;<?=tep_draw_radio_field('radioStatus['.$supplier_id.']['.$all_list_purchase_time_row['list_id'].']', 'STATUS_GROUP', ($all_list_purchase_time_row['supplier_purchase_mode']=='STATUS_GROUP' ? true : false)) . TEXT_GROUP;
											}
											echo '		</td>
													</tr>';
										}
									?>
										</table>
									</td>
									<td align="left" class="reportRecords" valign="top" nowrap>&nbsp;
										<a href="<?=tep_href_link(FILENAME_SUPPLIERS_LIST, 'action=edit_supplier&sID='.$supplier_id.'&page='.$_REQUEST["page"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit supplier", "", "", 'align="top"')?></a>
										<a href="javascript:void(confirm_delete('<?=$save_supplier_email?>', 'Supplier', '<?=tep_href_link(FILENAME_SUPPLIERS_LIST, 'action=delete_supplier&subaction=confirm_delete_supplier&sID='.$supplier_id.'&page='.$_REQUEST["page"])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete supplier", "", "", 'align="top"')?></a>
									</td>
									<td class="reportRecords" valign="top">
										<?=tep_draw_checkbox_field('supplier_batch[]', $supplier_id, false)?>
									</td>
								</tr>
<?
			$row_count++;
		}
		
		if ($row_count) {
			if ($view_payment_info_permission) {
				foreach ($total_outstanding_amount_array as $cur_type => $amt) {
    				$total_outstanding_amount_str .= $currencies->format($amt, false, $cur_type) . '<br>';
    			}
    			
				echo '			<tr>
				    				<td align="right" class="reportRecords" colspan="'.($list_colspan_count-4).'"><b>'.$total_outstanding_amount_str.'</b></td>
				    				<td colspan="4">&nbsp;</td>
				  				</tr>';
			} else {
				echo '			<tr>
									<td class="ordersRecords" colspan="'.($list_colspan_count).'">'.tep_draw_separator('pixel_trans.gif', '1', '1').'</td>
								</tr>';
			}
?>
								<tr>
									<td colspan="<?=$list_colspan_count?>" align="right">
									<?
										$batch_action_array = array(array('id' => '', 'text' => 'With selected:'),
																	array('id' => 'PurchaseMode', 'text' => 'Update purchase mode'),
																	array('id' => 'Active', 'text' => 'Set as active'),
																	array('id' => 'Inactive', 'text' => 'Set as inactive'),
																	array('id' => 'Delete', 'text' => 'Delete')
																	);
										echo tep_draw_pull_down_menu('batch_action', $batch_action_array) . '&nbsp;';
										echo tep_submit_button('Go', 'Go', 'name="multi_submit_btn" onClick="return confirmBatchAction()"', 'inputButton');
									?>
									</td>
								</tr>
								<script>
								<!--
									function confirmBatchAction() {
										if (trim_str(document.suppliers_list_form.batch_action.value) == '') {
											alert('Please select your batch action!');
											return false;
										} else {
											answer = confirm('Are you sure to perform "' + document.suppliers_list_form.batch_action[document.suppliers_list_form.batch_action.selectedIndex]['text'] + '" batch action?');
											if (answer !=0) {
												return true;
											}
											return false;
										}
									}
								//-->
								</script>
<?		} ?>
							</table>
							</form>
			   			</td>
			   		</tr>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td class="smallText" valign="top" NOWRAP><?=$page_split_object->display_count($sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_SUPPLIERS)?></td>
									<td class="smallText" align="right"><?=$page_split_object->display_links($sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_REQUEST['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont'))."cont=1")?></td>
								</tr>
							</table>
						</td>
					</tr>
<?	}
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<script language="javascript">
	function enabletasksfield(task_id) {
		document.getElementById('task_max['+task_id+']').disabled = false;
		document.getElementById('slot_max['+task_id+']').disabled = false;
		document.getElementById('task_ratio['+task_id+']').disabled = false;
		document.getElementById('task_game_remote_login_api['+task_id+']').disabled = false;
	}
	
	function disabletasksfield(task_id) {
		document.getElementById('task_max['+task_id+']').disabled = true;
		document.getElementById('slot_max['+task_id+']').disabled = true;
		document.getElementById('task_ratio['+task_id+']').disabled = true;
		document.getElementById('task_game_remote_login_api['+task_id+']').disabled = true;
	}
</script>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>