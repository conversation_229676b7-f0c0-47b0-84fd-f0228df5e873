<?
//error_reporting(E_ERROR);
//ini_set("display_errors", true);
require_once('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'buyback_supplier.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');

function draw_colour_picker($id=0, $selected_colour_code='') {
	global $colour_picker_arr;
	
	//---- Start Lame Colour Picker
	$colour_pickerHTML = "<select name='row_colour[$id]' id='row_colour_$i'>";
	$colour_pickerHTML .= "<option value='$id'> ".PULL_DOWN_DEFAULT." </option>";
	foreach ($colour_picker_arr as $colour_name => $colour_code) {
		$colour_pickerHTML .= "<option value='$colour_code' style='background-color:$colour_code' ";
		if ($colour_code == $selected_colour_code) {
			$colour_pickerHTML .= "selected";
		}
		$colour_pickerHTML .= "> $colour_name </option>";
	}
	$colour_pickerHTML .= '</select>';
	return $colour_pickerHTML;
}

// Load required permissions
$edit_preferences_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, 'BUYBACK_EDIT_GAME_PREFERENCES');
$edit_offer_prices_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, 'BUYBACK_EDIT_GAME_OFFER_PRICES');

$form_sessname = basename($_SERVER['PHP_SELF']);
define('DISPLAY_PRICE_DECIMAL', 6);
$game_cat_id_arr = tep_get_game_list_arr();

//set default section mode here. mode is 'show' or 'hide'
$sections_arr = array();
$sections_arr['preferences'] = array('default_mode' => 'show', 'default_label' => 'Hide');
$sections_arr['brackets'] = array('default_mode' => 'show', 'default_label' => 'Hide');
$sections_arr['prices'] = array('default_mode' => 'show', 'default_label' => 'Hide');

$bracket_mode_arr = array(0 => '$', 1 => '&#37;');
$bracket_sign_arr = array(0 => '+', 1 => '-');

$svrfull_tag_o = '<b>';
$svrfull_tag_c = '</b>';

$backorder_tag_o = '<u>';
$backorder_tag_c = '</u>';

$error_tag_o = '<font color="red">';
$error_tag_c = '</font>';

foreach ($sections_arr as $section_name => $section_settings_arr) {
	$mdvar = $section_name . '_show_mode_default';
	$$mdvar = $section_settings_arr['default_mode'];
	$ldvar = $section_name . '_show_mode_label_default';
	$$ldvar = $section_settings_arr['default_label'];
	
	$mvar = $section_name . '_show_mode';
	if (!isset($_SESSION[$form_sessname][$mvar])) {
		$_SESSION[$form_sessname][$mvar] = $$mdvar;
	}
	if (isset($_POST[$mvar])) {
		$_SESSION[$form_sessname][$mvar] = $_POST[$mvar];
	}
}

$parent_cat_id = isset($_REQUEST['game_cat_id']) ? (int)$_REQUEST['game_cat_id'] : 0;
$site = isset($_REQUEST['site']) ? (int)$_REQUEST['site'] : 0;

if (isset($_GET['action'])) {
	$action = $_GET['action'];
	
	if (!$parent_cat_id || !$site) {
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action')) . ''));
	}
} else {
	if (tep_not_null($site)) {
		if ($site == 1) {
			$action = 'price_control';
		} else if ($site == 2) {
			$action = 'edit_list';
		}
	}
}

if ($parent_cat_id > 0) {
    $buybackSupplierObj = new buyback_supplier($parent_cat_id);
    $path = $buybackSupplierObj->category_cat_path;
	$colour_picker_arr = $buybackSupplierObj->colour_picker_arr;
}

switch($action) {
	case 'update_price':
		if (isset($HTTP_POST_VARS['price_base']) && is_array($HTTP_POST_VARS['price_base'])) {
			foreach ($HTTP_POST_VARS['price_base'] as $products_id => $buyback_price_control_for_site_id) {
				if (isset($HTTP_POST_VARS['price'][$products_id])) {
					if (tep_not_null($buyback_price_control_for_site_id) && tep_not_null($HTTP_POST_VARS['price'][$products_id])) {
						$buyback_pc_update_array = array(	'products_id' => (int)$products_id,
															'buyback_price_control_for_site_id' => (int)$buyback_price_control_for_site_id,
															'buyback_price_control_value' => $HTTP_POST_VARS['price'][$products_id]);
						
						$products_id_select_sql = "	SELECT products_id FROM " . TABLE_BUYBACK_PRICE_CONTROL . " WHERE products_id ='" . (int)$products_id . "'";
						$products_id_result_sql = tep_db_query($products_id_select_sql);
						if (tep_db_num_rows($products_id_result_sql) < 1) {
							tep_db_perform(TABLE_BUYBACK_PRICE_CONTROL, $buyback_pc_update_array);
						} else {
							tep_db_perform(TABLE_BUYBACK_PRICE_CONTROL, $buyback_pc_update_array, 'update', "products_id = '" . (int)$products_id . "'");
						}
					}
				}
			}
		}
		
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action')) . 'action=price_control'));
		
		break;
	case 'edit_preferences':
		if ($edit_preferences_permission) {
			if ($buybackSupplierObj) {
				
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_sales_retrieval_method', $_POST['rdoSalesRetrievalMethod']);
				switch($buybackSupplierObj->buyback_supplier_settings['ofp_sales_retrieval_method']) {
					case 'by_date_range':
						$buybackSupplierObj->assign_buyback_supplier_settings('ofp_sales_start_date', $_POST['ofp_sales_start_date']);
						$buybackSupplierObj->assign_buyback_supplier_settings('ofp_sales_end_date', $_POST['ofp_sales_end_date']);
						break;
					case 'by_last_n_days':
						$buybackSupplierObj->assign_buyback_supplier_settings('ofp_last_n_days_sales', $_POST['ofp_last_n_days_sales']);
						break;
				}
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_inventory_days', $_POST['ofp_inventory_days']);
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_max_inv_space_percentage', (string)$_POST['ofp_max_inv_space_percentage']);
				
				$row_colour_setting_arr = array();
				foreach ($_POST['profit_percentage'] as $id => $profit_percentage) {
					$is_delete = false;
					if (isset($_POST['delete_colour_setting'][$id])) {
						$is_delete = true;
					}
					$row_colour = $_POST['row_colour'][$id];
					$profit_percentage = tep_db_prepare_input((string)$profit_percentage);
					if (!$is_delete && $row_colour && strlen($profit_percentage)) {
						$row_colour_setting_arr[] = "$profit_percentage|$row_colour";
					}
				}
				
				$ofp_not_buyback_start_time = $ofp_not_buyback_end_time = '';
				if (isset($_POST['not_buyback_start_hr']) && isset($_POST['not_buyback_start_min'])) {
					for ($time_cnt=0; $time_cnt < count($_POST['not_buyback_start_hr']); $time_cnt++) {
						if (tep_not_null($_POST['not_buyback_start_hr'][$time_cnt]) && tep_not_null($_POST['not_buyback_start_min'][$time_cnt])) {
							if ((int)$_POST['not_buyback_start_hr'][$time_cnt] < 0 || (int)$_POST['not_buyback_start_hr'][$time_cnt] > 23)	$_POST['not_buyback_start_hr'][$time_cnt] = "00";
							if ((int)$_POST['not_buyback_start_min'][$time_cnt] < 0 || (int)$_POST['not_buyback_start_min'][$time_cnt] > 59)	$_POST['not_buyback_start_min'][$time_cnt] = "00";
							
							$ofp_not_buyback_start_time .= sprintf("%02d", $_POST['not_buyback_start_hr'][$time_cnt]).':'.sprintf("%02d", $_POST['not_buyback_start_min'][$time_cnt]).':00,';
						}
					}
				}
				
				if (isset($_POST['not_buyback_end_hr']) && isset($_POST['not_buyback_end_min'])) {
					for ($time_cnt=0; $time_cnt < count($_POST['not_buyback_end_hr']); $time_cnt++) {
						if (tep_not_null($_POST['not_buyback_end_hr'][$time_cnt]) && tep_not_null($_POST['not_buyback_end_min'][$time_cnt])) {
							if ((int)$_POST['not_buyback_end_hr'][$time_cnt] < 0 || (int)$_POST['not_buyback_end_hr'][$time_cnt] > 23)	$_POST['not_buyback_end_hr'][$time_cnt] = "00";
							if ((int)$_POST['not_buyback_end_min'][$time_cnt] < 0 || (int)$_POST['not_buyback_end_min'][$time_cnt] > 59)	$_POST['not_buyback_end_min'][$time_cnt] = "00";
							
							$ofp_not_buyback_end_time .= sprintf("%02d", $_POST['not_buyback_end_hr'][$time_cnt]).':'.sprintf("%02d", $_POST['not_buyback_end_min'][$time_cnt]).':00,';
						}
					}
				}
				
				if (tep_not_null($ofp_not_buyback_start_time))	$ofp_not_buyback_start_time = substr($ofp_not_buyback_start_time, 0, -1);
				if (tep_not_null($ofp_not_buyback_end_time))	$ofp_not_buyback_end_time = substr($ofp_not_buyback_end_time, 0, -1);
				
				$buyback_list_controller = '';
				// Use >= for Open List Ctrl since there is case where only open list when no Pending Buyback
				if (is_numeric($_POST['ofp_open_list_ctrl']) && $_POST['ofp_open_list_ctrl'] >= 0
					&& is_numeric($_POST['ofp_close_list_ctrl']) && $_POST['ofp_close_list_ctrl'] > 0
					&& $_POST['ofp_close_list_ctrl'] > $_POST['ofp_open_list_ctrl']) {
					$buyback_list_controller = (int)$_POST['ofp_open_list_ctrl'].','.(int)$_POST['ofp_close_list_ctrl'];
				}
				if (tep_not_null($parent_cat_id) && $parent_cat_id > 0) {		// check with weichen for $parent_cat_id > 0, else will accidentally update 'top'
					$update_buyback_quantity_unit_sql = "	UPDATE " . TABLE_CATEGORIES_CONFIGURATION . " 
															SET categories_configuration_value = '".$_POST['buyback_quantity_unit']."'
															WHERE categories_id = '".(int)$parent_cat_id."'
																AND categories_configuration_key = 'BUYBACK_QUANTITY_UNIT'";
					tep_db_query($update_buyback_quantity_unit_sql);
					
					// remove this key setting for all its child categories
					tep_get_subcategories($sub_category_array, $parent_cat_id);
					
					if (count($sub_category_array) > 0) {
						$cat_configuration_delete_sql = "	DELETE FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
															WHERE categories_configuration_key = 'BUYBACK_QUANTITY_UNIT'
																AND categories_id <> 0 
																AND categories_id IN(" . (implode(',', $sub_category_array)) . ")";
						tep_db_query($cat_configuration_delete_sql);
					}
				}
				
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_row_colour', implode(',', $row_colour_setting_arr));
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_do_not_buyback', (int)$_POST['ofp_do_not_buyback']);
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_not_buyback_start_time', $ofp_not_buyback_start_time);
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_not_buyback_end_time', $ofp_not_buyback_end_time);
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_buyback_list_controller', $buyback_list_controller);
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_purchase_eta', (int)$_POST['ofp_purchase_eta']);
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_delivery_option', is_array($_POST['ofp_delivery_option']) && count($_POST['ofp_delivery_option']) ? implode(',', $_POST['ofp_delivery_option']) : '');
				$buybackSupplierObj->assign_buyback_supplier_settings('ofp_trading_place', $_POST['ofp_trading_place']);
				$buybackSupplierObj->assign_buyback_supplier_settings('vip_trade_mode_option', is_array($_POST['vip_trade_mode_option']) && count($_POST['vip_trade_mode_option']) ? implode(',', $_POST['vip_trade_mode_option']) : '');
				$buybackSupplierObj->assign_buyback_supplier_settings('vip_orders_expiry_duration', (int)$_POST['vip_orders_expiry_duration']);
				$buybackSupplierObj->assign_buyback_supplier_settings('customer_confirmation_duration', (int)$_POST['customer_confirmation_duration']);
				$buybackSupplierObj->assign_buyback_supplier_settings('vip_request_cancellation_duration', (int)$_POST['vip_request_cancellation_duration']);
				
				$buybackSupplierObj->save_buyback_supplier_settings();
				
				// Live update buyback list opening status
				$buybackSupplierObj->set_buyback_list_controller_status();
			}
		}
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action')) . 'action=edit_list'));
		
		break;
	case 'update_list':
		if ($edit_offer_prices_permission) {
			if ($buybackSupplierObj) {
				if ($_POST['btn_assign_bracket'] && $_POST['assign_bracket_set_id'] && count($_POST['cb'])) {
					//clicked assign bracket
					foreach ($_POST['cb'] as $products_id) {
						$buybackSupplierObj->assign_product_supplier_price_bracket_set($products_id, tep_db_prepare_input($_POST['assign_bracket_set_id']));
					}
					$buybackSupplierObj->save_product_supplier_price_bracket_set();
				} else if ($_POST['btn_update']) {
					//clicked update
					foreach ($_POST['maxQty'] as $products_id => $overwrite_qty) {
						$buybackSupplierObj->assign_buyback_product_overwrite_qty($products_id, $overwrite_qty);
					}
					$buybackSupplierObj->save_buyback_product_overwrite_qty();
				} else if ($_POST['btn_csv_export']) {
					$buybackSupplierObj->do_calculate_profit = true;
					$buybackSupplierObj->calculate_offer_price();
					
					//Clicked export
					$export_csv_data = $buybackSupplierObj->get_data_csv();
					
					if (tep_not_null($export_csv_data)) {
						$filename = 'avg_buying_price_'.date('YmdHis').'.csv';
						$mime_type = 'text/x-csv';
						// Download
				        header('Content-Type: ' . $mime_type);
				        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
				        // IE need specific headers
				        if (PMA_USR_BROWSER_AGENT == 'IE') {
				            header('Content-Disposition: inline; filename="' . $filename . '"');
				            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
				            header('Pragma: public');
				        } else {
				            header('Content-Disposition: attachment; filename="' . $filename . '"');
				            header('Pragma: no-cache');
				        }
						echo $export_csv_data;
						exit();
					} else {
						$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
						tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action'))));
					}
				} else if ($_POST['btn_csv_import']) {
					if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
						if ($_FILES['csv_import']["size"] > 0) {
							$import_error = false;
							$filename = ($_FILES['csv_import']['tmp_name']);
						    $handle = fopen($filename, 'r+');
							
						    $must_have_field = array(TABLE_HEADING_PRODUCT_ID => 0, TABLE_HEADING_OVERWRITE_MAX_QTY => 0, TABLE_HEADING_BRACKET_SET_ID => 0);
						    if (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
						    	$header_exists_count = 0;
						    	for ($i=0; $i < count($data); $i++) {
						    		if (in_array(trim($data[$i]), array_keys($must_have_field))) {
						    			$must_have_field[trim($data[$i])] = $i;
						    			$header_exists_count++;
						    		}
						    	}
								
						    	if ($header_exists_count != count($must_have_field)) {
						    		$messageStack->add_session("Some required fields does not exists. Please ensure your imported csv file contains " . implode(", ", array_keys($must_have_field)) . ".", 'error');
						    		$import_error = true;
						    	}
						    }
							
						    if (!$import_error) {
						    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
						    		if (trim($data[0]) == '') {	// Assume this row is useless
					    				continue;
					    			}
					    			
									$products_id = $data[$must_have_field[TABLE_HEADING_PRODUCT_ID]];
						    		if (!in_array((int)$products_id, array_keys($buybackSupplierObj->products_arr))) {
						    			$messageStack->add(sprintf(ERROR_IMPORTED_PRODUCT_NOT_CAT, $products_id), 'error');
						    			$import_error = true;
						    			break;
						    		} else {
										$buybackSupplierObj->assign_buyback_product_overwrite_qty($products_id, $data[$must_have_field[TABLE_HEADING_OVERWRITE_MAX_QTY]]);
										$buybackSupplierObj->assign_product_supplier_price_bracket_set($products_id, tep_db_prepare_input($data[$must_have_field[TABLE_HEADING_BRACKET_SET_ID]]));
							    	}
						    	}
								
						    	fclose($handle);
						    	if ($import_error) {
						    		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action'))));
						    	} else {
									$buybackSupplierObj->save_product_supplier_price_bracket_set();
									$buybackSupplierObj->save_buyback_product_overwrite_qty();
						    	}
						    } else {
						    	fclose($handle);
						    	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action'))));
						    }
						} else {
							$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
							tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action'))));
						}
					} else {
						$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
						tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action'))));
					}
				}//endif
			}
		}
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action')) . 'action=edit_list'));
		break;
}

$form_values_arr = $_SESSION[$form_sessname];

if ($buybackSupplierObj) {
    //-----Start Row Colour Brackets ---------------------------------------------------
	$colourBracketHTML = '<tr>
							<td class="dataTableContent" colspan="3">
		                   		<table cellpadding="3" cellspacing="0" border="0">
		                			<tr>
		                				<td class="ordersBoxHeading">'.TABLE_HEADING_PROFIT.'<br>'.TABLE_HEADING_ROW_COLOUR.'</td>';
	$i = 0;
	$color = "#FFFFCC";
	
	foreach ($buybackSupplierObj->colour_bracket_arr as $profit_percentage => $colour_code) {
		$colourBracketHTML .= ' 		<td class="dataTableContent" bgcolor="'.$color.'">'.tep_draw_input_field("profit_percentage[$i]", "$profit_percentage", 'size="5"').'&#37;
											<br>'.draw_colour_picker($i, $colour_code).'
											<br>'.tep_draw_checkbox_field("delete_colour_setting[$i]", '1').' Delete
										</td>';
		$color = (($i % 2)==0) ? "#D7D5D0" : "#FFFFCC";
		$i++;
	}
	
	$colourBracketHTML .= ' 			<td class="dataTableContent" bgcolor="'.$color.'">'.tep_draw_input_field("profit_percentage[$i]", '', 'size="5"').'&#37;<br>'.draw_colour_picker($i, '').'</td>';
	$colourBracketHTML .= ' 		</tr>
						   		</table>
						  	</td>
						 </tr>';
	
    //-----Start Price Brackets ---------------------------------------------------
	$bracket_set_id_arr = array(array('id' => 0, 'text'=>TABLE_HEADING_ASSIGN_BRACKET_SET));
	$bracketsHTML = '<tr><td class="dataTableContent" colspan="2">'
	                . '<table cellpadding="0" cellspacing="0" width="1" border="0"><!-- Open outer 1-->';
	
    //Don't enter if num brackets = 0.
    if (count($buybackSupplierObj->supplier_price_brackets_sets)) {
		foreach ($buybackSupplierObj->supplier_price_brackets_sets as $bracket_set_id => $bracket_set_array) {
			if ($bracket_set_array['cat_id'] == $parent_cat_id) {
				$bracket_set_id_arr[] = array('id' => "$bracket_set_id", 'text' => $bracket_set_array['set_name']);
				$row = $buybackSupplierObj->supplier_price_brackets[$i];
				$colspan = count($bracket_set_array)+1;
			    $bracketsHTML .= '<tr>';
				$bracketsHTML .= '<td class="dataTableContent" colspan="'.$colspan.'">Price Bracket Set : <b>'.$bracket_set_array['set_name']
									. " (ID: $bracket_set_id)</b> Min: {$bracket_set_array['min_qty']} /Max: {$bracket_set_array['max_qty']}"
									.'</td>
								  </tr>
								  <tr>
		        				  <td>
			                          <table cellpadding="3" cellspacing="0" width="1" border="0" width="100%">
			                              <tr>
			                                  <td nowrap align="left" class="ordersBoxHeading">'.TABLE_HEADING_BRACKET_NUMBER.'</td>
			                              </tr>
			                              <tr>
			                                  <td nowrap align="left" class="ordersBoxHeading">'.TEXT_PCT_MAX_INVENTORY.'</td>
			                              </tr>
			                              <tr>
			                                  <td nowrap align="left" class="ordersBoxHeading">'.TEXT_PCT_MARKET_PRICE.'</td>
			                              </tr>
			                          </table>
			                      </td>';
				
			    $color = "#FFFFCC";
				$i = 0;
				foreach ($bracket_set_array['brackets'] as $bracket_id => $bracket_array) {
			        $bracketsHTML .= '<td>
				                          <table cellpadding="3" cellspacing="0" border="0" width="100%">
				                              <tr>
			                                      <td width="12%" align="left" class="ordersBoxHeading">'.($i+1).'</td>
				                              </tr>
				                              <tr>
				                                  <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$bracket_array['bracket_quantity'].'&#37;</td>
				                              </tr>
				                              <tr>
				                                  <td width="12%" align="left" class="dataTableContent" bgcolor="'.$color.'">'.$bracket_sign_arr[(int)$bracket_array['bracket_sign']].number_format($bracket_array['bracket_value'], DISPLAY_PRICE_DECIMAL).$bracket_mode_arr[(int)$bracket_array['bracket_mode']].'</td>
				                              </tr>
				                          </table>
				                      </td>';
					$color = (($i % 2)==0) ? "#D7D5D0" : "#FFFFCC";
					$i++;
				}
				$bracketsHTML .= '</tr>';
				$bracketsHTML .= '<tr>
									<td colspan="'.$colspan.'">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>
								  </tr>';
			}
		}
    }
	
	$bracketsHTML .= '      </table><!-- Close Outer 1-->
	            </td></tr>';
	//-----End Price Brackets ---------------------------------------------------
}
$form_values_arr = $_SESSION[$form_sessname];

//------Globals end.
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET;?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script type="text/javascript" src="includes/general.js"></script>
	<script type="text/javascript" src="includes/javascript/select_box.js"></script>
	<script type="text/javascript">
	<!--
		function toggleCheckBox_pc(mainCheckBoxObj) {
			var numRows = document.getElementById('numRows').value;
			if (mainCheckBoxObj.checked) {
				var pc_state = true;
			} else {
				var pc_state = false;
			}
			
			for (i=0; i<numRows; i++) {
				document.getElementById('pc_' + i).checked = pc_state;
			}
		}
		
		function apply_batch(btnBatchObj) {
			var frm = btnBatchObj.form;
			var numRows = document.getElementById('numRows').value;
			
			if (frm.price_batch != null && frm.price_batch.value != '') {
				price_batchs = parseFloat(frm.price_batch.value);
			}
			
			for (i=0; i<numRows; i++) {
				if (document.getElementById('pc_' + i).checked == true) {
					if (document.getElementById('cus_base_batch').checked == true) {
						document.getElementById('cus_base_' + i).checked = true;
					} else if (document.getElementById('cn_base_batch').checked == true) {
						document.getElementById('cn_base_' + i).checked = true;
					} else if (document.getElementById('price_base_batch').checked == true) {
						document.getElementById('price_base_' + i).checked = true;
					}
					
					document.getElementById('price_' + i).value = price_batchs;
				}
			}
		}
		
		function check_form () {
			var numRows = document.getElementById('numRows').value;
			var error = false;
			var error_msg = '';
			
			for (i=0; i<numRows; i++) {
				if (typeof(document.getElementById('price_' + i)) != 'undefined' && document.getElementById('price_' + i) != null) {
					price = parseFloat(document.getElementById('price_' + i).value);
					
					if (price > 0 && document.getElementById('cus_base_' + i).checked == false && document.getElementById('cn_base_' + i).checked == false && document.getElementById('price_base_' + i).checked == false) {
						error = true;
						error_msg = 'Please select Price Adjust!';
						break;
					}
				}
			}
			
			if (error == true) {
				alert(error_msg);
				return false;
			} else {
				return true;	
			}
		}
		
		function submitDrowpDownForm(doSubmit) {
			if (document.game_selection.game_cat_id.value!=0) {
				if (document.game_selection.site.value == '') {
					alert("Please select a Site.");
					return false;
				}
			} else {
				alert("Please select a Main Category");
				return false;
			}
			
			if (doSubmit)	document.game_selection.submit();
		}
	
    	function enableFieldSet(setName) {
    	    var fld_id_enable_arr = new Array();
    	    var fld_id_disable_arr = new Array();
            switch (setName) {
                case 'rdoSalesRetrievalMethod1':
                    fld_id_disable_arr.push('ofp_last_n_days_sales');
                    fld_id_enable_arr.push('ofp_sales_start_date', 'ofp_sales_end_date');
                    break;
                case 'rdoSalesRetrievalMethod2':
                    fld_id_enable_arr.push('ofp_last_n_days_sales');
                    fld_id_disable_arr.push('ofp_sales_start_date', 'ofp_sales_end_date');
                    break;
                case 'ofp_delivery_option2':
                	var f2f;
                	f2f = document.getElementById('ofp_delivery_option2');
                	if(typeof(f2f) != 'undefined' && typeof(f2f) != null){
                		if(f2f.checked){
                			fld_id_enable_arr.push('ofp_trading_place');
                		}else{
                			fld_id_disable_arr.push('ofp_trading_place');
                		}
                	}
                	break;
                case 'vip_deal_on_game':
               	case 'ofp_deal_with_customers':
               		var vip_trade_us, vip_trade_customer;
               		vip_trade_us = document.getElementById('vip_deal_on_game');
               		vip_trade_customer = document.getElementById('ofp_deal_with_customers');
               		if((typeof(vip_trade_us) != 'undefined' && typeof(vip_trade_us) != null)|| (typeof(vip_trade_customer) != 'undefined' && typeof(vip_trade_customer) != null) ){
                		if(vip_trade_us.checked || vip_trade_customer.checked){
                			fld_id_enable_arr.push('vip_orders_expiry_duration');
							fld_id_enable_arr.push('vip_request_cancellation_duration');
                		}else {
                			fld_id_disable_arr.push('vip_orders_expiry_duration');
							fld_id_disable_arr.push('vip_request_cancellation_duration');
                		}
                	}
                	break;
            }
            setFieldState(fld_id_enable_arr, false);
            setFieldState(fld_id_disable_arr, true);
    	}

    	function setFieldState(fld_id_arr, isDisable) {
            for (i=0;i<fld_id_arr.length;i++) {
                document.getElementById(fld_id_arr[i]).disabled = isDisable;
            }
    	}

		function toggleTbody(type) {
			if (document.getElementById(type + 'Tbody').className == 'hide') {
				document.getElementById(type + 'LinkLabel').innerHTML = 'Hide';
				document.getElementById(type + 'Tbody').className = 'show';
				document.getElementById(type + '_show_mode_1').value = 'show';
				document.getElementById(type + '_show_mode_3').value = 'show';
			} else {
				document.getElementById(type + 'LinkLabel').innerHTML = 'Show';
				document.getElementById(type + 'Tbody').className = 'hide';
				document.getElementById(type + '_show_mode_1').value = 'hide';
				document.getElementById(type + '_show_mode_3').value = 'hide';
			}
		}

		function validate_offer_price_settings() {
			var error_message = '<?=JS_ERROR?>';
			var error = false;
			var focus_field = '';

	        var sales_retrieval_method1 = DOMCall('rdoSalesRetrievalMethod1');
	        var sales_retrieval_method2 = DOMCall('rdoSalesRetrievalMethod2');
	        if (sales_retrieval_method1.checked) {
	            //by date range
				var sales_start_date = DOMCall('ofp_sales_start_date');

				if (trim_str(sales_start_date.value) == '') {
					error_message += '* Please enter the sales start date.' + "\n";
					focus_field = 'ofp_sales_start_date';
					error = true;
				}

				var sales_end_date_temp = DOMCall('ofp_sales_end_date').value;
	            if (trim_str(sales_end_date_temp) == '') {
	                sales_end_date_temp = '<?=date('Y-m-d')?>';
	            }

				if (sales_start_date.value > sales_end_date_temp) {
					error_message += '* Sales Start Date is not after Sales End Date.' + "\n";
					focus_field = 'ofp_sales_start_date';
					error = true;
				}

	        } else if (sales_retrieval_method2.checked){
	            //by last N days
				var ofp_last_n_days_sales = DOMCall('ofp_last_n_days_sales');
				if (!parseInt(ofp_last_n_days_sales.value) > 0) {
					error_message += '* Please enter the last number days of sales .' + "\n";
					focus_field = 'ofp_last_n_days_sales';
					error = true;
				}
	        }

			var inventory_days = DOMCall('ofp_inventory_days');
			if (trim_str(inventory_days.value) == '') {
				error_message += '* Please enter the inventory days.' + "\n";
				focus_field = 'ofp_inventory_days';
				error = true;
			}
			
			var trade_us = DOMCall('vip_deal_on_game');
			var trade_customer = DOMCall('ofp_deal_with_customers');
			var expiry_duration = DOMCall('vip_orders_expiry_duration').value;
			var customer_confirmation_duration = DOMCall('customer_confirmation_duration').value;
			var cancellation_duration = DOMCall('vip_request_cancellation_duration').value;
			if(trade_us.checked || trade_customer.checked){
				if(trim_str(expiry_duration)== '' || trim_str(expiry_duration)== 0){
					error = true;
					error_message += '* Please enter the expiry duration for vip orders .' + "\n";
					focus_field = 'vip_orders_expiry_duration';
				}

				if(trim_str(cancellation_duration)== '' || trim_str(cancellation_duration)== 0){
					error = true;
					error_message += '* Please enter the cancellation duration for vip requests .' + "\n";
					focus_field = 'vip_request_cancellation_duration';
				}
			} else {
				expiry_duration = '';
				cancellation_duration = '';
			}
			
			if(trim_str(customer_confirmation_duration)== '' || trim_str(customer_confirmation_duration)== 0){
				error = true;
				error_message += '* Please enter the customer confirmation duration.' + "\n";
				focus_field = 'customer_confirmation_duration';
			}
			
			if (error == true) {
				alert(error_message);
				document.getElementById(focus_field).focus();
				return false;
			} else {
				return true;
			}
		}

		function toggleCheckBox(mainCheckBoxObj)
		{
			var numRows = document.getElementById('numRows').value;
			if (mainCheckBoxObj.checked) {
				var cb_state = true;
			} else {
				var cb_state = false;
			}
			for (i=0; i<numRows; i++) {
				document.getElementById("cb_" + i).checked = cb_state;
			}
		}

		function clear_input_boxes(ctrlObjName) {
			var numRows = document.getElementById('numRows').value;
			for (i=0; i<numRows; i++) {
				var input_box = DOMCall(ctrlObjName + i);

				if (input_box != null) {
					input_box.value = '';
				}
			}
		}
		
		function checkTime(obj, identifier) {
			var val = parseInt(obj.value, 10);
			
			if (isNaN(val)) {
				obj.value = '';
				return;
			}
			
			var max = (identifier == 'hr') ? 23 : 59;
			
			if (val > max || val < 0) {
				val = '';
			} else {
				if (val < 10) 	val = '0' + val;
			}
			
			obj.value = val;
		}
	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<?
	require(DIR_WS_INCLUDES . 'header.php');
?>
<!-- header_eof //-->

<div id="dhtmlTooltip"></div>
<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/dhtml_tooltip.js"></script>

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
   						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
        			<tr>
	        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
<?
$site_arr = array();
$site_arr[] = array('id' => '', 'text' => 'Please Select');

$site_select_sql = "SELECT site_id, site_name FROM " . TABLE_SITE_CODE . " WHERE site_has_buyback = 1";
$site_result_sql = tep_db_query($site_select_sql);
while ($site_row = tep_db_fetch_array($site_result_sql)) {
	$site_arr[] = array('id' => ($site_row['site_id'] + 1), 'text' => $site_row['site_name']);
}
?>
					<tr>
						<td valign="top" align="left" class="dataTableContent">
							<?=tep_draw_form('game_selection', FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action', 'game_cat_id', 'site')))?>
								<table border="0" width="50%" cellspacing="0" cellpadding="2">
									<tr>
										<td class="main">
											<b><?=TABLE_HEADING_MAIN_CATEGORY?></b>&nbsp;<?=tep_draw_pull_down_menu('game_cat_id', $game_cat_id_arr, (isset($form_values_arr['game_cat_id']) ? $form_values_arr['game_cat_id'] : '0')) . '&nbsp;&nbsp;'?>
										</td>
										<td class="main">
											<b><?=TABLE_HEADING_SITE?></b>&nbsp;<?=tep_draw_pull_down_menu('site', $site_arr, (isset($form_values_arr['site']) ? $form_values_arr['site'] : '')) . '&nbsp;&nbsp;'?>
										</td>
										<td><?=tep_submit_button("Next", "Next", 'name="next" onClick="return submitDrowpDownForm(this);"', 'inputButton')?></td>
									</tr>
								</table>
							</form>
						</td>
						
					</tr>
        			<tr>
	        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '15')?></td>
	      			</tr>
<?
switch($action) {
	case 'edit_list':
		if ($buybackSupplierObj) {
			$buybackSupplierObj->do_calculate_profit = true;
			$buybackSupplierObj->calculate_offer_price();
			
			$not_buyback_start_time_array = explode(',', $buybackSupplierObj->buyback_supplier_settings['ofp_not_buyback_start_time']);
			$not_buyback_end_time_array = explode(',', $buybackSupplierObj->buyback_supplier_settings['ofp_not_buyback_end_time']);
			
			$not_buyback_time_disabled = $buybackSupplierObj->buyback_supplier_settings['ofp_do_not_buyback']=='1' ? '' : ' DISABLED ';
			
			list($open_list_ctrl, $close_list_ctrl) = explode(',', $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller']);
			
			$buybackSupplierObj->buyback_supplier_settings['ofp_delivery_option'] = tep_not_null($buybackSupplierObj->buyback_supplier_settings['ofp_delivery_option']) ? explode(',', $buybackSupplierObj->buyback_supplier_settings['ofp_delivery_option']) : array();
			$buybackSupplierObj->buyback_supplier_settings['vip_trade_mode_option'] = tep_not_null($buybackSupplierObj->buyback_supplier_settings['vip_trade_mode_option']) ? explode(',', $buybackSupplierObj->buyback_supplier_settings['vip_trade_mode_option']) : array();
			
			if ($edit_preferences_permission) {	// Has permission to edit preferences setting
				$quantity_unit_select_sql = "	SELECT categories_configuration_value, 	categories_configuration_title 
												FROM " . TABLE_CATEGORIES_CONFIGURATION . "
												WHERE categories_configuration_key = 'BUYBACK_QUANTITY_UNIT'
													AND categories_id = '".(int)$parent_cat_id."'";
				$quantity_unit_result_sql = tep_db_query($quantity_unit_select_sql);
				if (!tep_db_num_rows($quantity_unit_result_sql)) {
					$cfg_default_value_select_sql = "SELECT *  
													 FROM " . TABLE_CATEGORIES_CONFIGURATION . " 
													 WHERE categories_configuration_key = 'BUYBACK_QUANTITY_UNIT' 
													 	AND categories_id = 0 ";
					$cfg_default_value_result_sql = tep_db_query($cfg_default_value_select_sql);
					if ($cfg_default_value_row = tep_db_fetch_array($cfg_default_value_result_sql)) {
						$insert_buyback_quantity_array = array(	'categories_id' => (int)$parent_cat_id,
							                      				'categories_configuration_title' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_title"]),
							                      				'categories_configuration_key' => 'BUYBACK_QUANTITY_UNIT',
							                      				'categories_configuration_value' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_value"]),
							                      				'categories_configuration_description' => tep_db_prepare_input($cfg_default_value_row["categories_configuration_description"]),
							                      				'categories_configuration_group_id' => (int)$cfg_default_value_row["categories_configuration_group_id"],
							                      				'sort_order' => tep_db_prepare_input($cfg_default_value_row["sort_order"]),
							                      				'last_modified' => 'NULL',
							                      				'date_added' => 'now()',
							                      				'use_function' => tep_db_prepare_input($cfg_default_value_row["use_function"]),
							                      				'set_function' => tep_db_prepare_input($cfg_default_value_row["set_function"])
							                      				);
						tep_db_perform(TABLE_CATEGORIES_CONFIGURATION, $insert_buyback_quantity_array);
					}
					
					$quantity_unit_select_sql = "	SELECT categories_configuration_value, 	categories_configuration_title 
													FROM " . TABLE_CATEGORIES_CONFIGURATION . "
													WHERE categories_configuration_key = 'BUYBACK_QUANTITY_UNIT'
														AND categories_id = '".(int)$parent_cat_id."'";
					$quantity_unit_result_sql = tep_db_query($quantity_unit_select_sql);
				}
?>
					<!-- PREFERENCES START --------------------------------------------------------------------------------------------------- -->
					<tr bgcolor="#d7d5d0">
						<td class="dataTableContent"><b><?=TEXT_PREFERENCES_FOR?></b> <?=$path?>&nbsp;&nbsp;<a href="javascript:void(0);" onclick="toggleTbody('preferences');"><span id="preferencesLinkLabel"><?=$preferences_show_mode_label_default?></span></a></td>
					</tr>
					<tbody id="preferencesTbody" class="<?=$form_values_arr['preferences_show_mode']?>">
					<tr>
						<td>
							<?=tep_draw_form('offer_price_settings', FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action', 'game_cat_id', 'site')) . 'action=edit_preferences&game_cat_id='.$parent_cat_id.'&site='.$site, 'post', 'onSubmit="return validate_offer_price_settings();"')
							. tep_draw_hidden_field('preferences_show_mode', $form_values_arr['preferences_show_mode'], 'id="preferences_show_mode_1"')
							. tep_draw_hidden_field('brackets_show_mode', $form_values_arr['brackets_show_mode'], 'id="brackets_show_mode_1"')
							. tep_draw_hidden_field('prices_show_mode', $form_values_arr['prices_show_mode'], 'id="prices_show_mode_1"')
							?>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
				      			<tr>
            						<td class="main" colspan="2">
                						<table border="0" cellspacing="0" cellpadding="2">
                							<tr valign="top">
                							 	<td class="dataTableContent">
                        							<table border="0" cellspacing="0" cellpadding="2">
                							     		<tr valign="top"><td colspan="3" class="dataTableContent"><b><?=TABLE_HEADING_SALES_RETRIEVAL_METHOD?></b></td></tr>
                							     		<tr valign="top"><td colspan="3" class="dataTableContent"><?=tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_date_range', (isset($buybackSupplierObj->buyback_supplier_settings['ofp_sales_retrieval_method']) ? (($buybackSupplierObj->buyback_supplier_settings['ofp_sales_retrieval_method'] == 'by_date_range') ? true : false) : true), '', ' id="rdoSalesRetrievalMethod1" onClick="enableFieldSet(this.id)" ')?>&nbsp;<?=TABLE_HEADING_SALES_RETRIEVE_BY_DATE_RANGE?></td></tr>
                                                 		<tr valign="top">
                                                    		<td>&nbsp;&nbsp;&nbsp;</td>
                                                    		<td class="dataTableContent" align="left"><?=TABLE_HEADING_SALES_START_DATE?></td>
                                                    		<td class="dataTableContent" align="left"><?=tep_draw_input_field('ofp_sales_start_date', (isset($buybackSupplierObj->buyback_supplier_settings['ofp_sales_start_date']) ? $buybackSupplierObj->buyback_supplier_settings['ofp_sales_start_date'] : ''), ' id="ofp_sales_start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.offer_price_settings.ofp_sales_start_date); }" onKeyPress="return noEnterKey(event)" ')?>
                                                      			<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.offer_price_settings.ofp_sales_start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>&nbsp;<span class="fieldRequired">*</span>
                                                      		</td>
                                                 		</tr>
                                                 		<tr valign="top">
                                                    		<td>&nbsp;&nbsp;&nbsp;</td>
                                                    		<td class="dataTableContent" align="left"><?=TABLE_HEADING_SALES_END_DATE?><br><i>Default: Current Date</i></td>
                                                    		<td class="dataTableContent" align="left"><?=tep_draw_input_field('ofp_sales_end_date', (isset($buybackSupplierObj->buyback_supplier_settings['ofp_sales_end_date']) ? $buybackSupplierObj->buyback_supplier_settings['ofp_sales_end_date'] : ''), ' id="ofp_sales_end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.offer_price_settings.ofp_sales_end_date); }" onKeyPress="return noEnterKey(event)" ')?>
                                                      			<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.offer_price_settings.ofp_sales_end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>
                                                      		</td>
                                                 		</tr>
                							     		<tr valign="top">
                							     			<td colspan="3" class="dataTableContent"><?=tep_draw_radio_field('rdoSalesRetrievalMethod', 'by_last_n_days', false, (($buybackSupplierObj->buyback_supplier_settings['ofp_sales_retrieval_method'] == 'by_last_n_days') ? true : false), ' id="rdoSalesRetrievalMethod2" onClick="enableFieldSet(this.id)" ')?>&nbsp;<?=TABLE_HEADING_SALES_RETRIEVE_BY_LAST?>
                                                      			&nbsp;<?=tep_draw_input_field('ofp_last_n_days_sales', (isset($buybackSupplierObj->buyback_supplier_settings['ofp_last_n_days_sales']) ? $buybackSupplierObj->buyback_supplier_settings['ofp_last_n_days_sales'] : ''), ' id="ofp_last_n_days_sales" size="4" maxlength="4"')?>
                                                      			&nbsp;<?=TEXT_DAYS?>
                                                      		</td>
                                                      	</tr>
                        								<tr>
                				        					<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
                				      					</tr>
                							    		<tr valign="top">
                							         		<td colspan="2" class="dataTableContent"><b><?=TABLE_HEADING_DAYS_INVENTORY?></b></td>
                							         		<td class="dataTableContent"><?=tep_draw_input_field('ofp_inventory_days', (isset($buybackSupplierObj->buyback_supplier_settings['ofp_inventory_days']) ? $buybackSupplierObj->buyback_supplier_settings['ofp_inventory_days'] : '' ), 'size="10" id="ofp_inventory_days" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')  . '&nbsp;' . TEXT_DAYS?></td>
                							    		</tr>
                        								<tr>
                				        					<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
                				      					</tr>
                							    		<tr valign="top">
                							         		<td colspan="2" class="dataTableContent"><b><?=TABLE_HEADING_PERCENTAGE_OF_QUANTITY?></b></td>
                							         		<td class="dataTableContent"><?=tep_draw_input_field('ofp_max_inv_space_percentage', (isset($buybackSupplierObj->buyback_supplier_settings['ofp_max_inv_space_percentage']) ? $buybackSupplierObj->buyback_supplier_settings['ofp_max_inv_space_percentage'] : '' ), 'size="10" id="ofp_max_inv_space_percentage" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')  . '&nbsp;&#37;'?></td>
                							    		</tr>
                        								<tr>
                				        					<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
                				      					</tr>
<?			if ($quantity_unit_row = tep_db_fetch_array($quantity_unit_result_sql)) { ?>
                							    		<tr valign="top">
                							         		<td colspan="2" class="dataTableContent"><b><?=$quantity_unit_row['categories_configuration_title'] . '&nbsp;&nbsp;(Configuration)'?></b></td>
                							         		<td class="dataTableContent"><?=tep_draw_input_field('buyback_quantity_unit', $quantity_unit_row['categories_configuration_value'], 'size="10" id="buyback_quantity_unit" ')?></td>
                							    		</tr>
<?			} ?>
                        								<tr>
                				        					<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
                				      					</tr>
                        								<tr>
                				        					<td colspan="3"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
                				      					</tr>
                                                	</table>
                							 	</td>
                							 	<td>
                							 		<table border="0" width="100%" cellspacing="0" cellpadding="2">
                							 			<tr>
                							 				<td class="dataTableContent"><?=ENTRY_PREF_DO_NOT_BUYBACK?></td>
                							 				<td class="dataTableContent"><?=tep_draw_checkbox_field("ofp_do_not_buyback", '1', ($buybackSupplierObj->buyback_supplier_settings['ofp_do_not_buyback']=='1' ? true : false), '', 'onClick="var not_buyback_time_disabled=(this.checked ? false : true);disable_multi_elements_input(\'offer_price_settings\', \'not_buyback_start_hr\', not_buyback_time_disabled);disable_multi_elements_input(\'offer_price_settings\', \'not_buyback_start_min\', not_buyback_time_disabled);disable_multi_elements_input(\'offer_price_settings\', \'not_buyback_end_hr\', not_buyback_time_disabled);disable_multi_elements_input(\'offer_price_settings\', \'not_buyback_end_min\', not_buyback_time_disabled);"')?></td>
                							 			</tr>
                							 			<tr>
                							 				<td class="dataTableContent">&nbsp;</td>
	                							 			<td class="dataTableContent">
															<?
																for ($time_cnt=0; $time_cnt < 3; $time_cnt++) {
																	list($not_buyback_start_hr, $not_buyback_start_min) = isset($not_buyback_start_time_array[$time_cnt]) ? explode(':', $not_buyback_start_time_array[$time_cnt]) : array('', '');
																	list($not_buyback_end_hr, $not_buyback_end_min) = isset($not_buyback_end_time_array[$time_cnt]) ? explode(':', $not_buyback_end_time_array[$time_cnt]) : array('', '');
																	
																	echo tep_draw_input_field('not_buyback_start_hr[]', $not_buyback_start_hr, 'SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'hr\')"'.$not_buyback_time_disabled) . ':' . tep_draw_input_field('not_buyback_start_min[]', $not_buyback_start_min, 'SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'min\')"'.$not_buyback_time_disabled) . TEXT_OFP_TIME_RANGE . tep_draw_input_field('not_buyback_end_hr[]', $not_buyback_end_hr, 'SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'hr\')"'.$not_buyback_time_disabled) . ':' . tep_draw_input_field('not_buyback_end_min[]', $not_buyback_end_min, 'SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'min\')"'.$not_buyback_time_disabled) . '<br>';
																}
																
																echo TEXT_OFP_NOT_BUYBACK_ANYTIME;
															?>
															</td>
														</tr>
														<tr>
															<td class="dataTableContent" valign="top"><?=ENTRY_PREF_BUYBACK_LIST_CONTROLLER?></td>
                							 				<td>
                							 					<table border="0" width="100%" cellspacing="0" cellpadding="0">
                							 						<tr>
                							 							<td class="dataTableContent"><?=ENTRY_PREF_BUYBACK_LIST_CLOSE_CONTROLLER?></td>
                							 							<td class="dataTableContent"><?=tep_draw_input_field('ofp_close_list_ctrl', $close_list_ctrl, 'size="5" id="ofp_purchase_eta" onChange="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateSignInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateSignInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
                							 						</tr>
                							 						<tr>
                							 							<td class="dataTableContent"><?=ENTRY_PREF_BUYBACK_LIST_OPEN_CONTROLLER?></td>
                							 							<td class="dataTableContent"><?=tep_draw_input_field('ofp_open_list_ctrl', $open_list_ctrl, 'size="5" id="ofp_purchase_eta" onChange="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateSignInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateSignInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
                							 						</tr>
                							 					</table>
                							 				</td>
                							 			</tr>
                							 			<tr>
                							 				<td class="dataTableContent"><?=ENTRY_PREF_PURCHASE_ETA_OFFSET?></td>
                							 				<td class="dataTableContent"><?=tep_draw_input_field('ofp_purchase_eta', (isset($buybackSupplierObj->buyback_supplier_settings['ofp_purchase_eta']) ? $buybackSupplierObj->buyback_supplier_settings['ofp_purchase_eta'] : '' ), 'size="5" id="ofp_purchase_eta" onChange="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateSignInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value) != \'\' && !validateSignInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
                							 			</tr>
                							 			<tr>
                							 				<td class="dataTableContent"><?=ENTRY_PREF_DELIVERY_OPTION?></td>
                							 				<td class="dataTableContent" nowrap>
                							 				    <?=tep_draw_checkbox_field("ofp_delivery_option[]", "ofp_deal_on_mail", (in_array('ofp_deal_on_mail', $buybackSupplierObj->buyback_supplier_settings['ofp_delivery_option']) ? true : false), ' id="ofp_delivery_option1"') . ENTRY_PREF_DEALING_ON_MAIL ?>
                							 				</td>
                							 			</tr>
                							 			<tr>
                							 				<td class="dataTableContent">&nbsp;</td>
                							 				<td class="dataTableContent" nowrap>
                							 				    <?=tep_draw_checkbox_field("ofp_delivery_option[]", "ofp_deal_on_game", false, (in_array('ofp_deal_on_game', $buybackSupplierObj->buyback_supplier_settings['ofp_delivery_option']) ? true : false), ' id="ofp_delivery_option2" onClick="enableFieldSet(this.id)" ') . ENTRY_PREF_DEALING_ON_GAME .'&nbsp;'.tep_draw_input_field('ofp_trading_place', (isset($buybackSupplierObj->buyback_supplier_settings['ofp_trading_place']) ? $buybackSupplierObj->buyback_supplier_settings['ofp_trading_place'] : '' ), 'size="31" id="ofp_trading_place"'). '&nbsp;' .ENTRY_PREF_TRADING_PLACE?>
                							 				</td>
                							 			</tr>
                							 			<tr>
                							 				<td class="dataTableContent"><?=ENTRY_PREF_VIP_TRADE_MODE?><br><small><span class="redIndicator"><?=TEXT_UNCHECK_ALL_TO_DISABLE_VIP?></span></small></td>
                							 				<td class="dataTableContent" nowrap>
                							 				   <?=tep_draw_checkbox_field("vip_trade_mode_option[]", "vip_deal_on_game", false, (in_array('vip_deal_on_game', $buybackSupplierObj->buyback_supplier_settings['vip_trade_mode_option']) ? true : false), ' id="vip_deal_on_game" onClick="enableFieldSet(this.id)"') . ENTRY_PREF_VIP_TRADE_WITH_US;?>
                							 				    &nbsp;<?
                							 				    echo tep_draw_checkbox_field("vip_trade_mode_option[]", "ofp_deal_with_customers", false, (in_array('ofp_deal_with_customers', $buybackSupplierObj->buyback_supplier_settings['vip_trade_mode_option']) ? true : false), ' id="ofp_deal_with_customers" onClick="enableFieldSet(this.id)"') . ENTRY_PREF_VIP_TRADE_WITH_CUSTOMERS;
                							 				    ?>
                							 				</td>
                							 			</tr>
														<tr>
                							 				<td class="dataTableContent"><?=ENTRY_PREF_VIP_REQUEST_CANCELLATION_DURATION?></td>
                							 				<td class="dataTableContent" nowrap><?=tep_draw_input_field('vip_request_cancellation_duration', (isset($buybackSupplierObj->buyback_supplier_settings['vip_request_cancellation_duration']) ? $buybackSupplierObj->buyback_supplier_settings['vip_request_cancellation_duration'] : '' ), 'size="8" id="vip_request_cancellation_duration"').ENTRY_PREF_VIP_ORDERS_EXPIRY_DURATION_IN_MINUTES?>               							 		
                							 				</td>
                							 			</tr>
                							 			<tr>
                							 				<td class="dataTableContent"><?=ENTRY_PREF_VIP_ORDERS_EXPIRY_DURATION?></td>
                							 				<td class="dataTableContent" nowrap><?=tep_draw_input_field('vip_orders_expiry_duration', (isset($buybackSupplierObj->buyback_supplier_settings['vip_orders_expiry_duration']) ? $buybackSupplierObj->buyback_supplier_settings['vip_orders_expiry_duration'] : '' ), 'size="8" id="vip_orders_expiry_duration"').ENTRY_PREF_VIP_ORDERS_EXPIRY_DURATION_IN_MINUTES?>               							 		
                							 				</td>
                							 			</tr>
                							 			<tr>
                							 				<td class="dataTableContent"><?=ENTRY_PREF_CUSTOMER_CONFIRMATION_DURATION?></td>
                							 				<td class="dataTableContent" nowrap><?=tep_draw_input_field('customer_confirmation_duration', (isset($buybackSupplierObj->buyback_supplier_settings['customer_confirmation_duration']) ? $buybackSupplierObj->buyback_supplier_settings['customer_confirmation_duration'] : '' ), 'size="8" id="customer_confirmation_duration"').ENTRY_PREF_VIP_ORDERS_EXPIRY_DURATION_IN_MINUTES?>               							 		
                							 				</td>
                							 			</tr>
                							 		</table>
                							 	</td>
                							</tr>
            							<tr valign="top">
							         		<td colspan="3" class="dataTableContent"><b><?=TABLE_HEADING_ROW_COLOUR?></b></td>
							    		</tr>
							    		<?=$colourBracketHTML?>
                						</table>
            						</td>
            						<td align="right" valign="bottom" class="main"><?=tep_submit_button(IMAGE_BUTTON_UPDATE_PREFERENCES, IMAGE_BUTTON_UPDATE_PREFERENCES, '', 'inputButton')?>
					        			<?php
					                        if ($buybackSupplierObj->buyback_supplier_settings['ofp_sales_retrieval_method'] == 'by_last_n_days') {
					                            $js = "\n enableFieldSet('rdoSalesRetrievalMethod2');";
					                        } else {
					                            //if date range selected, or nothing set.
					                            $js = "\n enableFieldSet('rdoSalesRetrievalMethod1');";
					                        }
					                        
					                        if (in_array('ofp_deal_on_game', $buybackSupplierObj->buyback_supplier_settings['ofp_delivery_option']) == false){
					                        	$js .= "\nvar disable_arr = new Array();";
												$js .= "\ndisable_arr.push('ofp_trading_place');";
												$js .= "\nsetFieldState(disable_arr, true);";
					                    	}
					                    	
					                    	if(in_array('vip_deal_on_game', $buybackSupplierObj->buyback_supplier_settings['vip_trade_mode_option']) == false && in_array('ofp_deal_with_customers', $buybackSupplierObj->buyback_supplier_settings['vip_trade_mode_option']) == false){
					                    		$js .= "\nvar disable_arr = new Array();";
												$js .= "\ndisable_arr.push('vip_orders_expiry_duration');";
												$js .= "\ndisable_arr.push('vip_request_cancellation_duration');";
												$js .= "\nsetFieldState(disable_arr, true);";
					                    	}	
					        			?>
					        			<script type="text/javascript">
											<?=$js?>
										</script>
            						</td>
        						</tr>
            					<tr>
            						<td colspan="2"><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
            					</tr>
        					</table>
    						</form>
        				</td>
        			</tr>
        			<tr>
	        			<td></td>
	      			</tr>
        			<tr>
						<td><?=tep_draw_separator()?></td>
					</tr>

					</tbody>
					<!-- PREFERENCES END -->
<?			}
			
			if ($edit_offer_prices_permission) {	// Has permission to edit offer price
?>
					<tr>
	        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
	      			</tr>
					<!-- BRACKETS START ----------------------------------------------------------------------------------------------------- -->
					<tr bgcolor="#d7d5d0">
						<td class="dataTableContent"><b><?=TEXT_OFFER_PRICE_BRACKETS_FOR?></b> <?=$path?>&nbsp;&nbsp;<a href="javascript:void(0);" onclick="toggleTbody('brackets');"><span id="bracketsLinkLabel"><?=$brackets_show_mode_label_default?></span></a></td>
					</tr>
					<tbody id="bracketsTbody" class="<?=$form_values_arr['brackets_show_mode']?>">
						<?=$bracketsHTML?>
        			<tr>
	        			<td></td>
	      			</tr>
        			<tr>
						<td><?=tep_draw_separator()?></td>
					</tr>
					</tbody>
					<!-- BRACKETS END -->
					<tr>
	        			<td></td>
	      			</tr>
					<!-- PRICES START ------------------------------------------------------------------------------------------------------- -->
					<tr bgcolor="#d7d5d0">
						<td class="dataTableContent"><b><?=TEXT_PRICES_FOR?></b> <?=$path?>&nbsp;&nbsp;<a href="javascript:void(0);" onclick="toggleTbody('prices');"><span id="pricesLinkLabel"><?=$prices_show_mode_label_default?></span></a></td>
					</tr>
					<tr>
						<td class="dataTableContent">Legend : <?=$svrfull_tag_o. 'Server Full' . $svrfull_tag_c?>,  <?=$backorder_tag_o . 'Back-Order' . $backorder_tag_c?> , <?=$error_tag_o . 'Error' . $error_tag_c?></td>
					</tr>
					<tbody id="pricesTbody" class="<?=$form_values_arr['prices_show_mode']?>">
					<tr>
						<td>
						<?
							echo tep_draw_form('offer_price_prices', FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action', 'game_cat_id', 'site')) . 'action=update_list&game_cat_id='.$parent_cat_id.'&site='.$site, 'post', 'enctype="multipart/form-data"') . 
									tep_draw_hidden_field('preferences_show_mode', $form_values_arr['preferences_show_mode'], 'id="preferences_show_mode_3"') . 
									tep_draw_hidden_field('brackets_show_mode', $form_values_arr['brackets_show_mode'], 'id="brackets_show_mode_3"') . 
									tep_draw_hidden_field('prices_show_mode', $form_values_arr['prices_show_mode'], 'id="prices_show_mode_3"');
							
							$serversHTML = '';
							$i = 0;
																					
							foreach ($buybackSupplierObj->products_arr as  $products_id => $products_row) {
								$product_display_name = tep_display_category_path($products_row['cat_path']." > ".$products_row['products_name'], $products_row['cat_id']);
								
								$svrfull_tag_open = '';
								$svrfull_tag_close = '';
								if (!$products_row['is_buyback']) {
									$svrfull_tag_open = $svrfull_tag_o;
									$svrfull_tag_close = $svrfull_tag_c;
								}
								
								$backorder_tag_open = '';
								$backorder_tag_close = '';
								if ($products_row['is_backorder']) {
									$backorder_tag_open = $backorder_tag_o;
									$backorder_tag_close = $backorder_tag_c;
								}
								$error_tag_open = '';
								$error_tag_close = '';
								if ($products_row['is_error']) {
									$error_tag_open = $error_tag_o;
									$error_tag_close = $error_tag_c;
								}
								
								$all_tags_open = $svrfull_tag_open . $backorder_tag_open . $error_tag_open;
								$all_tags_close = $svrfull_tag_close . $backorder_tag_close . $error_tag_close;
								
								$serversHTML .= "<tr bgcolor='{$products_row['profit_colour']}'>";
								//max Qty input box. Using products_id for name attrib, row number for id attrib.
								$serversHTML .= '<td class="ordersRecords" nowrap>' . tep_draw_checkbox_field('cb['.$i.']', $products_id, false, '', 'id="cb_'.$i.'"') . '&nbsp;' . $all_tags_open . $product_display_name;
								if (isset($products_row['error_msg']) && $products_row['error_msg']) {
									$serversHTML .= '&nbsp;<span class="ordersRecords" onMouseover="ddrivetip(\''.$products_row['error_msg'].'\', \'\', 200);" onMouseout="hideddrivetip();">' . tep_image(DIR_WS_IMAGES . 'icon_info.gif', '') . '</span>';
								}
								
								if (isset($products_row['upper_min_qty']) && $products_row['upper_min_qty'] > 0 && $products_row['upper_min_qty'] < $products_row['max_qty']) {
									$serversHTML .= '<br>(Min:'.$products_row['min_qty'].'/Max:'.$products_row['upper_min_qty'].' OR '.$products_row['max_qty'].')&nbsp;' . $all_tags_close .'</td>';
								} else {
									$serversHTML .= '<br>(Min:'.$products_row['min_qty'].'/Max:'.$products_row['max_qty'].')&nbsp;'.$all_tags_close .'</td>';
								}
								
								$serversHTML .= "<td align='right' class='ordersRecords'>" . $all_tags_open . $products_row['max_inventory_space_derived'] . $all_tags_close .'</td>';
								$serversHTML .= "<td align='right' class='ordersRecords'>" . tep_draw_input_field("maxQty[$products_id]", (trim($products_row['max_inventory_space_overwrite']) != '' ? $products_row['max_inventory_space_overwrite'] : ''), ' size="8" id="maxQty'.$i.'" onChange="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateSignInteger(trim_str(this.value))) ) { this.value = \'\'; }" onBlur="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateSignInteger(trim_str(this.value))) ) { this.value = \'\'; }" ') .'</td>';
								$serversHTML .= "<td align='right' class='ordersRecords'>" . $all_tags_open . $products_row['forecast_available_qty'] . $all_tags_close .'</td>';
								$serversHTML .= '<td align="right" class="ordersRecords">' . $all_tags_open . ($products_row['is_error'] ? $error_tag_o . $products_row['forecast_actual_qty'] . $error_tag_c : $products_row['forecast_actual_qty']) . $all_tags_close .'</td>';
								$serversHTML .= "<td align='right' class='ordersRecords'>" . $all_tags_open . $products_row['suggest_qty'] . $all_tags_close .'</td>';
								$serversHTML .= "<td align='right' class='ordersRecords'>" . $all_tags_open . $products_row['customer_price'] . $all_tags_close .'</td>';
								//$serversHTML .= "<td align='right' class='ordersRecords'>" . $all_tags_open . $products_row['avg_competitor_price_final'] . $all_tags_close .'</td>';
								$serversHTML .= "<td align='right' class='ordersRecords'>" . $all_tags_open . $products_row['avg_offer_price'] . $all_tags_close .'</td>';
								$serversHTML .= "<td align='right' class='ordersRecords'>" . $all_tags_open . $products_row['profit'] . $all_tags_close .'</td>';
								$serversHTML .= "<td align='right' class='ordersRecords'>" . $all_tags_open . $products_row['bracket_set_id'] . $all_tags_close .'</td>';
								$serversHTML .= "<td align='right' class='ordersRecords'>" . $all_tags_open . $buybackSupplierObj->supplier_price_brackets_sets[$products_row['bracket_set_id']]['brackets'][$products_row['buyback_supplier_price_bracket_id']]['bracket_quantity'] . '&#37;' . $all_tags_close .'</td>';
								
								$serversHTML .= '</tr>';
								$i++;
							}
							echo tep_draw_hidden_field('numRows', ($i), 'id="numRows"');
							?>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td class="ordersBoxHeading"><?=tep_draw_checkbox_field('checkAll', '', false, '', ' onclick="toggleCheckBox(this)" ') . '&nbsp;' . TABLE_HEADING_PRODUCT?></td>
									<td class="ordersBoxHeading" align="center"><?=TABLE_HEADING_MAX_QUANTITY?></td>
									<td width="8%" class="ordersBoxHeading" align="center">
									    <?=TABLE_HEADING_OVERWRITE_MAX_QTY?>&nbsp;
										<span class='ordersRecords' onMouseover="ddrivetip('<?=MESSAGE_MAXQTY_USAGE?>', '', 200);" onMouseout="hideddrivetip();"><?=tep_image(DIR_WS_IMAGES . 'icon_info.gif', '')?></span><br/>
									    <a href="javascript:;" onclick="clear_input_boxes('maxQty')" class="highlightLink"><?=TEXT_CLEAR?></a>
									</td>
									<td width="8%" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_AVAILABLE_QUANTITY?></td>
									<td width="8%" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_ACTUAL_QUANTITY?></td>
									<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_SUGGEST_QUANTITY?></td>
									<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_CUSTOMER_PRICE?></td>
									<!--<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_AVERAGE_MARKET_PRICE?></td>//-->
									<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_AVERAGE_OFFER_PRICE?></td>
									<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_PROFIT?></td>
									<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_BRACKET_SET_ID?></td>
									<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_ACTIVE_BRACKET?></td>
								</tr>
								<?=$serversHTML?>
								<tr>
				        			<td colspan="12"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
				      			</tr>
								<tr>
									<td colspan="12" align="left">
										<table border="0" width="100%" cellpadding="0" cellspacing="0">
										<tr>
											<td>
												<?=tep_draw_pull_down_menu('assign_bracket_set_id', $bracket_set_id_arr, '0')?>
												<input type="submit" name="btn_assign_bracket" value="Assign Brackets" title="Assign Brackets" class="inputButton">
											</td>
											<td>
												<?=tep_draw_file_field('csv_import', 'size="50"')?>
												<input type="submit" name="btn_csv_import" value="Import" title="Import csv file" class="inputButton">
											</td>
											<td>
												<input type="submit" name="btn_csv_export" value="Export" title="Export as csv file" class="inputButton">									
											</td>
											<td>
												<input type="submit" name="btn_update" value="Update" title="Update" class="inputButton">
											</td>
										</tr>
										</table>
									</td>
								</tr>
        					</table>
       						</form>
        				</td>
        			</tr>
					</tbody>
					<!-- PRICES END -->
<?			}
		}
		break;
	
	case 'price_control':
		if ($buybackSupplierObj) {
			$buybackSupplierObj->do_calculate_profit = true;
			$buybackSupplierObj->calculate_offer_price();
?>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td>
							<?=tep_draw_form('set_price_batch', FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, '', 'post')?>
								<table border="0" width="65%" cellspacing="0" cellpadding="2">
									<tr class="ordersBoxHeading">
										<td width="25%">&nbsp;</td>
										<td align="center"><?=TABLE_HEADING_RATIO_PRICE?></td>
										<td colspan="3" align="center"><?=TABLE_HEADING_PRICE_ADJUST?></td>
									</tr>
									<tr class="ordersListingEven">
										<td class="ordersRecords"><?=TEXT_APPLY_TO_SELECTED?></td>
										<td class="ordersRecords"><?=tep_draw_input_field('price_batch', '', 'id="price_batch"')?></td>
										<td class="ordersRecords"><?=tep_draw_radio_field('price_base_batch', '0', false, '', 'id="cus_base_batch"') . ENTRY_SELLING_PRICE?></td>
										<td class="ordersRecords"><?=tep_draw_radio_field('price_base_batch', '1', false, '', 'id="cn_base_batch"') . ENTRY_CN_BUYBACK_PRICE?></td>
										<td class="ordersRecords"><?=tep_draw_radio_field('price_base_batch', '999', false, '', 'id="price_base_batch"') . ENTRY_OVERWRITE_PRICE?></td>
									</tr>
									<tr>
										<td colspan="5"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
									</tr>
									<tr>
										<td colspan="5" align="right"><?=tep_button(IMAGE_BUTTON_BATCH_APPLY, IMAGE_BUTTON_BATCH_APPLY, '', 'onClick="apply_batch(this);"')?></td>
									</tr>
								</table>
							</form>
						</td>
					</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
					</tr>
					<tr>
						<td>
							<?=tep_draw_form('set_price', FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE, tep_get_all_get_params(array('action', 'game_cat_id', 'site')) . 'action=update_price&game_cat_id='.$parent_cat_id.'&site='.$site, 'post', 'onSubmit="return check_form();"')?>
								<table border="0" width="95%" cellspacing="0" cellpadding="2">
									<tr class="ordersBoxHeading">
										<td><?=tep_draw_checkbox_field('checkAll', '', false, '', ' onclick="toggleCheckBox_pc(this);" ') . '&nbsp;' . TABLE_HEADING_PRODUCT_NAME?></td>
										<td align="right"><?=TABLE_HEADING_SELLING_PRICE?></td>
										<td align="right"><?=TABLE_HEADING_CN_BUYBACK_PRICE?></td>
										<td align="center"><?=TABLE_HEADING_RATIO_PRICE?></td>
										<td colspan="3" align="center"><?=TABLE_HEADING_PRICE_ADJUST?></td>
									</tr>
									<tr class="ordersBoxHeading">
										<td colspan="4">&nbsp;</td>
										<td><?=ENTRY_SELLING_PRICE?></td>
										<td><?=ENTRY_CN_BUYBACK_PRICE?></td>
										<td><?=ENTRY_OVERWRITE_PRICE?></td>
									</tr>
<?
			$i = 0;
			
			foreach ($buybackSupplierObj->products_arr as $products_id => $products_row) {
				$buyback_pc_info_select_sql = "	SELECT buyback_price_control_for_site_id, buyback_price_control_value FROM " . TABLE_BUYBACK_PRICE_CONTROL . " WHERE products_id ='" . (int)$products_id . "'";
				$buyback_pc_info_result_sql = tep_db_query($buyback_pc_info_select_sql);
				$buyback_pc_info_row = tep_db_fetch_array($buyback_pc_info_result_sql);
				
				$row_style = ($i%2) ? 'ordersListingEven' : 'ordersListingOdd';
				$product_display_name = tep_display_category_path(tep_get_product_path($products_id), $products_row['cat_id']);
?>
									<?="<tr class='$row_style' onmouseover=\"rowOverEffect(this, 'ordersListingRowOver')\" onmouseout=\"rowOutEffect(this, '$row_style')\" onclick=\"rowClicked(this, '$row_style')\">";?>
										<td class="ordersRecords"><?=tep_draw_checkbox_field('pc['.$i.']', $products_id, false, '', 'id="pc_'.$i.'"') . '&nbsp;' . $product_display_name?></td>
										<td class="ordersRecords" align="right"><?=$products_row['customer_price']?></td>
										<td class="ordersRecords" align="right"><?=$products_row['avg_offer_price']?></td>
										<td class="ordersRecords" align="center"><?=tep_draw_input_field('price[' . $products_id . ']', $buyback_pc_info_row['buyback_price_control_value'], 'id="price_'.$i.'"')?></td>
										<td class="ordersRecords" align="center"><?=tep_draw_radio_field('price_base[' . $products_id . ']', '0', (($buyback_pc_info_row['buyback_price_control_for_site_id'] == '0') ? true : false), '', 'id="cus_base_'.$i.'"')?></td>
										<td class="ordersRecords" align="center"><?=tep_draw_radio_field('price_base[' . $products_id . ']', '1', (($buyback_pc_info_row['buyback_price_control_for_site_id'] == '1') ? true : false), '', 'id="cn_base_'.$i.'"')?></td>
										<td class="ordersRecords" align="center"><?=tep_draw_radio_field('price_base[' . $products_id . ']', '999', (($buyback_pc_info_row['buyback_price_control_for_site_id'] == '999') ? true : false), '', 'id="price_base_'.$i.'"')?></td>
									</tr>
<?
				$i++;
			}
			echo tep_draw_hidden_field('numRows', ($i), 'id="numRows"');
?>
									<tr>
										<td colspan="7"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
									</tr>
									<tr>
										<td align="right" colspan="6"><?=tep_submit_button(IMAGE_UPDATE, IMAGE_UPDATE, 'name="update_price"', 'inputButton');?></td>
									</tr>
								</table>
							</form>
						</td>
					</tr>
<?
		}
		break;
}
?>
				</table>
			</td>
		</tr>
	</table>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>