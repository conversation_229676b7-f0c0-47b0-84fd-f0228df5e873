<?php
require_once('includes/application_top.php');

$form_sessname = basename($_SERVER['PHP_SELF']);
define('DISPLAY_PRICE_DECIMAL', 4);

$bracket_mode_arr = array(0 => '$', 1 => '&#37;');
$bracket_sign_arr = array(0 => '+', 1 => '-');

$action = (isset($_GET['action']) ? $_GET['action'] : 'show_bracket_sets');

switch($action) {
	case 'delete_brackets':
		$bracket_id = tep_db_prepare_input(trim($_GET['bracket_id']));
		if ($bracket_id) {
			$delete_bracket_sql = "delete from ".TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET." where buyback_supplier_price_bracket_id='$bracket_id'";
			tep_db_query($delete_bracket_sql);
		}
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action', 'bracket_id')) . 'action=show_edit_set'));
		
		break;
	case 'edit_set':
		$set_id = tep_db_prepare_input(trim($_GET['set_id']));
		if ($set_id) {
			$new_set_name = tep_db_prepare_input($_POST['set_name']);
			$set_cat_id = tep_db_prepare_input($_POST['set_cat_id']);
			$min_qty = tep_db_prepare_input($_POST['min_qty']);
			$min_purchase_qty = tep_not_null($_POST['min_purchase_demand']) ? (int)$_POST['min_purchase_demand'] : 'NULL';
			$max_qty = tep_db_prepare_input($_POST['max_qty']);
			$max_qty_is_overwrite = tep_db_prepare_input($_POST['rdoMaxQtySource']);
			
			$bracket_set_update_arr = array('buyback_supplier_price_bracket_set_name' => $new_set_name,
											'buyback_supplier_price_bracket_set_cat_id' => $set_cat_id,
											'buyback_supplier_price_bracket_set_minimum_value' => $min_qty,
											'buyback_supplier_price_bracket_set_min_purchase' => $min_purchase_qty,
											'buyback_supplier_price_bracket_set_maximum_value' => $max_qty,
											'buyback_supplier_price_bracket_set_maximum_is_overwrite' => $max_qty_is_overwrite
											);
			tep_db_perform(TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET_SET, $bracket_set_update_arr, 'update', "buyback_supplier_price_bracket_set_id='$set_id'");
			
			$delete_all_set_brackets_sql = "DELETE FROM " . TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET . " 
											WHERE buyback_supplier_price_bracket_set_id = '".$set_id."'";
			$delete_success = tep_db_query($delete_all_set_brackets_sql);
			
			if ($delete_success) {
				foreach ($_POST['txtPct'] as $buyback_supplier_price_bracket_id => $buyback_supplier_price_bracket_quantity) {
					$buyback_supplier_price_bracket_value = $_POST['txtMultiplier'][$buyback_supplier_price_bracket_id];
					
					if (!$buyback_supplier_price_bracket_value || !$buyback_supplier_price_bracket_quantity) {
						continue;
					}
					
					$buyback_supplier_price_bracket_mode = $_POST['selMode'][$buyback_supplier_price_bracket_id];
					$buyback_supplier_price_bracket_sign = $_POST['selSign'][$buyback_supplier_price_bracket_id];
					
					$insert_bracket_arr = array('buyback_supplier_price_bracket_set_id' => $set_id,
												'buyback_supplier_price_bracket_id' => tep_db_prepare_input($buyback_supplier_price_bracket_id),
												'buyback_supplier_price_bracket_quantity' => tep_db_prepare_input($buyback_supplier_price_bracket_quantity),
												'buyback_supplier_price_bracket_value' => tep_db_prepare_input($buyback_supplier_price_bracket_value),
												'buyback_supplier_price_bracket_mode' => tep_db_prepare_input($buyback_supplier_price_bracket_mode),
												'buyback_supplier_price_bracket_sign' => tep_db_prepare_input($buyback_supplier_price_bracket_sign)
											);
					$success = tep_db_perform(TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET, $insert_bracket_arr);
				}
			}
		}
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action')) . 'action=show_edit_set'));
		
		break;
	case 'show_edit_set':
		$set_id = tep_db_prepare_input(trim($_GET['set_id']));
		
		if ($set_id) {
			$bracket_sets_select_sql = "SELECT * 
										FROM " . TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET_SET . " 
										WHERE buyback_supplier_price_bracket_set_id = '" . tep_db_input($set_id) . "'";
			$bracket_sets_result_sql = tep_db_query($bracket_sets_select_sql);
			if ($bracket_sets_row = tep_db_fetch_array($bracket_sets_result_sql)) {
				$set_name = $bracket_sets_row['buyback_supplier_price_bracket_set_name'];
				$set_cat_id = $bracket_sets_row['buyback_supplier_price_bracket_set_cat_id'];
				$min_qty = $bracket_sets_row['buyback_supplier_price_bracket_set_minimum_value'];
				$min_purchase_qty = $bracket_sets_row['buyback_supplier_price_bracket_set_min_purchase'];
				$max_qty = $bracket_sets_row['buyback_supplier_price_bracket_set_maximum_value'];
				$max_qty_is_overwrite = $bracket_sets_row['buyback_supplier_price_bracket_set_maximum_is_overwrite'];
			}
			
			$bracket_select_sql = "	SELECT buyback_supplier_price_bracket_id, buyback_supplier_price_bracket_quantity, buyback_supplier_price_bracket_value, buyback_supplier_price_bracket_mode, buyback_supplier_price_bracket_sign 
									FROM " . TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET . " 
									WHERE buyback_supplier_price_bracket_set_id = '" . tep_db_input($set_id) . "' 
									ORDER BY buyback_supplier_price_bracket_quantity ASC";
			$bracket_result_sql = tep_db_query($bracket_select_sql);
			
			$set_brackets_arr = array();
			while ($bracket_row = tep_db_fetch_array($bracket_result_sql)) {
				$set_brackets_arr[] = $bracket_row;
			}
			$num_brackets_in_set = count($set_brackets_arr);
			
		    //-----Start Brackets ---------------------------------------------------
			$bracketsHTML .= '	<tr>
									<td class="dataTableContent" colspan="2">
										<table cellpadding="0" cellspacing="0" height="100%" border="0">
										<!-- Open outer 1 -->';
			$color = "#FFFFCC";
		    $num_columns_per_row = 7;
			
		    $bracket_mode_pulldown_arr = array();
		    foreach ($bracket_mode_arr as $mode_id => $mode_val) {
		    	$bracket_mode_pulldown_arr[] = array('id' => $mode_id, 'text' => $mode_val);
		    }
		    $bracket_sign_pulldown_arr = array();
		    foreach ($bracket_sign_arr as $sign_id => $sign_val) {
		    	$bracket_sign_pulldown_arr[] = array('id' => $sign_id, 'text' => $sign_val);
		    }
           	
           	$bracketsHTML .= '				<td>
			                          			<table cellpadding="3" cellspacing="0" width="100%" height="100%" border="0">
			                              			<tr>
			                                  			<td width="12%" align="left" class="ordersBoxHeading">'.tep_draw_separator('pixel_trans.gif', '1', '3').'</td>
			                              			</tr>
			                              			<tr>
			                                  			<td width="12%" align="left" class="ordersBoxHeading">'.TEXT_PCT_MAX_INVENTORY.'</td>
			                              			</tr>
			                              			<tr>
			                                  			<td width="12%" align="left" class="ordersBoxHeading">'.TEXT_PCT_MARKET_PRICE.'</td>
			                              			</tr>
			                              			<tr>
			                                  			<td width="12%" align="left" class="ordersBoxHeading">&nbsp;</td>
			                              			</tr>
			                          			</table>
			                      			</td>';
			
		    //Don't enter if num brackets = 0.
			for ($i=0; $i < $num_brackets_in_set; $i++) {
				$row = $set_brackets_arr[$i];
				
				$row_sign_mode = tep_draw_pull_down_menu("selSign[{$row['buyback_supplier_price_bracket_id']}]", $bracket_sign_pulldown_arr, $row['buyback_supplier_price_bracket_sign'], 'id="selSign_'.$i.'" ');
				$row_bracket_mode = tep_draw_pull_down_menu("selMode[{$row['buyback_supplier_price_bracket_id']}]", $bracket_mode_pulldown_arr, $row['buyback_supplier_price_bracket_mode'], 'id="selMode_'.$i.'" ');
				
		        //Bracket field template
				$row_percent_max_inv = tep_draw_input_field("txtPct[{$row['buyback_supplier_price_bracket_id']}]", $row['buyback_supplier_price_bracket_quantity'], 'size="8" id="txtQty_'.$i.'" ').'&#37;';
				$row_percent_mkt_price = tep_draw_input_field("txtMultiplier[".$row['buyback_supplier_price_bracket_id']."]", number_format($row['buyback_supplier_price_bracket_value'], DISPLAY_PRICE_DECIMAL), 'size="8" id="txtPrice_'.$i.'" ');
				$row_delete_link = '<a onclick="return confirm(\'Confirm delete this bracket ?\')" href="'.tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action', 'bracket_id'))."action=delete_brackets&bracket_id=".$row['buyback_supplier_price_bracket_id']).'">'.TEXT_DELETE.'</a>';
				
				if (($i % $num_columns_per_row)==0) {
				    //Starting left most column
				    if ($i > 0) {
				    	$bracketsHTML .= '	</tr>
				    						<tr>';
			            $bracketsHTML .= '		<td>
					                          		<table cellpadding="3" cellspacing="0" width="100%" height="100%" border="0">
					                              		<tr>
					                                  		<td width="12%" align="left" class="ordersBoxHeading">'.tep_draw_separator('pixel_trans.gif', '1', '3').'</td>
					                              		</tr>
					                              		<tr>
					                                  		<td width="12%" align="left" class="ordersBoxHeading">'.TEXT_PCT_MAX_INVENTORY.'</td>
					                              		</tr>
					                              		<tr>
					                                  		<td width="12%" align="left" class="ordersBoxHeading">'.TEXT_PCT_MARKET_PRICE.'</td>
					                              		</tr>
					                              		<tr>
					                                  		<td width="12%" align="left" class="ordersBoxHeading">&nbsp;</td>
					                              		</tr>
					                          		</table>
					                      		</td>';
				    }
				    
		            $bracketsHTML .= '			<td>
				                          			<table cellpadding="3" cellspacing="0" width="100%" height="100%" border="0">
				                              			<tr>
			                                      			<td width="12%" nowrap align="left" class="ordersBoxHeading">'.sprintf(TABLE_HEADING_BRACKET,$i+1).'</td>
				                              			</tr>
				                              			<tr>
				                                  			<td width="12%" nowrap align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_percent_max_inv.'</td>
				                              			</tr>
				                              			<tr>
				                                  			<td width="12%" nowrap align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_percent_mkt_price.$row_sign_mode.'</td>
				                              			</tr>
				                              			<tr>
				                                  			<td width="12%" align="center" class="dataTableContent" bgcolor="'.$color.'">'.
				                                  				$row_bracket_mode.'&nbsp;'.$row_delete_link.'
				                                  			</td>
				                              			</tr>
				                          			</table>
				                      			</td>';
				} else {
				    //Second column till row end.
		            $bracketsHTML .= '			<td>
				                          			<table cellpadding="3" cellspacing="0" width="100%" height="100%" border="0">
				                              			<tr>
				                                  			<td width="12%" align="left" class="ordersBoxHeading">'.sprintf(TABLE_HEADING_BRACKET,$i+1).'</td>
				                              			</tr>
				                              			<tr>
				                                  			<td width="12%" nowrap align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_percent_max_inv.'</td>
				                              			</tr>
				                              			<tr>
				                                  			<td width="12%" nowrap align="left" class="dataTableContent" bgcolor="'.$color.'">'.$row_percent_mkt_price.$row_sign_mode.'</td>
				                              			</tr>
				                              			<tr>
				                                  			<td width="12%" align="center" class="dataTableContent" bgcolor="'.$color.'">'.
				                                  				$row_bracket_mode.'&nbsp;'.$row_delete_link.'
				                                  			</td>
				                              			</tr>
				                          			</table>
					                 	  		</td>';
				}
				
				//Put here so new bracket form has color change
				$color = (($i % 2)==0) ? "#D7D5D0" : "#FFFFCC";
			}
			
			if (($i % $num_columns_per_row)==0) {
			    //Starting left most column
			    if ($i>0) {
			    	$bracketsHTML .= '	</tr>
			    						<tr>
			                      			<td>
			                          			<table cellpadding="3" cellspacing="0" width="100%" height="100%" border="0">
			                              			<tr>
			                                  			<td width="12%" align="left" class="ordersBoxHeading">'.tep_draw_separator('pixel_trans.gif', '1', '3').'</td>
			                              			</tr>
			                              			<tr>
			                                  			<td width="12%" align="left" class="ordersBoxHeading">'.TEXT_PCT_MAX_INVENTORY.'</td>
			                              			</tr>
			                              			<tr>
			                                  			<td width="12%" align="left" class="ordersBoxHeading">'.TEXT_PCT_MARKET_PRICE.'</td>
			                              			</tr>
			                              			<tr>
			                                  			<td width="12%" align="left" class="ordersBoxHeading">&nbsp;</td>
			                              			</tr>
			                          			</table>
			                      			</td>';
				}
			}
			
			//New Bracket form
		    $bracketsHTML .= '     			<td>
		                              			<table cellpadding="3" cellspacing="0" width="100%" height="100%" border="0">
		                                  			<tr>
		                                      			<td width="12%" align="left" class="ordersBoxHeading">'.TABLE_HEADING_BRACKET_NEW.'</td>
		                                  			</tr>
		                                  			<tr>
		                                      			<td width="12%" nowrap align="left" class="dataTableContent" bgcolor="'.$color.'">'.tep_draw_input_field("txtPct[0]", '', "size='8' id='txtQty_new'").'&#37;</td>
		                                  			</tr>
		                                  			<tr>
		                                      			<td width="12%" nowrap align="left" class="dataTableContent" bgcolor="'.$color.'">'.tep_draw_input_field("txtMultiplier[0]", '', 'size="8" id="txtPrice_new" ') .tep_draw_pull_down_menu("selSign[0]", $bracket_sign_pulldown_arr, '0', 'id="selSign_0" ').'</td>
		                                  			</tr>
		                                  			<tr>
		                                      			<td width="12%" align="center" class="dataTableContent" bgcolor="'.$color.'">'.tep_draw_pull_down_menu("selMode[0]", $bracket_mode_pulldown_arr, '0', 'id="selMode_0" ').'</td>
		                                  			</tr>
		                              			</table>
		                          			</td>';
			$bracketsHTML .= '   		</tr>
									</table><!-- Close Outer 1-->
			            		</td>
			            	</tr>';
			//-----End Brackets ---------------------------------------------------
		} else {
			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action'))));
		}
		
		break;
	case 'add_set':
		$set_name = tep_db_prepare_input($_POST['set_name']);
		$set_cat_id = tep_db_prepare_input($_POST['set_cat_id']);
		
		if ($set_name) {
			$check_name_exists_select_sql = "	SELECT buyback_supplier_price_bracket_set_name 
												FROM " . TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET_SET . " 
												WHERE buyback_supplier_price_bracket_set_name = '" . tep_db_input($set_name) . "'";
			$check_name_exists_result_sql = tep_db_query($check_name_exists_select_sql);
			if (tep_db_num_rows($check_name_exists_result_sql) == 0) {
				$success = tep_db_perform(TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET_SET, array('buyback_supplier_price_bracket_set_name' => $set_name, 'buyback_supplier_price_bracket_set_cat_id' => $set_cat_id));
				if ($success) {
					$messageStack->add_session(MESSAGE_INSERT_SET_SUCCESS, 'success');
				} else {
					$messageStack->add_session(MESSAGE_INSERT_SET_FAILED);
				}
			} else {
				$messageStack->add_session(MESSAGE_SET_NAME_EXISTS);
			}
		}
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action'))));
		
		break;
	case 'delete_set':
		$set_id = tep_db_prepare_input(trim($_GET['set_id']));
		
		if ($set_id) {
			//check if assigned to somewhere
			$check_set_assigned_select_sql = "	SELECT buyback_setting_reference_id 
												FROM " . TABLE_BUYBACK_SETTING . "
												WHERE buyback_setting_table_name = '" . TABLE_BUYBACK_PRODUCTS . "' 
													AND buyback_setting_key = 'ofp_supplier_price_bracket_set'
													AND buyback_setting_value = '" . tep_db_input($set_id) . "'";
			$check_set_assigned_result_sql = tep_db_query($check_set_assigned_select_sql);
			
			if (tep_db_num_rows($check_set_assigned_result_sql) == 0) {
				$set_delete_sql = "	DELETE FROM " . TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET_SET . " 
									WHERE buyback_supplier_price_bracket_set_id = '" . tep_db_input($set_id) . "'";
				$success = tep_db_query($set_delete_sql);
				
				$set_delete_sql = "	DELETE FROM " . TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET . " 
									WHERE buyback_supplier_price_bracket_set_id = '" . tep_db_input($set_id) . "'";
				$success = tep_db_query($set_delete_sql);
			} else {
				$messageStack->add_session(MESSAGE_SET_STILL_ASSIGNED);
			}
		}
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action'))));
		
		break;
	case 'show_bracket_sets':
		$bracket_sets_select_sql = "select bbs.*, cd.categories_name from " . TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET_SET . " AS bbs left join " . TABLE_CATEGORIES_DESCRIPTION . " AS cd ON (bbs.buyback_supplier_price_bracket_set_cat_id=cd.categories_id AND cd.language_id='".$languages_id."') order by buyback_supplier_price_bracket_set_name";
		$page_split_object = new splitPageResults($_REQUEST["page"], MAX_DISPLAY_SEARCH_RESULTS, $bracket_sets_select_sql, $bracket_sets_sql_numrows);
		$bracket_sets_result_sql = tep_db_query($bracket_sets_select_sql);
		
		break;
}

$form_values_arr = $_SESSION[$form_sessname];

$all_categories_array = tep_get_category_tree(0, '___', '', $all_categories_array);

//------Globals end.
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET;?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script type="text/javascript" src="includes/general.js"></script>
	<script type="text/javascript" src="includes/javascript/select_box.js"></script>
	<script type="text/javascript">
	<!--
	function enableFieldSet(setName) {
	    var fld_id_enable_arr = new Array();
	    var fld_id_disable_arr = new Array();
        switch (setName) {
            case 'rdoMaxQtySource1':
                fld_id_enable_arr.push('max_qty');
                break;
            case 'rdoMaxQtySource2':
                fld_id_disable_arr.push('max_qty');
                break;
        }
        setFieldState(fld_id_enable_arr, false);
        setFieldState(fld_id_disable_arr, true);
	}

	function setFieldState(fld_id_arr, isDisable) {
        for (i=0;i<fld_id_arr.length;i++) {
            document.getElementById(fld_id_arr[i]).disabled = isDisable;
        }
	}
	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<?php
	require(DIR_WS_INCLUDES . 'header.php');
?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
   						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
        			<tr>
	        			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
<?php
switch($action) {
	case 'show_edit_set':
	    $js_str = '';
	    switch($max_qty_is_overwrite) {
	        case 1:
	            $js_str .= "enableFieldSet('rdoMaxQtySource1');";
	            break;
	        case 0:
	            $js_str .= "enableFieldSet('rdoMaxQtySource2');";
	            break;
	    }
		
		echo '	<tr>
    				<td>'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>
  				</tr>
				<tr>
    				<td class="ordersBoxHeading">'.TABLE_HEADING_EDIT_SET.'</td>
  				</tr>
				<tr>
        			<td valign="top">'.
        				tep_draw_form("edit_set", FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action')) . 'action=edit_set').'
        				<table border="0" width="100%" align="center" cellspacing="0" cellpadding="0">
		   					<tr>
							    <td class="main"><b>'.TABLE_HEADING_SET_NAME.'</b></td>
							    <td class="main">'.tep_draw_input_field('set_name', $set_name, 'id="set_name" size="60"').'</td>
							</tr>
							<tr>
		        				<td colspan="2">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>
		      				</tr>
		      				<tr>
							    <td class="main"><b>'.TABLE_HEADING_SET_CAT_ID.'</b></td>
							    <td class="main">'.tep_draw_pull_down_menu("set_cat_id", $all_categories_array, $set_cat_id, '').'</td>
							</tr>
							<tr>
		        				<td colspan="2">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>
		      				</tr>
		   					<tr>
							    <td colspan="2">
							    	<table border="0" cellspacing="0" cellpadding="0">
							    		<tr>
							    			<td class="main">
							    				<b>'.TABLE_HEADING_MIN_QTY.'</b>&nbsp;'.tep_draw_input_field('min_qty', $min_qty, 'id="min_qty"').'
							    			</td>
							    			<td width="100px"></td>
							    			<td class="main">
							    				<b>'.ENTRY_OPB_MIN_PURCHASE_DEMAND.'</b>&nbsp;'.tep_draw_input_field('min_purchase_demand', $min_purchase_qty, '').'
							    			</td>
							    		</tr>
							    	</table>
							   	</td>
							</tr>
							<tr>
		        				<td colspan="2">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>
		      				</tr>
		   					<tr>
							    <td class="main" align="left" colspan="2">
							    	<b>'.TABLE_HEADING_MAX_QTY.'</b><br><br>' .
							    	tep_draw_radio_field('rdoMaxQtySource', '1', ($max_qty_is_overwrite ? true : false), '', ' id="rdoMaxQtySource1" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TEXT_OVERWRITE. '&nbsp;' .tep_draw_input_field('max_qty', $max_qty, 'id="max_qty"').'
							    </td>
							</tr>
		   					<tr>
							    <td class="main" align="left" colspan="2">'.
							    	tep_draw_radio_field('rdoMaxQtySource', '0', ($max_qty_is_overwrite ? false : true), '', ' id="rdoMaxQtySource2" onClick="enableFieldSet(this.id)" ').'&nbsp;'.TEXT_SYSTEM_DEFINED. '<br>&nbsp;&nbsp;&nbsp;
							    </td>
							</tr>
							<tr>
		        				<td colspan="2">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>
		      				</tr>'.$bracketsHTML.'
							<tr>
		        				<td colspan="2">'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>
		      				</tr>
						</table>'.
						tep_submit_button(IMAGE_BUTTON_UPDATE_BRACKET_SET, IMAGE_BUTTON_UPDATE_BRACKET_SET, '', 'inputButton').'
						</form>
					    <script type="text/javascript">'.$js_str.'</script>
					</td>
				</tr>';
		
		break;
	case 'show_add_bracket_set':
		echo "		<tr>
						<td>".tep_draw_separator('pixel_trans.gif', '1', '10')."</td>
					</tr>
					<tr>
						<td class='ordersBoxHeading'>".TABLE_HEADING_ADD_SET."</td>
					</tr>
					<tr>
		    			<td valign='top'>
		    				".tep_draw_form("add_set", FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action')) . 'action=add_set')."
		    				<table border='0' cellspacing='0' cellpadding='2'>
			   					<tr>
								    <td width='40%' class='main'><b>".TABLE_HEADING_SET_NAME."</b></td>
								    <td class='main'>".tep_draw_input_field('set_name', '', 'id="set_name"')."</td>
								</tr>
								<tr>
			        				<td colspan='2'>".tep_draw_separator('pixel_trans.gif', '1', '1')."</td>
			      				</tr>
			      				<tr>
								    <td class='main'><b>".TABLE_HEADING_SET_CAT_ID."</b></td>
								    <td class='main'>".tep_draw_pull_down_menu("set_cat_id", $all_categories_array, '', '')."</td>
								</tr>
								<tr>
			        				<td colspan='2'>".tep_draw_separator('pixel_trans.gif', '1', '10')."</td>
			      				</tr>
			      				<tr>
			        				<td>&nbsp;</td>
			        				<td>".tep_submit_button(IMAGE_BUTTON_ADD_SET, IMAGE_BUTTON_ADD_SET, '', 'inputButton')."</td>
			      				</tr>
							</table>
							</form>
						</td>
					</tr>";
		
		break;
	case 'show_bracket_sets':
		if (tep_db_num_rows($bracket_sets_result_sql)) {
			echo '	<tr>
        				<td>'.tep_draw_separator('pixel_trans.gif', '1', '10').'</td>
      				</tr>
					<tr>
            			<td valign="top">
            				<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
								    <td width="5%" class="reportBoxHeading">'.TABLE_HEADING_SET_ID.'</td>
								    <td width="20%" class="reportBoxHeading">'.TABLE_HEADING_SET_NAME.'</td>
								    <td width="20%" class="reportBoxHeading">'.TABLE_HEADING_OFFER_PRICE_CAT_NAME.'</td>
								    <td width="15%" class="reportBoxHeading">'.TABLE_HEADING_MIN_QTY.'</td>
								    <td width="15%" class="reportBoxHeading">'.TABLE_HEADING_MAX_QTY.'</td>
								    <td width="15%" class="reportBoxHeading">'.TABLE_HEADING_OPB_MIN_PURCHASE_DEMAND.'</td>
								    <td width="5%" align="center" class="reportBoxHeading">'.TABLE_HEADING_ACTION.'</td>
								</tr>';
			
			$row_count = 0;
			while ($bracket_sets_row = tep_db_fetch_array($bracket_sets_result_sql)) {
		    	$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
				
				echo '			<tr class="'.$row_style.'" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \''.$row_style.'\')" onclick="rowClicked(this, \''.$row_style.'\')">
									<td class="reportRecords" valign="top">'.$bracket_sets_row["buyback_supplier_price_bracket_set_id"].'</td>
									<td class="reportRecords" valign="top">'.$bracket_sets_row["buyback_supplier_price_bracket_set_name"].'</td>
									<td class="reportRecords" valign="top">'.($bracket_sets_row["buyback_supplier_price_bracket_set_cat_id"] > 0 ? $bracket_sets_row["categories_name"] : TEXT_TOP).'</td>
									<td class="reportRecords" valign="top">'.$bracket_sets_row["buyback_supplier_price_bracket_set_minimum_value"].'</td>
									<td class="reportRecords" valign="top">'.$bracket_sets_row["buyback_supplier_price_bracket_set_maximum_value"].'&nbsp;(' . ($bracket_sets_row['buyback_supplier_price_bracket_set_maximum_is_overwrite'] ? TEXT_OVERWRITE : TEXT_SYSTEM_DEFINED) . ')</td>
									<td class="reportRecords" valign="top">'.$bracket_sets_row["buyback_supplier_price_bracket_set_min_purchase"].'</td>
									<td align="left" class="reportRecords" valign="top" nowrap>&nbsp;
										<a href="'.tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action')) . 'action=show_edit_set&set_id='.$bracket_sets_row["buyback_supplier_price_bracket_set_id"]).'">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit set", "", "", 'align="top"').'</a>
										&nbsp;
										<a href="'.tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action')) . 'action=delete_set&set_id='.$bracket_sets_row["buyback_supplier_price_bracket_set_id"]).'" onclick="return confirm(\'Delete Bracket Set '.htmlspecialchars(addslashes($bracket_sets_row["buyback_supplier_price_bracket_set_name"]), ENT_QUOTES).'?\')">'.tep_image(DIR_WS_ICONS."delete.gif", "Delete Bracket Set", "", "", 'align="top"').'</a>
									</td>
								</tr>';
				$row_count++;
			}
			
			echo '			</table>
						</td>
			   		</tr>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td class="smallText" valign="top" NOWRAP>'.$page_split_object->display_count($bracket_sets_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_SETS).'</td>
									<td class="smallText" align="right">'.$page_split_object->display_links($bracket_sets_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_REQUEST['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont'))."cont=1").'</td>
								</tr>
							</table>
						</td>
					</tr>';
		}
		
		echo '		<tr>
						<td>
							[ <a href="'.tep_href_link(FILENAME_SUPPLIERS_AVERAGE_OFFER_PRICE_BRACKETS, tep_get_all_get_params(array('action')) . 'action=show_add_bracket_set').'" class="actionLink">'.LINK_ADD_SET.'</a> ]
						</td>
					</tr>';
		
		break; //end show_bracket_sets
}
?>
				</table>
			</td>
		</tr>
	</table>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>