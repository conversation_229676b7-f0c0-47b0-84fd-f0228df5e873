<?php
/*
  	$Id: suppliers_groups.php,v 1.11 2006/07/22 04:13:23 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$subaction = (isset($_GET['subaction']) ? $_GET['subaction'] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "set_group_status":
			if ( ($_REQUEST['flag'] == '0') || ($_REQUEST['flag'] == '1') ) {
          		if (isset($_REQUEST['gID'])) {
      				$group_status_update_sql = "UPDATE " . TABLE_SUPPLIER_GROUPS . " 
      											SET supplier_groups_status = '".(int)$_REQUEST['flag']."' 
      											WHERE supplier_groups_id='" . (int)$_REQUEST["gID"] . "'";
					tep_db_query($group_status_update_sql);
					
					if ($_REQUEST['flag'] == '0') {
						$supplier_status_update_sql = "	UPDATE " . TABLE_SUPPLIER . " 
		  												SET supplier_status = '0' 
		  												WHERE supplier_groups_id='" . (int)$_REQUEST["gID"] . "'";
						tep_db_query($supplier_status_update_sql);
					}
          		}
        	}
        	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_GROUPS, 'page=' . $HTTP_GET_VARS['page']));
        	
			break;
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
      	case "insert_supplier_group":
		case "update_supplier_group":
			$error = false;
			
			$supplier_groups_name = ucwords(strtolower(tep_db_prepare_input($_POST['supplier_groups_name'])));
        	$supplier_groups_status = isset($_POST['supplier_groups_status']) ? (int)$_POST['supplier_groups_status'] : 0;
        	$show_purchase_status = isset($_POST['show_purchase_status']) ? (int)$_POST['show_purchase_status'] : 0;
        	$name_replace = ereg_replace_dep(" ", "", strtolower($supplier_groups_name));
        	
            if ($subaction == "insert_supplier_group") {
	        	if (($supplier_groups_name == '') || (strlen($supplier_groups_name) <= 1) ) {
	          		$messageStack->add(TEXT_INFO_GROUPS_NAME_FALSE);
					$error = true;
	        	} else {
	        		$check_groups_name_query = tep_db_query("select supplier_groups_name as group_name_new from " . TABLE_SUPPLIER_GROUPS . " where LOWER(REPLACE(supplier_groups_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
	          		$check_duplicate = tep_db_num_rows($check_groups_name_query);
	          		if ($check_duplicate > 0) {
	          			$messageStack->add(TEXT_INFO_GROUPS_NAME_USED);
						$error = true;
	          		} else {
	            		$sql_data_array = array('supplier_groups_name' => $supplier_groups_name,
	            								'supplier_groups_status' => $supplier_groups_status,
	            								'show_products_purchase_demand_status' => $show_purchase_status
	            								);
	            		tep_db_perform(TABLE_SUPPLIER_GROUPS, $sql_data_array);
						$new_supplier_grp_id = tep_db_insert_id();
						
						$messageStack->add_session(sprintf(SUCCESS_GROUP_INSERTED, $_POST["supplier_groups_name"]), 'success');
	          		}
	        	}
			} else {
				if (($supplier_groups_name == '') || (strlen($supplier_groups_name) <= 1) ) {
	          		$messageStack->add(TEXT_INFO_GROUPS_NAME_FALSE);
					$error = true;
	        	} else {
	          		$check_groups_name_query = tep_db_query("select supplier_groups_name as group_name_edit from " . TABLE_SUPPLIER_GROUPS . " where supplier_groups_id <> " . (int)$_POST['supGrpID'] . " and LOWER(REPLACE(supplier_groups_name, ' ', '')) = '" . tep_db_input($name_replace) . "'");
	          		$check_duplicate = tep_db_num_rows($check_groups_name_query);
	          		if ($check_duplicate > 0) {
	          			$messageStack->add(TEXT_INFO_GROUPS_NAME_USED);
						$error = true;
	          		} else {
	            		$sql_data_array = array('supplier_groups_name' => $supplier_groups_name,
	            								'supplier_groups_status' => $supplier_groups_status,
	            								'show_products_purchase_demand_status' => $show_purchase_status
	            								);
	            		tep_db_perform(TABLE_SUPPLIER_GROUPS, $sql_data_array, 'update', " supplier_groups_id='" . (int)$_POST['supGrpID'] . "'");
						
						if ($supplier_groups_status == '0') {
							$supplier_status_update_sql = "	UPDATE " . TABLE_SUPPLIER . " 
			  												SET supplier_status = '0' 
			  												WHERE supplier_groups_id='" . (int)$_POST['supGrpID'] . "'";
							tep_db_query($supplier_status_update_sql);
						}
						
						if (is_array($_POST['radioStatus']) && count($_POST['radioStatus'])) {
							foreach ($_POST['radioStatus'] as $purchase_list_id => $purchase_mode) {
								$list_info_select_sql = "SELECT products_purchases_lists_name FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id = '" . tep_db_input($purchase_list_id) . "'";
								$list_info_result_sql = tep_db_query($list_info_select_sql);
								$list_info_row = tep_db_fetch_array($list_info_result_sql);
								$list_name = $list_info_row["products_purchases_lists_name"];
								
								$first_list_start_hr = $_POST['first_list_start_hr'][$purchase_list_id];
								$first_list_start_min = $_POST['first_list_start_min'][$purchase_list_id];
								$first_list_end_hr = $_POST['first_list_end_hr'][$purchase_list_id];
								$first_list_end_min = $_POST['first_list_end_min'][$purchase_list_id];
								
								$first_list_edit_hr = $_POST['first_list_edit_hr'][$purchase_list_id];
								$first_list_edit_min = $_POST['first_list_edit_min'][$purchase_list_id];
								
								$second_list_start_hr = $_POST['second_list_start_hr'][$purchase_list_id];
								$second_list_start_min = $_POST['second_list_start_min'][$purchase_list_id];
								$second_list_end_hr = $_POST['second_list_end_hr'][$purchase_list_id];
								$second_list_end_min = $_POST['second_list_end_min'][$purchase_list_id];
					    		
					    		$auto_on = (int)$_POST['auto_on'][$purchase_list_id];
								$auto_off = (int)$_POST['auto_off'][$purchase_list_id];
								
								if ((int)$first_list_start_hr < 0 || (int)$first_list_start_hr > 23)	$first_list_start_hr = "00";
								if ((int)$first_list_start_min < 0 || (int)$first_list_start_min > 59)	$first_list_start_min = "00";
								
								if ((int)$first_list_end_hr < 0 || (int)$first_list_end_hr > 23)	$first_list_end_hr = "00";
								if ((int)$first_list_end_min < 0 || (int)$first_list_end_min > 59)	$first_list_end_min = "00";
								
								if ((int)$second_list_start_hr < 0 || (int)$second_list_start_hr > 23)	$second_list_start_hr = "00";
								if ((int)$second_list_start_min < 0 || (int)$second_list_start_min > 59)	$second_list_start_min = "00";
								
								if ((int)$second_list_end_hr < 0 || (int)$second_list_end_hr > 23)	$second_list_end_hr = "00";
								if ((int)$second_list_end_min < 0 || (int)$second_list_end_min > 59)	$second_list_end_min = "00";
								
								$first_list_start_time = $first_list_start_hr.':'.$first_list_start_min.':00';
								$first_list_end_time = $first_list_end_hr.':'.$first_list_end_min.':00';
								
								$second_list_start_time = $second_list_start_hr.':'.$second_list_start_min.':00';
								$second_list_end_time = $second_list_end_hr.':'.$second_list_end_min.':00';
								
								$first_list_edit_time = 'NULL';
								if (!isset($_POST['first_list_edit_anytime'][$purchase_list_id])) {
									if (tep_not_null($first_list_edit_hr) && tep_not_null($first_list_edit_min)) {
										if ((int)$first_list_edit_hr < 0 || (int)$first_list_edit_hr > 23)	$first_list_edit_hr = '00';
										if ((int)$first_list_edit_min < 0 || (int)$first_list_edit_min > 59)	$first_list_edit_min = '00';
										
										$first_list_edit_time = $first_list_edit_hr.':'.$first_list_edit_min.':00';
										
										if (!tep_time_check($first_list_start_time, $first_list_end_time, $first_list_edit_hr.$first_list_edit_min)) {
											$messageStack->add_session(sprintf(WARNING_INVALID_FIRST_LIST_EDIT_TIME, $list_name), 'warning');
											$first_list_edit_time = 'NULL';
										}
									} else {
										$messageStack->add_session(sprintf(WARNING_INVALID_FIRST_LIST_EDIT_TIME, $list_name), 'warning');
										$first_list_edit_time = 'NULL';
									}
								}
								
								$time_sql_data_array = array(	'normal_status' => $purchase_mode,
				            									'first_list_start_time' => $first_list_start_time,
				            									'first_list_end_time' => $first_list_end_time,
				            									'first_list_edit_time' => $first_list_edit_time,
				            									'second_list_start_time' => $second_list_start_time,
				            									'second_list_end_time' => $second_list_end_time,
				            									'auto_on' => $auto_on,
				            									'auto_off' => $auto_off
				            								);
				            	
				            	if ($purchase_mode != "STATUS_AUTO") {
				            		$time_sql_data_array["current_status"] = $purchase_mode;
								}
								
			            		tep_db_perform(TABLE_SUPPLIER_LIST_TIME_SETTING, $time_sql_data_array, 'update', " supplier_groups_id='" . (int)$_POST['supGrpID'] . "' AND products_purchases_lists_id = '" . (int)$purchase_list_id . "'");
			            	}
	            		}
						$messageStack->add_session(sprintf(SUCCESS_GROUP_UPDATED, $_POST["supplier_groups_name"]), 'success');
	          		}
	        	}
			}
			
			if (!$error) {
				tep_redirect(tep_href_link(FILENAME_SUPPLIERS_GROUPS, 'page=' . $_GET['page']));
			}
			
			break;
      	case "confirm_delete_supplier_group":
      		$supplier_group_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_GROUPS . " WHERE supplier_groups_id = '" . (int)$_GET['supGrpID'] . "'";
      		tep_db_query($supplier_group_delete_sql);
      		
			$supplier_list_time_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_LIST_TIME_SETTING . " WHERE supplier_groups_id = '" . (int)$_GET['supGrpID'] . "'";
      		tep_db_query($supplier_list_time_delete_sql);
	        
	        $supplier_pricing_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PRICING . " WHERE supplier_groups_id = '" . (int)$_GET['supGrpID'] . "'";
			tep_db_query($supplier_pricing_delete_sql);
			
			$supplier_pricing_setting_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PRICING_SETTING . " WHERE supplier_groups_id = '" . (int)$_GET['supGrpID'] . "'";
			tep_db_query($supplier_pricing_setting_delete_sql);
			
      		$supplier_purchase_mode_delete_sql = "	DELETE " . TABLE_SUPPLIER_PURCHASE_MODES . " FROM " . TABLE_SUPPLIER . ", " . TABLE_SUPPLIER_PURCHASE_MODES . " 
													WHERE " . TABLE_SUPPLIER . ".supplier_id=" . TABLE_SUPPLIER_PURCHASE_MODES . ".supplier_id AND " . TABLE_SUPPLIER . ".supplier_groups_id = '" . (int)$_GET['supGrpID'] . "'";
			tep_db_query($supplier_purchase_mode_delete_sql);
			/*
	        $supplier_delete_sql = "DELETE FROM " . TABLE_SUPPLIER . " WHERE supplier_groups_id = '" . (int)$_GET['supGrpID'] . "'";
      		tep_db_query($supplier_delete_sql);
      		*/
      		$messageStack->add_session(SUCCESS_GROUP_DELETED, 'success');
      		
	        tep_redirect(tep_href_link(FILENAME_SUPPLIERS_GROUPS, 'page=' . $_GET['page']));
	        
        	break;
        case "batch_reset":
        	if ($_GET['mode'] == 'STATUS_GROUP') {
        		$list_info_select_sql = "SELECT products_purchases_lists_name FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id = '" . tep_db_input($_GET['lID']) . "'";
				$list_info_result_sql = tep_db_query($list_info_select_sql);
				$list_info_row = tep_db_fetch_array($list_info_result_sql);
				
				// Update supplier purchase mode and send new list notification e-mail
        		$supplier_select_sql = "SELECT s.supplier_id, s.supplier_gender, s.supplier_firstname, s.supplier_lastname, s.supplier_email_address 
        								FROM " . TABLE_SUPPLIER . " AS s 
        								INNER JOIN " . TABLE_SUPPLIER_GROUPS . " AS sg 
		        							ON (s.supplier_groups_id=sg.supplier_groups_id AND s.supplier_status=1) 
        								INNER JOIN " . TABLE_SUPPLIER_PURCHASE_MODES . " AS spm 
		        							ON (s.supplier_id=spm.supplier_id AND spm.products_purchases_lists_id='" . tep_db_input($_GET['lID']) . "') 
		        						WHERE sg.supplier_groups_id = '" . tep_db_input($_GET['supGrpID']) . "'";
		        $supplier_result_sql = tep_db_query($supplier_select_sql);
		        
				while ($supplier_row = tep_db_fetch_array($supplier_result_sql)) {
					$supplier_pmode_update_sql = "	UPDATE " . TABLE_SUPPLIER_PURCHASE_MODES . " 
													SET supplier_purchase_mode='STATUS_GROUP' 
													WHERE supplier_id='".tep_db_input($supplier_row["supplier_id"])."' AND products_purchases_lists_id='".tep_db_input($_GET['lID'])."'";
					tep_db_query($supplier_pmode_update_sql);
					
					$email =	sprintf(EMAIL_TEXT_CONTENT, $list_info_row["products_purchases_lists_name"], tep_supplier_href_link('login.php', '', 'SSL'), tep_supplier_href_link('login.php', '', 'SSL')) . "\n\n" . 
								EMAIL_TEXT_CLOSING .
								EMAIL_FOOTER;
					
					$email_firstname = $supplier_row["supplier_firstname"];
					$email_lastname = $supplier_row["supplier_lastname"];
					
					$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $supplier_row['supplier_gender']);
					
					$email = $email_greeting . $email;
					tep_mail($email_firstname.' '.$email_lastname, $supplier_row["supplier_email_address"], EMAIL_TEXT_SUBJECT_NEW_LIST, $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
				}
        	} else if ($_GET['mode'] == 'HIDE_RSTK_CHAR') {
        		// Reset all the RSTK CHAR to not show to supplier in Supplier Pricing page
				$rstk_char_update_select_sql = "UPDATE " . TABLE_SUPPLIER_PRICING . " 
												SET supplier_pricing_show_comment=0 
												WHERE supplier_groups_id = '" . tep_db_input($_GET['supGrpID']) . "' 
													AND products_purchases_lists_id = '" . tep_db_input($_GET['lID']) . "'";
				tep_db_query($rstk_char_update_select_sql);
        	}
        	
        	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_GROUPS, 'action=edit_supplier_group&supGrpID='.$_GET['supGrpID'].'&page=' . $_GET['page']));
        	
        	break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
            			<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
if ($action == "new_supplier_group" || $action == "edit_supplier_group") {
	if (tep_not_null($_REQUEST["supGrpID"])) {
		$supplier_group_select_sql = "SELECT supplier_groups_name, supplier_groups_status, show_products_purchase_demand_status FROM " . TABLE_SUPPLIER_GROUPS . " WHERE supplier_groups_id='" . $_REQUEST["supGrpID"] . "'";
		$supplier_group_result_sql = tep_db_query($supplier_group_select_sql);
		$supplier_group_row = tep_db_fetch_array($supplier_group_result_sql);
	}
?>
					<tr>
        				<td width="100%">
<?
	echo tep_draw_form('supplier_group_form', FILENAME_SUPPLIERS_GROUPS, tep_get_all_get_params(array('subaction')) . 'subaction='.($action=="new_supplier_group" ? 'insert_supplier_group' : 'update_supplier_group'), 'post', 'onSubmit="return supplier_group_form_checking();"');
	echo tep_draw_hidden_field("supGrpID", $_REQUEST["supGrpID"]);
?>
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="main" width="15%" valign="top"><?=ENTRY_GROUP_NAME?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('supplier_groups_name', $supplier_group_row["supplier_groups_name"], 'size="40" id="supplier_groups_name"')?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
        							<td class="main"><?=ENTRY_GROUP_STATUS?></td>
        							<td class="main">
        								<?=tep_draw_radio_field('supplier_groups_status', '1', $supplier_group_row["supplier_groups_status"]=='1' ? true : false) . TEXT_ACTIVE . tep_draw_radio_field('supplier_groups_status', '0', $supplier_group_row["supplier_groups_status"]!='1' ? true : false) . TEXT_INACTIVE ?>
        							</td>
      							</tr>
      							<tr>
        							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      							</tr>
      							<tr>
        							<td class="main"><?=ENTRY_PRODUCT_PURCHASE_STATUS?></td>
        							<td class="main" valign="top">
        								<?=tep_draw_radio_field('show_purchase_status', '1', $supplier_group_row["show_products_purchase_demand_status"]=='1' ? true : false) . TEXT_YES . tep_draw_radio_field('show_purchase_status', '0', $supplier_group_row["show_products_purchase_demand_status"]!='1' ? true : false) . TEXT_NO ?>
        							</td>
      							</tr>
      							<tr>
        							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      							</tr>
      							<!--tr>
									<td class="main" colspan="2"><?=TEXT_SUPPLIER_LIST_STATUS?></tr>
								<tr-->
								<tr>
        							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
      							</tr>
<? 	if (tep_not_null($_REQUEST["supGrpID"])) { ?>
      							<tr>
									<td class="main"><?=TEXT_CURRENT_TIME?></td>
									<td class="main"><?=date('Y-m-d H:i')?></td>
								</tr>
								<!--tr>
									<td class="main"><?=TEXT_CURRENT_STATUS?></td>
									<td class="main"><?=$current_status_formatted?></td>
								</tr-->
								<tr>
        							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
      							</tr>
								<tr>
									<td class="main" valign="top"><?='&nbsp;'?></td>
									<td class="main">
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td class="reportBoxHeading"><?=TABLE_HEADING_PURCHASE_LIST?></td>
												<td class="reportBoxHeading"><?=TABLE_HEADING_PURCHASE_MODE?></td>
												<td class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
											</tr>
<?
		$assigned_purchase_list_select_sql = "	SELECT ppl.products_purchases_lists_id, ppl.products_purchases_lists_name, ppl.products_purchases_lists_cat_id 
												FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " AS ppl 
												INNER JOIN " . TABLE_SUPPLIER_PRICING_SETTING . " AS sps 
													ON ppl.products_purchases_lists_id=sps.products_purchases_lists_id 
												WHERE sps.supplier_groups_id = '" . $_REQUEST["supGrpID"] . "' 
												GROUP BY sps.products_purchases_lists_id 
												ORDER BY ppl.products_purchases_lists_sort_order ";
		$assigned_purchase_list_result_sql = tep_db_query($assigned_purchase_list_select_sql);
		$row_count = 0;
		while ($assigned_purchase_list_row = tep_db_fetch_array($assigned_purchase_list_result_sql)) {
			if (tep_check_cat_tree_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, $assigned_purchase_list_row["products_purchases_lists_cat_id"]) != 1) {
				continue;
			}
			
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
			
			$list_id = $assigned_purchase_list_row["products_purchases_lists_id"];
			$time_array = tep_get_supplier_list_timer($_REQUEST["supGrpID"], $list_id);
			
			$time_array['status'] = strtoupper($time_array['status']);
			
			if ($time_array['status'] == 'STATUS_ON' || $time_array['status'] == 'STATUS_OFF') {
				$autoClass = 'hide';
			} else {	// Auto Mode
				$autoClass = 'show';
				
				$edit_first_list_style = 'disabled';
				if ($time_array['first_list_edit_anytime'] == 0 && isset($time_array['first_list_edit_time'])) {
					$edit_first_list_style = '';
				}
			}
			
			$auto_off_check = (int)$time_array['auto_off'];
			$auto_on_check =  (int)$time_array['auto_on'];
			
			$rstk_char_count_select_sql = "	SELECT COUNT(products_id) AS total_active_rstk_char 
											FROM " . TABLE_SUPPLIER_PRICING . " 
											WHERE supplier_groups_id = '" . tep_db_input($_REQUEST["supGrpID"]) . "' 
												AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'
												AND supplier_pricing_show_comment=1";
			$rstk_char_count_result_sql = tep_db_query($rstk_char_count_select_sql);
			$rstk_char_count_row = tep_db_fetch_array($rstk_char_count_result_sql);
?>
											<tr class="<?=$row_style?>">
												<td width="15%" class="reportRecords" valign="top"><?=$assigned_purchase_list_row["products_purchases_lists_name"]?></td>
												<td class="reportRecords" valign="top">
													<table width="100%" border="0" cellspacing="0" cellpadding="2">
														<tr>
															<td class="main">
																<?=tep_draw_radio_field('radioStatus['.$list_id.']', 'STATUS_ON', ($time_array['status']=='STATUS_ON' ? true : false), '', ' onClick="javascript:document.getElementById(\'autoControls_'.$list_id.'\').className = \'hide\';" ') . TEXT_ON . '&nbsp;' . tep_draw_radio_field('radioStatus['.$list_id.']', 'STATUS_OFF', ($time_array['status']=='STATUS_OFF' ? true : false), '', 'onClick="javascript:document.getElementById(\'autoControls_'.$list_id.'\').className = \'hide\';" ') . TEXT_OFF . '&nbsp;' . tep_draw_radio_field('radioStatus['.$list_id.']', 'STATUS_AUTO', ($time_array['status']=='STATUS_AUTO' ? true : false), '', 'onClick="javascript:document.getElementById(\'autoControls_'.$list_id.'\').className = \'show\';" ') . TEXT_AUTO?>
															</td>
														</tr>
														<tbody id="autoControls_<?=$list_id?>" class="<?=$autoClass?>">
															<tr>
																<td class="main">
																	<table border="0" cellpadding="2" cellspacing="2">
																		<tr>
																			<td class="main"><?=TEXT_FIRST_LIST_TIME?></td>
																			<td class="main">
																				<?=tep_draw_input_field('first_list_start_hr['.$list_id.']', $time_array['first_list_start_time'][0], ' SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'hr\')"') . ':' . tep_draw_input_field('first_list_start_min['.$list_id.']', $time_array['first_list_start_time'][1], ' SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'min\')"') . TEXT_TO . tep_draw_input_field('first_list_end_hr['.$list_id.']', $time_array['first_list_end_time'][0], ' SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'hr\')"') . ':' . tep_draw_input_field('first_list_end_min['.$list_id.']', $time_array['first_list_end_time'][1], ' SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'min\')"')?>
																			</td>
																			<td>
																				<table border="0" cellpadding="0" cellspacing="1">
																					<tr>
																						<td class="main"><?=TEXT_FIRST_LIST_EDITABLE_TIME?></td>
																						<td class="main">
																							<?=tep_draw_input_field('first_list_edit_hr['.$list_id.']', $time_array['first_list_edit_time'][0], ' id="first_list_edit_hr_'.$list_id.'" SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'hr\')" ' . $edit_first_list_style) . ':' . tep_draw_input_field('first_list_edit_min['.$list_id.']', $time_array['first_list_edit_time'][1], ' id="first_list_edit_min_'.$list_id.'" SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'min\')" ' . $edit_first_list_style)?>
																						</td>
																						<td class="main"><?=tep_draw_checkbox_field('first_list_edit_anytime['.$list_id.']', '1', ($time_array['first_list_edit_anytime']==1 ? true : false), '', 'onClick="if (this.checked) {document.getElementById(\'first_list_edit_hr_'.$list_id.'\').disabled=true; document.getElementById(\'first_list_edit_min_'.$list_id.'\').disabled=true;} else {document.getElementById(\'first_list_edit_hr_'.$list_id.'\').disabled=false; document.getElementById(\'first_list_edit_min_'.$list_id.'\').disabled=false; }"')?></td>
																						<td class="main"><?=TEXT_ANYTIME?></td>
																					</tr>
																				</table>
																			</td>
																		</tr>
																		<tr>
																			<td class="main"><?=TEXT_SECOND_LIST_TIME?></td>
																			<td class="main">
																				<?=tep_draw_input_field('second_list_start_hr['.$list_id.']', $time_array['second_list_start_time'][0], ' SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'hr\')"') . ':' . tep_draw_input_field('second_list_start_min['.$list_id.']', $time_array['second_list_start_time'][1], ' SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'min\')"') . TEXT_TO . tep_draw_input_field('second_list_end_hr['.$list_id.']', $time_array['second_list_end_time'][0], ' SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'hr\')"') . ':' . tep_draw_input_field('second_list_end_min['.$list_id.']', $time_array['second_list_end_time'][1], ' SIZE="1" MAXLENGTH="2" onChange="checkTime(this, \'min\')"')?>
																			</td>
																			<td>&nbsp;</td>
																		</tr>
																	</table>
																</td>
															</tr>
															<tr>
																<td class="main"><?=tep_draw_checkbox_field('auto_off['.$list_id.']', '1', $auto_off_check) . ' ' . TEXT_AUTO_OFF?></td>							
															</tr>
															<tr>
																<td class="main"><?=tep_draw_checkbox_field('auto_on['.$list_id.']', '1', $auto_on_check) . ' ' . TEXT_AUTO_ON?></td>							
															</tr>
														</tbody>
													</table>
												</td>
												<td width="30%" class="reportRecords" valign="top">
													<a href="javascript:;" onClick="confirm_action('<?=JS_CONFIRM_RESET_SUP_PMODE?>', '<?=tep_href_link(FILENAME_SUPPLIERS_GROUPS, tep_get_all_get_params(array('subaction')) . 'subaction=batch_reset&mode=STATUS_GROUP&lID='.$list_id)?>')"><?=LINK_RESET_SUPPLIER_PURCHASE_MODE?></a>
												<?
													if ($rstk_char_count_row['total_active_rstk_char'] > 0) {
														echo '<br><br>Active Restock Characters: ' . $rstk_char_count_row['total_active_rstk_char'] . ' (<a href="javascript:;" onClick="confirm_action(\''.JS_CONFIRM_HIDE_RSTK_CHAR.'\', \''.tep_href_link(FILENAME_SUPPLIERS_GROUPS, tep_get_all_get_params(array('subaction')) . 'subaction=batch_reset&mode=HIDE_RSTK_CHAR&lID='.$list_id).'\')">'.LINK_HIDE_RSTK_CHAR.'</a>)';
													}
												?>
												</td>
											</tr>
<?			$row_count++;
		}
?>
										</table>
									</td>
								</tr>
								<tr>
        							<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      							</tr>
<?	} ?>
								<tr>
									<td align="right" colspan="2">
										<?=($action=="new_supplier_group" ? tep_image_submit('button_insert.gif', IMAGE_INSERT) : tep_image_submit('button_update.gif', IMAGE_UPDATE)) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_SUPPLIERS_GROUPS) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
							</form>
        				</td>
        			</tr>
        		<script>
					<!--
					function supplier_group_form_checking() {
						var group_name = DOMCall('supplier_groups_name');
						if (group_name.value == "") {
							alert('Please enter supplier group name!');
							group_name.focus();
							return false;
						}
						
						var group_status = document.supplier_group_form.supplier_groups_status;
						var group_status_selected = false;
						for (i=0; i < group_status.length; i++) {
							if (group_status[i].checked) {
								group_status_selected = true;
								break;
							}
						}
						if (!group_status_selected) {
							alert('Please select supplier group status!');
							return false;
						}
						return true;
					}
					
					function checkTime(obj, identifier) {
						var val = parseInt(obj.value, 10);
						
						if (isNaN(val)) {
							obj.value = "00";
							return;
						}
						
						var max = (identifier == 'hr') ? 23 : 59;
						
						if (val > max || val < 0) {
							val = "00";
						} else {
							if (val < 10) 	val = "0" + val;
						}
						
						obj.value = val;
					}
					//-->
				</script>
<?
} else {
	$supplier_group_select_sql = "select * from " . TABLE_SUPPLIER_GROUPS . " order by supplier_groups_name";
	$page_split_object = new splitPageResults($_REQUEST["page"], MAX_DISPLAY_SEARCH_RESULTS, $supplier_group_select_sql, $sql_numrows);
	$supplier_group_result_sql = tep_db_query($supplier_group_select_sql);
	
	$group_status_array = array('1'=> array('name' => 'icon_status_green', 'alt' => IMAGE_ICON_STATUS_GREEN, 'alt2' => IMAGE_ICON_STATUS_GREEN_LIGHT), 
								'0'=> array('name' => 'icon_status_red', 'alt' => IMAGE_ICON_STATUS_RED, 'alt2' => IMAGE_ICON_STATUS_RED_LIGHT)
								);
	
	if (tep_db_num_rows($supplier_group_result_sql)) {
?>
					<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
					<tr>
            			<td valign="top">
            				<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
								    <td class="reportBoxHeading"><?=TABLE_HEADING_GROUPS_NAME?></td>
								    <td width="10%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTIVE_SUPPLIER?></td>
								    <td width="8%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_GROUPS_STATUS?></td>
								    <!--td width="20%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_PURCHASE_MODE?></td-->
								    <td width="6%" align="center" class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
								</tr>
<?
		$row_count = 0;
		while ($supplier_group_row = tep_db_fetch_array($supplier_group_result_sql)) {
    		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
    		
    		$supplier_grp_id = $supplier_group_row["supplier_groups_id"];
    		
    		$active_supplier_count_sql = "SELECT COUNT(supplier_id) AS total_active_sup FROM " . TABLE_SUPPLIER . " WHERE supplier_groups_id = '" . $supplier_group_row["supplier_groups_id"] . "' AND supplier_status = '1'";
    		$active_supplier_result_sql = tep_db_query($active_supplier_count_sql);
    		$active_supplier_row = tep_db_fetch_array($active_supplier_result_sql);
    		$total_active_supplier = $active_supplier_row["total_active_sup"];
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top"><?=$supplier_group_row["supplier_groups_name"]?></td>
									<td class="reportRecords" align="center" valign="top"><?=$total_active_supplier?></td>
									<td class="reportRecords" align="center" valign="top">
									<?
										foreach ($group_status_array as $status_id => $img_res) {
											if ((int)$supplier_group_row["supplier_groups_status"] == (int)$status_id) {
												echo tep_image(DIR_WS_IMAGES . $img_res['name'] . '.gif', $img_res['alt'], 10, 10) . '&nbsp;&nbsp;';
											} else {
												echo '<a href="' . tep_href_link(FILENAME_SUPPLIERS_GROUPS, 'action=set_group_status&flag='.(int)$status_id.'&gID=' . $supplier_grp_id) . '">' . tep_image(DIR_WS_IMAGES . $img_res['name'] . '_light.gif', $img_res['alt2'], 10, 10) . '</a>&nbsp;&nbsp;';
											}
										}
									?>
									</td>
									<!--td class="reportRecords" align="center" valign="top"><?=ucfirst(strtolower(str_replace('STATUS_', '', $time_array['status'])))?></td-->
									<td align="left" class="reportRecords" valign="top" nowrap>&nbsp;
										<a href="<?=tep_href_link(FILENAME_SUPPLIERS_GROUPS, 'action=edit_supplier_group&supGrpID='.$supplier_grp_id.'&page='.$_REQUEST["page"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit supplier group", "", "", 'align="top"')?></a>
										<a href="javascript:void(confirm_delete('<?=$supplier_group_row["supplier_groups_name"]?>', 'Supplier Group', '<?=tep_href_link(FILENAME_SUPPLIERS_GROUPS, 'action=delete_supplier_group&subaction=confirm_delete_supplier_group&supGrpID='.$supplier_grp_id.'&page='.$_REQUEST["page"])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete supplier group", "", "", 'align="top"')?></a>
									</td>
								</tr>
<?			$row_count++;
		}
?>
							</table>
			   			</td>
			   		</tr>
					<tr>
						<td>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
									<td class="smallText" valign="top" NOWRAP><?=$page_split_object->display_count($sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_SUPPLIER_GROUPS)?></td>
									<td class="smallText" align="right"><?=$page_split_object->display_links($sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, 5, $_REQUEST['page'], tep_get_all_get_params(array('page', 'x', 'y', 'cont'))."cont=1")?></td>
								</tr>
							</table>
						</td>
					</tr>
<?	} ?>
					<tr>
						<td>
<?							echo '[ <a href="'.tep_href_link(FILENAME_SUPPLIERS_GROUPS, 'action=new_supplier_group').'" class="actionLink">'.LINK_ADD_SUPPLIER_GROUP.'</a> ]'; ?>
						</td>
					</tr>
<?
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>