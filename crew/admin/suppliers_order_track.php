<?
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'supplier_payment.php');
require(DIR_WS_CLASSES . 'supplier_order.php');

define('DISPLAY_PRICE_DECIMAL', 4);

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

define('PARTIAL_DELIVERY_STATUS', 1);
define('MAKE_PAYMENT_STATUS', 2);

$manage_search_criteria_permission = tep_admin_files_actions(FILENAME_STATS_ORDERS_TRACKING, 'SAVE_ORDER_LISTS_CRITERIA');
$view_payment_info_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'SUPPLIER_ORDER_PAYMENT_INFO');
$supplier_pricing_page_permission = tep_admin_check_boxes(FILENAME_SUPPLIERS_PRICING, 'sub_boxes');
$perform_unserialize_criteria = false;

$status_options = array();
$order_status_select_sql = "SELECT supplier_list_status_id, supplier_list_status_name FROM " . TABLE_SUPPLIER_LIST_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY supplier_list_status_sort_order";
$order_status_result_sql = tep_db_query($order_status_select_sql);
while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
	$status_options[$order_status_row["supplier_list_status_id"]] = $order_status_row["supplier_list_status_name"];
}

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if (tep_not_null($action)) {
	switch ($action) {
		case "show_report":
			if (!$_REQUEST['cont']) {
				if ($_REQUEST["subaction"] == "goto_search") {
					$_SESSION['sup_order_lists_param']["criteria_id"] = (int)$_REQUEST["criteria_id"];
					$perform_unserialize_criteria = true;
				} else {
					if ($_REQUEST["subaction"] == "sl_status") {
						if ($_REQUEST["order_status"]) {
							$_REQUEST["order_status"] = tep_array_unserialize($_REQUEST["order_status"]);
						}
					}
					
					unset($_SESSION['sup_order_lists_param']["criteria_id"]);
					unset($_SESSION['sup_order_lists_param']["cur_criteria_name"]);
					
					$_SESSION['sup_order_lists_param']["cat_id"] = isset($_REQUEST["cat_id"]) ? $_REQUEST["cat_id"] : 0;
					$_SESSION['sup_order_lists_param']["include_subcategory"] = (int)$_REQUEST["include_subcategory"];
					$_SESSION['sup_order_lists_param']["product_id"] = (int)$_REQUEST["product_id"];
					$_SESSION['sup_order_lists_param']["order_status"] = $_REQUEST["order_status"];
					$_SESSION['sup_order_lists_param']["start_date"] = $_REQUEST["start_date"];
					$_SESSION['sup_order_lists_param']["end_date"] = $_REQUEST["end_date"];
					$_SESSION['sup_order_lists_param']["order_id"] = (int)$_REQUEST["order_id"];
					$_SESSION['sup_order_lists_param']["supplier_group"] = $_REQUEST["supplier_group"];
					$_SESSION['sup_order_lists_param']["supplier"] = $_REQUEST["supplier"];
					$_SESSION['sup_order_lists_param']["sort_by"] = $_REQUEST["sort_by"];
		  			$_SESSION['sup_order_lists_param']["sort_order"] = $_REQUEST["sort_order"];
					$_SESSION['sup_order_lists_param']["show_records"] = $_REQUEST["show_records"];
					
					if (count($status_options)) {
						foreach ($status_options as $id => $title) {
							$_SESSION['sup_order_lists_param']["status_".$id] = $_REQUEST["status_".$id];
						}
					}
					
					if ($_REQUEST["supplier_order_list_subaction"] == "save_search") {
						if (tep_not_null($_REQUEST["search_name"])) {
							$save_string = array();
							foreach ($_SESSION['sup_order_lists_param'] as $key => $search_input) {
								$serialized_key_value_pair = tep_array_serialize($key) . ':~:' . tep_array_serialize($search_input);
								$save_string[] = $serialized_key_value_pair;
							}
							
							$search_name = tep_db_prepare_input($_REQUEST["search_name"]);
							$search_criteria_id = '';
							$search_criteria_check_select_sql = "SELECT search_criteria_id FROM " . TABLE_SEARCH_CRITERIA . " WHERE filename='" . tep_db_input(basename($PHP_SELF)) . "' AND search_criteria_name = '" . tep_db_input($search_name) . "'";
							$search_criteria_check_result_sql = tep_db_query($search_criteria_check_select_sql);
							if ($search_criteria_check_row = tep_db_fetch_array($search_criteria_check_result_sql)) {
								$search_criteria_id = (int)$search_criteria_check_row["search_criteria_id"];
							}
							
							if (count($save_string)) {
								$search_criteria_sql_data = array(	'filename' => tep_db_prepare_input(basename($PHP_SELF)),
								                       				'search_criteria_name' => $search_name,
								                       				'search_criteria_string' => tep_db_prepare_input(implode('#~#', $save_string))
								                       			);
								
								if (tep_not_null($search_criteria_id)) {
									// update existing search
									$update_sql_data = array(	'date_search_criteria_last_modified' => 'now()',
																'last_modified_by' => $login_id
															);
									$sql_data_array = array_merge($search_criteria_sql_data, $update_sql_data);
									tep_db_perform(TABLE_SEARCH_CRITERIA, $sql_data_array, 'update', "search_criteria_id = '" . $search_criteria_id . "'");
									$messageStack->add('OK, your criteria named &lt;'.$search_name.'&gt; is updated.', 'success');
								} else {
									// insert new searchs
									$insert_sql_data = array(	'date_search_criteria_added' => 'now()',
								    	                   		'search_criteria_created_by' => $login_id
															);
									$sql_data_array = array_merge($search_criteria_sql_data, $insert_sql_data);
									tep_db_perform(TABLE_SEARCH_CRITERIA, $sql_data_array);
									$search_criteria_id = tep_db_insert_id();
									$messageStack->add('OK, your new criteria named &lt;'.$search_name.'&gt; is saved.', 'success');
								}
								$_SESSION['sup_order_lists_param']["criteria_id"] = $search_criteria_id;
								$perform_unserialize_criteria = true;
							}
						}
					}
				}
		  	}
			break;
        case "reset_session":
        	unset($_SESSION['sup_order_lists_param']);
        	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING));
        	break;
        case "delete_search":
        	// check for permission
        	$search_criteria_check_select_sql = "SELECT search_criteria_id, search_criteria_name FROM " . TABLE_SEARCH_CRITERIA . " WHERE search_criteria_id = '" . (int)$_REQUEST["criteria_id"] . "'";
			$search_criteria_check_result_sql = tep_db_query($search_criteria_check_select_sql);
			if ($search_criteria_check_row = tep_db_fetch_array($search_criteria_check_result_sql)) {
				tep_db_query("DELETE FROM " . TABLE_SEARCH_CRITERIA . " WHERE search_criteria_id='" . $search_criteria_check_row["search_criteria_id"] . "'");
				$messageStack->add_session('OK, your criteria named &lt;'.$search_criteria_check_row["search_criteria_name"].'&gt; is deleted.', 'success');
			} else {
				$messageStack->add_session('Your criteria does not exists.', 'error');
			}
        	unset($_SESSION['sup_order_lists_param']);
        	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING));
        	break;
        case "batch_action":
        	switch($_POST["batch_action"]) {
        		case "MakePayment":
        			$supplier_payment = new supplier_payment();
        			$supplier_payment->set_remarks(tep_db_prepare_input($_POST['payment_remark']));
        			$supplier_payment->set_show_supplier_remark($_POST['show_supplier_remark']=='1' ? true : false);
        			
        			$payment_result = $supplier_payment->make_payment($_POST['orders_batch'], $_POST['partial_pay']);
        			
        			$messageStack->add_session($payment_result['text'], $payment_result['type']);
        			
        			break;
        		case "HideAllRestockCharacter": // hide all restock character
        		case "ShowAllRestockCharacter": //show all restock character
        			$restock_character_products_id_array = array();
        			
        			if (count($_REQUEST['orders_batch'])) {
        				if ($_POST["batch_action"] == "HideAllRestockCharacter") {
        					$supplier_pricing_show_comment = "0"; // hide all restock character
        				} elseif ($_POST["batch_action"] == "ShowAllRestockCharacter") {
        					$supplier_pricing_show_comment = "1"; //show all restock character
        				}
        				
	        			$restock_character_products_id_select_sql = "	SELECT sol.products_purchases_lists_id, s.supplier_groups_id
	        														 	FROM ". TABLE_SUPPLIER_ORDER_LISTS ." AS sol 
	        															INNER JOIN ". TABLE_SUPPLIER ." AS s
	        														 	 	ON (sol.suppliers_id=s.supplier_id)
	        														 	WHERE sol.supplier_order_lists_id IN ('". implode("', '", $_REQUEST['orders_batch']) ."')";
	        			$restock_character_products_id_result_sql = tep_db_query($restock_character_products_id_select_sql);
	        			
						while($restock_character_products_id_row = tep_db_fetch_array($restock_character_products_id_result_sql)) {
							if (!is_array($restock_character_products_id_array[$restock_character_products_id_row['products_purchases_lists_id']])) {
								$restock_character_products_id_array[$restock_character_products_id_row['products_purchases_lists_id']] = array();
							}
							if (!in_array($restock_character_products_id_row['supplier_groups_id'], $restock_character_products_id_array[$restock_character_products_id_row['products_purchases_lists_id']])) {
								$update_supplier_pricing_show_comment_sql = "	UPDATE " . TABLE_SUPPLIER_PRICING . "
																				SET supplier_pricing_show_comment='". $supplier_pricing_show_comment ."' 
																				WHERE supplier_groups_id = '". $restock_character_products_id_row['supplier_groups_id'] ."' 
																					AND products_purchases_lists_id = '". $restock_character_products_id_row['products_purchases_lists_id'] ."'
																					AND supplier_pricing_disabled=0";
			        			tep_db_query($update_supplier_pricing_show_comment_sql);
			        			
			        			$restock_character_products_id_array[$restock_character_products_id_row['products_purchases_lists_id']][] = $restock_character_products_id_row['supplier_groups_id'];
			        		}
		        		}
		        		
		        		if ($_POST['batch_action'] == 'ShowAllRestockCharacter') {
		        			for ($orders_batch_count = 0; $orders_batch_count < sizeof($_REQUEST['orders_batch']); $orders_batch_count++) {
								$supplier_order_lists_history_data_array = array(	'supplier_order_lists_id' => $_REQUEST['orders_batch'][$orders_batch_count],
																					'supplier_order_lists_status' => '0',
																					'date_added' => 'now()',
																					'supplier_notified' => '0',
																					'comments' => 'Show All Restock Character',
																					'changed_by' => $login_email_address
																				);
																				
								tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $supplier_order_lists_history_data_array);
							}
		        		}
	        		} else {
	        			$messageStack->add_session(ERROR_NO_SUPPLIER_ORDERS_SELECTED, 'error');
	        		}
	        		
        			break;
        		case "ShowSubmittedRestockCharacter": //show all submitted restock character
        			if (count($_REQUEST['orders_batch'])) {
        				$restock_character_products_id_select_sql = "	SELECT distinct(solp.products_id), sol.products_purchases_lists_id, s.supplier_groups_id
	        														 	FROM ". TABLE_SUPPLIER_ORDER_LISTS ." AS sol
	        															INNER JOIN ". TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS ." AS solp
	        														 	 	ON (sol.supplier_order_lists_id=solp.supplier_order_lists_id)
	        														 	INNER JOIN ". TABLE_SUPPLIER ." AS s
	        														 		ON (sol.suppliers_id=s.supplier_id)
	        														 	WHERE sol.supplier_order_lists_id IN ('". implode("', '", $_REQUEST['orders_batch']) ."')";
	        			$restock_character_products_id_result_sql = tep_db_query($restock_character_products_id_select_sql);
	        			
						while($restock_character_products_id_row = tep_db_fetch_array($restock_character_products_id_result_sql)) {
							$update_supplier_pricing_show_comment_sql = "	UPDATE " . TABLE_SUPPLIER_PRICING . " 
																			SET supplier_pricing_show_comment=1 
																			WHERE products_id = '". $restock_character_products_id_row['products_id'] ."' 
																				AND supplier_groups_id = '". $restock_character_products_id_row['supplier_groups_id'] ."' 
																				AND products_purchases_lists_id = '". $restock_character_products_id_row['products_purchases_lists_id'] ."'
																				AND supplier_pricing_disabled=0";
							tep_db_query($update_supplier_pricing_show_comment_sql);
						}
						
						for ($orders_batch_count = 0; $orders_batch_count < sizeof($_REQUEST['orders_batch']); $orders_batch_count++) {
							$supplier_order_lists_history_data_array = array(	'supplier_order_lists_id' => $_REQUEST['orders_batch'][$orders_batch_count],
																				'supplier_order_lists_status' => '0',
																				'date_added' => 'now()',
																				'supplier_notified' => '0',
																				'comments' => 'Show Submitted Servers Restock Character',
																				'changed_by' => $login_email_address
																			);
																			
							tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $supplier_order_lists_history_data_array);
						}
        			} else {
	        			$messageStack->add_session(ERROR_NO_SUPPLIER_ORDERS_SELECTED, 'error');
	        		}
        			
        			break;
        	}
        	
        	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING, 'action=show_report&cont=1'));
        	
        	break;
	}
}

$saved_search_options = array( array ('id' => '', "text" => "Saved Criteria >>>>>") );
$search_criteria_select_sql = "SELECT search_criteria_id, search_criteria_name FROM " . TABLE_SEARCH_CRITERIA . " WHERE filename='" . tep_db_input(basename($PHP_SELF)) . "' ORDER BY search_criteria_name";
$search_criteria_result_sql = tep_db_query($search_criteria_select_sql);
while ($search_criteria_row = tep_db_fetch_array($search_criteria_result_sql)) {
	$saved_search_options[] = array ('id' => $search_criteria_row["search_criteria_id"], "text" => $search_criteria_row["search_criteria_name"]);
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
	<script language="javascript" src="includes/javascript/orders.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
	<script language="javascript" src="includes/javascript/php_serializer.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<!-- body_text //-->
<?
if ($_REQUEST['action'] == 'show_report') {
	if ($perform_unserialize_criteria) {
		if (isset($_SESSION['sup_order_lists_param']["criteria_id"]) && is_numeric($_SESSION['sup_order_lists_param']["criteria_id"])) {
			$search_criteria_select_sql = "SELECT search_criteria_name, search_criteria_string FROM " . TABLE_SEARCH_CRITERIA . " WHERE search_criteria_id = '" . (int)$_SESSION['sup_order_lists_param']["criteria_id"] . "' AND filename = '" . tep_db_input(basename($PHP_SELF)) . "'";
			$search_criteria_result_sql = tep_db_query($search_criteria_select_sql);
			
			if ($search_criteria_row = tep_db_fetch_array($search_criteria_result_sql)) {
				$_SESSION['sup_order_lists_param']["cur_criteria_name"] = $search_criteria_row["search_criteria_name"];
				$criteria_array = array();
				$criteria_array = explode('#~#', $search_criteria_row["search_criteria_string"]);
				
				unset($_SESSION['sup_order_lists_param']["cat_id"]);
				unset($_SESSION['sup_order_lists_param']["include_subcategory"]);
				unset($_SESSION['sup_order_lists_param']["product_id"]);
				unset($_SESSION['sup_order_lists_param']["order_status"]);
				unset($_SESSION['sup_order_lists_param']["start_date"]);
				unset($_SESSION['sup_order_lists_param']["end_date"]);
				unset($_SESSION['sup_order_lists_param']["order_id"]);
				unset($_SESSION['sup_order_lists_param']["supplier_group"]);
				unset($_SESSION['sup_order_lists_param']["supplier"]);
				unset($_SESSION['sup_order_lists_param']["sort_by"]);
				unset($_SESSION['sup_order_lists_param']["sort_order"]);
				unset($_SESSION['sup_order_lists_param']["show_records"]);
				
				if (count($status_options)) {
					foreach ($status_options as $id => $title) {
						unset($_SESSION['sup_order_lists_param']["status_".$id]);
					}
				}
				
				for ($s=0; $s < count($criteria_array); $s++) {
					list($serialized_key, $serialized_value) = explode(':~:', $criteria_array[$s]);
					$unserialized_key = tep_array_unserialize($serialized_key);
					$serialized_key_value = tep_array_unserialize($serialized_value);
					
					$_SESSION['sup_order_lists_param'][$unserialized_key] = $serialized_key_value;
				}
			}
		}
		$perform_unserialize_criteria = false;
	}
	
	$orders_select_str = "select sol.supplier_order_lists_id, sol.products_purchases_lists_name, sol.products_purchases_lists_id, sol.suppliers_name, sol.suppliers_id, sol.suppliers_email_address, DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d %H:%i') AS date_submitted, sol.supplier_order_lists_last_modified AS last_modified, sol.supplier_order_lists_billing_status as billed, sol.supplier_orders_verify_mode as verified, sol.currency, sol.currency_value, sls.supplier_list_status_name, s.supplier_groups_id from " . TABLE_SUPPLIER_ORDER_LISTS . " as sol inner join " . TABLE_SUPPLIER_LIST_STATUS . " as sls on sol.supplier_order_lists_status = sls.supplier_list_status_id left join " . TABLE_SUPPLIER . " as s on sol.suppliers_id=s.supplier_id ";
	
	$product_id_str = (isset($_SESSION['sup_order_lists_param']["product_id"]) && tep_not_null($_SESSION['sup_order_lists_param']["product_id"])) ? " solp.products_id='" . $_SESSION['sup_order_lists_param']["product_id"] . "'" : "1";
	$order_id_str = (isset($_SESSION['sup_order_lists_param']["order_id"]) && tep_not_null($_SESSION['sup_order_lists_param']["order_id"])) ? " sol.supplier_order_lists_id='" . $_SESSION['sup_order_lists_param']["order_id"] . "'" : "1";
  	$supplier_str = (isset($_SESSION['sup_order_lists_param']["supplier"]) && tep_not_null($_SESSION['sup_order_lists_param']["supplier"])) ? " sol.suppliers_id='" . $_SESSION['sup_order_lists_param']["supplier"] . "'" : "1";
  	$supplier_group_str = (isset($_SESSION['sup_order_lists_param']["supplier_group"]) && tep_not_null($_SESSION['sup_order_lists_param']["supplier_group"])) ? " s.supplier_groups_id='" . $_SESSION['sup_order_lists_param']["supplier_group"] . "'" : "1";
  	
  	if (tep_not_null($_SESSION['sup_order_lists_param']["start_date"])) {
		if (strpos($_SESSION['sup_order_lists_param']["start_date"], ':') !== false) {
			$startDateObj = explode(' ', trim($_SESSION['sup_order_lists_param']["start_date"]));
			list($yr, $mth, $day) = explode('-', $startDateObj[0]);
			list($hr, $min) = explode(':', $startDateObj[1]);
			$start_date_str = " ( DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['sup_order_lists_param']["start_date"]));
			$start_date_str = " ( DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
		}
	} else {
		$start_date_str = " 1 ";
	}
	
	if (tep_not_null($_SESSION['sup_order_lists_param']["end_date"])) {
		if (strpos($_SESSION['sup_order_lists_param']["end_date"], ':') !== false) {
			$endDateObj = explode(' ', trim($_SESSION['sup_order_lists_param']["end_date"]));
			list($yr, $mth, $day) = explode('-', $endDateObj[0]);
			list($hr, $min) = explode(':', $endDateObj[1]);
			$end_date_str = " ( DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['sup_order_lists_param']["end_date"]));
			$end_date_str = " ( DATE_FORMAT(sol.supplier_order_lists_date, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
		}
	} else {
		$end_date_str = " 1 ";
	}
	
  	$categories_str = " 1 ";
  	if (tep_not_null($_SESSION['sup_order_lists_param']["product_id"]) || $_SESSION['sup_order_lists_param']["cat_id"] !== '') {
		$orders_select_str .= " left join " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " as solp on (sol.supplier_order_lists_id=solp.supplier_order_lists_id and (solp.supplier_order_lists_type=1 or solp.supplier_order_lists_type=2)) ";
		
		if ($_SESSION['sup_order_lists_param']["cat_id"] !== '') {
			$have_all_cat_permission = false;
			
			if (tep_check_cat_tree_permissions(FILENAME_SUPPLIERS_ORDERS, $_SESSION['sup_order_lists_param']["cat_id"]) == 1) {
				$category_array = array($_SESSION['sup_order_lists_param']["cat_id"]);
				
				if ($_SESSION['sup_order_lists_param']["cat_id"] == '0' && $_SESSION['sup_order_lists_param']["include_subcategory"])	$have_all_cat_permission = true;
			} else {
				$category_array = array();
			}
			
	  		if (!$have_all_cat_permission) {
	  			if ($_SESSION['sup_order_lists_param']["include_subcategory"]) {
		  			tep_get_subcategories($category_array, $_SESSION['sup_order_lists_param']["cat_id"], FILENAME_SUPPLIERS_ORDERS);
		  		}
		  		
	  			$orders_select_str .= " left join " . TABLE_PRODUCTS_TO_CATEGORIES . " as pc on (solp.products_id=pc.products_id and pc.products_is_link=0) ";
	  			$categories_str = " pc.categories_id IN ('" . implode("', '", $category_array) . "') ";
	  		}
  		}
  	}
  	
  	$orders_where_str = " where sls.language_id = '" . (int)$languages_id . "' ";
  	$orders_where_str .= " and $product_id_str and $order_id_str and $start_date_str and $end_date_str and $categories_str and $supplier_str and $supplier_group_str ";
  	
  	$orders_group_by_str = " group by sol.supplier_order_lists_id ";
  	$orders_order_by_str = " order by sol.supplier_order_lists_id DESC ";
	
	$orders_status_select_str = "select distinct(sol.supplier_order_lists_status) as status_id from " . TABLE_SUPPLIER_ORDER_LISTS . " as sol inner join " . TABLE_SUPPLIER_LIST_STATUS . " as sls on sol.supplier_order_lists_status = sls.supplier_list_status_id left join " . TABLE_SUPPLIER . " as s on sol.suppliers_id=s.supplier_id ";
	
	if (tep_not_null($_SESSION['sup_order_lists_param']["product_id"]) || $_SESSION['sup_order_lists_param']["cat_id"] !== '') {
		$orders_status_select_str .= " left join " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " as solp on (sol.supplier_order_lists_id=solp.supplier_order_lists_id and (solp.supplier_order_lists_type=1 or solp.supplier_order_lists_type=2)) ";
		
		if ($_SESSION['sup_order_lists_param']["cat_id"] !== '') {
	  		$orders_status_select_str .= " left join " . TABLE_PRODUCTS_TO_CATEGORIES . " as pc on (solp.products_id=pc.products_id and pc.products_is_link=0) ";
  		}
  	}
  	
	$show_order_status = isset($_SESSION['sup_order_lists_param']["order_status"]) ? $_SESSION['sup_order_lists_param']["order_status"] : array();
	
	if (!count($show_order_status)) {
		$auto_get_status_select_sql = $orders_status_select_str . $orders_where_str . " group by sol.supplier_order_lists_id order by sls.supplier_list_status_sort_order ASC ";
		$auto_get_status_result_sql = tep_db_query($auto_get_status_select_sql);
		while($auto_get_status_row = tep_db_fetch_array($auto_get_status_result_sql)) {
			$show_order_status[] = $auto_get_status_row["status_id"];
		}
	}
	
  	$show_records = $_SESSION['sup_order_lists_param']["show_records"];
?>
					<tr>
            			<td valign="top" class="pageHeading"><?=HEADING_TITLE?>
            			<?
            				if ($manage_search_criteria_permission) {	// check for permission
            					if (tep_not_null($_SESSION['sup_order_lists_param']["cur_criteria_name"]) && tep_not_null($_SESSION['sup_order_lists_param']["criteria_id"])) {
									echo '<br><a href="javascript:void(confirm_delete(\'\', \'this search criteria\', \''.tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING, 'action=delete_search&criteria_id='.$_SESSION['sup_order_lists_param']["criteria_id"]).'\'))">Forget &lt;'.$_SESSION['sup_order_lists_param']["cur_criteria_name"].'&gt; Criteria</a>'; 
								}
							}
						?>
						</td>
            			<td class="smallText" align="right" valign="top">&nbsp;
						<?
							echo tep_draw_form('orders', FILENAME_SUPPLIERS_ORDERS, '', 'post');
							echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('oID', '', 'size="12"') . tep_draw_hidden_field('subaction', 'change_order');
							echo "</form><br>";
							if (count($saved_search_options) > 1) {
								echo tep_draw_form('goto_search_form', FILENAME_SUPPLIERS_ORDERS_TRACKING, 'action=show_report', 'post');
								echo tep_draw_hidden_field('subaction', 'goto_search');
								echo tep_draw_pull_down_menu("criteria_id", $saved_search_options, tep_not_null($_SESSION['sup_order_lists_param']["criteria_id"]) ? $_SESSION['sup_order_lists_param']["criteria_id"] : '', 'onChange="if(this.value != \'\') { this.form.submit(); }"');
								echo "</form>";
							}
						?>
						</td>
          			</tr>
<?
	$extra_detail = array ();
	$js = '';
	for ($status_count=0; $status_count < count($show_order_status); $status_count++) {
		$order_status_id = $show_order_status[$status_count];
		$status = $status_options[$order_status_id];
		
		$list_colspan_count = 6;
		if ($order_status_id == PARTIAL_DELIVERY_STATUS) $list_colspan_count ++;	// Batch actions
		//if ($order_status_id == MAKE_PAYMENT_STATUS) $list_colspan_count ++;	// Batch actions
		if ($order_status_id == 5) $list_colspan_count ++;	// Batch actions
		if ($view_payment_info_permission) {
			$list_colspan_count ++;
			
			if ($order_status_id == 3) $list_colspan_count += 2;	// Billing and Verify status
			if ($order_status_id == MAKE_PAYMENT_STATUS) $list_colspan_count += 2;	// Total Paid and Balance column
		}
		
		$status_total_payable_amount = $status_total_paid_amount = 0;
?>
					<tr>
          				<td colspan="2">
<?
		$form_name = 'suppliers_'.$order_status_id.'_lists_form';
		if ($order_status_id == PARTIAL_DELIVERY_STATUS) {
			$form_filename = FILENAME_SUPPLIERS_ORDERS;
			$form_param = tep_get_all_get_params(array('action')) . 'action=edit';
		} else {
			$form_filename = FILENAME_SUPPLIERS_ORDERS_TRACKING;
			$form_param = tep_get_all_get_params(array('action')) . 'action=batch_action';
		}
		
		echo tep_draw_form($form_name, $form_filename, $form_param, 'post', '');
		echo tep_draw_hidden_field($order_status_id.'_order_str', '', ' id="'.$order_status_id.'_order_str" ');
		
		if ($order_status_id == MAKE_PAYMENT_STATUS) {
			echo tep_draw_hidden_field('payment_remark', '');
			echo tep_draw_hidden_field('show_supplier_remark', '');
		}
?>
							<table border="0" width="100%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
  								<tr>
    								<td colspan="<?=$list_colspan_count?>">
    									<span class="pageHeading"><?=$status?></span>
<?		echo '<span id="'.$order_status_id.'_sol_nav"></span>'; ?>
									</td>
			  					</tr>
								<tr>
									<td width="6%" class="ordersBoxHeading"><?=TABLE_HEADING_ORDER_NO?></td>
									<td width="10%" class="ordersBoxHeading"><?=TABLE_HEADING_LIST_TITLE?></td>
								    <td width="11%" class="ordersBoxHeading"><?=TABLE_HEADING_ORDER_DATE?></td>
								    <td class="ordersBoxHeading"><?=TABLE_HEADING_SUP_NAME?></td>
								    <td width="20%" class="ordersBoxHeading"><?=TABLE_HEADING_SUP_EMAIL?></td>
<?		if ($view_payment_info_permission) {
			if ($order_status_id == 3) {
				echo '			    <td width="9%" class="ordersBoxHeading">'.TABLE_HEADING_ORDER_BILLING_STATUS.'</td>';
				echo '			    <td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_ORDER_VERIFY_STATUS.'</td>';
			}
			
			echo '				    <td width="9%" class="ordersBoxHeading" align="right">'.TABLE_HEADING_TOTAL_AMT.'</td>';
			
			if ($order_status_id == MAKE_PAYMENT_STATUS) {
				echo '				<td width="9%" class="ordersBoxHeading" align="right">'.TABLE_HEADING_TOTAL_PAID.'</td>';
				echo '				<td width="9%" class="ordersBoxHeading" align="right">'.TABLE_HEADING_TOTAL_BAL.'</td>';
				//echo '			    <td width="8%" class="ordersBoxHeading" align="right">'.TABLE_HEADING_PARTIAL_PAY.'</td>';
			}
		}
?>
									<td width="3%" class="ordersBoxHeading" align="center">Actions</td>
<?		//if ($order_status_id == PARTIAL_DELIVERY_STATUS || $order_status_id == MAKE_PAYMENT_STATUS || $order_status_id == 5) {
		if ($order_status_id == PARTIAL_DELIVERY_STATUS || $order_status_id == 5) {
			echo '					<td width="1%" class="ordersBoxHeading">'.tep_draw_checkbox_field('select_all_'.$order_status_id, '', false, '', 'id="select_all_'.$order_status_id.'" title="Select or deselect all ' . $status . ' suppliers orders" onClick="javascript:void(setCheckboxes(\''.$form_name.'\',\'select_all_'.$order_status_id.'\',\'orders_batch\')); '.($order_status_id == MAKE_PAYMENT_STATUS ? 'update_selected_price(this.form, \''.$order_status_id.'\', \'orders_batch\');' : '').'"').'</td>';
		}
?>
								</tr>
<?
		$orders_select_sql = $orders_select_str . $orders_where_str . ' and sol.supplier_order_lists_status = ' . $show_order_status[$status_count] . $orders_group_by_str . $orders_order_by_str;
		
		if ($show_records != "ALL") {
			$orders_split_object = new splitPageResults($HTTP_GET_VARS['page'.$order_status_id], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $orders_select_sql, $orders_select_sql_numrows, true);
		}
		$orders_result_sql = tep_db_query($orders_select_sql);
		
		$row_count = 0;
		while ($row = tep_db_fetch_array($orders_result_sql)) {
			$order_number = $row['supplier_order_lists_id'];
  			$orderdate = $row['date_submitted'];
  			$order_user = $row['suppliers_name'];
  			$order_useremail = $row['suppliers_email_address'];
  			
  			$extra_detail[$order_status_id]['order_id'][] = $order_number;
  			
  			if ($order_status_id == PARTIAL_DELIVERY_STATUS || $order_status_id == 5) { // check supplier_pricing_show_comment = 0 if order status = 1 or 5
	  			$restock_character_select_sql = "SELECT supplier_pricing_show_comment
												 FROM ". TABLE_SUPPLIER_PRICING ." 
												 WHERE supplier_groups_id = '". $row['supplier_groups_id'] ."'
												 	AND products_purchases_lists_id='". $row['products_purchases_lists_id'] ."'
												 	AND supplier_pricing_show_comment='0'
												 LIMIT 1";
	  			$restock_character_result_sql = tep_db_query($restock_character_select_sql);
  			}
  			
  			$paid_amount = $payable_amount = 0;
  			if ($view_payment_info_permission) {
	  			if ($order_status_id != 5) {
	  				$payable_amount = supplier_order::get_order_total_payable_amount($order_number);
	  			}
	  			
	  			$status_total_payable_amount += $payable_amount;
	  			
	  			if ($order_status_id == MAKE_PAYMENT_STATUS) {
	  				$paid_amount = supplier_payment::get_order_paid_amount($order_number);
	  				
	  				$status_total_paid_amount += $paid_amount;
	  			}
  			}
  			
  			$unpaid_amount = (double)$payable_amount - (double)$paid_amount;
  			
  			$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
?>
								<tr id="<?=$order_status_id.'_main_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?=$order_status_id.'_sub_'.$row_count?>##<?=$order_status_id.'_sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?=$order_status_id.'_sub_'.$row_count?>##<?=$order_status_id.'_sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?=$order_status_id.'_sub_'.$row_count?>##<?=$order_status_id.'_sub2_'.$row_count?>')">
									<td class="ordersRecords" nowrap><a href="<?=tep_href_link(FILENAME_SUPPLIERS_ORDERS, 'oID='.$order_number.'&action=edit', 'NONSSL')?>"><?=$order_number?></a></td>
									<td class="ordersRecords">
									<?
										echo $row['products_purchases_lists_name'];
										if ($order_status_id == PARTIAL_DELIVERY_STATUS || $order_status_id == 5) { // order status is pending or draft
											if (!tep_db_num_rows($restock_character_result_sql)) { //if all supplier_pricing_show_comment <> 0 
								  				echo '&nbsp;'. tep_image(DIR_WS_IMAGES . 'icon_status_green.gif');
								  			}
							  			}
						  			?>
									</td>
							      	<td class="ordersRecords" nowrap><?=$orderdate?></td>
							      	<td class="ordersRecords"><?=$order_user?></td>
							      	<td class="ordersRecords"><?=$order_useremail?></td>
<?			if ($view_payment_info_permission) {	// Need to recalculate the currency exchange rate since it is in USD when grab from the query
				if ($order_status_id == 3) {
					echo '			<td class="ordersRecords">'.($row['billed'] == '1' ? TEXT_TRANS_BILLED : TEXT_TRANS_NOT_BILLED).'</td>';
					echo '			<td align="center" class="ordersRecords">'.($row['verified'] == '1' ? tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) : tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10)).'</td>';
				}
				
				echo '				<td class="ordersRecords" align="right">'.$currencies->format($payable_amount, true, $row['currency'], $row['currency_value']).'</td>';
				
				if ($order_status_id == MAKE_PAYMENT_STATUS) {
					echo '			<td class="ordersRecords" align="right">'.$currencies->format($paid_amount).'</td>
									<td class="ordersRecords" align="right">'.$currencies->format($unpaid_amount).'</td>';
					/*
					echo '		    <td class="ordersRecords" align="right">'.
										tep_draw_input_field('partial_pay['.$order_number.']', '', ' size="10" id="partial_pay_'.$order_number.'" onKeyUp="checkPayAmt(this, '.$unpaid_amount.', \''.$order_status_id.'\');" onKeyPress="return noEnterKey(event)"') . '
									</td>';
					*/
				}
				$js .= "orderAmountArray['".$order_number."'] = ".(double)$unpaid_amount.";\n";
			}
?>
									<td class="ordersRecords" align="center">
			  							<?='<a href="' . tep_href_link(FILENAME_SUPPLIERS_ORDERS, 'oID='.$order_number.'&action=edit', 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "14", "13", 'align="top"').'</a>'?>
			  						</td>
<?			if ($order_status_id == PARTIAL_DELIVERY_STATUS || $order_status_id == 5) {
				echo '				<td class="reportRecords">'.tep_draw_checkbox_field('orders_batch[]', $order_number, false).'</td>';
			}
			/*
			 else if ($order_status_id == MAKE_PAYMENT_STATUS) {
				echo '				<td class="reportRecords">'.tep_draw_checkbox_field('orders_batch[]', $order_number, false, '', 'id="'.$row['suppliers_id'].'_'.$order_number.'" onClick="update_selected_price(this.form, \''.$order_status_id.'\', \'orders_batch\');"').'</td>';
			}
			*/
?>
								</tr>
								<tbody id="<?=$order_status_id."_".$order_number.'_order_sec'?>" class="hide">
                    				<tr id="<?=$order_status_id.'_sub_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?=$order_status_id.'_main_'.$row_count?>##<?=$order_status_id.'_sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?=$order_status_id.'_main_'.$row_count?>##<?=$order_status_id.'_sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?=$order_status_id.'_main_'.$row_count?>##<?=$order_status_id.'_sub2_'.$row_count?>')">
			  							<td class="ordersRecords">&nbsp;</td>
			  							<td colspan="<?=($list_colspan_count-1)?>">
			  								<div id="<?=$order_status_id.'_'.$order_number.'_order'?>"></div>
			  							</td>
			  						</tr>
				               	</tbody>
<?
			$row_count++;
			unset($amt);
		}
		
		$batch_action_array = array(array('id' => '', 'text' => 'With selected:'));
		if ($supplier_pricing_page_permission) {
			$batch_action_restock_character_array = array(	array('id' => 'ShowAllRestockCharacter', 'text' => 'Show All RSTK CHAR'),
															array('id' => 'ShowSubmittedRestockCharacter', 'text' => 'Show Submitted Servers RSTK CHAR'),
															array('id' => 'HideAllRestockCharacter', 'text' => 'Hide All RSTK CHAR')
														  );
		}
		if ($order_status_id == PARTIAL_DELIVERY_STATUS) {
			$batch_action_array[] = array('id' => 'MergingEdit', 'text' => 'Merging Edit');
			$batch_action_array = array_merge($batch_action_array, $batch_action_restock_character_array);
		}
		/*
		if ($order_status_id == MAKE_PAYMENT_STATUS) {
			if ($view_payment_info_permission) {
				$batch_action_array[] = array('id' => 'MakePayment', 'text' => 'Make Payment');
			}
		}
		*/
		if ($order_status_id == 5) {
			$batch_action_array = array_merge($batch_action_array, $batch_action_restock_character_array);
		}
		
		if ($order_status_id != 4 && $order_status_id != 5) {
			if ($view_payment_info_permission) {
				echo '			<tr>
									<td class="ordersRecords" colspan="'.($order_status_id==3 ? 7 : 5).'">&nbsp;</td>
									<td align="right" class="ordersRecords"><b>'.$currencies->format($status_total_payable_amount).'</b></td>';
				if ($order_status_id == MAKE_PAYMENT_STATUS) {
					echo '			<td align="right" class="ordersRecords"><b>'.$currencies->format($status_total_paid_amount).'</b></td>
									<td align="right" class="ordersRecords"><b>'.$currencies->format($status_total_payable_amount-$status_total_paid_amount).'</b></td>
									<td align="right" class="ordersRecords" nowrap><span id="total_sel_amt_'.$order_status_id.'" class="redIndicator"></span></td>';
				} else {
					echo '			<td class="ordersRecords" colspan="'.($order_status_id==3 ? $list_colspan_count-8 : $list_colspan_count-6).'">&nbsp;</td>';
				}
				echo '			</tr>';
			} else {
				echo '			<tr>
									<td class="ordersRecords" colspan="'.($list_colspan_count).'">'.tep_draw_separator('pixel_trans.gif', '1', '1').'</td>
								</tr>';
			}
		}
		
		if (count($batch_action_array) > 1) {
?>
								<tr>
									<td colspan="<?=$list_colspan_count?>" align="right">
									<?
										echo tep_draw_pull_down_menu('batch_action', $batch_action_array) . '&nbsp;';
										echo tep_submit_button('Go', 'Go', 'name="multi_submit_btn" onClick="return confirmBatchAction(this.form, \''.$order_status_id.'\', \'orders_batch\')"', 'inputButton');
									?>
									</td>
								</tr>
<?		} ?>
							</table>
						</form>
						</td>
					</tr>
					<tr>
            			<td colspan="2">
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<tr>
                					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_ORDERS, tep_db_num_rows($orders_result_sql) > 0 ? "1" : "0", tep_db_num_rows($orders_result_sql), tep_db_num_rows($orders_result_sql)) : $orders_split_object->display_count($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'.$order_status_id], TEXT_DISPLAY_NUMBER_OF_ORDERS)?></td>
                					<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $orders_split_object->display_links($orders_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'.$order_status_id], tep_get_all_get_params(array('page'.$order_status_id, 'cont', 'subaction', 'criteria_id'))."cont=1", 'page'.$order_status_id)?></td>
              					</tr>
            				</table>
            			</td>
					</tr>
<?	} //end for loop ?>
	          		<script language="javascript">
					<!--
						var orderAmountArray = new Array();
					<?
						echo $js;
						
						foreach ($extra_detail as $status_id => $res) {
							$order_str = count($res['order_id']) ? implode(',', $res['order_id']) : ''; ?>
							document.getElementById('<?=$status_id?>'+'_order_str').value = "<?=$order_str?>";
							
							supplierOrderInfo('<?=$status_id?>', 0, false, '<?=(int)$languages_id?>', '<?=SID?>', '<?=$login_id?>');
					<?	} ?>
						
						function update_selected_price(frmObj, frmStatus, checkName) {
						<?	if (!$view_payment_info_permission) { echo 'return true;'; } ?>
								
							var total_sel_amount = 0;
							var any_box_selected = false;
							var elts      = (typeof(frmObj.elements[checkName+'[]']) != 'undefined')
					 						? frmObj.elements[checkName+'[]']
					 						: "";
    						var elts_cnt  = (typeof(elts.length) != 'undefined')
                  							? elts.length
                  							: 0;
                  			
                  			if (elts_cnt) {
						        for (var i=0; i < elts_cnt; i++) {
						            e = elts[i];
						            
						            if (e.type=='checkbox' && e.checked) {
						            	any_box_selected = true;
						            	if (orderAmountArray[e.value] != null) {
						            		var pay_obj = document.getElementById('partial_pay_' + e.value);
							        		
							        		if (pay_obj != null) {
							        			var pay_amt = parseFloat(pay_obj.value);
							        			if (pay_amt > 0 && pay_amt < orderAmountArray[e.value]) {
							        				total_sel_amount += pay_amt;
							        			} else {
							        				total_sel_amount += orderAmountArray[e.value];
							        				pay_obj.value = '';
							        			}
							        		} else {
							            		total_sel_amount += orderAmountArray[e.value];
							            	}
						            	}
									}
						        } // end for
						    } else if (elts != '') {
						    	e = elts;
						        if (e.type=='checkbox' && e.checked) {
						        	any_box_selected = true;
						        	if (orderAmountArray[e.value] != null) {
						        		var pay_obj = document.getElementById('partial_pay_' + e.value);
						        		
						        		if (pay_obj != null) {
						        			var pay_amt = parseFloat(pay_obj.value);
						        			if (pay_amt > 0 && pay_amt < orderAmountArray[e.value]) {
						        				total_sel_amount += pay_amt;
						        			} else {
						        				total_sel_amount += orderAmountArray[e.value];
						        				pay_obj.value = '';
						        			}
						        		} else {
						            		total_sel_amount += orderAmountArray[e.value];
						            	}
						            }
								}
						    }
						    
						    var span_obj = DOMCall('total_sel_amt_' + frmStatus);
						    if (any_box_selected) {
								span_obj.innerHTML = '<?=TEXT_SELECTED_ORDERS_AMOUNT?>' + currency(total_sel_amount, '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_left"]?>', '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_right"]?>', '<?=DISPLAY_PRICE_DECIMAL?>');
							} else {
								span_obj.innerHTML = '';
							}
						}
						
						function hideShow(groupName, styleClass) {
							var row_count = eval(groupName+"_count");
							for (var i=0; i<row_count; i++) {
								document.getElementById(groupName+"_"+i).className = styleClass;
							}
							
							if (styleClass == "show") {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'hide')\">Hide Ordered Details</a>";
								SetCookie(groupName, '1');
							} else {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'show')\">Show Ordered Details</a>";
								SetCookie(groupName, '0');
							}
						}
						
						function showOverEffect(object, class_name, extra_row) {
							rowOverEffect(object, class_name);
							var rowObjArray = extra_row.split('##');
	  						for (var i = 0; i < rowObjArray.length; i++) {
	  							if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
	  								rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
	  							}
	  						}
						}
						
						function showOutEffect(object, class_name, extra_row) {
							rowOutEffect(object, class_name);
							var rowObjArray = extra_row.split('##');
					  		for (var i = 0; i < rowObjArray.length; i++) {
					  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					  				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
					  			}
					  		}
						}
						
						function showClicked(object, class_name, extra_row) {
							rowClicked(object, class_name);
							
							var rowObjArray = extra_row.split('##');
					  		for (var i = 0; i < rowObjArray.length; i++) {
					  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
					  				/*
					  				if (rowObjArray[i].indexOf('_main_') != -1) {
										var temp_array = rowObjArray[i].split('_');
										refreshOrderListsOptions(temp_array[0], '<?=(int)$languages_id?>');
									}*/
					  			}
	  						}
						}
						
						function payment_info(remark, notify) {
							if (document.suppliers_2_lists_form.payment_remark != null) {
								document.suppliers_2_lists_form.payment_remark.value = remark;
							}
							if (document.suppliers_2_lists_form.show_supplier_remark != null) {
								document.suppliers_2_lists_form.show_supplier_remark.value = notify;
							}
							
							document.suppliers_2_lists_form.submit();
						}
						
						function payment_init() {
							document.suppliers_2_lists_form.batch_action.selectedIndex = 0;
						}
						
						function checkPayAmt(pay_obj, max_pay, frmStatus) {
							if (trim_str(pay_obj.value) != '' && !currencyValidation(trim_str(pay_obj.value))) {
								pay_obj.value = '';
							} else {
								if (parseFloat(pay_obj.value) > parseFloat(max_pay)) {
									alert('Entered pay amount exceed the balance amount!\nIt is reset to ' + max_pay + ', the balance amount.');
									pay_obj.value = max_pay;
									return false;
								}
							}
							
							update_selected_price(pay_obj.form, frmStatus, 'orders_batch');
							
							return true;
						}
						
						function confirmBatchAction(frmObj, frmStatus, checkName) {
							if (trim_str(frmObj.batch_action.value) == '') {
								alert('Please select your batch action!');
								return false;
							} else {
								if (frmObj.batch_action.value == 'MakePayment') {
									/* For showModalDialog return value */
									var myObj = new Object();
								    myObj.m_r = '';
								    myObj.m_n = '';

									var m_r = '';
									var m_n = '';
									
									var s_ordersArray = new Array();
									var s_index = 0;
									
									var first_sup = '';
									var total_sel_amount = 0;
									var any_box_selected = false;
									
									var elts      = (typeof(frmObj.elements[checkName+'[]']) != 'undefined')
							 						? frmObj.elements[checkName+'[]']
							 						: "";
		    						var elts_cnt  = (typeof(elts.length) != 'undefined')
		                  							? elts.length
		                  							: 0;
		                  			
		                  			if (elts_cnt) {
								        for (var i=0; i < elts_cnt; i++) {
								            e = elts[i];
								            
								            if (e.type=='checkbox' && e.checked) {
									            s_ordersArray[s_index] = new Array();
									            
								            	checkbox_id_array = e.id.split('_');
								            	if (!any_box_selected) {
								            		first_sup = checkbox_id_array[0];
								            	} else {
								            		if (first_sup != checkbox_id_array[0]) {
								            			alert('Your selections includes orders for more than one supplier!');
								            			frmObj.batch_action.selectedIndex = 0;
														return false;
								            		}
								            	}
								            	if (orderAmountArray[e.value] != null) {
								            		s_ordersArray[s_index]['id'] = e.value;
								            		
								            		var pay_obj = document.getElementById('partial_pay_' + e.value);
									        		if (pay_obj != null) {
									        			var pay_amt = parseFloat(pay_obj.value);
									        			if (pay_amt > 0 && pay_amt < orderAmountArray[e.value]) {
									        				total_sel_amount += pay_amt;
									        				s_ordersArray[s_index]['amt'] = pay_amt;
									        			} else {
									        				total_sel_amount += orderAmountArray[e.value];
									        				pay_obj.value = '';
									        				s_ordersArray[s_index]['amt'] = 'f';
									        			}
									        		} else {
									            		total_sel_amount += orderAmountArray[e.value];
									            		s_ordersArray[s_index]['amt'] = 'f';
									            	}
									            	s_index++;
								            	}
								            	any_box_selected = true;
											}
								        } // end for
								    } else if (elts != '') { // When there is only ONE order record in Processing Status
								    	e = elts;
								        if (e.type=='checkbox' && e.checked) {
								        	s_ordersArray[s_index] = new Array();
								        	
								        	checkbox_id_array = e.id.split('_');
								           	first_sup = checkbox_id_array[0];
								            
								            any_box_selected = true;
								            if (orderAmountArray[e.value] != null) {
								            	s_ordersArray[s_index]['id'] = e.value;
								            	
								            	var pay_obj = document.getElementById('partial_pay_' + e.value);
								        		
								        		if (pay_obj != null) {
								        			var pay_amt = parseFloat(pay_obj.value);
								        			if (pay_amt > 0 && pay_amt < orderAmountArray[e.value]) {
								        				total_sel_amount += pay_amt;
								        				s_ordersArray[s_index]['amt'] = pay_amt;
								        			} else {
								        				total_sel_amount += orderAmountArray[e.value];
								        				pay_obj.value = '';
								        				s_ordersArray[s_index]['amt'] = 'f';
								        			}
								        		} else {
								            		total_sel_amount += orderAmountArray[e.value];
								            		s_ordersArray[s_index]['amt'] = 'f';
								            	}
								            }
										}
								    }
								    
								    if (!any_box_selected) {
								    	alert('Please select at least one order for your payment!');
								    	frmObj.batch_action.selectedIndex = 0;
										return false;
								    } else {
								    	answer = confirm('Are you sure to make payment with the amount of ' + currency(total_sel_amount, '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_left"]?>', '<?=$currencies->currencies[DEFAULT_CURRENCY]["symbol_right"]?>') + ' ?');
								    	if (answer != 0) {
								    		var php = new PHP_Serializer();
									        var s_order_str = php.serialize(s_ordersArray);
									        
									        var win_height = 400 + ( (s_index + 1) * 14 ) + 35;
									        var m_win_height = 415 + ( (s_index + 1) * 15 ) + 20;
									        
								    		if (window.showModalDialog) {	// For IE
												window.showModalDialog('popup/payment_info.html.php?action=new_payment&aid='+'<?=$login_id?>'+'&sid='+first_sup+'&s_o='+s_order_str+'&_lang='+'<?=$languages_id?>',window,"dialogWidth:600px;dialogHeight:"+m_win_height+"px;center:yes;help:no;status:no");
											} else {
								    			openDGDialog('popup/payment_info.html.php?action=new_payment&aid='+'<?=$login_id?>'+'&sid='+first_sup+'&s_o='+s_order_str+'&_lang='+'<?=$languages_id?>', 600, win_height, '', ',scrollbars=yes');
								    		}
									    } else {
									    	frmObj.batch_action.selectedIndex = 0;
									    	return false;
									    }
								    }
								    
								    return false;
								} else if (frmObj.batch_action.value == 'ShowAllRestockCharacter' || frmObj.batch_action.value == 'HideAllRestockCharacter' || frmObj.batch_action.value == 'ShowSubmittedRestockCharacter') {
									var param = "<?=tep_get_all_get_params(array('action')) . 'action=batch_action'?>";
									var path = "<?=tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING)?>";
									var curSID = "<?=SID?>";
									
									var goto_url = path + (curSID ? '&' : '?') + param;
									
						    		frmObj.action = goto_url;
						    		
						    		return true;
								} else {
									return true;
								}
							}
						}
						
					<?	if (tep_not_null($_SESSION['sup_order_lists_param']["page_refresh"])) { ?>
							var page_url = "<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('oID', 'cont'))."cont=1")?>";
							setAutoRefresh("<?=$_SESSION['sup_order_lists_param']["page_refresh"]?>", page_url);
					<?	} ?>
					//-->
					</script>
<?
} else {
?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
									<td class="main" align="right">&nbsp;
									<?
									if (count($saved_search_options) > 1) {
										echo tep_draw_form('goto_search_form', FILENAME_SUPPLIERS_ORDERS_TRACKING, 'action=show_report', 'post');
										echo tep_draw_hidden_field('subaction', 'goto_search');
										echo tep_draw_pull_down_menu("criteria_id", $saved_search_options, '', 'onChange="if(this.value != \'\') { this.form.submit(); }"');
										echo "</form>";
									}
									?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<?
						echo tep_draw_form('supplier_order_lists_criteria', FILENAME_SUPPLIERS_ORDERS_TRACKING, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', '');
						echo tep_draw_hidden_field('supplier_order_list_subaction', 'do_search', ' id="supplier_order_list_subaction" ');
					?>
					<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="2" cellpadding="0">
<?
	$categories_array = tep_get_eligible_category_tree(FILENAME_SUPPLIERS_ORDERS, 0, '___', '', $categories_array);
	
	$sort_by_array = array(	array ('id' => 'sort_faction_servers', "text" => TEXT_SORT_FACTION_SERVERS),
							array ('id' => 'sort_servers', "text" => TEXT_SORT_SERVERS)
						);
	
	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
								array ('id' => '10', "text" => "10"),
								array ('id' => '20', "text" => "20"),
								array ('id' => '50', "text" => "50"),
								array ('id' => 'ALL', "text" => "All")
							);
	
	$supplier_group_options = array( array ('id' => '', "text" => "All Supplier Groups") );
	$sup_group_select_sql = "SELECT supplier_groups_id, supplier_groups_name FROM " . TABLE_SUPPLIER_GROUPS ." ORDER BY supplier_groups_name";
	$sup_group_result_sql = tep_db_query($sup_group_select_sql);
	while($sup_group_row = tep_db_fetch_array($sup_group_result_sql)) {
		$supplier_group_options[] = array('text' => $sup_group_row['supplier_groups_name'], 'id' => $sup_group_row['supplier_groups_id']);
	}
	
	$supplier_options = array( array ('id' => '', "text" => "All Suppliers") );
	$sup_select_sql = "SELECT supplier_id, supplier_code, supplier_firstname, supplier_lastname FROM " . TABLE_SUPPLIER ." ORDER BY supplier_firstname";
	$sup_result_sql = tep_db_query($sup_select_sql);
	while($sup_row = tep_db_fetch_array($sup_result_sql)) {
		$supplier_options[] = array('text' => $sup_row['supplier_firstname'] . '  ' . $sup_row['supplier_lastname'] . (tep_not_null($sup_row['supplier_code']) ? ' ['.$sup_row['supplier_code'].']' : ''), 'id' => $sup_row['supplier_id']);
	}
?>
								<tr>
									<td class="main" width="12%"><?=ENTRY_ORDER_START_DATE?></td>
					    			<td class="main">
					    				<table border="0" cellspacing="2" cellpadding="0">
					    					<tr>
					    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('start_date', $_SESSION['sup_order_lists_param']["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.supplier_order_lists_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.supplier_order_lists_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
					    						<td class="main" width="5%">&nbsp;</td>
					    						<td class="main" valign="top" nowrap><?=ENTRY_ORDER_END_DATE?></td>
					    						<td class="main" width="1%">&nbsp;</td>
					    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('end_date', $_SESSION['sup_order_lists_param']["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.supplier_order_lists_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.supplier_order_lists_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
					    					</tr>
					    				</table>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
								<tr>
									<td class="main"><?=ENTRY_ORDER_ID?></td>
					    			<td class="main"><?=tep_draw_input_field('order_id', $_SESSION['sup_order_lists_param']["order_id"], ' id="order_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main" width="15%"><?=ENTRY_CATEGORY?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("cat_id", $categories_array, $_SESSION['sup_order_lists_param']["cat_id"], ' id="cat_id" ')?>
					    				<?=tep_draw_checkbox_field("include_subcategory", 1, isset($_SESSION['sup_order_lists_param']["include_subcategory"]) && $_SESSION['sup_order_lists_param']["include_subcategory"] == 0 ? false : true, '', ' id="include_subcategory" ') . '&nbsp;' . TEXT_INCLUDE_SUBCATEGORY?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
								<tr>
									<td class="main"><?=ENTRY_PRODUCT_ID?></td>
					    			<td class="main">
					    			<?
					    				echo tep_draw_input_field('product_id', $_SESSION['sup_order_lists_param']["product_id"], ' id="product_id" size="15" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"');
					    				echo '&nbsp;<a href="javascript:openDGDialog(\''. tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'fname='.FILENAME_SUPPLIERS_ORDERS) . '\', 600, 250, \'\');">(Product List)</a>';
					    			?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
        						<tr>
									<td class="main" width="12%" valign="top"><?=ENTRY_ORDER_STATUS?></td>
					    			<td>
					    				<table border="0" cellspacing="2" cellpadding="0">
					    			<?
					    				if (count($status_options)) {
				    						echo '<tr><td class="main">'.tep_draw_checkbox_field('order_status_any', '1', isset($_SESSION['sup_order_lists_param']) && count($_SESSION['sup_order_lists_param']["order_status"]) ? false : true, '', 'id="order_status_any" onClick="set_status_option(this);"') . '</td><td class="main" colspan="'.(count($status_options)*2-1).'">'.TEXT_ANY.'</td></tr>';
				    						echo '<tr>';
				    						foreach ($status_options as $id => $title) {
				    							$order_status_display_str = '';
				    							
					    						$order_status_display_str .= 
					    							'	<td class="main">'.
					    									tep_draw_checkbox_field('order_status[]', $id, isset($_SESSION['sup_order_lists_param']) ? (is_array($_SESSION['sup_order_lists_param']["order_status"]) && in_array($id, $_SESSION['sup_order_lists_param']["order_status"]) ? true : false) : (false), '', 'onClick=verify_status_selection();') . '
					    								</td>
					    								<td class="main">'.$title.'</td>';
				    							echo $order_status_display_str;
					    					}
					    					echo '</tr>';
					    				}
					    			?>
					    				</table>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main" width="12%"><?=ENTRY_SUPPLIER_GROUP?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("supplier_group", $supplier_group_options, tep_not_null($_SESSION['sup_order_lists_param']["supplier_group"]) ? $_SESSION['sup_order_lists_param']["supplier_group"] : '', '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main" width="12%"><?=ENTRY_SUPPLIER?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("supplier", $supplier_options, tep_not_null($_SESSION['sup_order_lists_param']["supplier"]) ? $_SESSION['sup_order_lists_param']["supplier"] : '', '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main"><?=ENTRY_SORT?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("sort_by", $sort_by_array, $_SESSION['sup_order_lists_param']["sort_by"], '')?>
		    							<?=tep_draw_radio_field('sort_order', 'ASC', isset($_SESSION['sup_order_lists_param']) ? ($_SESSION['sup_order_lists_param']["sort_order"]=='ASC' ? "checked" : '') : "checked") . "&nbsp;" . TEXT_ASC . "&nbsp;" . tep_draw_radio_field('sort_order', 'DESC', isset($_SESSION['sup_order_lists_param']) ? ($_SESSION['sup_order_lists_param']["sort_order"]=='DESC' ? "checked" : '') : '') . "&nbsp;" . TEXT_DESC?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION['sup_order_lists_param']["show_records"]) ? $_SESSION['sup_order_lists_param']["show_records"] : '', '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
        					</table>
        				</td>
        			</tr>
        			<tr>
	  					<td>
	  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
	  							<tr>
									<td class="main" width="12%">&nbsp;</td>
	  								<td nowrap class="main">
	  								<?
	  								if ($manage_search_criteria_permission) {
	  									echo '<input type="submit" name="SaveSearchBtn" value="Save Criteria As" title="Enter the name for this order lists criteria" class="inputButton" onClick="return form_checking(this.form, \'save_search\');">&nbsp;&nbsp;&nbsp;&nbsp;';
	  									echo tep_draw_input_field('search_name', $_SESSION['sup_order_lists_param']["cur_criteria_name"], ' id="search_name" size="40" ');
	  								} else echo '&nbsp;';
	  								?>
	  								
	  								</td>
	  								<td align="right">
	  									<?=tep_image_submit('button_search.gif', IMAGE_SEARCH, " id='OrderListsBtn' onClick=\"return form_checking(this.form, 'do_search');\"")?>&nbsp;&nbsp;
	  									<a href="<?=tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING, 'action=reset_session')?>"><?=tep_image_button('button_reset.gif', IMAGE_RESET)?></a>
	  								</td>
	  							</tr>
	  						</table>
	  					</td>
	  				</tr>
	  				<tr>
						<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
					</tr>
					</form>
					<script language="javascript"><!--
						function form_checking(form_obj, action) {
							if (action=='save_search') {
								if (trim_str(document.getElementById('search_name').value) != '') {
									document.getElementById('supplier_order_list_subaction').value = 'save_search';
									form_obj.SaveSearchBtn.disabled = true;
									form_obj.SaveSearchBtn.value = 'Please wait...';
								} else {
									alert(" You must enter a name for saving your search criteria!");
									document.getElementById('search_name').value = '';
									document.getElementById('search_name').focus();
									return false;
								}
							} else {
								document.getElementById('supplier_order_list_subaction').value = 'do_search';
							}
							
						    form_obj.submit();
							return true;
			    		}
			    		
			    		function getReturnedValue(received_val) {
							document.getElementById('product_id').value = received_val;
						}
						
						function resetControls(controlObj) {
							if (trim_str(controlObj.value) != '') {
								document.supplier_order_lists_criteria.start_date.value = '';
								document.supplier_order_lists_criteria.end_date.value = '';
								document.supplier_order_lists_criteria.product_id.value = '';
								document.getElementById('order_status_any').checked = true;
								set_status_option(document.getElementById('order_status_any'));
								document.supplier_order_lists_criteria.supplier_group.selectedIndex = 0;
				    			document.supplier_order_lists_criteria.supplier.selectedIndex = 0;
				    			document.supplier_order_lists_criteria.show_records.selectedIndex = 0;
				    		} else {
				    			controlObj.value = '';
				    		}
						}
						
			    		function set_status_option(any_status_obj) {
			    			var multi_status_select = document.supplier_order_lists_criteria.elements['order_status[]'];
			    			if (any_status_obj.checked == true) {
								for (i=0;i<multi_status_select.length;i++) {
									multi_status_select[i].checked = false;
									
									var cur_status_id = multi_status_select[i].value;
			    					var multi_tags_select = document.supplier_order_lists_criteria.elements['status_'+cur_status_id+'[]'];
									if (typeof(multi_tags_select) != 'undefined') {
										if (typeof(multi_tags_select.length) != 'undefined') {
											for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
												multi_tags_select[tag_cnt].disabled = true;
												multi_tags_select[tag_cnt].checked = false;
											}
										} else {
											multi_tags_select.disabled = true;
											multi_tags_select.checked = false;
										}
										
									}
								}
			    			} else {	// force to check if no any order status option is selected
			    				var selected_count = 0;
			    				for (i=0;i<multi_status_select.length;i++) {
			    					var cur_status_id = multi_status_select[i].value;
			    					var multi_tags_select = document.supplier_order_lists_criteria.elements['status_'+cur_status_id+'[]'];
									if (multi_status_select[i].checked == true) {
										selected_count++;
										if (typeof(multi_tags_select) != 'undefined') {
											if (typeof(multi_tags_select.length) != 'undefined') {
												for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
													multi_tags_select[tag_cnt].disabled = false;
												}
											} else {
												multi_tags_select.disabled = false;
											}
										}
									} else {
										if (typeof(multi_tags_select) != 'undefined') {
											if (typeof(multi_tags_select.length) != 'undefined') {
												for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
													multi_tags_select[tag_cnt].disabled = true;
													multi_tags_select[tag_cnt].checked = false;
												}
											} else {
												multi_tags_select.disabled = true;
												multi_tags_select.checked = false;
											}
										}
									}
								}
								if (!selected_count) {
									any_status_obj.checked = true;
								}
			    			}
			    		}
			    		
			    		function verify_status_selection() {
			    			var multi_status_select = document.supplier_order_lists_criteria.elements['order_status[]'];
			    			var selected_count = 0;
		    				for (i=0;i<multi_status_select.length;i++) {
		    					var cur_status_id = multi_status_select[i].value;
			    				var multi_tags_select = document.supplier_order_lists_criteria.elements['status_'+cur_status_id+'[]'];
								if (multi_status_select[i].checked == true) {
									selected_count++;
									if (typeof(multi_tags_select) != 'undefined') {
										if (typeof(multi_tags_select.length) != 'undefined') {
											for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
												multi_tags_select[tag_cnt].disabled = false;
											}
										} else {
											multi_tags_select.disabled = false;
										}
									}
								} else {
									if (typeof(multi_tags_select) != 'undefined') {
										if (typeof(multi_tags_select.length) != 'undefined') {
											for (tag_cnt=0; tag_cnt<multi_tags_select.length; tag_cnt++) {
												multi_tags_select[tag_cnt].disabled = true;
												multi_tags_select[tag_cnt].checked = false;
											}
										} else {
											multi_tags_select.disabled = true;
											multi_tags_select.checked = false;
										}
									}
								}
							}
							if (!selected_count) {
								document.getElementById('order_status_any').checked = true;
							} else {
								document.getElementById('order_status_any').checked = false;
							}
			    		}
			    		
			    		set_status_option(document.getElementById('order_status_any'));
			    	//-->
					</script>
<?
}
?>
				</table>
<!-- body_text_eof //-->
  			</td>
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>