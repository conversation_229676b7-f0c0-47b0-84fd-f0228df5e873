<?
/******************************************************************************
	NOTE:
	(1) products_received_quantity = 'NULL' implies no restock been performed yet
	(2) products_received_quantity = '0' implies found nothing from restock account
									 although suppliers said they had sent
	
	(3) Back Order link will only available when there is back order(s) for that servers
	(4) Pending -> Processing: update the buyback avg. price
	(5) Processing -> Pending: adjust the buyback avg. price
******************************************************************************/
require('includes/application_top.php');
include(DIR_WS_CLASSES . 'supplier_order.php');
require(DIR_WS_CLASSES . 'supplier_payment.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'log.php');

require(DIR_WS_FUNCTIONS . 'supplier.php');

$log_object = new log_files($login_id);

define('DISPLAY_PRICE_DECIMAL', 4);

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$view_supplier_status_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'SUPPLER_INFO_VIEWING');
$view_payment_info_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'SUPPLIER_ORDER_PAYMENT_INFO');
$reversible_order_status_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'REVERSIBLE_SUPPLIER_ORDER_STATUS');
$verify_order_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'SUPPLIER_ORDER_VERIFY_STATUS');

define('PARTIAL_RECEIVE_STATUS', 1);
define('MERGING_EDIT_COLSPAN_MULTIPLE', 8);

if (isset($_SESSION['sup_order_lists_param']["criteria_id"])) {
	$back_btn_url = tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING, 'action=show_report&subaction=goto_search&criteria_id=' . $_SESSION['sup_order_lists_param']["criteria_id"]);
} else if (isset($_SESSION['sup_order_lists_param'])) {
	$back_btn_url = tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING, 'action=show_report&cont=1');
} else {
	$back_btn_url = tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING);
}

$serialized_oIDs = '';

if (isset($_REQUEST['oID'])) {
	$oID = tep_db_prepare_input($_REQUEST['oID']);
	$edit_mode = 'single';
} else {
	$multi_oID = array();
	if (isset($_REQUEST['orders_batch'])) {
		$multi_oID = $_REQUEST['orders_batch'];
	} else if (isset($_REQUEST['s_orders_batch'])) {
		$multi_oID = tep_array_unserialize(rawurldecode($_REQUEST['s_orders_batch']));
	}
	
	if (count($multi_oID) > 0) {
		// Check all these orders has the Pending status
		$non_pending_order_select_sql = "SELECT supplier_order_lists_id FROM " . TABLE_SUPPLIER_ORDER_LISTS . " WHERE supplier_order_lists_id IN ('" .implode("', '", $multi_oID) . "') AND supplier_order_lists_status<>" . (int)PARTIAL_RECEIVE_STATUS;
		$non_pending_order_result_sql = tep_db_query($non_pending_order_select_sql);
		if (tep_db_num_rows($non_pending_order_result_sql)) {
			$messageStack->add_session('Merging edit mode is not allowed since some of the selected orders is not in Pending status.', 'error');
			tep_redirect($back_btn_url);
		} else {
			$new_multi_order_array = array();
			$exists_order_select_sql = "SELECT supplier_order_lists_id FROM " . TABLE_SUPPLIER_ORDER_LISTS . " WHERE supplier_order_lists_id IN ('" .implode("', '", $multi_oID) . "') AND supplier_order_lists_status='" . (int)PARTIAL_RECEIVE_STATUS . "' ORDER BY supplier_order_lists_id";
			$exists_order_result_sql = tep_db_query($exists_order_select_sql);
			while ($exists_order_row = tep_db_fetch_array($exists_order_result_sql)) {
				$new_multi_order_array[] = $exists_order_row["supplier_order_lists_id"];
			}
			
			unset($multi_oID);
			$multi_oID = $new_multi_order_array;
		}
		
		if (count($multi_oID) > 0) {
			if (count($multi_oID) > 1) {
				$oID = $multi_oID[0];	// get the first order as currently viewed order details
				$edit_mode = 'multi';
			} else {
				$oID = (int)$multi_oID[0];
				$edit_mode = 'single';
			}
		} else {
			$oID = 0;
			$edit_mode = 'single';
		}
	} else {
		$oID = 0;
		$edit_mode = 'single';
	}
	
	$serialized_oIDs = rawurlencode(tep_array_serialize($multi_oID));
}

$orders_statuses = array();
$orders_status_array = array();
$orders_status_query = tep_db_query("SELECT supplier_list_status_id, supplier_list_status_name FROM " . TABLE_SUPPLIER_LIST_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY supplier_list_status_sort_order");
while ($orders_status = tep_db_fetch_array($orders_status_query)) {
	$orders_statuses[] = array(	'id' => $orders_status['supplier_list_status_id'],
                              	'text' => $orders_status['supplier_list_status_name']);
    $orders_status_array[$orders_status['supplier_list_status_id']] = $orders_status['supplier_list_status_name'];                       
}
$orders_statuses[] = array('id' => '0', 'text' => "--");
$orders_status_array['0'] = "--";

$order_exists = false;
$SESSION_SUPPLIER_ORDER_STATUS_B4 = '';

$current_order_status_select_sql = "SELECT supplier_order_lists_status FROM " . TABLE_SUPPLIER_ORDER_LISTS . " WHERE supplier_order_lists_id = '" . tep_db_input($oID) . "'";
$current_order_status_result_sql = tep_db_query($current_order_status_select_sql);
if ($current_order_status_row = tep_db_fetch_array($current_order_status_result_sql)) {
	$SESSION_SUPPLIER_ORDER_STATUS_B4 = $current_order_status_row['supplier_order_lists_status'];
    $order_exists = true;
} else {
	$messageStack->add(ERROR_ORDER_DOES_NOT_EXIST, 'error');
}

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');
$subaction = $_REQUEST["subaction"];

$read_only_mode = ($action == 'print') ? true : false;

if (tep_not_null($subaction)) {
	switch ($subaction) {
    	case 'update_order':
    		$informed_customers_array = array();
    		
	    	$status_cur = (int)$HTTP_POST_VARS['status_now'];
    		$admin_comment = tep_db_prepare_input($HTTP_POST_VARS["admin_comment"]);
			$new_comment_as_remark = $HTTP_POST_VARS["set_as_remark"] == 1 ? 1 : 0;
	  		$supplier_notify = (isset($HTTP_POST_VARS['notify']) && ($HTTP_POST_VARS['notify'] == '1')) ? 1 : 0;
			
			if ($HTTP_POST_VARS["status_DB_prev"] != $SESSION_SUPPLIER_ORDER_STATUS_B4) {	// hidden value passed does not match current session value
    			$messageStack->add_session(WARNING_ORDERS_NOT_UPDATED, 'warning');
    			$messageStack->add_session("This order status just update by someone!", 'warning');
    			
    			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch')) . 'action=edit' . (tep_not_null($serialized_oIDs) ? '&s_orders_batch='.$serialized_oIDs : '')) );
        		break;
    		} else {
	    		if ($status_cur > 0 && !tep_check_status_update_permission('S', $login_groups_id, $HTTP_POST_VARS["status_DB_prev"], $status_cur)) {
		    		$messageStack->add_session(ERROR_TRANS_UPDATE_DENIED, 'error');
					
					tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch')) . 'action=edit' . (tep_not_null($serialized_oIDs) ? '&s_orders_batch='.$serialized_oIDs : '')) );
					break;
		    	}
		    }
	    	
    		
        	$looping_orders = array();
			$looping_orders = ($edit_mode == 'multi') ? $multi_oID : array($oID);
			
			for ($o_cnt=0; $o_cnt < count($looping_orders); $o_cnt++) {	// each order
				$email_recv_products_list = '';
				
				$current_order_status_select_sql = "SELECT sol.suppliers_id, sol.supplier_order_lists_status, sol.products_purchases_lists_id, s.supplier_groups_id
													FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol
													INNER JOIN ". TABLE_SUPPLIER ." AS s
														ON (sol.suppliers_id=s.supplier_id)
													WHERE sol.supplier_order_lists_id = '" . tep_db_input($looping_orders[$o_cnt]) . "'";
				$current_order_status_result_sql = tep_db_query($current_order_status_select_sql);
		  		$current_order_status_row = tep_db_fetch_array($current_order_status_result_sql);
				
				if ($SESSION_SUPPLIER_ORDER_STATUS_B4 == $current_order_status_row['supplier_order_lists_status']) {
					$processing_error = false;
					
					if ($status_cur > 0) {
	          			switch($current_order_status_row['supplier_order_lists_status']) {
	          				case 1: // Previously is Pending
	          					if ($status_cur == 2) {	// Changed to Processing
	          						if (!in_array($current_order_status_row['suppliers_id'] . '_' . $current_order_status_row['products_purchases_lists_id'], $informed_customers_array)) {
	          							$informed_customers_array[] = $current_order_status_row['suppliers_id'] . '_' . $current_order_status_row['products_purchases_lists_id'];
	          							// Update supplier purchase mode to OFF
	          							$off_supplier_purchase_mode = "UPDATE " . TABLE_SUPPLIER_PURCHASE_MODES . " SET supplier_purchase_mode = 'STATUS_OFF' WHERE supplier_id = '" . tep_db_input($current_order_status_row['suppliers_id']) . "' AND products_purchases_lists_id = '" . tep_db_input($current_order_status_row['products_purchases_lists_id']) . "'";
	          							tep_db_query($off_supplier_purchase_mode);
	          						}
	          						
	          						// Updating the product average buyback price
	          						$supplier_order = new supplier_order($looping_orders[$o_cnt]);
	          						
	          						for ($prod_cnt=0; $prod_cnt < count($supplier_order->products); $prod_cnt++) {
	          							$current_buyback_info_select_sql = "SELECT products_buyback_quantity, products_buyback_price FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_db_input($supplier_order->products[$prod_cnt]['id']) . "'";
	          							$current_buyback_info_result_sql = tep_db_query($current_buyback_info_select_sql);
										if ($current_buyback_info_row = tep_db_fetch_array($current_buyback_info_result_sql)) {
											$prev_buyback_qty = (int)$current_buyback_info_row["products_buyback_quantity"];
											$prev_buyback_price = (double)$current_buyback_info_row["products_buyback_price"];
											
											$order_buyback_qty = (int)$supplier_order->products[$prod_cnt]['confirm_list']['products_received_quantity'];
											// Already take into account the $order_buyback_qty
											$total_buyback_price = (double)$supplier_order->products[$prod_cnt]['payable_amount'];
											
											$new_buyback_qty = $prev_buyback_qty + $order_buyback_qty;
											if ($new_buyback_qty > 0) {
												$new_buyback_price = ( ($prev_buyback_qty * $prev_buyback_price) + ($total_buyback_price) ) / $new_buyback_qty;
											} else {
												$new_buyback_price = 0;
											}
											
											$buyback_info_update_sql = "UPDATE " . TABLE_PRODUCTS . " 
																		SET products_buyback_quantity = '" . tep_db_input($new_buyback_qty) . "', products_buyback_price = '" . tep_db_input(round($new_buyback_price, 4)) . "' 
																		WHERE products_id = '" . tep_db_input($supplier_order->products[$prod_cnt]['id']) . "'";
											tep_db_query($buyback_info_update_sql);
										}
	          						}
	          						
	          						$email_recv_products_list = sprintf(EMAIL_RECV_PRODUCT, $supplier_order->get_products_ordered());
	          						
	          						// checking restock character share in Draft and Pending List with same supplier group and purchase list 
									$check_restock_character_select_sql = " SELECT s.supplier_id
																			FROM ". TABLE_SUPPLIER ." AS s
																			INNER JOIN ". TABLE_SUPPLIER_ORDER_LISTS ." AS sol 
																				ON (sol.suppliers_id=s.supplier_id)
																			WHERE s.supplier_groups_id = '". $current_order_status_row['supplier_groups_id'] ."'
																				AND sol.supplier_order_lists_id <> '" . tep_db_input($looping_orders[$o_cnt]) . "'
																				AND sol.products_purchases_lists_id = '" . tep_db_input($current_order_status_row['products_purchases_lists_id']) . "'
																				AND sol.supplier_order_lists_status in ('1','5')
																			LIMIT 1";
      								$check_restock_character_result_sql = tep_db_query($check_restock_character_select_sql);
      								
      								if (!tep_db_num_rows($check_restock_character_result_sql)) {
      									$update_restock_character_sql = "	UPDATE " . TABLE_SUPPLIER_PRICING . "
      																		SET supplier_pricing_show_comment=0
      																		WHERE supplier_groups_id = '". $current_order_status_row['supplier_groups_id'] ."'
      																			AND products_purchases_lists_id = '" . tep_db_input($current_order_status_row['products_purchases_lists_id']) . "'";
      									tep_db_query($update_restock_character_sql);
      								}
	          					}
	          					
	          					break;
	          				case 2:	// Previously is Processing
	          					if ($status_cur == 1) {	// Changed to Pending
	          						if ($reversible_order_status_permission) {	// This admin staff has permission on reversing order status
		          						$supplier_order = new supplier_order($looping_orders[$o_cnt]);
		          						
		          						for ($prod_cnt=0; $prod_cnt < count($supplier_order->products); $prod_cnt++) {
		          							$current_buyback_info_select_sql = "SELECT products_buyback_quantity, products_buyback_price FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . tep_db_input($supplier_order->products[$prod_cnt]['id']) . "'";
		          							$current_buyback_info_result_sql = tep_db_query($current_buyback_info_select_sql);
											if ($current_buyback_info_row = tep_db_fetch_array($current_buyback_info_result_sql)) {
												$prev_buyback_qty = (int)$current_buyback_info_row["products_buyback_quantity"];
												$prev_buyback_price = (double)$current_buyback_info_row["products_buyback_price"];
												
												$order_buyback_qty = (int)$supplier_order->products[$prod_cnt]['confirm_list']['products_received_quantity'];
												// Already take into account the $order_buyback_qty
												$total_buyback_price = (double)$supplier_order->products[$prod_cnt]['payable_amount'];
												
												$new_buyback_qty = $prev_buyback_qty - $order_buyback_qty;
												if ($new_buyback_qty > 0) {
													$new_buyback_price = ( ($prev_buyback_qty * $prev_buyback_price) - ($total_buyback_price) ) / $new_buyback_qty;
												} else {
													$new_buyback_price = 0;
												}
												
												$buyback_info_update_sql = "UPDATE " . TABLE_PRODUCTS . " 
																			SET products_buyback_quantity = '" . tep_db_input($new_buyback_qty) . "', products_buyback_price = '" . tep_db_input(round($new_buyback_price, 4)) . "' 
																			WHERE products_id = '" . tep_db_input($supplier_order->products[$prod_cnt]['id']) . "'";
												tep_db_query($buyback_info_update_sql);
											}
		          						}
		          					}
	          					} else if ($status_cur == 3) {	// Changed to Completed
	          						$cron_pending_credit_mature_period = 0;
	          						
	          						$supplier_order = new supplier_order($looping_orders[$o_cnt]);
	          						
	          						for ($prod_cnt=0; $prod_cnt < count($supplier_order->products); $prod_cnt++) {
	          							$this_prod_mature_period = tep_get_products_payment_mature_period($supplier_order->products[$prod_cnt]['id']);
	          							if ($this_prod_mature_period > $cron_pending_credit_mature_period)	$cron_pending_credit_mature_period = $this_prod_mature_period;
									}
									
									/*************************************************************************************
										Preparing data for scheduled cron job
										This step has to perform before the Supplier Order actually get updated to Completed
									*************************************************************************************/
									tep_insert_cron_pending_credit('S', $looping_orders[$o_cnt], $supplier_order->info["date_submitted"], $cron_pending_credit_mature_period, $status_cur);
									// End of preparing data for scheduled cron job
	          					}
	          					break;
	          				case 3: // Previously is Completed
	          					if ($status_cur == 2) {	// Changed to Processing
		          					$order_is_billed_select_sql = "	SELECT supplier_order_lists_billing_status 
		          													FROM " . TABLE_SUPPLIER_ORDER_LISTS . " 
		          													WHERE supplier_order_lists_id = '" . tep_db_input($looping_orders[$o_cnt]) . "'";
		          					$order_is_billed_result_sql = tep_db_query($order_is_billed_select_sql);
									$order_is_billed_row = tep_db_fetch_array($order_is_billed_result_sql);
									
									if ($order_is_billed_row['supplier_order_lists_billing_status'] == '1') {
										$processing_error = true;	// This order has been credited, no way to reverse
									} else {	// remove cronjob
										tep_remove_cron_pending_credit('S', $looping_orders[$o_cnt]);
									}
		          				}
	          					break;
	          			}
	          			
	          			if (!$processing_error) {
		          			$orders_status_update_sql_data = array(	'supplier_order_lists_status' => tep_db_input($status_cur),
		          					 								'supplier_order_lists_last_modified' => 'now()'
		          					 								);
		          			tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS, $orders_status_update_sql_data, 'update', "supplier_order_lists_id = '" . tep_db_input($looping_orders[$o_cnt]) . "'");
		          		}
	          		} else {
	          			$orders_modified_update_sql_data = array(	'supplier_order_lists_last_modified' => 'now()' );
	          			tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS, $orders_modified_update_sql_data, 'update', "supplier_order_lists_id = '" . tep_db_input($looping_orders[$o_cnt]) . "'");
	          		}
	          		
	          		if (!$processing_error) {
		          		if ($new_comment_as_remark == 1) {
		          			tep_db_query("UPDATE " . TABLE_SUPPLIER_ORDER_LISTS_HISTORY . " SET set_as_order_list_remarks=0 WHERE supplier_order_lists_id ='" . tep_db_input($looping_orders[$o_cnt]) . "'");
				  		}
				  		
				  		$supplier_order = new supplier_order($looping_orders[$o_cnt]);
				  		
				  		$order_list_history_data_array = array(	'supplier_order_lists_id' => $looping_orders[$o_cnt],
										                        'supplier_order_lists_status' => $status_cur,
										                        'date_added' => 'now()',
										                        'supplier_notified' => $supplier_notify,
										                        'comments' => $admin_comment,
										                        'set_as_order_list_remarks' => $new_comment_as_remark,
										                        'changed_by' => $login_email_address
										                       );
						tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $order_list_history_data_array);
						
						if ($supplier_notify == 1) {
							$supplier_order = new supplier_order($looping_orders[$o_cnt]);
							
							$email = EMAIL_TEXT_BODY_SUPPLIER . 
									 sprintf(EMAIL_TEXT_ORDER_NUMBER, $looping_orders[$o_cnt]) . "\n" .
									 sprintf(EMAIL_TEXT_ORDER_TITLE, $supplier_order->info["list_name"]) . "\n" .
									 sprintf(EMAIL_TEXT_DATE_ORDERED, tep_date_long($supplier_order->info["date_submitted"])) . "\n\n" .
									 sprintf(EMAIL_TEXT_STATUS_UPDATE, $status_cur > 0 ? $orders_status_array[$current_order_status_row['supplier_order_lists_status']] . ' -> ' . $orders_status_array[$status_cur] : $orders_status_array[$supplier_order->info["orders_status"]]) . "\n\n" . 
									 (tep_not_null($email_recv_products_list) ? $email_recv_products_list . "\n\n" : '') .
									 sprintf(EMAIL_TEXT_COMMENTS_UPDATE, $admin_comment) . "\n\n" . 
									 EMAIL_TEXT_CLOSING . "\n\n" . 
									 EMAIL_FOOTER;
							
							$supplier_profile_select_sql = "SELECT supplier_gender, supplier_firstname, supplier_lastname FROM " . TABLE_SUPPLIER . " WHERE supplier_id = '" . tep_db_input($supplier_order->supplier['id']) . "'";
							$supplier_profile_result_sql = tep_db_query($supplier_profile_select_sql);
							if ($supplier_profile_row = tep_db_fetch_array($supplier_profile_result_sql)) {
								$email_firstname = $supplier_profile_row["supplier_firstname"];
								$email_lastname = $supplier_profile_row["supplier_lastname"];
							} else {
								$email_firstname = $email_lastname = $supplier_order->supplier['name'];
							}
							
							$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $supplier_profile_row['supplier_gender']);
							
							$email = $email_greeting . $email;
							tep_mail($email_firstname.' '.$email_lastname, $supplier_order->supplier["email_address"], sprintf(EMAIL_TEXT_SUBJECT_SUPPLIER, $looping_orders[$o_cnt], $supplier_order->info["list_name"]), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
						}
						$messageStack->add_session(sprintf(SUCCESS_ORDER_UPDATED, $looping_orders[$o_cnt]), 'success');
						
						if ($status_cur)	tep_status_update_notification('S', $looping_orders[$o_cnt], $login_email_address, $current_order_status_row['supplier_order_lists_status'], $status_cur, 'M', $admin_comment);
					}
				} else {
					$messageStack->add_session(sprintf(WARNING_ORDER_NOT_UPDATED, $looping_orders[$o_cnt]), 'warning');
				}
			}
			
			if ($status_cur > 0) {
				tep_redirect($back_btn_url);
			} else {
				tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch')) . 'action=edit' . (tep_not_null($serialized_oIDs) ? '&s_orders_batch='.$serialized_oIDs : '')) );
			}
			
	    	break;
	    case 'partial_receive':
	    	$looping_orders = array();
			$looping_orders = ($edit_mode == 'multi') ? $multi_oID : array($oID);
			
			$non_pending_order_select_sql = "SELECT supplier_order_lists_id FROM " . TABLE_SUPPLIER_ORDER_LISTS . " WHERE supplier_order_lists_id IN ('" .implode("', '", $looping_orders) . "') AND supplier_order_lists_status<>" . (int)PARTIAL_RECEIVE_STATUS;
			$non_pending_order_result_sql = tep_db_query($non_pending_order_select_sql);
			if (tep_db_num_rows($non_pending_order_result_sql)) {
				$messageStack->add_session(sprintf(WARNING_RESTOCK_NON_PENDING_ORDERS, count($looping_orders)>1 ? 'Some orders' : 'The order' , $orders_status_array[(int)PARTIAL_RECEIVE_STATUS]), 'warning');
				tep_redirect($back_btn_url);
    			//tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch')) . 'action=edit' . (tep_not_null($serialized_oIDs) ? '&s_orders_batch='.$serialized_oIDs : '')) );
        		break;
			}
			
			$product_submission = array();
			for($o_cnt=0; $o_cnt < count($looping_orders); $o_cnt++) {
				$temp_sup_order = new supplier_order($looping_orders[$o_cnt]);
				
				for ($i=0; $i < count($temp_sup_order->products); $i++) {
					$product_submission[$temp_sup_order->products[$i]["id"]][$looping_orders[$o_cnt]] = $temp_sup_order->products[$i];
				}
			}
			
			$partial_receive_array = array();	// per each order
			if (count($HTTP_POST_VARS["partial_receive"])) {
				foreach ($HTTP_POST_VARS["partial_receive"] as $key => $qty) {
					if (tep_not_null($qty) && is_numeric($qty)) {
						list($pid, $orderid) = explode('_', $key);
						
						if (!isset($HTTP_POST_VARS["BatchReceiveBtn"]) && !isset($HTTP_POST_VARS["ReceiveBtn_" . $pid])) {
							continue;
						}
						
						$cid = tep_get_actual_product_cat_id($pid);
						if (tep_check_cat_tree_permissions(FILENAME_SUPPLIERS_ORDERS, $cid) != 1) {
							continue;
						}
						
						if (in_array($orderid, $looping_orders) && isset($product_submission[$pid][$orderid])) {	// this is the edited order.
							$selling_quantity = (int)$product_submission[$pid][$orderid]['confirm_list']['products_quantity'];
							$received_quantity = (int)$product_submission[$pid][$orderid]['confirm_list']['products_received_quantity'];
							$balance = $selling_quantity - $received_quantity;
							
							$update_qty_array = array();
							
							$product_info_select_sql = "SELECT products_quantity, products_actual_quantity, products_skip_inventory, products_cat_path 
														FROM " . TABLE_PRODUCTS . " 
														WHERE products_id = '" . tep_db_input($pid) . "'";
							$product_info_result_sql = tep_db_query($product_info_select_sql);
							$product_info_row = tep_db_fetch_array($product_info_result_sql);
							
							$qty = intval($HTTP_POST_VARS["partial_receive_sign"][$key] . $qty);
							if ($qty > 0) {	// positive value
								if ($qty <= $balance) {
									if (!$product_info_row["products_skip_inventory"]) {
										$update_qty_array = array(	array(	'field_name'=> 'products_quantity',
																			'operator'=> '+', 
																			'value'=> $qty),
																	array(	'field_name'=> 'products_actual_quantity',
																			'operator'=> '+', 
																			'value'=> $qty)
																);
									}
									
									$received_qty_update_sql = "UPDATE " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " 
																SET products_received_quantity = IF(products_received_quantity IS NULL, 0, products_received_quantity) + " . $qty . " 
																WHERE supplier_order_lists_id ='" . $orderid . "' 
																AND products_id = '" . $pid . "' 
																AND supplier_order_lists_type = 2 
																LIMIT 1";
									tep_db_query($received_qty_update_sql);
									
									$partial_receive_array[$orderid] .= "&raquo; " . tep_get_products_name($pid) . "\tx " . $qty . "\n";
								}
							} else if ($qty < 0) {	// negative receive qty => deducting previously received qty
								$deduct_qty = abs($qty);
								if ($deduct_qty <= $received_quantity) {
									if (!$product_info_row["products_skip_inventory"]) {
										$update_qty_array = array(	array(	'field_name'=> 'products_quantity',
																			'operator'=> '-', 
																			'value'=> $deduct_qty),
																	array(	'field_name'=> 'products_actual_quantity',
																			'operator'=> '-', 
																			'value'=> $deduct_qty)
																);
									}
									$received_qty_update_sql = "UPDATE " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " 
																SET products_received_quantity = IF(products_received_quantity IS NULL, 0, products_received_quantity) - " . $deduct_qty . " 
																WHERE supplier_order_lists_id ='" . $orderid . "' 
																AND products_id = '" . $pid . "' 
																AND supplier_order_lists_type = 2 
																LIMIT 1";
									tep_db_query($received_qty_update_sql);
									
									$partial_receive_array[$orderid] .= "&raquo; " . tep_get_products_name($pid) . "\tx " . $qty . "\n";
								}
							} else {	// those not submit any and do not want supplier to update the quantity anymore (if received_quantity is NULL currently)
								if (!tep_not_null($product_submission[$pid][$orderid]['confirm_list']['products_received_quantity'])) {
									$received_qty_update_sql = "UPDATE " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " 
																SET products_received_quantity = 0 
																WHERE supplier_order_lists_id ='" . $orderid . "' 
																AND products_id = '" . $pid . "' 
																AND supplier_order_lists_type = 2
																AND products_received_quantity IS NULL";
									tep_db_query($received_qty_update_sql);
									
									$partial_receive_array[$orderid] .= "&raquo; " . tep_get_products_name($pid) . "\tx " . $qty . "\n";
								}
							}
							
							if (count($update_qty_array)) {
								// This function will handle the qty adjustment and keep the log if asking so
								tep_set_product_qty($pid, $update_qty_array, true, sprintf(LOG_SUPPLIER_ORDER, $orderid), '');
							}
						}
					}
				}
			}
			
			if (count($HTTP_POST_VARS["extra_receive"])) {
				foreach ($HTTP_POST_VARS["extra_receive"] as $pid => $qty) {
					if (intval($qty) > 0) {
						if (!isset($HTTP_POST_VARS["BatchReceiveBtn"]) && !isset($HTTP_POST_VARS["ReceiveBtn_" . $pid])) {
							continue;
						}
						
						$update_qty_array = array();
						
						$product_info_select_sql = "SELECT products_quantity, products_actual_quantity, products_skip_inventory, products_cat_path 
													FROM " . TABLE_PRODUCTS . " 
													WHERE products_id = '" . tep_db_input($pid) . "'";
						$product_info_result_sql = tep_db_query($product_info_select_sql);
						$product_info_row = tep_db_fetch_array($product_info_result_sql);
						
						$qty = intval($HTTP_POST_VARS["extra_receive_sign"][$pid] . $qty);
						
						if ($qty > 0) {	// positive value
							if (!$product_info_row["products_skip_inventory"]) {
								$update_qty_array = array(	array(	'field_name'=> 'products_quantity',
																	'operator'=> '+', 
																	'value'=> $qty),
															array(	'field_name'=> 'products_actual_quantity',
																	'operator'=> '+', 
																	'value'=> $qty)
														);
							}
						} else if ($qty < 0) {	// negative receive qty => deducting previously received qty
							$deduct_qty = abs($qty);
							if (!$product_info_row["products_skip_inventory"]) {
								$update_qty_array = array(	array(	'field_name'=> 'products_quantity',
																	'operator'=> '-', 
																	'value'=> $deduct_qty),
															array(	'field_name'=> 'products_actual_quantity',
																	'operator'=> '-', 
																	'value'=> $deduct_qty)
														);
							}
						}
						
						if (count($update_qty_array)) {
							// This function will handle the qty adjustment and keep the log if asking so
							tep_set_product_qty($pid, $update_qty_array, true, sprintf(LOG_QTY_ADJUST, ''), 'Extra product quantity entered from Supplier Edit Order page.');
							
							$messageStack->add_session(sprintf(SUCCESS_EXTRA_QUANTITY_UPDATED, $qty, $pid), 'success');
						}
					}
				}
			}
			
			if (count($partial_receive_array)) {
				foreach ($partial_receive_array as $orderid => $receive_str) {
					while(substr($receive_str, -1) == "\n") {
						$receive_str = substr($receive_str, 0, -1);
					}
					$receive_str = strip_tags($receive_str);
					
					if (tep_not_null($receive_str)) {
						$receive_str = "The following items have been received:" . "\n" . $receive_str;
						
						$order_list_history_data_array = array(	'supplier_order_lists_id' => $orderid,
										                        'supplier_order_lists_status' => '0',
										                        'date_added' => 'now()',
										                        'supplier_notified' => '0',
										                        'comments' => $receive_str,
										                        'changed_by' => $login_email_address
										                       );
						tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $order_list_history_data_array);
					}
				}
			}
			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch', 'commentID')) . 'action=edit' . (tep_not_null($serialized_oIDs) ? '&s_orders_batch='.$serialized_oIDs : '')) );
			
			break;
		case 'set_as_remark':
			if (isset($_REQUEST["commentID"]) && tep_not_null($_REQUEST["commentID"])) {
				// Cater for merging edit case
				$order_id_select_sql = "SELECT supplier_order_lists_id FROM " . TABLE_SUPPLIER_ORDER_LISTS_HISTORY . " WHERE supplier_order_lists_history_id = '" . tep_db_input($_REQUEST["commentID"]) . "'";
				$order_id_result_sql = tep_db_query($order_id_select_sql);
				if ($order_id_row = tep_db_fetch_array($order_id_result_sql)) {
					tep_db_query("UPDATE " . TABLE_SUPPLIER_ORDER_LISTS_HISTORY . " SET set_as_order_list_remarks=0 WHERE supplier_order_lists_id ='" . $order_id_row["supplier_order_lists_id"] . "'");
					tep_db_query("UPDATE " . TABLE_SUPPLIER_ORDER_LISTS_HISTORY . " SET set_as_order_list_remarks=1 WHERE supplier_order_lists_history_id ='" . tep_db_input($_REQUEST["commentID"]) . "'");
				}
			}
			
			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch', 'commentID')) . 'action=edit' . (tep_not_null($serialized_oIDs) ? '&s_orders_batch='.$serialized_oIDs : '')) );
			
			break;
		case 'change_order':
			$oID = (int)$_REQUEST["oID"];
			
			$allow_to_access_order = false;
			$admin_file_cat_ids_array = tep_get_admin_file_cat_permissions(FILENAME_SUPPLIERS_ORDERS);
	  		if (!in_array(0, $admin_file_cat_ids_array)) {
	  			$admin_permitted_cat_array = tep_get_eligible_categories(FILENAME_SUPPLIERS_ORDERS, '', 0);
	  			
	  			$order_select_sql = "	SELECT sol.supplier_order_lists_id 
										FROM " . TABLE_SUPPLIER_ORDER_LISTS . " AS sol 
										LEFT JOIN " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
											ON ( sol.supplier_order_lists_id = solp.supplier_order_lists_id AND ( solp.supplier_order_lists_type =1 OR solp.supplier_order_lists_type =2 ) )
										LEFT JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc on (solp.products_id=pc.products_id and pc.products_is_link=0) 
										WHERE sol.supplier_order_lists_id = '" . tep_db_input($oID) . "'
											AND pc.categories_id IN ('" . implode("', '", $admin_permitted_cat_array) . "') ";
				$order_result_sql = tep_db_query($order_select_sql);
				if (tep_db_num_rows($order_result_sql)) {
					$allow_to_access_order = true;
				}
	  		} else {
	  			$allow_to_access_order = true;
	  		}
			
			if ($allow_to_access_order) {
				tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS, 'oID='.$oID.'&action=edit'));
			} else {
				$messageStack->add_session(ERROR_ORDER_DOES_NOT_EXIST, 'error');
				tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS_TRACKING));
			}
			
			break;
		case "verify_order":
			$oID = (int)$_REQUEST["oID"];
			
			$orders_verify_update_sql_data = array(	'supplier_orders_verify_mode' => (int)$_REQUEST["v_mode"],
  					 								'supplier_order_lists_last_modified' => 'now()'
  					 								);
  			tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS, $orders_verify_update_sql_data, 'update', "supplier_order_lists_id = '" . tep_db_input($oID) . "' AND supplier_order_lists_status=3");
  			
  			$order_list_history_data_array = array(	'supplier_order_lists_id' => $oID,
							                        'supplier_order_lists_status' => 0,
							                        'date_added' => 'now()',
							                        'supplier_notified' => 0,
							                        'comments' => ((int)$_REQUEST["v_mode"] == '1' ? 'Mark order as verified' : 'Mark order as unverify'),
							                        'changed_by' => $login_email_address
							                       );
			tep_db_perform(TABLE_SUPPLIER_ORDER_LISTS_HISTORY, $order_list_history_data_array);
			
  			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch', 'v_mode')) . 'action=edit' . (tep_not_null($serialized_oIDs) ? '&s_orders_batch='.$serialized_oIDs : '')) );
			
			break;
	}
}

$saved_search_options = array( array ('id' => '', "text" => "Saved Criteria >>>>>") );
$search_criteria_select_sql = "SELECT search_criteria_id, search_criteria_name FROM " . TABLE_SEARCH_CRITERIA . " WHERE filename='" . tep_db_input(FILENAME_SUPPLIERS_ORDERS_TRACKING) . "' ORDER BY search_criteria_name";
$search_criteria_result_sql = tep_db_query($search_criteria_select_sql);
while ($search_criteria_row = tep_db_fetch_array($search_criteria_result_sql)) {
	$saved_search_options[] = array ('id' => $search_criteria_row["search_criteria_id"], "text" => $search_criteria_row["search_criteria_name"]);
}

$server_status_array = array(	'urgent' => array('name' => 'icon_status_urgent'), 
								'important' => array('name' => 'icon_status_important'), 
								'normal' => array('name' => 'icon_status_normal')
							);

// Get the back order tag id
if ($SESSION_SUPPLIER_ORDER_STATUS_B4 == PARTIAL_RECEIVE_STATUS) {
	$back_order_tag_id = '';
	$back_order_status_array = array();
	if (tep_not_null(BACK_ORDER_TAG_NAME)) {
		$order_tag_select_sql = "SELECT orders_tag_id, orders_tag_status_ids FROM " . TABLE_ORDERS_TAG . " WHERE LOWER(orders_tag_name)='".strtolower(BACK_ORDER_TAG_NAME)."' LIMIT 1 ;";
		$order_tag_result_sql = tep_db_query($order_tag_select_sql);
		if ($order_tag_row = tep_db_fetch_array($order_tag_result_sql)) {
			$back_order_tag_id = $order_tag_row["orders_tag_id"];
			if (tep_not_null($order_tag_row["orders_tag_status_ids"])) {
				$back_order_status_array = explode(',', $order_tag_row["orders_tag_status_ids"]);
				$valid_back_order_status_array = array_intersect(array(7, 2), $back_order_status_array);
				$back_order_status_array = array();
				if (count($valid_back_order_status_array)) {
					foreach($valid_back_order_status_array as $bo_status) {
						$back_order_status_array[] = $bo_status;
					}
				}
			}
		}
	}
	
	$received_qty_adjust_array = array( array('id'=>"+", 'text'=>"+"),
										array('id'=>"-", 'text'=>"-")
									);
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/print.css" media="print">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript">
	<!--
		function submitForm(val) {
			if (val==0) {
				document.status_update_form.OrderUpdateBtn.disabled = true;
				document.status_update_form.OrderUpdateBtn.value = 'Please wait...';
				document.status_update_form.submit();
			}
		}
		
		function status_check(obj,total_received) {
			if (obj.value == 2) {
				if (total_received>0) {
					answer = confirm('All the products for this buyback order are not fully received. Are you sure you want to change the status to \'Processing\'?');
					
					if (answer !=0 ) {
						;
					} else {
						obj.selectedIndex=0;
					}
				} else {
					alert("You cannot change the status of this buyback order to 'Processing' because no product is received.");
					obj.selectedIndex=0;
				}
			}
		}
	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? if (!$read_only_mode)	require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">	
  		<tr>
<?
if (!$read_only_mode) {
?>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
<?
}
?>
			<td valign="top">
				<table width="100%"  border="0" cellspacing="0" cellpadding="3">
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
									<td class="pageHeading"><?=sprintf(HEADING_TITLE, $edit_mode == 'multi' ? '(Merging Edit)' : '')?></td>
									<td class="smallText" align="right" valign="top">&nbsp;
									<?
									if (!$read_only_mode) {
										echo tep_draw_form('orders', FILENAME_SUPPLIERS_ORDERS, '', 'post');
										echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('oID', '', 'size="12"') . tep_draw_hidden_field('subaction', 'change_order');
										echo "</form><br>";
										if (count($saved_search_options) > 1) {
											echo tep_draw_form('goto_search_form', FILENAME_SUPPLIERS_ORDERS_TRACKING, 'action=show_report', 'post');
											echo tep_draw_hidden_field('subaction', 'goto_search');
											echo tep_draw_pull_down_menu("criteria_id", $saved_search_options, tep_not_null($_SESSION['sup_order_lists_param']["criteria_id"]) ? $_SESSION['sup_order_lists_param']["criteria_id"] : '', 'onChange="if(this.value != \'\') { this.form.submit(); }"');
											echo "</form>";
										}
									}
									?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
<?
if ($order_exists == true) {
	$supplier_order = new supplier_order($oID);
	
	if ($edit_mode == 'multi') {
		$supplier_info_section = array();
		for($o_cnt=0; $o_cnt < count($multi_oID); $o_cnt++) {
			$temp_sup_order = new supplier_order($multi_oID[$o_cnt]);
			
			$supplier_info_section['oid'] .= '<td class="main"><b>'.$multi_oID[$o_cnt].'</b></td>';
			$supplier_info_section['o_status'] .= '<td class="main">'.$orders_status_array[$temp_sup_order->info["orders_status"]].'</td>';
			$supplier_info_section['o_list_name'] .= '<td class="main">'.$temp_sup_order->info["list_name"].'</td>';
			$supplier_info_section['o_date'] .= '<td class="main" nowrap>'.tep_datetime_short($temp_sup_order->info["date_submitted"], PREFERRED_DATE_TIME_FORMAT).'</td>';
			$supplier_info_section['o_modified'] .= '<td class="main" nowrap>'.tep_datetime_short($temp_sup_order->info["last_modified"], PREFERRED_DATE_TIME_FORMAT).'</td>';
			
			$supplier_info_section['s_name'] .= '<td class="main" valign="top" nowrap>';
            $sup_grp_str = '['.$temp_sup_order->supplier["supplier_group"].']' . TEXT_REAL_TIME_STAT;
            $supplier_info_section['s_name'] .= tep_output_string_protected($temp_sup_order->supplier['name']) . '&nbsp;'.$sup_grp_str;
            if ($view_supplier_status_permission) {
            	$supplier_info_section['s_name'] .= '<br>' . tep_address_format($temp_sup_order->supplier['format_id'], $temp_sup_order->supplier, 1, '', '<br>', '', false);
            }
            $supplier_info_section['s_name'] .= '</td>';
            
            $supplier_info_section['s_code'] .= '<td class="main">'.$temp_sup_order->supplier["code"].'</td>';
            
            $supplier_info_section['s_acc_date'] .= '<td class="main">'.tep_date_short($temp_sup_order->supplier["date_account_created"], PREFERRED_DATE_FORMAT);
            if (tep_not_null($temp_sup_order->supplier["date_account_created"]))
				$supplier_info_section['s_acc_date'] .= '&nbsp;(' . tep_calculate_age($temp_sup_order->supplier["date_account_created"], '', 0) . ')';
			$supplier_info_section['s_acc_date'] .= '</td>';
			
			$supplier_info_section['s_gender'] .= '<td class="main">'.($temp_sup_order->supplier["gender"]=='m' ? 'Male' : ($temp_sup_order->supplier["gender"]=='f' ? 'Female' : '&nbsp;')).'</td>';
			
			$supplier_info_section['s_dob'] .= '<td class="main" nowrap>'.tep_date_short($temp_sup_order->supplier["dob"], PREFERRED_DATE_FORMAT);
			if (tep_not_null($temp_sup_order->supplier["dob"])) {
				$supplier_age = tep_calculate_age($temp_sup_order->supplier["dob"], '', 0);
				$supplier_info_section['s_dob'] .= '&nbsp;(' . $supplier_age . ')';
			}
            $supplier_info_section['s_dob'] .= '</td>';
            
            $supplier_info_section['s_tel'] .= '<td class="main">'.$temp_sup_order->supplier['telephone'].'</td>';
            
            $supplier_info_section['s_fax'] .= '<td class="main">'.$temp_sup_order->supplier['fax'].'</td>';
            
            $supplier_info_section['s_email'] .= '<td class="main" nowrap><a href="mailto:' . $temp_sup_order->supplier['email_address'] . '"><u>' . $temp_sup_order->supplier['email_address'] . '</u></a></td>';
            
            $supplier_info_section['s_ip'] .= '<td class="main">'.tep_show_ip($temp_sup_order->info['remote_addr'], '<u>'.$temp_sup_order->info['remote_addr'].'</u>').'</td>';
		}
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
        						<tr>
                					<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_SUPPLIERS_INFO?></b></td>
              					</tr>
        						<tr>
            						<td valign="top">
            							<table border="0" width="1" cellspacing="1" cellpadding="3">
											<tr>
												<td class="main" width="15%" nowrap><b><?=ENTRY_SUPPLIER_ORDER_ID?></b></td>
												<?=$supplier_info_section['oid']?>
											</tr>
											<tr>
												<td class="main" nowrap><b><?=ENTRY_ORDER_STATUS?></b></td>
												<?=$supplier_info_section['o_status']?>
											</tr>
											<tr>
												<td class="main" nowrap><b><?=ENTRY_ORDER_LIST_NAME?></b></td>
												<?=$supplier_info_section['o_list_name']?>
											</tr>
											<tr>
												<td class="main" nowrap><b><?=ENTRY_ORDER_DATE_TIME?></b></td>
												<?=$supplier_info_section['o_date']?>
											</tr>
											<tr>
												<td class="main" nowrap><b><?=ENTRY_DATE_LAST_MODIFIED?></b></td>
												<?=$supplier_info_section['o_modified']?>
											</tr>
											<tr>
												<td class="main" valign="top" nowrap><b><?=ENTRY_SUPPLIER?></b></td>
                								<?=$supplier_info_section['s_name']?>
                							</tr>
<?		if ($view_supplier_status_permission) { ?>
                							<tr>
                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_CODE?></b><?=TEXT_REAL_TIME_STAT?></td>
                								<?=$supplier_info_section['s_code']?>
              								</tr>
              								<tr>
                								<td width="15%" class="main" valign="top"><b><?=ENTRY_SUPPLIER_SIGNUP_DATE?></b><?=TEXT_REAL_TIME_STAT?></td>
                								<?=$supplier_info_section['s_acc_date']?>
              								</tr>
              								<tr>
                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_GENDER?></b><?=TEXT_REAL_TIME_STAT?></td>
                								<?=$supplier_info_section['s_gender']?>
              								</tr>
              								<tr>
                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_DOB?></b><?=TEXT_REAL_TIME_STAT?></td>
                								<?=$supplier_info_section['s_dob']?>
              								</tr>
              								<tr>
                								<td class="main"><b><?=ENTRY_TELEPHONE_NUMBER?></b></td>
                								<?=$supplier_info_section['s_tel']?>
              								</tr>
              								<tr>
								                <td class="main"><b><?=ENTRY_FAX_NUMBER?></b><?=TEXT_REAL_TIME_STAT?></td>
								                <?=$supplier_info_section['s_fax']?>
              								</tr>
              								<tr>
				                				<td class="main"><b><?=ENTRY_EMAIL_ADDRESS?></b></td>
				                				<?=$supplier_info_section['s_email']?>
              								</tr>
              								<tr>
				                				<td class="main" valign="top"><b><?=ENTRY_ORDERING_IP?></b></td>
				                				<?=$supplier_info_section['s_ip']?>
				                			</tr>
<?		} ?>
										</table>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
<?	} else { ?>
      				<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
        						<tr>
            						<td valign="top" width="75%">
            							<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td class="main" width="15%"><b><?=ENTRY_SUPPLIER_ORDER_ID?></b></td>
												<td class="main"><b><?=$oID . ($SESSION_SUPPLIER_ORDER_STATUS_B4 == 3 ? '&nbsp;' . sprintf(TEXT_SUPPLIER_ORDER_VERIFIED_STATUS, ($supplier_order->info['verified_status'] ? TEXT_SUPPLIER_ORDER_VERIFIED : TEXT_SUPPLIER_ORDER_NOT_VERIFIED)) : '')?></b></td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_ORDER_STATUS?></b></td>
												<td class="main"><?=$orders_status_array[$SESSION_SUPPLIER_ORDER_STATUS_B4]?></td>
											</tr>
											<tr>
												<td class="main" nowrap><b><?=ENTRY_ORDER_LIST_NAME?></b></td>
												<td class="main"><?=$supplier_order->info["list_name"]?></td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_ORDER_DATE_TIME?></b></td>
												<td class="main"><?=tep_datetime_short($supplier_order->info["date_submitted"], PREFERRED_DATE_TIME_FORMAT)?></td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_DATE_LAST_MODIFIED?></b></td>
												<td class="main"><?=tep_datetime_short($supplier_order->info["last_modified"], PREFERRED_DATE_TIME_FORMAT)?></td>
											</tr>
										</table>
									</td>
									<td align="right" valign="top" nowrap>
<?
										if ($verify_order_permission) {
											if (!$read_only_mode && $SESSION_SUPPLIER_ORDER_STATUS_B4 == 3) {
												echo tep_draw_form('supplier_order_verify_form', FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('subaction')) . 'subaction=verify_order&v_mode='.($supplier_order->info['verified_status'] ? '0' : '1'), 'POST', '');
												
												if ($supplier_order->info['verified_status']) {
													echo tep_submit_button('Mark as Unverified', 'Mark as Unverify', '', 'inputButton');
												} else {
													echo tep_submit_button('Mark as Verified', 'Mark as Verified', '', 'inputButton');
												}
									  			echo '</form>';
											}
											
											echo '&nbsp;';
										}
										
										if (!$read_only_mode && ($SESSION_SUPPLIER_ORDER_STATUS_B4 == 2 || $SESSION_SUPPLIER_ORDER_STATUS_B4 == 3) ) {
											echo tep_draw_form('supplier_order_print_form', FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('action')) . 'action=print', 'POST', 'target="_blank"');
											echo tep_submit_button(BUTTON_PRINT_VERSION, ALT_BUTTON_PRINT_VERSION, '', 'inputButton');
								  			echo '</form>';
										}
?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<!--	Start of Supplier Information Section	-->
      				<tr>
        				<td>
        					<table width="100%" border="0" cellspacing="0" cellpadding="2">
								<tr>
            						<td><?=tep_draw_separator()?></td>
          						</tr>
          						<tr>
                					<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_SUPPLIERS_INFO?></b></td>
              					</tr>
              					<tr>
            						<td valign="top">
            							<table width="100%" border="0" cellspacing="0" cellpadding="2">
            								<tr>
            									<td valign="top">
            										<table width="100%" border="0" cellspacing="0" cellpadding="2">
			              								<tr>
			              									<td width="15%" class="main" valign="top" nowrap><b><?=ENTRY_SUPPLIER?></b></td>
                											<td class="main">
                											<?
                												$sup_grp_str = '['.$supplier_order->supplier["supplier_group"].']' . TEXT_REAL_TIME_STAT;
                												echo tep_output_string_protected($supplier_order->supplier['name']) . '&nbsp;'.$sup_grp_str;
                												if ($view_supplier_status_permission) {
                													echo '<br>' . tep_address_format($supplier_order->supplier['format_id'], $supplier_order->supplier, 1, '', '<br>', '', false);
                												}
                											?>
                											</td>
			              								</tr>
			              							</table>
			              						</td>
			              					</tr>
<?		if ($view_supplier_status_permission) { ?>
			              					<tr>
            									<td valign="top">
            										<table width="100%" border="0" cellspacing="0" cellpadding="2">
            											<tr>
			                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_CODE?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_order->supplier["code"]?></td>
			              								</tr>
			              								<tr>
			                								<td width="15%" class="main" valign="top"><b><?=ENTRY_SUPPLIER_SIGNUP_DATE?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main">
			                								<?
			                									echo tep_date_short($supplier_order->supplier["date_account_created"], PREFERRED_DATE_FORMAT);
			                									if (tep_not_null($supplier_order->supplier["date_account_created"]))
																	echo '&nbsp;(' . tep_calculate_age($supplier_order->supplier["date_account_created"], '', 0) . ')';
			                								?>
			                								</td>
			              								</tr>
			              								<tr>
			                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_GENDER?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=($supplier_order->supplier["gender"]=='m' ? 'Male' : ($supplier_order->supplier["gender"]=='f' ? 'Female' : '&nbsp;'))?></td>
			              								</tr>
			              								<tr>
			                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_DOB?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main">
			                								<?
			                									if (tep_not_null($supplier_order->supplier["dob"])) {
			                										$supplier_age = tep_calculate_age($supplier_order->supplier["dob"], '', 0);
			                										echo tep_date_short($supplier_order->supplier["dob"], PREFERRED_DATE_FORMAT);
																	echo '&nbsp;(' . $supplier_age . ')';
																}
			                								?>
			                								</td>
			              								</tr>
			              								<tr>
			                								<td class="main"><b><?=ENTRY_TELEPHONE_NUMBER?></b></td>
			                								<td class="main"><?=$supplier_order->supplier['telephone']?></td>
			              								</tr>
			              								<tr>
											                <td class="main"><b><?=ENTRY_FAX_NUMBER?></b><?=TEXT_REAL_TIME_STAT?></td>
											                <td class="main"><?=$supplier_order->supplier['fax']?></td>
			              								</tr>
			              								<tr>
							                				<td class="main"><b><?=ENTRY_EMAIL_ADDRESS?></b></td>
							                				<td class="main"><? echo '<a href="mailto:' . $supplier_order->supplier['email_address'] . '">' . $supplier_order->supplier['email_address'] . '</a>'; ?></td>
			              								</tr>
			              								<tr>
			                								<td class="main"><b><?=ENTRY_QQ_NUMBER?></b></td>
			                								<td class="main"><?=(($supplier_order->supplier['qq']) ? $supplier_order->supplier['qq'] : TEXT_NOT_AVAILABLE)?></td>
			              								</tr>
			              								<tr>
			                								<td class="main"><b><?=ENTRY_MSN_ADDRESS?></b></td>
			                								<td class="main"><?=(($supplier_order->supplier['msn']) ? $supplier_order->supplier['msn'] : TEXT_NOT_AVAILABLE)?></td>
			              								</tr>
			              								<tr>
							                				<td class="main" valign="top"><b><?=ENTRY_ORDERING_IP?></b></td>
							                				<td class="main"><?=tep_show_ip($supplier_order->info['remote_addr'])?></td>
							                			</tr>
			            							</table>
			            						</td>
			            					</tr>
<?		} ?>
			            				</table>
			            			</td>
			            		</tr>
          					</table>
          				</td>
          			</tr>
<?	} ?>
<!--	End of Supplier Information Section	-->
<!--	Start of Supplier Payment Information Section	-->
<?	if ($SESSION_SUPPLIER_ORDER_STATUS_B4 != PARTIAL_RECEIVE_STATUS && $view_payment_info_permission) { ?>
      				<tr>
        				<td>
        					<table width="100%" border="0" cellspacing="0" cellpadding="2">
        						<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
            						<td><?=tep_draw_separator()?></td>
          						</tr>
          						<tr>
                					<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_PAYMENT_INFO?></b></td>
              					</tr>
              					<tr>
            						<td valign="top">
            							<table width="100%" border="0" cellspacing="0" cellpadding="2">
			              					<tr>
            									<td valign="top">
            										<table width="100%" border="0" cellspacing="0" cellpadding="2">
            											<tr>
			                								<td width="20%" class="main" valign="top"><b><?=ENTRY_SUPPLIER_PAYMENT_PAYPAL?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_order->payment["paypal_email"]?></td>
			              								</tr>
			              								<tr>
			                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_NAME?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_order->payment["bank_name"]?></td>
			              								</tr>
			              								<tr>
							                				<td class="main"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE?></b><?=TEXT_REAL_TIME_STAT?></td>
							                				<td class="main"><?=$supplier_order->payment["bank_swift_code"]?></td>
			              								</tr>
			              								<tr>
			                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_order->payment["bank_address"]?></td>
			              								</tr>
			              								<tr>
			                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_order->payment["bank_telephone"]?></td>
			              								</tr>
			              								<tr>
			                								<td class="main"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_order->payment['bank_account_name']?></td>
			              								</tr>
			              								<tr>
											                <td class="main"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER?></b><?=TEXT_REAL_TIME_STAT?></td>
											                <td class="main"><?=$supplier_order->payment['bank_account_number']?></td>
			              								</tr>
			            							</table>
			            						</td>
			            					</tr>
			            				</table>
			            			</td>
			            		</tr>
          					</table>
          				</td>
          			</tr>
<?	} ?>
<!--	End of Supplier Payment Information Section	-->
					<tr>
  						<td class="main"><?=TEXT_REAL_TIME_STAT_DESC?></td>
  					</tr>
          			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
            			<td><?=tep_draw_separator()?></td>
          			</tr>
          			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
      				</tr>
      				<tr>
	  	    			<td align="right">
	  	    				<div id="show_product_name"><a href="javascript:;" onclick="show_hide_product_name(true)"><?=TEXT_SHOW_PRODUCT_NAME?></a></div>
	  	    			</td>
	  	    		</tr>
				  	<tr>
				  		<td>
<?
	// check whether this order need to show purchase status or not
	$show_purchase_status = false;
	
	$product_submission = array();
	$looping_orders = array();
	$temp_sorting_array = array();
	
	if ($edit_mode == 'multi') {
		for($o_cnt=0; $o_cnt < count($multi_oID); $o_cnt++) {
			$temp_sup_order = new supplier_order($multi_oID[$o_cnt]);
			
			for ($i=0; $i < count($temp_sup_order->products); $i++) {
				if (!in_array($temp_sup_order->products[$i]["id"], array_keys($temp_sorting_array))) {
					$temp_sorting_array[$temp_sup_order->products[$i]["id"]] = $temp_sup_order->products[$i]["sort_str"];
				}
				$product_submission[$temp_sup_order->products[$i]["id"]][$multi_oID[$o_cnt]] = $temp_sup_order->products[$i];
				
				if (is_array($temp_sup_order->products[$i]['confirm_list']) && count($temp_sup_order->products[$i]['confirm_list']) > 0) {
					$list_ref = 'confirm_list';
				} else {
					$list_ref = 'first_list';
				}
				if (tep_not_null($temp_sup_order->products[$i][$list_ref]['products_purchase_status'])) {
					$show_purchase_status = true;
				}
			}
		}
		$looping_orders = $multi_oID;
		$looping_order_status = PARTIAL_RECEIVE_STATUS;
		
		if ($_SESSION['sup_order_lists_param']["sort_order"] == 'DESC') {
			arsort($temp_sorting_array);
		} else {
			asort($temp_sorting_array);
		}
		
		reset($temp_sorting_array);
	} else {
		for ($i=0; $i < count($supplier_order->products); $i++) {
			if (!in_array($supplier_order->products[$i]["id"], array_keys($temp_sorting_array))) {
				$temp_sorting_array[$supplier_order->products[$i]["id"]] = $supplier_order->products[$i]["sort_str"];
			}
			
			$product_submission[$supplier_order->products[$i]["id"]][$oID] = $supplier_order->products[$i];
			
			if (is_array($supplier_order->products[$i]['confirm_list']) && count($supplier_order->products[$i]['confirm_list']) > 0) {
				$list_ref = 'confirm_list';
			} else {
				$list_ref = 'first_list';
			}
			if (tep_not_null($supplier_order->products[$i][$list_ref]['products_purchase_status'])) {
				$show_purchase_status = true;
			}
		}
		$looping_orders = array($oID);
		$looping_order_status = $supplier_order->info['orders_status'];
		
		reset($temp_sorting_array);
	}
	
	echo tep_draw_form('partial_receive_form', FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('subaction')), 'post', '');
	echo tep_draw_hidden_field('subaction', 'partial_receive');
	echo tep_draw_hidden_field('s_orders_batch', $serialized_oIDs);
	
	$rowspan = 1;
	$total_colspan = 7;
	$merging_multiple_offset = 0;
	if ($show_purchase_status) {
		$total_colspan ++;
	} else {
		$merging_multiple_offset--;
	}
	
	if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
		$total_colspan += 7;
	} else {
		$total_colspan++;	// For suggested qty
		if ($view_payment_info_permission)	$total_colspan += 3;
	}
	
	$total_merging_colspan_multiple = MERGING_EDIT_COLSPAN_MULTIPLE + $merging_multiple_offset;
	
	if ($edit_mode == 'multi') {
		$normal_width = 'width="40%"';
		$total_colspan += (count($multi_oID) - 1) * ($total_merging_colspan_multiple + 1);
		$rowspan = 2;
	}
?>
				  	    	<table width="100%" border="0" cellspacing="1" cellpadding="2" class="printViewTable">
						  		<tr>
								  	<td <?=$normal_width?> class="ordersBoxHeading" rowspan="<?=$rowspan?>" nowrap><?=TABLE_HEADING_PRODUCT_NAME?></td>
<?	if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
		echo '					  	<td width="5%" align="center" class="ordersBoxHeading" rowspan="'.$rowspan.'">'.TABLE_HEADING_BACK_ORDER.'</td>';
		echo '					  	<td width="5%" align="center" class="ordersBoxHeading" rowspan="'.$rowspan.'">'.TABLE_HEADING_NORMAL_ORDER.'</td>';
		echo '					  	<td '.$normal_width.' class="ordersBoxHeading" rowspan="'.$rowspan.'">'.TABLE_HEADING_PRODUCT_LOCATION.'</td>';
	}
?>
									<td width="5%" align="center" class="ordersBoxHeading" rowspan="<?=$rowspan?>"><?=TABLE_HEADING_PRODUCT_ACTUAL_QTY?></td>
<?	if ($edit_mode == 'multi') {
		for ($o_cnt=0; $o_cnt < count($multi_oID); $o_cnt++) {
			if ($o_cnt > 0) {
				echo '			<td align="center" valign="top" class="cell_separator"></td>';
			}
			echo '<td align="center" class="ordersBoxHeading" colspan="'.$total_merging_colspan_multiple.'">#'.$multi_oID[$o_cnt].'</td>';
		}
		
		if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
			echo '<td width="5%" align="center" class="ordersBoxHeading" rowspan="'.$rowspan.'">'.TABLE_HEADING_EXTRA_QTY.'</td>';
			echo '<td width="5%" align="center" class="ordersBoxHeading" rowspan="'.$rowspan.'">'.TABLE_HEADING_ACTION.'</td>';
		}
		
		echo '					</tr>
								<tr>';
	}
	
	$o_cnt=0;
	do {
		if ($o_cnt > 0) {
			echo '			<td align="center" valign="top" class="cell_separator"></td>';
		}
		
		if ($show_purchase_status) {
			echo '  				<td width="2%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_PRODUCT_DEMAND_STATUS.'</td>';
		}
		
		if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
			echo '					<td width="8%" align="center" class="ordersBoxHeading" nowrap>'.TABLE_HEADING_PRODUCT_ADMIN_COMMENT.'</td>';
		}
?>
									<td width="10%" class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_SUPPLIER_COMMENT?></td>
									<td width="5%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_MIN_QTY?></td>
<?		if ($supplier_order->info['orders_status'] != PARTIAL_RECEIVE_STATUS) {
			if ($view_payment_info_permission) {
				echo '					<td width="6%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_MAX_QTY.'</td>';
				echo '					<td width="6%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_UNIT_PRICE.'</td>';
				//echo '				<td width="6%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_OVER_LIMIT_MAX_QTY.'</td>';
				//echo '				<td width="6%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_UNIT_OVER_LIMIT_PRICE.'</td>';
			}
			
			echo '						<td width="5%" align="center" class="ordersBoxHeading">'.TABLE_HEADING_SUGGESTED_SELLING_QUANTITY.'</td>';
		}
?>
								  	<td width="5%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_SELLING_QUANTITY?></td>
								  	<td width="5%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_PREVIOUS_RECEIVED?></td>
<?		if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) { ?>
									<td width="5%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_BALANCE?></td>
									<td width="5%" align="center" class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_RECEIVE?></td>
<?		}
		
		if ($supplier_order->info['orders_status'] != PARTIAL_RECEIVE_STATUS && $view_payment_info_permission) {
?>
								  	<td width="8%" align="right" class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT_PAYABLE_AMOUNT?></td>
<?		}
		$o_cnt++;
	} while ($o_cnt < count($multi_oID));
	
	if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS && $edit_mode != 'multi') {
		echo '<td width="5%" align="center" class="ordersBoxHeading" rowspan="'.$rowspan.'">'.TABLE_HEADING_EXTRA_QTY.'</td>';
		echo '<td width="5%" align="center" class="ordersBoxHeading" rowspan="'.$rowspan.'">'.TABLE_HEADING_ACTION.'</td>';
	}
?>
						  		</tr>
<?
	$payable_total = 0;
	$partial_inputs = array();
	
	$not_received_any = true;
	$confirm_list_exist = false;
	
	$total_received = 0;
	
	$i=0;
	//print_r($product_submission);
	foreach ($temp_sorting_array as $this_pid => $sort_str) {
		$this_order_res = $product_submission[$this_pid];
		
		$this_product_need_restock = false;
		
		$unique_row_reference = $this_pid;
		
		$row_style = ($i%2) ? 'invoiceListingEven' : 'invoiceListingOdd';
		
		$product_info_select_sql = "SELECT p.products_id, p.products_actual_quantity, p.products_quantity, p2c.categories_id 
									FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
									INNER JOIN " . TABLE_PRODUCTS . " AS p 
										ON p2c.products_id=p.products_id 
									WHERE p.products_id = '" . $this_pid . "' AND p2c.products_is_link=0 ";
		$product_info_result_sql = tep_db_query($product_info_select_sql);
		$product_info_row = tep_db_fetch_array($product_info_result_sql);
		
		$this_cat_id = $product_info_row['categories_id'];
		$full_cat_permissions = (tep_check_cat_tree_permissions(FILENAME_SUPPLIERS_ORDERS, $this_cat_id) == 1) ? true : false;
			
		if (tep_not_null($product_info_row['products_id'])) {
			$prod_loc_result_sql = tep_db_query("SELECT products_name, products_location FROM " . TABLE_PRODUCTS_DESCRIPTION . " WHERE products_id =". $product_info_row['products_id'] . " AND language_id = '" . (int)$languages_id . "'");
			if ($prod_loc_row = tep_db_fetch_array($prod_loc_result_sql)) {
				$prod_loc = $prod_loc_row['products_location'];
			}
		}
?>
								<tr class="<?=$row_style?>" id="<?=$unique_row_reference?>" onMouseOver="showOverEffect('invoiceListingRowOver', '<?=$unique_row_reference?>')" onMouseOut="showOutEffect('<?=$row_style?>', '<?=$unique_row_reference?>')" onClick="showClicked('<?=$row_style?>', '<?=$unique_row_reference?>')">
									<td valign="top" class="ordersRecords">
									<?
										if (tep_not_null($product_info_row['products_id'])) {
											$prod_maincatpath = tep_output_generated_category_path_sq($product_info_row['categories_id']);
											if (tep_not_null($prod_maincatpath)) {
												echo "<span class='categoryPath'>".strip_tags($prod_maincatpath)."</span>";
											}
											echo '<span id="span_prod_name_'.(int)$product_info_row['products_id'].'" class="hide"> > <a href="' . tep_href_link(FILENAME_CATEGORIES, tep_get_path($product_info_row['categories_id']) . '&pID=' . $product_info_row['products_id'] . '&selected_box=catalog') . '"  target="_blank" class="blacklink" style="font-weight:bold;" title="'.TEXT_PRODUCTS_ID.' '.$product_info_row['products_id'].'">' . strip_tags($prod_loc_row['products_name']) . '</a></span> ';
											if ($supplier_order->info['orders_status'] == 5) {
												// Here we use $supplier_order->order_id since we only show in Draft list where only one order be viewed at a time. No batch edit mode
												$real_time_rstk_char = $this_order_res[$supplier_order->order_id]['first_list']['real_time_rstk_char'];
												echo tep_not_null($real_time_rstk_char) ? '['.$real_time_rstk_char.']' : WARNING_TEXT_NO_RESTK_CHAR_ASSIGN;
											}
										} else {
											echo "**--This product is no longer existing in db--**";
										}
									?>
									</td>
<?		if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
			if (tep_not_null($back_order_tag_id) && count($back_order_status_array)) {
				$bo_order_status = rawurlencode(tep_array_serialize($back_order_status_array));
				$bo_tag_id = rawurlencode(tep_array_serialize(array($back_order_tag_id)));
				
				$status_tag_id_url = '';
				for ($ts_cnt=0; $ts_cnt < count($back_order_status_array); $ts_cnt++) {
					$status_tag_id_url .= '&status_'.(trim($back_order_status_array[$ts_cnt])).'='.$bo_tag_id;
				}
				
				// Do not count as backorder if the order tagged with [ACC - PENDING REFUND] OR [ACC - ISSUE VOUCHER] OR [CB]
				$count_back_orders_select_sql = "	SELECT COUNT(DISTINCT o.orders_id) AS total_back_orders
													FROM " . TABLE_ORDERS . " AS o 
													INNER JOIN " . TABLE_ORDERS_STATUS . " AS s 
														ON o.orders_status = s.orders_status_id
													LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
														ON o.orders_id = op.orders_id 
													WHERE s.language_id = '" . (int)$languages_id . "' 
														AND op.products_id = '".$product_info_row['products_id']."' 
														AND o.orders_status IN ('" . implode("', '", $back_order_status_array) . "') 
														AND ( FIND_IN_SET( '".$back_order_tag_id."', o.orders_tag_ids ) )
														AND ( NOT FIND_IN_SET('58', o.orders_tag_ids ) AND NOT FIND_IN_SET('63', o.orders_tag_ids ) AND NOT FIND_IN_SET('92', o.orders_tag_ids ))";
				$count_back_orders_result_sql = tep_db_query($count_back_orders_select_sql);
				$count_back_orders_row = tep_db_fetch_array($count_back_orders_result_sql);
				if ($count_back_orders_row["total_back_orders"] > 0) {
					echo '				<td class="ordersRecords" align="center" valign="top">[<a href="' . tep_href_link(FILENAME_STATS_ORDERS_TRACKING, 'action=show_report&subaction=sl_status&include_subcategory=1&product_id='.$product_info_row['products_id'].'&order_status='.$bo_order_status . $status_tag_id_url) . '"  target="_blank" class="blacklink" title="Show back orders for this product">' . $count_back_orders_row["total_back_orders"] . '</a>]</td>';
				} else {
					echo '				<td class="ordersRecords" align="center" valign="top">&nbsp;</td>';
				}
			} else {
				echo '				<td class="ordersRecords" align="center" valign="top">&nbsp;</td>';
			}
			
			$normal_order_status = rawurlencode(tep_array_serialize(array(7, 2)));
			$status_tag_id_url .= '&status_7='.tep_array_serialize(array('no_tag')).'&status_2='.tep_array_serialize(array('no_tag'));
			
			$count_normal_orders_select_sql = "	SELECT COUNT(DISTINCT o.orders_id) AS total_normal_orders 
												FROM " . TABLE_ORDERS . " AS o 
												INNER JOIN " . TABLE_ORDERS_STATUS . " AS s 
													ON o.orders_status = s.orders_status_id
												LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
													ON o.orders_id = op.orders_id 
												WHERE s.language_id = '" . (int)$languages_id . "' 
													AND op.products_id = '".$product_info_row['products_id']."' 
													AND o.orders_status IN (7, 2) 
													AND o.orders_tag_ids=''";
			$count_normal_orders_result_sql = tep_db_query($count_normal_orders_select_sql);
			$count_normal_orders_row = tep_db_fetch_array($count_normal_orders_result_sql);
			if ($count_normal_orders_row["total_normal_orders"] > 0) {
				echo '				<td class="ordersRecords" align="center" valign="top">[<a href="' . tep_href_link(FILENAME_STATS_ORDERS_TRACKING, 'action=show_report&subaction=sl_status&include_subcategory=1&product_id='.$product_info_row['products_id'].'&order_status='.$normal_order_status . $status_tag_id_url) . '"  target="_blank" class="blacklink" title="Show current orders for this product">' . $count_normal_orders_row["total_normal_orders"] . '</a>]</td>';
			} else {
				echo '				<td class="ordersRecords" align="center" valign="top">&nbsp;</td>';
			}
			
        	echo '     				<td class="ordersRecords" align="center" valign="top"><span style="color:blue;" id="loc_'.$unique_row_reference.'">' . ($prod_loc ? $prod_loc : TEXT_OPTION_NOT_APPLICABLE) . '</span></td>' . "\n";
		}
		
		echo '						<td align="center" valign="top" class="ordersRecords">'.(tep_not_null($product_info_row['products_actual_quantity']) ? (int)$product_info_row['products_actual_quantity'] : '&nbsp').'</td>';
		
		for ($o_cnt=0; $o_cnt < count($looping_orders); $o_cnt++) {
			$this_order_id = $looping_orders[$o_cnt];
			$unique_product_reference = $this_pid . '_' . $this_order_id;
			
			if (is_array($this_order_res[$this_order_id]['confirm_list']) && count($this_order_res[$this_order_id]['confirm_list']) > 0) {
				$list_ref = 'confirm_list';
				$confirm_list_exist = true;
			} else {
				$list_ref = 'first_list';
			}
			
			if ($o_cnt > 0) {
				echo '				<td align="center" valign="top" class="cell_separator"></td>';
			}
			
			if (!isset($this_order_res[$this_order_id])) {
				for ($not_app_cnt=0; $not_app_cnt < $total_merging_colspan_multiple; $not_app_cnt++) {
					echo '			<td align="center" valign="top" class="ordersRecords">'.''.'</td>';
				}
			} else {
				$suggested_quantity = $this_order_res[$this_order_id]['first_list']['products_quantity'];
				$selling_quantity = $this_order_res[$this_order_id]['confirm_list']['products_quantity'];
				$received_quantity = $this_order_res[$this_order_id]['confirm_list']['products_received_quantity'];
				$balance = $selling_quantity - $received_quantity;
				
				$recv_qty_style = tep_not_null($received_quantity) && is_numeric($received_quantity) && $received_quantity < $selling_quantity ? 'class="redIndicator"' : '';
				
				if ($received_quantity > 0)	$not_received_any = false;
				
				$payable_total += (double)$this_order_res[$this_order_id]['payable_amount'];
				
				if ($show_purchase_status) {
					echo '			<td align="center" valign="top" class="ordersRecords">';
					$server_status_index = trim($this_order_res[$this_order_id][$list_ref]['products_purchase_status']);
					if (isset($server_status_array[$server_status_index])) {
						echo tep_image(DIR_WS_IMAGES . $server_status_array[$server_status_index]['name'] . '.gif', ucfirst($this_order_res[$this_order_id][$list_ref]['products_purchase_status']));
					}
					echo '			</td>';
				}
				
				if ($looping_order_status == PARTIAL_RECEIVE_STATUS) {
					echo '			<td valign="top" class="ordersRecords">'.$this_order_res[$this_order_id][$list_ref]['products_restock_comment'].'</td>';
				}
				
				echo '				<td valign="top" class="ordersRecords">'.$this_order_res[$this_order_id][$list_ref]['supplier_order_lists_products_comment'].'</td>';
				echo '				<td align="center" valign="top" class="ordersRecords">'.$this_order_res[$this_order_id][$list_ref]['min_quantity'].'</td>';
				
				if ($looping_order_status != PARTIAL_RECEIVE_STATUS) {
					if ($view_payment_info_permission) {
						echo '		<td align="center" valign="top" class="ordersRecords">'.$this_order_res[$this_order_id][$list_ref]['first_max_quantity'].'</td>';
						echo '		<td align="right" valign="top" class="ordersRecords">'.$this_order_res[$this_order_id][$list_ref]['first_max_unit_price'].'</td>';
						//echo '		<td align="center" valign="top" class="ordersRecords">'.$this_order_res[$this_order_id][$list_ref]['second_max_quantity'].'</td>';
						//echo '		<td align="right" valign="top" class="ordersRecords">'.$this_order_res[$this_order_id][$list_ref]['second_max_unit_price'].'</td>';
					}
					
					echo '			<td align="center" valign="top" class="ordersRecords">'.$suggested_quantity.'</td>';
				}
?>
									<td align="center" valign="top" class="ordersRecords"><?=$selling_quantity?></td>
									<td align="center" valign="top" class="ordersRecords"><?=$received_quantity?></td>
<?				if ($looping_order_status == PARTIAL_RECEIVE_STATUS) {
					echo '			<td align="center" valign="top" class="ordersRecords">'.$balance.'</td>';
					echo '			<td align="center" valign="top" class="ordersRecords" nowrap>';
					
					if ( ($balance > 0 || $received_quantity > 0) || ($selling_quantity === '0' && is_null($received_quantity)) ) {
						if ($full_cat_permissions) {
							echo tep_draw_pull_down_menu('partial_receive_sign['.$unique_product_reference.']', $received_qty_adjust_array, '', 'id="partial_receive_sign_'.$unique_product_reference.'"');
							echo tep_draw_input_field('partial_receive['.$unique_product_reference.']', 0, ' size="5" id="partial_receive_'.$unique_product_reference.'" onChange="checkBalance(this, '.(int)$received_quantity.', '.(int)$balance.')" onKeyPress="return noEnterKey(event)"');
							
							$partial_inputs[$unique_product_reference] = array('received' => $received_quantity, 'balance' => $balance);
							$this_product_need_restock = true;
						} else {
							echo tep_draw_input_field('no_access_field', TEXT_NO_ACCESS, ' size="10" DISABLED ');
						}
					} else echo '&nbsp;';
					
					echo '			</td>';
				} else if ($view_payment_info_permission) {
					echo '			<td align="right" valign="top" class="ordersRecords" nowrap>'.$currencies->format($this_order_res[$this_order_id]['payable_amount'], true, $supplier_order->info['currency'], $supplier_order->info['currency_value']).'</td>';
				}
			}
		}
		
		if ($looping_order_status == PARTIAL_RECEIVE_STATUS) {
			echo '					<td align="center" valign="top" class="ordersRecords" nowrap>';
			if ($this_product_need_restock) {
				echo tep_draw_pull_down_menu('extra_receive_sign['.$unique_row_reference.']', $received_qty_adjust_array, '', 'id="extra_receive_sign_'.$unique_row_reference.'"');
				echo tep_draw_input_field('extra_receive['.$unique_row_reference.']', 0, ' size="5" id="extra_receive_'.$unique_row_reference.'" class="redInputBox" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"');
			} else { echo '&nbsp;'; }
			echo '					</td>';
			
			echo '					<td align="center" valign="top" class="ordersRecords">';
			if ($this_product_need_restock) {
				echo tep_submit_button('Restock', 'Receive Stock', 'name="ReceiveBtn_'.$this_pid.'" onClick="return restock_form_checking(\''.$this_pid.'\');"', 'inputButton');
			} else { echo '&nbsp;'; }
			echo '					</td>';
		}
		
		echo '					</tr>';
		$i++;
	}
?>
								<tr>
									<td colspan="<?=$total_colspan?>">
										<table width="100%" border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td class="smallText" valign="top">
													<table border="0" cellspacing="0" cellpadding="0">
														<tr>
														<?
														if (count($server_status_array)) {
															foreach ($server_status_array as $ss_name => $ss_res) {
																echo '	<td>'.tep_image(DIR_WS_IMAGES . $ss_res['name'] . '.gif', ucfirst($ss_name)).'</td>
																		<td class="smallText" NOWRAP>&nbsp;'.ucfirst($ss_name).'&nbsp;&nbsp;</td>';
															}
														}
														?>
														</tr>
													</table>
												</td>
	          									<td align="right" class="smallText">
<?	if ($supplier_order->info['orders_status'] == PARTIAL_RECEIVE_STATUS) {
		echo '										<table border="0" width="100%">
														<tbody id="partial_delivery_box" class="show">
															<tr>
																<td align="right" valign="top" class="main">';
		if (count($partial_inputs)) {
			echo tep_submit_button('Batch Restock', 'Batch Receive Stock', 'name="BatchReceiveBtn" onClick="return restock_form_checking();"', 'inputButton');
		}
		echo '													</td>
															</tr>
														</tbody>
													</table>';
	} else {
		if ($view_payment_info_permission) {
			echo sprintf(TEXT_TOTAL_AMOUNT, $currencies->format($payable_total, true, $supplier_order->info['currency'], $supplier_order->info['currency_value']));
			$order_payments_array = supplier_payment::get_order_paid_history($oID, false);
			
			$outstanding_amount = $payable_total;
			for ($op_cnt=0; $op_cnt < count($order_payments_array); $op_cnt++) {
				$outstanding_amount -= $order_payments_array[$op_cnt]['paid_amount'];
				echo '<br>' . sprintf(TEXT_PAYMENT_RECORDS, $order_payments_array[$op_cnt]['payment_type']=='1' ? TEXT_PARTIAL_PAYMENT : TEXT_FULL_PAYMENT, $order_payments_array[$op_cnt]['payment_id'], $currencies->format($order_payments_array[$op_cnt]['paid_amount'], true, $order_payments_array[$op_cnt]['paid_currency'], $order_payments_array[$op_cnt]['paid_currency_value']));
			}
			
			if ($op_cnt > 0) {
				echo '<br>' . sprintf(TEXT_BALANCE_AMOUNT, $currencies->format($outstanding_amount, true, $supplier_order->info['currency'], $supplier_order->info['currency_value']));
			}
		}
	}
?>
												</td>
											</tr>
										</table>
									</td>
								</tr>
	   							<script language="javascript">
								<!--
									var parent_to_child = new Array();
									
		   							function showOverEffect(class_name, main_id) {
										rowOverEffect(document.getElementById(main_id), class_name);
										
										if (typeof(parent_to_child[main_id]) != 'undefined' && typeof(parent_to_child[main_id].length) != 'undefined') {
											for (var i = 0; i < parent_to_child[main_id].length; i++) {
												var rowObj = main_id + '_' + parent_to_child[main_id][i];
					  							rowOverEffect(document.getElementById(rowObj), class_name);
					  						}
										}
									}
									
									function showOutEffect(class_name, main_id) {
										rowOutEffect(document.getElementById(main_id), class_name);
										
										if (typeof(parent_to_child[main_id]) != 'undefined' && typeof(parent_to_child[main_id].length) != 'undefined') {
											for (var i = 0; i < parent_to_child[main_id].length; i++) {
												var rowObj = main_id + '_' + parent_to_child[main_id][i];
					  							rowOutEffect(document.getElementById(rowObj), class_name);
					  						}
										}
									}
									
									function showClicked(class_name, main_id) {
										rowClicked(document.getElementById(main_id), class_name);
										
										if (typeof(parent_to_child[main_id]) != 'undefined' && typeof(parent_to_child[main_id].length) != 'undefined') {
											for (var i = 0; i < parent_to_child[main_id].length; i++) {
												var rowObj = main_id + '_' + parent_to_child[main_id][i];
					  							rowClicked(document.getElementById(rowObj), class_name);
					  						}
										}
									}
									
									function checkBalance(obj, recv_qty, max_qty) {
										if (obj != null) {
											recv_qty = parseInt(recv_qty);
											max_qty = parseInt(max_qty);
											obj.value = trim_str(obj.value);
											
											var ref_id = replace(obj.id, 'partial_receive_', '');
											var qty_sign_obj = DOMCall('partial_receive_sign_' + ref_id);
											
											if (obj.value != '' && qty_sign_obj != null) {
												if (!validateInteger(obj.value)) {
													obj.value = '';
												} else {
													var entered_qty = parseInt(qty_sign_obj.value + '' + obj.value);
													if (entered_qty >= 0) {
														if (entered_qty > max_qty) {
															alert('Entered receive quantity exceed balance quantity!\nPlease reduce the quantity!');
															//obj.select();
															obj.focus();
															return false;
														}
													} else {
														if (Math.abs(entered_qty) > recv_qty) {
															alert('Entered deduct receive quantity exceed received quantity!\nPlease reduce the deduction quantity!');
															//obj.select();
															obj.focus();
															return false;
														}
													}
												}
											}
										}
										
										return true;
									}
									
									function restock_form_checking(ref) {
										var got_input = false;
										var invalid_input = false;
										var invalid_receive_input = false;
										var over_input = false;
										var over_deduct_input = false;
										var cur_obj = '';
										var cur_recv_obj = '';
										var error_message = '<?=JS_ERROR?>';
										var error = false;
										
										if (ref != null) {	// restock particular server
											answer = confirm('Are you sure to restock this product?');
										} else {
											answer = confirm('Are you sure to batch restock the product(s)?');
										}
										if (answer !=0 ) {
										<?	if (count($partial_inputs)) {
												$extra_product_array = array();
												foreach ($partial_inputs as $input_id => $qty_res) {
													$max_qty = (int)$qty_res["balance"];
													$recv_qty = (int)$qty_res["received"];
													
													list($pid, $orderid) = explode('_', $input_id);
													
													if (!in_array($pid, $extra_product_array)) {
														$extra_product_array[] = $pid;	?>
														cur_recv_obj = DOMCall('extra_receive_' + '<?=$pid?>');
														if (cur_recv_obj != null) {
															if (ref == null || ref == '<?=$pid?>') {
																if (trim_str(cur_recv_obj.value) != '') {
																	if (!validateInteger(cur_recv_obj.value)) {
																		invalid_receive_input = true;
																	} else {
																		var entered_recv_qty = parseInt(cur_recv_obj.value);
																		if (entered_recv_qty > 0) {
																			got_input = true;
																		} else {
																			invalid_receive_input = true;
																		}
																	}
																}
															}
														}
										<?
													}
										?>
												cur_obj = DOMCall('partial_receive_' + '<?=$input_id?>');
												
												if (cur_obj != null) {
													if (ref == null || cur_obj.id.search('partial_receive_'+ref) != -1) {
														var ref_id = replace(cur_obj.id, 'partial_receive_', '');
														var qty_sign_obj = DOMCall('partial_receive_sign_' + ref_id);
														
														if (trim_str(cur_obj.value) != '' && qty_sign_obj != null) {
															if (!validateInteger(cur_obj.value)) {
																invalid_input = true;
															} else {
																var entered_qty = parseInt(qty_sign_obj.value + '' + cur_obj.value);
																if (entered_qty >= 0) {
																	if (entered_qty > <?=$max_qty?>) {
																		over_input = true;
																	} else {
																		got_input = true;
																	}
																} else {
																	if (Math.abs(entered_qty) > <?=$recv_qty?>) {
																		over_deduct_input = true;
																	} else {
																		got_input = true;
																	}
																}
															}
														}
													}
												}
										<?	}
										}
										?>
											if (document.getElementById('status_now').value > 0) {
												error_message += '* Restock only available for supplier order in pending status.\nIt seems that you intend to update the supplier order status!' + "\n";
												document.getElementById('status_now').focus();
												error = true;
											}
											
											if (invalid_input) {
												error_message += '* Some input values for receive quantity is invalid!' + "\n";
												error = true;
											}
											
											if (over_input) {
												error_message += '* Some input values for receive quantity is over the balance quantity!' + "\n";
												error = true;
											}
											
											if (over_deduct_input) {
												error_message += '* Some input values for deduct receive quantity exceed received quantity!' + "\n";
												error = true;
											}
											
											if (invalid_receive_input) {
												error_message += '* Some input values for extra quantity is invalid!' + "\n";
												error = true;
											}
											
											if (!got_input && !error) {
												error_message += '* There is no any integer value entered for receive quantity!' + "\n";
												error = true;
											}
											
											if (!error) {
												if (ref != null) {
													//eval("document.partial_receive_form.ReceiveBtn_" + ref).disabled = true;
													eval("document.partial_receive_form.ReceiveBtn_" + ref).value = 'Please wait...';
												} else {
													//document.partial_receive_form.BatchReceiveBtn.disabled = true;
													document.partial_receive_form.BatchReceiveBtn.value = 'Please wait...';
												}
												return true;
											} else {
												alert(error_message);
												return false;
											}
										} else {
											return false;
										}
									}
									
									function show_hide_product_name(show) {
										var show_hide_prod_name_div = DOMCall('show_product_name');
										
										if (show) {
											show_hide_prod_name_div.innerHTML = '<a href="javascript:;" onclick="show_hide_product_name(false)"><?=TEXT_HIDE_PRODUCT_NAME?></a>';
										} else {
											show_hide_prod_name_div.innerHTML = '<a href="javascript:;" onclick="show_hide_product_name(true)"><?=TEXT_SHOW_PRODUCT_NAME?></a>';
										}
										
										var span_obj = document.getElementsByTagName("span");
										for (var i=0; i < span_obj.length; i++) {
											if (span_obj[i].id.search('span_prod_name_') != -1) {
												span_obj[i].className = show ? 'show' : 'hide';
											}
										}
									}
									
									function confirm_update_status(sel_obj, total_receive) {
										if (sel_obj.value == 2) {
										<?	if ($supplier_order->info['orders_status'] == '1') { ?>
												alert('This supplier "Purchase Mode" will be turned OFF when you update this order to Processing. ' + "\n" + 'Remember to update this "Purchase Mode" to "Follow Group Mode" from Suppliers List page when the new list is ready for this supplier.');
										<?	} ?>
										} else if (sel_obj.value == 3) {
											//document.getElementById('notify').checked = true;
										}
										return true;
									}
								//-->
								</script>
				  	    	</table>
				  	    	</form>
				  	    </td>
				  	</tr>
				  	<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
            			<td colspan="3"><?=tep_draw_separator()?></td>
          			</tr>
          			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
				  	<tr>
				  		<td class="main">
        					<table border="1" cellspacing="0" cellpadding="5">
          						<tr>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_DATE_ADDED?></b></td>
<?	if ($edit_mode == 'multi') {
		echo '						<td class="smallText" align="center"><b>'.TABLE_HEADING_ORDER_ID.'</b></td>';
	}
?>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_SUPPLIER_NOTIFIED?></b></td>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_STATUS?></b></td>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_COMMENTS?></b></td>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_CHANGED_BY?></b></td>
<?	if (!$read_only_mode) {
    	echo ' 						<td class="smallText" align="center"><b>'.TABLE_HEADING_ACTION.'</b></td>';
	}
?>
          						</tr>
<?
	$order_list_history_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_ORDER_LISTS_HISTORY . " WHERE supplier_order_lists_id IN ('" . implode("', '", $looping_orders) . "') ORDER BY date_added";
	$order_list_history_result_sql = tep_db_query($order_list_history_select_sql);
	while ($order_list_history_row = tep_db_fetch_array($order_list_history_result_sql)) {
		$comment_row_style = $order_list_history_row["set_as_order_list_remarks"] == 1 ? 'class="orderRemarkSelectedRow"' : '';
		
		$formatted_date_comment_added = tep_datetime_short($order_list_history_row["date_added"], PREFERRED_DATE_TIME_FORMAT);
		$img_str = ($order_list_history_row['supplier_notified'] == '1') ? tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) : tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS);
?>
								<tr <?=$comment_row_style?>>
            						<td class="smallText" align="center"><?=(tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--')?></td>
<?		if ($edit_mode == 'multi') {
			echo '					<td class="smallText" align="center">'.$order_list_history_row["supplier_order_lists_id"].'</td>';
		}
?>
            						<td class="smallText" align="center"><?=$img_str?></td>
            						<td class="smallText" align="center"><?=$orders_status_array[$order_list_history_row['supplier_order_lists_status']]?></td>
            						<td class="smallText"><?=nl2br(str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $order_list_history_row['comments']))?>&nbsp;</td>
            						<td class="smallText" align="center"><?=nl2br(tep_db_output($order_list_history_row['changed_by']))?>&nbsp;</td>
<?		if (!$read_only_mode) {
            echo '					<td class="smallText" align="center">'.($order_list_history_row["set_as_order_list_remarks"] == 1 ? '&nbsp;' : '<a href="' . tep_href_link(FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('subaction'))."subaction=set_as_remark&commentID=".$order_list_history_row["supplier_order_lists_history_id"]."&s_orders_batch=".$serialized_oIDs).'">'.TEXT_SET_AS_REMARK.'</a>').'</td>';
		}
?>
          						</tr>
<?	} ?>
							</table>
          				</td>
				  	</tr>
<?	if (!$read_only_mode) {
		echo '	  	<tr>
				  		<td>';
		echo tep_draw_form('status_update_form', FILENAME_SUPPLIERS_ORDERS, tep_get_all_get_params(array('subaction')), 'post', '');
		echo tep_draw_hidden_field('subaction', 'update_order');
		echo tep_draw_hidden_field('status_DB_prev', $SESSION_SUPPLIER_ORDER_STATUS_B4);
		echo tep_draw_hidden_field('s_orders_batch', $serialized_oIDs);
		
		$orders_statuses_new = array();
		switch ($supplier_order->info['orders_status']) {
			case 1:	// Pending
				$orders_statuses_new[] = array(	'id' => "0",
												'text' => SELECT_OPTION_UPDATE_COMMENT);
				$orders_statuses_new[] = array(	'id' => "2",
												'text' => "Process");
				if ($not_received_any) {
					$orders_statuses_new[] = array(	'id' => "4",
													'text' => "Cancel");
				}
				break;
			case 2:	// Process
				$orders_statuses_new[] = array(	'id' => "0",
												'text' => SELECT_OPTION_UPDATE_COMMENT);
				
				if (tep_check_status_update_permission('S', $login_groups_id, 2, 1)) { // only the person with permission can move to pending
					$orders_statuses_new[] = array(	'id' => "1",
													'text' => "*Pending*");
				}
				
				$orders_statuses_new[] = array(	'id' => "3",
												'text' => "Complete");
				
				break;
			case 3:	// Complete
				$orders_statuses_new[] = array(	'id' => "0",
												'text' => SELECT_OPTION_UPDATE_COMMENT);
				
				if ($supplier_order->info['billing_status'] != '1' && tep_check_status_update_permission('S', $login_groups_id, 3, 2)) {
					$orders_statuses_new[] = array(	'id' => "2",
													'text' => "*Process*");
				}
				break;
			case 4:	// Cancel
				$orders_statuses_new[] = array(	'id' => "0",
												'text' => SELECT_OPTION_UPDATE_COMMENT);
				if ($reversible_order_status_permission) {
					if ($confirm_list_exist) {
						$orders_statuses_new[] = array(	'id' => "1",
														'text' => "*Pending*");
					} else {
						$orders_statuses_new[] = array(	'id' => "5",
														'text' => "*Draft*");
					}
				}
				break;
			case 5: // Draft
				$orders_statuses_new[] = array(	'id' => "0",
												'text' => SELECT_OPTION_UPDATE_COMMENT);
				$orders_statuses_new[] = array(	'id' => "4",
												'text' => "Cancel");
				break;
		}
?>
				  			<table border="0" cellspacing="0" cellpadding="2">
				  				<tr>
				  					<td class="main" colspan="3"><b><?=ENTRY_ADMIN_COMMENT?></b></td>
				  				</tr>	
				  				<tr>
				  					<td class="main" colspan="2"><?=tep_draw_textarea_field('admin_comment', 'soft', '60', '5')?></td>
				  					<td class="main" valign="bottom" nowrap><?=tep_draw_checkbox_field('set_as_remark', '1', false, '', '') . '&nbsp;' . TEXT_SET_AS_REMARK?></td>
				  				</tr>
				  				<tr>
									<td colspan="2">
										<table border="0" cellspacing="0" cellpadding="0">
											<tr>
												<td class="main">
													<b><?=ENTRY_STATUS?></b>&nbsp;<? echo tep_draw_pull_down_menu('status_now', $orders_statuses_new, $supplier_order->info['orders_status'], ' id="status_now" onChange="return confirm_update_status(this, '.(int)$total_received.');"'); ?>
												</td>
											</tr>
										</table>
									</td>
									<td class="main">&nbsp;</td>
								</tr>
				  				<tr>
				  					<td class="main"><b><?=ENTRY_NOTIFY_SUPPLIER?></b>&nbsp;<?=tep_draw_checkbox_field('notify', '1', false, '', 'id=notify' . (!$reversible_order_status_permission ? ' onClick="if (document.getElementById(\'status_now\').value == 3) { this.checked = true; }"' : ''))?></td>
				  					<td align="right" class="main"><input type="submit" name="OrderUpdateBtn" value="Update" title="<?=IMAGE_UPDATE?>" class="inputButton" onClick="return submitForm(0);"></td>
				  					<td class="main">&nbsp;</td>
				  				</tr>
				  			</table>
				  		</form>
				  		</td>
				  	</tr>
<?		echo '		<tr>
						<td align="left"><br>
							'.tep_button(IMAGE_BUTTON_BACK, IMAGE_BUTTON_BACK . ' to Suppliers Order Lists', $back_btn_url, '', 'inputButton').'
	 					</td>
	 				</tr>';
	 }
} else {
	echo '			<tr>
        				<td width="100%" class="main">The Order <b>#'.$oID.'</b> cannot be found!</td>
        			</tr>';
}
?>
				</table>
			</td>
		<tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>