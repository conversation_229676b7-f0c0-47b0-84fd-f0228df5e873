<?
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'currencies.php');
require(DIR_WS_CLASSES . 'supplier_payment.php');
require(DIR_WS_CLASSES . 'supplier_order.php');

define('DISPLAY_PRICE_DECIMAL', 4);

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$manage_search_criteria_permission = tep_admin_files_actions(FILENAME_STATS_ORDERS_TRACKING, 'SAVE_ORDER_LISTS_CRITERIA');
$view_payment_info_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_ORDERS, 'SUPPLIER_ORDER_PAYMENT_INFO');
$reverse_payment_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_PAYMENT, 'REVERSE_SUPPLIER_PAYMENT');

$perform_unserialize_criteria = false;

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');
$subaction = $_REQUEST["subaction"];

if (tep_not_null($action)) {
	switch ($action) {
		case "show_report":
			if (!$_REQUEST['cont']) {
				if ($_REQUEST["subaction"] == "goto_search") {
					$_SESSION['sup_payment_param']["criteria_id"] = (int)$_REQUEST["criteria_id"];
					$perform_unserialize_criteria = true;
				} else {
					unset($_SESSION['sup_payment_param']["criteria_id"]);
					unset($_SESSION['sup_payment_param']["cur_criteria_name"]);
					
					$_SESSION['sup_payment_param']["start_date"] = $_REQUEST["start_date"];
					$_SESSION['sup_payment_param']["end_date"] = $_REQUEST["end_date"];
					$_SESSION['sup_payment_param']["payment_id"] = (int)$_REQUEST["payment_id"];
					$_SESSION['sup_payment_param']["order_id"] = (int)$_REQUEST["order_id"];
					$_SESSION['sup_payment_param']["supplier_group"] = $_REQUEST["supplier_group"];
					$_SESSION['sup_payment_param']["supplier"] = $_REQUEST["supplier"];
					$_SESSION['sup_payment_param']["sort_by"] = $_REQUEST["sort_by"];
  					$_SESSION['sup_payment_param']["sort_order"] = $_REQUEST["sort_order"];
					$_SESSION['sup_payment_param']["show_records"] = $_REQUEST["show_records"];
					
					if ($_REQUEST["supplier_order_list_subaction"] == "save_search") {
						if (tep_not_null($_REQUEST["search_name"])) {
							$save_string = array();
							foreach ($_SESSION['sup_payment_param'] as $key => $search_input) {
								$serialized_key_value_pair = tep_array_serialize($key) . ':~:' . tep_array_serialize($search_input);
								$save_string[] = $serialized_key_value_pair;
							}
							
							$search_name = tep_db_prepare_input($_REQUEST["search_name"]);
							$search_criteria_id = '';
							$search_criteria_check_select_sql = "SELECT search_criteria_id FROM " . TABLE_SEARCH_CRITERIA . " WHERE filename='" . tep_db_input(basename($PHP_SELF)) . "' AND search_criteria_name = '" . tep_db_input($search_name) . "'";
							$search_criteria_check_result_sql = tep_db_query($search_criteria_check_select_sql);
							if ($search_criteria_check_row = tep_db_fetch_array($search_criteria_check_result_sql)) {
								$search_criteria_id = (int)$search_criteria_check_row["search_criteria_id"];
							}
							
							if (count($save_string)) {
								$search_criteria_sql_data = array(	'filename' => tep_db_prepare_input(basename($PHP_SELF)),
								                       				'search_criteria_name' => $search_name,
								                       				'search_criteria_string' => tep_db_prepare_input(implode('#~#', $save_string))
								                       			);
								
								if (tep_not_null($search_criteria_id)) {
									// update existing search
									$update_sql_data = array(	'date_search_criteria_last_modified' => 'now()',
																'last_modified_by' => $login_id
															);
									$sql_data_array = array_merge($search_criteria_sql_data, $update_sql_data);
									tep_db_perform(TABLE_SEARCH_CRITERIA, $sql_data_array, 'update', "search_criteria_id = '" . $search_criteria_id . "'");
									$messageStack->add('OK, your criteria named &lt;'.$search_name.'&gt; is updated.', 'success');
								} else {
									// insert new searchs
									$insert_sql_data = array(	'date_search_criteria_added' => 'now()',
								    	                   		'search_criteria_created_by' => $login_id
															);
									$sql_data_array = array_merge($search_criteria_sql_data, $insert_sql_data);
									tep_db_perform(TABLE_SEARCH_CRITERIA, $sql_data_array);
									$search_criteria_id = tep_db_insert_id();
									$messageStack->add('OK, your new criteria named &lt;'.$search_name.'&gt; is saved.', 'success');
								}
								$_SESSION['sup_payment_param']["criteria_id"] = $search_criteria_id;
								$perform_unserialize_criteria = true;
							}
						}
					}
				}
		  	}
			break;
        case "reset_session":
        	unset($_SESSION['sup_payment_param']);
        	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PAYMENT));
        	break;
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
    	case 'update_payment':
    		$status_cur = $_POST['status_now'];
    		
    		$current_payment_status_select_sql = "SELECT supplier_payments_status FROM " . TABLE_SUPPLIER_PAYMENTS . " WHERE supplier_payments_id = '" . tep_db_input($_REQUEST['payID']) . "'";
			$current_payment_status_result_sql = tep_db_query($current_payment_status_select_sql);
			$current_payment_status_row = tep_db_fetch_array($current_payment_status_result_sql);
			
    		if ($HTTP_POST_VARS["status_DB_prev"] != $current_payment_status_row['supplier_payments_status']) {
    			$messageStack->add_session(WARNING_ORDERS_UPDATED_BY_SOMEONE, 'warning');
    			
    			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PAYMENT, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch', 'commentID')) . 'action=edit&payID='.$_REQUEST['payID']) );
        		break;
    		} else if ($status_cur > 0 && !$reverse_payment_permission) {	// If the user has no permission to reverse the payment
    			$messageStack->add_session(ERROR_REVERSE_PAYMENT_DENIED, 'error');
    			
    			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PAYMENT, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch', 'commentID')) . 'action=edit&payID='.$_REQUEST['payID']) );
        		break;
    		}
    		
    		$supplier_payment = new supplier_payment($_REQUEST['payID']);
			$supplier_payment->set_remarks(tep_db_prepare_input($_POST['admin_comment']));
			$supplier_payment->set_show_supplier_remark($_POST['notify']=='1' ? true : false);
			
			if ($status_cur > 0) {
				$payment_result = $supplier_payment->reverse_payment();
			} else {	// Update comment
				$payment_result = $supplier_payment->update_payment_comment();
			}
			
			if (is_array($payment_result) && count($payment_result)) {
				$messageStack->add_session($payment_result['text'], $payment_result['type']);
			}
			
			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PAYMENT, tep_get_all_get_params(array('action', 'subaction', 's_orders_batch', 'commentID')) . 'action=edit&payID='.$_REQUEST['payID']) );
			
    		break;
    }
}

$payment_statuses = array();
$payment_status_array = array();
$payment_status_select_sql = "SELECT supplier_payments_status_id, supplier_payments_status_name FROM " . TABLE_SUPPLIER_PAYMENTS_STATUS . " WHERE language_id = '" . (int)$languages_id . "' ORDER BY supplier_payments_status_sort_order";
$payment_status_result_sql = tep_db_query($payment_status_select_sql);
while ($payment_status_row = tep_db_fetch_array($payment_status_result_sql)) {
	$payment_statuses[] = array('id' => $payment_status_row['supplier_payments_status_id'],
                              	'text' => $payment_status_row['supplier_payments_status_name']);
    $payment_status_array[$payment_status_row['supplier_payments_status_id']] = $payment_status_row['supplier_payments_status_name'];                       
}

$payment_statuses[] = array('id' => '0', 'text' => "--");
$payment_status_array['0'] = "--";
/*
$saved_search_options = array( array ('id' => '', "text" => "Saved Criteria >>>>>") );
$search_criteria_select_sql = "SELECT search_criteria_id, search_criteria_name FROM " . TABLE_SEARCH_CRITERIA . " WHERE filename='" . tep_db_input(basename($PHP_SELF)) . "' ORDER BY search_criteria_name";
$search_criteria_result_sql = tep_db_query($search_criteria_select_sql);
while ($search_criteria_row = tep_db_fetch_array($search_criteria_result_sql)) {
	$saved_search_options[] = array ('id' => $search_criteria_row["search_criteria_id"], "text" => $search_criteria_row["search_criteria_name"]);
}
*/
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/xmlhttp.js"></script>
	<script language="javascript" src="includes/javascript/orders.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<!-- body_text //-->
<?
if ($_REQUEST['action'] == 'edit') {
	$payment_id = $_REQUEST['payID'];
	$supplier_payment = new supplier_payment($payment_id);
	
	if (isset($_SESSION['sup_payment_param'])) {
		$back_btn_url = tep_href_link(FILENAME_SUPPLIERS_PAYMENT, 'action=show_report&cont=1');
	} else {
		$back_btn_url = tep_href_link(FILENAME_SUPPLIERS_PAYMENT);
	}
?>
					<tr>
						<td valign="top" class="pageHeading"><?=HEADING_EDIT_PAYMENT_TITLE?></td>
					</tr>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
        						<tr>
            						<td valign="top" width="80%">
            							<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td class="main" width="15%"><b><?=ENTRY_EDITED_PAYMENT_ID?></b></td>
												<td class="main"><b><?=$payment_id?></b></td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_PAYMENT_STATUS?></b></td>
												<td class="main"><?=$payment_status_array[$supplier_payment->info['payments_status']]?></td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_PAYMENT_DATE_TIME?></b></td>
												<td class="main"><?=tep_datetime_short($supplier_payment->info["payments_date"], PREFERRED_DATE_TIME_FORMAT)?></td>
											</tr>
											<tr>
												<td class="main"><b><?=ENTRY_DATE_LAST_MODIFIED?></b></td>
												<td class="main"><?=tep_datetime_short($supplier_payment->info["last_modified"], PREFERRED_DATE_TIME_FORMAT)?></td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
						<td><?=tep_draw_separator()?></td>
					</tr>
					<tr>
        				<td>
        					<table width="100%" border="0" cellspacing="0" cellpadding="2">
          						<tr>
                					<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_SUPPLIERS_INFO?></b></td>
              					</tr>
              					<tr>
									<td valign="top">
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
              								<tr>
              									<td width="15%" class="main" valign="top" nowrap><b><?=ENTRY_SUPPLIER?></b></td>
    											<td class="main">
    											<?
    												$sup_grp_str = '['.$supplier_payment->supplier["supplier_group"].']' . TEXT_REAL_TIME_STAT;
    												echo tep_output_string_protected($supplier_payment->supplier['firstname'] . ' ' . $supplier_payment->supplier['lastname']) . '&nbsp;'.$sup_grp_str;
   													echo '<br>' . tep_address_format($supplier_payment->supplier['format_id'], $supplier_payment->supplier, 1, '', '<br>', '', false);
    											?>
    											</td>
              								</tr>
              							</table>
              						</td>
              				</table>
              			</td>
              		</tr>
              		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
						<td><?=tep_draw_separator()?></td>
					</tr>
              		<tr>
        				<td>
        					<table width="100%" border="0" cellspacing="0" cellpadding="2">
          						<tr>
                					<td class="pageHeading" valign="top"><b><?=TABLE_HEADING_PAYMENT_INFO?></b></td>
              					</tr>
              					<tr>
            						<td valign="top">
            							<table width="100%" border="0" cellspacing="0" cellpadding="2">
			              					<tr>
            									<td valign="top">
            										<table width="100%" border="0" cellspacing="0" cellpadding="2">
            											<tr>
			                								<td width="20%" class="main" valign="top"><b><?=ENTRY_SUPPLIER_PAYMENT_PAYPAL?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_payment->payment_info["paypal_email"]?></td>
			              								</tr>
			              								<tr>
			                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_NAME?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_payment->payment_info["bank_name"]?></td>
			              								</tr>
			              								<tr>
							                				<td class="main"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_SWIFT_CODE?></b><?=TEXT_REAL_TIME_STAT?></td>
							                				<td class="main"><?=$supplier_payment->payment_info["bank_swift_code"]?></td>
			              								</tr>
			              								<tr>
			                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ADDRESS?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_payment->payment_info["bank_address"]?></td>
			              								</tr>
			              								<tr>
			                								<td class="main" valign="top"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_PHONE_NUMBER?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_payment->payment_info["bank_telephone"]?></td>
			              								</tr>
			              								<tr>
			                								<td class="main"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NAME?></b><?=TEXT_REAL_TIME_STAT?></td>
			                								<td class="main"><?=$supplier_payment->payment_info['bank_account_name']?></td>
			              								</tr>
			              								<tr>
											                <td class="main"><b><?=ENTRY_SUPPLIER_PAYMENT_BANK_ACCOUNT_NUMBER?></b><?=TEXT_REAL_TIME_STAT?></td>
											                <td class="main"><?=$supplier_payment->payment_info['bank_account_number']?></td>
			              								</tr>
			            							</table>
			            						</td>
			            					</tr>
			            					<tr>
						        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
						      				</tr>
			            					<tr>
            									<td valign="top">
            										<table width="100%" border="0" cellspacing="0" cellpadding="2">
            											<tr>
			                								<td width="20%" class="main" valign="top"><b><?=ENTRY_PAYMENT_AMOUNT?></b></td>
			                								<td class="main"><?=$currencies->format($supplier_payment->info["payments_amount"], true, $supplier_payment->info['currency'], $supplier_payment->info['currency_value'])?></td>
			              								</tr>
			              								<tr>
			                								<td class="main"></td>
			                								<td class="main">
			                									<table border="0" cellspacing="0" cellpadding="2">
			                										<tr>
			        													<td width="10%" align="left" class="ordersBoxHeading"><?=TABLE_HEADING_ORDER_NO?></td>
			        													<td width="20%" align="left" class="ordersBoxHeading"><?=TABLE_HEADING_LIST_NAME?></td>
			        													<td width="20%" align="left" class="ordersBoxHeading"><?=TABLE_HEADING_ORDER_DATE?></td>
																		<td width="25%" align="right" class="ordersBoxHeading"><?=TABLE_HEADING_ORDER_AMOUNT?></td>
																		<td width="25%" align="right" class="ordersBoxHeading"><?=TABLE_HEADING_PAID_AMOUNT?></td>
																	</tr>
<?
	for ($i=0; $i < count($supplier_payment->orders); $i++) {
		$row_style = ($i%2) ? 'invoiceListingEven' : 'invoiceListingOdd';
		
		echo '														<tr class="'.$row_style.'" id="'.$i.'" onMouseOver="rowOverEffect(this, \'invoiceListingRowOver\')" onMouseOut="rowOutEffect(this, \''.$row_style.'\')" onClick="rowClicked(this, \''.$row_style.'\')">
				  														<td align="left" valign="top" class="ordersRecords"><a href="' . tep_href_link(FILENAME_SUPPLIERS_ORDERS, 'oID='.$supplier_payment->orders[$i]['id'].'&action=edit', 'NONSSL') . '" target="_blank">'.$supplier_payment->orders[$i]['id'].'</a>&nbsp;'.($supplier_payment->orders[$i]['payments_type'] == '1' ? TEXT_PARTIAL_PAID_ORDER : '').'</td>
				  														<td align="left" valign="top" class="ordersRecords" nowrap>' . $supplier_payment->orders[$i]['list_name'] . '</td>
				  														<td align="left" valign="top" class="ordersRecords" nowrap>' . $supplier_payment->orders[$i]['date_submitted'] . '</td>
													  					<td align="right" valign="top" class="ordersRecords">' . $currencies->format($supplier_payment->orders[$i]['payable_amount'], true, $supplier_payment->orders[$i]['currency'], $supplier_payment->orders[$i]['currency_value']) . '</td>
													  					<td align="right" valign="top" class="ordersRecords">' . $currencies->format($supplier_payment->orders[$i]['paid_amount'], true, $supplier_payment->orders[$i]['paid_currency'], $supplier_payment->orders[$i]['paid_currency_value']) . '</td>
													  				</tr>';
	}
?>
																</table>
			                								</td>
			                							</tr>
			              							</table>
            									</td>
            								</tr>
			            				</table>
			            			</td>
			            		</tr>
          					</table>
          				</td>
          			</tr>
          			<tr>
  						<td class="main"><?=TEXT_REAL_TIME_STAT_DESC?></td>
  					</tr>
          			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
            			<td><?=tep_draw_separator()?></td>
          			</tr>
          			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
      				</tr>
      				<tr>
				  		<td class="main">
        					<table border="1" cellspacing="0" cellpadding="5">
          						<tr>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_DATE_ADDED?></b></td>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_SUPPLIER_NOTIFIED?></b></td>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_STATUS?></b></td>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_COMMENTS?></b></td>
            						<td class="smallText" align="center"><b><?=TABLE_HEADING_CHANGED_BY?></b></td>
          						</tr>
<?
	$payment_history_select_sql = "SELECT * FROM " . TABLE_SUPPLIER_PAYMENTS_HISTORY . " WHERE supplier_payments_id = '" . $payment_id . "' ORDER BY date_added";
	$payment_history_result_sql = tep_db_query($payment_history_select_sql);
	while ($payment_history_row = tep_db_fetch_array($payment_history_result_sql)) {
		$formatted_date_comment_added = tep_datetime_short($payment_history_row["date_added"], PREFERRED_DATE_TIME_FORMAT);
		$img_str = ($payment_history_row['supplier_notified'] == '1') ? tep_image(DIR_WS_ICONS . 'tick.gif', ICON_TICK) : tep_image(DIR_WS_ICONS . 'cross.gif', ICON_CROSS);
?>
								<tr>
            						<td class="smallText" align="center"><?=(tep_not_null($formatted_date_comment_added) ? $formatted_date_comment_added : '--')?></td>
            						<td class="smallText" align="center"><?=$img_str?></td>
            						<td class="smallText" align="center"><?=$payment_status_array[$payment_history_row['supplier_payments_status']]?></td>
            						<td class="smallText"><?=nl2br(str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $payment_history_row['comments']))?>&nbsp;</td>
            						<td class="smallText" align="center"><?=nl2br(tep_db_output($payment_history_row['changed_by']))?>&nbsp;</td>
          						</tr>
<?	} ?>
							</table>
          				</td>
				  	</tr>
				  	<tr>
				  		<td>
<?
	echo tep_draw_form('status_update_form', FILENAME_SUPPLIERS_PAYMENT, tep_get_all_get_params(array('subaction')), 'post', '');
	echo tep_draw_hidden_field('subaction', 'update_payment');
	echo tep_draw_hidden_field('status_DB_prev', $supplier_payment->info['payments_status']);
	
	$payments_statuses_new = array();
	switch ($supplier_payment->info['payments_status']) {
		case 1:	// Pending
			$payments_statuses_new[] = array(	'id' => "0",
												'text' => SELECT_OPTION_UPDATE_COMMENT);
			if ($reverse_payment_permission) {
				$payments_statuses_new[] = array(	'id' => "3",
													'text' => 'Reverse');
			}
			break;
		case 2:	// Paid
			$payments_statuses_new[] = array(	'id' => "0",
												'text' => SELECT_OPTION_UPDATE_COMMENT);
			if ($reverse_payment_permission) {
				$payments_statuses_new[] = array(	'id' => "3",
													'text' => 'Reverse');
			}
			break;
		case 3:	// Reverse
			$payments_statuses_new[] = array(	'id' => "0",
												'text' => SELECT_OPTION_UPDATE_COMMENT);
			break;
	}
	
	if (count($payments_statuses_new)) {
?>
				  			<table border="0" cellspacing="0" cellpadding="2">
				  				<tr>
				  					<td class="main" colspan="3"><b><?=ENTRY_ADMIN_COMMENT?></b></td>
				  				</tr>	
				  				<tr>
				  					<td class="main" colspan="3"><?=tep_draw_textarea_field('admin_comment', 'soft', '60', '5')?></td>
				  				</tr>
				  				<tr>
				  					<td width="40%" class="main"><b><?=ENTRY_PAYMENT_STATUS?></b></td>
									<td><?=tep_draw_pull_down_menu('status_now', $payments_statuses_new, '', ' id="status_now"')?></td>
									<td class="main">&nbsp;</td>
								</tr>
				  				<tr>
				  					<td class="main"><b><?=ENTRY_NOTIFY_SUPPLIER?></b></td>
				  					<td class="main"><?=tep_draw_checkbox_field('notify', '1', false, '', 'id=notify')?></td>
				  					<td align="right" class="main">
				  						<?=tep_submit_button(BUTTON_UPDATE, ALT_BUTTON_UPDATE, 'name="PaymentUpdateBtn"', 'inputButton')?>
				  					</td>
				  				</tr>
				  			</table>
<?	} ?>
				  		</form>
				  		</td>
				  	</tr>
<?		echo '		<tr>
						<td align="right">
							<a href="' . $back_btn_url . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>
	 					</td>
	 				</tr>';
} else if ($_REQUEST['action'] == 'show_report') {
	if ($perform_unserialize_criteria) {
		if (isset($_SESSION['sup_payment_param']["criteria_id"]) && is_numeric($_SESSION['sup_payment_param']["criteria_id"])) {
			$search_criteria_select_sql = "SELECT search_criteria_name, search_criteria_string FROM " . TABLE_SEARCH_CRITERIA . " WHERE search_criteria_id = '" . (int)$_SESSION['sup_payment_param']["criteria_id"] . "' AND filename = '" . tep_db_input(basename($PHP_SELF)) . "'";
			$search_criteria_result_sql = tep_db_query($search_criteria_select_sql);
			
			if ($search_criteria_row = tep_db_fetch_array($search_criteria_result_sql)) {
				$_SESSION['sup_payment_param']["cur_criteria_name"] = $search_criteria_row["search_criteria_name"];
				$criteria_array = array();
				$criteria_array = explode('#~#', $search_criteria_row["search_criteria_string"]);
				
				unset($_SESSION['sup_payment_param']["start_date"]);
				unset($_SESSION['sup_payment_param']["end_date"]);
				unset($_SESSION['sup_payment_param']["payment_id"]);
				unset($_SESSION['sup_payment_param']["order_id"]);
				unset($_SESSION['sup_payment_param']["supplier_group"]);
				unset($_SESSION['sup_payment_param']["supplier"]);
				unset($_SESSION['sup_payment_param']["sort_by"]);
  				unset($_SESSION['sup_payment_param']["sort_order"]);
				unset($_SESSION['sup_payment_param']["show_records"]);
				
				for ($s=0; $s < count($criteria_array); $s++) {
					list($serialized_key, $serialized_value) = explode(':~:', $criteria_array[$s]);
					$unserialized_key = tep_array_unserialize($serialized_key);
					$serialized_key_value = tep_array_unserialize($serialized_value);
					
					$_SESSION['sup_payment_param'][$unserialized_key] = $serialized_key_value;
				}
			}
		}
		$perform_unserialize_criteria = false;
	}
	
	$payment_id_str = (isset($_SESSION['sup_payment_param']["payment_id"]) && tep_not_null($_SESSION['sup_payment_param']["payment_id"])) ? " sp.supplier_payments_id='" . $_SESSION['sup_payment_param']["payment_id"] . "'" : "1";
	$order_id_str = (isset($_SESSION['sup_payment_param']["order_id"]) && tep_not_null($_SESSION['sup_payment_param']["order_id"])) ? " spo.supplier_order_lists_id = '".$_SESSION['sup_payment_param']["order_id"]."'" : "1";
  	$supplier_str = (isset($_SESSION['sup_payment_param']["supplier"]) && tep_not_null($_SESSION['sup_payment_param']["supplier"])) ? " sp.suppliers_id='" . $_SESSION['sup_payment_param']["supplier"] . "'" : "1";
  	$supplier_group_str = (isset($_SESSION['sup_payment_param']["supplier_group"]) && tep_not_null($_SESSION['sup_payment_param']["supplier_group"])) ? " s.supplier_groups_id='" . $_SESSION['sup_payment_param']["supplier_group"] . "'" : "1";
  	
	$comparing_date_field = 'sph.date_added';
	$result_display_text = TEXT_DISPLAY_NUMBER_OF_TRANSACTIONS;
  	
  	if (tep_not_null($_SESSION['sup_payment_param']["start_date"])) {
		if (strpos($_SESSION['sup_payment_param']["start_date"], ':') !== false) {
			$startDateObj = explode(' ', trim($_SESSION['sup_payment_param']["start_date"]));
			list($yr, $mth, $day) = explode('-', $startDateObj[0]);
			list($hr, $min) = explode(':', $startDateObj[1]);
			$start_date_str = " ( $comparing_date_field >= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['sup_payment_param']["start_date"]));
			$start_date_str = " ( DATE_FORMAT($comparing_date_field, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
		}
	} else {
		$start_date_str = " 1 ";
	}
	
	if (tep_not_null($_SESSION['sup_payment_param']["end_date"])) {
		if (strpos($_SESSION['sup_payment_param']["end_date"], ':') !== false) {
			$endDateObj = explode(' ', trim($_SESSION['sup_payment_param']["end_date"]));
			list($yr, $mth, $day) = explode('-', $endDateObj[0]);
			list($hr, $min) = explode(':', $endDateObj[1]);
			$end_date_str = " ( $comparing_date_field <= '".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."' )";
		} else {
			list($yr, $mth, $day) = explode('-', trim($_SESSION['sup_payment_param']["end_date"]));
			$end_date_str = " ( DATE_FORMAT($comparing_date_field, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
		}
	} else {
		$end_date_str = " 1 ";
	}
	
	$payments_select_str = "select sp.supplier_payments_id, sp.suppliers_id, sp.suppliers_firstname, sp.suppliers_lastname, sp.supplier_payments_amount, sp.supplier_payments_total, sp.suppliers_email_address, DATE_FORMAT(sp.supplier_payments_date, '%Y-%m-%d %H:%i') AS payment_date, sp.currency, sp.currency_value, sp.supplier_payments_status as cur_status, sph.supplier_payments_history_id as trans_id, DATE_FORMAT(sph.date_added, '%Y-%m-%d %H:%i') AS trans_date, sph.comments, sph.supplier_payments_status as p_status from " . TABLE_SUPPLIER_PAYMENTS . " as sp inner join " . TABLE_SUPPLIER_PAYMENTS_HISTORY . " as sph on (sp.supplier_payments_id=sph.supplier_payments_id and sph.supplier_payments_status>0) " . (tep_not_null($_SESSION['sup_payment_param']["order_id"]) ? "inner join " . TABLE_SUPPLIER_PAYMENTS_ORDERS . " as spo on (sp. supplier_payments_id=spo.supplier_payments_id)" : '') . " left join " . TABLE_SUPPLIER . " as s on sp.suppliers_id=s.supplier_id ";
	$payments_order_by_str = " order by sph.date_added ";
	
	$history_title = TEXT_MODE_T;
	
  	$payments_where_str = " where 1 ";
  	$payments_where_str .= " and $payment_id_str and $order_id_str and $start_date_str and $end_date_str and $supplier_str and $supplier_group_str ";
  	
  	$payments_order_by_str = '';
  	if (tep_not_null($_SESSION['sup_payment_param']["sort_by"])) {
  		if ($_SESSION['sup_payment_param']["sort_by"] == 'date') {
  			$payments_order_by_str = ' order by ' . $comparing_date_field . ' ' . $_SESSION['sup_payment_param']["sort_order"];
  		}
  	}
  	
  	$payments_group_by_str = " ";
  	
  	$show_records = $_SESSION['sup_payment_param']["show_records"];
?>
					<tr>
            			<td valign="top" class="pageHeading"><?=HEADING_TITLE?>
            			<?
            				if ($manage_search_criteria_permission) {	// check for permission
            					if (tep_not_null($_SESSION['sup_payment_param']["cur_criteria_name"]) && tep_not_null($_SESSION['sup_payment_param']["criteria_id"])) {
									echo '<br><a href="javascript:void(confirm_delete(\'\', \'this search criteria\', \''.tep_href_link(FILENAME_SUPPLIERS_PAYMENT, 'action=delete_search&criteria_id='.$_SESSION['sup_payment_param']["criteria_id"]).'\'))">Forget &lt;'.$_SESSION['sup_payment_param']["cur_criteria_name"].'&gt; Criteria</a>'; 
								}
							}
						?>
						</td>
            			<td class="smallText" align="right" valign="top">&nbsp;
						<?/*
							echo tep_draw_form('orders', FILENAME_SUPPLIERS_ORDERS, '', 'post');
							echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('oID', '', 'size="12"') . tep_draw_hidden_field('subaction', 'change_order');
							echo "</form><br>";
							if (count($saved_search_options) > 1) {
								echo tep_draw_form('goto_search_form', FILENAME_SUPPLIERS_PAYMENT, 'action=show_report', 'post');
								echo tep_draw_hidden_field('subaction', 'goto_search');
								echo tep_draw_pull_down_menu("criteria_id", $saved_search_options, tep_not_null($_SESSION['sup_payment_param']["criteria_id"]) ? $_SESSION['sup_payment_param']["criteria_id"] : '', 'onChange="if(this.value != \'\') { this.form.submit(); }"');
								echo "</form>";
							}
							*/
						?>
						</td>
          			</tr>
<?
	$extra_detail = array ();
	$js = '';
	
	$total_payment_amt = 0;
	$list_colspan_count = 6;
	
	$list_colspan_count++;
?>
					<tr>
          				<td colspan="2">
							<table border="0" width="100%" cellspacing="0" cellpadding="2" style="border-collapse: collapse;">
  								<tr>
    								<td colspan="<?=$list_colspan_count?>">
    									<span class="pageHeading"><?=$history_title?></span>
<?	echo '<span id="'.$order_status_id.'_sol_nav"></span>'; ?>
									</td>
			  					</tr>
								<tr>
									<td width="7%" class="ordersBoxHeading"><?=TABLE_HEADING_PAYMENT_NO?></td>
									<td width="11%" class="ordersBoxHeading"><?=TABLE_HEADING_TRANS_DATE?></td>
								    <td class="ordersBoxHeading"><?=TABLE_HEADING_PAYEE?></td>
								    <td width="18%" class="ordersBoxHeading"><?=TABLE_HEADING_PAYEE_EMAIL?></td>
							    	<td class="ordersBoxHeading"><?=TABLE_HEADING_REMARK?></td>
								    <td width="10%" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_TRANS_AMOUNT?></td>
									<td width="5%" class="ordersBoxHeading" align="center">Actions</td>
								</tr>
<?
	$payments_select_sql = $payments_select_str . $payments_where_str . $payments_group_by_str . $payments_order_by_str;
	
	if ($show_records != "ALL") {
		$payments_split_object = new splitPageResults($HTTP_GET_VARS['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $payments_select_sql, $payments_select_sql_numrows, true);
	}
	$payments_result_sql = tep_db_query($payments_select_sql);
	
	$row_count = 0;
	
	while ($row = tep_db_fetch_array($payments_result_sql)) {
		$payment_number = $row['supplier_payments_id'];
		
		$unique_product_reference = $row_count . '_' . $payment_number;
		
		$payment_date = $row['trans_date'];
		$payee = $row['suppliers_firstname'].' ' . $row['suppliers_lastname'];
		$payee_email = $row['suppliers_email_address'];
		$payment_amt = $row['supplier_payments_total'];
		
		$extra_detail['id'][] = $payment_number;
		
		$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
		
		$is_credit_amount = $row['p_status'] == '3' ? true : false;
		
		if ($is_credit_amount) {
			$total_payment_amt -= $payment_amt;
		} else {
			$total_payment_amt += $payment_amt;
		}
?>
								<tr id="<?='main_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?='sub_'.$row_count?>##<?='sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?='sub_'.$row_count?>##<?='sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?='sub_'.$row_count?>##<?='sub2_'.$row_count?>')">
									<td class="ordersRecords" nowrap><a href="javascript:;" onClick="supplierPaymentOrderInfo('<?=$unique_product_reference?>', '<?=$payment_number?>', '<?=$row['trans_id']?>', '<?=(int)$languages_id?>', '<?=SID?>', '<?=$login_id?>');"><?=$payment_number?></a></td>
							      	<td class="ordersRecords" nowrap><?=$payment_date?></td>
							      	<td class="ordersRecords"><?=$payee?></td>
							      	<td class="ordersRecords"><?=$payee_email?></td>
									<td class="ordersRecords"><?=$row['comments']?></td>
							      	<td class="ordersRecords" align="right">
							      		<a href="javascript:;" onClick="supplierPaymentOrderInfo('<?=$unique_product_reference?>', '<?=$payment_number?>', '<?=$row['trans_id']?>', '<?=(int)$languages_id?>', '<?=SID?>', '<?=$login_id?>');"><?=sprintf($is_credit_amount ? '(%s)' : '%s', $currencies->format($payment_amt, true, $row['currency'], $row['currency_value']))?></a>
							      	</td>
									<td class="ordersRecords" align="center" nowrap>
			  						<?	echo '<a href="' . tep_href_link(FILENAME_SUPPLIERS_PAYMENT, 'payID='.$payment_number.'&action=edit', 'NONSSL') . '">'.tep_image(DIR_WS_ICONS."edit.gif", "Edit", "14", "13", 'align="top"').'</a>'; ?>
			  						</td>
								</tr>
								<tbody id="<?=$unique_product_reference.'_order_sec'?>" class="hide">
                    				<tr id="<?='sub_'.$row_count?>" class="<?=$row_style?>" onmouseover="showOverEffect(this, 'ordersListingRowOver', '<?='main_'.$row_count?>##<?='sub2_'.$row_count?>')" onmouseout="showOutEffect(this, '<?=$row_style?>', '<?='main_'.$row_count?>##<?='sub2_'.$row_count?>')" onclick="showClicked(this, '<?=$row_style?>', '<?='main_'.$row_count?>##<?='sub2_'.$row_count?>')">
                    					<td>&nbsp;</td>
			  							<td class="ordersRecords" colspan="<?=($list_colspan_count-4)?>" valign="top">
			  								<div id="<?=$unique_product_reference.'_p_info'?>"></div>
			  							</td>
			  							<td colspan="2">
			  								<div id="<?=$unique_product_reference.'_order'?>"></div>
			  							</td>
			  							<td>&nbsp;</td>
			  						</tr>
				               	</tbody>
<?
		$row_count++;
	}
	
	echo '					<tbody>
								<tr>
									<td class="ordersRecords" colspan="'.($list_colspan_count-2).'">&nbsp;</td>
									<td align="right" class="ordersRecords"><b>'.sprintf($total_payment_amt < 0 ? '(%s)' : '%s', $currencies->format(abs($total_payment_amt))).'</b></td>
									<td class="ordersRecords">&nbsp;</td>
								</tr>
							</tbody>';
?>
							</table>
						</form>
						</td>
					</tr>
					<tr>
            			<td colspan="2">
            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
              					<tr>
                					<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf($result_display_text, tep_db_num_rows($payments_result_sql) > 0 ? "1" : "0", tep_db_num_rows($payments_result_sql), tep_db_num_rows($payments_result_sql)) : $payments_split_object->display_count($payments_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int)$show_records), $HTTP_GET_VARS['page'], $result_display_text)?></td>
                					<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $payments_split_object->display_links($payments_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page', 'cont', 'subaction', 'criteria_id'))."cont=1", 'page')?></td>
              					</tr>
            				</table>
            			</td>
					</tr>
	          		<script language="javascript">
					<!--
						function hideShow(groupName, styleClass) {
							var row_count = eval(groupName+"_count");
							for (var i=0; i<row_count; i++) {
								document.getElementById(groupName+"_"+i).className = styleClass;
							}
							
							if (styleClass == "show") {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'hide')\">Hide Ordered Details</a>";
								SetCookie(groupName, '1');
							} else {
								document.getElementById(groupName+'_nav').innerHTML = "<a href=\"javascript:;\" onClick=\"hideShow('"+groupName+"', 'show')\">Show Ordered Details</a>";
								SetCookie(groupName, '0');
							}
						}
						
					<?	if (tep_not_null($_SESSION['sup_payment_param']["page_refresh"])) { ?>
							var page_url = "<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('oID', 'cont'))."cont=1")?>";
							setAutoRefresh("<?=$_SESSION['sup_payment_param']["page_refresh"]?>", page_url);
					<?	} ?>
					//-->
					</script>
<?
} else {
?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
									<td class="main" align="right">&nbsp;
									<?
									if (count($saved_search_options) > 1) {
										echo tep_draw_form('goto_search_form', FILENAME_SUPPLIERS_PAYMENT, 'action=show_report', 'post');
										echo tep_draw_hidden_field('subaction', 'goto_search');
										echo tep_draw_pull_down_menu("criteria_id", $saved_search_options, '', 'onChange="if(this.value != \'\') { this.form.submit(); }"');
										echo "</form>";
									}
									?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
<?	echo tep_draw_form('supplier_payments_criteria', FILENAME_SUPPLIERS_PAYMENT, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', ''); ?>
					<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="2" cellpadding="0">
<?
	$sort_by_array = array	( 	array ('id' => 'date', "text" => "Transaction Date")
						  	);
	
	$show_options = array 	(	array ('id' => 'DEFAULT', "text" => "Default"),
								array ('id' => '10', "text" => "10"),
								array ('id' => '20', "text" => "20"),
								array ('id' => '50', "text" => "50"),
								array ('id' => 'ALL', "text" => "All")
							);
	
	$supplier_group_options = array( array ('id' => '', "text" => "All Supplier Groups") );
	$sup_group_select_sql = "SELECT supplier_groups_id, supplier_groups_name FROM " . TABLE_SUPPLIER_GROUPS ." ORDER BY supplier_groups_name";
	$sup_group_result_sql = tep_db_query($sup_group_select_sql);
	while($sup_group_row = tep_db_fetch_array($sup_group_result_sql)) {
		$supplier_group_options[] = array('text' => $sup_group_row['supplier_groups_name'], 'id' => $sup_group_row['supplier_groups_id']);
	}
	
	$supplier_options = array( array ('id' => '', "text" => "All Suppliers") );
	$sup_select_sql = "SELECT supplier_id, supplier_code, supplier_firstname, supplier_lastname FROM " . TABLE_SUPPLIER ." ORDER BY supplier_firstname";
	$sup_result_sql = tep_db_query($sup_select_sql);
	while($sup_row = tep_db_fetch_array($sup_result_sql)) {
		$supplier_options[] = array('text' => $sup_row['supplier_firstname'] . '  ' . $sup_row['supplier_lastname'] . (tep_not_null($sup_row['supplier_code']) ? ' ['.$sup_row['supplier_code'].']' : ''), 'id' => $sup_row['supplier_id']);
	}
?>
								<tr>
									<td class="main" width="12%"><?=ENTRY_ORDER_START_DATE?></td>
					    			<td class="main">
					    				<table border="0" cellspacing="2" cellpadding="0">
					    					<tr>
					    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('start_date', $_SESSION['sup_payment_param']["start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.supplier_payments_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.supplier_payments_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
					    						<td class="main" width="5%">&nbsp;</td>
					    						<td class="main" valign="top" nowrap><?=ENTRY_ORDER_END_DATE?></td>
					    						<td class="main" width="1%">&nbsp;</td>
					    						<td class="main" valign="top" nowrap><?=tep_draw_input_field('end_date', $_SESSION['sup_payment_param']["end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.supplier_payments_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.supplier_payments_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
					    					</tr>
					    				</table>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main"><?=ENTRY_PAYMENT_ID?></td>
					    			<td class="main"><?=tep_draw_input_field('payment_id', $_SESSION['sup_payment_param']["payment_id"], ' id="payment_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
								<tr>
									<td class="main"><?=ENTRY_ORDER_ID?></td>
					    			<td class="main"><?=tep_draw_input_field('order_id', $_SESSION['sup_payment_param']["order_id"], ' id="order_id" size="15" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } else { resetControls(this); }" onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } else { resetControls(this); }" onKeyPress="return noEnterKey(event)"')?></td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main" width="12%"><?=ENTRY_SUPPLIER_GROUP?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("supplier_group", $supplier_group_options, tep_not_null($_SESSION['sup_payment_param']["supplier_group"]) ? $_SESSION['sup_payment_param']["supplier_group"] : '', '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main" width="12%"><?=ENTRY_SUPPLIER?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("supplier", $supplier_options, tep_not_null($_SESSION['sup_payment_param']["supplier"]) ? $_SESSION['sup_payment_param']["supplier"] : '', '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main"><?=ENTRY_SORT?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("sort_by", $sort_by_array, $_SESSION['sup_payment_param']["sort_by"], '')?>
					    				<?=tep_draw_radio_field('sort_order', 'ASC', isset($_SESSION['sup_payment_param']) ? ($_SESSION['sup_payment_param']["sort_order"]=='ASC' ? "checked" : '') : '') . "&nbsp;" . TEXT_ASC . "&nbsp;" . tep_draw_radio_field('sort_order', 'DESC', isset($_SESSION['sup_payment_param']) ? ($_SESSION['sup_payment_param']["sort_order"]=='DESC' ? "checked" : '') : "checked") . "&nbsp;" . TEXT_DESC?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
          						<tr>
									<td class="main"><?=ENTRY_RECORDS_PER_PAGE?></td>
					    			<td class="main">
					    				<?=tep_draw_pull_down_menu("show_records", $show_options, tep_not_null($_SESSION['sup_payment_param']["show_records"]) ? $_SESSION['sup_payment_param']["show_records"] : '', '')?>
					    			</td>
								</tr>
								<tr>
            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          						</tr>
        					</table>
        				</td>
        			</tr>
        			<tr>
	  					<td>
	  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
	  							<tr>
	  								<td align="right">
	  									<?=tep_image_submit('button_search.gif', IMAGE_SEARCH, " id='OrderListsBtn' onClick=\"return form_checking(this.form, 'do_search');\"")?>&nbsp;&nbsp;
	  									<a href="<?=tep_href_link(FILENAME_SUPPLIERS_PAYMENT, 'action=reset_session')?>"><?=tep_image_button('button_reset.gif', IMAGE_RESET)?></a>
	  								</td>
	  							</tr>
	  						</table>
	  					</td>
	  				</tr>
	  				<tr>
						<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
					</tr>
					</form>
					<script language="javascript"><!--
						function form_checking(form_obj, action) {
							/*
							if (action=='save_search') {
								if (trim_str(document.getElementById('search_name').value) != '') {
									document.getElementById('supplier_order_list_subaction').value = 'save_search';
									form_obj.SaveSearchBtn.disabled = true;
									form_obj.SaveSearchBtn.value = 'Please wait...';
								} else {
									alert(" You must enter a name for saving your search criteria!");
									document.getElementById('search_name').value = '';
									document.getElementById('search_name').focus();
									return false;
								}
							} else {
								document.getElementById('supplier_order_list_subaction').value = 'do_search';
							}
							*/
						    form_obj.submit();
							return true;
			    		}
			    		
						function resetControls(controlObj) {
							if (trim_str(controlObj.value) != '') {
								if (controlObj.id == 'payment_id') {
									document.supplier_payments_criteria.order_id.value = '';
								} else {
									document.supplier_payments_criteria.payment_id.value = '';
								}
								document.supplier_payments_criteria.start_date.value = '';
								document.supplier_payments_criteria.end_date.value = '';
								document.supplier_payments_criteria.supplier_group.selectedIndex = 0;
				    			document.supplier_payments_criteria.supplier.selectedIndex = 0;
				    			document.supplier_payments_criteria.show_records.selectedIndex = 0;
				    		} else {
				    			controlObj.value = '';
				    		}
						}
			    	//-->
					</script>
<?
}
?>
					<script language="javascript">
					<!--
						function showOverEffect(object, class_name, extra_row) {
							rowOverEffect(object, class_name);
							var rowObjArray = extra_row.split('##');
	  						for (var i = 0; i < rowObjArray.length; i++) {
	  							if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
	  								rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
	  							}
	  						}
						}
						
						function showOutEffect(object, class_name, extra_row) {
							rowOutEffect(object, class_name);
							var rowObjArray = extra_row.split('##');
					  		for (var i = 0; i < rowObjArray.length; i++) {
					  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					  				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
					  			}
					  		}
						}
						
						function showClicked(object, class_name, extra_row) {
							rowClicked(object, class_name);
							
							var rowObjArray = extra_row.split('##');
					  		for (var i = 0; i < rowObjArray.length; i++) {
					  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
					  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
					  			}
	  						}
						}
					//-->
					</script>
				</table>
<!-- body_text_eof //-->
  			</td>
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>