<?php
/*
  	$Id: suppliers_pricing.php,v 1.21 2006/09/26 08:03:00 sunny Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_FUNCTIONS . 'supplier.php');
require(DIR_WS_CLASSES . 'currencies.php');

define('DISPLAY_PRICE_DECIMAL', 4);

$currencies = new currencies();
$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$subaction = (isset($_GET['subaction']) ? $_GET['subaction'] : '');

$preview_mode = ($action == "list_preview") ? true : false;

$product_purchase_status_array = array(	array('id'=>'', 'text'=>'Please select'),
										array('id'=>'urgent', 'text'=>'Urgent'),
										array('id'=>'important', 'text'=>'Important'),
										array('id'=>'normal', 'text'=>'Normal')
									);

$restock_account_info_permission = tep_admin_files_actions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'SUPPLIER_RESTOCK_ACCOUNT_INFO');
$buyback_price_info_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_PRICING, 'SUPPLIER_BUYBACK_PRICE_INFO');
$unit_price_info_permission = tep_admin_files_actions(FILENAME_SUPPLIERS_PRICING, 'SUPPLIER_PRICING_UNIT_PRICE_INFO');

if (tep_not_null($_REQUEST["list_id"])) {
	$list_cat_select_sql = "SELECT products_purchases_lists_cat_id 
							FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " 
							WHERE products_purchases_lists_id='".(int)$_REQUEST["list_id"]."'";
	$list_cat_result_sql = tep_db_query($list_cat_select_sql);
	
	if ($list_cat_row = tep_db_fetch_array($list_cat_result_sql)) {
		if (tep_check_cat_tree_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, $list_cat_row["products_purchases_lists_cat_id"]) != 1) {
			$messageStack->add_session(ERROR_LIST_ACCESS_DENIED, 'error');
			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING));
		}
	} else {
		$messageStack->add_session(ERROR_LIST_NOT_EXISTS, 'error');
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING));
	}
}

if (tep_not_null($subaction)) {
	switch ($subaction) {
		case "update_pricing":
			$supplier_groups_id = $_POST["supplier_groups_id"];
			$list_id = $_POST["list_id"];
			
			$supplier_pricing_setting_select_sql = "SELECT supplier_pricing_setting_key, supplier_pricing_setting_value FROM " . TABLE_SUPPLIER_PRICING_SETTING . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'";
			$supplier_pricing_setting_result_sql = tep_db_query($supplier_pricing_setting_select_sql);
			while ($supplier_pricing_setting_row = tep_db_fetch_array($supplier_pricing_setting_result_sql)) {
				$pricing_setting_array[$supplier_pricing_setting_row["supplier_pricing_setting_key"]] = $supplier_pricing_setting_row["supplier_pricing_setting_value"];
			}
			
			if ($HTTP_POST_VARS['btn_csv_import']) {
				if ( tep_not_null($_FILES['csv_import']['tmp_name']) && ($_FILES['csv_import']['tmp_name'] != 'none') && is_uploaded_file($_FILES['csv_import']['tmp_name']) ) {
					if ($_FILES['csv_import']["size"] > 0) {
						$import_error = false;
						$filename = ($_FILES['csv_import']['tmp_name']);
					    $handle = fopen($filename, 'r+');
					    
					    $supplier_group_check_sql = "SELECT supplier_groups_id, show_products_purchase_demand_status AS show_status FROM " . TABLE_SUPPLIER_GROUPS . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "'";
						$supplier_group_check_result_sql = tep_db_query($supplier_group_check_sql);
						$supplier_group_check_row = tep_db_fetch_array($supplier_group_check_result_sql);
						
						$must_have_field = array(TABLE_CSV_HEADING_PRODUCT_ID => 0, TABLE_CSV_HEADING_OVERWRITE_MAX_QTY => 0);
						if ($supplier_group_check_row["show_status"] == '1')	$must_have_field[TABLE_CSV_HEADING_STATUS] = 0;
						if ($unit_price_info_permission)	$must_have_field[TABLE_CSV_HEADING_UNIT_PRICE] = 0;
						
					    if (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    	$header_exists_count = 0;
					    	for ($i=0; $i < count($data); $i++) {
					    		if (in_array(trim($data[$i]), array_keys($must_have_field))) {
					    			$must_have_field[trim($data[$i])] = $i;
					    			$header_exists_count++;
					    		}
					    	}
					    	
					    	if ($header_exists_count != count($must_have_field)) {
					    		$messageStack->add_session("Some required fields does not exists. Please ensure your imported csv file contains " . implode(", ", array_keys($must_have_field)) . ".", 'error');
					    		$import_error = true;
					    	}
					    }
					    
					    if (!$import_error) {
					    	$exist_product_info = array();
					    	$imported_products_array = array();
					    	
					    	$active_products_select_sql = "	SELECT sp.products_id, sp.supplier_pricing_server_full, sp.supplier_pricing_disabled, p.products_buyback_price, pd.products_name, p.products_cat_path, pp.products_purchases_selling_quantity AS selling_qty, pp.products_purchases_disabled, rci.restock_character_sets_id, rci.restock_character 
					    									FROM " . TABLE_SUPPLIER_PRICING . " AS sp 
					    									INNER JOIN " . TABLE_PRODUCTS . " AS p 
					    										ON (sp.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0) 
					    									INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
																ON sp.products_id=pd.products_id 
					    									INNER JOIN ".TABLE_PRODUCTS_PURCHASES." as pp 
																ON (pp.products_purchases_lists_id='".$list_id."' AND p.products_id=pp.products_id AND pp.products_purchases_disabled <> 1) 
															LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci 
																ON (sp.products_id=rci.products_id AND rci.restock_character_sets_id='" . tep_db_input($pricing_setting_array[KEY_SPS_RSTK_CHAR_SET]) . "') 
					    									WHERE sp.supplier_groups_id='" . $supplier_groups_id . "' AND sp.products_purchases_lists_id='".$list_id."' AND pd.language_id = '" . $languages_id . "'";
							$active_products_result_sql = tep_db_query($active_products_select_sql);
					  		while ($active_products_row = tep_db_fetch_array($active_products_result_sql)) {
					  			$exist_product_info[$active_products_row["products_id"]] = $active_products_row;
							}
							
					    	while (($data = fgetcsv($handle, 1024, ',', '"')) !== FALSE) {
					    		if (trim($data[0]) == '') {	// Assume this row is useless
				    				continue;
				    			}
				    			
				    			$product_id_str = $data[$must_have_field[TABLE_CSV_HEADING_PRODUCT_ID]];
				    			$overwrite_max_qty_str = $data[$must_have_field[TABLE_CSV_HEADING_OVERWRITE_MAX_QTY]];
				    			if ($unit_price_info_permission)	$unit_price_str = $data[$must_have_field[TABLE_CSV_HEADING_UNIT_PRICE]];
								$status_str = strtolower(trim($data[$must_have_field[TABLE_CSV_HEADING_STATUS]]));
								
								if (isset($pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY]) && $pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY] == 1) {
									$group_max_qty = $pricing_setting_array[KEY_SPS_MFC_SUPPLY_MAX_QTY];
								} else {
									$group_max_qty = ceil(((int)$exist_product_info[(int)$product_id_str]['selling_qty'] * (double)$pricing_setting_array[KEY_SPS_QUANTITY_RATIO]) / 100);
								}
								/**************************************************************************
									NOTE: 1. supplier_pricing_disabled is set to 0 means all the products
											 of this group matched the imported csv file need to be updated
											 regardless of it disabled status.
								****************************************************************************/
					    		$imported_products_array[] = array(	'prd_id' => (int)$product_id_str,
					    											'prd_full_path' => $exist_product_info[(int)$product_id_str]["products_cat_path"],
					    											'products_buyback_price' => $exist_product_info[(int)$product_id_str]["products_buyback_price"],
					    											'supplier_pricing_product_status' => $status_str,
					    											'group_max_qty' => (int)$group_max_qty,
					    											'supplier_pricing_max_quantity' => $overwrite_max_qty_str,
					    											'supplier_pricing_unit_price' => (double)$unit_price_str,
					    											'supplier_pricing_server_full' => (int)$exist_product_info[(int)$product_id_str]["supplier_pricing_server_full"],
					    											'supplier_pricing_disabled' => 0,
					    											'restock_character' => $exist_product_info[(int)$product_id_str]["restock_character"],
					    											'restock_character_exists' => is_null($exist_product_info[(int)$product_id_str]["restock_character_sets_id"]) ? '0' : '1',
					    											'products_purchases_disabled' => $exist_product_info[(int)$product_id_str]["products_purchases_disabled"],
					    											);
					    		
					    		if (!in_array((int)$product_id_str, array_keys($exist_product_info))) {
					    			$messageStack->add_session("Product ID: " . (int)$product_id_str . " does not match the Supplier Pricing's product id!" , 'error');
					    			$import_error = true;
					    		} else {
					    			$product_name = $exist_product_info[(int)$product_id_str]['products_cat_path']." > ".$exist_product_info[(int)$product_id_str]['products_name'];
					    			
					    			if ((int)$exist_product_info[(int)$product_id_str]["products_purchases_disabled"] == "1") {
					    				$messageStack->add_session(sprintf(ERROR_PRODUCT_DISABLED, $product_name) , 'error');
						    			$import_error = true;
					    			}
					    			
						    		if ($supplier_group_check_row["show_status"] == '1') {
						    			if (tep_not_null($status_str)) {
								    		$found = false;
								    		for ($s_cnt=1; $s_cnt < count($product_purchase_status_array); $s_cnt++) {
								    			if ($status_str == $product_purchase_status_array[$s_cnt]['id']) {
								    				$found = true;
								    				break;
								    			}
								    		}
								    		
								    		if (!$found) {
								    			$messageStack->add_session(sprintf(ERROR_INVALID_SERVER_STATUS, $status_str, $product_name), 'error');
							    				$import_error = true;
								    		}
							    		}
						    		}
						    		
						    		if (tep_not_null($overwrite_max_qty_str)) {
							    		if (!preg_match('/[1-9][0-9]*/is', $overwrite_max_qty_str) && $overwrite_max_qty_str != '0') {
							    			$messageStack->add_session(sprintf(ERROR_INVALID_OVERWRITE_MAXIMUM_QUANTITY, $product_name), 'error');
							    			$import_error = true;
							    		}
						    		}
						    		
						    		if ($unit_price_info_permission && !preg_match('/[0-9]+\.[0-9]+/is', $unit_price_str) && $unit_price_str != '0') {
						    			$messageStack->add_session(sprintf(ERROR_INVALID_UNIT_PRICE, $product_name) , 'error');
						    			$import_error = true;
						    		}
						    	}
					    	}
					    	
					    	fclose($handle);
					    	
					    	if ($import_error) {
					    		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=suppliers_pricing&supplier_groups_id='.$supplier_groups_id.'&list_id='.$list_id));
					    	}
					    } else {
					    	fclose($handle);
							tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=suppliers_pricing&supplier_groups_id='.$supplier_groups_id.'&list_id='.$list_id));
					    }
					} else {
						$messageStack->add_session(ERROR_NO_UPLOAD_FILE, 'error');
						tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=suppliers_pricing&supplier_groups_id='.$supplier_groups_id.'&list_id='.$list_id));
					}
				} else {
					$messageStack->add_session(WARNING_NO_FILE_UPLOADED, 'warning');
					tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=suppliers_pricing&supplier_groups_id='.$supplier_groups_id.'&list_id='.$list_id));
				}
			} else {
				$product_ids = $_POST['product_ids'];	
				$selStatus = $_POST['selStatus'];
				$txtSellingQty = $_POST['txtSellingQty'];
				$txtUnitPrice = $_POST['txtUnitPrice'];
				$arrDisabled = $_POST['chkDisabled'];
				$arrFull = $_POST['chkFull'];
				$chkShowComment = $_POST['chkShowComment'];
				
				$supplier_group_check_sql = "SELECT supplier_groups_id, show_products_purchase_demand_status AS show_status FROM " . TABLE_SUPPLIER_GROUPS . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "'";
				$supplier_group_check_result_sql = tep_db_query($supplier_group_check_sql);
				if ($supplier_group_check_row = tep_db_fetch_array($supplier_group_check_result_sql)) {
					// save pricing setting value
					tep_db_query("DELETE FROM " . TABLE_SUPPLIER_PRICING_SETTING . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'");
					$pricing_setting_keys_array = array(KEY_SPS_QUANTITY_RATIO, KEY_SPS_MIN_QTY, KEY_SPS_USE_MFC_SUPPLY, KEY_SPS_MFC_SUPPLY_MAX_QTY, KEY_SPS_RSTK_CHAR_SET);
					
					foreach ($pricing_setting_keys_array as $pricing_key) {
						$pricing_setting_data_array = array('supplier_groups_id' => $supplier_groups_id,
															'products_purchases_lists_id' => $list_id,
															'supplier_pricing_setting_key' => $pricing_key,
														 	'supplier_pricing_setting_value' => tep_db_prepare_input($_POST["$pricing_key"])
														 );
						tep_db_perform(TABLE_SUPPLIER_PRICING_SETTING, $pricing_setting_data_array);
					}
					
					// Add the Purchase Mode setting for both Supplier Group and Supplier if it does not exists yet
					$pricing_time_checking_sql = "SELECT products_purchases_lists_id FROM " . TABLE_SUPPLIER_LIST_TIME_SETTING . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'";
					$pricing_time_checking_result_sql = tep_db_query($pricing_time_checking_sql);
					if (!tep_db_num_rows($pricing_time_checking_result_sql)) {
						$time_sql_data_array = array(	'supplier_groups_id' => $supplier_groups_id,
														'products_purchases_lists_id' => $list_id
		            								);
		            	
	            		tep_db_perform(TABLE_SUPPLIER_LIST_TIME_SETTING, $time_sql_data_array);
	            		
	            		$supplier_select_sql = "SELECT s.supplier_id 
	            								FROM " . TABLE_SUPPLIER . " AS s 
	            								LEFT JOIN " . TABLE_SUPPLIER_PURCHASE_MODES . " AS spm 
	            									ON (s.supplier_id=spm.supplier_id AND spm.products_purchases_lists_id = '".$list_id."') 
	            								WHERE s.supplier_groups_id = '" . $supplier_groups_id . "' AND spm.supplier_id IS NULL";
	            		$supplier_result_sql = tep_db_query($supplier_select_sql);
						while ($supplier_row = tep_db_fetch_array($supplier_result_sql)) {
							$time_sql_data_array = array(	'supplier_id' => $supplier_row["supplier_id"],
															'products_purchases_lists_id' => $list_id,
															'supplier_purchase_mode' => 'STATUS_GROUP'
			            								);
		            		tep_db_perform(TABLE_SUPPLIER_PURCHASE_MODES, $time_sql_data_array);
						}
					}
					
					// save to database
					if (count($product_ids)) {
					  	foreach ($product_ids as $product_id) {
							$pricing_product_select_sql = "SELECT supplier_groups_id FROM " . TABLE_SUPPLIER_PRICING . " WHERE products_id = '" . tep_db_input($product_id) . "' AND supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'";
							$pricing_product_result_sql = tep_db_query($pricing_product_select_sql);
							$pricing_product_exists = tep_db_num_rows($pricing_product_result_sql) ? true : false;
							
							$overwrite_max = (isset($txtSellingQty[$product_id]) && tep_not_null($txtSellingQty[$product_id])) ? (int)$txtSellingQty[$product_id] : 'NULL';
							$unitprice = (double)$txtUnitPrice[$product_id];
							$unitprice = round($unitprice, DISPLAY_PRICE_DECIMAL);
							$isdisabled = (int)$arrDisabled[$product_id];
							$isfull = (int)$arrFull[$product_id];
							$status_state = $selStatus[$product_id];
							$show_pricing_comment = (int)$chkShowComment[$product_id];
							
							$supplier_pricing_data_array = array(	'supplier_groups_id' => $supplier_groups_id,
																	'products_purchases_lists_id' => $list_id,
																	'products_id' => $product_id,
																 	'supplier_pricing_max_quantity' => $overwrite_max,
																 	'supplier_pricing_disabled' => $isdisabled,
																 	'supplier_pricing_server_full' => $isfull,
																 	'supplier_pricing_product_status' => $status_state
																 );
							
							if ($unit_price_info_permission) {
								$supplier_pricing_data_array['supplier_pricing_unit_price'] = $unitprice;
							}
							
							if ($restock_account_info_permission) {
								$supplier_pricing_data_array['supplier_pricing_show_comment'] = $show_pricing_comment;
							}
							
							if ($pricing_product_exists) {
								tep_db_perform(TABLE_SUPPLIER_PRICING, $supplier_pricing_data_array, 'update', "products_id = '" . tep_db_input($product_id) . "' AND supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'");
							} else {
								tep_db_perform(TABLE_SUPPLIER_PRICING, $supplier_pricing_data_array);
							}
						}
						
						if ($HTTP_POST_VARS['btn_csv_export']) {
							$supplier_pricing_csv_data = '';
							/*
							$products_select_sql = "	SELECT p.products_id as prd_id, p.products_buyback_price, pd.products_name, p.products_cat_path, sp.*, pp.products_purchases_selling_quantity AS selling_qty 
														FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
														INNER JOIN " . TABLE_PRODUCTS . " AS p 
															ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
														INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
															ON p2c.products_id=pd.products_id 
														INNER JOIN ".TABLE_SUPPLIER_PRICING." as sp 
															ON (p2c.products_id=sp.products_id AND sp.supplier_groups_id='" . $supplier_groups_id . "') 
														INNER JOIN ".TABLE_PRODUCTS_PURCHASES." as pp 
															ON (p2c.products_id=pp.products_id AND pp.products_purchases_disabled <> 1) 
														WHERE p2c.categories_id IN ('" . implode("', '", $category_array) . "') AND p2c.products_is_link=0 AND sp.supplier_pricing_disabled <> 1 AND sp.supplier_pricing_server_full <> 1 AND pd.language_id = '" . (int)$languages_id . "' 
														ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
							*/
							$products_select_sql = "SELECT p.products_id as prd_id, p.products_buyback_price, pd.products_name, p.products_cat_path, sp.*, pp.products_purchases_selling_quantity AS selling_qty, rci.restock_character 
													FROM " . TABLE_PRODUCTS . " AS p 
													INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
														ON p.products_id=pd.products_id 
													INNER JOIN ".TABLE_PRODUCTS_PURCHASES." as pp 
														ON (pp.products_purchases_lists_id='".$list_id."' AND p.products_id=pp.products_id AND pp.products_purchases_disabled <> 1) 
													INNER JOIN ".TABLE_SUPPLIER_PRICING." as sp 
														ON (sp.products_purchases_lists_id='".$list_id."' AND p.products_id=sp.products_id AND sp.supplier_groups_id='" . $supplier_groups_id . "') 
													LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci 
														ON (p.products_id=rci.products_id AND rci.restock_character_sets_id='" . tep_db_input($_POST[KEY_SPS_RSTK_CHAR_SET]) . "') 
													WHERE p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0 AND sp.supplier_pricing_disabled <> 1 AND sp.supplier_pricing_server_full <> 1 AND pd.language_id = '" . (int)$languages_id . "' 
													ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
					  		$products_result_sql = tep_db_query($products_select_sql);
					  		
					  		if (tep_db_num_rows($products_result_sql)) {
					  			$csv_header_array = array(	0 => TABLE_CSV_HEADING_PRODUCT_ID,
					  										1 => str_replace('"', '""', TABLE_HEADING_PRODUCT),
					  										4 => str_replace('"', '""', TABLE_HEADING_MAX_QTY),
					  										5 => str_replace('"', '""', TABLE_CSV_HEADING_OVERWRITE_MAX_QTY)
					  									);
					  			
					  			if ($buyback_price_info_permission) {
					  				$csv_header_array[2] = str_replace('"', '""', TABLE_HEADING_BUYBACK_PRICE);
					  			}
					  			
					  			if ($supplier_group_check_row["show_status"] == '1') {
					  				$csv_header_array[3] = str_replace('"', '""', TABLE_CSV_HEADING_STATUS);
					  			}
					  			
					  			if ($unit_price_info_permission) {
					  				$csv_header_array[6] = str_replace('"', '""', TABLE_CSV_HEADING_UNIT_PRICE);
					  			}
					  			
					  			if ($restock_account_info_permission) {
					  				$csv_header_array[7] = str_replace('"', '""', TABLE_CSV_HEADING_COMMENT);
								}
					  			ksort($csv_header_array);
					  			
					  			$supplier_pricing_csv_data = implode(', ', $csv_header_array) . "\n";
					  			
								while ($products_row = tep_db_fetch_array($products_result_sql)) {
									$product_id = (int)$products_row["products_id"];
									$product_name = '"' . str_replace('"', '""', $products_row['products_cat_path']." > ". $products_row['products_name']) . '"';
									$avg_buyback_price = '"' . number_format($products_row["products_buyback_price"], DISPLAY_PRICE_DECIMAL, '.', '') . '"';
									$status_state = '"' . str_replace('"', '""', $products_row["supplier_pricing_product_status"]) . '"';
									
									if (isset($pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY]) && $pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY] == 1) {
										$group_max_qty = $pricing_setting_array[KEY_SPS_MFC_SUPPLY_MAX_QTY];
									} else {
										$group_max_qty = ceil(((int)$products_row['selling_qty'] * (double)$pricing_setting_array[KEY_SPS_QUANTITY_RATIO]) / 100);
									}
									
									$max = '"' . (int)$group_max_qty . '"';
									$overwrite_max = '"' . $products_row['supplier_pricing_max_quantity'] . '"';
									$unitprice = '"' . number_format($products_row["supplier_pricing_unit_price"], DISPLAY_PRICE_DECIMAL, '.', '') . '"';
									$pricing_comment = '"' . str_replace('"', '""', $products_row["restock_character"]) . '"';
									
									$csv_content_array = array(	0 => $product_id,
						  										1 => $product_name,
						  										4 => $max,
						  										5 => $overwrite_max
						  									);
						  			
						  			if ($buyback_price_info_permission) $csv_content_array[2] = $avg_buyback_price;
						  			
						  			if ($supplier_group_check_row["show_status"] == '1') {
						  				$csv_content_array[3] = $status_state;
						  			}
						  			
						  			if ($unit_price_info_permission) {
						  				$csv_content_array[6] = $unitprice;
						  			}
						  			
						  			if ($restock_account_info_permission) {
						  				$csv_content_array[7] = $pricing_comment;
						  			}
						  			
						  			ksort($csv_content_array);
						  			
									$supplier_pricing_csv_data .= implode(',', $csv_content_array) . "\n";
								}
							}
							
							if (tep_not_null($supplier_pricing_csv_data)) {
								$filename = 'supplier_pricing_'.date('YmdHis').'.csv';
								$mime_type = 'text/x-csv';
								// Download
						        header('Content-Type: ' . $mime_type);
						        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
						        // IE need specific headers
						        if (PMA_USR_BROWSER_AGENT == 'IE') {
						            header('Content-Disposition: inline; filename="' . $filename . '"');
						            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
						            header('Pragma: public');
						        } else {
						            header('Content-Disposition: attachment; filename="' . $filename . '"');
						            header('Pragma: no-cache');
						        }
								echo $supplier_pricing_csv_data;
								exit();
							} else {
								$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
								tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=suppliers_pricing&supplier_groups_id='.$supplier_groups_id.'&list_id='.$list_id));
							}
						} else {
							$messageStack->add_session(SUCCESS_SUPPLIER_PRICING_UPDATE, 'success');
							tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=suppliers_pricing&supplier_groups_id='.$supplier_groups_id.'&list_id='.$list_id));
						}
					} else {
						$messageStack->add_session(WARNING_NO_PRODUCTS_TO_UPDATE, 'warning');
						tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=suppliers_pricing&supplier_groups_id='.$supplier_groups_id.'&list_id='.$list_id));
					}
				} else {
					$messageStack->add_session(WARNING_NO_SUCH_SUPPLIER_GROUP, 'warning');
					tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING));
				}
			}
			
			break;
		case "copy_pricing":
			$supplier_groups_id = tep_db_prepare_input($_POST["supplier_groups_id"]);
			$copy_from_supplier_groups_id = tep_db_prepare_input($_POST["copy_from_group"]);
			$list_id = tep_db_prepare_input($_POST["list_id"]);
			
			// Clear the destination list first
			$clear_copy_to_list_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PRICING . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "' ";
			tep_db_query($clear_copy_to_list_delete_sql);
			
			$products_select_sql = "	SELECT sp.products_id as prd_id, sp.supplier_pricing_unit_price, sp.supplier_pricing_max_quantity AS max_qty, sp.supplier_pricing_disabled, sp.supplier_pricing_server_full, sp.supplier_pricing_product_status
										FROM " . TABLE_SUPPLIER_PRICING . " as sp 
										WHERE sp.supplier_groups_id='" . tep_db_input($copy_from_supplier_groups_id) . "' 
										AND sp.products_purchases_lists_id='" . tep_db_input($list_id) . "'";
	  		$products_result_sql = tep_db_query($products_select_sql);
			while ($products_row = tep_db_fetch_array($products_result_sql)) {
				$overwrite_max = (tep_not_null($products_row['max_qty'])) ? (int)$products_row['max_qty'] : 'NULL';
				
				$supplier_pricing_data_array = array(	'supplier_groups_id' => $supplier_groups_id,
														'products_purchases_lists_id' => $list_id,
														'products_id' => $products_row["prd_id"],
														'supplier_pricing_max_quantity' => $overwrite_max,
													 	'supplier_pricing_unit_price' => $products_row["supplier_pricing_unit_price"],
													 	'supplier_pricing_disabled' => $products_row["supplier_pricing_disabled"],
													 	'supplier_pricing_server_full' => $products_row["supplier_pricing_server_full"],
													 	'supplier_pricing_product_status' => $products_row["supplier_pricing_product_status"]
													 );
				tep_db_perform(TABLE_SUPPLIER_PRICING, $supplier_pricing_data_array);
				/*
				if (tep_db_num_rows($copy_to_product_result_sql)) {	// Update existing record
					$overwrite_max = (tep_not_null($products_row['max_qty'])) ? (int)$products_row['max_qty'] : 'NULL';
					
					$pricing_update_sql = "	UPDATE " . TABLE_SUPPLIER_PRICING . " 
											SET supplier_pricing_unit_price = '" . tep_db_input($products_row["supplier_pricing_unit_price"]) . "',
												supplier_pricing_max_quantity = " . $overwrite_max . " 
											WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' 
												AND products_purchases_lists_id = '" . tep_db_input($list_id) . "' 
												AND products_id = '" . tep_db_input($products_row["prd_id"]) . "'";
					tep_db_query($pricing_update_sql);
				}
				*/
			}
			
			$messageStack->add_session(SUCCESS_COPY_LIST, 'success');
			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=suppliers_pricing&supplier_groups_id='.$supplier_groups_id.'&list_id='.$list_id));
			
			break;
		case "remove_list":
			$supplier_groups_id = $_GET["supplier_groups_id"];
			$list_id = $_GET["list_id"];
			
			$supplier_pricing_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PRICING . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'";
			tep_db_query($supplier_pricing_delete_sql);
			
			$supplier_pricing_setting_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PRICING_SETTING . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'";
			tep_db_query($supplier_pricing_setting_delete_sql);
			
			$supplier_group_time_setting_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_LIST_TIME_SETTING . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'";
			tep_db_query($supplier_group_time_setting_delete_sql);
			
			$supplier_select_sql = "SELECT supplier_id FROM " . TABLE_SUPPLIER . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "'";
			$supplier_result_sql = tep_db_query($supplier_select_sql);
			while ($supplier_row = tep_db_fetch_array($supplier_result_sql)) {
				$supplier_purchase_mode_delete_sql = "DELETE FROM " . TABLE_SUPPLIER_PURCHASE_MODES . " WHERE supplier_id = '" . tep_db_input($supplier_row["supplier_id"]) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'";
				tep_db_query($supplier_purchase_mode_delete_sql);
			}
			
			$messageStack->add_session(SUCCESS_REMOVE_LIST, 'success');
			tep_redirect(tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=suppliers_pricing&supplier_groups_id=' . $supplier_groups_id ));
			
			break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript">
	<!--
		var products_ids = new Array();
		
		function disableTextBoxes(id, obj, sn) {
			var ele3 = document.getElementById("txtUnitPrice" + id);
			var server_full_obj = document.getElementById("chkFull" + id);
			var status_ele = document.getElementById("selStatus" + id);
			var overwrite_max_qty = document.getElementById("txtSellingQty" + id);
			var comment_ele = document.getElementById("txtComment" + id);
			var show_comment_ele = document.getElementById("chkShowComment" + id);
			
			var bl;
			
			if (sn == 1) {	// Server Full action
				if (obj.checked) {
					bl = true;	
				} else {
					bl = false;	
				}
			} else {	// Disabled action
				if (obj.checked) {
					bl = true;
					server_full_obj.disabled = true;
				} else if(document.getElementById("chkFull"+id).checked) {
					bl = true;
					server_full_obj.disabled = false;
				} else {
					bl = false;	
					server_full_obj.disabled = false;
				}
			}
			
			if (ele3 != null)	ele3.disabled = bl;
			if (status_ele != null) status_ele.disabled = bl;
			if (overwrite_max_qty != null) 	overwrite_max_qty.disabled = bl;
			if (comment_ele != null) comment_ele.disabled = bl;
			if (show_comment_ele != null) 	show_comment_ele.disabled = bl;
			
			if (bl) {
				if (ele3 != null)	ele3.style.background = '#D4D0C8';
				if (status_ele != null) 	status_ele.style.background = '#D4D0C8';
				if (overwrite_max_qty != null) 	overwrite_max_qty.style.background = '#D4D0C8';
				if (comment_ele != null)	comment_ele.style.background = '#D4D0C8';
			} else {
				if (ele3 != null)	ele3.style.background = '#ffffff';
				if (status_ele != null) 	status_ele.style.background = '#ffffff';
				if (overwrite_max_qty != null) 	overwrite_max_qty.style.background = '#ffffff';
				if (comment_ele != null)	comment_ele.style.background = '#ffffff';
			}
		}
		
		function select_all_checkbox_click(selectAllObj, ctrlName, skipDisabled) {
			if (products_ids != null) {
				var size_of_products_ids = products_ids.length;
				var checked_state = (selectAllObj.checked) ? true : false;
				
				for (i=0; i < size_of_products_ids; i++) {
					var chk_box = DOMCall(ctrlName + products_ids[i]);
					
					if (chk_box != null && !chk_box.disabled) {
						if (skipDisabled == true) {
							var disabled_state = GetCookie('pricing_show_hide_disabled');
							if (disabled_state != 1) {	// Currently the view exclude disabled rows
								var disabled_row = DOMCall("row" + products_ids[i]);
								
								if (disabled_row != null && disabled_row.className == 'show') {
									chk_box.checked = checked_state;
								}
							} else {
								chk_box.checked = checked_state;
							}
						} else {
							chk_box.checked = checked_state;
						}
						
						if (selectAllObj.value > 0) {
							disableTextBoxes(products_ids[i], chk_box, selectAllObj.value);
						}
					}
				}
			}
		}
		
		function show_hide_disabled(show) {
			var ele = document.getElementById('show_disabled');
			var temp_ele;
			var size_of_products_ids = products_ids.length;
			var count =0 ;
			var tempPool = new Array(); 
			
			ele.innerHTML = '';
			
			for (i=0; i < size_of_products_ids; i++) {
				chk_box = document.getElementById("chkDisabled" + products_ids[i]);
				
				if (chk_box.checked) {
					if (show) {
						document.getElementById("row" + products_ids[i]).className = 'show';
					} else {
				 		document.getElementById("row" + products_ids[i]).className = 'hide';
				 	}
				} else {
					document.getElementById("row" + products_ids[i]).className = 'show';
				}
			}
			
			// reaarange
			for (i=0; i < size_of_products_ids; i++) {
				objrow = document.getElementById("row" + products_ids[i]);
				if (show) {
					strclass = ( count%2 ) ? "ordersListingEven" : "ordersListingOdd";
					tempPool[products_ids[i]] = strclass;
					
					custrow = document.getElementById("custRow_" + products_ids[i]);
					objrow.className = 'show';
					custrow.className = strclass;
					custrow.onclick=function() {
							var tempStr = this.id.split('_');
							rowClicked(this, tempPool[tempStr[1]], '');
						}
					custrow.onmouseout=function() {
							var tempStr = this.id.split('_');
							rowOutEffect(this, tempPool[tempStr[1]], '');
						}
					
					count++;		
				} else {
					if (objrow.className == 'show') {
						strclass = ( count%2 ) ? "ordersListingEven" : "ordersListingOdd";
						tempPool[products_ids[i]] = strclass;
						
						custrow= document.getElementById("custRow_" + products_ids[i]);
						custrow.className = strclass;
						custrow.onclick=function() {
								var tempStr = this.id.split('_');
								rowClicked(this, tempPool[tempStr[1]], '');
							}
						custrow.onmouseout=function() {
								var tempStr = this.id.split('_');
								rowOutEffect(this, tempPool[tempStr[1]], '');
							}
						count++;
					}
				}
			}
			
			if (show) {
				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_disabled(false)"><?=TEXT_HIDE_DISABLED?></a>';
				SetCookie('pricing_show_hide_disabled', '1');
			} else {
				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_disabled(true)"><?=TEXT_SHOW_DISABLED?></a>';
				SetCookie('pricing_show_hide_disabled', '0');
			}
		}
		
		function show_hide_product_name(show) {
			var show_hide_prod_name_div = DOMCall('show_product_name');
			var size_of_products_ids = products_ids.length;
			
			show_hide_prod_name_div.innerHTML = '';
			
			for (var i=0; i < size_of_products_ids; i++) {
				var prod_name_span_obj = DOMCall('span_prod_name_' + products_ids[i]);
				
				prod_name_span_obj.className = show ? 'show' : 'hide';
			}
			
			if (show) {
				show_hide_prod_name_div.innerHTML = '<a href="javascript:;" onclick="show_hide_product_name(false)"><?=TEXT_HIDE_PRODUCT_NAME?></a>';
				SetCookie('pricing_show_hide_pname', '1');
			} else {
				show_hide_prod_name_div.innerHTML = '<a href="javascript:;" onclick="show_hide_product_name(true)"><?=TEXT_SHOW_PRODUCT_NAME?></a>';
				SetCookie('pricing_show_hide_pname', '0');
			}
		}
		
		function show_hide_batch_fill(show) {
			var ele = document.getElementById('show_batch_fill');
			var ele2 = document.getElementById('batch_fill');
			
			if (show) {
				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_batch_fill(false)"><?=TEXT_HIDE_BATCH_FILL?></a>';
				ele2.className = "show";
			} else {
				ele.innerHTML = '<a href="javascript:;" onclick="show_hide_batch_fill(true)"><?=TEXT_SHOW_BATCH_FILL?></a>';
				ele2.className = "hide";
			}
			
			return;
		}
		
		function apply_batch(btnBatchObj) {
			var frm = btnBatchObj.form;
			var global_min_qty_obj = DOMCall('<?=KEY_SPS_MIN_QTY?>');
			
			if (products_ids != null) {
				var size_of_products_ids = products_ids.length;
				var ischecked, temp_obj;
				var product_status, min, overwrite_max_qty, up, is_disabled;
				var disable_inputs_mode = 0;
				
				min = (global_min_qty_obj != null) ? global_min_qty_obj.value : 0;
				
				if (frm.selBatchStatus != null) {
					product_status = frm.selBatchStatus.value;
				}
				
				if (frm.txtBatchSellingQty != null) {
					overwrite_max_qty = parseInt(frm.txtBatchSellingQty.value, 10);
				}
				if (frm.txtBatchUnitPrice != null) {
					up = parseFloat(frm.txtBatchUnitPrice.value);
				}
				
				is_disabled = frm.chkBatchDisabled.checked;
				is_full = frm.chkBatchServerFull.checked;
				show_comment = frm.chkBatchShowComment.checked;
				
				if (isNaN(min))		min = 0;
				if (isNaN(overwrite_max_qty))	overwrite_max_qty = '';
				if (isNaN(up))		up = 0;
				
				for	(var i=0; i < size_of_products_ids; i++) {
					temp_obj = document.getElementById("chkSelected" + products_ids[i]);
					
					if (temp_obj != null) {
						ischecked = temp_obj.checked;
						
						if (ischecked) {
							if (document.getElementById("selStatus" + products_ids[i]) != null) {
								document.getElementById("selStatus" + products_ids[i]).value = product_status;
							}
							
							if (document.getElementById("txtSellingQty" + products_ids[i]) != null) {
								document.getElementById("txtSellingQty" + products_ids[i]).value = overwrite_max_qty;
							}
							
							if (document.getElementById("txtUnitPrice" + products_ids[i]) != null) {
								document.getElementById("txtUnitPrice" + products_ids[i]).value = up;
							}
							
							if (document.getElementById("chkFull" + products_ids[i]) != null) {
								document.getElementById("chkFull" + products_ids[i]).checked = is_full;
							}
							
							if (document.getElementById("chkDisabled" + products_ids[i]) != null) {
								document.getElementById("chkDisabled" + products_ids[i]).checked = is_disabled;
							}
							/*
							if (document.getElementById("txtComment" + products_ids[i]) != null) {
								document.getElementById("txtComment" + products_ids[i]).value = comment;
							}
							*/
							if (document.getElementById("chkShowComment" + products_ids[i]) != null) {
								document.getElementById("chkShowComment" + products_ids[i]).checked = show_comment;
							}
							
							disableTextBoxes(products_ids[i], document.getElementById("chkDisabled" + products_ids[i]), 2);
						}
					}
				}
			}
			return;
		}
		
		function checkMaxQty(id) {
			var obj = DOMCall("txtMaxQty" + id);
			var global_min_qty_obj = DOMCall('<?=KEY_SPS_MIN_QTY?>');
			
			var valMax = parseInt(obj.value);
			var valMin = (global_min_qty_obj != null) ? global_min_qty_obj.value : 0;
			
			if (isNaN(valMin)) 	valMin = 0;
			if (isNaN(valMax))	valMax = 0;
			
			if (valMin > valMax) {
				alert("Maximum quantity cannot be less than maximum quantity.");
				obj.value = '';
				obj.focus();
			}
			return;
		}
		
		function clear_input_boxes(ctrlObjName) {
			if (products_ids != null) {
				var size_of_products_ids = products_ids.length;
				
				for (i=0; i < size_of_products_ids; i++) {
					var input_box = DOMCall(ctrlObjName + products_ids[i]);
					
					if (input_box != null) {
						input_box.value = '';
					}
				}
			}
		}
	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
<?
if ($preview_mode) {
	$supplier_groups_id = $_REQUEST["supplier_groups_id"];
	$list_id = $_REQUEST["list_id"];
	
	$grp_info_select_sql = "SELECT supplier_groups_name FROM " . TABLE_SUPPLIER_GROUPS . " WHERE supplier_groups_id = '" . tep_db_input($supplier_groups_id) . "'";
	$grp_info_result_sql = tep_db_query($grp_info_select_sql);
	$grp_info_row = tep_db_fetch_array($grp_info_result_sql);
	$grp_name = $grp_info_row["supplier_groups_name"];
	
	$list_info_select_sql = "SELECT products_purchases_lists_name, products_purchases_lists_qty_round_up FROM " . TABLE_PRODUCTS_PURCHASES_LISTS . " WHERE products_purchases_lists_id = '" . (int)$list_id . "'";
	$list_info_result_sql = tep_db_query($list_info_select_sql);
	$list_info_row = tep_db_fetch_array($list_info_result_sql);
	$list_name = $list_info_row["products_purchases_lists_name"];
	$list_qty_round_up = (int)$list_info_row["products_purchases_lists_qty_round_up"] > 0 ? (int)$list_info_row["products_purchases_lists_qty_round_up"] : 1;
	
	$supplier_pricing_setting_array = tep_get_supplier_pricing_setting($supplier_groups_id, $list_id);
	$total_active_supplier = tep_get_active_suppliers_count($supplier_groups_id);
	$show_purchase_status = tep_show_purchase_status($supplier_groups_id);
	
	$server_status_array = array(	'urgent' => array('name' => 'icon_status_urgent'), 
									'important' => array('name' => 'icon_status_important'), 
									'normal' => array('name' => 'icon_status_normal')
								);
	
	$products_list_select_sql = "	SELECT p2c.categories_id, p.products_id as prd_id, p.products_quantity as inventory_qty, pd.products_name, p.products_cat_path, sp.*, pp.products_purchases_selling_quantity AS selling_qty 
									FROM " . TABLE_SUPPLIER_PRICING . " as sp 
									INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
										ON (sp.supplier_pricing_disabled=0 AND p2c.products_id=sp.products_id AND sp.supplier_groups_id='" . $supplier_groups_id . "')
									INNER JOIN " . TABLE_PRODUCTS_PURCHASES . " as pp 
										ON (sp.products_purchases_lists_id=pp.products_purchases_lists_id AND pp.products_purchases_disabled=0 AND p2c.products_id=pp.products_id) 
									INNER JOIN " . TABLE_PRODUCTS . " AS p 
										ON (p2c.products_id=p.products_id AND p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0)
									INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
										ON p2c.products_id=pd.products_id 
									WHERE sp.products_purchases_lists_id = '" . $list_id . "' AND p2c.products_is_link=0 AND pd.language_id = '" . $languages_id . "' 
									ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order";
	$products_list_result_sql = tep_db_query($products_list_select_sql);
?>
		<tr>
			<td class="pageHeading" valign="top"><?=$list_name?></td>
		</tr>
		<tr>
			<td align="left">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td width="25%" class="main"><?=sprintf(TEXT_PREVIEW_MIN_QTY, (int)$supplier_pricing_setting_array["sps_min_quantity"])?></td>
						<td width="50%" align="center" class="main"><?=sprintf(HEADING_TITLE_PREVIEW, $grp_name)?></td>
						<td width="25%">&nbsp;</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td>
				<table width="100%" border="0" cellspacing="0" cellpadding="2">
					<tr>
						<td class="ordersBoxHeading">&nbsp;<?=TABLE_PREVIEW_HEADING_PRODUCT?></td>
<?	if ($show_purchase_status) {
		echo '			<td align="center" class="ordersBoxHeading" width="4%">'.TABLE_PREVIEW_HEADING_STATUS.'</td>';
	}
?>
						<td class="ordersBoxHeading" width="6%" align="center"><?=TABLE_PREVIEW_HEADING_MIN_MAX_QTY?></td>
<?	if ($unit_price_info_permission) {
		echo '			<td class="ordersBoxHeading" width="6%" align="center">'.TABLE_PREVIEW_HEADING_UNIT_PRICE.'</td>';
	}
?>
						<td class="ordersBoxHeading" width="5%" align="center"><?=TABLE_PREVIEW_HEADING_SELLING_QTY?></td>
						<td class="ordersBoxHeading" width="9%" align="right"><?=TABLE_PREVIEW_HEADING_AMOUNT?>&nbsp;</td>
						<td class="ordersBoxHeading" width="15%"><?=TABLE_PREVIEW_HEADING_SUPPLIER_COMMENT?></td>
					</tr>
<?
	$i = 0;
	
	$list_colspan_count = 5;
	if ($show_purchase_status) $list_colspan_count++;
	if ($unit_price_info_permission) $list_colspan_count++;
	
	$form_can_be_process = false;
	while ($row = tep_db_fetch_array($products_list_result_sql)) {
		if ((int)$row['supplier_pricing_disabled'] == 0) {
			$showed_column = 0;
			$pid = $row['prd_id'];
			
			if ($total_active_supplier > 0) {
				if (tep_not_null($row['supplier_pricing_max_quantity']) && is_numeric($row['supplier_pricing_max_quantity'])) {	// If overwrite maximum qty is set
					$group_max_qty = (int)$row['supplier_pricing_max_quantity'];
				} else {
					if ($supplier_pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY] == '1') {
						$group_max_qty = (int)$supplier_pricing_setting_array[KEY_SPS_MFC_SUPPLY_MAX_QTY];
					} else {
						$group_max_qty = ceil(((int)$row['selling_qty'] * (double)$supplier_pricing_setting_array[KEY_SPS_QUANTITY_RATIO]) / 100);
					}
				}
				
				$offset_amount = tep_get_previously_restocked_qty($pid, $supplier_groups_id, $list_id);
				$group_max_qty -= (int)$offset_amount;
				
				$this_supplier_max_quantity = (int)$group_max_qty / $total_active_supplier;
				$this_supplier_max_quantity = tep_round_up_to($this_supplier_max_quantity, $list_qty_round_up);
			} else {
				$this_supplier_max_quantity = 0;	// no active supplier means no one can submit
			}
			
			$this_supplier_over_limit_max_quantity = 0;	// no active supplier means no one can submit
			
			$row_style = ($i%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
			
			$product_display_name = tep_display_category_path($row['products_cat_path']." > ".$row['products_name'], $row['categories_id'], 'catalog', false);
?>
					<tr height="26" class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
						<td class="dataTableContent" valign="top">&nbsp;<?=$product_display_name?></td>
<?			$showed_column++;
			if ($show_purchase_status) {
				echo '	<td align="center" valign="top" class="dataTableContent">';
				$server_status_index = trim($row['supplier_pricing_product_status']);
				if (isset($server_status_array[$server_status_index])) {
					echo tep_image(DIR_WS_IMAGES . $server_status_array[$server_status_index]['name'] . '.gif', $server_status_trans[$row['supplier_pricing_product_status']]);
				} else { echo '&nbsp;'; }
				echo '	</td>';
				
				$showed_column++;
			}
			
			if ((int)$row['supplier_pricing_server_full'] == 0 && (int)$this_supplier_max_quantity > 0 && (int)$supplier_pricing_setting_array["sps_min_quantity"] <= (int)$this_supplier_max_quantity) {
				// Accept selling quantity for this product
				$normal_unit_price = (double)$row['supplier_pricing_unit_price'];
				//$over_limit_unit_price = $normal_unit_price * ( (100 - (double)$supplier_pricing_setting_array["sps_over_limit_discount"]) / 100);
				$over_limit_unit_price = 0;
				
				echo '	<td class="dataTableContent" align="center" valign="top">'.$this_supplier_max_quantity.'</td>';
				
				if ($unit_price_info_permission)	echo '	<td class="dataTableContent" align="right" valign="top" nowrap>'.$currencies->format($normal_unit_price).'</td>';
  				
				echo '	<td class="dataTableContent" align="center" valign="top">';
				
				$disabled = '';
				$show_input_qty = '';
				
				echo tep_draw_input_field('cart_quantity['.$pid.']', $show_input_qty, 'size="6" id="cart_quantity_'.$pid.'" onKeyPress="return noEnterKey(event)" ' . $disabled);
				
				echo '	</td>';
				
				echo '	<td class="dataTableContent" align="right" valign="top" id="" nowrap><b><span id="cart_amount_'.$pid.'" >'.$currencies->format(0).'</span></b>&nbsp;</td>';
				
				
				echo '	<td class="dataTableContent" valign="top">';
				echo tep_draw_input_field("cart_comment[".$pid."]", '', 'size="35" onKeyPress="return noEnterKey(event)" ');
				echo '	</td>';
			} else {
				echo '	<td class="dataTableContent" align="center" colspan="'.($list_colspan_count-$showed_column).'">'.TEXT_PREVIEW_CURRENTLY_NOT_BUYING.'</td>';
			}
?>
					</tr>
<?
				$i++;
		}
	}
?>
					<tr>
						<td colspan="<?=$list_colspan_count?>" valign="top"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
					</tr>
				</table>
			</td>
		</tr>
<?
} else {
?>
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
            			<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
          			</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
					<tr>
						<td align="right">
<?
echo tep_draw_form('pricing_criteria_form', FILENAME_SUPPLIERS_PRICING, tep_get_all_get_params(array('action', 'subaction')) . 'action=suppliers_pricing', 'post');
$this_group_name = $this_list_name = '';

$groups_array = array ( array('id' => '', 'text' => TEXT_SELECT_SUPPLIER_GROUP) );
$groups_query = tep_db_query("select supplier_groups_id, supplier_groups_name from " . TABLE_SUPPLIER_GROUPS ." where supplier_groups_status=1 order by supplier_groups_name");
while($groups = tep_db_fetch_array($groups_query)) {
	if ($groups['supplier_groups_id'] == $_REQUEST["supplier_groups_id"]) $this_group_name = $groups['supplier_groups_name'];
	
	$groups_array[] = array('id' => $groups['supplier_groups_id'], 'text' => $groups['supplier_groups_name']);
}

$copy_from_array = array ( array('id' => '', 'text' => TEXT_COPY_FROM_GROUP) );
$copy_from_select_sql = "	SELECT sg.supplier_groups_id, sg.supplier_groups_name 
							FROM " . TABLE_SUPPLIER_GROUPS ." AS sg 
							INNER JOIN " . TABLE_SUPPLIER_PRICING_SETTING . " AS sps 
								ON (sg.supplier_groups_id=sps.supplier_groups_id AND sps.products_purchases_lists_id='".(int)$_REQUEST["list_id"]."') 
							WHERE sg.supplier_groups_status=1 
							GROUP BY sps.supplier_groups_id 
							ORDER BY sg.supplier_groups_name";
$copy_from_result_sql = tep_db_query($copy_from_select_sql);
while($copy_from_row = tep_db_fetch_array($copy_from_result_sql)) {
	if ($copy_from_row['supplier_groups_id'] != $_REQUEST["supplier_groups_id"]) {
		$copy_from_array[] = array('id' => $copy_from_row['supplier_groups_id'], 'text' => $copy_from_row['supplier_groups_name']);
	}
}

$purchase_list_array = array ( array('id' => '', 'text' => TEXT_SELECT_PURCHASE_LIST) );

$purchase_list_select_sql = "SELECT products_purchases_lists_id, products_purchases_lists_name, products_purchases_lists_cat_id FROM " . TABLE_PRODUCTS_PURCHASES_LISTS ." ORDER BY products_purchases_lists_sort_order, products_purchases_lists_name";
$purchase_list_result_sql = tep_db_query($purchase_list_select_sql);
while($purchase_list_row = tep_db_fetch_array($purchase_list_result_sql)) {
	if (tep_check_cat_tree_permissions(FILENAME_PRODUCTS_PURCHASE_QUANTITY, $purchase_list_row["products_purchases_lists_cat_id"]) != 1) {
		continue;
	}
	if ($purchase_list_row['products_purchases_lists_id'] == $_REQUEST["list_id"]) $this_list_name = $purchase_list_row['products_purchases_lists_name'];
	
	$purchase_list_array[] = array('id' => $purchase_list_row['products_purchases_lists_id'], 'text' => $purchase_list_row['products_purchases_lists_name']);
}

?>
							<div style="float: left;">
								<?=tep_draw_pull_down_menu('supplier_groups_id', $groups_array, $_REQUEST["supplier_groups_id"])?>
							</div>
							<div style="float: left;">&nbsp;
							<?
								echo tep_draw_pull_down_menu("list_id", $purchase_list_array, $_REQUEST["list_id"], ' id="list_id" ');
								echo tep_submit_button('Go', 'Go', 'name="go_btn" onClick="if(document.pricing_criteria_form.supplier_groups_id.value != \'\' && document.pricing_criteria_form.list_id.value != \'\') { return true; } else { alert(\'Please select supplier group and purchase list!\'); return false;}"', 'inputButton');
								if (tep_not_null($_REQUEST["supplier_groups_id"]) && tep_not_null($_REQUEST["list_id"])) {
									echo '&nbsp;&nbsp;' . tep_button(BUTTON_PREVIEW, ALT_BUTTON_PREVIEW, '', 'onClick="openNewWin(\''.tep_href_link(FILENAME_SUPPLIERS_PRICING, 'action=list_preview&supplier_groups_id='.$_REQUEST["supplier_groups_id"].'&list_id='.$_REQUEST["list_id"]).'\', \'Preview List\', \'\');" ', 'inputButton');
								}
							?>
							</div>
							</form>
							<div>
							<?
							if ($unit_price_info_permission && tep_not_null($_REQUEST["supplier_groups_id"]) && tep_not_null($_REQUEST["list_id"])) {
								echo tep_draw_form('copy_pricing_form', FILENAME_SUPPLIERS_PRICING, tep_get_all_get_params(array('subaction')) . 'subaction=copy_pricing', 'post');
								
								echo tep_draw_hidden_field("supplier_groups_id", $_REQUEST["supplier_groups_id"]);
								echo tep_draw_hidden_field("list_id", $_REQUEST["list_id"]);
								
								echo tep_draw_pull_down_menu("copy_from_group", $copy_from_array, '', ' id="copy_from_group" onChange="if (this.selectedIndex > 0) { if (confirm_action(\''.JS_CONFIRM_COPY_LIST.'\')) { this.form.submit(); } else { this.selectedIndex=0; } }"');
								echo '</form>';
							}
							?>
							</div>
						</td>
					</tr>
<?
	if ($action == "suppliers_pricing") {
		$this_supplier_group_id = (int)$_REQUEST["supplier_groups_id"];
		$list_id = (int)$_REQUEST["list_id"];
		
		if (tep_not_null($this_supplier_group_id) && tep_not_null($list_id)) {
			$supplier_group_info_select_sql = "	SELECT sg.show_products_purchase_demand_status AS show_status, COUNT(s.supplier_id) AS active_supplier 
												FROM " . TABLE_SUPPLIER_GROUPS . " AS sg 
												LEFT JOIN " . TABLE_SUPPLIER . " AS s 
													ON ( sg.supplier_groups_id=s.supplier_groups_id AND s.supplier_status=1 ) 
												WHERE sg.supplier_groups_id = '" . $this_supplier_group_id . "' 
												GROUP BY s.supplier_groups_id";
			$supplier_group_info_result_sql = tep_db_query($supplier_group_info_select_sql);
			if ($supplier_group_info_row = tep_db_fetch_array($supplier_group_info_result_sql)) {
				$pricing_setting_array = array();
				$rstk_char_set_array = array( array('id'=>'', 'text'=>TEXT_SELECT_RSTK_CHAR_SET) );
				
				$show_product_purchase_status = $supplier_group_info_row["show_status"] == '1' ? true : false;
				
				$supplier_pricing_setting_select_sql = "SELECT supplier_pricing_setting_key, supplier_pricing_setting_value FROM " . TABLE_SUPPLIER_PRICING_SETTING . " WHERE supplier_groups_id = '" . tep_db_input($this_supplier_group_id) . "' AND products_purchases_lists_id = '" . tep_db_input($list_id) . "'";
				$supplier_pricing_setting_result_sql = tep_db_query($supplier_pricing_setting_select_sql);
				while ($supplier_pricing_setting_row = tep_db_fetch_array($supplier_pricing_setting_result_sql)) {
					$pricing_setting_array[$supplier_pricing_setting_row["supplier_pricing_setting_key"]] = $supplier_pricing_setting_row["supplier_pricing_setting_value"];
				}
				
				$suggested_ms_max_qty = 2 * (int)$pricing_setting_array[KEY_SPS_MIN_QTY];
				
				$rstk_char_set_select_sql = "	SELECT rcs.restock_character_sets_id, rcs.restock_character_sets_name 
												FROM " . TABLE_RESTOCK_CHARACTER_SETS . " AS rcs
												INNER JOIN ". TABLE_RESTOCK_CHARACTER_SETS_TO_PURCHASES_LISTS ." AS rcs2ppl
										  	  		ON (rcs.restock_character_sets_id=rcs2ppl.restock_character_sets_id)
											  	WHERE rcs2ppl.products_purchases_lists_id = '" . tep_db_input($list_id) . "'
											  	ORDER BY rcs.restock_character_sets_name";
				$rstk_char_set_result_sql = tep_db_query($rstk_char_set_select_sql);
				while ($rstk_char_set_row = tep_db_fetch_array($rstk_char_set_result_sql)) {
					$rstk_char_set_array[] = array('id' => $rstk_char_set_row["restock_character_sets_id"], 'text'=>$rstk_char_set_row["restock_character_sets_name"]);
				}
?>
					<tr>
						<td>
<?				echo tep_draw_form('suppliers_pricing_form', FILENAME_SUPPLIERS_PRICING, tep_get_all_get_params(array('subaction')) . 'subaction=update_pricing', 'post', 'enctype="multipart/form-data"');
				echo tep_draw_hidden_field("supplier_groups_id", $this_supplier_group_id);
				echo tep_draw_hidden_field("list_id", $list_id);
?>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td>
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td>
													<table width="100%" border="0" cellspacing="0" cellpadding="0">
														<TBODY id="batch_fill" class="hide">
															<tr>
																<td>
																	<table width="100%" border="0" cellspacing="0" cellpadding="2">
																		<tr>
																			<td class="ordersBoxHeading">&nbsp;</td>
<?				if ($show_product_purchase_status) {
					echo '													<td class="ordersBoxHeading">'.TABLE_HEADING_STATUS.'</td>' . "\n";
				}
?>
																			<td class="ordersBoxHeading" align="center"><?=TABLE_HEADING_OVERWRITE_MAX_QTY?></td>
<?				if ($unit_price_info_permission) {
					echo '													<td class="ordersBoxHeading" align="center">'.TABLE_HEADING_UNIT_PRICE.'</td>' . "\n";
				}
?>
																			<td class="ordersBoxHeading" align="right"><?=TABLE_SERVER_FULL?></td>
																			<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_DISABLED?></td>
																			<td width="25%" class="ordersBoxHeading"><?=TABLE_HEADING_COMMENT.'<br>'.TABLE_HEADING_SHOW_COMMENT?></td>
																		</tr>	
																		<tr class="ordersListingEven">
																			<td class="ordersRecords"><?=TEXT_APPLY_TO_SELECTED?></td>
<?				if ($show_product_purchase_status) {
					echo '													<td class="ordersRecords">'.tep_draw_pull_down_menu("selBatchStatus", $product_purchase_status_array, '', '').'</td>' . "\n";
				}
?>
																			<td class="ordersRecords" align="center"><?=tep_draw_input_field("txtBatchSellingQty", '', 'size="5" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"')?></td>
<?				if ($unit_price_info_permission) {
					echo '													<td class="ordersRecords" align="center">'.tep_draw_input_field("txtBatchUnitPrice", '', 'size="6" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)"') . '</td>' . "\n";
				}
?>
																			<td class="ordersRecords" align="right"><?=tep_draw_checkbox_field("chkBatchServerFull")?></td>
																			<td class="ordersRecords" align="right"><?=tep_draw_checkbox_field("chkBatchDisabled")?></td>
																			<td class="ordersRecords"><?=tep_draw_checkbox_field("chkBatchShowComment")?></td>
																		</tr>
																		<tr>
																			<td colspan="<?=$show_product_purchase_status ? '7' : '6'?>" align="right">
																				<?=tep_button(IMAGE_BUTTON_BATCH_APPLY, IMAGE_BUTTON_BATCH_APPLY, '', 'onClick="apply_batch(this);"')?>
																			</td>
																		</tr>
																	</table>
																</td>
															</tr>
														</TBODY>
														<tr>
															<td class="main"><?=sprintf(TEXT_ACTIVE_SUPPLIERS, $supplier_group_info_row["active_supplier"])?></td>
														</tr>
														<tr>
															<td><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
														</tr>
														<tr>
															<td>
																<table border="0" cellspacing="1" cellpadding="2">
																	<tr>
																		<td>
																			<table border="0" cellspacing="0" cellpadding="0">
																				<tr>
																					<td class="main"><?=ENTRY_RSTK_CHAR_SET . '&nbsp;' . tep_draw_pull_down_menu(KEY_SPS_RSTK_CHAR_SET, $rstk_char_set_array, $pricing_setting_array[KEY_SPS_RSTK_CHAR_SET], '')?></td>
																				</tr>
																			</table>
																		</td>
																	</tr>
																	<tr>
																		<td>
																			<table border="0" cellspacing="0" cellpadding="0">
																				<tr>
																					<!--td class="main"><?=ENTRY_MAX_LIMIT . '&nbsp;' . tep_draw_input_field(KEY_SPS_MAX_LIMIT, $pricing_setting_array[KEY_SPS_MAX_LIMIT], ' size="8" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ')?></td>
																					<td class="main"><?=ENTRY_OVER_LIMIT_DISCOUNT . '&nbsp;' . tep_draw_input_field(KEY_SPS_OVER_LIMIT_DISCOUNT, $pricing_setting_array[KEY_SPS_OVER_LIMIT_DISCOUNT], ' size="5" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ') . '%'?></td-->
																					<td class="main"><?=ENTRY_QUANTITY_RATIO . '&nbsp;' . tep_draw_input_field(KEY_SPS_QUANTITY_RATIO, $pricing_setting_array[KEY_SPS_QUANTITY_RATIO], ' id="'.KEY_SPS_QUANTITY_RATIO.'" size="4" onKeyUP="if (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ' . ($pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY]=='1' ? 'DISABLED' : '')) . '%'?></td>
																					<td>&nbsp;&nbsp;OR&nbsp;&nbsp;</td>
																					<td class="main"><?=tep_draw_checkbox_field(KEY_SPS_USE_MFC_SUPPLY, '1', $pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY]=='1' ? true : false, '', 'onClick="if(this.checked){DOMCall(\''.KEY_SPS_QUANTITY_RATIO.'\').disabled=true; DOMCall(\''.KEY_SPS_MFC_SUPPLY_MAX_QTY.'\').disabled=false; DOMCall(\''.KEY_SPS_MFC_SUPPLY_MAX_QTY.'\').value=\''.$suggested_ms_max_qty.'\';}else{DOMCall(\''.KEY_SPS_QUANTITY_RATIO.'\').disabled=false; DOMCall(\''.KEY_SPS_MFC_SUPPLY_MAX_QTY.'\').disabled=true; DOMCall(\''.KEY_SPS_MFC_SUPPLY_MAX_QTY.'\').value=\'\';}"')?></td>
																					<td class="main"><?=ENTRY_USE_MFC_SUPPLY?></td>
																					<td>&nbsp;&nbsp;</td>
																					<td class="main"><?=ENTRY_MS_MAX_QTY . '&nbsp;' . tep_draw_input_field(KEY_SPS_MFC_SUPPLY_MAX_QTY, $pricing_setting_array[KEY_SPS_MFC_SUPPLY_MAX_QTY], ' id="'.KEY_SPS_MFC_SUPPLY_MAX_QTY.'" size="7" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ' . ($pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY]!='1' ? 'DISABLED' : ''))?></td>
																				</tr>
																			</table>
																		</td>
																	</tr>
																	<tr>
																		<td>
																			<table border="0" cellspacing="0" cellpadding="0">
																				<tr>
																					<td class="main"><?=ENTRY_MIN_QTY . '&nbsp;' . tep_draw_input_field(KEY_SPS_MIN_QTY, $pricing_setting_array[KEY_SPS_MIN_QTY], ' id="'.KEY_SPS_MIN_QTY.'" size="5" onKeyUP="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ')?></td>
																					<td>&nbsp;&nbsp;</td>
																				</tr>
																			</table>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
													</table>
												</td>
												<td align="right" valign="top">
													<div id="show_batch_fill"><a href="javascript:;" onclick="show_hide_batch_fill(true)"><?=TEXT_SHOW_BATCH_FILL?></a></div>
													<div id="show_disabled"><a href="javascript:;" onclick="show_hide_disabled(false)"><?=TEXT_HIDE_DISABLED?></a></div>
													<div id="show_product_name"><a href="javascript:;" onclick="show_hide_product_name(false)"><?=TEXT_HIDE_PRODUCT_NAME?></a></div>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
									<?
										if (count($pricing_setting_array)) {
											echo tep_button(sprintf(LINK_REMOVE_LIST, $this_list_name, $this_group_name), sprintf(LINK_REMOVE_LIST, $this_list_name, $this_group_name), '', 'onClick="if (confirm_action(\''.JS_CONFIRM_REMOVE_LIST.'\')) { window.location=\''.tep_href_link(FILENAME_SUPPLIERS_PRICING, tep_get_all_get_params(array('subaction', 'supplier_groups_id', 'list_id')) . 'subaction=remove_list&supplier_groups_id='.$this_supplier_group_id.'&list_id='.$list_id).'\' }"', 'inputButton');
										}
									?>
									</td>
								</tr>
								<tr>
									<td>
										<table width="100%" border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td class="ordersBoxHeading"><?=tep_draw_checkbox_field('checkAll', '', false, '', ' onclick="select_all_checkbox_click(this, \'chkSelected\', true)" ')?></td>
												<td class="ordersBoxHeading"><?=TABLE_HEADING_PRODUCT?></td>
<?				if ($buyback_price_info_permission) {
					echo '						<td align="center" class="ordersBoxHeading" width="6%">'.TABLE_HEADING_BUYBACK_PRICE.'</td>';
				}
				
				if ($show_product_purchase_status) {
					echo '						<td width="10%" class="ordersBoxHeading">'.TABLE_HEADING_STATUS.'</td>';
				}
?>
												<td width="8%" class="ordersBoxHeading" align="right"><?=TABLE_HEADING_MAX_QTY?></td>
												<td width="8%" class="ordersBoxHeading" align="center">
													<?=TABLE_HEADING_OVERWRITE_MAX_QTY?><br>
													<a href="javascript:;" onclick="clear_input_boxes('txtSellingQty')" class="highlightLink"><?=TEXT_CLEAR?></a>
												</td>
<?				if ($unit_price_info_permission) {
					echo '						<td class="ordersBoxHeading" align="center">'.TABLE_HEADING_UNIT_PRICE.'</td>';
				}
?>
												<td class="ordersBoxHeading" align="right">
													<?=TABLE_SERVER_FULL?><br>
													<?=tep_draw_checkbox_field('select_all_server_full', '1', false, '', 'id="select_all" title="Select or deselect all server full" onclick="javascript:void(select_all_checkbox_click(this, \'chkFull\', true));"')?>
												</td>
												<td class="ordersBoxHeading" align="right" valign="bottom">
													<?=TABLE_HEADING_DISABLED?><br>
													<?=tep_draw_checkbox_field('select_all_disabled', '2', false, '', 'id="select_all" title="Select or deselect all disabled" onclick="javascript:void(select_all_checkbox_click(this, \'chkDisabled\', true));"')?>
												</td>
<?				if ($restock_account_info_permission) {
					echo '						<td width="15%" class="ordersBoxHeading">'.TABLE_HEADING_COMMENT.'<br>'.TABLE_HEADING_SHOW_COMMENT.'</td>';
					echo '						<td class="ordersBoxHeading" valign="bottom" align="right">'.tep_draw_checkbox_field('select_all_comment', '', false, '', 'id="select_all" title="Select or deselect all show restock account" onclick="javascript:void(select_all_checkbox_click(this, \'chkShowComment\', true));"').'</td>';
				}
?>
											</tr>
<?
				if ($HTTP_POST_VARS['btn_csv_import']) {
					$js = '';
					//print_r($imported_products_array);
					for ($i=0, $row_count=0; $i < count($imported_products_array); $i++) {
						$row = $imported_products_array[$i];
						
						if ($row["products_purchases_disabled"] != 1) {
							$js .= "products_ids.push(".$row['prd_id'].");\n";
							
							$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
							$row_count++;
							
							$disabled = ($row['supplier_pricing_disabled']==1 || $row['supplier_pricing_server_full']==1) ? 'DISABLED' : '';
							$disabled_style = ($row['supplier_pricing_disabled']==1 || $row['supplier_pricing_server_full']==1) ? ' style="background:#D4D0C8"' : '';
							
							$products_cat_path = $row['prd_full_path'];
							$product_name = tep_get_products_name((int)$row['prd_id']);
?>
											<tbody id="row<?=(int)$row['prd_id']?>">
												<tr class="<?=$row_style?>" id="<? echo "custRow_".(int)$row['prd_id'];?>" onmouseover="rowOverEffect(this, 'ordersListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
													<td class="ordersRecords" valign="top"><?=tep_draw_checkbox_field("chkSelected[".$row['prd_id']."]", '1', false, '', ' id="chkSelected'.$row['prd_id'].'" ')?></td>
													<td class="ordersRecords"><?=$products_cat_path . '<span id="span_prod_name_'.(int)$row['prd_id'].'" class="show"> > ' . $product_name . ' ('.$row['prd_id'] . ')</span>'?></td>
<?							if ($buyback_price_info_permission) {
								echo '				<td align="right" class="ordersRecords">'.$row["products_buyback_price"].'&nbsp;</td>';
							}
							
							if ($show_product_purchase_status) {
								echo '				<td class="ordersRecords">'.tep_draw_pull_down_menu("selStatus[".$row['prd_id']."]", $product_purchase_status_array, $row['supplier_pricing_product_status'], 'id="selStatus'.$row['prd_id'].'" ' . $disabled).'</td>';
							}
?>
													<td class="ordersRecords" align="right"><?=(int)$row['group_max_qty']?></td>
													<td class="ordersRecords" align="center"><?=tep_draw_input_field("txtSellingQty[".$row['prd_id']."]", tep_not_null($row['supplier_pricing_max_quantity'])!='' ? $row['supplier_pricing_max_quantity'] : '', 'size="7" id="txtSellingQty'.$row['prd_id'].'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" ' . $disabled . $disabled_style)?></td>
<?							if ($unit_price_info_permission) {
								echo '				<td class="ordersRecords" align="center">'.tep_draw_input_field("txtUnitPrice[".$row['prd_id']."]", number_format($row['supplier_pricing_unit_price'], DISPLAY_PRICE_DECIMAL), 'size="6" id="txtUnitPrice'.$row['prd_id'].'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" '.$disabled).'</td>';
							}
?>
													<td class="ordersRecords" align="right"><?=tep_draw_checkbox_field("chkFull[".$row['prd_id']."]","1", ((int)$row['supplier_pricing_server_full']==1) ? true : false, '', ' id="chkFull'.$row['prd_id'].'" onClick="disableTextBoxes('.$row['prd_id'].',this,1)" '.($row['supplier_pricing_disabled']==1 ? 'DISABLED' : ''))?></td>
													<td class="ordersRecords" align="right"><?=tep_draw_checkbox_field("chkDisabled[".$row['prd_id']."]", "1",((int)$row['supplier_pricing_disabled']==1) ? true : false, '', ' id="chkDisabled'.$row['prd_id'].'" onclick="disableTextBoxes('.$row['prd_id'].',this,2)" ')?></td>
<?							if ($restock_account_info_permission) {
								echo '				<td class="ordersRecords">'.($row['restock_character_exists'] == '1' ? $row['restock_character'] : '<span class="redIndicator">'.TEXT_NOT_EXISTS.'</span>').'</td>';
								// When import default to not show
								echo '				<td class="ordersRecords" align="right">'.tep_draw_checkbox_field("chkShowComment[".$row['prd_id']."]", '1', false, '', ' id="chkShowComment'.$row['prd_id'].'" ' . $disabled).'</td>';
							}
							
							echo tep_draw_hidden_field("product_ids[]", (int)$row['prd_id']);
?>
												</tr>
											</tbody>
<?						}
					}
				} else {
					$products_list_select_product = "	SELECT p.products_id as prd_id, p.products_buyback_price, pd.products_name, p.products_cat_path, sp.*, pp.products_purchases_selling_quantity AS selling_qty, pp.products_purchases_disabled, rci.restock_character_sets_id, rci.restock_character 
														FROM " . TABLE_PRODUCTS . " AS p 
														INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
															ON p.products_id=pd.products_id 
														INNER JOIN " . TABLE_PRODUCTS_PURCHASES . " AS pp 
															ON (pp.products_purchases_lists_id='".$list_id."' AND p.products_id=pp.products_id AND pp.products_purchases_disabled <> 1) 
														LEFT JOIN " . TABLE_SUPPLIER_PRICING . " AS sp 
															ON (sp.products_purchases_lists_id='".$list_id."' AND p.products_id=sp.products_id AND sp.supplier_groups_id='" . $this_supplier_group_id . "') 
														LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci 
															ON (p.products_id=rci.products_id AND rci.restock_character_sets_id='" . tep_db_input($pricing_setting_array[KEY_SPS_RSTK_CHAR_SET]) . "') 
														WHERE p.products_bundle='' AND p.products_bundle_dynamic='' AND p.custom_products_type_id=0 AND pd.language_id = '" . $languages_id . "' 
														ORDER BY p.products_cat_path, pd.products_name, p.products_sort_order;";
					$result_select_product = tep_db_query($products_list_select_product);
					
					$row_count = 0;
					$js = '';
					while ($row = tep_db_fetch_array($result_select_product)) {
						if ($row["products_purchases_disabled"] != 1) {
							$js .= "products_ids.push(".$row['prd_id'].");\n";
							
							$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
							
							if (isset($pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY]) && $pricing_setting_array[KEY_SPS_USE_MFC_SUPPLY] == 1) {
								$group_max_qty = $pricing_setting_array[KEY_SPS_MFC_SUPPLY_MAX_QTY];
							} else {
								$group_max_qty = ceil(((int)$row['selling_qty'] * (double)$pricing_setting_array[KEY_SPS_QUANTITY_RATIO]) / 100);
							}
							
							if (!tep_not_null($row['supplier_groups_id'])) {
								// Unassigned products for this group set to disabled by default
								$product_name_style = "redIndicator";
								$row['supplier_pricing_disabled'] = 1;
							} else {
								$product_name_style = '';
							}
							
							$disabled = ($row['supplier_pricing_disabled']==1 || $row['supplier_pricing_server_full']==1) ? 'DISABLED' : '';
							$disabled_style = ($row['supplier_pricing_disabled']==1 || $row['supplier_pricing_server_full']==1) ? ' style="background:#D4D0C8"' : '';
?>
											<tbody id="row<?=(int)$row['prd_id']?>">
												<tr class="<?=$row_style?>" id="<? echo "custRow_".(int)$row['prd_id'];?>" onmouseover="rowOverEffect(this, 'ordersListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
													<td class="ordersRecords" valign="top"><?=tep_draw_checkbox_field("chkSelected[".$row['prd_id']."]", '1', false, '', ' id="chkSelected'.$row['prd_id'].'" ')?></td>						
													<td class="ordersRecords"><span class="<?=$product_name_style?>"><?=$row['products_cat_path'] . '<span id="span_prod_name_'.(int)$row['prd_id'].'" class="show"> > ' . $row['products_name'] . ' ('.$row['prd_id'] . ')</span>'?></span></td>
<?							if ($buyback_price_info_permission) {
								echo '				<td align="right" class="ordersRecords">'.$row["products_buyback_price"].'&nbsp;</td>';
							}
							
							if ($show_product_purchase_status) {
								echo '				<td class="ordersRecords">'.tep_draw_pull_down_menu("selStatus[".$row['prd_id']."]", $product_purchase_status_array, $row['supplier_pricing_product_status'], 'id="selStatus'.$row['prd_id'].'" ' . $disabled . $disabled_style).'</td>';
							}
?>
													<td class="ordersRecords" align="right"><?=$group_max_qty?></td>
													<td class="ordersRecords" align="center"><?=tep_draw_input_field("txtSellingQty[".$row['prd_id']."]", tep_not_null($row['supplier_pricing_max_quantity'])!='' ? $row['supplier_pricing_max_quantity'] : '', 'size="7" id="txtSellingQty'.$row['prd_id'].'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; }" ' . $disabled . $disabled_style)?></td>
<?							if ($unit_price_info_permission) {
								echo '				<td class="ordersRecords" align="center">'.
														tep_draw_input_field("txtUnitPrice[".$row['prd_id']."]", number_format($row['supplier_pricing_unit_price'], DISPLAY_PRICE_DECIMAL), 'size="6" id="txtUnitPrice'.$row['prd_id'].'" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !currencyValidation(trim_str(this.value))) ) { this.value = \'\'; }" onKeyPress="return noEnterKey(event)" ' . $disabled . $disabled_style) . '
													</td>';
							}
?>
													<td class="ordersRecords" align="right"><?=tep_draw_checkbox_field("chkFull[".$row['prd_id']."]", '1', ((int)$row['supplier_pricing_server_full']==1) ? true : false,'',' id="chkFull'.$row['prd_id'].'" onclick="disableTextBoxes('.$row['prd_id'].', this, 1)" '.($row['supplier_pricing_disabled']==1 ? 'DISABLED' : ''))?></td>
													<td class="ordersRecords" align="right"><?=tep_draw_checkbox_field("chkDisabled[".$row['prd_id']."]", '1', ((int)$row['supplier_pricing_disabled']==1) ? true : false,'',' id="chkDisabled'.$row['prd_id'].'" onclick="disableTextBoxes('.$row['prd_id'].', this, 2)" ')?></td>
<?							if ($restock_account_info_permission) {
								echo '				<td class="ordersRecords">'.(is_null($row['restock_character_sets_id']) ? '<span class="redIndicator">'.TEXT_NOT_EXISTS.'</span>' : $row['restock_character']).'</td>';
								echo '				<td class="ordersRecords" align="right">'.tep_draw_checkbox_field("chkShowComment[".$row['prd_id']."]", '1', ((int)$row['supplier_pricing_show_comment']==1) ? true : false, '', ' id="chkShowComment'.$row['prd_id'].'" ' . $disabled).'</td>';
							}
							
							echo tep_draw_hidden_field("product_ids[]", (int)$row['prd_id']);
?>
												</tr>
											</tbody>
<? 							$row_count++;
						}
					}
				}
?>
										</table>
									</td>
								</tr>
								<tr>
									<td><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
								</tr>
								<tr>
									<td>
										<table width="100%"  border="0" cellspacing="0" cellpadding="2">
											<tr>
												<td align="left">
												<?
													if ($row_count > 0) {
														echo tep_draw_file_field('csv_import', 'size="50"') . '&nbsp;';
														echo tep_submit_button('Import', 'Import csv file', 'name="btn_csv_import"', 'inputButton');
													}
												?>
												</td>
												<td align="right">
												<?
													if ($row_count > 0) {
														echo tep_submit_button('Update & Export', 'Update and Export as csv file', 'name="btn_csv_export"', 'inputButton') . '&nbsp;';
													}
													echo tep_submit_button(IMAGE_BUTTON_UPDATE, IMAGE_BUTTON_UPDATE, 'name="btn_db_update"', 'inputButton');
												?>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								</form>
							</table>
						</td>
					</tr>
					<script>
						<?=$js?>
						var disabled_state = GetCookie('pricing_show_hide_disabled');
						show_hide_disabled(disabled_state == 1 ? true : false);
						
						var pname_state = GetCookie('pricing_show_hide_pname');
						show_hide_product_name(pname_state == 1 ? true : false);
					</script>
<?			}
		}
	} else {
		$deleted_products_select_sql = "SELECT sp.products_id 
										FROM " . TABLE_SUPPLIER_PRICING . " AS sp 
										LEFT JOIN " . TABLE_PRODUCTS . " AS p 
											ON sp.products_id = p.products_id 
										WHERE p.products_id IS NULL 
										GROUP BY sp.products_id";
		$deleted_products_result_sql = tep_db_query($deleted_products_select_sql);
		while ($deleted_products_row = tep_db_fetch_array($deleted_products_result_sql)) {
			tep_db_query("DELETE FROM " . TABLE_SUPPLIER_PRICING . " WHERE products_id = '" . tep_db_input($deleted_products_row["products_id"]) . "'");
		}
	}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
<?
}
?>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>