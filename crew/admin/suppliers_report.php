<?
/*
  	$Id: suppliers_report.php,v 1.23 2009/09/29 01:56:35 weichen Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

require(DIR_WS_CLASSES . 'suppliers_report.php');
require(DIR_WS_CLASSES . 'currencies.php');

define('DISPLAY_PRICE_DECIMAL', 4);

$currencies = new currencies();

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

$buyback_from_array = array('cn' => SOURCE_CHINA_BUYBACK,
							'us' => SOURCE_WEBSITE_BUYBACK,
							'sup' => SOURCE_SUPPLIER_MODULE
					  		);

if (tep_not_null($action)) {
	switch ($action) {
        case 'reset_session':
        	unset($_SESSION['sup_report_param']);
        	tep_redirect(tep_href_link(FILENAME_SUPPLIERS_REPORT));
        	
        	break;
        case 'export_report':
        	$export_csv_array = tep_array_unserialize($HTTP_POST_VARS["serialized_export_csv_array"]);
			$export_csv_data = '';
			
			if (count($export_csv_array)) {
				foreach ($export_csv_array as $pid => $res) {
					$tmp_cvs_data_array = array();
					for ($i=0; $i < count($res); $i++) {
						$tmp_cvs_data_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res[$i]) . '"';
					}
					$export_csv_data .= implode(',', $tmp_cvs_data_array) . "\n";
				}
			}
			
			if (tep_not_null($export_csv_data)) {
				$filename = 'supplier_report_'.date('YmdHis').'.csv';
				$mime_type = 'text/x-csv';
				// Download
		        header('Content-Type: ' . $mime_type);
		        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
		        // IE need specific headers
		        if (PMA_USR_BROWSER_AGENT == 'IE') {
		            header('Content-Disposition: inline; filename="' . $filename . '"');
		            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		            header('Pragma: public');
		        } else {
		            header('Content-Disposition: attachment; filename="' . $filename . '"');
		            header('Pragma: no-cache');
		        }
				echo $export_csv_data;
				exit();
			} else {
				$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
				tep_redirect(tep_href_link(FILENAME_SUPPLIERS_REPORT, tep_get_all_get_params(array('action'))));
			}
        	break;
	}
}

$server_status_array = array(	'urgent' => array('name' => 'icon_status_urgent'), 
								'important' => array('name' => 'icon_status_important'), 
								'normal' => array('name' => 'icon_status_normal')
							);

$export_csv_array = array();
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="JavaScript" src="includes/javascript/select_box.js"></script>
	<script language="JavaScript" src="<?=DIR_WS_INCLUDES?>javascript/supplier_xmlhttp.js"></script>
	<script language="javascript">
	<!--
		function init() { initInfoCaptions(); }
		
		/* for other browsers */
	   	window.onload = init;
	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
	<!-- Include div for stat breakdown info //-->
	<div id="dhtmlTooltip"></div>
	<script language="javascript" src="<?=DIR_WS_INCLUDES?>javascript/dhtml_tooltip.js"></script>
	<!-- End of include div for stat breakdown info //-->
	
	<!-- Popup //-->
	<script language="JavaScript" src="<?=DIR_WS_INCLUDES?>addon/scriptasylum_popup/popup.js"></script>
	<!-- End of Popup //-->
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
  	if ($_REQUEST['action'] == 'show_report') {
  		if (!$_REQUEST['cont']) {
  			$_SESSION['sup_report_param']['report'] = $_REQUEST["report"];
  			$_SESSION['sup_report_param']["sup_grp_to"] = $_REQUEST["sup_grp_to"];
  			$_SESSION['sup_report_param']["buyback_from_any"] = $_REQUEST["buyback_from_any"];
  			$_SESSION['sup_report_param']["buyback_from"] = $_REQUEST["buyback_from"];
  			$_SESSION['sup_report_param']["cat_id"] = $_REQUEST["cat_id"];
  			$_SESSION['sup_report_param']['start_date'] = $_REQUEST["start_date"];
  			$_SESSION['sup_report_param']['end_date'] = $_REQUEST["end_date"];
  			$_SESSION['sup_report_param']['order_status'] = $_REQUEST["order_status"];
	  	}
	  	
  		$report_obj = new suppliers_report($_REQUEST["report"], $_REQUEST["start_date"], $_REQUEST["end_date"], $_REQUEST['cont']);
  		
  		if (isset($_SESSION['sup_report_param']["order_status"]) && count($_SESSION['sup_report_param']["order_status"])) {
  			$order_status_where_str = " sol.supplier_order_lists_status IN (" . implode(", ", $_SESSION['sup_report_param']["order_status"]) . ") ";
  		} else {
  			$order_status_where_str = "1";
  		}
  		
  		$report_obj->set_cat_id($_REQUEST["cat_id"]);
  		$report_obj->set_grp_array($_SESSION['sup_report_param']["sup_grp_to"]);
  		$report_obj->set_order_status_array($_SESSION['sup_report_param']["order_status"]);
  		
  		if ($report_obj->mode == '4') {
  			$report_obj->set_buyback_from_array($_SESSION['sup_report_param']["buyback_from_any"]=='1' ? array_keys($buyback_from_array): $_SESSION['sup_report_param']["buyback_from"]);
  		}
  		
  		$report_obj->sql_filter = $order_status_where_str;
		$report_obj->reportQuery();
  		$report_obj->getSummaryText();
?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
			        				<td>
			        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
			          						<tr>
			            						<td class="pageHeading" valign="top"><?=$report_obj->summary["report_title"]?></td>
			            						<td class="main" align="right">
			            							<a href="<?=tep_href_link(FILENAME_SUPPLIERS_REPORT)?>"><?=LINK_BACK?></a>
			            						</td>
			          						</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
<?
		if ($report_obj->summary["date_heading"]) {
?>
			          						<tr>
			          							<td class="main" colspan="2"><?=$report_obj->summary["date_heading"]?></td>
			          						</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
<?		} ?>
			        					</table>
			        				</td>
			      				</tr>
          						<tr>
            						<td valign="top">
<?
		switch ($report_obj->mode) {
			case 1:
			case 2:
			case 3:
				$has_sub_title = ($report_obj->mode == 1) ? false : true ;
				$rowspan = $has_sub_title ? 2 : 1 ;
				$colspan = $has_sub_title ? count($report_obj->subtitle) : 1 ;
				$column_total_count = array();
				$column_total_amount = array();
				
				$unit_price_stat_alt = 'unit price / total received quantity';
				$report_stat_alt = 'total received quantity / total max quantity (of servers in confirmation list)';
				
				$export_csv_array['HEADER'] = array($report_obj->summary["first_column_title"]);
				
				echo '					<table border="0" width="100%" cellspacing="1" cellpadding="2">	
			   								<tr>
			       								<td class="reportBoxHeading" rowspan="'.$rowspan.'">'.$report_obj->summary["first_column_title"].'</td>';
				if (isset($report_obj->summary["second_column_title"])) {
					echo '						<td align="center" class="reportBoxHeading" rowspan="'.$rowspan.'">'.sprintf($report_obj->summary["second_column_title"], tep_not_null($currencies->currencies[DEFAULT_CURRENCY]["symbol_left"]) ? $currencies->currencies[DEFAULT_CURRENCY]["symbol_left"] : $currencies->currencies[DEFAULT_CURRENCY]["symbol_right"]).'</td>';
					$export_csv_array['HEADER'][] = sprintf($report_obj->summary["second_column_title"], tep_not_null($currencies->currencies[DEFAULT_CURRENCY]["symbol_left"]) ? $currencies->currencies[DEFAULT_CURRENCY]["symbol_left"] : $currencies->currencies[DEFAULT_CURRENCY]["symbol_right"]);
				}
				
				for ($i=$report_obj->size-1; $i>=0 ; $i--) {
		   			echo '						<td class="reportBoxHeading" align="center" valign="bottom" width="10%" colspan="'.$colspan.'" nowrap>';
		   			echo date("Y-m-d", $report_obj->startDates[$i]);
		   			echo '						</td>';
		   			
		   			$export_csv_array['HEADER'][] = date("Y-m-d", $report_obj->startDates[$i]);
		   			
		   			if ($has_sub_title) {
		   				if ($i > 0) echo '		<td align="center" valign="top" class="cell_separator"></td>';
		   				
		   				for ($empty_field_cnt=0; $empty_field_cnt < count($report_obj->subtitle)-1; $empty_field_cnt++) {
		   					$export_csv_array['HEADER'][] = '';
		   				}
		   			}
		   		}
			   	
			   	echo '						</tr>';
				
				if ($has_sub_title) {
					$export_csv_array['SUB_HEADER'] = array('', '');
					echo '					<tr>';
					for ($i=$report_obj->size-1; $i>=0 ; $i--) {
						for ($j=0; $j < count($report_obj->subtitle); $j++) {
							echo '				<td class="reportBoxHeading" align="center" valign="bottom" width="5%">';
				   			echo '<span title="'.$report_obj->subtitle[$j]['alt'].'">' . $report_obj->subtitle[$j]['text'] . '</span>';
				   			echo '				</td>';
				   			
				   			$export_csv_array['SUB_HEADER'][] = $report_obj->subtitle[$j]['text'];
						}
						if ($i > 0)	echo '		<td align="center" valign="top" class="cell_separator"></td>';
					}
					echo '					</tr>';
				}
				
				for ($i=0; $i < count($report_obj->info) ; $i++) {
					$row_style = ($i%2) ? 'invoiceListingEven' : 'invoiceListingOdd';
					
					ob_start();
					$total_pay_amount = 0;
					$total_recv_qty = 0;
					
					$export_csv_array[$i] = array($report_obj->info[$i]['name']);
					
					echo '					<tr class="'.$row_style.'" height="26" id="'.$i.'" onMouseOver="rowOverEffect(this, \'invoiceListingRowOver\')" onMouseOut="rowOutEffect(this, \''.$row_style.'\')" onClick="rowClicked(this, \''.$row_style.'\')">
												<td class="reportRecords" valign="top">'.$report_obj->info[$i]['name'].'</td>';
					if (isset($report_obj->summary["second_column_title"])) {
						echo '					<td width="7%" class="reportRecords" align="center" valign="top" nowrap>##AVG_BUYBACK_PRICE##</td>';
						$export_csv_array[$i][] = '##AVG_BUYBACK_PRICE##';
					}
					for ($j=$report_obj->size-1; $j>=0 ; $j--) {
						if ($has_sub_title) {
							for ($k=0; $k < count($report_obj->subtitle); $k++) {
								$date_list_info_array = $report_obj->info[$i]['list_info'][$report_obj->startDates[$j]][$report_obj->subtitle[$k]['id']];
					   			echo '			<td class="reportRecords" align="center" valign="top" nowrap title="'.$unit_price_stat_alt.'">';
					   			if (tep_not_null($date_list_info_array['sec_list_qty']) && $date_list_info_array['recv_qty'] > 0) {
					   				$total_pay_amount += $date_list_info_array['total_amount'];
									$total_recv_qty += $date_list_info_array['recv_qty'];
									
					   				echo '<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $date_list_info_array['sec_list_up']).'</div>';
									echo '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
									echo '<div>'.(int)$date_list_info_array['recv_qty'].'</div>';
									
									$column_total_amount[$report_obj->startDates[$j]][$report_obj->subtitle[$k]['id']] += $date_list_info_array['total_amount'];
									$column_total_count[$report_obj->startDates[$j]][$report_obj->subtitle[$k]['id']] += (int)$date_list_info_array['recv_qty'];
									
									$export_csv_array[$i][] = (int)$date_list_info_array['recv_qty'] . ' (' . sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $date_list_info_array['sec_list_up']) . ')';
					   			} else {
					   				$export_csv_array[$i][] = '';
					   			}
					   			echo '			</td>';
							}
							if ($j > 0)	echo '	<td align="center" valign="top" class="cell_separator"></td>';
						} else {
							$date_list_info_array = $report_obj->info[$i]['list_info'][$report_obj->startDates[$j]];
							
				   			echo '					<td class="reportRecords" align="center" valign="top" nowrap title="'.$unit_price_stat_alt.'">';
				   			if (tep_not_null($date_list_info_array['sec_list_qty']) && $date_list_info_array['recv_qty'] > 0) {
				   				$total_pay_amount += $date_list_info_array['total_amount'];
								$total_recv_qty += $date_list_info_array['recv_qty'];
								
				   				echo '<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $date_list_info_array['sec_list_up']).'</div>';
								echo '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
								echo '<div>'.(int)$date_list_info_array['recv_qty'].'</div>';
								
								$column_total_amount[$report_obj->startDates[$j]] += $date_list_info_array['total_amount'];
								$column_total_count[$report_obj->startDates[$j]] += (int)$date_list_info_array['recv_qty'];
								
								$export_csv_array[$i][] = (int)$date_list_info_array['recv_qty'] . ' (' . sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $date_list_info_array['sec_list_up']) . ')';
				   			} else {
				   				$export_csv_array[$i][] = '';
				   			}
				   			echo '				</td>';
				   		}
			   		}
					echo '					</tr>';
					
					$server_rows_html = ob_get_contents();
					ob_end_clean();
					
					if (isset($report_obj->summary["second_column_title"])) {
						$array_key_found = false;
						if (in_array('##AVG_BUYBACK_PRICE##', $export_csv_array[$i])) {
							$avg_buyback_array_index = array_search('##AVG_BUYBACK_PRICE##', $export_csv_array[$i]);
							$array_key_found = true;
						}
						
						if ($total_recv_qty > 0) {
							$column_total_amount['AVG_BUYBACK_PRICE'] += $total_pay_amount;
							$column_total_count['AVG_BUYBACK_PRICE'] += $total_recv_qty;
							
							$avg_buyback_html = '<div>
													<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $total_pay_amount).'</div>
													<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 50px;"></div>
													<div>'.$total_recv_qty.'</div>
												</div>
												<div>='.number_format($total_pay_amount/$total_recv_qty, DISPLAY_PRICE_DECIMAL).'</div>';
							$server_rows_html = str_replace('##AVG_BUYBACK_PRICE##', $avg_buyback_html, $server_rows_html);
							
							if ($array_key_found) {
								$export_csv_array[$i][$avg_buyback_array_index] = $total_recv_qty . ' (' . number_format($total_pay_amount/$total_recv_qty, DISPLAY_PRICE_DECIMAL) . ')';
							}
						} else {
							$server_rows_html = str_replace('##AVG_BUYBACK_PRICE##', '&nbsp;', $server_rows_html);
							if ($array_key_found) {
								$export_csv_array[$i][$avg_buyback_array_index] = '';
							}
						}
					}
					echo $server_rows_html;
				}
				
				if (count($report_obj->info)) {
    				echo '					<tr>
												<td class="reportRecords" align="right" valign="top"><b>Total:</b></td>';
					if (isset($report_obj->summary["second_column_title"])) {
						echo '					<td class="reportRecords" align="center" valign="top">';
						if ($column_total_count['AVG_BUYBACK_PRICE'] > 0) {
							echo '<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $column_total_amount['AVG_BUYBACK_PRICE']).'</div>';
							echo '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
							echo '<div>'.$column_total_count['AVG_BUYBACK_PRICE'].'</div>';
						}
						echo '					</td>';
					}
					for ($i=$report_obj->size-1; $i>=0 ; $i--) {
						if ($has_sub_title) {
							for ($j=0; $j < count($report_obj->subtitle); $j++) {
								echo '					<td class="reportRecords" align="center" nowrap>';
								if ($column_total_count[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']] > 0) {
									echo '<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $column_total_amount[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']]).'</div>';
									echo '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
									echo '<div>'.$column_total_count[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']].'</div>';
								}
					   			echo '					</td>';
							}
							if ($i > 0)	echo '			<td align="center" valign="top"></td>';
						} else {
							echo '					<td class="reportRecords" align="center" nowrap>';
							if ($column_total_count[$report_obj->startDates[$i]] > 0) {
								echo '<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $column_total_amount[$report_obj->startDates[$i]]).'</div>';
								echo '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
								echo '<div>'.$column_total_count[$report_obj->startDates[$i]].'</div>';
							}
							echo '					</td>';
				   		}
					}
					echo '					</tr>';
					
					echo '					<tr class="invoiceListingEven">
												<td class="reportRecords" align="right" valign="top"><b>Statistics:</b></td>';
					if (isset($report_obj->summary["second_column_title"])) {
						echo '					<td class="reportRecords" align="right" valign="top">&nbsp;</td>';
					}
					
					$report_stat_csv_format_array = array();
					
					for ($i=$report_obj->size-1; $i>=0 ; $i--) {
						if ($has_sub_title) {
							for ($j=0; $j < count($report_obj->subtitle); $j++) {
								echo '			<td>
													<table border="0" width="100%" cellspacing="0" cellpadding="0">';
								
								if (isset($report_obj->summary['legend']) && count($report_obj->summary['legend'])) {
									foreach ($report_obj->summary['legend'] as $this_legend_title => $this_legend_res) {
										$recv_qty = $confirm_list_qty = $request_qty = '';
										
										$status_name = $this_legend_res['name'];
										$status_colour = $this_legend_res['colour'];
										
										if ($this_legend_title == 'normal') {
											if (isset($report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']][$this_legend_title]) || 
												isset($report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']]['no_server_status'])) {
												$recv_qty = $report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']][$this_legend_title]['recv_qty'] + $report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']]['no_server_status']['recv_qty'];
												$confirm_list_qty = (int)$report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']][$this_legend_title]['sec_list_qty'] + (int)$report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']]['no_server_status']['sec_list_qty'];
												$request_qty = (int)$report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']][$this_legend_title]['request_qty'] + (int)$report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']]['no_server_status']['request_qty'];
											}
										} else {
											if (isset($report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']][$this_legend_title]) && count($report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']][$this_legend_title])) {
												$recv_qty = $report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']][$this_legend_title]['recv_qty'];
												$confirm_list_qty = (int)$report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']][$this_legend_title]['sec_list_qty'];
												$request_qty = (int)$report_obj->report_stat[$report_obj->startDates[$i]][$report_obj->subtitle[$j]['id']][$this_legend_title]['request_qty'];
											}
										}
										
										echo '			<tr height="26">
															<td class="reportRecords" align="center" bgcolor="'.$status_colour.'" title="'.$report_stat_alt.'">';
										if (tep_not_null($recv_qty) && $recv_qty > 0) {
											echo '<div>'.$recv_qty.'</div>';
											echo '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
											echo '<div>'.$request_qty.'</div>';
											
											$report_stat_csv_format_array[$this_legend_title][$i][$j] = $request_qty . ' (' . $recv_qty . ')';
										} else {
											echo '---';
											
											$report_stat_csv_format_array[$this_legend_title][$i][$j] = '';
										}
										echo '				</td>';
										echo '			</tr>';
									}
								}
								
					   			echo '				</table>
				   								</td>';
							}
							if ($i > 0)	echo '	<td align="center" valign="top"></td>';
						} else {
							echo '					<td>
														<table border="0" width="100%" cellspacing="0" cellpadding="0">';
							
							if (isset($report_obj->summary['legend']) && count($report_obj->summary['legend'])) {
								foreach ($report_obj->summary['legend'] as $this_legend_title => $this_legend_res) {
									$recv_qty = $confirm_list_qty = $request_qty = '';
									
									$status_name = $this_legend_res['name'];
									$status_colour = $this_legend_res['colour'];
									
									if ($this_legend_title == 'normal') {
										if (isset($report_obj->report_stat[$report_obj->startDates[$i]][$this_legend_title]) || 
											isset($report_obj->report_stat[$report_obj->startDates[$i]]['no_server_status'])) {
											$recv_qty = $report_obj->report_stat[$report_obj->startDates[$i]][$this_legend_title]['recv_qty'] + $report_obj->report_stat[$report_obj->startDates[$i]]['no_server_status']['recv_qty'];
											$confirm_list_qty = (int)$report_obj->report_stat[$report_obj->startDates[$i]][$this_legend_title]['sec_list_qty'] + (int)$report_obj->report_stat[$report_obj->startDates[$i]]['no_server_status']['sec_list_qty'];
											$request_qty = (int)$report_obj->report_stat[$report_obj->startDates[$i]][$this_legend_title]['request_qty'] + (int)$report_obj->report_stat[$report_obj->startDates[$i]]['no_server_status']['request_qty'];
										}
									} else {
										if (isset($report_obj->report_stat[$report_obj->startDates[$i]][$this_legend_title]) && count($report_obj->report_stat[$report_obj->startDates[$i]][$this_legend_title])) {
											$recv_qty = $report_obj->report_stat[$report_obj->startDates[$i]][$this_legend_title]['recv_qty'];
											$confirm_list_qty = (int)$report_obj->report_stat[$report_obj->startDates[$i]][$this_legend_title]['sec_list_qty'];
											$request_qty = (int)$report_obj->report_stat[$report_obj->startDates[$i]][$this_legend_title]['request_qty'];
										}
									}
									
									echo '					<tr height="26">
																<td class="reportRecords" align="center" bgcolor="'.$status_colour.'" title="'.$report_stat_alt.'">';
									if (tep_not_null($recv_qty) && $recv_qty > 0) {
										echo '<div>'.$recv_qty.'</div>';
										echo '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
										echo '<div>'.$request_qty.'</div>';
										
										$report_stat_csv_format_array[$this_legend_title][$i] = $request_qty . ' (' . $recv_qty . ')';
									} else {
										echo '---';
										
										$report_stat_csv_format_array[$this_legend_title][$i] = '';
									}
									echo '						</td>';
									echo '					</tr>';
								}
							}
							
				   			echo '						</table>
			   										</td>';
			   			}
					}
					echo '					</tr>';
					
					if (isset($report_obj->summary['legend']) && count($report_obj->summary['legend'])) {
						$csv_index = count($export_csv_array);
						foreach ($report_obj->summary['legend'] as $this_legend_title => $this_legend_res) {
							if (isset($report_obj->summary["second_column_title"])) {
								$export_csv_array[$csv_index][] = '';
							}
							
							$export_csv_array[$csv_index][] = ucfirst($this_legend_title);
							
							if (count($report_stat_csv_format_array[$this_legend_title])) {
								if ($has_sub_title) {
									for ($csv_cnt=count($report_stat_csv_format_array[$this_legend_title])-1; $csv_cnt >= 0; $csv_cnt--) {
										for ($csv_sub_cnt=0; $csv_sub_cnt < count($report_stat_csv_format_array[$this_legend_title][$csv_cnt]); $csv_sub_cnt++) {
											$export_csv_array[$csv_index][] = $report_stat_csv_format_array[$this_legend_title][$csv_cnt][$csv_sub_cnt];
										}
									}
								} else {
									for ($csv_cnt=count($report_stat_csv_format_array[$this_legend_title])-1; $csv_cnt >= 0; $csv_cnt--) {
										$export_csv_array[$csv_index][] = $report_stat_csv_format_array[$this_legend_title][$csv_cnt];
									}
								}
							}
							$csv_index++;
						}
					}
					
    			}
    			
    			break;
    		case 4:
				$has_sub_title = false;
				$rowspan = 1;
				$colspan = 1 ;
				$column_total_count = array();
				$column_total_amount = array();
				
				$unit_price_stat_alt = 'unit price / total received quantity';
				$report_stat_alt = 'total received quantity / total confirm quantity';
				
				$export_csv_array['HEADER'] = array($report_obj->summary["first_column_title"]);
				
				echo '					<table border="0" width="100%" cellspacing="1" cellpadding="2">	
			   								<tr>
			       								<td class="reportBoxHeading" rowspan="'.$rowspan.'">'.$report_obj->summary["first_column_title"].'</td>';
				if (isset($report_obj->summary["second_column_title"])) {
					echo '						<td align="center" class="reportBoxHeading" rowspan="'.$rowspan.'">'.sprintf($report_obj->summary["second_column_title"], tep_not_null($currencies->currencies[DEFAULT_CURRENCY]["symbol_left"]) ? $currencies->currencies[DEFAULT_CURRENCY]["symbol_left"] : $currencies->currencies[DEFAULT_CURRENCY]["symbol_right"]).'</td>';
					$export_csv_array['HEADER'][] = sprintf($report_obj->summary["second_column_title"], tep_not_null($currencies->currencies[DEFAULT_CURRENCY]["symbol_left"]) ? $currencies->currencies[DEFAULT_CURRENCY]["symbol_left"] : $currencies->currencies[DEFAULT_CURRENCY]["symbol_right"]);
				}
				
				for ($i=$report_obj->size-1; $i>=0 ; $i--) {
		   			echo '						<td class="reportBoxHeading" align="center" valign="bottom" width="10%" colspan="'.$colspan.'" nowrap>';
		   			echo date("Y-m-d", $report_obj->startDates[$i]);
		   			echo '						</td>';
		   			
		   			$export_csv_array['HEADER'][] = date("Y-m-d", $report_obj->startDates[$i]);
		   		}
			   	
			   	echo '						</tr>';
				
				for ($i=0; $i < count($report_obj->info) ; $i++) {
					$row_style = ($i%2) ? 'invoiceListingEven' : 'invoiceListingOdd';
					
					ob_start();
					$total_pay_amount = 0;
					$total_recv_qty = 0;
					
					$export_csv_array[$i] = array($report_obj->info[$i]['name']);
					
					echo '					<tr class="'.$row_style.'" height="26" id="'.$i.'" onMouseOver="rowOverEffect(this, \'invoiceListingRowOver\')" onMouseOut="rowOutEffect(this, \''.$row_style.'\')" onClick="rowClicked(this, \''.$row_style.'\')">
												<td class="reportRecords" valign="top">'.$report_obj->info[$i]['name'].'</td>';
					if (isset($report_obj->summary["second_column_title"])) {
						echo '					<td width="7%" class="reportRecords" align="center" valign="top" nowrap>##AVG_BUYBACK_PRICE##</td>';
						$export_csv_array[$i][] = '##AVG_BUYBACK_PRICE##';
					}
					for ($j=$report_obj->size-1; $j>=0 ; $j--) {
						$date_list_info_array = $report_obj->info[$i]['list_info'][$report_obj->startDates[$j]];
						$stat_cell_html = '';
						
			   			$stat_cell_html .= '	<td class="reportRecords" align="center" valign="top" nowrap ';
			   			if (tep_not_null($date_list_info_array['send_qty']) && $date_list_info_array['recv_qty'] > 0) {
			   				$qty_breakdown_dhtml = '';
			   				for ($buy_fr_cnt=0; $buy_fr_cnt < count($report_obj->buyback_from_array); $buy_fr_cnt++) {
			   					$qty_breakdown_dhtml .= $buyback_from_array[$report_obj->buyback_from_array[$buy_fr_cnt]] . ' - ' . (int)$date_list_info_array[$report_obj->buyback_from_array[$buy_fr_cnt]]['recv_qty'] . '<br>';
			   				}
			   				
			   				$stat_cell_html .= 'onmouseover="ddrivetip(\''.str_replace(array("\r\n", "\n"), array('', '<br>'), htmlspecialchars(addslashes($qty_breakdown_dhtml), ENT_QUOTES)).'\', \'\' , 170);" onmouseout="hideddrivetip();">';
			   				
			   				$total_pay_amount += $date_list_info_array['total_amount'];
							$total_recv_qty += $date_list_info_array['recv_qty'];
							
			   				$stat_cell_html .= '<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $date_list_info_array['unit_price']).'</div>';
							$stat_cell_html .= '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
							$stat_cell_html .= '<div><a href="javascript:;" onClick="buyback_qty_by_supplier(\''.$report_obj->info[$i]['id'].'\', \'Product\', \''.date("Y-m-d", $report_obj->startDates[$j]).'\', \''.date("Y-m-d", $report_obj->startDates[$j]).'\', \''.implode(',', $report_obj->buyback_from_array).'\', \''.implode(',', $report_obj->order_status_array).'\');">'.(int)$date_list_info_array['recv_qty'].'</a></div>';
							
							$column_total_amount[$report_obj->startDates[$j]] += $date_list_info_array['total_amount'];
							$column_total_count[$report_obj->startDates[$j]] += (int)$date_list_info_array['recv_qty'];
							
							$export_csv_array[$i][] = (int)$date_list_info_array['recv_qty'] . ' (' . sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $date_list_info_array['unit_price']) . ')';
			   			} else {
			   				$stat_cell_html .= '>';
			   				
			   				$export_csv_array[$i][] = '';
			   			}
			   			$stat_cell_html .= '	</td>';
			   			
			   			echo $stat_cell_html;
			   		}
					echo '					</tr>';
					
					$server_rows_html = ob_get_contents();
					ob_end_clean();
					
					if (isset($report_obj->summary["second_column_title"])) {
						$array_key_found = false;
						if (in_array('##AVG_BUYBACK_PRICE##', $export_csv_array[$i])) {
							$avg_buyback_array_index = array_search('##AVG_BUYBACK_PRICE##', $export_csv_array[$i]);
							$array_key_found = true;
						}
						
						if ($total_recv_qty > 0) {
							$column_total_amount['AVG_BUYBACK_PRICE'] += $total_pay_amount;
							$column_total_count['AVG_BUYBACK_PRICE'] += $total_recv_qty;
							
							$avg_buyback_html = '<div>
													<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $total_pay_amount).'</div>
													<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 50px;"></div>
													<div><a href="javascript:;" onClick="buyback_qty_by_supplier(\''.$report_obj->info[$i]['id'].'\', \'Product\', \''.date("Y-m-d",$report_obj->startDate).'\', \''.date("Y-m-d",$report_obj->endDate).'\', \''.implode(',', $report_obj->buyback_from_array).'\', \''.implode(',', $report_obj->order_status_array).'\');">'.$total_recv_qty.'</a></div>
												</div>
												<div>='.number_format($total_pay_amount/$total_recv_qty, DISPLAY_PRICE_DECIMAL).'</div>';
							$server_rows_html = str_replace('##AVG_BUYBACK_PRICE##', $avg_buyback_html, $server_rows_html);
							
							if ($array_key_found) {
								$export_csv_array[$i][$avg_buyback_array_index] = $total_recv_qty . ' (' . number_format($total_pay_amount/$total_recv_qty, DISPLAY_PRICE_DECIMAL) . ')';
							}
						} else {
							$server_rows_html = str_replace('##AVG_BUYBACK_PRICE##', '&nbsp;', $server_rows_html);
							if ($array_key_found) {
								$export_csv_array[$i][$avg_buyback_array_index] = '';
							}
						}
					}
					echo $server_rows_html;
				}
				
				if (count($report_obj->info)) {
    				echo '					<tr>
												<td class="reportRecords" align="right" valign="top"><b>Total:</b></td>';
					if (isset($report_obj->summary["second_column_title"])) {
						echo '					<td class="reportRecords" align="center" valign="top">';
						if ($column_total_count['AVG_BUYBACK_PRICE'] > 0) {
							echo '<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $column_total_amount['AVG_BUYBACK_PRICE']).'</div>';
							echo '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
							//echo '<div>'.$column_total_count['AVG_BUYBACK_PRICE'].'</div>';
							echo '<div><a href="javascript:;" onClick="buyback_qty_by_supplier(\''.$report_obj->cat_id.'\', \'Category\', \''.date("Y-m-d",$report_obj->startDate).'\', \''.date("Y-m-d",$report_obj->endDate).'\', \''.implode(',', $report_obj->buyback_from_array).'\', \''.implode(',', $report_obj->order_status_array).'\');">'.$column_total_count['AVG_BUYBACK_PRICE'].'</a></div>';
						}
						echo '					</td>';
					}
					for ($i=$report_obj->size-1; $i>=0 ; $i--) {
						echo '					<td class="reportRecords" align="center" nowrap>';
						if ($column_total_count[$report_obj->startDates[$i]] > 0) {
							echo '<div>'.sprintf('%0.'.DISPLAY_PRICE_DECIMAL.'f', $column_total_amount[$report_obj->startDates[$i]]).'</div>';
							echo '<div style="border-bottom: 1px solid rgb(0, 0, 0); font-size: 1px; width: 35px;"></div>';
							//echo '<div>'.$column_total_count[$report_obj->startDates[$i]].'</div>';
							echo '<div><a href="javascript:;" onClick="buyback_qty_by_supplier(\''.$report_obj->cat_id.'\', \'Category\', \''.date("Y-m-d", $report_obj->startDates[$i]).'\', \''.date("Y-m-d", $report_obj->startDates[$i]).'\', \''.implode(',', $report_obj->buyback_from_array).'\', \''.implode(',', $report_obj->order_status_array).'\');">'.$column_total_count[$report_obj->startDates[$i]].'</a></div>';
						}
						echo '					</td>';
					}
					echo '					</tr>';
    			}
    			
    			break;
		}
?>
										</table>
			   						</td>
			   					</tr>
			   				</table>
			   			</td>
			   		</tr>
			   		<tr>
            			<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
          			</tr>
			   		<tr>
			   			<td>
			   				<table border="0" width="<?=($report_obj->mode != '6')?"100%":"80%"?>" cellspacing="0" cellpadding="1">
<?		if ($order_count) { ?>
                    			<tr class="dataTableRow">
                      				<td class="reportRecords" width="100%" align="right"><?='<b>'. ENTRY_AVERAGE_ORDER . ' </b>'?></td>
                      				<td>&nbsp;</td>
                      				<td class="reportRecords" align="right" nowrap><?=$currencies->format($sum / $order_count)?></td>
                    			</tr>
<?		} ?>
   							</table>
   						</td>
   					</tr>
<?		if (count($report_obj->summary['legend']) && $report_obj->mode <= 3) { ?>
					<tr>
          				<td>
          					<table border="0" cellspacing="0" cellpadding="0">
								<tr>
								<?
								foreach ($report_obj->summary['legend'] as $this_legend_title => $this_legend_res) {
									echo '	<td>
												<div style="width:10px; height:10px; border: 1px solid black; background-color:'.$this_legend_res['colour'].'; font-size:5px"></div>
											</td>
											<td class="smallText" NOWRAP>&nbsp;'.$this_legend_res['name'].'&nbsp;&nbsp;</td>';
								}
								?>
								</tr>
							</table>
          				</td>
          			</tr>
<?		}
		
		if ($report_obj->mode <= 4) {
?>
					<tr>
						<td>
<?			echo tep_draw_form('suppliers_report_form', FILENAME_SUPPLIERS_REPORT, tep_get_all_get_params(array('action')) . 'action=export_report', 'post', '');
			echo tep_draw_hidden_field("serialized_export_csv_array", tep_array_serialize($export_csv_array)) . "\n";
?>
							<table width="100%" border="0" cellspacing="0" cellpadding="2">
								<tr>
									<td align="right"><?=tep_submit_button('Export', 'Export as csv file', 'name="btn_csv_export"', 'inputButton')?></td>
								</tr>
							</table>
							</form>
						</td>
					</tr>
<?
		}
 	} else {
		$report_obj = new suppliers_report('');
		
		$supplier_groups_from_array = array( array ('id' => '', "text" => 'Select Supplier Groups', "type" => 'optgroup') );
		$supplier_groups_to_array = array( array ('id' => '', "text" => 'Selected Supplier Groups', "type" => 'optgroup') );
		
		$supplier_group_select_sql = "SELECT supplier_groups_id, supplier_groups_name FROM " . TABLE_SUPPLIER_GROUPS ." ORDER BY supplier_groups_name";
		$supplier_group_result_sql = tep_db_query($supplier_group_select_sql);
		while($supplier_group_row = tep_db_fetch_array($supplier_group_result_sql)) {
			$supplier_groups_array[] = array(	'id' => $supplier_group_row['supplier_groups_id'],
												'text' => $supplier_group_row['supplier_groups_name']
											);
		}
		
		if (isset($_SESSION['sup_report_param']["sup_grp_to"]) && count($_SESSION['sup_report_param']["sup_grp_to"])) {
			for ($i=0; $i < count($supplier_groups_array); $i++) {
				if (in_array($supplier_groups_array[$i]['id'], $_SESSION['sup_report_param']["sup_grp_to"])) {
					$supplier_groups_to_array[] = array('id' => $supplier_groups_array[$i]['id'],
														'text' => $supplier_groups_array[$i]['text']
														);
				} else {
					$supplier_groups_from_array[] = array('id' => $supplier_groups_array[$i]['id'],
														'text' => $supplier_groups_array[$i]['text']
														);
				}
			}
		} else {
			if (is_array($supplier_groups_array) && count($supplier_groups_array)) {
				$supplier_groups_to_array = array_merge($supplier_groups_to_array, $supplier_groups_array);
			}
		}
		
		$status_options = array();
		$order_status_select_sql = "SELECT supplier_list_status_id, supplier_list_status_name FROM " . TABLE_SUPPLIER_LIST_STATUS . " WHERE language_id='" . (int)$languages_id  . "' ORDER BY supplier_list_status_sort_order";
		$order_status_result_sql = tep_db_query($order_status_select_sql);
		while ($order_status_row = tep_db_fetch_array($order_status_result_sql)) {
			$status_options[$order_status_row["supplier_list_status_id"]] = $order_status_row["supplier_list_status_name"];
		}
		
  		$categories_array = tep_get_eligible_category_tree(FILENAME_SUPPLIERS_REPORT, 0, '___', '', $categories_array);
  		
  		$report_type_array = array	( 	array ('id' => '1', "text" => REPORT_TYPE_TOTAL),
  										array ('id' => '4', "text" => REPORT_TYPE_TOTAL_BY_SOURCE),
	  									array ('id' => '2', "text" => REPORT_TYPE_SUPPLIER_GROUPS),
	  									array ('id' => '3', "text" => REPORT_TYPE_SUPPLIERS)
  							  		);
?>
			  		<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td>
							<?=tep_draw_form('suppliers_report_criteria', FILENAME_SUPPLIERS_REPORT, tep_get_all_get_params(array('action')) . 'action=show_report', 'post', 'onSubmit="return form_checking();"');?>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
			        				<td>
			        					<table border="0" width="100%" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" width="15%"><?=ENTRY_REPORT_TYPE?></td>
								    			<td class="main">
								    				<table border="0" cellspacing="0" cellpadding="2">
								    					<tr>
								    						<td class="main"><?=tep_draw_pull_down_menu("report", $report_type_array, $_SESSION['sup_report_param']["report"], ' id="report" onChange="swap_fields(this.value)"')?></td>
								    						<td>&nbsp;</td>
								    						<td class="main" id="cat_level_head"></td>
								    						<td class="main" id="cat_level_field"></td>
								    						<?=tep_draw_hidden_field("show_cat_level", '', ' id="show_cat_level" ')?>
								    					</tr>
								    				</table>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tbody id="sup_grp_section" class="hide">
			          						<tr>
			            						<td class="main" valign="top"><?=ENTRY_SUPPLIER_GROUP?></td>
			            						<td class="main" align="left"><?=tep_draw_js_select_boxes('sup_grp', $supplier_groups_from_array, $supplier_groups_to_array, ' size="10" style="width:20em;"')?></td>
			          						</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						</tbody>
			          						<tbody id="buy_from_section" class="hide">
			          						<tr>
			            						<td class="main" valign="top"><?=ENTRY_BUYBACK_FROM?></td>
			            						<td class="main" align="left">
						    						<table border="0" cellspacing="2" cellpadding="0">
						    						<?
									    				if (count($buyback_from_array)) {
								    						echo '<tr><td class="main">'.tep_draw_checkbox_field('buyback_from_any', '1', isset($_SESSION['sup_report_param']) ? (count($_SESSION['sup_report_param']["buyback_from"]) ? false : true) : false, '', 'id="buyback_from_any" onClick="checkbox_select_any_is_clicked(\'suppliers_report_criteria\', this, \'buyback_from\');"') . '</td><td class="main" colspan="'.(count($buyback_from_array)*2-1).'">'.TEXT_ANY.'</td></tr>';
								    						echo '<tr>';
								    						
								    						foreach ($buyback_from_array as $id => $title) {
									    						echo '<td class="main">'.
									    								tep_draw_checkbox_field('buyback_from[]', $id, isset($_SESSION['sup_report_param']) ? (is_array($_SESSION['sup_report_param']["buyback_from"]) && in_array($id, $_SESSION['sup_report_param']["buyback_from"]) ? true : false) : false, '', 'onClick="group_checkboxes_is_clicked(\'suppliers_report_criteria\', \'buyback_from_any\', \'buyback_from\');"') . '
									    							  </td>
									    						   	  <td class="main">'.$title.'</td>';
									    					}
									    					echo '</tr>';
									    				}
									    			?>
									    			</table>
						    					</td>
			          						</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						</tbody>
			          						<tbody id="cat_section" class="show">
											<tr>
												<td class="main"><?=ENTRY_CATEGORY?></td>
								    			<td class="main">
								    				<?=tep_draw_pull_down_menu("cat_id", $categories_array, $_SESSION['sup_report_param']["cat_id"], ' id="cat_id" ')?>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						</tbody>
											<script language="javascript"><!--
			  									var date_start_date = new ctlSpiffyCalendarBox("date_start_date", "suppliers_report_criteria", "start_date", "btnDate_start_date", "", scBTNMODE_CUSTOMBLUE);
			  									calMgr.showHelpAlerts = true;
											//--></script>
											<tr>
			            						<td class="main"><?=ENTRY_START_DATE?></td>
			            						<td class="main" align="left"><script language="javascript">date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd"; document.getElementById('start_date').value='<?=$_SESSION['sup_report_param']["start_date"]?>';</script></td>
			          						</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<script language="javascript"><!--
			  									var date_end_date = new ctlSpiffyCalendarBox("date_end_date", "suppliers_report_criteria", "end_date", "btnDate_end_date", "", scBTNMODE_CUSTOMBLUE);
											//--></script>	<tr>
			            						<td class="main"><?=ENTRY_END_DATE?></td>
			            						<td class="main" align="left"><script language="javascript">date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd"; document.getElementById('end_date').value='<?=$_SESSION['sup_report_param']["end_date"]?>';</script></td>
			          						</tr>
			          						<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          						<tr>
												<td class="main"><?=ENTRY_ORDER_STATUS?></td>
								    			<td class="main">
								    			<?
								    				foreach ($status_options as $id => $title) {
								    					echo tep_draw_checkbox_field('order_status[]', $id, isset($_SESSION['sup_report_param']) ? (is_array($_SESSION['sup_report_param']["order_status"]) && in_array($id, $_SESSION['sup_report_param']["order_status"]) ? true : false) : ( $id=="1" || $id=="2" || $id=="3" ? true : false), '', 'id="order_status_'.$id.'"') . "&nbsp;" . $title . "&nbsp;";
								    				}
								    			?>
								    			</td>
											</tr>
											<tr>
			            						<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			          						</tr>
			          					</table>
									</td>
								</tr>
						        <tr>
				  					<td>
				  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
				  							<tr>
				  								<td width="20%">&nbsp;</td>
				  								<td align="right">
				  									<?=tep_image_submit('button_report.gif', IMAGE_REPORT)?>&nbsp;<a href="<?=tep_href_link(FILENAME_SUPPLIERS_REPORT, 'action=reset_session')?>"><?=tep_image_button('button_reset.gif', IMAGE_RESET)?></a>
				  								</td>
				  							</tr>
				  						</table>
				  					</td>
				  				</tr>
	  						</table>
	  						</form>
	  					</td>
	  				</tr>
					<script language="javascript"><!--
						function form_checking() {
							var start_date = document.getElementById('start_date').value;
							if(start_date.length > 0) {
			     				if (!validateDate(start_date)) {
			     					alert('Start date is not a valid date format as requested!');
									document.getElementById('start_date').focus();
									document.getElementById('start_date').select();
									return false;
			     				}
			   				}
			   				
			   				var end_date = document.getElementById('end_date').value;
							if(end_date.length > 0) {
			     				if (!validateDate(end_date)) {
			     					alert('End date is not a valid date format as requested!');
									document.getElementById('end_date').focus();
									document.getElementById('end_date').select();
									return false;
			     				}
			   				}
			   				
			   				if (start_date.length > 0 && end_date.length > 0) {
			   					if (!validStartAndEndDate(start_date, end_date)) {
			   						alert('Start Date is greater than End Date!');
									document.getElementById('start_date').focus();
									document.getElementById('start_date').select();
									return false;
			   					}
			   				}
							
							var selected_grp = document.suppliers_report_criteria.elements['sup_grp_to[]'];
							if (selected_grp != null) {
								for (x=0; x<(selected_grp.length); x++) {
			    					selected_grp.options[x].selected = true;
			  					}
			  					
			  					if (x < 1) {
			    					alert('Please select at least one supplier groups.');
			    					return false;
			  					}
		  					}
		  					
							return true;
			    		}
			    		
						function swap_fields(selected) {
							switch(selected) {
								case '2':
									document.getElementById('sup_grp_section').className = 'show';
									document.getElementById('buy_from_section').className = 'hide';
									
									break;
								case '4':
									document.getElementById('sup_grp_section').className = 'hide';
									document.getElementById('buy_from_section').className = 'show';
									
									checkbox_select_any_is_clicked('suppliers_report_criteria', DOMCall('buyback_from_any'), 'buyback_from');
									
									break;
								default:
									document.getElementById('sup_grp_section').className = 'hide';
									document.getElementById('buy_from_section').className = 'hide';
									
									break;
							}
						}
						
						function init() {
							swap_fields(document.getElementById('report').value);
						}
						init();
						//-->
					</script>
<?	} ?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>