<?php
/*
  	$Id: suppliers_restock_characters.php,v 1.2 2007/01/31 09:35:08 weichen Exp $

  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Venture

  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$subaction = (isset($_GET['subaction']) ? $_GET['subaction'] : '');

switch ($action) {
	case "edit_rstk":

	break;


}

switch($subaction) {
	case "update_rstk_list":
		$restock_character_delete_sql = "	DELETE FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES ."
									 		WHERE categories_id ='". $_REQUEST['category_id'] ."'";
		tep_db_query($restock_character_delete_sql);
		
		$restock_character_sets_data_array = array();
		if (count($_REQUEST["restock_character_sets_id_to"])) {
			foreach($_REQUEST["restock_character_sets_id_to"] AS $restock_character_sets_id) {
				$restock_character_sets_data_array = array('categories_id' => $_REQUEST['category_id'],
														   'restock_character_sets_id' => $restock_character_sets_id);
				tep_db_perform(TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES, $restock_character_sets_data_array);
			}
		}
		
		$messageStack->add_session(SUCCESS_CHARACTER_SET_NO_STRING_UPDATED, 'success');
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_RESTOCK_CHARACTERS, tep_get_all_get_params(array('action', 'subaction'))));
		
		break;
	case "confirm_delete_rstk_set":
		$restock_character_delete_sql = "	DELETE FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES ."
											WHERE categories_id='". $_REQUEST['catID'] ."'
												AND restock_character_sets_id='". $_REQUEST['rcsID'] ."'";
		tep_db_query($restock_character_delete_sql);
		
		$messageStack->add_session(sprintf(SUCCESS_CHARACTER_SET_REMOVED, $_REQUEST['rstk'], $_REQUEST['category']), 'success');
		tep_redirect(tep_href_link(FILENAME_SUPPLIERS_RESTOCK_CHARACTERS, tep_get_all_get_params(array('action', 'subaction'))));
		
		break;
}



$game_cat_id_arr = tep_get_game_list_arr();
unset($game_cat_id_arr[0]);

?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script type="text/javascript" src="includes/general.js"></script>
	<script type="text/javascript" src="includes/javascript/select_box.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
            			<td>
            				<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="main" align="right">
            						<?
            							if (tep_not_null($action)) {
            								if ($action == "add_rstk" || $action == "edit_acc_set") {
            									echo tep_button(IMAGE_BUTTON_BACK, IMAGE_BUTTON_BACK, tep_href_link(FILENAME_SUPPLIERS_RESTOCK_CHARACTERS, 'action=manage_rstk'), '', 'inputButton');
            								} else {
	            								echo tep_button(IMAGE_BUTTON_BACK, IMAGE_BUTTON_BACK, tep_href_link(FILENAME_SUPPLIERS_RESTOCK_CHARACTERS), '', 'inputButton');
	            							}
            							}
            						?>
            						</td>
          						</tr>
          					</table>
            			</td>
          			</tr>
					<tr>
						<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
					</tr>
<?
switch ($action) {
	case 'edit_rstk':
		$restock_character_sets_list_from_array = array( array ('id' => '', "text" => 'Select Restock Character Lists', "type" => 'optgroup') );
		$restock_character_sets_list_to_array = array( array ('id' => '', "text" => 'Selected Restock Character Lists', "type" => 'optgroup') );

		$categories_select_sql = "SELECT categories_heading_title, categories_id FROM ". TABLE_CATEGORIES_DESCRIPTION ." WHERE categories_id='". (int)$_REQUEST["catID"] ."'";
		$categories_result_sql = tep_db_query($categories_select_sql);
		$categories_row = tep_db_fetch_array($categories_result_sql);

		$rstk_character_selected_select_sql = "	SELECT rcs2ctg.restock_character_sets_id, rcs.restock_character_sets_name
												FROM ". TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES ." AS rcs2ctg
												INNER JOIN ". TABLE_RESTOCK_CHARACTER_SETS ." AS rcs
												  	ON(rcs2ctg.restock_character_sets_id=rcs.restock_character_sets_id)
												WHERE rcs2ctg.categories_id='". (int)$_REQUEST["catID"] ."'
												ORDER BY rcs.restock_character_sets_name";
		$rstk_character_selected_result_sql = tep_db_query($rstk_character_selected_select_sql);

		while ($rstk_character_selected_row = tep_db_fetch_array($rstk_character_selected_result_sql)) {
			$restock_character_sets_list_to_array[] = array(	'id' => $rstk_character_selected_row['restock_character_sets_id'],
														     	'text' => $rstk_character_selected_row['restock_character_sets_name']
												    			);
		}

		tep_get_parent_categories($parent_categories_array, $categories_row['categories_id']); //check category that have same parent
		tep_get_subcategories($subcategories_array, $categories_row['categories_id']);
		
		$whole_path_categories_array = array_merge(array($categories_row['categories_id']), $parent_categories_array, $subcategories_array);
		
		$unassigned_rstk_character_select_sql = "	SELECT rcs.restock_character_sets_id, rcs.restock_character_sets_name
												  	FROM ". TABLE_RESTOCK_CHARACTER_SETS ." AS rcs
												  	LEFT JOIN " . TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES ." AS rcs2ctg
														ON (rcs.restock_character_sets_id=rcs2ctg.restock_character_sets_id AND rcs2ctg.categories_id='".(int)$_REQUEST["catID"]."')
													WHERE rcs.restock_character_sets_cat_id IN ('" . implode("', '", $whole_path_categories_array) ."')
														AND rcs2ctg.restock_character_sets_id IS NULL
													ORDER BY rcs.restock_character_sets_name";
		
		$unassigned_rstk_character_result_sql = tep_db_query($unassigned_rstk_character_select_sql);

		while ($unassigned_rstk_character_row = tep_db_fetch_array($unassigned_rstk_character_result_sql)) {
			$restock_character_sets_list_from_array[] = array(	'id' => $unassigned_rstk_character_row['restock_character_sets_id'],
														     	'text' => $unassigned_rstk_character_row['restock_character_sets_name']
												    			);
		}
?>
					<tr>
						<td>
<?
		echo tep_draw_form('set_restock_character_form', FILENAME_SUPPLIERS_RESTOCK_CHARACTERS, tep_get_all_get_params(array('subaction')) . 'subaction=update_rstk_list', 'post', 'enctype="multipart/form-data" onSubmit="return form_checking();"');
		echo tep_draw_hidden_field("category_id", (int)$_REQUEST["catID"]);
?>
							<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
								<tr>
            						<td class="main" width="15%"><?=TABLE_HEADING_MAIN_CAT?></td>
            						<td class="main"><?=$categories_row['categories_heading_title']?></td>
        						</tr>
        						<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
								</tr>
        						<tr>
            						<td class="main" valign="top"><?=ENTRY_RESTOCK_CHARACTERS?></td>
            						<td class="main"><?=tep_draw_js_select_boxes('restock_character_sets_id', $restock_character_sets_list_from_array, $restock_character_sets_list_to_array, ' size="10" style="width:20em;"');?></td>
        						</tr>
        						<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '5')?></td>
								</tr>
        						<tr>
	        						<td colspan="2" align="right">
	        							<input type="submit" name="btn_db_update" value="<?=IMAGE_BUTTON_UPDATE?>" title="<?=IMAGE_BUTTON_UPDATE?>" class="inputButton"">
	        						</td>
        						</tr>
							</table>
						</form>
						</td>
					</tr>
					<script language="javascript"><!--
						function form_checking() {
							var selected_items = document.set_restock_character_form.elements['restock_character_sets_id_to[]'];
							if (selected_items != null) {
								for (x=0; x<(selected_items.length); x++) {
			    					selected_items.options[x].selected = true;
			  					}
		  					}
						}
						//-->
					</script>
<?
		break; //-------------------------------------------------------------------------------------------------
	default:
?>


					<tr>
            			<td valign="top">
            				<table border="0" width="100%" align="center" cellspacing="1" cellpadding="2">
			   					<tr>
								    <td align="left" class="reportBoxHeading"><?=TABLE_HEADING_MAIN_CAT?></td>
<?
			echo '				    <td width="20%" align="center" class="reportBoxHeading">'.TABLE_HEADING_RESTOCK_CHARACTERS;
			echo ' 						<br>[<a href="'.tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=manage_rstk').'" class="highlightLink">'.LINK_MANAGE.'</a>]</td>';
?>
								</tr>
<?
		$row_count = 0;
		foreach ($game_cat_id_arr as $cat_arr) {
			$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top"><?=$cat_arr['text']?></td>
									<td class="reportRecords" valign="top">
										<table border="0" width="100%" align="center" cellspacing="0" cellpadding="2">
											<tr>
												<td class="reportRecords" valign="top"><a href="<?=tep_href_link(FILENAME_SUPPLIERS_RESTOCK_CHARACTERS, 'action=edit_rstk&catID='.$cat_arr['id'])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Add restock characters", "", "", 'align="top"')?></a>&nbsp;</td>
												<td width="100%" class="reportRecords" valign="top">
<?
	    	$list_character_set_select_sql = "SELECT rcs.restock_character_sets_id, rcs.restock_character_sets_name
	    									  FROM " . TABLE_RESTOCK_CHARACTER_SETS . " AS rcs
	    									  INNER JOIN ". TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES ." AS rcs2ctg
	    									  		ON (rcs.restock_character_sets_id=rcs2ctg.restock_character_sets_id)
	    									  WHERE rcs2ctg.categories_id = '" . tep_db_input($cat_arr['id']) . "'
	    									  ORDER BY rcs.restock_character_sets_name";
	    	$list_character_set_result_sql = tep_db_query($list_character_set_select_sql);
			
			while ($list_character_set_row = tep_db_fetch_array($list_character_set_result_sql)) {
				$safe_set_name = htmlspecialchars(addslashes($list_character_set_row["restock_character_sets_name"]), ENT_QUOTES);
				
				echo '<a href="'.tep_href_link(FILENAME_PRODUCTS_PURCHASE_QUANTITY, 'action=edit_acc_set&rcsID='.$list_character_set_row["restock_character_sets_id"]).'" class="actionLink">'. $list_character_set_row["restock_character_sets_name"] .'</a>'
						.'&nbsp;[<a href="javascript:void(confirm_delete(\''.$safe_set_name.'\', \''.LINK_UNASSIGNED_RESTOCK_CHARACTERS.'\', \''.tep_href_link(FILENAME_SUPPLIERS_RESTOCK_CHARACTERS, 'subaction=confirm_delete_rstk_set&rcsID='.$list_character_set_row["restock_character_sets_id"].'&rstk='.$list_character_set_row["restock_character_sets_name"].'&category='.$cat_arr['text'].'&catID='.$cat_arr['id']).'\'))">unassigned</a>]<br>';
			}
?>
												</td>
											</tr>
										</table>
									</td>
								</tr>
<?			$row_count++;
		}
?>
							</table>
						</td>
					</tr>
<?
}
?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>