<?php
include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');
include_once(DIR_WS_CLASSES . 'direct_topup.php');
include_once(DIR_WS_CLASSES . 'publishers_update_server.php');
include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'memcache.php');

//Global Memcached Object
$memcache_obj = new OGM_Cache_MemCache();
if (empty($_SERVER['HTTP_X_REQUESTED_WITH']) || !(strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest')) {
    exit;
}
$pid = (int)$_GET['pid'];
if (empty($pid)) {
    exit;
} else {
    tep_db_connect() or die('Unable to connect to database server!');

    set_time_limit(0);
    ignore_user_abort(true);
    $sync_obj = new publishers_update_server();
    $sync_obj->get_server($pid);

}


?>
