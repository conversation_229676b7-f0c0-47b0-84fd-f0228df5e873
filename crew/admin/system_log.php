<?
/*
  	$Id: system_log.php,v 1.4 2010/03/17 02:36:18 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2008 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'log.php');

$system_log_object = new log_files($login_id);

define('CUSTOM_MAX_DISPLAY_LOG_RESULTS', 100);

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

if ($_REQUEST['action'] == 'show_log') {
	if ($_REQUEST['cont'] && !isset($_SESSION['ori_system_log_select_sql'])) {
		tep_redirect(tep_href_link(FILENAME_SYSTEM_LOG));
	}
}

if (tep_not_null($action)) {
	switch ($action) {
        case 'reset_session':
        	unset($_SESSION['system_log_param']);
        	tep_redirect(tep_href_link(FILENAME_SYSTEM_LOG));
        	break;
	}
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<META Http-Equiv="Cache-Control" Content="no-cache">
	<META Http-Equiv="Pragma" Content="no-cache">
	<META Http-Equiv="Expires" Content="0"> 
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF" onload="SetFocus();">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
			  		<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td class="pageHeading" valign="top"><?=HEADING_INPUT_TITLE?></td>
									<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
								</tr>
							</table>
						</td>
					</tr>
<?
  	if ($_REQUEST['action'] == 'show_log') {
  		if (!$_REQUEST['cont']) {
  			unset($_SESSION['ori_system_log_select_sql']);
  			
	  		$system_log_query_str = "select log.*, aa.admin_id, aa.admin_email_address from " . TABLE_SYSTEM_LOG . " as log left join " . TABLE_ADMIN . " as aa on log.system_log_admin_id=aa.admin_id ";
	  		$where_str = " where 1";
	  		
	  		if ($_REQUEST["action_performed_on"]) {
	  			if (tep_not_null($_REQUEST["action_performed_on"])) {
	  				switch ($_REQUEST["search_by"]) {
	  					case "admin_email_address":
			  				$system_log_id_query_str = "select system_log_id from " . TABLE_SYSTEM_LOG . " 
			  											where table_name = 'admin' ";
			  				
			  				$admin_id_query_str = "	select admin_id from " . TABLE_ADMIN . "
			  										where 1 ";
			  										
			  				if ($_REQUEST["match"]=='exact_match') {
			  					$system_log_id_query_str .= " and (entry_id LIKE '" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "<br>(Deleted:%' ";
				  				$admin_id_query_str .= " and admin_email_address = '" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "'";
				  			} else if ($_REQUEST["match"]=='partial_match') {
			  					$system_log_id_query_str .= " and (entry_id LIKE '%" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "%' ";
				  				$admin_id_query_str .= " and admin_email_address LIKE '%" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "%'";
				  			}
			  				$admin_id_result_sql = tep_db_query($admin_id_query_str);
			  				$admin_id_array = array();
		  					while ($admin_id_row = tep_db_fetch_array($admin_id_result_sql)){
		  						$admin_id_array[] = $admin_id_row['admin_id'];
		  					}
		  					
		  					if (count($admin_id_array)) {
		  						$system_log_id_query_str .= " or entry_id IN(" . implode(",", $admin_id_array) . ") ";	// all existing admin id with email address is/like action_performed_on
			  				}
			  				
			  				$system_log_id_query_str .= ")";
							
			  				$system_log_id_result_sql = tep_db_query($system_log_id_query_str);
			  				$system_log_id_array = array();
		  					while ($system_log_id_row = tep_db_fetch_array($system_log_id_result_sql)){
		  						$system_log_id_array[] = $system_log_id_row['system_log_id'];
		  					}
		  					
		  					if (count($system_log_id_array)) {
		  						$where_str .= " and system_log_id IN(" . implode(",", $system_log_id_array) . ") ";
			  				} else {
			  					$where_str .= " and 0 ";
			  				}
	  						break;
	  					case "admin_groups_name":
			  				$system_log_id_query_str = "select system_log_id from " . TABLE_SYSTEM_LOG . " 
			  											where table_name = 'admin_groups' ";
			  											
			  				$admin_group_id_query_str = "	select admin_groups_id from " . TABLE_ADMIN_GROUPS . "
			  												where 1 ";
			  										
			  				if ($_REQUEST["match"]=='exact_match') {
				  				$system_log_id_query_str .= " and (entry_id LIKE '" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "<br>(Deleted:%' ";
				  				$admin_group_id_query_str .= " and admin_groups_name = '" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "'";
				  			} else if ($_REQUEST["match"]=='partial_match'){
				  				$system_log_id_query_str .= " and (entry_id LIKE '%" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "%' ";
				  				$admin_group_id_query_str .= " and admin_groups_name LIKE '%" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "%'";
				  			}
				  			
			  				$admin_group_id_result_sql = tep_db_query($admin_group_id_query_str);
			  				$admin_group_id_array = array();
			  				while ($admin_group_id_row = tep_db_fetch_array($admin_group_id_result_sql)){
			  					$admin_group_id_array[] = $admin_group_id_row['admin_groups_id'];
			  				}
			  				
			  				if (count($admin_group_id_array)) {
				  				$system_log_id_query_str .= " or entry_id IN(" . implode(",", $admin_group_id_array) . ") ";	//all admin group id with name is/like action_performed_on
				  			}
				  			
				  			$system_log_id_query_str .= ")";
							
			  				$system_log_id_result_sql = tep_db_query($system_log_id_query_str);
			  				$system_log_id_array = array();
		  					while ($system_log_id_row = tep_db_fetch_array($system_log_id_result_sql)){
		  						$system_log_id_array[] = $system_log_id_row['system_log_id'];
		  					}
		  					
			  				if (count($system_log_id_array)) {
		  						$where_str .= " and system_log_id IN(" . implode(",", $system_log_id_array) . ") ";
			  				} else {
			  					$where_str .= " and 0 ";
			  				}
	  						break;
	  					case "customers_groups_name":
			  				$system_log_id_query_str = "select system_log_id from " . TABLE_SYSTEM_LOG . " 
			  											where table_name = 'customers_groups' ";
			  											
			  				$customer_group_id_query_str = "select customers_groups_id from " . TABLE_CUSTOMERS_GROUPS . "
			  												where 1 ";
			  				
			  				if ($_REQUEST["match"]=='exact_match') {
				  				$system_log_id_query_str .= " and (entry_id LIKE '" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "<br>(Deleted:%' ";
				  				$customer_group_id_query_str .= " and customers_groups_name = '" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "'";
				  			} else if ($_REQUEST["match"]=='partial_match'){
				  				$system_log_id_query_str .= " and (entry_id LIKE '%" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "%' ";
				  				$customer_group_id_query_str .= " and customers_groups_name LIKE '%" . tep_db_prepare_input($_REQUEST["action_performed_on"]) . "%'";
				  			}
				  			
			  				$customer_group_id_result_sql = tep_db_query($customer_group_id_query_str);
			  				$customer_group_id_array = array();
			  				while ($customer_group_id_row = tep_db_fetch_array($customer_group_id_result_sql)){
			  					$customer_group_id_array[] = $customer_group_id_row['customers_groups_id'];
			  				}
			  				
			  				if (count($customer_group_id_array)) {
				  				$system_log_id_query_str .= " or entry_id IN(" . implode(",", $customer_group_id_array) . ") ";	//all admin group id with name is/like action_performed_on
				  			}
				  			
				  			$system_log_id_query_str .= ")";
							
			  				$system_log_id_result_sql = tep_db_query($system_log_id_query_str);
			  				$system_log_id_array = array();
		  					while ($system_log_id_row = tep_db_fetch_array($system_log_id_result_sql)){
		  						$system_log_id_array[] = $system_log_id_row['system_log_id'];
		  					}
		  					
			  				if (count($system_log_id_array)) {
		  						$where_str .= " and system_log_id IN(" . implode(",", $system_log_id_array) . ") ";
			  				} else {
			  					$where_str .= " and 0 ";
			  				}
	  						break;
	  				}
			  	}
	  		}
	  		
	  		if (isset($_REQUEST["modified_by"]) && ($_REQUEST["modified_by"]!='0')) {
	  			$where_str .= " and system_log_admin_id='" . $_REQUEST["modified_by"] . "' ";
	  		}
	  		
	  		if ($_REQUEST["action_performed"]) {
	  			if($_REQUEST["action_performed"] != 'all') {
		  			$where_str .= " and system_log_action='" . $_REQUEST["action_performed"] . "' ";
		  		}
	  		}
	  		
	  		if ($_REQUEST["start_date"]) {
	  			if (strpos($_REQUEST["start_date"], ':') !== false) {
	  				$startDateObj = explode(' ', trim($_REQUEST["start_date"]));
	  				list($yr, $mth, $day) = explode('-', $startDateObj[0]);
	  				list($hr, $min) = explode(':', $startDateObj[1]);
	  				$where_str .= " and DATE_FORMAT(system_log_time, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT('".date("Y-m-d H:i:s",mktime((int)$hr, (int)$min, 0,$mth,$day,$yr))."', '%Y-%m-%d %H:%i:%s') ";
	  			} else {
	  				list($yr, $mth, $day) = explode('-', trim($_REQUEST["start_date"]));
	  				$where_str .= " and DATE_FORMAT(system_log_time, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d",mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') ";
	  			}
	  		}
	  		
	  		if ($_REQUEST["end_date"]) {
	  			if (strpos($_REQUEST["end_date"], ':') !== false) {
	  				$endDateObj = explode(' ', trim($_REQUEST["end_date"]));
	  				list($yr, $mth, $day) = explode('-', $endDateObj[0]);
	  				list($hr, $min) = explode(':', $endDateObj[1]);
	  				$where_str .= " and DATE_FORMAT(system_log_time, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT('".date("Y-m-d H:i:s",mktime((int)$hr, (int)$min, 0,$mth,$day,$yr))."', '%Y-%m-%d %H:%i:%s') ";
	  			} else {
	  				list($yr, $mth, $day) = explode('-', $_REQUEST["end_date"]);
	  				$where_str .= " and DATE_FORMAT(system_log_time, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d",mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') ";
	  			}
	  		}
	  		
	  		if ($_REQUEST["sort_order"]) {
	  			$sort_str = " order by system_log_time " . $_REQUEST["sort_order"];
	  		}
	  		
	  		$system_log_select_sql = $system_log_query_str . $where_str . $sort_str;
	  		$_SESSION['ori_system_log_select_sql'] = $system_log_select_sql;
	  		
	  		$_SESSION['system_log_param']['action_performed_on'] = $_REQUEST["action_performed_on"];
	  		$_SESSION['system_log_param']['search_by'] = $_REQUEST["search_by"];
	  		$_SESSION['system_log_param']['match'] = $_REQUEST["match"];
	  		$_SESSION['system_log_param']['modified_by'] = $_REQUEST["modified_by"];
	  		$_SESSION['system_log_param']['action_performed'] = $_REQUEST["action_performed"];
	  		$_SESSION['system_log_param']['start_date'] = trim($_REQUEST["start_date"]);
	  		$_SESSION['system_log_param']['end_date'] = trim($_REQUEST["end_date"]);
	  		$_SESSION['system_log_param']['sort_order'] = $_REQUEST["sort_order"];
	  	} else {
	  		$system_log_select_sql = $_SESSION['ori_system_log_select_sql'];
	  	}
	}

	echo tep_draw_form('log_input', FILENAME_SYSTEM_LOG, tep_get_all_get_params(array('action')) . 'action=show_log', 'post', 'onSubmit="return form_checking();"');
	$admin_users = array( array ('id' => 0, "text" => "All Admin Users"));
	$admin_users_select_sql = "	SELECT  admin_id, admin_firstname, admin_lastname, admin_email_address 
								FROM " . TABLE_ADMIN . "
								ORDER BY admin_lastname";
	$admin_users_result_sql = tep_db_query($admin_users_select_sql);
	while ($admin_users_row = tep_db_fetch_array($admin_users_result_sql)) {
		$admin_users[] = array	(	"id" => $admin_users_row["admin_id"],
									"text" => $admin_users_row["admin_lastname"] . ', ' . $admin_users_row["admin_firstname"] . ' (' . $admin_users_row["admin_email_address"] . ')'
								);
	}
	
	$action_option = array ( 	array("id" => "all", "text" => "All", "params" => 'id="all"'),
								array("id" => "New Admin Member", "value" => "New Admin Member", "text" => "New Admin Member", "params" => 'id="new_admin_member"'),
								array("id" => "Edit Admin Member", "value" => "Edit Admin Member", "text" => "Edit Admin Member", "params" => 'id="edit_admin_member"'),
								array("id" => "Edit Admin Member Group", "value" => "Edit Admin Member Group", "text" => "Edit Admin Member Group", "params" => 'id="edit_admin_member_group"'),
								array("id" => "Delete Admin Member", "value" => "Delete Admin Member", "text" => "Delete Admin Member", "params" => 'id="delete_admin_member"'),
								array("id" => "New Admin Group", "value" => "New Admin Group", "text" => "New Admin Group", "params" => 'id="new_admin_group"'),
								array("id" => "Edit Admin Group", "value" => "Edit Admin Group", "text" => "Edit Admin Group", "params" => 'id="edit_admin_group"'),
								array("id" => "Delete Admin Group", "value" => "Delete Admin Group", "text" => "Delete Admin Group", "params" => 'id="delete_admin_group"'),
								array("id" => "Edit Group Permission", "value" => "Edit Group Permission", "text" => "Edit Group Permission", "params" => 'id="edit_group_permission"'),
							);
	
	$sort_option = array ( 	array("name" => "sort_order", "value" => "ASC", "text" => "Ascending", "checked" => isset($_SESSION['system_log_param']) ? ($_SESSION['system_log_param']["sort_order"]=="ASC" ? true : false) : false, "params" => ''),
							array("name" => "sort_order", "value" => "DESC", "text" => "Descending", "checked" => isset($_SESSION['system_log_param']) ? ($_SESSION['system_log_param']["sort_order"]=="DESC" ? true : false) : true, "params" => '')
						);
	
	$input_array = array ( 	"action_performed_on" => array ("title" => "Action Performed On", "type" => "text", "default_value" => $_SESSION['system_log_param']['action_performed_on'], "params" => 'size="32" id="action_performed_on"', "extra_info" => "&nbsp;&nbsp;" . tep_draw_pull_down_menu('search_by', array(array("id"=>"admin_email_address","text"=>"Admin Email"), array("id"=>"admin_groups_name","text"=>"Admin Group Name"), array("id"=>"customers_groups_name","text"=>"Customer Group Name")), $_SESSION['system_log_param']['search_by'], '') . "&nbsp;&nbsp;" . tep_draw_pull_down_menu('match', array(array("id"=>"exact_match","text"=>"Exact Match"), array("id"=>"partial_match","text"=>"Partial Match")), $_SESSION['system_log_param']['match'], ''), "required" => 0),
							"modified_by" => array("title" => "Modified By", "type" => "select", "source" => $admin_users, "default_value" => $_SESSION['system_log_param']['modified_by']),
							"action_performed" => array("title" => "Action", "type" => "select", "source" => $action_option, "default_value" => $_SESSION['system_log_param']['action_performed']),
							"date_range" => array ("title" => array("date_range"=>"Date Range", "start_date"=>"Start Date","end_date"=>"End Date"), "type" => "date_range", "format" => "yyyy-MM-dd / yyyy-MM-dd HH:MM", "required" => 0, "default_value" => array("start_date"=>$_SESSION['system_log_param']['start_date'],"end_date"=>$_SESSION['system_log_param']['end_date']), "calendar" => "PopCal"),
							"sort_by" => array("title" => "Sort By Date", "type" => "radio", "format" => "horizontal", "source" => $sort_option)
							);
	$system_log_object->draw_inputs("log_input", $input_array);
	
  	echo '			<tr>
	  					<td>
	  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
	  							<tr>
	  								<td width="30%">&nbsp;</td>
	  								<td align="right">' .
	  									tep_submit_button(TEXT_INFO_LOG_REPORT, TEXT_INFO_LOG_REPORT, '', 'inputButton') . ' ' . tep_button(TEXT_INFO_LOG_RESET, TEXT_INFO_LOG_RESET, FILENAME_SYSTEM_LOG . '?action=reset_session', '', 'inputButton') . '
	  								</td>
	  							</tr>
	  						</table>
	  					</td>
	  				</tr>';
?>
					<tr>
						<td><div><iframe width=188 height=166 name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
					</tr>
					
					<script language="javascript"><!--
						function form_checking() {
							if (document.getElementById('action_performed_on').value.length == 1) {
		     					alert('Action Performed On cannot be only 1 character!');
								document.getElementById('action_performed_on').focus();
								document.getElementById('action_performed_on').select();
								return false;
							}
							
							var start_date = document.getElementById('start_date').value;
							if(start_date.length > 0){
			     				if (!validateDate(start_date)) {
			     					alert('Start date is not a valid date format as requested!');
									document.getElementById('start_date').focus();
									document.getElementById('start_date').select();
									return false;
			     				}
			   				}
			   				
			   				var end_date = document.getElementById('end_date').value;
							if(end_date.length > 0){
			     				if (!validateDate(end_date)) {
			     					alert('End date is not a valid date format as requested!');
									document.getElementById('end_date').focus();
									document.getElementById('end_date').select();
									return false;
			     				}
			   				}
			   				
			   				if (start_date.length > 0 && end_date.length > 0) {
			   					if (!validStartAndEndDate(start_date, end_date)) {
			   						alert('Start Date is greater than End Date!');
									document.getElementById('start_date').focus();
									document.getElementById('start_date').select();
									return false;
			   					}
			   				}
			   				
			   				if (start_date.length == 0 && end_date.length == 0 && document.getElementById('action_performed_on').value.length == 0){
		     					alert('You must fill up at least 1 of the following field:\n- Action Performed On\n- Start Date\n- End Date');
								document.getElementById('action_performed_on').focus();
								document.getElementById('action_performed_on').select();
								return false;
			   				}
			   				
							return true;
			    		}
						//-->
					</script>
        			</tr>
<?	if ($_REQUEST['action'] == 'show_log') { ?>
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="1" cellpadding="1">
               								<tr>
               									<td colspan="8">&nbsp;</td>
               								</tr>
               								<tr>
			       								<td class="reportBoxHeading" width="8%"><?=TABLE_HEADING_TIME?></td>
			       								<td class="reportBoxHeading"><?=TABLE_HEADING_ACTION?></td>
			       								<td class="reportBoxHeading"><?=TABLE_HEADING_ACTION_PERFORMED_ON?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_FIELD_NAME?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_FROM_VALUE?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_TO_VALUE?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_BY_ADMIN?></td>
								                <td class="reportBoxHeading"><?=TABLE_HEADING_BY_IP?></td>
			   								</tr>
<?
		$system_log_split_object = new splitPageResults($_REQUEST["page"], CUSTOM_MAX_DISPLAY_LOG_RESULTS, $system_log_select_sql, $log_sql_numrows);
		$system_log_split_object->show_all = false;
    	$system_log_query = tep_db_query($system_log_select_sql);
    	
		$row_count = 0;
		
		$field_name_array = array(	"admin_email_address"=>"Admin Email Address", 
									"admin_firstname"=>"Admin First Name",
									"admin_lastname"=>"Admin Last Name",
									"admin_groups_name"=>"Admin Group Name",
									"admin_groups_id"=>"Admin Group ID",
									"admin_groups_authorized"=>"Admin Group Authorized",
									"customers_groups_name"=>"Customers Group Name");
									
    	while ($system_log_row = tep_db_fetch_array($system_log_query)) {
    		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
    		
    		if (is_numeric($system_log_row["system_log_admin_id"])) {
    			$admin_query = tep_db_query("SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $system_log_row["system_log_admin_id"] . "'");
    			if ($admin_info = tep_db_fetch_array($admin_query))
    				$admin_email_address = $admin_info["admin_email_address"];
    			else
    				$admin_email_address = $system_log_row["system_log_admin_id"];
    		} else {
    			$admin_email_address = $system_log_row["system_log_admin_id"];
    		}

    		if (is_numeric($system_log_row["entry_id"])) {
    			$field_name = '';
    			switch ($system_log_row["table_name"]) {
    				case "admin":
    					$field_name = 'admin_email_address';
    					break;
    				case "admin_groups":
    					$field_name = 'admin_groups_name';
    					break;
    				case TABLE_CUSTOMERS_GROUPS:
    					$field_name = 'customers_groups_name';
    					break;
    			}
    			$entry_name = $system_log_row["entry_id"];
    			if (tep_not_null($field_name)) {
	    			$entry_query = tep_db_query("SELECT " . $field_name . " FROM " . $system_log_row["table_name"] . " WHERE " . $system_log_row["table_name"] . "_id" . " = '" . $system_log_row["entry_id"] . "'");
	    			if ($admin_info = tep_db_fetch_array($entry_query)) {
	    				$entry_name = $admin_info[$field_name];
	    			}
    			}
    		} else {
    			$entry_name = $system_log_row["entry_id"];
    		}
    		
    		$field_name = isset($field_name_array[$system_log_row["field_name"]]) ? $field_name_array[$system_log_row["field_name"]] : $system_log_row["field_name"];
?>
											<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
												<td class="reportRecords" valign="top"><?=$system_log_row["system_log_time"]?></td>
												<td class="reportRecords" valign="top"><?=$system_log_row["system_log_action"]?></td>
												<td class="reportRecords" valign="top"><?=$entry_name?></td>
												<td class="reportRecords" valign="top"><?=$field_name?></td>
												<td class="reportRecords" valign="top"><?=$system_log_row["from_value"]?></td>
								                <td class="reportRecords" valign="top"><?=$system_log_row["to_value"]?></td>
								                <td class="reportRecords" valign="top"><?=$admin_email_address?></td>
								                <td class="reportRecords" valign="top"><?=$system_log_row["system_log_ip"]?></td>
											</tr>
<?
			$row_count++;
    	}
?>
			   							</table>
			   						</td>
			   					</tr>
			   					<tr>
			   						<td>
			   							<table border="0" width="100%" cellspacing="1" cellpadding="2">
			   								<tr>
												<td class="smallText" valign="top"><?=$system_log_split_object->display_count($log_sql_numrows, CUSTOM_MAX_DISPLAY_LOG_RESULTS, $_REQUEST['page'], TEXT_DISPLAY_NUMBER_OF_LOGS)?></td>
												<td class="smallText" align="right"><?=$system_log_split_object->display_links($log_sql_numrows, CUSTOM_MAX_DISPLAY_LOG_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_REQUEST['page'], tep_get_all_get_params(array('page', 'info', 'x', 'y', 'cID', 'cont'))."cont=1")?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
			   				</table>
			   			</td>
			   		</tr>
<?	} ?>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>