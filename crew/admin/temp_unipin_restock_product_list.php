<?php

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

require_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_WS_CLASSES . 'direct_topup.php');

tep_db_connect() or die('Unable to connect to database server!');
tep_set_time_limit(0);

function processRequest($url, $params)
{
    $curl_obj = new curl();
    $curl_obj->connect_via_proxy = true;
    if ($params) {
        $params = json_encode($params);
    }
    $response = $curl_obj->curl_request('POST', $url, ['Content-Type: application/json'], $params);
    return $response;
}

$publishers_sql = 'SELECT p.publishers_id, p.publishers_name FROM ' . TABLE_PUBLISHERS . ' p LEFT JOIN ' . TABLE_PUBLISHERS_CONFIGURATION . ' pc ON p.publishers_id = pc.publishers_id WHERE pc.publishers_configuration_key="TOP_UP_MODE" AND pc.publishers_configuration_value="unipin"';

$publishers_result = tep_db_query($publishers_sql);
$output = [];

while ($publishers_row = tep_db_fetch_array($publishers_result)) {
    $publishers = new direct_topup();
    $publishers->publishers($publishers_row['publishers_id']);

    $config = $publishers->get_publishers_conf();

    $time = time();
    $base_url = $config['TOP_UP_URL']['publishers_configuration_value'];
    $key = $config['OGM_MERCHANT_ID']['publishers_configuration_value'];
    $secret = $config['SECRET_KEY']['publishers_configuration_value'];
    $hash = hash('sha256', $key . $time . $secret);

    $response = processRequest($base_url . "/voucher/list", [
        'partner_guid' => $key,
        'logid' => $time,
        'signature' => $hash
    ]);

    $data = json_decode($response, 1);

    if ($data['status'] == 1) {
        foreach ($data['voucher_list'] as $voucher) {
            $response = processRequest($base_url . "/voucher/details", [
                'partner_guid' => $key,
                'logid' => $time,
                'voucher_code' => $voucher['voucher_code'],
                'signature' => $hash
            ]);
            $deno = json_decode($response, 1);
            if (empty($deno['voucher_name'])) {
                echo 'Error With : ' . $voucher['voucher_code'] . '<pre>' . print_r($deno) . '</pre>';
            } else {
                $output[$publishers_row['publishers_name']][$deno['voucher_name']] = $deno['denominations'];
            }
        }
    }
    else{
        echo 'Error :<pre>' . print_r($data) . '</pre>';
    }
}

?>
<html>

<body>
<?php
foreach ($output as $publisher_name => $publisher_data) {
    echo "<h3>$publisher_name</h3>";
    echo '<table><thead><td>Denomination Code</td><td>Denomination Name</td><td>Currency</td><td>Amount</td></thead><tbody>';
    foreach ($publisher_data as $game_name => $deno_data) {
        echo '<tr><td>' . $game_name . '</td></tr>';

        foreach ($deno_data as $deno) {
            echo '<tr>';
            foreach ($deno as $value) {
                echo "<td>$value</td>";
            }
            echo '</tr>';
        }
        echo '<tr><td>&nbsp;</td></tr>';

    }
    echo '</tbody></table>';
    echo "================================================";
    echo "<br><br><br>";
}
?>
</body>
</html>
