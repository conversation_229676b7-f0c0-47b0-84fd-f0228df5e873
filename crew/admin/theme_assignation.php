<?
/*
  	$Id: theme_assignation.php,v 1.4 2008/01/02 09:45:47 leechuan.goh Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require(DIR_WS_CLASSES . 'file_manager.php');
require(DIR_WS_CLASSES . 'theme_manager.php');

$file_obj = new file_manager();

if (!@is_dir(DIR_FS_THEME)) {
	$file_obj->touchDirectory(DIR_FS_THEME);
}

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

$languages = tep_get_languages();
$languages_array = array( array('id'=>'', 'text'=>'Select Language') );
for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
	$languages_array[] = array('id' => $languages[$i]['id'],
                               'text' => $languages[$i]['name']);
}

if (tep_not_null($action)) {
	if (isset($_REQUEST['catID'])) {
		// Ensure user has the right to access
		if (tep_check_cat_tree_permissions(FILENAME_THEME_ASSIGNATION, (int)$_REQUEST['catID']) != 1) {
			$messageStack->add_session(ERROR_CAT_ACCESS_DENIED, 'error');
			tep_redirect(tep_href_link(FILENAME_THEME_ASSIGNATION, 'cat_id='.(int)$_REQUEST['catID']));
		}
	}
	
	switch ($action) {
		case "confirm_assign_theme":
		case "confirm_update_assign":
			$assign_to_cat = (int)$HTTP_POST_VARS["catID"];
			$existing_theme_id = (int)$HTTP_POST_VARS["themeID"];
			$sel_theme_id = (int)$HTTP_POST_VARS["themes_id"];
			
			// Ensure no such pairing yet
			if ($action == "confirm_assign_theme" || $sel_theme_id != $existing_theme_id) {
				$theme_2_cat_checking_sql = "SELECT themes_id 
											 FROM " . TABLE_THEMES_TO_CATEGORIES . " 
											 WHERE categories_id = '" . $assign_to_cat . "' AND themes_id = '" . $sel_theme_id . "'";
				$theme_2_cat_result_sql = tep_db_query($theme_2_cat_checking_sql);
				if (tep_db_num_rows($theme_2_cat_result_sql)) {
					$messageStack->add_session("This theme had been assigned to this category!", 'error');
					tep_redirect(tep_href_link(FILENAME_THEME_ASSIGNATION, 'cat_id='.$assign_to_cat));
				}
			}
			
			$theme_2_cat_sql_data_array = array('start_date' => tep_db_prepare_input($HTTP_POST_VARS["start_date"]),
	    	           							'end_date' => tep_db_prepare_input($HTTP_POST_VARS["end_date"]),
	    	           							'themes_id' => $sel_theme_id
	        	           						);
			
            if ($action == "confirm_assign_theme") {
	    	    $theme_2_cat_insert_sql_data = array('categories_id' => $assign_to_cat);
            	$sql_data_array = array_merge($theme_2_cat_sql_data_array, $theme_2_cat_insert_sql_data);
				tep_db_perform(TABLE_THEMES_TO_CATEGORIES, $sql_data_array);
			} else {
				tep_db_perform(TABLE_THEMES_TO_CATEGORIES, $theme_2_cat_sql_data_array, 'update', ' themes_id="'.$existing_theme_id.'" and categories_id="'.$assign_to_cat.'"');
			}
			tep_redirect(tep_href_link(FILENAME_THEME_ASSIGNATION, 'cat_id='.$assign_to_cat));
			
			break;
		case "unassign_theme":
			$delete_cat_id = (int)$_REQUEST["catID"];
			$delete_theme_id = (int)$_REQUEST["themeID"];
			
			$unassign_theme_delete_sql = "DELETE FROM " . TABLE_THEMES_TO_CATEGORIES . " WHERE themes_id = '" . $delete_theme_id . "' AND categories_id = '" . $delete_cat_id . "'";
			tep_db_query($unassign_theme_delete_sql);
			
			tep_redirect(tep_href_link(FILENAME_THEME_ASSIGNATION, 'cat_id='.$delete_cat_id));
			
			break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<link rel="stylesheet" type="text/css" href="includes/javascript/spiffyCal/spiffyCal_v2_1.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/spiffyCal/spiffyCal_v2_1.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<div id="spiffycalendar" class="text"></div>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ($action == "add_theme_to_cat" || $action == "edit_assigned_theme") {
?>
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE_ASSIGN?></td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?
	echo tep_draw_form('theme_assign_form', FILENAME_THEME_ASSIGNATION, tep_get_all_get_params(array('action')) . ($action == "edit_assigned_theme" ? 'action=confirm_update_assign' : 'action=confirm_assign_theme'), 'post', 'onSubmit="return theme_assign_form_checking();"');
	$curCatID = (int)$_REQUEST["catID"];
	echo tep_draw_hidden_field("catID", $curCatID);
	
	if ($action == "edit_assigned_theme") {
		echo tep_draw_hidden_field("themeID", $curThemeID);
		$curThemeID = (int)$_REQUEST["themeID"];
		
		$assign_info_select_sql = "	SELECT start_date, end_date 
									FROM " . TABLE_THEMES_TO_CATEGORIES . " 
									WHERE categories_id = '" . $curCatID . "' AND themes_id = '" . $curThemeID . "'";
		$assign_info_result_sql = tep_db_query($assign_info_select_sql);
		$assign_info_row = tep_db_fetch_array($assign_info_result_sql);
	} else {
		$curThemeID = '';
	}
	
	$omit_theme_id_array = array();
	$assigned_theme_select_sql = "	SELECT themes_id 
									FROM " . TABLE_THEMES_TO_CATEGORIES . " 
									WHERE categories_id = '" . $curCatID . "'";
	$assigned_theme_result_sql = tep_db_query($assigned_theme_select_sql);
	while($assigned_theme_row = tep_db_fetch_array($assigned_theme_result_sql)) {
		if ($assigned_theme_row["themes_id"] != $curThemeID) {
			$omit_theme_id_array[] = $assigned_theme_row["themes_id"];
		}
	}
	
	$theme_array = array( array('id'=>'', 'text'=>'Select Theme') );
	$theme_select_sql = "SELECT themes_id, themes_title 
						 FROM " . TABLE_THEMES . " 
						 WHERE themes_id NOT IN ('" . implode("', '", $omit_theme_id_array) . "') 
						 ORDER BY themes_title";
	$theme_result_sql = tep_db_query($theme_select_sql);
	while($theme_row = tep_db_fetch_array($theme_result_sql)) {
		$theme_array[] = array('id'=>$theme_row["themes_id"], 'text'=>$theme_row["themes_title"]);
	}
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
        						<tr>
									<td class="main" width="15%" valign="top"><?=ENTRY_CATEGORY_PATH?></td>
									<td class="main" valign="top"><?=$curCatID == 0 ? 'Top' : tep_output_category_nav_path($curCatID)?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" width="15%" valign="top"><?=ENTRY_THEME?></td>
									<td class="main" valign="top"><?=tep_draw_pull_down_menu("themes_id", $theme_array, $curThemeID, ' id="themes_id" ')?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<script language="javascript"><!--
  									var date_start_date = new ctlSpiffyCalendarBox('date_start_date', 'theme_assign_form', 'start_date', 'btnDate_start_date', '<?=$assign_info_row["start_date"]?>', scBTNMODE_CUSTOMBLUE);
								//--></script>
								<tr>
            						<td class="main" width="10%">Start Date<br><small>(YYYY-MM-DD)</small></td>
            						<td class="main" align="laft"><script language="javascript">date_start_date.writeControl(); date_start_date.dateFormat="yyyy-MM-dd";</script></td>
          						</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<script language="javascript"><!--
  									var date_end_date = new ctlSpiffyCalendarBox('date_end_date', 'theme_assign_form', 'end_date', 'btnDate_end_date', '<?=$assign_info_row["end_date"]?>', scBTNMODE_CUSTOMBLUE);
								//--></script>
								<tr>
            						<td class="main" width="10%">End Date<br><small>(YYYY-MM-DD)</small></td>
            						<td class="main" align="laft"><script language="javascript">date_end_date.writeControl(); date_end_date.dateFormat="yyyy-MM-dd";</script></td>
          						</tr>
          						<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td colspan="2" align="right">
										<?=tep_image_submit('button_confirm.gif', IMAGE_CONFIRM, "") . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_THEME_ASSIGNATION, "cat_id=".$curCatID) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
	        		</form>
	        		<script>
	        		<!--
						function theme_assign_form_checking() {
							if (document.getElementById('themes_id').selectedIndex < 1) {
								alert('Please select the theme!');
								document.getElementById('themes_id').focus();
								return false;
							}
							
							var start_date = document.getElementById('start_date').value;
							if(start_date.length > 0){
			     				if (!validateDate(start_date)) {
			     					alert('Start date is not a valid date format as requested!');
									document.getElementById('start_date').focus();
									document.getElementById('start_date').select();
									return false;
			     				}
			   				}
			   				
			   				var end_date = document.getElementById('end_date').value;
							if(end_date.length > 0){
			     				if (!validateDate(end_date)) {
			     					alert('End date is not a valid date format as requested!');
									document.getElementById('end_date').focus();
									document.getElementById('end_date').select();
									return false;
			     				}
			   				}
			   				
			   				if (start_date.length > 0 && end_date.length > 0) {
			   					if (!validStartAndEndDate(start_date, end_date)) {
			   						alert('Start Date is greater than End Date!');
									document.getElementById('start_date').focus();
									document.getElementById('start_date').select();
									return false;
			   					}
			   				}
			   				
							return true;
						}
						//-->
					</script>
<?
} else {
	$p_rank = tep_check_cat_tree_permissions(FILENAME_THEME_ASSIGNATION, (int)$_REQUEST["cat_id"]);
	
	$categories_array = tep_get_eligible_category_tree(FILENAME_THEME_ASSIGNATION, 0, '___', '', $categories_array);
	
	$themes_2_cat_select_sql = "SELECT t2c.categories_id, t2c.start_date, t2c.end_date, t.themes_id, t.themes_title, t.themes_language_id
								FROM " . TABLE_THEMES_TO_CATEGORIES . " AS t2c
								INNER JOIN " . TABLE_THEMES . " AS t
									ON t2c.themes_id=t.themes_id 
								WHERE t2c.categories_id = " . (int)$_REQUEST["cat_id"] . "
								ORDER BY t.themes_title";
	$themes_2_cat_result_sql = tep_db_query($themes_2_cat_select_sql);
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					</tr>
            					<tr>
			        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
									<td class="main">
									<?
										echo tep_draw_form('cat_search_form', FILENAME_THEME_ASSIGNATION);
				    					echo tep_draw_pull_down_menu("cat_id", $categories_array, $_REQUEST["cat_id"], ' id="cat_id" onChange="this.form.submit();"');
				    					echo '</form>';
				    				?>
					    			</td>
					    		</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
            			<td valign="top">
            				<table border="0" width="80%" cellspacing="1" cellpadding="2">
               					<tr>
               						<td class="reportBoxHeading" width="5%">
               						<?
               						if ($p_rank == 1) {
               							echo '<a href="'.tep_href_link(FILENAME_THEME_ASSIGNATION, 'action=add_theme_to_cat&catID='.(int)$_REQUEST["cat_id"]).'">'.tep_image(DIR_WS_ICONS.'add_item.gif', 'Assign theme to this category', '', '', 'align="top"').'</a>';
               						}
               						?>
               						</td>
			       					<td class="reportBoxHeading" width="8%"><?=TABLE_HEADING_THEME_ID?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_THEME_TITLE?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_THEME_LANGUAGE?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_THEME_START_DATE?></td>
					                <td class="reportBoxHeading" width="10%"><?=TABLE_HEADING_THEME_END_DATE?></td>
			   					</tr>
<?
	if ($p_rank == 1) {
		if (tep_db_num_rows($themes_2_cat_result_sql)) {
			$row_count = 0;
			while ($themes_2_cat_row = tep_db_fetch_array($themes_2_cat_result_sql)) {
	    		$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
?>
								<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top">
										<a href="<?=tep_href_link(FILENAME_THEME_ASSIGNATION, 'action=edit_assigned_theme&themeID='.$themes_2_cat_row["themes_id"].'&catID='.(int)$_REQUEST["cat_id"])?>"><?=tep_image(DIR_WS_ICONS.'edit.gif', 'Edit', '', '', 'align="top"')?></a>
										<a href="javascript:void(confirm_delete('this assignation', '', '<?=tep_href_link(FILENAME_THEME_ASSIGNATION, 'action=unassign_theme&themeID='.$themes_2_cat_row["themes_id"].'&catID='.(int)$_REQUEST["cat_id"])?>'))"><?=tep_image(DIR_WS_ICONS.'delete.gif', 'Unassign', '', '', 'align="top"')?></a>
									</td>
									<td class="reportRecords" valign="top"><?=$themes_2_cat_row["themes_id"]?></td>
									<td class="reportRecords" valign="top"><?=$themes_2_cat_row["themes_title"]?></td>
									<td class="reportRecords" valign="top">
									<?
									for ($lang_cnt=0; $lang_cnt < count($languages_array); $lang_cnt++) {
										if ($languages_array[$lang_cnt]['id'] == $themes_2_cat_row["themes_language_id"]) {
											echo $languages_array[$lang_cnt]['text'];
											break;
										}
									}
									?>
									</td>
									<td class="reportRecords" valign="top"><?=($themes_2_cat_row["start_date"] == '0000-00-00' ? '&nbsp;' : $themes_2_cat_row["start_date"])?></td>
									<td class="reportRecords" valign="top"><?=($themes_2_cat_row["end_date"] == '0000-00-00' ? '&nbsp;' : $themes_2_cat_row["end_date"])?></td>
								</tr>
<?
				$row_count++;
			}
		} else {
			echo '				<tr class="reportListingOdd">
									<td class="reportRecords" colspan="6">'.TEXT_NO_THEME_ASSIGNED.'</td>
								</tr>';
		}
	}
	echo '					</table>
    					</td>
    				</tr>';
}
?>

    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>