<?
/*
  	$Id: theme_customizing.php,v 1.1 2006/07/19 09:57:13 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
require(DIR_WS_CLASSES . 'file_manager.php');
require(DIR_WS_CLASSES . 'theme_manager.php');

$file_obj = new file_manager();

if (!@is_dir(DIR_FS_THEME)) {
	$file_obj->touchDirectory(DIR_FS_THEME);
}

$action = (isset($_REQUEST["action"]) ? $_REQUEST["action"] : '');

$languages = tep_get_languages();
$languages_array = array( array('id'=>'', 'text'=>'Select Language') );
for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
	$languages_array[] = array('id' => $languages[$i]['id'],
                               'text' => $languages[$i]['name']);
}

if (tep_not_null($action)) {
	switch ($action) {
		case "insert_theme":
		case "update_theme":
			$theme_title = tep_db_prepare_input($HTTP_POST_VARS["themes_title"]);
			//Make sure theme title is unique
			if ($action == "insert_theme") {
				$theme_title_select_sql = "SELECT count(themes_id) as title_count FROM " . TABLE_THEMES . " WHERE themes_title='" . $HTTP_POST_VARS["themes_title"] . "';";
			} else {
				$theme_title_select_sql = "SELECT count(themes_id) as title_count FROM " . TABLE_THEMES . " WHERE themes_title='" . $HTTP_POST_VARS["themes_title"] . "' AND themes_id <> '" . (int)$HTTP_POST_VARS["themeID"] . "';";
			}
			$theme_title_result_sql = tep_db_query($theme_title_select_sql);
			if ($theme_title_row = tep_db_fetch_array($theme_title_result_sql)) {
				if ($theme_title_row["title_count"] > 0) {
					$messageStack->add_session("This theme title: " . $theme_title . " had been used by other theme!", 'warning');
					tep_redirect(tep_href_link(FILENAME_THEME_CUSTOMIZING));
					break;
				}
			}
			
			$theme_sql_data_array = array(	'themes_language_id' => tep_db_prepare_input($HTTP_POST_VARS["themes_language_id"]),
    	           							'themes_title' => $theme_title,
    	           							'themes_description' => tep_db_prepare_input($HTTP_POST_VARS["themes_description"])
        	           					);
			
            if ($action == "insert_theme") {
            	$theme_insert_sql_data = array(	'themes_date_created' => 'now()');
            	$sql_data_array = array_merge($theme_sql_data_array, $theme_insert_sql_data);
				
				tep_db_perform(TABLE_THEMES, $sql_data_array);
				$new_theme_id = tep_db_insert_id();
				
				if (isset($HTTP_POST_VARS["copy_from_theme"]) && $HTTP_POST_VARS["copy_from_theme"] > 0) {
					$file_obj->sync(DIR_FS_THEME.$HTTP_POST_VARS["copy_from_theme"], DIR_FS_THEME.$new_theme_id);
				}
			} else {
				$theme_update_sql_data = array(	'themes_last_modified' => 'now()');
            	$sql_data_array = array_merge($theme_sql_data_array, $theme_update_sql_data);
            	
				tep_db_perform(TABLE_THEMES, $sql_data_array, 'update', ' themes_id="'.(int)$HTTP_POST_VARS["themeID"].'"');
			}
			tep_redirect(tep_href_link(FILENAME_THEME_CUSTOMIZING));
			
			break;
		case "delete_theme":
			$delete_theme_id = (int) $_REQUEST["themeID"];
			if ($delete_theme_id > 0) {
				$file_obj->removeDirectory(DIR_FS_THEME . $delete_theme_id . '/');
				$theme_delete_sql = "DELETE FROM " . TABLE_THEMES . " WHERE themes_id='" . $delete_theme_id . "'";
				tep_db_query($theme_delete_sql);
			}
			
			tep_redirect(tep_href_link(FILENAME_THEME_CUSTOMIZING));
			break;
		case "confirm_upload":
			$theme_obj = new theme_manager($_REQUEST["themeID"]);
			
			$themeXMLFile = DIR_FS_ADMIN . 'theme/standard.xml';
			if ( ($themeXMLContents = @file_get_contents ($themeXMLFile)) != FALSE) {
				$them_structure = $theme_obj->parse($themeXMLContents);
				$valid_file_array = array();
				
				for ($i=0; $i < count($them_structure); $i++) {
					if ($them_structure[$i]['_NAME'] == 'folder') {
						if (count($them_structure[$i]['_ELEMENTS'])) {
							while(list($index, $eleArray) = each($them_structure[$i]['_ELEMENTS'])) {
								$valid_file_array[$them_structure[$i]['name']][] = $eleArray;
							}
						}
					}
				}
				
				if (count($valid_file_array)) {
					foreach ($valid_file_array as $path => $files) {
						for ($file_cnt=0; $file_cnt < count($files); $file_cnt++) {
							$cur_file = $_FILES['upload']['name'][$files[$file_cnt]['name']];
							if (isset($cur_file) && tep_not_null($cur_file)) {
								$valid_ext = explode(',', $files[$file_cnt]['allowedtypes']);
								$file_obj->fileUpload($_FILES['upload'], $files[$file_cnt]['name'], DIR_FS_THEME . $_REQUEST["themeID"] . '/' . $path . '/', $valid_ext, true);
							}
						}
					}
				}
			}
			tep_redirect(tep_href_link(FILENAME_THEME_CUSTOMIZING));
			
			break;
	}
}
?>

<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script>
	<!--
		var theme_name_del = new Array();
	//-->
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
			<!-- body_text //-->
		    <td width="100%" valign="top">
		    	<table border="0" width="100%" cellspacing="0" cellpadding="2">
<?
if ($action == "new_theme" || $action == "edit_theme") {
	$text_new_or_edit = ($action == 'new_theme') ? TEXT_INFO_HEADING_NEW_THEME : TEXT_INFO_HEADING_EDIT_THEME;
?>
		    		<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=$text_new_or_edit?></td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
<?
	echo tep_draw_form('theme_form', FILENAME_THEME_CUSTOMIZING, tep_get_all_get_params(array('action')) . 'action='.($action=="new_theme" ? 'insert_theme' : 'update_theme'), 'post', 'onSubmit="return theme_form_checking();"');
	if ($_REQUEST["themeID"]) {
		$theme_select_sql = "SELECT * FROM " . TABLE_THEMES . " WHERE themes_id='" . $_REQUEST["themeID"] . "'";
		$theme_result_sql = tep_db_query($theme_select_sql);
		$theme_row = tep_db_fetch_array($theme_result_sql);
		
		echo tep_draw_hidden_field("themeID", $_REQUEST["themeID"]);
	}
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="2">
								<tr>
									<td class="main" width="21%" valign="top"><?=ENTRY_THEME_TITLE?></td>
									<td class="main" valign="top"><?=tep_draw_input_field('themes_title', $theme_row["themes_title"], 'size="40" id="themes_title"')?></td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
								<tr>
									<td class="main" valign="top"><?=ENTRY_THEME_LANGUAGE?></td>
									<td class="main">
										<?=tep_draw_pull_down_menu("themes_language_id", $languages_array, $theme_row["themes_language_id"], ' id="themes_language_id" ')?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
			      				<tr>
			      					<td class="main" valign="top"><?=ENTRY_THEME_DESCRIPTION?></td>
			        				<td class="main">
			        					<?=tep_draw_textarea_field('themes_description', 'soft', '60', '10', $theme_row["themes_description"])?>
			        				</td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
<?	if ($action == "new_theme") {
		$existing_theme_array = array( array('id'=>'', 'text'=>'Select Theme') );
		$existing_theme_select_sql = "SELECT themes_id, themes_title FROM " . TABLE_THEMES . " ORDER BY themes_title";
		$existing_theme_result_sql = tep_db_query($existing_theme_select_sql);
		while ($existing_theme_row = tep_db_fetch_array($existing_theme_result_sql)) {
			$existing_theme_array[] = array('id' => $existing_theme_row['themes_id'],
		                               		'text' => $existing_theme_row['themes_title']);
		}
?>
								<tr>
									<td class="main" valign="top"><?=ENTRY_COPY_FROM_THEME?></td>
									<td class="main">
										<?=tep_draw_pull_down_menu("copy_from_theme", $existing_theme_array, '')?>
									</td>
								</tr>
								<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
<?	} ?>
								<tr>
									<td colspan="2" align="right">
										<?=($action=="new_theme" ? tep_image_submit('button_insert.gif', IMAGE_INSERT, "") : tep_image_submit('button_update.gif', IMAGE_UPDATE, "")) . '&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_THEME_CUSTOMIZING) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>'?>
									</td>
								</tr>
							</table>
        				</td>
        			</tr>
	        		</form>
	        		<script>
	        		<!--
						function theme_form_checking() {
							if (trim_str(document.getElementById('themes_title').value) == "") {
								alert('Please enter the title for this theme!');
								document.getElementById('themes_title').value = '';
								document.getElementById('themes_title').focus();
								return false;
							}
							
							if (document.getElementById('themes_language_id').selectedIndex < 1) {
								alert('Please select the language for this theme!');
								document.getElementById('themes_language_id').focus();
								return false;
							}
							
							return true;
						}
						//-->
					</script>
<?
} else if ($action == "upload_theme_file") {
	$theme_obj = new theme_manager($_REQUEST["themeID"]);
	// To be continue...
	$themeXMLFile = DIR_FS_ADMIN . 'theme/standard.xml';
	if ( ($themeXMLContents = @file_get_contents ($themeXMLFile)) != FALSE) {
		$them_structure = $theme_obj->parse($themeXMLContents);
	}
	
	echo tep_draw_form('theme_upload_form', FILENAME_THEME_CUSTOMIZING, tep_get_all_get_params(array('action')) . 'action=confirm_upload', 'post', ' enctype="multipart/form-data" onSubmit="return theme_upload_form_checking();"');
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=TEXT_INFO_HEADING_UPLOAD_THEME?></td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td width="100%">
        					<table border="0" cellspacing="0" cellpadding="2">
			      				<tr>
			      					<td class="main"><?=ENTRY_HOME_DIRECTORY?></td>
			        				<td class="main"><?=DIR_FS_THEME.$_REQUEST["themeID"].'/'?></td>
			      				</tr>
			      				<tr>
			        				<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
			      				</tr>
<?
	for ($i=0; $i < count($them_structure); $i++) {
		if ($them_structure[$i]['_NAME'] == 'folder') {
?>
								<tr>
									<td class="main" colspan="2">
										<fieldset class="selectedFieldSet">
											<legend align=left class="sectionHeader"><?=$them_structure[$i]['name']?></legend>
											<table border="0" cellspacing="0" cellpadding="1">
<?			if (count($them_structure[$i]['_ELEMENTS'])) {
				while(list($index, $eleArray) = each($them_structure[$i]['_ELEMENTS'])) {
		      		echo '						<tr>	
							      					<td class="main" width="32%" rowspan="2" valign="top">'.$eleArray['name'].'</td>
							      					<td class="main">'.tep_draw_file_field('upload['.$eleArray['name'].']', 'size="40"').'</td>
							      				</tr>
							      				<tr>	
							      					<td class="main" valign="top">
							      						<table border="0" cellspacing="0" cellpadding="0">
							      							<tr>';
					$curDir = DIR_FS_THEME.$_REQUEST["themeID"].'/'.$them_structure[$i]['name'].'/';
					if ( ($found_filename = $file_obj->fileExists($curDir, $eleArray['name'], true)) !== false) {
						echo '									';
					}
					echo '	      							</tr>
														</table>
							      					</td>
							      				</tr>';
				}
			}
?>
							      			</table>
										</fieldset>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
<?		}
	}
?>
			      			</table>
			      		</td>
			      	</tr>
			      	<tr>
	  					<td>
	  						<table border="0" width="100%" cellspacing="0" cellpadding="0">
	  							<tr>
	  								<td width="20%">&nbsp;</td>
	  								<td align="right">
	  									<?=tep_image_submit('button_upload.gif', IMAGE_UPLOAD)?>&nbsp;
	  									<a href="<?=tep_href_link(FILENAME_THEME_CUSTOMIZING, '')?>"><?=tep_image_button('button_cancel.gif', IMAGE_CANCEL)?></a>
	  								</td>
	  							</tr>
	  						</table>
	  					</td>
	  				</tr>
	  				</form>
	  				<script>
	  				<!--
	  					function theme_upload_form_checking() {
	  						var any_file_to_upload = false;
	  						var frm = document.theme_upload_form;
	  						if (frm.elements.length != null) {
		  						for(i=0; i<frm.elements.length; i++) {
						  	  		if (frm.elements[i].type == 'file' && frm.elements[i].name.indexOf("upload[") === 0) {
						  	  			if (trim_str(frm.elements[i].value) != '') {
						  	  				any_file_to_upload = true;
						  	  				break;
						  	  			}
						  	  		}
						  		}
		  					}
		  					
		  					if (!any_file_to_upload) {
		  						alert('No any files to upload!');
		  						return false;
		  					}
	  						return true;
	  					}
	  				//-->
	  				</script>
<?
} else {
?>
					<tr>
        				<td width="100%">
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            					</tr>
            					<tr>
    								<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
  								</tr>
            					<tr>
            						<td class="main" valign="top">
            							<?='[ <a href="'.tep_href_link(FILENAME_THEME_CUSTOMIZING, 'action=new_theme').'" >'.LINK_ADD_THEME.'</a> ]';?>
            						</td>
            					</tr>
            				</table>
            			</td>
            		</tr>
            		<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
            			<td valign="top">
            				<table border="0" width="90%" cellspacing="1" cellpadding="2">
               					<tr>
               						<td class="reportBoxHeading" width="7%"><?="Action"?></td>
			       					<td class="reportBoxHeading" width="8%"><?=TABLE_HEADING_THEME_ID?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_THEME_TITLE?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_THEME_LANGUAGE?></td>
					                <td class="reportBoxHeading"><?=TABLE_HEADING_THEME_DESCRIPTION?></td>
					                <td class="reportBoxHeading" width="10%"><?=TABLE_HEADING_THEME_CREATION_DATE?></td>
					                <td class="reportBoxHeading" width="10%"><?=TABLE_HEADING_THEME_MODIFIED_DATE?></td>
			   					</tr>
<?
	$themes_select_sql = "select * from " . TABLE_THEMES . " order by themes_title";
	$theme_sql_object = new splitPageResults($HTTP_GET_VARS['page'], MAX_DISPLAY_SEARCH_RESULTS, $themes_select_sql, $themes_select_sql_numrows);
	$themes_result_sql = tep_db_query($themes_select_sql);
	
	if (tep_db_num_rows($themes_result_sql)) {
		$row_count = 0;
		while ($themes_row = tep_db_fetch_array($themes_result_sql)) {
	    	$row_style = ($row_count%2) ? 'reportListingEven' : 'reportListingOdd' ;
	    	$file_exist = file_exists(DIR_FS_THEME. $themes_row["themes_id"]) ? true : false;
?>
								<tr height="28" class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
									<td class="reportRecords" valign="top">
										<table border="0" cellspacing="0" cellpadding="1">
											<tr>
												<td width="33%">
<?			if ($themes_row["themes_type"] != 'system') { ?>
													<a href="<?=tep_href_link(FILENAME_THEME_CUSTOMIZING, 'action=edit_theme&themeID='.$themes_row["themes_id"])?>"><?=tep_image(DIR_WS_ICONS."edit.gif", "Edit", "", "", 'align="top"')?></a>
<?			} else echo '&nbsp;'; ?>
												</td>
												<td width="33%"><a href="<?=tep_href_link(FILENAME_THEME_CUSTOMIZING, 'action=upload_theme_file&themeID='.$themes_row["themes_id"])?>"><?=tep_image(DIR_WS_ICONS."file_upload.gif", "Upload files for this theme", "", "", 'align="top"')?></a></td>
												<td width="33%">
<?			if ($themes_row["themes_type"] != 'system') { ?>										
													<script>
														theme_name_del[<?=$themes_row["themes_id"]?>] = URLEncode("<?=addslashes($themes_row["themes_title"])?>");
													</script>
													<a href="javascript:void(confirm_delete(URLDecode(theme_name_del[<?=$themes_row["themes_id"]?>]), 'Theme', '<?=tep_href_link(FILENAME_THEME_CUSTOMIZING, 'action=delete_theme&themeID='.$themes_row["themes_id"])?>'))"><?=tep_image(DIR_WS_ICONS."delete.gif", "Delete", "", "", 'align="top"')?></a>
<?			} ?>								</td>
										</table>
									</td>
									<td class="reportRecords" valign="top"><span class="<?=$file_exist ? 'blackIndicator' : 'redIndicator' ?>"><?=$themes_row["themes_id"]?></span></td>
									<td class="reportRecords" valign="top"><?=$themes_row["themes_title"]?></td>
									<td class="reportRecords" valign="top">
									<?
									for ($lang_cnt=0; $lang_cnt < count($languages_array); $lang_cnt++) {
										if ($languages_array[$lang_cnt]['id'] == $themes_row["themes_language_id"]) {
											echo $languages_array[$lang_cnt]['text'];
											break;
										}
									}
									?>
									</td>
									<td class="reportRecords" valign="top"><?=$themes_row["themes_description"]?></td>
									<td class="reportRecords" valign="top"><?=tep_date_short($themes_row["themes_date_created"])?></td>
									<td class="reportRecords" valign="top"><?=tep_datetime_short($themes_row["themes_last_modified"])?></td>
								</tr>
<?
			$row_count++;
		}
?>
								<tr>
			            			<td colspan="7">
			            				<table border="0" width="100%" cellspacing="0" cellpadding="2">
			              					<tr>
			                					<td class="smallText" valign="top"><?=$theme_sql_object->display_count($themes_select_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_THEMES)?></td>
			                					<td class="smallText" align="right"><?=$theme_sql_object->display_links($themes_select_sql_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'], tep_get_all_get_params(array('page')), 'page')?></td>
			              					</tr>
			            				</table>
			            			</td>
			            			<td colspan="3">&nbsp;</td>
          						</tr>
<?	} else {
		echo '					<tr>
									<td class="reportRecords" colspan="7">'.TEXT_NO_THEME.'</td>
								</tr>';
	}
	echo '					</table>
    					</td>
    				</tr>';
}
?>

    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>