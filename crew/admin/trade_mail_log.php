<?php

require('includes/application_top.php');

$action = (isset($HTTP_GET_VARS['action']) ? $HTTP_GET_VARS['action'] : '');

$faction_array[] = array(TEXT_HUMAN => TEXT_ALLIANCE);
$faction_array[] = array(TEXT_DRAWF => TEXT_ALLIANCE);
$faction_array[] = array(TEXT_NIGHT_ELF => TEXT_ALLIANCE);
$faction_array[] = array(TEXT_GNOME => TEXT_ALLIANCE);
$faction_array[] = array(TEXT_DRAENEI => TEXT_ALLIANCE);

$faction_array[] = array(TEXT_ORC => TEXT_HORDE);
$faction_array[] = array(TEXT_UNDEAD => TEXT_HORDE);
$faction_array[] = array(TEXT_TAUREN => TEXT_HORDE);
$faction_array[] = array(TEXT_TROLL => TEXT_HORDE);
$faction_array[] = array(TEXT_BLOOD_ELF => TEXT_HORDE);

$view_admin_log_permission = tep_admin_files_actions(FILENAME_TRADE_MAIL_LOG, 'VIEW_ADMIN_MT_LOGS');
$view_supplier_log_permission = tep_admin_files_actions(FILENAME_TRADE_MAIL_LOG, 'VIEW_SUPPLIER_MT_LOGS');

if (tep_not_null($action)) {
	if (isset($_REQUEST["search"])) {
		$_SESSION['trade_mail_log_param']["trade_mail_log_start_date"] = tep_db_prepare_input($_REQUEST["trade_mail_log_start_date"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_end_date"] = tep_db_prepare_input($_REQUEST["trade_mail_log_end_date"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_order_id"] = tep_db_prepare_input($_REQUEST["trade_mail_log_order_id"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_account_name"] = tep_db_prepare_input($_REQUEST["trade_mail_log_account_name"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_char_name"] = tep_db_prepare_input($_REQUEST["trade_mail_log_char_name"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_server"] = tep_db_prepare_input($_REQUEST["trade_mail_log_server"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_realm"] = tep_db_prepare_input($_REQUEST["trade_mail_log_realm"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_char_log"] = tep_db_prepare_input($_REQUEST["trade_mail_log_char_log"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_faction"] = tep_db_prepare_input($_REQUEST["trade_mail_log_faction"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_trade_mail"] = tep_db_prepare_input($_REQUEST["trade_mail_log_trade_mail"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"] = tep_db_prepare_input($_REQUEST["trade_mail_log_send_receive"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_gold_item"] = tep_db_prepare_input($_REQUEST["trade_mail_log_gold_item"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_pc_name"] = tep_db_prepare_input($_REQUEST["trade_mail_log_pc_name"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_pic"] = tep_db_prepare_input($_REQUEST["trade_mail_log_pic"]);
		$_SESSION['trade_mail_log_param']["trade_mail_log_from"] = tep_db_prepare_input($_REQUEST["trade_mail_log_from"]);
	} else if (isset($_REQUEST["reset"])) {
		unset($_SESSION['trade_mail_log_param']);
        tep_redirect(tep_href_link(FILENAME_TRADE_MAIL_LOG));
	}
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?php echo TITLE; ?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
	<script language="javascript" src="includes/javascript/php_serializer.js"></script>
	<script language="javascript" src="includes/javascript/modal_win.js"></script>
	<script language="javascript" src="includes/javascript/custom_product_xmlhttp.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">

<!-- header //-->
<?php require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
	<tr>
		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    		<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
<!-- left_navigation //-->
<?php require(DIR_WS_INCLUDES . 'column_left.php'); ?>
<!-- left_navigation_eof //-->
			</table>
		</td>
<!-- body_text //-->
		<td width="100%" valign="top">
<?
	if ($_REQUEST['action'] == 'search') {
		$start_date_where_str = " 1 ";
		$end_date_where_str = " 1 ";
		$order_id_where_str = " 1 ";
		$accont_name_where_str = " 1 ";
		$realm_where_str = " 1 ";
		$faction_where_str = " 1 ";
		$server_where_str = " 1 ";
		$char_name_where_str = " 1 ";
		$type_where_str = " 1 ";
		$transfer_where_type_str = " 1 ";
		$pc_name_where_str = " 1 ";
		$pic_where_str = " 1 ";
		$send_receive_str = " 1 ";
		$user_role_where_str = " game_char_log_user_role = NULL ";
		
		$game_char_log_info_select_str = "	SELECT gcl.* 
											FROM " . TABLE_GAME_CHAR_LOG . " AS gcl ";
	
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_start_date"])) {
			if (strpos($_SESSION['trade_mail_log_param']["trade_mail_log_start_date"], ':') !== false) {
				list($yr, $mth, $day) = explode('-', $startDateObj[0]);
				list($hr, $min) = explode(':', $startDateObj[1]);
				$start_date_where_str = " ( DATE_FORMAT(gcl.game_char_log_time, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION['trade_mail_log_param']["trade_mail_log_start_date"]));
				$start_date_where_str = " ( DATE_FORMAT(gcl.game_char_log_time, '%Y-%m-%d') >= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
			}
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_end_date"])) {
			if (strpos($_SESSION['trade_mail_log_param']["trade_mail_log_end_date"], ':') !== false) {
				$endDateObj = explode(' ', trim($_SESSION['trade_mail_log_param']["trade_mail_log_end_date"]));
				list($yr, $mth, $day) = explode('-', $endDateObj[0]);
				list($hr, $min) = explode(':', $endDateObj[1]);
				$end_date_where_str = " ( DATE_FORMAT(gcl.game_char_log_time, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT('".date("Y-m-d H:i:s", mktime((int)$hr, (int)$min, 0, $mth, $day, $yr))."', '%Y-%m-%d %H:%i:%s') )";
			} else {
				list($yr, $mth, $day) = explode('-', trim($_SESSION['trade_mail_log_param']["trade_mail_log_end_date"]));
				$end_date_where_str = " ( DATE_FORMAT(gcl.game_char_log_time, '%Y-%m-%d') <= DATE_FORMAT('".date("Y-m-d", mktime(0,0,0,$mth,$day,$yr))."','%Y-%m-%d') )";
			}
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_order_id"])) {
			$order_id_where_str = " gcl.game_char_log_orders_id ='" . tep_db_input($_SESSION['trade_mail_log_param']["trade_mail_log_order_id"]) . "'";
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_account_name"])) {
			$accont_name_where_str = " gcl.game_char_log_account_name ='" . tep_db_input($_SESSION['trade_mail_log_param']["trade_mail_log_account_name"]) . "'";
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_char_name"])) {
			$char_name_where_str = "  gcl.game_char_log_login_as ='" . tep_db_input($_SESSION['trade_mail_log_param']["trade_mail_log_char_name"]) . "'";
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_server"])) {
			if ($_SESSION['trade_mail_log_param']["trade_mail_log_server"] == '1') {
				$server_where_str = " gcl.game_char_log_server = '".TEXT_US."'";
			} else if ($_SESSION['trade_mail_log_param']["trade_mail_log_server"] == '2') {
				$server_where_str = " gcl.game_char_log_server = '".TEXT_EU."'";
			}
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_realm"])) {
			$realm_where_str = " gcl.game_char_log_realm ='" . tep_db_input($_SESSION['trade_mail_log_param']["trade_mail_log_realm"]) . "'";
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_faction"])) {
			if ($_SESSION['trade_mail_log_param']["trade_mail_log_faction"] == '1') {
				$faction_where_str = " gcl.game_char_log_race IN ('" . TEXT_HUMAN ."', '" . TEXT_DRAWF . "', '" . TEXT_NIGHT_ELF . "', '" . TEXT_GNOME . "') ";
			} else if ($_SESSION['trade_mail_log_param']["trade_mail_log_faction"] == '2') {
				$faction_where_str = " gcl.game_char_log_race IN ('" . TEXT_ORC ."', '" . TEXT_UNDEAD . "', '" . TEXT_TAUREN . "', '" . TEXT_TROLL . "') ";
			}
		}
		
		if ($_SESSION['trade_mail_log_param']["trade_mail_log_pc_name"]) {
			$pc_name_where_str = " game_char_log_computer_name ='" . tep_db_input($_SESSION['trade_mail_log_param']["trade_mail_log_pc_name"]) . "'";
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_trade_mail"])) {
			if ($_SESSION['trade_mail_log_param']["trade_mail_log_trade_mail"] == '1') {
				$type_where_str = " gcl.game_char_log_type = 'mail'";
			} else if ($_SESSION['trade_mail_log_param']["trade_mail_log_trade_mail"] == '2') {
				$type_where_str = " gcl.game_char_log_type = 'trade'";
			}
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"])) {
			if ($_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"] == '1') {
				$send_receive_str = "  gcl.game_char_log_login_as = game_char_log_sender ";
			} else if ($_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"] == '2') {
				$send_receive_str = "  gcl.game_char_log_login_as = game_char_log_receiver ";
			}
		}
		
		if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_gold_item"])) {
			if ($_SESSION['trade_mail_log_param']["trade_mail_log_gold_item"] == '1') {
				if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"])) {
					if ($_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"] == '1') {
						$transfer_where_type_str = " gcl.game_char_log_send REGEXP \"([0-9]+)(:~:)(.*)?\" ";
					} else if ($_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"] == '2') {
						$transfer_where_type_str = " gcl.game_char_log_receive REGEXP \"([0-9]+)(:~:)(.*)?\" ";
					}
				}
			} else if ($_SESSION['trade_mail_log_param']["trade_mail_log_gold_item"] == '2') {
				if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"])) {
					if ($_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"] == '1') {
						$transfer_where_type_str = " gcl.game_char_log_send REGEXP \"([0-9]+)?(:~:)([0-9a-zA-Z]+)\" ";
					} else if ($_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"] == '2') {
						$transfer_where_type_str = " gcl.game_char_log_receive REGEXP \"([0-9]+)?(:~:)([0-9a-zA-Z]+)\" ";
					}
				}
			}
		}
		
		if ($_SESSION['trade_mail_log_param']["trade_mail_log_from"] == TEXT_ADMIN) {
			if ($view_admin_log_permission) {
				$user_role_where_str = " gcl.game_char_log_user_role = '" . tep_db_input(strtolower($_SESSION['trade_mail_log_param']["trade_mail_log_from"])) . "'";
				
				if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_pic"])) {
					$game_char_log_info_select_str .= "	INNER JOIN " . TABLE_ADMIN . " AS a 
																ON (gcl.game_char_log_login_user = a.admin_id) ";
					
					$pc_name_where_str = " a.admin_email_address ='" . tep_db_input($_SESSION['trade_mail_log_param']["trade_mail_log_pic"]) . "' OR gcl.game_char_log_login_user = '" . tep_db_input($_SESSION['trade_mail_log_param']["trade_mail_log_pic"]) . "'";
				}
			}
		} else if ($_SESSION['trade_mail_log_param']["trade_mail_log_from"] == TEXT_SUPPLIER) {
			if ($view_supplier_log_permission) {
				$user_role_where_str = " gcl.game_char_log_user_role = '" . tep_db_input(strtolower($_SESSION['trade_mail_log_param']["trade_mail_log_from"])) . "'";
			
				if (tep_not_null($_SESSION['trade_mail_log_param']["trade_mail_log_pic"])) {
					$game_char_log_info_select_str .= "	INNER JOIN " . TABLE_SUPPLIER . " AS s 
																ON (gcl.game_char_log_login_user = s.supplier_id) ";
					
					$pc_name_where_str = " s.supplier_email_address ='" . tep_db_input($_SESSION['trade_mail_log_param']["trade_mail_log_pic"]) . "' OR gcl.game_char_log_login_user = '" . tep_db_input($_SESSION['trade_mail_log_param']["trade_mail_log_pic"]) . "'";
				}
			}
		}
?>
			<table border="0" width="100%" cellspacing="1" cellpadding="2">
				<tr>
					<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
				</tr>
				<tr>
					<td colspan="11"><span class="pageHeading"><?=sprintf(HEADING_INPUT_TITLE, '')?></span></td>
				</tr>
				<tr>
					<td colspan="11"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
				</tr>
				<tr>
					<td class="ordersBoxHeading" width="7%"><?=TABLE_HEADING_DATE?></td>
					<td class="ordersBoxHeading" nowrap><?=TABLE_HEADING_ORDER_ID?></td>
					<td class="ordersBoxHeading" width="9%"><?=TABLE_HEADING_ACC_NAME?></td>
					<td class="ordersBoxHeading" width="5%"><?=TABLE_HEADING_SERVER?></td>
					<td class="ordersBoxHeading" width="7%"><?=TABLE_HEADING_REALM?></td>
					<td class="ordersBoxHeading" width="7%"><?=TABLE_HEADING_FACTION?></td>
					<td class="ordersBoxHeading"><?=TABLE_HEADING_CHAR?></td>
					<td class="ordersBoxHeading"><?=TABLE_HEADING_ACTION?></td>
					<td class="ordersBoxHeading"><?=TABLE_HEADING_SENDER_RECEIVER?></td>
					<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_ITEM_MONEY_SEND_RECEIVED?></td>
					<td class="ordersBoxHeading"><?=TABLE_HEADING_SYSTEM_MSG?></td>
					<td class="ordersBoxHeading" align="right"><?=TABLE_HEADING_CURR_BAL?></td>
					<td class="ordersBoxHeading" width="4%" align="center"><?=TABLE_HEADING_TYPE?></td>
					<td class="ordersBoxHeading" width="6%" align="center"><?=TABLE_HEADING_PC_NAME?></td>
					<td class="ordersBoxHeading" align="center"><?=TABLE_HEADING_PIC?></td>
					<td class="ordersBoxHeading" align="center"><?=TABLE_HEADING_USER_ROLE?></td>
				</tr>
<?
				$row_count = 0;
				
				$game_char_log_info_where_str = " WHERE 1 
														AND $start_date_where_str 
														AND $end_date_where_str 
														AND $order_id_where_str 
														AND $accont_name_where_str 
														AND $server_where_str 
														AND $realm_where_str 
														AND $char_name_where_str 
														AND $faction_where_str 
														AND $type_where_str 
														AND $transfer_where_type_str
														AND $pc_name_where_str 
														AND $pic_where_str 
														AND $send_receive_str 
														AND $user_role_where_str 
													ORDER BY game_char_log_time DESC";
				
				$game_char_log_info_select_sql = $game_char_log_info_select_str . $game_char_log_info_where_str;
				
				if ($show_records != "ALL") {
					$orders_split_object = new splitPageResults($HTTP_GET_VARS['page'], ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $game_char_log_info_select_sql, $game_char_log_info_select_sql_numrows, true);
				}
				
				$game_char_log_info_result_sql = tep_db_query($game_char_log_info_select_sql);
				
				while ($game_char_log_info_row = tep_db_fetch_array($game_char_log_info_result_sql)) {
					$row_style = ($row_count%2) ? 'ordersListingEven' : 'ordersListingOdd';
					
					$item_money_send_log = "";
					$item_money_receive_log = "";
					$pre_bal_log = "";
					$curr_bal_log = "";
					
					$send_array = array();
					$receive_array = array();
					$currency_array = array();
					
					$currency_array[] = array('currency' => 'Golds', 'value' => '10000');
					$currency_array[] = array('currency' => 'Silvers', 'value' => '100');
					$currency_array[] = array('currency' => 'Coppers', 'value' => '1');
					
					if (tep_not_null($game_char_log_info_row['game_char_log_send'])) {
						$send_array = explode(':~:', $game_char_log_info_row['game_char_log_send']);
						
						$currencies_send_result_array = tep_generate_game_currencies($send_array[0], $currency_array, "Golds");
						
						for ($count = 0; $count <sizeof($currencies_send_result_array); $count++) {
							foreach ($currencies_send_result_array[$count] as $currency_name => $value) {
								$item_money_send_log .= $value . ' ' . $currency_name . '<br>';
							}
						}
					} else {
						$item_money_send_log .= number_format(0, 4) . "<br>";
					}
					
					if (tep_not_null($game_char_log_info_row['game_char_log_receive'])) {
						$receive_array = explode(':~:', $game_char_log_info_row['game_char_log_receive']);
						
						$currencies_receive_result_array = tep_generate_game_currencies($receive_array[0], $currency_array, "Golds");
						
						for ($count = 0; $count <sizeof($currencies_receive_result_array); $count++) {
							foreach ($currencies_receive_result_array[$count] as $currency_name => $value) {
								$item_money_receive_log .= $value . ' ' . $currency_name . '<br>';
							}
						}
					} else {
						$item_money_receive_log .= number_format(0, 4) . "<br>";
					}
					
					if (tep_not_null($game_char_log_info_row['game_char_log_balance_before'])) {
						$pre_bal_log .= ($game_char_log_info_row['game_char_log_balance_before'] / 10000) . "<br>";
					} else {
						$pre_bal_log .= number_format(0, 4) . "<br>";
					}
					
					if (tep_not_null($game_char_log_info_row['game_char_log_balance_after'])) {
						$currencies_curr_bal_result_array = tep_generate_game_currencies($game_char_log_info_row['game_char_log_balance_after'], $currency_array, "Golds");
						
						for ($count = 0; $count <sizeof($currencies_curr_bal_result_array); $count++) {
							foreach ($currencies_curr_bal_result_array[$count] as $currency_name => $value) {
								$curr_bal_log .= $value . ' ' . $currency_name . '<br>';
							}
						}
					} else {
						$curr_bal_log .= number_format(0, 4) . "<br>";
					}
					
					if (isset($send_array[1])) {
						if (tep_not_null($send_array[1])) {
							
							$item_send_list_array = explode (',', $send_array[1]);
							
							if (isset($item_send_list_array)) {
								if (tep_not_null($item_money_send_log)) {
									$item_money_send_log .= '<br>';
								}
							
								foreach ($item_send_list_array as $key => $item_send_info) {
									$item_send_array = explode('_', $item_send_info);
									
									$game_item_info_id = $item_send_array[1];
									$item_quantity = $item_send_array[2];
									
									$game_item_info_name_select_sql = " SELECT game_item_info_name 
																		FROM " . TABLE_GAME_ITEM_INFO . " 
																		WHERE game_item_info_id ='" . tep_db_input($game_item_info_id) . "'";
								
									$game_item_info_name_result_sql = tep_db_query($game_item_info_name_select_sql);
									$game_item_info_name_row = tep_db_fetch_array($game_item_info_name_result_sql);
									
									$item_money_send_log .= $game_item_info_name_row['game_item_info_name'] . ' (' . $item_quantity . ')<br>';
								}
							}
						}
					}
					
					if (isset($receive_array[1])) {
						if (tep_not_null($receive_array[1])) {
							$item_receive_list_array = explode (',', $receive_array[1]);
							
							if (isset($item_receive_list_array)) {
								if (tep_not_null($item_money_receive_log)) {
									$item_money_receive_log .= '<br>';
								}
							
								foreach ($item_receive_list_array as $key => $item_receive_info) {
									$item_receive_array = explode('_', $item_receive_info);
									
									$game_item_info_id = $item_receive_array[1];
									$item_quantity = $item_receive_array[2];
									
									$game_item_info_name_select_sql = " SELECT game_item_info_name 
																		FROM " . TABLE_GAME_ITEM_INFO . " 
																		WHERE game_item_info_id ='" . tep_db_input($game_item_info_id) . "'";
								
									$game_item_info_name_result_sql = tep_db_query($game_item_info_name_select_sql);
									$game_item_info_name_row = tep_db_fetch_array($game_item_info_name_result_sql);
									
									$item_money_receive_log .= $game_item_info_name_row['game_item_info_name'] . ' (' . $item_quantity . ')<br>';
								}
							}
						}
					}
					
					if ($game_char_log_info_row['game_char_log_type'] == 'mail') {
?>
				<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
					<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_time']?></td>
					<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_orders_id']?></td>
					<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_account_name']?></td>
					<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_server']?></td>
					<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_realm']?></td>
					<td class="ordersRecords" valign="top">
<?
					for ($faction_array_count = 0; $faction_array_count < sizeof($faction_array); $faction_array_count++) {
						foreach ($faction_array[$faction_array_count] as $race => $faction) {
							if ($game_char_log_info_row['game_char_log_race'] == $race) {
								echo $faction;
							}
						}
					}
?>
					</td>
					<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_login_as']?></td>
<?						if ($game_char_log_info_row['game_char_log_login_as'] == $game_char_log_info_row['game_char_log_sender']) {	?>
							<td class="ordersRecords" valign="top"><?=TEXT_SEND?></td>
							<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_receiver']?></td>
							<td class="ordersRecords" valign="top" align="right"><?=$item_money_send_log?></td>
<?						} else if ($game_char_log_info_row['game_char_log_login_as'] == $game_char_log_info_row['game_char_log_receiver']) { ?>
							<td class="ordersRecords" valign="top"><?=TEXT_RECEIVED?></td>
							<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_sender']?></td>
							<td class="ordersRecords" valign="top" align="right"><?=$item_money_receive_log?></td>
<?						}	?>
					<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_system_messages']?></td>
					<td class="ordersRecords" valign="top" align="right"><?=$curr_bal_log?></td>
					<td class="ordersRecords" valign="top" align="center"><?=$game_char_log_info_row['game_char_log_type']?></td>
					<td class="ordersRecords" valign="top" align="center"><?=$game_char_log_info_row['game_char_log_computer_name']?></td>
					<td class="ordersRecords" valign="top" align="center">
<?
					if (is_numeric($game_char_log_info_row['game_char_log_login_user'])) {
						if ($game_char_log_info_row['game_char_log_user_role'] == strtolower(TEXT_SUPPLIER)) {
							$supplier_email_address_select_sql = "	SELECT supplier_email_address FROM " . TABLE_SUPPLIER . " WHERE supplier_id ='" . (int)$game_char_log_info_row['game_char_log_login_user'] . "'";
							$supplier_email_address_result_sql = tep_db_query($supplier_email_address_select_sql);
							$supplier_email_address_row = tep_db_fetch_array($supplier_email_address_result_sql);
							
							echo $supplier_email_address_row['supplier_email_address'];
						} else if ($game_char_log_info_row['game_char_log_user_role'] == strtolower(TEXT_ADMIN)) {
							$admin_email_address_select_sql = "	 SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . (int)$game_char_log_info_row['game_char_log_login_user'] . "'";
							$admin_email_address_result_sql = tep_db_query($admin_email_address_select_sql);
							$admin_email_address_row = tep_db_fetch_array($admin_email_address_result_sql);
							
							echo $admin_email_address_row['admin_email_address'];
						}
					} else {
						echo $game_char_log_info_row['game_char_log_login_user'];
					}
?>
					</td>
					<td class="ordersRecords" valign="top" align="center"><?=$game_char_log_info_row['game_char_log_user_role']?></td>
				</tr>
<?					} else if ($game_char_log_info_row['game_char_log_type'] == 'trade') { ?>
				<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
					<td class="ordersRecords" valign="top" rowspan="2"><?=$game_char_log_info_row['game_char_log_time']?></td>
					<td class="ordersRecords" valign="top" rowspan="2"><?=$game_char_log_info_row['game_char_log_orders_id']?></td>
					<td class="ordersRecords" valign="top" rowspan="2"><?=$game_char_log_info_row['game_char_log_account_name']?></td>
					<td class="ordersRecords" valign="top" rowspan="2"><?=$game_char_log_info_row['game_char_log_server']?></td>
					<td class="ordersRecords" valign="top" rowspan="2"><?=$game_char_log_info_row['game_char_log_realm']?></td>
					<td class="ordersRecords" valign="top" rowspan="2">
<?
					for ($faction_array_count = 0; $faction_array_count < sizeof($faction_array); $faction_array_count++) {
						foreach ($faction_array[$faction_array_count] as $race => $faction) {
							if ($game_char_log_info_row['game_char_log_race'] == $race) {
								echo $faction;
							}
						}
					}
?>
					</td>
					<td class="ordersRecords" valign="top" rowspan="2"><?=$game_char_log_info_row['game_char_log_login_as']?></td>
					<td class="ordersRecords" valign="top"><?=TEXT_SEND?></td>
<?						if ($game_char_log_info_row['game_char_log_login_as'] == $game_char_log_info_row['game_char_log_sender']) {	?>
							<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_receiver']?></td>
<?						} else if ($game_char_log_info_row['game_char_log_login_as'] == $game_char_log_info_row['game_char_log_receiver']) { ?>
							<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_sender']?></td>
<?						}	?>
					<td class="ordersRecords" valign="top" align="right"><?=$item_money_send_log?></td>
					<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_system_messages']?></td>
					<td class="ordersRecords" valign="top" align="right">
<?
						if (tep_not_null($game_char_log_info_row['game_char_log_send'])) {
							$trade_currencies_curr_bal_result_array = tep_generate_game_currencies(((int)$game_char_log_info_row['game_char_log_balance_before'] - (int)$send_array[0]), $currency_array, "Golds");
							
							for ($count = 0; $count <sizeof($trade_currencies_curr_bal_result_array); $count++) {
								foreach ($trade_currencies_curr_bal_result_array[$count] as $currency_name => $value) {
									echo $value . ' ' . $currency_name;
								}
							}
						} else {
							$trade_currencies_curr_bal_result_array = tep_generate_game_currencies($game_char_log_info_row['game_char_log_balance_before'], $currency_array, "Golds");
							
							for ($count = 0; $count <sizeof($trade_currencies_curr_bal_result_array); $count++) {
								foreach ($trade_currencies_curr_bal_result_array[$count] as $currency_name => $value) {
									echo $value . ' ' . $currency_name;
								}
							}
						}
?>
					</td>
					<td class="ordersRecords" valign="top" align="center"><?=$game_char_log_info_row['game_char_log_type']?></td>
					<td class="ordersRecords" valign="top" align="center"><?=$game_char_log_info_row['game_char_log_computer_name']?></td>
					<td class="ordersRecords" valign="top" align="center">
<?
					if (is_numeric($game_char_log_info_row['game_char_log_login_user'])) {
						if ($game_char_log_info_row['game_char_log_user_role'] == strtolower(TEXT_SUPPLIER)) {
							$supplier_email_address_select_sql = "	SELECT supplier_email_address FROM " . TABLE_SUPPLIER . " WHERE supplier_id ='" . (int)$game_char_log_info_row['game_char_log_login_user'] . "'";
							$supplier_email_address_result_sql = tep_db_query($supplier_email_address_select_sql);
							$supplier_email_address_row = tep_db_fetch_array($supplier_email_address_result_sql);
							
							echo $supplier_email_address_row['supplier_email_address'];
						} else if ($game_char_log_info_row['game_char_log_user_role'] == strtolower(TEXT_ADMIN)) {
							$admin_email_address_select_sql = "	 SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . (int)$game_char_log_info_row['game_char_log_login_user'] . "'";
							$admin_email_address_result_sql = tep_db_query($admin_email_address_select_sql);
							$admin_email_address_row = tep_db_fetch_array($admin_email_address_result_sql);
							
							echo $admin_email_address_row['admin_email_address'];
						}
					} else {
						echo $game_char_log_info_row['game_char_log_login_user'];
					}
?>
					</td>
					<td class="ordersRecords" valign="top" align="center"><?=$game_char_log_info_row['game_char_log_user_role']?></td>
				</tr>
				<tr class="<?=$row_style?>" onmouseover="rowOverEffect(this, 'reportListingRowOver')" onmouseout="rowOutEffect(this, '<?=$row_style?>')" onclick="rowClicked(this, '<?=$row_style?>')">
					<td class="ordersRecords" valign="top"><?=TEXT_RECEIVED?></td>
<?						if ($game_char_log_info_row['game_char_log_login_as'] == $game_char_log_info_row['game_char_log_sender']) {	?>
							<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_receiver']?></td>
<?						} else if ($game_char_log_info_row['game_char_log_login_as'] == $game_char_log_info_row['game_char_log_receiver']) { ?>
							<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_sender']?></td>
<?						}	?>
					<td class="ordersRecords" valign="top" align="right"><?=$item_money_receive_log?></td>
					<td class="ordersRecords" valign="top"><?=$game_char_log_info_row['game_char_log_system_messages']?></td>
					<td class="ordersRecords" valign="top" align="right">
<?
						if (tep_not_null($game_char_log_info_row['game_char_log_receive'])) {
							$trade_send = 0;
							
							if (isset($send_array[0])) {
								$trade_send = (int)$send_array[0];
							}
							
							$trade_currencies_curr_bal_result_array = tep_generate_game_currencies((((int)$game_char_log_info_row['game_char_log_balance_before'] - $trade_send) + (int)$receive_array[0]), $currency_array, "Golds");
							
							for ($count = 0; $count <sizeof($trade_currencies_curr_bal_result_array); $count++) {
								foreach ($trade_currencies_curr_bal_result_array[$count] as $currency_name => $value) {
									echo $value . ' ' . $currency_name;
								}
							}
						} else {
							$trade_currencies_curr_bal_result_array = tep_generate_game_currencies($game_char_log_info_row['game_char_log_balance_before'], $currency_array, "Golds");
							
							for ($count = 0; $count <sizeof($trade_currencies_curr_bal_result_array); $count++) {
								foreach ($trade_currencies_curr_bal_result_array[$count] as $currency_name => $value) {
									echo $value . ' ' . $currency_name;
								}
							}
						}
?>
					</td>
					<td class="ordersRecords" valign="top" align="center"><?=$game_char_log_info_row['game_char_log_type']?></td>
					<td class="ordersRecords" valign="top" align="center"><?=$game_char_log_info_row['game_char_log_computer_name']?></td>
					<td class="ordersRecords" valign="top" align="center">&nbsp;</td>
					<td class="ordersRecords" valign="top" align="center">&nbsp;</td>
				</tr>
<?
					}
					$row_count++;
				}
?>
				<tr>
					<td colspan="14">
						<table border="0" width="100%" cellspacing="0" cellpadding="2">
							<tr>
								<td class="smallText" valign="top"><?=$show_records == "ALL" ? sprintf(TEXT_DISPLAY_NUMBER_OF_LOGS, tep_db_num_rows($game_char_log_info_select_sql) > 0 ? "1" : "0", tep_db_num_rows($game_char_log_info_select_sql), tep_db_num_rows($game_char_log_info_select_sql)) : $orders_split_object->display_count($game_char_log_info_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), $HTTP_GET_VARS['page'], TEXT_DISPLAY_NUMBER_OF_LOGS)?></td>
								<td class="smallText" align="right"><?=$show_records == "ALL" ? "Page 1 of 1" : $orders_split_object->display_links($game_char_log_info_select_sql_numrows, ($show_records=='' || $show_records=='DEFAULT' ? MAX_DISPLAY_SEARCH_RESULTS : (int) $show_records), MAX_DISPLAY_PAGE_LINKS, $HTTP_GET_VARS['page'.$show_order_status[$status]], tep_get_all_get_params(array('page'.$show_order_status[$status], 'cont', 'criteria_id'))."cont=1", 'page')?></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="11"><?=tep_draw_separator('pixel_trans.gif', '1', '50')?></td>
				</tr>
			</table>
<?
	} else {
		$trade_mail_log_from_array = array();
		$trade_mail_log_from_array[] = array('id' => TEXT_SUPPLIER, 'text' => TEXT_SUPPLIER);
		$trade_mail_log_from_array[] = array('id' => TEXT_ADMIN, 'text' => TEXT_ADMIN);
?>
			<table border="0" width="100%" cellspacing="0" cellpadding="0">
				<tr>
					<td><div><iframe width="188" height="166" name="gToday:datetime:agenda.js:gfPop:plugins_24.js" id="gToday:datetime:agenda.js:gfPop:plugins_24.js" src="<?=DIR_WS_INCLUDES?>javascript/PopCalendarXp/ipopeng.htm" scrolling="no" frameborder="0" style="visibility:visible; z-index:999; position:absolute; top:-500px; left:-500px;"></iframe></div></td>
				</tr>
				<tr>
					<td>
<?						echo tep_draw_form('custom_product_criteria', FILENAME_TRADE_MAIL_LOG, 'action=search', 'post'); ?>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '7')?></td>
								</tr>
								<tr>
									<td colspan="2"><span class="pageHeading"><?=sprintf(HEADING_INPUT_TITLE, HEADING_CRITERIA)?></span></td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '20')?></td>
								</tr>
								<tr>
									<td valign="top">
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top"><?=ENTRY_HEADING_LOG_FROM?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top"><?=tep_draw_pull_down_menu('trade_mail_log_from', $trade_mail_log_from_array, $_SESSION['trade_mail_log_param']["trade_mail_log_from"])?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td width="11%">
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" width="12%"><?=ENTRY_HEADING_LOG_SUBMIT_START_DATE?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap width="20%"><?=tep_draw_input_field('trade_mail_log_start_date', $_SESSION['trade_mail_log_param']["trade_mail_log_start_date"], 'id="start_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.custom_product_criteria.start_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.custom_product_criteria.start_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
												<td class="main" width="12%"><?=ENTRY_HEADING_LOG_SUBMIT_END_DATE?></td>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('trade_mail_log_end_date', $_SESSION['trade_mail_log_param']["trade_mail_log_end_date"], 'id="end_date" size="16" maxlength="16" onblur="if (self.gfPop) { gfPop.validateUserInput(document.custom_product_criteria.end_date); }"') . '<a href="javascript:void(0)" onclick="if(self.gfPop)gfPop.fPopCalendar(document.custom_product_criteria.end_date);return false;" HIDEFOCUS><img name="popcal" align="absmiddle" src="includes/javascript/PopCalendarXp/calbtn.gif" width="34" height="22" border="0" alt=""></a>'?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main"><?=ENTRY_HEADING_LOG_ORDER_ID?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('trade_mail_log_order_id', $_SESSION['trade_mail_log_param']["trade_mail_log_order_id"], 'size="16" id="order_id" onKeyUP="if (trim_str(this.value)==\'\' || (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value))) ) { this.value = \'\'; } " onBlur="if (trim_str(this.value) != \'\' && !validateInteger(trim_str(this.value)) ) { this.value = \'\'; } " onKeyPress="return noEnterKey(event)"')?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td valign="top">
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top"><?=ENTRY_HEADING_LOG_ACCOUNT_NAME?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('trade_mail_log_account_name', $_SESSION['trade_mail_log_param']["trade_mail_log_account_name"], 'size="16"')?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '50', '1')?></td>
												<td class="main" valign="top"><?=ENTRY_HEADING_LOG_CHAR_NAME?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('trade_mail_log_char_name', $_SESSION['trade_mail_log_param']["trade_mail_log_char_name"])?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td valign="top">
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top"><?=ENTRY_HEADING_LOG_SERVER?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<?
													$server_select_array = array();
													$server_select_array[] = array('id' => '', 'text' => 'Not Selected');
													$server_select_array[] = array('id' => 1, 'text' => TEXT_US);
													$server_select_array[] = array('id' => 2, 'text' => TEXT_EU);
													
													$faction_select_array = array();
													$faction_select_array[] = array('id' => '', 'text' => 'Not Selected');
													$faction_select_array[] = array('id' => 1, 'text' => TEXT_ALLIANCE);
													$faction_select_array[] = array('id' => 2, 'text' => TEXT_HORDE);
												?>
												
												<td class="main" valign="top" nowrap><?=tep_draw_pull_down_menu('trade_mail_log_server', $server_select_array, $_SESSION['trade_mail_log_param']["trade_mail_log_server"])?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '60', '1')?></td>
												<td class="main" valign="top"><?=ENTRY_HEADING_LOG_REALM?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('trade_mail_log_realm', $_SESSION['trade_mail_log_param']["trade_mail_log_realm"])?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '50', '1')?></td>
												<td class="main" valign="top"><?=ENTRY_HEADING_LOG_FACTION?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
												<td class="main" valign="top" nowrap><?=tep_draw_pull_down_menu('trade_mail_log_faction', $faction_select_array, $_SESSION['trade_mail_log_param']["trade_mail_log_faction"])?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" width="43%"><?=ENTRY_HEADING_MAIL_TRADE?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<?
													$trade_mail_array = array();
													$trade_mail_array[] = array('id' => '', 'text' => 'Not Selected');
													$trade_mail_array[] = array('id' => 1, 'text' => TEXT_MAIL);
													$trade_mail_array[] = array('id' => 2, 'text' => TEXT_TRADE);
													
													$send_receive_array[] = array('id' => '', 'text' => 'Not Selected');
													$send_receive_array[] = array('id' => 1, 'text' => TEXT_SEND);
													$send_receive_array[] = array('id' => 2, 'text' => TEXT_RECEIVED);
												?>
												<td class="main" valign="top" nowrap><?=tep_draw_pull_down_menu('trade_mail_log_trade_mail', $trade_mail_array, $_SESSION['trade_mail_log_param']["trade_mail_log_trade_mail"])?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '60', '1')?></td>
												<td class="main" valign="top" nowrap><?=ENTRY_HEADING_SEND_RECEIVED?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
												<td class="main" valign="top" nowrap><?=tep_draw_pull_down_menu('trade_mail_log_send_receive', $send_receive_array, $_SESSION['trade_mail_log_param']["trade_mail_log_send_receive"])?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main"><?=ENTRY_HEADING_LOG_GOLD_ITEM?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<?
													$gold_item_array = array();
													$gold_item_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
													$gold_item_array[] = array('id' => 1, 'text' => TEXT_GOLD);
													$gold_item_array[] = array('id' => 2, 'text' => TEXT_ITEM);
												?>
												<td class="main" valign="top" nowrap><?=tep_draw_pull_down_menu('trade_mail_log_gold_item', $gold_item_array, $_SESSION['trade_mail_log_param']["trade_mail_log_gold_item"])?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main"><?=ENTRY_HEADING_LOG_PC_NAME?></td>
											</tr>
										</table>
									</td>
									<td>
										<table border="0" cellspacing="2" cellpadding="0">
											<tr>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('trade_mail_log_pc_name', $_SESSION['trade_mail_log_param']["trade_mail_log_pc_name"])?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '40', '1')?></td>
												<td class="main"><?=ENTRY_HEADING_LOG_PIC?></td>
												<td class="main"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
												<td class="main" valign="top" nowrap><?=tep_draw_input_field('trade_mail_log_pic', $_SESSION['trade_mail_log_param']["trade_mail_log_pic"], 'id="trade_mail_log_pic"')?></td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
								</tr>
								<tr>
									<td colspan="2" align="right">
										<?=tep_submit_button(IMAGE_BUTTON_SEARCH, IMAGE_BUTTON_SEARCH, " name=\"search\" id='search' onClick=\"return form_checking(this.form);\"", 'inputButton')?>
										<?=tep_submit_button(IMAGE_BUTTON_RESET, IMAGE_BUTTON_RESET, 'name="reset"', 'inputButton')?>
									</td>
								</tr>
								<tr>
									<td colspan="2"><?=tep_draw_separator('pixel_trans.gif', '1', '50')?></td>
								</tr>
							</table>
						</form>
					</td>
				</tr>
			</table>
<?	} ?>
<!-- body_eof //-->

<!-- footer //-->
<?php require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
	</body>
</html>

<script language="javascript"><!--
	function form_checking(form_obj) {
		var pic = trim_str(document.getElementById('trade_mail_log_pic').value);
		
		if (pic != '') {
			if (!validateEmail(pic)) {
				alert("The PIC email address is not a valid email address!");
				document.getElementById('trade_mail_log_pic').focus();
				document.getElementById('trade_mail_log_pic').select();
				return false;
			}
		}
		
	    form_obj.submit();
		return true;
	}
	
	
	function showOverEffect(object, class_name, extra_row) {
		rowOverEffect(object, class_name);
		var rowObjArray = extra_row.split('##');
	  	for (var i = 0; i < rowObjArray.length; i++) {
	  		if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
	  			rowOverEffect(document.getElementById(rowObjArray[i]), class_name);
	  		}
	  	}
	}
	
	function showOutEffect(object, class_name, extra_row) {
		rowOutEffect(object, class_name);
		var rowObjArray = extra_row.split('##');
		for (var i = 0; i < rowObjArray.length; i++) {
			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
				rowOutEffect(document.getElementById(rowObjArray[i]), class_name);
			}
		}
	}
	
	function showClicked(object, class_name, extra_row) {
		rowClicked(object, class_name);
		var rowObjArray = extra_row.split('##');
  		for (var i = 0; i < rowObjArray.length; i++) {
  			if (typeof (document.getElementById(rowObjArray[i])) != 'undefined' && document.getElementById(rowObjArray[i]) != null) {
  				rowClicked(document.getElementById(rowObjArray[i]), class_name);
  			}
		}
	}
//-->
</script>

<?php require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>