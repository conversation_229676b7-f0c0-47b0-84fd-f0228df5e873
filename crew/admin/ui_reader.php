<?
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/html');

$HTTP_SERVER_VARS = $_SERVER;
$HTTP_POST_VARS = $_POST;
$HTTP_GET_VARS = $_GET;
$HTTP_COOKIE_VARS = $_COOKIE;

include_once('includes/configure.php');
require_once(DIR_WS_FUNCTIONS . 'luaparser.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');

tep_db_connect() or die('Unable to connect to database server!');

if ($HTTP_POST_VARS['action'] == "post_contents" && tep_not_null($HTTP_POST_VARS['lua_logs_contents'])) {
	$data = lua_parse($HTTP_POST_VARS['lua_logs_contents']);
	
	if (isset($data['MailLogSetting'])) {
		$game_realm = "";
		$game_char_name = "";
		$game_char_race = "";
		$game_item_info_id_mail_send_list = "";
		$game_item_info_id_mail_receive_list = "";
		
		foreach ($data['MailLogSetting'] as $server => $realm_info_array) {
			
			if (isset($realm_info_array)) {
				foreach ($realm_info_array as $realm => $char_info_array) {
					$game_realm = $realm;
					
					if (isset($char_info_array)) {
						foreach ($char_info_array as $char_name => $char_info_detail_array) {
							$game_char_name = $char_name;
							$game_char_race = $char_info_detail_array['race'];
							$game_item_info_id_mail_send_list = '';
							
							if (isset($char_info_detail_array['Mail'])) {
								foreach ($char_info_detail_array['Mail'] as $mail_count => $mail_detail_array) {
									
									if (isset($mail_detail_array['ItemName'])) {
										$game_item_info_id_select_sql = "SELECT game_item_info_id FROM " . TABLE_GAME_ITEM_INFO . " WHERE game_item_info_name ='" . tep_db_input($mail_detail_array['ItemName']) . "'";
										$game_item_info_id_result_sql = tep_db_query($game_item_info_id_select_sql);
										$game_item_info_id_row = tep_db_fetch_array($game_item_info_id_result_sql);
										
										if (tep_not_null($game_item_info_id_row['game_item_info_id'])) {
											$game_item_send_info_id = $game_item_info_id_row['game_item_info_id'];
										} else {
											$sql_data_array = array('game_item_info_name' => $mail_detail_array['ItemName'],
																	'game_item_info_texture' => $mail_detail_array['ItemTexture']);
											
											tep_db_perform(TABLE_GAME_ITEM_INFO, $sql_data_array);
											
											$game_item_send_info_id = tep_db_insert_id();
										}
										$game_item_info_id_mail_send_list .= 's_' . $game_item_send_info_id . "_" . $mail_detail_array['ItemCount'] . ",";
										
										if ($game_item_info_id_mail_send_list{strlen($game_item_info_id_mail_send_list)-1} == ",") {
											$game_item_info_id_mail_send_list = substr($game_item_info_id_mail_send_list, 0, strlen($game_item_info_id_mail_send_list)-1);
										}
									}
									
									$sql_data_array = array('game_char_log_time' => $mail_detail_array['time'],
							                              	'game_char_log_account_name' => $HTTP_POST_VARS['account_name'],
							                              	'game_char_log_server' => $HTTP_POST_VARS['server'],
							                              	'game_char_log_realm' => $game_realm,
															'game_char_log_race' => $game_char_race,
							                              	'game_char_log_sender' => $game_char_name,
							                               	'game_char_log_receiver' => $mail_detail_array['Receiver'],
							                               	'game_char_log_subject' => $mail_detail_array['Subject'],
							                               	'game_char_log_messages' => $mail_detail_array['Message'],
							                               	'game_char_log_system_messages' => ((int)$mail_detail_array['COD'] > 0) ? "COD: " . $mail_detail_array['COD'] : "",
							                              	'game_char_log_balance_before' => $mail_detail_array['BalanceBefore'],
															'game_char_log_balance_after' => $mail_detail_array['BalanceAfter'],
															'game_char_log_send' => $mail_detail_array['Money'] . ':~:' . $game_item_info_id_mail_send_list,
							                              	'game_char_log_type' => 'mail',
							                              	'game_char_log_login_as' => $game_char_name,
															'game_char_log_computer_name' => $HTTP_POST_VARS['computer_name']);
									
							        tep_db_perform(TABLE_GAME_CHAR_LOG, $sql_data_array);
							        
							        $game_item_info_id_mail_send_list = '';
								}
							}
							
							if (isset($char_info_detail_array['Inbox'])) {
								foreach ($char_info_detail_array['Inbox'] as $inbox_mail_count => $inbox_mail_detail_array) {
									
									if (isset($inbox_mail_detail_array['ItemName'])) {
										$game_item_info_id_select_sql = "SELECT game_item_info_id FROM " . TABLE_GAME_ITEM_INFO . " WHERE game_item_info_name ='" . tep_db_input($inbox_mail_detail_array['ItemName']) . "'";
										$game_item_info_id_result_sql = tep_db_query($game_item_info_id_select_sql);
										$game_item_info_id_row = tep_db_fetch_array($game_item_info_id_result_sql);
									
										if (tep_not_null($game_item_info_id_row['game_item_info_id'])) {
											$game_item_receive_info_id = $game_item_info_id_row['game_item_info_id'];
										} else {
											$sql_data_array = array('game_item_info_name' => $inbox_mail_detail_array['ItemName'],
																	'game_item_info_texture' => $inbox_mail_detail_array['ItemTexture']);
																	
											tep_db_perform(TABLE_GAME_ITEM_INFO, $sql_data_array);
											
											$game_item_receive_info_id = tep_db_insert_id();
										}
										$game_item_info_id_mail_receive_list .= 'r_' . $game_item_receive_info_id . "_" . $inbox_mail_detail_array['ItemCount'] . ",";
										
										if ($game_item_info_id_mail_receive_list{strlen($game_item_info_id_mail_receive_list)-1} == ",") {
											$game_item_info_id_mail_receive_list = substr($game_item_info_id_mail_receive_list, 0, strlen($game_item_info_id_mail_receive_list)-1);
										}
									}
									
									$sql_data_array = array('game_char_log_time' => $inbox_mail_detail_array['time'],
							                              	'game_char_log_account_name' => $HTTP_POST_VARS['account_name'],
							                              	'game_char_log_server' => $HTTP_POST_VARS['server'],
							                              	'game_char_log_realm' => $game_realm,
															'game_char_log_race' => $game_char_race,
							                              	'game_char_log_sender' => $inbox_mail_detail_array['Sender'],
							                               	'game_char_log_receiver' => $game_char_name,
							                               	'game_char_log_subject' => $inbox_mail_detail_array['Subject'],
							                               	'game_char_log_messages' => $inbox_mail_detail_array['Message'],
							                               	'game_char_log_system_messages' => ((int)$inbox_mail_detail_array['COD'] > 0) ? "COD: " . $inbox_mail_detail_array['COD'] : "",
							                              	'game_char_log_balance_before' => $inbox_mail_detail_array['BalanceBefore'],
															'game_char_log_balance_after' => $inbox_mail_detail_array['BalanceAfter'],
															'game_char_log_receive' => $inbox_mail_detail_array['Money'] . ':~:' . $game_item_info_id_mail_receive_list,
							                              	'game_char_log_type' => 'mail',
							                              	'game_char_log_login_as' => $game_char_name,
															'game_char_log_computer_name' => $HTTP_POST_VARS['computer_name']);
							                              	
							        tep_db_perform(TABLE_GAME_CHAR_LOG, $sql_data_array);
							        
							        $game_item_info_id_mail_receive_list = '';
								}
							}
						}
					}
				}
			}
		}
	}
	
	
	if (isset($data['TradeLogSetting'])) {
		$game_server = "";
		$game_realm = "";
		$game_char_name = "";
		$game_char_race = "";
		$game_item_info_id_send_list = "";
		$game_item_info_id_receive_list = "";
		
		foreach ($data['TradeLogSetting'] as $server => $realm_info_array) {
			$game_server = $server;
			
			if (isset($realm_info_array)) {
				foreach ($realm_info_array as $realm => $char_info_array) {
					$game_realm = $realm;
					
					if (isset($char_info_array)) {
						foreach ($char_info_array as $char_name => $char_info_detail_array) {
							$game_char_name = $char_name;
							$game_char_race = $char_info_detail_array['race'];
							
							if (isset($char_info_detail_array['Trade'])) {
								foreach ($char_info_detail_array['Trade'] as $trade_count => $trade_detail_array) {
									if (isset($trade_detail_array['player_item_trade'])) {
										foreach ($trade_detail_array['player_item_trade'] as $trade_slot => $item_send_info_array) {
											
											$game_item_info_id_select_sql = "SELECT game_item_info_id FROM " . TABLE_GAME_ITEM_INFO . " WHERE game_item_info_name ='" . tep_db_input($item_send_info_array['Name']) . "'";
											$game_item_info_id_result_sql = tep_db_query($game_item_info_id_select_sql);
											$game_item_info_id_row = tep_db_fetch_array($game_item_info_id_result_sql);
											
											if (tep_not_null($game_item_info_id_row['game_item_info_id'])) {
												$game_item_send_info_id = $game_item_info_id_row['game_item_info_id'];
											} else {
												$sql_data_array = array('game_item_info_name' => $item_send_info_array['Name'],
																		'game_item_info_texture' => $item_send_info_array['Texture']);
																		
												tep_db_perform(TABLE_GAME_ITEM_INFO, $sql_data_array);
												
												$game_item_send_info_id = tep_db_insert_id();
											}
											$game_item_info_id_send_list .= 's_' . $game_item_send_info_id . "_" . $item_send_info_array['Numitems'] . ",";
										}
									}
									
									if (isset($trade_detail_array['target_item_trade'])) {
										foreach ($trade_detail_array['target_item_trade'] as $trade_slot => $item_receive_info_array) {
											
											$game_item_info_id_select_sql = "SELECT game_item_info_id FROM " . TABLE_GAME_ITEM_INFO . " WHERE game_item_info_name ='" . tep_db_input($item_receive_info_array['Name']) . "'";
											$game_item_info_id_result_sql = tep_db_query($game_item_info_id_select_sql);
											$game_item_info_id_row = tep_db_fetch_array($game_item_info_id_result_sql);
											
											if (tep_not_null($game_item_info_id_row['game_item_info_id'])) {
												$game_item_receive_info_id = $game_item_info_id_row['game_item_info_id'];
											} else {
												$sql_data_array = array('game_item_info_name' => $item_receive_info_array['Name'],
																		'game_item_info_texture' => $item_receive_info_array['Texture']);
																		
												tep_db_perform(TABLE_GAME_ITEM_INFO, $sql_data_array);
												
												$game_item_receive_info_id = tep_db_insert_id();
											}
											
											$game_item_info_id_receive_list .= 'r_' . $game_item_receive_info_id . "_" . $item_receive_info_array['Numitems'] . ",";
										}
										
									}
									
									if ($game_item_info_id_send_list{strlen($game_item_info_id_send_list)-1} == ",") {
										$game_item_info_id_send_list = substr($game_item_info_id_send_list, 0, strlen($game_item_info_id_send_list)-1);
									}
									
									if ($game_item_info_id_receive_list{strlen($game_item_info_id_receive_list)-1} == ",") {
										$game_item_info_id_receive_list = substr($game_item_info_id_receive_list, 0, strlen($game_item_info_id_receive_list)-1);
									}
									
									$sql_data_array = array('game_char_log_time' => $trade_detail_array['Time'],
							                              	'game_char_log_account_name' => $HTTP_POST_VARS['account_name'],
							                              	'game_char_log_server' => $game_server,
							                              	'game_char_log_realm' => $game_realm,
															'game_char_log_race' => $game_char_race,
							                              	'game_char_log_sender' => $trade_detail_array['player'],
							                               	'game_char_log_receiver' => $trade_detail_array['target'],
															'game_char_log_balance_before' => $trade_detail_array['BalanceBefore'],
															'game_char_log_balance_after' => $trade_detail_array['BalanceAfter'],
							                              	'game_char_log_send' => $trade_detail_array['player_money_trade'] . ':~:' . $game_item_info_id_send_list,
							                              	'game_char_log_receive' => $trade_detail_array['target_money_trade'] . ':~:' . $game_item_info_id_receive_list,
							                              	'game_char_log_type' => 'trade',
							                              	'game_char_log_login_as' => $trade_detail_array['player']);
							                              	
							        tep_db_perform(TABLE_GAME_CHAR_LOG, $sql_data_array);
							        
							        $game_item_info_id_send_list = "";
									$game_item_info_id_receive_list = "";
								}
							}
						}
					}
				}
			}
		}
	}
}

echo tep_draw_form('mail_trade_log', FILENAME_UI_READER, '', 'post', '');
echo tep_draw_hidden_field('action', 'post_contents');
echo tep_draw_textarea_field('lua_logs_contents', 'soft', '60', '15');
echo tep_draw_input_field('account_name');
echo tep_draw_input_field('computer_name');
echo tep_draw_input_field('server');
//echo tep_draw_input_field('login_user');


?>
</form>