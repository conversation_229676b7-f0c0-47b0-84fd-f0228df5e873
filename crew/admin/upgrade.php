<?
/*
  	$Id: upgrade.php,v 1.10 2005/06/16 12:19:21 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
require_once('includes/functions/upgrade.php');

tep_set_time_limit(0);

$upgrade_to = trim($_REQUEST[TEXT_VERSION_TITLE]);

function start_element($parser, $element_name, $element_attrs)
{
	global $store_version, $current_version, $current_child;
	
	$current_child = $element_name;
	
    if ($element_name == "VERSION") {
        $store_version[$element_attrs["ID"]] = array("id" => $element_attrs["ID"],
        						 "text" => TEXT_VERSION_TITLE . " " .$element_attrs["ID"]);
        $current_version = $element_attrs["ID"];
	}
}

function end_element($parser, $element_name)
{
    global $current_child, $current_version;
    
    if ($element_name == "VERSION")	$current_version = "";
    $current_child = "";
}

function character_data($parser,$data)
{
	global $store_version, $current_version, $current_child;
   	
   	switch ($current_child) {
   		case "DESCRIPTION" :
   			$store_version[$current_version][$current_child] = $data;
   		default:
   			break;
   	}
}

$dir_ok = false;
if (is_dir(DIR_FS_UPGRADE)) {
	if (is_readable(DIR_FS_UPGRADE)) {
		if (is_readable(DIR_FS_UPGRADE."version.xml")) {
			$store_version = array();
			$current_version = "";
			$current_child = "";
			
			//Initialize Parser
    		$parser=xml_parser_create();
    		//Specify Handlers to start and ending tag
    		xml_set_element_handler($parser, "start_element", "end_element");
    		//Data Handler
    		xml_set_character_data_handler($parser, "character_data");
    		//Read date
    		$fp=fopen(DIR_FS_UPGRADE."version.xml", "r") ;
			while ($data = fread($fp, 4096)) {
        		xml_parse($parser, $data, feof($fp)) or 
            		die (sprintf("XML Error: %s at line %d", 
                xml_error_string(xml_get_error_code($parser)),
                xml_get_current_line_number($parser)));
    		}
    		xml_parser_free($parser);
    		fclose($fp);
		} else {
			$messageStack->add(ERROR_UPGRADE_VERSION_NOT_FOUND, 'error');
		}
		$dir_ok = true;
    } else {
      $messageStack->add(ERROR_UPGRADE_DIRECTORY_NOT_WRITEABLE, 'error');
    }
} else {
    $messageStack->add(ERROR_UPGRADE_DIRECTORY_DOES_NOT_EXIST, 'error');
}
// here load the required upgrade file(s).
// check for refresh
if (tep_not_null($upgrade_to))
{
	if ($dir_ok) {
		$store_select_sql = "	SELECT configuration_value 
								FROM " . TABLE_CONFIGURATION . "
							 	WHERE configuration_key='STORE_VERSION'" ;
		$store_result_sql = tep_db_query($store_select_sql);
		if ($store_row_sql = tep_db_fetch_array($store_result_sql)) {
			if ($upgrade_to == $store_row_sql['configuration_value']) {
				$upgrade_ok = 0;
			} else {
				$upgrade_ok = first_version_greater($upgrade_to, $store_row_sql['configuration_value']) ? 1 : 0;
			}
			
			if ($upgrade_ok) {
				$tables_select_sql = " SHOW TABLES " ;
				$tables_result_sql = tep_db_query($tables_select_sql);
				while ($tables_row_sql = tep_db_fetch_row($tables_result_sql)) {
					$DBTables[] = $tables_row_sql[0];
				}
				
				$batch_upgrade_file = array();
				foreach (array_keys($store_version) as $version_id) {
					if ($version_id == $upgrade_to)
						break;
					if (first_version_greater($version_id, $store_row_sql['configuration_value']) &&
						first_version_greater($upgrade_to, $version_id) ) {
						$batch_upgrade_file[] = $version_id;
					}
				}
				$batch_upgrade_file[] = $upgrade_to;
				
				foreach ($batch_upgrade_file as $version_file) {
					$upgrade_to_version_id_array = explode(".", $version_file);
					$upgrade_filename = "version_".$upgrade_to_version_id_array[0]."/version_".implode("_", $upgrade_to_version_id_array).".php";
					$upgrade_file_path = DIR_FS_UPGRADE . $upgrade_filename;
					if (is_readable($upgrade_file_path)) {
						require($upgrade_file_path);
					} else {
						//$messageStack->add(ERROR_UPGRADED_FILE_NOT_FOUND, 'error');
					}
				}
				
				$store_update_sql = "	UPDATE " . TABLE_CONFIGURATION . "	
										SET configuration_value='".$upgrade_to."'
										WHERE configuration_key='STORE_VERSION'" ;
				tep_db_query($store_update_sql);
			}
		}
	} else {
    	$messageStack->add(ERROR_UPGRADE_DIRECTORY_DOES_NOT_EXIST, 'error');
	}
}

$store_select_sql = "	SELECT configuration_value 
						FROM " . TABLE_CONFIGURATION . "
						WHERE configuration_key='STORE_VERSION'" ;
$store_result_sql = tep_db_query($store_select_sql);
if ($store_row_sql = tep_db_fetch_array($store_result_sql)) 
	$latest_store_version = $store_row_sql["configuration_value"];
else
	$latest_store_version = STORE_VERSION;

$filtered_store_version = array();
$filtered_store_version = array( array("id" => '', "text" => "Select the version") );
foreach($store_version as $version_contents) {
	if (first_version_greater($version_contents["id"], $latest_store_version)) {
		$filtered_store_version[] = $version_contents;
	}
}

?>
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
	<title><?=TITLE?></title>
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
	<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
	<!-- header //-->
	<? require(DIR_WS_INCLUDES . 'header.php');?>
	<!-- header_eof //-->
	
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
					<!-- left_navigation //-->
					<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
					<!-- left_navigation_eof //-->
    			</table>
    		</td>
    		<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading" valign="top"><?=HEADING_TITLE?></td>
            						<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" align="center" width="50%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
                								<td class="dataTableHeadingContent"><?=TABLE_HEADING_CURRENT_VERSION?></td>
                								<td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_UPGRADE_VERSION?></td>
									    	</tr>
                							<tr class="dataTableRowSelected">
                								<td class="dataTableContent"><?=$latest_store_version?></td>
                								<td class="dataTableContent" align="center">
                								<?
                									echo tep_draw_form('goto', FILENAME_UPGRADE, '', 'post');
                									if (count($filtered_store_version)>1) {
			    										echo tep_draw_pull_down_menu(TEXT_VERSION_TITLE, $filtered_store_version, '',' id='.TEXT_VERSION_TITLE.' onChange="if (document.getElementById(\''.TEXT_VERSION_TITLE.'\').value) { if (confirm(\'Are you sure to upgrade your store now?\')) {this.form.submit();} else {document.getElementById(\''.TEXT_VERSION_TITLE.'\').value=\'\'; return false;} }"');
			    									} else {
			    										echo TEXT_INFO_NO_NEWEST_VERSION ;
			    									}
			    									echo "</form>";
												?>
                								</td>
									    	</tr>
									    	<?	ob_start(); ?>
									    			<tr>
                										<td colspan="2" align="center">
                											<?=SUCCESS_STORE_UPGRADED . $upgrade_to ."."?>
                										</td>
									    			</tr>
									   		<?	
									   			$upgrade_success_msg = ob_get_contents();
												ob_end_clean();
												
									    		if ($upgrade_ok) {
									    			echo $upgrade_success_msg;
									    		}
									    	?>
									    	<!--tr>
									    		<td >
									    			<a href=<?= "batch_processing.php?batch_action=insert&".SID?>><span style="color: purple;">Run Batch Insert</span></a><br>
									    			<a href=<?= "batch_processing.php?batch_action=update&".SID?>><span style="color: purple;">Updates Products</span></a><br>
									    			<a href=<?= "batch_processing.php?batch_action=merge&".SID?>><span style="color: purple;">Merge Customer Records</span></a>
									    		</td>
									    	</tr-->
                						</table>
                					</td>
                				</tr>
    						</table>
    					</td>
    				</tr>
    			</table>
    		</td>
    	</tr>
    </table>
    <!-- body_eof //-->
	<!-- footer //-->
	<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
	<!-- footer_eof //-->
<body>
</body>
</html>

<? require(DIR_WS_INCLUDES . 'application_bottom.php');?>
