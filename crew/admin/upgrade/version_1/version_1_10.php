<?
/*
	$Id: version_1_10.php,v 1.1 2005/11/22 08:30:16 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into data_pool_level_tags table (for min. and max. level)
$add_new_field = array();
$add_new_field[TABLE_DATA_POOL_LEVEL_TAGS] = array (array (	"field_name" => "data_pool_min_level",
															"field_attr" => " int(11) NOT NULL default '0' ",
															"add_after" => "data_pool_level_name"
									   					),
									   				array (	"field_name" => "data_pool_max_level",
															"field_attr" => " int(11) NOT NULL default '0' ",
															"add_after" => "data_pool_min_level"
									   						)
									  				);

$add_new_field[TABLE_DATA_POOL_LEVEL] = array (	array (	"field_name" => "data_pool_min_level",
														"field_attr" => " int(11) NOT NULL default '0' ",
														"add_after" => "data_pool_level_name"
													),
												array (	"field_name" => "data_pool_max_level",
														"field_attr" => " int(11) NOT NULL default '0' ",
														"add_after" => "data_pool_level_name"
									   					)
									  			);

add_field($add_new_field);
// End of insert new fields into data_pool_level_tags table (for min. and max. level)

// Create data_pool_options and data_pool_options_values tables
$add_new_tables = array();
$add_new_tables["data_pool_options_tags"] = array (	"structure" => " CREATE TABLE `data_pool_options_tags` (
																		`data_pool_options_id` int(11) NOT NULL auto_increment,
																		`data_pool_template_id` int(11) NOT NULL default '0',
																		`data_pool_options_title` varchar(255) NOT NULL default '',
																		`data_pool_options_input_type` varchar(32) NOT NULL default '1',
																		`data_pool_options_input_size` varchar(32) default '',
																		`data_pool_options_required` tinyint(1) NOT NULL default '0',
																		`data_pool_options_sort_order` int(5) NOT NULL default '50000',
																		PRIMARY KEY (`data_pool_options_id`)
																) TYPE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["data_pool_options_values_tags"] = array (	"structure" => " CREATE TABLE `data_pool_options_values_tags` (
																				`data_pool_options_values_id` int(11) NOT NULL default '0',
																				`data_pool_options_id` int(11) NOT NULL default '0',
																				`data_pool_options_value` varchar(255) default '',
																				`data_pool_options_values_price` decimal(15,4) NOT NULL default '0.0000',
																				`data_pool_options_values_eta` double(6,2) default NULL,
																				`data_pool_options_values_min_level` int(11) NOT NULL default '0',
																				`data_pool_options_values_max_level` int(11) NOT NULL default '0',
																				`data_pool_options_values_sort_order` int(5) NOT NULL default '50000',
																				PRIMARY KEY (`data_pool_options_values_id`, `data_pool_options_id`)
																		) TYPE=MyISAM;" ,
															"data" => ""
														);

$add_new_tables["data_pool_options"] = array (	"structure" => " CREATE TABLE `data_pool_options` (
																	`data_pool_options_id` int(11) NOT NULL auto_increment,
																	`products_id` int(11) NOT NULL default '0',
																	`data_pool_options_title` varchar(255) NOT NULL default '',
																	`data_pool_options_input_type` varchar(32) NOT NULL default '1',
																	`data_pool_options_input_size` varchar(32) default '',
																	`data_pool_options_required` tinyint(1) NOT NULL default '0',
																	`data_pool_options_sort_order` int(5) NOT NULL default '50000',
																	PRIMARY KEY (`data_pool_options_id`)
																) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["data_pool_options_values"] = array (	"structure" => " CREATE TABLE `data_pool_options_values` (
																			`data_pool_options_values_id` int(11) NOT NULL default '0',
																			`data_pool_options_id` int(11) NOT NULL default '0',
																			`data_pool_options_value` varchar(255) default '',
																			`data_pool_options_values_price` decimal(15,4) NOT NULL default '0.0000',
																			`data_pool_options_values_eta` double(6,2) default NULL,
																			`data_pool_options_values_min_level` int(11) NOT NULL default '0',
																			`data_pool_options_values_max_level` int(11) NOT NULL default '0',
																			`data_pool_options_values_sort_order` int(5) NOT NULL default '50000',
																			PRIMARY KEY (`data_pool_options_values_id`, `data_pool_options_id`)
																		) TYPE=MyISAM;" ,
														"data" => ""
													);
add_new_tables ($add_new_tables, $DBTables);
// End of create supplier related tables

// Insert new records into configuration table (Buyback Quantity Unit)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_QUANTITY_UNIT"] = array("insert" => " ('Quantity Unit', 'BUYBACK_QUANTITY_UNIT', '', 'The unit for the buyback quantity shown in buyback cart.', ".$row_sql["configuration_group_id"].", 66, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (Buyback Quantity Unit)
?>