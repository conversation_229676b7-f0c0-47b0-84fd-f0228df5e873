<?
/*
  	$Id: version_1_10_1.php,v 1.2 2005/12/06 09:41:06 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for worldpay credit card type selection)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_SHOW_CARD_TYPES"] = array( "insert" => " ('Show credit card types selection', 'MODULE_PAYMENT_WORLDPAY_SHOW_CARD_TYPES', 'False', 'Do you want to let customers select their credit card type at payment method selection page?', 6, 15, NULL, now(), NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),') " );
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_CARD_TYPES"] = array(	"insert" => " ('Credit Card Types', 'MODULE_PAYMENT_WORLDPAY_CARD_TYPES', '', 'Select which credit card types are accepted in this payment method. Those checked types will be available for select in checkout page.', 6, 16, NULL, now(), NULL, 'tep_cfg_key_select_multioption(array(\'Amex\'=>\'Amex\',\'VISA\'=>\'Visa\',\'VISP\'=>\'Visa Purchasing\',\'MSCD\'=>\'Master Card\',\'SWIT\'=>\'Switch\',\'SOLO\'=>\'Solo\',\'VISD\'=>\'Visa Delta\',\'VIED\'=>\'Visa Electron\',\'JCB\'=>\'JCB\'),') " );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for worldpay credit card type selection)

// Insert new records into configuration table (for payment email message)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_EMAIL_MESSAGE"] = array("insert" => " ('Payment Email Message', 'MODULE_PAYMENT_WORLDPAY_EMAIL_MESSAGE', 'Thank you for shopping at Offgamers.com.', 'This message will show up in order confirmation e-mail.', 6, 200, NULL, now(), NULL, 'tep_cfg_textarea(')" );
$conf_insert_sql["MODULE_PAYMENT_MONEYORDER_EMAIL_MESSAGE"] = array("insert" => " ('Payment Email Message', 'MODULE_PAYMENT_MONEYORDER_EMAIL_MESSAGE', 'Thank you for shopping at Offgamers.com.', 'This message will show up in order confirmation e-mail.', 6, 200, NULL, now(), NULL, 'tep_cfg_textarea(')" );
$conf_insert_sql["MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE"] = array("insert" => " ('Payment Email Message', 'MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE', 'Thank you for shopping at Offgamers.com.', 'This message will show up in order confirmation e-mail.', 6, 200, NULL, now(), NULL, 'tep_cfg_textarea(')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for payment email message)

// Insert new field into latest_news table (for summary news contents)
$add_new_field = array();

$add_new_field[TABLE_LATEST_NEWS] = array (	array (	"field_name" => "latest_news_summary",
								 					"field_attr" => " text ",
								 					"add_after" => "headline"
								 			   		)  
											);

$add_new_field[TABLE_CART_COMMENTS] = array (array ("field_name" => "cart_comments_type",
								 					"field_attr" => " tinyint(1) unsigned NOT NULL default '1' ",
								 					"add_after" => ""
								 			   		)  
											);

add_field ($add_new_field);
// End of insert new field into products table (for summary news contents)

// Create user_comments table
$add_new_tables = array();
$add_new_tables["user_comments"] = array(	"structure" => "CREATE TABLE `user_comments` (
																`user_comments_id` INT( 11 ) NOT NULL ,
																`user_id` INT( 11 ) NOT NULL ,
																`user_comments` TEXT,
																`user_role` VARCHAR( 16 ) NOT NULL ,
																PRIMARY KEY ( `user_comments_id` , `user_id`, `user_role`) 
															);" ,
											"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create user_comments table
?>