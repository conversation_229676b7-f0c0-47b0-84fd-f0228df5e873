<?
/*
  	$Id: version_1_10_2.php,v 1.3 2006/03/21 05:40:02 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin table (for batch update of products price)
$admin_files_insert_sql = array();
$admin_files_insert_sql["batch_update_prices.php"] = array	(	"insert" => " ('batch_update_prices.php', 0, 3, '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes=3, admin_groups_id='1' "
															);
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='batch_update_prices.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin table (for batch update of products price)

// Insert new records into configuration table (for package quantity used in batch update of products price)
$conf_insert_sql = array();
$conf_insert_sql["PACKAGE_QUANTITY_FOR_BATCH_UPDATE"] = array("insert" => " ('Price Update Package Quantity', 'PACKAGE_QUANTITY_FOR_BATCH_UPDATE', '', 'The quantities of the package(s) in which its prices need to be updated in batch update prices page.', 9, 100, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for package quantity used in batch update of products price)

// Create batch update of products price table and maxmind_history table
$add_new_tables = array();
$add_new_tables["batch_update_price_sets"] = array(	"structure" => "CREATE TABLE `batch_update_price_sets` (
																		`batch_update_price_sets_id` int(11) NOT NULL DEFAULT '0',
																		`batch_update_price_sets_name` varchar(128) NOT NULL default '',
																		`batch_update_price_sets_sort_order` int(5) NOT NULL default '0',
																		PRIMARY KEY  (`batch_update_price_sets_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["batch_update_price_sets_values"] = array(	"structure" => "CREATE TABLE `batch_update_price_sets_values` (
																				`batch_update_price_sets_id` int(11) NOT NULL DEFAULT '0',
																				`batch_update_qty` int(11) NOT NULL DEFAULT '0',
																				`batch_update_price` decimal(15,4) NOT NULL default '0.0000',
																				PRIMARY KEY  (`batch_update_price_sets_id`, `batch_update_qty`)
																			) TYPE=MyISAM;" ,
															"data" => ""
														);

$add_new_tables["categories_to_price_sets"] = array("structure" => "CREATE TABLE `categories_to_price_sets` (
  																		`categories_id` int(11) NOT NULL default '0',
  																		`batch_update_price_sets_id` int(11) NOT NULL default '0',
																		PRIMARY KEY  (`categories_id`,`batch_update_price_sets_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
													);

$add_new_tables["maxmind_history"] = array(	"structure" => "CREATE TABLE `maxmind_history` (
															  `orders_id` int(11) NOT NULL default '0',
															  `maxmind_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
															  `distance` int(11) NOT NULL default '0',
															  `country_match` varchar(9) NOT NULL default '',
															  `ip_country_code` char(2) NOT NULL default '',
															  `free_mail` varchar(9) NOT NULL default '',
															  `anonymous_proxy` varchar(9) NOT NULL default '',
															  `score` decimal(2,1) NOT NULL default '0.0',
															  `bin_match` varchar(9) NOT NULL default '',
															  `bin_country_code` varchar(9) NOT NULL default '',
															  `error` varchar(64) NOT NULL default '',
															  `proxy_score` decimal(4,1) NOT NULL default '0.0',
															  `spam_score` decimal(4,2) NOT NULL default '0.00',
															  `ip_region` varchar(64) NOT NULL default '',
															  `ip_city` varchar(64) NOT NULL default '',
															  `ip_latitude` decimal(5,4) NOT NULL default '0.0000',
															  `ip_longitude` decimal(5,4) NOT NULL default '0.0000',
															  `bin_name` varchar(64) NOT NULL default '',
															  `ip_isp` varchar(64) NOT NULL default '',
															  `ip_organization` varchar(64) NOT NULL default '',
															  `bin_name_match` varchar(9) NOT NULL default '',
															  `bin_phone_match` varchar(9) NOT NULL default '',
															  `bin_phone` varchar(9) NOT NULL default '',
															  `customer_phone_in_billing_location` varchar(9) NOT NULL default '',
															  `high_risk_country` char(3) NOT NULL default '',
															  `queries_remaining` varchar(64) NOT NULL default '',
															  `city_postal_match` varchar(9) NOT NULL default '',
															  `shipping_city_postal_match` varchar(9) NOT NULL default '',
															  `maxmind_history_source` varchar(50) NOT NULL default '',
															  `maxmind_id` varchar(64) NOT NULL default '',
															  PRIMARY KEY  (`orders_id`,`maxmind_history_date`)
															) TYPE=MyISAM;" ,
											"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create batch update of products price table and maxmind_history table

// Insert new records into configuration table (for payment's order considered confirmed order)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_PAYPAL_CONFIRM_COMPLETE"] = array("insert" => " ('Confirm Complete (in days)', 'MODULE_PAYMENT_PAYPAL_CONFIRM_COMPLETE', '0', 'The number of days in which an order made by this payment method is confirm completed', 6, 210, NULL, now(), NULL, NULL)" );
$conf_insert_sql["MODULE_PAYMENT_MONEYORDER_CONFIRM_COMPLETE"] = array("insert" => " ('Confirm Complete (in days)', 'MODULE_PAYMENT_MONEYORDER_CONFIRM_COMPLETE', '0', 'The number of days in which an order made by this payment method is confirm completed', 6, 210, NULL, now(), NULL, NULL)" );
$conf_insert_sql["MODULE_PAYMENT_2CHECKOUT_CONFIRM_COMPLETE"] = array("insert" => " ('Confirm Complete (in days)', 'MODULE_PAYMENT_2CHECKOUT_CONFIRM_COMPLETE', '0', 'The number of days in which an order made by this payment method is confirm completed', 6, 210, NULL, now(), NULL, NULL)" );
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_CONFIRM_COMPLETE"] = array("insert" => " ('Confirm Complete (in days)', 'MODULE_PAYMENT_WORLDPAY_CONFIRM_COMPLETE', '0', 'The number of days in which an order made by this payment method is confirm completed', 6, 210, NULL, now(), NULL, NULL)" );

$conf_insert_sql["MODULE_PAYMENT_CASHU_CONFIRM_COMPLETE"] = array("insert" => " ('Confirm Complete (in days)', 'MODULE_PAYMENT_CASHU_CONFIRM_COMPLETE', '0', 'The number of days in which an order made by this payment method is confirm completed', 6, 655, NULL, now(), NULL, NULL)" );
$conf_insert_sql["MODULE_PAYMENT_MOORCHECK_CONFIRM_COMPLETE"] = array("insert" => " ('Confirm Complete (in days)', 'MODULE_PAYMENT_MOORCHECK_CONFIRM_COMPLETE', '0', 'The number of days in which an order made by this payment method is confirm completed', 6, 760, NULL, now(), NULL, NULL)" );
$conf_insert_sql["MODULE_PAYMENT_WU_CONFIRM_COMPLETE"] = array("insert" => " ('Confirm Complete (in days)', 'MODULE_PAYMENT_WU_CONFIRM_COMPLETE', '0', 'The number of days in which an order made by this payment method is confirm completed', 6, 585, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for payment's order considered confirmed order)
?>