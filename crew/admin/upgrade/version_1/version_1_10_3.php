<?
/*
  	$Id: version_1_10_3.php,v 1.2 2006/03/21 05:40:19 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin table (for maxmind history used in order lists)
$admin_files_insert_sql = array();
$admin_files_insert_sql["maxmind_history.php"] = array	(	"insert" => " ('maxmind_history.php', 0, 152, '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes=152, admin_groups_id='1' "
														);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='maxmind_history.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin table (for maxmind history used in order lists)

// Insert new records into configuration table (for product name and category used in batch update of products price)
$conf_insert_sql = array();
$conf_insert_sql["PRODUCT_NAME_FOR_BATCH_UPDATE"] = array("insert" => " ('Price Update Product Name', 'PRODUCT_NAME_FOR_BATCH_UPDATE', '', 'The name of the subproduct in the package(s).', 9, 110, NULL, now(), NULL, NULL)" );
$conf_insert_sql["CATEGORY_FOR_BATCH_UPDATE"] = array("insert" => " ('Price Update Main Category', 'CATEGORY_FOR_BATCH_UPDATE', '', 'The main category that contains all the package(s) to be updated.', 9, 120, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for product name and category used in batch update of products price)
?>