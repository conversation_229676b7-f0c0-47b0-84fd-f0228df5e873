<?
/*
  	$Id: version_1_10_4.php,v 1.3 2005/12/22 05:45:38 weichen Exp $
	
  	Developer: <PERSON> (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Change field structure for brackets_key in brackets table and brackets_tags_key in brackets_tags table
$change_field_structure = array();
$change_field_structure[TABLE_BRACKETS] = array (array ("field_name" => "brackets_key",
									 					"field_attr" => " varchar(255) NOT NULL default '' "
										 				)
												);

$change_field_structure[TABLE_BRACKETS_TAGS] = array (array (	"field_name" => "brackets_tags_key",
									 							"field_attr" => " varchar(255) NOT NULL default '' "
										 					)
													);

change_field_structure ($change_field_structure);
// End of change field structure for brackets_key in brackets table and brackets_tags_key in brackets_tags table
?>