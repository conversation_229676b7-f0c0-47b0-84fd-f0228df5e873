<?
/*
	$Id: version_1_10_5.php,v 1.2 2005/12/28 06:13:54 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for WorldPay callback password)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_CALLBACK_PASSWORD"] = array("insert" => " ('Callback Password', 'MODULE_PAYMENT_WORLDPAY_CALLBACK_PASSWORD', '', 'Callback password used in callback script for security check purpose. Must also be entered into Worldpay installation config.', 6, 215, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for WorldPay callback password)


// <PERSON>'s changes
// Drop existing primary key
$existing_user_comments_fields = get_table_fields(TABLE_USER_COMMENTS);
if (!in_array('user_comments_auto_id', $existing_user_comments_fields)) {
	drop_index_key (TABLE_USER_COMMENTS, 'PRIMARY KEY', 'primary', $DBTables);
}
// End of drop existing primary key

// Insert new primary key field into user_comments table
$add_new_field = array();
$add_new_field[TABLE_USER_COMMENTS] = array (array (	"field_name" => "user_comments_auto_id",
										 				"field_attr" => " INT( 11 ) NOT NULL AUTO_INCREMENT PRIMARY KEY FIRST ",
										 				"add_after" => ""
										 				)
											  );

add_field ($add_new_field, false);
// End of insert new primary key field into user_comments table

// End of Chan's changes
?>