<?
/*
	$Id: version_1_10_6.php,v 1.1 2005/12/30 09:19:34 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define orders_id as unique key in orders_status_history
add_index_key (TABLE_ORDERS_STATUS_HISTORY, 'index_order_id_and_status', 'index', 'orders_id, orders_status_id', $DBTables);

add_index_key (TABLE_ORDERS_COMMENTS, 'index_orders_comments_status', 'index', 'orders_comments_status', $DBTables);

add_index_key (TABLE_SEARCH_CRITERIA, 'index_filename', 'index', 'filename', $DBTables);
// End of define orders_id as unique key in orders_status_history

// Insert new field into categories table
$add_new_field = array();
$add_new_field[TABLE_CATEGORIES] = array (array (	"field_name" => "products_count",
									 				"field_attr" => " int(11) unsigned NOT NULL default '0' "
								 				)
									  );

add_field ($add_new_field);
// End of insert new field into categories table
?>