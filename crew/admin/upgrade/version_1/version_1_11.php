<?
/*
  	$Id: version_1_11.php,v 1.3 2006/03/21 05:40:50 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define orders_id and products id as index key in orders_products table
add_index_key (TABLE_ORDERS_PRODUCTS, 'index_order_id_and_products_id', 'index', 'orders_id, products_id', $DBTables);
// End of define orders_id and products id as index key in orders_products table

// Change field structure for customers_country in orders table
$change_field_structure = array();
$change_field_structure[TABLE_ORDERS] = array (array (	"field_name" => "customers_country",
									 					"field_attr" => " varchar(64) NOT NULL default '' "
										 			)
												);

$change_field_structure[TABLE_ORDERS_PRODUCTS] = array (array (	"field_name" => "products_name",
											 					"field_attr" => " varchar(128) NOT NULL default '' "
												 			)
														);
change_field_structure ($change_field_structure);
// End of change field structure for customers_country in orders table

// Insert new records into configuration table (for back order tag name and purchase team e-mail)
$conf_insert_sql = array();
$conf_insert_sql["BACK_ORDER_TAG_NAME"] = array("insert" => " ('Back Order Tag Name', 'BACK_ORDER_TAG_NAME', '', 'The name of the order tag in Processing status used to indicate back order. (Case insensitive)', 9, 90, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for back order tag name and purchase team e-mail)

// Insert new records into configuration table (for buy back module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["PURCHASE_TEAM_EMAIL"] = array("insert" => " ('Purchase Team Email Address', 'PURCHASE_TEAM_EMAIL', '', 'Email address to which the email will be send to whenever supplier submit the order list.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', '".$row_sql["configuration_group_id"]."', 15, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for buy back module)

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for supplier module)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["suppliers_groups.php"] = array	(	"insert" => " ('suppliers_groups.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   										);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='suppliers_groups.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["suppliers.php"] = array(	"insert" => " ('suppliers.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   								);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='suppliers.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["suppliers_pricing.php"] = array(	"insert" => " ('suppliers_pricing.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   										);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='suppliers_pricing.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["suppliers_order_track.php"] = array(	"insert" => " ('suppliers_order_track.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   											);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='suppliers_order_track.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["suppliers_orders.php"] = array("insert" => " ('suppliers_orders.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   										);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='suppliers_orders.php' AND admin_files_is_boxes=0 ");
	
	// End of insert new records into admin_files table (for supplier module)
}

// Delete supplier_time_setting table
$delete_tables_array = array("supplier_time_setting");
delete_tables ($delete_tables_array, $DBTables);
// End of delete supplier_time_setting table

// Create suppliers module related tables
$add_new_tables = array();
$add_new_tables["supplier_groups"] = array(	"structure" => "CREATE TABLE `supplier_groups` (
															  `supplier_groups_id` int(11) NOT NULL auto_increment,
															  `supplier_groups_name` varchar(64) DEFAULT NULL,
															  `supplier_groups_status` tinyint(1) unsigned NOT NULL DEFAULT '0',
															  `show_products_purchase_demand_status` tinyint(1) unsigned NOT NULL DEFAULT '1',
															  PRIMARY KEY (`supplier_groups_id`),
															  UNIQUE KEY `supplier_groups_name` (`supplier_groups_name`)
															) TYPE=MyISAM;" ,
											"data" => ""
											);

$add_new_tables["supplier_list_time_setting"] = array(	"structure" => "CREATE TABLE `supplier_list_time_setting` (
																			`supplier_groups_id` int(11) NOT NULL DEFAULT '0',
																		  	`normal_status` varchar(20) NOT NULL default 'STATUS_OFF',
																		  	`first_list_start_time` time NOT NULL default '00:00:00',
																		  	`first_list_end_time` time NOT NULL default '00:00:00',
																		  	`second_list_start_time` time NOT NULL default '00:00:00',
																		  	`second_list_end_time` time NOT NULL default '00:00:00',
																		  	`auto_on` smallint(6) NOT NULL default '1',
																		  	`auto_off` smallint(6) NOT NULL default '0',
																		  	`current_status` varchar(20) NOT NULL default 'STATUS_OFF',
																		  	PRIMARY KEY (`supplier_groups_id`)
																		) TYPE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables["supplier_pricing_setting"] = array (	"structure" => " CREATE TABLE `supplier_pricing_setting` (
																		  `supplier_groups_id` int(11) NOT NULL default '0',
																		  `supplier_pricing_setting_key` varchar(255) NOT NULL default '',
																		  `supplier_pricing_setting_value` varchar(255) NOT NULL default '',
																		  PRIMARY KEY  (`supplier_groups_id`, `supplier_pricing_setting_key`)
																	) TYPE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables["supplier_order_lists"] = array (	"structure" => " CREATE TABLE `supplier_order_lists` (
																		  `supplier_order_lists_id` int(11) NOT NULL auto_increment,
																		  `suppliers_id` int(11) NOT NULL default '0',
																		  `suppliers_name` varchar(64) NOT NULL default '',
																		  `suppliers_street_address` varchar(64) NOT NULL default '',
																		  `suppliers_suburb` varchar(64) default NULL,
																		  `suppliers_city` varchar(32) NOT NULL default '',
																		  `suppliers_postcode` varchar(10) NOT NULL default '',
																		  `suppliers_state` varchar(32) default NULL,
																		  `suppliers_country` varchar(64) NOT NULL default '',  
																		  `suppliers_telephone` varchar(32) NOT NULL default '',
																		  `suppliers_email_address` varchar(96) NOT NULL default '',
																		  `supplier_order_lists_date` datetime NOT NULL default '0000-00-00 00:00:00',
																		  `supplier_order_lists_last_modified` datetime default NULL,
																		  `supplier_order_lists_status` smallint(1) NOT NULL default '5',
																		  `remote_addr` varchar(20) NOT NULL default '',
																		  `currency` char(3) default NULL,
																		  `currency_value` decimal(14,6) default NULL,
																		  PRIMARY KEY  (`supplier_order_lists_id`),
																		  KEY `index_suppliers_id` (`suppliers_id`)
																	) TYPE=MyISAM AUTO_INCREMENT=1000;" ,
													"data" => ""
												);

$add_new_tables["supplier_order_lists_products"] = array (	"structure" => " CREATE TABLE `supplier_order_lists_products` (
																				  `supplier_order_lists_products_id` int(11) NOT NULL auto_increment,
																				  `supplier_order_lists_id` int(11) NOT NULL default '0',
																				  `supplier_order_lists_type` tinyint(1) NOT NULL default '1',
																				  `products_id` int(11) NOT NULL default '0',
																				  `products_name` varchar(128) NOT NULL default '',
																				  `products_display_name` varchar(255) NOT NULL default '',
																				  `products_restock_comment` text,
																				  `products_purchase_status` varchar(20) NOT NULL default '',
																				  `first_max_quantity` int(11) NOT NULL default '0',
																				  `first_max_unit_price` double NOT NULL default '0',
																				  `second_max_quantity` int(11) NOT NULL default '0',
																				  `second_max_unit_price` double NOT NULL default '0',
																				  `products_quantity` int(11) NOT NULL default '0',
																				  `products_provision_amount` decimal(15,4) NOT NULL default '0.0000',
																				  `products_received_quantity` int(11) default NULL,
																				  `supplier_order_lists_products_comment` text,
																				  PRIMARY KEY  (`supplier_order_lists_products_id`),
																				  KEY `index_lists_id_and_type_and_products_id` (`supplier_order_lists_id`, `supplier_order_lists_type`, `products_id`)
																			) TYPE=MyISAM ;" ,
															"data" => ""
														);

$add_new_tables["supplier_order_lists_history"] = array (	"structure" => " CREATE TABLE `supplier_order_lists_history` (
																				`supplier_order_lists_history_id` int(11) NOT NULL auto_increment,
																				`supplier_order_lists_id` int(11) NOT NULL default '0',
																				`supplier_order_lists_status` smallint(1) NOT NULL default '0',
																				`date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																				`supplier_notified` tinyint(1) NOT NULL default '0',
																				`comments` text,
																				`set_as_order_list_remarks` tinyint(1) NOT NULL default '0',
																				`changed_by` varchar(128) NOT NULL default '',
																				PRIMARY KEY  (`supplier_order_lists_history_id`),
																				KEY `index_lists_id_and_lists_status` (`supplier_order_lists_id`,`supplier_order_lists_status`)
																			) TYPE=MyISAM ;" ,
															"data" => ""
														);

$add_new_tables["supplier_list_status"] = array (	"structure" => " CREATE TABLE `supplier_list_status` (
																		`supplier_list_status_id` int(11) NOT NULL default '0',
																		`language_id` int(11) NOT NULL default '1',
																		`supplier_list_status_name` varchar(32) NOT NULL default '',
																		`supplier_list_status_sort_order` int(5) NOT NULL default '50000',
																		PRIMARY KEY  (`supplier_list_status_id`,`language_id`),
																		KEY `idx_supplier_list_status_name` (`supplier_list_status_name`)
																	) TYPE=MyISAM;" ,
													"data" => "	INSERT INTO `supplier_list_status` (`supplier_list_status_id`, `language_id`, `supplier_list_status_name`, `supplier_list_status_sort_order`) 
																VALUES (1, 1, 'Pending', 20),
																	(2, 1, 'Processing', 30),
																	(3, 1, 'Completed', 40),
																	(4, 1, 'Canceled', 50),
																	(5, 1, 'Draft', 10),
																	(1, 2, '&#23457;&#26680;&#20013;', 20),
																	(2, 2, '&#22788;&#29702;', 30),
																	(3, 2, '&#23436;&#25104;', 40),
																	(4, 2, '&#21462;&#28040;', 50),
																	(5, 2, '&#36215;&#33609;', 10);"
												);

$add_new_tables["supplier_languages"] = array (	"structure" => " CREATE TABLE `supplier_languages` (
																  `languages_id` int(11) NOT NULL auto_increment,
																  `name` varchar(255) NOT NULL default '',
																  `code` char(2) NOT NULL default '',
																  `image` varchar(64) default NULL,
																  `directory` varchar(32) default NULL,
																  `sort_order` int(3) default NULL,
																  PRIMARY KEY  (`languages_id`),
																  KEY `IDX_LANGUAGES_NAME` (`name`)
																) ENGINE=MyISAM ;" ,
													"data" => "	INSERT INTO `supplier_languages` (`languages_id`, `name`, `code`, `image`, `directory`, `sort_order`) 
																VALUES (1, 'English', 'en', 'icon.gif', 'english', 1),
																	(2, '&#20013;&#25991;&#65288;&#31616;&#20307;&#65289;', 'zh', 'icon.gif', 'chinese', 2);"
												);

$add_new_tables["supplier_preferences"] = array (	"structure" => " CREATE TABLE `supplier_preferences` (
																	  `suppliers_id` int(11) NOT NULL default '0',
																	  `supplier_preferences_key` varchar(255) NOT NULL default '',
																	  `supplier_preferences_value` varchar(255) NOT NULL default '',
																	  PRIMARY KEY  (`suppliers_id`,`supplier_preferences_key`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create suppliers module related tables

// Insert new field into supplier table
$add_new_field = array();
$add_new_field[TABLE_SUPPLIER] = array (array (	"field_name" => "supplier_code",
								 				"field_attr" => " varchar(64) DEFAULT NULL ",
								 				"add_after" => "supplier_id"
								 				),
										array (	"field_name" => "supplier_groups_id",
								 				"field_attr" => " int(11) NOT NULL DEFAULT '0' ",
								 				"add_after" => "supplier_code"
								 				),
								 		array (	"field_name" => "supplier_purchase_mode",
								 				"field_attr" => " varchar(20) NOT NULL default 'STATUS_GROUP' ",
								 				"add_after" => "supplier_date_account_last_modified"
								 				),
								 		array (	"field_name" => "supplier_deduct_from_order_id",
								 				"field_attr" => " int(11) NOT NULL default '' ",
								 				"add_after" => ""
								 				),
								 		array (	"field_name" => "supplier_payment_bank_address",
								 				"field_attr" => " varchar(255) NOT NULL default '' ",
								 				"add_after" => "supplier_payment_bank_swift_code"
								 				),
								 		array (	"field_name" => "supplier_payment_bank_telephone",
								 				"field_attr" => " varchar(32) NOT NULL default '' ",
								 				"add_after" => "supplier_payment_bank_address"
								 				)
									  );

$add_new_field[TABLE_BUYBACK_BASKET] = array (array (	"field_name" => "buyback_basket_list_type",
								 						"field_attr" => " smallint(1) default '0' ",
								 						"add_after" => "buyback_basket_user_type"
								 					)
									  		);

$add_new_field[TABLE_BUYBACK_STATUS] = array (	array (	"field_name" => "buyback_status_sort_order",
									 					"field_attr" => " int(5) NOT NULL default '50000' ",
									 					"add_after" => ""
									 					)
										  );

$add_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_buyback_quantity",
								 					"field_attr" => " varchar(255) NOT NULL default '0' ",
								 					"add_after" => ""
								 					),
								 			array (	"field_name" => "products_buyback_price",
								 					"field_attr" => " decimal(15,4) NOT NULL default '0.0000' ",
								 					"add_after" => "products_buyback_quantity"
								 					)
									  );

add_field ($add_new_field);
// End of insert new field into supplier table

// Define supplier_code as unique key
add_index_key (TABLE_SUPPLIER, 'unique_supplier_code', 'unique', 'supplier_code', $DBTables);
// End of define supplier_code as unique key

// Delete supplier_pricing table
$existing_supplier_pricing_fields = get_table_fields("supplier_pricing");
if (in_array('supplier_pricing_id', $existing_supplier_pricing_fields)) {
	$delete_tables_array = array("supplier_pricing");
	delete_tables ($delete_tables_array, $DBTables);
	
	$add_new_tables = array();
	$add_new_tables["supplier_pricing"] = array (	"structure" => " CREATE TABLE supplier_pricing (
																	  `supplier_groups_id` int(11) NOT NULL default '0',
																	  `products_id` int(11) NOT NULL default '0',
																	  `supplier_pricing_min_quantity` int(11) NOT NULL default '0',
																	  `supplier_pricing_max_quantity` int(11) NOT NULL default '0',
																	  `supplier_pricing_unit_price` double NOT NULL default '0',
																	  `supplier_pricing_disabled` smallint(1) NOT NULL default '0',
																	  `supplier_pricing_server_full` smallint(1) NOT NULL default '0',
																	  `supplier_pricing_product_status` varchar(20) NOT NULL default '',
																	  `supplier_pricing_comment` text,
																	  PRIMARY KEY  (`supplier_groups_id`, `products_id`)
																) TYPE=MyISAM;" ,
													"data" => ""
												);
	add_new_tables ($add_new_tables, $DBTables);
}
// End of delete supplier_pricing table

// Delete supplier_pricing_min_quantity field from supplier_pricing table
$delete_field = array();
$delete_field["supplier_pricing"] = array  (	array( "field_name" => "supplier_pricing_min_quantity") );

delete_field ($delete_field);
// End of delete supplier_pricing_min_quantity field from supplier_pricing table

// Insert new field into supplier_pricing table
$add_new_field = array();

$add_new_field["supplier_pricing"] = array (array (	"field_name" => "supplier_pricing_show_comment",
								 					"field_attr" => " tinyint(1) NOT NULL default '0' ",
								 					"add_after" => ""
								 					)
									  		);

add_field ($add_new_field);
// End of insert new field into supplier_pricing table

// Insert new records into admin_files_actions table (for permission on editing restock account in supplier pricing page, view payment info in supplier order page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers_pricing.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_RESTOCK_ACCOUNT_INFO"] = array("insert" => " ('SUPPLIER_RESTOCK_ACCOUNT_INFO', 'Restock Account Details', ".$row_sql["admin_files_id"].", '1', 5)" );
	$admin_files_actions_insert_sql["SUPPLIER_BUYBACK_PRICE_INFO"] = array("insert" => " ('SUPPLIER_BUYBACK_PRICE_INFO', 'View products average buyback price', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}

$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers_orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_ORDER_PAYMENT_INFO"] = array("insert" => " ('SUPPLIER_ORDER_PAYMENT_INFO', 'Payment Details', ".$row_sql["admin_files_id"].", '1', 5)" );
	$admin_files_actions_insert_sql["REVERSIBLE_SUPPLIER_ORDER_STATUS"] = array("insert" => " ('REVERSIBLE_SUPPLIER_ORDER_STATUS', 'Reverse supplier order status', ".$row_sql["admin_files_id"].", '1', 15)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
	
	
	// Update records in admin_files_actions table (associate "Able to view supplier information" to suppliers_orders.php)
	$admin_file_actions_update_sql = array();
	$admin_file_actions_update_sql["SUPPLER_INFO_VIEWING"] = array("update" => " admin_files_actions_name='Supplier Details', admin_files_id='".$row_sql["admin_files_id"]."'");
	
	update_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_file_actions_update_sql, $DBTables);
	// End of update records in admin_files_actions table (associate "Able to view supplier information" to suppliers_orders.php)
}
// End of insert new records into admin_files_actions table (for permission on editing restock account in supplier pricing page, view payment info in supplier order page)

// Update records in buyback_status table (assign sort order to each status)
$buyback_status_update_sql = array();
$buyback_status_update_sql["1"] = array("update" => " buyback_status_sort_order='10' " );
$buyback_status_update_sql["2"] = array("update" => " buyback_status_sort_order='20' " );
$buyback_status_update_sql["3"] = array("update" => " buyback_status_sort_order='30' " );
$buyback_status_update_sql["4"] = array("update" => " buyback_status_sort_order='40' " );

update_records(TABLE_BUYBACK_STATUS, "buyback_status_id", $buyback_status_update_sql, $DBTables);
// End of update records in buyback_status table (assign sort order to each status)

// Chan's changes
// Insert new field into customers table
$add_new_field = array();

$add_new_field[TABLE_CUSTOMERS] = array (array ("field_name" => "email_verified",
								 				"field_attr" => " tinyint(1) NOT NULL default '0' ",
								 				"add_after" => "customers_email_address"
								 				),
										array (	"field_name" => "account_activated",
								 				"field_attr" => " tinyint(1) NOT NULL default '1' ",
								 				"add_after" => "customers_dob"
								 				),
								 		array (	"field_name" => "serial_number",
								 				"field_attr" => " VARCHAR(12) NOT NULL default '' ",
								 				"add_after" => "email_verified"
								 				)
									  );

add_field ($add_new_field);
// End of insert new field into customers table

// Change field structure for account_activated in customers table
$change_field_structure = array();
$change_field_structure[TABLE_CUSTOMERS] = array (array (	"field_name" => "account_activated",
									 						"field_attr" => " tinyint(1) NOT NULL default '0' "
										 				)
													);

change_field_structure ($change_field_structure);
// End of change field structure for account_activated in customers table
?>