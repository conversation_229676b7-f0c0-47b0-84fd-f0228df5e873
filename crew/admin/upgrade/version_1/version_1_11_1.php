<?
/*
  	$Id: version_1_11_1.php,v 1.2 2006/03/21 05:42:59 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for supplier module)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["suppliers_report.php"] = array	(	"insert" => " ('suppliers_report.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   										);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='suppliers_report.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["products_purchase_quantity.php"] = array	(	"insert" => " ('products_purchase_quantity.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																			"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   													);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='products_purchase_quantity.php' AND admin_files_is_boxes=0 ");
	
	// End of insert new records into admin_files table (for supplier module)
}

// Create supplier_products_pricing table
$add_new_tables = array();
$add_new_tables["products_purchases"] = array(	"structure" => "CREATE TABLE `products_purchases` (
																  `products_id` int(11) NOT NULL default '0',
																  `products_purchases_max_quantity` int(11) NOT NULL default '0',
																  `products_purchases_suggest_quantity` int(11) NOT NULL default '0',
																  `products_purchases_selling_quantity` int(11) NOT NULL default '0',
																  `products_purchases_quantity_overwrite` tinyint(1) NOT NULL default '0',
																  `products_purchases_disabled` tinyint(1) NOT NULL default '0',
																  PRIMARY KEY  (`products_id`)
																) TYPE=MyISAM;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create supplier_products_pricing table
?>