<?
/*
  	$Id: version_1_11_10.php,v 1.2 2006/03/21 05:43:09 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require_once(DIR_WS_CLASSES . 'currencies.php');

if (!is_object($currencies)){
	$currencies = new currencies();
	$currencies->set_decimal_places(3);
}

if (!in_array('supplier_payments', $DBTables)) {
	$group_uncategories_payment_orders = true;
} else {
	$group_uncategories_payment_orders = false;
}

// Define  supplier_order_lists_status as index key in supplier_order_lists table
add_index_key (TABLE_SUPPLIER_ORDER_LISTS, 'index_supplier_order_lists_status', 'index', ' supplier_order_lists_status', $DBTables);
// End of define  supplier_order_lists_status as index key in supplier_order_lists table

// Create suppliers payment related tables
$add_new_tables = array();

$add_new_tables["supplier_payments"] = array(	"structure" => "CREATE TABLE `supplier_payments` (
																  	`supplier_payments_id` int(11) NOT NULL auto_increment,
																  	`suppliers_id` int(11) NOT NULL default '0',
																  	`supplier_payments_amount` decimal(15,4) NOT NULL default '0.00',
																  	`supplier_payments_tax` decimal(15,4) NOT NULL default '0.00',
																  	`supplier_payments_total` decimal(15,4) NOT NULL default '0.00',
																  	`supplier_payments_date` datetime NOT NULL default '0000-00-00 00:00:00',
																  	`supplier_payments_last_modified` datetime NOT NULL default '0000-00-00 00:00:00',
																  	`supplier_payments_status` smallint(1) NOT NULL default '0',
																  	`suppliers_firstname` varchar(32) NOT NULL default '',
																  	`suppliers_lastname` varchar(32) NOT NULL default '',
																  	`suppliers_street_address` varchar(64) NOT NULL default '',
																  	`suppliers_suburb` varchar(64) default NULL,
																  	`suppliers_city` varchar(32) NOT NULL default '',
																  	`suppliers_postcode` varchar(10) NOT NULL default '',
																  	`suppliers_state` varchar(32) default NULL,
																  	`suppliers_country` varchar(64) NOT NULL default '',
																  	`suppliers_telephone` varchar(32) NOT NULL default '',
																  	`suppliers_email_address` varchar(96) NOT NULL default '',
																  	`currency` char(3) default NULL,
																  	`currency_value` decimal(14,6) default NULL,
																  	PRIMARY KEY  (`supplier_payments_id`),
																  	KEY `index_suppliers_id` (`suppliers_id`),
																  	KEY `index_supplier_payment_date` (`supplier_payments_date`)
																) TYPE=MyISAM AUTO_INCREMENT=1000;" ,
												"data" => ""
											);

$add_new_tables["supplier_payments_orders"] = array("structure" => "CREATE TABLE `supplier_payments_orders` (
																	  	`supplier_payments_id` int(11) NOT NULL default '0',
																	  	`supplier_order_lists_id` int(11) NOT NULL default '0',
																	  	`supplier_payments_orders_paid_amount` decimal(15,4) NOT NULL default '0.00',
																		`supplier_payments_type` tinyint(1) NOT NULL default '2',
																	  	PRIMARY KEY  (`supplier_payments_id`, `supplier_order_lists_id`),
																	  	KEY `index_supplier_order_lists_id` (`supplier_order_lists_id`)
																) TYPE=MyISAM ;" ,
													"data" => ""
												);

$add_new_tables["supplier_payments_history"] = array(	"structure" => "CREATE TABLE `supplier_payments_history` (
																		  	`supplier_payments_history_id` int(11) NOT NULL auto_increment,
																		  	`supplier_payments_id` int(11) NOT NULL default '0',
																		  	`supplier_payments_status` smallint(1) NOT NULL default '0',
																		  	`date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																		  	`supplier_notified` tinyint(1) NOT NULL default '0',
																		  	`comments` text,
																		  	`changed_by` varchar(128) NOT NULL default '',
																		  	PRIMARY KEY  (`supplier_payments_history_id`),
																		  	KEY `index_payments_id_and_payments_status` (`supplier_payments_id`,`supplier_payments_status`)
																		) TYPE=MyISAM ;" ,
														"data" => ""
													);

$add_new_tables["supplier_payments_status"] = array (	"structure" => " CREATE TABLE `supplier_payments_status` (
																			`supplier_payments_status_id` int(11) NOT NULL default '0',
																			`language_id` int(11) NOT NULL default '1',
																			`supplier_payments_status_name` varchar(32) NOT NULL default '',
																			`supplier_payments_status_sort_order` int(5) NOT NULL default '50000',
																			PRIMARY KEY  (`supplier_payments_status_id`,`language_id`),
																			KEY `idx_supplier_payments_status_name` (`supplier_payments_status_name`)
																		) TYPE=MyISAM;" ,
														"data" => "	INSERT INTO `supplier_payments_status` (`supplier_payments_status_id`, `language_id`, `supplier_payments_status_name`, `supplier_payments_status_sort_order`) 
																	VALUES (1, 1, 'Pending', 10),
																		(2, 1, 'Paid', 20),
																		(3, 1, 'Reversed', 30),
																		(1, 2, '&#31561;&#24453;&#20184;&#27454;', 10),
																		(2, 2, '&#24050;&#25903;&#20184;', 20),
																		(3, 2, '&#36820;&#36824;', 30);"
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create suppliers payment related tables

if ($group_uncategories_payment_orders) {
	$payments_group_array = array();
	
	$uncategories_completed_order_select_sql = "SELECT DISTINCT sol.supplier_order_lists_id, sol.suppliers_id, max(solh2.date_added) AS latest_added_date, DATE_FORMAT(max(solh2.date_added),  '%Y-%m-%d') AS payment_date
												FROM supplier_order_lists AS sol 
												INNER JOIN supplier_order_lists_history AS solh 
													ON ( sol.supplier_order_lists_id=solh.supplier_order_lists_id and sol.supplier_order_lists_status=solh.supplier_order_lists_status)
												INNER JOIN supplier_order_lists_history AS solh2 
													ON ( sol.supplier_order_lists_id=solh2.supplier_order_lists_id and sol.supplier_order_lists_status=solh2.supplier_order_lists_status)
												LEFT JOIN supplier_payments_orders AS spo 
													ON sol.supplier_order_lists_id = spo.supplier_order_lists_id 
												WHERE sol.supplier_order_lists_status=3 AND spo.supplier_payments_id IS NULL 
												GROUP BY solh.supplier_order_lists_id 
												ORDER BY latest_added_date";
	$uncategories_completed_order_result_sql = tep_db_query($uncategories_completed_order_select_sql);
	
	while ($uncategories_completed_order_row = tep_db_fetch_array($uncategories_completed_order_result_sql)) {
		$payments_group_array[$uncategories_completed_order_row['payment_date']][$uncategories_completed_order_row['suppliers_id']][] = array(	'order_id' => $uncategories_completed_order_row['supplier_order_lists_id'],
																																				'order_date' => $uncategories_completed_order_row['latest_added_date']);
	}
	
	if (count($payments_group_array)) {
		foreach ($payments_group_array as $payment_date => $pay_res) {
			if (is_array($pay_res) && count($pay_res)) {
				foreach ($pay_res as $supplier_id => $sup_orders) {
					$supplier_address_select_sql = "SELECT s.*, z.zone_name, co.countries_id, co.countries_name, co.countries_iso_code_2, co.countries_iso_code_3, co.address_format_id  
													FROM " . TABLE_SUPPLIER . " AS s
													LEFT JOIN " . TABLE_ZONES . " AS z 
														ON (s.supplier_zone_id = z.zone_id) 
													LEFT JOIN " . TABLE_COUNTRIES . " AS co 
														ON (s.supplier_country_id = co.countries_id) 
													WHERE s.supplier_id = '" . $supplier_id . "'";
					
			      	$supplier_address_result_sql = tep_db_query($supplier_address_select_sql);
			      	if ($supplier_address_row = tep_db_fetch_array($supplier_address_result_sql)) {
			      		$update_count++;
			      		
						// Create new payment
			      		$payment_data_array = array('suppliers_id' => $supplier_id,
						                            'supplier_payments_status' => 2,
						                            'suppliers_firstname' => $supplier_address_row['supplier_firstname'],
						                            'suppliers_lastname' => $supplier_address_row['supplier_lastname'],
						                            'suppliers_street_address' => $supplier_address_row['supplier_street_address'],
													'suppliers_suburb' => $supplier_address_row['supplier_suburb'],
													'suppliers_city' => $supplier_address_row['supplier_city'],
													'suppliers_postcode' => $supplier_address_row['supplier_postcode'],
						                            'suppliers_state' => ((tep_not_null($supplier_address_row['supplier_state'])) ? $supplier_address_row['supplier_state'] : $supplier_address_row['zone_name']),
						                            'suppliers_country' => $supplier_address_row['countries_name'],
						                            'suppliers_telephone' => $supplier_address_row['supplier_telephone'],
						                            'suppliers_email_address' => $supplier_address_row['supplier_email_address'],
						                            'currency' => DEFAULT_CURRENCY,
						                            'currency_value' => $currencies->currencies[DEFAULT_CURRENCY]['value']
						                           );
					    tep_db_perform(TABLE_SUPPLIER_PAYMENTS, $payment_data_array);
					    $supplier_payments_id = tep_db_insert_id();
					    
					    // Insert payment orders
					    $payment_date = '';
					    $this_payment_amount = 0;
					    for ($order_cnt=0; $order_cnt < count($sup_orders); $order_cnt++) {
					    	$order_id = $sup_orders[$order_cnt]['order_id'];
					    	
					    	if ($order_cnt == 0) {
					    		$payment_date = $sup_orders[$order_cnt]['order_date'];
					    	}
					    	
					    	$payable_amount_select_sql = "	SELECT SUM( (IF(solp.products_received_quantity > solp.first_max_quantity, IF(solp.products_received_quantity > solp.first_max_quantity+solp.second_max_quantity, solp.first_max_quantity*solp.first_max_unit_price + solp.second_max_quantity*solp.second_max_unit_price, solp.first_max_quantity*solp.first_max_unit_price + (solp.products_received_quantity-solp.first_max_quantity)*solp.second_max_unit_price), solp.products_received_quantity*solp.first_max_unit_price)) ) AS total_amount 
															FROM " . TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS . " AS solp 
															WHERE solp.supplier_order_lists_id = '" . tep_db_input($order_id) . "' 
																AND solp.supplier_order_lists_type=2";
							$payable_amount_result_sql = tep_db_query($payable_amount_select_sql);
							$payable_amount_row = tep_db_fetch_array($payable_amount_result_sql);
							
							$order_payable_amt = (double)$payable_amount_row['total_amount'];
					    	
					    	$supplier_payments_type = '2';
					    	
						    $payment_order_data_array = array(	'supplier_payments_id' => $supplier_payments_id,
				      											'supplier_order_lists_id' => $order_id,
				      											'supplier_payments_orders_paid_amount' => $order_payable_amt,
																'supplier_payments_type' => $supplier_payments_type
								                           	);
						    tep_db_perform(TABLE_SUPPLIER_PAYMENTS_ORDERS, $payment_order_data_array);
						    
							$this_payment_amount += (double)$order_payable_amt;
						}
					    
						// Update payment amount data
					    $payment_update_data_array = array(	'supplier_payments_amount' => $this_payment_amount,
	          					 							'supplier_payments_total' => $this_payment_amount,
	          					 							'supplier_payments_date' => $payment_date,
						                            		'supplier_payments_last_modified' => $payment_date
	          					 							);
	          			tep_db_perform(TABLE_SUPPLIER_PAYMENTS, $payment_update_data_array, 'update', "supplier_payments_id = '" . $supplier_payments_id . "'");
					    
					    // Insert payment history
					    $payment_history_data_array = array('supplier_payments_id' => $supplier_payments_id,
			      											'supplier_payments_status' => 2,
			      											'date_added' => $payment_date,
															'supplier_notified' => '0',
								                            'comments' => 'Payment created by system',
								                            'changed_by' => $login_email_address
							                           	);
					    tep_db_perform(TABLE_SUPPLIER_PAYMENTS_HISTORY, $payment_history_data_array);
			      	}
				}
			}
		}
	}
}

// Insert new records into admin_files table (for supplier payment)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["suppliers_payment.php"] = array(	"insert" => " ('suppliers_payment.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   										);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='suppliers_payment.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for supplier payment)

?>