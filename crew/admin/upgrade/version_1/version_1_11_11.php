<?
/*
  	$Id: version_1_11_11.php,v 1.1 2006/03/16 08:26:38 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define class and orders id as index key in orders_total table
add_index_key (TABLE_ORDERS_TOTAL, 'index_class_and_oid', 'index', 'class, orders_id', $DBTables);
// End of define class and orders id as index key in orders_total table

// Define customers id and orders status as index key in orders table
add_index_key (TABLE_ORDERS, 'index_cust_id_and_order_status', 'index', 'customers_id, orders_status', $DBTables);
// End of define customers id and orders status as index key in orders table


$existing_products_fields = get_table_fields(TABLE_PRODUCTS);
if (!in_array('products_out_of_stock_level', $existing_products_fields) && in_array('products_negative_qty', $existing_products_fields)) {
	$convert_pmode_setting = true;
} else {
	$convert_pmode_setting = false;
}

// Insert new field into products table
$add_new_field = array();
$add_new_field[TABLE_PRODUCTS] = array (array (	"field_name" => "products_out_of_stock_level",
												"field_attr" => " int(4) default NULL ",
												"add_after" => "products_eta"
									 			)
									  );
add_field ($add_new_field, false);
// End of insert new field into products table

if ($convert_pmode_setting) {
	// Update those products with pre-order-level = null to system defined value where purchase mode = 3
	$store_system_pre_order_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
											SET products_pre_order_level = '" . SYSTEM_PRE_ORDER_LEVEL . "' 
											WHERE products_purchase_mode=3 
												AND products_bundle='' 
												AND products_bundle_dynamic='' 
												AND custom_products_type_id=0 
												AND products_pre_order_level IS NULL";
	tep_db_query($store_system_pre_order_update_sql);
	// End of update those products with pre-order-level = null to system defined value where purchase mode = 3
	
	// Delete batch update setting for products_purchase_mode and products_negative_qty
	$pmode_and_negative_qty_update_field_delete_sql = "DELETE FROM " . TABLE_PRICE_TAGS . " WHERE price_tags_update_field='products_purchase_mode' OR price_tags_update_field='products_negative_qty'";
	tep_db_query($pmode_and_negative_qty_update_field_delete_sql);
	// End of delete batch update setting for products_purchase_mode and products_negative_qty
	
	/******************************************************************************************************
	 Purchase Mode = 1
		Do not keep inventory or Allow negative product quantity ===> Purchase Mode 1 (Always Add to Cart)
		Keep inventory and Not allow negative product quantity 	 ===> Purchase Mode 4 (Auto)
																		Out of Stock Level = 0
																		Pre-Order Level = NULL
	
	 Purchase Mode = 2  ===> No changes
	 
	 Purchase Mode =3	===> Purchase Mode 4 (Auto)
								Out of Stock Level = NULL
								Pre-Order Level = Remain Current Setting
	******************************************************************************************************/
	$purchase_mode_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
									SET products_purchase_mode = 4,
										products_out_of_stock_level = 0,
										products_pre_order_level = NULL 
									WHERE products_purchase_mode=1 
										AND products_bundle='' 
										AND products_bundle_dynamic='' 
										AND custom_products_type_id=0 
										AND (products_skip_inventory=0 AND products_negative_qty=0)";
	tep_db_query($purchase_mode_update_sql);
	
	$purchase_mode_update_sql = "	UPDATE " . TABLE_PRODUCTS . " 
									SET products_purchase_mode = 4,
										products_out_of_stock_level = NULL 
									WHERE products_purchase_mode=3 
										AND products_bundle='' 
										AND products_bundle_dynamic='' 
										AND custom_products_type_id=0 ";
	tep_db_query($purchase_mode_update_sql);
	
	// Delete products_negative_qty field from products table
	$delete_field = array();
	$delete_field[TABLE_PRODUCTS] = array  ( array( "field_name" => "products_negative_qty") );
	
	delete_field ($delete_field);
	// End of delete products_negative_qty field from products table
}
?>