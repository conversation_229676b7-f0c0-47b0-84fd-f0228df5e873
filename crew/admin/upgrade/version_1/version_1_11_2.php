<?
/*
  	$Id: version_1_11_2.php,v 1.2 2006/02/09 06:11:27 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into customers_info table
$add_new_field = array();

$add_new_field[TABLE_CUSTOMERS_INFO] = array (	array (	"field_name" => "customers_info_changes_made",
									 					"field_attr" => " text NOT NULL ",
									 					"add_after" => "customers_info_date_account_last_modified"
									 					)
										  );

add_field ($add_new_field);
// End of insert new field into customers_info table

// Insert new records into configuration table (for stock quantity reference buyback module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_STOCK_REFERENCE"] = array("insert" => " ('Buyback Stock Reference', 'BUYBACK_STOCK_REFERENCE', 'products_quantity', 'Which product\'s stock quantity to refer in buyback page?', ".$row_sql["configuration_group_id"].", 20, NULL, 'now()', NULL, 'tep_cfg_key_select_option(array(\'products_quantity\'=>\'Product Available Quantity\', \'products_actual_quantity\'=>\'Product Actual Quantity\'),')" );
	$conf_insert_sql["SUPPLIER_ORDER_REFERENCE_DATE"] = array("insert" => " ('Supplier Order Reference Date', 'SUPPLIER_ORDER_REFERENCE_DATE', '".date('Y-m-d H:i:s')."', 'Used for getting the total supplied quantity from suppliers orders newest than this date.', ".$row_sql["configuration_group_id"].", 130, NULL, 'now()', NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for stock quantity reference buyback module)

// Change field structure for supplier_list_status_name in supplier_list_status table
$change_field_structure = array();
$change_field_structure[TABLE_SUPPLIER_LIST_STATUS] = array (array ("field_name" => "supplier_list_status_name",
												 					"field_attr" => " varchar(255) NOT NULL default '' "
													 				)
															);

change_field_structure ($change_field_structure);
// End of change field structure for supplier_list_status_name in supplier_list_status table

// Update records in supplier_list_status table (changes to the status name for Processing and Completed)
$status_update_sql = array();
$status_update_sql[TABLE_SUPPLIER_LIST_STATUS] = array(	array(	"field_name" => "supplier_list_status_name",
																"update" => " supplier_list_status_name='Processing (Awaiting Payment)' ",
																"where_str" => " supplier_list_status_id=2 AND language_id=1"),
														array(	"field_name" => "supplier_list_status_name",
																"update" => " supplier_list_status_name='&#22788;&#29702; (&#31561;&#24453;&#20184;&#27454;)' ",
																"where_str" => " supplier_list_status_id=2 AND language_id=2"),
														array(	"field_name" => "supplier_list_status_name",
																"update" => " supplier_list_status_name='Completed (Paid)' ",
																"where_str" => " supplier_list_status_id=3 AND language_id=1"),
														array(	"field_name" => "supplier_list_status_name",
																"update" => " supplier_list_status_name='&#23436;&#25104; (&#24050;&#25903;&#20184;)' ",
																"where_str" => " supplier_list_status_id=3 AND language_id=2")
													 );

advance_update_records($status_update_sql, $DBTables);
// End of update records in supplier_list_status table (changes to the status name for Processing and Completed)
?>