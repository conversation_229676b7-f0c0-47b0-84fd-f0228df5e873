<?
/*
  	$Id: version_1_11_3.php,v 1.1 2006/02/15 02:29:38 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create products_purchases_lists table
$add_new_tables = array();
$add_new_tables["products_purchases_lists"] = array("structure" => "CREATE TABLE `products_purchases_lists` (
																	  	`products_purchases_lists_id` int(11) NOT NULL auto_increment,
																	  	`products_purchases_lists_name` varchar(255) NOT NULL DEFAULT '',
																	  	`products_purchases_lists_cat_id` int(11) NOT NULL default '0',
																	  	`products_purchases_lists_reference_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  	`products_purchases_lists_last_modified` datetime NOT NULL default '0000-00-00 00:00:00',
																	  PRIMARY KEY (`products_purchases_lists_id`),
																	  UNIQUE KEY `products_purchases_lists_name` (`products_purchases_lists_name`)
																	) TYPE=MyISAM;" ,
													"data" => "	INSERT INTO `products_purchases_lists` (`products_purchases_lists_id`, `products_purchases_lists_name`, `products_purchases_lists_cat_id`, `products_purchases_lists_reference_date`, `products_purchases_lists_last_modified`) 
																VALUES (1, 'General List', 0, '".date('Y-m-d H:i:s')."', now());"
													);

$add_new_tables["supplier_purchase_modes"] = array("structure" => "	CREATE TABLE `supplier_purchase_modes` (
																		`supplier_id` int(11) NOT NULL default '0',
																	  	`products_purchases_lists_id` int(11) NOT NULL default '0',
																	  	`supplier_purchase_mode` varchar(20) NOT NULL default 'STATUS_GROUP',
																	  	PRIMARY KEY  (`supplier_id`,`products_purchases_lists_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create products_purchases_lists table

// Reconstruct products_purchases table
$existing_products_purchases_fields = get_table_fields("products_purchases");
if (!in_array('products_purchases_lists_id', $existing_products_purchases_fields)) {
	// Drop existing primary key (products_id) for products_purchases table
	drop_index_key ("products_purchases", 'PRIMARY KEY', 'primary', $DBTables, array('products_id'));
	// End of drop existing primary key (products_id) for products_purchases table
	
	// Insert new primary key field into products_purchases table
	$add_new_field = array();
	$add_new_field["products_purchases"] = array (array (	"field_name" => "products_purchases_lists_id",
											 				"field_attr" => " INT(11) NOT NULL DEFAULT '1' FIRST",
											 				"add_after" => ""
											 			)
												  );
	add_field ($add_new_field, false);
	
	add_index_key ("products_purchases", 'primary key', 'primary', 'products_purchases_lists_id, products_id', $DBTables);
	// End of insert new primary key field into products_purchases table
	
	// Change field structure for products_purchases_lists_id in products_purchases table
	$change_field_structure = array();
	
	$change_field_structure["products_purchases"] = array (array (	"field_name" => "products_purchases_lists_id",
												 					"field_attr" => " INT(11) NOT NULL DEFAULT '0' "
													 			)
															);
	change_field_structure ($change_field_structure);
	// End of change field structure for products_purchases_lists_id in products_purchases table
	
	
	// Drop existing primary key (supplier_groups_id, products_id) for supplier_pricing table
	drop_index_key ("supplier_pricing", 'PRIMARY KEY', 'primary', $DBTables, array('supplier_groups_id', 'products_id'));
	// End of drop existing primary key (supplier_groups_id, products_id) for supplier_pricing table
	
	// Insert new primary key field into supplier_pricing table
	$add_new_field = array();
	$add_new_field["supplier_pricing"] = array (array (	"field_name" => "products_purchases_lists_id",
										 				"field_attr" => " INT(11) NOT NULL DEFAULT '1'",
										 				"add_after" => "supplier_groups_id"
										 			)
											  );
	add_field ($add_new_field, false);
	
	add_index_key ("supplier_pricing", 'primary key', 'primary', 'supplier_groups_id, products_purchases_lists_id, products_id', $DBTables);
	// End of insert new primary key field into supplier_pricing table
	
	// Change field structure for products_purchases_lists_id in supplier_pricing table
	$change_field_structure = array();
	
	$change_field_structure["supplier_pricing"] = array (array ("field_name" => "products_purchases_lists_id",
												 				"field_attr" => " INT(11) NOT NULL DEFAULT '0' "
													 			)
														);
	change_field_structure ($change_field_structure);
	// End of change field structure for products_purchases_lists_id in supplier_pricing table
	
	
	// Drop existing primary key (supplier_groups_id, supplier_pricing_setting_key) for supplier_pricing_setting table
	drop_index_key ("supplier_pricing_setting", 'PRIMARY KEY', 'primary', $DBTables, array('supplier_groups_id', 'supplier_pricing_setting_key'));
	// End of drop existing primary key (supplier_groups_id, supplier_pricing_setting_key) for supplier_pricing_setting table
	
	// Insert new primary key field into supplier_pricing_setting table
	$add_new_field = array();
	$add_new_field["supplier_pricing_setting"] = array (array (	"field_name" => "products_purchases_lists_id",
												 				"field_attr" => " INT(11) NOT NULL DEFAULT '1'",
												 				"add_after" => "supplier_groups_id"
										 			)
											  );
	add_field ($add_new_field, false);
	
	add_index_key ("supplier_pricing_setting", 'primary key', 'primary', 'supplier_groups_id, products_purchases_lists_id, supplier_pricing_setting_key', $DBTables);
	// End of insert new primary key field into supplier_pricing_setting table
	
	// Change field structure for products_purchases_lists_id in supplier_pricing_setting table
	$change_field_structure = array();
	
	$change_field_structure["supplier_pricing_setting"] = array (array ("field_name" => "products_purchases_lists_id",
														 				"field_attr" => " INT(11) NOT NULL DEFAULT '0' "
															 			)
																);
	change_field_structure ($change_field_structure);
	// End of change field structure for products_purchases_lists_id in supplier_pricing_setting table
	
	
	
	
	// Drop existing primary key (supplier_groups_id) for supplier_list_time_setting table
	drop_index_key ("supplier_list_time_setting", 'PRIMARY KEY', 'primary', $DBTables, array('supplier_groups_id'));
	// End of drop existing primary key (supplier_groups_id) for supplier_list_time_setting table
	
	// Insert new primary key field into supplier_list_time_setting table
	$add_new_field = array();
	$add_new_field["supplier_list_time_setting"] = array (array (	"field_name" => "products_purchases_lists_id",
													 				"field_attr" => " INT(11) NOT NULL DEFAULT '1'",
													 				"add_after" => "supplier_groups_id"
											 					)
												  		);
	add_field ($add_new_field, false);
	
	add_index_key ("supplier_list_time_setting", 'primary key', 'primary', 'supplier_groups_id, products_purchases_lists_id', $DBTables);
	// End of insert new primary key field into supplier_list_time_setting table
	
	// Change field structure for products_purchases_lists_id in supplier_list_time_setting table
	$change_field_structure = array();
	
	$change_field_structure["supplier_list_time_setting"] = array (array (	"field_name" => "products_purchases_lists_id",
															 				"field_attr" => " INT(11) NOT NULL DEFAULT '0' "
																 		)
																);
	change_field_structure ($change_field_structure);
	// End of change field structure for products_purchases_lists_id in supplier_list_time_setting table
	
	// Insert new fields into supplier_order_lists table
	$add_new_field = array();
	$add_new_field["supplier_order_lists"] = array (array (	"field_name" => "products_purchases_lists_id",
											 				"field_attr" => " INT(11) NOT NULL DEFAULT '1'",
											 				"add_after" => "supplier_order_lists_id"
												 			),
												 	array (	"field_name" => "products_purchases_lists_name",
											 				"field_attr" => " varchar(255) NOT NULL default '' ",
											 				"add_after" => "products_purchases_lists_id"
												 			)
												  );
	add_field ($add_new_field, false);
	// End of insert new fields into supplier_order_lists table
	
	// Change field structure for products_purchases_lists_id in supplier_order_lists table
	$change_field_structure = array();
	
	$change_field_structure["supplier_order_lists"] = array (array ("field_name" => "products_purchases_lists_id",
													 				"field_attr" => " INT(11) NOT NULL DEFAULT '0' "
																 	)
															);
	change_field_structure ($change_field_structure);
	// End of change field structure for products_purchases_lists_id in supplier_order_lists table
	
}
// End of reconstruct products_purchases table

// Insert new fields into buyback_basket table
$add_new_field = array();
$add_new_field[TABLE_BUYBACK_BASKET] = array (array (	"field_name" => "products_purchases_lists_id",
								 						"field_attr" => " INT(11) NOT NULL DEFAULT '0' ",
								 						"add_after" => "buyback_basket_list_type"
								 					)
									  		);
add_field ($add_new_field, false);
// End of insert new fields into buyback_basket table

?>