<?
/*
  	$Id: version_1_11_4.php,v 1.2 2006/02/17 03:51:31 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Delete records from configuration table (for Supplier Order Reference Date)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$inner_select_sql = "	SELECT configuration_id 
							FROM " . TABLE_CONFIGURATION . "
							WHERE configuration_key='SUPPLIER_ORDER_REFERENCE_DATE' AND configuration_group_id='".$row_sql["configuration_group_id"]."'" ;
	$inner_result_sql = tep_db_query($inner_select_sql);
	if ($inner_row = tep_db_fetch_array($inner_result_sql)) {	// if found existing record
		$conf_delete_sql = array();
		
		$conf_delete_sql['SUPPLIER_ORDER_REFERENCE_DATE'] = array(	"unique" => "1" );
		delete_records(TABLE_CONFIGURATION, "configuration_key", $conf_delete_sql, $DBTables);
	}
}
// End of delete records from configuration table (for Supplier Order Reference Date)

// Create restock_character_sets and restock_character_info tables
$add_new_tables = array();
$add_new_tables["restock_character_sets"] = array(	"structure" => "CREATE TABLE `restock_character_sets` (
																	`restock_character_sets_id` int(11) NOT NULL auto_increment,
																	`products_purchases_lists_id` int(11) NOT NULL default '0',
																  	`restock_character_sets_name` varchar(255) NOT NULL DEFAULT '',
																  PRIMARY KEY (`restock_character_sets_id`),
																  UNIQUE KEY `restock_character_sets_name` (`restock_character_sets_name`)
																) TYPE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["restock_character_info"] = array(	"structure" => "CREATE TABLE `restock_character_info` (
																		`restock_character_sets_id` int(11) NOT NULL default '0',
																	  	`products_id` int(11) NOT NULL default '0',
																	  	`restock_character` varchar(255) NOT NULL DEFAULT '',
																	  	PRIMARY KEY  (`restock_character_sets_id`,`products_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create restock_character_sets and restock_character_info tables

// Delete supplier_deduct_from_order_id field from supplier table
$delete_field = array();

$delete_field[TABLE_SUPPLIER] = array  ( array( "field_name" => "supplier_deduct_from_order_id") );
$delete_field[TABLE_SUPPLIER_PRICING] = array  ( array( "field_name" => "supplier_pricing_comment") );

delete_field ($delete_field);
// End of delete supplier_deduct_from_order_id field from supplier table

// Insert new field into supplier_order_lists_products table
$add_new_field = array();
$add_new_field[TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS] = array (array ("field_name" => "min_quantity",
													 				"field_attr" => " int(11) default NULL ",
													 				"add_after" => "products_purchase_status"
													 				)
														  	);
add_field ($add_new_field, false);
// End of insert new field into supplier_order_lists_products table


// Insert new records into configuration table (for Affiliate Program)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Affiliate Program'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["AFFILIATE_ORDERS_ELIGIBLE_PAYMENT_PERIOD"] = array("insert" => " ('Orders Eligible Payment Period', 'AFFILIATE_ORDERS_ELIGIBLE_PAYMENT_PERIOD', '60', 'The eligible payment period for orders made within this number of days from customer signup date. (Leave blank for no constraint)', '".$row_sql["configuration_group_id"]."', 17, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Affiliate Program)

?>