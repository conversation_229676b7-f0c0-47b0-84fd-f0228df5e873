<?
/*
  	$Id: version_1_11_5.php,v 1.1 2006/02/21 01:59:30 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define categories_id as index key in products_to_categories table
add_index_key (TABLE_PRODUCTS_TO_CATEGORIES, 'index_categories_id', 'index', 'categories_id', $DBTables);
// End of define categories_id as index key in products_to_categories table

// Define paypal_ipn_id as index key in orders table
add_index_key (TABLE_ORDERS, 'index_paypal_ipn_id', 'index', 'paypal_ipn_id', $DBTables);
// End of define paypal_ipn_id as index key in orders table

// Insert new field into products_purchases_lists table
$add_new_field = array();
$add_new_field[TABLE_PRODUCTS_PURCHASES_LISTS] = array (array ("field_name" => "products_purchases_lists_qty_round_up",
												 				"field_attr" => " int(11) NOT NULL default 100 ",
												 				"add_after" => "products_purchases_lists_cat_id"
												 				)
													  	);
add_field ($add_new_field, false);
// End of insert new field into products_purchases_lists table

// Change field structure for products_purchases_lists_qty_round_up in products_purchases_lists table
$change_field_structure = array();

$change_field_structure[TABLE_PRODUCTS_PURCHASES_LISTS] = array (array ("field_name" => "products_purchases_lists_qty_round_up",
											 							"field_attr" => " int(11) NOT NULL default 1 "
												 						)
																);
change_field_structure ($change_field_structure);
// End of change field structure for products_purchases_lists_qty_round_up in products_purchases_lists table
?>