<?
/*
  	$Id: version_1_11_6.php,v 1.1 2006/02/23 02:28:03 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define supplier_order_lists_date as index key in supplier_order_lists table
add_index_key (TABLE_SUPPLIER_ORDER_LISTS, 'index_supplier_order_lists_date', 'index', 'supplier_order_lists_date', $DBTables);
// End of define supplier_order_lists_date as index key in supplier_order_lists table

// Insert new records into admin_files_actions table (for permission on editing purchase list)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='products_purchase_quantity.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_PURCHASE_LIST"] = array("insert" => " ('SUPPLIER_PURCHASE_LIST', 'Edit Purchase Lists', ".$row_sql["admin_files_id"].", '1', 5)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
	
	
	// Update records in admin_files_actions table (associate "Restock account info" to products_purchase_quantity.php)
	$admin_file_actions_update_sql = array();
	$admin_file_actions_update_sql["SUPPLIER_RESTOCK_ACCOUNT_INFO"] = array("update" => " admin_files_sort_order='10', admin_files_id='".$row_sql["admin_files_id"]."'");
	
	update_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_file_actions_update_sql, $DBTables);
	// End of update records in admin_files_actions table (associate "Restock account info" to products_purchase_quantity.php)
}
// End of insert new records into admin_files_actions table (for permission on editing purchase list)

?>