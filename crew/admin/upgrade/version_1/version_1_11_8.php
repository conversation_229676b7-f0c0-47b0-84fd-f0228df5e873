<?
/*
  	$Id: version_1_11_8.php,v 1.1 2006/03/01 05:36:12 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Change field structure for restock_character in restock_character_info table
$change_field_structure = array();
$change_field_structure[TABLE_RESTOCK_CHARACTER_INFO] = array (array (	"field_name" => "restock_character",
												 						"field_attr" => " text "
													 					)
																);

change_field_structure ($change_field_structure);
// End of change field structure for restock_character in restock_character_info table

// Change field structure for supplier_pricing_max_quantity in supplier_pricing table
$max_qty_field_info_array = get_field_info (TABLE_SUPPLIER_PRICING, 'supplier_pricing_max_quantity');
if (is_array($max_qty_field_info_array) && count($max_qty_field_info_array)) {
	 if (strtoupper($max_qty_field_info_array['Null']) != 'YES') {
	 	$change_field_structure = array();
		$change_field_structure[TABLE_SUPPLIER_PRICING] = array (array ("field_name" => "supplier_pricing_max_quantity",
																		"field_attr" => " int(11) default NULL "
																		)
																);
		
		change_field_structure ($change_field_structure);
		
		$set_to_null_max_qty_update_sql = "UPDATE " . TABLE_SUPPLIER_PRICING . " SET supplier_pricing_max_quantity=NULL; ";
		tep_db_query($set_to_null_max_qty_update_sql);
	}
}
// End of change field structure for supplier_pricing_max_quantity in supplier_pricing table

?>