<?
/*
  	$Id: version_1_11_9.php,v 1.1 2006/03/06 02:07:35 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on manual adjust actual or available qty)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='categories.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CATALOG_MANUAL_INVENTORY_ADJUST"] = array("insert" => " ('CATALOG_MANUAL_INVENTORY_ADJUST', 'Manual Inventory Adjustment', ".$row_sql["admin_files_id"].", '1', 5)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on manual adjust actual or available qty)

?>