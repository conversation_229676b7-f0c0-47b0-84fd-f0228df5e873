<?
/*
  	$Id: version_1_12.php,v 1.2 2006/03/21 05:43:53 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// <PERSON>'s changes
// Insert new records into configuration table (for Phone Verification)
$conf_insert_sql = array();

$conf_insert_sql["MAX_CODE_VERIFICATION"] = array("insert" => " ('Code Verification Attempts', 'MAX_CODE_VERIFICATION', '3', 'The maximum number of attempts for code verification (e.g. phone verification)', '3', 115, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Phone Verification)


// Insert new field into countries table
$add_new_field = array();
$add_new_field[TABLE_COUNTRIES] = array (array ("field_name" => "countries_international_dialing_code",
												"field_attr" => " varchar(5) default NULL ",
										 		"add_after" => "countries_iso_code_3"
									 			)
									  );

$add_new_field[TABLE_ORDERS] = array (array (	"field_name" => "customers_country_international_dialing_code",
												"field_attr" => " varchar(5) default NULL ",
										 		"add_after" => "customers_country"
									 			)
									  );

add_field ($add_new_field, false);
// End of insert new field into countries table

// Update records in countries table (assign international dialing code to each countries)
$dialing_code_update_sql["1"] = array("update" => " countries_international_dialing_code='93' " );
$dialing_code_update_sql["2"] = array("update" => " countries_international_dialing_code='355' " );
$dialing_code_update_sql["3"] = array("update" => " countries_international_dialing_code='213' " );
$dialing_code_update_sql["4"] = array("update" => " countries_international_dialing_code='684' " );
$dialing_code_update_sql["5"] = array("update" => " countries_international_dialing_code='376' " );
$dialing_code_update_sql["6"] = array("update" => " countries_international_dialing_code='244' " );
$dialing_code_update_sql["7"] = array("update" => " countries_international_dialing_code='1264' " );
$dialing_code_update_sql["8"] = array("update" => " countries_international_dialing_code='672' " );
$dialing_code_update_sql["9"] = array("update" => " countries_international_dialing_code='1268' " );
$dialing_code_update_sql["10"] = array("update" => " countries_international_dialing_code='54' " );
$dialing_code_update_sql["11"] = array("update" => " countries_international_dialing_code='374' " );
$dialing_code_update_sql["12"] = array("update" => " countries_international_dialing_code='297' " );
$dialing_code_update_sql["13"] = array("update" => " countries_international_dialing_code='61' " );
$dialing_code_update_sql["14"] = array("update" => " countries_international_dialing_code='43' " );
$dialing_code_update_sql["15"] = array("update" => " countries_international_dialing_code='994' " );
$dialing_code_update_sql["16"] = array("update" => " countries_international_dialing_code='1242' " );
$dialing_code_update_sql["17"] = array("update" => " countries_international_dialing_code='973' " );
$dialing_code_update_sql["18"] = array("update" => " countries_international_dialing_code='880' " );
$dialing_code_update_sql["19"] = array("update" => " countries_international_dialing_code='1246' " );
$dialing_code_update_sql["20"] = array("update" => " countries_international_dialing_code='375' " );
$dialing_code_update_sql["21"] = array("update" => " countries_international_dialing_code='32' " );
$dialing_code_update_sql["22"] = array("update" => " countries_international_dialing_code='501' " );
$dialing_code_update_sql["23"] = array("update" => " countries_international_dialing_code='229' " );
$dialing_code_update_sql["24"] = array("update" => " countries_international_dialing_code='1441' " );
$dialing_code_update_sql["25"] = array("update" => " countries_international_dialing_code='975' " );
$dialing_code_update_sql["26"] = array("update" => " countries_international_dialing_code='591' " );
$dialing_code_update_sql["27"] = array("update" => " countries_international_dialing_code='387' " );
$dialing_code_update_sql["28"] = array("update" => " countries_international_dialing_code='267' " );
$dialing_code_update_sql["30"] = array("update" => " countries_international_dialing_code='55' " );
$dialing_code_update_sql["31"] = array("update" => " countries_international_dialing_code='246' " );
$dialing_code_update_sql["32"] = array("update" => " countries_international_dialing_code='673' " );
$dialing_code_update_sql["33"] = array("update" => " countries_international_dialing_code='359' " );
$dialing_code_update_sql["34"] = array("update" => " countries_international_dialing_code='226' " );
$dialing_code_update_sql["35"] = array("update" => " countries_international_dialing_code='257' " );
$dialing_code_update_sql["36"] = array("update" => " countries_international_dialing_code='855' " );
$dialing_code_update_sql["37"] = array("update" => " countries_international_dialing_code='237' " );
$dialing_code_update_sql["38"] = array("update" => " countries_international_dialing_code='1' " );
$dialing_code_update_sql["39"] = array("update" => " countries_international_dialing_code='238' " );
$dialing_code_update_sql["40"] = array("update" => " countries_international_dialing_code='1345' " );
$dialing_code_update_sql["41"] = array("update" => " countries_international_dialing_code='236' " );
$dialing_code_update_sql["42"] = array("update" => " countries_international_dialing_code='235' " );
$dialing_code_update_sql["43"] = array("update" => " countries_international_dialing_code='56' " );
$dialing_code_update_sql["44"] = array("update" => " countries_international_dialing_code='86' " );
$dialing_code_update_sql["45"] = array("update" => " countries_international_dialing_code='61' " );
$dialing_code_update_sql["46"] = array("update" => " countries_international_dialing_code='61' " );
$dialing_code_update_sql["47"] = array("update" => " countries_international_dialing_code='57' " );
$dialing_code_update_sql["48"] = array("update" => " countries_international_dialing_code='269' " );
$dialing_code_update_sql["49"] = array("update" => " countries_international_dialing_code='242' " );
$dialing_code_update_sql["50"] = array("update" => " countries_international_dialing_code='682' " );
$dialing_code_update_sql["51"] = array("update" => " countries_international_dialing_code='506' " );
$dialing_code_update_sql["52"] = array("update" => " countries_international_dialing_code='225' " );
$dialing_code_update_sql["53"] = array("update" => " countries_international_dialing_code='385' " );
$dialing_code_update_sql["54"] = array("update" => " countries_international_dialing_code='53' " );
$dialing_code_update_sql["55"] = array("update" => " countries_international_dialing_code='357' " );
$dialing_code_update_sql["56"] = array("update" => " countries_international_dialing_code='420' " );
$dialing_code_update_sql["57"] = array("update" => " countries_international_dialing_code='45' " );
$dialing_code_update_sql["58"] = array("update" => " countries_international_dialing_code='253' " );
$dialing_code_update_sql["59"] = array("update" => " countries_international_dialing_code='1767' " );
$dialing_code_update_sql["60"] = array("update" => " countries_international_dialing_code='1809' " );
$dialing_code_update_sql["61"] = array("update" => " countries_international_dialing_code='670' " );
$dialing_code_update_sql["62"] = array("update" => " countries_international_dialing_code='593' " );
$dialing_code_update_sql["63"] = array("update" => " countries_international_dialing_code='20' " );
$dialing_code_update_sql["64"] = array("update" => " countries_international_dialing_code='503' " );
$dialing_code_update_sql["65"] = array("update" => " countries_international_dialing_code='240' " );
$dialing_code_update_sql["66"] = array("update" => " countries_international_dialing_code='291' " );
$dialing_code_update_sql["67"] = array("update" => " countries_international_dialing_code='372' " );
$dialing_code_update_sql["68"] = array("update" => " countries_international_dialing_code='251' " );
$dialing_code_update_sql["69"] = array("update" => " countries_international_dialing_code='500' " );
$dialing_code_update_sql["70"] = array("update" => " countries_international_dialing_code='298' " );
$dialing_code_update_sql["71"] = array("update" => " countries_international_dialing_code='679' " );
$dialing_code_update_sql["72"] = array("update" => " countries_international_dialing_code='358' " );
$dialing_code_update_sql["73"] = array("update" => " countries_international_dialing_code='33' " );
$dialing_code_update_sql["74"] = array("update" => " countries_international_dialing_code='33' " );
$dialing_code_update_sql["75"] = array("update" => " countries_international_dialing_code='594' " );
$dialing_code_update_sql["76"] = array("update" => " countries_international_dialing_code='689' " );
$dialing_code_update_sql["78"] = array("update" => " countries_international_dialing_code='241' " );
$dialing_code_update_sql["79"] = array("update" => " countries_international_dialing_code='220' " );
$dialing_code_update_sql["81"] = array("update" => " countries_international_dialing_code='49' " );
$dialing_code_update_sql["82"] = array("update" => " countries_international_dialing_code='233' " );
$dialing_code_update_sql["83"] = array("update" => " countries_international_dialing_code='350' " );
$dialing_code_update_sql["84"] = array("update" => " countries_international_dialing_code='30' " );
$dialing_code_update_sql["85"] = array("update" => " countries_international_dialing_code='299' " );
$dialing_code_update_sql["86"] = array("update" => " countries_international_dialing_code='1473' " );
$dialing_code_update_sql["87"] = array("update" => " countries_international_dialing_code='590' " );
$dialing_code_update_sql["88"] = array("update" => " countries_international_dialing_code='1671' " );
$dialing_code_update_sql["89"] = array("update" => " countries_international_dialing_code='502' " );
$dialing_code_update_sql["90"] = array("update" => " countries_international_dialing_code='224' " );
$dialing_code_update_sql["91"] = array("update" => " countries_international_dialing_code='245' " );
$dialing_code_update_sql["92"] = array("update" => " countries_international_dialing_code='592' " );
$dialing_code_update_sql["93"] = array("update" => " countries_international_dialing_code='509' " );
$dialing_code_update_sql["95"] = array("update" => " countries_international_dialing_code='504' " );
$dialing_code_update_sql["96"] = array("update" => " countries_international_dialing_code='852' " );
$dialing_code_update_sql["97"] = array("update" => " countries_international_dialing_code='36' " );
$dialing_code_update_sql["98"] = array("update" => " countries_international_dialing_code='354' " );
$dialing_code_update_sql["99"] = array("update" => " countries_international_dialing_code='91' " );
$dialing_code_update_sql["100"] = array("update" => " countries_international_dialing_code='62' " );
$dialing_code_update_sql["101"] = array("update" => " countries_international_dialing_code='98' " );
$dialing_code_update_sql["102"] = array("update" => " countries_international_dialing_code='964' " );
$dialing_code_update_sql["103"] = array("update" => " countries_international_dialing_code='353' " );
$dialing_code_update_sql["104"] = array("update" => " countries_international_dialing_code='972' " );
$dialing_code_update_sql["105"] = array("update" => " countries_international_dialing_code='39' " );
$dialing_code_update_sql["106"] = array("update" => " countries_international_dialing_code='1876' " );
$dialing_code_update_sql["107"] = array("update" => " countries_international_dialing_code='81' " );
$dialing_code_update_sql["108"] = array("update" => " countries_international_dialing_code='962' " );
$dialing_code_update_sql["109"] = array("update" => " countries_international_dialing_code='7' " );
$dialing_code_update_sql["110"] = array("update" => " countries_international_dialing_code='254' " );
$dialing_code_update_sql["111"] = array("update" => " countries_international_dialing_code='686' " );
$dialing_code_update_sql["112"] = array("update" => " countries_international_dialing_code='850' " );
$dialing_code_update_sql["113"] = array("update" => " countries_international_dialing_code='82' " );
$dialing_code_update_sql["114"] = array("update" => " countries_international_dialing_code='965' " );
$dialing_code_update_sql["115"] = array("update" => " countries_international_dialing_code='996' " );
$dialing_code_update_sql["116"] = array("update" => " countries_international_dialing_code='856' " );
$dialing_code_update_sql["117"] = array("update" => " countries_international_dialing_code='371' " );
$dialing_code_update_sql["118"] = array("update" => " countries_international_dialing_code='961' " );
$dialing_code_update_sql["119"] = array("update" => " countries_international_dialing_code='266' " );
$dialing_code_update_sql["120"] = array("update" => " countries_international_dialing_code='231' " );
$dialing_code_update_sql["121"] = array("update" => " countries_international_dialing_code='218' " );
$dialing_code_update_sql["122"] = array("update" => " countries_international_dialing_code='423' " );
$dialing_code_update_sql["123"] = array("update" => " countries_international_dialing_code='370' " );
$dialing_code_update_sql["124"] = array("update" => " countries_international_dialing_code='352' " );
$dialing_code_update_sql["125"] = array("update" => " countries_international_dialing_code='853' " );
$dialing_code_update_sql["126"] = array("update" => " countries_international_dialing_code='389' " );
$dialing_code_update_sql["127"] = array("update" => " countries_international_dialing_code='261' " );
$dialing_code_update_sql["128"] = array("update" => " countries_international_dialing_code='265' " );
$dialing_code_update_sql["129"] = array("update" => " countries_international_dialing_code='60' " );
$dialing_code_update_sql["130"] = array("update" => " countries_international_dialing_code='960' " );
$dialing_code_update_sql["131"] = array("update" => " countries_international_dialing_code='223' " );
$dialing_code_update_sql["132"] = array("update" => " countries_international_dialing_code='356' " );
$dialing_code_update_sql["133"] = array("update" => " countries_international_dialing_code='692' " );
$dialing_code_update_sql["134"] = array("update" => " countries_international_dialing_code='596' " );
$dialing_code_update_sql["135"] = array("update" => " countries_international_dialing_code='222' " );
$dialing_code_update_sql["136"] = array("update" => " countries_international_dialing_code='230' " );
$dialing_code_update_sql["137"] = array("update" => " countries_international_dialing_code='269' " );
$dialing_code_update_sql["138"] = array("update" => " countries_international_dialing_code='52' " );
$dialing_code_update_sql["139"] = array("update" => " countries_international_dialing_code='591' " );
$dialing_code_update_sql["140"] = array("update" => " countries_international_dialing_code='373' " );
$dialing_code_update_sql["141"] = array("update" => " countries_international_dialing_code='377' " );
$dialing_code_update_sql["142"] = array("update" => " countries_international_dialing_code='976' " );
$dialing_code_update_sql["143"] = array("update" => " countries_international_dialing_code='1664' " );
$dialing_code_update_sql["144"] = array("update" => " countries_international_dialing_code='212' " );
$dialing_code_update_sql["145"] = array("update" => " countries_international_dialing_code='258' " );
$dialing_code_update_sql["146"] = array("update" => " countries_international_dialing_code='95' " );
$dialing_code_update_sql["147"] = array("update" => " countries_international_dialing_code='264' " );
$dialing_code_update_sql["148"] = array("update" => " countries_international_dialing_code='674' " );
$dialing_code_update_sql["149"] = array("update" => " countries_international_dialing_code='977' " );
$dialing_code_update_sql["150"] = array("update" => " countries_international_dialing_code='31' " );
$dialing_code_update_sql["151"] = array("update" => " countries_international_dialing_code='599' " );
$dialing_code_update_sql["152"] = array("update" => " countries_international_dialing_code='687' " );
$dialing_code_update_sql["153"] = array("update" => " countries_international_dialing_code='64' " );
$dialing_code_update_sql["154"] = array("update" => " countries_international_dialing_code='505' " );
$dialing_code_update_sql["155"] = array("update" => " countries_international_dialing_code='227' " );
$dialing_code_update_sql["156"] = array("update" => " countries_international_dialing_code='234' " );
$dialing_code_update_sql["157"] = array("update" => " countries_international_dialing_code='683' " );
$dialing_code_update_sql["158"] = array("update" => " countries_international_dialing_code='672' " );
$dialing_code_update_sql["159"] = array("update" => " countries_international_dialing_code='1670' " );
$dialing_code_update_sql["160"] = array("update" => " countries_international_dialing_code='47' " );
$dialing_code_update_sql["161"] = array("update" => " countries_international_dialing_code='968' " );
$dialing_code_update_sql["162"] = array("update" => " countries_international_dialing_code='92' " );
$dialing_code_update_sql["163"] = array("update" => " countries_international_dialing_code='680' " );
$dialing_code_update_sql["164"] = array("update" => " countries_international_dialing_code='507' " );
$dialing_code_update_sql["165"] = array("update" => " countries_international_dialing_code='675' " );
$dialing_code_update_sql["166"] = array("update" => " countries_international_dialing_code='595' " );
$dialing_code_update_sql["167"] = array("update" => " countries_international_dialing_code='51' " );
$dialing_code_update_sql["168"] = array("update" => " countries_international_dialing_code='63' " );
$dialing_code_update_sql["169"] = array("update" => " countries_international_dialing_code='872' " );
$dialing_code_update_sql["170"] = array("update" => " countries_international_dialing_code='48' " );
$dialing_code_update_sql["171"] = array("update" => " countries_international_dialing_code='351' " );
$dialing_code_update_sql["172"] = array("update" => " countries_international_dialing_code='1787' " );
$dialing_code_update_sql["173"] = array("update" => " countries_international_dialing_code='974' " );
$dialing_code_update_sql["174"] = array("update" => " countries_international_dialing_code='262' " );
$dialing_code_update_sql["175"] = array("update" => " countries_international_dialing_code='40' " );
$dialing_code_update_sql["176"] = array("update" => " countries_international_dialing_code='7' " );
$dialing_code_update_sql["177"] = array("update" => " countries_international_dialing_code='250' " );
$dialing_code_update_sql["178"] = array("update" => " countries_international_dialing_code='1869' " );
$dialing_code_update_sql["179"] = array("update" => " countries_international_dialing_code='1758' " );
$dialing_code_update_sql["180"] = array("update" => " countries_international_dialing_code='1784' " );
$dialing_code_update_sql["181"] = array("update" => " countries_international_dialing_code='685' " );
$dialing_code_update_sql["182"] = array("update" => " countries_international_dialing_code='378' " );
$dialing_code_update_sql["183"] = array("update" => " countries_international_dialing_code='239' " );
$dialing_code_update_sql["184"] = array("update" => " countries_international_dialing_code='966' " );
$dialing_code_update_sql["185"] = array("update" => " countries_international_dialing_code='221' " );
$dialing_code_update_sql["186"] = array("update" => " countries_international_dialing_code='248' " );
$dialing_code_update_sql["187"] = array("update" => " countries_international_dialing_code='232' " );
$dialing_code_update_sql["188"] = array("update" => " countries_international_dialing_code='65' " );
$dialing_code_update_sql["189"] = array("update" => " countries_international_dialing_code='421' " );
$dialing_code_update_sql["190"] = array("update" => " countries_international_dialing_code='386' " );
$dialing_code_update_sql["191"] = array("update" => " countries_international_dialing_code='677' " );
$dialing_code_update_sql["192"] = array("update" => " countries_international_dialing_code='252' " );
$dialing_code_update_sql["193"] = array("update" => " countries_international_dialing_code='27' " );
$dialing_code_update_sql["195"] = array("update" => " countries_international_dialing_code='34' " );
$dialing_code_update_sql["196"] = array("update" => " countries_international_dialing_code='94' " );
$dialing_code_update_sql["197"] = array("update" => " countries_international_dialing_code='290' " );
$dialing_code_update_sql["198"] = array("update" => " countries_international_dialing_code='508' " );
$dialing_code_update_sql["199"] = array("update" => " countries_international_dialing_code='249' " );
$dialing_code_update_sql["200"] = array("update" => " countries_international_dialing_code='597' " );
$dialing_code_update_sql["201"] = array("update" => " countries_international_dialing_code='79' " );
$dialing_code_update_sql["202"] = array("update" => " countries_international_dialing_code='268' " );
$dialing_code_update_sql["203"] = array("update" => " countries_international_dialing_code='46' " );
$dialing_code_update_sql["204"] = array("update" => " countries_international_dialing_code='41' " );
$dialing_code_update_sql["205"] = array("update" => " countries_international_dialing_code='963' " );
$dialing_code_update_sql["206"] = array("update" => " countries_international_dialing_code='886' " );
$dialing_code_update_sql["207"] = array("update" => " countries_international_dialing_code='992' " );
$dialing_code_update_sql["208"] = array("update" => " countries_international_dialing_code='255' " );
$dialing_code_update_sql["209"] = array("update" => " countries_international_dialing_code='66' " );
$dialing_code_update_sql["210"] = array("update" => " countries_international_dialing_code='228' " );
$dialing_code_update_sql["211"] = array("update" => " countries_international_dialing_code='690' " );
$dialing_code_update_sql["212"] = array("update" => " countries_international_dialing_code='676' " );
$dialing_code_update_sql["213"] = array("update" => " countries_international_dialing_code='1868' " );
$dialing_code_update_sql["214"] = array("update" => " countries_international_dialing_code='216' " );
$dialing_code_update_sql["215"] = array("update" => " countries_international_dialing_code='90' " );
$dialing_code_update_sql["216"] = array("update" => " countries_international_dialing_code='993' " );
$dialing_code_update_sql["217"] = array("update" => " countries_international_dialing_code='1649' " );
$dialing_code_update_sql["218"] = array("update" => " countries_international_dialing_code='688' " );
$dialing_code_update_sql["219"] = array("update" => " countries_international_dialing_code='256' " );
$dialing_code_update_sql["220"] = array("update" => " countries_international_dialing_code='380' " );
$dialing_code_update_sql["221"] = array("update" => " countries_international_dialing_code='971' " );
$dialing_code_update_sql["222"] = array("update" => " countries_international_dialing_code='44' " );
$dialing_code_update_sql["223"] = array("update" => " countries_international_dialing_code='1' " );
$dialing_code_update_sql["224"] = array("update" => " countries_international_dialing_code='1' " );
$dialing_code_update_sql["225"] = array("update" => " countries_international_dialing_code='598' " );
$dialing_code_update_sql["226"] = array("update" => " countries_international_dialing_code='998' " );
$dialing_code_update_sql["227"] = array("update" => " countries_international_dialing_code='678' " );
$dialing_code_update_sql["228"] = array("update" => " countries_international_dialing_code='39' " );
$dialing_code_update_sql["229"] = array("update" => " countries_international_dialing_code='58' " );
$dialing_code_update_sql["230"] = array("update" => " countries_international_dialing_code='84' " );
$dialing_code_update_sql["231"] = array("update" => " countries_international_dialing_code='284' " );
$dialing_code_update_sql["232"] = array("update" => " countries_international_dialing_code='340' " );
$dialing_code_update_sql["233"] = array("update" => " countries_international_dialing_code='681' " );
$dialing_code_update_sql["234"] = array("update" => " countries_international_dialing_code='212' " );
$dialing_code_update_sql["235"] = array("update" => " countries_international_dialing_code='967' " );
$dialing_code_update_sql["236"] = array("update" => " countries_international_dialing_code='381' " );
$dialing_code_update_sql["237"] = array("update" => " countries_international_dialing_code='243' " );
$dialing_code_update_sql["238"] = array("update" => " countries_international_dialing_code='260' " );
$dialing_code_update_sql["239"] = array("update" => " countries_international_dialing_code='263' " );

update_records(TABLE_COUNTRIES, "countries_id", $dialing_code_update_sql, $DBTables);

// End of update records in countries table (assign international dialing code to each countries)

// Create customers_info_verification tables
$civ_table_exists = true;

if (!in_array('customers_info_verification', $DBTables)) {
	$civ_table_exists = false;
	
	$add_new_tables = array();
	$add_new_tables["customers_info_verification"] = array(	"structure" => "CREATE TABLE `customers_info_verification` (
																				`customers_id` INT(11) NOT NULL ,
																				`customers_info_value` VARCHAR(96) NOT NULL ,
																				`serial_number` VARCHAR(12) NOT NULL ,
																				`verify_try_turns` TINYINT(2) UNSIGNED NOT NULL DEFAULT '0',
																				`info_verified` TINYINT(1) NOT NULL ,
																				`info_verification_type` VARCHAR(32) NOT NULL ,
																				PRIMARY KEY (`customers_id`, `customers_info_value`, `info_verification_type`)
																			) TYPE=MyISAM;" ,
															"data" => ""
															);
	
	add_new_tables ($add_new_tables, $DBTables);
}
// End of create customers_info_verification tables

// Insert new field into customers table
$add_new_field = array();
$add_new_field[TABLE_CUSTOMERS] = array (array ("field_name" => "customers_country_dialing_code_id",
										 		"field_attr" => " varchar(5) default NULL ",
										 		"add_after" => "customers_default_address_id"
									 			)
										  );
add_field ($add_new_field, false);
// End of insert new field into customers table

// Copy contents of email_verified in customers table to info_verified in customers_info_verification table
$existing_customers_fields = get_table_fields(TABLE_CUSTOMERS);
if (in_array('email_verified', $existing_customers_fields) && !$civ_table_exists) {
	$customers_email_verified_select_sql = "SELECT customers_id, customers_email_address, serial_number, email_verified FROM " . TABLE_CUSTOMERS;
	$customers_email_verified_result_sql = tep_db_query($customers_email_verified_select_sql);
	
	while ($customers_email_verified_row = tep_db_fetch_array($customers_email_verified_result_sql)) {
		$sql_data_array = array('customers_id' => $customers_email_verified_row['customers_id'],
		      					'customers_info_value' => $customers_email_verified_row['customers_email_address'],
		      					'serial_number' => $customers_email_verified_row['serial_number'],
		                     	'info_verified' => $customers_email_verified_row['email_verified'],
		                     	'info_verification_type' => "email");
		tep_db_perform("customers_info_verification", $sql_data_array);
	}
	
	// Delete email_verified field from customers table
	$delete_field = array();
	
	$delete_field[TABLE_CUSTOMERS] = array  ( array( "field_name" => "email_verified") );
	
	delete_field ($delete_field);
	// End of delete email_verified field from customers table
}
// End of copy contents of email_verified in customers table to info_verified in customers_info_verification table
?>