<?
/*
  	$Id: version_1_12_1.php,v 1.2 2006/03/23 10:01:45 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define date_purchased as index key in orders table
add_index_key (TABLE_ORDERS, 'index_date_purchased', 'index', 'date_purchased', $DBTables);
// End of define date_purchased as index key in orders table

// <PERSON>'s changes
// Insert new field into countries table
$add_new_field = array();
$add_new_field[TABLE_COUNTRIES] = array (array ("field_name" => "maxmind_support",
												"field_attr" => " tinyint(1) unsigned NOT NULL default '1' ",
										 		"add_after" => ""
									 			)
									  );

add_field ($add_new_field, false);
// End of insert new field into countries table

// Update records in countries table (set those countryies not supported by maxmind)
$status_update_sql = array();
$status_update_sql[TABLE_COUNTRIES] = array(	array(	"field_name" => "maxmind_support",
														"update" => " maxmind_support=0 ",
														"where_str" => " countries_id IN (8, 15, 29, 31, 45, 46, 52, 69, 74, 77, 78, 91, 94, 116, 137, 139, 151, 158, 169, 177, 181, 189, 192, 194, 201, 210, 221, 224, 228, 234, 236, 237) "
													)
											 );

advance_update_records($status_update_sql, $DBTables);
// End of update records in countries table (set those countryies not supported by maxmind)

// Change field structure for maxmind_support in countries table
$change_field_structure = array();
$change_field_structure[TABLE_COUNTRIES] = array (array (	"field_name" => "maxmind_support",
															"field_attr" => " tinyint(1) unsigned NOT NULL default '0' "
											 			)
													);
change_field_structure ($change_field_structure);
// End of change field structure for maxmind_support in countries table

// Insert new records into configuration table (for Verified payments e-mail admin notification)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION"] = array("insert" => " ('Verified Payments E-mail Admin Notification', 'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION', '', 'E-mail address to which the verification of payment e-mail will be send to whenever customers verified their payment e-mail address.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 6, 220, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Verified payments e-mail admin notification)
?>