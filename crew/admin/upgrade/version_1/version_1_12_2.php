<?
/*
  	$Id: version_1_12_2.php,v 1.2 2006/03/30 08:45:43 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for Show buy quantity box setting)
$conf_insert_sql = array();
$conf_insert_sql["SHOW_BUY_QTY_BOX"] = array("insert" => " ('Show buy quantity box', 'SHOW_BUY_QTY_BOX', 'false', 'Show the buy quantity input box in Product Listing page?', 9, 22, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Show buy quantity box setting)

// Insert new records into configuration table (for Verified payments e-mail admin notification order statuses)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION_STATUS"] = array("insert" => " ('Verified Payments E-mail Admin Notification Status', 'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION_STATUS', '7', 'The status for the orders to be included in the verified payment e-mail notification e-mail contents. Seperated by \',\' for multiple values.', 6, 225, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Verified payments e-mail admin notification order statuses)

// Insert new records into configuration_group table (for MaxMind module)
$configuration_group_insert_sql = array();

$configuration_group_insert_sql["MaxMind"] = array	(	"insert" => " ('MaxMind', 'MaxMind setting', 250, 1) ",
														"update" => " sort_order=250, visible='1' "
				   									);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");
// End of insert new records into configuration_group table (for MaxMind module)

// Insert new records into configuration table (for MaxMind module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='MaxMind'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["MAXMIND_LICENSE_KEY"] = array("insert" => " ('MaxMind License Key', 'MAXMIND_LICENSE_KEY', 'as4ekCRUvvix', 'This license key is used to query MaxMind webservices, including Credit Card Fraud Detection and Telephone Verification services.', ".$row_sql["configuration_group_id"].", 5, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["MAXMIND_PHONE_VERIFICATION_ENABLED"] = array("insert" => " ('Allow MaxMind Phone Verification', 'MAXMIND_PHONE_VERIFICATION_ENABLED', 'false', 'Enable MaxMind phone verification in checkout success page?', ".$row_sql["configuration_group_id"].", 10, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for MaxMind module)
?>