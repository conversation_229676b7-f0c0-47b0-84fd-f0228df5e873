<?
/*
  	$Id: version_1_12_3.php,v 1.4 2006/04/11 09:14:11 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_coupon_email_track_fields = get_table_fields('coupon_email_track');

// Insert new field into coupon_email_track table
$add_new_field = array();
$add_new_field['coupon_email_track'] = array (	array (	"field_name" => "sent_role",
														"field_attr" => " varchar(10) NOT NULL default 'customer' ",
														"add_after" => "customer_id_sent"
								 					),
								 				array (	"field_name" => "sent_email_address",
														"field_attr" => " varchar(96) default NULL ",
														"add_after" => "sent_role"
								 					)
									  		);
add_field ($add_new_field, false);
// End of insert new field into orders table

// Update records in coupon_email_track table (set the role to admin for those First Name is Admin and Last Name is null)
$email_track_update_sql = array();
$email_track_update_sql['coupon_email_track'] = array(	array(	"field_name" => "sent_role",
																"update" => " sent_role='admin' ",
																"where_str" => " customer_id_sent=0 AND sent_firstname='Admin' AND sent_lastname IS NULL")
													 );

advance_update_records($email_track_update_sql, $DBTables);
// End of update records in coupon_email_track table (set the role to admin for those First Name is Admin and Last Name is null)

// Batch update the existing records in coupon_email_track table
if (!in_array('sent_email_address', $existing_coupon_email_track_fields)) {
	$customer_select_sql = "SELECT cet.unique_id, c.customers_email_address 
							FROM coupon_email_track AS cet 
							INNER JOIN " . TABLE_CUSTOMERS . " AS c 
								ON cet.customer_id_sent=c.customers_id
							WHERE cet.sent_role='customer'";
	$customer_result_sql = tep_db_query($customer_select_sql);
	
	while ($customer_row = tep_db_fetch_array($customer_result_sql)) {
		$sent_email_update_sql = "	UPDATE coupon_email_track 
									SET sent_email_address = '" . tep_db_input($customer_row['customers_email_address']) . "' 
									WHERE unique_id = '" . tep_db_input($customer_row['unique_id']) . "'";
		tep_db_query($sent_email_update_sql);
	}
}
// End of batch update the existing records in coupon_email_track table

// Change field structure for delivery_country and billing_country in orders table and emailed_to in coupon_email_track table
$change_field_structure = array();

$change_field_structure[TABLE_ORDERS] = array (	array (	"field_name" => "delivery_country",
														"field_attr" => " varchar(64) NOT NULL default '' "
														),
												array (	"field_name" => "billing_country",
														"field_attr" => " varchar(64) NOT NULL default '' "
														)
												);

$change_field_structure['coupon_email_track'] = array (	array (	"field_name" => "emailed_to",
																"field_attr" => " varchar(96) default NULL "
																)
													);
change_field_structure ($change_field_structure);
// End of change field structure for delivery_country and billing_country in orders table and emailed_to in coupon_email_track table

// Batch update the existing orders records with the correct tel_country_id
$existing_orders_fields = get_table_fields(TABLE_ORDERS);
if (!in_array('customers_telephone_country', $existing_orders_fields)) {
	// Insert new field into orders table
	$add_new_field = array();
	$add_new_field[TABLE_ORDERS] = array (array (	"field_name" => "customers_telephone_country",
													"field_attr" => " varchar(64) default NULL ",
													"add_after" => "customers_country"
									 			)
										  );
	add_field ($add_new_field, false);
	// End of insert new field into orders table
	
	// Update for existing orders with dialing code
	$order_info_select_sql = "SELECT orders_id, customers_id, customers_country, customers_country_international_dialing_code, customers_telephone, billing_country FROM " . TABLE_ORDERS . " WHERE customers_country_international_dialing_code IS NOT NULL";
	$order_info_result_sql = tep_db_query($order_info_select_sql);
	
	while ($order_info_row = tep_db_fetch_array($order_info_result_sql)) {
		$country_array = array();
		$update_country_name_value = '';
		
		if (tep_not_null($order_info_row['customers_country_international_dialing_code'])) {
			$country_info_select_sql = "SELECT countries_id, countries_name FROM " . TABLE_COUNTRIES . " WHERE countries_international_dialing_code = '" . $order_info_row['customers_country_international_dialing_code'] . "'";
			$country_info_result_sql = tep_db_query($country_info_select_sql);
			
			if (tep_db_num_rows($country_info_result_sql) == 1) {
				$country_info_row = tep_db_fetch_array($country_info_result_sql);
				$update_country_name_value = $country_info_row['countries_name'];
			} else if (tep_db_num_rows($country_info_result_sql) > 1) {
				while ($country_info_row = tep_db_fetch_array($country_info_result_sql)) {
					$country_array[$country_info_row['countries_id']] = $country_info_row['countries_name'];
				}
				
				$profile_telephone_info_select_sql = "SELECT c.customers_telephone, coun.countries_name, coun.countries_international_dialing_code FROM " . TABLE_CUSTOMERS . " AS c LEFT JOIN " . TABLE_COUNTRIES . " AS coun ON (c.customers_country_dialing_code_id = coun.countries_id) WHERE c.customers_id ='" . (int)$order_info_row['customers_id'] . "'";
				$profile_telephone_info_result_sql = tep_db_query($profile_telephone_info_select_sql);
				$profile_telephone_info_row = tep_db_fetch_array($profile_telephone_info_result_sql);
				
				if ($profile_telephone_info_row['customers_telephone'] == $order_info_row['customers_telephone'] && $profile_telephone_info_row['countries_international_dialing_code'] == $order_info_row['customers_country_international_dialing_code']) {
					$update_country_name_value = $profile_telephone_info_row['countries_name'];
				} else {
					$updated_order_country_id = false;
					
					foreach($country_array as $country_id => $country_name) {
						if ($country_name == $order_info_row['billing_country']) {
							$update_country_name_value = $country_name;
							$updated_order_country_id = true;
						}
					}
					
					if ($updated_order_country_id == false) {
						foreach($country_array as $country_id => $country_name) {
							if ($country_name == $order_info_row['customers_country']) {
								$update_country_name_value = $country_name;
								$updated_order_country_id = true;
							}
						}
						
						if ($updated_order_country_id == false) {
							reset($country_array);
							$update_country_name_value = current($country_array);
						}
					}
				}
			}
			
			if (tep_not_null($update_country_name_value)) {
				$telephone_country_update_sql = "UPDATE " . TABLE_ORDERS . " set customers_telephone_country = '" . tep_db_input($update_country_name_value) . "' WHERE orders_id = '" . (int)$order_info_row['orders_id'] . "'";
				tep_db_query($telephone_country_update_sql);
			}
		}
	}
	// End of update for existing orders with dialing code
}
// End of batch update the existing orders records with the correct tel_country_id
?>