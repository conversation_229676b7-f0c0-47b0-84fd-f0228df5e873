<?
/*
  	$Id: version_1_12_4.php,v 1.3 2006/04/20 07:57:49 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define price_groups_id as index key in price_tags table
add_index_key (TABLE_PRICE_TAGS, 'index_price_groups_id', 'index', 'price_groups_id', $DBTables);
// End of define price_groups_id as index key in price_tags table

// Insert new records into configuration table (for markup margin percentage setting in supplier module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_MARKUP_MARGIN"] = array("insert" => " ('Buyback Markup Margin (%)', 'BUYBACK_MARKUP_MARGIN', '20', 'Set the percentage(%) for the buyback markup margin.', ".$row_sql["configuration_group_id"].", 25, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for markup margin percentage setting in supplier module)


// Reformat the telephone number stored in orders table
$order_country_code_select_sql = "	SELECT orders_id, customers_country_international_dialing_code, customers_telephone 
									FROM " . TABLE_ORDERS . " 
									WHERE customers_country_international_dialing_code IS NOT NULL";
$order_country_code_result_sql = tep_db_query($order_country_code_select_sql);

while($order_country_code_row = tep_db_fetch_array($order_country_code_result_sql)) {
	$country_code = $order_country_code_row['customers_country_international_dialing_code'];
	if (tep_not_null($country_code)) {
		$telephone = preg_replace('/[^\d]/', '', $order_country_code_row['customers_telephone']);
		
		$telephone =  preg_replace('/^(0+)(\d+)/', '$2', $telephone);
		
		while (strlen($telephone) > 10) {
			if (preg_match('/^('.$country_code.')(\d+)/', $telephone)) {
				$telephone =  preg_replace('/^('.$country_code.')(\d+)/', '$2', $telephone);
				$telephone =  preg_replace('/^(0+)(\d+)/', '$2', $telephone);
			} else {
				break;
			}
		}
		
		$order_telephone_update_sql = "	UPDATE " . TABLE_ORDERS . " 
										SET customers_telephone = '" . tep_db_input($telephone) . "' 
										WHERE orders_id ='" . (int)$order_country_code_row['orders_id'] . "'";
		tep_db_query($order_telephone_update_sql);
	}
}
// End of reformat the telephone number stored in orders table

?>