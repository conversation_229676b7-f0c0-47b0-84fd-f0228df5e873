<?
/*
  	$Id: version_1_13.php,v 1.3 2006/05/05 09:40:55 nickyap Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Delete records in admin_files table
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='static_page.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_file_delete_sql = array();
	$admin_file_delete_sql[$row_sql['admin_files_id']] = array(	"extra_where" => " admin_files_is_boxes=0 " );
	delete_records(TABLE_ADMIN_FILES, "admin_files_to_boxes", $admin_file_delete_sql, $DBTables);
	
	$admin_file_delete_sql = array();
	$admin_file_delete_sql["static_page.php"] = array(	"extra_where" => " admin_files_is_boxes=1 " );
	delete_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_file_delete_sql, $DBTables);
}
// End of delete records in admin_files table

// Insert new records into configuration table (for profit margin percentage setting in supplier module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_PROFIT_MARGIN"] = array("insert" => " ('Buyback Profit Margin (%)', 'BUYBACK_PROFIT_MARGIN', '20', 'Set the percentage(%) for the buyback profit margin.', ".$row_sql["configuration_group_id"].", 26, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for profit margin percentage setting in supplier module)

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='data_pool.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for custom product module)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["cdkey.php"] = array(	"insert" => " ('cdkey.php', 0, '".$row_sql[admin_files_id]."', '1') ",
													"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   								);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='cdkey.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for custom product module)	
}

//Start of create table custom_products_code to store cdkey codes
$add_new_tables = array();
$add_new_tables["custom_products_code"] = array (	"structure" => "CREATE TABLE `custom_products_code` (
																		`custom_products_code_id` int(11) NOT NULL auto_increment,
																		`products_id` int(11) NOT NULL default '0',
																		`status_id` tinyint(1) NOT NULL default '0',
																		`code_date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																		`code_date_modified` datetime NOT NULL default '0000-00-00 00:00:00',
																		`code` text NOT NULL default '',
																		`remarks` text NOT NULL  default '',
																		PRIMARY KEY  (`custom_products_code_id`),
																		KEY `index_products_id` (`products_id`, `status_id`)
																		)TYPE=MyISAM;" ,
													"data" => ""
												);
											
add_new_tables ($add_new_tables, $DBTables);
//End of create table.

// Insert new records into custom_products_type table (for CD Key)
$custom_product_insert_sql = array();
$custom_product_insert_sql[2] = array(	"insert" => " (2, 'CD Key', 0) ",
								 		"update" => " custom_products_type_name='CD Key', data_pool_id=0 "
				   					);
insert_new_records("custom_products_type", "custom_products_type_id", $custom_product_insert_sql, $DBTables, "(custom_products_type_id, custom_products_type_name, data_pool_id)");
// End of insert new records into custom_products_type table (for CD Key)
?>