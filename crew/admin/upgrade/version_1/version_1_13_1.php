<?
/*
  	$Id: version_1_13_1.php,v 1.2 2006/05/11 11:00:25 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define orders_log_orders_id as index key in orders_log_table table
add_index_key (TABLE_ORDERS_LOG_TABLE, 'index_orders_log_orders_id', 'index', 'orders_log_orders_id', $DBTables);
// End of define orders_log_orders_id as index key in orders_log_table table

// <PERSON>'s changes
// Insert new records into admin_files_actions table (for permission on viewing cd key details)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_CDKEY_DETAILS"] = array("insert" => " ('ORDER_CDKEY_DETAILS', 'CD Key Details Information', ".$row_sql["admin_files_id"].", '1', 8)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing cd key details)

// Chan's changes
// Create supplier_tasks_setting and supplier_tasks_allocation tables
$add_new_tables = array();
$add_new_tables["supplier_tasks_setting"] = array(	"structure" => "CREATE TABLE `supplier_tasks_setting` (
																		`suppliers_id` INT(11) NOT NULL DEFAULT '0',
																		`custom_products_type_id` INT(11) NOT NULL DEFAULT '0',
																		`supplier_tasks_allocation_slots` SMALLINT NOT NULL DEFAULT '0',
																		PRIMARY KEY (`suppliers_id` , `custom_products_type_id`) 
																	) TYPE = MYISAM ;" ,
													"data" => ""
												);

$add_new_tables["supplier_tasks_allocation"] = array(	"structure" => "CREATE TABLE `supplier_tasks_allocation` (
																			`orders_products_id` INT(11) NOT NULL DEFAULT '0',
																			`supplier_tasks_start_time` DATETIME NULL,
																			`supplier_tasks_time_reference` DATETIME NOT NULL,
																			`supplier_tasks_time_taken` INT(11) UNSIGNED NOT NULL DEFAULT '0',
																			`suppliers_id` INT(11) NOT NULL DEFAULT '0',
																			`supplier_firstname` VARCHAR(32) NOT NULL ,
																			`supplier_lastname` VARCHAR(32) NOT NULL ,
																			`supplier_tasks_status` TINYINT(1) NOT NULL DEFAULT '0',
																			`supplier_tasks_allocation_progress` VARCHAR(255) NOT NULL,
																			`supplier_tasks_allocation_info` TEXT NOT NULL,
																			PRIMARY KEY (`orders_products_id`) 
																		) TYPE = MYISAM ;" ,
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create supplier_tasks_setting and supplier_tasks_allocation tables

// Insert new records into admin_files_actions table (for permission on allocate tasks to supplier in Edit Order page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_SUPPLIERS_TASK_ALLOCATION"] = array("insert" => " ('ORDER_SUPPLIERS_TASK_ALLOCATION', 'Suppliers Task Allocation', ".$row_sql["admin_files_id"].", '1', 8)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on allocate tasks to supplier in Edit Order page)

$existing_orders_products_fields = get_table_fields('orders_products');
if (!in_array('custom_products_type_id', $existing_orders_products_fields)) {
	$update_custom_products_type_id = true;
} else {
	$update_custom_products_type_id = false;
}

// Insert new field into orders_products table
$add_new_field = array();
$add_new_field['orders_products'] = array (	array (	"field_name" => "custom_products_type_id",
													"field_attr" => " INT(11) NOT NULL DEFAULT '0' ",
													"add_after" => "products_pre_order"
							 					)
									  		);
add_field ($add_new_field, false);
// End of insert new field into orders_products table

// Updating custom_products_type_id to 1 for those previously ordered custom products
if ($update_custom_products_type_id) {
	$orders_products_custom_type_update_sql = "	UPDATE orders_custom_products AS ocp 
												INNER JOIN orders_products AS op 
													ON (ocp.orders_products_id=op.orders_products_id) 
												SET op.custom_products_type_id=1 
												WHERE 1";
	tep_db_query($orders_products_custom_type_update_sql);
}
// End of updating custom_products_type_id to 1 for those previously ordered custom products

// Insert new records into admin_files table (for custom product progress report)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='data_pool.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["progress_report.php"] = array(	"insert" => " ('progress_report.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   									);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='progress_report.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for custom product progress report)
?>