<?
/*
  	$Id: version_1_13_10.php,v 1.1 2006/07/06 08:38:32 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into data_pool_options_tags and data_pool_options tables
$add_new_field = array();

$add_new_field['data_pool_options_tags'] = array (array (	"field_name" => "data_pool_options_show_supplier",
															"field_attr" => " tinyint(1) NOT NULL DEFAULT '0' ",
															"add_after" => "data_pool_options_required"
								 						)
										  			);
$add_new_field['data_pool_options'] = array (array ("field_name" => "data_pool_options_show_supplier",
													"field_attr" => " tinyint(1) NOT NULL DEFAULT '0' ",
													"add_after" => "data_pool_options_required"
				 									)
						  					);

add_field ($add_new_field, false);
// End of insert new fields into data_pool_options_tags and data_pool_options tables

?>