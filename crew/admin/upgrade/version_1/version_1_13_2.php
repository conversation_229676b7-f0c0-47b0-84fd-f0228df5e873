<?
/*
  	$Id: version_1_13_2.php,v 1.2 2006/05/17 08:13:35 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define orders_status as index key in orders table
add_index_key (TABLE_ORDERS, 'index_orders_status', 'index', 'orders_status', $DBTables);
// End of define orders_status as index key in orders table

// Insert new records into admin_files_actions table (for permission on view all cd key images in CD Key management page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='cdkey.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CP_VIEW_CDKEY_IMAGES"] = array("insert" => " ('CP_VIEW_CDKEY_IMAGES', 'View CD key images', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on view all cd key images in CD Key management page)

// Insert new fields into custom_products_code table
$add_new_field = array();
$add_new_field['custom_products_code'] = array (array (	"field_name" => "file_name",
														"field_attr" => " VARCHAR(50) NOT NULL ",
														"add_after" => "status_id"
							 						),
							 					array (	"field_name" => "file_type",
														"field_attr" => " VARCHAR(5) NOT NULL ",
														"add_after" => "file_name"
							 						),
							 					array (	"field_name" => "code_uploaded_by",
														"field_attr" => " VARCHAR(65) NOT NULL ",
														"add_after" => "code_date_modified"
							 						)
									  			);
add_field ($add_new_field, false);
// End of insert new fields into custom_products_code table
?>