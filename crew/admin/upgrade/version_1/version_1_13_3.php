<?
/*
  	$Id: version_1_13_3.php,v 1.1 2006/05/20 02:46:39 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for advertisement)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Advertisement'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["GOOGLE_ANALYTICS_ACCOUNT_ID"] = array("insert" => " ('Google Analytics Account ID', 'GOOGLE_ANALYTICS_ACCOUNT_ID', '', 'Your Google Analytics Account ID', ".$row_sql["configuration_group_id"].", 30, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for advertisement)

// Change field structure for code in custom_products_code
$change_field_structure = array();

$change_field_structure[TABLE_CUSTOM_PRODUCTS_CODE] = array (	array (	"field_name" => "code",
																		"field_attr" => " LONGTEXT "
																		)
															);

change_field_structure ($change_field_structure);
// End of change field structure for code in custom_products_code
?>