<?
/*
  	$Id: version_1_13_4.php,v 1.2 2006/05/25 05:18:39 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for supplier module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["JUNIOR_PURCHASE_TEAM_EMAIL"] = array("insert" => " ('Junior Purchase Team Email Address', 'JUNIOR_PURCHASE_TEAM_EMAIL', '', 'Email address to which the email will be send to whenever supplier submit the order list (No price info in this email).<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', '".$row_sql["configuration_group_id"]."', 16, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for supplier module)

// Insert new records into admin_files_actions table (for permission on viewing unit price in supplier pricing page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers_pricing.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_PRICING_UNIT_PRICE_INFO"] = array("insert" => " ('SUPPLIER_PRICING_UNIT_PRICE_INFO', 'View products unit price', ".$row_sql["admin_files_id"].", '1', 20)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing unit price in supplier pricing page)
?>