<?
/*
  	$Id: version_1_13_5.php,v 1.1 2006/05/31 04:32:09 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on reversing powerleveling progress status and make the payment for custom products to supplier)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='progress_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["REVERSIBLE_PWL_PROGRESS_STATUS"] = array("insert" => " ('REVERSIBLE_PWL_PROGRESS_STATUS', 'Reverse powerleveling progress status', ".$row_sql["admin_files_id"].", '1', 10)" );
	$admin_files_actions_insert_sql["SUPPLIER_CP_PAYMENT_DETAILS"] = array("insert" => " ('SUPPLIER_CP_PAYMENT_DETAILS', 'Payment Details', ".$row_sql["admin_files_id"].", '1', 20)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on reversing powerleveling progress status and make the payment for custom products to supplier)

// Insert new records into admin_files_actions table (for permission on assigning custom products slots for suppliers)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_CP_SLOTS"] = array("insert" => " ('SUPPLIER_CP_SLOTS', 'Assign custom products slot', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on assigning custom products slots for suppliers)

// Insert missing county for UK
$select_sql = "	SELECT countries_id 
				FROM countries 
				WHERE countries_name='United Kingdom'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$uk_county_array = array (	'ANT' => 'Antrim',
								'ARM' => 'Armagh',
								'CNI' => 'Channel Islands',
								'DWL' => 'Down',
								'FMN' => 'Fermanagh',
								'IOM' => 'Isle of Man',
								'IOS' => 'Isles of Scilly',
								'DRY' => 'Londonderry',
								'MDX' => 'Middlesex',
								'TYR' => 'Tyrone'
							);
	
	foreach ($uk_county_array as $code => $county) {
		$uk_county_insert_sql = array();
		$uk_county_insert_sql[$code] = array ( "insert" => " ('".$row_sql["countries_id"]."', '" . tep_db_input($code) . "', '" . tep_db_input($county) . "' ) " );
		
		insert_new_records(TABLE_ZONES, "zone_code", $uk_county_insert_sql, $DBTables, "(zone_country_id, zone_code, zone_name)", " zone_country_id='".$row_sql["countries_id"]."' AND zone_code='".tep_db_input($code)."'");
	}
}

// End of insert missing county for UK
?>