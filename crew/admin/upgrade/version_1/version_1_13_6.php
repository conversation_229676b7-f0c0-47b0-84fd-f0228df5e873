<?
/*
  	$Id: version_1_13_6.php,v 1.2 2006/06/12 05:43:11 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Change field structure for changed_by in orders_status_history table
$change_field_structure = array();
$change_field_structure[TABLE_ORDERS_STATUS_HISTORY] = array (array (	"field_name" => "changed_by",
																		"field_attr" => " varchar(128) NOT NULL default '' "
														 			)
																);
change_field_structure ($change_field_structure);
// End of change field structure for changed_by in orders_status_history table

// <PERSON>'s changes
// Create supplier_tasks_setting and supplier_tasks_allocation tables
$add_new_tables = array();
$add_new_tables["supplier_tasks_allocation_history"] = array(	"structure" => "CREATE TABLE `supplier_tasks_allocation_history` (
																				  	`supplier_tasks_allocation_history_id` int(11) NOT NULL auto_increment,
																				  	`orders_products_id` int(11) NOT NULL default '0',
																				  	`supplier_tasks_status` tinyint(1) default NULL,
																				  	`date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																				  	`comments` text,
																				  	`changed_by` varchar(128) NOT NULL default '',
																				  	`user_role` varchar(16) NOT NULL default '',
																				  	`notify_recipient` tinyint(1) NOT NULL default '0',
																				  	PRIMARY KEY  (`supplier_tasks_allocation_history_id`)
																				);" ,
																"data" => ""
															);

add_new_tables ($add_new_tables, $DBTables);
// End of create supplier_tasks_setting and supplier_tasks_allocation tables

// Insert new records into admin_files_actions table (for permission on updating powerleveling progress status)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='progress_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["UPDATE_PWL_PROGRESS_STATUS"] = array("insert" => " ('UPDATE_PWL_PROGRESS_STATUS', 'Update powerleveling progress status', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on updating powerleveling progress status)

// Update records in admin_files_actions table (assign sort order to each admin file actions)
$admin_files_order_update_sql = array();

$admin_files_order_update_sql["REVERSIBLE_PWL_PROGRESS_STATUS"] = array("update" => " admin_files_sort_order='20' " );
$admin_files_order_update_sql["SUPPLIER_CP_PAYMENT_DETAILS"] = array("update" => " admin_files_sort_order='30' " );

update_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_order_update_sql, $DBTables);
// End of update records in admin_files_actions table (assign sort order to each admin file actions)

// Insert new fields into products_purchases_lists table
$add_new_field = array();

$add_new_field['products_purchases_lists'] = array (array (	"field_name" => "products_purchases_lists_sort_order",
															"field_attr" => " int(5) NOT NULL default '50000' ",
															"add_after" => "products_purchases_lists_qty_round_up"
							 								)
									  				);

add_field ($add_new_field, false);
// End of insert new fields into products_purchases_lists table

?>