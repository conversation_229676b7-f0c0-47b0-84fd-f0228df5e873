<?
/*
  	$Id: version_1_13_7.php,v 1.2 2006/06/20 05:18:23 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (Receiver's name and email address for manual cd key stock adjustment)
$conf_insert_sql = array();

$conf_insert_sql["MANUAL_CDKEY_ADDITION_EMAIL"] = array("insert" => " ('Manual CD Key Addition Email Address', 'MANUAL_CDKEY_ADDITION_EMAIL', '', 'Email address to which the email will be send to whenever someone manually add the CD Key stock.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 8, NULL, NOW(), NULL, NULL)" );
$conf_insert_sql["MANUAL_CDKEY_DEDUCTION_EMAIL"] = array("insert" => " ('Manual CD Key Deduction Email Address', 'MANUAL_CDKEY_DEDUCTION_EMAIL', '', 'Email address to which the email will be send to whenever someone manually deducts the CD Key stock.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 9, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (Receiver's name and email address for manual cd key stock adjustment)

// Insert new fields into custom_products_type table
$add_new_field = array();
$add_new_field['custom_products_type'] = array (array (	"field_name" => "custom_products_add_stock_email",
														"field_attr" => " VARCHAR(64) NOT NULL ",
														"add_after" => "data_pool_id"
							 						),
							 					array (	"field_name" => "custom_products_deduct_stock_email",
														"field_attr" => " VARCHAR(64) NOT NULL ",
														"add_after" => "custom_products_add_stock_email"
							 						)
									  			);
add_field ($add_new_field, false);
// End of insert new fields into custom_products_code table

// Update records in custom_products_type table (set the manual add / deduct stock configuration key for CD Key product type)
$custom_products_type_update_sql = array();
$custom_products_type_update_sql["2"] = array("update" => " custom_products_add_stock_email='MANUAL_CDKEY_ADDITION_EMAIL', custom_products_deduct_stock_email='MANUAL_CDKEY_DEDUCTION_EMAIL' ");

update_records('custom_products_type', "custom_products_type_id", $custom_products_type_update_sql, $DBTables);
// End of update records in custom_products_type table (set the manual add / deduct stock configuration key for CD Key product type)

// Chan's changes
// Create supplier_cp_payments, supplier_cp_payments_products and supplier_cp_payments_history tables
$add_new_tables = array();

$add_new_tables["supplier_cp_payments"] = array("structure" => "CREATE TABLE `supplier_cp_payments` (
																	`supplier_cp_payments_id` INT( 11 ) NOT NULL auto_increment,
																	`suppliers_id` INT( 11 ) NOT NULL DEFAULT '0',
																	`supplier_cp_payments_amount` DECIMAL( 15, 4 ) NOT NULL DEFAULT '0.0000',
																	`supplier_cp_payments_tax` DECIMAL( 15, 4 ) NOT NULL DEFAULT '0.0000',
																	`supplier_cp_payments_total` DECIMAL( 15, 4 ) NOT NULL DEFAULT '0.0000',
																	`supplier_cp_payments_date` DATETIME NOT NULL ,
																	`supplier_cp_payments_last_modified` DATETIME NOT NULL ,
																	`supplier_cp_payments_status` SMALLINT(1) NOT NULL DEFAULT '0',
																	`suppliers_firstname` VARCHAR( 32 ) NOT NULL ,
																	`suppliers_lastname` VARCHAR( 32 ) NOT NULL ,
																	`suppliers_street_address` VARCHAR( 64 ) NOT NULL ,
																	`suppliers_suburb` VARCHAR( 64 ) NULL ,
																	`suppliers_city` VARCHAR( 32 ) NOT NULL ,
																	`suppliers_postcode` VARCHAR( 10 ) NOT NULL ,
																	`suppliers_state` VARCHAR( 32 ) NULL ,
																	`suppliers_country` VARCHAR( 64 ) NOT NULL ,
																	`suppliers_telephone` VARCHAR( 32 ) NOT NULL ,
																	`suppliers_email_address` VARCHAR( 96 ) NOT NULL ,
																	`currency` CHAR( 3 ) NULL ,
																	`currency_value` DECIMAL( 14, 6 ) NULL ,
																	PRIMARY KEY ( `supplier_cp_payments_id` ) 
																) TYPE = MYISAM ;" ,
												"data" => ""
												);

$add_new_tables["supplier_cp_payments_products"] = array(	"structure" => "CREATE TABLE `supplier_cp_payments_products` (
																				`supplier_cp_payments_id` INT( 11 ) NOT NULL DEFAULT '0',
																				`orders_products_id` INT( 11 ) NOT NULL DEFAULT '0',
																				`supplier_cp_payments_products_paid_amount` DECIMAL( 15, 4 ) NOT NULL DEFAULT '0.0000',
																				`supplier_cp_payments_type` TINYINT( 1 ) NOT NULL DEFAULT '2',
																				PRIMARY KEY ( `supplier_cp_payments_id` , `orders_products_id` ) 
																			) TYPE = MYISAM ;" ,
															"data" => ""
														);

$add_new_tables["supplier_cp_payments_history"] = array(	"structure" => "CREATE TABLE `supplier_cp_payments_history` (
																				`supplier_cp_payments_history_id` INT( 11 ) NOT NULL AUTO_INCREMENT PRIMARY KEY ,
																				`supplier_cp_payments_id` INT( 11 ) NOT NULL ,
																				`supplier_cp_payments_status` SMALLINT NOT NULL ,
																				`date_added` DATETIME NOT NULL ,
																				`supplier_notified` TINYINT( 1 ) NOT NULL ,
																				`comments` TEXT NULL ,
																				`changed_by` VARCHAR( 128 ) NOT NULL 
																			) TYPE = MYISAM ;" ,
															"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// End of create supplier_cp_payments, supplier_cp_payments_products and supplier_cp_payments_history tables

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='data_pool.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for custom product payment)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["custom_product_payment.php"] = array(	"insert" => " ('custom_product_payment.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   											);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='custom_product_payment.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for custom product payment)	
}

// Insert new fields into supplier_tasks_allocation table
$add_new_field = array();

$add_new_field['supplier_tasks_allocation'] = array (array ("field_name" => "supplier_payable_adjust",
															"field_attr" => " DECIMAL(15, 4) NOT NULL DEFAULT '0.0000' ",
															"add_after" => ""
								 						)
									  				);

$add_new_field['supplier_tasks_setting'] = array (array (	"field_name" => "supplier_tasks_ratio",
															"field_attr" => " SMALLINT(6) NOT NULL DEFAULT '0' ",
															"add_after" => ""
								 						)
									  				);

add_field ($add_new_field, false);
// End of insert new fields into supplier_tasks_allocation table
?>