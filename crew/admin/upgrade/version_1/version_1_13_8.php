<?
/*
  	$Id: version_1_13_8.php,v 1.4 2006/06/29 08:04:36 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on saving criteria in Customer Lists Criteria page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SAVE_CUSTOMER_LISTS_CRITERIA"] = array("insert" => " ('SAVE_CUSTOMER_LISTS_CRITERIA', 'Save criteria', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on saving criteria in Customer Lists Criteria page)

// Insert new records into configuration table (Receiver's name and email address for cd key low stock e-mail)
$conf_insert_sql = array();

$conf_insert_sql["CDKEY_LOW_STOCK_EMAIL"] = array("insert" => " ('CD Key Low Stock Email Address', 'CDKEY_LOW_STOCK_EMAIL', '', 'Email address to which the CD Key low stock email will be send to.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 6, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (Receiver's name and email address for cd key low stock e-mail)

// Insert new fields into custom_products_type and supplier tables
$add_new_field = array();

$add_new_field['custom_products_type'] = array (array (	"field_name" => "custom_products_low_stock_email",
														"field_attr" => " VARCHAR(64) NOT NULL ",
														"add_after" => "data_pool_id"
							 						)
									  			);
$add_new_field['supplier'] = array (array (	"field_name" => "supplier_remarks",
											"field_attr" => " text ",
											"add_after" => ""
				 							)
						  			);

add_field ($add_new_field, false);
// End of insert new fields into custom_products_type and supplier tables

// Update records in custom_products_type table (set the low stock configuration key for CD Key product type)
$custom_products_type_update_sql = array();
$custom_products_type_update_sql["2"] = array("update" => " custom_products_low_stock_email='CDKEY_LOW_STOCK_EMAIL' ");

update_records('custom_products_type', "custom_products_type_id", $custom_products_type_update_sql, $DBTables);
// End of update records in custom_products_type table (set the low stock configuration key for CD Key product type)

// Insert new records into configuration table (Show / hide custom product progress bar in Customer Order History page)
$conf_insert_sql = array();

$conf_insert_sql["SHOW_CUSTOMER_CP_PROGRESS_STATUS"] = array("insert" => " ('Display Custom Product Progress Status', 'SHOW_CUSTOMER_CP_PROGRESS_STATUS', 'false', 'Do you want to display the custom product progress bar status in Customer Order History page?', 8, 40, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (Show / hide custom product progress bar in Customer Order History page)

// Insert new records into admin_files_actions table (for permission not to notify customer)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["EDIT_ORDER_NOT_NOTIFY_CUSTOMER"] = array("insert" => " ('EDIT_ORDER_NOT_NOTIFY_CUSTOMER', 'Not to notify customer on order comment', ".$row_sql["admin_files_id"].", '1', '17')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission not to notify customer)

// Change field structure for restrict_to_products and restrict_to_categories in coupons table
$change_field_structure = array();

$change_field_structure['coupons'] = array (array (	"field_name" => "restrict_to_products",
													"field_attr" => " text "
									 			),
									 		array (	"field_name" => "restrict_to_categories",
													"field_attr" => " text "
									 			)
											);

change_field_structure ($change_field_structure);
// End of change field structure for restrict_to_products and restrict_to_categories in coupons table

?>