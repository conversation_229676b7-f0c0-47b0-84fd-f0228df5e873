<?
/*
  	$Id: version_1_13_9.php,v 1.2 2006/07/04 08:52:18 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (Shopping Cart Price Update Disclaimer)
$conf_insert_sql = array();

$conf_insert_sql["SHOPPING_CART_PRICE_DISCLAIMER"] = array("insert" => " ('Shopping Cart Price Update Disclaimer', 'SHOPPING_CART_PRICE_DISCLAIMER', '', 'Disclaimer shown in the shopping cart page to mention that the product\'s price may be updated.', 1, 70, NULL, NOW(), NULL, 'tep_cfg_textarea(')" );
$conf_insert_sql["CANCELLATION_VOUCHER_LOST_EMAIL"] = array("insert" => " ('Cancellation Voucher Lost Email', 'CANCELLATION_VOUCHER_LOST_EMAIL', '', 'Email address to which the email will be send to when there is cancellation of voucher which resulting a lost.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 1, 15, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (Shopping Cart Price Update Disclaimer )

// Insert new records into admin_files_actions table (for permission on viewing affiliate info)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["VIEW_AFFILIATE_INFO"] = array("insert" => " ('VIEW_AFFILIATE_INFO', 'View affiliate information', ".$row_sql["admin_files_id"].", '1', 3)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing affiliate info)

// Update records in admin_files_actions table (Changes to the permissions title)
$admin_files_actions_update_sql = array();

$admin_files_actions_update_sql["REVERSIBLE_PWL_PROGRESS_STATUS"] = array("update" => " admin_files_actions_name='Reverse progress status from \"Done\" to \"In Progress/Hold\"' " );
$admin_files_actions_update_sql["UPDATE_PWL_PROGRESS_STATUS"] = array("update" => " admin_files_actions_name='Update progress status from \"Hold\" to \"In Progress\"' " );

update_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_update_sql, $DBTables);
// End of update records in admin_files_actions table (Changes to the permissions title)
?>