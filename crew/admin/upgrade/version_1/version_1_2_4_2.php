<?
/*
  $Id: version_1_2_4_2.php,v 1.3 2005/03/24 09:27:35 weichen Exp $

  Developer: <PERSON>
  Copyright (c) 2004 SKC Ventrue

  Released under the GNU General Public License
*/

// Insert new field into infolinks_groups and affiliate_sales tables
$add_new_field[TABLE_INFOLINKS_GROUPS] = array (	array (	"field_name" => "infolinks_groups_seperator_image",
								 							"field_attr" => " tinytext NOT NULL ",
								 							"add_after" => ""
								 				  		  )
								 				);
$add_new_field[TABLE_AFFILIATE_SALES] = array 	(	array (	"field_name" => "affiliate_commission_reclaim",
								 							"field_attr" => " int(1) NOT NULL default '0' ",
								 							"add_after" => ""
								 				  	  )
								 				);
add_field ($add_new_field);
// End of insert new field into infolinks_groups and affiliate_sales tables

// Update records in configuration table (for affiliate eligible payment status)
$select_sql = "	SELECT * 
				FROM " . TABLE_CONFIGURATION . "
				WHERE configuration_key='AFFILIATE_COMMISSSION_ORDER_STATUS'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	; // do not do update
} else {
	$conf_update_sql["AFFILIATE_PAYMENT_ORDER_MIN_STATUS"] = array(	"update" => "  configuration_key='AFFILIATE_COMMISSSION_ORDER_STATUS',  configuration_title='Commission Order Status', 
																				   configuration_value='3,4', configuration_description='The status an order must have at least, to be billed. Seperated by \',\' for multiple values.' " );
	
	update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
}
// End of update records in configuration table (for affiliate eligible payment status)
?>