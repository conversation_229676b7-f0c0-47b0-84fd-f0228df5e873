<?
/*
  $Id: version_1_2_4_3.php,v 1.3 2005/01/04 03:27:25 weichen Exp $

  Developer: <PERSON>
  Copyright (c) 2004 SKC Ventrue

  Released under the GNU General Public License
*/

// Insert new field into orders table
$add_new_field[TABLE_ORDERS] = array (	array (	"field_name" => "pm_2CO_cc_owner_firstname",
								 				"field_attr" => " varchar(64) default NULL ",
								 				"add_after" => ""
								 			   ),
								 		array (	"field_name" => "pm_2CO_cc_owner_lastname",
								 				"field_attr" => " varchar(64) default NULL ",
								 				"add_after" => ""
								 			   )	   
									);
add_field ($add_new_field);
// End of insert new field into orders table

// Change field structure in infolinks_contents table
$change_new_field[TABLE_INFOLINKS_CONTENTS] = array (	array (	"field_name" => "infolinks_contents_page",
								 								"field_attr" => " int(11) NOT NULL default '1' "
								 			   				  )
													);
change_field_structure ($change_new_field);
// End of change field structure in infolinks_contents table
?>