<?
/*
  	$Id: version_1_2_4_4.php,v 1.2 2005/01/06 10:11:24 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Update records in configuration table (for sending mail options)
$conf_update_sql["EMAIL_TRANSPORT"] = array("update" => " configuration_value='smtp' " );
$conf_update_sql["EMAIL_USE_HTML"] = array(	"update" => " configuration_value='true' " );

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (for affiliate eligible payment status)

// Change field structure in orders table
$change_new_field[TABLE_ORDERS] = array (	array (	"field_name" => "payment_method",
								 					"field_attr" => " varchar(64) NOT NULL default '' "
								 			   	   )
										);
change_field_structure ($change_new_field);
// End of change field structure in infolinks_contents table

// Insert new field into products table
$add_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_display",
								 					"field_attr" => " tinyint(1) NOT NULL default '1' ",
								 					"add_after" => "products_status"
								 			   )	   
										);
add_field ($add_new_field);
// End of insert new field into products table

// Update records in orders table (for payment_method)
$orders_update_sql[TABLE_ORDERS] = array(	array(	"field_name" => "payment_method",
													"update" => " payment_method='PayPal' ",
													"where_str" => " TRIM(LCASE(payment_method))='paypal/credit cards' ")
										 );

advance_update_records($orders_update_sql, $DBTables);
// End of update records in orders table (for payment_method)
?>