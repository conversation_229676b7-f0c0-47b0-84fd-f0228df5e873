<?
/*
  	$Id: version_1_2_4_5.php,v 1.7 2005/03/24 09:27:35 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into orders_products and products tables
$add_new_field[TABLE_ORDERS_PRODUCTS] = array (	array (	"field_name" => "products_bundle_id",
								 						"field_attr" => " smallint(6) NOT NULL default '0' ",
								 						"add_after" => "products_quantity"
								 			   			)
											  );

$add_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_skip_inventory",
								 					"field_attr" => " tinyint(1) NOT NULL default '0' ",
								 					"add_after" => "products_display"
								 			   ),
								 			array (	"field_name" => "products_negative_qty",
								 					"field_attr" => " tinyint(1) NOT NULL default '0' ",
								 					"add_after" => "products_skip_inventory"
								 			   )	   
										);
add_field ($add_new_field);
// End of insert new field into orders_products and products tables

// Insert new records into configuration table (for payment legend colour)
$conf_insert_sql["MODULE_PAYMENT_PAYPAL_LEGEND_COLOUR"] = array("insert" => " ('Legend Display Colour', 'MODULE_PAYMENT_PAYPAL_LEGEND_COLOUR', '#0066FF', 'Colour to indicate PayPal payment method (default is grey)', 6, 2, NULL, 'now()', NULL, 'tep_cfg_colour_palette(')" );

$conf_insert_sql["MODULE_PAYMENT_2CHECKOUT_LEGEND_COLOUR"] = array("insert" => " ('Legend Display Colour', 'MODULE_PAYMENT_2CHECKOUT_LEGEND_COLOUR', '#66FFCC', 'Colour to indicate 2CheckOut payment method (default is grey)', 6, 2, NULL, 'now()', NULL, 'tep_cfg_colour_palette(')" );

$conf_insert_sql["MODULE_PAYMENT_MONEYORDER_LEGEND_COLOUR"] = array("insert" => " ('Legend Display Colour', 'MODULE_PAYMENT_MONEYORDER_LEGEND_COLOUR', '#FF0000', 'Colour to indicate Check/Money Order payment method (default is grey)', 6, 2, NULL, 'now()', NULL, 'tep_cfg_colour_palette(')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for payment legend colour)

// Change field structure in products and products_bundles tables
$change_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_quantity",
								 					"field_attr" => " int(4) NULL default '0' "
								 				  )
										  );
$change_new_field[TABLE_PRODUCTS_BUNDLES] = array (	array (	"field_name" => "subproduct_qty",
								 							"field_attr" => " smallint(4) NOT NULL default '0' "
								 				  )
										  );
change_field_structure ($change_new_field);
// End of change field structure in products and products_bundles tables

// Update records in orders table (for payment_method) and products_description (set location to empty if '----')
$orders_update_sql[TABLE_ORDERS] = array(	array(	"field_name" => "payment_method",
													"update" => " payment_method='Credit Card (MasterCard/Visa/AMEX/Discover)' ",
													"where_str" => " TRIM(LCASE(payment_method))='credit card (mastercard/visa/ame' ")
										 );

$orders_update_sql[TABLE_PRODUCTS_DESCRIPTION] = array(	array(	"field_name" => "products_location",
																"update" => " products_location='' ",
																"where_str" => " TRIM(LCASE(products_location)) LIKE '--%' ")
										 );

advance_update_records($orders_update_sql, $DBTables);
// End of update records in orders table (for payment_method) and products_description (set location to empty if '----')

?>