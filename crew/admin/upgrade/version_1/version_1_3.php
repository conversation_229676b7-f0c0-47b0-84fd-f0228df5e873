<?
/*
  $Id: version_1_3.php,v 1.1 2005/01/20 03:52:53 weichen Exp $

  Developer: <PERSON>
  Copyright (c) 2004 SKC Ventrue

  Released under the GNU General Public License
*/

// Insert new field into products_to_categories table
$add_new_field[TABLE_PRODUCTS_TO_CATEGORIES] = array (	array (	"field_name" => "products_is_link",
								 								"field_attr" => " tinyint(1) NOT NULL default '0' ",
								 								"add_after" => ""
								 			   			)
											  );

add_field ($add_new_field);
// End of insert new field into products_to_categories table

// Change field structure in products and products_bundles tables
$change_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_cat_path",
								 					"field_attr" => " varchar(255) NOT NULL default '' "
								 				  )
										  );

change_field_structure ($change_new_field);
// End of change field structure in products and products_bundles tables

// Update records in configuration table
$conf_update_sql["MAX_DISPLAY_BESTSELLERS"] = array("update" => " configuration_value='5' " );
$conf_update_sql["MAX_DISPLAY_LATEST_NEWS"] = array("update" => " configuration_value='3' " );
$conf_update_sql["MAX_DISPLAY_LATEST_NEWS_PAGE"] = array("update" => " configuration_value='10' " );
$conf_update_sql["ENTRY_PASSWORD_MIN_LENGTH"] = array("update" => " configuration_value='6' " );

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table

?>