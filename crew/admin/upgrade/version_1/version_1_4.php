<?
/*
  	$Id: version_1_4.php,v 1.2 2006/03/21 05:44:18 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin table (for selection of product id in log input page)
$admin_files_insert_sql = array();
$admin_files_insert_sql["popup_products_list.php"] = array	(	"insert" => " ('popup_products_list.php', 0, 1, '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes=1, admin_groups_id='1' "
				   											);
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='popup_products_list.php' AND admin_files_is_boxes=0 ");

$admin_files_insert_sql = array();
$admin_files_insert_sql["stock_report.php"] = array	(	"insert" => " ('stock_report.php', 0, 8, '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes=8, admin_groups_id='1' "
													);
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='stock_report.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin table (for selection of product id in log input page)

// Create log_table table
$add_new_tables["log_table"] = array (	"structure" => " CREATE TABLE `log_table` (
															`log_id` int(11) NOT NULL auto_increment,
															`log_admin_id` int(11) NOT NULL default '0',
															`log_ip` varchar(15) NOT NULL default '',
	  														`log_time` datetime default NULL,
															`log_products_id` int(11) NOT NULL default '0',
															`log_system_messages` text NOT NULL, 
															`log_user_messages` text NOT NULL, 
															`log_field_name` varchar(255) NOT NULL default '',
															`log_from_value` varchar(255) NOT NULL default '',
															`log_to_value` varchar(255) NOT NULL default '',
	  														PRIMARY KEY  (`log_id`)
														  ) TYPE=MyISAM;" ,
										"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create log_table table

// Change field structure in products table
$change_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_quantity_order",
								 					"field_attr" => " int(4) default NULL "
								 				  )
										  );

change_field_structure ($change_new_field);
// End of change field structure in products table

?>