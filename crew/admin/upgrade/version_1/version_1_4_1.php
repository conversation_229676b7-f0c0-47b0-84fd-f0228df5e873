<?
/*
  	$Id: version_1_4_1.php,v 1.3 2006/03/21 05:44:49 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Delete records in admin_files table
$select_sql = "	SELECT * 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='categories_adv.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_file_delete_sql["categories.php"] = array(	"unique" => "1" );
	
	delete_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_file_delete_sql, $DBTables);
}
// End of delete records in admin_files table

// Update records in admin_files table (renaming categories_adv.php to categories.php)
$admin_file_update_sql[TABLE_ADMIN_FILES] = array(	array(	"field_name" => "admin_files_name",
															"update" => " admin_files_name='categories.php' ",
															"where_str" => " TRIM(LCASE(admin_files_name))='categories_adv.php' AND admin_files_is_boxes=0 ")
												 );

advance_update_records($admin_file_update_sql, $DBTables);
// End of update records in admin_files table (renaming categories_adv.php to categories.php)
?>