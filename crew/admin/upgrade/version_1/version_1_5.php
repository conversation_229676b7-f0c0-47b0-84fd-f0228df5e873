<?
/*
  	$Id: version_1_5.php,v 1.2 2006/03/21 05:45:03 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Change field structure in log_table table
$change_field[TABLE_LOG_TABLE] = array (	array (	"field_name" => "log_admin_id",
								 					"field_attr" => " varchar(255) NOT NULL default '' "
								 				  ),
											array (	"field_name" => "log_products_id",
								 					"field_attr" => " varchar(255) NOT NULL default '' "
								 				  )
										  );

change_field_structure ($change_field);
// End of change field structure in log_table table

// Insert new records into admin table (for sales report)
$admin_files_insert_sql = array();
$admin_files_insert_sql["sales_report.php"] = array	(	"insert" => " ('sales_report.php', 0, 8, '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes=8, admin_groups_id='1' "
													);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='sales_report.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin table (for sales report)

?>