<?
/*
  	$Id: version_1_5_2.php,v 1.4 2006/03/21 05:45:12 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into products table (for pre-order feature by storing 'Estimated Time of Arrival' and price tag) and latest_news table (display news in subcategories)
$add_new_field = array();
$add_new_field[TABLE_PRODUCTS] = array (array (	"field_name" => "products_pre_order_level",
								 				"field_attr" => " int(4) DEFAULT NULL ",
								 				"add_after" => "products_quantity_order"
												),
										array (	"field_name" => "products_eta",
							 					"field_attr" => " DOUBLE(6,2) DEFAULT NULL ",
							 					"add_after" => "products_pre_order_level"
												),
										array (	"field_name" => "products_price_tags_id",
								 				"field_attr" => " int(11) DEFAULT NULL ",
								 				"add_after" => "products_cat_path"
												)
										);

$add_new_field[TABLE_LATEST_NEWS] = array (	array (	"field_name" => "latest_news_include_subcat",
								 					"field_attr" => " tinyint(1) NOT NULL default '0' ",
								 					"add_after" => "news_groups_cat_id"
								 			   		)  
											);

add_field ($add_new_field);
// End of insert new field into products table (for pre-order feature by storing 'Estimated Time of Arrival' and price tag) and latest_news table (display news in subcategories)

// Insert new records into configuration table (for pre-order feature by storing 'Estimated Time of Arrival')
$conf_insert_sql = array();
$conf_insert_sql["SYSTEM_PRE_ORDER_LEVEL"] = array("insert" => " ('Default Pre-Order Level', 'SYSTEM_PRE_ORDER_LEVEL', '0', 'Displaying Pre-Order button when product stock quantity less than or equal this value', 9, 6, NULL, NOW(), NULL, NULL)" );
$conf_insert_sql["SYSTEM_PRODUCT_ETA"] = array("insert" => " ('ETA (hours)', 'SYSTEM_PRODUCT_ETA', '', 'Default product\'s estimated time of arrival (hours)', 9, 7, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for pre-order feature by storing 'Estimated Time of Arrival')

// Insert new records into orders_status table (for Refund status)
$order_status_insert_sql[6] = array("insert" => " (6, 1, 'Refunded') ",
									"update" => " language_id=1, orders_status_name='Refunded' "
				   					);

insert_new_records(TABLE_ORDERS_STATUS, "orders_status_id", $order_status_insert_sql, $DBTables, "(orders_status_id, language_id, orders_status_name)");
// End of insert new records into orders_status table (for Refund status)

// Insert new records into admin_files table (for price tag)
$admin_files_insert_sql = array();
$admin_files_insert_sql["price_tags.php"] = array	(	"insert" => " ('price_tags.php', 0, 3, '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes=3, admin_groups_id='1' "
													);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='price_tags.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin_files table (for price tag)

// Create price_groups and price_tags tables
$add_new_tables = array();
$add_new_tables["price_groups"] = array("structure" => " 	CREATE TABLE `price_groups` (
																`price_groups_id` int(11) NOT NULL auto_increment,
																`price_groups_name` varchar(32) NOT NULL default '',
																`price_groups_description` text,
																PRIMARY KEY  (`price_groups_id`) 
															) TYPE=MyISAM;" ,
										"data" => ""
										);

$add_new_tables["price_tags"] = array("structure" => " 	CREATE TABLE `price_tags` (
															`price_tags_id` int(11) NOT NULL auto_increment,
															`price_groups_id` int(11) NOT NULL default '0',
															`price_tags_name` varchar(32) NOT NULL default '',
															`price_tags_description` text,
															`tags_price` decimal(15,4) NOT NULL default '0.0000',
															PRIMARY KEY  (`price_tags_id`) 
														) TYPE=MyISAM;" ,
										"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create price_groups and price_tags tables

// Insert new records into configuration table (for show/hide also purchase section in product info page)
$conf_insert_sql = array();
$conf_insert_sql["SHOW_ALSO_PURCHASED_PRODUCTS"] = array("insert" => " ('Also Purchased Products', 'SHOW_ALSO_PURCHASED_PRODUCTS', 'false', 'Show also purchased products in product info page?', 1, 23, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for show/hide also purchase section in product info page)

// Change field structure in categories_description table
$change_field_struc = array();
$change_field_struc[TABLE_CATEGORIES_DESCRIPTION] = array (	array (	"field_name" => "categories_name",
								 									"field_attr" => " varchar(64) NOT NULL DEFAULT '' "
								 			  						)
										  				);

change_field_structure ($change_field_struc);
// End of change field structure in categories_description table

// Update records in latest_news_groups table (renaming announcements to news and notice to announcement)
$latest_news_update_sql = array();
$latest_news_update_sql["3"] = array("update" => " news_groups_name='News' " );
$latest_news_update_sql["5"] = array("update" => " news_groups_name='Announcements' " );

update_records(TABLE_LATEST_NEWS_GROUPS, "news_groups_id", $latest_news_update_sql, $DBTables);
// End of update records in latest_news_groups table (renaming announcements to news and notice to announcement)
?>