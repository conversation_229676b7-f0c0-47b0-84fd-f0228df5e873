<?
/*
	$Id: version_1_5_3.php,v 1.2 2005/03/24 09:27:35 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Delete products_price_tags_id field from products table
$delete_field = array();
$delete_field[TABLE_PRODUCTS] = array  (	array( "field_name" => "products_price_tags_id")
						 				);

delete_field ($delete_field);
// End of delete products_price_tags_id field from products table

// Create price_groups_to_categories table
$add_new_tables = array();
$add_new_tables["price_groups_to_categories"] = array(	"structure" => " 	CREATE TABLE `price_groups_to_categories` (
																				`price_groups_id` int(11) NOT NULL DEFAULT '0',
																				`categories_id` int(11) NOT NULL DEFAULT '0',
																				PRIMARY KEY  (`price_groups_id`, `categories_id`) 
																			) TYPE=MyISAM;" ,
														"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create price_groups_to_categories table

// Insert new field into price_tags table (for storing matching field name)
$add_new_field = array();
$add_new_field[TABLE_PRICE_TAGS] = array (	array (	"field_name" => "price_tags_field",
								 					"field_attr" => " varchar(255) NOT NULL DEFAULT '' "
								 			   		),
								 			array (	"field_name" => "price_tags_order",
								 					"field_attr" => " int(5) DEFAULT NULL "
								 			   		)
											);

add_field ($add_new_field);
// End of insert new field into price_tags table (for storing matching field name)

?>