<?
/*
  	$Id: version_1_5_4.php,v 1.2 2005/03/24 09:27:35 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Update records in configuration table (for sending mail options)
$conf_update_sql = array();
$conf_update_sql["SEND_EXTRA_ORDER_EMAILS_TO"] = array("update" => " sort_order=8 " );
$conf_update_sql["STORE_NAME_ADDRESS"] = array("update" => " sort_order=10 " );
$conf_update_sql["STORE_ZONE"] = array("update" => " sort_order=12 " );
$conf_update_sql["COPYRIGHT_INFORMATION"] = array("update" => " sort_order=14 " );
$conf_update_sql["SHOW_COUNTS"] = array("update" => " sort_order=16 " );
$conf_update_sql["ALLOW_CATEGORY_DESCRIPTIONS"] = array("update" => " sort_order=18 " );
$conf_update_sql["DISPLAY_DHTML_MENU"] = array("update" => " sort_order=20 " );
$conf_update_sql["PRODUCTS_FLAG"] = array("update" => " sort_order=22 " );
$conf_update_sql["PRODUCT_REVIEW"] = array("update" => " sort_order=24 " );
$conf_update_sql["SHOW_ALSO_PURCHASED_PRODUCTS"] = array("update" => " sort_order=26 " );
$conf_update_sql["DISPLAY_CART"] = array("update" => " sort_order=28 " );
$conf_update_sql["DISPLAY_PRICE_WITH_TAX"] = array("update" => " sort_order=30 " );
$conf_update_sql["STORE_PROMOTION"] = array("update" => " sort_order=32 " );
$conf_update_sql["LIVE_HELP_IMAGE_IMPLEMENTATION"] = array("update" => " sort_order=34 " );
$conf_update_sql["SEARCH_ENGINE_FRIENDLY_URLS"] = array("update" => " sort_order=36 " );
$conf_update_sql["ALLOW_GUEST_TO_TELL_A_FRIEND"] = array("update" => " sort_order=38 " );
$conf_update_sql["TAX_DECIMAL_PLACES"] = array("update" => " sort_order=40 " );
$conf_update_sql["NEW_SIGNUP_GIFT_VOUCHER_AMOUNT"] = array("update" => " sort_order=42 " );
$conf_update_sql["NEW_SIGNUP_DISCOUNT_COUPON"] = array("update" => " sort_order=44 " );
$conf_update_sql["EXPECTED_PRODUCTS_SORT"] = array("update" => " sort_order=46 " );
$conf_update_sql["EXPECTED_PRODUCTS_FIELD"] = array("update" => " sort_order=48 " );
$conf_update_sql["ADVANCED_SEARCH_DEFAULT_OPERATOR"] = array("update" => " sort_order=50 " );
$conf_update_sql["USE_DEFAULT_LANGUAGE_CURRENCY"] = array("update" => " sort_order=52 " );
$conf_update_sql["BOTTOM_LOGO_BAR"] = array("update" => " sort_order=60 " );

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (for sending mail options)

// Insert new records into configuration table (for pre-order discount)
$conf_insert_sql = array();
$conf_insert_sql["PRE_ORDER_DISCOUNT"] = array("insert" => " ('Pre-Order Discount (%)', 'PRE_ORDER_DISCOUNT', '0.00', 'Set the discount for pre-order items?<br>Please prefix your value with (+/- ) and without percentage', 9, 10, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for pre-order discount)

// Insert new field into orders_products table
$add_new_field = array();
$add_new_field[TABLE_ORDERS_PRODUCTS] = array (	array (	"field_name" => "products_pre_order",
								 						"field_attr" => " tinyint(1) NOT NULL DEFAULT '0' ",
								 						"add_after" => "products_bundle_id"
								 			   			)
											  );

add_field ($add_new_field);
// End of insert new field into orders_products table

// Change field structure in orders_products table
$change_new_field = array();
$change_new_field[TABLE_ORDERS_PRODUCTS] = array (	array (	"field_name" => "products_bundle_id",
										 					"field_attr" => " int(11) NOT NULL DEFAULT '0' "
										 				  )
												  );

change_field_structure ($change_new_field);
// End of change field structure in orders_products table

// Insert new records into configuration table (for browser title, meta description and meta keyword)
$conf_insert_sql = array();
$conf_insert_sql["BROWSER_TITLE"] = array("insert" => " ('Browser Title Tag', 'BROWSER_TITLE', '', 'Define the title which will be display ahead of any other custom titles', 1, 54, NULL, NOW(), NULL, NULL)" );
$conf_insert_sql["META_DESCRIPTION"] = array("insert" => " ('Meta Description', 'META_DESCRIPTION', '', 'Give a short and concise summary of your web page content', 1, 56, NULL, NOW(), NULL, 'tep_cfg_textarea(')" );
$conf_insert_sql["META_KEYWORDS"] = array("insert" => " ('Meta Keywords', 'META_KEYWORDS', '', 'Keyword phrases that describe your web page', 1, 58, NULL, NOW(), NULL, 'tep_cfg_textarea(')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for show/hide also purchase section in product info page)
?>