<?
/*
  	$Id: version_1_6.php,v 1.5 2006/03/21 05:45:34 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Update records in admin_files table (assign each files to correct box)
$admin_file_update_sql = array();
$admin_file_update_sql[TABLE_ADMIN_FILES] = array(	array(	"field_name" => "admin_files_to_boxes",
															"update" => " admin_files_to_boxes='137' ",
															"where_str" => " TRIM(LCASE(admin_files_name)) IN ('backup.php', 'cache.php', 'server_info.php') AND admin_files_is_boxes=0 ")
												 );

advance_update_records($admin_file_update_sql, $DBTables);
// End of update records in admin_files table (assign each files to correct box)

// Insert new records into configuration table (for worldpay show/hide credit card name and address, payment message, and payment legend colour)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_CARD_OWNER"] = array(	"insert" => " ('Credit card owner', 'MODULE_PAYMENT_WORLDPAY_CARD_OWNER', 'True', 'Should the credit card owner name to be changed at WorldPay payment?', 6, 12, NULL, now(), NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),') ",
																"update" => " configuration_value='True' "
				   									);
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_BILLING_ADDRESS"] = array("insert" => " ('Credit card billing address', 'MODULE_PAYMENT_WORLDPAY_BILLING_ADDRESS', 'True', 'Should the credit card billing address to be changed at WorldPay payment?', 6, 14, NULL, now(), NULL, 'tep_cfg_select_option(array(\'True\', \'False\'),') ",
																	"update" => " configuration_value='True' "
				   													);
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_LEGEND_COLOUR"] = array("insert" => " ('Legend Display Colour', 'MODULE_PAYMENT_WORLDPAY_LEGEND_COLOUR', '#FFFF00', 'Colour to indicate WorldPay payment method (default is grey)', 6, 16, NULL, now(), NULL, 'tep_cfg_colour_palette(')" );
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_MESSAGE"] = array("insert" => " ('Payment Message', 'MODULE_PAYMENT_WORLDPAY_MESSAGE', 'Thank you for shopping at SKC Store.', 'Payment message will show up during checkout process (WorldPay)', 6, 18, NULL, now(), NULL, 'tep_cfg_textarea(')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for worldpay show/hide credit card name and address, payment message, and payment legend colour)

// Create payment_extra_info table
$add_new_tables = array();

$add_new_tables["payment_extra_info"] = array(	"structure" => " 	CREATE TABLE `payment_extra_info` (
																	`orders_id` int(11) NOT NULL auto_increment,
																	`transaction_id` int(11) NOT NULL DEFAULT 0,
																	`transaction_status` tinyint(1) NOT NULL DEFAULT '0',
																	`transaction_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
																	`transaction_currency` varchar(10) DEFAULT '',
																	`transaction_text_amount` varchar(32) DEFAULT '',
																	`credit_card_type` varchar(64) DEFAULT '',
																	`credit_card_owner` varchar(255) DEFAULT '',
																	`email_address` varchar(255) DEFAULT '',
																	`billing_address` varchar(255) DEFAULT '',
																	`country` varchar(255) DEFAULT '',
																	`country_code` varchar(15) DEFAULT '',
																	`ip_address` varchar(64) DEFAULT '',
																	`tel` varchar(32) DEFAULT '',
																	`fax` varchar(32) DEFAULT '',
																	PRIMARY KEY  (`orders_id`) 
																) TYPE=MyISAM;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create payment_extra_info table
?>