<?
/*
  	$Id: version_1_6_1.php,v 1.3 2006/03/21 05:45:53 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into payment_extra_info table
$add_new_field = array();
$add_new_field[TABLE_PAYMENT_EXTRA_INFO] = array (	array (	"field_name" => "check_result",
								 							"field_attr" => " varchar(255) NOT NULL DEFAULT '' "
								 			   				)
											  	);

add_field ($add_new_field);
// End of insert new field into payment_extra_info table

// Change field structure in latest_news table
$change_new_field = array();
$change_new_field[TABLE_LATEST_NEWS] = array (	array (	"field_name" => "news_groups_cat_id",
										 				"field_attr" => " text NOT NULL "
										 			  )
											  );

change_field_structure ($change_new_field);
// End of change field structure in latest_news table

// Update records in latest_news table (appending "," to both end of the category id)
$latest_news_update_sql = array();
$latest_news_update_sql[TABLE_LATEST_NEWS] = array(	array(	"field_name" => "news_groups_cat_id",
															"update" => " news_groups_cat_id=CONCAT( ',', `news_groups_cat_id` , ',' ) ",
															"where_str" => " IF (LOCATE(',', news_groups_cat_id), 0, 1) " )
												 );

advance_update_records($latest_news_update_sql, $DBTables);
// End of update records latest_news table (appending "," to both end of the category id)

// Insert new records into admin_files table (for order comments)
$admin_files_insert_sql = array();
$admin_files_insert_sql["order_comment.php"] = array	(	"insert" => " ('order_comment.php', 0, 152, '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes=152, admin_groups_id='1' "
														);
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='order_comment.php' AND admin_files_is_boxes=0 ");

$admin_files_insert_sql = array();
$admin_files_insert_sql["order_add_comment.php"] = array	(	"insert" => " ('order_add_comment.php', 0, 152, '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes=152, admin_groups_id='1' "
															);
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='order_add_comment.php' AND admin_files_is_boxes=0 ");

$admin_files_insert_sql = array();
$admin_files_insert_sql["order_view_comment.php"] = array	(	"insert" => " ('order_view_comment.php', 0, 152, '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes=152, admin_groups_id='1' "
															);
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='order_view_comment.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin_files table (for order comments)

// Create orders_comments table
$add_new_tables = array();

$add_new_tables["orders_comments"] = array(	"structure" => "CREATE TABLE `orders_comments` (
																`orders_comments_id` int(11) NOT NULL auto_increment,
																`orders_comments_title` varchar(255) NOT NULL DEFAULT '',
																`orders_comments_text` text NOT NULL,
																`orders_comments_sort_order` int(5) NOT NULL DEFAULT '50000',
																`orders_comments_status` tinyint(1) NOT NULL DEFAULT '1',
																PRIMARY KEY  (`orders_comments_id`)
															) TYPE=MyISAM;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create orders_comments table
?>