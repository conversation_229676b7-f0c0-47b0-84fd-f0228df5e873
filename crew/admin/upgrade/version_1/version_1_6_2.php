<?
/*
  	$Id: version_1_6_2.php,v 1.2 2006/03/21 05:46:07 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin table (for shopping cart customer comments)
$admin_files_insert_sql = array();
$admin_files_insert_sql["cart_comments.php"] = array	(	"insert" => " ('cart_comments.php', 0, 4, '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes=4, admin_groups_id='1' "
														);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='cart_comments.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin table (for shopping cart customer comments)

// Create cart comments table
$add_new_tables = array();

$add_new_tables["cart_comments"] = array("structure" => " 	CREATE TABLE `cart_comments` (
															`cart_comments_id` int(11) NOT NULL auto_increment,
															`cart_comments_title` varchar(255) NOT NULL DEFAULT '',
															`cart_comments_input_type` varchar(32) NOT NULL DEFAULT 'text',
															`cart_comments_input_size` varchar(32) DEFAULT '',
															`cart_comments_options` text,
															`cart_comments_option_title` tinyint(1) NOT NULL DEFAULT '0',
															`cart_comments_required` tinyint(1) NOT NULL DEFAULT '0',
															`cart_comments_status` tinyint(1) NOT NULL DEFAULT '0',
															`cart_comments_sort_order` int(5) NOT NULL DEFAULT '50000',
															PRIMARY KEY  (`cart_comments_id`) 
														) TYPE=MyISAM;" ,
										"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create cart comments table

// Insert new field into orders table
$add_new_field = array();
$add_new_field[TABLE_ORDERS] = array (	array (	"field_name" => "orders_remarks",
												"field_attr" => " text NOT NULL "
								   				)
								  	);

add_field ($add_new_field);
// End of insert new field into orders table

?>