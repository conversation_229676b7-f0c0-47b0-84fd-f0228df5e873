<?
/*
  	$Id: version_1_6_3.php,v 1.3 2006/03/21 05:46:33 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin table (for printing the product list)
$admin_files_insert_sql = array();
$admin_files_insert_sql["printable_list.php"] = array	(	"insert" => " ('printable_list.php', 0, 3, '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes=3, admin_groups_id='1' "
														);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='printable_list.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin table (for printing the product list)

// Insert new records into configuration table (for show/hide affiliate program)
$conf_insert_sql = array();
$conf_insert_sql["AFFILATE_ENABLED"] = array("insert" => " ('Enable Affiliate Program', 'AFFILATE_ENABLED', 'false', 'Showing the affiliate program box in customer page?', 900, 1, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for show/hide affiliate program)

// Update records in configuration table (for affiliate program)
$conf_update_sql = array();
$conf_update_sql["AFFILIATE_EMAIL_ADDRESS"] = array("update" => " sort_order=5 " );
$conf_update_sql["AFFILIATE_COOKIE_LIFETIME"] = array("update" => " sort_order=10 " );
$conf_update_sql["AFFILIATE_BILLING_TIME"] = array("update" => " sort_order=15 " );
$conf_update_sql["AFFILIATE_COMMISSSION_ORDER_STATUS"] = array("update" => " sort_order=20 " );
$conf_update_sql["AFFILIATE_THRESHOLD"] = array("update" => " sort_order=25 " );
$conf_update_sql["AFFILIATE_PERCENT"] = array("update" => " sort_order=30 " );
$conf_update_sql["AFFILATE_INDIVIDUAL_PERCENTAGE"] = array("update" => " sort_order=35 " );
$conf_update_sql["AFFILIATE_USE_CHECK"] = array("update" => " sort_order=40 " );
$conf_update_sql["AFFILIATE_USE_PAYPAL"] = array("update" => " sort_order=45 " );
$conf_update_sql["AFFILIATE_USE_BANK"] = array("update" => " sort_order=50 " );
$conf_update_sql["AFFILATE_USE_TIER"] = array("update" => " sort_order=55 " );
$conf_update_sql["AFFILIATE_TIER_LEVELS"] = array("update" => " sort_order=60 " );
$conf_update_sql["AFFILIATE_TIER_PERCENTAGE"] = array("update" => " sort_order=65 " );

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (for sending mail options)

// Insert new field into orders table
$add_new_field = array();
$add_new_field[TABLE_ORDERS] = array (	array (	"field_name" => "orders_remarks_last_modified_by",
												"field_attr" => " varchar(255) NOT NULL DEFAULT '' "
								   				)
								  	);

add_field ($add_new_field);
// End of insert new field into orders table

?>