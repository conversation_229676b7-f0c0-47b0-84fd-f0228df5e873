<?
/*
  	$Id: version_1_7.php,v 1.3 2005/04/21 04:15:41 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create affiliate_account table
$add_new_tables["affiliate_account"] = array ("structure" => " CREATE TABLE `affiliate_account` (
																	`affiliate_account_id` int(11) NOT NULL auto_increment,
																	`affiliate_id` int(11) NOT NULL default '0',
	  																`affiliate_payment_id` int(11) NOT NULL default '0',
																	`affiliate_total_amount` decimal(15,2) NOT NULL default '0.00',
																	`affiliate_reclaim_amount` decimal(15,2) NOT NULL default '0.00',
																	`affiliate_paid_amount` decimal(15,2) NOT NULL default '0.00',
																	`affiliate_transaction_date` datetime NOT NULL default '0000-00-00 00:00:00',
	  																PRIMARY KEY  (`affiliate_account_id`)
																) TYPE=MyISAM;" ,
												"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create affiliate_account table

// Delete affiliate_last_modified field from affiliate_payment table
$delete_field = array();
$delete_field[TABLE_AFFILIATE_PAYMENT] = array  (	array( "field_name" => "affiliate_last_modified") );

delete_field ($delete_field);
// End of delete products_price_tags_id field from products table

// Insert new field into categories and affiliate_payment tables
$add_new_field = array();
$add_new_field[TABLE_CATEGORIES] = array (	array (	"field_name" => "categories_pinned",
								 					"field_attr" => " tinyint(1) unsigned NOT NULL default '0' ",
								 					"add_after" => "last_modified"
								 				    )
								 		);
$add_new_field[TABLE_AFFILIATE_PAYMENT] = array (	array (	"field_name" => "affiliate_payment_reclaimed_total",
								 							"field_attr" => " decimal(15,2) NOT NULL default '0.00' ",
								 							"add_after" => "affiliate_payment_date"
								 				    		),
													array (	"field_name" => "affiliate_payment_reclaimed_sales",
								 							"field_attr" => " text ",
								 							"add_after" => "affiliate_payment_reclaimed_total"
								 				    		)
								 				);
add_field ($add_new_field);
// End of insert new field into categories and affiliate_payment tables

// Insert new records into configuration table (for affiliate reclaim status)
$conf_insert_sql = array();

$conf_insert_sql["AFFILIATE_RECLAIM_ORDER_STATUS"] = array("insert" => " ('Reclaim Order Status', 'AFFILIATE_RECLAIM_ORDER_STATUS', '4', 'The status an order must have at least, to be reclaimed. Seperated by \',\' for multiple values.', 900, 21, NULL, now(), NULL, NULL)" );
$conf_insert_sql["SHOW_BESTSELLERS"] = array("insert" => " ('Allow Bestsellers', 'SHOW_BESTSELLERS', 'true', 'Show bestsellers panel in customer page?', 1, 23, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for affiliate reclaim status)

// Update records in affiliate_payment_status table (renaming Paid to Process)
$affiliate_payment_status_update_sql = array();
$affiliate_payment_status_update_sql[TABLE_AFFILIATE_PAYMENT_STATUS] = array(	array(	"field_name" => "affiliate_payment_status_name",
																						"update" => " affiliate_payment_status_name='Process' ",
																						"where_str" => " affiliate_payment_status_id=1 AND affiliate_language_id=1 ")
																			 );

advance_update_records($affiliate_payment_status_update_sql, $DBTables);
// End of update records in affiliate_payment_status table (renaming Paid to Process)

// Delete affiliate_payment_date field from affiliate_sales table
$delete_field = array();
$delete_field[TABLE_AFFILIATE_SALES] = array  (	array( "field_name" => "affiliate_payment_date") );

delete_field ($delete_field);
// End of delete affiliate_payment_date field from affiliate_sales table

// Update records in configuration table (for affiliate commission order status)
$conf_update_sql = array();
$conf_update_sql["AFFILIATE_COMMISSSION_ORDER_STATUS"] = array("update" => " configuration_value='3' ");

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (for affiliate commission order status)

// Insert new field into products table
$add_new_field = array();
// default to 1? then batch update all to 0 for static and dynamic.
$add_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_purchase_mode",
								 					"field_attr" => " tinyint(1) unsigned NOT NULL DEFAULT '1' ",
								 					"add_after" => "products_negative_qty"
								 					)
									  );

add_field ($add_new_field);
// End of insert new field into products table

// Change field structure in products table
$change_new_field = array();
$change_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_purchase_mode",
										 			"field_attr" => " tinyint(1) unsigned NOT NULL DEFAULT '0' "
										 		  )
										  );

change_field_structure ($change_new_field);
// End of change field structure in products table

// Update records in products table (set products_purchase_mode=0 for all static and dynamic products)
$products_purchase_mode_update_sql = array();
$products_purchase_mode_update_sql[TABLE_PRODUCTS] = array(	array(	"field_name" => "products_purchase_mode",
																					"update" => " products_purchase_mode=0 ",
																					"where_str" => " products_bundle='yes' OR products_bundle_dynamic='yes' ")
																	 );

advance_update_records($products_purchase_mode_update_sql, $DBTables);
// End of update records in products table (set products_purchase_mode=0 for all static and dynamic products)
?>