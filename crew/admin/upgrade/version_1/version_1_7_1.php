<?
/*
  	$Id: version_1_7_1.php,v 1.7 2006/03/21 05:46:52 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration_group table (for buy back module)
$configuration_group_insert_sql = array();

$configuration_group_insert_sql["Buyback"] = array	(	"insert" => " ('Buyback', 'Buy Back default values', 100, 1) ",
														"update" => " sort_order=100, visible='1' "
				   									);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");
// End of insert new records into configuration_group table (for buy back module)

// Insert new records into configuration table (for buy back module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_GLOBAL_QUANTITY"] = array("insert" => " ('Byback Global Quantity', 'BUYBACK_GLOBAL_QUANTITY', '0', 'Default value for Buy Back quantity', ".$row_sql["configuration_group_id"].", 30, NULL, 'now()', NULL, NULL)" );
	$conf_insert_sql["BUYBACK_GLOBAL_PRICE"] = array("insert" => " ('Byback Global Price', 'BUYBACK_GLOBAL_PRICE', '0.01', 'Default value for Buy Back price', ".$row_sql["configuration_group_id"].", 40, NULL, 'now()', NULL, NULL)" );
	$conf_insert_sql["BUYBACK_MINIMUM_QUANTITY"] = array("insert" => " ('Minimum Buyback Quantity', 'BUYBACK_MINIMUM_QUANTITY', '400', 'Default minimum buyback quantity', ".$row_sql["configuration_group_id"].", 50, NULL, 'now()', NULL, NULL)" );
	$conf_insert_sql["BUYBACK_ENABLED"] = array("insert" => " ('Allow Buyback', 'BUYBACK_ENABLED', 'false', 'Enable buyback?', ".$row_sql["configuration_group_id"].", 10, NULL, 'now()', NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	$conf_insert_sql["BUYBACK_DISCLAIMER"] = array("insert" => " ('Buyback Disclaimer', 'BUYBACK_DISCLAIMER', 'Buyback disclaimer goes here', 'Buyback Disclaimer', ".$row_sql["configuration_group_id"].", 20, NULL, 'now()', NULL, NULL)" );
	$conf_insert_sql["BUYBACK_MAXIMUM_QUANTITY"] = array("insert" => " ('Maximum Buyback Quantity', 'BUYBACK_MAXIMUM_QUANTITY', '1000', 'Default maximum buyback quantity', ".$row_sql["configuration_group_id"].", 60, NULL, 'now()', NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for buy back module)

// Create buyback tables
$add_new_tables = array();

$add_new_tables["buyback_status"] = array (	"structure" => " CREATE TABLE `buyback_status` (
															  	`buyback_status_id` int(11) NOT NULL auto_increment,
															  	`language_id` int(11) NOT NULL default '1',
															  	`buyback_status_name` varchar(255) NOT NULL default '',
															  	PRIMARY KEY  (`buyback_status_id`)
															) TYPE=MyISAM;" ,
											"data" => "	INSERT INTO `buyback_status` (`language_id`, `buyback_status_name`) 
															VALUES (1, 'Pending'), (1, 'Processing'), (1, 'Complete');
														"
										);

$add_new_tables["buyback"] = array (	"structure" => " CREATE TABLE `buyback` (
														  	`buyback_id` int(11) NOT NULL auto_increment,
														  	`products_id` int(11) NOT NULL default '0',
														  	`buyback_bracket_id` int(11) NOT NULL default '0',
														  	PRIMARY KEY  (`buyback_id`)
														 ) TYPE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["buyback_basket"] = array (	"structure" => " CREATE TABLE `buyback_basket` (
															  	`buyback_basket_id` int(11) NOT NULL auto_increment,
															  	`customers_id` int(11) NOT NULL default '0',
															  	`products_id` int(11) NOT NULL default '0',
															  	`buyback_basket_quantity` int(11) NOT NULL default '0',
															  	`buyback_basket_amount` double NOT NULL default '0',
															  	PRIMARY KEY  (`buyback_basket_id`)
															 ) TYPE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["buyback_bracket"] = array (	"structure" => " CREATE TABLE `buyback_bracket` (
																  	`buyback_bracket_id` int(11) NOT NULL auto_increment,
																  	`buyback_bracket_quantity` int(11) NOT NULL default '0',
																  	`buyback_bracket_value` double NOT NULL default '0',
																  	`buyback_bracket_mode` smallint(1) NOT NULL default '0',
																  	PRIMARY KEY  (`buyback_bracket_id`)
																 ) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["buyback_bracket_tags"] = array (	"structure" => " CREATE TABLE `buyback_bracket_tags` (
																	  	`buyback_bracket_tags_id` int(11) NOT NULL auto_increment,
																	  	`buyback_bracket_tags_quantity` int(11) NOT NULL default '0',
																	  	`buyback_bracket_tags_value` double NOT NULL default '0',
																	  	`buyback_bracket_tags_mode` varchar(255) NOT NULL default '',
																	  	PRIMARY KEY  (`buyback_bracket_tags_id`)
																	 ) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["buyback_groups"] = array (	"structure" => " CREATE TABLE `buyback_groups` (
															  	`buyback_groups_id` int(11) NOT NULL auto_increment,
															  	`buyback_groups_name` varchar(255) NOT NULL default '',
															  	`buyback_groups_description` text NOT NULL,
															  	PRIMARY KEY  (`buyback_groups_id`)
															 ) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["buyback_groups_tags"] = array (	"structure" => " CREATE TABLE `buyback_groups_tags` (
																	  	`buyback_groups_tags_id` int(11) NOT NULL auto_increment,
																	  	`buyback_groups_id` int(11) NOT NULL default '0',
																	  	`buyback_bracket_tags_id` int(11) NOT NULL default '0',
																	  	`buyback_groups_tags_info_id` int(11) NOT NULL default '0',
																	  	PRIMARY KEY  (`buyback_groups_tags_id`)
																	 ) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["buyback_groups_tags_info"] = array (	"structure" => " CREATE TABLE `buyback_groups_tags_info` (
																		  	`buyback_groups_tags_info_id` int(11) NOT NULL auto_increment,
																		  	`buyback_groups_tags_value` varchar(255) NOT NULL default '',
																		  	`buyback_groups_tags_field` varchar(255) NOT NULL default '',
																		  	`buyback_groups_tags_order` int(11) NOT NULL default '0',
																		  	`buyback_groups_id` int(11) NOT NULL default '0',
																		  	`buyback_groups_tags_minimum_buyback` int(11) NOT NULL default '400',
																		  	`buyback_groups_tags_maximum_buyback` int(11) NOT NULL default '1000',
																		  	PRIMARY KEY  (`buyback_groups_tags_info_id`)
																		 ) TYPE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables["buyback_groups_to_categories"] = array (	"structure" => " CREATE TABLE `buyback_groups_to_categories` (
																			  	`buyback_groups_id` int(11) NOT NULL default '0',
																			  	`categories_id` int(11) NOT NULL default '0',
																			  	PRIMARY KEY  (`buyback_groups_id`,`categories_id`)
																			 ) TYPE=MyISAM;" ,
															"data" => ""
														);

$add_new_tables["buyback_products"] = array (	"structure" => " CREATE TABLE `buyback_products` (
																  	`buyback_products_id` int(11) NOT NULL auto_increment,
																  	`products_id` int(11) NOT NULL default '0',
																  	`buyback_products_status` smallint(1) NOT NULL default '0',
																  	`buyback_products_minimum_value` int(11) NOT NULL default '400',
																  	`buyback_products_maximum_value` int(11) NOT NULL default '1000',
																  	PRIMARY KEY  (`buyback_products_id`)
																 ) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["buyback_request"] = array (	"structure" => " CREATE TABLE `buyback_request` (
																  	`buyback_request_id` int(11) NOT NULL auto_increment,
																  	`buyback_request_group_id` int(11) NOT NULL default '0',
																  	`products_id` int(11) NOT NULL default '0',
																  	`buyback_request_quantity` int(11) NOT NULL default '0',
																  	`buyback_amount` double NOT NULL default '0',
																  	PRIMARY KEY  (`buyback_request_id`)
																 ) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["buyback_request_group"] = array (	"structure" => " CREATE TABLE `buyback_request_group` (
																	  	`buyback_request_group_id` int(11) NOT NULL auto_increment,
																	  	`customers_id` int(11) NOT NULL default '0',
																	  	`buyback_request_group_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  	`remote_addr` varchar(255) NOT NULL default '',
																	  	`buyback_status_id` smallint(1) NOT NULL default '1',
																	  	PRIMARY KEY  (`buyback_request_group_id`)
																	 ) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create buyback tables

// Insert new records into admin_files table (for buyback module)
$admin_files_insert_sql = array();

$admin_files_insert_sql["buyback.php"] = array	(	"insert" => " ('buyback.php', 1, 0, '1') ",
													"update" => " admin_files_is_boxes=1, admin_files_to_boxes=0, admin_groups_id='1' "
				   								);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='buyback.php' AND admin_files_is_boxes=1 ");
// End of insert new records into admin_files table (for buyback module)

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for buyback module)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["buyback_requests.php"] = array	(	"insert" => " ('buyback_requests.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   										);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='buyback_requests.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["buyback_products_brackets.php"] = array	(	"insert" => " ('buyback_products_brackets.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																			"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   													);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='buyback_products_brackets.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["buyback_products.php"] = array	(	"insert" => " ('buyback_products.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   										);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='buyback_products.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["buyback_groups.php"] = array	(	"insert" => " ('buyback_groups.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   										);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='buyback_groups.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for buyback module)	
}

// Insert new field into products table
$add_new_field = array();
$add_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_actual_quantity",
								 					"field_attr" => " int(4) default '0' ",
								 					"add_after" => "products_quantity"
								 					)
									  );

$add_new_field[TABLE_ORDERS_PRODUCTS] = array (	array (	"field_name" => "products_delivered_quantity",
									 					"field_attr" => " int(2) NOT NULL default '0' ",
									 					"add_after" => "products_quantity"
									 					)
									  );

add_field ($add_new_field);
// End of insert new field into products table
?>