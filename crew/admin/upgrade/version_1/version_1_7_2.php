<?
/*
  	$Id: version_1_7_2.php,v 1.6 2006/03/21 05:47:17 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Update records in configuration table (for buyback typo correction)
$conf_update_sql = array();

$conf_update_sql["BUYBACK_GLOBAL_QUANTITY"] = array("update" => " configuration_title='System Buyback Global Quantity' ");
$conf_update_sql["BUYBACK_GLOBAL_PRICE"] = array("update" => " configuration_title='System Buyback Global Price' ");
$conf_update_sql["BUYBACK_DISCLAIMER"] = array("update" => " set_function='tep_cfg_textarea(' ");

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (for buyback typo correction)

// Create shipping_classes and shipping_classes_values table
$add_new_tables = array();
$add_new_tables["shipping_classes"] = array (	"structure" => " CREATE TABLE `shipping_classes` (
																	`shipping_classes_id` int(11) NOT NULL auto_increment,
																	`shipping_classes_name` varchar(255) NOT NULL DEFAULT '',
																	`shipping_module` varchar(255) NOT NULL DEFAULT '',
																	PRIMARY KEY  (`shipping_classes_id`)
																) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["shipping_classes_values"] = array ("structure" => " CREATE TABLE `shipping_classes_values` (
																		`shipping_classes_id` int(11) NOT NULL DEFAULT 0,
																		`shipping_classes_key` varchar(255) NOT NULL DEFAULT '',
																		`shipping_classes_values` varchar(255) NOT NULL DEFAULT '',
																		PRIMARY KEY  (`shipping_classes_id`, `shipping_classes_key`)
																	) TYPE=MyISAM;" ,
													"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create shipping_classes and shipping_classes_values table

// Insert new records into admin table (for shipping module and zone configuration)
$admin_files_insert_sql = array();
$admin_files_insert_sql["modules_classes.php"] = array	(	"insert" => " ('modules_classes.php', 0, 4, '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes=4, admin_groups_id='1' "
														);
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='modules_classes.php' AND admin_files_is_boxes=0 ");

$admin_files_insert_sql = array();
$admin_files_insert_sql["geo_zones.php"] = array	(	"insert" => " ('geo_zones.php', 0, 7, '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes=7, admin_groups_id='1' "
													);
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='geo_zones.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin table (for shipping module and zone configuration)

// Insert new records into configuration table (maximum width and alignment of product description)
$conf_insert_sql = array();
$conf_insert_sql["PRODUCT_DESCRIPTION_WIDTH"] = array("insert" => " ('Product Description Width (px/%)', 'PRODUCT_DESCRIPTION_WIDTH', '170px', 'The maximum width of the product description cell on Product Info page. Default to 170px.', 200, 25, NULL, NOW(), NULL, NULL)" );
$conf_insert_sql["PRODUCT_DESCRIPTION_ALIGN"] = array("insert" => " ('Product Description Alignment', 'PRODUCT_DESCRIPTION_ALIGN', 'center', 'The alignment of the product description cell on Product Info page.', 200, 28, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'left\', \'center\', \'right\'),')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (maximum width and alignment of product description)

// Update records in configuration table (for Parameters Max Value)
$conf_update_sql = array();
$conf_update_sql["MAX_DISPLAY_LATEST_NEWS"] = array("update" => " sort_order=5 " );
$conf_update_sql["MAX_DISPLAY_LATEST_NEWS_PAGE"] = array("update" => " sort_order=10 " );
$conf_update_sql["MAX_DISPLAY_NEW_REVIEWS"] = array("update" => " sort_order=15 " );
$conf_update_sql["MAX_RANDOM_SELECT_REVIEWS"] = array("update" => " sort_order=20 " );
$conf_update_sql["MAX_DISPLAY_SEARCH_RESULTS"] = array("update" => " sort_order=25 " );
$conf_update_sql["MAX_DISPLAY_PAGE_LINKS"] = array("update" => " sort_order=30 " );
$conf_update_sql["MAX_DISPLAY_MANUFACTURERS_IN_A_LIST"] = array("update" => " sort_order=35 " );
$conf_update_sql["MAX_MANUFACTURERS_LIST"] = array("update" => " sort_order=40 " );
$conf_update_sql["MAX_DISPLAY_MANUFACTURER_NAME_LEN"] = array("update" => " sort_order=45 " );
$conf_update_sql["MAX_DISPLAY_PRODUCTS_NEW"] = array("update" => " sort_order=50 " );
$conf_update_sql["MAX_DISPLAY_NEW_PRODUCTS"] = array("update" => " sort_order=55 " );
$conf_update_sql["MAX_DISPLAY_SPECIAL_PRODUCTS"] = array("update" => " sort_order=60 " );
$conf_update_sql["MAX_DISPLAY_UPCOMING_PRODUCTS"] = array("update" => " sort_order=65 " );
$conf_update_sql["MAX_RANDOM_SELECT_NEW"] = array("update" => " sort_order=70 " );
$conf_update_sql["MAX_RANDOM_SELECT_SPECIALS"] = array("update" => " sort_order=75 " );
$conf_update_sql["MAX_DISPLAY_BESTSELLERS"] = array("update" => " sort_order=85 " );
$conf_update_sql["MAX_DISPLAY_ALSO_PURCHASED"] = array("update" => " sort_order=90 " );
$conf_update_sql["MAX_DISPLAY_CATEGORIES_PER_ROW"] = array("update" => " sort_order=95 " );
$conf_update_sql["MAX_ADDRESS_BOOK_ENTRIES"] = array("update" => " sort_order=100 " );
$conf_update_sql["MAX_DISPLAY_ORDER_HISTORY"] = array("update" => " sort_order=105 " );
$conf_update_sql["MAX_DISPLAY_PRODUCTS_IN_ORDER_HISTORY_BOX"] = array("update" => " sort_order=110 " );

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (for Parameters Max Value)

// Change field structure for customer's remark in customers table and zone id in zones_to_geo_zones table
$change_field_structure = array();
$change_field_structure[TABLE_CUSTOMERS] = array (array (	"field_name" => "customers_remark",
								 							"field_attr" => " text NOT NULL "
								 				  		)
										  		);
$change_field_structure[TABLE_ZONES_TO_GEO_ZONES] = array (array (	"field_name" => "zone_id",
								 									"field_attr" => " text "
								 				  				)
										  				);
change_field_structure ($change_field_structure);
// End of change field structure for customer's remark in customers table and zone id in zones_to_geo_zones table
?>