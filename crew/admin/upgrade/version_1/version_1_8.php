<?
/*
  	$Id: version_1_8.php,v 1.7 2005/06/16 11:31:10 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Delete records in admin_files table
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='information.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_file_delete_sql = array();
	$admin_file_delete_sql["information.php"] = array(	"unique" => "1", "extra_where" => " admin_files_is_boxes=1 ");
	
	delete_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_file_delete_sql, $DBTables);
}
// End of delete records in admin_files table

// Create customers_remarks_history, custom_products_type and orders_remarks_history tables
$add_new_tables = array();

$add_new_tables[TABLE_CUSTOMERS_REMARKS_HISTORY] = array(	"structure" => " 	CREATE TABLE `customers_remarks_history` (
																					`customers_remarks_history_id` int(11) NOT NULL auto_increment,
																					`customers_id` int(11) NOT NULL default '0',
																					`date_remarks_added` datetime default NULL,
																					`remarks` text,
																					`remarks_added_by` varchar(255) default NULL,
																					PRIMARY KEY  (`customers_remarks_history_id`) 
																				) TYPE=MyISAM;" ,
															"data" => ""
														);

$add_new_tables[TABLE_ORDERS_REMARKS_HISTORY] = array(	"structure" => "CREATE TABLE `orders_remarks_history` (
																			`orders_remarks_history_id` int(11) NOT NULL auto_increment,
																			`orders_id` int(11) NOT NULL default '0',
																			`date_orders_remarks_added` datetime default NULL,
																			`orders_remarks` text,
																			`orders_remarks_added_by` varchar(255) default NULL,
																			PRIMARY KEY  (`orders_remarks_history_id`) 
																		) TYPE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables[TABLE_CUSTOM_PRODUCTS_TYPE] = array(	"structure" => "CREATE TABLE `custom_products_type` (
																			`custom_products_type_id` int(11) NOT NULL auto_increment,
																			`custom_products_type_name` varchar(100) NOT NULL default '',
																			`data_pool_id` int(11) NOT NULL default '0',
																			PRIMARY KEY  (`custom_products_type_id`)
																		) TYPE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables[TABLE_SEARCH_CRITERIA] = array(	"structure" => "CREATE TABLE `search_criteria` (
																	`search_criteria_id` int(11) NOT NULL auto_increment,
																	`filename` varchar(255) NOT NULL default '',
																	`search_criteria_name` varchar(255) NOT NULL default '',
																	`search_criteria_string` text,
																	`date_search_criteria_added` datetime NOT NULL default '0000-00-00 00:00:00',
																	`date_search_criteria_last_modified` datetime default NULL,
																	`search_criteria_created_by` varchar(255) default NULL,
																	`last_modified_by` varchar(255) default NULL,
																	PRIMARY KEY  (`search_criteria_id`) 
																) TYPE=MyISAM;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create customers_remarks_history, custom_products_type and orders_remarks_history tables


// Copy contents of customers_remark in customers table to remarks in customers_remarks_history table
$copy_field_contents = array();

$copy_field_contents[] = array  (	"from_table" => TABLE_CUSTOMERS,
									"to_table" => TABLE_CUSTOMERS_REMARKS_HISTORY,
									"primary_key" => "customers_id",
									"foreign_key" => "customers_id",
									"from_field" => "customers_remark",
									"to_field" => "remarks",
									"extra_field" => "", 
									"skip_empty_record" => "1"
								);

$copy_field_contents[] = array  (	"from_table" => TABLE_ORDERS,
									"to_table" => TABLE_ORDERS_REMARKS_HISTORY,
									"primary_key" => "orders_id",
									"foreign_key" => "orders_id",
									"to_table_pk" => "orders_remarks_history_id",
									"from_field" => array("orders_remarks", "orders_remarks_last_modified_by"),
									"to_field" => array("orders_remarks", "orders_remarks_added_by"),
									"extra_field" => "", 
									"skip_empty_record" => "1"
								);

copy_field_content ($copy_field_contents);
// End of copy contents of customers_remark in customers table to remarks in customers_remarks_history table

// Need to update the orders_remarks_last_modified_by from email to admin id in orders_remarks_history table
$orders_table_fields = get_table_fields(TABLE_ORDERS);
if (in_array("orders_remarks", $orders_table_fields) && in_array("orders_remarks_last_modified_by", $orders_table_fields)) {
	$update_orders_remarks_history_sql = " 	UPDATE " . TABLE_ORDERS_REMARKS_HISTORY . " AS orh 
											INNER JOIN " . TABLE_ADMIN . " AS a 
												ON orh.orders_remarks_added_by=a.admin_email_address 
											SET orh.orders_remarks_added_by = a.admin_id";
	tep_db_query($update_orders_remarks_history_sql);
}
// End of need to update the orders_remarks_last_modified_by from email to admin id in orders_remarks_history table

// Delete customers_remark field from customers table and orders_remarks and orders_remarks_last_modified_by fields from orders table
$delete_field = array();
$delete_field[TABLE_CUSTOMERS] = array  (	array( "field_name" => "customers_remark")	);
$delete_field[TABLE_ORDERS] = array (	array( "field_name" => "orders_remarks"),
										array( "field_name" => "orders_remarks_last_modified_by")
									);

delete_field ($delete_field);
// End of delete customers_remark field from customers table and orders_remarks and orders_remarks_last_modified_by fields from orders table

// Update records in admin_files table (move popup product list file from 'Administrator' to 'Tools')
$admin_file_update_sql = array();
$admin_file_update_sql[TABLE_ADMIN_FILES] = array(	array(	"field_name" => "admin_files_to_boxes",
															"update" => " admin_files_to_boxes='137' ",
															"where_str" => " TRIM(LCASE(admin_files_name)) = 'popup_products_list.php' AND admin_files_is_boxes=0")
												 );

advance_update_records($admin_file_update_sql, $DBTables);
// End of update records in admin_files table (move popup product list file from 'Administrator' to 'Tools')

// Update records in configuration_group table (set Images configuration group visible)
$conf_group_update_sql = array();
$conf_group_update_sql["Images"] = array("update" => " visible=1 " );

update_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $conf_group_update_sql, $DBTables);
// End of update records in configuration_group table (set Images configuration group visible)

// Insert new records into configuration table (maximum width and height of product image)
$conf_insert_sql = array();
$conf_insert_sql["PRODUCT_IMAGE_WIDTH"] = array("insert" => " ('Product Image Width', 'PRODUCT_IMAGE_WIDTH', '130', 'The pixel width of product images', 4, 15, NULL, NOW(), NULL, NULL)" );
$conf_insert_sql["PRODUCT_IMAGE_HEIGHT"] = array("insert" => " ('Product Image Height', 'PRODUCT_IMAGE_HEIGHT', '248', 'The pixel height of product images', 4, 20, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (maximum width and height of product image)

// Update records in configuration table (for images)
$conf_update_sql = array();
$conf_update_sql["SMALL_IMAGE_WIDTH"] = array("update" => " sort_order=5 " );
$conf_update_sql["SMALL_IMAGE_HEIGHT"] = array("update" => " sort_order=10 " );
$conf_update_sql["PRODUCT_IMAGE_WIDTH"] = array("update" => " sort_order=15 " );
$conf_update_sql["PRODUCT_IMAGE_HEIGHT"] = array("update" => " sort_order=20 " );
$conf_update_sql["HEADING_IMAGE_WIDTH"] = array("update" => " sort_order=25 " );
$conf_update_sql["HEADING_IMAGE_HEIGHT"] = array("update" => " sort_order=30 " );
$conf_update_sql["SUBCATEGORY_IMAGE_WIDTH"] = array("update" => " sort_order=35 " );
$conf_update_sql["SUBCATEGORY_IMAGE_HEIGHT"] = array("update" => " sort_order=40 " );
$conf_update_sql["CONFIG_CALCULATE_IMAGE_SIZE"] = array("update" => " sort_order=45 " );
$conf_update_sql["IMAGE_REQUIRED"] = array("update" => " sort_order=50 " );

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (for images)

// Insert new fields into products table (for custom product type) and payment_extra_info table (storing alert message)
$add_new_field = array();
$add_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "custom_products_type_id",
													"field_attr" => " int(11) NOT NULL default '0' ",
													"add_after" => ""
									   				)
									  	);

$add_new_field[TABLE_PAYMENT_EXTRA_INFO] = array (	array (	"field_name" => "alert_message",
								 							"field_attr" => " varchar(255) NOT NULL DEFAULT '' "
								 			   				)
											  	);

$add_new_field[TABLE_BUYBACK_REQUEST_GROUP] = array (	array (	"field_name" => "buyback_request_group_comment",
									 							"field_attr" => " text NOT NULL "
									 			   				)
												  	);

$add_new_field[TABLE_BUYBACK_GROUPS] = array (	array (	"field_name" => "buyback_groups_status",
							 							"field_attr" => " tinyint(1) NOT NULL default '1' "
							 			   				)
										  	);

add_field($add_new_field);
// End of insert new fields into products table (for custom product type) and payment_extra_info table (storing alert message)

// Delete records in admin_files table
$admin_file_delete_sql = array();
$admin_file_delete_sql["order_track2.php"] = array(	"unique" => "1", "extra_where" => " admin_files_is_boxes=0 " ); // extra where concatenated with default where checking string using 'AND' operator

delete_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_file_delete_sql, $DBTables);
// End of delete records in admin_files table

// Insert new records into admin_files_actions table (for permission on reversing order status)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='order_track.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SAVE_ORDER_LISTS_CRITERIA"] = array("insert" => " ('SAVE_ORDER_LISTS_CRITERIA', 'Save criteria', ".$row_sql["admin_files_id"].", '1')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`)");
}
// End of insert new records into admin_files_actions table (for permission on reversing order status)

// Update records in admin_files_actions table (renaming permission to reverse order status)
$admin_file_actions_update_sql = array();
$admin_file_actions_update_sql["REVERSIBLE_ORDER_STATUS"] = array("update" => " admin_files_actions_name='Reverse order status' ");

update_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_file_actions_update_sql, $DBTables);

// End of update records in admin_files_actions table (renaming permission to reverse order status)


// Insert new records into configuration table (for buy back module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_ADMIN_EMAIL"] = array("insert" => " ('Buyback Admin Email', 'BUYBACK_ADMIN_EMAIL', '', 'Buyback notification email.', ".$row_sql["configuration_group_id"].", 12, NULL, 'now()', NULL, NULL)" );
	$conf_insert_sql["BUYBACK_ADMIN_NAME"] = array("insert" => " ('Buyback Admin Name', 'BUYBACK_ADMIN_NAME', '', 'Name of buyback admin, to whom the email will be addressed to.', ".$row_sql["configuration_group_id"].", 13, NULL, 'now()', NULL, NULL)" );
	$conf_insert_sql["BUYBACK_AGREEMENT"] = array("insert" => " ('Buyback Agreement', 'BUYBACK_AGREEMENT', '', 'Buyback Agreement Text', ".$row_sql["configuration_group_id"].", 70, NULL, 'now()', NULL, 'tep_cfg_textarea(')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for buy back module)
?>