<?
/*
  	$Id: version_1_8_1.php,v 1.2 2005/06/30 07:49:06 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Delete records in admin_files table

// Insert new records into admin_files_actions table (for permission on viewing location in stock report and unlocking other people order)
$admin_files_actions_insert_sql = array();

$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='stock_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql["VIEW_PRODUCT_LOCATION"] = array("insert" => " ('VIEW_PRODUCT_LOCATION', 'View product\'s location', ".$row_sql["admin_files_id"].", '1')" );
}

$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql["UNLOCK_OTHERS_ORDERS"] = array("insert" => " ('UNLOCK_OTHERS_ORDERS', 'Unlock any orders', ".$row_sql["admin_files_id"].", '1')" );
}

insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`)");
// End of insert new records into admin_files_actions table (for permission on viewing location in stock report and unlocking other people order)

// Insert new fields into orders table (for locking while editing processing order)
$add_new_field = array();
$add_new_field[TABLE_ORDERS] = array (	array (	"field_name" => "orders_locked_by",
												"field_attr" => " int(11) default NULL ",
												"add_after" => ""
								   				),
								   		array (	"field_name" => "orders_locked_from_ip",
												"field_attr" => " varchar(20) default NULL ",
												"add_after" => "orders_locked_by"
								   				),
								   		array (	"field_name" => "orders_locked_datetime",
												"field_attr" => " datetime default NULL ",
												"add_after" => "orders_locked_from_ip"
								   				)
								  	);

$add_new_field[TABLE_BUYBACK_GROUPS] = array (	array (	"field_name" => "buyback_groups_sort_order",
														"field_attr" => " int(11) NOT NULL default '50000' ",
														"add_after" => ""
														)
											);

add_field($add_new_field);
// End of insert new fields into orders table (for locking while editing processing order)

// Change field structure for product's location in products_description table
$change_field_structure = array();
$change_field_structure[TABLE_PRODUCTS_DESCRIPTION] = array (array ("field_name" => "products_location",
											 						"field_attr" => " varchar(255) NOT NULL default '' "
											 				  		)
													  		);
change_field_structure ($change_field_structure);
// End of change field structure for product's location in products_description table

// Insert new records into configuration table (Receiver's name and email address for low stock email)
$conf_insert_sql = array();
$conf_insert_sql["LOW_STOCK_EMAIL"] = array("insert" => " ('Low Stock Email Address', 'LOW_STOCK_EMAIL', '', 'Email address to which the low stock email will be send to.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 5, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (Receiver's name and email address for low stock email)

// Update records in configuration table (for Parameters Max Value and paypal email address display title and disabled buyback)
$conf_update_sql = array();
$conf_update_sql["LOW_STOCK_EMAIL"] = array("update" => " sort_order=5 " );
$conf_update_sql["STOCK_CHECK"] = array("update" => " sort_order=10 " );
$conf_update_sql["STOCK_LIMITED"] = array("update" => " sort_order=15 " );
$conf_update_sql["STOCK_ALLOW_CHECKOUT"] = array("update" => " sort_order=20 " );
$conf_update_sql["STOCK_MARK_PRODUCT_OUT_OF_STOCK"] = array("update" => " sort_order=25 " );
$conf_update_sql["STOCK_REORDER_LEVEL"] = array("update" => " sort_order=30 " );
$conf_update_sql["SYSTEM_PRODUCT_ETA"] = array("update" => " sort_order=35 " );
$conf_update_sql["SYSTEM_PRE_ORDER_LEVEL"] = array("update" => " sort_order=40 " );
$conf_update_sql["PRE_ORDER_DISCOUNT"] = array("update" => " sort_order=45 " );

$conf_update_sql["BUYBACK_ENABLED"] = array("update" => " configuration_value='false' " );

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (for Parameters Max Value and paypal email address display title and disabled buyback)

// Insert new records into configuration table (for buy back module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_CATEGORY_START_LEVEL"] = array("insert" => " ('Buyback Category Start Level', 'BUYBACK_CATEGORY_START_LEVEL', '', 'Set from which level of the tree to start display.', ".$row_sql["configuration_group_id"].", 62, NULL, 'now()', NULL, NULL)" );
	$conf_insert_sql["BUYBACK_CATEGORY_EXPAND_LEVEL"] = array("insert" => " ('Buyback Category Expand Level', 'BUYBACK_CATEGORY_EXPAND_LEVEL', '', 'Set how many levels to try and expand before showing the buyback products.', ".$row_sql["configuration_group_id"].", 64, NULL, 'now()', NULL, NULL)" );
	$conf_insert_sql["BUYBACK_FORM_MESSAGE"] = array("insert" => " ('Buyback Form Message', 'BUYBACK_FORM_MESSAGE', 'Click on the %IMAGE_EXPAND% and %IMAGE_COLLAPSE% buttons to expand and collapse the categories. Fill in the amount of item that you intend to sell and click on the Continue button at the bottom of the Buyback form. Please make sure that you are filling in for the correct categories.', 'Set Buyback Form Message', ".$row_sql["configuration_group_id"].", 80, NULL, 'now()', NULL, 'tep_cfg_textarea(')" );
	$conf_insert_sql["BUYBACK_CONFIRMATION_MESSAGE"] = array("insert" => " ('Buyback Confirmation Message', 'BUYBACK_CONFIRMATION_MESSAGE', 'Please double check that all information is correct and click on the \'Confirm\' button to continue.  You may click on the \'Back\' button to edit the quantity. If you are not logged on, you will be prompted to when you click on the confirm button. If you do not yet have an account here, do sign up one for free.<br>\r\nNOTE: Remember to include payment information in the comment box, ie: Paypal e-mail address, wire transfer information, etc.', 'Set Buyback Confirmation Message', ".$row_sql["configuration_group_id"].", 90, NULL, 'now()', NULL, 'tep_cfg_textarea(')" );
	$conf_insert_sql["BUYBACK_SUCCESS_MESSAGE"] = array("insert" => " ('Buyback Success Message', 'BUYBACK_SUCCESS_MESSAGE', 'Thank you for your interest, your buyback request has been submitted successfully. Your request will be processed in the order that the item was received. Your request may be rejected at the sole and discreet decision of the Management. You may now click on our live chat button on the right to arrange for a transfer right away.', 'Set Buyback Success Message', ".$row_sql["configuration_group_id"].", 100, NULL, 'now()', NULL, 'tep_cfg_textarea(')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for buy back module)
?>