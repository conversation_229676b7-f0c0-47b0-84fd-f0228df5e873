<?
/*
	$Id: version_1_9.php,v 1.4 2006/03/21 05:47:40 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Delete records from configuration table (for Buyback Disclaimer)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$inner_select_sql = "	SELECT configuration_id 
							FROM " . TABLE_CONFIGURATION . "
							WHERE configuration_key='BUYBACK_DISCLAIMER' AND configuration_group_id='".$row_sql["configuration_group_id"]."'" ;
	$inner_result_sql = tep_db_query($inner_select_sql);
	if ($inner_row = tep_db_fetch_array($inner_result_sql)) {	// if found existing record
		$conf_delete_sql = array();
		
		$conf_delete_sql['BUYBACK_DISCLAIMER'] = array(	"unique" => "1" );
		delete_records(TABLE_CONFIGURATION, "configuration_key", $conf_delete_sql, $DBTables);
	}
}
// End of delete records from configuration table (for Buyback Disclaimer)

// Insert new fields into orders_status_history table (for setting which comment to be shown as order remark)
$add_new_field = array();
$add_new_field[TABLE_ORDERS_STATUS_HISTORY] = array (	array (	"field_name" => "set_as_order_remarks",
																"field_attr" => " tinyint(1) NOT NULL default '0' ",
																"add_after" => "comments"
																)
													);
add_field($add_new_field);
// End of insert new fields into orders_status_history table (for setting which comment to be shown as order remark)

// Move contents of orders_remarks_history table to orders_status_history table
if (in_array(TABLE_ORDERS_REMARKS_HISTORY, $DBTables) && in_array(TABLE_ORDERS_STATUS_HISTORY, $DBTables)) {
	$move_field_contents = array();
	$move_field_contents = array  (	"from_field" => array("orders_id", "date_orders_remarks_added", "orders_remarks", "orders_remarks_added_by"),
									"to_field" => array("orders_id", "orders_status_id", "date_added", "customer_notified", "comments", "changed_by"),
									"extra_field" => "", 
									"skip_empty_record" => "1"
									);
	
	$existing_from_fields = get_table_fields(TABLE_ORDERS_REMARKS_HISTORY);
	$existing_to_fields = get_table_fields(TABLE_ORDERS_STATUS_HISTORY);
	
	if (is_array($move_field_contents["from_field"]) && is_array($move_field_contents["to_field"])) {
		$non_exist_from_fields = array_diff($move_field_contents["from_field"], $existing_from_fields);
		$non_exist_to_fields = array_diff($move_field_contents["to_field"], $existing_to_fields);
		
		if (count($non_exist_from_fields) > 0 || count($non_exist_to_fields) > 0) {
			;	// table structure had been changed
		} else {
			// Insert temp fields to hold the set_as_order_remarks value
			$add_new_field = array();
			$add_new_field[TABLE_ORDERS_REMARKS_HISTORY] = array (	array (	"field_name" => "set_as_order_remarks",
																			"field_attr" => " tinyint(1) NOT NULL default '0' ",
																			"add_after" => ""
																			)
																);
			add_field($add_new_field);
			
			$latest_order_remarks_select_sql = "SELECT or1.orders_remarks_history_id, or1.date_orders_remarks_added, max(or2.date_orders_remarks_added) AS latest_added_date
												FROM " . TABLE_ORDERS_REMARKS_HISTORY . " AS or1 
												INNER JOIN " . TABLE_ORDERS_REMARKS_HISTORY . " AS or2
													ON ( or1.orders_id=or2.orders_id )
												GROUP BY or1.orders_id, or1.date_orders_remarks_added
												HAVING max(or2.date_orders_remarks_added) = or1.date_orders_remarks_added ";
			$latest_order_remarks_result_sql = tep_db_query($latest_order_remarks_select_sql);
			while ($latest_order_remarks_row = tep_db_fetch_array($latest_order_remarks_result_sql)) { 
				tep_db_query("UPDATE " . TABLE_ORDERS_REMARKS_HISTORY . " SET set_as_order_remarks=1 WHERE orders_remarks_history_id='" . $latest_order_remarks_row["orders_remarks_history_id"] . "'");
			}
			
			$order_remarks_select_sql = "	SELECT * 
										  	FROM " . TABLE_ORDERS_REMARKS_HISTORY ;
			
			$order_remarks_result_sql = tep_db_query($order_remarks_select_sql);
			while ($order_remarks_row = tep_db_fetch_array($order_remarks_result_sql)) { 
				if (is_numeric($order_remarks_row["orders_remarks_added_by"])) {
	    			$admin_query = tep_db_query("SELECT admin_email_address FROM " . TABLE_ADMIN . " WHERE admin_id = '" . $order_remarks_row["orders_remarks_added_by"] . "'");
	    			if ($admin_info = tep_db_fetch_array($admin_query)) {
	    				$remark_by_admin_email_address = $admin_info["admin_email_address"];
	    			} else {
	    				$remark_by_admin_email_address = $order_remarks_row["orders_remarks_added_by"];
	    			}
	    		} else {
	    			$remark_by_admin_email_address = $order_remarks_row["orders_remarks_added_by"];
	    		}
				
				$insert_sql = " INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, set_as_order_remarks, changed_by) 
							 	VALUES (".$order_remarks_row['orders_id']. ", 0, '" . addslashes($order_remarks_row['date_orders_remarks_added']) . "', 0, '[From Order Remarks]\n" .addslashes($order_remarks_row['orders_remarks'])."', '" . $order_remarks_row["set_as_order_remarks"] . "', '" .addslashes($remark_by_admin_email_address)."') " ;
				
				tep_db_query($insert_sql);
			}
			
			$latest_order_history_select_sql = "SELECT oh1.*, max(oh2.date_added) AS latest_added_date, max(oh2.set_as_order_remarks) as setting
												FROM " . TABLE_ORDERS_STATUS_HISTORY . " AS oh1 
												INNER JOIN " . TABLE_ORDERS_STATUS_HISTORY . " AS oh2
													ON ( oh1.orders_id=oh2.orders_id ) 
												GROUP BY oh1.orders_id, oh1.date_added 
												HAVING max(oh2.date_added) = oh1.date_added AND max(oh2.set_as_order_remarks) <= 0
												ORDER BY oh1.orders_id desc";
			$latest_order_history_result_sql = tep_db_query($latest_order_history_select_sql);
			while ($latest_order_history_row = tep_db_fetch_array($latest_order_history_result_sql)) { 
				tep_db_query("UPDATE " . TABLE_ORDERS_STATUS_HISTORY . " SET set_as_order_remarks=1 WHERE orders_status_history_id='" . $latest_order_history_row["orders_status_history_id"] . "'");
			}
		}
	}
}
// End of move contents of orders_remarks_history table to orders_status_history table

// Delete orders_remarks_history table
$delete_tables_array = array();
$delete_tables_array = array(TABLE_ORDERS_REMARKS_HISTORY);
delete_tables ($delete_tables_array, $DBTables);
// End of delete orders_remarks_history table

// Create orders_log_table table
$add_new_tables = array();
$add_new_tables["orders_log_table"] = array (	"structure" => " CREATE TABLE `orders_log_table` (
																	`orders_log_id` int(11) NOT NULL auto_increment,
																	`orders_log_admin_id` int(11) NOT NULL default '0',
																	`orders_log_ip` varchar(15) NOT NULL default '',
			  														`orders_log_time` datetime default NULL,
			  														`orders_log_orders_id` int(11) NOT NULL default '0',
																	`orders_log_system_messages` text NOT NULL, 
			  														PRIMARY KEY  (`orders_log_id`)
																  ) TYPE=MyISAM;" ,
												"data" => ""
											);
	// Changes by subrat
$add_new_tables["brackets"] = array (	"structure" => " CREATE TABLE `brackets` (
														  	`brackets_id` int(11) NOT NULL auto_increment,
														  	`brackets_key` varchar(100) NOT NULL default '',
														  	`brackets_value` text NOT NULL,
														  	`brackets_dependent` int(11) NOT NULL default '0',
														  	`data_pool_level_id` int(11) NOT NULL default '0',
														  	PRIMARY KEY  (`brackets_id`)
														 ) TYPE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["brackets_groups"] = array ("structure" => " CREATE TABLE `brackets_groups` (
																`brackets_groups_id` int(11) NOT NULL auto_increment,
																`brackets_groups_name` varchar(255) NOT NULL default '',
																`brackets_groups_description` text NOT NULL,
																`brackets_groups_visible` smallint(1) NOT NULL default '1',
																`brackets_groups_sort_order` int(11) NOT NULL default '50000',
																PRIMARY KEY  (`brackets_groups_id`)
															 ) TYPE=MyISAM;" ,
											"data" => ""
											);

$add_new_tables["brackets_groups_to_level_tags"] = array (	"structure" => " CREATE TABLE `brackets_groups_to_level_tags` (
																			  	`brackets_groups_id` int(11) NOT NULL default '0',
																			  	`data_pool_level_tags_id` int(11) NOT NULL default '0',
																			  	PRIMARY KEY  (`brackets_groups_id`,`data_pool_level_tags_id`)
																			 ) TYPE=MyISAM;" ,
															"data" => ""
														 );

$add_new_tables["brackets_tags"] = array (	"structure" => " CREATE TABLE `brackets_tags` (
																`brackets_tags_id` int(11) NOT NULL auto_increment,
																`brackets_tags_key` varchar(100) NOT NULL default '',
																`brackets_tags_value` text NOT NULL,
																`brackets_tags_dependent` int(11) NOT NULL default '0',
																`data_pool_level_tags_id` int(11) NOT NULL default '0',
																`brackets_groups_id` int(11) NOT NULL default '0',
																PRIMARY KEY  (`brackets_tags_id`)
															 ) TYPE=MyISAM;" ,
											"data" => ""
										 );

$add_new_tables["data_pool"] = array (	"structure" => " CREATE TABLE `data_pool` (
														  	`data_pool_id` int(11) NOT NULL auto_increment,
														  	`data_pool_name` varchar(100) NOT NULL default '',
														  	`data_pool_class` varchar(100) NOT NULL default '',
														  	`data_pool_description` text NOT NULL,
														  	`data_pool_input_field` varchar(100) NOT NULL default '',
														  	PRIMARY KEY  (`data_pool_id`)
														 ) TYPE=MyISAM;" ,
										"data" => ""
									 );

$add_new_tables["data_pool_level"] = array ("structure" => " CREATE TABLE `data_pool_level` (
																`data_pool_level_id` int(11) NOT NULL auto_increment,
																`data_pool_level_parent_id` int(11) NOT NULL default '0',
																`data_pool_level_name` varchar(100) NOT NULL default '',
																`data_pool_level_value` double NOT NULL default '0',
																`products_id` int(11) NOT NULL default '0',
																`data_pool_sort_order` int(11) NOT NULL default '0',
																`data_pool_input_field` varchar(50) NOT NULL default '',
																`data_pool_level_class_name` varchar(255) NOT NULL default '',
																`data_pool_level_class` varchar(100) NOT NULL default '',
																PRIMARY KEY  (`data_pool_level_id`)
															) TYPE=MyISAM;" ,
											"data" => ""
									 	  );

$add_new_tables["data_pool_level_tags"] = array (	"structure" => " CREATE TABLE `data_pool_level_tags` (
																		`data_pool_level_tags_id` int(11) NOT NULL auto_increment,
																		`data_pool_level_parent_id` int(11) NOT NULL default '0',
																		`data_pool_level_name` varchar(100) NOT NULL default '',
																		`data_pool_level_value` double NOT NULL default '0',
																		`data_pool_ref_id` int(11) NOT NULL default '0',
																		`data_pool_sort_order` int(11) NOT NULL default '0',
																		PRIMARY KEY  (`data_pool_level_tags_id`)
																	 ) TYPE=MyISAM;" ,
													"data" => ""
									 	  		);

$add_new_tables["data_pool_ref"] = array (	"structure" => " CREATE TABLE `data_pool_ref` (
																`data_pool_ref_id` int(11) NOT NULL auto_increment,
																`data_pool_id` int(11) NOT NULL default '0',
																`data_pool_ref_value` varchar(100) NOT NULL default '',
																PRIMARY KEY  (`data_pool_ref_id`)
															) TYPE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["data_pool_template"] = array (	"structure" => " CREATE TABLE `data_pool_template` (
																	`data_pool_template_id` int(11) NOT NULL auto_increment,
																	`data_pool_template_name` varchar(100) NOT NULL default '',
																	`data_pool_template_description` text NOT NULL,
																	`data_pool_level_tags_id` int(11) NOT NULL default '0',
																	`custom_products_type_id` int(11) NOT NULL default '0',
																	PRIMARY KEY  (`data_pool_template_id`)
																) TYPE=MyISAM;" ,
												"data" => ""
											 );

$add_new_tables["data_pool_template_to_categories"] = array (	"structure" => " CREATE TABLE `data_pool_template_to_categories` (
																					`categories_id` int(11) NOT NULL default '0',
																					`data_pool_template_id` int(11) NOT NULL default '0',
																					PRIMARY KEY  (`categories_id`,`data_pool_template_id`)
																				 ) TYPE=MyISAM;" ,
																"data" => ""
											 				);

$add_new_tables["customers_basket_custom"] = array (	"structure" => " CREATE TABLE `customers_basket_custom` (
																			`customers_basket_id` int(11) NOT NULL default '0',
																		  	`customers_basket_custom_key` varchar(100) NOT NULL default '',
																		  	`customers_basket_custom_value` blob NOT NULL,
																		  	PRIMARY KEY  (`customers_basket_id`)
																		 ) TYPE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables["orders_custom_products"] = array (	"structure" => " CREATE TABLE `orders_custom_products` (
																	  	`orders_custom_products_id` int(11) NOT NULL auto_increment,
																	  	`products_id` int(11) NOT NULL default '0',
																	  	`orders_products_id` int(11) NOT NULL default '0',
																	  	`orders_custom_products_key` varchar(100) NOT NULL default '',
																	  	`orders_custom_products_value` text NOT NULL,
																	  	PRIMARY KEY  (`orders_custom_products_id`)
																	 ) TYPE=MyISAM;" ,
													"data" => ""
												 );

	// End of changes by subrat
add_new_tables ($add_new_tables, $DBTables);
// End of create orders_log_table table

// Change field structure for product's location in products_description table
$change_field_structure = array();
$change_field_structure[TABLE_PRODUCTS_DESCRIPTION] = array (array ("field_name" => "products_location",
											 						"field_attr" => " text "
											 				  		)
													  		);
change_field_structure ($change_field_structure);
// End of change field structure for product's location in products_description table

/* Changes by subrat */
// Insert new records into data_pool table (for Power Leveling)
$data_pool_insert_sql = array();
$data_pool_insert_sql[1] = array(	"insert" => " (1, 'Power Leveling', 'root', '', '') ",
								 	"update" => " data_pool_name='Power Leveling', data_pool_class='root', data_pool_description='', data_pool_input_field='' "
				   				);
insert_new_records("data_pool", "data_pool_id", $data_pool_insert_sql, $DBTables, "(data_pool_id, data_pool_name, data_pool_class, data_pool_description, data_pool_input_field)");
// End of insert new records into data_pool table (for Power Leveling)

// Insert new records into custom_products_type table (for Power Leveling)
$custom_product_insert_sql = array();
$custom_product_insert_sql[1] = array(	"insert" => " (1, 'Power Leveling', 1) ",
								 		"update" => " custom_products_type_name='Power Leveling', data_pool_id=1 "
				   					);
insert_new_records("custom_products_type", "custom_products_type_id", $custom_product_insert_sql, $DBTables, "(custom_products_type_id, custom_products_type_name, data_pool_id)");
// End of insert new records into custom_products_type table (for Power Leveling)

// Insert new records into buyback_status table (for Cancel status)
$buyback_status_insert_sql = array();
$buyback_status_insert_sql[4] = array(	"insert" => " (4, 1, 'Cancel') ",
										"update" => " language_id=1, buyback_status_name='Cancel' "
				   					 );

insert_new_records("buyback_status", "buyback_status_id", $buyback_status_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name)");
// End of insert new records into buyback_status table (for Cancel status)

// Update records in buyback_status table (for Completed status)
$buyback_status_update_sql = array();
$buyback_status_update_sql[3] = array("update" => " buyback_status_name='Completed' " );

update_records("buyback_status", "buyback_status_id", $buyback_status_update_sql, $DBTables);
// End of update records in buyback_status table (for Completed status)

// Insert new fields into buyback_request_group table (for admin comment)
$add_new_field = array();
$add_new_field[TABLE_BUYBACK_REQUEST_GROUP] = array (	array (	"field_name" => "buyback_request_group_admin_comment",
																"field_attr" => " text NOT NULL ",
																"add_after" => ""
																)
													);

add_field($add_new_field);
// End of insert new fields into buyback_request_group table (for admin comment)
/* End of changes by subrat */

// Insert new records into admin_files table (for custom product module)
$admin_files_insert_sql = array();
$admin_files_insert_sql["data_pool.php"] = array(	"insert" => " ('data_pool.php', 1, 0, '1') ",
													"update" => " admin_files_is_boxes=1, admin_files_to_boxes=0, admin_groups_id='1' "
				   								);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='data_pool.php' AND admin_files_is_boxes=1 ");
// End of insert new records into admin_files table (for custom product module)

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='data_pool.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for custom product module)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["data_pool.php"] = array(	"insert" => " ('data_pool.php', 0, '".$row_sql[admin_files_id]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   								);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='data_pool.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for custom product module)	
}
?>