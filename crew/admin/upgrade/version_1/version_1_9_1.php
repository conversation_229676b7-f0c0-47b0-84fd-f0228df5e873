<?
/*
  	$Id: version_1_9_1.php,v 1.3 2006/03/21 05:47:53 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into orders_status table (for Verifying status)
$order_status_insert_sql[7] = array("insert" => " (7, 1, 'Verifying') ",
									"update" => " language_id=1, orders_status_name='Verifying' "
				   					);

insert_new_records(TABLE_ORDERS_STATUS, "orders_status_id", $order_status_insert_sql, $DBTables, "(orders_status_id, language_id, orders_status_name)");
// End of insert new records into orders_status table (for Verifying status)

// Insert new records into configuration table (for money order payment processing status)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_MONEYORDER_PROCESSING_STATUS_ID"] = array("insert" => " ('Set Order\'s \"Processing\" Status', 'MODULE_PAYMENT_MONEYORDER_PROCESSING_STATUS_ID', '2', 'Set the initial processing status of orders made with this payment module to this value', 6, 30, NULL, NOW(), 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses(')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for money order payment processing status)

// Insert new field into orders_status and orders tables
$add_new_field = array();
$add_new_field[TABLE_ORDERS_STATUS] = array (	array (	"field_name" => "orders_status_sort_order",
									 					"field_attr" => " int(5) NOT NULL default '50000' ",
									 					"add_after" => ""
									 					)
										  );
$add_new_field[TABLE_ORDERS] = array (	array (	"field_name" => "orders_tag_ids",
							 					"field_attr" => " varchar(255) NOT NULL default '' ",
							 					"add_after" => ""
							 					),
							 			array (	"field_name" => "orders_read_mode",
							 					"field_attr" => " tinyint(1) NOT NULL default '1' ",
							 					"add_after" => "orders_tag_ids"
							 					)
								  );

add_field ($add_new_field);
// End of insert new field into orders_status and orders tables

// Change field structure for orders_read_mode in orders table
$change_field_structure = array();
$change_field_structure[TABLE_ORDERS] = array (array (	"field_name" => "orders_read_mode",
							 							"field_attr" => " tinyint(1) NOT NULL default '0' "
							 				  		)
									  		);
change_field_structure ($change_field_structure);
// End of change field structure for orders_read_mode in orders table

// Update records in orders_status table (assign sort order to each status)
$orders_status_update_sql = array();
$orders_status_update_sql["1"] = array("update" => " orders_status_sort_order='10' " );
$orders_status_update_sql["7"] = array("update" => " orders_status_sort_order='20' " );
$orders_status_update_sql["2"] = array("update" => " orders_status_sort_order='30' " );
$orders_status_update_sql["3"] = array("update" => " orders_status_sort_order='40' " );
$orders_status_update_sql["4"] = array("update" => " orders_status_sort_order='50' " );
$orders_status_update_sql["5"] = array("update" => " orders_status_sort_order='60' " );
$orders_status_update_sql["6"] = array("update" => " orders_status_sort_order='70' " );

update_records(TABLE_ORDERS_STATUS, "orders_status_id", $orders_status_update_sql, $DBTables);
// End of update records in orders_status table (assign sort order to each status)

// Update records in configuration table (Update paypal's "Set Order Status" from "Processing" to "Verifying")
$conf_update_sql = array();
$conf_update_sql["MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID"] = array("update" => " configuration_value='7' " );

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (Update paypal's "Set Order Status" from "Processing" to "Verifying")

// Insert new records into admin table (for tagging used in order lists)
$admin_files_insert_sql = array();
$admin_files_insert_sql["order_tags.php"] = array	(	"insert" => " ('order_tags.php', 0, 152, '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes=152, admin_groups_id='1' "
													);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='order_tags.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin table (for tagging used in order lists)

// Create orders_tag table
$add_new_tables = array();
$add_new_tables["orders_tag"] = array (	"structure" => " CREATE TABLE `orders_tag` (
															`orders_tag_id` int(11) NOT NULL auto_increment,
															`orders_tag_name` varchar(150) NOT NULL default '',
															`orders_tag_status_ids` varchar(32) NOT NULL default '',
	  														PRIMARY KEY  (`orders_tag_id`)
														  ) TYPE=MyISAM;" ,
										"data" => ""
									);

add_new_tables ($add_new_tables, $DBTables);
// End of create orders_tag table

// Insert new records into configuration table (for buy back module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_HIDE_ITEM_NAME"] = array("insert" => " ('Hide Buyback Item Name', 'BUYBACK_HIDE_ITEM_NAME', 'false', 'Hide or show the item name in the catalog page', ".$row_sql["configuration_group_id"].", 68, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for buy back module)
?>