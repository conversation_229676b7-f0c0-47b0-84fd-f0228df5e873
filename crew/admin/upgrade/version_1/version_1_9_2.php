<?
/*
  	$Id: version_1_9_2.php,v 1.2 2005/07/28 02:38:32 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into admin_files_actions and price_tags tables
$add_new_field = array();
$add_new_field[TABLE_ADMIN_FILES_ACTIONS] = array (	array (	"field_name" => "admin_files_sort_order",
										 					"field_attr" => " int(5) NOT NULL default '50000' ",
										 					"add_after" => "admin_files_actions_name"
										 					)
											  		);
$add_new_field[TABLE_PRICE_TAGS] = array (	array (	"field_name" => "price_tags_update_field",
								 					"field_attr" => " varchar(255) NOT NULL default 'products_price' ",
								 					"add_after" => "price_tags_field"
								 					)
									  		);

add_field ($add_new_field);
// End of insert new field into admin_files_actions and price_tags tables

// Change field structure for price_tags_update_field in price_tags table
$change_field_structure = array();
$change_field_structure[TABLE_PRICE_TAGS] = array (array (	"field_name" => "price_tags_update_field",
							 								"field_attr" => " varchar(255) NOT NULL default '' "
								 				  		),
								 				   array (	"field_name" => "tags_price",
							 								"field_attr" => " varchar(255) NOT NULL default '' "
								 				  		)
										  		);
change_field_structure ($change_field_structure);
// End of change field structure for price_tags_update_field in price_tags table

// Update records in admin_files_actions table (assign sort order to each admin file actions)
$admin_files_order_update_sql = array();
$admin_files_order_update_sql["REVERSIBLE_ORDER_STATUS"] = array("update" => " admin_files_sort_order='10' " );
$admin_files_order_update_sql["SAVE_ORDER_LISTS_CRITERIA"] = array("update" => " admin_files_sort_order='10' " );
$admin_files_order_update_sql["VIEW_PRODUCT_LOCATION"] = array("update" => " admin_files_sort_order='10' " );
$admin_files_order_update_sql["UNLOCK_OTHERS_ORDERS"] = array("update" => " admin_files_sort_order='20' " );

update_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_order_update_sql, $DBTables);
// End of update records in admin_files_actions table (assign sort order to each admin file actions)

// Insert new records into admin_files_actions table (for changing Processing to Verifying status)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["REVERSE_PROCESSING_2_VERIFYING"] = array("insert" => " ('REVERSE_PROCESSING_2_VERIFYING', 'Reverse order from \"Processing\" to \"Verifying\"', ".$row_sql["admin_files_id"].", '1', '15')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for changing Processing to Verifying status)
?>