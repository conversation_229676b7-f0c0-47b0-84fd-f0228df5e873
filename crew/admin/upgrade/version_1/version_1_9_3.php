<?
/*
  	$Id: version_1_9_3.php,v 1.2 2005/08/09 04:36:08 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/
/**************************************************************************
	Repeating of task done in version_1_9_2.php due to incompatible 
	version among stores.
**************************************************************************/
// Insert new field into admin_files_actions and price_tags tables
$add_new_field = array();
$add_new_field[TABLE_ADMIN_FILES_ACTIONS] = array (	array (	"field_name" => "admin_files_sort_order",
										 					"field_attr" => " int(5) NOT NULL default '50000' ",
										 					"add_after" => "admin_files_actions_name"
										 					)
											  		);
$add_new_field[TABLE_PRICE_TAGS] = array (	array (	"field_name" => "price_tags_update_field",
								 					"field_attr" => " varchar(255) NOT NULL default 'products_price' ",
								 					"add_after" => "price_tags_field"
								 					)
									  		);

add_field ($add_new_field);
// End of insert new field into admin_files_actions and price_tags tables

// Change field structure for price_tags_update_field in price_tags table
$change_field_structure = array();
$change_field_structure[TABLE_PRICE_TAGS] = array (array (	"field_name" => "price_tags_update_field",
							 								"field_attr" => " varchar(255) NOT NULL default '' "
								 				  		),
								 				   array (	"field_name" => "tags_price",
							 								"field_attr" => " varchar(255) NOT NULL default '' "
								 				  		)
										  		);
change_field_structure ($change_field_structure);
// End of change field structure for price_tags_update_field in price_tags table

// Update records in admin_files_actions table (assign sort order to each admin file actions)
$admin_files_order_update_sql = array();
$admin_files_order_update_sql["REVERSIBLE_ORDER_STATUS"] = array("update" => " admin_files_sort_order='10' " );
$admin_files_order_update_sql["SAVE_ORDER_LISTS_CRITERIA"] = array("update" => " admin_files_sort_order='10' " );
$admin_files_order_update_sql["VIEW_PRODUCT_LOCATION"] = array("update" => " admin_files_sort_order='10' " );
$admin_files_order_update_sql["UNLOCK_OTHERS_ORDERS"] = array("update" => " admin_files_sort_order='20' " );

update_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_order_update_sql, $DBTables);
// End of update records in admin_files_actions table (assign sort order to each admin file actions)

// Insert new records into admin_files_actions table (for changing Processing to Verifying status)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["REVERSE_PROCESSING_2_VERIFYING"] = array("insert" => " ('REVERSE_PROCESSING_2_VERIFYING', 'Reverse order from \"Processing\" to \"Verifying\"', ".$row_sql["admin_files_id"].", '1', '15')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for changing Processing to Verifying status)
/**************************************************************************
	End of repeating of task done in version_1_9_2.php due to incompatible 
	version among stores.
**************************************************************************/

// Insert new field into buyback related tables
$add_new_field = array();
$add_new_field["buyback_basket"] = array (	array (	"field_name" => "buyback_basket_user_type",
								 					"field_attr" => " smallint(1) default '0' ",
								 					"add_after" => ""
								 					)
									  		);
$add_new_field["buyback_request"] = array (	array (	"field_name" => "buyback_quantity_received",
								 					"field_attr" => " int(11) NOT NULL default '0' ",
								 					"add_after" => ""
								 					)
									  		);
$add_new_field[TABLE_BUYBACK_REQUEST_GROUP] = array (	array (	"field_name" => "buyback_request_group_user_type",
											 					"field_attr" => " tinyint(1) NOT NULL default '0' ",
											 					"add_after" => ""
											 					)
												  		);

add_field ($add_new_field);
// End of insert new field into buyback related tables

// Delete buyback_request_group_admin_comment field from buyback_request_group table
$delete_field = array();
$delete_field[TABLE_BUYBACK_REQUEST_GROUP] = array  (	array( "field_name" => "buyback_request_group_admin_comment") );

delete_field ($delete_field);
// End of delete buyback_request_group_admin_comment field from buyback_request_group table

// Create buyback_status_history table
$add_new_tables = array();
$add_new_tables["buyback_status_history"] = array (	"structure" => " CREATE TABLE buyback_status_history (
																		  buyback_status_history_id int(11) NOT NULL auto_increment,
																		  buyback_request_group_id int(11) NOT NULL default '0',
																		  buyback_status_id int(5) NOT NULL default '0',
																		  date_added datetime NOT NULL default '0000-00-00 00:00:00',
																		  customer_notified int(1) default '0',
																		  comments text,
																		  set_as_buyback_remarks tinyint(1) NOT NULL default '0',
																		  changed_by varchar(65) NOT NULL default '',
																		  PRIMARY KEY  (buyback_status_history_id)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);
add_new_tables ($add_new_tables, $DBTables);
// End of create buyback_status_history table

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for buyback request info module)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["buyback_requests_info.php"] = array(	"insert" => " ('buyback_requests_info.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
								   								);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='buyback_requests_info.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for buyback request info module)
}

?>