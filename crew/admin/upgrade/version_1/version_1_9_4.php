<?
/*
  	$Id: version_1_9_4.php,v 1.3 2006/03/21 05:48:05 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into payment_extra_info table (for post-authorisation)
$add_new_field = array();
$add_new_field[TABLE_PAYMENT_EXTRA_INFO] = array (	array (	"field_name" => "authorisation_mode",
										 					"field_attr" => " char(1) NOT NULL default 'A' ",
										 					"add_after" => ""
										 					),
										 			array (	"field_name" => "authorisation_result",
										 					"field_attr" => " text ",
										 					"add_after" => "authorisation_mode"
										 					)
											  		);

add_field ($add_new_field);
// End of insert new field into payment_extra_info table (for post-authorisation)

// Change field structure for price_tags_update_field in price_tags table
$change_field_structure = array();
$change_field_structure[TABLE_PAYMENT_EXTRA_INFO] = array (	array (	"field_name" => "authorisation_mode",
									 								"field_attr" => " char(1) NOT NULL default '' "
										 				  		),
										 				  	array (	"field_name" => "transaction_id",
									 								"field_attr" => " varchar(15) NOT NULL default '0' "
										 				  		)
												  		);
change_field_structure ($change_field_structure);
// End of change field structure for price_tags_update_field in price_tags table

// Insert new records into configuration table (for money order payment processing status)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_ID"] = array("insert" => " ('Remote Administration Installation ID', 'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_ID', '124610', 'Your WorldPay remote administration ID', 6, 8, NULL, NOW(), NULL, NULL)" );
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_PASSWORD"] = array("insert" => " ('Remote Administration Password', 'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_PASSWORD', 'E98hffs42', 'Your WorldPay remote administration password', 6, 9, NULL, NOW(), NULL, NULL)" );
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_POST_AUTH_TIME_OUT"] = array("insert" => " ('Post Authorisation Time Out', 'MODULE_PAYMENT_WORLDPAY_POST_AUTH_TIME_OUT', '5', 'Set the time out period(days) for WorldPay\'s Pre-Authorisation transaction.', 6, 6, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for money order payment processing status)

// Insert new field into price_tags table (for language dependent update)
$add_new_field = array();
$add_new_field[TABLE_PRICE_TAGS] = array (	array (	"field_name" => "tags_update_language_id",
								 					"field_attr" => " int(11) NOT NULL default '0' ",
								 					"add_after" => "price_tags_update_field"
								 					)
									  	);

add_field ($add_new_field);
// End of insert new field into price_tags table (for language dependent update)

// Insert new records into admin_files_actions table (for editing WorldPay Transaction ID (those manually entered) and Manual Authorisation)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ADM_EDIT_WORLDPAY_TRANSACTION_ID"] = array("insert" => " ('ADM_EDIT_WORLDPAY_TRANSACTION_ID', 'Edit WorldPay Transaction ID', ".$row_sql["admin_files_id"].", '1', '25')" );
	$admin_files_actions_insert_sql["ADM_WORLDPAY_MANUAL_AUTHORISATION"] = array("insert" => " ('ADM_WORLDPAY_MANUAL_AUTHORISATION', 'Manually authorise WorldPay order', ".$row_sql["admin_files_id"].", '1', '30')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for editing WorldPay Transaction ID (those manually entered) and Manual Authorisation)

// Insert new records into configuration table (Receiver's name and email address for manual stock deduction)
$conf_insert_sql = array();
$conf_insert_sql["MANUAL_STOCK_DEDUCTION_EMAIL"] = array("insert" => " ('Manual Stock Deduction Email Address', 'MANUAL_STOCK_DEDUCTION_EMAIL', '', 'Email address to which the stock deduction email will be send to whenever someone manually deducts the stock.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 7, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (Receiver's name and email address for manual stock deduction)


// Changes made by subrat
// Create supplier related tables
$add_new_tables = array();
$add_new_tables["supplier"] = array (	"structure" => " CREATE TABLE supplier (
															  supplier_id int(11) NOT NULL auto_increment,
															  supplier_gender char(1) NOT NULL default '',
															  supplier_firstname varchar(32) NOT NULL default '',
															  supplier_lastname varchar(32) NOT NULL default '',
															  supplier_dob datetime NOT NULL default '0000-00-00 00:00:00',
															  supplier_email_address varchar(96) NOT NULL default '',
															  supplier_telephone varchar(32) NOT NULL default '',
															  supplier_fax varchar(32) NOT NULL default '',
															  supplier_password varchar(40) NOT NULL default '',
															  supplier_street_address varchar(64) NOT NULL default '',
															  supplier_suburb varchar(64) NOT NULL default '',
															  supplier_city varchar(32) NOT NULL default '',
															  supplier_postcode varchar(10) NOT NULL default '',
															  supplier_state varchar(32) NOT NULL default '',
															  supplier_country_id int(11) NOT NULL default '0',
															  supplier_zone_id int(11) NOT NULL default '0',
															  supplier_password_reset varchar(40) NOT NULL default '',
															  supplier_status tinyint(1) NOT NULL default '0',
															  supplier_company varchar(60) NOT NULL default '',
															  supplier_payment_check varchar(100) NOT NULL default '',
															  supplier_payment_paypal varchar(64) NOT NULL default '',
															  supplier_payment_bank_name varchar(64) NOT NULL default '',
															  supplier_payment_bank_branch_number varchar(64) NOT NULL default '',
															  supplier_payment_bank_swift_code varchar(64) NOT NULL default '',
															  supplier_payment_bank_account_name varchar(64) NOT NULL default '',
															  supplier_payment_bank_account_number varchar(64) NOT NULL default '',
															  supplier_date_of_last_logon datetime NOT NULL default '0000-00-00 00:00:00',
															  supplier_number_of_logons int(11) NOT NULL default '0',
															  supplier_date_account_created datetime NOT NULL default '0000-00-00 00:00:00',
															  supplier_date_account_last_modified datetime NOT NULL default '0000-00-00 00:00:00',
															  supplier_activation_code varchar(40) default NULL,
															  PRIMARY KEY  (supplier_id)
														) TYPE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["supplier_history"] = array (	"structure" => " CREATE TABLE supplier_history (
																	  supplier_history_id int(11) NOT NULL auto_increment,
																	  supplier_history_group_id int(11) NOT NULL default '0',
																	  supplier_history_product text NOT NULL,
																	  buyback_request_id int(11) NOT NULL default '0',
																	  PRIMARY KEY  (supplier_history_id)
																) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["supplier_history_group"] = array (	"structure" => " CREATE TABLE supplier_history_group (
																		  supplier_history_group_id int(11) NOT NULL auto_increment,
																		  supplier_id int(11) NOT NULL default '0',
																		  supplier_history_group_date date NOT NULL default '0000-00-00',
																		  supplier_history_group_time time NOT NULL default '00:00:00',
																		  PRIMARY KEY  (supplier_history_group_id)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["supplier_pricing"] = array (	"structure" => " CREATE TABLE supplier_pricing (
																	  supplier_pricing_id int(11) NOT NULL auto_increment,
																	  products_id int(11) NOT NULL default '0',
																	  supplier_pricing_min_quantity int(11) NOT NULL default '0',
																	  supplier_pricing_max_quantity int(11) NOT NULL default '0',
																	  supplier_pricing_unit_price double NOT NULL default '0',
																	  supplier_pricing_disabled smallint(1) NOT NULL default '0',
																	  supplier_pricing_server_full smallint(1) NOT NULL default '0',
																	  PRIMARY KEY  (supplier_pricing_id)
																) TYPE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["supplier_time_setting"] = array (	"structure" => " CREATE TABLE supplier_time_setting (
																		  supplier_time_setting_id int(11) NOT NULL auto_increment,
																		  normal_status varchar(100) NOT NULL default 'STATUS_OFF',
																		  normal_start_time time NOT NULL default '00:00:00',
																		  auto_on smallint(6) NOT NULL default '1',
																		  normal_end_time time NOT NULL default '00:00:00',
																		  auto_off smallint(6) NOT NULL default '0',
																		  current_status varchar(100) NOT NULL default 'STATUS_OFF',
																		  PRIMARY KEY  (supplier_time_setting_id)
																	) TYPE=MyISAM;" ,
													"data" => "INSERT INTO `supplier_time_setting` (supplier_time_setting_id, normal_status, normal_start_time, auto_on, normal_end_time, auto_off, current_status) VALUES (1, 'STATUS_OFF', '00:00:00', 0, '00:00:00', 0, 'STATUS_OFF')"
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create supplier related tables

// Insert new records into admin_files table (for buyback request info print)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for buyback module)
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["buyback_requests_info_print.php"] = array	(	"insert" => " ('buyback_requests_info_print.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																			"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
								   										);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='buyback_requests_info_print.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for buyback request info print)
}

// Insert new records into admin_files_actions table (for supplier info viewing)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='buyback_requests_info.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLER_INFO_VIEWING"] = array("insert" => " ('SUPPLER_INFO_VIEWING', 'Able to view supplier information', ".$row_sql["admin_files_id"].", '1', '10')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for supplier info viewing)

// Insert new records into configuration table (Buyback Supplier Display Levels)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_SUPPLIER_DISPLAY_LEVELS"] = array("insert" => " ('Buyback Supplier Display Levels', 'BUYBACK_SUPPLIER_DISPLAY_LEVELS', '2', 'Comma seperated level numbers to hide certain categories.', ".$row_sql["configuration_group_id"].", 110, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (Buyback Supplier Display Levels)
?>