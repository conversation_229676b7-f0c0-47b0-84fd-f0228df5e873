<?
/*
  	$Id: version_1_9_5.php,v 1.1 2005/09/06 08:26:21 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (Supplier Notification Email)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["SUPPLIER_EMAIL_ACTIVATION"] = array("insert" => " ('Supplier Email Activation', 'SUPPLIER_EMAIL_ACTIVATION', '', 'Email address to which the supplier activation email will be send to whenever there is new supplier registered.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 120, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (Supplier Notification Email)

?>