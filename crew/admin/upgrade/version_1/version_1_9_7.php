<?
/*
  	$Id: version_1_9_7.php,v 1.1 2005/10/11 04:59:12 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into supplier_pricing, buyback_basket, and buyback_request table
$add_new_field = array();

$add_new_field[TABLE_SUPPLIER_PRICING] = array (array (	"field_name" => "supplier_pricing_product_status",
										 				"field_attr" => " varchar(20) NOT NULL default '' ",
										 				"add_after" => ""
										 				),
										 		array (	"field_name" => "supplier_pricing_comment",
										 				"field_attr" => " text ",
										 				"add_after" => ""
										 				)
											  	);

$add_new_field[TABLE_BUYBACK_BASKET] = array (array (	"field_name" => "buyback_basket_comment",
										 				"field_attr" => " text ",
										 				"add_after" => ""
										 				)
											  	);

$add_new_field[TABLE_BUYBACK_REQUEST] = array (array (	"field_name" => "buyback_comment",
										 				"field_attr" => " text ",
										 				"add_after" => ""
										 				)
											  	);

add_field ($add_new_field);
// End of insert new field into supplier_pricing, buyback_basket, and buyback_request table

// Insert new records into admin_files_actions table (for permission on viewing customer and/or payment details)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_CUSTOMER_DETAILS"] = array("insert" => " ('ORDER_CUSTOMER_DETAILS', 'Customer Details', ".$row_sql["admin_files_id"].", '1', 3)" );
	$admin_files_actions_insert_sql["ORDER_PAYMENT_DETAILS"] = array("insert" => " ('ORDER_PAYMENT_DETAILS', 'Payment Details', ".$row_sql["admin_files_id"].", '1', 5)" );
	$admin_files_actions_insert_sql["ORDER_PWL_DETAILS"] = array("insert" => " ('ORDER_PWL_DETAILS', 'Power Leveling Details Information', ".$row_sql["admin_files_id"].", '1', 7)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing customer and/or payment details)

// Insert new records into admin_files_actions table (for permission on editing customer individual discount)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_INDIVIDUAL_DISCOUNT"] = array("insert" => " ('CUSTOMER_INDIVIDUAL_DISCOUNT', 'Grant Customer Individual Discount', ".$row_sql["admin_files_id"].", '1', 5)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on editing customer individual discount)

?>