<?
/*
  $Id: version_1_9_8.php,v 1.1 2005/10/26 10:22:30 weichen Exp $

  Developer: <PERSON>
  Copyright (c) 2004 SKC Ventrue

  Released under the GNU General Public License
*/

// Insert new records into configuration table (The IP address of Password Console Server)
$conf_insert_sql = array();
$conf_insert_sql["PASSWORD_CONSOLE_IP_ADDRESS"] = array("insert" => " ('Password Console Server IP Address', 'PASSWORD_CONSOLE_IP_ADDRESS', '************', 'The valid IP address of Password Console Server that remotely connecting to store.', 9, 50, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (The IP address of Password Console Server)

?>