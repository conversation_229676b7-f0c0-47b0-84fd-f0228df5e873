<?
/*
  	$Id: version_2_0.php,v 1.6 2006/07/28 03:26:43 weichen Exp $
	
  	Developer: <PERSON> (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create store merging related tables
$add_new_tables = array();
$add_new_tables["categories_configuration"] = array ("structure" => " CREATE TABLE `categories_configuration` (
																		  `categories_configuration_id` int(11) NOT NULL auto_increment,
																		  `categories_id` int(11) NOT NULL,
																		  `categories_configuration_title` varchar(64) NOT NULL default '',
																		  `categories_configuration_key` varchar(64) NOT NULL default '',
																		  `categories_configuration_value` text NOT NULL,
																		  `categories_configuration_description` varchar(255) NOT NULL default '',
																		  `categories_configuration_group_id` int(11) NOT NULL default '0',
																		  `sort_order` int(5) default NULL,
																		  `last_modified` datetime default NULL,
																		  `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																		  `use_function` varchar(255) default NULL,
																		  `set_function` varchar(255) default NULL,
																		  PRIMARY KEY  (`categories_configuration_id`)
																	  ) TYPE=MyISAM;" ,
													"data" => ""
													);

$add_new_tables["themes"] = array ("structure" => " CREATE TABLE `themes` (
														`themes_id` int(11) NOT NULL auto_increment,
														`themes_language_id` int(11) NOT NULL,
														`themes_title` varchar(128) NOT NULL default '',
														`themes_description` varchar(255) NOT NULL default '',
														`themes_date_created` datetime default NULL,
														`themes_last_modified` datetime NOT NULL default '0000-00-00 00:00:00',
														`themes_type` varchar(20) NOT NULL default 'user',
														PRIMARY KEY  (`themes_id`),
														UNIQUE KEY `themes_title` (`themes_title`)
													  ) TYPE=MyISAM;" ,
									"data" => ""
									);

$add_new_tables["themes_to_categories"] = array ("structure" => " CREATE TABLE `themes_to_categories` (
																	`themes_id` int(11) NOT NULL default '0',
			  														`categories_id` int(11) NOT NULL default '0',
																	`start_date` date default NULL,
																	`end_date` date default NULL,
																	PRIMARY KEY  (`themes_id`,`categories_id`)
																  ) TYPE=MyISAM;" ,
												"data" => ""
												);

$add_new_tables["customers_groups_discount"] = array (	"structure" => " CREATE TABLE `customers_groups_discount` (
																			`customers_groups_discount_id` int(11) NOT NULL auto_increment,
																			`customers_groups_id` int(11) NOT NULL default '0',
				  															`categories_id` int(11) NOT NULL default '0',
																			`customers_groups_discount` decimal(8,2) NOT NULL default '-0.00',
																			PRIMARY KEY  (`customers_groups_discount_id`),
																			UNIQUE KEY `unique_gid_cid` (`customers_groups_id`, `categories_id`), 
																			KEY `index_customers_groups_id` (`customers_groups_id`),
																			KEY `index_categories_id` (`categories_id`) 
																	  	) TYPE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables["admin_files_categories"] = array (	"structure" => " CREATE TABLE `admin_files_categories` (
																		`admin_files_id` int(11) NOT NULL default '0',
																		`admin_groups_id` int(11) NOT NULL default '0',
			  															`categories_ids` text NOT NULL,
																		PRIMARY KEY  (`admin_files_id`, `admin_groups_id`),
																		KEY `index_customers_groups_id` (`admin_groups_id`) 
																  	) TYPE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["newsletters_groups"] = array (	"structure" => " CREATE TABLE `newsletters_groups` (
																  	`newsletters_groups_id` int(11) NOT NULL auto_increment,
  																	`newsletters_groups_name` varchar(255) NOT NULL default '',
  																	`module` varchar(255) NOT NULL default 'newsletter',
  																	`newsletters_groups_sort_order` int(5) NOT NULL default '50000',
																  	PRIMARY KEY  (`newsletters_groups_id`)
																) TYPE=MyISAM;" ,
												"data" => "	INSERT INTO `newsletters_groups` (`newsletters_groups_id`, `newsletters_groups_name`, `module`) 
																VALUES (1, 'Product Notification', 'product_notification'), (2, 'General Newsletter', 'newsletter');
															"
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create store merging related tables

// Insert new fields into infolinks table (for categories dependant) and newsletters table (for newsletters group id)
$add_new_field = array();

$existing_infolinks_fields = get_table_fields(TABLE_INFOLINKS);
if (!in_array('infolinks_cat_id', $existing_infolinks_fields)) {
	$update_infolinks_cat_id = true;
} else {
	$update_infolinks_cat_id = false;
}

$add_new_field[TABLE_INFOLINKS] = array (	array (	"field_name" => "infolinks_cat_id",
													"field_attr" => " text ",
								 					"add_after" => ""
								 			   		),
								 			array (	"field_name" => "infolinks_include_subcat",
													"field_attr" => " tinyint(1) NOT NULL default '0' ",
								 					"add_after" => ""
								 			   		)
										);

$add_new_field[TABLE_NEWSLETTERS] = array (	array (	"field_name" => "newsletters_groups_id",
													"field_attr" => " int(11) NOT NULL default '0' ",
								 					"add_after" => "status"
								 			   		)
										);

$add_new_field["admin_files"] = array (	array (	"field_name" => "admin_files_cat_setting",
												"field_attr" => " tinyint(1) NOT NULL default '0' ",
							 					"add_after" => ""
							 			   		)
									);

add_field ($add_new_field);
// End of insert new fields into infolinks table (for categories dependant) and newsletters table (for newsletters group id)

if ($update_infolinks_cat_id) {
	// Update records in infolinks table (set all infolinks to shown in all categories)
	$infolinks_update_sql = array();
	$infolinks_update_sql[TABLE_INFOLINKS] = array(	array(	"field_name" => "infolinks_cat_id",
															"update" => " infolinks_cat_id='0' ",
															"where_str" => " infolinks_cat_id IS NULL "
														),
													array(	"field_name" => "infolinks_include_subcat",
															"update" => " infolinks_include_subcat='1' ",
															"where_str" => " 1 "
														)
												);
	
	advance_update_records($infolinks_update_sql, $DBTables);
	// End of update records in infolinks table (set all infolinks to shown in all categories)
}

// Update records in newsletters table
$newsletters_update_sql = array();
$newsletters_update_sql[TABLE_NEWSLETTERS] = array(	array(	"field_name" => "module",
															"update" => " newsletters_groups_id='1' ",
															"where_str" => " newsletters_groups_id=0 and module='product_notification' "
														),
													array(	"field_name" => "module",
															"update" => " newsletters_groups_id='2' ",
															"where_str" => " newsletters_groups_id=0 and module='newsletter' "
														)
												);

advance_update_records($newsletters_update_sql, $DBTables);
// End of update records in newsletters table

// Update records in admin_files table
$admin_files_name_array = array(FILENAME_BATCH_UPDATE_PRICES, FILENAME_BUYBACK_GROUPS, FILENAME_BUYBACK_PRODUCTS, FILENAME_BUYBACK_REQUESTS,
								FILENAME_CATEGORIES, FILENAME_CUSTOMERS_GROUPS, FILENAME_DATA_POOL, FILENAME_ORDERS,
								FILENAME_PRICE_TAGS, FILENAME_PRODUCTS_PURCHASE_QUANTITY, FILENAME_SUPPLIERS_ORDERS, FILENAME_THEME_ASSIGNATION,
								FILENAME_SALES_REPORT, FILENAME_LOG_FILES, FILENAME_SUPPLIERS_REPORT, FILENAME_INFOLINKS_INDEX, FILENAME_THEME_ASSIGNATION);

$admin_files_update_sql = array();
$admin_files_update_sql["admin_files"] = array(	array(	"field_name" => "admin_files_cat_setting",
														"update" => " admin_files_cat_setting='1' ",
														"where_str" => " admin_files_is_boxes=0 and admin_files_name IN ('" . implode("', '", $admin_files_name_array) . "') "
													)
											);

advance_update_records($admin_files_update_sql, $DBTables);
// End of update records in admin_files table

// Auto create default theme for each language
$select_sql = "	SELECT languages_id, name 
				FROM " . TABLE_LANGUAGES ;
$result_sql = tep_db_query($select_sql);
while ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$default_theme_insert_sql = array();
	$theme_title = $row_sql["name"] . ' Default Theme';
	$default_theme_insert_sql[$theme_title] = array("insert" => " ('".$row_sql["languages_id"]."', '".tep_db_input($theme_title)."', '".tep_db_input($theme_title)."', now(), 'system') ",
													"update" => " themes_title='".tep_db_input($theme_title)."', themes_description='".tep_db_input($theme_title)."', themes_last_modified=now(), themes_type='system' "
								   				 );
	
	insert_new_records(TABLE_THEMES, "themes_title", $default_theme_insert_sql, $DBTables, "(themes_language_id, themes_title, themes_description, themes_date_created, themes_type)", " themes_language_id='".$row_sql["languages_id"]."' AND themes_type='system' ");
}
// End of auto create default theme for each language

// Assign customer related setting from Parameters Min Value group to Customer Options group
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Customer Options'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_update_sql = array();
	$conf_update_sql["ENTRY_FIRST_NAME_MIN_LENGTH"] = array("update" => " sort_order=10, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_LAST_NAME_MIN_LENGTH"] = array("update" => " sort_order=15, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_DOB_MIN_LENGTH"] = array("update" => " sort_order=20, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_EMAIL_ADDRESS_MIN_LENGTH"] = array("update" => " sort_order=25, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_STREET_ADDRESS_MIN_LENGTH"] = array("update" => " sort_order=30, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_COMPANY_MIN_LENGTH"] = array("update" => " sort_order=35, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_POSTCODE_MIN_LENGTH"] = array("update" => " sort_order=40, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_CITY_MIN_LENGTH"] = array("update" => " sort_order=45, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_STATE_MIN_LENGTH"] = array("update" => " sort_order=50, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_TELEPHONE_MIN_LENGTH"] = array("update" => " sort_order=55, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["ENTRY_PASSWORD_MIN_LENGTH"] = array("update" => " sort_order=60, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["CC_OWNER_MIN_LENGTH"] = array("update" => " sort_order=65, configuration_group_id=".$row_sql["configuration_group_id"] );
	$conf_update_sql["CC_NUMBER_MIN_LENGTH"] = array("update" => " sort_order=70, configuration_group_id=".$row_sql["configuration_group_id"] );
	
	$conf_update_sql["SHIPPING_ORIGIN_COUNTRY"] = array("update" => " use_function=NULL ");
	
	update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
}
// End of assign customer related setting from Parameters Min Value group to Customer Options group

// Moving categories based configuration records to categories_configuration table
$categories_configuration_select_sql = " SELECT categories_configuration_id FROM categories_configuration; " ;
$categories_configuration_result_sql = tep_db_query($categories_configuration_select_sql);
if (!tep_db_num_rows($categories_configuration_result_sql)) {	// if not found existing record
	$all_root_array = array(0);
	$root_id_select_sql = "SELECT categories_id FROM " . TABLE_CATEGORIES . " WHERE parent_id=0";
	$root_id_result_sql = tep_db_query($root_id_select_sql);
	while ($root_id_row = tep_db_fetch_array($root_id_result_sql)) {
		$all_root_array[] = $root_id_row["categories_id"];
	}
	
	$buyback_cfg_key_array = array();
	$buyback_cfg_select_sql = "	SELECT c.configuration_key 
								FROM " . TABLE_CONFIGURATION_GROUP . " AS cg 
								INNER JOIN " . TABLE_CONFIGURATION . " AS c 
									ON cg.configuration_group_id=c.configuration_group_id 
								WHERE cg.configuration_group_title='Buyback' AND c.configuration_key NOT IN ('SUPPLIER_EMAIL_ACTIVATION', 'BUYBACK_AGREEMENT', 'BUYBACK_FORM_MESSAGE', 'BUYBACK_CONFIRMATION_MESSAGE', 'BUYBACK_SUCCESS_MESSAGE')" ;
	$buyback_cfg_result_sql = tep_db_query($buyback_cfg_select_sql);
	while ($buyback_cfg_row = tep_db_fetch_array($buyback_cfg_result_sql)) {
		$buyback_cfg_key_array[] = $buyback_cfg_row['configuration_key'];
	}
	
	$prod_display_cfg_key_array = array();
	$prod_display_cfg_select_sql = "SELECT c.configuration_key 
									FROM " . TABLE_CONFIGURATION_GROUP . " AS cg 
									INNER JOIN " . TABLE_CONFIGURATION . " AS c 
										ON cg.configuration_group_id=c.configuration_group_id 
									WHERE cg.configuration_group_title='Product Display' AND c.configuration_key NOT IN ('PREV_NEXT_BAR_LOCATION', 'PRODUCT_LIST_FILTER')" ;
	$prod_display_cfg_result_sql = tep_db_query($prod_display_cfg_select_sql);
	while ($prod_display_cfg_row = tep_db_fetch_array($prod_display_cfg_result_sql)) {
		$prod_display_cfg_key_array[] = $prod_display_cfg_row['configuration_key'];
	}
	
	$conf_to_be_moved = array ( array('id' => 'Buyback', 'conf_key' => "'" . implode("', '", $buyback_cfg_key_array) . "'"),
								array('id' => 'Parameters Max Value', 'conf_key' => "'MAX_DISPLAY_LATEST_NEWS', 'MAX_DISPLAY_LATEST_NEWS_PAGE', 'MAX_DISPLAY_NEW_PRODUCTS', 'MAX_DISPLAY_BESTSELLERS'"),
								array('id' => 'Parameters Min Value', 'conf_key' => "'MIN_DISPLAY_BESTSELLERS'"),
								array('id' => 'Product Display', 'conf_key' => "'" . implode("', '", $prod_display_cfg_key_array) . "'"),
								array('id' => 'Stock Options', 'conf_key' => "'LOW_STOCK_EMAIL', 'STOCK_REORDER_LEVEL', 'SYSTEM_PRODUCT_ETA', 'PRE_ORDER_DISCOUNT', 'MANUAL_STOCK_DEDUCTION_EMAIL', 'PACKAGE_QUANTITY_FOR_BATCH_UPDATE', 'PRODUCT_NAME_FOR_BATCH_UPDATE', 'SHOW_BUY_QTY_BOX', 'CDKEY_LOW_STOCK_EMAIL', 'MANUAL_CDKEY_DEDUCTION_EMAIL', 'MANUAL_CDKEY_ADDITION_EMAIL'"),
								array('id' => 'Store Information', 'conf_key' => "'PRODUCTS_FLAG', 'SHOW_BESTSELLERS', 'PRODUCT_REVIEW', 'SHOW_ALSO_PURCHASED_PRODUCTS', 'BROWSER_TITLE', 'META_DESCRIPTION', 'META_KEYWORDS'"),
							 );
	
	for ($conf_cnt=0; $conf_cnt < count($conf_to_be_moved); $conf_cnt++) {
		$conf_grp_select_sql = "SELECT configuration_group_id 
								FROM " . TABLE_CONFIGURATION_GROUP . "
								WHERE configuration_group_title='".$conf_to_be_moved[$conf_cnt]['id']."'" ;
		$conf_grp_result_sql = tep_db_query($conf_grp_select_sql);
		if ($conf_grp_row = tep_db_fetch_array($conf_grp_result_sql)) {
			$configuration_select_sql = "	SELECT configuration_id, configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, last_modified, date_added, use_function, set_function 
											FROM " . TABLE_CONFIGURATION . "
											WHERE configuration_group_id='".$conf_grp_row["configuration_group_id"]."'" . ($conf_to_be_moved[$conf_cnt]['conf_key'] == 'all' ? '' : " AND configuration_key IN (" . $conf_to_be_moved[$conf_cnt]['conf_key'] . ")") ;
			$configuration_result_sql = tep_db_query($configuration_select_sql);
			while ($configuration_row = tep_db_fetch_array($configuration_result_sql)) {
				foreach($all_root_array as $cat_id) {
					$sql_data_array = array('categories_id' => (int)$cat_id,
		                      				'categories_configuration_title' => tep_db_prepare_input($configuration_row["configuration_title"]),
		                      				'categories_configuration_key' => tep_db_prepare_input($configuration_row["configuration_key"]),
		                      				'categories_configuration_value' => tep_db_prepare_input($configuration_row["configuration_value"]),
		                      				'categories_configuration_description' => tep_db_prepare_input($configuration_row["configuration_description"]),
		                      				'categories_configuration_group_id' => tep_db_prepare_input($configuration_row["configuration_group_id"]),
		                      				'sort_order' => tep_db_prepare_input($configuration_row["sort_order"]),
		                      				'last_modified' => tep_db_prepare_input($configuration_row["last_modified"]),
		                      				'date_added' => tep_db_prepare_input($configuration_row["date_added"]),
		                      				'use_function' => tep_db_prepare_input($configuration_row["use_function"]),
		                      				'set_function' => tep_db_prepare_input($configuration_row["set_function"])
		                      				);
					tep_db_perform("categories_configuration", $sql_data_array);
				}
				// remove the current configuration record
				tep_db_query("DELETE FROM " . TABLE_CONFIGURATION . " WHERE configuration_id='" . $configuration_row["configuration_id"] . "'");
			}
		}
	}
	
	// Remove useless configuration key(s)
	$conf_delete_sql = array();
	
	$conf_delete_sql['CATEGORY_FOR_BATCH_UPDATE'] = array(	"unique" => "1" );
	$conf_delete_sql['SYSTEM_PRE_ORDER_LEVEL'] = array(	"unique" => "1" );
	
	delete_records(TABLE_CONFIGURATION, "configuration_key", $conf_delete_sql, $DBTables);
	// End of remove useless configuration key(s)
}
// End of moving categories based configuration records to categories_configuration table

//(1) theme_id = 0 for default
//array('id' => 'Images', 'conf_key' => 'all'),
//create theme_configuration table

// Insert new records into admin_files table (for theme module)
$admin_files_insert_sql = array();

$admin_files_insert_sql["theme.php"] = array	(	"insert" => " ('theme.php', 1, 0, '1') ",
													"update" => " admin_files_is_boxes=1, admin_files_to_boxes=0, admin_groups_id='1' "
				   								);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
// End of insert new records into admin_files table (for theme module)

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='theme.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for theme module)
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["theme_customizing.php"] = array(	"insert" => " ('theme_customizing.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   										);
	$admin_files_insert_sql["theme_assignation.php"] = array(	"insert" => " ('theme_assignation.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   										);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}

// Copy contents of customers_groups_discount in customers_groups table to customers_groups_discount in customers_groups_discount table
$customers_groups_discount_select_sql = " SELECT customers_groups_id FROM customers_groups_discount; " ;
$customers_groups_discount_result_sql = tep_db_query($customers_groups_discount_select_sql);
if (!tep_db_num_rows($customers_groups_discount_result_sql)) {	// if not found existing record
	$customers_groups_select_sql = "SELECT customers_groups_id, customers_groups_discount FROM " . TABLE_CUSTOMERS_GROUPS;
	$customers_groups_result_sql = tep_db_query($customers_groups_select_sql);
	
	while ($customers_groups_row = tep_db_fetch_array($customers_groups_result_sql)) {
		$sql_data_array = array('customers_groups_id' => $customers_groups_row['customers_groups_id'],
		      					'categories_id' => 16,
		      					'customers_groups_discount' => $customers_groups_row['customers_groups_discount']
		      					);
		tep_db_perform("customers_groups_discount", $sql_data_array);
	}
	
	// Delete customers_groups_discount field from customers_groups table
	$delete_field = array();
	$delete_field[TABLE_CUSTOMERS_GROUPS] = array  ( array( "field_name" => "customers_groups_discount") );
	
	delete_field ($delete_field);
	// End of delete customers_groups_discount field from customers_groups table
}
// End of copy contents of customers_groups_discount in customers_groups table to customers_groups_discount in customers_groups_discount table

// Populate the admin_files_categories table for Top Administrator
$unassigned_admin_files_select_sql = "	SELECT af.admin_files_id 
										FROM admin_files AS af 
										LEFT JOIN admin_files_categories AS afa 
											ON (af.admin_files_id=afa.admin_files_id AND afa.admin_groups_id=1) 
										WHERE afa.admin_files_id IS NULL 
											AND af.admin_files_is_boxes=0";
$unassigned_admin_files_result_sql = tep_db_query($unassigned_admin_files_select_sql);

while ($unassigned_admin_files_row = tep_db_fetch_array($unassigned_admin_files_result_sql)) {
	$sql_data_array = array('admin_files_id' => $unassigned_admin_files_row['admin_files_id'],
	      					'admin_groups_id' => 1,
	      					'categories_ids' => '0'
	      					);
	tep_db_perform("admin_files_categories", $sql_data_array);
}
// End of populate the admin_files_categories table for Top Administrator

// Delete module field from newsletters table
$delete_field = array();
$delete_field[TABLE_NEWSLETTERS] = array  ( array( "field_name" => "module") );

delete_field ($delete_field);
// End of delete module field from newsletters table

// Change field structure for customers_newsletter in customers table
$change_field_structure = array();
$change_field_structure[TABLE_CUSTOMERS] = array (array (	"field_name" => "customers_newsletter",
															"field_attr" => " varchar(255) default NULL "
											 			)
													);
change_field_structure ($change_field_structure);
// End of change field structure for customers_newsletter in customers table

// Define log_time and log_products_id as index key in log_table table
add_index_key (TABLE_LOG_TABLE, 'index_log_time', 'index', 'log_time', $DBTables);

add_index_key (TABLE_LOG_TABLE, 'index_log_products_id', 'index', 'log_products_id', $DBTables);

// End of define log_time and log_products_id as index key in log_table table
?>