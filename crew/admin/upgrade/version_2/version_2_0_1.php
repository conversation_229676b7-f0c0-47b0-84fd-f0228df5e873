<?
/*
	$Id: version_2_0_1.php,v 1.1 2006/08/01 03:54:58 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2004 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_orders_custom_products_fields = get_table_fields('orders_custom_products');

// Insert new field into orders_custom_products table
$add_new_field = array();

$add_new_field['orders_custom_products'] = array (	array (	"field_name" => "orders_custom_products_number",
															"field_attr" => " smallint unsigned NOT NULL default '0' ",
															"add_after" => ""
								 						)
									  			);

$add_new_field['supplier_tasks_allocation'] = array (	array (	"field_name" => "orders_tag_ids",
																"field_attr" => " varchar(255) NOT NULL ",
																"add_after" => ""
								 							)
									  				);

$add_new_field['orders_tag'] = array (	array (	"field_name" => "filename",
												"field_attr" => " varchar(255) NOT NULL default 'order_track.php' ",
												"add_after" => ""
												)
									);

add_field ($add_new_field, false);
// End of insert new field into orders_custom_products table

// Create supplier_tasks_status table
$add_new_tables = array();

$add_new_tables["supplier_tasks_status"] = array (	"structure" => "  	CREATE TABLE `supplier_tasks_status` (
																		  	`supplier_tasks_status_id` int(11) NOT NULL default '0',
																		  	`language_id` int(11) NOT NULL default '1',
																		  	`supplier_tasks_status_name` varchar(32) NOT NULL default '',
																		  	`supplier_tasks_status_sort_order` int(5) NOT NULL default '50000',
																		  	PRIMARY KEY  (`supplier_tasks_status_id`,`language_id`)
																		) ENGINE=MyISAM;" ,
													"data" => "	INSERT INTO `supplier_tasks_status` (`supplier_tasks_status_id`, `language_id`, `supplier_tasks_status_name`, `supplier_tasks_status_sort_order`) 
																VALUES 	(0, 1, 'New', 10),
																		(1, 1, 'In Progress', 20),
																		(2, 1, 'On Hold', 30),
																		(3, 1, 'Done', 40),
																		(4, 1, 'Completed (Awaiting for Payment)', 50),
																		(5, 1, 'Paid', 60),
																		(0, 2, '&#26032;&#21333;&#23376;', 10),
																		(1, 2, '&#36827;&#34892;&#20013;', 20),
																		(2, 2, '&#26242;&#20572;', 30),
																		(3, 2, '&#23436;&#27605;', 40),
																		(4, 2, '&#23436;&#25104;', 50),
																		(5, 2, '&#24050;&#25903;&#20184;', 60);"
												);
add_new_tables ($add_new_tables, $DBTables);
// End of create supplier_tasks_status table

// Batch update the existing records in orders_custom_products table
if (!in_array('orders_custom_products_number', $existing_orders_custom_products_fields)) {
	$orders_info_select_sql = "SELECT distinct(orders_id) FROM " . TABLE_ORDERS_PRODUCTS . " WHERE custom_products_type_id = 1 ";
	$orders_info_result_sql = tep_db_query($orders_info_select_sql);
	
	while ($orders_info_row = tep_db_fetch_array($orders_info_result_sql)) {
		$count = 1;
		
		$orders_products_id_select_sql = "SELECT orders_products_id FROM " . TABLE_ORDERS_PRODUCTS . " WHERE orders_id ='" . (int)$orders_info_row["orders_id"] . "' ORDER BY orders_products_id";
		$orders_products_id_result_sql = tep_db_query($orders_products_id_select_sql);
		
		while ($orders_products_id_row = tep_db_fetch_array($orders_products_id_result_sql)) {
			$check_update_exist_select_sql = "SELECT orders_products_id FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " WHERE orders_products_id ='" . (int)$orders_products_id_row["orders_products_id"] . "' AND orders_custom_products_key ='power_leveling_info'";
			$check_update_exist_result_sql = tep_db_query($check_update_exist_select_sql);
			if ($check_update_exist_row = tep_db_fetch_array($check_update_exist_result_sql)) {
				$custom_products_number_update_sql = "UPDATE " . TABLE_ORDERS_CUSTOM_PRODUCTS . " SET orders_custom_products_number ='" . (int)$count . "' WHERE orders_products_id ='" . (int)$check_update_exist_row["orders_products_id"] . "' AND orders_custom_products_key ='power_leveling_info'";
				tep_db_query($custom_products_number_update_sql);
				$count++;
			}
		}
	}
}
// End of batch update the existing records in orders_custom_products table

// Insert new records into admin_files_actions table (for permission on export customer list and batch email customer)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["BATCH_EMAIL_CUSTOMERS"] = array("insert" => " ('BATCH_EMAIL_CUSTOMERS', 'Batch e-mail customers', ".$row_sql["admin_files_id"].", '1', 7)" );
	$admin_files_actions_insert_sql["EXPORT_CUSTOMER_LISTS"] = array("insert" => " ('EXPORT_CUSTOMER_LISTS', 'Export customer search result lists', ".$row_sql["admin_files_id"].", '1', 8)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on export customer list and batch email customer)

// Insert new records into admin_files_actions table (for permission on search by category)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='order_track.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SEARCH_BY_ORDERED_CATEGORIES"] = array("insert" => " ('SEARCH_BY_ORDERED_CATEGORIES', 'Search by ordered categories', ".$row_sql["admin_files_id"].", '1', '7')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on search by category)

?>