<?
/*
	$Id: version_2_0_2.php,v 1.1 2006/08/03 06:41:54 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on changing powerleveling progress status from On Hold to Completed)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='progress_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["PWL_PROGRESS_STATUS_HOLD_2_COMPLETED"] = array("insert" => " ('PWL_PROGRESS_STATUS_HOLD_2_COMPLETED', 'Update progress status from \"Hold\" to \"Completed\"', ".$row_sql["admin_files_id"].", '1', 22)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on changing powerleveling progress status from On Hold to Completed)

// Insert new records into admin_files_actions table (for permission on reversing supplier payments)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers_payment.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["REVERSE_SUPPLIER_PAYMENT"] = array("insert" => " ('REVERSE_SUPPLIER_PAYMENT', 'Reverse supplier payment', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on reversing supplier payments)

// Insert new records into configuration table (for reversing supplier payment notification e-mail)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["REVERSE_SUPPLIER_PAYMENT_EMAIL"] = array("insert" => " ('Reverse Supplier Payment Email Address', 'REVERSE_SUPPLIER_PAYMENT_EMAIL', '', 'Email address to which the email will be send to whenever there is reversing of supplier payments.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', '".$row_sql["configuration_group_id"]."', 130, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for reversing supplier payment notification e-mail)

// Update records in admin_files table
$admin_files_name_array = array(FILENAME_STATS_PRODUCTS_PURCHASED, FILENAME_STATS_PRODUCTS_VIEWED, FILENAME_STATS_SALES_REPORT, FILENAME_STOCK_REPORT);

$admin_files_update_sql = array();
$admin_files_update_sql["admin_files"] = array(	array(	"field_name" => "admin_files_cat_setting",
														"update" => " admin_files_cat_setting='1' ",
														"where_str" => " admin_files_is_boxes=0 and admin_files_name IN ('" . implode("', '", $admin_files_name_array) . "') "
													)
											);

advance_update_records($admin_files_update_sql, $DBTables);
// End of update records in admin_files table

?>