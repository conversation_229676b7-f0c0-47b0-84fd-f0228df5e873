<?
/*
	$Id: version_2_0_3.php,v 1.1 2006/08/08 05:54:14 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on view avg buyback price in Supplier Report page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_REPORT_BUYBACK_PRICE_INFO"] = array("insert" => " ('SUPPLIER_REPORT_BUYBACK_PRICE_INFO', 'View products average buyback price', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on view avg buyback price in Supplier Report page)

// Insert new records into configuration table (for advertisement)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Advertisement'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["GOOGLEBOT_CRAWL_NOTIFICATION_EMAIL"] = array("insert" => " ('Googlebot Crawl Notification Email Address', 'GOOGLEBOT_CRAWL_NOTIFICATION_EMAIL', '', 'Email address to which the email will be send to whenever Google\'s spider visiting your site.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 40, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for advertisement)

// Define orders_products_id and orders_custom_products_key as index key in orders_custom_products
add_index_key (TABLE_ORDERS_CUSTOM_PRODUCTS, 'index_orders_products_id', 'index', 'orders_products_id', $DBTables);
add_index_key (TABLE_ORDERS_CUSTOM_PRODUCTS, 'index_orders_custom_products_key', 'index', 'orders_custom_products_key', $DBTables);
// End of define orders_products_id and orders_custom_products_key as index key in orders_custom_products
?>