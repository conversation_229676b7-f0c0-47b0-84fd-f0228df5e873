<?
/*
	$Id: version_2_0_4.php,v 1.2 2006/08/10 02:17:14 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Update records in configuration table (for STORE_COUNTRY's use_function)
$conf_update_sql = array();
$conf_update_sql["STORE_COUNTRY"] = array("update" => " use_function=NULL ");

update_records(TABLE_CONFIGURATION, "configuration_key", $conf_update_sql, $DBTables);
// End of update records in configuration table (for STORE_COUNTRY's use_function)

// Insert new fields into supplier_tasks_allocation_history, supplier_tasks_allocation and products_purchases tables (for show remarks to supplier and add ratio adjust option)
$add_new_field = array();

$add_new_field[TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY] = array (	array (	"field_name" => "supplier_tasks_allocation_history_show",
																			"field_attr" => " tinyint(1) NOT NULL default '1' ",
																			"add_after" => ""
															   			)
									  							);

$add_new_field[TABLE_SUPPLIER_TASKS_ALLOCATION] = array (	array (	"field_name" => "supplier_tasks_allocation_ratio_adjust",
																	"field_attr" => " decimal(15, 4) DEFAULT NULL ",
																	"add_after" => "supplier_tasks_status"
													   			)
									  					);

$add_new_field[TABLE_PRODUCTS_PURCHASES] = array (	array (	"field_name" => "products_purchases_max_quantity_overwrite",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => "products_purchases_quantity_overwrite"
												   			)
									  			);

add_field($add_new_field);
// End of insert new fields into supplier_tasks_allocation_history, supplier_tasks_allocation and products_purchases tables (for show remarks to supplier and add ratio adjust option)

// Insert new records into admin_files_actions table (for permission on adjusting payout ratio)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='progress_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_CP_PAYABLE_MANUAL_ADJUST"] = array("insert" => " ('SUPPLIER_CP_PAYABLE_MANUAL_ADJUST', 'Adjust supplier payable amount', ".$row_sql["admin_files_id"].", '1', 27)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on adjusting payout ratio)

// Create products_purchases_lists_setting table
$add_new_tables = array();

$add_new_tables["products_purchases_lists_setting"] = array (	"structure" => "CREATE TABLE `products_purchases_lists_setting` (
																			  		`products_purchases_lists_id` int(11) NOT NULL default '0',
																				 	`products_purchases_lists_setting_key` varchar(255) NOT NULL default '',
																					`products_purchases_lists_setting_value` varchar(255) NOT NULL default '',
																					PRIMARY KEY  (`products_purchases_lists_id`,`products_purchases_lists_setting_key`)
																				) ENGINE=MyISAM;" ,
																"data" => ""
															);
add_new_tables ($add_new_tables, $DBTables);
// End of create products_purchases_lists_setting table
?>