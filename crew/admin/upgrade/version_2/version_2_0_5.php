<?
/*
	$Id: version_2_0_5.php,v 1.1 2006/08/16 02:19:56 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into orders_products table (for storing product's store price before any discount)
$add_new_field = array();

$add_new_field[TABLE_ORDERS_PRODUCTS] = array (	array (	"field_name" => "orders_products_store_price",
														"field_attr" => " decimal(15,4) default NULL ",
														"add_after" => "products_name"
											   			)
											);

add_field($add_new_field);
// End of insert new fields into orders_products table (for storing product's store price before any discount)
?>