<?
/*
	$Id: version_2_1.php,v 1.2 2006/08/21 11:49:43 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into categories_configuration table (for batch update increment value setting)
$conf_insert_sql = array();

$conf_insert_sql["INCREMENT_VALUE_FOR_BATCH_UPDATE"] = array("insert" => " (0, 'Price Update Increment Value', 'INCREMENT_VALUE_FOR_BATCH_UPDATE', '', 'The increment value for calculating prices of the subproduct in the package(s).', 9, 120, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into categories_configuration table (for batch update increment value setting)

// Insert new records into admin table (for batch update of products price)
$admin_files_insert_sql = array();

$admin_files_insert_sql["batch_update_prices2.php"] = array("insert" => " ('batch_update_prices2.php', 0, 3, '1', 1) ", 
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes=3, admin_groups_id='1', admin_files_cat_setting='1' ");

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id, admin_files_cat_setting)", " admin_files_name='batch_update_prices2.php' AND admin_files_is_boxes=0 ");
// End of insert new records into admin table (for batch update of products price)

// Insert new records into admin_files_categories table
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='batch_update_prices2.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_categories_insert_sql = array();
	
	$admin_files_categories_insert_sql[$row_sql["admin_files_id"]] = array("insert" => " (".$row_sql["admin_files_id"].", '1', 0)" );
	insert_new_records(TABLE_ADMIN_FILES_CATEGORIES, "admin_files_id", $admin_files_categories_insert_sql, $DBTables, "(`admin_files_id`, `admin_groups_id`, `categories_ids`)", " admin_files_id='".$row_sql["admin_files_id"]."' AND admin_groups_id=1 ");
}
// End of insert new records into admin_files_categories table

// Change field structure for products_price in products table
$change_field_structure = array();

$change_field_structure['products'] = array (array ("field_name" => "products_price",
													"field_attr" => " decimal(15,6) NOT NULL default '0.000000' "
									 				)
											);

change_field_structure ($change_field_structure);
// End of change field structure for products_price in products table
?>