<?
/*
	$Id: version_2_10.php,v 1.1 2007/07/26 11:41:13 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_orders_products_fields = get_table_fields("orders_products");

// Insert new fields into orders_products table
$add_new_field = array();

$add_new_field['orders_products'] = array (	array (	"field_name" => "parent_orders_products_id",
													"field_attr" => " int(11) NOT NULL default '0' ",
													"add_after" => "products_bundle_id"
													)
										);

add_field($add_new_field);
// End of insert new fields into orders_products table

// Update records in orders_products table (Set relationship between sub product and main product)
if (!in_array('parent_orders_products_id', $existing_orders_products_fields)) {
	$order_product_info_select_sql = "	SELECT orders_products_id, orders_id, products_id 
										FROM " . TABLE_ORDERS_PRODUCTS . " 
										WHERE custom_products_type_id = 0 
											AND products_bundle_id = 0 
											AND orders_products_is_compensate = 0";
	$order_product_info_result_sql = tep_db_query($order_product_info_select_sql);
	
	while ($order_product_info_row = tep_db_fetch_array($order_product_info_result_sql)) {
		$order_product_parent_info_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
													SET parent_orders_products_id = '" . (int)$order_product_info_row['orders_products_id'] . "' 
													WHERE orders_id = '" . (int)$order_product_info_row['orders_id'] . "' 
														AND products_bundle_id = '" . (int)$order_product_info_row['products_id'] . "' 
														AND orders_products_is_compensate = 0 
														AND custom_products_type_id = 0";
		tep_db_query($order_product_parent_info_update_sql);
	}
}
// End of update records in orders_products table (Set relationship between sub product and main product)
?>