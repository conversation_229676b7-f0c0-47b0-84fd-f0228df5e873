<?
/*
	$Id: version_2_10_1.php,v 1.1 2007/07/31 10:06:42 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_categories_fields = get_table_fields("categories");

// Insert new fields into categories table
$add_new_field = array();

$add_new_field['categories'] = array (	array (	"field_name" => "categories_buyback_main_cat",
												"field_attr" => " tinyint(1) unsigned NOT NULL default '0' ",
												"add_after" => ""
												)
									);

add_field($add_new_field);
// End of insert new fields into categories table

// Update records in categories table (Set all main categories as buyback main category)
if (!in_array('categories_buyback_main_cat', $existing_categories_fields)) {
	$categories_update_sql = array();
	
	$categories_update_sql['categories'] = array(	array(	"field_name" => "categories_buyback_main_cat",
															"update" => " categories_buyback_main_cat='1' ",
															"where_str" => " parent_id = 0"
															)
												 );
	
	advance_update_records($categories_update_sql, $DBTables);
}
// End of update records in categories table (Set all main categories as buyback main category)
?>