<?
/*
	$Id: version_2_10_2.php,v 1.1 2007/08/02 16:54:19 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into products table (For delivery eta message)
$add_new_field = array();

$add_new_field['products'] = array (	array (	"field_name" => "products_add_to_cart_msg",
												"field_attr" => " varchar(128) default NULL ",
												"add_after" => "products_eta"
												),
										array (	"field_name" => "products_preorder_msg",
												"field_attr" => " varchar(128) default NULL ",
												"add_after" => "products_add_to_cart_msg"
												),
										array (	"field_name" => "products_out_of_stock_msg",
												"field_attr" => " varchar(128) default NULL ",
												"add_after" => "products_preorder_msg"
												)
									);

add_field($add_new_field);
// End of insert new fields into products table (For delivery eta message)
?>