<?
/*
	$Id: version_2_10_3.php,v 1.2 2007/08/06 18:02:48 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into buyback_supplier_price_bracket_set table (For minimum purchase demand qty)
$add_new_field = array();

$add_new_field['buyback_supplier_price_bracket_set'] = array (	array (	"field_name" => "buyback_supplier_price_bracket_set_min_purchase",
																		"field_attr" => " int(11) default NULL ",
																		"add_after" => "buyback_supplier_price_bracket_set_minimum_value"
																		)
															);

add_field($add_new_field);
// End of insert new fields into buyback_supplier_price_bracket_set table (For minimum purchase demand qty)

// Define products_id as index key in competitors_average_price_history table
add_index_key ('competitors_average_price_history', 'index_products_id', 'index', 'products_id', $DBTables);
// End of define products_id as index key in competitors_average_price_history table

?>