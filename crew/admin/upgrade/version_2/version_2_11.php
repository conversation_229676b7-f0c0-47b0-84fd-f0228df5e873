<?
/*
	$Id: version_2_11.php,v 1.1 2007/08/13 17:53:50 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create iPay88 and multiple currency related tables
$add_new_tables = array();

$add_new_tables["ipay88"] = array (	"structure" => "CREATE TABLE ipay88 (
														ipay88_orders_id int(11) NOT NULL default '0',
													  	ipay88_merchant_code varchar(20) NOT NULL default '',
													  	ipay88_payment_method tinyint(1) unsigned NOT NULL default '0',
													  	ipay88_payment_amount decimal(15,2) NOT NULL default '0.00',
													  	ipay88_currency char(5) default NULL,
													  	ipay88_remark varchar(100) NOT NULL default '',
													  	ipay88_trans_id varchar(30) NOT NULL default '',
													  	ipay88_bank_auth_code varchar(10) NOT NULL default '',
													  	ipay88_status tinyint(1) NOT NULL default '0',
													  	ipay88_err_desc varchar(100) DEFAULT NULL,
													  	ipay88_sha1_signature varchar(100) NOT NULL default '',
													  	PRIMARY KEY  (ipay88_orders_id)
													) TYPE=MyISAM COMMENT='iPay88 Transaction Table';" ,
									"data" => ""
								);

$add_new_tables["ipay88_payment_status_history"] = array (	"structure" => "CREATE TABLE `ipay88_payment_status_history` (
																			  	ipay88_payment_status_history_id int(11) NOT NULL auto_increment,
																			  	ipay88_orders_id int(11) NOT NULL default '0',
																			  	ipay88_status_key varchar(32) NOT NULL default '',
																				ipay88_date datetime NOT NULL default '0000-00-00 00:00:00',
																				ipay88_status tinyint(1) NOT NULL default '0',
																			  	PRIMARY KEY  (`ipay88_payment_status_history_id`),
																			  	KEY `index_orders_id` (`ipay88_orders_id`)
																			) TYPE=MyISAM COMMENT='iPay88 Transaction History Table';" ,
															"data" => ""
														);

$add_new_tables["products_currency_prices"] = array (	"structure" => "CREATE TABLE `products_currency_prices` (
																		  	products_id int(11) NOT NULL default '0',
																		  	products_currency_prices_code char(3) NOT NULL default '',
																		  	products_currency_prices_value decimal(15,6) NOT NULL default '0.000000',
																			PRIMARY KEY  (`products_id`, `products_currency_prices_code`)
																		) TYPE=MyISAM COMMENT='Product pre-defined multi currency price';" ,
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create iPay88 and multiple currency related tables

// Insert new fields into currencies, products and orders_compensate_products tables
$add_new_field = array();

$add_new_field['currencies'] = array (	array (	"field_name" => "currencies_used_for",
												"field_attr" => " varchar(32) NOT NULL default '' ",
												"add_after" => "currencies_live_update"
												)
									);

$add_new_field['products'] = array (	array (	"field_name" => "products_base_currency",
												"field_attr" => " char(3) NOT NULL default '".DEFAULT_CURRENCY."' ",
												"add_after" => "products_price"
												)
									);

$add_new_field['orders_compensate_products'] = array (	array (	"field_name" => "compensate_entered_currency",
																"field_attr" => " char(3) NOT NULL default '".DEFAULT_CURRENCY."' ",
																"add_after" => "compensate_for_orders_products_id"
																),
														array (	"field_name" => "compensate_entered_currency_value",
																"field_attr" => " decimal(15,4) NOT NULL default '1.0000' ",
																"add_after" => "compensate_entered_currency"
																),
														array (	"field_name" => "compensate_order_currency",
																"field_attr" => " char(3) NOT NULL default '".DEFAULT_CURRENCY."' ",
																"add_after" => "compensate_entered_currency_value"
																),
														array (	"field_name" => "compensate_order_currency_value",
																"field_attr" => " decimal(15,4) NOT NULL default '1.0000' ",
																"add_after" => "compensate_order_currency"
																)
													);

add_field($add_new_field);
// End of insert new fields into currencies, products and orders_compensate_products tables

// Update records in currencies table (define which currency is used for SELL/BUY)
$currencies_update_sql = array();

$currencies_update_sql["USD"] = array("update" => " currencies_used_for='SELL,BUY' " );
$currencies_update_sql["CNY"] = array("update" => " currencies_used_for='BUY' " );
$currencies_update_sql["MYR"] = array("update" => " currencies_used_for='SELL' " );

update_records("currencies", "code", $currencies_update_sql, $DBTables);
// End of update records in currencies table (define which currency is used for SELL/BUY)
?>