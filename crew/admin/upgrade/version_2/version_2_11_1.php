<?
/*
	$Id: version_2_11_1.php,v 1.1 2007/08/16 11:08:41 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on view Back Order link and edit Show/Hide RSTK CHAR)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='buyback_requests_info.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["BUYBACK_ORDER_BACK_ORDER_LINK"] = array("insert" => " ('BUYBACK_ORDER_BACK_ORDER_LINK', 'Access Back Orders Link', ".$row_sql["admin_files_id"].", '1,22,23,27,38,41,45', 40)" );
	$admin_files_actions_insert_sql["BUYBACK_ORDER_EDIT_RSTK_CHAR_STATUS"] = array("insert" => " ('BUYBACK_ORDER_EDIT_RSTK_CHAR_STATUS', 'Turning on/off Restock Character', ".$row_sql["admin_files_id"].", '1,23,27,38,41,45', 50)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on view Back Order link and edit Show/Hide RSTK CHAR)
?>