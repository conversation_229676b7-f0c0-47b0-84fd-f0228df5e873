<?
/*
	$Id: version_2_11_2.php,v 1.1 2007/08/27 19:31:29 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_customers_info_verification_fields = get_table_fields("customers_info_verification");

// Insert new fields into customers_info_verification table
$add_new_field = array();

$add_new_field['customers_info_verification'] = array (	array (	"field_name" => "customers_info_verification_mode",
																"field_attr" => " char(1) NOT NULL default 'A' ",
																"add_after" => ""
																)
													);

add_field($add_new_field);
// End of insert new fields into customers_info_verification table

// Update records in customers_info_verification table (Set all email verification as Auto and telephone verification as Manual)
if (!in_array('customers_info_verification_mode', $existing_customers_info_verification_fields)) {
	$verification_mode_update_sql = array();
	
	$verification_mode_update_sql['customers_info_verification'] = array(	array(	"field_name" => "customers_info_verification_mode",
																					"update" => " customers_info_verification_mode='M' ",
																					"where_str" => " info_verification_type = 'telephone' AND serial_number = '' AND verify_try_turns = 0 "
																					)
																		 );
	
	advance_update_records($verification_mode_update_sql, $DBTables);
}
// End of update records in customers_info_verification table (Set all email verification as Auto and telephone verification as Manual)

// Update records in countries table (assign international dialing code to each countries)
$dialing_code_update_sql = array();

$dialing_code_update_sql["80"] = array("update" => " countries_international_dialing_code='995' " );
$dialing_code_update_sql["29"] = array("update" => " countries_international_dialing_code='47' " );
$dialing_code_update_sql["94"] = array("update" => " countries_international_dialing_code='672' " );

update_records(TABLE_COUNTRIES, "countries_id", $dialing_code_update_sql, $DBTables);

// End of update records in countries table (assign international dialing code to each countries)

?>