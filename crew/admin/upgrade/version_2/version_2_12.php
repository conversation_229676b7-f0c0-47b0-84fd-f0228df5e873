<?
/*
	$Id: version_2_12.php,v 1.1 2007/10/06 05:33:05 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create buyback_price_control table
$add_new_tables = array();

$add_new_tables["buyback_price_control"] = array (	"structure" => "CREATE TABLE `buyback_price_control` (
																	  `products_id` int(11) NOT NULL default '0',
																	  `buyback_price_control_for_site_id` int(11) NOT NULL default '0',
																	  `buyback_price_control_site_id` int(11) NOT NULL default '0',
																	  `buyback_price_control_value` decimal(15,4) NOT NULL default '0.0000',
																	  PRIMARY KEY  (`products_id`, `buyback_price_control_for_site_id`)
																	) ENGINE=MyISAM ;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create buyback_price_control table

?>