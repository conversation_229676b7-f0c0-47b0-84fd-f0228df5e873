<?
/*
	$Id: version_2_12_2.php,v 1.1 2007/10/11 02:58:16 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_cron_process_track_fields = get_table_fields("cron_process_track");

// Insert new fields into cron_process_track table
$add_new_field = array();

$add_new_field['cron_process_track'] = array (	array (	"field_name" => "cron_process_track_filename",
														"field_attr" => " varchar(255) NOT NULL default 'cron_pending_credit.php' ",
														"add_after" => ""
														)
											);

add_field($add_new_field);
// End of insert new fields into cron_process_track table

// Update records in status_configuration table (for Customer Order - Auto cancel pending orders)
$status_configuration_update_sql = array();

$status_configuration_update_sql['status_configuration'] = array(	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='1' AND status_configuration_destination_status_id='5'"
																		)
																 );

advance_update_records($status_configuration_update_sql, $DBTables);
// End of update records in status_configuration table (for Customer Order - Auto cancel pending orders)

// Insert cron track record for cron_customer_orders.php
$cron_track_rec_select_sql = "	SELECT cron_process_track_filename 
								FROM cron_process_track 
								WHERE cron_process_track_filename = 'cron_customer_orders.php'";
$cron_track_rec_result_sql = tep_db_query($cron_track_rec_select_sql);

if (!tep_db_num_rows($cron_track_rec_result_sql)) {
	$cron_track_rec_data_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_customer_orders.php'
				                       );
	tep_db_perform('cron_process_track', $cron_track_rec_data_array);
}
// End of insert cron track record for cron_customer_orders.php

// Reconstruct cron_process_track table
if (!in_array('cron_process_track_filename', $existing_cron_process_track_fields)) {
	// Drop existing primary key (cron_process_track_start_date) for cron_process_track table
	drop_index_key ("cron_process_track", 'PRIMARY KEY', 'primary', $DBTables, array('cron_process_track_start_date'));
	// End of drop existing primary key (cron_process_track_start_date) for cron_process_track table
	
	// Insert new primary key field into cron_process_track table
	add_index_key ("cron_process_track", 'primary key', 'primary', 'cron_process_track_filename', $DBTables);
	// End of insert new primary key field into cron_process_track table
}
// End of reconstruct cron_process_track table
?>