<?
/*
	$Id: version_2_12_3.php,v 1.1 2007/10/11 02:58:16 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "25M");

include_once(DIR_WS_CLASSES . 'edit_order.php');
include_once(DIR_WS_CLASSES . 'store_credit.php');

if (file_exists(DIR_WS_LANGUAGES . 'english/orders.php')) {
	include_once(DIR_WS_LANGUAGES . 'english/orders.php');
}

include_once(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();

$payment_info_array = array();
$credit_activity_array = array();
$pending_status = tep_get_order_status_name(1, 1);
$cancel_status = tep_get_order_status_name(5, 1);

$installed_modules = explode(';', 'cashU.php;ddoreft.php;egold.php;iPay88.php;moneybookers.php;moneyorder.php;moneyorderorcheck.php;paypal.php;pm2checkout.php;wire_transfers.php;worldpay.php;wu.php');
foreach ($installed_modules as $module_file) {
	$module_class = substr($module_file, 0, strrpos($module_file, '.'));
	include_once(DIR_FS_CATALOG_LANGUAGES . 'english/modules/payment/' . $module_file);
	include_once(DIR_FS_CATALOG_MODULES . 'payment/' . $module_file);
	
	if (tep_class_exists($module_class)) {
		$pm_module = new $module_class;
		
		if (isset($pm_module->auto_cancel_period) && is_numeric($pm_module->auto_cancel_period) && $pm_module->auto_cancel_period > 0) {
			$payment_info_array[strtolower($pm_module->title)] = $pm_module->auto_cancel_period;
		}
	}
}

if (count($payment_info_array)) {
	foreach ($payment_info_array as $pm_name => $cancel_period) {
		if ($cancel_period > 0) {
			// Grab the first N records
			$expired_order_select_sql = "	SELECT o.orders_id, o.customers_id, o.customers_name, o.customers_email_address, o.date_purchased, o.currency, o.currency_value 
											FROM " . TABLE_ORDERS . " AS o 
											WHERE o.orders_status = '1' 
												AND o.date_purchased < DATE_SUB(NOW(), INTERVAL ".tep_db_input($cancel_period)." MINUTE) 
												AND LOWER(o.payment_method) = '".tep_db_input($pm_name)."'";
			$expired_order_result_sql = tep_db_query($expired_order_select_sql);
			
			while ($expired_order_row = tep_db_fetch_array($expired_order_result_sql)) {
				$oID = $expired_order_row['orders_id'];
				
				switch ($pm_name) {
					case 'moneybookers.com':
						$pending_payment_select_sql = "	SELECT mb_mb_trans_id 
														FROM " . TABLE_PAYMENT_MONEYBOOKERS . "
														WHERE mb_trans_id = '" . tep_db_input($oID) . "'";
						$pending_payment_result_sql = tep_db_query($pending_payment_select_sql);
						$pending_payment_row = tep_db_fetch_array($pending_payment_result_sql);
						
						if ($pending_payment_row['mb_mb_trans_id'] > 0) {
							continue 2;	// Note that in PHP the switch statement is considered a looping structure for the purposes of continue.
						}
						break;
					default:
						break;
				}
				$order_comment = 'Auto Cancellation';
				$notify_comments = sprintf(EMAIL_TEXT_COMMENTS_UPDATE, $order_comment) . "\n";
				
				// Update from Pending to Canceled
        		// Issue back the store credit used if any
        		$edit_order_obj = new edit_order('system', 'system', $oID);
        		$sc_result_array = $edit_order_obj->manage_order_sc('1', '5', $messageStack);
        		
        		for ($sc_cnt=0; $sc_cnt < count($sc_result_array); $sc_cnt++) {
        			$credit_activity_array[$oID][] = sprintf('SC - %s:', $sc_result_array[$sc_cnt]['sc_type']) . ' ' . $currencies->format($sc_result_array[$sc_cnt]['sc_amount'], true, $expired_order_row['currency'], $expired_order_row['currency_value']) . '('.$sc_result_array[$sc_cnt]['sc_trans_id'].')';
        			
        			$order_comment .= "\n" . sprintf('SC - %s:', $sc_result_array[$sc_cnt]['sc_type']) . ' ' . $currencies->format($sc_result_array[$sc_cnt]['sc_amount'], true, $expired_order_row['currency'], $expired_order_row['currency_value']) . '('.$sc_result_array[$sc_cnt]['sc_trans_id'].')';
        		}
        		
        		if (count($sc_result_array)) {
					$notify_comments .= 'We have noticed that you have placed an order using store credit, but did not select to pay for the outstanding balance on the order. We have cancelled your Pending order and re-issued the Store Credits back to your OffGamers Account.' . 
										"\n\n" . 'If you have the available store credits, once you check out the order will be paid in full, otherwise it is suggested that you pay the outstanding balance of the order with our other selectable payment method options.' . 
										"\n\n";
					$order_comment .= 	"\n\n" . 'We have noticed that you have placed an order using store credit, but did not select to pay for the outstanding balance on the order. We have cancelled your Pending order and re-issued the Store Credits back to your OffGamers Account.' . 
										"\n\n" . 'If you have the available store credits, once you check out the order will be paid in full, otherwise it is suggested that you pay the outstanding balance of the order with our other selectable payment method options.';
				}
				
        		$orders_status_update_sql_data = array(	'orders_status' => 5,
      					 								'last_modified' => 'now()',
      					 								'orders_locked_by' => 'NULL',
      					 								'orders_locked_from_ip' => 'NULL',
      					 								'orders_locked_datetime' => 'NULL'
      					 								);
      			tep_db_perform(TABLE_ORDERS, $orders_status_update_sql_data, 'update', "orders_id = '" . tep_db_input($oID) . "'");
      			
      			$orders_status_history_sql_data = array(	'orders_id' => $oID,
															'orders_status_id' => 5,
															'date_added' => 'now()',
															'customer_notified' => 1,
															'comments' => $order_comment,
															'comments_type' => '0',
															'changed_by' => 'system'
															 );
				tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_sql_data);
				
				
				tep_cron_email_notification($expired_order_row['customers_id'], $expired_order_row, $pending_status, $cancel_status, $notify_comments);		// Customer Notification
			}
		}
	}
	
	foreach ($credit_activity_array as $order_id => $info) {
		echo 'Order ID: ' . $order_id . '<br>';
		echo implode('<br>', $info);
		echo '<br><br>';
	}
}

function tep_cron_email_notification($cust_id, $order_info, $from_status, $to_status, $order_comment) {
	$customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname 
									FROM " . TABLE_CUSTOMERS . " 
									WHERE customers_id = '" . tep_db_input($cust_id) . "'";
	$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
	if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
		$email_firstname = $customer_profile_row["customers_firstname"];
		$email_lastname = $customer_profile_row["customers_lastname"];
	} else {
		$email_firstname = $order_info['customers_name'];
		$email_lastname = $order_info['customers_name'];
	}
	
	$email_greeting = tep_get_email_greeting($email_firstname, $email_lastname, $customer_profile_row["customers_gender"]);
	
	$email = $email_greeting . EMAIL_TEXT_STATUS_UPDATE_TITLE . EMAIL_TEXT_ORDER_NUMBER . ' ' . $order_info['orders_id'] . "\n"
			. EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($order_info['date_purchased']) . "\n" . EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $order_info['orders_id'], 'SSL') . "\n\n" ;
	
	$email .= sprintf(EMAIL_TEXT_UPDATED_STATUS, $from_status . ' -> ' . $to_status) . 
			  $order_comment . EMAIL_TEXT_CLOSING . "\n\n" . EMAIL_FOOTER;
	@tep_mail($order_info['customers_name'], $order_info['customers_email_address'], implode(' ', array(EMAIL_SUBJECT_PREFIX, sprintf(EMAIL_TEXT_SUBJECT, $order_info['orders_id']))), $email, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}
?>