<?
/*
	$Id: version_2_12_4.php,v 1.3 2007/10/11 05:03:20 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$site_customers_access_table_exists = in_array('site_customers_access', $DBTables) ? true : false;

// Change field structure for set_function in configuration table
$change_field_structure = array();
$change_field_structure['configuration'] = array (array (	"field_name" => "set_function",
															"field_attr" => " text "
											 			)
													);
change_field_structure ($change_field_structure);
// End of change field structure for set_function in configuration table

// Create webmoney and site_customers_access tables
$add_new_tables = array();

$add_new_tables["webmoney"] = array (	"structure" => "CREATE TABLE `webmoney` (
														  	webmoney_payment_no int(11) unsigned NOT NULL default '0',
														  	webmoney_operative_mode tinyint(1) NOT NULL default '0',
														  	webmoney_payment_amount decimal(15,4) NOT NULL default '0.0000',
															webmoney_payee_purse varchar(13) NOT NULL default '',
															webmoney_payer_wm varchar(20) NOT NULL default '',
															webmoney_payer_purse varchar(13) NOT NULL default '',
															webmoney_invoices_no varchar(15) NOT NULL default '',
															webmoney_transaction_no varchar(15) NOT NULL default '',
															webmoney_payment_timestamp varchar(32) NOT NULL default '',
															webmoney_hash varchar(32) NOT NULL default '',
													  		PRIMARY KEY (`webmoney_payment_no`),
													  		KEY `index_payer_wm` (`webmoney_payer_wm`)
														) TYPE=MyISAM COMMENT='WebMoney Transaction History Table';" ,
										"data" => ""
								);

$add_new_tables["site_customers_access"] = array (	"structure" => "CREATE TABLE `site_customers_access` (
																		`site_id` INT(2) NOT NULL,
																		`customers_groups_id` INT(11) NOT NULL,
																		`admin_groups_id` TEXT NOT NULL,
																		PRIMARY KEY (`site_id`, `customers_groups_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create webmoney and site_customers_access tables

// Insert new records into configuration table (for Website Buyback live help)
$conf_insert_sql = array();

$conf_insert_sql["LIVE_HELP_OGM_BUYBACK_IMPLEMENTATION"] = array("insert" => " ('Live Help Website Buyback Implementation', 'LIVE_HELP_OGM_BUYBACK_IMPLEMENTATION', '', 'This live help is used for website buyback', 1, 34, NULL, now(), NULL, 'tep_cfg_textarea(')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Website Buyback live help)

// Insert new records into admin_files_actions table (for permission on editing Product Pirce, Qty and Purchase Mode)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='categories.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CATALOG_EDIT_PRICE"] = array("insert" => " ('CATALOG_EDIT_PRICE', 'Edit Price', ".$row_sql["admin_files_id"].", '1,23,27,31,35,36,37,38,39,40,41,45', 47)" );
	$admin_files_actions_insert_sql["CATALOG_EDIT_QUANTITY"] = array("insert" => " ('CATALOG_EDIT_QUANTITY', 'Edit Quantity', ".$row_sql["admin_files_id"].", '1,23,27,31,35,36,37,38,39,40,41,45', 48)" );
	$admin_files_actions_insert_sql["CATALOG_EDIT_PURCHASE_MODE"] = array("insert" => " ('CATALOG_EDIT_PURCHASE_MODE', 'Edit Purchase Mode', ".$row_sql["admin_files_id"].", '1,23,27,31,35,36,37,38,39,40,41,45', 49)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on editing Product Pirce, Qty and Purchase Mode)


if (!$site_customers_access_table_exists) {
	$site_code_select_sql = "	SELECT site_id, admin_groups_id 
								FROM site_code ";
	$site_code_result_sql = tep_db_query($site_code_select_sql);
	while ($site_code_row = tep_db_fetch_array($site_code_result_sql)) {
		if ($site_code_row['site_id'] > 0) {
			$site_customers_access_data_array = array(	'site_id' => $site_code_row['site_id'],
														'customers_groups_id' => 0,
						            		            'admin_groups_id' => $site_code_row['admin_groups_id']
						                       		);
			tep_db_perform('site_customers_access', $site_customers_access_data_array);
		} else {
			$cust_grp_select_sql = "SELECT customers_groups_id 
									FROM customers_groups ";
			$cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
			
			while ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
				$site_customers_access_data_array = array(	'site_id' => $site_code_row['site_id'],
															'customers_groups_id' => $cust_grp_row['customers_groups_id'],
							            		            'admin_groups_id' => $site_code_row['admin_groups_id']
							                       		);
				tep_db_perform('site_customers_access', $site_customers_access_data_array);
			}
		}
	}
}

?>