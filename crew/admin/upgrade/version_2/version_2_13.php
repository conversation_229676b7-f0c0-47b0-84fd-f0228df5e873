<?
/*
	$Id: version_2_13.php,v 1.1 2007/10/18 03:24:15 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_currencies_fields = get_table_fields("currencies");

// Insert new fields into currencies table
$add_new_field = array();

$add_new_field['currencies'] = array (	array (	"field_name" => "buy_value",
												"field_attr" => " decimal(13,8) default NULL ",
												"add_after" => "value"
												),
										array (	"field_name" => "buy_value_adjust",
												"field_attr" => " decimal(7,2) NOT NULL default '0.0000' ",
												"add_after" => "buy_value"
												),
										array (	"field_name" => "sell_value",
												"field_attr" => " decimal(13,8) default NULL ",
												"add_after" => "buy_value_adjust"
												),
										array (	"field_name" => "sell_value_adjust",
												"field_attr" => " decimal(7,2) NOT NULL default '0.0000' ",
												"add_after" => "sell_value"
												)
									);

add_field($add_new_field);
// End of insert new fields into currencies table

// Update records in currencies table (define buy_value and sell_value)
if (!in_array('buy_value', $existing_currencies_fields)) {
	$currencies_update_sql = array();
	
	$currencies_update_sql["USD"] = array("update" => " buy_value='1.00000000' " );
	$currencies_update_sql["CNY"] = array("update" => " buy_value=value " );
	$currencies_update_sql["EUR"] = array("update" => " buy_value=0.95*value, buy_value_adjust='5.00' " );
	$currencies_update_sql["MYR"] = array("update" => " buy_value=0.95*value, buy_value_adjust='5.00' " );
	$currencies_update_sql["GBP"] = array("update" => " buy_value=0.95*value, buy_value_adjust='5.00' " );
	
	update_records("currencies", "code", $currencies_update_sql, $DBTables);
}

if (!in_array('sell_value', $existing_currencies_fields)) {
	$currencies_update_sql = array();
	
	$currencies_update_sql["USD"] = array("update" => " sell_value='1.00000000' " );
	$currencies_update_sql["CNY"] = array("update" => " sell_value=value " );
	$currencies_update_sql["EUR"] = array("update" => " sell_value=1.05*value, sell_value_adjust='5.00' " );
	$currencies_update_sql["MYR"] = array("update" => " sell_value=1.05*value, sell_value_adjust='5.00' " );
	$currencies_update_sql["GBP"] = array("update" => " sell_value=1.05*value, sell_value_adjust='5.00' " );
	
	update_records("currencies", "code", $currencies_update_sql, $DBTables);
}
// End of update records in currencies table (define buy_value and sell_value)
?>