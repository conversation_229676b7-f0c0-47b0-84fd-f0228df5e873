<?
/*
	$Id: version_2_13_1.php,v 1.1 2007/10/22 04:10:44 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Update records in orders_products table (Set orders_products_purchase_eta from day unit to hour unit)
$purchase_eta_update_sql = array();

$purchase_eta_update_sql['orders_products'] = array(	array(	"field_name" => "orders_products_purchase_eta",
																"update" => " orders_products_purchase_eta = orders_products_purchase_eta * 24 ",
																"where_str" => " orders_products_purchase_eta IS NOT NULL AND orders_products_purchase_eta > 0 "
															)
													 );

advance_update_records($purchase_eta_update_sql, $DBTables);
// End of update records in orders_products table (Set orders_products_purchase_eta from day unit to hour unit)
?>