<?
/*
	$Id: version_2_13_2.php,v 1.2 2007/11/09 04:05:31 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Change field structure for currency_value in related tables
$change_field_structure = array();

$change_field_structure['buyback_request_group'] = array (array (	"field_name" => "currency_value",
																	"field_attr" => " decimal(14,8) NOT NULL default '1.00000000' "
											 					)
														);

$change_field_structure['orders'] = array (array (	"field_name" => "currency_value",
													"field_attr" => " decimal(14,8) default NULL "
							 					)
										);

$change_field_structure['orders_compensate_products'] = array (	array (	"field_name" => "compensate_entered_currency_value",
																		"field_attr" => " decimal(14,8) NOT NULL default '1.00000000' "
												 					),
												 				array (	"field_name" => "compensate_order_currency_value",
																		"field_attr" => " decimal(14,8) NOT NULL default '1.00000000' "
												 					)
															);

$change_field_structure['store_payments'] = array (array (	"field_name" => "store_payments_paid_currency_value",
															"field_attr" => " decimal(14,8) default NULL "
									 					)
												);

$change_field_structure['supplier_cp_payments'] = array (array (	"field_name" => "currency_value",
																	"field_attr" => " decimal(14,8) default NULL "
											 					)
														);

$change_field_structure['supplier_order_lists'] = array (array (	"field_name" => "currency_value",
																	"field_attr" => " decimal(14,8) default NULL "
											 					)
														);

$change_field_structure['supplier_payments'] = array (array (	"field_name" => "currency_value",
																"field_attr" => " decimal(14,8) default NULL "
										 					)
													);

$change_field_structure['wd_pa_transactions'] = array (array (	"field_name" => "currency_value",
																"field_attr" => " decimal(14,8) default NULL "
										 					)
													);

change_field_structure ($change_field_structure);
// End of change field structure for currency_value in related tables

// Define supplier_tasks_status as index key in supplier_tasks_allocation table
add_index_key ('supplier_tasks_allocation', 'index_supplier_tasks_status', 'index', 'supplier_tasks_status', $DBTables);
// End of define supplier_tasks_status as index key in supplier_tasks_allocation table

// Define info_verification_type as index key in customers_info_verification table
add_index_key ('customers_info_verification', 'index_verification_type', 'index', 'info_verification_type', $DBTables);
// End of define info_verification_type as index key in customers_info_verification table

// Define remote_addr as index key in orders table
add_index_key ('orders', 'index_remote_addr', 'index', 'remote_addr', $DBTables);
// End of define remote_addr as index key in orders table
?>