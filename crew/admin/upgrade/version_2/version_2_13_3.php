<?
/*
	$Id: version_2_13_3.php,v 1.2 2007/11/09 10:29:36 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_latest_news_fields = get_table_fields("latest_news");

// Insert new fields into latest_news table
$add_new_field = array();

$add_new_field['latest_news'] = array (	array (	"field_name" => "custom_products_type",
												"field_attr" => " varchar(255) NOT NULL default '' ",
												"add_after" => "content"
												)
									);

add_field($add_new_field);
// End of insert new fields into latest_news table

// Update records in latest_news table (default all offgamers.com news to currency type)
if (!in_array('custom_products_type', $existing_latest_news_fields)) {
	$news_update_sql = "UPDATE latest_news 
						SET custom_products_type = '0' 
						WHERE FIND_IN_SET('0', news_display_sites)";
	tep_db_query($news_update_sql);
}
// End of update records in latest_news table (default all offgamers.com news to currency type)
?>