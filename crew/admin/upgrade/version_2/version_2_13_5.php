<?
/*
	$Id: version_2_13_5.php,v 1.1 2007/11/16 02:07:21 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Delete records in admin_files_actions table
$admin_actions_delete_sql["BUYBACK_STATUS_PENDING_2_PROCESSING"] = array( "unique" => "1" );

delete_records("admin_files_actions", " admin_files_actions_key", $admin_actions_delete_sql, $DBTables);
// End of delete records in admin_files_actions table

// Insert new records into admin_files_actions table (for permission on viewing Rstk Char)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='buyback_requests_info.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["BUYBACK_ORDER_VIEW_RSTK_CHAR"] = array("insert" => " ('BUYBACK_ORDER_VIEW_RSTK_CHAR', 'View restock character', ".$row_sql["admin_files_id"].", '1,20,22,23,27,28,29,31,32,35,36,37,38,39,40,41,42,45,46,50,53', 45)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing Rstk Char)
?>