<?
/*
	$Id: version_2_14_1.php,v 1.2 2007/11/27 07:51:24 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$set_default_delivery_options = in_array('categories_setting', $DBTables) ? false : true;

// Create categories_setting table
$add_new_tables = array();

$add_new_tables["categories_setting"] = array (	"structure" => "CREATE TABLE `categories_setting` (
																 	categories_id int(11) NOT NULL default '0',
																  	categories_setting_key varchar(64) NOT NULL default '',
																  	categories_setting_value varchar(255) NOT NULL default '',
																  	PRIMARY KEY (`categories_id`, `categories_setting_key`),
															  		KEY `index_categories_id` (`categories_id`)
																) TYPE=MyISAM;" ,
												"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create categories_setting table

// Define buyback_status_id as index key in buyback_request_group table
add_index_key ('buyback_request_group', 'index_buyback_status_id', 'index', 'buyback_status_id', $DBTables);
// End of define buyback_status_id as index key in buyback_request_group table

if ($set_default_delivery_options) {
	$category_select_sql = "SELECT categories_id 
							FROM categories 
							WHERE categories_buyback_main_cat = 1 ";
	$category_result_sql = tep_db_query($category_select_sql);
	while ($category_row = tep_db_fetch_array($category_result_sql)) {
		$cat_setting_sql_data = array(	'categories_id' => $category_row['categories_id'],
                    	 				'categories_setting_key' => 'cs_delivery_option',
                    	 				'categories_setting_value' => '1'
                    	 				);
		tep_db_perform('categories_setting', $cat_setting_sql_data);
	}
}
?>