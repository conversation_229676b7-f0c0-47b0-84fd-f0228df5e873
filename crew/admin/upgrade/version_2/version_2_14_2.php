<?
/*
	$Id: version_2_14_2.php,v 1.1 2007/12/12 07:34:37 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_categories_fields = get_table_fields("categories");

// Insert new fields into categories table
$add_new_field = array();

$add_new_field['categories'] = array (	array (	"field_name" => "categories_parent_path",
												"field_attr" => " varchar(255) NOT NULL default '' ",
												"add_after" => "parent_id"
												)
									);

add_field($add_new_field);
// End of insert new fields into categories table

if (!in_array('categories_parent_path', $existing_categories_fields)) {
	$root_category_select_sql = "	SELECT categories_id
									FROM categories
									WHERE parent_id = '0'";
	$root_category_result_sql = tep_db_query($root_category_select_sql);
    while($root_category_row = tep_db_fetch_array($root_category_result_sql)) {
    	upgrade_tep_update_parent_path($root_category_row['categories_id'], '_');
    }
}

function upgrade_tep_update_parent_path($parent_id, $path) {
	$subcategory_select_sql = "	SELECT categories_id
								FROM categories
								WHERE parent_id = '".tep_db_input($parent_id)."'";
	$subcategory_result_sql = tep_db_query($subcategory_select_sql);
    while($subcategory_row = tep_db_fetch_array($subcategory_result_sql)) {
    	$new_path = $path . $parent_id . '_';
    	
    	$path_update_sql = "UPDATE categories
    						SET categories_parent_path = '".tep_db_input($new_path)."'
    						WHERE categories_id='".tep_db_input($subcategory_row['categories_id'])."'";
    	tep_db_query($path_update_sql);
    	upgrade_tep_update_parent_path($subcategory_row['categories_id'], $new_path);
    }
}
?>