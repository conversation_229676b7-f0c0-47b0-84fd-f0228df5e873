<?
/*
	$Id: version_2_14_3.php,v 1.1 2007/12/13 08:16:34 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Change field structure for products_name in related tables
$change_field_structure = array();

$change_field_structure['orders_products'] = array (array (	"field_name" => "products_name",
															"field_attr" => " varchar(255) NOT NULL default '' "
									 					)
												);

$change_field_structure['products_description'] = array (array (	"field_name" => "products_name",
																	"field_attr" => " varchar(255) NOT NULL default '' "
											 					)
														);

$change_field_structure['supplier_order_lists_products'] = array (array (	"field_name" => "products_name",
																			"field_attr" => " varchar(255) NOT NULL default '' "
													 					)
																);

change_field_structure ($change_field_structure);
// End of change field structure for products_name in related tables
?>