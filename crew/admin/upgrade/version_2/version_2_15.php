<?
/*
	$Id: version_2_15.php,v 1.4 2008/01/02 10:18:05 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into buyback_request and customers tables
$add_new_field = array();

$add_new_field['buyback_request'] = array( 	array ( "field_name" => "buyback_request_customer_org_code",
													"field_attr" => " varchar(10) NOT NULL default '0'",
													"add_after" => ""
													),
											array ( "field_name" => "buyback_request_supplier_code",
													"field_attr" => " varchar(10) NOT NULL default '0'",
													"add_after" => ""
													),
											array ( "field_name" => "buyback_request_customer_matching_code",
													"field_attr" => " varchar(10) NOT NULL default '0'",
													"add_after" => ""
													),
											array ( "field_name" => "buyback_request_screenshot_before_name",
													"field_attr" => " varchar(32) NOT NULL default ''",
													"add_after" => ""
													),
											array ( "field_name" => "buyback_request_screenshot_after_name",
													"field_attr" => " varchar(32) NOT NULL default ''",
													"add_after" => ""
													),
											array ( "field_name" => "orders_id",
													"field_attr" => " int(11) NOT NULL default '0'",
													"add_after" => ""
													),
											array ( "field_name" => "orders_products_id",
													"field_attr" => " int(11) NOT NULL default '0'",
													"add_after" => ""
													),
											array ( "field_name" => "customers_rating",
													"field_attr" => " tinyint(1) NOT NULL default '0'",
													"add_after" => ""
													)
										);

$add_new_field['buyback_request_group'] = array(	array ( "field_name" => "buyback_request_order_type",
															"field_attr" => " tinyint(1) NOT NULL default '0'",
															"add_after" => ""
													)
											);

$add_new_field['customers'] = array( 	array ( "field_name" => "customers_yahoo",
												"field_attr" => " varchar(96) NOT NULL default ''",
												"add_after" => "customers_qq"
												),
										array ( "field_name" => "customers_icq",
												"field_attr" => " varchar(20) NOT NULL default ''",
												"add_after" => "customers_yahoo"
												)
										);

add_field ($add_new_field, false);
// End of insert new field into buyback_request and customers tables

// Create VIP Module tables
$add_new_tables = array();

$add_new_tables["vip_region_group"] = array (	"structure" => "CREATE TABLE `vip_region_group` (
																	`vip_region_group_id` int(11) NOT NULL auto_increment,
																	`vip_region_group_name` varchar(128) NOT NULL default '',
																	`countries_ids` varchar(255) NOT NULL default '',
																	`vip_region_group_sort_order` int(11) NOT NULL default '0',
																	PRIMARY KEY (`vip_region_group_id`)
																) TYPE=MyISAM ;",
												"data" => "INSERT INTO `vip_region_group` (`vip_region_group_id`, `vip_region_group_name`, `countries_ids`, `vip_region_group_sort_order`) 
															VALUES 	(1, 'CNBB', '44', 5000);"
											);

$add_new_tables["vip_supplier_groups"] = array ("structure" => "CREATE TABLE `vip_supplier_groups` (
																`vip_supplier_groups_id` int(11) NOT NULL auto_increment,
																`language_id` int(11) NOT NULL default '1',
																`vip_supplier_groups_name` varchar(255) NOT NULL default '',
																`vip_supplier_groups_rank` int(11) NOT NULL default '50000',
																PRIMARY KEY (`vip_supplier_groups_id`)
															) TYPE=MyISAM ;",
												"data" => "	INSERT INTO `vip_supplier_groups` (`vip_supplier_groups_id`, `language_id`, `vip_supplier_groups_name`, `vip_supplier_groups_rank`) 
															VALUES 	(1, '1', 'Member', '5000'), 
																	(2, '1', 'VIP', '500'),
																	(3, '1', 'VVIP', '50')
																	;"
												);

$add_new_tables["vip_rank"] = array (	"structure" => "CREATE TABLE `vip_rank` (
															`vip_rank_id` int(11)  NOT NULL auto_increment,
															`vip_region_group_id` int(11)  NOT NULL default '0',
															`vip_rank_name` varchar(255) NOT NULL default '',
															`vip_rank_sort_order` int(11)  NOT NULL default '50000',
															`vip_rank_percentage` decimal(6,2) unsigned NOT NULL default '0.00',
															`vip_rank_upgrade_point` decimal(10,2)  NOT NULL default '0.00',
															`vip_rank_downgrade_point` decimal(10,2)  NOT NULL default '0.00',
															`vip_rank_action` varchar(255) NOT NULL default '',
															PRIMARY KEY (`vip_rank_id`)
														) TYPE=MyISAM ;",
										"data" => "	INSERT INTO `vip_rank` (`vip_rank_id`, `vip_region_group_id`, `vip_rank_name`, `vip_rank_sort_order`, `vip_rank_percentage`, `vip_rank_upgrade_point`, `vip_rank_downgrade_point`, `vip_rank_action`) 
													VALUES 	(1, 1, 'Grade A', 1, 70.00, 2000.00, 999.00, 'SYSTEM_VIP_GROUPS_STATUS_2'),
														(2, 1, 'Grade B', 5, 70.00, 1000.00, 0.00, 'SYSTEM_VIP_GROUPS_STATUS_2');"
									);

$add_new_tables["customers_vip"] = array 	(	"structure" => 	"CREATE TABLE `customers_vip` (
																	`customers_id` int(11) NOT NULL default '0',
																	`vip_supplier_groups_id` int(11) NOT NULL default '1',
																	`vip_rank_id` int(11) NOT NULL default '0',
																	`vip_buyback_cummulative_point` decimal(10,2) NOT NULL default '0.00',
																	PRIMARY KEY (`customers_id`)
																) TYPE=MyISAM ;",
												"data" => ""
											);


$add_new_tables["vip_supplier_setting"] = array 	(	"structure" => 	"CREATE TABLE `vip_supplier_setting` (
																			`customers_id` int(11)  NOT NULL default '0',
																			`categories_id` int(11)  NOT NULL default '0',
																			`vip_supplier_setting_key` varchar(255) NOT NULL default '',
  																			`vip_supplier_setting_value` varchar(255) NOT NULL default '',
  																			KEY `index_customers_id` (`customers_id`),
																			PRIMARY KEY (`customers_id`, `categories_id`)
																		) TYPE=MyISAM ;",
														"data" => ""
													);

$add_new_tables["vip_supplier_inventory"] = array 	(	"structure" => 	"CREATE TABLE `vip_supplier_inventory` (
																			`customers_id` int(11)  NOT NULL default '0',
																			`products_id` int(11)  NOT NULL default '0',
																			`vip_supplier_inventory_qty` int(11)  NOT NULL default '0',
																			`vip_supplier_inventory_min_qty` int(11)  NOT NULL default '0',
																			KEY `index_customers_id` (`customers_id`),
																			PRIMARY KEY (`customers_id`, `products_id`)
																		) TYPE=MyISAM ;",
														"data" => ""
													);

$add_new_tables["vip_supplier_inventory_logs"] = array 	(	"structure" => 	"CREATE TABLE `vip_supplier_inventory_logs` (
																				`vip_supplier_inventory_logs_id` int(11)  NOT NULL auto_increment,
																				`customers_id` int(11)  NOT NULL default '0',
																				`vip_supplier_inventory_logs_time` datetime  NOT NULL default '0000-00-00 00:00:00',
																				`vip_supplier_inventory_logs_ip` varchar(20) NOT NULL default '***************',
																				`vip_supplier_inventory_logs_product_id` int(11)  NOT NULL default '0',
																				`log_from_value` varchar(255) NOT NULL default '',
																				`log_to_value` varchar(255) NOT NULL default '',
																				PRIMARY KEY (`vip_supplier_inventory_logs_id`, `customers_id`)
																			) TYPE=MyISAM ;",
															"data" => ""
														);

$add_new_tables["buyback_order_info"] = array 	(	"structure" => 	"CREATE TABLE `buyback_order_info` (
																		`buyback_request_group_id` int(11)  NOT NULL default '0',
																		`buyback_order_info_customer_firstname` varchar(32) NOT NULL default '',
																		`buyback_order_info_customer_lastname` varchar(32) NOT NULL default '',
																		`buyback_order_info_customer_telephone` varchar(32) NOT NULL default '',
																		`buyback_order_info_customer_email` varchar(96) NOT NULL default '',
																		`buyback_order_info_customer_dob` datetime,
																		`buyback_order_info_customer_gender` char(1) NOT NULL default '',
																		`buyback_order_info_customer_address` varchar(64) NOT NULL default '',
																		`buyback_order_info_customer_city` varchar(32) NOT NULL default '',
																		`buyback_order_info_customer_state` varchar(32) NOT NULL default '',
																		`buyback_order_info_customer_mobile` varchar(32) NOT NULL default '',
																		`buyback_order_info_customer_msn` varchar(96),
																		`buyback_order_info_customer_yahoo` varchar(96),
																		`buyback_order_info_customer_qq` varchar(20) NOT NULL default '',
																		`buyback_order_info_customer_icq` varchar(20),
																		PRIMARY KEY (`buyback_request_group_id`)
																	) TYPE=MyISAM ;",
														"data" => ""
												);

$add_new_tables["vip_order_allocation"] = array 	(	"structure" => 	"CREATE TABLE `vip_order_allocation` (
																			`orders_products_id` int(11) NOT NULL default '0',
																			`customers_id` int(11) NOT NULL default '0',
																			`products_id` int(11) NOT NULL default '0',
																			`vip_order_allocation_quantity` int(2) NOT NULL default '0',
																			`vip_order_allocation_time` datetime NOT NULL default '0000-00-00 00:00:00',
																			PRIMARY KEY (`orders_products_id`, `customers_id`)
																		) TYPE=MyISAM ;",
														"data" => ""
													);

$add_new_tables["vip_rules"] = array 	(	"structure" => 	"CREATE TABLE `vip_rules` (
																`vip_rules_key` varchar(64) NOT NULL default '',
																`vip_rules_title` varchar(128) NOT NULL default '',
																`vip_rules_operator` char(1) NOT NULL default '',
																`vip_rules_value` decimal(10,2) NOT NULL default '0.00',
																PRIMARY KEY (`vip_rules_key`)
															) TYPE=MyISAM ;",
											"data" => "INSERT INTO `vip_rules` (`vip_rules_key`, `vip_rules_title`, `vip_rules_operator`, `vip_rules_value`) 
														VALUES 	('VIP_DEDUCT_SUPPLIER_REJECT', 'Supplier Reject Orders Allocated', '-', '0'), 
																('VIP_DEDUCT_ORDER_EXPIRATION_CANCELLATION', 'VIP Orders Expiration Cancellation', '-', '0'),
																('VIP_DEDUCT_ORDER_CANCEL_AFTER_ACCEPTED', 'VIP Orders Cancel After Accepted', '-', '0'),
																('VIP_DEDUCT_ORDER_EXPIRATION_CANCELLATION_AFTER_ACCEPTED', 'VIP Orders Expiration Cancellation After Accepted', '-', '0'),
																('VIP_DEDUCT_SUPPLIER_PARTIAL_COMPLETE_ORDER', 'Supplier Partial Complete Order', '-', '0'),
																('VIP_ADD_SUPPLIER_ORDER_FULLY_COMPLETED', 'VIP Orders Fully Completed', '+', '0')
																;"
										);

$add_new_tables["vip_production_history"] = array 	(	"structure" => 	"CREATE TABLE `vip_production_history` (
																			`customers_id` int(11) NOT NULL default '0',
																			`products_id` int(11) NOT NULL default '0',
																			`vip_production_history_date` date NOT NULL default '0000-00-00',
																			`vip_production_history_qty` int(11) NOT NULL default '0',
																			`unit_selling_price` decimal(15,6) NOT NULL default '0.000000',
																			PRIMARY KEY (`customers_id`, `products_id`, `vip_production_history_date`)
																		) TYPE=MyISAM ;",
														"data" => ""
													);
													
$add_new_tables["vip_stock_history"] = array 	(	"structure" => 	"CREATE TABLE `vip_stock_history` (
																		`customers_id` int(11) NOT NULL default '0',
																		`products_id` int(11) NOT NULL default '0',
																		`vip_stock_history_date` date NOT NULL default '0000-00-00',
																		`vip_stock_history_qty` int(11) NOT NULL default '0',
																		PRIMARY KEY (`customers_id`, `products_id`, `vip_stock_history_date`)
																	) TYPE=MyISAM ;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create VIP Module tables

// Insert new records into admin_files table (for VIP module)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);

if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["vip_suppliers_ranking.php"] = array(	"insert" => " ('vip_suppliers_ranking.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
																);
													
	$admin_files_insert_sql["vip_rules.php"] = array(	"insert" => " ('vip_rules.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   								);
					   								
	$admin_files_insert_sql["vip_inventory_report.php"] = array("insert" => " ('vip_inventory_report.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   									);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", "");
}

// Insert new records into admin_files_categories table
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='vip_inventory_report.php' AND admin_files_is_boxes=0" ;
				
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_categories_insert_sql = array();
	
	$admin_files_categories_insert_sql[$row_sql["admin_files_id"]] = array("insert" => " (".$row_sql["admin_files_id"].", '1', 0)" );
	insert_new_records(TABLE_ADMIN_FILES_CATEGORIES, "admin_files_id", $admin_files_categories_insert_sql, $DBTables, "(`admin_files_id`, `admin_groups_id`, `categories_ids`)", " admin_files_id='".$row_sql["admin_files_id"]."' AND admin_groups_id=1 ");
}
// End of insert new records into admin_files_categories table

// Insert new records into admin_files_actions table (for permission on viewing screenshot)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='buyback_requests_info.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["BUYBACK_ORDER_VIEW_SCREENSHOT"] = array("insert" => " ('BUYBACK_ORDER_VIEW_SCREENSHOT', 'View uploaded screenshots', ".$row_sql["admin_files_id"].", '1', 60)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing screenshot)

// Define game_char_history_id as index key in char_skill_history table
add_index_key ('char_skill_history', 'index_game_char_history_id', 'index', 'game_char_history_id', $DBTables);
// End of define game_char_history_id as index key in char_skill_history table

// Define categories_id as index key in categories_configuration table
add_index_key ('categories_configuration', 'index_categories_id', 'index', 'categories_id', $DBTables);
// End of define categories_id as index key in categories_configuration table

// Define admin_files_name as index key in admin_files table
add_index_key ('admin_files', 'index_admin_files_name', 'index', 'admin_files_name', $DBTables);
// End of define admin_files_name as index key in admin_files table

// Define news_groups_id and status as index key in latest_news table
add_index_key ('latest_news', 'index_grp_id_and_status', 'index', 'news_groups_id, status', $DBTables);
// End of define news_groups_id and status as index key in latest_news table

// Define store_refund_trans_id as index key in store_refund table
add_index_key ('store_refund', 'index_refund_trans_id', 'index', 'store_refund_trans_id', $DBTables);
// End of define store_refund_trans_id as index key in store_refund table

// Define store_refund_status as index key in store_refund table
add_index_key ('store_refund', 'index_refund_status', 'index', 'store_refund_status', $DBTables);
// End of define store_refund_status as index key in store_refund table

// Define customers_id as index key in buyback_request_group table
add_index_key ('buyback_request_group', 'index_customers_id', 'index', 'customers_id', $DBTables);
// End of define customers_id as index key in buyback_request_group table

// Define categories_id as index key in themes_to_categories table
add_index_key ('themes_to_categories', 'index_categories_id', 'index', 'categories_id', $DBTables);
// End of define categories_id as index key in themes_to_categories table

// Define remote_addr as index key in buyback_request_group table
add_index_key ('buyback_request_group', 'index_remote_addr', 'index', 'remote_addr', $DBTables);
// End of define remote_addr as index key in buyback_request_group table
?>