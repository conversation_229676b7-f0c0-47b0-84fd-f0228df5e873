<?
/*
	$Id: version_2_15_1.php,v 1.3 2008/01/03 12:35:54 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create polling tables
$add_new_tables = array();

$add_new_tables["polls_questions"] = array (	"structure" => "CREATE TABLE `polls_questions` (
																 	polls_questions_id int(11) NOT NULL auto_increment,
																  	polls_questions varchar(255) NOT NULL default '',
																  	polls_questions_start_date datetime NOT NULL default '0000-00-00 00:00:00',
																  	polls_questions_end_date datetime NOT NULL default '0000-00-00 00:00:00',
																  	polls_questions_created_date datetime NOT NULL default '0000-00-00 00:00:00',
																  	polls_questions_last_modified datetime NOT NULL default '0000-00-00 00:00:00',
																  	polls_questions_status tinyint(1) NOT NULL default '0',
																  	polls_questions_added_by varchar(255) NOT NULL default '',
																  	polls_questions_custom_products_type varchar(255) NOT NULL default '',
																  	polls_questions_allow_anonymous tinyint(1) NOT NULL default '0',
																  	PRIMARY KEY (`polls_questions_id`),
															  		<PERSON>EY `index_polls_questions_start_date` (`polls_questions_start_date`)
																) TYPE=MyISAM;",
												"data" => ""
											);

$add_new_tables["polls_questions_options"] = array (	"structure" => "CREATE TABLE `polls_questions_options` (
																		 	polls_questions_options_id int(11) NOT NULL auto_increment,
																		  	polls_questions_id int(11) NOT NULL default '0',
																		  	polls_questions_options_value varchar(255) NOT NULL default '',
																		  	PRIMARY KEY (`polls_questions_options_id`),
																	  		KEY `index_polls_questions_id` (`polls_questions_id`)
																		) TYPE=MyISAM;",
														"data" => ""
													);

$add_new_tables["polls_questions_answers"] = array (	"structure" => "CREATE TABLE `polls_questions_answers` (
																			polls_questions_answers_id int(11) NOT NULL auto_increment,
																		 	customers_id int(11) NOT NULL default '0',
																		 	polls_questions_id int(11) NOT NULL default '0',
																		  	polls_questions_options_id int(11) NOT NULL default '0',
																		  	polls_questions_answers_date datetime NOT NULL default '0000-00-00 00:00:00',
																		  	PRIMARY KEY  (`polls_questions_answers_id`),
  																		    KEY `index_polls_cust_id_and_questions_id` (`customers_id`, `polls_questions_id`)
																		) TYPE=MyISAM;",
														"data" => ""
													);

$add_new_tables["polls_comments"] = array (	"structure" => "CREATE TABLE `polls_comments` (
															  `polls_comments_id` int(11) NOT NULL auto_increment,
															  `polls_comments` varchar(255) NOT NULL default '',
															  `polls_comments_date` datetime NOT NULL default '0000-00-00 00:00:00',
															  `polls_questions_id` int(11) NOT NULL default '0',
															  `customers_id` varchar(255) NOT NULL default '0',
															  PRIMARY KEY  (`polls_comments_id`)
															) TYPE=MyISAM;",
											"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create polling tables

// Insert new records into admin_files table (for polling page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='latest_news.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["polling.php"] = array(	"insert" => " ('polling.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
													"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='polling.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for polling page)

// Insert new records into configuration table (for customer age limit)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Customer Options'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["ENTRY_AGE_MAX"] = array("insert" => " ('Age', 'ENTRY_AGE_MAX', '110', 'Maximum age', '".$row_sql["configuration_group_id"]."', 25, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for customer age limit)
?>