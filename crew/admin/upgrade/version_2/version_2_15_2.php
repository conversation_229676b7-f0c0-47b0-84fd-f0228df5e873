<?
/*
	$Id: version_2_15_2.php,v 1.3 2008/01/14 11:26:43 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create Menu Management tables
$add_new_tables = array();

$add_new_tables["cms_menu"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `cms_menu` (
														  `cms_menu_id` int(11) NOT NULL auto_increment,
														  `cms_menu_type` tinyint(1) NOT NULL default '1',
														  `cms_menu_content_type` varchar(10) default NULL,
														  `cms_menu_sort_order` int(11) default 50000,
														  `cms_menu_status` tinyint(1) NOT NULL default '0',
														  `cms_menu_url` varchar(255) default NULL,
														  `cms_menu_seo_alias` varchar(255) default NULL,
														  PRIMARY KEY (`cms_menu_id`)
														) TYPE=MyISAM;",
										"data" => "INSERT INTO `cms_menu` (`cms_menu_id`, `cms_menu_type`, `cms_menu_content_type`, `cms_menu_sort_order`, `cms_menu_status`, `cms_menu_url`, `cms_menu_seo_alias`) VALUES
														(1, 1, 'url', 1, 1, 'http://www.offgamers.com/', NULL),
														(2, 1, 'content', 6, 0, NULL, ''),
														(3, 1, 'url', 2, 1, '/buyback.php', NULL),
														(4, 1, 'url', 3, 1, 'http://affiliate.offgamers.com/index.php', NULL),
														(5, 1, 'url', 4, 1, '/login.php', NULL),
														(6, 2, 'url', 1, 1, 'wowgold-eu-realm-i-320.ogm', NULL),
														(7, 1, 'content', 5, 1, 'http://www.offgamers.com/help_center.php', 'contact-us'),
														(8, 1, 'url', 3, 0, 'http://carerra-w.dpodium.com/world-of-warcraft-us-c-16.ogm?tpl=0', NULL),
														(9, 2, 'url', 2, 1, '/wowgold-us-realm-i-319.ogm', ''),
														(50, 2, 'url', 4, 1, '/lineage2-adena-sea-us-i-329.ogm', NULL),
														(51, 2, 'url', 5, 1, '/eve-online-c-1660.ogm', NULL),
														(49, 2, 'url', 3, 1, '/silkroad-gold-sro-i-321.ogm', NULL),
														(52, 2, 'url', 6, 1, '/ffxi-gil-i-328.ogm', NULL),
														(60, 2, 'url', 14, 1, '/archlord-c-1877.ogm', NULL),
														(53, 2, 'url', 7, 1, '/rf-online-currency-rfo-server-i-350.ogm', NULL),
														(54, 2, 'url', 8, 1, '/lotro-gold-i-327.ogm', NULL),
														(55, 2, 'url', 9, 1, '/anarchy-online-c-1802.ogm', NULL),
														(56, 2, 'url', 10, 1, '/cabal-online-c-1964.ogm', NULL),
														(57, 2, 'url', 11, 1, '/maple-story-mesos-servers-i-349.ogm', NULL),
														(58, 2, 'url', 12, 1, '/guild-wars-c-1401.ogm', NULL),
														(59, 2, 'url', 13, 1, '/eq2-gold-platinum-everquest2-i-348.ogm', NULL),
														(64, 2, 'url', 18, 1, '/city-of-villains-infamy-cov-i-343.ogm', NULL),
														(65, 2, 'url', 19, 1, '/hellgate-london-c-1982.ogm', NULL),
														(66, 2, 'url', 20, 1, '/maple-story-mesos-servers-i-349.ogm', NULL),
														(61, 2, 'url', 15, 1, '/2moons-c-2843.ogm', NULL),
														(62, 2, 'url', 16, 1, '/dofus-kamas-servers-i-346.ogm', NULL),
														(63, 2, 'url', 17, 1, '/hero-online-c-1834.ogm', NULL);"
									);

$add_new_tables["cms_menu_lang_setting"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `cms_menu_lang_setting` (
																		  `cms_menu_id` int(11) NOT NULL default '0',
																		  `languages_id` int(11) NOT NULL default '0',
																		  `cms_menu_lang_setting_key` varchar(100) NOT NULL default '',
																		  `cms_menu_lang_setting_key_value` text,
																		  PRIMARY KEY  (`cms_menu_id`, `languages_id`, `cms_menu_lang_setting_key`)
																		) TYPE=MyISAM;",
													"data" => "INSERT INTO `cms_menu_lang_setting` (`cms_menu_id`, `languages_id`, `cms_menu_lang_setting_key`, `cms_menu_lang_setting_key_value`) VALUES
																(1, 2, 'menu_content', ''),
																(1, 1, 'menu_title', 'Buy'),
																(1, 1, 'menu_content', ''),
																(1, 2, 'menu_title', ''),
																(2, 1, 'menu_content', 'Testing'),
																(2, 2, 'menu_content', ''),
																(2, 1, 'menu_submenu_script', ''),
																(2, 1, 'menu_title', 'Community'),
																(3, 2, 'menu_content', ''),
																(3, 1, 'menu_title', 'Trade'),
																(3, 1, 'menu_content', ''),
																(3, 2, 'menu_title', ''),
																(4, 2, 'menu_content', ''),
																(4, 1, 'menu_title', 'Affiliate'),
																(4, 1, 'menu_content', ''),
																(4, 2, 'menu_title', ''),
																(5, 2, 'menu_content', ''),
																(5, 1, 'menu_title', 'My Account'),
																(5, 1, 'menu_content', ''),
																(5, 2, 'menu_title', ''),
																(6, 2, 'menu_content', ''),
																(6, 1, 'menu_title', 'WOW GOLD EU'),
																(6, 1, 'menu_content', ''),
																(6, 2, 'menu_title', ''),
																(7, 2, 'menu_content', ''),
																(7, 1, 'menu_title', 'Contact Us'),
																(7, 2, 'menu_title', ''),
																(7, 1, 'menu_content', ''),
																(2, 2, 'menu_submenu_script', ''),
																(2, 2, 'menu_title', ''),
																(8, 2, 'menu_content', ''),
																(8, 1, 'menu_title', 'About OffGamers'),
																(8, 1, 'menu_content', ''),
																(8, 2, 'menu_title', ''),
																(8, 1, 'menu_submenu_script', ''),
																(8, 2, 'menu_submenu_script', ''),
																(9, 2, 'menu_content', ''),
																(9, 1, 'menu_title', 'WOW GOLD US'),
																(9, 2, 'menu_title', ''),
																(9, 1, 'menu_content', '<p><span style=\"font-size: large;\"><strong><font face=\"comic sans ms,sans-serif\">Buy Lineage II Gold</font></strong></span></p>'),
																(4, 2, 'menu_submenu_script', ''),
																(1, 1, 'menu_submenu_script', ''),
																(4, 1, 'menu_submenu_script', ''),
																(1, 2, 'menu_submenu_script', ''),
																(61, 1, 'menu_title', '2Moons Dil'),
																(61, 2, 'menu_title', ''),
																(61, 1, 'menu_content', ''),
																(61, 2, 'menu_content', ''),
																(61, 1, 'menu_submenu_script', ''),
																(61, 2, 'menu_submenu_script', ''),
																(62, 1, 'menu_title', 'Dofus Kamas'),
																(62, 2, 'menu_title', ''),
																(62, 1, 'menu_content', ''),
																(62, 2, 'menu_content', ''),
																(62, 1, 'menu_submenu_script', ''),
																(62, 2, 'menu_submenu_script', ''),
																(63, 1, 'menu_title', 'Hero Gold'),
																(63, 2, 'menu_title', ''),
																(63, 1, 'menu_content', ''),
																(63, 2, 'menu_content', ''),
																(63, 1, 'menu_submenu_script', ''),
																(63, 2, 'menu_submenu_script', ''),
																(64, 1, 'menu_submenu_script', ''),
																(64, 1, 'menu_title', 'COV Infamy'),
																(64, 1, 'menu_content', ''),
																(64, 2, 'menu_content', ''),
																(64, 2, 'menu_title', ''),
																(65, 1, 'menu_title', 'HGL Palladium'),
																(65, 2, 'menu_title', ''),
																(65, 1, 'menu_content', ''),
																(65, 2, 'menu_content', ''),
																(65, 1, 'menu_submenu_script', ''),
																(65, 2, 'menu_submenu_script', ''),
																(64, 2, 'menu_submenu_script', ''),
																(66, 1, 'menu_title', 'Maple Story Mesos'),
																(66, 2, 'menu_title', ''),
																(66, 1, 'menu_content', ''),
																(66, 2, 'menu_content', ''),
																(66, 1, 'menu_submenu_script', ''),
																(66, 2, 'menu_submenu_script', ''),
																(9, 2, 'menu_submenu_script', ''),
																(60, 2, 'menu_submenu_script', ''),
																(49, 1, 'menu_title', 'SRO Gold'),
																(49, 2, 'menu_title', ''),
																(49, 1, 'menu_content', ''),
																(49, 2, 'menu_content', ''),
																(49, 1, 'menu_submenu_script', ''),
																(49, 2, 'menu_submenu_script', ''),
																(50, 1, 'menu_title', 'L2 Adena'),
																(50, 2, 'menu_title', ''),
																(50, 1, 'menu_content', ''),
																(50, 2, 'menu_content', ''),
																(50, 1, 'menu_submenu_script', ''),
																(50, 2, 'menu_submenu_script', ''),
																(51, 1, 'menu_title', 'Eve Isk'),
																(51, 2, 'menu_title', ''),
																(51, 1, 'menu_content', ''),
																(51, 2, 'menu_content', ''),
																(51, 1, 'menu_submenu_script', ''),
																(51, 2, 'menu_submenu_script', ''),
																(52, 1, 'menu_title', 'FFXI Gil'),
																(52, 2, 'menu_title', ''),
																(52, 1, 'menu_content', ''),
																(52, 2, 'menu_content', ''),
																(52, 1, 'menu_submenu_script', ''),
																(52, 2, 'menu_submenu_script', ''),
																(53, 1, 'menu_title', 'RFO CP / Dalant / Disena'),
																(53, 2, 'menu_title', ''),
																(53, 1, 'menu_content', ''),
																(53, 2, 'menu_content', ''),
																(53, 1, 'menu_submenu_script', ''),
																(53, 2, 'menu_submenu_script', ''),
																(54, 1, 'menu_title', 'LOTRO Gold'),
																(54, 2, 'menu_title', ''),
																(54, 1, 'menu_content', ''),
																(54, 2, 'menu_content', ''),
																(54, 1, 'menu_submenu_script', ''),
																(54, 2, 'menu_submenu_script', ''),
																(55, 1, 'menu_title', 'AO Credits'),
																(55, 2, 'menu_title', ''),
																(55, 1, 'menu_content', ''),
																(55, 2, 'menu_content', ''),
																(55, 1, 'menu_submenu_script', ''),
																(55, 2, 'menu_submenu_script', ''),
																(60, 1, 'menu_submenu_script', ''),
																(56, 1, 'menu_title', 'Cabal Alz'),
																(60, 1, 'menu_content', ''),
																(60, 2, 'menu_content', ''),
																(56, 2, 'menu_title', ''),
																(56, 1, 'menu_content', ''),
																(56, 2, 'menu_content', ''),
																(56, 1, 'menu_submenu_script', ''),
																(56, 2, 'menu_submenu_script', ''),
																(57, 1, 'menu_title', 'Maple Story Mesos'),
																(57, 2, 'menu_title', ''),
																(57, 1, 'menu_content', ''),
																(57, 2, 'menu_content', ''),
																(57, 1, 'menu_submenu_script', ''),
																(57, 2, 'menu_submenu_script', ''),
																(60, 2, 'menu_title', ''),
																(58, 1, 'menu_title', 'GW Gold Plat'),
																(59, 2, 'menu_submenu_script', ''),
																(60, 1, 'menu_title', 'ArchLord Gold'),
																(58, 2, 'menu_title', ''),
																(58, 1, 'menu_content', ''),
																(58, 2, 'menu_content', ''),
																(58, 1, 'menu_submenu_script', ''),
																(58, 2, 'menu_submenu_script', ''),
																(59, 1, 'menu_title', 'EQ2 Gold Plat'),
																(59, 2, 'menu_title', ''),
																(59, 1, 'menu_content', ''),
																(59, 2, 'menu_content', ''),
																(59, 1, 'menu_submenu_script', ''),
																(6, 2, 'menu_submenu_script', ''),
																(9, 1, 'menu_submenu_script', ''),
																(3, 1, 'menu_submenu_script', ''),
																(3, 2, 'menu_submenu_script', ''),
																(7, 1, 'menu_submenu_script', ''),
																(7, 2, 'menu_submenu_script', ''),
																(6, 1, 'menu_submenu_script', ''),
																(5, 1, 'menu_submenu_script', ''),
																(5, 2, 'menu_submenu_script', '');
"
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create Menu Management tables

// Insert new records into admin_files table (for Menu Management page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='infolinks.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["menu_management.php"] = array(	"insert" => " ('menu_management.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
							   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='menu_management.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Menu Management page)

// Update records in admin_files table
$admin_files_name_array = array('vip_inventory_report.php');

$admin_files_update_sql = array();
$admin_files_update_sql["admin_files"] = array(	array(	"field_name" => "admin_files_cat_setting",
														"update" => " admin_files_cat_setting='1' ",
														"where_str" => " admin_files_is_boxes=0 and admin_files_name IN ('" . implode("', '", $admin_files_name_array) . "') "
													)
											);

advance_update_records($admin_files_update_sql, $DBTables);
// End of update records in admin_files table

// Delete records in define_mainpage table (remove help center page)
$define_mainpage_delete_sql = array();

$define_mainpage_delete_sql["2"] = array();

delete_records("define_mainpage", " Id", $define_mainpage_delete_sql, $DBTables);
// End of delete records in define_mainpage table (remove help center page)

// Define orders_products_id as index key in supplier_tasks_allocation_history table
add_index_key ('supplier_tasks_allocation_history', 'index_orders_products_id', 'index', 'orders_products_id', $DBTables);
// End of define orders_products_id as index key in supplier_tasks_allocation_history table

// Define payer_id as index key in paypal table
add_index_key ('paypal', 'index_payer_id', 'index', 'payer_id', $DBTables);
// End of define payer_id as index key in paypal table

// Define user_role and user_id as index key in store_payment_account_book table
add_index_key ('store_payment_account_book', 'index_role_and_user_id', 'index', 'user_role, user_id', $DBTables);
// End of define user_role and user_id as index key in store_payment_account_book table

// Define categories_types_groups_id and custom_products_type_id as index key in categories_types_sets table
add_index_key ('categories_types_sets', 'index_group_id_and_prod_type', 'index', 'categories_types_groups_id, custom_products_type_id', $DBTables);
// End of define categories_types_groups_id and custom_products_type_id as index key in categories_types_sets table

// Define supplier_id as index key in supplier_crew table
add_index_key ('supplier_crew', 'index_supplier_id', 'index', 'supplier_id', $DBTables);
// End of define supplier_id as index key in supplier_crew table

// Define brackets_dependent as index key in brackets table
add_index_key ('brackets', 'index_brackets_dependent', 'index', 'brackets_dependent', $DBTables);
// End of define brackets_dependent as index key in brackets table

// Define data_pool_level_id and brackets_key as index key in brackets table
add_index_key ('brackets', 'index_level_id_and_brackets_key', 'index', 'data_pool_level_id, brackets_key', $DBTables);
// End of define data_pool_level_id and brackets_key as index key in brackets table

// Define categories_id and groups_id as index key in categories_groups table
add_index_key ('categories_groups', 'index_cat_id_and_groups_id', 'index', 'categories_id, groups_id', $DBTables);
// End of define categories_id and groups_id as index key in categories_groups table

// Define categories_types_parent_id as index key in categories_types table
add_index_key ('categories_types', 'index_cat_types_parent', 'index', 'categories_types_parent_id', $DBTables);
// End of define categories_types_parent_id as index key in categories_types table

// Define customers_id as index key in customers_basket table
add_index_key ('customers_basket', 'index_customers_id', 'index', 'customers_id', $DBTables);
// End of define customers_id as index key in customers_basket table

// Define status as index key in latest_news table
add_index_key ('latest_news', 'index_status', 'index', 'status', $DBTables);
// End of define status as index key in latest_news table

// Define status as index key in customers table
add_index_key ('customers', 'index_customers_email_address', 'index', 'customers_email_address', $DBTables);
// End of define status as index key in customers table

// Define supplier_email_address as index key in supplier table
add_index_key ('supplier', 'index_supplier_email_address', 'index', 'supplier_email_address', $DBTables);
// End of define supplier_email_address as index key in supplier table

// Define data_pool_level_parent_id and products_id as index key in data_pool_level table
add_index_key ('data_pool_level', 'index_parent_id_and_products_id', 'index', 'data_pool_level_parent_id, products_id', $DBTables);
// End of define data_pool_level_parent_id and products_id as index key in data_pool_level table

// Define payment_method as index key in orders table
add_index_key ('orders', 'index_payment_method', 'index', 'payment_method', $DBTables);
// End of define payment_method as index key in orders table

// Define customers_id as index key in customers_remarks_history table
add_index_key ('customers_remarks_history', 'index_customers_id', 'index', 'customers_id', $DBTables);
// End of define customers_id as index key in customers_remarks_history table

// Define orders_id as index key in buyback_request table
add_index_key ('buyback_request', 'index_orders_id', 'index', 'orders_id', $DBTables);
// End of define orders_id as index key in buyback_request table

// Define categories_id as index key in restock_character_sets_to_categories table
add_index_key ('restock_character_sets_to_categories', 'index_categories_id', 'index', 'categories_id', $DBTables);
// End of define categories_id as index key in restock_character_sets_to_categories table

// Define credit_card_owner as index key in payment_extra_info table
add_index_key ('payment_extra_info', 'index_credit_card_owner', 'index', 'credit_card_owner', $DBTables);
// End of define credit_card_owner as index key in payment_extra_info table

// Define infolinks_id as index key in infolinks_contents table
add_index_key ('infolinks_contents', 'index_infolinks_id', 'index', 'infolinks_id', $DBTables);
// End of define infolinks_id as index key in infolinks_contents table

// Define data_pool_options_values_eta as index key in data_pool_options_values table
add_index_key ('data_pool_options_values', 'index_eta', 'index', 'data_pool_options_values_eta', $DBTables);
// End of define data_pool_options_values_eta as index key in data_pool_options_values table

// Define products_id as index key in user_favourite_products table
add_index_key ('user_favourite_products', 'index_products_id', 'index', 'products_id', $DBTables);
// End of define products_id as index key in user_favourite_products table

// Define subproduct_id as index key in products_bundles table
add_index_key ('products_bundles', 'index_subproduct_id', 'index', 'subproduct_id', $DBTables);
// End of define subproduct_id as index key in products_bundles table

// Define coupon_code as index key in coupons table
add_index_key ('coupons', 'index_coupon_code', 'index', 'coupon_code', $DBTables);
// End of define coupon_code as index key in coupons table

// Define admin_files_id as index key in admin_files_actions table
add_index_key ('admin_files_actions', 'index_admin_files_id', 'index', 'admin_files_id', $DBTables);
// End of define admin_files_id as index key in admin_files_actions table

// Define categories_buyback_main_cat as index key in categories table
add_index_key ('categories', 'index_buyback_main_cat', 'index', 'categories_buyback_main_cat', $DBTables);
// End of define categories_buyback_main_cat as index key in categories table

// Define suppliers_id as index key in supplier_tasks_allocation table
add_index_key ('supplier_tasks_allocation', 'index_suppliers_id', 'index', 'suppliers_id', $DBTables);
// End of define suppliers_id as index key in supplier_tasks_allocation table
?>