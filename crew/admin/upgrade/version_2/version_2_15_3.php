<?
/*
	$Id: version_2_15_3.php,v 1.2 2008/01/21 03:30:06 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create cms_menu_tab_pages table
$add_new_tables = array();

$add_new_tables["cms_menu_tab_pages"] = array (	"structure" => "CREATE TABLE `cms_menu_tab_pages` (
																 	cms_menu_id int(11) NOT NULL default '0',
																  	cms_linked_filename varchar(64) NOT NULL default '',
																  	PRIMARY KEY (`cms_menu_id`, `cms_linked_filename`)
																) TYPE=MyISAM;",
												"data" => "INSERT INTO `cms_menu_tab_pages` (`cms_menu_id`, `cms_linked_filename`) 
																VALUES (1, 'custom_product_info.php'),
																	(1, 'index.php'),
																	(1, 'product_info.php'),
																	(3, 'account_buyback.php'),
																	(3, 'buyback.php'),
																	(3, 'my_favourite_links.php'),
																	(3, 'my_order_history.php'),
																	(5, 'account.php'),
																	(5, 'account_activate.php'),
																	(5, 'account_edit.php'),
																	(5, 'account_history.php'),
																	(5, 'account_history_info.php'),
																	(5, 'account_payment_edit.php'),
																	(5, 'address_book.php');"
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create cms_menu_tab_pages table

// Insert new fields into orders_log_table and cms_menu tables
$add_new_field = array();

$add_new_field['cms_menu'] = array (	array (	"field_name" => "cms_menu_right_navigation",
												"field_attr" => " tinyint(1) NOT NULL default '1' ",
												"add_after" => ""
												)
									);

add_field($add_new_field);
// End of insert new fields into orders_log_table and cms_menu tables

// Define game_char_log_orders_id as index key in game_char_log table
add_index_key ('game_char_log', 'index_orders_id', 'index', 'game_char_log_orders_id', $DBTables);
// End of define game_char_log_orders_id as index key in game_char_log table

// Define categories_status as index key in categories table
add_index_key ('categories', 'index_categories_status', 'index', 'categories_status', $DBTables);
// End of define categories_status as index key in categories table

// Define game_char_history_id as index key in char_honor_history table
add_index_key ('char_honor_history', 'index_history_id', 'index', 'game_char_history_id', $DBTables);
// End of define game_char_history_id as index key in char_honor_history table

// Define game_char_history_id as index key in char_reputation_history table
add_index_key ('char_reputation_history', 'index_history_id', 'index', 'game_char_history_id', $DBTables);
// End of define game_char_history_id as index key in char_reputation_history table

// Define game_char_id as index key in char_pet table
add_index_key ('char_pet', 'index_char_id', 'index', 'game_char_id', $DBTables);
// End of define game_char_id as index key in char_pet table

// Drop existing index key (index_products_id) for user_favourite_products table
drop_index_key ("user_favourite_products", 'index_products_id', 'index', $DBTables, '');
// End of drop existing index key (index_products_id) for user_favourite_products table

// Define user_id and products_id as primary key in user_favourite_products table
add_index_key ('user_favourite_products', 'primary', 'primary', 'user_id, products_id', $DBTables);
// End of define user_id and products_id as primary key in user_favourite_products table

// Define serial_number as primary key in customers_info_verification table
add_index_key ('customers_info_verification', 'index_serial_number', 'index', 'serial_number', $DBTables);
// End of define serial_number as primary key in customers_info_verification table
?>