<?
/*
	$Id: version_2_15_4.php,v 1.1 2008/01/28 05:27:30 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_admin_groups_fields = get_table_fields("admin_groups");

// Insert new fields into admin_groups tables
$add_new_field = array();

$add_new_field['admin_groups'] = array (	array (	"field_name" => "admin_groups_authorized",
													"field_attr" => " varchar(255) NOT NULL default '' ",
													"add_after" => ""
													)
										);

add_field($add_new_field);
// End of insert new fields into admin_groups tables

// Insert new records into admin_files_actions table (for permission on editing admin member and group)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='admin_members.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ADMIN_MANAGE_MEMBERS"] = array("insert" => " ('ADMIN_MANAGE_MEMBERS', 'Manage Admin Members', ".$row_sql["admin_files_id"].", '1', 10)" );
	$admin_files_actions_insert_sql["ADMIN_MANAGE_GROUPS"] = array("insert" => " ('ADMIN_MANAGE_GROUPS', 'Manage Admin Groups', ".$row_sql["admin_files_id"].", '1', 20)" );
	$admin_files_actions_insert_sql["ADMIN_GROUP_TRANSFER"] = array("insert" => " ('ADMIN_GROUP_TRANSFER', 'Assign Admin Members to Admin Groups', ".$row_sql["admin_files_id"].", '1', 30)" );
	$admin_files_actions_insert_sql["ADMIN_EDIT_GROUP_PERMISSION"] = array("insert" => " ('ADMIN_EDIT_GROUP_PERMISSION', 'Edit Group Permission', ".$row_sql["admin_files_id"].", '1', 40)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on editing admin member and group)

// Insert new records into admin_files table (for customer order activity page and csv converter page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='sales.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["customers_order_activities.php"] = array(	"insert" => " ('customers_order_activities.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																		"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
										   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='customers_order_activities.php' AND admin_files_is_boxes=0 ");
}

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["csv_manager.php"] = array(	"insert" => " ('csv_manager.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
						   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='csv_manager.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for customer order activity page)

if (!in_array('admin_groups_authorized', $existing_admin_groups_fields)) {
	$all_groups_id_sql = "	SELECT admin_groups_id 
							FROM " . TABLE_ADMIN_GROUPS . " 
							ORDER BY admin_groups_id";
	$all_groups_id_result = tep_db_query($all_groups_id_sql);
	$add_all_groups_id = '';
	while ($all_groups_id_row = tep_db_fetch_array($all_groups_id_result)) {
		if ($add_all_groups_id != '') {
			$add_all_groups_id .= ",";
		}
		$add_all_groups_id .= "'" . $all_groups_id_row['admin_groups_id'] . "'";
	}
	$add_all_groups_id_files = str_replace("'", "", $add_all_groups_id);
	$top_admin_authorise_all_update_sql = "	UPDATE " . TABLE_ADMIN_GROUPS . " 
											SET admin_groups_authorized = '".$add_all_groups_id_files."' 
											WHERE admin_groups_id = '1'";
	tep_db_query($top_admin_authorise_all_update_sql);
}

// Define store_payments_methods_id as index key in store_payments table
add_index_key ('store_payments', 'index_payments_methods_id', 'index', 'store_payments_methods_id', $DBTables);
// End of define store_payments_methods_id as index key in store_payments table

// Define payment_methods_send_status as index key in payment_methods table
add_index_key ('payment_methods', 'index_send_status', 'index', 'payment_methods_send_status', $DBTables);
// End of define payment_methods_send_status as index key in payment_methods table

// Define mb_payer_email as index key in moneybookers table
add_index_key ('moneybookers', 'index_payer_email', 'index', 'mb_payer_email', $DBTables);
// End of define mb_payer_email as index key in moneybookers table

// Define cms_linked_filename as index key in cms_menu_tab_pages table
add_index_key ('cms_menu_tab_pages', 'index_linked_filename', 'index', 'cms_linked_filename', $DBTables);
// End of define cms_linked_filename as index key in cms_menu_tab_pages table

// Define supplier_id and supplier_crew_mac_address as index key in supplier_crew_mac_address table
add_index_key ('supplier_crew_mac_address', 'index_supplier_id_and_mac_address', 'index', 'supplier_id, supplier_crew_mac_address', $DBTables);
// End of define supplier_id and supplier_crew_mac_address as index key in supplier_crew_mac_address table

// Define categories_id as index key in buyback_categories table
add_index_key ('buyback_categories', 'index_categories_id', 'index', 'categories_id', $DBTables);
// End of define categories_id as index key in buyback_categories table
?>