<?
/*
	$Id: version_2_15_5.php,v 1.1 2008/02/04 03:54:56 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_buyback_request_group_fields = get_table_fields("buyback_request_group");

// Insert new fields into buyback_request_group tables
$add_new_field = array();

$add_new_field['buyback_request_group'] = array (	array (	"field_name" => "buyback_request_group_served",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => "show_restock"
															)
												);

add_field($add_new_field);
// End of insert new fields into buyback_request_group tables

if (!in_array('buyback_request_group_served', $existing_buyback_request_group_fields)) {
	// Change the ofp_deal_on_game on vip to new key vip_deal_on_game for VIP Order
	$deal_key_select_sql = "	SELECT br.buyback_request_id 
								FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
								INNER JOIN " . TABLE_BUYBACK_REQUEST ." AS br
									ON(brg.buyback_request_group_id = br.buyback_request_group_id)
								WHERE brg.buyback_request_order_type = '1'
									AND br.buyback_dealing_type='ofp_deal_on_game'";
	$deal_key_select_result = tep_db_query($deal_key_select_sql);
	
	while ($deal_key_select_row = tep_db_fetch_array($deal_key_select_result)) {	// if found existing record
		$deal_key_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST . "
	    						SET buyback_dealing_type= 'vip_deal_on_game'
	    						WHERE buyback_request_id = '".tep_db_input($deal_key_select_row['buyback_request_id'])."'";
	    tep_db_query($deal_key_update_sql);
	}
	// End of change the ofp_deal_on_game on vip to new key vip_deal_on_game for VIP Order
	
	// Reset all served buyback order
	$buyback_list_select_sql = "SELECT br.buyback_request_id, brg.buyback_request_group_id, brg.buyback_request_order_type 
								FROM " . TABLE_BUYBACK_REQUEST_GROUP ." AS brg
								INNER JOIN " . TABLE_BUYBACK_REQUEST ." AS br
									ON(brg.buyback_request_group_id=br.buyback_request_group_id)
								WHERE br.buyback_dealing_type=''";
	$buyback_list_select_result = tep_db_query($buyback_list_select_sql);
	
	while ($buyback_list_select_row = tep_db_fetch_array($buyback_list_select_result)) {
		$buyback_dealing_type = '';
		if ($buyback_list_select_row['buyback_request_order_type'] == '0') { //normal buyback
			$buyback_dealing_type = 'ofp_deal_on_game';
		} else if ($buyback_list_select_row['buyback_request_order_type'] == '1') { // vip order
			$buyback_dealing_type = 'vip_deal_on_game';
		}
		
		$buyback_list_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST ." 
									SET buyback_dealing_type = '" . $buyback_dealing_type . "'
									WHERE buyback_request_id = '" . $buyback_list_select_row['buyback_request_id'] . "'";
		tep_db_query($buyback_list_update_sql);
		
		$queue_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . "
							 SET buyback_request_group_served = '1' 
							 WHERE buyback_request_group_id='" . $buyback_list_select_row['buyback_request_group_id'] . "'";
		tep_db_query($queue_update_sql);
	}
	//End of reset all served buyback order
}
?>