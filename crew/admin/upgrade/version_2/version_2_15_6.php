<?
/*
	$Id: version_2_15_6.php,v 1.1 2008/02/19 12:30:38 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create locking table
$add_new_tables = array();

$add_new_tables["locking"] = array (	"structure" => "CREATE TABLE `locking` (
															`locking_trans_id` int(11) NOT NULL ,
															`locking_table_name` varchar(255) NOT NULL ,
															`locking_by` int(11) NULL,
															`locking_from_ip` varchar(20) NULL,
															`locking_datetime` datetime NULL,
															PRIMARY KEY (`locking_trans_id`, `locking_table_name`)
														) TYPE=MYISAM;",
										"data" => ""
									);

add_new_tables ($add_new_tables, $DBTables);
// End of create locking table

// Change field structure for orders_log_admin_id in orders_log_table tables
$change_field_structure = array();

$change_field_structure['orders_log_table'] = array (array ("field_name" => "orders_log_admin_id",
															"field_attr" => " varchar(255) NOT NULL default '0' "
									 					)
												);

change_field_structure ($change_field_structure);
// End of change field structure for orders_log_admin_id in orders_log_table tables

// Insert new fields into orders_log_table and cms_menu tables
$add_new_field = array();

$add_new_field['orders_log_table'] = array (	array (	"field_name" => "orders_log_filename",
														"field_attr" => " varchar(255) NOT NULL default 'orders.php' ",
														"add_after" => ""
														)
											);

add_field($add_new_field);
// End of insert new fields into orders_log_table and cms_menu tables

// Insert new records into configuration table (for Encrypted Website Payments (EWP))
$conf_insert_sql = array();

$conf_insert_sql["MODULE_PAYMENT_PAYPAL_EWP"] = array("insert" => " ('Encrypted Website Payments (EWP)', 'MODULE_PAYMENT_PAYPAL_EWP', 'No', 'Encrypt your PayPal checkout buttons dynamically when rendering your webpages to prevent and/or detect tampering with your buttons.', 6, 230, NULL, now(), NULL, 'tep_cfg_select_option(array(\'Yes\', \'No\'), ')" );
$conf_insert_sql["MODULE_PAYMENT_PAYPAL_EWP_CERT_ID"] = array("insert" => " ('Encrypted Website Payments Certificate ID', 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '', 'Certificate ID for the PayPal Public Certificate used in EWP.', 6, 235, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Encrypted Website Payments (EWP))

// Define cart_comments_type and cart_comments_status as index key in cart_comments table
add_index_key ('cart_comments', 'index_type_and_status', 'index', 'cart_comments_type, cart_comments_status', $DBTables);
// End of define cart_comments_type and cart_comments_status as index key in cart_comments table

// Define data_pool_options_id as index key in data_pool_options_values table
add_index_key ('data_pool_options_values', 'index_data_pool_options_id', 'index', 'data_pool_options_id', $DBTables);
// End of define data_pool_options_id as index key in data_pool_options_values table

// Define data_pool_level_tags_id and brackets_tags_key as index key in brackets_tags table
add_index_key ('brackets_tags', 'index_level_tags_id_and_tags_key', 'index', 'data_pool_level_tags_id, brackets_tags_key', $DBTables);
// End of define data_pool_level_tags_id and brackets_tags_key as index key in brackets_tags table

// Define data_pool_template_id as index key in data_pool_template_to_categories table
add_index_key ('data_pool_template_to_categories', 'index_data_pool_template_id', 'index', 'data_pool_template_id', $DBTables);
// End of define data_pool_template_id as index key in data_pool_template_to_categories table

// Define products_id as index key in data_pool_options table
add_index_key ('data_pool_options', 'index_products_id', 'index', 'products_id', $DBTables);
// End of define products_id as index key in data_pool_options table

// Define products_id as index key in data_pool_level table
add_index_key ('data_pool_level', 'index_products_id', 'index', 'products_id', $DBTables);
// End of define products_id as index key in data_pool_level table

// Define data_pool_options_id as index key in data_pool_options_values_tags table
add_index_key ('data_pool_options_values_tags', 'index_data_pool_options_id', 'index', 'data_pool_options_id', $DBTables);
// End of define data_pool_options_id as index key in data_pool_options_values_tags table

// Define paypal_ipn_id as index key in paypal_payment_status_history table
add_index_key ('paypal_payment_status_history', 'index_paypal_ipn_id', 'index', 'paypal_ipn_id', $DBTables);
// End of define paypal_ipn_id as index key in paypal_payment_status_history table

// Define products_id as index key in vip_supplier_inventory table
add_index_key ('vip_supplier_inventory', 'index_products_id', 'index', 'products_id', $DBTables);
// End of define products_id as index key in vip_supplier_inventory table

// Define zone_country_id as index key in zones table
add_index_key ('zones', 'index_zone_country_id', 'index', 'zone_country_id', $DBTables);
// End of define zone_country_id as index key in zones table

// Define orderid as index key in wd_pa_transactions table
add_index_key ('wd_pa_transactions', 'index_orderid', 'index', 'orderid', $DBTables);
// End of define orderid as index key in wd_pa_transactions table

// Define orders_id as index key in orders_session_info table
add_index_key ('orders_session_info', 'index_orders_id', 'index', 'orders_id', $DBTables);
// End of define orders_id as index key in orders_session_info table

// Define configuration_key as index key in configuration table
add_index_key ('configuration', 'index_configuration_key', 'index', 'configuration_key', $DBTables);
// End of define configuration_key as index key in configuration table

// Define infolinks_groups_id as index key in infolinks table
add_index_key ('infolinks', 'index_infolinks_groups_id', 'index', 'infolinks_groups_id', $DBTables);
// End of define infolinks_groups_id as index key in infolinks table

// Define game_char_history_id as index key in char_quest_history table
add_index_key ('char_quest_history', 'index_game_char_history_id', 'index', 'game_char_history_id', $DBTables);
// End of define game_char_history_id as index key in char_quest_history table

// Define buyback_competitor_status_bracket_set_id as index key in buyback_competitor_status_bracket table
add_index_key ('buyback_competitor_status_bracket', 'index_set_id', 'index', 'buyback_competitor_status_bracket_set_id', $DBTables);
// End of define buyback_competitor_status_bracket_set_id as index key in buyback_competitor_status_bracket table

// Define language_id as index key in supplier_tasks_status table
add_index_key ('supplier_tasks_status', 'index_language_id', 'index', 'language_id', $DBTables);
// End of define language_id as index key in supplier_tasks_status table

// Define mb_trans_id as index key in moneybookers_payment_status_history table
add_index_key ('moneybookers_payment_status_history', 'index_mb_trans_id', 'index', 'mb_trans_id', $DBTables);
// End of define mb_trans_id as index key in moneybookers_payment_status_history table
?>