<?
/*
	$Id: version_2_15_7.php,v 1.2 2008/02/25 05:08:41 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create locking table
$add_new_tables = array();

$add_new_tables["system_log"] = array (	"structure" => "CREATE TABLE `system_log` (
														  `system_log_id` int(11) NOT NULL auto_increment,
														  `system_log_ip` varchar(15) NOT NULL default '',
														  `system_log_time` datetime NOT NULL default '0000-00-00 00:00:00',
														  `system_log_action` varchar(255) NOT NULL default '',
														  `system_log_admin_id` varchar(255) NOT NULL default '',
														  `table_name` varchar(255) NOT NULL default '',
														  `entry_id` varchar(255) NOT NULL default '',
														  `field_name` varchar(255) default NULL,
														  `from_value` varchar(255) default NULL,
														  `to_value` varchar(255) default NULL,
														  PRIMARY KEY  (`system_log_id`)
														) TYPE=MYISAM;",
										"data" => ""
									);

add_new_tables ($add_new_tables, $DBTables);
// End of create locking table

// Insert new records into admin_files table (for system log page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='administrator.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["system_log.php"] = array(	"insert" => " ('system_log.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
						   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='system_log.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for system log page)

// Insert new records into configuration table (for Inactive admin member alert)
$conf_insert_sql = array();

$conf_insert_sql["MAX_INACTIVE_ADMIN_LOGIN_DAYS"] = array("insert" => " ('Inactive admin member alert notification period', 'MAX_INACTIVE_ADMIN_LOGIN_DAYS', '7', 'Alert notification contains list of admin members who have not log in for more than this period (days)', 3, 200, NULL, now(), NULL, NULL)" );
$conf_insert_sql["STORE_INACTIVE_ADMIN_ALERT_EMAIL_ADDRESS"] = array("insert" => " ('Inactive Admin Member Alert Email Address', 'STORE_INACTIVE_ADMIN_ALERT_EMAIL_ADDRESS', '', 'Email address to which the inactive admin member email will be send to whenever admin member does not log in for more than configured days.(In \"Name <Email>\" format. Use \',\' as delimiter for multiple recipient)', 1, 17, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Inactive admin member alert)

// Insert new fields into countries tables
$add_new_field = array();

$add_new_field['countries'] = array (	array (	"field_name" => "countries_currencies_id",
												"field_attr" => " int(11) NULL DEFAULT null ",
												"add_after" => "countries_iso_code_3"
												)
									);

add_field($add_new_field);
// End of insert new fields into countries tables

// Insert new records into configuration table (for customer age limit)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='MaxMind'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["MAXMIND_GEOIP_EMAIL_ADDRESS"] = array("insert" => " ('MaxMind Geo IP Email Address', 'MAXMIND_GEOIP_EMAIL_ADDRESS', '', 'Email address to which MaxMind notification email will be send to whenever MaxMind GeoIP does not function. (In \"Name\" format. Use \',\' as delimiter for multiple recipient)', '".$row_sql["configuration_group_id"]."', 25, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for customer age limit)
?>