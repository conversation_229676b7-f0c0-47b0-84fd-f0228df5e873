<?
/*
	$Id: version_2_15_8.php,v 1.1 2008/03/10 09:59:57 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Change field structure for vip_supplier_groups_id in customers_vip table
$change_field_structure = array();

$change_field_structure['customers_vip'] = array (array (	"field_name" => "vip_supplier_groups_id",
															"field_attr" => " int(11) NOT NULL DEFAULT '1' "
											 			)
													);
change_field_structure ($change_field_structure);
// End of change field structure for vip_supplier_groups_id in customers_vip table

// Reconstruct vip_supplier_setting table

// Drop existing primary key (customers_id and categories_id) for vip_supplier_setting table
drop_index_key ("vip_supplier_setting", 'PRIMARY KEY', 'primary', $DBTables, array('customers_id', 'categories_id'));
// End of drop existing primary key (customers_id and categories_id) for vip_supplier_setting table

// Insert new primary key field into vip_supplier_setting table
add_index_key ("vip_supplier_setting", 'primary key', 'primary', 'customers_id, categories_id, vip_supplier_setting_key', $DBTables);
// End of insert new primary key field into vip_supplier_setting table

// End of reconstruct vip_supplier_setting table

// Insert new records into admin_files table (for show image page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["show_image.php"] = array(	"insert" => " ('show_image.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
													);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='show_image.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for show image page)


// Converting OFF DAYS to Working Days
$day_in_week_array = array(1, 2, 3, 4, 5, 6, 0);
$supplier_setting_select_sql = "SELECT customers_id, categories_id, vip_supplier_setting_key, vip_supplier_setting_value 
								FROM " . TABLE_VIP_SUPPLIER_SETTING ."
								WHERE vip_supplier_setting_key = 'OFF_DAYS'";
$supplier_setting_select_result = tep_db_query($supplier_setting_select_sql);
while ($supplier_setting_select_row = tep_db_fetch_array($supplier_setting_select_result)) {	// if found existing record
	$working_day_array = array(	'customers_id' => $supplier_setting_select_row['customers_id'],
								'categories_id' => $supplier_setting_select_row['categories_id']);
	
	$offday_array = explode(",", $supplier_setting_select_row['vip_supplier_setting_value']);
	$working_array = array_diff($day_in_week_array, $offday_array);
	
	$working_day_array = array_merge($working_day_array, array('vip_supplier_setting_key' => 'WORKING_DAYS' , 'vip_supplier_setting_value' => implode("," , $working_array)));
	$working_timezone_array = array_merge($working_day_array, array('vip_supplier_setting_key' => 'TIME_ZONE' , 'vip_supplier_setting_value' => 'GMT +8'));
	$working_time_array = array_merge($working_day_array, array('vip_supplier_setting_key' => 'WORKING_TIME' , 'vip_supplier_setting_value' => '00:00,00:00'));
	
	$offday_delete_sql = "	DELETE FROM " . TABLE_VIP_SUPPLIER_SETTING . " 
							WHERE customers_id='" . $supplier_setting_select_row['customers_id'] . "' 
								AND categories_id='" . $supplier_setting_select_row['categories_id'] . "' 
								AND vip_supplier_setting_key='OFF_DAYS'";
	tep_db_query($offday_delete_sql);
	
	tep_db_perform(TABLE_VIP_SUPPLIER_SETTING, $working_day_array);
	tep_db_perform(TABLE_VIP_SUPPLIER_SETTING, $working_timezone_array);
	tep_db_perform(TABLE_VIP_SUPPLIER_SETTING, $working_time_array);
}
// End of converting OFF DAYS to Working Days
?>