<?
/*
	$Id: version_2_16.php,v 1.3 2008/03/20 06:53:23 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

if (!in_array('custom_product_vault', $DBTables)) {	// New CD Key storing method
	$move_cdkey_from_db = true;
} else {
	$move_cdkey_from_db = false;
}

// Create custom_product_vault table
$add_new_tables = array();

$add_new_tables["custom_product_vault"] = array (	"structure" => "CREATE TABLE `custom_product_vault` (
																	  `products_vault_id` int(11) NOT NULL auto_increment,
																	  `products_id` int(11) NOT NULL default '0',
																	  `file_name` varchar(50) default NULL,
																	  `file_size` int(11) default NULL,
																	  `file_type` varchar(5) default NULL,
																	  `zip_qty` smallint(5) default NULL,
																	  `zip_unit_price` decimal(10,2) default NULL,
																	  `zip_date_added` datetime default NULL,
																	  `zip_date_modified` datetime default NULL,
																	  `zip_date_unzipping` datetime default NULL,
																	  `zip_uploaded_by` varchar(65) default NULL,
																	  `zip_unzip_by` varchar(65) default NULL,
																	  `zip_status` tinyint(1) default NULL,
																	  `zip_description` varchar(255) default NULL,
																	  `remarks` text,
																	  PRIMARY KEY  (`products_vault_id`)
																	) TYPE=MYISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create custom_product_vault table

// Insert new records into admin_files_actions table (for permission on CD key management page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='cdkey.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CP_VIEW_REMARKS"] = array("insert" => " ('CP_VIEW_REMARKS', 'View CD Key Remarks', ".$row_sql["admin_files_id"].", '1', 20)" );
	$admin_files_actions_insert_sql["CP_SEARCH_BY_ID"] = array("insert" => " ('CP_SEARCH_BY_ID', 'Search by Order ID / CD Key ID', ".$row_sql["admin_files_id"].", '1', 30)" );
	$admin_files_actions_insert_sql["CP_UPDATE_STATUS_ACTUAL"] = array("insert" => " ('CP_UPDATE_STATUS_ACTUAL', 'Update CD Key Status - Set as Actual', ".$row_sql["admin_files_id"].", '1', 40)" );
	$admin_files_actions_insert_sql["CP_UPDATE_STATUS_DISABLED"] = array("insert" => " ('CP_UPDATE_STATUS_DISABLED', 'Update CD Key Status - Set as Disabled', ".$row_sql["admin_files_id"].", '1', 50)" );
	$admin_files_actions_insert_sql["CP_UPDATE_STATUS_ON_HOLD"] = array("insert" => " ('CP_UPDATE_STATUS_ON_HOLD', 'Update CD Key Status - Set as On Hold', ".$row_sql["admin_files_id"].", '1', 60)" );
	$admin_files_actions_insert_sql["CP_UPDATE_STATUS_BULK_REMARKS"] = array("insert" => " ('CP_UPDATE_STATUS_BULK_REMARKS', 'Add CD Key remarks in batch', ".$row_sql["admin_files_id"].", '1', 70)" );
	$admin_files_actions_insert_sql["CP_UPDATE_STATUS_DELETE"] = array("insert" => " ('CP_UPDATE_STATUS_DELETE', 'Update CD Key Status - Delete Disabled', ".$row_sql["admin_files_id"].", '1', 80)" );
	$admin_files_actions_insert_sql["CP_UPDATE_VAULT_STATUS"] = array("insert" => " ('CP_UPDATE_VAULT_STATUS', 'Update Zip Vault Status', ".$row_sql["admin_files_id"].", '1', 90)" );
	$admin_files_actions_insert_sql["CP_ADD_REMARKS"] = array("insert" => " ('CP_ADD_REMARKS', 'Add CD Key Remarks', ".$row_sql["admin_files_id"].", '1', 100)" );
	$admin_files_actions_insert_sql["CP_DOWNLOAD_VAULT_ZIP_FILE"] = array("insert" => " ('CP_DOWNLOAD_VAULT_ZIP_FILE', 'Download Vault Zip File', ".$row_sql["admin_files_id"].", '1', 110)" );
	$admin_files_actions_insert_sql["CP_UNZIP_VAULT"] = array("insert" => " ('CP_UNZIP_VAULT', 'Unzip Vault', ".$row_sql["admin_files_id"].", '1', 120)" );
	$admin_files_actions_insert_sql["CP_UPLOAD_CD_KEY"] = array("insert" => " ('CP_UPLOAD_CD_KEY', 'Upload CD Key', ".$row_sql["admin_files_id"].", '1', 130)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on CD key management page)

if ($move_cdkey_from_db) {
	$code_select_sql = "	SELECT custom_products_code_id, code 
							FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
							WHERE custom_products_code_id > 82756";
	$code_result_sql = tep_db_query($code_select_sql);
	
	while ($code_row = tep_db_fetch_array($code_result_sql)) {
		$custom_products_code_id = $code_row['custom_products_code_id'];
		$theData = tep_encrypt_data($code_row['code']);
		
		$fh = fopen(DIR_FS_SECURE.$custom_products_code_id.'.key', 'w') or die("can't open file");
		fwrite($fh, $theData);
		fclose($fh);
	}
}
?>