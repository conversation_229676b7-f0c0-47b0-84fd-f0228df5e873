<?
/*
	$Id: version_2_16_1.php,v 1.3 2008/03/25 03:35:01 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for PayPal Refund API)
$conf_insert_sql = array();

$conf_insert_sql["MODULE_PAYMENT_PAYPAL_API_USERNAME"] = array("insert" => " ('API Username', 'MODULE_PAYMENT_PAYPAL_API_USERNAME', '', 'API Username for PayPal remote features', 6, 240, NULL, now(), NULL, NULL)" );
$conf_insert_sql["MODULE_PAYMENT_PAYPAL_API_PASSWORD"] = array("insert" => " ('API Password', 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', '', 'API Password for PayPal remote features', 6, 245, NULL, now(), NULL, NULL)" );
$conf_insert_sql["MODULE_PAYMENT_PAYPAL_API_SIGNATURE"] = array("insert" => " ('API Signature', 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', '', 'API Signature for PayPal remote features', 6, 250, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for PayPal Refund API)

// Insert new records into admin_files_actions table (for permission on Manual Refund PayPal order)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='refund.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["REFUND_MANUAL_PAYPAL_REFUND"] = array("insert" => " ('REFUND_MANUAL_PAYPAL_REFUND', 'Manually refund PayPal payment without Refund API', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Manual Refund PayPal order)

// Set customers_rating to NULL if havent rated yet
$buyback_rating_select_sql = "	SELECT buyback_request_id 
								FROM buyback_request 
								WHERE customers_rating > 0";
$buyback_rating_result_sql = tep_db_query($buyback_rating_select_sql);
if (!tep_db_num_rows($buyback_rating_result_sql)) {
	$buyback_rating_update_sql = "	UPDATE buyback_request 
									SET customers_rating = NULL 
									WHERE customers_rating <= 0";
	tep_db_query($buyback_rating_update_sql);
}
// End of set customers_rating to NULL if havent rated yet

// Change field structure for customers_rating in buyback_request table
$change_field_structure = array();

$change_field_structure['buyback_request'] = array (array (	"field_name" => "customers_rating",
															"field_attr" => " tinyint(1) default NULL "
											 			)
													);
change_field_structure ($change_field_structure);
// End of change field structure for customers_rating in buyback_request table

// Insert new fields into store_payments tables
$add_new_field = array();

$add_new_field['store_payments'] = array (	array (	"field_name" => "user_country_international_dialing_code",
													"field_attr" => " varchar(5) default NULL ",
													"add_after" => "user_email_address"
													),
											array (	"field_name" => "user_telephone",
													"field_attr" => " varchar(32) NOT NULL default '' ",
													"add_after" => "user_country_international_dialing_code"
													),
											array (	"field_name" => "user_mobile",
													"field_attr" => " varchar(32) NOT NULL default '' ",
													"add_after" => "user_telephone"
													)
										);

add_field($add_new_field);
// End of insert new fields into store_payments tables

// Delete code field from custom_products_code table
$delete_field = array();
$delete_field['custom_products_code'] = array  ( array( "field_name" => "code") );

delete_field ($delete_field);
// End of delete code field from custom_products_code table
?>