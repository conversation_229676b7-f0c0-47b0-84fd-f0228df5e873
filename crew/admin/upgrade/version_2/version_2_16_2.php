<?
/*
	$Id: version_2_16_2.php,v 1.2 2008/03/26 11:05:58 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files table (for orders matching page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='sales.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["orders_matching.php"] = array(	"insert" => " ('orders_matching.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
							   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='orders_matching.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for orders matching page)

// Define invoice as index key in paypal table
add_index_key ('paypal', 'index_invoice', 'index', 'invoice', $DBTables);
// End of define invoice as index key in paypal table

// Define products_id and favourite_products_presale_notice as index key in user_favourite_products table
add_index_key ('user_favourite_products', 'index_products_id_and_presale', 'index', 'products_id, favourite_products_presale_notice', $DBTables);
// End of define products_id and favourite_products_presale_notice as index key in user_favourite_products table

// Define date_added as index key in orders_status_history table
add_index_key ('orders_status_history', 'index_date_added', 'index', 'date_added', $DBTables);
// End of define date_added as index key in orders_status_history table
?>