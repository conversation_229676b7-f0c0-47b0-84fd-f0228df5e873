<?
/*
	$Id: version_2_16_3.php,v 1.1 2008/04/01 07:13:33 sengleong.ang Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create admin_credit_limit table
$add_new_tables = array();

$add_new_tables["admin_credit_limit"] = array (	"structure" => "CREATE TABLE `admin_credit_limit` (
																	`admin_id` int(11) NOT NULL default '0',
																	`admin_credit_limit_max` decimal(15, 4) NOT NULL default '0.0000',
																	`admin_credit_limit_total` decimal(15, 4) NOT NULL default '0.0000',
																	PRIMARY KEY (`admin_id`)
																) TYPE=MYISAM;",
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create admin_credit_limit table

// Insert new records into admin_files_actions table (for permission on editing admin member and group)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='admin_members.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ADMIN_MANAGE_CREDIT_LIMIT"] = array("insert" => " ('ADMIN_MANAGE_CREDIT_LIMIT', 'Manage Admin Daily Credit Limit', ".$row_sql["admin_files_id"].", '1', 25)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on editing admin member and group)
?>