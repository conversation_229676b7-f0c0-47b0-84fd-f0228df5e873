<?
/*
	$Id: version_2_16_6.php,v 1.1 2008/04/22 02:35:14 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create custom_product_vault table
$add_new_tables = array();

$add_new_tables["events"] = array (	"structure" => "CREATE TABLE `events` (
														  `events_id` int(11) NOT NULL auto_increment,
														  `news_id` int(11) NOT NULL default '0',
														  `events_name` varchar(100) NOT NULL default '',
														  `events_remark` text NOT NULL,
														  `events_sender_email` varchar(255) NOT NULL default '',
														  `events_admin_copy_email` varchar(255) NOT NULL default '',
														  `events_email_tpl` text NOT NULL,
														  `events_status` tinyint(1) NOT NULL default '0',
														  `events_order_period_date_from` datetime NOT NULL default '0000-00-00 00:00:00',
														  `events_order_period_date_to` datetime NOT NULL default '0000-00-00 00:00:00',
														  `events_order_period_note` varchar(255) NOT NULL default '',
														  `events_order_period_empty_err_msg` varchar(255) NOT NULL default '',
														  `events_order_period_invalid_err_msg` varchar(255) NOT NULL default '',
														  PRIMARY KEY  (`events_id`)
													) ENGINE=MyISAM;",
									"data" => ""
								);

$add_new_tables["events_options"] = array (	"structure" => "CREATE TABLE `events_options` (
																  `events_options_id` int(11) NOT NULL auto_increment,
																  `events_id` int(11) NOT NULL default '0',
																  `events_options_title` varchar(255) NOT NULL default '',
																  `events_options_input_type` int(2) NOT NULL default '0',
																  `events_options_max_size` int(3) NOT NULL default '0',
																  `events_options_row_size` int(3) NOT NULL default '0',
																  `events_options_column_size` int(3) NOT NULL default '0',
																  `events_options_name` varchar(255) NOT NULL default '',
																  `events_options_sub_sort_order` varchar(255) NOT NULL default '0',
																  `events_options_required` tinyint(1) NOT NULL default '0',
																  `events_options_note` varchar(255) NOT NULL default '',
																  `events_options_err_msg` varchar(255) NOT NULL default '',
																  `events_options_sort_order` int(10) NOT NULL default '0',
																  `events_options_status` tinyint(1) NOT NULL default '1',
																  `events_options_last_modified` datetime NOT NULL default '0000-00-00 00:00:00',
																  `events_options_date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																  PRIMARY KEY  (`events_options_id`)
															) ENGINE=MyISAM;",
											"data" => ""
										);

$add_new_tables["events_options_values"] = array (	"structure" => "CREATE TABLE `events_options_values` (
																		  `events_options_values_id` int(11) NOT NULL auto_increment,
																		  `events_options_id` int(11) NOT NULL default '0',
																		  `events_options_values` varchar(255) NOT NULL default '',
																		  `events_options_values_sort_order` varchar(255) NOT NULL default '',
																		  PRIMARY KEY  (`events_options_values_id`)
																	) ENGINE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create custom_product_vault table

// Insert new records into admin_files table (for event configuration page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='latest_news.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["events.php"] = array(	"insert" => " ('events.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
													"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='events.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for event configuration page)
?>