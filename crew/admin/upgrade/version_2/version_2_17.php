<?
/*
	$Id: version_2_17.php,v 1.1 2008/05/07 03:37:56 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into customer table for PIN to Q&A conversion tracking
$add_new_field = array();
$add_new_field['customers'] = array(array ( "field_name" => "customers_security_start_time",
											"field_attr" => " date default NULL",
											"add_after" => ""
											)
									);

add_field ($add_new_field, false);
// End of insert new field into customer table for PIN to Q&A conversion tracking

// Create CN security tables
$add_new_tables = array();
$add_new_tables["customers_security_questions"] = array (	"structure" => "CREATE TABLE `customers_security_questions` (
																				`customers_security_question_id` int(11) NOT NULL auto_increment,
																				`language_id` int(11) NOT NULL default '1',
																				`customers_security_question` varchar(255) NOT NULL default '',
																				PRIMARY KEY (`customers_security_question_id`, `language_id`)
																			) TYPE=MyISAM ;",
															"data" => "INSERT INTO `customers_security_questions` (`language_id`, `customers_security_question`)
																			VALUES	('2', '&#24744;&#29238;&#20146;&#30340;&#22995;&#21517;&#26159;&#65311;'),
																					('2', '&#24744;&#29238;&#20146;&#30340;&#29983;&#26085;&#26159;&#65311;'),
																					('2', '&#24744;&#29238;&#20146;&#30340;&#32844;&#19994;&#26159;&#65311;'),
																					('2', '&#24744;&#27597;&#20146;&#30340;&#22995;&#21517;&#26159;&#65311;'),
																					('2', '&#24744;&#27597;&#20146;&#30340;&#29983;&#26085;&#26159;&#65311;'),
																					('2', '&#24744;&#27597;&#20146;&#30340;&#32844;&#19994;&#26159;&#65311;'),
																					('2', '&#24744;&#37197;&#20598;&#30340;&#22995;&#21517;&#26159;&#65311;'),
																					('2', '&#24744;&#37197;&#20598;&#30340;&#29983;&#26085;&#26159;&#65311;'),
																					('2', '&#24744;&#37197;&#20598;&#30340;&#32844;&#19994;&#26159;&#65311;'),
																					('2', '&#24744;&#23567;&#23398;&#29677;&#20027;&#20219;&#30340;&#21517;&#23383;&#26159;&#65311;'),
																					('2', '&#24744;&#21021;&#20013;&#29677;&#20027;&#20219;&#30340;&#21517;&#23383;&#26159;&#65311;'),
																					('2', '&#24744;&#39640;&#20013;&#29677;&#20027;&#20219;&#30340;&#21517;&#23383;&#26159;&#65311;'),
																					('2', '&#24744;&#30340;&#23398;&#21495;&#65288;&#25110;&#24037;&#21495;&#65289;&#26159;&#65311;'),
																					('2', '&#24744;&#30340;&#20986;&#29983;&#22320;&#26159;&#65311;');"
														);
$add_new_tables["customers_security"] = array (	"structure" => "CREATE TABLE `customers_security` (
																	`customers_id` int(11) NOT NULL,
																	`customers_security_question_1` varchar(255) NOT NULL default '',
																	`customers_security_answer_1` varchar(50) NOT NULL default '',
																	`customers_security_question_2` varchar(255) NOT NULL default '',
																	`customers_security_answer_2` varchar(50) NOT NULL default '',
																	`customers_security_question_3` varchar(255) NOT NULL default '',
																	`customers_security_answer_3` varchar(50) NOT NULL default '',
																	`customers_security_counter` tinyint(1) NOT NULL default '0',
																	`customers_security_question_ask` varchar(5) default NULL,
																	`customers_security_update_time` datetime NOT NULL default '0000-00-00 00:00:00',
																	PRIMARY KEY (`customers_id`)
																) TYPE=MyISAM ;",
														"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// End of create CN security tables

// Insert new records into configuration table (Buyback Quantity Unit)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Customer Options'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["MAX_SECURITY_TRY"] = array("insert" => " ('Max Security Q&A Try', 'MAX_SECURITY_TRY', '5', 'Maximum security tried for answer the secret question when edit profile, password and bank information at CN site.', ".$row_sql["configuration_group_id"].", 80, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (Buyback Quantity Unit)

$q_n_a_update_sql = "	UPDATE customers 
						SET customers_security_start_time = '2008-05-07'
						WHERE FIND_IN_SET('1', customers_login_sites) 
							AND customers_security_start_time IS NULL 
						LIMIT 6012";
tep_db_query($q_n_a_update_sql);
?>