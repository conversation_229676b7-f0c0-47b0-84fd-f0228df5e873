<?
/*
	$Id: version_2_17_14.php,v 1.1 2008/08/25 03:56:13 weichen Exp $
	
  	Developer: <PERSON> (c) 2008 Dynamic Podium Sdn Bhd
	
  	Released under the GNU General Public License
*/

// Create SEO Meta Tag tables
$add_new_tables = array();

$add_new_tables["seo_meta_tag"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `seo_meta_tag` (
															  `seo_meta_id` int(11) NOT NULL auto_increment,
															  `seo_meta_page_type` int(1) NOT NULL,
															  `seo_meta_baseurl` varchar(50) NOT NULL,
															  `seo_meta_query_string` varchar(100),
															  `seo_meta_title` varchar(255),
															  `seo_meta_title_overwrite` int(1),
															  `seo_meta_description` text,
															  `seo_meta_description_overwrite` int(1),
															  `seo_meta_keywords` varchar(255),
															  `seo_meta_keywords_overwrite` int(1),
															  `seo_meta_robots` int(1),
															  `seo_meta_lastupdate` datetime,
															  PRIMARY KEY  (`seo_meta_id`),
															  <PERSON>EY `index_seo_meta_baseurl` (`seo_meta_baseurl`),
															  KEY `seo_meta_query_string` (`seo_meta_query_string`)
														) TYPE=MyISAM;"
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create SEO Meta Tag tables

// Insert new fields into categories_description table
$add_new_field = array();

$add_new_field['categories_description'] = array (	array (	"field_name" => "categories_short_name",
															"field_attr" => " varchar(64) NOT NULL default '' ",
															"add_after" => "categories_name"
															)
												);

add_field($add_new_field);
// End of insert new fields into categories_description table

// Insert new records into admin_files table (for SEO Meta Tag page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='infolinks.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["seo_meta_tag.php"] = array(	"insert" => " ('seo_meta_tag.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
							   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='seo_meta_tag.php' AND admin_files_is_boxes=0 ");
}

// End of insert new records into admin_files table (for SEO Meta Tag page)

?>