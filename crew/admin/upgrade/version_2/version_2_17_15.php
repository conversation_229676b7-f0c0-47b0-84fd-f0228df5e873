<?
/*
	$Id: version_2_17_15.php,v 1.4 2008/09/03 03:44:59 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium Sdn Bhd
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files table (for Download Center page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["download_center.php"] = array(	"insert" => " ('download_center.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
							   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='download_center.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Download Center page)

// Insert new records into admin_files_actions table (for permission on Download System Generated Report)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='download_center.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["DOWNLOAD_CENTER_CDKEY_SALES"] = array("insert" => " ('DOWNLOAD_CENTER_CDKEY_SALES', 'CD Key 30-day Sales Data', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Download System Generated Report)

// Insert new records into payment_gateway_instance table (for supporting currencies and settle in EUR)
$currency_array = array('AED', 'AUD', 'BND', 'CAD', 'CHF', 'DKK', 'HKD', 'JPY', 'KWD', 'NOK', 'NZD', 'PLN', 'SAR', 'SEK', 'THB');

for ($cur_cnt=0; $cur_cnt < count($currency_array); $cur_cnt++) {
	$select_sql = "	SELECT payment_gateway_code 
					FROM payment_gateway_instance 
					WHERE payment_gateway_code = 'bibit' 
						AND payment_gateway_instance_key='MERCHANT_CODE'
						AND currency_code = '".$currency_array[$cur_cnt]."'" ;
	$result_sql = tep_db_query($select_sql);
	if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
		;
	} else {
		$sgd_select_sql = "	SELECT *  
							FROM payment_gateway_instance 
							WHERE payment_gateway_code = 'bibit' 
								AND currency_code = 'EUR'" ;
		$sgd_result_sql = tep_db_query($sgd_select_sql);
		
		while ($sgd_row = tep_db_fetch_array($sgd_result_sql)) {	// if found existing record
			$sql_data_array = array('payment_gateway_code' => $sgd_row['payment_gateway_code'],
									'payment_gateway_instance_key' => $sgd_row['payment_gateway_instance_key'],
									'currency_code' => $currency_array[$cur_cnt],
									'payment_gateway_instance_value' => $sgd_row['payment_gateway_instance_value']
									);
			tep_db_perform('payment_gateway_instance', $sql_data_array);
		}
	}
}
// End of insert new records into payment_gateway_instance table (for supporting currencies and settle in EUR)

// Update records in countries table (assign international dialing code to each countries)
$dialing_code_update_sql = array();

$dialing_code_update_sql["232"] = array("update" => " countries_international_dialing_code='1340' " );
$dialing_code_update_sql["230"] = array("update" => " countries_name ='Vietnam' " );

update_records(TABLE_COUNTRIES, "countries_id", $dialing_code_update_sql, $DBTables);

// End of update records in countries table (assign international dialing code to each countries)

// Remove useless admin file action key(s)
	$action_keys_delete_sql = array();
	
	$action_keys_delete_sql['UPDATE_PWL_PROGRESS_STATUS'] = array(	"unique" => "1" );
	$action_keys_delete_sql['REVERSIBLE_PWL_PROGRESS_STATUS'] = array(	"unique" => "1" );
	$action_keys_delete_sql['PWL_PROGRESS_STATUS_HOLD_2_COMPLETED'] = array( "unique" => "1" );
	
	delete_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $action_keys_delete_sql, $DBTables);
// End of remove useless admin file action key(s)
?>