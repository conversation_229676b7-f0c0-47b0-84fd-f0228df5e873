<?
/*
	$Id: version_2_17_18.php,v 1.1 2008/09/19 09:34:05 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define filename as index key in orders_tag table
add_index_key ('orders_tag', 'index_filename', 'index', 'filename', $DBTables);
// End of define filename as index key in orders_tag table

// Define customers_telephone as index key in orders table
add_index_key ('orders', 'index_customers_telephone', 'index', 'customers_telephone', $DBTables);
// End of define customers_telephone as index key in orders table

// Insert new records into admin_files_actions table (for permission on Download System Generated Report)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='download_center.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["DOWNLOAD_CENTER_CUSTOMER_PURCHASE_STAT"] = array("insert" => " ('DOWNLOAD_CENTER_CUSTOMER_PURCHASE_STAT', 'Customer Purchase Statistic', ".$row_sql["admin_files_id"].", '1', 50)" );
	$admin_files_actions_insert_sql["DOWNLOAD_CENTER_CRT_KPI"] = array("insert" => " ('DOWNLOAD_CENTER_CRT_KPI', 'CRT KPI Report', ".$row_sql["admin_files_id"].", '1', 100)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Download System Generated Report)
?>