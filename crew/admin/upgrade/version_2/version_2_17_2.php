<?
/*
	$Id: version_2_17_2.php,v 1.2 2008/05/27 02:53:25 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into infolinks table for control show/hide right column boxes
$add_new_field = array();
$add_new_field['infolinks'] = array(array ( "field_name" => "infolinks_right_navigation",
											"field_attr" => " tinyint(1) NOT NULL default '1' ",
											"add_after" => "infolinks_align"
											)
									);

add_field ($add_new_field, false);
// End of insert new field into infolinks table for control show/hide right column boxes

// Change field structure for subproduct_qty in products_bundles table
$change_field_structure = array();

$change_field_structure['products_bundles'] = array (array ("field_name" => "subproduct_qty",
															"field_attr" => " mediumint(4) UNSIGNED NOT NULL default '0' "
											 			)
													);
change_field_structure ($change_field_structure);
// End of change field structure for subproduct_qty in products_bundles table

// Create categories_product_types table
$add_new_tables = array();

$add_new_tables["categories_product_types"] = array (	"structure" => "CREATE TABLE `categories_product_types` (
																		  `categories_id` int(11) NOT NULL default '0',
																		  `custom_products_type_id` int(11) NOT NULL default '0',
																		  PRIMARY KEY  (`categories_id`,`custom_products_type_id`)
																		) TYPE=MyISAM;",
														"data" => "INSERT INTO `categories_product_types` 	(`categories_id`, `custom_products_type_id`) 
																	VALUES 	(2843,0), 
																			(2842,0), 
																			(2214,0), 
																			(1802,0), 
																			(1877,0), 
																			(1964,0), 
																			(1735,0), 
																			(1695,0), 
																			(1958,0), 
																			(1454,0), 
																			(1660,0), 
																			(1496,0), 
																			(3050,0), 
																			(1809,0), 
																			(3167,0), 
																			(3081,0), 
																			(1401,0), 
																			(1982,0), 
																			(1834,0), 
																			(3185,0), 
																			(3083,0), 
																			(3082,0), 
																			(1366,0), 
																			(2167,0), 
																			(1807,0), 
																			(2244,0), 
																			(1629,0), 
																			(3232,0), 
																			(1816,0), 
																			(2217,0), 
																			(2844,0), 
																			(1736,0), 
																			(2845,0), 
																			(2923,0), 
																			(3051,0), 
																			(2921,0), 
																			(2060,0), 
																			(1976,0), 
																			(16,0),
																			(2843,1), 
																			(2214,1), 
																			(1877,1), 
																			(1496,1), 
																			(1401,1), 
																			(1982,1), 
																			(2167,1), 
																			(3232,1), 
																			(2217,1), 
																			(1736,1), 
																			(2845,1), 
																			(1976,1), 
																			(16,1), 
																			(2214,2), 
																			(1877,2), 
																			(2867,2), 
																			(1735,2), 
																			(1695,2), 
																			(1694,2), 
																			(3220,2), 
																			(1808,2), 
																			(2880,2), 
																			(3216,2), 
																			(3031,2), 
																			(1454,2), 
																			(1660,2), 
																			(1496,2), 
																			(2841,2), 
																			(1809,2), 
																			(2982,2), 
																			(2889,2), 
																			(3227,2), 
																			(1985,2), 
																			(1401,2), 
																			(3165,2), 
																			(1982,2), 
																			(3017,2), 
																			(3218,2), 
																			(2894,2), 
																			(2891,2), 
																			(2846,2), 
																			(1366,2), 
																			(2167,2), 
																			(1662,2), 
																			(2860,2), 
																			(3016,2), 
																			(3208,2), 
																			(2244,2), 
																			(2879,2), 
																			(3119,2), 
																			(3163,2), 
																			(3199,2), 
																			(2887,2), 
																			(2898,2), 
																			(1629,2), 
																			(2882,2), 
																			(3232,2), 
																			(2861,2), 
																			(2870,2), 
																			(1736,2), 
																			(1663,2), 
																			(2881,2), 
																			(2923,2), 
																			(2893,2), 
																			(2921,2), 
																			(3256,2), 
																			(2060,2), 
																			(1976,2), 
																			(2897,2), 
																			(2865,2), 
																			(2868,2), 
																			(16,2), 
																			(3015,2), 
																			(3014,2);"
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create categories_product_types table

// Define first_name and last_name as index key in paypal table
add_index_key ('paypal', 'index_payer_name', 'index', 'first_name, last_name', $DBTables);
// End of define first_name and last_name as index key in paypal table

$q_n_a_update_sql = "	UPDATE customers 
						SET customers_security_start_time = '2008-05-27'
						WHERE FIND_IN_SET('1', customers_login_sites) 
							AND customers_security_start_time IS NULL 
						LIMIT 6012";
tep_db_query($q_n_a_update_sql);
?>