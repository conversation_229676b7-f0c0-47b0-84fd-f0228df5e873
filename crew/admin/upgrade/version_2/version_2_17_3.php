<?
/*
	$Id: version_2_17_3.php,v 1.1 2008/06/03 11:02:21 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create Bibit payment gateway related tables
$add_new_tables = array();

$add_new_tables["bibit"] = array 	(	"structure" => 	"CREATE TABLE `bibit` (
														  `orders_id` int(11) NOT NULL default '0',
														  `bibit_payment_id` int(11) default NULL,
														  `bibit_status` varchar(255) NOT NULL default '',
														  `bibit_currency` char(3) NOT NULL default '',
														  `bibit_amount` decimal(15,4) NOT NULL default '0.0000',
														  `bibit_payment_method` varchar(255) NOT NULL default '',
														  `bibit_cvc_status` varchar(64) default NULL,
														  `bibit_avs_status` varchar(64) default NULL,
														  `bibit_risk_score` smallint(4) default NULL,
														  `bibit_mac` varchar(255) NOT NULL default '',
														  `bibit_capture_request` tinyint(1) NOT NULL default '0',
														  PRIMARY KEY  (`orders_id`)
														) TYPE=MyISAM ;",
										"data" => ""
									);

$add_new_tables["bibit_currencies"] = array 	(	"structure" => 	"CREATE TABLE `bibit_currencies` (
																	  `bb_currID` char(3) NOT NULL default '',
																	  `bb_exponent` tinyint(1) NOT NULL default '2',
																	  PRIMARY KEY  (`bb_currID`)
																	) TYPE=MyISAM ;",
													"data" => "	INSERT INTO `bibit_currencies` (`bb_currID`, `bb_exponent`) 
																VALUES 	('AED', 2), 
																		('AFN', 2),
																		('ALL', 2),
																		('AMD', 2),
																		('ANG', 2),
																		('AOA', 2),
																		('ARS', 2),
																		('AUD', 2),
																		('AWG', 2),
																		('AZN', 2),
																		('BAM', 2),
																		('BBD', 2),
																		('BDT', 2),
																		('BGN', 2),
																		('BHD', 3),
																		('BIF', 0),
																		('BMD', 2),
																		('BND', 2),
																		('BOD', 2),
																		('BOV', 2),
																		('BRL', 2),
																		('BSD', 2),
																		('BTN', 2),
																		('BWP', 2),
																		('BYR', 0),
																		('BZD', 2),
																		('CAD', 2),
																		('CHE', 2),
																		('CHF', 2),
																		('CHW', 2),
																		('CLF', 0),
																		('CLP', 0),
																		('CNY', 2),
																		('COP', 2),
																		('COU', 2),
																		('CUP', 2),
																		('CVE', 2),
																		('CZK', 2),
																		('DJF', 0),
																		('DKK', 2),
																		('DOP', 2),
																		('DZD', 2),
																		('EEK', 2),
																		('EGP', 2),
																		('ERN', 2),
																		('ETB', 2),
																		('EUR', 2),
																		('FJD', 2),
																		('FKP', 2),
																		('GBP', 2),
																		('GEL', 2),
																		('GHS', 2),
																		('GIP', 2),
																		('GMD', 2),
																		('GNF', 0),
																		('GTQ', 2),
																		('GYD', 2),
																		('HKD', 2),
																		('HRK', 2),
																		('HTG', 2),
																		('HUF', 2),
																		('IDR', 2),
																		('ILS', 2),
																		('INR', 2),
																		('IQD', 3),
																		('IRR', 2),
																		('ISK', 2),
																		('JMD', 2),
																		('JOD', 3),
																		('JPY', 0),
																		('KES', 2),
																		('KGS', 2),
																		('KHR', 2),
																		('KMF', 0),
																		('KPW', 2),
																		('KRW', 0),
																		('KWD', 3),
																		('KYD', 2),
																		('KZT', 2),
																		('LAK', 2),
																		('LBP', 2),
																		('LKR', 2),
																		('LRD', 2),
																		('LSL', 2),
																		('LTL', 2),
																		('LVL', 2),
																		('LYD', 3),
																		('MAD', 2),
																		('MDL', 2),
																		('MKD', 2),
																		('MMK', 2),
																		('MNT', 2),
																		('MOP', 2),
																		('MUR', 2),
																		('MVR', 2),
																		('MWK', 2),
																		('MXN', 2),
																		('MXV', 2),
																		('MYR', 2),
																		('MZN', 2),
																		('NAD', 2),
																		('NGN', 2),
																		('NIO', 2),
																		('NOK', 2),
																		('NPN', 2),
																		('NZD', 2),
																		('OMR', 3),
																		('PAB', 2),
																		('PEN', 2),
																		('PGK', 2),
																		('PHP', 2),
																		('PKR', 2),
																		('PLN', 2),
																		('PYG', 0),
																		('QAR', 2),
																		('RON', 2),
																		('RSD', 2),
																		('RUB', 2),
																		('RWF', 0),
																		('SAR', 2),
																		('SBD', 2),
																		('SDG', 2),
																		('SEK', 2),
																		('SGD', 2),
																		('SHP', 2),
																		('SKK', 2),
																		('SLL', 2),
																		('SOS', 2),
																		('SRD', 2),
																		('STD', 2),
																		('SYP', 2),
																		('SZL', 2),
																		('THB', 2),
																		('TTD', 2),
																		('TJS', 2),
																		('TMM', 2),
																		('TND', 3),
																		('TOP', 2),
																		('TRY', 2),
																		('TWD', 2),
																		('TZS', 2),
																		('UAH', 2),
																		('UGX', 2),
																		('USD', 2),
																		('USN', 2),
																		('USS', 2),
																		('UYU', 2),
																		('UZS', 2),
																		('VEF', 2),
																		('VND', 2),
																		('VUV', 0),
																		('WST', 2),
																		('XAF', 0),
																		('XCD', 2),
																		('XOF', 0),
																		('XPF', 0),
																		('YER', 2),
																		('ZAR', 2),
																		('ZMK', 2),
																		('ZWD', 2)
																		;"
											);

$add_new_tables["bibit_payment_status_history"] = array 	(	"structure" => 	"CREATE TABLE `bibit_payment_status_history` (
																				  `bb_payment_status_history_id` int(11) NOT NULL auto_increment,
																				  `orders_id` int(11) NOT NULL default '0',
																				  `bb_err_no` smallint(3) unsigned NOT NULL default '0',
																				  `bb_err_txt` varchar(255) NOT NULL default '',
																				  `bb_date` datetime NOT NULL default '0000-00-00 00:00:00',
																				  `bb_status` varchar(255) default NULL,
																				  PRIMARY KEY  (`bb_payment_status_history_id`),
																				  KEY `index_orders_id` (`orders_id`)
																				) TYPE=MyISAM ;",
																"data" => ""
															);

$add_new_tables["payment_gateway_instance"] = array 	(	"structure" => 	"CREATE TABLE `payment_gateway_instance` (
																			  `payment_gateway_code` varchar(32) NOT NULL default '',
																			  `payment_gateway_instance_key` varchar(100) NOT NULL default '',
																			  `currency_code` char(3) NOT NULL default '',
																			  `payment_gateway_instance_value` varchar(64) NOT NULL default '',
																			  PRIMARY KEY  (`payment_gateway_code`,`payment_gateway_instance_key`,`currency_code`)
																			) ENGINE=MyISAM ;",
																		"data" => " INSERT INTO `payment_gateway_instance` (`payment_gateway_code`, `payment_gateway_instance_key`, `currency_code`, `payment_gateway_instance_value`) 
																						VALUES 	('bibit', 'MERCHANT_CODE', 'USD', 'OGHKUSD'), 
																								('bibit', 'MERCHANT_PASSWORD', 'USD', 'skc123store'), 
																								('bibit', 'MERCHANT_CODE', 'GBP', 'OGHKGBP'), 
																								('bibit', 'MERCHANT_PASSWORD', 'GBP', 'skc123store'), 
																								('bibit', 'MERCHANT_CODE', 'EUR', 'OGHKEUR'), 
																								('bibit', 'MERCHANT_PASSWORD', 'EUR', 'skc123store'), 
																								('bibit', 'MERCHANT_CODE', 'SGD', 'OGHKSGD'),
																								('bibit', 'MERCHANT_PASSWORD', 'SGD', 'skc123store');"
															);

add_new_tables ($add_new_tables, $DBTables);

// Enf of create Bibit payment gateway related tables

// Remove useless configuration key(s)
$conf_delete_sql = array();

$conf_delete_sql['REVERSE_SUPPLIER_PAYMENT_EMAIL'] = array(	"unique" => "1" );
$conf_delete_sql['CANCELLATION_VOUCHER_LOST_EMAIL'] = array(	"unique" => "1" );

delete_records(TABLE_CONFIGURATION, "configuration_key", $conf_delete_sql, $DBTables);
// End of remove useless configuration key(s)
?>