<?
/*
	$Id: version_2_17_5.php,v 1.1 2008/06/25 04:07:21 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on Edit Supplier profile)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_EDIT_INFO"] = array("insert" => " ('SUPPLIER_EDIT_INFO', 'Edit supplier profile details', ".$row_sql["admin_files_id"].", '1', 30)" );
	$admin_files_actions_insert_sql["SUPPLIER_EDIT_ACCOUNT"] = array("insert" => " ('SUPPLIER_EDIT_ACCOUNT', 'Edit supplier account', ".$row_sql["admin_files_id"].", '1', 40)" );
	$admin_files_actions_insert_sql["SUPPLIER_EDIT_REMARK"] = array("insert" => " ('SUPPLIER_EDIT_REMARK', 'Edit supplier remark', ".$row_sql["admin_files_id"].", '1', 50)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Edit Supplier profile)
?>