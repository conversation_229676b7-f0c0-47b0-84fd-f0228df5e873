<?
/*
	$Id: version_2_17_6.php,v 1.1 2008/07/02 02:54:54 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on View Supplier profile)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_VIEW_INFO"] = array("insert" => " ('SUPPLIER_VIEW_INFO', 'View supplier profile details', ".$row_sql["admin_files_id"].", '1', 29)" );
	$admin_files_actions_insert_sql["SUPPLIER_VIEW_ACCOUNT"] = array("insert" => " ('SUPPLIER_VIEW_ACCOUNT', 'View supplier account', ".$row_sql["admin_files_id"].", '1', 39)" );
	$admin_files_actions_insert_sql["SUPPLIER_VIEW_REMARK"] = array("insert" => " ('SUPPLIER_VIEW_REMARK', 'View supplier remark', ".$row_sql["admin_files_id"].", '1', 49)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on View Supplier profile)
?>