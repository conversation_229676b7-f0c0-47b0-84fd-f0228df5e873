<?
/*
	$Id: version_2_17_7.php,v 1.1 2008/07/04 04:28:39 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into payment_gateway_instance table (for supporting MYR currency)
$select_sql = "	SELECT payment_gateway_code 
				FROM payment_gateway_instance 
				WHERE payment_gateway_code = 'bibit' 
					AND payment_gateway_instance_key='MERCHANT_CODE'
					AND currency_code = 'MYR'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	;
} else {
	$sgd_select_sql = "	SELECT *  
						FROM payment_gateway_instance 
						WHERE payment_gateway_code = 'bibit' 
							AND currency_code = 'SGD'" ;
	$sgd_result_sql = tep_db_query($sgd_select_sql);
	
	while ($sgd_row = tep_db_fetch_array($sgd_result_sql)) {	// if found existing record
		$sql_data_array = array('payment_gateway_code' => $sgd_row['payment_gateway_code'],
								'payment_gateway_instance_key' => $sgd_row['payment_gateway_instance_key'],
								'currency_code' => 'MYR',
								'payment_gateway_instance_value' => $sgd_row['payment_gateway_instance_value']
								);
		tep_db_perform('payment_gateway_instance', $sql_data_array);
	}
}
// End of insert new records into payment_gateway_instance table (for supporting MYR currency)
?>