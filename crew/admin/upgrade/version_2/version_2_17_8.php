<?
/*
	$Id: version_2_17_8.php,v 1.1 2008/07/15 07:56:46 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Change field structure for bb_err_no in bibit_payment_status_history table
$change_field_structure = array();

$change_field_structure['bibit_payment_status_history'] = array (array ("field_name" => "bb_err_no",
																		"field_attr" => " varchar(32) NOT NULL default '0' "
														 			)
																);
change_field_structure ($change_field_structure);
// End of change field structure for bb_err_no in bibit_payment_status_history table


// Insert new fields into bibit table
$add_new_field = array();

$add_new_field['bibit'] = array (	array (	"field_name" => "bibit_cardholder_aut_result",
											"field_attr" => " varchar(128) NOT NULL default '' ",
											"add_after" => "bibit_avs_status"
											)
								);

add_field($add_new_field);
// End of insert new fields into bibit table
?>