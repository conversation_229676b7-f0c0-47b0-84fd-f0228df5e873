<?
/*
	$Id: version_2_18.php,v 1.1 2008/10/14 08:34:54 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create store_credit_daily_history table
$add_new_tables = array();

$add_new_tables["automate_alert_color"] = array 	(	"structure" => 	"CREATE TABLE `automate_alert_color` (
																		  `automate_alert_id` int(11) NOT NULL auto_increment,
																		  `categories_id` int(11) NOT NULL default '0',
																		  `automate_alert_type` varchar(20) NOT NULL default '',
																		  `automate_alert_value` varchar(10) NOT NULL default '',
																		  `automate_alert_color` varchar(10) NOT NULL default '',
																		  PRIMARY KEY  (`automate_alert_id`)
																		) TYPE=MyISAM;",
															"data" => ""
														);

$add_new_tables["automate_buyback_price"] = array 	(	"structure" => 	"CREATE TABLE `automate_buyback_price` (
																		  `products_id` int(11) NOT NULL default '0',
																		  `buyback_price` decimal(15,6) NOT NULL default '0.000000',
																		  `last_modified` datetime NOT NULL default '0000-00-00 00:00:00',
																		  `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																		  `last_modified_by` varchar(96) NOT NULL default '0',
																		  PRIMARY KEY  (`products_id`)
																		) TYPE=MyISAM;",
															"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// Enf of create store_credit_daily_history table

// Insert new records into admin_files table (for price setting page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["price_automation.php"] = array(	"insert" => " ('price_automation.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
								   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='price_automation.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for price setting page)
?>