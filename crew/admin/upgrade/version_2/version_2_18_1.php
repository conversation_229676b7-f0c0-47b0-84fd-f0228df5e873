<?
/*
	$Id: version_2_18_1.php,v 1.2 2008/11/03 11:23:30 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define txn_id as index key in paypal table
add_index_key ('paypal', 'index_txn_id', 'index', 'txn_id', $DBTables);
// End of define txn_id as index key in paypal table

// Define mb_order_id as index key in moneybookers table
add_index_key ('moneybookers', 'index_mb_order_id', 'index', 'mb_order_id', $DBTables);
// End of define mb_order_id as index key in moneybookers table

// Define info_changed_history_type and info_changed_history_type_id as index key in info_changed_history table
add_index_key ('info_changed_history', 'index_type_and_id', 'index', 'info_changed_history_type, info_changed_history_type_id', $DBTables);
// End of define info_changed_history_type and info_changed_history_type_id as index key in info_changed_history table

// Define customers_info_date_account_created as index key in customers_info table
add_index_key ('customers_info', 'index_date_account_created', 'index', 'customers_info_date_account_created', $DBTables);
// End of define customers_info_date_account_created as index key in customers_info table

// Update admin_files table (for SEO Meta Tag page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["seo_meta_tag.php"] = array(	"insert" => " ('seo_meta_tag.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."'"
							   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='seo_meta_tag.php' AND admin_files_is_boxes=0 ");
}
// End of update admin_files table (for SEO Meta Tag page)


// Customer Permission Task - Wilson
// Insert new records into admin_files_actions table (for permission on Edit Customer page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_VIEW_PROFILE"] = array("insert" => " ('CUSTOMER_VIEW_PROFILE', 'View customer profile details', ".$row_sql["admin_files_id"].", '1', 20)" );
	$admin_files_actions_insert_sql["CUSTOMER_VIEW_REMARK"] = array("insert" => " ('CUSTOMER_VIEW_REMARK', 'View customer remark', ".$row_sql["admin_files_id"].", '1', 50)" );
	$admin_files_actions_insert_sql["CUSTOMER_ENABLE_WITHDRAW"] = array("insert" => " ('CUSTOMER_ENABLE_WITHDRAW', 'Enable withdraw', ".$row_sql["admin_files_id"].", '1', 60)" );
	$admin_files_actions_insert_sql["CUSTOMER_DISABLE_WITHDRAW"] = array("insert" => " ('CUSTOMER_DISABLE_WITHDRAW', 'Disable withdraw', ".$row_sql["admin_files_id"].", '1', 65)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Edit Customer page)

// Update records in admin_files_actions table (assign sort order to each admin file actions)
$admin_files_action_update_sql = array();

$admin_files_action_update_sql["CUSTOMER_EDIT_PROFILE"] = array("update" => " admin_files_sort_order='25' " );
$admin_files_action_update_sql["CUSTOMER_EDIT_ACCOUNT"] = array("update" => " admin_files_sort_order='35' " );
$admin_files_action_update_sql["CUSTOMER_EDIT_GROUP"] = array("update" => " admin_files_sort_order='45' " );
$admin_files_action_update_sql["CUSTOMER_EDIT_REMARK"] = array("update" => " admin_files_sort_order='55' " );

update_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_action_update_sql, $DBTables);
// End of update records in admin_files_actions table (assign sort order to each admin file actions)

// Insert new records into admin_files_actions table (for permission on Edit Customer page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_ENABLE_WITHDRAW"] = array("insert" => " ('SUPPLIER_ENABLE_WITHDRAW', 'Enable withdraw', ".$row_sql["admin_files_id"].", '1', 60)" );
	$admin_files_actions_insert_sql["SUPPLIER_DISABLE_WITHDRAW"] = array("insert" => " ('SUPPLIER_DISABLE_WITHDRAW', 'Disable withdraw', ".$row_sql["admin_files_id"].", '1', 70)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Edit Customer page)

// Insert new fields into customers and supplier tables
$add_new_field = array();

$add_new_field['customers'] = array (	array (	"field_name" => "customers_disable_withdrawal",
												"field_attr" => " tinyint(1) NOT NULL default '0' ",
												"add_after" => "customers_security_start_time"
												)
									);

$add_new_field['supplier'] = array (	array (	"field_name" => "supplier_disable_withdrawal",
												"field_attr" => " tinyint(1) NOT NULL default '0' ",
												"add_after" => "supplier_reserve_amount"
												)
									);

add_field($add_new_field);
// End of insert new fields into customers and supplier tables

?>