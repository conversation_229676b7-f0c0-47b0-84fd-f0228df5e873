<?
/*
	$Id: version_2_18_2.php,v 1.1 2008/11/10 04:54:25 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define customers_login_date and customers_login_ip as index key in customers_login_ip_history table
add_index_key ('customers_login_ip_history', 'index_login_date_and_ip', 'index', 'customers_login_date, customers_login_ip', $DBTables);
// End of define customers_login_date and customers_login_ip as index key in customers_login_ip_history table

// Insert new fields into customers_login_ip_history table
$add_new_field = array();

$add_new_field['customers_login_ip_history'] = array (	array (	"field_name" => "customers_login_ua_info",
																"field_attr" => " varchar(255) NOT NULL default '' ",
																"add_after" => ""
																)
													);

add_field($add_new_field);
// End of insert new fields customers_login_ip_history table

?>