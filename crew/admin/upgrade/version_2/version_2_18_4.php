<?
/*
	$Id: version_2_18_4.php,v 1.1 2008/12/03 03:45:11 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into categories_configuration table (for determine which game support PIMA)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Custom Product'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["GAME_REMOTE_PIMA_LOGIN_API"] = array("insert" => " (0, 'Game Remote PIMA Login API', 'GAME_REMOTE_PIMA_LOGIN_API', 'false', 'Does this game support remote PIMA login API?', ".$row_sql["configuration_group_id"].", 12, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into categories_configuration table (for determine which game support PIMA)

// Insert new field into infolinks table for control show/hide BACK button
$add_new_field = array();
$add_new_field['infolinks'] = array(array ( "field_name" => "infolinks_back_button",
											"field_attr" => " tinyint(1) NOT NULL default '1' ",
											"add_after" => "infolinks_right_navigation"
											)
									);

add_field ($add_new_field, false);
// End of insert new field into infolinks table for control show/hide BACK button
?>