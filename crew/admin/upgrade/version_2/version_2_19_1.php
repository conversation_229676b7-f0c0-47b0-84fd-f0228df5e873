<?
/*
	$Id: version_2_19_1.php,v 1.7 2008/12/18 05:26:34 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_payment_methods_fields = get_table_fields("payment_methods");

// Create split gateway tables
$add_new_tables = array();

$add_new_tables["payment_configuration_info"] = array (	"structure" => "CREATE TABLE `payment_configuration_info` (
																		  `payment_configuration_info_id` int(11) NOT NULL auto_increment,
																		  `payment_methods_id` int(11) NOT NULL default '0',
																		  `payment_configuration_info_title` varchar(64) NOT NULL default '',
																		  `payment_configuration_info_key` varchar(64) NOT NULL default '',
																		  `payment_configuration_info_description` varchar(255) NOT NULL default '',
																		  `payment_configuration_info_sort_order` int(11) NOT NULL default '0',
																		  `last_modified` datetime default NULL,
																		  `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																		  `use_function` varchar(255) default NULL,
																		  `set_function` varchar(255) default NULL,
																		  PRIMARY KEY  (`payment_configuration_info_id`)
																		) TYPE=MyISAM;",
														"data" => "INSERT INTO `payment_configuration_info` (`payment_configuration_info_id`, `payment_methods_id`, `payment_configuration_info_title`, `payment_configuration_info_key`, `payment_configuration_info_description`, `payment_configuration_info_sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`) 
																	VALUES	(1, 12, 'Payment Info Notification Email Address', 'MODULE_PAYMENT_OFFLINE_NOTIFICATION_EMAIL', 'Email address to which the notification email will be send to when payment information is entered.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 915, NULL, '2008-11-20 15:53:37', '', ''),
																		(2, 12, 'Set Order Status', 'MODULE_PAYMENT_OFFLINE_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 930, NULL, '2008-11-20 15:53:37', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(3, 12, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_OFFLINE_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 935, NULL, '2008-11-20 15:53:37', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(4, 12, 'Payment Message', 'MODULE_PAYMENT_OFFLINE_MESSAGE', 'Payment message will show up during checkout process (Direct Deposit or Electronic Funds Transfer)', 945, NULL, '2008-11-20 15:53:37', '', 'tep_cfg_textarea('),
																		(5, 12, 'Payment Email Message', 'MODULE_PAYMENT_OFFLINE_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 950, NULL, '2008-11-20 15:53:37', '', 'tep_cfg_textarea('),
																		(6, 12, 'Confirm Complete (in days)', 'MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed.', 960, NULL, '2008-11-20 15:53:37', '', ''),
																		(7, 17, 'Manual Post Auth Notification', 'MODULE_PAYMENT_WORLDPAY_MANUAL_AUTH_NOTIFICATION_EMAIL', 'Email address to which the notification email will be send to when someone manually authorise WorldPay payment.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 220, NULL, '2008-11-20 15:59:19', '', ''),
																		(8, 17, 'Credit Card Types', 'MODULE_PAYMENT_WORLDPAY_CARD_TYPES', 'Select which credit card types are accepted in this payment method. Those checked types will be available for select in checkout page.', 16, NULL, '2008-11-20 15:59:19', '', 'tep_cfg_key_select_multioption(array(''Amex''=>''Amex'',''VISA''=>''Visa'',''VISP''=>''Visa Purchasing'',''MSCD''=>''Master Card'',''SWIT''=>''Switch'',''SOLO''=>''Solo'',''VISD''=>''Visa Delta'',''VIED''=>''Visa Electron'',''JCB''=>''JCB''),'),
																		(9, 17, 'Confirm Complete (in days)', 'MODULE_PAYMENT_WORLDPAY_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed', 210, NULL, '2008-11-20 15:59:19', '', ''),
																		(10, 17, 'Show credit card types selection', 'MODULE_PAYMENT_WORLDPAY_SHOW_CARD_TYPES', 'Do you want to let customers select their credit card type at payment method selection page?', 15, NULL, '2008-11-20 15:59:19', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(11, 17, 'Payment Email Message', 'MODULE_PAYMENT_WORLDPAY_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 200, NULL, '2008-11-20 15:59:19', '', 'tep_cfg_textarea('),
																		(12, 17, 'Post Authorisation Time Out', 'MODULE_PAYMENT_WORLDPAY_POST_AUTH_TIME_OUT', 'Set the time out period(days) for WorldPay''s Pre-Authorisation transaction.', 6, NULL, '2008-11-20 15:59:19', '', ''),
																		(13, 17, 'Payment Message', 'MODULE_PAYMENT_WORLDPAY_MESSAGE', 'Payment message will show up during checkout process (WorldPay)', 16, NULL, '2008-11-20 15:59:19', '', 'tep_cfg_textarea('),
																		(14, 17, 'Credit card billing address', 'MODULE_PAYMENT_WORLDPAY_BILLING_ADDRESS', 'Should the credit card billing address to be changed at WorldPay payment?', 14, NULL, '2008-11-20 15:59:19', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(15, 17, 'Credit card owner', 'MODULE_PAYMENT_WORLDPAY_CARD_OWNER', 'Should the credit card owner name to be changed at WorldPay payment?', 12, NULL, '2008-11-20 15:59:19', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(16, 17, 'Mode', 'MODULE_PAYMENT_WORLDPAY_MODE', 'The mode you are working in (100 = Test Mode Accept, 101 = Test Mode Decline, 0 = Live.', 3, NULL, '2008-11-20 15:59:19', '', ''),
																		(17, 17, 'Use Pre-Authorisation?', 'MODULE_PAYMENT_WORLDPAY_USEPREAUTH', 'Do you want to pre-authorise payments? Default=False. You need to request this from WorldPay before using it.', 4, NULL, '2008-11-20 15:59:19', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(18, 17, 'Pre-Auth', 'MODULE_PAYMENT_WORLDPAY_PREAUTH', 'The mode you are working in (A = Pay Now, E = Pre Auth). Ignored if Use PreAuth is False.', 5, NULL, '2008-11-20 15:59:19', '', ''),
																		(19, 17, 'Set Order Status', 'MODULE_PAYMENT_WORLDPAY_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value.', 7, NULL, '2008-11-20 15:59:19', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(21, 19, 'Set Order Status', 'MODULE_PAYMENT_EGOLD_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 30, NULL, '2008-11-20 16:00:29', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(22, 19, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_EGOLD_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 35, NULL, '2008-11-20 16:00:29', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(23, 19, 'Payment Message', 'MODULE_PAYMENT_EGOLD_MESSAGE', 'Payment message will show up during checkout process (e-gold)', 40, NULL, '2008-11-20 16:00:29', '', 'tep_cfg_textarea('),
																		(24, 19, 'Payment Email Message', 'MODULE_PAYMENT_EGOLD_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 45, NULL, '2008-11-20 16:00:29', '', 'tep_cfg_textarea('),
																		(25, 19, 'Confirm Complete (in days)', 'MODULE_PAYMENT_EGOLD_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed', 50, NULL, '2008-11-20 16:00:29', NULL, NULL),
																		(26, 21, 'Transaction Mode', 'MODULE_PAYMENT_CASHU_MODE', 'Transaction mode used for the cashU service', 610, NULL, '2008-11-20 16:01:46', NULL, 'tep_cfg_select_option(array(''Test'', ''Production''),'),
																		(27, 21, 'Set Order Status', 'MODULE_PAYMENT_CASHU_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 620, NULL, '2008-11-20 16:01:46', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(28, 21, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_CASHU_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 625, NULL, '2008-11-20 16:01:46', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(29, 21, 'Payment Message', 'MODULE_PAYMENT_CASHU_MESSAGE', 'Payment message will show up during checkout process (cashU)', 640, NULL, '2008-11-20 16:01:46', NULL, 'tep_cfg_textarea('),
																		(30, 21, 'Payment Email Message', 'MODULE_PAYMENT_CASHU_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 645, NULL, '2008-11-20 16:01:46', NULL, 'tep_cfg_textarea('),
																		(31, 21, 'Confirm Complete (in days)', 'MODULE_PAYMENT_CASHU_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed', 655, NULL, '2008-11-20 16:01:46', NULL, NULL),
																		(32, 23, 'Test mode', 'MODULE_PAYMENT_WEBMONEY_SIM_MODE', 'The field is used only in the test mode.', 1110, NULL, '2008-11-20 16:02:37', '', 'tep_cfg_key_select_option(array(''0''=>''All test payments will be successful'', ''1''=>''All test payments will fail'', ''2''=>''80% of test payments will be successful, 20% of test payments will fail.''),'),
																		(33, 23, 'Set Order Status', 'MODULE_PAYMENT_WEBMONEY_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 1120, NULL, '2008-11-20 16:02:37', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(34, 23, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_WEBMONEY_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 1125, NULL, '2008-11-20 16:02:37', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(35, 23, 'Payment Message', 'MODULE_PAYMENT_WEBMONEY_MESSAGE', 'Payment message will show up during checkout process (WebMoney)', 1130, NULL, '2008-11-20 16:02:37', '', 'tep_cfg_textarea('),
																		(36, 23, 'Payment Email Message', 'MODULE_PAYMENT_WEBMONEY_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 1135, NULL, '2008-11-20 16:02:37', '', 'tep_cfg_textarea('),
																		(37, 23, 'Confirm Complete (in days)', 'MODULE_PAYMENT_WEBMONEY_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed', 1140, NULL, '2008-11-20 16:02:37', '', ''),
																		(39, 25, 'Set Pending Notification Status', 'MODULE_PAYMENT_PAYPAL_PROCESSING_STATUS_ID', 'Set the Pending Notification status of orders made with this payment module to this value (''Processing'' recommended)', 5, NULL, '2008-11-20 16:04:26', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(40, 25, 'Set Order Status', 'MODULE_PAYMENT_PAYPAL_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value<br>(''Pending'' recommended)', 6, NULL, '2008-11-20 16:04:26', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(41, 25, 'Synchronize Invoice', 'MODULE_PAYMENT_PAYPAL_INVOICE_REQUIRED', 'Do you want to specify the order number as the PayPal invoice number?', 7, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(42, 25, 'Background Color', 'MODULE_PAYMENT_PAYPAL_CS', 'Select the background color of PayPal''s payment pages.', 9, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''White'',''Black''),'),
																		(43, 25, 'Processing logo', 'MODULE_PAYMENT_PAYPAL_PROCESSING_LOGO', 'The image file name to display the store''s checkout process', 10, NULL, '2008-11-20 16:04:26', '', ''),
																		(44, 25, 'Store logo', 'MODULE_PAYMENT_PAYPAL_STORE_LOGO', 'The image file name for PayPal to display (leave empty if your store does not have SSL)', 11, NULL, '2008-11-20 16:04:26', '', ''),
																		(45, 25, 'PayPal Page Style Name', 'MODULE_PAYMENT_PAYPAL_PAGE_STYLE', 'The name of the page style you have configured in your PayPal Account', 12, NULL, '2008-11-20 16:04:26', '', ''),
																		(46, 25, 'Include a note with payment', 'MODULE_PAYMENT_PAYPAL_NO_NOTE', 'Choose whether your customer should be prompted to include a note or not?', 13, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''Yes'',''No''),'),
																		(47, 25, 'Shopping Cart Method', 'MODULE_PAYMENT_PAYPAL_METHOD', 'What type of shopping cart do you want to use?', 14, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''Aggregate'',''Itemized''),'),
																		(48, 25, 'Enable PayPal Shipping Address', 'MODULE_PAYMENT_PAYPAL_SHIPPING_ALLOWED', 'Allow the customer to choose their own PayPal shipping address?', 15, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''Yes'',''No''),'),
																		(49, 25, 'Debug Email Notifications', 'MODULE_PAYMENT_PAYPAL_IPN_DEBUG', 'Enable debug email notifications', 16, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''Yes'',''No''),'),
																		(50, 25, 'Digest Key', 'MODULE_PAYMENT_PAYPAL_IPN_DIGEST_KEY', 'Key to use for the digest functionality', 17, NULL, '2008-11-20 16:04:26', '', ''),
																		(51, 25, 'Test Mode', 'MODULE_PAYMENT_PAYPAL_IPN_TEST_MODE', 'Set test mode (<a href=\"http://crew.dev5.offgamers.lan/paypal.php?filename=paypal.php&action=test\" target=\"ipn\"><u>Launch Test Page</u></a>)', 18, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''Off'',''On''),'),
																		(52, 25, 'Cart Test', 'MODULE_PAYMENT_PAYPAL_IPN_CART_TEST', 'Set cart test mode to verify the transaction amounts', 19, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''Off'',''On''),'),
																		(53, 25, 'Notification Address', 'MODULE_PAYMENT_PAYPAL_IPN_DEBUG_EMAIL', 'The e-mail address to send (level 1) notifications to', 20, NULL, '2008-11-20 16:04:26', '', ''),
																		(54, 25, 'PayPal Domain', 'MODULE_PAYMENT_PAYPAL_DOMAIN', 'Select which PayPal domain to use<br>(for live production select www.paypal.com)', 21, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''www.paypal.com'',''www.sandbox.paypal.com''),'),
																		(55, 25, 'Return URL behavior', 'MODULE_PAYMENT_PAYPAL_RM', 'How should the customer be sent back from PayPal to the specified URL?<br>0=No IPN, 1=GET, 2=POST', 22, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''0'',''1'',''2''),'),
																		(56, 25, 'Payment Message', 'MODULE_PAYMENT_PAYPAL_MESSAGE', 'Payment message will show up during checkout process (PayPal)', 24, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_textarea('),
																		(57, 25, 'Payment Email Message', 'MODULE_PAYMENT_PAYPAL_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 200, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_textarea('),
																		(58, 25, 'Confirm Complete (in days)', 'MODULE_PAYMENT_PAYPAL_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed', 210, NULL, '2008-11-20 16:04:26', '', ''),
																		(59, 25, 'Verified Payments E-mail Admin Notification', 'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION', 'E-mail address to which the verification of payment e-mail will be send to whenever customers verified their payment e-mail address.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 220, NULL, '2008-11-20 16:04:26', '', ''),
																		(60, 25, 'Verified Payments E-mail Admin Notification Status', 'MODULE_PAYMENT_PAYPAL_VERIFIED_EMAIL_NOTIFICATION_STATUS', 'The status for the orders to be included in the verified payment e-mail notification e-mail contents. Seperated by '','' for multiple values.', 225, NULL, '2008-11-20 16:04:26', '', ''),
																		(61, 25, 'Encrypted Website Payments (EWP)', 'MODULE_PAYMENT_PAYPAL_EWP', 'Encrypt your PayPal checkout buttons dynamically when rendering your webpages to prevent and/or detect tampering with your buttons.', 230, NULL, '2008-11-20 16:04:26', '', 'tep_cfg_select_option(array(''Yes'', ''No''),'),
																		(63, 27, 'Set Order Status', 'MODULE_PAYMENT_IPAY88_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 1030, NULL, '2008-11-20 16:06:12', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(64, 27, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_IPAY88_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 1035, NULL, '2008-11-20 16:06:12', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(65, 27, 'Payment Message', 'MODULE_PAYMENT_IPAY88_MESSAGE', 'Payment message will show up during checkout process (iPay88)', 1045, NULL, '2008-11-20 16:06:12', '', 'tep_cfg_textarea('),
																		(66, 27, 'Payment Email Message', 'MODULE_PAYMENT_IPAY88_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 1045, NULL, '2008-11-20 16:06:12', '', 'tep_cfg_textarea('),
																		(67, 27, 'Confirm Complete (in days)', 'MODULE_PAYMENT_IPAY88_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed', 1050, NULL, '2008-11-20 16:06:12', '', ''),
																		(68, 38, 'Set Order Status', 'MODULE_PAYMENT_MONEYORDER_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 5, NULL, '2008-11-20 16:10:06', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(69, 38, 'Payment Message', 'MODULE_PAYMENT_MONEYORDER_MESSAGE', 'Payment message will show up during checkout process (Money Order)', 6, NULL, '2008-11-20 16:10:06', '', 'tep_cfg_textarea('),
																		(70, 38, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_MONEYORDER_PROCESSING_STATUS_ID', 'PaymentSet the initial processing status of orders made with this payment module to this value', 30, NULL, '2008-11-20 16:10:06', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(71, 38, 'Payment Email Message', 'MODULE_PAYMENT_MONEYORDER_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 200, NULL, '2008-11-20 16:10:06', '', 'tep_cfg_textarea('),
																		(72, 38, 'Confirm Complete (in days)', 'MODULE_PAYMENT_MONEYORDER_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed', 210, NULL, '2008-11-20 16:10:06', '', ''),
																		(73, 40, 'Transaction Mode', 'MODULE_PAYMENT_2CHECKOUT_TESTMODE', 'Transaction mode used for the 2Checkout service', 3, NULL, '2008-11-20 16:11:41', '', 'tep_cfg_select_option(array(''Test'', ''Production''),'),
																		(74, 40, 'Merchant Notifications', 'MODULE_PAYMENT_2CHECKOUT_EMAIL_MERCHANT', 'Should 2CheckOut e-mail a receipt to the store owner?', 4, NULL, '2008-11-20 16:11:41', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(75, 40, 'Set Order Status', 'MODULE_PAYMENT_2CHECKOUT_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 7, NULL, '2008-11-20 16:11:41', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(76, 40, 'Check MD5 hash', 'MODULE_PAYMENT_2CHECKOUT_CHECK_HASH', 'Should the 2CheckOut MD5 hash facilty to be checked?', 8, NULL, '2008-11-20 16:11:41', 'tep_get_order_status_name', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(77, 40, 'Payment Message', 'MODULE_PAYMENT_2CHECKOUT_MESSAGE', 'Payment message will show up during checkout process (2CheckOut)', 13, NULL, '2008-11-20 16:11:41', '', 'tep_cfg_textarea('),
																		(78, 40, 'Credit card owner', 'MODULE_PAYMENT_2CHECKOUT_CARD_OWNER', 'Should the credit card owner name to be changed at checkout payment?', 10, NULL, '2008-11-20 16:11:41', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(79, 40, 'Credit card billing address', 'MODULE_PAYMENT_2CHECKOUT_BILLING_ADDRESS', 'Should the credit card billing address to be changed at checkout payment?', 11, NULL, '2008-11-20 16:11:41', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(80, 40, 'Confirm Complete (in days)', 'MODULE_PAYMENT_2CHECKOUT_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed', 210, NULL, '2008-11-20 16:11:41', '', ''),
																		(81, 42, 'Payment Info Notification Email Address', 'MODULE_PAYMENT_MOORCHECK_NOTIFICATION_EMAIL', 'Email address to which the notification email will be send to when payment information is entered.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 715, NULL, '2008-11-20 16:12:45', '', ''),
																		(82, 42, 'Set Order Status', 'MODULE_PAYMENT_MOORCHECK_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 730, NULL, '2008-11-20 16:12:45', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(83, 42, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_MOORCHECK_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 735, NULL, '2008-11-20 16:12:45', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(84, 42, 'Payment Message', 'MODULE_PAYMENT_MOORCHECK_MESSAGE', 'Payment message will show up during checkout process (Check/Money Order)', 745, NULL, '2008-11-20 16:12:45', '', 'tep_cfg_textarea('),
																		(85, 42, 'Payment Email Message', 'MODULE_PAYMENT_MOORCHECK_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 750, NULL, '2008-11-20 16:12:45', '', 'tep_cfg_textarea('),
																		(86, 42, 'Confirm Complete (in days)', 'MODULE_PAYMENT_MOORCHECK_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completeds', 760, NULL, '2008-11-20 16:12:45', '', ''),
																		(88, 44, 'Transaction Language', 'MODULE_PAYMENT_MONEYBOOKERS_LANGUAGE', 'Select the default language for the payment transactions. If the user selected language is not available at moneybookers.com, this currency will be the payment language.', 25, NULL, '2008-11-20 16:15:39', '', 'tep_cfg_select_option(array(''EN'', ''DE'', ''ES'', ''FR'', ''IT'', ''PL''),'),
																		(89, 44, 'Set Order Status', 'MODULE_PAYMENT_MONEYBOOKERS_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 28, NULL, '2008-11-20 16:15:39', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(90, 44, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_MONEYBOOKERS_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value.', 29, NULL, '2008-11-20 16:15:39', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(91, 44, 'Payment Message', 'MODULE_PAYMENT_MONEYBOOKERS_MESSAGE', 'Payment message will show up during checkout process (moneybookers.com)', 30, NULL, '2008-11-20 16:15:39', '', 'tep_cfg_textarea('),
																		(92, 44, 'Payment Email Message', 'MODULE_PAYMENT_MONEYBOOKERS_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 35, NULL, '2008-11-20 16:15:39', '', 'tep_cfg_textarea('),
																		(93, 44, 'Confirm Complete (in days)', 'MODULE_PAYMENT_MONEYBOOKERS_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed.', 40, NULL, '2008-11-20 16:15:39', '', ''),
																		(94, 64, 'Test Mode', 'MODULE_PAYMENT_BIBIT_TEST_MODE', 'Test Mode?', 1160, NULL, '2008-11-20 16:35:26', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(95, 64, 'Set Order Status', 'MODULE_PAYMENT_BIBIT_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 1190, NULL, '2008-11-20 16:35:26', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(96, 64, 'Payment Message', 'MODULE_PAYMENT_BIBIT_MESSAGE', 'Payment message will show up during checkout process (Bibit)', 1200, NULL, '2008-11-20 16:35:26', '', 'tep_cfg_textarea('),
																		(97, 64, 'Use Pre-Captured?', 'MODULE_PAYMENT_BIBIT_USEPRECAPTURE', 'Do you want to pre-captured payments? Default=False. You need to set in Bibit Merchant Interface before using it.', 1210, NULL, '2008-11-20 16:35:26', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(98, 64, 'Payment Email Message', 'MODULE_PAYMENT_BIBIT_EMAIL_MESSAGE', 'Thank you for shopping at Offgamers.com.', 1215, NULL, '2008-11-20 16:35:26', '', 'tep_cfg_textarea('),
																		(99, 64, 'Confirm Complete (in days)', 'MODULE_PAYMENT_BIBIT_CONFIRM_COMPLETE', 'Select the default currency for the payment transactions. If the user selected currency is not available at moneybookers.com, this currency will be the payment currency.', 1220, NULL, '2008-11-20 16:35:26', '', ''),
																		(100, 64, 'Manual Post Captured Notification', 'MODULE_PAYMENT_BIBIT_MANUAL_CAPTURE_NOTIFICATION_EMAIL', 'Email address to which the notification email will be send to when someone manually capture Bibit payment.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 1225, NULL, '2008-11-20 16:35:26', '', ''),
																		(107, 107, 'Test Mode', 'MODULE_PAYMENT_BIBIT_TEST_MODE', 'Test Mode?', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(108, 107, 'Set Order Status', 'MODULE_PAYMENT_BIBIT_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 0, NULL, '0000-00-00 00:00:00', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(109, 107, 'Payment Message', 'MODULE_PAYMENT_BIBIT_MESSAGE', 'Payment message will show up during checkout process (Bibit)', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(110, 107, 'Use Pre-Captured?', 'MODULE_PAYMENT_BIBIT_USEPRECAPTURE', 'Do you want to pre-captured payments? Default=False. You need to set in Bibit Merchant Interface before using it.', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_select_option(array(''True'', ''False''),'),
																		(111, 107, 'Payment Email Message', 'MODULE_PAYMENT_BIBIT_EMAIL_MESSAGE', 'Thank you for shopping at Offgamers.com.', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(112, 107, 'Confirm Complete (in days)', 'MODULE_PAYMENT_BIBIT_CONFIRM_COMPLETE', 'Select the default currency for the payment transactions. If the user selected currency is not available at moneybookers.com, this currency will be the payment currency.', 0, NULL, '0000-00-00 00:00:00', '', ''),
																		(113, 107, 'Manual Post Captured Notification', 'MODULE_PAYMENT_BIBIT_MANUAL_CAPTURE_NOTIFICATION_EMAIL', 'Email address to which the notification email will be send to when someone manually capture Bibit payment.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 0, NULL, '0000-00-00 00:00:00', '', ''),
																		(114, 15, 'Payment Info Notification Email Address', 'MODULE_PAYMENT_OFFLINE_NOTIFICATION_EMAIL', 'Email address to which the notification email will be send to when payment information is entered.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 0, NULL, '0000-00-00 00:00:00', '', ''),
																		(115, 15, 'Set Order Status', 'MODULE_PAYMENT_OFFLINE_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 0, NULL, '0000-00-00 00:00:00', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(116, 15, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_OFFLINE_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 0, NULL, '0000-00-00 00:00:00', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(117, 15, 'Payment Message', 'MODULE_PAYMENT_OFFLINE_MESSAGE', 'Payment message will show up during checkout process (Direct Deposit or Electronic Funds Transfer)', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(118, 15, 'Payment Email Message', 'MODULE_PAYMENT_OFFLINE_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(119, 15, 'Confirm Complete (in days)', 'MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed.', 0, NULL, '0000-00-00 00:00:00', '', ''),
																		(120, 16, 'Payment Info Notification Email Address', 'MODULE_PAYMENT_OFFLINE_NOTIFICATION_EMAIL', 'Email address to which the notification email will be send to when payment information is entered.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 0, NULL, '0000-00-00 00:00:00', '', ''),
																		(121, 16, 'Set Order Status', 'MODULE_PAYMENT_OFFLINE_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 0, NULL, '0000-00-00 00:00:00', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(122, 16, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_OFFLINE_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 0, NULL, '0000-00-00 00:00:00', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(123, 16, 'Payment Message', 'MODULE_PAYMENT_OFFLINE_MESSAGE', 'Payment message will show up during checkout process (Direct Deposit or Electronic Funds Transfer)', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(124, 16, 'Payment Email Message', 'MODULE_PAYMENT_OFFLINE_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(125, 16, 'Confirm Complete (in days)', 'MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed.', 0, NULL, '0000-00-00 00:00:00', '', ''),
																		(126, 14, 'Payment Info Notification Email Address', 'MODULE_PAYMENT_OFFLINE_NOTIFICATION_EMAIL', 'Email address to which the notification email will be send to when payment information is entered.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 0, NULL, '0000-00-00 00:00:00', '', ''),
																		(127, 14, 'Set Order Status', 'MODULE_PAYMENT_OFFLINE_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 0, NULL, '0000-00-00 00:00:00', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(128, 14, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_OFFLINE_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 0, NULL, '0000-00-00 00:00:00', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(129, 14, 'Payment Message', 'MODULE_PAYMENT_OFFLINE_MESSAGE', 'Payment message will show up during checkout process (Direct Deposit or Electronic Funds Transfer)', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(130, 14, 'Payment Email Message', 'MODULE_PAYMENT_OFFLINE_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(131, 14, 'Confirm Complete (in days)', 'MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed.', 0, NULL, '0000-00-00 00:00:00', '', ''),
																		(132, 13, 'Payment Info Notification Email Address', 'MODULE_PAYMENT_OFFLINE_NOTIFICATION_EMAIL', 'Email address to which the notification email will be send to when payment information is entered.<br>(In \"Name &lt;Email&gt;\" format. Use '','' as delimiter for multiple recipient)', 0, NULL, '0000-00-00 00:00:00', '', ''),
																		(133, 13, 'Set Order Status', 'MODULE_PAYMENT_OFFLINE_ORDER_STATUS_ID', 'Set the status of orders made with this payment module to this value', 0, NULL, '0000-00-00 00:00:00', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(134, 13, 'Set Order''s \"Processing\" Status', 'MODULE_PAYMENT_OFFLINE_PROCESSING_STATUS_ID', 'Set the initial processing status of orders made with this payment module to this value', 0, NULL, '0000-00-00 00:00:00', 'tep_get_order_status_name', 'tep_cfg_pull_down_order_statuses('),
																		(135, 13, 'Payment Message', 'MODULE_PAYMENT_OFFLINE_MESSAGE', 'Payment message will show up during checkout process (Direct Deposit or Electronic Funds Transfer)', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(136, 13, 'Payment Email Message', 'MODULE_PAYMENT_OFFLINE_EMAIL_MESSAGE', 'This message will show up in order confirmation e-mail.', 0, NULL, '0000-00-00 00:00:00', '', 'tep_cfg_textarea('),
																		(137, 13, 'Confirm Complete (in days)', 'MODULE_PAYMENT_OFFLINE_CONFIRM_COMPLETE', 'The number of days in which an order made by this payment method is confirm completed.', 0, NULL, '0000-00-00 00:00:00', '', '');"
													);

$add_new_tables["payment_configuration_info_description"] = array ("structure" => "CREATE TABLE `payment_configuration_info_description` (
																					  `payment_configuration_info_id` int(11) NOT NULL,
																					  `languages_id` int(11) NOT NULL default '1',
																					  `payment_configuration_info_value` text NOT NULL,
																					  PRIMARY KEY  (`payment_configuration_info_id`,`languages_id`)
																					) TYPE=MyISAM;",
																	"data" => "INSERT INTO `payment_configuration_info_description` (`payment_configuration_info_id`, `languages_id`, `payment_configuration_info_value`) 
																				VALUES (1, 1, ''),
																					(2, 1, '0'),
																					(3, 1, '7'),
																					(4, 1, 'Thank you for shopping at Offgamers.com.'),
																					(5, 1, 'Thank you for shopping at Offgamers.com.'),
																					(6, 1, '0'),
																					(7, 1, '<<EMAIL>>'),
																					(8, 1, ''),
																					(9, 1, '120'),
																					(10, 1, 'False'),
																					(11, 1, 'Thank you for shopping at Offgamers.com.'),
																					(12, 1, '4'),
																					(13, 1, 'Thank you for shopping at SKC Store.'),
																					(14, 1, 'True'),
																					(15, 1, 'True'),
																					(16, 1, '0'),
																					(17, 1, 'True'),
																					(18, 1, 'E'),
																					(19, 1, '0'),
																					(21, 1, '1'),
																					(22, 1, '2'),
																					(23, 1, 'Thank you for shopping at Offgamers.com.'),
																					(24, 1, 'Thank you for shopping at Offgamers.com.'),
																					(25, 1, '0'),
																					(26, 1, 'Production'),
																					(27, 1, '1'),
																					(28, 1, '2'),
																					(29, 1, 'A popular payment gateway in the Middle East. Allows you to pay by Visa / MasterCard and various other methods.'),
																					(30, 1, ''),
																					(31, 1, '0'),
																					(32, 1, '0'),
																					(33, 1, '1'),
																					(34, 1, '2'),
																					(35, 1, 'Thank you for shopping at Offgamers.com.'),
																					(36, 1, 'Thank you for shopping at Offgamers.com.'),
																					(37, 1, '0'),
																					(39, 1, '1'),
																					(40, 1, '7'),
																					(41, 1, 'True'),
																					(42, 1, 'White'),
																					(43, 1, ''),
																					(44, 1, ''),
																					(45, 1, 'default'),
																					(46, 1, 'No'),
																					(47, 1, 'Aggregate'),
																					(48, 1, 'Yes'),
																					(49, 1, 'No'),
																					(50, 1, 'PayPal_Shopping_Cart_IPN'),
																					(51, 1, 'Off'),
																					(52, 1, 'On'),
																					(53, 1, '<EMAIL>'),
																					(54, 1, 'www.paypal.com'),
																					(55, 1, '1'),
																					(56, 1, 'Pay by credit cards, debit cards, bank accounts and e-checks. E-checks will take 4-5 business days to clear and all new Paypal payments are subjected to verification.'),
																					(57, 1, 'Please note that PayPal payments are subjected to verification. Please make sure contact number and shipping address is correctly updated under \"My Account\" section.<br><br>\nPayPal Instant Payment Notification (IPN) may take up to 2 hours to notify our store on payment received when the load of PayPal servers is high.\n<br><br>E-checks will take 4-5 business days to clear.'),
																					(58, 1, '120'),
																					(59, 1, '<<EMAIL>>'),
																					(60, 1, '7'),
																					(61, 1, 'Yes'),
																					(63, 1, '1'),
																					(64, 1, '7'),
																					(65, 1, ''),
																					(66, 1, ''),
																					(67, 1, '0'),
																					(68, 1, '0'),
																					(69, 1, 'Thank you for shopping at SKC Store.'),
																					(70, 1, '2'),
																					(71, 1, 'Thank you for shopping at Offgamers.com.'),
																					(72, 1, '0'),
																					(73, 1, 'Test'),
																					(74, 1, 'True'),
																					(75, 1, '0'),
																					(76, 1, 'False'),
																					(77, 1, 'Thank you for shopping at SKC Store.'),
																					(78, 1, 'True'),
																					(79, 1, 'True'),
																					(80, 1, '0'),
																					(81, 1, ''),
																					(82, 1, '0'),
																					(83, 1, '7'),
																					(84, 1, 'Thank you for shopping at Offgamers.com.'),
																					(85, 1, 'Thank you for shopping at Offgamers.com.'),
																					(86, 1, '0'),
																					(88, 1, 'EN'),
																					(89, 1, '1'),
																					(90, 1, '7'),
																					(91, 1, 'Pay by credit cards, debit cards and bank accounts. First time payments are subjected to a one-time verification by Moneybookers.'),
																					(92, 1, 'If this is your first time paying through Moneybookers, your payment will be subjected to a one-time verification process by Moneybookers which will take up to 24 hours from now (48 hours on weekends).'),
																					(93, 1, '0'),
																					(94, 1, 'False'),
																					(95, 1, '1'),
																					(96, 1, 'Pay by Visa Credit / Debit, MasterCard Credit / Debit, or eNets. Credit card payments are subjected to verification.'),
																					(97, 1, 'False'),
																					(98, 1, 'Note that credit card payments are subjected to verification. Please make sure that your contact number and shipping address is correctly updated in \"My Account\".'),
																					(99, 1, '150'),
																					(100, 1, ''),
																					(107, 1, 'False'),
																					(108, 1, '0'),
																					(109, 1, 'Thank you for shopping at SKC Store.'),
																					(110, 1, 'False'),
																					(111, 1, 'Thank you for shopping at SKC Store.'),
																					(112, 1, '150'),
																					(113, 1, ''),
																					(114, 1, '<<EMAIL>>, <<EMAIL>>, <<EMAIL>>'),
																					(115, 1, '1'),
																					(116, 1, '7'),
																					(117, 1, 'Thank you for shopping at Offgamers.com.'),
																					(118, 1, 'Thank you for shopping at Offgamers.com.'),
																					(119, 1, '0'),
																					(120, 1, '<<EMAIL>>, <<EMAIL>>'),
																					(121, 1, '1'),
																					(122, 1, '7'),
																					(123, 1, 'Thank you for shopping at Offgamers.com.'),
																					(124, 1, 'Thank you for shopping at Offgamers.com.'),
																					(125, 1, '0'),
																					(126, 1, '<<EMAIL>>, <<EMAIL>>, <<EMAIL>>'),
																					(127, 1, '1'),
																					(128, 1, '7'),
																					(129, 1, 'Thank you for shopping at Offgamers.com.'),
																					(130, 1, 'Thank you for shopping at Offgamers.com.'),
																					(131, 1, '0'),
																					(132, 1, '<<EMAIL>>, <<EMAIL>>'),
																					(133, 1, '1'),
																					(134, 1, '7'),
																					(135, 1, 'Thank you for shopping at Offgamers.com.'),
																					(136, 1, 'Thank you for shopping at Offgamers.com.'),
																					(137, 1, '0');"
																	);


$add_new_tables["payment_methods_status_description"] = array (	"structure" => "CREATE TABLE `payment_methods_status_description` (
																				  `payment_methods_id` int(11) NOT NULL default '0',
																				  `payment_methods_mode` varchar(12) NOT NULL default 'SEND',
																				  `payment_methods_status` tinyint(1) NOT NULL default '0',
																				  `languages_id` int(11) NOT NULL default '1',
																				  `payment_methods_status_message` varchar(255) default NULL,
																				  PRIMARY KEY  (`payment_methods_id`, `payment_methods_mode`, `payment_methods_status`, `languages_id`)
																				) TYPE=MyISAM;",
																"data" => "INSERT INTO `payment_methods_status_description` (`payment_methods_id`, `payment_methods_mode`, `payment_methods_status`, `languages_id`, `payment_methods_status_message`) 
																			VALUES (12, 'RECEIVE', -1, 1, ''),
																				(13, 'RECEIVE', -1, 1, ''),
																				(14, 'RECEIVE', -1, 1, ''),
																				(15, 'RECEIVE', -1, 1, ''),
																				(16, 'RECEIVE', -1, 1, ''),
																				(17, 'RECEIVE', -1, 1, ''),
																				(18, 'RECEIVE', -1, 1, ''),
																				(19, 'RECEIVE', -1, 1, ''),
																				(20, 'RECEIVE', -1, 1, ''),
																				(21, 'RECEIVE', -1, 1, ''),
																				(22, 'RECEIVE', -1, 1, ''),
																				(23, 'RECEIVE', -1, 1, ''),
																				(24, 'RECEIVE', -1, 1, ''),
																				(25, 'RECEIVE', -1, 1, ''),
																				(26, 'RECEIVE', -1, 1, ''),
																				(27, 'RECEIVE', -1, 1, ''),
																				(28, 'RECEIVE', -1, 1, ''),
																				(29, 'RECEIVE', -1, 1, ''),
																				(30, 'RECEIVE', -1, 1, ''),
																				(31, 'RECEIVE', -1, 1, ''),
																				(32, 'RECEIVE', -1, 1, ''),
																				(33, 'RECEIVE', -1, 1, ''),
																				(34, 'RECEIVE', -1, 1, ''),
																				(35, 'RECEIVE', -1, 1, ''),
																				(36, 'RECEIVE', -1, 1, ''),
																				(37, 'RECEIVE', -1, 1, ''),
																				(38, 'RECEIVE', -1, 1, ''),
																				(39, 'RECEIVE', -1, 1, ''),
																				(40, 'RECEIVE', -1, 1, ''),
																				(41, 'RECEIVE', -1, 1, ''),
																				(42, 'RECEIVE', -1, 1, ''),
																				(43, 'RECEIVE', -1, 1, ''),
																				(44, 'RECEIVE', -1, 1, ''),
																				(45, 'RECEIVE', -1, 1, ''),
																				(46, 'RECEIVE', -1, 1, ''),
																				(47, 'RECEIVE', -1, 1, ''),
																				(48, 'RECEIVE', -1, 1, ''),
																				(49, 'RECEIVE', -1, 1, ''),
																				(50, 'RECEIVE', -1, 1, ''),
																				(51, 'RECEIVE', -1, 1, ''),
																				(52, 'RECEIVE', -1, 1, ''),
																				(53, 'RECEIVE', -1, 1, ''),
																				(54, 'RECEIVE', -1, 1, ''),
																				(55, 'RECEIVE', -1, 1, ''),
																				(56, 'RECEIVE', -1, 1, ''),
																				(57, 'RECEIVE', -1, 1, ''),
																				(58, 'RECEIVE', -1, 1, ''),
																				(59, 'RECEIVE', -1, 1, ''),
																				(60, 'RECEIVE', -1, 1, ''),
																				(61, 'RECEIVE', -1, 1, ''),
																				(62, 'RECEIVE', -1, 1, ''),
																				(63, 'RECEIVE', -1, 1, ''),
																				(64, 'RECEIVE', -1, 1, ''),
																				(65, 'RECEIVE', -1, 1, ''),
																				(66, 'RECEIVE', -1, 1, ''),
																				(67, 'RECEIVE', -1, 1, ''),
																				(68, 'RECEIVE', -1, 1, ''),
																				(69, 'RECEIVE', -1, 1, ''),
																				(70, 'RECEIVE', -1, 1, ''),
																				(71, 'RECEIVE', -1, 1, ''),
																				(72, 'RECEIVE', -1, 1, ''),
																				(73, 'RECEIVE', -1, 1, ''),
																				(74, 'RECEIVE', -1, 1, ''),
																				(75, 'RECEIVE', -1, 1, ''),
																				(76, 'RECEIVE', -1, 1, ''),
																				(77, 'RECEIVE', -1, 1, ''),
																				(78, 'RECEIVE', -1, 1, ''),
																				(79, 'RECEIVE', -1, 1, ''),
																				(80, 'RECEIVE', -1, 1, ''),
																				(81, 'RECEIVE', -1, 1, ''),
																				(82, 'RECEIVE', -1, 1, ''),
																				(83, 'RECEIVE', -1, 1, ''),
																				(84, 'RECEIVE', -1, 1, ''),
																				(85, 'RECEIVE', -1, 1, ''),
																				(86, 'RECEIVE', -1, 1, ''),
																				(87, 'RECEIVE', -1, 1, ''),
																				(88, 'RECEIVE', -1, 1, ''),
																				(89, 'RECEIVE', -1, 1, ''),
																				(90, 'RECEIVE', -1, 1, ''),
																				(91, 'RECEIVE', -1, 1, ''),
																				(92, 'RECEIVE', -1, 1, ''),
																				(93, 'RECEIVE', -1, 1, ''),
																				(94, 'RECEIVE', -1, 1, ''),
																				(95, 'RECEIVE', -1, 1, ''),
																				(96, 'RECEIVE', -1, 1, ''),
																				(97, 'RECEIVE', -1, 1, ''),
																				(98, 'RECEIVE', -1, 1, ''),
																				(99, 'RECEIVE', -1, 1, ''),
																				(100, 'RECEIVE', -1, 1, ''),
																				(101, 'RECEIVE', -1, 1, ''),
																				(102, 'RECEIVE', -1, 1, ''),
																				(103, 'RECEIVE', -1, 1, ''),
																				(104, 'RECEIVE', -1, 1, ''),
																				(105, 'RECEIVE', -1, 1, ''),
																				(106, 'RECEIVE', -1, 1, ''),
																				(107, 'RECEIVE', -1, 1, ''),
																				(108, 'RECEIVE', -1, 1, ''),
																				(109, 'RECEIVE', -1, 1, '');"
														);

$add_new_tables["payment_methods_description"] = array ("structure" => "CREATE TABLE `payment_methods_description` (
																		  `payment_methods_id` int(11) NOT NULL default '0',
																		  `languages_id` int(11) NOT NULL default '1',
																		  `payment_methods_description_title` varchar(255) NOT NULL default '',
																		  PRIMARY KEY  (`payment_methods_id`, `languages_id`)
																		) TYPE=MyISAM;",
														"data" => "INSERT INTO `payment_methods_description` (`payment_methods_id`, `languages_id`, `payment_methods_description_title`) 
																	VALUES (12, 1, 'Offline Payment Methods'),
																	(13, 1, 'Direct Deposit/EFT'),
																	(14, 1, 'MoneyGram'),
																	(15, 1, 'Western Union'),
																	(16, 1, 'International Wire Transfer'),
																	(17, 1, 'WorldPay'),
																	(18, 1, 'American Express (via WorldPay)<br><i>Verification required</i>'),
																	(19, 1, 'egold'),
																	(20, 1, 'e-gold'),
																	(21, 1, 'cashU'),
																	(22, 1, 'cashU'),
																	(23, 1, 'WebMoney'),
																	(24, 1, 'WebMoney'),
																	(25, 1, 'PayPal'),
																	(26, 1, 'PayPal<br><i>Verification required</i>'),
																	(27, 1, 'iPay88'),
																	(28, 1, 'Credit Card'),
																	(29, 1, 'Maybank2U'),
																	(30, 1, 'Alliance Online Transfer'),
																	(31, 1, 'AmOnline'),
																	(32, 1, 'RHB (Netscape and IE users only)'),
																	(33, 1, 'Hong Leong Bank'),
																	(34, 1, 'FPX (Bank Islam Malaysia Berhad, Public Bank Berhad)'),
																	(35, 1, 'Mobile Money'),
																	(36, 1, 'Pospay'),
																	(37, 1, 'CIMB'),
																	(38, 1, 'Check/Money Order/Western Union'),
																	(39, 1, 'Check/Money Order/Western Union'),
																	(40, 1, 'Credit Card (MasterCard/Visa/AMEX/Discover)'),
																	(42, 1, 'Check/Money Order'),
																	(43, 1, 'Check/Money Order'),
																	(44, 1, 'moneybookers.com'),
																	(45, 1, 'Moneybookers Wallet<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(46, 1, 'Visa (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(47, 1, 'MasterCard (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(48, 1, 'Visa Electron (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(49, 1, 'American Express (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(50, 1, 'Diners (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(51, 1, 'JCB (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(52, 1, 'POLi (IE 6.0 and above users only, via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(53, 1, 'Bank Transfer (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(54, 1, 'Nordea Solo (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(55, 1, 'Carte Bancaire / Bleue Cheque (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(56, 1, 'Giropay (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(57, 1, 'Sofort&uuml;eberweisung (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(58, 1, 'Elektronisches Lastschriftverfahren \"ELV\" (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(59, 1, 'iDEAL (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(60, 1, 'Przelewy24 (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(61, 1, 'Maestro (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(62, 1, 'Solo (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(63, 1, 'eNETS (via Moneybookers)<br><i>One time verification by Moneybookers is required for new customers</i>'),
																	(64, 1, 'bibit'),
																	(65, 1, 'Diners Club (via Bibit)<br><i>Verification required</i>'),
																	(66, 1, 'MasterCard (via Bibit)<br><i>Verification required</i>'),
																	(67, 1, 'Visa / Visa Electron (via Bibit)<br><i>Verification required</i>'),
																	(68, 1, 'eNETS (via Bibit)'),
																	(69, 1, 'American Express (via Bibit)<br><i>Verification required</i>'),
																	(70, 1, 'Maestro (via Bibit)<br><i>Verification required</i>'),
																	(71, 1, 'Solo (via Bibit)<br><i>Verification required</i>'),
																	(72, 1, 'Regular Cheque - United Kingdom (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(73, 1, 'Regular Bank Transfer - United Kingdom (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(74, 1, 'Paybox (via Bibit)'),
																	(75, 1, 'Elba Raiffeisen Zentralbank (via Bibit)'),
																	(76, 1, 'Netpay Erste Bank (via Bibit)'),
																	(77, 1, 'Partner Online Paying Bank Austria (via Bibit)<br><i>Not fully automated and will require confirmation by our accounts department -- usually within 24 hours</i>'),
																	(78, 1, 'Regular Bank Transfer \"??berweisung Inland\" - Austria (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(79, 1, 'Regular Bank Transfer \"Bankoverschrijving\" - Belgium (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(80, 1, 'Regular Bank Transfer - Denmark (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(81, 1, 'Hansapank Bank Link (via Bibit)'),
																	(82, 1, '??hispank Pangalink (via Bibit)'),
																	(83, 1, 'Regular Bank Transfer \"Tilisiirto Giro\" - Finland (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(84, 1, 'Nordea Solo (via Bibit)'),
																	(85, 1, 'Carte Bancaire (via Bibit)<br><i>Verification required</i>'),
																	(86, 1, 'Carte Bleue / E-Carte Bleue (via Bibit)<br><i>Verification required</i>'),
																	(87, 1, 'Regular Bank Transfer \"Virement Bancaire\" - France (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(88, 1, 'Regular Cheque (via Bibit)<br><i>Not fully automated and will require confirmation by our accounts department -- usually within 24 hours</i>'),
																	(89, 1, 'Regular Bank Transfer \"??berweisung Inland\" - Germany (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(90, 1, 'Elektronisches Lastschriftverfahren \"ELV\" (via Bibit)<br><i>Verification required</i>'),
																	(91, 1, 'Commerzbank OnlineBankingweb (via Bibit)<br><i>Not fully automated and will require confirmation by our accounts department -- usually within 24 hours</i>'),
																	(92, 1, 'Deutsche Bank 24 (via Bibit)<br><i>Not fully automated and will require confirmation by our accounts department -- usually within 24 hours</i>'),
																	(93, 1, 'Dresdner Bank internetbanking (via Bibit)<br><i>Not fully automated and will require confirmation by our accounts department -- usually within 24 hours</i>'),
																	(94, 1, 'German Direct Debit (via Bibit)<br><i>Not fully automated and will require confirmation by our accounts department -- usually within 24 hours</i>'),
																	(95, 1, 'Regular Bank Transfer - Greece (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(96, 1, 'Laser Card (via Bibit)<br><i>Verification required</i>'),
																	(97, 1, 'Regular Bank Transfer \"Trasferimento bancario / Trasferimento Bonifico\" - Italy (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(98, 1, 'Regular Bank Transfer - Luxembourg (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(99, 1, 'Dutch Direct Debit (via Bibit)'),
																	(100, 1, 'iDEAL (via Bibit)'),
																	(101, 1, 'Regular Bank Transfer \"Overboeking\" - Netherlands (via bibit)<br><i>Clearance within 5 business days</i>'),
																	(102, 1, 'Domiciliaci&#243;n Bancaria (via Bibit)'),
																	(103, 1, 'Domiciliaci&#243;n Bancaria \"U\" (via Bibit)'),
																	(104, 1, 'Regular Bank Transfer \"Transferencia\" - Spain (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(105, 1, 'Regular Bank Transfer \"Inbetalning / Girering\" - Sweden (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(106, 1, 'Regular Bank Transfer - Switzerland (via Bibit)<br><i>Clearance within 5 business days</i>'),
																	(107, 1, 'Obsolete Bibit'),
																	(108, 1, 'Obsolete iPay88'),
																	(109, 1, 'Obsolete moneybookers.com'),
																	(41, 1, 'Credit Card (MasterCard/Visa/AMEX/Discover)');"
													);

$add_new_tables["payment_methods_instance"] = array ("structure" => "CREATE TABLE `payment_methods_instance` (
																	  `payment_methods_instance_id` int(11) NOT NULL auto_increment,
																	  `payment_methods_id` int(11) NOT NULL default '0',
																	  `currency_code` char(3) NOT NULL default '0',
																	  `payment_methods_instance_default` tinyint(1) NOT NULL default '0',
																	  `payment_methods_instance_follow_default` int(11) NOT NULL default '0',
																	  PRIMARY KEY  (`payment_methods_instance_id`)
																	) TYPE=MyISAM;",
														"data" => "INSERT INTO `payment_methods_instance` (`payment_methods_instance_id`, `payment_methods_id`, `currency_code`, `payment_methods_instance_default`, `payment_methods_instance_follow_default`) 
																	VALUES	(1, 17, 'SGD', 1, 0),
																	(2, 17, 'AUD', 0, 1),
																	(3, 17, 'CAD', 0, 1),
																	(4, 17, 'HKD', 0, 1),
																	(5, 17, 'JPY', 0, 1),
																	(6, 17, 'NZD', 0, 1),
																	(7, 17, 'PLN', 0, 1),
																	(8, 17, 'SEK', 0, 1),
																	(9, 17, 'CHF', 0, 1),
																	(10, 17, 'BND', 0, 1),
																	(11, 17, 'KWD', 0, 1),
																	(12, 17, 'SAR', 0, 1),
																	(13, 17, 'AED', 0, 1),
																	(14, 17, 'DKK', 0, 1),
																	(15, 17, 'NOK', 0, 1),
																	(16, 64, 'USD', 0, 0),
																	(17, 64, 'AED', 0, 22),
																	(18, 64, 'BND', 0, 22),
																	(19, 64, 'CHF', 0, 22),
																	(20, 64, 'CAD', 0, 22),
																	(21, 64, 'DKK', 0, 22),
																	(22, 64, 'EUR', 1, 0),
																	(23, 64, 'HKD', 0, 22),
																	(24, 64, 'GBP', 0, 0),
																	(25, 64, 'KWD', 0, 22),
																	(26, 64, 'MYR', 0, 0),
																	(27, 64, 'NOK', 0, 22),
																	(28, 64, 'NZD', 0, 22),
																	(29, 64, 'PLN', 0, 22),
																	(30, 64, 'SAR', 0, 22),
																	(31, 64, 'SEK', 0, 22),
																	(32, 64, 'SGD', 0, 0),
																	(33, 64, 'THB', 0, 22),
																	(34, 64, 'JPY', 0, 22),
																	(35, 64, 'AUD', 0, 22),
																	(36, 21, 'USD', 1, 0),
																	(37, 21, 'AED', 0, 36),
																	(38, 21, 'EGP', 0, 36),
																	(39, 21, 'EUR', 0, 36),
																	(40, 21, 'JOD', 0, 36),
																	(41, 21, 'SAR', 0, 36),
																	(42, 27, 'MYR', 1, 0),
																	(98, 69, 'GBP', 0, 96),
																	(97, 69, 'EUR', 0, 96),
																	(96, 69, 'USD', 1, 0),
																	(95, 25, 'CHF', 0, 0),
																	(94, 25, 'SEK', 0, 0),
																	(93, 25, 'SGD', 0, 0),
																	(92, 25, 'GBP', 0, 0),
																	(91, 25, 'PLN', 0, 0),
																	(90, 25, 'NOK', 0, 0),
																	(89, 25, 'NZD', 0, 0),
																	(101, 15, 'USD', 1, 0),
																	(88, 25, 'MXN', 0, 0),
																	(87, 25, 'JPY', 0, 0),
																	(86, 25, 'ILS', 0, 0),
																	(85, 25, 'HUF', 0, 0),
																	(84, 25, 'HKD', 0, 0),
																	(83, 25, 'EUR', 0, 0),
																	(82, 25, 'DKK', 0, 0),
																	(81, 25, 'CZK', 0, 0),
																	(80, 25, 'CAD', 0, 0),
																	(79, 25, 'USD', 1, 0),
																	(77, 23, 'USD', 1, 0),
																	(65, 12, 'USD', 1, 0),
																	(66, 12, 'AUD', 0, 65),
																	(67, 12, 'CAD', 0, 65),
																	(68, 12, 'EUR', 0, 65),
																	(69, 12, 'HKD', 0, 65),
																	(70, 12, 'JPY', 0, 65),
																	(71, 12, 'NZD', 0, 65),
																	(72, 12, 'GBP', 0, 65),
																	(73, 12, 'SGD', 0, 65),
																	(74, 12, 'CHF', 0, 65),
																	(75, 12, 'THB', 0, 65),
																	(99, 25, 'AUD', 0, 0),
																	(100, 13, 'MYR', 1, 0),
																	(102, 15, 'GBP', 0, 101),
																	(103, 15, 'EUR', 0, 101),
																	(104, 15, 'AUD', 0, 101),
																	(105, 15, 'CAD', 0, 101),
																	(106, 15, 'HKD', 0, 101),
																	(107, 15, 'JPY', 0, 101),
																	(108, 15, 'NZD', 0, 101),
																	(109, 15, 'SGD', 0, 101),
																	(110, 15, 'CHF', 0, 101),
																	(111, 15, 'THB', 0, 101),
																	(112, 14, 'USD', 1, 0),
																	(113, 14, 'MYR', 0, 112),
																	(114, 14, 'GBP', 0, 112),
																	(115, 14, 'EUR', 0, 112),
																	(116, 14, 'AUD', 0, 112),
																	(117, 14, 'CAD', 0, 112),
																	(118, 14, 'HKD', 0, 112),
																	(119, 14, 'JPY', 0, 112),
																	(120, 14, 'NZD', 0, 112),
																	(121, 14, 'SGD', 0, 112),
																	(122, 14, 'CHF', 0, 112),
																	(123, 14, 'THB', 0, 112),
																	(124, 16, 'USD', 1, 0),
																	(125, 16, 'GBP', 0, 124),
																	(126, 16, 'EUR', 0, 124),
																	(127, 16, 'AUD', 0, 124),
																	(128, 16, 'CAD', 0, 124),
																	(129, 16, 'HKD', 0, 124),
																	(130, 16, 'JPY', 0, 124),
																	(131, 16, 'NZD', 0, 124),
																	(132, 16, 'SGD', 0, 124),
																	(133, 16, 'CHF', 0, 124),
																	(134, 16, 'THB', 0, 124),
																	(135, 15, 'MYR', 0, 101),
																	(136, 19, 'USD', 1, 0),
																	(137, 19, 'CAD', 0, 136),
																	(138, 19, 'CHF', 0, 136),
																	(139, 19, 'GBP', 0, 136),
																	(140, 19, 'AUD', 0, 136),
																	(141, 19, 'JPY', 0, 136),
																	(142, 19, 'EUR', 0, 136),
																	(143, 44, 'USD', 1, 0),
																	(144, 44, 'AUD', 0, 143),
																	(145, 44, 'GBP', 0, 143),
																	(146, 44, 'CAD', 0, 143),
																	(147, 44, 'CZK', 0, 143),
																	(148, 44, 'DKK', 0, 143),
																	(149, 44, 'EUR', 0, 143),
																	(150, 44, 'HKD', 0, 143),
																	(151, 44, 'HUF', 0, 143),
																	(152, 44, 'ILS', 0, 143),
																	(153, 44, 'JPY', 0, 143),
																	(154, 44, 'MYR', 0, 143),
																	(155, 44, 'NZD', 0, 143),
																	(156, 44, 'NOK', 0, 143),
																	(157, 44, 'PLN', 0, 143),
																	(158, 44, 'SGD', 0, 143),
																	(160, 44, 'SEK', 0, 143),
																	(161, 44, 'CHF', 0, 143),
																	(162, 44, 'THB', 0, 143),
																	(163, 12, 'MYR', 0, 65);"
													);

$add_new_tables["payment_methods_instance_setting"] = array ("structure" => "CREATE TABLE `payment_methods_instance_setting` (
																			  `payment_methods_instance_setting_id` int(11) NOT NULL auto_increment,
																			  `payment_methods_instance_id` int(11) NOT NULL default '0',
																			  `payment_methods_instance_setting_key` varchar(255) NOT NULL default '1',
																			  `payment_methods_instance_setting_value` text NOT NULL,
																			  PRIMARY KEY  (`payment_methods_instance_setting_id`)
																			) TYPE=MyISAM;",
														"data" => "INSERT INTO `payment_methods_instance_setting` (`payment_methods_instance_setting_id`, `payment_methods_instance_id`, `payment_methods_instance_setting_key`, `payment_methods_instance_setting_value`) 
															VALUES	(1, 1, 'MODULE_PAYMENT_WORLDPAY_ID', '135792'),
															(2, 1, 'MODULE_PAYMENT_WORLDPAY_MD5KEY', '6X81N35h72WE'),
															(3, 1, 'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_ID', '150953'),
															(4, 1, 'MODULE_PAYMENT_WORLDPAY_REMOTE_ADMIN_PASSWORD', 'E98hffs42'),
															(5, 1, 'MODULE_PAYMENT_WORLDPAY_CALLBACK_PASSWORD', 'TaA68JvuazSB'),
															(6, 16, 'MODULE_PAYMENT_BIBIT_ADMIN_CODE', 'OFFGAMERS'),
															(7, 16, 'MODULE_PAYMENT_BIBIT_MERCHANT_CODE', 'OGHKUSD'),
															(8, 16, 'MODULE_PAYMENT_BIBIT_MERCHANT_PASSWORD', 'u#6xW)(Hfs\"1'),
															(9, 16, 'MODULE_PAYMENT_BIBIT_MAC_KEY', '1hIR5Al0Zt4j'),
															(10, 36, 'MODULE_PAYMENT_CASHU_ID', 'offgamers'),
															(11, 36, 'MODULE_PAYMENT_CASHU_SECRET_WORD', '9g19p9nu50'),
															(12, 42, 'MODULE_PAYMENT_IPAY88_MERCHANT_CODE', 'M00121'),
															(13, 42, 'MODULE_PAYMENT_IPAY88_MERCHANT_KEY', '4vVOx8gAaJ'),
															(31, 101, 'MODULE_PAYMENT_OFFLINE_INFO', '<SPAN class=\"messageStackError\">NOTE:Western Union payments will be collected by 1000 GMT+8 and processed by 1600 GMT+8 from Monday to Friday. Payments sent out between Friday 1000 GMT+8 and Monday 1000 GMT+8 will be cleared the following Monday except on Malaysia public holidays.</SPAN><br><br>\nOrders will be delivered as soon as the payment is cleared. Also note that the receiver''s details may change, always refer to the latest receiver''s details displayed below when making a payment to avoid unnecessary delays in order delivery.<br><br>\nThis order is only valid for 10 working days - If your payment does not clear in 10 working days, it will be canceled and you will need to place a new order with the store credit that we are going to issue you when your payment clears. This policy is in place to protect you and ourselves from price fluctuations.<br><br>We suggest you to check out using Malaysia Ringgit (MYR) and send Western Union in MYR to save on the currency conversions.<br><br>\n\n<b>Western Union Payment Instructions</b><br>\n1. Visit http://www.westernunion.com to locate the nearest agent.<br>2. From the agent, obtain a money transfer form. They will help you with the application. Here is our information:<br><br>\n&nbsp;&nbsp;&nbsp;&nbsp;Last Updated: 2008-11-17<br><br>\n&nbsp;&nbsp;&nbsp;&nbsp;Receiver''s First Name: Noraisham<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Receiver''s Last Name: MD Yusof<br> \n&nbsp;&nbsp;&nbsp;&nbsp;City: Petaling Jaya<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Country: Malaysia<br><br>\n\n3. When done, a Money Transfer Control Number (MTCN) will be provided. E-mail <EMAIL> the following <br><br>\n\n<b>*Reminder : Please fill in the Sender''s first name and last name exactly as you filled in on your Western Union payment. If the name does not match it will cause a delay in payment collection</b><br><br>\n&nbsp;&nbsp;&nbsp;&nbsp;Sender''s Full Name:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Sender''s Surname:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;City:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Country:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Money Transfer Control Number :<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Secret Question & Answer (Optional):<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Exact Total of Amount Sent (USD):<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Order Number:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Receiver''s First Name:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Receiver''s Last Name:'),
															(18, 77, 'MODULE_PAYMENT_WEBMONEY_ID', 'Z292525234791'),
															(16, 65, 'MODULE_PAYMENT_OFFLINE_INFO', ''),
															(19, 77, 'MODULE_PAYMENT_WEBMONEY_SECRET_WORD', 'di-D&!/5Z+'),
															(20, 79, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(21, 79, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(22, 79, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(23, 79, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(24, 79, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(25, 79, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(26, 96, 'MODULE_PAYMENT_BIBIT_ADMIN_CODE', 'Admin Code'),
															(27, 96, 'MODULE_PAYMENT_BIBIT_MERCHANT_CODE', 'Merchant Code'),
															(28, 96, 'MODULE_PAYMENT_BIBIT_MERCHANT_PASSWORD', 'Merchant Password'),
															(29, 96, 'MODULE_PAYMENT_BIBIT_MAC_KEY', 'Mac Key'),
															(30, 100, 'MODULE_PAYMENT_OFFLINE_INFO', 'Please direct deposit or EFT (Electronic Fund Transfer) the exact ordered amount to one of the following bank accounts.  Once you have made the payment, kindly e-mail <A href=\"mailto:<EMAIL>\"><EMAIL></A> your order number, the bank account that you have made the payment to, and the exact amount that you have paid.  Your order will be processed as soon as the fund is cleared. Please note that cash deposit and EFT may take up to two bank working days to clear and check may take up to four bank working days to clear.\n<br><br>\n<SPAN class=\"messageStackError\">NOTE: This order is only valid for 10 working days - If your payment doesn''t clear in 10 working days from the day you placed this order, your order will be canceled and you will be required to place a new order with the store credit that we are going to issue you when your payment clears. This policy is in place to protect you and ourselves from price fluctuations.\n</SPAN>\n<br><br>\n<U>Public Bank</U>\n<br><br>Account Name: OffGamers Sdn. Bhd.\n<br>Company Registration Number: 657868-X\n<br>Account Number: **********\n<br><br><a href=\"http://www.pbebank.com/en/branches/loc_landing.html\" target=\"blank\">Click here to locate a Public Bank branch near you for direct deposit</a>\n<br><br><br>\n<U>Maybank</U>\n<br><br>Account Name: OffGamers Sdn. Bhd.\n<br>Company Registration Number: 657868-X\n<br>Account Number: ************\n<br><br><a href=\"http://maybank.com.my/locate_us/branches/branches.shtml\" target=\"blank\">Click here to locate a Maybank branch near you for direct deposit</a>'),
															(32, 112, 'MODULE_PAYMENT_OFFLINE_INFO', '<SPAN class=\"messageStackError\">NOTE:MoneyGram payments will be collected by 1000 GMT+8 and processed by 1600 GMT+8 from Monday to Friday. Payments sent out between Friday 1000 GMT+8 and Monday 1000 GMT+8 will be cleared the following Monday except on Malaysia public holidays.\n</SPAN><br><br>\nOrders will be delivered as soon as the payment is cleared. Also note that the receiver''s details may change, always refer to the latest receiver''s details displayed below when making a payment to avoid unnecessary delays in order delivery.<br><br>\nThis order is only valid for 10 working days - If your payment does not clear in 10 working days, it will be canceled and you will need to place a new order with the store credit that we are going to issue you when your payment clears. This policy is in place to protect you and ourselves from price fluctuations.<br><br>We suggest you to check out using Malaysia Ringgit (MYR) and send Western Union in MYR to save on the currency conversions.<br><br>\n\n<b>Money Gram Payment Instructions</b><br>\n1. Visit http://www.moneygram.com to locate the nearest agent.<br>2. From the agent, obtain a money transfer form. They will help you with the application. Here is our information:<br><br>\n&nbsp;&nbsp;&nbsp;&nbsp;Last Updated: 2008-11-17<br><br>\n&nbsp;&nbsp;&nbsp;&nbsp;Receiver''s First Name: Noraisham<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Receiver''s Last Name: MD Yusof<br> \n&nbsp;&nbsp;&nbsp;&nbsp;City: Petaling Jaya<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Country: Malaysia<br><br>\n\n3. When done, a \"Reference Number\" will be provided. E-mail <EMAIL> the following <br><br>\n\n<b>*Reminder : Please fill in the Sender''s first name and last name exactly as you filled in on your Money Gram payment. If the name does not match it will cause a delay in payment collection</b><br><br>\n&nbsp;&nbsp;&nbsp;&nbsp;Sender''s Full Name:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Sender''s Surname:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;City:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Country:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Reference  Number :<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Secret Question & Answer (Optional):<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Exact Total of Amount Sent (USD):<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Order Number:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Receiver''s First Name:<br>\n&nbsp;&nbsp;&nbsp;&nbsp;Receiver''s Last Name:'),
															(33, 124, 'MODULE_PAYMENT_OFFLINE_INFO', '<SPAN class=\"messageStackError\">If your order amount is below US$200.00, please send an additional US$7.00 for our bank clearance fee.  Please select \"shared charges\" regardless of order amount when making wire transfer or the sender fee will be deducted accordingly. Any intermediary bank charges (US$0.00~US$25.00) for senders outside USA will be deducted as well. If you are uncertain about the intermediary bank charge, we recommend you to send additional amount.  Any additional amount not deducted by bank will be credited to your OffGamers store credit.</SPAN>  Once you have made the payment, kindly e-mail <A href=\"mailto:<EMAIL>\"><EMAIL></A> your order number and the exact amount that you have sent.  Your order will be processed as soon as the fund is cleared.  Please note that wire transfer may take up to three bank working days to clear.  DO NOT send money to any bank account other than the one stated below.  OffGamers is not liable for any money loss or delay in delivery due to money sent to incorrect bank account.\n<br><br>\nNOTE: This order is only valid for 10 working days - If your payment doesn''t clear in 10 working days from the day you placed this order, your order will be canceled and you will be required to place a new order with the store credit that we are going to issue you when your payment clears. This policy is in place to protect you and ourselves from price fluctuations.\n<br><br>\n<table border=\"0\">\n	<tr>\n		<td width=\"200\" class=\"paymentInfo\">Effective Date:</td><td class=\"paymentInfo\">2007-05-21 22:30 CDT</td>\n	</tr>\n	<tr><td class=\"paymentInfo\">&nbsp;</td></tr>\n	<tr>\n		<td class=\"paymentInfo\">Bank Name:</td><td class=\"paymentInfo\">HSBC Hong Kong</td>\n	</tr>\n	<tr><td class=\"paymentInfo\">&nbsp;</td></tr>\n	<tr>\n		<td valign=\"top\" class=\"paymentInfo\">Bank Address:</td><td class=\"paymentInfo\">1 Queen''s Road Central, Hong Kong.</td>\n	</tr>\n	<tr><td class=\"paymentInfo\">&nbsp;</td></tr>\n	<tr>\n		<td class=\"paymentInfo\">SWIFT Code:</td><td class=\"paymentInfo\">HSBCHKHHHKH</td>\n	</tr>\n	<tr><td class=\"paymentInfo\">&nbsp;</td></tr>\n	<tr>\n		<td class=\"paymentInfo\">Account Name:</td><td class=\"paymentInfo\">OffGamers Limited</td>\n	</tr>\n	<tr><td class=\"paymentInfo\">&nbsp;</td></tr>\n	<tr>\n		<td class=\"paymentInfo\">Account Number:</td><td class=\"paymentInfo\">************</td>\n	</tr>\n	<tr><td class=\"paymentInfo\">&nbsp;</td></tr>\n	<tr>\n		<td class=\"paymentInfo\">Message to Beneficiary/Recipient:</td><td class=\"paymentInfo\"><i>Kindly include your Order ID to expedite the delivery of the order.</i></td>\n	</tr>\n</table>'),
															(35, 136, 'MODULE_PAYMENT_EGOLD_ACCOUNT', 'E-Gold Account'),
															(36, 136, 'MODULE_PAYMENT_EGOLD_NAME', 'E-Gold Name'),
															(37, 136, 'MODULE_PAYMENT_EGOLD_SECRET_WORD', 'E-Gold Secret Word'),
															(38, 143, 'MODULE_PAYMENT_MONEYBOOKERS_EMAILID', '<EMAIL>'),
															(39, 143, 'MODULE_PAYMENT_MONEYBOOKERS_PWD', '4fb0b5cfc1ada22ebb8ba0bb3acc1a2b'),
															(40, 143, 'MODULE_PAYMENT_MONEYBOOKERS_REFID', '2570717'),
															(41, 143, 'MODULE_PAYMENT_MONEYBOOKERS_SECRET_WORD', '6o\"+s(jo87'),
															(260, 80, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(62, 22, 'MODULE_PAYMENT_BIBIT_ADMIN_CODE', 'OFFGAMERS'),
															(63, 22, 'MODULE_PAYMENT_BIBIT_MERCHANT_CODE', 'OGHKEUR'),
															(64, 22, 'MODULE_PAYMENT_BIBIT_MERCHANT_PASSWORD', 'u#6xW)(Hfs\"1'),
															(65, 22, 'MODULE_PAYMENT_BIBIT_MAC_KEY', '1hIR5Al0Zt4j'),
															(70, 24, 'MODULE_PAYMENT_BIBIT_ADMIN_CODE', 'OFFGAMERS'),
															(71, 24, 'MODULE_PAYMENT_BIBIT_MERCHANT_CODE', 'OGHKGBP'),
															(72, 24, 'MODULE_PAYMENT_BIBIT_MERCHANT_PASSWORD', 'u#6xW)(Hfs\"1'),
															(73, 24, 'MODULE_PAYMENT_BIBIT_MAC_KEY', '1hIR5Al0Zt4j'),
															(78, 26, 'MODULE_PAYMENT_BIBIT_ADMIN_CODE', 'OFFGAMERS'),
															(79, 26, 'MODULE_PAYMENT_BIBIT_MERCHANT_CODE', 'OGHKSGD'),
															(80, 26, 'MODULE_PAYMENT_BIBIT_MERCHANT_PASSWORD', 'u#6xW)(Hfs\"1'),
															(81, 26, 'MODULE_PAYMENT_BIBIT_MAC_KEY', '1hIR5Al0Zt4j'),
															(102, 32, 'MODULE_PAYMENT_BIBIT_ADMIN_CODE', 'OFFGAMERS'),
															(103, 32, 'MODULE_PAYMENT_BIBIT_MERCHANT_CODE', 'OGHKSGD'),
															(104, 32, 'MODULE_PAYMENT_BIBIT_MERCHANT_PASSWORD', 'u#6xW)(Hfs\"1'),
															(105, 32, 'MODULE_PAYMENT_BIBIT_MAC_KEY', '1hIR5Al0Zt4j'),
															(261, 80, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(262, 80, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(263, 80, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(264, 80, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(265, 80, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(266, 81, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(267, 81, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(268, 81, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(269, 81, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(270, 81, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(271, 81, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(272, 82, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(273, 82, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(274, 82, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(275, 82, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(276, 82, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(277, 82, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(278, 83, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(279, 83, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(280, 83, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(281, 83, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(282, 83, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(283, 83, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(284, 84, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(285, 84, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(286, 84, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(287, 84, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(288, 84, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(289, 84, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(290, 85, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(291, 85, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(292, 85, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(293, 85, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(294, 85, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(295, 85, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(296, 86, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(297, 86, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(298, 86, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(299, 86, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(300, 86, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(301, 86, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(302, 87, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(303, 87, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(304, 87, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(305, 87, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(306, 87, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(307, 87, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(308, 88, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(309, 88, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(310, 88, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(311, 88, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(312, 88, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(313, 88, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(314, 89, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(315, 89, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(316, 89, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(317, 89, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(318, 89, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(319, 89, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(320, 90, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(321, 90, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(322, 90, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(323, 90, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(324, 90, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(325, 90, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(326, 91, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(327, 91, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(328, 91, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(329, 91, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(330, 91, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(331, 91, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(332, 92, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(333, 92, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(334, 92, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(335, 92, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(336, 92, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(337, 92, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(338, 93, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(339, 93, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(340, 93, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(341, 93, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(342, 93, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(343, 93, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(344, 94, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(345, 94, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(346, 94, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(347, 94, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(348, 94, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(349, 94, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(350, 95, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(351, 95, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(352, 95, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(353, 95, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(354, 95, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(355, 95, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc'),
															(356, 99, 'MODULE_PAYMENT_PAYPAL_ID', '<EMAIL>'),
															(357, 99, 'MODULE_PAYMENT_PAYPAL_EWP_CERT_ID', '4CNKDFYB94456'),
															(358, 99, 'MODULE_PAYMENT_PAYPAL_BUSINESS_ID', '<EMAIL>'),
															(359, 99, 'MODULE_PAYMENT_PAYPAL_API_USERNAME', 'pp_api1.offgamers.com'),
															(360, 99, 'MODULE_PAYMENT_PAYPAL_API_PASSWORD', 'SRLP8PH8VPCEPVNB'),
															(361, 99, 'MODULE_PAYMENT_PAYPAL_API_SIGNATURE', 'Al6DakxON1BICQmiQTNXEms1zgjQA6Hn0NfSZcPLTbtdeotYMj7H7Nmc');"
													);

$add_new_tables["payment_methods_history"] = array ("structure" => "CREATE TABLE `payment_methods_history` (
																	  `payment_methods_id` int(11) NOT NULL default '0',
																	  `payment_methods_code` varchar(255) NOT NULL default '',
																	  `payment_methods_title` varchar(255) NOT NULL default '',
																	  `payment_methods_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  PRIMARY KEY  (`payment_methods_id`)
																	) TYPE=MyISAM;",
													"data" => ""
													);

$add_new_tables["payment_methods_types"] = array (	"structure" => "CREATE TABLE `payment_methods_types` (
																	  `payment_methods_types_id` int(11) NOT NULL auto_increment,
																	  `payment_methods_types_name` varchar(255) NOT NULL default '',
																	  `payment_methods_types_sort_order` int(5) NOT NULL default '50000',
																	  PRIMARY KEY  (`payment_methods_types_id`),
																	  UNIQUE KEY `payment_methods_types_name` (`payment_methods_types_name`)
																	) TYPE=MyISAM;",
													"data" => " INSERT INTO `payment_methods_types` (`payment_methods_types_id`, `payment_methods_types_name`, `payment_methods_types_sort_order`) 
																VALUES 	(1, 'CREDIT/DEBIT CARD', 100),
																		(2, 'E-WALLETS OR PREPAID CARD PAYMENTS', 200),
																		(3, 'ONLINE BANK TRANSFER (LOCAL)', 300),
																		(4, 'OFFLINE PAYMENTS (1 to 5 business days)', 400)
																		;"
													);


$add_new_tables["payment_methods_types_description"] = array (	"structure" => "CREATE TABLE `payment_methods_types_description` (
																				  `payment_methods_types_id` int(11) NOT NULL default '0',
																				  `languages_id` int(11) NOT NULL default '1',
																				  `payment_methods_types_description` varchar(255) NOT NULL default '',
																				  PRIMARY KEY  (`payment_methods_types_id`,`languages_id`)
																				) TYPE=MyISAM;",
																"data" => "INSERT INTO `payment_methods_types_description` (`payment_methods_types_id`, `languages_id`, `payment_methods_types_description`) 
																			VALUES 	(1, 1, 'CREDIT/DEBIT CARD'),
																					(2, 1, 'E-WALLETS OR PREPAID CARD PAYMENTS'),
																					(3, 1, 'ONLINE BANK TRANSFER (LOCAL)'),
																					(4, 1, 'OFFLINE PAYMENTS (1 to 5 business days)'),
																					(1, 2, 'CREDIT/DEBIT CARD'),
																					(2, 2, 'E-WALLETS OR PREPAID CARD PAYMENTS'),
																					(3, 2, 'ONLINE BANK TRANSFER (LOCAL)'),
																					(4, 2, 'OFFLINE PAYMENTS (1 to 5 business days)');"
															);

add_new_tables ($add_new_tables, $DBTables);
// End of create split gateway tables

// Insert new fields into payment_methods tables
$add_new_field = array();

$add_new_field['payment_methods'] = array (	array (	"field_name" => "payment_methods_title",
													"field_attr" => " varchar(255) NOT NULL default '' ",
													"add_after" => "payment_methods_code"
													),
											array (	"field_name" => "payment_methods_receive_status_mode",
													"field_attr" => " tinyint(11) NOT NULL default '0'",
													"add_after" => "payment_methods_receive_status"
													),
											array (	"field_name" => "payment_methods_types_id",
													"field_attr" => " int(11) NOT NULL default '0' ",
													"add_after" => ""
													),
											array (	"field_name" => "payment_methods_parent_id",
													"field_attr" => " int(11) NOT NULL default '0' ",
													"add_after" => ""
													),
											array (	"field_name" => "payment_methods_legend_color",
													"field_attr" => " varchar(7) NOT NULL default '' ",
													"add_after" => ""
													),
											array (	"field_name" => "payment_methods_filename",
													"field_attr" => " varchar(255) default NULL ",
													"add_after" => ""
													),
											array (	"field_name" => "payment_methods_logo",
													"field_attr" => " varchar(255) default NULL ",
													"add_after" => ""
													),
											array (	"field_name" => "date_added",
													"field_attr" => " datetime default '0000-00-00 00:00:00' ",
													"add_after" => ""
													),
											array (	"field_name" => "last_modified",
													"field_attr" => " datetime NOT NULL default '0000-00-00 00:00:00' ",
													"add_after" => ""
													)
										);

add_field($add_new_field);
// End of insert new fields into payment_methods tables

// Define payment_methods_id and currency_code as index key in payment_methods_instance table
add_index_key ('payment_methods_instance', 'index_payment_methods_id_and_code', 'index', 'payment_methods_id, currency_code', $DBTables);
// End of define payment_methods_id and currency_code as index key in payment_methods_instance table

// Insert new records into admin_files table (for Split Gateway page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='modules.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["payment_methods.php"] = array(	"insert" => " ('payment_methods.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
							   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='payment_methods.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Split Gateway page)

if (!in_array('payment_methods_parent_id', $existing_payment_methods_fields)) {
	// Update records in payment_methods table (Split Gateway)
	$payment_methods_insert_sql = array();
	
	$payment_methods_insert_sql["12"] = array(	"insert" => " (12, 'offline', 'Offline Payment Methods', 0, NULL, NULL, '', 0, 0, 1, 20, '', 1, 0, 0, '#99FF99', 'offline.php', '', '2008-11-20 15:53:37', '2008-12-09 13:25:45') " );
	$payment_methods_insert_sql["13"] = array(	"insert" => " (13, 'ddoeft', 'Direct Deposit/EFT', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 12, '', NULL, '13_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:00:12') " );
	$payment_methods_insert_sql["14"] = array(	"insert" => " (14, 'moneygram', 'MoneyGram', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 12, '', NULL, '14_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:00:32') " );
	$payment_methods_insert_sql["15"] = array(	"insert" => " (15, 'wu', 'Western Union', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 12, '', NULL, '15_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:00:41') " );
	$payment_methods_insert_sql["16"] = array(	"insert" => " (16, 'wiretransfer', 'Wire Transfer', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 12, '', NULL, '16_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:00:50') " );
	$payment_methods_insert_sql["17"] = array(	"insert" => " (17, 'worldpay', 'WorldPay', 0, NULL, NULL, '', 0, 0, 1, 4, '', 1, 0, 0, '#FFFF00', 'worldpay.php', '', '2008-11-20 15:59:19', '2008-11-20 15:59:42') " );
	$payment_methods_insert_sql["18"] = array(	"insert" => " (18, 'Amex', 'American Express - WorldPay', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 17, '', NULL, '18_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:01:56') " );
	$payment_methods_insert_sql["19"] = array(	"insert" => " (19, 'egold', 'egold', 0, NULL, NULL, '', 0, 0, 1, 20, '', 1, 0, 0, '#00AEC5', 'egold.php', '', '2008-11-20 16:00:29', '2008-11-20 16:00:56') " );
	$payment_methods_insert_sql["20"] = array(	"insert" => " (20, 'egold', 'egold', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 3, 19, '', NULL, '20_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:37:20') " );
	$payment_methods_insert_sql["21"] = array(	"insert" => " (21, 'cashU', 'cashU', 0, NULL, NULL, '', 0, 0, 1, 6, '', 1, 0, 0, '#6E920D', 'cashU.php', '21_logo.gif', '2008-11-20 16:01:46', '2008-11-28 15:55:27') " );
	$payment_methods_insert_sql["22"] = array(	"insert" => " (22, 'cashu', 'cashU', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 3, 21, '', NULL, '22_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:55:17') " );
	$payment_methods_insert_sql["23"] = array(	"insert" => " (23, 'webmoney', 'WebMoney', 0, NULL, NULL, '', 0, 0, 1, 40, '', 1, 0, 0, '#9966FF', 'webmoney.php', '23_logo.gif', '2008-11-20 16:02:37', '2008-11-28 16:01:37') " );
	$payment_methods_insert_sql["24"] = array(	"insert" => " (24, 'webmoney', 'WebMoney', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 23, '', NULL, '24_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:01:28') " );
	$payment_methods_insert_sql["25"] = array(	"insert" => " (25, 'paypal', 'PayPal', 0, NULL, NULL, '', 0, 0, 1, 1, '', 1, 0, 0, '#0066FF', 'paypal.php', '25_logo.gif', '2008-11-20 16:04:26', '2008-11-28 16:01:06') " );
	$payment_methods_insert_sql["26"] = array(	"insert" => " (26, 'PayPal', 'PayPal', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 3, 25, '', NULL, '26_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:01:16') " );
	$payment_methods_insert_sql["27"] = array(	"insert" => " (27, 'iPay88', 'iPay88', 0, NULL, NULL, '', 0, 0, 1, 30, '', 1, 0, 0, '#333333', 'iPay88.php', '27_logo.gif', '2008-11-20 16:06:12', '2008-11-28 16:02:27') " );
	$payment_methods_insert_sql["28"] = array(	"insert" => " (28, '2', 'Credit Card', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 0, 2, 27, '', NULL, NULL, '0000-00-00 00:00:00', '2008-11-28 16:09:31') " );
	$payment_methods_insert_sql["29"] = array(	"insert" => " (29, '6', 'Maybank2U', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 27, '', NULL, '29_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:32:45') " );
	$payment_methods_insert_sql["30"] = array(	"insert" => " (30, '8', 'Alliance Online Transfer', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 27, '', NULL, '30_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:30:34') " );
	$payment_methods_insert_sql["31"] = array(	"insert" => " (31, '10', 'AmOnline', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 27, '', NULL, '31_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:30:44') " );
	$payment_methods_insert_sql["32"] = array(	"insert" => " (32, '14', 'RHB (Netscape and IE users only)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 27, '', NULL, '32_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:33:06') " );
	$payment_methods_insert_sql["33"] = array(	"insert" => " (33, '15', 'Hong Leong Bank', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 27, '', NULL, '33_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:32:24') " );
	$payment_methods_insert_sql["34"] = array(	"insert" => " (34, '16', 'FPX (Bank Islam Malaysia Berhad, Public Bank Berhad)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 27, '', NULL, '34_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:31:23') " );
	$payment_methods_insert_sql["35"] = array(	"insert" => " (35, '17', 'Mobile Money', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 27, '', NULL, '35_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:03:37') " );
	$payment_methods_insert_sql["36"] = array(	"insert" => " (36, '18', 'Pospay', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 27, '', NULL, '36_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:03:48') " );
	$payment_methods_insert_sql["37"] = array(	"insert" => " (37, '20', 'CIMB', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 27, '', NULL, '37_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:30:52') " );
	$payment_methods_insert_sql["38"] = array(	"insert" => " (38, 'moneyorder', 'Check/Money Order/Western Union', 0, NULL, NULL, '', 0, 0, 1, 2, '', 1, 0, 0, '#FF0000', 'moneyorder.php', '38_logo.gif', '2008-11-20 16:10:06', '2008-11-28 15:55:54') " );
	$payment_methods_insert_sql["39"] = array(	"insert" => " (39, 'moneyorder', 'Check/Money Order/Western Union', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 38, '', NULL, '39_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:56:02') " );
	$payment_methods_insert_sql["40"] = array(	"insert" => " (40, 'pm2checkout', 'Credit Card (MasterCard/Visa/AMEX/Discover)', 0, NULL, NULL, '', 0, 0, 1, 3, '', 1, 0, 0, '#66FFCC', 'pm2checkout.php', '', '2008-11-20 16:11:41', '2008-11-20 16:11:51') " );
	$payment_methods_insert_sql["41"] = array(	"insert" => " (41, 'pm2checkout', 'Credit Card (MasterCard/Visa/AMEX/Discover)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 40, '', NULL, NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00') " );
	$payment_methods_insert_sql["42"] = array(	"insert" => " (42, 'moneyorderorcheck', 'Check/Money Order', 0, NULL, NULL, '', 0, 0, 1, 8, '', 1, 0, 0, '#660099', 'moneyorderorcheck.php', '', '2008-11-20 16:12:45', '2008-11-20 16:13:11') " );
	$payment_methods_insert_sql["43"] = array(	"insert" => " (43, 'moneyorderorcheck', 'Check/Money Order', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 42, '', NULL, NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00') " );
	$payment_methods_insert_sql["44"] = array(	"insert" => " (44, 'moneybookers', 'moneybookers.com', 0, NULL, NULL, '', 0, 0, 1, 5, '', 1, 0, 0, '#D5CBC4', 'moneybookers.php', '44_logo.gif', '2008-11-20 16:15:39', '2008-11-28 15:59:36') " );
	$payment_methods_insert_sql["45"] = array(	"insert" => " (45, 'WLT', 'Moneybookers Wallet', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '45_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:58:42') " );
	$payment_methods_insert_sql["46"] = array(	"insert" => " (46, 'VSA', 'Visa', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '46_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:59:12') " );
	$payment_methods_insert_sql["47"] = array(	"insert" => " (47, 'MSC', 'MasterCard', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '47_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:58:28') " );
	$payment_methods_insert_sql["48"] = array(	"insert" => " (48, 'VSE', 'Visa Electron', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '48_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:59:22') " );
	$payment_methods_insert_sql["49"] = array(	"insert" => " (49, 'AMX', 'American Express', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '49_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:56:40') " );
	$payment_methods_insert_sql["50"] = array(	"insert" => " (50, 'DIN', 'Diners', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '50_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:57:21') " );
	$payment_methods_insert_sql["51"] = array(	"insert" => " (51, 'JCB', 'JCB', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '51_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:58:07') " );
	$payment_methods_insert_sql["52"] = array(	"insert" => " (52, 'PLI', 'POLi', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '52_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:15:31') " );
	$payment_methods_insert_sql["53"] = array(	"insert" => " (53, 'BWI', 'Bank Transfer', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 44, '', NULL, '53_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:36:19') " );
	$payment_methods_insert_sql["54"] = array(	"insert" => " (54, 'SO2', 'Nordea Solo Payment', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '54_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:58:56') " );
	$payment_methods_insert_sql["55"] = array(	"insert" => " (55, 'GCB', 'Carte Bancaire / Bleue Cheque', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '55_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:57:02') " );
	$payment_methods_insert_sql["56"] = array(	"insert" => " (56, 'GIR', 'Giropay', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '56_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:12:31') " );
	$payment_methods_insert_sql["57"] = array(	"insert" => " (57, 'SFT', 'Sofort&uuml;eberweisung', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '57_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:11:24') " );
	$payment_methods_insert_sql["58"] = array(	"insert" => " (58, 'DID', 'Direct Debit Unsigned', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '58_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:32:02') " );
	$payment_methods_insert_sql["59"] = array(	"insert" => " (59, 'IDL', 'iDEAL', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '59_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:57:54') " );
	$payment_methods_insert_sql["60"] = array(	"insert" => " (60, 'PWY', 'Przelewy24', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '60_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:18:03') " );
	$payment_methods_insert_sql["61"] = array(	"insert" => " (61, 'MAE', 'Maestro', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '61_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:58:15') " );
	$payment_methods_insert_sql["62"] = array(	"insert" => " (62, 'SLO', 'Solo', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '62_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:17:24') " );
	$payment_methods_insert_sql["63"] = array(	"insert" => " (63, 'ENT', 'eNETS', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 44, '', NULL, '63_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:57:41') " );
	$payment_methods_insert_sql["64"] = array(	"insert" => " (64, 'bibit', 'bibit', 0, NULL, NULL, '', 0, 0, 1, 4, '', 1, 0, 0, '#7C298A', 'bibit.php', '64_logo.gif', '2008-11-20 16:35:26', '2008-12-04 21:37:34') " );
	$payment_methods_insert_sql["65"] = array(	"insert" => " (65, 'DINERS-SSL', 'Diner''s Club', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '65_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:50:03') " );
	$payment_methods_insert_sql["66"] = array(	"insert" => " (66, 'ECMC-SSL', 'MasterCard', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '66_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:52:11') " );
	$payment_methods_insert_sql["67"] = array(	"insert" => " (67, 'VISA-SSL', 'Visa / Visa Electron', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '67_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:54:56') " );
	$payment_methods_insert_sql["68"] = array(	"insert" => " (68, 'ENETS-SSL', 'eNETS Debit (Singapore)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '68_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:51:08') " );
	$payment_methods_insert_sql["69"] = array(	"insert" => " (69, 'AMEX-SSL', 'American Express', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '69_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:49:10') " );
	$payment_methods_insert_sql["70"] = array(	"insert" => " (70, 'MAESTRO-SSL', 'Maestro (UK)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '70_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:51:57') " );
	$payment_methods_insert_sql["71"] = array(	"insert" => " (71, 'SOLO_GB-SSL', 'Solo (UK)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '71_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:30:20') " );
	$payment_methods_insert_sql["72"] = array(	"insert" => " (72, 'CHEQUE_GB-BANK', 'Regular Cheque (UK)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '72_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:30:07') " );
	$payment_methods_insert_sql["73"] = array(	"insert" => " (73, 'TRANSFER_GB-BANK', 'Regular Bank Transfer (GBP)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '73_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:27:48') " );
	$payment_methods_insert_sql["74"] = array(	"insert" => " (74, 'PAYBOX-SSL', 'Paybox', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '74_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:52:52') " );
	$payment_methods_insert_sql["75"] = array(	"insert" => " (75, 'ELBA-SSL', 'ELBA Internet Payment', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '75_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:36:35') " );
	$payment_methods_insert_sql["76"] = array(	"insert" => " (76, 'NETPAY-SSL', 'Netpay', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '76_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:52:23') " );
	$payment_methods_insert_sql["77"] = array(	"insert" => " (77, 'POP-SSL', 'Partner Online Paying', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '77_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:52:42') " );
	$payment_methods_insert_sql["78"] = array(	"insert" => " (78, 'TRANSFER_AT-BANK', 'Regular Bank Transfer (Austria)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '78_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:24:57') " );
	$payment_methods_insert_sql["79"] = array(	"insert" => " (79, 'TRANSFER_BE-BANK', 'Regular Bank Transfer (Belgium)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '79_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:21:27') " );
	$payment_methods_insert_sql["80"] = array(	"insert" => " (80, 'TRANSFER_DK-BANK', 'Regular Bank Transfer (Denmark)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '80_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:25:37') " );
	$payment_methods_insert_sql["81"] = array(	"insert" => " (81, 'HANSABANK-SSL', 'Hansapank', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '81_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:10:17') " );
	$payment_methods_insert_sql["82"] = array(	"insert" => " (82, 'UHISBANK-SSL', '&Uuml;hispank', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '82_logo.gif', '0000-00-00 00:00:00', '2008-12-09 13:29:26') " );
	$payment_methods_insert_sql["83"] = array(	"insert" => " (83, 'TRANSFER_FI-BANK', 'Regular Bank Transfer (Finland)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '83_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:25:59') " );
	$payment_methods_insert_sql["84"] = array(	"insert" => " (84, 'SOLO-SSL', 'Nordea Bank', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '84_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:18:12') " );
	$payment_methods_insert_sql["85"] = array(	"insert" => " (85, 'CB-SSL', 'Carte Bancaire', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '85_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:49:21') " );
	$payment_methods_insert_sql["86"] = array(	"insert" => " (86, 'CARTEBLEUE-SSL', 'Carte Bleue / E-Carte Bleue', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '86_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:49:32') " );
	$payment_methods_insert_sql["87"] = array(	"insert" => " (87, 'TRANSFER_FR-BANK', 'Regular Bank Transfer (France)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '87_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:27:37') " );
	$payment_methods_insert_sql["88"] = array(	"insert" => " (88, 'CHEQUE-BANK', 'Regular Cheque', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '88_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:29:44') " );
	$payment_methods_insert_sql["89"] = array(	"insert" => " (89, 'TRANSFER_DE-BANK', 'Regular Bank Transfer (Germany)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '89_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:28:11') " );
	$payment_methods_insert_sql["90"] = array(	"insert" => " (90, 'ELV-SSL', 'German Electronic Direct Debit', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '90_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:34:44') " );
	$payment_methods_insert_sql["91"] = array(	"insert" => " (91, 'COMLINE-BANK', 'Commerzbank OnlineBankingweb', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '91_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:49:42') " );
	$payment_methods_insert_sql["92"] = array(	"insert" => " (92, 'DB24-BANK', 'DB24', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '92_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:16:27') " );
	$payment_methods_insert_sql["93"] = array(	"insert" => " (93, 'DRESDNER-BANK', 'Dresdner Bank internetbanking', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '93_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:50:24') " );
	$payment_methods_insert_sql["94"] = array(	"insert" => " (94, 'INCASSO_DE-FAX', 'Direct debit', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '94_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:33:49') " );
	$payment_methods_insert_sql["95"] = array(	"insert" => " (95, 'TRANSFER_GR-BANK', 'Regular Bank Transfer (Greece)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '95_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:28:24') " );
	$payment_methods_insert_sql["96"] = array(	"insert" => " (96, 'LASER-SSL', 'Laser Card', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '96_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:51:45') " );
	$payment_methods_insert_sql["97"] = array(	"insert" => " (97, 'TRANSFER_IT-BANK', 'Regular Bank Transfer (Italy)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '97_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:28:51') " );
	$payment_methods_insert_sql["98"] = array(	"insert" => " (98, 'TRANSFER_LU-BANK', 'Regular Bank Transfer (Luxembourg)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '98_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:28:36') " );
	$payment_methods_insert_sql["99"] = array(	"insert" => " (99, 'INCASSO_NL-FAX', 'Dutch Direct Debit', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '99_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:08:41') " );
	$payment_methods_insert_sql["100"] = array(	"insert" => " (100, 'IDEAL-SSL', 'iDEAL', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '100_logo.gif', '0000-00-00 00:00:00', '2008-11-28 15:51:32') " );
	$payment_methods_insert_sql["101"] = array(	"insert" => " (101, 'TRANSFER_NL-BANK', 'Regular Bank Transfer (Netherlands)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '101_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:29:04') " );
	$payment_methods_insert_sql["102"] = array(	"insert" => " (102, 'SINGLE_SIGNED_DD_ES-', 'Domiciliaci&#243;n Bancaria', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '102_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:35:23') " );
	$payment_methods_insert_sql["103"] = array(	"insert" => " (103, 'SINGLE_UNSIGNED_DD_E', 'Domiciliaci&#243;n Bancaria (U)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 2, 64, '', NULL, '103_logo.gif', '0000-00-00 00:00:00', '2008-12-05 13:35:41') " );
	$payment_methods_insert_sql["104"] = array(	"insert" => " (104, 'TRANSFER_ES-BANK', 'Regular Bank Transfer (Spain)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '104_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:29:15') " );
	$payment_methods_insert_sql["105"] = array(	"insert" => " (105, 'TRANSFER_SE-BANK', 'Regular Bank Transfer (Sweden)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '105_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:22:32') " );
	$payment_methods_insert_sql["106"] = array(	"insert" => " (106, 'TRANSFER_CH-BANK', 'Regular Bank Transfer (Switzerland)', 0, NULL, NULL, '', 0, 0, 1, 50000, '', 1, 1, 64, '', NULL, '106_logo.gif', '0000-00-00 00:00:00', '2008-11-28 16:29:31') " );
	$payment_methods_insert_sql["107"] = array(	"insert" => " (107, 'Obsolete Bibit', 'Obsolete Bibit', 0, NULL, NULL, '', 0, 0, 1, 1, '', 0, 2, 64, '', NULL, NULL, '0000-00-00 00:00:00', '2008-12-01 15:35:05') " );
	$payment_methods_insert_sql["108"] = array(	"insert" => " (108, 'Obsolete iPay88', 'Obsolete iPay88', 0, NULL, NULL, '', 0, 0, 1, 1, '', 0, 3, 27, '', NULL, NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00') " );
	$payment_methods_insert_sql["109"] = array(	"insert" => " (109, 'Obsolete moneybooker', 'Obsolete moneybookers.com', 0, NULL, NULL, '', 0, 0, 1, 1, '', 0, 3, 44, '', NULL, NULL, '0000-00-00 00:00:00', '0000-00-00 00:00:00') " );
	
	insert_new_records('payment_methods', "payment_methods_id", $payment_methods_insert_sql, $DBTables, "(payment_methods_id, payment_methods_code, payment_methods_title, payment_methods_send_status, payment_methods_send_mode_name, payment_methods_send_required_info, payment_methods_send_currency, payment_methods_estimated_receive_period, payment_methods_send_mass_payment, payment_methods_receive_status, payment_methods_sort_order, payment_methods_admin_groups_id, payment_methods_receive_status_mode, payment_methods_types_id, payment_methods_parent_id, payment_methods_legend_color, payment_methods_filename, payment_methods_logo, date_added, last_modified)", "");
	// End of update records in payment_methods table (Split Gateway)
}
?>