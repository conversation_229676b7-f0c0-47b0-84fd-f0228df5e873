<?
/*
	$Id: version_2_19_2.php,v 1.2 2008/12/12 12:53:30 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_orders_fields = get_table_fields("orders");

// Insert new fields into orders tables
$add_new_field = array();

$add_new_field['orders'] = array (	array (	"field_name" => "payment_methods_parent_id",
											"field_attr" => " int(11) NOT NULL default '0' ",
											"add_after" => "payment_method"
											),
									array (	"field_name" => "payment_methods_id",
											"field_attr" => " int(11) NOT NULL default '0' ",
											"add_after" => "payment_methods_parent_id"
											)
									);

add_field($add_new_field);
// End of insert new fields into orders tables

// Define payment_methods_parent_id as index key in orders table
add_index_key ('orders', 'index_payment_gateway_id', 'index', 'payment_methods_parent_id', $DBTables);
// End of define payment_methods_parent_id as index key in orders table

// Define payment_methods_id as index key in orders table
add_index_key ('orders', 'index_payment_methods_id', 'index', 'payment_methods_id', $DBTables);
// End of define payment_methods_id as index key in orders table

if (!in_array('payment_methods_id', $existing_orders_fields)) {
	// Update records in orders table (Split Gateway)
	$order_update_sql = array();
	
	$order_update_sql["Bibit"] = array("update" => " payment_methods_parent_id='64', payment_methods_id='107'" );
	$order_update_sql["cashU"] = array("update" => " payment_methods_parent_id='21', payment_methods_id='22'" );
	$order_update_sql["Check/Money Order"] = array("update" => " payment_methods_parent_id='42', payment_methods_id='43'" );
	$order_update_sql["Check/Money Order/Western Union"] = array("update" => " payment_methods_parent_id='38', payment_methods_id='39'" );
	$order_update_sql["Credit Card (MasterCard/Visa/AMEX/Discover)"] = array("update" => " payment_methods_parent_id='40', payment_methods_id='41'" );
	$order_update_sql["Direct Deposit/EFT"] = array("update" => " payment_methods_parent_id='12', payment_methods_id='13'" );
	$order_update_sql["e-gold"] = array("update" => " payment_methods_parent_id='19', payment_methods_id='20'" );
	$order_update_sql["iPay88"] = array("update" => " payment_methods_parent_id='27', payment_methods_id='108'" );
	$order_update_sql["Money Order or Check"] = array("update" => " payment_methods_parent_id='42', payment_methods_id='43'" );
	$order_update_sql["Moneybookers.com"] = array("update" => " payment_methods_parent_id='44', payment_methods_id='109'" );
	$order_update_sql["MoneyGram"] = array("update" => " payment_methods_parent_id='12', payment_methods_id='14'" );
	$order_update_sql["PayPal"] = array("update" => " payment_methods_parent_id='25', payment_methods_id='26'" );
	$order_update_sql["pm2checkout"] = array("update" => " payment_methods_parent_id='40', payment_methods_id='41'" );
	$order_update_sql["WebMoney"] = array("update" => " payment_methods_parent_id='23', payment_methods_id='24'" );
	$order_update_sql["Western Union"] = array("update" => " payment_methods_parent_id='12', payment_methods_id='15'" );
	$order_update_sql["Wire Transfers"] = array("update" => " payment_methods_parent_id='12', payment_methods_id='16'" );
	$order_update_sql["WorldPay"] = array("update" => " payment_methods_parent_id='17', payment_methods_id='18'" );
	
	update_records("orders", "payment_method", $order_update_sql, $DBTables);
	// End of update records in orders table (Split Gateway)
}

?>