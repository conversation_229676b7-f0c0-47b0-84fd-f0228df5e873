<?
/*
	$Id: version_2_19_4.php,v 1.5 2009/05/25 04:23:34 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_customers_info_fields = get_table_fields("customers_info");

// Update Store Country
$conf_update_sql = array();

$conf_update_sql["STORE_COUNTRY"] = array("update" => " configuration_value='25' " );
update_records("configuration", "configuration_key ", $conf_update_sql, $DBTables);
// End of update Store Country

// Change field structure for code in languages table
$change_field_structure = array();

$change_field_structure['languages'] = array (array ("field_name" => "code",
													"field_attr" => " char(5) NOT NULL default '' "
									 				),
									 		  array ("field_name" => "name",
													"field_attr" => " varchar(64) NOT NULL default '' "
									 				)
											);

change_field_structure ($change_field_structure);
// End of change field structure for code in languages table

// Insert Chinese Simplified, Chinese Traditional and Thai Languages
$sq_insert_sql = array();

$sq_insert_sql["2"] = array(	"insert" => " (2, '&#31616;&#20307;&#20013;&#25991;', 'zh-CN', 'icon.gif', 'cn_simplified', 10) ",
								"update" => " name='&#31616;&#20307;&#20013;&#25991;', code='zh-CN', image='icon.gif', directory='cn_simplified', sort_order ='10' " );
/*
$sq_insert_sql["3"] = array(	"insert" => " (3, '&#32321;&#20307;&#20013;&#25991;', 'zh-TW', 'icon.gif', 'cn_traditional', 20) ",
								"update" => " name='&#32321;&#20307;&#20013;&#25991;', code='zh-TW', image='icon.gif', directory='cn_traditional', sort_order ='20' " );

$sq_insert_sql["4"] = array(	"insert" => " (4, '&#3616;&#3634;&#3625;&#3634;&#3652;&#3607;&#3618;', 'th', 'icon.gif', 'thai', 30) ",
								"update" => " name='&#3616;&#3634;&#3625;&#3634;&#3652;&#3607;&#3618;', code='th', image='icon.gif', directory='thai', sort_order ='30' " );
*/
insert_new_records('languages', "languages_id ", $sq_insert_sql, $DBTables, "(languages_id, name, code, image, directory, sort_order )", "");
// End of insert Chinese Simplified, Chinese Traditional and Thai Languages

// Create zones_info, instant_message_accounts and instant_message_type tables
$add_new_tables = array();

$add_new_tables["zones_info"] = array (	"structure" => "CREATE TABLE `zones_info` (
															`geo_zone_id` int(11) NOT NULL default '0',
															`geo_zone_info` text,
															PRIMARY KEY (`geo_zone_id`)
														) TYPE=MYISAM;",
										"data" => "INSERT INTO `zones_info` (`geo_zone_id`, `geo_zone_info`) 
													VALUES 	(4, '{\"zone_categories_id\":[\"2298\",\"3020\",\"1910\",\"2522\",\"2299\",\"3423\",\"3424\",\"2843\",\"3019\",\"2852\",\"2851\",\"3259\",\"3260\",\"1802\",\"3364\",\"3365\",\"1877\",\"3155\",\"3156\",\"3867\",\"3381\",\"2812\",\"2811\",\"2817\",\"2816\",\"1694\",\"3220\",\"3404\",\"1808\",\"2880\",\"3491\",\"802\",\"3216\",\"1958\",\"3031\",\"2792\",\"2791\",\"3816\",\"1660\",\"3343\",\"1496\",\"3401\",\"3824\",\"3244\",\"3050\",\"1809\",\"3169\",\"3168\",\"3081\",\"2982\",\"3850\",\"2962\",\"2961\",\"3496\",\"3489\",\"2927\",\"3024\",\"1401\",\"3494\",\"3006\",\"3005\",\"1834\",\"3312\",\"3185\",\"2894\",\"3360\",\"2965\",\"2964\",\"3083\",\"3352\",\"3353\",\"3082\",\"2857\",\"3245\",\"2915\",\"2788\",\"2787\",\"3406\",\"2833\",\"2796\",\"1662\",\"3830\",\"3831\",\"3249\",\"3250\",\"3487\",\"3853\",\"3854\",\"3818\",\"3481\",\"3482\",\"2966\",\"3223\",\"2967\",\"3499\",\"3500\",\"3122\",\"3123\",\"3163\",\"3200\",\"3399\",\"3201\",\"2970\",\"2971\",\"3493\",\"2898\",\"3089\",\"3407\",\"2973\",\"3232\",\"1816\",\"2217\",\"3808\",\"2844\",\"2183\",\"3961\",\"3962\",\"3963\",\"2861\",\"3165\",\"1736\",\"1663\",\"3018\",\"2977\",\"3115\",\"2845\",\"3416\",\"3417\",\"3051\",\"3495\",\"3468\",\"2975\",\"3377\",\"2980\",\"2979\",\"3256\",\"2060\",\"3314\",\"3039\",\"3847\",\"2897\",\"3458\",\"2920\",\"3860\",\"3859\",\"3456\",\"3466\",\"3868\",\"2002\",\"2916\",\"2924\"],\"zone_sort_type\":\"define\",\"zone_sort_order\":[\"2522\",\"2299\",\"3423\",\"3424\",\"3019\",\"3259\",\"3260\",\"1877\",\"3155\",\"3156\",\"3381\",\"2811\",\"2812\",\"3220\",\"3491\",\"1958\",\"1660\",\"1496\",\"1809\",\"2927\",\"1401\",\"3312\",\"2965\",\"3083\",\"2857\",\"3245\",\"2915\",\"2916\",\"2788\",\"2787\",\"2833\",\"2796\",\"3249\",\"2966\",\"3223\",\"3200\",\"2898\",\"1736\",\"2845\",\"3860\",\"3868\",\"2843\",\"2852\",\"2851\",\"1802\",\"1694\",\"3404\",\"1808\",\"2880\",\"802\",\"3216\",\"3031\",\"3816\",\"3343\",\"3401\",\"3050\",\"3081\",\"2982\",\"3850\",\"3024\",\"1834\",\"3185\",\"2894\",\"3360\",\"3082\",\"3406\",\"1662\",\"3487\",\"3818\",\"3163\",\"3089\",\"3407\",\"3232\",\"1816\",\"2217\",\"3808\",\"2844\",\"2183\",\"2861\",\"3165\",\"1663\",\"3018\",\"3416\",\"3417\",\"3051\",\"3468\",\"2975\",\"3256\",\"2060\",\"3314\",\"3039\",\"3847\",\"2897\",\"1910\",\"2298\",\"3020\",\"3364\",\"3365\",\"2816\",\"2792\",\"2791\",\"3824\",\"3244\",\"3169\",\"3168\",\"2962\",\"2961\",\"3496\",\"3489\",\"3494\",\"3006\",\"3005\",\"2964\",\"3352\",\"3353\",\"3830\",\"3831\",\"3250\",\"3853\",\"3854\",\"3481\",\"3482\",\"2967\",\"3499\",\"3500\",\"3122\",\"3123\",\"3201\",\"3399\",\"2970\",\"2971\",\"3493\",\"2973\",\"3961\",\"3962\",\"3963\",\"3115\",\"2977\",\"3495\",\"3377\",\"2980\",\"2979\",\"3458\",\"2920\",\"3859\",\"3456\",\"3466\",\"3867\",\"2002\",\"2924\",\"2817\"]}'),
													(5, '{\"zone_categories_id\":[\"2298\",\"1910\",\"3020\",\"2522\",\"2299\",\"3423\",\"3424\",\"2843\",\"3019\",\"1802\",\"1877\",\"2927\",\"3381\",\"3458\",\"1694\",\"3220\",\"3404\",\"1808\",\"2880\",\"802\",\"3216\",\"1958\",\"3031\",\"3816\",\"2852\",\"3169\",\"3259\",\"2792\",\"3416\",\"2833\",\"3006\",\"3155\",\"2788\",\"3830\",\"3456\",\"3122\",\"3245\",\"1660\",\"3343\",\"1496\",\"3401\",\"3050\",\"1809\",\"3081\",\"2982\",\"3850\",\"3853\",\"3481\",\"3024\",\"1401\",\"1834\",\"3312\",\"3185\",\"3494\",\"2894\",\"3360\",\"3083\",\"3082\",\"3406\",\"2962\",\"3867\",\"3377\",\"3482\",\"2973\",\"2857\",\"3115\",\"2970\",\"3200\",\"3352\",\"2965\",\"3499\",\"3961\",\"3860\",\"2966\",\"1662\",\"3487\",\"3249\",\"3824\",\"3364\",\"3818\",\"3854\",\"3489\",\"3163\",\"2898\",\"3089\",\"3407\",\"3232\",\"1816\",\"2217\",\"3491\",\"3493\",\"3808\",\"2844\",\"2183\",\"3250\",\"3244\",\"3365\",\"2861\",\"3165\",\"1736\",\"3500\",\"3962\",\"3353\",\"3495\",\"3496\",\"1663\",\"3018\",\"2845\",\"2967\",\"2964\",\"3868\",\"3859\",\"2980\",\"2961\",\"2920\",\"2977\",\"2971\",\"3201\",\"3051\",\"3399\",\"3223\",\"3468\",\"2975\",\"2816\",\"3005\",\"3123\",\"2915\",\"2787\",\"3466\",\"3963\",\"3260\",\"2796\",\"2851\",\"3168\",\"3417\",\"3156\",\"2811\",\"2791\",\"3831\",\"2979\",\"3256\",\"2060\",\"3314\",\"3039\",\"3847\",\"2897\",\"2002\",\"2916\",\"2924\"],\"zone_sort_type\":\"default\",\"zone_sort_order\":[]}'),
													(6, '{\"zone_languages_id\":[\"en\",\"zh-CN\"],\"zone_default_languages_id\":\"en\"}'),
													(7, '{\"zone_languages_id\":[\"en\",\"zh-CN\"],\"zone_default_languages_id\":\"en\"}'),
													(8, '{\"zone_currency_id\":[\"MYR\",\"USD\"],\"zone_default_currency_id\":\"MYR\"}'),
													(9, '{\"zone_currency_id\":[\"USD\"],\"zone_default_currency_id\":\"USD\"}'),
													(10, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"30\",\"31\",\"37\",\"34\",\"33\",\"29\",\"35\",\"36\",\"32\",\"45\",\"13\",\"14\",\"15\",\"26\",\"24\"]}'),
													(11, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(157, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\"]}'),
													(12, '{\"zone_currency_id\":[\"AUD\",\"USD\"],\"zone_default_currency_id\":\"AUD\"}'),
													(13, '{\"zone_currency_id\":[\"BND\",\"USD\"],\"zone_default_currency_id\":\"BND\"}'),
													(14, '{\"zone_currency_id\":[\"CAD\",\"USD\"],\"zone_default_currency_id\":\"CAD\"}'),
													(15, '{\"zone_currency_id\":[\"EUR\",\"CHF\",\"USD\"],\"zone_default_currency_id\":\"CHF\"}'),
													(16, '{\"zone_currency_id\":[\"AUD\",\"USD\"],\"zone_default_currency_id\":\"AUD\"}'),
													(17, '{\"zone_currency_id\":[\"CZK\",\"EUR\",\"USD\"],\"zone_default_currency_id\":\"CZK\"}'),
													(18, '{\"zone_currency_id\":[\"DKK\",\"EUR\",\"USD\"],\"zone_default_currency_id\":\"DKK\"}'),
													(19, '{\"zone_currency_id\":[\"EGP\",\"USD\"],\"zone_default_currency_id\":\"EGP\"}'),
													(21, '{\"zone_currency_id\":[\"EUR\",\"GBP\",\"USD\"],\"zone_default_currency_id\":\"EUR\"}'),
													(22, '{\"zone_currency_id\":[\"EUR\",\"USD\"],\"zone_default_currency_id\":\"EUR\"}'),
													(23, '{\"zone_currency_id\":[\"EUR\",\"USD\"],\"zone_default_currency_id\":\"EUR\"}'),
													(24, '{\"zone_currency_id\":[\"EUR\",\"GBP\",\"USD\"],\"zone_default_currency_id\":\"GBP\"}'),
													(25, '{\"zone_currency_id\":[\"GBP\",\"USD\"],\"zone_default_currency_id\":\"GBP\"}'),
													(26, '{\"zone_currency_id\":[\"HKD\",\"USD\"],\"zone_default_currency_id\":\"HKD\"}'),
													(20, '{\"zone_currency_id\":[\"AED\",\"USD\"],\"zone_default_currency_id\":\"AED\"}'),
													(27, '{\"zone_currency_id\":[\"EUR\",\"HUF\",\"USD\"],\"zone_default_currency_id\":\"HUF\"}'),
													(28, '{\"zone_currency_id\":[\"ILS\",\"USD\"],\"zone_default_currency_id\":\"ILS\"}'),
													(29, '{\"zone_currency_id\":[\"JOD\",\"USD\"],\"zone_default_currency_id\":\"JOD\"}'),
													(30, '{\"zone_currency_id\":[\"JPY\",\"USD\"],\"zone_default_currency_id\":\"JPY\"}'),
													(31, '{\"zone_currency_id\":[\"MXN\",\"USD\"],\"zone_default_currency_id\":\"MXN\"}'),
													(32, '{\"zone_currency_id\":[\"EUR\",\"NOK\",\"USD\"],\"zone_default_currency_id\":\"NOK\"}'),
													(33, '{\"zone_currency_id\":[\"NZD\",\"USD\"],\"zone_default_currency_id\":\"NZD\"}'),
													(34, '{\"zone_currency_id\":[\"EUR\",\"PLN\",\"USD\"],\"zone_default_currency_id\":\"PLN\"}'),
													(35, '{\"zone_currency_id\":[\"USD\"],\"zone_default_currency_id\":\"USD\"}'),
													(36, '{\"zone_currency_id\":[\"SAR\",\"USD\"],\"zone_default_currency_id\":\"SAR\"}'),
													(37, '{\"zone_currency_id\":[\"EUR\",\"SEK\",\"USD\"],\"zone_default_currency_id\":\"SEK\"}'),
													(38, '{\"zone_currency_id\":[\"SGD\",\"USD\"],\"zone_default_currency_id\":\"SGD\"}'),
													(39, '{\"zone_currency_id\":[\"EUR\",\"USD\"],\"zone_default_currency_id\":\"EUR\"}'),
													(41, '{\"zone_currency_id\":[\"THB\",\"USD\"],\"zone_default_currency_id\":\"THB\"}'),
													(42, '{\"zone_currency_id\":[\"EUR\",\"USD\"],\"zone_default_currency_id\":\"USD\"}'),
													(43, '{\"zone_currency_id\":[\"SGD\",\"USD\"],\"zone_default_currency_id\":\"USD\"}'),
													(110, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(111, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(44, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"26\",\"24\"]}'),
													(45, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\"]}'),
													(46, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"26\",\"24\"]}'),
													(47, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"26\",\"24\"]}'),
													(48, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"52\",\"14\",\"15\",\"26\",\"24\",\"18\"]}'),
													(49, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"76\",\"77\",\"74\",\"67\",\"45\",\"14\",\"15\",\"26\",\"24\"]}'),
													(50, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"26\",\"24\"]}'),
													(51, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"26\",\"24\"]}'),
													(52, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"24\"]}'),
													(53, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"79\",\"67\",\"45\",\"14\",\"15\",\"26\",\"24\"]}'),
													(54, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"26\",\"24\"]}'),
													(55, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(56, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\"]}'),
													(57, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(58, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(59, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(60, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(61, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(62, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(63, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"24\",\"18\"]}'),
													(64, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"24\",\"18\"]}'),
													(65, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(66, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(67, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(68, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(69, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(70, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(71, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"80\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(72, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(73, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(74, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(75, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(76, '{\"zone_payment_gateway_id\":[\"82\",\"69\",\"65\",\"81\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(77, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(78, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"26\",\"24\",\"18\"]}'),
													(79, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"84\",\"83\",\"67\",\"45\",\"54\",\"14\",\"15\",\"16\",\"24\"]}'),
													(80, '{\"zone_payment_gateway_id\":[\"69\",\"85\",\"86\",\"65\",\"66\",\"87\",\"88\",\"67\",\"22\",\"55\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(81, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(82, '{\"zone_payment_gateway_id\":[\"69\",\"91\",\"92\",\"65\",\"94\",\"93\",\"90\",\"66\",\"89\",\"88\",\"67\",\"22\",\"56\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(83, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"14\",\"15\",\"16\",\"24\"]}'),
													(84, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(85, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"95\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(86, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"24\",\"18\"]}'),
													(87, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(88, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"24\",\"18\"]}'),
													(89, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"18\"]}'),
													(90, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(91, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(92, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(93, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(94, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"24\"]}'),
													(95, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(96, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"96\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(97, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(98, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"97\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(99, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(100, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(101, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(102, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(103, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(104, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(105, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(106, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"106\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(107, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(108, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(109, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"98\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(112, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(139, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"68\",\"66\",\"67\",\"63\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(113, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(114, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(115, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(116, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(117, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"100\",\"66\",\"101\",\"67\",\"59\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(118, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(119, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(120, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(121, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(122, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(123, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(124, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(125, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(126, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(127, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(128, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"26\",\"24\",\"18\"]}'),
													(129, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"60\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(130, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(131, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(132, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(133, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(134, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(135, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(136, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"26\",\"24\"]}'),
													(137, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(138, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"26\",\"24\"]}'),
													(140, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(141, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(142, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(143, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"102\",\"103\",\"66\",\"104\",\"88\",\"67\",\"22\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(144, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"84\",\"105\",\"67\",\"22\",\"45\",\"54\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(145, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(146, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"24\"]}'),
													(147, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(148, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(149, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"24\"]}'),
													(150, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"24\",\"18\"]}'),
													(151, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(152, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(153, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(154, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(155, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}'),
													(156, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"70\",\"66\",\"71\",\"67\",\"22\",\"61\",\"45\",\"62\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(158, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(159, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"26\"]}'),
													(160, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(161, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\",\"26\",\"24\"]}'),
													(162, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"26\",\"24\"]}'),
													(163, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"15\",\"16\"]}'),
													(164, '{\"zone_payment_gateway_id\":[\"69\",\"65\",\"66\",\"67\",\"45\",\"14\",\"16\",\"26\",\"24\"]}'),
													(165, '{\"zone_currency_id\":[\"AUD\",\"BND\",\"CAD\",\"CZK\",\"DKK\",\"EGP\",\"EUR\",\"HKD\",\"HUF\",\"ILS\",\"JPY\",\"JOD\",\"KWD\",\"MYR\",\"MXN\",\"NZD\",\"NOK\",\"PLN\",\"GBP\",\"SAR\",\"SGD\",\"SEK\",\"CHF\",\"THB\",\"AED\",\"USD\"],\"zone_default_currency_id\":\"USD\"}'),
													(166, '{\"zone_payment_gateway_id\":[\"69\",\"82\",\"85\",\"86\",\"91\",\"92\",\"65\",\"103\",\"102\",\"93\",\"99\",\"75\",\"90\",\"68\",\"94\",\"81\",\"100\",\"96\",\"70\",\"66\",\"76\",\"84\",\"77\",\"74\",\"78\",\"89\",\"79\",\"105\",\"101\",\"83\",\"104\",\"97\",\"87\",\"80\",\"95\",\"98\",\"106\",\"73\",\"88\",\"72\",\"71\",\"67\",\"22\",\"30\",\"31\",\"37\",\"34\",\"33\",\"29\",\"35\",\"36\",\"32\",\"45\",\"13\",\"14\",\"15\",\"16\",\"26\",\"24\",\"18\"]}');"
										);

$add_new_tables["instant_message_accounts"] = array (	"structure" => "CREATE TABLE `instant_message_accounts` (
																			`instant_message_accounts_id` int(11) NOT NULL AUTO_INCREMENT ,
																			`customer_id` int(11) NOT NULL default '0',
																			`instant_message_type_id` int(11) NOT NULL ,
																			`instant_message_userid` varchar(96) NOT NULL ,
																			`instant_message_remarks` varchar(96) DEFAULT NULL ,
																			PRIMARY KEY (`instant_message_accounts_id`),
																			KEY `index_customer_id` (`customer_id`)
																		) TYPE=MYISAM;",
														"data" => ""
													);

$add_new_tables["instant_message_type"] = array (	"structure" => "CREATE TABLE `instant_message_type` (
																		`instant_message_type_id` int(11) NOT NULL AUTO_INCREMENT,
																		`instant_message_type_name` varchar(96) NOT NULL default '',
																		`instant_message_type_description` varchar(255) NOT NULL default '',
																		`instant_message_type_order` int(11) NOT NULL default '0',
																		PRIMARY KEY (`instant_message_type_id`)
																	) TYPE=MYISAM;",
													"data" => "	INSERT INTO `instant_message_type` (`instant_message_type_id`, `instant_message_type_name`, `instant_message_type_description`, `instant_message_type_order`) 
																VALUES (1, 'AOL Messenger', 'AOL Instant Messenger', 1),
																		(2, 'MSN Messenger', 'MSN Instant Messenger', 2),
																		(3, 'Yahoo Messenger', 'Yahoo Instant Messenger', 3),
																		(4, 'Goggle Talk', 'Goggle Talk Instant Messenger', 4),
																		(5, 'QQ', 'QQ Instant Messenger', 5),
																		(6, 'Skype', 'Skype', 6),
																		(7, 'ICQ', 'I See U', 7);"
													);

$add_new_tables["categories_structures"] = array (	"structure" => "CREATE TABLE `categories_structures` (
																		`categories_structures_key` varchar(64) NOT NULL default '',
																		`categories_structures_value` text,
																		PRIMARY KEY (`categories_structures_key`)
																	) TYPE=MYISAM;",
													"data" => "INSERT INTO `categories_structures` (`categories_structures_key`, `categories_structures_value`) 
																VALUES ('games', '2298,1910,3020,2522,2299,2920,3458,3423,3424,2843,3019,2852,2851,3260,3259,1802,3365,3364,1877,3155,3160,3156,3381,2812,2811,2817,2816,1694,3220,3404,1808,2880,3491,802,3216,1958,3031,2792,2791,3816,1660,3343,1496,3401,3824,3244,3050,1809,3169,3168,3081,2982,3850,2962,2961,3496,3489,2927,3024,1401,3494,3006,3005,1834,3312,3185,2894,3360,2965,2964,3083,3353,3352,3082,2857,3245,2915,2916,2788,2787,3406,2833,2796,1662,3831,3830,3250,3249,3487,3854,3853,3818,3482,3481,2967,3223,2966,3500,3499,3123,3122,3163,3201,3399,3200,2971,2970,3493,2898,3089,3407,2928,2924,2973,3232,1816,2217,3808,2844,2183,3962,3963,3961,2861,3165,1736,1663,3018,2977,3115,2976,2845,3417,3416,3051,3495,3468,2975,2980,3377,2979,3256,2060,3314,3039,3847,2897,3860,3859,3466,3456,3868,3867,2002,1684,2867,3227,3017,3218,2869,3016,1667,2893,2865,2868,3015,3014');"
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create zones_info, instant_message_accounts and instant_message_type tables

// Insert new fields into country, customers_info and geo_zones tables
$add_new_field = array();

$add_new_field['countries'] = array (	array (	"field_name" => "countries_website_domain",
												"field_attr" => " varchar(64) NOT NULL default '' ",
												"add_after" => "countries_international_dialing_code"
												)
									);

$add_new_field['customers_info'] = array (	array (	"field_name" => "customer_info_selected_country",
													"field_attr" => " int(11) NOT NULL default '0' ",
													"add_after" => ""
													)
										);

$add_new_field['geo_zones'] = array (	array (	"field_name" => "geo_zone_type",
												"field_attr" => " tinyint(1) UNSIGNED NOT NULL default '0' ",
												"add_after" => "geo_zone_description"
												)
									);

add_field($add_new_field);
// End of insert new fields into country, customers_info and geo_zones tables

// Insert secret question for English, Chinese Traditional and Thai Version
$sq_insert_sql = array();
$sq_insert_sql["1"] = array(	"insert" => " ('1', 1, 'What is the last name of your favorite musician?') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='1' AND language_id=1 ");

$sq_insert_sql = array();
$sq_insert_sql["2"] = array(	"insert" => " ('2', 1, 'What was the last name of your favorite teacher?') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='2' AND language_id=1 ");

$sq_insert_sql = array();
$sq_insert_sql["3"] = array(	"insert" => " ('3', 1, 'What was the last name of your best childhood friend?') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='3' AND language_id=1 ");

$sq_insert_sql = array();
$sq_insert_sql["4"] = array(	"insert" => " ('4', 1, 'What is the name of the hospital where you were born?') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='4' AND language_id=1 ");

$sq_insert_sql = array();
$sq_insert_sql["5"] = array(	"insert" => " ('5', 1, 'What is your main frequent flier number?') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='5' AND language_id=1 ");

$sq_insert_sql = array();
$sq_insert_sql["6"] = array(	"insert" => " ('6', 1, 'What is the name of the street on which you grew up?') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='6' AND language_id=1 ");

$sq_insert_sql = array();
$sq_insert_sql["7"] = array(	"insert" => " ('7', 1, 'What is the name of your favorite book?') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='7' AND language_id=1 ");

$sq_insert_sql = array();
$sq_insert_sql["8"] = array(	"insert" => " ('8', 1, 'Who is your favorite author?') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='8' AND language_id=1 ");

$sq_insert_sql = array();
$sq_insert_sql["9"] = array(	"insert" => " ('9', 1, 'Where did you spend your childhood summers?') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='9' AND language_id=1 ");
// End of insert secret question for English, Chinese Traditional and Thai Version

// Update records in countries table (assign countries_website_domain)
/*
$country_update_sql = array();

$country_update_sql["129"] = array("update" => " countries_website_domain ='my.offgamers.com' " );
$country_update_sql["209"] = array("update" => " countries_website_domain ='th.offgamers.com' " );
$country_update_sql["188"] = array("update" => " countries_website_domain ='sg.offgamers.com' " );

update_records('countries', "countries_id ", $country_update_sql, $DBTables);
*/
// End of update records in countries table (assign countries_website_domain)

// Insert new records into admin_files table (for Zone Info page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='localization.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["zones_configuration.php"] = array(	"insert" => " ('zones_configuration.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
										   						);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='zones_configuration.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Zone Info page)

// Insert new records into admin_files table (for Game Structure page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='localization.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["categories_structures.php"] = array(	"insert" => " ('categories_structures.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
										   						);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='categories_structures.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Game Structure page)

// Ask Q&A for existing customer
$q_n_a_update_sql = "	UPDATE customers 
						SET customers_security_start_time = '2008-11-16'
						WHERE FIND_IN_SET('0', customers_login_sites) 
							AND customers_security_start_time IS NULL ";
tep_db_query($q_n_a_update_sql);
// End of Ask Q&A for existing customer


tep_db_query("TRUNCATE TABLE `geo_zones`");

tep_db_query("INSERT INTO `geo_zones` (`geo_zone_id`, `geo_zone_name`, `geo_zone_description`, `geo_zone_type`, `last_modified`, `date_added`) VALUES
				(4, 'Default - Non Localized', 'Default - Non Localized', 1, '2008-12-18 07:09:49', '2008-12-12 21:53:01'),
				(6, 'MALAYSIA', 'MALAYSIA', 2, '2008-12-18 12:00:20', '2008-12-12 21:58:14'),
				(7, 'Default - Non Localized', 'Default - Non Localized', 2, '2008-12-18 12:00:05', '2008-12-12 21:58:23'),
				(8, 'MYR & USD', 'MYR & USD', 3, '2008-12-15 17:44:00', '2008-12-12 22:00:18'),
				(9, 'USD', 'USD', 3, '2008-12-15 18:43:08', '2008-12-12 22:00:40'),
				(10, 'MALAYSIA', 'MALAYSIA', 4, '2008-12-17 01:21:16', '2008-12-12 22:04:29'),
				(11, 'UNITED STATES', 'UNITED STATES', 4, '2008-12-17 02:43:52', '2008-12-12 22:04:51'),
				(13, 'BND', 'BND', 3, '2008-12-15 15:44:52', '2008-12-15 02:31:46'),
				(14, 'CAD', 'CAD', 3, '2008-12-15 15:45:58', '2008-12-15 02:32:50'),
				(15, 'CHF & EUR', 'CHF & EUR', 3, '2008-12-15 15:47:33', '2008-12-15 02:33:46'),
				(16, 'AUD & USD', 'AUD & USD', 3, '2008-12-15 15:38:23', '2008-12-15 02:35:17'),
				(17, 'CZK & EUR', 'CZK & EUR', 3, '2008-12-15 15:49:31', '2008-12-15 02:36:18'),
				(18, 'DKK & EUR', 'DKK & EUR', 3, '2008-12-15 15:50:42', '2008-12-15 02:37:27'),
				(19, 'EGP & USD', 'EGP & USD', 3, '2008-12-15 15:52:53', '2008-12-15 02:38:26'),
				(20, 'AED & USD', 'AED & USD', 3, NULL, '2008-12-15 15:36:37'),
				(21, 'EUR & GBP', 'EUR & GBP', 3, NULL, '2008-12-15 15:54:03'),
				(22, 'EUR & USD', 'EUR & USD', 3, NULL, '2008-12-15 15:57:12'),
				(23, 'EUR', 'EUR', 3, NULL, '2008-12-15 16:02:02'),
				(24, 'GBP & EUR', 'GBP & EUR', 3, NULL, '2008-12-15 16:22:52'),
				(25, 'GBP & USD', 'GBP & USD', 3, NULL, '2008-12-15 16:23:59'),
				(26, 'HKD & USD', 'HKD & USD', 3, NULL, '2008-12-15 16:27:55'),
				(27, 'HUF & EUR', 'HUF & EUR', 3, NULL, '2008-12-15 17:11:05'),
				(28, 'ILS & USD', 'ILS & USD', 3, NULL, '2008-12-15 17:17:45'),
				(29, 'JOD & USD', 'JOD & USD', 3, NULL, '2008-12-15 17:23:32'),
				(30, 'JPY & USD', 'JPY & USD', 3, NULL, '2008-12-15 17:28:31'),
				(31, 'MXN & USD', 'MXN & USD', 3, NULL, '2008-12-15 17:29:25'),
				(32, 'NOK & EUR', 'NOK & EUR', 3, NULL, '2008-12-15 17:47:30'),
				(33, 'NZD', 'NZD', 3, NULL, '2008-12-15 17:48:47'),
				(34, 'PLZ & EUR', 'PLZ & EUR', 3, NULL, '2008-12-15 17:50:35'),
				(35, 'RMB & USD', 'RMB & USD', 3, NULL, '2008-12-15 17:52:00'),
				(36, 'SAR & USD', 'SAR & USD', 3, NULL, '2008-12-15 17:56:43'),
				(37, 'SEK & EUR', 'SEK & EUR', 3, NULL, '2008-12-15 17:59:23'),
				(38, 'SGD', 'SGD', 3, NULL, '2008-12-15 18:04:37'),
				(39, 'SKK & EUR', 'SKK & EUR', 3, NULL, '2008-12-15 18:06:48'),
				(41, 'THB & USD', 'THB & USD', 3, NULL, '2008-12-15 18:15:19'),
				(42, 'USD & EUR', 'USD & EUR', 3, NULL, '2008-12-15 18:18:47'),
				(43, 'USD & SGD', 'USD & SGD', 3, NULL, '2008-12-15 18:21:30'),
				(44, 'ALGERIA', 'ALGERIA', 4, NULL, '2008-12-16 10:24:04'),
				(45, 'AMERICAN SAMOA', 'AMERICAN SAMOA', 4, NULL, '2008-12-16 12:15:57'),
				(46, 'ANDORRA', 'ANDORRA', 4, NULL, '2008-12-16 12:18:23'),
				(47, 'ARGENTINA', 'ARGENTINA', 4, NULL, '2008-12-16 12:26:01'),
				(48, 'AUSTRALIA', 'AUSTRALIA', 4, NULL, '2008-12-16 14:47:49'),
				(49, 'AUSTRIA', 'AUSTRIA', 4, NULL, '2008-12-16 14:58:57'),
				(50, 'BAHAMAS', 'BAHAMAS', 4, NULL, '2008-12-16 15:48:49'),
				(51, 'BAHRAIN', 'BAHRAIN', 4, NULL, '2008-12-16 15:56:44'),
				(52, 'BELARUS', 'BELARUS', 4, NULL, '2008-12-16 15:59:59'),
				(53, 'BELGIUM', 'BELGIUM', 4, NULL, '2008-12-16 16:02:23'),
				(54, 'BOLIVIA', 'BOLIVIA', 4, NULL, '2008-12-16 16:09:23'),
				(55, 'BRAZIL', 'BRAZIL', 4, NULL, '2008-12-16 17:06:59'),
				(56, 'BRITISH INDIAN OCEAN TERRITORY', 'BRITISH INDIAN OCEAN TERRITORY', 4, NULL, '2008-12-16 17:10:43'),
				(57, 'BRUNEI DARUSSALAM', 'BRUNEI DARUSSALAM', 4, NULL, '2008-12-16 17:12:14'),
				(58, 'BULGARIA', 'BULGARIA', 4, NULL, '2008-12-16 17:14:32'),
				(59, 'CANADA', 'CANADA', 4, NULL, '2008-12-16 17:15:00'),
				(60, 'CAYMAN ISLANDS', 'CAYMAN ISLANDS', 4, NULL, '2008-12-17 00:06:31'),
				(61, 'CHILE', 'CHILE', 4, NULL, '2008-12-17 00:07:02'),
				(62, 'CHINA', 'CHINA', 4, NULL, '2008-12-17 00:09:36'),
				(63, 'CHRISTMAS ISLAND', 'CHRISTMAS ISLAND', 4, NULL, '2008-12-17 00:12:05'),
				(64, 'COCOS (KEELING) ISLANDS', 'COCOS (KEELING) ISLANDS', 4, NULL, '2008-12-17 00:15:01'),
				(65, 'COLOMBIA', 'COLOMBIA', 4, NULL, '2008-12-17 00:15:40'),
				(66, 'COOK ISLANDS', 'COOK ISLANDS', 4, NULL, '2008-12-17 00:16:28'),
				(67, 'COSTA RICA', 'COSTA RICA', 4, NULL, '2008-12-17 00:16:43'),
				(68, 'CROATIA (local name: Hrvatska)', 'CROATIA (local name: Hrvatska)', 4, NULL, '2008-12-17 00:31:29'),
				(69, 'CYPRUS', 'CYPRUS', 4, NULL, '2008-12-17 00:33:13'),
				(70, 'CZECH REPUBLIC', 'CZECH REPUBLIC', 4, NULL, '2008-12-17 00:33:51'),
				(71, 'DENMARK', 'DENMARK', 4, NULL, '2008-12-17 00:34:50'),
				(72, 'DOMINICAN REPUBLIC', 'DOMINICAN REPUBLIC', 4, NULL, '2008-12-17 00:35:26'),
				(73, 'ECUADOR', 'ECUADOR', 4, NULL, '2008-12-17 00:35:55'),
				(74, 'EGYPT', 'EGYPT', 4, NULL, '2008-12-17 00:36:35'),
				(75, 'EL SALVADOR', 'EL SALVADOR', 4, NULL, '2008-12-17 00:47:19'),
				(76, 'ESTONIA', 'ESTONIA', 4, NULL, '2008-12-17 00:48:19'),
				(77, 'FALKLAND ISLANDS (MALVINAS)', 'FALKLAND ISLANDS (MALVINAS)', 4, NULL, '2008-12-17 00:48:29'),
				(78, 'FAROE ISLANDS', 'FAROE ISLANDS', 4, NULL, '2008-12-17 00:49:13'),
				(79, 'FINLAND', 'FINLAND', 4, NULL, '2008-12-17 00:49:25'),
				(80, 'FRANCE', 'FRANCE', 4, NULL, '2008-12-17 00:50:19'),
				(81, 'GEORGIA', 'GEORGIA', 4, NULL, '2008-12-17 00:50:52'),
				(82, 'GERMANY', 'GERMANY', 4, NULL, '2008-12-17 00:51:30'),
				(83, 'GHANA', 'GHANA', 4, NULL, '2008-12-17 00:52:08'),
				(84, 'GIBRALTAR', 'GIBRALTAR', 4, NULL, '2008-12-17 00:52:38'),
				(85, 'GREECE', 'GREECE', 4, NULL, '2008-12-17 00:52:54'),
				(86, 'GREENLAND', 'GREENLAND', 4, NULL, '2008-12-17 00:53:53'),
				(87, 'GUAM', 'GUAM', 4, NULL, '2008-12-17 00:54:20'),
				(88, 'HEARD AND MC DONALD ISLANDS', 'HEARD AND MC DONALD ISLANDS', 4, NULL, '2008-12-17 00:54:47'),
				(89, 'HONG KONG', 'HONG KONG', 4, NULL, '2008-12-17 00:55:26'),
				(90, 'HUNGARY', 'HUNGARY', 4, NULL, '2008-12-17 00:55:54'),
				(91, 'ICELAND', 'ICELAND', 4, NULL, '2008-12-17 00:56:23'),
				(92, 'INDIA', 'INDIA', 4, NULL, '2008-12-17 00:56:47'),
				(93, 'INDONESIA', 'INDONESIA', 4, NULL, '2008-12-17 00:58:00'),
				(94, 'IRAN (ISLAMIC REPUBLIC OF)', 'IRAN (ISLAMIC REPUBLIC OF)', 4, NULL, '2008-12-17 00:58:24'),
				(95, 'IRAQ', 'IRAQ', 4, NULL, '2008-12-17 00:58:54'),
				(96, 'IRELAND', 'IRELAND', 4, NULL, '2008-12-17 00:59:34'),
				(97, 'ISRAEL', 'ISRAEL', 4, NULL, '2008-12-17 01:00:26'),
				(98, 'ITALY', 'ITALY', 4, NULL, '2008-12-17 01:00:54'),
				(99, 'JAPAN', 'JAPAN', 4, NULL, '2008-12-17 01:14:25'),
				(100, 'JORDAN', 'JORDAN', 4, NULL, '2008-12-17 01:15:11'),
				(101, 'KIRIBATI', 'KIRIBATI', 4, NULL, '2008-12-17 01:15:37'),
				(102, 'KOREA REPUBLIC', 'KOREA REPUBLIC', 4, NULL, '2008-12-17 01:16:10'),
				(103, 'KUWAIT', 'KUWAIT', 4, NULL, '2008-12-17 01:16:48'),
				(104, 'LATVIA', 'LATVIA', 4, NULL, '2008-12-17 01:17:55'),
				(105, 'LEBANON', 'LEBANON', 4, NULL, '2008-12-17 01:18:28'),
				(106, 'LIECHTENSTEIN', 'LIECHTENSTEIN', 4, NULL, '2008-12-17 01:18:54'),
				(107, 'LITHUANIA', 'LITHUANIA', 4, NULL, '2008-12-17 01:19:23'),
				(108, 'LIBYA', 'LIBYA', 4, NULL, '2008-12-17 01:20:00'),
				(109, 'LUXEMBOURG', 'LUXEMBOURG', 4, NULL, '2008-12-17 01:20:32'),
				(110, 'MALTA', 'MALTA', 4, NULL, '2008-12-17 01:21:25'),
				(111, 'MARSHALL ISLANDS', 'MARSHALL ISLANDS', 4, NULL, '2008-12-17 01:21:52'),
				(112, 'MAURITIUS', 'MAURITIUS', 4, NULL, '2008-12-17 01:22:50'),
				(113, 'MEXICO', 'MEXICO', 4, NULL, '2008-12-17 01:23:33'),
				(114, 'MICRONESIA FEDERATED STATES OF', 'MICRONESIA FEDERATED STATES OF', 4, NULL, '2008-12-17 01:29:24'),
				(115, 'MONACO', 'MONACO', 4, NULL, '2008-12-17 01:31:38'),
				(116, 'NAURU', 'NAURU', 4, NULL, '2008-12-17 01:34:19'),
				(117, 'NETHERLANDS', 'NETHERLANDS', 4, NULL, '2008-12-17 01:36:06'),
				(118, 'NEW ZEALAND', 'NEW ZEALAND', 4, NULL, '2008-12-17 01:36:30'),
				(119, 'NIUE', 'NIUE', 4, NULL, '2008-12-17 01:36:55'),
				(120, 'NORFOLK ISLAND', 'NORFOLK ISLAND', 4, NULL, '2008-12-17 01:37:56'),
				(121, 'NORWAY', 'NORWAY', 4, NULL, '2008-12-17 01:39:16'),
				(122, 'OMAN', 'OMAN', 4, NULL, '2008-12-17 01:40:03'),
				(123, 'PAKISTAN', 'PAKISTAN', 4, NULL, '2008-12-17 01:40:26'),
				(124, 'PANAMA', 'PANAMA', 4, NULL, '2008-12-17 01:57:02'),
				(125, 'PARAGUAY', 'PARAGUAY', 4, NULL, '2008-12-17 01:57:28'),
				(126, 'PERU', 'PERU', 4, NULL, '2008-12-17 01:57:59'),
				(127, 'PHILIPPINES', 'PHILIPPINES', 4, NULL, '2008-12-17 01:58:25'),
				(128, 'PITCAIRN ISLANDS', 'PITCAIRN ISLANDS', 4, NULL, '2008-12-17 02:11:24'),
				(129, 'POLAND', 'POLAND', 4, NULL, '2008-12-17 02:11:55'),
				(130, 'PORTUGAL', 'PORTUGAL', 4, NULL, '2008-12-17 02:14:41'),
				(131, 'PUERTO RICO', 'PUERTO RICO', 4, NULL, '2008-12-17 02:15:08'),
				(132, 'QATAR', 'QATAR', 4, NULL, '2008-12-17 02:15:36'),
				(133, 'REUNION', 'REUNION', 4, NULL, '2008-12-17 02:16:05'),
				(134, 'ROMANIA', 'ROMANIA', 4, NULL, '2008-12-17 02:16:51'),
				(135, 'RUSSIAN FEDERATION', 'RUSSIAN FEDERATION', 4, NULL, '2008-12-17 02:17:22'),
				(136, 'SAN MARINO', 'SAN MARINO', 4, NULL, '2008-12-17 02:27:03'),
				(137, 'SAUDI ARABIA', 'SAUDI ARABIA', 4, NULL, '2008-12-17 02:29:05'),
				(138, 'SEYCHELLES', 'SEYCHELLES', 4, '2008-12-17 02:30:42', '2008-12-17 02:29:47'),
				(139, 'SINGAPORE', 'SINGAPORE', 4, NULL, '2008-12-17 02:31:09'),
				(140, 'SLOVAKIA', 'SLOVAKIA', 4, NULL, '2008-12-17 02:33:19'),
				(141, 'SLOVENIA', 'SLOVENIA', 4, NULL, '2008-12-17 02:33:43'),
				(142, 'SOUTH AFRICA', 'SOUTH AFRICA', 4, NULL, '2008-12-17 02:35:28'),
				(143, 'SPAIN', 'SPAIN', 4, NULL, '2008-12-17 02:36:02'),
				(144, 'SWEDEN', 'SWEDEN', 4, NULL, '2008-12-17 02:36:34'),
				(145, 'SWITZERLAND', 'SWITZERLAND', 4, NULL, '2008-12-17 02:36:59'),
				(146, 'SYRIAN ARAB REPUBLIC', 'SYRIAN ARAB REPUBLIC', 4, NULL, '2008-12-17 02:38:08'),
				(147, 'TAIWAN', 'TAIWAN', 4, NULL, '2008-12-17 02:39:13'),
				(148, 'THAILAND', 'THAILAND', 4, NULL, '2008-12-17 02:39:37'),
				(149, 'TIMOR-LESTE', 'TIMOR-LESTE', 4, NULL, '2008-12-17 02:40:03'),
				(150, 'TOKELAU', 'TOKELAU', 4, NULL, '2008-12-17 02:40:26'),
				(151, 'TURKEY', 'TURKEY', 4, NULL, '2008-12-17 02:40:51'),
				(152, 'TURKS AND CAICOS ISLANDS', 'TURKS AND CAICOS ISLANDS', 4, NULL, '2008-12-17 02:41:27'),
				(153, 'TUVALU', 'TUVALU', 4, NULL, '2008-12-17 02:41:52'),
				(154, 'UKRAINE', 'UKRAINE', 4, NULL, '2008-12-17 02:42:24'),
				(155, 'UNITED ARAB EMIRATES', 'UNITED ARAB EMIRATES', 4, NULL, '2008-12-17 02:42:57'),
				(156, 'UNITED KINGDOM', 'UNITED KINGDOM', 4, NULL, '2008-12-17 02:43:22'),
				(157, 'UNITED STATES MINOR OUTLYING ISL', 'UNITED STATES MINOR OUTLYING ISLANDS', 4, NULL, '2008-12-17 02:44:08'),
				(158, 'URUGUAY', 'URUGUAY', 4, NULL, '2008-12-17 02:44:31'),
				(159, 'VATICAN CITY STATE (HOLY SEE)', 'VATICAN CITY STATE (HOLY SEE)', 4, NULL, '2008-12-17 02:45:31'),
				(160, 'VENEZUELA', 'VENEZUELA', 4, NULL, '2008-12-17 02:47:19'),
				(161, 'VIETNAM', 'VIETNAM', 4, NULL, '2008-12-17 02:47:47'),
				(162, 'VIRGIN ISLANDS (BRITISH)', 'VIRGIN ISLANDS (BRITISH)', 4, NULL, '2008-12-17 02:48:22'),
				(163, 'VIRGIN ISLANDS (U.S.)', 'VIRGIN ISLANDS (U.S.)', 4, NULL, '2008-12-17 02:49:06'),
				(164, 'YEMEN', 'YEMEN', 4, NULL, '2008-12-17 02:50:28'),
				(165, 'Default - Non Localized', 'Default - Non Localized', 3, NULL, '2008-12-18 12:03:45'),
				(166, 'Default - Non Localized', 'Default - Non Localized', 4, NULL, '2008-12-18 12:05:09');");

tep_db_query("TRUNCATE TABLE `zones_to_geo_zones`");

tep_db_query("INSERT INTO `zones_to_geo_zones` (`association_id`, `zone_country_id`, `zone_id`, `geo_zone_id`, `last_modified`, `date_added`) VALUES
				(27, 25, ',0,', 4, '2008-12-18 07:10:19', '2008-12-12 21:53:15'),
				(29, 25, ',0,', 7, '2008-12-18 12:02:08', '2008-12-12 21:58:39'),
				(30, 129, ',0,', 6, NULL, '2008-12-12 21:59:22'),
				(31, 129, ',0,', 8, NULL, '2008-12-12 22:00:28'),
				(32, 223, ',0,', 9, NULL, '2008-12-12 22:00:58'),
				(33, 129, ',0,', 10, NULL, '2008-12-12 22:04:40'),
				(34, 223, ',0,', 11, NULL, '2008-12-12 22:05:03'),
				(36, 32, ',0,', 13, '2008-12-15 15:45:10', '2008-12-15 02:32:03'),
				(37, 122, ',0,', 15, '2008-12-15 15:47:51', '2008-12-15 02:34:06'),
				(38, 38, ',0,', 14, '2008-12-15 15:46:11', '2008-12-15 02:34:31'),
				(39, 13, ',0,', 16, NULL, '2008-12-15 02:35:35'),
				(40, 56, ',0,', 17, '2008-12-15 15:49:48', '2008-12-15 02:36:39'),
				(41, 57, ',0,', 18, '2008-12-15 15:50:59', '2008-12-15 02:37:43'),
				(42, 63, ',0,', 19, '2008-12-17 12:22:48', '2008-12-15 02:38:41'),
				(43, 221, ',0,', 20, NULL, '2008-12-15 15:37:07'),
				(44, 158, ',0,', 16, NULL, '2008-12-15 15:38:50'),
				(45, 111, ',0,', 16, NULL, '2008-12-15 15:39:08'),
				(46, 94, ',0,', 16, NULL, '2008-12-15 15:39:23'),
				(47, 218, ',0,', 16, NULL, '2008-12-15 15:39:44'),
				(278, 46, ',0,', 16, NULL, '2008-12-17 12:26:11'),
				(277, 45, ',0,', 16, '2008-12-17 12:25:34', '2008-12-17 12:24:47'),
				(50, 204, ',0,', 15, NULL, '2008-12-15 15:48:36'),
				(51, 85, ',0,', 18, NULL, '2008-12-15 15:51:10'),
				(52, 70, ',0,', 18, NULL, '2008-12-15 15:51:23'),
				(53, 132, ',0,', 21, NULL, '2008-12-15 15:54:20'),
				(54, 215, ',0,', 22, NULL, '2008-12-15 15:58:39'),
				(55, 98, ',0,', 22, NULL, '2008-12-15 15:58:55'),
				(56, 175, ',0,', 22, NULL, '2008-12-15 15:59:17'),
				(57, 220, ',0,', 22, NULL, '2008-12-15 15:59:32'),
				(58, 103, ',0,', 23, '2008-12-15 16:02:54', '2008-12-15 16:02:40'),
				(59, 83, ',0,', 23, NULL, '2008-12-15 16:03:08'),
				(60, 105, ',0,', 23, NULL, '2008-12-15 16:03:27'),
				(61, 84, ',0,', 23, NULL, '2008-12-15 16:03:49'),
				(62, 117, ',0,', 23, NULL, '2008-12-15 16:04:02'),
				(63, 182, ',0,', 23, NULL, '2008-12-15 16:08:33'),
				(64, 174, ',0,', 23, NULL, '2008-12-15 16:08:44'),
				(65, 228, ',0,', 23, NULL, '2008-12-15 16:08:58'),
				(66, 195, ',0,', 23, NULL, '2008-12-15 16:09:11'),
				(67, 141, ',0,', 23, NULL, '2008-12-15 16:09:26'),
				(68, 124, ',0,', 23, NULL, '2008-12-15 16:09:41'),
				(69, 123, ',0,', 23, NULL, '2008-12-15 16:09:58'),
				(70, 190, ',0,', 23, NULL, '2008-12-15 16:10:17'),
				(71, 150, ',0,', 23, NULL, '2008-12-15 16:10:39'),
				(72, 171, ',0,', 23, NULL, '2008-12-15 16:10:51'),
				(73, 5, ',0,', 23, NULL, '2008-12-15 16:11:03'),
				(74, 67, ',0,', 23, NULL, '2008-12-15 16:11:18'),
				(75, 53, ',0,', 23, NULL, '2008-12-15 16:17:36'),
				(76, 73, ',0,', 23, NULL, '2008-12-15 16:19:20'),
				(77, 14, ',0,', 23, NULL, '2008-12-15 16:19:33'),
				(78, 33, ',0,', 23, NULL, '2008-12-15 16:19:49'),
				(79, 20, ',0,', 23, NULL, '2008-12-15 16:20:02'),
				(80, 80, ',0,', 23, NULL, '2008-12-15 16:20:22'),
				(81, 81, ',0,', 23, NULL, '2008-12-15 16:20:32'),
				(82, 55, ',0,', 23, NULL, '2008-12-15 16:20:46'),
				(83, 21, ',0,', 23, NULL, '2008-12-15 16:21:02'),
				(84, 72, ',0,', 23, NULL, '2008-12-15 16:21:16'),
				(85, 222, ',0,', 24, NULL, '2008-12-15 16:23:10'),
				(86, 69, ',0,', 25, NULL, '2008-12-15 16:27:23'),
				(87, 96, ',0,', 26, NULL, '2008-12-15 16:28:14'),
				(88, 97, ',0,', 27, NULL, '2008-12-15 17:12:12'),
				(89, 104, ',0,', 28, NULL, '2008-12-15 17:19:32'),
				(90, 108, ',0,', 29, NULL, '2008-12-15 17:26:00'),
				(91, 107, ',0,', 30, NULL, '2008-12-15 17:28:43'),
				(92, 138, ',0,', 31, NULL, '2008-12-15 17:29:47'),
				(93, 160, ',0,', 32, NULL, '2008-12-15 17:47:47'),
				(94, 169, ',0,', 33, NULL, '2008-12-15 17:49:04'),
				(95, 50, ',0,', 33, NULL, '2008-12-15 17:49:17'),
				(96, 211, ',0,', 33, NULL, '2008-12-15 17:49:28'),
				(97, 153, ',0,', 33, NULL, '2008-12-15 17:49:40'),
				(98, 157, ',0,', 33, NULL, '2008-12-15 17:49:51'),
				(99, 170, ',0,', 34, NULL, '2008-12-15 17:51:00'),
				(100, 44, ',0,', 35, NULL, '2008-12-15 17:52:15'),
				(101, 184, ',0,', 36, NULL, '2008-12-15 17:57:42'),
				(102, 203, ',0,', 37, NULL, '2008-12-15 18:03:47'),
				(103, 188, ',0,', 38, NULL, '2008-12-15 18:04:52'),
				(104, 189, ',0,', 39, NULL, '2008-12-15 18:08:33'),
				(105, 209, ',0,', 41, NULL, '2008-12-15 18:15:59'),
				(106, 3, ',0,', 42, NULL, '2008-12-15 18:20:22'),
				(107, 101, ',0,', 42, NULL, '2008-12-15 18:20:35'),
				(108, 176, ',0,', 42, NULL, '2008-12-15 18:20:45'),
				(109, 100, ',0,', 43, NULL, '2008-12-15 18:21:44'),
				(110, 235, ',0,', 9, NULL, '2008-12-15 18:43:52'),
				(111, 193, ',0,', 9, NULL, '2008-12-15 18:44:06'),
				(112, 230, ',0,', 9, NULL, '2008-12-15 18:44:19'),
				(113, 232, ',0,', 9, NULL, '2008-12-15 18:45:19'),
				(114, 186, ',0,', 9, NULL, '2008-12-15 18:45:32'),
				(115, 231, ',0,', 9, NULL, '2008-12-15 18:45:44'),
				(116, 17, ',0,', 9, NULL, '2008-12-15 18:46:26'),
				(117, 61, ',0,', 9, NULL, '2008-12-15 18:50:25'),
				(118, 205, ',0,', 9, NULL, '2008-12-15 19:15:51'),
				(119, 217, ',0,', 9, NULL, '2008-12-15 19:16:08'),
				(120, 10, ',0,', 9, NULL, '2008-12-15 19:16:23'),
				(121, 4, ',0,', 9, NULL, '2008-12-15 19:16:35'),
				(122, 229, ',0,', 9, NULL, '2008-12-15 19:16:50'),
				(123, 16, ',0,', 9, NULL, '2008-12-15 19:17:01'),
				(124, 224, ',0,', 9, NULL, '2008-12-15 19:17:13'),
				(125, 225, ',0,', 9, NULL, '2008-12-15 19:18:51'),
				(126, 206, ',0,', 9, NULL, '2008-12-15 19:19:03'),
				(127, 121, ',0,', 9, NULL, '2008-12-15 19:19:53'),
				(128, 118, ',0,', 9, NULL, '2008-12-15 19:20:05'),
				(129, 60, ',0,', 9, NULL, '2008-12-15 19:20:20'),
				(130, 102, ',0,', 9, NULL, '2008-12-15 19:20:37'),
				(131, 172, ',0,', 9, NULL, '2008-12-15 19:20:50'),
				(132, 136, ',0,', 9, NULL, '2008-12-15 19:21:08'),
				(133, 133, ',0,', 9, NULL, '2008-12-15 19:21:18'),
				(134, 51, ',0,', 9, NULL, '2008-12-15 19:22:01'),
				(135, 99, ',0,', 9, NULL, '2008-12-15 19:22:13'),
				(136, 88, ',0,', 9, NULL, '2008-12-15 19:22:25'),
				(137, 82, ',0,', 9, NULL, '2008-12-15 19:22:37'),
				(138, 114, ',0,', 9, NULL, '2008-12-15 19:22:47'),
				(139, 113, ',0,', 9, NULL, '2008-12-15 19:23:04'),
				(140, 62, ',0,', 9, NULL, '2008-12-15 19:23:21'),
				(141, 64, ',0,', 9, NULL, '2008-12-15 19:23:32'),
				(142, 168, ',0,', 9, NULL, '2008-12-15 19:23:42'),
				(143, 167, ',0,', 9, NULL, '2008-12-15 19:23:54'),
				(144, 40, ',0,', 9, NULL, '2008-12-15 19:24:08'),
				(145, 26, ',0,', 9, NULL, '2008-12-15 19:24:18'),
				(146, 30, ',0,', 9, NULL, '2008-12-15 19:24:32'),
				(147, 31, ',0,', 9, NULL, '2008-12-15 19:24:52'),
				(148, 173, ',0,', 9, NULL, '2008-12-15 19:25:03'),
				(149, 43, ',0,', 9, NULL, '2008-12-15 19:25:15'),
				(150, 47, ',0,', 9, NULL, '2008-12-15 19:26:23'),
				(151, 139, ',0,', 9, NULL, '2008-12-15 19:27:08'),
				(152, 166, ',0,', 9, NULL, '2008-12-15 19:27:19'),
				(153, 164, ',0,', 9, NULL, '2008-12-15 19:27:28'),
				(154, 162, ',0,', 9, NULL, '2008-12-15 19:27:40'),
				(155, 161, ',0,', 9, NULL, '2008-12-15 19:27:50'),
				(156, 3, ',0,', 44, NULL, '2008-12-16 10:24:43'),
				(157, 4, ',0,', 45, NULL, '2008-12-16 12:16:24'),
				(158, 5, ',0,', 46, NULL, '2008-12-16 12:18:39'),
				(159, 10, ',0,', 47, NULL, '2008-12-16 12:26:23'),
				(160, 13, ',0,', 48, NULL, '2008-12-16 14:48:06'),
				(161, 14, ',0,', 49, NULL, '2008-12-16 14:59:12'),
				(162, 16, ',0,', 50, NULL, '2008-12-16 15:49:20'),
				(163, 17, ',0,', 51, NULL, '2008-12-16 15:56:57'),
				(164, 20, ',0,', 52, NULL, '2008-12-16 16:00:17'),
				(165, 21, ',0,', 53, NULL, '2008-12-16 16:02:55'),
				(166, 26, ',0,', 54, NULL, '2008-12-16 16:09:40'),
				(167, 30, ',0,', 55, NULL, '2008-12-16 17:07:30'),
				(168, 31, ',0,', 56, NULL, '2008-12-16 17:11:20'),
				(169, 32, ',0,', 57, NULL, '2008-12-16 17:13:02'),
				(170, 38, ',0,', 59, NULL, '2008-12-17 00:05:48'),
				(171, 33, ',0,', 58, NULL, '2008-12-17 00:06:08'),
				(172, 40, ',0,', 60, NULL, '2008-12-17 00:06:48'),
				(173, 43, ',0,', 61, NULL, '2008-12-17 00:07:16'),
				(174, 44, ',0,', 62, NULL, '2008-12-17 00:11:01'),
				(175, 45, ',0,', 63, NULL, '2008-12-17 00:12:21'),
				(176, 46, ',0,', 64, NULL, '2008-12-17 00:15:20'),
				(177, 47, ',0,', 65, NULL, '2008-12-17 00:16:11'),
				(178, 53, ',0,', 68, NULL, '2008-12-17 00:31:45'),
				(180, 51, ',0,', 67, NULL, '2008-12-17 00:32:32'),
				(181, 50, ',0,', 66, NULL, '2008-12-17 00:32:52'),
				(182, 55, ',0,', 69, NULL, '2008-12-17 00:33:29'),
				(183, 56, ',0,', 70, NULL, '2008-12-17 00:34:04'),
				(184, 57, ',0,', 71, NULL, '2008-12-17 00:35:09'),
				(185, 60, ',0,', 72, NULL, '2008-12-17 00:35:40'),
				(186, 62, ',0,', 73, NULL, '2008-12-17 00:36:08'),
				(187, 63, ',0,', 74, NULL, '2008-12-17 00:36:46'),
				(188, 64, ',0,', 75, NULL, '2008-12-17 00:47:31'),
				(189, 67, ',0,', 76, NULL, '2008-12-17 00:48:44'),
				(190, 69, ',0,', 77, NULL, '2008-12-17 00:49:00'),
				(191, 72, ',0,', 79, NULL, '2008-12-17 00:49:43'),
				(192, 70, ',0,', 78, NULL, '2008-12-17 00:50:01'),
				(193, 73, ',0,', 80, NULL, '2008-12-17 00:50:37'),
				(194, 80, ',0,', 81, NULL, '2008-12-17 00:51:15'),
				(195, 81, ',0,', 82, NULL, '2008-12-17 00:51:48'),
				(196, 82, ',0,', 83, NULL, '2008-12-17 00:52:23'),
				(197, 84, ',0,', 85, NULL, '2008-12-17 00:53:06'),
				(198, 83, ',0,', 84, NULL, '2008-12-17 00:53:25'),
				(199, 85, ',0,', 86, NULL, '2008-12-17 00:54:06'),
				(200, 88, ',0,', 87, NULL, '2008-12-17 00:54:34'),
				(201, 94, ',0,', 88, NULL, '2008-12-17 00:55:00'),
				(202, 96, ',0,', 89, NULL, '2008-12-17 00:55:39'),
				(203, 97, ',0,', 90, NULL, '2008-12-17 00:56:08'),
				(204, 98, ',0,', 91, NULL, '2008-12-17 00:56:35'),
				(205, 99, ',0,', 92, NULL, '2008-12-17 00:56:57'),
				(206, 100, ',0,', 93, NULL, '2008-12-17 00:58:11'),
				(207, 101, ',0,', 94, NULL, '2008-12-17 00:58:40'),
				(208, 102, ',0,', 95, NULL, '2008-12-17 00:59:12'),
				(209, 103, ',0,', 96, '2008-12-17 01:00:08', '2008-12-17 00:59:45'),
				(210, 104, ',0,', 97, NULL, '2008-12-17 01:00:39'),
				(211, 105, ',0,', 98, NULL, '2008-12-17 01:01:07'),
				(212, 108, ',0,', 100, NULL, '2008-12-17 01:15:24'),
				(213, 111, ',0,', 101, NULL, '2008-12-17 01:15:52'),
				(214, 113, ',0,', 102, NULL, '2008-12-17 01:16:22'),
				(215, 114, ',0,', 103, NULL, '2008-12-17 01:17:00'),
				(216, 117, ',0,', 104, NULL, '2008-12-17 01:18:17'),
				(217, 118, ',0,', 105, NULL, '2008-12-17 01:18:39'),
				(218, 122, ',0,', 106, NULL, '2008-12-17 01:19:09'),
				(219, 123, ',0,', 107, NULL, '2008-12-17 01:19:40'),
				(220, 121, ',0,', 108, NULL, '2008-12-17 01:20:20'),
				(221, 124, ',0,', 109, NULL, '2008-12-17 01:20:44'),
				(222, 132, ',0,', 110, NULL, '2008-12-17 01:21:38'),
				(223, 133, ',0,', 111, NULL, '2008-12-17 01:22:37'),
				(224, 136, ',0,', 112, NULL, '2008-12-17 01:23:08'),
				(225, 138, ',0,', 113, NULL, '2008-12-17 01:23:50'),
				(226, 139, ',0,', 114, NULL, '2008-12-17 01:29:44'),
				(227, 141, ',0,', 115, NULL, '2008-12-17 01:34:04'),
				(228, 148, ',0,', 116, NULL, '2008-12-17 01:34:34'),
				(229, 150, ',0,', 117, NULL, '2008-12-17 01:36:18'),
				(230, 153, ',0,', 118, NULL, '2008-12-17 01:36:43'),
				(231, 157, ',0,', 119, NULL, '2008-12-17 01:37:07'),
				(232, 158, ',0,', 120, NULL, '2008-12-17 01:38:14'),
				(233, 160, ',0,', 121, NULL, '2008-12-17 01:39:49'),
				(234, 161, ',0,', 122, NULL, '2008-12-17 01:40:14'),
				(235, 162, ',0,', 123, NULL, '2008-12-17 01:40:41'),
				(236, 164, ',0,', 124, NULL, '2008-12-17 01:57:15'),
				(237, 166, ',0,', 125, NULL, '2008-12-17 01:57:40'),
				(238, 167, ',0,', 126, NULL, '2008-12-17 01:58:10'),
				(239, 168, ',0,', 127, NULL, '2008-12-17 01:58:36'),
				(240, 169, ',0,', 128, NULL, '2008-12-17 02:11:42'),
				(241, 170, ',0,', 129, NULL, '2008-12-17 02:12:07'),
				(242, 171, ',0,', 130, NULL, '2008-12-17 02:14:55'),
				(243, 172, ',0,', 131, NULL, '2008-12-17 02:15:21'),
				(244, 173, ',0,', 132, NULL, '2008-12-17 02:15:47'),
				(245, 174, ',0,', 133, NULL, '2008-12-17 02:16:38'),
				(246, 175, ',0,', 134, NULL, '2008-12-17 02:17:02'),
				(247, 176, ',0,', 135, NULL, '2008-12-17 02:17:37'),
				(248, 182, ',0,', 136, NULL, '2008-12-17 02:28:05'),
				(249, 184, ',0,', 137, NULL, '2008-12-17 02:29:16'),
				(250, 186, ',0,', 138, NULL, '2008-12-17 02:30:55'),
				(251, 188, ',0,', 139, NULL, '2008-12-17 02:31:23'),
				(252, 189, ',0,', 140, NULL, '2008-12-17 02:33:31'),
				(253, 190, ',0,', 141, NULL, '2008-12-17 02:34:00'),
				(254, 193, ',0,', 142, NULL, '2008-12-17 02:35:48'),
				(255, 195, ',0,', 143, NULL, '2008-12-17 02:36:22'),
				(256, 203, ',0,', 144, NULL, '2008-12-17 02:36:48'),
				(257, 204, ',0,', 145, NULL, '2008-12-17 02:37:40'),
				(258, 205, ',0,', 146, NULL, '2008-12-17 02:39:01'),
				(259, 206, ',0,', 147, NULL, '2008-12-17 02:39:24'),
				(260, 209, ',0,', 148, NULL, '2008-12-17 02:39:50'),
				(261, 61, ',0,', 149, NULL, '2008-12-17 02:40:15'),
				(262, 211, ',0,', 150, NULL, '2008-12-17 02:40:38'),
				(263, 215, ',0,', 151, NULL, '2008-12-17 02:41:05'),
				(264, 217, ',0,', 152, NULL, '2008-12-17 02:41:40'),
				(265, 218, ',0,', 153, NULL, '2008-12-17 02:42:06'),
				(266, 220, ',0,', 154, NULL, '2008-12-17 02:42:36'),
				(267, 221, ',0,', 155, NULL, '2008-12-17 02:43:10'),
				(268, 222, ',0,', 156, NULL, '2008-12-17 02:43:35'),
				(269, 224, ',0,', 157, NULL, '2008-12-17 02:44:19'),
				(270, 225, ',0,', 158, NULL, '2008-12-17 02:44:43'),
				(271, 228, ',0,', 159, NULL, '2008-12-17 02:47:07'),
				(272, 229, ',0,', 160, NULL, '2008-12-17 02:47:33'),
				(273, 230, ',0,', 161, NULL, '2008-12-17 02:47:58'),
				(274, 231, ',0,', 162, NULL, '2008-12-17 02:48:49'),
				(275, 232, ',0,', 163, NULL, '2008-12-17 02:49:18'),
				(276, 235, ',0,', 164, NULL, '2008-12-17 02:50:41'),
				(279, 148, ',0,', 16, NULL, '2008-12-17 12:26:34'),
				(280, 25, ',0,', 165, NULL, '2008-12-18 12:04:11'),
				(281, 25, ',0,', 166, NULL, '2008-12-18 12:05:41');");


// Pre-populate the customer_info_selected_country
if (!in_array('customer_info_selected_country', $existing_customers_info_fields)) {
	$default_country_update_sql = " UPDATE customers_info AS ci
									INNER JOIN customers as c
										ON ci.customers_info_id=c.customers_id 
									INNER JOIN address_book AS ab
										ON c.customers_default_address_id = ab.address_book_id 
									SET ci.customer_info_selected_country = ab.entry_country_id 
									WHERE ci.customer_info_selected_country = 0";
	tep_db_query($default_country_update_sql);
}
// End of Pre-populate the customer_info_selected_country
?>