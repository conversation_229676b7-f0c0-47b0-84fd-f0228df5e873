<?
/*
	$Id: version_2_1_1.php,v 1.2 2006/09/07 08:20:44 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define supplier_order_lists_id, supplier_order_lists_type and products_id as unique key
add_index_key ('supplier_order_lists_products', 'unique_supplier_list_products', 'unique', 'supplier_order_lists_id, supplier_order_lists_type, products_id', $DBTables);
// End of supplier_order_lists_id, supplier_order_lists_type and products_id as unique key

// Create buyback_setting table
$add_new_tables = array();

$add_new_tables["buyback_setting"] = array (	"structure" => "CREATE TABLE `buyback_setting` (
																  `buyback_setting_reference_id` int(11) NOT NULL default '0',
																  `buyback_setting_table_name` varchar(150) NOT NULL default '',
																  `buyback_setting_key` varchar(255) NOT NULL default '',
																  `buyback_setting_value` varchar(255) NOT NULL default '',
																  PRIMARY KEY  (`buyback_setting_reference_id`, `buyback_setting_table_name`, `buyback_setting_key`),
																  <PERSON><PERSON><PERSON> `index_buyback_setting_table_name` (`buyback_setting_table_name`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["stock_history"] = array (	"structure" => "CREATE TABLE `stock_history` (
															  `stock_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
															  `products_id` int(11) NOT NULL default '0',
															  `products_cat_path` text,
															  `products_quantity` int(4) NOT NULL default '0',
															  `products_actual_quantity` int(4) NOT NULL default '0',
															  `products_quantity_fifo_cost` decimal(15,6) default NULL,
															  `products_actual_quantity_fifo_cost` decimal(15,6) default NULL,
															  PRIMARY KEY  (`stock_history_date`, `products_id`),
															  KEY `idx_stock_history_date` (`stock_history_date`)
															) ENGINE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["outstanding_payment_history"] = array (	"structure" => "CREATE TABLE `outstanding_payment_history` (
																				`outstanding_payment_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
																			  	`payment_category` varchar(16) NOT NULL default '',
																			  	`user_id` int(11) NOT NULL default '0',
																				`user_firstname` varchar(32) NOT NULL default '',
  																				`user_lastname` varchar(32) NOT NULL default '',
 																				`user_email_address` varchar(96) NOT NULL default '',
 																				`outstanding_payment_gross_amount` decimal(15,4) default NULL,
 																				`outstanding_payment_amount` decimal(15,4) NOT NULL default '0.0000',
																				PRIMARY KEY  (`outstanding_payment_history_date`, `payment_category`, `user_id`),
																				KEY `idx_outstanding_payment_history_date` (`outstanding_payment_history_date`)
																			) ENGINE=MyISAM;" ,
															"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// End of create buyback_setting table

// Change field structure for buyback_groups_tags_maximum_buyback in buyback_groups_tags_info table
$change_field_structure = array();
$change_field_structure["buyback_groups_tags_info"] = array (array (	"field_name" => "buyback_groups_tags_maximum_buyback",
																		"field_attr" => " int(11) default NULL "
														 			)
																);
change_field_structure ($change_field_structure);
// End of change field structure for buyback_groups_tags_maximum_buyback in buyback_groups_tags_info table

// Change existing primary key, buyback_products_id to products_id, and delete buyback_products_id field in buyback_products table
$existing_buyback_products_fields = get_table_fields("buyback_products");
if (in_array('buyback_products_id', $existing_buyback_products_fields)) {
	$delete_field = array();
	$delete_field["buyback_products"] = array  ( array( "field_name" => "buyback_products_id") );
	delete_field ($delete_field);
	
	add_index_key ("buyback_products", 'primary key', 'primary', 'products_id', $DBTables);
}
// End of change existing primary key, buyback_products_id to products_id, and delete buyback_products_id field in buyback_products table


// Insert new fields into products table (for storing product's FIFO average cost)
$add_new_field = array();

$add_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_quantity_fifo_cost",
													"field_attr" => " decimal(15,6) default NULL ",
													"add_after" => ""
										   			),
										   	array (	"field_name" => "products_actual_quantity_fifo_cost",
													"field_attr" => " decimal(15,6) default NULL ",
													"add_after" => "products_quantity_fifo_cost"
										   			)
										);

add_field($add_new_field);
// End of insert new fields into products table (for storing product's FIFO average cost)


/********************************************************************
 	Initialized the FIFO cost for available and actual quantities
********************************************************************/
$product_qty_select_sql = "	SELECT products_id, products_quantity, products_actual_quantity 
							FROM " . TABLE_PRODUCTS . " 
							WHERE products_skip_inventory=0 
								AND products_bundle='' 
								AND products_bundle_dynamic='' ";
$product_qty_result_sql = tep_db_query($product_qty_select_sql);		
while ($product_qty_row = tep_db_fetch_array($product_qty_result_sql)) {
	tep_set_product_fifo_avg_price($product_qty_row['products_id'], $product_qty_row['products_quantity'], $product_qty_row['products_actual_quantity']);
}
// End of initialized the FIFO cost for available and actual quantities


// Insert new records into configuration table (for Privileges Customers's live help)
$conf_insert_sql = array();

$conf_insert_sql["LIVE_HELP_PRIVILEGES_CUSTOMERS_IMPLEMENTATION"] = array("insert" => " ('Live Help Privileges Customers Implementation', 'LIVE_HELP_PRIVILEGES_CUSTOMERS_IMPLEMENTATION', '', 'This is used when there is customized live help for privileges customers', 1, 35, NULL, now(), NULL, 'tep_cfg_textarea(')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Privileges Customers's live help)

// Insert new records into admin_files_actions table (for permission on accessing delivery section when the customer who made that order is inactive)
$admin_files_actions_insert_sql = array();

$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql["VIEW_DELIVERY_SECTION_OF_INACTIVE_CUSTOMER_ORDER"] = array("insert" => " ('VIEW_DELIVERY_SECTION_OF_INACTIVE_CUSTOMER_ORDER', 'View delivery section of inactive customer order', ".$row_sql["admin_files_id"].", '1', 6)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on accessing delivery section when the customer who made that order is inactive)

?>