<?
/*
	$Id: version_2_1_2.php,v 1.1 2006/09/08 08:56:27 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on viewing FIFO info and outstanding report in stock report)
$admin_files_actions_insert_sql = array();

$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='stock_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql["VIEW_FIFO_INFO"] = array("insert" => " ('VIEW_FIFO_INFO', 'View product\'s FIFO unit cost', ".$row_sql["admin_files_id"].", '1', 20)" );
	$admin_files_actions_insert_sql["VIEW_OUTSTANDING_REPORT"] = array("insert" => " ('VIEW_OUTSTANDING_REPORT', 'View outstanding report', ".$row_sql["admin_files_id"].", '1', 30)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing FIFO info and outstanding report in stock report)

?>