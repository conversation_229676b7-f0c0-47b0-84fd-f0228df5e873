<?
/*
	$Id: version_2_1_3.php,v 1.2 2006/09/19 05:33:35 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on adjusting payout ratio at completed status)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='progress_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_CP_COMPLETED_PAYABLE_MANUAL_ADJUST"] = array("insert" => " ('SUPPLIER_CP_COMPLETED_PAYABLE_MANUAL_ADJUST', 'Adjust supplier payable amount at \"Completed\" status', ".$row_sql["admin_files_id"].", '1', 28)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on adjusting payout ratio at completed status)

// Change field structure for customers_country_dialing_code_id in customers table
	$change_field_structure = array();
	
	$change_field_structure["customers"] = array (array (	"field_name" => "customers_country_dialing_code_id",
										 					"field_attr" => " INT(11) NULL DEFAULT NULL "
											 			)
												);
	change_field_structure ($change_field_structure);
// End of change field structure for customers_country_dialing_code_id in customers table
?>