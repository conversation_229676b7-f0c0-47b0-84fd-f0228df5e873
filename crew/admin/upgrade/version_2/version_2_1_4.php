<?
/*
	$Id: version_2_1_4.php,v 1.2 2006/09/26 07:51:20 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration_group table (for Custom Product)
$configuration_group_insert_sql = array();

$configuration_group_insert_sql["Custom Product"] = array	(	"insert" => " ('Custom Product', 'Custom Product setting', 300, 1) ",
																"update" => " sort_order=300, visible='1' "
						   									);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");
// End of insert new records into configuration_group table (for Custom Product)

// Insert new records into configuration table (for advertisement)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Custom Product'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["CP_PROGRESS_ALERT_TIME"] = array("insert" => " ('Custom Product Progress Alert Time', 'CP_PROGRESS_ALERT_TIME', '1440', 'Maximum allowable time for latest custom product progress update (in minutes) or alert will shown in the progress bar.', ".$row_sql["configuration_group_id"].", 10, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for advertisement)

// Sunny's changes
// Create restock_character_sets_to_purchases_lists table
$add_new_tables = array();

$add_new_tables["restock_character_sets_to_purchases_lists"] = array (	"structure" => "CREATE TABLE `restock_character_sets_to_purchases_lists` (
																						  `restock_character_sets_id` int(11) NOT NULL default '0',
																						  `products_purchases_lists_id` int(11) NOT NULL default '0',
																						  PRIMARY KEY  (`restock_character_sets_id`,`products_purchases_lists_id`)
																						) ENGINE=MyISAM;",
																		"data" => ""
																	);

add_new_tables ($add_new_tables, $DBTables);
// End of create restock_character_sets_to_purchases_lists table

// Insert new fields into restock_character_sets table (for separating restock character sets from purchase list)
$add_new_field = array();

$add_new_field['restock_character_sets'] = array (	array (	"field_name" => "restock_character_sets_cat_id",
															"field_attr" => " INT(11) NOT NULL default '0' ",
															"add_after" => "restock_character_sets_name"
												   			),
												   	array (	"field_name" => "restock_character_sets_sort_order",
															"field_attr" => " INT(5) NOT NULL default '50000' ",
															"add_after" => "restock_character_sets_cat_id"
												   			),
												   	array (	"field_name" => "restock_character_sets_last_modified",
															"field_attr" => " datetime NOT NULL default '0000-00-00 00:00:00' ",
															"add_after" => ""
												   			)
												);

add_field($add_new_field);
// End of insert new fields into restock_character_sets table (for separating restock character sets from purchase list)


// Migrate existing purchase list and restock character sets relationship to new table, restock_character_sets_to_purchases_lists
$existing_restock_character_sets_fields = get_table_fields("restock_character_sets");
if (in_array('products_purchases_lists_id', $existing_restock_character_sets_fields)) {
	$transfer_select_sql = "SELECT rcs.restock_character_sets_id, rcs.products_purchases_lists_id, ppl.products_purchases_lists_cat_id
							FROM restock_character_sets AS rcs
							INNER JOIN products_purchases_lists AS ppl
								ON (rcs.products_purchases_lists_id=ppl.products_purchases_lists_id)
							ORDER BY rcs.restock_character_sets_id";
	$transfer_result_sql = tep_db_query($transfer_select_sql);
	
	while ($transfer_row = tep_db_fetch_array($transfer_result_sql)) {
		$transfer_data_array = array ('restock_character_sets_id' => $transfer_row['restock_character_sets_id'],
									 'products_purchases_lists_id' => $transfer_row['products_purchases_lists_id']
									 );
		tep_db_perform('restock_character_sets_to_purchases_lists', $transfer_data_array);
		tep_db_query("UPDATE `restock_character_sets` SET restock_character_sets_cat_id = '". $transfer_row['products_purchases_lists_cat_id'] ."' WHERE products_purchases_lists_id = '". $transfer_row['products_purchases_lists_id'] ."'");
	}
}
// End of migrate existing purchase list and restock character sets relationship to new table, restock_character_sets_to_purchases_lists

// Delete module field from restock_character_sets table
$delete_field = array();

$delete_field["restock_character_sets"] = array  ( array( "field_name" => "products_purchases_lists_id") );
delete_field ($delete_field);
// End of delete module field from restock_character_sets table

?>