<?
/*
	$Id: version_2_2_1.php,v 1.1 2006/10/02 09:22:57 weichen Exp $
	
  	Developer: <PERSON> (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create mail/trade log related tables
$add_new_tables = array();

$add_new_tables["game_char_log"] = array (	"structure" => "CREATE TABLE `game_char_log` (
															  `game_char_log_id` int(11) NOT NULL auto_increment,
															  `game_char_log_time` datetime default NULL,
															  `game_char_log_account_name` varchar(64) NOT NULL default '',
															  `game_char_log_server` varchar(32) NOT NULL default '',
															  `game_char_log_realm` varchar(32) NOT NULL default '',
															  `game_char_log_sender` varchar(64) NOT NULL default '',
															  `game_char_log_receiver` varchar(64) NOT NULL default '',
															  `game_char_log_subject` varchar(255) default NULL,
															  `game_char_log_messages` text,
															  `game_char_log_balance_before` varchar(255) NOT NULL default '0',
															  `game_char_log_balance_after` varchar(255) NOT NULL default '0',
															  `game_char_log_system_messages` text,
															  `game_char_log_send` varchar(255) NOT NULL default '',
															  `game_char_log_receive` varchar(255) NOT NULL default '',
															  `game_char_log_type` varchar(16) NOT NULL default '',
															  `game_char_log_login_as` varchar(32) NOT NULL default '',
															  `game_char_log_computer_name` varchar(64) NOT NULL default '',
															  PRIMARY KEY  (`game_char_log_id`)
															) TYPE=MyISAM COMMENT='Games Characters Log Info';" ,
											"data" => ""
										);

$add_new_tables["game_item_info"] = array (	"structure" => "CREATE TABLE `game_item_info` (
															  `game_item_info_id` int(11) NOT NULL auto_increment,
															  `game_item_info_name` varchar(64) NOT NULL default '',
															  `game_item_info_color` varchar(16) NOT NULL default '',
															  `game_item_info_tooltip` mediumtext NOT NULL,
															  `game_item_info_item` varchar(16) NOT NULL default '',
															  `game_item_info_texture` varchar(64) NOT NULL default '',
															  PRIMARY KEY  (`game_item_info_id`)
															) TYPE=MyISAM COMMENT='Games Items Info';" ,
											"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create mail/trade log related tables

// Insert new records into admin_files table (for mail/trade log report)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='data_pool.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["trade_mail_log.php"] = array(	"insert" => " ('trade_mail_log.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   									);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='trade_mail_log.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for mail/trade log report)

?>