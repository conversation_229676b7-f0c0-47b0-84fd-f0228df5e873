<?
/*
	$Id: version_2_2_2.php,v 1.3 2006/10/04 03:23:31 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// <PERSON>'s changes
// Moving the setting from buyback_group to buyback_groups_tags
$buyback_setting_bygroup_select_sql = "	select buyback_setting_reference_id, buyback_setting_table_name, buyback_setting_key, buyback_setting_value 
										from " . TABLE_BUYBACK_SETTING . " 
										where buyback_setting_table_name = '" . TABLE_BUYBACK_GROUPS . "' 
											and buyback_setting_key IN ('bgrp_inventory_days', 'bgrp_sales_end_date', 'bgrp_sales_start_date')";
$buyback_setting_bygroup_result_sql = tep_db_query($buyback_setting_bygroup_select_sql);

//Enter per group
while ($setting_row = tep_db_fetch_array($buyback_setting_bygroup_result_sql)) {
    $buyback_groups_id = $setting_row['buyback_setting_reference_id'];
    $buyback_setting_by_tag_info_insert_arr = $setting_row;
    
    //Find out the tag_info_id, which is one for each product in this group.
    $buyback_group_tag_info_select_sql = "	select buyback_groups_tags_info_id 
    										from " . TABLE_BUYBACK_GROUPS_TAGS_INFO . " 
    										where buyback_groups_id = '$buyback_groups_id'";
    $buyback_group_tag_info_result_sql = tep_db_query($buyback_group_tag_info_select_sql);
    
    //Enter per tag_info_id for this group
    while ($buyback_group_tag_info_row = tep_db_fetch_array($buyback_group_tag_info_result_sql)) {
        //Overwrite group_id with tag_info_id
        $buyback_setting_by_tag_info_insert_arr['buyback_setting_reference_id'] = $buyback_group_tag_info_row['buyback_groups_tags_info_id'];
        
        //Overwrite tablename buyback_groups with buyback_groups_tags
        $buyback_setting_by_tag_info_insert_arr['buyback_setting_table_name'] = TABLE_BUYBACK_GROUPS_TAGS_INFO;
        
        //Insert same group setting for each tag_info_id
        tep_db_perform(TABLE_BUYBACK_SETTING, $buyback_setting_by_tag_info_insert_arr);
    }
}

$buyback_setting_bygroup_delete_sql = "delete from " . TABLE_BUYBACK_SETTING . " where buyback_setting_table_name = '" . TABLE_BUYBACK_GROUPS . "' and buyback_setting_key IN ('bgrp_inventory_days', 'bgrp_sales_end_date', 'bgrp_sales_start_date')";
tep_db_query($buyback_setting_bygroup_delete_sql);

// End of moving the setting from buyback_group to buyback_groups_tags

// Chan's changes
// Insert new fields into game_char_log table (for storing character's race)
$add_new_field = array();

$add_new_field['game_char_log'] = array (	array (	"field_name" => "game_char_log_race",
													"field_attr" => " VARCHAR(64) NOT NULL ",
													"add_after" => "game_char_log_realm"
										   			)
										);

add_field($add_new_field);
// End of insert new fields into game_char_log table (for storing character's race)

?>