<?
/*
	$Id: version_2_3.php,v 1.1 2006/10/10 05:03:39 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create e-gold related tables
$add_new_tables = array();

$add_new_tables["egold_currencies"] = array (	"structure" => "CREATE TABLE egold_currencies (
																  	egold_currencies_code char(3) NOT NULL default '',
																  	egold_currencies_title varchar(32) NOT NULL default '',
																  	egold_currencies_payment_unit varchar(10) NOT NULL default '',
																  	PRIMARY KEY (egold_currencies_code)
																) TYPE=MyISAM COMMENT='e-gold Accepted Payment Units';" ,
												"data" => "INSERT INTO `egold_currencies` (egold_currencies_code, egold_currencies_title, egold_currencies_payment_unit) 
															VALUES 
																('USD', 'US Dollar', '1'),
															  	('CAD', 'Canadian Dollar', '2'),
															  	('CHF', 'Swiss Francs', '41'),
															  	('GBP', 'Gt. Britain Pound', '44'),
															  	('AUD', 'Australian Dollar', '61'),
															  	('JPY', 'Japanese Yen', '81'),
															  	('EUR', 'Euro', '85'),
															  	('EEK', 'Estonian Krooni', '96'),
															  	('LTL', 'Lithuanian Litas', '97'),
															  	('g', 'Gram', '8888'),
															  	('oz', 'Troy Ounce', '9999')"
											);

$add_new_tables["egold"] = array (	"structure" => "CREATE TABLE `egold` (
													  	orders_id int(11) unsigned NOT NULL default '0',
													  	egold_transaction_status tinyint(1) NOT NULL default '0',
													  	egold_payment_amount decimal(15,4) NOT NULL default '0.0000',
														egold_payment_units varchar(10) NOT NULL default '',
														egold_payment_metal_id char(1) NOT NULL default '1',
														egold_payment_batch_num varchar(32) NOT NULL default '',
														egold_payer_account varchar(32) NOT NULL default '',
														egold_actual_payment_ounces decimal(15,6) NOT NULL default '0.000000',
														egold_usd_per_ounce decimal(15,2) NOT NULL default '0.00',
														egold_feeweight decimal(15,6) NOT NULL default '0.000000',
														egold_payment_timestamp varchar(32) NOT NULL default '',
														egold_v2_hash varchar(32) NOT NULL default '',
													  PRIMARY KEY  (`orders_id`)
													) TYPE=MyISAM COMMENT='e-gold Transaction History Table';" ,
									"data" => ""
								);


add_new_tables ($add_new_tables, $DBTables);
// End of create e-gold related tables

// Insert new records into admin_files_actions table (for permission on manually authorise order made by payment with IPN)
$admin_files_actions_insert_sql = array();

$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql["ADM_IPN_ORDER_MANUAL_AUTHORISATION"] = array("insert" => " ('ADM_IPN_ORDER_MANUAL_AUTHORISATION', 'Manually authorise order made by payment with IPN', ".$row_sql["admin_files_id"].", '1', 35)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on manually authorise order made by payment with IPN)

?>