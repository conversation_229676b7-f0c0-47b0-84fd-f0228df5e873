<?
/*
	$Id: version_2_3_2.php,v 1.1 2006/10/17 06:25:12 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into payment_extra_info table (for storing cardholder authentication result)
$add_new_field = array();

$add_new_field['payment_extra_info'] = array (	array (	"field_name" => "cardholder_aut_result",
														"field_attr" => " varchar(64) default NULL ",
														"add_after" => "credit_card_owner"
										   				)
											);

add_field($add_new_field);
// End of insert new fields into payment_extra_info table (for storing cardholder authentication result)

?>