<?
/*
	$Id: version_2_3_3.php,v 1.1 2006/10/18 09:04:41 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into latest_news and infolinks tables (for SEO alias)
$add_new_field = array();

$add_new_field['latest_news'] = array (	array (	"field_name" => "latest_news_url_alias",
												"field_attr" => " varchar(255) NOT NULL ",
												"add_after" => "headline"
												)
									);

$add_new_field['infolinks'] = array (	array (	"field_name" => "infolinks_url_alias",
												"field_attr" => " varchar(255) NOT NULL ",
												"add_after" => "infolinks_title"
												)
									);
									
add_field($add_new_field);
// End of insert new fields into latest_news and infolinks tables (for SEO alias)


$news_select_sql = "SELECT news_id, headline FROM latest_news WHERE language=1 ORDER BY news_id";
$news_result_sql = tep_db_query($news_select_sql);
while ($news_row = tep_db_fetch_array($news_result_sql)) {
	$new_url_name = $news_row['headline'];
	
	$dash = array("/", "_", "'", "~", ",", ":", " ", "-");
	$join = array("(", ")", "!", "%", ".");
	
	$new_url_name = str_replace($dash, '-', $new_url_name); // replace to dash
	$new_url_name = str_replace($join, '', $new_url_name); // join text
	
	while (strstr($new_url_name, '--')) {
		$new_url_name = str_replace('--', '-', $new_url_name); // '--' -> '-'
	}
	
	$url_name_update_sql = "UPDATE latest_news 
							SET latest_news_url_alias = '". tep_db_input($new_url_name) ."' 
							WHERE news_id = '" . $news_row['news_id'] . "'";
	tep_db_query($url_name_update_sql);
}

$infolinks_title_select_sql = "SELECT infolinks_id, infolinks_title FROM infolinks WHERE language_id=1 ORDER BY infolinks_id";
$infolinks_title_result_sql = tep_db_query($infolinks_title_select_sql);
while ($infolinks_title_row = tep_db_fetch_array($infolinks_title_result_sql)) {
	$new_infolinks_url_name = $infolinks_title_row['infolinks_title'];
	
	$dash = array("/", "_", "'", "~", ",", ":", " ", "-");
	$join = array("(", ")", "!", "%", ".");	
	
	$new_infolinks_url_name = str_replace($dash, '-', $new_infolinks_url_name); // replace to dash
	$new_infolinks_url_name = str_replace($join, '', $new_infolinks_url_name); // join text	
	
	while (strstr($new_infolinks_url_name, '--')) {
		$new_infolinks_url_name = str_replace('--', '-', $new_infolinks_url_name); // '--' -> '-'
	}
	
	$url_name_update_sql = "UPDATE " . TABLE_INFOLINKS . " 
							SET infolinks_url_alias = '" . tep_db_input($new_infolinks_url_name) . "' 
							WHERE infolinks_id = '" . $infolinks_title_row['infolinks_id'] . "'";
	tep_db_query($url_name_update_sql);
}

// Create info_changed_history table
$add_new_tables = array();

$add_new_tables["info_changed_history"] = array (	"structure" => "CREATE TABLE `info_changed_history` (
																		`info_changed_history_id` INT(11) NOT NULL AUTO_INCREMENT ,
																		`info_changed_history_type` VARCHAR(30) NOT NULL ,
																		`info_changed_history_type_id` INT(11) NOT NULL ,
																		`info_changed_history_remark` TEXT NOT NULL ,
																		`info_changed_history_date_added` DATETIME NOT NULL ,
																		`info_changed_history_added_by` VARCHAR(255) NOT NULL ,
																		PRIMARY KEY (`info_changed_history_id`)
																	) TYPE=MyISAM COMMENT='Keep track of infolinks and latest news changes';" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create info_changed_history table

?>