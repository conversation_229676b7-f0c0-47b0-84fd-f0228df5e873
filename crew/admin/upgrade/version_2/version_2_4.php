<?
/*
	$Id: version_2_4.php,v 1.5 2006/11/02 08:52:29 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_data_pool_options_tags_fields = get_table_fields("data_pool_options_tags");

// Insert new records into admin_files table (for merge customers account)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='customers.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["customers_acc_merging.php"] = array(	"insert" => " ('customers_acc_merging.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   											);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='customers_acc_merging.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for merge customers account)

// Insert new fields into customers tables
$add_new_field = array();

$add_new_field['customers'] = array (	array (	"field_name" => "customers_merged_profile",
												"field_attr" => " varchar(255) NOT NULL default '' ",
												"add_after" => ""
						   						)
									);

add_field($add_new_field);
// End of insert new fields into customers tables


// Chan's changes
// Create supplier_crew and supplier_crew_mac_address tables
$add_new_tables = array();

$add_new_tables["supplier_crew"] = array (	"structure" => "CREATE TABLE `supplier_crew` (
															  `supplier_crew_id` int(11) NOT NULL auto_increment,
															  `supplier_crew_firstname` varchar(32) NOT NULL default '',
															  `supplier_crew_lastname` varchar(32) NOT NULL default '',
															  `supplier_crew_username` varchar(32) NOT NULL default '',
															  `supplier_crew_password` varchar(40) NOT NULL default '',
															  `supplier_crew_status` tinyint(1) NOT NULL default '0',
															  `supplier_crew_date_account_created` datetime NOT NULL default '0000-00-00 00:00:00',
															  `supplier_crew_date_account_last_modified` datetime NOT NULL default '0000-00-00 00:00:00',
															  `supplier_id` int(11) NOT NULL default '0',
															  PRIMARY KEY  (`supplier_crew_id`)
															);" ,
											"data" => ""
											);

$add_new_tables["supplier_crew_mac_address"] = array (	"structure" => "CREATE TABLE `supplier_crew_mac_address` (
																		  `supplier_crew_mac_address_id` int(11) NOT NULL auto_increment,
																		  `supplier_crew_mac_address` varchar(255) NOT NULL default '',
																		  `supplier_crew_mac_address_status` tinyint(1) unsigned NOT NULL default '0',
																		  `request_by_supplier_crew_id` varchar(255) NOT NULL default '',
																		  `request_date` datetime default NULL,
																		  `request_ip` varchar(15) NOT NULL default '',
																		  `supplier_crew_mac_comment` varchar(255) default NULL,
																		  `supplier_id` int(11) NOT NULL default '0',
																		  PRIMARY KEY  (`supplier_crew_mac_address_id`)
																		);" ,
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create supplier_crew and supplier_crew_mac_address tables

// Insert new fields into supplier_tasks_allocation, supplier_tasks_setting, data_pool_options_tags, data_pool_options and game_char_log tables
$add_new_field = array();

$add_new_field['supplier_tasks_allocation'] = array (	array (	"field_name" => "supplier_crew_id",
																"field_attr" => " int(11) NOT NULL DEFAULT '0' ",
																"add_after" => ""
										   						)
													);

$add_new_field['supplier_tasks_setting'] = array (	array (	"field_name" => "supplier_tasks_allocation_physical_slots",
															"field_attr" => " smallint(6) NOT NULL DEFAULT '0' ",
															"add_after" => "supplier_tasks_allocation_slots"
															)
													);

$add_new_field['data_pool_options_tags'] = array (	array (	"field_name" => "data_pool_options_class",
															"field_attr" => " varchar(32) NOT NULL DEFAULT 'GENERAL' ",
															"add_after" => "data_pool_options_title"
															)
												);

$add_new_field['data_pool_options'] = array (	array (	"field_name" => "data_pool_options_class",
														"field_attr" => " varchar(32) NOT NULL DEFAULT 'GENERAL' ",
														"add_after" => "data_pool_options_title"
														)
											);

$add_new_field['game_char_log'] = array (	array (	"field_name" => "game_char_log_user_role",
													"field_attr" => " varchar(16) NOT NULL DEFAULT 'admin' ",
													"add_after" => ""
													)
										);

add_field($add_new_field);
// End of insert new fields into supplier_tasks_allocation, supplier_tasks_setting, data_pool_options_tags, data_pool_options and game_char_log tables

// Change field structure for game_char_log_user_role in game_char_log table
$change_field_structure = array();

$change_field_structure["game_char_log"] = array (array (	"field_name" => "game_char_log_user_role",
									 						"field_attr" => " varchar(16) NOT NULL DEFAULT '' "
											 			)
												);
change_field_structure ($change_field_structure);
// End of change field structure for game_char_log_user_role in game_char_log table

// Insert new records into categories_configuration table (for determine which game has API login)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Custom Product'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["GAME_REMOTE_ACCOUNT_LOGIN_API"] = array("insert" => " (0, 'Game Remote Account Login API', 'GAME_REMOTE_ACCOUNT_LOGIN_API', 'false', 'Does this game using remote account login API?', ".$row_sql["configuration_group_id"].", 10, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into categories_configuration table (for determine which game has API login)

// Insert new records into admin_files_actions table (for permission on viewing pwl clear password)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_PWL_CLEAR_TEXT_PWD"] = array("insert" => " ('ORDER_PWL_CLEAR_TEXT_PWD', 'Power Leveling Clear Text Password', ".$row_sql["admin_files_id"].", '1', 7)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing pwl clear password)


// Manipulate the orders_custom_products_value in orders_custom_products table

if (!in_array('data_pool_options_class', $existing_data_pool_options_tags_fields)) {
	$orders_custom_products_info_select_sql = "SELECT * FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " WHERE orders_custom_products_key = 'power_leveling_info'";
	$orders_custom_products_info_result_sql = tep_db_query($orders_custom_products_info_select_sql);
	
	while ($orders_custom_products_info_row = tep_db_fetch_array($orders_custom_products_info_result_sql)) {
		$account_name = "";
		$password = "";
		$char = "";
		$realm = "";
		$faction = "";
		
		$account_info_array = explode("\n", $orders_custom_products_info_row['orders_custom_products_value']);
		
		for ($account_info_count = (sizeof($account_info_array) - 1); $account_info_count >= 0; $account_info_count--) {
			$account_info_array[$account_info_count] = trim($account_info_array[$account_info_count]);
			
			if (!tep_not_null($account_name)) {
				if (preg_match('/^(Account Username:\s)([0-9a-zA-Z]+)(##1##)?/', $account_info_array[$account_info_count], $account_name_array)) {
					$account_name = $account_name_array[2];
				}
			}
			
			if (!tep_not_null($password)) {
				if (preg_match('/^(Account Password(.*?):\s)([0-9a-zA-Z]+)(##1##)?/', $account_info_array[$account_info_count], $account_password_array)) {
					$password = $account_password_array[3];
				}
			}
			
			if (!tep_not_null($char)) {
				if (preg_match('/^(Character Name:\s)([0-9a-zA-Z]+)(##1##)?/', $account_info_array[$account_info_count], $char_name_array)) {
					$char = $char_name_array[2];
				}
			}
			
			if (!tep_not_null($realm)) {
				if (preg_match('/^(Realm Name:\s)([0-9a-zA-Z]+)(##1##)?/', $account_info_array[$account_info_count], $realm_name_array)) {
					$realm = $realm_name_array[2];
				}
			}
			
			if (!tep_not_null($faction)) {
				if (preg_match('/^(Faction:\s)([0-9a-zA-Z]+)(##1##)?/', $account_info_array[$account_info_count], $faction_name_array)) {
					$faction = $faction_name_array[2];
				}
			}
		}
		
		if (tep_not_null($account_name)) {
			$account_name_data_array = array('products_id' => $orders_custom_products_info_row['products_id'],
											 'orders_products_id' => $orders_custom_products_info_row['orders_products_id'],
											 'orders_custom_products_key' => 'power_leveling_account_username',
											 'orders_custom_products_value' => $account_name,
											 'orders_custom_products_number' => $orders_custom_products_info_row['orders_custom_products_number']
											 );
						          		
			tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $account_name_data_array);
		}
		
		if (tep_not_null($password)) {
			$password_data_array = array('products_id' => $orders_custom_products_info_row['products_id'],
										 'orders_products_id' => $orders_custom_products_info_row['orders_products_id'],
										 'orders_custom_products_key' => 'power_leveling_account_password',
										 'orders_custom_products_value' => $password,
										 'orders_custom_products_number' => $orders_custom_products_info_row['orders_custom_products_number']
										 );
			tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $password_data_array);
		}
		
		if (tep_not_null($char)) {
			$char_data_array = array('products_id' => $orders_custom_products_info_row['products_id'],
								     'orders_products_id' => $orders_custom_products_info_row['orders_products_id'],
								     'orders_custom_products_key' => 'power_leveling_account_character_name',
								     'orders_custom_products_value' => $char,
								     'orders_custom_products_number' => $orders_custom_products_info_row['orders_custom_products_number']
								     );
			tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $char_data_array);
		}
		
		if (tep_not_null($realm)) {
			$realm_data_array = array('products_id' => $orders_custom_products_info_row['products_id'],
									  'orders_products_id' => $orders_custom_products_info_row['orders_products_id'],
									  'orders_custom_products_key' => 'power_leveling_account_realm',
									  'orders_custom_products_value' => $realm,
									  'orders_custom_products_number' => $orders_custom_products_info_row['orders_custom_products_number']
									  );
						          		
			tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $realm_data_array);
		}
		
		if (tep_not_null($faction)) {	
			$faction_data_array = array('products_id' => $orders_custom_products_info_row['products_id'],
									    'orders_products_id' => $orders_custom_products_info_row['orders_products_id'],
									    'orders_custom_products_key' => 'power_leveling_account_faction',
									    'orders_custom_products_value' => $faction,
									    'orders_custom_products_number' => $orders_custom_products_info_row['orders_custom_products_number']
									    );
						          		
			tep_db_perform(TABLE_ORDERS_CUSTOM_PRODUCTS, $faction_data_array);
		}
	}
}

// End of manipulate the orders_custom_products_value in orders_custom_products table
?>