<?
/*
	$Id: version_2_4_1.php,v 1.4 2006/11/08 09:47:22 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create status_configuration table
$add_new_tables = array();

$add_new_tables["status_configuration"] = array (	"structure" => "CREATE TABLE `status_configuration` (
																		`status_configuration_trans_type` varchar(5) NOT NULL default '',
																		`status_configuration_source_status_id` int(11) NOT NULL default '0',
																		`status_configuration_destination_status_id` int(11) NOT NULL default '0',
																		`status_configuration_user_groups_id` text NOT NULL,
																		`status_configuration_manual_notification` varchar(255) NOT NULL,
																		`status_configuration_auto_notification` varchar(255) NOT NULL,
																		PRIMARY KEY (`status_configuration_trans_type`, `status_configuration_source_status_id`, `status_configuration_destination_status_id`)
																	) TYPE=MyISAM COMMENT='Set permission to change transaction status and notification setting';" ,
													"data" => "	INSERT INTO `status_configuration` (status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification) 
																VALUES 
																	('C', '1', '7', '1,2,17,24,20,32', '', ''),
																  	('C', '1', '2', '1,2,17,24,20,32', '', ''),
																  	('C', '1', '5', '1,2,17,24,20,32', '', ''),
																  	('C', '7', '1', '1,2,17,24,20,32', '', ''),
																  	('C', '7', '2', '1,2,17,24,20,32', '', ''),
																  	('C', '7', '5', '1,2,17,24,20,32', '', ''),
																  	('C', '7', '6', '1,2,17,24,20,32', '', ''),
																  	('C', '2', '7', '1,2,17,24,27,32', '', ''),
																  	('C', '2', '1', '1,2,17,24,27,32', '', ''),
																  	('C', '2', '3', '1,2,17,24,20,22,27,32', '', ''),
																  	('C', '2', '6', '1,2,17,24', '', ''),
																  	('C', '3', '2', '1,2,17,24,27,32', '', ''),
																  	('C', '3', '4', '1,2,17,24,20,32', '', ''),
																  	('C', '4', '3', '1,2,17,24,27,32', '', ''),
																  	('C', '5', '1', '1,2,17,24', '', ''),
																  	('C', '6', '7', '1,2,17,24', '', ''),
																  	('C', '6', '2', '1,2,17,24', '', '');"
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create status_configuration table

// Insert new records into admin_files table (for status configuration)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='administrator.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["orders_status_conf.php"] = array(	"insert" => " ('orders_status_conf.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   										);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='orders_status_conf.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for status configuration)

// chan's changes
// Rename the pwl key
$orders_custom_products_info_select_sql = "	SELECT orders_custom_products_id  
											FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " 
											WHERE orders_custom_products_key ='power_leveling_account_account_username' 
												OR orders_custom_products_key ='power_leveling_account_account_password'
											LIMIT 1";
$orders_custom_products_info_result_sql = tep_db_query($orders_custom_products_info_select_sql);

if (!tep_db_num_rows($orders_custom_products_info_result_sql)) {
	$orders_custom_products_username_update_sql = "	UPDATE " . TABLE_ORDERS_CUSTOM_PRODUCTS . " 
													SET orders_custom_products_key = 'power_leveling_account_account_username'
													WHERE orders_custom_products_key ='power_leveling_account_username'";
	tep_db_query($orders_custom_products_username_update_sql);
	
	$orders_custom_products_pwd_update_sql = "	UPDATE " . TABLE_ORDERS_CUSTOM_PRODUCTS . " 
												SET orders_custom_products_key = 'power_leveling_account_account_password'
												WHERE orders_custom_products_key ='power_leveling_account_password'";
	tep_db_query($orders_custom_products_pwd_update_sql);
}

// Define products_id as index key in orders_products table
add_index_key (TABLE_ORDERS_PRODUCTS, 'index_products_id', 'index', 'products_id', $DBTables);
// End of define products_id as index key in orders_products table

?>