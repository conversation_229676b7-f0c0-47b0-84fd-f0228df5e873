<?
/*
	$Id: version_2_4_2.php,v 1.1 2006/11/20 10:28:20 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on uncheck flagged option)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_UNCHECK_FLAG"] = array("insert" => " ('CUSTOMER_UNCHECK_FLAG', 'Uncheck customer flag', ".$row_sql["admin_files_id"].", '1', 6)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on uncheck flagged option)

// Update existing inactive customer with flag turned On to flag=4 (Chargeback)
$customers_remark = 'Changes made:' . "\n".
					'<span class="flagPink">Non-reversible Payment Only Flag:</span> Off --> On (<span class="redIndicator">Accept irreversible payment only</span>)' . "\n".
					'<span class="flagRed">Chargeback Flag:</span> Off --> On (<span class="redIndicator">Email CSL immediately. Do not process yet.</span>)';

$cb_customer_select_sql = "	SELECT customers_id 
							FROM customers 
							WHERE customers_status = 0 
								AND customers_flag = 'On'";
$cb_customer_result_sql = tep_db_query($cb_customer_select_sql);
while ($cb_customer_row = tep_db_fetch_array($cb_customer_result_sql)) {
	$cust_remarks_data_array = array(	'customers_id' => $cb_customer_row['customers_id'],
                						'date_remarks_added' => 'now()',
                        				'remarks' => $customers_remark,
                        				'remarks_added_by' => 'system');
	tep_db_perform('customers_remarks_history', $cust_remarks_data_array);
}

$cb_customers_update_sql = "	UPDATE customers 
								SET customers_flag = '1,2,4',
									customers_status = 1 
								WHERE customers_status = 0
									AND customers_flag = 'On'";
tep_db_query($cb_customers_update_sql);
// End of update existing inactive customer with flag turned On to flag=4 (Chargeback)

// Update the rest of customer with flag turned On to flag=1 (Normal Flag)
$flag_customers_update_sql = "	UPDATE customers 
								SET customers_flag = '1' 
								WHERE customers_flag = 'On'";
tep_db_query($flag_customers_update_sql);
// End of update the rest of customer with flag turned On to flag=1 (Normal Flag)

// Create user_flags table
$add_new_tables = array();

$add_new_tables["user_flags"] = array (	"structure" => "CREATE TABLE `user_flags` (
															`user_flags_id` int(11) NOT NULL auto_increment,
															`user_flags_name` varchar(128) NOT NULL default '',
															`user_flags_description` varchar(255) NOT NULL default '',
															`user_flags_alias` varchar(12) NOT NULL default '',
															`user_flags_status` tinyint(1) NOT NULL default '0',
															`user_flags_css_style` varchar(255) NOT NULL,
															PRIMARY KEY (`user_flags_id`)
														) TYPE=MyISAM COMMENT='A list of flags used to flag users';" ,
										"data" => "	INSERT INTO `user_flags` (user_flags_id, user_flags_name, user_flags_description, user_flags_alias, user_flags_status, user_flags_css_style) 
													VALUES 
														('1', 'Flag', '', 'FLG', 1, 'flagOrange'),
													  	('2', 'Non-reversible Payment Only', 'Accept irreversible payment only', 'NCP', 1, 'flagPink'),
													  	('3', 'On Hold', '', 'OH', 0, ''),
													  	('4', 'Chargeback', 'Email CSL immediately. Do not process yet.', 'CB', 1, 'flagRed');"
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create user_flags table
?>