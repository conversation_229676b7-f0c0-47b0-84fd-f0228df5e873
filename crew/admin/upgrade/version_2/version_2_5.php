<?
/*
	$Id: version_2_5.php,v 1.1 2006/11/22 09:23:31 weichen Exp $
	
  	Developer: <PERSON> (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/


$existing_products_fields = get_table_fields("products");

$existing_categories_fields = get_table_fields("categories");

// Create user_flags table
$add_new_tables = array();

$add_new_tables["char_honor_history"] = array (	"structure" => "CREATE TABLE `char_honor_history` (
																  `char_honor_history_id` int(11) NOT NULL auto_increment,
																  `game_char_id` int(11) NOT NULL default '0',
																  `game_char_history_id` int(11) NOT NULL default '0',
																  `current_honor_description` varchar(64) NOT NULL default '',
																  `current_honor_progress` varchar(64) NOT NULL default '',
																  `current_honor_texture` varchar(64) NOT NULL default '',
																  `current_honor_rank` varchar(64) NOT NULL default '0',
																  `yesterday_honorable_kill` int(11) NOT NULL default '0',
																  `yesterday_dishonorable_kill` int(11) NOT NULL default '0',
																  `yesterday_honor_contribution` int(11) NOT NULL default '0',
																  `this_week_honorable_kill` int(11) NOT NULL default '0',
																  `this_week_honor_contribution` int(11) NOT NULL default '0',
																  `last_week_honorable_kill` int(11) NOT NULL default '0',
																  `last_week_dishonorable_kill` int(11) NOT NULL default '0',
																  `last_week_honor_rank` int(11) NOT NULL default '0',
																  `last_week_honor_contribution` int(11) NOT NULL default '0',
																  `life_time_honor_name` varchar(64) NOT NULL default '0',
																  `life_time_honorable_kill` int(11) NOT NULL default '0',
																  `life_time_dishonorable_kill` int(11) NOT NULL default '0',
																  `life_time_rank` int(11) NOT NULL default '0',
																  `session_honorable_kill` int(11) NOT NULL default '0',
																  `session_dishonorable_kill` int(11) NOT NULL default '0',
																  PRIMARY KEY  (`char_honor_history_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["char_item_history"] = array (	"structure" => "CREATE TABLE `char_item_history` (
																  `char_item_history_id` int(11) NOT NULL auto_increment,
																  `game_char_id` int(11) NOT NULL default '0',
																  `game_char_history_id` int(11) NOT NULL default '0',
																  `char_item_name` varchar(64) NOT NULL default '',
																  `char_item_parent` varchar(64) NOT NULL default '',
																  `char_item_slot_available` varchar(32) NOT NULL default '',
																  `char_item_color` varchar(16) NOT NULL default '',
																  `char_item` varchar(16) NOT NULL default '',
																  `char_item_texture` varchar(64) NOT NULL default '',
																  `char_item_tooltip` mediumtext NOT NULL,
																  PRIMARY KEY  (`char_item_history_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["char_item_storage"] = array (	"structure" => "CREATE TABLE `char_item_storage` (
																  `char_item_storage_id` int(11) NOT NULL auto_increment,
																  `char_item_history_id` int(11) NOT NULL default '0',
																  `char_item_storage_name` varchar(64) NOT NULL default '',
																  `char_item_storage_slot` varchar(32) NOT NULL default '',
																  `char_item_storage_color` varchar(16) NOT NULL default '',
																  `char_item_storage` varchar(16) NOT NULL default '',
																  `char_item_storage_texture` varchar(64) NOT NULL default '',
																  `char_item_storage_quantity` varchar(11) NOT NULL default '',
																  `char_item_storage_tooltip` mediumtext NOT NULL,
																  PRIMARY KEY  (`char_item_storage_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["char_quest_history"] = array (	"structure" => "CREATE TABLE `char_quest_history` (
																  `char_quest_history_id` int(11) NOT NULL auto_increment,
																  `game_char_id` int(11) NOT NULL default '0',
																  `game_char_history_id` int(11) NOT NULL default '0',
																  `char_quest_location` varchar(64) NOT NULL default '',
																  `char_quest_order` int(11) NOT NULL default '0',
																  `char_quest_level` varchar(16) NOT NULL default '',
																  `char_quest_title` varchar(64) NOT NULL default '',
																  PRIMARY KEY  (`char_quest_history_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["char_reputation_detail"] = array (	"structure" => "CREATE TABLE `char_reputation_detail` (
																	  `char_reputation_history_id` int(11) NOT NULL default '0',
																	  `char_reputation_detail_category` varchar(64) NOT NULL default '',
																	  `char_reputation_detail_name` varchar(64) NOT NULL default '',
																	  `char_reputation_detail_standing` varchar(64) NOT NULL default '',
																	  `char_reputation_detail_value` varchar(64) NOT NULL default '',
																	  `char_reputation_detail_at_war` varchar(64) NOT NULL default '',
																	  PRIMARY KEY  (`char_reputation_history_id`,`char_reputation_detail_category`,`char_reputation_detail_name`)
																	) ENGINE=MyISAM ;" ,
													"data" => ""
												);

$add_new_tables["char_reputation_history"] = array ("structure" => "CREATE TABLE `char_reputation_history` (
																	  `char_reputation_history_id` int(11) NOT NULL auto_increment,
																	  `game_char_id` int(11) NOT NULL default '0',
																	  `game_char_history_id` int(11) NOT NULL default '0',
																	  PRIMARY KEY  (`char_reputation_history_id`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["char_skill_history"] = array (	"structure" => "CREATE TABLE `char_skill_history` (
																  `char_skill_history_id` int(11) NOT NULL auto_increment,
																  `game_char_id` int(11) NOT NULL default '0',
																  `game_char_history_id` int(11) NOT NULL default '0',
																  `char_skill_type` varchar(32) NOT NULL default '',
																  `char_skill_name` varchar(32) NOT NULL default '',
																  `char_skill_order` int(11) NOT NULL default '0',
																  `char_skill_level` varchar(16) NOT NULL default '',
																  PRIMARY KEY  (`char_skill_history_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
												);

$add_new_tables["game_char"] = array (	"structure" => "CREATE TABLE `game_char` (
														  `game_char_id` int(11) NOT NULL auto_increment,
														  `name` varchar(64) NOT NULL default '',
														  `server` varchar(32) NOT NULL default '',
														  PRIMARY KEY  (`game_char_id`)
														) ENGINE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["game_char_history"] = array (	"structure" => "CREATE TABLE `game_char_history` (
																  `game_char_history_id` int(11) NOT NULL auto_increment,
																  `game_char_id` int(11) NOT NULL default '0',
																  `stat_int` varchar(32) NOT NULL default '',
																  `stat_agl` varchar(32) NOT NULL default '',
																  `stat_sta` varchar(32) NOT NULL default '',
																  `stat_str` varchar(32) NOT NULL default '',
																  `stat_spr` varchar(32) NOT NULL default '',
																  `guild_name` varchar(64) NOT NULL default '',
																  `guild_title` varchar(64) NOT NULL default '',
																  `guild_rank` varchar(11) NOT NULL default '0',
																  `race` varchar(32) NOT NULL default '',
																  `res_holy` varchar(32) NOT NULL default '',
																  `res_frost` varchar(32) NOT NULL default '',
																  `res_arcane` varchar(32) NOT NULL default '',
																  `res_fire` varchar(32) NOT NULL default '',
																  `res_shadow` varchar(32) NOT NULL default '',
																  `res_nature` varchar(32) NOT NULL default '',
																  `armor` varchar(32) NOT NULL default '',
																  `level` int(11) NOT NULL default '0',
																  `defense` int(11) NOT NULL default '0',
																  `talent_points` int(11) NOT NULL default '0',
																  `money_c` int(11) NOT NULL default '0',
																  `money_s` int(11) NOT NULL default '0',
																  `money_g` int(11) NOT NULL default '0',
																  `exp` varchar(32) NOT NULL default '',
																  `class` varchar(32) NOT NULL default '',
																  `health` int(11) NOT NULL default '0',
																  `melee_power` int(11) default NULL,
																  `melee_rating` int(11) default NULL,
																  `melee_range` varchar(16) default NULL,
																  `melee_range_tooltip` tinytext,
																  `melee_power_tooltip` tinytext,
																  `ranged_power` int(11) default NULL,
																  `ranged_rating` int(11) default NULL,
																  `ranged_range` varchar(16) default NULL,
																  `ranged_range_tooltip` tinytext,
																  `ranged_power_tooltip` tinytext,
																  `version` varchar(16) default NULL,
																  `game_char_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
																  PRIMARY KEY  (`game_char_history_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create user_flags table


// Sunny's changes
// Insert new fields into products and categories tables (for SEO alias)
$add_new_field = array();

$add_new_field['products'] = array (	array (	"field_name" => "products_url_alias",
												"field_attr" => " varchar(255) NOT NULL ",
												"add_after" => "products_cat_path"
												)
									);

$add_new_field['categories'] = array (	array (	"field_name" => "categories_url_alias",
												"field_attr" => " varchar(255) NOT NULL ",
												"add_after" => "categories_status"
												)
									);
									
add_field($add_new_field);
// End of insert new fields into products and categories tables (for SEO alias)

// Prefill products_url_alias value
if (!in_array('products_url_alias', $existing_products_fields)) {
	$products_select_sql = "SELECT products_id, products_name 
							FROM " . TABLE_PRODUCTS_DESCRIPTION . " 
							WHERE language_id='1' 
							ORDER BY products_id";
	$products_result_sql = tep_db_query($products_select_sql);
	while ($products_row = tep_db_fetch_array($products_result_sql)) {
		$products_url_alias = strip_tags($products_row['products_name']);
		$products_url_alias = tep_translate_special_character($products_url_alias);
		
		$dash = array("/", "_", "'", "~", ",", ":", " ", "-");
		$join = array("(", ")", "!", "%", ".");
		
		$products_url_alias = str_replace($dash, '-', $products_url_alias); // replace to dash
		$products_url_alias = str_replace($join, '', $products_url_alias); // join text
		
		while (strstr($products_url_alias, '--')) {
			$products_url_alias = str_replace('--', '-', $products_url_alias); // '--' -> '-'
		}
		
		$url_alias_update_sql = "	UPDATE products 
									SET products_url_alias = '". tep_db_input(strtolower($products_url_alias)) ."'
									WHERE products_id = '" . tep_db_input($products_row['products_id']) . "'";
		tep_db_query($url_alias_update_sql);
	}
}
// End of prefill products_url_alias value

// Prefill categories_url_alias value
if (!in_array('categories_url_alias', $existing_categories_fields)) {
	$categories_select_sql = "	SELECT categories_id, categories_name 
								FROM " . TABLE_CATEGORIES_DESCRIPTION . " 
								WHERE language_id='1' 
								ORDER BY categories_id";
	$categories_result_sql = tep_db_query($categories_select_sql);
	while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
		$categories_url_alias = strip_tags($categories_row['categories_name']);
		$categories_url_alias = tep_translate_special_character($categories_url_alias);
		
		$dash = array("/", "_", "'", "~", ",", ":", " ", "-");
		$join = array("(", ")", "!", "%", ".");
		
		$categories_url_alias = str_replace($dash, '-', $categories_url_alias); // replace to dash
		$categories_url_alias = str_replace($join, '', $categories_url_alias); // join text
		
		while (strstr($categories_url_alias, '--')) {
			$categories_url_alias = str_replace('--', '-', $categories_url_alias); // '--' -> '-'
		}
		
		$url_alias_update_sql = "UPDATE ". TABLE_CATEGORIES ."
								SET categories_url_alias = '". tep_db_input(strtolower($categories_url_alias)) ."'
								WHERE categories_id = '" . $categories_row['categories_id'] . "'";
		
		tep_db_query($url_alias_update_sql);
	}
}
// End of prefill categories_url_alias value

?>