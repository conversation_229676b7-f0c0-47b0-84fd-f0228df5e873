<?
/*
	$Id: version_2_5_1.php,v 1.2 2006/11/27 07:09:43 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_game_char_fields = get_table_fields("game_char");

// Create character's pet tables
$add_new_tables = array();

$add_new_tables["char_pet"] = array (	"structure" => "CREATE TABLE `char_pet` (
														  `char_pet_id` int(11) NOT NULL auto_increment,
														  `game_char_id` int(11) NOT NULL default '0',
														  `char_pet_name` varchar(64) NOT NULL default '',
														  PRIMARY KEY  (`char_pet_id`)
														) ENGINE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["char_pet_history"] = array (	"structure" => "CREATE TABLE `char_pet_history` (
																	`char_pet_id` int(11) NOT NULL default '0',
																	`game_char_history_id` int(11) NOT NULL default '0',
																	`char_pet_stat_int` varchar(32) NOT NULL default '',
																	`char_pet_stat_agl` varchar(32) NOT NULL default '',
																	`char_pet_stat_sta` varchar(32) NOT NULL default '',
																	`char_pet_stat_str` varchar(32) NOT NULL default '',
																	`char_pet_stat_armor` varchar(32) NOT NULL default '',
																	`char_pet_stat_spr` varchar(32) NOT NULL default '',
																	`char_pet_melee_power` int(11) NOT NULL default '0',
																	`char_pet_melee_rating` int(11) NOT NULL default '0',
																	`char_pet_melee_range` varchar(32) NOT NULL default '',
																	`char_pet_melee_range_tooltip` mediumtext NOT NULL,
																	`char_pet_melee_power_tooltip` mediumtext NOT NULL,
																	`char_pet_armor` varchar(32) NOT NULL default '',
																	`char_pet_level` int(11) NOT NULL default '0',
																	`char_pet_loyalty` varchar(64) NOT NULL default '',
																	`char_pet_defense` int(11) NOT NULL default '0',
																	`char_pet_talent_points` int(11) NOT NULL default '0',
																	`char_pet_food_types` varchar(128) NOT NULL default '',
																	`char_pet_power` varchar(32) NOT NULL default '',
																	`char_pet_type` varchar(32) NOT NULL default '',
																	`char_pet_health` int(11) NOT NULL default '0',
																	`char_pet_exp` varchar(32) NOT NULL default '',
																	`char_pet_res_holy` varchar(32) NOT NULL default '',
																	`char_pet_res_arcane` varchar(32) NOT NULL default '',
																	`char_pet_res_shadow` varchar(32) NOT NULL default '',
																	`char_pet_res_fire` varchar(32) NOT NULL default '',
																	`char_pet_res_frost` varchar(32) NOT NULL default '',
																	`char_pet_res_nature` varchar(32) NOT NULL default '',
																	PRIMARY KEY  (`char_pet_id`,`game_char_history_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create character's pet tables

// Insert new fields into game_char table
$add_new_field = array();

$add_new_field['game_char'] = array (	array (	"field_name" => "orders_products_id",
												"field_attr" => " int(11) NOT NULL DEFAULT '0' ",
												"add_after" => "game_char_id"
												)
									);

add_field($add_new_field);
// End of insert new fields into game_char table

// Insert orders_products_id in game_char table
if (!in_array('orders_products_id', $existing_game_char_fields)) {
	$game_char_info_select_sql = " SELECT game_char_id, name, server FROM game_char";
	$game_char_info_result_sql = tep_db_query($game_char_info_select_sql);
	
	while ($game_char_info_row = tep_db_fetch_array($game_char_info_result_sql)) {
		$orders_products_id_select_sql = "	SELECT ocpAccount.orders_products_id 
											FROM orders_custom_products AS ocpAccount 
											INNER JOIN orders_custom_products AS ocpRealm 
												ON (ocpRealm.orders_products_id = ocpAccount.orders_products_id 
													AND ocpRealm.orders_custom_products_key ='power_leveling_account_realm' 
													AND ocpRealm.orders_custom_products_value='" . tep_db_input($game_char_info_row['server']) . "')
											WHERE ocpAccount.orders_custom_products_key ='power_leveling_account_character_name' 
												AND ocpAccount.orders_custom_products_value ='" . tep_db_input($game_char_info_row['name']) . "' 
											ORDER BY ocpAccount.orders_products_id DESC LIMIT 1";
		
		$orders_products_id_result_sql = tep_db_query($orders_products_id_select_sql);
		$orders_products_id_row = tep_db_fetch_array($orders_products_id_result_sql);
		
		$sql_data_array = array('orders_products_id' => $orders_products_id_row['orders_products_id']);
		
		tep_db_perform('game_char', $sql_data_array, 'update', "game_char_id = '" . (int)$game_char_info_row['game_char_id'] . "'");
	}
}
// End of insert orders_products_id in game_char table
?>