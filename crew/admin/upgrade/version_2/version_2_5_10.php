<?
/*
	$Id: version_2_5_10.php,v 1.1 2007/01/23 07:41:01 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into maxmind_telephone_identification table (Cater for International call)
$add_new_field = array();

$add_new_field['maxmind_telephone_identification'] = array (	array (	"field_name" => "city",
																		"field_attr" => " varchar(32) NULL DEFAULT NULL ",
																		"add_after" => "customers_telephone_type"
																		),
																array (	"field_name" => "state",
																		"field_attr" => " varchar(32) NULL DEFAULT NULL ",
																		"add_after" => "city"
																		),
																array (	"field_name" => "postcode",
																		"field_attr" => " varchar(10) NULL DEFAULT NULL ",
																		"add_after" => "state"
																		),
																array (	"field_name" => "countries_name",
																		"field_attr" => " varchar(64) NULL DEFAULT NULL ",
																		"add_after" => "postcode"
																		),
																array (	"field_name" => "latitude",
																		"field_attr" => " decimal(10, 4) NULL DEFAULT NULL ",
																		"add_after" => "countries_name"
																		),
																array (	"field_name" => "longitude",
																		"field_attr" => " decimal(10, 4) NULL DEFAULT NULL ",
																		"add_after" => "latitude"
																		)
															);

add_field($add_new_field);
// End of insert new fields into maxmind_telephone_identification table (Cater for International call)

?>