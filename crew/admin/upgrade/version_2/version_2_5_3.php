<?
/*
	$Id: version_2_5_3.php,v 1.1 2006/12/06 08:43:09 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/


// Insert new records into admin_files table (for IP Zone definition)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='localization.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["ip_zones.php"] = array(	"insert" => " ('ip_zones.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   								);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='ip_zones.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for IP Zone definition)

// Create character's pet tables
$add_new_tables = array();

$add_new_tables["defined_ip_zones"] = array (	"structure" => "CREATE TABLE `defined_ip_zones` (
																  `defined_ip_zones_id` int(11) NOT NULL auto_increment,
																  `defined_ip_zones_name` varchar(32) NOT NULL default '',
																  `defined_ip_zones_description` varchar(255) NOT NULL default '',
																  `defined_ip_zones_last_modified` datetime default NULL,
																  `defined_ip_zones_date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																  PRIMARY KEY  (`defined_ip_zones_id`)
																) ENGINE=MyISAM;",
												"data" => ""
											);

$add_new_tables["ip_to_defined_ip_zones"] = array (	"structure" => "CREATE TABLE `ip_to_defined_ip_zones` (
																	  `ip_to_defined_ip_zones_id` int(11) NOT NULL auto_increment,
																	  `ip_address` varchar(15) NOT NULL default '',
																	  `subnet` char(2) NOT NULL default '32',
																	  `defined_ip_zones_id` int(11) NOT NULL default '0',
																	  `ip_to_defined_ip_zones_last_modified` datetime default NULL,
																	  `ip_to_defined_ip_zones_date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																	  PRIMARY KEY  (`ip_to_defined_ip_zones_id`)
																	) ENGINE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create character's pet tables

?>