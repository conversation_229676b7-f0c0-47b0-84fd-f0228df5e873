<?
/*
	$Id: version_2_5_4.php,v 1.2 2006/12/12 06:36:37 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into char_honor_history table
$add_new_field = array();

$add_new_field['char_honor_history'] = array (	array (	"field_name" => "yesterday_honor",
														"field_attr" => " int(11) NOT NULL DEFAULT '0' ",
														"add_after" => "yesterday_honorable_kill"
														),
												array (	"field_name" => "session_honor",
														"field_attr" => " int(11) NOT NULL DEFAULT '0' ",
														"add_after" => "session_honorable_kill"
														)
											);

add_field($add_new_field);
// End of insert new fields into char_honor_history table

// Insert new records into configuration table (for WorldPay Manual Post Auth Notification)
$conf_insert_sql = array();
$conf_insert_sql["MODULE_PAYMENT_WORLDPAY_MANUAL_AUTH_NOTIFICATION_EMAIL"] = array("insert" => " ('Manual Post Auth Notification', 'MODULE_PAYMENT_WORLDPAY_MANUAL_AUTH_NOTIFICATION_EMAIL', '', 'Email address to which the notification email will be send to when someone manually authorise WorldPay payment.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 6, 220, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for WorldPay Manual Post Auth Notification)
?>