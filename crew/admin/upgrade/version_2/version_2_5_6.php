<?
/*
	$Id: version_2_5_6.php,v 1.1 2006/12/20 05:26:46 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for RedJack Version Access Control)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Custom Product'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["REMOTE_REDJACK_LEAST_SUPPORT_VERSION"] = array("insert" => " ('RedJack Least Support Version', 'REMOTE_REDJACK_LEAST_SUPPORT_VERSION', '1.1', 'This is supposed to be updated when there is a new RedJack update.', ".$row_sql["configuration_group_id"].", 20, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for RedJack Version Access Control)

?>