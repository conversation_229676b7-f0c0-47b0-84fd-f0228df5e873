<?
/*
	$Id: version_2_5_8.php,v 1.2 2007/01/15 07:13:37 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into customers_info table (Account creation IP)
$add_new_field = array();

$add_new_field['customers_info'] = array (	array (	"field_name" => "customers_info_account_created_ip",
													"field_attr" => " varchar(20) default NULL ",
													"add_after" => "customers_info_date_account_created"
													)
										);

add_field($add_new_field);
// End of insert new fields into customers_info table (Account creation IP)

// Create maxmind_telephone_identification tables
$add_new_tables = array();

$add_new_tables["maxmind_telephone_identification"] = array (	"structure" => "CREATE TABLE `maxmind_telephone_identification` (
																				  `customers_id` int(11) NOT NULL default '0',
																				  `customers_country_international_dialing_code` int(11) NOT NULL default '0',
																				  `customers_telephone` varchar(32) NOT NULL default '',
																				  `customers_telephone_type` tinyint(1) NOT NULL default '0',
																				  `error` varchar(64) NOT NULL default '',
																				  `maxmind_telephone_identification_id` varchar(64) NOT NULL default '',
																				  `requested_date` datetime NOT NULL default '0000-00-00 00:00:00',
																				  PRIMARY KEY  (`customers_id`,`customers_telephone`,`customers_country_international_dialing_code`)
																				) ENGINE=MyISAM;",
																"data" => ""
															);

add_new_tables ($add_new_tables, $DBTables);
// End of create maxmind_telephone_identification tables

// Insert new records into configuration table (for MaxMind Phone Type Identification)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='MaxMind'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["MAXMIND_PHONE_TYPE_IDENTIFICATION_ENABLED"] = array("insert" => " ('Allow MaxMind Phone Type Identification', 'MAXMIND_PHONE_TYPE_IDENTIFICATION_ENABLED', 'false', 'Enable MaxMind phone type identification in edit order page?', ".$row_sql["configuration_group_id"].", 15, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for MaxMind Phone Type Identification)

?>