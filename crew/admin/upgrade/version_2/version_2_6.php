<?
/*
	$Id: version_2_6.php,v 1.5 2007/01/29 07:43:26 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_supplier_order_lists_fields = get_table_fields("supplier_order_lists");
$existing_buyback_request_fields = get_table_fields("buyback_request");

// Update records in admin_files table (rename ip_zones.php tp ip_zone.php)
$admin_file_update_sql = array();
$admin_file_update_sql[TABLE_ADMIN_FILES] = array(	array(	"field_name" => "admin_files_name",
															"update" => " admin_files_name='ip_zone.php' ",
															"where_str" => " TRIM(LCASE(admin_files_name)) = 'ip_zones.php' AND admin_files_is_boxes=0")
												 );

advance_update_records($admin_file_update_sql, $DBTables);
// End of update records in admin_files table (rename ip_zones.php tp ip_zone.php)

// Insert new records into admin_files table (for payment module management page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='modules.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["payment_module.php"] = array(	"insert" => " ('payment_module.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   									);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='payment_module.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for payment module management page)

// Insert new records into admin_files table (for payments transaction)
$admin_files_insert_sql = array();
$admin_files_insert_sql["payments.php"] = array(	"insert" => " ('payments.php', 1, 0, '1') ",
													"update" => " admin_files_is_boxes=1, admin_files_to_boxes=0, admin_groups_id='1' "
				   								);

insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='payments.php' AND admin_files_is_boxes=1 ");
// End of insert new records into admin_files table (for payments transaction)

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='payments.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for account statement)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["account_statement.php"] = array(	"insert" => " ('account_statement.php', 0, '".$row_sql[admin_files_id]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   										);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='account_statement.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for account statement)
	
	// Insert new records into admin_files table (for payment)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["payment.php"] = array(	"insert" => " ('payment.php', 0, '".$row_sql[admin_files_id]."', '1') ",
													"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql[admin_files_id]."', admin_groups_id='1' "
					   							);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='payment.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for payment)
}


// Create new tables
$add_new_tables = array();

$add_new_tables["payment_methods"] = array (	"structure" => "CREATE TABLE `payment_methods` (
																  `payment_methods_id` int(11) NOT NULL auto_increment,
																  `payment_methods_code` varchar(20) NULL,
																  `payment_methods_send_status` tinyint(1) NOT NULL default '0',
																  `payment_methods_send_mode_name` varchar(20) NULL,
																  `payment_methods_send_required_info` text,
																  `payment_methods_send_currency` char(3) NOT NULL default '',
																  `payment_methods_estimated_receive_period` int(11) unsigned NOT NULL default '0',
																  `payment_methods_send_mass_payment` tinyint(1) NOT NULL default '0',
																  `payment_methods_receive_status` tinyint(1) NOT NULL default '0',
																  `payment_methods_sort_order` int(5) NOT NULL default '50000',
																  PRIMARY KEY(`payment_methods_id`)
																) TYPE=MyISAM;",
												"data" => ""
											);

$add_new_tables["payment_fees"] = array (	"structure" => "CREATE TABLE `payment_fees` (
																`payment_methods_id` int(11) NOT NULL default '0',
															  	`payment_methods_mode` varchar(10) NOT NULL default 'SEND',
															  	`payment_fees_max` decimal(15,4) NOT NULL default '0.0000',
															  	`payment_fees_min` decimal(15,4) NOT NULL default '0.0000',
															  	`payment_fees_cost_value` decimal(8,4) NOT NULL default '0.0000',
															  	`payment_fees_cost_percent` decimal(6,2) NOT NULL default '0.00',
															  	`payment_fees_cost_percent_min` decimal(8,2) NOT NULL default '0.00',
															  	`payment_fees_cost_percent_max` decimal(8,2) NOT NULL default '0.00',
															  	`payment_fees_bear_by` varchar(20) NOT NULL default 'beneficiary',
															  	`payment_fees_below_min` VARCHAR(20) NOT NULL default 'beneficiary',
															  	PRIMARY KEY(`payment_methods_id`, `payment_methods_mode`),
															  	KEY `idx_payment_methods_mode` (`payment_methods_mode`)
															) TYPE=MyISAM;",
											"data" => ""
										);

$add_new_tables["payment_methods_fields"] = array (	"structure" => "CREATE TABLE `payment_methods_fields` (
																	  	`payment_methods_fields_id` int(11) NOT NULL auto_increment,
																	  	`payment_methods_id` int(11) NOT NULL default '0',
																	  	`payment_methods_mode` varchar(10) NOT NULL default 'SEND',
																	  	`payment_methods_fields_title` varchar(255) NOT NULL default '',
																	  	`payment_methods_fields_pre_info` varchar(255) NOT NULL default '',
																	  	`payment_methods_fields_post_info` varchar(255) NOT NULL default '',
																	  	`payment_methods_fields_required` tinyint(1) NOT NULL default '1',
																	  	`payment_methods_fields_type` int(11) NOT NULL default '0',
																	  	`payment_methods_fields_size` varchar(32) default '',
																	  	`payment_methods_fields_status` tinyint(1) NOT NULL default '0',
																	  	`payment_methods_fields_option` text,
																	  	`payment_methods_fields_options_title` tinyint(1) NOT NULL default '0',
																	  	`payment_methods_fields_sort_order` int(5) NOT NULL default '50000',
																	  	PRIMARY KEY(`payment_methods_fields_id`),
																	  	KEY `idx_payment_methods_id` (payment_methods_id)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["store_account_balance"] = array (	"structure" => "CREATE TABLE `store_account_balance` (
																	  	`user_id` int(11) NOT NULL default '0',
																	  	`user_role` varchar(16) NOT NULL default '',
																	  	`store_account_balance_currency` char(3) NOT NULL default '',
																	  	`store_account_balance_amount` decimal(15,4) NOT NULL default '0.0000',
																	  	`store_account_reserve_amount` decimal(15,4) NOT NULL default '0.0000',
																	  	`store_account_last_modified` datetime default NULL,
																	  	PRIMARY KEY(`user_id`, `user_role`, `store_account_balance_currency`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

$add_new_tables["store_payment_account_book"] = array (	"structure" => "CREATE TABLE `store_payment_account_book` (
																		  `store_payment_account_book_id` int(11) NOT NULL auto_increment,
																		  `user_id` int(11) NOT NULL default '0',
																		  `user_role` varchar(16) NOT NULL default '',
																		  `payment_methods_id` int(11) NOT NULL default '0',
																		  `payment_methods_alias` varchar(32) NOT NULL default '',
																		  `store_payment_account_book_primary` tinyint(1) NOT NULL default '0',
																		  PRIMARY KEY(`store_payment_account_book_id`)
																		) TYPE=MyISAM;",
														"data" => ""
													);

$add_new_tables["store_payment_account_book_details"] = array (	"structure" => "CREATE TABLE `store_payment_account_book_details` (
																				  `store_payment_account_book_id` int(11) NOT NULL default '0',
																				  `payment_methods_fields_id` int(11) NOT NULL default '0',
																				  `payment_methods_fields_value` varchar(255) NOT NULL default '',
																				  PRIMARY KEY(`store_payment_account_book_id`, `payment_methods_fields_id`),
																				  KEY `idx_store_payment_account_book_id` (`store_payment_account_book_id`)
																				) TYPE=MyISAM;",
																"data" => ""
															);

$add_new_tables["store_account_history"] = array (	"structure" => "CREATE TABLE `store_account_history` (
																	  `store_account_history_id` int(11) NOT NULL auto_increment,
																	  `user_id` int(11) NOT NULL default '0',
																	  `user_role` varchar(16) NOT NULL default '',
																	  `store_account_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `store_account_transaction_reserved` tinyint(1) NOT NULL default '0',
																	  `store_account_history_currency` char(3) NOT NULL default '',
																	  `store_account_history_debit_amount` decimal(15,4) default NULL,
																	  `store_account_history_credit_amount` decimal(15,4) default NULL,
																	  `store_account_history_after_balance` decimal(15,4) NOT NULL default '0.0000',
																	  `store_account_history_trans_type` varchar(10) NOT NULL default '',
																	  `store_account_history_trans_id` varchar(255) NOT NULL default '',
																	  `store_account_history_activity_title` varchar(128) NOT NULL default '',
																	  `store_account_history_activity_desc` text NOT NULL,
																	  `store_account_history_added_by` varchar(128) NOT NULL default '',
																	  `store_account_history_added_by_role` varchar(16) NOT NULL default '',
																	  `store_account_history_admin_messages` text NOT NULL,
																	  PRIMARY KEY(`store_account_history_id`),
																	  KEY `idx_store_account_history_date` (`store_account_history_date`),
																	  KEY `idx_store_account_history_owner` (`user_id`, `user_role`),
																	  KEY `idx_store_account_transaction_reserved` (`store_account_transaction_reserved`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

$add_new_tables["store_payments"] = array (	"structure" => "CREATE TABLE `store_payments` (
															  `store_payments_id` int(11) NOT NULL auto_increment,
															  `user_id` int(11) NOT NULL default '0',
															  `user_role` varchar(16) NOT NULL default '',
															  `user_firstname` varchar(32) NOT NULL default '',
															  `user_lastname` varchar(32) NOT NULL default '',
															  `user_email_address` varchar(96) NOT NULL default '',
															  `store_payments_date` datetime NOT NULL default '0000-00-00 00:00:00',
															  `store_payments_status` smallint(1) NOT NULL default '0',
															  `store_payments_request_currency` char(3) NOT NULL default '',
															  `store_payments_request_amount` decimal(15,4) NOT NULL default '0.0000',
															  `store_payments_fees` decimal(15,4) NOT NULL default '0.0000',
															  `store_payments_after_fees_amount` decimal(15,4) NOT NULL default '0.0000',
															  `store_payments_paid_currency` char(3) NOT NULL default '',
															  `store_payments_paid_currency_value` decimal(14,6) default NULL,
															  `store_payments_paid_amount` decimal(15,4) NOT NULL default '0.0000',
															  `store_payments_reference` varchar(32) NULL,
															  `store_payments_methods_id` int(11) NOT NULL default '0',
															  `store_payments_methods_name` varchar(20) NULL,
															  `store_payment_account_book_id` int(11) NOT NULL default '0',
  															  `user_payment_methods_alias` varchar(32) NOT NULL default '',
  															  `store_payments_fees_calculation` text,
															  PRIMARY KEY(`store_payments_id`),
															  KEY `idx_store_payments_date` (`store_payments_date`),
															  KEY `idx_store_payments_owner` (`user_id`, `user_role`)
															) TYPE=MyISAM AUTO_INCREMENT=3100000;",
											"data" => ""
										);

$add_new_tables["store_payments_details"] = array (	"structure" => "CREATE TABLE `store_payments_details` (
																	  `store_payments_id` int(11) NOT NULL default '0',
																	  `payment_methods_fields_id` int(11) NOT NULL default '0',
																	  `payment_methods_fields_title` varchar(255) NOT NULL default '',
																	  `payment_methods_fields_value` varchar(255) NOT NULL default '',
																	  `payment_methods_fields_sort_order` int(5) NOT NULL default '50000',
																	  PRIMARY KEY(`store_payments_id`, `payment_methods_fields_id`),
																	  KEY `idx_store_payments_id` (`store_payments_id`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

$add_new_tables["store_payments_history"] = array (	"structure" => "CREATE TABLE `store_payments_history` (
																	  `store_payments_history_id` int(11) NOT NULL auto_increment,
																	  `store_payments_id` int(11) NOT NULL default '0',
																	  `store_payments_status` smallint(1) NOT NULL default '0',
																	  `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `payee_notified` tinyint(1) NOT NULL default '0',
																	  `comments` text,
																	  `changed_by` varchar(128) NOT NULL default '',
																	  `changed_by_role` varchar(16) NOT NULL default '',
																	  PRIMARY KEY  (`store_payments_history_id`),
																	  KEY `index_payments_id_and_payments_status` (`store_payments_id`,`store_payments_status`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

$add_new_tables["store_payments_status"] = array (	"structure" => "CREATE TABLE `store_payments_status` (
																	  `store_payments_status_id` smallint(11) NOT NULL default '0',
																	  `language_id` int(11) NOT NULL default '1',
																	  `store_payments_status_name` varchar(32) NOT NULL default '',
																	  `store_payments_status_sort_order` int(5) NOT NULL default '50000',
																	  PRIMARY KEY  (`store_payments_status_id`,`language_id`),
																	  KEY `idx_store_payments_status_name` (`store_payments_status_name`)
																	) TYPE=MyISAM;",
													"data" => "	INSERT INTO `store_payments_status` (`store_payments_status_id`, `language_id`, `store_payments_status_name`, `store_payments_status_sort_order`) 
																VALUES 	(1, 1, 'Pending', 10), 
																		(2, 1, 'Processing', 20),
																		(3, 1, 'Completed', 30),
																		(4, 1, 'Canceled', 40)
																		;"
												);

$add_new_tables["cron_pending_credit"] = array (	"structure" => "CREATE TABLE `cron_pending_credit` (
																	  `cron_pending_credit_trans_type` varchar(10) NOT NULL default '',
																	  `cron_pending_credit_trans_id` int(11) NOT NULL default '0',
																	  `cron_pending_credit_trans_created_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `cron_pending_credit_trans_completed_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `cron_pending_credit_mature_period` int(11) unsigned NOT NULL default '0',
																	  `cron_pending_credit_trans_status` smallint(1) NOT NULL default '5',
																	  `cron_pending_credit_trans_error` tinyint(1) NOT NULL default '0',
																	  PRIMARY KEY(`cron_pending_credit_trans_type`, `cron_pending_credit_trans_id`),
																	  KEY `idx_trans_processed_date` (`cron_pending_credit_trans_completed_date`),
																	  KEY `idx_trans_type_and_id` (`cron_pending_credit_trans_type`, `cron_pending_credit_trans_id`)
																	) TYPE=MyISAM COMMENT='Pending transactions to be credited to store account';",
													"data" => ""
												);

$add_new_tables["cron_process_track"] = array (	"structure" => "CREATE TABLE `cron_process_track` (
																  `cron_process_track_in_action` tinyint(1) NOT NULL default '0',
																  `cron_process_track_start_date` datetime NOT NULL default '0000-00-00 00:00:00',
																  `cron_process_track_failed_attempt` tinyint(1) NOT NULL default '0',
																  PRIMARY KEY(`cron_process_track_start_date`)
																) TYPE=MyISAM COMMENT='Track cron job process status';",
												"data" => "	INSERT INTO `cron_process_track` (`cron_process_track_in_action`, `cron_process_track_start_date`, `cron_process_track_failed_attempt`) 
															VALUES 	(0, now(), 0);"
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create new tables

// Insert new fields into products, supplier_order_lists, supplier and buyback_request tables
$add_new_field = array();

$add_new_field['products'] = array (	array (	"field_name" => "products_payment_mature_period",
												"field_attr" => " int(11) unsigned NOT NULL default '0' ",
												"add_after" => ""
												)
									);

$add_new_field['supplier_order_lists'] = array (	array (	"field_name" => "supplier_order_lists_billing_status",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => "supplier_order_lists_status"
															),
													array (	"field_name" => "supplier_orders_verify_mode",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => ""
															)
												);

$add_new_field['supplier'] = array (	array (	"field_name" => "supplier_reserve_amount",
												"field_attr" => " decimal(15,4) NOT NULL default '500.00' ",
												"add_after" => ""
												)
									);

$add_new_field['currencies'] = array (	array (	"field_name" => "currencies_live_update",
												"field_attr" => " tinyint(1) NOT NULL default '1' ",
												"add_after" => "value"
												)
									);

$add_new_field['buyback_request'] = array (	array (	"field_name" => "buyback_unit_price",
													"field_attr" => " double NOT NULL default '0' ",
													"add_after" => "buyback_request_quantity"
													),
											array (	"field_name" => "buyback_sender_character",
													"field_attr" => " text NOT NULL ",
													"add_after" => ""
													)
										);

add_field($add_new_field);
// End of insert new fields into products, supplier_order_lists, supplier and buyback_request tables


// Insert new records into configuration table (for Supplier's Payment Account Book Entries)
$conf_insert_sql = array();
$conf_insert_sql["MAX_SUPPLIER_PAYMENT_BOOK_ENTRIES"] = array("insert" => " ('Supplier\'s Payment Account Book Entries', 'MAX_SUPPLIER_PAYMENT_BOOK_ENTRIES', '5', 'Maximum payment account book entries a supplier is allowed to have', 3, 101, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Supplier's Payment Account Book Entries)

// Update existing completed supplier order to billed status
if (!in_array('supplier_order_lists_billing_status', $existing_supplier_order_lists_fields)) {
	$existing_completed_orders_billing_status_update_sql = "UPDATE supplier_order_lists 
															SET supplier_order_lists_billing_status=1,
																supplier_order_lists_last_modified=now() 
															WHERE supplier_order_lists_status = 3";
	tep_db_query($existing_completed_orders_billing_status_update_sql);
}
// End of Update existing completed supplier order to billed status

// Update existing completed supplier order to verified status
if (!in_array('supplier_orders_verify_mode', $existing_supplier_order_lists_fields)) {
	$existing_completed_orders_verify_mode_update_sql = "	UPDATE supplier_order_lists 
															SET supplier_orders_verify_mode=1,
																supplier_order_lists_last_modified=now() 
															WHERE supplier_order_lists_status = 3";
	tep_db_query($existing_completed_orders_verify_mode_update_sql);
}
// End of Update existing completed supplier order to verified status

// Update unit price for existing buyback records
if (!in_array('buyback_unit_price', $existing_buyback_request_fields)) {
	$unit_price_update_sql = "	UPDATE buyback_request 
								SET buyback_unit_price = IF(buyback_request_quantity > 0, buyback_amount / buyback_request_quantity, 0)";
	tep_db_query($unit_price_update_sql);
}
// End of update unit price for existing buyback records


// Insert new records into status_configuration table (for Supplier Order Status Permission)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["S"] = array("insert" => " ('S', '5', '1', '', '-1', '')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="S" AND status_configuration_source_status_id="5" AND status_configuration_destination_status_id="1" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["S"] = array("insert" => " ('S', '5', '4', '1,2,17,23,24,27,28,29,33,34', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="S" AND status_configuration_source_status_id="5" AND status_configuration_destination_status_id="4" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["S"] = array("insert" => " ('S', '1', '2', '1,2,17,22,23,24,27,28,29,33,34', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="S" AND status_configuration_source_status_id="1" AND status_configuration_destination_status_id="2" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["S"] = array("insert" => " ('S', '1', '4', '1,2,17,23,24,27,28,29,33,34', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="S" AND status_configuration_source_status_id="1" AND status_configuration_destination_status_id="4" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["S"] = array("insert" => " ('S', '2', '1', '1', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="S" AND status_configuration_source_status_id="2" AND status_configuration_destination_status_id="1" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["S"] = array("insert" => " ('S', '2', '3', '1,23,24,28,29', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="S" AND status_configuration_source_status_id="2" AND status_configuration_destination_status_id="3" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["S"] = array("insert" => " ('S', '3', '2', '1', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="S" AND status_configuration_source_status_id="3" AND status_configuration_destination_status_id="2" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["S"] = array("insert" => " ('S', '4', '5', '1', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="S" AND status_configuration_source_status_id="4" AND status_configuration_destination_status_id="5" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["S"] = array("insert" => " ('S', '4', '1', '1', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="S" AND status_configuration_source_status_id="4" AND status_configuration_destination_status_id="1" ');
// End of insert new records into status_configuration table (for Supplier Order Status Permission)


// Insert new records into status_configuration table (for Buyback Order Status Permission)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array("insert" => " ('B', '1', '2', '', '-1', '')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="B" AND status_configuration_source_status_id="1" AND status_configuration_destination_status_id="2" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array("insert" => " ('B', '1', '4', '1,2,17,22,24,27,32', '', '')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="B" AND status_configuration_source_status_id="1" AND status_configuration_destination_status_id="4" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array("insert" => " ('B', '2', '3', '1,2,17,22,24,27,32', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="B" AND status_configuration_source_status_id="2" AND status_configuration_destination_status_id="3" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array("insert" => " ('B', '2', '1', '1,2,17,24,27,32', '<<EMAIL>>', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="B" AND status_configuration_source_status_id="2" AND status_configuration_destination_status_id="1" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array("insert" => " ('B', '2', '4', '1,2,17,22,24,27,32', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="B" AND status_configuration_source_status_id="2" AND status_configuration_destination_status_id="4" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array("insert" => " ('B', '4', '2', '1,2,17,24,27,32', '<<EMAIL>>', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="B" AND status_configuration_source_status_id="4" AND status_configuration_destination_status_id="2" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array("insert" => " ('B', '4', '1', '1,2,17,24,27,32', '<<EMAIL>>', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="B" AND status_configuration_source_status_id="4" AND status_configuration_destination_status_id="1" ');
// End of insert new records into status_configuration table (for Buyback Order Status Permission)

// Insert new records into status_configuration table (for Powerleveling Order Status Permission)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PWL"] = array("insert" => " ('PWL', '0', '1', '', '-1', '')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="PWL" AND status_configuration_source_status_id="0" AND status_configuration_destination_status_id="1" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PWL"] = array("insert" => " ('PWL', '1', '2', '1,2,17,24,31,34,20,27,28,32', '', '')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="PWL" AND status_configuration_source_status_id="1" AND status_configuration_destination_status_id="2" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PWL"] = array("insert" => " ('PWL', '1', '3', '', '-1', '')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="PWL" AND status_configuration_source_status_id="1" AND status_configuration_destination_status_id="3" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PWL"] = array("insert" => " ('PWL', '2', '1', '1,2,17,24,31,34', '', '')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="PWL" AND status_configuration_source_status_id="2" AND status_configuration_destination_status_id="1" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PWL"] = array("insert" => " ('PWL', '2', '4', '1,2,17,24,31,34', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="PWL" AND status_configuration_source_status_id="2" AND status_configuration_destination_status_id="4" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PWL"] = array("insert" => " ('PWL', '3', '1', '1,2,17,24,31,34', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="PWL" AND status_configuration_source_status_id="3" AND status_configuration_destination_status_id="1" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PWL"] = array("insert" => " ('PWL', '3', '2', '1,2,17,24,31,34', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="PWL" AND status_configuration_source_status_id="3" AND status_configuration_destination_status_id="2" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PWL"] = array("insert" => " ('PWL', '3', '4', '', '-1', '')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="PWL" AND status_configuration_source_status_id="3" AND status_configuration_destination_status_id="4" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PWL"] = array("insert" => " ('PWL', '4', '2', '', '-1', '')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="PWL" AND status_configuration_source_status_id="4" AND status_configuration_destination_status_id="2" ');
// End of insert new records into status_configuration table (for Powerleveling Order Status Permission)


// Update records in status_configuration table (use -1 for not applicable notification)
$status_configuration_update_sql = array();

$status_configuration_update_sql['status_configuration'] = array(	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='1' AND status_configuration_destination_status_id='5'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='7' AND status_configuration_destination_status_id='1'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='7' AND status_configuration_destination_status_id='2'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='7' AND status_configuration_destination_status_id='5'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='7' AND status_configuration_destination_status_id='6'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='2' AND status_configuration_destination_status_id='7'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='2' AND status_configuration_destination_status_id='1'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='2' AND status_configuration_destination_status_id='3'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='2' AND status_configuration_destination_status_id='6'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='3' AND status_configuration_destination_status_id='2'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='3' AND status_configuration_destination_status_id='4'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='4' AND status_configuration_destination_status_id='3'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='5' AND status_configuration_destination_status_id='1'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='6' AND status_configuration_destination_status_id='7'"),
																	array(	"field_name" => "status_configuration_auto_notification",
																			"update" => " status_configuration_auto_notification='-1' ",
																			"where_str" => " status_configuration_trans_type='C' AND status_configuration_source_status_id='6' AND status_configuration_destination_status_id='2'")
																 );

advance_update_records($status_configuration_update_sql, $DBTables);
// End of update records in status_configuration table (use -1 for not applicable notification)


// Update records in currencies table (e-gold currency will not Live Update from Currency Mgmt Page)
$setting_update_sql = array();

$setting_update_sql['currencies'] = array(	array(	"field_name" => "currencies_live_update",
													"update" => " currencies_live_update='0' ",
													"where_str" => " code='AUG' "
													)
										 );

advance_update_records($setting_update_sql, $DBTables);
// End of update records in currencies table (e-gold currency will not Live Update from Currency Mgmt Page)

// Insert new records into admin_files_actions table (for permission on setting general reserve amount for suppliers)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_RESERVE_AMOUNT"] = array("insert" => " ('SUPPLIER_RESERVE_AMOUNT', 'Set reserve amount for credit withdrawal', ".$row_sql["admin_files_id"].", '1', 20)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on setting general reserve amount for suppliers)

// Insert new records into admin_files_actions table (for permission on mark supplier order as verified)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers_orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["SUPPLIER_ORDER_VERIFY_STATUS"] = array("insert" => " ('SUPPLIER_ORDER_VERIFY_STATUS', 'Mark supplier order as verified/unverify', ".$row_sql["admin_files_id"].", '1', 20)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on mark supplier order as verified)

?>