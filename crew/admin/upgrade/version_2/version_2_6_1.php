<?
/*
	$Id: version_2_6_1.php,v 1.5 2007/01/29 11:59:37 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_supplier_tasks_allocation_fields = get_table_fields("supplier_tasks_allocation");
$existing_buyback_request_grp_fields = get_table_fields("buyback_request_group");

// Insert new fields into supplier_tasks_allocation, store_payments and customers tables
$add_new_field = array();

$add_new_field['supplier_tasks_allocation'] = array (	array (	"field_name" => "supplier_tasks_billing_status",
																"field_attr" => " tinyint(1) NOT NULL default '0' ",
																"add_after" => "supplier_tasks_status"
																),
														array (	"field_name" => "supplier_tasks_verify_mode",
																"field_attr" => " tinyint(1) NOT NULL default '0' ",
																"add_after" => ""
																)
													);

$add_new_field['store_payments'] = array (	array (	"field_name" => "store_payments_last_modified",
													"field_attr" => " datetime default NULL ",
													"add_after" => ""
													)
										);

$add_new_field['customers'] = array (	array (	"field_name" => "customers_login_sites",
												"field_attr" => " varchar(255) NOT NULL DEFAULT '0' ",
												"add_after" => ""
												)
									);

add_field($add_new_field);
// End of insert new fields into supplier_tasks_allocation, store_payments and customers tables

// Update Paid tasks to Completed status with billed state
if (!in_array('supplier_tasks_billing_status', $existing_supplier_tasks_allocation_fields)) {
	$old_paid_status_select_sql = "	SELECT supplier_tasks_status_id 
									FROM supplier_tasks_status 
									WHERE supplier_tasks_status_id=5
										AND supplier_tasks_status_name='Paid'
										AND language_id=1";
	$old_paid_status_result_sql = tep_db_query($old_paid_status_select_sql);
	
	if ($old_paid_status_row = tep_db_fetch_array($old_paid_status_result_sql)) {
		$existing_paid_cp_orders_update_sql = "	UPDATE supplier_tasks_allocation 
												SET supplier_tasks_status=4,
													supplier_tasks_billing_status=1,
													supplier_tasks_verify_mode = 1
												WHERE supplier_tasks_status = 5";
		tep_db_query($existing_paid_cp_orders_update_sql);
		
		// Delete Paid status
		$old_paid_status_delete_sql = "	DELETE FROM supplier_tasks_status 
										WHERE supplier_tasks_status_id=5 ";
		tep_db_query($old_paid_status_delete_sql);
	}
}
// End of update Paid tasks to Completed status with billed state

// Update records in supplier_tasks_status table (standardised the status name)
$cp_status_update_sql = array();

$cp_status_update_sql['supplier_tasks_status'] = array(	array(	"field_name" => "supplier_tasks_status_name",
																"update" => " supplier_tasks_status_name='Pending' ",
																"where_str" => " supplier_tasks_status_id='0' AND language_id='1' AND supplier_tasks_status_name='New'"
																),
														array(	"field_name" => "supplier_tasks_status_name",
																"update" => " supplier_tasks_status_name='Processing' ",
																"where_str" => " supplier_tasks_status_id='1' AND language_id='1' AND supplier_tasks_status_name='In Progress'"
																),
														array(	"field_name" => "supplier_tasks_status_name",
																"update" => " supplier_tasks_status_name='Completed' ",
																"where_str" => " supplier_tasks_status_id='4' AND language_id='1' AND supplier_tasks_status_name='Completed (Awaiting for Payment)'"
																)
										 			);

advance_update_records($cp_status_update_sql, $DBTables);
// End of update records in supplier_tasks_status table (standardised the status name)


// Nick Changes
// Define buyback_request_group_billing_status as index key in buyback_request_group table
add_index_key ('buyback_request_group', 'index_buyback_billing_status', 'index', ' buyback_request_group_billing_status', $DBTables);
// End of buyback_request_group_billing_status as index key in buyback_request_group table

// Change field structure for currency and currency_value in buyback_request_group table
$change_field_structure = array();

$change_field_structure["buyback_request_group"] = array (array (	"field_name" => "currency",
									 								"field_attr" => " char(3) default NULL "
										 						),
										 					array (	"field_name" => "currency_value",
									 								"field_attr" => " decimal(14,6) default NULL "
										 						)
														);
change_field_structure ($change_field_structure);
// End of change field structure for currency and currency_value in buyback_request_group table

// Insert new fields into buyback_request_group, buyback_request and customers tables
$add_new_field = array();

$add_new_field['buyback_request_group'] = array (	array (	"field_name" => "currency",
															"field_attr" => " char(3) NOT NULL DEFAULT 'USD' ",
															"add_after" => "buyback_request_group_user_type"
															),
													array (	"field_name" => "currency_value",
															"field_attr" => " decimal(14, 6) NOT NULL DEFAULT '1.0' ",
															"add_after" => "currency"
															),
													array (	"field_name" => "show_restock",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => "buyback_status_id"
															),
													array (	"field_name" => "buyback_request_contact_name",
															"field_attr" => " varchar(32) NOT NULL default '' ",
															"add_after" => "restock_character_sets_id"
															),
													array (	"field_name" => "buyback_request_contact_telephone",
															"field_attr" => " varchar(32) NOT NULL default '' ",
															"add_after" => "buyback_request_contact_name"
															),
													array (	"field_name" => "buyback_request_group_expiry_date",
															"field_attr" => " datetime NOT NULL default '0000-00-00 00:00:00' ",
															"add_after" => "buyback_request_group_date"
															),
													array (	"field_name" => "buyback_request_group_site_id",
															"field_attr" => " int(2) NOT NULL default '0' ",
															"add_after" => "currency_value"
															),
													array (	"field_name" => "buyback_request_group_billing_status",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => "buyback_request_group_site_id"
															),
													array (	"field_name" => "buyback_request_group_last_modified",
															"field_attr" => " datetime NOT NULL default '0000-00-00 00:00:00' ",
															"add_after" => "buyback_request_group_billing_status"
															),
													array (	"field_name" => "buyback_request_group_verify_mode",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => ""
															)
												);

$add_new_field['buyback_request'] = array (	array (	"field_name" => "restock_character",
													"field_attr" => " text NOT NULL ",
													"add_after" => "buyback_amount"
													),
											array (	"field_name" => "buyback_quantity_confirmed",
													"field_attr" => " int(11) NOT NULL default '0' ",
													"add_after" => "restock_character"
													)
										);

$add_new_field['customers'] = array (	array (	"field_name" => "customers_mobile",
												"field_attr" => " varchar(32) NOT NULL default '' ",
												"add_after" => "customers_telephone"
												),
										array (	"field_name" => "customers_pin_number",
												"field_attr" => " varchar(40) NOT NULL default '' ",
												"add_after" => "customers_password"
												)
									);

add_field($add_new_field);
// End of insert new fields into buyback_request_group, buyback_request and customers tables

// Drop existing primary key (buyback_status_id) for buyback_status table
// Insert new primary key field into buyback_status table
tep_db_query("ALTER TABLE `buyback_status` DROP PRIMARY KEY , ADD PRIMARY KEY ( `buyback_status_id` , `language_id` );");

// Insert new records into buyback_status table (for chinese buyback status)
$buyback_status_insert_sql = array();
$buyback_status_insert_sql["1"] = array	(	"insert" => " (1, 2, '&#23457;&#26680;&#20013;', 10) " );
insert_new_records('buyback_status', "buyback_status_id", $buyback_status_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", " buyback_status_id='1' AND language_id=2 ");

$buyback_status_insert_sql = array();
$buyback_status_insert_sql["2"] = array	(	"insert" => " (2, 2, '&#22788;&#29702;', 20) " );
insert_new_records('buyback_status', "buyback_status_id", $buyback_status_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", " buyback_status_id='2' AND language_id=2 ");

$buyback_status_insert_sql = array();
$buyback_status_insert_sql["3"] = array	(	"insert" => " (3, 2, '&#23436;&#25104;', 30) " );
insert_new_records('buyback_status', "buyback_status_id", $buyback_status_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", " buyback_status_id='3' AND language_id=2 ");

$buyback_status_insert_sql = array();
$buyback_status_insert_sql["4"] = array	(	"insert" => " (4, 2, '&#21462;&#28040;', 40) " );
insert_new_records('buyback_status', "buyback_status_id", $buyback_status_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", " buyback_status_id='4' AND language_id=2 ");
// End of insert new records into buyback_status table (for chinese buyback status)

// Create site_code table
$add_new_tables = array();

$add_new_tables["site_code"] = array (	"structure" => "CREATE TABLE `site_code` (
															`site_id` int(2) NOT NULL, 
															`site_name` varchar(50) NOT NULL, 
															PRIMARY KEY (`site_id`)
														)TYPE=MyISAM;",
										"data" => "	INSERT INTO `site_code` (`site_id`, `site_name`) 
													VALUES 	(0, 'Offgamers US'), 
															(1, 'China Buyback')
															;"
									);

$add_new_tables["restock_character_sets_to_categories"] = array (	"structure" => "CREATE TABLE `restock_character_sets_to_categories` (
																					  `restock_character_sets_id` int(11) NOT NULL default '0',
																					  `categories_id` int(11) NOT NULL default '0',
																					  PRIMARY KEY  (`restock_character_sets_id`,`categories_id`)
																					) TYPE=MyISAM;",
																	"data" => ""
																);

$add_new_tables["buyback_competitor_status_bracket"] = array (	"structure" => "CREATE TABLE `buyback_competitor_status_bracket` (
																				  `buyback_competitor_status_bracket_id` int(11) NOT NULL auto_increment,
																				  `buyback_competitor_status_bracket_set_id` int(11) NOT NULL default '0',
																				  `buyback_competitor_status_bracket_quantity` int(11) NOT NULL default '0',
																				  `buyback_competitor_status_bracket_value` double NOT NULL default '0',
																				  PRIMARY KEY  (`buyback_competitor_status_bracket_id`)
																				) TYPE=MyISAM;",
																"data" => ""
															);

$add_new_tables["buyback_competitor_status_bracket_set"] = array (	"structure" => "CREATE TABLE `buyback_competitor_status_bracket_set` (
																					  `buyback_competitor_status_bracket_set_id` int(11) NOT NULL auto_increment,
																					  `buyback_competitor_status_bracket_set_name` varchar(30) NOT NULL default '0',
																					  PRIMARY KEY  (`buyback_competitor_status_bracket_set_id`)
																					) TYPE=MyISAM;",
																	"data" => ""
																);

$add_new_tables["buyback_supplier_price_bracket"] = array (	"structure" => "CREATE TABLE `buyback_supplier_price_bracket` (
																			  `buyback_supplier_price_bracket_id` int(11) NOT NULL auto_increment,
																			  `buyback_supplier_price_bracket_set_id` int(11) NOT NULL default '0',
																			  `buyback_supplier_price_bracket_quantity` int(11) NOT NULL default '0',
																			  `buyback_supplier_price_bracket_value` double NOT NULL default '0',
																			  `buyback_supplier_price_bracket_mode` smallint(1) NOT NULL default '0',
																			  `buyback_supplier_price_bracket_sign` smallint(1) NOT NULL default '0',
																			  PRIMARY KEY  (`buyback_supplier_price_bracket_id`)
																			) TYPE=MyISAM;",
															"data" => ""
														);

$add_new_tables["buyback_supplier_price_bracket_set"] = array (	"structure" => "CREATE TABLE `buyback_supplier_price_bracket_set` (
																				  `buyback_supplier_price_bracket_set_id` int(11) NOT NULL auto_increment,
																				  `buyback_supplier_price_bracket_set_name` varchar(30) NOT NULL default '0',
																				  `buyback_supplier_price_bracket_set_minimum_value` int(11) NOT NULL default '0',
																				  `buyback_supplier_price_bracket_set_maximum_value` int(11) NOT NULL default '0',
																				  PRIMARY KEY  (`buyback_supplier_price_bracket_set_id`)
																				) TYPE=MyISAM;",
																"data" => ""
															);

$add_new_tables["buyback_categories"] = array (	"structure" => "CREATE TABLE `buyback_categories` (
																  `buyback_categories_id` int(11) NOT NULL auto_increment,
																  `categories_id` int(11) NOT NULL default '0',
																  `buyback_competitor_status_bracket_set_id` int(11) NOT NULL default '0',
																  PRIMARY KEY  (`buyback_categories_id`)
																) TYPE=MyISAM;",
												"data" => ""
											);

$add_new_tables["competitors_average_price_history"] = array (	"structure" => "CREATE TABLE `competitors_average_price_history` (
																				  `competitors_average_price_history_id` int(11) NOT NULL auto_increment,
																				  `products_id` int(11) NOT NULL default '0',
																				  `competitors_average_price` decimal(15,4) NOT NULL default '0.0000',
																				  `competitors_average_price_final` decimal(15,4) NOT NULL default '0000.0000',
																				  `competitors_average_price_is_overwrite` tinyint(1) default NULL,
																				  `created_by` int(11) NOT NULL default '0',
																				  `created_on` datetime NOT NULL default '0000-00-00 00:00:00',
																				  PRIMARY KEY  (`competitors_average_price_history_id`)
																				) TYPE=MyISAM;",
																"data" => ""
															);

$add_new_tables["competitors_buying_price_history"] = array (	"structure" => "CREATE TABLE `competitors_buying_price_history` (
																				  `products_id` int(11) NOT NULL default '0',
																				  `competitors_id` int(11) NOT NULL default '0',
																				  `competitors_average_price_history_id` int(11) NOT NULL default '0',
																				  `competitors_buying_price` decimal(15,4) NOT NULL default '0.0000',
																				  `competitors_buying_status` tinyint(1) NOT NULL default '0',
																				  PRIMARY KEY  (`products_id`,`competitors_id`,`competitors_average_price_history_id`),
																				  KEY `idx_products_id_and_history_id` (`competitors_average_price_history_id`,`products_id`)
																				) TYPE=MyISAM;",
																"data" => ""
															);

$add_new_tables["competitors"] = array (	"structure" => "CREATE TABLE `competitors` (
															  `competitors_id` int(11) NOT NULL auto_increment,
															  `competitors_name` varchar(255) NOT NULL default '',
															  `currency_symbol_left` varchar(10) NOT NULL default '',
															  `currency_symbol_right` varchar(10) NOT NULL default '',
															  `number_of_connection` int(11) NOT NULL default '10',
															  `connection_session_delay_time` int(11) NOT NULL default '0',
															  PRIMARY KEY  (`competitors_id`)
															) TYPE=MyISAM;",
											"data" => ""
										);

$add_new_tables["competitors_to_categories"] = array (	"structure" => "CREATE TABLE `competitors_to_categories` (
																		  `competitors_to_categories_id` int(11) NOT NULL auto_increment,
																		  `competitors_id` int(11) NOT NULL default '0',
																		  `categories_id` int(11) NOT NULL default '0',
																		  `competitors_weight` double(5,3) NOT NULL default '0.000',
																		  PRIMARY KEY  (`competitors_to_categories_id`),
																		  KEY `index_categories_id` (`categories_id`)
																		) TYPE=MyISAM;",
														"data" => ""
													);

$add_new_tables["user_favourite_products"] = array (	"structure" => "CREATE TABLE `user_favourite_products` (
																			`user_id` int(11) NOT NULL,
																			`products_id` int(11) NOT NULL
																		) TYPE=MyISAM;",
														"data" => ""
													);

$add_new_tables["user_setting"] = array (	"structure" => "CREATE TABLE `user_setting` (
															  `user_setting_id` int(11) NOT NULL auto_increment,
															  `user_setting_parent_id` int(11) NOT NULL default '0',
															  `user_setting_user_id` int(11) NOT NULL default '0',
															  `user_setting_table_name` varchar(255) NOT NULL default '',
															  `user_setting_field_name` varchar(255) NOT NULL default '',
															  `user_setting_key` varchar(255) NOT NULL default '',
															  `user_setting_value` varchar(255) NOT NULL default '',
															  PRIMARY KEY  (`user_setting_id`),
															  KEY `index_user_setting_user_id` (`user_setting_user_id`)
															) TYPE=MyISAM;",
											"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create site_code table

// Update records in status_configuration table (for Buyback Order)
$status_configuration_update_sql = array();

$status_configuration_update_sql['status_configuration'] = array(	array(	"field_name" => "status_configuration_manual_notification",
																			"update" => " status_configuration_user_groups_id=1, status_configuration_manual_notification='' ",
																			"where_str" => " status_configuration_trans_type='B' AND status_configuration_source_status_id='1' AND status_configuration_destination_status_id='2'"
																		)
																 );

advance_update_records($status_configuration_update_sql, $DBTables);
// End of update records in status_configuration table (use -1 for not applicable notification)

// Update existing completed buyback order to verified status
if (!in_array('buyback_request_group_verify_mode', $existing_buyback_request_grp_fields)) {
	$existing_completed_orders_verify_mode_update_sql = "	UPDATE buyback_request_group 
															SET buyback_request_group_verify_mode=1, 
																buyback_request_group_billing_status=1, 
																buyback_request_group_last_modified=now() 
															WHERE buyback_status_id = 3";
	tep_db_query($existing_completed_orders_verify_mode_update_sql);
}
// End of Update existing completed buyback order to verified status

?>