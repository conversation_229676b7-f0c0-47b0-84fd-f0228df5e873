<?
/*
	$Id: version_2_6_10.php,v 1.1 2007/02/07 09:16:20 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_user_setting_fields = get_table_fields("user_setting");

// Delete record which having value for user_setting_table_name and user_setting_field_name fields
if (in_array('user_setting_table_name', $existing_user_setting_fields) &&
	in_array('user_setting_field_name', $existing_user_setting_fields) &&
	in_array('user_setting_parent_id', $existing_user_setting_fields)
	) {
	$user_setting_delete_sql = "DELETE FROM user_setting WHERE user_setting_table_name <> '' AND user_setting_field_name <> ''";
	tep_db_query($user_setting_delete_sql);
}
// End of delete record which having value for user_setting_table_name and user_setting_field_name fields

// Delete user_setting_table_name, user_setting_field_name and user_setting_parent_id fields from user_setting table
$delete_field = array();

$delete_field["user_setting"] = array  ( array( "field_name" => "user_setting_table_name"),
										 array( "field_name" => "user_setting_field_name"),
										 array( "field_name" => "user_setting_parent_id")
										 );
delete_field ($delete_field);
// End of delete user_setting_table_name, user_setting_field_name and user_setting_parent_id fields from user_setting table
?>