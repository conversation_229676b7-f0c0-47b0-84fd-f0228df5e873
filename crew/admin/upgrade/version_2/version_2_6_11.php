<?
/*
	$Id: version_2_6_11.php,v 1.1 2007/02/13 05:43:17 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create new tables
$add_new_tables = array();

$add_new_tables["customers_login_ip_history"] = array (	"structure" => "CREATE TABLE `customers_login_ip_history` (
																			`customers_id` int(11) NOT NULL default '0',
																		  	`customers_login_date` datetime NOT NULL default '0000-00-00 00:00:00',
																		  	`customers_login_ip` varchar(20) NOT NULL default '',
																		  	PRIMARY KEY  (`customers_id`,`customers_login_date`)
																		) TYPE=MyISAM ;",
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create new tables

// Insert new records into admin_files_actions table (for permission on view customer login IP history)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_LOGIN_IP_HISTORY"] = array("insert" => " ('CUSTOMER_LOGIN_IP_HISTORY', 'Customer login IP history', ".$row_sql["admin_files_id"].", '1', 15)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on view customer login IP history)
?>