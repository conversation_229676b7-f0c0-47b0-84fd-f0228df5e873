<?
/*
	$Id: version_2_6_14.php,v 1.2 2007/03/05 09:47:03 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into categories_configuration table (for Powerleveling Order Price Adjustment Notification)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Custom Product'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["POWERLEVELING_PRICE_ADJUSTMENT_NOTIFICATION"] = array("insert" => " (0, 'Powerleveling Order Price Adjustment Notification Email Address', 'POWERLEVELING_PRICE_ADJUSTMENT_NOTIFICATION', '', 'Email address to which the powerleveling order price adjustment email will be send to.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 20, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into categories_configuration table (for Powerleveling Order Price Adjustment Notification)

// Define buyback_request_group_date as index key in buyback_request_group table
add_index_key ('buyback_request_group', 'index_buyback_date', 'index', 'buyback_request_group_date', $DBTables);
// End of define buyback_request_group_date as index key in buyback_request_group table
?>