<?
/*
	$Id: version_2_6_2.php,v 1.2 2007/01/29 04:49:23 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require_once(DIR_WS_CLASSES . 'supplier_payment.php');
require_once(DIR_WS_CLASSES . 'supplier_order.php');
require_once(DIR_WS_CLASSES . 'currencies.php');

$currencies = new currencies();

// Insert new records into admin_files_actions table (for permission on mark pwl order and buyback order as verified)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='progress_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["PWL_ORDER_VERIFY_STATUS"] = array("insert" => " ('PWL_ORDER_VERIFY_STATUS', 'Mark powerleveling order as verified/unverify', ".$row_sql["admin_files_id"].", '1', 40)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}

$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='buyback_requests_info.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["BUYBACK_ORDER_VERIFY_STATUS"] = array("insert" => " ('BUYBACK_ORDER_VERIFY_STATUS', 'Mark buyback order as verified/unverify', ".$row_sql["admin_files_id"].", '1', 20)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on mark pwl order and buyback order as verified)

// Insert new fields into customers_info table (Sign up from site)
$add_new_field = array();

$add_new_field['customers_info'] = array (	array (	"field_name" => "customers_info_account_created_from",
													"field_attr" => " int(2) NOT NULL default '0' ",
													"add_after" => "customers_info_account_created_ip"
													)
										);

add_field($add_new_field);
// End of insert new fields into customers_info table (Sign up from site)


// Auto credit for partially paid suppliers orders
$partially_paid_processing_orders_select_sql = "SELECT suppliers_id, supplier_order_lists_id, currency, currency_value 
												FROM supplier_order_lists 
												WHERE supplier_order_lists_status=2";
$partially_paid_processing_orders_result_sql = tep_db_query($partially_paid_processing_orders_select_sql);

while ($partially_paid_processing_orders_row = tep_db_fetch_array($partially_paid_processing_orders_result_sql)) {
	$order_number = $partially_paid_processing_orders_row['supplier_order_lists_id'];
	
	$paid_amount = supplier_payment::get_order_paid_amount($order_number);
	
	$payable_amount = supplier_order::get_order_total_payable_amount($order_number);
	$unpaid_amount = (double)$payable_amount - (double)$paid_amount;
	
	
	// Update supplier_order_lists_status and supplier_order_lists_billing_status
	$trans_billing_update_sql = "	UPDATE supplier_order_lists 
									SET supplier_order_lists_status = 3,
										supplier_order_lists_billing_status = 1
									WHERE supplier_order_lists_id = '" . tep_db_input($order_number) . "'";
	tep_db_query($trans_billing_update_sql);
	
	// Get payable amount
	$final_currency_payable_amount = $unpaid_amount * $partially_paid_processing_orders_row['currency_value'];
	
	$comment_final_credited_amt = $currencies->format($final_currency_payable_amount, false, $partially_paid_processing_orders_row['currency']);
	
	$credit_update_success_flag = tep_version_upgrade_credit_user_balance($partially_paid_processing_orders_row['suppliers_id'], 'supplier', $partially_paid_processing_orders_row['currency'], $final_currency_payable_amount, 'S', $order_number);
	
	// Insert order comment and update last modified time
	if ($credit_update_success_flag) {
		$trans_history_data_array = array(	'supplier_order_lists_id' => $order_number,
					                        'supplier_order_lists_status' => 0,
					                        'date_added' => 'now()',
					                        'supplier_notified' => 0,
					                        'comments' => 'Credited to account balance',
					                        'changed_by' => 'system'
					                       );
		tep_db_perform('supplier_order_lists_history', $trans_history_data_array);
		
		$last_modified_update_sql = "	UPDATE supplier_order_lists 
										SET supplier_order_lists_last_modified = now() 
										WHERE supplier_order_lists_id = '" . tep_db_input($order_number) . "'";
		tep_db_query($last_modified_update_sql);
	}
}
// End of auto credit for partially paid suppliers orders


// Preparing cronjob for existing unbillied completed pwl order
$unbilled_completed_tasks_select_sql = "SELECT orders_products_id 
										FROM supplier_tasks_allocation 
										WHERE supplier_tasks_status=4 
											AND supplier_tasks_billing_status=0";
$unbilled_completed_tasks_result_sql = tep_db_query($unbilled_completed_tasks_select_sql);

while ($unbilled_completed_tasks_row = tep_db_fetch_array($unbilled_completed_tasks_result_sql)) {
	$cron_order_product_info_select_sql = "	SELECT o.date_purchased 
											FROM orders AS o 
											INNER JOIN orders_products AS op 
												ON o.orders_id=op.orders_id 
											WHERE op.orders_products_id = '" . tep_db_input($unbilled_completed_tasks_row['orders_products_id']) . "'";
	$cron_order_product_info_result_sql = tep_db_query($cron_order_product_info_select_sql);
	$cron_order_product_info_row = tep_db_fetch_array($cron_order_product_info_result_sql);
	
	$this_prod_mature_period = 2880;	// 2-day
	
	tep_insert_cron_pending_credit('PWL', $unbilled_completed_tasks_row['orders_products_id'], $cron_order_product_info_row['date_purchased'], $this_prod_mature_period, 4);
}
// End of preparing cronjob for existing unbillied completed pwl order


function tep_version_upgrade_credit_user_balance($user_id, $user_role, $credit_currency, $credit_amount, $trans_type, $trans_id) {
	// Store the credit into corresponding CURRENCY account
	
	if (tep_not_null($user_id) && tep_not_null($user_role) && tep_not_null($credit_currency)) {
		/*************************************************************************
		 	Lock the TABLE_STORE_ACCOUNT_BALANCE and TABLE_STORE_ACCOUNT_HISTORY.
		 	REMEMBER: Need to lock all the tables involved in this block.
		*************************************************************************/
		tep_db_query("LOCK TABLES " . TABLE_STORE_ACCOUNT_BALANCE . " WRITE, " . TABLE_STORE_ACCOUNT_HISTORY . " WRITE;");
		
		$user_credit_select_sql = "	SELECT store_account_balance_amount 
									FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
									WHERE user_id = '" . tep_db_input($user_id) . "' 
										AND user_role = '" . tep_db_input($user_role) . "'
										AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "' ";
		$user_credit_result_sql = tep_db_query($user_credit_select_sql);
		
		if ($user_credit_row = tep_db_fetch_array($user_credit_result_sql)) {
			$current_balance = $user_credit_row['store_account_balance_amount'];
		} else {	// Create new account
			$account_balance_data_array = array('user_id' => $user_id,
												'user_role' => $user_role,
						                        'store_account_balance_currency' => $credit_currency,
						                        'store_account_balance_amount' => 0,
						                        'store_account_last_modified' => 'now()'
						                       );
			tep_db_perform(TABLE_STORE_ACCOUNT_BALANCE, $account_balance_data_array);
			
			$current_balance = 0;
		}
		
		$new_balance = $current_balance + (double)$credit_amount;
		
		// Insert account statement history
		$account_balance_history_data_array = array('user_id' => $user_id,
													'user_role' => $user_role,
							                        'store_account_history_date' => 'now()',
							                        'store_account_history_currency' => $credit_currency,
							                        'store_account_history_debit_amount' => 'NULL',
							                        'store_account_history_credit_amount' => (double)$credit_amount,
							                        'store_account_history_after_balance' => (double)$new_balance,
							                        'store_account_history_trans_type' => $trans_type,
							                        'store_account_history_trans_id' => $trans_id,
							                        'store_account_history_added_by' => 'system',
							                        'store_account_history_added_by_role' => 'admin'
							                       );
		tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $account_balance_history_data_array);
		
		// Update store account new balance
		$account_balance_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . "
										SET store_account_balance_amount = store_account_balance_amount + " . (double)$credit_amount . ", 
											store_account_last_modified = now() 
										WHERE user_id = '" . tep_db_input($user_id) . "' 
											AND user_role = '" . tep_db_input($user_role) . "'
											AND store_account_balance_currency = '" . tep_db_input($credit_currency) . "' ";
		tep_db_query($account_balance_update_sql);
		
		tep_db_query("UNLOCK TABLES;");
		/********************************************************************
		 	End of locking the TABLE_STORE_ACCOUNT_BALANCE and 
		 	TABLE_STORE_ACCOUNT_HISTORY tables.
		********************************************************************/
		
		return true;
	} else {
		return false;
	}
}
?>