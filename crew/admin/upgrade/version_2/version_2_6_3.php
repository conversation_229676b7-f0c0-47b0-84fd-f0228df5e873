<?
/*
	$Id: version_2_6_3.php,v 1.1 2007/01/29 12:26:07 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files table (for china buyback module)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["competitors.php"] = array(	"insert" => " ('competitors.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   								);
	
	$admin_files_insert_sql["suppliers_restock_characters.php"] = array("insert" => " ('suppliers_restock_characters.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																		"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   													);
	
	$admin_files_insert_sql["competitors_average_market_price.php"] = array(	"insert" => " ('competitors_average_market_price.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																				"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   														);
	
	$admin_files_insert_sql["suppliers_average_offer_price.php"] = array(	"insert" => " ('suppliers_average_offer_price.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																			"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   													);
	
	$admin_files_insert_sql["suppliers_average_offer_price_brackets.php"] = array(	"insert" => " ('suppliers_average_offer_price_brackets.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																					"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   															);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", "");
}
// End of insert new records into admin_files table (for china buyback module)

// Change field structure for payment_methods_send_mode_name in payment_methods table
$change_field_structure = array();

$change_field_structure["payment_methods"] = array (array (	"field_name" => "payment_methods_send_mode_name",
									 						"field_attr" => " varchar(255) default NULL "
															)
													);
change_field_structure ($change_field_structure);
// End of change field structure for payment_methods_send_mode_name in payment_methods table

// Insert new field into customers table
$add_new_field = array();

$add_new_field['customers'] = array (array ("field_name" => "email_verified",
							 				"field_attr" => " tinyint(1) NOT NULL default '0' ",
							 				"add_after" => "customers_email_address"
							 				)
									  );

add_field ($add_new_field);
// End of insert new field into customers table
?>