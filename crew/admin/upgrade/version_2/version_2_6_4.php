<?
/*
	$Id: version_2_6_4.php,v 1.1 2007/01/30 06:28:13 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/


// Change field structure for store_payments_methods_name in store_payments table
$change_field_structure = array();

$change_field_structure["store_payments"] = array (array (	"field_name" => "store_payments_methods_name",
									 						"field_attr" => " varchar(255) default NULL "
															)
													);
change_field_structure ($change_field_structure);
// End of change field structure for store_payments_methods_name in store_payments table

// Insert new field into buyback_supplier_price_bracket_set table
$add_new_field = array();

$add_new_field['buyback_supplier_price_bracket_set'] = array (array (	"field_name" => "buyback_supplier_price_bracket_set_maximum_is_overwrite",
							 											"field_attr" => " tinyint(1) NOT NULL DEFAULT '0' ",
							 											"add_after" => "buyback_supplier_price_bracket_set_maximum_value"
							 											)
									  							);

add_field ($add_new_field);
// End of insert new field into buyback_supplier_price_bracket_set table

// Insert new records into admin_files_actions table (for permission on manual deduct/add on Acc Statement page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='account_statement.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["STORE_ACC_MANUAL_DEDUCT"] = array("insert" => " ('STORE_ACC_MANUAL_DEDUCT', 'Manual Deduction', ".$row_sql["admin_files_id"].", '1', 10)" );
	$admin_files_actions_insert_sql["STORE_ACC_MANUAL_ADD"] = array("insert" => " ('STORE_ACC_MANUAL_ADD', 'Manual Addition', ".$row_sql["admin_files_id"].", '1', 20)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on manual deduct/add on Acc Statement page)
?>