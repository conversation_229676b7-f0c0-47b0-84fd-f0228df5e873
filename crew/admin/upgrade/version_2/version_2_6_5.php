<?
/*
	$Id: version_2_6_5.php,v 1.1 2007/01/31 07:55:58 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for RedJack setting)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Custom Product'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["REMOTE_REDJACK_SEND_ERROR_REPORT"] = array("insert" => " ('Send RedJack error report', 'REMOTE_REDJACK_SEND_ERROR_REPORT', 'false', 'Do you want to receive RedJack\'s error report?', ".$row_sql["configuration_group_id"].", 30, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	$conf_insert_sql["REMOTE_REDJACK_PIXEL_VARIANCE"] = array("insert" => " ('RedJack allowed pixel variance', 'REMOTE_REDJACK_PIXEL_VARIANCE', '100', 'Game client allowed pixel variance (+/- of this value)', ".$row_sql["configuration_group_id"].", 40, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["REMOTE_REDJACK_LEAST_MATCH_PIXEL"] = array("insert" => " ('RedJack Least Match Pixel', 'REMOTE_REDJACK_LEAST_MATCH_PIXEL', '59', 'Game client minimum pixel to match (out of 64)', ".$row_sql["configuration_group_id"].", 40, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for RedJack setting)

// Insert new records into admin_files_actions table (for permission on accessing game using RedJack)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='progress_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["GAME_REMOTE_ACCOUNT_LOGIN_ACCESS"] = array("insert" => " ('GAME_REMOTE_ACCOUNT_LOGIN_ACCESS', 'Access to game via Redjack', ".$row_sql["admin_files_id"].", '1', 60)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on accessing game using RedJack)

?>