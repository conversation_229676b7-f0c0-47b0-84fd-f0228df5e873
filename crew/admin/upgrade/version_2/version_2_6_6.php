<?
/*
	$Id: version_2_6_6.php,v 1.1 2007/02/02 06:01:58 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Change field structure for value in currencies table, supplier_reserve_amount in supplier table, payment_methods_fields_value in store_payments_details table, payment_methods_fields_value in store_payment_account_book_details table
$change_field_structure = array();

$change_field_structure["currencies"] = array (array (	"field_name" => "value",
									 					"field_attr" => " decimal(13,8) default NULL "
														)
												);

$change_field_structure["supplier"] = array (array ("field_name" => "supplier_reserve_amount",
								 					"field_attr" => " decimal(15,4) NOT NULL default '0.0000' "
													)
											);

$change_field_structure["store_payments_details"] = array (array (	"field_name" => "payment_methods_fields_value",
								 									"field_attr" => " text "
																)
															);

$change_field_structure["store_payment_account_book_details"] = array (array (	"field_name" => "payment_methods_fields_value",
								 												"field_attr" => " text "
																			)
																	);

change_field_structure ($change_field_structure);
// End of change field structure for value in currencies table, supplier_reserve_amount in supplier table, payment_methods_fields_value in store_payments_details table, payment_methods_fields_value in store_payment_account_book_details table
?>