<?
/*
	$Id: version_2_6_7.php,v 1.2 2007/02/05 08:42:46 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into buyback_supplier_price_bracket_set table
$add_new_field = array();

$add_new_field['buyback_supplier_price_bracket_set'] = array (array (	"field_name" => "buyback_supplier_price_bracket_set_cat_id",
							 											"field_attr" => " int(11) NOT NULL default '0' ",
							 											"add_after" => "buyback_supplier_price_bracket_set_name"
							 											)
									  							);

add_field ($add_new_field);
// End of insert new field into buyback_supplier_price_bracket_set table

// Insert new records into configuration table (for China Buyback live help)
$conf_insert_sql = array();

$conf_insert_sql["LIVE_HELP_CHINA_BUYBACK_IMPLEMENTATION"] = array("insert" => " ('Live Help China Buyback Implementation', 'LIVE_HELP_CHINA_BUYBACK_IMPLEMENTATION', '', 'This live help is used for china buyback', 1, 35, NULL, now(), NULL, 'tep_cfg_textarea(')" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for China Buyback live help)

?>