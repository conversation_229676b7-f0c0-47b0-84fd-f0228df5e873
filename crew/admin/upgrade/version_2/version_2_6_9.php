<?
/*
	$Id: version_2_6_9.php,v 1.1 2007/02/07 07:22:25 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create new tables
$add_new_tables = array();

$add_new_tables["restock_character_log"] = array (	"structure" => "CREATE TABLE `restock_character_log` (
																	  `restock_characters_log_id` int(11) NOT NULL auto_increment,
																	  `restock_character_sets_id` int(11) NOT NULL default '0',
																	  `products_id` int(11) NOT NULL default '0',
																	  `restock_character_before` text,
																	  `restock_character_after` text,
																	  `restock_character_log_admin_id` varchar(255) NOT NULL default '',
																	  `restock_characters_log_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `restock_characters_log_ip` varchar(20) NOT NULL default '',
																	  PRIMARY KEY  (`restock_characters_log_id`)
																	) TYPE=MyISAM ;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create new tables

// Insert new records into admin_files table (for restock character log page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='administrator.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["restock_character_log.php"] = array(	"insert" => " ('restock_character_log.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   											);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='restock_character_log.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for restock character log page)

?>