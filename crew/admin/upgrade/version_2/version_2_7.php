<?
/*
	$Id: version_2_7.php,v 1.2 2007/03/20 06:20:44 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create new tables for affiliate module
$add_new_tables = array();

$add_new_tables["wd_g_accounts"] = array (	"structure" => "CREATE TABLE `wd_g_accounts` (
															  `accountid` varchar(8) NOT NULL default '',
															  `name` varchar(100) NOT NULL default '',
															  `description` text,
															  `dateinserted` datetime NOT NULL default '0000-00-00 00:00:00',
															  `rstatus` tinyint(4) NOT NULL default '0',
															  PRIMARY KEY  (`accountid`),
															  UNIQUE KEY `name` (`name`),
															  UNIQUE KEY `IDX_wd_g_accounts1` (`accountid`)
															) TYPE=MyISAM;",
											"data" => "	INSERT INTO `wd_g_accounts` (`accountid`, `name`, `description`, `dateinserted`, `rstatus`) 
														VALUES 	('default1', 'Default account', 'Do not delete', now(), 0)
																;"
										);

$add_new_tables["wd_g_categories"] = array ("structure" => "CREATE TABLE `wd_g_categories` (
															  `catid` varchar(8) NOT NULL default '',
															  `parentcatid` varchar(8) default NULL,
															  `name` varchar(100) NOT NULL default '',
															  `rstatus` tinyint(4) NOT NULL default '0',
															  `product` varchar(10) NOT NULL default '',
															  `description` text,
															  `dateinserted` datetime NOT NULL default '0000-00-00 00:00:00',
															  `deleted` tinyint(4) NOT NULL default '0',
															  PRIMARY KEY  (`catid`),
															  UNIQUE KEY `IDX_wd_g_categories2` (`catid`),
															  KEY `IDX_wd_g_categories1` (`parentcatid`)
															) TYPE=MyISAM;",
											"data" => ""
										);

$add_new_tables["wd_g_domains"] = array (	"structure" => "CREATE TABLE `wd_g_domains` (
															  `domainid` varchar(8) NOT NULL default '',
															  `accountid` varchar(8) default NULL,
															  `userid` varchar(8) default NULL,
															  `rtype` tinyint(4) NOT NULL default '0',
															  `url` varchar(255) NOT NULL default '',
															  `dateinserted` datetime NOT NULL default '0000-00-00 00:00:00',
															  `rstatus` tinyint(4) NOT NULL default '0',
															  `declinereason` text,
															  PRIMARY KEY  (`domainid`),
															  KEY `IDX_wd_g_domains1` (`accountid`),
															  KEY `IDX_wd_g_domains2` (`userid`)
															) ENGINE=MyISAM;",
											"data" => ""
										);

$add_new_tables["wd_g_emailtemplates"] = array ("structure" => "CREATE TABLE `wd_g_emailtemplates` (
																  `emailtempsid` varchar(8) NOT NULL default '',
																  `categorycode` varchar(20) NOT NULL default '',
																  `emailsubject` varchar(250) default NULL,
																  `emailtext` text,
																  `deleted` tinyint(4) default '0',
																  `lang` varchar(10) NOT NULL default '',
																  `accountid` varchar(8) default NULL,
																  PRIMARY KEY  (`emailtempsid`),
																  KEY `IDX_wd_g_emailtemplates1` (`accountid`)
																) TYPE=MyISAM;",
												"data" => "	INSERT INTO `wd_g_emailtemplates` (`emailtempsid`, `categorycode`, `emailsubject`, `emailtext`, `deleted`, `lang`, `accountid`) 
															VALUES ('091ab586', 'AFF_EMAIL_MONTH_REP', 'Monthly report $Date-$Month', 'Impressions: $Impressions\r\nClicks: $Clicks\r\nSales: $Sales_approved\r\nLeads: $Leads_approved\r\nCommissions: $Commissions_approved\r\n\r\n-----------------------------------\r\n$Sales_list\r\n\r\n\r\n-----------------------------------\r\n$Leads_list', 0, 'english', 'default1'),
																	('1', 'AFF_EMAIL_SIGNUP', 'Thank you for joining', 'Dear $Affiliate_name,\r\n\r\nthank you for joining our affiliate program.\r\n\r\nYour username is: $Affiliate_username\r\nYour password is: $Affiliate_password\r\nYou can log in into your control panel in url: http://www.yoursite.com/affiliate/affiliates/\r\n\r\nIf you have any requests regarding the affiliate program, don''t hesitate to contact me on the email address.\r\n\r\nsincerelly,\r\n\r\nYour Affiliate manager', 0, 'English', 'default1'),
																	('1916de1b', 'AFF_EMAIL_CONTACT_US', 'Message from affiliate', '$Date $Time\r\n\r\nAffiliate: $Affiliate_id - $Affiliate_name\r\n\r\nSubject: $Affiliate_emailsubject\r\n\r\nText:\r\n$Affiliate_emailtext', 0, 'english', 'default1'),
																	('2', 'AFF_EMAIL_NTF_SIGNUP', 'New affiliate signup', 'Email notification\r\n\r\nNew affiliate joined your affiliate program. Affiliate details:\r\n\r\nName: $Affiliate_name\r\nUsername/email: $Affiliate_username\r\nWebsite: $Affiliate_website\r\nCountry: $Affiliate_country\r\nStatus: $Affiliate_status ', 0, 'English', 'default1'),
																	('3', 'AFF_EMAIL_NTF_SALE', 'New sale', 'Affiliate Program Email Notification\r\n\r\nNew sale was registered by affiliate program. Sale details:\r\n\r\nTransaction ID:  $Sale_id\r\nCommission:  $Sale_commission\r\nTotal cost:  $Sale_totalcost\r\nOrderID:  $Sale_orderid\r\nProductID:  $Sale_productid\r\nDate&time:  $Sale_date\r\nAffiliate:  $Sale_affiliate\r\nStatus:  $Sale_status\r\nIP address:  $Sale_ip\r\nReferrer:  $Sale_referrer\r\n', 0, 'English', 'default1'),
																	('3cd5c8e5', 'AFF_EMAIL_AF_WE_REP', 'Weekly Report $Date', 'Impressions: $Impressions\r\nClicks: $Clicks\r\nSales: $Sales_approved\r\nLeads: $Leads_approved\r\nCommissions: $Commissions_approved\r\n\r\n-----------------------------------\r\n$Sales_list\r\n\r\n\r\n-----------------------------------\r\n$Leads_list', 0, 'english', 'default1'),
																	('491603db', 'AFF_EMAIL_FORGOTPAS1', 'Forgot password - step1', 'Hello,\r\n\r\nplease use the verification code below in the step 2:\r\n$Affiliate_verification_code\r\n\r\n\r\n', 0, 'english', 'default1'),
																	('4cc40c29', 'AFF_EMAIL_DAILY_REP', 'Daily Report $Date', 'Impressions: $Impressions\r\nClicks: $Clicks\r\nSales: $Sales_approved\r\nLeads: $Leads_approved\r\nCommissions: $Commissions_approved\r\n\r\n-----------------------------------\r\n$Sales_list\r\n\r\n\r\n-----------------------------------\r\n$Leads_list', 0, 'english', 'default1'),
																	('5', 'AFF_EMAIL_AF_NTF_SGN', 'New affiliate signup', 'Affiliate Program Email Notification\r\n\r\nNew affiliate joined the affiliate program. Affiliate details:\r\n\r\nUsername/email: $Affiliate_username\r\nWebsite: $Affiliate_website\r\nCountry: $Affiliate_country\r\nStatus: $Affiliate_status ', 0, 'English', 'default1'),
																	('5535e79a', 'AFF_EMAIL_NOTIFY_RC', 'New recurring commission was regerated', 'Email Notification\r\n\r\nNew recurring commission was generated\r\n\r\n$Date\r\n$Time\r\n$Rc_id\r\n$Rc_commission\r\n$Rc_orderid\r\n$Rc_affiliate\r\n$Rc_status\r\n$Rc_recurringcommissionid', 0, 'english', 'default1'),
																	('574e3282', 'AFF_EMAIL_AF_ML_REP', 'Monthly report $Date $Month', 'Impressions: $Impressions\r\nClicks: $Clicks\r\nSales: $Sales_approved\r\nLeads: $Leads_approved\r\nCommissions: $Commissions_approved\r\n\r\n-----------------------------------\r\n$Sales_list\r\n\r\n\r\n-----------------------------------\r\n$Leads_list', 0, 'english', 'default1'),
																	('5b108965', 'AFF_EMAIL_AFF_WE_REP', 'Weekly report', 'Impressions: $Impressions\r\nClicks: $Clicks\r\nSales: $Sales_approved\r\nLeads: $Leads_approved\r\nCommissions: $Commissions_approved\r\n\r\n-----------------------------------\r\n$Sales_list\r\n\r\n\r\n-----------------------------------\r\n$Leads_list', 0, 'english', 'default1'),
																	('5f6e0260', 'AFF_EMAIL_ONSIGN', 'Thank you for signing up', 'Dear $Affiliate_name,\r\n\r\nthank you for signing up to our affiliate program. Your application will be reviewed and you will receive email with access information after approval.\r\n\r\nIf you have any requests regarding the affiliate program, don''t hesitate to contact me on the email address\r\<EMAIL>.\r\n\r\nsincerelly,\r\n\r\nYour Affiliate manager', 0, 'english', 'default1'),
																	('6', 'AFF_EMAIL_AF_NTF_SLE', 'New sale commission', 'Affiliate Program Email Notification\r\n\r\nNew sale was registered by affiliate program. Sale details:\r\n\r\nCommission:  $Sale_commission\r\nOrderID:  $Sale_orderid\r\nProductID:  $Sale_productid\r\nDate&time:  $Sale_date\r\nStatus:  $Sale_status\r\nIP address:  $Sale_ip\r\nReferrer:  $Sale_referrer\r\n', 0, 'English', 'default1'),
																	('70aedbf5', 'AFF_EMAIL_WE_REP', 'Weekly Report $Date', 'Impressions: $Impressions\r\nClicks: $Clicks\r\nSales: $Sales_approved\r\nLeads: $Leads_approved\r\nCommissions: $Commissions_approved\r\n\r\n-----------------------------------\r\n$Sales_list\r\n\r\n\r\n-----------------------------------\r\n$Leads_list', 0, 'english', 'default1'),
																	('a8a1013a', 'AFF_EMAIL_FORGOTPAS2', 'Forgot password', 'Hello,\r\n\r\nyour password was set to: $Affiliate_password\r\nYour username is: $Affiliate_username', 0, 'english', 'default1'),
																	('ed25972d', 'AFF_EMAIL_AFF_CAMP_A', 'You have been approved', 'Dear $Affiliate_name,\r\n\r\nyou have been approved for campaign $camp_name.\r\n\r\nBest regards,\r\n\r\nYour affiliate manager', 0, 'english', 'default1'),
																	('f595c543', 'AFF_EMAIL_AF_DL_REP', 'Daily report $Date', 'Impressions: $Impressions\r\nClicks: $Clicks\r\nSales: $Sales_approved\r\nLeads: $Leads_approved\r\nCommissions: $Commissions_approved\r\n\r\n-----------------------------------\r\n$Sales_list\r\n\r\n\r\n-----------------------------------\r\n$Leads_list', 0, 'english', 'default1')
																	;"
											);

$add_new_tables["wd_g_groups"] = array (	"structure" => "CREATE TABLE `wd_g_groups` (
															  `groupid` varchar(8) NOT NULL default '',
															  `name` varchar(100) NOT NULL default '',
															  `rstatus` tinyint(4) NOT NULL default '0',
															  `product` varchar(10) NOT NULL default '',
															  `dateinserted` datetime default NULL,
															  `deleted` tinyint(4) NOT NULL default '0',
															  `parentgroupid` varchar(8) default NULL,
															  `leftnumber` int(11) default NULL,
															  `rightnumber` int(11) default NULL,
															  PRIMARY KEY  (`groupid`),
															  UNIQUE KEY `IDX_wd_g_groups1` (`groupid`),
															  KEY `IDX_wd_g_groups2` (`parentgroupid`)
															) TYPE=MyISAM;",
											"data" => ""
										);

$add_new_tables["wd_g_history"] = array (	"structure" => "CREATE TABLE `wd_g_history` (
															  `historyid` varchar(8) NOT NULL default '',
															  `accountid` varchar(8) default NULL,
															  `rtype` tinyint(4) NOT NULL default '0',
															  `value` text NOT NULL,
															  `dateinserted` datetime NOT NULL default '0000-00-00 00:00:00',
															  `hfile` text,
															  `line` int(11) default NULL,
															  `ip` varchar(20) default NULL,
															  `module` varchar(20) NOT NULL default '',
															  PRIMARY KEY  (`historyid`),
															  KEY `IDX_wd_g_history1` (`accountid`)
															) TYPE=MyISAM;",
											"data" => ""
										);

$add_new_tables["wd_g_jobs"] = array (	"structure" => "CREATE TABLE `wd_g_jobs` (
														  `jobid` char(8) NOT NULL default '',
														  `rtype` smallint(5) unsigned default NULL,
														  `rstatus` tinyint(3) unsigned default NULL,
														  `progress` bigint(20) default NULL,
														  `datecreated` datetime default NULL,
														  `datefinished` datetime default NULL,
														  PRIMARY KEY  (`jobid`)
														) TYPE=MyISAM;",
										"data" => ""
									);

$add_new_tables["wd_g_listviews"] = array (	"structure" => "CREATE TABLE `wd_g_listviews` (
															  `viewid` varchar(8) NOT NULL default '',
															  `accountid` varchar(8) NOT NULL default '',
															  `userid` varchar(8) NOT NULL default '',
															  `name` varchar(30) NOT NULL default '',
															  `rcolumns` text NOT NULL,
															  `listname` varchar(60) NOT NULL default '',
															  PRIMARY KEY  (`viewid`,`accountid`,`userid`),
															  KEY `IDX_wd_g_listviews1` (`accountid`),
															  KEY `IDX_wd_g_listviews2` (`userid`)
															) TYPE=MyISAM;",
											"data" => ""
										);

$add_new_tables["wd_g_messages"] = array (	"structure" => "CREATE TABLE `wd_g_messages` (
															  `messageid` varchar(8) NOT NULL default '',
															  `rtype` tinyint(4) NOT NULL default '0',
															  `dateinserted` datetime NOT NULL default '0000-00-00 00:00:00',
															  `title` varchar(100) NOT NULL default '',
															  `rtext` text NOT NULL,
															  `deleted` tinyint(4) NOT NULL default '0',
															  `accountid` varchar(8) default NULL,
															  `datevalidfrom` datetime default NULL,
															  `datevalidto` datetime default NULL,
															  `active` tinyint(3) unsigned default '0',
															  `showtoall` tinyint(3) unsigned default '0',
															  PRIMARY KEY  (`messageid`),
															  UNIQUE KEY `IDX_wd_g_messages2` (`messageid`),
															  KEY `IDX_wd_g_messages1` (`accountid`)
															) TYPE=MyISAM;",
											"data" => "	INSERT INTO `wd_g_messages` (`messageid`, `rtype`, `dateinserted`, `title`, `rtext`, `deleted`, `accountid`, `datevalidfrom`, `datevalidto`, `active`, `showtoall`) 
														VALUES 	('bf0ec759', 2, now(), 'Welcome to Affiliate Program', 'Dear $Affiliate_name,\r\n\r\nwelcome to our affiliate program.\r\n\r\nYour Affiliate Manager\r\n', 0, 'default1', '2006-03-03 00:00:00', '2008-04-03 23:59:59', 1, 1)
																;"
										);

$add_new_tables["wd_g_messagestousers"] = array (	"structure" => "CREATE TABLE `wd_g_messagestousers` (
																	  `messagetouserid` varchar(8) NOT NULL default '',
																	  `messageid` varchar(8) default NULL,
																	  `userid` varchar(8) default NULL,
																	  `email` varchar(80) default NULL,
																	  `rstatus` tinyint(4) NOT NULL default '0',
																	  PRIMARY KEY  (`messagetouserid`),
																	  KEY `IDX_wd_g_messagestousers1` (`messageid`),
																	  KEY `IDX_wd_g_messagestousers2` (`userid`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

$add_new_tables["wd_g_righttypes"] = array ("structure" => "CREATE TABLE `wd_g_righttypes` (
															  `righttypeid` varchar(8) NOT NULL default '',
															  `parentrighttypeid` varchar(8) default NULL,
															  `module` varchar(20) NOT NULL default '',
															  `category` varchar(40) NOT NULL default '',
															  `code` varchar(40) NOT NULL default '',
															  `righttype` varchar(20) default NULL,
															  `dateinserted` datetime NOT NULL default '0000-00-00 00:00:00',
															  `categorylangid` varchar(80) NOT NULL default '',
															  `rightlangid` varchar(80) NOT NULL default '',
															  `typelangid` varchar(80) NOT NULL default '',
															  `rorder` int(11) default NULL,
															  PRIMARY KEY  (`righttypeid`),
															  UNIQUE KEY `IDX_wd_g_righttypes1` (`righttypeid`),
															  KEY `IDX_wd_g_righttypes2` (`parentrighttypeid`)
															) TYPE=MyISAM;",
											"data" => "	INSERT INTO `wd_g_righttypes` (`righttypeid`, `parentrighttypeid`, `module`, `category`, `code`, `righttype`, `dateinserted`, `categorylangid`, `rightlangid`, `typelangid`, `rorder`) 
														VALUES 	('01', '02', '', 'campaigns', 'aff_camp_product_categories', 'view', '0000-00-00 00:00:00', 'L_G_RT_CAMPAIGNS', 'L_G_CAMPAIGNS', 'L_G_RT_VIEW', 1),
																('02', NULL, '', 'campaigns', 'aff_camp_product_categories', 'modify', '0000-00-00 00:00:00', 'L_G_RT_CAMPAIGNS', 'L_G_CAMPAIGNS', 'L_G_RT_MODIFY', 2),
																('03', '04', '', 'affiliates', 'aff_aff_affiliates', 'view', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_AFFILIATES', 'L_G_RT_VIEW', 1),
																('04', NULL, '', 'affiliates', 'aff_aff_affiliates', 'modify', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_AFFILIATES', 'L_G_RT_MODIFY', 2),
																('05', '06', '', 'affiliates', 'aff_aff_pay_affiliates', 'view', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_PAYOUT', 'L_G_RT_VIEW', 1),
																('06', NULL, '', 'affiliates', 'aff_aff_pay_affiliates', 'modify', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_PAYOUT', 'L_G_RT_MODIFY', 2),
																('07', '08', '', 'affiliates', 'aff_aff_accounting', 'view', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_ACCOUNTING', 'L_G_RT_VIEW', 1),
																('08', NULL, '', 'affiliates', 'aff_aff_accounting', 'modify', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_ACCOUNTING', 'L_G_RT_MODIFY', 2),
																('09', '10', '', 'transactions', 'aff_trans_transactions', 'view', '0000-00-00 00:00:00', 'L_G_TRANSACTIONS', 'L_G_TRANSACTIONS', 'L_G_RT_VIEW', 1),
																('10', '11', '', 'transactions', 'aff_trans_transactions', 'approvedecline', '0000-00-00 00:00:00', 'L_G_TRANSACTIONS', 'L_G_TRANSACTIONS', 'L_G_RT_APPROVE_DECLINE', 2),
																('11', NULL, '', 'transactions', 'aff_trans_transactions', 'modify', '0000-00-00 00:00:00', 'L_G_TRANSACTIONS', 'L_G_TRANSACTIONS', 'L_G_RT_MODIFY', 3),
																('12', NULL, '', 'reports', 'aff_rep_quick_report', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_QUICK', 'L_G_RT_VIEW', 0),
																('13', NULL, '', 'reports', 'aff_rep_transactions', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_TRANSACTIONS', 'L_G_RT_VIEW', 0),
																('14', NULL, '', 'reports', 'aff_rep_traffic_and_sales', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_TRAFFIC', 'L_G_RT_VIEW', 0),
																('15', NULL, '', 'reports', 'aff_rep_top_20_affiliates', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_TOP20AFFILIATES', 'L_G_RT_VIEW', 0),
																('16', NULL, '', 'reports', 'aff_rep_number_of_affiliates', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_AFFILIATECOUNTS', 'L_G_RT_VIEW', 0),
																('17', '18', '', 'communication', 'aff_comm_email_templates', 'view', '0000-00-00 00:00:00', 'L_G_COMMUNICATION', 'L_G_EMAILTEMPLATES', 'L_G_RT_VIEW', 1),
																('18', NULL, '', 'communication', 'aff_comm_email_templates', 'modify', '0000-00-00 00:00:00', 'L_G_COMMUNICATION', 'L_G_EMAILTEMPLATES', 'L_G_RT_MODIFY', 2),
																('19', NULL, '', 'communication', 'aff_comm_broadcast_email', 'use', '0000-00-00 00:00:00', 'L_G_COMMUNICATION', 'L_G_BROADCAST_MESSAGE', 'L_G_RT_USE', 0),
																('20', '21', '', 'tools', 'aff_tool_admins', 'view', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_ADMINS', 'L_G_RT_VIEW', 1),
																('21', NULL, '', 'tools', 'aff_tool_admins', 'modify', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_ADMINS', 'L_G_RT_MODIFY', 2),
																('22', '23', '', 'tools', 'aff_tool_user_profiles', 'view', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_USER_PROFILES_MANAGER', 'L_G_RT_VIEW', 1),
																('23', NULL, '', 'tools', 'aff_tool_user_profiles', 'modify', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_USER_PROFILES_MANAGER', 'L_G_RT_MODIFY', 2),
																('24', '25', '', 'tools', 'aff_tool_settings', 'view', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_SETTINGS', 'L_G_RT_VIEW', 1),
																('25', NULL, '', 'tools', 'aff_tool_settings', 'modify', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_SETTINGS', 'L_G_RT_MODIFY', 2),
																('26', NULL, '', 'tools', 'aff_tool_integration', 'view', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_INTEGRATIONCODE', 'L_G_RT_VIEW', 0),
																('27', '28', '', 'tools', 'aff_tool_history', 'view', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_HISTORY', 'L_G_RT_VIEW', 1),
																('28', NULL, '', 'tools', 'aff_tool_history', 'purge', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_HISTORY', 'L_G_RT_PURGE', 2),
																('29', NULL, '', 'tools', 'aff_tool_db_maintenance', 'backup', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_MAINTENANCE', 'L_G_RT_BACKUP', 1),
																('30', NULL, '', 'tools', 'aff_tool_db_maintenance', 'restore', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_MAINTENANCE', 'L_G_RT_RESTORE', 2),
																('31', '32', '', 'transactions', 'aff_trans_recurr_transactions', 'view', '0000-00-00 00:00:00', 'L_G_TRANSACTIONS', 'L_G_RECURRINGCOMMS', 'L_G_RT_VIEW', 1),
																('32', '33', '', 'transactions', 'aff_trans_recurr_transactions', 'approvedecline', '0000-00-00 00:00:00', 'L_G_TRANSACTIONS', 'L_G_RECURRINGCOMMS', 'L_G_RT_APPROVE_DECLINE', 2),
																('33', NULL, '', 'transactions', 'aff_trans_recurr_transactions', 'modify', '0000-00-00 00:00:00', 'L_G_TRANSACTIONS', 'L_G_RECURRINGCOMMS', 'L_G_RT_MODIFY', 3),
																('34', '35', '', 'campaigns', 'aff_camp_banner_links', 'view', '0000-00-00 00:00:00', 'L_G_RT_CAMPAIGNS', 'L_G_BANNERS', 'L_G_RT_VIEW', 1),
																('35', NULL, '', 'campaigns', 'aff_camp_banner_links', 'modify', '0000-00-00 00:00:00', 'L_G_RT_CAMPAIGNS', 'L_G_BANNERS', 'L_G_RT_MODIFY', 2),
																('36', '37', '', 'communication', 'aff_comm_communications', 'view', '0000-00-00 00:00:00', 'L_G_COMMUNICATION', 'L_G_COMMUNICATION', 'L_G_RT_VIEW', 1),
																('37', NULL, '', 'communication', 'aff_comm_communications', 'modify', '0000-00-00 00:00:00', 'L_G_COMMUNICATION', 'L_G_COMMUNICATION', 'L_G_RT_MODIFY', 2),
																('38', NULL, '', 'reports', 'aff_rep_top_urls', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_TOPREFERRINGURLS', 'L_G_RT_VIEW', 0),
																('39', NULL, '', 'reports', 'aff_rep_top_campaigns', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_TOPCAMPAIGNS', 'L_G_RT_VIEW', 0),
																('41', NULL, '', 'reports', 'aff_rep_non_perform_affiliates', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_NONPERFORMAFFILIATES', 'L_G_RT_VIEW', 0),
																('42', NULL, '', 'reports', 'aff_rep_rotator', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_ROTATORSTATS', 'L_G_RT_VIEW', 0),
																('51', NULL, '', 'tools', 'aff_tool_db_maintenance', 'repair', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_MAINTENANCE', 'L_G_RT_REPAIROPTIMIZE', 3),
																('52', NULL, '', 'tools', 'aff_tool_integration', 'modify', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_INTEGRATIONCODE', 'L_G_RT_MODIFY', 1),
																('53', NULL, '', 'tools', 'aff_tool_signupsettings', 'modify', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_AFFSIGNUPSETTINGS', 'L_G_RT_MODIFY', 1),
																('55', NULL, '', 'tools', 'aff_tool_signupsettings', 'view', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_AFFSIGNUPSETTINGS', 'L_G_RT_VIEW', 0),
																('56', NULL, '', 'tools', 'aff_tool_afflayoutsettings', 'view', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_LAYOUTSETTINGS', 'L_G_RT_VIEW', 0),
																('57', NULL, '', 'affiliates', 'aff_aff_accountingsettings', 'view', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_ACCOUNTINGSETTINGS', 'L_G_RT_VIEW', 0),
																('58', NULL, '', 'affiliates', 'aff_aff_accountingsettings', 'modify', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_ACCOUNTINGSETTINGS', 'L_G_RT_MODIFY', 0),
																('59', NULL, '', 'affiliates', 'aff_aff_appliedaffiliates', 'view', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_APPLIEDAFFILIATES', 'L_G_RT_VIEW', 0),
																('60', NULL, '', 'affiliates', 'aff_aff_appliedaffiliates', 'modify', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_APPLIEDAFFILIATES', 'L_G_RT_MODIFY', 0),
																('61', NULL, '', 'affiliates', 'aff_aff_appliedaffiliates', 'approvedecline', '0000-00-00 00:00:00', 'L_G_AFFILIATES', 'L_G_APPLIEDAFFILIATES', 'L_G_RT_APPROVE_DECLINE', 0),
																('62', NULL, '', 'tools', 'aff_tool_affpanelsettings', 'view', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_PANELSETTINGS', 'L_G_RT_VIEW', 0),
																('63', NULL, '', 'tools', 'aff_tool_db_maintenance', 'archive', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_MAINTENANCE', 'L_G_RT_ARCHIVE', 4),
																('64', NULL, '', 'tools', 'aff_tool_affpanelsettings', 'modify', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_PANELSETTINGS', 'L_G_RT_MODIFY', 1),
																('65', NULL, '', 'tools', 'aff_tool_afflayoutsettings', 'modify', '0000-00-00 00:00:00', 'L_G_TOOLS', 'L_G_LAYOUTSETTINGS', 'L_G_RT_MODIFY', 1),
																('66', NULL, '', 'reports', 'aff_rep_impclicks', 'view', '0000-00-00 00:00:00', 'L_G_REPORTS', 'L_G_IMPRESSIONCLICKS', 'L_G_RT_VIEW', 1);"
										);

$add_new_tables["wd_g_settings"] = array (	"structure" => "CREATE TABLE `wd_g_settings` (
															  `settingsid` varchar(8) NOT NULL default '',
															  `rtype` tinyint(3) unsigned NOT NULL default '0',
															  `code` varchar(50) NOT NULL default '',
															  `value` text,
															  `accountid` varchar(8) default NULL,
															  `userid` varchar(8) default NULL,
															  `id1` varchar(8) default NULL,
															  `id2` varchar(8) default NULL,
															  PRIMARY KEY  (`settingsid`),
															  KEY `IDX_wd_g_settings1` (`accountid`),
															  KEY `IDX_wd_g_settings2` (`userid`)
															) TYPE=MyISAM;",
											"data" => "	INSERT INTO `wd_g_settings` (`settingsid`, `rtype`, `code`, `value`, `accountid`, `userid`, `id1`, `id2`) 
														VALUES 	('0620dca6', 3, 'Aff_signup_data1_name', '', 'default1', NULL, NULL, NULL),
																('067c22e8', 3, 'Aff_settings_mainpage_show_description', 'false', 'default1', NULL, NULL, NULL),
																('08c6b3c4', 4, 'Aff_user_msn', '', 'default1', '1', NULL, NULL),
																('0b7b84d4', 3, 'Aff_settings_transactions_customdescription', '', 'default1', NULL, NULL, NULL),
																('0bc27d29', 3, 'Aff_signup_zipcode', '1', 'default1', NULL, NULL, NULL),
																('0c1c8958', 3, 'Aff_style_c_menu_link_disbled', '#666666', 'default1', NULL, NULL, NULL),
																('0ec7aefd', 3, 'Aff_settings_traffic_customdescription', '', 'default1', NULL, NULL, NULL),
																('1000432d', 3, 'Aff_settings_afftopurls_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('137a8997', 3, 'Aff_menu_item_topurls_show', 'true', 'default1', NULL, NULL, NULL),
																('140e0dad', 3, 'Aff_settings_contactus_customdescription', '', 'default1', NULL, NULL, NULL),
																('141b9c7e', 3, 'Aff_settings_contactus_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('151af7db', 3, 'Aff_settings_affprofile_show_description', 'true', 'default1', NULL, NULL, NULL),
																('17a90bc4', 3, 'Aff_signup_surname_mandatory', 'true', 'default1', NULL, NULL, NULL),
																('1967072e', 3, 'Aff_signup_force_acceptance', '1', 'default1', NULL, NULL, NULL),
																('1c43ee8f', 3, 'Aff_signup_fax_mandatory', 'false', 'default1', NULL, NULL, NULL),
																('1c82ff7c', 3, 'Aff_style_c_listheader', '#D9E6EC', 'default1', NULL, NULL, NULL),
																('1cf7c642', 3, 'Aff_signup_display_terms', '1', 'default1', NULL, NULL, NULL),
																('1da2a4a8', 3, 'Aff_style_c_menu_link', '#0056B6', 'default1', NULL, NULL, NULL),
																('1de2ae56', 3, 'Aff_settings_settings_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('23423423', 3, 'Aff_email_dailyreport', '0', 'default1', NULL, NULL, NULL),
																('2739f548', 3, 'Aff_style_c_normallink', '#FF0000', 'default1', NULL, NULL, NULL),
																('2a630357', 3, 'Aff_style_c_footer_background', '#E8EDFA', 'default1', NULL, NULL, NULL),
																('2ba8cf45', 3, 'Aff_style_merchant_skin', 'default', 'default1', '1', NULL, NULL),
																('2f1ffb04', 3, 'Aff_settings_afftopurls_customdescription', '', 'default1', NULL, NULL, NULL),
																('306aab79', 3, 'Aff_signup_url', 'http://a07.localhost/a07/affiliate/affsignup.php', 'default1', '1', NULL, NULL),
																('344054da', 3, 'Aff_settings_banners_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('35feef34', 3, 'Aff_signup_country', '1', 'default1', NULL, NULL, NULL),
																('37a45385', 4, 'Aff_user_skype', '', 'default1', '1', NULL, NULL),
																('3ac01511', 3, 'Aff_banners_url', 'http://a07.localhost/a07/affiliate/banners/', 'default1', '1', NULL, NULL),
																('3aff3269', 4, 'Aff_user_custom_html', '', 'default1', '1', NULL, NULL),
																('3b0529b9', 3, 'Aff_signup_data4', '0', 'default1', NULL, NULL, NULL),
																('3f711726', 3, 'Aff_signup_refid_mandatory', 'false', 'default1', NULL, NULL, NULL),
																('43425301', 3, 'Aff_menu_item_affprofile_show', 'true', 'default1', NULL, NULL, NULL),
																('43425302', 3, 'Aff_menu_item_subaffsignup_show', 'true', 'default1', NULL, NULL, NULL),
																('43425303', 3, 'Aff_menu_item_quick_show', 'true', 'default1', NULL, NULL, NULL),
																('43425304', 3, 'Aff_menu_item_show', 'true', 'default1', NULL, NULL, NULL),
																('43425305', 3, 'Aff_menu_item_traffic_show', 'true', 'default1', NULL, NULL, NULL),
																('43425306', 3, 'Aff_menu_item_subaffiliates_show', 'true', 'default1', NULL, NULL, NULL),
																('43425307', 3, 'Aff_menu_item_subid_show', 'true', 'default1', NULL, NULL, NULL),
																('43425308', 3, 'Aff_menu_item_afftopurls_show', 'true', 'default1', NULL, NULL, NULL),
																('43425309', 3, 'Aff_menu_item_contactus_show', 'true', 'default1', NULL, NULL, NULL),
																('43425310', 3, 'Aff_menu_item_banners_show', 'true', 'default1', NULL, NULL, NULL),
																('43425311', 3, 'Aff_menu_item_settings_show', 'true', 'default1', NULL, NULL, NULL),
																('43425312', 6, 'Aff_camp_cookielifetime', '0', 'default1', '1', '1', NULL),
																('43425313', 3, 'Aff_acct_predefined_bannersizes', '88_31;120_60;120_90;125_125;236_60;468_60', 'default1', NULL, NULL, NULL),
																('449bf520', 3, 'Aff_style_c_tableheader2', '#D6DFF5', 'default1', NULL, NULL, NULL),
																('453a3f53', 3, 'Aff_banners_dir', '../banners/', 'default1', '1', NULL, NULL),
																('46bc5cd2', 3, 'Aff_scripts_url', 'http://a07.localhost/a07/affiliate/scripts/', 'default1', '1', NULL, NULL),
																('46f2762d', 3, 'Aff_style_c_ok_header', '#BAFCBA', 'default1', NULL, NULL, NULL),
																('4a41a274', 3, 'Aff_settings_settings_customdescription', '', 'default1', NULL, NULL, NULL),
																('4v8xc9v9', 3, 'Aff_style_affiliate_skin', 'default', 'default1', NULL, NULL, NULL),
																('527051e5', 3, 'Aff_settings_subid_show_description', 'true', 'default1', NULL, NULL, NULL),
																('52872995', 3, 'Aff_settings_faq_show_customdescription', 'true', 'default1', NULL, NULL, NULL),
																('5505c066', 3, 'Aff_style_c_form_button', '#B3CED9', 'default1', NULL, NULL, NULL),
																('557b55cb', 3, 'Aff_style_c_tableheader', '#B3CED9', 'default1', NULL, NULL, NULL),
																('5648e1a3', 4, 'Aff_user_googletalk', '', 'default1', '1', NULL, NULL),
																('5734a90e', 3, 'Aff_signup_state', '1', 'default1', NULL, NULL, NULL),
																('5a55b7fd', 3, 'Aff_style_c_bacground_active', '#B3CED9', 'default1', NULL, NULL, NULL),
																('5c266818', 3, 'Aff_settings_banners_customdescription', '', 'default1', NULL, NULL, NULL),
																('5cbd13b3', 3, 'Aff_signup_data2_name', '', 'default1', NULL, NULL, NULL),
																('5cdd8ff1', 3, 'Aff_style_c_footer_text', '#555555', 'default1', NULL, NULL, NULL),
																('5d20a253', 3, 'Aff_signup_city_mandatory', 'true', 'default1', NULL, NULL, NULL),
																('5ece17b5', 3, 'Aff_resources_dir', 'resources', 'default1', NULL, NULL, NULL),
																('60316001', 3, 'Aff_fixed_cost', '0', 'default1', NULL, NULL, NULL),
																('60316002', 3, 'Aff_join_campaign', '0', 'default1', NULL, NULL, NULL),
																('60316003', 3, 'Aff_display_news', '1', 'default1', NULL, NULL, NULL),
																('60316004', 3, 'Aff_display_resources', '0', 'default1', NULL, NULL, NULL),
																('60316005', 3, 'Aff_display_banner_stats_all', '0', 'default1', NULL, NULL, NULL),
																('60316006', 3, 'Aff_use_forced_matrix', '0', 'default1', NULL, NULL, NULL),
																('60316007', 3, 'Aff_round_numbers', '2', 'default1', NULL, NULL, NULL),
																('60316008', 3, 'Aff_currency_left_position', '0', 'default1', NULL, NULL, NULL),
																('60316009', 3, 'Aff_program_signup_bonus', '0', 'default1', NULL, NULL, NULL),
																('60316010', 3, 'Aff_program_referral_commission', '0', 'default1', NULL, NULL, NULL),
																('60316011', 3, 'Aff_overwrite_cookie', '0', 'default1', NULL, NULL, NULL),
																('60316012', 3, 'Aff_delete_cookie', '0', 'default1', NULL, NULL, NULL),
																('60316013', 3, 'Aff_referred_affiliate_allow', '0', 'default1', NULL, NULL, NULL),
																('60316014', 3, 'Aff_permanent_redirect', '0', 'default1', NULL, NULL, NULL),
																('60316015', 3, 'Aff_mail_send_type', '2', 'default1', NULL, NULL, NULL),
																('60316016', 3, 'Aff_log_level', '11', 'default1', NULL, NULL, NULL),
																('60316017', 3, 'Aff_default_lang', 'english', 'default1', NULL, NULL, NULL),
																('60316018', 3, 'Aff_min_payout_options', '100;200;300;400;500', 'default1', NULL, NULL, NULL),
																('60316019', 3, 'Aff_initial_min_payout', '300', 'default1', NULL, NULL, NULL),
																('60316020', 3, 'Aff_affiliateapproval', '1', 'default1', NULL, NULL, NULL),
																('60316021', 3, 'Aff_affpostsignupurl', 'postsignup.php', 'default1', NULL, NULL, NULL),
																('60316022', 3, 'Aff_allow_choose_lang', '1', 'default1', NULL, NULL, NULL),
																('60316023', 3, 'Aff_link_style', '2', 'default1', NULL, NULL, NULL),
																('********', 3, 'Aff_forcecommfromproductid', 'no', 'default1', NULL, NULL, NULL),
																('********', 3, 'Aff_maxcommissionlevels', '10', 'default1', NULL, NULL, NULL),
																('********', 3, 'Aff_paging', '10', 'default1', NULL, NULL, NULL),
																('********', 3, 'Aff_login_protection_retries', '0', 'default1', NULL, NULL, NULL),
																('********', 3, 'Aff_login_protection_delay', '0', 'default1', NULL, NULL, NULL),
																('********', 1, 'Glob_accounts_dir', 'accounts', 'default1', NULL, NULL, NULL),
																('********', 3, 'Glob_acct_custom_template', '1', 'default1', NULL, NULL, NULL),
																('********', 3, 'Aff_style_merchant_skin', 'default', 'default1', NULL, NULL, NULL),
																('********', 3, 'Aff_mail_type', '1', 'default1', NULL, NULL, NULL),
																('********', 3, 'Aff_mail_charset', 'UTF-8', 'default1', NULL, NULL, NULL),
																('60316fd3', 3, 'Aff_support_sale_commissions', '1', 'default1', NULL, NULL, NULL),
																('60316fd4', 3, 'Aff_support_lead_commissions', '1', 'default1', NULL, NULL, NULL),
																('60316fdc', 3, 'Aff_support_click_commissions', '1', 'default1', NULL, NULL, NULL),
																('628ca545', 3, 'Aff_settings_subaffiliates_customdescription', '', 'default1', NULL, NULL, NULL),
																('63690dd4', 3, 'Aff_settings_subid_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('67830a89', 3, 'Aff_style_c_listresult_row1', '#FFFFFF', 'default1', NULL, NULL, NULL),
																('67f77e5c', 3, 'Aff_signup_data3_mandatory', 'hide', 'default1', NULL, NULL, NULL),
																('69906596', 3, 'Aff_signup_zipcode_mandatory', 'false', 'default1', NULL, NULL, NULL),
																('6c5554b5', 3, 'Aff_signup_street', '1', 'default1', NULL, NULL, NULL),
																('6cc87d6b', 4, 'Aff_user_selected_info', 'photo_url,welcome_msg', 'default1', '1', NULL, NULL),
																('6e6ce0c8', 3, 'Aff_style_c_border2', '#D9E6EC', 'default1', NULL, NULL, NULL),
																('6f76ac90', 3, 'Aff_signup_terms_conditions', 'To be an authorized affiliate of www.yoursite.com, you agree to abide by the terms and conditions contained in this agreement.\r\n\r\nEnter Your Terms & Conditions Here', 'default1', NULL, NULL, NULL),
																('702176a5', 3, 'Aff_p3p_xml', 'p3p.xml', 'default1', NULL, NULL, NULL),
																('706f8417', 4, 'Aff_user_other_name', '', 'default1', '1', NULL, NULL),
																('74bf90c1', 3, 'Aff_export_dir', '../exports/', 'default1', '1', NULL, NULL),
																('75a76106', 3, 'Aff_menu_item_subaff_show', 'true', 'default1', NULL, NULL, NULL),
																('7675f9d7', 3, 'Aff_style_c_listresult_row2', '#E8EDFA', 'default1', NULL, NULL, NULL),
																('76ead01b', 3, 'Aff_settings_subaffiliates_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('78996013', 3, 'Aff_overwrite_cookie', '1', 'default1', NULL, NULL, NULL),
																('78996014', 3, 'Aff_login_display_welcome', '', 'default1', NULL, NULL, NULL),
																('78996015', 3, 'Aff_login_display_statistics', '1', 'default1', NULL, NULL, NULL),
																('78996016', 3, 'Aff_login_display_trendgraph', '', 'default1', NULL, NULL, NULL),
																('78996017', 3, 'Aff_login_display_manager', '1', 'default1', NULL, NULL, NULL),
																('78996018', 3, 'Aff_login_welcome_msg', 'Welcome to the affiliate program.', 'default1', NULL, NULL, NULL),
																('78996019', 3, 'Aff_display_news', '1', 'default1', NULL, NULL, NULL),
																('78996020', 4, 'Aff_user_photo_url', 'http://www.affplanet.com/al/ad/man.gif', 'default1', '11111111', NULL, NULL),
																('78996021', 4, 'Aff_user_selected_info', 'photo_url,username,name,surname', 'default1', '11111111', NULL, NULL),
																('78996022', 3, 'Aff_signup_automatic_form', '1', 'default1', NULL, NULL, NULL),
																('7aa3fad5', 4, 'Aff_user_welcome_msg', 'Hello and welcome to our affiliate program.\r\n      <br/>\r\n      I''m your affiliate manager, and I''m here for you if you have ANY questions or problems related to our affiliate program.\r\n      <br/><br/>\r\n      I wish you all success in promoting our products, and profitable partnership for both you and us.\r\n', 'default1', '1', NULL, NULL),
																('7bde5a09', 3, 'Aff_settings_subid_customdescription', '', 'default1', NULL, NULL, NULL),
																('7cd71174', 3, 'Aff_settings_traffic_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('7d1ea272', 3, 'Aff_track_by_browser', '', 'default1', NULL, NULL, NULL),
																('7dc591ad', 3, 'Aff_signup_weburl', '1', 'default1', NULL, NULL, NULL),
																('85504dff', 3, 'Aff_signup_weburl_mandatory', 'true', 'default1', NULL, NULL, NULL),
																('8584a42a', 3, 'Aff_menu_item_profile_show', 'true', 'default1', NULL, NULL, NULL),
																('8678a468', 3, 'Aff_debug_emails', '1', 'default1', NULL, NULL, NULL),
																('87378f58', 3, 'Aff_signup_affect_editing', '1', 'default1', NULL, NULL, NULL),
																('874c1f46', 3, 'Aff_style_c_background', '#E8EDFA', 'default1', NULL, NULL, NULL),
																('8b9c6e9d', 3, 'Aff_settings_mainpage_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('8bf0b0c3', 3, 'Aff_signup_name_mandatory', 'true', 'default1', NULL, NULL, NULL),
																('8c340688', 3, 'Aff_signup_data5_mandatory', 'hide', 'default1', NULL, NULL, NULL),
																('8c7db251', 3, 'Aff_style_c_ok_border', '#00AA00', 'default1', NULL, NULL, NULL),
																('8fcdde81', 3, 'Aff_style_c_helplink', '#00AA00', 'default1', NULL, NULL, NULL),
																('908e3967', 3, 'Aff_style_c_error_message', '#FF0000', 'default1', NULL, NULL, NULL),
																('916b412d', 3, 'Aff_login_text_in_the_middle_msg', '<p>\r\n<span style=\"color: #3333cc; font-size: 14px; font-weight: bold; font-family: Tahoma;\">Getting Started As Affiliate</span>\r\n</p>\r\n<p>\r\nWelcome to our affiliate program. On the left side you can see the menu that will help you orient in the system.\r\n<br/>\r\nIn the <b>Banners</b> section you can get banners, text links, articles or reviews that you can use to promote our products.\r\n<br/>\r\n In <b>Reports</b> you can run various reports showing you trends of your promotion efforts over time <b>Traffic & Sales</b>, list of all <b>Transactions</b>, and so on.\r\n</p>\r\n\r\n<br/>\r\n<p>\r\n<span style=\"color: #3333cc; font-size: 14px; font-weight: bold; font-family: Tahoma;\">A few ways to make money with our affiliate program</span>\r\n</p>\r\n\r\n<p>\r\n<ol>\r\n<li><strong>Write a review of some of our products</strong>, letting people know how good it is. \r\nInclude your affiliate link at the end of the review. Post your review to free sites such \r\nas <a href=\"http://www.GoArticles.com\" target=_blank>GoArticles.com</a> & <a href=\"http://www.EzineArticles.com\" target=_blank>EzineArticles.com</a>. \r\n</li>\r\n <li><strong>Create a free blog</strong> (web log) at sites such as Blogger.com, and post your review of our products, \r\n    including your affiliate link. Then \"ping\" your blog at a site such as Pingomatic.com, so it gets picked up quickly by search engines.\r\n</li>\r\n\r\n <li><strong>Join popular marketing forums</strong> such as WarriorForum.com, and make frequent contributions to popular threads there. \r\n    Be sure to go into your forum profile and edit your \"signature\". Make a signature that includes your  affiliate link, \r\n    or a link to your own \"review\" website of our product. That way, every time you make a post, anyone who sees it will see your \r\n    signature and potentially click on your affiliate link.\r\n    </li>\r\n    \r\n    <li>Once every few weeks <strong>post a press release</strong> at PRweb.com, in which you include your favorable review of our product, \r\n    along with your affiliate link. If you pay them $80, they will guarantee that your press release is picked up by all major search \r\n    engines, potentially sending you thousands of visitors.\r\n    </li>\r\n    \r\n    <li>If you own an email list of <strong>newsletter</strong> subscribers or other people who have opted in to receive email offers from you, \r\n    send them an email telling them about our website, and feel free to use some text from our homepage in your email. Include \r\n    your affiliate link at the end of the email. You can even use our email sample above. \r\n    <br/>\r\n    *NOTE - We do not tolerate spamming in \r\n    any way. \r\n    </li>\r\n    \r\n    <li><strong>Pay-Per-Click</strong> (PPC):\r\n    Using a PPC account from Google Adwords, Overture, or many others, you can easily generate income with the our affiliate program. \r\n    You can either send people directly to us using your affiliate link in your PPC ads, or you can create your own website in which you have \r\n    a review of our product, followed by your affiliate tracking link. \r\n    </li>\r\n    \r\n    <li><strong>Upgrade yourself</strong><br/>\r\n    Being affiliate is not exceptionally difficult, but there are many tricks and techniques that can improve your results.\r\n    Learn from the best - <a style=\"color: #ff0000\" href=\"http://www.superaffiliatehandbook.com/cbae/?a=Zkjh06wHjL\" target=_blank><b>SuperAffiliate Handbook</b></a> is \r\n    highly recommended reading for every affiliate.\r\n    </li>\r\n    </ol>\r\n    </p>\r\n\r\n    <br/>\r\n    <p>\r\n    <span style=\"color: #3333cc; font-size: 14px; font-weight: bold; font-family: Tahoma;\">Tips and tricks to earn more as an affiliate</span>\r\n </p>\r\n    \r\n    <p>\r\n    <ul>\r\n    <li>From our experience, you will get the best results from writing your own product reviews, even if it''s hort. \r\n        You don''t have to be a good writer. Just write what you really think about the product. \r\n        When you publish your product review, use your general affiliate link (on the top) to send user to our website.\r\n    </li>\r\n    <br/>\r\n    <li>You will have much better conversion if you''ll put your visitors into pre-sold mood before sending them to our site. \r\n        <br/>\r\n        Pre-sold mood means that you build interest in product and visitor has decided to potentially buy it after he reads your product review.\r\n        <br/>\r\n        Choose from various types of product descriptions, download and adjust them to fit in your site. \r\n        Experiment with short or long, try to find perfect combination with banners.\r\n    </li>\r\n    <br/>\r\n    <li>Try to think like a visitor, when he comes to your page with review or affiliate link, you should draw his attention, build curiosity\r\n        or feeling that he might need this kind of solution.\r\n    </li>\r\n    <br/>\r\n    <li>Experiment with different banners, text links, or reviews. Keep these that bring good results, and change the others.\r\n        Sometimes only change of few words or color of link can mean difference.\r\n    </li>\r\n    </ul>\r\n    </p>', 'default1', NULL, NULL, NULL),
																('91c56715', 3, 'Aff_menu_item_notif_show', 'true', 'default1', NULL, NULL, NULL),
																('9385969d', 3, 'Aff_style_c_background_logo', '#FFFFFF', 'default1', NULL, NULL, NULL),
																('939ace0e', 3, 'Aff_login_display_text_in_the_middle', '1', 'default1', NULL, NULL, NULL),
																('94f204a5', 3, 'Aff_signup_street_mandatory', 'true', 'default1', NULL, NULL, NULL),
																('9b4cd65c', 3, 'Aff_signup_city', '1', 'default1', NULL, NULL, NULL),
																('a233bc2a', 3, 'Aff_style_c_datail_row1', '#EBEEF5', 'default1', NULL, NULL, NULL),
																('a2fb0e68', 3, 'Aff_style_c_error_border', '#FF0000', 'default1', NULL, NULL, NULL),
																('a32a2d49', 3, 'Aff_signup_data2_mandatory', 'hide', 'default1', NULL, NULL, NULL),
																('a476eee5', 3, 'Aff_bannerformat_textformat', '<a href=$DESTINATION><b>$TITLE</b><br>$DESCRIPTION$IMPRESSION_TRACK</a>', 'default1', NULL, NULL, NULL),
																('a4cb28ef', 3, 'Aff_settings_traffic_show_description', 'true', 'default1', NULL, NULL, NULL),
																('aa22566b', 3, 'Aff_signup_description', '<b>Welcome To The Post Affilaite Pro Demo Affiliate Program!</b>\r\n\r\n<p>\r\nOur program is free to join, it''s easy to sign-up and requires no technical knowledge.<br>\r\n</p>\r\n\r\n<p>\r\n<b>How does it work</b><br>\r\nWhen you join our program you will get an access to your own control panel. There you can get banners and text links to promote our site. \r\n<br>\r\nWhen you refer some customer to us, you will receive a percentage of his purchase as a reward.\r\n</p>\r\n\r\n<p>\r\n<b>Real-Time Statistics And Reporting</b><br/>\r\nCheck your statistic, sales, traffic, account balance and see how your banners are performing anytime inyour affiliate panel.\r\n</p>\r\n\r\n<p>\r\n<b>Affiliate Program Details</b><br/>\r\nCommission: <font color=#ff0000><b>pay per sale 10%</b></font>\r\n</p>', 'default1', NULL, NULL, NULL),
																('abdf0ba1', 3, 'Aff_settings_subaffsignup_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('abf6cfd9', 3, 'Aff_settings_settings_show_description', 'true', 'default1', NULL, NULL, NULL),
																('ae177764', 3, 'Aff_settings_subaffsignup_show_description', 'true', 'default1', NULL, NULL, NULL),
																('aed560ea', 3, 'Aff_signup_data1', '0', 'default1', NULL, NULL, NULL),
																('afdcffa5', 3, 'Aff_system_currency', '$', 'default1', NULL, NULL, NULL),
																('afdcffa6', 3, 'Aff_show_minihelp', '1', 'default1', NULL, NULL, NULL),
																('b0ee0521', 3, 'Aff_track_by_ip', '1', 'default1', NULL, NULL, NULL),
																('b1a4b176', 3, 'Aff_settings_transactions_show_description', 'true', 'default1', NULL, NULL, NULL),
																('b30d0924', 3, 'Aff_signup_refid', '1', 'default1', NULL, NULL, NULL),
																('b48c3cf0', 3, 'Aff_settings_subaffiliates_show_description', 'true', 'default1', NULL, NULL, NULL),
																('b4ca2aef', 3, 'Aff_debug_sales', '1', 'default1', NULL, NULL, NULL),
																('b52bcf76', 3, 'Aff_ip_validity_type', 'hours', 'default1', NULL, NULL, NULL),
																('b589cf76', 3, 'Aff_integration_version', '1000006', 'default1', NULL, NULL, NULL),
																('b6f50a3d', 3, 'Aff_ip_validity', '24', 'default1', NULL, NULL, NULL),
																('b719fd51', 4, 'Aff_user_yahoomessenger', '', 'default1', '1', NULL, NULL),
																('b855227b', 3, 'Aff_style_c_frm_button_light', '#F5F5F5', 'default1', NULL, NULL, NULL),
																('b8f8aa2e', 3, 'Aff_signup_country_mandatory', 'true', 'default1', NULL, NULL, NULL),
																('b94d0339', 3, 'Aff_signup_data5_name', '', 'default1', NULL, NULL, NULL),
																('bc17a4e9', 4, 'Aff_user_icq', '', 'default1', '1', NULL, NULL),
																('be76e83b', 3, 'Aff_style_c_actionheader', '#FFFFFF', 'default1', NULL, NULL, NULL),
																('bea3f852', 3, 'Aff_signup_data2', '0', 'default1', NULL, NULL, NULL),
																('c1507b66', 3, 'Aff_style_c_border', '#5993AB', 'default1', NULL, NULL, NULL),
																('c48510c6', 3, 'Aff_signup_username_mandatory', 'true', 'default1', NULL, NULL, NULL),
																('c5ac8f03', 3, 'Aff_signup_fax', '1', 'default1', NULL, NULL, NULL),
																('c92117b5', 3, 'Aff_settings_faq_customdescription', '<table width=\"780\" cellspacing=0 cellpadding-0>\r\n<tr>\r\n<td align=left>\r\n\r\n<table style=\"padding: 0px\" border=0 cellspacing=0 cellpadding=2>\r\n    <tr><td align=left>? ? <a class=apFaq href=\"#whatineed\">1. How to start promoting your products?</a></td></tr>\r\n    <tr><td align=left>? ? <a class=apFaq href=\"#howpaid\">2. How do I know I will be paid for my referral?</a></td></tr>\r\n    <tr><td align=left>? ? <a class=apFaq href=\"#payment\">3. How is the payment handled?</a></td></tr>\r\n    <tr><td align=left>? ? <a class=apFaq href=\"#afflink\">4. What is the affiliate link?</a></td></tr>\r\n    <tr><td align=left>? ? <a class=apFaq href=\"#ppc\">5. Can I promote you through pay-per-click search engines?</a></td></tr>\r\n    <tr><td align=left>? ? <a class=apFaq href=\"#training\">6. Do you have any training program for affiliates?</a></td></tr>\r\n</table>\r\n</p>\r\n\r\n<br><br>\r\n<p>\r\n<a name=\"whatineed\"></a>\r\n<span class=\"apFaqAnswer\">1. How to start promoting your products?</span><br/>\r\nYou can see the Getting Started section on the main page after you log-in. \r\n<br>\r\nGeneraly, you should choose some banners and links, and place them onto your pages. When your visitor clicks on a banner, he will be redirected to our page. If this visitor buys something, you will be rewarded with a commission for this sale.\r\n</p>\r\n\r\n<p>\r\n<a name=\"howpaid\"></a>\r\n<span class=\"apFaqAnswer\">2. How do I know I will be paid for my referral?</span><br/>\r\nThe program is powered by <b>Post Affiliate Pro</b>, the leading affiliate tracking software.\r\nPost Affiliate Pro uses combination of cookies and IP address to track referrals for best possible reliability. \r\nWhen the visitor follows your affiliate link to our site, our affiliate system registers this referral and places cookie on his or her computer. \r\nWhen the visitor pays for the product, affiliate system checks for cookie (if not found, checks for IP address of referral) and credits \r\nyour account with commission. \r\n<br/>\r\nThis process is absolutely automatic. All your referrals will be properly tracked.\r\n</p>\r\n<p>\r\nPost Affiliate Pro is used by thousands of Internet merchants and affiliates world-wide. \r\n</p>\r\n\r\n<p>\r\n<a name=\"payment\"></a>\r\n<span class=\"apFaqAnswer\">3. How is the payment handled?</span><br/>\r\nYou can choose if you want to be paid by PayPal or by wire transfer, as well as minimum payout value ($100 minimum). \r\nWe do not support regular checks at the moment.\r\n<br/>\r\nPayments are issues in US dollars, and are paid <b>once a month</b>, always on 15th. \r\n</p>\r\n<p>\r\n<a name=\"afflink\"></a>\r\n<span class=\"apFaqAnswer\">4. What is the affiliate link?</span><br/>\r\nAffiliate link is a special URL where you should be sending the visitors. You will get the URL''s for different banners\r\nin your affiliate panel after log in. \r\n</p>\r\n\r\n<p>\r\n<a name=\"ppc\"></a>\r\n<span class=\"apFaqAnswer\">5. Can I promote you through pay-per-click search engines?</span><br/>\r\nYES! You can promote us through pay-per-click search engines. In fact, this type of promotion becomes \r\nincreasingly popular and we are aware of several affiliates promoting our products in this way and making a very good profit. \r\n(Well, they keep promoting us month after month, which tells you something, doesn''t it?)\r\n\r\nIf you are not familiar with with type of promotion, we recommend <a href=\"http://netquality.wingcube.hop.clickbank.net/\" target=_blank>this excellent ebook</a>. \r\nSome people are making MILLIONS promoting other people''s products - why not you?\r\n</p>\r\n\r\n<p>\r\n<a name=\"whatnow\"></a>\r\n<span class=\"apFaqAnswer\">6. Do you have any training program for affiliates?</span><br/>\r\nThe basics of affiliate marketing and most useful tips are described in your affiliate panel. \r\nYou can find new tips and techniques also in our affiliate newsletter.\r\n<br/>\r\nIf you are seriouswithearning yourincome as an affiliate, we recommend \r\nexcellent <a href=\"http://www.superaffiliatehandbook.com/cbae/?a=Zkjh06wHjL\" target=_blank>SuperAffiliate Handbook</a>.\r\n</p>\r\n\r\n</td>\r\n</tr>\r\n</table>', 'default1', NULL, NULL, NULL),
																('c942a1e7', 3, 'Aff_settings_quick_customdescription', '', 'default1', NULL, NULL, NULL),
																('c9ca2889', 3, 'Aff_style_c_error_header', '#FFA9A9', 'default1', NULL, NULL, NULL),
																('caddaeeb', 3, 'Aff_signup_data4_mandatory', 'hide', 'default1', NULL, NULL, NULL),
																('cbbae032', 4, 'Aff_user_photo_url', '../affiliate-manager.gif', 'default1', '1', NULL, NULL),
																('cc22b9eb', 3, 'Aff_menu_item_faq_show', 'true', 'default1', NULL, NULL, NULL),
																('ce0b9f15', 3, 'Aff_style_c_textlink', '#0056B6', 'default1', NULL, NULL, NULL),
																('cee8a009', 3, 'Aff_signup_data3_name', '', 'default1', NULL, NULL, NULL),
																('d5f3a27e', 3, 'Aff_signup_data1_mandatory', 'hide', 'default1', NULL, NULL, NULL),
																('d69da25b', 3, 'Aff_signup_company_name_mandatory', 'false', 'default1', NULL, NULL, NULL),
																('d6fab855', 3, 'Aff_style_c_frm_button_shadow', '#B4B4B6', 'default1', NULL, NULL, NULL),
																('d74f71be', 3, 'Aff_debug_clicks', '1', 'default1', NULL, NULL, NULL),
																('d78b2875', 3, 'Aff_signup_surname', '1', 'default1', NULL, NULL, NULL),
																('d8d02be9', 3, 'Aff_signup_display_description', '1', 'default1', NULL, NULL, NULL),
																('d95dcfc1', 3, 'Aff_signup_state_mandatory', 'false', 'default1', NULL, NULL, NULL),
																('da426588', 3, 'Aff_signup_tax_ssn_mandatory', 'false', 'default1', NULL, NULL, NULL),
																('daf4aa18', 3, 'Aff_settings_banners_show_description', 'true', 'default1', NULL, NULL, NULL),
																('dcce7fe3', 3, 'Aff_settings_subaffsignup_customdescription', '', 'default1', NULL, NULL, NULL),
																('dd234c81', 3, 'Aff_style_c_ok_message', '#00AA00', 'default1', NULL, NULL, NULL),
																('de243cc7', 3, 'Aff_signup_phone', '1', 'default1', NULL, NULL, NULL),
																('dfde94a8', 3, 'Aff_signup_data5', '0', 'default1', NULL, NULL, NULL),
																('ds5fds68', 5, 'Aff_min_payout', '300', 'default1', '2', NULL, NULL),
																('ds5fds69', 5, 'Aff_payoptionfield_paypal01', '<EMAIL>', 'default1', '2', 'paypal01', NULL),
																('e1ecb26f', 3, 'Aff_settings_quick_show_description', 'true', 'default1', NULL, NULL, NULL),
																('e21dd880', 3, 'Aff_signup_company_name', '1', 'default1', NULL, NULL, NULL),
																('e6c1cf39', 3, 'Aff_debug_impressions', '1', 'default1', NULL, NULL, NULL),
																('e71940ac', 3, 'Aff_signup_name', '1', 'default1', NULL, NULL, NULL),
																('e7c4ecce', 3, 'Aff_settings_faq_show_description', 'true', 'default1', NULL, NULL, NULL),
																('ea21e59e', 3, 'Aff_signup_phone_mandatory', 'false', 'default1', NULL, NULL, NULL),
																('eb161702', 3, 'Aff_p3p_compact', 'NOI NID ADMa DEVa PSAa OUR BUS ONL UNI COM STA OTC', 'default1', NULL, NULL, NULL),
																('ebac34ee', 3, 'Aff_style_c_listheader_sort', '#B3CED9', 'default1', NULL, NULL, NULL),
																('eca39e30', 3, 'Aff_signup_data3', '0', 'default1', NULL, NULL, NULL),
																('efd4dd7e', 4, 'Aff_user_other_contact', '', 'default1', '1', NULL, NULL),
																('f0280398', 3, 'Aff_main_site_url', 'http://a07.localhost', 'default1', '1', NULL, NULL),
																('f3622c39', 3, 'Aff_settings_afftopurls_show_description', 'true', 'default1', NULL, NULL, NULL),
																('f3e2e9f6', 3, 'Aff_signup_data4_name', '', 'default1', NULL, NULL, NULL),
																('f66a4b8d', 3, 'Aff_system_email', '<EMAIL>', 'default1', '1', NULL, NULL),
																('f7869da1', 3, 'Aff_style_c_menu_link2', '#0056B6', 'default1', NULL, NULL, NULL),
																('f800843e', 3, 'Aff_settings_contactus_show_description', 'true', 'default1', NULL, NULL, NULL),
																('f854040e', 3, 'Aff_signup_username', '1', 'default1', NULL, NULL, NULL),
																('f8542885', 3, 'Aff_signup_tax_ssn', '1', 'default1', NULL, NULL, NULL),
																('f878eda6', 3, 'Aff_style_c_datail_row2', '#F2F5FC', 'default1', NULL, NULL, NULL),
																('fdc43348', 3, 'Aff_menu_item_trans_show', 'true', 'default1', NULL, NULL, NULL),
																('fe7515f7', 3, 'Aff_settings_quick_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('feb83217', 3, 'Aff_style_affiliate_skin', 'default', 'default1', '1', NULL, NULL),
																('ff582f2d', 3, 'Aff_settings_transactions_show_customdescription', 'false', 'default1', NULL, NULL, NULL),
																('ffb97112', 3, 'Aff_export_url', 'http://a07.localhost/a07/affiliate/exports/', 'default1', '1', NULL, NULL);"
										);

$add_new_tables["wd_g_usergroups"] = array (	"structure" => "CREATE TABLE `wd_g_usergroups` (
																  `usergroupid` char(8) NOT NULL default '',
																  `userid` char(8) NOT NULL default '',
																  `groupid` char(8) NOT NULL default '',
																  PRIMARY KEY  (`usergroupid`),
																  KEY `IDX_wd_g_usergroups1` (`userid`),
																  KEY `IDX_wd_g_usergroups2` (`groupid`)
																) TYPE=MyISAM;",
												"data" => ""
											);

$add_new_tables["wd_g_userprofiles"] = array (	"structure" => "CREATE TABLE `wd_g_userprofiles` (
																  `userprofileid` varchar(8) NOT NULL default '',
																  `name` varchar(100) NOT NULL default '',
																  `rtype` varchar(20) NOT NULL default '',
																  `accountid` varchar(8) default NULL,
																  PRIMARY KEY  (`userprofileid`),
																  UNIQUE KEY `IDX_wd_g_userprofiles1` (`userprofileid`),
																  KEY `IDX_wd_g_userprofiles2` (`accountid`)
																) TYPE=MyISAM;",
												"data" => "	INSERT INTO `wd_g_userprofiles` (`userprofileid`, `name`, `rtype`, `accountid`) 
															VALUES 	('userpro1', 'Default admin profile', '3', 'default1')
																	;"
											);

$add_new_tables["wd_g_userrights"] = array ("structure" => "CREATE TABLE `wd_g_userrights` (
															  `userrightid` char(8) NOT NULL default '',
															  `userprofileid` char(8) NOT NULL default '',
															  `righttypeid` char(8) default NULL,
															  PRIMARY KEY  (`userrightid`),
															  KEY `IDX_wd_g_userrights1` (`userprofileid`),
															  KEY `IDX_wd_g_userrights2` (`righttypeid`)
															) TYPE=MyISAM;",
											"data" => "	INSERT INTO `wd_g_userrights` (`userrightid`, `userprofileid`, `righttypeid`) 
														VALUES 	('0723cf68', 'userpro1', '29'),
																('0acf1810', 'userpro1', '13'),
																('0e8581e8', 'userpro1', '30'),
																('0f2295a4', 'userpro1', '17'),
																('18f2a81a', 'userpro1', '24'),
																('303322f3', 'userpro1', '01'),
																('3591ac15', 'userpro1', '05'),
																('39d51858', 'userpro1', '22'),
																('44d46fef', 'userpro1', '28'),
																('46cb70c0', 'userpro1', '26'),
																('55bae64f', 'userpro1', '12'),
																('62670421', 'userpro1', '09'),
																('6bfdc423', 'userpro1', '11'),
																('6d0469b2', 'userpro1', '03'),
																('715114df', 'userpro1', '33'),
																('75d8c0f7', 'userpro1', '15'),
																('79cc1788', 'userpro1', '35'),
																('7a209eb2', 'userpro1', '14'),
																('8a264905', 'userpro1', '04'),
																('8a721021', 'userpro1', '10'),
																('98edf3f2', 'userpro1', '02'),
																('9eaa424f', 'userpro1', '07'),
																('a0c939d5', 'userpro1', '31'),
																('c285fdsg', 'userpro1', '39'),
																('c2c671fe', 'userpro1', '16'),
																('c5bcad90', 'userpro1', '32'),
																('c677b9d0', 'userpro1', '21'),
																('c702dc78', 'userpro1', '25'),
																('cdc30ab1', 'userpro1', '37'),
																('cdc59g1u', 'userpro1', '38'),
																('d6f68d7a', 'userpro1', '20'),
																('de631656', 'userpro1', '08'),
																('df484242', 'userpro1', '23'),
																('e4025111', 'userpro1', '19'),
																('e5597917', 'userpro1', '34'),
																('eede7703', 'userpro1', '36'),
																('f5f6a7f6', 'userpro1', '18'),
																('fd95d2e5', 'userpro1', '27'),
																('fe7c978a', 'userpro1', '06'),
																('g45h4g41', 'userpro1', '41'),
																('g45h4g51', 'userpro1', '51'),
																('g45h4g52', 'userpro1', '52'),
																('g45h4g53', 'userpro1', '53'),
																('g45h4g55', 'userpro1', '55'),
																('g45h4g56', 'userpro1', '56'),
																('g45h4g57', 'userpro1', '57'),
																('g45h4g58', 'userpro1', '58'),
																('g45h4g59', 'userpro1', '59'),
																('g45h4g60', 'userpro1', '60'),
																('g45h4g61', 'userpro1', '61'),
																('g45h4g62', 'userpro1', '62'),
																('g45h4g63', 'userpro1', '63'),
																('g45h4g64', 'userpro1', '64'),
																('g45h4g65', 'userpro1', '65'),
																('g45h4g66', 'userpro1', '66'),
																('grte4g41', 'userpro1', '42');"
											);

$add_new_tables["wd_g_users"] = array (	"structure" => "CREATE TABLE `wd_g_users` (
														  `userid` varchar(8) NOT NULL default '',
														  `accountid` varchar(8) NOT NULL default '',
														  `refid` varchar(20) default NULL,
														  `username` varchar(60) NOT NULL default '',
														  `rpassword` varchar(60) NOT NULL default '',
														  `name` varchar(100) default NULL,
														  `surname` varchar(100) default NULL,
														  `rstatus` tinyint(4) NOT NULL default '0',
														  `product` varchar(10) NOT NULL default '',
														  `dateinserted` datetime default NULL,
														  `dateapproved` datetime default NULL,
														  `deleted` tinyint(4) NOT NULL default '0',
														  `userprofileid` varchar(8) default NULL,
														  `rtype` tinyint(4) NOT NULL default '0',
														  `parentuserid` varchar(8) default NULL,
														  `leftnumber` int(11) default NULL,
														  `rightnumber` int(11) default NULL,
														  `users_duration` int(11) NOT NULL default '0',
														  `users_rounddown` int(11) NOT NULL default '0',
														  `company_name` varchar(150) default NULL,
														  `weburl` varchar(250) default NULL,
														  `street` varchar(250) default NULL,
														  `city` varchar(250) default NULL,
														  `state` varchar(250) default NULL,
														  `country` varchar(150) default NULL,
														  `zipcode` varchar(40) default NULL,
														  `phone` varchar(100) default NULL,
														  `fax` varchar(100) default NULL,
														  `tax_ssn` varchar(100) default NULL,
														  `data1` varchar(250) default NULL,
														  `data2` varchar(250) default NULL,
														  `data3` varchar(250) default NULL,
														  `data4` varchar(250) default NULL,
														  `data5` varchar(250) default NULL,
														  `payoptid` varchar(8) default NULL,
														  `originalparentid` varchar(8) default NULL,
														  `flags` tinyint(4) default NULL,
														  PRIMARY KEY  (`userid`),
														  UNIQUE KEY `IDX_wd_g_users3` (`userid`),
														  KEY `IDX_pa_affiliates_1` (`deleted`),
														  KEY `IDX_pa_affiliates_2` (`username`,`rpassword`),
														  KEY `IDX_wd_g_users4` (`accountid`),
														  KEY `IDX_wd_g_users5` (`userprofileid`),
														  KEY `IDX_wd_g_users6` (`parentuserid`),
														  KEY `IDX_wd_g_users7` (`payoptid`),
														  KEY `IDX_wd_g_users8` (`originalparentid`)
														) TYPE=MyISAM;",
										"data" => "	INSERT INTO `wd_g_users` (`userid`, `accountid`, `refid`, `username`, `rpassword`, name, surname, rstatus, product, dateinserted, dateapproved, deleted, userprofileid, rtype, parentuserid, leftnumber, rightnumber, users_duration, users_rounddown, company_name, weburl, street, city, state, country, zipcode, phone, fax, tax_ssn, data1, data2, data3, data4, data5, payoptid, originalparentid, flags) 
													VALUES 	('1', 'default1', NULL, '<EMAIL>', 'pass', 'adminname', 'adminsurname', 0, '', now(), NULL, 0, 'userpro1', 3, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);"
									);

$add_new_tables["wd_pa_accounting"] = array (	"structure" => "CREATE TABLE `wd_pa_accounting` (
																  `accountingid` varchar(8) NOT NULL default '',
																  `dateinserted` datetime NOT NULL default '0000-00-00 00:00:00',
																  `datefrom` datetime NOT NULL default '0000-00-00 00:00:00',
																  `dateto` datetime NOT NULL default '0000-00-00 00:00:00',
																  `note` text,
																  `rfile` varchar(200) default NULL,
																  `mbfile` varchar(100) default NULL,
																  `wirefile` varchar(100) default NULL,
																  PRIMARY KEY  (`accountingid`),
																  UNIQUE KEY `IDX_wd_pa_accounting1` (`accountingid`)
																) TYPE=MyISAM;",
												"data" => ""
											);

$add_new_tables["wd_pa_affiliatescampaigns"] = array (	"structure" => "CREATE TABLE `wd_pa_affiliatescampaigns` (
																		  `affcampid` varchar(8) NOT NULL default '',
																		  `campcategoryid` varchar(8) default NULL,
																		  `affiliateid` varchar(8) default NULL,
																		  `campaignid` varchar(8) NOT NULL default '',
																		  `rstatus` tinyint(4) NOT NULL default '0',
																		  `declinereason` varchar(250) default NULL,
																		  PRIMARY KEY  (`affcampid`),
																		  KEY `IDX_wd_pa_affiliatescampaigns1` (`campcategoryid`),
																		  KEY `IDX_wd_pa_affiliatescampaigns2` (`affiliateid`),
																		  KEY `IDX_wd_pa_affiliatescampaigns3` (`campaignid`)
																		) TYPE=MyISAM;",
														"data" => ""
													);

$add_new_tables["wd_pa_bannercategories"] = array (	"structure" => "CREATE TABLE `wd_pa_bannercategories` (
																	  `bannercategoryid` varchar(8) NOT NULL default '',
																	  `name` varchar(255) NOT NULL default '',
																	  PRIMARY KEY  (`bannercategoryid`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

$add_new_tables["wd_pa_banners"] = array (	"structure" => "CREATE TABLE `wd_pa_banners` (
															  `bannerid` varchar(8) NOT NULL default '',
															  `name` varchar(100) default NULL,
															  `description` text,
															  `destinationurl` text,
															  `sourceurl` text,
															  `bannertype` tinyint(4) default NULL,
															  `deleted` tinyint(4) default '0',
															  `campaignid` varchar(8) default NULL,
															  `hidden` tinyint(4) default '0',
															  `size` varchar(11) default NULL,
															  `dateinserted` datetime default NULL,
															  `bannercategory` varchar(8) NOT NULL default '',
															  PRIMARY KEY  (`bannerid`),
															  UNIQUE KEY `IDX_wd_pa_banners2` (`bannerid`),
															  KEY `IDX_pa_banners_1` (`campaignid`)
															) TYPE=MyISAM;",
											"data" => ""
										);

$add_new_tables["wd_pa_campaigncategories"] = array (	"structure" => "CREATE TABLE `wd_pa_campaigncategories` (
																		  `campcategoryid` varchar(8) NOT NULL default '',
																		  `name` varchar(100) default NULL,
																		  `deleted` tinyint(4) NOT NULL default '0',
																		  `cpmcommission` float default NULL,
																		  `clickcommission` float default NULL,
																		  `salecommission` float default NULL,
																		  `recurringcommission` float default NULL,
																		  `recurringcommtype` varchar(5) default NULL,
																		  `recurringcommdate` int(11) default NULL,
																		  `recurringdatetype` tinyint(4) default NULL,
																		  `campaignid` varchar(8) default NULL,
																		  `salecommtype` varchar(5) default NULL,
																		  `stsalecommtype` varchar(5) default NULL,
																		  `st2clickcommission` float default NULL,
																		  `st2salecommission` float default NULL,
																		  `st3clickcommission` float default NULL,
																		  `st3salecommission` float default NULL,
																		  `st4clickcommission` float default NULL,
																		  `st4salecommission` float default NULL,
																		  `st5clickcommission` float default NULL,
																		  `st5salecommission` float default NULL,
																		  `st6clickcommission` float default NULL,
																		  `st6salecommission` float default NULL,
																		  `st7clickcommission` float default NULL,
																		  `st7salecommission` float default NULL,
																		  `st8clickcommission` float default NULL,
																		  `st8salecommission` float default NULL,
																		  `st9clickcommission` float default NULL,
																		  `st9salecommission` float default NULL,
																		  `st10clickcommission` float default NULL,
																		  `st10salecommission` float default NULL,
																		  `strecurringcommtype` varchar(5) default NULL,
																		  `st2recurringcommission` float default NULL,
																		  `st3recurringcommission` float default NULL,
																		  `st4recurringcommission` float default NULL,
																		  `st5recurringcommission` float default NULL,
																		  `st6recurringcommission` float default NULL,
																		  `st7recurringcommission` float default NULL,
																		  `st8recurringcommission` float default NULL,
																		  `st9recurringcommission` float default NULL,
																		  `st10recurringcommission` float default NULL,
																		  PRIMARY KEY  (`campcategoryid`),
																		  UNIQUE KEY `IDX_wd_pa_campaigncategories2` (`campcategoryid`),
																		  KEY `IDX_pa_affiliatecategories_1` (`campaignid`)
																		) TYPE=MyISAM;",
										"data" => "	INSERT INTO `wd_pa_campaigncategories` (campcategoryid, name, deleted, cpmcommission, clickcommission, salecommission, recurringcommission, recurringcommtype, recurringcommdate, recurringdatetype, campaignid, salecommtype, stsalecommtype, st2clickcommission, st2salecommission, st3clickcommission, st3salecommission, st4clickcommission, st4salecommission, st5clickcommission, st5salecommission, st6clickcommission, st6salecommission, st7clickcommission, st7salecommission, st8clickcommission, st8salecommission, st9clickcommission, st9salecommission, st10clickcommission, st10salecommission, strecurringcommtype, st2recurringcommission, st3recurringcommission, st4recurringcommission, st5recurringcommission, st6recurringcommission, st7recurringcommission, st8recurringcommission, st9recurringcommission, st10recurringcommission) 
													VALUES ('1', 'L_G_UNASSIGNED_USERS', 0, 0, 10, 20, 0, '$', NULL, 1, '1', '$', '$', 0, 9, 0, 8, 0, 7, 0, 6, 0, 5, 0, 4, 0, 3, 0, 2, 0, 1, '$', 0, 0, 0, 0, 0, 0, 0, 0, 0);"
									);

$add_new_tables["wd_pa_campaigns"] = array ("structure" => "CREATE TABLE `wd_pa_campaigns` (
															  `campaignid` varchar(8) NOT NULL default '',
															  `accountid` varchar(8) default NULL,
															  `name` varchar(100) default NULL,
															  `description` text,
															  `shortdescription` varchar(200) default NULL,
															  `dateinserted` datetime default NULL,
															  `deleted` tinyint(4) NOT NULL default '0',
															  `disabled` tinyint(4) NOT NULL default '0',
															  `commtype` tinyint(4) NOT NULL default '0',
															  `products` text,
															  PRIMARY KEY  (`campaignid`),
															  UNIQUE KEY `IDX_wd_pa_campaigns1` (`campaignid`),
															  KEY `IDX_wd_pa_campaigns2` (`accountid`)
															) TYPE=MyISAM;",
											"data" => ""
										);

$add_new_tables["wd_pa_impressions"] = array (	"structure" => "CREATE TABLE `wd_pa_impressions` (
																  `impressionid` varchar(8) NOT NULL default '',
																  `accountid` varchar(8) default NULL,
																  `dateimpression` datetime NOT NULL default '0000-00-00 00:00:00',
																  `bannerid` varchar(8) default NULL,
																  `rotatorid` varchar(8) default NULL,
																  `affiliateid` varchar(8) default NULL,
																  `all_imps_count` int(11) default '0',
																  `unique_imps_count` int(11) default '0',
																  `commissiongiven` tinyint(4) NOT NULL default '0',
																  `data1` varchar(80) default NULL,
																  `country` char(2) default '__',
																  PRIMARY KEY  (`impressionid`),
																  KEY `IDX_pa_impressions_3` (`bannerid`,`affiliateid`,`dateimpression`),
																  KEY `IDX_wd_pa_impressions2` (`bannerid`),
																  KEY `IDX_wd_pa_impressions3` (`affiliateid`),
																  KEY `IDX_wd_pa_impressions4` (`accountid`)
																) TYPE=MyISAM;",
												"data" => ""
											);

$add_new_tables["wd_pa_impressions_tmp"] = array (	"structure" => "CREATE TABLE `wd_pa_impressions_tmp` (
																	  `impressionid` varchar(8) NOT NULL default '',
																	  `accountid` varchar(8) default NULL,
																	  `dateimpression` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `bannerid` varchar(8) default NULL,
																	  `rotatorid` varchar(8) default NULL,
																	  `affiliateid` varchar(8) default NULL,
																	  `all_imps_count` int(11) default '0',
																	  `unique_imps_count` int(11) default '0',
																	  `commissiongiven` tinyint(4) NOT NULL default '0',
																	  `data1` varchar(80) default NULL,
																	  `country` char(2) default '__',
																	  PRIMARY KEY  (`impressionid`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

$add_new_tables["wd_pa_payoutfields"] = array (	"structure" => "CREATE TABLE `wd_pa_payoutfields` (
																  `payfieldid` varchar(8) NOT NULL default '',
																  `payoptid` varchar(8) NOT NULL default '',
																  `code` varchar(20) NOT NULL default '',
																  `name` varchar(40) NOT NULL default '',
																  `langid` varchar(80) NOT NULL default '',
																  `rtype` tinyint(4) NOT NULL default '0',
																  `mandatory` tinyint(4) NOT NULL default '0',
																  `visible` tinyint(4) NOT NULL default '0',
																  `availablevalues` text,
																  `rorder` tinyint(3) unsigned NOT NULL default '0',
																  `value` text,
																  PRIMARY KEY  (`payfieldid`),
																  KEY `IDX_wd_pa_payoutfields1` (`payoptid`)
																) TYPE=MyISAM;",
												"data" => ""
											);

$add_new_tables["wd_pa_payoutoptions"] = array ("structure" => "CREATE TABLE `wd_pa_payoutoptions` (
																  `payoptid` varchar(8) NOT NULL default '',
																  `accountid` varchar(8) NOT NULL default '',
																  `name` varchar(100) NOT NULL default '',
																  `exporttype` tinyint(4) NOT NULL default '0',
																  `exportformat` text,
																  `disabled` tinyint(4) NOT NULL default '0',
																  `langid` varchar(80) NOT NULL default '',
																  `rorder` tinyint(4) NOT NULL default '0',
																  `paybuttonformat` text,
																  PRIMARY KEY  (`payoptid`),
																  UNIQUE KEY `IDX_wd_pa_payoutoptions1` (`payoptid`),
																  KEY `IDX_wd_pa_payoutoptions2` (`accountid`)
																) TYPE=MyISAM;",
												"data" => ""
											);

$add_new_tables["wd_pa_recurringcommissions"] = array (	"structure" => "CREATE TABLE `wd_pa_recurringcommissions` (
																		  `recurringcommid` varchar(8) NOT NULL default '',
																		  `commission` float NOT NULL default '0',
																		  `commtype` varchar(5) default NULL,
																		  `commdate` tinyint(4) NOT NULL default '0',
																		  `datetype` tinyint(4) NOT NULL default '0',
																		  `rstatus` tinyint(4) NOT NULL default '0',
																		  `deleted` tinyint(4) default '0',
																		  `campcategoryid` varchar(8) default NULL,
																		  `affiliateid` varchar(8) default NULL,
																		  `originaltransid` varchar(8) default NULL,
																		  `dateinserted` datetime NOT NULL default '0000-00-00 00:00:00',
																		  `stcommtype` varchar(5) default NULL,
																		  `st2affiliateid` varchar(8) default NULL,
																		  `st2commission` float default NULL,
																		  `st3affiliateid` varchar(8) default NULL,
																		  `st3commission` float default NULL,
																		  `st4affiliateid` varchar(8) default NULL,
																		  `st4commission` float default NULL,
																		  `st5affiliateid` varchar(8) default NULL,
																		  `st5commission` float default NULL,
																		  `st6affiliateid` varchar(8) default NULL,
																		  `st6commission` float default NULL,
																		  `st7affiliateid` varchar(8) default NULL,
																		  `st7commission` float default NULL,
																		  `st8affiliateid` varchar(8) default NULL,
																		  `st8commission` float default NULL,
																		  `st9affiliateid` varchar(8) default NULL,
																		  `st9commission` float default NULL,
																		  `st10affiliateid` varchar(8) default NULL,
																		  `st10commission` float default NULL,
																		  PRIMARY KEY  (`recurringcommid`),
																		  UNIQUE KEY `IDX_wd_pa_recurringcommissions4` (`recurringcommid`),
																		  KEY `IDX_wd_pa_recurringcommissions1` (`campcategoryid`),
																		  KEY `IDX_wd_pa_recurringcommissions2` (`affiliateid`),
																		  KEY `IDX_wd_pa_recurringcommissions3` (`originaltransid`)
																		) TYPE=MyISAM;",
														"data" => ""
													);

$add_new_tables["wd_pa_rules"] = array ("structure" => "CREATE TABLE `wd_pa_rules` (
														  `ruleid` varchar(8) NOT NULL default '',
														  `name` varchar(100) default NULL,
														  `cond_when` varchar(20) default NULL,
														  `cond_in` varchar(20) default NULL,
														  `cond_is` varchar(20) default NULL,
														  `cond_value1` varchar(40) default NULL,
														  `cond_value2` varchar(40) default NULL,
														  `cond_action` varchar(40) default NULL,
														  `cond_action_value` varchar(100) default NULL,
														  `accountid` varchar(8) NOT NULL default '',
														  `campaignid` varchar(8) NOT NULL default '',
														  PRIMARY KEY  (`ruleid`,`accountid`,`campaignid`),
														  KEY `IDX_wd_pa_rules1` (`accountid`),
														  KEY `IDX_wd_pa_rules2` (`campaignid`)
														) TYPE=MyISAM;",
										"data" => ""
									);

$add_new_tables["wd_pa_transactions"] = array (	"structure" => "CREATE TABLE `wd_pa_transactions` (
																  `transid` varchar(8) NOT NULL default '',
																  `accountid` varchar(8) default NULL,
																  `rstatus` tinyint(4) NOT NULL default '0',
																  `transaction_rstatus_hold` tinyint(4) NOT NULL default '0',
																  `dateinserted` datetime default NULL,
																  `dateapproved` datetime default NULL,
																  `transtype` smallint(6) default '0',
																  `transtype2` smallint(6) NOT NULL default '0',
																  `payoutstatus` tinyint(4) default '1',
																  `datepayout` datetime default NULL,
																  `cookiestatus` tinyint(4) default NULL,
																  `orderid` varchar(200) default NULL,
																  `totalcost` float default NULL,
																  `bannerid` varchar(8) default NULL,
																  `rotatorid` varchar(8) default NULL,
																  `transkind` tinyint(4) default '0',
																  `refererurl` varchar(250) default NULL,
																  `affiliateid` varchar(8) default NULL,
																  `campcategoryid` varchar(8) default NULL,
																  `parenttransid` varchar(8) default NULL,
																  `commission` float default '0',
																  `ip` varchar(20) default NULL,
																  `countrycode` char(2) default NULL,
																  `recurringcommid` varchar(8) default NULL,
																  `accountingid` varchar(8) default NULL,
																  `productid` varchar(200) default NULL,
																  `data1` varchar(80) default NULL,
																  `data2` varchar(80) default NULL,
																  `data3` varchar(80) default NULL,
																  `browser` varchar(6) default NULL,
																  `count` int(11) default '1',
																  PRIMARY KEY  (`transid`),
																  UNIQUE KEY `IDX_wd_pa_transactions7` (`transid`),
																  KEY `IDX_pa_transactions_2` (`dateinserted`),
																  KEY `IDX_pa_transactions_3` (`transkind`,`transtype`,`rstatus`),
																  KEY `IDX_pa_transactions_4` (`campcategoryid`),
																  KEY `IDX_wd_pa_transactions4` (`bannerid`),
																  KEY `IDX_wd_pa_transactions5` (`affiliateid`),
																  KEY `IDX_wd_pa_transactions6` (`parenttransid`),
																  KEY `IDX_wd_pa_transactions8` (`recurringcommid`),
																  KEY `IDX_wd_pa_transactions9` (`accountingid`),
																  KEY `IDX_wd_pa_transactions10` (`accountid`)
																) TYPE=MyISAM;",
												"data" => ""
											);

$add_new_tables["wd_pa_transactions_tmp"] = array (	"structure" => "CREATE TABLE `wd_pa_transactions_tmp` (
																	  `transid` varchar(8) NOT NULL default '',
																	  `accountid` varchar(8) default NULL,
																	  `rstatus` tinyint(4) NOT NULL default '0',
																	  `dateinserted` datetime default NULL,
																	  `dateapproved` datetime default NULL,
																	  `transtype` smallint(6) default '0',
																	  `payoutstatus` tinyint(4) default '1',
																	  `datepayout` datetime default NULL,
																	  `cookiestatus` tinyint(4) default NULL,
																	  `orderid` varchar(200) default NULL,
																	  `totalcost` float default NULL,
																	  `bannerid` varchar(8) default NULL,
																	  `rotatorid` varchar(8) default NULL,
																	  `transkind` tinyint(4) default '0',
																	  `refererurl` varchar(250) default NULL,
																	  `affiliateid` varchar(8) default NULL,
																	  `campcategoryid` varchar(8) default NULL,
																	  `parenttransid` varchar(8) default NULL,
																	  `commission` float default '0',
																	  `ip` varchar(20) default NULL,
																	  `countrycode` char(2) default NULL,
																	  `recurringcommid` varchar(8) default NULL,
																	  `accountingid` varchar(8) default NULL,
																	  `productid` varchar(200) default NULL,
																	  `data1` varchar(80) default NULL,
																	  `data2` varchar(80) default NULL,
																	  `data3` varchar(80) default NULL,
																	  `browser` varchar(6) default NULL,
																	  `count` int(11) default '1',
																	  PRIMARY KEY  (`transid`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create new tables for affiliate module

// Insert new fields into customers table
$add_new_field = array();

$add_new_field['customers'] = array (	array (	"field_name" => "customers_reserve_amount",
												"field_attr" => " decimal(15,4) NOT NULL default '0.0000' ",
												"add_after" => "customers_login_sites"
												)
									);

add_field($add_new_field);
// End of insert new fields into customers table


// Insert new records into configuration table (for profiler upload alert)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Custom Product'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["CP_PROGRESS_PROFILE_ALERT_TIME"] = array("insert" => " ('Custom Product Progress Profile Alert Time', 'CP_PROGRESS_PROFILE_ALERT_TIME', '1440', 'Maximum allowable time for uploading of latest custom product progress profile (in minutes) or alert will be trigger for that progress report.', ".$row_sql["configuration_group_id"].", 12, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for profiler upload alert)

?>