<?
/*
	$Id: version_2_7_1.php,v 1.1 2007/03/29 07:36:29 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on viewing admin / supplier mail & trade logs)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='trade_mail_log.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["VIEW_ADMIN_MT_LOGS"] = array("insert" => " ('VIEW_ADMIN_MT_LOGS', 'View Trade & Mail logs by admin', ".$row_sql["admin_files_id"].", '1', 10)" );
	$admin_files_actions_insert_sql["VIEW_SUPPLIER_MT_LOGS"] = array("insert" => " ('VIEW_SUPPLIER_MT_LOGS', 'View Trade & Mail logs by suppliers', ".$row_sql["admin_files_id"].", '1', 12)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing admin / supplier mail & trade logs)

// Insert new fields into user_favourite_products table
$add_new_field = array();

$add_new_field['orders_comments'] = array (	array (	"field_name" => "orders_comments_filename",
													"field_attr" => " varchar(255) NOT NULL default 'orders.php' ",
													"add_after" => ""
													)
										);

$add_new_field['user_favourite_products'] = array (	array (	"field_name" => "favourite_products_presale_notice",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => ""
															)
												);

add_field($add_new_field);
// End of insert new fields into user_favourite_products table

// Update existing character log user email to user id
$supplier_id_select_sql = "	SELECT s.supplier_id, s.supplier_email_address 
							FROM supplier AS s 
							INNER JOIN . " . TABLE_GAME_CHAR_LOG . " AS gcl 
								ON (gcl.game_char_log_login_user = s.supplier_email_address) 
							GROUP BY gcl.game_char_log_login_user";
$supplier_id_result_sql = tep_db_query($supplier_id_select_sql);
while ($supplier_id_row = tep_db_fetch_array($supplier_id_result_sql)) {
	$sql_data_array = array('game_char_log_login_user' => $supplier_id_row['supplier_id']);
	
	tep_db_perform(TABLE_GAME_CHAR_LOG, $sql_data_array, 'update', "game_char_log_login_user = '" . tep_db_input($supplier_id_row['supplier_email_address']) . "'");
}

// End of update existing character log user email to user id
?>