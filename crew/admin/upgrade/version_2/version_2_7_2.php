<?
/*
	$Id: version_2_7_2.php,v 1.1 2007/03/30 11:35:38 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on setting customer reserve amount)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_RESERVE_AMOUNT"] = array("insert" => " ('CUSTOMER_RESERVE_AMOUNT', 'Set withdraw reserve amount', ".$row_sql["admin_files_id"].", '1', 8)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on setting customer reserve amount)

// Insert new fields into wd_pa_transactions table
$add_new_field = array();

$add_new_field['wd_pa_transactions'] = array (	array (	"field_name" => "currency",
														"field_attr" => " char(3) default NULL ",
														"add_after" => "transaction_rstatus_hold"
														),
												array (	"field_name" => "currency_value",
														"field_attr" => " decimal(14,6) default NULL ",
														"add_after" => "currency"
														)
											);

add_field($add_new_field);
// End of insert new fields into wd_pa_transactions table

?>