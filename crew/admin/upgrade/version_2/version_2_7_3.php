<?
/*
	$Id: version_2_7_3.php,v 1.2 2007/04/05 03:21:18 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into orders_products and site_code tables
$add_new_field = array();

$add_new_field['orders_products'] = array (	array (	"field_name" => "orders_products_purchase_eta",
													"field_attr" => " smallint(1) default NULL ",
													"add_after" => "custom_products_type_id"
													)
										);

$add_new_field['site_code'] = array (	array (	"field_name" => "site_has_buyback",
												"field_attr" => " tinyint(1) NOT NULL default '0' ",
												"add_after" => ""
												),
										array (	"field_name" => "admin_groups_id",
												"field_attr" => " text NOT NULL ",
												"add_after" => ""
												)
									);

$add_new_field['wd_pa_campaigns'] = array (	array (	"field_name" => "firstsaleonly",
													"field_attr" => " smallint(1) NOT NULL default '0' ",
													"add_after" => "commtype"
													)
										);

add_field($add_new_field);
// End of insert new fields into orders_products and site_code tables

// Insert new records into site_code table (for Affiliate site)
$site_id_insert_sql = array();

$site_id_insert_sql["2"] = array(	"insert" => " ('2', 'Affiliate', '0', '1,36') " );

insert_new_records("site_code", "site_id", $site_id_insert_sql, $DBTables, "(site_id, site_name, site_has_buyback, admin_groups_id)", "");
// End of insert new records into site_code table (for Affiliate site)

// Update records in site_code table (assign site_has_buyback and admin_groups_id setting)
$site_code_update_sql = array();

$site_code_update_sql["0"] = array("update" => " site_has_buyback='1', admin_groups_id='1,20,27,31,32,35,36,37,38,39,40' " );
$site_code_update_sql["1"] = array("update" => " site_has_buyback='1', admin_groups_id='1,41' " );

update_records("site_code", "site_id", $site_code_update_sql, $DBTables);
// End of update records in site_code table (assign site_has_buyback and admin_groups_id setting)

// Insert new records into admin_files table (for customer type admin access configuration)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='administrator.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["customers_type_conf.php"] = array(	"insert" => " ('customers_type_conf.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   										);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='customers_type_conf.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for customer type admin access configuration)

?>