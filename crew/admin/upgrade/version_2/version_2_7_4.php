<?
/*
	$Id: version_2_7_4.php,v 1.2 2007/04/11 07:49:50 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into categories_configuration table (for Game Add-On not in use notification)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Custom Product'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["GAME_ADDON_NOT_SELECTED_NOTIFICATION"] = array("insert" => " (0, 'Game Add-On not in use Notification Email Address', 'GAME_ADDON_NOT_SELECTED_NOTIFICATION', '', 'Email address to which the email will be send to when required game add-on is not in use.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 30, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into categories_configuration table (for Game Add-On not in use notification)

// Update records in status_configuration table (for PWL Order - Manual update Completed to On Hold)
$status_configuration_update_sql = array();

$status_configuration_update_sql['status_configuration'] = array(	array(	"field_name" => "status_configuration_manual_notification",
																			"update" => " status_configuration_manual_notification='' ",
																			"where_str" => " status_configuration_trans_type='PWL' AND status_configuration_source_status_id='4' AND status_configuration_destination_status_id='2'"
																		)
																 );

advance_update_records($status_configuration_update_sql, $DBTables);
// End of update records in status_configuration table (for PWL Order - Manual update Completed to On Hold)
?>