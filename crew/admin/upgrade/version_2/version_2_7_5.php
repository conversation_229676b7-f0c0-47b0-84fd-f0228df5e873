<?
/*
	$Id: version_2_7_5.php,v 1.2 2007/04/25 07:39:43 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on editing Product)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='categories.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CATALOG_ADD_CATEGORY"] = array("insert" => " ('CATALOG_ADD_CATEGORY', 'Add New Category', ".$row_sql["admin_files_id"].", '1,22,23,27,31,35,36,37,38,39,40,41', 15)" );
	$admin_files_actions_insert_sql["CATALOG_EDIT_OR_MOVE_CATEGORY"] = array("insert" => " ('CATALOG_EDIT_OR_MOVE_CATEGORY', 'Edit/Move Category', ".$row_sql["admin_files_id"].", '1,22,23,27,31,35,36,37,38,39,40,41', 20)" );
	$admin_files_actions_insert_sql["CATALOG_DELETE_CATEGORY"] = array("insert" => " ('CATALOG_DELETE_CATEGORY', 'Delete Category', ".$row_sql["admin_files_id"].", '1,22,23,27,31,35,36,37,38,39,40,41', 25)" );
	$admin_files_actions_insert_sql["CATALOG_ADD_PRODUCT"] = array("insert" => " ('CATALOG_ADD_PRODUCT', 'Add New Product', ".$row_sql["admin_files_id"].", '1,22,23,27,31,35,36,37,38,39,40,41', 30)" );
	$admin_files_actions_insert_sql["CATALOG_EDIT_OR_MOVE_OR_COPY_PRODUCT"] = array("insert" => " ('CATALOG_EDIT_OR_MOVE_OR_COPY_PRODUCT', 'Edit/Move/Copy Product', ".$row_sql["admin_files_id"].", '1,22,23,27,31,35,36,37,38,39,40,41', 35)" );
	$admin_files_actions_insert_sql["CATALOG_EDIT_PRODUCT_INFO"] = array("insert" => " ('CATALOG_EDIT_PRODUCT_INFO', 'Edit Product Information', ".$row_sql["admin_files_id"].", '1,22,23,27,31,35,36,37,38,39,40,41', 40)" );
	$admin_files_actions_insert_sql["CATALOG_EDIT_PRODUCT_LOCATION"] = array("insert" => " ('CATALOG_EDIT_PRODUCT_LOCATION', 'Edit Product Location', ".$row_sql["admin_files_id"].", '1,22,23,27,31,35,36,37,38,39,40,41', 45)" );
	$admin_files_actions_insert_sql["CATALOG_DELETE_PRODUCT"] = array("insert" => " ('CATALOG_DELETE_PRODUCT', 'Delete Product', ".$row_sql["admin_files_id"].", '1,22,23,27,31,35,36,37,38,39,40,41', 50)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on editing Product)

// Create new tables for store_account_comments
$add_new_tables = array();

$add_new_tables["store_account_comments"] = array (	"structure" => "CREATE TABLE `store_account_comments` (
																	  `store_account_comments_id` int(11) NOT NULL auto_increment,
																	  `store_account_history_id` int(11) NOT NULL default '0',
																	  `store_account_comments_date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `store_account_comments_notified` tinyint(4) NOT NULL default '0',
																	  `store_account_comments` text NOT NULL,
																	  `store_account_comments_added_by` varchar(255) NOT NULL default '',
																	  PRIMARY KEY  (`store_account_comments_id`),
																	  KEY `idx_store_account_history_id` (`store_account_history_id`)
																	)TYPE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create new tables for store_account_comments

// Insert new records into configuration table (for Account Statement Manual Action Notify Email)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Store Information'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["ACCOUNT_STAT_MANUAL_ACTION_NOTIFY_EMAIL"] = array("insert" => " ('Account Statement Manual Action Notify Email', 'ACCOUNT_STAT_MANUAL_ACTION_NOTIFY_EMAIL', '', 'Email address to which the email will be send to when there is manual action performed in account statement.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 16, NULL, now(), NULL, '')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Account Statement Manual Action Notify Email)

?>