<?
/*
	$Id: version_2_7_6.php,v 1.1 2007/05/02 08:07:50 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new fields into supplier, customers and store_account_history tables
$add_new_field = array();

$add_new_field['supplier'] = array (	array (	"field_name" => "supplier_msn",
												"field_attr" => " varchar(96) NOT NULL default'' ",
												"add_after" => "supplier_fax"
												),
										array (	"field_name" => "supplier_qq",
												"field_attr" => " varchar(20) NOT NULL default'' ",
												"add_after" => "supplier_msn"
												)
									);

$add_new_field['customers'] = array (	array (	"field_name" => "customers_msn",
												"field_attr" => " varchar(96) NOT NULL default '' ",
												"add_after" => "customers_fax"
												),
										array (	"field_name" => "customers_qq",
												"field_attr" => " varchar(20) NOT NULL default '' ",
												"add_after" => "customers_msn"
												)
									);

$add_new_field['store_account_history'] = array (	array (	"field_name" => "store_account_history_activity_desc_show",
															"field_attr" => " tinyint NOT NULL default '1' ",
															"add_after" => "store_account_history_activity_desc"
															)
												);

add_field($add_new_field);
// End of insert new fields into supplier, customers and store_account_history tables

?>