<?
/*
	$Id: version_2_7_7.php,v 1.1 2007/05/07 03:55:49 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define char_item_history_id and char_item_storage_slot as index key in char_item_storage table
add_index_key ('char_item_storage', 'index_history_id_and_storage_slot', 'index', 'char_item_history_id, char_item_storage_slot', $DBTables);

// End of define char_item_history_id and char_item_storage_slot as index key in char_item_storage table


// Define game_char_id and game_char_history_id as index key in char_item_history table
add_index_key ('char_item_history', 'index_char_id_and_history_id', 'index', 'game_char_id, game_char_history_id', $DBTables);

// End of define game_char_id and game_char_history_id as index key in char_item_history table
?>