<?
/*
	$Id: version_2_8.php,v 1.1 2007/05/10 10:17:10 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_custom_products_code_fields = get_table_fields("custom_products_code");
$existing_orders_status_history_fields = get_table_fields("orders_status_history");
$manual_issue_store_credit = in_array('store_credit_history', $DBTables) ? false : true;


// Insert new field into coupon_gv_customer, orders, supplier_tasks_allocation and custom_products_code tables
$add_new_field = array();

$add_new_field['coupon_gv_customer'] = array (	array (	"field_name" => "sc_reversible_amount",
							 							"field_attr" => " decimal(15,4) NOT NULL default '0.0000' ",
							 							"add_after" => ""
							 						),
							 					array (	"field_name" => "sc_reversible_reserve_amount",
							 							"field_attr" => " decimal(15,4) NOT NULL default '0.0000' ",
							 							"add_after" => "sc_reversible_amount"
							 						),
							 					array (	"field_name" => "sc_irreversible_amount",
							 							"field_attr" => " decimal(15,4) NOT NULL default '0.0000' ",
							 							"add_after" => "sc_reversible_reserve_amount"
							 						),
							 					array (	"field_name" => "sc_irreversible_reserve_amount",
							 							"field_attr" => " decimal(15,4) NOT NULL default '0.0000' ",
							 							"add_after" => "sc_irreversible_amount"
							 						),
							 					array (	"field_name" => "sc_last_modified",
							 							"field_attr" => " datetime default NULL ",
							 							"add_after" => "sc_irreversible_amount"
							 						)
											);

$add_new_field['orders'] = array (	array (	"field_name" => "orders_follow_up_datetime",
											"field_attr" => " datetime default NULL ",
											"add_after" => "orders_locked_datetime"
											)
								);

$add_new_field['orders_status_history'] = array (	array (	"field_name" => "comments_type",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => "comments"
															)
												);

$add_new_field['supplier_tasks_allocation'] = array (	array (	"field_name" => "supplier_tasks_follow_up_datetime",
																"field_attr" => " datetime default NULL ",
																"add_after" => "supplier_tasks_time_taken"
																)
													);

$add_new_field['custom_products_code'] = array (	array (	"field_name" => "custom_products_code_viewed",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => ""
															)
												);

add_field ($add_new_field);
// End of insert new field into coupon_gv_customer, orders, supplier_tasks_allocation and custom_products_code tables

// Change field structure for customer_id in coupon_gv_customer table
$change_field_structure = array();

$change_field_structure["coupon_gv_customer"] = array (array (	"field_name" => "customer_id",
									 							"field_attr" => " int(11) NOT NULL default '0' "
															)
													);

$change_field_structure["custom_products_code"] = array (array ("field_name" => "status_id",
									 							"field_attr" => " tinyint(1) NOT NULL default '1' "
																)
														);

change_field_structure ($change_field_structure);
// End of change field structure for customer_id in coupon_gv_customer table

// Create new tables
$add_new_tables = array();

$add_new_tables["store_credit_history"] = array (	"structure" => "CREATE TABLE `store_credit_history` (
																	  `store_credit_history_id` int(11) NOT NULL auto_increment,
																	  `customer_id` int(11) NOT NULL default '0',
																	  `store_credit_account_type` varchar(3) NOT NULL default '',
																	  `store_credit_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `store_credit_transaction_reserved` tinyint(1) NOT NULL default '0',
																	  `store_credit_history_debit_amount` decimal(15,4) default NULL,
																	  `store_credit_history_credit_amount` decimal(15,4) default NULL,
																	  `store_credit_history_r_after_balance` decimal(15,4) NOT NULL default '0.0000',
																	  `store_credit_history_nr_after_balance` decimal(15,4) NOT NULL default '0.0000',
																	  `store_credit_history_trans_type` varchar(10) NOT NULL default '',
																	  `store_credit_history_trans_id` varchar(255) NOT NULL default '',
																	  `store_credit_activity_type` varchar(2) NOT NULL default '',
																	  `store_credit_history_activity_title` varchar(128) NOT NULL default '',
																	  `store_credit_history_activity_desc` text NOT NULL,
																	  `store_credit_history_added_by` varchar(128) NOT NULL default '',
																	  `store_credit_history_added_by_role` varchar(16) NOT NULL default '',
																	  `store_credit_history_admin_messages` text NOT NULL,
																	  PRIMARY KEY(`store_credit_history_id`),
																	  KEY `idx_store_credit_history_date` (`store_credit_history_date`),
																	  KEY `idx_store_credit_user_sc_acc` (`customer_id`, `store_credit_account_type`),
																	  KEY `idx_store_credit_user_sc_activity` (`customer_id`, `store_credit_activity_type`)
																	) TYPE=MyISAM AUTO_INCREMENT=7000000;",
													"data" => ""
												);

$add_new_tables["custom_products_code_log"] = array (	"structure" => "CREATE TABLE `custom_products_code_log` (
																		  `custom_products_code_log_id` int(11) NOT NULL auto_increment,
																		  `custom_products_code_log_user` varchar(255) NOT NULL default '',
																		  `custom_products_code_log_user_role` varchar(16) NOT NULL default '',
																		  `log_ip` varchar(15) NOT NULL default '',
																		  `log_time` datetime default NULL,
																		  `custom_products_code_id` int(11) NOT NULL default '0',
																		  `log_system_messages` text NOT NULL,
																		  `log_user_messages` text NOT NULL,
																		  PRIMARY KEY  (`custom_products_code_log_id`),
																		  KEY `index_cp_code_log_time` (`log_time`),
																		  KEY `index_cp_code_id` (`custom_products_code_id`)
																		) TYPE=MyISAM ;",
														"data" => ""
													);

$add_new_tables["sales_activities"] = array (	"structure" => "CREATE TABLE `sales_activities` (
																  `sales_activities_id` int(11) NOT NULL auto_increment,
																  `sales_activities_date` datetime default NULL,
																  `sales_activities_orders_id` int(11) NOT NULL default '0',
																  `sales_activities_orders_products_id` int(11) NOT NULL default '0',
																  `sales_activities_products_id` int(11) NOT NULL default '0',
																  `sales_activities_code` varchar(5) NOT NULL default '',
																  `sales_activities_operator` char(1) NOT NULL default '',
																  `sales_activities_amount` decimal(15,4) NOT NULL default '0.0000',
																  `sales_activities_quantity` int(2) NOT NULL default '0',
																  `sales_activities_by_admin_id` varchar(255) NOT NULL default '',
																  PRIMARY KEY  (`sales_activities_id`),
																  KEY `index_sales_activities_date` (`sales_activities_date`),
																  KEY `index_sales_activities_products_id` (`sales_activities_products_id`)
																) TYPE=MyISAM ;",
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create new tables

// Delete records in admin_files table
$admin_actions_delete_sql["REVERSE_PROCESSING_2_VERIFYING"] = array( "unique" => "1" );
$admin_actions_delete_sql["REVERSIBLE_ORDER_STATUS"] = array( "unique" => "1" );

delete_records("admin_files_actions", " admin_files_actions_key", $admin_actions_delete_sql, $DBTables);
// End of delete records in admin_files table

// Insert new records into admin_files_actions table (for permission on reduce customer order delivered qty)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_REDUCE_DELIVERED_QTY"] = array("insert" => " ('ORDER_REDUCE_DELIVERED_QTY', 'Reduce delivered quantity', ".$row_sql["admin_files_id"].", '1', 6)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on reduce customer order delivered qty)


// Define buyback_request_group_id and buyback_status_id as index key in buyback_status_history table
add_index_key ('buyback_status_history', 'index_buyback_order_and_status', 'index', 'buyback_request_group_id, buyback_status_id', $DBTables);

// End of define buyback_request_group_id and buyback_status_id as index key in buyback_status_history table

// Define orders_products_id as index key in game_char table
add_index_key ('game_char', 'index_orders_products_id', 'index', 'orders_products_id', $DBTables);

// End of define orders_products_id as index key in game_char table

// Define game_char_id, game_char_history_date as index key in game_char_history table
add_index_key ('game_char_history', 'index_game_char_id_and_date', 'index', 'game_char_id, game_char_history_date', $DBTables);

// End of define game_char_id, game_char_history_date as index key in game_char_history table

if (!in_array('custom_products_code_viewed', $existing_custom_products_code_fields)) {
	// Update records in custom_products_code table (Set Disabled and Sold CD Key to viewed state)
	$setting_update_sql = array();
	
	$setting_update_sql['custom_products_code'] = array(	array(	"field_name" => "custom_products_code_viewed",
																	"update" => " custom_products_code_viewed='1' ",
																	"where_str" => " status_id IN ('-1', '0') "
																	)
														 );
	
	advance_update_records($setting_update_sql, $DBTables);
	// End of update records in custom_products_code table (Set Disabled and Sold CD Key to viewed state)
}

if (!in_array('comments_type', $existing_orders_status_history_fields)) {
	// Update records in orders_status_history table (Update existing order comment to corresponding type)
	$setting_update_sql = array();
	
	$setting_update_sql['orders_status_history'] = array(	array(	"field_name" => "comments_type",
																	"update" => " comments_type='2' ",
																	"where_str" => " comments REGEXP \"^(The following items have been delivered).*$\" "
																	),
															array(	"field_name" => "comments_type",
																	"update" => " comments_type='1' ",
																	"where_str" => " changed_by <> '' AND NOT (comments REGEXP \"^(The following items have been delivered).*$\") "
																	)
														 );
	
	advance_update_records($setting_update_sql, $DBTables);
	// End of update records in orders_status_history table (Update existing order comment to corresponding type)
}

if ($manual_issue_store_credit) {
	/*************************************************************
		Copy the voucher amount balance to Irreversible Account
		Resert the old voucher amount balance to zero
	*************************************************************/
	// Redeemed voucher but unused
	$voucher_balance_select_sql = "	SELECT * 
									FROM " . TABLE_COUPON_GV_CUSTOMER . " 
									WHERE amount > 0";
	$voucher_balance_result_sql = tep_db_query($voucher_balance_select_sql);
	
	while ($voucher_balance_row = tep_db_fetch_array($voucher_balance_result_sql)) {
		$non_reverse_balance_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . " 
											SET sc_irreversible_amount = sc_irreversible_amount + " . (double)$voucher_balance_row['amount'] . ",
												sc_last_modified = now(),
												amount = 0 
											WHERE customer_id = '" . tep_db_input($voucher_balance_row['customer_id']) . "'";
		tep_db_query($non_reverse_balance_update_sql);
		
		$new_non_reverse_balance_select_sql = "	SELECT sc_reversible_amount, sc_irreversible_amount 
												FROM " . TABLE_COUPON_GV_CUSTOMER . " 
												WHERE customer_id = '" . tep_db_input($voucher_balance_row['customer_id']) . "'";
		$new_non_reverse_balance_result_sql = tep_db_query($new_non_reverse_balance_select_sql);
		
		if ($new_non_reverse_balance_row = tep_db_fetch_array($new_non_reverse_balance_result_sql)) {
			// Insert account statement history
			$non_reverse_balance_history_data_array = array('customer_id' => $voucher_balance_row['customer_id'],
															'store_credit_account_type' => 'NR',
									                        'store_credit_history_date' => 'now()',
									                        'store_credit_history_debit_amount' => 'NULL',
								    	                    'store_credit_history_credit_amount' => (double)$voucher_balance_row['amount'],
								    	                    'store_credit_history_r_after_balance' => (double)$new_non_reverse_balance_row['sc_reversible_amount'],
															'store_credit_history_nr_after_balance' => (double)$new_non_reverse_balance_row['sc_irreversible_amount'],
								                    	    'store_credit_activity_type' => 'MI',
								                    	    'store_credit_history_activity_title' => 'Manual Addition',
															'store_credit_history_activity_desc' => 'Credited for redeemed voucher balance',
								                        	'store_credit_history_added_by' => 'system',
								                        	'store_credit_history_added_by_role' => 'admin'
								                       );
			tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $non_reverse_balance_history_data_array);
		}
	}
	
	
	// Unredeemed voucher
	$customer_unredeemed_balance = array();
	$customer_not_found_array = array();
	
	$unredeemed_voucher_select_sql = "	SELECT c.coupon_amount, c.coupon_id, et.emailed_to 
										FROM coupons AS c 
										INNER JOIN coupon_email_track AS et 
											ON c.coupon_id = et.coupon_id
										LEFT JOIN coupon_redeem_track AS crt 
											ON c.coupon_id = crt.coupon_id 
										WHERE crt.unique_id IS NULL 
											AND c.coupon_active = 'Y' 
										ORDER BY et.emailed_to";
	$unredeemed_voucher_result_sql = tep_db_query($unredeemed_voucher_select_sql);
	
	while ($unredeemed_voucher_row = tep_db_fetch_array($unredeemed_voucher_result_sql)) {
		if (isset($customer_unredeemed_balance[$unredeemed_voucher_row['emailed_to']]) && is_array($customer_unredeemed_balance[$unredeemed_voucher_row['emailed_to']])) {
			$customer_unredeemed_balance[$unredeemed_voucher_row['emailed_to']]['amount'] += (double)$unredeemed_voucher_row['coupon_amount'];
			$customer_unredeemed_balance[$unredeemed_voucher_row['emailed_to']]['coupon_id'][] = $unredeemed_voucher_row['coupon_id'];
		} else {	// New customer found
			$customer_unredeemed_balance[$unredeemed_voucher_row['emailed_to']] = array();
			
			$customer_unredeemed_balance[$unredeemed_voucher_row['emailed_to']]['amount'] = (double)$unredeemed_voucher_row['coupon_amount'];
			$customer_unredeemed_balance[$unredeemed_voucher_row['emailed_to']]['coupon_id'][] = $unredeemed_voucher_row['coupon_id'];
		}
	}
	
	if (count($customer_unredeemed_balance)) {
		foreach ($customer_unredeemed_balance as $cust_email => $voucher_info) {
			$customer_select_sql = "SELECT customers_id 
									FROM customers 
									WHERE customers_email_address = '" . tep_db_input($cust_email) . "'";
			$customer_result_sql = tep_db_query($customer_select_sql);
			
			if ($customer_row = tep_db_fetch_array($customer_result_sql)) {
				// Redeem on behalf of customer
				// Insert redeem track info
				for ($coupon_cnt=0; $coupon_cnt < count($voucher_info['coupon_id']); $coupon_cnt++) {
					$gv_query = tep_db_query("INSERT INTO " . TABLE_COUPON_REDEEM_TRACK . " (coupon_id, customer_id, redeem_date, redeem_ip) VALUES ('" . $voucher_info['coupon_id'][$coupon_cnt] . "', '" . $customer_row['customers_id'] . "', now(),'" . tep_db_input(getenv("REMOTE_ADDR")) . "')");
					$gv_update = tep_db_query("UPDATE " . TABLE_COUPONS . " SET coupon_active = 'N' where coupon_id = '" . tep_db_input($voucher_info['coupon_id'][$coupon_cnt]) . "'");
				}
				
				// Check if store credit account exists
				$sc_account_select_sql = "	SELECT customer_id 
											FROM " . TABLE_COUPON_GV_CUSTOMER . " 
											WHERE customer_id = '" . tep_db_input($customer_row['customers_id']) . "'";
				$sc_account_result_sql = tep_db_query($sc_account_select_sql);
				
			    if (tep_db_num_rows($sc_account_result_sql) > 0) {	// Store Credit account exists
					;
			    } else {
					$sc_balance_data_array = array(	'customer_id' => $customer_row['customers_id'],
													'sc_reversible_amount' => 0,
													'sc_irreversible_amount' => 0,
													'sc_last_modified' => 'now()'
												);
					tep_db_perform(TABLE_COUPON_GV_CUSTOMER, $sc_balance_data_array);
				}
				
				$amount_to_redeem = (double)$voucher_info['amount'];
				
				$non_reverse_balance_update_sql = "	UPDATE " . TABLE_COUPON_GV_CUSTOMER . " 
													SET sc_irreversible_amount = sc_irreversible_amount + " . (double)$amount_to_redeem . ",
														sc_last_modified = now(),
														amount = 0 
													WHERE customer_id = '" . tep_db_input($customer_row['customers_id']) . "'";
				tep_db_query($non_reverse_balance_update_sql);
				
				$new_non_reverse_balance_select_sql = "	SELECT sc_reversible_amount, sc_irreversible_amount 
														FROM " . TABLE_COUPON_GV_CUSTOMER . " 
														WHERE customer_id = '" . tep_db_input($customer_row['customers_id']) . "'";
				$new_non_reverse_balance_result_sql = tep_db_query($new_non_reverse_balance_select_sql);
				
				if ($new_non_reverse_balance_row = tep_db_fetch_array($new_non_reverse_balance_result_sql)) {
					// Insert account statement history
					$non_reverse_balance_history_data_array = array('customer_id' => $customer_row['customers_id'],
																	'store_credit_account_type' => 'NR',
											                        'store_credit_history_date' => 'now()',
											                        'store_credit_history_debit_amount' => 'NULL',
										    	                    'store_credit_history_credit_amount' => (double)$amount_to_redeem,
										    	                    'store_credit_history_r_after_balance' => (double)$new_non_reverse_balance_row['sc_reversible_amount'],
																	'store_credit_history_nr_after_balance' => (double)$new_non_reverse_balance_row['sc_irreversible_amount'],
										                    	    'store_credit_activity_type' => 'MI',
										                    	    'store_credit_history_activity_title' => 'Manual Addition',
																	'store_credit_history_activity_desc' => 'Credited for unredeemed voucher balance',
										                        	'store_credit_history_added_by' => 'system',
										                        	'store_credit_history_added_by_role' => 'admin'
										                       );
					tep_db_perform(TABLE_STORE_CREDIT_HISTORY, $non_reverse_balance_history_data_array);
				}
			} else {	// Customer email not exists
				$customer_not_found_array[] = $cust_email;
			}
		}
	}
	
	// Print out Customer not found list
	if (count($customer_not_found_array)) {
		echo "The following customer email cannot be found. Please redeem manually.<br><br>";
		echo implode("<br>", $customer_not_found_array);
	}
}
?>