<?
/*
	$Id: version_2_8_1.php,v 1.1 2007/05/10 10:17:10 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_orders_products_fields = get_table_fields("orders_products");

// Update existing value for ot_gv class in orders_total table
$ot_gv_order_select_sql = "	SELECT orders_total_id, text 
							FROM orders_total 
							WHERE class = 'ot_gv' 
								AND value <= 0";
$ot_gv_order_result_sql = tep_db_query($ot_gv_order_select_sql);

while ($ot_gv_order_row = tep_db_fetch_array($ot_gv_order_result_sql)) {
	preg_match('/(?:.*?)([0-9.]+)(?:.*?)/i', $ot_gv_order_row['text'], $regs);
	
	$ot_gv_order_update_sql = "	UPDATE orders_total
								SET value = '".tep_db_input($regs[1])."' 
								WHERE orders_total_id = '".tep_db_input($ot_gv_order_row['orders_total_id'])."'";
	tep_db_query($ot_gv_order_update_sql);
}
// End of update existing value for ot_gv class in orders_total table


// Insert new field into orders_products table
$add_new_field = array();

$add_new_field['orders_products'] = array (	array (	"field_name" => "orders_products_is_compensate",
							 						"field_attr" => " tinyint(1) NOT NULL default '0' ",
							 						"add_after" => "custom_products_type_id"
							 						),
							 				array (	"field_name" => "products_good_delivered_quantity",
													"field_attr" => " decimal(15,2) NOT NULL default '0' ",
													"add_after" => "products_delivered_quantity"
													),
											array (	"field_name" => "products_good_delivered_price",
													"field_attr" => " decimal(15,4) NOT NULL default '0.0000' ",
													"add_after" => "products_good_delivered_quantity"
													),
											array (	"field_name" => "products_canceled_quantity",
													"field_attr" => " decimal(15,2) NOT NULL default '0' ",
													"add_after" => "products_good_delivered_price"
													),
											array (	"field_name" => "products_canceled_price",
													"field_attr" => " decimal(15,4) NOT NULL default '0.0000' ",
													"add_after" => "products_canceled_quantity"
													),
											array (	"field_name" => "products_reversed_quantity",
													"field_attr" => " decimal(15,2) NOT NULL default '0' ",
													"add_after" => "products_canceled_price"
													),
											array (	"field_name" => "products_reversed_price",
													"field_attr" => " decimal(15,4) NOT NULL default '0.0000' ",
													"add_after" => "products_reversed_quantity"
													)
										);

add_field ($add_new_field);
// End of insert new field into orders_products table

// Change field structure for products_delivered_quantity in orders_products table
$change_field_structure = array();

$change_field_structure["orders_products"] = array (array (	"field_name" => "products_delivered_quantity",
									 						"field_attr" => " decimal(15,2) NOT NULL default '0' "
															)
													);

change_field_structure ($change_field_structure);
// End of change field structure for products_delivered_quantity in orders_products table

// Insert new records into admin_files table (for pwl customising page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='sales.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["order_cp_info.php"] = array(	"insert" => " ('order_cp_info.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   									);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='order_cp_info.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for pwl customising page)

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='payments.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for Store Credit Statement)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["store_credit.php"] = array("insert" => " ('store_credit.php', 0, '".$row_sql['admin_files_id']."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql['admin_files_id']."', admin_groups_id='1' "
					   									);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='store_credit.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["refund.php"] = array(	"insert" => " ('refund.php', 0, '".$row_sql['admin_files_id']."', '1') ",
													"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql['admin_files_id']."', admin_groups_id='1' "
					   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='refund.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for Store Credit Statement)
}

// Insert new records into admin_files_actions table (for permission on manual deduct/add on Store Credit Statement page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='store_credit.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["STORE_CREDIT_MANUAL_DEDUCT"] = array("insert" => " ('STORE_CREDIT_MANUAL_DEDUCT', 'Manual Deduction', ".$row_sql["admin_files_id"].", '1', 10)" );
	$admin_files_actions_insert_sql["STORE_CREDIT_MANUAL_ADD"] = array("insert" => " ('STORE_CREDIT_MANUAL_ADD', 'Manual Addition', ".$row_sql["admin_files_id"].", '1', 20)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on manual deduct/add on Store Credit Statement page)

// Create new tables
$add_new_tables = array();

$add_new_tables["orders_compensate_products"] = array (	"structure" => "CREATE TABLE `orders_compensate_products` (
																		  `orders_products_id` int(11) NOT NULL default '0',
																		  `orders_id` int(11) NOT NULL default '0',
																		  `compensate_for_orders_products_id` int(11) NOT NULL default '0',
																		  `compensate_accident_amount` decimal(15,4) NOT NULL default '0.0000',
																		  `compensate_non_accident_amount` decimal(15,4) NOT NULL default '0.0000',
																		  `compensate_supplier_amount` decimal(15,4) NOT NULL default '0.0000',
																		  `compensate_by_supplier_id` int(11) NOT NULL default '0',
																		  `compensate_by_supplier_firstname` varchar(32) NOT NULL default '',
																		  `compensate_by_supplier_lastname` varchar(32) NOT NULL default '',
																		  `compensate_by_supplier_code` varchar(64) default NULL,
																		  `compensate_by_supplier_email_address` varchar(96) NOT NULL default '',
																		  `orders_compensate_products_added_by` varchar(128) NOT NULL default '',
																		  `orders_compensate_products_messages` text NOT NULL,
																		  PRIMARY KEY(`orders_products_id`),
																		  KEY `idx_orders_id` (`orders_id`)
																		) TYPE=MyISAM",
														"data" => ""
													);

$add_new_tables["store_refund"] = array (	"structure" => "CREATE TABLE `store_refund` (
															  `store_refund_id` int(11) NOT NULL auto_increment,
															  `user_id` int(11) NOT NULL default '0',
															  `user_firstname` varchar(32) NOT NULL default '',
															  `user_lastname` varchar(32) NOT NULL default '',
															  `user_email_address` varchar(96) NOT NULL default '',
															  `store_refund_date` datetime NOT NULL default '0000-00-00 00:00:00',
															  `store_refund_trans_id` varchar(255) NOT NULL default '',
															  `store_refund_status` smallint(1) NOT NULL default '0',
															  `store_refund_trans_total_amount` decimal(15,4) NOT NULL default '0.0000',
															  `store_refund_amount` decimal(15,4) NOT NULL default '0.0000',
															  `store_refund_payments_reference` varchar(32) NULL,
															  `store_refund_payments_methods_name` varchar(20) NULL,
															  `store_refund_last_modified` datetime default NULL,
															  PRIMARY KEY(`store_refund_id`),
															  KEY `idx_store_refund_date` (`store_refund_date`),
															  KEY `idx_store_refund_owner` (`user_id`)
															) TYPE=MyISAM AUTO_INCREMENT=8000000;",
											"data" => ""
										);

$add_new_tables["store_refund_history"] = array (	"structure" => "CREATE TABLE `store_refund_history` (
																	  `store_refund_history_id` int(11) NOT NULL auto_increment,
																	  `store_refund_id` int(11) NOT NULL default '0',
																	  `store_refund_status` smallint(1) NOT NULL default '0',
																	  `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `payee_notified` tinyint(1) NOT NULL default '0',
																	  `comments` text,
																	  `changed_by` varchar(128) NOT NULL default '',
																	  `changed_by_role` varchar(16) NOT NULL default '',
																	  PRIMARY KEY  (`store_refund_history_id`),
																	  KEY `index_refund_id_and_refund_status` (`store_refund_id`,`store_refund_status`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create new tables

// Change field structure for compensate_for_orders_products_id in orders_compensate_products table
$change_field_structure = array();

$change_field_structure["orders_compensate_products"] = array (array (	"field_name" => "compensate_for_orders_products_id",
												 						"field_attr" => " varchar(255) NOT NULL default '' "
																		)
																);

change_field_structure ($change_field_structure);
// End of change field structure for compensate_for_orders_products_id in orders_compensate_products table

if (!in_array('products_good_delivered_quantity', $existing_orders_products_fields)) {
	// For Processing and Completed orders
	$completed_order_delivered_qty_update_sql = "	UPDATE orders_products AS op 
													INNER JOIN orders AS o 
														ON op.orders_id=o.orders_id 
													SET op.products_good_delivered_quantity=op.products_delivered_quantity 
													WHERE o.orders_status IN (2,3)";
	tep_db_query($completed_order_delivered_qty_update_sql);
	
	$completed_order_select_sql = "SELECT orders_id FROM orders WHERE orders_status IN (2,3)";
	$completed_order_result_sql = tep_db_query($completed_order_select_sql);
  	while ($completed_order_row = tep_db_fetch_array($completed_order_result_sql)) {
  		$order_products_select_sql = "	SELECT orders_products_id, products_id 
  										FROM orders_products 
  										WHERE orders_id = '" . tep_db_input($completed_order_row['orders_id']) . "' 
  											AND products_bundle_id < 1";
  		$order_products_result_sql = tep_db_query($order_products_select_sql);
  		
  		while ($order_products_row = tep_db_fetch_array($order_products_result_sql)) {
  			$product_type_select_sql = "SELECT products_bundle, products_bundle_dynamic 
  										FROM products 
  										WHERE products_id = '" . $order_products_row["products_id"] . "'";
  			$product_type_result_sql = tep_db_query($product_type_select_sql);
			$product_type_row = tep_db_fetch_array($product_type_result_sql);
			
			if ($product_type_row['products_bundle_dynamic'] == 'yes') {
				upgrade_update_delivered_price($completed_order_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle_dynamic');
			} else if ($product_type_row['products_bundle'] == 'yes') {
				upgrade_update_delivered_price($completed_order_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle');
			} else {
				upgrade_update_delivered_price($completed_order_row['orders_id'], $order_products_row["orders_products_id"], '');
			}
  		}
  	}
  	
  	// For Reversed orders
	$reversed_order_delivered_qty_update_sql = "	UPDATE orders_products AS op 
													INNER JOIN orders AS o 
														ON op.orders_id=o.orders_id 
													SET op.products_reversed_quantity =op.products_delivered_quantity 
													WHERE o.orders_status = 4";
	tep_db_query($reversed_order_delivered_qty_update_sql);
	
	$reversed_order_select_sql = "SELECT orders_id FROM orders WHERE orders_status = 4";
	$reversed_order_result_sql = tep_db_query($reversed_order_select_sql);
  	while ($reversed_order_row = tep_db_fetch_array($reversed_order_result_sql)) {
  		$order_products_select_sql = "	SELECT orders_products_id, products_id 
  										FROM orders_products 
  										WHERE orders_id = '" . tep_db_input($reversed_order_row['orders_id']) . "' 
  											AND products_bundle_id < 1";
  		$order_products_result_sql = tep_db_query($order_products_select_sql);
  		
  		while ($order_products_row = tep_db_fetch_array($order_products_result_sql)) {
  			$product_type_select_sql = "SELECT products_bundle, products_bundle_dynamic 
  										FROM products 
  										WHERE products_id = '" . $order_products_row["products_id"] . "'";
  			$product_type_result_sql = tep_db_query($product_type_select_sql);
			$product_type_row = tep_db_fetch_array($product_type_result_sql);
			
			if ($product_type_row['products_bundle_dynamic'] == 'yes') {
				upgrade_update_reversed_price($reversed_order_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle_dynamic');
			} else if ($product_type_row['products_bundle'] == 'yes') {
				upgrade_update_reversed_price($reversed_order_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle');
			} else {
				upgrade_update_reversed_price($reversed_order_row['orders_id'], $order_products_row["orders_products_id"], '');
			}
  		}
  		
  		tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, set_as_order_remarks, changed_by) VALUES ('".$reversed_order_row['orders_id']."', 3, now(), '0', 'New Order Flow', 1, 0, '".$login_email_address."')");
  	}
  	
  	$reversed_to_completed_order_update_sql = "	UPDATE orders SET orders_status = 3 
  												WHERE orders_status = 4";
  	tep_db_query($reversed_to_completed_order_update_sql);
  	
  	// For Refunded orders
	$refunded_order_delivered_qty_update_sql = "	UPDATE orders_products AS op 
													INNER JOIN orders AS o 
														ON op.orders_id=o.orders_id 
													SET op.products_good_delivered_quantity = op.products_delivered_quantity,
														op.products_canceled_quantity = op.products_quantity - op.products_delivered_quantity 
													WHERE o.orders_status = 6";
	tep_db_query($refunded_order_delivered_qty_update_sql);
	
	$refunded_order_select_sql = "SELECT orders_id FROM orders WHERE orders_status = 6";
	$refunded_order_result_sql = tep_db_query($refunded_order_select_sql);
  	while ($refunded_order_row = tep_db_fetch_array($refunded_order_result_sql)) {
  		$order_products_select_sql = "	SELECT orders_products_id, products_id 
  										FROM orders_products 
  										WHERE orders_id = '" . tep_db_input($refunded_order_row['orders_id']) . "' 
  											AND products_bundle_id < 1";
  		$order_products_result_sql = tep_db_query($order_products_select_sql);
  		
  		while ($order_products_row = tep_db_fetch_array($order_products_result_sql)) {
  			$product_type_select_sql = "SELECT products_bundle, products_bundle_dynamic 
  										FROM products 
  										WHERE products_id = '" . $order_products_row["products_id"] . "'";
  			$product_type_result_sql = tep_db_query($product_type_select_sql);
			$product_type_row = tep_db_fetch_array($product_type_result_sql);
			
			if ($product_type_row['products_bundle_dynamic'] == 'yes') {
				upgrade_update_refunded_price($refunded_order_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle_dynamic');
			} else if ($product_type_row['products_bundle'] == 'yes') {
				upgrade_update_refunded_price($refunded_order_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle');
			} else {
				upgrade_update_refunded_price($refunded_order_row['orders_id'], $order_products_row["orders_products_id"], '');
			}
  		}
  		
  		tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, set_as_order_remarks, changed_by) VALUES ('".$refunded_order_row['orders_id']."', 3, now(), '0', 'New Order Flow', 1, 0, '".$login_email_address."')");
  	}
  	
  	$refund_to_completed_order_update_sql = "	UPDATE orders SET orders_status = 3 
  												WHERE orders_status = 6";
  	tep_db_query($refund_to_completed_order_update_sql);
}

function upgrade_update_delivered_price($order_id, $orders_product_id, $product_type='') {
	switch ($product_type) {
		case 'products_bundle_dynamic':
		case 'products_bundle':
			$total_subproduct = 0;
			$good_delivered_ratio = 0;
			
			$purchased_final_price_select_sql = "	SELECT products_id, final_price * products_quantity AS total_amount
													FROM " . TABLE_ORDERS_PRODUCTS . " 
													WHERE orders_id = '" . tep_db_input($order_id) . "' 
														AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			$purchased_final_price_result_sql = tep_db_query($purchased_final_price_select_sql);
			
			if ($purchased_final_price_row = tep_db_fetch_array($purchased_final_price_result_sql)) {
    			$subproduct_select_sql = "	SELECT products_quantity, products_good_delivered_quantity 
											FROM " . TABLE_ORDERS_PRODUCTS . " 
											WHERE orders_id = '" . tep_db_input($order_id) . "' 
												AND products_bundle_id = '" . tep_db_input($purchased_final_price_row['products_id']) . "'";
				$subproduct_result_sql = tep_db_query($subproduct_select_sql);
				
				while ($subproduct_row = tep_db_fetch_array($subproduct_result_sql)) {
					if ($subproduct_row['products_quantity'] > 0) {
						$good_delivered_ratio += (double)$subproduct_row['products_good_delivered_quantity'] / $subproduct_row['products_quantity'];
						
						$total_subproduct++;
					}
				}
				
				if ($total_subproduct) {
					$latest_delivered_price = (double)($purchased_final_price_row['total_amount'] * $good_delivered_ratio) / $total_subproduct;
					
					// Update the latest delivered price
					$delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
													SET products_good_delivered_price = '" . tep_db_input($latest_delivered_price) . "'
	    											WHERE orders_id = '" . tep_db_input($order_id) . "' 
	    												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
	    			tep_db_query($delivered_price_update_sql);
				}
			}
			
			break;
			
		default:
			// Update the latest delivered price
			$delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
											SET products_good_delivered_price = products_good_delivered_quantity * final_price 
											WHERE orders_id = '" . tep_db_input($order_id) . "' 
												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			tep_db_query($delivered_price_update_sql);
			
			break;
	}
}

function upgrade_update_reversed_price($order_id, $orders_product_id, $product_type='') {
	switch ($product_type) {
		case 'products_bundle_dynamic':
		case 'products_bundle':
			$total_subproduct = 0;
			$delivered_ratio = 0;
			
			$purchased_final_price_select_sql = "	SELECT products_id, final_price * products_quantity AS total_amount
													FROM " . TABLE_ORDERS_PRODUCTS . " 
													WHERE orders_id = '" . tep_db_input($order_id) . "' 
														AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			$purchased_final_price_result_sql = tep_db_query($purchased_final_price_select_sql);
			
			if ($purchased_final_price_row = tep_db_fetch_array($purchased_final_price_result_sql)) {
    			$subproduct_select_sql = "	SELECT products_quantity, products_reversed_quantity 
											FROM " . TABLE_ORDERS_PRODUCTS . " 
											WHERE orders_id = '" . tep_db_input($order_id) . "' 
												AND products_bundle_id = '" . tep_db_input($purchased_final_price_row['products_id']) . "'";
				$subproduct_result_sql = tep_db_query($subproduct_select_sql);
				
				while ($subproduct_row = tep_db_fetch_array($subproduct_result_sql)) {
					if ($subproduct_row['products_quantity'] > 0) {
						$delivered_ratio += (double)$subproduct_row['products_reversed_quantity'] / $subproduct_row['products_quantity'];
						
						$total_subproduct++;
					}
				}
				
				if ($total_subproduct) {
					$latest_price = (double)($purchased_final_price_row['total_amount'] * $delivered_ratio) / $total_subproduct;
					
					// Update the latest reversed price
					$price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
											SET products_reversed_price = '" . tep_db_input($latest_price) . "'
	    									WHERE orders_id = '" . tep_db_input($order_id) . "' 
	    										AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
	    			tep_db_query($price_update_sql);
				}
			}
			
			break;
			
		default:
			// Update the latest reversed price
			$price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
									SET products_reversed_price = products_reversed_quantity * final_price 
									WHERE orders_id = '" . tep_db_input($order_id) . "' 
										AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			tep_db_query($price_update_sql);
			
			break;
	}
}

function upgrade_update_refunded_price($order_id, $orders_product_id, $product_type='') {
	switch ($product_type) {
		case 'products_bundle_dynamic':
		case 'products_bundle':
			$total_subproduct = 0;
			$delivered_ratio = 0;
			
			$purchased_final_price_select_sql = "	SELECT products_id, final_price * products_quantity AS total_amount
													FROM " . TABLE_ORDERS_PRODUCTS . " 
													WHERE orders_id = '" . tep_db_input($order_id) . "' 
														AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			$purchased_final_price_result_sql = tep_db_query($purchased_final_price_select_sql);
			
			if ($purchased_final_price_row = tep_db_fetch_array($purchased_final_price_result_sql)) {
    			$subproduct_select_sql = "	SELECT products_quantity, products_canceled_quantity 
											FROM " . TABLE_ORDERS_PRODUCTS . " 
											WHERE orders_id = '" . tep_db_input($order_id) . "' 
												AND products_bundle_id = '" . tep_db_input($purchased_final_price_row['products_id']) . "'";
				$subproduct_result_sql = tep_db_query($subproduct_select_sql);
				
				while ($subproduct_row = tep_db_fetch_array($subproduct_result_sql)) {
					if ($subproduct_row['products_quantity'] > 0) {
						$delivered_ratio += (double)$subproduct_row['products_canceled_quantity'] / $subproduct_row['products_quantity'];
						
						$total_subproduct++;
					}
				}
				
				if ($total_subproduct) {
					$latest_price = (double)($purchased_final_price_row['total_amount'] * $delivered_ratio) / $total_subproduct;
					
					// Update the latest delivered price
					$price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
											SET products_canceled_price = '" . tep_db_input($latest_price) . "'
	    									WHERE orders_id = '" . tep_db_input($order_id) . "' 
	    										AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
	    			tep_db_query($price_update_sql);
				}
			}
			
			break;
			
		default:
			// Update the latest delivered price
			$price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
									SET products_canceled_price = products_canceled_quantity * final_price 
									WHERE orders_id = '" . tep_db_input($order_id) . "' 
										AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			tep_db_query($price_update_sql);
			
			break;
	}
}
?>