<?
/*
	$Id: version_2_8_10.php,v 1.1 2007/06/07 07:12:33 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into status_configuration table (for Buyback Order Status Permission)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array("insert" => " ('B', '4', '3', '1', '', '-1')" );

insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="B" AND status_configuration_source_status_id="4" AND status_configuration_destination_status_id="3" ');
// End of insert new records into status_configuration table (for Buyback Order Status Permission)
?>