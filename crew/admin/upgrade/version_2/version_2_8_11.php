<?
/*
	$Id: version_2_8_11.php,v 1.1 2007/06/13 09:44:09 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Delete records in buyback_setting table (ofp_deal_on_game and ofp_deal_on_mail keys)
$buyback_setting_delete_sql = array();

$buyback_setting_delete_sql["ofp_deal_on_game"] = array();
$buyback_setting_delete_sql["ofp_deal_on_mail"] = array();

delete_records("buyback_setting", " buyback_setting_key", $buyback_setting_delete_sql, $DBTables);
// End of delete records in buyback_setting table (ofp_deal_on_game and ofp_deal_on_mail keys)

// Update records in buyback_status table (rename Pending's chinese name)
$buyback_status_update_sql = array();
	
$buyback_status_update_sql['buyback_status'] = array(	array(	"field_name" => "buyback_status_name",
																"update" => " buyback_status_name='&#23457;&#26680;' ",
																"where_str" => " (buyback_status_id = '1' AND language_id = '2') "
																)
													 );

advance_update_records($buyback_status_update_sql, $DBTables);
// End of update records in buyback_status table (rename Pending's chinese name)

?>