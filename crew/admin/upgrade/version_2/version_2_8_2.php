<?
/*
	$Id: version_2_8_2.php,v 1.1 2007/05/10 10:17:10 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new field into user_flags and orders table
$add_new_field = array();

$add_new_field['user_flags'] = array (	array (	"field_name" => "user_flags_on_notification",
						 						"field_attr" => " varchar(255) NOT NULL default '' ",
						 						"add_after" => ""
						 						),
						 				array (	"field_name" => "user_flags_off_notification",
												"field_attr" => " varchar(255) NOT NULL default '' ",
												"add_after" => "user_flags_on_notification"
												)
									);

$add_new_field['orders'] = array (	array (	"field_name" => "orders_cb_status",
											"field_attr" => " tinyint(1) default NULL ",
											"add_after" => "orders_status"
											)
								);

add_field ($add_new_field);
// End of insert new field into user_flags and orders table

// Insert new records into orders_status table (for On Hold status)
$order_status_insert_sql[8] = array("insert" => " (8, 1, 'On Hold', 80) ",
									"update" => " language_id=1, orders_status_name='On Hold' "
				   					);

insert_new_records(TABLE_ORDERS_STATUS, "orders_status_id", $order_status_insert_sql, $DBTables, "(orders_status_id, language_id, orders_status_name, orders_status_sort_order)");
// End of insert new records into orders_status table (for On Hold status)

// Insert new records into status_configuration table (for Customer Order On Hold new status)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["C"] = array("insert" => " ('C', '2', '8', '1', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="C" AND status_configuration_source_status_id="2" AND status_configuration_destination_status_id="8" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["C"] = array("insert" => " ('C', '3', '8', '1', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="C" AND status_configuration_source_status_id="3" AND status_configuration_destination_status_id="8" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["C"] = array("insert" => " ('C', '8', '2', '1', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="C" AND status_configuration_source_status_id="8" AND status_configuration_destination_status_id="2" ');

$status_configuration_insert_sql = array();
$status_configuration_insert_sql["C"] = array("insert" => " ('C', '8', '3', '1', '', '-1')" );
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", ' status_configuration_trans_type="C" AND status_configuration_source_status_id="8" AND status_configuration_destination_status_id="3" ');

// End of insert new records into status_configuration table (for Customer Order On Hold new status)

// Delete Reversed and Refunded status from orders_status table
$orders_status_delete_sql = array();
$orders_status_delete_sql["4"] = array( "unique" => "1" );
$orders_status_delete_sql["6"] = array( "unique" => "1" );

delete_records("orders_status", "orders_status_id", $orders_status_delete_sql, $DBTables);
// End of delete Reversed and Refunded status from orders_status table

// Delete Reversed and Refunded status from status_configuration table
$status_configuration_delete_sql = array();
$status_configuration_delete_sql["4"] = array( "extra_where" => "status_configuration_trans_type='C'" );
$status_configuration_delete_sql["6"] = array( "extra_where" => "status_configuration_trans_type='C'" );
delete_records("status_configuration", "status_configuration_source_status_id", $status_configuration_delete_sql, $DBTables);

$status_configuration_delete_sql = array();
$status_configuration_delete_sql["4"] = array( "extra_where" => "status_configuration_trans_type='C'" );
$status_configuration_delete_sql["6"] = array( "extra_where" => "status_configuration_trans_type='C'" );
delete_records("status_configuration", "status_configuration_destination_status_id", $status_configuration_delete_sql, $DBTables);

// End of delete Reversed and Refunded status from status_configuration table

// Change field structure for admin_files_actions_name in admin_files_actions table
$change_field_structure = array();

$change_field_structure["admin_files_actions"] = array (array (	"field_name" => "admin_files_actions_name",
									 							"field_attr" => " varchar(255) NOT NULL default '' "
															)
													);

change_field_structure ($change_field_structure);
// End of change field structure for admin_files_actions_name in admin_files_actions table

// Update records in admin_files_actions table (rename permission to view cd key)
$admin_file_actions_update_sql = array();
$admin_file_actions_update_sql["CP_VIEW_CDKEY_IMAGES"] = array("update" => " admin_files_actions_name='Full access on viewing CD Key images (Overwrite Sales->View viewed CD Key permission)' ");
$admin_file_actions_update_sql["ORDER_CDKEY_DETAILS"] = array("update" => " admin_files_actions_name='View viewed CD Key image in chargeback order' ");

update_records('admin_files_actions', 'admin_files_actions_key', $admin_file_actions_update_sql, $DBTables);
// End of update records in admin_files_actions table (rename permission to view cd key)

// Insert new records into admin_files_actions table (for permission on refund order)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_REFUND_PAYMENT"] = array("insert" => " ('ORDER_REFUND_PAYMENT', 'Refund Order', ".$row_sql["admin_files_id"].", '1', 40)" );
	$admin_files_actions_insert_sql["ORDER_ADD_COMPENSATION_ITEM"] = array("insert" => " ('ORDER_ADD_COMPENSATION_ITEM', 'Add item for Compensation/Loss', ".$row_sql["admin_files_id"].", '1', 6)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on refund order)

// Insert new records into admin_files_actions table (for permission on check unflagged option)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_CHECK_FLAG"] = array("insert" => " ('CUSTOMER_CHECK_FLAG', 'Check customer flag', ".$row_sql["admin_files_id"].", '1', 6)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on check unflagged option)
?>