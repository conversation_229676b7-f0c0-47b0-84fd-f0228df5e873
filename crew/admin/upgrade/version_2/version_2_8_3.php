<?
/*
	$Id: version_2_8_3.php,v 1.2 2007/05/15 08:46:06 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Define store_credit_history_trans_type, store_credit_history_trans_id as index key in store_credit_history table
add_index_key ('store_credit_history', 'index_trans_type_and_trans_id', 'index', 'store_credit_history_trans_type, store_credit_history_trans_id', $DBTables);

// End of define store_credit_history_trans_type, store_credit_history_trans_id as index key in store_credit_history table

// Change field structure for bundle_id and subproduct_id in products_bundles table
$change_field_structure = array();

$change_field_structure["products_bundles"] = array (	array (	"field_name" => "bundle_id",
									 							"field_attr" => " int(11) NOT NULL default '0' "
																),
														array (	"field_name" => "subproduct_id",
									 							"field_attr" => " int(11) NOT NULL default '0' "
																)
													);

change_field_structure ($change_field_structure);
// End of change field structure for bundle_id and subproduct_id in products_bundles table
?>