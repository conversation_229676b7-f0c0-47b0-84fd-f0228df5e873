<?
/*
	$Id: version_2_8_4.php,v 1.1 2007/05/15 08:48:34 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on rollback refunded quantity)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_ROLLBACK_REFUND_PAYMENT"] = array("insert" => " ('ORDER_ROLLBACK_REFUND_PAYMENT', 'Rollback Refunded Quantity', ".$row_sql["admin_files_id"].", '1', 45)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on rollback refunded quantity)


// Set old 'reversed' order to Reversed - LOST
$old_reversed_order_select_sql = "	SELECT DISTINCT(o.orders_id), SUM(op.products_reversed_price) AS total_reverse 
									FROM orders AS o 
									INNER JOIN orders_products AS op 
										ON o.orders_id = op.orders_id 
									WHERE op.products_bundle_id=0 
  	                    				AND op.orders_products_is_compensate=0 
  	                    				AND o.orders_status = 3 
  	                    				AND (o.orders_cb_status IS NULL OR o.orders_cb_status = '') 
  	                    			GROUP BY op.orders_id 
  	                    			HAVING total_reverse > 0";
$old_reversed_order_result_sql = tep_db_query($old_reversed_order_select_sql);
while ($old_reversed_order_row = tep_db_fetch_array($old_reversed_order_result_sql)) {
	$cb_status_update_sql = "	UPDATE orders
								SET orders_cb_status=2,
									last_modified=now() 
								WHERE orders_id='".tep_db_input($old_reversed_order_row['orders_id'])."'";
	tep_db_query($cb_status_update_sql);
	
	tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, set_as_order_remarks, changed_by) VALUES ('".$old_reversed_order_row['orders_id']."', 0, now(), '0', 'Order updated as Reversed - LOST', 0, 0, 'system')");
}
// End of set old 'reversed' order to Reversed - LOST
?>