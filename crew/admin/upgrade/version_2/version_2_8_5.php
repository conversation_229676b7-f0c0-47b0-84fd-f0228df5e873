<?
/*
	$Id: version_2_8_5.php,v 1.1 2007/05/22 09:29:16 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Update Delivered Quantity
$mising_delivered_qty_select_sql = "SELECT op.orders_id, SUM(op.products_delivered_quantity) AS delivered_qty 
									FROM orders_products AS op 
									INNER JOIN orders AS o 
										ON op.orders_id=o.orders_id
									WHERE o.date_purchased < '2007-05-10 10:00:00'
										AND o.orders_status = 3
									GROUP BY op.orders_id 
									HAVING delivered_qty = 0";
$mising_delivered_qty_result_sql = tep_db_query($mising_delivered_qty_select_sql);

while ($mising_delivered_qty_row = tep_db_fetch_array($mising_delivered_qty_result_sql)) {
	$delivered_qty_update_sql = "	UPDATE orders_products
									SET products_delivered_quantity = products_quantity 
									WHERE orders_id='".tep_db_input($mising_delivered_qty_row['orders_id'])."'";
	tep_db_query($delivered_qty_update_sql);
	
	tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, set_as_order_remarks, changed_by) VALUES ('".$mising_delivered_qty_row['orders_id']."', 0, now(), '0', 'Re-update total delivered quantity', 0, 0, 'system')");
}
// End of update Delivered Quantity
?>