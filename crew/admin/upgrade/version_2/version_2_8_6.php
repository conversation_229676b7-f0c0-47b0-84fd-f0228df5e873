<?
/*
	$Id: version_2_8_6.php,v 1.1 2007/05/22 09:29:16 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Update Good Delivered / Reversed Quantity
$orders_stat_select_sql = "	SELECT op.orders_id, SUM(op.products_good_delivered_quantity + products_canceled_quantity + products_reversed_quantity) AS delivered_qty 
							FROM orders_products AS op 
							INNER JOIN orders AS o 
								ON op.orders_id=o.orders_id
							WHERE o.date_purchased < '2006-03-25'
								AND o.orders_status = 3
							GROUP BY op.orders_id 
							HAVING delivered_qty = 0 ";
$orders_stat_result_sql = tep_db_query($orders_stat_select_sql);

while ($orders_stat_row = tep_db_fetch_array($orders_stat_result_sql)) {
	$cb_order_select_sql = "	SELECT orders_status_history_id 
								FROM orders_status_history 
								WHERE orders_id = '".$orders_stat_row['orders_id']."' 
									AND comments = 'New Order Flow'";
	$cb_order_result_sql = tep_db_query($cb_order_select_sql);
	
	if ($cb_order_row = tep_db_fetch_array($cb_order_result_sql)) {
		//	Is CB order
		$reversed_order_delivered_qty_update_sql = "	UPDATE orders_products AS op
														SET op.products_reversed_quantity =op.products_delivered_quantity 
														WHERE op.orders_id = '" . $orders_stat_row['orders_id'] . "'";
		tep_db_query($reversed_order_delivered_qty_update_sql);
		
  		$order_products_select_sql = "	SELECT orders_products_id, products_id 
  										FROM orders_products 
  										WHERE orders_id = '" . tep_db_input($orders_stat_row['orders_id']) . "' 
  											AND products_bundle_id < 1";
  		$order_products_result_sql = tep_db_query($order_products_select_sql);
  		
  		while ($order_products_row = tep_db_fetch_array($order_products_result_sql)) {
  			$product_type_select_sql = "SELECT products_bundle, products_bundle_dynamic 
  										FROM products 
  										WHERE products_id = '" . $order_products_row["products_id"] . "'";
  			$product_type_result_sql = tep_db_query($product_type_select_sql);
			$product_type_row = tep_db_fetch_array($product_type_result_sql);
			
			if ($product_type_row['products_bundle_dynamic'] == 'yes') {
				upgrade_update_reversed_price($orders_stat_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle_dynamic');
			} else if ($product_type_row['products_bundle'] == 'yes') {
				upgrade_update_reversed_price($orders_stat_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle');
			} else {
				upgrade_update_reversed_price($orders_stat_row['orders_id'], $order_products_row["orders_products_id"], '');
			}
  		}
  		
  		//	Update CB Lost
  		$cb_status_update_sql = "	UPDATE orders
									SET orders_cb_status=2,
										last_modified=now() 
									WHERE orders_id='".tep_db_input($orders_stat_row['orders_id'])."'";
		tep_db_query($cb_status_update_sql);
		
		tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, set_as_order_remarks, changed_by) VALUES ('".$orders_stat_row['orders_id']."', 0, now(), '0', 'Order updated as Reversed - LOST', 0, 0, 'system')");
	} else {
		// Is Completed order
		$completed_order_delivered_qty_update_sql = "	UPDATE orders_products AS op 
														SET op.products_good_delivered_quantity=op.products_delivered_quantity 
														WHERE op.orders_id = '" . $orders_stat_row['orders_id'] . "'";
		tep_db_query($completed_order_delivered_qty_update_sql);
		
		$order_products_select_sql = "	SELECT orders_products_id, products_id 
  										FROM orders_products 
  										WHERE orders_id = '" . tep_db_input($orders_stat_row['orders_id']) . "' 
  											AND products_bundle_id < 1";
  		$order_products_result_sql = tep_db_query($order_products_select_sql);
  		
  		while ($order_products_row = tep_db_fetch_array($order_products_result_sql)) {
  			$product_type_select_sql = "SELECT products_bundle, products_bundle_dynamic 
  										FROM products 
  										WHERE products_id = '" . $order_products_row["products_id"] . "'";
  			$product_type_result_sql = tep_db_query($product_type_select_sql);
			$product_type_row = tep_db_fetch_array($product_type_result_sql);
			
			if ($product_type_row['products_bundle_dynamic'] == 'yes') {
				upgrade_update_delivered_price($orders_stat_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle_dynamic');
			} else if ($product_type_row['products_bundle'] == 'yes') {
				upgrade_update_delivered_price($orders_stat_row['orders_id'], $order_products_row["orders_products_id"], 'products_bundle');
			} else {
				upgrade_update_delivered_price($orders_stat_row['orders_id'], $order_products_row["orders_products_id"], '');
			}
  		}
	}
}

function upgrade_update_reversed_price($order_id, $orders_product_id, $product_type='') {
	switch ($product_type) {
		case 'products_bundle_dynamic':
		case 'products_bundle':
			$total_subproduct = 0;
			$delivered_ratio = 0;
			
			$purchased_final_price_select_sql = "	SELECT products_id, final_price * products_quantity AS total_amount
													FROM " . TABLE_ORDERS_PRODUCTS . " 
													WHERE orders_id = '" . tep_db_input($order_id) . "' 
														AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			$purchased_final_price_result_sql = tep_db_query($purchased_final_price_select_sql);
			
			if ($purchased_final_price_row = tep_db_fetch_array($purchased_final_price_result_sql)) {
    			$subproduct_select_sql = "	SELECT products_quantity, products_reversed_quantity 
											FROM " . TABLE_ORDERS_PRODUCTS . " 
											WHERE orders_id = '" . tep_db_input($order_id) . "' 
												AND products_bundle_id = '" . tep_db_input($purchased_final_price_row['products_id']) . "'";
				$subproduct_result_sql = tep_db_query($subproduct_select_sql);
				
				while ($subproduct_row = tep_db_fetch_array($subproduct_result_sql)) {
					if ($subproduct_row['products_quantity'] > 0) {
						$delivered_ratio += (double)$subproduct_row['products_reversed_quantity'] / $subproduct_row['products_quantity'];
						
						$total_subproduct++;
					}
				}
				
				if ($total_subproduct) {
					$latest_price = (double)($purchased_final_price_row['total_amount'] * $delivered_ratio) / $total_subproduct;
					
					// Update the latest reversed price
					$price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
											SET products_reversed_price = '" . tep_db_input($latest_price) . "'
	    									WHERE orders_id = '" . tep_db_input($order_id) . "' 
	    										AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
	    			tep_db_query($price_update_sql);
				}
			}
			
			break;
			
		default:
			// Update the latest reversed price
			$price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
									SET products_reversed_price = products_reversed_quantity * final_price 
									WHERE orders_id = '" . tep_db_input($order_id) . "' 
										AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			tep_db_query($price_update_sql);
			
			break;
	}
}

function upgrade_update_delivered_price($order_id, $orders_product_id, $product_type='') {
	switch ($product_type) {
		case 'products_bundle_dynamic':
		case 'products_bundle':
			$total_subproduct = 0;
			$good_delivered_ratio = 0;
			
			$purchased_final_price_select_sql = "	SELECT products_id, final_price * products_quantity AS total_amount
													FROM " . TABLE_ORDERS_PRODUCTS . " 
													WHERE orders_id = '" . tep_db_input($order_id) . "' 
														AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			$purchased_final_price_result_sql = tep_db_query($purchased_final_price_select_sql);
			
			if ($purchased_final_price_row = tep_db_fetch_array($purchased_final_price_result_sql)) {
    			$subproduct_select_sql = "	SELECT products_quantity, products_good_delivered_quantity 
											FROM " . TABLE_ORDERS_PRODUCTS . " 
											WHERE orders_id = '" . tep_db_input($order_id) . "' 
												AND products_bundle_id = '" . tep_db_input($purchased_final_price_row['products_id']) . "'";
				$subproduct_result_sql = tep_db_query($subproduct_select_sql);
				
				while ($subproduct_row = tep_db_fetch_array($subproduct_result_sql)) {
					if ($subproduct_row['products_quantity'] > 0) {
						$good_delivered_ratio += (double)$subproduct_row['products_good_delivered_quantity'] / $subproduct_row['products_quantity'];
						
						$total_subproduct++;
					}
				}
				
				if ($total_subproduct) {
					$latest_delivered_price = (double)($purchased_final_price_row['total_amount'] * $good_delivered_ratio) / $total_subproduct;
					
					// Update the latest delivered price
					$delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
													SET products_good_delivered_price = '" . tep_db_input($latest_delivered_price) . "'
	    											WHERE orders_id = '" . tep_db_input($order_id) . "' 
	    												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
	    			tep_db_query($delivered_price_update_sql);
				}
			}
			
			break;
			
		default:
			// Update the latest delivered price
			$delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
											SET products_good_delivered_price = products_good_delivered_quantity * final_price 
											WHERE orders_id = '" . tep_db_input($order_id) . "' 
												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
			tep_db_query($delivered_price_update_sql);
			
			break;
	}
}
// End of update Good Delivered / Reversed Quantity
?>