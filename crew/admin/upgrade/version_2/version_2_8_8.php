<?
/*
	$Id: version_2_8_8.php,v 1.2 2007/05/29 08:12:05 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_store_credit_history_fields = get_table_fields("store_credit_history");

// Insert new records into categories_configuration table (for Compensate Item Email Address)
$conf_insert_sql = array();

$conf_insert_sql["ADD_COMPENSATE_ITEM_EMAIL"] = array("insert" => " (0, 'Compensate Item Email Address', 'ADD_COMPENSATE_ITEM_EMAIL', '', 'Email address to which the compensation notification email will be send to whenever someone compensate an item.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 12, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into categories_configuration table (for Compensate Item Email Address)

// Insert new field into store_credit_history table
$add_new_field = array();

$add_new_field['store_credit_history'] = array (	array (	"field_name" => "store_credit_history_activity_desc_show",
							 								"field_attr" => " tinyint(1) NOT NULL default '0' ",
							 								"add_after" => "store_credit_history_activity_desc"
							 						)
										);

add_field ($add_new_field);
// End of insert new field into store_credit_history table

if (!in_array('store_credit_history_activity_desc_show', $existing_store_credit_history_fields)) {
	// Update records in store_credit_history table (Set store_credit_history_activity_desc_show as show)
	$store_credit_history_update_sql = array();
	
	$store_credit_history_update_sql['store_credit_history'] = array(	array(	"field_name" => "store_credit_history_activity_desc_show",
																				"update" => " store_credit_history_activity_desc_show='1' ",
																				"where_str" => " (store_credit_history_activity_title <> '') OR (store_credit_activity_type='R' AND store_credit_history_activity_desc <> '') "
																				)
																	 );
	
	advance_update_records($store_credit_history_update_sql, $DBTables);
	// End of update records in store_credit_history table (Set store_credit_history_activity_desc_show as show)
}

// Insert new records into configuration table (for Issue Store Credit Notify Email)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Store Information'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["ISSUE_STORE_CREDIT_NOTIFY_EMAIL"] = array("insert" => " ('Issue Store Credit Notify Email', 'ISSUE_STORE_CREDIT_NOTIFY_EMAIL', '', 'Email address to which the email will be send to whenever there is store credit been issued for customer order.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 17, NULL, now(), NULL, '')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Issue Store Credit Notify Email)

// Insert new field into latest_news table
$add_new_field = array();

$add_new_field['latest_news'] = array (	array (	"field_name" => "extra_news_display_sites",
												"field_attr" => " tinyint(1) NOT NULL default '0' ",
												"add_after" => "news_display_sites"
												)
										);

add_field ($add_new_field);
// End of insert new field into latest_news table
?>