<?
/*
	$Id: version_2_8_9.php,v 1.1 2007/06/01 08:05:56 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on viewing Store Credit Issued/Used report type)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='store_credit.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["STORE_CREDIT_REPORT_SC_FLOW"] = array("insert" => " ('STORE_CREDIT_REPORT_SC_FLOW', 'View Store Credit Issued/Used report', ".$row_sql["admin_files_id"].", '1', 30)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing Store Credit Issued/Used report type)

// Insert new field into buyback_request_group table
$add_new_field = array();

$add_new_field['buyback_request_group'] = array (	array (	"field_name" => "orders_tag_ids",
															"field_attr" => " varchar(255) NOT NULL default '' ",
															"add_after" => "buyback_request_group_verify_mode"
															),
													array (	"field_name" => "orders_read_mode",
															"field_attr" => " tinyint(1) NOT NULL default '1' ",
															"add_after" => "orders_tag_ids"
															)
													);

add_field ($add_new_field);
// End of insert new field into buyback_request_group table

// Change field structure for orders_read_mode in buyback_request_group table
$change_field_structure = array();

$change_field_structure['buyback_request_group'] = array (array (	"field_name" => "orders_read_mode",
							 										"field_attr" => " tinyint(1) NOT NULL default '0' "
							 				  					)
									  					);
change_field_structure ($change_field_structure);
// End of change field structure for orders_read_mode in buyback_request_group table
?>