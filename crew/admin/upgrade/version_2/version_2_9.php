<?
/*
	$Id: version_2_9.php,v 1.2 2007/06/19 07:51:04 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

$existing_store_payments_fields = get_table_fields("store_payments");
$existing_payment_methods_fields = get_table_fields("payment_methods");
$existing_user_flags_fields = get_table_fields("user_flags");
$existing_site_code_fields = get_table_fields("site_code");

// Insert new records into store_payments_status table (for chinese status name)
$payments_status_insert_sql = array();
$payments_status_insert_sql["1"] = array(	"insert" => " (1, 2, '&#23457;&#26680;', 10) " );
insert_new_records('store_payments_status', "store_payments_status_id", $payments_status_insert_sql, $DBTables, "(store_payments_status_id, language_id, store_payments_status_name, store_payments_status_sort_order)", " store_payments_status_id='1' AND language_id=2 ");

$payments_status_insert_sql = array();
$payments_status_insert_sql["2"] = array(	"insert" => " (2, 2, '&#22788;&#29702;', 20) " );
insert_new_records('store_payments_status', "store_payments_status_id", $payments_status_insert_sql, $DBTables, "(store_payments_status_id, language_id, store_payments_status_name, store_payments_status_sort_order)", " store_payments_status_id='2' AND language_id=2 ");

$payments_status_insert_sql = array();
$payments_status_insert_sql["3"] = array(	"insert" => " (3, 2, '&#23436;&#25104;', 30) " );
insert_new_records('store_payments_status', "store_payments_status_id", $payments_status_insert_sql, $DBTables, "(store_payments_status_id, language_id, store_payments_status_name, store_payments_status_sort_order)", " store_payments_status_id='3' AND language_id=2 ");

$payments_status_insert_sql = array();
$payments_status_insert_sql["4"] = array(	"insert" => " (4, 2, '&#21462;&#28040;', 40) " );
insert_new_records('store_payments_status', "store_payments_status_id", $payments_status_insert_sql, $DBTables, "(store_payments_status_id, language_id, store_payments_status_name, store_payments_status_sort_order)", " store_payments_status_id='4' AND language_id=2 ");
// End of insert new records into store_payments_status table (for chinese status name)

// Insert new field into store_payments, payment_methods, user_flags and site_code tables
$add_new_field = array();

$add_new_field['store_payments'] = array (	array (	"field_name" => "store_payments_read_mode",
							 						"field_attr" => " tinyint(1) NOT NULL default '0' ",
							 						"add_after" => "store_payments_last_modified"
							 					)
										);

$add_new_field['payment_methods'] = array (	array (	"field_name" => "payment_methods_admin_groups_id",
							 						"field_attr" => " text NOT NULL ",
							 						"add_after" => "payment_methods_sort_order"
							 					)
										);

$add_new_field['user_flags'] = array (	array (	"field_name" => "user_flags_admin_groups_id",
												"field_attr" => " text NOT NULL ",
												"add_after" => "user_flags_off_notification"
							 					)
										);

$add_new_field['site_code'] = array (	array (	"field_name" => "buyback_admin_groups_id",
												"field_attr" => " text NOT NULL ",
												"add_after" => "admin_groups_id"
							 					)
										);

add_field ($add_new_field);
// End of insert new field into store_payments, payment_methods, user_flags and site_code tables

// Insert new records into admin_files_actions table (for permission on view customer's buyback statistic)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='buyback_requests_info.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["BUYBACK_VIEW_ORDER_STATISTICS"] = array("insert" => " ('BUYBACK_VIEW_ORDER_STATISTICS', 'View buyback order statistics', ".$row_sql["admin_files_id"].", '1,41', 30)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on view customer's buyback statistic)

// Insert new records into admin_files_actions table (for permissions on Edit Customer Profile page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_EDIT_PROFILE"] = array("insert" => " ('CUSTOMER_EDIT_PROFILE', 'Edit customer profile details', ".$row_sql["admin_files_id"].", '1,20,27,28,32,35,36,37,38,39,40,41,44', 6)" );
	$admin_files_actions_insert_sql["CUSTOMER_EDIT_ACCOUNT"] = array("insert" => " ('CUSTOMER_EDIT_ACCOUNT', 'Edit customer account', ".$row_sql["admin_files_id"].", '1,20,27,28,32,35,36,37,38,39,40,41,44', 6)" );
	$admin_files_actions_insert_sql["CUSTOMER_EDIT_GROUP"] = array("insert" => " ('CUSTOMER_EDIT_GROUP', 'Edit customer group', ".$row_sql["admin_files_id"].", '1,20,27,28,32,35,36,37,38,39,40,41,44', 6)" );
	$admin_files_actions_insert_sql["CUSTOMER_EDIT_REMARK"] = array("insert" => " ('CUSTOMER_EDIT_REMARK', 'Edit customer remark', ".$row_sql["admin_files_id"].", '1,20,27,28,32,35,36,37,38,39,40,41,44', 6)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permissions on Edit Customer Profile page)

// Insert new records into admin_files_actions table (for permission on batch update buyback orders)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='buyback_requests.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["BUYBACK_BATCH_UPDATE"] = array("insert" => " ('BUYBACK_BATCH_UPDATE', 'Batch update buyback order', ".$row_sql["admin_files_id"].", '1,23,27,41', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on batch update buyback orders)

if (!in_array('store_payments_read_mode', $existing_store_payments_fields)) {
	// Update records in store_payments table (Set all existing payment transactions as read)
	$store_payments_update_sql = array();
	
	$store_payments_update_sql['store_payments'] = array(	array(	"field_name" => "store_payments_read_mode",
																	"update" => " store_payments_read_mode='1' ",
																	"where_str" => " 1 "
																	)
														 );
	
	advance_update_records($store_payments_update_sql, $DBTables);
	// End of update records in store_payments table (Set all existing payment transactions as read)
}

if (!in_array('payment_methods_admin_groups_id', $existing_payment_methods_fields)) {
	// Update records in payment_methods table (Set all existing payment methods access permission to A&B Department)
	$payment_methods_update_sql = array();
	
	$payment_methods_update_sql['payment_methods'] = array(	array(	"field_name" => "payment_methods_admin_groups_id",
																	"update" => " payment_methods_admin_groups_id='1,42' ",
																	"where_str" => " 1 "
																)
													 );
	
	advance_update_records($payment_methods_update_sql, $DBTables);
	// End of update records in payment_methods table (Set all existing payment methods access permission to A&B Department)
}

if (!in_array('user_flags_admin_groups_id', $existing_user_flags_fields)) {
	// Update records in user_flags table
	$user_flags_update_sql = array();
	
	$user_flags_update_sql['user_flags'] = array(	array(	"field_name" => "user_flags_admin_groups_id",
															"update" => " user_flags_admin_groups_id='1,20,27,32,35,36,37,38,39,44' ",
															"where_str" => " 1 "
														)
												 );
	
	advance_update_records($user_flags_update_sql, $DBTables);
	// End of update records in user_flags table
}

if (!in_array('buyback_admin_groups_id', $existing_site_code_fields)) {
	// Update records in site_code table
	$site_code_update_sql = array();
	
	$site_code_update_sql['site_code'] = array(	array(	"field_name" => "buyback_admin_groups_id",
														"update" => " buyback_admin_groups_id='1,20,22,23,27,28,29,32,35,36,37,38,39,41,42,43,44,45,46' ",
														"where_str" => " site_has_buyback='1' "
													)
											 );
	
	advance_update_records($site_code_update_sql, $DBTables);
	// End of update records in site_code table
}
?>