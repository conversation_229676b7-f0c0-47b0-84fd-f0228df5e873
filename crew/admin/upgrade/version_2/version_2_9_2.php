<?
/*
	$Id: version_2_9_2.php,v 1.2 2007/06/28 09:52:39 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on Processing Western Union order)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ADM_WU_PROCESS_ORDER"] = array("insert" => " ('ADM_WU_PROCESS_ORDER', 'Update Western Union orders from \"Verifying\" to \"Processing\"', ".$row_sql["admin_files_id"].", '1', 37)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Processing Western Union order)

// Insert new records into categories_configuration table (for Buyback Alert Notification)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_ALERT_EMAIL"] = array("insert" => " (0, 'Buyback Alert Email Address', 'BUYBACK_ALERT_EMAIL', '', 'Email address to which the email will be send to when alert notification is required.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 17, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into categories_configuration table (for Buyback Alert Notification)
?>