<?
/*
	$Id: version_2_9_3.php,v 1.1 2007/07/19 17:56:05 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on editing preferences and price in Average Offer Price)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='suppliers_average_offer_price.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["BUYBACK_EDIT_GAME_PREFERENCES"] = array("insert" => " ('BUYBACK_EDIT_GAME_PREFERENCES', 'Edit game buyback preferences', ".$row_sql["admin_files_id"].", '1,23,38,41,45,', 10)" );
	$admin_files_actions_insert_sql["BUYBACK_EDIT_GAME_OFFER_PRICES"] = array("insert" => " ('BUYBACK_EDIT_GAME_OFFER_PRICES', 'Edit game buyback offer prices', ".$row_sql["admin_files_id"].", '1,23,41,45,', 20)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on editing preferences and price in Average Offer Price)

?>