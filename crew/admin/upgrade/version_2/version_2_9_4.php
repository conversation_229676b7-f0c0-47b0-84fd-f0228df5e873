<?
/*
	$Id: version_2_9_4.php,v 1.3 2007/07/30 11:35:12 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

// Create new tables (New Layout)
$add_new_tables = array();

$add_new_tables["categories_types_groups"] = array ("structure" => "CREATE TABLE `categories_types_groups` (
																	  `categories_types_groups_id` int(11) NOT NULL auto_increment,
																	  `categories_types_groups_name` varchar(255) NOT NULL default '',
																	  PRIMARY KEY  (`categories_types_groups_id`)
																	) TYPE=MyISAM;",
													"data" => "	INSERT INTO `categories_types_groups` (`categories_types_groups_id`, `categories_types_groups_name`) 
																VALUES 	(1, 'WOW'),
																		(2, 'LOTR'),
																		(3, 'Anarchy Online'),
																		(4, 'Archlord'),
																		(5, 'Auto Assault'),
																		(6, 'Cabal Online'),
																		(7, 'City of Heroes'),
																		(8, 'City of Villains'),
																		(9, '<PERSON><PERSON><PERSON>'),
																		(10, 'Dungeons & Dragons Online'),
																		(11, 'Eve Online'),
																		(12, 'Everquest II'),
																		(13, 'Final Fantasy XI'),
																		(14, 'Guild Wars'),
																		(15, 'Hero Online'),
																		(16, 'Lineage II'),
																		(17, 'Maple Story'),
																		(18, 'RF Online'),
																		(19, 'Rose Online'),
																		(20, 'Silkroad Online'),
																		(21, 'Vanguard SOH'),
																		(22, 'Matrix Online'),
																		(23, 'Star Wars Galaxies');"
												);

$add_new_tables["categories_types_sets"] = array (	"structure" => "CREATE TABLE `categories_types_sets` (
																	  `categories_types_sets_id` int(11) NOT NULL auto_increment,
																	  `categories_types_groups_id` int(11) NOT NULL default '0',
																	  `custom_products_type_id` int(11) NOT NULL default '0',
																	  `categories_types_root_id` int(11) NOT NULL default '0',
																	  PRIMARY KEY  (`categories_types_sets_id`)
																	) TYPE=MyISAM;",
													"data" => "	INSERT INTO `categories_types_sets` (`categories_types_sets_id`, `categories_types_groups_id`, `custom_products_type_id`, `categories_types_root_id`) 
																VALUES 	(1, 1, 0, 1),
																		(2, 2, 0, 5),
																		(3, 3, 0, 8),
																		(4, 4, 0, 10),
																		(5, 5, 0, 12),
																		(6, 6, 0, 15),
																		(7, 7, 0, 17),
																		(8, 8, 0, 19),
																		(9, 9, 0, 21),
																		(10, 10, 0, 23),
																		(11, 11, 0, 26),
																		(12, 12, 0, 28),
																		(13, 13, 0, 31),
																		(14, 14, 0, 33),
																		(15, 15, 0, 35),
																		(16, 16, 0, 37),
																		(17, 17, 0, 39),
																		(18, 18, 0, 42),
																		(19, 19, 0, 45),
																		(20, 20, 0, 47),
																		(21, 21, 0, 49),
																		(22, 1, 1, 51),
																		(23, 2, 1, 53),
																		(24, 22, 0, 55),
																		(25, 23, 0, 57);"
												);

$add_new_tables["categories_types"] = array (	"structure" => "CREATE TABLE `categories_types` (
																  `categories_types_id` int(11) NOT NULL auto_increment,
																  `categories_types_value` varchar(255) NOT NULL default '',
																  `categories_types_parent_id` varchar(255) NOT NULL default '0',
																  `categories_types_display` tinyint(1) NOT NULL default '0',
																  PRIMARY KEY  (`categories_types_id`)
																) TYPE=MyISAM;",
												"data" => "	INSERT INTO `categories_types` (`categories_types_id`, `categories_types_value`, `categories_types_parent_id`, `categories_types_display`) 
																VALUES 	(1, 'Region', '0', 1),
																		(2, 'Category Type', '1', 0),
																		(3, 'Server', '2', 1),
																		(4, 'Factions', '3', 1),
																		(5, 'Region', '0', 1),
																		(6, 'Category Type', '5', 0),
																		(7, 'Server', '6', 1),
																		(8, 'Category Type', '0', 0),
																		(9, 'Server', '8', 1),
																		(10, 'Category Type', '0', 0),
																		(11, 'Server', '10', 1),
																		(12, 'Category Type', '0', 0),
																		(13, 'Server', '12', 1),
																		(14, 'Faction', '13', 1),
																		(15, 'Category Type', '0', 0),
																		(16, 'Server', '15', 1),
																		(17, 'Category Type', '0', 0),
																		(18, 'Server', '17', 1),
																		(19, 'Category Type', '0', 0),
																		(20, 'Server', '19', 1),
																		(21, 'Category Type', '0', 0),
																		(22, 'Server', '21', 1),
																		(23, 'Region', '0', 1),
																		(24, 'Category Type', '23', 0),
																		(25, 'Server', '24', 1),
																		(26, 'Category Type', '0', 0),
																		(27, 'Server', '26', 1),
																		(28, 'Category Type', '0', 0),
																		(29, 'Server', '28', 1),
																		(30, 'Factions', '29', 1),
																		(31, 'Category Type', '0', 0),
																		(32, 'Server', '31', 1),
																		(33, 'Category Type', '0', 0),
																		(34, 'Campaign', '33', 1),
																		(35, 'Category Type', '0', 0),
																		(36, 'Server', '35', 1),
																		(37, 'Category Type', '0', 0),
																		(38, 'Server', '37', 1),
																		(39, 'Region', '0', 1),
																		(40, 'Category Type', '39', 0),
																		(41, 'Server', '40', 1),
																		(42, 'Category Type', '0', 0),
																		(43, 'Server', '42', 1),
																		(44, 'Factions', '43', 1),
																		(45, 'Category Type', '0', 0),
																		(46, 'Server', '45', 1),
																		(47, 'Category Type', '0', 0),
																		(48, 'Server', '47', 1),
																		(49, 'Category Type', '0', 0),
																		(50, 'Server', '49', 1),
																		(51, 'Region', '0', 1),
																		(52, 'Category Type', '51', 0),
																		(53, 'Region', '0', 1),
																		(54, 'Category Type', '53', 0),
																		(55, 'Category Type', '0', 0),
																		(56, 'Server', '55', 1),
																		(57, 'Category Type', '0', 0),
																		(58, 'Server', '57', 1);"
												);

$add_new_tables["orders_products_extra_info"] = array (	"structure" => "CREATE TABLE `orders_products_extra_info` (
																			`orders_products_id` INT( 11 ) NOT NULL ,
																			`orders_products_extra_info_key` VARCHAR( 100 ) NOT NULL ,
																			`orders_products_extra_info_value` TEXT NOT NULL ,
																			PRIMARY KEY ( `orders_products_id` , `orders_products_extra_info_key` )
																		) TYPE=MYISAM;",
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create new tables (New Layout)

// Insert new fields into categories table
$add_new_field = array();

$add_new_field['categories'] = array (	array (	"field_name" => "custom_products_type_id",
												"field_attr" => " int(11) NOT NULL default '999' ",
												"add_after" => ""
												),
										array (	"field_name" => "categories_types_groups_id",
												"field_attr" => " int(11) NOT NULL default '0' ",
												"add_after" => ""
												)
									);

add_field($add_new_field);
// End of insert new fields into categories table

// Insert new records into define_mainpage table
$define_mainpage_insert_sql = array();

$define_mainpage_insert_sql["1"] = array	(	"insert" => " (1, 1, '') " );
$define_mainpage_insert_sql["2"] = array	(	"insert" => " (2, 1, '') " );

insert_new_records('define_mainpage', "Id", $define_mainpage_insert_sql, $DBTables, "(Id, language_id, Text)", "");
// End of insert new records into define_mainpage table

// Insert new records into custom_products_type table
$custom_products_type_insert_sql = array();

$custom_products_type_insert_sql["0"] = array	(	"insert" => " ('0', 'Game Currency', '0', '', '', '') " );

insert_new_records('custom_products_type', "custom_products_type_id", $custom_products_type_insert_sql, $DBTables, "(`custom_products_type_id` , `custom_products_type_name` , `data_pool_id` , `custom_products_low_stock_email` , `custom_products_add_stock_email` , `custom_products_deduct_stock_email`)", "");
// End of insert new records into custom_products_type table

// Update records in custom_products_type table (Set Game Currency as 0 type)
$custom_products_type_update_sql = array();

$custom_products_type_update_sql['custom_products_type'] = array(	array(	"field_name" => "custom_products_type_id",
																			"update" => " custom_products_type_id='0' ",
																			"where_str" => " custom_products_type_name = 'Game Currency'"
																			)
																 );

advance_update_records($custom_products_type_update_sql, $DBTables);
// End of update records in custom_products_type table (Set Game Currency as 0 type)

// Insert new records into admin_files table (for defining category types page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='catalog.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["categories_types.php"] = array("insert" => " ('categories_types.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   									);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='categories_types.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for defining category types page)

// Insert new records into admin_files table (for defining mainpage and help center page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='infolinks.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["define_mainpage.php"] = array(	"insert" => " ('define_mainpage.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   									);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='define_mainpage.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["define_helpcenterpage.php"] = array(	"insert" => " ('define_helpcenterpage.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   											);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='define_helpcenterpage.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for defining mainpage and help center page)

?>