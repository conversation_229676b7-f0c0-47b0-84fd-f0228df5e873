<?
/*
	$Id: version_3_0_1.php,v 1.1 2008/12/30 05:35:09 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Rename country name in countries table
$country_update_sql = array();

$country_update_sql[TABLE_COUNTRIES] = array(	array(	"field_name" => "countries_name",
														"update" => " countries_name='Bosnia and Herzegovina' ",
														"where_str" => " countries_id='27'"
														)
											 );

$country_update_sql['orders'] = array(	array(	"field_name" => "customers_country",
												"update" => " customers_country='Bosnia and Herzegovina' ",
												"where_str" => " customers_country='Bosnia and Herzegowina'"
												),
										array(	"field_name" => "customers_telephone_country",
												"update" => " customers_telephone_country='Bosnia and Herzegovina' ",
												"where_str" => " customers_telephone_country='Bosnia and Herzegowina'"
												),
										array(	"field_name" => "billing_country",
												"update" => " billing_country='Bosnia and Herzegovina' ",
												"where_str" => " billing_country='Bosnia and Herzegowina'"
												),
									 );

advance_update_records($country_update_sql, $DBTables);

// End of rename country name in countries table


?>