<?
/*
	$Id: version_3_0_2.php,v 1.5 2009/01/06 04:46:58 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create store_credit_daily_history table
$add_new_tables = array();

$add_new_tables["store_credit_daily_history"] = array 	(	"structure" => 	"CREATE TABLE `store_credit_daily_history` (
																			  `store_credit_daily_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
																			  `store_credit_daily_history_currency` char(3) NOT NULL default '',
																			  `store_credit_daily_history_credit_type` varchar(4) NOT NULL default '',
																			  `store_credit_daily_history_amount` decimal(15,4) NOT NULL default '0.0000',
																			  `store_credit_daily_history_reserved_amount` decimal(15,4) NOT NULL default '0.0000',
																			  `user_id` varchar(32) NOT NULL default '',
																			  `user_role` varchar(16) NOT NULL default '',
																			  PRIMARY KEY  (`store_credit_daily_history_credit_type`,`store_credit_daily_history_date`,`store_credit_daily_history_currency`,`user_id`),
																			  KEY `index_closing_balance` (`store_credit_daily_history_credit_type`,`user_id`,`store_credit_daily_history_date`)
																			) TYPE=MyISAM;",
															"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// Enf of create store_credit_daily_history table

// Insert new records into configuration table (Control allow Buyback Screenshot)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_ENABLE_UPLOAD_SCREENSHOT"] = array("insert" => " ('Enable screenshot uploading module', 'BUYBACK_ENABLE_UPLOAD_SCREENSHOT', 'true', 'Enable screenshot uploading module', ".$row_sql["configuration_group_id"].", 15, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (Control allow Buyback Screenshot)

// Delete competitors_average_price_history and competitors_buying_price_history tables
$delete_tables_array = array();

$delete_tables_array = array('competitors_average_price_history', 'competitors_buying_price_history');

delete_tables ($delete_tables_array, $DBTables);
// End of delete competitors_average_price_history and competitors_buying_price_history tables

// Delete records in admin_files table
$admin_file_delete_sql = array();

$admin_file_delete_sql['competitors_average_market_price.php'] = array(	"extra_where" => " admin_files_is_boxes=0 " );
delete_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_file_delete_sql, $DBTables);

// End of delete records in admin_files table

// Insert new records into configuration table (Min and Max input control)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Customer Options'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["ENTRY_SECRET_ANSWER_MIN_LENGTH"] = array("insert" => " ('Q&A Answer', 'ENTRY_SECRET_ANSWER_MIN_LENGTH', '3', 'Minimum length of Q&A answer', ".$row_sql["configuration_group_id"].", 78, NULL, NOW(), NULL, NULL)" );
	$conf_insert_sql["ENTRY_IM_ACCOUNT_MAX_ENTRIES"] = array("insert" => " ('Instant Messenger', 'ENTRY_IM_ACCOUNT_MAX_ENTRIES', '10', 'Maximum entries of instant messenger allowed for a customer', ".$row_sql["configuration_group_id"].", 90, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (Min and Max input control)
?>