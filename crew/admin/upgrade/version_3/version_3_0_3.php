<?
/*
	$Id: version_3_0_3.php,v 1.2 2009/01/14 03:41:53 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create maybank tables
$add_new_tables = array();

$add_new_tables["maybank"] = array (	"structure" => "CREATE TABLE `maybank` (
														  `orders_id` int(11) NOT NULL default '0',
														  `maybank_status` char(2) NOT NULL default '',
														  `maybank_corporate` varchar(255) NOT NULL default '',
														  `maybank_account` varchar(30) NOT NULL default '',
														  `maybank_currency` char(3) default NULL,
														  `maybank_amount` decimal(15,2) NOT NULL default '0.00',
														  `maybank_approval_code` varchar(255) NOT NULL default '',
														  `maybank_reference_id` varchar(20) NOT NULL default '',
														  PRIMARY KEY (`orders_id`),
														  KEY `index_maybank_reference_id` (`maybank_reference_id`)
														) TYPE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["maybank_payment_status_history"] = array (	"structure" => "CREATE TABLE `maybank_payment_status_history` (
																			  `maybank_payment_status_history_id` int(11) NOT NULL auto_increment,
																			  `orders_id` int(11) NOT NULL default '0',
																			  `maybank_date` datetime NOT NULL default '0000-00-00 00:00:00',
																			  `maybank_status` char(2) NOT NULL default '',
																			  PRIMARY KEY  (`maybank_payment_status_history_id`),
																			  KEY `index_orders_id` (`orders_id`)
																			) TYPE=MyISAM;",
															"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// End of maybank tables

// Create categories_services table
$add_new_tables = array();

$add_new_tables["categories_services"] = array (	"structure" => "CREATE TABLE `categories_services` (
																	  `categories_id` int(11) NOT NULL default '0',
																	  `language_id` int(11) NOT NULL default '1',
																	  `categories_services_name` varchar(64) NOT NULL default '',
																	  `categories_services_url` varchar(255) NOT NULL default '',
																	  `type` tinyint(1) NOT NULL default '1',
																	  PRIMARY KEY  (`categories_id`,`language_id`,`type`)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create categories_services table

// Define geo_zone_type as index key in geo_zones table
add_index_key ('geo_zones', 'index_geo_zone_type', 'index', 'geo_zone_type', $DBTables);
// End of define geo_zone_type as index key in geo_zones table
?>