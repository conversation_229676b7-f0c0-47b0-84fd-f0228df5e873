<?
/*
	$Id: version_3_0_5.php,v 1.1 2009/01/19 04:57:29 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create search_products table
$add_new_tables = array();

$add_new_tables["jos_content_frontpage"] = array (	"structure" => "CREATE TABLE  `jos_content_frontpage` (
																	  `content_id` int(11) NOT NULL default '0',
																	  `ordering` int(11) NOT NULL default '0',
																	  PRIMARY KEY  (`content_id`)
																	) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
													"data" => ""
												);

$add_new_tables["jos_content_rating"] = array (	"structure" => "CREATE TABLE  `jos_content_rating` (
																  `content_id` int(11) NOT NULL default '0',
																  `rating_sum` int(11) unsigned NOT NULL default '0',
																  `rating_count` int(11) unsigned NOT NULL default '0',
																  `lastip` varchar(50) NOT NULL default '',
																  PRIMARY KEY  (`content_id`)
																) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
												"data" => ""
											);

$add_new_tables["jos_core_acl_aro"] = array (	"structure" => "CREATE TABLE  `jos_core_acl_aro` (
																  `id` int(11) NOT NULL auto_increment,
																  `section_value` varchar(240) NOT NULL default '0',
																  `value` varchar(240) NOT NULL default '',
																  `order_value` int(11) NOT NULL default '0',
																  `name` varchar(255) NOT NULL default '',
																  `hidden` int(11) NOT NULL default '0',
																  PRIMARY KEY  (`id`),
																  UNIQUE KEY `jos_section_value_value_aro` (`section_value`(100),`value`(100)),
																  KEY `jos_gacl_hidden_aro` (`hidden`)
																) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
												"data" => "INSERT INTO `jos_core_acl_aro` (`id`, `section_value`, `value`, `order_value`, `name`, `hidden`) 
															VALUES (10, 'users', '62', 0, 'Michael', 0);"
											);

$add_new_tables["jos_core_acl_aro_groups"] = array (	"structure" => "CREATE TABLE  `jos_core_acl_aro_groups` (
																		  `id` int(11) NOT NULL auto_increment,
																		  `parent_id` int(11) NOT NULL default '0',
																		  `name` varchar(255) NOT NULL default '',
																		  `lft` int(11) NOT NULL default '0',
																		  `rgt` int(11) NOT NULL default '0',
																		  `value` varchar(255) NOT NULL default '',
																		  PRIMARY KEY  (`id`),
																		  KEY `jos_gacl_parent_id_aro_groups` (`parent_id`),
																		  KEY `jos_gacl_lft_rgt_aro_groups` (`lft`,`rgt`)
																		) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
														"data" => "INSERT INTO `jos_core_acl_aro_groups` (`id`, `parent_id`, `name`, `lft`, `rgt`, `value`) 
																	VALUES (17, 0, 'ROOT', 1, 22, 'ROOT'),
																	(28, 17, 'USERS', 2, 21, 'USERS'),
																	(29, 28, 'Public Frontend', 3, 12, 'Public Frontend'),
																	(18, 29, 'Registered', 4, 11, 'Registered'),
																	(19, 18, 'Author', 5, 10, 'Author'),
																	(20, 19, 'Editor', 6, 9, 'Editor'),
																	(21, 20, 'Publisher', 7, 8, 'Publisher'),
																	(30, 28, 'Public Backend', 13, 20, 'Public Backend'),
																	(23, 30, 'Manager', 14, 19, 'Manager'),
																	(24, 23, 'Administrator', 15, 18, 'Administrator'),
																	(25, 24, 'Super Administrator', 16, 17, 'Super Administrator');"
													);

$add_new_tables["jos_core_acl_aro_map"] = array (	"structure" => "CREATE TABLE  `jos_core_acl_aro_map` (
																	  `acl_id` int(11) NOT NULL default '0',
																	  `section_value` varchar(230) NOT NULL default '0',
																	  `value` varchar(100) NOT NULL default '',
																	  PRIMARY KEY  (`acl_id`,`section_value`,`value`)
																	) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
														"data" => ""
													);

$add_new_tables["jos_core_acl_aro_sections"] = array (	"structure" => "CREATE TABLE  `jos_core_acl_aro_sections` (
																		  `id` int(11) NOT NULL auto_increment,
																		  `value` varchar(230) NOT NULL default '',
																		  `order_value` int(11) NOT NULL default '0',
																		  `name` varchar(230) NOT NULL default '',
																		  `hidden` int(11) NOT NULL default '0',
																		  PRIMARY KEY  (`id`),
																		  UNIQUE KEY `jos_gacl_value_aro_sections` (`value`),
																		  KEY `jos_gacl_hidden_aro_sections` (`hidden`)
																		) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
														"data" => "INSERT INTO `jos_core_acl_aro_sections` (`id`, `value`, `order_value`, `name`, `hidden`) 
																	VALUES (10, 'users', 1, 'Users', 0);"
													);

$add_new_tables["jos_core_acl_groups_aro_map"] = array (	"structure" => "CREATE TABLE  `jos_core_acl_groups_aro_map` (
																			  `group_id` int(11) NOT NULL default '0',
																			  `section_value` varchar(240) NOT NULL default '',
																			  `aro_id` int(11) NOT NULL default '0',
																			  UNIQUE KEY `group_id_aro_id_groups_aro_map` (`group_id`,`section_value`,`aro_id`)
																			) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
															"data" => "INSERT INTO `jos_core_acl_groups_aro_map` (`group_id`, `section_value`, `aro_id`) 
																		VALUES (25, '', 10);"
														);

$add_new_tables["jos_core_log_items"] = array (	"structure" => "CREATE TABLE  `jos_core_log_items` (
																  `time_stamp` date NOT NULL default '0000-00-00',
																  `item_table` varchar(50) NOT NULL default '',
																  `item_id` int(11) unsigned NOT NULL default '0',
																  `hits` int(11) unsigned NOT NULL default '0'
																) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
												"data" => ""
											);

$add_new_tables["jos_core_log_searches"] = array (	"structure" => "CREATE TABLE  `jos_core_log_searches` (
																	  `search_term` varchar(128) NOT NULL default '',
																	  `hits` int(11) unsigned NOT NULL default '0'
																	) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
													"data" => ""
												);
											
$add_new_tables["jos_groups"] = array (	"structure" => "CREATE TABLE  `jos_groups` (
														  `id` tinyint(3) unsigned NOT NULL default '0',
														  `name` varchar(50) NOT NULL default '',
														  PRIMARY KEY  (`id`)
														) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
										"data" => "INSERT INTO `jos_groups` (`id`, `name`) 
													VALUES (0, 'Public'),
														(1, 'Registered'),
														(2, 'Special');"
									);

$add_new_tables["jos_jf_content"] = array (	"structure" => "CREATE TABLE  `jos_jf_content` (
															  `id` int(10) unsigned NOT NULL auto_increment,
															  `language_id` int(11) NOT NULL default '0',
															  `reference_id` int(11) NOT NULL default '0',
															  `reference_table` varchar(100) NOT NULL default '',
															  `reference_field` varchar(100) NOT NULL default '',
															  `value` text NOT NULL,
															  `original_value` varchar(255) default NULL,
															  `original_text` text,
															  `modified` datetime NOT NULL default '0000-00-00 00:00:00',
															  `modified_by` int(11) unsigned NOT NULL default '0',
															  `published` tinyint(1) unsigned NOT NULL default '0',
															  PRIMARY KEY  (`id`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;" ,
											"data" => ""
										);


$add_new_tables["jos_jf_tableinfo"] = array (	"structure" => "CREATE TABLE  `jos_jf_tableinfo` (
																  `id` int(11) NOT NULL auto_increment,
																  `joomlatablename` varchar(100) NOT NULL default '',
																  `tablepkID` varchar(100) NOT NULL default '',
																  PRIMARY KEY  (`id`)
																) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
												"data" => ""
											);

$add_new_tables["jos_languages"] = array (	"structure" => "CREATE TABLE  `jos_languages` (
															  `id` int(11) NOT NULL auto_increment,
															  `name` varchar(100) NOT NULL default '',
															  `active` tinyint(1) NOT NULL default '0',
															  `iso` varchar(20) default NULL,
															  `code` varchar(20) NOT NULL default '',
															  `shortcode` varchar(20) default NULL,
															  `image` varchar(100) default NULL,
															  `fallback_code` varchar(20) NOT NULL default '',
															  `params` text,
															  `ordering` int(11) NOT NULL default '0',
															  PRIMARY KEY  (`id`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=4 ;" ,
											"data" => "INSERT INTO `jos_languages` (`id`, `name`, `active`, `iso`, `code`, `shortcode`, `image`, `fallback_code`, `params`, `ordering`) 
														VALUES	(2, 'English (United Kingdom)', 1, 'en_GB.utf8, en_GB.UT', 'en-GB', 'en', '', '', '', 0),
														(3, '&#31616;&#20307;&#20013;&#25991;', 1, 'zh_CN.utf8, zh, zho,', 'zh-CN', 'zh', '', '', '', 0);"
										);

$add_new_tables["jos_menu"] = array (	"structure" => "CREATE TABLE  `jos_menu` (
														  `id` int(11) NOT NULL auto_increment,
														  `menutype` varchar(75) default NULL,
														  `name` varchar(255) default NULL,
														  `alias` varchar(255) NOT NULL default '',
														  `link` text,
														  `type` varchar(50) NOT NULL default '',
														  `published` tinyint(1) NOT NULL default '0',
														  `parent` int(11) unsigned NOT NULL default '0',
														  `componentid` int(11) unsigned NOT NULL default '0',
														  `sublevel` int(11) default '0',
														  `ordering` int(11) default '0',
														  `checked_out` int(11) unsigned NOT NULL default '0',
														  `checked_out_time` datetime NOT NULL default '0000-00-00 00:00:00',
														  `pollid` int(11) NOT NULL default '0',
														  `browserNav` tinyint(4) default '0',
														  `access` tinyint(3) unsigned NOT NULL default '0',
														  `utaccess` tinyint(3) unsigned NOT NULL default '0',
														  `params` text NOT NULL,
														  `lft` int(11) unsigned NOT NULL default '0',
														  `rgt` int(11) unsigned NOT NULL default '0',
														  `home` int(1) unsigned NOT NULL default '0',
														  PRIMARY KEY  (`id`),
														  KEY `componentid` (`componentid`,`menutype`,`published`,`access`),
														  KEY `menutype` (`menutype`)
														) TYPE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=56 ;" ,
											"data" => "INSERT INTO `jos_menu` (`id`, `menutype`, `name`, `alias`, `link`, `type`, `published`, `parent`, `componentid`, `sublevel`, `ordering`, `checked_out`, `checked_out_time`, `pollid`, `browserNav`, `access`, `utaccess`, `params`, `lft`, `rgt`, `home`) 
														VALUES	(1, 'browse-by-games', 'Home', 'home', 'index.php?option=com_content&view=article&id=46', 'component', 1, 0, 20, 0, 1, 0, '0000-00-00 00:00:00', 0, 0, 0, 3, 'show_noauth=0\nshow_title=1\nlink_titles=0\nshow_intro=1\nshow_section=0\nlink_section=0\nshow_category=0\nlink_category=0\nshow_author=1\nshow_create_date=1\nshow_modify_date=1\nshow_item_navigation=0\nshow_readmore=1\nshow_vote=0\nshow_icons=1\nshow_pdf_icon=1\nshow_print_icon=1\nshow_email_icon=1\nshow_hits=1\nfeed_summary=\npage_title=Welcome to Offgamers Game Guides \nshow_page_title=1\npageclass_sfx=\nmenu_image=-1\nsecure=0\n\n', 0, 0, 1),
															(2, 'mainmenu', 'Joomla! License', 'joomla-license', 'index.php?option=com_content&view=article&id=5', 'component', 1, 0, 20, 0, 3, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'pageclass_sfx=\nmenu_image=-1\nsecure=0\nshow_noauth=0\nlink_titles=0\nshow_intro=1\nshow_section=0\nlink_section=0\nshow_category=0\nlink_category=0\nshow_author=1\nshow_create_date=1\nshow_modify_date=1\nshow_item_navigation=0\nshow_readmore=1\nshow_vote=0\nshow_icons=1\nshow_pdf_icon=1\nshow_print_icon=1\nshow_email_icon=1\nshow_hits=1\n\n', 0, 0, 0),
															(41, 'mainmenu', 'FAQ', 'faq', 'index.php?option=com_content&view=section&id=3', 'component', 1, 0, 20, 0, 5, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'show_page_title=1\nshow_description=0\nshow_description_image=0\nshow_categories=1\nshow_empty_categories=0\nshow_cat_num_articles=1\nshow_category_description=1\npageclass_sfx=\nmenu_image=-1\nsecure=0\norderby=\nshow_noauth=0\nshow_title=1\nlink_titles=0\nshow_intro=1\nshow_section=0\nlink_section=0\nshow_category=0\nlink_category=0\nshow_author=1\nshow_create_date=1\nshow_modify_date=1\nshow_item_navigation=0\nshow_readmore=1\nshow_vote=0\nshow_icons=1\nshow_pdf_icon=1\nshow_print_icon=1\nshow_email_icon=1\nshow_hits=1', 0, 0, 0),
															(20, 'usermenu', 'Your Details', 'your-details', 'index.php?option=com_user&view=user&task=edit', 'component', -2, 0, 14, 0, 1, 0, '0000-00-00 00:00:00', 0, 0, 1, 3, '', 0, 0, 0),
															(24, 'usermenu', 'Logout', 'logout', 'index.php?option=com_user&view=login', 'component', 1, 0, 14, 0, 4, 0, '0000-00-00 00:00:00', 0, 0, 1, 3, '', 0, 0, 0),
															(27, 'mainmenu', 'Joomla! Overview', 'joomla-overview', 'index.php?option=com_content&view=article&id=19', 'component', 1, 0, 20, 0, 2, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'pageclass_sfx=\nmenu_image=-1\nsecure=0\nshow_noauth=0\nlink_titles=0\nshow_intro=1\nshow_section=0\nlink_section=0\nshow_category=0\nlink_category=0\nshow_author=1\nshow_create_date=1\nshow_modify_date=1\nshow_item_navigation=0\nshow_readmore=1\nshow_vote=0\nshow_icons=1\nshow_pdf_icon=1\nshow_print_icon=1\nshow_email_icon=1\nshow_hits=1\n\n', 0, 0, 0),
															(54, 'contribute', 'Submit a Guide', 'submit-a-guide', 'index.php?option=com_content&view=article&id=56', 'component', 1, 0, 20, 0, 1, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'show_noauth=\nshow_title=\nlink_titles=\nshow_intro=\nshow_section=\nlink_section=\nshow_category=\nlink_category=\nshow_author=\nshow_create_date=\nshow_modify_date=\nshow_item_navigation=\nshow_readmore=\nshow_vote=\nshow_icons=\nshow_pdf_icon=\nshow_print_icon=\nshow_email_icon=\nshow_hits=\nfeed_summary=\npage_title=\nshow_page_title=0\npageclass_sfx=\nmenu_image=-1\nsecure=0\n\n', 0, 0, 0),
															(34, 'mainmenu', 'What''s New in 1.5?', 'what-is-new-in-1-5', 'index.php?option=com_content&view=article&id=22', 'component', 1, 27, 20, 1, 1, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'pageclass_sfx=\nmenu_image=-1\nsecure=0\nshow_noauth=0\nshow_title=1\nlink_titles=0\nshow_intro=1\nshow_section=0\nlink_section=0\nshow_category=0\nlink_category=0\nshow_author=1\nshow_create_date=1\nshow_modify_date=1\nshow_item_navigation=0\nshow_readmore=1\nshow_vote=0\nshow_icons=1\nshow_pdf_icon=1\nshow_print_icon=1\nshow_email_icon=1\nshow_hits=1\n\n', 0, 0, 0),
															(37, 'mainmenu', 'More about Joomla!', 'more-about-joomla', 'index.php?option=com_content&view=section&id=4', 'component', 1, 0, 20, 0, 4, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'show_page_title=1\nshow_description=0\nshow_description_image=0\nshow_categories=1\nshow_empty_categories=0\nshow_cat_num_articles=1\nshow_category_description=1\npageclass_sfx=\nmenu_image=-1\nsecure=0\norderby=\nshow_noauth=0\nshow_title=1\nlink_titles=0\nshow_intro=1\nshow_section=0\nlink_section=0\nshow_category=0\nlink_category=0\nshow_author=1\nshow_create_date=1\nshow_modify_date=1\nshow_item_navigation=0\nshow_readmore=1\nshow_vote=0\nshow_icons=1\nshow_pdf_icon=1\nshow_print_icon=1\nshow_email_icon=1\nshow_hits=1', 0, 0, 0),
															(48, 'mainmenu', 'Web Links', 'web-links', 'index.php?option=com_weblinks&view=categories', 'component', 1, 0, 4, 0, 7, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'page_title=Weblinks\nimage=-1\nimage_align=right\npageclass_sfx=\nmenu_image=-1\nsecure=0\nshow_comp_description=1\ncomp_description=\nshow_link_hits=1\nshow_link_description=1\nshow_other_cats=1\nshow_headings=1\nshow_page_title=1\nlink_target=0\nlink_icons=\n\n', 0, 0, 0),
															(49, 'mainmenu', 'News Feeds', 'news-feeds', 'index.php?option=com_newsfeeds&view=categories', 'component', 1, 0, 11, 0, 8, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'show_page_title=1\npage_title=Newsfeeds\nshow_comp_description=1\ncomp_description=\nimage=-1\nimage_align=right\npageclass_sfx=\nmenu_image=-1\nsecure=0\nshow_headings=1\nshow_name=1\nshow_articles=1\nshow_link=1\nshow_other_cats=1\nshow_cat_description=1\nshow_cat_items=1\nshow_feed_image=1\nshow_feed_description=1\nshow_item_description=1\nfeed_word_count=0\n\n', 0, 0, 0),
															(50, 'mainmenu', 'The News', 'the-news', 'index.php?option=com_content&view=category&layout=blog&id=1', 'component', 1, 0, 20, 0, 6, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'show_page_title=1\npage_title=The News\nshow_description=0\nshow_description_image=0\nnum_leading_articles=1\nnum_intro_articles=4\nnum_columns=2\nnum_links=4\nshow_title=1\npageclass_sfx=\nmenu_image=-1\nsecure=0\norderby_pri=\norderby_sec=\nshow_pagination=2\nshow_pagination_results=1\nshow_noauth=0\nlink_titles=0\nshow_intro=1\nshow_section=0\nlink_section=0\nshow_category=0\nlink_category=0\nshow_author=1\nshow_create_date=1\nshow_modify_date=1\nshow_item_navigation=0\nshow_readmore=1\nshow_vote=0\nshow_icons=1\nshow_pdf_icon=1\nshow_print_icon=1\nshow_email_icon=1\nshow_hits=1\n\n', 0, 0, 0),
															(51, 'usermenu', 'Submit an Article', 'submit-an-article', 'index.php?option=com_content&view=article&id=56', 'component', 1, 0, 20, 0, 3, 0, '0000-00-00 00:00:00', 0, 0, 1, 0, 'show_noauth=\nshow_title=\nlink_titles=\nshow_intro=\nshow_section=\nlink_section=\nshow_category=\nlink_category=\nshow_author=\nshow_create_date=\nshow_modify_date=\nshow_item_navigation=\nshow_readmore=\nshow_vote=\nshow_icons=\nshow_pdf_icon=\nshow_print_icon=\nshow_email_icon=\nshow_hits=\nfeed_summary=\npage_title=\nshow_page_title=1\npageclass_sfx=\nmenu_image=-1\nsecure=0\n\n', 0, 0, 0),
															(52, 'usermenu', 'Submit a Web Link', 'submit-a-web-link', 'index.php?option=com_weblinks&view=weblink&layout=form', 'component', -2, 0, 4, 0, 2, 0, '0000-00-00 00:00:00', 0, 0, 2, 0, '', 0, 0, 0),
															(53, 'browse-by-games', 'World of Warcraft', 'world-of-warcraft', 'index.php?option=com_content&view=section&id=5', 'component', 1, 0, 20, 0, 2, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'show_description=1\nshow_description_image=1\nshow_categories=1\nshow_empty_categories=1\nshow_cat_num_articles=1\nshow_category_description=1\norderby=\norderby_sec=\nshow_feed_link=1\nshow_noauth=\nshow_title=\nlink_titles=\nshow_intro=\nshow_section=\nlink_section=\nshow_category=\nlink_category=\nshow_author=\nshow_create_date=\nshow_modify_date=\nshow_item_navigation=\nshow_readmore=\nshow_vote=\nshow_icons=\nshow_pdf_icon=\nshow_print_icon=\nshow_email_icon=\nshow_hits=\nfeed_summary=\npage_title=\nshow_page_title=1\npageclass_sfx=\nmenu_image=-1\nsecure=0\n\n', 0, 0, 0),
															(55, 'contribute', 'Tell us what you need', 'tell-us-what-you-need', 'index.php?option=com_content&view=article&id=66', 'component', 1, 0, 20, 0, 2, 0, '0000-00-00 00:00:00', 0, 0, 0, 0, 'show_noauth=\nshow_title=\nlink_titles=\nshow_intro=\nshow_section=\nlink_section=\nshow_category=\nlink_category=\nshow_author=\nshow_create_date=\nshow_modify_date=\nshow_item_navigation=\nshow_readmore=\nshow_vote=\nshow_icons=\nshow_pdf_icon=\nshow_print_icon=\nshow_email_icon=\nshow_hits=\nfeed_summary=\npage_title=\nshow_page_title=1\npageclass_sfx=\nmenu_image=-1\nsecure=0\n\n', 0, 0, 0);"
										);

$add_new_tables["jos_menu_types"] = array (	"structure" => "CREATE TABLE  `jos_menu_types` (
															  `id` int(10) unsigned NOT NULL auto_increment,
															  `menutype` varchar(75) NOT NULL default '',
															  `title` varchar(255) NOT NULL default '',
															  `description` varchar(255) NOT NULL default '',
															  PRIMARY KEY  (`id`),
															  UNIQUE KEY `menutype` (`menutype`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=9 ;" ,
											"data" => "INSERT INTO `jos_menu_types` (`id`, `menutype`, `title`, `description`) 
														VALUES	(1, 'mainmenu', 'Main Menu', 'The main menu for the site'),
															(2, 'usermenu', 'User Menu', 'A Menu for logged in Users'),
															(8, 'contribute', 'Contribute', 'contribute'),
															(7, 'browse-by-games', 'Browse by Games', 'games');"
										);

$add_new_tables["jos_messages"] = array (	"structure" => "CREATE TABLE  `jos_messages` (
															  `message_id` int(10) unsigned NOT NULL auto_increment,
															  `user_id_from` int(10) unsigned NOT NULL default '0',
															  `user_id_to` int(10) unsigned NOT NULL default '0',
															  `folder_id` int(10) unsigned NOT NULL default '0',
															  `date_time` datetime NOT NULL default '0000-00-00 00:00:00',
															  `state` int(11) NOT NULL default '0',
															  `priority` int(1) unsigned NOT NULL default '0',
															  `subject` text NOT NULL,
															  `message` text NOT NULL,
															  PRIMARY KEY  (`message_id`),
															  KEY `useridto_state` (`user_id_to`,`state`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;" ,
											"data" => ""
										);

$add_new_tables["jos_messages_cfg"] = array (	"structure" => "CREATE TABLE  `jos_messages_cfg` (
																  `user_id` int(10) unsigned NOT NULL default '0',
																  `cfg_name` varchar(100) NOT NULL default '',
																  `cfg_value` varchar(255) NOT NULL default '',
																  UNIQUE KEY `idx_user_var_name` (`user_id`,`cfg_name`)
																) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
												"data" => ""
											);
										
$add_new_tables["jos_migration_backlinks"] = array (	"structure" => "CREATE TABLE  `jos_migration_backlinks` (
																		  `itemid` int(11) NOT NULL default '0',
																		  `name` varchar(100) NOT NULL default '',
																		  `url` text NOT NULL,
																		  `sefurl` text NOT NULL,
																		  `newurl` text NOT NULL,
																		  PRIMARY KEY  (`itemid`)
																		) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
														"data" => ""
													);

$add_new_tables["jos_modules"] = array (	"structure" => "CREATE TABLE  `jos_modules` (
															  `id` int(11) NOT NULL auto_increment,
															  `title` text NOT NULL,
															  `content` text NOT NULL,
															  `ordering` int(11) NOT NULL default '0',
															  `position` varchar(50) default NULL,
															  `checked_out` int(11) unsigned NOT NULL default '0',
															  `checked_out_time` datetime NOT NULL default '0000-00-00 00:00:00',
															  `published` tinyint(1) NOT NULL default '0',
															  `module` varchar(50) default NULL,
															  `numnews` int(11) NOT NULL default '0',
															  `access` tinyint(3) unsigned NOT NULL default '0',
															  `showtitle` tinyint(3) unsigned NOT NULL default '1',
															  `params` text NOT NULL,
															  `iscore` tinyint(4) NOT NULL default '0',
															  `client_id` tinyint(4) NOT NULL default '0',
															  `control` text NOT NULL,
															  PRIMARY KEY  (`id`),
															  KEY `published` (`published`,`access`),
															  KEY `newsfeeds` (`module`,`published`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=52 ;" ,
											"data" => "INSERT INTO `jos_modules` (`id`, `title`, `content`, `ordering`, `position`, `checked_out`, `checked_out_time`, `published`, `module`, `numnews`, `access`, `showtitle`, `params`, `iscore`, `client_id`, `control`) VALUES
														(1, 'Main Menu', '', 3, 'left', 0, '0000-00-00 00:00:00', 0, 'mod_mainmenu', 0, 0, 1, 'menutype=mainmenu\nmoduleclass_sfx=_menu\n', 1, 0, ''),
														(2, 'Login', '', 1, 'login', 0, '0000-00-00 00:00:00', 1, 'mod_login', 0, 0, 1, '', 1, 1, ''),
														(3, 'Popular', '', 3, 'cpanel', 0, '0000-00-00 00:00:00', 1, 'mod_popular', 0, 2, 1, '', 0, 1, ''),
														(4, 'Recent added Articles', '', 4, 'cpanel', 0, '0000-00-00 00:00:00', 1, 'mod_latest', 0, 2, 1, 'ordering=c_dsc\nuser_id=0\ncache=0\n\n', 0, 1, ''),
														(5, 'Menu Stats', '', 5, 'cpanel', 0, '0000-00-00 00:00:00', 1, 'mod_stats', 0, 2, 1, '', 0, 1, ''),
														(6, 'Unread Messages', '', 1, 'header', 0, '0000-00-00 00:00:00', 1, 'mod_unread', 0, 2, 1, '', 1, 1, ''),
														(7, 'Online Users', '', 2, 'header', 0, '0000-00-00 00:00:00', 1, 'mod_online', 0, 2, 1, '', 1, 1, ''),
														(8, 'Toolbar', '', 1, 'toolbar', 0, '0000-00-00 00:00:00', 1, 'mod_toolbar', 0, 2, 1, '', 1, 1, ''),
														(9, 'Quick Icons', '', 1, 'icon', 0, '0000-00-00 00:00:00', 1, 'mod_quickicon', 0, 2, 1, '', 1, 1, ''),
														(10, 'Logged in Users', '', 2, 'cpanel', 0, '0000-00-00 00:00:00', 1, 'mod_logged', 0, 2, 1, '', 0, 1, ''),
														(11, 'Footer', '', 0, 'footer', 0, '0000-00-00 00:00:00', 1, 'mod_footer', 0, 0, 1, '', 1, 1, ''),
														(12, 'Admin Menu', '', 1, 'menu', 0, '0000-00-00 00:00:00', 1, 'mod_menu', 0, 2, 1, '', 0, 1, ''),
														(13, 'Admin SubMenu', '', 1, 'submenu', 0, '0000-00-00 00:00:00', 1, 'mod_submenu', 0, 2, 1, '', 0, 1, ''),
														(14, 'User Status', '', 1, 'status', 0, '0000-00-00 00:00:00', 1, 'mod_status', 0, 2, 1, '', 0, 1, ''),
														(15, 'Title', '', 1, 'title', 0, '0000-00-00 00:00:00', 1, 'mod_title', 0, 2, 1, '', 0, 1, ''),
														(17, 'User Menu', '', 5, 'left', 0, '0000-00-00 00:00:00', 1, 'mod_mainmenu', 0, 1, 1, 'menutype=usermenu\nmoduleclass_sfx=_menu\ncache=1', 1, 0, ''),
														(18, 'Login Form', '', 1, 'left', 62, '2009-01-07 02:27:36', 1, 'mod_login', 0, 0, 1, 'cache=0\nmoduleclass_sfx=\npretext=\nposttext=\nlogin=\nlogout=\ngreeting=1\nname=0\nusesecure=0\n\n', 1, 0, ''),
														(51, 'WE''RE SORRY', '<div align=\"center\"> </div><div align=\"center\"><font color=\"#ff0000\"><strong>Coming Soon!</strong></font></div><div align=\"center\"> </div><div align=\"center\">If you have guide that you need urgently, send an <NAME_EMAIL> <br /></div>', 2, 'footer', 0, '0000-00-00 00:00:00', 1, 'mod_custom', 0, 0, 1, 'moduleclass_sfx=\n\n', 0, 0, ''),
														(25, 'Newsflash', '', 1, 'top', 0, '0000-00-00 00:00:00', 1, 'mod_newsflash', 0, 0, 1, 'catid=3\r\nstyle=random\r\nitems=\r\nmoduleclass_sfx=', 0, 0, ''),
														(27, 'Search', '', 1, 'user4', 0, '0000-00-00 00:00:00', 0, 'mod_search', 0, 0, 0, 'cache=1', 0, 0, ''),
														(47, 'Breadcrumb', '', 0, 'user4', 0, '0000-00-00 00:00:00', 1, 'mod_breadcrumbs', 0, 0, 1, 'showHome=1\nhomeText=Home\nshowLast=1\nseparator=\nmoduleclass_sfx=\ncache=0\n\n', 0, 0, ''),
														(48, 'WE''RE SORRY', '<p align=\"center\">&nbsp;</p><p align=\"center\"><font color=\"#ff0000\"><strong>Coming Soon.</strong></font></p><p align=\"center\">If you have a guide that you would like to submit, please do email your <NAME_EMAIL> </p>', 0, 'footer', 0, '0000-00-00 00:00:00', 1, 'mod_custom', 0, 0, 1, 'moduleclass_sfx=\n\n', 0, 0, ''),
														(44, 'Suggested World of Warcraft Guides', '', 0, 'footer', 0, '0000-00-00 00:00:00', 1, 'mod_mostread', 0, 0, 1, 'moduleclass_sfx=\nshow_front=1\ncount=5\ncatid=\nsecid=5\ncache=1\ncache_time=900\n\n', 0, 0, ''),
														(45, 'Language Selection', '', 0, 'user4', 0, '0000-00-00 00:00:00', 0, 'mod_jflanguageselection', 0, 0, 0, 'type=rawimages\nshow_active=1\ninc_jf_css=1\nmoduleclass_sfx=\ncache_href=1\n\n', 0, 0, ''),
														(46, 'Direct Translation', '', 0, 'status', 0, '0000-00-00 00:00:00', 1, 'mod_translate', 0, 2, 0, '', 0, 1, ''),
														(36, 'Syndication', '', 3, 'syndicate', 0, '0000-00-00 00:00:00', 1, 'mod_syndicate', 0, 0, 0, '', 1, 0, ''),
														(49, 'Contribute', '', 5, 'left', 0, '0000-00-00 00:00:00', 1, 'mod_mainmenu', 0, 0, 1, 'menutype=contribute\nmenu_style=list\nstartLevel=0\nendLevel=0\nshowAllChildren=0\nwindow_open=\nshow_whitespace=0\ncache=1\ntag_id=\nclass_sfx=\nmoduleclass_sfx=\nmaxdepth=10\nmenu_images=0\nmenu_images_align=0\nmenu_images_link=0\nexpand_menu=0\nactivate_parent=0\nfull_active_id=0\nindent_image=0\nindent_image1=\nindent_image2=\nindent_image3=\nindent_image4=\nindent_image5=\nindent_image6=\nspacer=\nend_spacer=\n\n', 0, 0, ''),
														(50, 'SUGGESTED GUIDES', '', 1, 'footer', 0, '0000-00-00 00:00:00', 1, 'mod_mostread', 0, 0, 1, 'moduleclass_sfx=\nshow_front=1\ncount=5\ncatid=\nsecid=\ncache=1\ncache_time=900\n\n', 0, 0, ''),
														(41, 'Welcome to Joomla!', '<div style=\"padding: 5px\">  <p>   Congratulations on choosing Joomla! as your content management system. To   help you get started, check out these excellent resources for securing your   server and pointers to documentation and other helpful resources. </p> <p>   <strong>Security</strong><br /> </p> <p>   On the Internet, security is always a concern. For that reason, you are   encouraged to subscribe to the   <a href=\"http://feedburner.google.com/fb/a/mailverify?uri=JoomlaSecurityNews\" target=\"_blank\">Joomla!   Security Announcements</a> for the latest information on new Joomla! releases,   emailed to you automatically. </p> <p>   If this is one of your first Web sites, security considerations may   seem complicated and intimidating. There are three simple steps that go a long   way towards securing a Web site: (1) regular backups; (2) prompt updates to the   <a href=\"http://www.joomla.org/download.html\" target=\"_blank\">latest Joomla! release;</a> and (3) a <a href=\"http://docs.joomla.org/Security_Checklist_2_-_Hosting_and_Server_Setup\" target=\"_blank\" title=\"good Web host\">good Web host</a>. There are many other important security considerations that you can learn about by reading the <a href=\"http://docs.joomla.org/Category:Security_Checklist\" target=\"_blank\" title=\"Joomla! Security Checklist\">Joomla! Security Checklist</a>. </p> <p>If you believe your Web site was attacked, or you think you have discovered a security issue in Joomla!, please do not post it in the Joomla! forums. Publishing this information could put other Web sites at risk. Instead, report possible security vulnerabilities to the <a href=\"http://developer.joomla.org/security/contact-the-team.html\" target=\"_blank\" title=\"Joomla! Security Task Force\">Joomla! Security Task Force</a>.</p><p><strong>Learning Joomla!</strong> </p> <p>   A good place to start learning Joomla! is the   \"<a href=\"http://docs.joomla.org/beginners\" target=\"_blank\">Absolute Beginner''s   Guide to Joomla!.</a>\" There, you will find a Quick Start to Joomla!   <a href=\"http://help.joomla.org/ghop/feb2008/task048/joomla_15_quickstart.pdf\" target=\"_blank\">guide</a>   and <a href=\"http://help.joomla.org/ghop/feb2008/task167/index.html\" target=\"_blank\">video</a>,   amongst many other tutorials. The   <a href=\"http://community.joomla.org/magazine/view-all-issues.html\" target=\"_blank\">Joomla!   Community Magazine</a> also has   <a href=\"http://community.joomla.org/magazine/article/522-introductory-learning-joomla-using-sample-data.html\" target=\"_blank\">articles   for new learners</a> and experienced users, alike. A great place to look for   answers is the   <a href=\"http://docs.joomla.org/Category:FAQ\" target=\"_blank\">Frequently Asked   Questions (FAQ)</a>. If you are stuck on a particular screen in the   Administrator (which is where you are now), try clicking the Help toolbar   button to get assistance specific to that page. </p> <p>   If you still have questions, please feel free to use the   <a href=\"http://forum.joomla.org/\" target=\"_blank\">Joomla! Forums.</a> The forums   are an incredibly valuable resource for all levels of Joomla! users. Before   you post a question, though, use the forum search (located at the top of each   forum page) to see if the question has been asked and answered. </p> <p>   <strong>Getting Involved</strong> </p> <p>   <a name=\"twjs\" title=\"twjs\"></a> If you want to help make Joomla! better, consider getting   involved. There are   <a href=\"http://www.joomla.org/about-joomla/contribute-to-joomla.html\" target=\"_blank\">many ways   you can make a positive difference.</a> Have fun using Joomla!.</p></div>', 0, 'cpanel', 0, '0000-00-00 00:00:00', 1, 'mod_custom', 0, 2, 1, 'moduleclass_sfx=\n\n', 1, 1, ''),
														(42, 'Joomla! Security Newsfeed', '', 6, 'cpanel', 62, '2008-10-25 20:15:17', 1, 'mod_feed', 0, 0, 1, 'cache=1\ncache_time=15\nmoduleclass_sfx=\nrssurl=http://feeds.joomla.org/JoomlaSecurityNews\nrssrtl=0\nrsstitle=1\nrssdesc=0\nrssimage=1\nrssitems=1\nrssitemdesc=1\nword_count=0\n\n', 0, 1, ''),
														(43, 'Browse by Games', '', 4, 'left', 0, '0000-00-00 00:00:00', 1, 'mod_mainmenu', 0, 0, 1, 'menutype=browse-by-games\nmenu_style=list\nstartLevel=0\nendLevel=0\nshowAllChildren=0\nwindow_open=\nshow_whitespace=0\ncache=1\ntag_id=\nclass_sfx=\nmoduleclass_sfx=\nmaxdepth=10\nmenu_images=0\nmenu_images_align=0\nmenu_images_link=0\nexpand_menu=0\nactivate_parent=0\nfull_active_id=0\nindent_image=0\nindent_image1=\nindent_image2=\nindent_image3=\nindent_image4=\nindent_image5=\nindent_image6=\nspacer=\nend_spacer=\n\n', 0, 0, '');"
										);

$add_new_tables["jos_modules_menu"] = array (	"structure" => "CREATE TABLE  `jos_modules_menu` (
																  `moduleid` int(11) NOT NULL default '0',
																  `menuid` int(11) NOT NULL default '0',
																  PRIMARY KEY  (`moduleid`,`menuid`)
																) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
												"data" => "INSERT INTO `jos_modules_menu` (`moduleid`, `menuid`) VALUES
															(1, 0),
															(17, 0),
															(18, 0),
															(25, 0),
															(27, 0),
															(29, 0),
															(36, 0),
															(39, 43),
															(39, 44),
															(39, 45),
															(39, 46),
															(39, 47),
															(43, 0),
															(44, 53),
															(45, 0),
															(47, 0),
															(48, 54),
															(49, 0),
															(50, 1),
															(51, 55);"
											);

$add_new_tables["jos_newsfeeds"] = array (	"structure" => "CREATE TABLE  `jos_newsfeeds` (
															  `catid` int(11) NOT NULL default '0',
															  `id` int(11) NOT NULL auto_increment,
															  `name` text NOT NULL,
															  `alias` varchar(255) NOT NULL default '',
															  `link` text NOT NULL,
															  `filename` varchar(200) default NULL,
															  `published` tinyint(1) NOT NULL default '0',
															  `numarticles` int(11) unsigned NOT NULL default '1',
															  `cache_time` int(11) unsigned NOT NULL default '3600',
															  `checked_out` tinyint(3) unsigned NOT NULL default '0',
															  `checked_out_time` datetime NOT NULL default '0000-00-00 00:00:00',
															  `ordering` int(11) NOT NULL default '0',
															  `rtl` tinyint(4) NOT NULL default '0',
															  PRIMARY KEY  (`id`),
															  KEY `published` (`published`),
															  KEY `catid` (`catid`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=15 ;" ,
												"data" => "INSERT INTO `jos_newsfeeds` (`catid`, `id`, `name`, `alias`, `link`, `filename`, `published`, `numarticles`, `cache_time`, `checked_out`, `checked_out_time`, `ordering`, `rtl`) VALUES
																(4, 1, 'Joomla! Announcements', 'joomla-official-news', 'http://feeds.joomla.org/JoomlaAnnouncements', '', 1, 5, 3600, 0, '0000-00-00 00:00:00', 1, 0),
																(4, 2, 'Joomla! Core Team Blog', 'joomla-core-team-blog', 'http://feeds.joomla.org/JoomlaCommunityCoreTeamBlog', '', 1, 5, 3600, 0, '0000-00-00 00:00:00', 2, 0),
																(4, 3, 'Joomla! Community Magazine', 'joomla-community-magazine', 'http://feeds.joomla.org/JoomlaMagazine', '', 1, 20, 3600, 0, '0000-00-00 00:00:00', 3, 0),
																(4, 4, 'Joomla! Developer News', 'joomla-developer-news', 'http://feeds.joomla.org/JoomlaDeveloper', '', 1, 5, 3600, 0, '0000-00-00 00:00:00', 4, 0),
																(4, 5, 'Joomla! Security News', 'joomla-security-news', 'http://feeds.joomla.org/JoomlaSecurityNews', '', 1, 5, 3600, 0, '0000-00-00 00:00:00', 5, 0),
																(5, 6, 'Free Software Foundation Blogs', 'free-software-foundation-blogs', 'http://www.fsf.org/blogs/RSS', NULL, 1, 5, 3600, 0, '0000-00-00 00:00:00', 4, 0),
																(5, 7, 'Free Software Foundation', 'free-software-foundation', 'http://www.fsf.org/news/RSS', NULL, 1, 5, 3600, 62, '2008-09-14 00:24:25', 3, 0),
																(5, 8, 'Software Freedom Law Center Blog', 'software-freedom-law-center-blog', 'http://www.softwarefreedom.org/feeds/blog/', NULL, 1, 5, 3600, 0, '0000-00-00 00:00:00', 2, 0),
																(5, 9, 'Software Freedom Law Center News', 'software-freedom-law-center', 'http://www.softwarefreedom.org/feeds/news/', NULL, 1, 5, 3600, 0, '0000-00-00 00:00:00', 1, 0),
																(5, 10, 'Open Source Initiative Blog', 'open-source-initiative-blog', 'http://www.opensource.org/blog/feed', NULL, 1, 5, 3600, 0, '0000-00-00 00:00:00', 5, 0),
																(6, 11, 'PHP News and Announcements', 'php-news-and-announcements', 'http://www.php.net/feed.atom', NULL, 1, 5, 3600, 62, '2008-09-14 00:25:37', 1, 0),
																(6, 12, 'Planet MySQL', 'planet-mysql', 'http://www.planetmysql.org/rss20.xml', NULL, 1, 5, 3600, 62, '2008-09-14 00:25:51', 2, 0),
																(6, 13, 'Linux Foundation Announcements', 'linux-foundation-announcements', 'http://www.linuxfoundation.org/press/rss20.xml', NULL, 1, 5, 3600, 62, '2008-09-14 00:26:11', 3, 0),
																(6, 14, 'Mootools Blog', 'mootools-blog', 'http://feeds.feedburner.com/mootools-blog', NULL, 1, 5, 3600, 62, '2008-09-14 00:26:51', 4, 0);"
											);

$add_new_tables["jos_plugins"] = array (	"structure" => "CREATE TABLE  `jos_plugins` (
															  `id` int(11) NOT NULL auto_increment,
															  `name` varchar(100) NOT NULL default '',
															  `element` varchar(100) NOT NULL default '',
															  `folder` varchar(100) NOT NULL default '',
															  `access` tinyint(3) unsigned NOT NULL default '0',
															  `ordering` int(11) NOT NULL default '0',
															  `published` tinyint(3) NOT NULL default '0',
															  `iscore` tinyint(3) NOT NULL default '0',
															  `client_id` tinyint(3) NOT NULL default '0',
															  `checked_out` int(11) unsigned NOT NULL default '0',
															  `checked_out_time` datetime NOT NULL default '0000-00-00 00:00:00',
															  `params` text NOT NULL,
															  PRIMARY KEY  (`id`),
															  KEY `idx_folder` (`published`,`client_id`,`access`,`folder`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=44 ;" ,
												"data" => "INSERT INTO `jos_plugins` (`id`, `name`, `element`, `folder`, `access`, `ordering`, `published`, `iscore`, `client_id`, `checked_out`, `checked_out_time`, `params`) VALUES
																(1, 'Authentication - Joomla', 'joomla', 'authentication', 0, 1, 1, 1, 0, 0, '0000-00-00 00:00:00', ''),
																(2, 'Authentication - LDAP', 'ldap', 'authentication', 0, 2, 0, 1, 0, 0, '0000-00-00 00:00:00', 'host=\nport=389\nuse_ldapV3=0\nnegotiate_tls=0\nno_referrals=0\nauth_method=bind\nbase_dn=\nsearch_string=\nusers_dn=\nusername=\npassword=\nldap_fullname=fullName\nldap_email=mail\nldap_uid=uid\n\n'),
																(3, 'Authentication - GMail', 'gmail', 'authentication', 0, 4, 0, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(4, 'Authentication - OpenID', 'openid', 'authentication', 0, 3, 0, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(5, 'User - Joomla!', 'joomla', 'user', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', 'autoregister=1\n\n'),
																(6, 'Search - Content', 'content', 'search', 0, 1, 1, 1, 0, 0, '0000-00-00 00:00:00', 'search_limit=50\nsearch_content=1\nsearch_uncategorised=1\nsearch_archived=1\n\n'),
																(7, 'Search - Contacts', 'contacts', 'search', 0, 3, 1, 1, 0, 0, '0000-00-00 00:00:00', 'search_limit=50\n\n'),
																(8, 'Search - Categories', 'categories', 'search', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', 'search_limit=50\n\n'),
																(9, 'Search - Sections', 'sections', 'search', 0, 5, 1, 0, 0, 0, '0000-00-00 00:00:00', 'search_limit=50\n\n'),
																(10, 'Search - Newsfeeds', 'newsfeeds', 'search', 0, 6, 1, 0, 0, 0, '0000-00-00 00:00:00', 'search_limit=50\n\n'),
																(11, 'Search - Weblinks', 'weblinks', 'search', 0, 2, 1, 1, 0, 0, '0000-00-00 00:00:00', 'search_limit=50\n\n'),
																(12, 'Content - Pagebreak', 'pagebreak', 'content', 0, 10000, 1, 1, 0, 0, '0000-00-00 00:00:00', 'enabled=1\ntitle=1\nmultipage_toc=1\nshowall=1\n\n'),
																(13, 'Content - Rating', 'vote', 'content', 0, 4, 1, 1, 0, 0, '0000-00-00 00:00:00', ''),
																(14, 'Content - Email Cloaking', 'emailcloak', 'content', 0, 5, 1, 0, 0, 0, '0000-00-00 00:00:00', 'mode=1\n\n'),
																(15, 'Content - Code Hightlighter (GeSHi)', 'geshi', 'content', 0, 5, 0, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(16, 'Content - Load Module', 'loadmodule', 'content', 0, 6, 1, 0, 0, 0, '0000-00-00 00:00:00', 'enabled=1\nstyle=0\n\n'),
																(17, 'Content - Page Navigation', 'pagenavigation', 'content', 0, 2, 1, 1, 0, 0, '0000-00-00 00:00:00', 'position=1\n\n'),
																(18, 'Editor - No Editor', 'none', 'editors', 0, 0, 1, 1, 0, 0, '0000-00-00 00:00:00', ''),
																(19, 'Editor - TinyMCE 2.0', 'tinymce', 'editors', 0, 0, 1, 1, 0, 0, '0000-00-00 00:00:00', 'theme=advanced\ncleanup=1\ncleanup_startup=0\nautosave=0\ncompressed=0\nrelative_urls=1\ntext_direction=ltr\nlang_mode=0\nlang_code=en\ninvalid_elements=applet\ncontent_css=1\ncontent_css_custom=\nnewlines=0\ntoolbar=top\nhr=1\nsmilies=1\ntable=1\nstyle=1\nlayer=1\nxhtmlxtras=0\ntemplate=0\ndirectionality=1\nfullscreen=1\nhtml_height=550\nhtml_width=750\npreview=1\ninsertdate=1\nformat_date=%Y-%m-%d\ninserttime=1\nformat_time=%H:%M:%S\n\n'),
																(20, 'Editor - XStandard Lite 2.0', 'xstandard', 'editors', 0, 0, 0, 1, 0, 0, '0000-00-00 00:00:00', ''),
																(21, 'Editor Button - Image', 'image', 'editors-xtd', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(22, 'Editor Button - Pagebreak', 'pagebreak', 'editors-xtd', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(23, 'Editor Button - Readmore', 'readmore', 'editors-xtd', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(24, 'XML-RPC - Joomla', 'joomla', 'xmlrpc', 0, 7, 0, 1, 0, 0, '0000-00-00 00:00:00', ''),
																(25, 'XML-RPC - Blogger API', 'blogger', 'xmlrpc', 0, 7, 0, 1, 0, 0, '0000-00-00 00:00:00', 'catid=1\nsectionid=0\n\n'),
																(27, 'System - SEF', 'sef', 'system', 0, 1, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(28, 'System - Debug', 'debug', 'system', 0, 2, 1, 0, 0, 0, '0000-00-00 00:00:00', 'queries=1\nmemory=1\nlangauge=1\n\n'),
																(29, 'System - Legacy', 'legacy', 'system', 0, 3, 0, 1, 0, 0, '0000-00-00 00:00:00', 'route=0\n\n'),
																(30, 'System - Cache', 'cache', 'system', 0, 4, 0, 1, 0, 0, '0000-00-00 00:00:00', 'browsercache=0\ncachetime=15\n\n'),
																(31, 'System - Log', 'log', 'system', 0, 5, 0, 1, 0, 0, '0000-00-00 00:00:00', ''),
																(32, 'System - Remember Me', 'remember', 'system', 0, 6, 1, 1, 0, 0, '0000-00-00 00:00:00', ''),
																(33, 'System - Backlink', 'backlink', 'system', 0, 7, 0, 1, 0, 0, '0000-00-00 00:00:00', ''),
																(34, 'System - Jfdatabase', 'jfdatabase', 'system', 0, -100, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(35, 'System - Jfrouter', 'jfrouter', 'system', 0, -101, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(36, 'Content - Jfalternative', 'jfalternative', 'content', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(37, 'Search - Jfcategories', 'jfcategories', 'search', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(38, 'Search - Jfcontacts', 'jfcontacts', 'search', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(39, 'Search - Jfcontent', 'jfcontent', 'search', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(40, 'Search - Jfnewsfeeds', 'jfnewsfeeds', 'search', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(41, 'Search - Jfsections', 'jfsections', 'search', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(42, 'Search - Jfweblinks', 'jfweblinks', 'search', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', ''),
																(43, 'Joomfish - Missing_translation', 'missing_translation', 'joomfish', 0, 0, 1, 0, 0, 0, '0000-00-00 00:00:00', '');"
											);

$add_new_tables["jos_polls"] = array (	"structure" => "CREATE TABLE  `jos_polls` (
														  `id` int(11) unsigned NOT NULL auto_increment,
														  `title` varchar(255) NOT NULL default '',
														  `alias` varchar(255) NOT NULL default '',
														  `voters` int(9) NOT NULL default '0',
														  `checked_out` int(11) NOT NULL default '0',
														  `checked_out_time` datetime NOT NULL default '0000-00-00 00:00:00',
														  `published` tinyint(1) NOT NULL default '0',
														  `access` int(11) NOT NULL default '0',
														  `lag` int(11) NOT NULL default '0',
														  PRIMARY KEY  (`id`)
														) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
										"data" => ""
									);

$add_new_tables["jos_poll_data"] = array (	"structure" => "CREATE TABLE  `jos_poll_data` (
															  `id` int(11) NOT NULL auto_increment,
															  `pollid` int(11) NOT NULL default '0',
															  `text` text NOT NULL,
															  `hits` int(11) NOT NULL default '0',
															  PRIMARY KEY  (`id`),
															  KEY `pollid` (`pollid`,`text`(1))
															) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
											"data" => ""
										);

$add_new_tables["jos_poll_date"] = array (	"structure" => "CREATE TABLE  `jos_poll_date` (
															  `id` bigint(20) NOT NULL auto_increment,
															  `date` datetime NOT NULL default '0000-00-00 00:00:00',
															  `vote_id` int(11) NOT NULL default '0',
															  `poll_id` int(11) NOT NULL default '0',
															  PRIMARY KEY  (`id`),
															  KEY `poll_id` (`poll_id`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
											"data" => ""
										);

$add_new_tables["jos_poll_menu"] = array (	"structure" => "CREATE TABLE  `jos_poll_menu` (
															  `pollid` int(11) NOT NULL default '0',
															  `menuid` int(11) NOT NULL default '0',
															  PRIMARY KEY  (`pollid`,`menuid`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
											"data" => ""
										);

$add_new_tables["jos_sections"] = array (	"structure" => "CREATE TABLE  `jos_sections` (
															  `id` int(11) NOT NULL auto_increment,
															  `title` varchar(255) NOT NULL default '',
															  `name` varchar(255) NOT NULL default '',
															  `alias` varchar(255) NOT NULL default '',
															  `image` text NOT NULL,
															  `scope` varchar(50) NOT NULL default '',
															  `image_position` varchar(30) NOT NULL default '',
															  `description` text NOT NULL,
															  `published` tinyint(1) NOT NULL default '0',
															  `checked_out` int(11) unsigned NOT NULL default '0',
															  `checked_out_time` datetime NOT NULL default '0000-00-00 00:00:00',
															  `ordering` int(11) NOT NULL default '0',
															  `access` tinyint(3) unsigned NOT NULL default '0',
															  `count` int(11) NOT NULL default '0',
															  `params` text NOT NULL,
															  PRIMARY KEY  (`id`),
															  KEY `idx_scope` (`scope`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=6 ;" ,
											"data" => "INSERT INTO `jos_sections` (`id`, `title`, `name`, `alias`, `image`, `scope`, `image_position`, `description`, `published`, `checked_out`, `checked_out_time`, `ordering`, `access`, `count`, `params`) VALUES
														(1, 'News', '', 'news', 'articles.jpg', 'content', 'right', 'Select a news topic from the list below, then select a news article to read.', 1, 0, '0000-00-00 00:00:00', 3, 0, 2, ''),
														(3, 'FAQs', '', 'faqs', 'key.jpg', 'content', 'left', 'From the list below choose one of our FAQs topics, then select an FAQ to read. If you have a question which is not in this section, please contact us.', 1, 0, '0000-00-00 00:00:00', 5, 0, 23, ''),
														(4, 'About Joomla!', '', 'about-joomla', '', 'content', 'left', '', 1, 0, '0000-00-00 00:00:00', 2, 0, 14, ''),
														(5, 'World of Warcraft', '', 'world-of-warcraft', '', 'content', 'center', '<hr /><div style=\"border: 1px solid #cecece; padding: 3px\"><p style=\"margin-bottom: 0in\"><img src=\"images/stories/wow%20title%20image1.jpg\" border=\"0\" alt=\"world-of-warcraft-game-guides\" title=\"World of Warcraft Game Guides\" hspace=\"10\" vspace=\"0\" width=\"213\" height=\"150\" align=\"left\" /></p><p style=\"margin-bottom: 0in\"> World of Warcraft explores the immense world of Azeroth and its mysteries. Hardy adventurers have risked life and limb to bring back fantastical stories of Azeroth and the mystical Outlands.</p> <p style=\"margin-bottom: 0in\">But this is not a time to relax your guard. As a new land was discovered. The land of the fearsome Lich King Notrhrend. Worry not as we have entered this new world and invited experienced adventurers to share in this section, stories of their achievements, strategies to bring down mighty monsters, lists of treasures to be found and secrets to becoming a master of your class and profession.</p> <p style=\"margin-bottom: 0in\">Do not hesitate to make full use of the resources here, we ask for not for payment but a promise that you will leave us with a story of your adventures.</p><p style=\"margin-bottom: 0in\">&nbsp;</p></div>', 1, 0, '0000-00-00 00:00:00', 6, 0, 29, '');"
									);

$add_new_tables["jos_session"] = array (	"structure" => "CREATE TABLE  `jos_session` (
															  `username` varchar(150) default '',
															  `time` varchar(14) default '',
															  `session_id` varchar(200) NOT NULL default '0',
															  `guest` tinyint(4) default '1',
															  `userid` int(11) default '0',
															  `usertype` varchar(50) default '',
															  `gid` tinyint(3) unsigned NOT NULL default '0',
															  `client_id` tinyint(3) unsigned NOT NULL default '0',
															  `data` longtext,
															  PRIMARY KEY  (`session_id`(64)),
															  KEY `whosonline` (`guest`,`usertype`),
															  KEY `userid` (`userid`),
															  KEY `time` (`time`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
											"data" => ""
									);

$add_new_tables["jos_stats_agents"] = array (	"structure" => "CREATE TABLE  `jos_stats_agents` (
																  `agent` varchar(255) NOT NULL default '',
																  `type` tinyint(1) unsigned NOT NULL default '0',
																  `hits` int(11) unsigned NOT NULL default '1'
																) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
												"data" => ""
										);

$add_new_tables["jos_templates_menu"] = array (	"structure" => "CREATE TABLE  `jos_templates_menu` (
																  `template` varchar(255) NOT NULL default '',
																  `menuid` int(11) NOT NULL default '0',
																  `client_id` tinyint(4) NOT NULL default '0',
																  PRIMARY KEY  (`menuid`,`client_id`,`template`)
																) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
												"data" => "INSERT INTO `jos_templates_menu` (`template`, `menuid`, `client_id`) VALUES
															('rhuk_milkyway', 0, 0),
															('khepri', 0, 1);"
										);

$add_new_tables["jos_users"] = array (	"structure" => "CREATE TABLE  `jos_users` (
														  `id` int(11) NOT NULL auto_increment,
														  `name` varchar(255) NOT NULL default '',
														  `username` varchar(150) NOT NULL default '',
														  `email` varchar(100) NOT NULL default '',
														  `password` varchar(100) NOT NULL default '',
														  `usertype` varchar(25) NOT NULL default '',
														  `block` tinyint(4) NOT NULL default '0',
														  `sendEmail` tinyint(4) default '0',
														  `gid` tinyint(3) unsigned NOT NULL default '1',
														  `registerDate` datetime NOT NULL default '0000-00-00 00:00:00',
														  `lastvisitDate` datetime NOT NULL default '0000-00-00 00:00:00',
														  `activation` varchar(100) NOT NULL default '',
														  `params` text NOT NULL,
														  `ogm_customers_id` int(11) NOT NULL default '0',
														  PRIMARY KEY  (`id`),
														  KEY `usertype` (`usertype`),
														  KEY `idx_name` (`name`),
														  KEY `gid_block` (`gid`,`block`),
														  KEY `username` (`username`),
														  KEY `email` (`email`)
														) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
										"data" => "INSERT INTO `jos_users` (`id`, `name`, `username`, `email`, `password`, `usertype`, `block`, `sendEmail`, `gid`, `registerDate`, `lastvisitDate`, `activation`, `params`, `ogm_customers_id`) 
													VALUES	(1, 'Michael', 'mike', '<EMAIL>', 'b2e2ed6c6337bbf2db5363d3f46fd63c:sD6exW0YYM31wRyvPCWkhnZpLdRTBwQy', 'Super Administrator', 0, 1, 25, '2008-12-30 05:15:26', '2009-01-16 10:57:33', '', '', 0);"
									);

$add_new_tables["jos_weblinks"] = array (	"structure" => "CREATE TABLE  `jos_weblinks` (
															  `id` int(11) unsigned NOT NULL auto_increment,
															  `catid` int(11) NOT NULL default '0',
															  `sid` int(11) NOT NULL default '0',
															  `title` varchar(250) NOT NULL default '',
															  `alias` varchar(255) NOT NULL default '',
															  `url` varchar(250) NOT NULL default '',
															  `description` text NOT NULL,
															  `date` datetime NOT NULL default '0000-00-00 00:00:00',
															  `hits` int(11) NOT NULL default '0',
															  `published` tinyint(1) NOT NULL default '0',
															  `checked_out` int(11) NOT NULL default '0',
															  `checked_out_time` datetime NOT NULL default '0000-00-00 00:00:00',
															  `ordering` int(11) NOT NULL default '0',
															  `archived` tinyint(1) NOT NULL default '0',
															  `approved` tinyint(1) NOT NULL default '1',
															  `params` text NOT NULL,
															  PRIMARY KEY  (`id`),
															  KEY `catid` (`catid`,`published`,`archived`)
															) TYPE=MyISAM DEFAULT CHARSET=utf8;" ,
											"data" => "INSERT INTO `jos_weblinks` (`id`, `catid`, `sid`, `title`, `alias`, `url`, `description`, `date`, `hits`, `published`, `checked_out`, `checked_out_time`, `ordering`, `archived`, `approved`, `params`) 
														VALUES (1, 2, 0, 'Joomla!', 'joomla', 'http://www.joomla.org', 'Home of Joomla!', '2005-02-14 15:19:02', 3, 1, 0, '0000-00-00 00:00:00', 1, 0, 1, 'target=0'),
														(2, 2, 0, 'php.net', 'php', 'http://www.php.net', 'The language that Joomla! is developed in', '2004-07-07 11:33:24', 6, 1, 0, '0000-00-00 00:00:00', 3, 0, 1, ''),
														(3, 2, 0, 'MySQL', 'mysql', 'http://www.mysql.com', 'The database that Joomla! uses', '2004-07-07 10:18:31', 1, 1, 0, '0000-00-00 00:00:00', 5, 0, 1, ''),
														(4, 2, 0, 'OpenSourceMatters', 'opensourcematters', 'http://www.opensourcematters.org', 'Home of OSM', '2005-02-14 15:19:02', 11, 1, 0, '0000-00-00 00:00:00', 2, 0, 1, 'target=0'),
														(5, 2, 0, 'Joomla! - Forums', 'joomla-forums', 'http://forum.joomla.org', 'Joomla! Forums', '2005-02-14 15:19:02', 4, 1, 0, '0000-00-00 00:00:00', 4, 0, 1, 'target=0'),
														(6, 2, 0, 'Ohloh Tracking of Joomla!', 'ohloh-tracking-of-joomla', 'http://www.ohloh.net/projects/20', 'Objective reports from Ohloh about Joomla''s development activity. Joomla! has some star developers with serious kudos.', '2007-07-19 09:28:31', 1, 1, 0, '0000-00-00 00:00:00', 6, 0, 1, 'target=0\n\n');"
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create search_products table

?>