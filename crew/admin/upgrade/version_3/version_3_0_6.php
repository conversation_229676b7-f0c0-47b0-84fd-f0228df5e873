<?
/*
	$Id: version_3_0_6.php,v 1.1 2009/01/21 09:36:55 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into categories_configuration table (for 0.00 Product Price notification)
$conf_insert_sql = array();

$conf_insert_sql["STOCK_ZERO_PRICE_PRODUCT_EMAIL"] = array("insert" => " (0, 'Zero Product Price Email Address', 'STOCK_ZERO_PRICE_PRODUCT_EMAIL', '', 'Email address to which the email will be send to whenever someone set 0.00 as active product price or turn on product with 0.00 price value.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 15, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into categories_configuration table (for 0.00 Product Price notification)
?>