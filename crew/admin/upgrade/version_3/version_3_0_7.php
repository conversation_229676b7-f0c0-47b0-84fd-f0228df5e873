<?
/*
	$Id: version_3_0_7.php,v 1.2 2009/02/09 07:49:08 weichen Exp $
	
  	Developer: <PERSON> (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new fields into maybank_payment_status_history table
$add_new_field = array();

$add_new_field['maybank_payment_status_history'] = array (	array (	"field_name" => "maybank_description",
																	"field_attr" => " varchar(64) default '' ",
																	"add_after" => ""
																	)
														);

add_field($add_new_field);
// End of insert new fields into maybank_payment_status_history table

// Create search_products table
$add_new_tables = array();

$add_new_tables["kuaiqian"] = array (	"structure" => "CREATE TABLE `kuaiqian` (
														  `orders_id` int(11) NOT NULL default '0',
														  `kuaiqian_merchant_acct_id` varchar(30) NOT NULL,
														  `kuaiqian_version` varchar(10) NOT NULL,
														  `kuaiqian_pay_type` varchar(2) NOT NULL,
														  `kuaiqian_bank_id` varchar(8) NOT NULL,
														  `kuaiqian_order_currency` varchar(3) NOT NULL default 'CNY',
														  `kuaiqian_order_amount` decimal(15,2) NOT NULL,
														  `kuaiqian_deal_id` varchar(30) NOT NULL,
														  `kuaiqian_bank_deal_id` varchar(30) NOT NULL,
														  `kuaiqian_deal_time` datetime NOT NULL,
														  `kuaiqian_pay_currency` varchar(3) NOT NULL default 'CNY',
														  `kuaiqian_pay_amount` decimal(15,2) NOT NULL,
														  `kuaiqian_fee` varchar(10) NOT NULL,
														  `kuaiqian_pay_result` varchar(2) NOT NULL,
														  `kuaiqian_err_code` varchar(10) NOT NULL,
														  `kuaiqian_signMsg` varchar(256) NOT NULL,
														  PRIMARY KEY  (`orders_id`),
														  KEY `index_deal_id` (`kuaiqian_deal_id`)
														) TYPE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["kuaiqian_payment_status_history"] = array (	"structure" => "CREATE TABLE `kuaiqian_payment_status_history` (
																				  `kuaiqian_payment_status_history_id` int(11) NOT NULL auto_increment,
																				  `orders_id` int(11) NOT NULL default '0',
																				  `kuaiqian_date` datetime NOT NULL default '0000-00-00 00:00:00',
																				  `kuaiqian_status` char(2) NOT NULL default '',
																				  `kuaiqian_description` varchar(64) default NULL,
																				  PRIMARY KEY  (`kuaiqian_payment_status_history_id`),
																				  KEY `index_orders_id` (`orders_id`)
																				) TYPE=MyISAM;",
																"data" => ""
															);

add_new_tables ($add_new_tables, $DBTables);
// End of create search_products table

// Insert Mandatory Address Info payment configuration setting
$pm_select_sql = "	SELECT pci.payment_methods_id, pci.payment_configuration_info_key, pm.payment_methods_title, pm.payment_methods_parent_id, pci.payment_configuration_info_sort_order 
					FROM payment_configuration_info as pci 
					INNER JOIN payment_methods as pm 
						ON pm.payment_methods_id = pci.payment_methods_id 
					WHERE pci.payment_configuration_info_key LIKE '%_CONFIRM_COMPLETE%'
					GROUP BY pci.payment_methods_id";
$pm_result_sql = tep_db_query($pm_select_sql);

$install_key_array = array();
while ($pm_row = tep_db_fetch_array($pm_result_sql)) {
	preg_match_all("/MODULE_PAYMENT_([A-Za-z0-9_ ]+)_CONFIRM_COMPLETE/", $pm_row['payment_configuration_info_key'], $matches);
	
	if (isset($matches[1][0])) {	
		$check_key_exist_sql = "SELECT payment_configuration_info_key 
								FROM payment_configuration_info
								WHERE payment_configuration_info_key = 'MODULE_PAYMENT_".$matches[1][0]."_MANDATORY_ADDRESS_FIELD'
									AND payment_methods_id = '".$pm_row['payment_methods_id']."'";
		$check_key_result_sql = tep_db_query($check_key_exist_sql);
		if (!tep_db_num_rows($check_key_result_sql)) {
			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>$pm_row['payment_methods_id'],
																'payment_configuration_info_title'=>'Require Address Information',
																'payment_configuration_info_key'=>"MODULE_PAYMENT_".$matches[1][0]."_MANDATORY_ADDRESS_FIELD",
																'payment_configuration_info_description'=>'Set address field as required info during checkout.',
																'payment_configuration_info_sort_order' => $pm_row['payment_configuration_info_sort_order'] + 50,
																'set_function'=>'tep_cfg_select_option(array(\'True\', \'False\'), ',
																'use_function'=>'',
																'date_added'=>'now()'
															),
											'desc' => array (	'payment_configuration_info_value'=>'False',
															 	'languages_id' => 1
															)
										);
		}
	}
}

foreach ($install_key_array as $data) {
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
	$payment_conf_id = tep_db_insert_id();

	$data['desc']['payment_configuration_info_id'] = (int)$payment_conf_id;
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
}
// End of insert Mandatory Address Info payment configuration setting

?>