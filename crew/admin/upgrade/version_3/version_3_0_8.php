<?
/*
	$Id: version_3_0_8.php,v 1.1 2009/02/11 04:53:43 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define code as index key in currencies table
add_index_key ('currencies', 'index_code', 'index', 'code', $DBTables);
// End of define code as index key in currencies table

// Define payment_methods_id as index key in payment_configuration_info table
add_index_key ('payment_configuration_info', 'index_payment_methods_id', 'index', 'payment_methods_id', $DBTables);
// End of define payment_methods_id as index key in payment_configuration_info table

// Define payment_methods_parent_id, payment_methods_receive_status as index key in payment_methods table
add_index_key ('payment_methods', 'index_parent_id', 'index', 'payment_methods_parent_id', $DBTables);
add_index_key ('payment_methods', 'index_receive_status', 'index', 'payment_methods_receive_status', $DBTables);
// End of define payment_methods_parent_id, payment_methods_receive_status as index key in payment_methods table

// Insert new records into jos_modules table (for mod_janalytics)
$jos_module_insert_sql = array();

$jos_module_insert_sql["mod_janalytics"] = array("insert" => " ('J!Analytics', '', 0, 'user4', 0, '0000-00-00 00:00:00', 1, 'mod_janalytics', 0, 0, 0, 'analyticsCode=ga\nanalyticsId=UA-123456-5\nanalyticsSubdomainTracking=example.com\nanalyticsGatFix=off\n\n', 0, 0, '')" );

insert_new_records('jos_modules', "module", $jos_module_insert_sql, $DBTables, "(`title`, `content`, `ordering`, `position`, `checked_out`, `checked_out_time`, `published`, `module`, `numnews`, `access`, `showtitle`, `params`, `iscore`, `client_id`, `control`)");
// End of insert new records into jos_modules table (for mod_janalytics)
?>