<?
/*
	$Id: version_3_0_9.php,v 1.3 2009/02/17 07:20:59 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on exporting latest permissions list)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='admin_members.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ADMIN_EXPORT_PERMISSION"] = array("insert" => " ('ADMIN_EXPORT_PERMISSION', 'Export Permissions List', ".$row_sql["admin_files_id"].", '1', 50)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on exporting latest permissions list)

// Create search_products table
$add_new_tables = array();

$add_new_tables["mobile_money"] = array (	"structure" => "CREATE TABLE `mobile_money` (
															  `orders_id` int(11) NOT NULL default '0',
															  `mobile_money_tran_id` varchar(20) NOT NULL default '',
															  `mobile_money_tran_status` char(2) NOT NULL default '',
															  `mobile_money_tran_errcode` varchar(10) NOT NULL default '',
															  `mobile_money_amt` double(15,2) NOT NULL default '0.00',
															  `mobile_money_currency` varchar(3) default NULL,
															  `mobile_money_ecash_apprcode` varchar(10) NOT NULL default '',
															  `mobile_money_tran_mmprocessdt` varchar(22) NOT NULL default '',
															  PRIMARY KEY  (`orders_id`),
															  KEY `index_tran_id` (`mobile_money_tran_id`)
															) TYPE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["mobile_money_history"] = array (	"structure" => "CREATE TABLE `mobile_money_history` (
																	  `mobile_money_history_id` int(11) NOT NULL auto_increment,
																	  `orders_id` int(11) NOT NULL default '0',
																	  `money_money_request_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `mobile_money_tran_status` char(2) NOT NULL default '',
																	  `mobile_money_tran_description` varchar(255) default NULL,
																	  PRIMARY KEY  (`mobile_money_history_id`),
																	  KEY `index_orders_id` (`orders_id`)
																	) ENGINE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create search_products table

// Define ogm_customers_id as index key in jos_users table
add_index_key ('jos_users', 'index_ogm_customers_id', 'index', 'ogm_customers_id', $DBTables);
// End of define ogm_customers_id as index key in jos_users table

// Define payment_methods_instance_id, payment_methods_instance_setting_key as index key in payment_methods_instance_setting table
add_index_key ('payment_methods_instance_setting', 'index_instance_id_and_key', 'index', 'payment_methods_instance_id, payment_methods_instance_setting_key', $DBTables);
// End of define payment_methods_instance_id, payment_methods_instance_setting_key as index key in payment_methods_instance_setting table


// Insert Mandatory IC Info payment configuration setting
$pm_select_sql = "	SELECT pci.payment_methods_id, pci.payment_configuration_info_key, pm.payment_methods_title, pm.payment_methods_parent_id, pci.payment_configuration_info_sort_order 
					FROM payment_configuration_info as pci 
					INNER JOIN payment_methods as pm 
						ON pm.payment_methods_id = pci.payment_methods_id 
					WHERE pci.payment_configuration_info_key LIKE '%_CONFIRM_COMPLETE%'
					GROUP BY pci.payment_methods_id";
$pm_result_sql = tep_db_query($pm_select_sql);

$install_key_array = array();
while ($pm_row = tep_db_fetch_array($pm_result_sql)) {
	preg_match_all("/MODULE_PAYMENT_([A-Za-z0-9_ ]+)_CONFIRM_COMPLETE/", $pm_row['payment_configuration_info_key'], $matches);
	
	if (isset($matches[1][0])) {	
		$check_key_exist_sql = "SELECT payment_configuration_info_key 
								FROM payment_configuration_info
								WHERE payment_configuration_info_key = 'MODULE_PAYMENT_".$matches[1][0]."_MANDATORY_CONTACT_FIELD'
									AND payment_methods_id = '".$pm_row['payment_methods_id']."'";
		$check_key_result_sql = tep_db_query($check_key_exist_sql);
		if (!tep_db_num_rows($check_key_result_sql)) {
			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>$pm_row['payment_methods_id'],
																'payment_configuration_info_title'=>'Require Contact Information',
																'payment_configuration_info_key'=>"MODULE_PAYMENT_".$matches[1][0]."_MANDATORY_CONTACT_FIELD",
																'payment_configuration_info_description'=>'Set contact field as required info during checkout.',
																'payment_configuration_info_sort_order' => $pm_row['payment_configuration_info_sort_order'] + 50,
																'set_function'=>'tep_cfg_select_option(array(\'True\', \'False\'), ',
																'use_function'=>'',
																'date_added'=>'now()'
															),
											'desc' => array (	'payment_configuration_info_value'=>'False',
															 	'languages_id' => 1
															)
										);
		}
	}
}

foreach ($install_key_array as $data) {
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
	$payment_conf_id = tep_db_insert_id();
	
	$data['desc']['payment_configuration_info_id'] = (int)$payment_conf_id;
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
}
// End of insert Mandatory Address Info payment configuration setting

// Insert Mandatory IC Info payment configuration setting
$pm_select_sql = "	SELECT pci.payment_methods_id, pci.payment_configuration_info_key, pm.payment_methods_title, pm.payment_methods_parent_id, pci.payment_configuration_info_sort_order 
					FROM payment_configuration_info as pci 
					INNER JOIN payment_methods as pm 
						ON pm.payment_methods_id = pci.payment_methods_id 
					WHERE pci.payment_configuration_info_key LIKE '%_CONFIRM_COMPLETE%'
					GROUP BY pci.payment_methods_id";
$pm_result_sql = tep_db_query($pm_select_sql);

$install_key_array = array();
while ($pm_row = tep_db_fetch_array($pm_result_sql)) {
	preg_match_all("/MODULE_PAYMENT_([A-Za-z0-9_ ]+)_CONFIRM_COMPLETE/", $pm_row['payment_configuration_info_key'], $matches);
	
	if (isset($matches[1][0])) {	
		$check_key_exist_sql = "SELECT payment_configuration_info_key 
								FROM payment_configuration_info
								WHERE payment_configuration_info_key = 'MODULE_PAYMENT_".$matches[1][0]."_MANDATORY_IC_FIELD'
									AND payment_methods_id = '".$pm_row['payment_methods_id']."'";
		$check_key_result_sql = tep_db_query($check_key_exist_sql);
		if (!tep_db_num_rows($check_key_result_sql)) {
			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>$pm_row['payment_methods_id'],
																'payment_configuration_info_title'=>'Require IC Information',
																'payment_configuration_info_key'=>"MODULE_PAYMENT_".$matches[1][0]."_MANDATORY_IC_FIELD",
																'payment_configuration_info_description'=>'Set IC field as required info during checkout.',
																'payment_configuration_info_sort_order' => $pm_row['payment_configuration_info_sort_order'] + 50,
																'set_function'=>'tep_cfg_select_option(array(\'True\', \'False\'), ',
																'use_function'=>'',
																'date_added'=>'now()'
															),
											'desc' => array (	'payment_configuration_info_value'=>'False',
															 	'languages_id' => 1
															)
										);
		}
	}
}

foreach ($install_key_array as $data) {
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
	$payment_conf_id = tep_db_insert_id();

	$data['desc']['payment_configuration_info_id'] = (int)$payment_conf_id;
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
}
// End of insert Mandatory Address Info payment configuration setting
?>