<?
/*
	$Id: version_3_1.php,v 1.2 2009/03/18 03:47:05 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define dateimpression as index key in wd_pa_impressions table
add_index_key ('wd_pa_impressions', 'index_dateimpression', 'index', 'dateimpression', $DBTables);
// End of define dateimpression as index key in wd_pa_impressions table

// AFT Automation - Chan
// Insert new records into admin_files table (for aft script page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["aft_automation.php"] = array(	"insert" => " ('aft_automation.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
						   								);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='aft_automation.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for aft script page)

// Create aft automation tables
$add_new_tables = array();

$add_new_tables["aft_functions"] = array 	(	"structure" => 	"CREATE TABLE `aft_functions` (
																  `aft_functions_id` int(11) NOT NULL auto_increment,
																  `aft_functions_name` varchar(255) NOT NULL default '',
																  `aft_functions_display_name` varchar(255) NOT NULL default '',
																  `aft_functions_description` varchar(255) NOT NULL default '',
																  `aft_functions_action` tinyint(1) NOT NULL default '0',
																  `line_determine` tinyint(1) NOT NULL default '0',
																  `aft_functions_setting` text NOT NULL,
																  PRIMARY KEY  (`aft_functions_id`)
																) TYPE=MyISAM;",
												"data" => "INSERT INTO `aft_functions` (aft_functions_id, aft_functions_name, aft_functions_display_name, aft_functions_description, aft_functions_action, line_determine, aft_functions_setting) 
															VALUES (1, 'set_order_status', 'Set Order Status', 'Update order status to', 1, 1, 'a:1:{i:0;a:3:{s:4:\"type\";s:6:\"select\";s:6:\"option\";a:6:{i:0;a:2:{s:2:\"id\";i:1;s:4:\"text\";s:7:\"Pending\";}i:1;a:2:{s:2:\"id\";i:2;s:4:\"text\";s:10:\"Processing\";}i:2;a:2:{s:2:\"id\";i:3;s:4:\"text\";s:9:\"Completed\";}i:3;a:2:{s:2:\"id\";i:5;s:4:\"text\";s:8:\"Canceled\";}i:4;a:2:{s:2:\"id\";i:7;s:4:\"text\";s:9:\"Verifying\";}i:5;a:2:{s:2:\"id\";i:8;s:4:\"text\";s:7:\"On Hold\";}}s:11:\"description\";s:21:\"Move order status to?\";}}'),
																 (2, 'get_order_payment_method', 'Get Order''s Payment Method', 'Return Payment Method''s ID made for this order.', 0, 0, ''),
																 (3, 'get_order_status', 'Get Order''s Status', 'Return Order''s Status', 0, 0, ''),
																 (4, 'get_order_payment_method_parent', 'Get Order''s Payment Gateway', 'Return Payment Gateway''s ID made for this order.', 0, 0, ''),
																 (5, 'get_total_rp_amount', 'Get customer total RP amount', 'Return customer''s total Reversable Payment amount', 0, 0, ''),
																 (6, 'get_confirm_completed_amount', 'Get confirm completed amount', 'Return customer''s total confirm completed amount', 0, 0, ''),
																 (7, 'is_flag_customer', 'Flagged customer?', 'Flagged customer? Return ''true'' or ''false''.', 0, 0, ''),
																 (8, 'is_nrp_customer', 'Is NRP customer?', 'Is Non-reversible Payment Only customer? Return ''true'' or ''false''.', 0, 0, ''),
																 (9, 'is_cb_customer', 'Is CB customer?', 'Is Chargeback customer? Return ''true'' or ''false''.', 0, 0, ''),
																 (10, 'is_email_verified', 'Customer email verified?', 'Is Customer''s email verified? Return ''true'' or ''false''.', 0, 0, ''),
																 (11, 'get_maxmind_score', 'Maxmind Score', 'Get Order''s Maxmind Score', 0, 0, ''),
																 (12, 'get_maxmind_phone_type', 'Maxmind Phone Type', 'Get Order''s Maxmind Phone Type ID - Please refer <a href=\"http://www.maxmind.com/app/phone_id_codes\">here</a>', 0, 0, ''),
																 (13, 'get_customer_group', 'Customer Group', 'Get customer''s group (2 - Member, 3 - Silver, 4 - Gold, 5 - Platinum, 6 - Reseller - CD Keys, 7 - Reseller - Currency)', 0, 0, ''),
																 (14, 'set_customer_flag', 'Flag Customer Action', 'Flag customer action', 1, 1, ''),
																 (15, 'set_customer_nrp_flag', 'Flag NRP customer', 'Flag NRP customer', 1, 1, ''),
																 (16, 'set_customer_cb_flag', 'Flag CB customer', 'Flag CB customer', 1, 1, ''),
																 (17, 'is_share_ip_with_cb_customers', 'Share IP with CB Customers?', 'Share IP with CB Customers? Return ''true'' or ''false''.', 0, 0, ''),
																 (18, 'is_share_tel_with_cb_customers', 'Share TEL with CB Customers?', 'Share TEL with CB Customers? Return ''true'' or ''false''.', 0, 0, ''),
																 (19, 'get_order_profile_country', 'Get Order Profile Country', 'Get Order''s Profile Country. Return country name.', 0, 0, ''),
																 (20, 'get_order_billing_address', 'Get Order Billing Address Country', 'Get Order''s Billing Address Country. Return country name.', 0, 0, ''),
																 (21, 'set_order_comment', 'Set Order Comments', 'Set order comments', 1, 1, 'a:3:{i:0;a:2:{s:4:\"type\";s:13:\"order_comment\";s:11:\"description\";s:14:\"Order Comments\";}i:1;a:3:{s:4:\"type\";s:6:\"select\";s:6:\"option\";a:2:{i:0;a:2:{s:2:\"id\";i:0;s:4:\"text\";s:2:\"No\";}i:1;a:2:{s:2:\"id\";i:1;s:4:\"text\";s:3:\"Yes\";}}s:11:\"description\";s:16:\"Notify Customer?\";}i:2;a:3:{s:4:\"type\";s:6:\"select\";s:6:\"option\";a:2:{i:0;a:2:{s:2:\"id\";i:0;s:4:\"text\";s:2:\"No\";}i:1;a:2:{s:2:\"id\";i:1;s:4:\"text\";s:3:\"Yes\";}}s:11:\"description\";s:17:\"Set Order Remark?\";}}'),
																 (22, 'is_order_nrp_payment', 'Is NRP order?', 'Is Order made by Non-reversible Payment methods? Return ''true'' or ''false''.', 0, 0, '');"
											);

$add_new_tables["aft_automation_category"] = array 	(	"structure" => 	"CREATE TABLE `aft_automation_category` (
																		  `aft_automation_category_id` int(11) NOT NULL auto_increment,
																		  `aft_automation_category_type` varchar(32) NOT NULL default '',
																		  `aft_automation_category_key` varchar(32) NOT NULL default '',
																		  PRIMARY KEY  (`aft_automation_category_id`)
																		) TYPE=MyISAM;",
														"data" => "INSERT INTO `aft_automation_category` (aft_automation_category_id, aft_automation_category_type, aft_automation_category_key) 
																		VALUES (1, 'C', '7'), 
																		(2, 'C', '2');"
													);

$add_new_tables["aft_automation"] = array 	(	"structure" => 	"CREATE TABLE `aft_automation` (
																  `aft_automation_id` int(11) NOT NULL auto_increment,
																  `aft_automation_category_id` int(11) NOT NULL default '0',
																  `aft_automation_mode` tinyint(1) NOT NULL default '0',
																  `aft_automation_code` text NOT NULL,
																  `aft_automation_version` varchar(16) NOT NULL default '0.0',
																  `admin_id` varchar(255) NOT NULL default '',
																  `last_modified` datetime NOT NULL default '0000-00-00 00:00:00',
																  PRIMARY KEY  (`aft_automation_id`)
																) TYPE=MyISAM;",
												"data" => "INSERT INTO `aft_automation` (aft_automation_id, aft_automation_category_id, aft_automation_mode, aft_automation_code, aft_automation_version) 
															VALUES (1, 1, 0, '', '1.0'),
															(2, 2, 0, '', '1.0');"
											);

$add_new_tables["aft_automation_version"] = array 	(	"structure" => 	"CREATE TABLE `aft_automation_version` (
																		  `aft_automation_version` varchar(16) NOT NULL default '',
																		  `aft_automation_id` int(11) NOT NULL default '0',
																		  `aft_automation_version_code` text NOT NULL,
																		  `changed_by` varchar(255) NOT NULL default '',
																		  `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																		  PRIMARY KEY  (`aft_automation_version`,`aft_automation_id`)
																		) TYPE=MyISAM;",
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// Enf of create aft automation tables

// Insert new fields into orders table (for keep track which order has been performed perform aft script)
$add_new_field = array();

$add_new_field['orders'] = array (	array (	"field_name" => "orders_aft_executed",
											"field_attr" => " tinyint(1) NOT NULL DEFAULT '0' ",
											"add_after" => ""
								   			)
								);

add_field($add_new_field);
// End of insert new fields into orders table (for keep track which order has been performed perform aft script)
?>