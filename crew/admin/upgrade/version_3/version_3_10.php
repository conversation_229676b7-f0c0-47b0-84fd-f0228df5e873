<?
/*
	$Id: version_3_10.php,v 1.1 2010/06/04 10:13:06 weesiong Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Wee Siong
// Create customers_connection table
$add_new_tables = array();

$add_new_tables["customers_connection"] = array (	"structure" => "CREATE TABLE `customers_connection` ( 
																		`customers_id` int(11) unsigned NOT NULL default '0', 
																		`customers_fb_uid` bigint(20) unsigned NOT NULL default '0', 
																		PRIMARY KEY (`customers_id`), 
																		KEY `index_customers_fb_uid` (`customers_fb_uid`) 
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create customers_connection table

// Insert new records into configuration_group table (for Facebook Connect module)
$configuration_group_insert_sql = array();

$configuration_group_insert_sql["Facebook Connect"] = array	(	"insert" => " ('Facebook Connect', 'Facebook Connect Configuration', 400, 1) ",
																"update" => " sort_order=400, visible='1' "
						   									);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");
// End of insert new records into configuration_group table (for Facebook Connect module)

// Insert new records into configuration table (for Facebook Connect module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Facebook Connect'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["FB_API_KEY"] = array("insert" => " ('Your public API key', 'FB_API_KEY', 'cd8ebbf790691980766ffa9cb3092948', 'Facebook Application API Key', ".$row_sql["configuration_group_id"].", 5, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["FB_SECRET"] = array("insert" => " ('Secret Key', 'FB_SECRET', '18d395b0f69f8a4970a291488b52a352', 'Do not share your secret key with anyone.', ".$row_sql["configuration_group_id"].", 10, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["FB_REQUIRED_FB_PERMISSION"] = array("insert" => " ('Permission needed from Facebook User', 'FB_REQUIRED_FB_PERMISSION', 'offline_access,publish_stream', 'Permission needed from Facebook User', ".$row_sql["configuration_group_id"].", 15, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Facebook Connect module)
?>