<?
/*
	$Id: version_3_10_2.php,v 1.1 2010/06/17 07:43:29 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "80M");
tep_set_time_limit(0);

require_once(DIR_WS_CLASSES . 'pinyin.php');
$pinyin = new pinyin();

$existing_categories_description_fields = get_table_fields(TABLE_CATEGORIES_DESCRIPTION);

// Insert new field into categories_description table
$add_new_field = array();

$add_new_field[TABLE_CATEGORIES_DESCRIPTION] = array (array (	"field_name" => "categories_pin_yin",
								 								"field_attr" => " varchar(64) not null default '' ",
								 								"add_after" => "categories_short_name"
								 							)
									  				);

add_field ($add_new_field, false);
// End of insert new field into categories_description table

if (!in_array('categories_pin_yin', $existing_categories_description_fields)) {
	// Update categories_pin_yin in categories_description table
	$description_select_sql = "	SELECT categories_id, language_id , categories_name
								FROM " . TABLE_CATEGORIES_DESCRIPTION . " 
								WHERE categories_pin_yin = ''
									AND categories_name <> ''";
	$description_result_sql = tep_db_query($description_select_sql);
	
	while ($description_row = tep_db_fetch_array($description_result_sql)) {
		$category_pin_yin = $pinyin->getPinyin($description_row['categories_name']);
		
		$update_pin_yin_sql = "	UPDATE " . TABLE_CATEGORIES_DESCRIPTION . "
								SET categories_pin_yin = '".tep_db_input($category_pin_yin)."' 
								WHERE categories_id = '".tep_db_input($description_row['categories_id'])."'
									AND language_id = '".tep_db_input($description_row['language_id'])."'";
		tep_db_query($update_pin_yin_sql);
	}
	// End of update categories_pin_yin in categories_description table
}
?>