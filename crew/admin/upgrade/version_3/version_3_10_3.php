<?
/*
	$Id: version_3_10_3.php,v 1.1 2010/06/23 10:23:13 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Ching Yen
// Insert new records into configuration table (for Bizrate Survey)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Advertisement'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BIZRATE_SURVEY_ID"] = array("insert" => " ('Bizrate Survey ID', 'BIZRATE_SURVEY_ID', '', 'Your Bizrate Survey ID (Leave blank if not supported)', ".$row_sql["configuration_group_id"].", 65, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Bizrate Survey)
?>