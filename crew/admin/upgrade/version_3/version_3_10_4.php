<?
/*
	$Id: version_3_10_4.php,v 1.1 2010/06/25 08:30:56 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON><PERSON>
// Create RHB tables
$add_new_tables = array();

$add_new_tables["rhb"] = array (	"structure" => "CREATE TABLE `rhb` (
														`rhb_orders_id` int(11) NOT NULL default '0',
														`rhb_status` varchar(20) NOT NULL,
														`rhb_amount` decimal(15,2) NOT NULL default '0.00',
														`rhb_currency` char(3) NOT NULL default 'MYR',
														`rhb_return_code` varchar(2) NOT NULL,
														PRIMARY KEY  (`rhb_orders_id`)
													) TYPE=MyISAM;",
									"data" => ""
								);
								
$add_new_tables["rhb_payment_status_history"] = array (	"structure" => "CREATE TABLE `rhb_payment_status_history` (
																		  `rhb_payment_status_history_id` int(11) NOT NULL auto_increment,
																		  `rhb_orders_id` int(11) NOT NULL default '0',
																		  `rhb_date` datetime NOT NULL default '0000-00-00 00:00:00',
																		  `rhb_status` varchar(20) default NULL,
																		  `rhb_description` varchar(64) NOT NULL,
																		  `rhb_changed_by` varchar(128) NOT NULL,
																		  PRIMARY KEY  (`rhb_payment_status_history_id`)
																		) TYPE=MyISAM;",
														"data" => ""
													);
add_new_tables ($add_new_tables, $DBTables);
// End of create RHB tables
?>