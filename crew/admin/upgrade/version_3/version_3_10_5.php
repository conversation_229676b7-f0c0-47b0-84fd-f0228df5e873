<?
/*
	$Id: version_3_10_5.php,v 1.2 2010/07/07 03:44:08 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Kee <PERSON>g
// Create products_follow_price table
$add_new_tables = array();

$add_new_tables["products_follow_price"] = array (	"structure" => "CREATE TABLE `products_follow_price` (
																	  `products_id` int(11) NOT NULL default '0',
																	  `follow_products_id` int(11) NOT NULL default '0',
																	  PRIMARY KEY  (`products_id`,`follow_products_id`),
																	  KEY `index_follow_products_id` (`follow_products_id`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create products_follow_price table

// Insert new records into admin_files_actions table (for permission on check/uncheck Follow Actual Product checkbox)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='categories.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CATALOG_EDIT_FOLLOW_ACTUAL_PRODUCT"] = array("insert" => " ('CATALOG_EDIT_FOLLOW_ACTUAL_PRODUCT', 'Edit Follow Actual Product', ".$row_sql["admin_files_id"].", '1', '49')" );
	$admin_files_actions_insert_sql["CATALOG_EDIT_FOLLOW_ACTUAL_PRODUCT_STATUS"] = array("insert" => " ('CATALOG_EDIT_FOLLOW_ACTUAL_PRODUCT_STATUS', 'Edit Follow Actual Product Status', ".$row_sql["admin_files_id"].", '1', '44')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on check/uncheck Follow Actual Product checkbox)
?>