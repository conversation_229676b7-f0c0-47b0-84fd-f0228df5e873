<?
/*
	$Id: version_3_10_8.php,v 1.2 2010/07/30 09:07:45 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Wilson
// Create products_low_stock table
$add_new_tables = array();

$add_new_tables["products_low_stock"] = array (	"structure" => "CREATE TABLE `products_low_stock` (
																  `products_id` int(11) NOT NULL,
																  `custom_products_type_id` int(11) NOT NULL default '0',
																  `low_stock_date` datetime default NULL,
																  `low_stock_tag_ids` varchar(255) NOT NULL default '',
																  `low_stock_status` tinyint(1) NOT NULL default '1',
																  PRIMARY KEY  (`products_id`),
																  KEY `idx_custom_products_type_id` (`custom_products_type_id`)
																) ENGINE=MyISAM;",
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create products_low_stock table

// Insert new records into admin_files table (for Low Stock Warning Report)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='stock.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["products_low_stock.php"] = array(	"insert" => " ('products_low_stock.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
								   							);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='products_low_stock.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Low Stock Warning Report)


// Wei Chen
// Delete useless script from admin_files table
$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'validcategories.php'";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}

$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'validproducts.php'";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}

$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'listcategories.php'";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}

$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'listproducts.php'";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}
// End of delete useless script from admin_files table

// Boon Hock
// Insert new records into configuration table (for Split Gateway Changes Notify Email)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Store Information'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["STORE_RECEIVE_PAYMENT_METHODS_CHANGES_NOTIFICATION_EMAIL"] = array("insert" => " ('Receiving Payment Method Changes Notify Email', 'STORE_RECEIVE_PAYMENT_METHODS_CHANGES_NOTIFICATION_EMAIL', '', 'Email address to which the email will be send to whenever admin user change receiving payment method settings.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 17, NULL, now(), NULL, '')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Split Gateway Changes Notify Email)

?>