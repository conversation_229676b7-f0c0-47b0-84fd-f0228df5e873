<?
/*
	$Id: version_3_11.php,v 1.1 2010/08/06 12:56:51 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$add_new_tables = array();

$add_new_tables["cron_customer_upgrade"] = array (	"structure" => "CREATE TABLE `cron_customer_upgrade` (
																	  `cron_customer_upgrade_from` int(11) NOT NULL,
																	  `cron_customer_upgrade_to` int(11) NOT NULL,
																	  `cron_customer_last_process_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `cron_customer_upgrade_in_action` tinyint(4) NOT NULL default '0',
																	  `cron_customer_upgrade_last_process_customer_id` int(11) NOT NULL,
																	  `cron_customer_upgrade_processed_count` int(11) NOT NULL DEFAULT '0',
																	  PRIMARY KEY  (`cron_customer_upgrade_from`,`cron_customer_upgrade_to`)
																	) ENGINE=MyISAM;" ,
											"data" => "	INSERT INTO `cron_customer_upgrade` VALUES
															(12, 3, '0000-00-00 00:00:00', 0, 0, 0),
															(3, 4, '0000-00-00 00:00:00', 0, 0, 0),
															(4, 5, '0000-00-00 00:00:00', 1, 0, 0);"
										);
add_new_tables ($add_new_tables, $DBTables);

$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_upgrade_customers_group.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_progress_task_data_sql = array(	'cron_process_track_in_action' => 0,
											'cron_process_track_start_date' => '2009-11-17 00:00:00',
											'cron_process_track_failed_attempt' => 0,
											'cron_process_track_filename' => 'cron_upgrade_customers_group.php'
										);
	tep_db_perform('cron_process_track', $cron_progress_task_data_sql);
}

?>