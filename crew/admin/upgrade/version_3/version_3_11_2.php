<?
/*
	$Id: version_3_11_2.php,v 1.1 2010/08/25 08:05:31 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration_group table (for Store Credit module)
$configuration_group_insert_sql = array();

$configuration_group_insert_sql["Store Credit"] = array	(	"insert" => " ('Store Credit', 'Store Credit Configuration', 375, 1) ",
															"update" => " sort_order=375, visible='1' "
					   									);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");
// End of insert new records into configuration_group table (for Store Credit module)

// Insert new records into configuration table (for Store Credit module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Store Credit'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["SC_CONFIG_EXTRA_PROMO"] = array("insert" => " ('Store Credit Purchase Extra Credit', 'SC_CONFIG_EXTRA_PROMO', '', 'Extra percentage to credit when deliver store credit (in %)', ".$row_sql["configuration_group_id"].", 5, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["SC_CONFIG_EXTRA_PROMO_CUST_GRP"] = array("insert" => " ('Extra Credit Eligible Customer Groups', 'SC_CONFIG_EXTRA_PROMO_CUST_GRP', '', 'List of Customer Group IDs (Separate each group by comma)', ".$row_sql["configuration_group_id"].", 10, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Store Credit module)
?>