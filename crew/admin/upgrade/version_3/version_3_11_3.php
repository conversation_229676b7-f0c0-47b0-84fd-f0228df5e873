<?
/*
	$Id: version_3_11_3.php,v 1.2 2010/09/01 05:46:17 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "150M");
tep_set_time_limit(0);

$existing_payment_methods_types_fields = get_table_fields('payment_methods_types');

// <PERSON><PERSON>ck
// Create customers_sc_cart table
$add_new_tables = array();

$add_new_tables["customers_sc_cart"] = array (	"structure" => "CREATE TABLE `customers_sc_cart` (
																  `customers_id` int(11) NOT NULL default '0',
																  `products_id` int(11) NOT NULL,
																  `customers_sc_cart_quantity` int(11) NOT NULL default '0',
																  `customers_sc_cart_date_added` datetime default '0000-00-00 00:00:00',
																  PRIMARY KEY  (`customers_id`),
																  KEY `index_customers_id` (`customers_id`)
																) ENGINE=MyISAM;",
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create customers_sc_cart table

// Change field structure for price info field in orders_products table
$change_field_structure = array();

$change_field_structure['orders_products'] = array (array (	"field_name" => "orders_products_store_price",
															"field_attr" => " decimal(19,8) default NULL "
									 					),
									 				array (	"field_name" => "products_price",
															"field_attr" => " decimal(19,8) NOT NULL default '0.0000' "
									 					),
									 				array (	"field_name" => "final_price",
															"field_attr" => " decimal(19,8) NOT NULL default '0.0000' "
									 					)
												);

change_field_structure ($change_field_structure);
// End of change field structure for price info field in orders_products table

// Insert new field into payment_methods_types table
$add_new_field = array();

$add_new_field['payment_methods_types'] = array (array (	"field_name" => "payment_methods_types_sites",
							 								"field_attr" => " varchar(16) NOT NULL DEFAULT 'MAIN,SC' ",
							 								"add_after" => "payment_methods_types_system_define"
							 							)
								  				);

add_field ($add_new_field, false);
// End of insert new field into payment_methods_types table

if (!in_array('payment_methods_types_sites', $existing_payment_methods_types_fields)) {
	tep_db_query("UPDATE payment_methods_types SET payment_methods_types_name = 'OFFLINE PAYMENTS (SC)', payment_methods_types_sites = 'SC' WHERE payment_methods_types_id=4");
	
	// Insert new records into payment_methods_types table
	$payment_methods_types_insert_sql = array();
	
	$payment_methods_types_insert_sql["OFFLINE PAYMENTS"] = array("insert" => " ('OFFLINE PAYMENTS', 'RECEIVE', '0', 'MAIN,SC', '500')");
	
	insert_new_records('payment_methods_types', "payment_methods_types_name", $payment_methods_types_insert_sql, $DBTables, "(`payment_methods_types_name`, `payment_methods_types_mode`, `payment_methods_types_system_define`, `payment_methods_types_sites`, `payment_methods_types_sort_order`)");
	
	$new_type_id = tep_db_insert_id();
	
	tep_db_query("INSERT INTO payment_methods_types_description (payment_methods_types_id, languages_id, payment_methods_types_description) VALUES (".$new_type_id.", '1', 'OFFLINE PAYMENTS (1 to 5 business days)'), (".$new_type_id.", '2', 'OFFLINE PAYMENTS (1 to 5 business days)');");
	// End of insert new records into payment_methods_types table
}
?>