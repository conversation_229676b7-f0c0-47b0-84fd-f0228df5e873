<?
/*
	$Id: version_3_11_4.php,v 1.1 2010/09/02 12:09:31 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

if (!in_array('events_description', $DBTables)) {
	$transfer_event_info = true;
} else {
	$transfer_event_info = false;
}

// Henry
// Create events related table
$add_new_tables = array();

$add_new_tables["events_description"] = array (	"structure" => "CREATE TABLE `events_description` (
																	`events_id` int(11) NOT NULL default '0',
																	`language_id` int(11) NOT NULL default '1',
																	`events_name` varchar(100) NOT NULL default '',
																	`events_email_tpl` text NOT NULL,
																	PRIMARY KEY (`events_id`, `language_id`) 
																)ENGINE=MyISAM;",
												"data" => ""
											);

$add_new_tables["events_options_description"] = array (	"structure" => "CREATE TABLE `events_options_description` (
																			`events_options_id` int(11) NOT NULL default '0',
																			`language_id` int(11) NOT NULL default '1',
																			`events_options_title` varchar(255) NOT NULL default '',
																			`events_options_note`  varchar(255) NOT NULL default '',
																			`events_options_name` varchar(128) NOT NULL default '',
																			`events_options_max_size` int(3) NOT NULL default '0',
																			`events_options_row_size` int(3) NOT NULL default '0',
																			`events_options_column_size` int(3) NOT NULL default '0',
																			`events_options_err_msg` varchar(128) NOT NULL default '',
																			PRIMARY KEY (`events_options_id`, `language_id`)
																		) ENGINE=MyISAM;",
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create events related table

// Insert new field into events_options_values table
$add_new_field = array();

$add_new_field['events_options_values'] = array (array (	"field_name" => "language_id",
							 								"field_attr" => " int(11) NOT NULL default '1' ",
							 								"add_after" => "events_options_id"
							 							)
								  				);

add_field ($add_new_field, false);
// End of insert new field into events_options_values table

if ($transfer_event_info) {
	$event_select_sql = '	SELECT events_id, events_name, events_email_tpl
							FROM events';
	$event_result_sql = tep_db_query($event_select_sql);
	
	while ($event_row = tep_db_fetch_array($event_result_sql)) {
		$event_desc_data_array = array(	'events_id' => $event_row['events_id'],
							 			'language_id' => '1',
							 			'events_name' => $event_row['events_name'],
							 			'events_email_tpl' => $event_row['events_email_tpl']
						     		);
		tep_db_perform('events_description', $event_desc_data_array);
	}
	
	$event_option_select_sql = 'SELECT events_options_id, events_options_title, events_options_max_size, events_options_row_size, events_options_column_size, events_options_name, events_options_note, events_options_err_msg
								FROM events_options';
	$event_option_result_sql = tep_db_query($event_option_select_sql);
	
	while ($event_option_row = tep_db_fetch_array($event_option_result_sql)) {
		$event_opt_desc_data_array = array(	'events_options_id' => $event_option_row['events_options_id'],
								 			'language_id' => '1',
								 			'events_options_title' => $event_option_row['events_options_title'],
								 			'events_options_note' => $event_option_row['events_options_note'],
								 			'events_options_name' => $event_option_row['events_options_name'],
								 			'events_options_max_size' => $event_option_row['events_options_max_size'],
								 			'events_options_row_size' => $event_option_row['events_options_row_size'],
								 			'events_options_column_size' => $event_option_row['events_options_column_size'],
								 			'events_options_err_msg' => $event_option_row['events_options_err_msg']
						     			);
		tep_db_perform('events_options_description', $event_opt_desc_data_array);
	}
}
?>