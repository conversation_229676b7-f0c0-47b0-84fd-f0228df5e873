<?
/*
	$Id: version_3_11_5.php,v 1.2 2010/09/06 09:48:43 wilson.sun Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "150M");
tep_set_time_limit(0);

// Wee <PERSON>ong
// Insert new records into configuration table (for Facebook New API)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Facebook Connect'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["FB_APPS_ID"] = array("insert" => " ('Facebook Application ID', 'FB_APPS_ID', '', 'Facebook Application ID', ".$row_sql["configuration_group_id"].", 6, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Facebook New API)

// Wilson
// Restructure database storing content for SC Sales
$sc_title_update_sql = "UPDATE store_credit_history
						SET store_credit_history_activity_title = '',
							store_credit_history_trans_type = 'C'
						WHERE store_credit_activity_type = 'S'
							AND store_credit_history_activity_title='Store Credit Sales'";
tep_db_query($sc_title_update_sql);
// End of restructure database storing content for SC Sales
?>