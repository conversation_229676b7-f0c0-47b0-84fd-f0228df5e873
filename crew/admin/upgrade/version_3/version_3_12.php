<?
/*
	$Id: version_3_12.php,v 1.2 2010/10/04 08:46:58 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// create new table for HLA [start]
$add_new_tables = array();

$add_new_tables["orders_products_item"] = array (	"structure" => "CREATE TABLE `orders_products_item` (
																		`orders_products_id` int(11) NOT NULL,
																		`products_hla_characters_id` int(10) unsigned NOT NULL,
																		`products_hla_characters_name` varchar(64) NOT NULL default '',
																		`orders_products_item_info` text NOT NULL,
																		PRIMARY KEY (`orders_products_id`,`products_hla_characters_id`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["products_hla_attributes"] = array ("structure" => "CREATE TABLE `products_hla_attributes` (
																		`products_hla_characters_id` int(11) unsigned NOT NULL,
																		`language_id` int(11) NOT NULL default '1',
																		`products_hla_attributes_type` varchar(16) NOT NULL default '',
																		`products_hla_value` varchar(32) default '',
																		PRIMARY KEY (`products_hla_characters_id`,`language_id`,`products_hla_attributes_type`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
													);

$add_new_tables["products_hla_characters"] = array ("structure" => "CREATE TABLE `products_hla_characters` (
																		`products_hla_characters_id` int(11) unsigned NOT NULL auto_increment,
																		`products_hla_id` int(11) unsigned NOT NULL,
																		`products_ref_id` char(20) NOT NULL COMMENT 'Ref id from supplier',
																		PRIMARY KEY (`products_hla_characters_id`,`products_hla_id`),
																		KEY `products_hla_id` (`products_hla_id`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
													);

$add_new_tables["products_hla_description"] = array (	"structure" => "CREATE TABLE `products_hla_description` (
																			`products_hla_characters_id` int(11) unsigned NOT NULL,
																			`language_id` int(11) NOT NULL default '1',
																			`products_hla_characters_name` varchar(64) default '',
																			`products_hla_characters_description` text,
																			PRIMARY KEY (`products_hla_characters_id`,`language_id`)
																		) ENGINE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables["products_hla"] = array (	"structure" => "CREATE TABLE `products_hla` (
																`products_hla_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
																`seller_id` int(11) NOT NULL,
																`products_id` int(11) NOT NULL,
																`products_type` enum('1','2') NOT NULL COMMENT '1 = single, 2 = multiple',
																`available_quantity` int(4) NOT NULL DEFAULT '0',
																`actual_quantity` int(4) NOT NULL DEFAULT '0',
																`products_account_id` int(11) DEFAULT NULL,
																`products_price` decimal(15,6) NOT NULL,
																`products_base_currency` char(3) NOT NULL,
																`products_status` enum('-1','0','1') NOT NULL COMMENT '-1 = Inactive, 0 = Sold, 1 = Active',
																`products_display` enum('0','1') NOT NULL COMMENT '0=hide, 1=show',
																`created_date` datetime NOT NULL,
																`products_hla_info_viewed` enum('0','1') NOT NULL COMMENT '0 = not viewed, 1 = viewed',
																PRIMARY KEY (`products_hla_id`),
																KEY `products_id` (`products_id`)
															) ENGINE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["products_templates"] = array (	"structure" => "CREATE TABLE `products_templates` (
																 	`products_id` int(11) unsigned NOT NULL default '0',
																	`products_templates_id` int(11) unsigned NOT NULL default '0',
																	PRIMARY KEY (`products_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// create new table for HLA [end]

// insert new schedule task [start]
$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_download_hla_rss.sh'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_process_track_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_download_hla_rss.sh'
			                       );
	tep_db_perform('cron_process_track', $cron_process_track_array);
}

$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_download_hla_profile.sh'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_process_track_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_download_hla_profile.sh'
			                       );
	tep_db_perform('cron_process_track', $cron_process_track_array);
}
// insert new schedule task [end]

// insert new permission control on orders.php: HLA account info STAGE-1 and STAGE-2 [start]
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_HLA_DETAILS_STAGE_1"] = array("insert" => " ('ORDER_HLA_DETAILS_STAGE_1', 'HLA Stage 1 Info', ".$row_sql["admin_files_id"].", '1', 7)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
	
	$admin_files_actions_insert_sql["ORDER_HLA_DETAILS_STAGE_2"] = array("insert" => " ('ORDER_HLA_DETAILS_STAGE_2', 'HLA Stage 2 Info', ".$row_sql["admin_files_id"].", '1', 7)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// insert new permission control on orders.php: HLA account info STAGE-1 and STAGE-2 [end]


// insert new custom_products_type [start]
$type_select_sql = 'SELECT custom_products_type_id
					FROM custom_products_type
					WHERE custom_products_type_id=4';
$type_result_sql = tep_db_query($type_select_sql);
if (!tep_db_num_rows($type_result_sql)) {
	$custom_products_type_data_sql = array(	'custom_products_type_id' => 4,
											'custom_products_type_name' => 'High Level Account',
											'data_pool_id' => 0,
											'custom_products_low_stock_email' => '',
											'custom_products_add_stock_email' => '',
											'custom_products_deduct_stock_email' => '',
											'custom_products_admin_group_id' => '1'
											);
	
	tep_db_perform('custom_products_type', $custom_products_type_data_sql);
}
// insert new custom_products_type [end]

// Insert new field into products_hla table
$add_new_field = array();

$add_new_field['products_hla'] = array (array (	"field_name" => "products_original_price",
				 								"field_attr" => " decimal(15,6) NOT NULL ",
				 								"add_after" => "products_price"
				 							)
					  				);

add_field ($add_new_field, false);
// End of insert new field into products_hla table
?>