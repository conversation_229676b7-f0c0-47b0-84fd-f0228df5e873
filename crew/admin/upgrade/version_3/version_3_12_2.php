<?
/*
	$Id: version_3_12_2.php,v 1.4 2010/10/14 12:25:03 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_categories_fields = get_table_fields('categories');
$existing_define_product_type_page_fields = get_table_fields('define_product_type_page');
$existing_categories_product_types_fields = get_table_fields('categories_product_types');

// Wee Siong
// Create custom_products_type_child table
$add_new_tables = array();

$add_new_tables["custom_products_type_child"] = array (	"structure" => "CREATE TABLE `custom_products_type_child` (
																		  `custom_products_type_child_id` int(11) NOT NULL auto_increment,
																		  `custom_products_type_id` int(11) NOT NULL default '0',
																		  `custom_products_type_child_url` varchar(64) NOT NULL default '',
																		  `display_status` tinyint(1) NOT NULL default '0',
																		  `sort_order` int(5) default NULL,
																		  PRIMARY KEY  (`custom_products_type_child_id`)
																		) ENGINE=MyISAM;",
														"data" => "INSERT INTO `custom_products_type_child` (`custom_products_type_child_id`, `custom_products_type_id`, `custom_products_type_child_url`, `display_status`, `sort_order`) 
																	VALUES (1, 0, 'game_currency_store', 1, 2000),
																	(2, 1, 'power_leveling_store', 1, 3000),
																	(3, 2, 'game_card_store', 1, 1000),
																	(4, 3, 'store_credit', 0, 3500),
																	(5, 4, 'high_level_account_store', 1, 4000),
																	(6, 2, 'game_tools', 1, 5000);"
													);

$add_new_tables["custom_products_type_child_lang"] = array (	"structure" => "CREATE TABLE `custom_products_type_child_lang` (
																				  `custom_products_type_child_id` int(11) NOT NULL default '0',
																				  `languages_id` int(11) NOT NULL default '0',
																				  `custom_products_type_child_name` varchar(64) NOT NULL default '',
																				  PRIMARY KEY  (`custom_products_type_child_id`,`languages_id`)
																				) ENGINE=MyISAM;",
																"data" => "INSERT INTO `custom_products_type_child_lang` (`custom_products_type_child_id`, `languages_id`, `custom_products_type_child_name`) 
																			VALUES (1, 1, 'Game Currency Store'),
																			(1, 2, '&#28216;&#25103;&#36135;&#24065;&#21830;&#38138;'),
																			(1, 3, '&#36938;&#25138;&#36008;&#24163;&#21830;&#33302;'),
																			(2, 1, 'Power Leveling Store'),
																			(2, 2, '&#28216;&#25103;&#20195;&#32451;&#21830;&#38138;'),
																			(2, 3, '&#36938;&#25138;&#20195;&#32244;&#21830;&#33302;'),
																			(3, 1, 'Game Card Store'),
																			(3, 2, '&#28216;&#25103;&#28857;&#21345;&#21830;&#38138;'),
																			(3, 3, '&#36938;&#25138;&#40670;&#21345;&#21830;&#33302;'),
																			(4, 1, 'Store Credit'),
																			(4, 2, 'Store Credit (zh-CN)'),
																			(4, 3, 'Store Credit (zh-TW)'),
																			(5, 1, 'High Level Account Store'),
																			(5, 2, '&#28216;&#25103;&#24080;&#21495;&#21830;&#38138;'),
																			(5, 3, '&#36938;&#25138;&#24115;&#34399;&#21830;&#33302;'),
																			(6, 1, 'Game Tools Store'),
																			(6, 2, '&#28216;&#25103;&#24037;&#20855;&#21830;&#38138;'),
																			(6, 3, '&#36938;&#25138;&#24037;&#20855;&#21830;&#33302;');"
															);

add_new_tables ($add_new_tables, $DBTables);
// End of create custom_products_type_child table

// Insert new fields into offgamers point related tables
$add_new_field = array();

$add_new_field['categories'] = array (	array (	"field_name" => "custom_products_type_child_id",
												"field_attr" => " int(11) NOT NULL default 999 ",
												"add_after" => "custom_products_type_id"
												)
									);

$add_new_field['categories_product_types'] = array (	array (	"field_name" => "custom_products_type_child_id",
																"field_attr" => " int(11) NOT NULL default 1 ",
																"add_after" => "custom_products_type_id"
																)
													);

add_field($add_new_field);
// End of insert new fields into offgamers point related tables

if (!in_array('custom_products_type_child_id', $existing_categories_fields)) {
	tep_db_query("UPDATE categories SET `custom_products_type_child_id` = `custom_products_type_id`;");
	
	tep_db_query("UPDATE categories SET `custom_products_type_child_id` = `custom_products_type_child_id` +1 WHERE `custom_products_type_child_id` !=999;");
		
	//categories_name or categories_short_name
	$latest_news_select_sql = "	SELECT ln.news_id, ln.custom_products_type 
								FROM ".TABLE_LATEST_NEWS." AS ln
								WHERE ln.custom_products_type != ''";
	$latest_news_result_sql = tep_db_query($latest_news_select_sql);
	while ($latest_news_row = tep_db_fetch_array($latest_news_result_sql)) {
		$temp_array = explode(",", $latest_news_row['custom_products_type']);
		foreach ($temp_array as $product_type) {
			$new_array[] = $product_type+1;
		}
		
		tep_db_perform(TABLE_LATEST_NEWS, array('custom_products_type' => implode(",", $new_array)), 'update', "news_id=".$latest_news_row['news_id']);
		unset($temp_array, $new_array);
	}
}

if (!in_array('custom_products_type_child_id', $existing_categories_product_types_fields)) {
	tep_db_query("UPDATE categories_product_types SET `custom_products_type_child_id` = `custom_products_type_id` +1 WHERE 1");
	
	// Reconstruct categories_product_types table
	// Drop existing primary key (categories_id, custom_products_type_id ) for categories_product_types table
	drop_index_key ("categories_product_types", 'PRIMARY KEY', 'primary', $DBTables, array('categories_id', 'custom_products_type_id'));
	// End of existing primary key (categories_id, custom_products_type_id ) for categories_product_types table
	
	// Insert new primary key field into categories_product_types table
	add_index_key ("categories_product_types", 'primary key', 'primary', 'categories_id, custom_products_type_id, custom_products_type_child_id', $DBTables);
	// End of insert new primary key field into categories_product_types table
}

if (in_array('custom_products_type_id', $existing_define_product_type_page_fields)) {
	tep_db_query("ALTER TABLE `define_product_type_page` CHANGE `custom_products_type_id` `custom_products_type_child_id` INT(11) NOT NULL DEFAULT '1';");
}
?>