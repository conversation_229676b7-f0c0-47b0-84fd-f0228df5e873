<?
/*
	$Id: version_3_12_3.php,v 1.1 2010/10/19 03:56:36 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_products_fields = get_table_fields('products');

// Define admin_groups_id as index key in admin table
add_index_key ('admin', 'index_group_id', 'index', 'admin_groups_id', $DBTables);
// End of define admin_groups_id as index key in admin table

// Define admin_files_to_boxes as index key in admin_files table
add_index_key ('admin_files', 'index_files_to_boxes', 'index', 'admin_files_to_boxes', $DBTables);
// End of define admin_files_to_boxes as index key in admin_files table


// Insert new fields into products and custom_products_type_child tables
$add_new_field = array();

$add_new_field['products'] = array (	array (	"field_name" => "custom_products_type_child_id",
												"field_attr" => " int(11) NOT NULL default 1 ",
												"add_after" => "custom_products_type_id"
												)
									);

$add_new_field['custom_products_type_child'] = array (	array (	"field_name" => "custom_products_type_child_name",
																"field_attr" => " varchar(64) default null ",
																"add_after" => "custom_products_type_child_url"
																)
													);

add_field($add_new_field);
// End of insert new fields into products and custom_products_type_child tables

if (!in_array('custom_products_type_child_id', $existing_products_fields)) {
	tep_db_query("update products set custom_products_type_child_id=custom_products_type_id;");
	tep_db_query("update products set custom_products_type_child_id=custom_products_type_child_id+1 where custom_products_type_child_id!=999;");
}

// Update custom_products_type_child_name in custom_products_type_child table
$aft_update_sql = array();

$aft_update_sql['custom_products_type_child'] = array(	array(	"field_name" => "custom_products_type_child_name",
																"update" => " custom_products_type_child_name = 'Game Currency' ",
																"where_str" => " custom_products_type_child_id=1"
																),
														array(	"field_name" => "custom_products_type_child_name",
																"update" => " custom_products_type_child_name = 'Power Leveling' ",
																"where_str" => " custom_products_type_child_id=2"
																),
														array(	"field_name" => "custom_products_type_child_name",
																"update" => " custom_products_type_child_name = 'CD Key' ",
																"where_str" => " custom_products_type_child_id=3"
																),
														array(	"field_name" => "custom_products_type_child_name",
																"update" => " custom_products_type_child_name = 'Store Credit' ",
																"where_str" => " custom_products_type_child_id=4"
																),
														array(	"field_name" => "custom_products_type_child_name",
																"update" => " custom_products_type_child_name = 'High Level Account' ",
																"where_str" => " custom_products_type_child_id=5"
																),
														array(	"field_name" => "custom_products_type_child_name",
																"update" => " custom_products_type_child_name = 'Game Tools' ",
																"where_str" => " custom_products_type_child_id=6"
																)
													 );

advance_update_records($aft_update_sql, $DBTables);
// End of update custom_products_type_child_name in custom_products_type_child table
?>