<?
/*
	$Id: version_3_12_4.php,v 1.8 2010/10/20 09:43:52 weesiong Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files_actions table (for permission on unlock and complete outgoing payment)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='payment.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["PAYMENT_MANUAL_UNLOCK_PAYMENT"] = array("insert" => " ('PAYMENT_MANUAL_UNLOCK_PAYMENT', 'Manual unlock locked outgoing payment', ".$row_sql["admin_files_id"].", '1', '20')" );
	$admin_files_actions_insert_sql["PAYMENT_MANUAL_COMPLETE_PAYMENT"] = array("insert" => " ('PAYMENT_MANUAL_COMPLETE_PAYMENT', 'Manual complete outgoing payment with API', ".$row_sql["admin_files_id"].", '1', '30')" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on unlock and complete outgoing payment)

// Insert new fields into payment_methods table
$add_new_field = array();

$add_new_field['payment_methods'] = array (	array (	"field_name" => "payment_methods_send_status_mode",
													"field_attr" => " tinyint(4) NOT NULL DEFAULT '0' ",
													"add_after" => "payment_methods_send_status"
													)
										);
add_field($add_new_field);
// End of insert new fields into payment_methods table


// Insert new fields into payment_methods_fields table
$add_new_field = array();

$add_new_field['payment_methods_fields'] = array (	array (	"field_name" => "payment_methods_fields_system_type",
															"field_attr" => " varchar(64) NOT NULL ",
															"add_after" => "payment_methods_fields_type"
															),
													array (	"field_name" => "payment_methods_fields_system_mandatory",
															"field_attr" => " varchar(32) NOT NULL ",
															"add_after" => "payment_methods_fields_sort_order"
															)
											);
											
$add_new_field['store_payments'] = array (	array (	"field_name" => "store_payments_lock",
													"field_attr" => " tinyint(1) NOT NULL DEFAULT '0' ",
													"add_after" => "store_payments_read_mode"
													)
									);
add_field($add_new_field);
// End of insert new fields into payment_methods_fields table

//// Change current outgoing payment methods' parent id
//$pm_to_pg_mapping_array = array(	'9' => '12',
//									'1' => '25',
//									'4' => '44',
//									'10' => '23',
//									'174' => '23',
//									'2' => '150',
//									'3' => '114',
//									'7' => '25',
//									'6' => '12',
//									'11' => '12',
//									'8' => '12');
//if (count($pm_to_pg_mapping_array)) {
//	foreach ($pm_to_pg_mapping_array as $pm_to_pg_mapping_key_loop => $pm_to_pg_mapping_data_loop) {
//		$payment_methods_data_sql = array(	'payment_methods_parent_id' => (int)$pm_to_pg_mapping_data_loop);
//		tep_db_perform(TABLE_PAYMENT_METHODS, $payment_methods_data_sql, 'update', " payment_methods_id = '".$pm_to_pg_mapping_key_loop."' ");
//	}
//}


// Create payment_methods_outgoing_instance and payment_methods_outgoing_instance_setting tables
$add_new_tables = array();

$add_new_tables["payment_methods_outgoing_instance"] = array (	"structure" => "CREATE TABLE `payment_methods_outgoing_instance` (
																				  `payment_methods_outgoing_instance_status` tinyint(4) NOT NULL default '0',
																				  `payment_methods_outgoing_instance_id` int(11) NOT NULL auto_increment,
																				  `payment_methods_id` int(11) NOT NULL default '0',
																				  `currency_code` char(3) NOT NULL default '0',
																				  `payment_methods_outgoing_instance_default` tinyint(1) NOT NULL default '0',
																				  `payment_methods_outgoing_instance_follow_default` int(11) NOT NULL default '0',
																				  PRIMARY KEY  (`payment_methods_outgoing_instance_id`),
																				  KEY `index_payment_methods_id_and_code` (`payment_methods_id`,`currency_code`)
																				) ENGINE=MyISAM;",
																"data" => ""
															);

$add_new_tables["payment_methods_outgoing_instance_setting"] = array (	"structure" => "CREATE TABLE `payment_methods_outgoing_instance_setting` (
																						  `payment_methods_outgoing_instance_setting_id` int(11) NOT NULL auto_increment,
																						  `payment_methods_outgoing_instance_id` int(11) NOT NULL default '0',
																						  `payment_methods_outgoing_instance_setting_key` varchar(255) NOT NULL default '1',
																						  `payment_methods_outgoing_instance_setting_value` text NOT NULL,
																						  PRIMARY KEY  (`payment_methods_outgoing_instance_setting_id`),
																						  KEY `index_instance_id_and_key` (`payment_methods_outgoing_instance_id`,`payment_methods_outgoing_instance_setting_key`)
																						) ENGINE=MyISAM;",
																		"data" => ""
																	);

add_new_tables ($add_new_tables, $DBTables);
// End of create payment_methods_outgoing_instance and payment_methods_outgoing_instance_setting tables

// Copy outgoing currency from payment_methods to payment_methods_outgoing_instance
$payment_methods_select_sql = "	SELECT payment_methods_id, payment_methods_send_currency 
								FROM " . TABLE_PAYMENT_METHODS . "
								WHERE payment_methods_send_status = 1
									AND payment_methods_types_id <> '6'";
$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
	$payment_gateway_instance_select_sql = "SELECT pmi.payment_methods_outgoing_instance_id, pmi.payment_methods_outgoing_instance_follow_default, pmi.currency_code 
			 								FROM " . TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE . " as pmi
			 								WHERE pmi.payment_methods_id = '" . (int)$payment_methods_row['payment_methods_id'] . "'";
	$payment_gateway_instance_result_sql = tep_db_query($payment_gateway_instance_select_sql);
	if (!tep_db_num_rows($payment_gateway_instance_result_sql)) {
		$currency_select_sql = "SELECT c.code 
								FROM " . TABLE_CURRENCIES . " as c
								WHERE currencies_id = '".(int)$payment_methods_row['payment_methods_send_currency']."'";
		$currency_result_sql = tep_db_query($currency_select_sql);
		$currency_row = tep_db_fetch_array($currency_result_sql);
		
		$payment_gateway_instance_data_sql = array(	'payment_methods_id' => (int)$payment_methods_row['payment_methods_id'],
													'currency_code' => tep_db_prepare_input($currency_row['code']));
		tep_db_perform(TABLE_PAYMENT_METHODS_OUTGOING_INSTANCE, $payment_gateway_instance_data_sql);
	}
}

$payment_gateway_select_sql = "	SELECT payment_methods_id
								FROM " . TABLE_PAYMENT_METHODS . "
								WHERE payment_methods_filename = 'paypal.php'";
$payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql);
$payment_gateway_row = tep_db_fetch_array($payment_gateway_result_sql);

$install_send_payment_key_array = array(	'payment_methods_fields_title'=>'PayPal Email',
											'payment_methods_fields_pre_info'=>'',
											'payment_methods_fields_post_info'=>'',
											'payment_methods_fields_required'=>1,
											'payment_methods_fields_type'=>'1',
											'payment_methods_fields_system_type'=>'MODULE_PAYPAL_SEND_EMAIL',
											'payment_methods_fields_size'=>'55',
											'payment_methods_fields_option'=>'NULL',
											'payment_methods_fields_options_title'=>'0',
											'payment_methods_fields_sort_order'=>0
										);

$paypal_select_sql = "	SELECT payment_methods_id
						FROM " . TABLE_PAYMENT_METHODS . "
						WHERE payment_methods_parent_id = '".$payment_gateway_row['payment_methods_id']."'";
$paypal_result_sql = tep_db_query($paypal_select_sql);
while ($paypal_row = tep_db_fetch_array($paypal_result_sql)) {
	$payment_methods_fields_select_sql = "	SELECT payment_methods_fields_system_type
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . " 
											WHERE payment_methods_fields_system_type = 'MODULE_PAYPAL_SEND_EMAIL'
												AND payment_methods_id = '".(int)$paypal_row['payment_methods_id']."'
												AND payment_methods_mode = 'SEND'";
	$payment_methods_fields_result_sql = tep_db_query($payment_methods_fields_select_sql);
	if (!tep_db_num_rows($payment_methods_fields_result_sql)) {
		$data = $install_send_payment_key_array;
		$data['payment_methods_id'] = (int)$paypal_row['payment_methods_id'];
		$data['payment_methods_mode'] = 'SEND';
		$data['payment_methods_fields_system_mandatory'] = 1;
		$data['payment_methods_fields_status']='1';
		tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $data);
	}
}

$payment_methods_data_sql = array(	"payment_methods_send_status_mode" =>  1);
tep_db_perform(TABLE_PAYMENT_METHODS, $payment_methods_data_sql, 'update', " payment_methods_send_status = '1' ");

//Include missing update for version 3.12.2 where custom_products_type_child_id was not added with 1.
$check_custom_products_type_child_id_select_sql = "	SELECT custom_products_type_child_id 
													FROM ".TABLE_DEFINE_PRODUCT_TYPE_PAGE."
													WHERE custom_products_type_child_id = 5
													LIMIT 1";
$check_custom_products_type_child_id_result_sql = tep_db_query($check_custom_products_type_child_id_select_sql);
if (!tep_db_num_rows($check_custom_products_type_child_id_result_sql)) {
	tep_db_query("UPDATE define_product_type_page SET `custom_products_type_child_id` = `custom_products_type_child_id` +1 WHERE custom_products_type_child_id <= 4 ORDER BY custom_products_type_child_id desc");
}

?>