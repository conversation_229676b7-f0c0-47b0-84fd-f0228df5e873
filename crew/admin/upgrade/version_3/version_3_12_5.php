<?
/*
	$Id: version_3_12_5.php,v 1.2 2010/10/27 03:41:58 weichen Exp $
	
  	Developer: Wei <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert View Customer Email permission
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='buyback_requests.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["BUYBACK_VIEW_CUSTOMER_EMAIL"] = array("insert" => " ('BUYBACK_VIEW_CUSTOMER_EMAIL', 'View Customer Email', ".$row_sql["admin_files_id"].", '1', 20)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert View Customer Email permission

// Insert View Customer Email permission
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='order_track.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_TRACK_VIEW_CUSTOMER_EMAIL"] = array("insert" => " ('ORDER_TRACK_VIEW_CUSTOMER_EMAIL', 'View Customer Email', ".$row_sql["admin_files_id"].", '1', 20)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert View Customer Email permission

// Insert View Customer Contact Info permission per Order Status
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_CUSTOMER_CONTACT_1"] = array("insert" => " ('ORDER_CUSTOMER_CONTACT_1', 'View Customer Contact Info for Pending Order', ".$row_sql["admin_files_id"].", '1', 3)" );
	$admin_files_actions_insert_sql["ORDER_CUSTOMER_CONTACT_2"] = array("insert" => " ('ORDER_CUSTOMER_CONTACT_2', 'View Customer Contact Info for Processing Order', ".$row_sql["admin_files_id"].", '1', 3)" );
	$admin_files_actions_insert_sql["ORDER_CUSTOMER_CONTACT_3"] = array("insert" => " ('ORDER_CUSTOMER_CONTACT_3', 'View Customer Contact Info for Completed Order', ".$row_sql["admin_files_id"].", '1', 3)" );
	$admin_files_actions_insert_sql["ORDER_CUSTOMER_CONTACT_5"] = array("insert" => " ('ORDER_CUSTOMER_CONTACT_5', 'View Customer Contact Info for Canceled Order', ".$row_sql["admin_files_id"].", '1', 3)" );
	$admin_files_actions_insert_sql["ORDER_CUSTOMER_CONTACT_7"] = array("insert" => " ('ORDER_CUSTOMER_CONTACT_7', 'View Customer Contact Info for Verifying Order', ".$row_sql["admin_files_id"].", '1', 3)" );
	$admin_files_actions_insert_sql["ORDER_CUSTOMER_CONTACT_8"] = array("insert" => " ('ORDER_CUSTOMER_CONTACT_8', 'View Customer Contact Info for On Hold Order', ".$row_sql["admin_files_id"].", '1', 3)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert View Customer Contact Info permission per Order Status

// Create new table for Archive Store Payment Account Book (Merging PayPal into PayPal Mass Payment
$add_new_tables = array();

$add_new_tables["archive_store_payment_account_book"] = array (	"structure" => "CREATE TABLE `archive_store_payment_account_book` (
																				  `store_payment_account_book_id` int(11) NOT NULL auto_increment,
																				  `user_id` int(11) NOT NULL default '0',
																				  `user_role` varchar(16) NOT NULL default '',
																				  `payment_methods_id` int(11) NOT NULL default '0',
																				  `payment_methods_alias` varchar(32) NOT NULL default '',
																				  `store_payment_account_book_primary` tinyint(1) NOT NULL default '0',
																				  PRIMARY KEY (`store_payment_account_book_id`),
																				  KEY `index_role_and_user_id` (`user_role`,`user_id`)
																				) ENGINE=MyISAM;" ,
																"data" => ""
															);

$add_new_tables["archive_store_payment_account_book_details"] = array (	"structure" => "CREATE TABLE `archive_store_payment_account_book_details` (
																						  `store_payment_account_book_id` int(11) NOT NULL default '0',
																						  `payment_methods_fields_id` int(11) NOT NULL default '0',
																						  `payment_methods_fields_value` text,
																						  PRIMARY KEY  (`store_payment_account_book_id`,`payment_methods_fields_id`),
																						  KEY `idx_store_payment_account_book_id` (`store_payment_account_book_id`)
																						) ENGINE=MyISAM;" ,
																		"data" => ""
																	);

add_new_tables ($add_new_tables, $DBTables);
// End of create new table for Archive Store Payment Account Book (Merging PayPal into PayPal Mass Payment
?>