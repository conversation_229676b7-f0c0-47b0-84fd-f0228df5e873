<?
/*
	$Id: version_3_12_6.php,v 1.4 2010/11/01 11:33:17 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Wee <PERSON>ong
// Insert new records into configuration table (for CO and BO Tagging)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_CO_MATCHES_BO_TM_TAG_ID"] = array("insert" => " ('CO & BO has same Threat Metrix ID Tag IDs', 'BUYBACK_CO_MATCHES_BO_TM_TAG_ID', '', 'The ID of the order tag used to indicate Customer Order has same Threat Metrix ID with Buyback Order. Entered in \"CO Tag ID, BO Tag ID\" format.', ".$row_sql["configuration_group_id"].", 140, NULL, now(), NULL, '')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for CO and BO Tagging)

// Insert new records into configuration_group table (for Threat Metrix module)
$configuration_group_insert_sql = array();

$configuration_group_insert_sql["Threat Metrix"] = array	(	"insert" => " ('Threat Metrix', 'Threat Metrix Configuration', 260, 1) ",
																"update" => " sort_order=260, visible='1' "
						   									);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");
// End of insert new records into configuration_group table (for Threat Metrix module)

// Insert new records into configuration table (for Threat Metrix module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Threat Metrix'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["TM_SERVICE_ENABLED"] = array("insert" => " ('Enable Threat Metrix', 'TM_SERVICE_ENABLED', 'false', 'Enable Threat Metrix Checking?', ".$row_sql["configuration_group_id"].", 1, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	$conf_insert_sql["TM_SERVICE_MODE"] = array("insert" => " ('Threat Metrix Mode', 'TM_SERVICE_MODE', 'Testing', 'Threat Metrix Mode', ".$row_sql["configuration_group_id"].", 2, NULL, now(), NULL, 'tep_cfg_select_option(array(\'Testing\', \'Live\'),')" );
	$conf_insert_sql["TM_ORG_ID"] = array("insert" => " ('Threat Metrix Organization ID', 'TM_ORG_ID', '', 'Threat Metrix Organization ID', ".$row_sql["configuration_group_id"].", 5, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["TM_API_Key"] = array("insert" => " ('Threat Metrix API Key', 'TM_API_Key', '', 'Threat Metrix API Key', ".$row_sql["configuration_group_id"].", 10, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Threat Metrix module)

// Insert View Device ID permission
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_VIEW_DEVICE_ID_INFO"] = array("insert" => " ('CUSTOMER_VIEW_DEVICE_ID_INFO', 'View Threat Metrix Device ID Info', ".$row_sql["admin_files_id"].", '1', 15)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert View View Device ID permission

// Create new table for Threat Metrix Module
$add_new_tables = array();

$add_new_tables["api_tm_browser"] = array (	"structure" => "CREATE TABLE `api_tm_browser` (
															  `api_tm_query_id` int(11) unsigned NOT NULL default '0',
															  `browser_language` varchar(32) NOT NULL default '',
															  `browser_string` varchar(200) NOT NULL default '',
															  `enabled_js` char(3) NOT NULL default '',
															  `enabled_fl` char(3) NOT NULL default '',
															  `enabled_ck` char(3) NOT NULL default '',
															  `enabled_im` char(3) NOT NULL default '',
															  `css_image_loaded` varchar(32) NOT NULL default '',
															  `flash_version` varchar(32) NOT NULL default '',
															  `flash_lang` varchar(10) NOT NULL default '',
															  `flash_os` varchar(32) NOT NULL default '',
															  `headers_name_value_hash` varchar(36) NOT NULL default '',
															  `headers_order_string_hash` varchar(36) NOT NULL default '',
															  `http_os_signature` varchar(32) NOT NULL default '',
															  `http_referer` varchar(36) NOT NULL default '',
															  `plugin_adobe_acrobat` varchar(32) NOT NULL default '',
															  `plugin_flash` varchar(32) NOT NULL default '',
															  `plugin_hash` varchar(36) NOT NULL default '',
															  `plugin_silverlight` varchar(32) NOT NULL default '',
															  PRIMARY KEY  (`api_tm_query_id`)
															) ENGINE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["api_tm_device"] = array (	"structure" => "CREATE TABLE `api_tm_device` (
															  `api_tm_query_id` int(11) unsigned NOT NULL default '0',
															  `device_id` varchar(36) NOT NULL default '',
															  `device_result` varchar(10) NOT NULL default '',
															  `os` varchar(32) NOT NULL default '',
															  `screen_res` varchar(12) NOT NULL default '',
															  `local_time_offset` decimal(11,6) NOT NULL default '0.000000',
															  `local_time_offset_range` decimal(11,6) NOT NULL default '0.000000',
															  `time_zone` int(5) NOT NULL default '0',
															  `device_score` int(3) NOT NULL default '0',
															  `device_attributes` varchar(64) NOT NULL default '',
															  `device_activities` varchar(64) NOT NULL default '',
															  `device_assert_history` varchar(64) NOT NULL default '',
															  `device_last_update` datetime default NULL,
															  `device_worst_score` int(3) NOT NULL default '0',
															  `profiling_datetime` int(11) unsigned NOT NULL default '0',
															  `device_first_seen` datetime default NULL,
															  `device_last_event` datetime default NULL,
															  `device_match_result` varchar(10) NOT NULL default '',
															  `offset_measure_time` int(11) unsigned NOT NULL default '0',
															  `os_anomaly` char(3) NOT NULL default '',
															  `os_fonts_hash` varchar(36) NOT NULL default '',
															  `os_fonts_number` varchar(10) NOT NULL default '',
															  PRIMARY KEY  (`api_tm_query_id`)
															) ENGINE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["api_tm_proxy_ip"] = array (	"structure" => "CREATE TABLE `api_tm_proxy_ip` (
																  `api_tm_query_id` int(11) unsigned NOT NULL default '0',
																  `proxy_ip` varchar(64) NOT NULL default '',
																  `proxy_ip_score` int(3) NOT NULL default '0',
																  `proxy_ip_attributes` varchar(64) NOT NULL default '',
																  `proxy_ip_activities` varchar(64) NOT NULL default '',
																  `proxy_ip_assert_history` varchar(50) NOT NULL default '',
																  `proxy_ip_last_update` datetime default NULL,
																  `proxy_ip_worst_score` int(3) NOT NULL default '0',
																  `proxy_ip_city` varchar(50) NOT NULL default '',
																  `proxy_ip_geo` char(2) NOT NULL default '',
																  `proxy_ip_isp` varchar(50) NOT NULL default '',
																  `proxy_ip_latitude` decimal(9,6) NOT NULL default '0.000000',
																  `proxy_ip_longitude` decimal(9,6) NOT NULL default '0.000000',
																  `proxy_type` varchar(32) NOT NULL default '',
																  `proxy_ip_first_seen` datetime default NULL,
																  `proxy_ip_last_event` datetime default NULL,
																  `proxy_ip_organization` varchar(100) NOT NULL default '',
																  `proxy_ip_region` varchar(32) NOT NULL default '',
																  `proxy_ip_result` varchar(10) NOT NULL default '',
																  PRIMARY KEY  (`api_tm_query_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["api_tm_risk_summary_n_policy"] = array (	"structure" => "CREATE TABLE `api_tm_risk_summary_n_policy` (
																			  `api_tm_query_id` int(11) unsigned NOT NULL default '0',
																			  `summary_risk_score` int(3) NOT NULL default '0',
																			  `policy_score` int(3) NOT NULL default '0',
																			  `reason_code` varchar(64) NOT NULL default '',
																			  PRIMARY KEY  (`api_tm_query_id`)
																			) ENGINE=MyISAM;" ,
															"data" => ""
														);

$add_new_tables["api_tm_transaction_identifier"] = array (	"structure" => "CREATE TABLE `api_tm_transaction_identifier` (
																			  `api_tm_query_id` int(11) unsigned NOT NULL auto_increment,
																			  `transaction_id` int(11) NOT NULL default '0',
																			  `customers_id` int(11) NOT NULL default '0',
																			  `customers_login_ip` varchar(20) NOT NULL default '',
																			  `customers_login_date` datetime NOT NULL default '0000-00-00 00:00:00',
																			  `request_result` varchar(64) NOT NULL default '',
																			  `request_id` varchar(64) NOT NULL default '',
																			  `transaction_type` char(2) NOT NULL default '',
																			  `device_id` varchar(36) NOT NULL default '',
																			  `create_datetime` datetime NOT NULL default '0000-00-00 00:00:00',
																			  `query_string` text NOT NULL,
																			  `missing_field_bk` text NOT NULL,
																			  PRIMARY KEY  (`api_tm_query_id`)
																			) ENGINE=MyISAM;" ,
															"data" => ""
														);

$add_new_tables["api_tm_true_ip"] = array (	"structure" => "CREATE TABLE `api_tm_true_ip` (
															  `api_tm_query_id` int(11) unsigned NOT NULL default '0',
															  `true_ip` varchar(64) NOT NULL default '',
															  `true_ip_activities` varchar(64) NOT NULL default '',
															  `true_ip_assert_history` char(3) NOT NULL default '',
															  `true_ip_attributes` varchar(64) NOT NULL default '',
															  `true_ip_city` varchar(50) NOT NULL default '',
															  `true_ip_geo` char(2) NOT NULL default '',
															  `true_ip_isp` varchar(50) NOT NULL default '',
															  `true_ip_last_update` datetime default NULL,
															  `true_ip_latitude` decimal(9,6) NOT NULL default '0.000000',
															  `true_ip_longitude` decimal(9,6) NOT NULL default '0.000000',
															  `true_ip_worst_score` int(3) NOT NULL default '0',
															  `true_ip_score` int(3) NOT NULL default '0',
															  `true_ip_first_seen` datetime default NULL,
															  `true_ip_last_event` datetime default NULL,
															  `true_ip_organization` varchar(100) NOT NULL default '',
															  `true_ip_region` varchar(32) NOT NULL default '',
															  `true_ip_result` varchar(10) NOT NULL default '',
															  PRIMARY KEY  (`api_tm_query_id`)
															) ENGINE=MyISAM;" ,
											"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create new table for Threat Metrix Module

// Insert new fields into products_hla_characters table
$add_new_field = array();

$add_new_field['products_hla_characters'] = array (	array (	"field_name" => "products_hla_characters_url",
															"field_attr" => "varchar(255) DEFAULT NULL",
															"add_after" => "products_ref_id"
															)
												);
add_field($add_new_field);
// End of insert new fields into products_hla_characters table
?>