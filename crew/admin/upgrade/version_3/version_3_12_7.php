<?
/*
	$Id: version_3_12_7.php,v 1.1 2010/11/08 08:18:41 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON><PERSON>
// Delete mozcom_digest field from mozcom table
$delete_field = array();

$delete_field["mozcom"] = array  (	array( "field_name" => "mozcom_digest") );

delete_field ($delete_field);
// End of delete mozcom_digest field from mozcom table

// Kee Peng
// Define products_hla_attributes_type and products_hla_value as index key in products_hla_attributes table
add_index_key ('products_hla_attributes', 'index_type_and_value', 'index', 'products_hla_attributes_type, products_hla_value', $DBTables);
// End of define products_hla_attributes_type and products_hla_value as index key in products_hla_attributes table

// Wei Chen
// Define transaction_id, customers_id and device_id as index key in api_tm_transaction_identifier table
add_index_key ('api_tm_transaction_identifier', 'index_trans_id', 'index', 'transaction_id', $DBTables);
add_index_key ('api_tm_transaction_identifier', 'index_customers_id', 'index', 'customers_id', $DBTables);
add_index_key ('api_tm_transaction_identifier', 'index_device_id', 'index', 'device_id', $DBTables);
// End of define transaction_id, customers_id and device_id as index key in api_tm_transaction_identifier table

// Wee Siong
// Create new table for Threat Metrix Module
$add_new_tables = array();

$add_new_tables["api_tm_query_queue"] = array (	"structure" => "CREATE TABLE `api_tm_query_queue` (
																	`orders_id` bigint(11) UNSIGNED NOT NULL ,
																	`transaction_type` char(2) not null,
																	`query_string` text NOT NULL ,
																	`extra_info` text NOT NULL ,
																	`created_datetime` datetime NOT NULL ,
																	PRIMARY KEY (`orders_id`)
																) ENGINE = MYISAM;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create new table for Threat Metrix Module
?>