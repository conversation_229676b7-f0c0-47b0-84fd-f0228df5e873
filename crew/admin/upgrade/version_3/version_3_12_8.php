<?
/*
	$Id: version_3_12_8.php,v 1.1 2010/11/17 04:06:32 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into custom_products_type_child table (for Direct Top-up)
$custom_products_type_child_select_sql = "	SELECT custom_products_type_child_id 	
											FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
											WHERE custom_products_type_child_id = '7'";
$custom_products_type_child_result_sql = tep_db_query($custom_products_type_child_select_sql);
if (!tep_db_num_rows($custom_products_type_child_result_sql)) {
	$custom_products_type_child_data_array = array(	'custom_products_type_id' => '2',
													'custom_products_type_child_url' => 'game_card_direct_top_up',
													'custom_products_type_child_name' => 'Direct Top-up',
													'display_status' => '0',
													'sort_order' => 1500
							                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, $custom_products_type_child_data_array);
	
	for ($lng_cnt = 1; $lng_cnt < 4; $lng_cnt++) {
		$data_array = array(	'custom_products_type_child_id' => '7',
								'languages_id' => $lng_cnt,
								'custom_products_type_child_name' => 'Direct Top-up'
		                       );
		tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	}
}
// End of insert new records into custom_products_type_child table (for Direct Top-up)

// Insert new records into custom_products_type_child table (for Facebook Credit)
$custom_products_type_child_select_sql = "	SELECT custom_products_type_child_id 	
											FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
											WHERE custom_products_type_child_id = '8'";
$custom_products_type_child_result_sql = tep_db_query($custom_products_type_child_select_sql);
if (!tep_db_num_rows($custom_products_type_child_result_sql)) {
	$custom_products_type_child_data_array = array(	'custom_products_type_id' => '2',
													'custom_products_type_child_url' => 'facebook_credit',
													'custom_products_type_child_name' => 'Facebook Credit',
													'display_status' => '0',
													'sort_order' => 5500
							                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, $custom_products_type_child_data_array);
	
	for ($lng_cnt = 1; $lng_cnt < 4; $lng_cnt++) {
		$data_array = array(	'custom_products_type_child_id' => '8',
								'languages_id' => $lng_cnt,
								'custom_products_type_child_name' => 'Facebook Credit'
		                       );
		tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	}
}
// End of insert new records into custom_products_type_child table (for Facebook Credit)
?>