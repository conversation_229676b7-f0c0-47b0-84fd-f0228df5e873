<?
/*
	$Id: version_3_12_9.php,v 1.1 2010/11/17 10:42:03 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Facebook Connect'";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["KT_SERVICE_ENABLED"] = array("insert" => " ('Enable Kontagent', 'KT_SERVICE_ENABLED', 'false', 'Enable Kontagent Analytics?', ".$row_sql["configuration_group_id"].", 20, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	$conf_insert_sql["KT_API_KEY"] = array("insert" => " ('Kontagent API Key', 'KT_API_KEY', '', 'Kontagent API Key', ".$row_sql["configuration_group_id"].", 25, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["KT_API_SERVER"] = array("insert" => " ('Kontagent API Server', 'KT_API_SERVER', 'test-server.kontagent.com', 'Kontagent API Server', ".$row_sql["configuration_group_id"].", 30, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
?>