<?
/*
	$Id: version_3_13.php,v 1.1 2010/11/15 03:30:45 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new fields into products_hla table (table exist in Phase-1)
$add_new_field = array();

$add_new_field['products_hla'] = array (array (	"field_name" => "products_hla_reserve_id",
												"field_attr" => "varchar(128) DEFAULT NULL",
												"add_after" => "created_date"
												)
										);

add_field($add_new_field);
// End of insert new fields into products_hla table (table exist in Phase-1)

// create new table for HLA [start]
$add_new_tables = array();

$add_new_tables["products_supplier"] = array ("structure" => "	CREATE TABLE `products_supplier` (
																	`supplier_id` int(11) NOT NULL,
																	`supplier_code` varchar(8) NOT NULL,
																	`payout_percentage` int(3) DEFAULT '0',
																	`payout_grace_period_offset` int(11) NOT NULL DEFAULT '0',
																	`reserve_account_api` enum('0','1') DEFAULT '0',
																	`retrieve_account_info_api` enum('0','1') DEFAULT '0',
																	`api_key` varchar(128) DEFAULT NULL,
																	`supplier_status` enum('0','1') NOT NULL,
																	PRIMARY KEY (`supplier_id`)
																) ENGINE=MyISAM;" ,
													"data" => ""
													);

$add_new_tables["products_to_supplier"] = array (	"structure" => "CREATE TABLE `products_to_supplier` (
																	`products_to_supplier_id` int(11) NOT NULL AUTO_INCREMENT,
																	`supplier_id` int(11) NOT NULL DEFAULT 0,
																	`products_id` int(11) NOT NULL DEFAULT 0,
																	PRIMARY KEY (`products_to_supplier_id`)
																	) ENGINE=MyISAM;",
													"data" => ""
												);

$add_new_tables["products_rss_link"] = array (	"structure" => "CREATE TABLE `products_rss_link` (
																	`products_rss_link_id` int(11) NOT NULL AUTO_INCREMENT,
																	`products_to_supplier_id` int(11) NOT NULL DEFAULT 0,
																	`url` varchar(255) DEFAULT NULL,
																	PRIMARY KEY (`products_rss_link_id`)
																) ENGINE=MyISAM;",
												"data" => ""
											);

$add_new_tables["cron_hla"] = array (	"structure" => "CREATE TABLE `cron_hla` (
														  `products_hla_id` int(11) unsigned NOT NULL DEFAULT '0',
														  `created_date` datetime DEFAULT NULL,
														  PRIMARY KEY (`products_hla_id`)
														) ENGINE=MyISAM;",
										"data" => ""
									);

$add_new_tables["products_hla_log"] = array (	"structure" => "CREATE TABLE `products_hla_log` (
																  `products_hla_log_id` int(11) NOT NULL AUTO_INCREMENT,
																  `products_hla_id` int(11) unsigned NOT NULL DEFAULT 0,
																  `log_date` datetime DEFAULT NULL,
																  `action` varchar(64) NOT NULL DEFAULT '',
																  `status` enum('0','1') NOT NULL DEFAULT '0',
																  `message` text,
																  `added_by` varchar(128) NOT NULL DEFAULT '',
																  PRIMARY KEY (`products_hla_log_id`),
																  KEY `index_products_hla_id` (`products_hla_id`)
																) ENGINE=MyISAM;",
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// create new table for HLA [end]

// Insert cron track record for cron_hla_account_reservation.php
$cron_track_rec_select_sql = "	SELECT cron_process_track_filename 
								FROM cron_process_track 
								WHERE cron_process_track_filename = 'cron_hla_account_reservation.php'";
$cron_track_rec_result_sql = tep_db_query($cron_track_rec_select_sql);

if (!tep_db_num_rows($cron_track_rec_result_sql)) {
	$cron_track_rec_data_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_hla_account_reservation.php'
				                       );
	
	tep_db_perform('cron_process_track', $cron_track_rec_data_array);
}
// End of insert cron track record for cron_hla_account_reservation.php

// Insert new records into admin_files table (for HLA Supplier Module)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='buyback.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["products_supplier.php"] = array(	"insert" => " ('products_supplier.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
								   							);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='products_supplier.php' AND admin_files_is_boxes=0 ");
}

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["popup_products_hla_log.php"] = array(	"insert" => " ('popup_products_hla_log.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
									   							);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='popup_products_hla_log.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for HLA Supplier Module)

// Insert new records into admin_files_categories table
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='products_supplier.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);

if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_categories_insert_sql = array();
	
	$admin_files_categories_insert_sql[$row_sql["admin_files_id"]] = array("insert" => " (".$row_sql["admin_files_id"].", '1', 0)" );
	insert_new_records(TABLE_ADMIN_FILES_CATEGORIES, "admin_files_id", $admin_files_categories_insert_sql, $DBTables, "(`admin_files_id`, `admin_groups_id`, `categories_ids`)", " admin_files_id='".$row_sql["admin_files_id"]."' AND admin_groups_id=1 ");
}
// End of insert new records into admin_files_categories table
?>