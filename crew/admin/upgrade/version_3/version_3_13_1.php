<?
/*
	$Id: version_3_13_1.php,v 1.1 2010/11/18 11:18:20 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// create new table for Direct Top Up [start]
$add_new_tables = array();

$add_new_tables["api_log"] = array (	"structure" => "CREATE TABLE `api_log` (
														  `api_log_id` int(10) unsigned NOT NULL auto_increment,
														  `action` varchar(32) NOT NULL,
														  `publishers_id` int(10) unsigned default NULL,
														  `orders_products_id` int(11) default NULL,
														  `publisher_ref_id` varchar(16) default NULL,
														  `top_up_id` int(10) unsigned default NULL,
														  `result_code` int(11) default NULL,
														  `request_log` text,
														  `response_log` text,
														  `request_start` datetime default NULL,
														  `request_end` datetime default NULL,
														  `ip_address` varchar(15) default NULL,
														  PRIMARY KEY (`api_log_id`),
														  KEY `index_publishers_id` (`publishers_id`)
														) ENGINE=MyISAM;",
										"data" => ""
									);

$add_new_tables["customers_top_up_info"] = array (	"structure" => "CREATE TABLE `customers_top_up_info` (
																	  `top_up_info_id` int(10) unsigned NOT NULL,
																	  `orders_products_id` int(11) NOT NULL,
																	  `top_up_value` varchar(255) NOT NULL,
																	  UNIQUE KEY `unique_top_up_info_id_and_orders_products_id` (`top_up_info_id`,`orders_products_id`),
																	  KEY `index_orders_products_id` (`orders_products_id`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
													);

$add_new_tables["orders_top_up"] = array (	"structure" => "CREATE TABLE `orders_top_up` (
															  `orders_products_id` int(11) NOT NULL COMMENT 'OGM order product ID',
															  `top_up_id` int(10) unsigned NOT NULL auto_increment COMMENT 'Top-up ID',
															  `publishers_ref_id` varchar(16) NOT NULL COMMENT 'Publisher reference ID',
															  `top_up_status` enum('1','3','10','11') NOT NULL default '1' COMMENT 'Top-up status, 1=Pending, 3=Processing, 10=Failed, 11=NotFound',
															  `publishers_response_time` datetime default NULL COMMENT 'Last response time from publisher, like top-up successful time',
															  `customer_before_balance` double default NULL COMMENT 'Customer''s Balance before top-up',
															  `customer_after_balance` double default NULL COMMENT 'Customer''s Balance after top-up',
															  `game` varchar(64) default NULL COMMENT 'Customer''s game to top-up',
															  `server` varchar(64) default NULL COMMENT 'Customer''s server to top-up',
															  `account` varchar(64) default NULL COMMENT 'Customer''s account to top-up',
															  `character` varchar(64) default NULL COMMENT 'Customer''s character to top-up',
															  `publishers_id` int(10) unsigned NOT NULL,
															  `top_up_process_flag` enum('0','1','2') NOT NULL default '0' COMMENT '0 = pending, 1 = processing, 2=complete',
															  `top_last_processed_time` datetime NOT NULL default '0000-00-00 00:00:00',
															  PRIMARY KEY (`top_up_id`),
															  UNIQUE KEY `unique_orders_products_id` (`orders_products_id`)
															) ENGINE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["orders_top_up_remark"] = array (	"structure" => "CREATE TABLE `orders_top_up_remark` (
																	  `orders_top_up_remark_id` int(10) unsigned NOT NULL auto_increment,
																	  `top_up_id` int(10) unsigned NOT NULL COMMENT 'Top-up ID',
																	  `data_added` datetime NOT NULL default '0000-00-00 00:00:00' COMMENT 'Record added time',
																	  `remark` text NOT NULL COMMENT 'Remark',
																	  PRIMARY KEY (`orders_top_up_remark_id`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["publishers"] = array (	"structure" => "CREATE TABLE `publishers` (
															  `publishers_id` int(10) unsigned NOT NULL auto_increment COMMENT 'Publisher ID',
															  `publishers_name` varchar(32) NOT NULL COMMENT 'Publisher Name',
															  `publishers_status` tinyint(4) NOT NULL COMMENT 'Publisher Status',
															  `last_modified` datetime default NULL COMMENT 'Last modified time',
															  `date_added` datetime NOT NULL default '0000-00-00 00:00:00' COMMENT 'Record added time',
															  `last_modified_by` int(11) NOT NULL,
															  `sort_order` int(11) NOT NULL default '50000' COMMENT 'sorting use',
															  PRIMARY KEY (`publishers_id`)
															) ENGINE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["publishers_configuration"] = array (	"structure" => "CREATE TABLE `publishers_configuration` (
																		  `publishers_configuration_id` int(10) unsigned NOT NULL auto_increment COMMENT 'ID',
																		  `publishers_id` int(10) unsigned NOT NULL COMMENT 'Publisher ID',
																		  `publishers_configuration_title` varchar(64) NOT NULL COMMENT 'Configuration Title, eg. Reminder Amount',
																		  `publishers_configuration_key` varchar(64) NOT NULL COMMENT 'Configuration Key',
																		  `publishers_configuration_value` text NOT NULL COMMENT 'Configuration Value, eg. 5000',
																		  `publishers_configuration_description` varchar(255) NOT NULL COMMENT 'Configuration Description, Min Amount for email notification',
																		  `sort_order` int(11) NOT NULL default '50000' COMMENT 'sorting',
																		  `last_modified` datetime default NULL COMMENT 'Last update time',
																		  `date_added` datetime NOT NULL default '0000-00-00 00:00:00' COMMENT 'Record added time',
																		  `last_modified_by` int(11) NOT NULL,
																		  `use_function` varchar(255) NOT NULL COMMENT 'function to integrate',
																		  `set_function` text NOT NULL COMMENT 'Input type, textbox, select box or ...',
																		  PRIMARY KEY (`publishers_configuration_id`),
																		  KEY `index_publishers_id` (`publishers_id`)
																		) ENGINE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables["publishers_games"] = array (	"structure" => "CREATE TABLE `publishers_games` (
																  `publishers_games_id` int(10) unsigned NOT NULL auto_increment,
																  `publishers_id` int(10) unsigned NOT NULL COMMENT 'Publisher ID',
																  `publishers_game` varchar(64) NOT NULL COMMENT 'Publisher Game',
																  `publishers_server` text NOT NULL COMMENT 'Publisher Servers in array (json)',
																  PRIMARY KEY (`publishers_games_id`),
																  UNIQUE KEY `unique_publishers_games` (`publishers_id`,`publishers_game`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["publishers_products"] = array (	"structure" => "CREATE TABLE `publishers_products` (
																	  `publishers_games_id` int(10) unsigned NOT NULL default '0',
																	  `products_id` int(11) NOT NULL default '0',
																	  PRIMARY KEY (`publishers_games_id`,`products_id`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["top_up_info"] = array (	"structure" => "CREATE TABLE `top_up_info` (
															  `top_up_info_id` int(10) unsigned NOT NULL auto_increment COMMENT 'ID',
															  `products_id` int(11) NOT NULL COMMENT 'Product ID',
															  `top_up_info_title` varchar(64) NOT NULL COMMENT 'Top-up info title, such as game account',
															  `top_up_info_key` varchar(64) NOT NULL COMMENT 'key to passover, eg. gameaccount',
															  `top_up_info_description` varchar(255) NOT NULL COMMENT 'Top-up description',
															  `top_up_info_value` varchar(255) default NULL COMMENT 'If type id is 1, then this value will be use for passing over, else will grab from customers_top_up_info',
															  `top_up_info_type_id` int(10) unsigned NOT NULL COMMENT 'Top-up info type, 1 or 2',
															  `sort_order` int(11) NOT NULL default '50000' COMMENT 'sorting',
															  `last_modified` datetime default NULL COMMENT 'Record updated time',
															  `date_added` datetime NOT NULL default '0000-00-00 00:00:00' COMMENT 'Record added time',
															  `last_modified_by` int(11) NOT NULL,
															  `use_function` varchar(255) NOT NULL COMMENT 'function to integrate',
															  `set_function` text NOT NULL COMMENT 'Input type, textbox, select box or ...',
															  PRIMARY KEY (`top_up_info_id`),
															  KEY `index_top_up_info_type_id` (`top_up_info_type_id`)
															) ENGINE=MyISAM;" ,
											"data" => ""
										);
										
$add_new_tables["top_up_info_lang"] = array (	"structure" => "CREATE TABLE `top_up_info_lang` (
																  `top_up_info_id` int(10) unsigned NOT NULL COMMENT 'Top up info ID',
																  `top_up_info_display` varchar(255) NOT NULL COMMENT 'Display value',
																  `languages_id` int(11) NOT NULL COMMENT 'Language ID',
																  PRIMARY KEY (`top_up_info_id`,`languages_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);
											
$add_new_tables["top_up_info_type"] = array (	"structure" => "CREATE TABLE `top_up_info_type` (
																  `top_up_info_type_id` int(10) unsigned NOT NULL auto_increment COMMENT 'ID',
																  `top_up_info_type_name` varchar(64) NOT NULL COMMENT 'Name',
																  PRIMARY KEY (`top_up_info_type_id`)
																) ENGINE=MyISAM;" ,
												"data" => "INSERT INTO `top_up_info_type` (`top_up_info_type_name`) 
															VALUES ('System Field'),('Display Field');"
											);
											
$add_new_tables["top_up_queue"] = array (	"structure" => "CREATE TABLE `top_up_queue` (
																`top_up_id` int( 10 ) unsigned NOT NULL AUTO_INCREMENT COMMENT 'OffGamers top-up ID',
																`counter` tinyint( 4 ) NOT NULL default '0',
																`check_date` datetime default NULL ,
																`date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																PRIMARY KEY ( `top_up_id` )
															) ENGINE = MYISAM;" ,
											"data" => ""
										);
										
add_new_tables ($add_new_tables, $DBTables);
// create new table for Direct Top Up [end]

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='data_pool.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	// Insert new records into admin_files table (for custom product payment)
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["publishers.php"] = array(	"insert" => " ('publishers.php', 0, '".$row_sql['admin_files_id']."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql['admin_files_id']."', admin_groups_id='1' "
		   											);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='publishers.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["publishers_games.php"] = array("insert" => " ('publishers_games.php', 0, '".$row_sql['admin_files_id']."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql['admin_files_id']."', admin_groups_id='1' "
		   											);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='publishers_games.php' AND admin_files_is_boxes=0 ");
	// End of insert new records into admin_files table (for custom product payment)	
}

// Insert new fields into categories_id, publishers_games_pending_message, publishers_games_reloaded_message, publishers_games_failed_message, publishers_games_remark to related tables publishers_games
// Insert new fields into publishers_remark to related tables publishers
$add_new_field = array();
$add_new_field['publishers_games'] = array (	array (	"field_name" => "categories_id",
														"field_attr" => " int(11) NOT NULL default 0 ",
														"add_after" => "publishers_game"
														),
												array (	"field_name" => "publishers_games_pending_message",
														"field_attr" => " varchar(255) default NULL ",
														"add_after" => "categories_id"
														),
												array (	"field_name" => "publishers_games_reloaded_message",
														"field_attr" => " varchar(255) default NULL ",
														"add_after" => "publishers_games_pending_message"
														),
												array (	"field_name" => "publishers_games_failed_message",
														"field_attr" => " varchar(255) default NULL ",
														"add_after" => "publishers_games_reloaded_message"
														),
												array (	"field_name" => "publishers_games_remark",
														"field_attr" => " varchar(255) default NULL ",
														"add_after" => "publishers_games_failed_message"
														)
									);

$add_new_field['publishers'] = array (	array (	"field_name" => "publishers_remark",
												"field_attr" => " varchar(255) default NULL  ",
												"add_after" => "publishers_status"
												)
									);

$add_new_field['orders_top_up'] = array (	array (	"field_name" => "result_code",
													"field_attr" => " int(11) NOT NULL ",
													"add_after" => "top_up_process_flag"
													)
										);
add_field($add_new_field);
// End of insert new fields into related tables

// Boon Hock
// Insert new records into aft_functions table
$aft_functions_insert_sql = array();

$aft_functions_insert_sql["get_reversible_fund_percentage"] = array("insert" => " ('get_reversible_fund_percentage', 'Reversible fund percentage', 'Get reversible fund percentage. Return ''0-100''.', '0', '0', '')" );
$aft_functions_insert_sql["is_paypal_country_matched_billing_country"] = array("insert" => " ('is_paypal_country_matched_billing_country', 'Is PayPal country matched with billing country?', 'Match PayPal returned country with customer billing country. Return ''true'' or ''false''.', '0', '0', '')" );
$aft_functions_insert_sql["is_paypal_name_matched_profile_name"] = array("insert" => " ('is_paypal_name_matched_profile_name', 'Is PayPal payer name matched with profile country?', 'Match PayPal returned payer name with customer profile''s name. Return ''true'' or ''false''.', '0', '0', '')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_functions_insert_sql, $DBTables, "(`aft_functions_name`, `aft_functions_display_name`, `aft_functions_description`, `aft_functions_action`, `line_determine`, `aft_functions_setting`)");
// End of insert new records into aft_functions table

// Delete popup_products_hla_log.php from admin_files table
$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'popup_products_hla_log.php'";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}
// End of delete popup_products_hla_log.php from admin_files table

// Delete payment_module.php from admin_files table
$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'payment_module.php'";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}
// End of delete payment_module.php from admin_files table
?>