<?
/*
	$Id: version_3_13_2.php,v 1.2 2010/11/30 10:37:26 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Change field structure for countries_ids field in vip_region_group table
$change_field_structure = array();

$change_field_structure['vip_region_group'] = array (array ("field_name" => "countries_ids",
															"field_attr" => " TEXT NOT NULL "
									 					)
												);

change_field_structure ($change_field_structure);
// End of change field structure for countries_ids field in vip_region_group table

// Insert new fields into coupons_generation and coupons table (For restrict by Customer Group)
$add_new_field = array();

$add_new_field['coupons_generation'] = array (array (	"field_name" => "restrict_to_customers_groups",
														"field_attr" => "varchar(64) NOT NULL DEFAULT 'ALL'",
														"add_after" => "restrict_to_customers"
													)
												);

$add_new_field['coupons'] = array (array (	"field_name" => "restrict_to_customers_groups",
											"field_attr" => "varchar(64) NOT NULL DEFAULT 'ALL'",
											"add_after" => "restrict_to_customers"
										)
									);

add_field($add_new_field);
// End of insert new fields into coupons_generation and coupons table (For restrict by Customer Group)
?>