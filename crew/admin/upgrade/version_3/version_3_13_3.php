<?
// FTP Project - SiongHuat
// Insert new records into admin_files table (for ftp user permission)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["image_upload.php"] = array(	"insert" => " ('image_upload.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => ""
						   								);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='image_upload.php' AND admin_files_is_boxes=0 ");
	
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["image_upload_xmlhttp.php"] = array(	"insert" => " ('image_upload_xmlhttp.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																	"update" => ""
														);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='image_upload_xmlhttp.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for ftp user permission)

// Create products_follow_price table
$add_new_tables = array();

$add_new_tables["image_configuration"] = array (	"structure" => "CREATE TABLE `image_configuration` (
																	`image_configuration_id` int(11) NOT NULL auto_increment,
																	`image_category` varchar(16) NOT NULL default '',
																	`file_path` varchar(80) NOT NULL default '',
																	`web_path` varchar(80) NOT NULL default '',
																	`user_groups` text NOT NULL,
																	PRIMARY KEY  (`image_configuration_id`)
																	)TYPE=MyISAM;",
													"data" => "INSERT INTO `image_configuration` (image_category, file_path, web_path, user_groups) 
															   VALUES ('newsletter', '/var/www/html/image.offgamers.com/newsletter', 'http://image.offgamers.com/newsletter', 1), 
																      ('buybackcn', '/var/www/html/image.offgamers.com/buybackcn', 'http://image.offgamers.com/buybackcn', 1), 
																      ('infolink', '/var/www/html/image.offgamers.com/infolink', 'http://image.offgamers.com/infolink', 1), 
																      ('event', '/var/www/html/image.offgamers.com/event', 'http://image.offgamers.com/event', 1), 
																      ('partner', '/var/www/html/image.offgamers.com/partner', 'http://image.offgamers.com/partner', 1), 
																      ('games', '/var/www/html/image.offgamers.com/games', 'http://image.offgamers.com/games', 1), 
																      ('publisher', '/var/www/html/image.offgamers.com/publisher', 'http://image.offgamers.com/publisher', 1), 
																      ('banners/1', '/var/www/html/image.offgamers.com/banners/1', 'http://image.offgamers.com/banners/1', 1), 
																      ('banners/2', '/var/www/html/image.offgamers.com/banners/2', 'http://image.offgamers.com/banners/2', 1)"
												);

$add_new_tables["api_tm_fuzzy_device"] = array (	"structure" => "CREATE TABLE `api_tm_fuzzy_device` (
																		`api_tm_query_id` INT( 11 ) UNSIGNED NOT NULL ,
																		`fuzzy_device_id` VARCHAR( 36 ) NOT NULL ,
																		`fuzzy_device_first_seen` DATE NOT NULL ,
																		`fuzzy_device_id_confidence` INT( 3 ) NOT NULL ,
																		`fuzzy_device_last_event` DATE NOT NULL ,
																		`fuzzy_device_last_update` DATE NOT NULL ,
																		`fuzzy_device_match_result` VARCHAR( 10 ) NOT NULL ,
																		`fuzzy_device_result` VARCHAR( 10 ) NOT NULL ,
																		`fuzzy_device_score` INT( 3 ) NOT NULL ,
																		`fuzzy_device_worst_score` INT( 3 ) NOT NULL
																	) TYPE = MYISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);

// Boon Hock
// Insert new fields into orders_top_up table
$add_new_field = array();
$add_new_field['orders_top_up'] = array ( array ( 	"field_name" => "top_up_timestamp",
													"field_attr" => " timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP ",
													"add_after" => "top_last_processed_time"
												)
										);
add_field($add_new_field);
// End of insert new fields into orders_top_up table

// Insert new fields into top_up_queue table
$add_new_field = array();
$add_new_field['top_up_queue'] = array ( array ( 	"field_name" => "top_up_queue_action",
													"field_attr" => " ENUM('0', '1') NOT NULL DEFAULT '0' ",
													"add_after" => "counter"
												)
										);
add_field($add_new_field);
// End of insert new fields into top_up_queue table
?>