<?
/*
	$Id: version_3_13_4.php,v 1.1 2010/12/17 03:14:39 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "100M");
tep_set_time_limit(0);

$existing_orders_fields = get_table_fields('orders');

// Insert new fields into orders_top_up table
$add_new_field = array();
$add_new_field['orders'] = array ( array ( 	"field_name" => "customers_groups_id",
											"field_attr" => " smallint(4) NOT NULL default '0' ",
											"add_after" => "customers_address_format_id"
										)
								);
add_field($add_new_field);
// End of insert new fields into orders_top_up table

// Define rhb_orders_id as index key in rhb_payment_status_history table
add_index_key ('rhb_payment_status_history', 'index_orders_id', 'index', 'rhb_orders_id', $DBTables);
// End of define rhb_orders_id as index key in rhb_payment_status_history table

// Define orders_products_id as index key in buyback_request table
add_index_key ('buyback_request', 'index_op_id', 'index', 'orders_products_id', $DBTables);
// End of define orders_products_id as index key in buyback_request table

// Define created_datetime as index key in api_tm_query_queue table
add_index_key ('api_tm_query_queue', 'index_created_datetime', 'index', 'created_datetime', $DBTables);
// End of define created_datetime as index key in api_tm_query_queue table

if (!in_array('customers_groups_id', $existing_orders_fields)) {
	$order_cust_grp_update_sql = "	UPDATE orders AS o 
									INNER JOIN customers AS c
										ON o.customers_id=c.customers_id
									SET o.customers_groups_id = c.customers_groups_id
									WHERE 1;";
	tep_db_query($order_cust_grp_update_sql);
}
?>