<?
/*
	$Id: version_3_13_5.php,v 1.2 2011/01/05 04:25:36 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Store Information'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	
	$conf_insert_sql = array();
	// Insert new records into configuration table (WWW Use Proxy Server)
	$conf_insert_sql["WWW_USE_PROXY_SERVER"] = array("insert" => " ('WWW Use Proxy Server', 'WWW_USE_PROXY_SERVER', 'false', 'WWW Use Proxy Server <br>(true=on false=off)', ".$row_sql["configuration_group_id"].", 75, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	// End of insert new records into configuration table (WWW Use Proxy Server)
	
	// Insert new records into configuration table (Crew Use Proxy Server)
	$conf_insert_sql["CREW_USE_PROXY_SERVER"] = array("insert" => " ('Crew Use Proxy Server', 'CREW_USE_PROXY_SERVER', 'false', 'Crew Use Proxy Server <br>(true=on false=off)', ".$row_sql["configuration_group_id"].", 75, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	// End of insert new records into configuration table (Crew Use Proxy Server)
	
	// Insert new records into configuration table (Supplier Use Proxy Server)
	$conf_insert_sql["SUPPLIER_USE_PROXY_SERVER"] = array("insert" => " ('Supplier Use Proxy Server', 'SUPPLIER_USE_PROXY_SERVER', 'false', 'Supplier Use Proxy Server <br>(true=on false=off)', ".$row_sql["configuration_group_id"].", 75, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	// End of insert new records into configuration table (Supplier Use Proxy Server)
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
?>