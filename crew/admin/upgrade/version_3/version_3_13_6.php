<?
/*
	$Id: version_3_13_6.php,v 1.2 2011/01/14 05:42:04 weichen Exp $
	
  	Developer: <PERSON> (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Dennis
// Create paynearme tables
$add_new_tables = array();

$add_new_tables["paynearme_order"] = array (	"structure" => "CREATE TABLE `paynearme_order` (
  																`paynearme_site_id` varchar(16) default NULL,
  																`paynearme_site_name` varchar(24) default NULL,
  																`paynearme_site_customer_id` int(11) NOT NULL default '0',
  																`paynearme_order_id` int(11) NOT NULL default '0',
  																`paynearme_order_created` varchar(32) default NULL,
  																`paynearme_order_status` varchar(12) default NULL,
  																`paynearme_order_amount` decimal(15,2) default '0.00',
  																`paynearme_order_currency` char(3) default NULL,
  																`paynearme_order_type` varchar(5) default NULL,
  																`paynearme_order_tracking_url` varchar(255) default NULL,
  																`paynearme_pnm_order_id` int(11) default '0',
  																`paynearme_pnm_balance_due_amount` decimal(15,2) default '0.00',
  																`paynearme_pnm_balance_due_currency` char(3) default NULL,
  																PRIMARY KEY  (`paynearme_order_id`),
  																KEY `index_order_id` (`paynearme_pnm_order_id`)
															) TYPE=MyISAM;" ,
													"data" => ""
											);

$add_new_tables["paynearme_customer"] = array (	"structure" => "CREATE TABLE `paynearme_customer` (
																  `paynearme_site_order_id` int(11) NOT NULL default '0',
																  `paynearme_site_customer_id` int(11) default '0',
																  `paynearme_pnm_customer_id` varchar(30) default NULL,
																  `paynearme_pnm_customer_email` varchar(128) default NULL,
																  `paynearme_pnm_customer_phone` varchar(16) default NULL,
																  `paynearme_pnm_customer_city` varchar(100) default NULL,
																  `paynearme_pnm_customer_state` varchar(50) default NULL,
																  `paynearme_pnm_customer_postal_code` varchar(12) default '0',
																  PRIMARY KEY  (`paynearme_site_order_id`),
																  KEY `index_customer_id` (`paynearme_site_customer_id`),
																  KEY `index_customer_email` (`paynearme_pnm_customer_email`)
																) TYPE=MyISAM;",
													"data" => ""
											);

$add_new_tables["paynearme_payment_info"] = array (	"structure" => "CREATE TABLE `paynearme_payment_info` (
	  																`paynearme_site_order_id` int(11) NOT NULL default '0',
	  																`paynearme_pending_amount` decimal(15,2) default '0.00',
	  																`paynearme_pending_currency` char(3) default NULL,
	  																`paynearme_pending_timestamp` datetime default NULL,
	  																`paynearme_authorization_due_to_site_amount` decimal(15,2) default '0.00',
	  																`paynearme_authorization_due_to_site_currency` char(3) default '',
	  																`paynearme_authorization_net_payment_amount` decimal(15,2) default '0.00',
	  																`paynearme_authorization_net_payment_currency` char(3) default '',
	  																`paynearme_authorization_payment_amount` decimal(15,2) default '0.00',
	  																`paynearme_authorization_payment_currency` char(3) default '',
	  																`paynearme_authorization_payment_date` datetime default NULL,
	  																`paynearme_authorization_pnm_payment_id` int(11) default '0',
	  																`paynearme_authorization_pnm_withheld_amount` decimal(15,2) default '0.00',
	  																`paynearme_authorization_pnm_withheld_currency` char(3) default '',
	  																`paynearme_authorization_timestamp` datetime default NULL,
	  																`paynearme_payment_due_to_site_amount` decimal(15,2) default '0.00',
	  																`paynearme_payment_due_to_site_currency` char(3) default '',
	  																`paynearme_payment_net_payment_amount` decimal(15,2) default '0.00',
	  																`paynearme_payment_net_payment_currency` char(3) default '',
	  																`paynearme_payment_payment_amount` decimal(15,2) default '0.00',
	  																`paynearme_payment_payment_currency` char(3) default '',
	  																`paynearme_payment_pnm_payment_id` int(11) default '0',
	  																`paynearme_payment_pnm_withheld_amount` decimal(15,2) default '0.00',
	  																`paynearme_payment_pnm_withheld_currency` char(3) default '',
	  																`paynearme_payment_status` varchar(24) default NULL,
	  																`paynearme_payment_timestamp` datetime default NULL,
	  																`paynearme_full_encoding` varchar(64) default NULL,
	  																`paynearme_slip_url` varchar(250) default NULL,
	  																`paynearme_slip_pdf_url` varchar(250) default NULL,
	  																PRIMARY KEY  (`paynearme_site_order_id`)
																) TYPE=MyISAM;",
													"data" => ""
												);

$add_new_tables["paynearme_status_history"] = array (	"structure" => "CREATE TABLE `paynearme_status_history` (
																		  `paynearme_status_history_id` int(11) NOT NULL auto_increment,
																		  `paynearme_order_id` int(11) default NULL,
																		  `paynearme_date` datetime default NULL,
																		  `paynearme_status` varchar(24) default NULL,
																		  `paynearme_description` varchar(250) default NULL,
																		  `paynearme_changed_by` varchar(128) default NULL,
																		  PRIMARY KEY  (`paynearme_status_history_id`),
																		  KEY `index_order_id` (`paynearme_order_id`)
																		) ENGINE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create paynearme tables

// Wee Siong
// Insert new records into configuration_group table (for Amazon Web Services)
$configuration_group_insert_sql = array();

$configuration_group_insert_sql["Amazon Web Services"] = array	(	"insert" => " ('Amazon Web Services', 'Amazon Web Services Configuration', 265, 1) ",
																	"update" => " sort_order=265, visible='1' "
						   										);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");
// End of insert new records into configuration_group table (for Amazon Web Services)

// Insert new records into configuration table (for Amazon Web Services)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Amazon Web Services'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["AWS_S3_ENABLED"] = array("insert" => " ('Enable S3 Support', 'AWS_S3_ENABLED', 'false', 'Enable Amazon Simple Storage Service (S3)?', ".$row_sql["configuration_group_id"].", 1, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Amazon Web Services)
?>