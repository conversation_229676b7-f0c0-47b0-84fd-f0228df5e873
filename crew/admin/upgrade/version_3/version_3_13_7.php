<?
/*
	$Id: version_3_13_7.php,v 1.1 2011/01/19 04:24:46 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new fields into tables publishers_games, top_up_queue 

$add_new_field = array();
$add_new_field['publishers_games'] = array (	array (	"field_name" => "publishers_games_status",
														"field_attr" => " tinyint(1) NOT NULL default '1' ",
														"add_after" => "categories_id"
														),
												array (	"field_name" => "publishers_games_daily_limit",
														"field_attr" => " DOUBLE( 10, 2 ) NOT NULL DEFAULT '0.00' COMMENT 'Daily Top-up Limit'",
														"add_after" => "publishers_games_status"
														),
												array (	"field_name" => "publishers_games_today_topped_amount",
														"field_attr" => " DOUBLE( 10, 2 ) NOT NULL DEFAULT '0.00' COMMENT 'Today Top-up Amount'",
														"add_after" => "publishers_games_daily_limit"
														)
										);
$add_new_field['top_up_queue'] = array (	array (	"field_name" => "top_up_queue_action",
													"field_attr" => " enum('0','1') NOT NULL default '0' ",
													"add_after" => "counter"
													)
										);
add_field($add_new_field);
// End of insert new fields into related tables

// Change field structure for price info field in publishers table
$change_field_structure = array();
$change_field_structure['publishers'] = array (array (	"field_name" => "publishers_remark",
														"field_attr" => " text NOT NULL "
									 					)
												);
change_field_structure ($change_field_structure);
// End of change field structure for price info field in orders_products table

// Insert cron track record for cron_direct_top_up.php
$cron_track_rec_select_sql = "	SELECT cron_process_track_filename 
								FROM cron_process_track 
								WHERE cron_process_track_filename = 'cron_direct_top_up.php'";
$cron_track_rec_result_sql = tep_db_query($cron_track_rec_select_sql);
if (!tep_db_num_rows($cron_track_rec_result_sql)) {
	$cron_track_rec_data_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_direct_top_up.php'
				                       );
	tep_db_perform('cron_process_track', $cron_track_rec_data_array);
}
// End of insert cron track record for cron_direct_top_up.php

// Insert cron track record for cron_pending_direct_top_up.php
$cron_track_rec_select_sql = "	SELECT cron_process_track_filename 
								FROM cron_process_track 
								WHERE cron_process_track_filename = 'cron_pending_direct_top_up.php'";
$cron_track_rec_result_sql = tep_db_query($cron_track_rec_select_sql);

if (!tep_db_num_rows($cron_track_rec_result_sql)) {
	$cron_track_rec_data_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_pending_direct_top_up.php'
				                       );
	tep_db_perform('cron_process_track', $cron_track_rec_data_array);
}
// End of insert cron track record for cron_pending_direct_top_up.php

$custom_products_type_child_sql = "	SELECT custom_products_type_child_id
									FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
									WHERE custom_products_type_child_url = 'game_card_direct_top_up'" ;
$custom_products_type_child_reult = tep_db_query($custom_products_type_child_sql);
if ($custom_products_type_child_row = tep_db_fetch_array($custom_products_type_child_reult)) {	// if found existing record
	tep_db_query("DELETE FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG . " WHERE custom_products_type_child_id = '".$custom_products_type_child_row['custom_products_type_child_id']."'");
	tep_db_query("DELETE FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . " WHERE custom_products_type_child_id = '".$custom_products_type_child_row['custom_products_type_child_id']."'");
}

// Insert new records into admin_files_actions table (for permission on check/uncheck Follow Actual Product checkbox)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='categories.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	$admin_files_actions_insert_sql["CATALOG_EDIT_PRODUCT_DELIVERY_INFO"] = array("insert" => " ('CATALOG_EDIT_PRODUCT_DELIVERY_INFO', 'Edit Product Delivery Information', ".$row_sql["admin_files_id"].", '1', '45')" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on check/uncheck Follow Actual Product checkbox)

$add_new_tables["products_delivery_info"] = array (	"structure" => "CREATE TABLE `products_delivery_info` (
																	  `products_id` int(11) NOT NULL,
																	  `products_delivery_mode_id` int(10) unsigned NOT NULL,
																	  PRIMARY KEY  (`products_id`,`products_delivery_mode_id`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
													);

$add_new_tables["products_delivery_mode"] = array (	"structure" => "CREATE TABLE `products_delivery_mode` (
																	  `products_delivery_mode_id` int(10) unsigned NOT NULL auto_increment,
																	  `custom_products_type` varchar(255) default NULL,
																	  `products_delivery_mode_title` varchar(64) NOT NULL,
																	  PRIMARY KEY  (`products_delivery_mode_id`)
																	) ENGINE=MyISAM;" ,
													"data" => "	INSERT INTO `products_delivery_mode` VALUES	(5, '2', 'Send To My Account'), 
																											(6, '2', 'Direct Top Up');"
													);

$add_new_tables["products_checkout_setting"] = array (	"structure" => "CREATE TABLE `products_checkout_setting` (
																		`products_id` int(11) NOT NULL,
																		`max_purchase_quantity` int(4) NOT NULL,
																		`max_purchase_period` int(11) NOT NULL COMMENT 'unit in minute',
																		PRIMARY KEY (`products_id`)
																		) ENGINE=MyISAM;" ,
													"data" => ""
													);
add_new_tables ($add_new_tables, $DBTables);
?>