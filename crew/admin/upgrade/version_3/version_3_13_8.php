<?
/*
	$Id: version_3_13_8.php,v 1.4 2011/01/31 08:33:25 chingyen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new fields into tables image_configuration 
$add_new_field = array();

$add_new_field['image_configuration'] = array (	array (	"field_name" => "aws_s3_info",
														"field_attr" => " varchar(80) default NULL",
														"add_after" => "web_path"
														)
										);

add_field($add_new_field);
// End of insert new fields into related tables

// Update aws_s3_info in image_category table
$ftp_s3_update_sql = array();

$ftp_s3_update_sql['image_configuration'] = array(	array(	"field_name" => "aws_s3_info",
																"update" => " aws_s3_info = '{\"bucket\":\"BUCKET_IMAGE\",\"path\":\"newsletter/\"}' ",
																"where_str" => " image_category='newsletter'"
																),
														array(	"field_name" => "aws_s3_info",
																"update" => " aws_s3_info = '{\"bucket\":\"BUCKET_IMAGE\",\"path\":\"buybackcn/\"}' ",
																"where_str" => " image_category='buybackcn'"
																),
														array(	"field_name" => "aws_s3_info",
																"update" => " aws_s3_info = '{\"bucket\":\"BUCKET_IMAGE\",\"path\":\"infolink/\"}' ",
																"where_str" => " image_category='infolink'"
																),
														array(	"field_name" => "aws_s3_info",
																"update" => " aws_s3_info = '{\"bucket\":\"BUCKET_IMAGE\",\"path\":\"events/\"}' ",
																"where_str" => " image_category='event'"
																),
														array(	"field_name" => "aws_s3_info",
																"update" => " aws_s3_info = '{\"bucket\":\"BUCKET_IMAGE\",\"path\":\"partner/\"}' ",
																"where_str" => " image_category='partner'"
																),
														array(	"field_name" => "aws_s3_info",
																"update" => " aws_s3_info = '{\"bucket\":\"BUCKET_IMAGE\",\"path\":\"games/\"}' ",
																"where_str" => " image_category='games'"
																),
														array(	"field_name" => "aws_s3_info",
																"update" => " aws_s3_info = '{\"bucket\":\"BUCKET_IMAGE\",\"path\":\"publisher/\"}' ",
																"where_str" => " image_category='publisher'"
																),
														array(	"field_name" => "aws_s3_info",
																"update" => " aws_s3_info = '{\"bucket\":\"BUCKET_IMAGE\",\"path\":\"banners/1/\"}' ",
																"where_str" => " image_category='banners/1'"
																),
														array(	"field_name" => "aws_s3_info",
																"update" => " aws_s3_info = '{\"bucket\":\"BUCKET_IMAGE\",\"path\":\"banners/2/\"}' ",
																"where_str" => " image_category='banners/2'"
																)
													 );

advance_update_records($ftp_s3_update_sql, $DBTables);
// End of update aws_s3_info in image_category table

// Add new Download Center option into admin_files
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE admin_files_name = 'download_center.php'" ;
$result_sql = tep_db_query($select_sql);

if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["DOWNLOAD_CENTER_SC_CHECKOUT"] = array("insert" => " ('DOWNLOAD_CENTER_SC_CHECKOUT', 'CO Paid with Full/Partial SC', ".$row_sql["admin_files_id"].", '1', 200)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of add new Download Center option into admin_files

// Insert new records into admin_files table (for Image Upload Permission)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='administrator.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["image_upload_conf.php"] = array(	"insert" => " ('image_upload_conf.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
								   							);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='image_upload_conf.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Image Upload Permission)

// create new table [start]
$add_new_tables = array();

$add_new_tables["orders_cancel_request"] = array (	"structure" => "CREATE TABLE `orders_cancel_request` (
																		`orders_id` int(11) NOT NULL,
																		PRIMARY KEY (`orders_id`)
																	) ENGINE=MyISAM;" ,
													"data" => "" );

add_new_tables ($add_new_tables, $DBTables);
// create new table [end]
?>