<?
/*
	$Id: version_3_14.php,v 1.2 2011/02/14 05:50:37 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new fields into tables orders_top_up 
$add_new_field = array();
$add_new_field['orders_top_up'] = array (	array (	"field_name" => "top_up_created_date",
													"field_attr" => " datetime NOT NULL default '0000-00-00 00:00:00' ",
													"add_after" => "top_up_timestamp"
													)
										);
add_field($add_new_field);
// End of insert new fields into related tables

// Change field structure for top_last_processed_time field in orders_top_up table
$change_field_structure = array();

$change_field_structure['orders_top_up'] = array (array (	"field_name" => "top_last_processed_time",
															"field_new_name" => "top_up_last_processed_time",
															"field_attr" => " DATETIME NOT NULL DEFAULT '0000-00-00 00:00:00' "
									 					)
												);

change_field_structure ($change_field_structure);
// End of change field structure for top_last_processed_time field in orders_top_up table

$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_download_daily_top_up_report.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_progress_task_data_sql = array(	'cron_process_track_in_action' => 0,
											'cron_process_track_start_date' => '2011-02-14 00:00:00',
											'cron_process_track_failed_attempt' => 0,
											'cron_process_track_filename' => 'cron_download_daily_top_up_report.php'
										);
	tep_db_perform('cron_process_track', $cron_progress_task_data_sql);
}

$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_update_topup_balance.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_progress_task_data_sql = array(	'cron_process_track_in_action' => 0,
											'cron_process_track_start_date' => '2011-02-14 00:00:00',
											'cron_process_track_failed_attempt' => 0,
											'cron_process_track_filename' => 'cron_update_topup_balance.php'
										);
	tep_db_perform('cron_process_track', $cron_progress_task_data_sql);
}

?>