<?
// Create new table [start]
$add_new_tables = array();

$add_new_tables["game_esrb"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_esrb` (
														  `game_esrb_id` int(11) NOT NULL AUTO_INCREMENT,
														  `sort_order` int(5) NOT NULL DEFAULT '50000',
														  PRIMARY KEY (`game_esrb_id`)
														) ENGINE=MyISAM;" ,
										"data" => "INSERT INTO `game_esrb` (`game_esrb_id`, `sort_order`) VALUES
													(1, 0),
													(2, 0),
													(3, 0),
													(4, 0),
													(5, 0),
													(6, 0),
													(7, 0);");

$add_new_tables["game_esrb_description"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_esrb_description` (
																	  `game_esrb_id` int(11) NOT NULL,
																	  `language_id` int(11) NOT NULL,
																	  `game_esrb_description` varchar(5) NOT NULL,
																	  `note` varchar(255) NOT NULL,
																	  PRIMARY KEY (`game_esrb_id`,`language_id`)
																	) ENGINE=MyISAM;" ,
													"data" => "INSERT INTO `game_esrb_description` (`game_esrb_id`, `language_id`, `game_esrb_description`, `note`) VALUES
																(1, 1, 'AO', 'Titles rated AO (Adults Only) have content that should only be played by persons 18 years and older.'),
																(1, 2, 'AO', '必须成人:其中包含内容只适合18岁以上的成人。'),
																(1, 3, 'AO', '必須成人:其中包含內容只適合18歲以上的成人。'),
																(2, 1, 'E', 'Titles rated E (Everyone) have content that may be suitable for ages 6 and older.'),
																(2, 2, 'E', '所有人:其中包含的内容适合6岁或者以上的玩家。'),
																(2, 3, 'E', '所有人:其中包含的內容適合6歲或者以上的玩家。'),
																(3, 1, 'E10', 'Titles rated E10+ (Everyone 10 and older) have content that may be suitable for ages 10 and older.'),
																(3, 2, 'E10', '10岁以上:其中内容适合10岁以上的玩家。'),
																(3, 3, 'E10', '10歲以上:其中內容適合10歲以上的玩家。'),
																(4, 1, 'EC', 'Titles rated EC - Early Childhood have content that may be suitable for ages 3 and older.'),
																(4, 2, 'EC', '幼儿:其中包含的内容适合3岁或者以上的儿童。'),
																(4, 3, 'EC', '幼兒:其中包含的內容適合3歲或者以上的兒童。'),
																(5, 1, 'M', 'Titles rated M (Mature) have content that may be suitable for persons ages 17 and older.'),
																(5, 2, 'M', '成熟:其中包含适合17岁以上玩家的内容。'),
																(5, 3, 'M', '成熟:其中包含適合17歲以上玩家的內容。'),
																(6, 1, 'RP', 'Titles listed as RP (Rating Pending) - have been submitted to the ESRB and are awaiting final rating.'),
																(6, 2, 'RP', '有待评级:产品已经提交ESA审定，正在等待分级结果。'),
																(6, 3, 'RP', '有待評級:產品已經提交ESA審定，正在等待分級結果。'),
																(7, 1, 'T', 'Titles rated T (Teen) have content that may be suitable for ages 13 and older.'),
																(7, 2, 'T', '青少年:其中包含适合13岁（英文Teen指13-19岁青年）以上的玩家的内容。'),
																(7, 3, 'T', '青少年:其中包含適合13歲（英文Teen指13-19歲青年）以上的玩家的內容。');" );

$add_new_tables["game_esrb_to_categories"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_esrb_to_categories` (
																		  `game_esrb_id` int(11) NOT NULL,
																		  `categories_id` int(11) NOT NULL,
																		  PRIMARY KEY (`game_esrb_id`,`categories_id`)
																		) ENGINE=MyISAM;" ,
														"data" => "" );

$add_new_tables["game_genre"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_genre` (
														  `game_genre_id` int(11) NOT NULL AUTO_INCREMENT,
														  `sort_order` int(5) NOT NULL DEFAULT '50000',
														  PRIMARY KEY (`game_genre_id`)
														) ENGINE=MyISAM;" ,
										"data" => "INSERT INTO `game_genre` (`game_genre_id`, `sort_order`) VALUES
													(1, 0),
													(2, 10),
													(3, 20),
													(4, 30),
													(5, 40),
													(6, 50),
													(7, 60),
													(8, 70),
													(9, 80),
													(10, 50000);" );

$add_new_tables["game_genre_description"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_genre_description` (
																	  `game_genre_id` int(11) NOT NULL,
																	  `language_id` int(11) NOT NULL,
																	  `game_genre_description` varchar(64) NOT NULL,
																	  PRIMARY KEY (`game_genre_id`,`language_id`)
																	) ENGINE=MyISAM;" ,
													"data" => "INSERT INTO `game_genre_description` (`game_genre_id`, `language_id`, `game_genre_description`) VALUES
																(1, 1, 'Action'),
																(1, 2, '动作类'),
																(1, 3, '動作類'),
																(2, 1, 'Casual'),
																(2, 2, '休闲/模拟类'),
																(2, 3, '休閒/模擬類'),
																(3, 1, 'Dance'),
																(3, 2, '舞蹈/音乐类'),
																(3, 3, '舞蹈/音樂類'),
																(4, 1, 'FPS'),
																(4, 2, '第一人称射击类'),
																(4, 3, '第一人稱射擊類'),
																(5, 1, 'MMO'),
																(5, 2, '多人在线类'),
																(5, 3, '多人在綫類'),
																(6, 1, 'RPG'),
																(6, 2, '角色扮演类'),
																(6, 3, '角色扮演類'),
																(7, 1, 'RTS'),
																(7, 2, '战略类'),
																(7, 3, '戰略類'),
																(8, 1, 'Sports'),
																(8, 2, '体育/竞速类'),
																(8, 3, '體育/競速類'),
																(9, 1, 'Strategy'),
																(9, 2, '策略类'),
																(9, 3, '策略類'),
																(10, 1, 'Others'),
																(10, 2, '其他'),
																(10, 3, '其他');" );

$add_new_tables["game_genre_to_categories"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_genre_to_categories` (
																		  `game_genre_id` int(11) NOT NULL,
																		  `categories_id` int(11) NOT NULL,
																		  PRIMARY KEY (`game_genre_id`,`categories_id`)
																		) ENGINE=MyISAM;" ,
														"data" => "" );

$add_new_tables["game_language"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_language` (
															  `game_language_id` int(11) NOT NULL AUTO_INCREMENT,
															  `sort_order` int(5) NOT NULL DEFAULT '50000',
															  PRIMARY KEY (`game_language_id`)
															) ENGINE=MyISAM;" ,
											"data" => "INSERT INTO `game_language` (`game_language_id`, `sort_order`) VALUES
														(1, 0),
														(2, 0),
														(3, 0),
														(4, 0),
														(5, 0),
														(6, 0);" );

$add_new_tables["game_language_description"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_language_description` (
																		  `game_language_id` int(11) NOT NULL,
																		  `language_id` int(11) NOT NULL,
																		  `game_language_description` varchar(64) NOT NULL,
																		  PRIMARY KEY (`game_language_id`,`language_id`)
																		) ENGINE=MyISAM;" ,
														"data" => "INSERT INTO `game_language_description` (`game_language_id`, `language_id`, `game_language_description`) VALUES
																	(1, 1, 'English Games'),
																	(1, 2, '英文游戏'),
																	(1, 3, '英文遊戲'),
																	(2, 1, 'Chinese Games'),
																	(2, 2, '中文游戏'),
																	(2, 3, '中文遊戲'),
																	(3, 1, 'Deutsch Games'),
																	(3, 2, '德文游戏'),
																	(3, 3, '德文遊戲'),
																	(4, 1, 'Malay Games'),
																	(4, 2, '马来文游戏'),
																	(4, 3, '馬來文遊戲'),
																	(5, 1, 'Thai Games'),
																	(5, 2, '泰文游戏'),
																	(5, 3, '泰文遊戲'),
																	(6, 1, 'Tagalog Games'),
																	(6, 2, '菲律宾语游戏'),
																	(6, 3, '菲律賓語遊戲');" );

$add_new_tables["game_platform"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_platform` (
															  `game_platform_id` int(11) NOT NULL AUTO_INCREMENT,
															  `sort_order` int(5) NOT NULL DEFAULT '50000',
															  PRIMARY KEY (`game_platform_id`)
															) ENGINE=MyISAM;" ,
											"data" => "INSERT INTO `game_platform` (`game_platform_id`, `sort_order`) VALUES
														(1, 0),
														(2, 0),
														(3, 0),
														(4, 0),
														(5, 0),
														(6, 0),
														(7, 0);" );

$add_new_tables["game_platform_description"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_platform_description` (
																		  `game_platform_id` int(11) NOT NULL,
																		  `language_id` int(11) NOT NULL,
																		  `game_platform_description` varchar(64) NOT NULL,
																		  PRIMARY KEY (`game_platform_id`,`language_id`)
																		) ENGINE=MyISAM;" ,
														"data" => "INSERT INTO `game_platform_description` (`game_platform_id`, `language_id`, `game_platform_description`) VALUES
																	(1, 1, 'PC'),
																	(1, 2, 'PC平台'),
																	(1, 3, 'PC平臺'),
																	(2, 1, 'Facebook'),
																	(2, 2, 'Facebook平台'),
																	(2, 3, 'Facebook平臺'),
																	(3, 1, 'Mobile'),
																	(3, 2, '手机平台'),
																	(3, 3, '手機平臺'),
																	(4, 1, 'Playstation'),
																	(4, 2, 'PS平台'),
																	(4, 3, 'PS平臺'),
																	(5, 1, 'Xbox'),
																	(5, 2, 'Xbox平台'),
																	(5, 3, 'Xbox平臺'),
																	(6, 1, 'Nintendo'),
																	(6, 2, '任天堂平台'),
																	(6, 3, '任天堂平臺'),
																	(7, 1, 'Others'),
																	(7, 2, '其他'),
																	(7, 3, '其他');" );

$add_new_tables["game_platform_to_categories"] = array ("structure" => "CREATE TABLE IF NOT EXISTS `game_platform_to_categories` (
																		  `game_platform_id` int(11) NOT NULL,
																		  `categories_id` int(11) NOT NULL,
																		  PRIMARY KEY (`game_platform_id`,`categories_id`)
																		) ENGINE=MyISAM;" ,
														"data" => "" );

$add_new_tables["game_region"] = array ("structure" => "CREATE TABLE IF NOT EXISTS `game_region` (
														  `game_region_id` int(11) NOT NULL AUTO_INCREMENT,
														  `sort_order` int(5) NOT NULL DEFAULT '50000',
														  PRIMARY KEY (`game_region_id`)
														) ENGINE=MyISAM;" ,
										"data" => "INSERT INTO `game_region` (`game_region_id`, `sort_order`) VALUES
													(1, 0),
													(2, 10),
													(3, 20),
													(4, 30),
													(5, 40),
													(6, 50),
													(7, 60),
													(8, 70),
													(9, 80),
													(10, 90),
													(11, 100),
													(12, 110),
													(13, 120),
													(14, 130),
													(15, 140),
													(16, 150);" );

$add_new_tables["game_region_description"] = array ("structure" => "CREATE TABLE IF NOT EXISTS `game_region_description` (
																	  `game_region_id` int(11) NOT NULL,
																	  `language_id` int(11) NOT NULL,
																	  `game_region_description` varchar(64) NOT NULL,
																	  PRIMARY KEY (`game_region_id`,`language_id`)
																	) ENGINE=MyISAM;" ,
													"data" => "INSERT INTO `game_region_description` (`game_region_id`, `language_id`, `game_region_description`) VALUES
																(1, 1, 'Global'),
																(1, 2, '国际'),
																(1, 3, '國際'),
																(2, 1, 'Australia (AU)'),
																(2, 2, '澳服'),
																(2, 3, '澳服'),
																(3, 1, 'China (CN)'),
																(3, 2, '中服'),
																(3, 3, '中服'),
																(4, 1, 'Europe (EU)'),
																(4, 2, '欧服'),
																(4, 3, '歐服'),
																(5, 1, 'Germany (DE)'),
																(5, 2, '德服'),
																(5, 3, '德服'),
																(6, 1, 'HongKong (HK)'),
																(6, 2, '港服'),
																(6, 3, '港服'),
																(7, 1, 'Malaysia (MY)'),
																(7, 2, '马服'),
																(7, 3, '馬服'),
																(8, 1, 'Phillipines (PH)'),
																(8, 2, '菲服'),
																(8, 3, '菲服'),
																(9, 1, 'Russia (RU)'),
																(9, 2, '俄服'),
																(9, 3, '俄服'),
																(10, 1, 'South East Asia (SEA)'),
																(10, 2, '亚服'),
																(10, 3, '亞服'),
																(11, 1, 'Singapore (SG)'),
																(11, 2, '新服'),
																(11, 3, '新服'),
																(12, 1, 'Taiwan (TW)'),
																(12, 2, '台服'),
																(12, 3, '臺服'),
																(13, 1, 'Thailand (TH)'),
																(13, 2, '泰服'),
																(13, 3, '泰服'),
																(14, 1, 'Turkey (TR)'),
																(14, 2, '土服'),
																(14, 3, '土服'),
																(15, 1, 'United Kingdom (UK)'),
																(15, 2, '英服'),
																(15, 3, '英服'),
																(16, 1, 'United State (US)'),
																(16, 2, '美服'),
																(16, 3, '美服');" );

$add_new_tables["game_region_language_to_categories"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_region_language_to_categories` (
																				  `game_region_to_language_id` int(11) NOT NULL,
																				  `categories_id` int(11) NOT NULL,
																				  PRIMARY KEY (`game_region_to_language_id`,`categories_id`)
																				) ENGINE=MyISAM;" ,
																"data" => "" );

$add_new_tables["game_region_to_language"] = array ("structure" => "CREATE TABLE IF NOT EXISTS `game_region_to_language` (
																	  `game_region_to_language_id` int(11) NOT NULL AUTO_INCREMENT,
																	  `game_region_id` int(11) NOT NULL,
																	  `game_language_id` int(11) NOT NULL,
																	  `default_setting` enum('0','1') NOT NULL,
																	  PRIMARY KEY (`game_region_to_language_id`)
																	) ENGINE=MyISAM;" ,
													"data" => "INSERT INTO `game_region_to_language` (`game_region_to_language_id`, `game_region_id`, `game_language_id`, `default_setting`) VALUES
																(1, 7, 1, '1'),
																(2, 7, 2, '0'),
																(3, 7, 4, '0'),
																(4, 16, 1, '1'),
																(5, 16, 2, '0'),
																(6, 3, 2, '0'),
																(7, 3, 1, '1'),
																(8, 5, 1, '1'),
																(9, 5, 2, '0'),
																(10, 4, 1, '1'),
																(11, 4, 3, '0'),
																(12, 4, 2, '0'),
																(13, 1, 1, '1'),
																(14, 1, 2, '0'),
																(15, 6, 1, '1'),
																(16, 6, 2, '0'),
																(17, 12, 2, '0'),
																(18, 12, 1, '1'),
																(19, 11, 2, '0'),
																(20, 11, 1, '1'),
																(21, 8, 6, '0'),
																(22, 8, 1, '1'),
																(23, 9, 1, '1'),
																(24, 10, 1, '1'),
																(25, 10, 2, '0'),
																(26, 13, 1, '1'),
																(27, 13, 5, '0'),
																(28, 14, 1, '1'),
																(29, 14, 2, '0'),
																(30, 15, 1, '1'),
																(31, 2, 1, '1');" );

$add_new_tables["game_to_publisher"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_to_publisher` (
																  `game_id` int(11) NOT NULL,
																  `publisher_id` int(11) NOT NULL,
																  PRIMARY KEY (`game_id`,`publisher_id`)
																) ENGINE=MyISAM;" ,
												"data" => "" );

add_new_tables ($add_new_tables, $DBTables);
// Create new table [end]


// Insert new fields into categories_game_details
$add_new_field = array();

$add_new_field['categories_game_details'] = array (array (	"field_name" => "game_template",
															"field_attr" => "enum('0','1') NOT NULL COMMENT '0=Game, 1=Publisher'",
															"add_after" => "" )
											);
add_field($add_new_field);
// End of insert new fields into categories_game_details


// Insert new records into admin_files table (for Game DB Config)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='infolinks.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["game_database_config.php"] = array("insert" => " ('game_database_config.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
								   							);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='game_database_config.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Game DB Config)

// Dennis
// Insert new fields into paynearme_payment_info table
$add_new_field = array();

$add_new_field['paynearme_payment_info'] = array (	array (	"field_name" => "paynearme_card_pdf_url",
															"field_attr" => " varchar(250) NULL default NULL ",
															"add_after" => "paynearme_slip_pdf_url"
															)
												  );
												  
add_field($add_new_field);
// End of insert new fields into paynearme_payment_info table

// Delete paynearme_slip_url field from paynearme_payment_info table
$delete_field = array();
$delete_field['paynearme_payment_info'] = array  ( array( "field_name" => "paynearme_slip_url") );

delete_field ($delete_field);
// End of delete paynearme_slip_url field from paynearme_payment_info table
?>