<?
/*
	$Id: version_3_14_3.php,v 1.1 2011/02/28 10:25:39 sionghuat.chng Exp $
	
  	Developer: <PERSON> (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

if (!in_array('latest_news_groups_description', $DBTables)) {
	$transfer_latest_news_grp_setting = true;
} else {
	$transfer_latest_news_grp_setting = false;
}

// Create latest news tag tables
$add_new_tables = array();

$add_new_tables["latest_news_tag_categories"] = array (	"structure" => "CREATE TABLE `latest_news_tag_categories` (
																		  `tag_id` int(11) NOT NULL auto_increment,
																		  `tag_name` varchar(50) NOT NULL,
																		  PRIMARY KEY  (`tag_id`)
																		) TYPE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables["latest_news_tag_content"] = array (	"structure" => "CREATE TABLE `latest_news_tag_content` (
																		  `tag_content_id` int(11) NOT NULL auto_increment,
																		  `content_id` int(11) NOT NULL,
																		  `tag_id` int(11) NOT NULL,
																		  `content_type` varchar(2),
																		  PRIMARY KEY  (`tag_content_id`)
																		) TYPE=MyISAM;" ,
														"data" => ""
													);

$add_new_tables["latest_news_groups_description"] = array (	"structure" => "CREATE TABLE `latest_news_groups_description` (
																			  `news_groups_id` int(11) NOT NULL auto_increment,
																			  `language_id` int(11) NOT NULL default '1',
																			  `news_groups_name` varchar(32) NOT NULL default '',
																			  PRIMARY KEY  (`news_groups_id`, `language_id`)
																			) TYPE=MyISAM;" ,
															"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// End of create latest news tag tables

// Insert new fields into latest_news_groups table
$add_new_field = array();

$add_new_field['latest_news_groups'] = array (	array (	"field_name" => "news_groups_display_status",
														"field_attr" => " tinyint(1) NOT NULL default '0' ",
														"add_after" => "news_groups_name"
														),
												array (	"field_name" => "news_groups_sort_order",
														"field_attr" => " int(3) NOT NULL default '50000' ",
														"add_after" => "news_groups_display_status"
														)
											);

add_field($add_new_field);
// End of insert new fields into latest_news_groups table

// Insert new records into admin_files table (for Latest News Group)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='latest_news.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["news_groups.php"] = array(	"insert" => " ('news_groups.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
						   							);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='news_groups.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Latest News Group)

if ($transfer_latest_news_grp_setting) {
	$latest_news_select_sql = " SELECT news_groups_id, news_groups_name 
								FROM latest_news_groups"; 
	$latest_news_result_sql = tep_db_query($latest_news_select_sql);
	
	while($latest_news_row = tep_db_fetch_array($latest_news_result_sql)){
		$latest_news_description_data_array = array('news_groups_id' => $latest_news_row['news_groups_id'],
  													'language_id' => '1',
  													'news_groups_name' => $latest_news_row['news_groups_name']
                          							);
		tep_db_perform('latest_news_groups_description', $latest_news_description_data_array);
	}
}

// Define infolinks_groups_parent_id as index key in infolinks_groups table
add_index_key ('infolinks_groups', 'index_infolinks_groups_parent_id', 'index', 'infolinks_groups_parent_id', $DBTables);
// End of define infolinks_groups_parent_id as index key in infolinks_groups table
?>