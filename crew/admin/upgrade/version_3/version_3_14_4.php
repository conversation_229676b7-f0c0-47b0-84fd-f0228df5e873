<?
//sionghuat
// Insert new records into admin_files table (for customer group permission control)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers_groups.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_GROUP_ADD_GROUP"] = array("insert" => " ('CUSTOMER_GROUP_ADD_GROUP', 'Add Customer Group', ".$row_sql["admin_files_id"].", '1', 10)" );
	$admin_files_actions_insert_sql["CUSTOMER_GROUP_EDIT_GROUP"] = array("insert" => " ('CUSTOMER_GROUP_EDIT_GROUP', 'Edit Customer Group', ".$row_sql["admin_files_id"].", '1', 20)" );
	$admin_files_actions_insert_sql["CUSTOMER_GROUP_ADD_DISCOUNT_SETTING"] = array("insert" => " ('CUSTOMER_GROUP_ADD_DISCOUNT_SETTING', 'Add Discount Setting for Customer Group', ".$row_sql["admin_files_id"].", '1', 30)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files table (for customer group permission control)

// Insert new fields into site_customers_access table (for customer group permission control)
$add_new_field = array();

$add_new_field['site_customers_access'] = array (	array (	"field_name" => "discount_setting_admin_groups_id",
															"field_attr" => " text NOT NULL",
															"add_after" => "admin_groups_id"
															)
												);

add_field($add_new_field);
// End of insert new fields into site_customers_access table (for customer group permission control)

// Insert new records into image_configuration table 
$image_configuration_category_select_sql = "	SELECT image_category 	
												FROM " . TABLE_IMAGE_CONFIGURATION . "
												WHERE image_category = 'gameguides'";
$image_configuration_category_result_sql = tep_db_query($image_configuration_category_select_sql);
if (!tep_db_num_rows($image_configuration_category_result_sql)) {
	$image_configuration_category_data_array = array(	'image_category' => 'gameguides',
														'file_path' => '/var/www/html/image.offgamers.com/gameguides',
														'web_path' => 'http://image.offgamers.com/gameguides',
														'aws_s3_info' => '{"bucket":"BUCKET_IMAGE","path":"gameguides/"}',
														'user_groups' => '1'
							                       	);
	tep_db_perform(TABLE_IMAGE_CONFIGURATION, $image_configuration_category_data_array);
}

$image_configuration_category_select_sql = "	SELECT image_category 	
												FROM " . TABLE_IMAGE_CONFIGURATION . "
												WHERE image_category = 'blog'";
$image_configuration_category_result_sql = tep_db_query($image_configuration_category_select_sql);
if (!tep_db_num_rows($image_configuration_category_result_sql)) {
	$image_configuration_category_data_array = array(	'image_category' => 'blog',
														'file_path' => '/var/www/html/image.offgamers.com/blog',
														'web_path' => 'http://image.offgamers.com/blog',
														'aws_s3_info' => '{"bucket":"BUCKET_IMAGE","path":"blog/"}',
														'user_groups' => '1'
							                       	);
	tep_db_perform(TABLE_IMAGE_CONFIGURATION, $image_configuration_category_data_array);
}
// Insert new records into image_configuration table

// Wee Siong
// Insert new records into custom_products_type_child table (for Game Portal Store)
$custom_products_type_child_select_sql = "	SELECT custom_products_type_child_id 	
											FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
											WHERE custom_products_type_child_id = '9'";
$custom_products_type_child_result_sql = tep_db_query($custom_products_type_child_select_sql);
if (!tep_db_num_rows($custom_products_type_child_result_sql)) {
	$custom_products_type_child_data_array = array(	'custom_products_type_id' => '2',
													'custom_products_type_child_url' => 'game_portal_store',
													'custom_products_type_child_name' => 'Game Portal Store',
													'display_status' => '0',
													'sort_order' => 5500
							                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, $custom_products_type_child_data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '9',
							'languages_id' => 1,
							'custom_products_type_child_name' => 'Game Portal Store'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '9',
							'languages_id' => 2,
							'custom_products_type_child_name' => '&#19968;&#21345;&#36890;&#21830;&#38138'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '9',
							'languages_id' => 3,
							'custom_products_type_child_name' => '&#19968;&#21345;&#36890;&#21830;&#33302'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
}
// End of insert new records into custom_products_type_child table (for Game Portal Store)
?>