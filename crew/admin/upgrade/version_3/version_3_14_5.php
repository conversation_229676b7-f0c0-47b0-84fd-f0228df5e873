<?
/*
	$Id: version_3_14_5.php,v 1.3 2011/03/22 10:22:49 sionghuat.chng Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Kee <PERSON>g
// Insert new records into categories_configuration table (for Product Status Editing notification)
$conf_insert_sql = array();
$conf_insert_sql["CATALOG_CDKEY_STATUS_EMAIL"] = array("insert" => " (0, 'CD Key Product Status Editing Email Address', 'CATALOG_CDKEY_STATUS_EMAIL', '', 'Email address to which the email will be send to whenever someone change CD Key Product Status.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 10, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into categories_configuration table (for Product Status Editing notification)

// Insert new fields into custom_products_type table
$add_new_field = array();

$add_new_field['custom_products_type'] = array (array (	"field_name" => "custom_products_change_status_email",
														"field_attr" => " varchar(32) NOT NULL default '' ",
														"add_after" => "custom_products_upload_email"
														)
											);
												
add_field($add_new_field);
// End of insert new fields into custom_products_type table

// Update records in custom_products_type table
$cp_update_sql = array();

$cp_update_sql["2"] = array("update" => " custom_products_change_status_email='CATALOG_CDKEY_STATUS_EMAIL' " );

update_records("custom_products_type", "custom_products_type_id", $cp_update_sql, $DBTables);
// End of update records in custom_products_type table

// create new table for customers groups extra op
$add_new_tables = array();

$add_new_tables["customers_groups_extra_op"] = array (	"structure" => "	CREATE TABLE `customers_groups_extra_op` (
																			  `customers_groups_extra_op_id` int(11) UNSIGNED NOT NULL auto_increment,
																			  `customers_groups_discount_id` int(11) UNSIGNED NOT NULL default '0',
																			  `payment_methods_id` int(11) UNSIGNED NOT NULL default '0',
																			  `currency` char(3) NOT NULL,
																			  `bonus_op` decimal(8,2) NOT NULL,
																			  PRIMARY KEY  (`customers_groups_extra_op_id`)
																			) ENGINE=MyISAM;",
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// END of create new table for customers groups extra op

// Siong Huat
// Insert new fields into site_customers_access table (for customer group discount setting email notification)
$add_new_field = array();

$add_new_field['site_customers_access'] = array (	array (	"field_name" => "discount_setting_notification",
															"field_attr" => " varchar(255) NOT NULL",
															"add_after" => " discount_setting_admin_groups_id"
															)
												);

add_field($add_new_field);
// End of insert new fields into site_customers_access table (for customer group discount setting email notification)
?> 