<?
/*
	$Id: version_3_14_6.php,v 1.2 2011/03/25 12:30:14 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define payment_fees_customers_groups_id as index key in payment_fees table
add_index_key ('payment_fees', 'index_customers_groups_id', 'index', 'payment_fees_customers_groups_id', $DBTables);
// End of define payment_fees_customers_groups_id as index key in payment_fees table

// Define customers_groups_discount_id and payment_methods_id as index key in customers_groups_extra_op table
add_index_key ('customers_groups_extra_op', 'index_discount_id_and_payment', 'index', 'customers_groups_discount_id,payment_methods_id', $DBTables);
// End of define customers_groups_discount_id and payment_methods_id as index key in customers_groups_extra_op table

// Define tag_id as index key in latest_news_tag_content table
add_index_key ('latest_news_tag_content', 'index_tag_id', 'index', 'tag_id', $DBTables);
// End of define tag_id as index key in latest_news_tag_content table

// Create promotions orders log tables
$add_new_tables = array();

$add_new_tables["promotions_orders_log"] = array (	"structure" => "CREATE TABLE `promotions_orders_log` (
																	`orders_id` INT(11) UNSIGNED NOT NULL ,
																	`proccess_flag` TINYINT(1) UNSIGNED NOT NULL ,
																	`created_datetime` DATETIME default NULL ,
																	PRIMARY KEY (`orders_id`)
																	) ENGINE = MYISAM;" ,
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create promotions orders log tables
?> 