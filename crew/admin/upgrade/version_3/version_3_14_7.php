<?
/*
	$Id: version_3_14_7.php,v 1.2 2011/03/28 08:37:16 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define content_id as index key in latest_news_tag_content table
add_index_key ('latest_news_tag_content', 'index_content_id', 'index', 'content_id', $DBTables);
// End of define content_id as index key in latest_news_tag_content table

// Insert new fields into paynearme_payment_info table
$add_new_field = array();

$add_new_field['paynearme_payment_info'] = array (	array (	"field_name" => "paynearme_authorization_processing_fee_amount",
															"field_attr" => " decimal(10,2) NULL default '0.00' ",
															"add_after" => "paynearme_authorization_pnm_withheld_currency"
															),
													array (	"field_name" => "paynearme_authorization_processing_fee_currency",
															"field_attr" => " char(3) DEFAULT NULL ",
															"add_after" => "paynearme_authorization_processing_fee_amount"
															),
													array (	"field_name" => "paynearme_payment_processing_fee_amount",
															"field_attr" => " decimal(10,2) NULL default '0.00' ",
															"add_after" => "paynearme_payment_pnm_withheld_currency"
															),
													array (	"field_name" => "paynearme_payment_processing_fee_currency",
															"field_attr" => " char(3) DEFAULT NULL ",
															"add_after" => "paynearme_payment_processing_fee_amount"
															)
												  );
												  
add_field($add_new_field);
// End of insert new fields into paynearme_payment_info table
?>