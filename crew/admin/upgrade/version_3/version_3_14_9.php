<?
/*
	$Id: version_3_14_9.php,v 1.1 2011/04/06 03:29:59 weesiong Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Wee <PERSON>

// Insert new records into configuration table (for Amazon Web Services)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Amazon Web Services'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["AWS_SES_ENABLED"] = array("insert" => " ('Enable SES Support', 'AWS_SES_ENABLED', 'false', 'Enable Amazon Simple Email Service (SES)?', ".$row_sql["configuration_group_id"].", 1, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Amazon Web Services)
?>