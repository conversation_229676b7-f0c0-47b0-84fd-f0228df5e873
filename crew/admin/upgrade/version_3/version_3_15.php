<?
// GST : Create new table for GST
$add_new_tables = array();
$add_new_tables["store_refund_info"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `store_refund_info` (
																  `store_refund_id` int(11) unsigned NOT NULL,
																  `store_refund_gst_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
																  PRIMARY KEY (`store_refund_id`)
																) ENGINE=MyISAM ;" ,
												"data" => "" );

$add_new_tables["orders_tax_configuration"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `orders_tax_configuration` (
																		  `orders_tax_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
																		  `country_code` char(2) NOT NULL COMMENT 'countries_iso_code_2',
																		  `currency` char(3) NOT NULL,
																		  `orders_tax_percentage` decimal(6,2) NOT NULL DEFAULT '0.00',
																		  `orders_tax_status` enum('0','1') NOT NULL DEFAULT '0',
																		  PRIMARY KEY (`orders_tax_id`)
																		) ENGINE=MyISAM ;" ,
														"data" => "" );

$add_new_tables["orders_tax_configuration_description"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `orders_tax_configuration_description` (
																					  `orders_tax_id` int(11) unsigned NOT NULL DEFAULT '0',
																					  `language_id` int(11) unsigned NOT NULL DEFAULT '0',
																					  `orders_tax_title` varchar(20) NOT NULL,
																					  PRIMARY KEY (`orders_tax_id`,`language_id`)
																					) ENGINE=MyISAM ;" ,
																	"data" => "" );

$add_new_tables["orders_extra_info"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `orders_extra_info` (
																  `orders_id` int(11) unsigned NOT NULL,
																  `orders_extra_info_key` varchar(32) NOT NULL,
																  `orders_extra_info_value` varchar(32) NOT NULL,
																  PRIMARY KEY (`orders_id`,`orders_extra_info_key`)
																) ENGINE=MyISAM ;" ,
												"data" => "");

add_new_tables ($add_new_tables, $DBTables);


// add new store file into admin_files [start]
$select_sql = "	SELECT admin_files_id FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_name = 'localization.php'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_files_array = array ('admin_files_name' => 'gst_configuration.php', 
								'admin_files_to_boxes' => $row_sql['admin_files_id'], 
								'admin_groups_id' => '1', 
								'admin_files_cat_setting' => '1' 
								);
	tep_db_perform(TABLE_ADMIN_FILES, $admin_files_array);
}
// add new store file into admin_files [end]
?>