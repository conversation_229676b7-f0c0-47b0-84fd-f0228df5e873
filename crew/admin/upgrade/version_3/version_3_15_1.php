<?
/*
	$Id: version_3_15_1.php,v 1.1 2011/04/07 09:15:34 keepeng.foong Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/


// Insert new records into products_delivery_mode table (for Currency Product Delivery Mode)
$products_delivery_mode_insert_sql = array();
	
$products_delivery_mode_insert_sql["1"] = array("insert" => " ('1', '0', 'Face to face trade')");
$products_delivery_mode_insert_sql["2"] = array("insert" => " ('2', '0', 'Put into my account')");
$products_delivery_mode_insert_sql["3"] = array("insert" => " ('3', '0', 'Mail')");
$products_delivery_mode_insert_sql["4"] = array("insert" => " ('4', '0', 'Open Store')");

insert_new_records('products_delivery_mode', "products_delivery_mode_id", $products_delivery_mode_insert_sql, $DBTables, "(`products_delivery_mode_id`, `custom_products_type`, `products_delivery_mode_title`)");

// End of insert new records into products_delivery_mode table (for Currency Product Delivery Mode)
?> 