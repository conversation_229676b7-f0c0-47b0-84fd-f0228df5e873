<?
/*
	$Id: version_3_15_2.php,v 1.4 2011/04/19 06:57:32 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_latest_news_tag_categories_fields = get_table_fields('latest_news_tag_categories');

// <PERSON><PERSON>
// Insert new records into admin_files table (for api report page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='reports.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["api_report.php"] = array(	"insert" => " ('api_report.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   								);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='api_report.php' AND admin_files_is_boxes=0 ");
}
// End of Insert new records into admin_files table (for api report page)

// Insert new records into api_report table (for permission on query CD Key Direct Top Up report)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='api_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["API_REPORT_CDK_DIRECT_TOP_UP"] = array("insert" => " ('API_REPORT_CDK_DIRECT_TOP_UP', 'CDK Direct Top Up', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files table (for permission on query CD Key Direct Top Up report)


// Dennis
// Create products promotion tables
$add_new_tables = array();

$add_new_tables["products_promotion"] = array (	"structure" => "CREATE TABLE  `products_promotion` (
																	  `products_id` int(11) unsigned NOT NULL default '0',
																	  `promotion_start_date` datetime default '0000-00-00 00:00:00',
																	  `promotion_end_date` datetime default '0000-00-00 00:00:00',
																	  `promotion_box_only` tinyint(1) default '0',
																	  `promotion_selling_status` tinyint(1) unsigned default NULL,
																	  `promotion_limited_stock` tinyint(1) default '0',
																	  `promotion_limited_stock_qty` mediumint(5) default NULL,
																	  PRIMARY KEY  (`products_id`)
																) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["products_promotion_description"] = array (	"structure" => "CREATE TABLE  `products_promotion_description` (
																						  `products_id` int(11) unsigned NOT NULL default '0',
																						  `language_id` tinyint(1) unsigned NOT NULL default '1',
																						  `promotion_image` varchar(32) default NULL,
																						  `promotion_image_title` varchar(64) default NULL,
																						  PRIMARY KEY  (`products_id`,`language_id`)
																			) TYPE=MyISAM;",
															"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// End of create products promotion tables

// Insert new records into admin_files table (for Product Promotion)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='catalog.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["products_promotion.php"] = array(	"insert" => " ('products_promotion.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   										);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='products_promotion.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Product Promotion)

// Siong Huat
// Insert new fields into latest_news_tag_categories table (for latest new tag frequency enhancement)
$add_new_field = array();

$add_new_field['latest_news_tag_categories'] = array (array (	"field_name" => "tag_counter",
																"field_attr" => "int(5) unsigned default 0",
																"add_after" => "tag_name"
											)
									);

add_field($add_new_field);
// End of Insert new fields into latest_news_tag_categories table (for latest new tag frequency enhancement)

// Populate tag counter based on existing news data
if (!in_array('tag_counter', $existing_latest_news_tag_categories_fields)) {
	$tag_id_select_sql = "SELECT tag_id FROM " . TABLE_LATEST_NEWS_TAG_CATEGORIES . ""; 
	$tag_id_result_sql = tep_db_query($tag_id_select_sql);
	
	while ($tag_id_row = tep_db_fetch_array($tag_id_result_sql)) {
		$tag_id_count_sql = " 	SELECT COUNT(tag_id) AS total_count
								FROM " . TABLE_LATEST_NEWS_TAG_CONTENT . "
								WHERE tag_id = " . $tag_id_row['tag_id'] . "";
		$tag_id_count_result_sql = tep_db_query($tag_id_count_sql);
		if ($tag_id_count_row = tep_db_fetch_array($tag_id_count_result_sql)) {
			$tag_counter_update_sql = "	UPDATE " . TABLE_LATEST_NEWS_TAG_CATEGORIES . "
										SET tag_counter = " . $tag_id_count_row['total_count'] . "
										WHERE tag_id = " . $tag_id_row['tag_id'] . "";
			tep_db_query($tag_counter_update_sql);
		}
	}
}
// End of populate tag counter based on existing news data
?>