<?
/*
	$Id: version_3_15_3.php,v 1.4 2011/05/12 08:14:14 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_countries_fields = get_table_fields('countries');
$existing_orders_status_stat_fields = get_table_fields('orders_status_stat');

// Wei <PERSON>
$add_new_tables = array();

// Create customers_flag_message table
$add_new_tables["customers_flag_message"] = array (	"structure" => "CREATE TABLE `customers_flag_message` (
																	  `customers_id` int(11) unsigned NOT NULL default 0,
																	  `flag_id` tinyint(1) unsigned NOT NULL,
																	  `message` text NOT NULL,
																	  `date_added` date NOT NULL default '0000-00-00 00:00:00',
																	  `added_by` varchar(255) NOT NULL default '',
																	  PRIMARY KEY (`customers_id`,`flag_id`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create customers_flag_message table

// Ching Yen
// Update records in `admin_files` table (for GST)
$select_sql = "	SELECT admin_files_id FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_name = 'gst_configuration.php' AND admin_files_is_boxes=0 " ;
$result_sql = tep_db_query($select_sql);

if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["gst_configuration.php"] = array ( "update" => " admin_files_cat_setting='0' " );
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id, admin_files_cat_setting)", " admin_files_name='gst_configuration.php' AND admin_files_is_boxes=0 ");
}
// End of update records in admin_files table (for GST)

// Insert new fields into `orders_status_stat` table
$add_new_field = array();

$add_new_field['orders_status_stat'] = array (	array (	"field_name" => "first_date",
														"field_attr" => "DATETIME NOT NULL DEFAULT '0000-00-00 00:00:00'",
														"add_after" => "occurrence"
														)
											);

add_field($add_new_field);
// End of insert new fields into `orders_status_stat` table

//sionghuat
// Insert new records into admin_files table (for customer group permission control)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_MANUAL_VERIFY_TELEPHONE"] = array("insert" => " ('CUSTOMER_MANUAL_VERIFY_TELEPHONE', 'Manual Verify Telephone', ".$row_sql["admin_files_id"].", '1', 37)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files table (for customer group permission control)

// Insert new fields into maxmind_telephone_identification table (for telesign integration)
$add_new_field = array();

$add_new_field['maxmind_telephone_identification'] = array (array (	"field_name" => "provider",
																	"field_attr" => "varchar(8) NOT NULL default 'maxmind'",
																	"add_after" => "error"
																	)
															);

add_field($add_new_field);
// End of Insert new fields into maxmind_telephone_identification table (for telesign integration)

// Insert new records into configuration_group table (for Telesign service switch and configuration)
$configuration_group_insert_sql = array();
$configuration_group_insert_sql["Phone Verification"] = array	("insert" => " ('Phone Verification', 'Phone Verification setting', '251', '1' )");

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");

$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Phone Verification'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["TELEPHONE_VERIFICATION_SERVICES"] = array("insert" => " ('Telephone Verification Services', 'TELEPHONE_VERIFICATION_SERVICES', 'telesign', 'Select between Telesign or MaxMind services, only one can be turn on. Noted: Telesign only coverred phone verification and phonetype enquiry services.', ".$row_sql["configuration_group_id"].", 1, NULL, now(), NULL, 'tep_cfg_select_option(array(\'maxmind\', \'telesign\'),')" );
	$conf_insert_sql["TELESIGN_CUSTOMER_ID"] = array("insert" => " ('Telesign Customer ID', 'TELESIGN_CUSTOMER_ID', '', 'The account ID for Telesign services.', ".$row_sql["configuration_group_id"].", 2, NULL, now(), NULL, '')" );
	$conf_insert_sql["TELESIGN_AUTHENTICATION_ID"] = array("insert" => " ('Telesign Authentication ID', 'TELESIGN_AUTHENTICATION_ID', '', 'The authentication ID for Telesign services.', ".$row_sql["configuration_group_id"].", 3, NULL, now(), NULL, '')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of Insert new records into configuration_group table (for Telesign service switch and configuration)

// Change field structure for customers_telephone_type in maxmind_telephone_identification table
$change_field_structure = array();

$change_field_structure['maxmind_telephone_identification'] = array (array (	"field_name" => "customers_telephone_type",
																				"field_attr" => "smallint(1) NOT NULL default '0'"
														 					)
																	);

change_field_structure ($change_field_structure);
// End of change field structure for customers_telephone_type in maxmind_telephone_identification table

// Insert new fields into customers_info_verification table (for telesign integration)
$add_new_field = array();

$add_new_field['customers_info_verification'] = array (	array (	"field_name" => "customers_info_verification_date",
																"field_attr" => "datetime default '0000-00-00 00:00:00'",
																"add_after" => " customers_info_verification_mode "
																),
														array (	"field_name" => "call_language",
																"field_attr" => "varchar(40) default NULL",
																"add_after" => " info_verified"
																)
														);

add_field($add_new_field);
// End of Insert new fields into customers_info_verification table (for telesign integration)

// Insert new fields into countries table (for telesign integration)
$add_new_field = array();

$add_new_field['countries'] = array (array (	"field_name" => "telesign_support",
												"field_attr" => "tinyint(1) unsigned NOT NULL default '1'",
												"add_after" => " maxmind_support"
											)
									);

add_field($add_new_field);
// End of Insert new fields into countries table (for telesign integration)

// Update records in countries table (Set telesign_support to 0 for unsupported countries)
if (!in_array('telesign_support', $existing_countries_fields)) {
	$countries_update_sql = array();
	
	$countries_update_sql['countries'] = array(	array(	"field_name" => "telesign_support",
														"update" => " telesign_support=0 ",
														"where_str" => " countries_id in (	4, 7, 16, 19, 25, 29, 45, 46, 48, 50, 51, 52, 58, 59, 60, 
																							61, 66, 69, 77, 86, 88, 89, 90, 91, 93, 94, 95, 111, 112, 
																							120, 133, 135, 139, 143, 146, 148, 154, 157, 158, 159, 163, 
																							165, 169, 178, 179, 180, 181, 182, 183, 191, 192, 194, 197, 
																							198, 201, 211, 212, 213, 217, 218, 228, 231, 232, 233, 234, 
																							236, 237 )"
														)
										 		);
	
	advance_update_records($countries_update_sql, $DBTables);
}
// End of Update records in countries table (Set telesign_support to 0 for unsupported countries)


// Update records in orders_status_stat table (Set first status insert date for each order status)
if (!in_array('first_date', $existing_orders_status_stat_fields)) {
	$sh_select_sql = "	SELECT osh.orders_id, osh.orders_status_id, osh.date_added 
						FROM " . TABLE_ORDERS . " o 
						INNER JOIN " . TABLE_ORDERS_STATUS_HISTORY . " osh 
							ON osh.orders_id = o.orders_id 
								AND osh.orders_status_id != '0' 
						WHERE o.date_purchased >= '2010-11-01 00:00:00' 
						GROUP BY osh.orders_status_id, osh.orders_id 
						ORDER BY o.orders_id, osh.orders_status_id, osh.orders_status_history_id";
	$sh_result_sql = tep_db_query($sh_select_sql);
	
	while ($sh_row = tep_db_fetch_array($sh_result_sql)) {
		$orders_status_sql = "	UPDATE " . TABLE_ORDERS_STATUS_STAT . " 
								SET first_date = '" . $sh_row['date_added'] . "' 
								WHERE orders_id = '" . $sh_row['orders_id'] . "'
									AND orders_status_id = '" . $sh_row['orders_status_id'] . "'";
		tep_db_query($orders_status_sql);
	}
}
// End of update records in orders_status_stat table (Set first status insert date for each order status)
?>