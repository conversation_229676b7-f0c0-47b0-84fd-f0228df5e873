<?
/*
	$Id: version_3_1_1.php,v 1.1 2009/03/25 04:30:00 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define countries_international_dialing_code as index key in countries table
add_index_key ('countries', 'index_countries_international_dialing_code', 'index', 'countries_international_dialing_code', $DBTables);
// End of define countries_international_dialing_code as index key in countries table

// Create cimb table
$add_new_tables = array();

$add_new_tables["cimb"] = array (	"structure" => "CREATE TABLE `cimb` (
													  `orders_id` int(11) NOT NULL default '0',
													  `cimb_channel_id` varchar(4) NOT NULL,
													  `cimb_reference_no` varchar(15) NOT NULL,
													  `cimb_amount` decimal(15,2) NOT NULL,
													  `cimb_currency` char(3) default NULL,
													  `cimb_user_full_name` varchar(40) NOT NULL,
													  `cimb_transaction_date` varchar(8) NOT NULL,
													  `cimb_transaction_time` varchar(6) NOT NULL,
													  `cimb_status` char(1) NOT NULL,
													  PRIMARY KEY  (`orders_id`)
													) TYPE=MyISAM;" ,
									"data" => ""
								);

$add_new_tables["cimb_status_history"] = array (	"structure" => "CREATE TABLE `cimb_status_history` (
																	  `cimb_status_history_id` int(11) NOT NULL auto_increment,
																	  `orders_id` int(11) NOT NULL default '0',
																	  `cimb_date` datetime NOT NULL,
																	  `cimb_status` char(1) NOT NULL,
																	  `cimb_description` varchar(64) NOT NULL,
																	  PRIMARY KEY  (`cimb_status_history_id`)
																	) TYPE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create cimb table
?>