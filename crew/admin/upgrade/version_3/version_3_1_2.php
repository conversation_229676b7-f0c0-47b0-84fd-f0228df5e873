<?
/*
	$Id: version_3_1_2.php,v 1.2 2009/03/27 12:18:28 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define time_last_click as index key in whos_online table
add_index_key ('whos_online', 'index_time_last_click', 'index', 'time_last_click', $DBTables);
// End of define time_last_click as index key in whos_online table

// Reconstruct define_mainpage table
$existing_define_mainpage_fields = get_table_fields("define_mainpage");
if (!in_array('geo_zone_id', $existing_define_mainpage_fields)) {
	// Drop existing primary key (Id, language_id) for define_mainpage table
	drop_index_key ("define_mainpage", 'PRIMARY KEY', 'primary', $DBTables, array('Id', 'language_id'));
	// End of drop existing primary key (Id, language_id) for define_mainpage table
	
	// Insert new primary key field into define_mainpage table
	$add_new_field = array();
	$add_new_field["define_mainpage"] = array (array (	"field_name" => "geo_zone_id",
										 				"field_attr" => " int(11) NOT NULL default '0' FIRST",
										 				"add_after" => ""
										 			)
											  );
	add_field ($add_new_field, false);
	
	tep_db_query("UPDATE define_mainpage SET geo_zone_id = 4;");
	
	add_index_key ("define_mainpage", 'primary key', 'primary', 'geo_zone_id, Id, language_id', $DBTables);
	// End of insert new primary key field into define_mainpage table
}

// Insert new records into aft_functions table (CD Key delivery)
$aft_functions_insert_sql = array();

$aft_functions_insert_sql["deliver_products"] = array("insert" => " ('deliver_products', 'Deliver Products', 'Deliver Products', '1', '1', '')" );
$aft_functions_insert_sql["get_order_subtotal"] = array("insert" => " ('get_order_subtotal', 'Get Order''s Subtotal', 'Get Order''s Subtotal', '0', '0', '')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_functions_insert_sql, $DBTables, "(`aft_functions_name`, `aft_functions_display_name`, `aft_functions_description`, `aft_functions_action`, `line_determine`, `aft_functions_setting`)");
// End of insert new records into aft_functions table (CD Key delivery)


// Insert new primary key field into define_mainpage table
$add_new_field = array();
$add_new_field["aft_automation"] = array (array (	"field_name" => "aft_automation_name",
									 				"field_attr" => " varchar(128) NOT NULL default ''",
									 				"add_after" => "aft_automation_mode"
									 			)
										  );
add_field ($add_new_field, false);
// End of insert new primary key field into define_mainpage table


// Update script name in aft_automation table
$aft_update_sql = array();

$aft_update_sql['aft_automation'] = array(	array(	"field_name" => "aft_automation_name",
													"update" => " aft_automation_name='Customer Order - Verifying Script' ",
													"where_str" => " aft_automation_id='1'"
													),
											array(	"field_name" => "aft_automation_name",
													"update" => " aft_automation_name='Customer Order - Processing Script' ",
													"where_str" => " aft_automation_id='2'"
													)
										 );

advance_update_records($aft_update_sql, $DBTables);
// End of update script name in aft_automation table
?>