<?
/*
	$Id: version_3_1_3.php,v 1.1 2009/04/07 09:01:40 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create search_keywords_log table
$add_new_tables = array();

$add_new_tables["search_keywords_log"] = array (	"structure" => "CREATE TABLE `search_keywords_log` (
																	  `search_keywords_log_id` int(11) NOT NULL auto_increment,
																	  `search_keywords_log_date` datetime default NULL,
																	  `search_keywords_log_keywords` text,
																	  `search_keywords_log_categories_id` varchar(32) default NULL,
																	  `search_keywords_log_customer_id` varchar(32) default NULL,
																	  `search_keywords_log_ip` varchar(15) default NULL,
																	  `search_keywords_log_ip_country` varchar(32) default NULL,
																	  PRIMARY KEY  (`search_keywords_log_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create search_keywords_log table
?>