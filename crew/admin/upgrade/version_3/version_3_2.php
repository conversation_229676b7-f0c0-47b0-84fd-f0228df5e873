<?
/*
	$Id: version_3_2.php,v 1.1 2009/04/14 08:07:45 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert require Payment Info configuration setting
$pm_select_sql = "	SELECT pci.payment_methods_id, pci.payment_configuration_info_key, pm.payment_methods_title, pm.payment_methods_parent_id, pci.payment_configuration_info_sort_order 
					FROM payment_configuration_info as pci 
					INNER JOIN payment_methods as pm 
						ON pm.payment_methods_id = pci.payment_methods_id 
					WHERE pci.payment_configuration_info_key LIKE '%_CONFIRM_COMPLETE%'
					GROUP BY pci.payment_methods_id";
$pm_result_sql = tep_db_query($pm_select_sql);

$install_key_array = array();
while ($pm_row = tep_db_fetch_array($pm_result_sql)) {
	preg_match_all("/MODULE_PAYMENT_([A-Za-z0-9_ ]+)_CONFIRM_COMPLETE/", $pm_row['payment_configuration_info_key'], $matches);
	
	if (isset($matches[1][0])) {	
		$check_key_exist_sql = "SELECT payment_configuration_info_key 
								FROM payment_configuration_info
								WHERE payment_configuration_info_key = 'MODULE_PAYMENT_".$matches[1][0]."_CUSTOMER_PAYMENT_INFO'
									AND payment_methods_id = '".$pm_row['payment_methods_id']."'";
		$check_key_result_sql = tep_db_query($check_key_exist_sql);
		if (!tep_db_num_rows($check_key_result_sql)) {
			$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>$pm_row['payment_methods_id'],
																'payment_configuration_info_title'=>'Require Customer Payment Info',
																'payment_configuration_info_key'=>"MODULE_PAYMENT_".$matches[1][0]."_CUSTOMER_PAYMENT_INFO",
																'payment_configuration_info_description'=>'Set to true if this Payment Method required customer input for Payment Information required customer payment info.',
																'payment_configuration_info_sort_order' => $pm_row['payment_configuration_info_sort_order'] + 50,
																'set_function'=>'tep_cfg_select_option(array(\'True\', \'False\'), ',
																'use_function'=>'',
																'date_added'=>'now()'
															),
											'desc' => array (	'payment_configuration_info_value'=>'False',
															 	'languages_id' => 1
															)
										);
		}
	}
}

foreach ($install_key_array as $data) {
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
	$payment_conf_id = tep_db_insert_id();

	$data['desc']['payment_configuration_info_id'] = (int)$payment_conf_id;
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
}
// End of insert require Payment Info configuration setting
?>