<?
/*
	$Id: version_3_2_1.php,v 1.7 2009/05/06 02:17:25 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

if (!in_array('categories_setting_lang', $DBTables)) {
	$transfer_categories_setting = true;
} else {
	$transfer_categories_setting = false;
}

// Insert new records into aft_functions table (for AFT function)
$aft_insert_sql = array();

$aft_insert_sql["is_order_fully_nrsc"] = array("insert" => " ('is_order_fully_nrsc', 'Order fully paid by non-reversible store credit', 'Order fully paid by non-reversible store credit', 0, 0, '')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_insert_sql, $DBTables, "(`aft_functions_name` , `aft_functions_display_name` , `aft_functions_description` , `aft_functions_action` , `line_determine` , `aft_functions_setting`)");
// End of insert new records into aft_functions table (for AFT function)

// Kee Peng
// Insert new records into admin_files_actions table (for permissions on Kick Customer)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_KICK_CUSTOMER"] = array("insert" => " ('CUSTOMER_KICK_CUSTOMER', 'End customer login session', ".$row_sql["admin_files_id"].", '1,32,41,57', 70)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permissions on Kick Customer)


// Wei Chen
// Define products_image as index key in products table
add_index_key ('products', 'index_products_image', 'index', 'products_image', $DBTables);
// End of define products_image as index key in products table

// Define data_pool_template_id as index key in data_pool_options_tags table
add_index_key ('data_pool_options_tags', 'index_data_pool_template_id', 'index', 'data_pool_template_id', $DBTables);
// End of define data_pool_template_id as index key in data_pool_options_tags table

// Define products_id as index key in orders_custom_products table
add_index_key ('orders_custom_products', 'index_products_id', 'index', 'products_id', $DBTables);
// End of define products_id as index key in orders_custom_products table

// Define polls_questions_end_date as index key in polls_questions table
add_index_key ('polls_questions', 'index_polls_questions_end_date', 'index', 'polls_questions_end_date', $DBTables);
// End of define polls_questions_end_date as index key in polls_questions table

// Wee Siong
// Create search_keywords_log table
$add_new_tables = array();

$add_new_tables["categories_setting_lang"] = array (	"structure" => "CREATE TABLE `categories_setting_lang` (
																		  `categories_id` int(11) NOT NULL default '0',
																		  `language_id` int(11) NOT NULL default '1',
																		  `categories_setting_key` varchar(64) NOT NULL default '',
																		  `categories_setting_value` varchar(255) NOT NULL default '',
																		  PRIMARY KEY  (`categories_id`,`language_id`,`categories_setting_key`),
																		  KEY `index_categories_id` (`categories_id`)
																		) TYPE=MyISAM;" ,
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create search_keywords_log table

// Insert new fields into categories, products and seo_meta_tag tables
$add_new_field = array();

$add_new_field["categories"] = array (	array (	"field_name" => "categories_auto_seo",
								 				"field_attr" => " tinyint(1) not null default '1' ",
								 				"add_after" => "categories_url_alias"
								 			),
								 		array (	"field_name" => "categories_auto_seo_type",
								 				"field_attr" => " varchar(64) not null default 'categories_name' ",
								 				"add_after" => "categories_auto_seo"
								 			)
									  );

$add_new_field["products"] = array (	array (	"field_name" => "products_auto_seo",
								 				"field_attr" => " tinyint(1) not null default '1' ",
								 				"add_after" => "products_url_alias"
								 			)
									  );

$add_new_field["seo_meta_tag"] = array (	array (	"field_name" => "language_id",
									 				"field_attr" => " int(11) not null default '1' ",
									 				"add_after" => "seo_meta_id"
									 			)
										  );

add_field ($add_new_field, false);
// End of insert new fields into categories, products and seo_meta_tag tables


if ($transfer_categories_setting) {
	tep_db_query("INSERT INTO categories_setting_lang( `categories_id` , `language_id` , `categories_setting_key` , `categories_setting_value` )
					SELECT `categories_id`, 1 , `categories_setting_key` , `categories_setting_value`
					FROM categories_setting
					WHERE categories_setting_key IN ('product_type_keyword_cdkey', 'product_type_keyword_currency', 'product_type_keyword_pwl')");

	tep_db_query("DELETE FROM categories_setting WHERE categories_setting_key IN (
					'product_type_keyword_cdkey',
					'product_type_keyword_currency',
					'product_type_keyword_pwl') ");
					
	tep_db_query("UPDATE `seo_meta_tag`  
					SET `seo_meta_query_string` = REPLACE( `seo_meta_query_string` , '&tpl=0', ''),
					`seo_meta_query_string` = REPLACE( `seo_meta_query_string` , '&tpl=1', ''),
					`seo_meta_query_string` = REPLACE( `seo_meta_query_string` , '&tpl=2', '') 
					WHERE `language_id` =1 AND `seo_meta_query_string` LIKE 'cPath=%' AND `seo_meta_query_string` LIKE '%&tpl=%'");
	
	$meta_id_array = array();
	
	$meta_id_select_sql = " SELECT MAX(`seo_meta_id`) as seo_meta_id
							FROM ". TABLE_SEO_META_TAG ." 
							WHERE `seo_meta_query_string` like 'cPath=%' and `language_id`=1 
							GROUP BY `seo_meta_query_string`";
	$meta_id_result_sql = tep_db_query($meta_id_select_sql);
	while ($meta_id_row = tep_db_fetch_array($meta_id_result_sql)) {
		$meta_id_array[] = $meta_id_row['seo_meta_id'];
	}
	
	if (count($meta_id_array)) {
		tep_db_query("	DELETE FROM ".TABLE_SEO_META_TAG." 
						WHERE `seo_meta_query_string` like 'cPath=%' and `language_id`=1 and `seo_meta_id` NOT IN
						(". implode(',',$meta_id_array) .")"
					);
	}
}
?>