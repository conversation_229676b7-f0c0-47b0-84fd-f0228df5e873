<?
/*
	$Id: version_3_2_2.php,v 1.1 2009/05/13 03:41:27 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON>
// Define payment_methods_code as index key in payment_methods table
add_index_key ('payment_methods', 'index_payment_methods_code', 'index', 'payment_methods_code', $DBTables);
// End of define payment_methods_code as index key in payment_methods table

// Define payment_methods_filename as index key in payment_methods table
add_index_key ('payment_methods', 'index_payment_methods_filename', 'index', 'payment_methods_filename', $DBTables);
// End of define payment_methods_filename as index key in payment_methods table

// Define news_id as index key in events table
add_index_key ('events', 'index_news_id', 'index', 'news_id', $DBTables);
// End of define news_id as index key in events table

// Define coupon_id and customer_id as index key in coupon_redeem_track table
add_index_key ('coupon_redeem_track', 'index_coupon_and_customer', 'index', 'coupon_id, customer_id', $DBTables);
// End of define coupon_id and customer_id as index key in coupon_redeem_track table

// Define customers_email_address as index key in orders table
add_index_key ('orders', 'index_customers_email', 'index', 'customers_email_address', $DBTables);
// End of define customers_email_address as index key in orders table

// Define events_id as index key in events_options table
add_index_key ('events_options', 'index_events_id', 'index', 'events_id', $DBTables);
// End of define events_id as index key in events_options table

// Define customers_groups_id as index key in customers table
add_index_key ('customers', 'index_customers_groups_id', 'index', 'customers_groups_id', $DBTables);
// End of define customers_groups_id as index key in customers table

// Define products_id, zip_status as index key in custom_product_vault table
add_index_key ('custom_product_vault', 'index_product_and_status', 'index', 'products_id, zip_status', $DBTables);
// End of define products_id, zip_status as index key in custom_product_vault table

// Define custom_products_type_id as index key in categories table
add_index_key ('categories', 'index_category_type', 'index', 'custom_products_type_id', $DBTables);
// End of define custom_products_type_id as index key in categories table

// Define categories_parent_path as index key in categories table
add_index_key ('categories', 'index_parent_path', 'index', 'categories_parent_path', $DBTables);
// End of define categories_parent_path as index key in categories table

// Define rtype as index key in wd_g_settings table
add_index_key ('wd_g_settings', 'index_rtype', 'index', 'rtype', $DBTables);
// End of define rtype as index key in wd_g_settings table

// Define events_options_id as index key in events_options_values table
add_index_key ('events_options_values', 'index_options_id', 'index', 'events_options_id', $DBTables);
// End of define events_options_id as index key in events_options_values table

// Define cms_menu_type as index key in cms_menu table
add_index_key ('cms_menu', 'index_cms_menu_type', 'index', 'cms_menu_type', $DBTables);
// End of define cms_menu_type as index key in cms_menu table

// Define themes_language_id and themes_type as index key in themes table
add_index_key ('themes', 'index_lang_and_themes_type', 'index', 'themes_language_id, themes_type', $DBTables);
// End of define themes_language_id and themes_type as index key in themes table

// Define buyback_request_group_date and buyback_status_id as index key in buyback_request_group table
add_index_key ('buyback_request_group', 'index_date_and_status', 'index', 'buyback_request_group_date, buyback_status_id', $DBTables);
// End of define buyback_request_group_date and buyback_status_id as index key in buyback_request_group table

// Define store_credit_daily_history_date as index key in store_credit_daily_history table
add_index_key ('store_credit_daily_history', 'index_daily_date', 'index', 'store_credit_daily_history_date', $DBTables);
// End of define store_credit_daily_history_date as index key in store_credit_daily_history table


// Insert Chinese Traditional Languages
$sq_insert_sql = array();

$sq_insert_sql["3"] = array(	"insert" => " (3, '&#32321;&#20307;&#20013;&#25991;', 'zh-TW', 'icon.gif', 'cn_traditional', 20) ",
								"update" => " name='&#32321;&#20307;&#20013;&#25991;', code='zh-TW', image='icon.gif', directory='cn_traditional', sort_order ='20' " );

insert_new_records('languages', "languages_id ", $sq_insert_sql, $DBTables, "(languages_id, name, code, image, directory, sort_order )", "");
// End of insert Chinese Traditional Languages

?>