<?
/*
	$Id: version_3_2_3.php,v 1.1 2009/05/18 10:22:14 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_custom_product_code_fields = get_table_fields(TABLE_CUSTOM_PRODUCTS_CODE);
	
// <PERSON>
// Define id1 as index key in wd_g_settings table
add_index_key ('wd_g_settings', 'index_id1', 'index', 'id1', $DBTables);
// End of define id1 as index key in wd_g_settings table

// Define language_id as index key in customers_security_questions table
add_index_key ('customers_security_questions', 'index_language_id', 'index', 'language_id', $DBTables);
// End of define language_id as index key in customers_security_questions table

// Define customers_id as index key in vip_order_allocation table
add_index_key ('vip_order_allocation', 'index_customers_id', 'index', 'customers_id', $DBTables);
// End of define customers_id as index key in vip_order_allocation table

// Create mazooma tables
$add_new_tables = array();

$add_new_tables["mazooma"] = array (	"structure" => "CREATE TABLE `mazooma` (
														  `orders_id` int(11) NOT NULL,
														  `mazooma_transaction_number` varchar(30) NOT NULL,
														  `mazooma_amount` decimal(15,4) NOT NULL default '0.0000',
														  `mazooma_currency` char(3) NOT NULL default '',
														  `mazooma_status` char(1) NOT NULL default '',
														  `mazooma_user_id` varchar(20) NOT NULL default '',
														  `mazooma_fee` decimal(15,4) NOT NULL default '0.0000',
														  `mazooma_error_code` varchar(4) NOT NULL default '',
														  PRIMARY KEY  (`orders_id`)
														) TYPE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["mazooma_status_history"] = array (	"structure" => "CREATE TABLE `mazooma_status_history` (
																	  `mazooma_status_history_id` int(11) NOT NULL auto_increment,
																	  `orders_id` int(11) NOT NULL default '0',
																	  `mazooma_status_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `mazooma_status` char(1) NOT NULL default '',
																	  `mazooma_status_description` varchar(255) default NULL,
																	  PRIMARY KEY  (`mazooma_status_history_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create mazooma tables

if (!in_array('orders_products_id', $existing_custom_product_code_fields)) {
	// Add new field 'orders_products_id' to TABLE_CUSTOM_PRODUCTS_CODE
	$add_new_field = array();
	$add_new_field[TABLE_CUSTOM_PRODUCTS_CODE] = array (	array (	"field_name" => "orders_products_id",
																	"field_attr" => " int(11) NOT NULL default '0' ",
																	"add_after" => "products_id"
																)
														);
	add_field($add_new_field);
	
	// Indexing the new field
	add_index_key(TABLE_CUSTOM_PRODUCTS_CODE, 'index_orders_products_id', 'index', 'orders_products_id', $DBTables);
	
	// Update Orders Product ID to TABLE_CUSTOM_PRODUCTS_CODE
	$orders_custom_products_array = array();
	
	$orders_custom_products_select_sql = "	SELECT ocp.orders_custom_products_id, ocp.orders_custom_products_value, ocp.orders_products_id 
											FROM orders_custom_products AS ocp 
											WHERE ocp.orders_custom_products_key = 'cd_key_id'";
	$orders_custom_products_result_sql = tep_db_query($orders_custom_products_select_sql);
	
	while ($orders_custom_products_row = tep_db_fetch_array($orders_custom_products_result_sql)) {
		if (tep_not_null($orders_custom_products_row['orders_custom_products_value'])) {
			$orders_custom_products_array = explode(",", $orders_custom_products_row['orders_custom_products_value']);
			$orders_custom_products_str = implode("','",$orders_custom_products_array);
			
			$custom_products_update_sql = "	UPDATE " . TABLE_CUSTOM_PRODUCTS_CODE . "
											SET orders_products_id = '".(int)$orders_custom_products_row['orders_products_id']."'
											WHERE custom_products_code_id IN ('".$orders_custom_products_str."')";
			tep_db_query($custom_products_update_sql);
		}
	}
}
?>