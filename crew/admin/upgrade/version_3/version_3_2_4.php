<?
/*
	$Id: version_3_2_4.php,v 1.3 2009/05/26 03:43:26 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_products_fields = get_table_fields(TABLE_PRODUCTS);

// Insert new records into configuration table (for advertisement)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Advertisement'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["ADBRITE_ACCOUNT_ID"] = array("insert" => " ('AdBrite Account ID', 'ADBRITE_ACCOUNT_ID', '', 'Your AdBrite Account ID', ".$row_sql["configuration_group_id"].", 33, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["AD4GAME_ACCOUNT_ID"] = array("insert" => " ('ad4game Account ID', 'AD4GAME_ACCOUNT_ID', '', 'Your ad4game Account ID', ".$row_sql["configuration_group_id"].", 35, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for advertisement)


// PVM
// Create Page View Module tables
$add_new_tables = array();

$add_new_tables["ip_list_history"] = array (	"structure" => "CREATE TABLE `ip_list_history` (
																  `ip_list_history_id` int(11) UNSIGNED NOT NULL auto_increment,
																  `customers_id` int(11) NOT NULL default '0',
																  `page_view_ip_list_id` int(11) UNSIGNED NOT NULL,
																  `ip_list_history_tags` varchar(32) NOT NULL,
																  `scripts_name` varchar(255) NOT NULL,
																  `ip_list_history_datatime` datetime NOT NULL,
																  `ip_list_history_remark` varchar(64) default NULL,
																  PRIMARY KEY  (`ip_list_history_id`)
																) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["ip_tags_stats"] = array (	"structure" => "CREATE TABLE `ip_tags_stats` (
															  `page_view_ip_list_id` int(11) UNSIGNED NOT NULL,
															  `script_tags_name` varchar(32) NOT NULL,
															  `ip_tags_stats_counter` smallint(9) NOT NULL,
															  `ip_tags_stats_last_visit` datetime NOT NULL,
															  PRIMARY KEY  (`page_view_ip_list_id`,`script_tags_name`)
															) TYPE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["page_view_ip_list"] = array (	"structure" => "CREATE TABLE `page_view_ip_list` (
																  `page_view_ip_list_id` int(11) UNSIGNED NOT NULL auto_increment,
																  `page_view_ip_list_ip` varchar(15) NOT NULL,
																  `page_view_ip_list_last_update` datetime NOT NULL,
																  `page_view_ip_list_last_url` varchar(255) NOT NULL,
																  `page_view_ip_list_mode` char(1) NOT NULL default '1',
																  `page_view_ip_list_list` char(1) NOT NULL,
																  `page_view_ip_list_remark` varchar(255) default NULL,
																  PRIMARY KEY  (`page_view_ip_list_id`)
																) TYPE=MyISAM;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create Page View Module tables

// Insert new records into admin_files table (for Page View Module page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["page_view_module.php"] = array("insert" => " ('page_view_module.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
						   									);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='page_view_module.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Page View Module page)

// Insert new records into admin_files_actions table (for permission on Download System Generated Report)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='download_center.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["DOWNLOAD_CENTER_PAGE_VIEW_MODULE"] = array("insert" => " ('DOWNLOAD_CENTER_PAGE_VIEW_MODULE', 'Page View Module', ".$row_sql["admin_files_id"].", '1', 150)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Download System Generated Report)


// Insert new records into buyback_status table (for chinese traditional buyback status)
$buyback_status_insert_sql = array();
$buyback_status_insert_sql["1"] = array	(	"insert" => " (1, 3, '&#23529;&#26680;', 10) " );
insert_new_records('buyback_status', "buyback_status_id", $buyback_status_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", " buyback_status_id='1' AND language_id=3 ");

$buyback_status_insert_sql = array();
$buyback_status_insert_sql["2"] = array	(	"insert" => " (2, 3, '&#34389;&#29702;', 20) " );
insert_new_records('buyback_status', "buyback_status_id", $buyback_status_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", " buyback_status_id='2' AND language_id=3 ");

$buyback_status_insert_sql = array();
$buyback_status_insert_sql["3"] = array	(	"insert" => " (3, 3, '&#23436;&#25104;', 30) " );
insert_new_records('buyback_status', "buyback_status_id", $buyback_status_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", " buyback_status_id='3' AND language_id=3 ");

$buyback_status_insert_sql = array();
$buyback_status_insert_sql["4"] = array	(	"insert" => " (4, 3, '&#21462;&#28040;', 40) " );
insert_new_records('buyback_status', "buyback_status_id", $buyback_status_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", " buyback_status_id='4' AND language_id=3 ");
// End of insert new records into buyback_status table (for chinese traditional buyback status)


// Insert secret question for Chinese Traditional Version
$sq_insert_sql = array();
$sq_insert_sql["1"] = array(	"insert" => " ('1', 3, '&#24744;&#29238;&#35242;&#30340;&#22995;&#21517;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='1' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["2"] = array(	"insert" => " ('2', 3, '&#24744;&#29238;&#35242;&#30340;&#29983;&#26085;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='2' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["3"] = array(	"insert" => " ('3', 3, '&#24744;&#29238;&#35242;&#30340;&#32887;&#26989;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='3' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["4"] = array(	"insert" => " ('4', 3, '&#24744;&#27597;&#35242;&#30340;&#22995;&#21517;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='4' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["5"] = array(	"insert" => " ('5', 3, '&#24744;&#27597;&#35242;&#30340;&#29983;&#26085;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='5' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["6"] = array(	"insert" => " ('6', 3, '&#24744;&#27597;&#35242;&#30340;&#32887;&#26989;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='6' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["7"] = array(	"insert" => " ('7', 3, '&#24744;&#37197;&#20598;&#30340;&#22995;&#21517;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='7' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["8"] = array(	"insert" => " ('8', 3, '&#24744;&#37197;&#20598;&#30340;&#29983;&#26085;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='8' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["9"] = array(	"insert" => " ('9', 3, '&#24744;&#37197;&#20598;&#30340;&#32887;&#26989;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='9' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["10"] = array(	"insert" => " ('10', 3, '&#24744;&#23567;&#23416;&#29677;&#20027;&#20219;&#30340;&#21517;&#23383;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='10' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["11"] = array(	"insert" => " ('11', 3, '&#24744;&#21021;&#20013;&#29677;&#20027;&#20219;&#30340;&#21517;&#23383;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='11' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["12"] = array(	"insert" => " ('12', 3, '&#24744;&#39640;&#20013;&#29677;&#20027;&#20219;&#30340;&#21517;&#23383;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='12' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["13"] = array(	"insert" => " ('13', 3, '&#24744;&#30340;&#23416;&#34399;&#65288;&#25110;&#24037;&#34399;&#65289;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='13' AND language_id=3 ");

$sq_insert_sql = array();
$sq_insert_sql["14"] = array(	"insert" => " ('14', 3, '&#24744;&#30340;&#20986;&#29983;&#22320;&#26159;&#65311;') " );
insert_new_records('customers_security_questions', "customers_security_question_id", $sq_insert_sql, $DBTables, "(customers_security_question_id, language_id, customers_security_question)", " customers_security_question_id='14' AND language_id=3 ");
// End of insert secret question for Chinese Traditional Version

if (!in_array('products_cat_id_path', $existing_products_fields)) {
	// add new field 'categories_parent_path' to TABLE_PRODUCTS
	$add_new_field[TABLE_PRODUCTS] = array (	array (	"field_name" => "products_cat_id_path",
														"field_attr" => " varchar(255) NOT NULL default ''",
														"add_after" => "products_main_cat_id"
													)
											);
	add_field($add_new_field);
	
	add_index_key (TABLE_PRODUCTS, 'index_products_categories', 'index', 'products_main_cat_id, products_cat_id_path', $DBTables);
	
	$product_select_sql = "	SELECT p.products_id, c.categories_parent_path, c.categories_id 
							FROM products as p
							INNER JOIN products_to_categories as ptc
								ON ptc.products_id = p.products_id
							INNER JOIN categories as c
								ON c.categories_id = ptc.categories_id";
	$product_result_sql = tep_db_query($product_select_sql);
	$product_array = array();
	while ($product_row = tep_db_fetch_array($product_result_sql)) {
		$path_str = $product_row['categories_parent_path'];
		if (tep_not_null($path_str)) {
			$path_str .= $product_row['categories_id']."_";
			$parent_path_array = explode("_",$product_row['categories_parent_path']);
			$top_parent_id = $parent_path_array[1];
		} else {
			$path_str = "_".$product_row['categories_id']."_";
			$top_parent_id = (int)$product_row['categories_id'];
		}
		
		$update_product_sql = "	UPDATE products
								SET products_cat_id_path = '".$path_str."',
									products_main_cat_id = '".(int)$top_parent_id."'
								WHERE products_id = '".$product_row['products_id']."'";
		tep_db_query($update_product_sql);
	}
}
?>