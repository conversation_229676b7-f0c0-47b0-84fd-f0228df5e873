<?
/*
	$Id: version_3_2_5.php,v 1.7 2009/06/02 09:56:12 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_page_view_ip_list_fields = get_table_fields(TABLE_PAGE_VIEW_IP_LIST);

// Wei Chen
// Define page_view_ip_list_ip as index key in page_view_ip_list table
add_index_key ('page_view_ip_list', 'index_ip', 'index', 'page_view_ip_list_ip', $DBTables);
// End of define page_view_ip_list_ip as index key in page_view_ip_list table

// Define reference_id, reference_table, language_id as index key in jos_jf_content table
add_index_key ('jos_jf_content', 'index_ip', 'index', 'reference_id, reference_table, language_id', $DBTables);
// End of define reference_id, reference_table, language_id as index key in jos_jf_content table

// Wee <PERSON>
// Create status_configuration_payment_methods table
$add_new_tables = array();

$add_new_tables["status_configuration_payment_methods"] = array (	"structure" => "CREATE TABLE `status_configuration_payment_methods` (
																					  `status_configuration_trans_type` varchar(5) NOT NULL default '',
																					  `status_configuration_source_status_id` int(11) NOT NULL default '0',
																					  `status_configuration_destination_status_id` int(11) NOT NULL default '0',
																					  `status_configuration_user_groups_id` int(11) NOT NULL default '0',
																					  `status_configuration_payment_methods_id` text NOT NULL,
																					  PRIMARY KEY  (`status_configuration_trans_type`,`status_configuration_source_status_id`,`status_configuration_destination_status_id`,`status_configuration_user_groups_id`)
																					) TYPE=MyISAM;" ,
																	"data" => ""
																);

add_new_tables ($add_new_tables, $DBTables);
// End of create status_configuration_payment_methods table

// Boon Hock
// Insert new page_view_ip_list_ip_binary field into page_view_ip_list table
$add_new_field = array();

$add_new_field[TABLE_PAGE_VIEW_IP_LIST] = array (	array (	"field_name" => "page_view_ip_list_ip_binary",
											 				"field_attr" => " varchar(32) NOT NULL default '' ",
											 				"add_after" => "page_view_ip_list_ip"
											 			),
											 		array (	"field_name" => "page_view_ip_list_ip_subnet",
											 				"field_attr" => " tinyint(1) NOT NULL default '32' ",
											 				"add_after" => "page_view_ip_list_ip_binary"
											 			),
											 		array (	"field_name" => "page_view_ip_list_last_blocked",
											 				"field_attr" => " datetime NOT NULL default '0000-00-00 00:00:00' ",
											 				"add_after" => "page_view_ip_list_remark"
											 			)
												  );

add_field($add_new_field, false);
// End of insert new page_view_ip_list_ip_binary field into page_view_ip_list table

// Insert new ip_list_history_ip_address field into ip_list_history table
$add_new_field = array();

$add_new_field[TABLE_IP_LIST_HISTORY] = array (	array (	"field_name" => "ip_list_history_ip_address",
														"field_attr" => " varchar(15) NOT NULL default ''",
														"add_after" => "ip_list_history_id"
														)
												);

add_field ($add_new_field, false);
// End of insert new ip_list_history_ip_address field into ip_list_history table

// Change field structure for page_view_ip_list_ip in page_view_ip_list table
$change_field_structure = array();

$change_field_structure['page_view_ip_list'] = array (array (	"field_name" => "page_view_ip_list_ip",
																"field_attr" => " VARCHAR(18) NOT NULL default '' "
										 					)
													);

change_field_structure ($change_field_structure);
// End of change field structure for page_view_ip_list_ip in page_view_ip_list table

// Define page_view_ip_list_id as index key in ip_list_history table
add_index_key (TABLE_IP_LIST_HISTORY, 'index_page_view_ip_list_id', 'index', 'page_view_ip_list_id', $DBTables);
// End of define page_view_ip_list_id as index key in ip_list_history table

// Remove useless admin file action key(s)
$action_keys_delete_sql = array();

$action_keys_delete_sql['ADM_WU_PROCESS_ORDER'] = array(	"unique" => "1" );

delete_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $action_keys_delete_sql, $DBTables);
// End of remove useless admin file action key(s)

if (!in_array('page_view_ip_list_ip_binary', $existing_page_view_ip_list_fields)) {
	$ip_array = array();
	
	$ip_select_sql = "	SELECT page_view_ip_list_id, page_view_ip_list_ip
						FROM " . TABLE_PAGE_VIEW_IP_LIST . " as pvil";
	$ip_result_sql = tep_db_query($ip_select_sql);
	
	while ($ip_row = tep_db_fetch_array($ip_result_sql)) {
		$update_ip_data_sql = array('page_view_ip_list_ip_binary' => tep_ip_in_binary_form($ip_row['page_view_ip_list_ip']));
		tep_db_perform(TABLE_PAGE_VIEW_IP_LIST, $update_ip_data_sql, 'update', " page_view_ip_list_id = '".$ip_row['page_view_ip_list_id']."' ");
		
		$ip_array[$ip_row['page_view_ip_list_id']] = $ip_row['page_view_ip_list_ip'];
	}
	
	if (count($ip_array)) {
		foreach ($ip_array as $ip_address_id => $ip_address_data) {
			$update_ip_data_sql = array('ip_list_history_ip_address' => $ip_address_data);
			
			tep_db_perform(TABLE_IP_LIST_HISTORY, $update_ip_data_sql, 'update', " page_view_ip_list_id = '".$ip_address_id."' ");	
		}
	}
}

// Initialize Admin Member Credit Limit
$admin_group_array = array(1, 28, 29, 32, 70, 71);
$limit_amount = 5000;

$admin_select_sql = "	SELECT admin_id
						FROM " . TABLE_ADMIN . "
						WHERE admin_groups_id IN ('".implode("','", $admin_group_array)."')";
$admin_result_sql = tep_db_query($admin_select_sql);
$admin_array = array();
while ($admin_row = tep_db_fetch_array($admin_result_sql)) {
	$admin_array[] = $admin_row['admin_id'];
}

$daily_limit_data_sql = array('admin_credit_limit_max'=> $limit_amount);
tep_db_perform(TABLE_ADMIN_CREDIT_LIMIT, $daily_limit_data_sql, 'update', " admin_id IN ('".implode("','", $admin_array)."') ");
// End of initialize Admin Member Credit Limit
?>