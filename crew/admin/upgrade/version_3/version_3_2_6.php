<?
/*
	$Id: version_3_2_6.php,v 1.1 2009/06/04 07:47:02 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert Chinese Traditional Languages
$sq_insert_sql = array();

$sq_insert_sql["3"] = array(	"insert" => " (3, '&#32321;&#39636;&#20013;&#25991;', 'zh-TW', 'icon.gif', 'cn_traditional', 20) ",
								"update" => " name='&#32321;&#39636;&#20013;&#25991;', code='zh-TW', image='icon.gif', directory='cn_traditional', sort_order ='20' " );

insert_new_records('languages', "languages_id ", $sq_insert_sql, $DBTables, "(languages_id, name, code, image, directory, sort_order )", "");
// End of insert Chinese Traditional Languages

// <PERSON><PERSON>
// Insert new page_view_ip_list_host field into page_view_ip_list table
$add_new_field = array();

$add_new_field[TABLE_PAGE_VIEW_IP_LIST] = array (	array (	"field_name" => "page_view_ip_list_host",
											 				"field_attr" => " varchar(32) NOT NULL default '' ",
											 				"add_after" => "page_view_ip_list_ip_subnet"
											 			)
												  );

add_field($add_new_field, false);
// End of insert new page_view_ip_list_host field into page_view_ip_list table

?>