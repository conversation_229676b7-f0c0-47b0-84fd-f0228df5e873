<?
/*
	$Id: version_3_2_7.php,v 1.4 2009/06/15 05:57:43 weichen Exp $
	
  	Developer: <PERSON> (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON>
// Define aro_id as index key in jos_core_acl_groups_aro_map table
add_index_key ('jos_core_acl_groups_aro_map', 'index_aro_id', 'index', 'aro_id', $DBTables);
// End of define aro_id as index key in jos_core_acl_groups_aro_map table

// Drop existing index key (parent_option) for jos_components table
drop_index_key ("jos_components", 'parent_option', 'index', $DBTables, '');
// End of drop existing index key (parent_option) for jos_components table

// Define option and parent as index key in jos_components table
add_index_key ('jos_components', 'index_option_and_parent', 'index', '`option`(16), `parent`', $DBTables);
// End of define option and parent as index key in jos_components table

// Create dineromail tables
$add_new_tables = array();

$add_new_tables["dineromail"] = array (	"structure" => "CREATE TABLE `dineromail` (
														  `orders_id` int(11) NOT NULL,
														  `dineromail_transaction_number` varchar(30) NOT NULL default '',
														  `dineromail_customer_email` varchar(32) NOT NULL,
														  `dineromail_amount` decimal(15,4) NOT NULL default '0.00',
														  `dineromail_currency` char(3) NOT NULL default '',
														  `dineromail_currency_flag` char(1) NOT NULL default '',
														  `dineromail_net_amount` decimal(15,4) NOT NULL default '0.00',
														  `dineromail_payment_method` tinyint(11) NOT NULL default '0',
														  `dineromail_status` char(1) NOT NULL default '',
														  PRIMARY KEY  (`orders_id`)
														) TYPE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["dineromail_status_history"] = array (	"structure" => "CREATE TABLE `dineromail_status_history` (
																		  `dineromail_status_history_id` int(11) NOT NULL auto_increment,
																		  `orders_id` int(11) NOT NULL default '0',
																		  `dineromail_status_date` datetime NOT NULL default '0000-00-00 00:00:00',
																		  `dineromail_status` char(1) NOT NULL default '',
																		  `dineromail_status_description` varchar(255) default NULL,
																		  PRIMARY KEY  (`dineromail_status_history_id`),
																		  KEY `idx_orders_id` (`orders_id`)
																		) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create dineromail tables

// Insert new records into admin_files_actions table (for permission on viewing customer purchase details)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_CUSTOMER_PURCHASES"] = array("insert" => " ('ORDER_CUSTOMER_PURCHASES', 'Customer Purchases Statistic', ".$row_sql["admin_files_id"].", '1,2,17,20,23,24,27,28,29,31,32,34,35,36,37,38,39,40,41,44,45,47,58,63,64,67,68,73,76,79,80,81', '3')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing customer purchase details)
?>