<?
/*
	$Id: version_3_3.php,v 1.4 2009/06/30 12:58:44 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define created as index key in jos_community_photos table
add_index_key ('jos_community_photos', 'index_created', 'index', 'created', $DBTables);
// End of define created as index key in jos_community_photos table

// Define connect_to, status as index key in jos_community_connection table
add_index_key ('jos_community_connection', 'index_connect_to_status', 'index', 'connect_to, status', $DBTables);
// End of define connect_to, status as index key in jos_community_connection table

// Define value as index key in jos_core_acl_aro table
add_index_key ('jos_core_acl_aro', 'index_value', 'index', '`value`(16)', $DBTables);
// End of define value as index key in jos_core_acl_aro table

// Define polls_questions_options_id as index key in polls_questions_answers table
add_index_key ('polls_questions_answers', 'index_options_id', 'index', 'polls_questions_options_id', $DBTables);
// End of define polls_questions_options_id as index key in polls_questions_answers table

// Wee Siong
// Insert new records into admin_files table (for Game Landing Page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='infolinks.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["define_gamepage.php"] = array(	"insert" => " ('define_gamepage.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																			"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
											   							);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='define_gamepage.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Game Landing Page)

// Change field structure for categories_setting_value in categories_setting_lang table
$change_field_structure = array();
$change_field_structure['categories_setting_lang'] = array (array (	"field_name" => "categories_setting_value",
																	"field_attr" => " text "
													 			)
															);
change_field_structure ($change_field_structure);
// End of change field structure for categories_setting_value in categories_setting_lang table


// Create alipay tables
$add_new_tables = array();

$add_new_tables["alipay"] = array (	"structure" => "CREATE TABLE `alipay` (
													  `alipay_orders_id` int(11) NOT NULL default '0',
													  `alipay_notify_type` varchar(32) NOT NULL default '',
													  `alipay_notify_id` varchar(125) NOT NULL default '',
													  `alipay_notify_time` datetime NOT NULL default '0000-00-00 00:00:00',
													  `alipay_sign` varchar(128) NOT NULL default '',
													  `alipay_sign_type` varchar(16) NOT NULL default '',
													  `alipay_trade_no` varchar(64) NOT NULL default '',
													  `alipay_payment_type` varchar(32) NOT NULL default '',
													  `alipay_total_fee` double(13,2) NOT NULL default '0.00',
													  `alipay_currency` char(3) NOT NULL default '',
													  `alipay_trade_status` varchar(15) NOT NULL default '',
													  `alipay_buyer_id` varchar(30) NOT NULL default '',
													  `alipay_buyer_email` varchar(100) NOT NULL default '',
													  PRIMARY KEY  (`alipay_orders_id`)
													) TYPE=MyISAM;" ,
									"data" => ""
								);

$add_new_tables["alipay_status_history"] = array (	"structure" => "CREATE TABLE `alipay_status_history` (
																	  `alipay_status_history_id` int(11) NOT NULL auto_increment,
																	  `alipay_orders_id` int(11) NOT NULL default '0',
																	  `alipay_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `alipay_status` varchar(15) NOT NULL default '',
																	  `alipay_description` varchar(255) NOT NULL default '',
																	  PRIMARY KEY  (`alipay_status_history_id`),
																	  KEY `index_orders_id` (`alipay_orders_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create alipay tables
?>