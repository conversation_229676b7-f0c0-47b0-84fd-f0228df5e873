<?
/*
	$Id: version_3_3_1.php,v 1.1 2009/07/07 04:46:42 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON>

// Define creator as index key in jos_community_photos table
add_index_key ('jos_community_photos', 'index_creator', 'index', 'creator', $DBTables);
// End of define creator as index key in jos_community_photos table

// Define memberid and approved as index key in jos_community_groups_members table
add_index_key ('jos_community_groups_members', 'index_member_status', 'index', 'memberid, approved', $DBTables);
// End of define memberid and approved as index key in jos_community_groups_members table

// Define userid as index key in jos_community_apps table
add_index_key ('jos_community_apps', 'index_userid', 'index', 'userid', $DBTables);
// End of define userid as index key in jos_community_apps table

// Define name as index key in jos_core_acl_aro_groups table
add_index_key ('jos_core_acl_aro_groups', 'index_name', 'index', '`name`(8)', $DBTables);
// End of define name as index key in jos_core_acl_aro_groups table


// Kee Peng
// Create temp_process table
$add_new_tables = array();

$add_new_tables["temp_process"] = array (	"structure" => "CREATE TABLE `temp_process` (
															  `temp_id` int(11) NOT NULL auto_increment,
															  `page_name` varchar(255) NOT NULL default '',
															  `match_case` varchar(255) NOT NULL default '',
															  `extra_info` varchar(255) NOT NULL default '',
															  `created_date` datetime NOT NULL default '0000-00-00 00:00:00',
															  PRIMARY KEY  (`temp_id`)
															) ENGINE=MyISAM;" ,
											"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create temp_process table
?>