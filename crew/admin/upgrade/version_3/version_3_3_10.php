<?
/*
	$Id: version_3_3_10.php,v 1.2 2009/07/31 11:22:44 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_customers_security_fields = get_table_fields('customers_security');

if (in_array('customers_security_question_2', $existing_customers_security_fields)) {
	// Delete customers_security_question_2, customers_security_answer_2, customers_security_question_3 and customers_security_answer_3 fields from customers_security table
	$delete_field = array();
	
	$delete_field["customers_security"] = array  (	array( "field_name" => "customers_security_question_2"),
											 		array( "field_name" => "customers_security_answer_2"),
											 		array( "field_name" => "customers_security_question_3"),
											 		array( "field_name" => "customers_security_answer_3")
											 );
	delete_field ($delete_field);
	// End of delete customers_security_question_2, customers_security_answer_2, customers_security_question_3 and customers_security_answer_3 fields from customers_security table
	
	$login_sites_update_sql = "	UPDATE customers 
								SET customers_login_sites = '0,1'
								WHERE customers_login_sites = '1'";
	tep_db_query($login_sites_update_sql);
}

// Insert new records into configuration table (for advertisement)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Advertisement'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["MSN_ACCOUNT_ID"] = array("insert" => " ('MSN Account ID', 'MSN_ACCOUNT_ID', '', 'Your MSN Account ID', ".$row_sql["configuration_group_id"].", 37, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for advertisement)
?>