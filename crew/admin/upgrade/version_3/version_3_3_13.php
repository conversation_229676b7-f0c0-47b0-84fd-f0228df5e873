<?
/*
	$Id: version_3_3_13.php,v 1.1 2009/08/04 03:18:15 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON><PERSON>
// Create tmp table for page view module, for reset id and size purpose.
$add_new_tables = array();

$add_new_tables["ip_list_history_tmp"] = array (	"structure" => "CREATE TABLE `ip_list_history_tmp` (
																		`ip_list_history_id` int(11) unsigned NOT NULL auto_increment,
																		`ip_list_history_ip_address` varchar(15) NOT NULL default '',
																		`customers_id` int(11) NOT NULL default '0',
																		`page_view_ip_list_id` int(11) unsigned NOT NULL default '0',
																		`ip_list_history_tags` varchar(32) NOT NULL default '',
																		`scripts_name` varchar(255) NOT NULL default '',
																		`ip_list_history_datatime` datetime NOT NULL default '0000-00-00 00:00:00',
																		`ip_list_history_remark` varchar(64) default NULL,
																		PRIMARY KEY  (`ip_list_history_id`)
																) TYPE=MyISAM;" ,
													"data" => ""
												);

$add_new_tables["ip_tags_stats_tmp"] = array (	"structure" => "CREATE TABLE `ip_tags_stats_tmp` (
																	`page_view_ip_list_id` int(11) unsigned NOT NULL default '0',
																	`script_tags_name` varchar(32) NOT NULL default '',
																	`ip_tags_stats_counter` smallint(9) NOT NULL default '0',
																	`ip_tags_stats_last_visit` datetime NOT NULL default '0000-00-00 00:00:00',
																	PRIMARY KEY  (`page_view_ip_list_id`,`script_tags_name`)
																) TYPE=MyISAM;" ,
												"data" => ""
											);

$add_new_tables["page_view_ip_list_tmp"] = array (	"structure" => "CREATE TABLE `page_view_ip_list_tmp` (
																	  	`page_view_ip_list_id` int(11) UNSIGNED NOT NULL auto_increment,
																	  	`page_view_ip_list_ip` varchar(15) NOT NULL,
																	  	`page_view_ip_list_ip_binary` varchar(32) NOT NULL default '',
																		`page_view_ip_list_ip_subnet` tinyint(1) NOT NULL default '32',
																		`page_view_ip_list_host` varchar(32) NOT NULL default '',
																		`page_view_ip_list_last_update` datetime NOT NULL default '0000-00-00 00:00:00',
																		`page_view_ip_list_last_url` varchar(255) NOT NULL default '',
																		`page_view_ip_list_mode` char(1) NOT NULL default '1',
																		`page_view_ip_list_list` char(1) NOT NULL default '',
																		`page_view_ip_list_remark` varchar(255) default NULL,
																		`page_view_ip_list_last_blocked` datetime NOT NULL default '0000-00-00 00:00:00',
																		PRIMARY KEY  (`page_view_ip_list_id`)
																) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);

add_index_key ('ip_list_history_tmp', 'index_page_view_ip_list_id', 'index', 'page_view_ip_list_id', $DBTables);
// End of create status_configuration_payment_methods table

?>