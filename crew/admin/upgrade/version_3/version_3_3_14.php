<?
/*
	$Id: version_3_3_14.php,v 1.2 2009/08/12 03:32:35 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

//  Store credit phase 1 database change
$custom_products_type_data_sql = array(	"custom_products_type_id" => 3,
										"custom_products_type_name" => 'Store Credit',
										"data_pool_id" => 0,
										"custom_products_low_stock_email" => '',
										"custom_products_add_stock_email" => '',
										"custom_products_deduct_stock_email" => ''
										);
tep_db_perform('custom_products_type', $custom_products_type_data_sql);

$add_new_field = array();
$add_new_field['coupon_gv_customer'] = array (	array (	"field_name" => "sc_currency_id",
														"field_attr" => " INT(11) NOT NULL DEFAULT '1' ",
														"add_after" => "sc_irreversible_reserve_amount"
														),
												array (	"field_name" => "sc_conversion_date",
														"field_attr" => " DATETIME NULL DEFAULT NULL ",
														"add_after" => "sc_currency_id"
									   					)
									  			);
$add_new_field['store_credit_history'] = array (	array (	"field_name" => "store_credit_history_currency_id",
															"field_attr" => " INT(11) NOT NULL DEFAULT '1' ",
															"add_after" => "store_credit_transaction_reserved"
															)
									  			);
add_field ($add_new_field, false);

$add_new_tables = array();
$add_new_tables["store_credit_conversion_history"] = array (	"structure" => "CREATE TABLE `store_credit_conversion_history` (
																				  `store_credit_conversion_history_id` int(11) NOT NULL auto_increment,
																				  `customer_id` int(11) NOT NULL default '0',
																				  `store_credit_conversion_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
																				  `store_credit_account_type` char(4) NOT NULL default '',
																				  `store_credit_conversion_from_currency_id` int(11) NOT NULL default '0',
																				  `store_credit_conversion_from_amount` decimal(15,4) NOT NULL default '0.0000',
																				  `store_credit_conversion_from_reserve_amount` decimal(15,4) NOT NULL default '0.0000',
																				  `store_credit_conversion_to_currency_id` int(11) NOT NULL default '0',
																				  `store_credit_conversion_to_amount` decimal(15,4) NOT NULL default '0.0000',
																				  `store_credit_conversion_to_reserve_amount` decimal(15,4) NOT NULL default '0.0000',
																				  `store_credit_conversion_currency_values` varchar(128) NOT NULL default '',
																				  PRIMARY KEY  (`store_credit_conversion_history_id`),
																				  KEY `customer_id` (`customer_id`)
																				) TYPE=MyISAM;" ,
																"data" => ""
															);

add_new_tables ($add_new_tables, $DBTables);
?>