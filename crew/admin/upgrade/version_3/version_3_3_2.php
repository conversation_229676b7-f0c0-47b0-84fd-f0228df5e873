<?
/*
	$Id: version_3_3_2.php,v 1.2 2009/07/14 05:39:07 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON>

// Define page_name and match_case as index key in temp_process table
add_index_key ('temp_process', 'index_page_match', 'index', '`page_name`(16), `match_case`(8)', $DBTables);
// End of define page_name and match_case as index key in temp_process table

// Chan

// Insert new fields into customers_groups_discount table
$add_new_field = array();

$add_new_field['customers_groups_discount'] = array (	array (	"field_name" => "customers_groups_rebate",
																"field_attr" => " decimal(8,2) NOT NULL default '0.00' ",
																"add_after" => ""
																)
													);

add_field($add_new_field);
// End of insert new fields into customers_groups_discount table
?>