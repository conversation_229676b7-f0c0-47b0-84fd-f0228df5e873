<?
/*
	$Id: version_3_3_3.php,v 1.3 2009/07/15 07:44:06 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_orders_fields = get_table_fields('orders');

// Insert new fields into offgamers point related tables
$add_new_field = array();

$add_new_field['orders_products'] = array (	array (	"field_name" => "op_rebate",
													"field_attr" => " int(11) default NULL ",
													"add_after" => "final_price"
													),
											array (	"field_name" => "op_rebate_delivered",
													"field_attr" => " int(11) default NULL ",
													"add_after" => "op_rebate"
													)
										);

$add_new_field['orders'] = array (	array (	"field_name" => "orders_rebated",
											"field_attr" => " tinyint(1) NOT NULL default '0' ",
											"add_after" => ""
											)
									);

$add_new_field['coupon_gv_customer'] = array (	array (	"field_name" => "sp_amount",
														"field_attr" => " decimal(15, 2) NOT NULL default '0.00' ",
														"add_after" => ""
														)
												);

add_field($add_new_field);
// End of insert new fields into offgamers point related tables

// Create offgamers point related tables
$add_new_tables = array();

$add_new_tables["store_points_history"] = array (	"structure" => "CREATE TABLE `store_points_history` (
																	  `store_points_history_id` int(11) NOT NULL auto_increment,
																	  `customer_id` int(11) NOT NULL default '0',
																	  `store_points_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `store_points_history_debit_amount` decimal(15,2) default NULL,
																	  `store_points_history_credit_amount` decimal(15,2) default NULL,
																	  `store_points_history_after_balance` decimal(15,2) NOT NULL default '0.00',
																	  `store_points_history_trans_type` varchar(10) NOT NULL default '',
																	  `store_points_history_trans_id` varchar(255) NOT NULL default '',
																	  `store_points_history_activity_type` char(2) NOT NULL default '',
																	  `store_points_history_activity_title` varchar(128) NOT NULL default '',
																	  `store_points_history_activity_desc` text NOT NULL,
																	  `store_points_history_activity_desc_show` tinyint(1) NOT NULL default '0',
																	  `store_points_history_added_by` varchar(128) NOT NULL default '',
																	  `store_points_history_added_by_role` varchar(16) NOT NULL default '',
																	  `store_points_history_admin_messages` text NOT NULL,
																	  PRIMARY KEY  (`store_points_history_id`)
																	) TYPE=MyISAM AUTO_INCREMENT=1000000 ;" ,
													"data" => ""
												);

$add_new_tables["store_points_redeem"] = array (	"structure" => "CREATE TABLE `store_points_redeem` (
																	  `store_points_redeem_id` int(11) NOT NULL auto_increment,
																	  `user_id` int(11) NOT NULL default '0',
																	  `user_role` varchar(16) NOT NULL default '',
																	  `user_firstname` varchar(32) NOT NULL default '',
																	  `user_lastname` varchar(32) NOT NULL default '',
																	  `user_email_address` varchar(96) NOT NULL default '',
																	  `user_country_international_dialing_code` varchar(5) NOT NULL default '',
																	  `user_telephone` varchar(32) NOT NULL default '',
																	  `user_mobile` varchar(32) NOT NULL default '',
																	  `store_points_redeem_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `store_points_redeem_status` smallint(1) NOT NULL default '0',
																	  `store_points_redeem_amount` decimal(15,2) NOT NULL default '0.00',
																	  `store_points_request_currency` char(3) NOT NULL default '',
																	  `store_points_request_currency_amount` decimal(15,4) NOT NULL default '0.0000',
																	  `store_points_paid_amount` decimal(15,2) NOT NULL default '0.00',
																	  `store_points_paid_currency` char(3) NOT NULL default '',
																	  `store_points_paid_currency_amount` decimal(15,4) NOT NULL default '0.0000',
																	  `store_points_exchange_rate` decimal(14,8) default NULL,
																	  `store_points_redeem_reference` varchar(32) default NULL,
																	  `store_points_redeem_last_modified` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `store_points_read_mode` tinyint(1) NOT NULL default '0',
																	  PRIMARY KEY  (`store_points_redeem_id`)
																	) TYPE=MyISAM AUTO_INCREMENT=1000000 ;",
													"data" => ""
												);

$add_new_tables["store_points_redeem_history"] = array (	"structure" => "CREATE TABLE `store_points_redeem_history` (
																			  `store_points_redeem_history_id` int(11) NOT NULL auto_increment,
																			  `store_points_redeem_id` int(11) NOT NULL default '0',
																			  `store_points_redeem_status` tinyint(1) NOT NULL default '0',
																			  `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																			  `payee_notified` tinyint(1) NOT NULL default '0',
																			  `comments` text,
																			  `changed_by` varchar(128) NOT NULL default '',
																			  `changed_by_role` varchar(16) NOT NULL default '',
																			  PRIMARY KEY  (`store_points_redeem_history_id`)
																			) TYPE=MyISAM;" ,
															"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// End of create offgamers point related tables


// Insert new records into admin_files table (for OffGamers Point page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='payments.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["redeem.php"] = array(	"insert" => " ('redeem.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
													"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
				   									);
	
	$admin_files_insert_sql["store_point.php"] = array(	"insert" => " ('store_point.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   									);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='store_point.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for OffGamers Point page)

// Insert new records into admin_files_actions table (for permission on viewing OffGamers Point Redeem page)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='store_point.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["STORE_POINT_MANUAL_DEDUCT"] = array("insert" => " ('STORE_POINT_MANUAL_DEDUCT', 'Manual Deduction', ".$row_sql["admin_files_id"].", '1', '10')" );
	$admin_files_actions_insert_sql["STORE_POINT_MANUAL_ADD"] = array("insert" => " ('STORE_POINT_MANUAL_ADD', 'Manual Addition', ".$row_sql["admin_files_id"].", '1', '20')" );
	$admin_files_actions_insert_sql["STORE_POINT_REPORT_SP_FLOW"] = array("insert" => " ('STORE_POINT_REPORT_SP_FLOW', 'View Store Point Issued/Used report', ".$row_sql["admin_files_id"].", '1', '30')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing OffGamers Point Redeem page)


// Update existing orders to -1 (Not applicable)
if (!in_array('orders_rebated', $existing_orders_fields)) {
	$order_update_sql = "	UPDATE orders
							SET orders_rebated = '-1'
							WHERE 1";
	tep_db_query($order_update_sql);
}
// End of update existing orders to -1 (Not applicable)
?>