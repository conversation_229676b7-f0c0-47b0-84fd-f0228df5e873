<?
/*
	$Id: version_3_3_5.php,v 1.1 2009/07/16 07:17:58 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON><PERSON>, <PERSON>, <PERSON>
// Change field structure for cron_pending_credit_trans_id in cron_pending_credit table and customers_login_sites in customers table
$change_field_structure = array();

$change_field_structure['cron_pending_credit'] = array (array (	"field_name" => "cron_pending_credit_trans_id",
																"field_attr" => " varchar(32) NOT NULL default '0' "
										 					)
													);

$change_field_structure['customers'] = array (array (	"field_name" => "customers_login_sites",
														"field_attr" => " varchar(32) NOT NULL default '0,2' "
								 					)
											);

change_field_structure ($change_field_structure);
// End of change field structure for cron_pending_credit_trans_id in cron_pending_credit table and customers_login_sites in customers table
?>