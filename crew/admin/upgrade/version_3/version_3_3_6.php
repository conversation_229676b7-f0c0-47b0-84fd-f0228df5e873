<?
/*
	$Id: version_3_3_6.php,v 1.1 2009/07/16 07:17:58 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON><PERSON>
// Syn user id

$qu_pap_users_select_sql = "SELECT c.customers_id, c.customers_password, qpu.userid, qpu.accountuserid, qgu.authid 
							FROM qu_pap_users AS qpu
							INNER JOIN customers as c
								ON qpu.userid = c.customers_id
							INNER JOIN qu_g_users as qgu 
								ON qgu.accountuserid = qpu.accountuserid";
$qu_pap_users_result_sql = tep_db_query($qu_pap_users_select_sql);

while ($qu_pap_users_row = tep_db_fetch_array($qu_pap_users_result_sql)) {
	$update_qu_g_users_data_sql = array(	"accountuserid" => $qu_pap_users_row['customers_id'],
											"authid" => $qu_pap_users_row['customers_id']);
	tep_db_perform('qu_g_users', $update_qu_g_users_data_sql, 'update', " accountuserid = '".$qu_pap_users_row['accountuserid']."' ");
	
	$update_qu_g_authusers_data_sql = array("authid" => $qu_pap_users_row['customers_id'],
											"rpassword" => $qu_pap_users_row['customers_password']);
	tep_db_perform('qu_g_authusers', $update_qu_g_authusers_data_sql, 'update', " authid = '".$qu_pap_users_row['authid']."' ");
	
	$update_qu_pap_data_sql = array("accountuserid" => $qu_pap_users_row['customers_id']);
	tep_db_perform('qu_pap_users', $update_qu_pap_data_sql, 'update', " userid = '".$qu_pap_users_row['userid']."' ");
}
?>