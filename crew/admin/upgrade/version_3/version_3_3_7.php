<?
/*
	$Id: version_3_3_7.php,v 1.1 2009/07/22 03:48:56 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new fields into automate_buyback_price table
$add_new_field = array();

$add_new_field['automate_buyback_price'] = array (	array (	"field_name" => "floating_qty",
															"field_attr" => " int(11) NOT NULL default 0 ",
															"add_after" => "buyback_price"
															),
													array (	"field_name" => "last_cache_date",
															"field_attr" => " datetime NOT NULL default '0000-00-00 00:00:00' ",
															"add_after" => "floating_qty"
															)
												);

add_field($add_new_field);
// End of insert new fields into automate_buyback_price table
?>