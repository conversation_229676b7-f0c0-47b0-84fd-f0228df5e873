<?
/*
	$Id: version_3_3_8.php,v 1.1 2009/07/24 07:18:06 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create payment status cronjob table
$add_new_tables = array();

$add_new_tables["cron_orders_payment_status"] = array (	"structure" => "CREATE TABLE `cron_orders_payment_status` (
																		  `orders_id` int(11) NOT NULL default 0,
																		  `counter` tinyint(4) NOT NULL default 0,
																		  `check_date` datetime NOT NULL,
																		  PRIMARY KEY  (`orders_id`)
																		) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create payment status cronjob table
?>