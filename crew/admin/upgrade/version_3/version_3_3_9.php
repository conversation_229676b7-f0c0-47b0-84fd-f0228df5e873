<?
/*
	$Id: version_3_3_9.php,v 1.1 2009/07/29 05:14:19 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert cron track record for cron_order_payment.php
$cron_track_rec_select_sql = "	SELECT cron_process_track_filename 
								FROM cron_process_track 
								WHERE cron_process_track_filename = 'cron_order_payment.php'";
$cron_track_rec_result_sql = tep_db_query($cron_track_rec_select_sql);

if (!tep_db_num_rows($cron_track_rec_result_sql)) {
	$cron_track_rec_data_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_order_payment.php'
				                       );
	tep_db_perform('cron_process_track', $cron_track_rec_data_array);
}
// End of insert cron track record for cron_order_payment.php
?>