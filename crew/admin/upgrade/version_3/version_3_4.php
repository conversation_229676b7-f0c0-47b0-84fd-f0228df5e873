<?
/*
	$Id: version_3_4.php,v 1.2 2009/08/18 09:21:38 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2008 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "100M");
tep_set_time_limit(0);

$add_new_tables = array();
$add_new_tables["orders_status_stat"] = array (	"structure" => "CREATE TABLE `orders_status_stat` (
																  `orders_id` int(11) NOT NULL default '0',
																  `orders_status_id` int(5) NOT NULL default '0',
																  `occurrence` tinyint(1) NOT NULL default '1',
																  `latest_date` datetime NOT NULL default '0000-00-00 00:00:00',
																  `changed_by` varchar(128) NOT NULL default '',
																  PRIMARY KEY  (`orders_id`,`orders_status_id`),
																  KEY `index_latest_date` (`latest_date`)
																) TYPE=MyISAM ;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);

// Insert new records into admin_files table (for order status changes stat)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='sales.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["order_list.php"] = array(	"insert" => " ('order_list.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
					   								);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='order_list.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for order status changes stat)


// Start populate existing orders status changes stat
$orders_status_sql = "	INSERT LOW_PRIORITY INTO orders_status_stat 
							(orders_id, orders_status_id, occurrence, latest_date, changed_by)
						VALUES ";
								
$sh_select_sql = "	SELECT orders_id, orders_status_id, date_added, changed_by 
					FROM orders_status_history
					WHERE orders_status_id != 0 
					ORDER BY orders_id, orders_status_id, orders_status_history_id";
					
$sh_result_sql = tep_db_query($sh_select_sql);
while ($sh_row = tep_db_fetch_array($sh_result_sql)) {
	$temp_o_id = $sh_row['orders_id'];
	$temp_o_s_id = $sh_row['orders_status_id'];
	
	$data_array[$temp_o_id][$temp_o_s_id]['occurrence'] = isset($data_array[$temp_o_id][$temp_o_s_id]) ? $data_array[$temp_o_id][$temp_o_s_id]['occurrence'] + 1 : 1;
	$data_array[$temp_o_id][$temp_o_s_id]['date_added'] = $sh_row['date_added'];
	$data_array[$temp_o_id][$temp_o_s_id]['changed_by'] = $sh_row['changed_by'];
	
	if(count($data_array) == 5001){
		$_data_array[$temp_o_id] = $data_array[$temp_o_id];
		unset($data_array[$temp_o_id]);
		
		foreach($data_array AS $orders_id => $orders_status_array) {
			foreach($orders_status_array AS $orders_status_id => $content) {
				$orders_status_values[] = "(". $orders_id.", "
											 . $orders_status_id.", "
											 . $content['occurrence'].", '"
											 . $content['date_added']."', '"
											 . $content['changed_by']."')";
			}
			unset($data_array[$orders_id]);
		}
		tep_db_query($orders_status_sql. implode(",", $orders_status_values) );
		
		unset($orders_status_values);
		unset($data_array);
		$data_array = $_data_array;
		unset($_data_array);
	}
}

if (isset($data_array)) {
	foreach($data_array AS $orders_id => $orders_status_array) {
		foreach($orders_status_array AS $orders_status_id => $content) {
			$orders_status_values[] = "(". $orders_id.", "
											 . $orders_status_id.", "
											 . $content['occurrence'].", '"
											 . $content['date_added']."', '"
											 . $content['changed_by']."')";
		}
		unset($data_array[$orders_id]);
	}
	tep_db_query($orders_status_sql. implode(",", $orders_status_values) );
	unset($orders_status_values);
	unset($data_array);
}

// End of populate existing orders status changes stat
?>