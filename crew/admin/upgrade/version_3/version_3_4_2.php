<?
/*
	$Id: version_3_4_2.php,v 1.1 2009/08/20 04:27:35 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert Payment Type setting for Dineromail
$pm_select_sql = "	SELECT pci.payment_methods_id, pci.payment_configuration_info_key, pm.payment_methods_title, pm.payment_methods_parent_id, pci.payment_configuration_info_sort_order 
					FROM payment_configuration_info as pci 
					INNER JOIN payment_methods as pm 
						ON pm.payment_methods_id = pci.payment_methods_id 
					WHERE pci.payment_configuration_info_key = 'MODULE_PAYMENT_DINEROMAIL_CONFIRM_COMPLETE'
					GROUP BY pci.payment_methods_id";
$pm_result_sql = tep_db_query($pm_select_sql);

$install_key_array = array();
while ($pm_row = tep_db_fetch_array($pm_result_sql)) {
	$check_key_exist_sql = "SELECT payment_configuration_info_key
							FROM payment_configuration_info
							WHERE payment_configuration_info_key = 'MODULE_PAYMENT_DINEROMAIL_MEDIOSPAGO'
								AND payment_methods_id = '".$pm_row['payment_methods_id']."'";
	$check_key_result_sql = tep_db_query($check_key_exist_sql);
	if (!tep_db_num_rows($check_key_result_sql)) {
		$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>$pm_row['payment_methods_id'],
															'payment_configuration_info_title'=>'Payment Type',
															'payment_configuration_info_key'=>"MODULE_PAYMENT_DINEROMAIL_MEDIOSPAGO",
															'payment_configuration_info_description'=>'Payment type, eg 2 = Cash Methods or Barcodes, 7 = Account Balance / eWallet...',
															'payment_configuration_info_sort_order' => $pm_row['payment_configuration_info_sort_order'] + 50,
															'set_function'=>'',
															'use_function'=>'',
															'date_added'=>'now()'
														),
										'desc' => array (	'payment_configuration_info_value'=>'',
														 	'languages_id' => 1
														)
									);
	}
}

foreach ($install_key_array as $data) {
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
	$payment_conf_id = tep_db_insert_id();
	
	$data['desc']['payment_configuration_info_id'] = (int)$payment_conf_id;
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
}
// End of insert Payment Type setting for Dineromail
?>