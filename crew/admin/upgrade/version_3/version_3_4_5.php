<?
/*
	$Id: version_3_4_5.php,v 1.2 2009/08/28 10:23:39 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration_group table (for OffGamers Point module)
$configuration_group_insert_sql = array();

$configuration_group_insert_sql["OffGamers Point"] = array	(	"insert" => " ('OffGamers Point', 'OffGamers Point Configuration', 350, 1) ",
																"update" => " sort_order=350, visible='1' "
						   									);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");
// End of insert new records into configuration_group table (for OffGamers Point module)

// Insert new records into configuration table (for OffGamers Point module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='OffGamers Point'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["OP_CREDIT_DURATION"] = array("insert" => " ('OffGamers Point Credit Duration', 'OP_CREDIT_DURATION', '10080', 'OffGamers Point credit duration (minutes)', ".$row_sql["configuration_group_id"].", 5, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["OP_MIN_REDEMPTION"] = array("insert" => " ('OffGamers Point Minimum Redemption', 'OP_MIN_REDEMPTION', '500', 'Minimum redemption point', ".$row_sql["configuration_group_id"].", 10, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for OffGamers Point module)

// add field to store_refund tables
$add_new_field = array();
$add_new_field['store_refund'] = array (array (	"field_name" => "store_refund_is_processed",
																"field_attr" => " tinyint(1) NOT NULL default '0' ",
																"add_after" => "payment_methods_parent_id"
														)
											);
add_field($add_new_field);

// Create alipay_refund_seq tables
$add_new_tables = array();
$add_new_tables["alipay_refund_seq"] = array (	"structure" => "CREATE TABLE `alipay_refund_seq` (
																  `alipay_refund_seq` smallint(4) NOT NULL  
																) TYPE=MyISAM;" ,
												"data" => ""
											);
add_new_tables ($add_new_tables, $DBTables);
?>