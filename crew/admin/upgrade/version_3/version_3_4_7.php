<?
/*
	$Id: version_3_4_7.php,v 1.1 2009/09/04 04:17:01 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON><PERSON>
// Create OpenInviter related tables.
$add_new_tables = array();

$add_new_tables["inviter_contacts"] = array (	"structure" => "CREATE TABLE `inviter_contacts` (
																 `inviter_contacts_contact_email` varchar(96) NOT NULL default '',
																 `inviter_contacts_service` varchar(16) NOT NULL default '',
																 `inviter_contacts_insert_datetime` datetime default NULL,
																 PRIMARY KEY  (`inviter_contacts_contact_email`,`inviter_contacts_service`)
																) ENGINE=MyISAM COMMENT='Keep track invited users to prevent duplicate invitations';" ,
												"data" => ""
											);

$add_new_tables["inviter_ignore_list"] = array (	"structure" => "CREATE TABLE `inviter_ignore_list` (
																	 `inviter_ignore_contact` varchar(192) NOT NULL default '',
																	 `inviter_ignore_service` varchar(16) NOT NULL default '',
																	 PRIMARY KEY  (`inviter_ignore_contact`,`inviter_ignore_service`)
																	) ENGINE=MyISAM COMMENT='Keep track user who opt out';" ,
													"data" => ""
												);

$add_new_tables["inviter_imports"] = array (	"structure" => "CREATE TABLE `inviter_imports` (
																 `inviter_imports_id` int(11) NOT NULL auto_increment,
																 `customer_id` int(11) NOT NULL default '0',
																 `inviter_imports_service` varchar(16) NOT NULL default '',
																 `inviter_imports_contact_email` varchar(96) NOT NULL default '',
																 `inviter_imports_contact_name` varchar(96) NOT NULL default '',
																 `inviter_imports_contact_joined` char(1) NOT NULL default '0',
																 `inviter_imports_code` varchar(8) NOT NULL default '' COMMENT 'To verify the link is a truely generated from our system',
																 `inviter_imports_insert_datetime` datetime default NULL,
																 `inviter_imports_insert_counter` int(1) default '1',
																 `inviter_imports_insert_ip` varchar(15) NOT NULL default '',
																 PRIMARY KEY  (`inviter_imports_id`)
																) ENGINE=MyISAM COMMENT='Invited user records';" ,
												"data" => ""
											);

$add_new_tables["inviter_messages"] = array (	"structure" => "CREATE TABLE `inviter_messages` (
																 `inviter_messages_id` int(11) NOT NULL auto_increment,
																 `inviter_imports_id` int(11) NOT NULL default '0',
																 `inviter_messages_type` varchar(32) NOT NULL default 'P' COMMENT '''P'' - message pending; ''S'' - message sent; ''NEWSLETTER_NAME'' - send in which newsletter',
																 `message` text,
																 PRIMARY KEY  (`inviter_messages_id`)
																) ENGINE=MyISAM;" ,
												"data" => ""
											);

add_new_tables ($add_new_tables, $DBTables);
// End of create OpenInviter related tables.

// Insert cron track record for cron_graphic_mail.php
$cron_track_rec_select_sql = "	SELECT cron_process_track_filename 
								FROM cron_process_track 
								WHERE cron_process_track_filename = 'cron_graphic_mail.php'";
$cron_track_rec_result_sql = tep_db_query($cron_track_rec_select_sql);

if (!tep_db_num_rows($cron_track_rec_result_sql)) {
	$cron_track_rec_data_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_graphic_mail.php'
				                       );
	tep_db_perform('cron_process_track', $cron_track_rec_data_array);
}
// End of insert cron track record for cron_graphic_mail.php
?>