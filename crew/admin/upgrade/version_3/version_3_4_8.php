<?
/*
	$Id: version_3_4_8.php,v 1.1 2009/09/07 12:42:30 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Chan
// Create store_points table.
$add_new_tables = array();

$add_new_tables["store_points"] = array (	"structure" => "CREATE TABLE `store_points` (
															  `customers_id` int(11) NOT NULL default '0',
															  `sp_amount` decimal(15,2) NOT NULL default '0.00',
															  `sp_last_modified` datetime NOT NULL default '0000-00-00 00:00:00',
															  PRIMARY KEY  (`customers_id`)
															) ENGINE=MyISAM;" ,
											"data" => ""
										);

add_new_tables ($add_new_tables, $DBTables);
// End of create OpenInviter related tables.

// Delete sp_amount from coupon_gv_customer table
$delete_field = array();

$delete_field["coupon_gv_customer"] = array  (	array( "field_name" => "sp_amount") );
delete_field ($delete_field);
// End of delete sp_amount from coupon_gv_customer table

?>