<?
/*
	$Id: version_3_5.php,v 1.2 2009/09/09 06:01:41 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for Graphic Mail API)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Advertisement'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["GRAPHIC_MAIL_DATASET_ID"] = array("insert" => " ('Graphic Mail Data Set ID', 'GRAPHIC_MAIL_DATASET_ID', '33857', 'The dataset id for dynamic newsletter content variables', ".$row_sql["configuration_group_id"].", 45, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["GRAPHIC_MAIL_NEWSLETTER_ID"] = array("insert" => " ('Graphic Mail Newsletter ID', 'GRAPHIC_MAIL_NEWSLETTER_ID', '472758', 'The id for pre-defined newsletter used for viral inviter email', ".$row_sql["configuration_group_id"].", 50, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["GRAPHIC_MAIL_API_USERNAME"] = array("insert" => " ('Graphic Mail API Username', 'GRAPHIC_MAIL_API_USERNAME', 'jsmfC3vRA1z5zsJkMuiM1dAIX4DbYKpz2+ld3UCGKkU4P8x0xdvfbLb3uyavkgZq', 'The encrypted username used for Graphic Mail API', ".$row_sql["configuration_group_id"].", 55, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["GRAPHIC_MAIL_API_PASSWORD"] = array("insert" => " ('Graphic Mail API Password', 'GRAPHIC_MAIL_API_PASSWORD', 'JKtXO2gkPheQJ8Rzc3HCLA==', 'The encrypted password used for Graphic Mail API', ".$row_sql["configuration_group_id"].", 60, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Graphic Mail API)

// Add new field 'maxmind_phone_language' to languages table
$add_new_field = array();
$add_new_field['languages'] = array (	array (	"field_name" => "maxmind_phone_language",
												"field_attr" => " varchar(16) NOT NULL default 'English' ",
												"add_after" => "directory"
											)
									);
add_field($add_new_field);
// End of add new field 'maxmind_phone_language' to languages table
?>