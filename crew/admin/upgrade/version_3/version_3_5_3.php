<?
/*
	$Id: version_3_5_3.php,v 1.2 2009/09/29 05:14:07 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define store_points_history_trans_id as index key in store_points_history table
add_index_key ('store_points_history', 'index_trans_id', 'index', 'store_points_history_trans_id', $DBTables);
// End of define store_points_history_trans_id as index key in store_points_history table

// Insert countries_display field into countries table
$add_new_field = array();
$add_new_field["countries"] = array (	array (	"field_name" => "aft_risk_type",
								 				"field_attr" => " varchar(10) NOT NULL default 'HIGH' ",
								 				"add_after" => ""
								 			),
										array (	"field_name" => "countries_display",
								 				"field_attr" => " tinyint(1) NOT NULL default '1' ",
								 				"add_after" => ""
								 			)
									  );
add_field ($add_new_field, false);
// End of insert countries_display field into countries table

// Insert new records into aft_functions table (for AFT function)
$aft_insert_sql = array();

$aft_insert_sql["is_sharing_paypal_id"] = array("insert" => " ('is_sharing_paypal_id', 'Sharing the same Paypal ID?', 'Sharing the same Paypal ID with others customers? Return ''true'' or ''false''.', 0, 0, '')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_insert_sql, $DBTables, "(`aft_functions_name` , `aft_functions_display_name` , `aft_functions_description` , `aft_functions_action` , `line_determine` , `aft_functions_setting`)");
// End of insert new records into aft_functions table (for AFT function)

// Insert new records into admin_files table (for purchase limit)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='customers.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["purchase_limit.php"] = array(	"insert" => " ('purchase_limit.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
						   								);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='purchase_limit.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for purchase limit)

// Create customers_groups_purchase_limit table
$add_new_tables = array();

$add_new_tables["customers_groups_purchase_limit"] = array (	"structure" => "CREATE TABLE `customers_groups_purchase_limit` (
																					`purchase_limit_id` int(11) NOT NULL auto_increment ,
																					`customers_groups_id` int(11) NOT NULL ,
																					`aft_countries_risk_type` varchar(10) NOT NULL ,
																					`purchase_limit_used` tinyint(1) NOT NULL DEFAULT '0',
																					`purchase_limit_per_day` decimal(15, 4) NOT NULL ,
																					`purchase_limit_per_week` decimal(15, 4) NOT NULL ,
																					`purchase_limit_per_month` decimal(15, 4) NOT NULL ,
																					PRIMARY KEY (`purchase_limit_id`)
																				) ENGINE = MYISAM;" ,
																"data" => ""
															);

add_new_tables ($add_new_tables, $DBTables);
// End of create customers_groups_purchase_limit table
?>