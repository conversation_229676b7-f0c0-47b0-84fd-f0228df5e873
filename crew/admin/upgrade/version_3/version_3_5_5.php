<?
/*
	$Id: version_3_5_5.php,v 1.3 2009/09/30 08:37:40 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$serbia_countries_id_select_sql = "	SELECT countries_id 
									FROM " . TABLE_COUNTRIES . " 
									WHERE countries_iso_code_2 = 'RS'";
$serbia_countries_id_result_sql = tep_db_query($serbia_countries_id_select_sql);
if (!tep_db_num_rows($serbia_countries_id_result_sql)) {
	$serbia_data_array = array(	'countries_name' => 'Serbia',
								'countries_iso_code_2' => 'RS',
								'countries_iso_code_3' => 'SRB',
								'countries_currencies_id' => 11,
								'countries_international_dialing_code' => 381,
								'address_format_id' => 1,
								'maxmind_support' => 1
								);
	tep_db_perform(TABLE_COUNTRIES, $serbia_data_array);
	
	$serbia_id = tep_db_insert_id();
	
	$serbia_zone1_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'BG',
										'zone_name' => 'Belgrade'
									);
	
	$serbia_zone2_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'BO',
										'zone_name' => 'Bor'
									);
	
	$serbia_zone3_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'BR',
										'zone_name' => 'Branicevo'
									);
	
	$serbia_zone4_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'SD',
										'zone_name' => 'Central Banat'
									);
	
	$serbia_zone5_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'PD',
										'zone_name' => 'Danube'
									);
	
	$serbia_zone6_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'JA',
										'zone_name' => 'Jablanica'
									);
	
	$serbia_zone7_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'KB',
										'zone_name' => 'Kolubara'
									);
	
	$serbia_zone8_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'KO',
										'zone_name' => 'Kosovo'
									);
	
	$serbia_zone9_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'KM',
										'zone_name' => 'Kosovsko-Mitrovica'
									);
	
	$serbia_zone10_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'KP',
										'zone_name' => 'Kosovsko-Pomoravlje'
									);
	
	$serbia_zone11_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'MA',
										'zone_name' => 'Macva'
									);
	
	$serbia_zone12_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'MR',
										'zone_name' => 'Morava'
									);
	
	$serbia_zone13_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'NS',
										'zone_name' => 'Nisava'
									);
	
	$serbia_zone14_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'SC',
										'zone_name' => 'North Backa'
									);
	
	$serbia_zone15_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'SN',
										'zone_name' => 'North Banat'
									);
	
	$serbia_zone16_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'PC',
										'zone_name' => 'Pcinja'
									);
	
	$serbia_zone17_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'PE',
										'zone_name' => 'Pec'
									);
	
	$serbia_zone18_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'PI',
										'zone_name' => 'Pirot'
									);
	
	$serbia_zone19_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'PM',
										'zone_name' => 'Pomoravlje'
									);
	
	$serbia_zone20_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'PZ',
										'zone_name' => 'Prizren'
									);
	
	$serbia_zone21_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'RN',
										'zone_name' => 'Rasina'
									);
	
	$serbia_zone22_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'RS',
										'zone_name' => 'Raska'
									);
	
	$serbia_zone23_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'JC',
										'zone_name' => 'South Backa'
									);
	
	$serbia_zone24_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'JN',
										'zone_name' => 'South Banat'
									);
	
	$serbia_zone25_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'SM',
										'zone_name' => 'Srem'
									);
	
	$serbia_zone26_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'SU',
										'zone_name' => 'Sumadija'
									);
	
	$serbia_zone27_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'TO',
										'zone_name' => 'Toplica'
									);
	
	$serbia_zone28_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'ZC',
										'zone_name' => 'West Backa'
									);
	
	$serbia_zone29_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'ZJ',
										'zone_name' => 'Zajecar'
									);
	
	$serbia_zone30_data_array = array(	'zone_country_id' => $serbia_id,
										'zone_code' => 'ZL',
										'zone_name' => 'Zlatibor'
									);
	
	tep_db_perform(TABLE_ZONES, $serbia_zone1_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone2_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone3_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone4_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone5_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone6_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone7_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone8_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone9_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone10_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone11_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone12_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone13_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone14_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone15_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone16_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone17_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone18_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone19_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone20_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone21_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone22_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone23_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone24_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone25_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone26_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone27_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone28_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone29_data_array);
	tep_db_perform(TABLE_ZONES, $serbia_zone30_data_array);
	
	$serbia1_address_sql = "	UPDATE " . TABLE_ADDRESS_BOOK . " 
								SET entry_country_id = '" . (int)$serbia_id . "', 
									entry_zone_id = 0, 
									entry_state = '' 
								WHERE entry_country_id = 236 
									AND entry_zone_id = 2401";
	
	$serbia2_address_sql = "	UPDATE " . TABLE_ADDRESS_BOOK . " 
								SET entry_country_id = '" . (int)$serbia_id . "', 
									entry_zone_id = 0, 
									entry_state = '' 
								WHERE entry_country_id = 236 
									AND entry_zone_id = 2404";
	
	$serbia3_address_sql = "	UPDATE " . TABLE_ADDRESS_BOOK . " 
								SET entry_country_id = '" . (int)$serbia_id . "', 
									entry_zone_id = 0, 
									entry_state = '' 
								WHERE entry_country_id = 236 
									AND entry_zone_id = 2403";
	
	tep_db_query($serbia1_address_sql);
	tep_db_query($serbia2_address_sql);
	tep_db_query($serbia3_address_sql);
}

$montenegro_countries_id_select_sql = "	SELECT countries_id 
										FROM " . TABLE_COUNTRIES . " 
										WHERE countries_iso_code_2 = 'ME'";
$montenegro_countries_id_result_sql = tep_db_query($montenegro_countries_id_select_sql);

if (!tep_db_num_rows($montenegro_countries_id_result_sql)) {
	$montenegro_data_array = array(	'countries_name' => 'Montenegro',
									'countries_iso_code_2' => 'ME',
									'countries_iso_code_3' => 'MNE',
									'countries_currencies_id' => 11,
									'countries_international_dialing_code' => 382,
									'address_format_id' => 1,
									'maxmind_support' => 1
									);
	tep_db_perform(TABLE_COUNTRIES, $montenegro_data_array);
	
	$montenegro_id = tep_db_insert_id();
	
	$montenegro_zone1_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'AN',
											'zone_name' => 'Andrijevica'
										);
	
	$montenegro_zone2_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'BA',
											'zone_name' => 'Bar'
										);
	
	$montenegro_zone3_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'BE',
											'zone_name' => 'Berane'
										);
	
	$montenegro_zone4_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'BP',
											'zone_name' => 'Bijelo Polje'
										);
	
	$montenegro_zone5_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'BU',
											'zone_name' => 'Budva'
										);
	
	$montenegro_zone6_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'CE',
											'zone_name' => 'Cetinje'
										);
	
	$montenegro_zone7_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'DA',
											'zone_name' => 'Danilovgrad'
										);
	
	$montenegro_zone8_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'HN',
											'zone_name' => 'Herceg Novi'
										);
	
	$montenegro_zone9_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'KL',
											'zone_name' => 'Kolasins'
										);
	
	$montenegro_zone10_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'KT',
											'zone_name' => 'Kotor'
										);
	
	$montenegro_zone11_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'MK',
											'zone_name' => 'Mojkovac'
										);
	
	$montenegro_zone12_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'NK',
											'zone_name' => 'Niksic'
										);
	
	$montenegro_zone13_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'PV',
											'zone_name' => 'Plav'
										);
	
	$montenegro_zone14_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'PL',
											'zone_name' => 'Pljevlja'
										);
	
	$montenegro_zone15_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'PU',
											'zone_name' => 'Pluzine'
										);
	
	$montenegro_zone16_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'PG',
											'zone_name' => 'Podgorica'
										);
	
	$montenegro_zone17_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'RO',
											'zone_name' => 'Rozaje'
										);
	
	$montenegro_zone18_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'RO',
											'zone_name' => 'Savnik'
										);
	
	$montenegro_zone19_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'RO',
											'zone_name' => 'Tivat'
										);
	
	$montenegro_zone20_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'RO',
											'zone_name' => 'Ulcinj'
										);
	
	$montenegro_zone21_data_array = array(	'zone_country_id' => $montenegro_id,
											'zone_code' => 'RO',
											'zone_name' => 'Zabljak'
										);
	
	tep_db_perform(TABLE_ZONES, $montenegro_zone1_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone2_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone3_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone4_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone5_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone6_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone7_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone8_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone9_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone10_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone11_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone12_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone13_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone14_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone15_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone16_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone17_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone18_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone19_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone20_data_array);
	tep_db_perform(TABLE_ZONES, $montenegro_zone21_data_array);
	
	$montenegro_update_sql = "	UPDATE " . TABLE_ADDRESS_BOOK . " 
								SET entry_country_id = '" . (int)$montenegro_id . "', 
									entry_zone_id = 0, 
									entry_state = '' 
								WHERE entry_country_id = 236 
									AND entry_zone_id = 2402";
	tep_db_query($montenegro_update_sql);
	
	// For the rest update to empty. Let Customer fill in themselve
	$update_entry_country_id_sql = "	UPDATE " . TABLE_ADDRESS_BOOK . " 
										SET entry_country_id = '0', 
											entry_zone_id = 0, 
											entry_state = '' 
										WHERE entry_country_id = 236";
	tep_db_query($update_entry_country_id_sql);
}

// Yugoslavia turn off and set selected country to empty
$profile_country_update_sql = "	UPDATE " . TABLE_CUSTOMERS_INFO . " 
								SET customer_info_selected_country = 0 
								WHERE customer_info_selected_country = 236";
tep_db_query($profile_country_update_sql);


$country_update_sql = "	UPDATE " . TABLE_COUNTRIES . " 
						SET countries_display = 0 
						WHERE countries_id = 236";
tep_db_query($country_update_sql);
?>