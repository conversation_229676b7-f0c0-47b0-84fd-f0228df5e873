<?
/*
	$Id: version_3_5_7.php,v 1.3 2009/10/13 04:54:59 weichen Exp $
	
  	Developer: Wei <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/
 
// Define orderid as index key in qu_pap_transactions table
add_index_key ('qu_pap_transactions', 'index_order_id', 'index', 'orderid', $DBTables);
// End of define orderid as index key in qu_pap_transactions table

// Define notificationemail as index key in qu_g_authusers table
add_index_key ('qu_g_authusers', 'index_notification_email', 'index', '`notificationemail`(12)', $DBTables);
// End of define notificationemail as index key in qu_g_authusers table

// Define ip as index key in qu_pap_rawclicks table
add_index_key ('qu_pap_rawclicks', 'index_ip', 'index', '`ip`(12)', $DBTables);
// End of define ip as index key in qu_pap_rawclicks table


// Delete file_manager.php from admin_files table
$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'file_manager.php'";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}
// End of delete file_manager.php from admin_files table


// Add new field 'admin_login_attempt' to TABLE_ADMIN
$add_new_field = array();

$add_new_field[TABLE_ADMIN] = array (	array (	"field_name" => "admin_login_attempt",
												"field_attr" => " tinyint(2) NOT NULL default '0' ",
												"add_after" => ""
											)
									);

add_field($add_new_field);
// Add new field 'admin_login_attempt' to TABLE_ADMIN

// Insert new records into admin_files_actions table (for permission on reactivate admin member)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='admin_members.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ADMIN_REACTIVATE_ADMIN_MEMBERS"] = array("insert" => " ('ADMIN_REACTIVATE_ADMIN_MEMBERS', 'Reactivate Admin Members', ".$row_sql["admin_files_id"].", '1', '35')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on reactivate admin member)

// Insert new records into configuration table (for Admin Email when Crew hits maximum attempts of failed login)
$conf_insert_sql = array();

$conf_insert_sql["SITE_INFO_ADMIN_EMAIL_ADDRESS"] = array("insert" => " ('Admin E-Mail Address', 'SITE_INFO_ADMIN_EMAIL_ADDRESS', '', 'Email address to which the email will be send to when there is notification to administrator.(In \"Name <Email>\" format. Use \',\' as delimiter for multiple recipient)', 1, 5, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into configuration table (for Admin Email when Crew hits maximum attempts of failed login)


// Create Mozcom table
$add_new_tables = array();
$add_new_tables["mozcom"] = array (	"structure" => "CREATE TABLE `mozcom` (
													  `orders_id` int(11) NOT NULL,
													  `mozcom_status` varchar(7) NOT NULL,
													  `mozcom_ref_num` varchar(16) NOT NULL,
													  `mozcom_client_email` varchar(32) NOT NULL,
													  `mozcom_client_name` varchar(32) NOT NULL,
													  `mozcom_client_phone` varchar(16) NOT NULL,
													  `mozcom_client_address` varchar(255) NOT NULL,
													  `mozcom_digest` varchar(64) NOT NULL,
													  `mozcom_digest2` varchar(64) NOT NULL,
													  `mozcom_reason` varchar(255) NOT NULL,
													PRIMARY KEY  (`orders_id`)
											) TYPE=MyISAM;" ,
									"data" => ""
								);
add_new_tables ($add_new_tables, $DBTables);
// End of create Mozcom table
?>