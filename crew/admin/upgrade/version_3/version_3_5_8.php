<?
/*
	$Id: version_3_5_8.php,v 1.1 2009/10/21 02:25:12 weichen Exp $
	
  	Developer: Wei <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "100M");
tep_set_time_limit(0);

$customer_id_select_sql = " SELECT cgc.customer_id
                            FROM " . TABLE_COUPON_GV_CUSTOMER . " AS cgc
                            INNER JOIN " . TABLE_CURRENCIES . " AS c
                                ON (sc_currency_id = c.currencies_id AND c.code = 'USD')
                            WHERE cgc.sc_reversible_amount = '0.0000'
                            	AND cgc.sc_reversible_reserve_amount = '0.0000'
                                AND cgc.sc_irreversible_amount = '0.0000'
                                AND cgc.sc_irreversible_reserve_amount = '0.0000'
                                AND cgc.customer_id <= 173200";
$customer_id_result_sql = tep_db_query($customer_id_select_sql);
while ($customer_id_row = tep_db_fetch_array($customer_id_result_sql)) {
    $store_credit_history_id_select_sql = " SELECT store_credit_history_id
                                            FROM " . TABLE_STORE_CREDIT_HISTORY . "
                                            WHERE customer_id = '" . (int)$customer_id_row['customer_id'] . "'";
    $store_credit_history_id_result_sql = tep_db_query($store_credit_history_id_select_sql);
    if (tep_db_num_rows($store_credit_history_id_result_sql) < 1) {
        $delete_coupon_gv_customer = "  DELETE FROM " . TABLE_COUPON_GV_CUSTOMER . "
                                        WHERE customer_id = '" . (int)$customer_id_row['customer_id'] . "'";
        tep_db_query($delete_coupon_gv_customer);
       
        echo $customer_id_row['customer_id'] . '<br>';
    }
}
?>