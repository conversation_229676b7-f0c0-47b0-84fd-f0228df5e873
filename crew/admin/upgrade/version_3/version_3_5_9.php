<?
/*
	$Id: version_3_5_9.php,v 1.1 2009/10/28 02:42:00 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into aft_functions table (Purchase Limit)
$aft_functions_insert_sql = array();

$aft_functions_insert_sql["is_within_purchase_limit"] = array("insert" => " ('is_within_purchase_limit', 'Purchase within limit?', 'Purchase within limit in the period of?', '0', '0', 'a:1:{i:0;a:3:{s:4:\"type\";s:6:\"select\";s:6:\"option\";a:3:{i:0;a:2:{s:2:\"id\";s:3:\"day\";s:4:\"text\";s:3:\"day\";}i:1;a:2:{s:2:\"id\";s:4:\"week\";s:4:\"text\";s:4:\"week\";}i:2;a:2:{s:2:\"id\";s:5:\"month\";s:4:\"text\";s:5:\"month\";}}s:11:\"description\";s:42:\"Is purchuse within limit, in the period of\";}}')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_functions_insert_sql, $DBTables, "(`aft_functions_name`, `aft_functions_display_name`, `aft_functions_description`, `aft_functions_action`, `line_determine`, `aft_functions_setting`)");
// End of insert new records into aft_functions table (Purchase Limit)
?>