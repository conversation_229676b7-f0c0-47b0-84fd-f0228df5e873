<?
/*
	$Id: version_3_6.php,v 1.3 2009/10/30 10:22:30 weichen Exp $
	
  	Developer: Wei <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "100M");
tep_set_time_limit(0);

// Define customers_groups_id as index key in customers_groups_purchase_limit table
add_index_key ('customers_groups_purchase_limit', 'index_customers_groups_id', 'index', 'customers_groups_id', $DBTables);
// End of define customers_groups_id as index key in customers_groups_purchase_limit table

// Define admin_files_actions_key as index key in admin_files_actions table
add_index_key ('admin_files_actions', 'index_actions_key', 'index', 'admin_files_actions_key', $DBTables);
// End of define admin_files_actions_key as index key in admin_files_actions table

// Define brackets_tags_dependent and brackets_tags_key as index key in brackets_tags table
add_index_key ('brackets_tags', 'index_tags_dependent_and_tags_key', 'index', '`brackets_tags_dependent`, `brackets_tags_key`(6)', $DBTables);
// End of define brackets_tags_dependent and brackets_tags_key as index key in brackets_tags table

if (!in_array('latest_news_description', $DBTables)) {
	$transfer_latest_news_setting = true;
} else {
	$transfer_latest_news_setting = false;
}

// Create latest_news_description table
$add_new_tables = array();

$add_new_tables["latest_news_description"] = array (	"structure" => "CREATE TABLE `latest_news_description` (
																		`news_id` int(11) NOT NULL auto_increment,
																		`language_id` int(11) NOT NULL default '1',
																		`headline` varchar(255) NOT NULL default '',
																		`latest_news_summary` text,
																		`content` text NOT NULL,
																		PRIMARY KEY ( `news_id` , `language_id` ) ,
																		FULLTEXT (
																			`latest_news_summary` ,
																			`content`
																		)
																	) TYPE=MYISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create latest_news_description table

if ($transfer_latest_news_setting) {
	$latest_news_select_sql = " SELECT news_id, headline, latest_news_summary, content, language
								FROM ".TABLE_LATEST_NEWS; 
	$latest_news_result_sql = tep_db_query($latest_news_select_sql);
	
	while($latest_news_row = tep_db_fetch_array($latest_news_result_sql)){
		$news_id = $latest_news_row['news_id']; 
		$headline = $latest_news_row['headline'];
		$summary = $latest_news_row['latest_news_summary'];
		$content = $latest_news_row['content'];
		$language_id = $latest_news_row['language'];
		
		$latest_news_description_data_array = array('news_id' => $news_id,
  													'headline' => $headline,
  													'latest_news_summary' => $summary,
  													'content' => $content,
  													'language_id' => $language_id
                          							);
		tep_db_perform(TABLE_LATEST_NEWS_DESCRIPTION, $latest_news_description_data_array);
	}
	
	// Delete headline, latest_news_summary and content fields from latest_news table
	$delete_field = array();
	
	$delete_field[TABLE_LATEST_NEWS] = array  ( array( "field_name" => "headline"),
											 	array( "field_name" => "latest_news_summary"),
											 	array( "field_name" => "content")
											 );
	delete_field ($delete_field);
	// End of headline, latest_news_summary and content fields from latest_news table
}
?>