<?
/*
	$Id: version_3_6_2.php,v 1.2 2009/11/11 01:45:16 weichen Exp $
	
  	Developer: Wei <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files table (for E-mail Donaim List)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["email_domain_list.php"] = array(	"insert" => " ('email_domain_list.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
							   								);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='email_domain_list.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for E-mail Donaim List)

// Create E-mail Donaim List table
$add_new_tables = array();

$add_new_tables["email_domain_groups"] = array (	"structure" => "CREATE TABLE `email_domain_groups` (
																	  `email_domain_groups_id` int(11) NOT NULL auto_increment,
																	  `email_domain_groups_name` varchar(255) NOT NULL default '',
																	  `verified_files_name` varchar(100) NOT NULL default '',
																	  `not_verified_files_name` varchar(100) NOT NULL default '',
																	  PRIMARY KEY  (`email_domain_groups_id`)
																	) Type=MyISAM;",
													"data" => ""
											);

$add_new_tables["email_domain_groups_domains"] = array (	"structure" => "CREATE TABLE `email_domain_groups_domains` (
																			  `email_domain_groups_domains_id` int(11) NOT NULL auto_increment,
																			  `email_domain_groups_domains_name` varchar(64) NOT NULL default '',
																			  `email_domain_groups_id` int(11) NOT NULL default '0',
																			  KEY `index_groups_id` (`email_domain_groups_id`),
																			  PRIMARY KEY  (`email_domain_groups_domains_id`)
																			) Type=MyISAM;",
															"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create E-mail Donaim List table

// Insert new records into aft_functions table (Purchase Limit)
$aft_functions_insert_sql = array();

$aft_functions_insert_sql["is_paypal_email_verified"] = array("insert" => " ('is_paypal_email_verified', 'Does Customer\'s paypal email verified?', 'Does Customer\'s paypal email verified? Return \'true\' or \'false\'.', '0', '0', '')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_functions_insert_sql, $DBTables, "(`aft_functions_name`, `aft_functions_display_name`, `aft_functions_description`, `aft_functions_action`, `line_determine`, `aft_functions_setting`)");
// End of insert new records into aft_functions table (Purchase Limit)
?>