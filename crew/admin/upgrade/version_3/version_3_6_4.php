<?
/*
	$Id: version_3_6_4.php,v 1.1 2009/11/23 09:43:23 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into admin_files table (for Affiliate Report)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='reports.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["pap4_affiliate_report.php"] = array(	"insert" => " ('pap4_affiliate_report.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
																	"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
								   								);
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='pap4_affiliate_report.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Affiliate Report)
?>