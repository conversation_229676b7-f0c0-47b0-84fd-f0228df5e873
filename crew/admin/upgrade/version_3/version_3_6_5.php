<?
/*
	$Id: version_3_6_5.php,v 1.2 2009/11/30 09:27:23 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$op_update_checking_sql = "	SELECT customers_groups_discount_id 
							FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . "
							WHERE customers_groups_rebate >= 100";
$op_update_result_sql = tep_db_query($op_update_checking_sql);

if (tep_db_num_rows($op_update_result_sql)) {
	$do_op_update = false;
} else {
	$do_op_update = true;
}

// Insert new records into aft_functions table (Post Capture Bibit Order)
$aft_functions_insert_sql = array();

$aft_functions_insert_sql["post_capture"] = array("insert" => " ('post_capture', 'Post Capture', 'Post Capture', '1', '1', '')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_functions_insert_sql, $DBTables, "(`aft_functions_name`, `aft_functions_display_name`, `aft_functions_description`, `aft_functions_action`, `line_determine`, `aft_functions_setting`)");
// End of insert new records into aft_functions table (Post Capture Bibit Order)

if ($do_op_update) {
	// OP setting revamp. 1 OP = US $0.0001
	$op_setting_update_sql = "	UPDATE " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . "
								SET customers_groups_rebate = CONCAT('+', ABS(customers_groups_rebate) * 100)
								WHERE customers_groups_rebate < 100";
	tep_db_query($op_setting_update_sql);
	// End of OP setting revamp. 1 OP = US $0.0001
	
	// Update Store Point Redeem remark
	$comments_select_sql = "	SELECT store_points_redeem_history_id, comments 
								FROM " . TABLE_STORE_POINTS_REDEEM_HISTORY . " 
								WHERE changed_by_role = 'customers'";
	$comments_result_sql = tep_db_query($comments_select_sql);
	while($comments_row = tep_db_fetch_array($comments_result_sql)) {
		if (preg_match("(\s\(RDN-\d+\))", $comments_row['comments'])) {
			$sql_data_array = array('comments' => $comments_row['comments'] . '<b>*</b>');
			
			tep_db_perform(TABLE_STORE_POINTS_REDEEM_HISTORY, $sql_data_array, 'update', " store_points_redeem_history_id = '" . $comments_row['store_points_redeem_history_id'] . "'");
		}
	}
	// End of update Store Point Redeem remark
	
	// Update Store Point credit remark
	$sp_history_select_sql = "	SELECT store_points_history_id, store_points_history_activity_desc 
								FROM " . TABLE_STORE_POINTS_HISTORY . " 
								WHERE store_points_history_activity_type = 'D'";
	$sp_history_result_sql = tep_db_query($sp_history_select_sql);
	while ($sp_history_row = tep_db_fetch_array($sp_history_result_sql)) {
		if (preg_match("(\s\(RDN-\d+\))", $sp_history_row['store_points_history_activity_desc'])) {
			$sp_sql_data_array = array('store_points_history_activity_desc' => $sp_history_row['store_points_history_activity_desc'] . '<b>*</b>');
			
			tep_db_perform(TABLE_STORE_POINTS_HISTORY, $sp_sql_data_array, 'update', " store_points_history_id = '" . $sp_history_row['store_points_history_id'] . "'");
		}
	}
	// End of update Store Point credit remark
	
	// Multiply Store Point Balance
	$store_points_select_sql = "	SELECT customers_id, sp_amount 
									FROM " . TABLE_STORE_POINTS;
	$store_points_result_sql = tep_db_query($store_points_select_sql);
	while ($store_points_row = tep_db_fetch_array($store_points_result_sql)) {
		$new_sp_amount = $store_points_row['sp_amount'] * 100;
		$credit_amount = $new_sp_amount - $store_points_row['sp_amount'];
		
		$sp_sql_array = array('sp_amount' => $new_sp_amount);
		
		$sp_history_array = array(	'customer_id' => $store_points_row['customers_id'],
									'store_points_history_date' => 'now()',
									'store_points_history_after_balance' => $new_sp_amount,
									'store_points_history_activity_desc' => 'Adjustment on points after program revamp',
									'store_points_history_activity_desc_show' => 0,
									'store_points_history_added_by' => 'system',
									'store_points_history_added_by_role' => 'admin'
								);
		
		if ($store_points_row['sp_amount'] >= 0) {
			$sp_history_array['store_points_history_credit_amount'] = $credit_amount;
			$sp_history_array['store_points_history_activity_type'] = 'MI';
			$sp_history_array['store_points_history_activity_title'] = 'Manual Addition';
		} else {
			$sp_history_array['store_points_history_debit_amount'] = $credit_amount * -1;
			$sp_history_array['store_points_history_activity_type'] = 'MR';
			$sp_history_array['store_points_history_activity_title'] = 'Manual Deduction';
		}
		
		tep_db_perform(TABLE_STORE_POINTS, $sp_sql_array, 'update', " customers_id = '" . $store_points_row['customers_id'] . "'");
		tep_db_perform(TABLE_STORE_POINTS_HISTORY, $sp_history_array);
	}
	// End of multiply Store Point Balance
}

?>