<?
/*
	$Id: version_3_7.php,v 1.1 2009/12/03 04:37:15 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define email_domain_groups_domains_name as index key in email_domain_groups_domains table
add_index_key ('email_domain_groups_domains', 'index_domain_name', 'index', '`email_domain_groups_domains_name`(12)', $DBTables);
// End of define email_domain_groups_domains_name as index key in email_domain_groups_domains table

// Define customer_id as index key in store_points_history table
add_index_key ('store_points_history', 'index_customer_id', 'index', 'customer_id', $DBTables);
// End of define customer_id as index key in store_points_history table

// Create cron_open_restock_id table
$add_new_tables = array();

$add_new_tables["cron_open_restock_id"] = array (	"structure" => "CREATE TABLE `cron_open_restock_id` (
																	  `buyback_request_group_id` int(11) NOT NULL default '0',
																	  `orders_products_id` int(11) NOT NULL default '0',
																	  `created_date` datetime NOT NULL,
																	  PRIMARY KEY  (`buyback_request_group_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create cron_open_restock_id table

// Insert inviter_messages_language_id field into inviter_messages table
$add_new_field = array();

$add_new_field["inviter_messages"] = array (	array (	"field_name" => "inviter_messages_language_id",
										 				"field_attr" => " int(11) NOT NULL default '1' ",
										 				"add_after" => "inviter_imports_id"
										 			)
											  );
add_field ($add_new_field, false);
// End of insert inviter_messages_language_id field into inviter_messages table

?>