<?
/*
	$Id: version_3_7_1.php,v 1.2 2009/12/09 02:37:07 boonhock Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define store_account_history_trans_id as index key in store_account_history table
add_index_key ('store_account_history', 'index_trans_id', 'index', '`store_account_history_trans_id`(16)', $DBTables);
// End of define store_account_history_trans_id as index key in store_account_history table

// Add new field 'orders_products_id' to customers_groups table
$add_new_field = array();

$add_new_field['customers_groups'] = array (	array (	"field_name" => "sort_order",
														"field_attr" => " int(5) NOT NULL default '50000' ",
														"add_after" => ""
													)
											);

add_field($add_new_field);

// Add new field 'bibit_card_number' to bibit table
$add_new_field = array();
$add_new_field['bibit'] = array (	array (	"field_name" => "bibit_card_number",
											"field_attr" => " varchar(16) NOT NULL default '' ",
											"add_after" => "bibit_capture_request"
										)
								);
add_field($add_new_field);
// End of add new field 'bibit_card_number' to bibit table

add_index_key ('bibit', 'index_card_number', 'index', '`bibit_card_number`(4)', $DBTables);
?>