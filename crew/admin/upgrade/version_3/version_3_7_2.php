<?
/*
	$Id: version_3_7_2.php,v 1.1 2009/12/15 10:06:33 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Delete floating_qty field from automate_buyback_price table
$delete_field = array();

$delete_field['automate_buyback_price'] = array  ( array( "field_name" => "floating_qty") );

delete_field ($delete_field);
// End of delete floating_qty field from automate_buyback_price table

// Change field structure for price_tags_name in price_tags table
$change_field_structure = array();

$change_field_structure['price_tags'] = array (array (	"field_name" => "price_tags_name",
														"field_attr" => " varchar(64) NOT NULL default '' "
								 					)
											);

change_field_structure ($change_field_structure);
// End of change field structure for price_tags_name in price_tags table
?>