<?
/*
	$Id: version_3_7_5.php,v 1.1 2010/01/11 03:46:02 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_payment_methods_types_fields = get_table_fields('payment_methods_types');

// Insert new fields into payment_methods_types table
$add_new_field = array();

$add_new_field['payment_methods_types'] = array (	array (	"field_name" => "payment_methods_types_mode",
															"field_attr" => " varchar(7) NOT NULL default 'RECEIVE' ",
															"add_after" => "payment_methods_types_name"
															),
													array (	"field_name" => "payment_methods_types_system_define",
															"field_attr" => " tinyint(1) NOT NULL default '0' ",
															"add_after" => "payment_methods_types_mode"
															)
												);

add_field($add_new_field);
// End of insert new fields into payment_methods_types table

// Insert new records into admin_files_actions table (for permission on auto credit WSC -> NRSC)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='payment.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["PAYMENT_AUTO_CREDIT_WSC_TO_NRSC"] = array("insert" => " ('PAYMENT_AUTO_CREDIT_WSC_TO_NRSC', 'Auto credit WSC to NRSC', ".$row_sql["admin_files_id"].", '1', '10')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on auto credit WSC -> NRSC)

// Insert new records into payment_methods_types table (for SEND payment type)
$payment_type_insert_sql = array();

$payment_type_insert_sql["5"] = array("insert" => " ('5', 'MANUAL SEND', 'SEND', '0', '100')" );
$payment_type_insert_sql["6"] = array("insert" => " ('6', 'STORE CREDIT', 'SEND', '1', '200')" );

insert_new_records('payment_methods_types', "payment_methods_types_id", $payment_type_insert_sql, $DBTables, "(`payment_methods_types_id`, `payment_methods_types_name`, `payment_methods_types_mode` , `payment_methods_types_system_define`, `payment_methods_types_sort_order`)");
// End of insert new records into admin_files_actions table (for SEND payment type)

// Define payment_methods_types_mode as index key in payment_methods_types table
add_index_key ('payment_methods_types', 'index_payment_methods_mode', 'index', 'payment_methods_types_mode', $DBTables);
// End of define payment_methods_types_mode as index key in payment_methods_types table

if (!in_array('payment_methods_types_mode', $existing_payment_methods_types_fields)) {
	// Update records in payment_methods table (Set method type id for Manual send payment method)
	$payment_methods_update_sql = array();
	
	$payment_methods_update_sql['payment_methods'] = array(	array(	"field_name" => "payment_methods_types_id",
																	"update" => " payment_methods_types_id='5' ",
																	"where_str" => " (payment_methods_send_status = '1') "
																	)
														 );
	
	advance_update_records($payment_methods_update_sql, $DBTables);
	// End of update records in payment_methods table (Set method type id for Manual send payment method)
	
	// Update records in payment_methods table (Set Store Credit Send Payment Method's currency to 0)
	$payment_methods_update_sql = array();	
	$payment_methods_update_sql["9"] = array("update" => " payment_methods_send_currency='0', payment_methods_types_id='6' ");
	update_records('payment_methods', 'payment_methods_id', $payment_methods_update_sql, $DBTables);
	// End of update records in payment_methods table (Set Store Credit Send Payment Method's currency to 0)
}
?>