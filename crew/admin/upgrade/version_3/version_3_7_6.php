<?
/*
	$Id: version_3_7_6.php,v 1.1 2010/01/18 04:19:14 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define orders_id as index key in cimb_status_history table
add_index_key ('cimb_status_history', 'index_orders_id', 'index', 'orders_id', $DBTables);
// End of define orders_id as index key in cimb_status_history table

// Add new field customers_groups_payment_methods in customers_groups table
$add_new_field = array();

$add_new_field[TABLE_CUSTOMERS_GROUPS] = array (	array (	"field_name" => "customers_groups_payment_methods",
															"field_attr" => " text NOT NULL ",
															"add_after" => "customers_groups_name"
															)
													);

add_field($add_new_field);
// End of add new field customers_groups_payment_methods in customers_groups table
?>