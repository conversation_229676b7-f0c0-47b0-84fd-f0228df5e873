<?
/*
	$Id: version_3_8.php,v 1.1 2010/01/25 08:16:02 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new cron_pending_credit_trans_executable_error field into cron_pending_credit table
$add_new_field = array();

$add_new_field['cron_pending_credit'] = array (	array (	"field_name" => "cron_pending_credit_trans_executable_error",
										 				"field_attr" => " tinyint(1) NOT NULL default '0' ",
										 				"add_after" => ""
										 			)
											  );

add_field($add_new_field, false);
// End of insert new cron_pending_credit_trans_executable_error field into cron_pending_credit table

// Insert new records into aft_functions table (Check if Paypal and Telephone is verified)
$aft_functions_insert_sql = array();

$aft_functions_insert_sql["is_paypal_email_verified"] = array("insert" => " ('is_paypal_email_verified', 'Is Customer PayPal email verified?', 'Is Customer''s PayPal email verified? Return ''true'' or ''false''.', '0', '0', '')" );
$aft_functions_insert_sql["is_telephone_verified"] = array("insert" => " ('is_telephone_verified', 'Customer telephone verified?', 'Is Customer''s telephone verified? Return ''true'' or ''false''.', '0', '0', '')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_functions_insert_sql, $DBTables, "(`aft_functions_name`, `aft_functions_display_name`, `aft_functions_description`, `aft_functions_action`, `line_determine`, `aft_functions_setting`)");
// End of insert new records into aft_functions table (Check if Paypal and Telephone is verified)

// Update aft_functions_name and aft_functions_setting in aft_functions table
$aft_update_sql = array();

$aft_update_sql['aft_functions'] = array(	array(	"field_name" => "aft_functions_setting",
													"update" => "aft_functions_setting='a:1:{i:0;a:3:{s:4:\"type\";s:6:\"select\";s:6:\"option\";a:3:{i:0;a:2:{s:2:\"id\";s:5:\"''day''\";s:4:\"text\";s:3:\"day\";}i:1;a:2:{s:2:\"id\";s:6:\"''week''\";s:4:\"text\";s:4:\"week\";}i:2;a:2:{s:2:\"id\";s:7:\"''month''\";s:4:\"text\";s:5:\"month\";}}s:11:\"description\";s:42:\"Is purchuse within limit, in the period of\";}}'",
													"where_str" => " aft_functions_name='is_within_purchase_limit'"
													),
											array(	"field_name" => "aft_functions_name",
													"update" => " aft_functions_name='get_order_billing_address_country_iso' ",
													"where_str" => " aft_functions_name='get_order_billing_address'"
													),
											array(	"field_name" => "aft_functions_name",
													"update" => " aft_functions_name='get_order_profile_address_country_iso' ",
													"where_str" => " aft_functions_name='get_order_profile_country'"
													)
										 );

advance_update_records($aft_update_sql, $DBTables);
// End of update aft_functions_name and aft_functions_setting in aft_functions table
?>