<?
/*
	$Id: version_3_8_1.php,v 1.2 2010/02/02 09:04:04 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Kee Peng - Customer control Purchase ETA
// Create orders_products_eta and orders_products_history tables
$add_new_tables = array();

$add_new_tables["orders_products_eta"] = array (	"structure" => "CREATE TABLE `orders_products_eta` (
																	  `orders_products_id` int(11) NOT NULL,
																	  `expiry_hour` int(11) NOT NULL default '0',
																	  `start_time` datetime NOT NULL,
																	  PRIMARY KEY  (`orders_products_id`)
																	) TYPE=MyISAM COMMENT='Control when to turn off buybak purchase by cronjob';" ,
													"data" => ""
												);

$add_new_tables["orders_products_history"] = array (	"structure" => "CREATE TABLE `orders_products_history` (
																		  `orders_products_history_id` int(11) NOT NULL auto_increment,
																		  `buyback_request_group_id` int(11) NOT NULL,
																		  `orders_id` int(11) NOT NULL,
																		  `orders_products_id` int(11) NOT NULL,
																		  `date_added` datetime NOT NULL,
																		  `last_updated` datetime NOT NULL,
																		  `date_confirm_delivered` datetime NOT NULL,
																		  `received` tinyint(1) default NULL,
																		  `rolled_back` tinyint(1) NOT NULL default '0',
																		  `delivered_amount` int(11) NOT NULL default '0',
																		  `delivered_character` varchar(50) NOT NULL,
																		  `dispute_comment` text NOT NULL,
																		  `changed_by` varchar(128) NOT NULL,
																		  PRIMARY KEY  (`orders_products_history_id`),
																		  KEY `index_orders_id` (`orders_id`),
																		  KEY `index_orders_products_id` (`orders_products_id`)
																		) TYPE=MyISAM COMMENT='Keep track product deliver status';" ,
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create orders_products_eta and orders_products_history tables

// Wilson - Payment Surcharge
// Drop existing primary key (payment_methods_id, payment_methods_mode) for payment_fees table
drop_index_key ("payment_fees", 'PRIMARY KEY', 'primary', $DBTables, array('payment_methods_id', 'payment_methods_mode'));
// End of drop existing primary key (payment_methods_id, payment_methods_mode) for payment_fees table

// Insert new primary key field into payment_fees table
$add_new_field = array();

$add_new_field["payment_fees"] = array (array (	"field_name" => "payment_fees_id",
								 				"field_attr" => " int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY FIRST",
								 				"add_after" => ""
								 			),
								 		array (	"field_name" => "payment_fees_operator",
								 				"field_attr" => " char(2) NOT NULL default '0'",
								 				"add_after" => "payment_methods_mode"
								 			),
								 		array (	"field_name" => "currency_code",
								 				"field_attr" => " char(3) NULL ",
								 				"add_after" => "payment_fees_below_min"
								 			),
								 		array (	"field_name" => "payment_methods_currency_code",
								 				"field_attr" => " char(3) NOT NULL default 'USD' ",
								 				"add_after" => "payment_methods_id"
								 			),
								 		array (	"field_name" => "payment_fees_customers_groups_id",
								 				"field_attr" => " int(11) NOT NULL default '1' ",
								 				"add_after" => "payment_fees_below_min"
								 			),
								 		array (	"field_name" => "payment_fees_follow_group",
								 				"field_attr" => " int(11) NOT NULL default '0' ",
								 				"add_after" => "payment_fees_customers_groups_id"
								 			)
									  );

$add_new_field["customers_groups"] = array (array (	"field_name" => "customers_groups_legend_color",
									 				"field_attr" => " varchar(7) not null default '' ",
									 				"add_after" => "customers_groups_name"
									 			)
										  );

add_field ($add_new_field, false);
// End of insert new primary key field into payment_fees table

// Define payment_methods_id and payment_methods_mode as index key in payment_fees table
add_index_key ('payment_fees', 'index_method_and_mode', 'index', 'payment_methods_id, payment_methods_mode', $DBTables);
// End of define payment_methods_id and payment_methods_mode as index key in payment_fees table
?>