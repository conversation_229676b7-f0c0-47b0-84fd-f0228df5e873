<?
/*
	$Id: version_3_8_2.php,v 1.1 2010/02/08 07:30:56 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "100M");
tep_set_time_limit(0);

$existing_categories_description_fields = get_table_fields('categories_description');
$existing_products_description_fields = get_table_fields('products_description');

// Kee Peng
// Insert new fields into orders_products_history table
$add_new_field = array();

$add_new_field['orders_products_history'] = array (	array (	"field_name" => "delivery_mode",
															"field_attr" => " tinyint(1) default NULL ",
															"add_after" => "dispute_comment"
															)
												);
												
add_field($add_new_field);
// End of insert new fields into orders_products_history table

// Henry
// Insert new fields into categories_description and products_description tables
$add_new_field = array();

$add_new_field['categories_description'] = array (	array (	"field_name" => "categories_image",
															"field_attr" => " varchar(64) DEFAULT NULL ",
															"add_after" => "categories_description"
															),
													array (	"field_name" => "categories_image_title",
															"field_attr" => " varchar(64) DEFAULT NULL ",
															"add_after" => "categories_image"
															)
												);

$add_new_field['products_description'] = array (	array (	"field_name" => "products_image",
															"field_attr" => " varchar(64) DEFAULT NULL ",
															"add_after" => "products_description"
															),
													array (	"field_name" => "products_image_title",
															"field_attr" => " varchar(64) DEFAULT NULL ",
															"add_after" => "products_image"
															),
													array (	"field_name" => "products_keyword",
															"field_attr" => " varchar(64) DEFAULT NULL ",
															"add_after" => "products_name"
															),
													array (	"field_name" => "products_description_image",
															"field_attr" => " varchar(64) DEFAULT NULL ",
															"add_after" => "products_image_title"
															),
													array (	"field_name" => "products_description_image_title",
															"field_attr" => " varchar(64) DEFAULT NULL ",
															"add_after" => "products_description_image"
															)
												);
												
add_field($add_new_field);
// End of insert new fields into categories_description and products_description tables

if (!in_array('categories_image', $existing_categories_description_fields)) {
	$categories_image_update_sql = "UPDATE categories_description AS cd
									INNER JOIN categories AS c 
										ON cd.categories_id = c.categories_id
									SET cd.categories_image=c.categories_image
									WHERE cd.language_id = 1"; 
	tep_db_query($categories_image_update_sql);
}

if (!in_array('products_image', $existing_products_description_fields)) {
	$products_image_update_sql = "	UPDATE products_description AS pd
									INNER JOIN products AS p
										ON pd.products_id = p.products_id
									SET pd.products_image=p.products_image
									WHERE pd.language_id = 1"; 
	tep_db_query($products_image_update_sql);
}

if (!in_array('products_description_image', $existing_products_description_fields)) {
	$products_description_image_update_sql = "	UPDATE products_description AS pd
												INNER JOIN products AS p
													ON pd.products_id = p.products_id
												SET pd.products_description_image=p.products_description_image
												WHERE pd.language_id = 1"; 
	tep_db_query($products_description_image_update_sql);
}
?>