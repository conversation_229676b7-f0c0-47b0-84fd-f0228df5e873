<?
/*
	$Id: version_3_8_3.php,v 1.1 2010/02/22 10:14:44 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Create onecard and onecard_status_history tables
$add_new_tables = array();

$add_new_tables["onecard"] = array (	"structure" => "CREATE TABLE `onecard` (
														  `onecard_orders_id` int(11) NOT NULL,
														  `onecard_status` varchar(16) NOT NULL,
														  `onecard_voucher_code` varchar(255) NOT NULL,
														  `onecard_order_voucher_redeem` tinyint(1) NOT NULL default '0',
														  `onecard_date` datetime NOT NULL,
														  `onecard_expired` datetime NOT NULL default '0000-00-00 00:00:00',
														  `onecard_redeemed` tinyint(1) NOT NULL,
														  `onecard_redeemed_time` datetime NOT NULL default '0000-00-00 00:00:00',
														  `onecard_transaction_time` datetime NOT NULL default '0000-00-00 00:00:00',
														  `onecard_amount` double(10,2) NOT NULL,
														  `onecard_currency` varchar(3) NOT NULL,
														  PRIMARY KEY  (`onecard_orders_id`)
														) ENGINE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["onecard_status_history"] = array (	"structure" => "CREATE TABLE `onecard_status_history` (
																	  `onecard_status_history_id` int(11) NOT NULL auto_increment,
																	  `onecard_orders_id` int(11) NOT NULL,
																	  `onecard_status_history_voucher_code` varchar(255) NOT NULL,
																	  `onecard_status_history_status` varchar(16) NOT NULL,
																	  `onecard_status_history_description` text NOT NULL,
																	  `onecard_status_history_datetime` datetime NOT NULL,
																	  `onecard_changed_by` varchar(128) NOT NULL,
																	  PRIMARY KEY  (`onecard_status_history_id`)
																	) ENGINE=MyISAM;" ,
													"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create onecard and onecard_status_history tables

// Define onecard_orders_id as index key in onecard_status_history table
add_index_key ('onecard_status_history', 'index_onecard_orders_id', 'index', 'onecard_orders_id', $DBTables);
// End of define onecard_orders_id as index key in onecard_status_history table
?>