<?
/*
	$Id: version_3_8_4.php,v 1.2 2010/03/01 08:49:28 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "100M");
tep_set_time_limit(0);

// Delete categories_image from categories table, products_image and products_description_image from products table
$delete_field = array();

$delete_field['products'] = array  ( array( "field_name" => "products_image"),
								 	array( "field_name" => "products_description_image")
								 );

$delete_field['categories'] = array  ( array( "field_name" => "categories_image") );

delete_field ($delete_field);
// End of delete categories_image from categories table, products_image and products_description_image from products table


// Kee Peng
// Insert new fields into orders_products_eta and orders_products_history table
$add_new_field = array();

$add_new_field['orders_products_eta'] = array (	array (	"field_name" => "total_buyback_time",
														"field_attr" => " int(11) NOT NULL default '0' ",
														"add_after" => ""
														)
											);

$add_new_field['orders_products_history'] = array (	array (	"field_name" => "orders_products_history_record",
														"field_attr" => " varchar(255) NOT NULL ",
														"add_after" => "dispute_comment"
														)
											);
												
add_field($add_new_field);
// End of insert new fields into orders_products_eta and orders_products_history table

// Delete delivery_mode from orders_products_history table
$delete_field = array();

$delete_field['orders_products_history'] = array  ( array( "field_name" => "delivery_mode") );

delete_field ($delete_field);
// End of delivery_mode from orders_products_history table

// Insert new records into aft_functions table (Email Domain checking)
$aft_functions_insert_sql = array();

$aft_functions_insert_sql["is_match_email_domain_group"] = array("insert" => " ('is_match_email_domain_group', 'Is Customers'' email domain matches the Email Domain Group ID?', 'Is Customers'' email domain matches the Email Domain Group ID? Return ''true'' or ''false''.', '0', '0', 'a:1:{i:0;a:1:{s:4:\"type\";s:4:\"text\";}}')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_functions_insert_sql, $DBTables, "(`aft_functions_name`, `aft_functions_display_name`, `aft_functions_description`, `aft_functions_action`, `line_determine`, `aft_functions_setting`)");
// End of insert new records into aft_functions table (Email Domain checking)

// Boon Hock
// Insert Test Mode setting for OneCard payment configuration setting
$pm_select_sql = "	SELECT pci.payment_methods_id, pci.payment_configuration_info_key, pm.payment_methods_title, pm.payment_methods_parent_id, pci.payment_configuration_info_sort_order 
					FROM payment_configuration_info as pci 
					INNER JOIN payment_methods as pm 
						ON pm.payment_methods_id = pci.payment_methods_id 
					WHERE pci.payment_configuration_info_key = 'MODULE_PAYMENT_ONECARD_CONFIRM_COMPLETE'
					GROUP BY pci.payment_methods_id";
$pm_result_sql = tep_db_query($pm_select_sql);

$install_key_array = array();
while ($pm_row = tep_db_fetch_array($pm_result_sql)) {
	$check_key_exist_sql = "SELECT payment_configuration_info_key 
							FROM payment_configuration_info
							WHERE payment_configuration_info_key = 'MODULE_PAYMENT_ONECARD_TEST_MODE'
								AND payment_methods_id = '".$pm_row['payment_methods_id']."'";
	$check_key_result_sql = tep_db_query($check_key_exist_sql);
	if (!tep_db_num_rows($check_key_result_sql)) {
		$install_key_array[] = array(	'info' => array (	'payment_methods_id'=>(int)$pm_row['payment_methods_id'],
															'payment_configuration_info_title'=>'Test Mode',
															'payment_configuration_info_key'=>'MODULE_PAYMENT_ONECARD_TEST_MODE',
															'payment_configuration_info_description'=>'Testing Mode?',
															'payment_configuration_info_sort_order'=>'1000',
															'set_function'=>'tep_cfg_select_option(array(\'True\', \'False\'), ',
															'use_function'=>'',
															'date_added'=>'now()'
														),
										'desc' => array (	'payment_configuration_info_value'=>'False',
														 	'languages_id' => 1
														)
									);
	}
}

foreach ($install_key_array as $data) {
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO, $data['info']);
	$payment_conf_id = tep_db_insert_id();

	$data['desc']['payment_configuration_info_id'] = (int)$payment_conf_id;
	tep_db_perform(TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION, $data['desc']);
}
// End of insert Test Mode setting for OneCard payment configuration setting
?>