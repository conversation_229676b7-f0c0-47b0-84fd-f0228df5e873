<?
/*
	$Id: version_3_9.php,v 1.3 2010/04/02 03:12:00 weesiong Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

ini_set("memory_limit", "80M");
tep_set_time_limit(0);

// Customer Document Submission - Wee <PERSON>
// Create customers_verification_document tables
$add_new_tables = array();

$add_new_tables["customers_verification_document"] = array (	"structure" => "CREATE TABLE `customers_verification_document` (
																					`customers_id` int(11) NOT NULL default '0',
																					`files_001` varchar(32) default NULL,
																					`files_001_locked` tinyint(1) NOT NULL default '1',
																					`files_002` varchar(32) default NULL,
																					`files_002_locked` tinyint(1) NOT NULL default '1',
																					`files_003` varchar(32) default NULL,
																					`files_003_locked` tinyint(1) NOT NULL default '1',
																					`files_004` varchar(32) default NULL,
																					`files_004_locked` tinyint(1) NOT NULL default '1',
																					`files_005` varchar(32) default NULL,
																					`files_005_locked` tinyint(1) NOT NULL default '1',
																					PRIMARY KEY (`customers_id`)
																				) TYPE=MYISAM;" ,
																"data" => ""
															);

$add_new_tables["customers_verification_document_log"] = array (	"structure" => "CREATE TABLE `customers_verification_document_log` (
																					  `log_id` int(11) NOT NULL auto_increment,
																					  `log_users_id` varchar(255) NOT NULL default '',
																					  `log_users_type` varchar(16) NOT NULL default '',
																					  `log_customers_id` int(11) NOT NULL default '0',
																					  `log_docs_id` char(3) NOT NULL default '',
																					  `log_IP` varchar(15) NOT NULL default '',
																					  `log_datetime` datetime NOT NULL default '0000-00-00 00:00:00',
																					  `log_filename` varchar(32) default NULL,
																					  `log_action` varchar(20) NOT NULL default '',
																					  PRIMARY KEY  (`log_id`),
																					  KEY `index_customers_id_and_docs_id` (`log_customers_id`,`log_docs_id`)
																					) TYPE=MyISAM;" ,
																	"data" => ""
																);

add_new_tables ($add_new_tables, $DBTables);
// End of create customers_verification_document tables

// Insert new records into admin_files table (for Image Editor page)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["image_editor.php"] = array(	"insert" => " ('image_editor.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
															"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
						   								);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='image_editor.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Image Editor page)

// Insert new records into admin_files_actions table (for permission on Customer Uploaded Document)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_VIEW_LATEST_DOC"] = array("insert" => " ('CUSTOMER_VIEW_LATEST_DOC', 'View Latest Customer Document', ".$row_sql["admin_files_id"].", '1', 56)" );
	$admin_files_actions_insert_sql["CUSTOMER_VIEW_ALL_DOC"] = array("insert" => " ('CUSTOMER_VIEW_ALL_DOC', 'View All Customer Documents and Logs', ".$row_sql["admin_files_id"].", '1', 57)" );
	$admin_files_actions_insert_sql["CUSTOMER_EDIT_LATEST_DOC"] = array("insert" => " ('CUSTOMER_EDIT_LATEST_DOC', 'Edit/Upload Customer Document', ".$row_sql["admin_files_id"].", '1', 58)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Customer Uploaded Document)

// Latest News Category Selection - Wee Siong
// Create latest_news_categories table
$add_new_tables = array();

$add_new_tables["latest_news_categories"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `latest_news_categories` (
																	  `news_id` int(11) NOT NULL default '0',
																	  `categories_id` int(11) NOT NULL default '0',
																	  PRIMARY KEY  (`news_id`,`categories_id`)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create latest_news_categories table

// Migrate Existing Data to New Structure
function tep_temp_get_categories_id($category_id, &$cat_id_array) {
	$sub_category_select_sql = "SELECT categories_id
								FROM " . TABLE_CATEGORIES . "
								WHERE parent_id = " . $category_id;
	$sub_category_result_sql = tep_db_query($sub_category_select_sql);
	if ($sub_category = tep_db_fetch_array($sub_category_result_sql)) {
		do {
			$cat_id_array[$sub_category['categories_id']] = 1;
			tep_temp_get_categories_id($sub_category['categories_id'], $cat_id_array);
		} while ($sub_category = tep_db_fetch_array($sub_category_result_sql));
	} else {
		$cat_id_array[$category_id] = 1;
	}
}

$top_inc_sub_cats_id[] = array(0);
$temp_array = array();
tep_temp_get_categories_id(0, $top_inc_sub_cats_id);
$top_inc_sub_cats_id = array_keys($top_inc_sub_cats_id);

//latest news's category ids
$cat_ids_select_sql = "	SELECT news_id, news_groups_cat_id, latest_news_include_subcat 
						FROM ".TABLE_LATEST_NEWS." AS ln
						WHERE news_groups_cat_id != ''";
$cat_ids_result_sql = tep_db_query($cat_ids_select_sql);
while ($cat_ids_row = tep_db_fetch_array($cat_ids_result_sql)) {
	$news_id = $cat_ids_row['news_id'];
	$temp_array = explode(',', substr($cat_ids_row['news_groups_cat_id'], 1, -1));
	$cat_ids_array = array();
	
	if ($cat_ids_row['latest_news_include_subcat'] > 0) {
		if (array_search(0, $temp_array) === false) {
			foreach ($temp_array as $cat_id) {
				$cat_ids_array[$cat_id] = 1;
				tep_temp_get_categories_id($cat_id, $cat_ids_array);
			}
			if (count($cat_ids_array))	$cat_ids_array = array_keys($cat_ids_array);
			$diff_array = array_diff($top_inc_sub_cats_id, $cat_ids_array);
			
			if (in_array(0, $diff_array) && count($diff_array) == 1) {
				$cat_ids_array = array(-999);
			}
			unset($t_array);
		} else {
			$cat_ids_array = array(0,-999);
		}
	}else{
		$cat_ids_array = $temp_array;
	}
	
	if (count($cat_ids_array)) {
		$insert_sql = "	INSERT INTO ".TABLE_LATEST_NEWS_CATEGORIES." (news_id, categories_id)
							VALUES (".$news_id.",".tep_db_prepare_input(implode('),('.$news_id.',', $cat_ids_array)).")";
      	tep_db_query($insert_sql);
	}
	unset($news_id, $temp_array, $cat_ids_array);
}
unset($top_inc_sub_cats_id);
?>