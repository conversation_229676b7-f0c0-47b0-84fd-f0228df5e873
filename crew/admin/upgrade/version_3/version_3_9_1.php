<?
/*
	$Id: version_3_9_1.php,v 1.1 2010/04/05 04:59:56 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

$existing_custom_products_type_fields = get_table_fields('custom_products_type');

// Insert new field into custom_products_type table
$add_new_field = array();

$add_new_field['custom_products_type'] = array (	array (	"field_name" => "custom_products_admin_group_id",
															"field_attr" => " text NOT NULL ",
															"add_after" => "custom_products_deduct_stock_email"
															)
												);
												
add_field($add_new_field);
// End of insert new field into custom_products_type table

if (!in_array('custom_products_admin_group_id', $existing_custom_products_type_fields)) {
	tep_db_query("UPDATE custom_products_type SET custom_products_admin_group_id = '1,31,45,60' WHERE custom_products_type_id = '0'");
	tep_db_query("UPDATE custom_products_type SET custom_products_admin_group_id = '1,51,60' WHERE custom_products_type_id = '2';");
	tep_db_query("UPDATE custom_products_type SET custom_products_admin_group_id = '1,31,45,64,60' WHERE custom_products_type_id = '1';");
	tep_db_query("UPDATE custom_products_type SET custom_products_admin_group_id = '1,60' WHERE custom_products_type_id = '3';");
}
?>