<?
/*
	$Id: version_3_9_10.php,v 1.1 2010/05/11 07:42:42 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// We<PERSON>
// Delete news_groups_cat_id and latest_news_include_subcat fields from latest_news table
$delete_field = array();

$delete_field['latest_news'] = array  ( array( "field_name" => "news_groups_cat_id"),
										array( "field_name" => "latest_news_include_subcat")
									 );

delete_field($delete_field);
// End of delete news_groups_cat_id and latest_news_include_subcat fields from latest_news table

// Add new country - Guernsey
$guernsey_countries_id_select_sql = "	SELECT countries_id 
										FROM " . TABLE_COUNTRIES . " 
										WHERE countries_iso_code_2 = 'GG'";
$guernsey_countries_id_result_sql = tep_db_query($guernsey_countries_id_select_sql);
if (!tep_db_num_rows($guernsey_countries_id_result_sql)) {
	$guernsey_data_array = array(	'countries_name' => 'Guernsey',
									'countries_iso_code_2' => 'GG',
									'countries_iso_code_3' => 'GGY',
									'countries_international_dialing_code' => 44,
									'address_format_id' => 1,
									'maxmind_support' => 1,
									'aft_risk_type' => 'HIGH',
									'countries_display' => 1
									);
	tep_db_perform(TABLE_COUNTRIES, $guernsey_data_array);
	
	$guernsey_id = tep_db_insert_id();
	
	$guernsey_zone1_data_array = array(	'zone_country_id' => $guernsey_id,
										'zone_code' => 'CA',
										'zone_name' => 'Castel'
										);
	
	$guernsey_zone2_data_array = array(	'zone_country_id' => $guernsey_id,
										'zone_code' => 'FO',
										'zone_name' => 'Forest'
										);
	
	$guernsey_zone3_data_array = array(	'zone_country_id' => $guernsey_id,
										'zone_code' => 'AN',
										'zone_name' => 'St Andrew'
										);
	
	$guernsey_zone4_data_array = array(	'zone_country_id' => $guernsey_id,
										'zone_code' => 'MA',
										'zone_name' => 'St Martin'
										);
	
	$guernsey_zone5_data_array = array(	'zone_country_id' => $guernsey_id,
										'zone_code' => 'PP',
										'zone_name' => 'St Peter Port'
										);
	
	$guernsey_zone6_data_array = array(	'zone_country_id' => $guernsey_id,
										'zone_code' => 'PB',
										'zone_name' => 'St Pierre Du Bois'
										);
	
	$guernsey_zone7_data_array = array(	'zone_country_id' => $guernsey_id,
										'zone_code' => 'SM',
										'zone_name' => 'St Sampson'
										);
	
	$guernsey_zone8_data_array = array(	'zone_country_id' => $guernsey_id,
										'zone_code' => 'SV',
										'zone_name' => 'St Saviour'
										);
	
	$guernsey_zone9_data_array = array(	'zone_country_id' => $guernsey_id,
										'zone_code' => 'TV',
										'zone_name' => 'Torteval'
										);
	
	$guernsey_zone10_data_array = array('zone_country_id' => $guernsey_id,
										'zone_code' => 'VA',
										'zone_name' => 'Vale'
										);
	
	$guernsey_zone11_data_array = array('zone_country_id' => $guernsey_id,
										'zone_code' => 'AL',
										'zone_name' => 'Alderney'
										);
	
	$guernsey_zone12_data_array = array('zone_country_id' => $guernsey_id,
										'zone_code' => 'BQ',
										'zone_name' => 'Brecqhou'
										);
	
	$guernsey_zone13_data_array = array('zone_country_id' => $guernsey_id,
										'zone_code' => 'HM',
										'zone_name' => 'Herm'
										);
	
	$guernsey_zone14_data_array = array('zone_country_id' => $guernsey_id,
										'zone_code' => 'JT',
										'zone_name' => 'Jethou'
										);
	
	$guernsey_zone15_data_array = array('zone_country_id' => $guernsey_id,
										'zone_code' => 'LH',
										'zone_name' => 'Lihou'
										);
	
	$guernsey_zone16_data_array = array('zone_country_id' => $guernsey_id,
										'zone_code' => 'SK',
										'zone_name' => 'Sark'
										);
	
	tep_db_perform(TABLE_ZONES, $guernsey_zone1_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone2_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone3_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone4_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone5_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone6_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone7_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone8_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone9_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone10_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone11_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone12_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone13_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone14_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone15_data_array);
	tep_db_perform(TABLE_ZONES, $guernsey_zone16_data_array);
}
// End of add new country - Guernsey

// Remove useless configuration key(s)
$conf_delete_sql = array();

$conf_delete_sql['BUYBACK_BO_IP_MATCH_CO_IP_TAG_ID'] = array(	"unique" => "1" );

delete_records(TABLE_CONFIGURATION, "configuration_key", $conf_delete_sql, $DBTables);
// End of remove useless configuration key(s)
?>