<?
/*
	$Id: version_3_9_13.php,v 1.1 2010/05/28 08:39:12 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON><PERSON>
// Insert new records into aft_functions table (Is Payment Info Verified)
$aft_functions_insert_sql = array();

$aft_functions_insert_sql["is_order_payment_info_verified"] = array("insert" => " ('is_order_payment_info_verified', 'Is Payment Info Verified?', 'Is Payment Info Verified? Return ''true'' or ''false''.', '0', '0', '')" );

insert_new_records('aft_functions', "aft_functions_name", $aft_functions_insert_sql, $DBTables, "(`aft_functions_name`, `aft_functions_display_name`, `aft_functions_description`, `aft_functions_action`, `line_determine`, `aft_functions_setting`)");
// End of insert new records into aft_functions table (Is Payment Info Verified)
?>