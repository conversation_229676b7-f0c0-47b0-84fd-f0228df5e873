<?
/*
	$Id: version_3_9_14.php,v 1.1 2010/05/28 10:54:43 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON><PERSON>
// Insert new records into admin_files_actions table (for permission on checking global collect payment status all the time)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_CHECK_GLOBAL_COLLECT_PAYMENT"] = array("insert" => " ('ORDER_CHECK_GLOBAL_COLLECT_PAYMENT', 'Check Global Collect Payment Status in all status', ".$row_sql["admin_files_id"].", '1', '33')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on checking global collect payment status all the time)
?>