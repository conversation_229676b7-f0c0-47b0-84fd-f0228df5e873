<?
/*
	$Id: version_3_9_15.php,v 1.1 2010/05/31 06:03:43 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON>
// Define global_collect_orders_id as index key in global_collect_status_history table
add_index_key ('global_collect_status_history', 'index_orders_id', 'index', 'global_collect_orders_id', $DBTables);
// End of define global_collect_orders_id as index key in global_collect_status_history table

// Define global_collect_cc_last_4_digit and global_collect_cc_expiry_date as index key in global_collect table
add_index_key ('global_collect', 'index_cc_number', 'index', 'global_collect_cc_last_4_digit, global_collect_cc_expiry_date', $DBTables);
// End of define global_collect_cc_last_4_digit and global_collect_cc_expiry_date as index key in global_collect table
?>