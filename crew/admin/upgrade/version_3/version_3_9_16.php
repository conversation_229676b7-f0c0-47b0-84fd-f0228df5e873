<?
/*
	$Id: version_3_9_16.php,v 1.1 2010/06/01 11:57:15 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Wei <PERSON>

include_once(DIR_WS_CLASSES . 'currencies.php');

ini_set("memory_limit", "80M");
tep_set_time_limit(0);

$currencies = new currencies();

$user_id_select_sql = "	SELECT c.customers_id AS user_id, c.customers_login_sites, sab.store_account_balance_currency, sab.store_account_balance_amount, sab.store_account_reserve_amount 
						FROM " . TABLE_CUSTOMERS . " AS c 
						LEFT JOIN " . TABLE_STORE_ACCOUNT_BALANCE . " AS sab 
							ON (c.customers_id = sab.user_id) 
						WHERE sab.user_role = 'customers' 
							AND sab.store_account_balance_currency <> 'USD'
							AND FIND_IN_SET(1, c.customers_login_sites)";

$user_id_result_sql = tep_db_query($user_id_select_sql);
while ($user_id_row = tep_db_fetch_array($user_id_result_sql)) {
	echo '<br>';
	$amt_select_sql = "	SELECT user_id, store_account_balance_amount, store_account_reserve_amount 
						FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
						WHERE user_role = 'customers' 
							AND store_account_balance_currency = 'USD'
							AND user_id = '" . (int)$user_id_row['user_id'] . "'";
	$amt_result_sql = tep_db_query($amt_select_sql);
	if ($amt_row = tep_db_fetch_array($amt_result_sql)) {
		;
	} else {
		$data_sql_array = array('user_id' => $user_id_row['user_id'],
								'user_role' => 'customers',
								'store_account_balance_currency' => 'USD',
								'store_account_balance_amount' => '0.0000',
								'store_account_reserve_amount' => '0.0000',
								'store_account_last_modified' => 'now()'
								);
		tep_db_perform(TABLE_STORE_ACCOUNT_BALANCE, $data_sql_array);
		echo 'Create new USD account for ' . $user_id_row['user_id'] . '<br>';
	}
	
	$other_curencies_select_sql = "	SELECT store_account_balance_currency, store_account_balance_amount, store_account_reserve_amount 
									FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
									WHERE user_id = '" . (int)$user_id_row['user_id'] . "' 
										AND user_role = 'customers' 
										AND store_account_balance_currency <> 'USD'";
	$other_curencies_result_sql = tep_db_query($other_curencies_select_sql);
	while ($other_curencies_row = tep_db_fetch_array($other_curencies_result_sql)) {
		if ($other_curencies_row['store_account_balance_amount'] == '0.0000' && $other_curencies_row['store_account_reserve_amount'] == '0.0000') {
			$delete_other_currencies_sql = "	DELETE FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
												WHERE user_id = '" . (int)$user_id_row['user_id'] . "' 
													AND user_role = 'customers' 
													AND store_account_balance_currency = '" . tep_db_input($other_curencies_row['store_account_balance_currency']) . "'";
			tep_db_query($delete_other_currencies_sql);
			echo $delete_other_currencies_sql . '<br>';
			
			unset($delete_other_currencies_sql);
		} else {
			$sql_update_array = array();
			$update_sql_str = '';
			
			if ($other_curencies_row['store_account_reserve_amount'] > 0) {
				$sql_update_array[] = 'store_account_reserve_amount = store_account_reserve_amount + ' . tep_db_input($currencies->advance_currency_conversion($other_curencies_row['store_account_reserve_amount'], $other_curencies_row['store_account_balance_currency'], 'USD', false, 'sell'));
			}
			
			if ($other_curencies_row['store_account_balance_amount'] > 0) {
				$sql_update_array[] = 'store_account_balance_amount = store_account_balance_amount + ' . tep_db_input($currencies->advance_currency_conversion($other_curencies_row['store_account_balance_amount'], $other_curencies_row['store_account_balance_currency'], 'USD', false, 'sell'));
			}
			
			if (count($sql_update_array)) {
		    	$sql_update_array[] = ' store_account_last_modified = now() ';
				
		    	$update_sql_str = " SET " . implode(', ', $sql_update_array);
		    	
		    	$sc_update_sql = "	UPDATE " . TABLE_STORE_ACCOUNT_BALANCE . 
		    						$update_sql_str . " 
									WHERE user_id = '" . (int)$user_id_row['user_id'] . "' 
										AND user_role = 'customers' 
										AND store_account_balance_currency = 'USD'";
				tep_db_query($sc_update_sql);
				
				echo $sc_update_sql . '<br>';
				
				$delete_currency_sql = "	DELETE FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
											WHERE user_id = '" . (int)$user_id_row['user_id'] . "' 
											AND user_role = 'customers' 
											AND store_account_balance_currency = '" . $other_curencies_row['store_account_balance_currency'] . "'";
				tep_db_query($delete_currency_sql);
				echo $delete_currency_sql . '<br>';
				
				unset($delete_currency_sql);
				
				$after_bal_select_sql = "	SELECT store_account_balance_amount  
											FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
											WHERE user_role = 'customers' 
												AND store_account_balance_currency = 'USD'
												AND user_id = '" . (int)$user_id_row['user_id'] . "'";
				$after_bal_result_sql = tep_db_query($after_bal_select_sql);
				$after_bal_row = tep_db_fetch_array($after_bal_result_sql);
				
				$deduct_store_account_history_data_sql = array(	'user_id' => $user_id_row['user_id'],
																'user_role' => 'customers',
																'store_account_history_date' => 'now()',
																'store_account_history_currency' => $other_curencies_row['store_account_balance_currency'],
																'store_account_history_debit_amount' => $other_curencies_row['store_account_balance_amount'],
																'store_account_history_after_balance' => '0.0000',
																'store_account_history_activity_title' => 'Manual Deduction',
																'store_account_history_activity_desc' => 'Convert to USD',
																'store_account_history_activity_desc_show' => 0,
																'store_account_history_added_by' => 'system',
																'store_account_history_added_by_role' => 'admin'
																);
				tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $deduct_store_account_history_data_sql);
				
				$store_account_history_data_sql = array('user_id' => $user_id_row['user_id'],
														'user_role' => 'customers',
														'store_account_history_date' => 'now()',
														'store_account_history_currency' => 'USD',
														'store_account_history_credit_amount' => $currencies->advance_currency_conversion($other_curencies_row['store_account_balance_amount'], $other_curencies_row['store_account_balance_currency'], 'USD', false, 'sell'),
														'store_account_history_after_balance' => $after_bal_row['store_account_balance_amount'],
														'store_account_history_activity_title' => 'Manual Addition',
														'store_account_history_activity_desc' => 'Convert from ' . $other_curencies_row['store_account_balance_currency'],
														'store_account_history_activity_desc_show' => 0,
														'store_account_history_added_by' => 'system',
														'store_account_history_added_by_role' => 'admin'
														);
				tep_db_perform(TABLE_STORE_ACCOUNT_HISTORY, $store_account_history_data_sql);
				
		    }
		}
	}
}
?>