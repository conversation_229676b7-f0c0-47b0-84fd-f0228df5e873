<?
/*
	$Id: version_3_9_2.php,v 1.2 2010/04/06 11:08:25 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into configuration table (for Verification Document Notify Email)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Store Information'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["STORE_AFT_DOCUMENT_NOTIFICATION_EMAIL"] = array("insert" => " ('Customer Upload Verification Document Notify Email', 'STORE_AFT_DOCUMENT_NOTIFICATION_EMAIL', '', 'Email address to which the email will be send to whenever customer upload verification document.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 17, NULL, now(), NULL, '')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Verification Document Notify Email)

$main_cat_select_sql = "SELECT c.categories_id
                        FROM " . TABLE_CATEGORIES . " AS c
                        WHERE c.categories_buyback_main_cat = 1";
$main_cat_result_sql = tep_db_query($main_cat_select_sql);
while ($main_cat_row = tep_db_fetch_array($main_cat_result_sql)) {
	$confirmation_duration_select_sql = "	SELECT buyback_setting_value 
											FROM " . TABLE_BUYBACK_SETTING . " 
											WHERE buyback_setting_table_name = 'buyback_categories' 
												AND buyback_setting_reference_id = '".(int)$main_cat_row['categories_id']."' 
												AND buyback_setting_key = 'customer_confirmation_duration' 
											LIMIT 1";
	$confirmation_duration_result_sql =	tep_db_query($confirmation_duration_select_sql);
	if ($confirmation_duration_row = tep_db_fetch_array($confirmation_duration_result_sql)) {
		$update_buyback_setting_value_array = array('buyback_setting_value' => '4320');
		tep_db_perform(TABLE_BUYBACK_SETTING, $update_buyback_setting_value_array, 'update', "buyback_setting_reference_id = '" . (int)$main_cat_row['categories_id'] . "' AND buyback_setting_table_name = 'buyback_categories' AND buyback_setting_key = 'customer_confirmation_duration'");
	} else {
		$insert_buyback_setting_value_array = array('buyback_setting_reference_id' => $main_cat_row['categories_id'],
													'buyback_setting_table_name' => 'buyback_categories',
													'buyback_setting_key' => 'customer_confirmation_duration',
													'buyback_setting_value' => '4320');
		tep_db_perform(TABLE_BUYBACK_SETTING, $insert_buyback_setting_value_array);
	}
}

// Define expiry_hour as index key in orders_products_eta table
add_index_key ('orders_products_eta', 'index_expiry_hour', 'index', 'expiry_hour', $DBTables);
// End of define expiry_hour as index key in orders_products_eta table
?>