<?
/*
	$Id: version_3_9_3.php,v 1.2 2010/04/14 03:27:56 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Henry
// Insert new records into categories_configuration table (for Product Price Editing notification)
$conf_insert_sql = array();

$conf_insert_sql["CATALOG_PWL_PRICE_EMAIL"] = array("insert" => " (0, 'PWL Product Price Editing Email Address', 'CATALOG_PWL_PRICE_EMAIL', '', 'Email address to which the email will be send to whenever someone change PWL Product price.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 10, NULL, now(), NULL, NULL)" );
$conf_insert_sql["CATALOG_CDKEY_PRICE_EMAIL"] = array("insert" => " (0, 'CD Key Product Price Editing Email Address', 'CATALOG_CDKEY_PRICE_EMAIL', '', 'Email address to which the email will be send to whenever someone change CD Key Product price.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 10, NULL, now(), NULL, NULL)" );
$conf_insert_sql["CATALOG_CURRENCY_PRICE_EMAIL"] = array("insert" => " (0, 'Currency Product Price Editing Email Address', 'CATALOG_CURRENCY_PRICE_EMAIL', '', 'Email address to which the email will be send to whenever someone change Currency Product price.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 10, NULL, now(), NULL, NULL)" );
$conf_insert_sql["CATALOG_SC_PRICE_EMAIL"] = array("insert" => " (0, 'Store Credit Product Price Editing Email Address', 'CATALOG_SC_PRICE_EMAIL', '', 'Email address to which the email will be send to whenever someone change Store Credit Product price.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 10, NULL, now(), NULL, NULL)" );

$conf_insert_sql["CATALOG_CDKEY_UPLOAD_EMAIL"] = array("insert" => " (0, 'CD Key Product Uploading Email Address', 'CATALOG_CDKEY_UPLOAD_EMAIL', '', 'Email address to which the email will be send to whenever someone upload CD Key product.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 11, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into categories_configuration table (for Product Price Editing notification)

// Insert new fields into custom_products_type table
$add_new_field = array();

$add_new_field['custom_products_type'] = array (array (	"field_name" => "custom_product_price_email",
														"field_attr" => " varchar(64) NOT NULL default '' ",
														"add_after" => "custom_products_deduct_stock_email"
														),
												array (	"field_name" => "custom_products_upload_email",
														"field_attr" => " varchar(64) NOT NULL default '' ",
														"add_after" => "custom_product_price_email"
														)
											);
												
add_field($add_new_field);
// End of insert new fields into custom_products_type table

// Update records in custom_products_type table
$cp_update_sql = array();

$cp_update_sql["0"] = array("update" => " custom_product_price_email='CATALOG_CURRENCY_PRICE_EMAIL' " );
$cp_update_sql["1"] = array("update" => " custom_product_price_email='CATALOG_PWL_PRICE_EMAIL' " );
$cp_update_sql["2"] = array("update" => " custom_product_price_email='CATALOG_CDKEY_PRICE_EMAIL' " );
$cp_update_sql["3"] = array("update" => " custom_product_price_email='CATALOG_SC_PRICE_EMAIL' " );

update_records("custom_products_type", "custom_products_type_id", $cp_update_sql, $DBTables);
// End of update records in currencies table (define which currency is used for SELL/BUY)

// Update records in custom_products_type table
$cp_update_sql = array();

$cp_update_sql["2"] = array("update" => " custom_products_upload_email='CATALOG_CDKEY_UPLOAD_EMAIL' " );

update_records("custom_products_type", "custom_products_type_id", $cp_update_sql, $DBTables);
// End of update records in currencies table (define which currency is used for SELL/BUY)
?>