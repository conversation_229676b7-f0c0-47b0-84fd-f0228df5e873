<?
/*
	$Id: version_3_9_4.php,v 1.2 2010/04/15 04:07:31 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Kee Peng
// Insert new records into configuration table (for CO and BO Tagging)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_ITEM_NOT_RECEIVED_TAG_ID"] = array("insert" => " ('Item not Received Tag IDs', 'BUYBACK_ITEM_NOT_RECEIVED_TAG_ID', '430,427', 'The ID of the order tag used to indicate Customer complaint not receive the delivered item. Entered in \"CO Tag ID, BO Tag ID\" format.', ".$row_sql["configuration_group_id"].", 130, NULL, now(), NULL, '')" );
	$conf_insert_sql["BUYBACK_ITEM_DELIVERED_OTHER_METHOD_TAG_ID"] = array("insert" => " ('Item Delivered via Other Delivery Method', 'BUYBACK_ITEM_DELIVERED_OTHER_METHOD_TAG_ID', '429,428', 'The ID of the order tag used to indicate Customer complaint item received but delivered via other methods. Entered in \"CO Tag ID, BO Tag ID\" format.', ".$row_sql["configuration_group_id"].", 135, NULL, now(), NULL, '')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for CO and BO Tagging)

// Ching Yen
// Create categories_game_details table
$add_new_tables = array();

$add_new_tables["categories_game_details"] = array (	"structure" => "CREATE TABLE `categories_game_details` (
																		  `categories_id` int(11) NOT NULL DEFAULT '0',
																		  `language_id` int(11) NOT NULL DEFAULT '0',
																		  `game_publisher` text,
																		  `game_developer` text,
																		  `game_platform` text,
																		  `game_category` text,
																		  `game_model` text,
																		  `game_language` text,
																		  `game_status` text,
																		  `game_interface` text,
																		  `game_client_type` text,
																		  `game_time_keeping_system` text,
																		  `game_year` int(4) DEFAULT NULL,
																		  `game_expansions` text,
																		  `game_launching_date` date DEFAULT NULL,
																		  `game_website_name` text,
																		  `game_website_url` text,
																		  `game_esrb` text,
																		  `game_keyword` text,
																		  `game_download_client_url` text,
																		  `game_signup_account_url` text,
																		  `game_spec_minimum` text,
																		  `game_spec_recommended` text,
																		  PRIMARY KEY (`categories_id`,`language_id`)
																		) TYPE=MyISAM;" ,
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create categories_game_details table
?>