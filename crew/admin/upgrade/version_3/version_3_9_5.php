<?
/*
	$Id: version_3_9_5.php,v 1.1 2010/04/20 10:14:54 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Define categories_id as index key in latest_news_categories table
add_index_key ('latest_news_categories', 'index_categories_id', 'index', '`categories_id`', $DBTables);
// End of define categories_id as index key in latest_news_categories table

// Define products_cat_id_path  as index key in products table
add_index_key ('products', 'index_cat_id_path', 'index', '`products_cat_id_path`(16)', $DBTables);
// End of define products_cat_id_path  as index key in products table

// Delete records in admin_files table
$admin_file_delete_sql = array();

$admin_file_delete_sql["coupon_restrict.php"] = array(	"unique" => "1", "extra_where" => " admin_files_is_boxes=0 " ); // extra where concatenated with default where checking string using 'AND' operator

delete_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_file_delete_sql, $DBTables);
// End of delete records in admin_files table

// Wilson
$existing_coupons_fields = get_table_fields('coupons');

// Create Discount Code Generation tables
$add_new_tables = array();

$add_new_tables["coupons_generation"] = array (	"structure" => "CREATE TABLE `coupons_generation` (
																  `coupon_generation_id` int(11) NOT NULL auto_increment,
																  `coupon_type` char(1) NOT NULL default 'F',
																  `coupon_code_prefix` varchar(5) NOT NULL default 'OGM',
																  `coupon_code_suffix` varchar(5) NOT NULL default '',
																  `coupon_amount` decimal(8,4) NOT NULL default '0.0000',
																  `coupon_minimum_order` decimal(8,4) NOT NULL default '0.0000',
																  `coupon_start_date` datetime NOT NULL default '0000-00-00 00:00:00',
																  `coupon_expire_date` datetime NOT NULL default '0000-00-00 00:00:00',
																  `coupon_number` int(5) NOT NULL default '1',
																  `uses_per_coupon` int(5) NOT NULL default '1',
																  `uses_per_coupon_unlimited` char(1) NOT NULL default 'N',
																  `uses_per_user` int(5) NOT NULL default '1',
																  `uses_per_user_unlimited` char(1) NOT NULL default 'N',
																  `restrict_to_products` text,
																  `restrict_to_categories` text,
																  `restrict_to_customers` text,
																  `coupon_generation_status` char(1) NOT NULL default 'P',
																  `requester_id` INT( 11 ) NOT NULL DEFAULT '0',
																  `date_created` datetime NOT NULL default '0000-00-00 00:00:00',
																  `date_modified` datetime NOT NULL default '0000-00-00 00:00:00',
																  PRIMARY KEY  (`coupon_generation_id`),
																  KEY `index_coupon_generation_status` (`coupon_generation_status`)
																) TYPE=MyISAM ;" ,
												"data" => "INSERT INTO `coupons_generation` (`coupon_type` , `coupon_code_prefix` , `coupon_code_suffix` , `coupon_amount` , `coupon_minimum_order`  , `coupon_start_date` , `coupon_expire_date` , `coupon_number` , `uses_per_coupon` , `uses_per_coupon_unlimited`  , `uses_per_user` , `uses_per_user_unlimited`  , `restrict_to_products`  , `restrict_to_categories`  , `restrict_to_customers`  , `coupon_generation_status`  , `requester_id`  , `date_created` , `date_modified` )
															VALUES ('F', '', '', '0.0000', '0.0000', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '1', '1', 'Y', '1', 'Y', NULL , NULL , NULL , 'Y', '1', NOW( ) , NOW( ));"
											);

$add_new_tables["coupons_generation_description"] = array (	"structure" => "CREATE TABLE `coupons_generation_description` (
																			  `coupon_generation_id` int(11) NOT NULL default '0',
																			  `language_id` int(11) NOT NULL default '0',
																			  `coupon_generation_name` varchar(128) NOT NULL default '',
																			  `coupon_generation_description` text,
																			  PRIMARY KEY  (`coupon_generation_id`, `language_id`)
																			) TYPE=MyISAM;" ,
															"data" => "INSERT INTO `coupons_generation_description` ( `coupon_generation_id` , `language_id`  , `coupon_generation_name` , `coupon_generation_description`)
																		VALUES (1, 1, 'Before Code Automation 2010-04-21', 'Before Code Automation 2010-04-21');"
														);

$add_new_tables["coupons_generation_status_history"] = array (	"structure" => "CREATE TABLE `coupons_generation_status_history` (
																				  `coupons_generation_status_history_id` int(11) NOT NULL auto_increment,
																				  `coupons_generation_id` int(11) NOT NULL default '0',
																				  `coupons_generation_status` char(1) NOT NULL default '',
																				  `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																				  `comments` text,
																				  `changed_by` varchar(128) NOT NULL default '',
																				  PRIMARY KEY  (`coupons_generation_status_history_id`),
																				  KEY `index_coupons_generation_id` (`coupons_generation_id`),
																				  KEY `index_date_added` (`date_added`)
																				) TYPE=MyISAM;" ,
																"data" => ""
															);

$add_new_tables["coupons_status_history"] = array (	"structure" => "CREATE TABLE `coupons_status_history` (
																	  `coupons_status_history_id` int(11) NOT NULL auto_increment,
																	  `coupon_id` int(11) NOT NULL default '0',
																	  `coupon_active` char(1) NOT NULL default '',
																	  `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `comments` text,
																	  `changed_by` varchar(128) NOT NULL default '',
																	  PRIMARY KEY  (`coupons_status_history_id`),
																	  KEY `index_coupon_id` (`coupon_id`),
																	  KEY `index_date_added` (`date_added`)
																	) TYPE=MyISAM;" ,
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create Discount Code Generation tables


// Insert new field into coupons table
$add_new_field = array();

$add_new_field['coupons'] = array (	array (	"field_name" => "coupon_generation_id",
											"field_attr" => " int(11) NOT NULL default '0' ",
											"add_after" => "coupon_id"
											),
									array (	"field_name" => "uses_per_coupon_unlimited",
											"field_attr" => " char(1) NOT NULL default 'N' ",
											"add_after" => "uses_per_coupon"
											),
									array (	"field_name" => "uses_per_user_unlimited",
											"field_attr" => " char(1) NOT NULL default 'N' ",
											"add_after" => "uses_per_user"
											)
								);
												
add_field($add_new_field);
// End of insert new field into coupons table

// Define coupon_generation_id as index key in coupons table
add_index_key ('coupons', 'index_coupon_generation_id', 'index', '`coupon_generation_id`', $DBTables);
// End of define coupon_generation_id as index key in coupons table

// Define coupon_start_date as index key in coupons table
add_index_key ('coupons', 'index_start_date', 'index', '`coupon_start_date`', $DBTables);
// End of define coupon_start_date as index key in coupons table

// Define coupon_expire_date as index key in coupons table
add_index_key ('coupons', 'index_expire_date', 'index', '`coupon_expire_date`', $DBTables);
// End of define coupon_expire_date as index key in coupons table

// Insert new records into admin_files_actions table (for permission on Coupon Generation Process)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='coupon_admin.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["COUPONS_GENERATION_REQUEST"] = array("insert" => " ('COUPONS_GENERATION_REQUEST', 'Request New Discount Coupon Generation', ".$row_sql["admin_files_id"].", '1', 10)" );
	$admin_files_actions_insert_sql["COUPONS_GENERATION_EDIT"] = array("insert" => " ('COUPONS_GENERATION_EDIT', 'Edit Pending Discount Coupon Generation Request', ".$row_sql["admin_files_id"].", '1', 20)" );
	$admin_files_actions_insert_sql["COUPONS_GENERATION_APPROVE"] = array("insert" => " ('COUPONS_GENERATION_APPROVE', 'Approve/Cancel Discount Coupon Request', ".$row_sql["admin_files_id"].", '1', 30)" );
	$admin_files_actions_insert_sql["COUPONS_VIEW_DISCOUNT_COUPON"] = array("insert" => " ('COUPONS_VIEW_DISCOUNT_COUPON', 'View Discount Coupon Code', ".$row_sql["admin_files_id"].", '1', 40)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Coupon Generation Process)

// Insert cron track record for cron_coupons_status.php
$cron_track_rec_select_sql = "	SELECT cron_process_track_filename 
								FROM cron_process_track 
								WHERE cron_process_track_filename = 'cron_coupons_status.php'";
$cron_track_rec_result_sql = tep_db_query($cron_track_rec_select_sql);

if (!tep_db_num_rows($cron_track_rec_result_sql)) {
	$cron_track_rec_data_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_coupons_status.php'
				                       );
	tep_db_perform('cron_process_track', $cron_track_rec_data_array);
}
// End of insert cron track record for cron_coupons_status.php

if (!in_array('coupon_generation_id', $existing_coupons_fields)) {
	// Update records in coupons table (Set default Coupon Generation ID)
	$coupon_update_sql = array();
	
	$coupon_update_sql['coupons'] = array(	array(	"field_name" => "coupon_generation_id",
													"update" => " coupon_generation_id=1 ",
													"where_str" => " 1 "
													)
										 );
	
	advance_update_records($coupon_update_sql, $DBTables);
	// End of update records in coupons table (Set default Coupon Generation ID)
}
?>