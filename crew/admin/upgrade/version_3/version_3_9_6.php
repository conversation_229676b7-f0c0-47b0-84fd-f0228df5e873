<?
/*
	$Id: version_3_9_6.php,v 1.2 2010/04/27 05:37:28 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Kee <PERSON>g
// Insert new records into configuration table (for Matching BO IP and CO IP Tagging)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Buyback'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["BUYBACK_BO_IP_MATCH_CO_IP_TAG_ID"] = array("insert" => " ('Buyback Order IP match Customer Order IP', 'BUYBACK_BO_IP_MATCH_CO_IP_TAG_ID', '434,433', 'The ID of the order tag used to indicate the BO IP matched with CO IP. Entered in \"CO Tag ID, BO Tag ID\" format.', ".$row_sql["configuration_group_id"].", 140, NULL, now(), NULL, '')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Matching BO IP and CO IP Tagging)

// Henry
// Insert new records into admin_files_actions table (for permission on Edit Product Status)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='categories.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CATALOG_EDIT_PRODUCT_STATUS"] = array("insert" => " ('CATALOG_EDIT_PRODUCT_STATUS', 'Edit Product Status', ".$row_sql["admin_files_id"].", '1', 44)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Edit Product Status)

// Ching Yen
// Insert new records into admin_files_actions table (for permission on Viewing Customer Email)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_VIEW_EMAIL_LISTING"] = array("insert" => " ('CUSTOMER_VIEW_EMAIL_LISTING', 'View Email in Listing Page', ".$row_sql["admin_files_id"].", '1', 18)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on Viewing Customer Email)
?>