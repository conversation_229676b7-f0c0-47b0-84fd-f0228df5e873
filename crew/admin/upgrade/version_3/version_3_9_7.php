<?
/*
	$Id: version_3_9_7.php,v 1.2 2010/04/27 12:21:23 weichen Exp $
	
  	Developer: <PERSON> (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// <PERSON><PERSON>
// Create global collect tables
$add_new_tables = array();

$add_new_tables["global_collect"] = array (	"structure" => "CREATE TABLE `global_collect` (
															  `global_collect_orders_id` int(11) NOT NULL,
															  `global_collect_currency_code` varchar(3) NOT NULL,
															  `global_collect_amount` double(13,2) NOT NULL,
															  `global_collect_status_id` int(5) NOT NULL,
															  `global_collect_status_date` varchar(20) NOT NULL,
															  `global_collect_payment_reference` varchar(20) NOT NULL,
															  `global_collect_additional_reference` varchar(20) NOT NULL,
															  `global_collect_effortid` varchar(8) NOT NULL,
															  `global_collect_attemptid` varchar(8) NOT NULL,
															  `global_collect_paymentproductid` varchar(8) NOT NULL,
															  `global_collect_paymentmethodid` varchar(8) NOT NULL,
															  `global_collect_receiveddate` varchar(14) NOT NULL,
															  `global_collect_cvv_result` char(1) NOT NULL,
															  `global_collect_avs_result` char(1) NOT NULL,
															  `global_collect_fraud_result` char(1) NOT NULL,
															  `global_collect_fraud_code` varchar(4) NOT NULL,
															  `global_collect_capture_request` tinyint(4) NOT NULL default '0',
															  `global_collect_request_id` varchar(10) NOT NULL,
															  `global_collect_request_time` varchar(20) NOT NULL,
															  `global_collect_cc_last_4_digit` varchar(4) NOT NULL,
															  `global_collect_cc_expiry_date` varchar(4) NOT NULL,
															  `global_collect_refund_request` tinyint(1) NOT NULL default '0',
															  `global_collect_eci` char(1) NOT NULL,
															  PRIMARY KEY  (`global_collect_orders_id`)
															) TYPE=MyISAM;" ,
											"data" => ""
										);

$add_new_tables["global_collect_status_history"] = array (	"structure" => "CREATE TABLE `global_collect_status_history` (
																			  `global_collect_status_history_id` int(11) NOT NULL auto_increment,
																			  `global_collect_orders_id` int(11) NOT NULL,
																			  `global_collect_date` datetime NOT NULL,
																			  `global_collect_status` int(5) NOT NULL,
																			  `global_collect_description` varchar(255) NOT NULL,
																			  `changed_by` varchar(128) NOT NULL,
																			  PRIMARY KEY  (`global_collect_status_history_id`)
																			) TYPE=MyISAM;" ,
															"data" => ""
														);

add_new_tables ($add_new_tables, $DBTables);
// End of create global collect tables

// Insert new records into admin_files_actions table (for permission on Cancel GC Payment before SET Payment)
/*
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_GLOBAL_COLLECT_CANCEL_PAYMENT"] = array("insert" => " ('ORDER_GLOBAL_COLLECT_CANCEL_PAYMENT', 'Cancel Global Collect Payment', ".$row_sql["admin_files_id"].", '1', 36)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
*/
// End of insert new records into admin_files_actions table (for permission on Cancel GC Payment before SET Payment)
?>