﻿<?php
// Create new table
$add_new_tables = array();

$add_new_tables["game_region_to_categories"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `game_region_to_categories` (
																		  `game_region_id` int(11) unsigned NOT NULL,
																		  `categories_id` int(11) unsigned NOT NULL,
																		  PRIMARY KEY (`game_region_id`,`categories_id`)
																		) ENGINE=MyISAM;" ,
														"data" => "" );

$add_new_tables["game_language_to_categories"] = array ("structure" => "CREATE TABLE IF NOT EXISTS `game_language_to_categories` (
																		  `game_language_id` int(11) unsigned  NOT NULL,
																		  `categories_id` int(11) unsigned NOT NULL,
																		  PRIMARY KEY (`game_language_id`,`categories_id`)
																		) ENGINE=MyISAM;" ,
														"data" => "" );

$add_new_tables["categories_search"] = array ("structure" => "CREATE TABLE IF NOT EXISTS `categories_search` (
																  `categories_id` int(11) unsigned NOT NULL,
																  `language_id` int(11) unsigned NOT NULL,
																  `search_value` text NOT NULL,
																  PRIMARY KEY (`categories_id`,`language_id`)
																) ENGINE=MyISAM;" ,
											"data" => "" );

add_new_tables ($add_new_tables, $DBTables);
// End of create new table


// Insert new fields into `define_mainpage` table
$add_new_field = array();

$add_new_field['define_mainpage'] = array (	array (	"field_name" => "mainpage_slider_content",
													"field_attr" => "text",
													"add_after" => ""
											),
											array (	"field_name" => "mainpage_best_selling_image",
													"field_attr" => "text",
													"add_after" => ""
											),
											array (	"field_name" => "mainpage_tab_content",
													"field_attr" => "text",
													"add_after" => ""
											)
									);

add_field($add_new_field);
// End of insert new fields into `orders_status_stat` table


// Insert new schedule task
$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_best_selling.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_process_track_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_best_selling.php'
			                       );
	tep_db_perform('cron_process_track', $cron_process_track_array);
}
// End of insert new schedule task
?>