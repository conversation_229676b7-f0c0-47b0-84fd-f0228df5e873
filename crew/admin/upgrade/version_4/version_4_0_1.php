<?
// Insert new fields into `define_mainpage` table
$add_new_field = array();

$add_new_field['define_mainpage'] = array (	array (	"field_name" => "footer_all_payment_image",
													"field_attr" => "text",
													"add_after" => ''
											)
									);
add_field($add_new_field);
// End of insert new fields into `define_mainpage` table

// Create new table
$add_new_tables = array();

$add_new_tables["customers_favorites"] = array (	"structure" => "CREATE TABLE customers_favorites (
																		`customers_id` INT(11) UNSIGNED NOT NULL ,
																		`categories_id` INT(11) UNSIGNED NOT NULL ,
																		PRIMARY KEY ( `customers_id` , `categories_id` )
																	) ENGINE = MYISAM ;" ,
													"data" => "" );

add_new_tables ($add_new_tables, $DBTables);
// End of create new table

// Populate game region and language data
$has_data_checking_select_sql = '	SELECT COUNT(categories_id) AS total_rec
									FROM game_language_to_categories';

$has_data_checking_result_sql = tep_db_query($has_data_checking_select_sql);
$has_data_checking_row = tep_db_fetch_array($has_data_checking_result_sql);

if ($has_data_checking_row['total_rec'] <= 0) {
	$game_language_select_sql = "	SELECT DISTINCT grtl.game_language_id, grltc.categories_id 
	               					FROM game_region_to_language AS grtl, game_region_language_to_categories AS grltc 
	               					WHERE grtl.game_region_to_language_id = grltc.game_region_to_language_id";
	$game_language_result_sql = tep_db_query($game_language_select_sql);
	
	while ($game_language_row = tep_db_fetch_array($game_language_result_sql)) {
		$game_language_insert_sql = "INSERT INTO `game_language_to_categories` 
									(`game_language_id`,`categories_id`)
									VALUES(	'".$game_language_row['game_language_id']."',
											'".$game_language_row['categories_id']."')";
		tep_db_query($game_language_insert_sql);
	}
}

$has_data_checking_select_sql = '	SELECT COUNT(categories_id) AS total_rec
									FROM game_region_to_categories';

$has_data_checking_result_sql = tep_db_query($has_data_checking_select_sql);
$has_data_checking_row = tep_db_fetch_array($has_data_checking_result_sql);

if ($has_data_checking_row['total_rec'] <= 0) {
	$game_region_select_sql = "	SELECT DISTINCT grtl.game_region_id, grltc.categories_id
               					FROM game_region_to_language AS grtl, game_region_language_to_categories AS grltc 
               					WHERE grtl.game_region_to_language_id = grltc.game_region_to_language_id";
	$game_region_result_sql = tep_db_query($game_region_select_sql);
	
	while ($game_region_row = tep_db_fetch_array($game_region_result_sql)) {
		$game_region_insert_sql = "INSERT INTO `game_region_to_categories` 
									(`game_region_id`,`categories_id`)
									VALUES(	'".$game_region_row['game_region_id']."',
										'".$game_region_row['categories_id']."')";
		tep_db_query($game_region_insert_sql);
	}
}
// End of populate game region and language data

// Populate search keyword
$has_data_checking_select_sql = '	SELECT COUNT(categories_id) AS total_rec
									FROM categories_search';

$has_data_checking_result_sql = tep_db_query($has_data_checking_select_sql);
$has_data_checking_row = tep_db_fetch_array($has_data_checking_result_sql);

if ($has_data_checking_row['total_rec'] <= 0) {
	$category_keyword_array = array();
	
	$select_sql = "	SELECT cgd.categories_id, cgd.language_id, cgd.game_keyword, cd.categories_name 
	               	FROM categories_game_details AS cgd, categories_description AS cd 
	               	WHERE cgd.categories_id = cd.categories_id 
	               		AND cgd.language_id = cd.language_id";
	$result_sql = tep_db_query($select_sql);
	
	while ($result_row = tep_db_fetch_array($result_sql)) {
		$search_value = '';
		
		if ($result_row['language_id'] == 1) {
			$search_value = $result_row['categories_name'] . ', ' . $result_row['game_keyword'];
			if (!tep_not_null($result_row['game_keyword']) || $result_row['game_keyword'] == '') {
				$search_value = $result_row['categories_name'];
				
				$word_arr = explode(' ', $result_row['categories_name']);
				$last_word = array_pop($word_arr);
				
				$keyword = '';
				if (count($word_arr) > 0) {
					foreach ($word_arr as $word) {
						$keyword .= strtoupper(substr($word,0,1));
					}
				}
				
				$keyword_update_sql = "	UPDATE categories_game_details
										SET game_keyword = '".tep_db_input($keyword . ' ' . $last_word)."'
										WHERE categories_id = '".$result_row['categories_id']."'
											AND language_id = '".$result_row['language_id']."'";
				tep_db_query($keyword_update_sql);
				
				$search_value .= ', ' . $keyword . ' ' . $last_word;
			}
		} else {
			$search_value = $result_row['game_keyword'];
			
			if (tep_not_null($result_row['categories_name'])) {
				$search_value = $result_row['categories_name'] . ', ' . $result_row['game_keyword'];
			} else {
				$en_select_sql = "	SELECT categories_name
									FROM categories_description
									WHERE categories_id = '".$result_row['categories_id']."' 
										AND language_id = 1";
				$en_result_sql = tep_db_query($en_select_sql);
				if ($en_result_row = tep_db_fetch_array($en_result_sql)) {
					$search_value = $en_result_row['categories_name'] . ', ' . $result_row['game_keyword'];
				}
			}
		}
		
		if ($search_value != '') {
			$cat_search_sql = "INSERT INTO `categories_search` 
								(`categories_id`,`language_id`,`search_value`)
								VALUES(	'".$result_row['categories_id']."',
										'".$result_row['language_id']."',
										'".tep_db_input($search_value)."')";
			tep_db_query($cat_search_sql);
		}
	}
}
// End of populate search keyword
?>