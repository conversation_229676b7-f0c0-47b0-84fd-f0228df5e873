<?
// Update custom_products_type_child_url and custom_products_type_child_name in custom_products_type_child table
$aft_update_sql = array();

$aft_update_sql['custom_products_type_child'] = array(	array(	"field_name" => "custom_products_type_child_name",
																"update" => " custom_products_type_child_name = 'Game Portal Card', custom_products_type_child_url='game_portal_card', sort_order=1100 ",
																"where_str" => " custom_products_type_child_id=8"
																),
														array(	"field_name" => "custom_products_type_child_name",
																"update" => " custom_products_type_child_name = 'Gift Card', custom_products_type_child_url='gift_card', sort_order=1200 ",
																"where_str" => " custom_products_type_child_id=9"
																)
													 );

advance_update_records($aft_update_sql, $DBTables);
// End of update custom_products_type_child_url and custom_products_type_child_name in custom_products_type_child table

// Update custom_products_type_child_name in custom_products_type_child_lang table
$aft_update_sql = array();

$aft_update_sql['custom_products_type_child_lang'] = array(	array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = 'Game Cards' ",
																	"where_str" => " custom_products_type_child_id=3 AND languages_id=1 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#28216;&#25103;&#28857;&#21345;' ",
																	"where_str" => " custom_products_type_child_id=3 AND languages_id=2 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#36938;&#25138;&#40670;&#21345;' ",
																	"where_str" => " custom_products_type_child_id=3 AND languages_id=3 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = 'Game Portal Cards' ",
																	"where_str" => " custom_products_type_child_id=8 AND languages_id=1 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#19968;&#21345;&#36890;' ",
																	"where_str" => " custom_products_type_child_id=8 AND languages_id=2 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#19968;&#21345;&#36890;' ",
																	"where_str" => " custom_products_type_child_id=8 AND languages_id=3 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = 'Gift Cards' ",
																	"where_str" => " custom_products_type_child_id=9 AND languages_id=1 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#30005;&#23376;&#28040;&#36153;&#21048;' ",
																	"where_str" => " custom_products_type_child_id=9 AND languages_id=2 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#38651;&#23376;&#28040;&#36027;&#21048;' ",
																	"where_str" => " custom_products_type_child_id=9 AND languages_id=3 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = 'In Game Currencies' ",
																	"where_str" => " custom_products_type_child_id=1 AND languages_id=1 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#28216;&#25103;&#36135;&#24065;' ",
																	"where_str" => " custom_products_type_child_id=1 AND languages_id=2 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#36938;&#25138;&#36008;&#24163;' ",
																	"where_str" => " custom_products_type_child_id=1 AND languages_id=3 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = 'Power Leveling Services' ",
																	"where_str" => " custom_products_type_child_id=2 AND languages_id=1 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#28216;&#25103;&#20195;&#32451;&#26381;&#21153;' ",
																	"where_str" => " custom_products_type_child_id=2 AND languages_id=2 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#36938;&#25138;&#20195;&#32244;&#26381;&#21209;' ",
																	"where_str" => " custom_products_type_child_id=2 AND languages_id=3 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = 'High Level Accounts' ",
																	"where_str" => " custom_products_type_child_id=5 AND languages_id=1 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#39640;&#32423;&#24080;&#21495;' ",
																	"where_str" => " custom_products_type_child_id=5 AND languages_id=2 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#39640;&#32026;&#24115;&#34399;' ",
																	"where_str" => " custom_products_type_child_id=5 AND languages_id=3 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = 'Gaming Tools' ",
																	"where_str" => " custom_products_type_child_id=6 AND languages_id=1 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#28216;&#25103;&#24037;&#20855;' ",
																	"where_str" => " custom_products_type_child_id=6 AND languages_id=2 "
																	),
															array(	"field_name" => "custom_products_type_child_name",
																	"update" => " custom_products_type_child_name = '&#36938;&#25138;&#24037;&#20855;' ",
																	"where_str" => " custom_products_type_child_id=6 AND languages_id=3 "
																	)
														 );

advance_update_records($aft_update_sql, $DBTables);
// End of update custom_products_type_child_name in custom_products_type_child_lang table

// Insert new records into custom_products_type_child table (for Console Point and Mobile / IDD reload)
$custom_products_type_child_select_sql = "	SELECT custom_products_type_child_id 	
											FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
											WHERE custom_products_type_child_id = '10'";
$custom_products_type_child_result_sql = tep_db_query($custom_products_type_child_select_sql);
if (!tep_db_num_rows($custom_products_type_child_result_sql)) {
	$custom_products_type_child_data_array = array(	'custom_products_type_id' => '2',
													'custom_products_type_child_url' => 'console_point',
													'custom_products_type_child_name' => 'Console Point',
													'display_status' => '0',
													'sort_order' => 1300
							                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, $custom_products_type_child_data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '10',
							'languages_id' => 1,
							'custom_products_type_child_name' => 'Console Points'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '10',
							'languages_id' => 2,
							'custom_products_type_child_name' => '&#23478;&#29992;&#26426;&#28857;&#25968;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '10',
							'languages_id' => 3,
							'custom_products_type_child_name' => '&#23478;&#29992;&#27231;&#40670;&#25976;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
}


$custom_products_type_child_select_sql = "	SELECT custom_products_type_child_id 	
											FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
											WHERE custom_products_type_child_id = '11'";
$custom_products_type_child_result_sql = tep_db_query($custom_products_type_child_select_sql);
if (!tep_db_num_rows($custom_products_type_child_result_sql)) {
	$custom_products_type_child_data_array = array(	'custom_products_type_id' => '2',
													'custom_products_type_child_url' => 'mobile_idd_reload',
													'custom_products_type_child_name' => 'Mobile / IDD reload',
													'display_status' => '0',
													'sort_order' => 1400
							                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, $custom_products_type_child_data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '11',
							'languages_id' => 1,
							'custom_products_type_child_name' => 'Mobile / IDD Reloads'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '11',
							'languages_id' => 2,
							'custom_products_type_child_name' => '&#25163;&#26426;&#20805;&#20540;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '11',
							'languages_id' => 3,
							'custom_products_type_child_name' => '&#25163;&#27231;&#20805;&#20540;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
}
// End of insert new records into custom_products_type_child table (for Console Point and Mobile / IDD reload)
?>