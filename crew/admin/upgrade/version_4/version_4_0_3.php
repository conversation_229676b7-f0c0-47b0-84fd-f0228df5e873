<?
// Dennis
// Create smart2pay tables
$add_new_tables = array();

$add_new_tables["smart2pay"] = array (	"structure" => "CREATE TABLE `smart2pay` (
														  `smart2pay_order_id` int(11) unsigned NOT NULL,
														  `smart2pay_merchant_id` varchar(37) default NULL,
														  `smart2pay_payment_method` varchar(20) default NULL,
														  `smart2pay_transaction_id` varchar(37) default NULL,
														  `smart2pay_amount` decimal(15,2) default NULL,
														  `smart2pay_currency` varchar(3) default NULL,
														  `smart2pay_status_id` tinyint(2) default NULL,
														  `smart2pay_payment_date` varchar(30) default NULL,
														  PRIMARY KEY  (`smart2pay_order_id`),
														  KEY `smart2pay_transaction_id` (`smart2pay_transaction_id`)
														) TYPE=MyISAM;" ,
										"data" => ""
									);

$add_new_tables["smart2pay_status_history"] = array (	"structure" => "CREATE TABLE `smart2pay_status_history` (
																		  `smart2pay_status_history_id` int(11) unsigned NOT NULL auto_increment,
																		  `smart2pay_order_id` int(11) unsigned NOT NULL,
																		  `smart2pay_date` datetime default NULL,
																		  `smart2pay_status_id` tinyint(2) default NULL,
																		  `smart2pay_description` text,
																		  `smart2pay_changed_by` varchar(128) default NULL,
																		  PRIMARY KEY  (`smart2pay_status_history_id`),
																		  KEY `smart2pay_order_id` (`smart2pay_order_id`)
																		) ENGINE=MyISAM;",
														"data" => ""
													);

add_new_tables ($add_new_tables, $DBTables);
// End of create smart2pay tables

// Ching Yen
// Create new table
$add_new_tables = array();

$add_new_tables["countries_content"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `countries_content` (
																  `countries_id` int(11) unsigned NOT NULL,
																  `geo_zone_id` int(11) unsigned NOT NULL,
																  `tab_content` text,
																  `footer_all_payment_image` text,
																  PRIMARY KEY (`countries_id`,`geo_zone_id`)
																) ENGINE=MyISAM;" ,
												"data" => "" );

$add_new_tables["countries_content_description"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `countries_content_description` (
																			  `countries_id` int(11) unsigned NOT NULL,
																			  `language_id` int(11) unsigned NOT NULL,
																			  `geo_zone_id` int(11) unsigned NOT NULL,
																			  `slider_content` text,
																			  `banner_content` text,
																			  PRIMARY KEY (`countries_id`,`language_id`)
																			) ENGINE=MyISAM;" ,
															"data" => "" );

add_new_tables ($add_new_tables, $DBTables);
// End of create new table


// Insert new permission control on zones_configuration.php
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='zones_configuration.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ZONE_TYPE_1"] = array("insert" => " ('ZONE_TYPE_1', 'Games & Product', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["ZONE_TYPE_2"] = array("insert" => " ('ZONE_TYPE_2', 'Language', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["ZONE_TYPE_3"] = array("insert" => " ('ZONE_TYPE_3', 'Currency', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["ZONE_TYPE_4"] = array("insert" => " ('ZONE_TYPE_4', 'Payment Gateway', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["ZONE_TYPE_5"] = array("insert" => " ('ZONE_TYPE_5', 'Content', ".$row_sql["admin_files_id"].", '1')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`)");
}
// End of insert new permission control on zones_configuration.php


// Insert new zone
$type_5_select_sql = "	SELECT geo_zone_name 
						FROM " . TABLE_GEO_ZONES . "
						WHERE geo_zone_type = 5" ;
$type_5_result_sql = tep_db_query($type_5_select_sql);

if (!tep_db_num_rows($type_5_result_sql)) {	// if not found existing record
	$content_zone = array (	'North America' => array('24','38','85','138','198','223','7','9','12','16','19','231','40','54','59','60','86','87','93','106','134','143','172','178','179','180','213','217','232','22','51','64','89','95','154','164','201', '224'),
							'South America' => array('10','26','30','43','47','62','69','75','92','166','167','200','225','229','8','29','77','94','169','194'),
							'Africa' => array('35','48','58','66','68','110','127','128','136','137','145','174','177','186','192','208','219','238','6','37','41','42','49','65','78','183','3','63','121','144','199','214','28','119','239','147','193','202','23','34','39','52','79','82','90','91','120','131','135','155','156','185','187','210','237'),
							'Europe' => array('2','5','11','14','20','21','27','33','53','55','56','57','67','72','73','80','81','84','97','98','103','105','117','122','123','124','126','132','140','141','241','150','160','170','171','175','182','240','189','190','195','203','204','220','222','228','83','242','151','70','74','197','236'),
							'Middle East' => array('109','115','207','216','226','15','17','102','101','104','108','114','118','161','173','184','205','215','221','235','234'),
							'North Asia' => array('96','107','125','142','112','44','206','113','176'),
							'Southeast Asia' => array('32','146','36','61','100','116','129','168','188','209','230'),
							'South Asia' => array('1','18','25','99','130','149','162','196'),
							'Oceania' => array('13','50','153','71','111','88','133','139','148','152','157','163','165','181','191','212','218','227','4','31','45','46','76','158','159','211','233')
					);
	foreach ($content_zone as $geo_zone_name => $geo_array) {
		$geo_zone_insert_sql = "INSERT INTO `" . TABLE_GEO_ZONES . "` (`geo_zone_name`, `geo_zone_description`, `geo_zone_type`, `date_added`) VALUES 
								('" . $geo_zone_name . "', '" . $geo_zone_name . "', '5', NOW() )";
		tep_db_query($geo_zone_insert_sql);
		$geo_zone_id = tep_db_insert_id();
		
		if (tep_not_empty($geo_zone_id)) {
			foreach ($geo_array as $num => $zone_country_id) {
				$zones_country_insert_sql = "	INSERT INTO `" . TABLE_ZONES_TO_GEO_ZONES . "` (`zone_country_id`, `zone_id`, `geo_zone_id`, `date_added`) VALUES 
												('" . $zone_country_id . "', ',0,', '" . $geo_zone_id . "', NOW() ) ";
				tep_db_query($zones_country_insert_sql);
			}
		}
	}
}
// End of insert new zone

// Wee Siong
// Create new table
$add_new_tables = array();

$add_new_tables["log_ses_bounce"] = array (	"structure" => "CREATE TABLE `log_ses_bounce` (
																`email` varchar(255) NOT NULL,
																`error_string` text,
																`created_datetime` DATETIME NOT NULL,
															PRIMARY KEY (`email`)
															) ENGINE = MYISAM;" ,
											"data" => "" );

add_new_tables ($add_new_tables, $DBTables);
// End of create new table
?>