<?
// <PERSON><PERSON> Hock
// Create DTU tables
$add_new_tables = array();
$add_new_tables["publishers_games_configuration"] = array (	"structure" => "CREATE TABLE `publishers_games_configuration` (
																						  `publishers_games_configuration_id` int(10) unsigned NOT NULL auto_increment COMMENT 'ID',
																						  `publishers_games_id` int(10) unsigned NOT NULL COMMENT 'Publisher ID',
																						  `publishers_games_configuration_title` varchar(64) NOT NULL COMMENT 'Configuration Title, eg. Reminder Amount',
																						  `publishers_games_configuration_key` varchar(64) NOT NULL COMMENT 'Configuration Key',
																						  `publishers_games_configuration_value` text NOT NULL COMMENT 'Configuration Value, eg. 5000',
																						  `publishers_games_configuration_description` varchar(255) NOT NULL COMMENT 'Configuration Description, Min Amount for email notification',
																						  `sort_order` int(11) NOT NULL default '50000' COMMENT 'sorting',
																						  `last_modified` datetime default NULL COMMENT 'Last update time',
																						  `date_added` datetime NOT NULL default '0000-00-00 00:00:00' COMMENT 'Record added time',
																						  `last_modified_by` int(11) NOT NULL,
																						  `use_function` varchar(255) NOT NULL COMMENT 'function to integrate',
																						  `set_function` text NOT NULL COMMENT 'Input type, textbox, select box or ...',
																						  PRIMARY KEY  (`publishers_games_configuration_id`),
																						  KEY `index_publishers_games` (`publishers_games_id`)
																						) ENGINE=MyISAM COMMENT='Publisher Game Configuration';" ,
															"data" => ""
									);
add_new_tables ($add_new_tables, $DBTables);
// End of create DTU tables

?>