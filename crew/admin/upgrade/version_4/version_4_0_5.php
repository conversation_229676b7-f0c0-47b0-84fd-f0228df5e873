<?
// Wilson
// Create archive tables
$archive_db = 'archive_'.DB_DATABASE;
$archive_link = tep_db_connect(DB_SERVER,DB_SERVER_USERNAME,DB_SERVER_PASSWORD,$archive_db,'archive_link');

$create_table_sql = "CREATE TABLE `orders` (
									`orders_id` int(11) NOT NULL auto_increment,
									`customers_id` int(11) NOT NULL default '0',
									`customers_name` varchar(64) NOT NULL default '',
									`customers_company` varchar(32) default NULL,
									`customers_street_address` varchar(64) NOT NULL default '',
									`customers_suburb` varchar(32) default NULL,
									`customers_city` varchar(32) NOT NULL default '',
									`customers_postcode` varchar(10) NOT NULL default '',
									`customers_state` varchar(32) default NULL,
									`customers_country` varchar(64) NOT NULL default '',
									`customers_telephone_country` varchar(64) default NULL,
									`customers_country_international_dialing_code` varchar(5) default NULL,
									`customers_telephone` varchar(32) NOT NULL default '',
									`customers_email_address` varchar(96) NOT NULL default '',
									`customers_address_format_id` int(5) NOT NULL default '0',
									`customers_groups_id` smallint(4) NOT NULL default '0',
									`delivery_name` varchar(64) NOT NULL default '',
									`delivery_company` varchar(32) default NULL,
									`delivery_street_address` varchar(64) NOT NULL default '',
									`delivery_suburb` varchar(32) default NULL,
									`delivery_city` varchar(32) NOT NULL default '',
									`delivery_postcode` varchar(10) NOT NULL default '',
									`delivery_state` varchar(32) default NULL,
									`delivery_country` varchar(64) NOT NULL default '',
									`delivery_address_format_id` int(5) NOT NULL default '0',
									`billing_name` varchar(64) NOT NULL default '',
									`billing_company` varchar(32) default NULL,
									`billing_street_address` varchar(64) NOT NULL default '',
									`billing_suburb` varchar(32) default NULL,
									`billing_city` varchar(32) NOT NULL default '',
									`billing_postcode` varchar(10) NOT NULL default '',
									`billing_state` varchar(32) default NULL,
									`billing_country` varchar(64) NOT NULL default '',
									`billing_address_format_id` int(5) NOT NULL default '0',
									`payment_method` varchar(64) NOT NULL default '',
									`payment_methods_parent_id` int(11) NOT NULL default '0',
									`payment_methods_id` int(11) NOT NULL default '0',
									`cc_type` varchar(20) default NULL,
									`cc_owner` varchar(64) default NULL,
									`cc_number` varchar(32) default NULL,
									`cc_expires` varchar(4) default NULL,
									`last_modified` datetime default NULL,
									`date_purchased` datetime default NULL,
									`orders_status` int(5) NOT NULL default '0',
									`orders_cb_status` tinyint(1) default NULL,
									`orders_date_finished` datetime default NULL,
									`currency` char(3) default NULL,
									`remote_addr` varchar(20) NOT NULL default '',
									`currency_value` decimal(14,8) default NULL,
									`paypal_ipn_id` int(11) NOT NULL default '0',
									`pm_2CO_cc_owner_firstname` varchar(64) default NULL,
									`pm_2CO_cc_owner_lastname` varchar(64) default NULL,
									`orders_locked_by` int(11) default NULL,
									`orders_locked_from_ip` varchar(20) default NULL,
									`orders_locked_datetime` datetime default NULL,
									`orders_follow_up_datetime` datetime default NULL,
									`orders_tag_ids` varchar(255) NOT NULL default '',
									`orders_read_mode` tinyint(1) NOT NULL default '0',
									`orders_aft_executed` tinyint(1) NOT NULL default '0',
									`orders_rebated` tinyint(1) NOT NULL default '0',
									PRIMARY KEY  (`orders_id`),
									KEY `index_paypal_ipn_id` (`paypal_ipn_id`),
									KEY `index_cust_id_and_order_status` (`customers_id`,`orders_status`),
									KEY `index_date_purchased` (`date_purchased`),
									KEY `index_orders_status` (`orders_status`),
									KEY `index_remote_addr` (`remote_addr`),
									KEY `index_payment_method` (`payment_method`),
									KEY `index_customers_telephone` (`customers_telephone`),
									KEY `index_payment_gateway_id` (`payment_methods_parent_id`),
									KEY `index_payment_methods_id` (`payment_methods_id`),
									KEY `index_customers_email` (`customers_email_address`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');

$create_table_sql = "CREATE TABLE `coupon_gv_queue` (
									`unique_id` int(5) NOT NULL auto_increment,
									`customer_id` int(5) NOT NULL default '0',
									`order_id` int(5) NOT NULL default '0',
									`amount` decimal(8,4) NOT NULL default '0.0000',
									`date_created` datetime NOT NULL default '0000-00-00 00:00:00',
									`ipaddr` varchar(32) NOT NULL default '',
									`release_flag` char(1) NOT NULL default 'N',
									PRIMARY KEY  (`unique_id`),
									KEY `uid` (`unique_id`,`customer_id`,`order_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `coupon_redeem_track` (
									`unique_id` int(11) NOT NULL auto_increment,
									`coupon_id` int(11) NOT NULL default '0',
									`customer_id` int(11) NOT NULL default '0',
									`redeem_date` datetime NOT NULL default '0000-00-00 00:00:00',
									`redeem_ip` varchar(32) NOT NULL default '',
									`order_id` int(11) NOT NULL default '0',
									PRIMARY KEY  (`unique_id`),
									KEY `index_coupon_and_customer` (`coupon_id`,`customer_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `customers_top_up_info` (
									`top_up_info_id` int(10) unsigned NOT NULL default '0',
									`orders_products_id` int(11) NOT NULL default '0',
									`top_up_value` varchar(255) NOT NULL default '',
									UNIQUE KEY `unique_top_up_info_id_and_orders_products_id` (`top_up_info_id`,`orders_products_id`),
									KEY `index_orders_products_id` (`orders_products_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `game_char` (
									`game_char_id` int(11) NOT NULL auto_increment,
									`orders_products_id` int(11) NOT NULL default '0',
									`name` varchar(64) NOT NULL default '',
									`server` varchar(32) NOT NULL default '',
									PRIMARY KEY  (`game_char_id`),
									KEY `index_orders_products_id` (`orders_products_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `game_char_log` (
									`game_char_log_id` int(11) NOT NULL auto_increment,
									`game_char_log_orders_id` int(11) default NULL,
									`game_char_log_time` datetime default NULL,
									`game_char_log_ip` varchar(15) NOT NULL default '',
									`game_char_log_account_name` varchar(64) NOT NULL default '',
									`game_char_log_server` varchar(32) NOT NULL default '',
									`game_char_log_realm` varchar(32) NOT NULL default '',
									`game_char_log_race` varchar(64) NOT NULL default '',
									`game_char_log_sender` varchar(64) NOT NULL default '',
									`game_char_log_receiver` varchar(64) NOT NULL default '',
									`game_char_log_subject` varchar(255) default NULL,
									`game_char_log_messages` text,
									`game_char_log_balance_before` varchar(255) NOT NULL default '0',
									`game_char_log_balance_after` varchar(255) NOT NULL default '0',
									`game_char_log_system_messages` text,
									`game_char_log_send` varchar(255) NOT NULL default '',
									`game_char_log_receive` varchar(255) NOT NULL default '',
									`game_char_log_type` varchar(16) NOT NULL default '',
									`game_char_log_login_as` varchar(32) NOT NULL default '',
									`game_char_log_computer_name` varchar(64) NOT NULL default '',
									`game_char_log_login_user` varchar(96) NOT NULL default '',
									`game_char_log_user_role` varchar(16) NOT NULL default '',
									PRIMARY KEY  (`game_char_log_id`),
									KEY `index_orders_id` (`game_char_log_orders_id`)
					) ENGINE=MyISAM COMMENT='Games Characters Log Info';";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `maxmind_history` (
									`orders_id` int(11) NOT NULL default '0',
									`maxmind_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
									`distance` int(11) NOT NULL default '0',
									`country_match` varchar(9) NOT NULL default '',
									`ip_country_code` char(2) NOT NULL default '',
									`free_mail` varchar(9) NOT NULL default '',
									`anonymous_proxy` varchar(9) NOT NULL default '',
									`score` decimal(2,1) NOT NULL default '0.0',
									`bin_match` varchar(9) NOT NULL default '',
									`bin_country_code` varchar(9) NOT NULL default '',
									`error` varchar(64) NOT NULL default '',
									`proxy_score` decimal(4,1) NOT NULL default '0.0',
									`spam_score` decimal(4,2) NOT NULL default '0.00',
									`ip_region` varchar(64) NOT NULL default '',
									`ip_city` varchar(64) NOT NULL default '',
									`ip_latitude` decimal(5,4) NOT NULL default '0.0000',
									`ip_longitude` decimal(5,4) NOT NULL default '0.0000',
									`bin_name` varchar(64) NOT NULL default '',
									`ip_isp` varchar(64) NOT NULL default '',
									`ip_organization` varchar(64) NOT NULL default '',
									`bin_name_match` varchar(9) NOT NULL default '',
									`bin_phone_match` varchar(9) NOT NULL default '',
									`bin_phone` varchar(9) NOT NULL default '',
									`customer_phone_in_billing_location` varchar(9) NOT NULL default '',
									`high_risk_country` char(3) NOT NULL default '',
									`queries_remaining` varchar(64) NOT NULL default '',
									`city_postal_match` varchar(9) NOT NULL default '',
									`shipping_city_postal_match` varchar(9) NOT NULL default '',
									`maxmind_history_source` varchar(50) NOT NULL default '',
									`maxmind_id` varchar(64) NOT NULL default '',
									PRIMARY KEY  (`orders_id`,`maxmind_history_date`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_extra_info` (
									`orders_id` int(11) unsigned NOT NULL default '0',
									`orders_extra_info_key` varchar(32) NOT NULL default '',
									`orders_extra_info_value` varchar(32) NOT NULL default '',
									PRIMARY KEY  (`orders_id`,`orders_extra_info_key`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_log_table` (
									`orders_log_id` int(11) NOT NULL auto_increment,
									`orders_log_admin_id` varchar(255) NOT NULL default '0',
									`orders_log_ip` varchar(15) NOT NULL default '',
									`orders_log_time` datetime default NULL,
									`orders_log_orders_id` int(11) NOT NULL default '0',
									`orders_log_system_messages` text NOT NULL,
									`orders_log_filename` varchar(255) NOT NULL default 'orders.php',
									PRIMARY KEY  (`orders_log_id`),
									KEY `index_orders_log_orders_id` (`orders_log_orders_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');

$create_table_sql = "CREATE TABLE `orders_status_history` (
									`orders_status_history_id` int(11) NOT NULL auto_increment,
									`orders_id` int(11) NOT NULL default '0',
									`orders_status_id` int(5) NOT NULL default '0',
									`date_added` datetime NOT NULL default '0000-00-00 00:00:00',
									`customer_notified` int(1) default '0',
									`comments` text,
									`comments_type` tinyint(1) NOT NULL default '0',
									`set_as_order_remarks` tinyint(1) NOT NULL default '0',
									`changed_by` varchar(128) NOT NULL default '',
									PRIMARY KEY  (`orders_status_history_id`),
									KEY `index_order_id_and_status` (`orders_id`,`orders_status_id`),
									KEY `index_date_added` (`date_added`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_status_stat` (
									`orders_id` int(11) NOT NULL default '0',
									`orders_status_id` int(5) NOT NULL default '0',
									`occurrence` tinyint(1) NOT NULL default '1',
									`first_date` datetime NOT NULL default '0000-00-00 00:00:00',
									`latest_date` datetime NOT NULL default '0000-00-00 00:00:00',
									`changed_by` varchar(128) NOT NULL default '',
									PRIMARY KEY  (`orders_id`,`orders_status_id`),
									KEY `index_latest_date` (`latest_date`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_total` (
									`orders_total_id` int(10) unsigned NOT NULL auto_increment,
									`orders_id` int(11) NOT NULL default '0',
									`title` varchar(255) NOT NULL default '',
									`text` varchar(255) NOT NULL default '',
									`value` decimal(15,4) NOT NULL default '0.0000',
									`class` varchar(32) NOT NULL default '',
									`sort_order` int(11) NOT NULL default '0',
									PRIMARY KEY  (`orders_total_id`),
									KEY `idx_orders_total_orders_id` (`orders_id`),
									KEY `index_class_and_oid` (`class`,`orders_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `payment_extra_info` (
									`orders_id` int(11) NOT NULL auto_increment,
									`transaction_id` varchar(15) NOT NULL default '0',
									`transaction_status` tinyint(1) NOT NULL default '0',
									`transaction_amount` decimal(15,4) NOT NULL default '0.0000',
									`transaction_currency` varchar(10) default '',
									`transaction_text_amount` varchar(32) default '',
									`credit_card_type` varchar(64) default '',
									`credit_card_owner` varchar(255) default '',
									`cardholder_aut_result` varchar(64) default NULL,
									`email_address` varchar(255) default '',
									`billing_address` varchar(255) default '',
									`country` varchar(255) default '',
									`country_code` varchar(15) default '',
									`ip_address` varchar(64) default '',
									`tel` varchar(32) default '',
									`fax` varchar(32) default '',
									`check_result` varchar(255) NOT NULL default '',
									`alert_message` varchar(255) NOT NULL default '',
									`authorisation_mode` char(1) NOT NULL default '',
									`authorisation_result` text,
									PRIMARY KEY  (`orders_id`),
									KEY `index_credit_card_owner` (`credit_card_owner`),
									KEY `index_transaction_id` (`transaction_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_compensate_products` (
									`orders_products_id` int(11) NOT NULL default '0',
									`orders_id` int(11) NOT NULL default '0',
									`compensate_for_orders_products_id` varchar(255) NOT NULL default '',
									`compensate_entered_currency` char(3) NOT NULL default 'USD',
									`compensate_entered_currency_value` decimal(14,8) NOT NULL default '1.00000000',
									`compensate_order_currency` char(3) NOT NULL default 'USD',
									`compensate_order_currency_value` decimal(14,8) NOT NULL default '1.00000000',
									`compensate_accident_amount` decimal(15,4) NOT NULL default '0.0000',
									`compensate_non_accident_amount` decimal(15,4) NOT NULL default '0.0000',
									`compensate_supplier_amount` decimal(15,4) NOT NULL default '0.0000',
									`compensate_by_supplier_id` int(11) NOT NULL default '0',
									`compensate_by_supplier_firstname` varchar(32) NOT NULL default '',
									`compensate_by_supplier_lastname` varchar(32) NOT NULL default '',
									`compensate_by_supplier_code` varchar(64) default NULL,
									`compensate_by_supplier_email_address` varchar(96) NOT NULL default '',
									`orders_compensate_products_added_by` varchar(128) NOT NULL default '',
									`orders_compensate_products_messages` text NOT NULL,
									PRIMARY KEY  (`orders_products_id`),
									KEY `idx_orders_id` (`orders_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_products` (
									`orders_products_id` int(11) NOT NULL auto_increment,
									`orders_id` int(11) NOT NULL default '0',
									`products_id` int(11) NOT NULL default '0',
									`products_model` varchar(12) default NULL,
									`products_name` varchar(255) NOT NULL default '',
									`orders_products_store_price` decimal(19,8) default NULL,
									`products_price` decimal(19,8) NOT NULL default '0.00000000',
									`final_price` decimal(19,8) NOT NULL default '0.00000000',
									`op_rebate` int(11) default NULL,
									`op_rebate_delivered` int(11) default NULL,
									`products_tax` decimal(7,4) NOT NULL default '0.0000',
									`products_quantity` int(2) NOT NULL default '0',
									`products_delivered_quantity` int(2) NOT NULL default '0',
									`products_good_delivered_quantity` decimal(15,2) NOT NULL default '0.00',
									`products_good_delivered_price` decimal(15,4) NOT NULL default '0.0000',
									`products_canceled_quantity` decimal(15,2) NOT NULL default '0.00',
									`products_canceled_price` decimal(15,4) NOT NULL default '0.0000',
									`products_reversed_quantity` decimal(15,2) NOT NULL default '0.00',
									`products_reversed_price` decimal(15,4) NOT NULL default '0.0000',
									`products_bundle_id` int(11) NOT NULL default '0',
									`parent_orders_products_id` int(11) NOT NULL default '0',
									`products_pre_order` tinyint(1) NOT NULL default '0',
									`custom_products_type_id` int(11) NOT NULL default '0',
									`orders_products_is_compensate` tinyint(1) NOT NULL default '0',
									`orders_products_purchase_eta` smallint(1) default NULL,
									`products_categories_id` int(11) NOT NULL default '0',
									PRIMARY KEY  (`orders_products_id`),
									KEY `index_order_id_and_products_id` (`orders_id`,`products_id`),
									KEY `index_products_id` (`products_id`),
									KEY `index_parent_orders_products_id` (`parent_orders_products_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_custom_products` (
									`orders_custom_products_id` int(11) NOT NULL auto_increment,
									`products_id` int(11) NOT NULL default '0',
									`orders_products_id` int(11) NOT NULL default '0',
									`orders_custom_products_key` varchar(100) NOT NULL default '',
									`orders_custom_products_value` text NOT NULL,
									`orders_custom_products_number` smallint(5) unsigned NOT NULL default '0',
									PRIMARY KEY  (`orders_custom_products_id`),
									KEY `index_orders_products_id` (`orders_products_id`),
									KEY `index_orders_custom_products_key` (`orders_custom_products_key`),
									KEY `index_products_id` (`products_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_products_attributes` (
									`orders_products_attributes_id` int(11) NOT NULL auto_increment,
									`orders_id` int(11) NOT NULL default '0',
									`orders_products_id` int(11) NOT NULL default '0',
									`products_options` varchar(32) NOT NULL default '',
									`products_options_values` varchar(32) NOT NULL default '',
									`options_values_price` decimal(15,4) NOT NULL default '0.0000',
									`price_prefix` char(1) NOT NULL default '',
									`products_options_id` int(11) NOT NULL default '0',
									`products_options_values_id` int(11) NOT NULL default '0',
									PRIMARY KEY  (`orders_products_attributes_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_products_eta` (
									`orders_products_id` int(11) NOT NULL default '0',
									`expiry_hour` int(11) NOT NULL default '0',
									`start_time` datetime NOT NULL default '0000-00-00 00:00:00',
									`total_buyback_time` int(11) NOT NULL default '0',
									PRIMARY KEY  (`orders_products_id`),
									KEY `index_expiry_hour` (`expiry_hour`)
					) ENGINE=MyISAM COMMENT='Control when to turn off buybak purchase by cronjob';";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_products_extra_info` (
									`orders_products_id` int(11) NOT NULL default '0',
									`orders_products_extra_info_key` varchar(100) NOT NULL default '',
									`orders_products_extra_info_value` text NOT NULL,
									PRIMARY KEY  (`orders_products_id`,`orders_products_extra_info_key`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_products_item` (
									`orders_products_id` int(11) NOT NULL default '0',
									`products_hla_characters_id` int(10) unsigned NOT NULL default '0',
									`products_hla_characters_name` varchar(64) NOT NULL default '',
									`orders_products_item_info` text NOT NULL,
									PRIMARY KEY  (`orders_products_id`,`products_hla_characters_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_top_up` (
									`orders_products_id` int(11) NOT NULL default '0' COMMENT 'OGM order product ID',
									`top_up_id` int(10) unsigned NOT NULL auto_increment COMMENT 'Top-up ID',
									`publishers_ref_id` varchar(16) NOT NULL default '' COMMENT 'Publisher reference ID',
									`top_up_status` enum('1','3','10','11') NOT NULL default '1' COMMENT 'Top-up status, 1=Pending, 3=Processing, 10=Failed, 11=NotFound',
									`publishers_response_time` datetime default NULL COMMENT 'Last response time from publisher, like top-up successful time',
									`customer_before_balance` double default NULL COMMENT 'Customer''s Balance before top-up',
									`customer_after_balance` double default NULL COMMENT 'Customer''s Balance after top-up',
									`game` varchar(64) default NULL COMMENT 'Customer''s game to top-up',
									`server` varchar(64) default NULL COMMENT 'Customer''s server to top-up',
									`account` varchar(64) default NULL COMMENT 'Customer''s account to top-up',
									`character` varchar(64) default NULL COMMENT 'Customer''s character to top-up',
									`publishers_id` int(10) unsigned NOT NULL default '0',
									`top_up_process_flag` enum('0','1','2') NOT NULL default '0' COMMENT '0 = pending, 1 = processing, 2=complete',
									`result_code` int(11) NOT NULL default '0',
									`top_up_last_processed_time` datetime NOT NULL default '0000-00-00 00:00:00',
									`top_up_timestamp` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
									`top_up_created_date` datetime NOT NULL default '0000-00-00 00:00:00',
									PRIMARY KEY  (`top_up_id`),
									UNIQUE KEY `unique_orders_products_id` (`orders_products_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


$create_table_sql = "CREATE TABLE `orders_top_up_remark` (
									`orders_top_up_remark_id` int(10) unsigned NOT NULL auto_increment,
									`top_up_id` int(10) unsigned NOT NULL default '0' COMMENT 'Top-up ID',
									`data_added` datetime NOT NULL default '0000-00-00 00:00:00' COMMENT 'Record added time',
									`remark` text NOT NULL COMMENT 'Remark',
									PRIMARY KEY  (`orders_top_up_remark_id`)
					) ENGINE=MyISAM ;";
tep_db_query($create_table_sql, 'archive_link');


tep_db_close('archive_link');
// End of create archive tables

?>