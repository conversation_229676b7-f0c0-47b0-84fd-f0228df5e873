<?
// Ching Yen
// Add index key for 
add_index_key (TABLE_GAME_TO_PUBLISHER, 'index_publisher_id', 'index', 'publisher_id', $DBTables);

// Insert new permission control on geo_zones.php
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='geo_zones.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["GEO_ZONE_TYPE_1"] = array("insert" => " ('GEO_ZONE_TYPE_1', 'Games & Product', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["GEO_ZONE_TYPE_2"] = array("insert" => " ('GEO_ZONE_TYPE_2', 'Language', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["GEO_ZONE_TYPE_3"] = array("insert" => " ('GEO_ZONE_TYPE_3', 'Currency', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["GEO_ZONE_TYPE_4"] = array("insert" => " ('GEO_ZONE_TYPE_4', 'Payment Gateway', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["GEO_ZONE_TYPE_5"] = array("insert" => " ('GEO_ZONE_TYPE_5', 'Content', ".$row_sql["admin_files_id"].", '1')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`)");
}
// End of insert new permission control on geo_zones.php
?>