<?
// Wilson Sun
// Create new table
$add_new_tables = array();

$add_new_tables["po_suppliers"] = array (	"structure" => "CREATE TABLE `po_suppliers` (
                                                                `po_suppliers_id` int(11) NOT NULL default '0',
                                                                `po_supplier_code` varchar(64) NOT NULL default '',
                                                                `po_payment_type` char(1) NOT NULL default 'c',
                                                                `po_payment_term` int(11) NOT NULL default '0',
                                                                `po_supplier_status` int(1) NOT NULL default '0',
                                                                `po_supplier_agreement_start_date` datetime default '0000-00-00 00:00:00',
                                                                `po_supplier_agreement_end_date` datetime default '0000-00-00 00:00:00',
                                                                `po_supplier_agreement_discount_terms` text,
                                                                `po_supplier_locked_by` int(11) default NULL,
                                                                `po_supplier_locked_from_ip` varchar(20) default NULL,
                                                                `po_supplier_locked_datetime` datetime default NULL,
                                                                PRIMARY KEY  (`po_suppliers_id`),
                                                                KEY `po_supplier_code` (`po_supplier_code`)
                                                            ) ENGINE=MyISAM ;",
											"data" => "" );

$add_new_tables["purchase_orders"] = array (	"structure" => "CREATE TABLE `purchase_orders` (
                                                                    `purchase_orders_id` int(11) unsigned NOT NULL auto_increment,
                                                                    `purchase_orders_ref_id` varchar(32) NOT NULL default '',
                                                                    `supplier_id` int(11) NOT NULL default '0',
                                                                    `supplier_name` varchar(64) NOT NULL default '',
                                                                    `supplier_company` varchar(32) default NULL,
                                                                    `supplier_street_address` varchar(64) NOT NULL default '',
                                                                    `supplier_suburb` varchar(32) default NULL,
                                                                    `supplier_city` varchar(32) NOT NULL default '',
                                                                    `supplier_postcode` varchar(10) NOT NULL default '',
                                                                    `supplier_state` varchar(32) default NULL,
                                                                    `supplier_country` varchar(64) NOT NULL default '',
                                                                    `supplier_telephone_country` varchar(64) default NULL,
                                                                    `supplier_country_international_dialing_code` varchar(5) default NULL,
                                                                    `supplier_telephone` varchar(32) NOT NULL default '',
                                                                    `supplier_email_address` varchar(96) NOT NULL default '',
                                                                    `supplier_address_format_id` int(5) NOT NULL default '0',
                                                                    `delivery_location` char(2) NOT NULL default 'MY',
                                                                    `delivery_name` varchar(64) NOT NULL default '',
                                                                    `delivery_company` varchar(32) default NULL,
                                                                    `delivery_street_address` varchar(64) NOT NULL default '',
                                                                    `delivery_suburb` varchar(32) default NULL,
                                                                    `delivery_city` varchar(32) NOT NULL default '',
                                                                    `delivery_postcode` varchar(10) NOT NULL default '',
                                                                    `delivery_state` varchar(32) default NULL,
                                                                    `delivery_country` varchar(64) NOT NULL default '',
                                                                    `delivery_address_format_id` int(5) NOT NULL default '0',
                                                                    `payment_type` char(1) NOT NULL default 'c',
                                                                    `payment_term` int(11) NOT NULL default '0',
                                                                    `store_payment_account_book_id` int(11) NOT NULL default '0',
                                                                    `purchase_orders_contact_info` varchar(64) NOT NULL default '',
                                                                    `purchase_orders_issue_date` datetime default NULL,
                                                                    `purchase_orders_status` int(5) NOT NULL default '0',
                                                                    `purchase_orders_date_finished` datetime default NULL,
                                                                    `purchase_orders_paid_amount` decimal(13,8) default '0.********',
                                                                    `purchase_orders_paid_currency` char(3) default NULL,
                                                                    `currency` char(3) default NULL,
                                                                    `suggested_currency_value` decimal(13,8) default NULL,
                                                                    `confirmed_currency_value` decimal(13,8) default NULL,
                                                                    `currency_usd_value` decimal(13,8) default NULL,
                                                                    `purchase_orders_tag_ids` varchar(255) NOT NULL default '',
                                                                    `last_modified` datetime default NULL,
                                                                    `purchase_orders_last_printed_by` int(11) default NULL,
                                                                    `purchase_orders_last_printed_from_ip` varchar(20) default NULL,
                                                                    `purchase_orders_last_printed` datetime default NULL,
                                                                    `purchase_orders_locked_by` int(11) default NULL,
                                                                    `purchase_orders_locked_from_ip` varchar(20) default NULL,
                                                                    `purchase_orders_locked_datetime` datetime default NULL,
                                                                    `purchase_orders_paid_status` int(5) NOT NULL default '0',
                                                                    `purchase_orders_billing_status` int(5) NOT NULL default '0',
                                                                    `purchase_orders_verify_mode` tinyint(1) NOT NULL default '0',
                                                                    PRIMARY KEY  (`purchase_orders_id`),
                                                                    KEY `purchase_orders_ref_id` (`purchase_orders_ref_id`),
                                                                    KEY `supplier_id` (`supplier_id`,`purchase_orders_status`),
                                                                    KEY `purchase_orders_issue_date` (`purchase_orders_issue_date`),
                                                                    KEY `purchase_orders_status` (`purchase_orders_status`)
                                                                ) ENGINE=MyISAM ;",
												"data" => "" );

$add_new_tables["purchase_orders_products"] = array (	"structure" => "CREATE TABLE `purchase_orders_products` (
                                                                            `purchase_orders_products_id` int(11) unsigned NOT NULL auto_increment,
                                                                            `purchase_orders_id` int(11) unsigned NOT NULL default '0',
                                                                            `products_id` int(11) NOT NULL default '0',
                                                                            `products_model` varchar(12) default NULL,
                                                                            `products_name` varchar(255) NOT NULL default '',
                                                                            `products_unit_price_type` char(1) NOT NULL default '',
                                                                            `products_unit_price_value` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_unit_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_selling_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_subtotal` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_usd_unit_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_usd_selling_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_usd_subtotal` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_tax` decimal(9,8) NOT NULL default '0.********',
                                                                            `products_quantity` int(11) NOT NULL default '0',
                                                                            `products_delivered_quantity` int(11) NOT NULL default '0',
                                                                            `products_good_delivered_quantity` int(11) NOT NULL default '0',
                                                                            `products_good_delivered_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_good_delivered_usd_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_canceled_quantity` int(11) NOT NULL default '0',
                                                                            `products_canceled_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_canceled_usd_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_refund_quantity` int(11) NOT NULL default '0',
                                                                            `products_refund_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_refund_usd_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_credit_note_quantity` int(11) NOT NULL default '0',
                                                                            `products_credit_note_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `products_credit_note_usd_price` decimal(13,8) NOT NULL default '0.********',
                                                                            `custom_products_type_id` int(11) NOT NULL default '0',
                                                                            `products_categories_id` int(11) NOT NULL default '0',
                                                                            PRIMARY KEY  (`purchase_orders_products_id`),
                                                                            KEY `purchase_orders_id` (`purchase_orders_id`,`products_id`)
                                                                        ) ENGINE=MyISAM ;",
														"data" => "" );

$add_new_tables["purchase_orders_status"] = array (	"structure" => "CREATE TABLE `purchase_orders_status` (
																				`purchase_orders_status_id` int(11) NOT NULL default '0',
																				`language_id` int(1) NOT NULL default '1',
																				`purchase_orders_status_name` varchar(32) NOT NULL default '',
																				`purchase_orders_status_sort_order` int(5) NOT NULL default '50000',
																				PRIMARY KEY  (`purchase_orders_status_id`,`language_id`)
																			) ENGINE=MyISAM ;",
													"data" => "	INSERT INTO `purchase_orders_status` (`purchase_orders_status_id`, `language_id`, `purchase_orders_status_name`, `purchase_orders_status_sort_order`) 
																VALUES (1, 1, 'Pending', 10000),
																		(2, 1, 'Processing', 20000),
																		(3, 1, 'Completed', 30000),
																		(4, 1, 'Canceled', 50000);"
													);

$add_new_tables["purchase_orders_status_history"] = array (	"structure" => "CREATE TABLE `purchase_orders_status_history` (
																						`purchase_orders_status_history_id` int(11) unsigned NOT NULL auto_increment,
																						`purchase_orders_id` int(11) unsigned NOT NULL default '0',
																						`purchase_orders_status_id` int(5) NOT NULL default '0',
																						`date_added` datetime NOT NULL default '0000-00-00 00:00:00',
																						`comments` text NOT NULL,
																						`comments_type` tinyint(1) NOT NULL default '0',
																						`set_as_po_remarks` tinyint(1) NOT NULL default '0',
																						`supplier_notified` int(1) default '0',
																						`changed_by` varchar(128) NOT NULL default '',
																						PRIMARY KEY  (`purchase_orders_status_history_id`),
																						KEY `purchase_orders_id` (`purchase_orders_id`,`purchase_orders_status_id`),
																						KEY `date_added` (`date_added`)
																					) ENGINE=MyISAM ;",
															"data" => "" );

$add_new_tables["purchase_orders_status_stat"] = array (	"structure" => "CREATE TABLE `purchase_orders_status_stat` (
																						`purchase_orders_id` int(11) unsigned NOT NULL default '0',
																						`purchase_orders_status_id` int(5) NOT NULL default '0',
																						`occurrence` tinyint(1) NOT NULL default '1',
																						`latest_date` datetime NOT NULL default '0000-00-00 00:00:00',
																						`changed_by` varchar(128) NOT NULL default '',
																						PRIMARY KEY  (`purchase_orders_id`,`purchase_orders_status_id`),
																						KEY `latest_date` (`latest_date`)
																					) ENGINE=MyISAM ;",
															"data" => "" );

$add_new_tables["store_payments_transaction_info"] = array (	"structure" => "CREATE TABLE `store_payments_transaction_info` (
																							`store_payments_transaction_id` int(11) unsigned NOT NULL auto_increment,
																							`store_payments_id` int(11) unsigned NOT NULL default '0',
																							`store_payments_reimburse_id` int(11) unsigned NOT NULL default '0',
																							`store_payments_reimburse_table` varchar(255) NOT NULL default '',
																							`store_payments_reimburse_amount` decimal(15,4) NOT NULL default '0.0000',
																							`store_payments_reimburse_currency` char(3) NOT NULL default '',
																							PRIMARY KEY  (`store_payments_transaction_id`),
																							KEY `store_payments_id` (`store_payments_id`,`store_payments_reimburse_id`)
																						) ENGINE=MyISAM ;",
																"data" => "" );

$add_new_tables["store_credit_note_history"] = array (	"structure" => "CREATE TABLE `store_credit_note_history` (
                                                                            `store_credit_note_history_id` int(10) unsigned NOT NULL auto_increment,
                                                                            `customer_id` int(11) NOT NULL default '0',
                                                                            `store_credit_note_history_date` datetime NOT NULL default '0000-00-00 00:00:00',
                                                                            `store_credit_note_history_currency` char(3) NOT NULL default '',
                                                                            `store_credit_note_history_debit_amount` decimal(13,8) default NULL,
                                                                            `store_credit_note_history_credit_amount` decimal(13,8) default NULL,
                                                                            `store_credit_note_history_after_balance` decimal(13,8) NOT NULL default '0.********',
                                                                            `store_credit_note_history_trans_type` varchar(10) NOT NULL default '',
                                                                            `store_credit_note_history_trans_id` varchar(255) NOT NULL default '',
                                                                            `store_credit_note_history_activity_type` char(2) NOT NULL default '',
                                                                            `store_credit_note_history_activity_title` varchar(128) NOT NULL default '',
                                                                            `store_credit_note_history_added_by` varchar(128) NOT NULL default '',
                                                                            `store_credit_note_history_added_by_role` varchar(16) NOT NULL default '',
                                                                            PRIMARY KEY  (`store_credit_note_history_id`),
                                                                            KEY `customer_id` (`customer_id`),
                                                                            KEY `store_credit_note_history_date` (`store_credit_note_history_date`),
                                                                            KEY `store_credit_note_history_trans_id` (`store_credit_note_history_trans_id`)
                                                                        ) ENGINE=MyISAM ;",
														"data" => "" );

$add_new_tables["purchase_orders_total"] = array (	"structure" => "CREATE TABLE `purchase_orders_total` (
                                                                        `purchase_orders_total_id` int(10) unsigned NOT NULL auto_increment,
                                                                        `purchase_orders_id` int(10) unsigned NOT NULL default '0',
                                                                        `title` varchar(255) NOT NULL default '',
                                                                        `usd_value` decimal(13,8) NOT NULL default '0.********',
                                                                        `value` decimal(13,8) NOT NULL default '0.********',
                                                                        `currency` char(3) NOT NULL default '',
                                                                        `class` varchar(32) NOT NULL default '',
                                                                        `sort_order` int(11) NOT NULL default '0',
                                                                        PRIMARY KEY  (`purchase_orders_total_id`),
                                                                        KEY `purchase_orders_id` (`purchase_orders_id`),
                                                                        KEY `class` (`class`,`purchase_orders_id`)
                                                                    ) ENGINE=MyISAM ;",
													"data" => "" );

$add_new_tables["store_credit_note_comments"] = array (	"structure" => "CREATE TABLE `store_credit_note_comments` (
                                                                            `store_credit_note_comments_id` int(11) NOT NULL auto_increment,
                                                                            `store_credit_note_history_id` int(11) NOT NULL default '0',
                                                                            `store_credit_note_comments_date_added` datetime NOT NULL default '0000-00-00 00:00:00',
                                                                            `store_credit_note_comments` text NOT NULL,
                                                                            `store_credit_note_comments_added_by` varchar(255) NOT NULL default '',
                                                                            PRIMARY KEY  (`store_credit_note_comments_id`),
                                                                            KEY `idx_store_account_history_id` (`store_credit_note_history_id`)
                                                                        ) ENGINE=MyISAM ;",
														"data" => "" );


add_new_tables ($add_new_tables, $DBTables);
// End of create new table

// Insert new fields
$add_new_field = array();

// Insert new fields into `custom_products_code` table
$add_new_field['custom_products_code'] = array (	array (	"field_name" => "purchase_orders_ref_id",
                                                            "field_attr" => "varchar(32) NOT NULL",
                                                            "add_after" => ''
														)
                                                );

// Insert new fields into `custom_product_vault` table
$add_new_field['custom_product_vault'] = array (	array (	"field_name" => "purchase_orders_ref_id",
                                                            "field_attr" => "varchar(32) NOT NULL",
                                                            "add_after" => ''
														)
											);

// Insert new fields into `store_account_balance` table
$add_new_field['store_account_balance'] = array (	array (	"field_name" => "store_account_po_wsc",
                                                            "field_attr" => "decimal(15,4) NOT NULL DEFAULT '0.0000'",
                                                            "add_after" => 'store_account_reserve_amount'
														),
													array (	"field_name" => "store_account_credit_note_amount",
                                                            "field_attr" => "decimal(15,4) NOT NULL DEFAULT '0.0000'",
                                                            "add_after" => 'store_account_po_wsc'
														)
											);

// Insert new fields into `payment_methods` table
$add_new_field['payment_methods'] = array (	array (	"field_name" => "payment_methods_send_available_sites",
													"field_attr" => "varchar(12) NOT NULL DEFAULT '0'",
													"add_after" => 'payment_methods_send_mass_payment'
												)
											);

add_field($add_new_field);
// End of insert new fields

// Insert new records
// Insert new records into site_code table (for PO)
$site_code_insert_sql = array();
$site_code_insert_sql["3"] = array(	"insert" => " ('3', 'PO System', '0', '1', '1') ");
insert_new_records(TABLE_SITE_CODE, "site_id", $site_code_insert_sql, $DBTables, "(site_id, site_name, site_has_buyback, admin_groups_id, buyback_admin_groups_id)", " site_id='3' ");

// Insert new records into admin_files table (for PO)
$admin_files_insert_sql = array();
$admin_files_insert_sql["purchase_orders.php"] = array(	"insert" => " ('purchase_orders.php', '1', '0', '1', '0') ");
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id, admin_files_cat_setting)", " admin_files_name='purchase_orders.php' AND admin_files_is_boxes=1 ");


// Insert new records into configuration_group table (for Telesign service switch and configuration)
$configuration_group_insert_sql = array();
$configuration_group_insert_sql["Purchase Order Supplier"] = array(	"insert" => " ('Purchase Order Supplier', 'Purchase Order Supplier Configuration', '300', '1') ");

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");

$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Purchase Order Supplier'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
    
    $conf_insert_sql["MAX_PO_SUPPLIER_PAYMENT_BOOK_ENTRIES"] = array(	"insert" => " ('PO Supplier\'s Payment Account Book Entries', 'MAX_PO_SUPPLIER_PAYMENT_BOOK_ENTRIES', '5', 'Maximum payment account book entries a supplier is allowed to have', ".$row_sql["configuration_group_id"].", 1, NULL, now(), NULL, '') ");
    $conf_insert_sql["PO_SUPPLIER_INACTIVE_NOTIFICATION_EMAIL"] = array(	"insert" => " ('Inactive PO Supplier Notification Email Address', 'PO_SUPPLIER_INACTIVE_NOTIFICATION_EMAIL', '', 'Email address to which the email will be send to whenever new po supplier added or change of supplier info.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 2, NULL, now(), NULL, '') ");
    $conf_insert_sql["PO_NEW_PO_NOTIFICATION_EMAIL"] = array(	"insert" => " ('New PO Notification Email Address', 'PO_NEW_PO_NOTIFICATION_EMAIL', '', 'Email address to which the email will be send to whenever new po generated.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 3, NULL, now(), NULL, '') ");
    $conf_insert_sql["PO_WSC_AUTO_CREDIT_NOTIFICATION_EMAIL"] = array(	"insert" => " ('PO WSC Auto Credited Notification Email Address', 'PO_WSC_AUTO_CREDIT_NOTIFICATION_EMAIL', '', 'Email address to which the email will be send to whenever PO WSC is auto credited during PO completion.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 4, NULL, now(), NULL, '') ");

	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of Insert new records into configuration_group table (for Telesign service switch and configuration)

// Insert new records into site_customers_access table (for PO)
$site_customers_access_insert_sql = array();
$site_customers_access_insert_sql["3"] = array(	"insert" => " ('3', '0', '1', '1', '') ");
insert_new_records(TABLE_SITE_CUSTOMERS_ACCESS, "site_id", $site_customers_access_insert_sql, $DBTables, "(site_id, customers_groups_id, admin_groups_id, discount_setting_admin_groups_id, discount_setting_notification)", " site_id='3' AND customers_groups_id='0' AND admin_groups_id='1' ");


// Insert new records into admin_files table (for PO)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='purchase_orders.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["po_suppliers.php"] = array("insert" => " ('po_suppliers.php', 0, '".$row_sql['admin_files_id']."', '1') ");
	$admin_files_insert_sql["edit_purchase_orders.php"] = array("insert" => " ('edit_purchase_orders.php', 0, '".$row_sql['admin_files_id']."', '1') ");
	$admin_files_insert_sql["printable_purchase_orders.php"] = array("insert" => " ('printable_purchase_orders.php', 0, '".$row_sql['admin_files_id']."', '1') ");
	$admin_files_insert_sql["po_payment.php"] = array("insert" => " ('po_payment.php', 0, '".$row_sql['admin_files_id']."', '1') ");
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}

// Insert new records into admin_files table (for payments)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='payments.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["credit_notes_statement.php"] = array("insert" => " ('credit_notes_statement.php', 0, '".$row_sql['admin_files_id']."', '1') ");
	$admin_files_insert_sql["po_provision_payments.php"] = array("insert" => " ('po_provision_payments.php', 0, '".$row_sql['admin_files_id']."', '1') ");
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}

// Insert new records into admin_files_actions table (for po suppliers)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='po_suppliers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["PO_NEW_SUPPLIER"] = array("insert" => " ('PO_NEW_SUPPLIER', 'New PO Supplier', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_EDIT_SUPPLIER_ACCOUNT"] = array("insert" => " ('PO_EDIT_SUPPLIER_ACCOUNT', 'Edit PO Supplier Account', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_VIEW_SUPPLIER_PAYMENT_INFO"] = array("insert" => " ('PO_VIEW_SUPPLIER_PAYMENT_INFO', 'View PO Supplier Payment Info', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_EDIT_SUPPLIER_PAYMENT_INFO"] = array("insert" => " ('PO_EDIT_SUPPLIER_PAYMENT_INFO', 'Edit PO Supplier Payment Info', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_VIEW_SUPPLIER_PAYMENT_STATISTIC"] = array("insert" => " ('PO_VIEW_SUPPLIER_PAYMENT_STATISTIC', 'View PO Supplier Payment Statistic', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_VIEW_SUPPLIER_REMARK"] = array("insert" => " ('PO_VIEW_SUPPLIER_REMARK', 'View PO Supplier Remarks', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_EDIT_SUPPLIER_REMARK"] = array("insert" => " ('PO_EDIT_SUPPLIER_REMARK', 'Edit PO Supplier Remarks', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_VIEW_SUPPLIER_DISCOUNT_TERMS"] = array("insert" => " ('PO_VIEW_SUPPLIER_DISCOUNT_TERMS', 'View PO Supplier Discount Terms Info', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_EDIT_SUPPLIER_DISCOUNT_TERMS"] = array("insert" => " ('PO_EDIT_SUPPLIER_DISCOUNT_TERMS', 'Edit PO Supplier Discount Terms Info', ".$row_sql["admin_files_id"].", '1')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`)");
}

// Insert new records into admin_files_actions table (for edit purchase orders)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='edit_purchase_orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["PO_NEW_PO"] = array("insert" => " ('PO_NEW_PO', 'New Purchase Order', '2', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_APPROVE_PO"] = array("insert" => " ('PO_APPROVE_PO', 'Approve New Purchase Order', '3', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_PROCESS_PO"] = array("insert" => " ('PO_PROCESS_PO', 'Process Purchase Order', '4', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_CANCEL_PENDING_RECEIVE"] = array("insert" => " ('PO_CANCEL_PENDING_RECEIVE', 'Cancel Pending Receive Stock', '5', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_REFUND_CREDIT_NOTE"] = array("insert" => " ('PO_REFUND_CREDIT_NOTE', 'Refund as Credit Note', '5', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_REFUND_CASH"] = array("insert" => " ('PO_REFUND_CASH', 'Refund as Cash', '6', ".$row_sql["admin_files_id"].", '1')" );
    $admin_files_actions_insert_sql["PO_VERIFY_PO"] = array("insert" => " ('PO_VERIFY_PO', 'Verify/unverify Completed Purchase Order', '7', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_MAKE_PAYMENT"] = array("insert" => " ('PO_MAKE_PAYMENT', 'Make Payment', '8', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_ADD_REMARK"] = array("insert" => " ('PO_ADD_REMARK', 'Add Remark', '10', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["PO_VIEW_REMARK"] = array("insert" => " ('PO_VIEW_REMARK', 'View Remarks', '11', ".$row_sql["admin_files_id"].", '1')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}

// Insert new records into admin_files_actions table (for credit note statement)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='credit_notes_statement.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["STORE_CREDIT_NOTES_MANUAL_ADD"] = array("insert" => " ('STORE_CREDIT_NOTES_MANUAL_ADD', 'Manual Addition', '8', ".$row_sql["admin_files_id"].", '1')" );
	$admin_files_actions_insert_sql["STORE_CREDIT_NOTES_MANUAL_DEDUCT"] = array("insert" => " ('STORE_CREDIT_NOTES_MANUAL_DEDUCT', 'Manual Deduction', '8', ".$row_sql["admin_files_id"].", '1')" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}

// Insert new records into status_configuration table (for PO status movement PENDING to PROCESSING)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PO"] = array("insert" => " ('PO', '1', '2', '1', '', '-1') ");
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", " status_configuration_trans_type='PO' AND status_configuration_source_status_id='1' AND status_configuration_destination_status_id='2' ");

// Insert new records into status_configuration table (for PO status movement PENDING to CANCELLED)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PO"] = array("insert" => " ('PO', '1', '4', '1', '', '-1') ");
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", " status_configuration_trans_type='PO' AND status_configuration_source_status_id='1' AND status_configuration_destination_status_id='4' ");

// Insert new records into status_configuration table (for PO status movement PROCESSING to COMPLETED)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PO"] = array("insert" => " ('PO', '2', '3', '1', '', '') ");
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", " status_configuration_trans_type='PO' AND status_configuration_source_status_id='2' AND status_configuration_destination_status_id='3' ");

// Insert new records into status_configuration table (for PO status movement COMPLETED to PROCESSING)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["PO"] = array("insert" => " ('PO', '3', '2', '1', '', '') ");
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", " status_configuration_trans_type='PO' AND status_configuration_source_status_id='3' AND status_configuration_destination_status_id='2' ");

// End of Insert new records
?>