﻿<?php
// <PERSON><PERSON>ck
// Insert new fields
$add_new_field = array();

// Insert new fields into `orders_top_up` table
$add_new_field['orders_top_up'] = array (	array (	"field_name" => "top_up_response_info",
													"field_attr" => "TEXT NOT NULL",
													"add_after" => 'publishers_id'
														)
											);

add_field($add_new_field);
// End of insert new fields

// Change field structure for publishers_ref_id in orders_top_up table
$change_field_structure = array();

$change_field_structure['orders_top_up'] = array (array (	"field_name" => "publishers_ref_id",
															"field_attr" => "varchar(32) NOT NULL"
									 					)
												);
change_field_structure ($change_field_structure);
// End of change field structure for publishers_ref_id in orders_top_up table

// Change field structure for publisher_ref_id in api_log table
$change_field_structure = array();

$change_field_structure['api_log'] = array (array (	"field_name" => "publisher_ref_id",
													"field_attr" => "varchar(32) NOT NULL"
							 					)
										);
change_field_structure ($change_field_structure);
// End of change field structure for publisher_ref_id in api_log table


// Ching Yen
// Insert new schedule task
$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_checkout_qty_ctrl.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_process_track_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_checkout_qty_ctrl.php'
			                       );
	tep_db_perform('cron_process_track', $cron_process_track_array);
}
// End of insert new schedule task

// Insert new records into categories_configuration table (for Checkout Qty Control)
$conf_insert_sql = array();
$conf_insert_sql["STOCK_NON_ZERO_PRICE_CHECKOUT_QTY_NOTIFICATION"] = array("insert" => " ('Non-zero Price Product Checkout Qty Control', 'STOCK_NON_ZERO_PRICE_CHECKOUT_QTY_NOTIFICATION', '', 'Email address to which the email will be send to whenever someone set the setting for non-zero price product and affect the sales (customer not able to checkout more).<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 100, NULL, NOW(), NULL, NULL)" );

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into categories_configuration table (for Checkout Qty Control)

// Delete `define_mainpage` records in admin_files table
$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'define_mainpage.php'
								AND admin_files_is_boxes=0";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}

// Delete `define_helpcenterpage` records in admin_files table
$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'define_helpcenterpage.php'
								AND admin_files_is_boxes=0";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}

// Delete `specials` records in admin_files table
$admin_file_select_sql = "	SELECT admin_files_id 
							FROM " . TABLE_ADMIN_FILES . "
							WHERE admin_files_name = 'specials.php'
								AND admin_files_is_boxes=0";
$admin_file_result_sql = tep_db_query($admin_file_select_sql);

if ($admin_file_row = tep_db_fetch_array($admin_file_result_sql)) {
	$delete_admin_file_sql = "DELETE FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_sql);
	
	$delete_admin_file_action_sql = "DELETE FROM " . TABLE_ADMIN_FILES_ACTIONS . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_action_sql);
	
	$delete_admin_file_cat_sql = "DELETE FROM " . TABLE_ADMIN_FILES_CATEGORIES . " WHERE admin_files_id = '" . $admin_file_row['admin_files_id'] . "'";
	tep_db_query($delete_admin_file_cat_sql);
}


// Wilson
// Change field structure for po_supplier_code in po_suppliers table
$change_field_structure = array();

$change_field_structure['po_suppliers'] = array (array ("field_name" => "po_supplier_code",
														"field_attr" => " varchar(16) NOT NULL default '' "
														)
												);

change_field_structure ($change_field_structure);
// End of change field structure for po_supplier_code in po_suppliers table
?>