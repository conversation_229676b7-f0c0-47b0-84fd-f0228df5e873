<?php

// Remove useless admin file action key(s)
$action_keys_delete_sql = array();

$action_keys_delete_sql['PO_APPROVE_PO'] = array(	"unique" => "1" );

delete_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $action_keys_delete_sql, $DBTables);
// End of remove useless admin file action key(s)

// Update script name in admin file action table
$update_sql = array();

$update_sql[TABLE_ADMIN_FILES_ACTIONS] = array(	array(	"field_name" => "admin_files_actions_name",
                                                        "update" => " admin_files_actions_name='Upload CD Keys from PO' ",
                                                        "where_str" => " admin_files_actions_key='PO_PROCESS_PO' "
                                                      ),
                                                array(	"field_name" => "admin_files_actions_name",
                                                        "update" => " admin_files_actions_name='Credit WSC for Pre-payment PO' ",
                                                        "where_str" => " admin_files_actions_key='PO_MAKE_PAYMENT' "
                                                      )
                                              );

advance_update_records($update_sql, $DBTables);
// End of update script name in admin file action table

// Ching Yen
// Insert new schedule task
$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_undelivered_report.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_process_track_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_undelivered_report.php'
			                       );
	tep_db_perform('cron_process_track', $cron_process_track_array);
}
// End of insert new schedule task

// Create cron_undelivered_report table
$add_new_tables["cron_undelivered_report"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `cron_undelivered_report` (
																		  `payment_methods_id` int(11) NOT NULL,
																		  `currency` char(3) NOT NULL,
																		  `cur` decimal(15,4) NOT NULL,
																		  `pwl` decimal(15,4) NOT NULL,
																		  `cdk` decimal(15,4) NOT NULL,
																		  `sc` decimal(15,4) NOT NULL,
																		  `hla` decimal(15,4) NOT NULL,
																		  `subtotal` decimal(15,4) NOT NULL,
																		  PRIMARY KEY (`payment_methods_id`,`currency`)
																		) ENGINE=MyISAM;",
													"data" => ""
												);

add_new_tables ($add_new_tables, $DBTables);
// End of create cron_undelivered_report table

// Add new Download Center option into admin_files
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE admin_files_name = 'download_center.php'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ANB_UNDELIVERED_REPORT"] = array("insert" => " ('ANB_UNDELIVERED_REPORT', 'ANB Undelivered Report', " . $row_sql["admin_files_id"] . ", '1', 250)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of add new Download Center option into admin_files

// Insert new records into configuration_group table (for Captcha module)
$configuration_group_insert_sql = array();

$configuration_group_insert_sql["Captcha"] = array	(	"insert" => " ('Captcha', 'Captcha Configuration', 405, 1) ",
														"update" => " sort_order=405, visible='1' "
						   							);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");
// End of insert new records into configuration_group table (for Captcha module)

// Insert new records into configuration table (for Captcha module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Captcha'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["CAPTCHA_RECAPTCHA_PUBLIC_KEY"] = array("insert" => " ('reCaptcha Public API Key', 'CAPTCHA_RECAPTCHA_PUBLIC_KEY', '', 'reCaptcha Public API Key', ".$row_sql["configuration_group_id"].", 5, NULL, now(), NULL, NULL)" );
	$conf_insert_sql["CAPTCHA_RECAPTCHA_PRIVATE_KEY"] = array("insert" => " ('reCaptcha Private API Key', 'CAPTCHA_RECAPTCHA_PRIVATE_KEY', '', 'reCaptcha Private API Key', ".$row_sql["configuration_group_id"].", 10, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Captcha module)
?>