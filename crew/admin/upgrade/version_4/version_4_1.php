<?php

// Define top_up_process_flag as index key in orders_top_up table
add_index_key ('orders_top_up', 'index_top_up_process_flag', 'index', 'top_up_process_flag', $DBTables);
// End of define top_up_process_flag as index key in orders_top_up table

// Define categories_id as index key in game_language_to_categories table
add_index_key ('game_language_to_categories', 'index_categories_id', 'index', 'categories_id', $DBTables);
// End of define categories_id as index key in game_language_to_categories table

// Define categories_id as index key in game_platform_to_categories table
add_index_key ('game_platform_to_categories', 'index_categories_id', 'index', 'categories_id', $DBTables);
// End of define categories_id as index key in game_platform_to_categories table

// Define categories_id as index key in game_genre_to_categories table
add_index_key ('game_genre_to_categories', 'index_categories_id', 'index', 'categories_id', $DBTables);
// End of define categories_id as index key in game_genre_to_categories table

// Define categories_id as index key in game_esrb_to_categories table
add_index_key ('game_esrb_to_categories', 'index_categories_id', 'index', 'categories_id', $DBTables);
// End of define categories_id as index key in game_esrb_to_categories table

// Wilson
$existing_po_suppliers_fields = get_table_fields('po_suppliers');
$existing_purchase_orders_fields = get_table_fields('purchase_orders');
$existing_store_account_history_fields = get_table_fields('store_account_history');

// Insert new fields
$add_new_field = array();

// Insert new fields into `po_suppliers` table
$add_new_field['po_suppliers'] = array (	array (	"field_name" => "po_days_pay_wsc",
													"field_attr" => "INT(11) DEFAULT '0' NOT NULL",
													"add_after" => 'po_payment_term'
											)
									);
// End of insert new fields into `po_suppliers` table

// Insert new fields into `purchase_orders` table
$add_new_field['purchase_orders'] = array (	array (	"field_name" => "payment_days_pay_wsc",
													"field_attr" => "INT(11) DEFAULT '0' NOT NULL",
													"add_after" => 'payment_term'
											)
									);
// End of insert new fields into `purchase_orders` table

// Insert new fields into `store_account_history` table
$add_new_field['store_account_history'] = array (	array (	"field_name" => "store_account_history_account_type",
															"field_attr" => "CHAR(5) DEFAULT 'WSC' NOT NULL",
															"add_after" => 'store_account_history_currency'
													)
											);
// End of insert new fields into `store_account_history` table
	
add_field($add_new_field);
// End of insert new fields

if (!in_array('store_account_history_account_type', $existing_store_account_history_fields)) {
	// Change history data for PO Suppliers
	$store_account_history_update_sql = "	UPDATE store_account_history
											SET store_account_history_account_type = 'POWSC'
											WHERE user_role = 'customers' 
											AND user_id IN (SELECT DISTINCT po_suppliers_id FROM po_suppliers)";
	tep_db_query($store_account_history_update_sql);
	// End of change history data for PO Suppliers
}

if (!in_array('payment_days_pay_wsc', $existing_purchase_orders_fields)) {
	// Copy old day-term value to Actual Credit WSC field
	$purchase_orders_update_sql = "	UPDATE purchase_orders
									SET payment_days_pay_wsc = payment_term";
	tep_db_query($purchase_orders_update_sql);
	// End of Copy old day-term value to Actual Credit WSC field
}

if (!in_array('po_days_pay_wsc', $existing_po_suppliers_fields)) {
	// Copy old day-term value to Actual Credit WSC field
	$po_suppliers_update_sql = "UPDATE po_suppliers
								SET po_days_pay_wsc = po_payment_term";
	tep_db_query($po_suppliers_update_sql);
	// End of Copy old day-term value to Actual Credit WSC field
}
?>