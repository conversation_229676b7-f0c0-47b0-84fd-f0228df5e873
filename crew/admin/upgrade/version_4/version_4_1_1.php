<?php
$existing_latest_news_description_fields = get_table_fields('latest_news_description');

// Insert new fields
$add_new_field = array();

// Insert new fields into `latest_news_description` table
$add_new_field['latest_news_description'] = array (	array (	"field_name" => "is_default",
                                                            "field_attr" => "TINYINT(1) UNSIGNED DEFAULT '0' NOT NULL",
                                                            "add_after" => 'content'
											)
									);

add_field($add_new_field);
// End of insert new fields into `latest_news_description` table

if (!in_array('is_default', $existing_latest_news_description_fields)) {
    $latest_news_id_select_sql = "  SELECT news_id, COUNT(news_id) AS total_rec, language_id
                                    FROM " . TABLE_LATEST_NEWS_DESCRIPTION . "
                                    GROUP BY news_id";
    $latest_news_id_result_sql = tep_db_query($latest_news_id_select_sql);
    
    while($latest_news_row = tep_db_fetch_array($latest_news_id_result_sql)) {
        if ($latest_news_row['total_rec'] > 1) {
            $update_single_news_default_lang = "UPDATE " . TABLE_LATEST_NEWS_DESCRIPTION . " 
                                                SET is_default = 1
                                                WHERE news_id = '" . $latest_news_row['news_id'] . "'
                                                    AND language_id = 1";
            tep_db_query($update_single_news_default_lang);
        } else {
            $update_single_news_default_lang = "UPDATE " . TABLE_LATEST_NEWS_DESCRIPTION . " 
                                                SET is_default = 1
                                                WHERE news_id = '" . $latest_news_row['news_id'] . "'
                                                    AND language_id = '" . $latest_news_row['language_id'] . "'";
            tep_db_query($update_single_news_default_lang);
        }
    }
}
?>
