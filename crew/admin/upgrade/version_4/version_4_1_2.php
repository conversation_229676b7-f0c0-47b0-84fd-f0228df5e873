﻿<?
// Wilson
$existing_custom_product_vault_fields = get_table_fields('custom_product_vault');
$existing_custom_products_code_fields = get_table_fields('custom_products_code');

$add_new_field = array();
// Insert new fields into `custom_product_vault` table
$add_new_field['custom_product_vault'] = array (	array (	"field_name" => "purchase_orders_id",
															"field_attr" => "INT(11) UNSIGNED",
															"add_after" => ''
													)
											);
// End of insert new fields into `custom_product_vault` table

// Insert new fields into `custom_products_code` table
$add_new_field['custom_products_code'] = array (	array (	"field_name" => "purchase_orders_id",
															"field_attr" => "INT(11) UNSIGNED",
															"add_after" => ''
													)
											);
// End of insert new fields into `custom_products_code` table

// Insert new fields into `po_suppliers` table
$add_new_field['po_suppliers'] = array (	array (	"field_name" => "po_supplier_po_ref_year",
													"field_attr" => "INT UNSIGNED DEFAULT '0' NOT NULL",
													"add_after" => 'po_supplier_code'
												),
											array (	"field_name" => "po_supplier_po_ref_counter",
													"field_attr" => "INT UNSIGNED DEFAULT '0' NOT NULL",
													"add_after" => 'po_supplier_po_ref_year'
												)
									);
// End of insert new fields into `po_suppliers` table

add_field($add_new_field);

add_index_key ('custom_product_vault', 'index_purchase_orders_id', 'index', 'purchase_orders_id', $DBTables);
add_index_key ('custom_products_code', 'index_purchase_orders_id', 'index', 'purchase_orders_id', $DBTables);

if (!in_array('purchase_orders_id', $existing_custom_product_vault_fields)) {
	// update purchase order id
	$existing_ref_id_select_sql = "	SELECT po.purchase_orders_id, cpv.products_vault_id, cpv.purchase_orders_ref_id
									FROM purchase_orders AS po
									INNER JOIN custom_product_vault AS cpv
										ON (po.purchase_orders_ref_id = cpv.purchase_orders_ref_id AND cpv.purchase_orders_ref_id <> '')";
	$existing_ref_id_result_sql = tep_db_query($existing_ref_id_select_sql);
	while ($existing_ref_row = tep_db_fetch_array($existing_ref_id_result_sql)) {
		$vault_update_sql = "	UPDATE custom_product_vault
								SET purchase_orders_id = '".$existing_ref_row['purchase_orders_id']."'
								WHERE products_vault_id = '".$existing_ref_row['products_vault_id']."'";
		tep_db_query($vault_update_sql);
	}
	// End of update purchase order id
}

if (!in_array('purchase_orders_id', $existing_custom_products_code_fields)) {
	// update purchase order id
	$existing_ref_id_select_sql = "	SELECT po.purchase_orders_id, cpc.custom_products_code_id, cpc.purchase_orders_ref_id
									FROM purchase_orders AS po
									INNER JOIN custom_products_code AS cpc
										ON (po.purchase_orders_ref_id = cpc.purchase_orders_ref_id AND cpc.purchase_orders_ref_id <> '')";
	$existing_ref_id_result_sql = tep_db_query($existing_ref_id_select_sql);
	while ($existing_ref_row = tep_db_fetch_array($existing_ref_id_result_sql)) {
		$code_update_sql = "	UPDATE custom_products_code
								SET purchase_orders_id = '".$existing_ref_row['purchase_orders_id']."'
								WHERE custom_products_code_id = '".$existing_ref_row['custom_products_code_id']."'";
		tep_db_query($code_update_sql);
	}
	// End of update purchase order id
}


// Insert new records into api_report table (for permission on Report Direct Top Up Transaction)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='api_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["API_DTU_REPOST_TRANSACTION"] = array("insert" => " ('API_DTU_REPOST_TRANSACTION', 'DTU repost transaction', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of Insert new records into api_report table (for permission on Report Direct Top Up Transaction)


// Insert new fiels into orders_top_up_remark table (for permission on Report Direct Top Up Transaction)
$add_new_field = array();
$add_new_field['orders_top_up_remark'] = array (array (	"field_name" => "changed_by",
														"field_attr" => " varchar(128) default 'system' ",
														"add_after" => "remark"
												)
											);
add_field($add_new_field);
// End of Insert new fiels into orders_top_up_remark table (for permission on Report Direct Top Up Transaction)
?>