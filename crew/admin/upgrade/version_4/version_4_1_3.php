<?php
/*
	$Id: version_4_1_3.php,v 1.1 2011/08/02 08:23:24 weichen Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2009 Dynamic Podium
	
  	Released under the GNU General Public License
*/

// Insert new records into payment_methods_types table
$payment_methods_types_insert_sql = array();

$payment_methods_types_insert_sql["AUTOMATED TELLER MACHINE (ATM)"] = array("insert" => " ('AUTOMATED TELLER MACHINE (ATM)', 'RECEIVE', '0', 'SC', '550')");

insert_new_records('payment_methods_types', "payment_methods_types_name", $payment_methods_types_insert_sql, $DBTables, "(`payment_methods_types_name`, `payment_methods_types_mode`, `payment_methods_types_system_define`, `payment_methods_types_sites`, `payment_methods_types_sort_order`)");

$new_type_id = tep_db_insert_id();

tep_db_query("INSERT INTO payment_methods_types_description (payment_methods_types_id, languages_id, payment_methods_types_description) VALUES (".$new_type_id.", '1', 'Automated Teller Machine (ATM)'), (".$new_type_id.", '2', 'Automated Teller Machine (ATM)');");


$payment_methods_types_insert_sql = array();

$payment_methods_types_insert_sql["CASH DEPOSIT MACHINE (CDM)"] = array("insert" => " ('CASH DEPOSIT MACHINE (CDM)', 'RECEIVE', '0', 'SC', '600')");

insert_new_records('payment_methods_types', "payment_methods_types_name", $payment_methods_types_insert_sql, $DBTables, "(`payment_methods_types_name`, `payment_methods_types_mode`, `payment_methods_types_system_define`, `payment_methods_types_sites`, `payment_methods_types_sort_order`)");

$new_type_id = tep_db_insert_id();

tep_db_query("INSERT INTO payment_methods_types_description (payment_methods_types_id, languages_id, payment_methods_types_description) VALUES (".$new_type_id.", '1', 'Cash Deposit Machine (CDM)'), (".$new_type_id.", '2', 'Cash Deposit Machine (CDM)');");
// End of insert new records into payment_methods_types table
?>