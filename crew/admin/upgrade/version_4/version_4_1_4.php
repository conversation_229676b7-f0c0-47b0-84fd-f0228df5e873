<?
$add_new_field = array();
// Insert new fields into `purchase_orders` table
$add_new_field['purchase_orders'] = array (	array (	"field_name" => "purchase_orders_bankcharges_included",
													"field_attr" => "DECIMAL(13,8)",
													"add_after" => 'purchase_orders_paid_currency'
											),
											array (	"field_name" => "purchase_orders_bankcharges_refunded",
													"field_attr" => "DECIMAL(13,8)",
													"add_after" => 'purchase_orders_bankcharges_included'
											),
											array (	"field_name" => "purchase_orders_gst_currency",
													"field_attr" => "CHAR(3)",
													"add_after" => 'purchase_orders_bankcharges_refunded'
											),
											array (	"field_name" => "purchase_orders_gst_value",
													"field_attr" => "DECIMAL(6,2)",
													"add_after" => 'purchase_orders_gst_currency'
											),
											array (	"field_name" => "purchase_orders_gst_amount",
													"field_attr" => "DECIMAL(13,8)",
													"add_after" => 'purchase_orders_gst_value'
											),
											array (	"field_name" => "purchase_orders_gst_refunded",
													"field_attr" => "DECIMAL(13,8)",
													"add_after" => 'purchase_orders_gst_amount'
											)
									);
// End of insert new fields into `purchase_orders` table

add_field($add_new_field);

$delete_field = array();

// delete purchase_orders_ref_id field from custom_products_code table
$delete_field['custom_products_code'] = array  ( array( "field_name" => "purchase_orders_ref_id") );
// End of delete purchase_orders_ref_id field from custom_products_code table

// delete purchase_orders_ref_id field from custom_product_vault table
$delete_field['custom_product_vault'] = array  ( array( "field_name" => "purchase_orders_ref_id") );
// End of delete purchase_orders_ref_id field from custom_product_vault table

delete_field($delete_field);

// Create new table
$add_new_tables = array();

$add_new_tables["po_company"] = array (	"structure" => "CREATE TABLE `po_company` (
															`po_company_id` int(11) unsigned NOT NULL auto_increment,
															`po_company_code` varchar(2) NOT NULL default '',
															`po_company_name` varchar(32) NOT NULL default '',
															`po_company_contact_name` varchar(64) NOT NULL default '',
															`po_company_street_address` varchar(64) NOT NULL default '',
															`po_company_suburb` varchar(32) NOT NULL default '',
															`po_company_city` varchar(32) NOT NULL default '',
															`po_company_postcode` varchar(10) NOT NULL default '',
															`po_company_state` varchar(32) NOT NULL default '',
															`po_company_country_id` int(11) default '0',
															`po_company_zone_id` int(11) default '0',
															`po_company_format_id` int(5) NOT NULL default '0',
															`po_company_telephone` varchar(32) NOT NULL default '',
															`po_company_fax` varchar(32) NOT NULL default '',
															`po_company_invoice_footer` text,
															`po_company_gst_percentage` decimal(6,2) NOT NULL default '0.00000000',
															PRIMARY KEY  (`po_company_id`),
															KEY `po_company_code` (`po_company_code`)
														) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
											"data" => "	INSERT INTO `po_company` (`po_company_code`, `po_company_name`, `po_company_contact_name`, `po_company_street_address`, `po_company_suburb`, `po_company_city`, `po_company_postcode`, `po_company_state`, `po_company_country_id`, `po_company_zone_id`, `po_company_format_id`, `po_company_telephone`, `po_company_fax`, `po_company_invoice_footer`, `po_company_gst_percentage`) 
														VALUES ('MY', 'OffGamers Sdn Bhd', 'OffGamers Sdn Bhd', '2-B, Jalan Pasar', 'Pudu', 'Kuala Lumpur', '55100', '', '129', '558', '1', '+603-9223 4673', '+603-9222 3135', 'OffGamers Sdn Bhd [657868-X] | 2-B, Jalan Pasar, Pudu, 55100 Kuala Lumpur, Malaysia. | Tel: +603-9223 4673 | Fax: +603-9222 3135 | Customer Care: +603-9222 6654 | Store: www.offgamers.com', '0'),
																('SG', 'OffGamers Global Pte Ltd', 'OffGamers Global Pte Ltd', '111, North Brigde Road', '#20-02 Peninsula Plaza', '', '1790', '', '188', '0', '4', '+603-9223 4673', '+603-9222 3135', 'OffGamers Global Pte Ltd [201021120K] | 111, North Brigde Road, #20-02 Peninsula Plaza, Singapore 1790 | Tel: +603-9223 4673 | Fax: +603-9222 3135 | Store: www.offgamers.com', '7'),
																('HK', 'OffGamers Limited', 'OffGamers Limited', 'Room 813, Hollywood Plaza', '610 Nathan Road', '', '', '', '96', '0', '1', '+603-9223 4673', '+603-9222 3135', 'OffGamers Limited [1024676] | Room 813, Hollywood Plaza, 610 Nathan Road, Hong Kong. | Tel: +852-8226 9198 | Fax: +852-3585 1118 | Store: www.offgamers.com', '0');" );

add_new_tables ($add_new_tables, $DBTables);
// End of create new table

// Insert new records into admin_files table (for PO)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='purchase_orders.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["po_company.php"] = array("insert" => " ('po_company.php', 0, '".$row_sql['admin_files_id']."', '1') ");
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}
// End of Insert new records into admin_files table (for PO)

// Insert new records into configuration table (for Credit Notes Manual Action Notify Email)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Purchase Order Supplier'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["CREDIT_NOTES_MANUAL_ACTION_NOTIFY_EMAIL"] = array("insert" => " ('Credit Notes Manual Action Notify Email', 'CREDIT_NOTES_MANUAL_ACTION_NOTIFY_EMAIL', '', 'Email address to which the email will be send to when there is manual action performed in credit notes.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 16, NULL, now(), NULL, '')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Credit Notes Manual Action Notify Email)

?>