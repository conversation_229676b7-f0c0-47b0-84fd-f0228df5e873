﻿<?php
// Create mobile_customers_basket table
$add_new_tables["mobile_customers_basket"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `mobile_customers_basket` (
																		  `mobile_customers_basket_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,	
																		  `cart_id` int(5) NOT NULL,
																		  `customers_id` int(11) NOT NULL,
																		  `products_id` int(11) NOT NULL,
																		  `customers_basket_quantity` int(2) NOT NULL DEFAULT '0',
																		  `customers_basket_date_added` datetime NOT NULL,
																		  `products_categories_id` int(11) NOT NULL,
																		  `customers_basket_custom_key` varchar(20) NOT NULL,
																		  `customers_basket_custom_value` text,
																		  PRIMARY KEY (`mobile_customers_basket_id`),
																		  KEY `index_cart_id` (`cart_id`),
																		  KEY `index_customers_id` (`customers_id`)
																		) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
														"data" => "" );
add_new_tables ($add_new_tables, $DBTables);
// End of create mobile_customers_basket table



// Insert Mobile Site Code
$select_sql = "	SELECT site_id 
				FROM " . TABLE_SITE_CODE . "
				WHERE site_id = '4'" ;
$result_sql = tep_db_query($select_sql);
if (!tep_db_num_rows($result_sql)) {
	$site_code_data_sql = array (	'site_id' => 4,
									'site_name' => 'Mobile',
									'site_has_buyback' => 0,
									'admin_groups_id' => '1',
									'buyback_admin_groups_id' => ''
								);
	tep_db_perform(TABLE_SITE_CODE, $site_code_data_sql);
}
// End of Mobile Site Code



// Insert new records into configuration_group table (for Mobile Store configuration)
$configuration_group_insert_sql = array();
$configuration_group_insert_sql["Mobile Store"] = array	("insert" => " ('Mobile Store', 'Mobile Store setting', '400', '1' )");

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");

$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Mobile Store'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["MOBILE_GOOGLE_ANALYTICS_ACCOUNT_ID"] = array("insert" => " ('Google Analytics Account ID', 'MOBILE_GOOGLE_ANALYTICS_ACCOUNT_ID', '', 'Your Google Analytics Account ID', ".$row_sql["configuration_group_id"].", 5, NULL, NOW(), NULL, NULL)" );
	$conf_insert_sql["MOBILE_CART_CHECKOUT"] = array("insert" => " ('Allow Checkout', 'MOBILE_CART_CHECKOUT', 'true', 'Allow Customer place order via Mobile platform<br>(true=on false=off)', ".$row_sql["configuration_group_id"].", 10, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	$conf_insert_sql["MOBILE_DOWN_FOR_MAINTENANCE"] = array("insert" => " ('Down for Maintenance', 'MOBILE_DOWN_FOR_MAINTENANCE', 'false', 'Mobile Store down for maintenance<br>(true=on false=off)', ".$row_sql["configuration_group_id"].", 15, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of Insert new records into configuration_group table (for Mobile Store configuration)

?>