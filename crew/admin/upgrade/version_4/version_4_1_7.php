<?php
$existing_customers_info_fields = get_table_fields('customers_info');

// Insert new fields into `customers_info` table
$add_new_field = array();

$add_new_field['customers_info'] = array (	array (	"field_name" => "customer_info_account_dormant",
													"field_attr" => "TINYINT(1) NULL",
													"add_after" => 'customers_info_date_of_last_logon'
												)
											);

add_field($add_new_field);
// End of insert new fields

if (!in_array('customer_info_account_dormant', $existing_customers_info_fields)) {
    //Filter all domant accounts
    $update_sql = "	UPDATE " . TABLE_CUSTOMERS_INFO . "
                    SET customer_info_account_dormant=1
                    WHERE customers_info_date_of_last_logon < DATE_SUB(curdate(), INTERVAL 90 DAY) OR customers_info_date_of_last_logon IS NULL" ;
    tep_db_query($update_sql);
    //End of filter all domant accounts
}
?>