<?php
// make search_value field as FULLTEXT
add_index_key ('categories_search', 'ft_search_value', 'fulltext', 'search_value', $DBTables);

// Create cron_mobile_orders tables
$add_new_tables = array();

$add_new_tables['cron_genesis_orders'] = array (	"structure" => "CREATE TABLE `cron_genesis_orders` (
                                                                      `orders_id` int(11) NOT NULL default '0',
                                                                      `flag` tinyint(1) NOT NULL default '0',
                                                                      `re_run` tinyint(1) default '0',
                                                                      `last_modified` datetime default '0000-00-00 00:00:00',
                                                                      PRIMARY KEY  (`orders_id`)
                                                                    ) TYPE=MyISAM;" ,
                                                    "data" => ""
                                                );
add_new_tables ($add_new_tables, $DBTables);
// End of create cron_mobile_orders tables

// Change field structure for serial_number in customers_info_verification table
$change_field_structure = array();

$change_field_structure['customers_info_verification'] = array (array (	"field_name" => "serial_number",
                                                                        "field_attr" => "varchar(12) default NULL"
                                                                    )
                                                            );
change_field_structure ($change_field_structure);
// End of change field structure for serial_number in customers_info_verification table

// Insert new schedule task
$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_genesis_orders.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_process_track_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_genesis_orders.php'
			                       );
	tep_db_perform('cron_process_track', $cron_process_track_array);
}
// End of insert new schedule task
?>
