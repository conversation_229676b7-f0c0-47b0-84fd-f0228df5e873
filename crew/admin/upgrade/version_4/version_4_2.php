<?php
$copy_surcharge = in_array('payment_fees_sc', $DBTables) ? false : true;

// Create payment_fees_sc tables
$add_new_tables = array();

$add_new_tables['payment_fees_sc'] = array (	"structure" => "CREATE TABLE `payment_fees_sc` (
                                                                  `payment_fees_id` int(11) UNSIGNED NOT NULL auto_increment,
                                                                  `payment_methods_id` int(11) UNSIGNED NOT NULL default '0',
                                                                  `payment_methods_currency_code` char(3) NOT NULL default 'USD',
                                                                  `payment_methods_mode` varchar(10) NOT NULL default 'SEND',
                                                                  `payment_fees_operator` char(2) NOT NULL default '0',
                                                                  `payment_fees_max` decimal(15,4) NOT NULL default '0.0000',
                                                                  `payment_fees_min` decimal(15,4) NOT NULL default '0.0000',
                                                                  `payment_fees_cost_value` decimal(8,4) NOT NULL default '0.0000',
                                                                  `payment_fees_cost_percent` decimal(6,2) NOT NULL default '0.00',
                                                                  `payment_fees_cost_percent_min` decimal(8,2) NOT NULL default '0.00',
                                                                  `payment_fees_cost_percent_max` decimal(8,2) NOT NULL default '0.00',
                                                                  `payment_fees_customers_groups_id` int(11) UNSIGNED NOT NULL default '1',
                                                                  `payment_fees_follow_group` int(11) UNSIGNED NOT NULL default '0',
                                                                  `currency_code` char(3) default NULL,
                                                                  PRIMARY KEY  (`payment_fees_id`),
                                                                  KEY `index_method_and_mode` (`payment_methods_id`,`payment_methods_mode`),
                                                                  KEY `index_customers_groups_id` (`payment_fees_customers_groups_id`)
                                                                ) Type=MyISAM DEFAULT CHARSET=utf8;" ,
                                                "data" => ""
                                            );
add_new_tables ($add_new_tables, $DBTables);
// End of create payment_fees_sc tables

if ($copy_surcharge) {
    $current_payment_fees_select_sql = "SELECT *
                                        FROM payment_fees
                                        WHERE payment_methods_mode = 'RECEIVE'
                                        ORDER BY payment_fees_id";
    $current_payment_fees_result_sql = tep_db_query($current_payment_fees_select_sql);
		
	while ($current_payment_fees_row = tep_db_fetch_array($current_payment_fees_result_sql)) {
        $sql_data_array = array('payment_methods_id' => $current_payment_fees_row['payment_methods_id'],
                                'payment_methods_currency_code' => $current_payment_fees_row['payment_methods_currency_code'],
                                'payment_methods_mode' => $current_payment_fees_row['payment_methods_mode'],
                                'payment_fees_operator' => $current_payment_fees_row['payment_fees_operator'],
                                'payment_fees_max' => $current_payment_fees_row['payment_fees_max'],
                                'payment_fees_min' => $current_payment_fees_row['payment_fees_min'],
                                'payment_fees_cost_value' => $current_payment_fees_row['payment_fees_cost_value'],
                                'payment_fees_cost_percent' => $current_payment_fees_row['payment_fees_cost_percent'],
                                'payment_fees_cost_percent_min' => $current_payment_fees_row['payment_fees_cost_percent_min'],
                                'payment_fees_cost_percent_max' => $current_payment_fees_row['payment_fees_cost_percent_max'],
                                'payment_fees_customers_groups_id' => $current_payment_fees_row['payment_fees_customers_groups_id'],
                                'payment_fees_follow_group' => $current_payment_fees_row['payment_fees_follow_group'],
                                'currency_code' => $current_payment_fees_row['currency_code']
                                );

        tep_db_perform('payment_fees_sc', $sql_data_array);
    }
}
?>