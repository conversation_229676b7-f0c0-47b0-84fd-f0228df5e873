<?php
// Insert new records into custom_products_type_child table (for In Game Items)
$custom_products_type_child_select_sql = "	SELECT custom_products_type_child_id 	
											FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
											WHERE custom_products_type_child_id = '12'";
$custom_products_type_child_result_sql = tep_db_query($custom_products_type_child_select_sql);
if (!tep_db_num_rows($custom_products_type_child_result_sql)) {
	$custom_products_type_child_data_array = array(	'custom_products_type_id' => '0',
													'custom_products_type_child_url' => 'in_game_items',
													'custom_products_type_child_name' => 'In Game Items',
													'display_status' => '1',
													'sort_order' => 2200
							                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, $custom_products_type_child_data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '12',
							'languages_id' => 1,
							'custom_products_type_child_name' => 'In Game Items'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '12',
							'languages_id' => 2,
							'custom_products_type_child_name' => '&#28216;&#25103;&#36947;&#20855;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '12',
							'languages_id' => 3,
							'custom_products_type_child_name' => '&#36938;&#25138;&#36947;&#20855;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
}
// End of insert new records into custom_products_type_child table (for In Game Items)
?>