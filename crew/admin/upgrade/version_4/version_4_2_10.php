<?php
// Create phone_verification_limit table
$add_new_tables = array();

$add_new_tables['phone_verification_limit'] = array (	"structure" => "CREATE TABLE `phone_verification_limit` (
                                                                      `telephone_number` varchar(32) NOT NULL,
                                                                      `counter` int(1) unsigned NOT NULL,
                                                                      `last_request_date` date NOT NULL,
                                                                      PRIMARY KEY (`telephone_number`),
                                                                      KEY `index_telephone_number` (`telephone_number`(6))
                                                                    ) Type=MyISAM DEFAULT CHARSET=utf8;" ,
                                                        "data" => ""
                                                    );

add_new_tables ($add_new_tables, $DBTables);
?>
