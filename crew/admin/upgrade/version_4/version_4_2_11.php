<?php
// Create customers_upkeep table
$add_new_tables = array();

$add_new_tables['customers_upkeep'] = array (   "structure" => "CREATE TABLE IF NOT EXISTS `customers_upkeep` (
                                                                  `customers_id` int(11) unsigned NOT NULL,
                                                                  `storage` text NOT NULL,
                                                                  PRIMARY KEY (`customers_id`)
                                                                ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                                "data" => ""
                                            );

$add_new_tables['authorized_token'] = array (   "structure" => "CREATE TABLE IF NOT EXISTS `authorized_token` (
                                                                  `auth_token` varchar(64) NOT NULL,
                                                                  `customers_id` int(11) unsigned NOT NULL,
                                                                  `created_datetime` datetime NOT NULL,
                                                                  PRIMARY KEY (`auth_token`)
                                                                ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                                "data" => ""
                                            );

add_new_tables ($add_new_tables, $DBTables);

?>
