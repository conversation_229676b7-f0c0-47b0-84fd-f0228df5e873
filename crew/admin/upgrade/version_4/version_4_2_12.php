<?php
// Create pin_request_log table
$add_new_tables = array();

$add_new_tables['pin_request'] = array (   "structure" => "CREATE TABLE `pin_request` (
                                                              `pin_request_id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
                                                              `products_id` int(11) unsigned NOT NULL DEFAULT 0,
                                                              `products_quantity_order` int(4) unsigned DEFAULT NULL,
                                                              `pin_request_qty` int(4) unsigned DEFAULT 1,
                                                              `pin_currency` char(3) DEFAULT NULL,
                                                              `pin_amount` decimal(15,2) unsigned NOT NULL DEFAULT '0.00',
                                                              `pin_module_trans_id` mediumint(8) unsigned NOT NULL DEFAULT 0,
                                                              `pin_request_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                                                              `pin_receive_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                                                              `pin_request_status` tinyint(1) NOT NULL DEFAULT '0',
                                                              `pin_request_message` varchar(64) NOT NULL DEFAULT '',
                                                              `pin_request_log` tinytext,
                                                              PRIMARY KEY (`pin_request_id`)
                                                            ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                            "data" => ""
                                        );

$add_new_tables['pin_request_info'] = array (   "structure" => "CREATE TABLE `pin_request_info` (
                                                                  `pin_request_id` mediumint(8) unsigned NOT NULL DEFAULT 0,
                                                                  `custom_products_code_id` int(11) unsigned NOT NULL DEFAULT 0,
                                                                  `pin_module_pin_id` mediumint(8) unsigned NOT NULL DEFAULT 0,
                                                                  PRIMARY KEY (`pin_request_id`, `custom_products_code_id`)
                                                                ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                                "data" => ""
                                            );

add_new_tables ($add_new_tables, $DBTables);

?>