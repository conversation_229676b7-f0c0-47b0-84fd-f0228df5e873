<?php
// Create gift_card_redemption table
$add_new_tables["gift_card_redemption"] = array (	"structure" => "CREATE TABLE `gift_card_redemption` (
                                                                      `gift_card_redemption_id` int(11) unsigned NOT NULL auto_increment,
                                                                      `customers_id` int(11) unsigned NOT NULL,
                                                                      `transaction_id` int(11) unsigned NOT NULL,
                                                                      `serial_number` varchar(18) NOT NULL,
                                                                      `pin_number` varchar(16) NOT NULL,
                                                                      `gift_card_deno` decimal(15,4) default '0.0000',
                                                                      `gift_card_currency_id` int(11) unsigned default NULL,
                                                                      `gift_card_currency_code` char(3) NOT NULL,
                                                                      `transaction_type` varchar(10) default NULL,
                                                                      `redeem_date` datetime NOT NULL,
                                                                      `redeem_ip` varchar(32) default '0',
                                                                      `issued_amount` decimal(15,4) NOT NULL,
                                                                      `issued_currency_id` int(11) unsigned default NULL,
                                                                      `issued_currency_code` char(3) NOT NULL,
                                                                      PRIMARY KEY  (`gift_card_redemption_id`)
                                                                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
														"data" => "" );
add_new_tables ($add_new_tables, $DBTables);
// End of create gift_card_redemption table

$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Mobile Store'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["MOBILE_SITE_URL"] = array("insert" => " ('Mobile Site URL', 'MOBILE_SITE_URL', 'http://m.offgamers.biz/page/mobile/', 'Mobile Site Landing Page', ".$row_sql["configuration_group_id"].", 5, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
?>
