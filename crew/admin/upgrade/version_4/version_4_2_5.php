<?php
$populate_customers_aft_groups = in_array('customers_aft_groups', $DBTables) ? false : true;

// Create customers_aft_groups table
$add_new_tables = array();

$add_new_tables['customers_aft_groups'] = array (	"structure" => "CREATE TABLE `customers_aft_groups` (
                                                                      `customers_aft_groups_id` int(11) UNSIGNED NOT NULL auto_increment,
                                                                      `customers_aft_groups_name` varchar(32) NOT NULL default '',
                                                                      `sort_order` int(5) UNSIGNED NOT NULL default '50000',
                                                                      PRIMARY KEY  (`customers_aft_groups_id`)
                                                                    ) Type=MyISAM DEFAULT CHARSET=utf8;" ,
                                                    "data" => ""
                                                );

$add_new_tables["cron_customer_aft_upgrade"] = array (	"structure" => "CREATE TABLE `cron_customer_aft_upgrade` (
																	  `cron_customer_upgrade_from` int(11) NOT NULL,
																	  `cron_customer_upgrade_to` int(11) NOT NULL,
																	  `cron_customer_last_process_date` datetime NOT NULL default '0000-00-00 00:00:00',
																	  `cron_customer_upgrade_in_action` tinyint(4) NOT NULL default '0',
																	  `cron_customer_upgrade_last_process_customer_id` int(11) NOT NULL,
																	  `cron_customer_upgrade_processed_count` int(11) NOT NULL DEFAULT '0',
																	  PRIMARY KEY  (`cron_customer_upgrade_from`,`cron_customer_upgrade_to`)
																	) Type=MyISAM DEFAULT CHARSET=utf8;" ,
                                                        "data" => "	INSERT INTO `cron_customer_aft_upgrade` VALUES
                                                                        (12, 3, '0000-00-00 00:00:00', 0, 0, 0),
                                                                        (3, 4, '0000-00-00 00:00:00', 0, 0, 0),
                                                                        (4, 5, '0000-00-00 00:00:00', 1, 0, 0);"
                                                    );

add_new_tables ($add_new_tables, $DBTables);
// End of create customers_aft_groups tables

if ($populate_customers_aft_groups) {
    $source_select_sql = "  SELECT customers_groups_id, customers_groups_name, sort_order
                            FROM customers_groups
                            WHERE 1
                            ORDER BY customers_groups_id";
    $source_result_sql = tep_db_query($source_select_sql);
    
    while ($source_row = tep_db_fetch_array($source_result_sql)) {
        $customer_aft_group_data_sql = array(   'customers_aft_groups_id' => $source_row['customers_groups_id'],
                                                'customers_aft_groups_name' => 'Trust Level ('.$source_row['customers_groups_name'].')',
                                                'sort_order' => $source_row['sort_order']
                                            );
        tep_db_perform('customers_aft_groups', $customer_aft_group_data_sql);
    }
    
    // Insert new fields into customers_groups_purchase_limit and customers tables
    $add_new_field = array();
    
    $add_new_field['customers_groups_purchase_limit'] = array (	array (	"field_name" => "customers_aft_groups_id",
                                                                        "field_attr" => "int(11) UNSIGNED NOT NULL default '0'",
                                                                        "add_after" => 'customers_groups_id'
                                                                )
                                                        );
    
    $add_new_field['customers'] = array (	array (	"field_name" => "customers_aft_groups_id",
                                                    "field_attr" => "int(11) UNSIGNED NOT NULL default '2'",
                                                    "add_after" => 'customers_groups_id'
                                            )
                                    );
    
    add_field($add_new_field);
    // End of insert new fields into customers_groups_purchase_limit and customers tables
    
    // Define customers_aft_groups_id as index key in customers_groups_purchase_limit and customers table
    add_index_key ('customers_groups_purchase_limit', 'index_aft_groups', 'index', 'customers_aft_groups_id', $DBTables);
    add_index_key ('customers', 'index_aft_groups', 'index', 'customers_aft_groups_id', $DBTables);
    // End of define customers_aft_groups_id as index key in customers_groups_purchase_limit and customers table
    
    tep_db_query('UPDATE customers_groups_purchase_limit SET customers_aft_groups_id=customers_groups_id WHERE 1');
    tep_db_query('UPDATE customers SET customers_aft_groups_id=customers_groups_id WHERE 1');
}

// Insert new upgrade record into cron_customer_upgrade table
$cron_customer_upgrade_insert_sql['2'] = array("insert" => " (2, 12, now(), 0, 0, 0)" );

insert_new_records(TABLE_CRON_CUSTOMER_UPGRADE, 'cron_customer_upgrade_from', $cron_customer_upgrade_insert_sql, $DBTables, "(cron_customer_upgrade_from, cron_customer_upgrade_to, cron_customer_last_process_date, cron_customer_upgrade_in_action, cron_customer_upgrade_last_process_customer_id, cron_customer_upgrade_processed_count)", 'cron_customer_upgrade_from="2" AND cron_customer_upgrade_to="12"');
// End of insert new upgrade record into cron_customer_upgrade table

// Insert new records into admin_files table (for AFT group permission control)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers_groups.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_GROUP_ADD_AFT_GROUP"] = array("insert" => " ('CUSTOMER_GROUP_ADD_AFT_GROUP', 'Add AFT Group', ".$row_sql["admin_files_id"].", '1', 50)" );
	$admin_files_actions_insert_sql["CUSTOMER_GROUP_EDIT_AFT_GROUP"] = array("insert" => " ('CUSTOMER_GROUP_EDIT_AFT_GROUP', 'Edit AFT Group', ".$row_sql["admin_files_id"].", '1', 60)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files table (for AFT group permission control)

$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_upgrade_aft_group.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_progress_task_data_sql = array(	'cron_process_track_in_action' => 0,
											'cron_process_track_start_date' => '2011-11-29 00:00:00',
											'cron_process_track_failed_attempt' => 0,
											'cron_process_track_filename' => 'cron_upgrade_aft_group.php'
										);
	tep_db_perform('cron_process_track', $cron_progress_task_data_sql);
}

// Show CDM and ATM in Instant purchase as well
tep_db_query("UPDATE payment_methods_types SET payment_methods_types_sites = 'MAIN,SC' WHERE payment_methods_types_id=8");
tep_db_query("UPDATE payment_methods_types SET payment_methods_types_sites = 'MAIN,SC' WHERE payment_methods_types_id=9");
// End of show CDM and ATM in Instant purchase as well
?>