<?php
// Define products_id as index key in top_up_info table
add_index_key ('top_up_info', 'index_products_id', 'index', 'products_id', $DBTables);
// End of define products_id as index key in top_up_info table

// Insert new records into custom_products_type_child table (for Game Download)
$custom_products_type_child_select_sql = "	SELECT custom_products_type_child_id 	
											FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
											WHERE custom_products_type_child_id = '13'";
$custom_products_type_child_result_sql = tep_db_query($custom_products_type_child_select_sql);
if (!tep_db_num_rows($custom_products_type_child_result_sql)) {
	$custom_products_type_child_data_array = array(	'custom_products_type_id' => '2',
													'custom_products_type_child_url' => 'game_download',
													'custom_products_type_child_name' => 'Game Download',
													'display_status' => '0',
													'sort_order' => 1110
							                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, $custom_products_type_child_data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '13',
							'languages_id' => 1,
							'custom_products_type_child_name' => 'Game Download'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '13',
							'languages_id' => 2,
							'custom_products_type_child_name' => '&#28216;&#25103;&#19979;&#36733;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '13',
							'languages_id' => 3,
							'custom_products_type_child_name' => '&#36938;&#25138;&#19979;&#36617;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
}
// End of insert new records into custom_products_type_child table (for Game Download)
?>