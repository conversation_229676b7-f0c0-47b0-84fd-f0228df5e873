<?php

// Add new Download Center option into admin_files
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE admin_files_name = 'download_center.php'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["DOWNLOAD_PG_COUNTRY_SALES"] = array("insert" => " ('DOWNLOAD_PG_COUNTRY_SALES', 'PG Country Sales', " . $row_sql["admin_files_id"] . ", '1', 300)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of add new Download Center option into admin_files

// Add new field customers_groups_extra_sc in customers_groups table
$add_new_field = array();

$add_new_field[TABLE_CUSTOMERS_GROUPS] = array (	array (	"field_name" => "customers_groups_extra_sc",
															"field_attr" => " decimal(8,2) UNSIGNED NOT NULL default '0.00' COMMENT 'In Percentage'",
															"add_after" => "customers_groups_payment_methods"
															)
													);

add_field($add_new_field);
// End of add new field customers_groups_extra_sc in customers_groups table

// Insert new records into admin_files table (for Extra SC)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers_groups.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["CUSTOMER_GROUP_EXTRA_SC"] = array("insert" => " ('CUSTOMER_GROUP_EXTRA_SC', 'Extra SC', ".$row_sql["admin_files_id"].", '1', 35)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files table (for Extra SC)

// Remove useless configuration key(s)
$conf_delete_sql = array();

$conf_delete_sql['SC_CONFIG_EXTRA_PROMO'] = array(	"unique" => "1" );
$conf_delete_sql['SC_CONFIG_EXTRA_PROMO_CUST_GRP'] = array(	"unique" => "1" );

delete_records(TABLE_CONFIGURATION, "configuration_key", $conf_delete_sql, $DBTables);
// End of remove useless configuration key(s)

tep_db_query("UPDATE payment_methods_types_description SET payment_methods_types_description = 'Automated Teller Machine (ATM) / 3rd Party Bank Transfer' WHERE payment_methods_types_id = 8");
?>