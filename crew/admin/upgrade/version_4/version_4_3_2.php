<?php
// Delete phone_verification_limit table
$delete_tables_array = array();
$delete_tables_array = array('phone_verification_limit');
delete_tables ($delete_tables_array, $DBTables);
// End of delete phone_verification_limit table

$add_new_tables = array();

// Create customers_setting tables
$add_new_tables["customers_setting"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `customers_setting` (
                                                                  `customers_id` int(11) unsigned NOT NULL,
                                                                  `customers_setting_key` varchar(64) NOT NULL,
                                                                  `customers_setting_value` text,
                                                                  `updated_datetime` datetime NOT NULL,
                                                                  PRIMARY KEY (`customers_id`,`customers_setting_key`),
                                                                  KEY `index_customers_id` (`customers_id`)
                                                                ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                                "data" => ""
											);
// End of create customers_setting tables

// Create cron_aws_cf tables
$add_new_tables["cron_aws_cf"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `cron_aws_cf` (
                                                              `cf_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                                              `bucket_key` varchar(45) NOT NULL,
                                                              `filepath` varchar(125) NOT NULL,
                                                              `filename` varchar(125) NOT NULL,
                                                              `created_datetime` datetime NOT NULL,
                                                              `status` char(1) NOT NULL,
                                                              PRIMARY KEY (`cf_id`)
                                                            ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1;" ,
                                                "data" => ""
											);
// End of create cron_aws_cf tables

add_new_tables ($add_new_tables, $DBTables);

// End of create customers_setting tables

// Insert new schedule task
$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_aws_cloudfront.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_process_track_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_aws_cloudfront.php'
			                       );
	tep_db_perform('cron_process_track', $cron_process_track_array);
}
?>