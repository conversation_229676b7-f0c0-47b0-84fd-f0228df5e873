<?php
// Add new Download Center option into admin_files
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE admin_files_name = 'download_center.php'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["DOWNLOAD_ROOT_PRODUCT_SALES"] = array("insert" => " ('DOWNLOAD_ROOT_PRODUCT_SALES', 'Root Product Sales', " . $row_sql["admin_files_id"] . ", '1', 350)" );
    $admin_files_actions_insert_sql["DOWNLOAD_PLAT_PLUS_REPORT"] = array("insert" => " ('DOWNLOAD_PLAT_PLUS_REPORT', 'Plat Plus Report', " . $row_sql["admin_files_id"] . ", '1', 400)" );
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of add new Download Center option into admin_files
?>