<?php
// Update custom_products_type_child_url and custom_products_type_child_name in custom_products_type_child table
$custom_product_update_sql = array();

$custom_product_update_sql['custom_products_type_child'] = array(	array(	"field_name" => "custom_products_type_child_name",
                                                                            "update" => " custom_products_type_child_name = 'Mobile Reloads', custom_products_type_child_url='mobile_reloads', sort_order='6000'",
                                                                            "where_str" => " custom_products_type_child_id=11"
                                                                            )
                                                             );

advance_update_records($custom_product_update_sql, $DBTables);
// End of update custom_products_type_child_url and custom_products_type_child_name in custom_products_type_child table

// Update custom_products_type_child_name in custom_products_type_child_lang table
$custom_product_update_sql = array();

$custom_product_update_sql['custom_products_type_child_lang'] = array(	array(	"field_name" => "custom_products_type_child_name",
                                                                                "update" => " custom_products_type_child_name = 'Mobile Reloads' ",
                                                                                "where_str" => " custom_products_type_child_id=11 AND languages_id=1 "
                                                                                ),
                                                                        array(	"field_name" => "custom_products_type_child_name",
                                                                                "update" => " custom_products_type_child_name = '&#31227;&#21160;&#20805;&#20540;' ",
                                                                                "where_str" => " custom_products_type_child_id=11 AND languages_id=2 "
                                                                                ),
                                                                        array(	"field_name" => "custom_products_type_child_name",
                                                                                "update" => " custom_products_type_child_name = '&#31227;&#21205;&#20805;&#20540;' ",
                                                                                "where_str" => " custom_products_type_child_id=11 AND languages_id=3 "
                                                                                )
                                                                     );

advance_update_records($custom_product_update_sql, $DBTables);
// End of update custom_products_type_child_name in custom_products_type_child_lang table

// Insert new records into custom_products_type_child table (for Broadband Subscription)
$custom_products_type_child_select_sql = "	SELECT custom_products_type_child_id 	
											FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
											WHERE custom_products_type_child_id = '14'";
$custom_products_type_child_result_sql = tep_db_query($custom_products_type_child_select_sql);
if (!tep_db_num_rows($custom_products_type_child_result_sql)) {
	$custom_products_type_child_data_array = array(	'custom_products_type_id' => '1',
													'custom_products_type_child_url' => 'broadband_subscription',
													'custom_products_type_child_name' => 'Broadband Subscription',
													'display_status' => '0',
													'sort_order' => 6500
							                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, $custom_products_type_child_data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '14',
							'languages_id' => 1,
							'custom_products_type_child_name' => 'Broadband Subscription'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '14',
							'languages_id' => 2,
							'custom_products_type_child_name' => '&#23485;&#24102;&#22871;&#39184;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '14',
							'languages_id' => 3,
							'custom_products_type_child_name' => '&#23532;&#24118;&#22871;&#39184;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
}
// End of insert new records into custom_products_type_child table (for Broadband Subscription)
?>