<?php

// Change field structure for CDK PO tables due to IDR Currency
$change_field_structure = array();

$change_field_structure['purchase_orders'] = array (array (	"field_name" => "purchase_orders_paid_amount",
                                                            "field_attr" => " decimal(20,8) UNSIGNED default '0.00000000' "
                                                        ),
                                                    array (	"field_name" => "suggested_currency_value",
                                                            "field_attr" => " decimal(15,8) UNSIGNED default NULL "
                                                        ),
                                                    array (	"field_name" => "confirmed_currency_value",
                                                            "field_attr" => " decimal(15,8) UNSIGNED default NULL "
                                                        ),
                                                    array (	"field_name" => "currency_usd_value",
                                                            "field_attr" => " decimal(15,8) UNSIGNED default NULL "
                                                        ),
                                                    array (	"field_name" => "purchase_orders_bankcharges_included",
                                                            "field_attr" => " decimal(20,8) UNSIGNED DEFAULT NULL "
                                                        ),
                                                    array (	"field_name" => "purchase_orders_bankcharges_refunded",
                                                            "field_attr" => " decimal(20,8) UNSIGNED DEFAULT NULL "
                                                        ),
                                                    array (	"field_name" => "purchase_orders_gst_amount",
                                                            "field_attr" => " decimal(20,8) UNSIGNED DEFAULT NULL "
                                                        ),
                                                    array (	"field_name" => "purchase_orders_gst_refunded",
                                                            "field_attr" => " decimal(20,8) UNSIGNED DEFAULT NULL "
                                                        )
                                                );

$change_field_structure['purchase_orders_products'] = array (array ("field_name" => "products_unit_price_value",
                                                                    "field_attr" => " decimal(20,8) UNSIGNED NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_unit_price",
                                                                    "field_attr" => " decimal(20,8) UNSIGNED NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_selling_price",
                                                                    "field_attr" => " decimal(20,8) UNSIGNED NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_subtotal",
                                                                    "field_attr" => " decimal(20,8) UNSIGNED NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_usd_unit_price",
                                                                    "field_attr" => " decimal(20,8) UNSIGNED NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_usd_selling_price",
                                                                    "field_attr" => " decimal(20,8) UNSIGNED NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_usd_subtotal",
                                                                    "field_attr" => " decimal(20,8) UNSIGNED NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_tax",
                                                                    "field_attr" => " decimal(20,8) UNSIGNED NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_good_delivered_price",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_good_delivered_usd_price",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_canceled_price",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_canceled_usd_price",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_refund_price",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_refund_usd_price",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_credit_note_price",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "products_credit_note_usd_price",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                )
                                                );

$change_field_structure['store_credit_note_history'] = array (array (	"field_name" => "store_credit_note_history_debit_amount",
                                                                        "field_attr" => " decimal(20,8) UNSIGNED default NULL "
                                                                    ),
                                                                array (	"field_name" => "store_credit_note_history_credit_amount",
                                                                        "field_attr" => " decimal(20,8) UNSIGNED default NULL "
                                                                    ),
                                                                array (	"field_name" => "store_credit_note_history_after_balance",
                                                                        "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                    )
                                                            );

$change_field_structure['purchase_orders_total'] = array (array (	"field_name" => "usd_value",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                ),
                                                            array (	"field_name" => "value",
                                                                    "field_attr" => " decimal(20,8) NOT NULL default '0.00000000' "
                                                                )
                                                        );

change_field_structure ($change_field_structure);
// End of change field structure for CDK PO tables due to IDR Currency
?>