<?php
$add_new_tables = array();

// Create sso_token tables
$add_new_tables["sso_token"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `sso_token` (
                                                          `sso_token` varchar(36) NOT NULL COMMENT 'token for single sign-on verification',
                                                          `customers_id` int(11) unsigned NOT NULL,
                                                          `datetime` datetime NOT NULL,
                                                          PRIMARY KEY  (`sso_token`,`customers_id`)
                                                        ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                        "data" => ""
                                        );
// End of create sso_token tables

add_new_tables ($add_new_tables, $DBTables);
?>
