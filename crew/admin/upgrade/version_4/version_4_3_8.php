<?php

// Add new field products_dtu_extra_info_1, products_dtu_extra_info_2, products_dtu_extra_info_3 in products_description table
$add_new_field = array();

$add_new_field[TABLE_PRODUCTS_DESCRIPTION] = array (	array (	"field_name" => "products_dtu_extra_info_1",
                                                                "field_attr" => "VARCHAR( 32 ) NOT NULL COMMENT 'DTU deno label'",
                                                                "add_after" => ""
                                                                ),
                                                        array (	"field_name" => "products_dtu_extra_info_2",
                                                                "field_attr" => "VARCHAR( 32 ) NOT NULL COMMENT 'DTU Deno calculation'",
                                                                "add_after" => ""
                                                                ),
                                                        array (	"field_name" => "products_dtu_extra_info_3",
                                                                "field_attr" => "VARCHAR( 32 ) NOT NULL COMMENT 'DTU deno label'",
                                                                "add_after" => ""
                                                                )
													);

add_field($add_new_field);
// End of add new field products_dtu_extra_info_1, products_dtu_extra_info_2, products_dtu_extra_info_3 in products_description table
?>