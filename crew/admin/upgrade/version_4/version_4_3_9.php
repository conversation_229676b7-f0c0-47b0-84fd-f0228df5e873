<?php
// <PERSON>
// Insert new fields into customers_setting table
$add_new_field['customers_setting'] = array (	array (	"field_name" => "created_datetime",
                                                        "field_attr" => " datetime NOT NULL ",
                                                        "add_after" => 'customers_setting_value'
                                        )
                                );

add_field($add_new_field);
// End of insert new fields into customers_setting table

// Create new table
$add_new_tables = array();

$add_new_tables["customers_otp"] = array (	"structure" => "CREATE TABLE `customers_otp` (
                                                                `customers_id` int(11) unsigned NOT NULL DEFAULT '0',
                                                                `customers_otp_digit` varchar(8) default NULL,
                                                                `customers_otp_request_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                                                                PRIMARY KEY  (`customers_id`)
                                                            ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                            "data" => "" );


$add_new_tables["sms_report"] = array (	"structure" => "CREATE TABLE `sms_report` (
                                                            `customers_id` int(11) unsigned NOT NULL DEFAULT '0',
                                                            `sms_request_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                                                            `sms_request_type` varchar(16) default NULL,
                                                            `sms_request_phone_number` varchar(32) default NULL,
                                                            `sms_request_phone_country_id` int(11) unsigned NOT NULL DEFAULT '0',
                                                            `sms_request_page` varchar(128) default NULL,
                                                            PRIMARY KEY  (customers_id, sms_request_date, sms_request_type)
                                                        ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                        "data" => "" );

add_new_tables ($add_new_tables, $DBTables);
// End of create new table

// Insert new records into configuration_group table (for Mobile Store configuration)
$configuration_group_insert_sql = array();
$configuration_group_insert_sql["SMS Module"] = array	("insert" => " ('SMS Module', 'SMS Module setting', '450', '1' )");

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_title", $configuration_group_insert_sql, $DBTables, "(configuration_group_title, configuration_group_description, sort_order, visible)");

$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='SMS Module'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["SMS_MODULE_MODE"] = array("insert" => " ('SMS Mode', 'SMS_MODULE_MODE', 'Testing', 'SMS Operation Mode. SMS will not send in Testing Mode and will display on webpage', ".$row_sql["configuration_group_id"].", 5, NULL, NOW(), NULL, 'tep_cfg_select_option(array(\'Production\', \'Testing\'),')" );
	$conf_insert_sql["SMS_MOBILE_ACE_USERNAME"] = array("insert" => " ('mobileAce API Username', 'SMS_MOBILE_ACE_USERNAME', '', 'Your mobileACE account username', ".$row_sql["configuration_group_id"].", 10, NULL, NOW(), NULL, NULL)" );
	$conf_insert_sql["SMS_MOBILE_ACE_PASSWORD"] = array("insert" => " ('mobileAce API Password', 'SMS_MOBILE_ACE_PASSWORD', '', 'Your mobileACE account password', ".$row_sql["configuration_group_id"].", 15, NULL, NOW(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of Insert new records into configuration_group table (for Mobile Store configuration)


// Wee Siong
// Remove unverified phone number
tep_db_query("UPDATE ".TABLE_CUSTOMERS_INFO_VERIFICATION." 
                    SET info_verification_type = 'telephone_del' 
                  WHERE info_verification_type = 'telephone' AND info_verified=0");
// Remove unverified phone number


// Clean verified phone number which it's sharing or it's not a mobile number
$dialing_code_array = array();

$dialing_code_sql_select = "SELECT countries_id, countries_international_dialing_code 
                            FROM " . TABLE_COUNTRIES;
$dialing_code_query = tep_db_query($dialing_code_sql_select);
while ($dialing_code_row = tep_db_fetch_array($dialing_code_query)) {
    $dialing_code_array[$dialing_code_row['countries_id']] = $dialing_code_row['countries_international_dialing_code'];
}

$select_sql = "	SELECT DISTINCT customers_id 
				FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
				WHERE info_verification_type = 'telephone'";
$result_sql = tep_db_query($select_sql);
while ($row_sql = tep_db_fetch_array($result_sql)) {
    $remove_cust_id_arr = array();
    $customers_id = $row_sql['customers_id'];
    
    $select2a_sql = "	SELECT customers_id 
                        FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
                        WHERE info_verification_type = 'telephone'
                            AND customers_id = '" . $customers_id . "'";
    $result2a_sql = tep_db_query($select2a_sql);
    if (!tep_db_num_rows($result2a_sql)) {
        continue;
    }
    
    $select2_sql = "	SELECT customers_telephone, customers_country_dialing_code_id 
                        FROM " . TABLE_CUSTOMERS . " 
                        WHERE customers_id = '" . $customers_id . "'";
    $result2_sql = tep_db_query($select2_sql);
    if ($row2_sql = tep_db_fetch_array($result2_sql)) {
        $telephone_no = $row2_sql['customers_telephone'];
        $dialing_id = $row2_sql['customers_country_dialing_code_id'];
        $dialing_code = $dialing_code_array[$dialing_id];
        
        $select3_sql = "	SELECT customers_telephone_type
                            FROM " . TABLE_MAXMIND_TELEPHONE_IDENTIFICATION . " 
                            WHERE customers_id = '" . $customers_id . "'
                                AND customers_country_international_dialing_code = '" . $dialing_code . "' 
								AND customers_telephone ='" . $telephone_no . "' ";
        $result3_sql = tep_db_query($select3_sql);
        if ($row3_sql = tep_db_fetch_array($result3_sql)) {
            if (in_array($row3_sql['customers_telephone_type'], array(2, 3, 302, 303))) {
                $select4_sql = "	SELECT customers_id
                                    FROM " . TABLE_CUSTOMERS . " 
                                    WHERE customers_id != '" . $customers_id . "'
                                        AND customers_country_dialing_code_id = '" . $dialing_id . "' 
                                        AND customers_telephone ='" . $telephone_no . "' ";
                $result4_sql = tep_db_query($select4_sql);
                while ($row4_sql = tep_db_fetch_array($result4_sql)) {
                    $remove_cust_id_arr[] = $row4_sql['customers_id'];
                } 
                
                if (count($remove_cust_id_arr)) {
                    // number shared each others
                    $remove_cust_id_arr[] = $customers_id;
                }
            } else {
                // number does not belongs to mobile phone type
                $remove_cust_id_arr[] = $customers_id;
            }
        } else {
            // number does not exist in Maxmind
            $remove_cust_id_arr[] = $customers_id;
        }
    }
    
    if (count($remove_cust_id_arr)) {
        tep_db_query("UPDATE ".TABLE_CUSTOMERS_INFO_VERIFICATION." 
                        SET info_verification_type = 'telephone_del' 
                      WHERE info_verification_type = 'telephone' AND customers_id IN ('".implode("','", $remove_cust_id_arr)."')");
        
        unset($remove_cust_id_arr);
    }
}
// Clean verified phone number which it's sharing or it's not a mobile number

$aft_group_array = array();
$affected_customers = array();

$customers_aft_status_select_sql = "SELECT customers_aft_groups_id, customers_aft_groups_name 
                                    FROM " . TABLE_CUSTOMERS_AFT_GROUPS;
$customers_aft_status_result_sql = tep_db_query ($customers_aft_status_select_sql);
while ($row_sql = tep_db_fetch_array($customers_aft_status_result_sql)) {
    $aft_group_array[$row_sql['customers_aft_groups_id']] = $row_sql['customers_aft_groups_name'];
}

$select_sql = "	SELECT DISTINCT customers_id 
				FROM " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
				WHERE info_verification_type = 'telephone_del'" ;
$result_sql = tep_db_query($select_sql);
while ($row_sql = tep_db_fetch_array($result_sql)) {
    $affected_customers[] = $row_sql['customers_id'];
}

$groups_id_select_sql = "	SELECT customers_id, customers_aft_groups_id 
                            FROM " . TABLE_CUSTOMERS . " 
                            WHERE customers_id IN ('".implode("','", $affected_customers)."')";
$groups_id_result_sql = tep_db_query($groups_id_select_sql);
while ($groups_id_row = tep_db_fetch_array($groups_id_result_sql)) {
    echo $groups_id_row['customers_id'] . ', ' . $aft_group_array[$groups_id_row['customers_aft_groups_id']] . '<br>';
}

$conf_delete_sql = array();
$conf_delete_sql['telephone_del'] = array(	"unique" => "0", "extra_where" => "" );
delete_records(TABLE_CUSTOMERS_INFO_VERIFICATION, "info_verification_type", $conf_delete_sql, $DBTables);
unset($conf_delete_sql);
?>