<?php
$existing_sms_report_fields = get_table_fields('sms_report');

// Define seller_id as index key in products_hla table
add_index_key ('products_hla', 'index_seller_id', 'index', 'seller_id', $DBTables);
// End of define seller_id as index key in products_hla table

// Insert new fields into sms_report table
$add_new_field['sms_report'] = array (	array (	"field_name" => "sms_provider",
                                                "field_attr" => " varchar(16) NOT NULL DEFAULT '' ",
                                                "add_after" => ''
                                            )
                                    );

add_field($add_new_field);
// End of insert new fields into sms_report table

if (!in_array('sms_provider', $existing_sms_report_fields)) {
    // Update to 'mobile_ace'
    $update_sql = "	UPDATE " . TABLE_SMS_REPORT . "
                    SET sms_provider='mobile_ace'
                    WHERE 1" ;
    tep_db_query($update_sql);
    //End of update to 'mobile_ace'
}
?>