<?php
// Add new product flag
$products_flag_data_array = array(	'products_flag_id' => '4',
									'products_flag_name' => 'Gift Card',
									'products_flag_image' => '',
									'date_added' => 'NOW()',
									'last_modified' => '',
									'languages_id' => 0,
								   );
tep_db_perform(TABLE_PRODUCTS_FLAG, $products_flag_data_array);
// End of add new product flag

$existing_gift_card_redemption_fields = get_table_fields('gift_card_redemption');

// Alter gift_card_redemption tables
if (!in_array('gift_card_redeem_amount', $existing_gift_card_redemption_fields)) {
	tep_db_query("ALTER TABLE `gift_card_redemption` ADD `gift_card_redeem_amount` DECIMAL( 15, 4 ) NOT NULL AFTER `gift_card_deno`;");
}
// End of alter gift_card_redemption tables
?>