<?php

$dominica_select_sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 59";
$dominica_result_sql = tep_db_query($dominica_select_sql);
if ($dominica_row = tep_db_fetch_array($dominica_result_sql)) {
    if ($dominica_row['countries_international_dialing_code'] == '1767') {
        $dominica_update_sql = "UPDATE customers 
                                SET customers_telephone = CONCAT('767',customers_telephone)
                                WHERE  `customers_country_dialing_code_id` =59";
        tep_db_query($dominica_update_sql);
    }
}

$dominica_rep_select_sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 60";
$dominica_rep_result_sql = tep_db_query($dominica_rep_select_sql);
if ($dominica_rep_row = tep_db_fetch_array($dominica_rep_result_sql)) {
    if ($dominica_rep_row['countries_international_dialing_code'] == '1809') {
        $dominica_rep_update_sql = "UPDATE customers 
                                    SET customers_telephone = CONCAT('809',customers_telephone)
                                    WHERE  `customers_country_dialing_code_id` =60";
        tep_db_query($dominica_rep_update_sql);
    }
}

// Update international dialing code for Dominica and Dominican Republic
$update_sql = array();

$update_sql["59"] = array("update" => " countries_international_dialing_code=1 " );
$update_sql["60"] = array("update" => " countries_international_dialing_code=1 " );

update_records("countries", "countries_id", $update_sql, $DBTables);
// End of update international dialing code for Dominica and Dominican Republic
?>