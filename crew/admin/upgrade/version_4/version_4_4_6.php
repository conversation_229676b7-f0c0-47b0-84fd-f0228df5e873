<?php
// Insert new records into custom_products_type_child table (for PlayNow Marketplace)
$custom_products_type_child_select_sql = "	SELECT custom_products_type_child_id 	
											FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . "
											WHERE custom_products_type_child_id = '15'";
$custom_products_type_child_result_sql = tep_db_query($custom_products_type_child_select_sql);
if (!tep_db_num_rows($custom_products_type_child_result_sql)) {
	$custom_products_type_child_data_array = array(	'custom_products_type_id' => '1',
													'custom_products_type_child_url' => 'playnow_marketplace',
													'custom_products_type_child_name' => 'PlayNow Marketplace',
													'display_status' => '1',
													'sort_order' => 6500
							                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, $custom_products_type_child_data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '15',
							'languages_id' => 1,
							'custom_products_type_child_name' => 'PLAY<i>NOW</i> Marketplace'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '15',
							'languages_id' => 2,
							'custom_products_type_child_name' => 'PLAY<i>NOW</i>&#24066;&#22330;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
	
	$data_array = array(	'custom_products_type_child_id' => '15',
							'languages_id' => 3,
							'custom_products_type_child_name' => 'PLAY<i>NOW</i>&#24066;&#22580;'
	                       );
	tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, $data_array);
}
// End of insert new records into custom_products_type_child table (for PlayNow Marketplace)
?>