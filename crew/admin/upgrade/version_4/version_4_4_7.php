<?php
$existing_customers_otp_fields = get_table_fields('customers_otp');

// Alter customers_otp tables
if (!in_array('customers_otp_type', $existing_customers_otp_fields)) {
	tep_db_query("ALTER TABLE `customers_otp` ADD `customers_otp_type` VARCHAR( 32 ) NOT NULL DEFAULT 'security_token_request' AFTER `customers_id`;");
    
    // ALTER PRIMARY Key from `customers_id` TO `customers_id` & `customers_otp_type`
    tep_db_query("ALTER TABLE `customers_otp` DROP PRIMARY KEY ,ADD PRIMARY KEY ( `customers_id` , `customers_otp_type` )");
}
// End of alter customers_otp tables
?>