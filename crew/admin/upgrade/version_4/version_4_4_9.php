<?php

$bahamas_select_sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 16";
$bahamas_result_sql = tep_db_query($bahamas_select_sql);
if ($bahamas_row = tep_db_fetch_array($bahamas_result_sql)) {
    if ($bahamas_row['countries_international_dialing_code'] == '1242') {
        $bahamas_update_sql = " UPDATE customers 
                                SET customers_telephone = CONCAT('242',customers_telephone)
                                WHERE  `customers_country_dialing_code_id` =16";
        tep_db_query($bahamas_update_sql);
    }
}

// Update international dialing code for Bahamas
$update_sql = array();

$update_sql["16"] = array("update" => " countries_international_dialing_code=1 " );

update_records("countries", "countries_id", $update_sql, $DBTables);
// End of update international dialing code for Bahamas
?>