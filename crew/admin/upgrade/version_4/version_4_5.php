<?php

// Insert new records into configuration table (for Amazon Web Services)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Amazon Web Services'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	
	$conf_insert_sql["AWS_S3_BUCKET_SECURE_ENABLED"] = array("insert" => " ('Enable S3\'s SECURE Bucket Support', 'AWS_S3_BUCKET_SECURE_ENABLED', 'false', 'Enable Amazon Simple Storage Service (S3\'s SECURE Bucket)?', ".$row_sql["configuration_group_id"].", 10, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Amazon Web Services)
?>