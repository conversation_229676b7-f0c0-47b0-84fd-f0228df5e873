<?php
// Create new table
$add_new_tables = array();

$add_new_tables["indomog"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `indomog` (
                                                          `indomog_orders_id` int(11) unsigned NOT NULL default '0',
                                                          `indomog_merchant_id` varchar(32) default NULL,
                                                          `indomog_transaction_id` varchar(32) default NULL,
                                                          `indomog_bank_id` varchar(32) default NULL,
                                                          `indomog_transaction_status` varchar(20) default NULL,
                                                          `indomog_transaction_date` datetime default NULL,
                                                          `indomog_amount` decimal(15,2) NOT NULL default '0.00',
                                                          `indomog_signature` varchar(255) default NULL,
                                                          `indomog_certificate` TEXT DEFAULT NULL,
                                                          PRIMARY KEY  (`indomog_orders_id`),
                                                          KEY `index_transaction_id` (`indomog_transaction_id`(12))
                                                        ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                        "data" => "" );

$add_new_tables["indomog_status_history"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `indomog_status_history` (
                                                                      `indomog_status_history_id` int(11) unsigned NOT NULL auto_increment,
                                                                      `indomog_orders_id` int(11) unsigned default NULL,
                                                                      `indomog_response_code` varchar(3) default NULL,
                                                                      `indomog_response_description` varchar(128) default NULL,
                                                                      `indomog_status_datetime` datetime default NULL,
                                                                      `indomog_changed_by` varchar(128) default NULL,
                                                                      PRIMARY KEY  (`indomog_status_history_id`),
                                                                      KEY `index_order_id` (`indomog_orders_id`)
                                                                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                                    "data" => "" );

add_new_tables ($add_new_tables, $DBTables);
// End of create new table
?>