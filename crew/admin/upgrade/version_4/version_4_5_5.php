<?php
// Create new table
$add_new_tables = array();

$add_new_tables["buyback_online_user"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `buyback_online_user` (
                                                                      `customer_id` int(11) unsigned NOT NULL,
                                                                      `entry_type` enum('0','1') NOT NULL COMMENT '0=unverified,1=verified',
                                                                      `expiry_date` datetime NOT NULL,
                                                                      `hit_count` int(11) unsigned NOT NULL,
                                                                      `hit_start_time` datetime NOT NULL,
                                                                      PRIMARY KEY (`customer_id`)
                                                                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                                    "data" => "" );

$add_new_tables["buyback_suspend_user"] = array (	"structure" => "CREATE TABLE IF NOT EXISTS `buyback_suspend_user` (
                                                                      `customer_id` int(11) unsigned NOT NULL,
                                                                      `expiry_date` datetime NOT NULL,
                                                                      PRIMARY KEY (`customer_id`)
                                                                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;" ,
                                                    "data" => "" );

add_new_tables ($add_new_tables, $DBTables);
// End of create new table
?>