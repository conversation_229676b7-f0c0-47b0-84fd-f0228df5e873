<?php

// Insert new upgrade record into custom_products_type_child_lang table for Indonesian language
$insert_sql = array();
$insert_sql['1'] = array("insert" => " (1, 4, 'Mata Uang Game')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="1" AND languages_id="4"');

$insert_sql = array();
$insert_sql['2'] = array("insert" => " (2, 4, 'Layanan Power Leveling')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="2" AND languages_id="4"');

$insert_sql = array();
$insert_sql['3'] = array("insert" => " (3, 4, 'Kartu Game')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="3" AND languages_id="4"');

$insert_sql = array();
$insert_sql['4'] = array("insert" => " (4, 4, 'Kredit Toko')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="4" AND languages_id="4"');

$insert_sql = array();
$insert_sql['5'] = array("insert" => " (5, 4, 'Akun Level Tinggi')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="5" AND languages_id="4"');

$insert_sql = array();
$insert_sql['6'] = array("insert" => " (6, 4, 'Alat Gaming')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="6" AND languages_id="4"');

$insert_sql = array();
$insert_sql['8'] = array("insert" => " (8, 4, 'Kartu Portal Game')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="8" AND languages_id="4"');

$insert_sql = array();
$insert_sql['9'] = array("insert" => " (9, 4, 'Kartu Hadiah')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="9" AND languages_id="4"');

$insert_sql = array();
$insert_sql['10'] = array("insert" => " (10, 4, 'Poin Konsol Game')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="10" AND languages_id="4"');

$insert_sql = array();
$insert_sql['11'] = array("insert" => " (11, 4, 'Isi Ulang Ponsel')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="11" AND languages_id="4"');

$insert_sql = array();
$insert_sql['12'] = array("insert" => " (12, 4, 'Item Game')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="12" AND languages_id="4"');

$insert_sql = array();
$insert_sql['13'] = array("insert" => " (13, 4, 'Unduhan Game')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="13" AND languages_id="4"');

$insert_sql = array();
$insert_sql['14'] = array("insert" => " (14, 4, 'Langganan Internet')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="14" AND languages_id="4"');

$insert_sql = array();
$insert_sql['15'] = array("insert" => " (15, 4, 'PLAY<i>NOW</i>')" );
insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, 'custom_products_type_child_id', $insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="15" AND languages_id="4"');

// End of insert new upgrade record into custom_products_type_child_lang table for Indonesian language
?>