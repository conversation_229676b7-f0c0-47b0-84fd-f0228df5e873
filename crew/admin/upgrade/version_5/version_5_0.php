<?php

// Create new table
$add_new_tables["c2c_best_selling"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_best_selling` (
                      `countries_id` int(11) unsigned NOT NULL,
                      `custom_products_type_id` int(11) unsigned NOT NULL,
                      `game` text,
                      PRIMARY KEY (`countries_id`,`custom_products_type_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=latin1;",
    "data" => "");

$add_new_tables["c2c_buyback"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_buyback` (
                          `c2c_buyback_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                          `seller_id` int(11) unsigned NOT NULL COMMENT '`customer_id`',
                          `buyback_status_id` int(5) unsigned NOT NULL,
                          `date_added` datetime NOT NULL,
                          `date_expiry` datetime NOT NULL,
                          `show_restock` enum('1','0') NOT NULL,
                          `currency` varchar(3) NOT NULL DEFAULT 'USD',
                          `currency_value` decimal(14,8) NOT NULL DEFAULT '1.00000000',
                          `orders_tag_ids` varchar(255) NOT NULL,
                          `orders_read_mode` enum('1','0') NOT NULL,
                          `c2c_buyback_locked_by` int(11) unsigned NOT NULL,
                          `c2c_buyback_locked_from_ip` varchar(20) NOT NULL,
                          `c2c_buyback_locked_datetime` datetime NOT NULL,
                          PRIMARY KEY (`c2c_buyback_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_buyback_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_buyback_history` (
                      `c2c_buyback_history_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                      `c2c_buyback_id` int(11) NOT NULL,
                      `buyback_status_id` int(5) unsigned NOT NULL,
                      `date_added` datetime NOT NULL,
                      `customer_notified` tinyint(1) unsigned NOT NULL,
                      `comments` text,
                      `set_as_buyback_remarks` tinyint(1) unsigned NOT NULL,
                      PRIMARY KEY (`c2c_buyback_history_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_buyback_order_rating"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_buyback_order_rating` (
                      `order_id` int(11) unsigned NOT NULL,
                      `customer_id` int(11) unsigned NOT NULL,
                      `custom_product_type` tinyint(2) unsigned NOT NULL,
                      `rating_id` tinyint(3) unsigned NOT NULL,
                      `rating_value` tinyint(2) unsigned NOT NULL,
                      PRIMARY KEY (`order_id`,`customer_id`,`custom_product_type`,`rating_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_buyback_order_total_rating"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_buyback_order_total_rating` (
                      `customer_id` int(11) unsigned NOT NULL,
                      `custom_product_type` tinyint(2) unsigned NOT NULL,
                      `rating_id` tinyint(3) unsigned NOT NULL,
                      `average_rating` decimal(6,4) NOT NULL COMMENT 'if rating type = 1, the value is average rate, if rating type = 2, this field is refer to seller current level',
                      `total_rating` int(11) unsigned NOT NULL,
                      `total_order` int(11) unsigned NOT NULL,
                      PRIMARY KEY (`customer_id`,`custom_product_type`,`rating_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_buyback_product"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_buyback_product` (
                      `c2c_buyback_product_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                      `c2c_buyback_id` int(11) NOT NULL,
                      `game_id` int(11) unsigned NOT NULL,
                      `product_id` int(11) unsigned NOT NULL,
                      `custom_products_type` int(11) unsigned NOT NULL,
                      `c2c_products_listing_id` int(11) unsigned NOT NULL,
                      `buyback_request_quantity` int(11) NOT NULL COMMENT 'buyer purchase quantity',
                      `buyback_unit_price` double NOT NULL,
                      `buyback_amount` double NOT NULL,
                      `restock_character` text NOT NULL,
                      `buyback_quantity_confirmed` int(11) NOT NULL,
                      `buyback_quantity_received` int(11) NOT NULL,
                      `buyback_dealing_type` varchar(32) NOT NULL COMMENT 'Trade On Mail = `ofp_deal_on_mail`, Face to Face Trade = `ofp_deal_on_game`, Open Store = `ofp_deal_on_open_store`, Trade with OGM = `vip_deal_on_game`, Trade with Customer = `ofp_deal_with_customers`',
                      `orders_id` int(11) unsigned NOT NULL,
                      `orders_products_id` int(11) unsigned NOT NULL,
                      PRIMARY KEY (`c2c_buyback_product_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_configuration"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_configuration` ( 
                        `c2c_configuration_id` int(11) NOT NULL AUTO_INCREMENT, 
                        `configuration_title` varchar(64) NOT NULL DEFAULT '', 
                        `configuration_key` varchar(64) NOT NULL DEFAULT '', 
                        `configuration_value` text NOT NULL, 
                        `configuration_description` varchar(255) NOT NULL DEFAULT '', 
                        `configuration_group` varchar(255) NOT NULL, 
                        `sort_order` int(5) DEFAULT NULL, 
                        `last_modified` datetime DEFAULT NULL, 
                        `date_added` datetime NOT NULL DEFAULT '0000-00-00 00:00:00', 
                        `use_function` varchar(255) DEFAULT NULL, 
                        `set_function` text, 
                        PRIMARY KEY (`c2c_configuration_id`), 
                        KEY `index_configuration_key` (`configuration_key`) 
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1;",
    "data" => "");

$add_new_tables["c2c_customers"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_customers` (
                        `customers_id` int(11) unsigned NOT NULL,
                        `username` varchar(12) NOT NULL,
                        `seller_status` int(1) unsigned NOT NULL DEFAULT '0' COMMENT '''1''=active, ''0''=inactive',
                        `verified_seller` enum('0','1') NOT NULL DEFAULT '0' COMMENT '0=unverified, 1=verified, seller complete min order to be verified',
                        `seller_group_id` int(11) unsigned NOT NULL DEFAULT '0',
                        `seller_create_listing_permission` int(1) unsigned NOT NULL DEFAULT '0' COMMENT '''1''=enable, ''0''=disable',
                        `seller_product_listing_limit` int(11) NOT NULL DEFAULT '0' COMMENT '''-1''=unlimited',
                        `report_listing_counter` int(3) unsigned DEFAULT '0',
                        `last_report_listing_date` datetime DEFAULT NULL,
                        `cronjob_seller_product_listing_limit` enum('0','1') NOT NULL DEFAULT '0' COMMENT '''0''=false, ''1''=true, cronjob or manual updated',
                        `cronjob_verified_seller` enum('0','1') NOT NULL DEFAULT '0' COMMENT '''0''=false, ''1''=true, cronjob / manual update verified_seller',
                        PRIMARY KEY (`customers_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8; ",
    "data" => "");

$add_new_tables["c2c_customers_basket"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_customers_basket` (
                        `c2c_customers_basket_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                        `cart_id` int(5) NOT NULL,
                        `customers_id` int(11) NOT NULL,
                        `products_id` int(11) NOT NULL,
                        `customers_basket_quantity` int(2) NOT NULL DEFAULT '0',
                        `customers_basket_date_added` datetime NOT NULL,
                        `products_categories_id` int(11) NOT NULL,
                        `customers_basket_custom_key` int(3) unsigned DEFAULT NULL COMMENT 'custom_product_type_id',
                        `customers_basket_custom_value` text,
                        PRIMARY KEY (`c2c_customers_basket_id`),
                        KEY `index_cart_id` (`cart_id`),
                        KEY `index_customers_id` (`customers_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_customer_favourite_store"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_customer_favourite_store` (
                        `customers_id` int(11) unsigned NOT NULL,
                        `seller_id` int(11) unsigned NOT NULL,
                        `created_date` datetime NOT NULL,
                        `description` varchar(255) DEFAULT NULL,
                        PRIMARY KEY (`customers_id`,`seller_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8; ",
    "data" => "");

$add_new_tables["c2c_delivery_speed"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_delivery_speed` (
                        `custom_products_type_id` int(11) unsigned NOT NULL,
                        `delivery_speed` int(11) unsigned NOT NULL,
                        `from_minute` int(11) unsigned NOT NULL,
                        `to_minute` int(11) unsigned NOT NULL,
                        PRIMARY KEY (`custom_products_type_id`,`delivery_speed`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_delivery_speed_description"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_delivery_speed_description` ( 
                        `delivery_speed_id` int(11) unsigned NOT NULL, 
                        `language_id` int(11) unsigned NOT NULL, 
                        `delivery_speed_name` varchar(40) NOT NULL, 
                        PRIMARY KEY (`delivery_speed_id`,`language_id`) 
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8; ",
    "data" => "	INSERT INTO `c2c_delivery_speed_description` (`delivery_speed_id`, `language_id`, `delivery_speed_name`) VALUES
																		(1, 1, 'Very Fast'),
																		(2, 1, 'Fast'),
																		(3, 1, 'Average'),
																		(4, 1, 'Slow'),
																		(5, 1, 'Very Slow');");

$add_new_tables["c2c_mailbox"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_mailbox` (
                        `c2c_mailbox_id` bigint(20) unsigned NOT NULL,
                        `orders_id` int(11) unsigned NOT NULL,
                        `sender_id` int(11) unsigned NOT NULL,
                        `recipient_id` int(11) unsigned NOT NULL,
                        `message` text NOT NULL,
                        `recipient_view_status` enum('0','1') NOT NULL,
                        `sender_flag` enum('0','1') NOT NULL DEFAULT '1',
                        `recipient_flag` enum('0','1') NOT NULL DEFAULT '1',
                        `datetime` datetime NOT NULL,
                        PRIMARY KEY (`c2c_mailbox_id`,`orders_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_mailbox_group"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_mailbox_group` (
                        `orders_id` int(11) unsigned NOT NULL,
                        `customers_id` int(11) unsigned NOT NULL,
                        `recipient_id` int(11) unsigned NOT NULL,
                        `title` varchar(200) NOT NULL,
                        `status` enum('0','1') NOT NULL DEFAULT '1' COMMENT '0=hide mail, 1=display mail',
                        PRIMARY KEY (`orders_id`,`customers_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["orders_notification"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `orders_notification` (
                        `customers_id` int(11) unsigned NOT NULL,
                        `orders_id` int(11) unsigned NOT NULL,
                        `orders_type` varchar(3) NOT NULL COMMENT 'CO, BO',
                        `site_id` int(3) unsigned NOT NULL,
                        PRIMARY KEY (`customers_id`,`orders_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_product_margin"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_product_margin` (
                        `seller_group_id` int(11) unsigned NOT NULL,
                        `game_id` int(11) unsigned NOT NULL,
                        `custom_products_type_id` int(11) unsigned NOT NULL,
                        `payout_percentage` int(3) unsigned NOT NULL,
                        PRIMARY KEY (`seller_group_id`,`game_id`,`custom_products_type_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8; ",
    "data" => "");

$add_new_tables["c2c_product_configuration"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_product_configuration` (
                        `game_id` int(11) unsigned NOT NULL,
                        `custom_products_type_id` int(11) unsigned NOT NULL,
                        `min_purchase_amount_usd` decimal(15,6) NOT NULL,
                        `max_purchase_amount_usd` decimal(15,6) NOT NULL,
                        `product_unit_name` varchar(32) DEFAULT NULL COMMENT 'eg Gold, Kamas, Qty',
                        `product_max_level` varchar(32) DEFAULT '',
                        PRIMARY KEY (`game_id`,`custom_products_type_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_products_hla"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_hla` (
                        `products_hla_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                        `products_type` enum('1','2') NOT NULL COMMENT '1 = single, 2 = multiple',
                        `products_account_id` int(11) unsigned DEFAULT NULL,
                        `products_hla_reserve_id` varchar(128) DEFAULT NULL,
                        `products_hla_info_viewed` enum('0','1') NOT NULL COMMENT '0 = not viewed, 1 = viewed',
                        PRIMARY KEY (`products_hla_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_products_hla_attributes"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_hla_attributes` ( 
                        `products_hla_characters_id` int(11) unsigned NOT NULL, 
                        `products_hla_attributes_type` varchar(16) NOT NULL DEFAULT '', 
                        `products_hla_value` varchar(32) DEFAULT '', 
                        PRIMARY KEY (`products_hla_characters_id`,`products_hla_attributes_type`), 
                        KEY `index_type_and_value` (`products_hla_attributes_type`,`products_hla_value`) 
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_products_hla_characters"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_hla_characters` (
                        `products_hla_characters_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                        `products_hla_id` int(11) unsigned NOT NULL,
                        `products_ref_id` char(20) NOT NULL COMMENT 'Ref ID from Supplier',
                        PRIMARY KEY (`products_hla_characters_id`,`products_hla_id`),
                        KEY `products_hla_id` (`products_hla_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_products_hla_description"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_hla_description` ( 
                        `products_hla_characters_id` int(11) unsigned NOT NULL, 
                        `products_hla_characters_name` varchar(64) DEFAULT '', 
                        `products_hla_characters_description` text, 
                        PRIMARY KEY (`products_hla_characters_id`), 
                        FULLTEXT KEY `products_hla_characters_name` (`products_hla_characters_name`) 
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8; ",
    "data" => "");

$add_new_tables["c2c_products_item_attributes"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_item_attributes` (
                        `c2c_products_listing_id` int(11) unsigned NOT NULL,
                        `products_item_attributes_type` varchar(16) NOT NULL,
                        `products_item_value` varchar(32) NOT NULL,
                        PRIMARY KEY (`c2c_products_listing_id`,`products_item_attributes_type`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_products_listing"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_listing` (
                        `c2c_products_listing_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                        `c2c_products_instant_id` int(11) unsigned NOT NULL,
                        `seller_id` int(11) unsigned NOT NULL,
                        `game_id` int(11) unsigned NOT NULL,
                        `products_id` int(11) unsigned NOT NULL,
                        `custom_products_type` int(11) unsigned NOT NULL,
                        `products_title` varchar(255) NOT NULL,
                        `products_description` text NOT NULL,
                        `guarantee_delivery_speed` int(2) NOT NULL DEFAULT '1',
                        `available_quantity` int(11) NOT NULL DEFAULT '0',
                        `actual_quantity` int(11) NOT NULL DEFAULT '0',
                        `products_price` decimal(15,6) NOT NULL DEFAULT '0.000000',
                        `products_original_price` decimal(15,6) NOT NULL DEFAULT '0.000000',
                        `products_base_currency` char(3) NOT NULL,
                        `products_status` enum('-1','0','1') NOT NULL COMMENT '-1 = Inactive, 0 = Sold, 1 = Active',
                        `products_display` enum('0','1') NOT NULL COMMENT '0=hide, 1=show',
                        `delivery_mode` varchar(20) NOT NULL,
                        `created_date` datetime NOT NULL,
                        `last_modified_date` datetime NOT NULL,
                        `expiry_date` datetime NOT NULL,
                        PRIMARY KEY (`c2c_products_listing_id`),
                        KEY `products_id` (`products_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_products_listing_media"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_listing_media` ( 
                        `c2c_products_listing_media_id` int(11) unsigned NOT NULL AUTO_INCREMENT, 
                        `c2c_products_listing_id` int(11) unsigned NOT NULL, 
                        `media_type` varchar(10) NOT NULL COMMENT '''image''=local image, ''link''=external link', 
                        `media_url` varchar(255) NOT NULL, 
                        PRIMARY KEY (`c2c_products_listing_media_id`) 
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_products_listing_remarks_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_listing_remarks_history` ( 
                        `c2c_products_listing_remarks_history_id` int(11) unsigned NOT NULL AUTO_INCREMENT, 
                        `c2c_products_listing_id` int(11) unsigned NOT NULL, 
                        `remarks_added_date` datetime NOT NULL, 
                        `remarks` text NOT NULL, 
                        `remarks_added_by` varchar(255) NOT NULL, 
                        PRIMARY KEY (`c2c_products_listing_remarks_history_id`) 
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_rating_type"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_rating_type` (
                      `c2c_rating_type_id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
                      `custom_product_type` tinyint(2) unsigned NOT NULL,
                      `rating_type` enum('1','2') NOT NULL COMMENT '1=normal rating, 2=product level calculation',
                      `display_status` enum('0','1') NOT NULL COMMENT '0=hide, 1=show',
                      `sort_order` int(5) NOT NULL DEFAULT '50000',
                      PRIMARY KEY (`c2c_rating_type_id`)
                    ) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1;",
    "data" => "");

$add_new_tables["c2c_rating_type_description"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_rating_type_description` (
                      `c2c_rating_type_id` tinyint(3) unsigned NOT NULL,
                      `language_id` tinyint(2) unsigned NOT NULL,
                      `rating_description` varchar(255) NOT NULL,
                      PRIMARY KEY (`c2c_rating_type_id`,`language_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_seller_groups"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_seller_groups` (
                        `seller_group_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                        `seller_group_name` varchar(32) NOT NULL,
                        `total_completed_sales_amount` decimal(15,2) unsigned NOT NULL DEFAULT '0.00',
                        `payout_grace_period_offset` int(11) DEFAULT '0',
                        `sort_order` int(5) NOT NULL DEFAULT '50000',
                        PRIMARY KEY (`seller_group_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["c2c_seller_level_configuration"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_seller_level_configuration` (
                      `c2c_seller_level_configuration_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                      `custom_product_type` tinyint(3) unsigned NOT NULL,
                      `min_level` tinyint(3) unsigned NOT NULL,
                      `max_level` tinyint(3) unsigned NOT NULL,
                      `orders_required` int(5) unsigned NOT NULL,
                      `accumulated_orders` int(5) unsigned NOT NULL,
                      PRIMARY KEY (`c2c_seller_level_configuration_id`)
                    ) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["customers_verification_setting"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `customers_verification_setting` (
                        `countries_id` int(11) NOT NULL,
                        `custom_products_type` int(11) unsigned NOT NULL,
                        `email` enum('0','1') NOT NULL DEFAULT '0',
                        `telephone` enum('0','1') NOT NULL DEFAULT '0',
                        `files_001` enum('0','1') NOT NULL DEFAULT '0' COMMENT 'Purchase Authorisation Form',
                        `files_002` enum('0','1') NOT NULL DEFAULT '0' COMMENT 'Utility Bill',
                        `files_003` enum('0','1') NOT NULL DEFAULT '0' COMMENT 'Photo Identification',
                        `files_004` enum('0','1') NOT NULL DEFAULT '0' COMMENT 'Front Credit Card',
                        `files_005` enum('0','1') NOT NULL DEFAULT '0' COMMENT 'Back Credit Card',
                        PRIMARY KEY (`countries_id`,`custom_products_type`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
// End of create new table
// Insert new fields into `categories` table
$add_new_field = array();
$add_new_field['categories'] = array(array("field_name" => "c2c_categories_status",
        "field_attr" => "tinyint(1) unsigned NOT NULL DEFAULT '0'",
        "add_after" => 'categories_status'
    )
);
add_field($add_new_field);
// End of insert new fields
// Insert new fields into `customers_groups_discount` table
$add_new_field = array();
$add_new_field['customers_groups_discount'] = array(array("field_name" => "c2c_customers_groups_discount",
        "field_attr" => "decimal(8,2) NOT NULL DEFAULT '0.00'",
        "add_after" => ''
    ),
    array("field_name" => "c2c_customers_groups_rebate",
        "field_attr" => "decimal(8,2) NOT NULL DEFAULT '0.00'",
        "add_after" => ''
    ),
);
add_field($add_new_field);
// End of insert new fields
// Insert C2C Site Code
$select_sql = "	SELECT site_id 
				FROM " . TABLE_SITE_CODE . "
				WHERE site_id = '5'";
$result_sql = tep_db_query($select_sql);
if (!tep_db_num_rows($result_sql)) {
    $site_code_data_sql = array('site_id' => 5,
        'site_name' => 'C2C',
        'site_has_buyback' => 0,
        'admin_groups_id' => '1',
        'buyback_admin_groups_id' => ''
    );
    tep_db_perform(TABLE_SITE_CODE, $site_code_data_sql);
}
// End of insert C2C Site Code
// Insert Edit Customer C2C Information Permission
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $file_action_sel_sql = "SELECT admin_files_actions_key 
							FROM " . TABLE_ADMIN_FILES_ACTIONS . "
							WHERE admin_files_actions_key='CUSTOMER_EDIT_C2C_INFO'";
    $file_action_res_sql = tep_db_query($file_action_sel_sql);
    if (!$file_action_row = tep_db_fetch_array($file_action_res_sql)) {
        $admin_files_actions_insert_sql = array();

        $admin_files_actions_insert_sql["CUSTOMER_EDIT_C2C_INFO"] = array("insert" => " ('CUSTOMER_EDIT_C2C_INFO', 'Edit customer C2C information', " . $row_sql["admin_files_id"] . ", '1', 100)");
        insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
    }
}
// End of insert Edit Customer C2C Information Permission
// Insert new records into admin_files table (for C2C)
$admin_files_insert_sql = array();
$admin_files_insert_sql['c2c.php'] = array("insert" => " ('c2c.php', '1', '0', '1', '0') ");
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id, admin_files_cat_setting)", " admin_files_name='c2c.php' AND admin_files_is_boxes=1 ");
// End of insert new records into admin_files table (for C2C)
// 
// Add new store file into admin_files
$select_sql = "	SELECT admin_files_id FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_name = 'c2c.php'";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["c2c_configuration.php"] = array("insert" => " ('c2c_configuration.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='c2c_configuration.php' AND admin_files_is_boxes=0 ");
    
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["c2c_seller_group.php"] = array("insert" => " ('c2c_seller_group.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='c2c_seller_group.php' AND admin_files_is_boxes=0 ");
    
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["c2c_seller_product_listing.php"] = array("insert" => " ('c2c_seller_product_listing.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='c2c_seller_product_listing.php' AND admin_files_is_boxes=0 ");

    $admin_files_insert_sql = array();
    $admin_files_insert_sql["c2c_product_configuration.php"] = array("insert" => " ('c2c_product_configuration.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='c2c_product_configuration.php' AND admin_files_is_boxes=0 ");

    $admin_files_insert_sql = array();
    $admin_files_insert_sql["c2c_delivery_speed.php"] = array("insert" => " ('c2c_delivery_speed.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='c2c_delivery_speed.php' AND admin_files_is_boxes=0 ");
}
// End of add new store file into admin_files
// Insert new records into c2c_configuration table (C2C Configuration)
$conf_insert_sql = array();

# Product Listing
$conf_insert_sql["C2C_LISTING_PER_PAGE"] = array("insert" => " ('Max Product Listing Display', 'C2C_LISTING_PER_PAGE', '20', 'Maximum product listing display per page', 'C2C_PRODUCT_LISTING', 10, NULL, NOW(), NULL, '') ");

# Product Configuration
$conf_insert_sql["C2C_DEFAULT_PRODUCT_MARGIN"] = array("insert" => " ('Default Product Margin', 'C2C_DEFAULT_PRODUCT_MARGIN', '40', 'Default product margin for all game and product type', 'C2C_PRODUCT_CONFIGURATION', 10, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_DEFAULT_MIN_PURCHASE"] = array("insert" => " ('Default Minimum Purchase (USD)', 'C2C_DEFAULT_MIN_PURCHASE', '10', 'Selling / Purchase Qty X Unit Price should not less than Default Minimum Purchase Price (USD)', 'C2C_PRODUCT_CONFIGURATION', 15, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_DEFAULT_PRODUCT_UNIT_NAME"] = array("insert" => " ('Default Product Unit Name', 'C2C_DEFAULT_PRODUCT_UNIT_NAME', 'Gold', 'Product Unit Name, e.g. Gold, Mil, Kamas, etc', 'C2C_PRODUCT_CONFIGURATION', 20, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_DEFAULT_PRODUCT_MAX_LEVEL"] = array("insert" => " ('Default Product Max Level', 'C2C_DEFAULT_PRODUCT_MAX_LEVEL', '1', 'HLA Product Maximum Level', 'C2C_PRODUCT_CONFIGURATION', 25, NULL, NOW(), NULL, '') ");

# Seller Product Listing
$conf_insert_sql["C2C_DEFAULT_LISTING_LIMIT"] = array("insert" => " ('Default Create Listing Limit', 'C2C_DEFAULT_LISTING_LIMIT', '20', 'Default Listing Limit for new seller', 'C2C_SELLER_PRODUCT_LISTING', 10, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_SELLER_MIN_COMPLETED_ORDER"] = array("insert" => " ('Min number of Completed Order for upgrade', 'C2C_SELLER_MIN_COMPLETED_ORDER', '10', 'Min completed order to upgrade seller maximum Create Product Listing, tag as Verified Seller, etc', 'C2C_SELLER_PRODUCT_LISTING', 15, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_MAX_LISTING_LIMIT"] = array("insert" => " ('Max Create Listing Limit', 'C2C_MAX_LISTING_LIMIT', '3000', 'Maximum number of Product Listing per Seller, which Product Listing in any type of status', 'C2C_SELLER_PRODUCT_LISTING', 20, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_MAX_ENTRY_PER_LISTING"] = array("insert" => " ('Max Entry per Listing', 'C2C_MAX_ENTRY_PER_LISTING', '500', 'Maximum number of Entry per Product Listing', 'C2C_SELLER_PRODUCT_LISTING', 21, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_PRODUCT_BASE_CURRENCY"] = array("insert" => " ('Product Base Currency', 'C2C_PRODUCT_BASE_CURRENCY', '3, 1', 'Product base currency', 'C2C_SELLER_PRODUCT_LISTING', 25, NULL, NOW(), NULL, 'tep_cfg_currencies(') ");
$conf_insert_sql["C2C_LISTING_REPORT_RECIPIENT"] = array("insert" => " ('Recipient of Reporting Notify Email', 'C2C_LISTING_REPORT_RECIPIENT', '', 'Recipient of reporting on Product Listing', 'C2C_SELLER_PRODUCT_LISTING', 30, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_MAX_LISTING_REPORT_PER_DAY"] = array("insert" => " ('Max Number of Reporting', 'C2C_MAX_LISTING_REPORT_PER_DAY', '3', 'Maximum number of reporting on Product Listing per user in a day', 'C2C_SELLER_PRODUCT_LISTING', 35, NULL, NOW(), NULL, '')");
$conf_insert_sql["C2C_LISTING_ACTIVE_PERIOD"] = array("insert" => " ('Product Listing Active Period', 'C2C_LISTING_ACTIVE_PERIOD', '5', 'New Product Listing active period (day)', 'C2C_SELLER_PRODUCT_LISTING', 40, NULL, NOW(), NULL, '')");
$conf_insert_sql["C2C_LISTING_EXTEND_PERIOD"] = array("insert" => " ('Product Listing Extend Period', 'C2C_LISTING_EXTEND_PERIOD', '3', 'Expired Product Listing extend period (day)', 'C2C_SELLER_PRODUCT_LISTING', 45, NULL, NOW(), NULL, '')");

# Buyer Order
$conf_insert_sql["C2C_MAX_NUM_OF_PENDING"] = array("insert" => " ('Maximum number of Pending Order', 'C2C_MAX_NUM_OF_PENDING', '5', 'Maximum number of Pending Order within X Minutes', 'C2C_BUYER_ORDER', 10, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_MAX_PENDING_WITHIN_MIN"] = array("insert" => " ('Maximum Pending Order Within X Minutes', 'C2C_MAX_PENDING_WITHIN_MIN', '30', 'X Minutes to restrict maximum Pending Order', 'C2C_BUYER_ORDER', 15, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_EXCEED_MAX_PENDING_RECIPIENT"] = array("insert" => " ('Recipient of Exceed Pending Order Notify Email', 'C2C_EXCEED_MAX_PENDING_RECIPIENT', '', 'Recipient of Buyer Exceed Pending Order Notification Email', 'C2C_BUYER_ORDER', 20, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_MAX_NUM_OF_CANCEL"] = array("insert" => " ('Maximum number of Cancel Order', 'C2C_MAX_NUM_OF_CANCEL', '5', 'Maximum number of Cancel Order within X Minutes', 'C2C_BUYER_ORDER', 25, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_MAX_CANCEL_WITHIN_MIN"] = array("insert" => " ('Maximum Cancel Order Within X Minutes', 'C2C_MAX_CANCEL_WITHIN_MIN', '30', 'X Minutes to restrict maximum Cancel Order', 'C2C_BUYER_ORDER', 30, NULL, NOW(), NULL, '') ");
$conf_insert_sql["C2C_EXCEED_MAX_CANCEL_RECIPIENT"] = array("insert" => " ('Recipient of Exceed Cancel Order Notify Email', 'C2C_EXCEED_MAX_CANCEL_RECIPIENT', '', 'Recipient of Buyer Exceed Cancel Order Notification Email', 'C2C_BUYER_ORDER', 35, NULL, NOW(), NULL, '') ");

# Mailbox
$conf_insert_sql["C2C_MAX_MAIL_THREAD"] = array("insert" => " ('Maximum number of Mail Thread', 'C2C_MAX_MAIL_THREAD', '500', 'Maximum number of Mail Thread per user', 'C2C_MAILBOX', 10, NULL, NOW(), NULL, '') ");

insert_new_records(TABLE_C2C_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into c2c_configuration table (C2C Configuration)
// Insert new custom_products_type
$type_select_sql = 'SELECT custom_products_type_id
					FROM custom_products_type
					WHERE custom_products_type_id=5';
$type_result_sql = tep_db_query($type_select_sql);
if (!tep_db_num_rows($type_result_sql)) {
    $custom_products_type_data_sql = array('custom_products_type_id' => 5,
        'custom_products_type_name' => 'Game Item',
        'data_pool_id' => 0,
        'custom_products_low_stock_email' => '',
        'custom_products_add_stock_email' => '',
        'custom_products_deduct_stock_email' => '',
        'custom_products_admin_group_id' => '1'
    );
    tep_db_perform(TABLE_CUSTOM_PRODUCTS_TYPE, $custom_products_type_data_sql);



    # Insert custom_products_type_child_url and custom_products_type_child_name in custom_products_type_child table
    $cpt_insert_sql = array();
    $cpt_insert_sql["game_item_store"] = array("insert" => " ('5', 'game_item_store', 'Game Item', '1', '6000') ");
    
    insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD, "custom_products_type_child_url", $cpt_insert_sql, $DBTables, "(custom_products_type_id, custom_products_type_child_url, custom_products_type_child_name, display_status, sort_order)");



    # Insert custom_products_type_child_name in custom_products_type_child_lang table
    $select_sql = "	SELECT custom_products_type_child_id 
					FROM " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . " 
					WHERE custom_products_type_child_url = 'game_item_store'";
    $result_sql = tep_db_query($select_sql);
    if ($row_sql = tep_db_fetch_array($result_sql)) {
        $cpt_insert_sql = array();
        $cpt_insert_sql[$row_sql['custom_products_type_child_id']] = array("insert" => " ('" . $row_sql['custom_products_type_child_id'] . "', '1', 'Item') ");
        insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, "custom_products_type_child_id", $cpt_insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="'.$row_sql['custom_products_type_child_id'].'" AND languages_id="1"');

        $cpt_insert_sql = array();
        $cpt_insert_sql[$row_sql['custom_products_type_child_id']] = array("insert" => " ('" . $row_sql['custom_products_type_child_id'] . "', '2', '&#36947;&#20855;') ");
        insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, "custom_products_type_child_id", $cpt_insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="'.$row_sql['custom_products_type_child_id'].'" AND languages_id="2"');

        $cpt_insert_sql = array();
        $cpt_insert_sql[$row_sql['custom_products_type_child_id']] = array("insert" => " ('" . $row_sql['custom_products_type_child_id'] . "', '3', '&#36947;&#20855;') ");
        insert_new_records(TABLE_CUSTOM_PRODUCTS_TYPE_CHILD_LANG, "custom_products_type_child_id", $cpt_insert_sql, $DBTables, "(custom_products_type_child_id, languages_id, custom_products_type_child_name)", 'custom_products_type_child_id="'.$row_sql['custom_products_type_child_id'].'" AND languages_id="3"');
    }
}
// End of insert new custom_products_type
// Insert new News Group
$news_select_sql = "SELECT news_groups_id 
					FROM " . TABLE_LATEST_NEWS_GROUPS_DESCRIPTION . " 
					WHERE news_groups_name = 'C2C News' 
						AND language_id = '1'";
$news_result_sql = tep_db_query($news_select_sql);
if (!tep_db_num_rows($news_result_sql)) {
    $news_data_sql = array('news_groups_display_status' => 0,
        'news_groups_sort_order' => 50000);
    tep_db_perform(TABLE_LATEST_NEWS_GROUPS, $news_data_sql);

    $news_id = tep_db_insert_id();
    for ($i = 1; 3 > $i; $i++) {
        $news_data_sql = array('news_groups_id' => $news_id,
            'language_id' => $i,
            'news_groups_name' => 'C2C News');
        tep_db_perform(TABLE_LATEST_NEWS_GROUPS_DESCRIPTION, $news_data_sql);
    }
}
// End of insert new News Group
// Insert countries_id into `customers_verification_setting`
// countries_id = '0' : all country
$cpt_sel_sql = "SELECT custom_products_type_id FROM " . TABLE_CUSTOM_PRODUCTS_TYPE;
$cpt_res_sql = tep_db_query($cpt_sel_sql);
while ($row_sql = tep_db_fetch_array($cpt_res_sql)) {
    $cpt = $row_sql['custom_products_type_id'];

    $setting_data_sql = array();
    $setting_data_sql["sett"] = array("insert" => " ('0', '" . $cpt . "', '1', '1', '0', '0', '1', '0', '0') ");
    insert_new_records(TABLE_CUSTOMERS_VERIFICATION_SETTING, '', $setting_data_sql, $DBTables, "(`countries_id`, `custom_products_type`, `email`, `telephone`, `files_001`, `files_002`, `files_003`, `files_004`, `files_005`)", " countries_id = '0' AND custom_products_type = '" . $cpt . "'");
}
// End of insert countries_id into `customers_verification_setting`
// Add new store file into admin_files
$select_sql = "	SELECT admin_files_id FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_name = 'c2c.php'";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["c2c_buyback_order.php"] = array("insert" => " ('c2c_buyback_order.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='c2c_buyback_order.php' AND admin_files_is_boxes=0 ");
}
// End of add new store file into admin_files
// Add Site ID to Customer Favorite Game ( Search All Game )
$existing_customers_favorites_fields = get_table_fields('customers_favorites');
if (!in_array('site_id', $existing_customers_favorites_fields)) {
    tep_db_query("ALTER TABLE `customers_favorites` ADD `site_id` tinyint(1) unsigned NOT NULL DEFAULT '0' AFTER `categories_id`;");
    tep_db_query("ALTER TABLE `customers_favorites` DROP PRIMARY KEY ,ADD PRIMARY KEY ( `customers_id` , `categories_id` , `site_id` )");
}
?>