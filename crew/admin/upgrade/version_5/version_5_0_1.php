<?php

// Insert new fields into c2c_customers_basket table
$add_new_field = array();

$add_new_field['c2c_customers_basket'] = array(array("field_name" => "pg_tran_id",
        "field_attr" => " INT(11) UNSIGNED NOT NULL ",
        "add_after" => ''
    ),
    array("field_name" => "cart_info",
        "field_attr" => " TEXT NOT NULL ",
        "add_after" => ''
    )
);
add_field($add_new_field);
// End of insert new fields into c2c_customers_basket table

$add_new_tables["c2c_buyback_cron"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_buyback_cron` (
                      `order_id` int(11) unsigned NOT NULL COMMENT 'C2C Customer Order ID',
                      PRIMARY KEY (`order_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);