<?php

// G2G Sell Order and B2C Buy Order
# change database field
$change_field = array();
$change_field['orders_products'] = array(
    array(
        "field_name" => "products_good_delivered_price",
        "field_attr" => " DECIMAL( 19, 8 ) NOT NULL DEFAULT '0.0000'"
    ),
    array(
        "field_name" => "products_canceled_price",
        "field_attr" => " DECIMAL( 19, 8 ) NOT NULL DEFAULT '0.0000'"
    ),
    array(
        "field_name" => "products_reversed_price",
        "field_attr" => " DECIMAL( 19, 8 ) NOT NULL DEFAULT '0.0000'"
    )
);
$change_field['c2c_buyback_product'] = array(
    array(
        "field_name" => "product_unit_price",
        "field_attr" => " DECIMAL( 19, 8 ) NOT NULL COMMENT 'set by seller'"
    ),
    array(
        "field_name" => "product_unit_price_usd",
        "field_attr" => " DECIMAL( 19, 8 ) NOT NULL COMMENT 'set by seller'"
    ),
    array(
        "field_name" => "after_fee_unit_price",
        "field_attr" => " DECIMAL( 19, 8 ) NOT NULL DEFAULT '0.0000'"
    ),
    array(
        "field_name" => "after_fee_unit_price_usd",
        "field_attr" => " DECIMAL( 19, 8 ) NOT NULL DEFAULT '0.0000'"
    )
);
change_field_structure($change_field);

// OffGamers Store
// Insert new records
$buyback_status = array(
    1 => 'Pending',
    2 => 'Processing',
    3 => 'Completed',
    4 => 'Cancel'
);

foreach ($buyback_status as $key => $val) {
    $conf_insert_sql = array();
    $conf_insert_sql[$key] = array("insert" => " ('" . $key . "', '4', '" . $val . "', '" . $key . "0') ");

    insert_new_records(TABLE_BUYBACK_STATUS, "buyback_status_id", $conf_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", "buyback_status_id = " . $key . " AND language_id = 4");
}
?>