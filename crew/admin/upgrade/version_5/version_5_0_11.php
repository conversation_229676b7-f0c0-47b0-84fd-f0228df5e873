<?php

# change database field
$change_field = array();
$change_field['payment_extra_info'] = array(
    array(
        "field_name" => "orders_id",
        "field_attr" => " INT( 11 ) UNSIGNED NOT NULL"
    )
);
$change_field['whos_online'] = array(
    array(
        "field_name" => "last_page_url",
        "field_attr" => " VARCHAR( 255 ) NOT NULL DEFAULT ''"
    )
);
change_field_structure($change_field);

// Add new admin_files
$select_sql = "	SELECT admin_files_id FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_name = 'c2c.php'";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["c2c_rating_type.php"] = array("insert" => " ('c2c_rating_type.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='c2c_rating_type.php' AND admin_files_is_boxes=0 ");
    
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["c2c_seller_level_configuration.php"] = array("insert" => " ('c2c_seller_level_configuration.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='c2c_seller_level_configuration.php' AND admin_files_is_boxes=0 ");
}
// End of add new admin_files
?>