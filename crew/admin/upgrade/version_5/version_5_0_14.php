<?php

# update record
$update_sql = array();
$update_sql['c2c_configuration'] = array(
    array(
        "field_name" => "configuration_title",
        "update" => " configuration_title = 'Max Create Listing Limit (New Seller)' ",
        "where_str" => " configuration_key = 'C2C_DEFAULT_LISTING_LIMIT' "
    ),
    array(
        "field_name" => "configuration_title",
        "update" => " configuration_title = 'Max Create Listing Limit (Verified Seller)' ",
        "where_str" => " configuration_key = 'C2C_MAX_LISTING_LIMIT' "
    )
);
advance_update_records($update_sql, $DBTables);
unset($update_sql);

# delete record
$delete_sql = array();
$delete_sql['C2C_SELLER_MIN_COMPLETED_ORDER'] = array("unique" => "1");
delete_records(TABLE_C2C_CONFIGURATION, "configuration_key", $delete_sql, $DBTables);
unset($delete_sql);

# delete field
$delete_field = array();
$delete_field['c2c_customers'] = array(
    array("field_name" => "cronjob_seller_product_listing_limit"),
    array("field_name" => "cronjob_verified_seller")
);
delete_field($delete_field);
unset($delete_field);
?>