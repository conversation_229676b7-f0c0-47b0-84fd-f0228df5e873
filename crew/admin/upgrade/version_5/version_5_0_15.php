<?php

# change database field
$change_field = array();
$change_field['c2c_products_listing'] = array(
    array(
        "field_name" => "products_title",
        "field_attr" => " VARCHAR( 128 ) NOT NULL "
    )
);
change_field_structure($change_field);

# update record
$update_sql = array();
$update_sql['custom_products_type_child_lang'] = array(
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'In-Game Currency' ",
        "where_str" => " custom_products_type_child_id = '1' AND languages_id = '1' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'Account' ",
        "where_str" => " custom_products_type_child_id = '5' AND languages_id = '1' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'In-Game Item' ",
        "where_str" => " custom_products_type_child_id = '16' AND languages_id = '1' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#28216;&#25103;&#24065;' ",
        "where_str" => " custom_products_type_child_id = '1' AND languages_id = '2' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#24080;&#21495;' ",
        "where_str" => " custom_products_type_child_id = '5' AND languages_id = '2' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#36938;&#25138;&#24163;' ",
        "where_str" => " custom_products_type_child_id = '1' AND languages_id = '3' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#24115;&#34399;' ",
        "where_str" => " custom_products_type_child_id = '5' AND languages_id = '3' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'Gold' ",
        "where_str" => " custom_products_type_child_id = '1' AND languages_id = '4' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'Akun' ",
        "where_str" => " custom_products_type_child_id = '5' AND languages_id = '4' "
    )
);
advance_update_records($update_sql, $DBTables);
unset($update_sql);
?>