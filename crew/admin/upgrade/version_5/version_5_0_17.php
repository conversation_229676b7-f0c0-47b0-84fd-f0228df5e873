<?php

# delete field
$delete_field = array();

$delete_field['c2c_products_listing'] = array(
    array("field_name" => "products_original_price")
);

delete_field($delete_field);
unset($delete_field);

// Google Wallet tables
$add_new_tables = array();

$add_new_tables["google_wallet"] = array(
    "structure" => "CREATE TABLE `google_wallet` (
                        `google_wallet_order_id` int(11) unsigned NOT NULL DEFAULT '0',
                        `google_wallet_merchant_id` varchar(20) DEFAULT NULL,
                        `google_wallet_create_date` datetime DEFAULT NULL,
                        `google_wallet_expired_date` datetime DEFAULT NULL,
                        `google_wallet_transaction_id` varchar(80) DEFAULT NULL,
                        `google_wallet_amount` decimal(15,2) DEFAULT NULL,
                        `google_wallet_currency` char(3) DEFAULT NULL,
                        `google_wallet_return_jwt` text,
                        `google_wallet_custom_signature` varchar(64) DEFAULT NULL,
                        PRIMARY KEY (`google_wallet_order_id`),
                        KEY `index_transaction_id` (`google_wallet_transaction_id`)
                      ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["google_wallet_status_history"] = array(
    "structure" => "CREATE TABLE `google_wallet_status_history` (
                        `google_wallet_status_history_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                        `google_wallet_order_id` int(11) unsigned NOT NULL DEFAULT '0',
                        `google_wallet_date` datetime DEFAULT NULL,
                        `google_wallet_status_id` varchar(32) DEFAULT NULL,
                        `google_wallet_description` varchar(128) DEFAULT NULL,
                        `google_wallet_changed_by` varchar(128) DEFAULT NULL,
                        PRIMARY KEY (`google_wallet_status_history_id`),
                        KEY `index_order_id` (`google_wallet_order_id`)
                      ) ENGINE=MyISAM  DEFAULT CHARSET=utf8;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

?>