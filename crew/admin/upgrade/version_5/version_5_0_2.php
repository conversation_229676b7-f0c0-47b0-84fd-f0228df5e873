<?php

$add_new_tables["c2c_customer_setting"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_customer_setting` (
                      `customer_id` int(11) unsigned NOT NULL,
                      `key` varchar(64) NOT NULL,
                      `value` varchar(255) DEFAULT NULL,
                      `created_datetime` datetime NOT NULL,
                      `updated_datetime` datetime NOT NULL,
                      PRIMARY KEY (`customer_id`,`key`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_products_listing_attributes"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_listing_attributes` (
                      `c2c_products_listing_id` int(11) unsigned NOT NULL,
                      `key` varchar(255) NOT NULL COMMENT 'refer game_attribute_list',
                      `value` text NOT NULL,
                      PRIMARY KEY (`c2c_products_listing_id`,`key`),
                      KE<PERSON> `key` (`key`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["c2c_products_listing_extra_info"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_products_listing_extra_info` (
                      `c2c_products_listing_id` int(11) unsigned NOT NULL,
                      `key` varchar(255) NOT NULL COMMENT '`media`=image url, `hla_account_id`=HLA supplier acc id, `hla_reserve_id`=RandyRun reserve id, `hla_ref_id`=HLA supplier ref',
                      `value` text NOT NULL,
                      PRIMARY KEY (`c2c_products_listing_id`,`key`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["game_attribute"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `game_attribute` (
                      `game_attribute_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                      `game_id` int(11) unsigned NOT NULL,
                      `key` varchar(255) NOT NULL COMMENT 'refer game_attribute_list',
                      `media` varchar(16) NULL,
                      PRIMARY KEY (`game_attribute_id`),
                      KEY `game_id` (`game_id`,`key`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["game_attribute_description"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `game_attribute_description` (
                      `game_attribute_description_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                      `game_attribute_id` int(11) unsigned NOT NULL,
                      `language_id` int(3) unsigned NOT NULL DEFAULT '1',
                      `value` varchar(255) NOT NULL,
                      PRIMARY KEY (`game_attribute_description_id`),
                      KEY `game_attribute_id` (`game_attribute_id`,`language_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["game_attribute_list"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `game_attribute_list` (
                      `key` varchar(255) NOT NULL,
                      PRIMARY KEY (`key`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");

$add_new_tables["game_attribute_setting"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `game_attribute_setting` (
                      `game_attribute_setting_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                      `game_id` int(11) unsigned NOT NULL,
                      `rule` text NOT NULL,
                      PRIMARY KEY (`game_attribute_setting_id`),
                      KEY `game_id` (`game_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

// Update table
// Delete fields
$delete_field = array();

$delete_field['c2c_products_listing'] = array  ( array( "field_name" => "c2c_products_instant_id") );
$delete_field['c2c_customers'] = array  ( array( "field_name" => "report_listing_counter"), array( "field_name" => "last_report_listing_date") );
$delete_field['c2c_buyback'] = array  ( array( "field_name" => "show_restock") );

delete_field ($delete_field);
// End of delete fields

tep_db_query("ALTER TABLE `c2c_products_listing` CHANGE `products_description` `products_description` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL");
tep_db_query("ALTER TABLE `c2c_products_listing` CHANGE `products_status` `products_status` ENUM( '-1', '0', '1', '2' ) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '-1=Expire, 0=Inactive, 1=Active, 2=Delete'");
tep_db_query("ALTER TABLE `c2c_products_listing` CHANGE `delivery_mode` `delivery_mode` VARCHAR( 20 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'refer products_delivery_mode'");
tep_db_query("ALTER TABLE `c2c_buyback` CHANGE `buyback_status_id` `status` INT( 3 ) UNSIGNED NOT NULL COMMENT 'refer buyback_status'");
tep_db_query("ALTER TABLE `c2c_buyback` CHANGE `orders_tag_ids` `orders_tag_ids` VARCHAR( 255 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL");
tep_db_query("ALTER TABLE `c2c_buyback` CHANGE `orders_read_mode` `orders_read_mode` ENUM( '1', '0' ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL");
tep_db_query("ALTER TABLE `c2c_buyback` CHANGE `c2c_buyback_locked_by` `locked_by` VARCHAR( 128 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL");
tep_db_query("ALTER TABLE `c2c_buyback` CHANGE `c2c_buyback_locked_from_ip` `locked_from_ip` VARCHAR( 20 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL");
tep_db_query("ALTER TABLE `c2c_buyback` CHANGE `c2c_buyback_locked_datetime` `locked_datetime` DATETIME NULL DEFAULT NULL");
tep_db_query("ALTER TABLE `c2c_buyback_history` CHANGE `buyback_status_id` `status` INT( 3 ) UNSIGNED NOT NULL COMMENT  'refer buyback_status'");
tep_db_query("ALTER TABLE `c2c_buyback_history` CHANGE `customer_notified` `seller_notified` TINYINT( 1 ) UNSIGNED NOT NULL");
tep_db_query("ALTER TABLE `c2c_buyback_history` CHANGE `set_as_buyback_remarks` `set_as_remarks` ENUM( '1', '0' ) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0'");
tep_db_query("ALTER TABLE `c2c_buyback_product` CHANGE `c2c_buyback_id` `c2c_buyback_id` INT( 11 ) UNSIGNED NOT NULL");
tep_db_query("ALTER TABLE `c2c_buyback_product` CHANGE `buyback_dealing_type` `buyback_dealing_type` VARCHAR( 32 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'refer products_delivery_mode'");
tep_db_query("ALTER TABLE `c2c_seller_groups` CHANGE `total_completed_sales_amount` `total_completed_sales_amount` DECIMAL( 15, 2 ) UNSIGNED NULL DEFAULT NULL");

$add_new_field = array();
$add_new_field['c2c_products_listing'] = array(
    array(
        "field_name" => "custom_products_type_child_id",
        "field_attr" => "int(11) unsigned NOT NULL",
        "add_after" => 'custom_products_type'
    ),
    array(
        "field_name" => "listing_type",
        "field_attr" => "enum('1','2') NOT NULL DEFAULT '1' COMMENT '1=normal, 2=auction'",
        "add_after" => 'custom_products_type_child_id'
    ),
    array(
        "field_name" => "forecast_quantity",
        "field_attr" => "int(11) NOT NULL DEFAULT '0'",
        "add_after" => 'guarantee_delivery_speed'
    ),
);

$add_new_field['c2c_buyback_history'] = array(
    array(
        "field_name" => "changed_by",
        "field_attr" => "varchar(128) NULL DEFAULT NULL",
        "add_after" => 'set_as_remarks'
    )
);

$add_new_field['c2c_buyback_product'] = array(
    array(
        "field_name" => "custom_products_type_child_id",
        "field_attr" => "int(11) unsigned NOT NULL",
        "add_after" => 'custom_products_type'
    ),
    array(
        "field_name" => "margin",
        "field_attr" => "decimal( 8, 4 ) NOT NULL",
        "add_after" => 'c2c_products_listing_id'
    ),
    array(
        "field_name" => "buyback_unit_price_usd",
        "field_attr" => "decimal( 15, 8 ) NOT NULL",
        "add_after" => 'buyback_amount'
    ),
    array(
        "field_name" => "buyback_amount_usd",
        "field_attr" => "decimal( 15, 8 ) NOT NULL",
        "add_after" => 'buyback_unit_price_usd'
    )
);
add_field($add_new_field);

// Delete tables
$delete_tables_array = array();

$delete_tables_array = array('c2c_products_hla', 'c2c_products_hla_attributes', 'c2c_products_hla_characters', 'c2c_products_hla_description', 'c2c_products_item_attributes', 'c2c_products_listing_media');

delete_tables ($delete_tables_array, $DBTables);
// End of delete tables
?>