<?php

$updateTable = array('c2c_buyback_order_rating', 'c2c_buyback_order_total_rating', 'c2c_rating_type', 'c2c_seller_level_configuration');
$cptTocpc = array(0 => 1, 4 => 5, 5 => 12);
foreach ($updateTable as $updateTableValue) {

    // Insert new fields into current table
    $add_new_field = array();
    $add_new_field[$updateTableValue] = array(
        array("field_name" => "custom_products_type_child_id",
            "field_attr" => " INT(11) UNSIGNED NOT NULL ",
            "add_after" => 'custom_product_type'
        ),
    );
    add_field($add_new_field);
    // End of insert new fields into current table

    foreach ($cptTocpc as $cpt => $cpc) {
// Update custom_products_type_child_id by custom_product_type
        $update_sql = array();

        $update_sql[$cpt] = array("update" => " custom_products_type_child_id= " . $cpc . " ");

        update_records($updateTableValue, "custom_product_type", $update_sql, $DBTables);
// End of update custom_products_type_child_id by custom_product_type
    }
}

// Delete tables
$delete_tables_array = array();

$delete_tables_array = array('c2c_buyback_product');

delete_tables($delete_tables_array, $DBTables);
// End of delete tables

$add_new_tables["c2c_buyback_product"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_buyback_product` (
                      `c2c_buyback_product_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                      `c2c_buyback_id` int(11) unsigned NOT NULL,
                      `game_id` int(11) unsigned NOT NULL,
                      `product_id` int(11) unsigned NOT NULL,
                      `product_name` varchar(255) NOT NULL,
                      `custom_products_type` int(11) unsigned NOT NULL,
                      `custom_products_type_child_id` int(11) unsigned NOT NULL,
                      `c2c_products_listing_id` int(11) unsigned NOT NULL,
                      `margin` decimal(8,4) NOT NULL,
                      `purchase_quantity` int(11) unsigned NOT NULL COMMENT 'buyer purchase quantity',
                      `delivered_quantity` int(11) unsigned NOT NULL,
                      `product_unit_price` decimal(15,8) NOT NULL COMMENT 'set by seller',
                      `product_unit_price_usd` decimal(15,8) NOT NULL COMMENT 'set by seller',
                      `after_fee_unit_price` decimal(15,8) NOT NULL,
                      `after_fee_unit_price_usd` decimal(15,8) NOT NULL,
                      `buyer_character` varchar(100) DEFAULT NULL,
                      `delivery_mode` varchar(32) DEFAULT NULL COMMENT 'refer `products_delivery_mode`',
                      `total_screenshot` int(3) unsigned NOT NULL,
                      `orders_id` int(11) unsigned NOT NULL,
                      `orders_products_id` int(11) unsigned NOT NULL,
                      PRIMARY KEY (`c2c_buyback_product_id`),
                      KEY `index_c2c_buyback_id` (`c2c_buyback_id`),
                      KEY `index_game_id` (`game_id`),
                      KEY `index_orders_id` (`orders_id`),
                      KEY `index_orders_products_id` (`orders_products_id`)
                    ) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
?>