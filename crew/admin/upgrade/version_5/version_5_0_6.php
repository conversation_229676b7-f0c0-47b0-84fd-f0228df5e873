<?php

/* G2G Setting */
# add new field
$add_new_field = array();
$add_new_field['c2c_buyback_order_rating'] = array(
    array(
        "field_name" => "order_product_id",
        "field_attr" => "INT(11) unsigned NOT NULL COMMENT 'Rate by each product'",
        "add_after" => 'order_id'
    )
);
$add_new_field['c2c_customers'] = array(
    array(
        "field_name" => "newsletter",
        "field_attr" => "ENUM( '1', '0' ) NOT NULL DEFAULT '0'",
        "add_after" => 'seller_product_listing_limit'
    ),
    array(
        "field_name" => "created_date",
        "field_attr" => "DATETIME NOT NULL",
        "add_after" => 'cronjob_verified_seller'
    ),
    array(
        "field_name" => "verified_date",
        "field_attr" => "DATETIME NULL",
        "add_after" => 'created_date'
    )
);
add_field($add_new_field);

# delete field
$delete_field = array();
$delete_field['c2c_buyback_order_rating'] = array(
    array("field_name" => "custom_product_type"),
    array("field_name" => "custom_products_type_child_id")
);
$delete_field['c2c_buyback_order_total_rating'] = array(
    array("field_name" => "custom_product_type"),
    array("field_name" => "custom_products_type_child_id")
);
delete_field($delete_field);

# change database field
$change_field = array();
$change_field['c2c_buyback_order_rating'] = array(
    array(
        "field_name" => "customer_id",
        "field_new_name" => "seller_id",
        "field_attr" => " INT( 11 ) UNSIGNED NOT NULL DEFAULT '0'"
    ),
    array(
        "field_name" => "rating_id",
        "field_new_name" => "c2c_rating_type_id",
        "field_attr" => " TINYINT( 3 ) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'refer c2c_rating_type table' "
    )
);

$change_field['c2c_buyback_order_total_rating'] = array(
    array(
        "field_name" => "customer_id",
        "field_new_name" => "seller_id",
        "field_attr" => " INT( 11 ) UNSIGNED NOT NULL DEFAULT '0'"
    ),
    array(
        "field_name" => "rating_id",
        "field_new_name" => "c2c_rating_type_id",
        "field_attr" => " TINYINT( 3 ) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'refer c2c_rating_type table' "
    ),
    array(
        "field_name" => "average_rating",
        "field_new_name" => "current_rating",
        "field_attr" => " DECIMAL( 6, 4 ) NOT NULL COMMENT 'rating type 1 = total_rating / total_order, rating type 2 = refer c2c_seller_level_configuration table formula' "
    )
);
$change_field['c2c_rating_type'] = array(
    array(
        "field_name" => "custom_products_type_child_id",
        "field_attr" => " TINYINT( 3 ) UNSIGNED NOT NULL COMMENT 'refer custom_products_type_child table' "
    ),
    array(
        "field_name" => "rating_type",
        "field_attr" => " ENUM( '1', '2' ) NOT NULL COMMENT '1=dynamic rating, 2=overall' "
    )
);
$change_field['c2c_customers'] = array(
    array(
        "field_name" => "verified_seller",
        "field_attr" => " ENUM( '0',  '1' ) NOT NULL DEFAULT '0' COMMENT '1 = seller passed the verification during Sign Up as Seller' "
    )
);
change_field_structure($change_field);

# insert c2c seller group record
if (in_array('c2c_seller_groups', $DBTables)) {
    tep_db_query("TRUNCATE TABLE " . 'c2c_seller_groups');

    $insert_sql = array();
    $insert_sql[] = array("insert" => " ( '1', 'Normal Seller', '0.00', '0', '10' ) ");
    insert_new_records('c2c_seller_groups', "seller_group_id", $insert_sql, $DBTables, "(seller_group_id, seller_group_name, total_completed_sales_amount, payout_grace_period_offset, sort_order)");
}

# insert rating record
$_g2g_cpt = array(0, 4, 5);
$_rate = array(
    0 => array(
        'type' => 1,
        'desc' => array(
            1 => 'Item as described',
            2 => '描述相符',
            3 => '描述相符',
            4 => 'Item yang dijelaskan'
        )
    ),
    1 => array(
        'type' => 1,
        'desc' => array(
            1 => 'Delivery Speed',
            2 => '发货速度',
            3 => '發貨速度',
            4 => 'Kecepatan Penghantaran'
        )
    ),
    2 => array(
        'type' => 1,
        'desc' => array(
            1 => 'Service & Communication',
            2 => '服务态度',
            3 => '服務態度',
            4 => 'Servis & Komunikasi'
        )
    ),
    3 => array(
        'type' => 2,
        'desc' => array(
            1 => 'Overall Impression',
            2 => '总评价',
            3 => '總評價',
            4 => 'Impresi Kesuluruhan'
        )
    ),
);

if (in_array('c2c_rating_type', $DBTables)) {
    tep_db_query("TRUNCATE TABLE " . 'c2c_rating_type');
}

if (in_array('c2c_rating_type_description', $DBTables)) {
    tep_db_query("TRUNCATE TABLE " . 'c2c_rating_type_description');
}

foreach ($_g2g_cpt as $_cpt) {
    $_res = tep_db_query("SELECT custom_products_type_child_id FROM custom_products_type_child WHERE custom_products_type_id = " . $_cpt);
    while ($_row = tep_db_fetch_array($_res)) {
        for ($i = 0, $cnt = count($_rate); $cnt > $i; $i++) {
            $_sort = ((10 * $i) + 10);

            $insert_sql = array();
            $insert_sql[] = array("insert" => " ( '" . $_cpt . "', '" . $_row['custom_products_type_child_id'] . "', '" . $_rate[$i]['type'] . "', '1', '" . $_sort . "' ) ");
            insert_new_records('c2c_rating_type', "custom_products_type_child_id", $insert_sql, $DBTables, "(custom_product_type, custom_products_type_child_id, rating_type, display_status, sort_order)");
            $_pk = tep_db_insert_id();

            if ($_pk) {
                for ($j = 1, $jcnt = count($_rate[$i]['desc']); $jcnt >= $j; $j++) {
                    $insert_sql = array();
                    $insert_sql[] = array("insert" => " ( '" . $_pk . "', '" . $j . "', '" . $_rate[$i]['desc'][$j] . "' ) ");
                    insert_new_records('c2c_rating_type_description', "c2c_rating_type_id", $insert_sql, $DBTables, "(c2c_rating_type_id, language_id, rating_description)", " c2c_rating_type_id = " . $_pk . " AND language_id = " . $j);
                }
            }
        }
    }
}

/* OffGamers Store Setting */
# update OffGamers Point to WOR Token
$update_sql = array();
$update_sql['configuration_group'] = array(
    array(
        "field_name" => "configuration_group_title",
        "update" => " configuration_group_title = 'WOR Token'",
        "where_str" => " configuration_group_title = 'OffGamers Point'"
    ),
    array(
        "field_name" => "configuration_group_description",
        "update" => " configuration_group_description = 'WOR Token Configuration'",
        "where_str" => " configuration_group_description = 'OffGamers Point Configuration'"
    )
);
$update_sql['configuration'] = array(
    array(
        "field_name" => "configuration_title",
        "update" => " configuration_title = 'WOR Token Minimum Redemption'",
        "where_str" => " configuration_title = 'OffGamers Point Minimum Redemption'"
    ),
    array(
        "field_name" => "configuration_title",
        "update" => " configuration_title = 'WOR Token Credit Duration'",
        "where_str" => " configuration_title = 'OffGamers Point Credit Duration'"
    ),
    array(
        "field_name" => "configuration_description",
        "update" => " configuration_description = 'WOR Token credit duration (minutes)'",
        "where_str" => " configuration_description = 'OffGamers Point credit duration (minutes)'"
    )
);
advance_update_records($update_sql, $DBTables);
?>