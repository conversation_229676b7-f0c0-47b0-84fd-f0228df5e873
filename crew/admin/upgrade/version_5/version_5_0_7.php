<?php

// Delete tables
$delete_tables_array = array('c2c_buyback_cron', 'c2c_mailbox', 'c2c_mailbox_group');
delete_tables($delete_tables_array, $DBTables);
// End of delete tables

// Change primary key
tep_db_query("ALTER TABLE `orders_notification` DROP PRIMARY KEY, ADD PRIMARY KEY ( `customers_id`, `orders_id`, `orders_type` )");

$add_new_tables = array();
$add_new_tables["c2c_mailbox"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_mailbox` (
                    `mailbox_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                    `transaction_id` int(11) NOT NULL COMMENT 'Seller Order ID',
                    `transaction_type` varchar(5) NOT NULL,
                    `sender_id` int(11) NOT NULL,
                    `recipient_id` int(11) NOT NULL,
                    `message` text NOT NULL,
                    `user1_message_flag` enum('0','1') NOT NULL COMMENT 'If user1 delete the mail group, 0 = hide, 1 = show',
                    `user2_message_flag` enum('0','1') NOT NULL COMMENT 'If user2 delete the mail group, 0 = hide, 1 = show',
                    `view_status` enum('0','1') NOT NULL COMMENT 'For recipient, 0 = unread, 1 = already view',
                    `datetime` datetime NOT NULL,
                    PRIMARY KEY (`mailbox_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1;",
    "data" => "");
$add_new_tables["c2c_mailbox_group"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_mailbox_group` (
                    `transaction_id` int(11) NOT NULL COMMENT 'Seller Order ID',
                    `transaction_type` varchar(5) NOT NULL,
                    `user1_id` int(11) NOT NULL COMMENT 'Seller ID',
                    `user1_displayname` varchar(20) NOT NULL COMMENT 'Seller Order ID',
                    `user2_id` int(11) NOT NULL COMMENT 'Buyer ID',
                    `user2_displayname` varchar(20) NOT NULL COMMENT 'Customer Order ID',
                    `status` enum('0','1') NOT NULL COMMENT '1 when SO created and 0 when SO cancel or completed',
                    `title` varchar(255) NOT NULL COMMENT 'Product Listing Title',
                    `create_date` datetime NOT NULL,
                    `last_modify` datetime NOT NULL,
                    PRIMARY KEY (`transaction_id`,`transaction_type`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);
?>