<?php

/* G2G Setting */
# add new field
$add_new_field = array();
$add_new_field['c2c_buyback_history'] = array(
    array(
        "field_name" => "c2c_buyback_product_id",
        "field_attr" => "INT( 11 ) UNSIGNED NOT NULL",
        "add_after" => 'c2c_buyback_id'
    ),
    array(
        "field_name" => "orders_products_history_id",
        "field_attr" => "INT( 11 ) UNSIGNED NOT NULL COMMENT 'refer orders_products_history'",
        "add_after" => 'set_as_remarks'
    )
);
add_field($add_new_field);

// Insert new records into c2c_configuration table (C2C Configuration)
$conf_insert_sql = array();

# G2G Mail Setting
$conf_insert_sql["G2G_STORE_NAME"] = array("insert" => " ('Store Name', 'G2G_STORE_NAME', 'G2G.com', 'G2G Store Name', 'G2G_STORE_INFORMATION', 10, NULL, NOW(), NULL, '') ");
$conf_insert_sql["G2G_STORE_OWNER"] = array("insert" => " ('Store Owner', 'G2G_STORE_OWNER', 'G2G.com Team', 'G2G Store Owner', 'G2G_STORE_INFORMATION', 20, NULL, NOW(), NULL, '') ");
$conf_insert_sql["G2G_EMAIL_SUBJECT_PREFIX"] = array("insert" => " ('Email Subject Prefix', 'G2G_EMAIL_SUBJECT_PREFIX', '[G2G]', 'G2G Email Subject Prefix', 'G2G_STORE_INFORMATION', 30, NULL, NOW(), NULL, '') ");
$conf_insert_sql["G2G_EMAIL_TO"] = array("insert" => " ('Email To', 'G2G_EMAIL_TO', '<EMAIL>', 'G2G Store Name', 'Recipient Email Address', 40, NULL, NOW(), NULL, '') ");
$conf_insert_sql["G2G_STORE_OWNER_EMAIL_ADDRESS"] = array("insert" => " ('Store Owner Email Address', 'G2G_STORE_OWNER_EMAIL_ADDRESS', '<EMAIL>', 'Sender Email Address', 'G2G_STORE_INFORMATION', 50, NULL, NOW(), NULL, '') ");
$conf_insert_sql["G2G_EMAIL_FOOTER"] = array("insert" => " ('Email Footer', 'G2G_EMAIL_FOOTER', 'G2G.com Team', 'Email Footer', 'G2G_STORE_INFORMATION', 60, NULL, NOW(), NULL, '') ");

insert_new_records(TABLE_C2C_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
?>