<?php

$add_new_tables = array();
$add_new_tables["smart2pay_globalpay_status_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `smart2pay_globalpay_status_history` (
                    `smart2pay_globalpay_status_history_id` int(11) unsigned NOT NULL auto_increment,
                    `smart2pay_globalpay_order_id` int(11) unsigned NOT NULL default '0',
                    `smart2pay_globalpay_date` datetime default NULL,
                    `smart2pay_globalpay_status_id` tinyint(2) default NULL,
                    `smart2pay_globalpay_description` varchar(128) default NULL,
                    `smart2pay_globalpay_changed_by` varchar(128) default NULL,
                    PRIMARY KEY (`smart2pay_globalpay_status_history_id`),
                    KEY `index_order_id` (`smart2pay_globalpay_order_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");
$add_new_tables["smart2pay_globalpay"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `smart2pay_globalpay` (
                    `smart2pay_globalpay_order_id` int(11) unsigned NOT NULL default '0',
                    `smart2pay_globalpay_merchant_id` varchar(12) default NULL,
                    `smart2pay_globalpay_method_id_returned` varchar(20) default NULL,
                    `smart2pay_globalpay_transaction_id` varchar(20) default NULL,
                    `smart2pay_globalpay_amount` decimal(15,2) default NULL,
                    `smart2pay_globalpay_currency` char(3) default NULL,
                    `smart2pay_globalpay_status_id` tinyint(2) default NULL,
                    `smart2pay_globalpay_return_info` varchar(128) default NULL,
                    `smart2pay_globalpay_hash` varchar(64) default NULL,
                    PRIMARY KEY (`smart2pay_globalpay_order_id`),
                    KEY `index_transaction_id` (`smart2pay_globalpay_transaction_id`)
                    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);

// Insert new records into admin_files table (for Social Profile)
$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . " 
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	
	$admin_files_insert_sql["social_profile.php"] = array("insert" => " ('social_profile.php', 0, '".$row_sql['admin_files_id']."', '1') ");
	
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}
// End of Insert new records into admin_files table (for Social Profile)


/* Product Listing Remarks History */
# delete field
$delete_field = array();
$delete_field['c2c_products_listing_remarks_history'] = array(
    array("field_name" => "c2c_products_listing_remarks_history_id"),
    array("field_name" => "remarks")
);
delete_field($delete_field);

# add new field
$add_new_field = array();
$add_new_field['c2c_products_listing_remarks_history'] = array(
    array(
        "field_name" => "log_action",
        "field_attr" => "VARCHAR( 16 ) NULL",
        "add_after" => 'c2c_products_listing_id'
    ),
    array(
        "field_name" => "log_controller_action",
        "field_attr" => "VARCHAR( 32 ) NULL",
        "add_after" => 'log_action'
    ),
    array(
        "field_name" => "remarks_before_changes",
        "field_attr" => "TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL",
        "add_after" => 'log_controller_action'
    ),
    array(
        "field_name" => "remarks_after_changes",
        "field_attr" => "TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL",
        "add_after" => 'remarks_before_changes'
    ),
    array(
        "field_name" => "user_ip",
        "field_attr" => "VARCHAR( 16 ) NULL",
        "add_after" => 'remarks_after_changes'
    ),
    array(
        "field_name" => "user_role",
        "field_attr" => "VARCHAR( 16 ) NULL DEFAULT 'seller'",
        "add_after" => 'user_ip'
    )
);
add_field($add_new_field);

?>