<?php
// Insert new records into configuration table (for Threat Metrix module)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Stock Options'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	$conf_insert_sql["AUTO_DELIVERY_SC_ENABLED"] = array("insert" => " ('Enable Auto Delivery Store Credit', 'AUTO_DELIVERY_SC_ENABLED', 'false', 'Enable Auto Delivery Store Credit?', ".$row_sql["configuration_group_id"].", 110, NULL, now(), NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Threat Metrix module)

tep_db_query("ALTER TABLE `temp_process` ENGINE = InnoDB");

$add_new_tables = array();
$add_new_tables["orders_delivery_queue"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `orders_delivery_queue` (
                    `orders_products_id` int(11) unsigned NOT NULL,
                    `orders_id` int(11) unsigned NOT NULL,
                    `extra_info` text NOT NULL COMMENT 'Data store in json format',
                    `created_datetime` datetime NOT NULL,
                    PRIMARY KEY (`orders_products_id`)
                  ) ENGINE=MyISAM DEFAULT CHARSET=utf8;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);

// Insert new schedule task
$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_auto_delivery.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);

if (!tep_db_num_rows($cron_progress_task_result_sql)) {
	$cron_process_track_array = array(	'cron_process_track_in_action' => 0,
				                        'cron_process_track_start_date' => 'now()',
				                        'cron_process_track_failed_attempt' => 0,
				                        'cron_process_track_filename' => 'cron_auto_delivery.php'
			                       );
	tep_db_perform('cron_process_track', $cron_process_track_array);
}
?>