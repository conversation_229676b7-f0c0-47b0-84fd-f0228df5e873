<?php

# update data
$update_sql = array();
$update_sql[TABLE_ORDERS_NOTIFICATION] = array(
    array(
        "field_name" => "site_id",
        "update" => " site_id = 0 ",
        "where_str" => " site_id = 1 "
    )
);
advance_update_records($update_sql, $DBTables);

# remove Cancel order status from Notification
$select_sql = " SELECT osn.orders_id, osn.orders_type, osn.site_id 
                FROM " . TABLE_ORDERS_NOTIFICATION . " AS osn
                LEFT JOIN " . TABLE_ORDERS . " AS o
                    ON o.orders_id = osn.orders_id
                LEFT JOIN " . TABLE_C2C_BUYBACK . " AS cb
                    ON cb.c2c_buyback_id = osn.orders_id
                LEFT JOIN " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
                    ON brg.buyback_request_group_id = osn.orders_id
                WHERE (IF(osn.orders_type = 'CO' AND (osn.site_id = 0 OR osn.site_id = 5), o.orders_status = 5, 
                        IF(osn.orders_type = 'BO' AND osn.site_id = 5, cb.status = 4, 
                        IF(osn.orders_type = 'BO' AND osn.site_id = 0, brg.buyback_status_id = 4, 0))))";
$result_sql = tep_db_query($select_sql);
while ($row_sql = tep_db_fetch_array($result_sql)) {
    # delete record
    tep_db_query("DELETE FROM " . TABLE_ORDERS_NOTIFICATION . " WHERE orders_id = " . $row_sql['orders_id'] . " AND orders_type = '" . $row_sql['orders_type'] . "' AND site_id = " . $row_sql['site_id']);
}

# change primary key
tep_db_query("ALTER TABLE `orders_notification` DROP PRIMARY KEY, ADD PRIMARY KEY ( `orders_id`, `orders_type`, `site_id` )");
?>