<?php

// Create new table
$add_new_tables["supplier_login_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `supplier_login_history` (
                        `supplier_id` int(11) unsigned NOT NULL,
                        `email_address` varchar(96) COLLATE utf8_unicode_ci NOT NULL,
                        `login_date` datetime NOT NULL,
                        `login_ip` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
                        `ua_info` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
                        `session_key` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
                        `session` text COLLATE utf8_unicode_ci NOT NULL
                      ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);
// End of create new table
?>