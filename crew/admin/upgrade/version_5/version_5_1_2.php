<?php
# update record
$update_sql = array();
$update_sql['custom_products_type_child_lang'] = array(
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#36141;&#29289;&#20195;&#37329;&#21173;' ",
        "where_str" => " custom_products_type_child_id = '4' AND languages_id = '2' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#36092;&#29289;&#20195;&#37329;&#21048;' ",
        "where_str" => " custom_products_type_child_id = '4' AND languages_id = '3' "
    ),
);
advance_update_records($update_sql, $DBTables);
unset($update_sql);

$add_new_tables = array();
$add_new_tables["log_rixty_response"] = array(
            "structure" => "CREATE TABLE IF NOT EXISTS `log_rixty_response` (
                `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                `method` varchar(16) NOT NULL,
                `description` varchar(255) NOT NULL,
                `amount` decimal(8,2) NOT NULL,
                `settle_amount` decimal(8,2) NOT NULL,
                `token` varchar(255) NOT NULL,
                `ack` varchar(16) NOT NULL,
                `serialnumber`  varchar (255) NOT NULL,
                `flag_state` char(1) NOT NULL COMMENT 'N: New, U: Uploading, S: In-stock, E: Error',
                `error_msg` varchar(255) NOT NULL,
                `created_datetime` datetime NOT NULL,
                PRIMARY KEY (`id`)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=0;",
            "data" => "");
add_new_tables($add_new_tables, $DBTables);
?>