<?php

// Insert new country
$currency_sel_sql = "SELECT currencies_id FROM " . TABLE_CURRENCIES . " WHERE code = 'ISL'";
$currency_res_sql = tep_db_query($currency_sel_sql);
$currency_row = tep_db_fetch_array($currency_res_sql);

$country_array = array(
    'countries_name' => 'Palestine',
    'countries_iso_code_2' => 'PS',
    'countries_iso_code_3' => 'PSE',
    'countries_currencies_id' => (isset($currency_row['currencies_id']) ? $currency_row['currencies_id'] : 1),
    'countries_international_dialing_code' => '970',
    'address_format_id' => 1,
    'maxmind_support' => 1,
    'countries_display' => 1,
    'telesign_support' => 1
);

$country_sel_sql = "SELECT countries_id
                    FROM countries
                    WHERE countries_name = 'Palestine'
                        AND countries_iso_code_2 = 'PS'";
$country_res_sql = tep_db_query($country_sel_sql);
if (!tep_db_num_rows($country_res_sql)) {
    tep_db_perform('countries', $country_array);
} else {
    $country_row = tep_db_fetch_array($country_res_sql);
    tep_db_perform('countries', $country_array, 'update', "countries_id = " . $country_row['countries_id']);
}
?>