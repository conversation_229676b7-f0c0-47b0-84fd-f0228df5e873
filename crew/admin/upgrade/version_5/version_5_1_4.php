<?php

# add new field
$add_new_field = array();
$add_new_field['maxmind_history'] = array(
    array(
        "field_name" => "ip_corporate_proxy",
        "field_attr" => "VARCHAR( 9 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL",
        "add_after" => 'ip_organization'
    ),
    array(
        "field_name" => "is_trans_proxy",
        "field_attr" => "VARCHAR( 9 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL",
        "add_after" => 'ip_organization'
    ),
    array(
        "field_name" => "ip_net_speed_cell",
        "field_attr" => "VARCHAR( 16 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL",
        "add_after" => 'ip_organization'
    ),
    array(
        "field_name" => "ip_user_type",
        "field_attr" => "VARCHAR( 32 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL",
        "add_after" => 'ip_organization'
    ),
    array(
        "field_name" => "ip_time_zone",
        "field_attr" => "VARCHAR( 32 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL",
        "add_after" => 'ip_organization'
    ),
    array(
        "field_name" => "ip_area_code",
        "field_attr" => "VARCHAR( 9 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL",
        "add_after" => 'ip_organization'
    ),
    array(
        "field_name" => "ip_postal_code",
        "field_attr" => "VARCHAR( 9 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL",
        "add_after" => 'ip_organization'
    ),
    array(
        "field_name" => "ip_accuracy_radius",
        "field_attr" => "INT( 11 ) UNSIGNED NOT NULL",
        "add_after" => 'ip_organization'
    )
);
add_field($add_new_field);
?>