<?php
$add_new_tables = array();
$add_new_tables["coda"] = array(
            "structure" => "CREATE TABLE `coda` (
							  `coda_order_id` int(11) unsigned NOT NULL DEFAULT '0',
							  `coda_txn_id` varchar(80) DEFAULT NULL,
							  `coda_result_code` int(11) DEFAULT NULL,
							  `coda_total_price` decimal(15,2) DEFAULT NULL,
							  `coda_checksum` varchar(32) DEFAULT NULL,
							  PRIMARY KEY (`coda_order_id`),
							  KEY `index_transaction_id` (`coda_txn_id`)
							) ENGINE=InnoDB DEFAULT CHARSET=utf8;",
            "data" => "");
$add_new_tables["coda_status_history"] = array(
            "structure" => "CREATE TABLE `coda_status_history` (
							`coda_status_history_id` int(11) NOT NULL AUTO_INCREMENT,
							`coda_orders_id` int(11) NOT NULL DEFAULT '0',
							`coda_date` datetime DEFAULT NULL,
							`coda_result_code` int(11) DEFAULT NULL,
							`coda_result_description` varchar(255) DEFAULT NULL,
							`coda_total_price` decimal(15,2) DEFAULT NULL,
							`changed_by` varchar(128) DEFAULT NULL,
							PRIMARY KEY (`coda_status_history_id`),
							KEY `index_orders_id` (`coda_orders_id`)
						  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
            "data" => "");
add_new_tables($add_new_tables, $DBTables);
?>
