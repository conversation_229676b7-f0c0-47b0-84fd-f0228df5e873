<?php

# add new field
$add_new_field = array();
$add_new_field['log_rixty_response'] = array(
    array(
        "field_name" => "custom_products_code_id",
        "field_attr" => "INT( 11 ) UNSIGNED NOT NULL default '0'",
        "add_after" => 'id'
    )
);
add_field($add_new_field);

// Define index key in log_rixty_response table
add_index_key ('log_rixty_response', 'index_custom_products_code_id', 'index', 'custom_products_code_id', $DBTables);
// End of define index key in log_rixty_response table

$cpc_sql = "    SELECT file_name, custom_products_code_id
                FROM custom_products_code
                WHERE custom_products_code_id > 3638402 AND remarks = 'RIXTY_API'";
$cpc_result = tep_db_query($cpc_sql);
while ($cpc_row = tep_db_fetch_array($cpc_result)) {
    $log_array = array(
        'custom_products_code_id' => $cpc_row['custom_products_code_id']
    );
    
    tep_db_perform('log_rixty_response', $log_array, 'update', "id = '" . $cpc_row['file_name'] . "'");
}
?>