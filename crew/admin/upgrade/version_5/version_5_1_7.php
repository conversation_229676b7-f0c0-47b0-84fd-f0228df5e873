<?php
// Insert new records into configuration_group table (for <PERSON> pending order to create within x mins by a customer)
$select_sql = "	SELECT configuration_group_id 
				FROM " . TABLE_CONFIGURATION_GROUP . "
				WHERE configuration_group_title='Store Information'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	$conf_insert_sql["B2C_MAX_NUM_OF_PENDING"] = array("insert" => " ('Maximum number of Pending Order', 'B2C_MAX_NUM_OF_PENDING', '5', 'Maximum number of Pending Order within X Minutes', ".$row_sql["configuration_group_id"].", 80, NULL, NOW(), NULL, '') ");
	$conf_insert_sql["B2C_MAX_PENDING_WITHIN_MIN"] = array("insert" => " ('Maximum Pending Order Within X Minutes', 'B2C_MAX_PENDING_WITHIN_MIN', '30', 'X Minutes to restrict maximum Pending Order', ".$row_sql["configuration_group_id"].", 85, NULL, NOW(), NULL, '') ");
	$conf_insert_sql["B2C_EXCEED_MAX_PENDING_RECIPIENT"] = array("insert" => " ('Recipient of Exceed Pending Order Notify Email', 'B2C_EXCEED_MAX_PENDING_RECIPIENT', '', 'Recipient of Buyer Exceed Pending Order Notification Email', ".$row_sql["configuration_group_id"].", 90, NULL, NOW(), NULL, '') ");
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of Insert new records into configuration_group table (for Max pending order to create within x mins by a customer)

// Insert new records into payment_methods_types and payment_methods_types_description table (for new Mobile Payment category type)
$select_sql = "	SELECT payment_methods_types_id 
				FROM " . TABLE_PAYMENT_METHODS_TYPES . "
				WHERE payment_methods_types_name='Mobile Payment'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$_pk = $row_sql['payment_methods_types_id'];
} else {
	$insert_sql = array();
	$insert_sql[] = array("insert" => " ( 'Mobile Payment', 'RECEIVE', 0, 'MAIN,SC', 700 ) ");
	insert_new_records(TABLE_PAYMENT_METHODS_TYPES, "payment_methods_types_name", $insert_sql, $DBTables, "(payment_methods_types_name, payment_methods_types_mode, payment_methods_types_system_define, payment_methods_types_sites, payment_methods_types_sort_order)");
	$_pk = tep_db_insert_id();
}

if ($_pk) {
	$insert_sql = array();
	$insert_sql[] = array("insert" => " ( " . $_pk . ", 1, 'Mobile Payment' ) ");
	insert_new_records(TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION, "payment_methods_types_description", $insert_sql, $DBTables, "(payment_methods_types_id, languages_id, payment_methods_types_description)", "payment_methods_types_id = " . $_pk . " AND languages_id = 1");

	$insert_sql = array();
	$insert_sql[] = array("insert" => " ( " . $_pk . ", 2, '移动支付'  ) ");
	insert_new_records(TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION, "payment_methods_types_description", $insert_sql, $DBTables, "(payment_methods_types_id, languages_id, payment_methods_types_description)", "payment_methods_types_id = " . $_pk . " AND languages_id = 2");
	
	$insert_sql = array();
	$insert_sql[] = array("insert" => " ( " . $_pk . ", 3, '移動支付'  ) ");
	insert_new_records(TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION, "payment_methods_types_description", $insert_sql, $DBTables, "(payment_methods_types_id, languages_id, payment_methods_types_description)", "payment_methods_types_id = " . $_pk . " AND languages_id = 3");
	
	$insert_sql = array();
	$insert_sql[] = array("insert" => " ( " . $_pk . ", 4, 'Pembayaran Ponsel'  ) ");
	insert_new_records(TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION, "payment_methods_types_description", $insert_sql, $DBTables, "(payment_methods_types_id, languages_id, payment_methods_types_description)", "payment_methods_types_id = " . $_pk . " AND languages_id = 4");
}
// End of Insert new records into payment_methods_types and payment_methods_types_description table (for new Mobile Payment category type)

// Update existing payment category to support multi-languages 
$update_sql = array();
$update_sql[TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION] = array(
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '信用卡、借记卡'",
        "where_str" => "payment_methods_types_id = 1 AND languages_id = 2"
    ),
	array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '电子钱包或预付卡支付'",
        "where_str" => "payment_methods_types_id = 2 AND languages_id = 2"
    ),
	array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '网上银行转帐（本地）'",
        "where_str" => "payment_methods_types_id = 3 AND languages_id = 2"
    ),
	array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '离线支付（1-5个工作日）'",
        "where_str" => "payment_methods_types_id = 4 AND languages_id = 2"
    ),
	array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '离线支付（1-5个工作日）'",
        "where_str" => "payment_methods_types_id = 7 AND languages_id = 2"
    ),
	array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '自动柜员机（ATM）第三方银行转帐'",
        "where_str" => "payment_methods_types_id = 8 AND languages_id = 2"
    ),
	array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '现金存款机（CDM）'",
        "where_str" => "payment_methods_types_id = 9 AND languages_id = 2"
    ),
);
advance_update_records($update_sql, $DBTables);

$insert_sql = array(
	array("insert" => " ( 1, 3, '信用卡、借記卡'  ) "),
	array("insert" => " ( 1, 4, 'Kartu Kredit/Debit'  ) "),
	array("insert" => " ( 2, 3, '電子錢包或預付卡支付'  ) "),
	array("insert" => " ( 2, 4, 'Dompet Electrik atau Pembayaran Kartu Prabayar'  ) "),
	array("insert" => " ( 3, 3, '網上銀行轉帳（本地）'  ) "),
	array("insert" => " ( 3, 4, 'Transfer Bank Online (Bank Lokal)'  ) "),
	array("insert" => " ( 4, 3, '離線支付（1-5個工作日）'  ) "),
	array("insert" => " ( 4, 4, 'Pembayaran Offline (1-5hari hari kerja)'  ) "),
	array("insert" => " ( 7, 3, '離線支付（1-5個工作日）'  ) "),
	array("insert" => " ( 7, 4, 'Pembayaran Offline (1-5hari hari kerja)'  ) "),
	array("insert" => " ( 8, 3, '自動櫃員機（ATM）第三方銀行轉帳'  ) "),
	array("insert" => " ( 8, 4, 'ATM / Transfer Bank Pihak Ketiga'  ) "),
	array("insert" => " ( 9, 3, '現金存款機（CDM）'  ) "),
	array("insert" => " ( 9, 4, 'Deposit Tunai'  ) "),
);
insert_new_records(TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION, "payment_methods_types_description", $insert_sql, $DBTables, "(payment_methods_types_id, languages_id, payment_methods_types_description)");
// End of Update existing payment category to support multi-languages
$add_new_tables = array();
$add_new_tables["adyen"] = array(
            "structure" => "CREATE TABLE `adyen` (
							`adyen_order_id` int(11) unsigned NOT NULL DEFAULT '0',
							`adyen_event_code` varchar(30) DEFAULT NULL,
							`adyen_psp_reference` bigint(20) DEFAULT NULL,
							`adyen_original_reference` bigint(20) DEFAULT NULL,
							`adyen_merchant_account_code` varchar(32) DEFAULT NULL,
							`adyen_event_date` varchar(32) DEFAULT NULL,
							`adyen_success` varchar(5) DEFAULT NULL,
							`adyen_payment_method` varchar(32) DEFAULT NULL,
							`adyen_operations` varchar(255) DEFAULT NULL,
							`adyen_reason` varchar(128) DEFAULT NULL,
							`adyen_currency` char(3) DEFAULT NULL,
							`adyen_amount` int(11) DEFAULT NULL,
							`adyen_cc_cvc_result` varchar(32) DEFAULT NULL,
							`adyen_cc_expiry_date` varchar(7) DEFAULT NULL,
							`adyen_cc_card_bin` int(6) DEFAULT NULL,
							`adyen_cc_card_summary` int(4) DEFAULT NULL,
							`adyen_cc_auth_code` varchar(11) DEFAULT NULL,
							`adyen_cc_fraud_score` int(3) DEFAULT NULL,
							`adyen_cc_extra_cost` int(6) DEFAULT NULL,
							`adyen_cc_three_d_auth` varchar(5) DEFAULT NULL,
							`adyen_cc_three_d_auth_offer` varchar(5) DEFAULT NULL,
							`adyen_cc_avs_result` varchar(255) DEFAULT NULL,
							`adyen_data` text DEFAULT NULL,
							`adyen_capture_request` tinyint(1) DEFAULT NULL,
							PRIMARY KEY (`adyen_order_id`),
							KEY `adyen_order_id` (`adyen_order_id`),
							KEY `index_transaction_id` (`adyen_psp_reference`)
						  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
            "data" => "");
$add_new_tables["adyen_status_history"] = array(
            "structure" => "CREATE TABLE `adyen_status_history` (
							`adyen_status_history_id` int(11) NOT NULL AUTO_INCREMENT,
							`adyen_orders_id` int(11) DEFAULT NULL,
							`adyen_event_code` varchar(30) DEFAULT NULL,
							`adyen_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
							`adyen_reason` varchar(128) DEFAULT NULL,
							`changed_by` varchar(128) DEFAULT NULL,
							PRIMARY KEY (`adyen_status_history_id`),
							KEY `index_orders_id` (`adyen_orders_id`)
						  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1 ;",
            "data" => "");
add_new_tables($add_new_tables, $DBTables);
?>
