<?php
tep_db_query('RENAME TABLE log_rixty_response TO log_api_restock');

$add_new_field = array();
$add_new_field['log_api_restock'] = array(
    array(
        "field_name" => "sku",
        "field_attr" => "VARCHAR( 32 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL",
        "add_after" => 'method'
    ),
    array(
        "field_name" => "api_provider",
        "field_attr" => "VARCHAR( 32 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL",
        "add_after" => 'error_msg'
    )
);
add_field($add_new_field);

$update_sql = "UPDATE log_api_restock SET api_provider = 'RIXTY', sku = CONCAT('RXT-',LPAD(ROUND(amount),3,'0'),'00-P') WHERE method IN ('CheckSKU', 'GetSKU') AND sku = ''";
tep_db_query($update_sql);
?>