<?php

/* Add New Table */
$add_new_tables["game_blog"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `game_blog` (
                `game_blog_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `sort_order` int(5) NOT NULL,
                PRIMARY KEY (`game_blog_id`)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1;",
    "data" => "");

$add_new_tables["game_blog_description"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `game_blog_description` (
                `game_blog_id` int(11) unsigned NOT NULL,
                `language_id` tinyint(2) NOT NULL,
                `game_blog_description` varchar(64) CHARACTER SET utf8 NOT NULL,
                PRIMARY KEY (`game_blog_id`,`language_id`)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["game_blog_categories"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `game_blog_categories` (
                      `game_blog_id` int(11) unsigned NOT NULL,
                      `categories_id` int(11) unsigned NOT NULL,
                      PRIMARY KEY (`game_blog_id`, `categories_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["frontend_template"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `frontend_template` (
                      `tpl_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                      `id` int(11) unsigned NOT NULL,
                      `id_type` tinyint(2) unsigned NOT NULL COMMENT '`0: Category, 1: Product, 2: Game`',
                      `background_source` varchar(255) NOT NULL,
                      `background_color` varchar(30) NOT NULL,
                      `logo_source` varchar(255) NOT NULL,
                      `tpl_status` TINYINT( 1 ) UNSIGNED NOT NULL DEFAULT '1',
                      PRIMARY KEY (`tpl_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["frontend_template_lang"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `frontend_template_lang` (
                      `tpl_id` int(11) unsigned NOT NULL,
                      `language_id` tinyint(2) unsigned NOT NULL,
                      `description` Text NOT NULL,
                      `notice` Text NOT NULL,
                      `system_requirements` text NOT NULL,
                      `remark` Text NOT NULL,
                      `gallery_info` Text NOT NULL,
                      `game_image_source` varchar(255) NOT NULL,
                      `game_title` varchar(255) NOT NULL,
                      `game_publisher` varchar(255) NOT NULL,
                      `game_developer` varchar(255) NOT NULL,
                      `game_release_date` varchar(255) NOT NULL,
                      `related_link_info` TEXT NOT NULL,
                      PRIMARY KEY (`tpl_id`, `language_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["frontend_template_to_game_info"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `frontend_template_to_game_info` (
                      `tpl_id` int(11) unsigned NOT NULL,
                      `game_info_id` int(11) unsigned NOT NULL,
                      `game_info_type` varchar(60) NOT NULL,
                      PRIMARY KEY (`tpl_id`, `game_info_id`, `game_info_type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["categories_tag"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `categories_tag` (
                `tag_id` int(11) NOT NULL AUTO_INCREMENT,
                `tag_key` varchar(125) NOT NULL,
                `tag_lft` int(11) NOT NULL,
                `tag_rgt` int(11) NOT NULL,
                `tag_status` TINYINT( 1 ) UNSIGNED NOT NULL DEFAULT '1',
                PRIMARY KEY (`tag_id`),
                KEY `tag_label` (`tag_key`)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "INSERT INTO `categories_tag` (`tag_id`, `tag_key`, `tag_lft`, `tag_rgt`, `tag_status`) VALUES
(1, 'ROOT', 1, 46, 1),
(2, 'ENTERTAINMENT', 40, 45, 0),
(3, 'MOBILE', 34, 39, 1),
(4, 'TOOLS', 24, 33, 1),
(5, 'PC_GAMES', 18, 23, 1),
(6, 'GIFT_CARDS', 14, 17, 1),
(7, 'GAME_CARD', 6, 13, 1),
(8, 'DIRECT_TOP_UP', 2, 5, 1),
(9, 'DIRECT_TOP_UP_CHILD', 3, 4, 1),
(10, 'GAME_POINT', 11, 12, 1),
(11, 'UNIVERSAL_GAME_CARDS', 9, 10, 1),
(12, 'CONSOLES', 7, 8, 1),
(13, 'GIFT_CARDS_CHILD', 15, 16, 1),
(14, 'PC_GAMES_OTHERS', 21, 22, 1),
(15, 'MASSIVELY_MULTIPLAYER', 19, 20, 1),
(16, 'HIGH_LEVEL_ACCOUNT', 31, 32, 1),
(17, 'HARDWARE', 29, 30, 1),
(18, 'SOFTWARE', 27, 28, 1),
(19, 'VPN', 25, 26, 1),
(20, 'MOBILE_RELOAD', 37, 38, 1),
(21, 'MOBILE_GAMES', 35, 36, 1),
(22, 'COLLECTIBLES', 43, 44, 0),
(23, 'MOVIES_AND_MUSIC', 41, 42, 0);");

$add_new_tables["categories_tagmap"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `categories_tagmap` (
                `id` bigint(32) unsigned NOT NULL AUTO_INCREMENT,
                `game_id` bigint(32) unsigned NOT NULL,
                `tag_id` int(11) unsigned NOT NULL,
                PRIMARY KEY (`id`)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["categories_tag_description"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `categories_tag_description` (
                `tag_id` int(11) unsigned NOT NULL,
                `language_id` int(11) unsigned NOT NULL,
                `tag_title` varchar(125) NOT NULL,
                `tag_description` varchar(125) NOT NULL,
                `tag_featured_banner` TEXT NULL DEFAULT NULL,
                PRIMARY KEY (`tag_id`,`language_id`)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "INSERT INTO `categories_tag_description` (`tag_id`, `language_id`, `tag_title`, `tag_description`, `tag_featured_banner`) VALUES
(2, 1, 'Entertainment', '', ''),
(2, 2, '&#23089;&#20048;', '', ''),
(2, 3, '&#23067;&#27138;', '', ''),
(2, 4, 'Hiburan', '', ''),
(3, 1, 'Mobile', '', ''),
(3, 2, '&#25163;&#26426;', '', ''),
(3, 3, '&#25163;&#27231;', '', ''),
(3, 4, 'Ponsel', '', ''),
(4, 1, 'Tools', '', ''),
(4, 2, '&#28216;&#25103;&#24037;&#20855;', '', ''),
(4, 3, '&#36938;&#25138;&#24037;&#20855;', '', ''),
(4, 4, 'Alat-alat', '', ''),
(5, 1, 'PC Games', '', ''),
(5, 2, '&#30005;&#33041;&#28216;&#25103;', '', ''),
(5, 3, '&#38651;&#33126;&#36938;&#25138;', '', ''),
(5, 4, 'Permainan PC', '', ''),
(6, 1, 'Gift Cards', '', ''),
(6, 2, '&#31036;&#21697;&#21345;', '', ''),
(6, 3, '&#31150;&#21697;&#21345;', '', ''),
(6, 4, 'Kartu Hadiah', '', ''),
(7, 1, 'Game Cards', '', ''),
(7, 2, '&#28216;&#25103;&#28857;&#21345;', '', ''),
(7, 3, '&#36938;&#25138;&#40670;&#21345;', '', ''),
(7, 4, 'Kartu Permainan', '', ''),
(8, 1, 'Direct Top Up', '', ''),
(8, 2, '&#30452;&#20805;', '', ''),
(8, 3, '&#30452;&#20805;', '', ''),
(8, 4, 'Top Up Langsung', '', ''),
(9, 1, 'Direct Top Up', '(Rage of 3 Kingdoms, Pirate King Online, Yulgang 2)', ''),
(9, 2, '&#30452;&#20805;', '(Rage of 3 Kingdoms, Pirate King Online, Yulgang 2)', ''),
(9, 3, '&#30452;&#20805;', '(Rage of 3 Kingdoms, Pirate King Online, Yulgang 2)', ''),
(9, 4, 'Top Up Langsung', '(Rage of 3 Kingdoms, Pirate King Online, Yulgang 2)', ''),
(10, 1, 'Game Point', '(League of Legends, Minecraft)', ''),
(10, 2, '&#28216;&#25103;&#28857;&#25968;', '(League of Legends, Minecraft)', ''),
(10, 3, '&#36938;&#25138;&#40670;&#25976;', '(League of Legends, Minecraft)', ''),
(10, 4, 'Poin Permainan', '(League of Legends, Minecraft)', ''),
(11, 1, 'Universal Game Cards', '(GoCash, Rixty, Cherry Credits, Karma Koin)', ''),
(11, 2, '&#36890;&#29992;&#28216;&#25103;&#21345;', '(GoCash, Rixty, Cherry Credits, Karma Koin)', ''),
(11, 3, '&#36890;&#29992;&#36938;&#25138;&#21345;', '(GoCash, Rixty, Cherry Credits, Karma Koin)', ''),
(11, 4, 'Kartu Permainan Universal', '(GoCash, Rixty, Cherry Credits, Karma Koin)', ''),
(12, 1, 'Consoles', '(Xbox Live, Playstation Network Card, Nintendo)', ''),
(12, 2, '&#28216;&#25103;&#26426;', '(Xbox Live, Playstation Network Card, Nintendo)', ''),
(12, 3, '&#36938;&#25138;&#27231;', '(Xbox Live, Playstation Network Card, Nintendo)', ''),
(12, 4, 'Konsol', '(Xbox Live, Playstation Network Card, Nintendo)', ''),
(13, 1, 'Gift Cards', '(iTunes, Amazon, GameStop)', ''),
(13, 2, '&#31036;&#21697;&#21345;', '(iTunes, Amazon, GameStop)', ''),
(13, 3, '&#31150;&#21697;&#21345;', '(iTunes, Amazon, GameStop)', ''),
(13, 4, 'Kartu Hadiah', '(iTunes, Amazon, GameStop)', ''),
(14, 1, 'Others', '(Battlefield 4, Call of Duty, Brink, Left4Dead)', ''),
(14, 2, '&#20854;&#23427;', '(Battlefield 4, Call of Duty, Brink, Left4Dead)', ''),
(14, 3, '&#20854;&#23427;', '(Battlefield 4, Call of Duty, Brink, Left4Dead)', ''),
(14, 4, 'Lain-lain', '(Battlefield 4, Call of Duty, Brink, Left4Dead)', ''),
(15, 1, 'Massively Multiplayer', '(World of Warcraft, WildStar, SWTOR)', ''),
(15, 2, '&#22823;&#22411;&#22810;&#20154;&#22312;&#32447;&#28216;&#25103;', '(World of Warcraft, WildStar, SWTOR)', ''),
(15, 3, '&#22823;&#22411;&#22810;&#20154;&#22312;&#32218;&#36938;&#25138;', '(World of Warcraft, WildStar, SWTOR)', ''),
(15, 4, 'Pemain Multiplayer Masal', '(World of Warcraft, WildStar, SWTOR)', ''),
(16, 1, 'High Level Account', '(World of Warcraft, Aion, League of Legends)', ''),
(16, 2, '&#39640;&#32423;&#36134;&#21495;', '(World of Warcraft, Aion, League of Legends)', ''),
(16, 3, '&#39640;&#32026;&#36076;&#34399;', '(World of Warcraft, Aion, League of Legends)', ''),
(16, 4, 'Akun Tingkat Tinggi', '(World of Warcraft, Aion, League of Legends)', ''),
(17, 1, 'Hardware', '(Gaming Keyboard, Gaming Mouse, Mousepad)', ''),
(17, 2, '&#30828;&#20214;', '(&#28216;&#25103;&#38190;&#30424;&#65292;&#28216;&#25103;&#40736;&#26631;&#65292;&#40736;&#26631;&#22443;)', ''),
(17, 3, '&#30828;&#20214;', '(&#36938;&#25138;&#37749;&#30436;&#65292;&#36938;&#25138;&#40736;&#27161;&#65292;&#40736;&#27161;&#22666;)', ''),
(17, 4, 'Perangkat Keras', '(Gaming Keyboard, Gaming Mouse, Mousepad)', ''),
(18, 1, 'Software', '(Anti Virus)', ''),
(18, 2, '&#36719;&#20214;', '(Anti Virus)', ''),
(18, 3, '&#36575;&#20214;', '(Anti Virus)', ''),
(18, 4, 'Perangkat Lunak', '(Anti Virus)', ''),
(19, 1, 'VPN', '(Battleping, WTFast, Ultima Proxy)', ''),
(19, 2, 'VPN', '(Battleping, WTFast, Ultima Proxy)', ''),
(19, 3, 'VPN', '(Battleping, WTFast, Ultima Proxy)', ''),
(19, 4, 'VPN', '(Battleping, WTFast, Ultima Proxy)', ''),
(20, 1, 'Mobile Reload', '(XOX, YES)', ''),
(20, 2, '&#25163;&#26426;&#20805;&#20540;', '(XOX, YES)', ''),
(20, 3, '&#25163;&#27231;&#20805;&#20540;', '(XOX, YES)', ''),
(20, 4, 'Pengisian Pulsa', '(XOX, YES)', ''),
(21, 1, 'Mobile Games', '', ''),
(21, 2, '&#25163;&#26426;&#28216;&#25103;', '', ''),
(21, 3, '&#25163;&#27231;&#36938;&#25138;', '', ''),
(21, 4, 'Permainan Ponsel', '', ''),
(22, 1, 'Collectibles', '(Toys, Figurines, Miniatures)', ''),
(22, 2, '&#28216;&#25103;&#21608;&#36793;', '(&#20844;&#20180;&#65292;&#25163;&#21150;&#65292;&#27169;&#22411;)', ''),
(22, 3, '&#36938;&#25138;&#21608;&#37002;', '(&#20844;&#20180;&#65292;&#25163;&#36774;&#65292;&#27169;&#22411;)', ''),
(22, 4, 'Koleksi', '(Mainan, Patung-patung, Miniatur)', ''),
(23, 1, 'Movies & Music', '(Channel Subscription, Movies, Music)', ''),
(23, 2, '&#30005;&#24433;&&#38899;&#20048;', '(&#39057;&#36947;&#35746;&#38405;&#65292;&#30005;&#24433;&#65292;&#38899;&#20048;)', ''),
(23, 3, '&#38651;&#24433;&&#38899;&#27138;', '(&#38971;&#36947;&#35330;&#38321;&#65292;&#38651;&#24433;&#65292;&#38899;&#27138;)', ''),
(23, 4, 'Film & Musik', '(Channel berlangganan, Film, Musik)', '');");

add_new_tables($add_new_tables, $DBTables);

/* Add New Field */
$add_new_field = array();
$add_new_field['countries_content'] = array(
    array(
        "field_name" => "supported_pg",
        "field_attr" => "TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL",
        "add_after" => ''
    ),
    array(
        "field_name" => "toll_free",
        "field_attr" => "varchar(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL",
        "add_after" => ''
    )
);
add_field($add_new_field);

// Insert new records into admin_files table (for Game Landing Page)
$select_sql = " SELECT admin_files_id
                FROM " . TABLE_ADMIN_FILES . "
                WHERE admin_files_name='infolinks.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);

if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["define_gamepage2.php"] = array("insert" => " ('define_gamepage2.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='define_gamepage2.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Game Landing Page)

?>