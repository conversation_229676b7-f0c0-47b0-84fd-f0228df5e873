<?php

/* Add New Table */
$add_new_tables = array();

$add_new_tables["search_keywords"] = array(
"structure" => "CREATE TABLE IF NOT EXISTS `search_keywords` (
                `tpl_id` int(11) unsigned NOT NULL DEFAULT '0',
                `id` int(11) unsigned NOT NULL DEFAULT '0',
                `id_type` tinyint(2) unsigned NOT NULL COMMENT '`0: Category, 2: Product, 3: Game`',
                `language_id` int(11) unsigned NOT NULL DEFAULT '0',
                `search_value` text NOT NULL,
                PRIMARY KEY (`id`,`id_type`,`language_id`),
                FULLTEXT KEY `ft_search_value` (`search_value`)
              ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
"data" => "INSERT INTO search_keywords( id, id_type, language_id, search_value )
SELECT categories_id, '0', language_id, search_value
FROM categories_search");

add_new_tables($add_new_tables, $DBTables);

/* Add New Field */
$add_new_field = array();
$add_new_field['frontend_template_lang'] = array(
    array(
        "field_name" => "game_keyword",
        "field_attr" => "TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL",
        "add_after" => 'game_release_date'
    )
);
add_field($add_new_field);
?>