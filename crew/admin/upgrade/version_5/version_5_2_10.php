<?php
tep_db_query('ALTER TABLE categories_configuration ENGINE=InnoDB;');
tep_db_query('ALTER TABLE configuration ENGINE=InnoDB;');
tep_db_query('ALTER TABLE configuration_group ENGINE=InnoDB;');
tep_db_query('ALTER TABLE game_attribute ENGINE=InnoDB;');
tep_db_query('ALTER TABLE game_attribute_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE game_attribute_list ENGINE=InnoDB;');
tep_db_query('ALTER TABLE game_attribute_setting ENGINE=InnoDB;');
tep_db_query('ALTER TABLE address_book ENGINE=InnoDB;');
tep_db_query('ALTER TABLE address_format ENGINE=InnoDB;');
tep_db_query('ALTER TABLE admin ENGINE=InnoDB;');
tep_db_query('ALTER TABLE admin_credit_limit ENGINE=InnoDB;');
tep_db_query('ALTER TABLE admin_files ENGINE=InnoDB;');
tep_db_query('ALTER TABLE admin_files_actions ENGINE=InnoDB;');
tep_db_query('ALTER TABLE admin_files_categories ENGINE=InnoDB;');
tep_db_query('ALTER TABLE admin_groups ENGINE=InnoDB;');
tep_db_query('ALTER TABLE affiliate_account ENGINE=InnoDB;');
tep_db_query('ALTER TABLE affiliate_affiliate ENGINE=InnoDB;');
tep_db_query('ALTER TABLE affiliate_banners ENGINE=InnoDB;');
tep_db_query('ALTER TABLE affiliate_banners_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE affiliate_clickthroughs ENGINE=InnoDB;');
tep_db_query('ALTER TABLE affiliate_payment ENGINE=InnoDB;');
tep_db_query('ALTER TABLE affiliate_payment_status ENGINE=InnoDB;');
tep_db_query('ALTER TABLE affiliate_payment_status_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE affiliate_sales ENGINE=InnoDB;');
tep_db_query('ALTER TABLE aft_automation ENGINE=InnoDB;');
tep_db_query('ALTER TABLE aft_automation_category ENGINE=InnoDB;');
tep_db_query('ALTER TABLE aft_automation_version ENGINE=InnoDB;');
tep_db_query('ALTER TABLE aft_functions ENGINE=InnoDB;');
tep_db_query('ALTER TABLE customers_info ENGINE=InnoDB;');
tep_db_query('ALTER TABLE custom_products_code ENGINE=InnoDB;');
tep_db_query('ALTER TABLE custom_products_code_log ENGINE=InnoDB;');
tep_db_query('ALTER TABLE ip_list_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE ip_list_history_tmp ENGINE=InnoDB;');
tep_db_query('ALTER TABLE ip_tags_stats ENGINE=InnoDB;');
tep_db_query('ALTER TABLE ip_tags_stats_tmp ENGINE=InnoDB;');
tep_db_query('ALTER TABLE ip_to_defined_ip_zones ENGINE=InnoDB;');
tep_db_query('ALTER TABLE store_refund ENGINE=InnoDB;');
tep_db_query('ALTER TABLE store_refund_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE store_refund_info ENGINE=InnoDB;');
tep_db_query('ALTER TABLE temp_process ENGINE=InnoDB;');
tep_db_query('ALTER TABLE whos_online ENGINE=InnoDB;');
?>