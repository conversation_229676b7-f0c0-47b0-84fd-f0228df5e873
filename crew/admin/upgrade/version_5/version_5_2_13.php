<?php

//ONECARD V2
$add_new_tables = array();
$add_new_tables["onecardv2"] = array(
    "structure" => "CREATE TABLE `onecardv2` (
					`order_id` varchar(25) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
					`code` varchar(11) COLLATE utf8_unicode_ci DEFAULT NULL,
					`amount` decimal(15,2) DEFAULT NULL,
					`currency` char(3) COLLATE utf8_unicode_ci DEFAULT NULL,
					`date_added` datetime DEFAULT NULL,
					`last_modified` bigint(13) unsigned DEFAULT NULL,
					`data` text COLLATE utf8_unicode_ci,
					`hash_key` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
					PRIMARY KEY (`order_id`)
				  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["onecardv2_status_history"] = array(
    "structure" => "CREATE TABLE `onecardv2_status_history` (
					`onecardv2_status_history_id` int(11) NOT NULL AUTO_INCREMENT,
					`order_id` varchar(25) COLLATE utf8_unicode_ci DEFAULT NULL,
					`status_code` smallint(3) DEFAULT NULL,
					`status_message` text COLLATE utf8_unicode_ci,
					`last_modified` datetime DEFAULT NULL,
					`hash_key` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
					`changed_by` varchar(128) COLLATE utf8_unicode_ci DEFAULT NULL,
					PRIMARY KEY (`onecardv2_status_history_id`),
					KEY `index_order_id` (`order_id`)
				  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

// shasso integration (omg userbar) customers_preference & customers_login_history
$add_new_tables["customers_preference"] = array(
    "structure" => " CREATE TABLE `customers_preference` (
                    `customers_id` int(11) NOT NULL,
                    `preference_key` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
                    `value` varchar(128) COLLATE utf8_unicode_ci NOT NULL,
                    PRIMARY KEY (`customers_id`,`preference_key`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["customers_login_history"] = array(
    "structure" => "CREATE TABLE `customers_login_history` (
                    `customers_id` int(11) NOT NULL,
                    `customers_login_date` datetime NOT NULL,
                    `customers_login_ip` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
                    `customers_login_ua_info` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
                    `login_method` varchar(12) COLLATE utf8_unicode_ci NOT NULL,
                    PRIMARY KEY (`customers_id`,`customers_login_date`),
                    KEY `index_login_date_and_ip` (`customers_login_date`,`customers_login_ip`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

// Define tag_id as index key in categories_tagmap table
add_index_key('categories_tagmap', 'index_tag_id', 'index', 'tag_id', $DBTables);
// Define id & id_type as index key in frontend_template table
add_index_key('frontend_template', 'index_id_and_id_type', 'index', 'id,id_type', $DBTables);

// Enable entertainment > movies and music game category tag
$update_sql = " UPDATE categories_tag 
                    SET tag_status = 1 
                WHERE tag_key in ('ENTERTAINMENT', 'MOVIES_AND_MUSIC')";
tep_db_query($update_sql);

$featured_banner = array(
    8 => array(//  tag id
        'key' => 'DIRECT_TOP_UP',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/mmcat1_001en.png',
            'cid' => 19874,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/mmcat1_001cn.png',
                'cid' => 19874,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/mmcat1_001cn.png',
                'cid' => 19874,
            ),
            4 => '',
        )
    ),
    7 => array(//  tag id
        'key' => 'GAME_CARD',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/mmcat2_001en.png',
            'cid' => 5329,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/mmcat2_001cn.png',
                'cid' => 3466,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/mmcat2_001cn.png',
                'cid' => 3466,
            ),
            4 => '',
        )
    ),
    6 => array(//  tag id
        'key' => 'GIFT_CARDS',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/mmcat3_001en.png',
            'cid' => 4444,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/latest-mmcat3_001cn.png',
                'cid' => 17855,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/latest-mmcat3_001cn.png',
                'cid' => 17855,
            ),
            4 => '',
        )
    ),
    5 => array(//  tag id
        'key' => 'PC_GAMES',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/mmcat4_001en.png',
            'cid' => 2299,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/mmcat4_001cn.png',
                'cid' => 19958,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/mmcat4_001cn.png',
                'cid' => 19958,
            ),
            4 => '',
        )
    ),
    4 => array(//  tag id
        'key' => 'TOOLS',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/mmcat5_001en.png',
            'cid' => 17634,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/mmcat5_001cn.png',
                'cid' => 5486,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/mmcat5_001cn.png',
                'cid' => 5486,
            ),
            4 => '',
        )
    ),
    3 => array(//  tag id
        'key' => 'MOBILE',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/mmcat6_001en.png',
            'cid' => 17261,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/mmcat6_001cn.png',
                'cid' => 17261,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/mmcat6_001cn.png',
                'cid' => 17261,
            ),
            4 => '',
        )
    ),
    2 => array(//  tag id
        'key' => 'ENTERTAINMENT',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/mmcat7_001en.png',
            'cid' => 18181,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/mmcat7_001cn.png',
                'cid' => 19148,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/mmcat7_001cn.png',
                'cid' => 19148,
            ),
            4 => '',
        )
    )
);

foreach ($featured_banner as $tag_id => $banner_info) {
    $default_json_str = array(
        'src' => $banner_info['lang_default']['src'],
        'createURL' => array(
            'category/index', array('cid' => $banner_info['lang_default']['cid'])
        ),
        'href' => '',
    );

    foreach ($banner_info['lang'] as $lang_id => $lang_banner_info) {
        $json_str = $lang_banner_info == '' ? $default_json_str : array(
            'src' => $lang_banner_info['src'],
            'createURL' => array(
                'category/index', array('cid' => $lang_banner_info['cid'])
            ),
            'href' => '',
        );

        $update_sql = " UPDATE categories_tag_description 
                            SET tag_featured_banner = '" . json_encode($json_str) . "' 
                        WHERE tag_id = '" . $tag_id . "'";
        tep_db_query($update_sql);
    }
}
?>