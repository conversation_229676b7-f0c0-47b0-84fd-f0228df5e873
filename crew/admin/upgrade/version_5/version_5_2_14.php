<?php
tep_db_query('ALTER TABLE orders_cancel_request ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_comments ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_compensate_products ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_delivery_queue ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_extra_info ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_notification ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_products_attributes ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_products_download ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_products_eta ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_products_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_products_item ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_session_info ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_status ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_tag ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_custom_products ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_log_table ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_products_extra_info ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_total ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_status_stat ENGINE=InnoDB;');
?>