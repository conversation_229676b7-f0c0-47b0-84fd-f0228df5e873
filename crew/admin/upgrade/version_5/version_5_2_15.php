<?php
# Change ipay88 table field
$change_field = array();

$change_field['ipay88'] = array(
    array(
        "field_name" => "ipay88_bank_auth_code",
        "field_attr" => " VARCHAR(24) NOT NULL "
    )
);

change_field_structure($change_field);
# End of change ipay88 table field

// Define top_up_id as index key in api_log table
add_index_key ('api_log', 'index_top_up_id', 'index', 'top_up_id', $DBTables);
// End of define top_up_id as index key in api_log table

// Update customers verification document filename to format yyyymmddhhmmss
$update_sql = " UPDATE customers_verification_document 
                    SET files_001 = if (LOCATE('_001_', files_001) > 0, SUBSTRING_INDEX(files_001, '_', -1), files_001),
                        files_002 = if (LOCATE('_002_', files_002) > 0, SUBSTRING_INDEX(files_002, '_', -1), files_002),
                        files_003 = if (LOCATE('_003_', files_003) > 0, SUBSTRING_INDEX(files_003, '_', -1), files_003),
                        files_004 = if (LOCATE('_004_', files_004) > 0, SUBSTRING_INDEX(files_004, '_', -1), files_004),
                        files_005 = if (LOCATE('_005_', files_005) > 0, SUBSTRING_INDEX(files_005, '_', -1), files_005)";
tep_db_query($update_sql);

$update_sql = " UPDATE customers_verification_document_log
                    SET log_filename = if (LOCATE('_'+log_docs_id +'_', log_filename) > 0, SUBSTRING_INDEX(log_filename, '_', -1), log_filename)";
tep_db_query($update_sql);
// Update customers verification document filename to format yyyymmddhhmmss


?>