<?php
tep_db_query('ALTER TABLE cart_comments ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_game_details ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_groups ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_product_types ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_services ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_setting ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_setting_lang ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_structures ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_to_price_sets ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_types ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_types_groups ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cimb ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cimb_status_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cms_menu ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cms_menu_lang_setting ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cms_menu_tab_pages ENGINE=InnoDB;');
tep_db_query('ALTER TABLE store_points ENGINE=InnoDB;');
tep_db_query('ALTER TABLE moneybookers_countries ENGINE=InnoDB;');
tep_db_query('ALTER TABLE moneybookers_currencies ENGINE=InnoDB;');
tep_db_query('ALTER TABLE moneybookers_payment_status_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE mozcom ENGINE=InnoDB;');
tep_db_query('ALTER TABLE myacc_link ENGINE=InnoDB;');
tep_db_query('ALTER TABLE myacc_link_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE myacc_menu ENGINE=InnoDB;');
tep_db_query('ALTER TABLE myacc_menu_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE newsletters ENGINE=InnoDB;');
tep_db_query('ALTER TABLE newsletters_groups ENGINE=InnoDB;');
tep_db_query('ALTER TABLE onecard ENGINE=InnoDB;');
tep_db_query('ALTER TABLE onecard_status_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_tax_configuration ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_tax_configuration_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_top_up ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_top_up_remark ENGINE=InnoDB;');
tep_db_query('ALTER TABLE payment_methods ENGINE=InnoDB;');
tep_db_query('ALTER TABLE payment_methods_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE payment_methods_fields ENGINE=InnoDB;');
tep_db_query('ALTER TABLE payment_methods_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE store_payments_status ENGINE=InnoDB;');
tep_db_query('ALTER TABLE products_follow_price ENGINE=InnoDB;');
tep_db_query('ALTER TABLE products_currency_prices ENGINE=InnoDB;');
tep_db_query('ALTER TABLE promotions ENGINE=InnoDB;');

tep_db_query('ALTER TABLE products_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE store_payments_details ENGINE=InnoDB;');
tep_db_query('ALTER TABLE store_payments_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE store_payments ENGINE=InnoDB;');
tep_db_query('ALTER TABLE page_view_ip_list ENGINE=InnoDB;');
tep_db_query('ALTER TABLE page_view_ip_list_tmp ENGINE=InnoDB;');
?>