<?php
// Add OGC Reseller ID to Pin Request
$existing_customers_favorites_fields = get_table_fields('pin_request');
if (!in_array('pin_reseller_id', $existing_customers_favorites_fields)) {
    tep_db_query("ALTER TABLE `pin_request` ADD `pin_reseller_id` varchar(16) NOT NULL AFTER `pin_request_id`;");
    
    $update_sql = "	UPDATE pin_request	
                    SET pin_reseller_id = 'OGMGLOBAL'
                    WHERE pin_reseller_id = ''";
    tep_db_query($update_sql);
}

// Disable High Level Account from game category tag
$update_sql = " UPDATE categories_tag 
                    SET tag_status = 0
                WHERE tag_key in ('HIGH_LEVEL_ACCOUNT')";
tep_db_query($update_sql);
?>