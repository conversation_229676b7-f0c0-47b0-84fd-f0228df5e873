<?php
$add_new_tables = array();

// New table to store relation between game blogs and products
$add_new_tables["products_to_game_blog"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `products_to_game_blog` (
                    `products_id` int(11) NOT NULL DEFAULT '0',
                    `game_blog_id` int(11) NOT NULL DEFAULT '0',
                    PRIMARY KEY (`products_id`,`game_blog_id`),
                    KEY `index_game_blog_id` (`game_blog_id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

// Define categories_url_alias as index key in categories table
add_index_key ('categories', 'index_categories_url_alias', 'index', 'categories_url_alias', $DBTables);
// End of define categories_url_alias as index key in categories table

// Define products_url_alias as index key in products table
add_index_key ('products', 'index_products_url_alias', 'index', 'products_url_alias', $DBTables);
// End of define products_url_alias as index key in products table

// Insert new country Palestine into Middle East Zone used in content
$country_sel_sql = "SELECT countries_id
                    FROM countries
                    WHERE countries_name = 'Palestine'
                        AND countries_iso_code_2 = 'PS'";
$country_res_sql = tep_db_query($country_sel_sql);
if ($country_row = tep_db_fetch_array($country_res_sql)) {
    $geo_zone_sql = "SELECT geo_zone_id
                    FROM " . TABLE_GEO_ZONES . "
                    WHERE geo_zone_name = 'Middle East'
                        AND geo_zone_type = 5";
    $geo_zone_res_sql = tep_db_query($geo_zone_sql);
	if ($geo_zone_row = tep_db_fetch_array($geo_zone_res_sql)) {
        $sel_sql = "SELECT association_id
                    FROM " . TABLE_ZONES_TO_GEO_ZONES . "
                    WHERE zone_country_id = '" . $country_row['countries_id'] . "'
                        AND geo_zone_id = '" . $geo_zone_row['geo_zone_id'] . "'";
        $res_sql = tep_db_query($sel_sql);
        if (!tep_db_num_rows($res_sql)) {
            $zones_country_insert_sql = "INSERT INTO `" . TABLE_ZONES_TO_GEO_ZONES . "` (`zone_country_id`, `zone_id`, `geo_zone_id`, `date_added`) VALUES 
                                                ('" . $country_row['countries_id'] . "', ',0,', '" . $geo_zone_row['geo_zone_id'] . "', NOW() ) ";
            tep_db_query($zones_country_insert_sql);
        }
    }
}
// End of insert new country

tep_db_query('ALTER TABLE authorized_token ENGINE=InnoDB;');
tep_db_query('ALTER TABLE automate_alert_color ENGINE=InnoDB;');
tep_db_query('ALTER TABLE automate_buyback_price ENGINE=InnoDB;');
tep_db_query('ALTER TABLE banners ENGINE=InnoDB;');
tep_db_query('ALTER TABLE banners_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE batch_update_price_sets ENGINE=InnoDB;');
tep_db_query('ALTER TABLE batch_update_price_sets_values ENGINE=InnoDB;');
tep_db_query('ALTER TABLE bibit_currencies ENGINE=InnoDB;');
tep_db_query('ALTER TABLE brackets ENGINE=InnoDB;');
tep_db_query('ALTER TABLE brackets_groups ENGINE=InnoDB;');
tep_db_query('ALTER TABLE brackets_groups_to_level_tags ENGINE=InnoDB;');
tep_db_query('ALTER TABLE brackets_tags ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_basket ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_bracket ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_bracket_tags ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_categories ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_competitor_status_bracket ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_competitor_status_bracket_set ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_groups ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_groups_tags ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_groups_tags_info ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_groups_to_categories ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_online_user ENGINE=InnoDB;');

tep_db_query('ALTER TABLE buyback_price_control ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_products ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_setting ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_status ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_supplier_price_bracket ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_supplier_price_bracket_set ENGINE=InnoDB;');
tep_db_query('ALTER TABLE buyback_suspend_user ENGINE=InnoDB;');

tep_db_query('ALTER TABLE bibit ENGINE=InnoDB;'); //1 min 20.77 sec
tep_db_query('ALTER TABLE bibit_payment_status_history ENGINE=InnoDB;'); //2 min 45.51 sec
tep_db_query('ALTER TABLE buyback_order_info ENGINE=InnoDB;'); //12.32 sec
tep_db_query('ALTER TABLE buyback_request ENGINE=InnoDB;'); // 58.82 sec
tep_db_query('ALTER TABLE buyback_request_group ENGINE=InnoDB;'); // 1 min 17.91 sec
tep_db_query('ALTER TABLE buyback_status_history ENGINE=InnoDB;'); //4 min 37.75 sec
?>