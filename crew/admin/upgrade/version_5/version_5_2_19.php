<?php
// V2 Mega Menu Featured Banner Setting
$featured_banner = array(
    8 => array(//  tag id
        'key' => 'DIRECT_TOP_UP',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/dtu_en.png',
            'cid' => 9578,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/dtu_cn.png',
                'cid' => 20883,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/dtu_cn.png',
                'cid' => 20883,
            ),
            4 => '',
        )
    ),
    7 => array(//  tag id
        'key' => 'GAME_CARD',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/game_card_en.png',
            'cid' => 17508,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/game_card_cn.png',
                'cid' => 21691,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/game_card_cn.png',
                'cid' => 21691,
            ),
            4 => '',
        )
    ),
    6 => array(//  tag id
        'key' => 'GIFT_CARDS',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/gift_card_en-new.png',
            'cid' => 4130,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/gift_card_cn.png',
                'href' => 'http://www.offgamers.com/blog/hot-sale-cards-for-hk-region/',
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/gift_card_cn.png',
                'href' => 'http://www.offgamers.com/blog/hot-sale-cards-for-hk-region/',
            ),
            4 => '',
        )
    ),
    5 => array(//  tag id
        'key' => 'PC_GAMES',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/pc_games_en.png',
            'href' => 'http://www.offgamers.com/blog/wildstar-discount-promotion/',
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/pc_games_cn.png',
                'cid' => 19958,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/pc_games_cn.png',
                'cid' => 19958,
            ),
            4 => '',
        )
    ),
    4 => array(//  tag id
        'key' => 'TOOLS',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/tools_en.png',
            'cid' => 6031,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/tools_cn.png',
                'cid' => 4835,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/tools_cn.png',
                'cid' => 4835,
            ),
            4 => '',
        )
    ),
    3 => array(//  tag id
        'key' => 'MOBILE',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/mobile_en.png',
            'cid' => 16996,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/mobile_cn.png',
                'cid' => 21889,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/mobile_cn.png',
                'cid' => 21889,
            ),
            4 => '',
        )
    ),
    2 => array(//  tag id
        'key' => 'ENTERTAINMENT',
        'lang_default' => array(
            'src' => 'http://image.offgamers.com/games/entertainment_en.png',
            'cid' => 20879,
        ),
        'lang' => array(
            1 => '',
            2 => array(
                'src' => 'http://image.offgamers.com/games/entertainment_cn.png',
                'cid' => 19305,
            ),
            3 => array(
                'src' => 'http://image.offgamers.com/games/entertainment_cn.png',
                'cid' => 19305,
            ),
            4 => '',
        )
    )
);

foreach ($featured_banner as $tag_id => $banner_info) {
    $default_json_str = array(
        'src' => $banner_info['lang_default']['src'],
        'href' => isset($banner_info['lang_default']['href']) ? $banner_info['lang_default']['href'] : '',
    );
    
    if (isset($banner_info['lang_default']['cid'])) {
        $default_json_str['createURL'] = array(
            'category/index', array('cid' => $banner_info['lang_default']['cid'])
        );
    }

    foreach ($banner_info['lang'] as $lang_id => $lang_banner_info) {
        if ($lang_banner_info === '') {
            $json_str = $default_json_str;
        } else {
            $json_str = array(
                'src' => $lang_banner_info['src'],
                'href' => isset($lang_banner_info['href']) ? $lang_banner_info['href'] : '',
            );
            
            if (isset($lang_banner_info['cid'])) {
                $json_str['createURL'] = array(
                    'category/index', array('cid' => $lang_banner_info['cid'])
                );
            }
        }
        
        $update_sql = " UPDATE categories_tag_description 
                            SET tag_featured_banner = '" . json_encode($json_str) . "' 
                        WHERE tag_id = '" . $tag_id . "'
                            AND language_id = '" . $lang_id . "'";
        tep_db_query($update_sql);
    }
}
?>