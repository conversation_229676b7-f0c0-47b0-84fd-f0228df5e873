<?php

// Create new table
$add_new_tables["customers_purchase_statistic"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `customers_purchase_statistic` (
                    `customers_id` int(11) unsigned NOT NULL,
                    `statistic_key` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
                    `statistic_value` text COLLATE utf8_unicode_ci NOT NULL,
                    `created_date` datetime NOT NULL,
                    PRIMARY KEY (`customers_id`,`statistic_key`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);
// End of create new table

// Insert View Customer Pin Request Permission
$select_sql = "	SELECT admin_files_id, admin_groups_id 
                FROM " . TABLE_ADMIN_FILES . "
                WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $file_action_sel_sql = "SELECT admin_files_actions_key 
                            FROM " . TABLE_ADMIN_FILES_ACTIONS . "
                            WHERE admin_files_actions_key='CUSTOMER_VIEW_PIN_REQUEST'";
    $file_action_res_sql = tep_db_query($file_action_sel_sql);
    if (!$file_action_row = tep_db_fetch_array($file_action_res_sql)) {
        $admin_files_actions_insert_sql = array();

        $admin_files_actions_insert_sql["CUSTOMER_VIEW_PIN_REQUEST"] = array("insert" => " ('CUSTOMER_VIEW_PIN_REQUEST', 'View customer pin request', " . $row_sql["admin_files_id"] . ", '1', 100)");
        insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
    }
}

// Define customers_info_date_of_last_logon as index key in customers_info table
add_index_key ('customers_info', 'customers_info_date_of_last_logon', 'index', 'customers_info_date_of_last_logon', $DBTables);
// End of define customers_info_date_of_last_logon as index key in customers_info table
?>