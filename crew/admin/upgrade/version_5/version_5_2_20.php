<?php

// Delete tables
$delete_tables_array = array('sso_token');
delete_tables($delete_tables_array, $DBTables);
// End of delete tables

tep_db_query('ALTER TABLE categories_types_sets ENGINE=InnoDB;');
tep_db_query('ALTER TABLE char_honor_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE char_pet ENGINE=InnoDB;');
tep_db_query('ALTER TABLE char_pet_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE char_reputation_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coda ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coda_status_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE competitors ENGINE=InnoDB;');
tep_db_query('ALTER TABLE competitors_to_categories ENGINE=InnoDB;');
tep_db_query('ALTER TABLE counter ENGINE=InnoDB;');
tep_db_query('ALTER TABLE counter_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE countries ENGINE=InnoDB;');
tep_db_query('ALTER TABLE countries_content ENGINE=InnoDB;');
tep_db_query('ALTER TABLE countries_content_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupons ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupons_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupons_generation ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupons_generation_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupons_generation_status_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupons_status_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupon_email_track ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupon_gv_customer ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupon_gv_queue ENGINE=InnoDB;');
tep_db_query('ALTER TABLE coupon_redeem_track ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_aws_cf ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_customer_aft_upgrade ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_customer_upgrade ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_genesis_orders ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_hla ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_open_restock_id ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_orders_payment_status ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_pending_credit ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_process_track ENGINE=InnoDB;');
tep_db_query('ALTER TABLE cron_undelivered_report ENGINE=InnoDB;');
tep_db_query('ALTER TABLE currencies ENGINE=InnoDB;');
tep_db_query('ALTER TABLE customers_aft_groups ENGINE=InnoDB;');
tep_db_query('ALTER TABLE char_skill_history ENGINE=InnoDB;');  // 53.21 sec
tep_db_query('ALTER TABLE char_reputation_detail ENGINE=InnoDB;');  // 2 min 44.11 sec
tep_db_query('ALTER TABLE char_quest_history ENGINE=InnoDB;');  //1 min 22.72 sec
tep_db_query('ALTER TABLE char_item_storage ENGINE=InnoDB;'); // 11 min 7.60 sec
tep_db_query('ALTER TABLE char_item_history ENGINE=InnoDB;'); //40.26 sec

?>