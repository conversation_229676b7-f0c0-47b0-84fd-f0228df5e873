<?php

$add_new_tables["orders_tax_configuration_log"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `orders_tax_configuration_log` (
                      `log_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                      `log_users_id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
                      `log_orders_tax_id` int(11) NOT NULL,
                      `log_remarks` text COLLATE utf8_unicode_ci NOT NULL,
                      `log_IP` varchar(15) COLLATE utf8_unicode_ci NOT NULL,
                      `log_datetime` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
                      `log_action` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
                      PRIMARY KEY (`log_id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

tep_db_query("ALTER TABLE `orders_tax_configuration` ENGINE = InnoDB DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;");
tep_db_query("ALTER TABLE `orders_tax_configuration_description` ENGINE = InnoDB DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;");

tep_db_query("ALTER TABLE `orders_tax_configuration` CHANGE `country_code` `country_code` CHAR( 2 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'countries_iso_code_2';");
tep_db_query("ALTER TABLE `orders_tax_configuration` CHANGE `orders_tax_status` `orders_tax_status` ENUM( '0', '1' ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '0';");
tep_db_query("ALTER TABLE `orders_tax_configuration_description` CHANGE `orders_tax_title` `orders_tax_title` VARCHAR( 32 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '';");

$add_new_field = array();

$add_new_field['orders_tax_configuration'] = array(
    array(
        "field_name" => "start_datetime",
        "field_attr" => "DATETIME NOT NULL",
        "add_after" => 'orders_tax_status'
    ),
    array(
        "field_name" => "address_1",
        "field_attr" => "varchar(64) NOT NULL",
        "add_after" => 'start_datetime'
    ),
    array(
        "field_name" => "address_2",
        "field_attr" => "varchar(64) NOT NULL",
        "add_after" => 'address_1'
    ),
    array(
        "field_name" => "address_3",
        "field_attr" => "varchar(64) NOT NULL",
        "add_after" => 'address_2'
    ),
    array(
        "field_name" => "contact",
        "field_attr" => "varchar(32) NOT NULL",
        "add_after" => 'address_3'
    ),
    array(
        "field_name" => "website",
        "field_attr" => "varchar(32) NOT NULL",
        "add_after" => 'contact'
    ),
    array(
        "field_name" => "gst_registration_no",
        "field_attr" => "varchar(32) NOT NULL",
        "add_after" => 'website'
    ),
    array(
        "field_name" => "company_name",
        "field_attr" => "varchar(32) NOT NULL",
        "add_after" => 'gst_registration_no'
    ),
    array(
        "field_name" => "company_logo",
        "field_attr" => "varchar(255) NOT NULL",
        "add_after" => 'company_name'
    )
);

$add_new_field['orders_tax_configuration_description'] = array(
    array(
        "field_name" => "orders_tax_title_short",
        "field_attr" => "varchar(10) NOT NULL",
        "add_after" => 'orders_tax_title'
    )
);

add_field($add_new_field);
?>
