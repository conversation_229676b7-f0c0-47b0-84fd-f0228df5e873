<?php

$add_new_tables["ogm_customers_basket"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `ogm_customers_basket` (
            `cart_id` int(5) NOT NULL,
            `customers_id` int(11) NOT NULL,
            `products_id` int(11) NOT NULL,
            `customers_basket_quantity` int(2) NOT NULL DEFAULT '0',
            `customers_basket_date_added` datetime NOT NULL,
            `products_categories_id` int(11) NOT NULL,
            `customers_basket_custom_key` int(3) unsigned DEFAULT NULL COMMENT 'custom_product_type_id',
            `customers_basket_custom_value` text,
            `pg_tran_id` int(11) unsigned NOT NULL,
            `cart_info` text NOT NULL,
            PRIMARY KEY (`cart_id`),
            KEY `index_customers_id` (`customers_id`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
?>
