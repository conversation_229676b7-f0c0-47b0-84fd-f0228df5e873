<?php

# Wee Siong
// Insert new records into admin_files_actions table (for permission on editing pending order order amount)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();

    $admin_files_actions_insert_sql["EDIT_ORDER_MODIFY_PENDING_ORDER"] = array("insert" => " ('EDIT_ORDER_MODIFY_PENDING_ORDER', 'Modify Pending Order', " . $row_sql["admin_files_id"] . ", '1', '50')");

    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// End of insert new records into admin_files_actions table (for permission on viewing customer purchase details)

#Wei Chen
$add_new_tables = array();

$add_new_tables["invoice_queue_date"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `invoice_queue_date` (
                    `last_run_datetime` datetime DEFAULT NULL,
                    PRIMARY KEY (`last_run_datetime`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["invoice_queue"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `invoice_queue` (
                    `created_datetime` datetime DEFAULT NULL,
                    `orders_id` int(11) unsigned NOT NULL,
                    `extra_info` text NOT NULL COMMENT 'Data store in json format',
                    PRIMARY KEY (`created_datetime`, `orders_id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["invoice_running_number"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `invoice_running_number` (
                    `country_iso2` varchar(12) NOT NULL DEFAULT 'GENERAL',
                    `invoice_number` int(6) unsigned NOT NULL DEFAULT 1,
                    `invoice_month` int(1) unsigned NOT NULL DEFAULT 1,
                    PRIMARY KEY (`country_iso2`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["invoice"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `invoice` (
                    `invoice_id` bigint(11) unsigned NOT NULL AUTO_INCREMENT,
                    `invoice_number` varchar(32) NOT NULL DEFAULT '',
                    `invoice_type` varchar(16) NOT NULL DEFAULT '',
                    `orders_id` int(11) unsigned NOT NULL DEFAULT 0,
                    `invoice_file_domain` varchar(32) DEFAULT NULL,
                    `invoice_file_path` varchar(128) DEFAULT NULL,
                    `file_raw_data` text NOT NULL COMMENT 'Data store in json format',
                    `created_datetime` datetime DEFAULT NULL,
                    `last_modified_datetime` datetime DEFAULT NULL,
                    PRIMARY KEY (`invoice_id`),
                    KEY `index_invoice_number` (`invoice_number`),
                    KEY `index_orders_id` (`orders_id`),
                    KEY `index_date` (`created_datetime`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["credit_note_queue_date"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `credit_note_queue_date` (
                    `last_run_datetime` datetime DEFAULT NULL,
                    PRIMARY KEY (`last_run_datetime`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["credit_note_queue"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `credit_note_queue` (
                    `created_datetime` datetime DEFAULT NULL,
                    `orders_id` int(11) unsigned NOT NULL,
                    `extra_info` text NOT NULL COMMENT 'Data store in json format',
                    PRIMARY KEY (`created_datetime`, `orders_id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["credit_note_running_number"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `credit_note_running_number` (
                    `country_iso2` varchar(12) NOT NULL DEFAULT 'GENERAL',
                    `cn_number` int(6) unsigned NOT NULL DEFAULT 1,
                    `cn_month` int(1) unsigned NOT NULL DEFAULT 1,
                    PRIMARY KEY (`country_iso2`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["credit_note"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `credit_note` (
                    `cn_id` bigint(11) unsigned NOT NULL AUTO_INCREMENT,
                    `cn_number` varchar(32) NOT NULL DEFAULT '',
                    `cn_type` varchar(16) NOT NULL DEFAULT '',
                    `orders_id` int(11) unsigned NOT NULL DEFAULT 0,
                    `invoice_number` varchar(32) NOT NULL DEFAULT '',
                    `cn_file_domain` varchar(32) DEFAULT NULL,
                    `cn_file_path` varchar(128) DEFAULT NULL,
                    `file_raw_data` text NOT NULL COMMENT 'Data store in json format',
                    `created_datetime` datetime DEFAULT NULL,
                    `last_modified_datetime` datetime DEFAULT NULL,
                    PRIMARY KEY (`cn_id`),
                    KEY `index_cn_number` (`cn_number`),
                    KEY `index_invoice_number` (`invoice_number`),
                    KEY `index_orders_id` (`orders_id`),
                    KEY `index_date` (`created_datetime`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
?>