<?php

# Darren
// Insert Kount Configurations and Create Kount Query Queue Table
$add_new_tables = array();

$add_new_tables[TABLE_API_KOUNT_QUERY_QUEUE] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `" . TABLE_API_KOUNT_QUERY_QUEUE . "` (
      `orders_id` bigint(11) unsigned NOT NULL DEFAULT '0',
      `transaction_type` varchar(2) NOT NULL DEFAULT '',
      `extra_info` text NOT NULL,
      `created_datetime` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
      PRIMARY KEY (`orders_id`),
      KEY `index_created_datetime` (`created_datetime`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);

$config_group_insert = array(
    "915" => array("insert" => " (915, 'Kount', 'Kount Configuration', 261, 1); "),
);

insert_new_records(TABLE_CONFIGURATION_GROUP, "configuration_group_id", $config_group_insert, $DBTables, "(`configuration_group_id`, `configuration_group_title`, `configuration_group_description`, `sort_order`, `visible`)");

$config_insert = array(
    "KOUNT_ENABLED" => array("insert" => " ('Enable Kount', 'KOUNT_ENABLED', 'true', 'Enable Kount Risk Inquiry Services?', 915, 1, NULL, CURRENT_TIMESTAMP, NULL, 'tep_cfg_select_option(array(\'true\', \'false\'),')"),
    "KOUNT_MERCHANT_ID" => array("insert" => " ('Merchant ID', 'KOUNT_MERCHANT_ID', '148100', 'Kount Merchant ID', 915, 2, NULL, CURRENT_TIMESTAMP, NULL, NULL)"),
    "KOUNT_RIS_URL" => array("insert" => " ('RIS Server URL', 'KOUNT_RIS_URL', 'https://risk.test.kount.net', 'Kount RIS Server URL', 915, 4, NULL, CURRENT_TIMESTAMP, NULL, NULL)"),
    "KOUNT_RIS_API_KEY" => array("insert" => " ('RIS API Key', 'KOUNT_RIS_API_KEY', 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiIxNDgxMDAiLCJhdWQiOiJLb3VudC4xIiwiaWF0IjoxNDMxMzQyNTEwLCJzY3AiOnsia2EiOm51bGwsImtjIjpudWxsLCJhcGkiOmZhbHNlLCJyaXMiOnRydWV9fQ.Kk6-NM58MV09hDjFNLa7NIkXpuvoRAF7_sV4iwmf6UA', 'Kount RIS API Key', 915, 5, NULL, CURRENT_TIMESTAMP, NULL, NULL)"),
);

insert_new_records(TABLE_CONFIGURATION, "configuration_key", $config_insert, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");

?>