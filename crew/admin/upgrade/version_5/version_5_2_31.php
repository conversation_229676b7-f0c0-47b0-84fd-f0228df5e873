<?php

// Create new table
$add_new_tables["rating_rules"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `rating_rules` (
  `rule_id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
  `rule_type` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
  `rule_operator` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
  `rule_value` text COLLATE utf8_unicode_ci NOT NULL,
  `created_date` datetime NOT NULL,
  `last_modified_date` datetime NOT NULL,
  PRIMARY KEY (`rule_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["rating_rules_tags"] = array(
   "structure" => "CREATE TABLE IF NOT EXISTS `rating_rules_tags` (
  `rule_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL,
  PRIMARY KEY (`rule_id`,`tag_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=6 ;",
    "data" => "");

$add_new_tables["rating_tags"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `rating_tags` (
  `tag_id` int(11) NOT NULL AUTO_INCREMENT,
  `tag_name` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
  `tag_method` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
  `tag_trigger_content` text COLLATE utf8_unicode_ci NOT NULL,
  `tag_sequence` tinyint(2) NOT NULL,
  `internal_index_sequence` tinyint(2) NOT NULL DEFAULT '0',
  `created_date` datetime NOT NULL,
  `last_modified_date` datetime NOT NULL,
  PRIMARY KEY (`tag_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

$rating_rules_insert_sql = array(
    '1' => array("insert" => " (1, 'Js rule1', '{payment_methods_parent_id}', 'does_not_equal', '12', NOW(), NOW()); "),
    '2' => array("insert" => " (2, 'Js rule2', '{customers_groups_id}', 'does_not_equal', '1', NOW(), NOW()); "),
    '3' => array("insert" => " (3, 'Js rule3', '{customers_groups_id}', 'does_not_equal', '6', NOW(), NOW()); "),
    '4' => array("insert" => " (4, 'Js rule4', '{customers_groups_id}', 'does_not_equal', '8', NOW(), NOW()); "),
    '5' => array("insert" => " (5, 'Js rule5', '{subtotal}', 'greater', '0', NOW(), NOW()); ")
);

insert_new_records(TABLE_RATING_RULES, 'rule_id', $rating_rules_insert_sql, $DBTables, "(`rule_id`, `rule_name`, `rule_type`, `rule_operator`, `rule_value`, `created_date`, `last_modified_date`)");

$rating_rules_tags_insert_sql = array(
//    '1' => array("insert" => " (1, 1); "),
//    '2' => array("insert" => " (2, 1); "),
//    '3' => array("insert" => " (3, 1); "),
//    '4' => array("insert" => " (4, 1); "),
//    '5' => array("insert" => " (5, 1); ")
);

insert_new_records(TABLE_RATING_RULES_TAGS, 'rule_id', $rating_rules_tags_insert_sql, $DBTables, "(`rule_id`, `tag_id`)");

$rating_tags_insert_sql = array();

$tag1 = '<p style=\"text-align:center;\"><a href=\"http://www.sitejabber.com/requested-review?biz_id=5534bcfde4c70\" target=\"_blank\">Rate your experience with us</a></p>';
$tag2 = '<p style=\"text-align:center;\"><a href=\"https://www.trustpilot.com/evaluate/www.offgamers.com\" target=\"_blank\">Review your experience with us</a></p>';

$tag3 = "var productsPurchased= 'URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=';var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;ga.src = '//eval.bizrate.com/js/pos_90268.js';var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);";
$tag4 = '<p style=\"text-align:center;\"><a href=\"https://trustedcompany.com/my/comment/reply/30724\" target=\"_blank\">Rate your experience with us</a></p>';
$tag5 = '<p style=\"text-align:center;\"><a href=\"https://www.mywot.com/en/scorecard/offgamers.com\" target=\"_blank\"><img style=\"width:500px;\" src=\"http://image.offgamers.com/banners/1/hpen_1506201101_.jpg\"></a></p>';

$data = array(
    'tag_id' => 1,
    'tag_name' => 'test html tag1',
    'tag_method' => 'JS',
    'tag_trigger_content' => json_encode(array('openDialog("alert","' . $tag1 . '");', 'openDialog("alert","' . $tag2 . '");', $tag3, 'openDialog("alert","' . $tag4 . '");', 'openDialog("alert","' . $tag5 . '");')),
    'tag_sequence' => 1,
    'internal_index_sequence' => 0,
    'created_date' => 'now()',
    'last_modified_date' => 'now()',
);
tep_db_perform(TABLE_RATING_TAGS, $data, 'insert');
?>