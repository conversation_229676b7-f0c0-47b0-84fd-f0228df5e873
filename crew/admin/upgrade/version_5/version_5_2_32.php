<?php
$add_new_tables = array();
// Create new table
$add_new_tables["payu"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `payu` (
                    `order_id` varchar(11) NOT NULL,
                    `transaction_id` varchar(40) DEFAULT NULL,
                    `payu_order_id` varchar(15) DEFAULT NULL,
                    `account_id` int(8) unsigned NOT NULL,
                    `amount` decimal(15,2) DEFAULT NULL,
                    `currency` char(3) NOT NULL,
                    `code` varchar(15) DEFAULT NULL,
                    `state` varchar(32) DEFAULT NULL,
                    `response_code` varchar(64) DEFAULT NULL,
                    `buyer_dni` varchar(20) DEFAULT NULL,
                    `payer_name` varchar(100) DEFAULT NULL,
                    `payer_address` varchar(255) DEFAULT NULL,
                    `payer_city` varchar(100) DEFAULT NULL,
                    `payer_state` varchar(15) DEFAULT NULL,
                    `payer_postcode` varchar(15) DEFAULT NULL,
                    `payer_country` char(3) DEFAULT NULL,
                    `payer_contact_phone` varchar(20) DEFAULT NULL,
                    `cc_number` varchar(20) DEFAULT NULL,
                    `cc_expiration_date` varchar(7) DEFAULT NULL,
                    `operation_date` bigint(15) DEFAULT NULL,
                    `ip_address` varchar(15) DEFAULT NULL,
                    `date_added` datetime NOT NULL,
                    `last_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    `order_expiration` datetime DEFAULT NULL,
                    `capture_request` tinyint(1) DEFAULT '0',
                    `data` text NOT NULL,
                    PRIMARY KEY (`order_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
                    "data" => "");

$add_new_tables["payu_payment_data"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `payu_payment_data` (
                    `customers_id` int(11) unsigned NOT NULL,
                    `payment_method_id` int(5) unsigned NOT NULL,
                    `buyer_dni` varchar(20) DEFAULT NULL,
                    `buyer_cnpj` varchar(14) DEFAULT NULL,
                    `payer_name` varchar(100) DEFAULT NULL,
                    `payer_address` varchar(255) DEFAULT NULL,
                    `payer_city` varchar(100) DEFAULT NULL,
                    `payer_state` varchar(15) DEFAULT NULL,
                    `payer_postcode` varchar(15) DEFAULT NULL,
                    `payer_country` char(3) DEFAULT NULL,
                    `payer_contact_phone` varchar(20) DEFAULT NULL,
                    `cc_number` varchar(20) DEFAULT NULL,
                    `cc_expiration_date` varchar(7) DEFAULT NULL,
                    `cc_security_code` varchar(4) DEFAULT NULL,
                    `ip_address` varchar(15) DEFAULT NULL,
                    `token` varchar(40) DEFAULT NULL,
                    `tokenized` tinyint(1) NOT NULL DEFAULT '0',
                    PRIMARY KEY (`customers_id`,`payment_method_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
                    "data" => "");

$add_new_tables["payu_status_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `payu_status_history` (
                    `status_history_id` int(11) unsigned NOT NULL auto_increment,
                    `order_id` varchar(11) NOT NULL,
                    `payu_order_id` varchar(15) DEFAULT NULL,
                    `transaction_id` varchar(40) DEFAULT NULL,
                    `state` varchar(32) DEFAULT NULL,
                    `response_code` varchar(64) DEFAULT NULL,
                    `date` datetime NOT NULL,
                    `changed_by` varchar(128) DEFAULT NULL,
                    PRIMARY KEY (`status_history_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
                    "data" => "");

add_new_tables($add_new_tables, $DBTables);

/* Add New Field */
$add_new_field = array();
$add_new_field['adyen'] = array(
    array(
        "field_name" => "adyen_cc_issuer_country",
        "field_attr" => "char(2) NULL",
        "add_after" => 'adyen_cc_avs_result'
    )
);
add_field($add_new_field);
?>