<?php

$existing_log_api_restock_fields = get_table_fields(TABLE_LOG_API_RESTOCK);

if (!in_array('currency_code', $existing_log_api_restock_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_LOG_API_RESTOCK . " ADD `currency_code` CHAR(3) NOT NULL DEFAULT 'USD' AFTER `description`;");
}

if (!in_array('currency_rate', $existing_log_api_restock_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_LOG_API_RESTOCK . " ADD `currency_rate` DECIMAL(13,8) NOT NULL DEFAULT '1.00000000' AFTER `currency_code`;");
}

if (in_array('currency_rate', $existing_log_api_restock_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_LOG_API_RESTOCK . " CHANGE  `currency_rate`  `currency_rate` DECIMAL( 13, 8 ) NOT NULL DEFAULT  '1.00000000';");
}

if (!in_array('currency_settle_amount', $existing_log_api_restock_fields)) {
    tep_db_query("ALTER TABLE  " . TABLE_LOG_API_RESTOCK . " ADD  `currency_settle_amount` DECIMAL( 15,6) NOT NULL AFTER  `currency_rate`;");
}

$update_sql = " UPDATE " . TABLE_LOG_API_RESTOCK . " SET currency_settle_amount =settle_amount WHERE currency_settle_amount=0";
tep_db_query($update_sql);

