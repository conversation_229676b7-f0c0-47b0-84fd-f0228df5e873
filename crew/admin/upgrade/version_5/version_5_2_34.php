<?php

$featured_banners = array(
    8=> array( //  tag id
        'key' => 'DIRECT_TOP_UP',
        'lang' => array(
            1 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/touch.png',
                'cid' => 21222,
            ),
            
            4 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/lod.png',
                'cid' => 22784,
            ),
        )
    ),
    
     7=> array( //  tag id
        'key' => 'GAME_CARD',
        'lang' => array(
            4 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/fifa16.png',
                'cid' => 21894,
            ),
            
            2 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/china.png',
                'href' => 'http://www.offgamers.com/blog/offgamers-%E4%B8%AD%E6%96%87%E4%BA%A7%E5%93%81%E5%A4%A7%E5%85%A8/',
               
            ),
        )
    ),
    
    6=> array( //  tag id
        'key' => 'GIFT_CARDS',
        'lang' => array(
            1 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/cashu.png',
                'cid' => 22790,
            ),
            
           
        )
    ),
    
    
     5=> array( //  tag id
        'key' => 'PC_GAMES',
        'lang' => array(
            1 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/gw2.png',
                'cid' => 21704,
            ),
            
            2 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/guyu2.png',
                'cid' => 22926,
            ),
             4 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/gw2.png',
                'cid' => 21704,
            ),
        )
    ),
    
    
    
);

foreach ($featured_banners as $tag_id => $banner_info) {
    
    foreach ($banner_info['lang'] as $lang_id => $lang_banner_info) {
         
            $json_str = array(
                'src' => $lang_banner_info['src'],
                'href' => isset($lang_banner_info['href']) ? $lang_banner_info['href'] : '',
            );
            
            if (isset($lang_banner_info['cid'])) {
                $json_str['createURL'] = array(
                    'category/index', array('cid' => $lang_banner_info['cid'])
                );
            }
        
        $update_sql = " UPDATE categories_tag_description 
                            SET tag_featured_banner = '" . json_encode($json_str) . "' 
                        WHERE tag_id = '" . $tag_id . "'
                            AND language_id = '" . $lang_id . "'";
        tep_db_query($update_sql);
    }
}
?>