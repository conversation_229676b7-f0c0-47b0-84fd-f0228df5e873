<?php
$add_new_tables = array();

$add_new_tables["url_rewrite"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `url_rewrite` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `old_url` text COLLATE utf8_unicode_ci NOT NULL,
  `new_url` text COLLATE utf8_unicode_ci,
  `redirect_type` int(3) NOT NULL DEFAULT '301',
  `is_system` tinyint(1) NOT NULL DEFAULT '0',
  `created_datetime` datetime NOT NULL,
  `last_access_datetime` datetime NOT NULL,
  `hits` int(11) NOT NULL DEFAULT '0',
  `description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index_old_url` (`old_url`(125))
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["products_extra_info"] = array(
 "structure" => "CREATE TABLE IF NOT EXISTS `products_extra_info` (
  `products_id` int(11) unsigned NOT NULL,
  `products_extra_info_key` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `products_extra_info_value` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`products_id`,`products_extra_info_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;",
"data" => "");

add_new_tables($add_new_tables, $DBTables);