<?php

# Update G2G Product Type display text
$update_sql = array();
$update_sql['custom_products_type_child_lang'] = array(
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'IN-GAME ITEMS' ",
        "where_str" => " custom_products_type_child_id = '16' AND languages_id = '1' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#28216;&#25103;&#35013;&#22791;' ",
        "where_str" => " custom_products_type_child_id = '16' AND languages_id = '2' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#36938;&#25138;&#35037;&#20633;' ",
        "where_str" => " custom_products_type_child_id = '16' AND languages_id = '3' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'ITEM IN-GAME' ",
        "where_str" => " custom_products_type_child_id = '16' AND languages_id = '4' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'GAME ACCOUNTS' ",
        "where_str" => " custom_products_type_child_id = '5' AND languages_id = '1' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#28216;&#25103;&#36134;&#21495;' ",
        "where_str" => " custom_products_type_child_id = '5' AND languages_id = '2' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#36938;&#25138;&#36076;&#34399;' ",
        "where_str" => " custom_products_type_child_id = '5' AND languages_id = '3' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'AKUN GAME' ",
        "where_str" => " custom_products_type_child_id = '5' AND languages_id = '4' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'IN-GAME CURRENCY' ",
        "where_str" => " custom_products_type_child_id = '1' AND languages_id = '1' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#28216;&#25103;&#24065;' ",
        "where_str" => " custom_products_type_child_id = '1' AND languages_id = '2' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = '&#36938;&#25138;&#24163;' ",
        "where_str" => " custom_products_type_child_id = '1' AND languages_id = '3' "
    ),
    array(
        "field_name" => "custom_products_type_child_name",
        "update" => " custom_products_type_child_name = 'MATA UANG IN-GAME' ",
        "where_str" => " custom_products_type_child_id = '1' AND languages_id = '4' "
    ),
);
advance_update_records($update_sql, $DBTables);


// Update sort order for IN_GAME ITEMS above Account
$update_sql = array();

$update_sql['custom_products_type_child'] = array(
    array("field_name" => "custom_products_type_child_name",
        "update" => " sort_order=3600 ",
        "where_str" => " custom_products_type_child_id=16"
    )
);

advance_update_records($update_sql, $DBTables);
?>