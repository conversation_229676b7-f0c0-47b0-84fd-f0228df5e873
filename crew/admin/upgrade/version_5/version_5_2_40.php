<?php
$add_new_tables = array();

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_insert_sql = array();
	$admin_files_insert_sql["aft_rule.php"] = array(	"insert" => " ('aft_rule.php', 0, '".$row_sql["admin_files_id"]."', '1') ",
														"update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql["admin_files_id"]."', admin_groups_id='1' "
						   								);
	insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='aft_rule.php' AND admin_files_is_boxes=0 ");
}

$add_new_tables["aft_rule"] = array(
 "structure" => "CREATE TABLE IF NOT EXISTS `aft_rule` (
  `aft_rule_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `aft_rule_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `aft_rule_value` text COLLATE utf8_unicode_ci NOT NULL,
  `aft_rule_sort_order` int(5) NOT NULL,
  `aft_rule_status` tinyint(1) NOT NULL DEFAULT '1',
  `created_date` datetime NOT NULL,
  `last_modified_date` datetime NOT NULL,
  PRIMARY KEY (`aft_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1 ;",
"data" => "");

add_new_tables($add_new_tables, $DBTables);