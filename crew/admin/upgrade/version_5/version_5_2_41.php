<?php
// Update existing offline payment description
$update_sql = array();
$update_sql[TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION] = array(
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'OFFLINE PAYMENTS (1 to 2 business days) '",
        "where_str" => "payment_methods_types_id = 4 AND languages_id = 1"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '离线支付（1-2个工作日）'",
        "where_str" => "payment_methods_types_id = 4 AND languages_id = 2"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '離線支付（1-2個工作日）'",
        "where_str" => "payment_methods_types_id = 4 AND languages_id = 3"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'Pembayaran Offline (1-2hari hari kerja) '",
        "where_str" => "payment_methods_types_id = 4 AND languages_id = 4"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'OFFLINE PAYMENTS (1 to 2 business days) '",
        "where_str" => "payment_methods_types_id = 7 AND languages_id = 1"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '离线支付（1-2个工作日）'",
        "where_str" => "payment_methods_types_id = 7 AND languages_id = 2"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '離線支付（1-2個工作日）'",
        "where_str" => "payment_methods_types_id = 7 AND languages_id = 3"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'Pembayaran Offline (1-2hari hari kerja) '",
        "where_str" => "payment_methods_types_id = 7 AND languages_id = 4"
    ),
);
advance_update_records($update_sql, $DBTables);
