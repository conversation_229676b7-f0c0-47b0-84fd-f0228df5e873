<?php
//PAYPAL EC
$add_new_tables = array();
$add_new_tables["paypalec"] = array(
	"structure" => " CREATE TABLE `paypalec` (
					`paypal_order_id` varchar(25) NOT NULL DEFAULT '',
					`txn_type` varchar(25) DEFAULT NULL,
					`payment_type` varchar(15) DEFAULT NULL,
					`payment_status` varchar(20) DEFAULT NULL,
					`pending_reason` varchar(15) DEFAULT NULL,
					`mc_currency` char(3) DEFAULT NULL,
					`first_name` varchar(32) DEFAULT NULL,
					`last_name` varchar(32) DEFAULT NULL,
					`address_name` varchar(32) DEFAULT NULL,
					`address_street` varchar(128) DEFAULT NULL,
					`address_city` varchar(32) DEFAULT NULL,
					`address_state` varchar(32) DEFAULT NULL,
					`address_zip` varchar(10) DEFAULT NULL,
					`address_country` varchar(64) DEFAULT NULL,
					`address_status` varchar(11) DEFAULT NULL,
					`payer_email` varchar(128) DEFAULT NULL,
					`payer_id` varchar(32) DEFAULT NULL,
					`payer_status` varchar(10) DEFAULT NULL,
					`payment_date` varchar(30) DEFAULT NULL,
					`business` varchar(128) DEFAULT NULL,
					`receiver_email` varchar(128) DEFAULT NULL,
					`receiver_id` varchar(32) DEFAULT NULL,
					`txn_id` varchar(17) NOT NULL DEFAULT '',
					`mc_gross` decimal(12, 2) DEFAULT NULL,
					`mc_fee` decimal(12, 2) DEFAULT NULL,
					`payment_gross` decimal(12, 2) DEFAULT NULL,
					`payment_fee` decimal(12, 2) DEFAULT NULL,
					`notify_version` varchar(10) DEFAULT NULL,
					`verify_sign` varchar(128) DEFAULT NULL,
					`residence_country` varchar(10) DEFAULT NULL,
					`protection_eligibility` varchar(15) DEFAULT NULL,
					`last_modified` datetime DEFAULT NULL,
					`date_added` datetime DEFAULT NULL,
					`data` text COLLATE utf8_unicode_ci,
					PRIMARY KEY (`paypal_order_id`, `txn_id`)
				   ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
	"data" => "");
$add_new_tables["paypalec_reference_transaction"] = array(
	"structure" => "CREATE TABLE `paypalec_reference_transaction` (
					`customer_id` varchar(25) NOT NULL DEFAULT '',
					`billing_id` varchar(25) NOT NULL DEFAULT '',
					`date` datetime NOT NULL,
					PRIMARY KEY (`customer_id`, `billing_id`)
					) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
	"data" => "");
$add_new_tables["paypalec_status_history"] = array(
	"structure" => "CREATE TABLE `paypalec_status_history` (
					`paypalec_status_history_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
					`paypal_order_id` varchar(25) DEFAULT NULL,
					`payment_status` varchar(20) DEFAULT NULL,
					`reason` varchar(50) DEFAULT NULL,
					`date` datetime DEFAULT NULL,
					`changed_by` varchar(128) DEFAULT NULL,
					PRIMARY KEY (`paypalec_status_history_id`)
					) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
	"data" => "");
$add_new_tables["paypalec_reference_transaction_history"] = array(
	"structure" => "CREATE TABLE `paypalec_reference_transaction_history` (
					`history_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
					`billing_id` varchar(25) DEFAULT NULL,
					`customer_id` varchar(25) DEFAULT NULL,
					`order_id` varchar(25) DEFAULT NULL,
					`date` datetime DEFAULT NULL,
					`status` varchar(128) DEFAULT NULL,
					`data` text COLLATE utf8_unicode_ci,
					PRIMARY KEY (`history_id`)
					) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
	"data" => "");
add_new_tables($add_new_tables, $DBTables);
?>