<?php
// Drop existing primary key (customer_id, billing_id) for paypalec_reference_transaction table
drop_index_key ("paypalec_reference_transaction", 'PRIMARY KEY', 'primary', $DBTables, array('customer_id', 'billing_id'));
// End of drop existing primary key (customer_id, billing_id) for paypalec_reference_transaction table

/* Add New Field */
$add_new_field = array();
$add_new_field['paypalec_reference_transaction'] = array(
    array(
        "field_name" => "paypalec_reference_transaction_id",
        "field_attr" => "BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY FIRST",
    ),
    array(
        "field_name" => "merchant_email",
        "field_attr" => "VARCHAR( 128 ) NOT NULL AFTER `billing_id`",
    )
);
add_field($add_new_field);

add_index_key ('paypalec_reference_transaction', 'index_customer_id', 'index', 'customer_id', $DBTables);
add_index_key ('paypalec_reference_transaction', 'index_merchant_email', 'index', 'merchant_email', $DBTables);

$payment_gateway_select_sql = "	SELECT payment_methods_id
								FROM " . TABLE_PAYMENT_METHODS . "
								WHERE payment_methods_filename = 'paypalEC.php'";
$payment_gateway_result_sql = tep_db_query($payment_gateway_select_sql);

if ($paypal_row = tep_db_fetch_array($payment_gateway_result_sql)) {
	$payment_methods_fields_select_sql = "	SELECT payment_methods_fields_system_type
											FROM " . TABLE_PAYMENT_METHODS_FIELDS . " 
											WHERE payment_methods_fields_system_type = 'MODULE_PAYPALEC_SEND_EMAIL'
												AND payment_methods_id = '".(int)$paypal_row['payment_methods_id']."'
												AND payment_methods_mode = 'SEND'";
	$payment_methods_fields_result_sql = tep_db_query($payment_methods_fields_select_sql);
	if (!tep_db_num_rows($payment_methods_fields_result_sql)) {
		$data = array(	'payment_methods_fields_title'=>'PayPalEC Email',
						'payment_methods_fields_pre_info'=>'',
						'payment_methods_fields_post_info'=>'',
						'payment_methods_fields_required'=>1,
						'payment_methods_fields_type'=>'1',
						'payment_methods_fields_system_type'=>'MODULE_PAYPALEC_SEND_EMAIL',
						'payment_methods_fields_size'=>'55',
						'payment_methods_fields_option'=>'NULL',
						'payment_methods_fields_options_title'=>'0',
						'payment_methods_fields_sort_order'=>0,
						'payment_methods_id' => (int)$paypal_row['payment_methods_id'],
						'payment_methods_mode' => 'SEND',
						'payment_methods_fields_system_mandatory' => 1,
						'payment_methods_fields_status' => '1'
					);
		tep_db_perform(TABLE_PAYMENT_METHODS_FIELDS, $data);
	}
}
?>
