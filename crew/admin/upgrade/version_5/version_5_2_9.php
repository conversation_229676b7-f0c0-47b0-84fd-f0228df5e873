<?php
tep_db_query('TRUNCATE TABLE c2c_products_listing_remarks_history;');
tep_db_query('ALTER TABLE c2c_products_listing_remarks_history ENGINE=InnoDB;');
add_index_key ('c2c_products_listing_remarks_history', 'index_date', 'index', 'remarks_added_date', $DBTables);

tep_db_query('ALTER TABLE sessions ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories ENGINE=InnoDB;');
tep_db_query('ALTER TABLE categories_description ENGINE=InnoDB;');
tep_db_query('ALTER TABLE products ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_products ENGINE=InnoDB;');
tep_db_query('ALTER TABLE orders_status_history ENGINE=InnoDB;');
tep_db_query('ALTER TABLE sso_token ENGINE=InnoDB;');

add_index_key ('api_tm_transaction_identifier', 'index_login_ip', 'index', 'customers_login_ip', $DBTables);
?>