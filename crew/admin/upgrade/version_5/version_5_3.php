<?php
// Add new field to invoice_running_number and credit_note_running_number tables
$add_new_field = array();

$add_new_field['invoice_running_number'] = array(
    array(
        "field_name" => "invoice_year",
        "field_attr" => "int(4) unsigned NOT NULL DEFAULT 2015",
        "add_after" => 'invoice_month'
    )
);

$add_new_field['credit_note_running_number'] = array(
    array(
        "field_name" => "cn_year",
        "field_attr" => "int(4) unsigned NOT NULL DEFAULT 2015",
        "add_after" => 'cn_month'
    )
);

add_field($add_new_field);
// End of add new field to invoice_running_number and credit_note_running_number tables

// Drop existing primary key (country_iso2, invoice_month) and add new primary key (country_iso2, invoice_month, invoice_year) for invoice_running_number table
drop_index_key ("invoice_running_number", 'PRIMARY KEY', 'primary', $DBTables, array('country_iso2', 'invoice_month'));

add_index_key ("invoice_running_number", 'PRIMARY KEY', 'primary', 'country_iso2, invoice_month, invoice_year', $DBTables);
// End of drop existing primary key (country_iso2, invoice_month) and add new primary key (country_iso2, invoice_month, invoice_year) for invoice_running_number table

// Drop existing primary key (country_iso2, cn_month) and add new primary key (country_iso2, cn_month, cn_year) for credit_note_running_number table
drop_index_key ("credit_note_running_number", 'PRIMARY KEY', 'primary', $DBTables, array('country_iso2', 'cn_month'));

add_index_key ("credit_note_running_number", 'PRIMARY KEY', 'primary', 'country_iso2, cn_month, cn_year', $DBTables);
// End of drop existing primary key (country_iso2, cn_month) and add new primary key (country_iso2, cn_month, cn_year) for credit_note_running_number table

// Update Jan and Feb to year 2016 in invoice_running_number and credit_note_running_number tables
$update_sql = array();

$update_sql['invoice_running_number'] = array(array(	"field_name" => "invoice_year",
                                                            "update" => " invoice_year = 2016 ",
                                                            "where_str" => " invoice_month=1 AND invoice_year = 2015"
                                                        ),
                                                    array(	"field_name" => "invoice_year",
                                                            "update" => " invoice_year = 2016 ",
                                                            "where_str" => " invoice_month=2 AND invoice_year = 2015"
                                                        )
                                             );

$update_sql['credit_note_running_number'] = array(array(	"field_name" => "cn_year",
                                                            "update" => " cn_year = 2016 ",
                                                            "where_str" => " cn_month=1 AND cn_year = 2015"
                                                        ),
                                                    array(	"field_name" => "cn_year",
                                                            "update" => " cn_year = 2016 ",
                                                            "where_str" => " cn_month=2 AND cn_year = 2015"
                                                        )
                                             );

advance_update_records($update_sql, $DBTables);
// End of update Jan and Feb to year 2016 in invoice_running_number and credit_note_running_number tables
?>