<?php
// rating tags content
$tag1 = '<a href=\"http://www.sitejabber.com/requested-review?biz_id=5534bcfde4c70\" target=\"_blank\" style=\"width:100%;height:100%;display:block;\"></a>';
$tag2 = '<a href=\"https://www.trustpilot.com/evaluate/www.offgamers.com\" target=\"_blank\" style=\"width:100%;height:100%;display:block;\"></a>';

$tag3 = "var productsPurchased= 'URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=';var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;ga.src = '//eval.bizrate.com/js/pos_90268.js';var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);";
$tag4 = '<a href=\"https://trustedcompany.com/my/comment/reply/30724\" target=\"_blank\" style=\"width:100%;height:100%;display:block;\"></a>';
$tag5 = '<a href=\"https://www.mywot.com/en/scorecard/offgamers.com\" target=\"_blank\" style=\"width:100%;height:100%;display:block;\"></a>';

tep_db_query("DELETE FROM " . TABLE_RATING_TAGS . " WHERE tag_id =1" );

$tag_trigger_content=json_encode(array('checkoutSuccessPopup("basic","' . $tag1 . '");', 'checkoutSuccessPopup("basic","' . $tag2 . '");', $tag3, 'checkoutSuccessPopup("basic","' . $tag4 . '");', 'checkoutSuccessPopup("basic","' . $tag5 . '");'));
$data = array(
    'tag_id' => 1,
    'tag_name' => 'test html tag1',
    'tag_method' => 'JS',
    'tag_trigger_content' => $tag_trigger_content,
    'tag_sequence' => 1,
    'internal_index_sequence' => 0,
    'created_date' => 'now()',
    'last_modified_date' => 'now()',
);
tep_db_perform(TABLE_RATING_TAGS, $data, 'insert');

//$rating_rules_tags_insert_sql = array(
//    '1' => array("insert" => " (1, 1); "),
//    '2' => array("insert" => " (2, 1); "),
//    '3' => array("insert" => " (3, 1); "),
//    '4' => array("insert" => " (4, 1); "),
//    '5' => array("insert" => " (5, 1); ")
//);

//insert_new_records(TABLE_RATING_RULES_TAGS, 'rule_id', $rating_rules_tags_insert_sql, $DBTables, "(`rule_id`, `tag_id`)");

