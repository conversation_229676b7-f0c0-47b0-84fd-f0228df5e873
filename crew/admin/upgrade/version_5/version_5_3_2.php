<?php

$add_new_tables = array();

// Create new table
$add_new_tables["currencies_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `currencies_history` (
  `id` bigint(11) unsigned NOT NULL AUTO_INCREMENT,      
  `currencies_id` int(11) unsigned NOT NULL,
  `code` char(3) COLLATE utf8_unicode_ci NOT NULL,
  `buy_value` decimal(13,8) NOT NULL,
  `spot_value` decimal(13,8) NOT NULL,
  `sell_value` decimal(13,8) NOT NULL,
  `date_from` datetime NOT NULL,
  `date_to` datetime NOT NULL,
  `version` int(11) unsigned NOT NULL,
  `last_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index_code` (`code`) 
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1;",
    "data" => "");


add_new_tables($add_new_tables, $DBTables);
$i = 0;
$currencies_history_insert_sql = array(
    $i+=1 => array("insert" => " (1, 32, 'ARS', '12.73605000', '12.93000000', '13.12395000', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (2, 7, 'AUD', '1.34884915', '1.36939000', '1.38993085', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (3, 33, 'BRL', '3.90065910', '3.96006000', '4.01946090', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (4, 20, 'BND', '1.39249450', '1.41370000', '1.43490550', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (5, 8, 'CAD', '1.36461900', '1.38540000', '1.40618100', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (6, 34, 'CLP', '697.42925000', '708.05000000', '718.67075000', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (7, 3, 'CNY', '6.39511250', '6.49250000', '6.58988750', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (8, 16, 'CZK', '24.48710000', '24.86000000', '25.23290000', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (9, 17, 'DKK', '6.76947160', '6.87256000', '6.97564840', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (10, 2, 'AUG', '0.00000000', '0.03835673', '0.00000000', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (11, 24, 'EGP', '7.70763485', '7.82501000', '7.94238515', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (12, 6, 'EUR', '0.90698800', '0.92080000', '0.93461200', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (13, 9, 'HKD', '7.63427205', '7.75053000', '7.86678795', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (14, 18, 'HUF', '285.51899500', '289.86700000', '294.21500500', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (15, 35, 'IDR', '13615.55650000', '13822.90000000', '14030.24350000', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (16, 28, 'ILS', '3.83054680', '3.88888000', '3.94721320', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (17, 10, 'JPY', '118.81562500', '120.62500000', '122.43437500', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (18, 25, 'JOD', '0.69839455', '0.70903000', '0.71966545', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (19, 21, 'KWD', '0.29889825', '0.30345000', '0.30800175', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (20, 4, 'MYR', '4.23067350', '4.29510000', '4.35952650', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (21, 27, 'MXN', '16.94633400', '17.20440000', '17.46246600', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (22, 11, 'NZD', '1.44236505', '1.46433000', '1.48629495', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (23, 19, 'NOK', '8.71192115', '8.84459000', '8.97725885', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (24, 31, 'PHP', '46.22112500', '46.92500000', '47.62887500', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (25, 14, 'PLN', '3.89183350', '3.95110000', '4.01036650', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (26, 5, 'GBP', '0.66843085', '0.67861000', '0.68878915', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (27, 0, 'QAR', '3.58746850', '3.64210000', '3.69673150', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (28, 29, 'RUB', '72.30491000', '73.40600000', '74.50709000', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (29, 22, 'SAR', '3.69769000', '3.75400000', '3.81031000', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (30, 12, 'SGD', '1.39249450', '1.41370000', '1.43490550', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (31, 15, 'SEK', '8.33304090', '8.45994000', '8.58683910', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (32, 13, 'CHF', '0.98647750', '1.00150000', '1.01652250', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (33, 26, 'THB', '35.47970000', '36.02000000', '36.56030000', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (34, 23, 'AED', '3.61741250', '3.67250000', '3.72758750', '2016-01-01 08:30:00', '2016-01-02 08:30:00', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (35, 1, 'USD', '1.00000000', '1.00000000', '1.00000000', '2016-01-01 08:30:00', '2999-12-31 23:59:59', 1, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (36, 32, 'ARS', '12.73841400', '12.93240000', '13.12638600', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (37, 7, 'AUD', '1.34882945', '1.36937000', '1.38991055', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (38, 33, 'BRL', '3.89971350', '3.95910000', '4.01848650', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (39, 20, 'BND', '1.38998275', '1.41115000', '1.43231725', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (40, 34, 'CLP', '698.02222000', '708.65200000', '719.28178000', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (41, 3, 'CNY', '6.40767125', '6.50525000', '6.60282875', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (42, 16, 'CZK', '24.51862000', '24.89200000', '25.26538000', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (43, 17, 'DKK', '6.76957010', '6.87266000', '6.97574990', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (44, 2, 'AUG', '0.00000000', '0.03835673', '0.03835673', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (45, 24, 'EGP', '7.71264850', '7.83010000', '7.94755150', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (46, 6, 'EUR', '0.90674175', '0.92055000', '0.93435825', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (47, 9, 'HKD', '7.63430160', '7.75056000', '7.86681840', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (48, 18, 'HUF', '285.53180000', '289.88000000', '294.22820000', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (49, 35, 'IDR', '13618.51150000', '13825.90000000', '14033.28850000', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (50, 28, 'ILS', '3.83052710', '3.88886000', '3.94719290', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (51, 25, 'JOD', '0.69841425', '0.70905000', '0.71968575', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (52, 21, 'KWD', '0.29884802', '0.30339900', '0.30794999', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (53, 4, 'MYR', '4.23838605', '4.30293000', '4.36747395', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (54, 11, 'NZD', '1.43942975', '1.46135000', '1.48327025', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (55, 19, 'NOK', '8.71204920', '8.84472000', '8.97739080', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (56, 31, 'PHP', '46.34858400', '47.05440000', '47.76021600', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (57, 14, 'PLN', '3.88567725', '3.94485000', '4.00402275', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (58, 5, 'GBP', '0.66660565', '0.67675700', '0.68690836', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (59, 0, 'QAR', '3.58584325', '3.64045000', '3.69505675', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (60, 29, 'RUB', '71.90362100', '72.99860000', '74.09357900', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (61, 22, 'SAR', '3.69680350', '3.75310000', '3.80939650', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (62, 12, 'SGD', '1.38998275', '1.41115000', '1.43231725', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (63, 15, 'SEK', '8.33324775', '8.46015000', '8.58705225', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (64, 26, 'THB', '35.49496750', '36.03550000', '36.57603250', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (65, 23, 'AED', '3.61785575', '3.67295000', '3.72804425', '2016-01-02 08:30:00', '2016-01-03 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (66, 32, 'ARS', '12.73211000', '12.92600000', '13.11989000', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (67, 33, 'BRL', '3.89991050', '3.95930000', '4.01868950', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (68, 34, 'CLP', '697.47850000', '708.10000000', '718.72150000', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (69, 16, 'CZK', '24.51802900', '24.89140000', '25.26477100', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (70, 27, 'MXN', '16.94623550', '17.20430000', '17.46236450', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (71, 11, 'NZD', '1.43869100', '1.46060000', '1.48250900', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (72, 31, 'PHP', '46.15838050', '46.86130000', '47.56421950', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (73, 14, 'PLN', '3.88557875', '3.94475000', '4.00392125', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (74, 0, 'QAR', '3.58672975', '3.64135000', '3.69597025', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (75, 26, 'THB', '35.39646750', '35.93550000', '36.47453250', '2016-01-03 08:30:00', '2016-01-04 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (76, 32, 'ARS', '12.75575000', '12.95000000', '13.14425000', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 4, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (77, 7, 'AUD', '1.35169580', '1.37228000', '1.39286420', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (78, 33, 'BRL', '3.89978245', '3.95917000', '4.01855755', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 4, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (79, 20, 'BND', '1.39805975', '1.41935000', '1.44064025', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (80, 8, 'CAD', '1.36455990', '1.38534000', '1.40612010', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (81, 34, 'CLP', '698.02222000', '708.65200000', '719.28178000', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 4, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (82, 3, 'CNY', '6.42112635', '6.51891000', '6.61669365', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (83, 16, 'CZK', '24.53812300', '24.91180000', '25.28547700', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 4, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (84, 17, 'DKK', '6.77548010', '6.87866000', '6.98183990', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (85, 24, 'EGP', '7.71255000', '7.83000000', '7.94745000', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (86, 6, 'EUR', '0.90796020', '0.92178700', '0.93561381', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (87, 9, 'HKD', '7.63435085', '7.75061000', '7.86686915', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (88, 18, 'HUF', '286.21342000', '290.57200000', '294.93058000', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (89, 35, 'IDR', '13617.62500000', '13825.00000000', '14032.37500000', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (90, 28, 'ILS', '3.84148030', '3.89998000', '3.95847970', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (91, 10, 'JPY', '118.52702000', '120.33200000', '122.13698000', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (92, 25, 'JOD', '0.69836599', '0.70900100', '0.71963602', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (93, 4, 'MYR', '4.23057500', '4.29500000', '4.35942500', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (94, 27, 'MXN', '16.95913900', '17.21740000', '17.47566100', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (95, 11, 'NZD', '1.44416760', '1.46616000', '1.48815240', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 4, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (96, 19, 'NOK', '8.70413965', '8.83669000', '8.96924035', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (97, 31, 'PHP', '46.16301000', '46.86600000', '47.56899000', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 4, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (98, 14, 'PLN', '3.86712970', '3.92602000', '3.98491030', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 4, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (99, 5, 'GBP', '0.66856580', '0.67874700', '0.68892821', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (100, 0, 'QAR', '3.58717300', '3.64180000', '3.69642700', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 4, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (101, 29, 'RUB', '72.30550100', '73.40660000', '74.50769900', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (102, 22, 'SAR', '3.69679365', '3.75309000', '3.80938635', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (103, 12, 'SGD', '1.39805975', '1.41935000', '1.44064025', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (104, 15, 'SEK', '8.32701270', '8.45382000', '8.58062730', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (105, 13, 'CHF', '0.98539400', '1.00040000', '1.01540600', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 2, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (106, 26, 'THB', '35.53269300', '36.07380000', '36.61490700', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 4, '2016-02-24 15:24:32');  "),
    $i+=1 => array("insert" => " (107, 23, 'AED', '3.61682150', '3.67190000', '3.72697850', '2016-01-04 08:30:00', '2016-01-05 08:30:00', 3, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (108, 32, 'ARS', '12.98653550', '13.18430000', '13.38206450', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (109, 7, 'AUD', '1.37046005', '1.39133000', '1.41219995', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (110, 33, 'BRL', '3.98750655', '4.04823000', '4.10895345', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (111, 20, 'BND', '1.40404855', '1.42543000', '1.44681145', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (112, 8, 'CAD', '1.37189815', '1.39279000', '1.41368185', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 3, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (113, 34, 'CLP', '705.99875000', '716.75000000', '727.50125000', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (114, 3, 'CNY', '6.43711290', '6.53514000', '6.63316710', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (115, 16, 'CZK', '24.58757000', '24.96200000', '25.33643000', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (116, 17, 'DKK', '6.79014675', '6.89355000', '6.99695325', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (117, 6, 'EUR', '0.90992330', '0.92378000', '0.93763670', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (118, 9, 'HKD', '7.63456755', '7.75083000', '7.86709245', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (119, 18, 'HUF', '286.13757500', '290.49500000', '294.85242500', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (120, 35, 'IDR', '13733.56935000', '13942.71000000', '14151.85065000', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (121, 28, 'ILS', '3.86002785', '3.91881000', '3.97759215', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (122, 10, 'JPY', '117.63264000', '119.42400000', '121.21536000', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 3, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (123, 25, 'JOD', '0.69905450', '0.70970000', '0.72034550', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (124, 21, 'KWD', '0.29904600', '0.30360000', '0.30815400', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 3, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (125, 4, 'MYR', '4.28015005', '4.34533000', '4.41050995', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (126, 27, 'MXN', '17.03498400', '17.29440000', '17.55381600', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (127, 11, 'NZD', '1.46014430', '1.48238000', '1.50461570', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (128, 19, 'NOK', '8.74852375', '8.88175000', '9.01497625', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (129, 31, 'PHP', '46.42797500', '47.13500000', '47.84202500', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (130, 14, 'PLN', '3.91317845', '3.97277000', '4.03236155', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (131, 5, 'GBP', '0.66948776', '0.67968300', '0.68987825', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (132, 0, 'QAR', '3.58697600', '3.64160000', '3.69622400', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (133, 29, 'RUB', '72.04290000', '73.14000000', '74.23710000', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (134, 22, 'SAR', '3.69567075', '3.75195000', '3.80822925', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (135, 12, 'SGD', '1.40404855', '1.42543000', '1.44681145', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (136, 15, 'SEK', '8.37325845', '8.50077000', '8.62828155', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (137, 13, 'CHF', '0.98751175', '1.00255000', '1.01758825', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 3, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (138, 26, 'THB', '35.61267500', '36.15500000', '36.69732500', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (139, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-01-05 08:30:00', '2016-01-06 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (140, 32, 'ARS', '13.42111750', '13.62550000', '13.82988250', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (141, 7, 'AUD', '1.37421290', '1.39514000', '1.41606710', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (142, 33, 'BRL', '3.97013115', '4.03059000', '4.09104885', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (143, 20, 'BND', '1.40384170', '1.42522000', '1.44659830', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (144, 8, 'CAD', '1.37780815', '1.39879000', '1.41977185', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (145, 34, 'CLP', '702.89600000', '713.60000000', '724.30400000', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (146, 3, 'CNY', '6.42717425', '6.52505000', '6.62292575', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (147, 16, 'CZK', '24.77343950', '25.15070000', '25.52796050', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (148, 17, 'DKK', '6.83960360', '6.94376000', '7.04791640', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (149, 24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (150, 6, 'EUR', '0.91673261', '0.93069300', '0.94465340', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (151, 9, 'HKD', '7.63496155', '7.75123000', '7.86749845', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (152, 18, 'HUF', '287.36685500', '291.74300000', '296.11914500', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (153, 35, 'IDR', '13667.66300000', '13875.80000000', '14083.93700000', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (154, 28, 'ILS', '3.87088255', '3.92983000', '3.98877745', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (155, 10, 'JPY', '117.35388500', '119.14100000', '120.92811500', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (156, 25, 'JOD', '0.69915300', '0.70980000', '0.72044700', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (157, 21, 'KWD', '0.29936613', '0.30392500', '0.30848388', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (158, 4, 'MYR', '4.29290580', '4.35828000', '4.42365420', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (159, 27, 'MXN', '17.07684650', '17.33690000', '17.59695350', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (160, 11, 'NZD', '1.47151120', '1.49392000', '1.51632880', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (161, 19, 'NOK', '8.77620225', '8.90985000', '9.04349775', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (162, 31, 'PHP', '46.18625600', '46.88960000', '47.59294400', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (163, 14, 'PLN', '3.94773225', '4.00785000', '4.06796775', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (164, 5, 'GBP', '0.67131001', '0.68153300', '0.69175600', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (165, 0, 'QAR', '3.58692675', '3.64155000', '3.69617325', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (166, 29, 'RUB', '72.23990000', '73.34000000', '74.44010000', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (167, 22, 'SAR', '3.69586775', '3.75215000', '3.80843225', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (168, 12, 'SGD', '1.40383185', '1.42521000', '1.44658815', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (169, 15, 'SEK', '8.47231005', '8.60133000', '8.73034995', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (170, 13, 'CHF', '0.99372710', '1.00886000', '1.02399290', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 4, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (171, 26, 'THB', '35.58844400', '36.13040000', '36.67235600', '2016-01-06 08:30:00', '2016-01-07 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (172, 32, 'ARS', '13.63909800', '13.84680000', '14.05450200', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (173, 7, 'AUD', '1.39087910', '1.41206000', '1.43324090', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (174, 33, 'BRL', '3.96439845', '4.02477000', '4.08514155', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (175, 20, 'BND', '1.41446000', '1.43600000', '1.45754000', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (176, 8, 'CAD', '1.38600335', '1.40711000', '1.42821665', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (177, 34, 'CLP', '704.36660500', '715.09300000', '725.81939500', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (178, 3, 'CNY', '6.46414130', '6.56258000', '6.66101870', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (179, 16, 'CZK', '24.70173150', '25.07790000', '25.45406850', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (180, 17, 'DKK', '6.81822910', '6.92206000', '7.02589090', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (181, 24, 'EGP', '7.71255000', '7.83000000', '7.94745000', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (182, 6, 'EUR', '0.91382587', '0.92774200', '0.94165813', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (183, 9, 'HKD', '7.63629130', '7.75258000', '7.86886870', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (184, 18, 'HUF', '287.58651000', '291.96600000', '296.34549000', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (185, 35, 'IDR', '13701.45835000', '13910.11000000', '14118.76165000', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (186, 28, 'ILS', '3.88581515', '3.94499000', '4.00416485', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (187, 10, 'JPY', '116.86237000', '118.64200000', '120.42163000', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (188, 25, 'JOD', '0.69885750', '0.70950000', '0.72014250', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (189, 21, 'KWD', '0.29983302', '0.30439900', '0.30896499', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (190, 4, 'MYR', '4.35524645', '4.42157000', '4.48789355', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (191, 27, 'MXN', '17.26734550', '17.53030000', '17.79325450', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (192, 11, 'NZD', '1.48076035', '1.50331000', '1.52585965', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (193, 19, 'NOK', '8.81010595', '8.94427000', '9.07843405', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (194, 31, 'PHP', '46.39172700', '47.09820000', '47.80467300', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (195, 14, 'PLN', '3.97550925', '4.03605000', '4.09659075', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (196, 5, 'GBP', '0.67310763', '0.68335800', '0.69360837', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (197, 0, 'QAR', '3.58776400', '3.64240000', '3.69703600', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (198, 29, 'RUB', '73.46317150', '74.58190000', '75.70062850', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (199, 22, 'SAR', '3.69734525', '3.75365000', '3.80995475', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (200, 12, 'SGD', '1.41446000', '1.43600000', '1.45754000', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (201, 15, 'SEK', '8.45933760', '8.58816000', '8.71698240', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (202, 13, 'CHF', '0.99196395', '1.00707000', '1.02217605', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (203, 26, 'THB', '35.71708500', '36.26100000', '36.80491500', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (204, 23, 'AED', '3.61805275', '3.67315000', '3.72824725', '2016-01-07 08:30:00', '2016-01-08 08:30:00', 5, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (205, 32, 'ARS', '13.67574000', '13.88400000', '14.09226000', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (206, 7, 'AUD', '1.40288625', '1.42425000', '1.44561375', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (207, 33, 'BRL', '3.98452200', '4.04520000', '4.10587800', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (208, 20, 'BND', '1.41119965', '1.43269000', '1.45418035', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (209, 8, 'CAD', '1.38929325', '1.41045000', '1.43160675', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (210, 34, 'CLP', '710.48050000', '721.30000000', '732.11950000', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (211, 3, 'CNY', '6.49567115', '6.59459000', '6.69350885', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (212, 16, 'CZK', '24.39726800', '24.76880000', '25.14033200', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (213, 17, 'DKK', '6.73088915', '6.83339000', '6.93589085', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (214, 24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (215, 6, 'EUR', '0.90223439', '0.91597400', '0.92971361', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (216, 9, 'HKD', '7.63767030', '7.75398000', '7.87028970', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (217, 18, 'HUF', '283.72925000', '288.05000000', '292.37075000', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (218, 35, 'IDR', '13651.63705000', '13859.53000000', '14067.42295000', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (219, 28, 'ILS', '3.86917850', '3.92810000', '3.98702150', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (220, 10, 'JPY', '115.92859000', '117.69400000', '119.45941000', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (221, 25, 'JOD', '0.69836500', '0.70900000', '0.71963500', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (222, 21, 'KWD', '0.29944493', '0.30400500', '0.30856508', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (223, 4, 'MYR', '4.32045625', '4.38625000', '4.45204375', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (224, 27, 'MXN', '17.54019050', '17.80730000', '18.07440950', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (225, 11, 'NZD', '1.48726135', '1.50991000', '1.53255865', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (226, 19, 'NOK', '8.75983155', '8.89323000', '9.02662845', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (227, 31, 'PHP', '46.36887500', '47.07500000', '47.78112500', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (228, 14, 'PLN', '3.92182675', '3.98155000', '4.04127325', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (229, 5, 'GBP', '0.67409362', '0.68435900', '0.69462439', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (230, 0, 'QAR', '3.58692675', '3.64155000', '3.69617325', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (231, 29, 'RUB', '73.64864700', '74.77020000', '75.89175300', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (232, 22, 'SAR', '3.69729600', '3.75360000', '3.80990400', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (233, 12, 'SGD', '1.41120950', '1.43270000', '1.45419050', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (234, 15, 'SEK', '8.34126565', '8.46829000', '8.59531435', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (235, 13, 'CHF', '0.97944066', '0.99435600', '1.00927134', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (236, 26, 'THB', '35.70073400', '36.24440000', '36.78806600', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (237, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-01-08 08:30:00', '2016-01-09 08:30:00', 6, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (238, 32, 'ARS', '13.61279850', '13.82010000', '14.02740150', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 9, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (239, 7, 'AUD', '1.41665655', '1.43823000', '1.45980345', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (240, 33, 'BRL', '3.96575775', '4.02615000', '4.08654225', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 9, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (241, 20, 'BND', '1.42110875', '1.44275000', '1.46439125', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (242, 8, 'CAD', '1.39599125', '1.41725000', '1.43850875', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (243, 34, 'CLP', '717.37057500', '728.29500000', '739.21942500', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 9, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (244, 3, 'CNY', '6.49425275', '6.59315000', '6.69204725', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (245, 16, 'CZK', '24.40938350', '24.78110000', '25.15281650', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 9, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (246, 17, 'DKK', '6.72617100', '6.82860000', '6.93102900', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (247, 24, 'EGP', '7.71501250', '7.83250000', '7.94998750', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (248, 6, 'EUR', '0.90156065', '0.91529000', '0.92901935', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (249, 9, 'HKD', '7.64686035', '7.76331000', '7.87975965', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (250, 18, 'HUF', '285.41458500', '289.76100000', '294.10741500', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (251, 35, 'IDR', '13642.84100000', '13850.60000000', '14058.35900000', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (252, 28, 'ILS', '3.86661750', '3.92550000', '3.98438250', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (253, 10, 'JPY', '115.49125000', '117.25000000', '119.00875000', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (254, 25, 'JOD', '0.69920225', '0.70985000', '0.72049775', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (255, 21, 'KWD', '0.29934052', '0.30389900', '0.30845749', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 7, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (256, 4, 'MYR', '4.35655650', '4.42290000', '4.48924350', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (257, 27, 'MXN', '17.65986800', '17.92880000', '18.19773200', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (258, 11, 'NZD', '1.50549370', '1.52842000', '1.55134630', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 9, '2016-02-24 15:24:33');  "),
    $i+=1 => array("insert" => " (259, 19, 'NOK', '8.71602860', '8.84876000', '8.98149140', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (260, 31, 'PHP', '46.55661600', '47.26560000', '47.97458400', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (261, 14, 'PLN', '3.94018715', '4.00019000', '4.06019285', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (262, 5, 'GBP', '0.67839708', '0.68872800', '0.69905892', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (263, 0, 'QAR', '3.58712375', '3.64175000', '3.69637625', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (264, 29, 'RUB', '73.46366400', '74.58240000', '75.70113600', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (265, 22, 'SAR', '3.69670500', '3.75300000', '3.80929500', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (266, 12, 'SGD', '1.42110875', '1.44275000', '1.46439125', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (267, 15, 'SEK', '8.37250985', '8.50001000', '8.62751015', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (268, 13, 'CHF', '0.98010948', '0.99503500', '1.00996053', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 7, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (269, 26, 'THB', '35.81558500', '36.36100000', '36.90641500', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (270, 23, 'AED', '3.61746175', '3.67255000', '3.72763825', '2016-01-09 08:30:00', '2016-01-10 08:30:00', 7, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (271, 32, 'ARS', '13.66983000', '13.87800000', '14.08617000', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (272, 33, 'BRL', '3.96265500', '4.02300000', '4.08334500', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (273, 34, 'CLP', '715.99650000', '726.90000000', '737.80350000', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (274, 16, 'CZK', '24.42021850', '24.79210000', '25.16398150', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (275, 17, 'DKK', '6.73007160', '6.83256000', '6.93504840', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (276, 18, 'HUF', '285.41360000', '289.76000000', '294.10640000', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (277, 28, 'ILS', '3.86680465', '3.92569000', '3.98457535', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (278, 11, 'NZD', '1.50513910', '1.52806000', '1.55098090', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (279, 19, 'NOK', '8.71687570', '8.84962000', '8.98236430', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (280, 14, 'PLN', '3.93399150', '3.99390000', '4.05380850', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (281, 5, 'GBP', '0.67846800', '0.68880000', '0.69913200', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (282, 29, 'RUB', '73.72646200', '74.84920000', '75.97193800', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (283, 15, 'SEK', '8.37250000', '8.50000000', '8.62750000', '2016-01-10 08:30:00', '2016-01-11 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (284, 32, 'ARS', '13.61270000', '13.82000000', '14.02730000', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (285, 7, 'AUD', '1.41869550', '1.44030000', '1.46190450', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (286, 33, 'BRL', '3.96575775', '4.02615000', '4.08654225', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (287, 20, 'BND', '1.42140425', '1.44305000', '1.46469575', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (288, 8, 'CAD', '1.39749830', '1.41878000', '1.44006170', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (289, 34, 'CLP', '717.40702000', '728.33200000', '739.25698000', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (290, 3, 'CNY', '6.42913440', '6.52704000', '6.62494560', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (291, 16, 'CZK', '24.34269900', '24.71340000', '25.08410100', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (292, 17, 'DKK', '6.71984730', '6.82218000', '6.92451270', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (293, 24, 'EGP', '7.71502235', '7.83251000', '7.94999765', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (294, 6, 'EUR', '0.90075788', '0.91447500', '0.92819213', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (295, 9, 'HKD', '7.64667320', '7.76312000', '7.87956680', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (296, 18, 'HUF', '285.18705000', '289.53000000', '293.87295000', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (297, 35, 'IDR', '13640.68385000', '13848.41000000', '14056.13615000', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (298, 28, 'ILS', '3.87162130', '3.93058000', '3.98953870', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (299, 10, 'JPY', '115.28538500', '117.04100000', '118.79661500', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (300, 4, 'MYR', '4.35139510', '4.41766000', '4.48392490', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (301, 27, 'MXN', '17.69670700', '17.96620000', '18.23569300', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (302, 11, 'NZD', '1.51149235', '1.53451000', '1.55752765', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (303, 19, 'NOK', '8.74699700', '8.88020000', '9.01340300', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (304, 31, 'PHP', '46.63029400', '47.34040000', '48.05050600', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (305, 14, 'PLN', '3.93128275', '3.99115000', '4.05101725', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (306, 5, 'GBP', '0.67818235', '0.68851000', '0.69883765', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (307, 29, 'RUB', '73.58974400', '74.71040000', '75.83105600', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (308, 12, 'SGD', '1.42140425', '1.44305000', '1.46469575', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (309, 15, 'SEK', '8.36708250', '8.49450000', '8.62191750', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (310, 13, 'CHF', '0.97838967', '0.99328900', '1.00818834', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (311, 26, 'THB', '35.79194500', '36.33700000', '36.88205500', '2016-01-11 08:30:00', '2016-01-12 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (312, 32, 'ARS', '13.48336950', '13.68870000', '13.89403050', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (313, 7, 'AUD', '1.40912130', '1.43058000', '1.45203870', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (314, 33, 'BRL', '3.99310135', '4.05391000', '4.11471865', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (315, 20, 'BND', '1.41372125', '1.43525000', '1.45677875', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (316, 8, 'CAD', '1.40009870', '1.42142000', '1.44274130', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (317, 34, 'CLP', '720.91460500', '731.89300000', '742.87139500', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (318, 3, 'CNY', '6.47038620', '6.56892000', '6.66745380', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (319, 16, 'CZK', '24.50709550', '24.88030000', '25.25350450', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (320, 17, 'DKK', '6.76682195', '6.86987000', '6.97291805', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (321, 24, 'EGP', '7.71264850', '7.83010000', '7.94755150', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (322, 6, 'EUR', '0.90694959', '0.92076100', '0.93457242', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (323, 9, 'HKD', '7.64635800', '7.76280000', '7.87924200', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (324, 18, 'HUF', '288.06817500', '292.45500000', '296.84182500', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (325, 35, 'IDR', '13713.09120000', '13921.92000000', '14130.74880000', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (326, 28, 'ILS', '3.87715700', '3.93620000', '3.99524300', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (327, 10, 'JPY', '115.87835500', '117.64300000', '119.40764500', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (328, 25, 'JOD', '0.69851275', '0.70915000', '0.71978725', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (329, 21, 'KWD', '0.29914352', '0.30369900', '0.30825449', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (330, 4, 'MYR', '4.32916365', '4.39509000', '4.46101635', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (331, 27, 'MXN', '17.63169700', '17.90020000', '18.16870300', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (332, 11, 'NZD', '1.50347445', '1.52637000', '1.54926555', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (333, 19, 'NOK', '8.80193045', '8.93597000', '9.07000955', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (334, 31, 'PHP', '46.49820550', '47.20630000', '47.91439450', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (335, 14, 'PLN', '3.94779135', '4.00791000', '4.06802865', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (336, 5, 'GBP', '0.67720228', '0.68751500', '0.69782773', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (337, 0, 'QAR', '3.58663125', '3.64125000', '3.69586875', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (338, 29, 'RUB', '75.18465600', '76.32960000', '77.47454400', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (339, 22, 'SAR', '3.69797565', '3.75429000', '3.81060435', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (340, 12, 'SGD', '1.41372125', '1.43525000', '1.45677875', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (341, 15, 'SEK', '8.40796985', '8.53601000', '8.66405015', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (342, 13, 'CHF', '0.98617215', '1.00119000', '1.01620785', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (343, 26, 'THB', '35.72851100', '36.27260000', '36.81668900', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (344, 23, 'AED', '3.61765875', '3.67275000', '3.72784125', '2016-01-12 08:30:00', '2016-01-13 08:30:00', 8, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (345, 32, 'ARS', '13.27228400', '13.47440000', '13.67651600', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 13, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (346, 7, 'AUD', '1.40642240', '1.42784000', '1.44925760', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (347, 33, 'BRL', '3.96856500', '4.02900000', '4.08943500', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 13, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (348, 20, 'BND', '1.41475550', '1.43630000', '1.45784450', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (349, 8, 'CAD', '1.40342800', '1.42480000', '1.44617200', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (350, 34, 'CLP', '718.36050000', '729.30000000', '740.23950000', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 13, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (351, 3, 'CNY', '6.47332150', '6.57190000', '6.67047850', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (352, 16, 'CZK', '24.57289350', '24.94710000', '25.32130650', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 13, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (353, 17, 'DKK', '6.78396095', '6.88727000', '6.99057905', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (354, 24, 'EGP', '7.71255000', '7.83000000', '7.94745000', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (355, 6, 'EUR', '0.90922198', '0.92306800', '0.93691402', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (356, 9, 'HKD', '7.64327495', '7.75967000', '7.87606505', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (357, 18, 'HUF', '287.62689500', '292.00700000', '296.38710500', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (358, 35, 'IDR', '13724.59600000', '13933.60000000', '14142.60400000', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (359, 28, 'ILS', '3.88134325', '3.94045000', '3.99955675', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (360, 10, 'JPY', '116.10096500', '117.86900000', '119.63703500', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (361, 25, 'JOD', '0.69836500', '0.70900000', '0.71963500', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (362, 21, 'KWD', '0.29924300', '0.30380000', '0.30835700', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (363, 4, 'MYR', '4.32469175', '4.39055000', '4.45640825', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (364, 27, 'MXN', '17.63041650', '17.89890000', '18.16738350', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (365, 11, 'NZD', '1.50312970', '1.52602000', '1.54891030', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 13, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (366, 19, 'NOK', '8.75027705', '8.88353000', '9.01678295', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (367, 31, 'PHP', '46.60035000', '47.31000000', '48.01965000', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (368, 14, 'PLN', '3.96227085', '4.02261000', '4.08294915', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 13, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (369, 5, 'GBP', '0.68174510', '0.69212700', '0.70250891', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (370, 0, 'QAR', '3.58658200', '3.64120000', '3.69581800', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (371, 29, 'RUB', '75.70306150', '76.85590000', '78.00873850', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (372, 22, 'SAR', '3.69498125', '3.75125000', '3.80751875', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (373, 12, 'SGD', '1.41475550', '1.43630000', '1.45784450', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (374, 15, 'SEK', '8.41287515', '8.54099000', '8.66910485', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (375, 13, 'CHF', '0.98864450', '1.00370000', '1.01875550', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 10, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (376, 26, 'THB', '35.76298600', '36.30760000', '36.85221400', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (377, 23, 'AED', '3.61741250', '3.67250000', '3.72758750', '2016-01-13 08:30:00', '2016-01-14 08:30:00', 9, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (378, 32, 'ARS', '13.32616350', '13.52910000', '13.73203650', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 14, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (379, 7, 'AUD', '1.41980855', '1.44143000', '1.46305145', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (380, 33, 'BRL', '3.95782850', '4.01810000', '4.07837150', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 14, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (381, 20, 'BND', '1.41694220', '1.43852000', '1.46009780', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (382, 8, 'CAD', '1.41374095', '1.43527000', '1.45679905', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (383, 34, 'CLP', '716.44172000', '727.35200000', '738.26228000', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 14, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (384, 3, 'CNY', '6.47726150', '6.57590000', '6.67453850', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (385, 16, 'CZK', '24.44770000', '24.82000000', '25.19230000', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 14, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (386, 17, 'DKK', '6.75144610', '6.85426000', '6.95707390', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 13, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (387, 24, 'EGP', '7.71264850', '7.83010000', '7.94755150', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (388, 6, 'EUR', '0.90458657', '0.91836200', '0.93213743', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (389, 9, 'HKD', '7.64399400', '7.76040000', '7.87680600', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (390, 18, 'HUF', '285.65985000', '290.01000000', '294.36015000', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 13, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (391, 35, 'IDR', '13585.78980000', '13792.68000000', '13999.57020000', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (392, 28, 'ILS', '3.87671375', '3.93575000', '3.99478625', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 13, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (393, 10, 'JPY', '115.61043500', '117.37100000', '119.13156500', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 11, '2016-02-24 15:24:34');  "),
    $i+=1 => array("insert" => " (394, 25, 'JOD', '0.69885750', '0.70950000', '0.72014250', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 11, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (395, 21, 'KWD', '0.29926172', '0.30381900', '0.30837629', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 10, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (396, 4, 'MYR', '4.33311350', '4.39910000', '4.46508650', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (397, 27, 'MXN', '17.69207750', '17.96150000', '18.23092250', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (398, 11, 'NZD', '1.51815095', '1.54127000', '1.56438905', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (399, 19, 'NOK', '8.68687260', '8.81916000', '8.95144740', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (400, 31, 'PHP', '46.81902000', '47.53200000', '48.24498000', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (401, 14, 'PLN', '3.93936960', '3.99936000', '4.05935040', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (402, 5, 'GBP', '0.68341566', '0.69382300', '0.70423035', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (403, 0, 'QAR', '3.58707450', '3.64170000', '3.69632550', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (404, 29, 'RUB', '75.45080300', '76.59980000', '77.74879700', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (405, 22, 'SAR', '3.69808400', '3.75440000', '3.81071600', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 11, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (406, 12, 'SGD', '1.41694220', '1.43852000', '1.46009780', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (407, 15, 'SEK', '8.39050580', '8.51828000', '8.64605420', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (408, 13, 'CHF', '0.99052585', '1.00561000', '1.02069415', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 11, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (409, 26, 'THB', '35.75618950', '36.30070000', '36.84521050', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (410, 23, 'AED', '3.61785575', '3.67295000', '3.72804425', '2016-01-14 08:30:00', '2016-01-15 08:30:00', 10, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (411, 32, 'ARS', '13.33502850', '13.53810000', '13.74117150', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (412, 7, 'AUD', '1.40854015', '1.42999000', '1.45143985', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (413, 33, 'BRL', '3.95682380', '4.01708000', '4.07733620', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (414, 20, 'BND', '1.41579960', '1.43736000', '1.45892040', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (415, 8, 'CAD', '1.41343560', '1.43496000', '1.45648440', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (416, 34, 'CLP', '713.23062000', '724.09200000', '734.95338000', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (417, 3, 'CNY', '6.49042110', '6.58926000', '6.68809890', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (418, 16, 'CZK', '24.50886850', '24.88210000', '25.25533150', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (419, 17, 'DKK', '6.76896925', '6.87205000', '6.97513075', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (420, 24, 'EGP', '7.71499280', '7.83248000', '7.94996720', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (421, 6, 'EUR', '0.90701657', '0.92082900', '0.93464144', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (422, 9, 'HKD', '7.66343790', '7.78014000', '7.89684210', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (423, 18, 'HUF', '285.50914500', '289.85700000', '294.20485500', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (424, 35, 'IDR', '13742.07975000', '13951.35000000', '14160.62025000', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (425, 28, 'ILS', '3.88380575', '3.94295000', '4.00209425', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (426, 10, 'JPY', '116.42700000', '118.20000000', '119.97300000', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (427, 25, 'JOD', '0.69890084', '0.70954400', '0.72018716', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (428, 21, 'KWD', '0.29985272', '0.30441900', '0.30898529', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 11, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (429, 4, 'MYR', '4.30970990', '4.37534000', '4.44097010', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (430, 27, 'MXN', '17.59200150', '17.85990000', '18.12779850', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (431, 11, 'NZD', '1.51925415', '1.54239000', '1.56552585', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (432, 19, 'NOK', '8.63396825', '8.76545000', '8.89693175', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (433, 31, 'PHP', '47.11649000', '47.83400000', '48.55151000', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (434, 14, 'PLN', '3.98493570', '4.04562000', '4.10630430', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (435, 5, 'GBP', '0.68302757', '0.69342900', '0.70383044', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (436, 29, 'RUB', '75.03503450', '76.17770000', '77.32036550', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (437, 22, 'SAR', '3.69368105', '3.74993000', '3.80617895', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (438, 12, 'SGD', '1.41592765', '1.43749000', '1.45905235', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (439, 15, 'SEK', '8.42177955', '8.55003000', '8.67828045', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (440, 13, 'CHF', '0.99041750', '1.00550000', '1.02058250', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (441, 26, 'THB', '35.77194950', '36.31670000', '36.86145050', '2016-01-15 08:30:00', '2016-01-16 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (442, 32, 'ARS', '13.04238500', '13.24100000', '13.43961500', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (443, 7, 'AUD', '1.43654370', '1.45842000', '1.48029630', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (444, 33, 'BRL', '3.98757550', '4.04830000', '4.10902450', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (445, 20, 'BND', '1.41795675', '1.43955000', '1.46114325', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (446, 8, 'CAD', '1.43233775', '1.45415000', '1.47596225', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (447, 34, 'CLP', '721.50560500', '732.49300000', '743.48039500', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (448, 3, 'CNY', '6.48248200', '6.58120000', '6.67991800', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (449, 16, 'CZK', '24.40475400', '24.77640000', '25.14804600', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (450, 17, 'DKK', '6.73744925', '6.84005000', '6.94265075', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (451, 24, 'EGP', '7.70998900', '7.82740000', '7.94481100', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (452, 6, 'EUR', '0.90243829', '0.91618100', '0.92992372', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (453, 9, 'HKD', '7.67679450', '7.79370000', '7.91060550', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (454, 18, 'HUF', '284.44928500', '288.78100000', '293.11271500', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (455, 35, 'IDR', '13673.52375000', '13881.75000000', '14089.97625000', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (456, 28, 'ILS', '3.90700250', '3.96650000', '4.02599750', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (457, 10, 'JPY', '115.29129500', '117.04700000', '118.80270500', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (458, 25, 'JOD', '0.69920225', '0.70985000', '0.72049775', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (459, 21, 'KWD', '0.29945872', '0.30401900', '0.30857929', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 12, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (460, 4, 'MYR', '4.35867425', '4.42505000', '4.49142575', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (461, 27, 'MXN', '17.96137650', '18.23490000', '18.50842350', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (462, 11, 'NZD', '1.52396245', '1.54717000', '1.57037755', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (463, 19, 'NOK', '8.71730910', '8.85006000', '8.98281090', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (464, 31, 'PHP', '47.11777050', '47.83530000', '48.55282950', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (465, 14, 'PLN', '4.05027075', '4.11195000', '4.17362925', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (466, 5, 'GBP', '0.69096174', '0.70148400', '0.71200626', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (467, 0, 'QAR', '3.58672975', '3.64135000', '3.69597025', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (468, 29, 'RUB', '76.56887650', '77.73490000', '78.90092350', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (469, 22, 'SAR', '3.69256800', '3.74880000', '3.80503200', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (470, 12, 'SGD', '1.41795675', '1.43955000', '1.46114325', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (471, 15, 'SEK', '8.46090375', '8.58975000', '8.71859625', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (472, 13, 'CHF', '0.98656615', '1.00159000', '1.01661385', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (473, 26, 'THB', '35.77096450', '36.31570000', '36.86043550', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (474, 23, 'AED', '3.61746175', '3.67255000', '3.72763825', '2016-01-16 08:30:00', '2016-01-17 08:30:00', 11, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (475, 32, 'ARS', '13.21870000', '13.42000000', '13.62130000', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (476, 7, 'AUD', '1.43512530', '1.45698000', '1.47883470', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (477, 33, 'BRL', '3.98570400', '4.04640000', '4.10709600', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (478, 8, 'CAD', '1.43228850', '1.45410000', '1.47591150', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (479, 34, 'CLP', '719.34550000', '730.30000000', '741.25450000', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (480, 16, 'CZK', '24.38534950', '24.75670000', '25.12805050', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (481, 17, 'DKK', '6.73673020', '6.83932000', '6.94190980', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (482, 6, 'EUR', '0.90243730', '0.91618000', '0.92992270', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (483, 9, 'HKD', '7.67678465', '7.79369000', '7.91059535', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (484, 18, 'HUF', '284.51725000', '288.85000000', '293.18275000', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (485, 28, 'ILS', '3.91744350', '3.97710000', '4.03675650', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (486, 10, 'JPY', '115.19870500', '116.95300000', '118.70729500', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (487, 27, 'MXN', '17.95999750', '18.23350000', '18.50700250', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (488, 11, 'NZD', '1.52465195', '1.54787000', '1.57108805', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (489, 19, 'NOK', '8.71672795', '8.84947000', '8.98221205', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (490, 14, 'PLN', '4.04958125', '4.11125000', '4.17291875', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (491, 5, 'GBP', '0.69110654', '0.70163100', '0.71215547', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (492, 29, 'RUB', '76.58315900', '77.74940000', '78.91564100', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (493, 15, 'SEK', '8.46188875', '8.59075000', '8.71961125', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (494, 13, 'CHF', '0.98657600', '1.00160000', '1.01662400', '2016-01-17 08:30:00', '2016-01-18 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (495, 32, 'ARS', '13.35364500', '13.55700000', '13.76035500', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 18, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (496, 7, 'AUD', '1.43629745', '1.45817000', '1.48004255', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (497, 33, 'BRL', '3.98757550', '4.04830000', '4.10902450', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 18, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (498, 20, 'BND', '1.41821285', '1.43981000', '1.46140715', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (499, 8, 'CAD', '1.43585420', '1.45772000', '1.47958580', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (500, 34, 'CLP', '721.50560500', '732.49300000', '743.48039500', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 18, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (501, 3, 'CNY', '6.45349345', '6.55177000', '6.65004655', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (502, 16, 'CZK', '24.36732400', '24.73840000', '25.10947600', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 18, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (503, 17, 'DKK', '6.73040650', '6.83290000', '6.93539350', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (504, 24, 'EGP', '7.71255000', '7.83000000', '7.94745000', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (505, 6, 'EUR', '0.90175765', '0.91549000', '0.92922235', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (506, 9, 'HKD', '7.67675510', '7.79366000', '7.91056490', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (507, 18, 'HUF', '284.43943500', '288.77100000', '293.10256500', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (508, 35, 'IDR', '13661.29990000', '13869.34000000', '14077.38010000', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (509, 28, 'ILS', '3.90103340', '3.96044000', '4.01984660', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (510, 10, 'JPY', '115.20461500', '116.95900000', '118.71338500', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (511, 25, 'JOD', '0.69885750', '0.70950000', '0.72014250', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (512, 21, 'KWD', '0.29845500', '0.30300000', '0.30754500', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 13, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (513, 4, 'MYR', '4.34385000', '4.41000000', '4.47615000', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (514, 27, 'MXN', '18.02451500', '18.29900000', '18.57348500', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (515, 11, 'NZD', '1.53022705', '1.55353000', '1.57683295', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 18, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (516, 19, 'NOK', '8.72513000', '8.85800000', '8.99087000', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (517, 31, 'PHP', '47.15195000', '47.87000000', '48.58805000', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (518, 14, 'PLN', '4.04811360', '4.10976000', '4.17140640', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 18, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (519, 5, 'GBP', '0.69052046', '0.70103600', '0.71155154', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (520, 0, 'QAR', '3.58638500', '3.64100000', '3.69561500', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 14, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (521, 29, 'RUB', '76.60837500', '77.77500000', '78.94162500', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (522, 12, 'SGD', '1.41821285', '1.43981000', '1.46140715', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (523, 15, 'SEK', '8.45082720', '8.57952000', '8.70821280', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 17, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (524, 13, 'CHF', '0.98643810', '1.00146000', '1.01648190', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 15, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (525, 26, 'THB', '35.77451050', '36.31930000', '36.86408950', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 16, '2016-02-24 15:24:35');  "),
    $i+=1 => array("insert" => " (526, 23, 'AED', '3.61731400', '3.67240000', '3.72748600', '2016-01-18 08:30:00', '2016-01-19 08:30:00', 12, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (527, 32, 'ARS', '13.31917000', '13.52200000', '13.72483000', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (528, 7, 'AUD', '1.43107695', '1.45287000', '1.47466305', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (529, 33, 'BRL', '3.99547520', '4.05632000', '4.11716480', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (530, 20, 'BND', '1.41599660', '1.43756000', '1.45912340', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (531, 8, 'CAD', '1.43190435', '1.45371000', '1.47551565', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (532, 34, 'CLP', '719.50310000', '730.46000000', '741.41690000', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (533, 3, 'CNY', '6.48099465', '6.57969000', '6.67838535', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (534, 16, 'CZK', '24.46336150', '24.83590000', '25.20843850', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (535, 17, 'DKK', '6.75389875', '6.85675000', '6.95960125', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (536, 24, 'EGP', '7.69777500', '7.81500000', '7.93222500', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 15, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (537, 6, 'EUR', '0.90494216', '0.91872300', '0.93250385', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (538, 9, 'HKD', '7.68409335', '7.80111000', '7.91812665', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (539, 18, 'HUF', '285.44709000', '289.79400000', '294.14091000', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (540, 35, 'IDR', '13687.60925000', '13896.05000000', '14104.49075000', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (541, 28, 'ILS', '3.89567500', '3.95500000', '4.01432500', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (542, 10, 'JPY', '115.77000500', '117.53300000', '119.29599500', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (543, 25, 'JOD', '0.69900525', '0.70965000', '0.72029475', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 15, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (544, 21, 'KWD', '0.29971088', '0.30427500', '0.30883913', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 14, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (545, 4, 'MYR', '4.32450460', '4.39036000', '4.45621540', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (546, 27, 'MXN', '17.91803650', '18.19090000', '18.46376350', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (547, 11, 'NZD', '1.52659240', '1.54984000', '1.57308760', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (548, 19, 'NOK', '8.76692355', '8.90043000', '9.03393645', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (549, 31, 'PHP', '47.07315000', '47.79000000', '48.50685000', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (550, 14, 'PLN', '4.03826360', '4.09976000', '4.16125640', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (551, 5, 'GBP', '0.69092727', '0.70144900', '0.71197074', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (552, 0, 'QAR', '3.58687750', '3.64150000', '3.69612250', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 15, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (553, 29, 'RUB', '78.22623750', '79.41750000', '80.60876250', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (554, 22, 'SAR', '3.69586775', '3.75215000', '3.80843225', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 14, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (555, 12, 'SGD', '1.41599660', '1.43756000', '1.45912340', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (556, 15, 'SEK', '8.46220395', '8.59107000', '8.71993605', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (557, 13, 'CHF', '0.99090015', '1.00599000', '1.02107985', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (558, 26, 'THB', '35.71275100', '36.25660000', '36.80044900', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (559, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-01-19 08:30:00', '2016-01-20 08:30:00', 13, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (560, 32, 'ARS', '13.26381300', '13.46580000', '13.66778700', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (561, 7, 'AUD', '1.42831895', '1.45007000', '1.47182105', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (562, 33, 'BRL', '4.00097150', '4.06190000', '4.12282850', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (563, 20, 'BND', '1.41616405', '1.43773000', '1.45929595', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (564, 8, 'CAD', '1.43530260', '1.45716000', '1.47901740', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (565, 34, 'CLP', '716.03787000', '726.94200000', '737.84613000', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (566, 3, 'CNY', '6.47945805', '6.57813000', '6.67680195', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (567, 16, 'CZK', '24.38062150', '24.75190000', '25.12317850', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (568, 17, 'DKK', '6.73149985', '6.83401000', '6.93652015', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (569, 24, 'EGP', '7.71314100', '7.83060000', '7.94805900', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (570, 6, 'EUR', '0.90201178', '0.91574800', '0.92948422', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (571, 9, 'HKD', '7.70521175', '7.82255000', '7.93988825', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (572, 18, 'HUF', '284.34684500', '288.67700000', '293.00715500', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (573, 35, 'IDR', '13597.88560000', '13804.96000000', '14012.03440000', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (574, 28, 'ILS', '3.90936650', '3.96890000', '4.02843350', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (575, 10, 'JPY', '115.69022000', '117.45200000', '119.21378000', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (576, 25, 'JOD', '0.69885750', '0.70950000', '0.72014250', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (577, 21, 'KWD', '0.30043387', '0.30500900', '0.30958414', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 15, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (578, 4, 'MYR', '4.31480235', '4.38051000', '4.44621765', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (579, 27, 'MXN', '18.00924750', '18.28350000', '18.55775250', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (580, 11, 'NZD', '1.54353440', '1.56704000', '1.59054560', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (581, 19, 'NOK', '8.68311975', '8.81535000', '8.94758025', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (582, 31, 'PHP', '46.95987500', '47.67500000', '48.39012500', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (583, 14, 'PLN', '4.01324460', '4.07436000', '4.13547540', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (584, 5, 'GBP', '0.69519527', '0.70578200', '0.71636873', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (585, 29, 'RUB', '77.49310200', '78.67320000', '79.85329800', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (586, 22, 'SAR', '3.69429175', '3.75055000', '3.80680825', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 15, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (587, 12, 'SGD', '1.41624285', '1.43781000', '1.45937715', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (588, 15, 'SEK', '8.40019820', '8.52812000', '8.65604180', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (589, 13, 'CHF', '0.98772845', '1.00277000', '1.01781155', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (590, 26, 'THB', '35.75323450', '36.29770000', '36.84216550', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (591, 23, 'AED', '3.61785575', '3.67295000', '3.72804425', '2016-01-20 08:30:00', '2016-01-21 08:30:00', 14, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (592, 32, 'ARS', '13.21810900', '13.41940000', '13.62069100', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (593, 7, 'AUD', '1.41931605', '1.44093000', '1.46254395', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (594, 33, 'BRL', '4.04287340', '4.10444000', '4.16600660', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (595, 20, 'BND', '1.41448955', '1.43603000', '1.45757045', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (596, 8, 'CAD', '1.42414255', '1.44583000', '1.46751745', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (597, 34, 'CLP', '716.39050000', '727.30000000', '738.20950000', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (598, 3, 'CNY', '6.48059080', '6.57928000', '6.67796920', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (599, 16, 'CZK', '24.49468450', '24.86770000', '25.24071550', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (600, 17, 'DKK', '6.76210380', '6.86508000', '6.96805620', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (601, 24, 'EGP', '7.71255000', '7.83000000', '7.94745000', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (602, 6, 'EUR', '0.90595474', '0.91975100', '0.93354727', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (603, 9, 'HKD', '7.70122250', '7.81850000', '7.93577750', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (604, 18, 'HUF', '285.17227500', '289.51500000', '293.85772500', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (605, 35, 'IDR', '13791.47750000', '14001.50000000', '14211.52250000', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (606, 28, 'ILS', '3.91143500', '3.97100000', '4.03056500', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (607, 10, 'JPY', '115.43904500', '117.19700000', '118.95495500', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (608, 25, 'JOD', '0.69836500', '0.70900000', '0.71963500', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (609, 21, 'KWD', '0.29988325', '0.30445000', '0.30901675', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (610, 4, 'MYR', '4.30545470', '4.37102000', '4.43658530', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (611, 27, 'MXN', '18.17994800', '18.45680000', '18.73365200', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (612, 11, 'NZD', '1.52444510', '1.54766000', '1.57087490', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (613, 19, 'NOK', '8.74243645', '8.87557000', '9.00870355', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (614, 31, 'PHP', '47.20120000', '47.92000000', '48.63880000', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (615, 14, 'PLN', '4.06716350', '4.12910000', '4.19103650', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (616, 5, 'GBP', '0.69385764', '0.70442400', '0.71499036', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (617, 0, 'QAR', '3.58658200', '3.64120000', '3.69581800', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (618, 29, 'RUB', '80.27818950', '81.50070000', '82.72321050', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (619, 12, 'SGD', '1.41448955', '1.43603000', '1.45757045', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (620, 15, 'SEK', '8.46923685', '8.59821000', '8.72718315', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (621, 13, 'CHF', '0.99074255', '1.00583000', '1.02091745', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (622, 26, 'THB', '35.72299500', '36.26700000', '36.81100500', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (623, 23, 'AED', '3.61711700', '3.67220000', '3.72728300', '2016-01-21 08:30:00', '2016-01-22 08:30:00', 15, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (624, 32, 'ARS', '13.30922150', '13.51190000', '13.71457850', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 22, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (625, 7, 'AUD', '1.40521085', '1.42661000', '1.44800915', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (626, 33, 'BRL', '4.09495035', '4.15731000', '4.21966965', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 22, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (627, 20, 'BND', '1.41131785', '1.43281000', '1.45430215', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (628, 8, 'CAD', '1.40582155', '1.42723000', '1.44863845', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (629, 34, 'CLP', '714.37125000', '725.25000000', '736.12875000', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 22, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (630, 3, 'CNY', '6.48507255', '6.58383000', '6.68258745', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (631, 16, 'CZK', '24.55329200', '24.92720000', '25.30110800', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 22, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (632, 17, 'DKK', '6.76892985', '6.87201000', '6.97509015', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (633, 24, 'EGP', '7.69293865', '7.81009000', '7.92724135', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (634, 6, 'EUR', '0.90772183', '0.92154500', '0.93536818', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (635, 9, 'HKD', '7.69532235', '7.81251000', '7.92969765', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (636, 18, 'HUF', '285.11810000', '289.46000000', '293.80190000', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (637, 35, 'IDR', '13736.71150000', '13945.90000000', '14155.08850000', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (638, 28, 'ILS', '3.91867475', '3.97835000', '4.03802525', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (639, 10, 'JPY', '115.90396500', '117.66900000', '119.43403500', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (640, 25, 'JOD', '0.69713375', '0.70775000', '0.71836625', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 18, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (641, 21, 'KWD', '0.30008419', '0.30465400', '0.30922381', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (642, 4, 'MYR', '4.27738220', '4.34252000', '4.40765780', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (643, 27, 'MXN', '18.42275050', '18.70330000', '18.98384950', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (644, 11, 'NZD', '1.50774935', '1.53071000', '1.55367065', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 22, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (645, 19, 'NOK', '8.66566555', '8.79763000', '8.92959445', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (646, 31, 'PHP', '47.10772350', '47.82510000', '48.54247650', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (647, 14, 'PLN', '4.07708245', '4.13917000', '4.20125755', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 22, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (648, 5, 'GBP', '0.69292288', '0.70347500', '0.71402713', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (649, 0, 'QAR', '3.58692675', '3.64155000', '3.69617325', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 17, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (650, 29, 'RUB', '81.02974450', '82.26370000', '83.49765550', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (651, 22, 'SAR', '3.69650800', '3.75280000', '3.80909200', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (652, 12, 'SGD', '1.41131785', '1.43281000', '1.45430215', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (653, 15, 'SEK', '8.44296690', '8.57154000', '8.70011310', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 21, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (654, 13, 'CHF', '0.99342175', '1.00855000', '1.02367825', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 19, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (655, 26, 'THB', '35.67138100', '36.21460000', '36.75781900', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 20, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (656, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-01-22 08:30:00', '2016-01-23 08:30:00', 16, '2016-02-24 15:24:36');  "),
    $i+=1 => array("insert" => " (657, 32, 'ARS', '13.49450000', '13.70000000', '13.90550000', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (658, 7, 'AUD', '1.40635345', '1.42777000', '1.44918655', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (659, 33, 'BRL', '4.03347650', '4.09490000', '4.15632350', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (660, 20, 'BND', '1.40814615', '1.42959000', '1.45103385', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (661, 8, 'CAD', '1.39101700', '1.41220000', '1.43338300', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (662, 34, 'CLP', '704.47200000', '715.20000000', '725.92800000', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (663, 3, 'CNY', '6.48080750', '6.57950000', '6.67819250', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (664, 16, 'CZK', '24.63711550', '25.01230000', '25.38748450', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (665, 17, 'DKK', '6.80738425', '6.91105000', '7.01471575', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (666, 24, 'EGP', '7.71255000', '7.83000000', '7.94745000', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 19, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (667, 6, 'EUR', '0.91220456', '0.92609600', '0.93998744', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (668, 9, 'HKD', '7.67974950', '7.79670000', '7.91365050', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (669, 18, 'HUF', '283.97353000', '288.29800000', '292.62247000', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (670, 35, 'IDR', '13664.90500000', '13873.00000000', '14081.09500000', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (671, 28, 'ILS', '3.92276250', '3.98250000', '4.04223750', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (672, 10, 'JPY', '116.95594500', '118.73700000', '120.51805500', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (673, 25, 'JOD', '0.69688750', '0.70750000', '0.71811250', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 19, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (674, 21, 'KWD', '0.29944000', '0.30400000', '0.30856000', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 18, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (675, 4, 'MYR', '4.20742750', '4.27150000', '4.33557250', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (676, 27, 'MXN', '18.17246200', '18.44920000', '18.72593800', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (677, 11, 'NZD', '1.51801305', '1.54113000', '1.56424695', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (678, 19, 'NOK', '8.60167010', '8.73266000', '8.86364990', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (679, 31, 'PHP', '47.02390000', '47.74000000', '48.45610000', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (680, 14, 'PLN', '4.07340840', '4.13544000', '4.19747160', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (681, 5, 'GBP', '0.69053327', '0.70104900', '0.71156474', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (682, 0, 'QAR', '3.58677900', '3.64140000', '3.69602100', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 18, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (683, 29, 'RUB', '77.05172350', '78.22510000', '79.39847650', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (684, 22, 'SAR', '3.69547375', '3.75175000', '3.80802625', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 17, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (685, 12, 'SGD', '1.40815600', '1.42960000', '1.45104400', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (686, 15, 'SEK', '8.46746385', '8.59641000', '8.72535615', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (687, 13, 'CHF', '1.00061225', '1.01585000', '1.03108775', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (688, 26, 'THB', '35.46000000', '36.00000000', '36.54000000', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (689, 23, 'AED', '3.61780650', '3.67290000', '3.72799350', '2016-01-23 08:30:00', '2016-01-24 08:30:00', 17, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (690, 7, 'AUD', '1.40673760', '1.42816000', '1.44958240', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (691, 33, 'BRL', '4.03160500', '4.09300000', '4.15439500', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (692, 8, 'CAD', '1.39118445', '1.41237000', '1.43355555', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (693, 3, 'CNY', '6.48070900', '6.57940000', '6.67809100', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (694, 16, 'CZK', '24.63514550', '25.01030000', '25.38545450', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (695, 17, 'DKK', '6.80299115', '6.90659000', '7.01018885', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (696, 24, 'EGP', '7.71284550', '7.83030000', '7.94775450', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (697, 6, 'EUR', '0.91220850', '0.92610000', '0.93999150', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (698, 18, 'HUF', '283.96072500', '288.28500000', '292.60927500', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (699, 28, 'ILS', '3.92244730', '3.98218000', '4.04191270', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (700, 10, 'JPY', '116.95890000', '118.74000000', '120.52110000', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (701, 25, 'JOD', '0.69836402', '0.70899900', '0.71963399', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (702, 21, 'KWD', '0.30012852', '0.30469900', '0.30926949', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 19, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (703, 27, 'MXN', '18.17285600', '18.44960000', '18.72634400', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (704, 11, 'NZD', '1.51748115', '1.54059000', '1.56369885', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (705, 19, 'NOK', '8.61136250', '8.74250000', '8.87363750', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (706, 31, 'PHP', '47.01808850', '47.73410000', '48.45011150', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (707, 14, 'PLN', '4.07317200', '4.13520000', '4.19722800', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (708, 5, 'GBP', '0.69030770', '0.70082000', '0.71133230', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (709, 0, 'QAR', '3.58672975', '3.64135000', '3.69597025', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 19, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (710, 29, 'RUB', '76.97568150', '78.14790000', '79.32011850', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (711, 12, 'SGD', '1.40814615', '1.42959000', '1.45103385', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (712, 15, 'SEK', '8.46804500', '8.59700000', '8.72595500', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (713, 13, 'CHF', '1.00066150', '1.01590000', '1.03113850', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (714, 26, 'THB', '35.48068500', '36.02100000', '36.56131500', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (715, 23, 'AED', '3.61746175', '3.67255000', '3.72763825', '2016-01-24 08:30:00', '2016-01-25 08:30:00', 18, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (716, 32, 'ARS', '13.46495000', '13.67000000', '13.87505000', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (717, 7, 'AUD', '1.40679670', '1.42822000', '1.44964330', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (718, 33, 'BRL', '4.03347650', '4.09490000', '4.15632350', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (719, 20, 'BND', '1.40672775', '1.42815000', '1.44957225', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (720, 8, 'CAD', '1.39219900', '1.41340000', '1.43460100', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (721, 34, 'CLP', '704.75962000', '715.49200000', '726.22438000', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (722, 3, 'CNY', '6.48304345', '6.58177000', '6.68049655', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (723, 16, 'CZK', '24.66331650', '25.03890000', '25.41448350', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (724, 17, 'DKK', '6.81078250', '6.91450000', '7.01821750', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (725, 24, 'EGP', '7.71255000', '7.83000000', '7.94745000', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (726, 6, 'EUR', '0.91271479', '0.92661400', '0.94051321', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (727, 9, 'HKD', '7.67793710', '7.79486000', '7.91178290', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (728, 18, 'HUF', '284.33699500', '288.66700000', '292.99700500', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (729, 35, 'IDR', '13685.09750000', '13893.50000000', '14101.90250000', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (730, 28, 'ILS', '3.92227000', '3.98200000', '4.04173000', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (731, 10, 'JPY', '116.78849500', '118.56700000', '120.34550500', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (732, 25, 'JOD', '0.69688750', '0.70750000', '0.71811250', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (733, 21, 'KWD', '0.29944000', '0.30400000', '0.30856000', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (734, 4, 'MYR', '4.21678500', '4.28100000', '4.34521500', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (735, 27, 'MXN', '18.16418800', '18.44080000', '18.71741200', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (736, 11, 'NZD', '1.51953980', '1.54268000', '1.56582020', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (737, 19, 'NOK', '8.61330295', '8.74447000', '8.87563705', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (738, 31, 'PHP', '47.02390000', '47.74000000', '48.45610000', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (739, 14, 'PLN', '4.06941915', '4.13139000', '4.19336085', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (740, 5, 'GBP', '0.69043477', '0.70094900', '0.71146324', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (741, 0, 'QAR', '3.58677900', '3.64140000', '3.69602100', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (742, 29, 'RUB', '77.04364650', '78.21690000', '79.39015350', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (743, 12, 'SGD', '1.40672775', '1.42815000', '1.44957225', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (744, 15, 'SEK', '8.45580145', '8.58457000', '8.71333855', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (745, 26, 'THB', '35.47576000', '36.01600000', '36.55624000', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (746, 23, 'AED', '3.61780650', '3.67290000', '3.72799350', '2016-01-25 08:30:00', '2016-01-26 08:30:00', 19, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (747, 32, 'ARS', '13.36920800', '13.57280000', '13.77639200', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (748, 7, 'AUD', '1.41679445', '1.43837000', '1.45994555', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (749, 33, 'BRL', '4.02929025', '4.09065000', '4.15200975', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 26, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (750, 20, 'BND', '1.40934785', '1.43081000', '1.45227215', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (751, 8, 'CAD', '1.40731875', '1.42875000', '1.45018125', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (752, 34, 'CLP', '711.66250000', '722.50000000', '733.33750000', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (753, 3, 'CNY', '6.48153640', '6.58024000', '6.67894360', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (754, 16, 'CZK', '24.53004600', '24.90360000', '25.27715400', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 26, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (755, 17, 'DKK', '6.77461330', '6.87778000', '6.98094670', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (756, 24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (757, 6, 'EUR', '0.90782624', '0.92165100', '0.93547577', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (758, 9, 'HKD', '7.67834095', '7.79527000', '7.91219905', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (759, 18, 'HUF', '283.36381500', '287.67900000', '291.99418500', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (760, 35, 'IDR', '13619.94960000', '13827.36000000', '14034.77040000', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (761, 28, 'ILS', '3.91165170', '3.97122000', '4.03078830', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (762, 10, 'JPY', '116.44670000', '118.22000000', '119.99330000', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (763, 25, 'JOD', '0.69718300', '0.70780000', '0.71841700', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (764, 21, 'KWD', '0.29939075', '0.30395000', '0.30850925', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (765, 4, 'MYR', '4.22497035', '4.28931000', '4.35364965', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (766, 27, 'MXN', '18.33567650', '18.61490000', '18.89412350', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (767, 11, 'NZD', '1.53045360', '1.55376000', '1.57706640', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 26, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (768, 19, 'NOK', '8.62800900', '8.75940000', '8.89079100', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (769, 31, 'PHP', '47.27015000', '47.99000000', '48.70985000', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (770, 14, 'PLN', '4.06671040', '4.12864000', '4.19056960', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 26, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (771, 5, 'GBP', '0.69268352', '0.70323200', '0.71378048', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (772, 0, 'QAR', '3.58697600', '3.64160000', '3.69622400', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 21, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (773, 29, 'RUB', '78.93573300', '80.13780000', '81.33986700', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (774, 22, 'SAR', '3.69370075', '3.74995000', '3.80619925', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 18, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (775, 12, 'SGD', '1.40936755', '1.43083000', '1.45229245', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (776, 15, 'SEK', '8.43577640', '8.56424000', '8.69270360', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (777, 13, 'CHF', '0.99786410', '1.01306000', '1.02825590', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (778, 26, 'THB', '35.44433850', '35.98410000', '36.52386150', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (779, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-01-26 08:30:00', '2016-01-27 08:30:00', 20, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (780, 32, 'ARS', '13.60679000', '13.81400000', '14.02121000', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 26, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (781, 7, 'AUD', '1.40786050', '1.42930000', '1.45073950', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (782, 33, 'BRL', '4.01037825', '4.07145000', '4.13252175', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 27, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (783, 20, 'BND', '1.40575260', '1.42716000', '1.44856740', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (784, 8, 'CAD', '1.39104655', '1.41223000', '1.43341345', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (785, 34, 'CLP', '707.42207500', '718.19500000', '728.96792500', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 26, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (786, 3, 'CNY', '6.48514150', '6.58390000', '6.68265850', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (787, 16, 'CZK', '24.49921550', '24.87230000', '25.24538450', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 27, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (788, 17, 'DKK', '6.76563995', '6.86867000', '6.97170005', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 26, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (789, 24, 'EGP', '7.71284550', '7.83030000', '7.94775450', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (790, 6, 'EUR', '0.90671614', '0.92052400', '0.93433186', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 25, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (791, 9, 'HKD', '7.67583905', '7.79273000', '7.90962095', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (792, 18, 'HUF', '282.93731000', '287.24600000', '291.55469000', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 26, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (793, 35, 'IDR', '13701.70460000', '13910.36000000', '14119.01540000', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (794, 28, 'ILS', '3.90503250', '3.96450000', '4.02396750', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 26, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (795, 10, 'JPY', '116.50481500', '118.27900000', '120.05318500', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 24, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (796, 25, 'JOD', '0.69885750', '0.70950000', '0.72014250', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (797, 21, 'KWD', '0.29924202', '0.30379900', '0.30835599', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 22, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (798, 4, 'MYR', '4.20069995', '4.26467000', '4.32864005', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 23, '2016-02-24 15:24:37');  "),
    $i+=1 => array("insert" => " (799, 27, 'MXN', '18.17482600', '18.45160000', '18.72837400', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (800, 11, 'NZD', '1.51498910', '1.53806000', '1.56113090', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (801, 19, 'NOK', '8.57191325', '8.70245000', '8.83298675', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (802, 31, 'PHP', '47.30669350', '48.02710000', '48.74750650', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (803, 14, 'PLN', '4.03297415', '4.09439000', '4.15580585', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (804, 5, 'GBP', '0.68648590', '0.69694000', '0.70739410', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (805, 0, 'QAR', '3.58687750', '3.64150000', '3.69612250', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 22, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (806, 29, 'RUB', '77.43065300', '78.60980000', '79.78894700', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (807, 22, 'SAR', '3.69443950', '3.75070000', '3.80696050', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 19, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (808, 12, 'SGD', '1.40575260', '1.42716000', '1.44856740', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (809, 15, 'SEK', '8.41952390', '8.54774000', '8.67595610', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (810, 13, 'CHF', '1.00183365', '1.01709000', '1.03234635', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 23, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (811, 26, 'THB', '35.31205300', '35.84980000', '36.38754700', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (812, 23, 'AED', '3.61805275', '3.67315000', '3.72824725', '2016-01-27 08:30:00', '2016-01-28 08:30:00', 21, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (813, 32, 'ARS', '13.62846000', '13.83600000', '14.04354000', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (814, 7, 'AUD', '1.40363485', '1.42501000', '1.44638515', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (815, 33, 'BRL', '4.04947290', '4.11114000', '4.17280710', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (816, 20, 'BND', '1.41090415', '1.43239000', '1.45387585', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (817, 8, 'CAD', '1.38937205', '1.41053000', '1.43168795', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (818, 34, 'CLP', '705.50625000', '716.25000000', '726.99375000', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (819, 3, 'CNY', '6.47973385', '6.57841000', '6.67708615', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (820, 16, 'CZK', '24.41460400', '24.78640000', '25.15819600', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (821, 17, 'DKK', '6.74235455', '6.84503000', '6.94770545', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (822, 24, 'EGP', '7.70763485', '7.82501000', '7.94238515', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (823, 6, 'EUR', '0.90346761', '0.91722600', '0.93098439', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (824, 9, 'HKD', '7.67408575', '7.79095000', '7.90781425', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (825, 18, 'HUF', '283.92526500', '288.24900000', '292.57273500', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (826, 35, 'IDR', '13635.31560000', '13842.96000000', '14050.60440000', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (827, 28, 'ILS', '3.91484310', '3.97446000', '4.03407690', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (828, 10, 'JPY', '116.71560500', '118.49300000', '120.27039500', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (829, 25, 'JOD', '0.69811875', '0.70875000', '0.71938125', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (830, 21, 'KWD', '0.29897114', '0.30352400', '0.30807686', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 23, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (831, 4, 'MYR', '4.19997105', '4.26393000', '4.32788895', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (832, 27, 'MXN', '18.19216200', '18.46920000', '18.74623800', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (833, 11, 'NZD', '1.53195080', '1.55528000', '1.57860920', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (834, 19, 'NOK', '8.53345885', '8.66341000', '8.79336115', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (835, 31, 'PHP', '47.22090000', '47.94000000', '48.65910000', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (836, 14, 'PLN', '4.04674445', '4.10837000', '4.16999555', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (837, 5, 'GBP', '0.69122572', '0.70175200', '0.71227828', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (838, 0, 'QAR', '3.58707450', '3.64170000', '3.69632550', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 23, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (839, 29, 'RUB', '77.01626350', '78.18910000', '79.36193650', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (840, 22, 'SAR', '3.69227250', '3.74850000', '3.80472750', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 20, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (841, 12, 'SGD', '1.41097310', '1.43246000', '1.45394690', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (842, 15, 'SEK', '8.37681430', '8.50438000', '8.63194570', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (843, 13, 'CHF', '0.99944995', '1.01467000', '1.02989005', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (844, 26, 'THB', '35.34800550', '35.88630000', '36.42459450', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (845, 23, 'AED', '3.61785575', '3.67295000', '3.72804425', '2016-01-28 08:30:00', '2016-01-29 08:30:00', 22, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (846, 32, 'ARS', '13.61998900', '13.82740000', '14.03481100', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (847, 7, 'AUD', '1.39072150', '1.41190000', '1.43307850', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (848, 33, 'BRL', '4.00798470', '4.06902000', '4.13005530', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (849, 20, 'BND', '1.40550635', '1.42691000', '1.44831365', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (850, 8, 'CAD', '1.38269375', '1.40375000', '1.42480625', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (851, 34, 'CLP', '700.42956000', '711.09600000', '721.76244000', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (852, 3, 'CNY', '6.47817755', '6.57683000', '6.67548245', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (853, 16, 'CZK', '24.34496450', '24.71570000', '25.08643550', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (854, 17, 'DKK', '6.72380700', '6.82620000', '6.92859300', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (855, 24, 'EGP', '7.71495340', '7.83244000', '7.94992660', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (856, 6, 'EUR', '0.90096473', '0.91468500', '0.92840528', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (857, 9, 'HKD', '7.67506090', '7.79194000', '7.90881910', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (858, 18, 'HUF', '282.16605500', '286.46300000', '290.75994500', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (859, 35, 'IDR', '13653.26230000', '13861.18000000', '14069.09770000', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (860, 28, 'ILS', '3.89457180', '3.95388000', '4.01318820', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (861, 10, 'JPY', '117.05641500', '118.83900000', '120.62158500', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (862, 25, 'JOD', '0.69792569', '0.70855400', '0.71918231', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (863, 21, 'KWD', '0.29904600', '0.30360000', '0.30815400', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (864, 4, 'MYR', '4.09815160', '4.16056000', '4.22296840', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (865, 27, 'MXN', '18.06982500', '18.34500000', '18.62017500', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (866, 11, 'NZD', '1.52036720', '1.54352000', '1.56667280', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (867, 19, 'NOK', '8.49192140', '8.62124000', '8.75055860', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (868, 31, 'PHP', '46.97563500', '47.69100000', '48.40636500', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (869, 14, 'PLN', '4.01737175', '4.07855000', '4.13972825', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (870, 5, 'GBP', '0.68598257', '0.69642900', '0.70687544', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (871, 0, 'QAR', '3.58540000', '3.64000000', '3.69460000', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (872, 29, 'RUB', '75.27793550', '76.42430000', '77.57066450', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (873, 22, 'SAR', '3.69401595', '3.75027000', '3.80652405', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 21, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (874, 12, 'SGD', '1.40550635', '1.42691000', '1.44831365', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (875, 15, 'SEK', '8.38147335', '8.50911000', '8.63674665', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (876, 13, 'CHF', '0.99912490', '1.01434000', '1.02955510', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (877, 26, 'THB', '35.27994200', '35.81720000', '36.35445800', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (878, 23, 'AED', '3.61795425', '3.67305000', '3.72814575', '2016-01-29 08:30:00', '2016-01-30 08:30:00', 23, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (879, 32, 'ARS', '13.73237750', '13.94150000', '14.15062250', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (880, 7, 'AUD', '1.39046540', '1.41164000', '1.43281460', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (881, 33, 'BRL', '3.93911350', '3.99910000', '4.05908650', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (882, 20, 'BND', '1.40254150', '1.42390000', '1.44525850', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (883, 8, 'CAD', '1.37668525', '1.39765000', '1.41861475', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (884, 34, 'CLP', '702.79750000', '713.50000000', '724.20250000', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (885, 3, 'CNY', '6.45836920', '6.55672000', '6.65507080', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (886, 16, 'CZK', '24.57131750', '24.94550000', '25.31968250', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (887, 17, 'DKK', '6.78714250', '6.89050000', '6.99385750', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (888, 24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (889, 6, 'EUR', '0.90943080', '0.92328000', '0.93712920', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (890, 9, 'HKD', '7.66516165', '7.78189000', '7.89861835', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (891, 18, 'HUF', '282.57483000', '286.87800000', '291.18117000', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (892, 35, 'IDR', '13650.37625000', '13858.25000000', '14066.12375000', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (893, 28, 'ILS', '3.90109250', '3.96050000', '4.01990750', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (894, 10, 'JPY', '119.34161500', '121.15900000', '122.97638500', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (895, 25, 'JOD', '0.69870975', '0.70935000', '0.71999025', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (896, 21, 'KWD', '0.29934150', '0.30390000', '0.30845850', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (897, 4, 'MYR', '4.09070500', '4.15300000', '4.21529500', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (898, 27, 'MXN', '17.84160050', '18.11330000', '18.38499950', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (899, 11, 'NZD', '1.51830855', '1.54143000', '1.56455145', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (900, 19, 'NOK', '8.54993790', '8.68014000', '8.81034210', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (901, 31, 'PHP', '46.98942500', '47.70500000', '48.42057500', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (902, 14, 'PLN', '4.01837645', '4.07957000', '4.14076355', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (903, 5, 'GBP', '0.69150251', '0.70203300', '0.71256350', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (904, 0, 'QAR', '3.58599100', '3.64060000', '3.69520900', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 25, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (905, 29, 'RUB', '74.50884750', '75.64350000', '76.77815250', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (906, 22, 'SAR', '3.69384850', '3.75010000', '3.80635150', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 22, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (907, 12, 'SGD', '1.40254150', '1.42390000', '1.44525850', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (908, 15, 'SEK', '8.45032485', '8.57901000', '8.70769515', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (909, 13, 'CHF', '1.00798005', '1.02333000', '1.03867995', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (910, 26, 'THB', '35.16942500', '35.70500000', '36.24057500', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (911, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-01-30 08:30:00', '2016-01-31 08:30:00', 24, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (912, 32, 'ARS', '13.67869500', '13.88700000', '14.09530500', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (913, 7, 'AUD', '1.39019945', '1.41137000', '1.43254055', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (914, 33, 'BRL', '3.93734050', '3.99730000', '4.05725950', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 31, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (915, 34, 'CLP', '702.10800000', '712.80000000', '723.49200000', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (916, 3, 'CNY', '6.47834500', '6.57700000', '6.67565500', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (917, 16, 'CZK', '24.56718050', '24.94130000', '25.31541950', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 31, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (918, 17, 'DKK', '6.78509370', '6.88842000', '6.99174630', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (919, 24, 'EGP', '7.71264850', '7.83010000', '7.94755150', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (920, 9, 'HKD', '7.66517150', '7.78190000', '7.89862850', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (921, 18, 'HUF', '282.57187500', '286.87500000', '291.17812500', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (922, 28, 'ILS', '3.90193960', '3.96136000', '4.02078040', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (923, 10, 'JPY', '119.35540500', '121.17300000', '122.99059500', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 28, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (924, 21, 'KWD', '0.29931097', '0.30386900', '0.30842704', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 26, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (925, 4, 'MYR', '4.08077620', '4.14292000', '4.20506380', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 27, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (926, 27, 'MXN', '17.84219150', '18.11390000', '18.38560850', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (927, 11, 'NZD', '1.51945115', '1.54259000', '1.56572885', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 31, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (928, 19, 'NOK', '8.54906125', '8.67925000', '8.80943875', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (929, 31, 'PHP', '46.98972050', '47.70530000', '48.42087950', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 29, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (930, 14, 'PLN', '4.01958800', '4.08080000', '4.14201200', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 31, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (931, 5, 'GBP', '0.69168966', '0.70222300', '0.71275635', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 30, '2016-02-24 15:24:38');  "),
    $i+=1 => array("insert" => " (932, 0, 'QAR', '3.58594175', '3.64055000', '3.69515825', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 26, '2016-02-24 15:24:39');  "),
    $i+=1 => array("insert" => " (933, 29, 'RUB', '74.58764750', '75.72350000', '76.85935250', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 30, '2016-02-24 15:24:39');  "),
    $i+=1 => array("insert" => " (934, 15, 'SEK', '8.45031500', '8.57900000', '8.70768500', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 30, '2016-02-24 15:24:39');  "),
    $i+=1 => array("insert" => " (935, 13, 'CHF', '1.00770425', '1.02305000', '1.03839575', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 27, '2016-02-24 15:24:39');  "),
    $i+=1 => array("insert" => " (936, 26, 'THB', '35.14923250', '35.68450000', '36.21976750', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 29, '2016-02-24 15:24:39');  "),
    $i+=1 => array("insert" => " (937, 23, 'AED', '3.61765875', '3.67275000', '3.72784125', '2016-01-31 08:30:00', '2016-02-01 08:30:00', 25, '2016-02-24 15:24:39');  "),
    $i+=1 => array("insert" => " (938, 32, 'ARS', '13.72114850', '13.93010000', '14.13905150', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (939, 7, 'AUD', '1.39611930', '1.41738000', '1.43864070', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 30, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (940, 33, 'BRL', '3.93911350', '3.99910000', '4.05908650', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (941, 20, 'BND', '1.40421600', '1.42560000', '1.44698400', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 27, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (942, 8, 'CAD', '1.37740430', '1.39838000', '1.41935570', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 28, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (943, 34, 'CLP', '702.56799500', '713.26700000', '723.96600500', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (944, 3, 'CNY', '6.50204410', '6.60106000', '6.70007590', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 29, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (945, 16, 'CZK', '24.56599850', '24.94010000', '25.31420150', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (946, 17, 'DKK', '6.78444360', '6.88776000', '6.99107640', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (947, 2, 'AUG', '0.00000000', '0.03835673', '0.00000000', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 3, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (948, 6, 'EUR', '0.90915697', '0.92300200', '0.93684703', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 29, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (949, 9, 'HKD', '7.66778175', '7.78455000', '7.90131825', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 29, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (950, 18, 'HUF', '282.97178500', '287.28100000', '291.59021500', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (951, 35, 'IDR', '13673.73060000', '13881.96000000', '14090.18940000', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 27, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (952, 28, 'ILS', '3.90167365', '3.96109000', '4.02050635', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (953, 10, 'JPY', '119.40170000', '121.22000000', '123.03830000', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 29, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (954, 4, 'MYR', '4.09957985', '4.16201000', '4.22444015', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 28, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (955, 27, 'MXN', '17.88966850', '18.16210000', '18.43453150', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 30, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (956, 11, 'NZD', '1.52449435', '1.54771000', '1.57092565', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (957, 19, 'NOK', '8.55284365', '8.68309000', '8.81333635', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (958, 31, 'PHP', '46.92540000', '47.64000000', '48.35460000', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 30, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (959, 14, 'PLN', '4.01929250', '4.08050000', '4.14170750', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (960, 5, 'GBP', '0.69162563', '0.70215800', '0.71269037', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (961, 29, 'RUB', '74.36769700', '75.50020000', '76.63270300', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (962, 12, 'SGD', '1.40426525', '1.42565000', '1.44703475', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 28, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (963, 15, 'SEK', '8.44982250', '8.57850000', '8.70717750', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (964, 13, 'CHF', '1.00765500', '1.02300000', '1.03834500', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 28, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (965, 26, 'THB', '35.20606700', '35.74220000', '36.27833300', '2016-02-01 08:30:00', '2016-02-02 08:30:00', 30, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (966, 32, 'ARS', '13.88909100', '14.10060000', '14.31210900', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (967, 7, 'AUD', '1.38648600', '1.40760000', '1.42871400', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (968, 33, 'BRL', '3.90431345', '3.96377000', '4.02322655', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 33, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (969, 20, 'BND', '1.40300445', '1.42437000', '1.44573555', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 28, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (970, 8, 'CAD', '1.37510925', '1.39605000', '1.41699075', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 29, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (971, 34, 'CLP', '700.18725000', '710.85000000', '721.51275000', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (972, 3, 'CNY', '6.47972400', '6.57840000', '6.67707600', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 30, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (973, 16, 'CZK', '24.42258250', '24.79450000', '25.16641750', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 33, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (974, 17, 'DKK', '6.74565430', '6.84838000', '6.95110570', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (975, 2, 'AUG', '0.00000000', '0.03835673', '0.03835673', '2016-02-02 08:30:00', '2999-12-31 23:59:59', 4, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (976, 24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 28, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (977, 6, 'EUR', '0.90386851', '0.91763300', '0.93139750', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 30, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (978, 9, 'HKD', '7.66167475', '7.77835000', '7.89502525', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 30, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (979, 18, 'HUF', '280.54671500', '284.81900000', '289.09128500', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (980, 35, 'IDR', '13436.20770000', '13640.82000000', '13845.43230000', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 28, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (981, 28, 'ILS', '3.89690625', '3.95625000', '4.01559375', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (982, 10, 'JPY', '119.11112500', '120.92500000', '122.73887500', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 30, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (983, 25, 'JOD', '0.69752775', '0.70815000', '0.71877225', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 27, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (984, 21, 'KWD', '0.29857813', '0.30312500', '0.30767188', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 27, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (985, 4, 'MYR', '4.11391160', '4.17656000', '4.23920840', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 29, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (986, 27, 'MXN', '17.97231000', '18.24600000', '18.51969000', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (987, 11, 'NZD', '1.50632110', '1.52926000', '1.55219890', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 33, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (988, 19, 'NOK', '8.55465605', '8.68493000', '8.81520395', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (989, 31, 'PHP', '46.94510000', '47.66000000', '48.37490000', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (990, 14, 'PLN', '3.97616920', '4.03672000', '4.09727080', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 33, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (991, 5, 'GBP', '0.68289459', '0.69329400', '0.70369341', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (992, 0, 'QAR', '3.58638500', '3.64100000', '3.69561500', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 27, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (993, 29, 'RUB', '76.28037000', '77.44200000', '78.60363000', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (994, 22, 'SAR', '3.69443950', '3.75070000', '3.80696050', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 23, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (995, 12, 'SGD', '1.40300445', '1.42437000', '1.44573555', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 29, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (996, 15, 'SEK', '8.39937080', '8.52728000', '8.65518920', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (997, 13, 'CHF', '1.00452270', '1.01982000', '1.03511730', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 29, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (998, 26, 'THB', '35.06462100', '35.59860000', '36.13257900', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (999, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-02-02 08:30:00', '2016-02-03 08:30:00', 26, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1000, 32, 'ARS', '13.90022150', '14.11190000', '14.32357850', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 33, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1001, 7, 'AUD', '1.39942890', '1.42074000', '1.44205110', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 32, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1002, 33, 'BRL', '3.92896800', '3.98880000', '4.04863200', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 34, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1003, 20, 'BND', '1.40848105', '1.42993000', '1.45137895', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 29, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1004, 8, 'CAD', '1.38607230', '1.40718000', '1.42828770', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 30, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1005, 34, 'CLP', '703.57959000', '714.29400000', '725.00841000', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 33, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1006, 3, 'CNY', '6.48079765', '6.57949000', '6.67818235', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1007, 16, 'CZK', '24.36604350', '24.73710000', '25.10815650', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 34, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1008, 17, 'DKK', '6.72861380', '6.83108000', '6.93354620', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 33, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1009, 6, 'EUR', '0.90163453', '0.91536500', '0.92909548', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 31, '2016-02-24 15:25:17');  "),
    $i+=1 => array("insert" => " (1010, 9, 'HKD', '7.67811440', '7.79504000', '7.91196560', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1011, 18, 'HUF', '281.35540000', '285.64000000', '289.92460000', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1012, 35, 'IDR', '13484.37420000', '13689.72000000', '13895.06580000', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 29, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1013, 28, 'ILS', '3.90358455', '3.96303000', '4.02247545', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1014, 10, 'JPY', '118.01186500', '119.80900000', '121.60613500', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1015, 25, 'JOD', '0.69836402', '0.70899900', '0.71963399', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 28, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1016, 21, 'KWD', '0.29849342', '0.30303900', '0.30758459', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 28, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1017, 4, 'MYR', '4.17222360', '4.23576000', '4.29929640', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 30, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1018, 27, 'MXN', '18.21609750', '18.49350000', '18.77090250', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1019, 11, 'NZD', '1.50722730', '1.53018000', '1.55313270', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1020, 19, 'NOK', '8.58887495', '8.71967000', '8.85046505', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1021, 31, 'PHP', '47.20701150', '47.92590000', '48.64478850', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1022, 14, 'PLN', '3.97593280', '4.03648000', '4.09702720', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1023, 5, 'GBP', '0.68377912', '0.69419200', '0.70460488', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1024, 0, 'QAR', '3.58627665', '3.64089000', '3.69550335', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 28, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1025, 29, 'RUB', '78.63255000', '79.83000000', '81.02745000', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1026, 22, 'SAR', '3.69381895', '3.75007000', '3.80632105', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 24, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1027, 12, 'SGD', '1.40848105', '1.42993000', '1.45137895', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 30, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1028, 15, 'SEK', '8.42117870', '8.54942000', '8.67766130', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1029, 13, 'CHF', '1.00312400', '1.01840000', '1.03367600', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 30, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1030, 26, 'THB', '35.30850700', '35.84620000', '36.38389300', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1031, 23, 'AED', '3.61785575', '3.67295000', '3.72804425', '2016-02-03 08:30:00', '2016-02-04 08:30:00', 27, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1032, 32, 'ARS', '13.93026400', '14.14240000', '14.35453600', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1033, 7, 'AUD', '1.37319835', '1.39411000', '1.41502165', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1034, 33, 'BRL', '3.83780625', '3.89625000', '3.95469375', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1035, 20, 'BND', '1.39486835', '1.41611000', '1.43735165', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 30, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1036, 8, 'CAD', '1.35601995', '1.37667000', '1.39732005', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1037, 34, 'CLP', '695.41000000', '706.00000000', '716.59000000', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1038, 3, 'CNY', '6.47496645', '6.57357000', '6.67217355', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1039, 16, 'CZK', '23.95618500', '24.32100000', '24.68581500', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1040, 17, 'DKK', '6.61666855', '6.71743000', '6.81819145', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1041, 6, 'EUR', '0.88657979', '0.90008100', '0.91358222', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1042, 9, 'HKD', '7.67194830', '7.78878000', '7.90561170', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1043, 18, 'HUF', '275.99109000', '280.19400000', '284.39691000', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1044, 35, 'IDR', '13434.41500000', '13639.00000000', '13843.58500000', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 30, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1045, 28, 'ILS', '3.88212140', '3.94124000', '4.00035860', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1046, 10, 'JPY', '116.20439000', '117.97400000', '119.74361000', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1047, 25, 'JOD', '0.69708450', '0.70770000', '0.71831550', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 29, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1048, 21, 'KWD', '0.29707600', '0.30160000', '0.30612400', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 29, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1049, 4, 'MYR', '4.09459575', '4.15695000', '4.21930425', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1050, 27, 'MXN', '17.91921850', '18.19210000', '18.46498150', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1051, 11, 'NZD', '1.47727345', '1.49977000', '1.52226655', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1052, 19, 'NOK', '8.42705915', '8.55539000', '8.68372085', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1053, 31, 'PHP', '47.00853400', '47.72440000', '48.44026600', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1054, 14, 'PLN', '3.91789660', '3.97756000', '4.03722340', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1055, 5, 'GBP', '0.67472894', '0.68500400', '0.69527906', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1056, 0, 'QAR', '3.58623725', '3.64085000', '3.69546275', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 29, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1057, 29, 'RUB', '75.51285800', '76.66280000', '77.81274200', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1058, 22, 'SAR', '3.69424250', '3.75050000', '3.80675750', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 25, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1059, 12, 'SGD', '1.39486835', '1.41611000', '1.43735165', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1060, 15, 'SEK', '8.30930240', '8.43584000', '8.56237760', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1061, 13, 'CHF', '0.98876270', '1.00382000', '1.01887730', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1062, 26, 'THB', '35.14125400', '35.67640000', '36.21154600', '2016-02-04 08:30:00', '2016-02-05 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1063, 32, 'ARS', '13.95351000', '14.16600000', '14.37849000', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1064, 7, 'AUD', '1.36918940', '1.39004000', '1.41089060', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1065, 33, 'BRL', '3.82983760', '3.88816000', '3.94648240', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1066, 20, 'BND', '1.37687240', '1.39784000', '1.41880760', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1067, 8, 'CAD', '1.35431590', '1.37494000', '1.39556410', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1068, 34, 'CLP', '685.60925000', '696.05000000', '706.49075000', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1069, 3, 'CNY', '6.46705690', '6.56554000', '6.66402310', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1070, 16, 'CZK', '23.76588300', '24.12780000', '24.48971700', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1071, 17, 'DKK', '6.56392180', '6.66388000', '6.76383820', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1072, 6, 'EUR', '0.87948779', '0.89288100', '0.90627422', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1073, 9, 'HKD', '7.66977145', '7.78657000', '7.90336855', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1074, 18, 'HUF', '273.21831500', '277.37900000', '281.53968500', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1075, 35, 'IDR', '13433.98160000', '13638.56000000', '13843.13840000', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1076, 28, 'ILS', '3.84197280', '3.90048000', '3.95898720', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1077, 10, 'JPY', '115.18590000', '116.94000000', '118.69410000', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1078, 25, 'JOD', '0.69723225', '0.70785000', '0.71846775', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 30, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1079, 21, 'KWD', '0.29611563', '0.30062500', '0.30513438', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 30, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1080, 4, 'MYR', '4.04788705', '4.10953000', '4.17117295', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1081, 27, 'MXN', '17.99752600', '18.27160000', '18.54567400', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1082, 11, 'NZD', '1.46381835', '1.48611000', '1.50840165', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1083, 19, 'NOK', '8.37209615', '8.49959000', '8.62708385', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1084, 31, 'PHP', '46.82217200', '47.53520000', '48.24822800', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1085, 14, 'PLN', '3.89134100', '3.95060000', '4.00985900', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1086, 5, 'GBP', '0.67590405', '0.68619700', '0.69648996', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1087, 0, 'QAR', '3.58653275', '3.64115000', '3.69576725', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 30, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1088, 29, 'RUB', '75.62239000', '76.77400000', '77.92561000', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1089, 22, 'SAR', '3.69370075', '3.74995000', '3.80619925', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 26, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1090, 12, 'SGD', '1.37676405', '1.39773000', '1.41869595', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1091, 15, 'SEK', '8.27260130', '8.39858000', '8.52455870', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1092, 13, 'CHF', '0.97835519', '0.99325400', '1.00815281', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1093, 26, 'THB', '35.04994450', '35.58370000', '36.11745550', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1094, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-02-05 08:30:00', '2016-02-06 08:30:00', 28, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1095, 32, 'ARS', '14.12145250', '14.33650000', '14.55154750', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1096, 7, 'AUD', '1.39404095', '1.41527000', '1.43649905', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1097, 33, 'BRL', '3.85208875', '3.91075000', '3.96941125', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 37, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1098, 20, 'BND', '1.38670270', '1.40782000', '1.42893730', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1099, 8, 'CAD', '1.36940610', '1.39026000', '1.41111390', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1100, 34, 'CLP', '695.01600000', '705.60000000', '716.18400000', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1101, 3, 'CNY', '6.47455275', '6.57315000', '6.67174725', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1102, 16, 'CZK', '23.89747900', '24.26140000', '24.62532100', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 37, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1103, 17, 'DKK', '6.58763075', '6.68795000', '6.78826925', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1104, 6, 'EUR', '0.88266146', '0.89610300', '0.90954455', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1105, 9, 'HKD', '7.67502150', '7.79190000', '7.90877850', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1106, 18, 'HUF', '272.71892000', '276.87200000', '281.02508000', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1107, 35, 'IDR', '13430.47500000', '13635.00000000', '13839.52500000', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1108, 28, 'ILS', '3.82524750', '3.88350000', '3.94175250', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1109, 10, 'JPY', '115.09922000', '116.85200000', '118.60478000', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1110, 25, 'JOD', '0.69885750', '0.70950000', '0.72014250', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1111, 21, 'KWD', '0.29293900', '0.29740000', '0.30186100', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 31, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1112, 4, 'MYR', '4.09563000', '4.15800000', '4.22037000', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1113, 27, 'MXN', '18.18172100', '18.45860000', '18.73547900', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1114, 11, 'NZD', '1.48659155', '1.50923000', '1.53186845', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 37, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1115, 19, 'NOK', '8.43558925', '8.56405000', '8.69251075', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1116, 31, 'PHP', '47.01897500', '47.73500000', '48.45102500', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1117, 14, 'PLN', '3.89095685', '3.95021000', '4.00946315', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 37, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1118, 5, 'GBP', '0.67921365', '0.68955700', '0.69990036', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1119, 29, 'RUB', '76.44752450', '77.61170000', '78.77587550', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1120, 22, 'SAR', '3.69399625', '3.75025000', '3.80650375', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 27, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1121, 12, 'SGD', '1.38670270', '1.40782000', '1.42893730', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1122, 15, 'SEK', '8.31688690', '8.44354000', '8.57019310', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1123, 13, 'CHF', '0.97603552', '0.99089900', '1.00576249', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1124, 26, 'THB', '35.05615000', '35.59000000', '36.12385000', '2016-02-06 08:30:00', '2016-02-07 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1125, 32, 'ARS', '14.14263000', '14.35800000', '14.57337000', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 37, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1126, 7, 'AUD', '1.39351890', '1.41474000', '1.43596110', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 36, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1127, 33, 'BRL', '3.84435650', '3.90290000', '3.96144350', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 38, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1128, 20, 'BND', '1.38673225', '1.40785000', '1.42896775', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1129, 8, 'CAD', '1.36939625', '1.39025000', '1.41110375', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1130, 34, 'CLP', '695.47895000', '706.07000000', '716.66105000', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 37, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1131, 3, 'CNY', '6.47514375', '6.57375000', '6.67235625', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1132, 16, 'CZK', '23.89935050', '24.26330000', '24.62724950', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 38, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1133, 17, 'DKK', '6.58577895', '6.68607000', '6.78636105', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 37, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1134, 24, 'EGP', '7.71373200', '7.83120000', '7.94866800', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 29, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1135, 6, 'EUR', '0.88266343', '0.89610500', '0.90954658', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1136, 9, 'HKD', '7.67403650', '7.79090000', '7.90776350', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1137, 18, 'HUF', '272.71695000', '276.87000000', '281.02305000', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 37, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1138, 35, 'IDR', '13391.61675000', '13595.55000000', '13799.48325000', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 33, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1139, 28, 'ILS', '3.82455800', '3.88280000', '3.94104200', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 37, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1140, 10, 'JPY', '115.07558000', '116.82800000', '118.58042000', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 35, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1141, 21, 'KWD', '0.29637567', '0.30088900', '0.30540234', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 32, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1142, 4, 'MYR', '4.09837815', '4.16079000', '4.22320185', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 34, '2016-02-24 15:25:18');  "),
    $i+=1 => array("insert" => " (1143, 11, 'NZD', '1.48668020', '1.50932000', '1.53195980', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1144, 19, 'NOK', '8.44213950', '8.57070000', '8.69926050', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1145, 31, 'PHP', '47.07177100', '47.78860000', '48.50542900', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1146, 14, 'PLN', '3.89404975', '3.95335000', '4.01265025', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1147, 5, 'GBP', '0.67912303', '0.68946500', '0.69980698', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1148, 29, 'RUB', '75.84637900', '77.00140000', '78.15642100', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1149, 12, 'SGD', '1.38673225', '1.40785000', '1.42896775', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 34, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1150, 15, 'SEK', '8.31630575', '8.44295000', '8.56959425', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1151, 13, 'CHF', '0.97610053', '0.99096500', '1.00582948', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 34, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1152, 26, 'THB', '35.05910500', '35.59300000', '36.12689500', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1153, 23, 'AED', '3.61785575', '3.67295000', '3.72804425', '2016-02-07 08:30:00', '2016-02-08 08:30:00', 29, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1154, 32, 'ARS', '14.14548650', '14.36090000', '14.57631350', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1155, 7, 'AUD', '1.38964785', '1.41081000', '1.43197215', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1156, 33, 'BRL', '3.84524300', '3.90380000', '3.96235700', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1157, 20, 'BND', '1.38739220', '1.40852000', '1.42964780', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 34, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1158, 8, 'CAD', '1.36812560', '1.38896000', '1.40979440', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 35, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1159, 34, 'CLP', '694.64071500', '705.21900000', '715.79728500', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1160, 3, 'CNY', '6.48600830', '6.58478000', '6.68355170', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1161, 16, 'CZK', '23.94190250', '24.30650000', '24.67109750', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1162, 17, 'DKK', '6.59885975', '6.69935000', '6.79984025', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1163, 24, 'EGP', '7.71372215', '7.83119000', '7.94865785', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 30, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1164, 6, 'EUR', '0.88422071', '0.89768600', '0.91115129', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1165, 9, 'HKD', '7.67502150', '7.79190000', '7.90877850', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1166, 18, 'HUF', '273.49707000', '277.66200000', '281.82693000', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1167, 35, 'IDR', '13411.24780000', '13615.48000000', '13819.71220000', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 34, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1168, 28, 'ILS', '3.82351390', '3.88174000', '3.93996610', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1169, 10, 'JPY', '115.24401500', '116.99900000', '118.75398500', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1170, 25, 'JOD', '0.69836402', '0.70899900', '0.71963399', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 32, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1171, 27, 'MXN', '18.13296350', '18.40910000', '18.68523650', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1172, 11, 'NZD', '1.48472005', '1.50733000', '1.52993995', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1173, 19, 'NOK', '8.44704480', '8.57568000', '8.70431520', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1174, 14, 'PLN', '3.90086595', '3.96027000', '4.01967405', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1175, 5, 'GBP', '0.67892209', '0.68926100', '0.69959992', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1176, 29, 'RUB', '75.65656950', '76.80870000', '77.96083050', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1177, 12, 'SGD', '1.38739220', '1.40852000', '1.42964780', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 35, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1178, 15, 'SEK', '8.34525490', '8.47234000', '8.59942510', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1179, 13, 'CHF', '0.97830594', '0.99320400', '1.00810206', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 35, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1180, 26, 'THB', '35.07594850', '35.61010000', '36.14425150', '2016-02-08 08:30:00', '2016-02-09 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1181, 32, 'ARS', '14.06481500', '14.27900000', '14.49318500', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1182, 7, 'AUD', '1.39429705', '1.41553000', '1.43676295', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1183, 33, 'BRL', '3.83816085', '3.89661000', '3.95505915', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1184, 20, 'BND', '1.38555025', '1.40665000', '1.42774975', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 35, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1185, 8, 'CAD', '1.37298165', '1.39389000', '1.41479835', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1186, 34, 'CLP', '702.10209000', '712.79400000', '723.48591000', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1187, 3, 'CNY', '6.47676900', '6.57540000', '6.67403100', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1188, 16, 'CZK', '23.83306000', '24.19600000', '24.55894000', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1189, 17, 'DKK', '6.57208745', '6.67217000', '6.77225255', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1190, 24, 'EGP', '7.71294400', '7.83040000', '7.94785600', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 31, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1191, 6, 'EUR', '0.88055159', '0.89396100', '0.90737042', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1192, 9, 'HKD', '7.67580950', '7.79270000', '7.90959050', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1193, 18, 'HUF', '273.88910000', '278.06000000', '282.23090000', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1194, 35, 'IDR', '13396.24625000', '13600.25000000', '13804.25375000', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 35, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1195, 28, 'ILS', '3.83285170', '3.89122000', '3.94958830', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1196, 10, 'JPY', '113.85024000', '115.58400000', '117.31776000', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1197, 25, 'JOD', '0.69885750', '0.70950000', '0.72014250', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 33, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1198, 21, 'KWD', '0.29599152', '0.30049900', '0.30500649', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 33, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1199, 4, 'MYR', '4.12565280', '4.18848000', '4.25130720', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 35, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1200, 27, 'MXN', '18.38906350', '18.66910000', '18.94913650', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1201, 11, 'NZD', '1.49008830', '1.51278000', '1.53547170', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1202, 19, 'NOK', '8.46165235', '8.59051000', '8.71936765', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1203, 31, 'PHP', '47.03306050', '47.74930000', '48.46553950', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1204, 14, 'PLN', '3.92058565', '3.98029000', '4.03999435', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1205, 5, 'GBP', '0.68302757', '0.69342900', '0.70383044', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1206, 29, 'RUB', '77.02739400', '78.20040000', '79.37340600', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1207, 22, 'SAR', '3.69424250', '3.75050000', '3.80675750', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 28, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1208, 12, 'SGD', '1.38553055', '1.40663000', '1.42772945', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1209, 15, 'SEK', '8.32611635', '8.45291000', '8.57970365', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1210, 13, 'CHF', '0.97205513', '0.98685800', '1.00166087', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1211, 26, 'THB', '34.97961550', '35.51230000', '36.04498450', '2016-02-09 08:30:00', '2016-02-10 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1212, 32, 'ARS', '14.14075850', '14.35610000', '14.57144150', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1213, 7, 'AUD', '1.39381440', '1.41504000', '1.43626560', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1214, 33, 'BRL', '3.82729630', '3.88558000', '3.94386370', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1215, 20, 'BND', '1.37614350', '1.39710000', '1.41805650', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1216, 8, 'CAD', '1.36765280', '1.38848000', '1.40930720', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1217, 34, 'CLP', '703.70862500', '714.42500000', '725.14137500', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1218, 3, 'CNY', '6.47605980', '6.57468000', '6.67330020', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1219, 16, 'CZK', '23.57627050', '23.93530000', '24.29432950', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1220, 17, 'DKK', '6.51113565', '6.61029000', '6.70944435', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1221, 24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 32, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1222, 6, 'EUR', '0.87240958', '0.88569500', '0.89898043', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1223, 9, 'HKD', '7.67840005', '7.79533000', '7.91225995', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1224, 18, 'HUF', '272.25892500', '276.40500000', '280.55107500', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1225, 35, 'IDR', '13378.99890000', '13582.74000000', '13786.48110000', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1226, 28, 'ILS', '3.81296455', '3.87103000', '3.92909545', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1227, 10, 'JPY', '113.21885500', '114.94300000', '116.66714500', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1228, 25, 'JOD', '0.69718300', '0.70780000', '0.71841700', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 34, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1229, 21, 'KWD', '0.29520450', '0.29970000', '0.30419550', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 34, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1230, 4, 'MYR', '4.09990490', '4.16234000', '4.22477510', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 36, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1231, 27, 'MXN', '18.51583300', '18.79780000', '19.07976700', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1232, 11, 'NZD', '1.48299630', '1.50558000', '1.52816370', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1233, 19, 'NOK', '8.45352610', '8.58226000', '8.71099390', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1234, 31, 'PHP', '46.89979000', '47.61400000', '48.32821000', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1235, 14, 'PLN', '3.87673345', '3.93577000', '3.99480655', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1236, 5, 'GBP', '0.68104181', '0.69141300', '0.70178420', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1237, 0, 'QAR', '3.58658200', '3.64120000', '3.69581800', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 31, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1238, 29, 'RUB', '78.09917250', '79.28850000', '80.47782750', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1239, 22, 'SAR', '3.69402580', '3.75028000', '3.80653420', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 29, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1240, 12, 'SGD', '1.37625185', '1.39721000', '1.41816815', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1241, 15, 'SEK', '8.28814460', '8.41436000', '8.54057540', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1242, 13, 'CHF', '0.95835181', '0.97294600', '0.98754019', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1243, 26, 'THB', '34.87215200', '35.40320000', '35.93424800', '2016-02-10 08:30:00', '2016-02-11 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1244, 32, 'ARS', '13.86880000', '14.08000000', '14.29120000', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1245, 7, 'AUD', '1.38417125', '1.40525000', '1.42632875', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 40, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1246, 33, 'BRL', '3.87072495', '3.92967000', '3.98861505', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 42, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1247, 20, 'BND', '1.37030245', '1.39117000', '1.41203755', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1248, 8, 'CAD', '1.37058810', '1.39146000', '1.41233190', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 38, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1249, 34, 'CLP', '701.81250000', '712.50000000', '723.18750000', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1250, 3, 'CNY', '6.47562640', '6.57424000', '6.67285360', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1251, 16, 'CZK', '23.60818450', '23.96770000', '24.32721550', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 42, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1252, 17, 'DKK', '6.51719340', '6.61644000', '6.71568660', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1253, 6, 'EUR', '0.87318576', '0.88648300', '0.89978025', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1254, 9, 'HKD', '7.67547460', '7.79236000', '7.90924540', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1255, 18, 'HUF', '271.94766500', '276.08900000', '280.23033500', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1256, 35, 'IDR', '13301.23315000', '13503.79000000', '13706.34685000', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1257, 28, 'ILS', '3.81913065', '3.87729000', '3.93544935', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1258, 10, 'JPY', '111.64187000', '113.34200000', '115.04213000', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1259, 25, 'JOD', '0.69639500', '0.70700000', '0.71760500', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 35, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1260, 21, 'KWD', '0.29436725', '0.29885000', '0.30333275', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 35, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1261, 4, 'MYR', '4.05775675', '4.11955000', '4.18134325', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 37, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1262, 27, 'MXN', '18.61876550', '18.90230000', '19.18583450', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1263, 11, 'NZD', '1.47129450', '1.49370000', '1.51610550', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 42, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1264, 19, 'NOK', '8.39002315', '8.51779000', '8.64555685', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1265, 31, 'PHP', '46.77765000', '47.49000000', '48.20235000', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 39, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1266, 14, 'PLN', '3.85018770', '3.90882000', '3.96745230', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 42, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1267, 5, 'GBP', '0.67759628', '0.68791500', '0.69823373', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1268, 0, 'QAR', '3.58668050', '3.64130000', '3.69591950', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 32, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1269, 29, 'RUB', '77.69433750', '78.87750000', '80.06066250', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 41, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1270, 22, 'SAR', '3.69389775', '3.75015000', '3.80640225', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 30, '2016-02-24 15:25:19');  "),
    $i+=1 => array("insert" => " (1271, 12, 'SGD', '1.37030245', '1.39117000', '1.41203755', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 38, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1272, 15, 'SEK', '8.24918785', '8.37481000', '8.50043215', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1273, 13, 'CHF', '0.95889553', '0.97349800', '0.98810047', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 38, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1274, 26, 'THB', '34.72972100', '35.25860000', '35.78747900', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1275, 23, 'AED', '3.61741250', '3.67250000', '3.72758750', '2016-02-11 08:30:00', '2016-02-12 08:30:00', 30, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1276, 32, 'ARS', '14.36425500', '14.58300000', '14.80174500', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1277, 7, 'AUD', '1.38551085', '1.40661000', '1.42770915', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1278, 33, 'BRL', '3.93150930', '3.99138000', '4.05125070', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1279, 20, 'BND', '1.36842110', '1.38926000', '1.41009890', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 38, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1280, 8, 'CAD', '1.37117910', '1.39206000', '1.41294090', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 39, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1281, 34, 'CLP', '701.56625000', '712.25000000', '722.93375000', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1282, 3, 'CNY', '6.47481870', '6.57342000', '6.67202130', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1283, 16, 'CZK', '23.53450650', '23.89290000', '24.25129350', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1284, 17, 'DKK', '6.49785785', '6.59681000', '6.69576215', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1285, 24, 'EGP', '7.71255000', '7.83000000', '7.94745000', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 33, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1286, 6, 'EUR', '0.87052232', '0.88377900', '0.89703569', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1287, 9, 'HKD', '7.67225365', '7.78909000', '7.90592635', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1288, 18, 'HUF', '270.70361000', '274.82600000', '278.94839000', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1289, 35, 'IDR', '13285.33525000', '13487.65000000', '13689.96475000', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 38, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1290, 28, 'ILS', '3.83089155', '3.88923000', '3.94756845', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1291, 10, 'JPY', '110.64209500', '112.32700000', '114.01190500', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1292, 25, 'JOD', '0.69738000', '0.70800000', '0.71862000', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 36, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1293, 21, 'KWD', '0.29377625', '0.29825000', '0.30272375', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 36, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1294, 4, 'MYR', '4.07703320', '4.13912000', '4.20120680', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 38, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1295, 27, 'MXN', '18.86294700', '19.15020000', '19.43745300', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1296, 11, 'NZD', '1.47063455', '1.49303000', '1.51542545', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1297, 19, 'NOK', '8.43277215', '8.56119000', '8.68960785', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1298, 31, 'PHP', '46.84069000', '47.55400000', '48.26731000', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1299, 14, 'PLN', '3.85035515', '3.90899000', '3.96762485', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1300, 5, 'GBP', '0.68070100', '0.69106700', '0.70143301', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1301, 0, 'QAR', '3.58672975', '3.64135000', '3.69597025', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 33, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1302, 29, 'RUB', '78.84925000', '80.05000000', '81.25075000', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1303, 22, 'SAR', '3.69424250', '3.75050000', '3.80675750', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 31, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1304, 12, 'SGD', '1.36842110', '1.38926000', '1.41009890', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 39, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1305, 15, 'SEK', '8.24899085', '8.37461000', '8.50022915', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1306, 13, 'CHF', '0.95781203', '0.97239800', '0.98698397', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 39, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1307, 26, 'THB', '34.69465500', '35.22300000', '35.75134500', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1308, 23, 'AED', '3.61751100', '3.67260000', '3.72768900', '2016-02-12 08:30:00', '2016-02-13 08:30:00', 31, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1309, 32, 'ARS', '14.56322500', '14.78500000', '15.00677500', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1310, 7, 'AUD', '1.38609200', '1.40720000', '1.42830800', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1311, 33, 'BRL', '3.93113500', '3.99100000', '4.05086500', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1312, 20, 'BND', '1.37689210', '1.39786000', '1.41882790', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 39, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1313, 8, 'CAD', '1.36388025', '1.38465000', '1.40541975', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1314, 34, 'CLP', '693.29225000', '703.85000000', '714.40775000', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1315, 3, 'CNY', '6.47839425', '6.57705000', '6.67570575', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1316, 16, 'CZK', '23.66856500', '24.02900000', '24.38943500', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1317, 17, 'DKK', '6.53143650', '6.63090000', '6.73036350', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1318, 24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 34, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1319, 6, 'EUR', '0.87519811', '0.88852600', '0.90185389', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1320, 9, 'HKD', '7.67429260', '7.79116000', '7.90802740', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1321, 18, 'HUF', '271.18035000', '275.31000000', '279.43965000', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1322, 35, 'IDR', '13275.33750000', '13477.50000000', '13679.66250000', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 39, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1323, 28, 'ILS', '3.82623250', '3.88450000', '3.94276750', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1324, 10, 'JPY', '111.51382000', '113.21200000', '114.91018000', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1325, 25, 'JOD', '0.69885750', '0.70950000', '0.72014250', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 37, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1326, 21, 'KWD', '0.29387475', '0.29835000', '0.30282525', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 37, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1327, 4, 'MYR', '4.08778940', '4.15004000', '4.21229060', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 39, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1328, 27, 'MXN', '18.62438000', '18.90800000', '19.19162000', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1329, 11, 'NZD', '1.48643395', '1.50907000', '1.53170605', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1330, 19, 'NOK', '8.45154625', '8.58025000', '8.70895375', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1331, 31, 'PHP', '46.85645000', '47.57000000', '48.28355000', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1332, 14, 'PLN', '3.84734105', '3.90593000', '3.96451895', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1333, 5, 'GBP', '0.67900384', '0.68934400', '0.69968416', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1334, 29, 'RUB', '77.49556450', '78.67570000', '79.85583550', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1335, 22, 'SAR', '3.69483350', '3.75110000', '3.80736650', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 32, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1336, 12, 'SGD', '1.37689210', '1.39786000', '1.41882790', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1337, 15, 'SEK', '8.27764450', '8.40370000', '8.52975550', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1338, 13, 'CHF', '0.96251245', '0.97717000', '0.99182755', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1339, 26, 'THB', '35.06895500', '35.60300000', '36.13704500', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1340, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-02-13 08:30:00', '2016-02-14 08:30:00', 32, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1341, 32, 'ARS', '14.55830000', '14.78000000', '15.00170000', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1342, 7, 'AUD', '1.38537295', '1.40647000', '1.42756705', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1343, 33, 'BRL', '3.94118200', '4.00120000', '4.06121800', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1344, 20, 'BND', '1.37653750', '1.39750000', '1.41846250', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1345, 8, 'CAD', '1.36397875', '1.38475000', '1.40552125', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1346, 34, 'CLP', '693.44000000', '704.00000000', '714.56000000', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1347, 16, 'CZK', '23.66009400', '24.02040000', '24.38070600', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1348, 17, 'DKK', '6.53163350', '6.63110000', '6.73056650', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1349, 24, 'EGP', '7.71008750', '7.82750000', '7.94491250', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 35, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1350, 6, 'EUR', '0.87512818', '0.88845500', '0.90178183', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1351, 9, 'HKD', '7.67248020', '7.78932000', '7.90615980', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1352, 18, 'HUF', '271.03260000', '275.16000000', '279.28740000', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1353, 35, 'IDR', '13318.43125000', '13521.25000000', '13724.06875000', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 40, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1354, 28, 'ILS', '3.82249935', '3.88071000', '3.93892065', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1355, 10, 'JPY', '111.52761000', '113.22600000', '114.92439000', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1356, 21, 'KWD', '0.29372602', '0.29819900', '0.30267199', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 38, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1357, 11, 'NZD', '1.48601040', '1.50864000', '1.53126960', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1358, 19, 'NOK', '8.46646900', '8.59540000', '8.72433100', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1359, 31, 'PHP', '46.82650600', '47.53960000', '48.25269400', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1360, 14, 'PLN', '3.83647650', '3.89490000', '3.95332350', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1361, 5, 'GBP', '0.67907575', '0.68941700', '0.69975826', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1362, 29, 'RUB', '77.50492200', '78.68520000', '79.86547800', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1363, 22, 'SAR', '3.69507975', '3.75135000', '3.80762025', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 33, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1364, 12, 'SGD', '1.37653750', '1.39750000', '1.41846250', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1365, 15, 'SEK', '8.27641325', '8.40245000', '8.52848675', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1366, 13, 'CHF', '0.96242380', '0.97708000', '0.99173620', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1367, 23, 'AED', '3.61785575', '3.67295000', '3.72804425', '2016-02-14 08:30:00', '2016-02-15 08:30:00', 33, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1368, 32, 'ARS', '14.40966350', '14.62910000', '14.84853650', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1369, 7, 'AUD', '1.38363935', '1.40471000', '1.42578065', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 44, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1370, 33, 'BRL', '3.94413700', '4.00420000', '4.06426300', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 46, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1371, 20, 'BND', '1.37635035', '1.39731000', '1.41826965', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1372, 8, 'CAD', '1.36267855', '1.38343000', '1.40418145', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1373, 34, 'CLP', '693.92560500', '704.49300000', '715.06039500', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1374, 3, 'CNY', '6.45988610', '6.55826000', '6.65663390', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1375, 16, 'CZK', '23.72254300', '24.08380000', '24.44505700', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 46, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1376, 17, 'DKK', '6.54633955', '6.64603000', '6.74572045', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1377, 24, 'EGP', '7.71007765', '7.82749000', '7.94490235', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 36, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1378, 6, 'EUR', '0.87703317', '0.89038900', '0.90374484', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1379, 9, 'HKD', '7.67133760', '7.78816000', '7.90498240', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1380, 18, 'HUF', '271.81075000', '275.95000000', '280.08925000', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1381, 35, 'IDR', '13341.18475000', '13544.35000000', '13747.51525000', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 41, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1382, 28, 'ILS', '3.82966030', '3.88798000', '3.94629970', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1383, 10, 'JPY', '111.71968500', '113.42100000', '115.12231500', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1384, 27, 'MXN', '18.63925350', '18.92310000', '19.20694650', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1385, 11, 'NZD', '1.48439500', '1.50700000', '1.52960500', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 46, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1386, 19, 'NOK', '8.45805710', '8.58686000', '8.71566290', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1387, 31, 'PHP', '46.78247650', '47.49490000', '48.20732350', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1388, 14, 'PLN', '3.85689555', '3.91563000', '3.97436445', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 46, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1389, 5, 'GBP', '0.67900680', '0.68934700', '0.69968721', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1390, 22, 'SAR', '3.69557225', '3.75185000', '3.80812775', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 34, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1391, 12, 'SGD', '1.37638975', '1.39735000', '1.41831025', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1392, 15, 'SEK', '8.29575865', '8.42209000', '8.54842135', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1393, 13, 'CHF', '0.96336054', '0.97803100', '0.99270147', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1394, 26, 'THB', '35.13820050', '35.67330000', '36.20839950', '2016-02-15 08:30:00', '2016-02-16 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1395, 32, 'ARS', '14.56421000', '14.78600000', '15.00779000', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 46, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1396, 7, 'AUD', '1.37656705', '1.39753000', '1.41849295', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 45, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1397, 33, 'BRL', '3.93672980', '3.99668000', '4.05663020', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 47, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1398, 20, 'BND', '1.37823170', '1.39922000', '1.42020830', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 42, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1399, 8, 'CAD', '1.35826575', '1.37895000', '1.39963425', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1400, 34, 'CLP', '693.14450000', '703.70000000', '714.25550000', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 46, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1401, 3, 'CNY', '6.40066790', '6.49814000', '6.59561210', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 43, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1402, 16, 'CZK', '23.83473450', '24.19770000', '24.56066550', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 47, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1403, 17, 'DKK', '6.58362180', '6.68388000', '6.78413820', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 46, '2016-02-24 15:25:20');  "),
    $i+=1 => array("insert" => " (1404, 24, 'EGP', '7.68792500', '7.80500000', '7.92207500', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 37, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1405, 6, 'EUR', '0.88199560', '0.89542700', '0.90885841', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1406, 9, 'HKD', '7.66732865', '7.78409000', '7.90085135', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1407, 18, 'HUF', '272.94842500', '277.10500000', '281.26157500', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1408, 35, 'IDR', '13186.93375000', '13387.75000000', '13588.56625000', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 42, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1409, 28, 'ILS', '3.83866320', '3.89712000', '3.95557680', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1410, 10, 'JPY', '112.75196500', '114.46900000', '116.18603500', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1411, 25, 'JOD', '0.69797100', '0.70860000', '0.71922900', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 38, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1412, 21, 'KWD', '0.29419488', '0.29867500', '0.30315513', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 39, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1413, 4, 'MYR', '4.06630655', '4.12823000', '4.19015345', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 40, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1414, 27, 'MXN', '18.50095950', '18.78270000', '19.06444050', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 43, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1415, 11, 'NZD', '1.48093765', '1.50349000', '1.52604235', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1416, 19, 'NOK', '8.48472105', '8.61393000', '8.74313895', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1417, 31, 'PHP', '46.71855000', '47.43000000', '48.14145000', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1418, 14, 'PLN', '3.87668420', '3.93572000', '3.99475580', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1419, 5, 'GBP', '0.68223563', '0.69262500', '0.70301438', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1420, 0, 'QAR', '3.58658200', '3.64120000', '3.69581800', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 34, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1421, 29, 'RUB', '76.74509300', '77.91380000', '79.08250700', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1422, 22, 'SAR', '3.69357270', '3.74982000', '3.80606730', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 35, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1423, 12, 'SGD', '1.37817260', '1.39916000', '1.42014740', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 43, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1424, 15, 'SEK', '8.35048525', '8.47765000', '8.60481475', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1425, 13, 'CHF', '0.97181282', '0.98661200', '1.00141118', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 43, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1426, 26, 'THB', '35.09988400', '35.63440000', '36.16891600', '2016-02-16 08:30:00', '2016-02-17 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1427, 32, 'ARS', '14.58804700', '14.81020000', '15.03235300', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1428, 7, 'AUD', '1.38596395', '1.40707000', '1.42817605', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1429, 33, 'BRL', '4.00869390', '4.06974000', '4.13078610', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1430, 20, 'BND', '1.38526460', '1.40636000', '1.42745540', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 43, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1431, 8, 'CAD', '1.36630335', '1.38711000', '1.40791665', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1432, 34, 'CLP', '698.29112500', '708.92500000', '719.55887500', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1433, 3, 'CNY', '6.42241670', '6.52022000', '6.61802330', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1434, 16, 'CZK', '23.90693500', '24.27100000', '24.63506500', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1435, 17, 'DKK', '6.60398175', '6.70455000', '6.80511825', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1436, 24, 'EGP', '7.71116115', '7.82859000', '7.94601885', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 38, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1437, 6, 'EUR', '0.88485308', '0.89832800', '0.91180292', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1438, 9, 'HKD', '7.67443050', '7.79130000', '7.90816950', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1439, 18, 'HUF', '275.09474000', '279.28400000', '283.47326000', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1440, 35, 'IDR', '13267.04380000', '13469.08000000', '13671.11620000', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 43, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1441, 28, 'ILS', '3.84391325', '3.90245000', '3.96098675', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1442, 10, 'JPY', '112.52246000', '114.23600000', '115.94954000', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1443, 25, 'JOD', '0.69836402', '0.70899900', '0.71963399', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 39, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1444, 21, 'KWD', '0.29431702', '0.29879900', '0.30328099', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 40, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1445, 4, 'MYR', '4.13571950', '4.19870000', '4.26168050', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 41, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1446, 27, 'MXN', '18.60330100', '18.88660000', '19.16989900', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1447, 11, 'NZD', '1.49877600', '1.52160000', '1.54442400', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1448, 19, 'NOK', '8.50533710', '8.63486000', '8.76438290', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1449, 31, 'PHP', '46.87339200', '47.58720000', '48.30100800', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1450, 14, 'PLN', '3.89885655', '3.95823000', '4.01760345', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1451, 5, 'GBP', '0.68907251', '0.69956600', '0.71005949', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1452, 0, 'QAR', '3.58554775', '3.64015000', '3.69475225', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 35, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1453, 29, 'RUB', '76.92544650', '78.09690000', '79.26835350', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1454, 22, 'SAR', '3.69417355', '3.75043000', '3.80668645', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 36, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1455, 12, 'SGD', '1.38526460', '1.40636000', '1.42745540', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1456, 15, 'SEK', '8.38497995', '8.51267000', '8.64036005', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1457, 13, 'CHF', '0.97410393', '0.98893800', '1.00377207', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1458, 26, 'THB', '35.13692000', '35.67200000', '36.20708000', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1459, 23, 'AED', '3.61805275', '3.67315000', '3.72824725', '2016-02-17 08:30:00', '2016-02-18 08:30:00', 34, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1460, 32, 'ARS', '14.64773800', '14.87080000', '15.09386200', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1461, 7, 'AUD', '1.37337565', '1.39429000', '1.41520435', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1462, 33, 'BRL', '3.92657445', '3.98637000', '4.04616555', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1463, 20, 'BND', '1.38319610', '1.40426000', '1.42532390', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1464, 8, 'CAD', '1.34753910', '1.36806000', '1.38858090', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1465, 34, 'CLP', '690.11562500', '700.62500000', '711.13437500', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1466, 3, 'CNY', '6.42922305', '6.52713000', '6.62503695', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1467, 16, 'CZK', '23.92673350', '24.29110000', '24.65546650', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1468, 17, 'DKK', '6.60824680', '6.70888000', '6.80951320', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1469, 24, 'EGP', '7.71353500', '7.83100000', '7.94846500', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 39, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1470, 6, 'EUR', '0.88527860', '0.89876000', '0.91224140', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1471, 9, 'HKD', '7.66980100', '7.78660000', '7.90339900', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1472, 18, 'HUF', '274.45941500', '278.63900000', '282.81858500', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1473, 35, 'IDR', '13331.65980000', '13534.68000000', '13737.70020000', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 44, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1474, 28, 'ILS', '3.84313510', '3.90166000', '3.96018490', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1475, 10, 'JPY', '112.43972000', '114.15200000', '115.86428000', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1476, 25, 'JOD', '0.69767550', '0.70830000', '0.71892450', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 40, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1477, 21, 'KWD', '0.29494840', '0.29944000', '0.30393160', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 41, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1478, 4, 'MYR', '4.11371460', '4.17636000', '4.23900540', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 42, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1479, 27, 'MXN', '18.06391500', '18.33900000', '18.61408500', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1480, 11, 'NZD', '1.48478900', '1.50740000', '1.53001100', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1481, 19, 'NOK', '8.42507930', '8.55338000', '8.68168070', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1482, 31, 'PHP', '46.86531500', '47.57900000', '48.29268500', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1483, 14, 'PLN', '3.89360650', '3.95290000', '4.01219350', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1484, 5, 'GBP', '0.68946356', '0.69996300', '0.71046245', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1485, 0, 'QAR', '3.58723210', '3.64186000', '3.69648790', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 36, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1486, 29, 'RUB', '74.29165500', '75.42300000', '76.55434500', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1487, 22, 'SAR', '3.69385835', '3.75011000', '3.80636165', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 37, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1488, 12, 'SGD', '1.38319610', '1.40426000', '1.42532390', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1489, 15, 'SEK', '8.38528530', '8.51298000', '8.64067470', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1490, 13, 'CHF', '0.97742535', '0.99231000', '1.00719465', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1491, 26, 'THB', '35.06954600', '35.60360000', '36.13765400', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1492, 23, 'AED', '3.61778680', '3.67288000', '3.72797320', '2016-02-18 08:30:00', '2016-02-19 08:30:00', 35, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1493, 32, 'ARS', '14.77628050', '15.00130000', '15.22631950', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1494, 7, 'AUD', '1.37843855', '1.39943000', '1.42042145', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1495, 33, 'BRL', '3.96784595', '4.02827000', '4.08869405', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 50, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1496, 20, 'BND', '1.38434855', '1.40543000', '1.42651145', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1497, 8, 'CAD', '1.35424695', '1.37487000', '1.39549305', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1498, 34, 'CLP', '692.25800000', '702.80000000', '713.34200000', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1499, 3, 'CNY', '6.41795465', '6.51569000', '6.61342535', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1500, 16, 'CZK', '23.95234350', '24.31710000', '24.68185650', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 50, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1501, 17, 'DKK', '6.61558505', '6.71633000', '6.81707495', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1502, 24, 'EGP', '7.71499280', '7.83248000', '7.94996720', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 40, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1503, 6, 'EUR', '0.88653940', '0.90004000', '0.91354060', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1504, 9, 'HKD', '7.66246275', '7.77915000', '7.89583725', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1505, 18, 'HUF', '275.07602500', '279.26500000', '283.45397500', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1506, 35, 'IDR', '13311.40820000', '13514.12000000', '13716.83180000', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 45, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1507, 28, 'ILS', '3.85374355', '3.91243000', '3.97111645', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1508, 10, 'JPY', '111.39463500', '113.09100000', '114.78736500', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1509, 25, 'JOD', '0.69781734', '0.70844400', '0.71907066', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 41, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1510, 21, 'KWD', '0.29481050', '0.29930000', '0.30378950', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 42, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1511, 4, 'MYR', '4.12263870', '4.18542000', '4.24820130', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 43, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1512, 27, 'MXN', '18.04638200', '18.32120000', '18.59601800', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1513, 11, 'NZD', '1.48497615', '1.50759000', '1.53020385', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 50, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1514, 19, 'NOK', '8.46135685', '8.59021000', '8.71906315', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1515, 31, 'PHP', '46.89053100', '47.60460000', '48.31866900', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1516, 14, 'PLN', '3.89566515', '3.95499000', '4.01431485', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 50, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1517, 5, 'GBP', '0.68746105', '0.69793000', '0.70839895', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1518, 0, 'QAR', '3.58737000', '3.64200000', '3.69663000', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 37, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1519, 29, 'RUB', '75.14348300', '76.28780000', '77.43211700', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 48, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1520, 22, 'SAR', '3.69402580', '3.75028000', '3.80653420', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 38, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1521, 12, 'SGD', '1.38434855', '1.40543000', '1.42651145', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1522, 15, 'SEK', '8.31960550', '8.44630000', '8.57299450', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1523, 13, 'CHF', '0.97747362', '0.99235900', '1.00724439', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 46, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1524, 26, 'THB', '35.05418000', '35.58800000', '36.12182000', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 47, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1525, 23, 'AED', '3.61787545', '3.67297000', '3.72806455', '2016-02-19 08:30:00', '2016-02-20 08:30:00', 36, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1526, 32, 'ARS', '14.84148750', '15.06750000', '15.29351250', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 50, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1527, 7, 'AUD', '1.37776875', '1.39875000', '1.41973125', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 49, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1528, 33, 'BRL', '3.96226100', '4.02260000', '4.08293900', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 51, '2016-02-24 15:25:21');  "),
    $i+=1 => array("insert" => " (1529, 20, 'BND', '1.38411215', '1.40519000', '1.42626785', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 46, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1530, 8, 'CAD', '1.35611845', '1.37677000', '1.39742155', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 47, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1531, 34, 'CLP', '689.64775000', '700.15000000', '710.65225000', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1532, 3, 'CNY', '6.42614000', '6.52400000', '6.62186000', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 47, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1533, 16, 'CZK', '23.91057950', '24.27470000', '24.63882050', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1534, 17, 'DKK', '6.60339075', '6.70395000', '6.80450925', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1535, 24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 41, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1536, 6, 'EUR', '0.88483535', '0.89831000', '0.91178465', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1537, 9, 'HKD', '7.65788250', '7.77450000', '7.89111750', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1538, 18, 'HUF', '273.06662500', '277.22500000', '281.38337500', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1539, 35, 'IDR', '13259.87300000', '13461.80000000', '13663.72700000', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 46, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1540, 28, 'ILS', '3.85332000', '3.91200000', '3.97068000', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1541, 10, 'JPY', '110.89622500', '112.58500000', '114.27377500', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1542, 25, 'JOD', '0.69718300', '0.70780000', '0.71841700', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 42, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1543, 21, 'KWD', '0.29467260', '0.29916000', '0.30364740', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 43, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1544, 4, 'MYR', '4.14192500', '4.20500000', '4.26807500', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 44, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1545, 27, 'MXN', '17.97280250', '18.24650000', '18.52019750', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 47, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1546, 11, 'NZD', '1.48542925', '1.50805000', '1.53067075', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1547, 19, 'NOK', '8.42766985', '8.55601000', '8.68435015', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1548, 31, 'PHP', '46.91062500', '47.62500000', '48.33937500', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1549, 14, 'PLN', '3.86901105', '3.92793000', '3.98684895', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1550, 5, 'GBP', '0.68379587', '0.69420900', '0.70462214', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1551, 29, 'RUB', '75.90006150', '77.05590000', '78.21173850', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1552, 22, 'SAR', '3.69409475', '3.75035000', '3.80660525', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 39, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1553, 12, 'SGD', '1.38411215', '1.40519000', '1.42626785', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 47, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1554, 15, 'SEK', '8.32168385', '8.44841000', '8.57513615', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1555, 13, 'CHF', '0.97485549', '0.98970100', '1.00454652', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 47, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1556, 26, 'THB', '35.18420000', '35.72000000', '36.25580000', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1557, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-02-20 08:30:00', '2016-02-21 08:30:00', 37, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1558, 32, 'ARS', '14.77992500', '15.00500000', '15.23007500', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1559, 7, 'AUD', '1.37785740', '1.39884000', '1.41982260', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1560, 33, 'BRL', '3.95960150', '4.01990000', '4.08019850', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1561, 20, 'BND', '1.38426975', '1.40535000', '1.42643025', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 47, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1562, 8, 'CAD', '1.35629575', '1.37695000', '1.39760425', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1563, 34, 'CLP', '688.21950000', '698.70000000', '709.18050000', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1564, 3, 'CNY', '6.42407150', '6.52190000', '6.61972850', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1565, 16, 'CZK', '23.90516200', '24.26920000', '24.63323800', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1566, 17, 'DKK', '6.60382415', '6.70439000', '6.80495585', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1567, 24, 'EGP', '7.71255000', '7.83000000', '7.94745000', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 42, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1568, 18, 'HUF', '272.90508500', '277.06100000', '281.21691500', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1569, 28, 'ILS', '3.85355640', '3.91224000', '3.97092360', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1570, 10, 'JPY', '110.94843000', '112.63800000', '114.32757000', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1571, 4, 'MYR', '4.13995500', '4.20300000', '4.26604500', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 45, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1572, 27, 'MXN', '17.97595450', '18.24970000', '18.52344550', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1573, 11, 'NZD', '1.48533075', '1.50795000', '1.53056925', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1574, 19, 'NOK', '8.42480350', '8.55310000', '8.68139650', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1575, 31, 'PHP', '46.91555000', '47.63000000', '48.34445000', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1576, 14, 'PLN', '3.86986800', '3.92880000', '3.98773200', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1577, 5, 'GBP', '0.68381360', '0.69422700', '0.70464041', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1578, 0, 'QAR', '3.58687750', '3.64150000', '3.69612250', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 38, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1579, 29, 'RUB', '75.71104000', '76.86400000', '78.01696000', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1580, 22, 'SAR', '3.69389775', '3.75015000', '3.80640225', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 40, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1581, 12, 'SGD', '1.38426975', '1.40535000', '1.42643025', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1582, 15, 'SEK', '8.32157550', '8.44830000', '8.57502450', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1583, 13, 'CHF', '0.97502195', '0.98987000', '1.00471805', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1584, 26, 'THB', '35.20390000', '35.74000000', '36.27610000', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1585, 23, 'AED', '3.61751100', '3.67260000', '3.72768900', '2016-02-21 08:30:00', '2016-02-22 08:30:00', 38, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1586, 7, 'AUD', '1.37720730', '1.39818000', '1.41915270', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1587, 33, 'BRL', '3.96226100', '4.02260000', '4.08293900', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1588, 20, 'BND', '1.38491000', '1.40600000', '1.42709000', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1589, 8, 'CAD', '1.35745805', '1.37813000', '1.39880195', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1590, 34, 'CLP', '688.81148500', '699.30100000', '709.79051500', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1591, 3, 'CNY', '6.43319260', '6.53116000', '6.62912740', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1592, 16, 'CZK', '23.94584250', '24.31050000', '24.67515750', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1593, 17, 'DKK', '6.61300435', '6.71371000', '6.81441565', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1594, 6, 'EUR', '0.88610994', '0.89960400', '0.91309806', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1595, 9, 'HKD', '7.65603070', '7.77262000', '7.88920930', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1596, 18, 'HUF', '273.37197500', '277.53500000', '281.69802500', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1597, 35, 'IDR', '13272.92425000', '13475.05000000', '13677.17575000', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 47, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1598, 28, 'ILS', '3.85831395', '3.91707000', '3.97582605', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1599, 10, 'JPY', '111.02723000', '112.71800000', '114.40877000', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1600, 21, 'KWD', '0.29471102', '0.29919900', '0.30368699', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 44, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1601, 27, 'MXN', '17.92571950', '18.19870000', '18.47168050', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1602, 11, 'NZD', '1.48474960', '1.50736000', '1.52997040', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1603, 19, 'NOK', '8.43863290', '8.56714000', '8.69564710', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1604, 14, 'PLN', '3.87447780', '3.93348000', '3.99248220', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1605, 5, 'GBP', '0.68939264', '0.69989100', '0.71038937', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1606, 29, 'RUB', '74.93623900', '76.07740000', '77.21856100', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1607, 12, 'SGD', '1.38491000', '1.40600000', '1.42709000', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1608, 15, 'SEK', '8.32228470', '8.44902000', '8.57575530', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1609, 13, 'CHF', '0.97595081', '0.99081300', '1.00567520', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1610, 26, 'THB', '35.20212700', '35.73820000', '36.27427300', '2016-02-22 08:30:00', '2016-02-23 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1611, 32, 'ARS', '15.03720700', '15.26620000', '15.49519300', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1612, 7, 'AUD', '1.36020620', '1.38092000', '1.40163380', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1613, 33, 'BRL', '3.88804125', '3.94725000', '4.00645875', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 54, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1614, 20, 'BND', '1.37809380', '1.39908000', '1.42006620', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 49, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1615, 8, 'CAD', '1.34902645', '1.36957000', '1.39011355', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1616, 34, 'CLP', '681.81700000', '692.20000000', '702.58300000', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1617, 3, 'CNY', '6.42641580', '6.52428000', '6.62214420', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1618, 16, 'CZK', '24.17111200', '24.53920000', '24.90728800', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 54, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1619, 17, 'DKK', '6.66524875', '6.76675000', '6.86825125', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1620, 24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 43, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1621, 6, 'EUR', '0.89323051', '0.90683300', '0.92043550', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1622, 9, 'HKD', '7.65351895', '7.77007000', '7.88662105', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1623, 18, 'HUF', '274.34909500', '278.52700000', '282.70490500', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1624, 35, 'IDR', '13242.89160000', '13444.56000000', '13646.22840000', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 48, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1625, 28, 'ILS', '3.84952775', '3.90815000', '3.96677225', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1626, 10, 'JPY', '111.19172500', '112.88500000', '114.57827500', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1627, 21, 'KWD', '0.29485975', '0.29935000', '0.30384025', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 45, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1628, 4, 'MYR', '4.10913435', '4.17171000', '4.23428565', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 46, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1629, 27, 'MXN', '17.81116400', '18.08240000', '18.35363600', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1630, 11, 'NZD', '1.46734465', '1.48969000', '1.51203535', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 54, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1631, 19, 'NOK', '8.47884060', '8.60796000', '8.73707940', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1632, 31, 'PHP', '46.87122500', '47.58500000', '48.29877500', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1633, 14, 'PLN', '3.89311400', '3.95240000', '4.01168600', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 54, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1634, 5, 'GBP', '0.69621179', '0.70681400', '0.71741621', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1635, 0, 'QAR', '3.58717300', '3.64180000', '3.69642700', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 39, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1636, 29, 'RUB', '74.12144700', '75.25020000', '76.37895300', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 52, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1637, 22, 'SAR', '3.69399625', '3.75025000', '3.80650375', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 41, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1638, 12, 'SGD', '1.37809380', '1.39908000', '1.42006620', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1639, 15, 'SEK', '8.36997840', '8.49744000', '8.62490160', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1640, 13, 'CHF', '0.98426224', '0.99925100', '1.01423977', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1641, 26, 'THB', '35.20193000', '35.73800000', '36.27407000', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1642, 23, 'AED', '3.61785575', '3.67295000', '3.72804425', '2016-02-23 08:30:00', '2016-02-24 08:30:00', 39, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1643, 32, 'ARS', '15.07483400', '15.30440000', '15.53396600', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1644, 7, 'AUD', '1.36863780', '1.38948000', '1.41032220', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 53, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1645, 33, 'BRL', '3.89759575', '3.95695000', '4.01630425', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 55, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1646, 20, 'BND', '1.38567830', '1.40678000', '1.42788170', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 50, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1647, 8, 'CAD', '1.35892570', '1.37962000', '1.40031430', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1648, 34, 'CLP', '685.31375000', '695.75000000', '706.18625000', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 54, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1649, 3, 'CNY', '6.42869115', '6.52659000', '6.62448885', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 51, '2016-02-24 15:25:22');  "),
    $i+=1 => array("insert" => " (1650, 16, 'CZK', '24.16638400', '24.53440000', '24.90241600', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 55, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1651, 17, 'DKK', '6.67314845', '6.77477000', '6.87639155', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 54, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1652, 24, 'EGP', '7.70768410', '7.82506000', '7.94243590', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 44, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1653, 6, 'EUR', '0.89419581', '0.90781300', '0.92143020', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 51, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1654, 9, 'HKD', '7.65362730', '7.77018000', '7.88673270', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 51, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1655, 18, 'HUF', '275.33803500', '279.53100000', '283.72396500', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 54, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1656, 35, 'IDR', '13191.88830000', '13392.78000000', '13593.67170000', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 49, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1657, 28, 'ILS', '3.85253200', '3.91120000', '3.96986800', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 54, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1658, 10, 'JPY', '110.30621000', '111.98600000', '113.66579000', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 52, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1659, 25, 'JOD', '0.69792175', '0.70855000', '0.71917825', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 43, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1660, 21, 'KWD', '0.29555615', '0.30005700', '0.30455786', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 46, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1661, 4, 'MYR', '4.15719250', '4.22050000', '4.28380750', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 47, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1662, 27, 'MXN', '17.93803200', '18.21120000', '18.48436800', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 51, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1663, 11, 'NZD', '1.48555730', '1.50818000', '1.53080270', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 55, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1664, 19, 'NOK', '8.48018020', '8.60932000', '8.73845980', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 54, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1665, 31, 'PHP', '46.94047050', '47.65530000', '48.37012950', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 51, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1666, 14, 'PLN', '3.92154110', '3.98126000', '4.04097890', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 55, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1667, 5, 'GBP', '0.70326538', '0.71397500', '0.72468463', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 54, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1668, 0, 'QAR', '3.58697600', '3.64160000', '3.69622400', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 40, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1669, 29, 'RUB', '74.96253850', '76.10410000', '77.24566150', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 53, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1670, 22, 'SAR', '3.69360225', '3.74985000', '3.80609775', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 42, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1671, 12, 'SGD', '1.38567830', '1.40678000', '1.42788170', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 51, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1672, 15, 'SEK', '8.36346755', '8.49083000', '8.61819245', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 54, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1673, 13, 'CHF', '0.97691414', '0.99179100', '1.00666787', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 51, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1674, 26, 'THB', '35.19966450', '35.73570000', '36.27173550', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 52, '2016-02-24 15:25:23');  "),
    $i+=1 => array("insert" => " (1675, 23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2016-02-24 08:30:00', '2999-12-31 23:59:59', 40, '2016-02-24 15:25:23');  "),
);

insert_new_records(TABLE_CURRENCIES_HISTORY, 'id', $currencies_history_insert_sql, $DBTables, " (`id`, `currencies_id`, `code`, `buy_value`, `spot_value`, `sell_value`, `date_from`, `date_to`, `version`, `last_modified`)");
?>