<?php
$rating_rules_tags_insert_sql = array(
    '1' => array("insert" => " (1, 1); "),
    '2' => array("insert" => " (2, 1); "),
    '3' => array("insert" => " (3, 1); "),
    '4' => array("insert" => " (4, 1); "),
    '5' => array("insert" => " (5, 1); ")
);

insert_new_records(TABLE_RATING_RULES_TAGS, 'rule_id', $rating_rules_tags_insert_sql, $DBTables, "(`rule_id`, `tag_id`)");

$featured_banners = array(
    8=> array( //  tag id
        'key' => 'DIRECT_TOP_UP',
        'lang' => array(
            1 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/dtu_en_id_20160205.png',
                'cid' => 23276,
            ),
            
            4 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/dtu_en_id_20160205.png',
                'cid' => 23276,
            ),
        )
    ),
    
    6=> array( //  tag id
        'key' => 'GIFT_CARDS',
        'lang' => array(
            2 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/gift_card_cn_20160205.png',
                'href' => 'http://www.offgamers.com/blog/itunes-gift-card-covered-regions/',
            ),
            
            4 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/gift_card_id_20160205.png',
                'createURL' => array(
                    'search/index', array('keyword' => 'battlenet')
                ),
            ),
        )
    ),
    
    7=> array( //  tag id
        'key' => 'GAME_CARD',
        'lang' => array(
            1 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/game_cards_en_id_20160205.png',
                'cid' => 4130,
               
            ),
            4 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/game_cards_en_id_20160205.png',
                'cid' => 4130,
            ),
        )
    ),
    
     5=> array( //  tag id
        'key' => 'PC_GAMES',
        'lang' => array(
            1 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/pc_games_en_cn_id_20160205.png',
                'href' => 'http://www.offgamers.com/blog/world-of-warcraft-legion-available-at-offgamers/',
            ),
            
            2 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/pc_games_en_cn_id_20160205.png',
                'href' => 'http://www.offgamers.com/blog/world-of-warcraft-legion-available-at-offgamers/',
            ),
             4 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/pc_games_en_cn_id_20160205.png',
                'href' => 'http://www.offgamers.com/blog/world-of-warcraft-legion-available-at-offgamers/',
            ),
        )
    ),
    
    4 => array(//  tag id
        'key' => 'TOOLS',
        'lang' => array(
            2 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/tools_cn__20160205.png',
                'href' => 'https://www.offgamers.com/zh-CN/tools/vpn/battleping',
            ),
        )
    ),
    
    3 => array(//  tag id
        'key' => 'MOBILE',
        'lang' => array(
            1 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/mobile_en_20160205.png',
                'href' => 'http://www.offgamers.com/blog/recharge-your-mobile-phone-at-offgamers/',
            ),
            2 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/mobile_cn_20160205.png',
                'href' => 'http://www.offgamers.com/blog/recharge-your-mobile-phone-at-offgamers/',
            ),
            4 => array(
                'src' => '//d130xiciw9h9wz.cloudfront.net/infolink/mobile_id_20160205.png',
                'href' => 'http://www.offgamers.com/blog/recharge-your-mobile-phone-at-offgamers/',
            ),
        )
    ),
);

foreach ($featured_banners as $tag_id => $banner_info) {
    foreach ($banner_info['lang'] as $lang_id => $lang_banner_info) {
        $json_str = array(
            'src' => $lang_banner_info['src'],
            'href' => isset($lang_banner_info['href']) ? $lang_banner_info['href'] : '',
        );

        if (isset($lang_banner_info['cid'])) {
            $json_str['createURL'] = array(
                'category/index', array('cid' => $lang_banner_info['cid'])
            );
        } else if (isset($lang_banner_info['createURL'])) {
            $json_str['createURL'] = $lang_banner_info['createURL'];
        }
        
        $update_sql = " UPDATE categories_tag_description 
                            SET tag_featured_banner = '" . json_encode($json_str) . "' 
                        WHERE tag_id = '" . $tag_id . "'
                            AND language_id = '" . $lang_id . "'";
        tep_db_query($update_sql);
    }
}
?>