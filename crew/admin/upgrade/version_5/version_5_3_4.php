<?php

$add_new_tables = array();

$add_new_tables["products_cost"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `products_cost` (
  `products_id` int(11) NOT NULL,
  `products_model` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
  `products_cost` decimal(15,6) NOT NULL,
  `products_currency` char(3) COLLATE utf8_unicode_ci NOT NULL,
  `products_last_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`products_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);


$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='categories.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["CATALOG_EDIT_COST"] = array("insert" => " ('CATALOG_EDIT_COST', 'Edit Cost', " . $row_sql["admin_files_id"] . ",'1', 51)");

    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}

$add_new_tables["log_delivered_products"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `log_delivered_products` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `orders_id` int(11) unsigned NOT NULL,
  `orders_products_id` int(11) unsigned NOT NULL,
  `products_id` int(11) unsigned NOT NULL,
  `products_quantity` int(2) unsigned NOT NULL,
  `extra_info` text COLLATE utf8_unicode_ci NOT NULL,
  `log_date_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

$existing_orders_top_up_fields = get_table_fields(TABLE_ORDERS_TOP_UP);

if (!in_array('currency_code', $existing_orders_top_up_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_ORDERS_TOP_UP . " ADD `currency_code` CHAR(3) NOT NULL  AFTER `publishers_ref_id`;");
}

if (!in_array('currency_settle_amount', $existing_orders_top_up_fields)) {
    tep_db_query("ALTER TABLE  " . TABLE_ORDERS_TOP_UP . " ADD  `currency_settle_amount` DECIMAL( 15,6) NOT NULL AFTER  `currency_code`;");
}
?>