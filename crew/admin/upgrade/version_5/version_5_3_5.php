<?php
$add_new_tables = array();
// Create new table
$add_new_tables["bitpay"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `bitpay` (
                    `order_id` int(11) unsigned NOT NULL,
                    `invoice_id` varchar(40) DEFAULT NULL,
                    `status` varchar(32) DEFAULT NULL,
                    `pos_data` varchar(255) DEFAULT NULL,
                    `btc_price` varchar(15) DEFAULT NULL,
                    `price` decimal(15,2) DEFAULT NULL,
                    `currency` char(3) NOT NULL,
                    `invoice_time` bigint(15) unsigned DEFAULT NULL,
                    `expiration_time` bigint(15) unsigned DEFAULT NULL,
                    `currently_time` bigint(15) unsigned DEFAULT NULL,
                    `btc_paid` varchar(15) DEFAULT NULL,
                    `btc_due` varchar(15) DEFAULT NULL,
                    `rate` decimal(10,2) DEFAULT NULL,
                    `exception_status` varchar(15) DEFAULT NULL,
                    `guid` varchar(50) DEFAULT NULL,
                    `payment_urls` text DEFAULT NULL,
                    `invoice_data` text DEFAULT NULL,
                    `notification_data` text DEFAULT NULL,
                    `date_added` datetime DEFAULT NULL,
                    `date_modified` datetime DEFAULT NULL,
                    PRIMARY KEY (`order_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["bitpay_status_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `bitpay_status_history` (
                    `status_history_id` int(11) unsigned NOT NULL auto_increment,
                    `order_id` int(11) unsigned NOT NULL,
                    `status` varchar(32) DEFAULT NULL,
                    `reason` varchar(255) DEFAULT NULL,
                    `date` datetime NOT NULL,
                    `changed_by` varchar(128) DEFAULT NULL,
                    PRIMARY KEY (`status_history_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
?>