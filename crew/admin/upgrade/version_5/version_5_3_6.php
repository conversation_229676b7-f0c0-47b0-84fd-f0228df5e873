<?php
tep_db_query("TRUNCATE TABLE " . TABLE_LOG_DELIVERED_PRODUCTS);

$sql = "SELECT o.orders_id, 
              op.orders_products_id, op.products_quantity, op.products_good_delivered_quantity, 
              op.products_id, op.final_price, op.products_model,
              opei.orders_products_extra_info_value,
              oss.first_date
        FROM orders AS o
        INNER JOIN orders_status_stat AS oss ON o.orders_id = oss.orders_id AND oss.orders_status_id =1
        INNER JOIN orders_products AS op ON o.orders_id = op.orders_id
        INNER JOIN orders_extra_info AS  oei ON (o.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'site_id' AND orders_extra_info_value = '0')
        INNER JOIN orders_products_extra_info AS opei  ON (opei.orders_products_id = op.orders_products_id AND opei.orders_products_extra_info_key = 'delivery_mode')
        AND op.parent_orders_products_id =0
        WHERE oss.first_date >=  '2016-01-01 00:00:00'
        AND o.orders_status = 3";
$result = tep_db_query($sql);
while ($orders_products_row = tep_db_fetch_array($result)) {
    $opid = $orders_products_row['orders_products_id'];
    $oid = $orders_products_row["orders_id"];

    $stock_select_sql = "   SELECT products_bundle
                            FROM " . TABLE_PRODUCTS . "
                            WHERE products_id = '" . (int) $orders_products_row["products_id"] . "'";
    $stock_result_sql = tep_db_query($stock_select_sql);
    if ($stock_row = tep_db_fetch_array($stock_result_sql)) {
        if ($stock_row['products_bundle'] == 'yes') {
            $total_package_values = temp_get_order_item_total_values($oid, $opid);
            $orders_products_info_select_sql = "    SELECT orders_products_id, products_id, products_quantity, orders_products_store_price
                                                    FROM " . TABLE_ORDERS_PRODUCTS . "
                                                    WHERE orders_id = '" . (int) $oid . "'
                                                        AND parent_orders_products_id = '" . (int) $opid . "'
                                                        AND orders_products_is_compensate = 0
                                                        AND products_good_delivered_quantity > 0";
            $orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
            $total_products_bundle = tep_db_num_rows($orders_products_info_result_sql);
            while ($orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql)) {
                $total_item_amount = $orders_products_info_row['orders_products_store_price'] * $orders_products_info_row["products_quantity"];
                $total_item_final_amount = ($total_item_amount / $total_package_values) * ($orders_products_row['final_price'] * $orders_products_row['products_quantity']);
                log_cdkey_delivered($oid, $orders_products_info_row['orders_products_id'], $orders_products_info_row['products_id'], $total_item_final_amount, $orders_products_row["products_quantity"], $orders_products_row);
            }
        } else if ($orders_products_row['products_good_delivered_quantity'] > 0) {
            $total_item_final_amount = $orders_products_row['final_price'] * $orders_products_row['products_good_delivered_quantity'];
            
            if ($orders_products_row['orders_products_extra_info_value'] == '6') {
                $products_cost_select_sql = "   SELECT products_cost, products_currency
                                                FROM " . TABLE_PRODUCTS_COST . "
                                                WHERE products_id = '" . $orders_products_row['products_id'] . "'";
                $products_cost_result_sql = tep_db_query($products_cost_select_sql);
                if ($products_cost_row = tep_db_fetch_array($products_cost_result_sql)) {
                    $product_cost = $products_cost_row['products_cost'];
                    $products_currency = $products_cost_row['products_currency'];
                }

                $orders_top_up_data_sql = array(
                    'currency_settle_amount' => isset($product_cost) ? $product_cost : 0,
                    'currency_code' => isset($products_currency) ? $products_currency : '',
                );
                tep_db_perform(TABLE_ORDERS_TOP_UP, $orders_top_up_data_sql, 'update', " orders_products_id = '" . $opid . "'  AND  currency_code =  '' ");

                $topup_select_sql = "   SELECT top_up_id
                                        FROM orders_top_up
                                        WHERE orders_products_id = '" . $opid . "' AND top_up_status=3";
                $delivered_products_cd_key_array = array();
                $delivered_softpin_qty = 0;
                $topup_result_sql = tep_db_query($topup_select_sql);
                if ($topup_result = tep_db_fetch_array($topup_result_sql)) {
                    $delivered_softpin_qty ++;
                    $delivered_products_cd_key_array['DTU']['top_up_id'][] = $topup_result['top_up_id'];
                }

                if ($delivered_products_cd_key_array) {
                    $delivered_products_cd_key_array['final_price'] = $total_item_final_amount;

                    $log_data = array(
                        'orders_id' => $orders_products_row['orders_id'],
                        'orders_products_id' => $opid,
                        'products_id' => $orders_products_row['products_id'],
                        'products_quantity' => $delivered_softpin_qty,
                        'extra_info' => json_encode($delivered_products_cd_key_array),
                        'log_date_time' => $orders_products_row['first_date']
                    );

                    tep_db_perform('log_delivered_products', $log_data);
                }
            } else {
                log_cdkey_delivered($oid, $opid, $orders_products_row['products_id'], $total_item_final_amount, $orders_products_row["products_good_delivered_quantity"], $orders_products_row);
            }
        }
    }
    
}

function temp_get_order_item_total_values($order_id, $orders_products_id) {
    $total = 0;
    if (!tep_not_null($orders_products_id)) {
        return $total;
    }

    $orders_products_info_select_sql = "	SELECT orders_products_id, orders_products_store_price, products_quantity
                                                FROM " . TABLE_ORDERS_PRODUCTS . "
                                                WHERE orders_id = '" . (int) $order_id . "'
                                                        AND parent_orders_products_id = '" . (int) $orders_products_id . "'
                                                        AND orders_products_is_compensate = 0";
    $orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
    while ($orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql)) {
        $total = $total + ($orders_products_info_row['products_quantity'] * $orders_products_info_row['orders_products_store_price']);
    }
    return tep_round($total, 6);
}

function log_cdkey_delivered($order_id, $orders_products_id, $products_id, $total_item_amount, $process_purchase_quantity, $orders_products_row) {
    if ($orders_products_id) {
        $delivered_products_cd_key_array = array();
        $delivered_softpin_qty = 0;

        $cdkey_confirmed_delivered_select_sql = "   SELECT custom_products_code_id,purchase_orders_id, remarks
                                                    FROM custom_products_code
                                                    WHERE orders_products_id = '" . $orders_products_id . "'";
        $cdkey_confirmed_delivered_result_sql = tep_db_query($cdkey_confirmed_delivered_select_sql);
        while ($cdkey_confirm_delivered = tep_db_fetch_array($cdkey_confirmed_delivered_result_sql)) {
            if (strpos($cdkey_confirm_delivered['remarks'], '_API') === false) {
                $delivered_softpin_qty ++;
                $delivered_products_cd_key_array['SOFTPIN']['po_id'][$cdkey_confirm_delivered['purchase_orders_id']]['cdkey_id'][] = $cdkey_confirm_delivered['custom_products_code_id'];
            } else if (strpos($cdkey_confirm_delivered['remarks'], '_API')) {
                $delivered_softpin_qty ++;
                $delivered_products_cd_key_array['API']['custom_products_code_id'][] = $cdkey_confirm_delivered['custom_products_code_id'];
            }
        }

        if ($delivered_products_cd_key_array) {
            $delivered_products_cd_key_array['final_price'] = $total_item_amount > 0 ? $total_item_amount / $process_purchase_quantity : 0;

            $log_data = array(
                'orders_id' => $order_id,
                'orders_products_id' => $orders_products_id,
                'products_id' => $products_id,
                'products_quantity' => $delivered_softpin_qty,
                'extra_info' => json_encode($delivered_products_cd_key_array),
                'log_date_time' => $orders_products_row['first_date']
            );
            
            tep_db_perform(TABLE_LOG_DELIVERED_PRODUCTS, $log_data);
        }
    }
}
?>