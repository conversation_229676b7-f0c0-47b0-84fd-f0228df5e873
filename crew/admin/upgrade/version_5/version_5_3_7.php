<?php

$select_sql = "	SELECT code FROM " . TABLE_CURRENCIES_HISTORY . " WHERE version = 1 AND last_modified = '2016-02-24 15:24:32'" ;
$result_sql = tep_db_query($select_sql);
if (tep_db_num_rows($result_sql)) {
    $code = array();
    
    while ($row = tep_db_fetch_array($result_sql)) {
        $code[] = $row['code'];
    }
    
    tep_db_query("UPDATE " . TABLE_CURRENCIES_HISTORY . " SET version=version+1 WHERE code IN ('" . implode("','", $code) . "')");

    $insert_sql = "INSERT INTO `" . TABLE_CURRENCIES_HISTORY . "` (`currencies_id`, `code`, `buy_value`, `spot_value`, `sell_value`, `date_from`, `date_to`, `version`, `last_modified`) VALUES 
        (32, 'ARS', '12.75318900', '12.94740000', '13.14161100', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (7, 'AUD', '1.35211935', '1.37271000', '1.39330065', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (33, 'BRL', '3.90126980', '3.96068000', '4.02009020', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (20, 'BND', '1.39337115', '1.41459000', '1.43580885', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (8, 'CAD', '1.36666780', '1.38748000', '1.40829220', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (34, 'CLP', '698.85750000', '709.50000000', '720.14250000', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (3, 'CNY', '6.39293565', '6.49029000', '6.58764435', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (16, 'CZK', '24.34397950', '24.71470000', '25.08542050', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (17, 'DKK', '6.72152180', '6.82388000', '6.92623820', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (2, 'AUG', '0.00000000', '0.03835673', '0.03835673', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (24, 'EGP', '7.70762500', '7.82500000', '7.94237500', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (6, 'EUR', '0.90077856', '0.91449600', '0.92821344', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (9, 'HKD', '7.63474485', '7.75101000', '7.86727515', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (18, 'HUF', '282.27933000', '286.57800000', '290.87667000', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (35, 'IDR', '13560.16995000', '13766.67000000', '13973.17005000', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (28, 'ILS', '3.84399205', '3.90253000', '3.96106795', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (10, 'JPY', '118.64916000', '120.45600000', '122.26284000', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (25, 'JOD', '0.69910375', '0.70975000', '0.72039625', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (21, 'KWD', '0.29894750', '0.30350000', '0.30805250', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (4, 'MYR', '4.23540150', '4.29990000', '4.36439850', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (27, 'MXN', '17.12136850', '17.38210000', '17.64283150', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (11, 'NZD', '1.43885845', '1.46077000', '1.48268155', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (19, 'NOK', '8.65275220', '8.78452000', '8.91628780', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (31, 'PHP', '45.72448800', '46.42080000', '47.11711200', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (14, 'PLN', '3.83616130', '3.89458000', '3.95299870', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (5, 'GBP', '0.66469475', '0.67481700', '0.68493926', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (29, 'RUB', '72.13726300', '73.23580000', '74.33433700', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (22, 'SAR', '3.69734525', '3.75365000', '3.80995475', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (12, 'SGD', '1.39337115', '1.41459000', '1.43580885', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (15, 'SEK', '8.28366285', '8.40981000', '8.53595715', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (13, 'CHF', '0.97291898', '0.98773500', '1.00255103', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (26, 'THB', '35.53397350', '36.07510000', '36.61622650', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (23, 'AED', '3.61790500', '3.67300000', '3.72809500', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18'),
        (1, 'USD', '1.00000000', '1.00000000', '1.00000000', '2015-12-31 08:30:00', '2016-01-01 08:30:00', 1, '2016-05-26 10:12:18')
    ";
    tep_db_query($insert_sql);
}
?>