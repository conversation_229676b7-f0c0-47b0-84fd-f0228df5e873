<?php
$add_new_tables = array();
// Create new table
$add_new_tables["paymaster24"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `paymaster24` (
                    `order_id` int(11) unsigned NOT NULL,
                    `payment_id` varchar(40) DEFAULT NULL,
                    `payment_date` datetime DEFAULT NULL,
                    `amount` decimal(15,2) DEFAULT NULL,
                    `currency` char(3) NOT NULL,
                    `paid_amount` decimal(15,2) DEFAULT NULL,
                    `paid_currency` varchar(15) DEFAULT NULL,
                    `payment_system` varchar(15) DEFAULT NULL,
                    `payer_identifier` varchar(40) DEFAULT NULL,
                    `payer_country` char(2) DEFAULT NULL,
                    `payer_ip` varchar(15) DEFAULT NULL,
                    `data` text DEFAULT NULL,
                    `date_added` datetime DEFAULT NULL,
                    `date_modified` datetime DEFAULT NULL,
                    PRIMARY KEY (`order_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["paymaster24_status_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `paymaster24_status_history` (
                    `status_history_id` int(11) unsigned NOT NULL auto_increment,
                    `order_id` int(11) unsigned NOT NULL,
                    `status` varchar(32) DEFAULT NULL,
                    `reason` varchar(255) DEFAULT NULL,
                    `date` datetime NOT NULL,
                    `changed_by` varchar(128) DEFAULT NULL,
                    PRIMARY KEY (`status_history_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
?>