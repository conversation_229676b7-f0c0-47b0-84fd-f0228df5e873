<?php

$add_new_tables = array();

$add_new_tables["customers_groups_purchase_control"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `customers_groups_purchase_control` (
                    `products_id` int(11) NOT NULL,
                    `customers_groups_id` int(11) NOT NULL,
                    `purchase_limit` int(4) NOT NULL,
                    `out_of_stock_flag` tinyint(1) NOT NULL COMMENT '0: available, 1: out-of-stock',
                    `out_of_stock_datetime` datetime DEFAULT NULL,
                    PRIMARY KEY (`products_id`,`customers_groups_id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["out_of_stock_rules"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `out_of_stock_rules` (
                `rules_id` int(11) NOT NULL AUTO_INCREMENT,
                `rules_name` varchar(32) NOT NULL,
                `date_added` datetime NOT NULL,
                `last_modified` datetime NOT NULL,
                PRIMARY KEY (`rules_id`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["out_of_stock_customers_groups_rules"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `out_of_stock_customers_groups_rules` (
                `out_of_stock_rules_id` int(11) NOT NULL,
                `customers_groups_id` int(11) NOT NULL,
                `out_of_stock_in_percentage` decimal(8,4) NOT NULL,
                PRIMARY KEY (`out_of_stock_rules_id`,`customers_groups_id`)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
        
$add_new_tables["indomog_dtu_logs"] = array(
       "structure" => "CREATE TABLE IF NOT EXISTS `indomog_dtu_logs` (
                        `indomog_log_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                        `indomog_request_type` varchar(7) COLLATE utf8_unicode_ci NOT NULL,
                        `email_hp` varchar(96) COLLATE utf8_unicode_ci NOT NULL,
                        `indomog_qid` varchar(12) COLLATE utf8_unicode_ci DEFAULT NULL,
                        `indomog_verify_log_id` int(11) unsigned NOT NULL DEFAULT '0',
                        `indomog_topup_log_id` int(11) unsigned NOT NULL DEFAULT '0',
                        `created_datetime` datetime NOT NULL,
                        PRIMARY KEY (`indomog_log_id`)
                       ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);

$select_sql = "	SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . "
                WHERE admin_files_name='catalog.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["out_of_stock_rule.php"] = array("insert" => " ('out_of_stock_rule.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='out_of_stock_rule.php' AND admin_files_is_boxes=0 ");
}

$select_sql = "	SELECT admin_files_id, admin_groups_id 
                FROM " . TABLE_ADMIN_FILES . "
                WHERE TRIM(LCASE(admin_files_name))='categories.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["RECALCULATE_OUT_OF_STOCK"] = array("insert" => " ('RECALCULATE_OUT_OF_STOCK', 'Recalculate purchase quantity', " . $row_sql["admin_files_id"] . ",'1', 52)");
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
?>