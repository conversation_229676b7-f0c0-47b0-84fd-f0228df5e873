<?php

$add_new_tables = array();
// Create new table
$add_new_tables["c2c_invoice"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_invoice` (
                    `invoice_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                    `invoice_number` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
                    `invoice_type` varchar(16) COLLATE utf8_unicode_ci NOT NULL,
                    `orders_id` int(11) unsigned NOT NULL DEFAULT '0',
                    `invoice_file_domain` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
                    `invoice_file_path` varchar(128) COLLATE utf8_unicode_ci DEFAULT NULL,
                    `file_raw_data` text COLLATE utf8_unicode_ci NOT NULL COMMENT 'Data store in json format',
                    `created_datetime` datetime DEFAULT NULL,
                    `last_modified_datetime` datetime DEFAULT NULL,
                    <PERSON>IMARY KEY (`invoice_id`),
                    <PERSON><PERSON><PERSON> `index_invoice_number` (`invoice_number`),
                    <PERSON><PERSON>Y `index_orders_id` (`orders_id`),
                    KEY `index_date` (`created_datetime`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["c2c_invoice_running_number"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_invoice_running_number` (
                    `country_iso2` varchar(12) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'GENERAL',
                    `invoice_number` int(6) unsigned NOT NULL DEFAULT '1',
                    `invoice_month` int(1) unsigned NOT NULL DEFAULT '1',
                    `invoice_year` int(4) unsigned NOT NULL DEFAULT '2015',
                    PRIMARY KEY (`country_iso2`,`invoice_month`,`invoice_year`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["c2c_credit_note"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_credit_note` (
                    `cn_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                    `cn_number` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
                    `cn_type` varchar(16) COLLATE utf8_unicode_ci NOT NULL,
                    `orders_id` int(11) unsigned NOT NULL DEFAULT '0',
                    `invoice_number` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
                    `cn_file_domain` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
                    `cn_file_path` varchar(128) COLLATE utf8_unicode_ci DEFAULT NULL,
                    `file_raw_data` text COLLATE utf8_unicode_ci NOT NULL COMMENT 'Data store in json format',
                    `created_datetime` datetime DEFAULT NULL,
                    `last_modified_datetime` datetime DEFAULT NULL,
                    PRIMARY KEY (`cn_id`),
                    KEY `index_cn_number` (`cn_number`),
                    KEY `index_invoice_number` (`invoice_number`),
                    KEY `index_orders_id` (`orders_id`),
                    KEY `index_date` (`created_datetime`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["c2c_credit_note_running_number"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_credit_note_running_number` (
                    `country_iso2` varchar(12) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'GENERAL',
                    `cn_number` int(6) unsigned NOT NULL DEFAULT '1',
                    `cn_month` int(1) unsigned NOT NULL DEFAULT '1',
                    `cn_year` int(4) unsigned NOT NULL DEFAULT '2015',
                    PRIMARY KEY (`country_iso2`,`cn_month`,`cn_year`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
?>