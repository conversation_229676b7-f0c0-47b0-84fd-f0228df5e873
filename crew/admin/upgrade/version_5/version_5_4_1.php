<?php

$existing_frontend_template_fields = get_table_fields('frontend_template');

if (!in_array('af_notice_enable_status', $existing_frontend_template_fields)) {
    tep_db_query("ALTER TABLE frontend_template ADD `af_notice_enable_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '`0: Disabled, 1: Enabled with hide option, 2: Enabled without hide option`';");
    tep_db_query("UPDATE frontend_template SET af_notice_enable_status=1 WHERE id_type IN (0, 2)");
}

$existing_indomog_dtu_logs_fields = get_table_fields('indomog_dtu_logs');
if (!in_array('products_id', $existing_indomog_dtu_logs_fields)) {
    tep_db_query("ALTER TABLE indomog_dtu_logs ADD `products_id` int(11) unsigned NOT NULL AFTER `indomog_log_id`;");
    tep_db_query("ALTER TABLE indomog_dtu_logs ADD `indomog_pid` varchar(255) NOT NULL AFTER `email_hp`;");
}
?>
