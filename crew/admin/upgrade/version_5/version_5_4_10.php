<?php
$add_new_tables = array();
// Create new table
$add_new_tables["vtc"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `vtc` (
                    `order_id` int(11) unsigned NOT NULL,
                    `website_id` int(4) DEFAULT NULL,
                    `amount` decimal(15,2) NOT NULL,
                    `currency` char(3) NOT NULL,
                    `status` varchar(5) DEFAULT NULL,
                    `tran_ref_no` varchar(12) DEFAULT NULL,
                    `method` varchar(50) DEFAULT NULL,
                    `message` varchar(255) DEFAULT NULL,
                    `receiver_acc` VARCHAR (15) DEFAULT NULL,
                    `sign` varchar(125) DEFAULT NULL,
                    `data` text DEFAULT NULL,
                    `date_added` datetime DEFAULT NULL,
                    `date_modified` datetime DEFAULT NULL,
                    PRIMARY KEY (`order_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["vtc_status_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `vtc_status_history` (
                    `status_history_id` int(11) unsigned NOT NULL auto_increment,
                    `order_id` int(11) unsigned NOT NULL,
                    `status_code` varchar(5) DEFAULT NULL,
                    `status_message` varchar(127) DEFAULT NULL,
                    `date` datetime NOT NULL,
                    `changed_by` varchar(128) DEFAULT NULL,
                    PRIMARY KEY (`status_history_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
?>
