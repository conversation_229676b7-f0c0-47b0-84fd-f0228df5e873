<?php

$add_new_tables = array();
// Create new table
$add_new_tables["log_released_products"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `log_released_products` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `orders_id` int(11) unsigned NOT NULL,
                    `orders_products_id` int(11) unsigned NOT NULL,
                    `products_id` int(11) unsigned NOT NULL,
                    `custom_products_code_id` int(11) unsigned NOT NULL,
                    `log_date_time` datetime NOT NULL,
                    `log_delivered_released_status` BOOLEAN NOT NULL DEFAULT FALSE,
                    PRIMARY KEY (`id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["log_refunded_products"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `log_refunded_products` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `orders_id` int(11) unsigned NOT NULL,
                    `orders_products_id` int(11) unsigned NOT NULL,
                    `products_id` int(11) unsigned NOT NULL,
                    `products_canceled_quantity` int(2) unsigned NOT NULL,
                    `refund_type` varchar(32) NOT NULL,
                    `refund_id` int(11) unsigned NOT NULL,
                    `log_date_time` datetime NOT NULL,
                    PRIMARY KEY (`id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["log_delivered_released"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `log_delivered_released` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `orders_id` int(11) unsigned NOT NULL,
                    `orders_products_id` int(11) unsigned NOT NULL,
                    `products_id` int(11) unsigned NOT NULL,
                    `released_quantity` int(2) unsigned NOT NULL,
                    `extra_info` text COLLATE utf8_unicode_ci NOT NULL,
                    `log_date_time` datetime NOT NULL,
                    PRIMARY KEY (`id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

// Indexing
add_index_key ('log_released_products', 'index_custom_products_code_id', 'index', 'custom_products_code_id', $DBTables);
add_index_key ('log_released_products', 'index_orders_id', 'index', 'orders_id', $DBTables);
//add_index_key ('log_refunded_products', 'index_orders_id', 'index', 'orders_id', $DBTables);

// Process data migration for release process.
// Start orders_id: 7362898
$orders_id = 7362898;

// Start migrating released data to log_released_producst
tep_db_query("TRUNCATE TABLE " . TABLE_LOG_RELEASED_PRODUCTS);

$ldp_sql = "SELECT H.orders_id, H.date_added, H.comments, "
        . "O.orders_products_id, O.products_id "
        . "FROM " . TABLE_ORDERS_STATUS_HISTORY . " as H "
        . "LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " as O "
        . "ON O.orders_id = H.orders_id "
        . "WHERE H.comments LIKE 'release%' "
        . "AND H.orders_id IN (SELECT orders_id FROM log_delivered_products GROUP BY orders_id) "
        . "AND H.orders_id >=  " . $orders_id . " "
        . "ORDER BY H.orders_id ASC";
$result_ldp = tep_db_query($ldp_sql);
if (tep_db_num_rows($result_ldp)) {
    while ($row_ldp = tep_db_fetch_array($result_ldp)) {
        $log_data = array(
            'orders_id' => $row_ldp['orders_id'],
            'orders_products_id' => $row_ldp['orders_products_id'],
            'products_id' => $row_ldp['products_id'],
            'custom_products_code_id' => get_numerics($row_ldp['comments']),
            'log_date_time' => $row_ldp['date_added'],
            'log_delivered_released_status' => 1
        );

        tep_db_perform(TABLE_LOG_RELEASED_PRODUCTS, $log_data);
    }
}

// Start compiling data for log_delivered_released (for cron GP report - released)
tep_db_query("TRUNCATE TABLE " . TABLE_LOG_DELIVERED_RELEASED);

$sql = "SELECT DISTINCT o.orders_id, op.orders_products_id, op.products_quantity, "
        . "op.products_good_delivered_quantity as pgd_quantity, op.products_id, "
        . "op.final_price, opei.orders_products_extra_info_value as opei_value, oss.first_date, "
        . "(SELECT p.products_bundle FROM products AS p WHERE p.products_id = op.products_id) AS p_bundle "
        . "FROM " . TABLE_ORDERS . " AS o "
        . "INNER JOIN " . TABLE_LOG_DELIVERED_PRODUCTS . " AS ldp "
        . "ON o.orders_id = ldp.orders_id "
        . "INNER JOIN " . TABLE_ORDERS_STATUS_STAT . " AS oss "
        . "ON o.orders_id = oss.orders_id "
        . "AND oss.orders_status_id = 1 "
        . "INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op "
        . "ON o.orders_id = op.orders_id "
        . "INNER JOIN " . TABLE_ORDERS_EXTRA_INFO . " AS oei "
        . "ON (o.orders_id = oei.orders_id AND oei.orders_extra_info_key = 'site_id' AND orders_extra_info_value = '0') "
        . "INNER JOIN " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " AS opei "
        . "ON (opei.orders_products_id = op.orders_products_id AND opei.orders_products_extra_info_key = 'delivery_mode') "
        . "AND op.parent_orders_products_id =0 "
        . "WHERE o.orders_id >= " . $orders_id . " "
        . "AND o.orders_status = 3";
$result = tep_db_query($sql);
while ($orders_products_row = tep_db_fetch_array($result)) {
    $opid = $orders_products_row['orders_products_id'];
    $oid = $orders_products_row["orders_id"];
    // Check if product is a bundle product?
    if ($orders_products_row['p_bundle'] == 'yes') {
        $total_package_values = temp_get_order_item_total_values($oid, $opid);
        $orders_products_info_select_sql = "SELECT orders_products_id, products_id, "
                . "products_quantity, orders_products_store_price "
                . "FROM " . TABLE_ORDERS_PRODUCTS . " "
                . "WHERE orders_id = '" . (int) $oid . "' "
                . "AND parent_orders_products_id = '" . (int) $opid . "' "
                . "AND orders_products_is_compensate = 0 "
                . "AND products_good_delivered_quantity > 0";
        $orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
        while ($orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql)) {
            $total_item_amount = $orders_products_info_row['orders_products_store_price'] * $orders_products_info_row["products_quantity"];
            $total_item_final_amount = ($total_item_amount / $total_package_values) * ($orders_products_row['final_price'] * $orders_products_row['products_quantity']);
            log_cdkey_delivered_released($oid, $orders_products_info_row['orders_products_id'], $orders_products_info_row['products_id'], $total_item_final_amount, $orders_products_row["products_quantity"], $orders_products_row['first_date']);
        }
    } 
    else if ($orders_products_row['pgd_quantity'] > 0) {
        $total_item_final_amount = $orders_products_row['final_price'] * $orders_products_row['pgd_quantity'];

        if ($orders_products_row['opei_value'] == '6') {
            // do nothing for DTU products
        } 
        else {
            log_cdkey_delivered_released($oid, $opid, $orders_products_row['products_id'], $total_item_final_amount, $orders_products_row["pgd_quantity"], $orders_products_row['first_date']);
        }
    }
}

function temp_get_order_item_total_values($order_id, $orders_products_id) {
    $total = 0;
    if (!tep_not_null($orders_products_id)) {
        return $total;
    }

    $orders_products_info_select_sql = "SELECT orders_products_id, "
            . "orders_products_store_price, products_quantity "
            . "FROM " . TABLE_ORDERS_PRODUCTS . " "
            . "WHERE orders_id = '" . (int) $order_id . "' "
            . "AND parent_orders_products_id = '" . (int) $orders_products_id . "' "
            . "AND orders_products_is_compensate = 0";
    $orders_products_info_result_sql = tep_db_query($orders_products_info_select_sql);
    while ($orders_products_info_row = tep_db_fetch_array($orders_products_info_result_sql)) {
        $total = $total + ($orders_products_info_row['products_quantity'] * $orders_products_info_row['orders_products_store_price']);
    }
    return tep_round($total, 6);
}

function log_cdkey_delivered_released($order_id, $orders_products_id, $products_id, $total_item_amount, $process_purchase_quantity, $first_date) {
    if ($orders_products_id) {
        $released_products_cd_key_array = array();
        $released_softpin_qty = 0;
        // Get all the released key
        $cdkey_released_sql = "SELECT lrp.custom_products_code_id, cpc.purchase_orders_id, cpc.remarks "
                . "FROM " . TABLE_LOG_RELEASED_PRODUCTS . " AS lrp "
                . "INNER JOIN " . TABLE_CUSTOM_PRODUCTS_CODE . " AS cpc "
                . "ON cpc.custom_products_code_id = lrp.custom_products_code_id "
                . "WHERE lrp.orders_id = " . $order_id . " "
                . "AND lrp.orders_products_id = " . $orders_products_id . " "
                . "AND lrp.products_id = " . $products_id;
        $cdkey_released_result = tep_db_query($cdkey_released_sql);
        while ($cdkey_released = tep_db_fetch_array($cdkey_released_result)) {
            if (strpos($cdkey_released['remarks'], '_API') === false) {
                $released_products_cd_key_array['SOFTPIN']['po_id'][$cdkey_released['purchase_orders_id']]['cdkey_id'][] = $cdkey_released['custom_products_code_id'];
                $released_softpin_qty ++;
            } else if (strpos($cdkey_released['remarks'], '_API')) {
                $released_products_cd_key_array['API']['custom_products_code_id'][] = $cdkey_released['custom_products_code_id'];
                $released_softpin_qty ++;
            }  
        }

        if ($released_products_cd_key_array) {
            $released_products_cd_key_array['final_price'] = $total_item_amount > 0 ? $total_item_amount / $process_purchase_quantity : 0;
            $log_data = array(
                'orders_id' => $order_id,
                'orders_products_id' => $orders_products_id,
                'products_id' => $products_id,
                'released_quantity' => $released_softpin_qty,
                'extra_info' => json_encode($released_products_cd_key_array),
                'log_date_time' => $first_date
            );
            tep_db_perform(TABLE_LOG_DELIVERED_RELEASED, $log_data);
        }
    }
}

function get_numerics($str) {
    $matches = array();
    preg_match_all('/\d+/', $str, $matches);
    return $matches[0][0];
}

//G2G config for auto cancel offline orders
$conf_insert_sql = array();
$conf_insert_sql["C2C_AUTO_CANCEL_PENDING_OFFLINE_PAYMENT_ORDER_IN_X_MINUTES"] = array("insert" => " ('Auto cancel pending offline order', 'C2C_AUTO_CANCEL_PENDING_OFFLINE_PAYMENT_ORDER_IN_X_MINUTES', '180', 'Auto Cancel Pending Offline Payment Order within X Minutes', 'C2C_BUYER_ORDER', 40, NULL, NOW(), NULL, '') ");

insert_new_records(TABLE_C2C_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");

# Wee Siong - Adding index below reduce overhead.
add_index_key ('orders_notification', 'index_customers_id', 'index', 'customers_id', $DBTables);
tep_db_query("ALTER TABLE `api_tm_fuzzy_device` ADD PRIMARY KEY ( `api_tm_query_id` )");
