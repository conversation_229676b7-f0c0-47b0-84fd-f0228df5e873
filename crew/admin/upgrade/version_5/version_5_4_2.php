<?php

$add_new_tables = array();

$select_sql = "	SELECT admin_files_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE admin_files_name='tools.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["aft_account_limit.php"] = array("insert" => " ('aft_account_limit.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='aft_account_limit.php' AND admin_files_is_boxes=0 ");
}

$add_new_tables["aft_account_create_limit"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `aft_account_create_limit` (
  `countries_id` int(11) unsigned NOT NULL,
  `aft_limit` int(11) unsigned NOT NULL,
  `aft_duration` int(11) unsigned NOT NULL,
  `last_call` datetime DEFAULT '0000-00-00 00:00:00',
  `next_call` datetime DEFAULT '0000-00-00 00:00:00',
  `last_call_total` int(11) unsigned NOT NULL DEFAULT '0',
  `created_date_time` datetime NOT NULL,
  `last_modified_date_time` datetime DEFAULT '0000-00-00 00:00:00',
  `cron_last_modified_date_time` datetime DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`countries_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");


$add_new_tables["aft_account_create_limit_log"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `aft_account_create_limit_log` (
  `log_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `countries_id` int(11) unsigned NOT NULL,
  `aft_limit` int(11) unsigned NOT NULL,
  `aft_duration` int(11) unsigned NOT NULL,
  `last_call_total` int(11) NOT NULL DEFAULT '0',
  `created_date_time` datetime NOT NULL,
  PRIMARY KEY (`log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);


$cron_progress_task_select_sql = "	SELECT cron_process_track_in_action 
									FROM cron_process_track 
									WHERE cron_process_track_filename = 'cron_aft_account_limit.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
    $cron_process_track_array = array('cron_process_track_in_action' => 0,
        'cron_process_track_start_date' => 'now()',
        'cron_process_track_failed_attempt' => 0,
        'cron_process_track_filename' => 'cron_aft_account_limit.php'
    );
    tep_db_perform('cron_process_track', $cron_process_track_array);
}

$select_sql = "	SELECT admin_files_id, admin_groups_id
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_AFT_ACCOUNT_WHITELIST"] = array("insert" => " ('ORDER_AFT_ACCOUNT_WHITELIST', 'Whitelist Customer Account', ".$row_sql["admin_files_id"].", '1', 53)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
