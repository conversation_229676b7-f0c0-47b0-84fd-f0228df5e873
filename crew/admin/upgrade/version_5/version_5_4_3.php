<?php
$existing_product_cost_fields = get_table_fields('products_cost');
if (!in_array('products_po_company_name', $existing_product_cost_fields)) {
    tep_db_query("ALTER TABLE products_cost ADD `products_cost_company_name` VARCHAR(32) NULL AFTER `products_currency`;");
  
}

// Add twilio to the service list
tep_db_query("UPDATE " . TABLE_CONFIGURATION . " SET set_function='tep_cfg_select_option(array(\'maxmind\', \'telesign\',\'neutrino\', \'twilio\'),' WHERE CONFIGURATION_KEY = 'TELEPHONE_VERIFICATION_SERVICES'");
?>