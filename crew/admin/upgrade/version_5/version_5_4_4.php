<?php
$add_new_tables = array();
// Create new table
$add_new_tables["public_bank"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `public_bank` (
                    `order_id` int(11) unsigned NOT NULL,
                    `payer_name` varchar(18) DEFAULT NULL,
                    `transaction_date_received` datetime DEFAULT NULL,
                    `transaction_date_returned` datetime DEFAULT NULL,
                    `amount` decimal(15,2) NOT NULL,
                    `currency` char(3) NOT NULL,
                    `confirmation_num` varchar(20) DEFAULT NULL,
                    `active` tinyint(1) DEFAULT NULL,
                    `md5` varchar(125) DEFAULT NULL,
                    `data` text DEFAULT NULL,
                    `date_added` datetime DEFAULT NULL,
                    `date_modified` datetime DEFAULT NULL,
                    PRIMARY KEY (`order_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["public_bank_status_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `public_bank_status_history` (
                    `status_history_id` int(11) unsigned NOT NULL auto_increment,
                    `order_id` int(11) unsigned NOT NULL,
                    `return_code` varchar(3) DEFAULT NULL,
                    `return_message` varchar(40) DEFAULT NULL,
                    `date` datetime NOT NULL,
                    `changed_by` varchar(128) DEFAULT NULL,
                    PRIMARY KEY (`status_history_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
?>