<?php
$conf_insert_sql = array();
$conf_insert_sql["C2C_AUTO_CANCEL_PENDING_ORDER_IN_X_MINUTES"] = array("insert" => " ('Auto cancel pending order', 'C2C_AUTO_CANCEL_PENDING_ORDER_IN_X_MINUTES', '30', 'Auto Cancel Pending Order within X Minutes', 'C2C_BUYER_ORDER', 40, NULL, NOW(), NULL, '') ");

insert_new_records(TABLE_C2C_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");

// Alter tbl:products_cost adding new column products_cost_company_code
$alter_table_arr = array(
    'products_cost' => array(
        array(
            'field_name' => 'products_cost_company_code',
            'field_attr' => 'VARCHAR(2) NULL',
            'add_after' => 'products_currency',
        )
    ),
);

add_field($alter_table_arr);

// Get products_cost_company_code from tbl:po_company and update
$pc_sql = "SELECT products_id, products_cost_company_name "
        . "FROM ".TABLE_PRODUCTS_COST . " "
        . "WHERE products_cost_company_code IS NULL";
$result_pc = tep_db_query($pc_sql);
if (tep_db_num_rows($result_pc)) {
    while ($row_pc = tep_db_fetch_array($result_pc)) {
        $company_sql = "SELECT po_company_code, po_company_name "
                        . "FROM ".TABLE_PO_COMPANY." "
                        . "WHERE po_company_name = '" . $row_pc['products_cost_company_name'] . "'";
        $result_company = tep_db_query($company_sql);
        if ($row_company = tep_db_fetch_array($result_company)) {
            $update_sql = "UPDATE " . TABLE_PRODUCTS_COST . " "
                    . "SET products_cost_company_code = '".$row_company['po_company_code']."' "
                    . "WHERE products_id = ".$row_pc['products_id'];
            tep_db_query($update_sql);
        }
    }
}
