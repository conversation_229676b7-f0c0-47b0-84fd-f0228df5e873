<?php
$add_new_tables = array();
// Create new table
$add_new_tables["pipwave_prediction"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `pipwave_prediction` (
                    `order_id` int(11) unsigned NOT NULL,
                    `result_label` varchar(18) DEFAULT NULL,
                    `result_score` decimal(11,8) DEFAULT NULL,
                    `version` VARCHAR(24) DEFAULT NULL,
                    `data` text DEFAULT NULL,
                    `date_added` datetime DEFAULT NULL,
                    `date_modified` datetime DEFAULT NULL,
                    PRIMARY KEY (`order_id`, `version`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);

// Update existing offline payment description
$update_sql = array();
$update_sql[TABLE_PAYMENT_METHODS_TYPES_DESCRIPTION] = array(
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'OFFLINE PAYMENTS (Manual Checking Needed) '",
        "where_str" => "payment_methods_types_id = 4 AND languages_id = 1"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '离线支付（需要手动检查）'",
        "where_str" => "payment_methods_types_id = 4 AND languages_id = 2"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '離線支付（需要手動檢查）'",
        "where_str" => "payment_methods_types_id = 4 AND languages_id = 3"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'Pembayaran Offline (Pemeriksaan secara manual diperlukan) '",
        "where_str" => "payment_methods_types_id = 4 AND languages_id = 4"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'OFFLINE PAYMENTS (Manual Checking Needed) '",
        "where_str" => "payment_methods_types_id = 7 AND languages_id = 1"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '离线支付（需要手动检查）'",
        "where_str" => "payment_methods_types_id = 7 AND languages_id = 2"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '離線支付（需要手動檢查）'",
        "where_str" => "payment_methods_types_id = 7 AND languages_id = 3"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'PEMBAYARAN OFFLINE (Pemeriksaan secara manual diperlukan) '",
        "where_str" => "payment_methods_types_id = 7 AND languages_id = 4"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'ATM / CDM / BANK TRANSFER '",
        "where_str" => "payment_methods_types_id = 8 AND languages_id = 1"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '自动提款机 / 现金存款机 / 银行汇款 '",
        "where_str" => "payment_methods_types_id = 8 AND languages_id = 2"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '自動提款機 / 現金存款機 / 銀行匯款 '",
        "where_str" => "payment_methods_types_id = 8 AND languages_id = 3"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'ATM / CDM / TRANSFER BANK '",
        "where_str" => "payment_methods_types_id = 8 AND languages_id = 4"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'ONLINE BANKING '",
        "where_str" => "payment_methods_types_id = 3 AND languages_id = 1"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '网上银行 '",
        "where_str" => "payment_methods_types_id = 3 AND languages_id = 2"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = '網上銀行 '",
        "where_str" => "payment_methods_types_id = 3 AND languages_id = 3"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'BANK OTOMATIS '",
        "where_str" => "payment_methods_types_id = 3 AND languages_id = 4"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'MOBILE PAYMENT '",
        "where_str" => "payment_methods_types_id = 10 AND languages_id = 1"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'PEMBAYARAN PONSEL '",
        "where_str" => "payment_methods_types_id = 10 AND languages_id = 4"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'DOMPET ELECTRIK ATAU PEMBAYARAN KARTU PRABAYAR '",
        "where_str" => "payment_methods_types_id = 2 AND languages_id = 4"
    ),
    array(
        "field_name" => "payment_methods_types_description",
        "update" => "payment_methods_types_description = 'KARTU KREDIT/DEBIT '",
        "where_str" => "payment_methods_types_id = 1 AND languages_id = 4"
    ),
);
advance_update_records($update_sql, $DBTables);

$select_sql = "	SELECT admin_files_id, admin_groups_id
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
    $admin_files_actions_insert_sql = array();

    $admin_files_actions_insert_sql["CALL_PIPWAVE_PREDICTION"] = array("insert" => " ('CALL_PIPWAVE_PREDICTION', 'Execute pipwave prediction API', ".$row_sql["admin_files_id"].", '1', 54)" );

    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
?>