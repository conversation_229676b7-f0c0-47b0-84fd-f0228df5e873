<?php

$products_description_fields = get_table_fields('products_description');
if (!in_array('products_alt_name', $products_description_fields)) {
    tep_db_query("ALTER TABLE products_description ADD products_alt_name VARCHAR(255) AFTER products_name;");
}

$po_suppliers_fields = get_table_fields('po_suppliers');
if (!in_array('po_supplier_company_code', $po_suppliers_fields)) {
    tep_db_query("ALTER TABLE po_suppliers ADD po_supplier_company_code VARCHAR(2) AFTER po_days_pay_wsc;");
}

// Insert new records into admin table
$admin_files_insert_sql = array();
$admin_files_insert_sql["popup_suppliers_term.php"] = array("insert" => " ('popup_suppliers_term.php', 0, 1, '1,28,30,31,45,57,60,64,67,69,72,73,79,80,82,83,88,90,91,92,93,94,95,96,97,98,100,101,102,104,108,110,111') ",
    "update" => " admin_files_is_boxes=0, admin_files_to_boxes=137, admin_groups_id='1,28,30,31,45,57,60,64,67,69,72,73,79,80,82,83,88,90,91,92,93,94,95,96,97,98,100,101,102,104,108,110,111' "
);
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='popup_suppliers_term.php' AND admin_files_is_boxes=0 ");
