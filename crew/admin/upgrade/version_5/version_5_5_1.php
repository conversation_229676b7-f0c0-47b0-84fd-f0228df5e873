<?php

// New admin files setup
$select_sql = "	SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='purchase_orders.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["popup_suppliers_term.php"] = array(
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}

// Update cdkey view permission
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='admin_members.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);

if ($row_sql = tep_db_fetch_array($result_sql)) {
	$admin_files_actions_insert_sql = array();
	$admin_files_actions_insert_sql["ADMIN_MANAGE_CDKEY_VIEW_LIMIT"] = array(
        "insert" => " ('ADMIN_MANAGE_CDKEY_VIEW_LIMIT', 'Manage Admin Daily CDKey View Limit', ".$row_sql["admin_files_id"].", '1', 25)" 
    );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
