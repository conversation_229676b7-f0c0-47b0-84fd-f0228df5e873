<?php

$add_new_tables = array();
// Create new table
$add_new_tables["pipwave_payment"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `pipwave_payment` (
                    `orders_id` int(11) unsigned NOT NULL,
                    `status` varchar(5) NOT NULL,
                    `api_key` varchar(255) NOT NULL,
                    `notification_id` varchar(32) NULL,
                    `notification_date` datetime NULL,
                    `pw_id` varchar(255) NOT NULL,
                    `amount` decimal(19,8) NOT NULL,
                    `tax_exempted_amount` decimal(19,8) NULL,
                    `processing_fee_amount` decimal(19,8) NULL,
                    `tax_amount` decimal(19,8) NULL,
                    `total_amount` decimal(19,8) NOT NULL,
                    `final_amount` decimal(19,8) NOT NULL,
                    `currency_code` varchar(3) NOT NULL,
                    `transaction_status` varchar(5) NOT NULL,
                    `type` varchar(32) NOT NULL,
                    `subscription_token` varchar(32) NULL,
                    `charge_index` int(3) NULL,
                    `payment_method_code` varchar(32) NULL,
                    `reversible_payment` tinyint(1) unsigned NULL,
                    `require_capture` tinyint(1) unsigned NULL,
                    `mobile_number` varchar(255) NULL,
                    `mobile_number_verification` tinyint(1) unsigned NULL,
                    `pg_status` varchar(5) NULL,
                    `pg_reason` varchar(255) NULL,
                    `pg_date` datetime NULL,
                    `pg_raw_data` text NULL,
                    `risk_management_data` text NULL,
                    `matched_rules` text NULL,
                    `pipwave_score` int(3) NULL,
                    `rules_action` varchar(16) NULL,
                    `extra_param1` varchar(255) NULL,
                    `extra_param2` varchar(255) NULL,
                    `extra_param3` varchar(255) NULL,
                    PRIMARY KEY (`orders_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["pipwave_payment_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `pipwave_payment_history` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `orders_id` int(11) unsigned NOT NULL,
                    `status` varchar(255) NULL,
                    `message` varchar(255) NULL,
                    `notification_date` datetime NULL,
                    `changed_by` varchar(255) NULL,
                    PRIMARY KEY (`id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["pipwave_payment_mapper"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `pipwave_payment_mapper` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `pm_id` int(11) unsigned NOT NULL,
                    `pipwave_payment_code` varchar(255) NOT NULL,
                    `pm_display_name` varchar(255) NOT NULL,
                    `pg_display_name` varchar(255) NOT NULL,
                    `pg_id` int(11) unsigned NOT NULL,
                    `pg_code` varchar(255) NOT NULL,
                    `is_rp` tinyint(1) NOT NULL,
                    PRIMARY KEY (`id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1;",
    //staging
    "data" => "");

$add_new_tables["pipwave_payment_surcharge_mapper"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `pipwave_payment_surcharge_mapper` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `customer_group_id` int(11) unsigned NOT NULL,
                    `pipwave_processing_group_id` varchar(255) NOT NULL,
                    PRIMARY KEY (`id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1;",
    //live
    "data" => " INSERT INTO `pipwave_payment_surcharge_mapper` (`customer_group_id`, `pipwave_processing_group_id`)
                VALUES
                    (1, '1'),
                    (2, '1'),
                    (12, '1'),
                    (3, '1'),
                    (4, '1'),
                    (5, '1'),
                    (15, '1'),
                    (6, '1'),
                    (8, '1'),
                    (16, '1'),
                    (18, '1'),
                    (20, '1'),
                    (21, '1'),
                    (22, '1'),
                    (23, '1'),
                    (24, '1'),
                    (25, '1'),
                    (26, '1'),
                    (27, '1'),
                    (28, '1'),
                    (29, '1'),
                    (30, '1'),
                    (13, '1'),
                    (9, '1'),
                    (19, '1');
                    ");
$add_new_tables["analysis_pg_info"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `analysis_pg_info` (
                    `orders_id` int(11) unsigned NOT NULL,
                    `info_key` varchar(128) NOT NULL,
                    `info_value` varchar(255) DEFAULT NULL,
                    `created_at` datetime NOT NULL,
                    PRIMARY KEY (`orders_id`,`info_key`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["analysis_credit_card_info"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `analysis_credit_card_info` (
                    `orders_id` int(11) unsigned NOT NULL,
                    `fomatted_pan` varchar(32) DEFAULT NULL,
                    `bin_number` varchar(6) DEFAULT NULL,
                    `card_summary` varchar(4) DEFAULT NULL,
                    `expiry` varchar(7) DEFAULT NULL,
                    `three_d_offered` varchar(5) DEFAULT NULL,
                    `three_d_result` varchar(11) DEFAULT NULL,
                    `issuer_country` char(2) DEFAULT NULL,
                    `created_at` datetime NOT NULL,
                    PRIMARY KEY (`orders_id`)
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);