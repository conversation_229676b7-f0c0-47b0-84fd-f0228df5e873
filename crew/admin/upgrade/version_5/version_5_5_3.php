<?php

$add_new_tables = array();
// Create new table
$add_new_tables["orders_top_up_withdrawal"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `orders_top_up_withdrawal` (
                    `orders_top_up_withdrawal_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `top_up_id` int(10) unsigned NOT NULL,
                    `orders_products_id` int(11) NOT NULL,
                    `publishers_id` int(10) unsigned NOT NULL,
                    `purchase_orders_id` int(11) unsigned NOT NULL default '0',
                    `top_up_withdrawal_ref_id` varchar(32) default NULL COMMENT 'Ref ID for Payment Withdrawal',
                    `top_up_withdrawal_status` enum('0','1','3','7','11') NOT NULL default '0' COMMENT 'Top-up withdrawal status, 0=No Withdrawal 1=Processing, 3=Approved, 7=Canceled, 11=Paid',
                    `top_up_withdrawal_temp_status` enum('0','1') NOT NULL default '0' COMMENT 'Top-up withdrawal temp status, 0=unchecked 1=checked',
                    `top_up_withdrawal_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `top_up_cb_status` enum('0','1','2','3') NOT NULL default '0' COMMENT 'Top-up Cash Back status, 0=Not set 1=Cash Back, 2=Debit Note, 3=DTU Issue',
                    `top_up_cb_temp_status` enum('0','1','2','3') NOT NULL default '0' COMMENT 'Top-up Cash Back temp status, 0=unchecked 1=checked',
                    `top_up_cb_deduction_po_id` int(11) unsigned NOT NULL default '0',
                    `top_up_cb_deduction_status` enum('0','1') NOT NULL default '0' COMMENT 'Top-up Cash Back deduction status, 0=Available for deduction 1=Deducted',
                    `top_up_cb_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Cash Back',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`orders_top_up_withdrawal_id`),
                    UNIQUE KEY (`top_up_id`),
                    UNIQUE KEY (`orders_products_id`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["orders_top_up_purchase_orders"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `orders_top_up_purchase_orders` (
                    `orders_top_up_purchase_orders_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `top_up_id` int(10) unsigned NOT NULL,
                    `purchase_orders_id` int(11) unsigned NOT NULL,
                    `purchase_orders_ref_id` varchar(32) default NULL COMMENT 'Ref ID for Payment Withdrawal',
                    `top_up_withdrawal_status` enum('0','1','3','7','11') NOT NULL default '0' COMMENT 'Top-up withdrawal status, 0=No Withdrawal 1=Processing, 3=Approved, 7=Canceled, 11=Paid',
                    `top_up_withdrawal_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`orders_top_up_purchase_orders_id`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["orders_top_up_cb"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `orders_top_up_cb` (
                    `orders_top_up_cb_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `top_up_id` int(10) unsigned NOT NULL,
                    `top_up_cb_status` enum('0','1','2','3') NOT NULL default '0' COMMENT 'Top-up Cash Back status, 0=Not set 1=Cash Back, 2=Debit Note, 3=DTU Issue',
                    `top_up_cb_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`orders_top_up_cb_id`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["orders_top_up_cb_purchase_orders"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `orders_top_up_cb_purchase_orders` (
                    `orders_top_up_cb_purchase_orders_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `top_up_id` int(10) unsigned NOT NULL,
                    `purchase_orders_id` int(11) unsigned NOT NULL,
                    `purchase_orders_ref_id` varchar(32) default NULL COMMENT 'Ref ID for Payment Withdrawal',
                    `top_up_cb_status` enum('0','1','2') NOT NULL default '0' COMMENT 'Top-up CB status, 0=No CB or DN 1=Charge Back, 2=Debit Note',
                    `top_up_cb_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`orders_top_up_cb_purchase_orders_id`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

// Alter tbl:purchase_orders
$alter_table_purchase_orders = array(
    'purchase_orders' => array(
        array(
            'field_name' => 'purchase_orders_type',
            'field_attr' => 'INT(5) NOT NULL DEFAULT 0',
            'add_after' => 'purchase_orders_status',
        )
    ),
);

add_field($alter_table_purchase_orders);

// Alter tbl:publishers
$alter_table_publishers = array(
    'publishers' => array(
        array(
            'field_name' => 'publishers_supplier_id',
            'field_attr' => 'INT(11) NULL DEFAULT NULL',
            'add_after' => 'publishers_remark',
        )
    ),
);

add_field($alter_table_publishers);

// Insert new records into admin_files table (for DTU)
$select_sql = "	SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='purchase_orders.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["dtu_payment.php"] = array(
        "insert" => " ('dtu_payment.php', 0, '".$row_sql['admin_files_id']."', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_insert_sql["printable_dtu_request.php"] = array(
        "insert" => " ('printable_dtu_request.php', 0, '".$row_sql['admin_files_id']."', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}

// Insert new records into admin_files_actions table (for dtu suppliers)
$select_sql = "	SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='dtu_payment.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["DTU_NEW_PO"] = array(
        "insert" => " ('DTU_NEW_PO', 'New DTU Payment Request', '2', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=2, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["DTU_PROCESS_PO"] = array(
        "insert" => " ('DTU_PROCESS_PO', 'Process DTU PO', '4', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=4, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["DTU_CANCEL_PENDING_RECEIVE"] = array(
        "insert" => " ('DTU_CANCEL_PENDING_RECEIVE', 'Cancel Pending Receive Stock', '5', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=5, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["DTU_VERIFY_PO"] = array(
        "insert" => " ('DTU_VERIFY_PO', 'Verify/unverify Completed Purchase Order', '7', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=7, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["DTU_MAKE_PAYMENT"] = array(
        "insert" => " ('DTU_MAKE_PAYMENT', 'Make Payment', '8', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=8, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["DTU_ADD_REMARK"] = array(
        "insert" => " ('DTU_ADD_REMARK', 'Add Remark', '10', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=10, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["DTU_VIEW_REMARK"] = array(
        "insert" => " ('DTU_VIEW_REMARK', 'View Remarks', '11', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=11, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["DTU_PO_SET_PAID_STATUS"] = array(
        "insert" => " ('DTU_PO_SET_PAID_STATUS', 'Update DTU Withdrawal Status', '12', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=12, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}
// End of insert new records into admin_files table (for dtu payment)

// Insert tbl:orders_top_up_withdrawal
$topup_date_cutoff = "2017-01-01 00:00:00";
$topup_qry = "SELECT top_up_id, orders_products_id, publishers_id, top_up_timestamp
              FROM " . TABLE_ORDERS_TOP_UP . "
              WHERE top_up_timestamp >= '" . $topup_date_cutoff . "'";
$topup_result = tep_db_query($topup_qry);
if (tep_db_num_rows($topup_result)) {
    while ($topup_row = tep_db_fetch_array($topup_result)) {
        $topup_withdrawal_qry = "SELECT orders_top_up_withdrawal_id
                                 FROM " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . "
                                 WHERE top_up_id = " . (int)$topup_row['top_up_id'];
        $topup_withdrawal_result = tep_db_query($topup_withdrawal_qry);
        if ($topup_withdrawal_row = tep_db_fetch_array($topup_withdrawal_result)) {
            // Do Nothing
        } else {
            $insert_sql = "INSERT INTO " . TABLE_ORDERS_TOP_UP_WITHDRAWAL . "
                           (top_up_id, orders_products_id, publishers_id) 
                           VALUES
                           ('" . $topup_row['top_up_id'] . "', '" . $topup_row['orders_products_id'] . "', '" . $topup_row['publishers_id'] . "')";
            tep_db_query($insert_sql);
        }
    }
}
