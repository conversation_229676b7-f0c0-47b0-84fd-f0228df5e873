<?php
// Insert new fields into `pipwave_payment` table
$add_new_field = array();
$add_new_field['pipwave_payment'] = array(array("field_name" => "txn_sub_status",
                                                "field_attr" => "varchar(64) NULL",
                                                "add_after" => 'transaction_status'
                                            )
);
add_field($add_new_field);

//staging
//$conf_insert_sql[292] = array("insert" => " (292, 'adyen.api.mc', 'MasterCard', 'Adyen', 291, 'adyen', 1) ");
//$conf_insert_sql[293] = array("insert" => " (293, 'adyen.api.visa', 'Visa', 'Adyen', 291, 'adyen', 1) ");
//$conf_insert_sql[151] = array("insert" => " (151, 'alipay.alipay', 'Alipay', 'Alipay', 150, 'alipay', 0) ");
//$conf_insert_sql[372] = array("insert" => " (372, 'bitpay.bitpay', 'BitPay', 'Bitcoin', 371, 'bitpay', 0) ");
//$conf_insert_sql[22] = array("insert" => " (22, 'cashu.cashu', 'CashU', 'CashU', 21, 'cashU', 0) ");
//$conf_insert_sql[224] = array("insert" => " (224, 'indomog.indomog_account', 'INDOMOG Account (E-Wallet)', 'INDOMOG', 215, 'indomog', 0) ");
//$conf_insert_sql[113] = array("insert" => " (113, 'maybank.m2u', 'M2U', 'Maybank', 112, 'maybank', 0) ");
//$conf_insert_sql[45] = array("insert" => " (45, 'moneybooker.skrill', 'Moneybookers E-Wallet', 'Skrill', 44, 'moneybookers', 0) ");
//$conf_insert_sql[300] = array("insert" => " (300, 'paypal.ec', 'PayPal EC', 'PayPal', 298, 'paypalEC', 1) ");
//$conf_insert_sql[305] = array("insert" => " (305, 'paypal.cc', 'PayPal CC', 'PayPal', 298, 'paypalEC', 1) ");
//$conf_insert_sql[376] = array("insert" => " (376, 'pbbank.pbbank', 'PBe Bank', 'Public Bank', 375, 'pbbank', 0) ");

//live
$conf_insert_sql = array();
//adyen
$conf_insert_sql[314] = array("insert" => " (314, 'adyen.api.mc', 'MasterCard', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[315] = array("insert" => " (314, 'adyen.api.visa', 'Visa', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[316] = array("insert" => " (316, 'adyen.api.maestro', 'Maestro', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[320] = array("insert" => " (320, 'adyen.api.maestrouk', 'Maestro UK', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[335] = array("insert" => " (335, 'adyen.api.amex', 'American Express', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[329] = array("insert" => " (329, 'adyen.api.unionpay', 'China Union Pay', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[336] = array("insert" => " (336, 'adyen.api.diners', 'Diners Club', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[334] = array("insert" => " (334, 'adyen.api.discover', 'Discover', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[337] = array("insert" => " (337, 'adyen.api.jcb', 'JCB', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[428] = array("insert" => " (428, 'adyen.hpp.dragonpay_ebanking', 'Dragonpay Online Banking', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[330] = array("insert" => " (330, 'adyen.hpp.ebanking_FI', 'Finland e-Banking', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[331] = array("insert" => " (331, 'adyen.hpp.ideal', 'iDEAL', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[333] = array("insert" => " (333, 'adyen.hpp.payshop', 'Payshop', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[332] = array("insert" => " (332, 'adyen.hpp.safetypay', 'SafetyPay', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[340] = array("insert" => " (340, 'adyen.hpp.bankTransfer_AT', 'Bank Transfer AT', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[347] = array("insert" => " (347, 'adyen.hpp.bankTransfer_BE', 'Bank Transfer BE', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[339] = array("insert" => " (339, 'adyen.hpp.bankTransfer_DE', 'Bank Transfer DE', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[341] = array("insert" => " (341, 'adyen.hpp.bankTransfer_DK', 'Bank Transfer DK', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[344] = array("insert" => " (344, 'adyen.hpp.bankTransfer_ES', 'Bank Transfer ES', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[342] = array("insert" => " (342, 'adyen.hpp.bankTransfer_FI', 'Bank Transfer FI', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[348] = array("insert" => " (348, 'adyen.hpp.bankTransfer_FR', 'Bank Transfer FR', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[343] = array("insert" => " (343, 'adyen.hpp.bankTransfer_IE', 'Bank Transfer IE', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[338] = array("insert" => " (338, 'adyen.hpp.bankTransfer_NL', 'Bank Transfer NL', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[345] = array("insert" => " (345, 'adyen.hpp.bankTransfer_NO', 'Bank Transfer NO', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[346] = array("insert" => " (346, 'adyen.hpp.bankTransfer_SE', 'Bank Transfer SE', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[349] = array("insert" => " (349, 'adyen.hpp.bankTransfer_GB', 'Bank Transfer UK', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[426] = array("insert" => " (426, 'adyen.hpp.dragonpay_otc_banking', 'Dragonpay ATM Payments', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[429] = array("insert" => " (429, 'adyen.hpp.dragonpay_otc_non_banking', 'Dragonpay OTC', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[350] = array("insert" => " (350, 'adyen.hpp.bankTransfer_IBAN', 'SEPA Bank Transfer Europe', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[427] = array("insert" => " (427, 'adyen.hpp.dragonpay_gcash', 'Dragonpay Globe GCASH', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[478] = array("insert" => " (478, 'adyen.hpp.wallet_RU', 'Moneta Wallet', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[473] = array("insert" => " (473, 'adyen.hpp.paysafecard', 'PaySafeCard', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[475] = array("insert" => " (475, 'adyen.hpp.qiwiwallet', 'QIWI Wallet', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[489] = array("insert" => " (489, 'adyen.hpp.giropay', 'GiroPay', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[491] = array("insert" => " (491, 'adyen.hpp.online_RU', 'Moneta Online Banking', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[490] = array("insert" => " (490, 'adyen.hpp.terminal_RU', 'Moneta Cash Terminal', 'Adyen', 313, 'adyen', 0) ");
$conf_insert_sql[477] = array("insert" => " (477, 'adyen.hpp.directEbanking', 'SofortBanking', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[476] = array("insert" => " (476, 'adyen.hpp.poli', 'POLi', 'Adyen', 313, 'adyen', 1) ");
$conf_insert_sql[425] = array("insert" => " (425, 'adyen.hpp.wechatpay', 'WeChat Pay', 'Adyen', 313, 'adyen', 0) ");

//alipay
$conf_insert_sql[151] = array("insert" => " (151, 'alipay.alipay', 'Alipay', 'Alipay', 150, 'alipay', 0) ");

//bitpay
$conf_insert_sql[419] = array("insert" => " (419, 'bitpay.bitpay', 'Bitcoin', 'BitPay', 418, 'bitpay', 0) ");

//cashU
$conf_insert_sql[22] = array("insert" => " (22, 'cashu.cashu', 'CashU', 'CashU', 21, 'cashU', 0) ");

//cimb
$conf_insert_sql[143] = array("insert" => " (143, 'cimb.cimbclicks', 'CIMB Clicks', 'CIMB', 142, 'cimb', 0) ");

//coda
$conf_insert_sql[417] = array("insert" => " (417, 'coda.doku', 'Doku Wallet', 'CodaPayment', 297, 'coda', 0) ");
$conf_insert_sql[383] = array("insert" => " (383, 'coda.alfamart', 'Alfamart', 'CodaPayment', 297, 'coda', 0) ");
$conf_insert_sql[371] = array("insert" => " (371, 'coda.otc', 'Counter Payment, Online Banking & ATM', 'CodaPayment', 297, 'coda', 0) ");
$conf_insert_sql[424] = array("insert" => " (424, 'coda.celcom', 'Celcom', 'CodaPayment', 297, 'coda', 0) ");
$conf_insert_sql[423] = array("insert" => " (423, 'coda.digi', 'Digi', 'CodaPayment', 297, 'coda', 0) ");
$conf_insert_sql[415] = array("insert" => " (415, 'coda.h3i', 'H3I', 'CodaPayment', 297, 'coda', 0) ");
$conf_insert_sql[321] = array("insert" => " (321, 'coda.m1', 'M1', 'CodaPayment', 297, 'coda', 0) ");
$conf_insert_sql[298] = array("insert" => " (298, 'coda.singtel', 'Singtel', 'CodaPayment', 297, 'coda', 0) ");
$conf_insert_sql[370] = array("insert" => " (370, 'coda.starhub', 'Starhub', 'CodaPayment', 297, 'coda', 0) ");
$conf_insert_sql[416] = array("insert" => " (416, 'coda.xl', 'XL', 'CodaPayment', 297, 'coda', 0) ");

//indomog
$conf_insert_sql[266] = array("insert" => " (266, 'indomog.indomogwallet', 'INDOMOG Account (E-Wallet)', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[263] = array("insert" => " (263, 'indomog.kpybca', 'iBanking BCA KlikPay', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[265] = array("insert" => " (265, 'indomog.intman', 'iBanking Internet Banking Mandiri', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[262] = array("insert" => " (262, 'indomog.klkbca', 'iBanking KlikBCA', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[255] = array("insert" => " (255, 'indomog.bcatrf', 'Bank Central Asia (BCA)', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[258] = array("insert" => " (258, 'indomog.biitrf', 'Bank Internasional Indonesia (BII)', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[256] = array("insert" => " (256, 'indomog.mantrf', 'Bank Mandiri', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[260] = array("insert" => " (260, 'indomog.bnitrf', 'Bank Negara Indonesia (BNI)', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[257] = array("insert" => " (257, 'indomog.bpvtrf', 'Bank Permata', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[259] = array("insert" => " (259, 'indomog.britrf', 'Bank Rakyat Indonesia (BRI)', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[261] = array("insert" => " (261, 'indomog.bsmtrf', 'Bank Syariah Mandiri', 'INDOMOG', 253, 'indomog', 0) ");
$conf_insert_sql[264] = array("insert" => " (264, 'indomog.smsman', 'mBanking Mobile Banking Mandiri', 'INDOMOG', 253, 'indomog', 0) ");

//ipay88
$conf_insert_sql[111] = array("insert" => " (111, 'ipay88.paysbuy', 'PaySbuy', 'iPay88', 27, 'iPay88', 0) ");
$conf_insert_sql[34] = array("insert" => " (34, 'ipay88.fpx', 'FPX', 'iPay88', 27, 'iPay88', 0) ");
$conf_insert_sql[33] = array("insert" => " (33, 'ipay88.hongleongbank', 'Hong Leong Bank', 'iPay88', 27, 'iPay88', 0) ");

//maybank
$conf_insert_sql[113] = array("insert" => " (113, 'maybank.m2u', 'M2U', 'Maybank', 112, 'maybank', 0) ");

//skrill
$conf_insert_sql[471] = array("insert" => " (471, 'moneybooker.neteller', 'NETELLER', 'Skrill', 44, 'moneybookers', 0) ");
$conf_insert_sql[469] = array("insert" => " (469, 'moneybooker.astropayonline', 'Astropay Online Banking', 'Skrill', 44, 'moneybookers', 0) ");
$conf_insert_sql[493] = array("insert" => " (493, 'moneybooker.astropaycash', 'Astropay Cash Payment', 'Skrill', 44, 'moneybookers', 0) ");
$conf_insert_sql[292] = array("insert" => " (292, 'moneybooker.astropayoffline', 'Astropay Offline Banking', 'Skrill', 44, 'moneybookers', 0) ");
$conf_insert_sql[470] = array("insert" => " (470, 'moneybooker.rapidtransfer', 'Rapid Transfer', 'Skrill', 44, 'moneybookers', 0) ");
$conf_insert_sql[45] = array("insert" => " (45, 'moneybooker.skrill', 'Moneybookers E-Wallet', 'Skrill', 44, 'moneybookers', 1) ");

//onecard
$conf_insert_sql[368] = array("insert" => " (368, 'onecard.onecard', 'OneCard', 'OneCard', 367, 'onecardv2', 0) ");

//paypal
$conf_insert_sql[365] = array("insert" => " (365, 'paypal.ec', 'PayPal EC', 'PayPal', 354, 'paypalEC', 1) ");
$conf_insert_sql[364] = array("insert" => " (364, 'paypal.cc', 'PayPal EC (CC)', 'PayPal', 354, 'paypalEC', 1) ");

//public bank
$conf_insert_sql[422] = array("insert" => " (422, 'pbbank.pbe', 'PBe Bank', 'Public Bank', 421, 'pbbank', 0) ");

//rhb
$conf_insert_sql[182] = array("insert" => " (182, 'rhb.now', 'RHB Now', 'RHB', 180, 'rhb', 0) ");

//smart2pay
$conf_insert_sql[271] = array("insert" => " (271, 'smart2pay.dineromail', 'DineroMail', 'Smart2Pay', 268, 'smart2pay_globalpay', 1) ");
$conf_insert_sql[273] = array("insert" => " (273, 'smart2pay.mercado', 'DineroMail', 'Smart2Pay', 268, 'smart2pay_globalpay', 1) ");
$conf_insert_sql[281] = array("insert" => " (281, 'smart2pay.moneta', 'Moneta Wallet', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[292] = array("insert" => " (292, 'smart2pay.psc', 'PaySafeCard', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[278] = array("insert" => " (278, 'smart2pay.paysera', 'PaySera', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[296] = array("insert" => " (296, 'smart2pay.qiwi', 'QIWI Wallet', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[280] = array("insert" => " (280, 'smart2pay.toditocash', 'Todito Cash', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[295] = array("insert" => " (295, 'smart2pay.yandexmoney', 'Yandex.Money', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[323] = array("insert" => " (323, 'smart2pay.eps', 'Austrian EPS', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[272] = array("insert" => " (272, 'smart2pay.bancodobrasil', 'Débito Banco do Brasil', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[413] = array("insert" => " (413, 'smart2pay.enets', 'eNETS Debit (Singapore)', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[324] = array("insert" => " (324, 'smart2pay.giropay', 'GiroPay', 'Smart2Pay', 268, 'smart2pay_globalpay', 1) ");
$conf_insert_sql[269] = array("insert" => " (269, 'smart2pay.mrcash', 'MrCash (BanContract)', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[325] = array("insert" => " (325, 'smart2pay.mybank', 'MyBank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[326] = array("insert" => " (326, 'smart2pay.poli', 'POLi', 'Smart2Pay', 268, 'smart2pay_globalpay', 1) ");
$conf_insert_sql[283] = array("insert" => " (283, 'smart2pay.sibs', 'SIBS (MultiBanco)', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[327] = array("insert" => " (327, 'smart2pay.sofort', 'SofortBanking', 'Smart2Pay', 268, 'smart2pay_globalpay', 1) ");
$conf_insert_sql[287] = array("insert" => " (287, 'smart2pay.tenpay', 'Tenpay', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[275] = array("insert" => " (275, 'smart2pay.transferencia', 'Transferencia Entre Contas (Bradesco)', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[293] = array("insert" => " (293, 'smart2pay.trustly', 'Trustly', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[270] = array("insert" => " (270, 'smart2pay.trustpay', 'TrustPay', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");
$conf_insert_sql[385] = array("insert" => " (385, 'smart2pay.beeline', 'Beeline', 'Smart2Pay', 268, 'smart2pay_globalpay', 0) ");

//webmoney
$conf_insert_sql[24] = array("insert" => " (24, 'webmoney.webmoney', 'Webmoney', 'Webmoney', 23, 'webmoney', 0) ");

//worldpay
$conf_insert_sql[304] = array("insert" => " (304, 'worldpay.neosurf', 'NeoSurf', 'WorldPay', 64, 'bibit', 0) ");
$conf_insert_sql[308] = array("insert" => " (308, 'worldpay.ticketsurf', 'Ticketsurf Premium', 'WorldPay', 64, 'bibit', 0) ");
$conf_insert_sql[91] = array("insert" => " (91, 'worldpay.commerzbank', 'Commerzbank Online Banking', 'WorldPay', 64, 'bibit', 0) ");
$conf_insert_sql[93] = array("insert" => " (93, 'worldpay.dresdner', 'Dresdner Bank internetbanking', 'WorldPay', 64, 'bibit', 0) ");
$conf_insert_sql[68] = array("insert" => " (68, 'worldpay.enets', 'eNETS Debit (Singapore)', 'WorldPay', 64, 'bibit', 0) ");
$conf_insert_sql[299] = array("insert" => " (299, 'worldpay.euteller', 'EuTeller', 'WorldPay', 64, 'bibit', 1) ");
$conf_insert_sql[81] = array("insert" => " (81, 'worldpay.hansapank', 'Hansapank Bank Link', 'WorldPay', 64, 'bibit', 1) ");
$conf_insert_sql[74] = array("insert" => " (74, 'worldpay.paybox', 'Paybox', 'WorldPay', 64, 'bibit', 0) ");
$conf_insert_sql[303] = array("insert" => " (303, 'worldpay.pluspay', 'PlusPay', 'WorldPay', 64, 'bibit', 0) ");
$conf_insert_sql[307] = array("insert" => " (307, 'worldpay.sporopay', 'Sporopay', 'WorldPay', 64, 'bibit', 1) ");
$conf_insert_sql[290] = array("insert" => " (290, 'worldpay.boleto', 'Boleto Bancario', 'WorldPay', 64, 'bibit', 0) ");

//payu
$conf_insert_sql[397] = array("insert" => " (397, 'payu.br.elo', 'ELO', 'PayU LATAM', 396, 'payU', 1) ");
$conf_insert_sql[398] = array("insert" => " (398, 'payu.br.hipercard', 'Hipercard', 'PayU LATAM', 396, 'payU', 1) ");
$conf_insert_sql[405] = array("insert" => " (405, 'payu.mx.seven_eleven', '7-Eleven', 'PayU LATAM', 396, 'payU', 0) ");
$conf_insert_sql[401] = array("insert" => " (401, 'payu.mx.bancomer', 'Bancomer', 'PayU LATAM', 396, 'payU', 0) ");
$conf_insert_sql[403] = array("insert" => " (403, 'payu.mx.ixe', 'Ixe', 'PayU LATAM', 396, 'payU', 0) ");
$conf_insert_sql[404] = array("insert" => " (404, 'payu.mx.oxxo', 'Oxxo', 'PayU LATAM', 396, 'payU', 0) ");
$conf_insert_sql[400] = array("insert" => " (400, 'payu.mx.santander', 'Santander', 'PayU LATAM', 396, 'payU', 0) ");
$conf_insert_sql[402] = array("insert" => " (402, 'payu.mx.scotiabank', 'Scotiabank', 'PayU LATAM', 396, 'payU', 0) ");

//vtc
$conf_insert_sql[436] = array("insert" => " (436, 'vtc.vtcpaywallet', 'VTC Pay Wallet', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[463] = array("insert" => " (463, 'vtc.abbank', 'ABBANK', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[449] = array("insert" => " (449, 'vtc.acb', 'ACB', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[441] = array("insert" => " (441, 'vtc.agribank', 'Agribank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[461] = array("insert" => " (461, 'vtc.bacabank', 'BAC A BANK', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[465] = array("insert" => " (465, 'vtc.bvb', 'BaoViet Bank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[444] = array("insert" => " (444, 'vtc.bidv', 'BIDV', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[442] = array("insert" => " (442, 'vtc.dongabank', 'DongA Bank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[448] = array("insert" => " (448, 'vtc.Eximbank', 'Eximbank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[460] = array("insert" => " (460, 'vtc.GPBank', 'GP Bank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[450] = array("insert" => " (450, 'vtc.hdbank', 'HDBank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[467] = array("insert" => " (467, 'vtc.kienlongbank', 'KienLongBank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[464] = array("insert" => " (464, 'vtc.lvpb', 'LienVietPostBank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[447] = array("insert" => " (447, 'vtc.maritimebank', 'Maritime Bank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[439] = array("insert" => " (439, 'vtc.mbbank', 'MB Bank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[451] = array("insert" => " (451, 'vtc.namabank', 'Nam A Bank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[459] = array("insert" => " (459, 'vtc.navibank', 'NCB', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[462] = array("insert" => " (462, 'vtc.phuongdong', 'OCB', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[443] = array("insert" => " (443, 'vtc.oceanbank', 'Ocean Bank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[458] = array("insert" => " (458, 'vtc.pgbank', 'PG Bank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[453] = array("insert" => " (453, 'vtc.sacombank', 'Sacombank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[452] = array("insert" => " (452, 'vtc.saigonbank', 'SaigonBank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[466] = array("insert" => " (466, 'vtc.scbbank', 'SCB', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[457] = array("insert" => " (457, 'vtc.seabank', 'SeABank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[445] = array("insert" => " (445, 'vtc.shb', 'SHB', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[438] = array("insert" => " (438, 'vtc.techcombank', 'Techcombank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[456] = array("insert" => " (456, 'vtc.tienphongbank', 'TienPhongBank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[446] = array("insert" => " (446, 'vtc.vib', 'VIB', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[454] = array("insert" => " (454, 'vtc.vietabank', 'Viet A Bank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[437] = array("insert" => " (437, 'vtc.vietcombank', 'Vietcombank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[440] = array("insert" => " (440, 'vtc.vietinbank', 'Vietinbank', 'VTC', 435, 'vtc', 0) ");
$conf_insert_sql[455] = array("insert" => " (455, 'vtc.vpbank', 'VPBank', 'VTC', 435, 'vtc', 0) ");

//offline
$conf_insert_sql[220] = array("insert" => " (220, 'offline_my_ambank.ambank', 'AmBank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[214] = array("insert" => " (214, 'offline_my_cimb.cimbmy', 'CIMB Bank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[430] = array("insert" => " (430, 'offline_my_hsbc.hsbcmy', 'G2G HSBC Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[431] = array("insert" => " (431, 'offline_sg_hsbc.hsbcsg', 'G2G HSBC Bank (SG) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[432] = array("insert" => " (432, 'offline_my_maybank.maybank', 'G2G Maybank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[433] = array("insert" => " (433, 'offline_my_pbbank.publicbank', 'G2G Public Bank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[251] = array("insert" => " (251, 'offline_my_hlb.hongleongbank', 'Hong Leong Bank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[222] = array("insert" => " (222, 'offline_my_hsbc.hsbcmy', 'HSBC Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[223] = array("insert" => " (223, 'offline_sg_hsbc.hsbcsg', 'HSBC Bank (SG) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[186] = array("insert" => " (186, 'offline_my_maybank.maybank', 'Maybank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[14] = array("insert" => " (14, 'offline_my_moneygram.moneygram', 'MoneyGram', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[243] = array("insert" => " (243, 'offline_my_ocbc.ocbcmy', 'OCBC Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[248] = array("insert" => " (248, 'offline_sg_ocbc.ocbcsg', 'OCBC Bank (SG) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[187] = array("insert" => " (187, 'offline_my_pbbank.publicbank', 'Public Bank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[213] = array("insert" => " (213, 'offline_my_rhb.rhb', 'RHB Bank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[241] = array("insert" => " (241, 'offline_my_stanchart.standardcharteredmy', 'Standard Chartered Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[317] = array("insert" => " (317, 'offline_my_uob.uobmy', 'United Overseas Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[246] = array("insert" => " (246, 'offline_sg_uob.uobsg', 'United Overseas Bank Limited (SG) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[15] = array("insert" => " (15, 'offline_my_westernunion.westernunion', 'Western Union', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[16] = array("insert" => " (16, 'offline_my_wiretransfer.wiretransfer', 'Wire Transfer', 'Offline', 12, 'offline', 0) ");
$conf_insert_sql[468] = array("insert" => " (468, 'offline_sg_wiretransfer.wiretransfer', 'Wire Transfer SG', 'Offline', 12, 'offline', 0) ");

insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`)");
?>