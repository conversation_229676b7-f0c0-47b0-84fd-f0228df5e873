<?php

//custom product => custom product child
$custom_product = array(
    0 => array(1),
    1 => array(2),
    4 => array(5),
    5 => array(16, 17, 18, 19),
);

$c2c_product_margin_fields = get_table_fields(TABLE_C2C_PRODUCT_MARGIN);
if (!in_array('custom_products_type_child_id', $c2c_product_margin_fields)) {
    $add_new_field = array();
    $add_new_field[TABLE_C2C_PRODUCT_MARGIN] = array(array("field_name" => "custom_products_type_child_id",
            "field_attr" => " int(11) unsigned NOT NULL ",
            "add_after" => "custom_products_type_id"
        )
    );
    add_field($add_new_field, false);
}

//drop existing primary key
$existing_c2c_product_margin_fields = get_table_fields(TABLE_C2C_PRODUCT_MARGIN);
$primaryField = array('seller_group_id', 'game_id', 'custom_products_type_id');
if (count(array_intersect($existing_c2c_product_margin_fields, $primaryField)) == count($primaryField)) {
    drop_index_key(TABLE_C2C_PRODUCT_MARGIN, 'PRIMARY KEY', 'primary', $DBTables);
}
// End of drop existing primary key


$product_margin = array();

$prod_margin_sel_sql = "	SELECT seller_group_id, game_id, custom_products_type_id, payout_percentage
                            FROM " . TABLE_C2C_PRODUCT_MARGIN;
$prod_margin_res_sql = tep_db_query($prod_margin_sel_sql);
while ($prod_margin_row = tep_db_fetch_array($prod_margin_res_sql)) {
    if (isset($custom_product[$prod_margin_row['custom_products_type_id']])) {
        $record = 1;
        foreach ($custom_product[$prod_margin_row['custom_products_type_id']] as $child) {
            if ($record == 1) {
                //update record
                $update_sql = array();
                $update_sql[TABLE_C2C_PRODUCT_MARGIN] = array(
                    array(
                        "field_name" => "custom_products_type_child_id",
                        "update" => " custom_products_type_child_id = '" . $child . "'",
                        "where_str" => " seller_group_id = '" . $prod_margin_row['seller_group_id'] . "' AND game_id = '" . $prod_margin_row['game_id'] . "' AND custom_products_type_id = '" . $prod_margin_row['custom_products_type_id'] . "'"
                    )
                );
                advance_update_records($update_sql, $DBTables);
            } else {
                //clone and insert new record
                $product_margin_insert_sql = "INSERT INTO `" . TABLE_C2C_PRODUCT_MARGIN . "` (`seller_group_id`, `game_id`, `custom_products_type_id`, `custom_products_type_child_id`, `payout_percentage`) VALUES 
                                                ('" . $prod_margin_row['seller_group_id'] . "', '" . $prod_margin_row['game_id'] . "','" . $prod_margin_row['custom_products_type_id'] . "','" . $child . "', '" . $prod_margin_row['payout_percentage'] . "') ";
                tep_db_query($product_margin_insert_sql);
            }
            $record++;
        }
    }
}

//create primary key after udpate all custom_products_type_child_id 
$primaryField = array('seller_group_id', 'game_id', 'custom_products_type_child_id');
if (count(array_intersect($existing_c2c_product_margin_fields, $primaryField)) == count($primaryField)) {
    add_index_key(TABLE_C2C_PRODUCT_MARGIN, 'PRIMARY KEY', 'primary', 'seller_group_id, game_id, custom_products_type_child_id', $DBTables);
}

//==============================================//

$c2c_product_configuration_fields = get_table_fields(TABLE_C2C_PRODUCT_CONFIGURATION);
if (!in_array('custom_products_type_child_id', $c2c_product_configuration_fields)) {
    $add_new_field = array();
    $add_new_field[TABLE_C2C_PRODUCT_CONFIGURATION] = array(array("field_name" => "custom_products_type_child_id",
            "field_attr" => " int(11) unsigned NOT NULL ",
            "add_after" => "custom_products_type_id"
        )
    );
    add_field($add_new_field, false);
}

//drop existing primary key
$existing_c2c_product_configuration_fields = get_table_fields(TABLE_C2C_PRODUCT_CONFIGURATION);
$primaryField = array('game_id', 'custom_products_type_id');
if (count(array_intersect($existing_c2c_product_configuration_fields, $primaryField)) == count($primaryField)) {
    drop_index_key(TABLE_C2C_PRODUCT_CONFIGURATION, 'PRIMARY KEY', 'primary', $DBTables);
}
// End of drop existing primary key

$product_configuration = array();

$prod_config_sel_sql = "	SELECT game_id, custom_products_type_id, min_purchase_amount_usd, product_unit_name, product_max_level 
                            FROM " . TABLE_C2C_PRODUCT_CONFIGURATION;
$prod_config_res_sql = tep_db_query($prod_config_sel_sql);
while ($prod_config_row = tep_db_fetch_array($prod_config_res_sql)) {
    if (isset($custom_product[$prod_config_row['custom_products_type_id']])) {
        $record = 1;
        foreach ($custom_product[$prod_config_row['custom_products_type_id']] as $child) {
            if ($record == 1) {
                //update record
                $update_sql = array();
                $update_sql[TABLE_C2C_PRODUCT_CONFIGURATION] = array(
                    array(
                        "field_name" => "custom_products_type_child_id",
                        "update" => " custom_products_type_child_id = '" . $child . "'",
                        "where_str" => " game_id = '" . $prod_config_row['game_id'] . "' AND custom_products_type_id = '" . $prod_config_row['custom_products_type_id'] . "'"
                    )
                );
                advance_update_records($update_sql, $DBTables);
            } else {
                //clone and insert new record
                $product_configuration_insert_sql = "INSERT INTO `" . TABLE_C2C_PRODUCT_CONFIGURATION . "` (`game_id`, `custom_products_type_id`, `custom_products_type_child_id`, `min_purchase_amount_usd`, `product_unit_name`, `product_max_level`) VALUES 
                                                ('" . $prod_config_row['game_id'] . "','" . $prod_config_row['custom_products_type_id'] . "','" . $child . "', '" . $prod_config_row['min_purchase_amount_usd'] . "', '" . $prod_config_row['product_unit_name'] . "', '" . $prod_config_row['product_max_level'] . "') ";
                tep_db_query($product_configuration_insert_sql);
            }
            $record++;
        }
    }
}

//create primary key after udpate all custom_products_type_child_id 
$primaryField = array('game_id', 'custom_products_type_child_id');
if (count(array_intersect($existing_c2c_product_configuration_fields, $primaryField)) == count($primaryField)) {
    add_index_key(TABLE_C2C_PRODUCT_CONFIGURATION, 'PRIMARY KEY', 'primary', 'game_id, custom_products_type_child_id', $DBTables);
}

?>