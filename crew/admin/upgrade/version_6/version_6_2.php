<?php

// Insert new records into admin_files table (for Catalog Discount Page)
$select_sql = " SELECT admin_files_id
                FROM " . TABLE_ADMIN_FILES . "
                WHERE admin_files_name='catalog.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);

if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["categories_discount_group_rules.php"] = array("insert" => " ('categories_discount_group_rules.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='categories_discount_group_rules.php' AND admin_files_is_boxes=0 ");
}
// End of insert new records into admin_files table (for Catalog Discount Page)
$add_new_tables = array();
$add_new_tables["categories_discount_rules"] = array(
			"structure" => "CREATE TABLE IF NOT EXISTS `categories_discount_rules` (
                `cdrules_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `cdrules_title` varchar(255) NOT NULL,
                `cdrules_date_added` datetime DEFAULT NULL,
                `cdrules_last_modified` datetime DEFAULT NULL,
                PRIMARY KEY (`cdrules_id`)
             ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
			 "data" => "");
			 
$add_new_tables["categories_discount_group_rules"] = array(
			"structure" => "CREATE TABLE IF NOT EXISTS `categories_discount_group_rules` (
                `cdgr_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `cdrules_id`int(11) NOT NULL,
                `cdgr_customer_group_id`int(11) NOT NULL,
                `cdgr_discount`decimal(8,2) NULL,
                `cdgr_wor`decimal(8,2) NULL,
                `cdgr_updated_datetime` datetime DEFAULT NULL,
                PRIMARY KEY (`cdgr_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
			 "data" => "");
			 
$add_new_tables["categories_discount_list"] = array(
			"structure" => "CREATE TABLE IF NOT EXISTS `categories_discount_list` (
                `cdl_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `cdrules_id`int(11) NOT NULL,
                `cdl_category_id`int(11) NOT NULL,
                `cdl_datetime` datetime DEFAULT NULL,
                PRIMARY KEY (`cdl_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
			 "data" => "");

add_new_tables($add_new_tables, $DBTables);

//- add indexing for (cdrules_id, cdgr_customer_group_id) in categories_discount_group_rules
//- add indexing for cdl_category_id in categories_discount_list
add_index_key (TABLE_CATEGORY_DISCOUNT_GROUP_RULES, 'index_id_and_cdgr_customer_group_id', 'index', 'cdrules_id, cdgr_customer_group_id', $DBTables);
add_index_key (TABLE_CATEGORY_DISCOUNT_LIST, 'index_cdl_category_id', 'index', 'cdl_category_id', $DBTables);
?>