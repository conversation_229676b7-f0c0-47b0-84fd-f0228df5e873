<?php

// Insert new records into configuration table (for advertisement)
$select_sql = "	SELECT configuration_group_id 
                FROM " . TABLE_CONFIGURATION_GROUP . "
                WHERE configuration_group_title='Store Information'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$conf_insert_sql = array();
	$conf_insert_sql["CUSTOMER_SURVEY_LAST_DT"] = array("insert" => " ('Customer Survey Last Account created Date', 'CUSTOMER_SURVEY_LAST_DT', '2017-04-26', 'Customer Survey Last Account created Date', ".$row_sql["configuration_group_id"].", 100, NULL, now(), NULL, NULL)" );
	
	insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for advertisement)

$cron_progress_task_select_sql = "  SELECT cron_process_track_in_action 
                                    FROM cron_process_track 
                                    WHERE cron_process_track_filename = 'cron_customer_survey.php'";
$cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
if (!tep_db_num_rows($cron_progress_task_result_sql)) {
    $cron_process_track_array = array(
        'cron_process_track_in_action' => 0,
        'cron_process_track_start_date' => 'now()',
        'cron_process_track_failed_attempt' => 0,
        'cron_process_track_filename' => 'cron_customer_survey.php'
    );
    tep_db_perform('cron_process_track', $cron_process_track_array);
}

$add_new_tables = array();
// Create new table
$add_new_tables["customers_survey"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `customers_survey` (
                    `customers_id` int(11) NOT NULL,
                    `customer_notified` smallint(4) DEFAULT 0,
                    `survey_sent_datetime` datetime DEFAULT NULL,
                    `account_created_datetime` datetime DEFAULT NULL,
                    PRIMARY KEY (`customers_id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

add_index_key ('customers_survey', 'index_customer_notified', 'index', 'customer_notified', $DBTables);
?>
