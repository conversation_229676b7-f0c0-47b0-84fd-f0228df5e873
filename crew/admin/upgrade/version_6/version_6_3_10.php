<?php
//update wrong pipwave code
//payU
$conf_insert_sql = array();
$conf_insert_sql[397] = array("insert" => " (397, 'payu_br.elo', 'ELO', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 397 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[397] = array("insert" => " (397, 'payu_br.elo', 'ELO', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 397 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[398] = array("insert" => " (398, 'payu_br.hipercard', 'Hipercard', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 398 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[398] = array("insert" => " (398, 'payu_br.hipercard', 'Hipercard', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 398 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[405] = array("insert" => " (405, 'payu_mx.7eleven', '7-Eleven', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 405 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[405] = array("insert" => " (405, 'payu_mx.7eleven', '7-Eleven', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 405 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[401] = array("insert" => " (401, 'payu_mx.bancomer', 'Bancomer', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 401 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[401] = array("insert" => " (401, 'payu_mx.bancomer', 'Bancomer', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 401 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[403] = array("insert" => " (403, 'payu_mx.ixe', 'Ixe', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 403 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[403] = array("insert" => " (403, 'payu_mx.ixe', 'Ixe', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 403 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[404] = array("insert" => " (404, 'payu_mx.oxxo', 'Oxxo', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 404 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[404] = array("insert" => " (404, 'payu_mx.oxxo', 'Oxxo', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 404 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[400] = array("insert" => " (400, 'payu_mx.santander', 'Santander', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 400 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[400] = array("insert" => " (400, 'payu_mx.santander', 'Santander', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 400 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[402] = array("insert" => " (402, 'payu_mx.scotiabank', 'Scotiabank', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 402 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[402] = array("insert" => " (402, 'payu_mx.scotiabank', 'Scotiabank', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 402 AND site_id = 5");

//add new payU pm
$conf_insert_sql = array();
$conf_insert_sql[569] = array("insert" => " (569, 'payu_mx.spei', 'SPEI', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 569 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[569] = array("insert" => " (569, 'payu_mx.spei', 'SPEI', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 569 AND site_id = 5");

//turn payshop into rp
$conf_insert_sql = array();
$conf_insert_sql[333] = array("insert" => " (333, 'adyen.hpp.payshop', 'Payshop', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 333 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[333] = array("insert" => " (333, 'adyen.hpp.payshop', 'Payshop', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 333 AND site_id = 5");

//add new pg paymaster24
$conf_insert_sql = array();
$conf_insert_sql[571] = array("insert" => " (571, 'paymaster24.pay24', 'Paymaster24', 'Paymaster24', 570, 'paymaster24', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 571 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[571] = array("insert" => " (571, 'paymaster24.pay24', 'Paymaster24', 'Paymaster24', 313, 'paymaster24', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 571 AND site_id = 5");

//add adyen root payment
$conf_insert_sql = array();
$conf_insert_sql[565] = array("insert" => " (565, 'adyen.hpp.ebanking_FI::aktia', 'Finland e-Banking Aktia', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 565 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[565] = array("insert" => " (565, 'adyen.hpp.ebanking_FI::aktia', 'Finland e-Banking Aktia', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 565 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[561] = array("insert" => " (561, 'adyen.hpp.ebanking_FI::alandsbanken', 'Finland e-Banking Alandsbanken', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 561 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[561] = array("insert" => " (561, 'adyen.hpp.ebanking_FI::alandsbanken', 'Finland e-Banking Alandsbanken', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 561 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[560] = array("insert" => " (560, 'adyen.hpp.ebanking_FI::danskebank', 'Finland e-Banking Danske Bank', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 560 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[560] = array("insert" => " (560, 'adyen.hpp.ebanking_FI::danskebank', 'Finland e-Banking Danske Bank', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 560 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[562] = array("insert" => " (562, 'adyen.hpp.ebanking_FI::handelsbanken', 'Finland e-Banking Handelsbanken', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 562 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[562] = array("insert" => " (562, 'adyen.hpp.ebanking_FI::handelsbanken', 'Finland e-Banking Handelsbanken', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 562 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[551] = array("insert" => " (551, 'adyen.hpp.ebanking_FI::nordea', 'Finland e-Banking Nordea', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 551 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[551] = array("insert" => " (551, 'adyen.hpp.ebanking_FI::nordea', 'Finland e-Banking Nordea', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 551 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[568] = array("insert" => " (568, 'adyen.hpp.ebanking_FI::oma_saastopankki', 'Finland e-Banking Oma Saastopankki', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 568 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[568] = array("insert" => " (568, 'adyen.hpp.ebanking_FI::oma_saastopankki', 'Finland e-Banking Oma Saastopankki', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 568 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[563] = array("insert" => " (563, 'adyen.hpp.ebanking_FI::osuuspankki', 'Finland e-Banking OP-Pohjola', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 563 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[563] = array("insert" => " (563, 'adyen.hpp.ebanking_FI::osuuspankki', 'Finland e-Banking OP-Pohjola', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 563 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[566] = array("insert" => " (566, 'adyen.hpp.ebanking_FI::paikallisosuuspankit', 'Finland e-Banking POP Pankki', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 566 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[566] = array("insert" => " (566, 'adyen.hpp.ebanking_FI::paikallisosuuspankit', 'Finland e-Banking POP Pankki', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 566 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[564] = array("insert" => " (564, 'adyen.hpp.ebanking_FI::spankki', 'Finland e-Banking S-Pankki', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 564 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[564] = array("insert" => " (564, 'adyen.hpp.ebanking_FI::spankki', 'Finland e-Banking S-Pankki', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 564 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[567] = array("insert" => " (567, 'adyen.hpp.ebanking_FI::saastopankki', 'Finland e-Banking Saastopankki', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 567 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[567] = array("insert" => " (567, 'adyen.hpp.ebanking_FI::saastopankki', 'Finland e-Banking Saastopankki', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 567 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[510] = array("insert" => " (510, 'adyen.hpp.ideal::0031', 'iDEAL ABN Amro', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 510 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[510] = array("insert" => " (510, 'adyen.hpp.ideal::0031', 'iDEAL ABN Amro', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 510 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[507] = array("insert" => " (507, 'adyen.hpp.ideal::0761', 'iDEAL ASN Bank', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 507 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[507] = array("insert" => " (507, 'adyen.hpp.ideal::0761', 'iDEAL ASN Bank', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 507 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[513] = array("insert" => " (513, 'adyen.hpp.ideal::0802', 'iDEAL Bunq', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 513 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[513] = array("insert" => " (513, 'adyen.hpp.ideal::0802', 'iDEAL Bunq', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 513 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[505] = array("insert" => " (505, 'adyen.hpp.ideal::0721', 'iDEAL ING', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 505 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[505] = array("insert" => " (505, 'adyen.hpp.ideal::0721', 'iDEAL ING', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 505 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[514] = array("insert" => " (514, 'adyen.hpp.ideal::0801', 'iDEAL Knab', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 514 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[514] = array("insert" => " (514, 'adyen.hpp.ideal::0801', 'iDEAL Knab', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 514 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[511] = array("insert" => " (511, 'adyen.hpp.ideal::0021', 'iDEAL Rabobank', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 511 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[511] = array("insert" => " (511, 'adyen.hpp.ideal::0021', 'iDEAL Rabobank', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 511 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[509] = array("insert" => " (509, 'adyen.hpp.ideal::0771', 'iDEAL RegioBank', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 509 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[509] = array("insert" => " (509, 'adyen.hpp.ideal::0771', 'iDEAL RegioBank', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 509 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[508] = array("insert" => " (508, 'adyen.hpp.ideal::0751', 'iDEAL SNS Bank', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 508 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[508] = array("insert" => " (508, 'adyen.hpp.ideal::0751', 'iDEAL SNS Bank', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 508 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[506] = array("insert" => " (506, 'adyen.hpp.ideal::0511', 'iDEAL Triodos Bank', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 506 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[506] = array("insert" => " (506, 'adyen.hpp.ideal::0511', 'iDEAL Triodos Bank', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 506 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[512] = array("insert" => " (512, 'adyen.hpp.ideal::0161', 'iDEAL Van Lanschot', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 512 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[512] = array("insert" => " (512, 'adyen.hpp.ideal::0161', 'iDEAL Van Lanschot', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 512 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[556] = array("insert" => " (556, 'adyen.hpp.online_RU::alfaclick', 'Moneta - Alfa-Click', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 556 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[556] = array("insert" => " (556, 'adyen.hpp.online_RU::alfaclick', 'Moneta - Alfa-Click', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 556 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[557] = array("insert" => " (557, 'adyen.hpp.online_RU::faktura_ru', 'Moneta - Faktura.ru', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 557 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[557] = array("insert" => " (557, 'adyen.hpp.online_RU::faktura_ru', 'Moneta - Faktura.ru', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 557 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[558] = array("insert" => " (558, 'adyen.hpp.online_RU::promsvyazbank', 'Moneta - Internet-bank Promsvyazbank', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 558 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[558] = array("insert" => " (558, 'adyen.hpp.online_RU::promsvyazbank', 'Moneta - Internet-bank Promsvyazbank', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 558 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[559] = array("insert" => " (559, 'adyen.hpp.online_RU::rsb_ru', 'Moneta - Russian Standart internet-bank', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 559 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[559] = array("insert" => " (559, 'adyen.hpp.online_RU::rsb_ru', 'Moneta - Russian Standart internet-bank', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 559 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[549] = array("insert" => " (549, 'adyen.hpp.online_RU::sberbank', 'Moneta - Sberbank', 'Adyen', 313, 'adyen', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 549 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[549] = array("insert" => " (549, 'adyen.hpp.online_RU::sberbank', 'Moneta - Sberbank', 'Adyen', 313, 'adyen', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 549 AND site_id = 5");

//insert missing uob
$conf_insert_sql = array();
$conf_insert_sql[317] = array("insert" => " (317, 'offline_uob.uobmy', 'United Overseas Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 317 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[317] = array("insert" => " (317, 'offline_uob.uobmy', 'United Overseas Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 317 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[246] = array("insert" => " (246, 'offline_uob.uobsg', 'United Overseas Bank Limited (SG) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 246 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[246] = array("insert" => " (246, 'offline_uob.uobsg', 'United Overseas Bank Limited (SG) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 246 AND site_id = 5");

?>