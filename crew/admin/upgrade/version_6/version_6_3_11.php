<?php
//OG UPDATE !!
# delete record
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 397");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 398");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 405");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 401");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 403");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 404");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 400");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 402");

$conf_insert_sql = array();
$conf_insert_sql[397] = array("insert" => " (397, 'payu_br.elo', 'ELO', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 397 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[397] = array("insert" => " (397, 'payu_br.elo', 'ELO', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 397 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[398] = array("insert" => " (398, 'payu_br.hipercard', 'Hipercard', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 398 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[398] = array("insert" => " (398, 'payu_br.hipercard', 'Hipercard', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 398 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[405] = array("insert" => " (405, 'payu_mx.7eleven', '7-Eleven', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 405 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[405] = array("insert" => " (405, 'payu_mx.7eleven', '7-Eleven', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 405 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[401] = array("insert" => " (401, 'payu_mx.bancomer', 'Bancomer', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 401 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[401] = array("insert" => " (401, 'payu_mx.bancomer', 'Bancomer', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 401 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[403] = array("insert" => " (403, 'payu_mx.ixe', 'Ixe', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 403 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[403] = array("insert" => " (403, 'payu_mx.ixe', 'Ixe', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 403 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[404] = array("insert" => " (404, 'payu_mx.oxxo', 'Oxxo', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 404 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[404] = array("insert" => " (404, 'payu_mx.oxxo', 'Oxxo', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 404 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[400] = array("insert" => " (400, 'payu_mx.santander', 'Santander', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 400 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[400] = array("insert" => " (400, 'payu_mx.santander', 'Santander', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 400 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[402] = array("insert" => " (402, 'payu_mx.scotiabank', 'Scotiabank', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 402 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[402] = array("insert" => " (402, 'payu_mx.scotiabank', 'Scotiabank', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 402 AND site_id = 5");

#correct paymaster pg code
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 571 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[571] = array("insert" => " (571, 'paymaster24.pay24', 'Paymaster24', 'Paymaster24', 570, 'paymaster24', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 571 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[573] = array("insert" => " (573, 'molpay.enets', 'eNETS Debit (Singapore)', 'MOLPay', 572, 'pipwavePG', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 573 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[573] = array("insert" => " (573, 'molpay.enets', 'eNETS Debit (Singapore)', 'MOLPay', 572, 'pipwavePG', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 573 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[575] = array("insert" => " (575, 'molpay.singpost', 'Singpost', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 575 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[575] = array("insert" => " (575, 'molpay.singpost', 'Singpost', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 575 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[574] = array("insert" => " (574, 'molpay.paysbuy', 'PaySBuy', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 574 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[574] = array("insert" => " (574, 'molpay.paysbuy', 'PaySBuy', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 574 AND site_id = 5");

?>