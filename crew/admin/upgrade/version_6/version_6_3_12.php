<?php
$conf_insert_sql = array();
$conf_insert_sql[248] = array("insert" => " (248, 'offline_ocbc.ocbcsg', 'OCBC Bank (SG) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 248 AND site_id = 5");
?>