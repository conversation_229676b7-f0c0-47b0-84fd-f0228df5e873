<?php
// Insert new fields into `pipwave_payment_mapper` table
$add_new_field = array();
$add_new_field['pipwave_payment_mapper'] = array(array("field_name" => "site_id",
    "field_attr" => "tinyint(3) unsigned NOT NULL DEFAULT '0'",
    "add_after" => 'is_rp'
)
);
add_field($add_new_field);

//OG UPDATE !!
# delete record
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 430");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 431");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 432");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE pm_id = 433");

# update record
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_ambank.ambank' WHERE pm_id = 220 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_cimb.cimbmy' WHERE pm_id = 214 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_hlb.hongleongbank' WHERE pm_id = 251 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_hsbc.hsbcmy' WHERE pm_id = 222 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_maybank.maybank' WHERE pm_id = 186 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_moneygram.moneygram' WHERE pm_id = 14 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_ocbc.ocbcmy' WHERE pm_id = 243 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_pbbank.publicbank' WHERE pm_id = 187 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_rhb.rhb' WHERE pm_id = 213 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_stanchart.standardcharteredmy' WHERE pm_id = 241 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_uob.uobmy' WHERE pm_id = 317 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_westernunion.westernunion' WHERE pm_id = 15 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_wiretransfer.wiretransfer' WHERE pm_id = 16 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_custom.customatm' WHERE pm_id = 468 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_hsbc.hsbcbanksg' WHERE pm_id = 223 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_ocbc.ocbcsg' WHERE pm_id = 248 AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'offline_uob.uobsg' WHERE pm_id = 246 AND site_id = 0");

# add record
$conf_insert_sql = array();
//offline
$conf_insert_sql[145] = array("insert" => " (145, 'offline_otc.otc', 'Cash @ OffGamers Pudu', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)");

//G2G NEW RECORDS

//truncate g2g pm data before initial data insert
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE site_id = 5");

$conf_insert_sql = array();

//live
//adyen
$conf_insert_sql[3141] = array("insert" => " (314, 'adyen.api.mc', 'MasterCard', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[3151] = array("insert" => " (314, 'adyen.api.visa', 'Visa', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[3161] = array("insert" => " (316, 'adyen.api.maestro', 'Maestro', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[3201] = array("insert" => " (320, 'adyen.api.maestrouk', 'Maestro UK', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[3351] = array("insert" => " (335, 'adyen.api.amex', 'American Express', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[3291] = array("insert" => " (329, 'adyen.api.unionpay', 'China Union Pay', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[3361] = array("insert" => " (336, 'adyen.api.diners', 'Diners Club', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[3341] = array("insert" => " (334, 'adyen.api.discover', 'Discover', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[3371] = array("insert" => " (337, 'adyen.api.jcb', 'JCB', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[4281] = array("insert" => " (428, 'adyen.hpp.dragonpay_ebanking', 'Dragonpay Online Banking', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3301] = array("insert" => " (330, 'adyen.hpp.ebanking_FI', 'Finland e-Banking', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3311] = array("insert" => " (331, 'adyen.hpp.ideal', 'iDEAL', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3331] = array("insert" => " (333, 'adyen.hpp.payshop', 'Payshop', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3321] = array("insert" => " (332, 'adyen.hpp.safetypay', 'SafetyPay', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[3401] = array("insert" => " (340, 'adyen.hpp.bankTransfer_AT', 'Bank Transfer AT', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3471] = array("insert" => " (347, 'adyen.hpp.bankTransfer_BE', 'Bank Transfer BE', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3391] = array("insert" => " (339, 'adyen.hpp.bankTransfer_DE', 'Bank Transfer DE', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3411] = array("insert" => " (341, 'adyen.hpp.bankTransfer_DK', 'Bank Transfer DK', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3441] = array("insert" => " (344, 'adyen.hpp.bankTransfer_ES', 'Bank Transfer ES', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3421] = array("insert" => " (342, 'adyen.hpp.bankTransfer_FI', 'Bank Transfer FI', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3481] = array("insert" => " (348, 'adyen.hpp.bankTransfer_FR', 'Bank Transfer FR', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3431] = array("insert" => " (343, 'adyen.hpp.bankTransfer_IE', 'Bank Transfer IE', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3381] = array("insert" => " (338, 'adyen.hpp.bankTransfer_NL', 'Bank Transfer NL', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3451] = array("insert" => " (345, 'adyen.hpp.bankTransfer_NO', 'Bank Transfer NO', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3461] = array("insert" => " (346, 'adyen.hpp.bankTransfer_SE', 'Bank Transfer SE', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3491] = array("insert" => " (349, 'adyen.hpp.bankTransfer_GB', 'Bank Transfer UK', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4261] = array("insert" => " (426, 'adyen.hpp.dragonpay_otc_banking', 'Dragonpay ATM Payments', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4291] = array("insert" => " (429, 'adyen.hpp.dragonpay_otc_non_banking', 'Dragonpay OTC', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[3501] = array("insert" => " (350, 'adyen.hpp.bankTransfer_IBAN', 'SEPA Bank Transfer Europe', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4271] = array("insert" => " (427, 'adyen.hpp.dragonpay_gcash', 'Dragonpay Globe GCASH', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4781] = array("insert" => " (478, 'adyen.hpp.wallet_RU', 'Moneta Wallet', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4731] = array("insert" => " (473, 'adyen.hpp.paysafecard', 'PaySafeCard', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4751] = array("insert" => " (475, 'adyen.hpp.qiwiwallet', 'QIWI Wallet', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4891] = array("insert" => " (489, 'adyen.hpp.giropay', 'GiroPay', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4911] = array("insert" => " (491, 'adyen.hpp.online_RU', 'Moneta Online Banking', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4901] = array("insert" => " (490, 'adyen.hpp.terminal_RU', 'Moneta Cash Terminal', 'Adyen', 313, 'adyen', 0, 5) ");
$conf_insert_sql[4771] = array("insert" => " (477, 'adyen.hpp.directEbanking', 'SofortBanking', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[4761] = array("insert" => " (476, 'adyen.hpp.poli', 'POLi', 'Adyen', 313, 'adyen', 1, 5) ");
$conf_insert_sql[4251] = array("insert" => " (425, 'adyen.hpp.wechatpay', 'WeChat Pay', 'Adyen', 313, 'adyen', 0, 5) ");

//alipay
$conf_insert_sql[1511] = array("insert" => " (151, 'alipay.alipay', 'Alipay', 'Alipay', 150, 'alipay', 0, 5) ");

//bitpay
$conf_insert_sql[4191] = array("insert" => " (419, 'bitpay.bitpay', 'Bitcoin', 'BitPay', 418, 'bitpay', 0, 5) ");

//cashU
$conf_insert_sql[2211] = array("insert" => " (22, 'cashu.cashu', 'CashU', 'CashU', 21, 'cashU', 0, 5) ");

//cimb
$conf_insert_sql[1431] = array("insert" => " (143, 'cimb.cimbclicks', 'CIMB Clicks', 'CIMB', 142, 'cimb', 0, 5) ");

//coda
$conf_insert_sql[4171] = array("insert" => " (417, 'coda.doku', 'Doku Wallet', 'CodaPayment', 297, 'coda', 0, 5) ");
$conf_insert_sql[3831] = array("insert" => " (383, 'coda.alfamart', 'Alfamart', 'CodaPayment', 297, 'coda', 0, 5) ");
$conf_insert_sql[3711] = array("insert" => " (371, 'coda.otc', 'Counter Payment, Online Banking & ATM', 'CodaPayment', 297, 'coda', 0, 5) ");
$conf_insert_sql[4241] = array("insert" => " (424, 'coda.celcom', 'Celcom', 'CodaPayment', 297, 'coda', 0, 5) ");
$conf_insert_sql[4231] = array("insert" => " (423, 'coda.digi', 'Digi', 'CodaPayment', 297, 'coda', 0, 5) ");
$conf_insert_sql[4151] = array("insert" => " (415, 'coda.h3i', 'H3I', 'CodaPayment', 297, 'coda', 0, 5) ");
$conf_insert_sql[3211] = array("insert" => " (321, 'coda.m1', 'M1', 'CodaPayment', 297, 'coda', 0, 5) ");
$conf_insert_sql[2981] = array("insert" => " (298, 'coda.singtel', 'Singtel', 'CodaPayment', 297, 'coda', 0, 5) ");
$conf_insert_sql[3701] = array("insert" => " (370, 'coda.starhub', 'Starhub', 'CodaPayment', 297, 'coda', 0, 5) ");
$conf_insert_sql[4161] = array("insert" => " (416, 'coda.xl', 'XL', 'CodaPayment', 297, 'coda', 0, 5) ");

//indomog
$conf_insert_sql[2661] = array("insert" => " (266, 'indomog.indomogwallet', 'INDOMOG Account (E-Wallet)', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2631] = array("insert" => " (263, 'indomog.kpybca', 'iBanking BCA KlikPay', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2651] = array("insert" => " (265, 'indomog.intman', 'iBanking Internet Banking Mandiri', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2621] = array("insert" => " (262, 'indomog.klkbca', 'iBanking KlikBCA', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2551] = array("insert" => " (255, 'indomog.bcatrf', 'Bank Central Asia (BCA)', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2581] = array("insert" => " (258, 'indomog.biitrf', 'Bank Internasional Indonesia (BII)', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2561] = array("insert" => " (256, 'indomog.mantrf', 'Bank Mandiri', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2601] = array("insert" => " (260, 'indomog.bnitrf', 'Bank Negara Indonesia (BNI)', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2571] = array("insert" => " (257, 'indomog.bpvtrf', 'Bank Permata', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2591] = array("insert" => " (259, 'indomog.britrf', 'Bank Rakyat Indonesia (BRI)', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2611] = array("insert" => " (261, 'indomog.bsmtrf', 'Bank Syariah Mandiri', 'INDOMOG', 253, 'indomog', 0, 5) ");
$conf_insert_sql[2641] = array("insert" => " (264, 'indomog.smsman', 'mBanking Mobile Banking Mandiri', 'INDOMOG', 253, 'indomog', 0, 5) ");

//ipay88
$conf_insert_sql[1111] = array("insert" => " (111, 'ipay88.paysbuy', 'PaySbuy', 'iPay88', 27, 'iPay88', 0, 5) ");
$conf_insert_sql[3412] = array("insert" => " (34, 'ipay88.fpx', 'FPX', 'iPay88', 27, 'iPay88', 0, 5) ");
$conf_insert_sql[3312] = array("insert" => " (33, 'ipay88.hongleongbank', 'Hong Leong Bank', 'iPay88', 27, 'iPay88', 0, 5) ");

//maybank
$conf_insert_sql[1131] = array("insert" => " (113, 'maybank.m2u', 'M2U', 'Maybank', 112, 'maybank', 0, 5) ");

//skrill
$conf_insert_sql[4711] = array("insert" => " (471, 'moneybooker.neteller', 'NETELLER', 'Skrill', 44, 'moneybookers', 0, 5) ");
$conf_insert_sql[4691] = array("insert" => " (469, 'moneybooker.astropayonline', 'Astropay Online Banking', 'Skrill', 44, 'moneybookers', 0, 5) ");
$conf_insert_sql[4931] = array("insert" => " (493, 'moneybooker.astropaycash', 'Astropay Cash Payment', 'Skrill', 44, 'moneybookers', 0, 5) ");
$conf_insert_sql[2921] = array("insert" => " (292, 'moneybooker.astropayoffline', 'Astropay Offline Banking', 'Skrill', 44, 'moneybookers', 0, 5) ");
$conf_insert_sql[4701] = array("insert" => " (470, 'moneybooker.rapidtransfer', 'Rapid Transfer', 'Skrill', 44, 'moneybookers', 0, 5) ");
$conf_insert_sql[4512] = array("insert" => " (45, 'moneybooker.skrill', 'Moneybookers E-Wallet', 'Skrill', 44, 'moneybookers', 1, 5) ");

//onecard
$conf_insert_sql[3681] = array("insert" => " (368, 'onecard.onecard', 'OneCard', 'OneCard', 367, 'onecardv2', 0, 5) ");

//paypal
$conf_insert_sql[3651] = array("insert" => " (365, 'paypal.ec', 'PayPal EC', 'PayPal', 354, 'paypalEC', 1, 5) ");
$conf_insert_sql[3641] = array("insert" => " (364, 'paypal.cc', 'PayPal EC (CC)', 'PayPal', 354, 'paypalEC', 1, 5) ");

//public bank
$conf_insert_sql[4221] = array("insert" => " (422, 'pbbank.pbe', 'PBe Bank', 'Public Bank', 421, 'pbbank', 0, 5) ");

//rhb
$conf_insert_sql[1821] = array("insert" => " (182, 'rhb.now', 'RHB Now', 'RHB', 180, 'rhb', 0, 5) ");

//smart2pay
$conf_insert_sql[2711] = array("insert" => " (271, 'smart2pay.dineromail', 'DineroMail', 'Smart2Pay', 268, 'smart2pay_globalpay', 1, 5) ");
$conf_insert_sql[2731] = array("insert" => " (273, 'smart2pay.mercado', 'DineroMail', 'Smart2Pay', 268, 'smart2pay_globalpay', 1, 5) ");
$conf_insert_sql[2811] = array("insert" => " (281, 'smart2pay.moneta', 'Moneta Wallet', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[2921] = array("insert" => " (292, 'smart2pay.psc', 'PaySafeCard', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[2781] = array("insert" => " (278, 'smart2pay.paysera', 'PaySera', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[2961] = array("insert" => " (296, 'smart2pay.qiwi', 'QIWI Wallet', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[2801] = array("insert" => " (280, 'smart2pay.toditocash', 'Todito Cash', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[2951] = array("insert" => " (295, 'smart2pay.yandexmoney', 'Yandex.Money', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[3231] = array("insert" => " (323, 'smart2pay.eps', 'Austrian EPS', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[2721] = array("insert" => " (272, 'smart2pay.bancodobrasil', 'Débito Banco do Brasil', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[4131] = array("insert" => " (413, 'smart2pay.enets', 'eNETS Debit (Singapore)', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[3241] = array("insert" => " (324, 'smart2pay.giropay', 'GiroPay', 'Smart2Pay', 268, 'smart2pay_globalpay', 1, 5) ");
$conf_insert_sql[2691] = array("insert" => " (269, 'smart2pay.mrcash', 'MrCash (BanContract)', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[3251] = array("insert" => " (325, 'smart2pay.mybank', 'MyBank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[3261] = array("insert" => " (326, 'smart2pay.poli', 'POLi', 'Smart2Pay', 268, 'smart2pay_globalpay', 1, 5) ");
$conf_insert_sql[2831] = array("insert" => " (283, 'smart2pay.sibs', 'SIBS (MultiBanco)', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[3271] = array("insert" => " (327, 'smart2pay.sofort', 'SofortBanking', 'Smart2Pay', 268, 'smart2pay_globalpay', 1, 5) ");
$conf_insert_sql[2871] = array("insert" => " (287, 'smart2pay.tenpay', 'Tenpay', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[2751] = array("insert" => " (275, 'smart2pay.transferencia', 'Transferencia Entre Contas (Bradesco)', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[2931] = array("insert" => " (293, 'smart2pay.trustly', 'Trustly', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[2701] = array("insert" => " (270, 'smart2pay.trustpay', 'TrustPay', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
$conf_insert_sql[3851] = array("insert" => " (385, 'smart2pay.beeline', 'Beeline', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");

//webmoney
$conf_insert_sql[2412] = array("insert" => " (24, 'webmoney.webmoney', 'Webmoney', 'Webmoney', 23, 'webmoney', 0, 5) ");

//worldpay
$conf_insert_sql[3041] = array("insert" => " (304, 'worldpay.neosurf', 'NeoSurf', 'WorldPay', 64, 'bibit', 0, 5) ");
$conf_insert_sql[3081] = array("insert" => " (308, 'worldpay.ticketsurf', 'Ticketsurf Premium', 'WorldPay', 64, 'bibit', 0, 5) ");
$conf_insert_sql[9112] = array("insert" => " (91, 'worldpay.commerzbank', 'Commerzbank Online Banking', 'WorldPay', 64, 'bibit', 0, 5) ");
$conf_insert_sql[9312] = array("insert" => " (93, 'worldpay.dresdner', 'Dresdner Bank internetbanking', 'WorldPay', 64, 'bibit', 0, 5) ");
$conf_insert_sql[6812] = array("insert" => " (68, 'worldpay.enets', 'eNETS Debit (Singapore)', 'WorldPay', 64, 'bibit', 0, 5) ");
$conf_insert_sql[2991] = array("insert" => " (299, 'worldpay.euteller', 'EuTeller', 'WorldPay', 64, 'bibit', 1, 5) ");
$conf_insert_sql[8112] = array("insert" => " (81, 'worldpay.hansapank', 'Hansapank Bank Link', 'WorldPay', 64, 'bibit', 1, 5) ");
$conf_insert_sql[7412] = array("insert" => " (74, 'worldpay.paybox', 'Paybox', 'WorldPay', 64, 'bibit', 0, 5) ");
$conf_insert_sql[3031] = array("insert" => " (303, 'worldpay.pluspay', 'PlusPay', 'WorldPay', 64, 'bibit', 0, 5) ");
$conf_insert_sql[3071] = array("insert" => " (307, 'worldpay.sporopay', 'Sporopay', 'WorldPay', 64, 'bibit', 1, 5) ");
$conf_insert_sql[2901] = array("insert" => " (290, 'worldpay.boleto', 'Boleto Bancario', 'WorldPay', 64, 'bibit', 0, 5) ");

//payu
$conf_insert_sql[3971] = array("insert" => " (397, 'payu.br.elo', 'ELO', 'PayU LATAM', 396, 'payU', 1, 5) ");
$conf_insert_sql[3981] = array("insert" => " (398, 'payu.br.hipercard', 'Hipercard', 'PayU LATAM', 396, 'payU', 1, 5) ");
$conf_insert_sql[4051] = array("insert" => " (405, 'payu.mx.seven_eleven', '7-Eleven', 'PayU LATAM', 396, 'payU', 0, 5) ");
$conf_insert_sql[4011] = array("insert" => " (401, 'payu.mx.bancomer', 'Bancomer', 'PayU LATAM', 396, 'payU', 0, 5) ");
$conf_insert_sql[4031] = array("insert" => " (403, 'payu.mx.ixe', 'Ixe', 'PayU LATAM', 396, 'payU', 0, 5) ");
$conf_insert_sql[4041] = array("insert" => " (404, 'payu.mx.oxxo', 'Oxxo', 'PayU LATAM', 396, 'payU', 0, 5) ");
$conf_insert_sql[4001] = array("insert" => " (400, 'payu.mx.santander', 'Santander', 'PayU LATAM', 396, 'payU', 0, 5) ");
$conf_insert_sql[4021] = array("insert" => " (402, 'payu.mx.scotiabank', 'Scotiabank', 'PayU LATAM', 396, 'payU', 0, 5) ");

//vtc
$conf_insert_sql[4361] = array("insert" => " (436, 'vtc.vtcpaywallet', 'VTC Pay Wallet', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4631] = array("insert" => " (463, 'vtc.abbank', 'ABBANK', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4491] = array("insert" => " (449, 'vtc.acb', 'ACB', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4411] = array("insert" => " (441, 'vtc.agribank', 'Agribank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4611] = array("insert" => " (461, 'vtc.bacabank', 'BAC A BANK', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4651] = array("insert" => " (465, 'vtc.bvb', 'BaoViet Bank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4441] = array("insert" => " (444, 'vtc.bidv', 'BIDV', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4421] = array("insert" => " (442, 'vtc.dongabank', 'DongA Bank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4481] = array("insert" => " (448, 'vtc.Eximbank', 'Eximbank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4601] = array("insert" => " (460, 'vtc.GPBank', 'GP Bank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4501] = array("insert" => " (450, 'vtc.hdbank', 'HDBank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4671] = array("insert" => " (467, 'vtc.kienlongbank', 'KienLongBank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4641] = array("insert" => " (464, 'vtc.lvpb', 'LienVietPostBank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4471] = array("insert" => " (447, 'vtc.maritimebank', 'Maritime Bank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4391] = array("insert" => " (439, 'vtc.mbbank', 'MB Bank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4511] = array("insert" => " (451, 'vtc.namabank', 'Nam A Bank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4591] = array("insert" => " (459, 'vtc.navibank', 'NCB', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4621] = array("insert" => " (462, 'vtc.phuongdong', 'OCB', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4431] = array("insert" => " (443, 'vtc.oceanbank', 'Ocean Bank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4581] = array("insert" => " (458, 'vtc.pgbank', 'PG Bank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4531] = array("insert" => " (453, 'vtc.sacombank', 'Sacombank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4521] = array("insert" => " (452, 'vtc.saigonbank', 'SaigonBank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4661] = array("insert" => " (466, 'vtc.scbbank', 'SCB', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4571] = array("insert" => " (457, 'vtc.seabank', 'SeABank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4451] = array("insert" => " (445, 'vtc.shb', 'SHB', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4381] = array("insert" => " (438, 'vtc.techcombank', 'Techcombank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4561] = array("insert" => " (456, 'vtc.tienphongbank', 'TienPhongBank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4461] = array("insert" => " (446, 'vtc.vib', 'VIB', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4541] = array("insert" => " (454, 'vtc.vietabank', 'Viet A Bank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4371] = array("insert" => " (437, 'vtc.vietcombank', 'Vietcombank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4401] = array("insert" => " (440, 'vtc.vietinbank', 'Vietinbank', 'VTC', 435, 'vtc', 0, 5) ");
$conf_insert_sql[4551] = array("insert" => " (455, 'vtc.vpbank', 'VPBank', 'VTC', 435, 'vtc', 0, 5) ");

//offline
$conf_insert_sql[2201] = array("insert" => " (220, 'offline_ambank.ambank', 'AmBank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[1451] = array("insert" => " (145, 'offline_otc.otc', 'Cash @ OffGamers Pudu', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[2141] = array("insert" => " (214, 'offline_cimb.cimbmy', 'CIMB Bank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[4301] = array("insert" => " (430, 'offline_hsbc.hsbcmy', 'G2G HSBC Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[4311] = array("insert" => " (431, 'offline_sg_hsbc.hsbcsg', 'G2G HSBC Bank (SG) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[4321] = array("insert" => " (432, 'offline_maybank.maybank', 'G2G Maybank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[4331] = array("insert" => " (433, 'offline_pbbank.publicbank', 'G2G Public Bank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[2511] = array("insert" => " (251, 'offline_hlb.hongleongbank', 'Hong Leong Bank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[1412] = array("insert" => " (14, 'offline_moneygram.moneygram', 'MoneyGram', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[2431] = array("insert" => " (243, 'offline_ocbc.ocbcmy', 'OCBC Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[2131] = array("insert" => " (213, 'offline_rhb.rhb', 'RHB Bank ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[2411] = array("insert" => " (241, 'offline_stanchart.standardcharteredmy', 'Standard Chartered Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[3171] = array("insert" => " (317, 'offline_uob.uobmy', 'United Overseas Bank (MY) ATM / 3rd Party Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[1512] = array("insert" => " (15, 'offline_westernunion.westernunion', 'Western Union', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[1612] = array("insert" => " (16, 'offline_wiretransfer.wiretransfer', 'Wire Transfer', 'Offline', 12, 'offline', 0, 5) ");
$conf_insert_sql[4681] = array("insert" => " (468, 'offline_custom.customatm', 'Wire Transfer SG (custom)', 'Offline', 12, 'offline', 0, 5) ");

insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)");
?>
