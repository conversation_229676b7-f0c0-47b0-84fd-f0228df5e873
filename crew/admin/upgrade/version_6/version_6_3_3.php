<?php
# update record for OG & G2G
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'adyen.hpp.mc' WHERE pm_id = 314");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'adyen.hpp.maestro' WHERE pm_id = 316");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'adyen.hpp.maestrouk' WHERE pm_id = 320");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'adyen.hpp.amex' WHERE pm_id = 335");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'adyen.hpp.unionpay' WHERE pm_id = 329");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'adyen.hpp.diners' WHERE pm_id = 336");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'adyen.hpp.discover' WHERE pm_id = 334");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pipwave_payment_code = 'adyen.hpp.jcb' WHERE pm_id = 337");

$conf_insert_sql = array();

//adyen
$conf_insert_sql[3151] = array("insert" => " (315, 'adyen.hpp.visa', 'Visa', 'Adyen', 313, 'adyen', 1, 0) ");
$conf_insert_sql[3152] = array("insert" => " (315, 'adyen.hpp.visa', 'Visa', 'Adyen', 313, 'adyen', 1, 5) ");

insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)");
?>