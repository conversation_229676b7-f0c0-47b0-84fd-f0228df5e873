<?php
//ipay88
$conf_insert_sql = array();
$conf_insert_sql[30] = array("insert" => " (30, 'ipay88.alliance', 'Alliance Online', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 30 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[30] = array("insert" => " (30, 'ipay88.alliance', 'Alliance Online', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 30 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[496] = array("insert" => " (496, 'ipay88.affin', 'Affin Bank', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 496 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[496] = array("insert" => " (496, 'ipay88.affin', 'Affin Bank', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 496 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[31] = array("insert" => " (31, 'ipay88.ambank', 'Ambank online', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 31 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[31] = array("insert" => " (31, 'ipay88.ambank', 'Ambank online', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 31 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[495] = array("insert" => " (495, 'ipay88.bankrakyat', 'Bank Rakyat', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 495 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[495] = array("insert" => " (495, 'ipay88.bankrakyat', 'Bank Rakyat', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 495 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[498] = array("insert" => " (498, 'ipay88.uob', 'UOB', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 498 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[498] = array("insert" => " (498, 'ipay88.uob', 'UOB', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 498 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[499] = array("insert" => " (499, 'ipay88.bsn', 'BSN', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 499 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[499] = array("insert" => " (499, 'ipay88.bsn', 'BSN', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 499 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[503] = array("insert" => " (503, 'ipay88.bankmuamalat', 'Bank Muamalat', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 503 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[503] = array("insert" => " (503, 'ipay88.bankmuamalat', 'Bank Muamalat', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 503 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[500] = array("insert" => " (500, 'ipay88.ocbc', 'OCBC', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 500 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[500] = array("insert" => " (500, 'ipay88.ocbc', 'OCBC', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 500 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[501] = array("insert" => " (501, 'ipay88.sc', 'Standard Chartered', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 501 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[501] = array("insert" => " (501, 'ipay88.sc', 'Standard Chartered', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 501 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[502] = array("insert" => " (502, 'ipay88.hsbc', 'HSBC', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 502 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[502] = array("insert" => " (502, 'ipay88.hsbc', 'HSBC', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 502 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[497] = array("insert" => " (497, 'ipay88.bankislam', 'Bank Islam', 'iPay88', 27, 'iPay88', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 497 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[497] = array("insert" => " (497, 'ipay88.bankislam', 'Bank Islam', 'iPay88', 27, 'iPay88', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 497 AND site_id = 5");

//worldpay
$conf_insert_sql = array();
$conf_insert_sql[261] = array("insert" => " (261, 'worldpay.astropay', 'AstroPay Card', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 261 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[261] = array("insert" => " (261, 'worldpay.astropay', 'AstroPay Card', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 261 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[304] = array("insert" => " (304, 'worldpay.neosurf', 'NeoSurf', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 304 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[304] = array("insert" => " (304, 'worldpay.neosurf', 'NeoSurf', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 304 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[308] = array("insert" => " (308, 'worldpay.ticketsurf', 'Ticketsurf Premium', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 308 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[308] = array("insert" => " (308, 'worldpay.ticketsurf', 'Ticketsurf Premium', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 308 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[91] = array("insert" => " (91, 'worldpay.commerzbank', 'Commerzbank Online Banking', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 91 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[91] = array("insert" => " (91, 'worldpay.commerzbank', 'Commerzbank Online Banking', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 91 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[93] = array("insert" => " (93, 'worldpay.dresdner', 'Dresdner Bank internetbanking', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 93 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[93] = array("insert" => " (93, 'worldpay.dresdner', 'Dresdner Bank internetbanking', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 93 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[68] = array("insert" => " (68, 'worldpay.enets', 'eNETS Debit (Singapore)', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 68 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[68] = array("insert" => " (68, 'worldpay.enets', 'eNETS Debit (Singapore)', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 68 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[299] = array("insert" => " (299, 'worldpay.euteller', 'EuTeller', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 299 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[299] = array("insert" => " (299, 'worldpay.euteller', 'EuTeller', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 299 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[81] = array("insert" => " (81, 'worldpay.hansapank', 'Hansapank Bank Link', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 81 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[81] = array("insert" => " (81, 'worldpay.hansapank', 'Hansapank Bank Link', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 81 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[74] = array("insert" => " (74, 'worldpay.paybox', 'Paybox', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 74 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[74] = array("insert" => " (74, 'worldpay.paybox', 'Paybox', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 74 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[303] = array("insert" => " (303, 'worldpay.pluspay', 'PlusPay', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 303 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[303] = array("insert" => " (303, 'worldpay.pluspay', 'PlusPay', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 303 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[307] = array("insert" => " (307, 'worldpay.sporopay', 'Sporopay', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 307 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[307] = array("insert" => " (307, 'worldpay.sporopay', 'Sporopay', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 307 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[290] = array("insert" => " (290, 'worldpay.boleto', 'Boleto Bancario', 'Worldpay', 64, 'bibit', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 290 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[290] = array("insert" => " (290, 'worldpay.boleto', 'Boleto Bancario', 'Worldpay', 64, 'bibit', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 290 AND site_id = 5");

//payU
$conf_insert_sql = array();
$conf_insert_sql[397] = array("insert" => " (397, 'payu.br.elo', 'ELO', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 397 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[397] = array("insert" => " (397, 'payu.br.elo', 'ELO', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 397 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[398] = array("insert" => " (398, 'payu.br.hipercard', 'Hipercard', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 398 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[398] = array("insert" => " (398, 'payu.br.hipercard', 'Hipercard', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 398 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[405] = array("insert" => " (405, 'payu.mx.seven_eleven', '7-Eleven', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 405 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[405] = array("insert" => " (405, 'payu.mx.seven_eleven', '7-Eleven', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 405 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[401] = array("insert" => " (401, 'payu.mx.bancomer', 'Bancomer', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 401 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[401] = array("insert" => " (401, 'payu.mx.bancomer', 'Bancomer', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 401 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[403] = array("insert" => " (403, 'payu.mx.ixe', 'Ixe', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 403 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[403] = array("insert" => " (403, 'payu.mx.ixe', 'Ixe', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 403 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[404] = array("insert" => " (404, 'payu.mx.oxxo', 'Oxxo', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 404 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[404] = array("insert" => " (404, 'payu.mx.oxxo', 'Oxxo', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 404 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[400] = array("insert" => " (400, 'payu.mx.santander', 'Santander', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 400 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[400] = array("insert" => " (400, 'payu.mx.santander', 'Santander', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 400 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[402] = array("insert" => " (402, 'payu.mx.scotiabank', 'Scotiabank', 'PayU', 396, 'payU', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 402 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[402] = array("insert" => " (402, 'payu.mx.scotiabank', 'Scotiabank', 'PayU', 396, 'payU', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 402 AND site_id = 5");

?>