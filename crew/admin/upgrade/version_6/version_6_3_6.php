<?php
$add_new_tables = array();
$add_new_tables["log_api_restock_transaction"] = array(
                    "structure" => "CREATE TABLE IF NOT EXISTS `log_api_restock_transaction` (
                    `transaction_id` bigint(20) unsigned NOT NULL,
                    `log_api_provider` varchar(32) NULL,
                    `log_method` varchar(20) NULL,
                    `log_sku` varchar(32) NULL,
                    `log_description` varchar(255) NULL,
                    `log_ack` varchar(10) NULL,
                    `log_flag_state` char(1) NULL,
                    `log_msg` varchar(255) NULL,
                    `log_created_datetime` datetime
                  ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

# Column length too short, data truncated
tep_db_query("ALTER TABLE " . TABLE_PIPWAVE_PAYMENT . " CHANGE `payment_method_code` `payment_method_code` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT . " SET payment_method_code = 'offline_wiretransfer.wiretransfer' WHERE payment_method_code = 'offline_wiretransfer.wiretransfe'");

?>