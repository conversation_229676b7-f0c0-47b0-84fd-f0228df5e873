<?php
# delete record
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE id = 2");
tep_db_query("DELETE FROM " . TABLE_PIPWAVE_PAYMENT_MAPPER . " WHERE id = 338");

# insert missing recode
$conf_insert_sql = array();
$conf_insert_sql[492] = array("insert" => " (492, 'moneybooker.astropayoffline', 'Astropay Offline Banking', 'Skrill', 44, 'moneybookers', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 492 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[492] = array("insert" => " (492, 'moneybooker.astropayoffline', 'Astropay Offline Banking', 'Skrill', 44, 'moneybookers', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 492 AND site_id = 5");
