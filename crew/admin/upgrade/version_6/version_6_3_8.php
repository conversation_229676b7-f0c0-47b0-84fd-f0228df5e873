<?php

//iDE<PERSON>
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 331");
//Dragonpay Globe GCASH
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 427");
//PaySafeCard
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 473");
//GiroPay
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 489");
//Doku Wallet
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 417");
//Alfamart
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 383");
//Counter Payment, Online Banking & ATM
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 371");
//Celcom
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 424");
//Digi
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 423");
//H3I
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 415");
//M1
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 321");
//Singtel
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 298");
//Starhub
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 370");
//XL
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 416");
//INDOMOG Account (E-Wallet)
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 266");
//iBanking BCA KlikPay
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 263");
//iBanking Internet Banking Mandiri
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 265");
//iBanking KlikBCA
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 262");
//Bank Central Asia (BCA)
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 255");
//Bank Internasional Indonesia (BII)
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 258");
//Bank Mandiri
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 256");
//Bank Negara Indonesia (BNI)
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 260");
//Bank Negara Indonesia (BNI)
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 257");
//Bank Rakyat Indonesia (BRI)
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 259");
//Bank Syariah Mandiri
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 261");
//mBanking Mobile Banking Mandiri
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 264");
//eNETS Debit (Singapore)
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 413");
//eNETS Debit (Singapore)
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 68");
//G2G HSBC Bank (SG) ATM / 3rd Party Transfer
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 431");
//HSBC Bank (SG) ATM / 3rd Party Transfer
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 223");
//Wire Transfer SG (custom)
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pm_id = 468");
//Adyen - Payshop
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pm_id = 333");

$conf_insert_sql = array();
$conf_insert_sql[388] = array("insert" => " (388, 'adyen.hpp.alipay', 'Alipay', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 388 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[388] = array("insert" => " (388, 'adyen.hpp.alipay', 'Alipay', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 388 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[388] = array("insert" => " (504, 'offline_alipay.alipay_manual', 'Alipay Manual Transfer', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 504 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[388] = array("insert" => " (504, 'offline_alipay.alipay_manual', 'Alipay Manual Transfer', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 504 AND site_id = 5");
