<?php  

$add_new_tables = array();
// Create new table
$add_new_tables["publishers_replenish"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `publishers_replenish` (
                    `publishers_replenish_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `publishers_name` varchar(32) NOT NULL,
                    `publishers_status` tinyint(4) NOT NULL default '0',
                    `publishers_remark` text NOT NULL,
                    `publishers_supplier_id` int(11) unsigned NULL,
                    `publishers_payment_term` tinyint(4) NOT NULL default '0',
                    `publishers_sku_header` varchar(32) NOT NULL,
                    `publishers_api_provider` varchar(32) NOT NULL,
                    `publishers_api_method` varchar(16) NOT NULL,
                    `last_modified` datetime default '0000-00-00 00:00:00',
                    `date_added` datetime NOT NULL default '0000-00-00 00:00:00',
                    `last_modified_by` int(11) NOT NULL default '0',
                    `sort_order` int(11) NOT NULL default '50000',
                    PRIMARY KEY (`publishers_replenish_id`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["api_replenish_purchase_orders"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `api_replenish_purchase_orders` (
                    `api_replenish_purchase_orders_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `custom_products_code_id` int(11) unsigned NOT NULL,
                    `purchase_orders_id` int(11) unsigned NOT NULL,
                    `purchase_orders_ref_id` varchar(32) default NULL COMMENT 'Ref ID for Payment Withdrawal',
                    `api_withdrawal_status` enum('0','1','2','3','4') NOT NULL default '0' COMMENT 'CDKEY withdrawal status, 0=No Withdrawal 1=Processing, 2=Approved, 3=Canceled, 4=Paid',
                    `api_withdrawal_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`api_replenish_purchase_orders_id`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["api_replenish_cb"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `api_replenish_cb` (
                    `api_replenish_cb_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `custom_products_code_id` int(11) unsigned NOT NULL,
                    `api_cb_status` enum('0','1','2') NOT NULL default '0' COMMENT 'CDKEY Charge Back status, 0=Not set 1=Charge Back, 2=API Issue',
                    `api_cb_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`api_replenish_cb_id`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["api_replenish_cb_purchase_orders"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `api_replenish_cb_purchase_orders` (
                    `api_replenish_cb_purchase_orders_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `custom_products_code_id` int(11) unsigned NOT NULL,
                    `purchase_orders_id` int(11) unsigned NOT NULL,
                    `purchase_orders_ref_id` varchar(32) default NULL COMMENT 'Ref ID for Payment Withdrawal',
                    `api_cb_status` enum('0','1') NOT NULL default '0' COMMENT 'API CB status, 0=No CB 1=Charge Back',
                    `api_cb_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`api_replenish_cb_purchase_orders_id`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

// Alter tbl:log_api_restock
$alter_table_publishers = array(
    'log_api_restock' => array(
        array(
            'field_name' => 'changed_by',
            'field_attr' => "VARCHAR(128) DEFAULT 'system'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'api_cb_date',
            'field_attr' => "DATETIME DEFAULT '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Charge Back'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'api_cb_deduction_status',
            'field_attr' => "ENUM('0','1') NOT NULL DEFAULT '0' COMMENT 'API Charge Back deduction status, 0=Available for deduction 1=Deducted'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'api_cb_deduction_po_id',
            'field_attr' => "INT(11) UNSIGNED NOT NULL DEFAULT '0'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'api_cb_temp_status',
            'field_attr' => "ENUM('0','1','2') NOT NULL DEFAULT '0' COMMENT 'API Charge Back temp status, 0=Not set 1=Charge Back, 2=API Issue'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'api_cb_status',
            'field_attr' => "ENUM('0','1','2') NOT NULL DEFAULT '0' COMMENT 'API Charge Back status, 0=Not set 1=Charge Back, 2=API Issue'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'api_withdrawal_date',
            'field_attr' => "DATETIME DEFAULT '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'api_withdrawal_temp_status',
            'field_attr' => "ENUM('0','1') NOT NULL DEFAULT '0' COMMENT 'API withdrawal temp status, 0=unchecked 1=checked'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'api_withdrawal_status',
            'field_attr' => "ENUM('0','1','2','3','4') NOT NULL DEFAULT '0' COMMENT 'API withdrawal status, 0=No Withdrawal 1=Processing, 2=Approved, 3=Canceled, 4=Paid'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'api_withdrawal_ref_id',
            'field_attr' => "VARCHAR(32) DEFAULT NULL COMMENT 'Ref ID for Payment Withdrawal'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'purchase_orders_id',
            'field_attr' => "INT(11) UNSIGNED NOT NULL DEFAULT '0'",
            'add_after' => 'created_datetime',
        ),
        array(
            'field_name' => 'publishers_id',
            'field_attr' => "INT(10) UNSIGNED NOT NULL",
            'add_after' => 'created_datetime',
        )
    )
);

add_field($alter_table_publishers);

// Insert new records into admin_files table (for API Replenish Publishers)
$select_sql = "	SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . "
                WHERE admin_files_name='data_pool.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["publishers_replenish.php"] = array(	
        "insert" => " ('publishers_replenish.php', 0, '".$row_sql['admin_files_id']."', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='".$row_sql['admin_files_id']."', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}

// Insert new records into admin_files table (for API Replenish Payment Request)
$select_sql = "	SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='purchase_orders.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["api_replenish_payment.php"] = array(
        "insert" => " ('api_replenish_payment.php', 0, '".$row_sql['admin_files_id']."', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_insert_sql["printable_api_replenish.php"] = array(
        "insert" => " ('printable_api_replenish.php', 0, '".$row_sql['admin_files_id']."', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}

// Insert new records into admin_files_actions table (for api replenish suppliers)
$select_sql = "	SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='api_replenish_payment.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["API_NEW_PO"] = array(
        "insert" => " ('API_NEW_PO', 'New API Replenish Payment Request', '2', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=2, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["API_PROCESS_PO"] = array(
        "insert" => " ('API_PROCESS_PO', 'Process API PO', '4', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=4, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["API_CANCEL_PENDING_RECEIVE"] = array(
        "insert" => " ('API_CANCEL_PENDING_RECEIVE', 'Cancel Pending API PO', '5', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=5, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["API_VERIFY_PO"] = array(
        "insert" => " ('API_VERIFY_PO', 'Verify/unverify Completed Purchase Order', '7', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=7, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["API_MAKE_PAYMENT"] = array(
        "insert" => " ('API_MAKE_PAYMENT', 'Make Payment', '8', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=8, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["API_ADD_REMARK"] = array(
        "insert" => " ('API_ADD_REMARK', 'Add Remark', '10', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=10, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["API_VIEW_REMARK"] = array(
        "insert" => " ('API_VIEW_REMARK', 'View Remarks', '11', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=11, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["API_PO_SET_PAID_STATUS"] = array(
        "insert" => " ('API_PO_SET_PAID_STATUS', 'Update API Withdrawal Status', '12', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=12, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}
// End of insert new records into admin_files table (for api replenish payment)

// Insert tbl:log_api_restock

$api_date_cutoff = "2010-01-01 00:00:00";

$update_sql[TABLE_LOG_API_RESTOCK] = array(
    array(
        "field_name" => "api_withdrawal_status",
        "update" => " api_withdrawal_status = '4'",
        "where_str" => " flag_state = 'S' AND created_datetime < '".$api_date_cutoff."'"
    )
);

advance_update_records($update_sql, $DBTables);