<?php  
// Migration Info for Manual DTU Status Change
// Insert new records into admin_files_actions table (for po consignment suppliers)
$select_sql = " SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='api_report.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["API_DTU_STATUS_CHANGE"] = array(
        "insert" => " ('API_DTU_STATUS_CHANGE', 'DTU status change', '11', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=11, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}

// Insert new records into categories_configuration table (for Manual DTU Status change)
$conf_insert_sql = array();

$conf_insert_sql["MANUAL_DTU_STATUS_CHANGE_EMAIL"] = array("insert" => " (0, 'Manual DTU Status Change', 'MANUAL_DTU_STATUS_CHANGE_EMAIL', '', 'Email address to which the email will be send to whenever someone manually change a DTU status.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', 9, 121, NULL, now(), NULL, NULL)" );

insert_new_records(TABLE_CATEGORIES_CONFIGURATION, "categories_configuration_key", $conf_insert_sql, $DBTables, "(`categories_id`, `categories_configuration_title`, `categories_configuration_key`, `categories_configuration_value`, `categories_configuration_description`, `categories_configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
// End of insert new records into categories_configuration table (for Manual DTU Status change)

// Migration Steps:
// 1. Add 3 more field
// 	- original cost amount in origianl currency
// 	- original currency
// 	- use currency exchange (on/off)
// 2. get all products cost info
// 3. populate new field

// Insert new fields into `products_cost` table
$alter_table_arr = array(
    'products_cost' => array(
        array(
            'field_name' => 'products_original_currency',
            'field_attr' => 'CHAR(3) NOT NULL',
            'add_after' => 'products_model'
        ),
        array(
            'field_name' => 'products_original_cost',
            'field_attr' => 'DECIMAL(15,6) NOT NULL',
            'add_after' => 'products_model'
        ),
        array(
            'field_name' => 'products_forex_status',
            'field_attr' => 'CHAR(3) NOT NULL',
            'add_after' => 'products_model'
        )
    ),
);
add_field($alter_table_arr);

// Data Migration for new fields
$data_qry = "SELECT products_id, products_cost, products_currency 
			FROM " . TABLE_PRODUCTS_COST;
$data_result = tep_db_query($data_qry);

while ($data_row = tep_db_fetch_array($data_result)) {
	$update_sql[TABLE_PRODUCTS_COST] = array(
	    array(
	        "field_name" => "products_forex_status",
	        "update" => " products_forex_status = 'off'",
	        "where_str" => " products_id = '" . $data_row['products_id'] . "'"
	    ),
	    array(
	        "field_name" => "products_original_cost",
	        "update" => " products_original_cost = '" . $data_row['products_cost'] . "'",
	        "where_str" => " products_id = '" . $data_row['products_id'] . "'"
	    ),
	    array(
	        "field_name" => "products_original_currency",
	        "update" => " products_original_currency = '" . $data_row['products_currency'] . "'",
	        "where_str" => " products_id = '" . $data_row['products_id'] . "'"
	    )
	);

	advance_update_records($update_sql, $DBTables);
}

?>