<?php
// Insert new records into configuration table (for Credit Notes Manual Action Notify Email)
$select_sql = " SELECT configuration_group_id 
                FROM " . TABLE_CONFIGURATION_GROUP . "
                WHERE configuration_group_title='Purchase Order Supplier'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {   // if found existing record
    $conf_insert_sql = array();
    
    $conf_insert_sql["NRSC_DISCREPANCY_CHECKING_EMAIL"] = array("insert" => " ('NRSC Discrepancy Checking Email Notification', 'NRSC_DISCREPANCY_CHECKING_EMAIL', '', 'Email address to which the email will be send to when there is error during descrepancy checking.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 16, NULL, now(), NULL, '')" );
    
    insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Credit Notes Manual Action Notify Email)
?>