<?php
# update record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[577] = array("insert" => " (577, 'offline_maybank_billpayment.billpayment', 'Maybank BillPayment', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 577 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[577] = array("insert" => " (577, 'offline_maybank_billpayment.billpayment', 'Maybank BillPayment', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 577 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[578] = array("insert" => " (578, 'm2upay.cashpayment', 'Maybank2U Pay', 'M2UPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 578 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[578] = array("insert" => " (578, 'm2upay.cashpayment', 'Maybank2U Pay', 'M2UPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 578 AND site_id = 5");
?>