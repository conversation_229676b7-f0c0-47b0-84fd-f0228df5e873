<?php
// migrate unpaid PO data to set purchase_orders_paid_currency

// get unpaid PO
$po_sql = " SELECT purchase_orders_id, store_payment_account_book_id 
            FROM " . TABLE_PURCHASE_ORDERS . " 
            WHERE purchase_orders_billing_status = '1'
                AND purchase_orders_paid_status <> '1'
                AND (purchase_orders_paid_currency IS NULL OR purchase_orders_paid_currency = '') 
            ORDER BY purchase_orders_id DESC";
$po_result = tep_db_query($po_sql);
while ($po_row = tep_db_fetch_array($po_result)) {
    $pm_select_sql = "SELECT pab.store_payment_account_book_id, pm.payment_methods_send_currency
                        FROM " . TABLE_STORE_PAYMENT_ACCOUNT_BOOK . " AS pab
                        INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm
                            ON pab.payment_methods_id=pm.payment_methods_id
                        WHERE pab.store_payment_account_book_id = '" . tep_db_input($po_row['store_payment_account_book_id']) . "'";
    $pm_result_sql = tep_db_query($pm_select_sql);
    if ($pm_row = tep_db_fetch_array($pm_result_sql)) {
        if ($pm_row['payment_methods_send_currency'] < 1) {
            // Do Nothing
        } else {
            // Get currency code
            $curr_code_sql = "SELECT currencies_id, code 
                            FROM currencies 
                            WHERE currencies_id = '" . tep_db_input($pm_row['payment_methods_send_currency']) . "'";
            $curr_code_result = tep_db_query($curr_code_sql);
            if ($curr_code_row = tep_db_fetch_array($curr_code_result)) {
                // Update records in purchase_orders table
                $po_update_sql = array();
                $po_update_sql[$po_row['purchase_orders_id']] = array("update" => "purchase_orders_paid_currency='".$curr_code_row["code"]."'");
                
                update_records(TABLE_PURCHASE_ORDERS, "purchase_orders_id", $po_update_sql, $DBTables);
            }
        }
    }
}
?>