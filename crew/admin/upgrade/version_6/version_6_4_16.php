<?php
# update record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[579] = array("insert" => " (579, 'pg2c2p.123_channel', '2C2P - 123', '2C2P', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 579 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[579] = array("insert" => " (579, 'pg2c2p.123_channel', '2C2P - 123', '2C2P', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 579 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[580] = array("insert" => " (580, 'pg2c2p.mpu', '2C2P - MPU', '2C2P', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 580 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[580] = array("insert" => " (580, 'pg2c2p.mpu', '2C2P - MPU', '2C2P', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 580 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[582] = array("insert" => " (582, 'doku.doku_wallet', 'Doku Wallet', 'DOKU', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 582 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[582] = array("insert" => " (582, 'doku.doku_wallet', 'Doku Wallet', 'DOKU', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 582 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[583] = array("insert" => " (583, 'doku.permata', 'Permata', 'DOKU', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 583 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[583] = array("insert" => " (583, 'doku.permata', 'Permata', 'DOKU', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 583 AND site_id = 5");
?>