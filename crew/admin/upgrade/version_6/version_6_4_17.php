<?php
// Alter tbl:payment_methods
$alter_table = array(
    'payment_methods' => array(
        array(
            'field_name' => 'payment_methods_payment_admin_groups_id',
            'field_attr' => "TEXT NOT NULL",
            'add_after' => 'payment_methods_admin_groups_id',
        )
    )
);

add_field($alter_table);

// Insert new records into admin_files_actions table (for normal PO)
$select_sql = " SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='edit_purchase_orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["PO_UNLOCK_PO"] = array(
        "insert" => " ('PO_UNLOCK_PO', 'Unlock any PO', '2', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=15, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}

// Insert new records into admin_files_actions table (for dtu PO)
$select_sql = " SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='dtu_payment.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["DTU_UNLOCK_PO"] = array(
        "insert" => " ('DTU_UNLOCK_PO', 'Unlock any DTU PO', '2', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=15, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}

// Insert new records into admin_files_actions table (for api replenish PO)
$select_sql = " SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='api_replenish_payment.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["API_UNLOCK_PO"] = array(
        "insert" => " ('API_UNLOCK_PO', 'Unlock any API PO', '2', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=15, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}

// Insert new records into admin_files_actions table (for consignment PO)
$select_sql = " SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='consignment_payment.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["CDK_UNLOCK_PO"] = array(
        "insert" => " ('CDK_UNLOCK_PO', 'Unlock any CDK PO', '2', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=15, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}
?>