<?php
// Insert new records into status_configuration table (for B status movement COMPLETED to PENDING)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array("insert" => " ('B', '3', '1', '', '', '-1') ");
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", " status_configuration_trans_type='B' AND status_configuration_source_status_id='3' AND status_configuration_destination_status_id='1' ");
?>