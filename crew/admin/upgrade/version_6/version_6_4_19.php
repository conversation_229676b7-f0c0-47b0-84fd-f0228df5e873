<?php
// Update international dialing code
$update_countries_sql = array();

// AS
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 4";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '684') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('684',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =4";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["4"] = array("update" => " countries_international_dialing_code=1 " );

// VG
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 231";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '284') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('284',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =231";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["231"] = array("update" => " countries_international_dialing_code=1 " );

// AG
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 9";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1268') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('268',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =9";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["9"] = array("update" => " countries_international_dialing_code=1 " );

// AI
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 7";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1264') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('264',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =7";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["7"] = array("update" => " countries_international_dialing_code=1 " );

// BM
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 24";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1441') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('441',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =24";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["24"] = array("update" => " countries_international_dialing_code=1 " );

// GD
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 86";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1473') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('473',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =86";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["86"] = array("update" => " countries_international_dialing_code=1 " );

// GU
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 88";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1671') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('671',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =88";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["88"] = array("update" => " countries_international_dialing_code=1 " );

// JM
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 106";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1876') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('876',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =106";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["106"] = array("update" => " countries_international_dialing_code=1 " );

// KN
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 178";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1869') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('869',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =178";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["178"] = array("update" => " countries_international_dialing_code=1 " );

// KY
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 40";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1345') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('345',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =40";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["40"] = array("update" => " countries_international_dialing_code=1 " );

// LC
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 179";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1758') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('758',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =179";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["179"] = array("update" => " countries_international_dialing_code=1 " );

// MP
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 159";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1670') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('670',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =159";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["159"] = array("update" => " countries_international_dialing_code=1 " );

// MS
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 143";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1664') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('664',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =143";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["143"] = array("update" => " countries_international_dialing_code=1 " );

// PR
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 172";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1787') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('787',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =172";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["172"] = array("update" => " countries_international_dialing_code=1 " );

// TC
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 217";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1649') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('649',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =217";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["217"] = array("update" => " countries_international_dialing_code=1 " );

// TT
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 213";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1868') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('868',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =213";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["213"] = array("update" => " countries_international_dialing_code=1 " );

// VC
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 180";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1784') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('784',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =180";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["180"] = array("update" => " countries_international_dialing_code=1 " );

// VI
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 232";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1340') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('340',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =232";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["232"] = array("update" => " countries_international_dialing_code=1 " );

// SJ & YT - ignore update customers phone
$update_countries_sql["201"] = array("update" => " countries_international_dialing_code=47 " );
$update_countries_sql["137"] = array("update" => " countries_international_dialing_code=262 " );

update_records("countries", "countries_id", $update_countries_sql, $DBTables);
// End of update international dialing code
?>