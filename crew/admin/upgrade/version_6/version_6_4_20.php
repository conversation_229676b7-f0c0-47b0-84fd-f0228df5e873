<?php
// Create new tables
$add_new_tables = array();
$add_new_tables["cron_po_dayterm"] = array (    
    "structure" => "CREATE TABLE IF NOT EXISTS `cron_po_dayterm` (
                    `cron_po_dayterm_trans_id` int(11) unsigned NOT NULL COMMENT 'PO ID',
                    `cron_po_dayterm_trans_created_date` datetime NOT NULL default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for PO',
                    `cron_po_dayterm_trans_period` int(11) unsigned NOT NULL default '0' COMMENT 'Day Term no of days',
                    `cron_po_dayterm_trans_date` datetime NOT NULL default '0000-00-00 00:00:00' COMMENT 'Final date for PO Payment',
                    `cron_po_dayterm_trans_notification_count` int(11) NOT NULL default '0' COMMENT 'No of notification send out',
                    `cron_po_dayterm_trans_error` tinyint(1) NOT NULL default '0' COMMENT 'Cron error for this PO',
                    <PERSON><PERSON>ARY KEY (`cron_po_dayterm_trans_id`),
                    <PERSON>EY `idx_trans_created_date` (`cron_po_dayterm_trans_created_date`)
              ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables ($add_new_tables, $DBTables);

// Insert cron track record for cron_po_dayterm.php
$cron_track_rec_select_sql = "  SELECT cron_process_track_filename 
                                FROM cron_process_track 
                                WHERE cron_process_track_filename = 'cron_po_dayterm.php'";
$cron_track_rec_result_sql = tep_db_query($cron_track_rec_select_sql);

if (!tep_db_num_rows($cron_track_rec_result_sql)) {
    $cron_track_rec_data_array = array( 'cron_process_track_in_action' => 0,
                                        'cron_process_track_start_date' => 'now()',
                                        'cron_process_track_failed_attempt' => 0,
                                        'cron_process_track_filename' => 'cron_po_dayterm.php'
                                       );
    tep_db_perform('cron_process_track', $cron_track_rec_data_array);
}
// End of insert cron track record for cron_po_dayterm.php

// Check for previous Day Term PO which is not yet paid & insert into cron_po_dayterm
// This data will be use for Day Term Notification cron
$cutoff_date = "2017-01-01 00:00:00";
$po_qry = "	SELECT purchase_orders_id, purchase_orders_issue_date, payment_term
			FROM " . TABLE_PURCHASE_ORDERS . "
			WHERE payment_type = 't'
                AND purchase_orders_status < 4 
                AND purchase_orders_status > 1 
				AND purchase_orders_issue_date >= '" . $cutoff_date . "'";
$po_result = tep_db_query($po_qry);
if (tep_db_num_rows($po_result)) {
    while ($po_row = tep_db_fetch_array($po_result)) {
    	$insert_cron = false;
        $payment_sql = "SELECT spti.store_payments_transaction_id, spti.store_payments_reimburse_id, sp.store_payments_status 
        				FROM " . TABLE_STORE_PAYMENTS_TRANSACTION_INFO . " AS spti 
        				INNER JOIN " . TABLE_STORE_PAYMENTS . " AS sp 
        					ON spti.store_payments_id = sp.store_payments_id 
    					WHERE spti.store_payments_reimburse_id = " . $po_row['purchase_orders_id'] . " 
    					ORDER BY store_payments_transaction_id 
    					DESC LIMIT 1";
    	$payment_result = tep_db_query($payment_sql);
    	if ($payment_row = tep_db_fetch_array($payment_result)) {
    		if ($payment_row['store_payments_status'] < 3) {
    			$insert_cron = true;
    		}
    	} else {
    		$insert_cron = true;
    	}

    	if ($insert_cron) {
            // Check cron table if exist
            $cron_po_dayterm_sql = "SELECT cpd.cron_po_dayterm_trans_id 
                                    FROM " . TABLE_CRON_PO_DAYTERM . " AS cpd
                                    WHERE cron_po_dayterm_trans_id = " . $po_row['purchase_orders_id'] . "";
            $cron_po_dayterm_result = tep_db_query($cron_po_dayterm_sql);
            if ($cron_po_dayterm_row = tep_db_fetch_array($cron_po_dayterm_result)) {
                // do nothing
            } else {
                $cron_po_dayterm_data_array = array(
                    'cron_po_dayterm_trans_id' => $po_row['purchase_orders_id'],
                    'cron_po_dayterm_trans_created_date' => $po_row['purchase_orders_issue_date'],
                    'cron_po_dayterm_trans_period' => $po_row['payment_term'],
                    'cron_po_dayterm_trans_date' => date('Y-m-d H:i:s', strtotime($po_row['purchase_orders_issue_date'].' +'.$po_row['payment_term'].' day'))
                );
                tep_db_perform(TABLE_CRON_PO_DAYTERM, $cron_po_dayterm_data_array);
            }
    	}
    }
}

// Insert new records into configuration table (for Credit Notes Manual Action Notify Email)
$select_sql = " SELECT configuration_group_id 
                FROM " . TABLE_CONFIGURATION_GROUP . "
                WHERE configuration_group_title='Purchase Order Supplier'" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {   // if found existing record
    $conf_insert_sql = array();
    
    $conf_insert_sql["DAYTERM_NOTIFICATION_EMAIL"] = array("insert" => " ('PO Day Term Notification Email', 'DAYTERM_NOTIFICATION_EMAIL', '', 'Email address to which the email will be send to when there is notification for Day Term PO.<br>(In \"Name &lt;Email&gt;\" format. Use \',\' as delimiter for multiple recipient)', ".$row_sql["configuration_group_id"].", 16, NULL, now(), NULL, '')" );
    
    insert_new_records(TABLE_CONFIGURATION, "configuration_key", $conf_insert_sql, $DBTables, "(`configuration_title`, `configuration_key`, `configuration_value`, `configuration_description`, `configuration_group_id`, `sort_order`, `last_modified`, `date_added`, `use_function`, `set_function`)");
}
// End of insert new records into configuration table (for Credit Notes Manual Action Notify Email)
?>