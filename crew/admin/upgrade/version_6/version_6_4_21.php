<?php
// Update international dialing code
$update_countries_sql = array();

// BB
$sql = "SELECT countries_international_dialing_code FROM countries WHERE countries_id = 19";
$result = tep_db_query($sql);
if ($row = tep_db_fetch_array($result)) {
    if ($row['countries_international_dialing_code'] == '1246') {
        $update_sql = "UPDATE customers 
                        SET customers_telephone = CONCAT('246',customers_telephone)
                        WHERE  `customers_country_dialing_code_id` =19";
        tep_db_query($update_sql);
    }
}
$update_countries_sql["19"] = array("update" => " countries_international_dialing_code=1 " );

// PN - ignore update customers phone
$update_countries_sql["169"] = array("update" => " countries_international_dialing_code=64 " );

update_records("countries", "countries_id", $update_countries_sql, $DBTables);
// End of update international dialing code
?>