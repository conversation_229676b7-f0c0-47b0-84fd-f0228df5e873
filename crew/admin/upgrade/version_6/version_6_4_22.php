<?php
# update record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[24] = array("insert" => " (24, 'webmoney.webmoney', 'Webmoney', 'Webmoney', 23, 'webmoney', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 24 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[24] = array("insert" => " (24, 'webmoney.webmoney', 'Webmoney', 'Webmoney', 23, 'webmoney', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 24 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[584] = array("insert" => " (584, 'webmoney.webmoney.r', 'Webmoney', 'Webmoney', 23, 'webmoney', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 584 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[584] = array("insert" => " (584, 'webmoney.webmoney.r', 'Webmoney', 'Webmoney', 23, 'webmoney', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 584 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[585] = array("insert" => " (585, 'doku.doku_cc', 'DOKU - Local Credit Card (IDR)', 'DOKU', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 585 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[585] = array("insert" => " (585, 'doku.doku_cc', 'DOKU - Local Credit Card (IDR)', 'DOKU', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 585 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[586] = array("insert" => " (586, 'doku.doku_alfamart', 'DOKU - Alfamart', 'DOKU', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 586 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[586] = array("insert" => " (586, 'doku.doku_alfamart', 'DOKU - Alfamart', 'DOKU', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 586 AND site_id = 5");
?>