<?php 

$add_new_tables = array();
// Create new table
$add_new_tables["custom_products_withdrawal"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `custom_products_withdrawal` (
                    `custom_products_withdrawal_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `custom_products_code_id` int(11) NOT NULL,
                    `supplier_id` int(11) NOT NULL,
                    `purchase_orders_id` int(11) unsigned NOT NULL default '0',
                    `cdk_withdrawal_ref_id` varchar(32) default NULL COMMENT 'Ref ID for Payment Withdrawal',
                    `cdk_withdrawal_status` enum('0','1','2','3','4') NOT NULL default '0' COMMENT 'CDK withdrawal status, 0=No Withdrawal 1=Processing, 2=Approved, 3=Canceled, 4=Paid',
                    `cdk_withdrawal_temp_status` enum('0','1') NOT NULL default '0' COMMENT 'CDK withdrawal temp status, 0=unchecked 1=checked',
                    `cdk_withdrawal_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `cdk_cb_status` enum('0','1','2','3') NOT NULL default '0' COMMENT 'CDK Charge Back status, 0=Not set 1=Charge Back, 2=Debit Note, 3=DTU Issue',
                    `cdk_cb_temp_status` enum('0','1','2','3') NOT NULL default '0' COMMENT 'CDK Charge Back temp status, 0=Not set 1=Charge Back, 2=Debit Note, 3=DTU Issue',
                    `cdk_cb_deduction_po_id` int(11) unsigned NOT NULL default '0',
                    `cdk_cb_deduction_status` enum('0','1') NOT NULL default '0' COMMENT 'CDK Cash Back deduction status, 0=Available for deduction 1=Deducted',
                    `cdk_cb_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Cash Back',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`custom_products_withdrawal_id`),
                    UNIQUE KEY (`custom_products_code_id`)
                ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["custom_products_purchase_orders"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `custom_products_purchase_orders` (
                    `custom_products_purchase_orders_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `custom_products_code_id` int(11) NOT NULL,
                    `purchase_orders_id` int(11) unsigned NOT NULL,
                    `purchase_orders_ref_id` varchar(32) default NULL COMMENT 'Ref ID for Payment Withdrawal',
                    `cdk_withdrawal_status` enum('0','1','2','3','4') NOT NULL default '0' COMMENT 'CDKEY withdrawal status, 0=No Withdrawal 1=Processing, 2=Approved, 3=Canceled, 4=Paid',
                    `cdk_withdrawal_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`custom_products_purchase_orders_id`)
                ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["custom_products_cb_purchase_orders"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `custom_products_cb_purchase_orders` (
                    `custom_products_cb_purchase_orders_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `custom_products_code_id` int(11) NOT NULL,
                    `purchase_orders_id` int(11) unsigned NOT NULL,
                    `purchase_orders_ref_id` varchar(32) default NULL COMMENT 'Ref ID for Payment Withdrawal',
                    `cdk_cb_status` enum('0','1','2') NOT NULL default '0' COMMENT 'CDK CB status, 0=No CB or DN 1=Charge Back, 2=Debit Note',
                    `cdk_cb_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for Withdrawal',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`custom_products_cb_purchase_orders_id`)
                ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

$add_new_tables["custom_products_cb"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `custom_products_cb` (
                    `custom_products_cb_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `custom_products_code_id` int(11) NOT NULL,
                    `cdk_cb_status` enum('0','1','2','3') NOT NULL default '0' COMMENT 'CDKEY Charge Back status, 0=Not set 1=Charge Back, 2=Debit Note, 3=CDK Issue',
                    `cdk_cb_date` datetime default '0000-00-00 00:00:00' COMMENT 'Start Processing Date for CDK Status Change',
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`custom_products_cb_id`)
                ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);

// Insert new records into admin_files table (for PO Consignment Payment Request)
$select_sql = " SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='purchase_orders.php' AND admin_files_is_boxes=1" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["consignment_payment.php"] = array(
        "insert" => " ('consignment_payment.php', 0, '".$row_sql['admin_files_id']."', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_insert_sql["printable_consignment_request.php"] = array(
        "insert" => " ('printable_consignment_request.php', 0, '".$row_sql['admin_files_id']."', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_insert_sql["popup_cdkey_list.php"] = array(
        "insert" => " ('popup_cdkey_list.php', 0, '".$row_sql['admin_files_id']."', '1') ",
        "update" => " admin_files_is_boxes=0, admin_files_to_boxes='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)");
}

// Insert new records into admin_files_actions table (for po consignment suppliers)
$select_sql = " SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . " 
                WHERE TRIM(LCASE(admin_files_name))='consignment_payment.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["CDK_NEW_PO"] = array(
        "insert" => " ('CDK_NEW_PO', 'New PO Consignment CDK Payment Request', '2', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=2, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["CDK_PROCESS_PO"] = array(
        "insert" => " ('CDK_PROCESS_PO', 'Process CDK PO', '4', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=4, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["CDK_CANCEL_PENDING_RECEIVE"] = array(
        "insert" => " ('CDK_CANCEL_PENDING_RECEIVE', 'Cancel Pending CDK PO', '5', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=5, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["CDK_VERIFY_PO"] = array(
        "insert" => " ('CDK_VERIFY_PO', 'Verify/unverify Completed Purchase Order', '7', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=7, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["CDK_MAKE_PAYMENT"] = array(
        "insert" => " ('CDK_MAKE_PAYMENT', 'Make Payment', '8', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=8, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["CDK_ADD_REMARK"] = array(
        "insert" => " ('CDK_ADD_REMARK', 'Add Remark', '10', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=10, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["CDK_VIEW_REMARK"] = array(
        "insert" => " ('CDK_VIEW_REMARK', 'View Remarks', '11', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=11, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    $admin_files_actions_insert_sql["CDK_PO_SET_PAID_STATUS"] = array(
        "insert" => " ('CDK_PO_SET_PAID_STATUS', 'Update API Withdrawal Status', '12', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=12, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}
// End of insert new records into admin_files table (for po_consignment payment)

// Insert tbl:custom_products_withdrawal
$cdk_date_cutoff = "2017-01-01 00:00:00";
$po_consignment_qry = "SELECT purchase_orders_id, supplier_id
              FROM " . TABLE_PURCHASE_ORDERS . "
              WHERE purchase_orders_issue_date >= '" . $cdk_date_cutoff . "'
              AND payment_type = 'g'
              AND purchase_orders_type = '0'
";
$po_consignment_result = tep_db_query($po_consignment_qry);
if (tep_db_num_rows($po_consignment_result)) {
    while ($po_consignment_row = tep_db_fetch_array($po_consignment_result)) {

        $cdk_qry = "SELECT custom_products_code_id
                    FROM " . TABLE_CUSTOM_PRODUCTS_CODE . "
                    WHERE purchase_orders_id = '" . (int) $po_consignment_row['purchase_orders_id'] . "'
        ";

        $cdk_result = tep_db_query($cdk_qry);
        if (tep_db_num_rows($cdk_result)) {
            $insert_sql = array();
            while ($cdk_row = tep_db_fetch_array($cdk_result)) {
                $cdk_withdrawal_qry = "SELECT custom_products_code_id
                                         FROM " . TABLE_CUSTOM_PRODUCTS_WITHDRAWAL . "
                                         WHERE custom_products_code_id = " . (int)$cdk_row['custom_products_code_id'];
                $cdk_withdrawal_result = tep_db_query($cdk_withdrawal_qry);
                if ($cdk_withdrawal_row = tep_db_fetch_array($cdk_withdrawal_result)) {
                    // Do Nothing
                } else {
                    $insert_sql[$cdk_row['custom_products_code_id']] = array("insert" => " (".$cdk_row['custom_products_code_id'].", ".$po_consignment_row['supplier_id'].") ");
                }
            }
            // Insert data into custom_products_withdrawal
            insert_new_records(TABLE_CUSTOM_PRODUCTS_WITHDRAWAL, "custom_products_code_id", $insert_sql, $DBTables, "(`custom_products_code_id`, `supplier_id`)");
        }
    }
}
?>