<?php

$add_new_tables = array();

$add_new_tables["products_payment_methods_restrictions"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `products_payment_methods_restrictions` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `products_id` int(11) NOT NULL,
                    `restriction_mode` varchar(255) NOT NULL,
                    `restriction_info` text NOT NULL,
                    `created_date` datetime,
                    `updated_date` datetime,
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);

$select_sql = "	SELECT admin_files_id, admin_groups_id 
                FROM " . TABLE_ADMIN_FILES . "
                WHERE TRIM(LCASE(admin_files_name))='categories.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_actions_insert_sql = array();
    $admin_files_actions_insert_sql["CATALOG_EDIT_PAYMENT_METHODS_RESTRICTIONS"] = array(
        "insert" => " ('CATALOG_EDIT_PAYMENT_METHODS_RESTRICTIONS', 'Edit Payment Methods Restrictions', " . $row_sql["admin_files_id"] . ",'1', 53)",
        "update" => " admin_files_sort_order=53, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' ");
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
?>