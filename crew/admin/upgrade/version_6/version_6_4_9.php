<?php

$select_sql = " SELECT admin_files_id 
                FROM " . TABLE_ADMIN_FILES . "
                WHERE admin_files_name='catalog.php' AND admin_files_is_boxes=1";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["products_payment_restrictions.php"] = array("insert" => " ('products_payment_restrictions.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='products_payment_restrictions.php' AND admin_files_is_boxes=0 ");
}

?>