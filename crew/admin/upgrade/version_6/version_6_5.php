<?php

$add_new_tables = array();
$add_new_tables["c2c_invoice_queue_api"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `c2c_invoice_queue_api` (
                `trans_id` bigint(20) NOT NULL,
                `type` varchar(128) NOT NULL,
                `created_at` int(11) NOT NULL,
                `updated_at` int(11) NOT NULL,
                PRIMARY KEY (`trans_id`,`type`,`created_at`)
             ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
?>