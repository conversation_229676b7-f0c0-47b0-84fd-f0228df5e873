<?php
# update record for OG & G2G
//visa
$conf_insert_sql = array();
$conf_insert_sql[588] = array("insert" => " (588, 'safecharge.api.3d.visa', 'Visa', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 588 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[588] = array("insert" => " (588, 'safecharge.api.3d.visa', 'Visa', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 588 AND site_id = 5");

//master
$conf_insert_sql = array();
$conf_insert_sql[589] = array("insert" => " (589, 'safecharge.api.3d.mc', 'Mastercard', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 589 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[589] = array("insert" => " (589, 'safecharge.api.3d.mc', 'Mastercard', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 589 AND site_id = 5");

//Maestro
$conf_insert_sql = array();
$conf_insert_sql[590] = array("insert" => " (590, 'safecharge.api.3d.maestro', 'Maestro', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 590 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[590] = array("insert" => " (590, 'safecharge.api.3d.maestro', 'Maestro', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 590 AND site_id = 5");

//Maestro UK
$conf_insert_sql = array();
$conf_insert_sql[591] = array("insert" => " (591, 'safecharge.api.3d.maestrouk', 'Maestro UK', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 591 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[591] = array("insert" => " (591, 'safecharge.api.3d.maestrouk', 'Maestro UK', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 591 AND site_id = 5");

//UnionPay
$conf_insert_sql = array();
$conf_insert_sql[592] = array("insert" => " (592, 'safecharge.api.3d.unionpay', 'UnionPay', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 592 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[592] = array("insert" => " (592, 'safecharge.api.3d.unionpay', 'UnionPay', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 592 AND site_id = 5");

//AMEX
$conf_insert_sql = array();
$conf_insert_sql[611] = array("insert" => " (611, 'safecharge.api.3d.amex', 'AMEX', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 611 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[611] = array("insert" => " (611, 'safecharge.api.3d.amex', 'AMEX', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 611 AND site_id = 5");

//EPS
$conf_insert_sql = array();
$conf_insert_sql[593] = array("insert" => " (593, 'safecharge.hpp.apmgw_EPS', 'EPS', 'SafeCharge', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 593 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[593] = array("insert" => " (593, 'safecharge.hpp.apmgw_EPS', 'EPS', 'SafeCharge', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 593 AND site_id = 5");

//Mistercash
$conf_insert_sql = array();
$conf_insert_sql[594] = array("insert" => " (594, 'safecharge.hpp.apmgw_MISTERCASH', 'Mistercash', 'SafeCharge', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 594 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[594] = array("insert" => " (594, 'safecharge.hpp.apmgw_MISTERCASH', 'Mistercash', 'SafeCharge', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 594 AND site_id = 5");

//NeoSurf
$conf_insert_sql = array();
$conf_insert_sql[595] = array("insert" => " (595, 'safecharge.hpp.apmgw_Neosurf', 'NeoSurf', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 595 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[595] = array("insert" => " (595, 'safecharge.hpp.apmgw_Neosurf', 'NeoSurf', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 595 AND site_id = 5");

//AstroPay
$conf_insert_sql = array();
$conf_insert_sql[596] = array("insert" => " (596, 'safecharge.hpp.apmgw_AstroPay', 'AstroPay', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 596 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[596] = array("insert" => " (596, 'safecharge.hpp.apmgw_AstroPay', 'AstroPay', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 596 AND site_id = 5");

//AstroPay Local Payments
$conf_insert_sql = array();
$conf_insert_sql[612] = array("insert" => " (612, 'safecharge.hpp.apmgw_Astropay_TEF', 'AstroPay Local Payments', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 612 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[612] = array("insert" => " (612, 'safecharge.hpp.apmgw_Astropay_TEF', 'AstroPay Local Payments', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 612 AND site_id = 5");

//Boleto Bancario
$conf_insert_sql = array();
$conf_insert_sql[597] = array("insert" => " (597, 'safecharge.hpp.apmgw_BOLETO', 'Boleto Bancario', 'SafeCharge', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 597 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[597] = array("insert" => " (597, 'safecharge.hpp.apmgw_BOLETO', 'Boleto Bancario', 'SafeCharge', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 597 AND site_id = 5");

//Wechat
$conf_insert_sql = array();
$conf_insert_sql[598] = array("insert" => " (598, 'safecharge.hpp.apmgw_WeChat', 'Wechat', 'SafeCharge', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 598 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[598] = array("insert" => " (598, 'safecharge.hpp.apmgw_WeChat', 'Wechat', 'SafeCharge', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 598 AND site_id = 5");

//Trustly
$conf_insert_sql = array();
$conf_insert_sql[599] = array("insert" => " (599, 'safecharge.hpp.apmgw_Trustly_DI', 'Trustly', 'SafeCharge', 572, 'pipwavePG', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 599 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[599] = array("insert" => " (599, 'safecharge.hpp.apmgw_Trustly_DI', 'Trustly', 'SafeCharge', 572, 'pipwavePG', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 599 AND site_id = 5");

//TrustPay
$conf_insert_sql = array();
$conf_insert_sql[600] = array("insert" => " (600, 'safecharge.hpp.apmgw_Trustpay', 'TrustPay', 'SafeCharge', 572, 'pipwavePG', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 600 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[600] = array("insert" => " (600, 'safecharge.hpp.apmgw_Trustpay', 'TrustPay', 'SafeCharge', 572, 'pipwavePG', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 600 AND site_id = 5");

//GiroPay
$conf_insert_sql = array();
$conf_insert_sql[601] = array("insert" => " (601, 'safecharge.hpp.apmgw_Giropay', 'GiroPay', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 601 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[601] = array("insert" => " (601, 'safecharge.hpp.apmgw_Giropay', 'GiroPay', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 601 AND site_id = 5");

//SofortBanking
$conf_insert_sql = array();
$conf_insert_sql[602] = array("insert" => " (602, 'safecharge.hpp.apmgw_Sofort', 'SofortBanking', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 602 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[602] = array("insert" => " (602, 'safecharge.hpp.apmgw_Sofort', 'SofortBanking', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 602 AND site_id = 5");

//PaysafeCard
$conf_insert_sql = array();
$conf_insert_sql[603] = array("insert" => " (603, 'safecharge.hpp.paysafecard', 'PaysafeCard', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 603 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[603] = array("insert" => " (603, 'safecharge.hpp.paysafecard', 'PaysafeCard', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 603 AND site_id = 5");

//Todito Cash
$conf_insert_sql = array();
$conf_insert_sql[604] = array("insert" => " (604, 'safecharge.hpp.apmgw_Todito_Cash', 'Todito Cash', 'SafeCharge', 572, 'pipwavePG', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 604 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[604] = array("insert" => " (604, 'safecharge.hpp.apmgw_Todito_Cash', 'Todito Cash', 'SafeCharge', 572, 'pipwavePG', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 604 AND site_id = 5");

//iDeal
$conf_insert_sql = array();
$conf_insert_sql[605] = array("insert" => " (605, 'safecharge.hpp.apmgw_iDeal', 'iDeal', 'SafeCharge', 572, 'pipwavePG', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 605 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[605] = array("insert" => " (605, 'safecharge.hpp.apmgw_iDeal', 'iDeal', 'SafeCharge', 572, 'pipwavePG', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 605 AND site_id = 5");

//Multibanco
$conf_insert_sql = array();
$conf_insert_sql[606] = array("insert" => " (606, 'safecharge.hpp.apmgw_MULTIBANCO', 'Multibanco', 'SafeCharge', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 606 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[606] = array("insert" => " (606, 'safecharge.hpp.apmgw_MULTIBANCO', 'Multibanco', 'SafeCharge', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 606 AND site_id = 5");

//Moneta
$conf_insert_sql = array();
$conf_insert_sql[607] = array("insert" => " (607, 'safecharge.hpp.apmgw_Moneta', 'Moneta', 'SafeCharge', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 607 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[607] = array("insert" => " (607, 'safecharge.hpp.apmgw_Moneta', 'Moneta', 'SafeCharge', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 607 AND site_id = 5");

//Yandex.Money
$conf_insert_sql = array();
$conf_insert_sql[608] = array("insert" => " (608, 'safecharge.hpp.apmgw_YANDEXMONEY', 'Yandex.Money', 'SafeCharge', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 608 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[608] = array("insert" => " (608, 'safecharge.hpp.apmgw_YANDEXMONEY', 'Yandex.Money', 'SafeCharge', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 608 AND site_id = 5");

//QIWI Wallet
$conf_insert_sql = array();
$conf_insert_sql[609] = array("insert" => " (609, 'safecharge.hpp.apmgw_QIWI', 'QIWI Wallet', 'SafeCharge', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 609 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[609] = array("insert" => " (609, 'safecharge.hpp.apmgw_QIWI', 'QIWI Wallet', 'SafeCharge', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 609 AND site_id = 5");

//POLi
$conf_insert_sql = array();
$conf_insert_sql[613] = array("insert" => " (613, 'safecharge.hpp.apmgw_POLI', 'POLi', 'SafeCharge', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 613 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[613] = array("insert" => " (613, 'safecharge.hpp.apmgw_POLI', 'POLi', 'SafeCharge', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 613 AND site_id = 5");

//OXXO
$conf_insert_sql = array();
$conf_insert_sql[610] = array("insert" => " (610, 'safecharge.hpp.apmgw_Oxxo', 'OXXO', 'SafeCharge', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 610 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[610] = array("insert" => " (610, 'safecharge.hpp.apmgw_Oxxo', 'OXXO', 'SafeCharge', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 610 AND site_id = 5");
?>