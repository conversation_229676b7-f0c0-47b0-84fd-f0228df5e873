<?php
// rating tags content
$tag1 = '<a href=\"http://www.sitejabber.com/requested-review?biz_id=5534bcfde4c70\" target=\"_blank\" style=\"width:100%;height:100%;display:block;\"></a>';
$tag2 = '<a href=\"https://www.trustpilot.com/evaluate/www.offgamers.com\" target=\"_blank\" style=\"width:100%;height:100%;display:block;\"></a>';

$tag3 = "var productsPurchased= 'URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=|URL=^SKU=^GTIN=^PRICE=';var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;ga.src = '//eval.bizrate.com/js/pos_90268.js';var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);";
$tag4 = '<a href=\"https://www.mywot.com/en/scorecard/offgamers.com\" target=\"_blank\" style=\"width:100%;height:100%;display:block;\"></a>';

$tag_trigger_content=json_encode(array('checkoutSuccessPopup("basic","' . $tag1 . '");', 'checkoutSuccessPopup("basic","' . $tag2 . '");', $tag3, 'checkoutSuccessPopup("basic","' . $tag4 . '");'));

$data[1] = array("update" => ("tag_trigger_content = '".tep_db_input($tag_trigger_content)."', last_modified_date = now()"));


update_records(TABLE_RATING_TAGS,'tag_id',$data ,$DBTables);


