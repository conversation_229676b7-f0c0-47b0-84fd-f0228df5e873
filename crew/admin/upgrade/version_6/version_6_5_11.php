<?php  
$add_new_tables = array();

$add_new_tables["cron_customer_account_closure"] = array (    
    "structure" => "CREATE TABLE IF NOT EXISTS `cron_customer_account_closure` (
                    `customers_id` int(11) unsigned NOT NULL COMMENT 'Customers ID',
                    `closure_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0-pending, 1-completed, 2-processing',
                    `created_at` int(11) NOT NULL,
                    `executed_at` int(11) NOT NULL,
                    `updated_at` int(11) NOT NULL,
                    `requested_by` varchar(255) default NULL,
                    PRIMARY KEY (`customers_id`)
                ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables ($add_new_tables, $DBTables);

// Insert new records into admin_files_actions table (for permissions on customer request account closure)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	// Closure Request permission
    $admin_files_actions_insert_sql["CUSTOMER_REQUEST_CLOSURE"] = array(
        "insert" => " ('CUSTOMER_REQUEST_CLOSURE', 'Set customer for account closure', '69', ".$row_sql["admin_files_id"].", '1') ",
        "update" => " admin_files_sort_order=69, admin_files_id='" . $row_sql["admin_files_id"] . "', admin_groups_id='1' "
    );
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_sort_order`, `admin_files_id`, `admin_groups_id`)");
}
// End of insert new records into admin_files_actions table (for permissions on customer request account closure)
?>