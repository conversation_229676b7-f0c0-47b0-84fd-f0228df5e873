<?php
// Alter tbl:log_api_restock
$alter_table = array(
    'log_api_restock' => array(
        array(
            'field_name' => 'api_cb_status',
            'field_attr' => "ENUM('0','1','2','3') NOT NULL DEFAULT '0' COMMENT 'API Charge Back status, 0=Not set 1=Charge Back, 2=API Issue, 3=Removed'",
        ),
        array(
            'field_name' => 'api_cb_temp_status',
            'field_attr' => "ENUM('0','1','2','3') NOT NULL DEFAULT '0' COMMENT 'API Charge Back temp status, 0=Not set 1=Charge Back, 2=API Issue, 3=Removed'",
        ),
    ),
    'api_replenish_cb' => array(
        array(
            'field_name' => 'api_cb_status',
            'field_attr' => "ENUM('0','1','2','3') NOT NULL DEFAULT '0' COMMENT 'API Charge Back status, 0=Not set 1=Charge Back, 2=API Issue, 3=Removed'",
        ),
        array(
            'field_name' => 'log_system_messages',
            'field_attr' => "text NOT NULL",
            'add_after' => 'api_cb_status',
        ),
    )
);

add_field($alter_table);
?>