<?php

$existing_products_checkout_fields = get_table_fields(TABLE_PRODUCTS_CHECKOUT_SETTING);

if (!in_array('min_checkout_quantity', $existing_products_checkout_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_PRODUCTS_CHECKOUT_SETTING . " ADD `min_checkout_quantity` INT(4) NOT NULL DEFAULT 0 AFTER `max_purchase_period`;");
}

if (!in_array('max_checkout_quantity', $existing_products_checkout_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_PRODUCTS_CHECKOUT_SETTING . " ADD `max_checkout_quantity` INT(4) NOT NULL DEFAULT 0 AFTER `min_checkout_quantity`;");
}

$existing_publisher_fields = get_table_fields(TABLE_PUBLISHERS);

if (!in_array('server_last_sync', $existing_publisher_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_PUBLISHERS . " ADD `server_last_sync` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' AFTER `sort_order`;");
}
