<?php
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.paysafecard'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.qiwiwallet'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.giropay'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0031'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0761'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0802'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0721'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0801'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0021'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0771'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0751'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0511'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ideal::0161'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'indomog.kpybca'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'indomog.intman'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'indomog.klkbca'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'indomog.bcatrf'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'indomog.mantrf'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'indomog.bnitrf'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'indomog.britrf'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'indomog.smsman'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'smart2pay.mercado'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.moneta'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.paysera'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.qiwi'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.toditocash'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.yandexmoney'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.eps'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.bancodobrasil'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.mrcash'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.sibs'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.beeline'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'offline_alipay.alipay_manual'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'offline_custom.customatm'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'paymaster24.pay24'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'molpay.enets'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'molpay.singpost'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'molpay.alipay'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'molpay.paysbuy'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'safecharge.hpp.apmgw_BOLETO'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'safecharge.hpp.apmgw_EPS'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'safecharge.hpp.apmgw_Giropay'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'safecharge.hpp.apmgw_iDeal'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'safecharge.hpp.apmgw_MISTERCASH'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'safecharge.hpp.apmgw_Neosurf'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'safecharge.hpp.paysafecard'");
?>