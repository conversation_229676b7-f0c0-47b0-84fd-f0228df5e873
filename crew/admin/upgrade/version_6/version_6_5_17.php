<?php
// Add permission to view Deleted customers info in orders page
$select_sql = "	SELECT admin_files_id, admin_groups_id
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();
	
	$admin_files_actions_insert_sql["ORDER_DELETED_CUSTOMER_INFO"] = array("insert" => " ('ORDER_DELETED_CUSTOMER_INFO', 'View Deleted Customers Account Info', ".$row_sql["admin_files_id"].", '1', 54)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}

// Update cost for ecoVoucher -2%
$update_sql[TABLE_LOG_API_RESTOCK] = array(
    array(
        "field_name" => "currency_settle_amount",
        "update" => " currency_settle_amount = currency_settle_amount - (currency_settle_amount * 0.02)",
        "where_str" => " flag_state = 'S' AND method = 'issue' AND api_provider = 'ECOVOUCHER' AND currency_settle_amount = amount"
    )
);

advance_update_records($update_sql, $DBTables);

// Update settle_amount after -2%
$update2_sql[TABLE_LOG_API_RESTOCK] = array(
    array(
        "field_name" => "settle_amount",
        "update" => " settle_amount = (currency_settle_amount / currency_rate)",
        "where_str" => " flag_state = 'S' AND method = 'issue' AND api_provider = 'ECOVOUCHER'"
    )
);

advance_update_records($update2_sql, $DBTables);
?>