<?php
// Insert new fields into `po_company` table
$add_new_field['po_company'] = array (	
	array (
		"field_name" => "po_company_status",
		"field_attr" => "INT UNSIGNED DEFAULT '0' NOT NULL",
		"add_after" => 'po_company_code'
	)
);
// End of insert new fields into `po_company` table

add_field($add_new_field);

// Create po_company_remarks_history tables
$add_new_tables = array();

$add_new_tables['po_company_remarks_history'] = array(
	"structure" => "CREATE TABLE IF NOT EXISTS `po_company_remarks_history` (
					`po_company_remarks_history_id` int(11) NOT NULL auto_increment,
					`po_company_id` int(11) NOT NULL default '0',
					`date_remarks_added` datetime default NULL,
					`remarks` text,
					`remarks_added_by` varchar(255) default NULL,
					PRIMARY KEY  (`po_company_remarks_history_id`) 
				) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
	"data" => "");

add_new_tables($add_new_tables, $DBTables);
// End of create po_company_remarks_history

// Define po_company_id as index key in po_company_remarks_history table
add_index_key('po_company_remarks_history', 'index_po_company_id', 'index', 'po_company_id', $DBTables);
// End of define po_company_id as index key in po_company_remarks_history table
?>