<?php
# update record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[614] = array("insert" => " (614, 'molpay.affin', 'Affin Bank', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 614 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[614] = array("insert" => " (614, 'molpay.affin', 'Affin Bank', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 614 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[615] = array("insert" => " (615, 'molpay.alliance', 'Alliance online', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 615 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[615] = array("insert" => " (615, 'molpay.alliance', 'Alliance online', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 615 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[616] = array("insert" => " (616, 'molpay.ambank', 'Ambank', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 616 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[616] = array("insert" => " (616, 'molpay.ambank', 'Ambank', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 616 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[617] = array("insert" => " (617, 'molpay.bankislam', 'Bank Islam', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 617 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[617] = array("insert" => " (617, 'molpay.bankislam', 'Bank Islam', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 617 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[618] = array("insert" => " (618, 'molpay.muamalat', 'Bank Muamalat', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 618 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[618] = array("insert" => " (618, 'molpay.muamalat', 'Bank Muamalat', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 618 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[619] = array("insert" => " (619, 'molpay.bank_rakyat', 'Bank Rakyat', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 619 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[619] = array("insert" => " (619, 'molpay.bank_rakyat', 'Bank Rakyat', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 619 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[620] = array("insert" => " (620, 'molpay.bsn', 'BSN', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 620 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[620] = array("insert" => " (620, 'molpay.bsn', 'BSN', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 620 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[621] = array("insert" => " (621, 'molpay.cimb', 'CIMB Clicks', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 621 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[621] = array("insert" => " (621, 'molpay.cimb', 'CIMB Clicks', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 621 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[622] = array("insert" => " (622, 'molpay.hlb', 'Hong Leong Connect', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 622 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[622] = array("insert" => " (622, 'molpay.hlb', 'Hong Leong Connect', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 622 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[623] = array("insert" => " (623, 'molpay.hcbc', 'HSBC Bank', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 623 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[623] = array("insert" => " (623, 'molpay.hcbc', 'HSBC Bank', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 623 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[624] = array("insert" => " (624, 'molpay.kfh', 'Kuwait Finance House Bank', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 624 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[624] = array("insert" => " (624, 'molpay.kfh', 'Kuwait Finance House Bank', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 624 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[625] = array("insert" => " (625, 'molpay.ocbc', 'OCBC Online', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 625 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[625] = array("insert" => " (625, 'molpay.ocbc', 'OCBC Online', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 625 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[626] = array("insert" => " (626, 'molpay.pbb', 'PBe Online', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 626 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[626] = array("insert" => " (626, 'molpay.pbb', 'PBe Online', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 626 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[627] = array("insert" => " (627, 'molpay.rhb', 'RHB Now', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 627 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[627] = array("insert" => " (627, 'molpay.rhb', 'RHB Now', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 627 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[628] = array("insert" => " (628, 'molpay.scb', 'Standard Chartered Online', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 628 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[628] = array("insert" => " (628, 'molpay.scb', 'Standard Chartered Online', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 628 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[629] = array("insert" => " (629, 'molpay.uob', 'UOB Bank', 'MOLPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 629 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[629] = array("insert" => " (629, 'molpay.uob', 'UOB Bank', 'MOLPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 629 AND site_id = 5");

?>