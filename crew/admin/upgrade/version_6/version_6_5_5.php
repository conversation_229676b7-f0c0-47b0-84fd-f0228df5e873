<?php
# update record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[637] = array("insert" => " (637, 'adyen.api.mc', 'API Mastercard', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 637 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[637] = array("insert" => " (637, 'adyen.api.mc', 'API Mastercard', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 637 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[638] = array("insert" => " (638, 'adyen.api.visa', 'API Visa', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 638 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[638] = array("insert" => " (638, 'adyen.api.visa', 'API Visa', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 638 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[639] = array("insert" => " (639, 'adyen.api.maestro', 'API Maestro', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 639 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[639] = array("insert" => " (639, 'adyen.api.maestro', 'API Maestro', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 639 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[640] = array("insert" => " (640, 'adyen.api.maestrouk', 'API Maestro UK', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 640 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[640] = array("insert" => " (640, 'adyen.api.maestrouk', 'API Maestro UK', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 640 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[641] = array("insert" => " (641, 'adyen.api.amex', 'API Amex', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 641 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[641] = array("insert" => " (641, 'adyen.api.amex', 'API Amex', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 641 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[642] = array("insert" => " (642, 'adyen.api.unionpay', 'API China UnionPay', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 642 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[642] = array("insert" => " (642, 'adyen.api.unionpay', 'API China UnionPay', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 642 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[643] = array("insert" => " (643, 'adyen.api.diners', 'API Diners', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 643 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[643] = array("insert" => " (643, 'adyen.api.diners', 'API Diners', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 643 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[644] = array("insert" => " (644, 'adyen.api.discover', 'API Discover', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 644 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[644] = array("insert" => " (644, 'adyen.api.discover', 'API Discover', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 644 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[645] = array("insert" => " (645, 'adyen.api.jcb', 'API JCB', 'Adyen', 313, 'adyen', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 645 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[645] = array("insert" => " (645, 'adyen.api.jcb', 'API JCB', 'Adyen', 313, 'adyen', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 645 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[633] = array("insert" => " (633, 'coda.dtac', 'DTAC', 'CodaPayment', 297, 'coda', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 633 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[633] = array("insert" => " (633, 'coda.dtac', 'DTAC', 'CodaPayment', 297, 'coda', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 633 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[634] = array("insert" => " (634, 'coda.cat', 'CAT', 'CodaPayment', 297, 'coda', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 634 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[634] = array("insert" => " (634, 'coda.cat', 'CAT', 'CodaPayment', 297, 'coda', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 634 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[632] = array("insert" => " (632, 'coda.rabbitline', 'Rabbit LINE Pay', 'CodaPayment', 297, 'coda', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 632 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[632] = array("insert" => " (632, 'coda.rabbitline', 'Rabbit LINE Pay', 'CodaPayment', 297, 'coda', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 632 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[631] = array("insert" => " (631, 'coda.indomaret', 'Indomaret', 'CodaPayment', 297, 'coda', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 631 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[631] = array("insert" => " (631, 'coda.indomaret', 'Indomaret', 'CodaPayment', 297, 'coda', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 631 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[630] = array("insert" => " (630, 'coda.gopay', 'Go-Pay', 'CodaPayment', 297, 'coda', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 630 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[630] = array("insert" => " (630, 'coda.gopay', 'Go-Pay', 'CodaPayment', 297, 'coda', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 630 AND site_id = 5");
?>