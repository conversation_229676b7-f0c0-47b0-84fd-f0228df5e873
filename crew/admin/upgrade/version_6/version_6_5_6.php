<?php  
$add_new_tables = array();

$add_new_tables["categories_payment_methods_restrictions"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `categories_payment_methods_restrictions` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `categories_id` int(11) NOT NULL,
                    `restriction_mode` varchar(255) NOT NULL,
                    `restriction_info` text NOT NULL,
                    `created_date` datetime,
                    `updated_date` datetime,
                    `changed_by` varchar(128) DEFAULT 'system',
                    PRIMARY KEY (`id`)
                  ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");
add_new_tables($add_new_tables, $DBTables);

// Set indexing for categories_payment_methods_restrictions
add_index_key('categories_payment_methods_restrictions', 'index_categories_id', 'index', 'categories_id', $DBTables);
add_index_key('categories_payment_methods_restrictions', 'index_restriction_mode', 'index', 'restriction_mode', $DBTables);
// End indexing for categories_payment_methods_restrictions

// Set indexing for products_payment_methods_restrictions
add_index_key(TABLE_PRODUCTS_PAYMENT_METHODS_RESTRICTIONS, 'index_products_id', 'index', 'products_id', $DBTables);
add_index_key(TABLE_PRODUCTS_PAYMENT_METHODS_RESTRICTIONS, 'index_restriction_mode', 'index', 'restriction_mode', $DBTables);
// End set indexing for products_payment_methods_restrictions
?>