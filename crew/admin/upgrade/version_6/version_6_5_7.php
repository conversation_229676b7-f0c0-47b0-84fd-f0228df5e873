<?php
    $cron_progress_task_select_sql = "SELECT cron_process_track_in_action
    FROM cron_process_track
    WHERE cron_process_track_filename = 'cron_sync_algolia.php'";
    $cron_progress_task_result_sql = tep_db_query($cron_progress_task_select_sql);
    if (!tep_db_num_rows($cron_progress_task_result_sql)) {
        $cron_process_track_array = array(
            'cron_process_track_in_action' => 0,
            'cron_process_track_start_date' => date('Y-m-d H:i:s', '1526538285'),
            'cron_process_track_failed_attempt' => 0,
            'cron_process_track_filename' => 'cron_sync_algolia.php'
        );
        tep_db_perform('cron_process_track', $cron_process_track_array);
    }