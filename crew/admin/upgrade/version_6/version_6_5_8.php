<?php
# update record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[646] = array("insert" => " (646, 'offline_paypal.masspayment', 'PayPal MassPayment', 'Offline', 12, 'offline', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 646 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[646] = array("insert" => " (646, 'offline_paypal.masspayment', 'PayPal MassPayment', 'Offline', 12, 'offline', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 646 AND site_id = 5");
?>