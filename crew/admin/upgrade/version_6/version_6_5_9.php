<?php
# update record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[646] = array("insert" => " (646, 'offline_paypal.masspayment', 'PayPal MassPayment', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 646 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[646] = array("insert" => " (646, 'offline_paypal.masspayment', 'PayPal MassPayment', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 646 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[648] = array("insert" => " (648, 'offline_pbbank_billpayment.pbe_billpayment', 'Pbe Bill Payment', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 648 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[648] = array("insert" => " (648, 'offline_pbbank_billpayment.pbe_billpayment', 'Pbe Bill Payment', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 648 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[655] = array("insert" => " (655, 'eghl.affin_bank', 'Affin Bank', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 655 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[655] = array("insert" => " (655, 'eghl.affin_bank', 'Affin Bank', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 655 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[651] = array("insert" => " (651, 'eghl.alliance_bank', 'Alliance Bank (Personal)', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 651 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[651] = array("insert" => " (651, 'eghl.alliance_bank', 'Alliance Bank (Personal)', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 651 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[660] = array("insert" => " (660, 'eghl.ambank', 'AmBank', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 660 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[660] = array("insert" => " (660, 'eghl.ambank', 'AmBank', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 660 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[652] = array("insert" => " (652, 'eghl.bankislam', 'Bank Islam', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 652 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[652] = array("insert" => " (652, 'eghl.bankislam', 'Bank Islam', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 652 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[653] = array("insert" => " (653, 'eghl.bank_muamalat', 'Bank Muamalat', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 653 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[653] = array("insert" => " (653, 'eghl.bank_muamalat', 'Bank Muamalat', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 653 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[650] = array("insert" => " (650, 'eghl.bank_raykat', 'Bank Rakyat', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 650 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[650] = array("insert" => " (650, 'eghl.bank_raykat', 'Bank Rakyat', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 650 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[663] = array("insert" => " (663, 'eghl.bsn', 'BSN Online', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 663 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[663] = array("insert" => " (663, 'eghl.bsn', 'BSN Online', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 663 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[662] = array("insert" => " (662, 'eghl.cimb', 'CIMB Clicks', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 662 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[662] = array("insert" => " (662, 'eghl.cimb', 'CIMB Clicks', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 662 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[658] = array("insert" => " (658, 'eghl.hlb', 'Hong Leong Connect', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 658 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[658] = array("insert" => " (658, 'eghl.hlb', 'Hong Leong Connect', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 658 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[664] = array("insert" => " (664, 'eghl.hsbc', 'HSBC Online', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 664 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[664] = array("insert" => " (664, 'eghl.hsbc', 'HSBC Online', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 664 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[654] = array("insert" => " (654, 'eghl.kfh', 'Kuwait Finance House', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 654 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[654] = array("insert" => " (654, 'eghl.kfh', 'Kuwait Finance House', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 654 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[656] = array("insert" => " (656, 'eghl.ocbc', 'OCBC Online', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 656 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[656] = array("insert" => " (656, 'eghl.ocbc', 'OCBC Online', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 656 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[649] = array("insert" => " (649, 'eghl.pbebank', 'PBe Online', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 649 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[649] = array("insert" => " (649, 'eghl.pbebank', 'PBe Online', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 649 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[661] = array("insert" => " (661, 'eghl.rhbnow', 'RHB Now', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 661 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[661] = array("insert" => " (661, 'eghl.rhbnow', 'RHB Now', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 661 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[657] = array("insert" => " (657, 'eghl.sc_bank', 'Standard Chartered Online', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 657 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[657] = array("insert" => " (657, 'eghl.sc_bank', 'Standard Chartered Online', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 657 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[659] = array("insert" => " (659, 'eghl.uob', 'UOB', 'eGHL', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 659 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[659] = array("insert" => " (659, 'eghl.uob', 'UOB', 'eGHL', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 659 AND site_id = 5");

?>