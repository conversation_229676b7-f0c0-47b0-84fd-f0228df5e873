<?php
# update record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[694] = array("insert" => " (694, 'eghl.eghl_mc', 'Mastercard', 'eGHL', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 694 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[694] = array("insert" => " (694, 'eghl.eghl_mc', 'Mastercard', 'eGHL', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 694 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[695] = array("insert" => " (695, 'eghl.eghl_visa', 'Visa', 'eGHL', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 695 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[695] = array("insert" => " (695, 'eghl.eghl_visa', 'Visa', 'eGHL', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 695 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[696] = array("insert" => " (696, 'jompay.jompay', 'JomPAY', 'JomPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 696 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[696] = array("insert" => " (696, 'jompay.jompay', 'JomPAY', 'JomPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 696 AND site_id = 5");

?>