<?php

$select_sql = "SELECT * FROM `store_credit_history` WHERE `store_credit_history_currency_id` = 0 AND store_credit_history_date > '2019-03-01' ORDER BY store_credit_history_date ASC";

$result_sql = tep_db_query($select_sql);

while ($row_sql = tep_db_fetch_array($result_sql)) {
    $sc_history_log = "SELECT * FROM `store_credit_history` WHERE customer_id = {$row_sql['customer_id']} AND store_credit_history_id < {$row_sql['store_credit_history_id']} ORDER BY store_credit_history_id DESC LIMIT 1";

    $sc_result_log = tep_db_query($sc_history_log);

    if ($sc_row = tep_db_fetch_array($sc_result_log)) {
        $reversible_sc = (double)$sc_row['store_credit_history_r_after_balance'];
        $irreversible_sc = (double)$sc_row['store_credit_history_nr_after_balance'];
        if ($row_sql['store_credit_account_type'] == 'NR') {
            $irreversible_sc = $irreversible_sc + (double)$row_sql['store_credit_history_credit_amount'];
        } elseif ($row_sql['store_credit_account_type'] == 'R') {
            $reversible_sc = $reversible_sc + (double)$row_sql['store_credit_history_credit_amount'];
        }

        $data[$row_sql['store_credit_history_id']] = array(
            "update" => (
                    "store_credit_history_currency_id = '" . tep_db_prepare_input($sc_row['store_credit_history_currency_id']) . "'," .
                    "store_credit_history_r_after_balance = '" . tep_db_prepare_input($reversible_sc) . "'," .
                    "store_credit_history_nr_after_balance = '" . tep_db_prepare_input($irreversible_sc)) . "'"
        );

        update_records(TABLE_STORE_CREDIT_HISTORY, 'store_credit_history_id', $data, $DBTables);
    }
}