<?php

$select_sql = "SELECT * FROM `log_api_restock` WHERE `custom_products_code_id` > 0 AND flag_state <> 'S' AND `api_provider` = 'CODESWHOLESALE'";

$result_sql = tep_db_query($select_sql);

while ($row_sql = tep_db_fetch_array($result_sql)) {
    $data[$row_sql['id']] = array(
        "update" => (
                "flag_state = '" . tep_db_prepare_input('S') . "'," .
                "method = '" . tep_db_prepare_input('/v2/orders') . "'"
            )
    );

    update_records(TABLE_LOG_API_RESTOCK, 'id', $data, $DBTables);
}