<?php 
// Insert new records into admin_files_actions table (for permission on editing restock account in supplier pricing page, view payment info in supplier order page)

// Update records in buyback_status table (Update name)
$buyback_status_update_sql = array();
$buyback_status_update_array = array();

$buyback_status_update_array = array(
    1 => array(
        1 => "'Delivering'",
        2 => "'交易中'",
        3 => "'交易中'",
        4 => "'Sedang Dikirim'",
    ),
    2 => array(
        1 => "'Delivered'",
        2 => "'已交易'",
        3 => "'已交易'",
        4 => "'Terkirim'"
    ),
    3 => array(
        1 => "'Completed'",
        2 => "'已完成'",
        3 => "'已完成'",
        4 => "'Selesai'"
    ),
    4 => array(
        1 => "'Cancelled'",
        2 => "'已取消'",
        3 => "'已取消'",
        4 => "'Dibatalkan'"
    )
);

foreach($buyback_status_update_array as $k => $v){
    foreach($v as $key => $value){
        $buyback_status_update_sql[TABLE_BUYBACK_STATUS][] = array(
            "field_name" => "buyback_status_name",
            "update" => " buyback_status_name=" . $value,
            "where_str" => " language_id=".$key." AND buyback_status_id = ".$k." ");
    }
}
advance_update_records($buyback_status_update_sql, $DBTables);
// End of update records in buyback_status table (Update name)


// Insert new status
$order_status = array(
    1 => "New order",
    2 => "新订单",
    3 => "新订单",
    4 => "Pesanan Baru"
);

foreach ($order_status as $key => $val) {
    $conf_insert_sql = array();
    $conf_insert_sql[$key] = array(
        "insert" => " ('" . 5 . "', $key, '" . $val . "', '" . "5') ",
        "update" => "buyback_status_id='" . 5 . "', language_id='".$key."', buyback_status_name='".$val."', buyback_status_sort_order='5'");

    insert_new_records(TABLE_BUYBACK_STATUS, "buyback_status_id", $conf_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", "buyback_status_id = " . 5 . " AND language_id = $key");
}

$order_status_preparing = array(
            1 => "Preparing",
            2 => "备货中",
            3 => "备货中",
            4 => "Persediaan"
);
foreach ($order_status_preparing as $key => $val) {
    $conf_insert_sql = array();
    $conf_insert_sql[$key] = array(
        "insert" => " ('" . 6 . "', $key, '" . $val . "', '" . "25') ",
        "update" => "buyback_status_id='" . 6 . "', language_id='".$key."', buyback_status_name='".$val."', buyback_status_sort_order='25'"
    );

    insert_new_records(TABLE_BUYBACK_STATUS, "buyback_status_id", $conf_insert_sql, $DBTables, "(buyback_status_id, language_id, buyback_status_name, buyback_status_sort_order)", "buyback_status_id = " . 6 . " AND language_id = $key");
}
//end of inserting new status

// Insert new records into status_configuration table (for B status movement COMPLETED to PENDING)
$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array(
    "insert" => " ('B', '5', '4', '', '', '-1') ",
    "update" => "status_configuration_trans_type='B', status_configuration_source_status_id=5, status_configuration_destination_status_id=4, status_configuration_user_groups_id='', status_configuration_manual_notification='', status_configuration_auto_notification='-1'"
);
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", " status_configuration_trans_type='B' AND status_configuration_source_status_id='5' AND status_configuration_destination_status_id='4' ");


$status_configuration_insert_sql = array();
$status_configuration_insert_sql["B"] = array(
    "insert" => " ('B', '6', '4', '', '', '-1') ",
    "update" => "status_configuration_trans_type='B', status_configuration_source_status_id=6, status_configuration_destination_status_id=4, status_configuration_user_groups_id='', status_configuration_manual_notification='', status_configuration_auto_notification='-1'"
);
insert_new_records(TABLE_STATUS_CONFIGURATION, "status_configuration_trans_type", $status_configuration_insert_sql, $DBTables, "(status_configuration_trans_type, status_configuration_source_status_id, status_configuration_destination_status_id, status_configuration_user_groups_id, status_configuration_manual_notification, status_configuration_auto_notification)", " status_configuration_trans_type='B' AND status_configuration_source_status_id='6' AND status_configuration_destination_status_id='4' ");
?>

?>