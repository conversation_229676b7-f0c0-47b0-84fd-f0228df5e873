<?php
# update record for OG & G2G
# iPay88
$conf_insert_sql = array();
$conf_insert_sql[758] = array("insert" => " (758, 'ipay88.grabpay', 'Grabpay', 'iPay88', 27, 'iPay88', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 758 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[758] = array("insert" => " (758, 'ipay88.grabpay', 'Grabpay', 'iPay88', 27, 'iPay88', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 758 AND site_id = 5");
?>