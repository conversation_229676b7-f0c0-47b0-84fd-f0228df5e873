<?php
#Insert missing country and update aft_risk_type to medium.
$messy_countries_json = '[{"name":"\u00c5land Islands","iso_code_2":"AX","iso_code_3":"ALA","default_currency":"EUR","dialing_code":"358","risk_level":"risk_level_low"}, {"name":"Saint Barth\u00e9lemy","iso_code_2":"BL","iso_code_3":"BLM","default_currency":"EUR","dialing_code":"590","risk_level":"risk_level_medium"}, {"name":"Bonaire","iso_code_2":"BQ","iso_code_3":"BES","default_currency":"USD","dialing_code":"5997","risk_level":"risk_level_medium"}, {"name":"Democratic Republic of the Congo","iso_code_2":"CD","iso_code_3":"COD","default_currency":"CDF","dialing_code":"243","risk_level":"risk_level_medium"}, {"name":"Cura\u00e7ao","iso_code_2":"CW","iso_code_3":"CUW","default_currency":"ANG","dialing_code":"5999","risk_level":"risk_level_medium"}, {"name":"Isle of Man","iso_code_2":"IM","iso_code_3":"IMN","default_currency":"GBP","dialing_code":"44","risk_level":"risk_level_medium"}, {"name":"Jersey","iso_code_2":"JE","iso_code_3":"JEY","default_currency":"GBP","dialing_code":"44","risk_level":"risk_level_medium"}, {"name":"Saint Martin","iso_code_2":"MF","iso_code_3":"MAF","default_currency":"EUR","dialing_code":"590","risk_level":"risk_level_medium"}, {"name":"South Sudan","iso_code_2":"SS","iso_code_3":"SSD","default_currency":"USD","dialing_code":"211","risk_level":"risk_level_medium"}, {"name":"Sint Maarten","iso_code_2":"SX","iso_code_3":"SXM","default_currency":"ANG","dialing_code":"1","risk_level":"risk_level_medium"}, {"name":"Republic of Kosovo","iso_code_2":"XK","iso_code_3":"KOS","default_currency":"EUR","dialing_code":"383","risk_level":"risk_level_medium"}]';
$messy_countries = json_decode($messy_countries_json, 1);
$structured_countries = null;
foreach($messy_countries as $key => $country_arr){
    $country_array = array(
        'countries_name' => $country_arr['name'],
        'countries_iso_code_2' => $country_arr['iso_code_2'],
        'countries_iso_code_3' => $country_arr['iso_code_3'],
        'countries_currencies_id' => 1,
        'countries_international_dialing_code' => $country_arr['dialing_code'],
        'address_format_id' => 1,
        'maxmind_support' => 1,
        'aft_risk_type' => 'MEDIUM',
        'countries_display' => 1,
        'telesign_support' => 1
    );

    $country_sel_sql = "SELECT countries_id
    FROM countries
    WHERE countries_name = '".$country_arr['name'] ."'
        AND countries_iso_code_2 = '".$country_arr['iso_code_2'] . "'";
    $country_res_sql = tep_db_query($country_sel_sql);

    if (!tep_db_num_rows($country_res_sql)) {
        tep_db_perform('countries', $country_array);
    } else {
        $country_row = tep_db_fetch_array($country_res_sql);
        tep_db_perform('countries', $country_array, 'update', "countries_id = " . $country_row['countries_id']);
    }
}
?>