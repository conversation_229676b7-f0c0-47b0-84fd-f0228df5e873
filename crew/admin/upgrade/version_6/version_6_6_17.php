<?php
// update Skrill to NRP
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'moneybooker.neteller'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'moneybooker.rapidtransfer'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'moneybooker.skrill'");
?>