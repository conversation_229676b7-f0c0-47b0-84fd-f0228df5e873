<?php
# update record for OG & G2G
# DaoPay
$conf_insert_sql = array();
$conf_insert_sql[760] = array("insert" => " (760, 'daopay.idl', 'iDeal', 'DaoPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 760 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[760] = array("insert" => " (760, 'daopay.idl', 'iDeal', 'DaoPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 760 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[761] = array("insert" => " (761, 'daopay.sb', 'SofortBanking', 'DaoPay', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 761 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[761] = array("insert" => " (761, 'daopay.sb', 'SofortBanking', 'DaoPay', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 761 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[762] = array("insert" => " (762, 'daopay.mrc', 'Mistercash', 'DaoPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 762 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[762] = array("insert" => " (762, 'daopay.mrc', 'Mistercash', 'DaoPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 762 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[763] = array("insert" => " (763, 'daopay.gp', 'Giropay', 'DaoPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 763 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[763] = array("insert" => " (763, 'daopay.gp', 'Giropay', 'DaoPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 763 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[764] = array("insert" => " (764, 'daopay.pays', 'Paysera', 'DaoPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 764 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[764] = array("insert" => " (764, 'daopay.pays', 'Paysera', 'DaoPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 764 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[765] = array("insert" => " (765, 'daopay.eps', 'EPS', 'DaoPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 765 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[765] = array("insert" => " (765, 'daopay.eps', 'EPS', 'DaoPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 765 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[766] = array("insert" => " (766, 'daopay.poli', 'POLi', 'DaoPay', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 766 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[766] = array("insert" => " (766, 'daopay.poli', 'POLi', 'DaoPay', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 766 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[759] = array("insert" => " (759, 'daopay.psc', 'PaySafeCard', 'DaoPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 759 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[759] = array("insert" => " (759, 'daopay.psc', 'PaySafeCard', 'DaoPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 759 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[767] = array("insert" => " (767, 'daopay.qiwi', 'QIWI', 'DaoPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 767 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[767] = array("insert" => " (767, 'daopay.qiwi', 'QIWI', 'DaoPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 767 AND site_id = 5");