<?php
# update payment record for OG & G2G
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pipwave_payment_code = 'moneybooker.astropaycash'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pipwave_payment_code = 'moneybooker.astropayoffline'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pipwave_payment_code = 'moneybooker.astropayonline'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pipwave_payment_code = 'moneybooker.neteller'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pipwave_payment_code = 'moneybooker.rapidtransfer'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pipwave_payment_code = 'moneybooker.skrill'");