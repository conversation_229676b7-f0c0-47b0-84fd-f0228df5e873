<?php
foreach ([
    "13102272" => "115105391",
    "13102354" => "115106397",
    "13102471" => "115107781",
    "13102485" => "115108715",
    "13102738" => "115111501",
    "13102762" => "115111885",
    "13102769" => "115111953",
    "13102990" => "115114225",
    "13103171" => "115116365",
    "13103193" => "115116533",
    "13103392" => "115118519",
    "13103442" => "115118981",
    "13103443" => "115118983",
    "13103618" => "115120813",
    "13103739" => "115122553",
    "13104033" => "115127575",
    "13104397" => "115132005",
    "13104903" => "115136291",
    "13105554" => "115142523",
    "13106063" => "115148661",
    "13106280" => "115150759",
    "13106283" => "115150791",
    "13106313" => "115151081",
    "13106800" => "115156315",
    "13106960" => "115157987",
    "13107053" => "115158809",
    "13107199" => "115160107",
    "13107282" => "115161025",
    "13107501" => "115163365",
    "13107738" => "115166003",
    "13108029" => "115169385",
    "13108143" => "115170397",
    "13108144" => "115170411",
    "13108291" => "115171961",
    "13109376" => "115179353",
    "13109743" => "115191551",
    "13110518" => "115197907",
    "13110925" => "115202663",
    "13111316" => "115207385",
    "13111626" => "115210729",
    "13111984" => "115214441",
    "13112223" => "115216975",
    "13112245" => "115217297",
    "13112371" => "115219217",
    "13112597" => "115222787",
    "13112737" => "115224635",
    "13112745" => "115224689",
    "13112877" => "115226499",
    "13113280" => "115230203",
    "13113340" => "115230647",
    "13113563" => "115232855",
    "13113726" => "115234357",
    "13114247" => "115238901",
    "13114606" => "115241945",
    "13115237" => "115248143",
    "13115538" => "115250993",
    "13115761" => "115253553",
    "13116431" => "115261199",
    "13116525" => "115262239",
    "13116580" => "115262887",
    "13119506" => "115292465",
    "13119585" => "115293799",
    "13119709" => "115294945",
    "13119763" => "115295431",
    "13120039" => "115298143",
    "13120061" => "115298409",
    "13120863" => "115308147",
    "13121362" => "115313461",
    "13121364" => "115313485",
    "13121400" => "115313803",
    "13121753" => "115316767",
    "13121783" => "115317091",
    "13122431" => "115322753",
    "13122456" => "115323031",
    "13122794" => "115325373",
    "13122893" => "115326311",
    "13123090" => "115327915",
    "13123230" => "115329295",
    "13123666" => "115332933",
    "13123799" => "115334095",
    "13124099" => "115336437",
    "13124838" => "115343785",
    "13125064" => "115346435",
    "13125302" => "115349415",
    "13126503" => "115360545",
    "13126790" => "115362623",
    "13127132" => "115368101",
    "13128486" => "115379305",
    "13129046" => "115387783",
    "13129125" => "115388565",
    "13129571" => "115394033",
    "13130062" => "115399315",
    "13130420" => "115402939",
    "13130936" => "115407595",
    "13131106" => "115409075",
    "13131436" => "115412103",
    "13131738" => "115414663",
    "13131958" => "115416555",
    "13132072" => "115417867",
    "13132185" => "115419095",
    "13132246" => "115419729",
    "13132296" => "115420263",
    "13132310" => "115420387",
    "13132534" => "115422807",
    "13133207" => "115430857",
    "13133331" => "115432807",
    "13133403" => "115433733",
    "13134444" => "115443389",
] as $txn_id => $pg_txn_id) {
    tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT . " SET `pg_txn_id`='" . $pg_txn_id . "' WHERE (`orders_id` = " . $txn_id . ") AND (`pg_txn_id` = " . $txn_id . ") AND (`payment_method_code` LIKE 'daopay.%')");
}