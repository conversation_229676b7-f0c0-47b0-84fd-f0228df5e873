<?php
// Database update for eGHL ECI status

// Get analysis_credit_card_info data starting date 2018-12-26 00:00:00

$startDate = '2018-12-26 00:00:00';

$sql = "SELECT acci.orders_id, pp.pg_raw_data
        FROM " . TABLE_ANALYSIS_CREDIT_CARD_INFO . " AS acci
        INNER JOIN " . TABLE_PIPWAVE_PAYMENT . " AS pp 
        	ON pp.orders_id = acci.orders_id 
        	AND pp.payment_method_code LIKE 'eghl%'
        WHERE acci.created_at >= '" . $startDate . "'";

$result = tep_db_query($sql);

// Process pg_raw_data & update analysis_credit_card_info with the right ECI status
while ($row = tep_db_fetch_array($result)) {
    $pgRawData = json_decode($row['pg_raw_data'], 1);
    // ECI checking
    if (isset($pgRawData['ECI'])) {
        switch ($pgRawData['ECI']) {
            case '02':
            case '05':
                // consider authenticated
                $threedOffer = 'true';
                $threedAuth = 'true';
                break;
            case '03':
            case '06':
                // consider bank not enrolled
                $threedOffer = 'false';
                $threedAuth = 'false';
                break;
            default:
                $threedOffer = 'false';
                $threedAuth = 'false';
                break;
        }
        // Update analysis_credit_card_info data
        $update_sql[TABLE_ANALYSIS_CREDIT_CARD_INFO] = array(
            array(
                "field_name" => "three_d_offered",
                "update" => " three_d_offered = '" . $threedOffer . "'",
                "where_str" => " orders_id = '" . $row['orders_id'] . "'"
            ),
            array(
                "field_name" => "three_d_result",
                "update" => " three_d_result = '" . $threedAuth . "'",
                "where_str" => " orders_id = '" . $row['orders_id'] . "'"
            ),
        );

        advance_update_records($update_sql, $DBTables);
    }
}
