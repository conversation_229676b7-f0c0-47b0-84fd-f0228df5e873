<?php
// Alter tbl:log_api_restock
$alter_table = array(
    'log_api_restock' => array(
        array(
            'field_name' => 'api_cb_status',
            'field_attr' => "ENUM('0','1','2','3','4') NOT NULL DEFAULT '0' COMMENT 'API Charge Back status, 0=Not set 1=Charge Back, 2=API Issue, 3=Removed, 4=Debit Note'",
        ),
        array(
            'field_name' => 'api_cb_temp_status',
            'field_attr' => "ENUM('0','1','2','3','4','5','6') NOT NULL DEFAULT '0' COMMENT 'API Charge Back temp status, 0=Not set 1=Charge Back, 2=API Issue, 3=Removed, 4=Debit Note, 5=Paid, 6=Un-Paid'",
        ),
    ),
    'api_replenish_cb' => array(
        array(
            'field_name' => 'api_cb_status',
            'field_attr' => "ENUM('0','1','2','3','4') NOT NULL DEFAULT '0' COMMENT 'API Charge Back status, 0=Not set 1=Charge Back, 2=API Issue, 3=Removed, 4=Debit Note'",
        ),
    ),
    'api_replenish_cb_purchase_orders' => array(
        array(
            'field_name' => 'api_cb_status',
            'field_attr' => "ENUM('0','1','4') NOT NULL DEFAULT '0' COMMENT 'Top-up CB status, 0=No CB or DN 1=Charge Back, 4=Debit Note'",
        ),
    ),
);

add_field($alter_table);
