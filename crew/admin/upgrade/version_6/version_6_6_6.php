<?php

$admin_files_insert_sql = array();
$admin_files_insert_sql['ogc.php'] = array("insert" => " ('ogc.php', '1', '0', '1', '0') ");
insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id, admin_files_cat_setting)", " admin_files_name='ogc.php' AND admin_files_is_boxes=1 ");

// Add new store file into admin_files
$select_sql = "	SELECT admin_files_id FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_name = 'ogc.php'";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["ogc_redeem_status.php"] = array("insert" => " ('ogc_redeem_status.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='ogc_redeem_status.php' AND admin_files_is_boxes=0 ");
}

# update record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[698] = array("insert" => " (698, 'offline_otc.tngwallet_otc', 'Touch n GO Wallet', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 698 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[698] = array("insert" => " (698, 'offline_otc.tngwallet_otc', 'Touch n GO Wallet', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 698 AND site_id = 5");
?>