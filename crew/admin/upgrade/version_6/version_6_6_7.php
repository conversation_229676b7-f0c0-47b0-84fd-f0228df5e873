<?php
# update record for OG & G2G
# Dragonpay
$conf_insert_sql = array();
$conf_insert_sql[699] = array("insert" => " (699, 'dragonpay.BDO', 'BDO Internet Banking', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 699 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[699] = array("insert" => " (699, 'dragonpay.BDO', 'BDO Internet Banking', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 699 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[700] = array("insert" => " (700, 'dragonpay.BPI', 'BPI Express', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 700 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[700] = array("insert" => " (700, 'dragonpay.BPI', 'BPI Express', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 700 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[701] = array("insert" => " (701, 'dragonpay.CBC', 'Chinabank Online', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 701 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[701] = array("insert" => " (701, 'dragonpay.CBC', 'Chinabank Online', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 701 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[702] = array("insert" => " (702, 'dragonpay.UBE', 'EON by Unionbank', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 702 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[702] = array("insert" => " (702, 'dragonpay.UBE', 'EON by Unionbank', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 702 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[703] = array("insert" => " (703, 'dragonpay.LBPA', 'Landbank Online', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 703 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[703] = array("insert" => " (703, 'dragonpay.LBPA', 'Landbank Online', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 703 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[704] = array("insert" => " (704, 'dragonpay.MBTC', 'Metrobank Direct', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 704 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[704] = array("insert" => " (704, 'dragonpay.MBTC', 'Metrobank Direct', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 704 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[705] = array("insert" => " (705, 'dragonpay.PNBB', 'PNB', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 705 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[705] = array("insert" => " (705, 'dragonpay.PNBB', 'PNB', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 705 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[706] = array("insert" => " (706, 'dragonpay.PSB', 'PS Bank', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 706 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[706] = array("insert" => " (706, 'dragonpay.PSB', 'PS Bank', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 706 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[707] = array("insert" => " (707, 'dragonpay.RCBC', 'RCBC AccessOne', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 707 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[707] = array("insert" => " (707, 'dragonpay.RCBC', 'RCBC AccessOne', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 707 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[708] = array("insert" => " (708, 'dragonpay.RSB', 'Robinson\'s Bank Bill Payment', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 708 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[708] = array("insert" => " (708, 'dragonpay.RSB', 'Robinson\'s Bank Bill Payment', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 708 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[709] = array("insert" => " (709, 'dragonpay.UCPB', 'UCPC Connect', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 709 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[709] = array("insert" => " (709, 'dragonpay.UCPB', 'UCPC Connect', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 709 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[710] = array("insert" => " (710, 'dragonpay.UBP', 'Unionbank Internet Banking', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 710 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[710] = array("insert" => " (710, 'dragonpay.UBP', 'Unionbank Internet Banking', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 710 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[711] = array("insert" => " (711, 'dragonpay.AUB', 'Asia United Bank', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 711 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[711] = array("insert" => " (711, 'dragonpay.AUB', 'Asia United Bank', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 711 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[712] = array("insert" => " (712, 'dragonpay.BDOA', 'BDO ATM', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 712 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[712] = array("insert" => " (712, 'dragonpay.BDOA', 'BDO ATM', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 712 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[713] = array("insert" => " (713, 'dragonpay.BPIX', 'BPI Over the Counter', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 713 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[713] = array("insert" => " (713, 'dragonpay.BPIX', 'BPI Over the Counter', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 713 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[714] = array("insert" => " (714, 'dragonpay.CBCX', 'Chinabank ATM', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 714 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[714] = array("insert" => " (714, 'dragonpay.CBCX', 'Chinabank ATM', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 714 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[715] = array("insert" => " (715, 'dragonpay.EWBX', 'EastWest Bank OTC', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 715 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[715] = array("insert" => " (715, 'dragonpay.EWBX', 'EastWest Bank OTC', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 715 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[716] = array("insert" => " (716, 'dragonpay.LBXB', 'Landbank Bill Payment', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 716 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[716] = array("insert" => " (716, 'dragonpay.LBXB', 'Landbank Bill Payment', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 716 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[717] = array("insert" => " (717, 'dragonpay.MBTX', 'Metrobank Over the Counter', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 717 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[717] = array("insert" => " (717, 'dragonpay.MBTX', 'Metrobank Over the Counter', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 717 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[718] = array("insert" => " (718, 'dragonpay.RCXB', 'RCBC OTC Bill Payment', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 718 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[718] = array("insert" => " (718, 'dragonpay.RCXB', 'RCBC OTC Bill Payment', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 718 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[719] = array("insert" => " (719, 'dragonpay.RSBB', 'Robinson\'s Bank OTC', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 719 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[719] = array("insert" => " (719, 'dragonpay.RSBB', 'Robinson\'s Bank OTC', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 719 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[720] = array("insert" => " (720, 'dragonpay.SBCA', 'Security Bank ATM', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 720 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[720] = array("insert" => " (720, 'dragonpay.SBCA', 'Security Bank ATM', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 720 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[721] = array("insert" => " (721, 'dragonpay.UCXB', 'UCPC Bill Payment', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 721 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[721] = array("insert" => " (721, 'dragonpay.UCXB', 'UCPC Bill Payment', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 721 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[722] = array("insert" => " (722, 'dragonpay.UBXB', 'Unionbank Bill payment', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 722 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[722] = array("insert" => " (722, 'dragonpay.UBXB', 'Unionbank Bill payment', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 722 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[723] = array("insert" => " (723, 'dragonpay.BAYD', 'Bayad Center', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 723 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[723] = array("insert" => " (723, 'dragonpay.BAYD', 'Bayad Center', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 723 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[724] = array("insert" => " (724, 'dragonpay.CEBP', 'Cebuana Lhuillier', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 724 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[724] = array("insert" => " (724, 'dragonpay.CEBP', 'Cebuana Lhuillier', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 724 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[725] = array("insert" => " (725, 'dragonpay.ECPY', 'EC Pay', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 725 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[725] = array("insert" => " (725, 'dragonpay.ECPY', 'EC Pay', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 725 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[726] = array("insert" => " (726, 'dragonpay.LBC', 'LBC', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 726 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[726] = array("insert" => " (726, 'dragonpay.LBC', 'LBC', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 726 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[727] = array("insert" => " (727, 'dragonpay.MLH', 'M. Lhuillier', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 727 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[727] = array("insert" => " (727, 'dragonpay.MLH', 'M. Lhuillier', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 727 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[728] = array("insert" => " (728, 'dragonpay.PNBR', 'PNB Remittance', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 728 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[728] = array("insert" => " (728, 'dragonpay.PNBR', 'PNB Remittance', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 728 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[729] = array("insert" => " (729, 'dragonpay.RDS', 'Robinson Dept Store', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 729 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[729] = array("insert" => " (729, 'dragonpay.RDS', 'Robinson Dept Store', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 729 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[730] = array("insert" => " (730, 'dragonpay.RLNT', 'RuralNet', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 730 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[730] = array("insert" => " (730, 'dragonpay.RLNT', 'RuralNet', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 730 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[731] = array("insert" => " (731, 'dragonpay.SKYF', 'Sky Freight', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 731 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[731] = array("insert" => " (731, 'dragonpay.SKYF', 'Sky Freight', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 731 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[732] = array("insert" => " (732, 'dragonpay.SMR', 'SM Dept Counter', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 732 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[732] = array("insert" => " (732, 'dragonpay.SMR', 'SM Dept Counter', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 732 AND site_id = 5");

# Indomog
$conf_insert_sql = array();
$conf_insert_sql[733] = array("insert" => " (733, 'indomog.bca_klikpay', 'BCA KlikPay', 'INDOMOG', 253, 'indomog', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 733 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[733] = array("insert" => " (733, 'indomog.bca_klikpay', 'BCA KlikPay', 'INDOMOG', 253, 'indomog', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 733 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[734] = array("insert" => " (734, 'indomog.klikbca', 'KlikBCA', 'INDOMOG', 253, 'indomog', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 734 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[734] = array("insert" => " (734, 'indomog.klikbca', 'KlikBCA', 'INDOMOG', 253, 'indomog', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 734 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[735] = array("insert" => " (735, 'indomog.sakuku', 'Sakuku', 'INDOMOG', 253, 'indomog', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 735 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[735] = array("insert" => " (735, 'indomog.sakuku', 'Sakuku', 'INDOMOG', 253, 'indomog', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 735 AND site_id = 5");
?>