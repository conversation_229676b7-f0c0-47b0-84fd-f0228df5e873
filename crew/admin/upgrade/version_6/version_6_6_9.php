<?php
# update record for OG & G2G
# DOKU
$conf_insert_sql = array();
$conf_insert_sql[751] = array("insert" => " (751, 'doku.doku_artajasa', 'ATM Bersama VA', 'DOKU', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 751 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[751] = array("insert" => " (751, 'doku.doku_artajasa', 'ATM Bersama VA', 'DOKU', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 751 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[752] = array("insert" => " (752, 'doku.doku_btn', 'Bank BTN', 'DOKU', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 752 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[752] = array("insert" => " (752, 'doku.doku_btn', 'Bank BTN', 'DOKU', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 752 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[753] = array("insert" => " (753, 'doku.doku_mandiriva', 'Bank Mandiri', 'DOKU', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 753 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[753] = array("insert" => " (753, 'doku.doku_mandiriva', 'Bank Mandiri', 'DOKU', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 753 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[754] = array("insert" => " (754, 'doku.doku_bni', 'BNI', 'DOKU', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 754 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[754] = array("insert" => " (754, 'doku.doku_bni', 'BNI', 'DOKU', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 754 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[755] = array("insert" => " (755, 'doku.doku_danamonva', 'Danamon VA', 'DOKU', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 755 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[755] = array("insert" => " (755, 'doku.doku_danamonva', 'Danamon VA', 'DOKU', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 755 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[756] = array("insert" => " (756, 'doku.doku_maybankva', 'Maybank VA', 'DOKU', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 756 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[756] = array("insert" => " (756, 'doku.doku_maybankva', 'Maybank VA', 'DOKU', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 756 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[757] = array("insert" => " (757, 'doku.doku_qnb', 'QNB VA', 'DOKU', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 757 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[757] = array("insert" => " (757, 'doku.doku_qnb', 'QNB VA', 'DOKU', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 757 AND site_id = 5");
?>