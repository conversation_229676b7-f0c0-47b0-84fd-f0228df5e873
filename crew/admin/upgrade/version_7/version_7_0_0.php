<?php
$add_new_field = array();
// Insert new fields into `orders_tax_configuration` table
$add_new_field['orders_tax_configuration'] = array(
	array(
		'field_name' => 'orders_provide_invoice_status',
		'field_attr' => "tinyint(1) NOT NULL DEFAULT '0'",
		'add_after' => 'orders_tax_status'
	)
);
// Insert new fields into `orders_tax_configuration_description` table
$add_new_field['orders_tax_configuration_description'] = array(
	array(
		'field_name' => 'orders_tax_message',
		'field_attr' => "text NOT NULL",
		'add_after' => 'orders_tax_title_short'
	)
);
add_field($add_new_field);

// Set all current tax config to send invoice/tax-invoice email
tep_db_query("UPDATE " . TABLE_ORDERS_TAX_CONFIGURATION . " SET orders_provide_invoice_status = 1");
?>