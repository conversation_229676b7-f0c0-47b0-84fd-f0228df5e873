<?php
# update record for OG & G2G
# eGHL
$conf_insert_sql = array();
$conf_insert_sql[768] = array("insert" => " (768, 'eghl.grabpay', 'GrabPay', 'eGHL', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 768 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[768] = array("insert" => " (768, 'eghl.grabpay', 'GrabPay', 'eGHL', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 768 AND site_id = 5");

# Moneybookers - Skrill
$conf_insert_sql = array();
$conf_insert_sql[771] = array("insert" => " (771, 'moneybooker.trustly', 'Trustly', 'Skrill', 44, 'moneybookers', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 771 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[771] = array("insert" => " (771, 'moneybooker.trustly', 'Trustly', 'Skrill', 44, 'moneybookers', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 771 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[52] = array("insert" => " (52, 'moneybooker.poli', 'POLi', 'Skrill', 44, 'moneybookers', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 52 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[52] = array("insert" => " (52, 'moneybooker.poli', 'POLi', 'Skrill', 44, 'moneybookers', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 52 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[59] = array("insert" => " (59, 'moneybooker.ideal', 'IDEAL', 'Skrill', 44, 'moneybookers', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 59 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[59] = array("insert" => " (59, 'moneybooker.ideal', 'IDEAL', 'Skrill', 44, 'moneybookers', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 59 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[770] = array("insert" => " (770, 'moneybooker.eps', 'EPS', 'Skrill', 44, 'moneybookers', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 770 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[770] = array("insert" => " (770, 'moneybooker.eps', 'EPS', 'Skrill', 44, 'moneybookers', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 770 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[56] = array("insert" => " (56, 'moneybooker.giropay', 'Giropay', 'Skrill', 44, 'moneybookers', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 56 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[56] = array("insert" => " (56, 'moneybooker.giropay', 'Giropay', 'Skrill', 44, 'moneybookers', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 56 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[57] = array("insert" => " (57, 'moneybooker.sofortbanking', 'SofortBanking', 'Skrill', 44, 'moneybookers', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 57 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[57] = array("insert" => " (57, 'moneybooker.sofortbanking', 'SofortBanking', 'Skrill', 44, 'moneybookers', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 57 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[769] = array("insert" => " (769, 'moneybooker.paysafecard', 'PaySafeCard', 'Skrill', 44, 'moneybookers', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 769 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[769] = array("insert" => " (769, 'moneybooker.paysafecard', 'PaySafeCard', 'Skrill', 44, 'moneybookers', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 769 AND site_id = 5");
?>