<?php
# insert new pipwave mapper record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[779] = array("insert" => " (779, 'offline_maybank_qrpay.maybankqr', 'Maybank QRPay', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 779 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[779] = array("insert" => " (779, 'offline_maybank_qrpay.maybankqr', 'Maybank QRPay', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 779 AND site_id = 5");
?>