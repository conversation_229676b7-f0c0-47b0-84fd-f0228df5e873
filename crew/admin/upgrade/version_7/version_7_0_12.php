<?php
# insert new record for OG & G2G
# Adyen
$conf_insert_sql = array();
$conf_insert_sql[781] = array("insert" => " (781, 'adyen.hpp.eps::68db503b-ea48-4681-814b-10fc74064f68', 'EPS Austrian Anadi Bank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 781 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[781] = array("insert" => " (781, 'adyen.hpp.eps::68db503b-ea48-4681-814b-10fc74064f68', 'EPS Austrian Anadi Bank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 781 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[782] = array("insert" => " (782, 'adyen.hpp.eps::bbd44f4d-609b-454e-8d5a-e0d1ac21f15a', 'EPS Bank Austria', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 782 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[782] = array("insert" => " (782, 'adyen.hpp.eps::bbd44f4d-609b-454e-8d5a-e0d1ac21f15a', 'EPS Bank Austria', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 782 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[783] = array("insert" => " (783, 'adyen.hpp.eps::d5639c17-0207-4d49-8708-922181135ad1', 'EPS Bankhaus Carl Spängler & Co AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 783 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[783] = array("insert" => " (783, 'adyen.hpp.eps::d5639c17-0207-4d49-8708-922181135ad1', 'EPS Bankhaus Carl Spängler & Co AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 783 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[784] = array("insert" => " (784, 'adyen.hpp.eps::c522e299-479c-4134-849f-a6ef0399a6e0', 'EPS Bankhaus Schelhammer & Schattera AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 784 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[784] = array("insert" => " (784, 'adyen.hpp.eps::c522e299-479c-4134-849f-a6ef0399a6e0', 'EPS Bankhaus Schelhammer & Schattera AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 784 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[785] = array("insert" => " (785, 'adyen.hpp.eps::********-e6fc-401c-8f3d-26e9169ba6ff', 'EPS BAWAG P.S.K.', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 785 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[785] = array("insert" => " (785, 'adyen.hpp.eps::********-e6fc-401c-8f3d-26e9169ba6ff', 'EPS BAWAG P.S.K.', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 785 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[786] = array("insert" => " (786, 'adyen.hpp.eps::7217d822-470c-4768-b346-a440e8cb7096', 'EPS BKS Bank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 786 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[786] = array("insert" => " (786, 'adyen.hpp.eps::7217d822-470c-4768-b346-a440e8cb7096', 'EPS BKS Bank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 786 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[787] = array("insert" => " (787, 'adyen.hpp.eps::f848c582-1b94-4e13-b58e-b3a2c68a3fba', 'EPS Brüll Kallmus Bank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 787 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[787] = array("insert" => " (787, 'adyen.hpp.eps::f848c582-1b94-4e13-b58e-b3a2c68a3fba', 'EPS Brüll Kallmus Bank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 787 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[788] = array("insert" => " (788, 'adyen.hpp.eps::e590eb4f-b805-43f7-86a0-4ea3f560b18f', 'EPS Capital Bank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 788 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[788] = array("insert" => " (788, 'adyen.hpp.eps::e590eb4f-b805-43f7-86a0-4ea3f560b18f', 'EPS Capital Bank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 788 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[789] = array("insert" => " (789, 'adyen.hpp.eps::69a67a81-7518-463f-8527-3f1128af1f93', 'EPS Deutsche Apotheker- und Ärztebank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 789 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[789] = array("insert" => " (789, 'adyen.hpp.eps::69a67a81-7518-463f-8527-3f1128af1f93', 'EPS Deutsche Apotheker- und Ärztebank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 789 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[790] = array("insert" => " (790, 'adyen.hpp.eps::b42dd796-8bda-4893-acc2-ddfae9c9cdb7', 'EPS Dolomitenbank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 790 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[790] = array("insert" => " (790, 'adyen.hpp.eps::b42dd796-8bda-4893-acc2-ddfae9c9cdb7', 'EPS Dolomitenbank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 790 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[791] = array("insert" => " (791, 'adyen.hpp.eps::54016d9d-31b0-4db3-9eaf-cf9fbc58ee81', 'EPS Easybank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 791 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[791] = array("insert" => " (791, 'adyen.hpp.eps::54016d9d-31b0-4db3-9eaf-cf9fbc58ee81', 'EPS Easybank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 791 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[792] = array("insert" => " (792, 'adyen.hpp.eps::1d48e2f7-604c-4d1d-894e-170635d1a645', 'EPS Erste Group Bank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 792 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[792] = array("insert" => " (792, 'adyen.hpp.eps::1d48e2f7-604c-4d1d-894e-170635d1a645', 'EPS Erste Group Bank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 792 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[793] = array("insert" => " (793, 'adyen.hpp.eps::958d1cf8-0d2d-4b21-b1f2-8383bb9f243f', 'EPS Hypo Alpe-Adria-Bank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 793 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[793] = array("insert" => " (793, 'adyen.hpp.eps::958d1cf8-0d2d-4b21-b1f2-8383bb9f243f', 'EPS Hypo Alpe-Adria-Bank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 793 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[794] = array("insert" => " (794, 'adyen.hpp.eps::880cc8c4-d723-49ab-8299-dc35eafc9385', 'EPS Hypo Landesbank Vorarlberg', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 794 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[794] = array("insert" => " (794, 'adyen.hpp.eps::880cc8c4-d723-49ab-8299-dc35eafc9385', 'EPS Hypo Landesbank Vorarlberg', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 794 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[795] = array("insert" => " (795, 'adyen.hpp.eps::9c92d29b-a898-488e-836f-e93b911f9a94', 'EPS HYPO NOE Landesbank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 795 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[795] = array("insert" => " (795, 'adyen.hpp.eps::9c92d29b-a898-488e-836f-e93b911f9a94', 'EPS HYPO NOE Landesbank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 795 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[796] = array("insert" => " (796, 'adyen.hpp.eps::89c79da1-1b75-41e2-9254-118f36231bbd', 'EPS HYPO Steiermark', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 796 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[796] = array("insert" => " (796, 'adyen.hpp.eps::89c79da1-1b75-41e2-9254-118f36231bbd', 'EPS HYPO Steiermark', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 796 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[797] = array("insert" => " (797, 'adyen.hpp.eps::********-fb43-4094-afd5-82980ff48750', 'EPS Hypo Tirol Bank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 797 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[797] = array("insert" => " (797, 'adyen.hpp.eps::********-fb43-4094-afd5-82980ff48750', 'EPS Hypo Tirol Bank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 797 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[798] = array("insert" => " (798, 'adyen.hpp.eps::b3b1554f-c9ae-4396-a62f-baffb8a2de1c', 'EPS HYPO-BANK BURGENLAND', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 798 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[798] = array("insert" => " (798, 'adyen.hpp.eps::b3b1554f-c9ae-4396-a62f-baffb8a2de1c', 'EPS HYPO-BANK BURGENLAND', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 798 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[799] = array("insert" => " (799, 'adyen.hpp.eps::744d26da-90c9-49eb-9a0b-9a7f1fa3a8d2', 'EPS OberBank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 799 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[799] = array("insert" => " (799, 'adyen.hpp.eps::744d26da-90c9-49eb-9a0b-9a7f1fa3a8d2', 'EPS OberBank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 799 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[800] = array("insert" => " (800, 'adyen.hpp.eps::********-7ba4-4afb-b706-843e49a64759', 'EPS Raiffeisen Bankengruppe', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 800 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[800] = array("insert" => " (800, 'adyen.hpp.eps::********-7ba4-4afb-b706-843e49a64759', 'EPS Raiffeisen Bankengruppe', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 800 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[801] = array("insert" => " (801, 'adyen.hpp.eps::3ed7da63-0924-424c-a822-2a0dfaaadce1', 'EPS Schoellerbank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 801 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[801] = array("insert" => " (801, 'adyen.hpp.eps::3ed7da63-0924-424c-a822-2a0dfaaadce1', 'EPS Schoellerbank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 801 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[802] = array("insert" => " (802, 'adyen.hpp.eps::cc4a034e-f8a1-4ce4-a96b-1cd251af03b5', 'EPS Sparda Bank Wien', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 802 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[802] = array("insert" => " (802, 'adyen.hpp.eps::cc4a034e-f8a1-4ce4-a96b-1cd251af03b5', 'EPS Sparda Bank Wien', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 802 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[803] = array("insert" => " (803, 'adyen.hpp.eps::a990a73b-3d6b-4d91-9b8a-856428089b34', 'EPS VIER LÄNDER BANK', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 803 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[803] = array("insert" => " (803, 'adyen.hpp.eps::a990a73b-3d6b-4d91-9b8a-856428089b34', 'EPS VIER LÄNDER BANK', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 803 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[804] = array("insert" => " (804, 'adyen.hpp.eps::479ff5d2-6f44-4921-9330-6bf6763806e9', 'EPS Volksbank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 804 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[804] = array("insert" => " (804, 'adyen.hpp.eps::479ff5d2-6f44-4921-9330-6bf6763806e9', 'EPS Volksbank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 804 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[805] = array("insert" => " (805, 'adyen.hpp.eps::43b3af24-7969-4701-a7ce-1f9fc6eef834', 'EPS Volkskreditbank AG', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 805 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[805] = array("insert" => " (805, 'adyen.hpp.eps::43b3af24-7969-4701-a7ce-1f9fc6eef834', 'EPS Volkskreditbank AG', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 805 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[806] = array("insert" => " (806, 'adyen.hpp.openbankuk::1', 'Open Bank - Allied Irish Bank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 806 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[806] = array("insert" => " (806, 'adyen.hpp.openbankuk::1', 'Open Bank - Allied Irish Bank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 806 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[807] = array("insert" => " (807, 'adyen.hpp.openbankuk::2', 'Open Bank - Allied Irish Bank (Corporate)', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 807 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[807] = array("insert" => " (807, 'adyen.hpp.openbankuk::2', 'Open Bank - Allied Irish Bank (Corporate)', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 807 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[808] = array("insert" => " (808, 'adyen.hpp.openbankuk::19', 'Open Bank - Bank of Ireland UK', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 808 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[808] = array("insert" => " (808, 'adyen.hpp.openbankuk::19', 'Open Bank - Bank of Ireland UK', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 808 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[809] = array("insert" => " (809, 'adyen.hpp.openbankuk::4', 'Open Bank - Bank of Scotland', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 809 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[809] = array("insert" => " (809, 'adyen.hpp.openbankuk::4', 'Open Bank - Bank of Scotland', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 809 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[810] = array("insert" => " (810, 'adyen.hpp.openbankuk::3', 'Open Bank - Barclays', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 810 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[810] = array("insert" => " (810, 'adyen.hpp.openbankuk::3', 'Open Bank - Barclays', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 810 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[811] = array("insert" => " (811, 'adyen.hpp.openbankuk::5', 'Open Bank - Danske Bank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 811 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[811] = array("insert" => " (811, 'adyen.hpp.openbankuk::5', 'Open Bank - Danske Bank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 811 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[812] = array("insert" => " (812, 'adyen.hpp.openbankuk::6', 'Open Bank - Danske Bank (Corporate)', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 812 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[812] = array("insert" => " (812, 'adyen.hpp.openbankuk::6', 'Open Bank - Danske Bank (Corporate)', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 812 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[813] = array("insert" => " (813, 'adyen.hpp.openbankuk::7', 'Open Bank - First Direct', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 813 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[813] = array("insert" => " (813, 'adyen.hpp.openbankuk::7', 'Open Bank - First Direct', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 813 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[814] = array("insert" => " (814, 'adyen.hpp.openbankuk::8', 'Open Bank - First Trust Bank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 814 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[814] = array("insert" => " (814, 'adyen.hpp.openbankuk::8', 'Open Bank - First Trust Bank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 814 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[815] = array("insert" => " (815, 'adyen.hpp.openbankuk::9', 'Open Bank - First Trust Bank (Corporate)', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 815 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[815] = array("insert" => " (815, 'adyen.hpp.openbankuk::9', 'Open Bank - First Trust Bank (Corporate)', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 815 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[816] = array("insert" => " (816, 'adyen.hpp.openbankuk::10', 'Open Bank - Halifax', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 816 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[816] = array("insert" => " (816, 'adyen.hpp.openbankuk::10', 'Open Bank - Halifax', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 816 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[817] = array("insert" => " (817, 'adyen.hpp.openbankuk::11', 'Open Bank - HSBC Bank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 817 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[817] = array("insert" => " (817, 'adyen.hpp.openbankuk::11', 'Open Bank - HSBC Bank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 817 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[818] = array("insert" => " (818, 'adyen.hpp.openbankuk::12', 'Open Bank - Lloyds Bank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 818 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[818] = array("insert" => " (818, 'adyen.hpp.openbankuk::12', 'Open Bank - Lloyds Bank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 818 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[819] = array("insert" => " (819, 'adyen.hpp.openbankuk::13', 'Open Bank - M&S Bank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 819 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[819] = array("insert" => " (819, 'adyen.hpp.openbankuk::13', 'Open Bank - M&S Bank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 819 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[820] = array("insert" => " (820, 'adyen.hpp.openbankuk::15', 'Open Bank - National Westminster Bank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 820 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[820] = array("insert" => " (820, 'adyen.hpp.openbankuk::15', 'Open Bank - National Westminster Bank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 820 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[821] = array("insert" => " (821, 'adyen.hpp.openbankuk::14', 'Open Bank - Nationwide Building Society', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 821 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[821] = array("insert" => " (821, 'adyen.hpp.openbankuk::14', 'Open Bank - Nationwide Building Society', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 821 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[822] = array("insert" => " (822, 'adyen.hpp.openbankuk::16', 'Open Bank - Royal Bank of Scotland', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 822 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[822] = array("insert" => " (822, 'adyen.hpp.openbankuk::16', 'Open Bank - Royal Bank of Scotland', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 822 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[823] = array("insert" => " (823, 'adyen.hpp.openbankuk::17', 'Open Bank - Santander UK', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 823 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[823] = array("insert" => " (823, 'adyen.hpp.openbankuk::17', 'Open Bank - Santander UK', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 823 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[824] = array("insert" => " (824, 'adyen.hpp.openbankuk::18', 'Open Bank - Ulster Bank', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 824 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[824] = array("insert" => " (824, 'adyen.hpp.openbankuk::18', 'Open Bank - Ulster Bank', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 824 AND site_id = 5");
?>