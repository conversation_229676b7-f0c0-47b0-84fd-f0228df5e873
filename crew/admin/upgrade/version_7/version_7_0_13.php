<?php
// Update Pipwave payment mapper RP status
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::1'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::2'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::19'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::4'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::3'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::5'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::6'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::7'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::8'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::9'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::10'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::11'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::12'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::13'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::15'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::14'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::16'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::17'");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::18'");
?>