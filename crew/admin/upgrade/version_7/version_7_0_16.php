<?php
// Alter column type from integer to varchar
tep_db_query("ALTER TABLE " . TABLE_GIFT_CARD_REDEMPTION . " MODIFY `transaction_id` VARCHAR(32);");

# insert new record for OG & G2G
# Adyen
$conf_insert_sql = array();
$conf_insert_sql[826] = array("insert" => " (826, 'ipay88.tng', 'Touch n Go e-Wallet', 'iPay88', 27, 'iPay88', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 826 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[826] = array("insert" => " (826, 'ipay88.tng', 'Touch n Go e-Wallet', 'iPay88', 27, 'iPay88', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 826 AND site_id = 5");