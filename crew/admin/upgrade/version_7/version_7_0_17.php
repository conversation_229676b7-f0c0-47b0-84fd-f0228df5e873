<?php
// Update Pipwave payment mapper RP status
// OG
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.directEbanking' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.poli' AND site_id = 0");

tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::1' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::2' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::19' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::4' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::3' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::5' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::6' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::7' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::8' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::9' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::10' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::11' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::12' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::13' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::15' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::14' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::16' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::17' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.openbankuk::18' AND site_id = 0");

// G2G
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'moneybooker.rapidtransfer' AND site_id = 5");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'moneybooker.skrill' AND site_id = 5");
?>