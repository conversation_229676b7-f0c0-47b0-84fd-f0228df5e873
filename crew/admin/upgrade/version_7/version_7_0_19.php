<?php
# update record for OG & G2G
# eGHL
$conf_insert_sql = array();
$conf_insert_sql[827] = array("insert" => " (827, 'eghl.tng', 'Touch n GO Wallet', 'eGHL', 572, 'pipwavePG', 1, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 827 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[827] = array("insert" => " (827, 'eghl.tng', 'Touch n GO Wallet', 'eGHL', 572, 'pipwavePG', 1, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 827 AND site_id = 5");

#DaoPay
$conf_insert_sql = array();
$conf_insert_sql[829] = array("insert" => " (829, 'daopay.myb', 'MyBank', 'DaoPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 829 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[829] = array("insert" => " (829, 'daopay.myb', 'MyBank', 'DaoPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 829 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[828] = array("insert" => " (828, 'daopay.vp', 'Verkkopankki', 'DaoPay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 828 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[828] = array("insert" => " (828, 'daopay.vp', 'Verkkopankki', 'DaoPay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 828 AND site_id = 5");
?>