<?php
# update record for OG & G2G
# Adyen
$conf_insert_sql = array();
$conf_insert_sql[772] = array("insert" => " (772, 'adyen.hpp.eps', 'EPS', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 772 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[772] = array("insert" => " (772, 'adyen.hpp.eps', 'EPS', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 772 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[773] = array("insert" => " (773, 'adyen.hpp.openbankuk', 'Open Bank UK', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 773 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[773] = array("insert" => " (773, 'adyen.hpp.openbankuk', 'Open Bank UK', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 773 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[774] = array("insert" => " (774, 'adyen.hpp.interac', 'Interac Online', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 774 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[774] = array("insert" => " (774, 'adyen.hpp.interac', 'Interac Online', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 774 AND site_id = 5");
?>