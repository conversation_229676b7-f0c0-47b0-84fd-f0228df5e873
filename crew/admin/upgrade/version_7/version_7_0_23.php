<?php
# update record for OG & G2G
# smart2pay
$conf_insert_sql = array();
$conf_insert_sql[834] = array("insert" => " (834, 'smart2pay.banktransfer', 'Bank Transfer EU', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 834 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[834] = array("insert" => " (834, 'smart2pay.banktransfer', 'Bank Transfer EU', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 834 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[836] = array("insert" => " (836, 'smart2pay.mts', 'MTS', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 836 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[836] = array("insert" => " (836, 'smart2pay.mts', 'MTS', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 836 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[835] = array("insert" => " (835, 'smart2pay.finnishbank', 'Finnish Bank - Verkkopankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 835 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[835] = array("insert" => " (835, 'smart2pay.finnishbank', 'Finnish Bank - Verkkopankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 835 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[837] = array("insert" => " (837, 'smart2pay.przelewy24', 'Przelewy24', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 837 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[837] = array("insert" => " (837, 'smart2pay.przelewy24', 'Przelewy24', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 837 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[838] = array("insert" => " (838, 'smart2pay.tele2', 'Tele2', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 838 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[838] = array("insert" => " (838, 'smart2pay.tele2', 'Tele2', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 838 AND site_id = 5");

// Update
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'smart2pay.poli'");
?>
