<?php 
// Insert new records into admin_files_actions table (for permission cancel delivery for G2G sold order)
$select_sql = "	SELECT admin_files_id, admin_groups_id 
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='c2c_buyback_order.php' AND admin_files_is_boxes=0" ;
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {	// if found existing record
	$admin_files_actions_insert_sql = array();

	$admin_files_actions_insert_sql["CANCEL_SELLER_DELIVERY"] = array("insert" => " ('CANCEL_SELLER_DELIVERY', 'Cancel Seller Delivery', ".$row_sql["admin_files_id"].", '1', 10)" );
	
	insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}

tep_db_query("UPDATE " . TABLE_CUSTOM_PRODUCTS_TYPE_CHILD . " SET custom_products_type_child_url = 'coaching', custom_products_type_child_name = 'Coaching' WHERE custom_products_type_child_id = '17'");