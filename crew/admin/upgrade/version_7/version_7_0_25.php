<?php 
// Update international dialing code
$update_countries_sql = array();

// BQ
$update_countries_sql["246"] = array("update" => " countries_international_dialing_code=599 " );
$update_countries_sql["248"] = array("update" => " countries_international_dialing_code=599 " );
update_records("countries", "countries_id", $update_countries_sql, $DBTables);
// End of update international dialing code

# update record for OG & G2G
# smart2pay
$conf_insert_sql = array();
$conf_insert_sql[862] = array("insert" => " (862, 'smart2pay.saastopankki', 'Finnish Bank Saastopankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 862 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[862] = array("insert" => " (862, 'smart2pay.saastopankki', 'Finnish Bank Saastopankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 862 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[861] = array("insert" => " (861, 'smart2pay.spankki', 'Finnish Bank S-Pankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 861 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[861] = array("insert" => " (861, 'smart2pay.spankki', 'Finnish Bank S-Pankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 861 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[860] = array("insert" => " (860, 'smart2pay.poppankki', 'Finnish Bank POP Pankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 860 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[860] = array("insert" => " (860, 'smart2pay.poppankki', 'Finnish Bank POP Pankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 860 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[859] = array("insert" => " (859, 'smart2pay.oppohjola', 'Finnish Bank OP-Pohjola', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 859 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[859] = array("insert" => " (859, 'smart2pay.oppohjola', 'Finnish Bank OP-Pohjola', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 859 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[858] = array("insert" => " (858, 'smart2pay.omasaastopankki', 'Finnish Bank Oma Saastopankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 858 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[858] = array("insert" => " (858, 'smart2pay.omasaastopankki', 'Finnish Bank Oma Saastopankki', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 858 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[857] = array("insert" => " (857, 'smart2pay.nordea', 'Finnish Bank Nordea', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 857 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[857] = array("insert" => " (857, 'smart2pay.nordea', 'Finnish Bank Nordea', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 857 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[856] = array("insert" => " (856, 'smart2pay.finnishhandelsbanken', 'Finnish Bank Handelsbanken', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 856 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[856] = array("insert" => " (856, 'smart2pay.finnishhandelsbanken', 'Finnish Bank Handelsbanken', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 856 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[855] = array("insert" => " (855, 'smart2pay.danskebank', 'Finnish Bank Danske Bank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 855 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[855] = array("insert" => " (855, 'smart2pay.danskebank', 'Finnish Bank Danske Bank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 855 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[854] = array("insert" => " (854, 'smart2pay.alandsbanken', 'Finnish Bank Alandsbanken', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 854 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[854] = array("insert" => " (854, 'smart2pay.alandsbanken', 'Finnish Bank Alandsbanken', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 854 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[853] = array("insert" => " (853, 'smart2pay.aktia', 'Finnish Bank Aktia', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 853 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[853] = array("insert" => " (853, 'smart2pay.aktia', 'Finnish Bank Aktia', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 853 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[852] = array("insert" => " (852, 'smart2pay.moneyou', 'iDEAL MoneYou', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 852 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[852] = array("insert" => " (852, 'smart2pay.moneyou', 'iDEAL MoneYou', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 852 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[851] = array("insert" => " (851, 'smart2pay.vanlanschot', 'iDEAL Van Lanschot', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 851 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[851] = array("insert" => " (851, 'smart2pay.vanlanschot', 'iDEAL Van Lanschot', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 851 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[850] = array("insert" => " (850, 'smart2pay.handelsbanken', 'iDEAL Handelsbanken', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 850 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[850] = array("insert" => " (850, 'smart2pay.handelsbanken', 'iDEAL Handelsbanken', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 850 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[849] = array("insert" => " (849, 'smart2pay.triodosbank', 'iDEAL Triodos Bank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 849 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[849] = array("insert" => " (849, 'smart2pay.triodosbank', 'iDEAL Triodos Bank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 849 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[848] = array("insert" => " (848, 'smart2pay.snsbank', 'iDEAL SNS Bank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 848 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[848] = array("insert" => " (848, 'smart2pay.snsbank', 'iDEAL SNS Bank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 848 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[847] = array("insert" => " (847, 'smart2pay.regiobank', 'iDEAL RegioBank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 847 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[847] = array("insert" => " (847, 'smart2pay.regiobank', 'iDEAL RegioBank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 847 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[846] = array("insert" => " (846, 'smart2pay.rabobank', 'iDEAL Rabobank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 846 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[846] = array("insert" => " (846, 'smart2pay.rabobank', 'iDEAL Rabobank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 846 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[845] = array("insert" => " (845, 'smart2pay.knab', 'iDEAL Knab', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 845 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[845] = array("insert" => " (845, 'smart2pay.knab', 'iDEAL Knab', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 845 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[844] = array("insert" => " (844, 'smart2pay.ing', 'iDEAL ING', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 844 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[844] = array("insert" => " (844, 'smart2pay.ing', 'iDEAL ING', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 844 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[843] = array("insert" => " (843, 'smart2pay.bunq', 'iDEAL Bunq', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 843 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[843] = array("insert" => " (843, 'smart2pay.bunq', 'iDEAL Bunq', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 843 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[842] = array("insert" => " (842, 'smart2pay.asnbank', 'iDEAL ASN Bank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 842 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[842] = array("insert" => " (842, 'smart2pay.asnbank', 'iDEAL ASN Bank', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 842 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[841] = array("insert" => " (841, 'smart2pay.abnamro', 'iDEAL ABN Amro', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 841 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[841] = array("insert" => " (841, 'smart2pay.abnamro', 'iDEAL ABN Amro', 'Smart2Pay', 268, 'smart2pay_globalpay', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 841 AND site_id = 5");

# adyen
$conf_insert_sql = array();
$conf_insert_sql[839] = array("insert" => " (839, 'adyen.hpp.ideal::0804', 'iDEAL Handelsbanken', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 839 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[839] = array("insert" => " (839, 'adyen.hpp.ideal::0804', 'iDEAL Handelsbanken', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 839 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[840] = array("insert" => " (840, 'adyen.hpp.ideal::0803', 'iDEAL MoneYou', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 840 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[840] = array("insert" => " (840, 'adyen.hpp.ideal::0803', 'iDEAL MoneYou', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 840 AND site_id = 5");
?>