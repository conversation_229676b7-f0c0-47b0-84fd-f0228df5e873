<?php 
// QFPay
$conf_insert_sql = array();
$conf_insert_sql[864] = array("insert" => " (864, 'qfpay.alipay', 'Alipay', 'QFPay', 572, 'pipwavePG', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 864 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[864] = array("insert" => " (864, 'qfpay.alipay', 'Alipay', 'QFPay', 572, 'pipwavePG', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 864 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[863] = array("insert" => " (863, 'qfpay.wechat', 'WeChat Pay', 'QFPay', 572, 'pipwavePG', 2, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 863 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[863] = array("insert" => " (863, 'qfpay.wechat', 'WeChat Pay', 'QFPay', 572, 'pipwavePG', 2, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 863 AND site_id = 5");
?>