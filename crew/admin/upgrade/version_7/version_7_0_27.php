<?php

// insert new permission control on customer.php: Approve/Deny document ID [start]
$select_sql = "	SELECT admin_files_id, admin_groups_id
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();

    $admin_files_actions_insert_sql["CUSTOMER_APPROVE_NAME"] = array("insert" => " ('CUSTOMER_APPROVE_NAME', 'Approve/Deny Name Change Request', " . $row_sql["admin_files_id"] . ", '1', 59)");
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// insert new permission control on customer.php: Approve/Deny document ID [end]
?>