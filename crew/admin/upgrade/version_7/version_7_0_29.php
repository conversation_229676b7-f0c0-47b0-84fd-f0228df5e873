<?php
# update record for OG & G2G
# Dragonpay
$conf_insert_sql = array();
$conf_insert_sql[866] = array("insert" => " (866, 'dragonpay.RDP', 'RD Pawnshop', 'Dragonpay', 572, 'pipwavePG', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 866 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[866] = array("insert" => " (866, 'dragonpay.RDP', 'RD Pawnshop', 'Dragonpay', 572, 'pipwavePG', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 866 AND site_id = 5");
?>