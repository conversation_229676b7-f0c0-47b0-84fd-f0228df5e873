<?php 
// Insert new DragonPay
$conf_insert_sql = array();
$conf_insert_sql[879] = array("insert" => " (879, 'dragonpay.grabpay', 'GrabPay', 'Dragonpay', 572, 'pipwavePG', 0, 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 879 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[879] = array("insert" => " (879, 'dragonpay.grabpay', 'GrabPay', 'Dragonpay', 572, 'pipwavePG', 0, 5, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 879 AND site_id = 5");

// Insert new DaoPay
$conf_insert_sql = array();
$conf_insert_sql[867] = array("insert" => " (867, 'daopay.ABNANL2A', 'iDEAL ABN Amro', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 867 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[867] = array("insert" => " (867, 'daopay.ABNANL2A', 'iDEAL ABN Amro', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 867 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[868] = array("insert" => " (868, 'daopay.ASNBNL21', 'iDEAL ASN Bank', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 868 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[868] = array("insert" => " (868, 'daopay.ASNBNL21', 'iDEAL ASN Bank', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 868 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[869] = array("insert" => " (869, 'daopay.BUNQNL2A', 'iDEAL Bunq', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 869 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[869] = array("insert" => " (869, 'daopay.BUNQNL2A', 'iDEAL Bunq', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 869 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[870] = array("insert" => " (870, 'daopay.HANDNL2A', 'iDEAL Handelsbanken', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 870 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[870] = array("insert" => " (870, 'daopay.HANDNL2A', 'iDEAL Handelsbanken', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 870 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[871] = array("insert" => " (871, 'daopay.INGBNL2A', 'iDEAL ING', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 871 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[871] = array("insert" => " (871, 'daopay.INGBNL2A', 'iDEAL ING', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 871 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[872] = array("insert" => " (872, 'daopay.KNABNL2H', 'iDEAL Knab', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 872 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[872] = array("insert" => " (872, 'daopay.KNABNL2H', 'iDEAL Knab', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 872 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[873] = array("insert" => " (873, 'daopay.MOYONL21', 'iDEAL MoneYou', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 873 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[873] = array("insert" => " (873, 'daopay.MOYONL21', 'iDEAL MoneYou', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 873 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[874] = array("insert" => " (874, 'daopay.RABONL2U', 'iDEAL Rabobank', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 874 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[874] = array("insert" => " (874, 'daopay.RABONL2U', 'iDEAL Rabobank', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 874 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[875] = array("insert" => " (875, 'daopay.RBRBNL21', 'iDEAL RegioBank', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 875 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[875] = array("insert" => " (875, 'daopay.RBRBNL21', 'iDEAL RegioBank', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 875 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[876] = array("insert" => " (876, 'daopay.SNSBNL2A', 'iDEAL SNS Bank', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 876 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[876] = array("insert" => " (876, 'daopay.SNSBNL2A', 'iDEAL SNS Bank', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 876 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[877] = array("insert" => " (877, 'daopay.TRIONL2U', 'iDEAL Triodos Bank', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 877 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[877] = array("insert" => " (877, 'daopay.TRIONL2U', 'iDEAL Triodos Bank', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 877 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[878] = array("insert" => " (878, 'daopay.FVLBNL22', 'iDEAL Van Lanschot', 'DaoPay', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 878 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[878] = array("insert" => " (878, 'daopay.FVLBNL22', 'iDEAL Van Lanschot', 'DaoPay', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 878 AND site_id = 5");

// Update is_refundable value for all
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 314");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 315");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 316");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 320");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 335");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 329");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 336");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 334");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 337");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 637");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 638");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 639");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 640");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 641");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 642");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 643");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 644");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 645");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 428");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 330");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 331");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 333");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 332");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 340");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 347");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 339");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 341");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 344");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 342");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 348");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 343");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 338");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 345");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 346");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 349");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 426");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 429");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 350");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 427");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 478");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 473");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 475");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 489");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 491");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 490");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 477");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 476");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 388");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 425");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 565");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 561");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 560");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 562");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 551");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 568");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 563");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 566");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 564");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 567");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 510");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 507");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 513");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 505");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 514");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 511");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 509");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 508");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 506");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 839");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 840");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 512");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 772");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 781");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 782");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 783");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 784");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 785");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 786");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 787");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 788");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 789");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 790");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 791");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 792");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 793");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 794");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 795");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 796");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 797");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 798");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 799");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 800");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 801");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 802");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 803");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 804");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 805");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 773");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 806");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 807");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 808");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 809");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 810");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 811");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 812");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 813");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 814");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 815");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 816");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 817");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 818");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 819");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 820");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 821");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 822");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 823");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 824");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 777");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 774");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 556");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 557");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 558");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 559");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 549");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 151");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 419");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 22");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 143");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 417");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 383");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 371");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 424");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 423");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 415");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 321");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 298");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 370");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 633");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 634");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 632");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 631");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 630");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 416");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 266");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 263");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 265");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 262");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 255");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 258");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 256");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 260");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 257");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 259");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 261");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 733");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 734");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 735");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 264");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 111");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 34");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 33");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 30");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 496");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 31");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 495");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 498");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 499");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 503");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 500");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 501");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 502");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 826");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 758");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 497");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 113");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 471");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 469");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 493");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 492");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 470");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 771");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 52");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 59");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 770");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 56");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 57");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 769");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 45");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 368");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 365");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 364");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 422");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 182");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 271");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 273");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 281");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 292");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 278");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 834");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 836");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 835");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 837");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 838");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 296");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 280");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 295");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 841");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 842");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 843");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 844");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 845");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 846");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 847");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 848");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 849");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 850");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 851");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 852");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 853");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 854");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 855");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 856");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 857");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 858");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 859");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 860");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 861");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 862");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 323");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 272");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 413");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 324");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 269");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 325");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 326");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 283");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 327");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 287");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 275");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 293");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 270");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 385");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 24");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 584");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 261");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 304");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 308");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 91");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 93");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 68");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 299");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 81");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 74");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 303");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 307");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 290");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 397");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 398");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 405");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 401");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 403");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 569");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 404");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 400");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 402");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 436");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 463");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 449");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 441");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 461");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 465");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 444");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 442");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 448");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 460");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 450");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 467");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 464");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 447");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 439");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 451");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 459");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 462");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 443");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 458");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 453");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 452");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 466");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 457");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 445");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 438");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 456");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 446");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 454");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 437");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 440");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 455");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 504");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 220");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 145");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 214");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 778");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 430");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 431");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 432");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 433");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 251");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 222");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 223");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 779");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 186");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 14");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 243");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 248");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 187");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 213");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 241");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 317");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 246");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 15");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 16");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 577");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 648");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 698");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 646");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 468");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 571");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 573");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 575");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 587");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 614");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 615");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 616");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 617");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 618");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 619");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 620");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 621");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 622");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 623");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 624");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 625");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 626");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 627");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 628");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 629");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 574");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 576");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 578");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 579");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 580");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 582");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 585");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 586");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 583");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 751");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 752");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 753");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 754");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 755");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 756");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 757");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 611");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 596");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 612");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 597");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 593");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 601");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 605");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 590");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 591");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 589");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 594");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 607");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 606");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 595");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 610");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 603");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 613");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 609");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 602");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 604");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 599");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 600");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 592");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 588");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 598");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 608");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 655");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 651");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 660");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 652");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 653");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 650");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 697");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 663");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 662");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 658");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 664");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 694");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 654");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 656");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 649");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 661");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 657");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 827");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 768");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 659");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 695");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 696");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 699");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 700");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 701");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 702");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 703");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 704");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 705");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 706");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 707");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 708");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 709");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 710");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 711");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 712");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 713");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 865");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 714");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 715");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 716");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 717");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 718");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 719");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 720");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 721");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 722");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 723");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 724");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 725");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 726");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 727");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 728");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 729");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 730");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 866");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 731");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 830");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 879");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 732");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 760");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 867");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 868");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 869");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 870");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 871");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 872");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 873");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 874");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 875");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 876");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 877");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 878");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 761");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 762");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 780");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 829");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 763");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 764");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 765");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 766");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 759");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 828");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 767");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 776");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 0 WHERE pm_id = 775");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 864");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_refundable = 1 WHERE pm_id = 863");

?>