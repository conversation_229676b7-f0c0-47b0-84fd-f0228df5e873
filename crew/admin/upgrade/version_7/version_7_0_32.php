<?php

$order_list_sql = 'select t.orders_id, t.notification_date, t.changed_by from pipwave_payment_history t left join orders_status_history h ON h.orders_id = t.orders_id AND h.orders_status_id = 2 where t.notification_date > "2020-03-02 12:00:00" AND t.changed_by != "system" AND t.status = "capture-payment - OK" AND h.orders_status_history_id IS NULL';

$result_sql = tep_db_query($order_list_sql);
while ($row_sql = tep_db_fetch_array($result_sql)) {
    $oID = $row_sql['orders_id'];
    $date = '"' . $row_sql['notification_date'] . '"';

    tep_update_record_tags(FILENAME_STATS_ORDERS_TRACKING, (int)$oID, (int)2, '');

    $orders_status_history_update_sql = array(
        'orders_id' => (int)$oID,
        'orders_status_id' => (int)2,
        'date_added' => $row_sql['notification_date'],
        'customer_notified' => 0,
        'comments' => '',
        'comments_type' => 1,
        'set_as_order_remarks' => 0,
        'changed_by' => tep_db_prepare_input($row_sql['changed_by'])
    );

    tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $orders_status_history_update_sql);
    $orders_status_sql = "  INSERT INTO " . TABLE_ORDERS_STATUS_STAT . "
                                    (orders_id, orders_status_id, occurrence, first_date, latest_date, changed_by)
                                    VALUES (" . $oID . ", " . 2 . ", " . 1 . ", $date, " . $date . ", '" . tep_db_prepare_input($row_sql['changed_by']) . "')";
    tep_db_query($orders_status_sql);
}