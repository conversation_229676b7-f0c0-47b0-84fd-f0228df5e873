<?php

$add_new_tables = array();
$add_new_tables["g2g_fraud"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `g2g_fraud` (
        `customers_id` int(11) NOT NULL,
        `opt_raw` text NOT NULL,
        `opt_status` tinyint(4) NOT NULL COMMENT '0 = pending, 1 = approved, 2 = declined, 3 = not fraud',
        `opt_action` smallint(6) NOT NULL DEFAULT '0',
        `aft_raw` text NOT NULL,
        `aft_status` tinyint(4) NOT NULL COMMENT '0 = pending, 1 = approved, 2 = declined, 3 = not fraud',
        `aft_action` smallint(6) NOT NULL COMMENT '0 = no action, 1 = request LL, 2 = disabled withdrawal',
        `fraud_status` tinyint(4) NOT NULL COMMENT '0 = review, 3 = not fraud',
        `first_sale_date` int(11) NOT NULL,
        `created_at` int(11) NOT NULL,
        `updated_at` int(11) NOT NULL,
        PRIMARY KEY (`customers_id`),
        INDEX index_opt_status (`opt_status`),
        INDEX index_aft_status (`aft_status`),
        INDEX index_created_at (`created_at`)
      ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => ""
);

$add_new_tables["g2g_fraud_remarks"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `g2g_fraud_remarks` (
        `customers_id` int(11) NOT NULL,
        `remarks_type` varchar(32) NOT NULL,
        `remarks` text NOT NULL,
        `changed_by` varchar(128) DEFAULT 'system',
        `created_at` int(11) NOT NULL,
        PRIMARY KEY (`customers_id`, `remarks_type`,`created_at`),
        INDEX index_customers_id (`customers_id`),
        INDEX index_created_at (`created_at`)
      ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => ""
);

add_new_tables($add_new_tables, $DBTables);