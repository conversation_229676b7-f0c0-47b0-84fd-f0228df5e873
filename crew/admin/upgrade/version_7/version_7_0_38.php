<?php 
// Insert new DragonPay
$conf_insert_sql = array();
$conf_insert_sql[887] = array("insert" => " (887, 'dragonpay.7eleven', '7-11', 'Dragonpay', 572, 'pipwavePG', 0, 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 887 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[887] = array("insert" => " (887, 'dragonpay.7eleven', '7-11', 'Dragonpay', 572, 'pipwavePG', 0, 5, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 887 AND site_id = 5");

// Insert new Checkout.com
$conf_insert_sql = array();
$conf_insert_sql[881] = array("insert" => " (881, 'checkoutdotcom.visa', 'Visa', 'Checkout.com', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 881 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[882] = array("insert" => " (882, 'checkoutdotcom.mastercard', 'Mastercard', 'Checkout.com', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 882 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[883] = array("insert" => " (883, 'checkoutdotcom.amex', 'Amex', 'Checkout.com', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 883 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[884] = array("insert" => " (884, 'checkoutdotcom.bancontact', 'Bancontract', 'Checkout.com', 572, 'pipwavePG', 0, 5, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 884 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[885] = array("insert" => " (885, 'checkoutdotcom.poli', 'POLi', 'Checkout.com', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 885 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[886] = array("insert" => " (886, 'checkoutdotcom.giropay', 'Giropay', 'Checkout.com', 572, 'pipwavePG', 0, 5, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 886 AND site_id = 5");
?>