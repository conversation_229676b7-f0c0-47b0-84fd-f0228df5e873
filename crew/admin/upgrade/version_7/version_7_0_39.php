<?php 
// Insert new Openbucks
$conf_insert_sql = array();
$conf_insert_sql[888] = array("insert" => " (888, 'openbucks.cvspharmacy', 'CVS Pharmacy', 'Openbucks', 572, 'pipwavePG', 0, 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 888 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[888] = array("insert" => " (888, 'openbucks.cvspharmacy', 'CVS Pharmacy', 'Openbucks', 572, 'pipwavePG', 0, 5, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 888 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[889] = array("insert" => " (889, 'openbucks.dollargeneral', 'Dollar General', 'Openbucks', 572, 'pipwavePG', 0, 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 889 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[889] = array("insert" => " (889, 'openbucks.dollargeneral', 'Dollar General', 'Openbucks', 572, 'pipwavePG', 0, 5, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 889 AND site_id = 5");
?>