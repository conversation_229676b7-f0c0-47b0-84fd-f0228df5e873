<?php

/*
 * Permission to set subordinate ( Authorized Group ) Daily Credit Limit
 */

$add_field['admin_credit_limit'] = array(
    array(
        'field_name' => 'reset_team_limit_used',
        'field_attr' => "decimal(15, 4) NOT NULL DEFAULT '0.0000'",
        'add_after' => 'admin_credit_limit_total'
    ),
    array(
        'field_name' => 'reset_team_limit_max',
        'field_attr' => "decimal(15, 4) NOT NULL DEFAULT '0.0000'",
        'add_after' => 'reset_team_limit_used'
    )
);
add_field($add_field);

// permission control
$sql = "SELECT admin_files_id, admin_groups_id
        FROM " . TABLE_ADMIN_FILES . "
        WHERE TRIM(LCASE(admin_files_name)) = 'admin_members.php' AND admin_files_is_boxes = 0";
$res = tep_db_query($sql);
if ($row = tep_db_fetch_array($res)) {
    $action["ADMIN_MANAGE_TEAM_CREDIT_LIMIT"] = array("insert" => " ('ADMIN_MANAGE_TEAM_CREDIT_LIMIT', 'Manage Team ( Authorized Group ) Daily Credit Limit', " . $row["admin_files_id"] . ", '1', 26)");
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $action, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
?>