<?php
// Update Pipwave payment mapper RP status

// OG Pipwave Mapper
// Adyen
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::aktia' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::alandsbanken' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::danskebank' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::handelsbanken' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::oma_saastopankki' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::osuuspankki' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::paikallisosuuspankit' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::spankki' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::saastopankki' AND site_id = 0");

// Skrill
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'moneybooker.neteller' AND site_id = 0");

// G2G Pipwave Mapper
// Skrill
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'moneybooker.neteller' AND site_id = 5");

// Update both site mapper
// DOKU
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'doku.doku_alfamart'");
// Offline
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'offline_paypal.masspayment'");
// DragonPay - Update PM & PG Name
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET pm_display_name = 'GCash', pg_display_name = 'Dragonpay' WHERE pipwave_payment_code = 'dragonpay.gcash'");
?>