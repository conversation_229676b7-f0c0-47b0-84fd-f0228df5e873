<?php
// Update Pipwave payment mapper RP status

// OG Pipwave Mapper
// Adyen
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.ebanking_FI::nordea' AND site_id = 0");

// G2G Pipwave Mapper
// Checkout.com
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'checkoutdotcom.bancontact' AND site_id = 5");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 1 WHERE pipwave_payment_code = 'checkoutdotcom.poli' AND site_id = 5");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'checkoutdotcom.giropay' AND site_id = 5");

// Update both site mapper
// DaoPay
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'daopay.trustly'");

?>