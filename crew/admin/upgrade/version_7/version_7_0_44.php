<?php 
// Insert new Doku
$conf_insert_sql = array();
$conf_insert_sql[893] = array("insert" => " (893, 'doku.doku_ovo', 'OVO', 'DOKU', 572, 'pipwavePG', 0, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 893 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[893] = array("insert" => " (893, 'doku.doku_ovo', 'OVO', 'DOKU', 572, 'pipwavePG', 0, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 893 AND site_id = 5");
?>