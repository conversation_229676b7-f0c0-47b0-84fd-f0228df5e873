<?php
// Update Pipwave payment mapper RP status

// Smart2Pay - OG update
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.aktia' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.alandsbanken' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.danskebank' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.finnishhandelsbanken' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.omasaastopankki' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.oppohjola' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.poppankki' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.spankki' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 0 WHERE pipwave_payment_code = 'smart2pay.saastopankki' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.nordea' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'smart2pay.eps' AND site_id = 0");

// Adyen - OG update
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.eps::bbd44f4d-609b-454e-8d5a-e0d1ac21f15a' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.eps::64669764-e6fc-401c-8f3d-26e9169ba6ff' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.eps::54016d9d-31b0-4db3-9eaf-cf9fbc58ee81' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.eps::1d48e2f7-604c-4d1d-894e-170635d1a645' AND site_id = 0");
tep_db_query("UPDATE " . TABLE_PIPWAVE_PAYMENT_MAPPER . " SET is_rp = 2 WHERE pipwave_payment_code = 'adyen.hpp.eps::09350598-7ba4-4afb-b706-843e49a64759' AND site_id = 0");
?>