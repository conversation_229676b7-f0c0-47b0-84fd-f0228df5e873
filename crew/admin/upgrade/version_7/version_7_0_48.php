<?php 
// Insert new DaoPay
$conf_insert_sql = array();
$conf_insert_sql[894] = array("insert" => " (894, 'daopay.HAABAT2K', 'EPS Austrian Anadi Bank', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 894 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[894] = array("insert" => " (894, 'daopay.HAABAT2K', 'EPS Austrian Anadi Bank', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 894 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[895] = array("insert" => " (895, 'daopay.BKAUATWW', 'EPS Bank Austria', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 895 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[895] = array("insert" => " (895, 'daopay.BKAUATWW', 'EPS Bank Austria', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 895 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[896] = array("insert" => " (896, 'daopay.SPBAATWWXXX', 'EPS BANK99', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 896 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[896] = array("insert" => " (896, 'daopay.SPBAATWWXXX', 'EPS BANK99', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 896 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[897] = array("insert" => " (897, 'daopay.BAWAATWW', 'EPS BAWAG P.S.K.', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 897 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[897] = array("insert" => " (897, 'daopay.BAWAATWW', 'EPS BAWAG P.S.K.', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 897 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[898] = array("insert" => " (898, 'daopay.BFKKAT2K', 'EPS BKS Bank', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 898 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[898] = array("insert" => " (898, 'daopay.BFKKAT2K', 'EPS BKS Bank', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 898 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[899] = array("insert" => " (899, 'daopay.ESBKATWW', 'EPS Denizbank', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 899 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[899] = array("insert" => " (899, 'daopay.ESBKATWW', 'EPS Denizbank', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 899 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[900] = array("insert" => " (900, 'daopay.OVLIAT21XXX', 'EPS Dolomiten Bank', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 900 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[900] = array("insert" => " (900, 'daopay.OVLIAT21XXX', 'EPS Dolomiten Bank', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 900 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[901] = array("insert" => " (901, 'daopay.GIBAATWW', 'EPS Erste Group Bank', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 901 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[901] = array("insert" => " (901, 'daopay.GIBAATWW', 'EPS Erste Group Bank', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 901 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[902] = array("insert" => " (902, 'daopay.MVOGAT22XXX', 'EPS Marchfelder Bank', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 902 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[902] = array("insert" => " (902, 'daopay.MVOGAT22XXX', 'EPS Marchfelder Bank', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 902 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[903] = array("insert" => " (903, 'daopay.OBKLAT2L', 'EPS Oberbank', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 903 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[903] = array("insert" => " (903, 'daopay.OBKLAT2L', 'EPS Oberbank', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 903 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[904] = array("insert" => " (904, 'daopay.RLNWATWW', 'EPS Raiffeisen Bank', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 904 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[904] = array("insert" => " (904, 'daopay.RLNWATWW', 'EPS Raiffeisen Bank', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 904 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[905] = array("insert" => " (905, 'daopay.BTVAAT22', 'EPS VIER LÄNDER BANK', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 905 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[905] = array("insert" => " (905, 'daopay.BTVAAT22', 'EPS VIER LÄNDER BANK', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 905 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[906] = array("insert" => " (906, 'daopay.VKBLAT2LXXX', 'EPS Volkskreditbank', 'DaoPay', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 906 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[906] = array("insert" => " (906, 'daopay.VKBLAT2LXXX', 'EPS Volkskreditbank', 'DaoPay', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 906 AND site_id = 5");

// Checkout.com
$conf_insert_sql = array();
$conf_insert_sql[909] = array("insert" => " (909, 'checkoutdotcom.applepay', 'Applepay', 'Checkout.com', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 909 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[909] = array("insert" => " (909, 'checkoutdotcom.applepay', 'Applepay', 'Checkout.com', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 909 AND site_id = 5");

$conf_insert_sql = array();
$conf_insert_sql[910] = array("insert" => " (910, 'checkoutdotcom.googlepay', 'Googlepay', 'Checkout.com', 572, 'pipwavePG', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 910 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[910] = array("insert" => " (910, 'checkoutdotcom.googlepay', 'Googlepay', 'Checkout.com', 572, 'pipwavePG', 1, 5, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 910 AND site_id = 5");

// Adyen PG - OG Only
$conf_insert_sql = array();
$conf_insert_sql[908] = array("insert" => " (908, 'adyen.hpp.applepay', 'Applepay', 'Adyen', 313, 'adyen', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 908 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[907] = array("insert" => " (907, 'adyen.hpp.googlepay', 'Googlepay', 'Adyen', 313, 'adyen', 1, 0, 1) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`, `is_refundable`)", "pm_id = 907 AND site_id = 0");
?>