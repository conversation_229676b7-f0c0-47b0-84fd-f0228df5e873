<?php

$existing_store_refund_info_fields = get_table_fields(TABLE_CUSTOMERS_VERIFICATION_DOCUMENT);

if (!in_array('files_001_status', $existing_store_refund_info_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . " ADD `files_001_status` tinyint(1) NOT NULL default 0 AFTER `files_001_locked`;");
}

if (!in_array('files_002_status', $existing_store_refund_info_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . " ADD `files_002_status` tinyint(1) NOT NULL default 0 AFTER `files_002_locked`;");
}

if (!in_array('files_003_status', $existing_store_refund_info_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . " ADD `files_003_status` tinyint(1) NOT NULL default 0 COMMENT '0 = Pending, 1 = Approved, 2 = Denied' AFTER `files_003_locked`;");
}

if (!in_array('files_004_status', $existing_store_refund_info_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . " ADD `files_004_status` tinyint(1) NOT NULL default 0 AFTER `files_004_locked`;");
}

if (!in_array('files_005_status', $existing_store_refund_info_fields)) {
    tep_db_query("ALTER TABLE " . TABLE_CUSTOMERS_VERIFICATION_DOCUMENT . " ADD `files_005_status` tinyint(1) NOT NULL default 0 AFTER `files_005_locked`;");
}

// insert new permission control on customer.php: Approve/Deny document ID [start]
$select_sql = "	SELECT admin_files_id, admin_groups_id
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();

    $admin_files_actions_insert_sql["CUSTOMER_APPROVE_DOC_ID"] = array("insert" => " ('CUSTOMER_APPROVE_DOC_ID', 'Approve/Deny Customer document', " . $row_sql["admin_files_id"] . ", '1', 59)");
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// insert new permission control on customer.php: Approve/Deny document ID [end]

// Add new Adyen pipwave payment mapper
$conf_insert_sql = array();
$conf_insert_sql[777] = array("insert" => " (777, 'adyen.hpp.multibanco', 'Multibanco', 'Adyen', 313, 'adyen', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 777 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[777] = array("insert" => " (777, 'adyen.hpp.multibanco', 'Multibanco', 'Adyen', 313, 'adyen', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 777 AND site_id = 5");