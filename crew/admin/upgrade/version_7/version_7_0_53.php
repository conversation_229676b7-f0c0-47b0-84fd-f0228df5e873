<?php 

$add_new_tables["customer_info_lock"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `customer_info_lock` (
                       `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT, 
                        `customer_id` INT(11) UNSIGNED NOT NULL , 
                        `info_key` VARCHAR(256) NOT NULL , 
                        `locked` BOOLEAN NOT NULL , 
                        `updated_at` INT(11) UNSIGNED NOT NULL,
                        PRIMARY KEY  (`id`),
                        KEY  `index_customer_id_info_key` (`info_key`,`locked`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");

$add_new_tables["customer_info_lock_history"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `customer_info_lock_history` (
                        `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT, 
                        `customer_info_lock_id` INT(11) UNSIGNED NOT NULL , 
                        `action` VARCHAR(256) NOT NULL , 
                        `remark` VARCHAR(256) NOT NULL , 
                        `created_at` INT(11) UNSIGNED NOT NULL,
                        `updated_by` VARCHAR(256) NOT NULL , 
                        `locked` BOOLEAN NOT NULL , 
                        PRIMARY KEY  (`id`),
                        KEY `index_customer_info_lock_id` (`customer_info_lock_id`)
                    ) ENGINE = InnoDB, DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;",
    "data" => "");
add_new_tables ($add_new_tables, $DBTables);


// insert new permission control on customer.php: Lock/Unlock dob lock [start]
$select_sql = "	SELECT admin_files_id, admin_groups_id
				FROM " . TABLE_ADMIN_FILES . "
				WHERE TRIM(LCASE(admin_files_name))='customers.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();

    $admin_files_actions_insert_sql["CUSTOMER_DOB_LOCKING"] = array("insert" => " ('CUSTOMER_DOB_LOCKING', 'Lock/Unlock Customer DOB change', " . $row_sql["admin_files_id"] . ", '1', 59)");
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
// insert new permission control on customer.php: Lock/Unlock dob lock [end]
?>
