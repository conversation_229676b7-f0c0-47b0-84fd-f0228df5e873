<?php
# insert new record for OG & G2G
$conf_insert_sql = array();
$conf_insert_sql[778] = array("insert" => " (778, 'offline_dbs_paynow.dbspaynow', 'DBS Paynow', 'Offline', 12, 'offline', 0, 0) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 778 AND site_id = 0");

$conf_insert_sql = array();
$conf_insert_sql[778] = array("insert" => " (778, 'offline_dbs_paynow.dbspaynow', 'DBS Paynow', 'Offline', 12, 'offline', 0, 5) ");
insert_new_records(TABLE_PIPWAVE_PAYMENT_MAPPER, "pm_id", $conf_insert_sql, $DBTables, "(`pm_id`, `pipwave_payment_code`, `pm_display_name`, `pg_display_name`, `pg_id`, `pg_code`, `is_rp`, `site_id`)", "pm_id = 778 AND site_id = 5");
?>