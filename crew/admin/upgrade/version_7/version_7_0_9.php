<?php

// Add new store file into admin_files
$select_sql = "	SELECT admin_files_id FROM " . TABLE_ADMIN_FILES . " WHERE admin_files_name = 'reports.php'";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) {
    $admin_files_insert_sql = array();
    $admin_files_insert_sql["mgc_sales_report.php"] = array("insert" => " ('mgc_sales_report.php', 0, '" . $row_sql["admin_files_id"] . "', '1') ");
    insert_new_records(TABLE_ADMIN_FILES, "admin_files_name", $admin_files_insert_sql, $DBTables, "(admin_files_name, admin_files_is_boxes, admin_files_to_boxes, admin_groups_id)", " admin_files_name='mgc_sales_report.php' AND admin_files_is_boxes=0 ");
}
?>