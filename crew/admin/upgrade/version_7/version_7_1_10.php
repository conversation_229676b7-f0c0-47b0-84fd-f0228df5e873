<?php
include_once("includes/classes/page_template.php");

tep_db_query("ALTER TABLE " . TABLE_GAME_BLOG . " ADD custom_url varchar(255) unique");

$selectSql = "SELECT game_blog_id, game_blog_description 
              FROM " . TABLE_GAME_BLOG_DESCRIPTION .  " GBD 
              WHERE language_id = 1";
$selectResultsSql = tep_db_query($selectSql);

while($row = tep_db_fetch_array($selectResultsSql)){
    $pageTemplate = new page_template();
    $customUrl = $pageTemplate->format_url($row['game_blog_description']);

    $updateSql = "UPDATE IGNORE " . TABLE_GAME_BLOG . 
                 " SET custom_url = '" . $customUrl . "' 
                 WHERE game_blog_id = " . intval($row['game_blog_id']);
    
    tep_db_query($updateSql);
}