<?php

$add_new_tables["customers_mobile_change_approval"] = array(
    "structure" => "CREATE TABLE customers_mobile_change_approval (
        customers_id int(11) NOT NULL,
        street_address varchar(64),
        suburb varchar(32),
        postcode varchar(10),
        city varchar(32),
        state varchar(32),
        country_id int(11),
        zone_id int(11),
        dialing_code_id int(11),
        telephone varchar(32),
        approval_status tinyint,
        approved_status_updated_by varchar(255),
        approval_status_datetime datetime,
        created_datetime datetime,
        updated_datetime datetime
    ) ENGINE=InnoDB;
    ",
    "data" => "");

add_new_tables ($add_new_tables, $DBTables);

// add multi keys
$update_table_sql = "ALTER TABLE customers_mobile_change_approval
ADD UNIQUE INDEX id_key (customers_id);
";

tep_db_query($update_table_sql);
?>