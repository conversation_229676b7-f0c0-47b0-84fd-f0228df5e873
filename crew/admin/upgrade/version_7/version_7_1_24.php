<?php

// insert new permission control on release CDK
$select_sql = "	SELECT admin_files_id, admin_groups_id
                FROM " . TABLE_ADMIN_FILES . "
                WHERE TRIM(LCASE(admin_files_name))='orders.php' AND admin_files_is_boxes=0";
$result_sql = tep_db_query($select_sql);
if ($row_sql = tep_db_fetch_array($result_sql)) { // if found existing record
    $admin_files_actions_insert_sql = array();

    $admin_files_actions_insert_sql["RELEASE_CDK"] = array("insert" => " ('RELEASE_CDK', 'Release CDK', " . $row_sql["admin_files_id"] . ", '1', 10)");
    insert_new_records(TABLE_ADMIN_FILES_ACTIONS, "admin_files_actions_key", $admin_files_actions_insert_sql, $DBTables, "(`admin_files_actions_key`, `admin_files_actions_name`, `admin_files_id`, `admin_groups_id`, `admin_files_sort_order`)");
}
?>
