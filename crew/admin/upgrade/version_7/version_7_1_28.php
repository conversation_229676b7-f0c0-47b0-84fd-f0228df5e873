<?php

$add_new_tables = array();
$add_new_tables["g2g_og_order_mapper"] = array(
    "structure" => "CREATE TABLE IF NOT EXISTS `g2g_og_order_mapper` (
                `id` bigint(20) NOT NULL auto_increment,
                `og_order_id` bigint(20) NOT NULL,
                `g2g_order_id` varchar(64) NOT NULL,
                `delivery_id` varchar(64) NOT NULL,
                `extra_info` text,
                `status` int(3),
                `error` int(3),
                `created_at` int(11) NOT NULL,
                `updated_at` int(11) NOT NULL,
                PRIMARY KEY (`id`)
             ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;",
    "data" => "");

add_new_tables($add_new_tables, $DBTables);
add_index_key ('g2g_og_order_mapper', 'index_og_order_id', 'index', 'og_order_id', $DBTables);
add_index_key ('g2g_og_order_mapper', 'index_g2g_order_id', 'index', 'g2g_order_id', $DBTables);
add_index_key ('g2g_og_order_mapper', 'index_status', 'index', 'status', $DBTables);
add_index_key ('g2g_og_order_mapper', 'index_created_at', 'index', 'created_at', $DBTables);
?>
