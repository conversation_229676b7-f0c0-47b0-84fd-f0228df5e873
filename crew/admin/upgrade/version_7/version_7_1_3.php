<?php

/*
 * trim email address in bounce log
 */

$sql = "SELECT email, created_datetime FROM " . TABLE_LOG_SES_BOUNCE;
$res = tep_db_query($sql);
while ($row = tep_db_fetch_array($res)) {
    $str = explode(" ", $row["email"]);
    $email_address = trim(array_pop($str), "<>");

    if (!empty($email_address) && ($email_address != $row["email"])) {
        $_sql = "SELECT email FROM " . TABLE_LOG_SES_BOUNCE . " WHERE email = '" . $email_address . "'";
        $_res = tep_db_query($_sql);
        if (tep_db_num_rows($_res)) {
            tep_db_query("DELETE FROM " . TABLE_LOG_SES_BOUNCE . " WHERE email = '" . str_replace("'", "\'", $row["email"]) . "' AND created_datetime = '" . $row["created_datetime"] . "'");
        } else {
            tep_db_query("UPDATE " . TABLE_LOG_SES_BOUNCE . " SET email = '" . $email_address . "' WHERE email = '" . str_replace("'", "\'", $row["email"]) . "' AND created_datetime = '" . $row["created_datetime"] . "' LIMIT 1");
        }
    }
}
?>