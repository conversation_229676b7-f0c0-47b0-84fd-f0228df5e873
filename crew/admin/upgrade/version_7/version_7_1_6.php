<?php

    $add_new_tables["store_payments_extra_info"] = array(
        "structure" => "CREATE TABLE store_payments_extra_info (
            store_payments_id bigint(20) NOT NULL,
            store_payments_extra_info_key varchar(255) NOT NULL,
            store_payments_extra_info_value varchar(255) NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
        ",
        "data" => "");

    add_new_tables ($add_new_tables, $DBTables);

    // add multi keys
    $update_table_sql = "ALTER TABLE store_payments_extra_info
    ADD INDEX id_key (store_payments_id,store_payments_extra_info_key);
";

    tep_db_query($update_table_sql);

?>