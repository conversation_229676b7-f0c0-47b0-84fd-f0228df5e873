<?
require('includes/application_top.php');
require(DIR_WS_FUNCTIONS . 'supplier.php');
require(DIR_WS_CLASSES . 'vip_inventory_report.php');
require(DIR_WS_CLASSES . 'vip_order.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');
$pID = (isset($_REQUEST['pID']) ? $_REQUEST['pID'] : '');
$opID = (isset($_REQUEST['opID']) ? $_REQUEST['opID'] : '');
$report = new vip_inventory_report();

switch ($action) {
	case 'show_report':
		if ($_REQUEST['cont'] && !isset($_SESSION['vip_param']['vip_stock_select_sql'])) {
			tep_redirect(tep_href_link(FILENAME_VIP_INVENTORY_REPORT));
		} else if (!$_REQUEST['cont'] && $_POST['report'] == '1' && $_POST['cat_id'] == '') {
			tep_redirect(tep_href_link(FILENAME_VIP_INVENTORY_REPORT));
		} else if (!$_REQUEST['cont']) {
			unset($_SESSION['vip_param']['vip_stock_select_sql']);
			
			$_SESSION['vip_param']['report'] = $_POST['report'];
			$_SESSION['vip_param']['cat_id'] = $_POST['cat_id'];
			$_SESSION['vip_param']['include_subcategory'] = $_POST['report'] == 1 ? $_POST['include_subcategory'] : 1 ;
			$_SESSION['vip_param']['show_records'] = $_POST['show_records'];
	  	}
	  	if ($_SESSION['vip_param']['report'] == '1') {
	  		$heading_title = HEADING_VIP_INVENTORY_REPORT;
	  	} else {
	  		$heading_title = HEADING_BUYBACK_FOLLOW_UP_REPORT;
	  	}
	  	$report->set_mode($_SESSION['vip_param']['report']);
	  	$report->set_main_category_id($_SESSION['vip_param']['cat_id'], $_SESSION['vip_param']['include_subcategory']);
	  	if ($_SESSION['vip_param']['report'] == '1') {
			$report->set_sorting_info(array(array('p.products_cat_path', 'ASC'), array('p.products_sort_order', 'ASC'), array('pd.products_name', 'ASC')));
		} else if ($_SESSION['vip_param']['report'] == '2') {
			$report->set_sorting_info(array(array('p.products_cat_path', 'DESC'), array('p.products_sort_order', 'DESC')));
		}
		$report->reportQuery();
		$form_content = $report->display_report();
		break;
	case 'reset_session':
    	unset($_SESSION['vip_param']);
    	tep_redirect(tep_href_link(FILENAME_VIP_INVENTORY_REPORT));
    	break;
    case 'export_report':
    	$export_csv_array = tep_array_unserialize($HTTP_POST_VARS['serialized_export_csv_array']);
		$export_csv_data = '';
		
		if (count($export_csv_array)) {
			foreach ($export_csv_array as $res) {
				$tmp_csv_data_array = array();
				for ($export_cnt=0; $export_cnt < count($res); $export_cnt++) {
					$tmp_csv_data_array[] = '"' . str_replace(array('"', '<br>', '<BR>'), array('""', ''), $res[$export_cnt]) . '"';
				}
				$export_csv_data .= implode(',', $tmp_csv_data_array) . "\n";
			}
		}
		
		if (tep_not_null($export_csv_data)) {
			$filename = $_REQUEST['filename'].'_'.date('YmdHis').'.csv';
			$mime_type = 'text/x-csv';
			// Download
	        header('Content-Type: ' . $mime_type);
	        header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
	        // IE need specific headers
	        if (PMA_USR_BROWSER_AGENT == 'IE') {
	            header('Content-Disposition: inline; filename="' . $filename . '"');
	            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
	            header('Pragma: public');
	        } else {
	            header('Content-Disposition: attachment; filename="' . $filename . '"');
	            header('Pragma: no-cache');
	        }
			echo $export_csv_data;
			exit();
		} else {
			$messageStack->add_session(WARNING_NOTHING_TO_EXPORT, 'warning');
			tep_redirect(tep_href_link(FILENAME_VIP_INVENTORY_REPORT));
		}
    	break;
    default : 
    	$heading_title = HEADING_BUYBACK_FOLLOW_UP_CRITERIA;
    	$form_content = $report->report_criteria(FILENAME_VIP_INVENTORY_REPORT);
    	break;
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?($action=='supplier_detail' ? CHARSET_CHINESE : CHARSET)?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script type="text/javascript" language="javascript" src="includes/general.js"></script>
<script language="JavaScript" src="includes/javascript/select_box.js"></script>
<script language="javascript" src="includes/javascript/modal_win.js"></script>
<script language="javascript" src="includes/javascript/supplier_xmlhttp.js"></script>
<script language="javascript" src="includes/javascript/jquery.js"></script>

<!-- TableSorting //-->
<script language="javascript" src="includes/javascript/jquery.tablesorter.js"></script>
<style type="text/css">@import "includes/tablesorter.css";</style>
<!-- End Of TableSorting //-->
<!-- Popup //-->
<script language="JavaScript" src="<?=DIR_WS_INCLUDES?>addon/scriptasylum_popup/popup.js"></script>
<!-- End of Popup //-->
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<? if($action != 'supplier_detail'){?>
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
<? }?>
<script type="text/javascript">
<?	if ($_SESSION['vip_param']['report'] == '2') { ?>
	    var page_url = "<?=tep_href_link(basename($PHP_SELF), tep_get_all_get_params(array('cont'))."cont=1")?>";
	    setAutoRefresh("5:00", page_url); // refresh in 5min
<?	} ?>
</script>
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    		<? if($action != 'supplier_detail'){?>
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		<? }?>
			</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
						<td>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td>
										<table border="0" width="100%" cellspacing="2" cellpadding="0">
											<tr>
												<td class="pageHeading" valign="top"><?=$heading_title?></td>
												<td class="pageHeading" align="right"><?=tep_draw_separator('pixel_trans.gif', '1', '40')?></td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
							<table border="0" width="100%" cellspacing="0" cellpadding="0">
								<tr>
									<td>
										<?=$form_content?>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
<? if($action != 'supplier_detail'){?>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
<? }?>
<script type="text/javascript">
	var c_name = 'vip_report_sort_info';	// Cookie name
	jQuery(function() {
		jQuery("#report_table").tablesorter({
			sortList: <?=isset($_COOKIE['vip_report_sort_info']) ?  $_COOKIE['vip_report_sort_info'] : '[[0,1]]' ;?>
		});
	});
</script>
</body>
</html>