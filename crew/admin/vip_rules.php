<?
require('includes/application_top.php');
require(DIR_WS_CLASSES . 'vip_rules.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

$vip_rules_object = new vip_rules();

switch($subaction) {
    case "update_rules":
		$subaction_res = $vip_rules_object->update_rules($HTTP_POST_VARS, $messageStack);
		
		tep_redirect(tep_href_link(FILENAME_VIP_RULES, tep_get_all_get_params(array('action', 'subaction'))));
		
		break;
	default:
		;	// Nothing to perform
		break;
}

switch($action) {
	default:
		$header_title = HEADER_FORM_VIP_RULES_TITLE;
		$form_content = $vip_rules_object->show_rules_form(FILENAME_VIP_RULES);
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script type="text/javascript" language="javascript" src="includes/general.js"></script>
<script language="JavaScript" src="includes/javascript/select_box.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
			</td>
			<td valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
					<tr>
        				<td width="100%" class="pageHeading"><b><?=$header_title?></b></td>
        			</tr>
        			<tr>
        				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
      				</tr>
      				<tr>
        				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>