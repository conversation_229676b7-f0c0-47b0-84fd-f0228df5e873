<?
require('includes/application_top.php');
require_once(DIR_WS_CLASSES . 'vip_groups.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');

$vip_groups_object = new vip_groups(0);

switch ($subaction) {
	case "insert_region_group":
	case "update_region_group":
		$subaction_res = $vip_groups_object->update_rank_region($_REQUEST, $messageStack);
		
		if ($subaction_res == true) {
			tep_redirect(tep_href_link(FILENAME_VIP_SUPPLIER_RANKING, tep_get_all_get_params(array('action', 'subaction', 'rgID'))));
		} else {
			tep_redirect(tep_href_link(FILENAME_VIP_SUPPLIER_RANKING, tep_get_all_get_params(array('subaction'))));
		}
		
		break;
	case "insert_ranking":
	case "update_ranking":
		$subaction_res = $vip_groups_object->update_rank($_REQUEST, $messageStack);
		
		if ($subaction_res == true) {
			tep_redirect(tep_href_link(FILENAME_VIP_SUPPLIER_RANKING, tep_get_all_get_params(array('action', 'subaction', 'rgID'))));
		} else {
			tep_redirect(tep_href_link(FILENAME_VIP_SUPPLIER_RANKING, tep_get_all_get_params(array('subaction'))));
		}
		
		break;
	case "delete_ranking":
		tep_redirect(tep_href_link(FILENAME_VIP_SUPPLIER_RANKING));		
		break;
}

switch($action) {
	case "new_region_group":
	case "edit_region_group":
		$header_title = $action == "new_region_group" ? HEADER_FORM_VIP_RANKING_NEW_REGION : HEADER_FORM_VIP_RANKING_EDIT_REGION;
		$form_content = $vip_groups_object->manage_rank_region(FILENAME_VIP_SUPPLIER_RANKING, $_REQUEST);
		
    	break;
    case "delete_region_group":
    	$vip_groups_object->delete_rank_region($_REQUEST["rgID"], $messageStack);
    	
		tep_redirect(tep_href_link(FILENAME_VIP_SUPPLIER_RANKING));		
		
		break;
	case "new_ranking":
	case "edit_ranking":
		$header_title = $action == "new_ranking" ? HEADER_FORM_VIP_RANKING_NEW_RANK : HEADER_FORM_VIP_RANKING_EDIT_RANK;
		$form_content = $vip_groups_object->manage_rank(FILENAME_VIP_SUPPLIER_RANKING, $_REQUEST);
		
    	break;
    case "delete_ranking":
    	$vip_groups_object->delete_rank($_REQUEST["rkID"], $messageStack);
    	
		tep_redirect(tep_href_link(FILENAME_VIP_SUPPLIER_RANKING));		
		
		break;
	default:
		$header_title = HEADER_FORM_VIP_RANKING;
		$form_content = $vip_groups_object->show_ranking_list(FILENAME_VIP_SUPPLIER_RANKING);
		
		break;
}
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?> >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script type="text/javascript" language="javascript" src="includes/general.js"></script>
<script language="JavaScript" src="includes/javascript/select_box.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?php echo BOX_WIDTH; ?>" valign="top">
    			<table border="0" width="<?php echo BOX_WIDTH; ?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
			</td>
			<td valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">	
					<tr>
        				<td width="100%" class="pageHeading"><b><?=$header_title?></b></td>
        			</tr>
					<tr>
	      				<td><?=tep_draw_separator('pixel_trans.gif', '1', '10')?></td>
	      			</tr>
	      			<tr>
        				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>	
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>