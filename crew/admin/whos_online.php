<?
/*
  	$Id: whos_online.php,v 1.4 2009/10/12 09:20:24 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

  $xx_mins_ago = (time() - 900);

  require('includes/application_top.php');

  require(DIR_WS_CLASSES . 'currencies.php');
  $currencies = new currencies();

// remove entries that have expired
tep_db_query("delete from " . TABLE_WHOS_ONLINE . " where time_last_click < '" . $xx_mins_ago . "'");
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?=HTML_PARAMS?>>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
<? if($_SERVER["QUERY_STRING"] == 600 ){  ?>
		<meta http-equiv="refresh" content="600;URL=<?=tep_href_link("whos_online.php", "600")?>">
<? } else if ($_SERVER["QUERY_STRING"] == 300){ ?>
		<meta http-equiv="refresh" content="300;URL=<?=tep_href_link("whos_online.php", "300")?>">
<? } else if ($_SERVER["QUERY_STRING"] == 60){ ?>
		<meta http-equiv="refresh" content="60;URL=<?=tep_href_link("whos_online.php", "60")?>">
<? } else if ($_SERVER["QUERY_STRING"] == 30){ ?>
		<meta http-equiv="refresh" content="30;URL=<?=tep_href_link("whos_online.php", "30")?>">
<? } else { ?>
<? } ?>
<title><?=TITLE?></title>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->

<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
  		<tr>
    		<td width="<?=BOX_WIDTH?>" valign="top">
    			<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
				<!-- left_navigation //-->
				<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
				<!-- left_navigation_eof //-->
    			</table>
    		</td>
<!-- body_text //-->
    		<td width="100%" valign="top">
    			<table border="0" width="100%" cellspacing="0" cellpadding="2">
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td class="pageHeading">
            							<?=HEADING_TITLE?>
            							<span class="dataTableContent" style="font-size: 10px; color:#909090">Set Refresh Rate:</span>
            							<input type='button' value='None' onClick="location.href='<?=tep_href_link("whos_online.php")?>'">
							            <input type='button' value='10 min' onClick="location.href='<?=tep_href_link("whos_online.php", "600")?>'">
							            <input type='button' value='5 min' onClick="location.href='<?=tep_href_link("whos_online.php", "300")?>'">
							            <input type='button' value='60 sec' onClick="location.href='<?=tep_href_link("whos_online.php", "60")?>'">
							            <input type='button' value='30 sec' onClick="location.href='<?=tep_href_link("whos_online.php", "30")?>'">
	  								</td>
          						</tr>
        					</table>
        				</td>
      				</tr>
      				<tr>
        				<td>
        					<table border="0" width="100%" cellspacing="0" cellpadding="0">
          						<tr>
            						<td valign="top">
            							<table border="0" width="100%" cellspacing="0" cellpadding="2">
              								<tr class="dataTableHeadingRow">
                								<td class="dataTableHeadingContent"><?=TABLE_HEADING_ONLINE?></td>
                								<td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_CUSTOMER_ID?></td>
								                <td class="dataTableHeadingContent"><?=TABLE_HEADING_FULL_NAME?></td>
								                <td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_IP_ADDRESS?></td>
								                <td class="dataTableHeadingContent" align="center"><?="WhoIs"?></td>
								                <td class="dataTableHeadingContent"><?=TABLE_HEADING_ENTRY_TIME?></td>
								                <td class="dataTableHeadingContent" align="center"><?=TABLE_HEADING_LAST_CLICK?></td>
								                <td class="dataTableHeadingContent"><?=TABLE_HEADING_LAST_PAGE_URL?>&nbsp;</td>
								                <td class="dataTableHeadingContent"><?=TABLE_HEADING_HTTP_REFERER?>&nbsp;</td>
              								</tr>
<?
$whos_online_query = tep_db_query("select customer_id, full_name, ip_address, time_entry, time_last_click, last_page_url, http_referer, session_id from " . TABLE_WHOS_ONLINE . ' order by ip_address');
while ($whos_online = tep_db_fetch_array($whos_online_query)) {
	$time_online = (time() - $whos_online['time_entry']);
	if ((!isset($HTTP_GET_VARS['info']) || (isset($HTTP_GET_VARS['info']) && ($HTTP_GET_VARS['info'] == $whos_online['session_id']))) && !isset($info)) {
		$info = $whos_online['session_id'];
	}
	/* BEGIN COUNT MOD */
    if ($old_array['ip_address'] == $whos_online['ip_address']) {
    	$i++;
    }
	/* END COUNT MOD */
	
    if ($whos_online['session_id'] == $info) {
		if ($whos_online['http_referer'] != "") {
			$http_referer_url = $whos_online['http_referer'];
		}
      	echo '              				<tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)">' . "\n";
	} else {
		echo '              				<tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link(FILENAME_WHOS_ONLINE, tep_get_all_get_params(array('info', 'action')) . 'info=' . $whos_online['session_id'], 'NONSSL') . '\'">' . "\n";
    }
?>
								                <td class="dataTableContent"><?=gmdate('H:i:s', $time_online)?></td>
								                <td class="dataTableContent" align="center"><a HREF="customers.php?selected_box=customers&cID=<?=$whos_online['customer_id']?>&action=edit"><?=$whos_online['customer_id']?></a></td>
								                <td class="dataTableContent"><?=$whos_online['full_name']?></td>
								                <td class="dataTableContent" align="center"><?=$whos_online['ip_address']?></td>
												<td class="dataTableContent" align="center"><?=tep_show_ip($whos_online['ip_address'], "who is")?></td>
								                <td class="dataTableContent"><?=date('H:i:s', $whos_online['time_entry'])?></td>
								                <td class="dataTableContent" align="center"><?=date('H:i:s', $whos_online['time_last_click'])?></td>
								                <td class="dataTableContent"><a HREF="<?=(($request_type == 'SSL') ? HTTPS_SERVER : HTTP_SERVER) . $whos_online['last_page_url']?>" target=\"_blank\"><?php if (eregi_dep('^(.*)' . tep_session_name() . '=[a-f,0-9]+[&]*(.*)', $whos_online['last_page_url'], $array)) { echo $array[1] . $array[2]; } else { echo $whos_online['last_page_url']; } ?></a>&nbsp;</td>
<?
	if ($whos_online['http_referer'] == "") {
		echo '<td class="dataTableContent" align="center">'.TEXT_HTTP_REFERER_NOT_FOUND.'</td>';
	} else {
		echo '<td class="dataTableContent" align="center">'.TEXT_HTTP_REFERER_FOUND.'</td>';
	}
?>
              								</tr>
<?
 	$old_array = $whos_online;
}

if (!$i) {
	$i=0;
}
$total_dupes = $i;
$total_sess = tep_db_num_rows($whos_online_query);
$total_cust = $total_sess - $total_dupes;

if (isset($http_referer_url)) {
?>
  											<tr>
												<td class="smallText" colspan="8"><?='<strong>' . TEXT_HTTP_REFERER_URL . ':</strong> ' . $http_referer_url?></td>
  											</tr>
<?
}
?>
              								<tr>
                								<td class="smallText" colspan="7"><? echo sprintf(TEXT_NUMBER_OF_CUSTOMERS, $total_sess); print "<br>Duplicate IPs: $total_dupes<br>Real customers: $total_cust<br><br><br>Your IP Address: ".$_SERVER["REMOTE_ADDR"];?></td>
              								</tr>
            							</table>
            						</td>
<?
$heading = array();
$contents = array();

if (isset($info)) {
	$heading[] = array('text' => '<b>' . TABLE_HEADING_SHOPPING_CART . '</b>');
	
    if (STORE_SESSIONS == 'mysql') {
      	$session_data = tep_db_query("select value from " . TABLE_SESSIONS . " WHERE sesskey = '" . $info . "'");
      	$session_data = tep_db_fetch_array($session_data);
      	$session_data = trim($session_data['value']);
    } else {
      	if ( (file_exists(tep_session_save_path() . '/sess_' . $info)) && (filesize(tep_session_save_path() . '/sess_' . $info) > 0) ) {
        	$session_data = file(tep_session_save_path() . '/sess_' . $info);
        	$session_data = trim(implode('', $session_data));
      	}
	}
	
    if ($length = strlen($session_data)) {
      	if (PHP_VERSION < 4) {
        	$start_id = strpos($session_data, 'customer_id[==]s');
        	$start_cart = strpos($session_data, 'cart[==]o');
        	$start_currency = strpos($session_data, 'currency[==]s');
        	$start_country = strpos($session_data, 'customer_country_id[==]s');
        	$start_zone = strpos($session_data, 'customer_zone_id[==]s');
      	} else {
        	$start_id = strpos($session_data, 'customer_id|s');
        	$start_cart = strpos($session_data, 'cart|O');
        	$start_currency = strpos($session_data, 'currency|s');
        	$start_country = strpos($session_data, 'customer_country_id|s');
        	$start_zone = strpos($session_data, 'customer_zone_id|s');
      	}
		
      	for ($i=$start_cart; $i<$length; $i++) {
        	if ($session_data[$i] == '{') {
          		if (isset($tag)) {
            		$tag++;
          		} else {
            		$tag = 1;
          		}
        	} else if ($session_data[$i] == '}') {
          		$tag--;
        	} else if ( (isset($tag)) && ($tag < 1) ) {
          		break;
        	}
      	}
		
      	$session_data_id = substr($session_data, $start_id, (strpos($session_data, ';', $start_id) - $start_id + 1));
      	$session_data_cart = substr($session_data, $start_cart, $i);
      	$session_data_currency = substr($session_data, $start_currency, (strpos($session_data, ';', $start_currency) - $start_currency + 1));
      	$session_data_country = substr($session_data, $start_country, (strpos($session_data, ';', $start_country) - $start_country + 1));
      	$session_data_zone = substr($session_data, $start_zone, (strpos($session_data, ';', $start_zone) - $start_zone + 1));
		
      	session_decode($session_data_id);
      	session_decode($session_data_currency);
      	session_decode($session_data_country);
      	session_decode($session_data_zone);
      	session_decode($session_data_cart);
		
      	if (PHP_VERSION < 4) {
        	$broken_cart = $cart;
        	$cart = new shoppingCart;
        	$cart->unserialize($broken_cart);
      	}
		
      	if (is_object($cart)) {
        	$products = $cart->get_products();
        	for ($i = 0, $n = sizeof($products); $i < $n; $i++) {
          		$contents[] = array('text' => $products[$i]['quantity'] . ' x ' . $products[$i]['name']);
        	}
			
        	if (sizeof($products) > 0) {
          		$contents[] = array('text' => tep_draw_separator('pixel_black.gif', '100%', '1'));
          		$contents[] = array('align' => 'right', 'text'  => TEXT_SHOPPING_CART_SUBTOTAL . ' ' . $currencies->format($cart->show_total(), true, $currency));
        	} else {
          		$contents[] = array('text' => '&nbsp;');
        	}
      	}
	}
}

if ( (tep_not_null($heading)) && (tep_not_null($contents)) ) {
	echo '            				<td width="25%" valign="top">' . "\n";
	
    $box = new box;
    echo $box->infoBox($heading, $contents);
	
    echo '            				</td>' . "\n";
}
?>
          						</tr>
        					</table>
        				</td>
      				</tr>
    			</table>
    		</td>
<!-- body_text_eof //-->
  		</tr>
	</table>
<!-- body_eof //-->

<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<br>
</body>
</html>
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>