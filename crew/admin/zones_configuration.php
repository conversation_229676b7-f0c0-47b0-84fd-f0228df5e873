<?php
/*
	$Id: zones_configuration.php,v 1.4 2011/06/15 02:52:50 chingyen Exp $

	Developer: <PERSON>
	Copyright (c) 2007 SKC Ventrue

	Released under the GNU General Public License
*/

require('includes/application_top.php');
require(DIR_WS_CLASSES . 'zones_functions.php');
require(DIR_WS_CLASSES . 'zones_html.php');
require(DIR_WS_CLASSES . 'json.php');
require(DIR_WS_CLASSES . 'payment_methods.php');

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$geo_zone_id = (isset($_REQUEST['geo_zone_id']) ? $_REQUEST['geo_zone_id'] : '');
$zone_type = (isset($_REQUEST['zone_type']) ? $_REQUEST['zone_type'] : '');
$langID = ($_SESSION['languages_id'] ? $_SESSION['languages_id'] : 1);

$function = new zones_functions(); // Function - Actions
$html = new zones_HTML(); // HTML printout

switch($action) {
	case 'save':
	    if (tep_admin_files_actions(FILENAME_ZONES_INFO, 'ZONE_TYPE_' . $zone_type)) {
	        $function->save($zone_type, $geo_zone_id);
        }
		tep_redirect(tep_href_link(FILENAME_ZONES_INFO,'selected_box=localization&zone_type='.urlencode($zone_type).'&geo_zone_id='.urlencode($geo_zone_id)));
		break;
	default:
		$header_title = '';
		$form_content = $html->zonesListing();
		break;
}

?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html <?=HTML_PARAMS?> >
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?=CHARSET?>">
	<title><?=TITLE?></title>

	<link rel="stylesheet" type="text/css" href="includes/javascript/select2/css/select2.min.css">
	<link rel="stylesheet" href="includes/javascript/multi-dual-select/css/dualselect-1.0.min.css" />
	<link rel="stylesheet" type="text/css" href="includes/jquery.tree.css">
	<link rel="stylesheet" type="text/css" href="includes/ui.tabs.css">
	<link rel="stylesheet" type="text/css" href="includes/stylesheet.css?v=<?php echo filemtime('includes/stylesheet.css'); ?>">

	<script language="javascript" src="includes/javascript/jquery/1.11.0/jquery.min.js"></script>
	<script language="javascript" src="includes/javascript/jquery/migrate/jquery-migrate-1.4.1.min.js"></script>
	<script language="JavaScript" src="includes/javascript/jquery.ui.js"></script>
	<script type="text/javascript" src="includes/javascript/jquery.tree.js"></script>
	<script language="javascript" src="includes/general.js"></script>
	<script language="JavaScript" src="includes/javascript/ogm_jquery.js?v=<?php echo filemtime('includes/javascript/ogm_jquery.js'); ?>"></script>
	<script language="javascript" src="includes/javascript/jquery.tabs.js"></script>
	<script language="javascript" src="includes/javascript/jquery.selectboxes.js"></script>
	<script language="javascript" src="includes/javascript/php.packed.js"></script>
	<script language="javascript" src="includes/javascript/select2/js/select2.min.js"></script>
	<script language="javascript" src="includes/javascript/multi-dual-select/js/dualselect-1.0.min.js"></script>
	
	<script>
		jQuery(document).ready(function(){
			
			const dualselect1 = jQuery('#zone_categories_id').dualselect({
				moveOnSelect:false
			});
			
			const dualselect2 = jQuery('#zone_currency_id').dualselect({
				moveOnSelect:false
			});
			
			const dualselect3 = jQuery('#zone_languages_id').dualselect({
				moveOnSelect:false
			});

			jQuery("#zone_type").select2({
				minimumResultsForSearch: -1
			});

			jQuery("#geo_zone_id").select2({
				minimumResultsForSearch: -1
			});
			
		});
	</script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">
<!-- header //-->
<? require(DIR_WS_INCLUDES . 'header.php'); ?>
<!-- header_eof //-->
	<!-- body //-->
	<table border="0" width="100%" cellspacing="2" cellpadding="2">
		<tr>
			<td width="<?=BOX_WIDTH?>" valign="top">
				<table border="0" width="<?=BOX_WIDTH?>" cellspacing="1" cellpadding="1" class="columnLeft">
			<!-- left_navigation //-->
			<? require(DIR_WS_INCLUDES . 'column_left.php'); ?>
			<!-- left_navigation_eof //-->
				</table>
			</td>
			<!-- body_text //-->
			<td width="100%" valign="top">
				<table border="0" width="100%" cellspacing="0" cellpadding="2">
	 				<tr>
		 				<td width="100%" valign="top"><?=$form_content?></td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
<!-- footer //-->
<? require(DIR_WS_INCLUDES . 'footer.php'); ?>
<!-- footer_eof //-->
<? require(DIR_WS_INCLUDES . 'application_bottom.php'); ?>
</body>
</html>

