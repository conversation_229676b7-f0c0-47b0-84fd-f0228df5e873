<?php
/*
  	$Id: zones_html_xmlhttp.php,v 1.2 2011/06/07 10:05:30 chingyen Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/xml; charset=utf-8');

include_once('includes/application_top.php');
include_once(DIR_WS_LANGUAGES . 'english/' . FILENAME_ZONES_INFO);

$action = isset($_GET['action']) ? $_GET['action'] : '';
$cnt = isset($_GET['cnt']) ? (int)$_GET['cnt'] : '';
$geo_zone_id = isset($_GET['geo_zone_id']) ? (int)$_GET['geo_zone_id'] : '';
$lang_code = isset($_GET['lang_code']) ? $_GET['lang_code'] : '';

$result = '';

echo '<response>';

if (tep_not_null($action)) {
	if ( !isset($_SERVER['HTTP_REFERER']) ||
		(strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
		) {
		echo "You are not allowed to access from outside";
		echo '</response>';
		exit;
	}
	
	switch ($action) {
		case 'add_slider':
			$countries = array();
			$countries_select_all = array();
			
			$cnt = $cnt + 1;
			
			# ZONE COUNTRY
			$zone_country_sel_sql = "	SELECT c.countries_id, c.countries_name 
										FROM " . TABLE_ZONES_TO_GEO_ZONES . " AS ztgz 
										INNER JOIN " . TABLE_COUNTRIES . " AS c 
											ON ztgz.zone_country_id = c.countries_id 
										WHERE ztgz.geo_zone_id = '" . tep_db_input($geo_zone_id) . "'";
			$zone_country_res_sql = tep_db_query($zone_country_sel_sql);
			while ($zone_country_row = tep_db_fetch_array($zone_country_res_sql)) {
				$countries[] = array (	'id' => $zone_country_row['countries_id'], 
										'text' => $zone_country_row['countries_name']);
				$countries_select_all[] = $zone_country_row['countries_id'];
			}
			
			ob_start();
?>
			<tr id="slider_content_row_<?=$lang_code;?>_<?=$cnt;?>">
				<td>
					<table border="0" width="100%" cellspacing="0" cellpadding="2">
						<tr>
							<td class="dottedLine" colspan="3">&nbsp;</td>
						</tr>
						<tr>
							<td class="main" colspan="2">
								<a href="javascript: del_slider('<?=$lang_code;?>', '<?=$cnt;?>');"><img src="images/icons/delete.gif" border="0" alt="<?=TEXT_DELETE_SLIDER;?>" title="<?=TEXT_DELETE_SLIDER;?>" /></a>
							</td>
							<td class="main"><b><?=SELECT_APPLICABLE_COUNTRY;?></b></td>
						</tr>
						<tr>
							<td class="main" width="120px"><?=TEXT_THUMBNAIL_IMAGE;?><span style="color: red">*</span></td>
							<td class="main"><?=tep_draw_input_field('slider_thumbnail_image[' . $lang_code . '][' . $cnt . ']', '', ' id="slider_thumbnail_image_' . $lang_code . '_' . $cnt . '" size="115"'); ?></td>
							<td class="main" rowspan="6"><?php echo tep_draw_pull_down_menu('slider_country[' . $lang_code . '][' . $cnt . '][]', $countries , $countries_select_all, ' id="slider_country_' . $lang_code . '_' . $cnt . '" multiple size=10 style="min-width: 150px;"'); ?></td>
						</tr>
						<tr>
							<td class="main" width="120px">Desktop View<span style="color: red">*</span></td>
							<td class="main"><?=tep_draw_input_field('slider_image_url[' . $lang_code . '][' . $cnt . ']', '', ' id="slider_image_url_' . $lang_code . '_' . $cnt . '" size="115"'); ?></td>
						</tr>
						<tr>
							<td class="main" width="120px">Redirect Link<span style="color: red">*</span></td>
							<td class="main"><?=tep_draw_input_field('slider_background_image[' . $lang_code . '][' . $cnt . ']', '', ' id="slider_background_image_' . $lang_code . '_' . $cnt . '" size="115"'); ?></td>
						</tr>
						<tr>
							<td class="main" width="120px"><?=TEXT_CAPTION;?><span style="color: red">*</span></td>
							<td class="main"><?=tep_draw_input_field('slider_caption[' . $lang_code . '][' . $cnt . ']', '', ' id="slider_caption_' . $lang_code . '_' . $cnt . '" size="115"'); ?></td>
						</tr>
						<tr>
							<td class="main" width="120px"><?=TEXT_SORT_ORDER;?></td>
							<td class="main"><?=tep_draw_input_field('slider_sort_order[' . $lang_code . '][' . $cnt . ']', '', ' id="slider_sort_order_' . $lang_code . '_' . $cnt . '" size="20"'); ?></td>
						</tr>
						<tr>
							<td colspan="3">&nbsp;</td>
						</tr>
					</table>
				</td>
			</tr>
<?php
			$result = ob_get_contents();
			ob_end_clean();
	        
            echo '<result><![CDATA[' . $result . ']]></result>';
            echo '<row>' . (int)$cnt . '</row>';
    		break;
    		
		default:
			echo "<result>Unknown request!</result>";
			break;
	}
}

echo '</response>';
?>