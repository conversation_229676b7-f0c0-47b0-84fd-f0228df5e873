<?php

error_reporting(null);
error_reporting(E_ERROR);
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

include_once('includes/configure.php');

if (!isset($_SERVER['HTTP_REFERER']) ||
        (strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
) {
    echo "You are not allowed to access from outside";
    exit;
}


include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_FUNCTIONS . 'localization.php');
// include localization class
require_once(DIR_WS_CLASSES . 'json.php');
// include caching class
include_once(DIR_WS_CLASSES . 'cache_abstract.php');
include_once(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

ini_set("memory_limit", "64M");
tep_set_time_limit(0);

$min_length = 1;

$lang_id = $default_languages_id = 1;
if (isset($_REQUEST['languages_id']) && in_array((int) $_REQUEST['languages_id'], array(1, 2))) {
    $lang_id = (int) $_REQUEST['languages_id'];
}

tep_db_connect() or die('Unable to connect to database server!');

if (isset($_COOKIE['RegionalSet']['loc_country'])) {
    $country = $_COOKIE['RegionalSet']['loc_country'];
}

if (!tep_not_null($country) || !tep_country_exists($country)) {
    $cookies_started = false;
    # 1st: base on IP, 2nd : db config
    $country = tep_get_ip_country_id();
    if (empty($country))
        $country = STORE_COUNTRY;
}

// include localization class
include_once(DIR_WS_CLASSES . 'localization.php');

$localization = new localization($country);
$zone_info_array = $localization->get_zone_configuration();

$keyword = (isset($_REQUEST['keyword']) && strlen($_REQUEST['keyword']) >= $min_length ? tep_db_prepare_input($_REQUEST['keyword']) : '');
$keyword = htmlentities($keyword, ENT_QUOTES, "utf-8");

$max = (isset($_REQUEST['max']) && (int) $_REQUEST['max'] ? (int) $_REQUEST['max'] : 10);

$games_array = array();
if (tep_not_empty($keyword)) {
    $record_count = 0;
    $games_array = array();
    $id_array = array();

    if (is_array($zone_info_array[1]->zone_categories_id) && count($zone_info_array[1]->zone_categories_id)) {
        $categories_search_sql = "	SELECT DISTINCT categories_id, MATCH(search_value) AGAINST('" . tep_db_input($keyword) . "') AS score
									FROM " . TABLE_CATEGORIES_SEARCH . "
									WHERE MATCH(search_value) AGAINST('" . tep_db_input($keyword) . "')
                                    ORDER BY score DESC";
        $categories_search_result = tep_db_query($categories_search_sql);

        if (!tep_db_num_rows($categories_search_result)) {
            $categories_search_sql = "	SELECT DISTINCT categories_id
                                        FROM " . TABLE_CATEGORIES_SEARCH . "
                                        WHERE search_value LIKE '%" . tep_db_input($keyword) . "%'";
            $categories_search_result = tep_db_query($categories_search_sql);
        }

        while ($categories_search_row = tep_db_fetch_array($categories_search_result)) {
            if (!isset($id_array[$categories_search_row['categories_id']])) {
                if (in_array($categories_search_row['categories_id'], $zone_info_array[1]->zone_categories_id)) {
                    $record_count += 1;
                    $id_array[$categories_search_row['categories_id']] = 1;
                    $games_array[] = array('id' => $categories_search_row['categories_id'], 'name' => tep_get_categories_name($categories_search_row['categories_id'], $lang_id));

                    if ($record_count >= $max) {
                        break;
                    }
                }
            }
        }
    }
}
echo json_encode($games_array);
?>