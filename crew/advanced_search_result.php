<?
/*
  	$Id: advanced_search_result.php,v 1.14 2011/05/14 10:25:13 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/
require('includes/application_top.php');

// This page no longer in use
tep_redirect(tep_href_link(FILENAME_DEFAULT, '', 'SSL'));
exit;

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_ADVANCED_SEARCH);
// Added for custom product
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CUSTOM_PRODUCT_INFO);

if (!tep_session_is_registered('customer_id')) 	$navigation->set_snapshot();

$error = false;

$action = (isset($_REQUEST['do_action']) ? $_REQUEST['do_action'] : '');
$keywords = (isset($_REQUEST['keywords']) ? trim(tep_db_prepare_input($_REQUEST['keywords'])) : '');

switch($action) {
	case 'search_result':		
		if (tep_not_null($keywords) && $keywords != TEXT_SEARCH_WHOLE_STORE) {
			tep_record_search_key($keywords, $categories_id, $customer_id);
		}
		
	    tep_redirect(tep_href_link(FILENAME_ADVANCED_SEARCH_RESULT, tep_get_all_get_params(array('do_action')), 'NONSSL', false));
	    
		break;
	default:
		if (tep_not_null($keywords) && strlen($keywords) > 2) {
	    	if (!tep_parse_search_string($keywords, $search_keywords)) {
	        	$error = true;
				
				$messageStack->add_session('search', ERROR_INVALID_KEYWORDS);
	      	}
	    } else {
	    	$error = true;
    		$messageStack->add_session('search', ERROR_AT_LEAST_ONE_INPUT);
	    }
		
		break;
}

if (strlen($keywords) <= 2 || ($error == true && strtolower($_SERVER['REQUEST_METHOD']) != 'get' || !tep_not_null(trim($HTTP_GET_VARS['keywords'])))) {
	tep_redirect(tep_href_link(FILENAME_ADVANCED_SEARCH, tep_get_all_get_params(), 'NONSSL', true, false));
}

if (file_exists('checkout_xmlhttp.js.php')) { include_once ('checkout_xmlhttp.js.php'); }

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_ADVANCED_SEARCH));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_ADVANCED_SEARCH_RESULT, tep_get_all_get_params(), 'NONSSL', true, false));

$content = CONTENT_ADVANCED_SEARCH_RESULT;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>