<?php

require_once('includes/application_top.php');

require_once(DIR_WS_MODULES . 'payment/adyen/classes/adyenIpnClass.php');
include(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

if (isset($_POST) && count($_POST)) {
    include_once(DIR_WS_CLASSES . 'order.php');
    include_once(DIR_WS_CLASSES . 'anti_fraud.php');
    include_once(DIR_WS_CLASSES . 'log.php');
	include_once(DIR_WS_CLASSES . 'pgs.php');
	
    $payment = 'adyen';
	$reCapture = false;
	echo '[accepted]'; //reply to adyen IPN
        
	if ($_POST['eventCode'] == 'REPORT_AVAILABLE' && $_POST['success'] == 'true' && $_POST['live'] == 'true') {
		if (preg_match("/\bsettlement\w/i", $_POST['pspReference'])) {
			$settlement_report_email_content = 'Filename: ' . $_POST['pspReference'] . '<br>Event Date:' . $_POST['eventDate'] . '<br>Merchant Account:' . $_POST['merchantAccountCode'] . '<br>Download URL:' . $_POST['reason'];
			mail('<EMAIL>', '[OffGamers] Adyen Report ready to Download : ' . $_POST['merchantAccountCode'], $settlement_report_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		}
		exit;
	} else if ($_POST['eventCode'] == 'NOTIFICATION_OF_CHARGEBACK' || $_POST['eventCode'] == 'CHARGEBACK_REVERSED' || $_POST['eventCode'] == 'REQUEST_FOR_INFORMATION') {
		$cb_email_content = 'Order ID: ' . $_POST['merchantReference'] . '<br>PSP Reference:' . $_POST['pspReference'] . '<br>Event Type:' . $_POST['eventCode'] . '<br>Reason:' . $_POST['reason'];
		mail('<EMAIL>', '[OffGamers] Adyen Dispute/Chargeback Notification', $cb_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		exit;
	} else if ($_POST['eventCode'] == 'NOTIFICATION_OF_FRAUD') {
		$cb_email_content = 'Order ID: ' . $_POST['merchantReference'] . '<br>PSP Reference:' . $_POST['pspReference'] . '<br>Event Type:' . $_POST['eventCode'] . '<br>Reason:' . $_POST['reason'];
		mail('<EMAIL>', '[OffGamers] Adyen Fraud Notification', $cb_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		exit;
	} else if ($_POST['eventCode'] == 'CAPTURE' && $_POST['success'] == 'false') {
//		$reCapture = true;
		$email_content = 'Order ID: ' . $_POST['merchantReference'] . '<br>PSP Reference:' . $_POST['pspReference'] . '<br>Event Type:' . $_POST['eventCode'] . '<br>Reason:' . $_POST['reason'];
		mail('<EMAIL>', '[OffGamers] Adyen Capture Failure', $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
	if (pgs::getOrderSiteId($_POST['merchantReference']) == '5') {
		if (isset($_SERVER['PHP_AUTH_USER']) && isset($_SERVER['PHP_AUTH_PW'])) {
			$_POST['signature'] = base64_encode(pack("H*",hash_hmac('sha1',$_SERVER['PHP_AUTH_USER'].$_POST['merchantReference'].$_POST['eventCode'].$_POST['pspReference'],$_SERVER['PHP_AUTH_PW'])));
		}
		pgs::repostToPGS('adyen', $_POST);
	} else {
		include_once(DIR_WS_CLASSES . 'payment.php');

		$adyenIpn = new adyenIpnClass($_POST);
		$orders_id = $adyenIpn->get_order_id();
		$order = new order($orders_id);
		$payment_modules = new payment($payment);
		$$payment = new $payment($order->info['payment_methods_id']);
		$$payment->get_merchant_account($adyenIpn->key['currency']);
		$addresses = array();
		$ip_range = explode(',', $$payment->ip_list);
		foreach ($ip_range as $range) {
			@list($ip, $len) = explode('/', $range);
			if (($min = ip2long($ip)) !== false) {
				$max = ($min | (1 << (32 - $len)) - 1);
				for ($i = $min; $i < $max; $i++)
					$addresses[] = long2ip($i);
			}
		}
		$ip_address = tep_get_ip_address();
		if (in_array($ip_address, $addresses)) {
			if (isset($_SERVER['PHP_AUTH_USER']) && $_SERVER['PHP_AUTH_USER'] == $$payment->http_user
					&& isset($_SERVER['PHP_AUTH_PW']) && $_SERVER['PHP_AUTH_PW'] == $$payment->http_password) {
				if ($reCapture) {
					$$payment->post_capture($_POST['merchantReference'], 'system');
					exit;
				}
				if ($$payment->code == $adyenIpn->key['paymentMethod'] && ($adyenIpn->key['live'] == 'true' || $$payment->test_mode == 'True')) {
					$log_object = new log_files('system');
					if ((int) $orders_id > 0) {
						if ($adyenIpn->key['eventCode'] == 'AUTHORISATION') {
							$adyen_data_array = array(
								'adyen_event_code' => $adyenIpn->key['eventCode'],
								'adyen_psp_reference' => $adyenIpn->key['pspReference'],
								'adyen_original_reference' => $adyenIpn->key['originalReference'],
								'adyen_merchant_account_code' => $adyenIpn->key['merchantAccountCode'],
								'adyen_event_date' => $adyenIpn->key['eventDate'],
								'adyen_success' => $adyenIpn->key['success'],
								'adyen_payment_method' => $adyenIpn->key['paymentMethod'],
								'adyen_operations' => (isset($adyenIpn->key['operations']) && !empty($adyenIpn->key['operations'])) ? $adyenIpn->key['operations'] : '',
								'adyen_reason' => (isset($adyenIpn->key['reason']) && !empty($adyenIpn->key['reason'])) ? $adyenIpn->key['reason'] : '',
								'adyen_currency' => $adyenIpn->key['currency'],
								'adyen_amount' => $adyenIpn->key['value'],
								'adyen_cc_cvc_result' => (isset($adyenIpn->key['additionalData_cvcResult']) && !empty($adyenIpn->key['additionalData_cvcResult'])) ? $adyenIpn->key['additionalData_cvcResult'] : '',
								'adyen_cc_expiry_date' => (isset($adyenIpn->key['additionalData_expiryDate']) && !empty($adyenIpn->key['additionalData_expiryDate'])) ? $adyenIpn->key['additionalData_expiryDate'] : '',
								'adyen_cc_card_bin' => (isset($adyenIpn->key['additionalData_cardBin']) && !empty($adyenIpn->key['additionalData_cardBin'])) ? $adyenIpn->key['additionalData_cardBin'] : '',
								'adyen_cc_card_summary' => (isset($adyenIpn->key['additionalData_cardSummary']) && !empty($adyenIpn->key['additionalData_cardSummary'])) ? $adyenIpn->key['additionalData_cardSummary'] : '',
								'adyen_cc_auth_code' => (isset($adyenIpn->key['additionalData_authCode']) && !empty($adyenIpn->key['additionalData_authCode'])) ? $adyenIpn->key['additionalData_authCode'] : '',
								'adyen_cc_fraud_score' => (isset($adyenIpn->key['additionalData_totalFraudScore']) ? $adyenIpn->key['additionalData_totalFraudScore'] : ''),
								'adyen_cc_extra_cost' => (isset($adyenIpn->key['additionalData_extraCostsValue']) ? $adyenIpn->key['additionalData_extraCostsValue'] : ''),
								'adyen_cc_three_d_auth' => (isset($adyenIpn->key['additionalData_threeDAuthenticated']) ? $adyenIpn->key['additionalData_threeDAuthenticated'] : ''),
								'adyen_cc_three_d_auth_offer' => (isset($adyenIpn->key['additionalData_threeDOffered']) ? $adyenIpn->key['additionalData_threeDOffered'] : ''),
								'adyen_cc_avs_result' => (isset($adyenIpn->key['additionalData_avsResult']) ? $adyenIpn->key['additionalData_avsResult'] : ''),
								'adyen_data' => json_encode($adyenIpn->key)
							);
						} else if ($adyenIpn->key['eventCode'] == 'CAPTURE') {
							$adyen_data_array = array(
								'adyen_event_code' => $adyenIpn->key['eventCode'],
								'adyen_psp_reference' => $adyenIpn->key['pspReference'],
								'adyen_original_reference' => $adyenIpn->key['originalReference'],
								'adyen_merchant_account_code' => $adyenIpn->key['merchantAccountCode'],
								'adyen_event_date' => $adyenIpn->key['eventDate'],
								'adyen_success' => $adyenIpn->key['success'],
								'adyen_payment_method' => $adyenIpn->key['paymentMethod'],
								'adyen_operations' => $adyenIpn->key['operations'],
								'adyen_currency' => $adyenIpn->key['currency'],
								'adyen_amount' => $adyenIpn->key['value']
							);
						}
						
						if (isset($adyen_data_array) && !empty($adyen_data_array)) {
							$adyen_payment_select_sql = "	SELECT adyen_order_id
															FROM " . TABLE_ADYEN . "
															WHERE adyen_order_id = '" . (int) $orders_id . "'";
							$adyen_payment_result_sql = tep_db_query($adyen_payment_select_sql);
							if (tep_db_num_rows($adyen_payment_result_sql)) {
								tep_db_perform(TABLE_ADYEN, $adyen_data_array, 'update', " adyen_order_id = '" . (int) $orders_id . "' ");
							} else {
								$adyen_data_array['adyen_order_id'] = $orders_id;
								tep_db_perform(TABLE_ADYEN, $adyen_data_array);
							}
						}
						
						$adyen_payment_history_data_array = array(	'adyen_orders_id' => $adyenIpn->get_order_id(),
																	'adyen_event_code' => $adyenIpn->key['eventCode'],
																	'adyen_date' => 'now()',
																	'adyen_reason' => $adyenIpn->key['reason'],
																	'changed_by' => 'system'
																	);
						tep_db_perform(TABLE_ADYEN_STATUS_HISTORY, $adyen_payment_history_data_array);
					}

					if ($adyenIpn->key['success'] == 'true') {
//						if ($adyenIpn->key['eventCode'] == 'AUTHORISATION' || $adyenIpn->key['eventCode'] == 'CAPTURE') { 
						if ($adyenIpn->key['eventCode'] == 'AUTHORISATION') { 
							if ($adyenIpn->validate_receiver_account($$payment)) {
								if (tep_not_null($orders_id)) {
									if (!is_object($order))
										$order = new order($orders_id);
									if ($order->info['orders_status_id'] == $$payment->order_status) { // If the order still in initial status
										if($adyenIpn->authenticate($$payment, $order)) {
											$adyenIpn->process_this_order($orders_id, $$payment, $$payment->order_processing_status);
										}
									}
								}
							}
						} 
					}
				} else {
					$adyen_payment_history_data_array = array(	'adyen_orders_id' => $adyenIpn->get_order_id(),
																'adyen_event_code' => 'ERROR',
																'adyen_date' => 'now()',
																'adyen_reason' =>'PG Mismatched - ' . $$payment->code .' != '. $adyenIpn->key['paymentMethod'],
																'changed_by' => 'system'
																);
					tep_db_perform(TABLE_ADYEN_STATUS_HISTORY, $adyen_payment_history_data_array);
				}
			} else {
				mail('<EMAIL>', (isset($orders_id) && (int) $orders_id > 0 ? (int) $orders_id . ' - ' : '') . 'Adyen Invalid IPN HTTP AUTHENTICATION', print_r($_SERVER,1), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			}
		} else {
			mail('<EMAIL>', (isset($orders_id) && (int) $orders_id > 0 ? (int) $orders_id . ' - ' : '') . 'Adyen Invalid IPN IP Address', $ip_address, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
		}
	}
}
?>