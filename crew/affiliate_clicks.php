<?
/*
	$Id: affiliate_clicks.php,v 1.5 2005/04/14 09:31:23 weichen Exp $
  	
  	OSC-Affiliate
  	Contribution based on:
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
  	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

if (!tep_session_is_registered('affiliate_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_AFFILIATE, '', 'SSL'));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_AFFILIATE_CLICKS);

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_AFFILIATE_CLICKS, '', 'SSL'));

$affiliate_clickthroughs_raw = " select a.*, pd.products_name from " . TABLE_AFFILIATE_CLICKTHROUGHS . " a 
    							left join " . TABLE_PRODUCTS . " p on (p.products_id = a.affiliate_products_id) 
    							left join " . TABLE_PRODUCTS_DESCRIPTION . " pd on (pd.products_id = p.products_id and pd.language_id = '" . $languages_id . "') 
    							where a.affiliate_id = '" . $affiliate_id . "'  ORDER BY a.affiliate_clientdate desc
    							";

$affiliate_clickthroughs_split = new splitPageResults($affiliate_clickthroughs_raw, MAX_DISPLAY_SEARCH_RESULTS);

$content = CONTENT_AFFILIATE_CLICKS; 

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php'); 
?>