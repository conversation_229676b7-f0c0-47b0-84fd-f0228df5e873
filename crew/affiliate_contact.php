<?
/*
	$Id: affiliate_contact.php,v 1.3 2005/04/14 09:31:23 weichen Exp $
	
	OSC-Affiliate
  	Contribution based on:
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

if (!tep_session_is_registered('affiliate_id')) {
	$navigation->set_snapshot();
	tep_redirect(tep_href_link(FILENAME_AFFILIATE, '', 'SSL'));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_AFFILIATE_CONTACT);

$error = false;

if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'send')) {
	if (!tep_not_null($HTTP_POST_VARS['name'])) {
		$messageStack->add('affiliate_contact', TEXT_ERROR_EMPTY_NAME);
		$error = true;
	}
	
	if (!tep_not_null($HTTP_POST_VARS['enquiry'])) {
		$messageStack->add('affiliate_contact', TEXT_ERROR_EMPTY_ENQUIRY);
		$error = true;
	}
	
	if (!tep_validate_email(trim($HTTP_POST_VARS['email']))) {
		$messageStack->add('affiliate_contact', ENTRY_EMAIL_ADDRESS_CHECK_ERROR);
		$error = true;
	}
	
	if (!$error) {
		tep_mail(STORE_OWNER, AFFILIATE_EMAIL_ADDRESS, EMAIL_SUBJECT, $HTTP_POST_VARS['enquiry'], $HTTP_POST_VARS['name'], $HTTP_POST_VARS['email']);
	   	tep_redirect(tep_href_link(FILENAME_AFFILIATE_CONTACT, 'action=success'));
	}
}

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_AFFILIATE_CONTACT));

$affiliate_values = tep_db_query("SELECT affiliate_firstname, affiliate_lastname, affiliate_email_address FROM " . TABLE_AFFILIATE . " WHERE affiliate_id = '" . $affiliate_id . "'");
$affiliate = tep_db_fetch_array($affiliate_values);

$content = CONTENT_AFFILIATE_CONTACT; 

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php'); 
?>