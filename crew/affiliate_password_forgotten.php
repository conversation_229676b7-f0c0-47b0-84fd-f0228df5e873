<?
/*
  	$Id: affiliate_password_forgotten.php,v 1.3 2005/04/14 09:32:06 weichen Exp $
	
  	OSC-Affiliate
  	Contribution based on:
  	osCommerce, Open Source E-Commerce Solutions
 	http://www.oscommerce.com
	
  	Copyright (c) 2002 -2003 osCommerce
  	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_AFFILIATE_PASSWORD_FORGOTTEN);

if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'process')) {
	$email_address = tep_db_prepare_input($HTTP_POST_VARS['email_address']);
	$check_affiliate_query = tep_db_query("SELECT affiliate_firstname, affiliate_lastname, affiliate_password, affiliate_id FROM " . TABLE_AFFILIATE . " WHERE affiliate_email_address = '" . tep_db_input($email_address) . "'");
	
    if (tep_db_num_rows($check_affiliate_query)) {
      	$check_affiliate = tep_db_fetch_array($check_affiliate_query);
      	// Crypted password mods - create a new password, update the database and mail it to them
      	$newpass = tep_create_random_value(ENTRY_PASSWORD_MIN_LENGTH);
      	$crypted_password = tep_encrypt_password($newpass);
      	tep_db_query("UPDATE " . TABLE_AFFILIATE . " SET affiliate_password = '" . tep_db_input($crypted_password) . "' WHERE affiliate_id = '" . (int)$check_affiliate['affiliate_id'] . "'");
      	
      	$email_body = EMAIL_PASSWORD_REMINDER_BODY . "\n\n" . EMAIL_FOOTER;
      	tep_mail($check_affiliate['affiliate_firstname'] . " " . $check_affiliate['affiliate_lastname'], $email_address, EMAIL_PASSWORD_REMINDER_SUBJECT, nl2br(sprintf($email_body, $newpass)), STORE_OWNER, AFFILIATE_EMAIL_ADDRESS);
      	$messageStack->add_session('affiliate_login', TEXT_PASSWORD_SENT, 'success');
      	tep_redirect(tep_href_link(FILENAME_AFFILIATE, '', 'SSL'));
    } else {
    	$messageStack->add('password_forgotten', TEXT_NO_EMAIL_ADDRESS_FOUND);
    }
}

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_AFFILIATE, '', 'SSL'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link(FILENAME_AFFILIATE_PASSWORD_FORGOTTEN, '', 'SSL'));

$content = CONTENT_AFFILIATE_PASSWORD_FORGOTTEN;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>