<?
/*
	$Id: affiliate_payment.php,v 1.3 2005/04/14 09:32:23 weichen Exp $
  	
  	OSC-Affiliate
  	Contribution based on:
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
	Released under the GNU General Public License
*/

require('includes/application_top.php');

if (!tep_session_is_registered('affiliate_id')) {
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_AFFILIATE, '', 'SSL'));
}

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_AFFILIATE_PAYMENT);

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_AFFILIATE_PAYMENT, '', 'SSL'));

$affiliate_payment_raw = "	select p.* , s.affiliate_payment_status_name, acc.affiliate_paid_amount, acc.affiliate_transaction_date from " . 
							TABLE_AFFILIATE_PAYMENT . " as p 
							inner join " . TABLE_AFFILIATE_PAYMENT_STATUS . " as s 
								on p.affiliate_payment_status = s.affiliate_payment_status_id 
							left join " . TABLE_AFFILIATE_ACCOUNT . " as acc 
								on p.affiliate_payment_id=acc.affiliate_payment_id 
           					where s.affiliate_language_id = '" . $languages_id . "' 
           						and p.affiliate_id ='" . $affiliate_id . "' 
           					order by p.affiliate_payment_date DESC ";

$affiliate_payment_split = new splitPageResults($affiliate_payment_raw, MAX_DISPLAY_SEARCH_RESULTS);

$content = CONTENT_AFFILIATE_PAYMENT; 

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php'); 
?>