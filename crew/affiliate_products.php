<?
  require('includes/application_top.php');
?>
<!doctype html public "-//W3C//DTD HTML 4.01 Transitional//EN">
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<title><?php echo TITLE; ?></title>
<link rel="stylesheet" type="text/css" href="stylesheet.css">
<script language="javascript" src="includes/general.js"></script>
</head>
<body marginwidth="0" marginheight="0" topmargin="0" bottommargin="0" leftmargin="0" rightmargin="0" bgcolor="#FFFFFF">

<!-- body //-->
<table border="0" width="100%" cellspacing="2" cellpadding="2">
<tr>
    <td width="100%" valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
<!-- body_text //--> 
    <?php


?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
  <tr>
    <td class="productSmallText"><u>Product ID</u></td>
    <td class="productSmallText"><u>Product Name</u></td>
  </tr>
<?php

$result_query = tep_db_query("SELECT * FROM `products_description` ORDER BY products_id ASC");
while ($row = tep_db_fetch_array($result_query)) {
	
    ?>
    <tr>
      <td class="productSmallText"><?php echo $row['products_id']; ?></td>
      <td class="productSmallText"><?php echo $row['products_name']; ?></td>
    </tr>
    <?php
}
?>

</table>   
<!-- body_text_eof //-->
  </td></tr>
</table>
<p>
 <center>
  		<a href="javascript:window.close();">Close Windows</a>
</center>
</body>
</html>
