<?
/*
  	$Id: affiliate_show_banner.php,v 1.7 2010/03/16 08:56:18 boonhock Exp $
  	
  	OSC-Affiliate
  	Contribution based on:
	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2002 - 2003 osCommerce
	
  	Released under the GNU General Public License
*/

// CHECKIT
// -> optimize code -> double parts

// require of application_top not possible 
// cause then whois online registers it also as visitor
//

define('TABLE_AFFILIATE_BANNERS_HISTORY', 'affiliate_banners_history');
define('TABLE_AFFILIATE_BANNERS', 'affiliate_banners');
define('TABLE_PRODUCTS', 'products');

// Set the local configuration parameters - mainly for developers
if (file_exists('includes/local/configure.php')) include('includes/local/configure.php');
require('includes/configure.php');
if (file_exists('includes/local/affiliate_configure.php')) include('includes/local/affiliate_configure.php');
require('includes/affiliate_configure.php');

// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');
// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

function affiliate_show_banner($pic) {
	//Read Pic and send it to browser
    $fp = fopen($pic, "rb");
    if (!$fp) exit();
    
    $img_path_parts = pathinfo($pic);
	$img_type = $path_parts["extension"];	// Get Image type
    //$img_type = substr($pic, strrpos($pic, ".") + 1);
	$img_name = $path_parts["basename"];	// Get Imagename
    
    header ("Content-type: image/$img_type");
    header ("Content-Disposition: inline; filename=$img_name");
    fpassthru($fp);
    // The file is closed when fpassthru() is done reading it (leaving handle useless).  
    // fclose ($fp);
    exit();
}

function affiliate_debug($banner,$sql)
{
?>
	<table border=1 cellpadding=2 cellspacing=2>
    	<tr>
    		<td colspan=2>Check the pathes! (catalog/includes/configure.php)</td>
    	</tr>
      	<tr>
      		<td>absolute path to picture:</td>
      		<td><? echo DIR_FS_IMAGES . $banner; ?></td>
      	</tr>
      	<tr>
      		<td>build with:</td>
      		<td>DIR_WS_IMAGES . $banner</td>
      	</tr>
      	<tr>
      		<td>DIR_FS_DOCUMENT_ROOT</td>
      		<td><?=DIR_FS_DOCUMENT_ROOT?></td>
      	</tr>
      	<tr>
      		<td>DIR_WS_CATALOG</td>
      		<td><?=DIR_WS_CATALOG?></td>
      	</tr>
      	<tr>
      		<td>DIR_WS_IMAGES</td>
      		<td><?=DIR_WS_IMAGES?></td>
      	</tr>
      	<tr>
      		<td>$banner</td>
      		<td><?=$banner?></td>
      	</tr>
      	<tr>
      		<td>SQL-Query used:</td>
      		<td><?=$sql?></td>
      	</tr>
      	<tr>
      		<th>Try to find error:</td><td>&nbsp;</th>
      	</tr>
      	<tr>
      		<td>SQL-Query:</td>
      		<td><? if ($banner) echo "Got Result"; else echo "No result"; ?></td>
      	</tr>
      	<tr>
      		<td>Locating Pic</td><td>
<? 
	$pic = DIR_FS_IMAGES . $banner;
    echo $pic . "<br>";
    if (!is_file($pic)) {
    	echo "failed<br>";
    } else {
      	echo "success<br>";
    }
?>
			</td>
		</tr>
	</table>
<?
	exit();
}

// Register needed Post / Get Variables
if ($_GET['ref']) $affiliate_id=$_GET['ref'];
if ($_POST['ref']) $affiliate_id=$_POST['ref'];

if ($_GET['affiliate_banner_id']) $banner_id = $_GET['affiliate_banner_id'];
if ($_POST['affiliate_banner_id']) $banner_id = $_POST['affiliate_banner_id'];
if ($_GET['affiliate_pbanner_id']) $prod_banner_id = $_GET['affiliate_pbanner_id'];
if ($_POST['affiliate_pbanner_id']) $prod_banner_id = $_POST['affiliate_pbanner_id'];

$banner = '';
$products_id = '';

if ($banner_id) {
    $banner_select_sql = "SELECT affiliate_banners_image, affiliate_products_id FROM " . TABLE_AFFILIATE_BANNERS . " WHERE affiliate_banners_id = '" . $banner_id  . "' AND affiliate_status = 1";
    $banner_result_sql = tep_db_query($banner_select_sql);
    if ($banner_row = tep_db_fetch_array($banner_result_sql)) {
      	$banner = $banner_row['affiliate_banners_image'];
      	$products_id = $banner_row['affiliate_products_id']; 
    }
}

if ($prod_banner_id) {
    $banner_id = 1; // Banner ID for these Banners is one
	
    $product_banner_select_sql = "	SELECT products_image 
    								FROM " . TABLE_PRODUCTS_DESCRIPTION . " 
    								WHERE products_id = '" . $prod_banner_id  . "' 
    									AND products_status = 1
    									AND language_id=1";
    $product_banner_result_sql = tep_db_query($product_banner_select_sql);
    if ($product_banner_row = tep_db_fetch_array($product_banner_result_sql)) {
      	$banner = $product_banner_row['products_image'];
      	$products_id = $prod_banner_id;
    }
}

// DebugModus
if (AFFILIATE_SHOW_BANNERS_DEBUG == 'true') affiliate_debug($banner,$sql);

$update_show_history = true;
$http_length = strpos(HTTP_SERVER, "//") + 2;
if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], $_SERVER['HTTP_HOST']) == $http_length) {
	$update_show_history = false;
}

if ($banner) {
	$pic = DIR_FS_IMAGES . $banner;
	
    // Show Banner only if it exists:
    if (is_file($pic)) {
      	$today = date('Y-m-d');
    	// Update stats:
      	if ($affiliate_id && $update_show_history) {
        	$banner_stats_query = tep_db_query("SELECT * FROM " . TABLE_AFFILIATE_BANNERS_HISTORY . " WHERE affiliate_banners_id = '" . $banner_id  . "' AND affiliate_banners_products_id = '" . $products_id ."' AND affiliate_banners_affiliate_id = '" . $affiliate_id. "' AND affiliate_banners_history_date = '" . $today . "'");
    		// Banner has been shown today 
        	if ($banner_stats_array = tep_db_fetch_array($banner_stats_query)) {
          		tep_db_query("UPDATE " . TABLE_AFFILIATE_BANNERS_HISTORY . " SET affiliate_banners_shown = affiliate_banners_shown + 1 WHERE affiliate_banners_id = '" . $banner_id  . "' AND affiliate_banners_affiliate_id = '" . $affiliate_id. "' AND affiliate_banners_products_id = '" . $products_id ."' AND affiliate_banners_history_date = '" . $today . "'");
        	} else { // First view of Banner today
          		tep_db_query("INSERT INTO " . TABLE_AFFILIATE_BANNERS_HISTORY . " (affiliate_banners_id, affiliate_banners_products_id, affiliate_banners_affiliate_id, affiliate_banners_shown, affiliate_banners_history_date) VALUES ('" . $banner_id  . "', '" .  $products_id ."', '" . $affiliate_id. "', '1', '" . $today . "')");
        	}
      	}
    	// Show Banner
      	affiliate_show_banner($pic);
	}
}

// Show default Banner if none is found
if (is_file(AFFILIATE_SHOW_BANNERS_DEFAULT_PIC)) {
	affiliate_show_banner(AFFILIATE_SHOW_BANNERS_DEFAULT_PIC);
} else {
    echo "<br>"; // Output something to prevent endless loading
}
exit();
?>