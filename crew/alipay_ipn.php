<?
/*
  	$Id: alipay_ipn.php, v1.0 2005/11/29 18:08:05
  	Author : <PERSON> (<EMAIL>)
  	Title: alipay.com Payment Module V1.0
	
	Revisions:
		Version 1.0 Initial Payment Module
	
  	Copyright (c) 2007 SKC Venture
	
  	Released under the GNU General Public License
*/

require_once('includes/application_top.php');
include_once(DIR_WS_CLASSES . 'payment.php');
include_once(DIR_WS_CLASSES . 'order.php');
include_once(DIR_WS_CLASSES . 'log.php');

$success_flag = "success";
$orders_id = '';

$trace_log = '--1--';

if (isset($_REQUEST) && count($_REQUEST) && isset($_REQUEST['notify_type'])) {
	$trace_log .= '--2--';
	if ($_REQUEST['notify_type'] == 'batch_refund_notify') {
		if (isset($_REQUEST['batch_no']) && tep_not_null($_REQUEST['batch_no'])) {
			$trace_log .= '--3--';
			$trade_no = '';
			$refund_amt = '';
			$status_flag = '';
			
			if (isset($_REQUEST['refund_status'])) {
				$trace_log .= '--4--';
				$trade_no = (int)(isset($_REQUEST['trade_no'])?$_REQUEST['trade_no']:'');
				$refund_amt = (double)(isset($_REQUEST['total_fee'])?$_REQUEST['total_fee']:'');
				$status_flag = (isset($_REQUEST['refund_status'])?$_REQUEST['refund_status']:'');
			} else {
				$trace_log .= '--4a--';
				if ((isset($_REQUEST['result_details']) && tep_not_null($_REQUEST['result_details']) && isset($_REQUEST['notify_id']) && tep_not_null($_REQUEST['notify_id']))) {
					$trace_log .= '--5--';
					$result_details_array = explode("^", $_REQUEST['result_details']);
					if (count($result_details_array)>2) {
						$trace_log .= '--6--';
						$trade_no = (int)(isset($result_details_array[0])?$result_details_array[0]:'');
						$refund_amt = (double)(isset($result_details_array[1])?$result_details_array[1]:'');
						$status_flag = (isset($result_details_array[2])?$result_details_array[2]:'');					
					}
				}
			}
			
			if ((int)$trade_no > 0) {
				$trace_log .= '--7--';
				$store_refund_select_sql = "SELECT sr.store_refund_id, sr.store_refund_trans_id 
											FROM " . TABLE_ALIPAY . " AS a
											LEFT JOIN " . TABLE_STORE_REFUND . " as sr
												ON a.alipay_orders_id = sr.store_refund_trans_id 
											WHERE alipay_trade_no = '".tep_db_input($trade_no)."'
												AND sr.store_refund_payments_reference = '".tep_db_input($_REQUEST['batch_no'])."'
												AND sr.store_refund_is_processed='1'";
				$store_refund_result_sql = tep_db_query($store_refund_select_sql);
				if ($store_refund_row = tep_db_fetch_array($store_refund_result_sql)) {
					$orders_id = (int)$store_refund_row['store_refund_trans_id'];
					$trace_log .= '--8--';
					$store_refund_id = (int)$store_refund_row['store_refund_id'];
					
					require_once(DIR_WS_MODULES . 'payment/alipay.php');
					
					$order = new order($orders_id);
					$alipay_obj = new alipay($order->info['payment_methods_id']);
					
					$alipayCurrencies = $order->info['currency'];
			      	if (!in_array($alipayCurrencies, $alipay_obj->alipayCurrencies)) {
			        	$alipayCurrencies = $alipay_obj->alipayCurrencies[0];
			      	}
					
					$alipay_obj->get_merchant_account($alipayCurrencies);
					
			      	require_once(DIR_WS_MODULES . 'payment/alipay/alipay_notify.php');
			      	
			      	$alipay = new alipay_notify($alipay_obj->partner_id, $alipay_obj->security_code, $alipay_obj->sign_type, $alipay_obj->input_charset, 'https');
					$verify_result = $alipay->notify_verify();
					
					if ($verify_result) {
						$trace_log .= '--9--';
						//if (isset($_REQUEST['success_num']) && (int)$_REQUEST['success_num']==1) { //[success_num] => 1 is because we only 
		          			// Insert refund payment history
		          			$refund_history_sql_data_array = array(	'store_refund_id' => $store_refund_id,
					    	 	          							'date_added' => 'now()',
					    	 	          							'payee_notified' => '1',
					    	 	          							'changed_by' => 'system',
					    	 	          							'changed_by_role' => 'system'
				        	    	       							);
		          			
		          			switch ($status_flag) {
		          				case 'SUCCESS':
		          				case 'REFUND_SUCCESS':
								case 'TRADE_SUCCESS':
								case 'TRADE_FINISHED':
								$trace_log .= '-10-';
									$refund_update_sql_data_array = array(	'store_refund_status' => 3,
																			'store_refund_is_processed' => 0,
																			'store_refund_last_modified' => 'now()' );
									tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($store_refund_id) . "'");
									
									$comments = "Alipay Refund Receive<BR>";
									$comments .= "Notify ID : " . (isset($_REQUEST['notify_id'])?$_REQUEST['notify_id']:'n/a') . "<BR>";
									$comments .= "Trade No.: " . (tep_not_null($trade_no)?$trade_no:'n/a') . "<BR>";
									$comments .= "Refund Amount: " . (tep_not_null($refund_amt)? $refund_amt :'n/a') . "<BR>";
									$comments .= "Status: " . (tep_not_null($status_flag)?$status_flag:'n/a');
									
			  						$refund_history_sql_data_array['store_refund_status'] = 3;
									$refund_history_sql_data_array['comments'] = $comments;
									break;
								default:
									$trace_log .= '-11-';
									$refund_update_sql_data_array = array(	'store_refund_is_processed' => 0,
																			'store_refund_last_modified' => 'now()' );
									tep_db_perform(TABLE_STORE_REFUND, $refund_update_sql_data_array, 'update', "store_refund_id = '" . tep_db_input($store_refund_id) . "'");
									
									$comments = "Alipay Refund Receive - failed<BR>";
									$comments .= "Notify ID : " . (isset($_REQUEST['notify_id'])?$_REQUEST['notify_id']:'n/a') . "<BR>";
									$comments .= "Trade No.: " . (tep_not_null($trade_no)?$trade_no:'n/a') . "<BR>";
									$comments .= "Refund Amount: " . (tep_not_null($refund_amt)?$refund_amt:'n/a') . "<BR>";
									$comments .= "Status: " . (tep_not_null($status_flag)?$status_flag:'n/a');
									
									$refund_history_sql_data_array['comments'] = $comments;
									break;
							}
							tep_db_perform(TABLE_STORE_REFUND_HISTORY, $refund_history_sql_data_array);
							$success_flag = 'success';
						//}
					}
				}
			}
		}
	} else if (isset($_REQUEST['refund_status'])) {
		$success_flag = "success";
	} else {
		$trace_log .= '-12-';
		include_once(DIR_WS_MODULES . 'payment/alipay/classes/alipay_ipn_class.php');
		include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);
		include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	
		/**********************************************************
			(1) Check this order is pay to us
		**********************************************************/
		$payment = 'alipay';
	    $payment_modules = new payment($payment);
	    
	    $alipay_ipn = new alipay_ipn($_REQUEST);
	    $orders_id = $alipay_ipn->get_order_id();
	    $log_object = new log_files('system');
		
		if (tep_not_null($orders_id)) {
			$trace_log .= '-13-';
			$order = new order($orders_id);
			$$payment = new $payment($order->info['payment_methods_id']);
			
			$alipayCurrencies = $order->info['currency'];
	      	if (!in_array($alipayCurrencies, $$payment->alipayCurrencies)) {
	        	$alipayCurrencies = $$payment->defCurr;
	      	}
			
			$$payment->get_merchant_account($alipayCurrencies);
			if ($alipay_ipn->validate_receiver_account($$payment->seller_email)) {
				$trace_log .= '-14-';
				if ($alipay_ipn->validate_transaction_data($$payment)) {	// To ensure the integrity of the data posted back to merchant's server
					$trace_log .= '-15-';
					if ($order->info['orders_status_id'] == $$payment->order_status) {	// If the order still in initial status
						$trace_log .= '-16-';
						$payment_success = $alipay_ipn->authenticate($order, $orders_id, $$payment);
						if ($payment_success) {
							$trace_log .= '-17-';
							$success_flag = "success";
						}
					}
				}
			}
			
			$alipay_data_array = array(	'alipay_notify_type' => $_REQUEST['notify_type'],
								  		'alipay_notify_id' => $_REQUEST['notify_id'],
										'alipay_notify_time' => $_REQUEST['notify_time'], 
										'alipay_sign' => $_REQUEST['sign'], 
										'alipay_sign_type' => $_REQUEST['sign_type'], 
										'alipay_trade_no' => $_REQUEST['trade_no'], 
										'alipay_payment_type' => $_REQUEST['payment_type'], 
										'alipay_total_fee' => $_REQUEST['total_fee'], 
										'alipay_currency' => 'CNY', 
										'alipay_trade_status' => $_REQUEST['trade_status'], 
										'alipay_buyer_id' => $_REQUEST['buyer_id'],
										'alipay_buyer_email' => $_REQUEST['buyer_email']
			                      		);
			$check_alipay_select_sql = "SELECT alipay_orders_id
										FROM " . TABLE_ALIPAY . "
										WHERE alipay_orders_id = '".$_REQUEST['out_trade_no']."'";
			$check_alipay_result_sql = tep_db_query($check_alipay_select_sql);
			if (tep_db_num_rows($check_alipay_result_sql)) {
				tep_db_perform(TABLE_ALIPAY, $alipay_data_array, 'update', ' alipay_orders_id="'.(int)$_REQUEST['out_trade_no'].'"');
			} else {
				$alipay_data_array['alipay_orders_id'] = (int)$_REQUEST['out_trade_no'];
				tep_db_perform(TABLE_ALIPAY, $alipay_data_array);
			}
		}
	}
}
echo $success_flag;
?>