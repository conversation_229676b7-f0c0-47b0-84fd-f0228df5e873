<?php

ini_set('display_errors', 1);
error_reporting(E_ALL);

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html; charset=utf-8');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');
include_once(DIR_WS_CLASSES . 'direct_topup.php');
include_once(DIR_WS_INCLUDES . 'modules/direct_topup/fulu.php');
include_once(DIR_WS_CLASSES . 'slack_notification.php');

ini_set("memory_limit", "64M");
tep_set_time_limit(0);

$languages_id = 1;

tep_db_connect() or die('Unable to connect to database server!');
$slack_notification = new slack_notification();
// set application wide parameters
$configuration_sql = "	SELECT configuration_key as cfgKey, configuration_value as cfgValue 
						FROM " . TABLE_CONFIGURATION;
$configuration_query = tep_db_query($configuration_sql);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    if (!defined($configuration['cfgKey'])) {
        define($configuration['cfgKey'], $configuration['cfgValue']);
    }
}

$fulu = new dtu_fulu();

// Check with Fulu after receive notify from Fulu
$top_up_array = $fulu->processCallBackRequest($_POST);

if (isset($top_up_array['status'])) {
    $orders_products_select_sql = "SELECT op.products_id, o.customers_name, op.products_delivered_quantity, op.final_price, 
                                        o.customers_email_address, o.orders_id, op.products_quantity, op.products_name
                                    FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                                    INNER JOIN " . TABLE_ORDERS . " AS o
                                        ON o.orders_id = op.orders_id
                                    WHERE op.orders_products_id = '" . $top_up_array['orders_products_id'] . "'
                                        AND o.orders_status IN ('2', '3')";
    $orders_products_result_sql = tep_db_query($orders_products_select_sql);
    if ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
        $top_up_array['products_id'] = $orders_products_row['products_id'];
        $top_up_array['orders_id'] = $orders_products_row['orders_id'];
        $top_up_array['products_title'] = $orders_products_row['products_name'];
        $top_up_array['customers_name'] = $orders_products_row['customers_name'];
        $top_up_array['customers_email_address'] = $orders_products_row['customers_email_address'];
    }

    $publishers_games_select_sql = "SELECT pg.publishers_games_id, pg.publishers_game, pg.publishers_games_pending_message,
                                        pg.publishers_games_reloaded_message, pg.publishers_games_failed_message,
                                        pg.publishers_games_daily_limit, pg.publishers_games_today_topped_amount,
                                        pg.publishers_games_status, pg.categories_id
                                    FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
                                    INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
                                            ON pg.publishers_games_id = pp.publishers_games_id
                                    WHERE pp.products_id = '" . $top_up_array['products_id'] . "'";
    $publishers_games_result_sql = tep_db_query($publishers_games_select_sql);
    if ($publishers_games_row = tep_db_fetch_array($publishers_games_result_sql)) {
        $top_up_array['reloaded_message'] = $publishers_games_row['publishers_games_reloaded_message'];
        $top_up_array['failed_message'] = $publishers_games_row['publishers_games_failed_message'];
    }

    $mail_subject = '[OFFGAMERS] Order Update #' . $top_up_array['orders_id'];
    $mail_content = 'Dear ' . $top_up_array['customers_name'] . ",\n";
    $mail_content .= "Order Number: " . $top_up_array['orders_id'] . "\n";

    switch ($top_up_array['status']) {
        case 'reloaded':
            // Update Topped Amount
            $select_sql = "SELECT pp.publishers_games_id, op.products_quantity, tui.top_up_info_value FROM " . TABLE_ORDERS_TOP_UP . " as otu LEFT JOIN " . TABLE_ORDERS_PRODUCTS . " as op ON otu.orders_products_id = op.orders_products_id LEFT JOIN " . TABLE_TOP_UP_INFO . " as tui ON tui.top_up_info_key = 'amount' AND tui.products_id = op.products_id LEFT JOIN " . TABLE_PUBLISHERS_PRODUCTS . " as pp ON pp.products_id = op.products_id WHERE otu.orders_products_id = " . $top_up_array['orders_products_id'];
            $result_sql = tep_db_query($select_sql);
            if ($result = tep_db_fetch_array($result_sql)) {
                $total = $result['products_quantity'] * $result['top_up_info_value'];
                $games_id = $result['publishers_games_id'];
                $update_topped_amount_sql = "UPDATE " . TABLE_PUBLISHERS_GAMES . " SET publishers_games_today_topped_amount = publishers_games_today_topped_amount + " . $total . " WHERE publishers_games_id = '" . (int) $games_id . "'";
                tep_db_query($update_topped_amount_sql);
            }
            //Email Notification
            if (isset($top_up_array['reloaded_message']) && tep_not_null($top_up_array['reloaded_message'])) {
                $mail_content .= "Status: Reloaded\n";
                $mail_content .= "Top-up Reference ID: " . $top_up_array['publisher_ref_id'] . "\n";
                $mail_content .= "\n\n" . $top_up_array['reloaded_message'];
                @tep_mail($top_up_array['customers_name'], $top_up_array['customers_email_address'], $mail_subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }
            break;

        case 'failed':
            if (isset($top_up_array['failed_message']) && tep_not_null($top_up_array['failed_message'])) {
                $mail_content .= "Status: Failed\n";
                $mail_content .= "\n\n" . $top_up_array['failed_message'];
                @tep_mail($top_up_array['customers_name'], $top_up_array['customers_email_address'], $mail_subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
            }

            $title = "*Failed Delivery #" . $top_up_array['orders_id'] . " delivery on DTU API*";
            $message = "Orders ID : <" . HTTPS_CREW_SERVER . "orders.php?oID=" . $top_up_array['orders_id'] . "&action=edit|" . $top_up_array['orders_id'] . ">\n" .
                    "Top-up ID : <" . HTTPS_CREW_SERVER . "api_report.php?selected_box=reports&action=viewlog&top_up_id=" . $top_up_array['top_up_id'] . "|" . $top_up_array['top_up_id'] . ">\n" .
                    "Product Name : " . $top_up_array['products_title'] . " \n" .
                    "Error : 3000 \n" .
                    "Action : Reattempt delivery or contact publishers with error message";
            $slack_notification->sendMessageStack($title, $message, 'warning', SLACK_WEBHOOK_DTU);
            break;
    }
}