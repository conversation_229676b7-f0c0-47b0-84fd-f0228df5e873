<?php

ini_set('display_errors', 1);
error_reporting(E_ALL);

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html; charset=utf-8');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');
include_once(DIR_WS_CLASSES . 'direct_topup.php');
include_once(DIR_WS_CLASSES . 'slack_notification.php');

// Prevent direct access from Web URL
if (!isset($_SERVER['argv']) || !isset($_SERVER['argv'][1])) {
    exit;
} else {
    $permission_code = $_SERVER['argv'][1];
}
if ($permission_code != 'kj2601ed#%337c7c') {
    exit;
}

ini_set("memory_limit", "64M");
tep_set_time_limit(0);

$languages_id = 1;

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_sql = "	SELECT configuration_key as cfgKey, configuration_value as cfgValue FROM " . TABLE_CONFIGURATION;
$configuration_query = tep_db_query($configuration_sql);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    if (!defined($configuration['cfgKey'])) {
        define($configuration['cfgKey'], $configuration['cfgValue']);
    }
}

if (DOWN_FOR_MAINTENANCE == 'true') {
    exit;
}

include_once(DIR_WS_LANGUAGES . 'english.php');

// include caching class
require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();
$slack_notification = new slack_notification();
$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS overdue_process, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 45 MINUTE) AS long_process, cron_process_track_failed_attempt 
                                        FROM " . TABLE_CRON_PROCESS_TRACK . "
                                        WHERE cron_process_track_filename = 'cron_direct_top_up.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '1') {
        if ($cron_process_checking_row['overdue_process'] == 1) {
            $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                                SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
                                                WHERE cron_process_track_filename = 'cron_direct_top_up.php'";
            tep_db_query($cron_process_attempt_update_sql);
        } else {
            $headers = 'To: <EMAIL>' . "\r\n" .
                'From: ' . STORE_NAME . '<<EMAIL>>' . "\r\n" .
                'Reply-To: <EMAIL>' . "\r\n" .
                'X-Mailer: PHP/' . phpversion();
            mail("<EMAIL>", EMAIL_SUBJECT_PREFIX . " Cronjob Failed", 'direct top-up cronjob failed at ' . $cron_process_checking_row['cron_process_track_start_date'], $headers);
        }

        if ($cron_process_checking_row['long_process'] == 1) {  // Prevent DTU cronjob keep awaiting
            $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                        SET cron_process_track_in_action=1, 
                                            cron_process_track_start_date=now(),
                                            cron_process_track_failed_attempt=0 
                                        WHERE cron_process_track_filename = 'cron_direct_top_up.php'";
            tep_db_query($cron_process_update_sql);
        } else {
            exit;
        }
    } else {
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
									SET cron_process_track_in_action=1, 
										cron_process_track_start_date=now(),
										cron_process_track_failed_attempt=0 
									WHERE cron_process_track_filename = 'cron_direct_top_up.php'";
        tep_db_query($cron_process_update_sql);
    }

    $orders_top_up_array = array();
    $publishers_array = array();
    $orders_top_up_select_sql = "	SELECT otu.top_up_id, otu.publishers_id, otu.orders_products_id, otu.top_up_created_date
									FROM " . TABLE_ORDERS_TOP_UP . " AS otu 
									INNER JOIN " . TABLE_PUBLISHERS . " AS p
										ON p.publishers_id = otu.publishers_id
									WHERE p.publishers_status = '1'
										AND otu.top_up_process_flag = '0'
										AND otu.top_up_status = '1'
									LIMIT 30";
    $orders_top_up_result_sql = tep_db_query($orders_top_up_select_sql);
    while ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result_sql)) {
        $orders_products_select_sql = "	SELECT o.orders_id,op.products_id, o.customers_name, op.products_delivered_quantity, op.final_price, 
											o.customers_email_address, o.orders_id, op.products_quantity, op.products_name 
										FROM " . TABLE_ORDERS_PRODUCTS . " AS op
										INNER JOIN " . TABLE_ORDERS . " AS o
											ON o.orders_id = op.orders_id
										WHERE op.orders_products_id = '" . $orders_top_up_row['orders_products_id'] . "'
											AND o.orders_status IN ('2', '3')";
        $orders_products_result_sql = tep_db_query($orders_products_select_sql);
        if ($orders_products_row = tep_db_fetch_array($orders_products_result_sql)) {
            $orders_top_up_array[$orders_top_up_row['top_up_id']] = array_merge($orders_top_up_row, $orders_products_row);
            $products_array[$orders_products_row['products_id']] = '';
        }
    }

    if (count($orders_top_up_array)) {
        // hold top-up
        $hold_top_up_data_sql = array(
            'top_up_process_flag' => '1',
            'top_up_last_processed_time' => 'now()'
        );
        tep_db_perform(TABLE_ORDERS_TOP_UP, $hold_top_up_data_sql, 'update', " top_up_id IN ('" . implode("','", array_keys($orders_top_up_array)) . "')");

        $direct_topup_obj = new direct_topup();
        foreach ($products_array as $products_id_loop => $products_data_loop) {
            $products_array[$products_id_loop] = $direct_topup_obj->get_top_up_info($products_id_loop);
        }

        $products_games_array = array();
        foreach ($orders_top_up_array as $orders_top_up_id_loop => $orders_top_up_data_loop) {
            if (!isset($products_games_array[$orders_top_up_data_loop['products_id']])) {
                $publishers_games_select_sql = "SELECT pg.publishers_games_id, pg.publishers_game, pg.publishers_games_pending_message, 
													pg.publishers_games_reloaded_message, pg.publishers_games_failed_message, 
													pg.publishers_games_daily_limit, pg.publishers_games_today_topped_amount,
													pg.publishers_games_status, pg.categories_id 
												FROM " . TABLE_PUBLISHERS_GAMES . " AS pg 
												INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
													ON pg.publishers_games_id = pp.publishers_games_id 
												WHERE pp.products_id = '" . $orders_top_up_data_loop['products_id'] . "'";
                $publishers_games_result_sql = tep_db_query($publishers_games_select_sql);
                $publishers_games_row = tep_db_fetch_array($publishers_games_result_sql);
                $products_games_array[$orders_top_up_data_loop['products_id']] = array(
                    'publishers_game' => $publishers_games_row['publishers_game'],
                    'publishers_games_id' => $publishers_games_row['publishers_games_id'],
                    'categories_id' => $publishers_games_row['categories_id'],
                    'publishers_games_pending_message' => $publishers_games_row['publishers_games_pending_message'],
                    'publishers_games_reloaded_message' => $publishers_games_row['publishers_games_reloaded_message'],
                    'publishers_games_failed_message' => $publishers_games_row['publishers_games_failed_message']
                );
                $publishers_games_array[$publishers_games_row['publishers_games_id']] = array(
                    'publishers_games_daily_limit' => $publishers_games_row['publishers_games_daily_limit'],
                    'publishers_games_today_topped_amount' => $publishers_games_row['publishers_games_today_topped_amount'],
                    'publishers_games_status' => (int)$publishers_games_row['publishers_games_status']
                );
            }

            $games_id = $products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_id'];

            if ($publishers_games_array[$games_id]['publishers_games_status'] == 0) {
                // hold top-up
                $skip_top_up_data_sql = array('top_up_process_flag' => '2');
                tep_db_perform(TABLE_ORDERS_TOP_UP, $skip_top_up_data_sql, 'update', " top_up_id = '" . (int)$orders_top_up_id_loop . "'");

                // sending notification via slack
                $title = "*Failed Delivery #" . $orders_top_up_data_loop['orders_id'] . " delivery on DTU API*";
                $message = "Orders ID : " . $orders_top_up_data_loop['orders_id'] . "\n" .
                           "Top-up ID : " . $orders_top_up_data_loop['top_up_id'] . "\n" .
                           "Product Name : " . $orders_top_up_data_loop['products_name'] . " \n" .
                           "Error : Publishers Games turned off \n" .
                           "Action : Turn on Publishers Games and repost top up transaction";
                $slack_notification->sendMessageStack($title, $message, 'warning', SLACK_WEBHOOK_DTU);
                
                continue;
            }

            // Get customer top-up info
            $customers_top_up_key_array = array();
            $customers_top_up_info_array = array();

            if (isset($products_array[$orders_top_up_data_loop['products_id']]) && count($products_array[$orders_top_up_data_loop['products_id']])) {
                foreach ($products_array[$orders_top_up_data_loop['products_id']] as $top_up_info_key_loop => $top_up_info_data_loop) {
                    if ($top_up_info_data_loop['top_up_info_type_id'] == 2) {
                        $customers_top_up_info_array[$top_up_info_data_loop['top_up_info_id']] = '';
                        $customers_top_up_key_array[$top_up_info_data_loop['top_up_info_key']] = $top_up_info_data_loop['top_up_info_id'];
                    }
                }

                if (count($customers_top_up_info_array)) {
                    $customers_top_up_info_select_sql = "	SELECT top_up_info_id, top_up_value
															FROM " . TABLE_CUSTOMERS_TOP_UP_INFO . "
															WHERE orders_products_id = '" . $orders_top_up_data_loop['orders_products_id'] . "'
																AND top_up_info_id IN ('" . implode("','", array_keys($customers_top_up_info_array)) . "')";
                    $customers_top_up_info_result_sql = tep_db_query($customers_top_up_info_select_sql);
                    while ($customers_top_up_info_row = tep_db_fetch_array($customers_top_up_info_result_sql)) {
                        $customers_top_up_info_array[$customers_top_up_info_row['top_up_info_id']] = htmlspecialchars_decode($customers_top_up_info_row['top_up_value']);
                    }
                }
            }

            $total_topup_amount = ($products_array[$orders_top_up_data_loop['products_id']]['amount']['top_up_info_value'] * $orders_top_up_data_loop['products_quantity']);

            if (isset($publishers_games_array[$games_id]['publishers_games_daily_limit'])) {
                $daily_limit = (int)$publishers_games_array[$games_id]['publishers_games_daily_limit'];

                if ($daily_limit > 0) {
                    $publishers_configuration_sql = "	SELECT publishers_configuration_value 
														FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
														WHERE publishers_id = '" . $orders_top_up_data_loop['publishers_id'] . "'
															AND publishers_configuration_key = 'PUBLISHER_API_ID'";
                    $publishers_configuration_result = tep_db_query($publishers_configuration_sql);
                    $publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result);
                    $publisher_api_id = $publishers_configuration_row['publishers_configuration_value'];

                    $earlier_notification_daily_limit = $daily_limit * 0.75; // 75% quota notification
                    $coming_daily_accumulate_top_up_amount = $total_topup_amount + $publishers_games_array[$games_id]['publishers_games_today_topped_amount'];

                    if ($coming_daily_accumulate_top_up_amount >= $earlier_notification_daily_limit) {
                        $categories_name_select_sql = "	SELECT cd.categories_name 
                                                        FROM " . TABLE_CATEGORIES_DESCRIPTION . " as cd
                                                        WHERE cd.categories_id = '" . $products_games_array[$orders_top_up_data_loop['products_id']]['categories_id'] . "'
                                                            AND cd.language_id='1'";
                        $categories_name_select_result = tep_db_query($categories_name_select_sql);
                        $categories_name_row = tep_db_fetch_array($categories_name_select_result);

                        $title = "Direct Top-up: Exceed 75% daily topup amount - " . $publisher_api_id;
                        $message = "Publisher: " . $publisher_api_id . "\n" .
                            "Game: " . $categories_name_row['categories_name'] . "\n" .
                            "Current amount: " . $coming_daily_accumulate_top_up_amount . "\n" .
                            "Daily Top-up Limit: " . $daily_limit;
                        $slack_notification->sendMessageStack($title, $message, 'warning', SLACK_WEBHOOK_DTU);
                    }

                    if ($coming_daily_accumulate_top_up_amount > $daily_limit) {
                        $skip_top_up_data_sql = array('top_up_process_flag' => '0');
                        tep_db_perform(TABLE_ORDERS_TOP_UP, $skip_top_up_data_sql, 'update', " top_up_id = '" . (int)$orders_top_up_id_loop . "'");

                        $publishers_games_array[$games_id]['publishers_games_status'] = 0;
                        $publishers_games_status_data_sql = array('publishers_games_status' => '0');
                        tep_db_perform(TABLE_PUBLISHERS_GAMES, $publishers_games_status_data_sql, 'update', " publishers_games_id = '" . (int)$games_id . "'");

                        $message = "Publisher: " . $publisher_api_id . "\n" .
                            "Game: " . $products_games_array[$orders_top_up_data_loop['products_id']]['publishers_game'] . "\n" .
                            "Product ID:" . $orders_top_up_data_loop['products_id'] . "\n" .
                            "Order ID:" . $orders_top_up_data_loop['orders_id'] . "\n" .
                            "Top-up ID:" . $orders_top_up_id_loop . "\n" .
                            "Top-up Amount:" . $publishers_games_array[$games_id]['publishers_games_today_topped_amount'] . "\n" .
                            "Top-up limit:" . $daily_limit . "\n" .
                            "Current Top-up Amount: " . $total_topup_amount;
                        $slack_notification->sendMessageStack('Exceed limit', $message, 'warning', SLACK_WEBHOOK_DTU);
                        continue;
                    }
                }
            }

            $topup_array = $direct_topup_obj->do_top_up($orders_top_up_data_loop['orders_products_id'], $orders_top_up_data_loop['publishers_id'], $orders_top_up_data_loop['top_up_id'], $products_array[$orders_top_up_data_loop['products_id']]['amount_type']['top_up_info_value'], (isset($products_array[$orders_top_up_data_loop['products_id']]['amount']['top_up_info_value']) ? $products_array[$orders_top_up_data_loop['products_id']]['amount']['top_up_info_value'] : ''), $orders_top_up_data_loop['products_quantity'], (isset($customers_top_up_key_array['account']) && $customers_top_up_info_array[$customers_top_up_key_array['account']] ? $customers_top_up_info_array[$customers_top_up_key_array['account']] : ''), (isset($customers_top_up_key_array['character']) && isset($customers_top_up_info_array[$customers_top_up_key_array['character']]) ? $customers_top_up_info_array[$customers_top_up_key_array['character']] : ''), (isset($products_games_array[$orders_top_up_data_loop['products_id']]['publishers_game']) ? $products_games_array[$orders_top_up_data_loop['products_id']]['publishers_game'] : ''), (isset($customers_top_up_key_array['server']) && $customers_top_up_info_array[$customers_top_up_key_array['server']] ? $customers_top_up_info_array[$customers_top_up_key_array['server']] : ''), (isset($customers_top_up_key_array['account_platform']) && $customers_top_up_info_array[$customers_top_up_key_array['account_platform']] ? $customers_top_up_info_array[$customers_top_up_key_array['account_platform']] : ''), array(
                'pid' => $orders_top_up_data_loop['products_id'],
                'topup_info' => $products_array[$orders_top_up_data_loop['products_id']],
                'publisher_games_id' => $games_id,
                'orders_top_up' => $orders_top_up_data_loop
            ));

            $oID = $orders_top_up_data_loop['orders_id'];
            msOrderSQSPush($oID);

            $update_reset_top_up = "	UPDATE " . TABLE_ORDERS_TOP_UP . "
										SET top_up_process_flag = '2' ,
											publishers_response_time = now() 
										WHERE orders_products_id = '" . $orders_top_up_data_loop['orders_products_id'] . "'"; //MINUTE
            tep_db_query($update_reset_top_up);

            $debug_email_content = '';
            $debug_email_content .= "Top-up ID: " . $orders_top_up_data_loop['top_up_id'] . "\n";
            $debug_email_content .= "Amount Type: " . $products_array[$orders_top_up_data_loop['products_id']]['amount_type']['top_up_info_value'] . "\n";
            $debug_email_content .= "Amount: " . (isset($products_array[$orders_top_up_data_loop['products_id']]['amount']['top_up_info_value']) ? $products_array[$orders_top_up_data_loop['products_id']]['amount']['top_up_info_value'] : '') . "\n";
            $debug_email_content .= "Quantity: " . $orders_top_up_data_loop['products_quantity'] . "\n";
            $debug_email_content .= "Product Name: " . $orders_top_up_data_loop['products_name'] . "\n";
            $debug_email_content .= "Publisher Ref ID: " . (isset($topup_array['publisher_ref_id']) ? $topup_array['publisher_ref_id'] : '-') . "\n";
            $debug_email_content .= "Result Code: " . (isset($topup_array['result_code']) ? $topup_array['result_code'] : '-') . "\n";
            $debug_email_content .= "Status: " . (isset($topup_array['top_up_status']) ? $topup_array['top_up_status'] : '-') . "\n";

            if (!isset($topup_array['top_up_status']) || (strtolower($topup_array['top_up_status']) != 'pending' && strtolower($topup_array['top_up_status']) != 'reloaded')) {
                $data = json_encode(array(
                    'text' => '*[og-api] Top-up Notification (' . (int)$orders_top_up_data_loop['orders_id'] . ')*',
                    'attachments' => array(
                        array(
                            'color' => 'warning',
                            'text' => $debug_email_content
                        )
                    )
                ));
                $slack_notification->send(SLACK_WEBHOOK_DEV_INFO, $data);
            }

            if (isset($topup_array['top_up_status'])) {

                $mail_subject = '[OFFGAMERS] Order Update #' . $orders_top_up_data_loop['orders_id'];

                $mail_content = 'Dear ' . $orders_top_up_data_loop['customers_name'] . ",\n";
                $mail_content .= "Order Number: " . $orders_top_up_data_loop['orders_id'] . "\n";

                switch (strtolower($topup_array['top_up_status'])) {
                    case 'failed':
                        if ($topup_array['result_code'] == '1508') {

                            $tomorrow_date_select_sql = "	SELECT DATE_FORMAT(DATE_ADD(now(), INTERVAL 1 DAY), '%Y-%m-%d %00:00:00') as tomorrow_date;";
                            $tomorrow_date_result_sql = tep_db_query($tomorrow_date_select_sql);
                            $tomorrow_date_row = tep_db_fetch_array($tomorrow_date_result_sql);

                            $orders_top_up_remark_data_sql = array(
                                'top_up_id' => (int)$orders_top_up_data_loop['top_up_id'],
                                'data_added' => 'now()',
                                'remark' => "Top-up: Failed, Exceed player top-up limit (top-up again after " . $tomorrow_date_row['tomorrow_date'] . ")"
                            );
                            tep_db_perform(TABLE_ORDERS_TOP_UP_REMARK, $orders_top_up_remark_data_sql);

                            $top_up_queue_data_sql = array(
                                'top_up_id' => (int)$orders_top_up_data_loop['top_up_id'],
                                'counter' => '0',
                                'check_date' => $tomorrow_date_row['tomorrow_date'],
                                'top_up_queue_action' => '1',
                                'date_added' => 'now()'
                            );
                            tep_db_perform(TABLE_TOP_UP_QUEUE, $top_up_queue_data_sql);

                            $mail_content .= "Status: Failed (Over limit)\n";
                            $mail_content .= "\n\n" . $products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_failed_message'];
                            @tep_mail($orders_top_up_data_loop['customers_name'], $orders_top_up_data_loop['customers_email_address'], $mail_subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                        } elseif ($topup_array['result_code'] != '1512' && isset($products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_failed_message']) && tep_not_null($products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_failed_message'])) {
                            $mail_content .= "Status: Failed\n";
                            $mail_content .= "\n\n" . $products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_failed_message'];
                            @tep_mail($orders_top_up_data_loop['customers_name'], $orders_top_up_data_loop['customers_email_address'], $mail_subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                        }

                        if ($topup_array['result_code'] != '1512'){
                            $error = (!empty($topup_array['error_msg']) ? $topup_array['error_msg'] : $topup_array['result_code'] . ' : ' . $direct_topup_obj->get_result_code_description($topup_array['result_code']));

                            $title = "*Failed Delivery #" . $orders_top_up_data_loop['orders_id'] . " delivery on DTU API*";
                            $message = "Orders ID : <" . HTTPS_CREW_SERVER . "orders.php?oID=" . $orders_top_up_data_loop['orders_id'] . "&action=edit|" . $orders_top_up_data_loop['orders_id'] . ">\n" .
                                "Top-up ID : <" . HTTPS_CREW_SERVER . "api_report.php?selected_box=reports&action=viewlog&top_up_id=" . $orders_top_up_data_loop['top_up_id'] . "|" . $orders_top_up_data_loop['top_up_id'] . ">\n" .
                                "Product Name : " . $orders_top_up_data_loop['products_name'] . " \n" .
                                "Error : " . $error . " \n" .
                                "Action : Reattempt delivery or contact publishers with error message";
                            $slack_notification->sendMessageStack($title, $message, 'warning', SLACK_WEBHOOK_DTU);
                        }

                        break;

                    case 'not_found':
                        $data = json_encode(array(
                            'text' => '[OG Crew] Top-up Not Found (' . (int)$orders_top_up_data_loop['orders_id'] . ')',
                            'attachments' => array(
                                array(
                                    'color' => 'danger',
                                    'text' => json_encode($topup_array)
                                )
                            )
                        ));
                        $slack_notification->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
                        break;
                    case 'pending':
                        if (isset($products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_pending_message']) && tep_not_null($products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_pending_message']) && isset($topup_array['result_code']) && $topup_array['result_code'] != '1200' && $topup_array['result_code'] != '1513') {
                            $mail_content .= "Status: Pending\n";
                            $mail_content .= "\n\n" . $products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_pending_message'];
                            @tep_mail($orders_top_up_data_loop['customers_name'], $orders_top_up_data_loop['customers_email_address'], $mail_subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);

                            $data = json_encode(array(
                                'text' => '[OG Crew] Top Up Pending (' . (int)$orders_top_up_data_loop['orders_id'] . ')',
                                'attachments' => array(
                                    array(
                                        'color' => 'warning',
                                        'text' => $mail_content
                                    )
                                )
                            ));
                            $slack_notification->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
                        }
                        if (!(isset($topup_array['publisher_ref_id']) && !empty($topup_array['publisher_ref_id']))) {
                            $top_up_inqueue_data_sql = array(
                                'top_up_id' => (int)$orders_top_up_data_loop['top_up_id'],
                                'date_added' => 'now()'
                            );
                            tep_db_perform(TABLE_TOP_UP_QUEUE, $top_up_inqueue_data_sql);
                        }

                        break;
                    case 'reloaded':
                        $publishers_games_array[$games_id]['publishers_games_today_topped_amount'] = $publishers_games_array[$games_id]['publishers_games_today_topped_amount'] + $total_topup_amount;
                        tep_db_query("UPDATE " . TABLE_PUBLISHERS_GAMES . " SET publishers_games_today_topped_amount = '" . tep_db_input($publishers_games_array[(int)$games_id]['publishers_games_today_topped_amount']) . "' WHERE publishers_games_id = '" . (int)$games_id . "'");

                        if (isset($products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_reloaded_message']) && tep_not_null($products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_reloaded_message'])) {
                            $mail_content .= "Status: Reloaded\n";
                            if ($topup_array['result_code'] == '2000') {
                                $mail_content .= "Top-up Reference ID: " . $topup_array['publisher_ref_id'] . "\n";
                            }
                            $mail_content .= "\n\n" . $products_games_array[$orders_top_up_data_loop['products_id']]['publishers_games_reloaded_message'];
                            @tep_mail($orders_top_up_data_loop['customers_name'], $orders_top_up_data_loop['customers_email_address'], $mail_subject, $mail_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
                        }

                        $update_reset_top_up = "UPDATE " . TABLE_ORDERS_TOP_UP . "
                                                SET top_up_status = '3',
                                                    result_code = '',
                                                    publishers_response_time = now()
                                                WHERE orders_products_id = '" . $orders_top_up_data_loop['orders_products_id'] . "'"; //MINUTE
                        tep_db_query($update_reset_top_up);
                        break;
                }
            }
        }
    }
    // Exclude reach top up limit
    $repost_topup_select_sql = "	SELECT top_up_id 
                                    FROM " . TABLE_ORDERS_TOP_UP . "
                                    WHERE (top_up_status = '10' OR top_up_status = '1')
                                        AND (
                                            (
                                            top_up_process_flag = '2'
                                            AND result_code <> 1508
                                            AND result_code <> 1510
                                            AND result_code <> 1511
                                            AND result_code <> 1512
                                            AND result_code <> 1513
                                            AND top_up_created_date > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
                                            AND top_up_created_date > DATE_SUB(top_up_last_processed_time, INTERVAL 5 MINUTE)
                                            )
                                        OR
                                            (
                                                top_up_process_flag = '2'
                                                AND result_code = 1512
                                                AND top_up_created_date > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
                                            )
                                        )"
                                        ;
                                        // failed & processed & >15mins from cron time & 5 mins between created time and last process time
    $repost_topup_select_result = tep_db_query($repost_topup_select_sql);

    while ($repost_topup_row = tep_db_fetch_array($repost_topup_select_result)) {
        $reset_topup_sql = array(
            'top_up_process_flag' => '0',
            'top_up_status' => '1',
            'top_up_last_processed_time' => 'now()'
        );
        tep_db_perform(TABLE_ORDERS_TOP_UP, $reset_topup_sql, 'update', " top_up_id = '" . $repost_topup_row['top_up_id'] . "'");

        include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
        $repost_topup_order_sql = "	SELECT op.orders_id 
                                    FROM " . TABLE_ORDERS_PRODUCTS . " op inner join " . TABLE_ORDERS_TOP_UP . " otu
                                    ON otu.orders_products_id = op.orders_products_id
                                    WHERE otu.top_up_id = " . $repost_topup_row['top_up_id'];
        $repost_topup_order_result = tep_db_query($repost_topup_order_sql);
        while ($repost_topup_order_row = tep_db_fetch_array($repost_topup_order_result)) {
            $oID = $repost_topup_order_row['orders_id'];
            msOrderSQSPush($oID);
        }

    }

    $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
								SET cron_process_track_in_action=0 
								WHERE cron_process_track_filename = 'cron_direct_top_up.php'";
    tep_db_query($unlock_cron_process_sql);
}

function msOrderSQSPush($oID)
{
    include_once(DIR_WS_CLASSES . 'ogm_amazon_ws.php');
    $slack_notification = new slack_notification();
    try {
        $aws_obj = new ogm_amazon_ws();
        $params = array(
            'key' => AWS_SQS_KEY,
            'secret' => AWS_SQS_SECRET,
        );
        $aws_obj->init_sqs(AWS_SQS_MS_ORDER_URL, $params);
        $timestamp = time();
        $data = ['params' => ['order_id' => $oID, 'timestamp' => $timestamp]];
        $payload = ['data' => $data];
        $aws_obj->sendMessage($payload);
        return true;
    } catch (exception $e) {
        $title = 'OG Crew DTU - Ms Order SQS failure';
        $message = $e->getMessage();
        $slack_notification->sendMessageStack($title, $message, 'warning', SLACK_WEBHOOK_DEV_ALERT);
        return true;
    }
}

?>