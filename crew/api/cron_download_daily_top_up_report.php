<?php
	include_once('includes/configure.php');
	include_once(DIR_WS_INCLUDES . 'filenames.php');
	include_once(DIR_WS_INCLUDES . 'database_tables.php');
	include_once(DIR_WS_FUNCTIONS . 'database.php');
	include_once(DIR_WS_FUNCTIONS . 'general.php');
	include_once(DIR_WS_FUNCTIONS . 'html_output.php');
	include_once(DIR_WS_CLASSES . 'direct_topup.php');
	include_once(DIR_WS_CLASSES . 'mime.php');
	include_once(DIR_WS_CLASSES . 'email.php');
	
	ini_set("memory_limit", "64M");
	tep_set_time_limit(0);
	
	tep_db_connect() or die('Unable to connect to database server!');
	
	// include caching class
	require(DIR_WS_CLASSES . 'cache_abstract.php');
	require(DIR_WS_CLASSES . 'memcache.php');
	$memcache_obj = new OGM_Cache_MemCache();	
	
	$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS overdue_process, cron_process_track_failed_attempt 
											FROM " . TABLE_CRON_PROCESS_TRACK . "
											WHERE cron_process_track_filename = 'cron_download_daily_top_up_report.php'";
	$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);
	
	if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
		if ($cron_process_checking_row['cron_process_track_in_action'] == '1') {
			if ($cron_process_checking_row['overdue_process'] == 1) {
				$cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
													SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1 
													WHERE cron_process_track_filename = 'cron_download_daily_top_up_report.php'";
				tep_db_query($cron_process_attempt_update_sql);
			} else {
				mail("<EMAIL>", "[OFFGAMERS] Cronjob Failed", 'Direct top-up daily report cronjob failed at ' . $cron_process_checking_row['cron_process_track_start_date'],
				     "From: <EMAIL>\r\n" .
				     "X-Mailer: PHP/" . phpversion());
			}
			exit;
		} else {
			$cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
										SET cron_process_track_in_action=1, 
											cron_process_track_start_date=now(),
											cron_process_track_failed_attempt=0 
										WHERE cron_process_track_filename = 'cron_download_daily_top_up_report.php'";
			tep_db_query($cron_process_update_sql);
		}
		
		// set application wide parameters
		$configuration_sql = "	SELECT configuration_key as cfgKey, configuration_value as cfgValue 
								FROM " . TABLE_CONFIGURATION;
		$configuration_query = tep_db_query($configuration_sql);
		while ($configuration = tep_db_fetch_array($configuration_query)) {
			if (!defined($configuration['cfgKey'])) define($configuration['cfgKey'], $configuration['cfgValue']);
		}
		
		$direct_topup_obj = new direct_topup();
		
		$publishers_select_sql = "	SELECT publishers_id, publishers_name 
									FROM " . TABLE_PUBLISHERS . "
									WHERE publishers_status = '1'";
		$publishers_result_sql = tep_db_query($publishers_select_sql);
		while ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
			$publishers_id = $publishers_row['publishers_id'];
			
			$start_time = mktime(0, 0, 0, date('m'), date('d')-1, date('Y'));
			$end_time = mktime(23, 59, 59, date('m'), date('d')-1, date('Y'));
            
			$file = 'download/' . $publishers_id . '_' . date("Ymd", $start_time) . '.csv';
			$direct_topup_obj->save_top_up_list($publishers_id, $start_time, $end_time, $file);
		}
        
		$unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
									SET cron_process_track_in_action=0 
									WHERE cron_process_track_filename = 'cron_download_daily_top_up_report.php'";
		tep_db_query($unlock_cron_process_sql);
	}
?>