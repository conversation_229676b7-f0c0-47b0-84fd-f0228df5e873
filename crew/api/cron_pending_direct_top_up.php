<?php

ini_set('display_errors', 1);
error_reporting(E_ALL);

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');
include_once(DIR_WS_CLASSES . 'direct_topup.php');

// Prevent direct access from Web URL
if (!isset($_SERVER['argv']) || !isset($_SERVER['argv'][1])) {
    exit;
} else {
    $permission_code = $_SERVER['argv'][1];
}
if ($permission_code != 'kj2601ed#%337c7c')
    exit;

//require('includes/application_top.php');

tep_db_connect() or die('Unable to connect to database server!');

ini_set("memory_limit", "64M");
tep_set_time_limit(0);

if (!defined('DIR_FS_CATALOG_MODULES')) {
    define('DIR_FS_CATALOG_MODULES', DIR_WS_MODULES);
}
//require_once(DIR_WS_LANGUAGES . 'english/checkout_process.php');

$direct_topup_obj = new direct_topup();

// counter => minutes
$counter_control = array('0' => 10,
    '1' => 30,
    '2' => 60,
    '3' => 120,
    '4' => 300);
$MAX_COUNTER_CONTROL = count($counter_control);

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date, cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS overdue_process, cron_process_track_failed_attempt 
                                        FROM " . TABLE_CRON_PROCESS_TRACK . "
                                        WHERE cron_process_track_filename = 'cron_pending_direct_top_up.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql);

if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '1') {
        if ($cron_process_checking_row['overdue_process'] == 1) {
            $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                                SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
                                                WHERE cron_process_track_filename = 'cron_pending_direct_top_up.php'";
            tep_db_query($cron_process_attempt_update_sql);
        } else {
            $headers = 'To: <EMAIL>' . "\r\n" .
                    'From: <EMAIL>' . "\r\n" .
                    'Reply-To: <EMAIL>' . "\r\n" .
                    'X-Mailer: PHP/' . phpversion();
            mail("<EMAIL>", "[OFFGAMERS] Cronjob Failed", 'Pending direct top-up cronjob failed at ' . $cron_process_checking_row['cron_process_track_start_date'], $headers);
        }
        exit;
    } else {
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
									SET cron_process_track_in_action=1, 
										cron_process_track_start_date=now(),
										cron_process_track_failed_attempt=0 
									WHERE cron_process_track_filename = 'cron_pending_direct_top_up.php'";
        tep_db_query($cron_process_update_sql);
    }

    $remove_non_pending_array = array();
    $remove_non_pending_select_sql = "	SELECT tui.top_up_id
										FROM " . TABLE_TOP_UP_QUEUE . " AS tui 
										INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu 
											ON tui.top_up_id = otu.top_up_id
												AND otu.top_up_status NOT IN ('1','10') ";
    $remove_non_pending_result_sql = tep_db_query($remove_non_pending_select_sql);
    while ($remove_non_pending_row = tep_db_fetch_array($remove_non_pending_result_sql)) {
        $remove_non_pending_array[] = $remove_non_pending_row['top_up_id'];
    }

    if (count($remove_non_pending_array)) {
        $delete_non_pending_sql = "	DELETE FROM " . TABLE_TOP_UP_QUEUE . "
									WHERE top_up_id IN ('" . implode("','", $remove_non_pending_array) . "')";
        tep_db_query($delete_non_pending_sql);
    }

    $cron_topup_counter = 0;
    $cron_check_status_array = array();

    $cron_topup_select_sql = "	SELECT p.publishers_status, tui.top_up_id, tui.counter, tui.check_date, tui.top_up_queue_action, (tui.check_date <= now()) as check_flag
								FROM " . TABLE_TOP_UP_QUEUE . " as tui 
								INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu 
									ON tui.top_up_id = otu.top_up_id
								INNER JOIN " . TABLE_PUBLISHERS . " AS p
									ON p.publishers_id = otu.publishers_id
								WHERE tui.counter <= '" . (int) $MAX_COUNTER_CONTROL . "'
								ORDER BY tui.check_date";
    $cron_topup_result_sql = tep_db_query($cron_topup_select_sql);
    while ($cron_topup_row = tep_db_fetch_array($cron_topup_result_sql)) {
        echo "<pre>";
        print_r($cron_topup_row);
        echo "<BR><BR>";

        $time_diff_array = get_time_difference($cron_topup_row['check_date'], date("Y-m-d H:i:s"));

        if ($cron_topup_row['top_up_queue_action'] == 1) {
            if ($cron_topup_row['publishers_status'] && $cron_topup_row['check_flag'] && isset($counter_control[$cron_topup_row['counter']])) {
                if ((((int) $time_diff_array['days'] * 1440) + ((int) $time_diff_array['hours'] * 60) + (int) $time_diff_array['minutes']) >= $counter_control[$cron_topup_row['counter']]) {
                    echo "- Qualified Top-up<BR>";
                    $cron_topup_payment_status_data_sql = array('counter' => ((int) $cron_topup_row['counter'] + 1),
                        'check_date' => 'now()');
                    tep_db_perform(TABLE_TOP_UP_QUEUE, $cron_topup_payment_status_data_sql, 'update', " top_up_id = '" . $cron_topup_row['top_up_id'] . "'");

                    $reset_topup_sql = array('top_up_process_flag' => '0',
                        'top_up_status' => '1',
                        'top_up_last_processed_time' => 'now()');
                    tep_db_perform(TABLE_ORDERS_TOP_UP, $reset_topup_sql, 'update', " top_up_id = '" . $cron_topup_row['top_up_id'] . "'");

                    $cron_topup_counter++;
                }
            }
        } else {
            if (isset($counter_control[$cron_topup_row['counter']])) {
                if ((((int) $time_diff_array['days'] * 1440) + ((int) $time_diff_array['hours'] * 60) + (int) $time_diff_array['minutes']) >= $counter_control[$cron_topup_row['counter']]) {
                    echo "- Qualified Check Status<BR>";
                    $cron_check_status_array[$cron_topup_row['top_up_id']] = $cron_topup_row['counter'];
                    $cron_topup_payment_status_data_sql = array('counter' => ((int) $cron_topup_row['counter'] + 1),
                        'check_date' => 'now()');
                    tep_db_perform(TABLE_TOP_UP_QUEUE, $cron_topup_payment_status_data_sql, 'update', " top_up_id = '" . $cron_topup_row['top_up_id'] . "'");
                    $cron_topup_counter++;
                }
            }
        }
        if ($cron_topup_counter >= 30)
            break;
    }

    if (count($cron_check_status_array)) {
        $orders_top_up_select_sql = "	SELECT publishers_ref_id, publishers_id, top_up_id 
										FROM " . TABLE_ORDERS_TOP_UP . "
										WHERE top_up_id IN ('" . implode("','", array_keys($cron_check_status_array)) . "')";
        $orders_top_up_result_sql = tep_db_query($orders_top_up_select_sql);
        while ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result_sql)) {
            $topup_array = $direct_topup_obj->check_top_up_status($orders_top_up_row['publishers_id'], $orders_top_up_row['publishers_ref_id']);

            if (isset($topup_array['top_up_status']) && $topup_array['top_up_status'] == 'RELOADED') {
                switch (strtolower($topup_array['top_up_status'])) {
                    case 'reloaded':
                        echo "DELETE FROM " . TABLE_TOP_UP_QUEUE . " WHERE top_up_id = '" . $orders_top_up_row['top_up_id'] . "'<BR>";
                        tep_db_query("DELETE FROM " . TABLE_TOP_UP_QUEUE . " WHERE top_up_id = '" . $orders_top_up_row['top_up_id'] . "'");
                        break;
                    case 'failed':
                        echo "DELETE FROM " . TABLE_TOP_UP_QUEUE . " WHERE top_up_id = '" . $orders_top_up_row['top_up_id'] . "'<BR>";
                        tep_db_query("DELETE FROM " . TABLE_TOP_UP_QUEUE . " WHERE top_up_id = '" . $orders_top_up_row['top_up_id'] . "'");
                        break;
                }
            }
        }
    }

    $remove_pending_select_sql = "	SELECT tui.top_up_id, op.orders_id
									FROM " . TABLE_TOP_UP_QUEUE . " AS tui
									INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu
										ON tui.top_up_id = otu.top_up_id
									INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op
										ON op.orders_products_id = otu.orders_products_id
									WHERE tui.counter >= '" . (int) $MAX_COUNTER_CONTROL . "'";
    $remove_pending_result_sql = tep_db_query($remove_pending_select_sql);
    while ($remove_pending_row = tep_db_fetch_array($remove_pending_result_sql)) {
        $remove_non_pending_select_sql = "	DELETE FROM " . TABLE_TOP_UP_QUEUE . " 
											WHERE top_up_id = '" . (int) $remove_pending_row['top_up_id'] . "'";
        tep_db_query($remove_non_pending_select_sql);
    }

    $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
								SET cron_process_track_in_action=0 
								WHERE cron_process_track_filename = 'cron_pending_direct_top_up.php'";
    tep_db_query($unlock_cron_process_sql);
}

/**
 * Function to calculate date or time difference.
 * 
 * Function to calculate date or time difference. Returns an array or
 * false on error.
 *
 * <AUTHOR> de Silva                             <<EMAIL>>
 * @copyright    Copyright &copy; 2005, J de Silva
 * @link         http://www.gidnetwork.com/b-16.html    Get the date / time difference with PHP
 * @param        string                                 $start
 * @param        string                                 $end
 * @return       array
 */
function get_time_difference($start, $end) {
    $uts['start'] = strtotime($start);
    $uts['end'] = strtotime($end);
    if ($uts['start'] !== -1 && $uts['end'] !== -1) {
        if ($uts['end'] >= $uts['start']) {
            $diff = $uts['end'] - $uts['start'];
            if ($days = intval((floor($diff / 86400))))
                $diff = $diff % 86400;
            if ($hours = intval((floor($diff / 3600))))
                $diff = $diff % 3600;
            if ($minutes = intval((floor($diff / 60))))
                $diff = $diff % 60;
            $diff = intval($diff);
            return( array('days' => $days, 'hours' => $hours, 'minutes' => $minutes, 'seconds' => $diff) );
        } else {
            //trigger_error( "Ending date/time is earlier than the start date/time", E_USER_WARNING );
        }
    } else {
        //trigger_error( "Invalid date/time data detected", E_USER_WARNING );
    }
    return( false );
}

?>