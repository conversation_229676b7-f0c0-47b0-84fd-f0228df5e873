<?php

ini_set('display_errors', 1);
error_reporting(E_ALL);

header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");
header('Content-Type: text/html; charset=utf-8');

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');
include_once(DIR_WS_CLASSES . 'reward.php');

ini_set("memory_limit", "64M");
tep_set_time_limit(0);

tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$configuration_sql = "	SELECT configuration_key as cfgKey, configuration_value as cfgValue FROM " . TABLE_CONFIGURATION;
$configuration_query = tep_db_query($configuration_sql, 'db_link');
while ($configuration = tep_db_fetch_array($configuration_query)) {
    if (!defined($configuration['cfgKey']))
        define($configuration['cfgKey'], $configuration['cfgValue']);
}

if (DOWN_FOR_MAINTENANCE == 'true') {
    exit;
}

$cron_process_checking_select_sql = "	SELECT cron_process_track_in_action, cron_process_track_start_date, 
                                            cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AS overdue_process, 
                                            cron_process_track_start_date < DATE_SUB(NOW(), INTERVAL 1 DAY) AS long_process, 
                                            cron_process_track_failed_attempt 
                                        FROM " . TABLE_CRON_PROCESS_TRACK . "
                                        WHERE cron_process_track_filename = 'cron_update_reward_top_up_queue.php'";
$cron_process_checking_result_sql = tep_db_query($cron_process_checking_select_sql, 'db_link');
if ($cron_process_checking_row = tep_db_fetch_array($cron_process_checking_result_sql)) {
    if ($cron_process_checking_row['cron_process_track_in_action'] == '1') {
        if ($cron_process_checking_row['overdue_process'] == 1) {
            $cron_process_attempt_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
                                                SET cron_process_track_failed_attempt = cron_process_track_failed_attempt + 1
                                                WHERE cron_process_track_filename = 'cron_update_reward_top_up_queue.php'";
            tep_db_query($cron_process_attempt_update_sql, 'db_link');
        } else {
            $subject = EMAIL_SUBJECT_PREFIX . " Cronjob Failed";
            $message = 'Update Reward Top up queue cronjob failed at ' . $cron_process_checking_row['cron_process_track_start_date'];
            @tep_mail('OffGamers', '<EMAIL>', $subject, $message, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        }

        if ($cron_process_checking_row['long_process'] == 1) {  // Prevent DTU cronjob keep awaiting
            $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . " 
                                        SET cron_process_track_in_action=1, 
                                            cron_process_track_start_date=now(),
                                            cron_process_track_failed_attempt=0 
                                        WHERE cron_process_track_filename = 'cron_update_reward_top_up_queue.php'";
            tep_db_query($cron_process_update_sql, 'db_link');
        } else {
            exit;
        }
    } else {
        $cron_process_update_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
									SET cron_process_track_in_action=1, 
										cron_process_track_start_date=now(),
										cron_process_track_failed_attempt=0 
									WHERE cron_process_track_filename = 'cron_update_reward_top_up_queue.php'";
        tep_db_query($cron_process_update_sql, 'db_link');
    }

    // start update reward top up queue
    $reward_obj = new reward();
    $reward_obj->cron_update_reward_top_up_queue();
    unset($reward_obj);
    // end update reward top up queue

    $unlock_cron_process_sql = "UPDATE " . TABLE_CRON_PROCESS_TRACK . "
								SET cron_process_track_in_action=0 
								WHERE cron_process_track_filename = 'cron_update_reward_top_up_queue.php'";
    tep_db_query($unlock_cron_process_sql, 'db_link');
}
?>