<?
include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'filenames.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'html_output.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');
include_once(DIR_WS_CLASSES . 'direct_topup.php');

tep_db_connect() or die('Unable to connect to database server!');

tep_set_time_limit(0);

// include caching class
require(DIR_WS_CLASSES . 'cache_abstract.php');
require(DIR_WS_CLASSES . 'memcache.php');
$memcache_obj = new OGM_Cache_MemCache();

// set application wide parameters
$configuration_sql = "  SELECT configuration_key as cfgKey, configuration_value as cfgValue
                        FROM " . TABLE_CONFIGURATION;
$configuration_query = tep_db_query($configuration_sql);
while ($configuration = tep_db_fetch_array($configuration_query)) {
    if (!defined($configuration['cfgKey'])) define($configuration['cfgKey'], $configuration['cfgValue']);
}

$direct_topup_obj = new direct_topup();

$publishers_games_sql = "	SELECT publishers_id, publishers_games_id, publishers_game 
							FROM " . TABLE_PUBLISHERS_GAMES;
$publishers_games_result = tep_db_query($publishers_games_sql);
while ($publishers_games_row = tep_db_fetch_array($publishers_games_result)) {
	$direct_topup_obj->publishers($publishers_games_row['publishers_id']);
	$get_publishers_conf_array = $direct_topup_obj->get_publishers_conf();

	$class = $direct_topup_obj->void_include_class($publishers_games_row['publishers_id']);

	if (tep_class_exists('dtu_'.$class)) {
		eval('$direct_topup_class_obj = new dtu_'.$class.'();');
	} else {
		$direct_topup_class_obj = new dtu_offgamers();
	}
    $direct_topup_class_obj->publishers($publishers_games_row['publishers_id']);
  	if ($direct_topup_obj->validate_active_publisher() === true) {

        // Sync Publisher Game List
        if (method_exists($direct_topup_class_obj, 'get_game_list')) {
            $direct_topup_class_obj->get_game_list($publishers_games_row['publishers_id'],$get_publishers_conf_array);
        }

        if (method_exists($direct_topup_class_obj, 'get_account_platform_list')) {
            $account_platform_array = $direct_topup_class_obj->get_account_platform_list($publishers_games_row['publishers_id'], $publishers_games_row['publishers_game'], $get_publishers_conf_array);
            if (isset($account_platform_array['account_platforms'])) {
                $publishers_games_configuration_data_sql = array();
                $publishers_games_configuration_data_sql['publishers_games_configuration_value'] = tep_db_prepare_input(json_encode($account_platform_array['account_platforms']));

                $publishers_games_conf_select_sql = "	SELECT publishers_games_configuration_id
                                                        FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . "
                                                        WHERE publishers_games_id = '".$publishers_games_row['publishers_games_id']."'
                                                            AND publishers_games_configuration_key = 'ACCOUNT_PLATFORM_LIST'";
                $publishers_games_conf_result = tep_db_query($publishers_games_conf_select_sql);
                if ($publishers_games_conf_row = tep_db_fetch_array($publishers_games_conf_result)) {
                    $publishers_games_configuration_data_sql['last_modified'] = 'now()';
                    tep_db_perform(TABLE_PUBLISHERS_GAMES_CONFIGURATION, $publishers_games_configuration_data_sql, 'update', " publishers_games_configuration_id = '".$publishers_games_conf_row['publishers_games_configuration_id']."' ");
                } else {
                    $publishers_games_configuration_data_sql['publishers_games_configuration_key'] = tep_db_prepare_input('ACCOUNT_PLATFORM_LIST');
                    $publishers_games_configuration_data_sql['publishers_games_id'] = $publishers_games_row['publishers_games_id'];
                    $publishers_games_configuration_data_sql['sort_order'] = '50000';
                    $publishers_games_configuration_data_sql['date_added'] = 'now()';
                    tep_db_perform(TABLE_PUBLISHERS_GAMES_CONFIGURATION, $publishers_games_configuration_data_sql);

                }
            }
        } else {
            //
        }

        if (method_exists($direct_topup_class_obj, 'get_server_list')) {
            $server_list_array = $direct_topup_class_obj->get_server_list($publishers_games_row['publishers_id'], $publishers_games_row, $get_publishers_conf_array);

            if (isset($server_list_array['servers']) && tep_not_null($server_list_array['servers'])) {
                $direct_topup_obj->set_publishers_game_server($publishers_games_row['publishers_games_id'], $server_list_array['servers']);
                unset($server_list_array);
            }
        } else {
            //
        }
        $publishers_array = array('server_last_sync' => 'now()');
        tep_db_perform(TABLE_PUBLISHERS, $publishers_array, 'update',
            " publishers_id = " . $publishers_games_row['publishers_id']);
        unset($direct_topup_class_obj);
    }
	unset($direct_topup_class_obj);
}

?>