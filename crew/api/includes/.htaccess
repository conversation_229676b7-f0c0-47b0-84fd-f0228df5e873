# $Id: .htaccess,v 1.1 2016/04/20 09:41:45 weesiong Exp $
#
# This is used with Apache WebServers
# The following blocks direct HTTP requests in this directory recursively
#
# For this to work, you must include the parameter 'Limit' to the AllowOverride                                                                              configuration
#
# Example:
#
#<Directory "/usr/local/apache/htdocs">
#  AllowOverride Limit
#
# 'All' with also work. (This configuration is in your apache/conf/httpd.conf fi                                                                             le)
#
# This does not affect PHP include/require functions
#
# Example: http://server/catalog/includes/application_top.php will not work

<Files *.*>
Order Deny,Allow
Deny from all
</Files>
