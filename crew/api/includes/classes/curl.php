<?php
class curl {
    private $error_array = array();
    public $ssl_verification = false;
    public $connect_via_proxy = false;
    public $http_header = array('Expect:');
    public $custom_request_type = false;

    function curl_request($method, $url, $header = array(), $post_data = '')
    {
        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        if(!empty($post_data)){
            curl_setopt($ch,CURLOPT_POSTFIELDS,$post_data);
        }
        if ($this->ssl_verification) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        } else {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);

        if (defined("HTTP_PROXY")) {
            // Fix PHP 5.4 empty() not accepting constrain / function
            $proxy = HTTP_PROXY;
            if (!empty($proxy)){
                curl_setopt($ch, CURLOPT_PROXY, $proxy);
            }
        }

        $response = curl_exec($ch);

        if ($curl_errno = curl_errno($ch)) {
            $this->set_error($response, $curl_errno, curl_error($ch));
        }

        curl_close($ch);

        return $response;
    }

    function curl_get($url, $data='', $filename='') {
        $getfields = '';

        if (tep_not_null($data)) {
            if (is_array($data)) {
                while (list($key, $value) = each($data)) {
                    $getfields .= $key . '=' . urlencode($value) . '&';
                }
            } else {
                $getfields = $data;
            }
        }

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

        $ch = curl_init($url . '?' . $getfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);    // 2
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->http_header);
        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://my-proxy.offgamers.lan:3128');
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);    // false

        $response = curl_exec($ch);

        if($curl_errno = curl_errno($ch)) {
            $this->set_error($response, $curl_errno, curl_error($ch));
        }

        curl_close($ch);

        return $response;
    }

    function curl_post($url, $data, $filename='') {
        $ch = curl_init($url);

        $postfields = '';
        if (is_array($data)) {
            while (list($key, $value) = each($data)) {
                $postfields .= $key . '=' . urlencode($value) . '&';
            }
        } else {
            $postfields = $data;
        }

        $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";

        if ($this->custom_request_type) {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $this->custom_request_type);
        }

        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_NOPROGRESS, 0);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);

        if (tep_not_null($filename)) {
            $fp = fopen ($filename, 'w+');
            curl_setopt($ch, CURLOPT_FILE, $fp);
        }

        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER,  $this->http_header);

        if ($this->connect_via_proxy) {
            curl_setopt($ch, CURLOPT_PROXY, 'http://my-proxy.offgamers.lan:3128');
        }

        // Verification of the SSL cert
        if ($this->ssl_verification) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); // true
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);    // 2
        } else {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);    // false
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);    // 2   // should be false
        }

        $response = curl_exec($ch);

        if($curl_errno = curl_errno($ch)) {
            $this->set_error($response, $curl_errno, curl_error($ch));
        }

        curl_close($ch);
        if (tep_not_null($filename)) {
            fclose($fp);
        }

        return $response;
    }

    public function get_error() {
        return $this->error_array;
    }

    private function set_error($response, $errorCode, $errorMessage='') {
        $this->error_array = array (
            'response' => $response,
            'error_code' => $errorCode,
            'error_message' => $errorMessage
        );
    }
}
?>