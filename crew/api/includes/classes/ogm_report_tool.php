<?php

class ogm_report_tool {

    public function log_top_up_reloaded($top_up_id, $order_id, $orders_products_id, $products_id, $total_item_amount, $quantity) {
        if ($top_up_id) {
            $delivered_top_up_array = array();
            $delivered_softpin_qty = 1;
            // check topup exist?
            $top_up_select_sql = "  SELECT top_up_id
                                    FROM " . TABLE_ORDERS_TOP_UP . " 
                                    WHERE top_up_id = '" . (int) $top_up_id . "'";
            $top_up_result_sql = tep_db_query($top_up_select_sql);
            if ($topup_result = tep_db_fetch_array($top_up_result_sql)) {
                $delivered_top_up_array['DTU']['top_up_id'][] = $topup_result['top_up_id'];
            }
            if ($delivered_top_up_array) {
                $delivered_softpin_qty = $quantity;
                $delivered_top_up_array['final_price'] = $total_item_amount;

                $log_data = array(
                    'orders_id' => $order_id,
                    'orders_products_id' => $orders_products_id,
                    'products_id' => $products_id,
                    'products_quantity' => $delivered_softpin_qty,
                    'extra_info' => json_encode($delivered_top_up_array),
                    'log_date_time' => 'now()'
                );

                tep_db_perform(TABLE_LOG_DELIVERED_PRODUCTS, $log_data);
            }
        }
    }
}

?>