<?php
class reward{
    const MAX_READ_RECORDS_FOR_TOPUP_QUEUE = ' LIMIT 20 '; // 'LIMIT  100';
    
    private $b2c_db = array(
        'link' => 'db_link'
    );
    private $reward_db = array(
        'server' => DB_PLAYNOW_SERVER, 
        'username' => DB_PLAYNOW_SERVER_USERNAME, 
        'password' => DB_PLAYNOW_SERVER_PASSWORD, 
        'database' => DB_PLAYNOW_DATABASE, 
        'link' => 'playnow_link'
    );
    private $publisher = array(),
            $publisher_game = array(),
            $publisher_game_id = array(),
            $publisher_products = array(),
            $topup_products_amount = array();
	
	function __construct() {
        tep_db_connect(
                $this->reward_db['server'], 
                $this->reward_db['username'], 
                $this->reward_db['password'], 
                $this->reward_db['database'], 
                $this->reward_db['link']) or die('Unable to connect to database server!');
	}
	
	function cron_update_reward_top_up_queue() {
        $topup_queue_array = $this->getTopUpQueueData();
		
        foreach ($topup_queue_array as $key => $data) {
            $publisher_game_info = $this->getPublisherGame($data['game_id']);
            $publishers_id = $this->getPublishersID($publisher_game_info);
            $publisher_game_id = $this->getPublisherGameID($publishers_id, $publisher_game_info[0]);
            $publisher_support_product_array = $this->getPublisherProducts($publisher_game_id);
            
            $total_top_up_array = $this->getTotalTopUpQty($data['recipient_customers_id'], $data['start_date'], $data['expiry_date'], $publisher_support_product_array);
            $total_top_up = $this->getTotalTopUp($total_top_up_array);
            $topup_queue_array[$key]['total_top_up'] = $total_top_up;
        }
        
        $this->updateTopUpQueueData($topup_queue_array);
	}
    
    private function getTotalTopUp($topup_data) {
        $total_qty = 0;
        
        foreach ($topup_data as $data) {
            $product_qty = $this->getTopUpProductAmount($data['products_id']);
            $total_qty += $data['products_good_delivered_quantity'] * $product_qty;
        }
        
        return $total_qty;
    }
    
    private function getTopUpProductAmount($product_id) {
        if (!isset($this->topup_products_amount[$product_id])) {
            $topup_amount_select_sql = "	SELECT top_up_info_value
                                            FROM " . TABLE_TOP_UP_INFO . "
                                            WHERE products_id = '" . $product_id . "'
                                                AND top_up_info_key = 'amount'";
            $topup_amount_result_sql = tep_db_query($topup_amount_select_sql, $this->b2c_db['link']);
            if ($topup_amount_row = tep_db_fetch_array($topup_amount_result_sql)) {
                $this->topup_products_amount[$product_id] = (int)$topup_amount_row['top_up_info_value'];
            } else {
                $this->topup_products_amount[$product_id] = 0;
            }
        }
        
		return $this->topup_products_amount[$product_id];
    }
    
    private function getTotalTopUpQty($customer_id, $start_date, $end_date, $support_products_array) {
        $return_array = array();
        
        $top_up_queue_select_sql = "	SELECT op.products_id, op.products_good_delivered_quantity
                                        FROM " . TABLE_ORDERS_PRODUCTS . " AS op
                                        INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu
                                            ON op.orders_products_id = otu.orders_products_id
                                        LEFT JOIN " . TABLE_ORDERS . " AS o 
                                            ON op.orders_id = o.orders_id
                                        WHERE op.products_id IN ('" . implode("','", $support_products_array) . "')
                                            AND otu.top_up_status = '3'
                                            AND otu.top_up_timestamp >= '" . $start_date . "'
                                            AND otu.top_up_timestamp <= '" . $end_date . "' 
                                            AND o.customers_id = '" . $customer_id . "'";
        $top_up_queue_result_sql = tep_db_query($top_up_queue_select_sql, $this->b2c_db['link']);
        while ($top_up_queue_row = tep_db_fetch_array($top_up_queue_result_sql)) {
            $return_array[] = $top_up_queue_row;
        }
        
		return $return_array;
    }
    
    private function getTopUpQueueData() {
        $return_array = array();
        $top_up_queue_select_sql = "SELECT *
                                    FROM " . TABLE_GAMES_TOP_UP_QUEUE . "
                                    WHERE total_top_up = -1 
                                        AND expiry_date < now()
                                    " . self::MAX_READ_RECORDS_FOR_TOPUP_QUEUE;
        $top_up_queue_result_sql = tep_db_query($top_up_queue_select_sql, $this->reward_db['link']);
        while ($top_up_queue_row = tep_db_fetch_array($top_up_queue_result_sql)) {
            $return_array[] = $top_up_queue_row;
        }
        
		return $return_array;
    }
    
    private function getPublishersID($publisher_game_array) {
        $api_id = $publisher_game_array[1];
        
        if (!isset($this->publisher[$api_id])) {
            $publisher_select_sql = "	SELECT publishers_id
                                        FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
                                        WHERE publishers_configuration_value = '" . $api_id . "'
                                            AND publishers_configuration_key = 'PUBLISHER_API_ID'";
            $publisher_result_sql = tep_db_query($publisher_select_sql, $this->b2c_db['link']);
            if ($publisher_row = tep_db_fetch_array($publisher_result_sql)) {
                $this->publisher[$api_id] = $publisher_row['publishers_id'];
            } else {
                $this->publisher[$api_id] = 0;
            }
        }
        
		return $this->publisher[$api_id];
    }
    
    private function getPublisherGame($game_id) {
        if (!isset($this->publisher_game[$game_id])) {
            $reward_publisher_game_select_sql = "	SELECT publisher_game_mapping_id, publisher_api_id
                                                    FROM " . TABLE_REWARD_PUBLISHER_GAME . "
                                                    WHERE game_id = '" . $game_id . "'
                                                    LIMIT 1";
            $reward_publisher_game_result_sql = tep_db_query($reward_publisher_game_select_sql, $this->reward_db['link']);
            if ($reward_publisher_game_row = tep_db_fetch_array($reward_publisher_game_result_sql)) {
                $this->publisher_game[$game_id] = array($reward_publisher_game_row['publisher_game_mapping_id'], $reward_publisher_game_row['publisher_api_id']);
            } else {
                $this->publisher_game[$game_id] = array('', '');
            }
        }
        
		return $this->publisher_game[$game_id];
    }
    
    private function getPublisherGameID($publisher_id, $publisher_game) {
        if (!isset($this->publisher_game_id[$publisher_id.$publisher_game])) {
            $publisher_game_select_sql = "	SELECT publishers_games_id
                                            FROM " . TABLE_PUBLISHERS_GAMES . "
                                            WHERE publishers_id = '" . $publisher_id . "'
                                                AND publishers_game = '" . $publisher_game . "'";
            $publisher_game_result_sql = tep_db_query($publisher_game_select_sql, $this->b2c_db['link']);
            if ($publisher_game_row = tep_db_fetch_array($publisher_game_result_sql)) {
                $this->publisher_game_id[$publisher_id.$publisher_game] = $publisher_game_row['publishers_games_id'];
            } else {
                $this->publisher_game_id[$publisher_id.$publisher_game] = 0;
            }
        }
        
		return $this->publisher_game_id[$publisher_id.$publisher_game];
    }
    
    private function getPublisherProducts($publisher_game_id) {
        if (!isset($this->publisher_products[$publisher_game_id])) {
            $publisher_products_select_sql = "	SELECT *
                                                FROM " . TABLE_PUBLISHERS_PRODUCTS . "
                                                WHERE publishers_games_id = '" . $publisher_game_id . "'";
            $publisher_products_result_sql = tep_db_query($publisher_products_select_sql, $this->b2c_db['link']);
            while ($publisher_products_row = tep_db_fetch_array($publisher_products_result_sql)) {
                $this->publisher_products[$publisher_game_id][] = $publisher_products_row['products_id'];
            }
        }
        
		return isset($this->publisher_products[$publisher_game_id]) ? $this->publisher_products[$publisher_game_id] : array();
    }
    
    private function updateTopUpQueueData($topup_queue_array) {
        foreach ($topup_queue_array as $data) {
            $update_sql = " UPDATE " . TABLE_GAMES_TOP_UP_QUEUE . " 
                            SET total_top_up = " . $data['total_top_up'] . "
                            WHERE game_id = '" . $data['game_id'] . "'
                                AND referer_customers_id = '" . $data['referer_customers_id'] ."'
                                AND recipient_customers_id = '" . $data['recipient_customers_id'] . "'";
            tep_db_query($update_sql, $this->reward_db['link']);
        }
    }
}
?>
