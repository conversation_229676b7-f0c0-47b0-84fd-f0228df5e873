<?php

include_once(DIR_WS_CLASSES . 'curl.php');

class slack_notification {

    public $curl_obj, $base_path;

    public function __construct() {
        $this->curl_obj = new curl();
        if (defined("SLACK_WEBHOOK_BASE_PATH")) {
            // Fix PHP 5.4 empty() not accepting constrain / function
            $this->base_path = SLACK_WEBHOOK_BASE_PATH;
        } else {
            $this->base_path = 'https://hooks.slack.com/services';
        }
    }

    public function send($url, $body) {
        $url = $this->base_path . '/' . $url;
        $header = array('Content-Type:application/json');
        $this->curl_obj->curl_request('POST', $url, $header, $body);
    }

    public function sendMessageStack($title, $message, $color = 'warning', $channel = 'DEFAULT') {
        $data = json_encode(array(
            'text' => '*' . $title . '*',
            'attachments' => array(
                array(
                    'color' => $color,
                    'text' => $message
                )
            )
        ));

        $url = $this->base_path . '/' . $channel;
        $header = array('Content-Type:application/json');
        $this->curl_obj->curl_request('POST', $url, $header, $data);
    }

}
