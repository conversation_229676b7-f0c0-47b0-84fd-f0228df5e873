<?php

/*
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
 */

/* * *****************************************************************
  Define the webserver and path parameters
  DIR_FS_* = Filesystem directories (local/physical)

  DIR_WS_* = Webserver directories (virtual/URL)
  Note: Should not has leading slash and must has ending slash
 * ****************************************************************** */
define('HTTP_SERVER', 'http://carerra.localhost/'); // eg, http://localhost - should not be empty for productive servers
define('HTTPS_SERVER', 'https://carerra.localhost/'); // eg, http://localhost - should not be empty for productive servers
define('HTTPS_CREW_SERVER', 'https://carerra.localhost/'); // eg, http://localhost - should not be empty for productive servers
define('HTTP_CATALOG_SERVER', 'http://carerra.localhost/');
define('HTTPS_CATALOG_SERVER', '');
define('ENABLE_SSL_CATALOG', 'false'); // secure webserver for catalog module
define('HTTP_COOKIE_DOMAIN', 'carerra.localhost');
define('HTTPS_COOKIE_DOMAIN', 'carerra.localhost');
define('HTTP_COOKIE_PATH', '/');
define('HTTPS_COOKIE_PATH', '/');
define('DIR_FS_DOCUMENT_ROOT', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/api/'); // where the pages are located on the server
define('DIR_WS_ADMIN', 'admin/'); // absolute path required
define('DIR_FS_ADMIN', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/admin/'); // absolute path required
define('DIR_WS_CATALOG', ''); // absolute path required
define('DIR_FS_CATALOG', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/'); // absolute path required
define('DIR_WS_IMAGES', 'images/');
define('DIR_WS_ICONS', DIR_WS_IMAGES . 'icons/');
define('DIR_WS_INCLUDES', 'includes/');
define('DIR_WS_BOXES', DIR_WS_INCLUDES . 'boxes/');
define('DIR_WS_FUNCTIONS', DIR_WS_INCLUDES . 'functions/');
define('DIR_WS_CLASSES', DIR_WS_INCLUDES . 'classes/');
define('DIR_WS_MODULES', DIR_WS_INCLUDES . 'modules/');
define('DIR_WS_LANGUAGES', DIR_WS_INCLUDES . 'languages/');
define('DIR_WS_STATISTICS', DIR_WS_INCLUDES . 'statistics/');
define('DIR_WS_CATALOG_LANGUAGES', DIR_WS_CATALOG . 'includes/languages/');
define('DIR_FS_CATALOG_LANGUAGES', DIR_FS_CATALOG . 'includes/languages/');
define('DIR_FS_CATALOG_IMAGES', DIR_FS_CATALOG . 'images/');
define('DIR_FS_CATALOG_MODULES', DIR_FS_CATALOG . 'includes/modules/');

/* * **************************
  Memcached
 * ************************** */
define('CFG_MEMCACHE_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers

/* * **************************
  Slack Notification
 * ************************** */
define('SLACK_WEBHOOK_BASE_PATH', 'https://hooks.slack.com/services');
define('SLACK_WEBHOOK_DEV_ALERT', 'TFQ825EGN/BU9TBKVFV/KW9spgc5aabL6syOzmghLEqd');
define('SLACK_WEBHOOK_DEV_DEBUG', 'TFQ825EGN/BFTT71D60/UsqxaRyM3HvV45CZX1oOfkP7');
define('SLACK_WEBHOOK_DEV_INFO', 'TFQ825EGN/BUPHVC4LE/9C449bTLcOdVpwfYQHHUT1mD');
define('SLACK_WEBHOOK_DTU', 'TFQ825EGN/BUP7W5555/SlmnEnDGwIZYV8hoP9I9ItYg');

// Crew2 Path Config
define('CREW2_PATH', 'https://dev-crew2.offgamers.com');
define('CREW2_OPEN_PATH', 'https://dev-open.offgamers.com');

// define our database connection
define('DB_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
define('DB_SERVER_USERNAME', 'root');
define('DB_SERVER_PASSWORD', 'mysql');
define('DB_DATABASE', 'offgamers');
define('USE_PCONNECT', 'false'); // use persisstent connections?
define('STORE_SESSIONS', 'mysql'); // leave empty '' for default handler or set to 'mysql'
// define our reporting database connection  
define('DB_REPORT_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
define('DB_REPORT_SERVER_USERNAME', 'root');
define('DB_REPORT_SERVER_PASSWORD', 'mysql');
define('DB_REPORT_DATABASE', 'carerra');

// define playnow database connection  
define('DB_PLAYNOW_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
define('DB_PLAYNOW_SERVER_USERNAME', 'skc_user');
define('DB_PLAYNOW_SERVER_PASSWORD', 'skc_password');
define('DB_PLAYNOW_DATABASE', 'playnow');
define('HTTP_PROXY', '');

/* * **************************
  AWS SQS
 * ************************** */
define('AWS_SQS_KEY', '');
define('AWS_SQS_SECRET', '');
define('AWS_SQS_MS_ORDER_URL', '');

define('MICRO_SERVICE_ORDER',serialize([
    'baseUrl' => 'https://staging-ms-order.offgamers.com',
    'key' => 'backend',
    'secret' => '123456'
]));

define('UNIPIN_DELAYED_DTU_PRODUCTS_CODE', serialize([]));

?>