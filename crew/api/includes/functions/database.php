<?
/*
  	$Id: database.php,v 1.1 2011/02/14 05:18:38 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

function tep_db_connect($server = DB_SERVER, $username = DB_SERVER_USERNAME, $password = DB_SERVER_PASSWORD, $database = DB_DATABASE, $link = 'db_link') {
	global $$link;
	
    if (USE_PCONNECT == 'true') {
      	$$link = mysql_pconnect($server, $username, $password);
    } else {
      	$$link = @mysql_connect($server, $username, $password, ($link != 'db_link' ? true : false));
    }
	
    if ($$link) mysql_select_db($database);
	
    return $$link;
}

function tep_db_close($link = 'db_link') {
    global $$link;
	
    return mysql_close($$link);
}

function tep_db_error($query, $errno, $error) { 
	die('<font color="#000000"><b>' . $errno . ' - ' . $error . '<br><br>' . $query . '<br><br><small><font color="#ff0000">[TEP STOP]</font></small><br><br></b></font>');
}

function tep_db_query($query, $link = 'db_link') {
    global $$link, $logger, $mysql_query_time;
	
	$s_time = microtime();
	
    if (defined('STORE_DB_TRANSACTIONS') && (STORE_DB_TRANSACTIONS == 'true')) {
      	if (!is_object($logger)) $logger = new logger;
      	$logger->write($query, 'QUERY');
    }

    $result = mysql_query($query, $$link) or tep_db_error($query, mysql_errno(), mysql_error());
	
    if (defined('STORE_DB_TRANSACTIONS') && (STORE_DB_TRANSACTIONS == 'true')) {
      	if (mysql_error()) $logger->write(mysql_error(), 'ERROR');
    }
	
	$end_time = microtime();
	$time_start = explode(' ', $s_time);
	$time_end = explode(' ', $end_time);
	
	$totaltime = ($time_end[1] + $time_end[0]) - ($time_start[1] + $time_start[0]);
	
	$mysql_query_time += $totaltime;
	/*
	if ($totaltime > 0.01) echo '<span class="redIndicator">';
	if (strstr($query, 'session') || strstr($query, 'configuration') || strstr($query, 'admin_files')) {
	} else 
		echo $query . '<br>Take Time: ' . $totaltime .'<br><br>';
	if ($totaltime > 0.01) echo '</span>';
	*/
    return $result;
}

function tep_db_perform($table, $data, $action = 'insert', $parameters = '', $link = 'db_link') {
    reset($data);
    if ($action == 'insert') {
      	$query = 'insert into ' . $table . ' (';
      	while (list($columns, ) = each($data)) {
        	$query .= $columns . ', ';
      	}
      	$query = substr($query, 0, -2) . ') values (';
      	reset($data);
      	while (list(, $value) = each($data)) {
        	switch ((string)$value) {
          		case 'now()':
            		$query .= 'now(), ';
            		break;
          		case 'null':
          		case 'NULL':
            		$query .= 'null, ';
            		break;
          		default:
            		$query .= '\'' . tep_db_input($value) . '\', ';
            		break;
        	}
      	}
      	$query = substr($query, 0, -2) . ')';
    } else if ($action == 'update') {
      	$query = 'update ' . $table . ' set ';
      	while (list($columns, $value) = each($data)) {
        	switch ((string)$value) {
          		case 'now()':
            		$query .= $columns . ' = now(), ';
            		break;
          		case 'null':
          		case 'NULL':
            		$query .= $columns .= ' = null, ';
            		break;
          		default:
            		$query .= $columns . ' = \'' . tep_db_input($value) . '\', ';
            		break;
        	}
      	}
      	$query = substr($query, 0, -2) . ' where ' . $parameters;
    }
	
    return tep_db_query($query, $link);
}

function tep_db_fetch_array($db_query) {
	return mysql_fetch_array($db_query, MYSQL_ASSOC);
}

function tep_db_fetch_row($db_query) {
	return mysql_fetch_row($db_query);
}

function tep_db_result($result, $row, $field = '') {
    return mysql_result($result, $row, $field);
}

function tep_db_num_rows($db_query) {
    return mysql_num_rows($db_query);
}

function tep_db_affected_rows($link = 'db_link') {
	global $$link;
	
	return mysql_affected_rows($$link);
}

function tep_db_data_seek($db_query, $row_number) {
    return mysql_data_seek($db_query, $row_number);
}

function tep_db_insert_id() {
	return mysql_insert_id();
}

function tep_db_free_result($db_query) {
	return mysql_free_result($db_query);
}

function tep_db_fetch_fields($db_query) {
	return mysql_fetch_field($db_query);
}

function tep_db_output($string) {
	return htmlspecialchars($string);
}

function tep_db_input($string, $link = 'db_link') {
	global $$link;
	
	if (function_exists('mysql_real_escape_string') && isset($$link)) {
		return mysql_real_escape_string($string, $$link);
	} elseif (function_exists('mysql_escape_string')) {
		return mysql_escape_string($string);
	}
	return addslashes($string);
}

function tep_db_prepare_input($string) {
	if (is_string($string)) {
		return trim(stripslashes($string));
    } else if (is_array($string)) {
      	reset($string);
      	while (list($key, $value) = each($string)) {
        	$string[$key] = tep_db_prepare_input($value);
      	}
      	return $string;
    } else {
      	return $string;
	}
}
?>