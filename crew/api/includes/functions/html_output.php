<?
/*
  	$Id: html_output.php,v 1.1 2011/02/14 05:18:38 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

////
// The HTML href link wrapper function
function tep_href_link($page = '', $parameters = '', $connection = 'NONSSL') {
	if ($page == '') {
      	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine the page link!<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
    if ($connection == 'NONSSL') {
      	$link = HTTP_SERVER . DIR_WS_ADMIN;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL == 'true') {
        	$link = HTTPS_SERVER . DIR_WS_ADMIN;
      	} else {
        	$link = HTTP_SERVER . DIR_WS_ADMIN;
      	}
    } else {
      	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
	
    if (tep_not_null($parameters)) {	      
		$link = $link . $page . '?' . $parameters . (substr($parameters,-1)=='&' ? '' : '&') . (defined('SID') ? SID : '');
    } else {
		$link = $link . $page . '?' . (defined('SID') ? SID : '');
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
    return $link;
}

function tep_catalog_href_link($page = '', $parameters = '', $connection = 'NONSSL', $add_session_id = true, $search_engine_safe = true, $use_achor = '') {
	$get_data_array = '';
	$if_seo_url = false;
	
	if ($connection == 'NONSSL') {
		$link = HTTP_CATALOG_SERVER . DIR_WS_CATALOG;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL_CATALOG == 'true') {
        	$link = HTTP_CATALOG_SERVER . DIR_WS_CATALOG;
        	//$link = HTTPS_CATALOG_SERVER . DIR_WS_CATALOG;
      	} else {
        	$link = HTTP_CATALOG_SERVER . DIR_WS_CATALOG;
      	}
	} else if ($connection == 'FOLLOW') {
       	$link = '/'.DIR_WS_CATALOG;
	} else {
		die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
    
    if ( (SEARCH_ENGINE_FRIENDLY_URLS == 'true') && ($search_engine_safe == true) ) {
    	include_once(DIR_WS_CLASSES . 'seo.php'); //seo url class file
    	$seo = new seo_url($page, $parameters);
    	$parameters = $seo->parameters;
	    $if_seo_url = $seo->if_seo_url;
	    $get_data_array = $seo->get_data_array;
	}
	
	if (tep_not_null($parameters)) {
    	if ($if_seo_url) {
    		$link .= tep_output_string($parameters) . FILENAME_URL_EXTENSION; // *.ogm
    		$separator = '?';
    	} else {
	      	$link .= $page . '?' . tep_output_string($parameters);
	      	$separator = '&';
	   	}
    } else {
      	$link .= $page;
      	$separator = '?';
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
	if (tep_not_null($get_data_array)) {
		$link .= $separator . $get_data_array;
		$separator = '&';
	}
	
    if (tep_not_null($use_achor)) {
    	$link .= $use_achor;
	}
	
    return $link;
}

function tep_affiliate_href_link($page = '', $parameters = '', $connection = 'NONSSL') {
	if (!tep_not_null($page)) {
      	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine the page link!<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
    if ($connection == 'NONSSL') {
      	$link = HTTP_AFFILIATE_SERVER . DIR_WS_AFFILIATE;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL == 'true') {
        	$link = HTTPS_AFFILIATE_SERVER . DIR_WS_AFFILIATE;
      	} else {
        	$link = HTTP_AFFILIATE_SERVER . DIR_WS_AFFILIATE;
      	}
    } else {
      	die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
	
    if (tep_not_null($parameters)) {
		$link = $link . $page . '?' . $parameters . (substr($parameters,-1)=='&' ? '' : '&') . (defined('SID') ? SID : '');
    } else {
		$link = $link . $page . '?' . (defined('SID') ? SID : '');
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
    return $link;
}

function tep_get_country_list($name, $selected = '', $parameters = '') {
    $countries = tep_get_countries_list();
	
	$countries_array[] = array('id' => '', 'text' => PULL_DOWN_DEFAULT);
	
    for ($i=0, $n=sizeof($countries); $i<$n; $i++) {
      	$countries_array[] = array('id' => $countries[$i]['countries_id'], 'text' => $countries[$i]['countries_name']);
    }
	
    return tep_draw_pull_down_menu($name, $countries_array, $selected, $parameters);
}


function tep_supplier_href_link($page = '', $parameters = '', $connection = 'NONSSL') {
	if ($connection == 'NONSSL') {
		$link = HTTP_SUPPLIER_SERVER . DIR_WS_SUPPLIER;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL_SUPPLIER == 'true') {
        	$link = HTTPS_SUPPLIER_SERVER . DIR_WS_SUPPLIER;
      	} else {
        	$link = HTTP_SUPPLIER_SERVER . DIR_WS_SUPPLIER;
      	}
	} else {
		die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
    
    if ($parameters == '') {
      	$link .= $page;
    } else {
      	$link .= $page . '?' . $parameters;
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
    return $link;
}

function tep_cnbb_href_link($page = '', $parameters = '', $connection = 'NONSSL') {
	if ($connection == 'NONSSL') {
		$link = HTTP_CNBB_SERVER . DIR_WS_CNBB;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL_CNBB == 'true') {
        	$link = HTTPS_CNBB_SERVER . DIR_WS_CNBB;
      	} else {
        	$link = HTTP_CNBB_SERVER . DIR_WS_CNBB;
      	}
	} else {
		die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }
    
    if ($parameters == '') {
      	$link .= $page;
    } else {
      	$link .= $page . '?' . $parameters;
    }
	
    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);
	
    return $link;
}

function tep_upload_href_link($page = '', $parameters = '', $connection = 'NONSSL') {
	if ($connection == 'NONSSL') {
		$link = HTTP_UPLOAD_SERVER . DIR_WS_UPLOAD;
    } else if ($connection == 'SSL') {
      	if (ENABLE_SSL_UPLOAD == 'true') {
        	$link = HTTPS_UPLOAD_SERVER . DIR_WS_UPLOAD;
      	} else {
        	$link = HTTP_UPLOAD_SERVER . DIR_WS_UPLOAD;
      	}
	} else {
		die('</td></tr></table></td></tr></table><br><br><font color="#ff0000"><b>Error!</b></font><br><br><b>Unable to determine connection method on a link!<br><br>Known methods: NONSSL SSL<br><br>Function used:<br><br>tep_upload_href_link(\'' . $page . '\', \'' . $parameters . '\', \'' . $connection . '\')</b>');
    }

    if ($parameters == '') {
      	$link .= $page;
    } else {
      	$link .= $page . '?' . $parameters;
    }

    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);

    return $link;
}

////
// The HTML image wrapper function
function tep_image($src, $alt = '', $width = '', $height = '', $params = '') {
	$image = '<img src="' . $src . '" border="0" alt="' . $alt . '"';
    
    if ($alt) {
      	$image .= ' title=" ' . $alt . ' "';
    }
    if ($width) {
      	$image .= ' width="' . $width . '"';
    }
    if ($height) {
      	$image .= ' height="' . $height . '"';
    }
    if ($params) {
      	$image .= ' ' . $params;
    }
    $image .= '>';
	
    return $image;
}

////
// The HTML form submit button wrapper function
// Outputs a button in the selected language
function tep_image_submit($image, $alt = '', $parameters = '', $own_path='') {
	global $language;
	
    $image_submit = '<input type="image" src="' . tep_output_string(($own_path) ? $own_path.$image : DIR_WS_LANGUAGES . $language . '/images/buttons/' . $image) . '" border="0" alt="' . tep_output_string($alt) . '"';
	
    if (tep_not_null($alt)) $image_submit .= ' title=" ' . tep_output_string($alt) . ' "';
	
	if (tep_not_null($parameters)) {
		$image_submit .= ' ' . $parameters;
		
		if (strpos($parameters, 'onClick') === false) {
			//$image_submit .= " onClick=\"this.disabled=true; this.form.submit();\" ";
		}
	} else {
		//$image_submit .= " onClick=\"this.disabled=true; this.form.submit();\" ";
	}
	
    $image_submit .= '>';
	
    return $image_submit;
}

function tep_submit_button($name='', $alt = '', $parameters = '', $css_class = 'generalBtn', $btn_active = true) {
   	$submit_html = '<input type="SUBMIT" value="'.$name.'" class="'.$css_class.'" ';
	
    if (tep_not_null($alt)) $submit_html .= ' title=" ' . tep_output_string($alt) . ' "';
	
    if (tep_not_null($parameters)) $submit_html .= ' ' . $parameters;
    
	$submit_html .= ($btn_active == true ? ' onMouseOver="this.className=\''.$css_class.'Over'.'\'"' : '') .' onMouseOut="this.className=\''.$css_class.'\'" ';
    
    $submit_html .= '>';
	
    return $submit_html;
}

function tep_button($name, $alt = '', $url='', $parameters = '', $css_class = 'generalBtn', $btn_active = true) {
   	$button_html = '<input type="BUTTON" value="'.$name.'" class="'.$css_class.'" ';
	
    if (tep_not_null($alt)) $button_html .= ' title=" ' . tep_output_string($alt) . ' "';
	
    if (tep_not_null($parameters)) $button_html .= ' ' . $parameters;
	
	$button_html .= ($btn_active == true ? ' onMouseOver="this.className=\''.$css_class.'Over'.'\'"' : '') .' onMouseOut="this.className=\''.$css_class.'\'" ';
    
    if (tep_not_null($url))	$button_html .= 'onClick="window.location=\''.$url.'\'"';
    
    $button_html .= '>';
	
    return $button_html;
}

////
// Draw a 1 pixel black line
function tep_black_line() {
    return tep_image(DIR_WS_IMAGES . 'pixel_black.gif', '', '100%', '1');
}

////
// Output a separator either through whitespace, or with an image
function tep_draw_separator($image = 'pixel_black.gif', $width = '100%', $height = '1') {
	return tep_image(DIR_WS_IMAGES . $image, '', $width, $height);
}

////
// Output a function button in the selected language
function tep_image_button($image, $alt = '', $params = '') {
	global $language;
	//$params .= ' class="inputButton"';
    return tep_image(DIR_WS_LANGUAGES . $language . '/images/buttons/' . $image, $alt, '', '', $params);
}

////
// javascript to dynamically update the states/provinces list when the country is changed
// TABLES: zones
function tep_js_zone_list($country, $form, $field, $selection_type='') {
	$countries_query = tep_db_query("select distinct zone_country_id from " . TABLE_ZONES . " order by zone_country_id");
    $num_country = 1;
    $output_string = '';
    while ($countries = tep_db_fetch_array($countries_query)) {
      	if ($num_country == 1) {
        	$output_string .= '  if (' . $country . ' == "' . $countries['zone_country_id'] . '") {' . "\n";
      	} else {
        	$output_string .= '  } else if (' . $country . ' == "' . $countries['zone_country_id'] . '") {' . "\n";
      	}
		
      	$states_query = tep_db_query("select zone_name, zone_id from " . TABLE_ZONES . " where zone_country_id = '" . $countries['zone_country_id'] . "' order by zone_name");
		
      	$num_state = 1;
      	while ($states = tep_db_fetch_array($states_query)) {
        	if ($num_state == '1') {
        		if ($selection_type == 'multiple')
        			$output_string .= '    ' . $form . '.elements[\'' . $field . '\'].options[0] = new Option("' . PLEASE_SELECT . '", "0");' . "\n";
        		else
        			$output_string .= '    ' . $form . '.' . $field . '.options[0] = new Option("' . PLEASE_SELECT . '", "0");' . "\n";
        	}
        	if ($selection_type == 'multiple')
        		$output_string .= '    ' . $form . '.elements[\'' . $field . '\'].options[' . $num_state . '] = new Option("' . $states['zone_name'] . '", "' . $states['zone_id'] . '");' . "\n";
        	else
        		$output_string .= '    ' . $form . '.' . $field . '.options[' . $num_state . '] = new Option("' . $states['zone_name'] . '", "' . $states['zone_id'] . '");' . "\n";
        	
        	$num_state++;
      	}
      	$num_country++;
	}
    $output_string .= '  } else {' . "\n" .
                      ($selection_type == 'multiple' ? ('    ' . $form . '.elements[\'' . $field . '\'].options[0] = new Option("' . TYPE_BELOW . '", "0");' . "\n") : ('    ' . $form . '.' . $field . '.options[0] = new Option("' . TYPE_BELOW . '", "0");' . "\n") ).
                      '  }' . "\n";
	
    return $output_string;
}

////
// Output a form
function tep_draw_form($name, $action, $parameters = '', $method = 'post', $params = '') {
	$form = '<form name="' . tep_output_string($name) . '" action="';
    if (tep_not_null($parameters)) {
      	$form .= tep_href_link($action, $parameters);
    } else if (tep_not_null($action)) {
      	$form .= tep_href_link($action);
    }
    $form .= '" method="' . tep_output_string($method) . '"';
    if (tep_not_null($params)) {
      	$form .= ' ' . $params;
    }
    $form .= '>';
	
	if (SID && strtolower($method) == 'get') $form .= tep_draw_hidden_field(tep_session_name(), tep_session_id());
	
    return $form;
}

////
// Output a form input field
function tep_draw_input_field($name, $value = '', $parameters = '', $required = false, $type = 'text', $reinsert_value = true) {
    $field = '<input type="' . tep_output_string($type) . '" name="' . tep_output_string($name) . '"';
	
    if (isset($GLOBALS[$name]) && ($reinsert_value == true) && is_string($GLOBALS[$name])) {
      	$field .= ' value="' . tep_output_string(stripslashes($GLOBALS[$name])) . '"';
    } else if (tep_not_null($value)) {
      	$field .= ' value="' . tep_output_string($value) . '"';
    }
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    if ($required == true) $field .= TEXT_FIELD_REQUIRED;
	
    return $field;
}

////
// Output a form password field
function tep_draw_password_field($name, $value = '', $required = false) {
	$field = tep_draw_input_field($name, $value, 'maxlength="40"', $required, 'password', false);
	
    return $field;
}

////
// Output a form filefield
function tep_draw_file_field($name, $parameters = '', $required = false) {
    $field = tep_draw_input_field($name, '', $parameters, $required, 'file');
	
    return $field;
}

//Admin begin
////
// Output a selection field - alias function for tep_draw_checkbox_field() and tep_draw_radio_field()
//  function tep_draw_selection_field($name, $type, $value = '', $checked = false, $compare = '') {
//    $selection = '<input type="' . tep_output_string($type) . '" name="' . tep_output_string($name) . '"';
//
//    if (tep_not_null($value)) $selection .= ' value="' . tep_output_string($value) . '"';
//
//    if ( ($checked == true) || (isset($GLOBALS[$name]) && is_string($GLOBALS[$name]) && ($GLOBALS[$name] == 'on')) || (isset($value) && isset($GLOBALS[$name]) && (stripslashes($GLOBALS[$name]) == $value)) || (tep_not_null($value) && tep_not_null($compare) && ($value == $compare)) ) {
//      $selection .= ' CHECKED';
//    }
//
//    $selection .= '>';
//
//    return $selection;
//  }
//
////
// Output a form checkbox field
//  function tep_draw_checkbox_field($name, $value = '', $checked = false, $compare = '') {
//    return tep_draw_selection_field($name, 'checkbox', $value, $checked, $compare);
//  }
//
////
// Output a form radio field
//  function tep_draw_radio_field($name, $value = '', $checked = false, $compare = '') {
//    return tep_draw_selection_field($name, 'radio', $value, $checked, $compare);
//  }
////
// Output a selection field - alias function for tep_draw_checkbox_field() and tep_draw_radio_field()
function tep_draw_selection_field($name, $type, $value = '', $checked = false, $compare = '', $parameter = '') {
    $selection = '<input type="' . $type . '" name="' . $name . '"';
    if ($value != '') {
      	$selection .= ' value="' . $value . '"';
    }
    if ( ($checked == true) || ( isset($GLOBALS[$name]) && is_string($GLOBALS[$name]) && ( ($GLOBALS[$name] == 'on') || (isset($value) && (stripslashes($GLOBALS[$name]) == $value))) ) || ($value && ($value == $compare)) ) {
    //if ( ($checked == true) || ($GLOBALS[$name] === 'on') || ($value && ($GLOBALS[$name] == $value)) || ($value && ($value == $compare)) ) {
      	$selection .= ' CHECKED';
    }
    if ($parameter != '') {
      	$selection .= ' ' . $parameter;
    }   
    $selection .= '>';
	
    return $selection;
}

////
// Output a form checkbox field
function tep_draw_checkbox_field($name, $value = '', $checked = false, $compare = '', $parameter = '') {
	return tep_draw_selection_field($name, 'checkbox', $value, $checked, $compare, $parameter);
}

////
// Output a form radio field
function tep_draw_radio_field($name, $value = '', $checked = false, $compare = '', $parameter = '') {
    return tep_draw_selection_field($name, 'radio', $value, $checked, $compare, $parameter);
}
//Admin end

////
// Output a form textarea field
function tep_draw_textarea_field($name, $wrap, $width, $height, $text = '', $parameters = '', $reinsert_value = true) {
	$field = '<textarea name="' . tep_output_string($name) . '" wrap="' . tep_output_string($wrap) . '" cols="' . tep_output_string($width) . '" rows="' . tep_output_string($height) . '"';
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    if ( (isset($GLOBALS[$name])) && ($reinsert_value == true) ) {
      	$field .= stripslashes($GLOBALS[$name]);
    } else if (tep_not_null($text)) {
      	$field .= $text;
    }
	
    $field .= '</textarea>';
	
    return $field;
}

////
// Output a form hidden field
function tep_draw_hidden_field($name, $value = '', $parameters = '') {
    $field = '<input type="hidden" name="' . tep_output_string($name) . '"';
	
    if (tep_not_null($value)) {
      	$field .= ' value="' . tep_output_string($value) . '"';
    } else if (isset($GLOBALS[$name]) && is_string($GLOBALS[$name])) {
      	$field .= ' value="' . tep_output_string(stripslashes($GLOBALS[$name])) . '"';
    }
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    return $field;
}

////
// Output a form pull down menu
function tep_draw_pull_down_menu($name, $values, $default = '', $parameters = '', $required = false) {
	$field = '<select name="' . tep_output_string($name) . '"';
	
    if (tep_not_null($parameters)) $field .= ' ' . $parameters;
	
    $field .= '>';
	
    if (empty($default) && isset($GLOBALS[$name])) {
    	$default = stripslashes($GLOBALS[$name]);
    }
    
    if (!is_array($default) && strcmp($default, (int)$default) === 0) {
    	$default = (int)$default;
    }
    
    $selected = false;
    if (is_array($values)) {
	    for ($i=0, $n=sizeof($values); $i<$n; $i++) {
	    	if (isset($values[$i]['type']) && $values[$i]['type'] == 'optgroup') {
	    		$field .= '<optgroup label="'.tep_output_string($values[$i]['text'], array('"' => '&quot;', '\'' => '&#039;', '<' => '&lt;', '>' => '&gt;')).'"></optgroup>';
	    	} else {
		      	$field .= '<option value="' . tep_output_string($values[$i]['id']) . '"';
		      	if (strcmp($values[$i]['id'], (int)$values[$i]['id']) === 0) {
			    	$values[$i]['id'] = (int)$values[$i]['id'];
			    }
			    
			    if (is_array($default)) {
			    	if (in_array($values[$i]['id'], $default))	$field .= ' SELECTED';
			    } else if ($default === $values[$i]['id'] && !$selected) {
		      		$field .= ' SELECTED';
		        	$selected = true;
		      	}
		      	
				if (isset($values[$i]['param']) && tep_not_null($values[$i]['param']))	$field .= ' ' . $values[$i]['param'];
		      	$field .= '>' . tep_output_string($values[$i]['text'], array('"' => '&quot;', '\'' => '&#039;', '<' => '&lt;', '>' => '&gt;')) . '</option>';
			}
	    }
	}
    $field .= '</select>';
	
    if ($required == true) $field .= TEXT_FIELD_REQUIRED;
	
    return $field;
}

function tep_draw_js_select_boxes($name, $from_values, $to_values, $parameters = '', $type = '1', $required = false) {
	$field = '<table border=0 cellspacing="0" cellpadding="2">
				<tr>
					<td>';
	
	$sel_from_name = $name . '_from[]';
	$sel_to_name = $name . '_to[]';
	
	$field .= tep_draw_pull_down_menu($sel_from_name, $from_values, '', ' id="'.$sel_from_name.'" multiple onDblClick="moveSelectedOptions(document.getElementById(\''.$sel_from_name.'\'), document.getElementById(\''.$sel_to_name.'\'), true)"' . $parameters);
	
	$field .= '		</td>
					<td valign=middle align=center>' .
						tep_button('&gt;&gt;', '', '', 'name="right" style="width:60px;" onClick="moveAllOptions(document.getElementById(\''.$sel_from_name.'\'), document.getElementById(\''.$sel_to_name.'\'), true); return false;"', 'inputButton') . '<br><br>' .
						tep_button('&gt;', '', '', 'name="right" style="width:60px;" onClick="moveSelectedOptions(document.getElementById(\''.$sel_from_name.'\'), document.getElementById(\''.$sel_to_name.'\'), true);return false;"', 'inputButton') . '<br><br>' .
						tep_button('&lt;', '', '', 'name="left" style="width:60px;" onClick="moveSelectedOptions(document.getElementById(\''.$sel_to_name.'\'), document.getElementById(\''.$sel_from_name.'\'), true); return false;"', 'inputButton') . '<br><br>' .
						tep_button('&lt;&lt;', '', '', 'name="left" style="width:60px;" onClick="moveAllOptions(document.getElementById(\''.$sel_to_name.'\'), document.getElementById(\''.$sel_from_name.'\'), true); return false;"', 'inputButton') . '
					</td>
					<td>';
	
	$field .= tep_draw_pull_down_menu($sel_to_name, $to_values, '', ' id="'.$sel_to_name.'" multiple onDblClick="moveSelectedOptions(document.getElementById(\''.$sel_to_name.'\'), document.getElementById(\''.$sel_from_name.'\'), true)"' . $parameters);
	
	$field .= '		</td>
				</tr>
			  </table>';
	
	return $field;
}

function tep_draw_comment_select($field_name="commentID", $filename) {
	$result = tep_db_query("SELECT orders_comments_id, orders_comments_title FROM " . TABLE_ORDERS_COMMENTS . " WHERE orders_comments_status='1' AND orders_comments_filename ='" . tep_db_input($filename) . "' ORDER BY orders_comments_sort_order;");
	
	echo '<select name="'.$field_name.'" onchange="setOrderComment(this)" SIZE="7">';
	echo "<option value='0' selected>" . TEXT_SELECT_PREDEFINED_COMMENT . "</option>";
	
	while ($row = tep_db_fetch_array($result)) {
		echo "<option value='$row[orders_comments_id]'>" .  $row['orders_comments_title']  . "</option>";	
	}	
	echo "</select>";
}

function tep_draw_date_box($name, $from_date, $period, $default='') {
	$date_array = array();
	list($y, $m, $d) = explode('-', $from_date);
	$m = (int)$m;
	$d = (int)$d;
	
	$month_day = date('t', mktime(0, 0, 0, $m, $d, $y));
	
	$days_this_month = ($month_day - $d) + 1;
	$days_next_month = $period - $days_this_month;
	
	$current_month_days_loop = $d + ($period > $days_this_month ? $days_this_month : $period);
	for ($day_cnt=(int)$d; $day_cnt <= $current_month_days_loop; $day_cnt++) {
		$date_array[] = array('id' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt), 'text' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt));
	}
	
	while ($days_next_month > 0) {
		$m++;
		if ($m > 12) {
			$m = 1;
			$y++;
		}
		
		$month_day = date('t', mktime(0, 0, 0, $m, $d, $y));
		
		$month_total_days = ($days_next_month > $month_day) ? $month_day : $days_next_month;
		for ($day_cnt=1; $day_cnt <= $month_total_days; $day_cnt++) {
			$date_array[] = array('id' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt), 'text' => $y . '-' . ((int)$m < 10 ? '0'.(int)$m : (int)$m) . '-' . ($day_cnt < 10 ? '0'.$day_cnt : $day_cnt));
		}
		
		$days_next_month -= $month_day;
	}
	
	return tep_draw_pull_down_menu($name, $date_array, $default, 'onChange=""');
}

function tep_draw_date_selection_boxes($month='', $day='', $year='', $display_lastest_value=false) {
	$month_array = array( array('id' => '', 'text' => 'Month') );
	$month_array[] = array('id' => '01', 'text' => 'January');
	$month_array[] = array('id' => '02', 'text' => 'February');
	$month_array[] = array('id' => '03', 'text' => 'March');
	$month_array[] = array('id' => '04', 'text' => 'April');
	$month_array[] = array('id' => '05', 'text' => 'May');
	$month_array[] = array('id' => '06', 'text' => 'June');
	$month_array[] = array('id' => '07', 'text' => 'July');
	$month_array[] = array('id' => '08', 'text' => 'August');
	$month_array[] = array('id' => '09', 'text' => 'September');
	$month_array[] = array('id' => '10', 'text' => 'October');
	$month_array[] = array('id' => '11', 'text' => 'November');
	$month_array[] = array('id' => '12', 'text' => 'December');
	
	$day_array = array( array('id' => '', 'text' => 'Day') );
	for ($d_i=1; $d_i <= 31; $d_i++) {
		$day_str = sprintf('%02d', $d_i);
		$day_array[] = array('id' => $day_str, 'text' => $day_str);
	}
	
	$dob = tep_draw_pull_down_menu('dob_month', $month_array, $month, 'id="month"') . "&nbsp;&nbsp;" . tep_draw_pull_down_menu('dob_day', $day_array, $day, 'id="day"') . "&nbsp; , &nbsp;" . tep_draw_input_field('dob_year', $year, 'size="4" maxlength="4"', 'text', $display_lastest_value ? true : false);
	
	return $dob;
}

function tep_switch_image($status, $onURL, $offURL, $parameters = '') {
	$status = (bool)$status;
	
	if ($status) {
		echo tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="'.$offURL.'"'.$parameters.'>' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
	} else {
		echo '<a href="'.$onURL.'"'.$parameters.'>'.tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10);
	}
}

function tep_3_switch_image($status, $onURL, $neutralURL, $offURL) {
	switch($status) {
		case 0:
			$switch_html = '<a href="'.$onURL.'">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '</a>&nbsp;&nbsp;' . '<a href="'.$neutralURL.'">' . tep_image(DIR_WS_IMAGES . 'icon_status_yellow_light.gif', IMAGE_ICON_STATUS_YELLOW, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_red.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10);
			break;
		case 1:
			$switch_html = '<a href="'.$onURL.'">' . tep_image(DIR_WS_IMAGES . 'icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image(DIR_WS_IMAGES . 'icon_status_yellow.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="'.$offURL.'">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
			break;
		case 2:
			$switch_html = tep_image(DIR_WS_IMAGES . 'icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="'.$neutralURL.'">' . tep_image(DIR_WS_IMAGES . 'icon_status_yellow_light.gif', IMAGE_ICON_STATUS_YELLOW, 10, 10) . '</a>&nbsp;&nbsp;<a href="'.$offURL.'">' . tep_image(DIR_WS_IMAGES . 'icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
			break;
	}
	
	return $switch_html;
}

function tep_generate_pool_dropdown($selected = 0, $ref_id = 0, $static = false) {
	if ($static) {
		if ($ref_id == 0 )
			$sql = "select * from data_pool;";
		else
			$sql = "select data_pool.data_pool_id,data_pool.data_pool_description from data_pool, data_pool_ref 
					where data_pool_ref.data_pool_ref_id = '$ref_id' 
					and data_pool_ref.data_pool_id = data_pool.data_pool_id;";	
			$result = tep_db_query($sql);
			
			if ($row = tep_db_fetch_array($result))		
				$html = $row['data_pool_description'];
			else
				$html = "";
	} else {
		if($ref_id == 0)
			$sql = "select * from data_pool where data_pool_class!='root';";	
		else
			$sql = "select data_pool.data_pool_id,data_pool.data_pool_description from data_pool, data_pool_ref 
					where data_pool_ref.data_pool_ref_id = '$ref_id' 
					and data_pool_ref.data_pool_id != data_pool.data_pool_id and trim(data_pool.data_pool_input_field) != '';";	
			$result =tep_db_query($sql);
			
			if (tep_db_num_rows($result) == 0 && $ref_id > 0) {
				$sql = "select * from data_pool where data_pool_class!='root';";		
				$result =tep_db_query($sql);		
			}
			
			$html = "<select name='data_pool'>";
			
			while($row = tep_db_fetch_array($result)) {			
				if ($selected == $row['data_pool_id'])
					$html.="<option value='".$row['data_pool_id']."' selected>".$row['data_pool_description']."</option>";
				else
					$html.="<option value='".$row['data_pool_id']."'>".$row['data_pool_description']."</option>";
			}
			
			$html.= "</select>";
	}
	
	return $html;
}

function tep_html_datapool_tree($id, $name="Untitled", $linking=false) {
	$dataArray = tep_get_datapool_tree_array($id);
	
	$html = "";	
	
	if ($linking) {
		global $assigned_links, $brackets_groups_id;			
	}	
	
	foreach ($dataArray as $a) {
		tep_html_datapool_subtree($a, $html, $linking);
		
		$a['path'] = urlencode($a['path']);
		if ($linking)
			$html =  'aux'.$a['id'].' = insFld('.($a["parent_id"] == 0 ? 'foldersTree' : 'template'.$id).',gFld("'.tep_sanitize_string($a['name']).'",""));'."\n\n".'aux'.$a['id'].'._readonly = 0;'."\n\n".$html;
		else
			$html =  'aux'.$a['id'].' = insFld('.($a["parent_id"] == 0 ? 'foldersTree' : 'template'.$id).',gFld("'.tep_sanitize_string($a['name']).' [<a href=\"'.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action'))."action=edit&ref_id=$a[ref_id]&id=$a[id]&path=$a[path]").'\">Edit</a>] [<a href=\"'.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action'))."action=brackets&ref_id=$a[ref_id]&id=$a[id]&path=$a[path]").'\">Add/Edit Brackets</a>]",""));'."\n\n".'aux'.$a['id'].'._readonly = 0;'."\n\n".$html;
	}
	
	return $html;	
}

function tep_html_datapool_subtree($dataArray, &$html, $linking=false) {
	$hasBrackets = false;
	
	if ($linking) {
		global $assigned_links,$brackets_groups_id;			
	}
	
	foreach ($dataArray['child'] as $a) {
		tep_html_datapool_subtree($a, $html, $linking);
		$a['path'] = urlencode($a['path']);
		// 	TODO: No need check for bracket
		//$hasBrackets = tep_has_brackets((int)$a['id']);
		
		if (empty($a['child'])) {
			$a['name'] = htmlentities($a['name'], ENT_QUOTES);
			
			if ($linking) {
				// Since i dont have any kids, i can add brackets to myself, add a checkbox here
				if (in_array($a['id'],$assigned_links))
					$checked = true;
				else
					$checked = false;
				
				//	Get their conflict string					
				$res_dups = tep_db_query("SELECT * from ".TABLE_BRACKETS_GROUPS_TO_LEVEL_TAGS." as a where a.data_pool_level_tags_id='".$a['id']."' and a.brackets_groups_id!='".$brackets_groups_id."';");	
				
				$count_records = 0;
				if (tep_db_num_rows($res_dups)) {
					$conflictString = '&nbsp;[<font color="red">Assigned to: ';
					
					while ($res_dups_row = tep_db_fetch_array($res_dups)) {
						$count_records++;
						$temp_name_res = tep_db_query("SELECT * from ".TABLE_BRACKETS_GROUPS." where brackets_groups_id='$res_dups_row[brackets_groups_id]';");
						$temp_name_row = tep_db_fetch_array($temp_name_res);
						
						if ($count_records == tep_db_num_rows($res_dups))
							$conflictString.= "".$temp_name_row['brackets_groups_name'];
						else
							$conflictString.= "".$temp_name_row['brackets_groups_name'].", ";
					}
					
					$conflictString.= "</font>]";
				} else {
					$conflictString = "";	
				}
				
				$html =  'aux'.$a['id'].' = insFld(aux'.$dataArray['id'].',gFld(\''.tep_draw_checkbox_field('SelectedItem[]',$a['id'],$checked)."&nbsp;".$a['name'].$conflictString.'\',""));'."\n".'aux'.$a['id'].'._readonly = 0;'."\n\n".$html;
			} else {
				$html =  'aux'.$a['id'].' = insFld(aux'.$dataArray['id'].',gFld("'.$a['name'].' [<a href=\"'.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action'))."action=edit&ref_id=$a[ref_id]&id=$a[id]&path=$a[path]").'\">Edit</a>]",""));'."\n".'aux'.$a['id'].'._readonly = 0;'."\n\n".$html;
			}
		} else {
			if ($linking)
				$html =  'aux'.$a['id'].' = insFld(aux'.$dataArray['id'].',gFld("'.$a['name'].'",""));'."\n".'aux'.$a['id'].'._readonly = 0;'."\n\n".$html;
			else
				$html =  'aux'.$a['id'].' = insFld(aux'.$dataArray['id'].',gFld("'.$a['name'].' [<a href=\"'.tep_href_link(FILENAME_DATA_POOL,tep_get_all_get_params(array('ref_id','id','action'))."action=edit&ref_id=$a[ref_id]&id=$a[id]&path=$a[path]").'\">Edit</a>]",""));'."\n".'aux'.$a['id'].'._readonly = 0;'."\n\n".$html;
		}
	}
}

function tep_html_datapool_tree_dropdown($id,$name="Untitled") {
	$dataArray = tep_get_datapool_tree_array($id,false);
	
	foreach ($dataArray as $a) {
		$html.="<option value='".$a['id']."'>".$a['name']."</option>";
		tep_html_datapool_subtree_dropdown($a,$html);
	}
	
	$html ="<option value='0'>--- Import Selections ---</option>".$html;
	$html = "<select name='import_selection' onChange='import_selections(this)'>".$html."</select>";
	
	return $html;	
}

function tep_html_datapool_subtree_dropdown($dataArray,&$html,$spacing="") {
	$spacing = $spacing."__";
	
	foreach ($dataArray['child'] as $a) {
		if ($a['child'] == array())
			$html.="<option value='-1'>".$spacing.$a['name']."</option>";
		else
			$html.="<option value='".$a['id']."'>".$spacing.$a['name']."</option>";
		
		tep_html_datapool_subtree_dropdown($a,$html,$spacing);	
	}
}

function tep_display_category_path($path, $id, $id_type='catalog', $safe_quote=true) {
	$cat_display_level_setting = '';
	
	$cat_cfg_array = tep_get_cfg_setting($id, $id_type, 'BUYBACK_SUPPLIER_DISPLAY_LEVELS');
	$cat_display_level_setting = $cat_cfg_array['BUYBACK_SUPPLIER_DISPLAY_LEVELS'];
	
	if (!tep_not_null($cat_display_level_setting))	return $path;
	
	$level_array = array();
	
	$level_array = explode(',', $cat_display_level_setting);
	$path_array = explode('>', $path);
	$ret = array();
	
	for ($i=0; $i < count($level_array); $i++) {
		$level_array[$i] = trim($level_array[$i]);
		$level_array[$i] = (int)$level_array[$i];
		if ($level_array[$i] > 0) {
			$index = $level_array[$i]-1;
			if (trim($path_array[$index]) != '')	$ret[] = trim($path_array[$index]);
		}
	}
	
	return $safe_quote ? implode(" &gt; ",$ret) : implode(" > ",$ret);
}

function tep_chunk_string ($field_value, $field_type="string", $separator='>', $str_section='all', $result_tpl='1', $safe_quote=true) {
	/********************************************************
		NOTE: $field_value (Pass the id of the specified field in $field_type or straight away pass the string if $field_type="string")
			  $field_type (Indicate which field to looking for the string)
			  $separator (Operator used to separate the string, e.g. '>')
			  $result_tpl (The format which determine how the result to be)
			  $safe_quote (Set to true if the result requires safe html quote)
	********************************************************/
	
	$string_to_parse = $field_value;
	$chunk_string_array = array();
	
	switch($field_type) {
		case "products_id":
			$products_cat_path_select_sql = "SELECT products_cat_path FROM " . TABLE_PRODUCTS . " WHERE products_id ='" . tep_db_input($field_value) . "'";
			$products_cat_path_result_sql = tep_db_query($products_cat_path_select_sql);
			$products_cat_path_row = tep_db_fetch_array($products_cat_path_result_sql);
			
			$string_to_parse = $products_cat_path_row['products_cat_path'];
			break;
	}
	
	if (tep_not_null($string_to_parse)) {
		$string_to_parse_array = explode($separator, $string_to_parse);
		$str_section_array = array();
		
		if ($str_section != "all") {
			$str_section_array = explode(',', $str_section);
		}
		
		if ($result_tpl == "1") {
			for ($count = 0; $count < count($string_to_parse_array); $count++) {
				if (in_array(($count+1), $str_section_array) || $str_section == "all") {
					$string_parse = "";
					$str_section_before_parse = $string_to_parse_array[$count];
					$words_array = explode(" ", $str_section_before_parse);
					
					foreach ($words_array as $word_data) {
						$string_parse .= $word_data{0};
						for ($str_length_count = 1; $str_length_count < strlen($word_data); $str_length_count++) {
							if (ctype_upper($word_data{$str_length_count}) || $word_data{$str_length_count} == " ") {
								$string_parse .= $word_data{$str_length_count};
							}
						}
					}
					$chunk_string_array[] = $string_parse;
				}
			}
		}
		return $safe_quote ? implode(" &gt; ",$chunk_string_array) : implode(" > ",$chunk_string_array);
	}
}

function tep_encode_to_charset($in_str, $charset) { 
	$out_str = $in_str;
	
   	if ($out_str && $charset) {
		// define start delimimter, end delimiter and spacer
       	$end = "?=";
       	$start = "=?" . $charset . "?B?";
       	$spacer = $end . "\r\n " . $start;
		
       	// determine length of encoded text within chunks
       	// and ensure length is even
       	$length = 75 - strlen($start) - strlen($end);
       	$length = floor($length/2) * 2;
		
       	// encode the string and split it into chunks
       	// with spacers after each chunk
       	$out_str = base64_encode($out_str);
       	$out_str = chunk_split($out_str, $length, $spacer);
		
       	// remove trailing spacer and
       	// add start and end delimiters
       	$spacer = preg_quote($spacer);
       	$out_str = preg_replace("/" . $spacer . "$/", "", $out_str);
       	$out_str = $start . $out_str . $end;
	}
   	
   	return $out_str;
}

// mbstring module related

function tep_mb_convert_encoding($str, $to_encoding, $from_encoding) {
	$do_encoding = true;
	if (extension_loaded('mbstring')) {
		$to_encoding = strtoupper($to_encoding);
		$from_encoding = strtoupper($from_encoding);
		if (tep_not_null($from_encoding)) {
			if (mb_detect_encoding($str) != $from_encoding) {
				$do_encoding = false;
			}
		} else {
			$from_encoding = mb_detect_encoding($str);
		}
		if ($do_encoding)	$str = mb_convert_encoding($str, $to_encoding, $from_encoding);
	}
	
	return $str;
}
?>