<?
class publishers {
	var $pass_id, $publishers_id;
	
	function publishers($pass_id = '') {
		$this->publishers_id = (int)$pass_id;
	}
	
	function get_publishers() {
		$publishers_array = array();
		$publishers_select_sql = "	SELECT * 
									FROM " . TABLE_PUBLISHERS . "
									WHERE publishers_id = '".$this->publishers_id."'";
		$publishers_result_sql = tep_db_query($publishers_select_sql);
		if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
			$publishers_array[$publishers_row['publishers_id']] = $publishers_row;
		}
		return $publishers_array;
	}
	
	function get_all_publishers() {
		$publishers_array = array();
		$publishers_select_sql = "	SELECT * 
									FROM " . TABLE_PUBLISHERS;
		$publishers_result_sql = tep_db_query($publishers_select_sql);
		while ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
			$publishers_array[$publishers_row['publishers_id']] = $publishers_row;
		}
		return $publishers_array;
	}
	
	function set_publishers_conf($pass_key, $pass_value='') {
		$publishers_configuration_array = array();
		$publishers_configuration_array['publishers_configuration_value'] = tep_db_prepare_input($pass_value);
		$publishers_configuration_array['last_modified'] = 'now()';
		if (isset($_SESSION['login_email_address'])) $publishers_configuration_array['last_modified_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
		tep_db_perform(TABLE_PUBLISHERS_CONFIGURATION, $publishers_configuration_array, 'update', " publishers_id = '".$this->publishers_id."' AND publishers_configuration_key = '".tep_db_input($pass_key)."'");
	}
	
	function get_publishers_conf($pass_key='') {
		$publishers_configuration_array = array();
		$publishers_configuration_select_sql = "SELECT * 
												FROM " . TABLE_PUBLISHERS_CONFIGURATION . "
												WHERE publishers_id = '".$this->publishers_id."'";
		if (tep_not_null($pass_key)) {
			$publishers_configuration_select_sql .= " publishers_configuration_key = '".tep_db_input($pass_key)."'";
			$publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
			$publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql);
			return $publishers_configuration_row['publishers_configuration_value'];
		} else {
			$publishers_configuration_result_sql = tep_db_query($publishers_configuration_select_sql);
			while ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result_sql)) {
				$publishers_configuration_array[$publishers_configuration_row['publishers_configuration_key']] = $publishers_configuration_row;
			}
			return $publishers_configuration_array;
		}
	}
	
	function activate_publishers() {
		$publishers_data_sql = array(	"publishers_status" => 1,
										"last_modified" => 'now()');
		if (isset($_SESSION['login_email_address'])) $publishers_data_sql['last_modified_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
		tep_db_perform(TABLE_PUBLISHERS, $publishers_data_sql, 'update', " publishers_id = '".$this->publishers_id."' ");
	}
	
	function deactivate_publishers() {
		$publishers_data_sql = array(	"publishers_status" => 0,
										"last_modified" => 'now()');
		if (isset($_SESSION['login_email_address'])) $publishers_data_sql['last_modified_by'] = tep_db_prepare_input($_SESSION['login_email_address']);
		tep_db_perform(TABLE_PUBLISHERS, $publishers_data_sql, 'update', " publishers_id = '".$this->publishers_id."' ");
	}
	
	function set_publishers_game($pass_id, $pass_game) {
		$update_publishers_game_data_sql = array( 'publishers_game' => tep_db_prepare_input($pass_game));
		$servers_select_sql = "	SELECT publishers_id 
								FROM " . TABLE_PUBLISHERS_GAMES . "
								WHERE publishers_id = '".$this->publishers_id."'
									AND products_id = '".(int)$pass_id."'";	
		$servers_result_sql = tep_db_query($servers_select_sql);
		if (tep_db_num_rows($servers_result_sql)) {
			tep_db_perform(TABLE_PUBLISHERS_GAMES, $update_publishers_game_data_sql, 'update', " publishers_id = '".$this->publishers_id."' AND products_id = '".(int)$pass_id."' ");
		} else {
			$update_publishers_game_data_sql['publishers_id'] = $this->publishers_id;
			$update_publishers_game_data_sql['products_id'] = (int)$pass_id;
			tep_db_perform(TABLE_PUBLISHERS_GAMES, $update_publishers_game_data_sql);
		}
	}
	
	function get_publishers_game($publishers_id = '') {
		$publishers_games_array = array();
		$servers_select_sql_where = '';
		
		if (tep_not_null($publishers_id)) {
			$servers_select_sql_where = "WHERE publishers_id = '".$this->publishers_id."'";
		}
		// Checking
		echo $servers_select_sql = "	SELECT publishers_games_id, publishers_id, publishers_game
								FROM " . TABLE_PUBLISHERS_GAMES .
								$servers_select_sql_where;
		echo '<br>';
		$servers_result_sql = tep_db_query($servers_select_sql);
		while ($servers_row = tep_db_fetch_array($servers_result_sql)) {
			$publishers_games_array[$servers_row['publishers_id']][$servers_row['publishers_games_id']] = $servers_row['publishers_game'];
		}
		
		return $publishers_games_array;
	}
	
	function set_publishers_game_server($publishers_games_id, $publishers_server) {
		if ((int)$publishers_games_id) {
			$game_array = array('publishers_server' => json_encode($publishers_server));
			
			$servers_select_sql = "	SELECT publishers_games_id 
									FROM " . TABLE_PUBLISHERS_GAMES . "
									WHERE publishers_games_id = ".$game_array['publishers_games_id'];
			$servers_result_sql = tep_db_query($servers_select_sql);
			if (tep_db_num_rows($servers_result_sql)) {
				tep_db_perform(TABLE_PUBLISHERS_GAMES, $game_array, 'update', " publishers_games_id = ".$publishers_games_id);
			}
			
			unset($game_array);
		}
	}
	
	function get_publishers_game_server($pass_match, $pass_type='products_id') {
		$servers_array = array();
		if ($pass_type == 'products_id') {
			$servers_select_sql = "	SELECT publishers_servers 
									FROM " . TABLE_PUBLISHERS_GAMES . "
									WHERE products_id = '".(int)$pass_match."'";			
		} else {
			$servers_select_sql = "	SELECT publishers_servers 
									FROM " . TABLE_PUBLISHERS_GAMES . "
									WHERE game = '".tep_db_input($pass_match)."'";
		}
		$servers_result_sql = tep_db_query($servers_select_sql);
		if ($servers_row = tep_db_fetch_array($servers_result_sql) && tep_not_null($servers_row['publishers_servers'])) {
			$servers_array = json_decode($servers_row['publishers_servers']);
		}
		return $servers_array;
	}
}
?>