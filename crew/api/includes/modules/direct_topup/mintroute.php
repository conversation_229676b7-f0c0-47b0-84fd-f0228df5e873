<?php

include_once(DIR_WS_CLASSES . 'direct_topup.php');
include_once(DIR_WS_CLASSES . 'curl.php');
include_once(DIR_WS_CLASSES . 'currencies.php');
include_once(DIR_WS_CLASSES . 'slack_notification.php');
include_once(DIR_WS_CLASSES . 'curl.php');

class dtu_mintroute extends direct_topup
{
    public $title, $enable;

    private $error_code, $error_msg;

    protected $curl_obj;

    const MIN_MARGIN = 1, LOW_MARGIN = 3;

    function dtu_mintroute()
    {
        $this->title = 'MintRoute';
        $this->enable = true;
    }

    function get_title()
    {
        return $this->title;
    }

    function get_enable()
    {
        return $this->enable;
    }

    function draw_admin_input($pID = '')
    {
        return '';
    }

    function draw_admin_game_input($action, $publishers_games_array)
    {
        switch ($action) {
            /* GAME SETTING */
            case 'new_game':
            case 'edit_game':
                $publishers_game_name = '';
                $publishers_games_daily_limit = 0;
                $publishers_games_today_topped_amount = 0;
                $publishers_games_status = 1;
                if ($action == 'edit_game') {
                    $publishers_game_select_sql = "	SELECT publishers_game, categories_id, publishers_games_remark, 
														publishers_games_pending_message, publishers_games_reloaded_message, 
														publishers_games_failed_message, publishers_games_daily_limit, 
														publishers_games_today_topped_amount, publishers_games_status 
													FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
													WHERE publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'
														AND publishers_id = '" . (int)$publishers_games_array['publishers_id'] . "'";
                    $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                    if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                        $publishers_game_name = $publishers_game_row['publishers_game'];
                        $cat_id = $publishers_game_row['categories_id'];
                        $publishers_games_daily_limit = $publishers_game_row['publishers_games_daily_limit'];
                        $publishers_games_today_topped_amount = $publishers_game_row['publishers_games_today_topped_amount'];
                        $publishers_game_remark = $publishers_game_row['publishers_games_remark'];
                        $publishers_games_pending_message = $publishers_game_row['publishers_games_pending_message'];
                        $publishers_games_reloaded_message = $publishers_game_row['publishers_games_reloaded_message'];
                        $publishers_games_failed_message = $publishers_game_row['publishers_games_failed_message'];
                        $publishers_games_status = $publishers_game_row['publishers_games_status'];
                    }

                    $publishers_games_configuration_select_sql = "	SELECT publishers_games_configuration_value, publishers_games_configuration_key 
																	FROM " . TABLE_PUBLISHERS_GAMES_CONFIGURATION . " AS pg
																	WHERE publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'";
                    $publishers_games_configuration_result_sql = tep_db_query($publishers_games_configuration_select_sql);
                    while ($publishers_games_configuration_row = tep_db_fetch_array($publishers_games_configuration_result_sql)) {
                        $publishers_games_configuration_array[$publishers_games_configuration_row['publishers_games_configuration_key']] = $publishers_games_configuration_row['publishers_games_configuration_value'];
                    }
                }

                $games_name_array = array();
                if ($action == 'new') {
                    $games_name_array[] = array(
                        'id' => '',
                        'text' => PULL_DOWN_DEFAULT
                    );
                }
                $categories_games_select_sql = "SELECT categories_structures_value 
												FROM " . TABLE_CATEGORIES_STRUCTURES . "
												WHERE categories_structures_key  = 'games'";
                $categories_games_result_sql = tep_db_query($categories_games_select_sql);
                $categories_games_row = tep_db_fetch_array($categories_games_result_sql);

                $categories_games_array = explode(",", $categories_games_row['categories_structures_value']);

                $games_name_select_sql = "	SELECT c.categories_id, cd.categories_name
											FROM " . TABLE_CATEGORIES . " as c
											INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " as cd
												ON c.categories_id = cd.categories_id 
											WHERE c.categories_id IN ('" . implode("','", $categories_games_array) . "')
												AND cd.language_id='1'";
                $games_name_result_sql = tep_db_query($games_name_select_sql);
                while ($games_name_row = tep_db_fetch_array($games_name_result_sql)) {
                    $games_name_array[] = array(
                        'id' => $games_name_row['categories_id'],
                        'text' => $games_name_row['categories_name']
                    );
                }

                $display_html = '<style>.toggle_a{cursor:pointer;color:blue;text-decoration: underline}</style><script>function toggleItem(a){var item=$(a).next();if(item.hasClass("hide")){item.show().removeClass("hide");}else{item.hide().addClass("hide");}}</script><table border="0" width="100%" cellspacing="0" cellpadding="2">
					      			<tr>
					        			<td class="formAreaTitle">' . ($action == 'new_game' ? HEADING_TITLE_INSERT : HEADING_TITLE_UPDATE) . '</td>
					      			</tr>
					      			<tr>
					        			<td class="formArea">
					        				<table border="0" cellspacing="2" cellpadding="2" class="main">
					          					<tr>
					            					<td width="160px">' . ENTRY_GAME_NAME . ':</td>
					            					<td width="400px">' . tep_draw_pull_down_menu('sel_categories_games', $games_name_array, $cat_id) . '</td>
					          					</tr>
					          					<tr>
                                                    <td>' . ENTRY_GAME_STATUS . ':</td>
					            					<td>' . tep_draw_radio_field('publishers_games_status', 1, $publishers_games_status) . 'On ' . tep_draw_radio_field('publishers_games_status', 0, (!$publishers_games_status)) . 'Off 
					            					</td>
</tr>
					          					<tr>
					            					<td>' . ENTRY_TOP_UP_DAILY_LIMIT . ':</td>
					            					<td>' . tep_draw_input_field('publishers_top_up_daily_limit', $publishers_games_daily_limit, ' id="publishers_top_up_daily_limit" ', false) . '</td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_DAILY_TOPPED_AMOUNT . ':</td>
					            					<td>' . number_format($publishers_games_today_topped_amount, 2, ".", "") . '
					            					&nbsp;&nbsp;&nbsp;<a href="' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array(
                            'pID',
                            'pgID',
                            'flag',
                            'action',
                            'subaction',
                            'product_id'
                        )) . 'pID=' . (int)$_REQUEST['pID'] . '&pgID=' . (int)$_REQUEST['pgID'] . '&action=edit&subaction=reset_amount') . '">' . LINK_RESET_TOP_UP_AMOUNT . '</a></td>
					          					</tr>
					          					<tr>
					            					<td>' . ENTRY_GAME_MAPPING . ':</td>
					            					<td>' . tep_draw_input_field('publishers_game', $publishers_game_name, ' id="publishers_game" ', false) . '</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_PENDING_MESSAGE . ':</td>
					            					<td><textarea name="txt_pending_message" cols="50" rows="5">' . $publishers_games_pending_message . '</textarea></td>
													<td class="main" colspan="3">&nbsp;</td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_RELOADED_MESSAGE . ':</td>
					            					<td><textarea name="txt_reloaded_message" cols="50" rows="5">' . $publishers_games_reloaded_message . '</textarea></td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_FAILED_MESSAGE . ':</td>
					            					<td><textarea name="txt_failed_message" cols="50" rows="5">' . $publishers_games_failed_message . '</textarea></td>
					          					</tr>
					          					<tr>
					            					<td valign="top">' . ENTRY_GAME_REMARK . ':</td>
					            					<td><textarea name="txt_remark" cols="50" rows="5">' . $publishers_game_remark . '</textarea></td>
					          					</tr>
								      			<tr>
								      				<td>&nbsp;</td>
								        			<td align="left" class="main">' . ($action == 'new_game' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
								      			</tr>
					        				</table>
					        			</td>
					      			</tr>
					      			<tr>
					        			<td>' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
					      			</tr>
					      		</table>';
                break;
        }
        return $display_html;
    }

    function draw_admin_customer_input($action, $publishers_games_array)
    {
        switch ($action) {
            /* GAME SETTING */
            case 'new_product':
            case 'edit_product':
                $languages = tep_get_languages();

                $products_id = '';
                $publishers_game_name = '';

                $publishers_game_select_sql = "	SELECT publishers_game
												FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
												WHERE publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'
													AND publishers_id = '" . (int)$publishers_games_array['publishers_id'] . "'";
                $publishers_game_result_sql = tep_db_query($publishers_game_select_sql);
                if ($publishers_game_row = tep_db_fetch_array($publishers_game_result_sql)) {
                    $publishers_game_name = $publishers_game_row['publishers_game'];
                }

                $def_amount = '';
                $def_amount_type = 'currency';
                $def_product_code = '';
                $def_account_flag = 0;
                $def_server_flag = 0;
                $def_character_flag = 0;

                $def_sort_order_account = '50000';

                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $def_label_account[$languages[$n]['id']] = '';
                }

                $def_sort_order_server = '50000';

                $def_sort_order_character = '50000';

                $def_sort_order_account_platform = '50000';

                $def_sync_publisher_character_flag = 0;

                if ($action == 'edit_product') {
                    $publishers_products_select_sql = "	SELECT p.products_id, p.products_cat_path, pd.products_name 
														FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
														INNER JOIN " . TABLE_PRODUCTS . " AS p 
															ON pp.products_id = p.products_id
														INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
															ON pp.products_id = pd.products_id
														WHERE pp.publishers_games_id = '" . (int)$publishers_games_array['publishers_games_id'] . "'
															AND pp.products_id = '" . (int)$publishers_games_array['products_id'] . "' 
															AND pd.language_id = 1 
															AND pd.products_name <> ''";
                    $publishers_products_result_sql = tep_db_query($publishers_products_select_sql);
                    $publishers_products_row = tep_db_fetch_array($publishers_products_result_sql);
                    $products_id = $publishers_products_row['products_id'];
                    $products_cat_path_display = $publishers_products_row['products_cat_path'] . ' > ' . $publishers_products_row['products_name'];

                    $top_up_info_select_sql = "	SELECT top_up_info_key, top_up_info_value, top_up_info_type_id, sort_order, top_up_info_display, languages_id 
												FROM " . TABLE_TOP_UP_INFO . " AS tui
												LEFT JOIN " . TABLE_TOP_UP_INFO_LANG . " AS tuil 
													ON tui.top_up_info_id = tuil.top_up_info_id
												WHERE products_id = '" . (int)$publishers_games_array['products_id'] . "'";
                    $top_up_info_result_sql = tep_db_query($top_up_info_select_sql);
                    while ($top_up_info_row = tep_db_fetch_array($top_up_info_result_sql)) {
                        switch ($top_up_info_row['top_up_info_key']) {
                            case 'sync_publisher_character_flag':
                                $def_sync_publisher_character_flag = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount':
                                $def_amount = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'amount_type':
                                $def_amount_type = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'product_code':
                                $def_product_code = $top_up_info_row['top_up_info_value'];
                                break;
                            case 'account':
                                $def_label_account[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_sort_order_account = $top_up_info_row['sort_order'];
                                $def_account_flag = 1;
                                break;
                            case 'server':
                                $def_sort_order_server = $top_up_info_row['sort_order'];
                                $def_label_server[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_server_flag = 1;
                                break;
                            case 'character':
                                $def_sort_order_character = $top_up_info_row['sort_order'];
                                $def_label_character[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_character_flag = 1;
                                break;
                            case 'account_platform':
                                $def_sort_order_account_platform = $top_up_info_row['sort_order'];
                                $def_label_account_platform[$top_up_info_row['languages_id']] = $top_up_info_row['top_up_info_display'];
                                $def_account_platform_flag = 1;
                                break;
                        }
                    }
                }

                $display_html = '<table border="0" cellspacing="2" cellpadding="2" class="main" width="100%">
			          					<tr>
			            					<td width="100px" valign="top">' . ENTRY_PRODUCT_ID . ':</td>
			            					<td class="reportRecords">';
                if ($action == 'new_product') {
                    $display_html .= tep_draw_input_field('product_id', $products_id, ' id="games_product_id" onblur="load_product_cat_path(this.value)" ', false) . '&nbsp;(<a href="javascript:openDGDialog(' . tep_href_link(FILENAME_POPUP_PRODUCTS_LIST, 'direct_top_up=1&fname=log_files.php') . ', 600, 250)">Product List</a>)';
                } else {
                    $display_html .= $products_id . tep_draw_hidden_field('product_id', $products_id);
                }
                $display_html .= '				<div id="div_cat_path">' . (tep_not_null($products_cat_path_display) ? $products_cat_path_display : '') . '</div>
											</td>
			    	      				</tr>
			          					<tr>
			            					<td colspan="2">
			            						<fieldset>
			            							<legend>Configuration</legend>
							        				<table border="0" cellspacing="0" cellpadding="2" class="main">
							          					<tr valign="top" class="reportListingOdd">
							            					<td class="reportBoxHeading" width="100px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="150px">&nbsp;</td>
							            					<td class="reportBoxHeading" width="300px" align="center">Description</td>
							            					<td class="reportBoxHeading" width="100px" align="center">' . TABLE_HEADING_SORT_ORDER . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportBoxHeading" width="100px" align="center">' . $languages[$i]['name'] . '</td>';
                }
                $display_html .= '						<td class="reportBoxHeading" width="150px" align="center">' . TABLE_HEADING_SYSTEM_TYPE . '</td>
															<td class="reportBoxHeading" width="100px">Use in API Signature?</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT_TYPE . ':</td>
							            					<td class="reportRecords">';
                $amount_type_array = array();
                $amount_type_array[] = array('id' => 'currency', 'text' => 'Currency');
                $amount_type_array[] = array('id' => 'point', 'text' => 'Point');
                $display_html .= tep_draw_pull_down_menu('sel_amount_type', $amount_type_array, $def_amount_type, false);
                $display_html .= '						</td>
							            					<td class="reportRecords">\'point\' or \'currency\' for this top-up amount.</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '					<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '							<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_AMOUNT . ':</td>
							            					<td class="reportRecords">' . tep_draw_input_field('txt_amount', $def_amount, ' id="txt_amount" ', false) . '</td>
							            					<td class="reportRecords">Deno face value to be top-up.<br>(total to be top-up = quantity * amount)</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '         					<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
                                                        <tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this, \'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_PRODUCT_CODE . ':</td>
							            					<td class="reportRecords">' . tep_draw_input_field('txt_product_code', $def_product_code, ' id="txt_product_code" ', false) . '</td>
							            					<td class="reportRecords">Product reference code used at Publisher end</td>
							            					<td class="reportRecords" align="center">-</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">-</td>';
                }
                $display_html .= '         					<td class="reportRecords" align="center">-</td>
							            					<td class="reportRecords" align="center"></td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT . ':</td>
							            					<td class="reportRecords">-</td>
															<td class="reportRecords">Game\'s account name to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account', $def_sort_order_account, ' id="txt_sort_order_account" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account[' . $languages[$i]['id'] . ']', $def_label_account[$languages[$i]['id']], ' id="txt_label_account_' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '								<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_account', '1', ($def_account_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_account', '0', (!$def_account_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingEven\')" onclick="rowClicked(this,\'reportListingEven\')">
							            					<td class="reportRecords">' . ENTRY_SERVER . ':</td>
							            					<td class="reportRecords">-</td>
							            					<td class="reportRecords">Server ID of the selected game.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_server', $def_sort_order_server, ' id="txt_sort_order_server" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_server[' . $languages[$i]['id'] . ']', $def_label_server[$languages[$i]['id']], ' id="txt_label_server' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }

                $display_html .= '							<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_server', '1', ($def_server_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_server', '0', (!$def_server_flag ? true : false)) . '&nbsp;Disable
															</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingOdd" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_CHARACTER . ':</td>
							            					<td class="reportRecords">' . tep_draw_checkbox_field('chk_sync_publisher_character', 1, $def_sync_publisher_character_flag, ' id="chk_sync_publisher_character" ') . ' sync with publisher</td>
							            					<td class="reportRecords">Character in game to be top-up.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_character', $def_sort_order_character, ' id="txt_sort_order_character" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_character[' . $languages[$i]['id'] . ']', $def_label_character[$languages[$i]['id']], ' id="txt_label_character' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_character', '1', ($def_character_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_character', '0', (!$def_character_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          					<tr valign="top" class="reportListingEven" onmouseover="rowOverEffect(this, \'reportListingRowOver\')" onmouseout="rowOutEffect(this, \'reportListingOdd\')" onclick="rowClicked(this, \'reportListingOdd\')">
							            					<td class="reportRecords">' . ENTRY_ACCOUNT_PLATFORM . ':</td>
							            					<td class="reportRecords">&nbsp;</td>
							            					<td class="reportRecords">Game account platform.</td>
							            					<td class="reportRecords" align="center">' . tep_draw_input_field('txt_sort_order_account_platform', $def_sort_order_account_platform, ' id="txt_sort_order_account_platform" size="10" ', false) . '</td>';
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $display_html .= '						<td class="reportRecords" align="center">' . tep_draw_input_field('txt_label_account_platform[' . $languages[$i]['id'] . ']', $def_label_account_platform[$languages[$i]['id']], ' id="txt_label_account_platform' . $languages[$i]['id'] . '" size="10" ', false) . '</td>';
                }
                $display_html .= '		  					<td class="reportRecords" nowrap>' . tep_draw_radio_field('rd_account_platform', '1', ($def_account_platform_flag ? true : false)) . '&nbsp;Display&nbsp;&nbsp;' . tep_draw_radio_field('rd_account_platform', '0', (!$def_account_platform_flag ? true : false)) . '&nbsp;Disable
							            					</td>
							            					<td class="reportRecords" align="center">Always</td>
							          					</tr>
							          				</table>
								          		</fieldset>
			            					</td>
			          					</tr>
						      			<tr>
						      				<td>&nbsp;</td>
						        			<td align="left" class="main">' . ($action == 'new_product' ? '<input type="submit" class="inputButton" value="Insert">' : '<input type="submit" class="inputButton" value="Update">') . '&nbsp;<input type="button" class="inputButton" value="Cancel" onclick="location.href=\'' . tep_href_link(FILENAME_PUBLISHERS_GAMES, tep_get_all_get_params(array('action', 'subaction')) . 'action=edit') . '\'"></td>
						      			</tr>
			        				</table>';
                break;
        }
        return $display_html;
    }

    function validate_admin_update_input($param, &$msg)
    {
        return true;
    }

    function validate_admin_game_input($param, &$msg)
    {
        return true;
    }

    function validate_admin_customer_input($param, &$msg)
    {
        return true;
    }

    /**
     * @param $orders_products_id
     * @param $publisher_id
     * @param $top_up_id
     * @param $amount_type (Currency / Point)
     * @param $amount
     * @param $quantity
     * @param $account
     * @param $character
     * @param $game (Publishers Game Mapping Column)
     * @param string $server
     * @param string $account_platform
     * @param array $product_array => some useful data ['pid' => products_id, top_up_info => ['publishers_games_id' => 'publishers_games_id']]
     * (Sample Data converted to JSON below)
     * {"pid":"118913","topup_info":{"amount_type":{"top_up_info_id":"840","products_id":"118913","top_up_info_title":"Amount Type","top_up_info_key":"amount_type","top_up_info_description":"Top-up amount type","top_up_info_value":"currency","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"amount":{"top_up_info_id":"841","products_id":"118913","top_up_info_title":"Base Amount","top_up_info_key":"amount","top_up_info_description":"Top-up base Amount","top_up_info_value":"1","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"product_code":{"top_up_info_id":"842","products_id":"118913","top_up_info_title":"Product Code","top_up_info_key":"product_code","top_up_info_description":"Product reference from Publisher","top_up_info_value":"1222586","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"sync_publisher_character_flag":{"top_up_info_id":"843","products_id":"118913","top_up_info_title":"Sync publisher character","top_up_info_key":"sync_publisher_character_flag","top_up_info_description":"Sync publisher character","top_up_info_value":"1","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"account":{"top_up_info_id":"844","products_id":"118913","top_up_info_title":"Customer's Account","top_up_info_key":"account","top_up_info_description":"Customer's Account","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"retype_account_flag":{"top_up_info_id":"845","products_id":"118913","top_up_info_title":"Retype Account","top_up_info_key":"retype_account_flag","top_up_info_description":"Retype Account","top_up_info_value":"0","top_up_info_type_id":"1","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"server":{"top_up_info_id":"846","products_id":"118913","top_up_info_title":"Server","top_up_info_key":"server","top_up_info_description":"Customer's game servers","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"character":{"top_up_info_id":"847","products_id":"118913","top_up_info_title":"Customer's character","top_up_info_key":"character","top_up_info_description":"Customer's character","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 12:47:46","last_modified_by":"0","use_function":"","set_function":""},"account_platform":{"top_up_info_id":"848","products_id":"118913","top_up_info_title":"Account Platform","top_up_info_key":"account_platform","top_up_info_description":"Account Platform","top_up_info_value":"","top_up_info_type_id":"2","sort_order":"50000","last_modified":null,"date_added":"2018-09-13 16:01:46","last_modified_by":"0","use_function":"","set_function":""}},"publisher_games_id":"63"}
     * @return array|mixed
     */
    public function do_top_up(
        $orders_products_id,
        $publisher_id,
        $top_up_id,
        $amount_type,
        $amount,
        $quantity,
        $account,
        $character,
        $game,
        $server = '',
        $platform = '',
        $product_array = array()
    ) {
        $result_code = '';

        $op_data = $this->getOrdersIdByOrdersProductsId($orders_products_id);

        $response_array = $this->processRequest([
            'orders_id' => $op_data['orders_id'],
            'orders_products_id' => $orders_products_id,
            'game_account_info' => [
                'account' => $account,
                'character' => $character,
                'game' => $game,
                'server' => $server,
                'platform' => $platform
            ]
        ]);

        if (empty($response_array)) {
            $response_array['result_code'] = (!empty($result_code) ? $result_code : '1001');
            $response_array['publisher_ref_id'] = '';
            $response_array['top_up_status'] = 'failed';
            $response_array['error_msg'] = $this->error_code . ' : ' . $this->error_msg;
        }

        return $response_array;
    }

    protected function processRequest($params)
    {
        $headers = array(
            'Content-Type: application/json'
        );

        $config = unserialize(MICRO_SERVICE_ORDER);

        $timestamp = time();

        $attach = array(
            'api_key' => $config['key'],
            'timestamp' => $timestamp,
        );

        $body = array_merge($attach, ['data' => $params]);

        ksort($body);

        $body = json_encode($body);

        $signature = hash_hmac('sha256', $body, $config['secret']);

        $headers[] = 'signature: ' . $signature;

        $this->curl_obj = new curl();
        $this->curl_obj->timeout = 300;
        $this->curl_obj->connect_via_proxy = false;

        $response = $this->curl_obj->curl_request('POST', $config['baseUrl'] . '/direct-top-up/index', $headers, $body, false);
        $response = json_decode($response, 1);

        if ($curl_error = $this->curl_obj->get_error()) {
            $this->error_code = $curl_error['error_code'];
            $this->error_msg = $curl_error['error_message'];
        }

        return (!empty($response['response']) ? $response['response'] : []);
    }


    public function getOrdersIdByOrdersProductsId($opId)
    {
        $return_array = [];
        $op_sql = "SELECT orders_id
                    FROM " . TABLE_ORDERS_PRODUCTS . "
                    WHERE orders_products_id = '" . $opId . "'
                    LIMIT 1";
        $op_results = tep_db_query($op_sql);
        if ($op_row = tep_db_fetch_array($op_results)) {
            $return_array['orders_id'] = $op_row['orders_id'];
        }
        return $return_array;
    }

    public function get_server_list($publisher_id, $game_info, $get_publishers_conf_array)
    {
        return true;
    }

    public function save_top_up_list($publisher_id, $start_time, $end_time = '', $filename = '')
    {
        return;
    }

    public function reportError($response_data, $ext_subject = '')
    {
        $slack = new slack_notification();
        $data = json_encode(array(
            'text' => '[OG Crew] ' . $this->title . ' API Error - ' . date("F j, Y H:i") . ' from ' . (isset($_SERVER['SERVER_NAME']) && !empty($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '-'),
            'attachments' => array(
                array(
                    'color' => 'warning',
                    'text' => $ext_subject . "\n\n" . json_encode($response_data)
                )
            )
        ));
        $slack->send(SLACK_WEBHOOK_DEV_DEBUG, $data);
    }

}

?>
