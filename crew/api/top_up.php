<?php
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

error_reporting(E_ALL & ~E_NOTICE);

ini_set("memory_limit", "50M");

include_once('includes/configure.php');
include_once(DIR_WS_INCLUDES . 'database_tables.php');
include_once(DIR_WS_FUNCTIONS . 'database.php');
include_once(DIR_WS_FUNCTIONS . 'general.php');
include_once(DIR_WS_CLASSES . 'mime.php');
include_once(DIR_WS_CLASSES . 'email.php');

tep_db_connect() or die('Unable to connect to database server!');

// include caching class
require_once(DIR_WS_CLASSES . 'cache_abstract.php');
require_once(DIR_WS_CLASSES . 'memcache.php');
require_once(DIR_WS_CLASSES . 'direct_topup.php');
require_once(DIR_WS_CLASSES . 'direct_topup_api_log.php');
$memcache_obj = new OGM_Cache_MemCache();

// set application wide parameters
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	if (!defined($configuration['cfgKey'])) define($configuration['cfgKey'], $configuration['cfgValue']);
}
$request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';

$_POST = $_REQUEST;

$action = '';
if (isset($_REQUEST['action'])) $action = $_REQUEST['action'];

$merchant_status = 1;
$publisher_api_id = (isset($_REQUEST['publisher_id']) ? $_REQUEST['publisher_id']: '');
$return_code = '2000';
$return_array = array();

$publishers_configuration_array = array();
$publishers_configuration_sql = "	SELECT pc.publishers_id ,pc.publishers_configuration_value, pc.publishers_configuration_key
									FROM " . TABLE_PUBLISHERS_CONFIGURATION . " AS pc  
									INNER JOIN " . TABLE_PUBLISHERS_CONFIGURATION . " AS pc2
										ON pc2.publishers_id = pc.publishers_id 
											AND pc2.publishers_configuration_key = 'PUBLISHER_API_ID'
									WHERE pc2.publishers_configuration_value = '".tep_db_input($publisher_api_id)."'";
$publishers_configuration_result = tep_db_query($publishers_configuration_sql);
while ($publishers_configuration_row = tep_db_fetch_array($publishers_configuration_result)) {
	$publishers_configuration_array[$publishers_configuration_row['publishers_configuration_key']] = $publishers_configuration_row['publishers_configuration_value'];
	$publishers_id = $publishers_configuration_row['publishers_id'];
}

switch ($action) {
    case 'get_top_up_report': 
        $direct_topup_obj = new direct_topup();
        
        if (!empty($publishers_id) && isset($_REQUEST['starttime']) && isset($_REQUEST['endtime']) && isset($_REQUEST['signature'])) {
            $response = $direct_topup_obj->get_top_up_report($publishers_id, $_REQUEST['starttime'], $_REQUEST['endtime'], $_REQUEST['signature']);
            
            $return_code = $response['code'];
            $return_array['report'] = $response['result'];
            
            if ($return_code == '2000') {   // success
                echo json_encode($return_array);
                exit;
            }
        } else {
            $return_code = '1001';
        }
        
        break;
    case 'check_top_up_status': 
        $direct_topup_obj = new direct_topup();
        
        $result_flag = false;
        
        $orders_top_up_select_sql = "	SELECT publishers_id, publishers_ref_id 
                                        FROM " . TABLE_ORDERS_TOP_UP . "
                                        WHERE top_up_id = '".(int)$_REQUEST['top_up_id']."'";
        $orders_top_up_result_sql = tep_db_query($orders_top_up_select_sql );
        if ($orders_top_up_row = tep_db_fetch_array($orders_top_up_result_sql)) {
            $direct_topup_obj->check_top_up_status($orders_top_up_row['publishers_id'], $orders_top_up_row['publishers_ref_id']);
            $result_flag = true;
        }
        
        header('Content-Type: text/xml');
        echo '<response>';
        echo '<result>'.($result_flag?1:0).'</result>';
        echo '</response>';
        exit;
    
	case 'check_payment_status': //Check Payment Status
		
		/* Start API Request Log */
		$request_log = "url: -\n";
		ob_start();
		echo "<pre>";
		print_r($_POST);
		$request_log .= ob_get_contents();
		ob_end_clean();
		$api_log_obj = new direct_topup_api_log($action, $request_log, array('publishers_id' => (int)$publishers_id, 'top_up_id' => (isset($_REQUEST['top_up_id']) ? $_REQUEST['top_up_id'] : '' )));
		/* End API Request Log */
		
		$return_array['top_up_id'] = '';
		$return_array['publisher_id'] = '';
		$return_array['payment_status'] = '';
		if (!isset($publishers_id) || $publishers_id == '') {
			$return_code = '1001';
		} else if (!isset($publisher_api_id) || $publisher_api_id == '') {
			$return_code = '1001';
		}  else if (!isset($_POST['top_up_id']) || $_POST['top_up_id'] == '') {
			$return_code = '1001';
		} else if (!isset($_POST['signature']) || $_POST['signature'] == '') {
			$return_code = '1001';
		} else {
			// check signature
			$check_signature_array = array();
			$check_signature_array['action'] = 'check_payment_status';
			$check_signature_array['publisher_id'] = $publisher_api_id;
			$check_signature_array['top_up_id'] = $_POST['top_up_id'];
			$publisher_signature = get_signature($check_signature_array, $publishers_configuration_array['SECRET_KEY']);
			if ($publisher_signature == $_POST['signature']) {
				if ($publishers_configuration_array['SECRET_KEY']=='') {
					$return_code = '1004';
				} else if (!$merchant_status){
					$return_code = '1005';
				} else {
					$return_code = '2000';
					$orders_status_select_sql = "	SELECT o.orders_status 
													FROM " . TABLE_ORDERS . " AS o 
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
														ON op.orders_id = o.orders_id 
													INNER JOIN " . TABLE_ORDERS_TOP_UP . " AS otu 
														ON otu.orders_products_id = op.orders_products_id 
													WHERE otu.publishers_id = '".(int)$publishers_id."'
														AND otu.top_up_id = '".(int)$_POST['top_up_id']."'";
					$orders_status_result_sql = tep_db_query($orders_status_select_sql);
					if ($orders_status_row = tep_db_fetch_array($orders_status_result_sql)) {
						$return_array['publisher_id'] = $publisher_api_id;
						$return_array['top_up_id'] = $_POST['top_up_id'];
						
						switch ($orders_status_row['orders_status']) {
							case '1':
							case '7':
								$return_array['payment_status'] = 'PENDING';
								break;
							case '2':
							case '3':
								$return_array['payment_status'] = 'PAID';
								break;
							default:
								$return_array['payment_status'] = 'UNKNOWN';
								break;
						}
					} else {
						$return_array['payment_status'] = 'NOT_FOUND';
					}
				}
			} else {
				$return_code = '1002';
			}
		}
		/* Start API Response Log */
		ob_start();
		echo "<pre>";
		print_r($return_array);
		$curl_response = ob_get_contents();
		ob_end_clean();
		$api_log_obj->end_log($return_code, $curl_response, array('publishers_id' => (int)$publishers_id, 'top_up_id' => (isset($_REQUEST['top_up_id']) ? $_REQUEST['top_up_id'] : '' )));
		/* End API Response Log */
        
		break;
		
	case 'direct_topup_insert_cart':	// validate before add-to-cart
		if (isset($_REQUEST['pid']) && (int)$_REQUEST['pid']>0) {
			$game_info = array();
			
			if (isset($_REQUEST['game_info_se'])) {
				$game_info['game_info'] = @unserialize(stripslashes($_REQUEST['game_info_se']));
			}
			
			if (!isset($game_info['game_info']) || !isset($game_info['game_info']['account']) || !tep_not_null($game_info['game_info']['account'])) {
				$return_code = '1007';
				$return_array['result_message'] = 'ERROR_INVALID_TOP_UP_ACCOUNT';
            } else {
                if (isset($game_info['game_info']['account_2']) && isset($game_info['game_info']['account'])) {
                    if (trim($game_info['game_info']['account']) != trim($game_info['game_info']['account_2'])) {
                        $return_code = '1007';
                        $return_array['result_message'] = 'ERROR_INVALID_TOP_UP_ACCOUNT';
                        break;
                    }
                }
				$products_id = (int)$_REQUEST['pid'];
				$qty = (isset($_REQUEST['txt_qty']) && (int)$_REQUEST['txt_qty']>0 ? (int)$_REQUEST['txt_qty'] : 1);
				$game_info['game_info']['account'] = trim($game_info['game_info']['account']);
				
				$direct_topup_obj = new direct_topup();
				
				$publishers_games_products_id = $products_id;
				if ($publishers_id = $direct_topup_obj->check_is_supported_by_direct_top_up($products_id)) {
					
					$products_select_sql = "SELECT products_bundle, products_bundle_dynamic
											FROM " . TABLE_PRODUCTS . "
											WHERE products_id = '".$publishers_games_products_id."'";
					$products_result_sql = tep_db_query($products_select_sql);
					if ( $products_row = tep_db_fetch_array($products_result_sql)) {
						if ($products_row['products_bundle'] == 'yes' || $products_row['products_bundle_dynamic'] == 'yes') {
							$bundle_select_sql = "	SELECT pp.products_id
													FROM " . TABLE_PUBLISHERS_PRODUCTS . " AS pp 
													INNER JOIN " . TABLE_PRODUCTS_DELIVERY_INFO . " AS pdi
														ON pdi.products_id = pp.products_id 
													INNER JOIN " . TABLE_PRODUCTS_BUNDLES . " AS pb
														ON pp.products_id=pb.subproduct_id
													WHERE pb.bundle_id = '".tep_get_prid($publishers_games_products_id)."'
														AND pdi.products_delivery_mode_id = '6'
													LIMIT 1";
							$bundle_result_sql = tep_db_query($bundle_select_sql);
							$bundle_row = tep_db_fetch_array($bundle_result_sql);
							$publishers_games_products_id = $bundle_row['products_id'];
						}
					}
					
					$games_mapping_select_sql = "	SELECT pg.publishers_game, pg.publishers_games_id 
													FROM " . TABLE_PUBLISHERS_GAMES . " AS pg 
													INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
														ON pg.publishers_games_id = pp.publishers_games_id
													WHERE pg.publishers_id = '".(int)$publishers_id."'
														AND pp.products_id = '".(int)$publishers_games_products_id."'";
					$games_mapping_result_sql = tep_db_query($games_mapping_select_sql);
					$games_mapping_row = tep_db_fetch_array($games_mapping_result_sql);
					
					$get_top_up_info_array = $direct_topup_obj->get_top_up_info($publishers_games_products_id);
					
					$validate_game_acc_array = array(	'game' => $games_mapping_row['publishers_game'], 
														'publishers_games_id' => $games_mapping_row['publishers_games_id'],
                                                        'product_id' => $publishers_games_products_id,
														'amount_type' => $get_top_up_info_array['amount_type']['top_up_info_value'],
														'amount' => $get_top_up_info_array['amount']['top_up_info_value'],
														'quantity' => $qty
													);
					if ($direct_topup_obj->validate_game_acc($publishers_id, array_merge($game_info['game_info'], $validate_game_acc_array), $curl_response_array)) {
						$return_code = '1';
						$return_array['result_message'] = 'SUCCESS';
					} else {
						if (isset($curl_response_array['result_code']) && $curl_response_array['result_code'] == '1508') {
							$return_code = '0';
							$return_array['result_message'] = 'ERROR_DTU_EXCEED_TOP_UP_LIMIT';
						} else if (isset($curl_response_array['game_acc_status']) && $curl_response_array['game_acc_status'] == 'NOT_RELOADABLE') {
							$return_code = '0';
							$return_array['result_message'] = 'ERROR_INVALID_TOP_UP_ACCOUNT';
						} else {
							$return_code = '0';
							$return_array['result_message'] = 'ERROR_UNABLE_VALIDATE_TOP_UP_ACCOUNT';
						}
					}
				} else {
					$return_code = '0';
					$return_array['result_message'] = 'ERROR_PUBLISHER_DOES_NOT_EXIST';
				}
			}
		} else {
			$return_code = '0';
			$return_array['result_message'] = 'INCOMPLETE_REQUEST';
		}
		break;
		
		
	case 'load_dtu_character_list':		// load game character list
		if (isset($_REQUEST['pid']) && (int)$_REQUEST['pid'] > 0 && (isset($_REQUEST['account']) && tep_not_null($_REQUEST['account'])) || (isset($_REQUEST['server']) && tep_not_null($_REQUEST['server']))) {
			$pid = (int)$_REQUEST['pid'];
			$account = $_REQUEST['account'];
			$server = $_REQUEST['server'];
			
			$publishers_select_sql = "	SELECT pg.publishers_id, pg.publishers_games_id, pg.publishers_game 
										FROM " . TABLE_PUBLISHERS_GAMES . " AS pg
										INNER JOIN " . TABLE_PUBLISHERS_PRODUCTS . " AS pp
											ON pp.publishers_games_id = pg.publishers_games_id
										WHERE pp.products_id = '".$pid."'";
			$publishers_result_sql = tep_db_query($publishers_select_sql);
			if ($publishers_row = tep_db_fetch_array($publishers_result_sql)) {
				$direct_topup_obj = new direct_topup();
				
				$games_acc_array = array();
				$games_acc_array['account'] = $account;
				$games_acc_array['server'] = $server;
				$games_acc_array['game'] = $publishers_row['publishers_game'];
				$games_acc_array['publishers_games_id'] = $publishers_row['publishers_games_id'];
				
				$characters_array = $direct_topup_obj->get_character_list($publishers_row['publishers_id'], $games_acc_array, $curl_response_array);
				
				if (isset($curl_response_array['result_code']) && $curl_response_array['result_code'] == '2000') {
					if (count($characters_array) > 0) {
						$return_code = '1';
						$return_array['result_message'] = 'SUCCESS';
						$return_array['data'] = $characters_array;
					} else {
						$return_code = '0';
						$return_array['result_message'] = 'ERROR_DTU_NO_CHARACTERS_IN_LIST';
					}
				} else {
					$return_code = '0';
					$return_array['result_message'] = 'ERROR_DTU_NO_CHARACTERS_IN_LIST';
				}
			} else {
				$return_code = '0';
				$return_array['result_message'] = 'INVALID_PUBLISHER';
			}
		} else {
			$return_code = '0';
			$return_array['result_message'] = 'INCOMPLETE_REQUEST';
		}
		break;
		
		
	default:
		$return_code = '1000';
		break;
}

header('Content-Type: text/html');
$return_array['result_code'] = $return_code;
unset($return_array['action']);
$return_array['signature'] = get_signature($return_array, $publishers_configuration_array['SECRET_KEY']);
echo json_encode($return_array);

function get_signature($param, $key) {
	$action = array();
	if (isset($param['action'])) {
		$action['action'] = $param['action'];
	}
	unset($param['action']);
	ksort($param);
	reset($param);
	
	$param = array_merge($action, $param);
	
	$signature_array = array();
	foreach ($param as $key_loop => $data_loop) {
		$signature_array[] = $key_loop . '=' . $data_loop;
	}
	$signature_array[] = 'secret_key=' . $key;
	
	return sha1(implode("&",$signature_array));
}
?>