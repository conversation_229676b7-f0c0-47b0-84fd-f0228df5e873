<?
require('includes/application_top.php');
?>
<html>
	<head></head>
	<body onload="return document.bb_payment_success.submit();">
<?
		$url = tep_href_link(FILENAME_CHECKOUT_PROCESS);
		echo "\n".tep_draw_form('bb_payment_success', $url, 'post');
		
		reset($_REQUEST);
		while (list($key, $value) = each($_REQUEST)) {
			if (!is_array($_REQUEST[$key])) {
				echo tep_draw_hidden_field($key, htmlspecialchars(stripslashes($value)));
			}
		}
?>
		</form>
	</body>
</html>