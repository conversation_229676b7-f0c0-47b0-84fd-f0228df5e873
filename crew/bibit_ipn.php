<?php
//Process payment
echo '<html><body>[OK]</body></html>';

require_once('includes/application_top.php');

require_once(DIR_WS_MODULES . 'payment/bibit/classes/bibit_ipn_class.php');
include_once(DIR_WS_LANGUAGES . $language . '/' . FILENAME_CHECKOUT_PROCESS);

if (isset($_POST) && count($_POST)) {
	$payment = 'bibit';
	
	// load selected payment module
	include_once(DIR_WS_CLASSES . 'payment.php');
	include_once(DIR_WS_CLASSES . 'pgs.php');
	
	$payment_modules = new payment($payment);
	
	$bibit_mapping_array = array(	'ECMC_CREDIT-SSL' => 'ECMC-SSL',
                                    'ECMC_DEBIT-SSL' => 'ECMC-SSL',
                                    'ECMC_COMMERCIAL_CREDIT-SSL' => 'ECMC-SSL',
                                    'ECMC_COMMERCIAL_DEBIT-SSL' => 'ECMC-SSL',
                                    'VISA_CREDIT-SSL' => 'VISA-SSL',
                                    'VISA_DEBIT-SSL' => 'VISA-SSL',
                                    'VISA_COMMERCIAL_CREDIT-SSL' => 'VISA-SSL',
                                    'VISA_COMMERCIAL_DEBIT-SSL' => 'VISA-SSL');
	$bibit_return_system_code = '';
	if (isset($_POST['PaymentMethod']) && tep_not_null($_POST['PaymentMethod'])) {
		$bibit_return_system_code = $_POST['PaymentMethod'];
		if (isset($bibit_mapping_array[$bibit_return_system_code])) {
			$bibit_return_system_code = $bibit_mapping_array[$bibit_return_system_code];
		}
	}
	
	$payment_methods_id_select_sql = "	SELECT pm.payment_methods_id 
										FROM " . TABLE_PAYMENT_METHODS . " AS pm
										INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pg 
											ON (pm.payment_methods_parent_id = pg.payment_methods_id 
												AND pg.payment_methods_filename = '" . tep_db_input($payment . '.php') . "') 
										WHERE pm.payment_methods_code = '" . tep_db_input(trim($bibit_return_system_code)) . "' 
										LIMIT 1";
	$payment_methods_id_result_sql = tep_db_query($payment_methods_id_select_sql);
	if ($payment_methods_id_row = tep_db_fetch_array($payment_methods_id_result_sql)) {
		$$payment = new $payment($payment_methods_id_row['payment_methods_id']);
	}
	
	$bibit_ipn_object = new bibit_ipn($_POST);
	
	//transaction_id
	include_once(DIR_WS_FUNCTIONS . 'custom_product_info.php');
	include_once(DIR_WS_CLASSES . 'order.php');
	include_once(DIR_WS_CLASSES . 'anti_fraud.php');
	include_once(DIR_WS_CLASSES . 'log.php');
	
	$log_object = new log_files('system');
	
	$orders_id = $bibit_ipn_object->get_order_id();
	if (pgs::getOrderSiteId($orders_id) == '5') {
		pgs::repostToPGS('bibit', $_POST);
	} else {
		$order = new order($orders_id);
		if (isset($order->info['currency'])) $currency = $order->info['currency'];

		$original_payment_method_select_sql = "	SELECT payment_methods_id 
												FROM " . TABLE_ORDERS . "
												WHERE orders_id = '".tep_db_input($orders_id)."'";
		$original_payment_method_result_sql = tep_db_query($original_payment_method_select_sql);
		if ($original_payment_method_row = tep_db_fetch_array($original_payment_method_result_sql)) {
			if ($original_payment_method_row['payment_methods_id'] == $payment_methods_id_row['payment_methods_id']) {
				if ($bibit_ipn_object->validate_receiver_account($$payment, $orders_id)) {
					$payment_id_update_sql = "	UPDATE " . TABLE_BIBIT . " 
												SET bibit_payment_id = '" . tep_db_input($_POST['PaymentId']) . "' 
												WHERE orders_id = '" . tep_db_input($orders_id) . "'";
					tep_db_query($payment_id_update_sql);
				}
			} else {
				$payment_id_update_sql = "	UPDATE " . TABLE_BIBIT . " 
											SET bibit_payment_id = '" . tep_db_input($_POST['PaymentId']) . "' 
											WHERE orders_id = '" . tep_db_input($orders_id) . "'";
				tep_db_query($payment_id_update_sql);
			}
		}
	}
}
?>