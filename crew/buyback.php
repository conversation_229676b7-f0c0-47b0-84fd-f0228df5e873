<?php
/*
  	$Id: buyback.php,v 1.87 2015/05/28 10:51:32 darren.ng Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/
require('includes/application_top.php');
require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_BUYBACK);
require(DIR_WS_LANGUAGES . $language . '/email_contents.php');
require_once(DIR_WS_CLASSES . 'buyback.php');
include_once(DIR_WS_CLASSES . 'buyback_supplier.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_CLASSES . 'concurrent_process.php');
require_once(DIR_WS_CLASSES . 'anti_fraud.php');

define('DISPLAY_PRICE_DECIMAL', 2);
define('BUYBACK_PRICE_DEFAULT_CURRENCY', $_SESSION['currency']);

$content = CONTENT_BUYBACK;
if (!tep_session_is_registered('customer_id')) {
//	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}
//$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

//to avoid order flooding.
$max_num_orders = 10;				//max buyback orders per user that is in pending status or processing with zero received. 
$max_num_orders_per_product = 5;	//max buyback orders per user & per product that is in pending status or processing with zero received. 

//dealing type array
$dealing_type_array = array('ofp_deal_on_game' => TEXT_DEALING_TYPE_ON_GAME,
                            'ofp_deal_on_mail' => TEXT_DEALING_TYPE_ON_MAIL
                            );

function tep_send_buyback_notification_email($buyback_request_group_id, $total_buyback, $buyback_product_list, $admin_buyback_product_list, $wbb_input_comment) {
	global $currencies, $customer_id, $messageStack, $content, $PAYMENT_STATUS_ARRAY;
   	
    //Send notification email
	$customer_result = tep_db_query("SELECT customers_firstname,customers_lastname,customers_email_address, customers_gender from ".TABLE_CUSTOMERS." where customers_id='".$customer_id."';");
	$customer_row = tep_db_fetch_array($customer_result);
	
	$customer_name = $customer_row['customers_firstname']." ".$customer_row['customers_lastname'];
	$customer_greeting_name = tep_get_email_greeting($customer_row['customers_firstname'], $customer_row['customers_lastname'], $customer_row['customers_gender']);
	$wbb_input_comment = $wbb_input_comment;
	
	$email_content = 	$customer_greeting_name . 
						EMAIL_NEW_BUYBACK_BODY . 
						sprintf(EMAIL_NEW_BUYBACK_ORDER_NUMBER, $buyback_request_group_id) . "\n" .
						sprintf(EMAIL_NEW_BUYBACK_DATE_ORDERED, tep_date_long(date("Y-m-d")." 00:00:00")) . "\n" .
						sprintf(EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL, $customer_row['customers_email_address']) . "\n\n" .
						EMAIL_NEW_BUYBACK_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . $buyback_product_list .
						EMAIL_SEPARATOR . "\n" . 
 						sprintf(EMAIL_NEW_BUYBACK_ORDER_TOTAL, $currencies->format($total_buyback, true, BUYBACK_PRICE_DEFAULT_CURRENCY, '', 'buy')) . "\n\n" .
 						EMAIL_NEW_BUYBACK_COMMENTS . "\n" . $wbb_input_comment . "\n\n" . 
 						EMAIL_NEW_BUYBACK_STATUS . "\n" .
 						EMAIL_BUYBACK_ORDER_GUIDE . "\n\n" .
 						EMAIL_NEW_BUYBACK_ORDER_CLOSING . "\n\n" .
						EMAIL_NEW_BUYBACK_ORDER_FOOTER;
	
	$admin_email_content = 	$customer_greeting_name . 
							EMAIL_NEW_BUYBACK_BODY . 
							sprintf(EMAIL_NEW_BUYBACK_ORDER_NUMBER, $buyback_request_group_id) . "\n" .
							sprintf(EMAIL_NEW_BUYBACK_DATE_ORDERED, tep_date_long(date("Y-m-d")." 00:00:00")) . "\n" .
							sprintf(EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL, $customer_row['customers_email_address']) . "\n\n" .
							EMAIL_NEW_BUYBACK_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . $admin_buyback_product_list .
							EMAIL_SEPARATOR . "\n" . 
	 						sprintf(EMAIL_NEW_BUYBACK_ORDER_TOTAL, $currencies->format($total_buyback, true, BUYBACK_PRICE_DEFAULT_CURRENCY, '', 'buy')) . "\n\n" .
	 						EMAIL_NEW_BUYBACK_COMMENTS . "\n" . $wbb_input_comment . "\n\n" . 
	 						EMAIL_NEW_BUYBACK_STATUS . "\n" .
	 						EMAIL_BUYBACK_ORDER_GUIDE . "\n\n" .
	 						EMAIL_NEW_BUYBACK_ORDER_CLOSING .
							EMAIL_NEW_BUYBACK_ORDER_FOOTER;
	
	//calculate new statistics
	$today_total_buyback = 0;
    $today_total_cancel_buyback = 0;
    $percentage = 0;
    $today_date = date('Y-m-d');
    $buyback_order_stat_select_sql = "  SELECT buyback_status_id  
                                        FROM ". TABLE_BUYBACK_REQUEST_GROUP ."
                                        WHERE customers_id = '". $customer_id ."'
                                            AND DATE_FORMAT(buyback_request_group_date, '%Y-%m-%d') = CURDATE()";
    $buyback_order_stat_result_sql = tep_db_query($buyback_order_stat_select_sql);
    while ($buyback_order_stat_row = tep_db_fetch_array($buyback_order_stat_result_sql)) {
        $today_total_buyback++;
        
        if ($buyback_order_stat_row['buyback_status_id'] == 4) {
            $today_total_cancel_buyback++;
        }
    }
    
    if ($today_total_cancel_buyback > 0 && $today_total_buyback > 0) {
        $percentage = ($today_total_cancel_buyback / $today_total_buyback) * 100;
        if ($percentage > 50) {
            $stat_cancel_email_content = sprintf(EMAIL_STAT_CUSTOMER_NAME, $customer_name) . "\n" .
                                         sprintf(EMAIL_STAT_CUSTOMER_EMAIL, $customer_row['customers_email_address']) . "\n" .
                                         sprintf(EMAIL_STAT_DATE, $today_date) . "\n\n" .
                                         EMAIL_STAT_TITLE . "\n" .
                                         sprintf(EMAIL_STAT_FORMULAR, $today_total_cancel_buyback, $today_total_buyback, number_format($percentage, 2, '.', ''));
            
            $admin_email_to_array = tep_parse_email_string(BUYBACK_ALERT_EMAIL);
        	for ($i=0; $i < count($admin_email_to_array); $i++) {
        		tep_mail($admin_email_to_array[$i]['name'], $admin_email_to_array[$i]['email'], EMAIL_STAT_SUBJECT, $stat_cancel_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        	}
        }
    }
    
	$admin_email_to_array = tep_parse_email_string(PURCHASE_TEAM_EMAIL);
	for ($i=0; $i < count($admin_email_to_array); $i++) {
		tep_mail($admin_email_to_array[$i]['name'], $admin_email_to_array[$i]['email'], sprintf(EMAIL_NEW_BUYBACK_SUBJECT, $buyback_request_group_id), $admin_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
	 
	$jr_email_to_array = tep_parse_email_string(JUNIOR_PURCHASE_TEAM_EMAIL);
	for ($i=0; $i < count($jr_email_to_array); $i++) {
		tep_mail($jr_email_to_array[$i]['name'], $jr_email_to_array[$i]['email'], sprintf(EMAIL_NEW_BUYBACK_SUBJECT, $buyback_request_group_id), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
	
	if (eregi_dep('^[a-zA-Z0-9._-]+@[a-zA-Z0-9-]+\.[a-zA-Z.]{2,5}$',  $customer_row['customers_email_address'])) {
		tep_mail($customer_name, $customer_row['customers_email_address'], sprintf(EMAIL_NEW_BUYBACK_SUBJECT, $buyback_request_group_id), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
}

//Creating session key based on filename so we know where it got its values.
$form_session_name = constant('FILENAME_BUYBACK');
if (!tep_session_is_registered($form_session_name)) {
	$$form_session_name = array();
	tep_session_register($form_session_name);
}

$form_values_arr = $_SESSION[$form_session_name];

$wbb_input_game_select = 0;
$wbb_input_product_select = 0;
$wbb_error_message = '';
$wbb_error_found = '';
$wbb_notice_found = '';
$wbb_hidden_products_id = 0;
$wbb_hidden_unit_price = 0;
$top_cat_id = '';

if (isset($_GET['action'])) {
	$action = $_GET['action'];
} else {
	$action = 'update';
}

$errorCount = 0;

switch ($action) {
	case 'confirm':
		if (!tep_session_is_registered('customer_id')) {
			tep_redirect(tep_href_link(FILENAME_LOGIN, 'trace=bb_chkout', 'SSL'));
		}
		
		//Validating update2
		$errorCount = 0;
		$form_values_arr = array();
		$concurrentProcessArr = array();
		
		if ($_POST) {
			if (isset($_SESSION['securimage_code_value']) && ($_SESSION['securimage_code_value']!== '')) { // Use !==
			    if ($_SESSION['securimage_code_value'] != strtolower(trim($_POST['captcha_code']))) {
			    	$messageStack->add_session($content, ERROR_INVALID_CODE);
		  	 		$errorCount++;
			    }
			} else {
				$messageStack->add_session($content, ERROR_INVALID_CODE);
		  	 	$errorCount++;
			}
			
			if ($_POST['wbb_input_game_select']) {
				$form_values_arr['wbb_input_game_select'] = tep_db_prepare_input($_POST['wbb_input_game_select']);
			} else {
				$messageStack->add_session($content, sprintf(ERROR_MANDATORY_ISBLANK, ENTRY_GAME_SELECTION));
				$errorCount++;
			}
			
			if ($_POST['wbb_input_product_select']) {
				$form_values_arr['wbb_input_product_select'] = tep_db_prepare_input($_POST['wbb_input_product_select']);
			} else {
				$messageStack->add_session($content, sprintf(ERROR_MANDATORY_ISBLANK, ENTRY_SERVER_SELECTION));
				$errorCount++;
			}

			if ((int)$form_values_arr['wbb_input_product_select'] < 1) {
				$errorCount++;
				$messageStack->add_session($content, TEXT_INVALID_SEVER_SELECTION);
			}
			
			# email address, IM, phone number validation status
			$count_im_select_sql = "SELECT instant_message_accounts_id 
									FROM " . TABLE_INSTANT_MESSAGE_ACCOUNTS . " 
									WHERE customer_id ='" . (int)$_SESSION['customer_id'] . "'";
			$count_im_result_sql = tep_db_query($count_im_select_sql);
			$count_im_num = tep_db_num_rows($count_im_result_sql);
			$buyback_email_verify_check = tep_info_verified_check($_SESSION['customer_id'], tep_get_customers_email($_SESSION['customer_id']), 'email');
			
			// verify phone number
			$customer_complete_phone_info_array = tep_format_telephone($_SESSION['customer_id']);
			$complete_telephone_number = sizeof($customer_complete_phone_info_array) > 0 ? $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'] : '';
			$buyback_phone_verify_check = tep_info_verified_check($_SESSION['customer_id'], $complete_telephone_number, 'telephone');
			
			if ($count_im_num == 0 || $buyback_email_verify_check != 1 || $buyback_phone_verify_check != 1) {
				$errorCount++;
			}
			
			if ($errorCount > 0) {
				tep_redirect(tep_href_link(FILENAME_BUYBACK, 'action=buyback_checkout&pid='.$form_values_arr['wbb_input_product_select'].'&gcat='.$form_values_arr['wbb_input_game_select'], 'SSL'));
			} else {
				$error_msg = '';
				$products_id = $form_values_arr['wbb_input_product_select'];
				/*
				$customer_info_require = tep_customer_info_require($customer_id, $missing_required_info_array);
			
				if ($customer_info_require) {
					$messageStack->add_session('account_edit', sprintf(TEXT_PERSONAL_INFO_REQUIRED, implode(', ', $missing_required_info_array)), 'warning');
					tep_redirect(tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=bb_chkout&gcat='.$form_values_arr['wbb_input_game_select'].'&pid='.$form_values_arr['wbb_input_product_select'], 'SSL'));
				}
				*/
				$first_page_error = false;
				$buybackSupplierObj = new buyback_supplier($form_values_arr['wbb_input_game_select'], $products_id);
				$buybackSupplierObj->calculate_offer_price();

				$min_qty = (int)$buybackSupplierObj->products_arr[$products_id]['min_qty'];
				$min_purchse_qty = (int)$buybackSupplierObj->supplier_price_brackets_sets[$buybackSupplierObj->products_arr[$products_id]['bracket_set_id']]['min_purchase_qty'];
				//$upper_min_qty = $buybackSupplierObj->products_arr[$products_id]['upper_min_qty'];
				//$system_max_qty = (int)$buybackSupplierObj->products_arr[$products_id]['max_qty'];
				
				if (!tep_game_open_for_buyback($form_values_arr['wbb_input_game_select'])) {
					$messageStack->add_session($content, ERROR_BUYBACK_NOT_BUYBACK_GAME);
					$first_page_error = true;
				} else if ($buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status']=='0' || !$buybackSupplierObj->products_arr[$products_id]['is_buyback']) {
					$messageStack->add_session($content, TEXT_NOT_ACCEPT_BUYBACK);
					$first_page_error = true;
				}
				
				//Check customer's total awaiting orders
	            $buyback_order_accepted = tep_accept_buyback_order((int)$_SESSION['customer_id'], $max_num_orders);
				
	        	if (!$buyback_order_accepted) {
	                $messageStack->add_session($content, TEXT_FORM_MAX_ORDERS_REACHED);
	               	$first_page_error = true;
	        	} else {
	            	//Check customer's total awaiting orders for this product.
		            $buyback_order_accepted = tep_accept_buyback_order((int)$_SESSION['customer_id'], $max_num_orders_per_product, $form_values_arr['wbb_input_product_select']);
	            	
		            if (!$buyback_order_accepted) {
		                $messageStack->add_session($content, WARNING_BUYBACK_FORM_STILL_PROCESSING);
		                $first_page_error = true;
		            }
	        	}
	        	
	        	if ($first_page_error)	{
	        		$_SESSION[$form_session_name] = $form_values_arr;
	        		tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=re-submit'));
	        	}
	        	
				// If got order ID
				if (isset($_POST['opid_arr']) && tep_not_null($_POST['opid_arr'])) {
					$opid_arr = explode(",", $_POST['opid_arr']);
					foreach ($opid_arr as $order_product_id) {
						$no_error = true;
						$order_id = '';
						$input_qty = $_POST['qty_delivered_'.$order_product_id];
						
						// Temporary Processing to prevent insert unwanted record;
						$matchcase = md5($order_product_id.':~:'.$products_id.':~:SplitOrder');
						$concurrentProcessObj = new concurrent_process(FILENAME_BUYBACK, $matchcase, (int)$input_qty);
						
						usleep(1100000);

						if($concurrentProcessObj->concurrent_process_matching()) {	// If there is > 1 orders matched
							if (!$concurrentProcessObj->concurrent_process_match_insert_id()) {
								$is_buyback_order = false;
								unset($opid_arr);
								$messageStack->add_session($content, ERROR_SUBMITTED_BY_OTHER_USER);
							}
						}
						
						if ($order_product_id == 'extra_inventory') {
							$max_inventory_space_overwrite = $buybackSupplierObj->products_arr[$products_id]['max_inventory_space_overwrite'];
							if (tep_not_null($max_inventory_space_overwrite) && $max_inventory_space_overwrite > 0) {
//								$actual_qty = ($buybackSupplierObj->products_arr[$products_id]['actual_qty'] > 0) ? $buybackSupplierObj->products_arr[$products_id]['actual_qty'] : 0;
//								$first_list_qty = tep_get_extra_first_list_quantity($products_id);
//								$extra_product_qty = ($max_inventory_space_overwrite - $actual_qty) - $first_list_qty;
								//$extra_product_qty = $buybackSupplierObj->products_arr[$products_id]['suggest_qty'] - $first_list_qty;
								
								$extra_product_qty = $buybackSupplierObj->products_arr[$products_id]['max_qty'];
								
								$upper_min_qty = $extra_product_qty - $min_purchse_qty;
								//Checking Qty////////////////////////////////////////////////////////
								if ((int)$input_qty > 0) {
									if ($min_qty > $extra_product_qty) {
										if ($input_qty != $extra_product_qty) {
											$messageStack->add_session($content, ERROR_BUYBACK_QUANTITY_INVALID);
											$no_error = false;
										}
									} else {
										if (is_numeric($upper_min_qty) && $upper_min_qty > 0) {
											if ( (	$input_qty >= $min_qty 
													&& $input_qty <= $upper_min_qty)
												|| $input_qty == $extra_product_qty) {
												;
											} else {
												$messageStack->add_session($content, ERROR_BUYBACK_QUANTITY_INVALID);
												$no_error = false;
											}
										} else {
											if ($input_qty < $min_qty || $input_qty > $extra_product_qty) {
												$messageStack->add_session($content, ERROR_BUYBACK_QUANTITY_INVALID);
												$no_error = false;
											}
										}
									}
								} else {
									$messageStack->add_session($content, sprintf(ERROR_MANDATORY_ISBLANK, TEXT_QUANTITY));
									$no_error = false;
								}
								//Checking Qty////////////////////////////////////////////////////////
								if ($extra_product_qty > 0 && $no_error == true) {
									;
								} else {
									$messageStack->add_session($content, sprintf(ERROR_MANDATORY_ISBLANK, TEXT_QUANTITY));
									$errorCount++;
								}
							} else {
								$messageStack->add_session($content, sprintf(ERROR_MANDATORY_ISBLANK, TEXT_QUANTITY));
								$errorCount++;
							}
						} else {
							$current_time  = time();
							$get_buyback_qty_select_sql = "	SELECT o.orders_id, o.date_purchased, op.products_id, op.products_quantity, op.products_delivered_quantity, op.orders_products_purchase_eta 
															FROM " . TABLE_ORDERS . " AS o
															INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
																ON o.orders_id = op.orders_id 
															WHERE op.orders_products_id = " . (int)$order_product_id . " 
															AND o.orders_status	IN ( 2, 7 ) 
															";
							$get_buyback_qty_result_sql = tep_db_query($get_buyback_qty_select_sql);
							if ($get_buyback_qty_row = tep_db_fetch_array($get_buyback_qty_result_sql)) {
								if ($products_id != $get_buyback_qty_row['products_id']) {
									$messageStack->add_session($content, TEXT_INVALID_SEVER_SELECTION);
									$errorCount++;
								}
								$max_qty = $get_buyback_qty_row['products_quantity'];
								$order_id = $get_buyback_qty_row['orders_id'];
								$is_buyback_order = false;
								$main_cat_eta_offset = tep_get_game_preset_eta($products_id);
								
								if (!tep_not_null($get_buyback_qty_row['orders_products_purchase_eta']) || $get_buyback_qty_row['orders_products_purchase_eta'] === '0') {
									$is_buyback_order = true;
								} else if ($get_buyback_qty_row['orders_products_purchase_eta'] > 0) {
									$extra_hour = $main_cat_eta_offset + $get_buyback_qty_row['orders_products_purchase_eta'];
									$eta = strtotime($get_buyback_qty_row['date_purchased']." + ".$extra_hour." hours");
									
									if ($current_time > $eta) {
										$is_buyback_order = true;
									}
								}
								
								if ($is_buyback_order) {
									$first_list_qty = tep_get_so_first_list_quantity($order_id, $order_product_id);
									
									$product_qty = ($get_buyback_qty_row['products_quantity'] - $get_buyback_qty_row['products_delivered_quantity']) - $first_list_qty;
									$upper_min_qty = $product_qty - $min_purchse_qty;
									
									//Checking Qty////////////////////////////////////////////////////////
									if ((int)$input_qty > 0) {
										if ($min_qty > $product_qty) {
											if ($input_qty != $product_qty) {
												$messageStack->add_session($content, ERROR_BUYBACK_QUANTITY_INVALID);
												$no_error = false;
											}
										} else {
											if (is_numeric($upper_min_qty) && $upper_min_qty > 0) {
												if ( (	$input_qty >= $min_qty 
														&& $input_qty <= $upper_min_qty)
													|| $input_qty == $product_qty) {
													;
												} else {
													$messageStack->add_session($content, ERROR_BUYBACK_QUANTITY_INVALID);
													$no_error = false;
												}
											} else {
												if ($input_qty < $min_qty || $input_qty > $product_qty) {
													$messageStack->add_session($content, ERROR_BUYBACK_QUANTITY_INVALID);
													$no_error = false;
												}
											}
										}
									} else {
										$messageStack->add_session($content, sprintf(ERROR_MANDATORY_ISBLANK, TEXT_QUANTITY));
										$no_error = false;
									}
									//Checking Qty////////////////////////////////////////////////////////
									if ($product_qty > 0 && $no_error == true) {
										;
									} else {
										$messageStack->add_session($content, sprintf(ERROR_MANDATORY_ISBLANK, TEXT_QUANTITY));
										$errorCount++;
									}
								} else {
									$messageStack->add_session($content, ERROR_BUYBACK_ORDER_CLOSED);
									$errorCount++;
								}
							} else {
								$messageStack->add_session($content, ERROR_BUYBACK_QTY_NOT_EXIST);
								$errorCount++;
							}
						}
						
						$concurrentProcessArr[] = array('temp_id' => (int)$concurrentProcessObj->concurrent_process_insert_id,
														'order_product_id' => $order_product_id,
														'products_id' => $products_id);
						
					}
				} else {
					$messageStack->add_session($content, sprintf(ERROR_MANDATORY_ISBLANK, ENTRY_SELLING_QTY));
					$errorCount++;
				}
			}
		}
		
		if ($errorCount > 0 || count($_SESSION['messageToStack']) > 0) {
			if (count($concurrentProcessArr) > 0) {
				for ($concurrentProcess_cnt=0; $concurrentProcess_cnt < count($concurrentProcessArr); $concurrentProcess_cnt++) {
					$temp_id = $concurrentProcessArr[$concurrentProcess_cnt]['temp_id'];
					$order_product_id = $concurrentProcessArr[$concurrentProcess_cnt]['order_product_id'];
					$products_id = $concurrentProcessArr[$concurrentProcess_cnt]['products_id'];
					
					//usleep(1100000); // For make sure to run $concurrentProcessObj->concurrent_process_matching() b4 update or delete the record;
					$update_temp_process_sql = "UPDATE LOW_PRIORITY " . TABLE_TEMP_PROCESS . " SET 
												page_name = '".FILENAME_BUYBACK.":DONE', 
												match_case = '".$order_product_id.":~:".$products_id.":~:SplitOrder' 
												WHERE temp_id = '".(int)$temp_id."'";
					tep_db_query($update_temp_process_sql);
					//$concurrentProcessObj->concurrent_process_cleanup();
				}
			}
			tep_redirect(tep_href_link(FILENAME_BUYBACK, 'action=buyback_checkout&pid='.$products_id.'&gcat='.$form_values_arr['wbb_input_game_select'], 'SSL'));
		} else {
			/*
			$customer_info_require = tep_customer_info_require($customer_id, $missing_required_info_array);
			
			if ($customer_info_require) {
				$messageStack->add_session('account_edit', sprintf(TEXT_PERSONAL_INFO_REQUIRED, implode(', ', $missing_required_info_array)), 'warning');
				tep_redirect(tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=bb_chkout&gcat='.$form_values_arr['wbb_input_game_select'].'&pid='.$products_id, 'SSL'));
			}
			
			*/
			$store_bo_id_arr = array();
			
			foreach ($opid_arr as $order_product_id) {
				$input_qty = $_POST['qty_delivered_'.$order_product_id];
				$order_id = '';
				$trade_mode = $_POST['trade_mode_'.$order_product_id];
						
				// Create buyback order
		    	$y = array('buyback_request_group_id' => 0,
		    			   'customers_id' => (int)$_SESSION['customer_id'],
		    			   'buyback_request_group_date' => 'now()',
		    			   'buyback_request_group_expiry_date' => '',
		    			   'remote_addr' => tep_get_ip_address(),
						   'currency' => DEFAULT_CURRENCY,
						   'currency_value' => $currencies->currencies[DEFAULT_CURRENCY]['buy_value'],
		    			   //'buyback_request_group_comment' => $form_values_arr['wbb_input_comments'],
		    			   'buyback_request_group_user_type' => (int)tep_get_account_created_from($_SESSION['customer_id']),
						   //'buyback_request_contact_name' => $form_values_arr['wbb_input_character_id'],
						   //'buyback_request_contact_telephone' => $form_values_arr['wbb_input_contact'],
						   'buyback_request_group_site_id' => SITE_ID
		    				);
		    				
		    	tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $y);
		    	$buyback_request_group_id = tep_db_insert_id();
		    	
		    	$store_bo_id_arr[] = $buyback_request_group_id;
		    	
				$order_unit_price = $buybackSupplierObj->products_arr[$products_id]['avg_offer_price'];
		    	$wbb_total_price = $order_unit_price * (int)$input_qty;
		    	$form_values_arr['wbb_total_price'] = $wbb_total_price;
				
				$unit_selling_price = $buybackSupplierObj->products_arr[$products_id]['customer_price'];
				$profit_margin = $unit_selling_price > 0 ? ( ($unit_selling_price - $order_unit_price) / $unit_selling_price ) * 100 : '--';
				$profit_margin = sprintf('%.3f', $profit_margin);
				
				if ($profit_margin < (double)$margin_cfg_array['BUYBACK_PROFIT_MARGIN']) {
					$cell_span_style = ' style="color:#ff0000;" ';
				} else {
					$cell_span_style = ' style="color:#000000;" ';
				}
				
				$profit_margin = $profit_margin . '%';
				
				if ($order_product_id == 'extra_inventory') {
					unset($y);
					$y = array('buyback_request_group_id' => $buyback_request_group_id,
							   'products_id' => $products_id,
							   'buyback_request_quantity' => $input_qty,
							   'buyback_amount' => $wbb_total_price,
							   //'buyback_comment' => $form_values_arr['wbb_input_comments'],
							   //'buyback_sender_character' => $form_values_arr['wbb_input_character_id'],
							   'buyback_dealing_type' => 'extra_inventory',
							   'buyback_unit_price' => $order_unit_price
							   //'orders_id' => $order_id,
							   //'orders_products_id' => $order_product_id
							   );
					tep_db_perform(TABLE_BUYBACK_REQUEST, $y);
				} else {
					$get_orders_id_select_sql = "	SELECT o.orders_id 
													FROM " . TABLE_ORDERS . " AS o 
													INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
														ON (o.orders_id = op.orders_id)
													WHERE op.orders_products_id = " . (int)$order_product_id;
					$get_orders_id_result_sql = tep_db_query($get_orders_id_select_sql);
					
					if ($get_orders_id_row = tep_db_fetch_array($get_orders_id_result_sql)) {
						$order_id = $get_orders_id_row['orders_id'];
						
						$comment = '##BO##'.$buyback_request_group_id.'## - '.$input_qty.' by '.tep_get_customers_name($_SESSION['customer_id']);
				    	
				    	$comment_array = array(	'orders_id' => $order_id,
												'orders_status_id' => '0',
												'date_added' => 'now()',
												'customer_notified' => '0',
												'comments' => $comment,
												'comments_type' => '0',
												'set_as_order_remarks' => '0',
												'changed_by' => tep_get_customers_email($_SESSION['customer_id'])
												);
						tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $comment_array);
						
						unset($y);
						$y = array('buyback_request_group_id' => $buyback_request_group_id,
								   'products_id' => $products_id,
								   'buyback_request_quantity' => $input_qty,
								   'buyback_amount' => $wbb_total_price,
								   //'buyback_comment' => $form_values_arr['wbb_input_comments'],
								   //'buyback_sender_character' => $form_values_arr['wbb_input_character_id'],
								   'buyback_dealing_type' => $trade_mode,
								   'buyback_unit_price' => $order_unit_price,
								   'orders_id' => $order_id,
								   'orders_products_id' => $order_product_id
								   );
						tep_db_perform(TABLE_BUYBACK_REQUEST, $y);
						
						$cron_open_restock_id_array = array('buyback_request_group_id' => $buyback_request_group_id,
															'orders_products_id' => $order_product_id,
															'created_date' => 'now()');
						tep_db_perform(TABLE_CRON_OPEN_RESTOCK_ID, $cron_open_restock_id_array);
					}
				}
				
				// Insert buyback history comment
				$buyback_history_data_array = array('buyback_status_id' => '1',
		    			   							'buyback_request_group_id' => $buyback_request_group_id,
		    			   							'date_added' => 'now()',
		    			   							'customer_notified' => '1',
		    			   							'set_as_buyback_remarks' => '1'
		    			   							);
		    	// Auto insert remark if this is delivery by mail
		    	if ($form_values_arr['wbb_input_dealing_type'] == 'ofp_deal_on_mail') {
					$buyback_history_data_array['comments'] = TEXT_CHAR_DISCONNECTED;
		    	}
		    	tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
				
				if (round($profit_margin) < 15) { // Only notify when gross profut is < 20%
					$cat_path = tep_output_generated_category_path($products_id, 'product');
					$buyback_product_list = $input_qty.' x '.$cat_path.' = ' . $currencies->format((double)$wbb_total_price, true, BUYBACK_PRICE_DEFAULT_CURRENCY, '', 'buy')."\n";
					
					$admin_buyback_product_list = 	'<table border="0" cellspacing="2" cellpadding="2" width="100%">'.
										  			'	<tr>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px">Product</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Maximum Qty</td>'.
										 			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Forecast Available Qty</td>'.
													'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Forecast Actual Qty</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Price (USD)</td>' .
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Selling Price (USD)</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Profit Margin</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px">First List Selling Quantity</td>'.
										  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Amount (USD)</td>'.
										  			'	</tr>'.
													'	<tr>'.
										  			'		<td style="background-color:#DFDFDF; color:#000; font-size:11px"><span '.$cell_span_style.'>'.$cat_path.'</span></td>'.
										  			'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$products_id]['max_inventory_space'].'</span></td>'.
													'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$products_id]['forecast_available_qty'].'</span></td>'.
													'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$products_id]['forecast_actual_qty'].'</span></td>'.
											  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$order_unit_price.'</span></td>' .
											  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$unit_selling_price.'</span></td>' .
											  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$profit_margin.'</span></td>'.
													'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$input_qty.'</span></td>'.
													'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="right"><span '.$cell_span_style.'>'.$wbb_total_price.'</span></td>'.
													'	</tr>'.
													'</table>';
			   		tep_send_buyback_notification_email($buyback_request_group_id, $wbb_total_price, $buyback_product_list, $admin_buyback_product_list, $form_values_arr['wbb_input_comments']);
				}
			}
			
			if (count($concurrentProcessArr) > 0) {
				for ($concurrentProcess_cnt=0; $concurrentProcess_cnt < count($concurrentProcessArr); $concurrentProcess_cnt++) {
					$temp_id = $concurrentProcessArr[$concurrentProcess_cnt]['temp_id'];
					$order_product_id = $concurrentProcessArr[$concurrentProcess_cnt]['order_product_id'];
					$products_id = $concurrentProcessArr[$concurrentProcess_cnt]['products_id'];
					
					//usleep(1100000); // For make sure to run $concurrentProcessObj->concurrent_process_matching() b4 update or delete the record;
					$update_temp_process_sql = "UPDATE LOW_PRIORITY " . TABLE_TEMP_PROCESS . " SET 
												page_name = '".FILENAME_BUYBACK.":DONE', 
												match_case = '".$order_product_id.":~:".$products_id.":~:SplitOrder' 
												WHERE temp_id = '".(int)$temp_id."'";
					tep_db_query($update_temp_process_sql);
					//$concurrentProcessObj->concurrent_process_cleanup();
				}
			}
			
			if (count($store_bo_id_arr) > 0) {
				/*$aft_obj = new anti_fraud();
				$aft_module = $aft_obj->getAftModule();
				$aft_module->set_order_ids($store_bo_id_arr);
				$aft_module->set_customers_id($_SESSION['customer_id']);
				$aft_module->set_transaction_type('BO');  // Customers Order /Buyback Order
				$aft_module->execute_query_call(); // execute the API call*/
			}
			
			unset($_SESSION[$form_session_name]);
	        tep_session_unregister($form_session_name);
			//tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=confirm_msg'));
			tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY, 'history_type=buyback&order_status=1', 'SSL'));
		}
		break;
	case 'update2':
			if (tep_session_is_registered('customer_id')) {
				$customers_info_select_sql = "	SELECT customers_dob, customers_gender, customers_default_address_id 
												FROM " . TABLE_CUSTOMERS . " 
												WHERE customers_id ='" . (int)$_SESSION['customer_id'] . "'";
				$customers_info_result_sql = tep_db_query($customers_info_select_sql);
				$customers_info_row = tep_db_fetch_array($customers_info_result_sql);
				
				if (!tep_not_null($customers_info_row['customers_gender']) || !tep_not_null($customers_info_row['customers_dob']) || $customers_info_row['customers_dob'] == '0000-00-00 00:00:00') {
					tep_redirect(tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=bb_chkout', 'SSL'));
				}
				
				$address_book_info_select_sql = "	SELECT entry_street_address, entry_postcode, entry_city, entry_state, entry_country_id, entry_zone_id 
													FROM " . TABLE_ADDRESS_BOOK . " 
													WHERE address_book_id = '" . (int)$customers_info_row['customers_default_address_id'] . "'";
				$address_book_info_result_sql = tep_db_query($address_book_info_select_sql);
				if ($address_book_info_row = tep_db_fetch_array($address_book_info_result_sql)) {
					if (!tep_not_null($address_book_info_row['entry_street_address']) || 
						!tep_not_null($address_book_info_row['entry_postcode']) || 
						!tep_not_null($address_book_info_row['entry_city']) ||
						!tep_not_null($address_book_info_row['entry_country_id']) ||
						(!tep_not_null($address_book_info_row['entry_state']) && (int)$address_book_info_row['entry_zone_id'] < 1)) {
						tep_redirect(tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=bb_chkout', 'SSL'));
					}
				} else {
					tep_redirect(tep_href_link(FILENAME_ACCOUNT_EDIT, 'trace=bb_chkout', 'SSL'));
				}
				
				tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=update'));
			} else {
				tep_redirect(tep_href_link(FILENAME_LOGIN, 'trace=bb_chkout', 'SSL'));
			}
		
		break;
	case 'update':
		$get_news_select_num = 0;
		$wbb_game_list_arr = tep_get_game_list_arr();
		
		// Buyback news is defined under Promotion news group
		$get_news_select_sql = "SELECT lnd.news_id, lnd.headline, lnd.content, ln.date_added, ln.url 
								FROM ". TABLE_LATEST_NEWS. " AS ln 
								INNER JOIN " . TABLE_LATEST_NEWS_DESCRIPTION . " AS lnd 
									on (ln.news_id = lnd .news_id) 
								WHERE ln.status = '1' 
									and ln.news_groups_id = '2'
									and ln.extra_news_display_sites = '1'
 							    	and ( if(lnd.language_id = '". $languages_id. "', 1, 
		 								  if ((	select count(lnd_inner.news_id) > 0 
		 								  		from  " . TABLE_LATEST_NEWS_DESCRIPTION . " AS lnd_inner
		 										where lnd_inner.language_id ='". $languages_id. "'
													and lnd_inner.news_id=ln.news_id), 
												0,
	 					 						lnd.language_id = '".$default_languages_id."')
	 											 )
	 										 )
 							    ORDER BY ln.date_added DESC LIMIT 8";
		$get_news_select_result_sql = tep_db_query($get_news_select_sql);
		$get_news_select_num = tep_db_num_rows($get_news_select_result_sql);
		
		break;
		
	case 'latest_news':
		$wbb_game_list_arr = tep_get_game_list_arr();
		$get_news_select_sql = "select lnd.news_id, lnd.headline, lnd.content, ln.date_added, ln.url from ". TABLE_LATEST_NEWS. " AS ln inner join " . TABLE_LATEST_NEWS_DESCRIPTION . " AS lnd on (ln.news_id = lnd .news_id) where ln.status = '1' and ln.news_groups_id = '2' and ln.extra_news_display_sites = '1' and ( if(lnd.language_id = '". $languages_id. "', 1, if (( select count(lnd_inner.news_id) > 0 from  " . TABLE_LATEST_NEWS_DESCRIPTION . " AS lnd_inner where lnd_inner.language_id ='". $languages_id. "' and lnd_inner.news_id=ln.news_id), 0, lnd.language_id = '".$default_languages_id."'))) ORDER BY ln.date_added DESC";
		//$get_news_select_sql = "select ln.news_id, ln.headline, ln.latest_news_summary, ln.content, ln.date_added, ln.news_groups_id, ln.url from " . TABLE_LATEST_NEWS . " AS ln LEFT JOIN " . TABLE_LATEST_NEWS_GROUPS . " AS lng ON (ln.news_groups_id = lng.news_groups_id) WHERE ln.status = '1' AND (IF(ln.language='".tep_db_input($languages_id)."', 1, ln.language='".tep_db_input($default_languages_id)."')) AND ln.extra_news_display_sites = '1' ORDER BY ln.date_added DESC";
		
		break;
	case 'favourites_link':
		$wbb_game_list_arr = tep_get_game_list_arr();
		
		$_SESSION[$form_session_name]['wbb_input_game_select'] = tep_db_prepare_input($_GET['gcat']);
		$_SESSION[$form_session_name]['wbb_input_product_select'] = tep_db_prepare_input($_GET['pid']);
		//tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action', 'gcat', 'pid')). 'action=update'));
		
		break;
	case 'buyback_checkout':
		$wbb_game_list_arr = tep_get_game_list_arr();
		break;
}

if (tep_session_is_registered('customer_id')) {
	$count_im_select_sql = "SELECT instant_message_accounts_id 
							FROM " . TABLE_INSTANT_MESSAGE_ACCOUNTS . " 
							WHERE customer_id ='" . (int)$_SESSION['customer_id'] . "'";
	$count_im_result_sql = tep_db_query($count_im_select_sql);
	$count_im_num = tep_db_num_rows($count_im_result_sql);
	$buyback_email_verify_check = tep_info_verified_check($_SESSION['customer_id'], tep_get_customers_email($_SESSION['customer_id']), 'email');
	
	// verify phone number
	$customer_complete_phone_info_array = tep_format_telephone($_SESSION['customer_id']);
	$complete_telephone_number = sizeof($customer_complete_phone_info_array) > 0 ? $customer_complete_phone_info_array['country_international_dialing_code'] . $customer_complete_phone_info_array['telephone_number'] : '';
	$buyback_phone_verify_check = tep_info_verified_check($_SESSION['customer_id'], $complete_telephone_number, 'telephone');
	
	if ($count_im_num == 0 || $buyback_email_verify_check != 1 || $buyback_phone_verify_check != 1) {
		$split_order_listing = '<tr>
									<td width="100" valign="top" colspan="2">
										<div style="color:red;" class="langInfoSizeMedium langInfoLineHeight1_5">'.
										WARNING_PLS_CHECK_EMAIL_VERIFIED_AND_IM.
										($count_im_num == 0 ? '<br>'.WARNING_FILL_IN_IM : '') .
										($buyback_email_verify_check != 1 ? '<br>'.sprintf(WARNING_VERIFY_EMAIL_LINK, tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'action=resend')) : '') .
										($buyback_phone_verify_check != 1 ? '<br>'.sprintf(WARNING_VERIFY_PHONE_LINK, tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'verify=phone')) : '') .
										'</div>
									</td>
								</tr>';
	} else {
		$split_order_listing = '<tr>
									<td width="100" valign="top">'.ENTRY_SELLING_QTY.':</td>
									<td valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
										<tr>
											<td valign="top" width="60%">
												<div id="selling_qty"></div>
											</td>
										</tr>
										</table>
									</td>
								</tr>';
	}
} else {
	$split_order_listing = '<tr>
									<td width="100" valign="top">'.ENTRY_SELLING_QTY.':</td>
									<td valign="top">
										<table border="0" width="100%" cellspacing="0" cellpadding="0">
										<tr>
											<td valign="top" width="60%">
												<div id="selling_qty"></div>
											</td>
										</tr>
										</table>
									</td>
								</tr>';
}

$form_values_arr = $_SESSION[$form_session_name];

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link(FILENAME_BUYBACK, '', 'SSL'));

$javascript = 'supplier_xmlhttp.js.php';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

/********************************************************************
	Know when google crawls your site
********************************************************************/
if ( strpos( $_SERVER['HTTP_USER_AGENT'], 'Googlebot' ) !== false && tep_not_null(GOOGLEBOT_CRAWL_NOTIFICATION_EMAIL) ) {
	$google_email_to_array = tep_parse_email_string(GOOGLEBOT_CRAWL_NOTIFICATION_EMAIL);
	
	for ($google_email_cnt=0; $google_email_cnt < count($google_email_to_array); $google_email_cnt++) {
		tep_mail($google_email_to_array[$google_email_cnt]['name'], $google_email_to_array[$google_email_cnt]['email'], 'Googlebot Visit', 'Googlebot has visited your page: '.$_SERVER['REQUEST_URI'].' at ' . date('Y-m-d H:i:s'), STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
}

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>