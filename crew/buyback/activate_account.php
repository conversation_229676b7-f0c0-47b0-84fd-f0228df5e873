<?php
/*
  	$Id: activate_account.php,v 1.7 2008/05/06 11:32:39 weichen Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

	Released under the GNU General Public License
*/

require('includes/application_top.php');

require(DIR_WS_LANGUAGES . $sup_language . '/' . FILENAME_SUPPLIER_SIGNUP);

define('HEADING_TITLE', HEADER_TITLE_ACTIVATE_ACCOUNT);
define('NAVBAR_TITLE', HEADER_TITLE_ACTIVATE_ACCOUNT);

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_ACTIVATE_ACCOUNT;
//Define the javascript file
$javascript = '';

$action = $_REQUEST['action'];

if (tep_not_null($action)) {
	$errorCount = 0;
	switch ($_REQUEST['action']) {
		case 'dir_ac': //by user
			$ac_key = tep_db_prepare_input($_REQUEST['key']);
			$ac_email = tep_db_prepare_input($_REQUEST['email_address']);
			$apply_vip = false;
			
			if (tep_not_null($ac_key) && tep_not_null($ac_email)) {
				$customer_info_select_sql = "	SELECT c.* 
												FROM " . TABLE_CUSTOMERS . " AS c 
												WHERE c.customers_email_address = '" . tep_db_input($ac_email) . "' 
													AND c.account_activated = '0'";
				$customer_info_result_sql = tep_db_query($customer_info_select_sql);
				
				if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
					if ($customer_info_row['serial_number'] == $ac_key) {
						//	Generate 
						//list($pin_number_enc, $pin_number_plain) = tep_gen_random_pin();
						
						//$success = tep_db_perform(TABLE_CUSTOMERS, array('customers_status' => '1', 'email_verified' => 1, 'serial_number' => '', 'customers_pin_number' => $pin_number_enc, 'account_activated' => '1'), 'update', "customers_id='".tep_db_input($customer_info_row['customers_id'])."'");
						//disable of using pin.
						$success = tep_db_perform(TABLE_CUSTOMERS, array('customers_status' => '1', 'email_verified' => 1, 'serial_number' => '', 'account_activated' => '1'), 'update', "customers_id='".tep_db_input($customer_info_row['customers_id'])."'");
						if ($success) {
							tep_db_query("UPDATE " . TABLE_CUSTOMERS_INFO_VERIFICATION . " SET info_verified = '1' WHERE customers_info_value ='" . tep_db_input($customer_info_row['customers_email_address']) . "' AND customers_id = '" . tep_db_input($customer_info_row['customers_id']) . "' AND info_verification_type = 'email'");
							$customer_vip_sql = "SELECT vip_rank_id FROM " . TABLE_CUSTOMERS_VIP . " WHERE  customers_id='".$customer_info_row['customers_id']."' AND vip_rank_id <> 0 AND  vip_buyback_cummulative_point <> 0";
							$customer_vip_result = tep_db_query($customer_vip_sql);
							if(tep_db_num_rows($customer_vip_result) > 0){
								$apply_vip = true;
							}
							
							//	Send China Buyback Customer OK message
							$email_activated = 	tep_mb_convert_encoding(tep_get_email_greeting(tep_mb_convert_encoding($customer_info_row['customers_firstname'], EMAIL_CHARSET, CHARSET), tep_mb_convert_encoding($customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_info_row['customers_gender']), CHARSET, EMAIL_CHARSET) . 
												EMAIL_SIGNUP_STEP2_BODY . "\n\n" . ($apply_vip== true ? EMAIL_SIGNUP_STEP2_VIP_BODY : '') .
												EMAIL_SIGNUP_FOOTER;
							tep_mail(tep_mb_convert_encoding($customer_info_row['customers_firstname'] . ' ' . $customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_info_row['customers_email_address'], EMAIL_SIGNUP_STEP2_SUBJECT, $email_activated, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
							// done till here
							
							//	Send Supplier OK Message
							$email_activated = sprintf(EMAIL_ADMIN_SIGNUP_SUCCESS_BODY, $ac_email)
							      	 		   . EMAIL_SIGNUP_FOOTER;
							
							$admin_email_to_array = tep_parse_email_string(SUPPLIER_EMAIL_ACTIVATION);
							for ($admEmailCnt=0; $admEmailCnt < count($admin_email_to_array); $admEmailCnt++) {
								tep_mail($admin_email_to_array[$admEmailCnt]['name'], $admin_email_to_array[$admEmailCnt]['email'], EMAIL_SIGNUP_STEP2_SUBJECT, $email_activated, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
							}
							
							$messageStack->add_session($content, TEXT_ACTIVATION_SUCCESS, 'success');
							tep_redirect(tep_href_link(FILENAME_ACTIVATE_ACCOUNT, 'action=suc_ac'));
						} else {
							$messageStack->add_session($content, TEXT_ACTIVATION_FAILURE);
							$errorCount++;
						}
					} else {
						$messageStack->add_session($content, TEXT_INCORRECT_ACTIVATION_CODE);
						$errorCount++;
					}
				} else {
					$messageStack->add_session($content, TEXT_EMAIL_NOT_EXIST_OR_ALREADY_ACTIVATED);
					$errorCount++;
				}
			} else {
				$messageStack->add_session($content, TEXT_INCOMPLETE_FIELDS);
				$errorCount++;
			}
			
			break;
	}
	
	if ($errorCount > 0) {
		tep_redirect(tep_href_link(FILENAME_ACTIVATE_ACCOUNT, tep_get_all_get_params(array('action'))));
	}
}

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_ACTIVATE_ACCOUNT, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>