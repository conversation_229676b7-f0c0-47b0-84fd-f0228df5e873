<?php
/*
  	$Id: buyback.php,v 1.45 2009/07/29 04:45:13 keepeng.foong Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

	Released under the GNU General Public License
*/

require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback.php');
include_once(DIR_WS_CLASSES . 'buyback_supplier.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_CLASSES . 'concurrent_process.php');

if (!tep_session_is_registered('customer_id')) {
	$messageStack->add_session(CONTENT_LOGIN, TEXT_MUST_LOGIN);
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

//to avoid order flooding.
$max_num_orders = 10;				//max buyback orders per user that is in pending status or processing with zero received. 
$max_num_orders_per_product = 1;	//max buyback orders per user & per product that is in pending status or processing with zero received. 

//dealing type array
$dealing_type_array = array('ofp_deal_on_game' => TEXT_DEALING_TYPE_ON_GAME,
                            'ofp_deal_on_mail' => TEXT_DEALING_TYPE_ON_MAIL
                            );

function tep_send_buyback_notification_email($buyback_request_group_id, $total_buyback, $buyback_product_list, $admin_buyback_product_list, $wbb_input_comment) {
    global $currencies, $customer_id, $messageStack, $content, $PAYMENT_STATUS_ARRAY;
    
    //Send notification email
	$customer_result = tep_db_query("SELECT customers_firstname,customers_lastname,customers_email_address, customers_gender from ".TABLE_CUSTOMERS." where customers_id='".$customer_id."';");
	$customer_row = tep_db_fetch_array($customer_result);
	
	$customer_name = tep_mb_convert_encoding($customer_row['customers_firstname']." ".$customer_row['customers_lastname'], EMAIL_CHARSET, CHARSET);
	$customer_greeting_name = tep_mb_convert_encoding(tep_get_email_greeting(tep_mb_convert_encoding($customer_row['customers_firstname'], EMAIL_CHARSET, CHARSET), tep_mb_convert_encoding($customer_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_row['customers_gender']), CHARSET, EMAIL_CHARSET);
	$wbb_input_comment = tep_mb_convert_encoding($wbb_input_comment, EMAIL_CHARSET, CHARSET);
	
	$email_content = 	$customer_greeting_name . 
						EMAIL_NEW_BUYBACK_BODY . 
						sprintf(EMAIL_NEW_BUYBACK_ORDER_NUMBER, $buyback_request_group_id) . "\n" .
						sprintf(EMAIL_NEW_BUYBACK_DATE_ORDERED, tep_date_long(date("Y-m-d")." 00:00:00")) . "\n" .
						sprintf(EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL, $customer_row['customers_email_address']) . "\n\n" .
						EMAIL_NEW_BUYBACK_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . $buyback_product_list .
						EMAIL_SEPARATOR . "\n" . 
 						sprintf(EMAIL_NEW_BUYBACK_ORDER_TOTAL, defined('EMAIL_DISPLAY_CURRENCY') ? constant('EMAIL_DISPLAY_CURRENCY') : constant('DISPLAY_CURRENCY'), $currencies->apply_currency_exchange($total_buyback, DISPLAY_CURRENCY)) . "\n\n" .
 						EMAIL_NEW_BUYBACK_COMMENTS . "\n" . $wbb_input_comment . "\n\n" . 
 						EMAIL_NEW_BUYBACK_STATUS . "\n" .
 						EMAIL_BUYBACK_ORDER_GUIDE . "\n\n" .
 						EMAIL_NEW_BUYBACK_ORDER_CLOSING . "\n\n" .
						EMAIL_NEW_BUYBACK_ORDER_FOOTER;
	
	$admin_email_content = 	$customer_greeting_name . 
							EMAIL_NEW_BUYBACK_BODY . 
							sprintf(EMAIL_NEW_BUYBACK_ORDER_NUMBER, $buyback_request_group_id) . "\n" .
							sprintf(EMAIL_NEW_BUYBACK_DATE_ORDERED, tep_date_long(date("Y-m-d")." 00:00:00")) . "\n" .
							sprintf(EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL, $customer_row['customers_email_address']) . "\n\n" .
							EMAIL_NEW_BUYBACK_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . $admin_buyback_product_list .
							EMAIL_SEPARATOR . "\n" . 
	 						sprintf(EMAIL_NEW_BUYBACK_ORDER_TOTAL, defined('EMAIL_DISPLAY_CURRENCY') ? constant('EMAIL_DISPLAY_CURRENCY') : constant('DISPLAY_CURRENCY'), $currencies->apply_currency_exchange($total_buyback, DISPLAY_CURRENCY)) . "\n\n" .
	 						EMAIL_NEW_BUYBACK_COMMENTS . "\n" . $wbb_input_comment . "\n\n" . 
	 						EMAIL_NEW_BUYBACK_STATUS . "\n" .
	 						EMAIL_BUYBACK_ORDER_GUIDE . "\n\n" .
	 						EMAIL_NEW_BUYBACK_ORDER_CLOSING .
							EMAIL_NEW_BUYBACK_ORDER_FOOTER;
	
	//calculate new statistics
	$today_total_buyback = 0;
    $today_total_cancel_buyback = 0;
    $percentage = 0;
    $today_date = date('Y-m-d');
    $buyback_order_stat_select_sql = "  SELECT buyback_status_id  
                                        FROM ". TABLE_BUYBACK_REQUEST_GROUP ."
                                        WHERE customers_id = '". $customer_id ."'
                                            AND DATE_FORMAT(buyback_request_group_date, '%Y-%m-%d') = CURDATE()";
    $buyback_order_stat_result_sql = tep_db_query($buyback_order_stat_select_sql);
    while ($buyback_order_stat_row = tep_db_fetch_array($buyback_order_stat_result_sql)) {
        $today_total_buyback++;
        
        if ($buyback_order_stat_row['buyback_status_id'] == 4) {
            $today_total_cancel_buyback++;
        }
    }
    
    if ($today_total_cancel_buyback > 0 && $today_total_buyback > 0) {
        $percentage = ($today_total_cancel_buyback / $today_total_buyback) * 100;
        if ($percentage > 50) {
            $stat_cancel_email_content = sprintf(EMAIL_STAT_CUSTOMER_NAME, $customer_name) . "\n" .
                                         sprintf(EMAIL_STAT_CUSTOMER_EMAIL, $customer_row['customers_email_address']) . "\n" .
                                         sprintf(EMAIL_STAT_DATE, $today_date) . "\n\n" .
                                         EMAIL_STAT_TITLE . "\n" .
                                         sprintf(EMAIL_STAT_FORMULAR, $today_total_cancel_buyback, $today_total_buyback, number_format($percentage, 2, '.', ''));
            
            $admin_email_to_array = tep_parse_email_string(BUYBACK_ALERT_EMAIL);
        	for ($i=0; $i < count($admin_email_to_array); $i++) {
        		tep_mail($admin_email_to_array[$i]['name'], $admin_email_to_array[$i]['email'], EMAIL_STAT_SUBJECT, $stat_cancel_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
        	}
        }
    }
    
	$admin_email_to_array = tep_parse_email_string(PURCHASE_TEAM_EMAIL);

	for ($i=0; $i < count($admin_email_to_array); $i++) {
		tep_mail($admin_email_to_array[$i]['name'], $admin_email_to_array[$i]['email'], sprintf(EMAIL_NEW_BUYBACK_SUBJECT, $buyback_request_group_id), $admin_email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
	 
	$jr_email_to_array = tep_parse_email_string(JUNIOR_PURCHASE_TEAM_EMAIL);

	for ($i=0; $i < count($jr_email_to_array); $i++) {
		tep_mail($jr_email_to_array[$i]['name'], $jr_email_to_array[$i]['email'], sprintf(EMAIL_NEW_BUYBACK_SUBJECT, $buyback_request_group_id), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
	
	if (eregi('^[a-zA-Z0-9._-]+@[a-zA-Z0-9-]+\.[a-zA-Z.]{2,5}$',  $customer_row['customers_email_address'])) {
		tep_mail($customer_name, $customer_row['customers_email_address'], sprintf(EMAIL_NEW_BUYBACK_SUBJECT, $buyback_request_group_id), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	}
}

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_BUYBACK;
//Define the javascript file
$javascript = 'supplier_xmlhttp.js';

//Creating session key based on filename so we know where it got its values.
$form_session_name = constant('FILENAME_BUYBACK');
if (!tep_session_is_registered($form_session_name)) {
	//tep_session_unregister($form_session_name);
	$$form_session_name = array();
	tep_session_register($form_session_name);
}

$form_values_arr = $_SESSION[$form_session_name];

$wbb_input_game_select = 0;
$wbb_input_product_select = 0;
$wbb_input_qty = 0;
$wbb_error_message = '';
$wbb_notice_message = '';
$wbb_error_found = '';
$wbb_notice_found = '';
$wbb_hidden_products_id = 0;
$wbb_hidden_unit_price = 0;
$top_cat_id = 0;
$upper_min_qty = 0;

if (isset($_GET['action'])) {
	$action = $_GET['action'];
} else {
	$action = 'update';
}
 


$errorCount = 0;
switch ($action) {
	case 'confirm':
		//Validating update2
		$errorCount = 0;
		
		if ($_POST) {
			include("includes/addon/captcha/securimage.php");
			$img = new Securimage();
			$valid = $img->check($_POST['captcha_code']);
			
			if ($valid == false) {
			    $errorCount++;
		  	 	$messageStack->add_session($content, ERROR_INVALID_CODE);
			}
			
			if ($_POST['wbb_input_character_name']) {
				$_SESSION[$form_session_name]['wbb_input_character_name'] = tep_db_prepare_input($_POST['wbb_input_character_name']);
			} else {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CHARACTER_NAME));
				$errorCount++;
			}
			
			if ($_POST['wbb_input_dealing_type']) {
			    $_SESSION[$form_session_name]['display_dealing_type_array'][$_POST['wbb_input_dealing_type']] = 1;
        		$_SESSION[$form_session_name]['wbb_input_dealing_type'] = tep_db_prepare_input($_POST['wbb_input_dealing_type']);
			} else {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_DEALING_TYPE));
				$errorCount++;
			}
			
			if ($_POST['wbb_input_delivery_time']) {
				$_SESSION[$form_session_name]['wbb_input_delivery_time'] = tep_db_prepare_input($_POST['wbb_input_delivery_time']);
			} else {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_DELIVERY_TIME));
				$errorCount++;
			}
			
			if ($_POST['wbb_input_contact_name']) {
				$_SESSION[$form_session_name]['wbb_input_contact_name'] = tep_db_prepare_input($_POST['wbb_input_contact_name']);
			} else {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CONTACT_NAME));
				$errorCount++;
			}
			if ($_POST['wbb_input_contact_no']) {
				$_SESSION[$form_session_name]['wbb_input_contact_no'] = tep_db_prepare_input($_POST['wbb_input_contact_no']);
			} else {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CONTACT_NO));
				$errorCount++;
			}
			if ($_POST['wbb_textarea_comments']) {
				$_SESSION[$form_session_name]['wbb_textarea_comments'] = tep_db_prepare_input($_POST['wbb_textarea_comments']);
			} else {
				$_SESSION[$form_session_name]['wbb_textarea_comments'] = '';
			}
			
			$form_values_arr = $_SESSION[$form_session_name];
		}
		
		if ((int)$form_values_arr['wbb_hidden_products_id'] < 1) {
			$errorCount++;
			$messageStack->add_session($content, TEXT_INVALID_SEVER_SELECTION);
		}
		
		if ($errorCount > 0) {
			tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=update2'));
		} else {
			$first_page_error = false;
			$buybackSupplierObj = new buyback_supplier($form_values_arr['wbb_input_game_select'], $form_values_arr['wbb_hidden_products_id']);
			$buybackSupplierObj->calculate_offer_price();
			
			$system_min_qty = (int)$buybackSupplierObj->products_arr[$form_values_arr['wbb_hidden_products_id']]['min_qty'];
			$upper_min_qty = $buybackSupplierObj->products_arr[$form_values_arr['wbb_hidden_products_id']]['upper_min_qty'];
			$system_max_qty = (int)$buybackSupplierObj->products_arr[$form_values_arr['wbb_hidden_products_id']]['max_qty'];
			
			if (!tep_game_open_for_buyback($form_values_arr['wbb_input_game_select'])) {
				$messageStack->add_session($content, ERROR_BUYBACK_NOT_BUYBACK_GAME);
				$first_page_error = true;
			} else if ($buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status']=='0' || !$buybackSupplierObj->products_arr[$form_values_arr['wbb_hidden_products_id']]['is_buyback']) {
				$messageStack->add_session($content, TEXT_NOT_ACCEPT_BUYBACK);
				$first_page_error = true;
			}
			
			if ($form_values_arr['wbb_input_qty'] > 0) {
				if (is_numeric($upper_min_qty) && $upper_min_qty > 0) {
					if ( (	$form_values_arr['wbb_input_qty'] >= $system_min_qty 
							&& $form_values_arr['wbb_input_qty'] <= $upper_min_qty)
						|| $form_values_arr['wbb_input_qty'] == $system_max_qty) {
						;
					} else {
						$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
						$first_page_error = true;
					}
				} else {
					if ($form_values_arr['wbb_input_qty'] < $system_min_qty 
						|| $form_values_arr['wbb_input_qty'] > $system_max_qty) {
						$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
						$first_page_error = true;
					}
				}
			} else {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_QUANTITY));
				$first_page_error = true;
			}
			
            //Check customer's total awaiting orders
            $buyback_order_accepted = tep_accept_buyback_order((int)$_SESSION['customer_id'], $max_num_orders);
			
        	if (!$buyback_order_accepted) {
                $messageStack->add_session($content, TEXT_FORM_MAX_ORDERS_REACHED);
               	$first_page_error = true;
        	} else {
            	//Check customer's total awaiting orders for this product.
	            $buyback_order_accepted = tep_accept_buyback_order((int)$_SESSION['customer_id'], $max_num_orders_per_product, $form_values_arr['wbb_hidden_products_id']);
            	
	            if (!$buyback_order_accepted) {
	                $messageStack->add_session($content, TEXT_FORM_STILL_PROCESSING);
	                $first_page_error = true;
	            } else {	// If there is any Pending and Unprocessed Processing Order using the same Sender Character Name in same realm
	            	if (tep_share_sender_id($form_values_arr['wbb_input_character_name'], $form_values_arr['wbb_hidden_products_id'])) {
	            		$messageStack->add_session($content, ERROR_BUYBACK_SENDER_ID_EXISTS);
	                	$first_page_error = true;
	            	}
	            }
        	}
        	
        	// Temporary Processing to prevent insert unwanted record;
			$matchcase = md5($form_values_arr['wbb_hidden_products_id'].':~:'.$system_max_qty);
			$concurrentProcessObj = new concurrent_process(FILENAME_BUYBACK, $matchcase, (int)$form_values_arr['wbb_input_qty']);
			
			//usleep(rand(500000, 2000000)); // Delay execution in microseconds
			usleep(1500000);
			if($concurrentProcessObj->concurrent_process_matching()) {	// If there is > 1 orders matched
				//if (array_sum($concurrentProcessObj->extra_info_arr) > $max_qty) {
					if (!$concurrentProcessObj->concurrent_process_match_insert_id()) {
						$first_page_error = true;
						$messageStack->add_session($content, ERROR_SUBMITTED_BY_OTHER_USER);
					}
				//}
			}
        	
        	if ($first_page_error)	{
        		//$concurrentProcessObj->concurrent_process_cleanup();
				usleep(1500000); // For make sure to run $concurrentProcessObj->concurrent_process_matching() b4 update or delete the record;
				$update_temp_process_sql = "UPDATE LOW_PRIORITY " . TABLE_TEMP_PROCESS . " SET 
											page_name = '".FILENAME_BUYBACK.":DONE', 
											match_case = '".$form_values_arr['wbb_hidden_products_id'].':~:'.$system_max_qty."' 
											WHERE temp_id = '".(int)$concurrentProcessObj->concurrent_process_insert_id."'";
				tep_db_query($update_temp_process_sql);
				tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=update'));
        	}
		}
		
		$margin_cfg_array = tep_get_cfg_setting($form_values_arr['wbb_hidden_products_id'], 'product', 'BUYBACK_PROFIT_MARGIN');
		
		// Create buyback order
		$delivery_time_str = date('Y-m-d H:i:s', $form_values_arr['wbb_input_delivery_time']);
    	$y = array('buyback_request_group_id' => 0,
    			   'customers_id' => (int)$_SESSION['customer_id'],
    			   'buyback_request_group_date' => 'now()',
    			   'buyback_request_group_expiry_date' => $delivery_time_str,
    			   'remote_addr' => $_SERVER['REMOTE_ADDR'],
				   'currency' => DISPLAY_CURRENCY,
				   'currency_value' => $currencies->get_value(DISPLAY_CURRENCY, 'buy'),
    			   'buyback_request_group_comment' => $form_values_arr['wbb_textarea_comments'],
    			   'buyback_request_group_user_type' => '1',
				   'buyback_request_contact_name' => $form_values_arr['wbb_input_contact_name'],
				   'buyback_request_contact_telephone' => $form_values_arr['wbb_input_contact_no'],
				   'buyback_request_group_site_id' => SITE_ID,
				   'buyback_request_order_type' => '0'
    				);
    	tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $y);
    	$buyback_request_group_id = tep_db_insert_id();
		
		$order_unit_price = (double)$buybackSupplierObj->products_arr[$form_values_arr['wbb_hidden_products_id']]['avg_offer_price'];
    	$wbb_total_price = $order_unit_price * (int)$form_values_arr['wbb_input_qty'];
    	// $form_values_arr['wbb_total_price'];
		
		$unit_selling_price = (double)$buybackSupplierObj->products_arr[$form_values_arr['wbb_hidden_products_id']]['customer_price'];
		$profit_margin = $unit_selling_price > 0 ? ( ($unit_selling_price - $order_unit_price) / $unit_selling_price ) * 100 : '--';
		$profit_margin = sprintf('%.3f', $profit_margin);
		
		if ($profit_margin < (double)$margin_cfg_array['BUYBACK_PROFIT_MARGIN']) {
			$cell_span_style = ' style="color:#ff0000;" ';
		} else {
			$cell_span_style = ' style="color:#000000;" ';
		}
		
		$profit_margin = $profit_margin . '%';
		
		unset($y);
		$y = array('buyback_request_group_id' => $buyback_request_group_id,
				   'products_id' => $form_values_arr['wbb_hidden_products_id'],
				   'buyback_request_quantity' => $form_values_arr['wbb_input_qty'],
				   'buyback_amount' => $wbb_total_price,
				   'buyback_comment' => $form_values_arr['wbb_textarea_comments'],
				   'buyback_sender_character' => $form_values_arr['wbb_input_character_name'],
				   'buyback_dealing_type' => $form_values_arr['wbb_input_dealing_type'],
				   'buyback_unit_price' => $order_unit_price
				   );
		tep_db_perform(TABLE_BUYBACK_REQUEST, $y);
		
		// Insert buyback history comment
		$buyback_history_data_array = array('buyback_status_id' => '1',
    			   							'buyback_request_group_id' => $buyback_request_group_id,
    			   							'date_added' => 'now()',
    			   							'customer_notified' => '1',
    			   							'set_as_buyback_remarks' => '1'
    			   							);
    	// Auto insert remark if this is delivery by mail
    	if ($form_values_arr['wbb_input_dealing_type'] == 'ofp_deal_on_mail') {
    		$pending_order_comment_select_sql = "	SELECT orders_comments_text 
													FROM " . TABLE_ORDERS_COMMENTS . "
													WHERE orders_comments_id = '122'";
			$pending_order_comment_result_sql = tep_db_query($pending_order_comment_select_sql);
			
			if ($pending_order_comment_row = tep_db_fetch_array($pending_order_comment_result_sql)) {
				$buyback_history_data_array['comments'] = $pending_order_comment_row['orders_comments_text'];
			}
    	}
    	tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
		
		$cat_path = tep_output_generated_category_path($form_values_arr['wbb_hidden_products_id'], 'product');
		$buyback_product_list = $form_values_arr['wbb_input_qty'].' x '.$cat_path.' = ' . (defined('EMAIL_DISPLAY_CURRENCY') ? constant('EMAIL_DISPLAY_CURRENCY') : constant('DISPLAY_CURRENCY')) . ' ' . $currencies->apply_currency_exchange((double)$wbb_total_price, DISPLAY_CURRENCY)."\n";
		
		$select_supplier_info = "SELECT c.customers_firstname, c.customers_lastname, c.customers_telephone, c.customers_email_address, c.customers_dob, 
										c.customers_gender, ab.entry_street_address, ab.entry_city, ab.entry_state, c.customers_mobile, c.customers_msn, 
										c.customers_yahoo, c.customers_qq, c.customers_icq 		
								FROM " . TABLE_CUSTOMERS . " AS c 
								INNER JOIN " . TABLE_ADDRESS_BOOK . " AS ab
									ON(c.customers_id=ab.customers_id) 
								WHERE c.customers_id=".(int)$_SESSION['customer_id'];
								
		$select_supplier_info_result = tep_db_query($select_supplier_info);
		$select_supplier_info_row = tep_db_fetch_array($select_supplier_info_result);
		$supplier_info_array = array (	'buyback_request_group_id' => $buyback_request_group_id,
										'buyback_order_info_customer_firstname' => $select_supplier_info_row['customers_firstname'],
										'buyback_order_info_customer_lastname' => $select_supplier_info_row['customers_lastname'],
										'buyback_order_info_customer_telephone' => $select_supplier_info_row['customers_telephone'],
										'buyback_order_info_customer_email' => $select_supplier_info_row['customers_email_address'],
										'buyback_order_info_customer_dob' => $select_supplier_info_row['customers_dob'],
										'buyback_order_info_customer_gender' => $select_supplier_info_row['customers_gender'],
										'buyback_order_info_customer_address' => $select_supplier_info_row['entry_street_address'],
										'buyback_order_info_customer_city' => $select_supplier_info_row['entry_city'],
										'buyback_order_info_customer_state' => $select_supplier_info_row['entry_state'],
										'buyback_order_info_customer_mobile' => $select_supplier_info_row['customers_mobile'],
										'buyback_order_info_customer_msn' => $select_supplier_info_row['customers_msn'],
										'buyback_order_info_customer_yahoo' => $select_supplier_info_row['customers_yahoo'],
										'buyback_order_info_customer_qq' => $select_supplier_info_row['customers_qq'],
										'buyback_order_info_customer_icq' => $select_supplier_info_row['customers_icq']
									);
		tep_db_perform(TABLE_BUYBACK_ORDER_INFO, $supplier_info_array);
		
		$admin_buyback_product_list = 	'<table border="0" cellspacing="2" cellpadding="2">'.
							  			'	<tr>'.
							  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px">Product</td>'.
							  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Maximum Qty</td>'.
							 			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Forecast Available Qty</td>'.
										'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Forecast Actual Qty</td>'.
							  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Price (USD)</td>' .
							  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Unit Selling Price (USD)</td>'.
							  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Profit Margin</td>'.
							  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px">First List Selling Quantity</td>'.
							  			'		<td style="background-color:#FFD073; color:#000099; font-size:11px" align="center">Amount (USD)</td>'.
							  			'	</tr>'.
										'	<tr>'.
							  			'		<td style="background-color:#DFDFDF; color:#000; font-size:11px"><span '.$cell_span_style.'>'.$cat_path.'</span></td>'.
							  			'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$form_values_arr['wbb_hidden_products_id']]['max_inventory_space'].'</span></td>'.
										'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$form_values_arr['wbb_hidden_products_id']]['forecast_available_qty'].'</span></td>'.
										'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$buybackSupplierObj->products_arr[$form_values_arr['wbb_hidden_products_id']]['forecast_actual_qty'].'</span></td>'.
								  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$order_unit_price.'</span></td>' .
								  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$unit_selling_price.'</span></td>' .
								  		'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$profit_margin.'</span></td>'.
										'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="center"><span '.$cell_span_style.'>'.$form_values_arr['wbb_input_qty'].'</span></td>'.
										'		<td style="background-color:#DFDFDF; color:#000; font-size:11px" align="right"><span '.$cell_span_style.'>'.$wbb_total_price.'</span></td>'.
										'	</tr>'.
										'</table>';
		
	   	tep_send_buyback_notification_email($buyback_request_group_id, $wbb_total_price, $buyback_product_list, $admin_buyback_product_list, $form_values_arr['wbb_textarea_comments']);
        unset($_SESSION[$form_session_name]);
        tep_session_unregister($form_session_name);
		
		//$concurrentProcessObj->concurrent_process_cleanup();
		usleep(1500000); // For make sure to run $concurrentProcessObj->concurrent_process_matching() b4 update or delete the record;
		$update_temp_process_sql = "UPDATE LOW_PRIORITY " . TABLE_TEMP_PROCESS . " SET 
									page_name = '".FILENAME_BUYBACK.":DONE', 
									match_case = '".$form_values_arr['wbb_hidden_products_id'].':~:'.$system_max_qty."' 
									WHERE temp_id = '".(int)$concurrentProcessObj->concurrent_process_insert_id."'";
		tep_db_query($update_temp_process_sql);
		
		tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=confirm_msg'));
		
		break;
	case 'update2':
		$errorCount = 0;
		$game_cat_id = '';
		
		if (isset($_POST['wbb_input_game_select'])) {
			$_SESSION[$form_session_name]['wbb_input_game_select'] = tep_db_prepare_input($_POST['wbb_input_game_select']);
		} else if (!isset($_SESSION[$form_session_name]['wbb_input_game_select'])) {
			$errorCount++;
		}
		
		if (isset($_POST['wbb_input_product_select']) || isset($_SESSION[$form_session_name]['wbb_input_product_select'])) {
			if (isset($_POST['wbb_input_product_select'])) {
				$_SESSION[$form_session_name]['wbb_input_product_select'] = tep_db_prepare_input($_POST['wbb_input_product_select']);
				$_SESSION[$form_session_name]['wbb_hidden_products_id'] = $_SESSION[$form_session_name]['wbb_input_product_select'];
			}
			//Get cat names for the summary
			$categories_select_sql = "SELECT categories_id
									  FROM ". TABLE_CATEGORIES ." 
									  WHERE categories_id='" . tep_db_input($_SESSION[$form_session_name]['wbb_input_game_select']) . "'";
			$categories_result_sql = tep_db_query($categories_select_sql);
			if ($categories_row = tep_db_fetch_array($categories_result_sql)) {
				$buyback_main_cat_array = tep_get_buyback_main_cat_info($categories_row['categories_id']);
				$game_cat_id = $buyback_main_cat_array['id'];
			} else {
				$messageStack->add_session($content, TEXT_INVALID_SEVER_SELECTION);
				$errorCount++;
			}
		} else {
			$errorCount++;
		}
		
		$selected_game_id = $_SESSION[$form_session_name]['wbb_input_game_select'];
		$selected_product_id = $_SESSION[$form_session_name]['wbb_input_product_select'];
		
		if (!$errorCount) {
			$buybackSupplierObj = new buyback_supplier($selected_game_id, $selected_product_id);
			$buybackSupplierObj->calculate_offer_price();
			
			$system_min_qty = (int)$buybackSupplierObj->products_arr[$selected_product_id]['min_qty'];
			$upper_min_qty = $buybackSupplierObj->products_arr[$selected_product_id]['upper_min_qty'];
			$system_max_qty = (int)$buybackSupplierObj->products_arr[$selected_product_id]['max_qty'];
			$system_unit_price = (double)$buybackSupplierObj->products_arr[$selected_product_id]['avg_offer_price'];
			$system_unit_price_localised = $currencies->do_raw_conversion($system_unit_price, true, DISPLAY_CURRENCY);	/*Localised*/
	    	
	    	if (!tep_game_open_for_buyback($selected_game_id)) {
				$messageStack->add_session($content, ERROR_BUYBACK_NOT_BUYBACK_GAME);
				$errorCount++;
			} else if ($buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status']=='0' || !$buybackSupplierObj->products_arr[$selected_product_id]['is_buyback']) {
				$messageStack->add_session($content, TEXT_NOT_ACCEPT_BUYBACK);
				$errorCount++;
			}
			
			if (isset($_POST['wbb_input_qty'])) {
				$_SESSION[$form_session_name]['wbb_input_qty'] = (int)$_POST['wbb_input_qty'];
			} else if (isset($_SESSION[$form_session_name]['wbb_input_qty'])) {
				;
			} else {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_QUANTITY));
				$errorCount++;
			}
			
			if (is_numeric($upper_min_qty) && $upper_min_qty > 0) {
				if ( (	$_SESSION[$form_session_name]['wbb_input_qty'] >= $system_min_qty 
						&& $_SESSION[$form_session_name]['wbb_input_qty'] <= $upper_min_qty)
					|| $_SESSION[$form_session_name]['wbb_input_qty'] == $system_max_qty) {
					;
				} else {
					$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
					$errorCount++;
				}
			} else {
				if ($_SESSION[$form_session_name]['wbb_input_qty'] < $system_min_qty 
					|| $_SESSION[$form_session_name]['wbb_input_qty'] > $system_max_qty) {
					$messageStack->add_session($content, TEXT_QUANTITY_INVALID);
					$errorCount++;
				}
			}
			
            //Check customer's total awaiting orders
            $buyback_order_accepted = tep_accept_buyback_order((int)$_SESSION['customer_id'], $max_num_orders);
			
        	if (!$buyback_order_accepted) {
                $messageStack->add_session($content, TEXT_FORM_MAX_ORDERS_REACHED);
                $errorCount++;
        	} else {
            	//Check customer's total awaiting orders for this product.
	            $buyback_order_accepted = tep_accept_buyback_order((int)$_SESSION['customer_id'], $max_num_orders_per_product, $_SESSION[$form_session_name]['wbb_input_product_select']);
            	
	            if (!$buyback_order_accepted) {
	                $messageStack->add_session($content, TEXT_FORM_STILL_PROCESSING);
	                $errorCount++;
	            }
        	}
			
			$_SESSION[$form_session_name]['wbb_hidden_unit_price'] = $system_unit_price_localised;
			$_SESSION[$form_session_name]['wbb_hidden_product_name'] = $buybackSupplierObj->products_arr[$selected_product_id]['cat_path'];
			
			$_SESSION[$form_session_name]['wbb_total_price'] = number_format(((int) $_SESSION[$form_session_name]['wbb_input_qty'] * $_SESSION[$form_session_name]['wbb_hidden_unit_price']), DISPLAY_PRICE_DECIMAL, '.', '');
		}
		
		if ($errorCount > 0) {
			tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=update'));
		}
		//ok no errors
		
		//Full name and contact no
		$default_full_name = '';
		$default_contact_no = '';
		$customer_info_select_sql = "select customers_firstname, customers_lastname, customers_telephone, customers_mobile from ".TABLE_CUSTOMERS." where customers_id='{$_SESSION['customer_id']}'";
		$customer_info_result_sql = tep_db_query($customer_info_select_sql);
		while ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
			$default_full_name = "{$customer_info_row['customers_firstname']} {$customer_info_row['customers_lastname']}";
			$default_contact_no = (tep_not_null($customer_info_row['customers_mobile'])) ? $customer_info_row['customers_mobile'] : $customer_info_row['customers_telephone'];
		}

		//time difference between each option
		$interval_mins = 5;
		$num_options = 5;		
		
		//Start with this time. Use Server time for updating database, but display in gmt.
		$time_unix_server = mktime(date('H'), date('i')+30); //+30 minutes

		$time_unix_site = time()+ (8 * 60 * 60) + (30*60); //+30 minutes
		$time_text_site = gmdate('Y-m-d H:i:s', $time_unix_site); 

		$delivery_time_arr =  array(array('id'=>'', 'text'=>PULL_DOWN_DEFAULT));
		$delivery_time_arr[] =  array('id'=> $time_unix_server, 'text'=> $time_text_site);
		
		for ($i=0; $i<($num_options-1); $i++) {
			$time_unix_server += $interval_mins * 60;
			$time_unix_site += $interval_mins * 60;

			$time_text_site = gmdate('Y-m-d H:i:s', $time_unix_site);
			$delivery_time_arr[] =  array('id'=> $time_unix_server, 'text'=> $time_text_site);
		}
		
		//get dealing type
		if (tep_not_null($game_cat_id)) {
		    $_SESSION[$form_session_name]['display_dealing_type_array'] = '';
		    $_SESSION[$form_session_name]['trading_place'] = '';
		    $dealing_type_select_sql = "SELECT buyback_setting_value
		                                FROM ". TABLE_BUYBACK_SETTING ." 
		                                WHERE buyback_setting_reference_id = '". (int)$game_cat_id ."'
		                                    AND buyback_setting_table_name = 'buyback_categories'
		                                    AND buyback_setting_key = 'ofp_delivery_option'";
		    $dealing_type_result_sql = tep_db_query($dealing_type_select_sql);
	        $dealing_type_row = tep_db_fetch_array($dealing_type_result_sql);
	        
	        if (tep_not_null($dealing_type_row['buyback_setting_value'])) {
		        $buyback_setting_value_array = explode(",", $dealing_type_row['buyback_setting_value']);
				
		        if (in_array('ofp_deal_on_game', $buyback_setting_value_array)) {
	            	$trading_place_sql = "	SELECT buyback_setting_value
	            							FROM ". TABLE_BUYBACK_SETTING ."
	            							WHERE buyback_setting_reference_id = '". (int)$game_cat_id ."'
	                                    	AND buyback_setting_table_name = 'buyback_categories'
	                                    	AND buyback_setting_key = 'ofp_trading_place'";
	            	$trading_place_result_sql = tep_db_query($trading_place_sql);
        			$trading_place_row = tep_db_fetch_array($trading_place_result_sql);    	
	            	$_SESSION[$form_session_name]['trading_place'] = $trading_place_row['buyback_setting_value'];		
	            }
        	}
		}
		break;
	case 'update':
		$wbb_game_list_arr = tep_get_game_list_arr();
		break;
	case 'favourites_link':
		$_SESSION[$form_session_name]['wbb_input_game_select'] = tep_db_prepare_input($_GET['gcat']);
		$_SESSION[$form_session_name]['wbb_input_product_select'] = tep_db_prepare_input($_GET['pid']);
		tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action', 'gcat', 'pid')). 'action=update'));
		
		break;
}

$form_values_arr = $_SESSION[$form_session_name];

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_BUYBACK, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>