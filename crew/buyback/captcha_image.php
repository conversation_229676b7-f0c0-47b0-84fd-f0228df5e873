<?php
error_reporting(null);
/*
============================
QuickCaptcha 1.0 - A bot-thwarting text-in-image web tool.
Copyright (c) 2006 Web 1 Marketing, Inc.

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.
*/

/*
Modified by Nick Oct'06
*/
require('includes/application_top.php');

//---------------------------------------------------------------------------------
//-Start Settings
//---------------------------------------------------------------------------------

//First key will always be the background color, other colors will exclude first key.
//All subsequent colors must contrast sharply with first color to improve readability
//So to change bgcolor, just move that color to index[0]
$captcha_color_pallette_arr = array();
$captcha_color_pallette_arr[] = array('r' => '255', 'g' => '255', 'b' => '255'); //white

$captcha_color_pallette_arr[] = array('r' => '051', 'g' => '051', 'b' => '051'); //black
$captcha_color_pallette_arr[] = array('r' => '255', 'g' => '051', 'b' => '051'); //red
$captcha_color_pallette_arr[] = array('r' => '255', 'g' => '102', 'b' => '000'); //orange
$captcha_color_pallette_arr[] = array('r' => '000', 'g' => '153', 'b' => '051'); //green
$captcha_color_pallette_arr[] = array('r' => '153', 'g' => '051', 'b' => '204'); //purple
$captcha_color_pallette_arr[] = array('r' => '000', 'g' => '000', 'b' => '255'); //blue
$captcha_color_pallette_arr[] = array('r' => '102', 'g' => '051', 'b' => '000'); //dark wood
//$captcha_color_pallette_arr[] = array('r' => '102', 'g' => '255', 'b' => '000'); //fluoroscent green
//$captcha_color_pallette_arr[] = array('r' => '102', 'g' => '255', 'b' => '255'); //cyan
//$captcha_color_pallette_arr[] = array('r' => '255', 'g' => '051', 'b' => '255'); //electric purple
//$captcha_color_pallette_arr[] = array('r' => '255', 'g' => '102', 'b' => '255'); //purple
//$captcha_color_pallette_arr[] = array('r' => '255', 'g' => '153', 'b' => '153'); //wood
//$captcha_color_pallette_arr[] = array('r' => '255', 'g' => '204', 'b' => '000'); //gold
//$captcha_color_pallette_arr[] = array('r' => '255', 'g' => '255', 'b' => '000'); //yellow
//$captcha_color_pallette_arr[] = array('r' => '204', 'g' => '255', 'b' => '000'); //fluoroscent green
//$captcha_color_pallette_arr[] = array('r' => '204', 'g' => '204', 'b' => '255'); //light purple

$cnum = array();
$security_code = $_SESSION['sup_security_code'];

for ($i=0; $i < strlen($security_code); $i++) {
    $cnum[$i] = $security_code[$i];
}

//---------------------------------------------------------------------------------
//-end Settings
//---------------------------------------------------------------------------------

// Keep #'s reasonable.
$captcha_lines_min_thickness = max(1,CAPTCHA_LINES_MIN_THICKNESS);
$captcha_lines_max_thickness = min(20,CAPTCHA_LINES_MAX_THICKNESS);
// Make radii into height/width
$captcha_ellipses_min_radius = CAPTCHA_ELLIPSES_MIN_RADIUS * 2;
$captcha_ellipses_max_radius = CAPTCHA_ELLIPSES_MAX_RADIUS * 2;
// Renormalize contrast
$captcha_contrast = 255 * (CAPTCHA_CONTRAST / 100.0);
$o_contrast = 1.3 * $captcha_contrast;

$font = 4; //gd library only provides font 1->5

//$width = 15 * imagefontwidth (5);
$width = 9.5 * imagefontwidth ($font);
//$width = 10.5 * imagefontwidth ($font);
//$height = 2.5 * imagefontheight (5);
$height = 1.5 * imagefontheight ($font);

//Always defaults as black background
$image = imagecreatetruecolor ($width, $height);

imagealphablending($image, true);

//Set the background here.
$bgColor = imagecolorallocate($image, $captcha_color_pallette_arr[0]['r'], $captcha_color_pallette_arr[0]['g'], $captcha_color_pallette_arr[0]['b']);
imagefill($image, 0, 0, $bgColor);

$white = imagecolorallocatealpha($image,255,204,255,0);
//--??--imagecolorallocate($image, $white);

// Add string to image
$rotated = imagecreatetruecolor (70, 70);
for ($i = 0; $i < CAPTCHA_STRINGLENGTH; $i++) {
	$buffer = imagecreatetruecolor (20, 20);
	$buffer2 = imagecreatetruecolor (40, 40);

	// Create character
    $c_key = mt_rand(1, count($captcha_color_pallette_arr)-1);
	$chColor = imagecolorallocate ($buffer, $captcha_color_pallette_arr[$c_key]['r'], $captcha_color_pallette_arr[$c_key]['g'], $captcha_color_pallette_arr[$c_key]['b']);
	imagestring($buffer, $font, 0, 0, $cnum[$i], $chColor);

	// Resize character
//	imagecopyresized($buffer2, $buffer, 0, 0, 0, 0, 25 + mt_rand(0,12), 25 + mt_rand(0,12), 20, 20);
	imagecopyresized($buffer2, $buffer, 0, 0, 0, 0, 28, 32 , 20, 20);

	// Rotate characters a little
	//$angle_of_character = mt_rand(-25, 25);
	//$angle_of_character = mt_rand(-5, 5);
	$angle_of_character = 0;

	$rotated = imagerotate($buffer2, $angle_of_character, imagecolorallocatealpha($buffer2,0,0,0,0));
	imagecolortransparent ($rotated, imagecolorallocatealpha($rotated,0,0,0,0));

	// Move characters around a little
//	$y = mt_rand(1, 3); //y axis
	$y = 1;
//	$x += mt_rand(2, 6);//x axis
	$x += 1;

	imagecopymerge ($image, $rotated, $x, $y, 0, 0, 40, 40, 100);
//	$x += 22; //increment for character spacing
	$x += 13;

	imagedestroy ($buffer);
	imagedestroy ($buffer2);
}

// Draw polygons
if (CAPTCHA_NUM_POLYGONS > 0) for ($i = 0; $i < CAPTCHA_NUM_POLYGONS; $i++) {
	$vertices = array (
		mt_rand(-0.25*$width,$width*1.25),mt_rand(-0.25*$width,$width*1.25),
		mt_rand(-0.25*$width,$width*1.25),mt_rand(-0.25*$width,$width*1.25),
		mt_rand(-0.25*$width,$width*1.25),mt_rand(-0.25*$width,$width*1.25)
	);
	$color = imagecolorallocatealpha ($image, mt_rand(0,$o_contrast), mt_rand(0,$o_contrast), mt_rand(0,$o_contrast), $captcha_object_opacity);
	imagefilledpolygon($image, $vertices, 3, $color);
}

// Draw random circles
if (CAPTCHA_NUM_ELLIPSES > 0) for ($i = 0; $i < CAPTCHA_NUM_ELLIPSES; $i++) {
	$x1 = mt_rand(0,$width);
	$y1 = mt_rand(0,$height);
	$color = imagecolorallocatealpha ($image, mt_rand(0,$o_contrast), mt_rand(0,$o_contrast), mt_rand(0,$o_contrast), $captcha_object_opacity);
//	$color = imagecolorallocate($image, mt_rand(0,$o_contrast), mt_rand(0,$o_contrast), mt_rand(0,$o_contrast));
	imagefilledellipse($image, $x1, $y1, mt_rand($captcha_ellipses_min_radius, $captcha_ellipses_max_radius), mt_rand($captcha_ellipses_min_radius,$captcha_ellipses_max_radius), $color);
}

// Draw random lines
if (CAPTCHA_NUM_LINES > 0) for ($i = 0; $i < CAPTCHA_NUM_LINES; $i++) {
	$x1 = mt_rand(-$width*0.25,$width*1.25);
	$y1 = mt_rand(-$height*0.25,$height*1.25);
	$x2 = mt_rand(-$width*0.25,$width*1.25);
	$y2 = mt_rand(-$height*0.25,$height*1.25);
	$color = imagecolorallocatealpha ($image, mt_rand(0,$o_contrast), mt_rand(0,$o_contrast), mt_rand(0,$o_contrast), $captcha_object_opacity);
	imagesetthickness ($image, mt_rand($captcha_lines_min_thickness, $captcha_lines_max_thickness));
	imageline($image, $x1, $y1, $x2, $y2 , $color);
}

// Draw random dots
if (CAPTCHA_NUM_DOTS > 0) for ($i = 0; $i < CAPTCHA_NUM_DOTS; $i++) {
	$x1 = mt_rand(0,$width);
	$y1 = mt_rand(0,$height);

	$color = imagecolorallocatealpha ($image, mt_rand(0,$o_contrast), mt_rand(0,$o_contrast), mt_rand(0,$o_contrast),$captcha_object_opacity);
	imagesetpixel($image, $x1, $y1, $color);
}
header('Content-type: image/png');
header('Cache-Control: no-cache');
header('Pragma: no-cache');
header('Expires: -1');
imagepng($image);
imagedestroy($image);
?>