<?
require('includes/application_top.php');

function populatePages($id, $select, $page=0) {
	if ($select == 0) {
		return '';
	} else {
		$infolinks_contents_select_sql = "	SELECT * 
											FROM " . TABLE_INFOLINKS_CONTENTS . " 
											WHERE infolinks_id = '" . tep_db_input($id) . "' 
											ORDER BY infolinks_contents_page ASC;";
		$infolinks_contents_result_sql = tep_db_query($infolinks_contents_select_sql);
		
	  	$count = tep_db_num_rows($infolinks_contents_result_sql);
	  	
		if ($count == 1) {
			return '';
		} else if ($count>1) {
			$output = 'Pages: ';
		} else {
			$output = '';
		}
		
		$nextpage = $page+1;
		$prevpage = $page-1;	
		
		if ($page > 1) {
			$infolinks_page_select_sql = "	SELECT infolinks_contents_id 
											FROM " . TABLE_INFOLINKS_CONTENTS . " 
											WHERE infolinks_id = '" . tep_db_input($id) . "' 
												AND infolinks_contents_page = '" . tep_db_input($prevpage) . "';";
			$infolinks_page_result_sql = tep_db_query($infolinks_page_select_sql);
			
			if ($infolinks_page_row = tep_db_fetch_array($infolinks_page_result_sql)) {
				$output .= '<a href="' . tep_href_link(FILENAME_INFOLINKS, tep_get_all_get_params(array('id', 'content_id')) . "id=".$id."&content_id=".(int)$infolinks_page_row['infolinks_contents_id'], 'NONSSL') . '" class="pageResults"><u>[< Prev]</u></a>'."&nbsp;";
			}
		}
		
		if ($count) {
			for ($i=0; $i < $count; ++$i) {
				$infolinks_contents_row = tep_db_fetch_array($infolinks_contents_result_sql);
				
				if ($select == (int)$infolinks_contents_row['infolinks_contents_id']) {
					$output .= '<b>'.(int)$infolinks_contents_row['infolinks_contents_page'].'</b>'."&nbsp;";
				} else {
					$output .= '<a href="' . tep_href_link(FILENAME_INFOLINKS, tep_get_all_get_params(array('id', 'content_id')) . "id=".$id."&content_id=".(int)$infolinks_contents_row['infolinks_contents_id'], 'NONSSL') . '" class="pageResults"><u>'.(int)$infolinks_contents_row['infolinks_contents_page'].'</u></a>'."&nbsp;";
				}
			}
			
			$infolinks_next_page_select_sql = "	SELECT infolinks_contents_id 
												FROM " . TABLE_INFOLINKS_CONTENTS . " 
												WHERE infolinks_id = '" . tep_db_input($id) . "' 
													AND infolinks_contents_page = '" . tep_db_input($nextpage) . "';";
			$infolinks_next_page_result_sql = tep_db_query($infolinks_next_page_select_sql);
			
			if ($infolinks_next_page_row = tep_db_fetch_array($infolinks_next_page_result_sql)) {
				$next='<a href="' . tep_href_link(FILENAME_INFOLINKS, tep_get_all_get_params(array('id', 'content_id')) . "id=".$id."&content_id=".(int)$infolinks_next_page_row['infolinks_contents_id'], 'NONSSL') . '" class="pageResults"><u>[Next >]</u></a>'."&nbsp;";
			}
			
			return $output.$next;
		} else {
			return '';
		}
	}
}

$id = (int)$_GET['id'];
$content_id = (int)$_GET['content_id'];

$infolinks_select_sql = "SELECT * FROM " . TABLE_INFOLINKS . " WHERE infolinks_id = '" . tep_db_input($id) . "';";
$infolinks_result_sql = tep_db_query($infolinks_select_sql);
$rowMain = tep_db_fetch_array($infolinks_result_sql);

if ($content_id == 0) {
	$sql = mysql_query("SELECT * FROM infolinks_contents where infolinks_id='".$id."' and infolinks_contents_page='1';");
	
	if (mysql_num_rows($sql))	$row=mysql_fetch_array($sql);
	$content_id=(int)$row['infolinks_contents_id'];
}

$sql = mysql_query("SELECT * FROM infolinks_contents where infolinks_contents_id='".$content_id."';");
if (mysql_num_rows($sql)) {
	$row = mysql_fetch_array($sql);
	$curpageno = (int)$row['infolinks_contents_page'];
	$isempty = false;
} else {
	$curpageno = 0;
	$isempty = true;  
}

define("PAGES_LINK",populatePages($id,$content_id,$curpageno));

require(DIR_WS_LANGUAGES . $language . '/' . FILENAME_INFOLINKS);

if (!tep_session_is_registered('customer_id')) 	$navigation->set_snapshot();

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_INFOLINKS, '', 'NONSSL'));
 
$content = CONTENT_INFOLINKS;
unset($content_template);

if (tep_not_null($_GET['action']) && $_GET['action'] == 'order_history') {
    require(DIR_WS_CONTENT . CONTENT_INFOLINKS . '.tpl.php');
} else {
    require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
    require(DIR_WS_INCLUDES . 'application_bottom.php');
}
?>