<?
/*
  	$Id: application_top.php,v 1.21 2009/03/13 10:46:49 weichen Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

/***********************************************************
2007-01-30 nick 
	(Line:287)$sup_lng->set_language('zh');
***********************************************************/

// start the timer for the page parse time log
define('PAGE_PARSE_START_TIME', microtime());

// Set the level of error reporting
//error_reporting(E_ALL & ~E_NOTICE);

$HTTP_SERVER_VARS = $_SERVER;
$HTTP_POST_VARS = $_POST;
$HTTP_GET_VARS = $_GET;
$HTTP_COOKIE_VARS = $_COOKIE;

// Check if register_globals is enabled.
// Since this is a temporary measure this message is hardcoded. The requirement will be removed before 2.2 is finalized.
if (function_exists('ini_get')) {
	ini_get('register_globals') or exit('FATAL ERROR: register_globals is disabled in php.ini, please enable it!');
}

// Set the local configuration parameters - mainly for developers
if (file_exists('includes/local/configure.php')) include('includes/local/configure.php');

// Include application configuration parameters
require('includes/configure.php');

// Define the project version
define('PROJECT_VERSION', 'osCommerce 2.2-MS2');

// set the type of request (secure or not)
$request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';

// set php_self in the local scope
$PHP_SELF = (isset($HTTP_SERVER_VARS['PHP_SELF']) ? $HTTP_SERVER_VARS['PHP_SELF'] : $HTTP_SERVER_VARS['SCRIPT_NAME']);

$Sep = (strstr(DIR_FS_DOCUMENT_ROOT, ":/")?";":":") ;
$GLOBALS["_PEAR_Path_"] = DIR_FS_DOCUMENT_ROOT . "pear" ;
ini_set("include_path", $GLOBALS["_PEAR_Path_"]. $Sep.".".$Sep .$_SERVER['DOCUMENT_ROOT']) ;

// Used in the "Backup Manager" to compress backups
define('LOCAL_EXE_GZIP', '/usr/bin/gzip');
define('LOCAL_EXE_GUNZIP', '/usr/bin/gunzip');
define('LOCAL_EXE_ZIP', '/usr/local/bin/zip');
define('LOCAL_EXE_UNZIP', '/usr/local/bin/unzip');

// include the list of project filenames
require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
require(DIR_WS_INCLUDES . 'database_tables.php');

// customization for the design layout
define('BOX_WIDTH', 160); // how wide the boxes should be in pixels (default: 125)

// Define how do we update currency exchange rates
// Possible values are 'oanda' 'xe' or ''
define('CURRENCY_SERVER_PRIMARY', 'oanda');
define('CURRENCY_SERVER_BACKUP', 'xe');

define('DISPLAY_PRICE_DECIMAL', 4);

// include the database functions
require(DIR_WS_FUNCTIONS . 'database.php');

// make a connection to the database... now
tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
$cat_conf_array = array();
//First look in general configuration
$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
while ($configuration = tep_db_fetch_array($configuration_query)) {
	$cat_conf_array[$configuration['cfgKey']] = $configuration['cfgValue'];
}

//Overwrite with root cat configuration
$categories_configuration_query = tep_db_query('select categories_configuration_key as cfgKey, categories_configuration_value as cfgValue from ' . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_id=0; ");
while ($categories_configuration = tep_db_fetch_array($categories_configuration_query)) {
	$cat_conf_array[$categories_configuration['cfgKey']] = $categories_configuration['cfgValue'];
}
//Overwrite with configuration for cat in cPath
if (isset($cPath) && $cPath != '') {
	$cat_path_array = explode('_', $cPath);
	for ($i=0; $i < count($cat_path_array); $i++) {
		$categories_configuration_query = tep_db_query('select categories_configuration_key as cfgKey, categories_configuration_value as cfgValue from ' . TABLE_CATEGORIES_CONFIGURATION . " WHERE categories_id='".$cat_path_array[$i]."'; ");
		while ($categories_configuration = tep_db_fetch_array($categories_configuration_query)) {
			$cat_conf_array[$categories_configuration['cfgKey']] = $categories_configuration['cfgValue'];
		}
	}
}
//Define once here to avoid complaints of double declaration
foreach ($cat_conf_array as $cfgKey => $cfgValue) {
   	define($cfgKey, $cfgValue);
}

// define our general functions used application-wide
require(DIR_WS_FUNCTIONS . 'general.php');
require(DIR_WS_FUNCTIONS . 'html_output.php');
//Admin begin
require(DIR_WS_FUNCTIONS . 'password_funcs.php');
//Admin end

// Get particular categories configuration setting(s)
require(DIR_WS_FUNCTIONS . 'configuration.php');

// initialize the logger class
require(DIR_WS_CLASSES . 'logger.php');

//intialize the customers security class
require(DIR_WS_CLASSES . 'customers_security.php');

// include navigation history class
require(DIR_WS_CLASSES . 'navigation_history.php');

// some code to solve compatibility issues
require(DIR_WS_FUNCTIONS . 'compatibility.php');

// BOF: Down for Maintenance except for admin ip
if (EXCLUDE_ADMIN_IP_FOR_MAINTENANCE != getenv('REMOTE_ADDR')){
	if (DOWN_FOR_MAINTENANCE=='true' and !strstr($PHP_SELF, DOWN_FOR_MAINTENANCE_FILENAME)) { tep_redirect(tep_href_link(DOWN_FOR_MAINTENANCE_FILENAME)); }
}

// do not let people get to down for maintenance page if not turned on
if (DOWN_FOR_MAINTENANCE=='false' and strstr($PHP_SELF,DOWN_FOR_MAINTENANCE_FILENAME)) {
    tep_redirect(tep_href_link(FILENAME_DEFAULT));
}

//Sessions------------------------------------------------------------------------
    // set the cookie domain
    $cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
    $cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);

    // check to see if php implemented session management functions - if not, include php3/php4 compatible session class
    if (!function_exists('session_start')) {
    	define('PHP_SESSION_NAME', 'osCSupID');
        define('PHP_SESSION_PATH', $cookie_path);
        define('PHP_SESSION_DOMAIN', $cookie_domain);
        define('PHP_SESSION_SAVE_PATH', SESSION_WRITE_DIRECTORY);

        include(DIR_WS_CLASSES . 'sessions.php');
    }

    // define how the session functions will be used
    require(DIR_WS_FUNCTIONS . 'sessions.php');

    // set the session name and save path
    tep_session_name('osCSupID');
    tep_session_save_path(SESSION_WRITE_DIRECTORY);

	// set the session cookie parameters
    if (function_exists('session_set_cookie_params')) {
    	//session_set_cookie_params(0, DIR_WS_CATALOG);
    	session_set_cookie_params(0, $cookie_path, $cookie_domain);
    } elseif (function_exists('ini_set')) {
    	ini_set('session.cookie_lifetime', '0');
        //ini_set('session.cookie_path', DIR_WS_CATALOG);
        ini_set('session.cookie_path', $cookie_path);
	    ini_set('session.cookie_domain', $cookie_domain);
    }
	/*
	if (!$session_is_reloaded) {
		if (isset($HTTP_POST_VARS[tep_session_name()])) {
			tep_session_id($HTTP_POST_VARS[tep_session_name()]);
		} elseif ( ($request_type == 'SSL') && isset($HTTP_GET_VARS[tep_session_name()]) ) {
			tep_session_id($HTTP_GET_VARS[tep_session_name()]);
		} else {
			if (isset($_REQUEST["session_id"])) {
				$cashU_session = explode(':~:', $_REQUEST["session_id"]);
				tep_session_id($cashU_session[0]);
			}
		}
	}
	*/
	define('KEY_SP_LANG', "sp_lang");
	
    $session_started = false;
    if (SESSION_FORCE_COOKIE_USE == 'True') {
        //Force the use of sessions when cookies are only enabled.
    	tep_setcookie('cookie_test', 'please_accept_for_session', time()+60*60*24*30, $cookie_path, $cookie_domain);

        if (isset($HTTP_COOKIE_VARS['cookie_test'])) {
          tep_session_start();
        }
    } elseif (SESSION_BLOCK_SPIDERS == 'True') {
        //Prevent known spiders from starting a session.
        $user_agent = strtolower(getenv('HTTP_USER_AGENT'));
        $spider_flag = false;

        if (tep_not_null($user_agent)) {
        	$spiders = file(DIR_WS_INCLUDES . 'spiders.txt');

          	for ($i=0, $n=sizeof($spiders); $i<$n; $i++) {
            	if (tep_not_null($spiders[$i])) {
              		if (is_integer(strpos($user_agent, trim($spiders[$i])))) {
                		$spider_flag = true;
                		break;
              		}
            	}
          	}
        }
        if ($spider_flag == false) {
        	tep_session_start();
          	$session_started = true;
        }
    } else {
    	tep_session_start();
        $session_started = true;
    }
    
    if (tep_session_is_registered('customer_id')) {
    	$supplier_pref_setting = tep_get_supplier_pref_setting($customer_id);
    } else {
    	if (isset($_COOKIE['ogm_cn']) && isset($_COOKIE['ogm_cn']['un']) && isset($_COOKIE['ogm_cn']['uc'])) {
			$cookie_email_address = $_COOKIE['ogm_cn']['un'];
    		$cookie_password = $_COOKIE['ogm_cn']['uc'];
	      	
	      	// Check if email exists
	      	$customer_login_select_sql = "SELECT customers_id as login_id, customers_firstname as login_firstname, customers_password as login_password, customers_email_address as login_email_address, customers_default_address_id, customers_status, customers_groups_id 
	      								FROM " . TABLE_CUSTOMERS . " 
	      								WHERE customers_status = '1' 
	      									AND account_activated = '1' 
	      									AND customers_email_address = '" . tep_db_input($cookie_email_address) . "' 
	      									AND FIND_IN_SET( '".SITE_ID."', customers_login_sites)";
		    $customer_login_result_sql = tep_db_query($customer_login_select_sql);
			
		    if ($customer_login_row = tep_db_fetch_array($customer_login_result_sql)) {
		        if (md5($customer_login_row['login_email_address'].':'.$customer_login_row['login_password']) == $cookie_password) {
		        	if (SESSION_RECREATE == 'True') {
		          		tep_session_recreate();
		        		
			        	if (tep_session_is_registered('password_forgotten')) {
			          		tep_session_unregister('password_forgotten');
			        	}
						
			        	$customer_id = $customer_login_row['login_id'];
				        $login_first_name = $customer_login_row['login_firstname'];
				        $login_email_address = $customer_login_row['login_email_address'];
						
			        	tep_session_register('customer_id');
			        	tep_session_register('login_first_name');
			        	tep_session_register('login_email_address');
						
			   	        $_SESSION['customers_groups_id'] = $customer_login_row['customers_groups_id'];
						
						$vip_supplier_groups_id = 1;
			   	        $vip_group_select_sql = "	SELECT vip_supplier_groups_id 
			   	        							FROM " . TABLE_CUSTOMERS_VIP . " 
			   	        							WHERE customers_id = '" . tep_db_input($customer_id) . "'";
			   	        $vip_group_result_sql = tep_db_query($vip_group_select_sql);
			   	        if ($vip_group_row = tep_db_fetch_array($vip_group_result_sql)) {
			   	        	$vip_supplier_groups_id = $vip_group_row['vip_supplier_groups_id'];
			   	        }
			   	        tep_session_register('vip_supplier_groups_id');
						
						$supplier_pref_setting = tep_get_supplier_pref_setting($customer_id);
						if (count($supplier_pref_setting)) {
				    		$lang_code_select_sql = "SELECT directory FROM " . TABLE_SUPPLIER_LANGUAGES . " WHERE languages_id = '". $supplier_pref_setting[KEY_SP_LANG] . "'";
				    		$lang_code_result_sql = tep_db_query($lang_code_select_sql);
							if ($lang_code_row = tep_db_fetch_array($lang_code_result_sql)) {
								$sup_language = $lang_code_row['directory'];
				    			$sup_languages_id = $supplier_pref_setting[KEY_SP_LANG];
							}
						}
						
						$customer_login_sql_data_array = array(	'customers_id' => tep_db_prepare_input($customer_id),
																'customers_login_date' => 'now()',
																'customers_login_ua_info' => (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT')),
																'customers_login_ip' => tep_db_prepare_input(tep_get_ip_address()));
						
						tep_db_perform(TABLE_CUSTOMERS_LOGIN_IP_HISTORY, $customer_login_sql_data_array);
		    		}
		    	}
	    	}
		}
	}
	define('SID', tep_session_name() . '=' . tep_session_id());
	$SID = SID;
	
// set the language
	if (!tep_session_is_registered('language')
		|| isset($HTTP_GET_VARS['language']) ) {
		if (!tep_session_is_registered('language')) {
	    	tep_session_register('language');
	      	tep_session_register('languages_id');
		}

	    include(DIR_WS_CLASSES . 'language.php');
	    $lng = new language();

	    if (isset($HTTP_GET_VARS['language']) && tep_not_null($HTTP_GET_VARS['language'])) {
	    	$lng->set_language($HTTP_GET_VARS['language']);
	    } else {
	      	$lng->get_browser_language();
	    }

	    $language = $lng->language['directory'];
	    $languages_id = $lng->language['id'];
	}

	if (!tep_session_is_registered('sup_language')
		|| isset($HTTP_GET_VARS['sup_language'])
		|| (tep_session_is_registered('customer_id') && $supplier_pref_setting[KEY_SP_LANG] != $_SESSION['sup_languages_id'])
		) {
		if (!tep_session_is_registered('sup_language')) {
	    	tep_session_register('sup_language');
	      	tep_session_register('sup_languages_id');
		}

	    include_once(DIR_WS_CLASSES . 'language.php');
	    $sup_lng = new language('', TABLE_SUPPLIER_LANGUAGES);

	    if (isset($HTTP_GET_VARS['sup_language']) && tep_not_null($HTTP_GET_VARS['sup_language'])) {
	    	$sup_lng->set_language($HTTP_GET_VARS['sup_language']);
	    } else {
	    	if (tep_session_is_registered('customer_id')) {
	    		$lang_code_select_sql = "SELECT code FROM " . TABLE_SUPPLIER_LANGUAGES . " WHERE languages_id = '". $supplier_pref_setting[KEY_SP_LANG] . "'";
	    		$lang_code_result_sql = tep_db_query($lang_code_select_sql);
				if ($lang_code_row = tep_db_fetch_array($lang_code_result_sql)) {
					$sup_lng->set_language($lang_code_row['code']);
				} else {
					$sup_lng->get_browser_language();
				}
	    	} else {
	    		$sup_lng->set_language('zh');
	    		//$sup_lng->get_browser_language();
			}
	    }

	    $sup_language = $sup_lng->language['directory'];
	    $sup_languages_id = $sup_lng->language['id'];
	}

	// include the language translations
	require(DIR_WS_LANGUAGES . $sup_language . '.php');
	$current_page = basename($PHP_SELF);
	if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/email_contents.php')) {
		include(DIR_WS_LANGUAGES . $sup_language . '/email_contents.php');
	}
	
	if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/' . $current_page)) {
		include_once(DIR_WS_LANGUAGES . $sup_language . '/' . $current_page);
	}
// currency
    if (!tep_session_is_registered('currency') || isset($HTTP_GET_VARS['currency']) || ( (USE_DEFAULT_LANGUAGE_CURRENCY == 'true') && (LANGUAGE_CURRENCY != $currency) ) ) {
    	if (!tep_session_is_registered('currency')) tep_session_register('currency');

        if (isset($HTTP_GET_VARS['currency'])) {
        	if (!$currency = tep_currency_exists($HTTP_GET_VARS['currency'])) $currency = (USE_DEFAULT_LANGUAGE_CURRENCY == 'true') ? LANGUAGE_CURRENCY : DEFAULT_CURRENCY;
        } else {
          	$currency = (USE_DEFAULT_LANGUAGE_CURRENCY == 'true') ? LANGUAGE_CURRENCY : DEFAULT_CURRENCY;
        }
    }


// navigation history
	if (tep_session_is_registered('navigation')) {
	    if (PHP_VERSION < 4) {
	    	$broken_navigation = $navigation;
	      	$navigation = new navigationHistory;
	      	$navigation->unserialize($broken_navigation);
	    }
	} else {
	    tep_session_register('navigation');
	    $navigation = new navigationHistory;
	}

	if (is_object($navigation)) {
		$navigation->add_current_page();
	}

// customer groups id. applies to supplier group id as well
	if (!tep_session_is_registered('customers_groups_id')) {
		$customers_groups_id = 0; //Anonymous/Public
		tep_session_register('customers_groups_id');
	}
	
	if (!tep_session_is_registered('vip_supplier_groups_id')) {
		$vip_supplier_groups_id = 0; //Anonymous/Public
		tep_session_register('vip_supplier_groups_id');
	}

// define our localization functions
require(DIR_WS_FUNCTIONS . 'localization.php');

// Include validation functions (right now only email address)
require(DIR_WS_FUNCTIONS . 'validations.php');

// setup our boxes
require(DIR_WS_CLASSES . 'table_block.php');
require(DIR_WS_CLASSES . 'box.php');

// Infobox class
require(DIR_WS_CLASSES . 'boxes.php');

// initialize the message stack for output messages
require(DIR_WS_CLASSES . 'message_stack.php');
$messageStack = new messageStack;

// split-page-results
require(DIR_WS_CLASSES . 'split_page_results.php');

// entry/item info classes
require(DIR_WS_CLASSES . 'object_info.php');

// email classes
require(DIR_WS_CLASSES . 'mime.php');
require(DIR_WS_CLASSES . 'email.php');

// file uploading class
require(DIR_WS_CLASSES . 'upload.php');

require(DIR_WS_CLASSES . 'currencies.php');
$currencies = new currencies();
//$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);


// Time Zone class
require_once ('Date.php');

// include the breadcrumb class and start the breadcrumb trail
require(DIR_WS_CLASSES . 'breadcrumb.php');
$breadcrumb = new breadcrumb;
//Show left most breadcrumb, ie. Home.
$breadcrumb->add(tep_image(DIR_WS_ICONS . 'icon_home.gif') . '&nbsp;' . HEADER_TITLE_TOP, tep_href_link(FILENAME_DEFAULT));

// BROWSER AND VERSION
// (must check everything else before Mozilla)
if (preg_match('@Opera(/| )([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[2]);
    define('PMA_USR_BROWSER_AGENT', 'OPERA');
} else if (preg_match('@MSIE ([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[1]);
    define('PMA_USR_BROWSER_AGENT', 'IE');
} else if (preg_match('@OmniWeb/([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[1]);
    define('PMA_USR_BROWSER_AGENT', 'OMNIWEB');
//} else if (ereg('Konqueror/([0-9].[0-9]{1,2})', $HTTP_USER_AGENT, $log_version)) {
// Konqueror 2.2.2 says Konqueror/2.2.2
// Konqueror 3.0.3 says Konqueror/3
} else if (preg_match('@(Konqueror/)(.*)(;)@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[2]);
    define('PMA_USR_BROWSER_AGENT', 'KONQUEROR');
} else if (preg_match('@Mozilla/([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)
           && preg_match('@Safari/([0-9]*)@', $HTTP_USER_AGENT, $log_version2)) {
    define('PMA_USR_BROWSER_VER', $log_version[1] . '.' . $log_version2[1]);
    define('PMA_USR_BROWSER_AGENT', 'SAFARI');
} else if (preg_match('@Mozilla/([0-9].[0-9]{1,2})@', $HTTP_USER_AGENT, $log_version)) {
    define('PMA_USR_BROWSER_VER', $log_version[1]);
    define('PMA_USR_BROWSER_AGENT', 'MOZILLA');
} else {
    define('PMA_USR_BROWSER_VER', 0);
    define('PMA_USR_BROWSER_AGENT', 'OTHER');
}

// set which precautions should be checked
define('WARN_INSTALL_EXISTENCE', 'true');
define('WARN_CONFIG_WRITEABLE', 'true');
define('WARN_SESSION_DIRECTORY_NOT_WRITEABLE', 'true');
define('WARN_SESSION_AUTO_START', 'true');
define('WARN_DOWNLOAD_DIRECTORY_NOT_READABLE', 'true');

//Hardcode this for now
$SESSION_THEME = '1';

if ($SESSION_THEME == '') {
	//define(THEMA_STYLE, "stylesheet.php");

  	//define(THEMA_IMAGES, DIR_WS_IMAGES);
  	//tep_redirect('theme.php');
} else {
	require_once(DIR_WS_CLASSES . 'theme_manager.php');
	$theme_obj = new theme_manager($SESSION_THEME);
	$theme_obj->load_actual_filenames();
  	define('THEMA', DIR_WS_THEME . $SESSION_THEME."/");
  	define('THEMA_STYLE', DIR_WS_THEME . $SESSION_THEME . '/' . T_CSS_MAIN);
  	define('THEMA_RESOURCE', DIR_WS_THEME . $SESSION_THEME . '/');
  	//define(THEMA_IMAGES, DIR_WS_IMAGES . "16/");
  	define('THEMA_IMAGES', DIR_WS_IMAGES);
//
//  	$title_left_image_url = THEMA_RESOURCE . T_TITLE_LEFT;
//  	$title_center_image_url = THEMA_RESOURCE . T_TITLE_CENTER;
//  	$title_right_image_url = THEMA_RESOURCE . T_TITLE_RIGHT;
    $top_center_background_image_url = THEMA_RESOURCE . T_HEADER_REPEAT;
}

//end multiple theme


/*****************************************************************
	CAPTCHA Image
*****************************************************************/

// This string contains allowable characters for the image.
// To reduce confusion, zero and the letter 'o' have been removed,
// and QuickCaptcha is NOT case-sensitive.
//$captcha_acceptedChars = 'A1B2C3D4E5F6G7H8I9JKLMNPQRSTUVWXYZ';
$captcha_acceptedChars = '1234567890';

// Number of characters in image.
define('CAPTCHA_STRINGLENGTH', 5);

// A value between 0 and 100 describing how much color overlap
// there is between text and other objects.  Lower is more
// secure against bots, but also harder to read.
define('CAPTCHA_CONTRAST', 60);

// Various obfuscation techniques.
define('CAPTCHA_NUM_POLYGONS', 0); // Number of triangles to draw.  0 = none
define('CAPTCHA_NUM_ELLIPSES', 1); // Number of ellipses to draw.  0 = none
define('CAPTCHA_NUM_LINES', 2); // Number of lines to draw.  0 = none
define('CAPTCHA_NUM_DOTS', 0); // Number of dots to draw.  0 = none

define('CAPTCHA_LINES_MIN_THICKNESS', 1); // Minimum thickness in pixels of lines
define('CAPTCHA_LINES_MAX_THICKNESS', 3); // Maximum thickness in pixles of lines
define('CAPTCHA_ELLIPSES_MIN_RADIUS', 5); // Minimum radius in pixels of ellipses
define('CAPTCHA_ELLIPSES_MAX_RADIUS', 15); // Maximum radius in pixels of ellipses

// How opaque should the obscuring objects (lines/dots/ellipses/polygons) be. 0 is opaque, 127 is transparent.
$captcha_object_opacity = 75;

/*****************************************************************
	Calculate category path
*****************************************************************/
if (isset($HTTP_GET_VARS['cPath'])) {
	$cPath = $HTTP_GET_VARS['cPath'];
} elseif (isset($HTTP_GET_VARS['products_id']) && !isset($HTTP_GET_VARS['manufacturers_id'])) {
    $cPath = tep_get_product_path($HTTP_GET_VARS['products_id']);
} else {
	$cPath = '';
    //$cPath = $SESSION_THEME;
}

if (tep_not_null($cPath)) {
	$cPath_array = tep_parse_category_path($cPath);
    $cPath = implode('_', $cPath_array);
    $current_category_id = $cPath_array[(sizeof($cPath_array)-1)];
} else {
	$cPath_array = array();
    $current_category_id = 0;
}

require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'vip_groups.php');

// check customer is vip status.
$vip = false; //default set false
$_SESSION['vip_supplier_groups_id'] = 0; //default set to guess
if(tep_session_is_registered('customer_id')) {
	$select_customers_vip_sql = "SELECT vip_supplier_groups_id FROM " . TABLE_CUSTOMERS_VIP . " WHERE customers_id='". $_SESSION['customer_id'] ."'";
	$select_customers_vip_result = tep_db_query($select_customers_vip_sql);
	if($select_customers_vip_row = tep_db_fetch_array($select_customers_vip_result)){
		$_SESSION['vip_supplier_groups_id'] = $select_customers_vip_row['vip_supplier_groups_id'];
		if($select_customers_vip_row['vip_supplier_groups_id'] > 1){ //member = 1, vip=2, vvip=3
			$vip = true;
		}
	}else {
		$_SESSION['vip_supplier_groups_id'] = 1; //default set to member if success login
	}
}

$customers_security_obj = new customers_security($_SESSION['sup_languages_id']);
if (tep_session_is_registered('customer_id')) {
	$customers_security_obj->set_customers_id($_SESSION['customer_id']);
	$date_diff = $customers_security_obj->get_convert_pin_expired_date();
	if (tep_not_null($date_diff) && $date_diff <= 0 && (basename($_SERVER['PHP_SELF'])!= FILENAME_MY_ACCOUNT_MGMT && basename($_SERVER['PHP_SELF'])!= FILENAME_LOGOFF)
		) {
		tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, 'current_step=3'));
	}
}
?>