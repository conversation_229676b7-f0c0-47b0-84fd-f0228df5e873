<?php
if (isset($_SESSION['customer_id'])) {

	$boxHeading = HEADER_TITLE_MY_CREDIT_BALANCE;
	$background_image = DIR_WS_IMAGES . 'box_game1a.gif';
	$heading_title_style = 'class="systemBoxHeading"';

    $boxContent = '<table border="0" width="100%" cellspacing="0" cellpadding="0">';

	$user_credit_balance = tep_user_balance($_SESSION['customer_id'], 'customers');
	if (count($user_credit_balance)) {
		$i = 0;
		foreach ($user_credit_balance as $bal_currency => $bal_amt) {
			$boxContent .= '
						<tr>
							<td colspan="2"><p>' . TEXT_ACCOUNT_TOTAL . ' : <p></td>
						</tr>
						<tr>
							<td><p>' . $currencies->currencies[$bal_currency]['symbol_left'].number_format($bal_amt, 2, '.', ',').$currencies->currencies[$bal_currency]['symbol_right'] . '</p></td>
							<td>
								<a href="'.tep_href_link(FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action', 'cur')) . 'action=show_request&cur='.$bal_currency).'">'.TEXT_WITHDRAW.'</a>
							</td>
						</tr>
						<tr>
							<td colspan="2" background="'.DIR_WS_IMAGES.'space_line2.gif" height="1">'.tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '1').'</td>
		                </tr>';
			$i++;
		}
	}

    $boxContent .= '</table>';

	require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
}

//. tep_draw_form('payment_history_request_' . $i,
//FILENAME_MY_PAYMENT_HISTORY, tep_get_all_get_params(array('action', 'cur')) . 'action=show_request&cur='.$bal_currency, "post")
//. '&nbsp;' . tep_image_submit('', TEXT_WITHDRAW, "name='pyh_button_request_$i'", 'generalBtn')
//.'</form>
//
?>