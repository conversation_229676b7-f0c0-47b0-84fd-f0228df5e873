<!-- categories //-->
<?
if (basename($_SERVER['PHP_SELF']) == FILENAME_BUYBACK && isset($_REQUEST['action']) && tep_not_null($_REQUEST['action'])) {
	return ;
}

if (!tep_session_is_registered('GLOBAL_STORE_BREAK_TIME')) {
	tep_session_register('GLOBAL_STORE_BREAK_TIME');
}

$GLOBAL_STORE_BREAK_TIME = '';

$boxHeading = HEADER_TITLE_CATEGORIES;
$background_image = DIR_WS_IMAGES . 'box_game1a.gif';
$heading_title_style = 'class="systemBoxHeading"';

$first_game_cat_id = '';

$main_cat_select_sql = "SELECT c.categories_id  
						FROM " . TABLE_CATEGORIES . " AS c
						WHERE c.categories_buyback_main_cat = '1'
						ORDER BY c.sort_order";
$main_cat_result_sql = tep_db_query($main_cat_select_sql);
//$parent_id = 0;
if (tep_db_num_rows($main_cat_result_sql)) {
	$main_cat_count = 0;
	$active_game_array = $inactive_game_array = array();
	$reopen_time_array = array();
	$boxContent = '<table border="0" width="100%" cellspacing="0" cellpadding="0">';
	
    while ($main_cat_row = tep_db_fetch_array($main_cat_result_sql)) {
		$categeries_select_sql = "	SELECT c.categories_parent_path, c.parent_id 
									FROM " . TABLE_CATEGORIES . " AS c
									INNER JOIN " . TABLE_CATEGORIES_GROUPS . " as cg
										ON cg.categories_id = c.categories_id
											AND cg.groups_id IN ('0', '1')
									WHERE c.categories_id = '".(int)$main_cat_row['categories_id']."'
										AND c.categories_status = '1'";
		$categeries_result_sql = tep_db_query($categeries_select_sql);
		if ($categeries_row = tep_db_fetch_array($categeries_result_sql)) {
			if ($categeries_row['parent_id'] == 0) {
				$category_id = $main_cat_row['categories_id'];
			} else {
				$cat_parent_path = explode('_',$categeries_row['categories_parent_path']);
				$category_id = $cat_parent_path[1];
			}
			$category_has_product_type_select_sql = "	SELECT custom_products_type_id 
														FROM ".TABLE_CATEGORIES_PRODUCT_TYPES."
														WHERE categories_id = '".(int)$category_id."'
															AND custom_products_type_id='0'";
			$category_has_product_type_result_sql = tep_db_query($category_has_product_type_select_sql);
			if (tep_db_num_rows($category_has_product_type_result_sql)) {
				$active_game_array[] = '<a href="javascript:void(0)" id="game_link_'.$main_cat_row['categories_id'].'" onClick="set_selected_game('.$main_cat_row['categories_id'].', 0)" class="categoryNavigation">'.(defined("DISPLAY_NAME_CAT_ID_".$main_cat_row['categories_id']) ? constant("DISPLAY_NAME_CAT_ID_".$main_cat_row['categories_id']) : strip_tags(tep_get_categories_name($main_cat_row['categories_id']))).'</a>';
			}
		}
	}
	
	if (!count($active_game_array) && count($reopen_time_array))	$GLOBAL_STORE_BREAK_TIME = min($reopen_time_array);
	
	for ($catCnt=0; $catCnt < count($active_game_array); $catCnt++, $main_cat_count++) {
		if ($main_cat_count > 0) {
			$boxContent .= '<tr>
								<td colspan="2" background="'.DIR_WS_IMAGES.'space_line2.gif" height="1">'.tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '1').'</td>
							</tr>';
		}
		
		$boxContent .= '<tr>
							<td width="25">&nbsp;</td>
							<td align="left">'.$active_game_array[$catCnt].'</td>
						</tr>';
	}
	
	for ($catCnt=0; $catCnt < count($inactive_game_array); $catCnt++, $main_cat_count++) {
		if ($main_cat_count > 0) {
			$boxContent .= '<tr>
								<td colspan="2" background="'.DIR_WS_IMAGES.'space_line2.gif" height="1">'.tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '1').'</td>
							</tr>';
		}
		
		$boxContent .= '<tr>
							<td width="25">&nbsp;</td>
							<td align="left">'.$inactive_game_array[$catCnt].'</td>
						</tr>';
	}
	
	$boxContent .= '</table>';
}

require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

?>
<!-- testbox //-->