<!-- testbox //-->
<?
//Build the captcha code
$captcha_security_code_plain = NULL;
$sup_security_code = NULL;

$max = strlen($captcha_acceptedChars)-1;
for($i=0; $i < CAPTCHA_STRINGLENGTH; $i++) {
	$cnum[$i] = $captcha_acceptedChars{mt_rand(0, $max)};
	$captcha_security_code_plain .= $cnum[$i];
}
$sup_security_code = $captcha_security_code_plain;

if (tep_session_is_registered('sup_security_code')) {
    tep_session_unregister('sup_security_code');
}
tep_session_register('sup_security_code');
//-Finish building the captcha code.

$loaded_extensions = get_loaded_extensions();
$show_captcha_mode = 'image';
if (!in_array('gd', $loaded_extensions)) { 
	$show_captcha_mode = 'text';
}

$boxHeading = HEADER_TITLE_LOGIN;
$background_image = DIR_WS_IMAGES . 'box_game1a.gif';
$heading_title_style = 'class="systemBoxHeading"';

$boxContent = tep_draw_form('login', FILENAME_LOGIN, 'action=process');
$boxContent .= '<table border="0" width="100%" cellspacing="0" cellpadding="0">';
$boxContent .= '<tr>
					<tr>
        				<td align="left" class="systemBoxLabel">' . ENTRY_EMAIL_ADDRESS . '</td>
          				<td align="left" class="systemBoxField">' . tep_draw_input_field('email_address', '', 'id="email_address" class="form-login-textfield"') . '</td>
      				</tr>
      				<tr>
	  					<td height="4" colspan="2"></td>
	  				</tr>
	  				<tr>
        				<td align="left" class="systemBoxLabel">' . ENTRY_PASSWORD . '</td>
          				<td align="left" class="systemBoxField">' . tep_draw_password_field('password', '', false, 'id="password" class="form-login-textfield"') . '</td>
      				</tr>
					<tr>
	  					<td height="4" colspan="2"></td>
	  				</tr>
	  				<tr>
        				<td align="left" class="systemBoxLabel">' . TEXT_SECURITY_CODE . '</td>
          				<td align="left">' . tep_draw_input_field('security_code', '', 'size="5" maxlength="5" id="captcha_code" class="form-captcha-textfield"') . ( $show_captcha_mode == 'image' ? tep_image(tep_href_link(FILENAME_CAPTCHA_IMAGE, '', 'SSL', true), TEXT_SECURITY_CODE) : "<span class='title-text'>".$_SESSION['sup_security_code']."</span>" ) . '</td>
      				</tr>
					<tr>
	  					<td height="4" colspan="2"></td>
	  				</tr>
					<tr>
               			<td align="left" valign="top" class="systemBoxLabel" colspan="2">'.tep_draw_checkbox_field('remember_me', '1', false, "id='remember_me'").'&nbsp;'.TEXT_REMEMBER_ME.'&nbsp;'.tep_image_submit(THEMA.'button_login.gif', IMAGE_BUTTON_LOGIN, 'SSL') . '</td>
					</tr>
					<tr>
    		  			<td align="left" colspan="2">
    		  			 	<a class="systemNav" href="'.tep_href_link(FILENAME_SUPPLIER_SIGNUP, '', 'SSL').'">'.HEADER_TITLE_SIGNUP.'</a>
    		  			</td>
    		  		</tr>
    		  		<tr>
                		<td align="left" height="5" colspan="2" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
                	</tr>
                	<tr>
    		  			<td align="left" colspan="2">
    		  			 	<a class="systemNav" href="'.tep_href_link(FILENAME_ACTIVATE_ACCOUNT, '', 'SSL').'">'.HEADER_TITLE_ACTIVATE_ACCOUNT.'</a>
						</td>
					</tr>
					<tr>
                		<td align="left" height="5" colspan="2" width="100%" style="background: url(\''.DIR_WS_IMAGES.'row_seperator.gif'.'\'); background-repeat: repeat-x; background-position: left center;"></td>
                	</tr>
                	<tr>
    		  			<td align="left" colspan="2">
    		  			 	<a class="systemNav" href="'.tep_href_link(FILENAME_PASSWORD_FORGOTTEN, '', 'SSL').'">'.HEADER_TITLE_FORGOT_PASSWORD.'</a>
						</td>
    		  		</tr>
				</table>
                </form>';
require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

?>
<!-- testbox //-->