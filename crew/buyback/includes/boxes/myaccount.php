<!-- testbox //-->
<?
$boxHeading = HEADER_TITLE_MY_ACCOUNT;
$background_image = DIR_WS_IMAGES . 'box_game1a.gif';
$heading_title_style = 'class="systemBoxHeading"';

$parent_id = 0;

    $boxContent = '<table border="0" width="100%" cellspacing="0" cellpadding="0">';
	foreach ($_my_account_files as $filename => $title) {
	    $boxContent .= '
	    					<tr>
	                            <td width="25">&nbsp;</td>
	                            <td align="left"><a href="'.tep_href_link($filename).'" class="systemNav">'.$title.'</a></td>
	        		  		</tr>
	                        <tr>
	                          <td colspan="2" background="'.DIR_WS_IMAGES.'space_line2.gif" height="1">'.tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '1').'</td>
	                        </tr>';
	}
    $boxContent .= '</table>';
require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

?>
<!-- testbox //-->