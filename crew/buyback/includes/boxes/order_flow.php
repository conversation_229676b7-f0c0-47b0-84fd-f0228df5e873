﻿<!-- order_flow box //-->
<?
$boxHeading = '<font color="#FFFFFF">'.HEADER_TITLE_ORDER_FLOW_INFO.'</font>';
$background_image = DIR_WS_IMAGES . 'box_game1a.gif';
$heading_title_style = 'class="systemBoxHeading"';
$heading_title_icon_image = 'blue_bg_grey_icon.gif';
$box_img_prefix = 'box_blue';

$boxContent = '	<table border="0" width="100%" cellspacing="0" cellpadding="0">
               		<tr>
				        <td align="center" bgcolor="#007ACC">
				        	<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0" width="200" height="82" title="order_flow">
				          		<param name="movie" value="http://image.offgamers.com/buybackcn/order_flow.swf" />
				          		<param name="quality" value="high" />
				          		<embed src="http://image.offgamers.com/buybackcn/order_flow.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer" type="application/x-shockwave-flash" width="200" height="82"></embed>
				        	</object>
				      	</td>
				  	</tr>
				  	<tr>
				        <td align="left" bgcolor="#007ACC"><a href="'.tep_href_link(FILENAME_BUYBACK).'" class="box">'.LINK_MAIN_MAKE_ORDER_NOW.'</a></td>
					</tr>
				</table>';

require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

?>
<!-- order_flow box //--> 