<!-- testbox //-->
<?
$boxHeading = "Title";
$background_image = DIR_WS_IMAGES . 'box_game1a.gif';
$heading_title_style = 'class="systemBoxHeading"';

$boxContent = tep_draw_form('test_box_form', 'dummy_result.php', '', 'get');
$boxContent .= '<table border="0" width="100%" cellspacing="0" cellpadding="0">';
$boxContent .= tep_draw_hidden_field("search_in_description","1") . tep_hide_session_id();
$boxContent .= '	<tr>
            			<td height="4"><p>qw kfshd ksdhfasdfaskdjf hasdfa daslkdf hasdflkas dhasd asdflkj shsadkf jhasdaskldfh sdfklashdfaskjdf</p></td>
            		</tr>
					<tr>
               			<td width="118" height="18" align="center">';
$boxContent .= tep_draw_input_field('keywords', '', 'size="15" maxlength="15" class="form-login-textfield"');
$boxContent .= '		</td>
					</tr>
					<tr>
    		  			<td height="4"></td>
    		  		</tr>
					<tr>
						<td align="center">'.tep_image_submit('', 'TEST_BTN_ALT').'</td>
					</tr>
					<tr>
    		  			<td height="8"></td>
    		  		</tr>
					<tr>
    		  			<td align="center"><a href="dummy.php" class="systemNav"> Link </a></td>
    		  		</tr>
				</table>
                </form>';

require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

?>
<!-- testbox //-->