<!-- vip account //-->
<?
if (isset($_SESSION['customer_id'])) {
		
	if($vip){
		$boxHeading = HEADER_TITLE_MY_VIP_ACCOUNT;
		$background_image = DIR_WS_IMAGES . 'box_game1a.gif';
		$heading_title_style = 'class="systemBoxHeading"';
		$right_title = '';
		
		$boxContent = '<table border="0" width="100%" cellspacing="0" cellpadding="0">';
		foreach ($_my_vip_account_files as $vip_filename => $vip_title) {
		$boxContent .= '		<tr>
									<td width="25">&nbsp;</td>
		                            <td align="left"><a href="'.tep_href_link($vip_filename).'" class="menuBoxVIPLink">'.$vip_title.'</a></td>
		        		  		</tr>
		                        <tr>
		                          <td colspan="2" background="'.DIR_WS_IMAGES.'space_line2.gif" height="1">'.tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '1').'</td>
		                        </tr>';
		}
		$boxContent .= '</table>';
		
		$right_title = '<a href="' . tep_href_link(FILENAME_INFOLINKS, 'id=364&content_id=425') . '" class="menuBoxVIPLink">' . TEXT_VIP_ORDER_FLOW . '</a>';
	}
}



require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);

?>
<!-- vip account //-->
