<!-- testbox //-->
<?
$boxHeading = HEADER_TITLE_WELCOME;
$background_image = DIR_WS_IMAGES . 'box_game1a.gif';
$heading_title_style = 'class="systemBoxHeading"';

$boxContent = '<table border="0" width="100%" cellspacing="0" cellpadding="0">';
$boxContent .= '	<tr>
            			<td align="left"><p>'.sprintf(TEXT_WELCOME, $_SESSION["login_first_name"], STORE_NAME).'</p></td>
            		</tr>
                    <tr>
            			<td height="4" align="right">'.tep_image(DIR_WS_IMAGES . 'pixel_trans.gif', '', '', '4').'</td>
            		</tr>
                    <tr>
                      <td height="4">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1').'</td>
                    </tr>
                    <tr>
            			<td align="right">'.tep_image_button(BUTTON_SIGN_OUT, ALT_BUTTON_SIGN_OUT, tep_href_link(FILENAME_LOGOFF)).'</td>
            		</tr>
				</table>';

require(DIR_WS_TEMPLATES . TEMPLATENAME_BOX);
?>
<!-- testbox //-->

