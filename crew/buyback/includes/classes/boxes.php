<?
/*
  	$Id: boxes.php,v 1.1 2007/01/25 14:33:37 nickyap Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

class tableBox {
    var $table_border = '0';
    var $table_width = '100%';
    var $table_cellspacing = '0';
    var $table_cellpadding = '2';
    var $table_parameters = '';
    var $table_row_parameters = '';
    var $table_data_parameters = '';
	var $prepend_tbody = false;
    var $CustomeBoxStyle;
	// class constructor
    function tableBox($contents, $direct_output = false) {
      	$tableBox_string = '<table border="' . tep_output_string($this->table_border) . '" width="' . tep_output_string($this->table_width) . '" cellspacing="' . tep_output_string($this->table_cellspacing) . '" cellpadding="' . tep_output_string($this->table_cellpadding) . '"';
      	if (tep_not_null($this->table_parameters)) $tableBox_string .= ' ' . $this->table_parameters;
      	$tableBox_string .= '>' . "\n";

      	for ($i=0, $n=sizeof($contents); $i<$n; $i++) {
        	if (isset($contents[$i]['form']) && tep_not_null($contents[$i]['form'])) $tableBox_string .= $contents[$i]['form'] . "\n";
        	if ($this->prepend_tbody) {
        		$tableBox_string .= '<tbody ';
	        	if (tep_not_null($this->table_row_parameters)) $tableBox_string .= ' ' . $this->table_row_parameters;
	        	if (isset($contents[$i]['params']) && tep_not_null($contents[$i]['params'])) $tableBox_string .= ' ' . $contents[$i]['params'];
	        	$tableBox_string .= '>' . "\n";
	        	$tableBox_string .= '  <tr>';
        	} else {
        		$tableBox_string .= '  <tr';
	        	if (tep_not_null($this->table_row_parameters)) $tableBox_string .= ' ' . $this->table_row_parameters;
	        	if (isset($contents[$i]['params']) && tep_not_null($contents[$i]['params'])) $tableBox_string .= ' ' . $contents[$i]['params'];
	        	$tableBox_string .= '>' . "\n";
        	}

        	if (isset($contents[$i][0]) && is_array($contents[$i][0])) {
          		for ($x=0, $n2=sizeof($contents[$i]); $x<$n2; $x++) {
            		if (isset($contents[$i][$x]['text']) && tep_not_null($contents[$i][$x]['text'])) {
              			$tableBox_string .= '    <td';
              			if (isset($contents[$i][$x]['align']) && tep_not_null($contents[$i][$x]['align'])) $tableBox_string .= ' align="' . tep_output_string($contents[$i][$x]['align']) . '"';
          				if (isset($contents[$i][$x]['params']) && tep_not_null($contents[$i][$x]['params'])) {
            				$tableBox_string .= ' ' . $contents[$i][$x]['params'];
          				} else if (tep_not_null($this->table_data_parameters)) {
            				$tableBox_string .= ' ' . $this->table_data_parameters;
          				}
          				$tableBox_string .= '>';
          				if (isset($contents[$i][$x]['form']) && tep_not_null($contents[$i][$x]['form'])) $tableBox_string .= $contents[$i][$x]['form'];
          				$tableBox_string .= $contents[$i][$x]['text'];
          				if (isset($contents[$i][$x]['form']) && tep_not_null($contents[$i][$x]['form'])) $tableBox_string .= '</form>';
          				$tableBox_string .= '</td>' . "\n";
            		}
          		}
        	} else {
      			$tableBox_string .= '    <td';
      			if (isset($contents[$i]['align']) && tep_not_null($contents[$i]['align'])) $tableBox_string .= ' align="' . tep_output_string($contents[$i]['align']) . '"';
      			if (isset($contents[$i]['params']) && tep_not_null($contents[$i]['params'])) {
        			$tableBox_string .= ' ' . $contents[$i]['params'];
      			} else if (tep_not_null($this->table_data_parameters)) {
        			$tableBox_string .= ' ' . $this->table_data_parameters;
      			}
      			$tableBox_string .= '>' . $contents[$i]['text'] . '</td>' . "\n";
    		}

    		$tableBox_string .= '  </tr>' . "\n";
    		if ($this->prepend_tbody) $tableBox_string .= '  </tbody>' . "\n";
        	if (isset($contents[$i]['form']) && tep_not_null($contents[$i]['form'])) $tableBox_string .= '</form>' . "\n";
		}

      	$tableBox_string .= '</table>' . "\n";

      	if ($direct_output == true) echo $tableBox_string;

      	return $tableBox_string;
	}
}

class infoBox extends tableBox {
    function infoBox($contents, $UseTransImg='1', $CustomeBoxStyle='', $CustomeRowsStyle='', $CustomeContentsStyle='', $CellPadding=1) {
      	$info_box_contents = array();
      	$info_box_contents[] = array('text' => $this->infoBoxContents($contents, $UseTransImg, $CustomeRowsStyle, $CustomeContentsStyle));
      	$this->table_cellpadding = $CellPadding;
      	$this->table_parameters = $CustomeBoxStyle ? $CustomeBoxStyle : 'class="infoBox"';
      	$this->tableBox($info_box_contents, true);
	}

    function infoBoxContents($contents, $UseTransImg='1', $CustomeRowsStyle='', $CustomeContentsStyle='') {
      	$this->table_cellpadding = '3';
      	$this->table_parameters = $CustomeContentsStyle ? $CustomeContentsStyle : 'class="infoBoxContents"';
      	$info_box_contents = array();
      	if ($UseTransImg)
      		$info_box_contents[] = array(array(	'text' => tep_draw_separator('pixel_trans.gif', '100%', '1')));
      	for ($i=0, $n=sizeof($contents); $i<$n; $i++) {
        	$info_box_contents[] = array(array(	'align' => (isset($contents[$i]['align']) ? $contents[$i]['align'] : ''),
                                           		'form' => (isset($contents[$i]['form']) ? $contents[$i]['form'] : ''),
                                           		'params' => ($CustomeContentsStyle ? $CustomeContentsStyle : 'class="boxText"'),
                                           		'text' => (isset($contents[$i]['text']) ? $contents[$i]['text'] : '')));
      	}
      	if ($UseTransImg)
      		$info_box_contents[] = array(array('text' => tep_draw_separator('pixel_trans.gif', '100%', '1')));
      	return $this->tableBox($info_box_contents);
	}
}

class infoBoxHeading extends tableBox {
	function infoBoxHeading($contents, $left_corner = true, $right_corner = true, $right_arrow = false) {
		$this->table_cellpadding = '0';

//      	if ($left_corner == true) {
//        	$left_corner = tep_image(DIR_WS_IMAGES . 'infobox/corner_left.gif');
//      	} else {
//        	$left_corner = tep_image(DIR_WS_IMAGES . 'infobox/corner_right_left.gif');
//      	}
//      	if ($right_arrow == true) {
//        	$right_arrow = '<a href="' . $right_arrow . '">' . tep_image(DIR_WS_IMAGES . 'infobox/arrow_right.gif', ICON_ARROW_RIGHT) . '</a>';
//      	} else {
//        	$right_arrow = '';
//      	}
//      	if ($right_corner == true) {
//        	$right_corner = $right_arrow . tep_image(DIR_WS_IMAGES . 'infobox/corner_right.gif');
//      	} else {
//        	$right_corner = $right_arrow . tep_draw_separator('pixel_trans.gif', '11', '14');
//      	}

      	$info_box_contents = array();
//      	$info_box_contents[] = array(	array(	'params' => 'height="14" class="infoBoxHeading"',
//                                        	 	'text' => $left_corner),
//                                   		array(	'params' => 'width="100%" height="14" class="infoBoxHeading"',
//                                         		'text' => $contents[0]['text']),
//                                   		array(	'params' => 'height="14" class="infoBoxHeading" nowrap',
//                                         		'text' => $right_corner));

      	$info_box_contents[] = array(	array(	'params' => 'width="100%" height="14" class="infoBoxHeading"',
                                         		'text' => $contents[0]['text']));

      	$this->tableBox($info_box_contents, true);
	}
}

class contentBox extends tableBox {

    function contentBox($contents, $CustomeBoxStyle='', $CustomeContentsStyle='', $CellPadding='1') {
      	$info_box_contents = array();
      	$info_box_contents[] = array('text' => $this->contentBoxContents($contents, $CustomeContentsStyle));
      	$this->table_cellpadding = $CellPadding;
      	$this->CustomeBoxStyle = $CustomeBoxStyle;
      	$this->table_parameters = $CustomeBoxStyle ? $CustomeBoxStyle : 'class="infoBox"';
      	$this->tableBox($info_box_contents, true);

    }

    function contentBoxContents($contents, $CustomeContentsStyle='') {
      	$this->table_cellpadding = '4';
      	$this->table_parameters = $this->CustomeBoxStyle ? $this->CustomeBoxStyle : 'class="infoBoxContents"';
      	return $this->tableBox($contents);
	}
}

class contentBoxHeading extends tableBox {
    function contentBoxHeading($contents) {
      	$this->table_width = '100%';
      	$this->table_cellpadding = '0';

      	$info_box_contents = array();
//      	$info_box_contents[] = array(	array(	'params' => 'height="14" class="infoBoxHeading"',
//                                         		'text' => tep_image(DIR_WS_IMAGES . 'infobox/corner_left.gif')),
//                                   		array(	'params' => ($contents[0]['params'] ? $contents[0]['params'] : ' class="infoBoxHeading" ') . ' height="14  width="100%"' ,
//                                         		'text' => $contents[0]['text']),
//                                   		array(	'params' => 'height="14" class="infoBoxHeading"',
//                                         		'text' => tep_image(DIR_WS_IMAGES . 'infobox/corner_right_left.gif')));

      	$info_box_contents[] = array(	array(	'params' => ($contents[0]['params'] ? $contents[0]['params'] : ' class="infoBoxHeading" ') . ' height="14"  width="100%"' ,
                                         		'text' => $contents[0]['text']) );

      	$this->tableBox($info_box_contents, true);
	}
}

class errorBox extends tableBox {
	function errorBox($contents) {
		$this->table_data_parameters = 'class="errorBox"';
		$this->tableBox($contents, true);
	}
}

class productListingBox extends tableBox {
	function productListingBox($contents, $CustomeStyle='', $prepend_tbody=false, $table_cellspacing='', $table_cellpadding='') {
		$this->table_parameters = ($CustomeStyle) ? $CustomeStyle : 'class="productListing"';

		if ($table_cellspacing)	$this->table_cellspacing = $table_cellspacing;
		if ($table_cellpadding)	$this->table_cellpadding = $table_cellpadding;

		$this->prepend_tbody = $prepend_tbody;
		$this->tableBox($contents, true);
	}
}
?>