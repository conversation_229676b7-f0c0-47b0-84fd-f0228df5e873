<?
/*
  	$Id: breadcrumb.php,v 1.1 2007/01/25 14:33:37 nickyap Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class breadcrumb
{
	var $_trail;
	
    function breadcrumb() {
      	$this->reset();
    }
	
    function reset() {
      	$this->_trail = array();
    }
	
    function add($title, $link = '') {
      	$this->_trail[] = array('title' => $title, 'link' => $link);
    }
	
    function trail($separator = ' - ') {
      	$trail_string = '';
		
      	for ($i=0, $n=sizeof($this->_trail); $i<$n; $i++) {
			if (isset($this->_trail[$i]['link']) && tep_not_null($this->_trail[$i]['link']) && ($i < $n-1)) {
        		if ($this->_trail[$i]['title'] == "Catalog") {
        	   		//do nothing
        		} else {
        			$trail_string .= '<a href="' . $this->_trail[$i]['link'] . '" class="systemNav">' . $this->_trail[$i]['title'] . '</a>';	
        		}
        	} else {
        		if ($this->_trail[$i]['title'] == "Catalog" ) {
        	   		//do nothing
        		} else {
            		$trail_string .= $this->_trail[$i]['title'];
        		}
        	}
	   		if ($this->_trail[$i]['title'] == "Catalog" ) {
	   			// || $this->_trail[$i]['title'] == "Home"
				//do nothing
	   		} else {
          		if (($i+1) < $n) $trail_string .= $separator;
			}
		}
		
      	return $trail_string;
	}
}
?>