<?php
/*
  	$Id: buyback_supplier.php,v 1.25 2009/06/05 09:52:16 keepeng.foong Exp $

  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Venture

  	Released under the GNU General Public License
*/
//require_once(DIR_WS_FUNCTIONS . 'supplier.php');
require_once(DIR_WS_CLASSES . 'buyback_competitor.php');

//Product specific errors defined here.
define('ERROR_NEGATIVE_FORECAST_ACTUAL', "Forecast Actual Quantity cannot be less than zero. ");
define('MESSAGE_INVALID_MAX_INV_SPACE', "Maximum Quantity cannot be less than zero");

class buyback_supplier extends buyback_competitor {
	var $buyback_supplier_settings = array(); //ie. settings for getting average sales
	
  	var $supplier_price_brackets_sets = array();        //All supplier price brackets
  	var $supplier_price_num_sets = 0;
	
  	var $price_bracket_mode_arr = array(0 => '$', 1 => '&#37;');
	var $price_bracket_sign_arr = array(0 => '+', 1 => '-');
	
	var $colour_picker_arr = array('yellow'=>'#FFFF99', 'blue'=>'#66FFFF', 'green'=>'#00FF99', 'red'=>'#FF6699', 'grey'=>'#E5E5E5');
	var $colour_bracket_arr = array();
	
  	var $error_msg_arr = array();           //The complain queue for messageStack. Otherwise always assign messages as fatal.
  	var $pending_orders = 0;
    
  	var $do_calculate_profit = false;
  	
  	var $game_buyback_unit;
  	
  	// for vip module, we need the bracket min qty and ,min_purchase_qty
  	var $vip = false;
  	var $vip_min_qty = 0;
  	var $vip_min_purchse_qty = 0;
  	
  	var $category_id = 0;
  	var $buyback_price_arr = array();
  	
	
    //Public Constructor
    function buyback_supplier($category_id, $products_id=0) {
    	parent::buyback_competitor($category_id, $products_id);
    	
		$this->category_id = $category_id;
        $this->set_supplier_price_brackets();
        $this->set_buyback_list_controller_status();
        $this->set_buyback_supplier_settings();
        $this->set_products_price_brackets();
		$this->set_buyback_product_overwrite_qty();
		$this->game_buyback_unit = $this->get_buyback_qty_unit();

		if (is_numeric($products_id) && $products_id > 0)	$this->set_buyback_qty_unit($products_id);
		//$this->assign_buyback_price();
    }
	
    /*****************************************************************************************************
     * Public methods -  Callable
     *****************************************************************************************************/
    function assign_buyback_supplier_settings($key, $value) {
		$this->buyback_supplier_settings[$key] = $value;
    }
	
    function save_buyback_supplier_settings($setting_key='') {
        //save buyback settings
        if (tep_not_null($setting_key)) {
        	$buyback_setting_select_sql = "	SELECT buyback_setting_value 
        									FROM " . TABLE_BUYBACK_SETTING . " 
    										WHERE buyback_setting_reference_id = '" . tep_db_input($this->category_id) . "' 
                                            	AND buyback_setting_table_name = '".TABLE_BUYBACK_CATEGORIES."' 
                                            	AND buyback_setting_key = '".tep_db_input($setting_key)."'";
            $buyback_setting_result_sql = tep_db_query($buyback_setting_select_sql);
            
            if ($buyback_setting_row = tep_db_fetch_array($buyback_setting_result_sql)) {
            	if (isset($this->buyback_supplier_settings[$setting_key]) && strlen($this->buyback_supplier_settings[$setting_key])) {
            		if ($buyback_setting_row['buyback_setting_value'] != $this->buyback_supplier_settings[$setting_key]) {
            			$buyback_setting_update_sql = "	UPDATE ".TABLE_BUYBACK_SETTING." 
            											SET buyback_setting_value = '" . tep_db_input($this->buyback_supplier_settings[$setting_key]) . "' 
            											WHERE buyback_setting_reference_id = '" . tep_db_input($this->category_id) . "' 
            												AND buyback_setting_table_name = '".TABLE_BUYBACK_CATEGORIES."' 
                                            				AND buyback_setting_key = '".tep_db_input($setting_key)."'";
            			tep_db_query($buyback_setting_update_sql);
            		}
		        }
            } else {
            	if (isset($this->buyback_supplier_settings[$setting_key]) && strlen($this->buyback_supplier_settings[$setting_key])) {
		        	tep_db_perform(TABLE_BUYBACK_SETTING, array('buyback_setting_reference_id' => $this->category_id, 'buyback_setting_table_name' => TABLE_BUYBACK_CATEGORIES, 'buyback_setting_key' => $setting_key, 'buyback_setting_value' => tep_db_prepare_input($this->buyback_supplier_settings[$setting_key])));
		        }
            }
        } else {
	        $buyback_products_setting_delete_sql = "DELETE FROM " . TABLE_BUYBACK_SETTING . " 
	        										WHERE buyback_setting_reference_id='$this->category_id'
	                                                	AND buyback_setting_table_name='".TABLE_BUYBACK_CATEGORIES."' AND buyback_setting_key IN ('".implode("','", array_keys($this->buyback_supplier_settings))."')";
			
	        $buyback_products_setting_result_sql = tep_db_query($buyback_products_setting_delete_sql);
	        foreach ($this->buyback_supplier_settings as $key => $value) {
				if (strlen($value)) {
	            	tep_db_perform(TABLE_BUYBACK_SETTING, array('buyback_setting_reference_id' => $this->category_id, 'buyback_setting_table_name' => TABLE_BUYBACK_CATEGORIES, 'buyback_setting_key' => $key, 'buyback_setting_value' => tep_db_prepare_input($value)));
				}
	        }
	    }
    }
	
    function set_buyback_supplier_settings() {
    	$buyback_supplier_settings = array();
    	$settings_select_sql = "SELECT buyback_setting_key, buyback_setting_value FROM ".TABLE_BUYBACK_SETTING."
    							WHERE buyback_setting_table_name = '".TABLE_BUYBACK_CATEGORIES."'
    							AND buyback_setting_reference_id = '{$this->category_id}'";
    	$settings_result_sql = tep_db_query($settings_select_sql);
    	while ($settings_row = tep_db_fetch_array($settings_result_sql)) {
			$buyback_supplier_settings[$settings_row['buyback_setting_key']] = $settings_row['buyback_setting_value'];
    	}
    	$this->buyback_supplier_settings = $buyback_supplier_settings;
    }
	
	function set_buyback_list_controller_status() {
		$settings_select_sql = "SELECT buyback_setting_value 
								FROM " . TABLE_BUYBACK_SETTING . " 
    							WHERE buyback_setting_table_name = '" . TABLE_BUYBACK_CATEGORIES . "' 
    								AND buyback_setting_reference_id = '".tep_db_input($this->category_id)."' 
    								AND buyback_setting_key = 'ofp_buyback_list_controller'";
    	$settings_result_sql = tep_db_query($settings_select_sql);
    	$settings_row = tep_db_fetch_array($settings_result_sql);
    	
    	if (tep_not_null($settings_row['buyback_setting_value'])) {
    		$this->pending_orders = tep_get_game_pending_buyback($this->category_id, SITE_ID);
    		
			list($min, $max) = explode(',', $settings_row['buyback_setting_value']);
			if ($this->pending_orders >= (int)$max) {
				$this->assign_buyback_supplier_settings('ofp_buyback_list_controller_status', '0');	// Close the list
				$this->save_buyback_supplier_settings('ofp_buyback_list_controller_status');
			} else if ($this->pending_orders <= (int)$min) {
				$this->assign_buyback_supplier_settings('ofp_buyback_list_controller_status', '1');	// Open the list
				$this->save_buyback_supplier_settings('ofp_buyback_list_controller_status');
			}
    	} else {
    		$this->assign_buyback_supplier_settings('ofp_buyback_list_controller_status', '1');	// Always open list
    		$this->save_buyback_supplier_settings('ofp_buyback_list_controller_status');
    	}
	}
	
    function assign_product_supplier_price_bracket_set($products_id, $bracket_set_id) {
		$this->products_arr[$products_id]['bracket_set_id'] = $bracket_set_id;
    }
    
    function save_product_supplier_price_bracket_set() {
		foreach ($this->products_arr as $products_id => $products_arr) {
			if (!isset($products_arr['bracket_set_id'])) {
				continue;
			}
			$buyback_set_assignment_delete_sql = "DELETE FROM ".TABLE_BUYBACK_SETTING." WHERE buyback_setting_table_name='".TABLE_BUYBACK_PRODUCTS."'
													AND buyback_setting_reference_id = '" . tep_db_input($products_id) . "'
													AND buyback_setting_key = 'ofp_supplier_price_bracket_set'";
			tep_db_query($buyback_set_assignment_delete_sql);
			$buyback_set_assignment_arr = array('buyback_setting_value' => $products_arr['bracket_set_id'],
												'buyback_setting_reference_id' => $products_id,
												'buyback_setting_key' => 'ofp_supplier_price_bracket_set',
												'buyback_setting_table_name' => TABLE_BUYBACK_PRODUCTS);
			tep_db_perform(TABLE_BUYBACK_SETTING, $buyback_set_assignment_arr);
		}
    }

    function assign_buyback_product_overwrite_qty($products_id, $overwrite_qty) {
		$this->products_arr[$products_id]['max_inventory_space_overwrite'] = $overwrite_qty;
    }

    function save_buyback_product_overwrite_qty() {
		foreach ($this->products_arr as $products_id => $products_arr) {
			if (!isset($products_arr['max_inventory_space_overwrite'])) {
				continue;
			}
			$product_overwrite_qty_delete_sql = "DELETE FROM ".TABLE_BUYBACK_SETTING." WHERE buyback_setting_table_name='".TABLE_BUYBACK_PRODUCTS."'
													AND buyback_setting_reference_id = '" . tep_db_input($products_id) . "'
													AND buyback_setting_key = 'ofp_buyback_product_overwrite_qty'";
			tep_db_query($product_overwrite_qty_delete_sql);
			$product_overwrite_qty_arr = array('buyback_setting_value' => $products_arr['max_inventory_space_overwrite'],
												'buyback_setting_reference_id' => $products_id,
												'buyback_setting_key' => 'ofp_buyback_product_overwrite_qty',
												'buyback_setting_table_name' => TABLE_BUYBACK_PRODUCTS);
			tep_db_perform(TABLE_BUYBACK_SETTING, $product_overwrite_qty_arr);
		}
    }

    function calculate_offer_price() {
		foreach ($this->products_arr as $products_id => $products_arr) {
			$this->set_product_quantities($products_id);
			$products_arr = $this->products_arr[$products_id];
			$min_buyback_qty = $max_buyback_qty = $back_order_qty = 0;
			
			$max_inventory_space = (int)$products_arr['max_inventory_space'];
			
			$active_supplier_price_bracket_arr = array();
			$active_supplier_price_bracket_key = 0;
//			$active_supplier_price_bracket_mode = 0;
//			$active_supplier_price_bracket_sign = 0;
			
			//Always get min from db
			$min_buyback_qty = (int)$this->supplier_price_brackets_sets[$this->products_arr[$products_id]['bracket_set_id']]['min_qty'];
			
			if ($this->vip) {
				$this->vip_min_qty = $min_buyback_qty;
				$this->vip_min_purchse_qty = (int)$this->supplier_price_brackets_sets[$this->products_arr[$products_id]['bracket_set_id']]['min_purchase_qty'];
			}
			$products_bracket_arr = isset($this->supplier_price_brackets_sets[$products_arr['bracket_set_id']]['brackets']) && is_array($this->supplier_price_brackets_sets[$products_arr['bracket_set_id']]['brackets']) 
									? $this->supplier_price_brackets_sets[$products_arr['bracket_set_id']]['brackets'] 
									: array();
			
			//Look for ideal bracket
			/***********************************************************************************
				1. $max_inventory_space < 0 => Reserve some stock for other buyback system
				2. For BackOrder case, max qty always = BO qty
			***********************************************************************************/
			$i=0;
	        foreach ($products_bracket_arr as $bracket_id => $bracket_arr) {
	        	$bracket_inventory_quantity = ($bracket_arr['bracket_quantity']/100) * $max_inventory_space;
	        	if ($bracket_arr['bracket_quantity'] < 0 
	        		&& $products_arr['forecast_available_qty'] < 0
	        		&& $max_inventory_space > $products_arr['forecast_available_qty']) {
	        		$back_order_qty = $max_buyback_qty = $max_inventory_space < 0 ? $max_inventory_space - $products_arr['forecast_available_qty'] : abs($products_arr['forecast_available_qty']);
	        		
	        		$active_supplier_price_bracket_arr = $bracket_arr;
	            	$active_supplier_price_bracket_key = $i+1;//+1 for display only. cos we dont display 0.
	            	$active_supplier_price_bracket_id = $bracket_id;
	            	
	            	$min_buyback_qty = $this->_suggest_backorder_min_max($products_id, $back_order_qty, $min_buyback_qty);
					
					break;
	        	} else {
					if ($bracket_inventory_quantity > $products_arr['forecast_actual_qty']) {
		            	$active_supplier_price_bracket_arr = $bracket_arr;
		            	$active_supplier_price_bracket_key = $i+1;//+1 for display only. cos we dont display 0.
		            	$active_supplier_price_bracket_id = $bracket_id;
		            	
		            	$max_buyback_qty = $bracket_inventory_quantity - $products_arr['forecast_actual_qty'];
						break;
					}
	        	}
	            $i++;
	        }
			
	        if (!$active_supplier_price_bracket_arr) {
	            //No brackets selected yet. Just grab the largest bracket so we have something. Largest bracket will always show cheapest price.
				end($products_bracket_arr);
            	$active_supplier_price_bracket_id = key($products_bracket_arr);
            	$active_supplier_price_bracket_arr = $products_bracket_arr[$active_supplier_price_bracket_id];
            	$active_supplier_price_bracket_key = count($products_bracket_arr);
            	reset($products_bracket_arr);
            	
				$max_buyback_qty = (float)$max_inventory_space - $products_arr['forecast_actual_qty'];
				
		        if ($max_inventory_space <= $products_arr['forecast_actual_qty']) {
		        	$this->products_arr[$products_id]['is_buyback'] = false;
		        }
	        }

			$max_buyback_qty_is_overwrite = $this->supplier_price_brackets_sets[$this->products_arr[$products_id]['bracket_set_id']]['max_qty_is_overwrite'];
			if ($max_buyback_qty_is_overwrite) {
				$max_buyback_qty = $this->supplier_price_brackets_sets[$this->products_arr[$products_id]['bracket_set_id']]['max_qty'];
			}

			//overwrite/sys defined max (per transaction) still gets checked against the following;

             //Can't be more than remainder inventory space. So just overwrite with the smaller.
            $max_buyback_qty = min($max_buyback_qty, $products_arr['suggest_qty']);

            //Can't be less than the minimum. The minimum imposes the final ceiling. Cos qty may be less than worth the effort.
            //This is more important than making sure MIS not exceeded.
            //So just overwrite with the larger.
            //Its ok if min == max. Ie. Min=500/Max=500
            $max_buyback_qty = max($max_buyback_qty, $min_buyback_qty);

//			$avg_offer_price = 0;

//			$active_supplier_price_bracket_mode = $this->price_bracket_mode_arr[$active_supplier_price_bracket_arr['bracket_mode']];
//			$active_supplier_price_bracket_sign = $this->price_bracket_sign_arr[$active_supplier_price_bracket_arr['bracket_sign']];

//			switch ((int)$active_supplier_price_bracket_arr['bracket_mode']) {
//				case 0://$
//					$bracket_value = $active_supplier_price_bracket_arr['bracket_value'];
//					break;
//				case 1://%
//					$bracket_value = $this->number_format(($active_supplier_price_bracket_arr['bracket_value'] * $products_arr['avg_competitor_price_final'])/100);
//					break;
//			}

//			switch ((int)$active_supplier_price_bracket_arr['bracket_sign']) {
//				case 0://+ve
//					$avg_offer_price = $products_arr['avg_competitor_price_final'] + $bracket_value;
//					break;
//				case 1://-ve
//					$avg_offer_price = $products_arr['avg_competitor_price_final'] - $bracket_value;
//			}

			$this->products_arr[$products_id]['min_qty'] = $min_buyback_qty;
			$this->products_arr[$products_id]['max_qty'] = ceil($max_buyback_qty);
			if (isset($this->products_arr[$products_id]['upper_min_qty']) && is_numeric($this->products_arr[$products_id]['upper_min_qty'])) {
				$this->products_arr[$products_id]['upper_min_qty'] = min($this->products_arr[$products_id]['max_qty'], $this->products_arr[$products_id]['upper_min_qty']);
			}
//			$this->products_arr[$products_id]['avg_offer_price'] = $this->number_format($this->buyback_price_arr[$products_id]);
//			$this->products_arr[$products_id]['avg_offer_price_mode'] = $active_supplier_price_bracket_mode;
//			$this->products_arr[$products_id]['avg_offer_price_sign'] = $active_supplier_price_bracket_sign;
			$this->products_arr[$products_id]['buyback_supplier_price_bracket_id'] = $active_supplier_price_bracket_id;
			$this->products_arr[$products_id]['buyback_supplier_price_bracket_key'] = $active_supplier_price_bracket_key;
			
			if ($this->do_calculate_profit) {
				$this->calculate_profit($products_id);
			}
		}
    }
	
	function _suggest_backorder_min_max($products_id, $bo_qty, $min_qty) {
		$new_min_qty = $min_qty;
		
		$this->products_arr[$products_id]['upper_min_qty'] = '';
		
		if ($bo_qty < $min_qty) {
			$new_min_qty = $bo_qty;
		} else {
			$min_purchase_qty = $this->supplier_price_brackets_sets[$this->products_arr[$products_id]['bracket_set_id']]['min_purchase_qty'];
			$upper_min_qty = $bo_qty - (int)$min_purchase_qty;
			
			if ($upper_min_qty < $min_qty) {
				$new_min_qty = $bo_qty;
			} else {
				$new_min_qty = $min_qty;
				if (is_numeric($min_purchase_qty)) {
					$this->products_arr[$products_id]['upper_min_qty'] = $upper_min_qty;
				}
			}
		}
		
		return $new_min_qty;
	}
	
    function calculate_profit($products_id) {
    	$products_arr = $this->products_arr[$products_id];
		$this->set_colour_bracket_arr();
    	
		$customer_price = $products_arr['customer_price'];
		if ((float)$customer_price <= 0) {
			//avoid division by zero
			$profit_percentage = 0;
		} else {
			// $products_arr['avg_offer_price'] storing buyback_price FROM automate_buyback_price TABLE
			$profit_percentage = (1-($products_arr['avg_offer_price']/$customer_price))*100;
		}
		$profit_percentage_colour = '';
		foreach ($this->colour_bracket_arr as $percentage => $colour_code) {
			if ($percentage >= $profit_percentage) {
				$profit_percentage_colour = $colour_code;
				break;
			}
		}
		if (!$profit_percentage_colour) {
			end($this->colour_bracket_arr);
			$profit_percentage_colour = $this->colour_bracket_arr[key($this->colour_bracket_arr)];
        	reset($this->colour_bracket_arr);
		}

		$this->products_arr[$products_id]['profit'] = $this->number_format($profit_percentage);
		$this->products_arr[$products_id]['profit_colour'] = $profit_percentage_colour;

    }    
    
    function get_daily_confirmed_production_qty() {
    	return 0;
    }
	
    /*****************************************************************************************************
     * Private methods -  Not Callable
     *****************************************************************************************************/
    //Set db values to class
    function set_supplier_price_brackets() {
        //supplier price brackets
        $supplier_price_brackets = array();
		$supplier_price_brackets_select_sql = "SELECT bs.*, pb.*
        								FROM " . TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET_SET . " AS bs
        								INNER JOIN " . TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET . " AS pb ON bs.buyback_supplier_price_bracket_set_id = pb.buyback_supplier_price_bracket_set_id
        								ORDER BY bs.buyback_supplier_price_bracket_set_name, pb.buyback_supplier_price_bracket_quantity ASC;";

		$supplier_price_brackets_result_sql = tep_db_query($supplier_price_brackets_select_sql);

		while ($row = tep_db_fetch_array($supplier_price_brackets_result_sql)) {
			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['set_name'] = $row['buyback_supplier_price_bracket_set_name'];
			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['cat_id'] = $row['buyback_supplier_price_bracket_set_cat_id'];
			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['min_qty'] = $row['buyback_supplier_price_bracket_set_minimum_value'];
			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['min_purchase_qty'] = $row['buyback_supplier_price_bracket_set_min_purchase'];
			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['max_qty'] = $row['buyback_supplier_price_bracket_set_maximum_value'];
			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['max_qty_is_overwrite'] = $row['buyback_supplier_price_bracket_set_maximum_is_overwrite'];
			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['brackets'][$row['buyback_supplier_price_bracket_id']]['bracket_quantity'] = $row['buyback_supplier_price_bracket_quantity'];
//			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['brackets'][$row['buyback_supplier_price_bracket_id']]['bracket_value'] = $row['buyback_supplier_price_bracket_value'];
//			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['brackets'][$row['buyback_supplier_price_bracket_id']]['bracket_mode'] = $row['buyback_supplier_price_bracket_mode'];
//			$supplier_price_brackets[$row['buyback_supplier_price_bracket_set_id']]['brackets'][$row['buyback_supplier_price_bracket_id']]['bracket_sign'] = $row['buyback_supplier_price_bracket_sign'];
		}
		
		$this->supplier_price_num_sets = count($supplier_price_brackets);
		$this->supplier_price_brackets_sets = $supplier_price_brackets;
    }

    function set_products_price_brackets() {
		$product_bracket_set_select_sql = "SELECT buyback_setting_reference_id as products_id, buyback_setting_value as bracket_set_id
											FROM ".TABLE_BUYBACK_SETTING."
											WHERE buyback_setting_table_name = '".TABLE_BUYBACK_PRODUCTS."'
											AND buyback_setting_reference_id IN ('".implode("','", array_keys($this->products_arr))."')
											AND buyback_setting_key = 'ofp_supplier_price_bracket_set'";
		$product_bracket_set_result_sql = tep_db_query($product_bracket_set_select_sql);
		while ($product_bracket_set_row = tep_db_fetch_array($product_bracket_set_result_sql)) {
	    	$this->products_arr[$product_bracket_set_row['products_id']]['bracket_set_id'] = $product_bracket_set_row['bracket_set_id'];
		}
    }

    function set_buyback_product_overwrite_qty() {
		$product_overwrite_qty_select_sql = "SELECT buyback_setting_reference_id as products_id, buyback_setting_value as overwrite_qty
											FROM ".TABLE_BUYBACK_SETTING."
											WHERE buyback_setting_table_name = '".TABLE_BUYBACK_PRODUCTS."'
											AND buyback_setting_reference_id IN ('".implode("','", array_keys($this->products_arr))."')
											AND buyback_setting_key = 'ofp_buyback_product_overwrite_qty'";
		$product_overwrite_qty_result_sql = tep_db_query($product_overwrite_qty_select_sql);
		while ($product_overwrite_qty_row = tep_db_fetch_array($product_overwrite_qty_result_sql)) {
	    	$this->products_arr[$product_overwrite_qty_row['products_id']]['max_inventory_space_overwrite'] = $product_overwrite_qty_row['overwrite_qty'];
		}
    }
	
	function get_buyback_qty_unit() {
		$cat_cfg_array = tep_get_cfg_setting($this->category_id, 'catalog', 'BUYBACK_QUANTITY_UNIT');
		return $cat_cfg_array['BUYBACK_QUANTITY_UNIT'];
	}
	
	function set_buyback_qty_unit($products_id) {
		/*
		$cat_cfg_array = tep_get_cfg_setting($products_id, 'product', 'BUYBACK_QUANTITY_UNIT', 'configuration_key');
	    //Get Unit of measurement (ie. Million)
		$this->products_arr[$products_id]['qty_unit'] = $cat_cfg_array['BUYBACK_QUANTITY_UNIT'];
		*/
		$this->products_arr[$products_id]['qty_unit'] = $this->game_buyback_unit;
	}
	
	function get_buyback_product_server_full_info() {
		$product_server_full_select_sql = "	SELECT buyback_setting_reference_id AS products_id, buyback_setting_value AS is_server_full
											FROM " . TABLE_BUYBACK_SETTING . " 
											WHERE buyback_setting_table_name = '" . TABLE_BUYBACK_PRODUCTS . "' 
												AND buyback_setting_reference_id IN ('".implode("','", array_keys($this->products_arr))."')
												AND buyback_setting_key = 'bprd_is_server_full'";
		$product_server_full_result_sql = tep_db_query($product_server_full_select_sql);
		while ($product_server_full_row = tep_db_fetch_array($product_server_full_result_sql)) {
	    	$this->products_arr[$product_server_full_row['products_id']]['is_server_full'] = $product_server_full_row['is_server_full'];
		}
    }
    
    function set_buyback_product_server_full_info($products_id, $server_full) {
    	if (isset($this->products_arr[$products_id])) {
    		$product_server_full_delete_sql = "	DELETE FROM " . TABLE_BUYBACK_SETTING . " 
												WHERE buyback_setting_table_name = '" . TABLE_BUYBACK_PRODUCTS . "' 
													AND buyback_setting_reference_id = '" . tep_db_input($products_id) . "' 
													AND buyback_setting_key = 'bprd_is_server_full'";
			tep_db_query($product_server_full_delete_sql);
			
			$product_server_full_data_array = array('buyback_setting_reference_id' => $products_id,
													'buyback_setting_table_name' => TABLE_BUYBACK_PRODUCTS,
													'buyback_setting_key' => 'bprd_is_server_full',
													'buyback_setting_value' => $server_full);
			tep_db_perform(TABLE_BUYBACK_SETTING, $product_server_full_data_array);
			
			if (!$server_full) {	// E-mail latest 10 customers who subscribe for this product pre-sale notice
				$email_content = 	EMAIL_BUYBACK_PRESALES_TITLE . "\n\n" .
									EMAIL_BUYBACK_PRESALES_SUMMARY_TITLE . "\n" .
									EMAIL_SEPARATOR . "\n" . 
									sprintf(EMAIL_BUYBACK_PRESALES_PRODUCT, '<a href="'.tep_href_link(FILENAME_BUYBACK, 'action=favourites_link&gcat='.$this->category_id.'&pid='.$products_id).'">'.$this->products_arr[$products_id]['categories_name'].'</a>') . "\n" .
									sprintf(EMAIL_BUYBACK_PRESALES_MIN_QTY, $this->products_arr[$products_id]['min_qty']) . "\n" .
									sprintf(EMAIL_BUYBACK_PRESALES_MAX_QTY, $this->products_arr[$products_id]['max_qty']) . "\n\n" .
									EMAIL_BUYBACK_PRESALES_COMMENTS . "\n" .
			 						EMAIL_BUYBACK_PRESALES_FOOTER;
				
				$customer_select_sql = "SELECT c.customers_email_address, c.customers_gender, c.customers_firstname, c.customers_lastname 
										FROM " . TABLE_USER_FAVOURITE_PRODUCTS . " AS f 
										INNER JOIN " . TABLE_CUSTOMERS . " AS c 
											ON f.user_id=c.customers_id 
										INNER JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
											ON c.customers_id=ci.customers_info_id 
										WHERE f.products_id = '".tep_db_input($products_id)."' 
											AND f.favourite_products_presale_notice=1 
											AND FIND_IN_SET(1, customers_login_sites) 
										ORDER BY ci.customers_info_date_of_last_logon DESC 
										LIMIT 10";
				$customer_result_sql = tep_db_query($customer_select_sql);
				
				while ($customer_row = tep_db_fetch_array($customer_result_sql)) {
					$email_greeting = tep_mb_convert_encoding(tep_get_email_greeting(tep_mb_convert_encoding($customer_row['customers_firstname'], EMAIL_CHARSET, CHARSET), tep_mb_convert_encoding($customer_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_row["customers_gender"]), CHARSET, EMAIL_CHARSET);
					
					tep_mail(tep_mb_convert_encoding($customer_row['customers_firstname'].' '.$customer_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_row['customers_email_address'], EMAIL_BUYBACK_PRESALES_SUBJECT, $email_greeting.$email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
				}
			}
    	}
    }
    
    function set_product_quantities($products_id) {
       	$prod_suggest_qty = 0;

		$is_buyback = true; //false == server full
		$is_backorder = false;
		$is_error = false;
		$error_msg = '';
		
		//Forecast quantities
		$first_list_quantity = tep_get_first_list_quantity($products_id);

		if(!$this->vip){
			$first_list_quantity = $first_list_quantity + tep_get_total_reserve_vip($products_id); //hold the quantity that assign for VIP
    	}
    	$forecast_available_qty = $this->products_arr[$products_id]['available_qty'] + $first_list_quantity;
    	$forecast_actual_qty = $this->products_arr[$products_id]['actual_qty'] + $first_list_quantity;
		
		//Check backorder/server full. needed when we calculate MIS
		
        if ($forecast_available_qty < 0) {
			$is_backorder = true;
            $is_buyback = true; 
        } else {
			$is_backorder = false;	
        }
        
        //MIS - assign MIS vars to class before calculate
        $this->products_arr[$products_id]['is_backorder'] = $is_backorder;
		$this->products_arr[$products_id]['forecast_available_qty'] = $forecast_available_qty;
		$this->products_arr[$products_id]['forecast_actual_qty'] = $forecast_actual_qty;
		$this->set_maximum_inventory_space($products_id);
        
        if ($is_backorder) {
			//Suggest Qty (remainder MIS left to fill)
			$prod_suggest_qty = (int)$this->products_arr[$products_id]['max_inventory_space'] - $forecast_available_qty;
        } else {
			//always deduct from forecast actual so we dont have excess stock if pending orders are reversed / refunded.
			$prod_suggest_qty = (int)$this->products_arr[$products_id]['max_inventory_space'] - $forecast_actual_qty;				
        }
        
        //Validate. If error, Set is_buyback to false to block orders and set is_error to highlight in admin ----------------------
		if ($prod_suggest_qty <= 0) {
			$is_buyback = false;
		}
		if ($forecast_actual_qty < 0) {
			$is_buyback = false;
			$is_error = true;
			$error_msg .= ERROR_NEGATIVE_FORECAST_ACTUAL;
		}
		if ($forecast_available_qty > $forecast_actual_qty) {
			$is_buyback = false;
		}
		
		$this->products_arr[$products_id]['first_list_qty'] = $first_list_quantity;			
		$this->products_arr[$products_id]['suggest_qty'] = $prod_suggest_qty; //the quantity system is suggesting we buyback.
		$this->products_arr[$products_id]['is_buyback'] = $is_buyback;
		$this->products_arr[$products_id]['is_error'] = $is_error;
		$this->products_arr[$products_id]['error_msg'] .= $error_msg;
    }

    //num_days from yesterday
    function get_start_end_dates($num_days) {
        $start_date = '';
        $end_date = '';

        //Here end_date will always be midnite last nite (dont want to mess with partials of today).
        //We want to find out the start date based on num of days to count backwards.
        $gm_end_date  = mktime(24, 0, 0, date("m"), date("d")-1, date("Y"));
        $end_date = date('Y-m-d H:i:s', $gm_end_date);
        $gm_start_date = $gm_end_date - ((int)$num_days * 86400);
        $start_date = date('Y-m-d H:i:s', $gm_start_date);
        return array($start_date, $end_date);
    }

    function get_inventory_space_by_date_range($products_id, $start_date, $end_date) {
        $inventory_space_in_range = 0;
		if ($this->buyback_supplier_settings['ofp_inventory_days'] && $start_date && $end_date) {
    	    //Calculate now.
            $buyback_calculate_inventory_space_settings = array(
                                                            'ppls_inventory_days' => $this->buyback_supplier_settings['ofp_inventory_days'],
                                                            'ppls_sales_start_date' => $start_date,
                                                            'ppls_sales_end_date' => $end_date);
            $inventory_space_in_range = tep_get_suggested_max_purchase_qty($products_id, $buyback_calculate_inventory_space_settings);
		}
        return $inventory_space_in_range;
    }

    //Caculate MIS
    function set_maximum_inventory_space($products_id) {
        $maximum_inventory_space = 0;
        $maximum_inventory_space_derived = 0;
        $maximum_inventory_space_system_defined = 0;
		
        switch ($this->buyback_supplier_settings['ofp_sales_retrieval_method']) {
            case 'by_date_range':
                //If Sales end date is not saved, use today's date. So it calculates realtime everytime we need inventory space.
                //Will of course affect the average sales cos more days involved as time goes by.
                $end_date = (trim($this->buyback_supplier_settings['ofp_sales_end_date']) ? $this->buyback_supplier_settings['ofp_sales_end_date'] : date('Y-m-d'));
                if (tep_day_diff($this->buyback_supplier_settings['ofp_sales_start_date'], $end_date) > 0) {
                	$start_date = $this->buyback_supplier_settings['ofp_sales_start_date'];
                } else {
                    $this->set_error(MESSAGE_INVALID_START_DATE);
                }
                break;
            case 'by_last_n_days';
            	list($start_date, $end_date) = $this->get_start_end_dates($this->buyback_supplier_settings['ofp_last_n_days_sales']);
                break;
        }
		
		$maximum_inventory_space_derived = $this->get_inventory_space_by_date_range($products_id, $start_date, $end_date);
		
		$maximum_inventory_space_derived -= $this->get_daily_confirmed_production_qty();
		
        //Find the greater between the two. Only get greater if overwrite qty is greater than 0.
        //admin will set to 0 if manually setting server full.
		if (isset($this->products_arr[$products_id]['max_inventory_space_overwrite']) && is_numeric($this->products_arr[$products_id]['max_inventory_space_overwrite'])) {
			$maximum_inventory_space_defined = ceil($this->products_arr[$products_id]['max_inventory_space_overwrite']);
		} else {
			$maximum_inventory_space_defined = $maximum_inventory_space_derived;
		}
		
        if (isset($this->buyback_supplier_settings['ofp_max_inv_space_percentage']) && (float)$this->buyback_supplier_settings['ofp_max_inv_space_percentage'] > 0) {
            $maximum_inventory_space = (float)$maximum_inventory_space_defined * ((float)$this->buyback_supplier_settings['ofp_max_inv_space_percentage']/100);
        } else {
            //Rubbish percentage ? Ignore it cos we don't want zero division error.
            $maximum_inventory_space = $maximum_inventory_space_defined;
        }
		
        $this->products_arr[$products_id]['max_inventory_space'] = ceil($maximum_inventory_space);
        $this->products_arr[$products_id]['max_inventory_space_derived'] = ceil($maximum_inventory_space_derived);
    }

    function set_colour_bracket_arr () {

		if (isset($this->buyback_supplier_settings['ofp_row_colour'])) {
			$colour_setting_arr = explode(',', $this->buyback_supplier_settings['ofp_row_colour']);
			foreach ($colour_setting_arr as $colour_setting) {
				list($profit_percentage, $colour_code) = explode('|', $colour_setting);
				$this->colour_bracket_arr[(string)$profit_percentage] = $colour_code;
			}
			ksort($this->colour_bracket_arr);
		}
    }
}
?>