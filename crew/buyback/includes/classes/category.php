<?

class category {
    var $category_id;
	
	// class constructor
    function category($cid) {
      	$this->category_id = $cid;
	}
	
	function category_has_product_type($product_type_id, $customer_group_id=0) {
		$categeries_select_sql = "	SELECT c.categories_parent_path, c.parent_id 
									FROM " . TABLE_CATEGORIES . " AS c ";
		if ($customer_group_id) {
			$categeries_select_sql .= "	INNER JOIN " . TABLE_CATEGORIES_GROUPS . " as cg
											ON cg.categories_id = c.categories_id
												AND cg.groups_id IN ('0', '".$customer_group_id."')";
		}
		$categeries_select_sql .= "	WHERE c.categories_id = '".(int)$this->category_id."'
										AND c.categories_status = '1'";
		$categeries_result_sql = tep_db_query($categeries_select_sql);
		
		if ($categeries_row = tep_db_fetch_array($categeries_result_sql)) {
			if ($categeries_row['parent_id'] == 0) {
				$category_id = $this->category_id;
			} else {
				$cat_parent_path = explode('_',$categeries_row['categories_parent_path']);
				$category_id = $cat_parent_path[1];
			}
			
			$category_has_product_type_select_sql = "	SELECT custom_products_type_id 
														FROM ".TABLE_CATEGORIES_PRODUCT_TYPES."
														WHERE categories_id = '".(int)$category_id."'
															AND custom_products_type_id='".(int)$product_type_id."'";
			$category_has_product_type_result_sql = tep_db_query($category_has_product_type_select_sql);
			return (tep_db_num_rows($category_has_product_type_result_sql)>0 ? true : false );
		}
		
		return false;
	}
}
?>