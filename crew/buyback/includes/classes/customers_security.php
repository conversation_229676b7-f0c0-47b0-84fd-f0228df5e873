<?
/*
	$Id: customers_security.php,v 1.6 2009/08/03 01:48:47 weichen Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class customers_security
{
	var $transition_period;
	var $convert_start_date;
	var $customers_id;
	var $language_id;
	
	function customers_security($language_id) {
		$this->language_id = $language_id;
		$this->transition_period = 15;
	}
	
	function set_customers_id($customers_id) {
		$this->customers_id = $customers_id;
		$this->get_customers_security_start_time();
	}
	
	//update the customers_security_start_time to 0000-00-00 to recognise it had set the Q&A
	function remove_customers_security_start_time() {
		$start_time_update_sql = "	UPDATE " . TABLE_CUSTOMERS ." 
									SET customers_security_start_time = '0000-00-00'
									WHERE customers_id='". tep_db_input($this->customers_id) ."'";
		tep_db_query($start_time_update_sql);
	}
	
	function set_customer_security_answer($question, $answer, $action = 'insert') {
		if (!is_array($question) || !is_array($answer)) {
			return false;
		}
		
		if (!tep_not_null($question[0]) || !tep_not_null($answer[0])) {
			return false;
		}
		$customer_security_answer_array = array (	'customers_security_question_1' => $question[0],
													'customers_security_answer_1' => $answer[0],
													'customers_security_counter' => '0',
													'customers_security_update_time' => 'now()'
												);
		if ($action == 'insert') {
			$customer_security_answer_array['customers_id'] = $this->customers_id;
			tep_db_perform(TABLE_CUSTOMERS_SECURITY, $customer_security_answer_array);
			$this->remove_customers_security_start_time();
		} else if ($action == 'update') {
			tep_db_perform(TABLE_CUSTOMERS_SECURITY, $customer_security_answer_array, 'update', " customers_id = '" . tep_db_input($this->customers_id) . "'");
			$this->remove_customers_security_start_time();
		}
	}
	
	function update_security_counter() {
		$customer_security_counter_update_sql = "UPDATE " . TABLE_CUSTOMERS_SECURITY . " 
												SET customers_security_counter = customers_security_counter+1
												WHERE customers_id='". tep_db_input($this->customers_id) ."'";
		tep_db_query($customer_security_counter_update_sql);
	}
	
	function update_question_ask($question_array) {
		if (!is_array($question_array)) {
			return false;
		}
		$question_ask = implode(",", $question_array);
		$customer_security_counter_update_sql = "UPDATE " . TABLE_CUSTOMERS_SECURITY . " 
												SET customers_security_question_ask = '" . tep_db_input($question_ask) . "'
												WHERE customers_id='". tep_db_input($this->customers_id) ."'";
		tep_db_query($customer_security_counter_update_sql);
	}
	
	function check_secret_question_isset() {
		$exist = false;
		$secret_question_select_sql = "	SELECT customers_id 
										FROM " . TABLE_CUSTOMERS_SECURITY . "
										WHERE customers_id = '" . tep_db_input($this->customers_id) . "'";
		$secret_question_select_result = tep_db_query($secret_question_select_sql);
		if ($secret_question_select_row = tep_db_fetch_array($secret_question_select_result)) {
			$exist = true;
		}
		return $exist;
	}
	
	function check_secret_question_is_reset() {
		$reset_qna = false;
		$secret_question_select_sql = "	SELECT customers_id 
										FROM " . TABLE_CUSTOMERS_SECURITY . "
										WHERE customers_id = '" . tep_db_input($this->customers_id) . "'
											AND customers_security_question_1 = ''
											AND customers_security_answer_1 = ''";
		$secret_question_select_result = tep_db_query($secret_question_select_sql);
		
		if ($secret_question_select_row = tep_db_fetch_array($secret_question_select_result)) {
			$reset_qna = true;
		}
		return $reset_qna;
	}
	
	function check_is_max_attempted() {
		$max_attempted = true;
		if ($this->get_security_counter() < MAX_SECURITY_TRY) {
			$max_attempted = false;
		}
		return $max_attempted;
	}
	
	function check_account_freeze() {
		//Freeze the withdraw with following condition :
		//1. Over the max attempted to answer the question
		$freeze_withdraw = false;
		if ($this->check_is_max_attempted()) {
			$freeze_withdraw = true;
		}
		return $freeze_withdraw;
	}
	
	//check need to disable the submit button
	function check_disable_form() {
		$button = false;
		$date_diff = $this->get_convert_pin_expired_date();
		if (tep_not_null($date_diff)) {
			$button = true;
		} else {
			if ($this->check_secret_question_isset()) {
				if ($this->check_is_max_attempted()) {
					$button = true;
				} else if ($this->check_secret_question_is_reset()) {
					$button = true;
				}
			}
		}
		return $button;
	}
	
	function get_customers_security_start_time() {
		$security_start_time_select_sql = "SELECT customers_security_start_time 
		 									FROM " . TABLE_CUSTOMERS . "
		 									WHERE  customers_id = '" . tep_db_input($this->customers_id) . "'";
		$security_start_time_select_result = tep_db_query($security_start_time_select_sql);
		if ($security_start_time_select_row = tep_db_fetch_array($security_start_time_select_result)) {
			$this->convert_start_date = $security_start_time_select_row['customers_security_start_time'];
		}
	}
	
	function get_convert_pin_expired_date() {
		//calculate the day left for setting Q&A
		//if NOT NULL mean they not yet set.
		$day_diff = NULL;
		if (tep_not_null($this->convert_start_date) && $this->convert_start_date != '0000-00-00') {
			$today_date = date("Y-m-d");
			$day_diff = $this->transition_period - tep_day_diff($this->convert_start_date, $today_date);
		}
		return $day_diff;
	}
	
	function get_random_question($first_question = 0) {
		$sec_question = 0;
		//no first random number
		if ($first_question != 0) {
			$sec_question = tep_rand(1, 3);
			if ($sec_question == $first_question) {
				$sec_question = $this->get_random_question($first_question);
			}
		} else {
			$sec_question = tep_rand(1, 3);
		}
		return $sec_question;
	}

	function get_customer_security_questions_ask() {
		$question_ask_array = array(1);
//		$security_questions_ask_sql = "	SELECT customers_security_question_ask 
//										FROM " . TABLE_CUSTOMERS_SECURITY . "
//										WHERE customers_id='". tep_db_input($this->customers_id) ."'";
//		$security_questions_ask_result = tep_db_query($security_questions_ask_sql);
//		if ($security_questions_ask_row = tep_db_fetch_array($security_questions_ask_result)) {		
//			if (tep_not_null($security_questions_ask_row['customers_security_question_ask'])) {
//				$question_ask_array = explode(",", $security_questions_ask_row['customers_security_question_ask']);
//			} else {
//				$question_ask_array[] = $this->get_random_question();
//				$question_ask_array[] = $this->get_random_question($question_ask_array[0]);
//				$this->update_question_ask($question_ask_array);
//			}
//		}
		return $question_ask_array;
	}
	
	function get_question_text($question_id){
		$question_text = '';
		$question_array = $this->get_customer_security_questions_list();
		foreach ($question_array as $counter => $q_array) {
			foreach ($q_array as $id => $text) {
				if ($question_array[$counter]['id'] == $question_id) {
					$question_text = $question_array[$counter]['text'];
				}
			}
		}
		return $question_text;
	}
		
	function get_customer_security_questions_list() {
		$customer_security_questions_array = array();
		$customer_security_select_sql = "SELECT customers_security_question_id, customers_security_question 
										FROM " . TABLE_CUSTOMERS_SECURITY_QUESTIONS ." 
										WHERE language_id='" . tep_db_input($this->language_id) . "'";
		$customer_security_select_result = tep_db_query($customer_security_select_sql);
		while ($customer_security_select_row = tep_db_fetch_array($customer_security_select_result)) {
			$customer_security_questions_array[] = array(	'id' => $customer_security_select_row['customers_security_question_id'],
															'text' => $customer_security_select_row['customers_security_question']);
		}
		return $customer_security_questions_array;
	}
	
	function get_customer_security_answer($question) {
		$answer = '';
		$answer_field_query = 'customers_security_answer_'.$question;
		$customer_security_answer_select_sql = "	SELECT " . $answer_field_query . "
													FROM ". TABLE_CUSTOMERS_SECURITY . "
													WHERE customers_id = '" . tep_db_input($this->customers_id) . "'";														
		$customer_security_answer_select_result = tep_db_query($customer_security_answer_select_sql);
		if ($customer_security_answer_select_row = tep_db_fetch_array($customer_security_answer_select_result)) {
			$answer = $customer_security_answer_select_row[$answer_field_query];
		}
		return $answer;
	}
	
	function get_customer_hidden_contact_no() {
		//grap the original contact no and mobile no from and compare to detect any changes.
		$contact_no_select_sql = "	SELECT customers_telephone, customers_mobile 
									FROM " . TABLE_CUSTOMERS . "
									WHERE customers_id='" . tep_db_input($this->customers_id) . "'";
		$contact_no_select_result = tep_db_query($contact_no_select_sql);
		$contact_no_select_row = tep_db_fetch_array($contact_no_select_result);
		return $contact_no_select_row;
	}
	
	function get_security_counter() {
		$security_counter_select_sql = "SELECT customers_security_counter 
										FROM " . TABLE_CUSTOMERS_SECURITY ." 
										WHERE customers_id='" . tep_db_input($this->customers_id) . "'";
		$security_counter_select_result = tep_db_query($security_counter_select_sql);
		$security_counter_select_row = tep_db_fetch_array($security_counter_select_result);
		return $security_counter_select_row['customers_security_counter'];
	}
	
	function reset_security_counter() {
		//reset counter and question once success update profile.
		$counter_question_update_sql = "UPDATE " . TABLE_CUSTOMERS_SECURITY . "
										SET customers_security_counter = '0',
											customers_security_question_ask = NULL
										WHERE customers_id='" . tep_db_input($this->customers_id) . "'";
		tep_db_query($counter_question_update_sql);
	}
	
	//validation for signup page
	function form_validation($val_arr, $update_session=true) {
		global $messageStack, $form_session_name, $content, $form_values_arr;
		$errorCount = 0;
		$question_selected_array = array();
		$duplicate_question_array = array();
		for ($question_cnt = 1; $question_cnt < 2; $question_cnt++) {
			if (!in_array($val_arr['question_'.$question_cnt], $question_selected_array)) {
				$question_selected_array[] = $val_arr['question_'.$question_cnt];
			} else {
				$duplicate_question_array[] = ENTRY_QUESTION . $question_cnt;
			}
			
			if (!trim($val_arr['question_'.$question_cnt])) {
			    $messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, ENTRY_QUESTION));
				$errorCount++;
			}
			
			if (!trim($val_arr['answer_'.$question_cnt])) {
			    $messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, ENTRY_ANSWER));
				$errorCount++;
			}
		}
		
		if (count($question_selected_array) != 1) {
			$messageStack->add_session($content, sprintf(TEXT_DUPLICATE_ENTRY, implode(',', $duplicate_question_array)));
			$errorCount++;
		}
		
		if ($update_session) {
			for ($quest_cnt = 1; $quest_cnt < 2; $quest_cnt++) {	
				$_SESSION[$form_session_name]['question_'.$quest_cnt] = tep_db_prepare_input($val_arr['question_'.$quest_cnt]);
				$_SESSION[$form_session_name]['answer_'.$quest_cnt] = tep_db_prepare_input($val_arr['answer_'.$quest_cnt]);
			}
		}
		return $errorCount;
	}
	
	//validation for edit info at account management.
	function form_validation_edit_profile($val_arr) {
		global $messageStack, $content;
		$errorCount = 0;
		$date_diff = $this->get_convert_pin_expired_date();
		if (!tep_not_null($date_diff) && $this->check_secret_question_isset() && !$this->check_secret_question_is_reset() && !$this->check_is_max_attempted()) {
			$question_id_array = $this->get_customer_security_questions_ask();
			$answer_array = array();
			$this->update_security_counter();
			for ($ans = 1; $ans <=1; $ans++) {
				if (!trim($val_arr['answer_'.$ans])) {
				    $messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, (ENTRY_ANSWER)) . '(' . sprintf(TEXT_SHOW_TRIED_ATTEMPTED, (MAX_SECURITY_TRY - $this->get_security_counter())) . ')' );
					$errorCount++;
				} else {
					// check the answer
					$ori_answer = $this->get_customer_security_answer($question_id_array[$ans-1]);
					if (strcmp($ori_answer, $val_arr['answer_'.$ans]) != 0) {
						$messageStack->add_session($content, sprintf(TEXT_REQUIRE_CORRECT_ANSWER, (ENTRY_ANSWER)) . '(' . sprintf(TEXT_SHOW_TRIED_ATTEMPTED, (MAX_SECURITY_TRY - $this->get_security_counter())) . ')');
						$errorCount++;
					}
				}
			}
		} else {
			//validate pin input box
			if (!trim($val_arr['pin_number'])) {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_PIN_NUMBER));
				$errorCount++;
			} else if (!tep_check_pin_number($val_arr['pin_number'], $this->customers_id)) {	// We do not have customer_id session when customer has not login yet
				$messageStack->add_session($content, TEXT_INVALID_PIN_NUMBER);
				$errorCount++;
			}
		}
		return $errorCount;
	}
	
	/*******************************************
	Draw Interface.
	********************************************/
	// interface for modify profile
	function draw_question_answer_form() {
		ob_start();
		$tab_counter = 30;
		$date_diff = $this->get_convert_pin_expired_date();
		if (!tep_not_null($date_diff) && $this->check_secret_question_isset() && !$this->check_secret_question_is_reset() && !$this->check_is_max_attempted()) {
			$question_ask_array = $this->get_customer_security_questions_ask();
			$query_field = $question_form_html = '';
			$query_question_array = array();
			$question_text_array = array();
			$empty_question = false;
			foreach ($question_ask_array as $counter => $qest_number) {
				$query_question_array[] = 'customers_security_question_'.$qest_number;
			}
			$query_field = implode(", ",$query_question_array);
			$customer_question_select_sql = "SELECT " . $query_field . " 
											FROM " . TABLE_CUSTOMERS_SECURITY . " 
											WHERE customers_id='" . tep_db_input($this->customers_id) . "'";
			$customer_question_select_result = tep_db_query($customer_question_select_sql);								
			if ($customer_question_select_row = tep_db_fetch_array($customer_question_select_result)) {
				foreach ($query_question_array as $counter => $query_field_result) {
					$question_text_array[] = $customer_question_select_row[$query_field_result];
					if (!tep_not_null($customer_question_select_row[$query_field_result])) {
						$empty_question = true;
					}
				}		
				if ($this->get_security_counter() < MAX_SECURITY_TRY && !$empty_question) {
					//draw question and ans input field.
					for ($question_cnt = 0; $question_cnt < count($question_text_array); $question_cnt++) {
?>
						<tr>
				    		<td><p><?=ENTRY_QUESTION.':'?></p></td>
				    		<td>
				    			<p><?=$question_text_array[$question_cnt]?></p>
				    		</td>
						</tr>
						<tr>
				    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						</tr>
						<tr>
				    		<td><p><?=ENTRY_ANSWER.':'?></p></td>
				    		<td>
				    			<?=tep_draw_input_field('answer_' . ($question_cnt+1), (isset($form_values_arr['answer_' . ($question_cnt+1)]) && $form_values_arr['answer_' . ($question_cnt+1)] ? $form_values_arr['answer_' . ($question_cnt+1)] : ''), 'size="24" autocomplete="off" id="answer_'. ($question_cnt+1) .'" tabindex='.$tab_counter++, true);?>
				    			<div class="hint"><p><?=TEXT_HELP_SECURITY_ANSWER?></p></div>
				    		</td>
						</tr>
						<tr>
				    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
						</tr>
<?
					}
				} else if ($this->get_security_counter() >= MAX_SECURITY_TRY) {
?>
					<tr>
			    		<td colspan="2"><p align="center"><span class="redIndicator"><?=TEXT_EXCEED_MAX_ATTEMPTED?></span></p></td>
					</tr>
					<tr>
			    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
					</tr>
<?
				} else {
?>
					<tr>
			    		<td colspan="2"><p align="center"><span class="redIndicator"><?=($this->check_secret_question_is_reset() ? TEXT_QUESTION_AND_ANSWER_IS_RESET : TEXT_QUESTION_AND_ANSWER_IS_NOT_YET_SET)?></span></p></td>
					</tr>
					<tr>
			    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
					</tr>
<?					
				}
			} else {
?>
					<tr>
			    		<td colspan="2"><p align="center"><span class="redIndicator"><?=TEXT_QUESTION_AND_ANSWER_IS_NOT_YET_SET?></span></p></td>
					</tr>
					<tr>
			    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
					</tr>
<?			
			}
		} else {
?>
			<tr>
    			<td><p><?=TEXT_PIN_NUMBER?></p></td>
    			<td>
    				<?=tep_draw_input_field('pin_number', '', 'size="6" tabindex='.$tab_counter++, true);?>
    				<div class="hint"><p><?=TEXT_HELP_PIN_NUMBER?></p></div>
    			</td>
			</tr>
<?
		}
		$question_form_html = ob_get_contents();
		ob_end_clean();
		return $question_form_html;
	}

	// interface for signup/ reset Q&A
	// $action:
	// new = new signup
	// exist = existing customer that change their PIN code to Q&A
	// reset = existing customer that request admin to reset.
	function display_customer_security_form($action = 'new', $form_values_arr) {
		global $next_step;
		$security_form_html = '';
		$question_drop_down_array = array(	array('id' => '0' , 'text' => TEXT_SELECT_SECURITY_QUESTION));
		$action_array = array();
		$get_params_always_exclude_array = array('current_step', 'action', 'remove_row_num');
		if ($action == 'new') {
			$action_array['form_name'] = 'create_security_question';
			$action_array['filename'] = FILENAME_SUPPLIER_SIGNUP;
			$action_array['parameters'] = tep_get_all_get_params($get_params_always_exclude_array) . "current_step=".$next_step; //tep_get_all_get_params($get_params_always_exclude_array)
			$action_array['params'] = 'onSubmit="return check_form_security(' . $action_array['form_name'] . ');" autocomplete="off"';
		} else if ($action == 'exist' || $action == 'reset') {
			$action_array['form_name'] = 'process3';
			$action_array['filename'] = FILENAME_MY_ACCOUNT_MGMT;
			$action_array['parameters'] = "current_step=".$next_step;
			$action_array['params'] = 'onSubmit="return check_form_security(' . $action_array['form_name'] . ');" autocomplete="off"';			
		}
		
		ob_start();
?>
		<table border="0" cellpadding="0" cellspacing="2" width="100%">
			<tr>
				<td colspan="2"><p><?=TEXT_SECURITY_QUESTION_AND_ANSWER_USAGE?><br><span class="redIndicator"><b><?=TEXT_PLEASE_FILL_IN_SERIOUSLY?></b></span></p></td>
			</tr>
			<tr>
	    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			</tr>
<?		
		echo tep_draw_form($action_array['form_name'], $action_array['filename'], $action_array['parameters'], 'post', $action_array['params']);
		echo tep_draw_hidden_field('action', 'process');
		
		$question_array = $this->get_customer_security_questions_list();
		$question_drop_down_array = array_merge($question_drop_down_array, $question_array);

		$tab_counter = 0;
		for ($question_cnt = 1; $question_cnt < 2; $question_cnt++) {
?>
			<tr>
	    		<td><p><?=ENTRY_QUESTION.$question_cnt.':'?></p></td>
	    		<td>
	    			<?=tep_draw_pull_down_menu('question_' . $question_cnt, $question_drop_down_array, (isset($form_values_arr['question_' . $question_cnt]) && $form_values_arr['question_' . $question_cnt] ? $form_values_arr['question_' . $question_cnt] : ''), 'id="question_' . $question_cnt . '" onchange="check_avilable_question();" tabindex='.$tab_counter++, true);?>
	    			<div class="hint"><p><?=TEXT_HELP_SECURITY_QUESTION?></p></div>
	    		</td>
			</tr>
			<tr>
	    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			</tr>
			<tr>
	    		<td><p><?=ENTRY_ANSWER.':'?></p></td>
	    		<td>
	    			<?=tep_draw_input_field('answer_' . $question_cnt, (isset($form_values_arr['answer_' . $question_cnt]) && $form_values_arr['answer_' . $question_cnt] ? $form_values_arr['answer_' . $question_cnt] : ''), 'size="24" autocomplete="off" id="answer_'. $question_cnt .'" tabindex='.$tab_counter++, true);?>
	    			<div class="hint"><p><?=TEXT_HELP_SECURITY_ANSWER?></p></div>
	    		</td>
			</tr>
			<tr>
	    		<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			</tr>
<?
		}
		
		if ($action == 'exist' || $action == 'reset') {
?>
            <tr>
    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"></td>
			</tr>
			<tr>
    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			</tr>
			<tr>
    			<td><p><?=TEXT_PIN_NUMBER?></p></td>
    			<td>
    				<?=tep_draw_input_field('pin_number', '', 'size="6" autocomplete="off" tabindex='.$tab_counter++, true);?>
    				<div class="hint"><p><?=TEXT_HELP_PIN_NUMBER?></p></div>
    			</td>
			</tr>
			<tr>
    			<td colspan="2" background="<?=DIR_WS_IMAGES . 'space.gif'?>" height="8"></td>
			</tr>
<?		
		}
?>
			<tr>
<?
		if ($action == 'exist' || $action == 'reset') {
?>
	    		<td colspan="2" align="right"><p><?=tep_image_submit(DIR_WS_IMAGES.'button_next.gif', TEXT_SAVE, 'tabindex='.$tab_counter++)?></p></td>
<?
		} else if ($action == 'new'){
?>		
				<td colspan="2" align="right"><p><?=tep_image_submit(DIR_WS_IMAGES.'button_next.gif', TEXT_NEXT, 'tabindex='.$tab_counter++)?></p></td>
<?		
		}
?>
			</tr>
		</table>
		</form>
<?
		$security_form_html = ob_get_contents();
		ob_end_clean();
		return $security_form_html;
	}
}
?>