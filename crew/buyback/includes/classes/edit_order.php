<?
/*
	$Id: edit_order.php,v 1.2 2009/08/17 11:41:43 weesiong Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class edit_order
{
	var $order_id;
    function edit_order($order_id) {
		$this->order_id = $order_id;
    }
    
    function is_valid_locked() {
    	$lock_orders_select_sql = "	SELECT orders_locked_by 
    								FROM " . TABLE_ORDERS . " 
    								WHERE orders_id = '" . tep_db_input($this->order_id) . "'";
		$lock_orders_result_sql = tep_db_query($lock_orders_select_sql);
		$lock_orders_row = tep_db_fetch_array($lock_orders_result_sql);
		
		// orders_locked_by = 0  //system lock
		return ($lock_orders_row["orders_locked_by"] == 0);
    }
    
	function deliver_order($sent_qty, $order_products_id, $supplier_email, $notify_customer = true, $messageStack) {
    	global $cat_conf_array;
		$log_message_to_use = sprintf(LOG_PARTIAL_DELIVERY_SALES, $this->order_id); 
		
		$fully_delivered_order = true;
		
		$order = new order($this->order_id);
		$partial_deliver_str = '';
		
		for ($i=0; $i < count($order->products); $i++) {
			$log_user_msg = '';
			
			$stock_select_sql = "	SELECT products_quantity, products_actual_quantity, products_bundle, products_bundle_dynamic, 
									products_skip_inventory, products_cat_path, custom_products_type_id 
									FROM " . TABLE_PRODUCTS . " 
									WHERE products_id = '" . tep_get_prid($order->products[$i]['id']) . "'";
			$stock_result_sql = tep_db_query($stock_select_sql);
			
			if (tep_db_num_rows($stock_result_sql) > 0) {
				$stock_row = tep_db_fetch_array($stock_result_sql);
				
				if ($stock_row['products_bundle_dynamic'] == 'yes') {	// dynamic bundle
					$recalculate_delivered_price = false;
					$dynamic_deliver_item = array();
					for ($pbd_loop=0; $pbd_loop < count($order->products[$i]["bundle"]); $pbd_loop++) {
						$qty_updated = false;
						
						//$cur_product_ref = 'deliver_'.$order->products[$i]['id'].'_'.$order->products[$i]["bundle"][$pbd_loop]["id"];
						//$cur_deliver_sign_ref = 'deliver_sign_'.$order->products[$i]['id'].'_'.$order->products[$i]["bundle"][$pbd_loop]["id"];
						
						if (isset($sent_qty) && $sent_qty > 0 && $order_products_id == $order->products[$i]['bundle'][$pbd_loop]['order_products_id']) {
							$qty = $sent_qty;
							
							if (abs($qty) > 0) {
								// Check if this admin user has the right for this category
								//$full_cat_permissions = (tep_check_cat_tree_permissions(FILENAME_ORDERS, tep_get_actual_product_cat_id($order->products[$i]["bundle"][$pbd_loop]["id"])) == 1) ? true : false;
								
								$subproduct_stock_query = tep_db_query("SELECT products_quantity, products_actual_quantity, products_skip_inventory, products_cat_path 
																		FROM " . TABLE_PRODUCTS . " 
																		WHERE products_id = '" . tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]) . "'");
								$subproduct_stock_values = tep_db_fetch_array($subproduct_stock_query);
								
								if ($qty > 0) {	// positive value
									$max_qty = $order->products[$i]["bundle"][$pbd_loop]["qty"] - $order->products[$i]["bundle"][$pbd_loop]["delivered_qty"];
									if ($qty <= $max_qty) {
										if (!$subproduct_stock_values["products_skip_inventory"]) {
											$update_qty_array = array(	array(	'field_name'=> 'products_actual_quantity',
																				'operator'=> '-', 
																				'value'=> $qty)
																	);
											// This function will handle the qty adjustment and keep the log if asking so
											tep_set_product_qty(tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]), $update_qty_array, true, $log_message_to_use, '');
										}
										
										$delived_qty_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
																	SET products_delivered_quantity = products_delivered_quantity + " . $qty . ",
																		products_good_delivered_quantity = products_good_delivered_quantity + " . $qty . " 
																	WHERE orders_id='" . $this->order_id . "' 
																		AND products_id = '" . tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]) . "' 
																		AND products_bundle_id = '" . $order->products[$i]['id'] . "' 
																		AND orders_products_is_compensate=0";
										tep_db_query($delived_qty_update_sql);
										
										$dynamic_deliver_item[] = "&raquo; " . $order->products[$i]["bundle"][$pbd_loop]["name"] . "\tx " . $qty;
										
										$recalculate_delivered_price = true;
										
										// Sales activity log
										$this->insert_sales_activity($order->products[$i]['order_products_id'], tep_get_prid($order->products[$i]["bundle"][$pbd_loop]["id"]), 'D', (($order->products[$i]["final_price"]*$order->products[$i]["qty"])/count($order->products[$i]["bundle"])), $order->products[$i]["bundle"][$pbd_loop]["qty"], $qty, $supplier_email);
										
										if ($qty < $max_qty)	$fully_delivered_order = false;
										
										$qty_updated = true;
									}
								}
							}
						}
						if (!$qty_updated) {
							if ($order->products[$i]["bundle"][$pbd_loop]["delivered_qty"] < $order->products[$i]["bundle"][$pbd_loop]["qty"]) {
								$fully_delivered_order = false;
							}
						}
					}
					
					if (count($dynamic_deliver_item))	$partial_deliver_str .= implode("\n", $dynamic_deliver_item) . "\n";
					
					if ($recalculate_delivered_price)	$this->update_delivered_price($order->products[$i]['order_products_id'], 'products_bundle_dynamic');
				} else if ($stock_row['products_bundle'] == 'yes') {	// static bundle
					$recalculate_delivered_price = false;
					$static_deliver_item = array();
					for ($static_loop=0; $static_loop < count($order->products[$i]["static"]); $static_loop++) {
						$qty_updated = false;
						
						//$cur_product_ref = 'deliver_'.$order->products[$i]['static'][$static_loop]['order_products_id'];
						//$cur_deliver_sign_ref = 'deliver_sign_'.$order->products[$i]['static'][$static_loop]['order_products_id'];
						
						if (isset($sent_qty) && $sent_qty > 0 && $order_products_id == $order->products[$i]['static'][$static_loop]['order_products_id']) {
							$qty = $sent_qty;
							
							if (abs($qty) > 0) {
								// Check if this admin user has the right for this category
								//$full_cat_permissions = (tep_check_cat_tree_permissions(FILENAME_ORDERS, tep_get_actual_product_cat_id($order->products[$i]["static"][$static_loop]["id"])) == 1) ? true : false;
								
								$subproduct_stock_select_sql = "	SELECT products_quantity, products_actual_quantity, products_skip_inventory, products_cat_path, custom_products_type_id 
																	FROM " . TABLE_PRODUCTS . " 
																	WHERE products_id = '" . tep_get_prid($order->products[$i]["static"][$static_loop]["id"]) . "'";
								$subproduct_stock_query = tep_db_query($subproduct_stock_select_sql);
								$subproduct_stock_values = tep_db_fetch_array($subproduct_stock_query);
								
								if ($qty > 0) {	// positive value
									$max_qty = $order->products[$i]["static"][$static_loop]["qty"] - $order->products[$i]["static"][$static_loop]["delivered_qty"];
									if ($qty <= $max_qty) {
										if (!$subproduct_stock_values["products_skip_inventory"]) {
											$update_qty_array = array(	array(	'field_name'=> 'products_actual_quantity',
																				'operator'=> '-', 
																				'value'=> $qty)
																	);
											// This function will handle the qty adjustment and keep the log if asking so
											tep_set_product_qty(tep_get_prid($order->products[$i]["static"][$static_loop]["id"]), $update_qty_array, true, $log_message_to_use, '');
										}
										
										$delived_qty_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
																	SET products_delivered_quantity = products_delivered_quantity + " . $qty . ",
																		products_good_delivered_quantity = products_good_delivered_quantity + " . $qty . " 
																	WHERE orders_id='" . $this->order_id . "' 
																		AND products_id = '" . tep_get_prid($order->products[$i]["static"][$static_loop]["id"]) . "' 
																		AND orders_products_id = '" . $order->products[$i]["static"][$static_loop]["order_products_id"] . "' 
																		AND products_bundle_id = '" . $order->products[$i]['id'] . "' 
																		AND orders_products_is_compensate = 0";
										
										tep_db_query($delived_qty_update_sql);
										
										$static_deliver_item[] = "&raquo; " . $order->products[$i]["static"][$static_loop]["name"] . "\tx " . $qty;
										
										$recalculate_delivered_price = true;
										
										// Sales activity log
										$this->insert_sales_activity($order->products[$i]['order_products_id'], tep_get_prid($order->products[$i]["static"][$static_loop]["id"]), 'D', (($order->products[$i]["final_price"]*$order->products[$i]["qty"])/count($order->products[$i]["static"])), $order->products[$i]["static"][$static_loop]["qty"], $qty, $supplier_email);
										
										if ($qty < $max_qty)	$fully_delivered_order = false;
										
										$qty_updated = true;
									}
								} 								
							}
						}
						
						if (!$qty_updated) {
							if ($order->products[$i]["static"][$static_loop]["delivered_qty"] < $order->products[$i]["static"][$static_loop]["qty"]) {
								$fully_delivered_order = false;
							}
						}
					}
					
					if (count($static_deliver_item))	$partial_deliver_str .= implode("\n", $static_deliver_item) . "\n";
					
					if ($recalculate_delivered_price)	$this->update_delivered_price($order->products[$i]['order_products_id'], 'products_bundle');
				} else {	// single product
					$recalculate_delivered_price = false;
					$qty_updated = false;
					
					if (!$qty_updated) {
						if ($order->products[$i]["delivered_qty"] < $order->products[$i]["qty"]) {
							$fully_delivered_order = false;
						}
					}
					
					if ($recalculate_delivered_price)	$this->update_delivered_price($order->products[$i]['order_products_id'], '');
				}
         	}//end if
   		}//end for loop
		
		// Only considered fully delivered if both Normal Purchase and Compensate Items are fully delivered
		if ($fully_delivered_order) {
			$order->get_compensate_products();	// Compensate product is not loaded in Constructor when $order class object is created
			if ($order->info['compensation_fully_delivered'] != 1)	$fully_delivered_order = false;
		}
		
		$status_cur = $fully_delivered_order ? 3 : 0;
		
		while (substr($partial_deliver_str, -1) == "\n")	$partial_deliver_str = substr($partial_deliver_str, 0, -1);
		$partial_deliver_str = strip_tags($partial_deliver_str);
		
		if (tep_not_null($partial_deliver_str)) {
			$partial_deliver_str = TEXT_VIP_BUYBACK_ORDER_ITEMS_DELIVERED_REMARK . "\n" . $partial_deliver_str;
			
			//$reversible_order_status_permission = tep_check_status_update_permission('C', $login_groups_id, 3, PARTIAL_DELIVERY_STATUS);
			//$not_notify_customer_permission = tep_admin_files_actions(FILENAME_ORDERS, 'EDIT_ORDER_NOT_NOTIFY_CUSTOMER');
			
			if ($notify_customer) {
				$customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname 
												FROM " . TABLE_CUSTOMERS . " 
												WHERE customers_id = '" . $order->customer['id'] . "'";
				$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
				if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
					$email_firstname = $customer_profile_row["customers_firstname"];
					$email_lastname = $customer_profile_row["customers_lastname"];
				} else {
					$email_firstname = $order->customer['name'];
					$email_lastname = $order->customer['name'];
				}
				include_once(DIR_WS_LANGUAGES . 'email_customers.php');
				$orders_status_array = $this->get_customer_order_status();
				$email_greeting = tep_get_email_greeting_english($email_firstname, $email_lastname, $customer_profile_row["customers_gender"]);
				
				$email = $email_greeting . EN_EMAIL_TEXT_ORDER_NUMBER . ' ' . (int)$this->order_id . "\n"
						. EN_EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($order->info['date_purchased']) . "\n" . EN_EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $this->order_id, 'SSL') . "\n\n" ;
							
				$email .= sprintf(EN_EMAIL_TEXT_UPDATED_STATUS, ($fully_delivered_order ? $orders_status_array[$order->info["orders_status"]] . ' -> ' . $orders_status_array[$status_cur] : $orders_status_array[$order->info["orders_status"]])) .
					 	  str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $partial_deliver_str) . "\n\n" . sprintf(EN_EMAIL_TEXT_CLOSING, $cat_conf_array['EMAIL_TO'], $cat_conf_array['STORE_NAME']) . "\n\n" . $cat_conf_array['STORE_EMAIL_SIGNATURE'];
				
            	tep_mail($order->customer['name'], $order->customer['email_address'], implode(' ', array($cat_conf_array['EMAIL_SUBJECT_PREFIX'], sprintf(EN_EMAIL_TEXT_SUBJECT, (int)$this->order_id))), $email, $cat_conf_array['STORE_OWNER'], $cat_conf_array['STORE_OWNER_EMAIL_ADDRESS'], 'iso-8859-1');
            	
            	$partial_delivery_notify_customer = true;
            } else {
            	$partial_delivery_notify_customer = false;
            }
            
			tep_db_query("INSERT INTO " . TABLE_ORDERS_STATUS_HISTORY . " (orders_id, orders_status_id, date_added, customer_notified, comments, comments_type, changed_by) VALUES ('" . (int)$this->order_id . "', $status_cur, now(), '".($partial_delivery_notify_customer == true ? '1' : '0')."', '" . tep_db_input($partial_deliver_str)  . "', 2, '".$supplier_email."')");
			tep_update_orders_status_counter(array("orders_id" => $this->order_id, "orders_status_id" => $status_cur, "date_added" => 'now()' , "changed_by" => $supplier_email));
			
      		if ($fully_delivered_order) {
      			$orders_status_update_sql_data = array(	'orders_status' => tep_db_input($status_cur),
  					 								'last_modified' => 'now()');
  				tep_db_perform(TABLE_ORDERS, $orders_status_update_sql_data, 'update', "orders_id = '" . (int)$this->order_id . "'");			
  				
      			// Update the order's tag since it is updated to "Completed"
      			tep_update_record_tags(FILENAME_STATS_ORDERS_TRACKING, (int)$this->order_id, $status_cur, '');
      			      			
      			// Perform status update e-mail notification
        		//tep_status_update_notification('C', (int)$this->order_id, $this->admin_email, PARTIAL_DELIVERY_STATUS, $status_cur, 'M', $partial_deliver_str);
      		}
		}
    }
    

    function insert_sales_activity($op_id, $p_id, $activity_code, $total_amt, $total_qty, $activity_qty, $supplier_email) {
    	if ($total_qty > 0 && $activity_qty > 0) {
    		switch ($activity_code) {
    			case 'D':	// Deliver
    				$activity_operator = '+';
    				break;
    			case 'RD':	// Reduce delivered qty
    				$activity_operator = '-';
    				break;
    			case 'CD':	// Compensate delivered qty
    				$activity_operator = '+';
    				break;
    			case 'CRD':	// Compensate reduce delivered qty
    				$activity_operator = '-';
    				break;
    			case 'RFD':	// Refund delivered qty
    				$activity_operator = '+';
    				break;
    			case 'RFRD':// Refund reduce delivered qty
    				$activity_operator = '-';
    				break;
    			case 'RVD':	// Reverse delivered qty
    				$activity_operator = '+';
    				break;
    			case 'RVRD':// Reverse reduce delivered qty
    				$activity_operator = '-';
    				break;
    		}
    		
    		$sales_activities_sql_data = array(	'sales_activities_date' => 'now()',
												'sales_activities_orders_id' => $this->order_id,
				 								'sales_activities_orders_products_id' => $op_id,
				 								'sales_activities_products_id' => $p_id,
				 								'sales_activities_code' => $activity_code,
				 								'sales_activities_operator' => $activity_operator,
				 								'sales_activities_amount' => ($activity_qty * $total_amt) / $total_qty,
				 								'sales_activities_quantity' => $activity_qty,
				 								'sales_activities_by_admin_id' => $supplier_email,
				 								);
  			tep_db_perform(TABLE_SALES_ACTIVITIES, $sales_activities_sql_data);
    	}
    }
    
    function update_delivered_price($orders_product_id, $product_type='') {
    	global $currencies;
    	
    	$order_info_select_sql = "	SELECT currency 
									FROM " . TABLE_ORDERS . " 
									WHERE orders_id = '" . tep_db_input($this->order_id) . "'";
		$order_info_result_sql = tep_db_query($order_info_select_sql);
		$order_info_row = tep_db_fetch_array($order_info_result_sql);
		
    	switch ($product_type) {
    		case 'products_bundle_dynamic':
    		case 'products_bundle':
    			$total_subproduct = 0;
    			$good_delivered_ratio = 0;
    			
    			$purchased_final_price_select_sql = "	SELECT products_id, final_price, products_quantity 
    													FROM " . TABLE_ORDERS_PRODUCTS . " 
    													WHERE orders_id = '" . tep_db_input($this->order_id) . "' 
    														AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
    			$purchased_final_price_result_sql = tep_db_query($purchased_final_price_select_sql);
				
				if ($purchased_final_price_row = tep_db_fetch_array($purchased_final_price_result_sql)) {
					if ($order_info_row['currency'] == DEFAULT_CURRENCY) {
						$total_amount = $purchased_final_price_row['products_quantity'] * tep_round($purchased_final_price_row['final_price'], $currencies->currencies[$order_info_row['currency']]['decimal_places']);
					} else {
						$total_amount = $purchased_final_price_row['products_quantity'] * $purchased_final_price_row['final_price'];
					}
					
	    			$subproduct_select_sql = "	SELECT products_quantity, products_good_delivered_quantity 
												FROM " . TABLE_ORDERS_PRODUCTS . " 
												WHERE orders_id = '" . tep_db_input($this->order_id) . "' 
													AND parent_orders_products_id = '" . tep_db_input($orders_product_id) . "'";
					$subproduct_result_sql = tep_db_query($subproduct_select_sql);
					
					while ($subproduct_row = tep_db_fetch_array($subproduct_result_sql)) {
						if ($subproduct_row['products_quantity'] > 0) {
							$good_delivered_ratio += (double)$subproduct_row['products_good_delivered_quantity'] / $subproduct_row['products_quantity'];
							
							$total_subproduct++;
						}
					}
					
					if ($total_subproduct) {
						$latest_delivered_price = (double)($total_amount * $good_delivered_ratio) / $total_subproduct;
						
						// Update the latest delivered price
						$delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
														SET products_good_delivered_price = '" . tep_db_input($latest_delivered_price) . "'
		    											WHERE orders_id = '" . tep_db_input($this->order_id) . "' 
		    												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
		    			tep_db_query($delivered_price_update_sql);
					}
				}
				
    			break;
    		default:
    			// Update the latest delivered price
    			if ($order_info_row['currency'] == DEFAULT_CURRENCY) {
					$delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
													SET products_good_delivered_price = products_good_delivered_quantity * ROUND(final_price, ".(int)$currencies->currencies[$order_info_row['currency']]['decimal_places'].") 
	    											WHERE orders_id = '" . tep_db_input($this->order_id) . "' 
	    												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
	    		} else {
	    			$delivered_price_update_sql = "	UPDATE " . TABLE_ORDERS_PRODUCTS . " 
													SET products_good_delivered_price = products_good_delivered_quantity * final_price 
	    											WHERE orders_id = '" . tep_db_input($this->order_id) . "' 
	    												AND orders_products_id = '" . tep_db_input($orders_product_id) . "'";
	    		}
    			tep_db_query($delivered_price_update_sql);
				
    			break;
    	}
	}
	
	/********************************************************
	 notify customer if order delivered
	 *******************************************************/
	function get_customer_order_status() {
		$orders_status_array = array();
		$orders_status_query = tep_db_query("select orders_status_id, orders_status_name from " . TABLE_ORDERS_STATUS . " where language_id = '1'");
		while ($orders_status = tep_db_fetch_array($orders_status_query)) {
		    $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
		}
		return $orders_status_array;
	}
	
	
	function set_customers_comment($orders_id, $orders_status_id, $comment, $customer_notified='0', $comments_type='0') {
		$comment_array = array(	'orders_id' => $orders_id,
								'orders_status_id' => $orders_status_id,
								'date_added' => 'now()',
								'customer_notified' => $customer_notified,
								'comments' => $comment,
								'comments_type' => $comments_type,
								'set_as_order_remarks' => '0',
								'changed_by' => 'system'
								);
		tep_db_perform(TABLE_ORDERS_STATUS_HISTORY, $comment_array);
		tep_update_orders_status_counter($comment_array);
		
		if ($customer_notified == '1') {
			$this->customers_orders_notification($orders_id, $comment);
		}
	}
	
	//sent by buyback
	function customers_orders_notification($orders_id, $comment){
		global $cat_conf_array;
		$select_customers_info = "SELECT customers_id, date_purchased, orders_status FROM " . TABLE_ORDERS ." WHERE orders_id='" . $orders_id . "'";
		$select_customers_info_result = tep_db_query($select_customers_info);
		
		if($select_customers_info_row = tep_db_fetch_array($select_customers_info_result)){
			$cur_status = $select_customers_info_row['orders_status'];
			$customer_profile_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname, customers_email_address FROM " . TABLE_CUSTOMERS . " WHERE customers_id = '" . $select_customers_info_row['customers_id'] . "'";
			$customer_profile_result_sql = tep_db_query($customer_profile_select_sql);
			
			if ($customer_profile_row = tep_db_fetch_array($customer_profile_result_sql)) {
				$email_firstname = $customer_profile_row["customers_firstname"];
				$email_lastname = $customer_profile_row["customers_lastname"];
				$email_address = $customer_profile_row["customers_email_address"];
				$customers_gender = $customer_profile_row["customers_gender"];
			}
			
			include_once(DIR_WS_LANGUAGES . 'email_customers.php');
			$orders_status_array = $this->get_customer_order_status();
			$email_greeting = tep_get_email_greeting_english($email_firstname, $email_lastname, $customers_gender);
				
			$email = $email_greeting . EN_EMAIL_TEXT_ORDER_NUMBER . ' ' . (int)$orders_id . "\n"
					. EN_EMAIL_TEXT_DATE_ORDERED . ' ' . tep_date_long($select_customers_info_row['date_purchased']) . "\n" . EN_EMAIL_TEXT_INVOICE_URL . ' ' . tep_catalog_href_link(FILENAME_CATALOG_ACCOUNT_HISTORY_INFO, 'order_id=' . $orders_id, 'SSL') . "\n\n" ;
						
			$email .= sprintf(EN_EMAIL_TEXT_UPDATED_STATUS, $orders_status_array[$cur_status]) .
				 	  str_replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;", $comment) . "\n\n" . sprintf(EN_EMAIL_TEXT_CLOSING, $cat_conf_array['EMAIL_TO'], $cat_conf_array['STORE_NAME']) . "\n\n" . $cat_conf_array['STORE_EMAIL_SIGNATURE'];
			
        	tep_mail($email_firstname.' '.$email_lastname, $email_address, implode(' ', array($cat_conf_array['EMAIL_SUBJECT_PREFIX'], sprintf(EN_EMAIL_TEXT_SUBJECT, (int)$orders_id))), $email, $cat_conf_array['STORE_OWNER'], $cat_conf_array['STORE_OWNER_EMAIL_ADDRESS'], 'iso-8859-1');       	
		}
	}
}
?>