<?
/*
  	$Id: log.php,v 1.4 2008/03/06 10:39:05 wailai Exp $
	
  	Developer: <PERSON>
  	Copyright (c) 2005 SKC Ventrue
	
  	Released under the GNU General Public License
*/

class log_files {
	var $identity;
	var $table = TABLE_LOG_TABLE;
	
	function log_files($identity)
	{
		$this->identity = $identity;
	}
	
	function insert_customer_history_log($customers_email, $customer_remarks='')
	{
		$sql = sprintf(	'	INSERT INTO ' . TABLE_CUSTOMERS_REMARKS_HISTORY . ' (customers_id, date_remarks_added, remarks, remarks_added_by) ' . '
							VALUES ("%d", NOW(), "%s", "%s")',
                     		$this->identity, tep_db_input($customer_remarks), tep_db_input($customers_email));
		tep_db_query($sql);
	}
	
	function insert_cdkey_history_log($user_role, $cp_id, $sys_msg='', $user_msg='')
	{
		$sql = sprintf(	'	INSERT INTO ' . TABLE_CUSTOM_PRODUCTS_CODE_LOG . ' (custom_products_code_log_user, custom_products_code_log_user_role, log_ip, log_time, custom_products_code_id, log_system_messages, log_user_messages) ' . '
							VALUES ("%d", "%s", "%s", NOW(), "%d", "%s", "%s")',
                     		$this->identity, tep_db_input($user_role), tep_db_input(getenv("REMOTE_ADDR")), tep_db_input($cp_id), tep_db_input($sys_msg), tep_db_input($user_msg));
		tep_db_query($sql);
	}
	
	function construct_log_message($changes_array)
	{
		$message_str = array();
		if (count($changes_array)) {
			foreach ($changes_array as $key => $changes) {
				$readable_array = $this->get_readable_log_input($key, $changes['from'], $changes['to']);
				if (count($readable_array)) {
					$message_str[] = $readable_array;
				}
			}
		}
		
		return $message_str;
	}
	
	function get_readable_log_input($field_name, $old_val, $new_val) {
		$plain_result = false;
		
		$result = array();
		$old_val = trim($old_val);
		$new_val = trim($new_val);
		switch($field_name) {
			case 'customers_flag':
				$old_string = $new_string = '';
				
				if (function_exists('tep_get_user_flags')) {
					$user_flags_array = tep_get_user_flags();
				} else {
					include_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
					$user_flags_array = tep_get_user_flags();
				}
				
				$changes_array = array();
				
				$old_array = tep_not_null($old_val) ? explode(',', $old_val) : array();
				$new_array = tep_not_null($new_val) ? explode(',', $new_val) : array();
				
				$flagged_array = array_diff($new_array, $old_array);
				$unflagged_array = array_diff($old_array, $new_array);
				
				if (count($flagged_array)) {	// From Off->On
					foreach ($flagged_array as $flag_id) {
						$flag_label = $user_flags_array[$flag_id]['user_flags_name'] . (strpos($user_flags_array[$flag_id]['user_flags_name'], str_replace(':', '', ENTRY_CUSTOMERS_FLAG)) !== FALSE ? ':' : ' ' . ENTRY_CUSTOMERS_FLAG);
						$changes_array[$flag_id] = '<span class="'.$user_flags_array[$flag_id]['user_flags_css_style'].'">'.$flag_label.'</span> Off --> On' . (tep_not_null($user_flags_array[$flag_id]['user_flags_description']) ? ' (<span class="redIndicator">'.$user_flags_array[$flag_id]['user_flags_description'].'</span>)' : '');
					}
				}
				
				if (count($unflagged_array)) {	// From On->Off
					foreach ($unflagged_array as $flag_id) {
						$flag_label = $user_flags_array[$flag_id]['user_flags_name'] . (strpos($user_flags_array[$flag_id]['user_flags_name'], str_replace(':', '', ENTRY_CUSTOMERS_FLAG)) !== FALSE ? ':' : ' ' . ENTRY_CUSTOMERS_FLAG);
						$changes_array[$flag_id] = '<span class="'.$user_flags_array[$flag_id]['user_flags_css_style'].'">'.$flag_label.'</span> On --> Off';
					}
				}
				
				ksort($changes_array);
				reset($changes_array);
				
				$text = implode("\n", $changes_array);
				$plain_result = true;
				
				break;
			case 'customers_newsletter':
				$old_string = '';
				
				$old_array = tep_not_null($old_val) ? explode(',', $old_val) : array();
				$new_array = tep_not_null($new_val) ? explode(',', $new_val) : array();
				
				$new_subscribe_array = array_diff($new_array, $old_array);
				$unsubscribe_array = array_diff($old_array, $new_array);
				
				if (count($new_subscribe_array)) {
					$new_subscribe_str = '';
					
					$newsletter_group_select_sql = "SELECT newsletters_groups_name 
													FROM " . TABLE_NEWSLETTERS_GROUPS . " 
													WHERE module = 'newsletter' 
														AND newsletters_groups_id IN ('" . implode("', '", $new_subscribe_array) . "') 
													ORDER BY newsletters_groups_sort_order";
					$newsletter_group_result_sql = tep_db_query($newsletter_group_select_sql);
					
					while ($newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql)) {
						$new_subscribe_str .= $newsletter_group_row['newsletters_groups_name'] . "\n";
					}
					
					if (tep_not_null($new_subscribe_str))	$old_string .= "\n" . '<b><i>' . ENTRY_NEWSLETTER_YES . "</i></b>\n" . $new_subscribe_str;
				}
				
				if (substr($old_string, -1) == "\n")	$old_string = substr($old_string, 0, -1);
				
				if (count($unsubscribe_array)) {
					$unsubscribe_str = '';
					
					$newsletter_group_select_sql = "SELECT newsletters_groups_name 
													FROM " . TABLE_NEWSLETTERS_GROUPS . " 
													WHERE module = 'newsletter' 
														AND newsletters_groups_id IN ('" . implode("', '", $unsubscribe_array) . "') 
													ORDER BY newsletters_groups_sort_order";
					$newsletter_group_result_sql = tep_db_query($newsletter_group_select_sql);
					
					while ($newsletter_group_row = tep_db_fetch_array($newsletter_group_result_sql)) {
						$unsubscribe_str .= $newsletter_group_row['newsletters_groups_name'] . "\n";
					}
					
					if (tep_not_null($unsubscribe_str))	$old_string .= "\n" . '<b><i>' . ENTRY_NEWSLETTER_NO . "</i></b>\n" . $unsubscribe_str;
				}
				
				if (substr($old_string, -1) == "\n")	$old_string = substr($old_string, 0, -1);
				
				$text = 'Newsletter';
				
				break;
			case 'customers_groups_id':
				if ((int)$old_val > 0) {
					$cust_grp_select_sql = "SELECT customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS . " WHERE customers_groups_id = '" . (int)$old_val . "'";
					$cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
					if ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
						$old_string = $cust_grp_row["customers_groups_name"];
					} else {
						$old_string = "Customer group not found!";
					}
				}
				
				if ((int)$new_val > 0) {
					$cust_grp_select_sql = "SELECT customers_groups_name FROM " . TABLE_CUSTOMERS_GROUPS . " WHERE customers_groups_id = '" . (int)$new_val . "'";
					$cust_grp_result_sql = tep_db_query($cust_grp_select_sql);
					if ($cust_grp_row = tep_db_fetch_array($cust_grp_result_sql)) {
						$new_string = $cust_grp_row["customers_groups_name"];
					} else {
						$new_string = "Customer group not found!";
					}
				}
				$text = 'Group';
				
				break;
			case 'customers_gender':
				$old_string = ($old_val == 'm') ? 'Male' : 'Female';
				$new_string = ($new_val == 'm') ? 'Male' : 'Female';
				$text = 'Gender';
				
				break;
			case 'customers_dob':
				$old_string = tep_date_short($old_val);
				$new_string = tep_date_short($new_val);
				$text = 'Date of Birth';
				
				break;
			case 'customers_phone_verified':
				$old_string = ((int)$old_val == '1') ? 'Verify' : 'Unverify';
				$new_string = ((int)$new_val == '1') ? 'Verify' : 'Unverify';
				$text = 'Phone Verification';
				
				break;
			case 'entry_country_id':
				$old_string = tep_get_country_name($old_val);
				$new_string = tep_get_country_name($new_val);
				$text = 'Country';
				
				break;
			case 'entry_zone_id':
				return;
				break;
			case 'customers_country_dialing_code_id':
				$old_string = tep_get_country_name($old_val);
				$new_string = tep_get_country_name($new_val);
				$text = 'Location';
				break;
			case 'custom_products_code_viewed':
				$old_string = ((int)$old_val == '1') ? 'Viewed' : 'Not Viewed';
				$new_string = ((int)$new_val == '1') ? 'Viewed' : 'Not Viewed';
				$text = 'CD Key View Status';
				
				break;
			default:
				$display_label = array(	'customers_firstname' => 'First Name', 'customers_lastname' => 'Last Name', 'customers_email_address' => 'E-Mail Address', 
										'customers_telephone' => 'Telephone Number', 'customers_fax' => 'Fax Number', 'customers_mobile' => 'Mobile Number', 'customers_discount' => 'Customer Discount Rate',
										'customers_phone_verified' => 'Phone Verification', 'customers_phone_verified_datetime' => 'Phone Verification Date',										
										'entry_street_address' => 'Street Address', 'entry_postcode' => 'Post Code', 'entry_city' => 'City', 'entry_company' => 'Company',
										'entry_suburb' => 'Suburb', 'entry_state' => 'State', 'customers_msn' => 'MSN', 'customers_qq' => 'QQ', 'customers_yahoo' => 'YAHOO', 'customers_icq' => 'ICQ' 
										);
				
				$old_string = (trim($old_val) != '') ? $old_val : "EMPTY";
				$new_string = (trim($new_val) != '') ? $new_val : "EMPTY";
				$text = tep_not_null($display_label[$field_name]) ? $display_label[$field_name] : $field_name;
				
				break;
		}
		
		$result[$field_name] = array('text' => $text, 'from' => $old_string, 'to' => $new_string, 'plain_result' => ($plain_result ? '1' : '0') );
		
		return $result;
	}
	
	function detect_changes($old_data, $new_data)
	{
		$changes_array = array();
		if (count($old_data) && count($new_data)) {
			foreach ($old_data as $key => $value) {
				if (strcmp($new_data[$key], $value) !== 0) {
					$changes_array[$key] = array('from'=> $value, 'to'=> $new_data[$key]);
				}
			}
		}	
		return $changes_array;
	}
	
	function insert_log($prod_id, $field_name, $from_val='', $to_val='', $admin_msg='', $user_msg='')
	{
		$sql = sprintf(	'	INSERT INTO %s (log_admin_id, log_ip, log_time, log_products_id, log_system_messages, log_user_messages, log_field_name, log_from_value, log_to_value) ' . '
							VALUES ("%s", "%s", NOW(), %d, "%s", "%s", "%s", "%s", "%s")',
                     		$this->table, $this->identity, tep_db_input(getenv("REMOTE_ADDR")), $prod_id, tep_db_input($admin_msg),
                     		tep_db_input($user_msg), tep_db_input($field_name), tep_db_input($from_val), tep_db_input($to_val));
		tep_db_query($sql);
	}

	function insert_vip_inventory_log($prod_id, $from_val='', $to_val='')
	{
		$sql = sprintf(	'	INSERT INTO %s (customers_id, vip_supplier_inventory_logs_time, vip_supplier_inventory_logs_ip, log_from_value, log_to_value, vip_supplier_inventory_logs_product_id) ' . '
							VALUES ("%s", NOW(), "%s", "%s", "%s", %d)',
                     		$this->table, $this->identity, tep_db_input(getenv("REMOTE_ADDR")), tep_db_input($from_val), tep_db_input($to_val), tep_db_input($prod_id));
		tep_db_query($sql);
	}
	
	function update_production_log($products_id, $production_qty)
	{
		//check record exist
		$action = 'insert';
		
		$select_production_sql = "	SELECT vip_production_history_qty FROM " . TABLE_VIP_PRODUCTION_HISTORY . " 
									WHERE customers_id='".tep_db_input($this->identity)."' AND products_id='".tep_db_input($products_id)."' AND vip_production_history_date=NOW()";
		$select_production_result = tep_db_query($select_production_sql);
		if($select_production_row = tep_db_fetch_array($select_production_result)){ //if record found
			$action = 'update';
		}
		$unit_price = tep_get_product_unit_price($products_id);
		
		
		if($action == 'insert'){
			$sql = "INSERT INTO " . TABLE_VIP_PRODUCTION_HISTORY . " (customers_id, products_id, vip_production_history_date, vip_production_history_qty, unit_selling_price) 
					VALUES ('" . tep_db_input($this->identity) . "', '" . tep_db_input($products_id) . "', NOW(), '" . tep_db_input($production_qty) . "', '" . tep_db_input($unit_price) . "')";
		} else {
			$sql = "UPDATE " . TABLE_VIP_PRODUCTION_HISTORY . " 
					SET vip_production_history_qty = vip_production_history_qty + '".tep_db_input($production_qty)."', 
						unit_selling_price='".tep_db_input($unit_price)."' 
					WHERE customers_id='".tep_db_input($this->identity)."' AND 
						products_id='".tep_db_input($products_id)."' AND 
						vip_production_history_date=NOW()";
		}		
		tep_db_query($sql);
	}
	
	function contruct_changes_string($customer_changes_array, $all_customers_info_changes_made, $customer_verified_array = '') {
		if(gettype($customer_changes_array) == "array" && sizeof($customer_changes_array) > 0) {
			foreach($customer_changes_array as $field => $res) {
				if(tep_not_null($all_customers_info_changes_made)) {
	        		if(!preg_match('/(##)?('.$field.')(##)?/', $all_customers_info_changes_made)) {
	        			$all_customers_info_changes_made .= "##" . $field;
	        		}
	        	} else {
	        		$all_customers_info_changes_made .= $field;	
	        	}
			}
		}
		
		while (strstr($all_customers_info_changes_made, '####')) $all_customers_info_changes_made = str_replace('####', '##', $all_customers_info_changes_made);
		
		if (preg_match('/^##/', $all_customers_info_changes_made)) {
			$all_customers_info_changes_made = substr($all_customers_info_changes_made, 2);
	    }
	    
	    if (preg_match('/##$/', $all_customers_info_changes_made)) {
	    	$all_customers_info_changes_made = substr($all_customers_info_changes_made, 0, -2);
	    }
	    
		return $all_customers_info_changes_made;	
	}
	
	function draw_inputs($form_name="log_form", $input_array=array())
	{
		echo '	<tr>
        			<td>
        				<table border="0" width="100%" cellspacing="2" cellpadding="0">';
		foreach ($input_array as $key => $resources) {
			$input_str = "";
			switch ($resources["type"]) {
				case "date":
					$input_str = '	<script language="javascript"><!--
  										var ' . "date_$key" . ' = new ctlSpiffyCalendarBox("' . "date_$key" . '", "' . $form_name . '", "' . $key . '", "' . "btnDate_$key". '", "", scBTNMODE_CUSTOMBLUE);
										//--></script>';
					$input_str .= '	<tr>
            							<td class="main" width="10%">' . $resources["title"] . '<br><small>(' . strtoupper($resources["format"]) . ')</small></td>
            							<td class="main" align="laft"><script language="javascript">' . "date_$key" . '.writeControl(); ' . "date_$key" . '.dateFormat="' . $resources["format"] . '"; document.getElementById(\''.$key.'\').value=\''.$resources["default_value"].'\';</script>' .
            							($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . $resources["extra_msg"].'</td>
          							</tr>';
          			$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "text":
					$input_str = '	<tr>
										<td class="main">' . $resources["title"] . '</td>
										<td class="main">' . tep_draw_input_field("$key", "$resources[default_value]", "$resources[params]") . ($resources["required"] ? '<sup><span style="color:red;">*</span></sup>' : '');
					
				 	if (isset($resources["lookup"])) {
				 		$input_str .= '&nbsp;<a href="javascript:openpopup(\''. tep_href_link($resources["lookup"]["file"], $resources["lookup"]["params"]) . '\');">' . $resources["lookup"]["link"] . '</a>';
				 	}
					$input_str .= '		</td>
									</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "select":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>
							            <td class="main">' . tep_draw_pull_down_menu("$key", $resources["source"], $resources["default_value"], '') . 
							            ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>
									</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "checkbox":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>';
					if ($resources["format"] == "horizontal") {
						$input_str .= ' <td class="main">';
						foreach ($resources["source"] as $ind_checkbox) {
							$input_str .= tep_draw_checkbox_field($ind_checkbox["id"], (isset($ind_checkbox["value"]) ? $ind_checkbox["value"] : $ind_checkbox["text"]), ( $ind_checkbox["checked"] ? true : false), "", $ind_checkbox["params"]) . '&nbsp;' . $ind_checkbox["text"] . '&nbsp;&nbsp;';
						}
						$input_str .= ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					}
					if (tep_not_null($resources["js"])) {
						$input_str .= "\n" . $resources["js"] . "\n";
					}
					$input_str .= '	</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
				case "radio":
					$input_str = '	<tr >
										<td class="main">' . $resources["title"] . '</td>';
					if ($resources["format"] == "horizontal") {
						$input_str .= ' <td class="main">';
						foreach ($resources["source"] as $ind_radio) {
							$input_str .= tep_draw_radio_field($ind_radio["name"], (isset($ind_radio["value"]) ? $ind_radio["value"] : $ind_radio["text"]), ( $ind_radio["checked"] ? true : false), "", $ind_radio["params"]) . '&nbsp;' . $ind_radio["text"] . '&nbsp;&nbsp;';
						}
						$input_str .= ($resources["required"] ? '<span class="fieldRequired">*</span>' : '') . '</td>';
					}
					if (tep_not_null($resources["js"])) {
						$input_str .= "\n" . $resources["js"] . "\n";
					}
					$input_str .= '	</tr>';
					$input_str .= '	<tr>
            							<td colspan="2">' . tep_draw_separator('pixel_trans.gif', '1', '10') . '</td>
          							</tr>';
					break;
			}
			echo $input_str;
		}
		echo '			</table>
					</td>
				</tr>';
	}
	
	function set_log_table($log_table = 'TABLE_LOG_TABLE'){
		$this->table = $log_table;
	}
	
	function insert_orders_log($order_id, $orders_log_system_msg='', $orders_log_filename='')
	{
		$orders_log_admin_id = $this->identity;
		
		if (is_numeric($this->identity)) {
			$customers_email_address_select_sql = "	SELECT customers_email_address FROM " . TABLE_CUSTOMERS . " WHERE customers_id = '" . (int)$this->identity . "'";
			$customers_email_address_result_sql = tep_db_query($customers_email_address_select_sql);
			$customers_email_address_row = tep_db_fetch_array($customers_email_address_result_sql);
			
			$orders_log_admin_id = $customers_email_address_row['customers_email_address'];
		}
		
		$sql = sprintf(	'	INSERT INTO ' . TABLE_ORDERS_LOG_TABLE . ' (orders_log_admin_id, orders_log_ip, orders_log_time, orders_log_orders_id, orders_log_system_messages, orders_log_filename) ' . '
							VALUES ("%s", "%s", NOW(), "%s", "%s", "%s")',
                     		tep_db_input($orders_log_admin_id), tep_db_input(getenv("REMOTE_ADDR")), $order_id, tep_db_input($orders_log_system_msg), tep_db_input($orders_log_filename));
		tep_db_query($sql);
	}
	
}
?>