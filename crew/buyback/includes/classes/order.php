<?
/*
	$Id: order.php,v 1.3 2008/12/12 04:10:05 boonhock Exp $
	
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2003 osCommerce
	
  	Released under the GNU General Public License
*/

class order
{
	var $info, $totals, $order_totals, $price_info, $price_info_icons, $products, $compensate_products, $customer, $delivery;
	var $order_id;
	
    function order($order_id)
    {
      	$this->info = array();
      	$this->totals = array();
      	$this->products = array();
      	$this->compensate_products = array();
      	$this->customer = array();
      	$this->delivery = array();
		$this->price_info = array('delivered_price' => 0, 'canceled_price' => 0, 'reversed_price' => 0);
		$this->price_info_icons = array('delivered_price' => array('label' => 'Delivered', 'on' => 'icon_status_green.gif', 'off' => 'icon_status_green_light.gif'),
										'canceled_price' => array('label' => 'Refunded', 'on' => 'icon_status_blue.gif', 'off' => 'icon_status_blue_light.gif'),
										'reversed_price' => array('label' => 'Reversed', 'on' => 'icon_status_red.gif', 'off' => 'icon_status_red_light.gif')
									);
		$this->cb_status = array('1' => 'WIN', '2' => 'LOST', '3' => 'RESOLVED');
		
		$this->order_id = $order_id;
		
      	$this->query($order_id);
    }

	function get_cdkey_identifier($products_id, $orders_products_id)
	{
		$cdkey_identifier_arr = array();
		
		$sql = "SELECT orders_custom_products_id, orders_custom_products_value 
				FROM " . TABLE_ORDERS_CUSTOM_PRODUCTS . " 
				WHERE products_id = '". tep_db_input($products_id) ."' 
					AND orders_products_id = '" . tep_db_input($orders_products_id) . "' 
					AND orders_custom_products_key = 'cd_key_id'";
		$result = tep_db_query($sql);
		$ids_arr = array();
		while ($row = tep_db_fetch_array($result)) {
			$ids_arr[$row['orders_custom_products_id']] = $row['orders_custom_products_value'];
		}
		
		unset($row);
		
		foreach ($ids_arr as $orders_custom_products_id => $order_cp_value) {
			if (tep_not_null($order_cp_value)) {
				$sql = "SELECT custom_products_code_id, file_name, file_type, custom_products_code_viewed 
						FROM " . TABLE_CUSTOM_PRODUCTS_CODE . " 
						WHERE custom_products_code_id in (".$order_cp_value.")
						ORDER BY code_date_added asc, file_name asc";
				$result = tep_db_query($sql);	
				while ($row = tep_db_fetch_array($result)) {
					$cdkey_identifier_arr[$row['custom_products_code_id']] = array(	'key_identifier' => $row['custom_products_code_id'],
																					'file_name' => $row['file_name'],
																					'file_type' => $row['file_type'],
																					'is_viewed' => $row['custom_products_code_viewed'],
																					'orders_custom_products_id' => $orders_custom_products_id);
				}
			}
		}
		return $cdkey_identifier_arr;
	}    
    
    function query($order_id)
    {
    	global $currencies, $languages_id;
    	
    	$customer_grp_discount_array = array();
    	
      	//begin PayPal_Shopping_Cart_IPN
      	$orders_sql = "	SELECT o.*, pm.payment_methods_title,  coun.countries_international_dialing_code, coun.countries_name 
      					FROM " . TABLE_ORDERS . " AS o 
      					LEFT JOIN " . TABLE_COUNTRIES . " AS coun 
      						ON (coun.countries_name = o.customers_country) 
      					INNER JOIN " . TABLE_PAYMENT_METHODS . " AS pm 
      						ON (pm.payment_methods_id = o.payment_methods_id) 
      					WHERE o.orders_id = '" . (int)$order_id . "'";
		$order_query = tep_db_query($orders_sql);
		//end PayPal_Shopping_Cart_IPN
		
      	$order = tep_db_fetch_array($order_query);
		
      	$totals_query = tep_db_query("select title, text, class, value from " . TABLE_ORDERS_TOTAL . " where orders_id = '" . (int)$order_id . "' order by sort_order");
      	while ($totals = tep_db_fetch_array($totals_query)) {
        	$this->totals[] = array('title' => $totals['title'],
                                	'text' => $totals['text'],
                                	'value' => $totals['value'],
                                	'class' => $totals['class']
                                	);
			
			$this->order_totals[$totals['class']] = array(	'title' => $totals['title'],
						                                	'text' => $totals['text'],
						                                	'value' => $totals['value']
														);
      	}
      	
		$customer_personal_select_sql = "	SELECT c.customers_gender, civ.info_verified, c.customers_dob, c.customers_groups_id, c.customers_status, c.customers_flag, c.customers_discount, c.affiliate_ref_id, c.customers_email_address, ci.customers_info_date_account_created AS date_created 
											FROM " . TABLE_CUSTOMERS . " AS c
											LEFT JOIN " . TABLE_CUSTOMERS_INFO . " AS ci 
												ON c.customers_id = ci.customers_info_id 
											LEFT JOIN " . TABLE_CUSTOMERS_INFO_VERIFICATION . " AS civ 
												ON c.customers_id = civ.customers_id AND civ.customers_info_value = c.customers_email_address AND civ.info_verification_type = 'email' 
											LEFT JOIN " . TABLE_CUSTOMERS_GROUPS . " AS cg 
												ON c.customers_groups_id = cg.customers_groups_id
											WHERE c.customers_id = '" . $order["customers_id"] . "'";
		$customer_personal_result_sql = tep_db_query($customer_personal_select_sql);
		$customer_personal_row = tep_db_fetch_array($customer_personal_result_sql);
		
		$grp_discount_select_sql = "SELECT gd.categories_id, gd.customers_groups_discount 
									FROM " . TABLE_CUSTOMERS_GROUPS_DISCOUNT . " AS gd 
									INNER JOIN " . TABLE_CATEGORIES . " AS c 
										ON gd.categories_id=c.categories_id 
									INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd 
										ON (c.categories_id=cd.categories_id AND cd.language_id = '" . (int)$languages_id . "') 
									WHERE gd.customers_groups_id = '" . $customer_personal_row["customers_groups_id"] . "' 
									ORDER BY c.sort_order, cd.categories_name";
		$grp_discount_result_sql = tep_db_query($grp_discount_select_sql);
		while ($grp_discount_row = tep_db_fetch_array($grp_discount_result_sql)) {
			$customer_grp_discount_array[] = array(	'cat_id' => $grp_discount_row['categories_id'],
													'discount' => $grp_discount_row['customers_groups_discount']);
		}
		
      	$this->info = array('currency' => $order['currency'],
                          	'currency_value' => $order['currency_value'],
                          	'payment_method' => $order['payment_method'],
                          	'payment_methods_id' => $order['payment_methods_id'],
                          	'payment_methods_parent_id' => $order['payment_methods_parent_id'],
                          	'cc_type' => $order['cc_type'],
                          	'cc_owner' => $order['cc_owner'],
                          	'cc_number' => $order['cc_number'],
                          	'cc_expires' => $order['cc_expires'],
                          	'pm_2CO_cc_owner_firstname' => $order['pm_2CO_cc_owner_firstname'],
                          	'pm_2CO_cc_owner_lastname' => $order['pm_2CO_cc_owner_lastname'],
                          	'date_purchased' => $order['date_purchased'],
                          	'orders_status' => $order['orders_status'],
                          	'cb_status' => $order['orders_cb_status'],
						  	'paypal_ipn_id' => $order['paypal_ipn_id'],
						  	'remote_addr' => $order['remote_addr'],
                          	'last_modified' => $order['last_modified'],
                          	'follow_up' => $order['orders_follow_up_datetime'],
                          	'normal_purchase_fully_delivered' => 1
                          	);
		
      	$this->customer = array('id' => $order['customers_id'],
      							'name' => $order['customers_name'],
                              	'company' => $order['customers_company'],
                              	'street_address' => $order['customers_street_address'],
                              	'suburb' => $order['customers_suburb'],
                              	'city' => $order['customers_city'],
                              	'postcode' => $order['customers_postcode'],
                              	'state' => $order['customers_state'],
                              	'country' => $order['customers_country'],
                              	'format_id' => $order['customers_address_format_id'],
                              	'telephone_country' => (tep_not_null($order['customers_telephone_country']) ? $order['customers_telephone_country'] : $order['countries_name']),
								'order_country_code' => (tep_not_null($order['customers_country_international_dialing_code']) ? $order['customers_country_international_dialing_code'] : $order['countries_international_dialing_code']),
                              	'telephone' => $order['customers_telephone'],
                              	'email_address' => $order['customers_email_address'],
                              	'profile_email_address' => $customer_personal_row['customers_email_address'],
                              	'email_verified' => $customer_personal_row['info_verified'],
                              	'gender' => $customer_personal_row['customers_gender'],
                              	'dob' => $customer_personal_row['customers_dob'],
                              	'ref_id' => $customer_personal_row['affiliate_ref_id'],
                              	'customers_info_date_account_created' => $customer_personal_row['date_created'],
                              	'customers_discount' => $customer_personal_row['customers_discount'],
                              	'customers_groups_discount' => $customer_grp_discount_array,
                              	'account_status' => $customer_personal_row['customers_status'],
                              	'customers_flag' => explode(',', $customer_personal_row['customers_flag'])
                              	);
		
      	$this->delivery = array('name' => $order['delivery_name'],
                              	'company' => $order['delivery_company'],
                              	'street_address' => $order['delivery_street_address'],
                              	'suburb' => $order['delivery_suburb'],
                              	'city' => $order['delivery_city'],
                              	'postcode' => $order['delivery_postcode'],
                              	'state' => $order['delivery_state'],
                              	'country' => $order['delivery_country'],
                              	'format_id' => $order['delivery_address_format_id']);
		
      	$this->billing = array(	'name' => $order['billing_name'],
                             	'company' => $order['billing_company'],
                             	'street_address' => $order['billing_street_address'],
                             	'suburb' => $order['billing_suburb'],
                             	'city' => $order['billing_city'],
                             	'postcode' => $order['billing_postcode'],
                             	'state' => $order['billing_state'],
                             	'country' => $order['billing_country'],
                             	'format_id' => $order['billing_address_format_id']);
		
      	$index = 0;
      	
      	$orders_products_select_sql = "	SELECT *, IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999, 
      													NULL, 
      													(TO_DAYS(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
      												) AS purchase_eta 
      									FROM " . TABLE_ORDERS_PRODUCTS . " 
      									WHERE orders_id = '" . (int)$order_id . "' 
      										AND parent_orders_products_id < 1 
      										AND orders_products_is_compensate = 0";
      	$orders_products_query = tep_db_query($orders_products_select_sql);
      	while ($orders_products = tep_db_fetch_array($orders_products_query)) {
      		if ($this->info['currency'] == DEFAULT_CURRENCY) {
      			$products_price = tep_round($orders_products['products_price'], $currencies->currencies[$this->info['currency']]['decimal_places']);
      			$final_price = tep_round($orders_products['final_price'], $currencies->currencies[$this->info['currency']]['decimal_places']);
      		} else {
      			$products_price = $orders_products['products_price'];
      			$final_price = $orders_products['final_price'];
      		}
      		
        	$this->products[$index] = array('order_products_id' => $orders_products['orders_products_id'],
											'qty' => $orders_products['products_quantity'],
                                        	'name' => $orders_products['products_name'],
                                        	'id' => $orders_products['products_id'],
                                        	'model' => $orders_products['products_model'],
                                        	'tax' => $orders_products['products_tax'],
                                        	'price' => $products_price,
                                        	'final_price' => $final_price,
                                        	'pre_order' => $orders_products['products_pre_order'],
                                        	'custom_products_type_id' => $orders_products['custom_products_type_id'],
                                        	'delivered_qty' => $orders_products['products_delivered_quantity'],
                                        	'purchase_eta' => $orders_products['purchase_eta'],
                                        	'org_purchase_eta' => $orders_products['orders_products_purchase_eta']
                                        	);
			
			$this->products[$index]['price_info'] = array(	'delivered_price' => $orders_products['products_good_delivered_price'],
															'canceled_price' => $orders_products['products_canceled_price'],
															'reversed_price' => $orders_products['products_reversed_price']);
			$this->products[$index]['qty_info'] = array(	'delivered_quantity' => $orders_products['products_good_delivered_quantity'],
															'canceled_quantity' => $orders_products['products_canceled_quantity'],
															'reversed_quantity' => $orders_products['products_reversed_quantity']
														);
			
			$this->price_info['delivered_price'] += $orders_products['products_good_delivered_price'];
			$this->price_info['canceled_price'] += $orders_products['products_canceled_price'];
			$this->price_info['reversed_price'] += $orders_products['products_reversed_price'];
			
			if ($cdkey_info = $this->get_cdkey_identifier($orders_products['products_id'], $orders_products['orders_products_id'])) {
				$this->products[$index]['cdkey_info'] = $cdkey_info;                                        	
			}
			
        	$subindex = 0;
        	$attributes_query = tep_db_query("select products_options, products_options_values, options_values_price, price_prefix from " . TABLE_ORDERS_PRODUCTS_ATTRIBUTES . " where orders_id = '" . (int)$order_id . "' and orders_products_id = '" . (int)$orders_products['orders_products_id'] . "'");
        	if (tep_db_num_rows($attributes_query)) {
          		while ($attributes = tep_db_fetch_array($attributes_query)) {
            		$this->products[$index]['attributes'][$subindex] = array(	'option' => $attributes['products_options'],
                                                                     			'value' => $attributes['products_options_values'],
                                                                     			'prefix' => $attributes['price_prefix'],
                                                                     			'price' => $attributes['options_values_price']);
					
            		$subindex++;
          		}
        	}
        	
        	$product_query = tep_db_query("SELECT products_bundle, products_bundle_dynamic FROM " . TABLE_PRODUCTS . " WHERE products_id = '" . $orders_products["products_id"] . "'");
			$product = tep_db_fetch_array($product_query);
			if ($product["products_bundle_dynamic"] == "yes") {
				$b_index = 0;
				$product_bundle_dynamic_select_sql = "	SELECT orders_products_id, products_id, products_model, products_name, products_quantity, products_delivered_quantity, products_categories_id, orders_products_purchase_eta, 
															products_good_delivered_quantity, products_canceled_quantity, products_reversed_quantity, 
															IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999, 
		      													NULL, 
		      													(TO_DAYS(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
		      												) AS purchase_eta 
														FROM " . TABLE_ORDERS_PRODUCTS . " 
														WHERE orders_id = '" . (int)$order_id . "' 
															AND parent_orders_products_id = '" . $orders_products["orders_products_id"] . "' 
															AND orders_products_is_compensate=0";
				$product_bundle_dynamic_result_sql = tep_db_query($product_bundle_dynamic_select_sql);
				
				while ($product_bundle = tep_db_fetch_array($product_bundle_dynamic_result_sql)) {
					$this->products[$index]['bundle'][$b_index] = array('id' => $product_bundle['products_id'],
																		'order_products_id' => $product_bundle['orders_products_id'],
																		'qty' => $product_bundle['products_quantity'],
	                                        							'name' => $product_bundle['products_name'],
	                                        							'model' => $product_bundle['products_model'],
	                                        							'products_categories_id' => $product_bundle['products_categories_id'],
	                                        							'delivered_qty' => $product_bundle['products_delivered_quantity'],
	                                        							'purchase_eta' => $product_bundle['purchase_eta'],
	                                        							'org_purchase_eta' => $product_bundle['orders_products_purchase_eta']
	                                        							);
					$this->products[$index]['bundle'][$b_index]['qty_info'] = array(	'delivered_quantity' => $product_bundle['products_good_delivered_quantity'],
																						'canceled_quantity' => $product_bundle['products_canceled_quantity'],
																						'reversed_quantity' => $product_bundle['products_reversed_quantity']
																					);
					if ($product_bundle['products_delivered_quantity'] < $product_bundle['products_quantity'])	$this->info['normal_purchase_fully_delivered'] = 0;
					
					$b_index++;
				}
			} else if ($product["products_bundle"] == "yes") {
				$s_index = 0;
				$static_bundle_select_sql = "	SELECT orders_products_id, products_id, products_model, products_name, products_quantity, products_delivered_quantity, products_categories_id, orders_products_purchase_eta, 
													products_good_delivered_quantity, products_canceled_quantity, products_reversed_quantity, 
													IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999, 
      													NULL, 
      													(TO_DAYS(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$order['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
      												) AS purchase_eta 
												FROM " . TABLE_ORDERS_PRODUCTS . " 
												WHERE orders_id = '" . (int)$order_id . "' 
													AND parent_orders_products_id = '" . $orders_products["orders_products_id"] . "' 
													AND orders_products_is_compensate = 0";
				$static_bundle_result_sql = tep_db_query($static_bundle_select_sql);
				while ($static_bundle = tep_db_fetch_array($static_bundle_result_sql)) {
					$this->products[$index]['static'][$s_index] = array('id' => $static_bundle['products_id'],
																		'order_products_id' => $static_bundle['orders_products_id'],
																		'qty' => $static_bundle['products_quantity'],
	                                        							'name' => $static_bundle['products_name'],
	                                        							'model' => $static_bundle['products_model'],
	                                        							'products_categories_id' => $static_bundle['products_categories_id'],
	                                        							'delivered_qty' => $static_bundle['products_delivered_quantity'],
	                                        							'purchase_eta' => $static_bundle['purchase_eta'],
	                                        							'org_purchase_eta' => $static_bundle['orders_products_purchase_eta']
	                                        							);
					
					$this->products[$index]['static'][$s_index]['qty_info'] = array(	'delivered_quantity' => $static_bundle['products_good_delivered_quantity'],
																						'canceled_quantity' => $static_bundle['products_canceled_quantity'],
																						'reversed_quantity' => $static_bundle['products_reversed_quantity']
																					);
					if ($static_bundle['products_delivered_quantity'] < $static_bundle['products_quantity'])	$this->info['normal_purchase_fully_delivered'] = 0;
					$subproduct_type_select_sql = "	SELECT custom_products_type_id 
								    				FROM " . TABLE_PRODUCTS . " 
								    				WHERE products_id = '" . tep_db_input($static_bundle["products_id"]) . "'";
					$subproduct_type_result_sql = tep_db_query($subproduct_type_select_sql);
					
					while ($subproduct_type_row = tep_db_fetch_array($subproduct_type_result_sql)) {
						if ($subproduct_type_row['custom_products_type_id']==2) {
							if ($cdkey_info = $this->get_cdkey_identifier($static_bundle['products_id'], $static_bundle['orders_products_id'])) {
								$this->products[$index]['static'][$s_index]['cdkey_info'] = $cdkey_info;                                        	
							}
						}
					}
					$s_index++;
				}
			} else {
				if ($orders_products['products_delivered_quantity'] < $orders_products['products_quantity'])	$this->info['normal_purchase_fully_delivered'] = 0;
			}
			
        	$index++;
      	}
	}
	
	function format_character_name($matches) {
		// as usual: $matches[0] is the complete match
		// $matches[1] the match for the first subpattern
		// enclosed in '(...)' and so on
		
		$formated_string = preg_replace('/([^A-Z]+)/s', "<span style='color:#0A246A;'>\\1</span>", preg_quote($matches[1], "/"));
		$formated_string = tep_db_prepare_input($formated_string);
		
		return "<span style='color:#019858;'>".$formated_string."</span>";
	}
	
	function get_product_order_info() {
		$total_ordered_product = count($this->products);
		$styling_keys_array = array('char_name', 'char_account_name', 'char_account_pwd');
		
		for ($prod_cnt=0; $prod_cnt < $total_ordered_product; $prod_cnt++) {
			$this->products[$prod_cnt]['extra_info'] = array();
			
			$extra_info_select_sql = "	SELECT orders_products_extra_info_key, orders_products_extra_info_value 
										FROM " . TABLE_ORDERS_PRODUCTS_EXTRA_INFO . " 
										WHERE orders_products_id = '" . tep_db_input($this->products[$prod_cnt]['order_products_id']) . "'";
			$extra_info_result_sql = tep_db_query($extra_info_select_sql);
			
			while ($extra_info_row = tep_db_fetch_array($extra_info_result_sql)) {
				$value = $extra_info_row['orders_products_extra_info_value'];
				
				if (in_array($extra_info_row['orders_products_extra_info_key'], $styling_keys_array)) {
					$value = preg_replace_callback(	'/([^a-z]+)/s',
              										array($this, 'format_character_name'),
              										$value);
					
					if (tep_not_null($value))	$value = '<span style="color: red; font-size: 15px;"><b>'.$value.'</b></span>';
				}
				
				$this->products[$prod_cnt]['extra_info'][$extra_info_row['orders_products_extra_info_key']] = $value;
			}
		}
	}
	
	function get_compensate_products() {
		$index = 0;
		$this->info['compensation_fully_delivered'] = 1;
		
		$compensate_product_select_sql = "	SELECT *, 
												IF(	orders_products_purchase_eta IS NULL or orders_products_purchase_eta=-999, 
  													NULL, 
  													(TO_DAYS(DATE_ADD('".$this->info['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR))*24*3600 + TIME_TO_SEC(DATE_ADD('".$this->info['date_purchased']."', INTERVAL orders_products_purchase_eta HOUR)) - (TO_DAYS(now())*24*3600 + TIME_TO_SEC(now()))) / 3600
  												) AS purchase_eta 
											FROM " . TABLE_ORDERS_PRODUCTS . " 
											WHERE orders_id = '" . (int)$this->order_id . "' 
												AND orders_products_is_compensate=1
											ORDER BY orders_products_id";
		$compensate_product_result_sql = tep_db_query($compensate_product_select_sql);
      	while ($compensate_product_row = tep_db_fetch_array($compensate_product_result_sql)) {
        	$this->compensate_products[$index] = array(	'order_products_id' => $compensate_product_row['orders_products_id'],
														'qty' => $compensate_product_row['products_quantity'],
			                                        	'name' => $compensate_product_row['products_name'],
            			                            	'id' => $compensate_product_row['products_id'],
                        			                	'price' => $compensate_product_row['products_price'],
            			                            	'final_price' => $compensate_product_row['final_price'],
                        			                	'pre_order' => $compensate_product_row['products_pre_order'],
                                    			    	'custom_products_type_id' => $compensate_product_row['custom_products_type_id'],
			                                        	'delivered_qty' => $compensate_product_row['products_delivered_quantity'],
            			                            	'purchase_eta' => $compensate_product_row['purchase_eta'],
                        			                	'org_purchase_eta' => $compensate_product_row['orders_products_purchase_eta']
                                    		    	);
			
			$this->compensate_products[$index]['price_info'] = array(	'delivered_price' => $compensate_product_row['products_good_delivered_price'],
																		'canceled_price' => $compensate_product_row['products_canceled_price'],
																		'reversed_price' => $compensate_product_row['products_reversed_price']);
			
			if ($compensate_product_row['products_delivered_quantity'] < $compensate_product_row['products_quantity'])	$this->info['compensation_fully_delivered'] = 0;
			
			if ($cdkey_info = $this->get_cdkey_identifier($compensate_product_row['products_id'], $compensate_product_row['orders_products_id'])) {
				$this->compensate_products[$index]['cdkey_info'] = $cdkey_info;                                        	
			}
			
			$compensation_select_sql = "SELECT compensate_for_orders_products_id, compensate_entered_currency, compensate_entered_currency_value, compensate_order_currency, compensate_order_currency_value, compensate_accident_amount, compensate_non_accident_amount, compensate_supplier_amount, compensate_by_supplier_id, 
											orders_compensate_products_added_by, orders_compensate_products_messages 
										FROM " . TABLE_ORDERS_COMPENSATE_PRODUCTS . " 
										WHERE orders_products_id = '" . tep_db_input($compensate_product_row['orders_products_id']) . "'";
			$compensation_result_sql = tep_db_query($compensation_select_sql);
	      	if ($compensation_row = tep_db_fetch_array($compensation_result_sql)) {
	      		$this->compensate_products[$index]['compensate'] = array(	'for_product' => $compensation_row['compensate_for_orders_products_id'],
																			'input_currency' => $compensation_row['compensate_entered_currency'],
																			'input_currency_value' => $compensation_row['compensate_entered_currency_value'],
																			'output_currency' => $compensation_row['compensate_order_currency'],
																			'output_currency_value' => $compensation_row['compensate_order_currency_value'],
																			'accident_amount' => $compensation_row['compensate_accident_amount'],
																			'non_accident_amount' => $compensation_row['compensate_non_accident_amount'],
																			'supplier_amount' => $compensation_row['compensate_supplier_amount'],
																			'supplier_id' => $compensation_row['compensate_by_supplier_id'],
																			'added_by' => $compensation_row['orders_compensate_products_added_by'],
																			'comments' => $compensation_row['orders_compensate_products_messages']
																			);
		    }
		    
			$index++;
		}
	}
	
	function get_products_ordered()
	{
		global $languages_id, $currencies;
		$products_ordered = '';
		for ($i=0, $n=sizeof($this->products); $i<$n; $i++) {
		    $products_ordered_attributes = '';
		    if (isset($this->products[$i]['attributes'])) {
		      	for ($j=0, $n2=sizeof($this->products[$i]['attributes']); $j<$n2; $j++) {
		      		if (DOWNLOAD_ENABLED == 'true') {
		          		$attributes_query = "	select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix, pad.products_attributes_maxdays, pad.products_attributes_maxcount , pad.products_attributes_filename
		                               			from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa
		                               			left join " . TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD . " pad
		                                			on pa.products_attributes_id=pad.products_attributes_id
		                               			where pa.products_id = '" . $this->products[$i]['id'] . "'
					                                and pa.options_id = '" . $this->products[$i]['attributes'][$j]['option_id'] . "'
					                                and pa.options_id = popt.products_options_id
					                                and pa.options_values_id = '" . $this->products[$i]['attributes'][$j]['value_id'] . "'
					                                and pa.options_values_id = poval.products_options_values_id
					                                and popt.language_id = '" . $languages_id . "'
					                                and poval.language_id = '" . $languages_id . "'";
		          		$attributes = tep_db_query($attributes_query);
		        	} else {
		          		$attributes = tep_db_query("select popt.products_options_name, poval.products_options_values_name, pa.options_values_price, pa.price_prefix from " . TABLE_PRODUCTS_OPTIONS . " popt, " . TABLE_PRODUCTS_OPTIONS_VALUES . " poval, " . TABLE_PRODUCTS_ATTRIBUTES . " pa where pa.products_id = '" . $this->products[$i]['id'] . "' and pa.options_id = '" . $this->products[$i]['attributes'][$j]['option_id'] . "' and pa.options_id = popt.products_options_id and pa.options_values_id = '" . $this->products[$i]['attributes'][$j]['value_id'] . "' and pa.options_values_id = poval.products_options_values_id and popt.language_id = '" . $languages_id . "' and poval.language_id = '" . $languages_id . "'");
		        	}
		        	$attributes_values = tep_db_fetch_array($attributes);
		        	
		        	$products_ordered_attributes .= "\n\t" . $attributes_values['products_options_name'] . ' ' . $attributes_values['products_options_values_name'];
		      	}
			}
			
			$cat_path = tep_output_generated_category_path($this->products[$i]['id'], 'product');
	      	$product_email_display_str = $cat_path . (tep_not_null($cat_path) ? ' > ' : '') . $this->products[$i]['name'] . (tep_not_null($this->products[$i]['model']) ? ' (' . $this->products[$i]['model'] . ')' : '');
		    
		    $products_ordered .= $this->products[$i]['qty'] . ' x ' . $product_email_display_str . ' = ' . $currencies->display_price($this->products[$i]['final_price'], $this->products[$i]['tax'], $this->products[$i]['qty']) . $products_ordered_attributes . "\n";
		}
		
		return $products_ordered;
	}
	
	function get_payments_breakdown()
	{
		$payments_breakdown = '';
		for ($i=0, $n=sizeof($this->totals); $i<$n; $i++) {
			$payments_breakdown .= strip_tags($this->totals[$i]['title']) . ' ' . strip_tags($this->totals[$i]['text']) . "\n";
		}
		
		return $payments_breakdown;
	}
	
	function do_payment_action($fn_name, $language)
	{
		$payment_module_obj = new payment_methods($this->info['payment_methods_id']);
		$selected_payment_module_obj = $payment_module_obj->payment_method_array;
		
		if (is_object($selected_payment_module_obj) && ((int)$selected_payment_module_obj->payment_methods_id > 0)) {
			eval('$selected_payment_module_obj->$fn_name($this->order_id);');	// Use single quote
		}
	}
}
?>