<?php

class payment_methods {
	var $payment_methods_id;
	var $payment_methods_parent_id;
	var $payment_methods_filename;
	var $payment_methods_class_name;
	var $payment_methods_title;
	var $payment_methods_sort_order;
	var $payment_methods_legend_color;
	var $payment_methods_receive_status_mode;
	var $payment_methods_code;
	var $payment_methods_description_title;
	var $payment_methods_instance_key_info;
	var $payment_methods_types_id;
	
	var $payment_method_array;		// for selected payment method
	var $payment_methods_array;		// for all payment methods
	var $payment_gateways_array;	// for all payment gateways
	
	var $payment_methods_size;
	
	// class constructor
    function payment_methods($pmID='') {
    	if (is_numeric($pmID) && (int)$pmID>0 && $pm_action == '') {
	    	
	    	$filename_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_filename 
									FROM " . TABLE_PAYMENT_METHODS . " as pm
									WHERE pm.payment_methods_parent_id = '0'
										AND pm.payment_methods_receive_status = '1'
										AND pm.payment_methods_filename IS NOT NULL";
			
			$filename_result_sql = tep_db_query($filename_select_sql);
			
			$filename_result_array = array();
			while ($filename_row = tep_db_fetch_array($filename_result_sql)) {
				$filename_result_array[$filename_row['payment_methods_id']] = $filename_row['payment_methods_filename'];
			}
			
			$payment_methods_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_parent_id, pmsd.payment_methods_status_message, 
												pm.payment_methods_logo, pm.payment_methods_types_id 
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											LEFT JOIN " . TABLE_PAYMENT_METHODS_STATUS_DESCRIPTION . " as pmsd
												ON pm.payment_methods_id = pmsd.payment_methods_id
													AND pmsd.payment_methods_mode = 'RECEIVE'
													AND pm.payment_methods_id = pmsd.payment_methods_status
											WHERE pm.payment_methods_receive_status = '1'
												AND pm.payment_methods_id = '".(int)$pmID."'";
			
			$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
			
			if ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
				
				$this->payment_methods_id = $payment_methods_row['payment_methods_id'];
				
				if (tep_not_null($payment_methods_row['payment_methods_filename'])) {
					$filename = $payment_methods_row['payment_methods_filename'];
				} else {
					$filename = $filename_result_array[$payment_methods_row['payment_methods_parent_id']];
				}
				$module_class = substr($filename, 0, strrpos($filename, '.'));
				
				$this->payment_method_array = array();
				if (!tep_class_exists($module_class)) {
					if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php')) {
						include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php');
					}
					if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php')) {
						include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php');
					}
				}
				
				eval('$pm_module = new '.$module_class.'('.$payment_methods_row['payment_methods_id'].');');
				
				$this->payment_method_array = $pm_module;
				
				$this->payment_methods_class_name = $module_class;
				
				$this->payment_methods_title = $pm_module->title;
				$this->payment_methods_sort_order = $pm_module->sort_order;
				$this->payment_methods_legend_color = $pm_module->legend_display_colour;
				$this->payment_methods_receive_status_mode = $pm_module->enabled;
				$this->payment_methods_description_title = $pm_module->display_title;
				$this->payment_methods_code = $pm_module->code;
				$this->payment_methods_parent_id = $pm_module->payment_methods_parent_id;
				$this->payment_methods_filename = $filename;
				
				$this->payment_methods_status_message = $payment_methods_row['payment_methods_status_message'];  // for now support -1 only
				$this->payment_methods_logo = $payment_methods_row['payment_methods_logo'];
				$this->payment_methods_types_id = (int)$payment_methods_row['payment_methods_types_id'];
				
				$pm_types_select_sql = "SELECT pmt.payment_methods_types_name, pmt.payment_methods_types_id
										FROM " . TABLE_PAYMENT_METHODS_TYPES . " as pmt
										WHERE pmt.payment_methods_types_id = '".(int)$payment_methods_row['payment_methods_types_id']."'";
				$pm_types_result_sql = tep_db_query($pm_types_select_sql);
				$pm_types_row = tep_db_fetch_array($pm_types_result_sql);
				
				$this->payment_methods_types_name = $pm_types_row['payment_methods_types_name']; 
				
			}
	    	
	    } else {
	    	switch ($pmID) {
	    		case 'count_payment_methods':
	    			$payment_methods_select_sql = "	SELECT count(pm.payment_methods_id) as total 
													FROM " . TABLE_PAYMENT_METHODS . " as pm
													WHERE payment_methods_receive_status = '1'
														AND payment_methods_parent_id != '0'";
					$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
					$payment_methods_row = tep_db_fetch_array($payment_methods_result_sql);
					$this->payment_methods_size = (int)$payment_methods_row['total'];

	    			break;
				
	    		case 'payment_gateways':
			    	$payment_gateway_file_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_title 
														FROM " . TABLE_PAYMENT_METHODS . " as pm
														WHERE pm.payment_methods_parent_id = '0'
															AND pm.payment_methods_receive_status = '1' 
															AND pm.payment_methods_filename IS NOT NULL";
					
					$payment_gateway_file_result_sql = tep_db_query($payment_gateway_file_select_sql);
					
					$this->payment_gateways_array = array();
					while ($payment_gateway_file_row = tep_db_fetch_array($payment_gateway_file_result_sql)) {
						
						$module_class = substr($payment_gateway_file_row['payment_methods_filename'], 0, strrpos($payment_gateway_file_row['payment_methods_filename'], '.'));
						
						if (!tep_class_exists($module_class)) {
							if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php')) {
								include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php');
							}
							if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php')) {
								include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php');
							}
						}
						
						eval('$pm_module = new '.$module_class.'('.$payment_gateway_file_row['payment_methods_id'].');');
						
						$this->payment_gateways_array[$payment_gateway_file_row['payment_methods_id']] = $pm_module;
					}
	    			break;

	    		case 'payment_methods':
	    			
					$filename_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_filename 
											FROM " . TABLE_PAYMENT_METHODS . " as pm
											WHERE pm.payment_methods_parent_id = '0'
												AND pm.payment_methods_filename IS NOT NULL";
					
					$filename_result_sql = tep_db_query($filename_select_sql);
					
					$filename_result_array = array();
					while ($filename_row = tep_db_fetch_array($filename_result_sql)) {
						$filename_result_array[$filename_row['payment_methods_id']] = $filename_row['payment_methods_filename'];
					}
					
	    			$payment_methods_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_parent_id 
													FROM " . TABLE_PAYMENT_METHODS . " as pm
													WHERE payment_methods_receive_status = '1'
														AND payment_methods_parent_id != '0'";
					
					$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
					
					$this->payment_methods_array = array();
					
					while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
						
						$filename = $filename_result_array[$payment_methods_row['payment_methods_parent_id']];
						$module_class = substr($filename, 0, strrpos($filename, '.'));
						
						$this->payment_methods_array[$payment_methods_row['payment_methods_id']] = array();
						
						if (!tep_class_exists($module_class)) {
							if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php')) {
								include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php');
							}
							if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php')) {
								include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php');
							}
						}
						
						eval('$pm_module = new '.$module_class.'('.$payment_methods_row['payment_methods_id'].');');
						
						$this->payment_methods_array[$payment_methods_row['payment_methods_id']] = $pm_module;
					}
					
					$this->payment_methods_size = count($this->payment_methods_array);
	    			break;
	    			
	    		case 'all':
	    		
					$this->payment_methods_array = array();
					
			    	$payment_gateway_file_select_sql = "SELECT pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_title 
														FROM " . TABLE_PAYMENT_METHODS . " as pm
														WHERE pm.payment_methods_parent_id = '0'
															AND pm.payment_methods_receive_status = '1' 
															AND pm.payment_methods_filename IS NOT NULL";
					$payment_gateway_file_result_sql = tep_db_query($payment_gateway_file_select_sql);
					
					$filename_result_array = array();
					while ($payment_gateway_file_row = tep_db_fetch_array($payment_gateway_file_result_sql)) {
						
						$module_class = substr($payment_gateway_file_row['payment_methods_filename'], 0, strrpos($payment_gateway_file_row['payment_methods_filename'], '.'));
						
						if (!tep_class_exists($module_class)) {
							if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php')) {
								include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php');
							}
							if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php')) {
								include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php');
							}
						}
						
						eval('$pm_module = new '.$module_class.'('.$payment_gateway_file_row['payment_methods_id'].');');
						
						$this->payment_methods_array[$payment_gateway_file_row['payment_methods_id']] = $pm_module;
						$filename_result_array[$payment_gateway_file_row['payment_methods_id']] = $payment_gateway_file_row['payment_methods_filename'];
					}
					
	    			$payment_methods_select_sql = "	SELECT pm.payment_methods_id, pm.payment_methods_filename, pm.payment_methods_parent_id 
													FROM " . TABLE_PAYMENT_METHODS . " as pm
													WHERE payment_methods_receive_status = '1'
														AND payment_methods_parent_id != '0'";
					$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
					
					while ($payment_methods_row = tep_db_fetch_array($payment_methods_result_sql)) {
						$filename = $filename_result_array[$payment_methods_row['payment_methods_parent_id']];
						$module_class = substr($filename, 0, strrpos($filename, '.'));
						$this->payment_methods_array[$payment_methods_row['payment_methods_id']] = array();
						if (!tep_class_exists($module_class)) {
							if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php')) {
								include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class . '.php');
							}
							if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php')) {
								include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$module_class.'/admin/languages/english/' .$module_class .'.lng.php');
							}
						}
						eval('$pm_module = new '.$module_class.'('.$payment_methods_row['payment_methods_id'].');');
						$this->payment_methods_array[$payment_methods_row['payment_methods_id']] = $pm_module;
					}
	    			break;
		    }
	    }
	}

	function get_pm_display_title() {
		if ((int)$this->payment_methods_parent_id==0) return $this->payment_methods_title;
		
		$parent_select_sql = "	SELECT payment_methods_title
								FROM " . TABLE_PAYMENT_METHODS . "
								WHERE payment_methods_id = '".$this->payment_methods_parent_id."'";
		$parent_result_sql = tep_db_query($parent_select_sql);
		if ($parent_row = tep_db_fetch_array($parent_result_sql)) {
			return $parent_row['payment_methods_title'] ." > " . $this->payment_methods_title;
		}
	}
	
	function get_payment_method_configuration_key($language_id=1) {
		$payment_configuration_info_select_sql = "	SELECT pci.payment_configuration_info_title, pci.payment_configuration_info_key, pcid.payment_configuration_info_value, pci.payment_configuration_info_description, pci.use_function, pci.set_function  
													FROM " . TABLE_PAYMENT_CONFIGURATION_INFO . " as pci
													LEFT JOIN " . TABLE_PAYMENT_CONFIGURATION_INFO_DESCRIPTION . " as pcid
														ON pci.payment_configuration_info_id = pcid.payment_configuration_info_id 
															AND pcid.languages_id = '".(int)$language_id."' 
													WHERE pci.payment_methods_id = '".(int)((int)$this->payment_methods_parent_id>0?$this->payment_methods_parent_id:$this->payment_methods_id)."' 
													GROUP BY payment_configuration_info_key
													ORDER BY pci.payment_configuration_info_sort_order ";
		$payment_configuration_info_result_sql = tep_db_query($payment_configuration_info_select_sql);
		$payment_configuration_info_array = array();
		while ($payment_configuration_info_row = tep_db_fetch_array($payment_configuration_info_result_sql)) {
			$payment_configuration_info_array[$payment_configuration_info_row['payment_configuration_info_key']] = array (	'payment_configuration_info_title'=>$payment_configuration_info_row['payment_configuration_info_title'],
																															'payment_configuration_info_value'=>$payment_configuration_info_row['payment_configuration_info_value'],
																															'payment_configuration_info_description'=>$payment_configuration_info_row['payment_configuration_info_description'],
																															'use_function'=>$payment_configuration_info_row['use_function'],
																															'set_function'=>$payment_configuration_info_row['set_function']);
		}
		return $payment_configuration_info_array;
	}

	function set_instance_key_info() {
		if (file_exists(DIR_FS_CATALOG_MODULES . 'payment/'.$this->payment_methods_filename)) {
			include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$this->payment_methods_filename);
			include_once(DIR_FS_CATALOG_MODULES . 'payment/'.$this->payment_methods_class_name.'/admin/languages/english/' .$this->payment_methods_class_name .'.lng.php');

			if (tep_class_exists($this->payment_methods_class_name)) {
				$module = new $this->payment_methods_class_name();
				$this->payment_methods_instance_key_info = $module->merchant_information_keys();
			}
		}
	}
	
	function get_payment_methods_types($mode = 'RECEIVE') {
		$payment_methods_types_select_sql = "	SELECT payment_methods_types_id, payment_methods_types_name
												FROM " . TABLE_PAYMENT_METHODS_TYPES . "
												WHERE payment_methods_types_mode = '" . tep_db_input($mode) . "'
												ORDER BY payment_methods_types_sort_order";
		$payment_methods_types_result_sql = tep_db_query($payment_methods_types_select_sql);
		$payment_methods_types_array = array();
		while ($payment_methods_types_row = tep_db_fetch_array($payment_methods_types_result_sql)) {
			$payment_methods_types_array[$payment_methods_types_row['payment_methods_types_id']] = $payment_methods_types_row['payment_methods_types_name'];
		}
		return $payment_methods_types_array;
	}

	function set_payment_methods_filename() {
		$payment_methods_filename_select_sql = "SELECT payment_methods_filename
												FROM " . TABLE_PAYMENT_METHODS . "
												WHERE payment_methods_id = '".(int)((int)$this->payment_methods_parent_id>0?$this->payment_methods_parent_id:$this->payment_methods_id)."'";
		$payment_methods_filename_result_sql = tep_db_query($payment_methods_filename_select_sql);
		if ($payment_methods_filename_row = tep_db_fetch_array($payment_methods_filename_result_sql)) {
			$this->payment_methods_filename = $payment_methods_filename_row['payment_methods_filename'];
		} else {
			$this->payment_methods_filename = '';
		}
	}

	function get_number_of_child() {
		$payment_methods_select_sql = "	SELECT pm.payment_methods_id
										FROM " . TABLE_PAYMENT_METHODS . " as pm
										WHERE pm.payment_methods_parent_id = '".(int)$this->payment_methods_id."'";
		$payment_methods_result_sql = tep_db_query($payment_methods_select_sql);
		return tep_db_num_rows($payment_methods_result_sql);
	}

	function get_payment_methods_class_name() {
		return $this->payment_methods_class_name;
	}

	function get_status_message() {
		return $this->payment_methods_status_message;
	}

	function get_types_id() {
		return $this->payment_methods_types_id;
	}
		
	function get_types_name() {
		return $this->payment_methods_types_name;
	}
	
	function get_logo() {
		return $this->payment_methods_logo;
	}
		
	function get_title() {
		return $this->payment_methods_title;
	}

	function get_display_title() {
		return $this->payment_methods_description_title;
	}
	
	function get_sort_order() {
		return $this->payment_methods_sort_order;
	}

	function get_code() {
		return $this->payment_methods_code;
	}

	function get_legend_color() {
		return $this->payment_methods_legend_color;
	}
	
	function get_receive_status() {
		return $this->payment_methods_receive_status_mode;
	}

	function get_parent_id() {
		return $this->payment_methods_parent_id;
	}
	
	function get_filename() {
		return $this->payment_methods_filename;
	}

	function get_instance_key_info() {
		return $this->payment_methods_instance_key_info;
	}
}
?>