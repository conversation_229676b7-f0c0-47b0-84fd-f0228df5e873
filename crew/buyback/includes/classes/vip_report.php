<?

/****************************************************************************************
		REPORT MODE
	1 : DAILY_PRODUCTION
	2 : DAILY_STOCK_LEFT
	3 : DAILY_REVENUE_ON_OFFGAMERS_SELLING_PRICE_FOR_DAILY_PRODUCTION
	4 : DAILY_REVENUE_ON_ORDER_COMPLETED_ON_OFFGAMERS
******************************************************************************************/

class vip_report {
	var $customer_id;
	var $categories_id;
	var $report_type;
	var $start_date;
	var $end_date;
	var $start_date_display_array = array();
	var $report_info = array();
	var $max_display_days = 30;
	
	function vip_report($customer_id, $cID, $report_type, $start_date='', $end_date='') {
		$this->customer_id = $customer_id;
		$this->categories_id = $cID;
		$this->report_type = $report_type;

		$start_date_array = explode('-', $start_date);
		$end_date_array = explode('-', $end_date);
		
		$start_date_timestamp = mktime(0, 0, 0, $start_date_array["1"], $start_date_array["2"], $start_date_array["0"]);
		$end_date_timestamp = mktime(0, 0, 0, $end_date_array["1"], $end_date_array["2"], $end_date_array["0"]);
		
		if ($start_date) {
			$this->start_date = mktime(0, 0, 0, date("m", $start_date_timestamp), date("d", $start_date_timestamp), date("Y", $start_date_timestamp));
		} else {
			$this->start_date = mktime(0, 0, 0, date("m"), date("d"), date("Y"));
		}
		
		if ($end_date) {
			$this->end_date = mktime(0, 0, 0, date("m", $end_date_timestamp), date("d", $end_date_timestamp), date("Y", $end_date_timestamp));
		} else {
			$this->end_date = mktime(0, 0, 0, date("m"), date("d"), date("Y"));
		}
		
		$diff_stamp = $this->end_date - $this->start_date;
		$days_diff = $diff_stamp/(24 * 60 * 60) + 1;
		if ($days_diff > $this->max_display_days){
			$days_diff = $this->max_display_days;
		}
		
		for ($day_cnt = 0; $day_cnt < $days_diff;  $day_cnt++){
			$this->start_date_display_array[$day_cnt] = mktime(0, 0, 0, date("m", $this->start_date), date("d", $this->start_date) + $day_cnt, date("Y", $this->start_date));
		}
		
	}
	
	function vip_report_query(){
		global $currencies;
		$_DECIMAL_PLACES_DISPLAY = 4; //Display
		$currencies->set_decimal_places($_DECIMAL_PLACES_DISPLAY);
		switch($this->report_type){
			case '1': 
				//daily production
				$query = "	SELECT products_id, vip_production_history_date AS history_date, vip_production_history_qty AS quantity
							FROM " . TABLE_VIP_PRODUCTION_HISTORY . " 
							WHERE customers_id='" . $this->customer_id . "' 
								AND DATE_FORMAT(vip_production_history_date, '%Y-%m-%d') >= DATE_FORMAT('" . date("Y-m-d", $this->start_date) . "','%Y-%m-%d')  
								AND DATE_FORMAT(vip_production_history_date, '%Y-%m-%d') <= DATE_FORMAT('" . date("Y-m-d", $this->end_date) . "','%Y-%m-%d')
							ORDER BY vip_production_history_date DESC";	
				break;		
				
			case '2':
				//daily stock left
				$query = "	SELECT products_id, vip_stock_history_date AS history_date, vip_stock_history_qty AS quantity
							FROM " . TABLE_VIP_STOCK_HISTORY . " 
							WHERE customers_id='" . $this->customer_id . "' 
								AND DATE_FORMAT( vip_stock_history_date, '%Y-%m-%d') >= DATE_FORMAT('" . date("Y-m-d", $this->start_date) . "','%Y-%m-%d')  
								AND DATE_FORMAT( vip_stock_history_date, '%Y-%m-%d') <= DATE_FORMAT('" . date("Y-m-d", $this->end_date) . "','%Y-%m-%d')
							ORDER BY vip_stock_history_date DESC";
				break;
					
			case '3':
				//daily revenue based on 70% of offgamers selling price for daily production
				$query = "	SELECT products_id, vip_production_history_date  AS history_date, vip_production_history_qty AS quantity, unit_selling_price AS price
							FROM " . TABLE_VIP_PRODUCTION_HISTORY . " 
							WHERE customers_id='" . $this->customer_id . "' 
								AND DATE_FORMAT(vip_production_history_date, '%Y-%m-%d') >= DATE_FORMAT('" . date("Y-m-d", $this->start_date) . "','%Y-%m-%d') 
								AND DATE_FORMAT(vip_production_history_date, '%Y-%m-%d') <= DATE_FORMAT('" . date("Y-m-d", $this->end_date) . "','%Y-%m-%d')
							ORDER BY vip_production_history_date DESC";	
				break;
				
			case '4':
				//daily revenue on order completed on offgamers
				$query = "	SELECT br.products_id, DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') AS history_date, br.buyback_amount AS price, brg.currency_value AS rate
							FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
							RIGHT JOIN " . TABLE_BUYBACK_REQUEST . " AS br
								ON (brg.buyback_request_group_id=br.buyback_request_group_id)
							WHERE brg.customers_id='" . $this->customer_id . "' 
								AND buyback_status_id = '3' 
								AND DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') >= DATE_FORMAT('" . date("Y-m-d", $this->start_date) . "','%Y-%m-%d') 
								AND DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') <= DATE_FORMAT('" . date("Y-m-d", $this->end_date) . "','%Y-%m-%d')
							ORDER BY brg.buyback_request_group_date DESC";
				break;
		}
		$report_result = tep_db_query($query);
		while($report_row = tep_db_fetch_array($report_result)){
			$cur_date = strtotime($report_row['history_date']);
			if($this->report_type == 3){
				$report_row['price'] = $report_row['price'] * 0.7;
			}
			if (!is_array ($this->report_info[$report_row['products_id']][$cur_date])){
				$this->report_info[$report_row['products_id']][$cur_date] = array(	'quantity' => 0,
																					'price' => 0
																				);
			}
			
			if($this->report_type < 4){
				$this->report_info[$report_row['products_id']][$cur_date]['quantity'] += $report_row['quantity'];	
				$this->report_info[$report_row['products_id']][$cur_date]['price'] += $report_row['price'];	
			} else {
				$this->report_info[$report_row['products_id']][$cur_date]['quantity']++;	
				$this->report_info[$report_row['products_id']][$cur_date]['price'] += $currencies->do_raw_conversion($report_row['price'], true, DISPLAY_CURRENCY, $report_row['rate']); //localise first since we will add all amount here
			}
			
		}
	}
	
	
}
?>