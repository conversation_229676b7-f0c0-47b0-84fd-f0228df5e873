<?
/*
  	$Id: column_manager.php,v 1.3 2008/01/05 04:27:41 weichen Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

$this_file_column_blocks_arr = array();

if (isset($column)) {
	foreach ($_app_blocks_arr as $block_filename => $block_settings) {
		//Can this user view this block ?
		
		if ( 	(!tep_not_null($block_settings['user_group']) || !in_array($_SESSION['customers_groups_id'], (explode(',', $block_settings['user_group'])))) && 
				(!tep_not_null($block_settings['vip_group']) || !in_array($_SESSION['vip_supplier_groups_id'], (explode(',', $block_settings['vip_group'])))) ) {
			continue;
		}
		
		//Get this block's settings for this column
		$this_column_setting_arr = $block_settings['column'][$column];

		//Is this block hidden from this column (all pages) ?
		if (!$this_column_setting_arr['status']) {
			continue;
		}

		//Is this block hidden from this page ?
		if (in_array(basename($_SERVER['PHP_SELF']), $this_column_setting_arr['hide_loc'])
			|| in_array(FILENAME_ALL, $this_column_setting_arr['hide_loc'])) {
			continue;
		}

		//Is this block hidden from this page ?
		if (in_array(basename($_SERVER['PHP_SELF']), $this_column_setting_arr['show_loc'])
			|| in_array(FILENAME_ALL, $this_column_setting_arr['show_loc'])) {
			$this_file_column_blocks_arr[$block_filename] = $this_column_setting_arr['order'];
		}
	}
}

asort($this_file_column_blocks_arr);

$column = '';
?>