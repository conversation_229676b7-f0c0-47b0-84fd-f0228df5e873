<?php
/*
  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com
	
  	Copyright (c) 2006 osCommerce
	
  	Released under the GNU General Public License
*/

/*******************************************************************
	Define the webserver and path parameters
	DIR_FS_* = Filesystem directories (local/physical)

	DIR_WS_* = Webserver directories (virtual/URL)
		Note: Should not has leading slash and must has ending slash
********************************************************************/
  define('HTTP_SERVER', 'http://carerra.localhost/'); // eg, http://localhost - should not be empty for productive servers
  define('HTTPS_SERVER', 'https://carerra.localhost/');
  define('HTTP_CATALOG_SERVER', 'http://carerra.localhost/');
  define('HTTPS_CATALOG_SERVER', 'https://carerra.localhost/');
  define('HTTP_ADMIN_SERVER', 'http://carerra.localhost/');
  define('HTTPS_ADMIN_SERVER', 'https://carerra.localhost/');
  define('HTTP_SUPPLIER_SERVER', 'http://carerra.localhost/');
  define('HTTPS_SUPPLIER_SERVER', 'https://carerra.localhost/');
  define('ENABLE_SSL', 'false'); // secure webserver for buyback_cn module
  define('ENABLE_SSL_ADMIN', 'false'); // secure webserver for admin module
  define('ENABLE_SSL_SUPPLIER', 'false'); // secure webserver for admin module
  define('ENABLE_SSL_CATALOG', 'false'); // secure webserver for catalog module
  define('ENABLE_SSL_UPLOAD', 'false'); // secure webserver for upload module
  define('HTTP_UPLOAD_SERVER', 'http://carerra.localhost/');
  define('HTTPS_UPLOAD_SERVER', 'https://carerra.localhost/');
  define('HTTP_COOKIE_DOMAIN', 'carerra.localhost');
  define('HTTPS_COOKIE_DOMAIN', 'carerra.localhost');
  define('HTTP_COOKIE_PATH', '/buyback/');
  define('HTTPS_COOKIE_PATH', '/buyback/');
  define('DIR_FS_DOCUMENT_ROOT', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/buyback/'); // where the pages are located on the server
  define('DIR_WS_ADMIN', 'admin/'); // absolute path required
  define('DIR_WS_SUPPLIER', 'supplier/'); // absolute path required
  define('DIR_WS_UPLOAD', 'upload/'); // absolute path required
  define('DIR_FS_SUPPLIER', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/supplier/'); // absolute path required
  define('DIR_WS_CATALOG', ''); // absolute path required
  define('DIR_FS_CATALOG', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/'); // absolute path required
  define('DIR_WS_BUYBACK', 'buyback/');
  define('DIR_FS_BUYBACK', 'C:/Apache2/vhosts/carerra.localhost/httpdocs/buyback/'); // absolute path required
  define('DIR_FS_THEME', DIR_FS_DOCUMENT_ROOT . 'theme/');
  define('DIR_WS_THEME', 'theme/');
  define('DIR_WS_IMAGES', 'images/');
  define('DIR_WS_ICONS', DIR_WS_IMAGES . 'icons/');
  define('DIR_WS_BUTTONS', DIR_WS_IMAGES . 'buttons/');
  define('DIR_WS_CATALOG_IMAGES', DIR_WS_CATALOG . 'images/');
  define('DIR_WS_INCLUDES', 'includes/');
  define('DIR_WS_BOXES', DIR_WS_INCLUDES . 'boxes/');
  define('DIR_WS_FUNCTIONS', DIR_WS_INCLUDES . 'functions/');
  define('DIR_WS_CLASSES', DIR_WS_INCLUDES . 'classes/');
  define('DIR_WS_MODULES', DIR_WS_INCLUDES . 'modules/');
  define('DIR_WS_LANGUAGES', DIR_WS_INCLUDES . 'languages/');
  define('DIR_WS_CATALOG_LANGUAGES', DIR_WS_CATALOG . 'includes/languages/');
  define('DIR_FS_CATALOG_LANGUAGES', DIR_FS_CATALOG . 'includes/languages/');
  define('DIR_FS_CATALOG_IMAGES', DIR_FS_CATALOG . 'images/');
  define('DIR_FS_DOWNLOAD', DIR_FS_CATALOG . 'download/');
  define('DIR_WS_TEMPLATES', 'templates/');
  define('DIR_WS_CONTENT', DIR_WS_TEMPLATES . 'content/');
  define('DIR_WS_JAVASCRIPT', DIR_WS_INCLUDES . 'javascript/');

  define('DISPLAY_CURRENCY', 'CNY');
  define('SITE_ID', '1');

// define our database connection
  define('DB_SERVER', 'localhost'); // eg, localhost - should not be empty for productive servers
  define('DB_SERVER_USERNAME', 'skc_user');
  define('DB_SERVER_PASSWORD', 'skc_password');
  define('DB_DATABASE', 'carerra');
  define('USE_PCONNECT', 'false'); // use persisstent connections?
  define('STORE_SESSIONS', 'mysql'); // leave empty '' for default handler or set to 'mysql'
?>