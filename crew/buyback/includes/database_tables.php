<?
/*
  	$Id: database_tables.php,v 1.14 2009/11/03 03:51:40 henry.chow Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/
// BEGIN latest news
define('TABLE_LATEST_NEWS', 'latest_news');
define('TABLE_LATEST_NEWS_DESCRIPTION', 'latest_news_description');
define('TABLE_LATEST_NEWS_GROUPS', 'latest_news_groups');

// define the database table names used in the project
define('TABLE_ADMIN', 'admin');
define('TABLE_ADDRESS_BOOK', 'address_book');
define('TABLE_ADDRESS_FORMAT', 'address_format');
define('TABLE_AUTOMATE_BUYBACK_PRICE', 'automate_buyback_price');
define('TABLE_AUTOMATE_ALERT_COLOR', 'automate_alert_color');
define('TABLE_BANNERS', 'banners');
define('TABLE_BANNERS_HISTORY', 'banners_history');
define('TABLE_CART_COMMENTS', 'cart_comments');
define('TABLE_CATEGORIES', 'categories');
define('TABLE_CATEGORIES_CONFIGURATION', 'categories_configuration');
define('TABLE_CATEGORIES_DESCRIPTION', 'categories_description');
define('TABLE_CATEGORIES_GROUPS', 'categories_groups');
define('TABLE_CATEGORIES_PRODUCT_TYPES', 'categories_product_types');

define('TABLE_CONFIGURATION', 'configuration');
define('TABLE_CONFIGURATION_GROUP', 'configuration_group');
define('TABLE_COUNTER', 'counter');
define('TABLE_COUNTER_HISTORY', 'counter_history');
define('TABLE_COUNTRIES', 'countries');
define('TABLE_CRON_PENDING_CREDIT', 'cron_pending_credit');
define('TABLE_CURRENCIES', 'currencies');
define('TABLE_CUSTOMERS', 'customers');
define('TABLE_CUSTOMERS_BASKET', 'customers_basket');
define('TABLE_CUSTOMERS_BASKET_ATTRIBUTES', 'customers_basket_attributes');
define('TABLE_CUSTOMERS_BASKET_BUNDLE', 'customers_basket_bundle');
define('TABLE_CUSTOMERS_GROUPS', 'customers_groups');
define('TABLE_CUSTOMERS_GROUPS_DISCOUNT', 'customers_groups_discount');
define('TABLE_CUSTOMERS_INFO', 'customers_info');
define('TABLE_CUSTOMERS_LAST_CPATH', 'customers_last_cpath');
define('TABLE_CUSTOMERS_LOGIN_IP_HISTORY', 'customers_login_ip_history');
define('TABLE_GEO_ZONES', 'geo_zones');
define('TABLE_INFOLINKS', 'infolinks');
define('TABLE_INFOLINKS_CONTENTS', 'infolinks_contents');
define('TABLE_INFOLINKS_GROUPS', 'infolinks_groups');
define('TABLE_LANGUAGES', 'languages');
define('TABLE_LOCKING', 'locking');
define('TABLE_LOG_TABLE', 'log_table');
define('TABLE_MANUFACTURERS', 'manufacturers');
define('TABLE_MANUFACTURERS_INFO', 'manufacturers_info');
define('TABLE_NEWSLETTERS_GROUPS', 'newsletters_groups');
define('TABLE_ORDERS', 'orders');
define('TABLE_ORDERS_COMMENTS', 'orders_comments');
define('TABLE_ORDERS_LOG_TABLE', 'orders_log_table');
define('TABLE_ORDERS_PRODUCTS', 'orders_products');
define('TABLE_ORDERS_PRODUCTS_ATTRIBUTES', 'orders_products_attributes');
define('TABLE_ORDERS_PRODUCTS_DOWNLOAD', 'orders_products_download');
define('TABLE_ORDERS_PRODUCTS_EXTRA_INFO', 'orders_products_extra_info');
define('TABLE_ORDERS_STATUS', 'orders_status');
define('TABLE_ORDERS_STATUS_HISTORY', 'orders_status_history');
define('TABLE_ORDERS_STATUS_STAT', 'orders_status_stat');
define('TABLE_ORDERS_TOTAL', 'orders_total');
define('TABLE_ORDERS_TAG', 'orders_tag');
define('TABLE_PAYMENT_EXTRA_INFO', 'payment_extra_info');
define('TABLE_PAYMENT_EGOLD', 'egold');
define('TABLE_PAYMENT_EGOLD_CURRENCIES', 'egold_currencies');
define('TABLE_PAYMENT_MONEYBOOKERS', 'moneybookers');
define('TABLE_PAYMENT_MONEYBOOKERS_COUNTRIES', 'moneybookers_countries');
define('TABLE_PAYMENT_MONEYBOOKERS_CURRENCIES', 'moneybookers_currencies');
define('TABLE_PAYMENT_MONEYBOOKERS_STATUS_HISTORY', 'moneybookers_payment_status_history');
define('TABLE_PAYMENT_FEES', 'payment_fees');
define('TABLE_PAYMENT_METHODS', 'payment_methods');
define('TABLE_PAYMENT_METHODS_FIELDS', 'payment_methods_fields');
define('TABLE_PAYPALIPN_TXN', 'paypalipn_txn'); // PAYPALIPN
define('TABLE_PRODUCTS', 'products');
define('TABLE_PRODUCTS_ATTRIBUTES', 'products_attributes');
define('TABLE_PRODUCTS_ATTRIBUTES_DOWNLOAD', 'products_attributes_download');
define('TABLE_PRODUCTS_BUNDLES', 'products_bundles');
define('TABLE_PRODUCTS_DESCRIPTION', 'products_description');
define('TABLE_PRODUCTS_GROUPS', 'products_groups'); //Separate Pricing per Customer Mod
define('TABLE_PRODUCTS_NOTIFICATIONS', 'products_notifications');
define('TABLE_PRODUCTS_OPTIONS', 'products_options');
define('TABLE_PRODUCTS_OPTIONS_VALUES', 'products_options_values');
define('TABLE_PRODUCTS_OPTIONS_VALUES_TO_PRODUCTS_OPTIONS', 'products_options_values_to_products_options');
define('TABLE_PRODUCTS_TO_CATEGORIES', 'products_to_categories');
define('TABLE_PRODUCTS_XSELL', 'products_xsell'); 	// Added for Xsell Products Mod
define('TABLE_REVIEWS', 'reviews');
define('TABLE_REVIEWS_DESCRIPTION', 'reviews_description');
define('TABLE_SALES_ACTIVITIES', 'sales_activities');
define('TABLE_SESSIONS', 'sessions');
define('TABLE_SHIPPING_CLASSES', 'shipping_classes');
define('TABLE_SHIPPING_CLASSES_VALUES', 'shipping_classes_values');
define('TABLE_SPECIALS', 'specials');
define('TABLE_TAX_CLASS', 'tax_class');
define('TABLE_TAX_RATES', 'tax_rates');
define('TABLE_TEMP_PROCESS', 'temp_process');
define('TABLE_THEMES', 'themes');
define('TABLE_THEMES_TO_CATEGORIES', 'themes_to_categories');
define('TABLE_WHOS_ONLINE', 'whos_online');
define('TABLE_ZONES', 'zones');
define('TABLE_ZONES_TO_GEO_ZONES', 'zones_to_geo_zones');

// data pool
define('TABLE_DATA_POOL', 'data_pool');
define('TABLE_DATA_POOL_LEVEL', 'data_pool_level');
define('TABLE_DATA_POOL_LEVEL_TAGS', 'data_pool_level_tags');
define('TABLE_DATA_POOL_OPTIONS', 'data_pool_options');
define('TABLE_DATA_POOL_OPTIONS_VALUES', 'data_pool_options_values');
define('TABLE_DATA_POOL_REF', 'data_pool_ref');
define('TABLE_DATA_POOL_TEMPLATE', 'data_pool_template');
define('TABLE_BRACKETS_TAGS', 'brackets_tags');
define('TABLE_BRACKETS', 'brackets');
define('TABLE_CUSTOM_PRODUCTS_TYPE', 'custom_products_type');
define('TABLE_POOL_TEMPLATE_T0_CATEGORIES', 'data_pool_template_to_categories');
define('TABLE_CUSTOMERS_BASKET_CUSTOM', 'customers_basket_custom');
define('TABLE_ORDERS_CUSTOM_PRODUCTS', 'orders_custom_products');
define('TABLE_CUSTOM_PRODUCTS_CODE', 'custom_products_code');

//buyback
define('TABLE_BUYBACK', 'buyback');
define('TABLE_BUYBACK_REQUEST', 'buyback_request');
define('TABLE_BUYBACK_REQUEST_GROUP', 'buyback_request_group');
define('TABLE_BUYBACK_BASKET','buyback_basket');
define('TABLE_BUYBACK_BRACKET','buyback_bracket');
define('TABLE_BUYBACK_BRACKET_TAGS','buyback_bracket_tags');
define('TABLE_BUYBACK_SETTING', 'buyback_setting');
define('TABLE_BUYBACK_GROUPS','buyback_groups');
define('TABLE_BUYBACK_GROUPS_TAGS','buyback_groups_tags');
define('TABLE_BUYBACK_GROUPS_TAGS_INFO','buyback_groups_tags_info');
define('TABLE_BUYBACK_GROUPS_TO_CATEGORIES','buyback_groups_to_categories');
define('TABLE_BUYBACK_PRODUCTS','buyback_products');
define('TABLE_BUYBACK_STATUS','buyback_status');
define('TABLE_BUYBACK_STATUS_HISTORY','buyback_status_history');

define('TABLE_USER_COMMENTS', 'user_comments');
define('TABLE_CUSTOMERS_REMARKS_HISTORY', 'customers_remarks_history');

//start VIP module
define('TABLE_BUYBACK_ORDER_INFO', 'buyback_order_info');
define('TABLE_CUSTOMERS_VIP', 'customers_vip');
define('TABLE_VIP_REGION_GROUP', 'vip_region_group');
define('TABLE_VIP_RANK', 'vip_rank');
define('TABLE_VIP_SUPPLIER_GROUPS', 'vip_supplier_groups');
define('TABLE_VIP_ORDER_ALLOCATION', 'vip_order_allocation');
define('TABLE_VIP_PRODUCTION_HISTORY', 'vip_production_history');
define('TABLE_VIP_STOCK_HISTORY', 'vip_stock_history');
define('TABLE_VIP_SUPPLIER_INVENTORY_LOGS', 'vip_supplier_inventory_logs');
define('TABLE_VIP_SUPPLIER_INVENTORY', 'vip_supplier_inventory');
define('TABLE_VIP_SUPPLIER_SETTING', 'vip_supplier_setting');
define('TABLE_VIP_RULES', 'vip_rules');
//end VIP module

//CN security Q&A
define('TABLE_CUSTOMERS_SECURITY', 'customers_security');
define('TABLE_CUSTOMERS_SECURITY_QUESTIONS', 'customers_security_questions');

// Paypal E-mail Address Verification
define('TABLE_CUSTOMERS_PAYMENT_EMAILS', 'customers_payment_emails');

define('TABLE_CUSTOMERS_INFO_VERIFICATION', 'customers_info_verification');

// Supplier
define('TABLE_RESTOCK_CHARACTER_INFO', 'restock_character_info');
define('TABLE_RESTOCK_CHARACTER_SETS', 'restock_character_sets');
define('TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES', 'restock_character_sets_to_categories');
define('TABLE_SUPPLIER', 'supplier');
define('TABLE_SUPPLIER_GROUPS', 'supplier_groups');
define('TABLE_SUPPLIER_HISTORY', 'supplier_history');
define('TABLE_SUPPLIER_HISTORY_GROUP', 'supplier_history_group');
define('TABLE_SUPPLIER_LANGUAGES', 'supplier_languages');
define('TABLE_SUPPLIER_LIST_TIME_SETTING', 'supplier_list_time_setting');
define('TABLE_SUPPLIER_LIST_STATUS', 'supplier_list_status');
define('TABLE_SUPPLIER_ORDER_LISTS', 'supplier_order_lists');
define('TABLE_SUPPLIER_ORDER_LISTS_HISTORY', 'supplier_order_lists_history');
define('TABLE_SUPPLIER_ORDER_LISTS_PRODUCTS', 'supplier_order_lists_products');
define('TABLE_SUPPLIER_PAYMENTS', 'supplier_payments');
define('TABLE_SUPPLIER_PAYMENTS_HISTORY', 'supplier_payments_history');
define('TABLE_SUPPLIER_PAYMENTS_ORDERS', 'supplier_payments_orders');
define('TABLE_SUPPLIER_PAYMENTS_STATUS', 'supplier_payments_status');
define('TABLE_SUPPLIER_PREFERENCES', 'supplier_preferences');
define('TABLE_SUPPLIER_PRICING', 'supplier_pricing');
define('TABLE_SUPPLIER_PRICING_SETTING', 'supplier_pricing_setting');
define('TABLE_SUPPLIER_PURCHASE_MODES', 'supplier_purchase_modes');
define('TABLE_SUPPLIER_TASKS_ALLOCATION_HISTORY', 'supplier_tasks_allocation_history');
define('TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET', 'buyback_competitor_status_bracket');
define('TABLE_BUYBACK_COMPETITOR_STATUS_BRACKET_SET', 'buyback_competitor_status_bracket_set');
define('TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET', 'buyback_supplier_price_bracket');
define('TABLE_BUYBACK_SUPPLIER_PRICE_BRACKET_SET', 'buyback_supplier_price_bracket_set');
define('TABLE_BUYBACK_CATEGORIES', 'buyback_categories');
define('TABLE_BUYBACK_SUPPLIER', 'buyback_supplier');


define('TABLE_USER_SETTING', 'user_setting');
define('TABLE_COMPETITORS', 'competitors');
define('TABLE_COMPETITORS_TO_CATEGORIES', 'competitors_to_categories');

define('TABLE_SUPPLIER_CP_PAYMENTS_PRODUCTS', 'supplier_cp_payments_products');
define('TABLE_SUPPLIER_CP_PAYMENTS', 'supplier_cp_payments');
define('TABLE_SUPPLIER_CP_PAYMENTS_HISTORY', 'supplier_cp_payments_history');
// Custom Product
define('TABLE_SUPPLIER_TASKS_ALLOCATION', 'supplier_tasks_allocation');
define('TABLE_SUPPLIER_TASKS_SETTING', 'supplier_tasks_setting');
define('TABLE_SUPPLIER_TASKS_STATUS', 'supplier_tasks_status');

define('TABLE_STORE_ACCOUNT_BALANCE', 'store_account_balance');
define('TABLE_STORE_ACCOUNT_COMMENTS', 'store_account_comments');
define('TABLE_STORE_ACCOUNT_HISTORY', 'store_account_history');
define('TABLE_STORE_PAYMENT_ACCOUNT_BOOK', 'store_payment_account_book');
define('TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS', 'store_payment_account_book_details');
define('TABLE_STORE_PAYMENTS_STATUS', 'store_payments_status');
define('TABLE_STORE_PAYMENTS', 'store_payments');
define('TABLE_STORE_PAYMENTS_HISTORY', 'store_payments_history');
define('TABLE_STORE_PAYMENTS_DETAILS', 'store_payments_details');

define('TABLE_USER_FAVOURITE_PRODUCTS', 'user_favourite_products');
?>