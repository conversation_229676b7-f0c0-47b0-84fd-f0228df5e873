<?php
if (defined("PAGE_PARSE_START_TIME")) {
	$end_time = microtime();
	$time_start = explode(' ', PAGE_PARSE_START_TIME);
	$time_end = explode(' ', $end_time);

	$totaltime = ($time_end[1] + $time_end[0]) - ($time_start[1] + $time_start[0]);
}
?>
<table width="100%" border="0" cellpadding="0" cellspacing="0">
	<tr height="30"><td>&nbsp;</td></tr>
	<tr height="30">
		<td>
			<div class="loggingFromMsg"><?=sprintf(FOOTER_TEXT_LOGIN_FROM, getenv("REMOTE_ADDR"))?></div>
		</td>
	</tr>
	<tr>
		<td class="pageLoadingMsg">
		<? //require(DIR_WS_INCLUDES . 'counter.php'); ?>
		<? printf ("Page was generated in %0.3f seconds", $totaltime); ?>
		<? if (getenv("REMOTE_ADDR") == EXCLUDE_ADMIN_IP_FOR_MAINTENANCE) printf ("(http: %0.3f seconds, MySQL: %0.3f seconds)", $totaltime-$mysql_query_time, $mysql_query_time); ?>
		<br><br>
		</td>
	</tr>
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0" style="background: url('<?=$footer_background_image_url?>'); background-repeat: repeat-x; background-position: left top;">
				<tr>
          			<td width="<?=$footer_left_image_dimension[0]?>" valign="top"><img alt='' src="<?=$footer_left_image_url?>"></td>
          			<td valign="top">
          				<table height="<?=$table_cell_height?>" border="0" align="center" cellpadding="0" cellspacing="0">
              				<tr>
	                			<td <?=(file_exists($footer_center_background_image_url) ? 'style="background:url(\''.$footer_center_background_image_url.'\'); background-repeat:no-repeat; background-position:center top;"' : '') ?> >
	                				<div class="copyrightMsg">
	                					<p><?=COPYRIGHT_INFORMATION?>
										Powered by SKC Store v<?=STORE_VERSION?></p>
									</div>
								</td>
              				</tr>
              			</table>
              		</td>
          			<td width="<?=$footer_right_image_dimension[0]?>" valign="top"><img alt='' src="<?=$footer_right_image_url?>"></td>
        		</tr>
			</table>
		</td>
	</tr>
</table>