<?
/*
  	$Id: configuration.php,v 1.1 2007/01/25 14:33:37 nickyap Exp $

  	Developer: <PERSON> (c) 2006 SKC Venture

  	Released under the GNU General Public License
*/

function tep_get_cfg_setting($id, $id_type='catalog', $cfg_key='', $key_type='configuration_key') {
	$cat_cfg_array = array();
	$cid = $id;

	if ($id_type == 'product') 	$cid = tep_get_actual_product_cat_id($id);

	$cat_path = tep_get_particular_cat_path($cid);

	if (tep_not_null($cfg_key)) {
		$cat_path_array = explode('_', $cat_path);
		$cat_path_array = array_merge(array(0), $cat_path_array);

		if ($key_type == 'configuration_key') {
			for ($i=count($cat_path_array)-1; $i >= 0; $i--) {
				$cfg_value_select_sql = "	SELECT categories_configuration_value AS cfgValue
											FROM " . TABLE_CATEGORIES_CONFIGURATION . "
											WHERE categories_id ='" . $cat_path_array[$i] . "'
												AND categories_configuration_key = '" . tep_db_input($cfg_key) . "'; ";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				if ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_key] = $cfg_value_row['cfgValue'];
					break;
				}
			}
		} else if ($key_type == 'group_id') {
			for ($i=0; $i < count($cat_path_array); $i++) {
				$cfg_value_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue
											FROM " . TABLE_CATEGORIES_CONFIGURATION . "
											WHERE categories_id ='" . $cat_path_array[$i] . "'
												AND categories_configuration_group_id = '" . tep_db_input($cfg_key) . "'";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				while ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_value_row['cfgKey']] = $cfg_value_row['cfgValue'];
				}
			}
		}
	} else {
		$cat_cfg_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue
								FROM " . TABLE_CATEGORIES_CONFIGURATION . "
								WHERE categories_id=0; ";
		$cat_cfg_result_sql = tep_db_query($cat_cfg_select_sql);
		while ($cat_cfg_row = tep_db_fetch_array($cat_cfg_result_sql)) {
			$cat_cfg_array[$cat_cfg_row['cfgKey']] = $cat_cfg_row['cfgValue'];
		}

		if (tep_not_null($cat_path)) {
			$cat_path_array = explode('_', $cat_path);
			for ($i=0; $i < count($cat_path_array); $i++) {
				$cfg_value_select_sql = "	SELECT categories_configuration_key AS cfgKey, categories_configuration_value AS cfgValue
											FROM " . TABLE_CATEGORIES_CONFIGURATION . "
											WHERE categories_id ='" . $cat_path_array[$i] . "' ";
				$cfg_value_result_sql = tep_db_query($cfg_value_select_sql);
				while ($cfg_value_row = tep_db_fetch_array($cfg_value_result_sql)) {
					$cat_cfg_array[$cfg_value_row['cfgKey']] = $cfg_value_row['cfgValue'];
				}
			}
		}
	}

	return $cat_cfg_array;
}

?>