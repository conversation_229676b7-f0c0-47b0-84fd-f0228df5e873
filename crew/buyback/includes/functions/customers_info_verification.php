<?
/*
  	$Id: customers_info_verification.php,v 1.5 2011/05/12 08:15:20 weichen Exp $
	
  	Developer: <PERSON>wang <PERSON>
  	Copyright (c) 2006 SKC Venture
	
  	Released under the GNU General Public License
*/

function tep_format_telephone($customer_id) {
	$customer_info_select_sql = "select customers_telephone, customers_country_dialing_code_id, customers_default_address_id from " . TABLE_CUSTOMERS . " where customers_id ='" . (int)$customer_id . "'";
	$customer_info_result_sql = tep_db_query($customer_info_select_sql);
	$customer_info_row = tep_db_fetch_array($customer_info_result_sql);

	$customer_telephone_not_standard_format = $customer_info_row['customers_telephone'];
	$customer_telephone_not_standard_format = preg_replace('/[^\d]/', '', $customer_telephone_not_standard_format);
	$customer_country_dialing_code_id = $customer_info_row['customers_country_dialing_code_id'];
	$customer_default_address_id = $customer_info_row['customers_default_address_id'];
	
	if (!tep_not_null($customer_country_dialing_code_id)) {
		$customer_country_dialing_code_id_select_sql = "select entry_country_id from " . TABLE_ADDRESS_BOOK . " where address_book_id = '" . $customer_default_address_id . "' and customers_id ='" . (int)$customer_id . "'";
		$customer_country_dialing_code_id_query = tep_db_query($customer_country_dialing_code_id_select_sql);
		$customer_country_dialing_code_id_row = tep_db_fetch_array($customer_country_dialing_code_id_query);
		
		$international_dialing_code_id_update_sql = "update " . TABLE_CUSTOMERS . " set customers_country_dialing_code_id = '" . $customer_country_dialing_code_id_row['entry_country_id'] . "' where customers_id ='" . (int)$customer_id . "'";
		tep_db_query($international_dialing_code_id_update_sql);
		
		$customer_country_dialing_code_id = $customer_country_dialing_code_id_row['entry_country_id'];
	}
	
	$country_international_dialing_code_sql_select = "select countries_international_dialing_code, countries_name from " . TABLE_COUNTRIES . " where countries_id='" . $customer_country_dialing_code_id . "'";
	$country_international_dialing_code_query = tep_db_query($country_international_dialing_code_sql_select);
	$country_international_dialing_code_row = tep_db_fetch_array($country_international_dialing_code_query);
	
	$country_international_dialing_code = $country_international_dialing_code_row['countries_international_dialing_code'];
	$country_name = $country_international_dialing_code_row['countries_name'];
	
	$customer_telephone = tep_parse_telephone($customer_telephone_not_standard_format, $country_international_dialing_code, 'code');
	
	$customer_telephone = array('country_id' => $customer_country_dialing_code_id, 'country_name' => $country_name, 'country_international_dialing_code' => $country_international_dialing_code, 'telephone_number' => $customer_telephone);
	return $customer_telephone;
}

function tep_verification_allowed_check($customer_id) {
	$customer_complete_phone_info = tep_format_telephone($customer_id);
	$complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
	$complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);
	
	$customer_turns_select_sql = "select verify_try_turns from " . TABLE_CUSTOMERS_INFO_VERIFICATION . " where customers_id ='" . (int)$customer_id . "' and customers_info_value ='" . $complete_telephone_number . "' and info_verification_type ='" . 'telephone' . "'";
	$customer_turns_result_sql = tep_db_query($customer_turns_select_sql);
	$customer_turns_row = tep_db_fetch_array($customer_turns_result_sql);
	
	return $customer_turns_row['verify_try_turns'];
}

function tep_parse_telephone($telephone, $country_needle, $type = 'id') {
	$country_code = '';
	
	$telephone = preg_replace('/[^\d]/', '', $telephone);
	
	if ($type == 'id') {
		$telephone_country_code_select_sql = "select countries_id, countries_international_dialing_code from " . TABLE_COUNTRIES . " where countries_id ='" . tep_db_input($country_needle) . "'";
	    $telephone_country_code_result_sql = tep_db_query($telephone_country_code_select_sql);
	    $telephone_country_code_row = tep_db_fetch_array($telephone_country_code_result_sql);
	    
	    $country_code = $telephone_country_code_row['countries_international_dialing_code'];
	    $country_id = $telephone_country_code_row['countries_id'];
	} else if ($type == 'code') {
		$country_code = $country_needle;
		
		$country_id_select_sql = "select countries_id from " . TABLE_COUNTRIES . " where countries_international_dialing_code ='" . tep_db_input($country_code) . "'";
	    $country_id_result_sql = tep_db_query($country_id_select_sql);
	    $country_id_row = tep_db_fetch_array($country_id_result_sql);
	    
	    $country_id = $country_id_row['countries_id'];
	}
	
	switch($country_id) {
		case 105:	// Italy (Fixed line has one elading zero but Mobile does not have)
			$extra_reg_rule = '(?:0)';
			break;
		default:
			$extra_reg_rule = '';
			break;
	}
	
	$telephone =  preg_replace('/^(0+)('.$extra_reg_rule.'\d+)/', '$2', $telephone);
	
	if (tep_not_null($country_code)) {
		while (strlen($telephone) > 10) {
			if (preg_match('/^('.$extra_reg_rule.$country_code.')(\d+)/', $telephone)) {
				$telephone =  preg_replace('/^('.$extra_reg_rule.$country_code.')(\d+)/', '$2', $telephone);
				$telephone =  preg_replace('/^(0+)('.$extra_reg_rule.'\d+)/', '$2', $telephone);
			} else {
				break;
			}
		}
	}
	
	switch($country_id) {
		case 105:	// Italy (Fixed line has one elading zero but Mobile does not have)
			if (substr($telephone, 0, 2) == '03') {	// Mobile number
				$telephone = substr($telephone, 1);
			}
			
			break;
		default:
			;
			break;
	}
	
	return $telephone;
}

function tep_four_digit_code_generate() {
	$code = rand(1000, 9999);

	return $code;
}

function tep_confirming_code($customer_id, $code_received) {
	$customer_complete_phone_info = tep_format_telephone($customer_id);
	$complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
	$complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);
	
	$customer_info_verify_code_select_sql = "select serial_number from " . TABLE_CUSTOMERS_INFO_VERIFICATION . " where customers_id ='" . (int)$customer_id . "' and customers_info_value ='" . $complete_telephone_number . "' and info_verification_type ='" . 'telephone' . "'";
	$customer_info_verify_code_result_sql = tep_db_query($customer_info_verify_code_select_sql);
	$customer_info_verify_code_row = tep_db_fetch_array($customer_info_verify_code_result_sql);
	
	$code_generated = $customer_info_verify_code_row['serial_number'];
	
	if ($code_generated == $code_received) {
		return true;
	} else {
		return false;	
	}
}

function tep_country_maxmind_support($country_dialing_code) {
	$country_maxmind_support_check_select_sql = "	SELECT " . TELEPHONE_VERIFICATION_SERVICES . "_support
													FROM " . TABLE_COUNTRIES . " 
													WHERE countries_international_dialing_code = '" . (int)$country_dialing_code . "'";
	$country_maxmind_support_check_result_sql = tep_db_query($country_maxmind_support_check_select_sql);
	$country_maxmind_support_check_row = tep_db_fetch_array($country_maxmind_support_check_result_sql);
	
	return $country_maxmind_support_check_row[TELEPHONE_VERIFICATION_SERVICES.'_support'];
}

function tep_set_info_verified ($customer_id) {
	$customer_complete_phone_info = tep_format_telephone($customer_id);
	$complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
	$complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);
	
	$update_set_info_verified = "	UPDATE " . TABLE_CUSTOMERS_INFO_VERIFICATION . " 
									SET info_verified = '1', 
										serial_number = '',
										customers_info_verification_date = now()
									WHERE customers_info_value ='" . $complete_telephone_number . "' 
										AND info_verification_type='telephone'
										AND customers_id ='" . $customer_id . "'";
	tep_db_query($update_set_info_verified);
	
}

function tep_set_try_turns ($customer_id, $turns) {
	$customer_complete_phone_info = tep_format_telephone($customer_id);
	$complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
	$complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);
	
	$update_verify_try_turns = "update " . TABLE_CUSTOMERS_INFO_VERIFICATION . " set verify_try_turns = '" . $turns . "' where customers_id ='" . $customer_id . "' and customers_info_value ='" . $complete_telephone_number . "' and info_verification_type='telephone'";
	tep_db_query($update_verify_try_turns);
}

function tep_reset_try_turns ($customer_id, $verify_info, $type) {
	$update_reset_try_turns = "update " . TABLE_CUSTOMERS_INFO_VERIFICATION . " set verify_try_turns = '" . 0 . "' where customers_id ='" . $customer_id . "' and customers_info_value ='" . $verify_info . "' and info_verification_type='" . $type . "'";
	tep_db_query($update_reset_try_turns);
}

function tep_try_turns ($customer_id) {
	$customer_complete_phone_info = tep_format_telephone($customer_id);
	$complete_telephone_number = $customer_complete_phone_info['country_international_dialing_code'] . $customer_complete_phone_info['telephone_number'];
	$complete_telephone_number = preg_replace('/[^\d]/', '', $complete_telephone_number);
	
	$customer_verify_try_turns_select_sql = "select verify_try_turns from " . TABLE_CUSTOMERS_INFO_VERIFICATION . " where customers_id ='" . (int)$customer_id . "' and customers_info_value ='" . $complete_telephone_number . "' and info_verification_type ='" . 'telephone' . "'";
	$customer_verify_try_turns_result_sql = tep_db_query($customer_verify_try_turns_select_sql);
	$customer_verify_try_turns_row = tep_db_fetch_array($customer_verify_try_turns_result_sql);
	
	return $customer_verify_try_turns_row['verify_try_turns'];
}

function tep_info_been_verify ($customer_id, $verify_info, $type) {
	$customer_verify_info_select_sql = "select customers_info_value from " . TABLE_CUSTOMERS_INFO_VERIFICATION . " where customers_id ='" . (int)$customer_id . "' and customers_info_value ='" . $verify_info . "' and info_verification_type ='" . $type . "'";
	$customer_verify_info_result_sql = tep_db_query($customer_verify_info_select_sql);
	$customer_verify_info_row = tep_db_fetch_array($customer_verify_info_result_sql);
	
	if (sizeof($customer_verify_info_row['customers_info_value']) > 0) {
		return true;
	} else {
		return false;	
	}
}

function tep_info_verified_check ($customer_id, $verify_info, $type) {
	$customer_info_verified_select_sql = "select info_verified from " . TABLE_CUSTOMERS_INFO_VERIFICATION . " where customers_id ='" . (int)$customer_id . "' and customers_info_value ='" . $verify_info . "' and info_verification_type ='" . $type . "'";
	$customer_info_verified_result_sql = tep_db_query($customer_info_verified_select_sql);
	$customer_info_verified_row = tep_db_fetch_array($customer_info_verified_result_sql);
	
	return $customer_info_verified_row['info_verified'];
}

function tep_send_info_verification_email ($email_address, $firstname, $lastname, $gender = '', $serial = '') {
	if(!tep_not_null($serial)) {
		// Generate Serial Number
		$new_serial = tep_gen_random_serial($email_address, TABLE_CUSTOMERS, 'serial_number', '', 12);
		tep_db_query("update " . TABLE_CUSTOMERS . " set serial_number = '" . $new_serial . "' where customers_email_address = '" . tep_db_input($email_address) . "'");
	} else {
		$new_serial = $serial;
	}
	
	$name = $firstname . ' ' . $lastname;
		
	$email_text .= tep_get_email_greeting($firstname, $lastname, $gender);
	
	$activate_address = tep_href_link(FILENAME_ACCOUNT_ACTIVATE, 'serial=' . $new_serial . '&email=' . $email_address . '&action=verify_email&aov=verify');
	$link = '<a href="' . $activate_address . '" target="_blank">' . $activate_address . '</a><br>';
	
	$email_text .= EMAIL_VERIFY_CONTENT . $link . EMAIL_MANUAL_ACTIVATE_EMAIL_2 . $email_address . EMAIL_MANUAL_ACTIVATE_CODE_2 . $new_serial . "\n\n" . EMAIL_CONTACT . EMAIL_FOOTER;
	tep_mail($name, $email_address, implode(' ', array(EMAIL_SUBJECT_PREFIX, EMAIL_SUBJECT_2)), $email_text, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
}

function tep_error_report ($customer_id, $orders_id) {
	$customer_info_select_sql = "select customers_firstname, customers_lastname, customers_gender from " . TABLE_CUSTOMERS . " where customers_id ='" . (int)$customer_id . "'";
	$customer_info_result_sql = tep_db_query($customer_info_select_sql);
	$customer_info_row = tep_db_fetch_array($customer_info_result_sql);
	
	$order_info_select_sql = "select date_purchased, orders_status from " . TABLE_ORDERS . " where orders_id ='" . (int)$orders_id . "'";
	$order_info_result_sql = tep_db_query($order_info_select_sql);
	$order_info_row = tep_db_fetch_array($order_info_result_sql);
	
	$customer_complete_phone_info_array = tep_format_telephone($customer_id);
	$complete_telephone_number = $customer_complete_phone_info_array['country_international_dialing_code'] . ' '  . $customer_complete_phone_info_array['telephone_number'];
	$country_name = $customer_complete_phone_info_array['country_name'];
	
	$firstname = $customer_info_row['customers_firstname'];
	$lastname = $customer_info_row['customers_lastname'];
	$gender = $customer_info_row['customers_gender'];
	$name = $firstname . ' ' . $lastname;
	
	$report_time = date('Y-m-d H:i:s');
	
	$email_text .= EMAIL_CONTENT_1 . ' ' . $report_time;
	$email_text .= '<br>' . EMAIL_CONTENT_2 . ' ' . $name;
	$email_text .= '<br>' . EMAIL_CONTENT_3 . ' ' . $customer_id;
	$email_text .= '<br>' . EMAIL_CONTENT_4 . ' ' . $orders_id;
	$email_text .= '<br>' . EMAIL_CONTENT_5 . ' ' .	$order_info_row['date_purchased'];
	//$email_text .= '<br>' . EMAIL_CONTENT_6 . ' ' . 
	$email_text .= '<br>' . EMAIL_CONTENT_7 . ' ' . $country_name;
	$email_text .= '<br>' . EMAIL_CONTENT_8 . ' ' . '+' . $complete_telephone_number;
	
	tep_mail(EMAIL_ADMIN_NAME, STORE_OWNER_EMAIL_ADDRESS, EMAIL_TITLE, $email_text, $name, $customer_email);
	$messageStack = new messageStack();
	$messageStack->add_session('index', EMAIL_REPORT_SEND_MESSAGE, 'success');
	tep_redirect(tep_href_link(FILENAME_DEFAULT));
}
?>