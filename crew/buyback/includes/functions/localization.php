<?php
/*
  $Id: localization.php,v 1.1 2007/01/25 14:33:37 nickyap Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  function quote_oanda_currency($code, $base = DEFAULT_CURRENCY) {
    $page = file('http://www.oanda.com/convert/fxdaily?value=1&redirected=1&exch=' . $code .  '&format=CSV&dest=Get+Table&sel_list=' . $base);

    $match = array();

    preg_match('/(.+),(\w{3}),([0-9.]+),([0-9.]+)/i', implode('', $page), $match);

    if (sizeof($match) > 0) {
      return $match[3];
    } else {
      return false;
    }
  }

  function quote_xe_currency($to, $from = DEFAULT_CURRENCY) {
    $page = file('http://www.xe.net/ucc/convert.cgi?Amount=1&From=' . $from . '&To=' . $to);

    $match = array();

    preg_match('/[0-9.]+\s*' . $from . '\s*=\s*([0-9.]+)\s*' . $to . '/', implode('', $page), $match);

    if (sizeof($match) > 0) {
      return $match[1];
    } else {
      return false;
    }
  }

function tep_user_balance($user_id, $user_role) {
	$user_balance_array = array();
	
	$store_account_balance_select_sql = "	SELECT store_account_balance_currency, store_account_balance_amount 
											FROM " . TABLE_STORE_ACCOUNT_BALANCE . " 
											WHERE user_id = '" . tep_db_input($user_id) . "' 
												AND user_role = '" . tep_db_input($user_role) . "' 
											ORDER BY store_account_balance_currency";
	$store_account_balance_result_sql = tep_db_query($store_account_balance_select_sql);
	
    while ($store_account_balance_row = tep_db_fetch_array($store_account_balance_result_sql)) {
    	$user_balance_array[$store_account_balance_row['store_account_balance_currency']] = $store_account_balance_row['store_account_balance_amount'];
    }
    
    return $user_balance_array;
}
?>
