<?php
/*
  $Id: sessions.php,v 1.3 2007/02/09 08:39:30 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  if (STORE_SESSIONS == 'mysql') {

	if (!$SESS_LIFE = get_cfg_var('session.gc_maxlifetime')) {
		$SESS_LIFE = 1440;
	}

	function _sess_open($save_path, $session_name) {
      return true;
    }

    function _sess_close() {
      return true;
    }

    function _sess_read($key) {
      $value_query = tep_db_query("select value from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($key) . "' and expiry > '" . time() . "'");
      $value = tep_db_fetch_array($value_query);

      if (isset($value['value'])) {
        return $value['value'];
      }

      return false;
    }

    function _sess_write($key, $val) {
      global $SESS_LIFE;

      $expiry = time() + $SESS_LIFE;
      $value = $val;

      $check_query = tep_db_query("select count(*) as total from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($key) . "'");
      $check = tep_db_fetch_array($check_query);

      if ($check['total'] > 0) {
        return tep_db_query("update " . TABLE_SESSIONS . " set expiry = '" . tep_db_input($expiry) . "', value = '" . tep_db_input($value) . "' where sesskey = '" . tep_db_input($key) . "'");
      } else {
        return tep_db_query("insert into " . TABLE_SESSIONS . " values ('" . tep_db_input($key) . "', '" . tep_db_input($expiry) . "', '" . tep_db_input($value) . "')");
      }
    }

    function _sess_destroy($key) {
      return tep_db_query("delete from " . TABLE_SESSIONS . " where sesskey = '" . tep_db_input($key) . "'");
    }

    function _sess_gc($maxlifetime) {
      tep_db_query("delete from " . TABLE_SESSIONS . " where expiry < '" . time() . "'");

      return true;
    }

    session_set_save_handler('_sess_open', '_sess_close', '_sess_read', '_sess_write', '_sess_destroy', '_sess_gc');
  }

  function tep_session_start() {
    return session_start();
  }

  function tep_session_register($variable) {
    global $session_started;

    if ($session_started == true) {
      return session_register($variable);
    } else {
      return false;
    }
  }

  function tep_session_is_registered($variable) {
    return session_is_registered($variable);
  }

  function tep_session_unregister($variable) {
    return session_unregister($variable);
  }

  function tep_session_id($sessid = '') {
    if (!empty($sessid)) {
      return session_id($sessid);
    } else {
      return session_id();
    }
  }

  function tep_session_name($name = '') {
    if (!empty($name)) {
      return session_name($name);
    } else {
      return session_name();
    }
  }

  function tep_session_close() {
    if (PHP_VERSION >= '4.0.4') {
      return session_write_close();
    } elseif (function_exists('session_close')) {
      return session_close();
    }
  }

  function tep_session_destroy() {
    return session_destroy();
  }

  function tep_session_save_path($path = '') {
    if (!empty($path)) {
      return session_save_path($path);
    } else {
      return session_save_path();
    }
  }

function tep_session_recreate() {
	if (PHP_VERSION >= 4.1) {
		//-MS- Add session regeneration
      	if (SESSION_FORCE_COOKIE_USE != 'True') {
			if ( tep_session_regenerate() )	return;
      	}
		//-MS- Add session regeneration EOM
		
  		$session_backup = $_SESSION;
		
  		unset($_COOKIE[tep_session_name()]);
		
  		tep_session_destroy();
		
  		if (STORE_SESSIONS == 'mysql') {
    		session_set_save_handler('_sess_open', '_sess_close', '_sess_read', '_sess_write', '_sess_destroy', '_sess_gc');
  		}
		
  		tep_session_start();
		
  		$_SESSION = $session_backup;
  		unset($session_backup);
	}
}

//-MS- Add session regeneration
function tep_session_regenerate() {
	global $SID;
    
    if (PHP_VERSION > 4.3) {
      	if (STORE_SESSIONS == 'mysql') {
        	session_set_save_handler('_sess_open', '_sess_close', '_sess_read', '_sess_write', '_sess_destroy', '_sess_gc');
      	}
      	session_regenerate_id();
      	// set SID once, even if empty
      	$SID = (defined('SID') ? SID : '');
		
		// >>> BEGIN REGISTER_GLOBALS
      	// Work-around to allow disabling of register_globals - map all defined
      	// session variables
      	/*
      	if (count($_SESSION)) {
        	$session_keys = array_keys($_SESSION);
        	foreach($session_keys as $variable) {
          		link_session_variable($variable, true);
        	}
      	}
      	*/
		// <<< END REGISTER_GLOBALS
		
      	return true;
	}
    
    return false;
}
//-MS- Add session regeneration EOM
?>