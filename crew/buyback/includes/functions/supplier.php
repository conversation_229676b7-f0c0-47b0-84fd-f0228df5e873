<?
/*
  	$Id: supplier.php,v 1.37 2009/07/21 10:23:07 keepeng.foong Exp $

  	Developer: <PERSON>
  	Copyright (c) 2006 SKC Ventrue

  	Released under the GNU General Public License
*/

if (!defined('ORDERS_LOG_UNLOCK_OWN_ORDER'))	define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');

function tep_get_suggested_max_purchase_qty($product_id, $setting_array) {
    $total_days_inventory = $setting_array['ppls_inventory_days'];
    if (!$total_days_inventory>0) {
        return 0;
    }
	
    $suggested_max_purchase_qty = 0;
    $product_total_sales_qty = 0;
    $suggested_max_purchase_qty = 0;
	
    $start_date = $setting_array['ppls_sales_start_date'];
    $end_date = $setting_array['ppls_sales_end_date'];
  	
	$days_in_range = tep_day_diff($start_date, $end_date);
	
	if ($days_in_range <= 0) {
		return 0;
	}
	
	$product_total_sales_qty_select_sql = "	SELECT SUM(op.products_quantity) AS total_prod_qty
									        FROM " . TABLE_ORDERS . " AS o
									        INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op ON (o.orders_id=op.orders_id AND o.orders_status = '3')
									        WHERE op.products_id = '".$product_id."'
									        AND o.date_purchased >= '".$start_date."'
									        AND o.date_purchased <= '".$end_date."' ";
    $product_total_sales_qty_result_sql = tep_db_query($product_total_sales_qty_select_sql);
    
    while ($product_total_sales_qty_row = tep_db_fetch_array($product_total_sales_qty_result_sql)) {
        $product_total_sales_qty += $product_total_sales_qty_row['total_prod_qty'];
    }
    
   	$suggested_max_purchase_qty = ceil(($product_total_sales_qty/$days_in_range) * $total_days_inventory);
	
   	return $suggested_max_purchase_qty;
}

function tep_cancel_expirable_buyback_request() {
	global $language, $languages_id;
	/*****************************************************************************************
		Buyback Order Expiration Cancellation Task
		Note: If adjusting intervals, adjust auto-cancellation notice in Buyback List as well.
	*******************************************************************************************/

	$buyback_data_array = array();

	
	$expired_pending_buyback_order_select_sql = "	SELECT br.buyback_request_group_id, brg.customers_id, brg.buyback_request_order_type,
													brg.buyback_request_group_date, brg.buyback_request_group_site_id, l.locking_by, 
													SUM(br.buyback_quantity_received) AS total_recv_qty
													FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br
														ON (brg.buyback_request_group_id=br.buyback_request_group_id) 
													LEFT JOIN " . TABLE_LOCKING . " AS l 
														ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
													WHERE brg.buyback_status_id=1
														AND ( (br.buyback_quantity_confirmed <= 0 AND br.buyback_dealing_type = 'ofp_deal_on_mail' AND 
																	( brg.buyback_request_group_expiry_date > '0000-00-00 00:00:00'
																  		AND brg.buyback_request_group_expiry_date <= DATE_SUB(NOW(), INTERVAL 10 MINUTE) )
															   )
															)
														AND brg.buyback_request_group_user_type IN (0, 1)
													GROUP BY br.buyback_request_group_id
													HAVING total_recv_qty <= 0";
	$expired_pending_buyback_order_result_sql = tep_db_query($expired_pending_buyback_order_select_sql);
	
	while ($expired_pending_buyback_order_row = tep_db_fetch_array($expired_pending_buyback_order_result_sql)) {
		$buyback_data_array[$expired_pending_buyback_order_row['buyback_request_group_id']] = $expired_pending_buyback_order_row;
	}

	if (count($buyback_data_array)) {
		$cancel_buyback_order_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . "
											SET buyback_status_id = 4, show_restock = 0
											WHERE buyback_request_group_id IN ('" . implode("', '", array_keys($buyback_data_array)) . "')";
		tep_db_query($cancel_buyback_order_update_sql);

		foreach ($buyback_data_array as $buyback_group_id => $buyback_info) {
			if (tep_not_null($buyback_info['locking_by'])) {
				tep_order_lock($buyback_group_id, ORDERS_LOG_UNLOCK_OWN_ORDER, 'System');
			}
			
		    $buyback_history_data_array = array('buyback_request_group_id' => $buyback_group_id,
						                        'buyback_status_id' => '4',
												'date_added' => 'now()',
												'customer_notified' => '0',
												'comments' => 'Auto Cancel',
												'changed_by' => 'System'
												);
			tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
		    
		    //check is vip order or not
		    if ($buyback_info['buyback_request_order_type'] == 1) {
		    	$vipGroupsObj = new vip_groups($buyback_info['customers_id']);
				$vipGroupsObj->calculate_cummulative_point('VIP_DEDUCT_ORDER_EXPIRATION_CANCELLATION_AFTER_ACCEPTED');
			}
		    
			$buyback_order_date = tep_date_long($buyback_info['buyback_request_group_date']);
			
			//Get Preferred language. Defaults to logged in user's language (customer in buyback site/admin in admin module)	
			$user_language_id = $languages_id; 
			$user_language = $language;	
			$pref_array = tep_get_supplier_pref_setting($buyback_info['customers_id']);
			
			if (count($pref_array)) {
	    		$lang_code_select_sql = "SELECT directory FROM " . TABLE_SUPPLIER_LANGUAGES . " WHERE languages_id = '". $pref_array[KEY_SP_LANG] . "'";
	    		$lang_code_result_sql = tep_db_query($lang_code_select_sql);
				if ($lang_code_row = tep_db_fetch_array($lang_code_result_sql)) {
					$user_language = $lang_code_row['directory'];
	    			$user_language_id = $pref_array[KEY_SP_LANG];
				}
			}
			
			if (!file_exists(DIR_WS_LANGUAGES . "buyback_{$user_language}.php")) {
				$user_language_id = $languages_id; 
				$user_language = $language;	
			}
			
			include_once(DIR_WS_LANGUAGES . "buyback_{$user_language}.php");
			
			$status_str = tep_get_buyback_order_status_name(4, $user_language_id); //Cancel
			//--
			
			$subject_str = EMAIL_SUBJECT_BUYBACK_ORDER_UPDATED;
			$order_summary_title = EMAIL_TEXT_BUYBACK_ORDER_SUMMARY;
			
			$customer_info_select_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_gender
											FROM " . TABLE_CUSTOMERS . "
											WHERE customers_id = '" . tep_db_input($buyback_info['customers_id']) . "'";
			$customer_info_result_sql = tep_db_query($customer_info_select_sql);
			if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
				//User exists. Prepare notification email.
				$to_name = tep_mb_convert_encoding($customer_info_row['customers_firstname'].' '.$customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET);
				$to_email = $customer_info_row['customers_email_address'];
				
				$greeting_str = tep_mb_convert_encoding(tep_get_email_greeting(tep_mb_convert_encoding($customer_info_row['customers_firstname'], EMAIL_CHARSET, CHARSET), tep_mb_convert_encoding($customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_info_row['customers_gender']), CHARSET, EMAIL_CHARSET);
				
				$email_body = sprintf(EMAIL_BODY_ORDER_UPDATED, $order_summary_title, $buyback_group_id, $buyback_order_date, $status_str, EMAIL_BODY_ORDER_CANCELLED_EXPIRED);
				
				$email_subject = EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX . tep_mb_convert_encoding(sprintf($subject_str, $buyback_group_id), EMAIL_CHARSET, CHARSET);
				
				tep_mail($to_name, $to_email, $email_subject, $greeting_str . tep_mb_convert_encoding($email_body, EMAIL_CHARSET, CHARSET), tep_mb_convert_encoding(STORE_OWNER, EMAIL_CHARSET, CHARSET), STORE_OWNER_EMAIL_ADDRESS);
			}
		}
	}
	// End of Buyback Order Expiration Cancellation Task
	
	//delete those order allocation which expired
	$orders_products_select_sql = "SELECT DISTINCT(orders_products_id) FROM " . TABLE_VIP_ORDER_ALLOCATION;
	$orders_products_select_result = tep_db_query($orders_products_select_sql);
	while($orders_products_select_row = tep_db_fetch_array($orders_products_select_result)){
		$vipOrderObj = new vip_order($orders_products_select_row['orders_products_id']);
		$vipOrderObj->get_orders_details();
		$vipOrderObj->check_vip_mode($vipOrderObj->order_detail['buyback_categories_id']);
		$vipOrderObj->order_request_expired();
	}
}

function tep_cancel_buyback_order($buyback_oid) {
	global $sup_languages_id, $currencies;
	
	// Check if this Pending Order belong to this customer
	$buyback_order_check_select_sql = "	SELECT brg.buyback_request_group_date, brg.currency, brg.currency_value, l.locking_by 
										FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
										LEFT JOIN " . TABLE_LOCKING . " AS l 
											ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
										WHERE brg.buyback_request_group_id = '" . tep_db_input($buyback_oid) . "' 
											AND brg.buyback_status_id = 1 
											AND brg.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
	$buyback_order_check_result_sql = tep_db_query($buyback_order_check_select_sql);
	if ($buyback_order_check_row = tep_db_fetch_array($buyback_order_check_result_sql)) {
		// Cancel and hide the restock character info
		$buyback_order_cancel_update_sql = "UPDATE " . TABLE_BUYBACK_REQUEST_GROUP . "
											SET buyback_status_id = 4, 
												show_restock = 0 
											WHERE buyback_request_group_id = '" . tep_db_input($buyback_oid) . "'";
		tep_db_query($buyback_order_cancel_update_sql);
		
		if (tep_not_null($buyback_order_check_row['locking_by'])) {
			tep_order_lock($buyback_oid, ORDERS_LOG_UNLOCK_OWN_ORDER, $_SESSION['customer_id']);
		}
		
		$customer_info_select_sql = "	SELECT customers_firstname, customers_lastname, customers_email_address, customers_gender 
										FROM " . TABLE_CUSTOMERS . "
										WHERE customers_id = '" . tep_db_input($_SESSION['customer_id']) . "'";
		$customer_info_result_sql = tep_db_query($customer_info_select_sql);
		if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
		    //get total buyback and buyback product details
		    $buyback_request_details_select_sql = "SELECT * 
		                                           FROM ". TABLE_BUYBACK_REQUEST ."
		                                           WHERE buyback_request_group_id = '" . tep_db_input($buyback_oid) . "'";
		    
		    $buyback_request_details_result_sql = tep_db_query($buyback_request_details_select_sql);
		    $buyback_request_details_row = tep_db_fetch_array($buyback_request_details_result_sql);
		    
		    $cat_path = tep_output_generated_category_path($buyback_request_details_row['products_id'], 'product');
		    $buyback_product_list = $buyback_request_details_row['buyback_request_quantity'].' x '.$cat_path.' = ' . (defined('EMAIL_DISPLAY_CURRENCY') ? constant('EMAIL_DISPLAY_CURRENCY') : constant('DISPLAY_CURRENCY')) . ' ' . $currencies->apply_currency_exchange((double)$buyback_request_details_row['buyback_amount'], $buyback_order_check_row['currency'], $buyback_order_check_row['currency_value'])."\n";
			
				$to_name = tep_mb_convert_encoding($customer_info_row['customers_firstname'].' '.$customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET);
				$to_email = $customer_info_row['customers_email_address'];
				
				$email_content =tep_mb_convert_encoding(tep_get_email_greeting(tep_mb_convert_encoding($customer_info_row['customers_firstname'], EMAIL_CHARSET, CHARSET), tep_mb_convert_encoding($customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_info_row['customers_gender']), CHARSET, EMAIL_CHARSET) . 
								EMAIL_BUYBACK_UPDATE_BODY . 
								sprintf(EMAIL_BUYBACK_UPDATE_ORDER_NUMBER, $buyback_oid) . "\n" .
								sprintf(EMAIL_BUYBACK_UPDATE_DATE_ORDERED, $buyback_order_check_row['buyback_request_group_date']) . "\n" .
								sprintf(EMAIL_BUYBACK_UPDATE_CUSTOMER_EMAIL, $to_email) . "\n\n" .
								EMAIL_BUYBACK_UPDATE_PRODUCTS . "\n" . EMAIL_SEPARATOR . "\n" . $buyback_product_list .
        						EMAIL_SEPARATOR . "\n" . 
         						sprintf(EMAIL_BUYBACK_UPDATE_ORDER_TOTAL, defined('EMAIL_DISPLAY_CURRENCY') ? constant('EMAIL_DISPLAY_CURRENCY') : constant('DISPLAY_CURRENCY'), $currencies->apply_currency_exchange($buyback_request_details_row['buyback_amount'], $buyback_order_check_row['currency'], $buyback_order_check_row['currency_value'])) . "\n\n" .
								//sprintf(EMAIL_BUYBACK_UPDATE_STATUS, tep_get_buyback_order_status_name(1, $sup_languages_id) . '->' . tep_get_buyback_order_status_name(4, $sup_languages_id)) . "\n\n" . 
								EMAIL_BUYBACK_UPDATE_STATUS . "\n" .
								EMAIL_BUYBACK_ORDER_GUIDE . "\n\n" .
								EMAIL_BUYBACK_UPDATE_FOOTER;
			
				tep_mail($to_name, $to_email, sprintf(EMAIL_BUYBACK_UPDATE_SUBJECT, $buyback_oid), $email_content, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
			
			//update buyback history and comments history
			$buyback_history_data_array = array('buyback_request_group_id' => $buyback_oid,
											   	'buyback_status_id' => '4',
											   	'date_added' => 'now()',
											   	'customer_notified' => '1',
											   	'comments' => 'Manual Cancel',
											   	'changed_by' => $customer_info_row['customers_email_address']
												);
			tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
		}
		
		return true;
	} else {
		return false;
	}
}

function tep_get_buyback_restock_char($products_id) {
	//get category
	$buyback_main_cat_array = tep_get_buyback_main_cat_info($products_id, 'product');
	
	//get restock
	$restock_character = '';
	$get_restock_charname_select_sql = "SELECT rcs.restock_character_sets_id, rcs.restock_character_sets_cat_id as server_cat_id, rcs.restock_character_sets_name,
											rci.restock_character, rcs2ctg.categories_id as main_cat_id
										FROM " . TABLE_RESTOCK_CHARACTER_SETS . " AS rcs
										INNER JOIN " . TABLE_RESTOCK_CHARACTER_SETS_TO_CATEGORIES . " AS rcs2ctg
											ON (rcs.restock_character_sets_id = rcs2ctg.restock_character_sets_id
												AND rcs2ctg.categories_id = '" . tep_db_input($buyback_main_cat_array['id']) . "') 
										LEFT JOIN " . TABLE_RESTOCK_CHARACTER_INFO . " AS rci
											ON (rci.restock_character_sets_id = rcs.restock_character_sets_id)
										WHERE rci.products_id = '" . (int)$products_id. "'";
	$get_restock_charname_result_sql = tep_db_query($get_restock_charname_select_sql);
	
	if ($get_restock_charname_row = tep_db_fetch_array($get_restock_charname_result_sql)) {
		$restock_character = $get_restock_charname_row['restock_character'];
	}
	
	return $restock_character;
}

function tep_check_pin_number($pin_number_str, $customer_id='') {
	$customer_id = tep_not_null($customer_id) ? $customer_id : $_SESSION['customer_id'];
	
	$checkStatus = false;
	
	$pin_number_select_sql = "	SELECT customers_pin_number 
								FROM " . TABLE_CUSTOMERS . " 
								WHERE customers_id = '".tep_db_input($customer_id)."'";
	$pin_number_result_sql = tep_db_query($pin_number_select_sql);
	if ($pin_number_row = tep_db_fetch_array($pin_number_result_sql)) {
		$checkStatus = tep_validate_password($pin_number_str, $pin_number_row['customers_pin_number']);
	}
	return $checkStatus;
}

function tep_get_language_selection() {
	$pref_language_array = array();
//		$languages_query = tep_db_query("select languages_id, name from " . TABLE_SUPPLIER_LANGUAGES . " order by sort_order");
	$languages_query = tep_db_query("select languages_id, name from " . TABLE_SUPPLIER_LANGUAGES . " where code = 'zh' order by sort_order");
	while ($languages = tep_db_fetch_array($languages_query)) {
		$pref_language_array[] = array(	'id' => $languages['languages_id'],
										'text' => $languages['name']);
	}
	return $pref_language_array;
}

/**
 * When $products_id is not zero, max_orders is total by customer AND product
 */
function tep_accept_buyback_order($customers_id, $max_orders, $products_id=0) {
	$accept_buyback_order = true;
	
	if ($products_id && !is_array($products_id)) {
		$products_id = array($products_id);
	}
	
	// For customer with Silver status and above (>=3), only constraint by Pending order
	$this_customers_group_id = tep_get_customers_group($customers_id);
	
	if ($this_customers_group_id >= 3 && !$products_id) {	// If by server then follow normal rules
		$buyback_order_status_where_str = " brg.buyback_status_id = 1 ";
	} else {
		$buyback_order_status_where_str = " IF(brg.buyback_status_id = 1, 1, IF(brg.buyback_status_id = 2, br.buyback_quantity_received=0, 0)) ";
	}
	
    $num_awaiting_orders_select_sql = "	SELECT brg.buyback_request_group_id
					   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
										INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
					                		ON brg.buyback_request_group_id = br.buyback_request_group_id 
					            		WHERE brg.customers_id='" . tep_db_input($customers_id) . "' 
					            			AND " . $buyback_order_status_where_str;
    if ($products_id) {
    	array_walk($products_id, 'tep_addslashes_input');
   		$num_awaiting_orders_select_sql .= " AND br.products_id IN ('" . implode("','", $products_id) . "') ";
    }
    
    $num_awaiting_orders_result_sql = tep_db_query($num_awaiting_orders_select_sql);
    
	if (tep_db_num_rows($num_awaiting_orders_result_sql) >= $max_orders) {
		$accept_buyback_order = false;		
	}
	
	return $accept_buyback_order;
}

/**
 * Prevent same character name used in any of existing Pending / Processing (Unprocessed) Buyback Orders
 */
function tep_share_sender_id($sender_id, $products_id) {
	$buyback_order_status_where_str = " IF(brg.buyback_status_id = 1, 1, IF(brg.buyback_status_id = 2, br.buyback_quantity_received=0, 0)) ";
	
    $sender_id_exists_select_sql = "SELECT brg.buyback_request_group_id
					 	         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
									INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
				                		ON brg.buyback_request_group_id = br.buyback_request_group_id 
				            		WHERE br.products_id = '".tep_db_input($products_id)."' 
				            			AND LOWER(br.buyback_sender_character) = '".tep_db_input(strtolower($sender_id))."' 
				            			AND " . $buyback_order_status_where_str;
	//echo $sender_id_exists_select_sql;
    $sender_id_exists_result_sql = tep_db_query($sender_id_exists_select_sql);
	if (tep_db_num_rows($sender_id_exists_result_sql)) {
		return true;
	} else {
		return false;
	}
}

function tep_calculate_floating_available_qty($products_id, $real_time = true) {
	$floating_qty = 0;
	
	if ($real_time) {
		$main_cat_eta_offset = tep_get_game_preset_eta($products_id);
		$floating_qty_select_sql = "SELECT products_quantity, products_delivered_quantity, o.date_purchased, op.orders_products_purchase_eta 
									FROM " . TABLE_ORDERS . " AS o 
									INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
										ON o.orders_id=op.orders_id 
									WHERE op.products_id = '" . tep_db_input($products_id) . "' 
										AND o.orders_status IN (2, 7)";
											
		$floating_qty_result_sql = tep_db_query($floating_qty_select_sql);
		
		$current_time  = time();
		
		while ($floating_qty_row = tep_db_fetch_array($floating_qty_result_sql)) {
			if (tep_not_null($floating_qty_row['orders_products_purchase_eta'])) {
				if ($floating_qty_row['orders_products_purchase_eta'] == '-999') {
					$floating_qty += $floating_qty_row['products_quantity'] - $floating_qty_row['products_delivered_quantity'];	
				} else if ($floating_qty_row['orders_products_purchase_eta'] > 0) {
	//				list($dp_date_str, $dp_time_str) = explode(' ', $floating_qty_row['date_purchased']);
	//				list($dp_yr, $dp_mth, $dp_day) = explode('-', $dp_date_str);
	//				list($dp_hr, $dp_min, $dp_sec) = explode(':', $dp_time_str);
					
	//				$eta = mktime((int)$dp_hr+$main_cat_eta_offset+$floating_qty_row['orders_products_purchase_eta'], (int)$dp_min, (int)$dp_sec, $dp_mth, $dp_day, $dp_yr);
					$extra_hour = $main_cat_eta_offset + $floating_qty_row['orders_products_purchase_eta'];
					$eta = strtotime($floating_qty_row['date_purchased']." + ".$extra_hour." hours");
					
					if ($eta > $current_time) {
						$floating_qty += $floating_qty_row['products_quantity'] - $floating_qty_row['products_delivered_quantity'];	
					}
				}
			}
		}
		
		$update_floating_qty = "UPDATE " . TABLE_AUTOMATE_BUYBACK_PRICE . " 
								SET floating_qty = '".(int)$floating_qty."', 
									last_cache_date = now() 
								WHERE products_id = '".(int)$products_id."'";
		tep_db_query($update_floating_qty);
	} else {
		$get_floating_qty_select_sql = "SELECT floating_qty 
										FROM " . TABLE_AUTOMATE_BUYBACK_PRICE . " 
										WHERE products_id = '".(int)$products_id."' AND DATE_ADD(last_cache_date, INTERVAL 120 SECOND) > NOW()";
		$get_floating_qty_result_sql = tep_db_query($get_floating_qty_select_sql);
		
		if ($get_floating_qty_row = tep_db_fetch_array($get_floating_qty_result_sql)) {
			$floating_qty = $get_floating_qty_row['floating_qty'];
		} else {
			$main_cat_eta_offset = tep_get_game_preset_eta($products_id);
			$floating_qty_select_sql = "SELECT products_quantity, products_delivered_quantity, o.date_purchased, op.orders_products_purchase_eta 
										FROM " . TABLE_ORDERS . " AS o 
										INNER JOIN " . TABLE_ORDERS_PRODUCTS . " AS op 
											ON o.orders_id=op.orders_id 
										WHERE op.products_id = '" . tep_db_input($products_id) . "' 
											AND o.orders_status IN (2, 7)";
												
			$floating_qty_result_sql = tep_db_query($floating_qty_select_sql);
			
			$current_time  = time();
			
			while ($floating_qty_row = tep_db_fetch_array($floating_qty_result_sql)) {
				if (tep_not_null($floating_qty_row['orders_products_purchase_eta'])) {
					if ($floating_qty_row['orders_products_purchase_eta'] == '-999') {
						$floating_qty += $floating_qty_row['products_quantity'] - $floating_qty_row['products_delivered_quantity'];	
					} else if ($floating_qty_row['orders_products_purchase_eta'] > 0) {
						$extra_hour = $main_cat_eta_offset + $floating_qty_row['orders_products_purchase_eta'];
						$eta = strtotime($floating_qty_row['date_purchased']." + ".$extra_hour." hours");
						
						if ($eta > $current_time) {
							$floating_qty += $floating_qty_row['products_quantity'] - $floating_qty_row['products_delivered_quantity'];	
						}
					}
				}
			}
			
			$update_floating_qty = "UPDATE " . TABLE_AUTOMATE_BUYBACK_PRICE . " 
									SET floating_qty = '".(int)$floating_qty."', 
										last_cache_date = now() 
									WHERE products_id = '".(int)$products_id."'";
			tep_db_query($update_floating_qty);
		}
	}
	
	return (int)$floating_qty;
}

function tep_get_vip_excluded_quantity($products_id, $orders_products_id) {
	// find which quantity are not belong to the particular orders_products_id
	$first_list_quantity = 0;
    
    $buyback_prod_pending_list_qty_select_sql = "	SELECT br.buyback_request_quantity
								   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
								                		ON brg.buyback_request_group_id = br.buyback_request_group_id 
								            		WHERE br.products_id = '" . tep_db_input($products_id) . "' 
								            			AND br.orders_products_id <> '" . tep_db_input($orders_products_id) . "' 
								            			AND brg.buyback_status_id = 1 
								            			AND brg.buyback_request_order_type = 1 ";
    $buyback_prod_pending_list_qty_result_sql = tep_db_query($buyback_prod_pending_list_qty_select_sql);
    while ($buyback_prod_pending_list_qty_row = tep_db_fetch_array($buyback_prod_pending_list_qty_result_sql)) {
    	$first_list_quantity += (int)$buyback_prod_pending_list_qty_row['buyback_request_quantity'];
    }
    
    $buyback_prod_processing_list_qty_select_sql = "SELECT IF(brg.buyback_request_group_site_id > 0, br.buyback_quantity_confirmed, br.buyback_request_quantity) as first_list_qty
								   		         	FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
													INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
								                		ON brg.buyback_request_group_id = br.buyback_request_group_id 
								            		WHERE br.products_id = '" . tep_db_input($products_id) . "' 
								            			AND br.orders_products_id <> '" . tep_db_input($orders_products_id) . "' 
								            			AND brg.buyback_status_id = 2 
								            			AND brg.buyback_request_order_type = 1
								            			AND br.buyback_quantity_received = 0";
    $buyback_prod_processing_list_qty_result_sql = tep_db_query($buyback_prod_processing_list_qty_select_sql);
    while ($buyback_prod_processing_list_qty_row = tep_db_fetch_array($buyback_prod_processing_list_qty_result_sql)) {
    	$first_list_quantity += (int)$buyback_prod_processing_list_qty_row['first_list_qty'];
    }
    
	return $first_list_quantity;
}

function tep_game_open_for_buyback($game_id) {
	$game_is_buyback = true;
	$do_not_buyback_select_sql = "	SELECT buyback_setting_reference_id 
									FROM " . TABLE_BUYBACK_SETTING . "
									WHERE buyback_setting_table_name = '".TABLE_BUYBACK_CATEGORIES."' 
										AND buyback_setting_reference_id = '".tep_db_input($game_id)."' 
										AND buyback_setting_key = 'ofp_do_not_buyback' 
										AND buyback_setting_value = '1'";
	$do_not_buyback_result_sql = tep_db_query($do_not_buyback_select_sql);
	
	if ($do_not_buyback_row = tep_db_fetch_array($do_not_buyback_result_sql)) {
		$not_buyback_start_time_select_sql = "	SELECT buyback_setting_value 
												FROM " . TABLE_BUYBACK_SETTING . "
												WHERE buyback_setting_table_name = '".TABLE_BUYBACK_CATEGORIES."' 
													AND buyback_setting_reference_id = '".tep_db_input($game_id)."' 
													AND buyback_setting_key = 'ofp_not_buyback_start_time' ";
		$not_buyback_start_time_result_sql = tep_db_query($not_buyback_start_time_select_sql);
		$not_buyback_start_time_row = tep_db_fetch_array($not_buyback_start_time_result_sql);
		
		$not_buyback_end_time_select_sql = "SELECT buyback_setting_value 
											FROM " . TABLE_BUYBACK_SETTING . "
											WHERE buyback_setting_table_name = '".TABLE_BUYBACK_CATEGORIES."' 
												AND buyback_setting_reference_id = '".tep_db_input($game_id)."' 
												AND buyback_setting_key = 'ofp_not_buyback_end_time' ";
		$not_buyback_end_time_result_sql = tep_db_query($not_buyback_end_time_select_sql);
		$not_buyback_end_time_row = tep_db_fetch_array($not_buyback_end_time_result_sql);
		
		if (tep_not_null($not_buyback_start_time_row['buyback_setting_value']) && tep_not_null($not_buyback_end_time_row['buyback_setting_value'])) {
			$not_buyback_start_time_array = explode(',', $not_buyback_start_time_row['buyback_setting_value']);
			$not_buyback_end_time_array = explode(',', $not_buyback_end_time_row['buyback_setting_value']);
			
			for ($time_cnt=0; $time_cnt < count($not_buyback_start_time_array); $time_cnt++) {
				if (tep_time_check($not_buyback_start_time_array[$time_cnt], $not_buyback_end_time_array[$time_cnt])) {	// Fall in not buyback time range
					$game_is_buyback = false;
					break;
				}
			}
		} else {	// Do not buyback anytime
			$game_is_buyback = false;
		}
	}
	
	return $game_is_buyback;
}

function tep_get_game_reopen_time($game_id) {
	$reopen_time = '';
	
	$not_buyback_start_time_select_sql = "	SELECT buyback_setting_value 
											FROM " . TABLE_BUYBACK_SETTING . "
											WHERE buyback_setting_table_name = '".TABLE_BUYBACK_CATEGORIES."' 
												AND buyback_setting_reference_id = '".tep_db_input($game_id)."' 
												AND buyback_setting_key = 'ofp_not_buyback_start_time' ";
	$not_buyback_start_time_result_sql = tep_db_query($not_buyback_start_time_select_sql);
	$not_buyback_start_time_row = tep_db_fetch_array($not_buyback_start_time_result_sql);
	
	$not_buyback_end_time_select_sql = "SELECT buyback_setting_value 
										FROM " . TABLE_BUYBACK_SETTING . "
										WHERE buyback_setting_table_name = '".TABLE_BUYBACK_CATEGORIES."' 
											AND buyback_setting_reference_id = '".tep_db_input($game_id)."' 
											AND buyback_setting_key = 'ofp_not_buyback_end_time' ";
	$not_buyback_end_time_result_sql = tep_db_query($not_buyback_end_time_select_sql);
	$not_buyback_end_time_row = tep_db_fetch_array($not_buyback_end_time_result_sql);
	
	if (tep_not_null($not_buyback_start_time_row['buyback_setting_value']) && tep_not_null($not_buyback_end_time_row['buyback_setting_value'])) {
		$not_buyback_start_time_array = explode(',', $not_buyback_start_time_row['buyback_setting_value']);
		$not_buyback_end_time_array = explode(',', $not_buyback_end_time_row['buyback_setting_value']);
		
		for ($time_cnt=0; $time_cnt < count($not_buyback_start_time_array); $time_cnt++) {
			$time_diff_secs = tep_time_check($not_buyback_start_time_array[$time_cnt], $not_buyback_end_time_array[$time_cnt], '', true);
			if ($time_diff_secs !== FALSE) {	// Fall in not buyback time range
				$reopen_time = $time_diff_secs/60;
				break;
			}
		}
	}
	
	return $reopen_time;
}

function tep_get_game_pending_buyback($game_id, $site_id) {
	$subcat_array = array($game_id);
	tep_get_subcategories($subcat_array, $game_id);
	
	$pending_buyback_select_sql = "	SELECT COUNT(DISTINCT brg.buyback_request_group_id) AS total_pending_order 
		            				FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
		            				INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
		            					ON brg.buyback_request_group_id = br.buyback_request_group_id 
		            				INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c 
										ON br.products_id=p2c.products_id 
		            				WHERE brg.buyback_status_id=1 
		            					AND brg.buyback_request_group_site_id='".tep_db_input($site_id)."' 
		            					AND p2c.categories_id IN ('" . implode("', '", $subcat_array) . "') 
		            					AND p2c.products_is_link=0 ";
    $pending_buyback_result_sql = tep_db_query($pending_buyback_select_sql);
	$pending_buyback_row = tep_db_fetch_array($pending_buyback_result_sql);
	
	return $pending_buyback_row['total_pending_order'];
}

function tep_get_game_preset_eta($product_id) {
	$buyback_main_cat_info = tep_get_buyback_main_cat_info($product_id, 'product');
	
	$cat_purchase_eta_offset_select_sql = "	SELECT buyback_setting_value 
											FROM " . TABLE_BUYBACK_SETTING . " 
											WHERE buyback_setting_table_name = '" . TABLE_BUYBACK_CATEGORIES . "' 
												AND buyback_setting_key = 'ofp_purchase_eta' 
												AND buyback_setting_reference_id = '" . tep_db_input($buyback_main_cat_info['id']) . "'";
	$cat_purchase_eta_offset_result_sql = tep_db_query($cat_purchase_eta_offset_select_sql);
	if ($cat_purchase_eta_offset_row = tep_db_fetch_array($cat_purchase_eta_offset_result_sql)) {
		$main_cat_eta_offset = (int)$cat_purchase_eta_offset_row['buyback_setting_value'];
	} else {
		$main_cat_eta_offset = 0;
	}
	
	return $main_cat_eta_offset;
}

function tep_order_lock($buyback_request_group_id, $orders_log_system_messages, $orders_log_admin_id) {
	$log_object = new log_files($orders_log_admin_id);
	
	if ($orders_log_system_messages == ORDERS_LOG_UNLOCK_OWN_ORDER) {
		$unlocking_orders_sql = "DELETE FROM " . TABLE_LOCKING . " WHERE locking_trans_id = '" . tep_db_input($buyback_request_group_id) . "' AND locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "'";
		tep_db_query($unlocking_orders_sql);
	}
	
	$log_object->insert_orders_log($buyback_request_group_id, $orders_log_system_messages, FILENAME_BUYBACK_REQUESTS_INFO);
}
?>