<?
function tep_get_browser($user_agent_var) {
	if((ereg("Nav", $user_agent_var)) || (ereg("Gold", $user_agent_var)) || (ereg("X11", $user_agent_var)) || (ereg("Mozilla", $user_agent_var)) || (ereg("Netscape", $user_agent_var)) AND (!ereg("MSIE", $user_agent_var))) $browser = "Netscape";
	elseif(ereg("MSIE", $user_agent_var)) $browser = "MSIE";
	elseif(ereg("Lynx", $user_agent_var)) $browser = "Lynx";
	elseif(ereg("Opera", $user_agent_var)) $browser = "Opera";
	elseif(ereg("WebTV", $user_agent_var)) $browser = "WebTV";
	elseif(ereg("Konqueror", $user_agent_var)) $browser = "Konqueror";
	elseif((eregi("bot", $user_agent_var)) || (ereg("Google", $user_agent_var)) || (ereg("Slurp", $user_agent_var)) || (ereg("Scooter", $user_agent_var)) || (eregi("Spider", $user_agent_var)) || (eregi("Infoseek", $user_agent_var))) $browser = "Bot";
	else $browser = "Other";
	
	return $browser;
}

function tep_get_os($user_agent_var) {
	if(ereg("Win", $user_agent_var)) $os = "Windows";
	elseif((ereg("Mac", $user_agent_var)) || (ereg("PPC", $user_agent_var))) $os = "Mac";
	elseif(ereg("Linux", $user_agent_var)) $os = "Linux";
	elseif(ereg("FreeBSD", $user_agent_var)) $os = "FreeBSD";
	elseif(ereg("SunOS", $user_agent_var)) $os = "SunOS";
	elseif(ereg("IRIX", $user_agent_var)) $os = "IRIX";
	elseif(ereg("BeOS", $user_agent_var)) $os = "BeOS";
	elseif(ereg("OS/2", $user_agent_var)) $os = "OS/2";
	elseif(ereg("AIX", $user_agent_var)) $os = "AIX";
	else $os = "Other";
	
	return $os;
}
?>