<?
/*
  	$Id: header.php,v 1.13 2009/01/16 09:32:39 weichen Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/

$faq_infolinks_groups_id_arr = array(11);

// check if the 'install' directory exists, and warn of its existence
if (WARN_INSTALL_EXISTENCE == 'true') {
	if (file_exists(dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/install')) {
		$messageStack->add('header', WARNING_INSTALL_DIRECTORY_EXISTS, 'warning');
    }
}

// check if the configure.php file is writeable
if (WARN_CONFIG_WRITEABLE == 'true') {
	if ( (file_exists(dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/includes/configure.php')) && (is_writeable(dirname($HTTP_SERVER_VARS['SCRIPT_FILENAME']) . '/includes/configure.php')) ) {
		$messageStack->add('header', WARNING_CONFIG_FILE_WRITEABLE, 'warning');
    }
}

// check if the session folder is writeable
if (WARN_SESSION_DIRECTORY_NOT_WRITEABLE == 'true') {
	if (STORE_SESSIONS == '') {
		if (!is_dir(tep_session_save_path())) {
        	$messageStack->add('header', WARNING_SESSION_DIRECTORY_NON_EXISTENT, 'warning');
      	} elseif (!is_writeable(tep_session_save_path())) {
        	$messageStack->add('header', WARNING_SESSION_DIRECTORY_NOT_WRITEABLE, 'warning');
      	}
	}
}

// check session.auto_start is disabled
if ( (function_exists('ini_get')) && (WARN_SESSION_AUTO_START == 'true') ) {
	if (ini_get('session.auto_start') == '1') {
		$messageStack->add('header', WARNING_SESSION_AUTO_START, 'warning');
	}
}
/*
if ( (WARN_DOWNLOAD_DIRECTORY_NOT_READABLE == 'true') && (DOWNLOAD_ENABLED == 'true') ) {
	if (!is_dir(DIR_FS_DOWNLOAD)) {
		$messageStack->add('header', WARNING_DOWNLOAD_DIRECTORY_NON_EXISTENT, 'warning');
	}
}
*/
// this will let the admin know that the website is DOWN FOR MAINTENANCE to the public
if ( (DOWN_FOR_MAINTENANCE == 'true') && (EXCLUDE_ADMIN_IP_FOR_MAINTENANCE == getenv('REMOTE_ADDR')) ) {
	$messageStack->add('header', TEXT_ADMIN_DOWN_FOR_MAINTENANCE, 'warning');
}

if ($messageStack->size('header') > 0) {
	echo $messageStack->output('header');
}

if (isset($HTTP_GET_VARS['error_message']) && tep_not_null($HTTP_GET_VARS['error_message'])) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
  	<tr class="headerError">
    	<td class="headerError"><?=htmlspecialchars(urldecode($HTTP_GET_VARS['error_message']))?></td>
  	</tr>
	</table>
<?
}

if (isset($HTTP_GET_VARS['info_message']) && tep_not_null($HTTP_GET_VARS['info_message'])) {
?>
	<table border="0" width="100%" cellspacing="0" cellpadding="2">
  	<tr class="headerInfo">
    	<td class="headerInfo"><?=htmlspecialchars($HTTP_GET_VARS['info_message'])?></td>
  	</tr>
	</table>
<?
}
?>

<?
//Initialise
/**
 * Box settings as default are;
 * 'user_group' : '0' is anonymous/not yet login. set as csv string of customer/supplier group_id.
 * 'column' : array where column name is the key .
 *		'show_loc' : array of filenames where box appears, use FILENAME_ALL if show all
 *		'hide_loc' : array of filenaes where box IS HIDDEN, use FILENAME_ALL if hide all. takes precedence over show_loc.
 *		'order' : sort order for the box in that column
 * 		'status' : 0 is hide, 1 is show
 */
$default_block_setting = array(	'user_group' => '0',
								'vip_group' => '',
							 	'column'=> array(	'left' => array('show_loc' => array(FILENAME_DEFAULT),
												 					'hide_loc' => array(),
												 					'order' => 0,
												 					'status' => 0),
							 						'right' => array(	'show_loc' => array(FILENAME_DEFAULT),
													 					'hide_loc' => array(),
													 					'order' => 0,
													 					'status' => 0)
												)
						 	);

//Now lets make sure all blocks have at least the default setting.
$_app_blocks_arr = array();
if ($handle = opendir(DIR_WS_BOXES)) {
   while (false !== ($file = readdir($handle))) {
       if ($file != "." && $file != "..") {
           $_app_blocks_arr[$file] = $default_block_setting;
       }
   }
   closedir($handle);
}

//now we configure the blocks. hardcoded here until we have an interface.

$_my_account_files = array(	FILENAME_MY_ACCOUNT => HEADER_TITLE_MY_ACCOUNT,
						  	FILENAME_MY_ORDER_HISTORY => HEADER_TITLE_MY_ORDER_HISTORY,
						  	FILENAME_MY_PAYMENT_HISTORY => HEADER_TITLE_MY_PAYMENT_HISTORY,
						  	FILENAME_MY_FAVOURITE_LINKS => HEADER_TITLE_MY_FAVOURITE_LINKS,
						  	FILENAME_MY_ACCOUNT_MGMT => HEADER_TITLE_MY_ACCOUNT_MGMT
						  );

$_my_vip_account_files = array(	FILENAME_BOXES_VIP_ACCOUNT => HEADER_TITLE_MY_VIP_ACCOUNT,
								FILENAME_MY_VIP_REGISTER_SERVER => HEADER_TITLE_MY_VIP_REGISTER_SERVER,
						  		FILENAME_MY_VIP_INVENTORY_UPDATE => HEADER_TITLE_MY_VIP_INVENTORY_UPDATE,
						  		FILENAME_MY_VIP_ORDERS_HISTORY => HEADER_TITLE_MY_VIP_ORDERS_HISTORY,
						  		FILENAME_MY_VIP_REPORT => HEADER_TITLE_MY_VIP_REPORT
						  		);

$_app_blocks_arr['login.php'] = array(	'user_group' => '0',
										'vip_group' => '',
							 			'column'=> array('left' => array(	'show_loc' => array(FILENAME_ALL),
													 						'hide_loc' => array(FILENAME_DEFAULT),
													 						'order' => 0,
													 						'status' => 1),
							 							'right' => array(	'show_loc' => array(FILENAME_DEFAULT),
													 						'hide_loc' => array(),
													 						'order' => 10,
													 						'status' => 1)
														)
						 			);

$_app_blocks_arr['welcome.php'] = array('user_group' => '2,3,4,5,6',
										'vip_group' => '2,3',
							 			'column'=> array(	'left' => array('show_loc' => array(FILENAME_ALL),
													 						'hide_loc' => array(),
													 						'order' => 10,
													 						'status' => 1),
							 								'right' => array( 	'show_loc' => array(),
													 							'hide_loc' => array(),
													 							'order' => 0,
													 							'status' => 0)
														)
						 				);
$_app_blocks_arr['categories.php'] = array(	'user_group' => '0,2,3,4,5,6',
											'vip_group' => '',
							 				'column'=> array(	'left' => array('show_loc' => array(FILENAME_BUYBACK, FILENAME_DEFAULT),
													 							'hide_loc' => array(),
													 							'order' => 20,
													 							'status' => 1),
							 									'right' => array( 	'show_loc' => array(),
													 								'hide_loc' => array(),
													 								'order' => 0,
													 								'status' => 0)
															)
						 				);

$_app_blocks_arr['help_desk.php'] = array(	'user_group' => '0,2,3,4,5,6',
											'vip_group' => '',
							 				'column'=> array(	'left' => array('show_loc' => array(FILENAME_ALL),
													 							'hide_loc' => array(FILENAME_DEFAULT),
													 							'order' => 25,
													 							'status' => 1),
							 									'right' => array(	'show_loc' => array(FILENAME_DEFAULT),
													 								'hide_loc' => array(),
													 								'order' => 25,
													 								'status' => 1)
															)
						 				);

$_app_blocks_arr['faq.php'] = array( 	'user_group' => '0,2,3,4,5,6',
										'vip_group' => '',
							 			'column'=> array(	'left' => array('show_loc' => array(FILENAME_ALL),
													 						'hide_loc' => array(FILENAME_DEFAULT),
													 						'order' => 30,
													 						'status' => 1),
							 								'right' => array(	'show_loc' => array(FILENAME_DEFAULT),
													 							'hide_loc' => array(),
													 							'order' => 30,
													 							'status' => 1)
														)
						 			);
/*
$_app_blocks_arr['order_flow.php'] = array( 'user_group' => '0,2,3,4,5,6',
											'vip_group' => '',
							 				'column'=> array(	'left' => array('show_loc' => array(),
														 						'hide_loc' => array(),
														 						'order' => 5,
														 						'status' => 1),
							 									'right' => array(	'show_loc' => array(FILENAME_DEFAULT),
														 							'hide_loc' => array(),
														 							'order' => 5,
														 							'status' => 1)
															)
						 				);
*/
$_app_blocks_arr['partnership.php'] = array( 	'user_group' => '0,2,3,4,5,6',
												'vip_group' => '',
							 					'column'=> array(	'left' => array('show_loc' => array(FILENAME_DEFAULT),
															 						'hide_loc' => array(),
															 						'order' => 100,
															 						'status' => 1),
							 										'right' => array(	'show_loc' => array(),
															 							'hide_loc' => array(),
															 							'order' => 100,
															 							'status' => 1)
																)
						 					);

$_app_blocks_arr['myaccount.php'] = array(	'user_group' => '2,3,4,5,6',
											'vip_group' => '',
							 				'column'=> array(	'left' => array('show_loc' => array_keys(array_merge($_my_account_files, $_my_vip_account_files)),
													 							'hide_loc' => array(),
													 							'order' => 22,
													 							'status' => 1),
							 									'right' => array(	'show_loc' => array(),
													 								'hide_loc' => array(),
													 								'order' => 15,
													 								'status' => 1)
															)
						 				);
						 				
$_app_blocks_arr['vip_account.php'] = array('user_group' => '',
											'vip_group' => '2,3',
							 				'column'=> array(	'left' => array('show_loc' => array(FILENAME_ALL),
													 							'hide_loc' => array(),
													 							'order' => 18,
													 							'status' => 1),
							 									'right' => array(	'show_loc' => array(),
													 								'hide_loc' => array(),
													 								'order' => 15,
													 								'status' => 1)
															)
						 				);

$_app_blocks_arr['account_balance.php'] = array('user_group' => '2,3,4,5,6',
												'vip_group' => '',
							 					'column'=> array(	'left' => array('show_loc' => array(FILENAME_ALL),
													 								'hide_loc' => array(),
													 								'order' => 15,
													 								'status' => 1),
							 										'right' => array(	'show_loc' => array(),
													 									'hide_loc' => array(),
													 									'order' => 15,
													 									'status' => 1)
																)
						 						);
unset($_my_account_files[FILENAME_MY_ACCOUNT]); //use this for column display only
unset($_my_vip_account_files[FILENAME_BOXES_VIP_ACCOUNT]); //use this for column display only

?>