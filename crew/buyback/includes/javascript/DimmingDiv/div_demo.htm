
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
	<head>
		<title></title>
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
        
        <LINK REL=StyleSheet HREF="dimming.css" TYPE="text/css">
		<SCRIPT LANGUAGE="JavaScript" SRC="dimmingdiv.js">
		</SCRIPT>

		<script language="javascript">
		    function displayWindow()
		    {
		        var w, h, l, t;
		        w = 400;
		        h = 200;
		        l = screen.width/4;
		        t = screen.height/4;
                
                // no title		        
		        // displayFloatingDiv('windowcontent', '', w, h, l, t);

                // with title		        
		        displayFloatingDiv('windowcontent', 'Floating and Dimming Div', w, h, l, t);
		    }
		</script>
	</head>
	<body leftmargin="0" topmargin="0" bgcolor=FloralWhite>
	<table width="100%" border="0" cellspacing="0">
	    <tr style="background-image:url(bgimg.jpg)"><td background="sponge.jpg" width="90px" height="77px"></td>
	    <td><font color="white"><strong><span style="font-size: 14pt; font-family: Verdana">HOW TO MAKE FLOATING AND <br />DIMMING A DIV USING JAVASCRIPT</span></strong></font></td></tr>
	    
	    <tr><td colspan="2" border="0">
	        <table width="100%" height="100%">
	            <tr>
	                <td >
	                    <strong>Menu Area</strong><br />
	                <ul>
	                    <li><a href="#">Menu 1</a></li>
	                    <li><a href="#">Menu 2</a></li>
	                    <li><a href="#">Menu 3</a></li>
	                    <li><a href="#">Menu 4</a></li>
	                    <li><a href="#">Menu 5</a></li>
	                    <li><a href="#">Menu 6</a></li>
	                    <li><a href="#">Menu 7</a></li>
	                </ul>
	                
	                
	                </td> 
	                <td width="90%" valign="TOP" >
	                    <p align="center" ><b>Content Area</b></p>
	                    <br />
	                    <p>
	                    Give us your name clicking <a href="javascript:displayWindow();">Here</a>
	                    </p>
	                    
	                    
	                </td>
	                
	                </tr>
	        
	        </table>
	    
	    </td></tr>
	    
	</table>

<br />
 
 
<!--  the following hidden div will be used by the script -->
<div style="width: 518px; height: 287px;visibility:hidden" id="windowcontent">
            <script language="javascript">
            function Hello()
            {
               alert('Hello '+  document.getElementById('yourname').value + '!');
               
               
            }
            </script>
            <table >
            <tr><td colspan="2"></td></tr>
            <tr>
            <td>Your name:</td>
            <td><input type="text" style="width: 292px" id="yourname" /></td>
            </tr>
            <tr>
            <td colspan="2">
            <input type="button" value="Hello button" onclick="Hello();" style="width: 119px"/></td>
            </tr>
            <tr><td colspan="2"><br />Click the left mouse button on the blue header then move the floating div!!!</td></tr>
            </table>
</div>

		
	</body>
</html>
