<?php
/*
  $Id: form_check.js.php,v 1.5 2009/07/31 10:23:03 keepeng.foong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/
?>
<script language="javascript"><!--	
var form = "";
var submitted = false;
var error = false;
var error_message = "";

function Trim(strValue) {
	return LTrim(RTrim(strValue));
}

function LTrim(strValue) {
	var LTRIMrgExp = /^\s */;
	return strValue.replace(LTRIMrgExp, '');
}

function RTrim(strValue) {
	var RTRIMrgExp = /\s *$/;
	return strValue.replace(RTRIMrgExp, '');
}

function add_area_code(area_code) {
	area_no = document.getElementById(area_code).value;
  	area_no = Trim(area_no);
  	if(area_no.length < 4 && area_no.length == 3){      
    	area_no = '0'+area_no;
    	document.getElementById(area_code).value = area_no; 
  	} else {
  		document.getElementById(area_code).value = area_no; 	
  	}
}

function IsNumeric(value)
{
	var ValidChars = "**********";
	var IsNumber=true;
	var character;
	
	for (i = 0; i < value.length && IsNumber == true; i++) { 
		character = value.charAt(i); 
    	if (ValidChars.indexOf(character) == -1) {
    		IsNumber = false;
    	}
	}
   	return IsNumber;   
}

function check_input(field_name, field_size, message) {
  if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
    var field_value = form.elements[field_name].value;
	
	if (field_value == '' || field_value.length < field_size) {
	  error_message = error_message + "* " + message + "\n";
	  error = true;
	}
 }
}

function check_contact_no(field_name_1, field_name_2, field_size_1, field_size_2, message_1, message_2){
	var count = 0;
	var num_count = 0;
	if (form.elements[field_name_1] && (form.elements[field_name_1].type != "hidden") && form.elements[field_name_2] && (form.elements[field_name_2].type != "hidden")) {
		var field_value_1 = form.elements[field_name_1].value;
		var field_value_2 = form.elements[field_name_2].value;

		if (field_value_1 == '' || field_value_1.length < field_size_1) {
				count++;
		}
		if (field_value_2 == '' || field_value_2.length < field_size_2) {
				count++;
		}
	
		if (!IsNumeric(field_value_1) || !IsNumeric(field_value_2)) {
				num_count++;
		}
	
		if (count > 0){
			error_message = error_message + "* " + message_1 + "\n";
		  error = true;
		}
		else if (num_count > 0){
			error_message = error_message + "* " + message_2 + "\n";
		  error = true;
		}
	}
}

function check_phone_no(field_name, field_size, phone_type, message_1, message_2){
	if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
		var field_value = form.elements[field_name].value;
		if (field_value == '' || field_value.length != field_size){
			if (phone_type == "phone") {
				if (!(field_value.charAt(1) == '3' || field_value.charAt(1) == '5') || !(field_value.charAt(0) == '1')) {
					error_message = error_message + "* " + message_1 + "\n";
					error = true;	
				}
			}
		} else if (!IsNumeric(field_value)){
			error_message = error_message + "* " + message_2 + "\n";
			error = true;
		}
	}
}

function detect_phone_number_change(field_name, field_size, phone_type, default_value, message_1, message_2){
	if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
		var field_value = form.elements[field_name].value;
		if (field_value != default_value) {
			check_phone_no(field_name, field_size, phone_type, message_1, message_2)
		}
	}
}

function check_radio(field_name, message) {
  var isChecked = false;

  if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
    var radio = form.elements[field_name];

    for (var i=0; i<radio.length; i++) {
      if (radio[i].checked == true) {
        isChecked = true;
        break;
      }
    }

    if (isChecked == false) {
      error_message = error_message + "* " + message + "\n";
      error = true;
    }
  }
}

function check_select(field_name, field_default, message) {
	if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
		var field_value = form.elements[field_name].value;
		
		if (field_value == field_default) {
			error_message = error_message + "* " + message + "\n";
			error = true;
		}
	}
}

function check_dob(dob_month, dob_day, dob_year, message_1) {
	var day = form.elements[dob_day].value;
	var month = form.elements[dob_month].value;
	var year = form.elements[dob_year].value;
	var count = 0;
	
	if (month == '') {
		count++;
	}
	
	if (day == '') {
		count++;
	}
	
	if (year == '') {
		count++;
	}
	
	if (count > 0) {
		error_message = error_message + "* " + message_1 + "\n";
		error = true;
	}
}

function check_password(field_name_1, field_name_2, field_size, message_1, message_2) {
  if (form.elements[field_name_1] && (form.elements[field_name_1].type != "hidden")) {
    var password = form.elements[field_name_1].value;
    var confirmation = form.elements[field_name_2].value;

    if (password == '' || password.length < field_size) {
      error_message = error_message + "* " + message_1 + "\n";
      error = true;
    } else if (password != confirmation) {
      error_message = error_message + "* " + message_2 + "\n";
      error = true;
    }
  }
}


function check_password_new(field_name_1, field_name_2, field_name_3, field_size, message_1, message_2, message_3) {
  if (form.elements[field_name_1] && (form.elements[field_name_1].type != "hidden")) {
    var password_current = form.elements[field_name_1].value;
    var password_new = form.elements[field_name_2].value;
    var password_confirmation = form.elements[field_name_3].value;

    if (password_current == '' || password_current.length < field_size) {
      error_message = error_message + "* " + message_1 + "\n";
      error = true;
    } else if (password_new == '' || password_new.length < field_size) {
      error_message = error_message + "* " + message_2 + "\n";
      error = true;
    } else if (password_new != password_confirmation) {
      error_message = error_message + "* " + message_3 + "\n";
      error = true;
    }
  }
}


function check_form(form_name) {
	if (submitted == true) {
		alert("<?php echo JS_ERROR_SUBMITTED; ?>");
		return false;
	}
	
	error = false;
	form = form_name;
	
	error_message = "<?php echo JS_ERROR; ?>";
	
	check_input("email_address", "<?php echo ENTRY_EMAIL_ADDRESS_MIN_LENGTH; ?>", "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_EMAIL_ADDRESS); ?>");
	
	check_password("password", "confirm_password", "<?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>", "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_NEW_PASSWORD); ?>", "<?php echo TEXT_PASSWORD_NOTMATCH; ?>");
	
	check_input("firstname", 1, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_FIRST_NAME); ?>");
	check_input("lastname", 1, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_LAST_NAME); ?>");
	
	check_dob("dob_month", "dob_day", "dob_year", "<? echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_DATE_OF_BIRTH); ?>");
	check_input("city", 1, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CITY); ?>");
	check_select("state", "", "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_STATE); ?>");
	check_input("address", 3, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_ADDRESS); ?>");
	check_contact_no("contact_area_no", "contact_phone_no", 4, 7, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CONTACT_NO);?>", "<?php echo sprintf(TEXT_INVALID_NUMBER, TEXT_CONTACT_NO);?>");
	check_phone_no("mobile_no", 11, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_MOBILE_NO); ?>", "<?php echo sprintf(TEXT_INVALID_NUMBER, TEXT_MOBILE_NO); ?>");
	check_input("qq_no", 3, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_QQ_NO); ?>");
	
	
	if (error == true) {
		alert(error_message);
		return false;
	} else {
		submitted = true;
		return true;
	}
}

function check_form1(form_name) {
	if (submitted == true) {
		alert("<?php echo JS_ERROR_SUBMITTED; ?>");
    	return false;
  	}

  	error = false;
  	form = form_name;
  
  	error_message = "<?php echo JS_ERROR; ?>";
  	
   	check_input("email_address", "<?php echo ENTRY_EMAIL_ADDRESS_MIN_LENGTH; ?>", "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_EMAIL_ADDRESS); ?>");
   
  	check_input("firstname", 1, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_FIRST_NAME); ?>");
  	check_input("lastname", 1, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_LAST_NAME); ?>");
  
  	check_dob("dob_month", "dob_day", "dob_year", "<? echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_DATE_OF_BIRTH); ?>");
  	check_input("address", 3, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_ADDRESS); ?>");
  	check_input("city", 1, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CITY); ?>");
  	check_select("state", "", "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_STATE); ?>");

  	detect_phone_number_change("contact_no", 11, 'contact', "<?=substr($ori_contact_no['customers_telephone'], 0, -4) . '****'?>", "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CONTACT_NO);?>", "<?php echo sprintf(TEXT_INVALID_NUMBER, TEXT_CONTACT_NO); ?>");
  	detect_phone_number_change("mobile_no", 11, 'mobile', "<?=substr($ori_contact_no['customers_mobile'], 0, -4) . '****'?>", "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_MOBILE_NO); ?>", "<?php echo sprintf(TEXT_INVALID_NUMBER, TEXT_MOBILE_NO); ?>");
  	check_input("qq_no", 3, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_QQ_NO); ?>");
	
	for (var i = 1; i <= 1; i++) {
  		check_input("answer_"+i, 1, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, ENTRY_ANSWER); ?>");
  	}
	
  	if (error == true) {
    	alert(error_message);
    	return false;
  	} else {
    	submitted = true;
    	return true;
  	}
}

function check_form2(form_name) { 
	if (submitted == true) {
    	alert("<?php echo JS_ERROR_SUBMITTED; ?>");
    	return false;
  	}

  	error = false;
  	form = form_name;
  	error_message = "<?php echo JS_ERROR; ?>";
	check_input("current_password", 6, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CURRENT_PASSWORD); ?>");
  	check_password("password", "confirm_password", "<?php echo ENTRY_PASSWORD_MIN_LENGTH; ?>", "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_NEW_PASSWORD); ?>", "<?php echo TEXT_PASSWORD_NOTMATCH; ?>");
	for (var i = 1; i <= 2; i++) {
  		check_input("answer_"+i, 1, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, ENTRY_ANSWER); ?>"+i);
  	}
  	 	
  	if (error == true) {
  	  	alert(error_message);
		return false;
  	} else {
    	submitted = true;
    	return true;
  	}
}

function check_form3(form_name) { 
	if (submitted == true) {
    	alert("<?php echo JS_ERROR_SUBMITTED; ?>");
    	return false;
  	}

  	error = false;
  	form = form_name;
  	error_message = "<?php echo JS_ERROR; ?>";
  	for (var i = 1; i <= 2; i++) {
  		check_input("answer_"+i, 1, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, ENTRY_ANSWER); ?>"+i);
  	}
  	
  	if (error == true) {
  	  	alert(error_message);
		return false;
  	} else {
    	submitted = true;
    	return true;
  	}
}

function check_duplicate_question(fields_name, field_default, message) {
	var questionArray = new Array();
	var duplicateArray = new Array();
	var duplicate_count = 0;
	for (var qna = 0; qna < 3 ; qna++) {
		field_name = fields_name+(qna+1);
		if (form.elements[field_name] && (form.elements[field_name].type != "hidden")) {
			var field_value = form.elements[field_name].value;
			for (var quest_cnt = 0; quest_cnt < questionArray.length; quest_cnt++) {
				if (questionArray[quest_cnt] == field_value && field_value != 0) {
					duplicateArray[duplicate_count++] = "<?php echo ENTRY_QUESTION;?>"+(qna+1);
					break;
				}
			}
			questionArray[qna] = field_value;
		}
	}
	
	if (duplicateArray.length > 0) {
		duplicateQuestion = duplicateArray.join(",");
		error_message = error_message + "* " + message + duplicateQuestion + "\n";
		error = true;
	}
	
	
}

function check_form_security(form_name) {
	if (submitted == true) {
		alert("<?php echo JS_ERROR_SUBMITTED; ?>");
		return false;
	}
	error = false;
	form = form_name;
	error_message = "<?php echo JS_ERROR; ?>";
	for (var question=1; question< 4 ; question++) {
		<?php $answer_error = sprintf(TEXT_MANDATORY_ISBLANK, ENTRY_ANSWER);?>
		<?php $question_error = sprintf(TEXT_MANDATORY_ISBLANK, ENTRY_QUESTION); ?>
		var answer_error = "<?php echo $answer_error; ?>" + question;
		var question_error = "<?php echo $question_error; ?>" + question;
		check_select('question_'+question, "0", question_error);
		check_input('answer_'+question, 1, answer_error);
	}

	//check pin for account management.
	if (form_name.name == 'process3') {
		check_input("pin_number", 7, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_PIN_NUMBER); ?>");
	}
	
	//check duplicate entry.
	check_duplicate_question('question_', 0, "<?php echo JS_ERROR_DUPLICATE_ENTRY;?>");

	if (error == true) {
  	  	alert(error_message);
		return false;
  	} else {
    	submitted = true;
    	return true;
  	}
}

function check_form_pin(form_name) {
	if (submitted == true) {
		alert("<?php echo JS_ERROR_SUBMITTED; ?>");
		return false;
	}
	error = false;
	form = form_name;
	error_message = "<?php echo JS_ERROR; ?>";
	check_input("pin_number", 7, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_PIN_NUMBER); ?>");
	if (error == true) {
  	  	alert(error_message);
		return false;
  	} else {
    	submitted = true;
    	return true;
  	}
}

function check_form_email_address(form_name) {
	if (submitted == true) {
		alert("<?php echo JS_ERROR_SUBMITTED; ?>");
		return false;
	}
	error = false;
	form = form_name;
	error_message = "<?php echo JS_ERROR; ?>";
	check_input("email_address", <?php echo ENTRY_EMAIL_ADDRESS_MIN_LENGTH; ?>, "<?php echo sprintf(TEXT_MANDATORY_ISBLANK, TEXT_EMAIL_ADDRESS); ?>");
	if (error == true) {
  	  	alert(error_message);
		return false;
  	} else {
    	submitted = true;
    	return true;
  	}
}

//-->
</script>