function SetFocus() {
  	if (document.forms.length > 0) {
    	var field = document.forms[0];
    	for (i=0; i<field.length; i++) {
	      	if ( (field.elements[i].type != "image") &&
	           (field.elements[i].type != "hidden") &&
	           (field.elements[i].type != "reset") &&
	           (field.elements[i].type != "submit") ) {

	        	document.forms[0].elements[i].focus();

	        	if 	( (field.elements[i].type == "text") ||
	            	(field.elements[i].type == "password") )
	          		document.forms[0].elements[i].select();
	        	break;
	      	}
    	}
  	}
}

function noEnterKey(e_obj) {
	var characterCode;

	if (e_obj && e_obj.which) {		//if which property of event object is supported (NN4)
		e_obj = e_obj;
		characterCode = e_obj.which; 	//character code is contained in NN4's which property
	} else {
		e_obj = event;
		characterCode = e_obj.keyCode; 	//character code is contained in IE's keyCode property
	}

	if(characterCode == 13){ 	//if generated character code is equal to ascii 13 (if enter key)
		return false;
	} else {
		return true;
	}
}

function rowOverEffect(object, class_name) {
	if (object.className == 'dataTableRow') {
  		object.className = 'dataTableRowOver';
  	} else if (class_name != '' && class_name != undefined && object.className != 'rowSelected'){
  		object.className = class_name;
  	}
}

function rowOutEffect(object, class_name) {
  	if (object.className == 'dataTableRowOver') {
  		object.className = 'dataTableRow';
	} else if (class_name != '' && class_name != undefined && object.className != 'rowSelected'){
  		object.className = class_name;
  	}
}

function rowClicked (object, class_name) {
  	if (object.className == 'rowSelected') {
  		object.className = class_name;
	} else {
  		object.className = 'rowSelected';
  	}
}

function setTreeCheckbox(frmName, curname, doCheck, childStr)
{
	var browserName = navigator.appName;

	parseArr = new Array() ;
	frm = document.forms[frmName];
	do_check = doCheck;
	arrayOfStrings = curname.split('_');

	/***********************************
		arrayOfStrings[0] = MSel
		arrayOfStrings[1] = parent ID
		arrayOfStrings[2] = this ID
	***********************************/
	searchStr = arrayOfStrings[0] + "_" + arrayOfStrings[2] + "_" ;
	parseArr.push(searchStr) ;

    checkName = 'SelectedItem';
	var elts2      = (typeof(frm.elements[checkName+'[]']) != 'undefined')
					 ? frm.elements[checkName+'[]']
					 : "";
    var elts2_cnt  = (typeof(elts2.length) != 'undefined')
                  ? elts2.length
                  : 0;
	if (browserName == 'Microsoft Internet Explorer') {
	    while (searchStr)
		{
		    if (elts2_cnt) {
		        for (var i = 0; i < elts2_cnt; i++) {
		            e=elts2[i];

		            if (e.type=='checkbox' && e.id.search(searchStr) != -1 && e.checked!=do_check) {
						e.checked = do_check ;
					}
		        } // end for
		    } else if (elts2!='') {
		    	e=elts2;
		        if (e.type=='checkbox' && e.id.search(searchStr) != -1 && e.checked!=do_check) {
					e.checked = do_check ;
				}
		    } // end if... else
			searchStr = parseArr.shift() ;
		}
	} else {
		if (childStr != null) {
			childArray = childStr.split(',');
			for(var i=0; i<childArray.length; i++) {
				if (document.getElementById('MSel_'+arrayOfStrings[2]+'_'+childArray[i]) != null) {
					document.getElementById('MSel_'+arrayOfStrings[2]+'_'+childArray[i]).checked = do_check;
					if (document.getElementById('HiddenCat_'+childArray[i]) != undefined) {
						document.getElementById('HiddenCat_'+childArray[i]).value = (do_check ? 1 : 0);
					}
				}
			}
		}
	}
}

function setCheckboxes(the_form, control_field, check_items)
{
	if (document.getElementById(control_field).value > 0) {	// if currently selected all
		var do_check = 0;
		document.getElementById(control_field).value = 0;
	} else { // if currently not selected all
		var do_check = 1;
		document.getElementById(control_field).value = 1;
	}

	var checkObjArray = check_items.split('##');
	for (var opt=0; opt < checkObjArray.length; opt++) {
		var check_item = checkObjArray[opt];
		var elts      = (typeof(document.forms[the_form].elements[check_item+'[]']) != 'undefined')
						? document.forms[the_form].elements[check_item+'[]']
						: "";
		var elts_cnt  = (typeof(elts.length) != 'undefined')
						? elts.length
						: 0;

		if (elts_cnt) {
	    	for (var i = 0; i < elts_cnt; i++) {
	        	elts[i].checked = do_check;
	    	} // end for
		} else if (elts!='') {
	       	elts.checked = do_check;
		} // end if... else
	}
    return true;
} // end of the 'setCheckboxes()' function

function cmbRedirectInfoLinks(obj,url) {
	url=url + "&addnewpage=" + obj.value;

	document.location.href=url;
}

function gotoPage(page_url) {
	if (page_url != '')
		location.href = page_url;
}

function setAutoRefresh(limit, goto_url) {
	/************************************************
	DESCRIPTION:
		Refreshes a webpage, after the specified
		amount of time

	PARAMETERS:
	   limit - Refresh time in "minutes:seconds" format
	   goto_url - alternative page url (can be used for current page with POST DATA)

	RETURNS:
	   Call handleRefresh function to refresh the page
	   after "limit" time

	REMARKS:
	   Minutes should range from 0 to inifinity.
	   Seconds should range from 0 to 59
	*************************************************/

	if (document.images){
		var parselimit=limit.split(":")
		parselimit=parselimit[0]*60+parselimit[1]*1
	}

	handleRefresh(parselimit, goto_url);
}

function handleRefresh(remaining, goto_url) {
	if (!document.images)
		return
	if (remaining==1) {
		if (goto_url == null) {
			window.location.reload();
		} else {
			window.location.href = goto_url;
		}
	} else {
		remaining-=1
		curmin=Math.floor(remaining/60)
		cursec=remaining%60
		if (curmin != 0)
			curtime=curmin+" minutes and "+cursec+" seconds left until page refresh!"
		else
			curtime=cursec+" seconds left until page refresh!"

		window.status=curtime
		setTimeout("handleRefresh("+remaining+", '"+goto_url+"')",1000)
	}
}

function validateInteger( strValue ) {
	var objRegExp  = /(^\d\d*$)/;

	//check for integer characters
	return objRegExp.test(strValue);
}

function validateSignInteger( strValue ) {
	var objRegExp  = /^[\+\-]?\d\d*$/;

	//check for integer characters
	return objRegExp.test(strValue);
}

function validateMayHvPtDecimal(strValue, wholeResult) {
	if (wholeResult) {
		var objRegExp  = /^\d+(\.\d+)?$/;
	} else {
		var objRegExp  = /^\d+(\.)?(\d+)?$/;
	}
	//check for integer characters
	return objRegExp.test(strValue);
}

function validateMayHvPtSignDecimal(strValue) {
	var objRegExp  = /^[\+\-]?\d+(\.\d+)?$/;

	//check for integer characters
	return objRegExp.test(strValue);
}

function currencyValidation(str) {
	var curchr, retval, fullstop;
	str.toString();
	retval=1;
	if (str) {
	    fullstop=0;
		for (i=0; i < str.length; i++) {
			curchr = str.charAt(i) ;
			if (curchr!='.' && curchr!=',' && curchr!='0' && !parseInt(curchr)) {
				retval = 0;
			} else if (curchr=='.') {
			    fullstop++;
			}
		}
		if (fullstop>1) {
		    retval=0;
		}
	}
	return retval;
}

function validateDate( strValue ) {
	/************************************************
	DESCRIPTION: Validates that a string contains only
	    valid dates with 2 digit month, 2 digit day,
	    4 digit year. Date separator can be ., -, or /.
	    Uses combination of regular expressions and
	    string parsing to validate date.
	    Ex. yyyy/mm/dd or yyyy-mm-dd or yyyy.mm.dd

	PARAMETERS:
	   strValue - String to be tested for validity

	RETURNS:
	   True if valid, otherwise false.

	REMARKS:
	   Avoids some of the limitations of the Date.parse()
	   method such as the date separator character.
	*************************************************/
	//var objRegExp = /^\d{4}(\-|\/|\.)\d{1,2}\1\d{1,2}$/
	if (strValue.indexOf(':') > 1) {
		var dateTimeArray = strValue.split(' ');
		var iDate = dateTimeArray[0];
		var iTime = dateTimeArray[1];

		var timeRegExp=new RegExp("^([0-1]?[0-9]|2[0-3]):[0-5]?[0-9]$");
		if (timeRegExp.test(iTime)==false) {
			return false;
		}
	} else {
		var iDate = strValue;
	}
	var objRegExp = /^\d{4}(\-)\d{1,2}\1\d{1,2}$/

	//check to see if in correct format
	if(!objRegExp.test(iDate))
		return false; //doesn't match pattern, bad date
	else {
		var arrayDate = iDate.split(RegExp.$1); //split date into month, day, year
		var intDay = parseInt(arrayDate[2],10);
		var intYear = parseInt(arrayDate[0],10);
		var intMonth = parseInt(arrayDate[1],10);

		//check for valid month
		if(intMonth > 12 || intMonth < 1) {
			return false;
		}

	    //create a lookup for months not equal to Feb.
	    var arrayLookup = { '01' : 31,'03' : 31, '04' : 30,'05' : 31,'06' : 30,'07' : 31,
	                        '08' : 31,'09' : 30,'10' : 31,'11' : 30,'12' : 31}

	    //check if month value and day value agree
	    if(arrayLookup[arrayDate[1]] != null) {
	    	if(intDay <= arrayLookup[arrayDate[1]] && intDay != 0)
	        	return true; //found in lookup table, good date
	    }

	    //check for February
		var booLeapYear = (intYear % 4 == 0 && (intYear % 100 != 0 || intYear % 400 == 0));
	    if( ((booLeapYear && intDay <= 29) || (!booLeapYear && intDay <=28)) && intDay !=0)
	      	return true; //Feb. had valid number of days
	}

	return false; //any other values, bad date
}

function validateEmail(str) {
	var at="@";
	var dot=".";
	var lat=str.indexOf(at);
	var lstr=str.length;
	var ldot=str.indexOf(dot);

	if (str.indexOf(at)==-1) {
	   return false;
	}

	if (str.indexOf(at)==-1 || str.indexOf(at)==0 || str.indexOf(at)== (lstr-1)) {
	   return false;
	}

	if (str.indexOf(dot)==-1 || str.indexOf(dot)==0 || str.indexOf(dot)==(lstr-1)) {
	    return false;
	}

	if (str.indexOf(at,(lat+1))!=-1) {
	    return false;
	}

	if (str.substring(lat-1,lat)==dot || str.substring(lat+1,lat+2)==dot) {
	    return false;
	}

	if (str.indexOf(dot,(lat+2))==-1) {
		return false;
	}

	if (str.indexOf(" ")!=-1) {
		return false;
	}

	return true;
}

function SetCookie(name, value, expires, path, domain) {
	document.cookie = name + "=" + escape(value) +
  					((expires == null) ? "" : "; expires=" + getExpiryDate(expires)) +
  					((path == null)    ? "" : "; path=" + path) +
  					((domain == null)  ? "" : "; domain=" + domain);
}

function GetCookie(name) {
	var cname = name + "=";
  	var dc = document.cookie;
  	if (dc.length > 0) {
  		begin = dc.indexOf(cname);
    	if (begin != -1) {
    		begin += cname.length;
      		end = dc.indexOf(";", begin);
      		if (end == -1) end = dc.length;
      		return unescape(dc.substring(begin, end));
    	}
  	}
  	return null;
}

function getExpiryDate(days){
	var UTCstring;
	Today = new Date();
	nomilli = Date.parse(Today);
	Today.setTime(nomilli + days*24*60*60*1000);
	UTCstring = Today.toUTCString();
	return UTCstring;
}

function confirm_delete(s, t, loc) {
	answer = confirm('Are you sure to delete '+ (trim_str(s) != '' ? "<" + s + "> " : '') + t + ' record?')
	if (answer !=0) {
		location = loc;
	}
}

function confirm_action(msg, loc) {
	answer = confirm(msg)
	if (answer != 0) {
		if (loc != null) {
			location = loc;
		} else {
			return true;
		}
	} else {
		return false;
	}
}

function currency(anynum, sym_left, sym_right, dec) {
	var dec_pt = dec != null ? dec : 3;
	var retval = currency_display(anynum, dec_pt);

	if (sym_left != null && sym_left != '') {
		return sym_left + retval;
	} else {
		return retval + sym_right;
	}
}

function currency_display(number, places) {
	var result = Math.round(number*Math.pow(10,places))/Math.pow(10,places);
	var str_res = result.toString();

	if (str_res.lastIndexOf('.') < 0) {
		var padding_right_zero = '';

		for (var i=0; i < places; i++) {
			padding_right_zero += '0';
		}

		if (padding_right_zero.length > 0) {
			str_res = str_res + '.' + padding_right_zero;
		}
	}

	dStr = str_res.substr(0, str_res.indexOf("."));
	dNum = dStr - 0;
	pStr = str_res.substr(str_res.indexOf("."));
	while (pStr.length <= places) { pStr += '0'; }

	if (dNum >= 1000) {
		dLen = dStr.length;
		dStr = parseInt(""+(dNum/1000))+","+dStr.substring(dLen-3,dLen);
	}

	if (dNum >= 1000000) {
		dLen = dStr.length;
		dStr = parseInt(""+(dNum/1000000))+","+dStr.substring(dLen-7,dLen);
	}

	result = dStr + pStr;

	return result;
}

function trim_str(strValue) {
	return ltrim_str(rtrim_str(strValue));
}

function ltrim_str(strValue) {
	var LTRIMrgExp = /^\s */;
	return strValue.replace(LTRIMrgExp, '');
}

function rtrim_str(strValue) {
	var RTRIMrgExp = /\s *$/;
	return strValue.replace(RTRIMrgExp, '');
}

function replace(s, t, u) {
	/*
  	**  Replace a token in a string
  	**    s  string to be processed
  	**    t  token to be found and removed
  	**    u  token to be inserted
  	**  returns new String
  	*/
  	i = s.indexOf(t);
  	r = "";
  	if (i == -1) return s;
  	r += s.substring(0,i) + u;
  	if ( i + t.length < s.length)
    	r += replace(s.substring(i + t.length, s.length), t, u);
  	return r;
}

function URLEncode(plain_text) {
	// The Javascript escape and unescape functions do not correspond
	// with what browsers actually do...
	var SAFECHARS = "0123456789" +					// Numeric
					"ABCDEFGHIJKLMNOPQRSTUVWXYZ" +	// Alphabetic
					"abcdefghijklmnopqrstuvwxyz" +
					"-_.!~*'()";					// RFC2396 Mark characters
	var HEX = "0123456789ABCDEF";

	var plaintext = plain_text;
	var encoded = "";
	for (var i = 0; i < plaintext.length; i++ ) {
		var ch = plaintext.charAt(i);
	    if (ch == " ") {
		    encoded += "+";				// x-www-urlencoded, rather than %20
		} else if (SAFECHARS.indexOf(ch) != -1) {
		    encoded += ch;
		} else {
		    var charCode = ch.charCodeAt(0);
			if (charCode > 255) {
			    alert( "Unicode Character '"
                        + ch
                        + "' cannot be encoded using standard URL encoding.\n" +
				          "(URL encoding only supports 8-bit characters.)\n" +
						  "A space (+) will be substituted." );
				encoded += "+";
			} else {
				encoded += "%";
				encoded += HEX.charAt((charCode >> 4) & 0xF);
				encoded += HEX.charAt(charCode & 0xF);
			}
		}
	} // for

	return encoded;
}

function URLDecode(encoded_text) {
   	// Replace + with ' '
   	// Replace %xx with equivalent character
   	// Put [ERROR] in output if %xx is invalid.
   	var HEXCHARS = "0123456789ABCDEFabcdef";
   	var encoded = encoded_text;
   	var plaintext = "";
   	var i = 0;
   	while (i < encoded.length) {
       	var ch = encoded.charAt(i);
	   	if (ch == "+") {
	       	plaintext += " ";
		   	i++;
	   	} else if (ch == "%") {
			if (i < (encoded.length-2)
					&& HEXCHARS.indexOf(encoded.charAt(i+1)) != -1
					&& HEXCHARS.indexOf(encoded.charAt(i+2)) != -1 ) {
				plaintext += unescape( encoded.substr(i,3) );
				i += 3;
			} else {
				alert( 'Bad escape combination near ...' + encoded.substr(i) );
				plaintext += "%[ERROR]";
				i++;
			}
		} else {
		   	plaintext += ch;
		   	i++;
		}
	} // while
	return plaintext;
}

function openNewWin(open_url, name, args) {
	/*
	if (typeof(popupWin) != "object"){
		popupWin = window.open(url, name, args);
	} else {
		if (!popupWin.closed) {
			popupWin.location.href = url;
		} else {
			popupWin = window.open(url, name,args);
		}
	}
	*/
	popupWin = window.open(open_url, name, args);
	popupWin.focus();
}

function DOMCall(name) {
	if (document.getElementById)
    	return document.getElementById(name);
	else if (document.all)
		return document.all[name];
	else if (document.layers)
		return document.layers[name];
}

function cursor_wait() {
  document.body.style.cursor = 'wait';
}

function cursor_clear() {
  document.body.style.cursor = 'default';
}

//newState = true to disable, false to enable
function disableElement(elementIDArray, newState) {
    for (var i=0; i<elementIDArray.length; i++) {
        document.getElementById(elementIDArray[i]).disabled = newState;
    }
}

function changeElementDisplay(elementIDArray, className) {
    for (var i=0; i<elementIDArray.length; i++) {
        document.getElementById(elementIDArray[i]).className = className;
    }
}

function f_scrollTop() {
	return f_filterResults (
		window.pageYOffset ? window.pageYOffset : 0,
		document.documentElement ? document.documentElement.scrollTop : 0,
		document.body ? document.body.scrollTop : 0
	);
}
function f_filterResults(n_win, n_docel, n_body) {
	var n_result = n_win ? n_win : 0;
	if (n_docel && (!n_result || (n_result > n_docel)))
		n_result = n_docel;
	return n_body && (!n_result || (n_result > n_body)) ? n_body : n_result;
}