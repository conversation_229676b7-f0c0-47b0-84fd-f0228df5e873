function addLoadEvent(func) {
	var oldonload = window.onload;
	if (typeof window.onload != 'function') {
    	window.onload = func;
  	} else {
    	window.onload = function() {
      		oldonload();
      		func();
    	}
  	}
}

function prepareInputsForHints() {
	var inputs = document.getElementsByTagName("input");
	for (var input_cnt=0; input_cnt<inputs.length; input_cnt++){
		// test to see if the hint span exists first
		if (inputs[input_cnt].parentNode.getElementsByTagName("div")[0]) {
			// the span exists!  on focus, show the hint
			inputs[input_cnt].onfocus = function () {
				this.parentNode.getElementsByTagName("div")[0].style.display = "inline";
			}
			// when the cursor moves away from the field, hide the hint
			inputs[input_cnt].onblur = function () {
				this.parentNode.getElementsByTagName("div")[0].style.display = "none";
			}
		}
	}
	// repeat the same tests as above for selects
	var selects = document.getElementsByTagName("select");
	for (var k=0; k<selects.length; k++){
		if (selects[k].parentNode.getElementsByTagName("div")[0]) {
			selects[k].onfocus = function () {
				this.parentNode.getElementsByTagName("div")[0].style.display = "inline";
			}
			selects[k].onblur = function () {
				this.parentNode.getElementsByTagName("div")[0].style.display = "none";
			}
		}
	}
}
addLoadEvent(prepareInputsForHints);