<SCRIPT language="JavaScript" type="text/javascript">
<!--

function getPMOptions (pm_sel, opt_div, b_id) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var obj_ref = pm_sel;
	var div_option_obj = DOMCall(opt_div);
	var res;
    var server_action = (b_id != null && b_id != '') ? 'edit_pm_fields' : 'add_pm_fields';
    
    div_option_obj.innerHTML = 'Loading ...';
    obj_ref.disabled = true;
	
	var ref_url = "payment_xmlhttp.php?action="+server_action+"&b_id="+b_id+"&pm_id="+obj_ref.value;
	xmlhttp.open("GET", ref_url); 
    xmlhttp.onreadystatechange = function() { 
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
      		res = xmlhttp.responseText;
      		
      		if (typeof (xmlhttp.responseXML.getElementsByTagName("option_selection")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("option_selection")[0] != null) {
				div_option_obj.innerHTML = xmlhttp.responseXML.getElementsByTagName("option_selection")[0].firstChild.data
			} else {
      			div_option_obj.innerHTML = '';
      		}
      		
      		obj_ref.disabled = false;
      	}
    }
    
    xmlhttp.send(null);
}

function getPMInfo (pb_sel, bal_cur, bal_txt, opt_div) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var obj_ref = pb_sel;
	var bal_obj = DOMCall(bal_txt);
	var div_option_obj = DOMCall(opt_div);
	var res;
    var server_action = 'withdraw_pm_info';
    var js_error = false;
    
    if (obj_ref.value != '') {
	    if (trim_str(bal_obj.value) == '') {
	    	alert('<?=JS_ERROR_NO_WITHDRAW_AMOUNT?>');
	    	js_error = true;
	    } else if (!currencyValidation(trim_str(bal_obj.value))) {
	   		alert('<?=JS_ERROR_INVALID_WITHDRAW_AMOUNT?>');
	   		js_error = true;
	    }
    }
    
    if (js_error) {
    	obj_ref.selectedIndex = 0;
    	return false;
	} else {
	    div_option_obj.innerHTML = 'Loading ...';
	    obj_ref.disabled = true;
		
		var ref_url = "payment_xmlhttp.php?action="+server_action+"&b_id="+obj_ref.value+"&w_cur="+bal_cur+"&w_amt="+bal_obj.value;
		xmlhttp.open("GET", ref_url);
	    xmlhttp.onreadystatechange = function() { 
	      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) { 
	      		res = xmlhttp.responseText;
	      		//alert(res);
	      		
      			if (typeof (xmlhttp.responseXML.getElementsByTagName("withdraw_info")[0]) != "undefined" && xmlhttp.responseXML.getElementsByTagName("withdraw_info")[0] != null) {
					div_option_obj.innerHTML = xmlhttp.responseXML.getElementsByTagName("withdraw_info")[0].firstChild.data
				} else {
	      			div_option_obj.innerHTML = '';
	      		}
	      		
	      		obj_ref.disabled = false;
	      	}
	    }
	    
	    xmlhttp.send(null);
	}
}
//--></SCRIPT>