function hello_world() {
	var server_action = 'hello_world';
	var ref_url = "supplier_xmlhttp.php?action="+server_action;
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		res = xmlhttp.responseText;
			alert(res);
      	}
    }

    xmlhttp.send(null);
}

function onQuickBuybackGameSelection(game_cat_id, languages_id) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var server_action = 'get_backorder_buyback_server_list';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_cat_id="+game_cat_id+"&slang="+languages_id;
    var wbb_div_game_cat_id = DOMCall('wbb_div_game_cat_id');
    var wbb_div_server_listing = DOMCall('wbb_div_server_listing');
	var div_game_reopen_count_down_obj = DOMCall('div_game_reopen_count_down');
	document.getElementById('dyn_game_product_unit_name').innerHTML = '';
    if (game_cat_id == 0) {
		wbb_div_server_listing.innerHTML = '';
		return;
	}

	if (game_cat_id != wbb_div_game_cat_id.value) {
		wbb_div_game_cat_id.value = game_cat_id;
	}
   	
   	wbb_div_server_listing.className = "loadingImg";
   	wbb_div_server_listing.innerHTML = '<span class="title-text">&nbsp;&nbsp;&nbsp;&nbsp;'+global_loading_message+'</span>';
	div_game_reopen_count_down_obj.innerHTML = '';
	
	xmlhttp.open("GET", ref_url);
	xmlhttp.onreadystatechange = function() {
		if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
            //No servers, etc
            //alert(xmlhttp.responseText);
            var msg_id = 0;
			var error_msg_obj = xmlhttp.responseXML.getElementsByTagName('error')[0];
      		if (typeof (error_msg_obj) != 'undefined' && error_msg_obj != null) {
      			var msg_id = parseInt(error_msg_obj.firstChild.data);
      		}
			
      	    if (msg_id > 0) {
				wbb_div_server_listing.innerHTML = '';
      	    } else {
      	    	var game = xmlhttp.responseXML.getElementsByTagName('game')[0];
      	    	if (typeof (game) != 'undefined' && game != null) {
      	    		var list_status_msg = typeof (game.getElementsByTagName("list_status_msg")[0]) != "undefined" && game.getElementsByTagName("list_status_msg")[0] != null ? game.getElementsByTagName("list_status_msg")[0].firstChild.data : '';
      	    		div_game_reopen_count_down_obj.innerHTML = list_status_msg;
	      		}
	      		
				var server_arr = xmlhttp.responseXML.getElementsByTagName('server');
				if (server_arr.length > 0) {
					wbb_div_server_listing.innerHTML = '';
					divhtml = '';
	      		    for (var j=0; j<server_arr.length; j++) {
	          		    var products_id = server_arr[j].getElementsByTagName('id')[0].firstChild.nodeValue;
	          		    var products_name = server_arr[j].getElementsByTagName('name')[0].firstChild.nodeValue;
	          		    var products_unit_price = server_arr[j].getElementsByTagName('unit_price')[0].firstChild.nodeValue;
	          		    var min_qty = server_arr[j].getElementsByTagName('min_qty')[0].firstChild.nodeValue;
	          		    var max_qty = server_arr[j].getElementsByTagName('max_qty')[0].firstChild.nodeValue;
	          		    var products_is_buyback_int = server_arr[j].getElementsByTagName('is_buyback')[0].firstChild.nodeValue;
          		    	document.getElementById('dyn_game_product_unit_name').innerHTML =  server_arr[j].getElementsByTagName('qty_unit')[0].firstChild.nodeValue;
	          		    if (products_is_buyback_int == '1') {
	          		    	var products_is_buyback_bool = true;
	          		    } else {
	          		    	var products_is_buyback_bool = false;
	          		    }
						//update the div
						add_server_listing_row(products_name, products_unit_price, products_is_buyback_bool ? min_qty+'-'+max_qty : '&nbsp;', game_cat_id, products_id, products_is_buyback_int);
	    	        }
	    	        show_server_listing_div();
					disableDivInputs(false);
				} else {
					wbb_div_server_listing.innerHTML = '<span class="title-text">No results found</span>';
				}
      		}
      		
      		wbb_div_server_listing.className = "";
      	}
    }
    xmlhttp.send(null);
}

function onBuybackGameSelection(gameListObj, languages_id, server_product_id_autoselect, SID) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var game_cat_id = gameListObj.value;
	var server_action = 'get_buyback_server_list';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_cat_id="+game_cat_id+"&slang="+languages_id+"&buyback_products_id="+server_product_id_autoselect+"&SID="+SID;
	
   	document.getElementById('wbb_div_msgField1').innerHTML = global_loading_message;
	
	var dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select');
	disableElement(dependantInputs, true);
	
    var dependantElements = new Array('wbb_div_add_shortcut', 'wbb_tbody_error', 'wbb_tbody_notice', 'wbb_tbody_step2');
    changeElementDisplay(dependantElements, 'hide');
	
    var server_selection = DOMCall('wbb_input_product_select');
    var wbb_div_game_cat_id = DOMCall('wbb_div_game_cat_id');
    var wbb_div_server_listing = DOMCall('wbb_div_server_listing');
	var div_game_reopen_count_down_obj = DOMCall('div_game_reopen_count_down');
	
	var gameListInput = new Array('wbb_input_game_select');
	var serverListInput = new Array('wbb_input_product_select');
	document.getElementById('wbb_div_table_heading_product_unit_name').innerHTML = "";	
    if (game_cat_id == 0) {
		//clear server select list
		clearOptionList(server_selection);
		appendToSelect(server_selection, '0', gameListObj[gameListObj.selectedIndex].text);
		disableElement(serverListInput, true);
		disableElement(gameListInput, false);
		document.getElementById('wbb_div_msgField1').innerHTML = '';
		wbb_div_server_listing.innerHTML = '<span class="title-text">'+global_check_price_message+'</span>';
		wbb_div_server_listing.className = "";
		return;
	}
	
	wbb_div_server_listing.className = "loadingImg";
	wbb_div_server_listing.innerHTML = '<span class="title-text">&nbsp;&nbsp;&nbsp;&nbsp;'+global_loading_message+'</span>';
	div_game_reopen_count_down_obj.innerHTML = '';
	
	xmlhttp.open("GET", ref_url);
	xmlhttp.onreadystatechange = function() {
		if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      	   	//alert(xmlhttp.responseText);
     		clearOptionList(server_selection);
			
            //No servers, etc
            var msg_id = 0;
			var error_msg_obj = xmlhttp.responseXML.getElementsByTagName('error')[0];
      		if (typeof (error_msg_obj) != 'undefined' && error_msg_obj != null) {
      			var msg_id = parseInt(error_msg_obj.firstChild.data);
      		}
			
			var append_server_div = true;
			wbb_div_server_listing.innerHTML = '';
			//check if we need to bother updating the server list div
			if (game_cat_id != wbb_div_game_cat_id.value) {
				wbb_div_game_cat_id.value = game_cat_id;
			}

      	    if (msg_id > 0) {
          	    showError(msg_id);
				disableElement(serverListInput, true);
				disableElement(gameListInput, false);
				wbb_div_server_listing.innerHTML = '';
      	    } else {
      	    	var game = xmlhttp.responseXML.getElementsByTagName('game')[0];
      	    	if (typeof (game) != 'undefined' && game != null) {
      	    		var list_status_msg = typeof (game.getElementsByTagName("list_status_msg")[0]) != "undefined" && game.getElementsByTagName("list_status_msg")[0] != null ? game.getElementsByTagName("list_status_msg")[0].firstChild.data : '';
      	    		div_game_reopen_count_down_obj.innerHTML = list_status_msg;
	      		}

				var server_arr = xmlhttp.responseXML.getElementsByTagName('server');
				if (server_arr.length == 0) {
					var dependantInputs = new Array('wbb_input_game_select');
					disableElement(dependantInputs, false);
				} else {
					divhtml = '';
					document.getElementById('wbb_div_table_heading_product_unit_name').innerHTML = server_arr[0].getElementsByTagName('qty_unit')[0].firstChild.nodeValue;
	      		    for (var j=0; j<server_arr.length; j++) {
	          		    var products_id = server_arr[j].getElementsByTagName('id')[0].firstChild.nodeValue;
	          		    var products_name = server_arr[j].getElementsByTagName('name')[0].firstChild.nodeValue;
	          		    var products_unit_price = server_arr[j].getElementsByTagName('unit_price')[0].firstChild.nodeValue;
	          		    var min_qty = server_arr[j].getElementsByTagName('min_qty')[0].firstChild.nodeValue;
						var max_qty = server_arr[j].getElementsByTagName('max_qty')[0].firstChild.nodeValue;
	          		    var products_is_buyback_int = server_arr[j].getElementsByTagName('is_buyback')[0].firstChild.nodeValue;
	          		    
	          		    if (products_is_buyback_int == '1') {
	          		    	var products_is_buyback_bool = true;
	          		    } else {
	          		    	var products_is_buyback_bool = false;
	          		    }
	          		    
	          		    //update the dropdown
	                    appendToSelect(server_selection, products_id, products_name);
	                    
						//update the div
						add_server_listing_row(products_name, products_unit_price, products_is_buyback_bool ? min_qty+'-'+max_qty : '&nbsp;', game_cat_id, products_id, products_is_buyback_int);
	    	        }
	    	        show_server_listing_div();
	    	        var dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select');
	    	        disableElement(dependantInputs, false);
				}
      		}
        	document.getElementById('wbb_div_msgField1').innerHTML = '';
        	
        	if (parseInt(server_product_id_autoselect) > 0) {
				server_selection.value = server_product_id_autoselect;
        	} else if (server_arr.length > 0) {
				//If no server to preselect and the game has several servers, get the first one.
				server_selection.value = server_arr[0].getElementsByTagName('id')[0].firstChild.nodeValue;
			}
			
			show_product_buyback_info(xmlhttp.responseXML);	// Call another function handle the rest of the result
      	}
    }
    if (xmlhttp != null)	xmlhttp.send(null);
}

function show_product_buyback_info(responseXMLInfo) {
	var bo_exact_qty_msg = DOMCall('span_match_bo_exact_qty_msg');
	
	var selected_product_info = responseXMLInfo.getElementsByTagName("selected_product")[0];
	
	document.getElementById('wbb_span_product_unit_name').innerHTML = '';
	document.getElementById('wbb_div_msgField2').innerHTML = global_loading_message;
	
	if (typeof (selected_product_info) != 'undefined' && selected_product_info != null) {
		var dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select', 'wbb_input_qty', 'wbb_button_confirm');
		disableElement(dependantInputs, true);
		
		var dependantElements = new Array('wbb_div_add_shortcut', 'wbb_tbody_error', 'wbb_tbody_notice', 'wbb_tbody_step2');
	    changeElementDisplay(dependantElements, 'hide');
		
		var errorMsg = '';
		
		var msg_id = 0;
		var notice_msg_obj = selected_product_info.getElementsByTagName('notice')[0];
		if (typeof (notice_msg_obj) != 'undefined' && notice_msg_obj != null) {
			var msg_id = parseInt(notice_msg_obj.firstChild.data);
		}
	    if (msg_id > 0) {
	  	    showNotice(msg_id);
	    }
		
	    var msg_id = 0;
		var error_msg_obj = selected_product_info.getElementsByTagName('error')[0];
		if (typeof (error_msg_obj) != 'undefined' && error_msg_obj != null) {
			var msg_id = parseInt(error_msg_obj.firstChild.data);
		}
		
	    if (msg_id > 0) {
	  	    showError(msg_id);
	        dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select');
	        disableElement(dependantInputs, false);
	    } else {
	        var unit_price = selected_product_info.getElementsByTagName('unit_price')[0].firstChild.data;
	        var unit_price_display = selected_product_info.getElementsByTagName('unit_price_display')[0].firstChild.data;
	        
	        document.getElementById('wbb_span_unit_price').innerHTML = unit_price_display;
	        document.getElementById('wbb_hidden_unit_price').value = unit_price;
			
	        document.getElementById('wbb_span_max_value').innerHTML = selected_product_info.getElementsByTagName('max')[0].firstChild.data;
	        document.getElementById('wbb_span_product_name').innerHTML = selected_product_info.getElementsByTagName('product_name')[0].firstChild.data;
	        document.getElementById('wbb_hidden_product_name').value = selected_product_info.getElementsByTagName('product_name')[0].firstChild.data;
	        document.getElementById('wbb_span_min_value').innerHTML = selected_product_info.getElementsByTagName('min')[0].firstChild.data;
	        document.getElementById('wbb_hidden_min_value').value = selected_product_info.getElementsByTagName('min')[0].firstChild.data;
	        document.getElementById('wbb_hidden_products_id').value = selected_product_info.getElementsByTagName('product_id')[0].firstChild.data;
	        document.getElementById('wbb_span_product_unit_name').innerHTML = selected_product_info.getElementsByTagName('product_unit_name')[0].firstChild.data;
	        
	        if (bo_exact_qty_msg != null) {
		        if (typeof (selected_product_info.getElementsByTagName('bo_exact_qty_msg')[0]) != 'undefined' && selected_product_info.getElementsByTagName('bo_exact_qty_msg')[0] != null) {
		        	bo_exact_qty_msg.innerHTML = selected_product_info.getElementsByTagName('bo_exact_qty_msg')[0].firstChild.data;
		        } else {
		        	bo_exact_qty_msg.innerHTML = '';
		        }
	        }
	        
	        document.getElementById('wbb_tbody_step2').className = 'show';
	        document.getElementById('wbb_div_add_shortcut').className = 'generalTextShow';
	        disableElement(dependantInputs, false);
	    }
	    document.getElementById('wbb_div_msgField2').innerHTML = '';
	}
}

function onBuybackServerSelection(serverListObj, languages_id) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var server_action = 'get_buyback_product_info';
	var parent_cat_id = DOMCall('wbb_input_game_select').value;
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_products_id="+serverListObj.value+"&buyback_parent_cat_id="+parent_cat_id+"&slang="+languages_id;
	
	document.getElementById('wbb_div_msgField2').innerHTML = global_loading_message;
	
	var dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select', 'wbb_input_qty', 'wbb_button_confirm');
	disableElement(dependantInputs, true);
    
    var dependantElements = new Array('wbb_div_add_shortcut', 'wbb_tbody_error', 'wbb_tbody_notice', 'wbb_tbody_step2');
    changeElementDisplay(dependantElements, 'hide');
	
	var errorMsg = '';
	
    if (serverListObj.value > 0) {
    	xmlhttp.open("GET", ref_url);
    	xmlhttp.onreadystatechange = function() {
          	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
          		show_product_buyback_info(xmlhttp.responseXML);	// Call another function handle the rest of the result
          	}
        }
        xmlhttp.send(null);
    } else {
        dependantInputs = new Array('wbb_input_game_select', 'wbb_input_product_select');
        disableElement(dependantInputs, false);
        document.getElementById('wbb_div_msgField2').innerHTML = '';
    }
}

function onOrderHistoryRefresh(order_status_id, product_type, order_no, game_cat_id, start_date, end_date, languages_id, SID) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var server_action = 'search_order_history';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&order_status_id="+order_status_id+"&product_type="+product_type+"&order_no="+URLEncode(order_no)+"&game_cat_id="+URLEncode(game_cat_id)+"&start_date="+URLEncode(start_date)+"&end_date="+URLEncode(end_date);
	
	var odh_div_search_results = DOMCall('odh_div_search_results');
	
	odh_div_search_results.innerHTML = '<br><p class="title-text" align="center">'+global_loading_message+'<\/p>';
	
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
			//alert(res);
			
			document.getElementById('num_results').innerHTML = xmlhttp.responseXML.getElementsByTagName('num_results')[0].firstChild.data;
			
			var row_arr = xmlhttp.responseXML.getElementsByTagName('row');
			if (row_arr.length > 0) {
				odh_div_search_results.innerHTML = '';
				divhtml = '';
      		    for (var j=0; j<row_arr.length; j++) {
	      		    var req_grp_id = row_arr[j].getElementsByTagName('req_grp_id')[0].firstChild.nodeValue;
	      		    var req_id = row_arr[j].getElementsByTagName('req_id')[0].firstChild.nodeValue;
					var show_restock = trim_str(row_arr[j].getElementsByTagName('show_restock')[0].firstChild.nodeValue);
					var restock_character = trim_str(row_arr[j].getElementsByTagName('restock_character')[0].firstChild.nodeValue);
					var confirmed_qty = trim_str(row_arr[j].getElementsByTagName('confirmed_qty')[0].firstChild.nodeValue);
					var game_name = row_arr[j].getElementsByTagName('game_name')[0].firstChild.nodeValue;
					var server_name = row_arr[j].getElementsByTagName('server_name')[0].firstChild.nodeValue;
					var req_qty = row_arr[j].getElementsByTagName('req_qty')[0].firstChild.nodeValue;
					var amount = row_arr[j].getElementsByTagName('amount')[0].firstChild.nodeValue;
					var status_name = row_arr[j].getElementsByTagName('status_name')[0].firstChild.nodeValue;
					var show_expiry = row_arr[j].getElementsByTagName('show_expiry')[0].firstChild.nodeValue;
					var expiry_time = row_arr[j].getElementsByTagName('expiry_time')[0].firstChild.nodeValue;
					
					//update the div
					add_search_results_row(row_arr[j], order_status_id);
    	        }
    	        
    	        show_search_results_div();
    	        toggleCountdown(1);
				disableDivInputs(false);
			} else {
				odh_div_search_results.innerHTML = '<span class="title-text">No results found</span>';
			}
      	}
    }
    xmlhttp.send(null);
}

function show_order_report(buyback_req_grp_id, languages_id, SID) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	document.getElementById('wbb_div_msgField_'+buyback_req_grp_id).innerHTML = global_loading_message;
	var server_action = 'show_order_report';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_req_grp_id="+buyback_req_grp_id+"&slang="+languages_id+"&SID="+SID;
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		//res = xmlhttp.responseText;
			//alert(res);

			document.getElementById('wbb_show_game_name').innerHTML = xmlhttp.responseXML.getElementsByTagName('game_name')[0].firstChild.data;
			document.getElementById('wbb_show_server_name').innerHTML = xmlhttp.responseXML.getElementsByTagName('server_name')[0].firstChild.data;
			document.getElementById('wbb_show_request_quantity').innerHTML = xmlhttp.responseXML.getElementsByTagName('req_qty')[0].firstChild.data;
			document.getElementById('wbb_show_uom').innerHTML = xmlhttp.responseXML.getElementsByTagName('uom')[0].firstChild.data;
			document.getElementById('wbb_show_product_name').innerHTML = xmlhttp.responseXML.getElementsByTagName('product_name')[0].firstChild.data;
			document.getElementById('wbb_show_total_price').innerHTML = xmlhttp.responseXML.getElementsByTagName('total_price')[0].firstChild.data;
			document.getElementById('wbb_show_sender_character').innerHTML = xmlhttp.responseXML.getElementsByTagName('sender_character')[0].firstChild.data;
			document.getElementById('wbb_show_restk_character').innerHTML = xmlhttp.responseXML.getElementsByTagName('restk_character')[0].firstChild.data;
			document.getElementById('wbb_show_delivery_time').innerHTML = xmlhttp.responseXML.getElementsByTagName('delivery_time')[0].firstChild.data;
			document.getElementById('wbb_show_contact_name').innerHTML = xmlhttp.responseXML.getElementsByTagName('contact_name')[0].firstChild.data;
			document.getElementById('wbb_show_contact_no').innerHTML = xmlhttp.responseXML.getElementsByTagName('contact_no')[0].firstChild.data;
			document.getElementById('wbb_show_comments').innerHTML = xmlhttp.responseXML.getElementsByTagName('show_comments')[0].firstChild.data;
			document.getElementById('wbb_show_order_reference').innerHTML = xmlhttp.responseXML.getElementsByTagName('order_reference')[0].firstChild.data;

			//this are parameters for the popup div
			var w, h, l, t;
			w = 700;
			h = 500;
//			l = 0;
//			t = 0;
			l = screen.width/4;
			t = screen.height/4;
			displayFloatingDiv('order_report', 'Order Report', w, h, l, t);
			//order_report_content is preset in tpl as height=1px and width=1px so blank zone not apparent when hidden
			//so reset the height and width here
		    document.getElementById('order_report_content').style.width = w + 'px';
		    document.getElementById('order_report_content').style.height = h + 'px';

            document.getElementById('wbb_div_msgField_'+buyback_req_grp_id).innerHTML = '';
      	}
    }

    xmlhttp.send(null);
}

function show_payment_report(payment_id) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	document.getElementById('wbb_div_msgField_'+payment_id).innerHTML = 'Loading...';
	
	var server_action = 'show_payment_report';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&payment_id="+payment_id;
    
	xmlhttp.open("GET", ref_url);
    xmlhttp.onreadystatechange = function() {
      	if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      		res = xmlhttp.responseText;
      		
        	new popUp(300, f_scrollTop()+200, 600, 500, payment_id, res, "white", "#00385c", "9pt sans-serif", '&#20184;&#27454;&#25253;&#21578; - '+ payment_id, "#00385c", "white", "lightgrey", "#00568c", "black", true, false, false, true, false, false, '', '', 'cir_close.gif', 'resize.gif');
        	
			document.getElementById('wbb_div_msgField_'+payment_id).innerHTML = '';
      	}
    }
    
    xmlhttp.send(null);
}

function onFavLinksGameSelection(gameListObj, languages_id, server_product_id_autoselect) {
	if (xmlhttp.readyState > 0 && xmlhttp.readyState < 4) return ;
	
	var game_cat_id = gameListObj.value;
	var server_action = 'get_buyback_server_list';
	var ref_url = "supplier_xmlhttp.php?action="+server_action+"&buyback_cat_id="+game_cat_id+"&slang="+languages_id;

   	document.getElementById('fvl_div_msgField1').innerHTML = global_loading_message;

	var dependantInputs = new Array('fvl_input_game_select', 'fvl_input_product_select');
	disableElement(dependantInputs, true);

    var dependantElements = new Array('fvl_tbody_step2', 'fvl_tbody_error', 'fvl_tbody_notice');
    changeElementDisplay(dependantElements, 'hide');

    var server_selection = DOMCall('fvl_input_product_select');
    var fvl_div_game_cat_id = DOMCall('fvl_div_game_cat_id');

	var serverListInput = new Array('fvl_input_product_select');
	var gameListInput = new Array('fvl_input_game_select');

    if (game_cat_id == 0) {
		//clear server select list
		clearOptionList(server_selection);
        appendToSelect(server_selection, '0', '--');
		disableElement(serverListInput, true);
		disableElement(gameListInput, false);
		document.getElementById('fvl_div_msgField1').innerHTML = '';
		return;
	}

	xmlhttp.open("GET", ref_url);
	xmlhttp.onreadystatechange = function() {
		if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
      	    //alert(xmlhttp.responseText);
			
     		clearOptionList(server_selection);
			
            //No servers, etc
            var msg_id = 0;
			var error_msg_obj = xmlhttp.responseXML.getElementsByTagName('error')[0];
      		if (typeof (error_msg_obj) != 'undefined' && error_msg_obj != null) {
      			var msg_id = parseInt(error_msg_obj.firstChild.data);
      		}
			
			//check if we need to bother updating the server list div
			if (game_cat_id != fvl_div_game_cat_id.value) {
				fvl_div_game_cat_id.value = game_cat_id;
			}
      	    
      	    if (msg_id > 0) {
          	    showError(msg_id);
				disableElement(serverListInput, true);
                appendToSelect(server_selection, '0', '--');
				disableElement(gameListInput, false);
      	    } else {
				var server_arr = xmlhttp.responseXML.getElementsByTagName('server');
      		    for (var j=0; j<server_arr.length; j++) {
          		    var scat_id = server_arr[j].getElementsByTagName('id')[0].firstChild.nodeValue;
          		    var scat_name = server_arr[j].getElementsByTagName('name')[0].firstChild.nodeValue;
          		    //update the dropdown
                    appendToSelect(server_selection, scat_id, scat_name);
    	        }
    	        disableElement(dependantInputs, false);
    	        document.getElementById('fvl_tbody_step2').className = 'show';
      		}
      		
        	document.getElementById('fvl_div_msgField1').innerHTML = '';
        	
        	if (server_product_id_autoselect > 0) {
				server_selection.value = server_product_id_autoselect;
				onBuybackServerSelection(server_selection, languages_id);
        	}
      	}
    }
    xmlhttp.send(null);
}