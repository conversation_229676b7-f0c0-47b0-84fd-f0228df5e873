<?php
define('TEXT_CHANGE_PASSWORD', '更换密码');
define('TEXT_INVALID_PIN_NUMBER', '不正确 ' . TEXT_PIN_NUMBER);
define('TEXT_INVALID_CURRENT_PASSWORD', '不正确 ' . TEXT_CURRENT_PASSWORD);
define('TEXT_UPDATE_FAILED', '更新失败');
define('TEXT_UPDATE_SUCCESS', '更新成功');
 
define('TEXT_SELECT_SECURITY_QUESTION', '请选择提示问题');
define('TEXT_QUESTION_AND_ANSWER_IS_ALREADY_SET', '已设定密保');
define('TEXT_QUESTION_AND_ANSWER_IS_SET', '机密问题已设定');
 
define('TEXT_QUESTION_AND_ANSWER_IS_NOT_YET_SET', '抱歉，机密问题还没设定，请联系网站客服谢谢');
define('TEXT_QUESTION_AND_ANSWER_IS_RESET', '之前的机密问题已删除，麻烦重设机密问题');
define('TEXT_EXCEED_MAX_ATTEMPTED', '抱歉，您的尝试已到限定次数，请联系网站客服谢谢');
define('TEXT_SHOW_TRIED_ATTEMPTED', '您还可以尝试输入%d次');
define('TEXT_MANDATORY_ISBLANK', '资料不完整: %s');

define('TEXT_HELP_CURRENT_PASSWORD', '请输入您目前密码 ');
 
define('TEXT_HELP_SECURITY_QUESTION', '请选择任意一个问题');
define('TEXT_HELP_SECURITY_ANSWER', '请输入您要设定的答案');

// Wei Chen
define('ENTRY_FORM_PM_SELECT_PM', '付款方式:');
define('ENTRY_FORM_PM_ALIAS', '账号别名（例如：工商-上海）');
 

define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', '必要资料:');

define('TABLE_HEADING_PM_PAYMENT_ACCOUNT', '付款账号');

define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', '目前还没有付款账号的记录.');

define('LINK_PM_EDIT_PM_ACCOUNT', '更改付款账号');
define('LINK_PM_DELETE_PM_ACCOUNT', '删除付款账号');

define('SUCCESS_PM_DELETE_PM_ACCOUNT', '成功: 付款账号已经成功删除.');
 
define('SUCCESS_PM_EDIT_PM_ACCOUNT', '成功: 付款账号已经成功更改.');
define('SUCCESS_PM_ADD_NEW_PM_ACCOUNT', '成功: 新的付款账号已经增加.');


define('ERROR_INVALID_PM_BOOK_SELECTION', '错误: 您正在填写的付款账号有错误.');
define('ERROR_INVALID_PM_SELECTION', '错误: 请选择正确的付款方式.');
define('ERROR_EMPTY_PM_ALIAS', '错误: 付款项目必须填写.');
define('ERROR_PM_FIELD_INFO_REQUIRED', '错误: 请填写 "%s" 资料!');
define('ERROR_PM_ACCOUNT_BOOK_FULL', '您的付款账号已经满了。请删除不需要的帐号以重新储存一个新的.');
define('ERROR_INVALID_PIN', 'PIN 代码无效，更新失败');

define('JS_ERROR_NO_WITHDRAW_AMOUNT', '请填入您要提款的数额');
define('JS_ERROR_INVALID_WITHDRAW_AMOUNT', '无效的提款数额');
 

define('TEXT_SECURITY_QUESTION_AND_ANSWER_USAGE', '您知道机密问题有什么用吗？当此帐号修改个人资料、密码、银行帐号时会要求您回答机密问题，这将确保是您本人在进行修改操作，使您的帐号更安全。');
define('TEXT_PLEASE_FILL_IN_SERIOUSLY', '所以请务必认真填写');

define('TEXT_REQUIRE_CORRECT_ANSWER', '需要真确的答案才能更新 : %s');

?>