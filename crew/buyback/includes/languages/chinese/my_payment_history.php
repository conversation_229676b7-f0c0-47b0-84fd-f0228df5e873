<?
/*
  $Id: my_payment_history.php,v 1.6 2008/11/03 10:48:00 wilson.sun Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('HEADING_TITLE', HEADER_TITLE_MY_PAYMENT_HISTORY);
define('NAVBAR_TITLE', HEADER_TITLE_MY_PAYMENT_HISTORY);

define('TEXT_PAYMENT_ID', '付款编号');
define('TEXT_PAYMENT_METHOD', '付款方式');
define('TEXT_DATE_REQUESTED', '提款日期');
define('TEXT_RESULTS_FOUND', '搜获的记录');
define('TEXT_AMOUNT_PAID', '已付数额');
define('TEXT_REMAINING', '余额');
define('TEXT_DATE', '日期');
define('TEXT_TIME', '时间');
define('TEXT_ACCOUNT_FREEZE_PLEASE_CONTACT_CS', '抱歉，你的帐号已被冻结，请联系网站客服谢谢');
define('TEXT_ACCOUNT', '帐号');
define('TEXT_WITHDRAW_AMT', '提款数额');
define('TEXT_CLOSE', '关闭');
define('TEXT_INVALID_AMOUNT', '数额不正确.');
define('TEXT_CANCEL', '删除');
define('TEXT_OK', 'OK');
define('TEXT_CONFIRM_WITHDRAWAL', '确认提款？');
define('TEXT_REQUEST_SUCCESSFULL', '已成功提款');

define('ENTRY_WITHDRAW_CURRENT_BALANCE', '目前余额:');
define('ENTRY_WITHDRAW_RESERVE_AMOUNT', '保留:');
define('ENTRY_WITHDRAW_AVAILABLE_BALANCE', '可提款数额:');
define('ENTRY_WITHDRAW_AMOUNT', '提款数额:');
define('ENTRY_WITHDRAW_PAYMENT_ACCOUNT', '结款致:');
define('ENTRY_WITHDRAW_FINAL_AMOUNT', '可接收数额:');

define('ENTRY_WITHDRAW_PAYMENT_METHOD', '付款方式:');
define('ENTRY_WITHDRAW_MIN_AMT', '&nbsp;- 最低数额:');
define('ENTRY_WITHDRAW_MAX_AMT', '&nbsp;- 最高数额:');
define('ENTRY_WITHDRAW_FEES', '&nbsp;- 结款费:');

//For payment module------------------------------------------------------------
define('HEADER_FORM_ACC_STAT_TITLE', '帐户结存');
define('ENTRY_ORDER_START_DATE', '从 日期<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_END_DATE', '至 日期<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', '付款编号');
define('ENTRY_ORDER_ID', '单子编号');
define('ENTRY_RECORDS_PER_PAGE', '显示一页记录');
define('ENTRY_PAYMENT_STATUS', '付款状态');
define('TEXT_LANG_RECORDS_PER_PAGE', '');
define('TABLE_HEADING_ACC_STAT_DATETIME', '日期/时间');
define('TABLE_HEADING_ACC_STAT_ACTIVITY', '活动项目');
define('TABLE_HEADING_ACC_STAT_PAYMENT_STATUS', '付款状态');
define('TABLE_HEADING_ACC_STAT_DEBIT', '支入');
define('TABLE_HEADING_ACC_STAT_CREDIT', '支出');
define('TABLE_HEADING_ACC_STAT_BALANCE', '余额');
define('TEXT_ACC_STAT_NOT_APPLICABLE', '-');
define('TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE', '%s on %s');
define('TEXT_ACC_STAT_ADMIN_COMMENT', '注解:');
define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', '请填写资料:');
define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', '暂时还没有任何付款帐号的记录。');
define('LINK_PM_EDIT_PM_ACCOUNT', '填入付款帐号');
define('LINK_PM_DELETE_PM_ACCOUNT', '删除付款帐号');
define('SUCCESS_PM_DELETE_PM_ACCOUNT', '成功: 付款帐号已经成功删除。');
define('ERROR_INVALID_PM_BOOK_SELECTION', '错误: 您填入的付款帐号无效。');
define('ERROR_INVALID_PM_SELECTION', '错误: 请选择有效的付款方式。');
define('ERROR_EMPTY_PM_ALIAS', '错误: 付款项目必须填写。');
define('ERROR_PM_FIELD_INFO_REQUIRED', '错误: 请务必填写 "%s" 资料！');
define('ERROR_PM_ACCOUNT_BOOK_FULL', '您的付款帐号书已经满了。请删除不需要的帐号以储存新的帐号。');
//--end for payment module--------------------------------------------------------
define('COMMENT_WITHDRAW_FUNDS_REQUESTED', '要求款额结款致 %s(%s)');

define('TEXT_NO_WITHDRAW_LIMIT', '任何');
define('TEXT_WITHDRAW_FEES_FREE', '免费');

define('LINK_WITHDRAW_ACCOUNT_STATEMENT', '[<a href="%s">帐户结存</a>]');
define('LINK_PM_ACC_BOOK', '[<a href="%s">付款帐号书</a>]');

define('ERROR_WITHDRAW_NO_CURRENCY_ACCOUNT_INFO', '请选择钱币帐号来提款。');
define('ERROR_WITHDRAW_NO_AVAILABLE_FUNDS', '您目前没有余额结款，或是您的结款数额已超过了可利用资金。');
define('ERROR_WITHDRAW_NO_PAYMENT_ACCOUNT_BOOK', '请选择接收结款的帐号。');
define('ERROR_WITHDRAW_NO_WITHDRAW_AMOUNT', '请输入结款数额。');
define('ERROR_WITHDRAW_NOT_SUCCESS', '您输入的结款数额不被接收。');
define('ERROR_WITHDRAW_DISABLE_WITHDRAWAL', '您的提款功能失效，您的户口现在无法正常提款，我们已经冻结了您的帐户，具体原因请联系我们的在线客服');
define('WARNING_WITHDRAW_FEES_HIGHER_THAN_WITHDRAW_AMOUNT', '结款费高于结款数额。');
define('WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '%s 不接受这个结款数额。');
define('SUCCESS_WITHDRAW_SUCCESS', '您已经成功提款。');

define('CONTENTS_WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '<span class="redIndicator">%s 不接受这个结款数额</span>');

define('JS_ERROR_NO_WITHDRAW_AMOUNT', '请输入您的结款数额');
define('JS_ERROR_INVALID_WITHDRAW_AMOUNT', '结款数额无效');
?>