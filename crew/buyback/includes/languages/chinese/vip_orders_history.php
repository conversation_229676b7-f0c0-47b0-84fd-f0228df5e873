<?php

define('HEADING_TITLE', HEADER_TITLE_MY_VIP_ORDERS_HISTORY);
define('NAVBAR_TITLE', HEADER_TITLE_MY_VIP_ORDERS_HISTORY);

define('TABLE_HEADING_CONFIRM_QUANTITY', '已发数量');
define('TABLE_HEADING_CUSTOMERS_CODE', '收货口令');
define('TABLE_HEADING_ORDER_QUANTITY', '订单数量');
define('TABLE_HEADING_ORDER_AWAITING_ACCEPT_QUANTITY', '分配数额');
define('TABLE_HEADING_RECEIVER_CHAR_NAME', '接货ID');
define('TABLE_HEADING_SENDERS_CHARACTER_ID', '发货ID');
define('TABLE_HEADING_SERVER_NAME', '服务器');
define('TABLE_HEADING_SUPPLIERS_CODE', '发货口令');
define('TABLE_HEADING_SUBMIT_QUANTITY', '提交数量');
define('TABLE_HEADING_PRICE_PER_UNIT', '单价');

define('BUTTON_TRADE_WITH_US', '与网站交易');
define('BUTTON_TRADE_CUSTOMERS', '与客户交易');
define('BUTTON_TRADE_CANCEL', '取消');
define('ENTRY_RECEIVER_CHAR_NAME', '接货ID');

define('TEXT_ALL_GAMES', '* 所有游戏 *');
define('TEXT_ALL_ORDER_STATUS', '* 所有单子状态 *');
 
 
define('TEXT_ADDITIONAL_COMMENTS', '另外的评论');
define('TEXT_CHARACTER_NAME_IN_PREPARATION', '<span class="redIndicator">正在准备中。。。</span>');
define('TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE', '<span class="redIndicator">请稍等, 还有%s位就到您</span>');
 
define('TEXT_CHARACTER_NAME', '发送者ID');
define('TEXT_COUNTDOWN', '倒数:');
 
define('TEXT_CONTACT_NAME', '姓名'); 
define('TEXT_CONTACT_NO', '联络号码');
define('TEXT_DELIVERY_TIME', '发送时间');
define('TEXT_EXPIRES', '单子取消时间:');
 
 
define('TEXT_ERROR_MSG', '错误信息');
define('TEXT_ERROR_INVALID_QTY', '数额错误。 请重试。');
define('TEXT_ERROR_INVALID_CUSTOMERS_CODE', '收货口令错误。 请重试。');
define('TEXT_ERROR_NO_SS_AFTER_UPLOADED', '没发货后截图。 请重试。');
define('TEXT_ERROR_NO_SS_BEFORE_UPLOADED', '没发货前截图。 请重试。');
define('TEXT_ERROR_SS_AFTER_SIZE_EXCEED_LIMIT', '发货后截图超过 %sKB。 请重试。');
define('TEXT_ERROR_SS_BEFORE_SIZE_EXCEED_LIMIT', '发货前截图超过  %sKB。 请重试。');
define('TEXT_ERROR_SS_AFTER_EXTENSION_WRONG', '发货后截图文档错误。 请重试。');
define('TEXT_ERROR_SS_BEFORE_EXTENSION_WRONG', '发货前截图文档错误。 请重试。');
define('TEXT_ERROR_GET_DATA_FAILED', '寻找资料失败');
define('TEXT_FILE_SIZE', '(体积: %d KB)');
define('TEXT_GAME_CURRENCY', '游戏金币');
 
define('TEXT_MATCH_BACKORDER_EXACT_AMOUNT', '（若出货高于%d，只可直接提交%d，%d-%d之间本站不予接受）');
 
define('TEXT_MATCH_BACKORDER_AMOUNT_ONLY', '(只可直接提交%d)');
define('TEXT_MINUTES', '分钟');
 
 
define('TEXT_NO_RECORD_FOUND', '没相关资料');
define('TEXT_OF', ' X ');
define('TEXT_ORDERS_FOUND', '搜索的纪录记录 ');
define('TEXT_ORDER_REFERENCE', '单子参考');
define('TEXT_PRODUCT_TYPE', '产品类型'); 
 
 
 
define('TEXT_QUANTITY_INVALID', "你输入不对的数量， 请输入最高和最低的之间的额量");
 
define('TEXT_QUANTITY_MAX_NOT_MATCH', "你输入不对的数量，请输入和分配数额一样的数量");
define('TEXT_SCREENSHOT_BEFORE_SEND_GOLD','发货前截图');
define('TEXT_SCREENSHOT_AFTER_SENT_GOLD','发货后截图');
 
 
define('TEXT_TRADE_WITH_US_PRICE', '与网站交易价格：');
define('TEXT_TRADE_CUSTOMERS_PRICE', '与客户交易价格：');
define('TEXT_TOTAL_PRICE', '总数');
define('TEXT_SECONDS', '秒');
define('TEXT_VIP_AWAITING_ORDERS_LIST', 'VIP订单分配');
define('TEXT_VIP_ORDERS_HISTORY', 'VIP单子状态');
 
 
define('TEXT_WAIT_FOR_REFRESH', '正在刷新，请稍等');
 
define('TEXT_UPLOAD_SS', '上载截图');

define('TEXT_USER_REQUIREMENT', '请上传交易截图两张备查. 要求如下：');
define('TEXT_FIRST_SS_TITLE', '1. 交易前截图:');
define('TEXT_FIRST_SS_DESC', '交易时的截图需显示双方交易界面, 双绿单绿均可, 同时界面要显示交易金币数量, 交易时间, 包裹金币数量, 交易双方处于正常交易状态（而非一方显示繁忙）');
define('TEXT_SECOND_SS_TITLE', '2. 交易后截图:');
define('TEXT_SECOND_SS_DESC', '交易后截图界面要显示"Trade complete", 交易时间、包裹剩余金币等');
define('TEXT_SS_REF_TITLE', '3. 截图可参照范例:');
define('TEXT_SS_REF_DESC', '<a href="http://cn.offgamers.com/news.php?news_id=876&news_type=5">http://cn.offgamers.com/news.php?news_id=876&news_type=5</a>');
define('TEXT_REQUIREMENT_TITLE', '4. 截图要求:');
define('TEXT_REQUIREMENT_DESC', 'jpg格式, 每张大小不得超过 500kb.');
define('TEXT_MORE_THAN_2_PHOTO', '5. 凡事交易超过2张截图的交易，请将剩余的截图经QQ， MSN 或 电邮support<EMAIL> 转送给我们');

define('SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS', '成功: 单子已经成功取消。');
define('ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED', '错误: 单子取消不成功。');
define('ERROR_NO_TRADE_CHARACTER', '请填写发货ID');
define('ERROR_INVALID_QTY', '数额错误。 请重试。');
 
define('ERROR_PLS_TRY_AGAIN', '请重试。');
define('ERROR_SUBMITTED_BY_OTHER_USER', '这单子已被其他客户提交。请重试。');
define('WARNING_INVALID_ORDERS', '单子不存在');
define('JS_CONFIRM_CANCEL_ORDER', '确定取消单子?');
define('JS_NO_CUSTOMERS_CODE', '请填写收货口令');
 
define('JS_NO_SS_BEFORE_TRADE', '请上载发货前截图');
 
define('JS_NO_SS_AFTER_TRADED', '请上载发货后截图');
define('JS_INVALID_SS_FILE_EXT', '上载截图错误, 只允许 *.jpg, *.jpeg 文档名');
define('ERROR_UPLOAD_FAILED', '上载截图失败，请稍后再重试。');
 

 
define('TEXT_HELP_ORDER_STATUS_SELECT', '审核：在【单子状态】等待接货ID出现，然后进入游戏交易（当面交易方式/游戏内邮寄方式）后，必须确认填上【已发数量】，否则款项将不被支入【结存余额】。<br><br>处理：等待卧虎游戏员工确认确实已发数量，少或多于【草单数量】的单子，将会被视为免费金币单子。<br><br>完成：单子在完成后的15分钟，单子款项会自动支入供应商的【结存余额】。供应商可以在15分钟后，自行选择提款支入先前已设好的银行账号。<br><br>取消：单子已被供应商或卧虎游戏取消。');

define('COMMENTS_VIP_TRADE_COMPLETED', 'trade customer completed');
define('COMMENTS_PROCESSING_TO_COMPLETED', '您的货款将在15分钟后汇入您的卧虎账号【结存余额】。'); 

?>