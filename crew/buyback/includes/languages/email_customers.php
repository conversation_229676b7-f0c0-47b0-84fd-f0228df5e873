<?
// e-mail greeting
define('EN_EMAIL_GREET_MR', 'Dear Mr. %s,' . "\n\n");
define('EN_EMAIL_GREET_MS', 'Dear Ms. %s,' . "\n\n");
define('EN_EMAIL_GREET_NONE', 'Dear %s,' . "\n\n"); 


define('EN_EMAIL_SEPARATOR', '------------------------------------------------------');
define('EN_EMAIL_TEXT_SUBJECT', 'Order Update #%d');
define('EN_EMAIL_TEXT_ORDER_NUMBER', 'Order Number:');
define('EN_EMAIL_TEXT_INVOICE_URL', 'Detailed Invoice:'); 
define('EN_EMAIL_TEXT_DATE_ORDERED', 'Order Date:');
define('EN_EMAIL_TEXT_UPDATED_STATUS', 'Status: %s' . "\n\n");
define('EN_EMAIL_TEXT_CLOSING', 'For any enquiries or assistance, you may use our Online Live Support service or e-mail your enquiries to %s. Please remember to include your Order Number when contacting us to expedite the process. Thanks again for shopping at %s.' . "\n");
define('EN_EMAIL_TEXT_STATUS_UPDATE_TITLE', '');

//customer order comment remark
define('EN_EMAIL_TEXT_IN_GAME_TRADE_CODE', 'Please login game and our agent will deliver gold for you, after traded please make sure the quantity is correct and give our agent this trade code: <font color="#FF0000" size="5px">%s</font>');


?>