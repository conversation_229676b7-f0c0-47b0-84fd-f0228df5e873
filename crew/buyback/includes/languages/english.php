<?
/*
  	$Id: english.php,v 1.34 2013/03/25 10:33:23 hungnian.yong Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

  	Released under the GNU General Public License
*/
	// look in your $PATH_LOCALE/locale directory for available locales
	// or type locale -a on the server.
	// Examples:
	// on RedHat try 'en_US'
	// on FreeBSD try 'en_US.ISO_8859-1'
	// on Windows try 'en', or 'English'

	@setlocale(LC_TIME, 'en_US.ISO_8859-1');
//	define('KEY_SP_LANG', "sp_lang");//Moved to application top
	define('KEY_SP_TIME_ZONE', "sp_time_zone");
	define('KEY_SPS_RSTK_CHAR_SET', "rstk_char_set");

	// charset for web pages and emails
	define('CHARSET', 'iso-8859-1');

	define('DATE_FORMAT_SHORT', '%m/%d/%Y');  // this is used for strftime()
	define('DATE_FORMAT_LONG', '%A, %d %B, %Y'); // this is used for strftime()
	define('DATE_FORMAT', 'm/d/Y'); // this is used for date()
	define('DATE_TIME_FORMAT', DATE_FORMAT_SHORT . ' %H:%M:%S');

	define('LOCAL_STORE_NAME', 'OffGamers');

	// Global entries for the <html> tag
	define('HTML_PARAMS','dir="LTR" lang="en"');

	define('BUTTON_MIN_CHAR_LENGTH', 11);

	// page title
	define('TITLE', STORE_NAME);

	// if USE_DEFAULT_LANGUAGE_CURRENCY is true, use the following currency, instead of the applications default currency (used when changing language)
	define('LANGUAGE_CURRENCY', 'USD');

    define('HEADER_TITLE_TOP', 'Home');
    define('HEADER_TITLE_ABOUT_US', 'About Us');
    define('HEADER_TITLE_CONTACT_US', 'Contact Us');
    define('HEADER_TITLE_DATE', 'OffGamers Official Time');
    define('HEADER_TITLE_FAQ', 'FAQ');
    define('HEADER_TITLE_FORUM', 'Forum');
    define('HEADER_TITLE_LOGIN', 'My Account');
    define('HEADER_TITLE_PARTNERSHIP', 'Partnership');
    define('HEADER_TITLE_POLL', 'Poll');
    define('HEADER_TITLE_ORDER_FLOW_INFO', 'Order Flow');
    define('HEADER_TITLE_MY_ACCOUNT', 'My Account');
    define('HEADER_TITLE_MY_ACCOUNT_MGMT', 'Account Management');
    define('HEADER_TITLE_MY_ORDER_HISTORY', 'My Order Status');
    define('HEADER_TITLE_MY_FAVOURITE_LINKS', 'My Favourite Links');
    define('HEADER_TITLE_MY_PAYMENT_HISTORY', 'Account Statement');
    define('HEADER_TITLE_MY_CREDIT_BALANCE', 'Credit Balance');
    define('HEADER_TITLE_BUYBACK', 'I want to sell');
    define('HEADER_TITLE_CATALOG', 'I want to buy');
    define('HEADER_TITLE_TRADE', 'I want to trade');
    define('HEADER_TITLE_CATEGORIES', 'Choose your game:');
    define('HEADER_TITLE_WELCOME', 'Welcome');
    define('HEADER_TITLE_SIGNUP', 'Sign Up');
    define('HEADER_TITLE_FORGOT_PASSWORD', 'Forgot Password?');
    define('HEADER_TITLE_ACTIVATE_ACCOUNT', 'Activate Account');
	define('HEADER_TITLE_SIGN_OFF', 'Sign Off');
	define('HEADER_TITLE_HELP_DESK', 'Help Desk');
	define('HEADER_TITLE_WEBSITE_NOTE', 'Website Note');	
	
	define('TABLE_HEADING_ACTION', 'Action');

	define('ENTRY_EMAIL_ADDRESS', 'E-mail Address');
	define('ENTRY_PASSWORD', 'Password');

	define('TEXT_SECURITY_CODE', 'Security Code');
	define('TEXT_REMEMBER_ME', 'Remember Me');
	define('TEXT_LATEST_NEWS', 'Latest News');
	define('TEXT_NOTICE_BOARD', 'Notice Board');
	define('TEXT_GAME_INFO', 'Game Info');
	define('TEXT_TIPS', 'Tips');
	define('TEXT_POLL_QUESTION', 'Rate our services.');
	define('TEXT_WEBSITE_NOTE_DESC', 'OffGamers only accept those order via website transaction...... we won\'t take any responsibility if any people pretend us by using others method to cheat');
	define('TEXT_OPTIONAL', '(Optional)');

	define('TEXT_PIN_NUMBER', 'PIN Number');
	define('TEXT_PINNUMBER_REQUIRED', 'PIN Number is required to update this field : %s');

	define('TEXT_BACK', 'Back');
	define('TEXT_CONFIRM', 'Confirm');
	define('TEXT_FINISHED', 'Done, create my account now.');
	define('TEXT_MAIN_PLEASE_SELECT_GAME', '<---Please select game');
	define('TEXT_NEXT', 'Next');
	define('TEXT_SAVE', 'Save');
	define('TEXT_SEARCH', 'Search');
	define('TEXT_SUBMIT', 'Submit');
	define('TEXT_UPDATE', 'Update');
	define('TEXT_UPDATE_AND_NEXT', 'Update >>');
	define('TEXT_VIEW', 'View');
	
	define('TEXT_ACCOUNT_TOTAL', 'Account Total');
	define('TEXT_WITHDRAW', 'Withdraw');
	define('LINK_MAIN_MAKE_ORDER_NOW', '我要下单');
	
	define('IMAGE_BUTTON_LOGIN', 'Sign In');
	define('IMAGE_BUTTON_CONTINUE', 'Continue');

	define('ICON_ERROR', 'Error');
	define('ICON_SUCCESS', 'Success');
	define('ICON_WARNING', 'Warning');

	define('BUTTON_ADD_PM', 'Add Payment Account');
	define('BUTTON_AGREE', 'Agree');
	define('BUTTON_BACK', 'Back');
	define('BUTTON_CANCEL', 'Cancel');
	define('BUTTON_CONFIRM', 'Confirm');
	define('BUTTON_CONTINUE', 'Continue');
	define('BUTTON_DELETE', 'Delete');
	define('BUTTON_DISAGREE', 'Disagree');
	define('BUTTON_EDIT', 'Edit');
	define('BUTTON_EXPORT_TEMPLATE', 'Export Template');
	define('BUTTON_IMPORT', 'Import');
	define('BUTTON_PRESALE_NOTICE', 'Pre-Sale E-mail');
	define('BUTTON_PRESALE_REMOVE', 'Pre-Sale Remove');
	define('BUTTON_REFRESH', 'Refresh');
	define('BUTTON_RESET', 'Reset');
	define('BUTTON_SEARCH', 'Search');
	define('BUTTON_SHOW_ALL_PRODUCTS', 'Show All Servers');
	define('BUTTON_SHOW_SELLING_PRODUCTS', 'Show Selling Servers');
	define('BUTTON_SUBMIT', 'Submit');
	define('BUTTON_SIGN_IN', 'Sign In');
	define('BUTTON_SIGN_UP', 'Sign Up');
	define('BUTTON_SIGN_OUT', 'Sign Out');
	
	define('ALT_BUTTON_ADD_PM', 'Add Payment Account');
	define('ALT_BUTTON_AGREE', 'Agree');
	define('ALT_BUTTON_BACK', 'Back to previous page');
	define('ALT_BUTTON_CANCEL', 'Cancel');
	define('ALT_BUTTON_CONFIRM', 'Confirm');
	define('ALT_BUTTON_CONTINUE', 'Continue');
	define('ALT_BUTTON_DELETE', 'Delete');
	define('ALT_BUTTON_DISAGREE', 'Disagree');
	define('ALT_BUTTON_EDIT', 'Edit');
	define('ALT_BUTTON_PRESALE_NOTICE', 'Pre-Sale E-mail');
	define('ALT_BUTTON_PRESALE_REMOVE', 'Pre-Sale Remove');
	define('ALT_BUTTON_REFRESH', 'Refresh');
	define('ALT_BUTTON_RESET', 'Reset');
	define('ALT_BUTTON_SEARCH', 'Search');
	define('ALT_BUTTON_SHOW_ALL_PRODUCTS', 'Click here to show all servers.');
	define('ALT_BUTTON_SHOW_SELLING_PRODUCTS', 'Click here to show your selling servers only.');
	define('ALT_BUTTON_SUBMIT', 'Submit form');
	define('ALT_BUTTON_SIGN_IN', 'Sign In');
	define('ALT_BUTTON_SIGN_UP', 'Sign Up');
	define('ALT_BUTTON_SIGN_OUT', 'Sign Out');
	
    define('TEXT_WELCOME', 'Welcome %s to OffGamers Buyback!');
	define('TEXT_REFRESH_WHEN_CHANGE', '(Page will refresh when changed)');
	define('TEXT_FIELD_REQUIRED', '&nbsp;<span class="fieldRequired">* </span>');
	define('TEXT_ERRORS_FOUND', 'Kindly correct the following errors before proceeding.');

    define('TEXT_MONTH', 'Month');
    define('TEXT_DAY', 'Day');
    define('TEXT_YEAR', 'Year');

    define('TEXT_MONTH_1', 'January');
    define('TEXT_MONTH_2', 'February');
    define('TEXT_MONTH_3', 'March');
    define('TEXT_MONTH_4', 'April');
    define('TEXT_MONTH_5', 'May');
    define('TEXT_MONTH_6', 'June');
    define('TEXT_MONTH_7', 'July');
    define('TEXT_MONTH_8', 'August');
    define('TEXT_MONTH_9', 'September');
    define('TEXT_MONTH_10', 'October');
    define('TEXT_MONTH_11', 'November');
    define('TEXT_MONTH_12', 'December');
	
	define('TEXT_CLOSED_DOWN', 'Closed');
	define('TEXT_PAYMENT_ACCOUNT_NAME', 'Account Name');
	define('TEXT_PRIMARY_ACCOUNT', 'Primary Account');

	define('TITLE_TRANS_PAYMENT', 'Payment %s');
	define('TITLE_TRANS_SUPPLIER_ORDER', 'Supplier Order %s');
	define('TITLE_TRANS_PWL_ORDER', 'PWL Order %s');
	define('TITLE_TRANS_BUYBACK_ORDER', 'Buyback Order %s');
	define('TEXT_DISPLAY_NUMBER_OF_RECORDS', 'Displaying <b>%d</b> to <b>%d</b> (of <b>%d</b> records)');
	define('TEXT_RESULT_PAGE', 'Page %s of %s');

	define('TEXT_HELP_PIN_NUMBER', 'Kindly provide your PIN Number');

	//Common fields
	define('TEXT_SERVER', 'Server');
	define('TEXT_GAME', 'Game');
	define('TEXT_QUANTITY', 'Qty'); //units
	define('TEXT_AMOUNT', 'Amount'); //$$$
	define('TEXT_AMOUNT_WITH_CURRENCY', 'Amount'); //$$$
	define('TEXT_STATUS', 'Status');
	define('TEXT_ACTION', 'Action');
	define('TEXT_ORDER_NO', 'Order #');
	define('TEXT_ORDER_STATUS', 'Order Status');
	define('TEXT_PAYMENT_STATUS', 'Payment Status');
	define('TEXT_START_DATE', 'Start Date');
	define('TEXT_END_DATE', 'End Date');
	define('TEXT_LOADING_MESSAGE', 'Loading ...');
	define('PULL_DOWN_DEFAULT', 'Please Select');

	define('ENTRY_COUNTRY_TEXT', '*');
	define('ENTRY_PASSWORD_ERROR', 'Your Password must contain a minimum of ' . ENTRY_PASSWORD_MIN_LENGTH . ' characters.');

	// e-mail greeting
	define('EMAIL_GREET_MR', 'Dear Mr. %s,' . "\n\n");
	define('EMAIL_GREET_MS', 'Dear Ms. %s,' . "\n\n");
	define('EMAIL_GREET_NONE', 'Dear %s,' . "\n\n");

	define('TEXT_UNKNOWN_ERROR', 'An unknown error has occured. Please try again.');
	define('TEXT_MUST_LOGIN', 'Login required to access this section.');

	define('TEXT_ALL_PAGES', 'All pages');

	define('TEXT_TOP', 'Top');

	define('WARNING_DOWNLOAD_DIRECTORY_NON_EXISTENT', 'Warning: The downloadable products directory does not exist: ' . DIR_FS_DOWNLOAD . '. Downloadable products will not work until this directory is valid.');

	define('PREVNEXT_BUTTON_PREV', '&lt;&lt;');
	define('PREVNEXT_BUTTON_NEXT', '&gt;&gt;');

	define('TEXT_AUTO_REFRESH', 'Auto-Refresh');
	
	define('JS_NO_GAME_SELECTED', 'Please select game');
	
	define('ERROR_EMAIL_ADDRESS_INVALID', 'Error: The email address doesn\'t appear to be valid');
	define('ENTRY_EMAIL_ADDRESS_ERROR_EXISTS', 'We notice that your e-mail address exists in our records, please <a href="' . tep_href_link(FILENAME_LOGIN) . '"> sign in </a> with the e-mail address or <a href="' . tep_href_link(FILENAME_PASSWORD_FORGOTTEN) . '">click here</a> if you have forgotten your password.');
	
	define('FOOTER_TEXT_LOGIN_FROM', 'I SEE THAT YOU ARE LOGGING ON FROM %s');
	
	// File upload message
	define('ERROR_NO_UPLOAD_FILE', 'Error: Source file does not exist.');
	define('ERROR_DESTINATION_DOES_NOT_EXIST', 'Error: Destination does not exist.');
	define('ERROR_DESTINATION_NOT_WRITEABLE', 'Error: Destination not writeable.');
	define('ERROR_FILE_SIZE_EXCEEDED', 'Error: File size has been exceeded.');
	define('ERROR_FILE_NOT_SAVED', 'Error: File upload not saved.');
	define('ERROR_FILETYPE_NOT_ALLOWED', 'Error: File upload type not allowed.');
	define('SUCCESS_FILE_SAVED_SUCCESSFULLY', 'Success: File upload saved successfully.');
	define('WARNING_NO_FILE_UPLOADED', 'Warning: No file uploaded.');
	define('WARNING_FILE_UPLOADS_DISABLED', 'Warning: File uploads are disabled in the php.ini configuration file.');
	define('WARNING_NOTHING_TO_EXPORT', 'Warning: There is no any data to be exported.');
	define('WARNING_NOTHING_TO_IMPORT', 'Warning: There is no any data to be imported.');
	define('ERROR_FILESIZE_EXCEED', 'Error: Upload size exceeded');
	define('ERROR_UPLOAD_PARTIAL', 'Error: The uploaded file was only partially uploaded.');
	define('ERROR_NO_TMP_DIR', 'Error: Missing a temporary folder.');

	// Email template
	define('EMAIL_SEPARATOR', '------------------------------------------------------');
	define('EMAIL_SUBJECT_BUYBACK', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Buyback Order Update #%s")));
	define('EMAIL_TEXT_BODY_CUSTOMER','Your buyback order has been updated to the following status.
	
	Buyback Order Summary:
	'.EMAIL_SEPARATOR."\n");
	define('EMAIL_NEW_BUYBACK_ORDER_NUMBER', 'Buyback Order Number: %s');
	define('EMAIL_NEW_BUYBACK_DATE_ORDERED', 'Buyback Order Date: %s');
	define('EMAIL_NEW_BUYBACK_CUSTOMER_EMAIL', 'E-mail Address: %s');
	
	define('EMAIL_TEXT_ORDER_NUMBER', 'Buyback Order Number: ');
	define('EMAIL_TEXT_DATE_ORDERED', 'Buyback Order Date: ');
	define('EMAIL_TEXT_STATUS_UPDATE', "Status: %s");
	define('EMAIL_TEXT_BUYBACK_ORDER_CANCELLED', "Your expired buyback order has been cancelled.");
	define('EMAIL_FOOTER', "\n\n".STORE_EMAIL_SIGNATURE);
	
	define('LOCAL_LATEST_NEWS_DETAILS', '[News Details]');
	
	define('LOCAL_META_TAG_TITLE', '臥虎游戏 － 中国专业采购、销售和交易欧美网络游戏网');
	define('LOCAL_META_TAG_DESCRIPTION', '臥虎游戏是全球专业游戏交易商: 是欧美网络游戏最大销售商: 魔兽世界,天堂2,行会战争,黑客帝国,最终幻想,星球大战,无尽任务,World of Warcraft Gold, Lineage 2 Adena, EverQuest Platinum, EQ2 Plat, SWG credits,MXO info,guild wars gold,ffxi gil,eve isk and more！');
	define('LOCAL_META_TAG_KEYWORDS', 'cn.offgamers.com, 臥虎游戏, 魔兽世界,天堂2,行会战争,黑客帝国,World of warcraft gold, WoW gold,  Lineage 2 Adena, EverQuest Platinum, EQ2 Plat,魔兽金币,wow gold,天堂币,游戏交易,网络游戏交易,网游交易,装备交易,帐号交易,qq币,游戏交易平台,游戏交易网,虚拟币交易');
	
	define('DISPLAY_NAME_CAT_ID_194', '魔兽世界（美服）');
	define('DISPLAY_NAME_CAT_ID_195', '魔兽世界（欧服）');
	define('DISPLAY_NAME_CAT_ID_1802', '混乱在线');
	define('DISPLAY_NAME_CAT_ID_1877', '霸王');
	define('DISPLAY_NAME_CAT_ID_1684', '末日战车');
	define('DISPLAY_NAME_CAT_ID_1964', '惊天动地');
	define('DISPLAY_NAME_CAT_ID_1735', '英雄城市');
	define('DISPLAY_NAME_CAT_ID_1695', '恶棍城市');
	define('DISPLAY_NAME_CAT_ID_1454', '龙与地下城');
	define('DISPLAY_NAME_CAT_ID_1496', '无尽任务II');
	define('DISPLAY_NAME_CAT_ID_1809', '最终幻想XI');
	define('DISPLAY_NAME_CAT_ID_1401', '行会战争');
	define('DISPLAY_NAME_CAT_ID_1834', '英雄');
	define('DISPLAY_NAME_CAT_ID_1366', '天堂II');
	define('DISPLAY_NAME_CAT_ID_1807', '冒险岛');
	define('DISPLAY_NAME_CAT_ID_1736', '丝路');
	define('DISPLAY_NAME_CAT_ID_2060', '英雄诗歌');
	define('DISPLAY_NAME_CAT_ID_2172', '魔戒指环王（美服）');
	define('DISPLAY_NAME_CAT_ID_2208', '魔戒指环王（欧服）');
	define('DISPLAY_NAME_CAT_ID_3263', '科南时代美服');
	define('DISPLAY_NAME_CAT_ID_2235', '科南时代欧服');
	
	// Down For Maintenance
	define('TEXT_BEFORE_DOWN_FOR_MAINTENANCE', 'NOTICE: This website will be down for maintenance on %s for a period of %s.');
	define('TEXT_ADMIN_DOWN_FOR_MAINTENANCE', 'NOTICE: the website is currently Down For Maintenance to the public');
	
	
	define('EMAIL_BUYBACK_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Buyback Order Update #%s")));
	
	define('EMAIL_BUYBACK_UPDATE_BODY', 'Buyback Order Summary:'."\n".EMAIL_SEPARATOR."\n");
	define('EMAIL_BUYBACK_UPDATE_ORDER_NUMBER', 'Order Number: %s');
	define('EMAIL_BUYBACK_UPDATE_DATE_ORDERED', 'Order Date: %s');
	define('EMAIL_BUYBACK_UPDATE_STATUS', 'Status: %s');
	
	define('EMAIL_BUYBACK_UPDATE_FOOTER', "\n\n" . STORE_EMAIL_SIGNATURE);
	
	//payment pop up menu
	define('POP_PAYMENT_REPORT_PAYMENT_MESSAGE', 'Payment Message');
	define('POP_PAYMENT_REPORT_DATETIME', 'Date/Time');
	
	define('JS_ERROR_SUBMITTED', 'This form has already been submitted. Please press Ok and wait for this process to be completed.');
	define('JS_ERROR', 'Errors have occured during the process of your form!\nPlease make the following corrections:\n\n');
	
	// File upload message: error msg refer to http://www.php.net/manual/en/features.file-upload.errors.php
	define('ERROR_UPLOAD_ERR_INI_SIZE', 'Error: The uploaded file exceeds the upload_max_filesize directive in php.ini.');
	define('ERROR_UPLOAD_ERR_FORM_SIZE', 'Error: The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.');
	define('ERROR_UPLOAD_ERR_PARTIAL', 'Error: The uploaded file was only partially uploaded.');
	define('ERROR_UPLOAD_ERR_NO_FILE', 'Error: No file was uploaded.');
	define('ERROR_UPLOAD_ERR_NO_TMP_DIR', 'Error:issing a temporary folder.');
	define('ERROR_UPLOAD_ERR_CANT_WRITE', 'Error: Failed to write file to disk.');
	
	// Captcha
	define('ERROR_INVALID_CODE', 'Sorry, the code you entered was invalid.');
	
?>