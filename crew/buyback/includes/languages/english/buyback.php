<?php
/*
  $Id: buyback.php,v 1.11 2013/03/25 10:15:54 hungnian.yong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('NAVBAR_TITLE', 'I want to sell');
define('HEADING_TITLE', NAVBAR_TITLE);

define('TEXT_NOTICE_MSG', 'Reminder: Kindly confirm ID of receiver, beware of scammer.');
define('TEXT_HEADING_STEP1', 'Step 1 : Specify server and quantity');
define('TEXT_HEADING_STEP2', 'Step 2 : Specify personal details');
define('TEXT_HEADING_STEP3', 'Step 3 : Confirmation');

define('TEXT_HEADING_SERVER_LIST', 'Server Listing');
define('TEXT_HEADING_SERVER', 'Server');
define('TEXT_HEADING_UNIT_PRICE', 'Unit Price');
define('TABLE_HEADING_PURCHASE_QTY', 'Quantity');
define('TEXT_HEADING_STATUS', 'Status');

define('TEXT_HEADING_SUMMARY', 'Order Summary');
define('TEXT_HEADING_CHARACTER_INFO', 'Character Info');
define('TEXT_HEADING_CONTACT_INFO', 'Contact Info');
define('TEXT_HEADING_PAYMENT_INFO', 'Payment Info');

//define('TEXT_NO_ITEMS_TO_BUYBACK', 'No items to buyback.');
define('TEXT_SELECT_LINK', 'Select');
define('TEXT_MIN', 'Min : ');
define('TEXT_MAX', 'Max : ');
define('TEXT_OF', 'of');
define('TEXT_OR', 'or');
define('TEXT_NOTES', 'Notes:');

define('TEXT_PRODUCT', 'Product : ');
define('TEXT_UNIT_PRICE', 'Unit Price : ');
define('TEXT_TOTAL_PRICE', 'Total Price');
define('TEXT_MEETING_POINT', 'Meeting Point');
define('TEXT_ADD_SHORTCUT_TO', 'Add Server Shortcut to :');
define('TEXT_ADD_TO_MANUFACTURER_LIST', 'Manufacturer List');
define('TEXT_INSTRUCTIONS_HEADER1', 'Buyback');
define('TEXT_INSTRUCTIONS_LEFT', 'Kindly provide the following details and proceed to the next step.');
define('TEXT_MATCH_BACKORDER_EXACT_AMOUNT', '(If submit more than %d, we only accept %d, we do not accept any quantity within %d-%d)');

define('TEXT_CHARACTER_NAME', 'Sender Character ID');
define('TEXT_CHARACTER_NAME_HELP', 'Kindly provide your character name for us to arrange receipt.');
//define('TEXT_CHARACTER_NAME_RECEIVER', 'Receiver Character ID');
define('TEXT_DELIVERY_TIME', 'Delivery Time');
define('TEXT_DELIVERY_TIME_HELP', 'Note: Order will be cancelled if exceeded.');
define('TEXT_CONTACT_NAME', 'Name');
define('TEXT_CONTACT_NO', 'Contact Number');

define('TEXT_PAY_TO_ACCOUNT', 'Pay to');
define('TEXT_PAY_TO_ACCOUNT_HELP', 'Kindly select an account for us to deliver payment.');
define('TEXT_ADDITIONAL_COMMENTS', 'Additional Comments');
define('TEXT_ORDER_REFERENCE', 'Order Reference');

define('TEXT_ADD_NEW', 'Place another order');
define('TEXT_DELIVER_NOW', 'Deliver now');

define('TEXT_BUTTON_LABEL_STEP2', 'Next');
define('TEXT_BUTTON_LABEL_STEP3', 'Submit Order');
define('TEXT_QUANTITY_INVALID', "You have entered an incorrect amount. Please enter an amount within the minimum and maximum quantity.");
define('TEXT_MANDATORY_ISBLANK', 'Incomplete field : %s');
define('TEXT_FORM_INCOMPLETE', 'Incomplete field');
define('TEXT_FORM_STILL_PROCESSING', 'We are still processing your last buyback order for this server.<br />Please try a different server.');
define('TEXT_FORM_MAX_ORDERS_REACHED', 'You have reached the maximum number of Pending buyback orders. Please wait for those orders to complete before submitting more orders.');
define('TEXT_NOT_ACCEPT_BUYBACK', " Sorry we are not currently accepting buybacks for the selected server.<br /> Please check again in 24 hours or e-mail to <a href=\"mailto:<EMAIL>\"><EMAIL><\/a> for further details.");
define('TEXT_EMAIL_IF_MORE_SUPPLY', "Kindly e-Mail to <a href=\"mailto:<EMAIL>\"><EMAIL><\/a> if you have more than the maximum quantity<br>or have consistent supply.");
define('TEXT_INCOMPLETE_FIELDS', 'Incomplete field(s) detected. Please try again.');
define('TEXT_INVALID_SEVER_SELECTION', 'Invalid Game/Server Selection. Please try again.');
define('TEXT_ORDER_POSTED_INSTRUCTIONS', 'Your order has been submitted and will appear in your order history. To start delivering, click '.TEXT_DELIVER_NOW.' or click '.TEXT_ADD_NEW.' to submit another order.');
define('ERROR_BUYBACK_SENDER_ID_EXISTS', 'Error: Sender Character ID has been exists, please check your Sender Character ID');
define('ERROR_SUBMITTED_BY_OTHER_USER', 'This order has been submitted by other user. Please try again.');

define('EMAIL_NEW_BUYBACK_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "New Buyback Order #%s")));
define('EMAIL_NEW_BUYBACK_BODY', "Thank you for selling your items to ".STORE_NAME.".\n\n Supplier Order Summary:\n".EMAIL_SEPARATOR."\n");
define('EMAIL_NEW_BUYBACK_PRODUCTS', "\n\n".'Products');
define('EMAIL_NEW_BUYBACK_ORDER_TOTAL', 'Total: %s %s');
define('EMAIL_NEW_BUYBACK_COMMENTS','Extra Information:');
define('EMAIL_NEW_BUYBACK_ORDER_CLOSING', "Please contact us now via our Online Live Support service or e-mail to ".EMAIL_TO." to arrange a time for collection of all the buyback items.  Please remember to include your Buyback Order Number when contacting us to expedite the process.\n\nYou will receive an e-mail notification whenever there is an update in your buyback order status.  You can also review your buyback orders and check their status by signing in to OffGamers.com and clicking on \"View Buyback Order History\".\n\nThank you for supporting ".STORE_NAME.'.');
define('EMAIL_NEW_BUYBACK_ORDER_FOOTER', "\n\n" . EMAIL_FOOTER);
?>