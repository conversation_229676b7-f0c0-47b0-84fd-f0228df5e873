<?php

define('TEXT_CURRENT_PASSWORD', 'Current Password');
define('TEXT_CHANGE_PASSWORD', 'Change Password');
define('TEXT_INVALID_PIN_NUMBER', 'Invalid ' . TEXT_PIN_NUMBER);
define('TEXT_INVALID_CURRENT_PASSWORD', 'Invalid ' . TEXT_CURRENT_PASSWORD);
define('TEXT_UPDATE_FAILED', 'Update Failed');
define('TEXT_UPDATE_SUCCESS', 'Update Successful');
define('TEXT_HELP_CURRENT_PASSWORD', 'Kindly provide your current password ');

// <PERSON>
define('ENTRY_FORM_PM_SELECT_PM', 'Payment Method:');
define('ENTRY_FORM_PM_ALIAS', 'Payment Title:');

define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', 'Required Information:');

define('TABLE_HEADING_PM_PAYMENT_ACCOUNT', 'Payment Account');

define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', 'There is no any payment account records yet.');

define('LINK_PM_EDIT_PM_ACCOUNT', 'Edit payment account');
define('LINK_PM_DELETE_PM_ACCOUNT', 'Delete payment account');

define('SUCCESS_PM_DELETE_PM_ACCOUNT', 'Success: Payment account has been successfully deleted.');

define('ERROR_INVALID_PM_BOOK_SELECTION', 'Error: You are editing invalid payment account.');
define('ERROR_INVALID_PM_SELECTION', 'Error: Please select valid payment method.');
define('ERROR_EMPTY_PM_ALIAS', 'Error: Payment title is required.');
define('ERROR_PM_FIELD_INFO_REQUIRED', 'Error: Please provide "%s" information!');
define('ERROR_PM_ACCOUNT_BOOK_FULL', 'Your payment account book is full. Please delete an unneeded account to save a new one.');
define('ERROR_INVALID_PIN', 'Invalid PIN Code. Update aborted.');

define('JS_ERROR_NO_WITHDRAW_AMOUNT', 'Please enter your withdraw amount');
define('JS_ERROR_INVALID_WITHDRAW_AMOUNT', 'Invalid withdraw amount');
?>