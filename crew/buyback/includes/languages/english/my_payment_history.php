<?
/*
  $Id: my_payment_history.php,v 1.5 2008/11/03 10:48:32 wilson.sun Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('HEADING_TITLE', HEADER_TITLE_MY_PAYMENT_HISTORY);
define('NAVBAR_TITLE', HEADER_TITLE_MY_PAYMENT_HISTORY);

define('TEXT_PAYMENT_ID', 'Payment No');
define('TEXT_PAYMENT_METHOD', 'Payment Method');
define('TEXT_DATE_REQUESTED', 'Date Requested');
define('TEXT_RESULTS_FOUND', 'Results Found');
define('TEXT_AMOUNT_PAID', 'Amount Paid');
define('TEXT_REMAINING', 'Remaining');
define('TEXT_DATE', 'Date');
define('TEXT_TIME', 'Time');

define('TEXT_ACCOUNT', 'Account');
define('TEXT_WITHDRAW_AMT', 'Withdraw Amount');
define('TEXT_CLOSE', 'Close');
define('TEXT_INVALID_AMOUNT', 'Invalid Amount.');
define('TEXT_CANCEL', 'Cancel');
define('TEXT_OK', 'OK');
define('TEXT_CONFIRM_WITHDRAWAL', 'Confirm Withdrawal ?');
define('TEXT_REQUEST_SUCCESSFULL', 'Request Successfull');


define('ENTRY_WITHDRAW_CURRENT_BALANCE', 'Current Balance:');
define('ENTRY_WITHDRAW_RESERVE_AMOUNT', 'Reserve:');
define('ENTRY_WITHDRAW_AVAILABLE_BALANCE', 'Available for Withdrawal:');
define('ENTRY_WITHDRAW_AMOUNT', 'Withdraw Amount:');
define('ENTRY_WITHDRAW_PAYMENT_ACCOUNT', 'Withdraw To:');
define('ENTRY_WITHDRAW_FINAL_AMOUNT', 'Receivable Amount:');

define('ENTRY_WITHDRAW_PAYMENT_METHOD', 'Payment Method:');
define('ENTRY_WITHDRAW_MIN_AMT', '&nbsp;- Minimum Amount:');
define('ENTRY_WITHDRAW_MAX_AMT', '&nbsp;- Maximum Amount:');
define('ENTRY_WITHDRAW_FEES', '&nbsp;- Withdraw Fees:');

//For payment module------------------------------------------------------------
define('HEADER_FORM_ACC_STAT_TITLE', 'Account Statement');
define('ENTRY_ORDER_START_DATE', 'Start Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_ORDER_END_DATE', 'End Date<br><small>(YYYY-MM-DD)</small>');
define('ENTRY_PAYMENT_ID', 'Payment ID');
define('ENTRY_ORDER_ID', 'Order ID');
define('ENTRY_RECORDS_PER_PAGE', 'Records per page');
define('ENTRY_PAYMENT_STATUS', 'Payment Status');
define('TEXT_LANG_RECORDS_PER_PAGE', '');
define('TABLE_HEADING_ACC_STAT_DATETIME', 'Date/Time');
define('TABLE_HEADING_ACC_STAT_ACTIVITY', 'Activity');
define('TABLE_HEADING_ACC_STAT_PAYMENT_STATUS', 'Payment Status');
define('TABLE_HEADING_ACC_STAT_DEBIT', 'Debit');
define('TABLE_HEADING_ACC_STAT_CREDIT', 'Credit');
define('TABLE_HEADING_ACC_STAT_BALANCE', 'Balance');
define('TEXT_ACC_STAT_NOT_APPLICABLE', '-');
define('TEXT_ACC_STAT_PAYMENT_STATUS_UPDATE_DATE', '%s on %s');
define('TEXT_ACC_STAT_ADMIN_COMMENT', 'Comment:');
define('SECTION_TITLE_FORM_PM_REQUIRED_INFO', 'Required Information:');
define('TEXT_PM_NO_EXISTING_PAYMENT_ACCOUNT', 'There is no any payment account records yet.');
define('LINK_PM_EDIT_PM_ACCOUNT', 'Edit payment account');
define('LINK_PM_DELETE_PM_ACCOUNT', 'Delete payment account');
define('SUCCESS_PM_DELETE_PM_ACCOUNT', 'Success: Payment account has been successfully deleted.');
define('ERROR_INVALID_PM_BOOK_SELECTION', 'Error: You are editing invalid payment account.');
define('ERROR_INVALID_PM_SELECTION', 'Error: Please select valid payment method.');
define('ERROR_EMPTY_PM_ALIAS', 'Error: Payment title is required.');
define('ERROR_PM_FIELD_INFO_REQUIRED', 'Error: Please provide "%s" information!');
define('ERROR_PM_ACCOUNT_BOOK_FULL', 'Your payment account book is full. Please delete an unneeded account to save a new one.');
//--end for payment module--------------------------------------------------------

define('COMMENT_WITHDRAW_FUNDS_REQUESTED', 'Request funds withdraw to %s(%s)');

define('TEXT_NO_WITHDRAW_LIMIT', 'Any');
define('TEXT_WITHDRAW_FEES_FREE', 'Free');

define('LINK_WITHDRAW_ACCOUNT_STATEMENT', '[<a href="%s">Account Statement</a>]');
define('LINK_PM_ACC_BOOK', '[<a href="%s">Payment Account Book</a>]');

define('ERROR_WITHDRAW_NO_CURRENCY_ACCOUNT_INFO', 'Please select the currency account to withdraw.');
define('ERROR_WITHDRAW_NO_AVAILABLE_FUNDS', 'You have no available funds to withdraw or your withdraw amount exceed the available funds.');
define('ERROR_WITHDRAW_NO_PAYMENT_ACCOUNT_BOOK', 'Please select which account to receive the funds.');
define('ERROR_WITHDRAW_NO_WITHDRAW_AMOUNT', 'Please enter your withdraw amount.');
define('ERROR_WITHDRAW_NOT_SUCCESS', 'Your funds withdraw is failed.');
define('ERROR_WITHDRAW_DISABLE_WITHDRAWAL', 'Your payment withdrawal capability has been disabled.'."\n\r".'Please contact <a href=\'mailto:<EMAIL>\'>Customer Support</a> for detail.');
define('WARNING_WITHDRAW_FEES_HIGHER_THAN_WITHDRAW_AMOUNT', 'Withdraw fees is higher than withdraw amount.');
define('WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '%s is not available for this withdraw amount.');
define('SUCCESS_WITHDRAW_SUCCESS', 'Your funds has been successfully withdraw.');

define('CONTENTS_WARNING_WITHDRAW_NOT_ALLOWED_PAYMENT_METHOD', '<span class="redIndicator">%s is not available for this withdraw amount</span>');

define('JS_ERROR_NO_WITHDRAW_AMOUNT', 'Please enter your withdraw amount');
define('JS_ERROR_INVALID_WITHDRAW_AMOUNT', 'Invalid withdraw amount');

// E-mail
define('EMAIL_PAYMENT_PAYMENT_UPDATE_SUBJECT', implode(' ', array(EMAIL_SUBJECT_PREFIX, "Payment Update #%s")));

define('EMAIL_PAYMENT_TEXT_TITLE', 'You request to withdraw %s via %s from your OffGamers Store Balance account.');
define('EMAIL_PAYMENT_TEXT_SUMMARY_TITLE', 'Payment Summary:');
define('EMAIL_PAYMENT_TEXT_PAYMENT_NUMBER', 'Payment Number: %s');
define('EMAIL_PAYMENT_TEXT_PAYMENT_DATE', 'Payment Date: %s');
define('EMAIL_PAYMENT_TEXT_COMMENTS', 'Comment:' . "\n%s\n");
define('EMAIL_PAYMENT_TEXT_STATUS_UPDATE', "Status: %s");

define('EMAIL_PAYMENT_WITHDRAW_CLOSING', 'Thank you for supporting ' . STORE_NAME . '.');
define('EMAIL_PAYMENT_FOOTER', EMAIL_FOOTER);
?>