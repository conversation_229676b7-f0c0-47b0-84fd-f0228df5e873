<?php
/*
  $Id: password_forgotten.php,v 1.3 2008/07/08 07:37:57 weichen Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/
define('HEADING_TITLE', HEADER_TITLE_FORGOT_PASSWORD);
define('NAVBAR_TITLE', HEADER_TITLE_FORGOT_PASSWORD);

define('TEXT_EMAIL_ADDRESS', 'Email address');
define('TEXT_HELP_EMAIL_ADDRESS', 'Kindly provide your Email address');
define('TEXT_INSTRUCTIONS', 'Kindly provide your E-mail address and PIN Number. A new password will be generated and sent to your email address.');
define('TEXT_RESET_PASSWORD', 'Reset Password');

define('TEXT_INCOMPLETE_FIELDS', 'Incomplete field(s) detected. Please try again.');
define('TEXT_RESET_FAILED', 'Operation failed : Please try again.');
define('TEXT_RESET_SUCCESS', 'A new password has been generated and sent to your email address.');
define('TEXT_INVALID_ENTRIES_OR_ACCOUNT_NOT_ENABLED', 'Invalid email/PIN Number or account is disabled.');

define('TEXT_INVALID_PIN_NUMBER', 'Invalid ' . TEXT_PIN_NUMBER);

define('SUPPLIER_EMAIL_SUBJECT', implode(' ',array(EMAIL_SUBJECT_PREFIX,"Supplier Account Password Reset")));
define('SUPPLIER_EMAIL_TEXT', 'Your supplier account password has been reset to the following.  You may now sign in to our supplier module at the following link,

%s

E-mail Address: %s
New Password: %s');

define('SUPPLIER_EMAIL_FOOTER', "\n\n".LOCAL_STORE_EMAIL_SIGNATURE);
?>
