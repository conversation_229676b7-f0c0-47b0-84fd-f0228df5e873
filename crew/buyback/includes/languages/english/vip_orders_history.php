<?
/*
  $Id: vip_orders_history.php,v 1.7 2012/11/06 04:09:01 chunhoong.leong Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

define('HEADING_TITLE', HEADER_TITLE_MY_ORDER_HISTORY);
define('NAVBAR_TITLE', HEADER_TITLE_MY_ORDER_HISTORY);

define('TEXT_ALL_GAMES', '* All Games *');
define('TEXT_PRODUCT_TYPE', 'Product Type');
define('TABLE_HEADING_RECEIVER_CHAR_NAME', 'Character Name');

define('TEXT_REQUEST_QTY', '1st List Qty');
define('TEXT_SENT_QTY', '2nd List Qty');

define('TEXT_EXPIRES', 'Order will be canceled in:');
define('TEXT_MINUTES', 'mins');

define('TEXT_TIME_LEFT', 'Time Left (mins)');
define('TEXT_ORDERS_FOUND', 'record(s) found. ');

define('TEXT_PENDING_DELIVERY', 'Pending Delivery');
define('TEXT_PENDING_PAYMENT', 'Pending Payment');
define('TEXT_RECEIVED', 'Received');

define('TEXT_PENDING', '1st List');
define('TEXT_PROCESSING', 'Second List');
define('TEXT_COMPLETED', 'Confirmed List');
define('TEXT_CANCELLED', 'Cancelled');

define('TEXT_GAME_CURRENCY', 'Game Currency');

define('TEXT_ERROR_TRYAGAIN', 'An error has occured. Please try again.');
define('TEXT_ERROR_INVALID_QTY', 'Invalid Quantity. Please try again.');
define('TEXT_ERROR_NO_SS_AFTER_UPLOADED', 'No after send gold screenshot. Please try again.');
define('TEXT_ERROR_NO_SS_BEFORE_UPLOADED', 'No before send gold screenshot. Please try again.');
define('TEXT_REFRESH_NOTICE', 'Results will refresh in ');
define('TEXT_SECONDS', 'seconds');
define('TEXT_WAIT_FOR_REFRESH', 'Refreshing, please wait');

define('TEXT_CHARACTER_NAME', 'Sender Character ID');
define('TEXT_CHARACTER_NAME_IN_PREPARATION', 'Wait for update');
define('TEXT_CHARACTER_NAME_HELP', 'Kindly provide your character name for us to arrange receipt.');
define('ENTRY_RECEIVER_CHAR_NAME', 'Receiver Character ID');
define('TEXT_DELIVERY_TIME', 'Delivery Time');
define('TEXT_DELIVERY_TIME_HELP', 'Note: Order will be cancelled if exceeded.');
define('TEXT_CONTACT_NAME', 'Name');
define('TEXT_CONTACT_NO', 'Contact Number');
define('TEXT_ADDITIONAL_COMMENTS', 'Additional Comments');
define('TEXT_ORDER_REFERENCE', 'Order Reference');
define('TEXT_TOTAL_PRICE', 'Total Amount');
define('TEXT_FILE_SIZE', '(File Size: %d KB)');
define('TEXT_OF', 'of');
define('SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS', 'Success: Buyback order has been successfully canceled.');
define('ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED', 'Error: Buyback order cancellation failed.');
define('ERROR_SUBMITTED_BY_OTHER_USER', 'This order has been submitted by other user. Please try again.');
define('TEXT_SCREENSHOT_BEFORE_SEND_GOLD','Screenshot before send gold');
define('TEXT_SCREENSHOT_AFTER_SENT_GOLD','Screenshot after send gold');

define('JS_CONFIRM_CANCEL_ORDER', 'Confirm cancel this order?');
define('JS_NO_SS_BEFORE_TRADE', 'Please upload before send gold screenshot');
define('JS_NO_SS_AFTER_TRADED', 'Please upload after send gold screenshot');
define('JS_INVALID_SS_FILE_EXT', 'Error: Screenshot upload, Only allowed *.jpg, *.jpeg file type');
define('ERROR_UPLOAD_FAILED', 'Screenshot Upload failed. Please try later.');
define('ERROR_PLS_TRY_AGAIN', 'Please try again.');
define('LINK_SEND_STOCK_FAQ', 'Submit Order FAQ');

define('EMAIL_TEXT_CLOSING', 'Thank you for supporting '.STORE_NAME.'.');	// unique

define('ORDERS_LOG_UNLOCK_OWN_ORDER', '##ul_1##');

define('TEXT_USER_REQUIREMENT', 'Please upload your before and after trade screen shot which to have the required fields as:');
define('TEXT_FIRST_SS_TITLE', '1. First Screen Shot:');
define('TEXT_FIRST_SS_DESC', 'Trade window with Customer character and make sure your bags are open to show the gold you have. Show the Gold amount to be traded according to the buyback order you have made;');
define('TEXT_SECOND_SS_TITLE', '2. Second Screen Shot:');
define('TEXT_SECOND_SS_DESC', 'Floating text will appear with the words "Trade Complete" after clicking on "trade" and Trade Completion time with bags are open to show the gold you have after trade complete.');
define('TEXT_SS_REF_TITLE', '3. Screenshot reference:');
define('TEXT_SS_REF_DESC', '<a href="http://kb.offgamers.com/?p=310">http://kb.offgamers.com/?p=310</a>');
define('TEXT_REQUIREMENT_TITLE', '4. Requirement:');
define('TEXT_REQUIREMENT_DESC', '*.jpg format. Each file size cannot exceed 500kb.');
define('TEXT_MORE_THAN_2_PHOTO', '5. If exceed than 2 screen shot, please send us the rest of the screen shot via MSN or <NAME_EMAIL>');
?>