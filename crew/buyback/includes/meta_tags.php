<?
/***********************************************************
	FILE: 	meta_tags.php
	USE : 	This file controls the title, meta description,
			and meta keywords of every page on your web site.
			See the install docs for instructions.
***********************************************************/

// Define Primary Section Output
define('PRIMARY_SECTION', ' : ');

// Define Secondary Section Output
define('SECONDARY_SECTION', ' - ');

// Define Tertiary Section Output
define('TERTIARY_SECTION', ', ');

// Optional customization options for each language
if (!$languages_id) $languages_id = 1;
switch ($languages_id) {
  	case '1':	// English language
    	//Extra keywords that will be outputted on every page
    	$mt_extra_keywords = '';
		//Descriptive tagline of your web site
		$web_site_tagline = TERTIARY_SECTION . '';
		break;
  	case '2':	// German language
    	//Extra keywords that will be outputted on every page
    	$mt_extra_keywords = '';
		//Descriptive tagline of your web site
		$web_site_tagline = TERTIARY_SECTION . '';
		break;
  	case '3':	// Spanish language
    	//Extra keywords that will be outputted on every page
    	$mt_extra_keywords = '';
		//Descriptive tagline of your web site
		$web_site_tagline = TERTIARY_SECTION . '';
		break;
}

// Clear web site tagline if not customized
if ($web_site_tagline == TERTIARY_SECTION) {
	$web_site_tagline = '';
}

$mt_keywords_string = '';
/*
$browser_title = (defined('BROWSER_TITLE') && tep_not_null(BROWSER_TITLE)) ? BROWSER_TITLE . PRIMARY_SECTION : '';
define('WEB_SITE_KEYWORDS', $mt_keywords_string . $mt_extra_keywords . (defined('META_KEYWORDS') && tep_not_null(META_KEYWORDS) ? META_KEYWORDS . TERTIARY_SECTION : '') );
define('META_TAG_DESCRIPTION', TITLE . PRIMARY_SECTION . NAVBAR_TITLE . SECONDARY_SECTION . WEB_SITE_KEYWORDS);
*/
switch ($content) {
  	case CONTENT_INDEX_DEFAULT:
	    define('META_TAG_TITLE', LOCAL_META_TAG_TITLE);
	    define('META_TAG_DESCRIPTION', LOCAL_META_TAG_DESCRIPTION);
		define('META_TAG_KEYWORDS', LOCAL_META_TAG_KEYWORDS);
		
	    break;
  	default:
    	define('META_TAG_TITLE', LOCAL_META_TAG_TITLE);
	    define('META_TAG_DESCRIPTION', LOCAL_META_TAG_DESCRIPTION);
		define('META_TAG_KEYWORDS', LOCAL_META_TAG_KEYWORDS);
		
		break;
}
?>