<?
if(!function_exists('eval_buffer')) {
	function eval_buffer($string) {
		ob_start();
		eval("$string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_print_buffer')) {
	function eval_print_buffer($string) {
		ob_start();
		eval("print $string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_html')) {
	function eval_html($string) {
		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si", "eval_print_buffer",$string);
		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si", "eval_buffer",$string);
	}
}

$latest_news_group_select_sql = "SELECT * FROM " . TABLE_LATEST_NEWS_GROUPS ;
$latest_news_group_result_sql = tep_db_query($latest_news_group_select_sql);
while ($latest_news_group_row = tep_db_fetch_array($latest_news_group_result_sql)) {
	$latest_news_group_array[] = array('id' => $latest_news_group_row['news_groups_id'], 'text' => $latest_news_group_row['news_groups_name']);
}

$this_site_id = 0;
if (defined('SITE_ID')) {
	$this_site_id = SITE_ID;
}
$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";

if (isset($latest_news_group_array)) {
    foreach ($latest_news_group_array AS $key => $extra_latest_news) {
        $count=0;
        $news_id = '';
        $display_these = array();
/*--------------------------------------------------------------- BEFORE ---------------------------------------------------------------------         
        $listing_select_sql = "SELECT news_id, news_groups_id, headline, content, date_added, url 
                               FROM " . TABLE_LATEST_NEWS . " 
                               WHERE status = '1' 
                                   AND $news_display_sites_where_str 
                                   AND language = '". $languages_id. "' 
                                   AND news_groups_id='".$extra_latest_news['id']."' 
                                   AND extra_news_display_sites = '1'
                               ORDER BY date_added DESC";
--------------------------------------------------------------- BEFORE --------------------------------------------------------------------- */  
//--------------------------------------------------------------- AFTER ---------------------------------------------------------------------
        $listing_select_sql = "SELECT lnd.news_id, ln.news_groups_id, lnd.headline, lnd.content, ln.date_added, ln.url 
                               FROM " . TABLE_LATEST_NEWS . " AS ln
                               INNER JOIN ".TABLE_LATEST_NEWS_DESCRIPTION." AS lnd 
                                ON ln.news_id = lnd.news_id  
                               WHERE ln.status = '1' 
                                   AND $news_display_sites_where_str 
                                   AND lnd.language_id = '". $languages_id. "' 
                                   AND ln.news_groups_id='".$extra_latest_news['id']."' 
                                   AND extra_news_display_sites = '1'
                               ORDER BY ln.date_added DESC";
//--------------------------------------------------------------- AFTER ---------------------------------------------------------------------      
        $listing_result_sql = tep_db_query($listing_select_sql);
        while ($listing_row = tep_db_fetch_array($listing_result_sql)) {
            $news_id = $listing_row['news_id'];
            
            if (tep_not_null($news_id)) {
            	$info_box_contents = array();
          		$info_box_contents[] = array('params' => 'class="latestNewsBoxHeading"',
          									 'text' => '<table border="0" width="100%" cellspacing="0" cellpadding="0"><tr>'
                                               		   .'<td align="left">'
                                               		   .'<div class="latestNewsTitle">'.eval_html($listing_row["headline"]).'</div>'
                                               		   .'</td>'
                                               		   .'<td align="right">'
                                               		   .'<div class="latestNewsDate">'.tep_date_long($listing_row["date_added"].'</div>')
                                               		   .'</td>'
                                               		   ."</tr></table>\n"
        									 );
                
                $info_box_contents = array();
                $text = "<table border=\"0\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\"><tr><td colspan=\"2\" align=\"left\">" .
                         "<table border=\"0\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\"><tr onmouseover=\"this.className='latestNewsTableRowOver';\" onclick=\"showHideContent('". $listing_row['news_id'] ."', '". $listing_row["date_added"] ."');\"><td align=\"left\"><div class=\"latestNewsTitle\">". eval_html($listing_row["headline"]) ."</div></td><td width=\"20%\"><div id=\"datetime_". $listing_row['news_id']."\" class=\"latestNewsDate\">". $listing_row["date_added"] ."</div></td><td align=\"right\" width=\"5%\" class=\"latestNewsTitle\"><div id=\"clip_". $listing_row['news_id']."\" class=\"latestNewsTitle\">". tep_image(DIR_WS_ICONS . 'icon_expand.gif') ."</div></td></tr></table>" .
                         "</td></tr><tbody id=\"content_". $listing_row['news_id'] ."\" class=\"hide\">" .
                         "<tr><td colspan=\"2\"><div class=\"latestNewsDate\">". tep_date_long($listing_row["date_added"]) ."</div></td></tr>" .
                         "<tr><td>&nbsp;&nbsp;</td><td class=\"latestNewsBoxContents\">". nl2br(eval_html($listing_row['content'])) . "</td></tr>" .
                         "</tbody><tr><td colspan=\"2\">&nbsp;&nbsp;</td></tr></table>";
                
                $info_box_contents[] = array('params' => 'class="latestNewsBoxHeading"',
                    						 'text' => $text);
                
            	$display_these[$count] = $info_box_contents[0]['text'];
                $count++;
            }
        }
        
        if (tep_not_null($news_id)) {
            $info_box_contents_title = array();
            $news_group_name_select_sql = "SELECT news_groups_name 
                                           FROM " . TABLE_LATEST_NEWS_GROUPS . " 
                                           WHERE ". (isset($extra_latest_news['id']) ? "news_groups_id='{$extra_latest_news['id']}'" : "1");
            $news_group_name_result_sql = tep_db_query($news_group_name_select_sql);
            $news_group_name_row = tep_db_fetch_array($news_group_name_result_sql);
            
        	$local_constant_news_title = preg_replace('/[^a-z]/i', '_', $news_group_name_row["news_groups_name"]);
        	
        	$info_box_contents_title[] = array(	'align' => 'left',
        									'params' => 'class="latestNewsBoxHeading"',
               	    	                  	'text'  => (defined(strtoupper("LOCAL_".$local_constant_news_title)) ? constant(strtoupper("LOCAL_".$local_constant_news_title)) : $news_group_name_row["news_groups_name"]));
        	new contentBoxHeading($info_box_contents_title);
            
            //display contents
            $display_contents = '
                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                	<tbody>
                	<tr>
                		<td width="3" height="20">'. tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '20') .'</td>
                		<td background="'. DIR_WS_IMAGES . 'box_side02.gif">&nbsp;</td>
                		<td width="3" height="20">'. tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '20') .'</td>
                	</tr>
                	<tr>
                		<td width="3" background="'. DIR_WS_IMAGES . 'box_side08.gif">'. tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1') .'</td>
                		<td>
                		<!-- start repeat -->
                			<table cellpadding="2" border="0" cellspacing="0" width="100%">
                				<tbody>
                            	<tr>
                					<td>';
             	
            for($i=0; $i< sizeof($display_these) ; $i++) {
        		$display_contents .= $display_these[$i];
        	}
            
            $display_contents .= '
                					</td>
                				</tr>
                				</tbody>
                			</table>
                            <!-- end repeat -->
                        </td>
                        <td width="3" background="'. DIR_WS_IMAGES . 'box_side04.gif">'. tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1') .'</td>
                	</tr>
                	<tr>
                		<td width="3" height="16">'. tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16') .'</td>
                		<td background="'. DIR_WS_IMAGES . 'box_side06.gif">'. tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16') .'</td>
                		<td width="3" height="16">'. tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16') .'</td>
                	</tr>
                	</tbody>
                </table>';
            
            echo $display_contents;
        }
    }
}
?>
<script language="javascript">
<!--
	function showHideContent(itemID, dateTime) {
	    var content = 'content_' + itemID;
	    var clip = 'clip_' + itemID;
	    var date_time = 'datetime_' + itemID;
	    
	    if (DOMCall(content).className == 'hide') {
	        DOMCall(content).className = 'show';
	        DOMCall(clip).innerHTML = '<?=tep_image(DIR_WS_ICONS . 'icon_collapse.gif')?>';
	        DOMCall(date_time).innerHTML = "&nbsp;";
	        
	    } else {
	        DOMCall(content).className = 'hide';
	        DOMCall(date_time).innerHTML = dateTime;
	        DOMCall(clip).innerHTML = '<?=tep_image(DIR_WS_ICONS . 'icon_expand.gif')?>';
	    }
	}
//-->
</script>