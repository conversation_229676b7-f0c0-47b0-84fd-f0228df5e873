<? 
/*
  	$Id: latest_news.php,v 1.15 2010/06/16 03:12:24 henry.chow Exp $

  	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 Will Mays

  	Released under the GNU General Public License
*/
?>

<!-- latest_news //-->
<?
if(!function_exists('eval_buffer')) {
	function eval_buffer($string) {
   		ob_start();
   		eval("$string[2];");
   		$return = ob_get_contents();
   		ob_end_clean();
   		return $return;
	}
}

if(!function_exists('eval_print_buffer')) {
	function eval_print_buffer($string) {
   		ob_start();
   		eval("print $string[2];");
   		$return = ob_get_contents();
   		ob_end_clean();
   		return $return;
	}
}

if(!function_exists('eval_html')) {
	function eval_html($string) {
   		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si",
        	                           "eval_print_buffer",$string);
   		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si",
        	                         "eval_buffer",$string);
	}
}

$cat_id = end($cPath_array);

$news_groups_cat = array(" news_groups_cat_id LIKE '%,0,%' ");
foreach ($cPath_array as $cur_cat) {
	$news_groups_cat[] = " news_groups_cat_id LIKE '%,$cur_cat,%' ";
}

$news_groups_cat_where_str = implode(" OR ", $news_groups_cat);

if ($cat_id) {
	$news_cat_where_str = " IF(latest_news_include_subcat<>0, (".$news_groups_cat_where_str."), news_groups_cat_id LIKE '%,".$cat_id.",%' OR news_groups_cat_id LIKE '%,0,%')" ;
} else {
	$news_cat_where_str = '1';
}

$group_news_select_sql = "	SELECT news_groups_id
							FROM " . TABLE_LATEST_NEWS_GROUPS . "
							WHERE news_groups_id = '". $LATEST_NEWS_TYPE. "'";
$group_news_result_sql = tep_db_query($group_news_select_sql);
$group_news = tep_db_fetch_array($group_news_result_sql);

$this_site_id = 0;
if (defined('SITE_ID')) {
	$this_site_id = SITE_ID;
}
$news_display_sites_where_str = "FIND_IN_SET( '" . $this_site_id . "', news_display_sites)";

$cnt = 0;

$news_count_select_sql = "	SELECT COUNT(ln.news_id) AS total_news
							FROM " . TABLE_LATEST_NEWS . " AS ln
							INNER JOIN ".TABLE_LATEST_NEWS." AS lnd 
							 ON ln.news_id = lnd.news_id
							WHERE ln.status = '1'
								AND $news_cat_where_str
								AND $news_display_sites_where_str
								AND ln.news_groups_id = '". $group_news["news_groups_id"] ."'
								AND lnd.language_id = '". $languages_id. "'";

$news_count_result_sql = tep_db_query($news_count_select_sql);

if ($news_count_row = tep_db_fetch_array($news_count_result_sql)) {
	$cnt = $news_count_row["total_news"];
}

$limit_sql = $latest_news_settings['type'] == "classic" ? '' : ' LIMIT ' . MAX_DISPLAY_LATEST_NEWS;

if ($latest_news_settings['type'] == "classic") {
	if ($LATEST_NEWS_TYPE){
		$latest_news_query = tep_db_query('SELECT lnd.news_id, lnd.headline, lnd.latest_news_summary, lnd.content, ln.date_added, ln.news_groups_id, ln.url FROM ' . TABLE_LATEST_NEWS . " AS ln INNER JOIN ".TABLE_LATEST_NEWS_DESCRIPTION." AS lnd ON ln.news_id = lnd.news_id WHERE ln.status = '1' AND $news_display_sites_where_str AND " . $news_cat_where_str . " AND ln.news_groups_id = '". $group_news['news_groups_id'] ."' AND lnd.language_id = '". $languages_id. "' ORDER BY ln.date_added DESC " . $limit_sql);
	} else {
		$latest_news_query = tep_db_query('SELECT lnd.news_id, lnd.headline, lnd.latest_news_summary, lnd.content, ln.date_added, ln.url, ln.news_groups_id FROM ' . TABLE_LATEST_NEWS . " AS ln INNER JOIN ".TABLE_LATEST_NEWS_DESCRIPTION." AS lnd ON ln.news_id = lnd.news_id WHERE ln.status = '1' AND $news_display_sites_where_str AND " . $news_cat_where_str . " AND lnd.language_id = '". $languages_id. "' ORDER BY ln.date_added DESC " . $limit_sql);
	}
} else {
	if ($LATEST_NEWS_TYPE){
		$latest_news_query = tep_db_query('SELECT lnd.news_id, lnd.headline, ln.date_added, ln.url, ln.news_groups_id FROM ' . TABLE_LATEST_NEWS . " AS ln INNER JOIN ".TABLE_LATEST_NEWS_DESCRIPTION." ON ln.news_id = lnd.news_id WHERE ln.status = '1' AND $news_display_sites_where_str AND " . $news_cat_where_str . " AND ln.news_groups_id = '". $group_news['news_groups_id'] ."' AND " . $news_cat_where_str . " AND lnd.language_id = '". $languages_id. "' ORDER BY ln.date_added DESC " . $limit_sql);
	} else {
		$latest_news_query = tep_db_query('SELECT lnd.news_id, lnd.headline, ln.date_added, ln.url, ln.news_groups_id FROM ' . TABLE_LATEST_NEWS . " AS ln INNER JOIN ".TABLE_LATEST_NEWS_DESCRIPTION." ON ln.news_id = lnd.news_id WHERE ln.status = '1' AND $news_display_sites_where_str AND " . $news_cat_where_str . " AND " . $news_cat_where_str . " AND lnd.language_id = '". $languages_id. "' ORDER BY ln.date_added DESC " . $limit_sql);
	}
}

$info_box_contents = array();

if($LATEST_NEWS_TYPE) {
	$local_constant_news_title = preg_replace('/[^a-z]/i', '_', $LATEST_NEWS_TYPE);
	
	$info_box_contents[] = array(	'align' => 'left',
									'params' => 'class="systemBoxHeading" width="100%"',
       	    	                  	'text'  => tep_image(DIR_WS_ICONS . "icon_03.gif", $LATEST_NEWS_TYPE, '10', '10', 'hspace="4"') . (defined(strtoupper("LOCAL_".$local_constant_news_title)) ? constant(strtoupper("LOCAL_".$local_constant_news_title)) : $LATEST_NEWS_TYPE));
} else {
	$info_box_contents[] = array(	'align' => 'left',
									'params' => 'class="systemBoxHeading" background="images/box_info2.gif"',
        	                     	'text'  => tep_image(DIR_WS_ICONS . "icon_03.gif", $LATEST_NEWS_TYPE, '10', '10', 'hspace="4"').TABLE_HEADING_LATEST_NEWS);
}
new contentBoxHeading($info_box_contents);

if (!tep_db_num_rows($latest_news_query)) { // there are no news
	echo '<!-- no news -->';
} else {
    $info_box_contents = array();
    $latest_news_paging_contents = '';
    $div_read_more_bottom = '';
    
    $row = 0;
    while ($latest_news = tep_db_fetch_array($latest_news_query)) {
    	$latest_news_section_contents = '';
    	
        if ($latest_news_settings['type'] == "classic") {
        	if (tep_not_null($latest_news['latest_news_summary'])) {
        		$show_content = $latest_news['latest_news_summary'];
        		$read_more_link = ' <a href="' . tep_href_link(FILENAME_NEWS, "news_id=".$latest_news['news_id']."&news_type=".$group_news['news_groups_id']).'" class="actionLink">'.LOCAL_LATEST_NEWS_DETAILS.'</a>';
        	} else {
        		$show_content = $latest_news['content'];
        		$read_more_link = '';
        	}
        }
		
		if ($latest_news_settings['type'] == "small") {
        	$info_box_contents[$row] = array('align' => 'left',
            	                             'params' => 'valign="top" class="latestNewsTitle"',
                	                         'text' => 	'<a href="' . tep_href_link(FILENAME_NEWS, "news_type=".$group_news['news_groups_id']."&news_id=".$latest_news['news_id']) .'" class="latestNewsLink">'
                    	        	                    ."<b><div class='latestNewsDate'>".tep_date_short($latest_news['date_added'])
                        	        	                .'</div></b>&nbsp;-&nbsp;'
                            	        	            .eval_html($latest_news['headline']) . '</a><br>');
      	} else if ($latest_news_settings['type'] == "classic") {
       		if($latest_news["url"]){
       			$latest_news_section_contents = '<div class="latestNewsTitle">'.eval_html($latest_news["headline"]).'</div>'.
                     							'<div class="latestNewsDate">'.tep_date_long($latest_news["date_added"]).'</div>'
                      							.'<table border="0"><tr><td>&nbsp;&nbsp;</td><td class="latestNewsBoxContents">' . nl2br(eval_html($show_content)) . $read_more_link . '<br><br><a href=http://'.$latest_news["url"].' class="latestNewsLink">[URL]</a><br>' . '</td></tr></table>';
         		
			} else {
				$latest_news_section_contents = '<div class="latestNewsTitle">'.eval_html($latest_news["headline"]).'</div>'.
                               					'<div class="latestNewsDate">'.tep_date_long($latest_news["date_added"]).'</div>'
                               					.'<table border="0" width="100%"><tr><td>&nbsp;&nbsp;</td><td class="latestNewsBoxContents" width="100%">' . nl2br(eval_html($show_content)) . '</td></tr></table>';
			}
			
			if ($latest_news_settings['template'] == 'paging') {
				$dyn_div_id = 'news_tab_'. $group_news["news_groups_id"] . '_' . $row;
				$dyn_div_read_more_bottom_id = 'news_tab_bottom_'. $group_news["news_groups_id"] . '_' . $row;
				
				$latest_news_paging_contents .= '<div name="'.$dyn_div_id.'" id="'.$dyn_div_id.'" class="hide" style="height:120;">'.$latest_news_section_contents.'</div>';
				$div_read_more_bottom .= '<div name="'.$dyn_div_read_more_bottom_id.'" id="'.$dyn_div_read_more_bottom_id.'" class="hide">'. $read_more_link .'</div>';
			}
			$info_box_contents[$row] = array(	'align' 	=> 'left',
            		                          	'params' 	=> 'class="" valign="top"',
                                              	'text' 		=> $latest_news_section_contents);
		} else {
           	$info_box_contents[$row] = array('align' => 'left',
            	                             'params' => '',
                	                         'text' => '<table cellpadding="0" cellspacing="2" width="100%" border="0">
                                                            <tbody>
                                                                <tr>
                                                                    <td>
                                                                        <table cellpadding="0" cellspacing="0" width="100%">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td><p><a href="' . tep_href_link(FILENAME_NEWS, "news_type=".$group_news['news_groups_id']."&news_id=".$latest_news['news_id']) . '" class="systemNav">'.eval_html($latest_news['headline']).'</a></p></td>
                                                                                    <td align="right"><p><a href="' . tep_href_link(FILENAME_NEWS, "news_type=".$group_news['news_groups_id']."&news_id=".$latest_news['news_id']) . '" class="systemNav">'.tep_date_short($latest_news['date_added']).'</a></p></td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td background="images/space_line2.gif" height="1"><img alt="" src="images/space.gif" height="1" width="1"></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>');
     	}
      	$row++;
	}//end while
	
	if (isset($latest_news_settings['template']) && $latest_news_settings['template'] == 'paging') {
		$info_box_contents = array();
		
		$pagingNavHTML = '';
		for ($tabCnt=0; $tabCnt < $row; $tabCnt++) {
			$dyn_paging_id = 'news_page_'. $group_news["news_groups_id"] . '_' . $tabCnt;
			
			$pagingNavHTML .= '<a id="'.$dyn_paging_id.'" href="javascript:;" onClick="openTab('.$group_news["news_groups_id"].', '.$tabCnt.')" class="box">'.($tabCnt+1).'</a>&nbsp;&nbsp;';
		}
		
		$info_box_contents[0] = array(	'align' 	=> 'left',
    			                        'params' 	=> 'width="100%" valign="top" height="120"',
            		                    'text' 		=> $latest_news_paging_contents);
		
		//show read more link and page navigate link
		$paging_nav_bottom = '<table border="0" width="100%" cellpadding="0" cellspacing="0"><tr><td align="left" nowrap><b>'. $div_read_more_bottom .'</b></td><td align="right">'. $pagingNavHTML .'</td></tr></table>';
		
		$info_box_contents[1] = array(	'align' 	=> 'center',
										'params' 	=> 'class="" valign="top"',
            		                    'text' 		=> $paging_nav_bottom);
	}
	
	new contentBox($info_box_contents, 'class="latestNewsBox"', 'class="latestNewsBoxContents"');
    
    if (isset($latest_news_settings['template']) && $latest_news_settings['template'] == 'paging') {
?>
    	<script language="javascript">
		<!--
    		var totalTab = <?=$row?>;
    		var timerID = 0;
    		var oTab = 0;
			openTab(<?=$group_news["news_groups_id"]?>, 0);
			function openTab(gID, tabID)
			{
				if (timerID) {
					clearTimeout(timerID);
					timerID = 0;
				}
				
				oTab = tabID;
				for(tabCnt=0; tabCnt < totalTab; tabCnt++) {
					if (document.getElementById('news_tab_'+gID+'_'+tabID) != null) {
						if (tabCnt == tabID) {
							document.getElementById('news_tab_'+gID+'_'+tabCnt).className = 'show';
							if (document.getElementById('news_tab_bottom_'+gID+'_'+tabCnt) != null)	document.getElementById('news_tab_bottom_'+gID+'_'+tabCnt).className = 'show';
							if (document.getElementById('news_page_'+gID+'_'+tabCnt) != null) document.getElementById('news_page_'+gID+'_'+tabCnt).className = 'boxSelected';
						} else {
							document.getElementById('news_tab_'+gID+'_'+tabCnt).className = 'hide';
							if (document.getElementById('news_tab_bottom_'+gID+'_'+tabCnt) != null) document.getElementById('news_tab_bottom_'+gID+'_'+tabCnt).className = 'hide';
							if (document.getElementById('news_page_'+gID+'_'+tabCnt) != null) document.getElementById('news_page_'+gID+'_'+tabCnt).className = 'box';
						}
					}
				}
				
				timerID = setTimeout("openTab(<?=$group_news["news_groups_id"]?>, (oTab+1 >= totalTab ? 0 : (oTab+1)))", 10000);
			}
		//-->
		</script>
<?
    }
}
?>
<!-- latest_news_eof //-->