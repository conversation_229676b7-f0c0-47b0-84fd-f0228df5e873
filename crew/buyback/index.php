<?php
/*
  	$Id: index.php,v 1.8 2008/05/07 03:58:17 weichen Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

	Released under the GNU General Public License
*/

require('includes/application_top.php');

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_INDEX_DEFAULT;
//Define the javascript file
$javascript = 'supplier_xmlhttp.js';

//check need to show pop up onload or not.
//$customers_security_obj = new customers_security($_SESSION['sup_languages_id']);
//$customers_security_obj->set_customers_id($_SESSION['customer_id']);
$show_popup = false;
$date_diff = $customers_security_obj->get_convert_pin_expired_date();
if (tep_session_is_registered('customer_id') && (!isset($_COOKIE['popupAlert']) || !$_COOKIE['popupAlert']) && tep_not_null($date_diff) && !$customers_security_obj->check_secret_question_isset()) {
	setcookie("popupAlert", "true", time()+60*60*24*1);
	$show_popup = true;
}

$form_session_name = constant('FILENAME_DEFAULT');
if (!tep_session_is_registered($form_session_name)) {
	$$form_session_name = array();
	tep_session_register($form_session_name);
}

$action = (isset($_GET['action']) ? trim($_GET['action']) : '');
switch ($action) {
	case 'redir_2_order':
		$game_cat_id = (isset($_GET['gcid']) ? tep_db_prepare_input($_GET['gcid']) : '');
		$products_id = (isset($_GET['pid']) ? tep_db_prepare_input($_GET['pid']) : '');
		
		//if (!$game_cat_id || !$products_id) {
		if (!$game_cat_id) {
			tep_redirect(tep_href_link(FILENAME_DEFAULT, tep_get_all_get_params(array('action', 'gcid', 'pid'))));
		}
		//initialize the buyback page's session vars
		$form_session_name = constant('FILENAME_BUYBACK');
		if (!tep_session_is_registered($form_session_name)) {
			$$form_session_name = array();
			tep_session_register($form_session_name);
		}
		
		//save to session then redirect
		$_SESSION[$form_session_name]['wbb_input_game_select'] = $game_cat_id;
		$_SESSION[$form_session_name]['wbb_input_product_select'] = $products_id;
		tep_redirect(tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('action', 'gcid', 'pid'))));

		break;
}

$form_values_arr = $_SESSION[$form_session_name];

//	$num_titles = count($col_titles);
//	$favLinksHTML = '';
//	$fav_links_num_rows = 0;

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>