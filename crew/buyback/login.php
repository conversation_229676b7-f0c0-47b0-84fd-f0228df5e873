<?php
/*
  	$Id: login.php,v 1.11 2008/12/05 11:54:53 keepeng.foong Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2003 osCommerce

	Released under the GNU General Public License
*/

require('includes/application_top.php');

//redirect the customer to a friendly cookie-must-be-enabled page if cookies are disabled (or the session has not started)
if ($session_started == false) {
	tep_redirect(tep_href_link(FILENAME_COOKIE_USAGE));
}

//Define content that gets included in the center column.
$content = CONTENT_LOGIN;

$error = false;
$error_activation = false;

if (isset($HTTP_GET_VARS['action']) && ($HTTP_GET_VARS['action'] == 'process')) {
	$email_address = tep_db_prepare_input($HTTP_POST_VARS['email_address']);
    $password = tep_db_prepare_input($HTTP_POST_VARS['password']);
    $security_code = tep_db_prepare_input($HTTP_POST_VARS['security_code']);

	// Check if email exists
    $check_customer_query = tep_db_query("select customers_id as login_id, customers_firstname as login_firstname, account_activated, customers_password as login_password, customers_email_address as login_email_address, customers_default_address_id, customers_status, customers_groups_id from " . TABLE_CUSTOMERS . " where customers_status = '1' and customers_email_address = '" . tep_db_input($email_address) . "' and FIND_IN_SET( '".SITE_ID."', customers_login_sites)");

    if (!tep_db_num_rows($check_customer_query)) {
        $error = true;
    	$HTTP_GET_VARS['login'] = 'fail';
    } else {
      	$check_customer = tep_db_fetch_array($check_customer_query);
		
		// Check whether customer are allowed to login
    	if ($check_customer['account_activated'] != 1) {
    		$error_activation = true;
		// Check that password is good
   		} else if (!tep_validate_password($password, $check_customer['login_password']) || $security_code != (isset($_SESSION['sup_security_code']) ? $_SESSION['sup_security_code'] : '')) {
      	    $error = true;
        	$HTTP_GET_VARS['login'] = 'fail';
      	} else {
			if (SESSION_RECREATE == 'True') {
          		tep_session_recreate();
        	}
        	
        	if (tep_session_is_registered('password_forgotten')) {
          		tep_session_unregister('password_forgotten');
        	}

        	$customer_id = $check_customer['login_id'];
	        $login_first_name = $check_customer['login_firstname'];
	        $login_email_address = $check_customer['login_email_address'];

        	tep_session_register('customer_id');
        	tep_session_register('login_first_name');
        	tep_session_register('login_email_address');
			
   	        $_SESSION['customers_groups_id'] = $check_customer['customers_groups_id'];
			
			$vip_supplier_groups_id = 1;
   	        $get_vip = "SELECT vip_supplier_groups_id FROM " . TABLE_CUSTOMERS_VIP . " WHERE customers_id='" . $customer_id . "'";
   	        $get_vip_result = tep_db_query($get_vip);
   	        if($get_vip_row = tep_db_fetch_array($get_vip_result)){
   	        	$vip_supplier_groups_id = $get_vip_row['vip_supplier_groups_id'];
   	        }
   	        tep_session_register('vip_supplier_groups_id');
			
			$pref_array = tep_get_supplier_pref_setting($customer_id);
			if (count($pref_array)) {
	    		$lang_code_select_sql = "SELECT directory FROM " . TABLE_SUPPLIER_LANGUAGES . " WHERE languages_id = '". $pref_array[KEY_SP_LANG] . "'";
	    		$lang_code_result_sql = tep_db_query($lang_code_select_sql);
				if ($lang_code_row = tep_db_fetch_array($lang_code_result_sql)) {
					$sup_language = $lang_code_row['directory'];
	    			$sup_languages_id = $pref_array[KEY_SP_LANG];
				}
			}
			
			$customer_login_sql_data_array = array(	'customers_id' => tep_db_prepare_input($customer_id),
													'customers_login_date' => 'now()',
													'customers_login_ua_info' => (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : getenv('HTTP_USER_AGENT')),
													'customers_login_ip' => tep_db_prepare_input(tep_get_ip_address()));
			tep_db_perform(TABLE_CUSTOMERS_LOGIN_IP_HISTORY, $customer_login_sql_data_array);
			
			// Remember me feature
			if (isset($_REQUEST['remember_me'])) {
		    	// Check to see if the 'setcookie' box was ticked to remember the user
		    	setcookie("ogm_cn[un]", $login_email_address, time() + (60*60*24*7));    // Sets the cookie username
		    	setcookie("ogm_cn[uc]", md5($login_email_address.':'.$check_customer['login_password']), time() + (60*60*24*7));    // Sets the cookie password
		    }
		    
	        $stored_user_query = tep_db_query("select last_page_url, last_cpath from " . TABLE_CUSTOMERS_LAST_CPATH . " where customers_id = '" . (int)$customer_id . "'");
	        $stored_user = tep_db_fetch_array($stored_user_query);

	        if (count($navigation->snapshot) > 0) {
	        	$origin_href = tep_href_link($navigation->snapshot['page'], tep_array_to_string($navigation->snapshot['get'], array(tep_session_name())), $navigation->snapshot['mode']);

	          	$navigation->clear_snapshot();
	          	/*if($stored_user['last_cpath']){
	          		$path = tep_href_link("index.php","cPath=".$stored_user['last_cpath']);
	          	} else {
	          		*/
	          		$path = $origin_href;
	          	//}
	          	tep_redirect($path);
	        } else {
	          	if($stored_user['last_cpath']){
	          		$path = tep_href_link("index.php","cPath=".$stored_user['last_cpath']);
	          	} else {
	          	    $path = tep_href_link(FILENAME_DEFAULT);
	          	}
	          	tep_redirect($path);
			}
      	}
	}
}
if (isset($error) && $error == true) {
	$messageStack->add_session($content, TEXT_LOGIN_ERROR);
	tep_redirect(tep_href_link(FILENAME_LOGIN));
} else if (isset($error_activation) && $error_activation == true) {
	$messageStack->add_session(CONTENT_ACTIVATE_ACCOUNT, TEXT_LOGIN_EMAIL_ACTIVATE_ERROR);
	tep_redirect(tep_href_link(FILENAME_ACTIVATE_ACCOUNT, 'email_address='.$email_address.'&action=ac'));
}

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_LOGIN, '', 'SSL'));

//Define the javascript file
$javascript = $content . '.js';

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>