<?php
/*
  	$Id: my_account.php,v 1.4 2008/05/06 11:38:17 weichen Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

	Released under the GNU General Public License
*/

require('includes/application_top.php');

if (!tep_session_is_registered('customer_id')) {
	$messageStack->add_session(CONTENT_LOGIN, TEXT_MUST_LOGIN);
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_ACCOUNT;
//Define the javascript file
$javascript = '';

//$customers_security_obj = new customers_security($_SESSION['sup_languages_id']);
//$customers_security_obj->set_customers_id($_SESSION['customer_id']);
$date_diff = $customers_security_obj->get_convert_pin_expired_date();

$select_customer_group = "	SELECT cv.vip_supplier_groups_id, cv.vip_rank_id, cv.vip_buyback_cummulative_point, ab.entry_country_id
							FROM " . TABLE_CUSTOMERS_VIP . " AS cv
							INNER JOIN " . TABLE_ADDRESS_BOOK . " AS ab
								ON(cv.customers_id = ab.customers_id)
							 WHERE cv.customers_id='" . $_SESSION['customer_id'] . "'";
$select_customer_group_result = tep_db_query($select_customer_group);
$select_customer_group_row = tep_db_fetch_array($select_customer_group_result);


$group_rank_array = array();
//get all status in ASC VVIP, VIP, Member
$select_supplier_groups = "	SELECT vip_supplier_groups_id, vip_supplier_groups_name 
							FROM " . TABLE_VIP_SUPPLIER_GROUPS . " 
							WHERE language_id = '".tep_db_input($languages_id)."' 
							ORDER BY vip_supplier_groups_rank ASC";
$select_supplier_groups_result = tep_db_query($select_supplier_groups);
while($select_supplier_groups_row = tep_db_fetch_array($select_supplier_groups_result)){
	$group_rank_array[$select_supplier_groups_row['vip_supplier_groups_id']]['name'] = $select_supplier_groups_row['vip_supplier_groups_name'];
}

//get all rank in ASC VVIP, VIP, Member
$select_rank_sql = "SELECT vr.* 
					FROM " . TABLE_VIP_RANK . " AS vr
					INNER JOIN " . TABLE_VIP_REGION_GROUP ." AS vrg
						ON(vr.vip_region_group_id=vrg.vip_region_group_id)
					WHERE FIND_IN_SET('" . $select_customer_group_row['entry_country_id'] . "', countries_ids)
					ORDER BY vr.vip_rank_sort_order ASC";			
$select_rank_sql_result = tep_db_query($select_rank_sql);
$total_rank = tep_db_num_rows($select_rank_sql_result);
while ($select_rank_sql_row = tep_db_fetch_array($select_rank_sql_result)) {
	 $supplier_group = explode("_", $select_rank_sql_row['vip_rank_action']);
	 $supplier_group = $supplier_group[(count($supplier_group)-1)];
	 $group_rank_array[$supplier_group]['rank'][$select_rank_sql_row['vip_rank_id']] = array(	'name' => $select_rank_sql_row['vip_rank_name'],
										 														'upgrade_point' => $select_rank_sql_row['vip_rank_upgrade_point'],
										 														'downgrade_point' => $select_rank_sql_row['vip_rank_downgrade_point'],
										 														'percentage' => $select_rank_sql_row['vip_rank_percentage']
										 														);
}

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);
require(DIR_WS_INCLUDES . 'application_bottom.php');
?>