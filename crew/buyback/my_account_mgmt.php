<?php
/*
  	$Id: my_account_mgmt.php,v 1.16 2009/05/13 02:22:39 keepeng.foong Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

	Released under the GNU General Public License
*/
require('includes/application_top.php');
include_once(DIR_WS_LANGUAGES . $sup_language . '/' . FILENAME_SUPPLIER_SIGNUP);
require_once(DIR_WS_CLASSES . 'log.php');
require_once(DIR_WS_FUNCTIONS . 'user_agent.php');
require_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');

if (!tep_session_is_registered('customer_id')) {
	$messageStack->add_session(CONTENT_LOGIN, TEXT_MUST_LOGIN);
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

define('HEADING_TITLE', HEADER_TITLE_MY_ACCOUNT_MGMT);
define('NAVBAR_TITLE', HEADER_TITLE_MY_ACCOUNT_MGMT);

define('SIGNUP_DEFAULT_COUNTRY_ID', 44);	// Default to China

$log_object = new log_files($customer_id);

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_ACCOUNT_MGMT;

require(DIR_WS_CLASSES . 'payment_module_info.php');

$pm_object = new payment_module_info($customer_id, 'customers');
//initialise customer security class
//$customers_security_obj = new customers_security($_SESSION['sup_languages_id']);
//$customers_security_obj->set_customers_id($_SESSION['customer_id']);
$ori_contact_no = $customers_security_obj->get_customer_hidden_contact_no();
//get the expired day for changing the Q&A
$day_diff = $customers_security_obj->get_convert_pin_expired_date();

$action = (isset($_REQUEST['action']) ? $_REQUEST['action'] : '');
$subaction = (isset($_REQUEST['subaction']) ? $_REQUEST['subaction'] : '');
$form_content = '';
switch($subaction) {
	case "insert_pm":
		$errorCount = 0;
		$errorCount = $customers_security_obj->form_validation_edit_profile($_POST);
		if ($errorCount > 0) {
			tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params(array('subaction')) . (isset($_GET['page']) ? 'page=' . $_GET['page'] : '') ));
		} else {
			$subaction_res = $pm_object->insert_payment_account($HTTP_POST_VARS, $messageStack, $content);
			if ($subaction_res) {
				tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params(array('action', 'subaction')) . (isset($_GET['page']) ? 'page=' . $_GET['page'] : '')));
			} else {
				;
			}
		}

		break;
	case "update_pm":
		$errorCount = 0;
		$errorCount = $customers_security_obj->form_validation_edit_profile($_POST);
		if ($errorCount > 0) {
			tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params(array('subaction')) . (isset($_GET['page']) ? 'page=' . $_GET['page'] : '') ));
		} else {
			$subaction_res = $pm_object->update_payment_account($HTTP_POST_VARS, $messageStack, $content);
			if ($subaction_res) {
				tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params(array('action', 'subaction')) . (isset($_GET['page']) ? 'page=' . $_GET['page'] : '') ));
			} else {
				;
			}
		}

		break;
	case "ask_delete_pm":
		//ask for pin number
		if (isset($_GET['book_id']) && tep_not_null($_GET['book_id'])) {
			$form_content = $pm_object->confirm_delete_payment_account(FILENAME_MY_ACCOUNT_MGMT, $_GET['book_id']);
		} else {
			tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params(array('action', 'subaction', 'book_id')) . (isset($_GET['page']) ? 'page=' . $_GET['page'] : '') ));
		}
		break;
	case "confirm_delete_pm":
		$errorCount = 0;
		$errorCount = $customers_security_obj->form_validation_edit_profile($_POST);
		if ($errorCount > 0) {
			tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params(array('subaction')) .'subaction=ask_delete_pm&'. (isset($_GET['page']) ? 'page=' . $_GET['page'] : '') ));
		} else {
			$pm_object->delete_payment_account($HTTP_GET_VARS['book_id'], $messageStack, $content);
			tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params(array('action', 'subaction', 'book_id')) . (isset($_GET['page']) ? 'page=' . $_GET['page'] : '') ));
		}
		break;
	default:
		break;
}

function get_country_states($country_id) {
	global $form_values_arr, $COUNTRY_STATE_CHINESE_ARRAY;
	$states_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
  	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$country_id . "' ORDER BY zone_name";
   	$zones_result_sql = tep_db_query($zones_select_sql);
   	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
   	    $states_array[] = array('id' => $zones_row['zone_id'], 'text' => isset($COUNTRY_STATE_CHINESE_ARRAY[$zones_row['zone_id']]) ? $COUNTRY_STATE_CHINESE_ARRAY[$zones_row['zone_id']] : $zones_row['zone_name']);
   	}
   	return $states_array;
}

function validate_tab_0($val_arr, $update_session=true) {
	global $log_object, $messageStack, $form_session_name, $content, $get_params_always_exclude_array, $customers_security_obj;
	$errorCount = 0;
	
	if (!trim($val_arr['email_address']) ) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_EMAIL_ADDRESS));
		$errorCount++;
	} else {
		if ($val_arr['email_address'] != $_SESSION[$form_session_name]['email_address']) {
			if (tep_validate_email($val_arr['email_address']) == false) {
		      	$messageStack->add_session($content, ERROR_EMAIL_ADDRESS_INVALID);
		      	$errorCount++;
			}
		}
		
		$check_email_query = tep_db_query("select count(*) as total from " . TABLE_CUSTOMERS . " where customers_email_address = '" . tep_db_input($val_arr['email_address']) . "' and customers_id != '" . (int)$_SESSION['customer_id'] . "'");
    	$check_email = tep_db_fetch_array($check_email_query);
	    if ($check_email['total'] > 0) {
	    	$messageStack->add_session($content, ENTRY_EMAIL_ADDRESS_ERROR_EXISTS);
	      	$errorCount++;
	    }
	}
	
	if (!trim($val_arr['lastname'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_LAST_NAME));
		$errorCount++;
	}
	
	if (!trim($val_arr['firstname'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_FIRST_NAME));
		$errorCount++;
	}
	
	if (!trim($val_arr['dob_month']) || !trim($val_arr['dob_day']) || !trim($val_arr['dob_year'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_DATE_OF_BIRTH));
		$errorCount++;
	}
	
	if (!trim($val_arr['gender'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_GENDER));
		$errorCount++;
	}
	
	if (!trim($val_arr['address'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_ADDRESS));
		$errorCount++;
	}
	
	if (!trim($val_arr['city'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CITY));
		$errorCount++;
	}
	
	if (!trim($val_arr['state'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_STATE));
		$errorCount++;
	}
	
	$val_arr['country'] = SIGNUP_DEFAULT_COUNTRY_ID;
	/*
	if (!trim($val_arr['country'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_COUNTRY));
		$errorCount++;
	}
	*/
	
	$ori_contact_no = $customers_security_obj->get_customer_hidden_contact_no();
	if (!trim($val_arr['contact_no'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CONTACT_NO));
		$errorCount++;
	} else if (!is_numeric($val_arr['contact_no'])) {
		if ($val_arr['contact_no'] != (substr($ori_contact_no['customers_telephone'], 0, -4).'****')) {
			$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_CONTACT_NO));
			$errorCount++;
		} else {
			$val_arr['contact_no'] = $ori_contact_no['customers_telephone'];
		}
	}
	
	if (!trim($val_arr['mobile_no'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_MOBILE_NO));
		$errorCount++;
	} else if (!is_numeric($val_arr['mobile_no'])) {
		if ($val_arr['mobile_no'] != (substr($ori_contact_no['customers_mobile'], 0, -4) . '****')) {
			$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_MOBILE_NO));
			$errorCount++;
		} else {
			$val_arr['mobile_no'] = $ori_contact_no['customers_mobile'];
		}
	}
	
	if (!trim($val_arr['qq_no'])) {
	    $messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_QQ_NO));
		$errorCount++;
	} else if (!is_numeric($val_arr['qq_no'])) {
		$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_QQ_NO));
		$errorCount++;
	}
	
	if (trim($val_arr['msn_address']) && tep_validate_email($val_arr['msn_address']) == false) {
      	$messageStack->add_session($content, ERROR_MSN_ADDRESS_INVALID);
      	$errorCount++;
	}
	
	if (trim($val_arr['yahoo_address']) && tep_validate_email($val_arr['yahoo_address']) == false) {
      	$messageStack->add_session($content, ERROR_YAHOO_ADDRESS_INVALID);
      	$errorCount++;
	}
	
	if (trim($val_arr['icq_no']) && !is_numeric($val_arr['icq_no'])) {
      	$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_ICQ_NO));
      	$errorCount++;
	}
	/*
	if (trim($val_arr['fax_no']) && !is_numeric($val_arr['fax_no']))  { //Optional field
		$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_FAX_NO));
		$errorCount++;
	}
	*/
		
	// Hardcode as requested
	$val_arr[KEY_SP_TIME_ZONE] = 'Etc/GMT-8';
	$val_arr[KEY_SP_LANG] = 2;	// Chinese language
	/*
	if (!trim($val_arr[KEY_SP_TIME_ZONE])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_TIME_ZONE));
		$errorCount++;
	}
	if (!trim($val_arr[KEY_SP_LANG])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_LANGUAGE));
		$errorCount++;
	}
	*/
	
	//validate the security answer
	$errorCount += $customers_security_obj->form_validation_edit_profile($val_arr);
	
	if ($update_session) {
		$_SESSION[$form_session_name]['firstname'] = tep_db_prepare_input($val_arr['firstname']);
		$_SESSION[$form_session_name]['lastname'] = tep_db_prepare_input($val_arr['lastname']);
		$_SESSION[$form_session_name]['dob_month'] = tep_db_prepare_input($val_arr['dob_month']);
		$_SESSION[$form_session_name]['dob_day'] = tep_db_prepare_input($val_arr['dob_day']);
		$_SESSION[$form_session_name]['dob_year'] = tep_db_prepare_input($val_arr['dob_year']);
		$_SESSION[$form_session_name]['gender'] = tep_db_prepare_input($val_arr['gender']);
		$_SESSION[$form_session_name]['address'] = tep_db_prepare_input($val_arr['address']);
		$_SESSION[$form_session_name]['suburb'] = tep_db_prepare_input($val_arr['suburb']);
		$_SESSION[$form_session_name]['postcode'] = tep_db_prepare_input($val_arr['postcode']);
		$_SESSION[$form_session_name]['city'] = tep_db_prepare_input($val_arr['city']);
		$_SESSION[$form_session_name]['state'] = tep_db_prepare_input($val_arr['state']);
		$_SESSION[$form_session_name]['country'] = tep_db_prepare_input($val_arr['country']);
		$_SESSION[$form_session_name]['contact_no'] = tep_db_prepare_input($val_arr['contact_no']);
		$_SESSION[$form_session_name]['mobile_no'] = tep_db_prepare_input($val_arr['mobile_no']);
		$_SESSION[$form_session_name]['fax_no'] = tep_db_prepare_input($val_arr['fax_no']);
		$_SESSION[$form_session_name]['qq_no'] = tep_db_prepare_input($val_arr['qq_no']);
		$_SESSION[$form_session_name]['msn_address'] = tep_db_prepare_input($val_arr['msn_address']);
		$_SESSION[$form_session_name]['yahoo_address'] = tep_db_prepare_input($val_arr['yahoo_address']);
		$_SESSION[$form_session_name]['icq_no'] = tep_db_prepare_input($val_arr['icq_no']);
		$_SESSION[$form_session_name]['email_address'] = tep_db_prepare_input($val_arr['email_address']);
		$_SESSION[$form_session_name][KEY_SP_TIME_ZONE] = tep_db_prepare_input($val_arr[KEY_SP_TIME_ZONE]);
		$_SESSION[$form_session_name][KEY_SP_LANG] = tep_db_prepare_input($val_arr[KEY_SP_LANG]);
		for ($answer = 1; $answer <= 2; $answer++) {
			$_SESSION[$form_session_name]['answer_'.$answer] = tep_db_prepare_input($val_arr['answer_'.$answer]);
		}
	}
	
	if ($errorCount > 0) {
		return $errorCount;
	}
	
	//Start saving now
	$form_values_arr = $_SESSION[$form_session_name];
	
	$states_array = get_country_states((int)$form_values_arr['country']);
	//First row is 'Please select'. If country has zones in our db, select list was used. so value of 'state' is rowid. Otherwise, value is user defined string.
	if (count($states_array) > 1) {
		$state_str = '';
		$zone_id = $form_values_arr['state'];
	} else {
		$state = $form_values_arr['state'];
		$zone_id = '0';
	}
	$customer_log_select_sql = "SELECT customers_gender, customers_firstname, customers_lastname, customers_dob, 
									customers_email_address, customers_telephone, customers_mobile, customers_qq, customers_msn, customers_yahoo, customers_icq 
								FROM " . TABLE_CUSTOMERS . " WHERE customers_id='" . tep_db_input($_SESSION['customer_id']) . "'";
	$customer_log_result_sql = tep_db_query($customer_log_select_sql);
	$customer_old_log_row = tep_db_fetch_array($customer_log_result_sql);
	
	$update_user_arr = array(	'customers_gender' 		=> strtolower($form_values_arr['gender']),
								'customers_firstname'	=> $form_values_arr['firstname'],
								'customers_lastname'	=> $form_values_arr['lastname'],
								'customers_dob' 		=> $form_values_arr['dob_year'] . '-' . $form_values_arr['dob_month'] . '-' . $form_values_arr['dob_day'],
								'customers_email_address'=> $form_values_arr['email_address'],
								'customers_telephone' 	=> $form_values_arr['contact_no'],
								'customers_mobile' 		=> $form_values_arr['mobile_no'],
								'customers_qq' 			=> $form_values_arr['qq_no'],
								'customers_msn' 		=> $form_values_arr['msn_address'],
								'customers_yahoo' 		=> $form_values_arr['yahoo_address'],
								'customers_icq' 		=> $form_values_arr['icq_no']
								);
	$success_update_customer = tep_db_perform(TABLE_CUSTOMERS, $update_user_arr, 'update', "customers_id = '{$_SESSION['customer_id']}'");
	
	if (!$success_update_customer) {
		//Signup failed, Go back.
		$messageStack->add_session($content, TEXT_UPDATE_FAILED);
		tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=0'));
		exit;
	}
	$customer_log_result_sql = tep_db_query($customer_log_select_sql);
	$customer_new_log_row = tep_db_fetch_array($customer_log_result_sql);
	
	$customer_changes_array = $log_object->detect_changes($customer_old_log_row, $customer_new_log_row);
	
	$customer_address_log_select_sql = "SELECT entry_street_address, entry_city, entry_state, entry_country_id, entry_zone_id 
										FROM " . TABLE_ADDRESS_BOOK . " WHERE customers_id='" . tep_db_input($_SESSION['customer_id']) . "' 
											and address_book_id = '" . tep_db_input($form_values_arr['customers_default_address_id']) . "'";
	$customer_address_log_result_sql = tep_db_query($customer_address_log_select_sql);
	$customer_address_old_log_row = tep_db_fetch_array($customer_address_log_result_sql);
	
	if ((int)$customer_address_old_log_row["entry_zone_id"] > 0) {
		$customer_address_old_log_row["entry_state"] = tep_get_zone_name((int)$customer_address_old_log_row["entry_country_id"], (int)$customer_address_old_log_row["entry_zone_id"], '');
		unset($customer_address_old_log_row["entry_zone_id"]);
	}
	
	$update_address_arr = array('entry_gender' => strtolower($form_values_arr['gender']),
								'entry_firstname' => $form_values_arr['firstname'],
								'entry_lastname' => $form_values_arr['lastname'],
								'entry_street_address' => $form_values_arr['address'],
								'entry_city' => $form_values_arr['city'],
								'entry_state' => $state_str,
								'entry_country_id' => $form_values_arr['country'],
								'entry_zone_id' => $zone_id
								);
	$success_update_addbook = tep_db_perform(TABLE_ADDRESS_BOOK, $update_address_arr, 'update', "address_book_id='{$form_values_arr['customers_default_address_id']}'");
	
	if (!$success_update_addbook) {
		//Signup failed, Go back.
		$messageStack->add_session($content, TEXT_UPDATE_FAILED);
		tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=0'));
		exit;
	}
	
	$customer_address_log_result_sql = tep_db_query($customer_address_log_select_sql);
	$customer_address_new_log_row = tep_db_fetch_array($customer_address_log_result_sql);
	
	if ((int)$customer_address_new_log_row["entry_zone_id"] > 0) {
		$customer_address_new_log_row["entry_state"] = tep_get_zone_name((int)$customer_address_new_log_row["entry_country_id"], (int)$customer_address_new_log_row["entry_zone_id"], '');
		unset($customer_address_new_log_row["entry_zone_id"]);
	}
	
	$customer_address_changes_array = $log_object->detect_changes($customer_address_old_log_row, $customer_address_new_log_row);
	
	$overall_customer_changes_array = array_merge($customer_changes_array, $customer_address_changes_array);
	$customer_changes_formatted_array = $log_object->construct_log_message($overall_customer_changes_array);
	
	if (count($customer_changes_formatted_array)) {
		$changes_str = 'Changes made:' . "\n";
		for ($i=0; $i < count($customer_changes_formatted_array); $i++) {
			if (count($customer_changes_formatted_array[$i])) {
				foreach($customer_changes_formatted_array[$i] as $field => $res) {
					if (isset($res['plain_result']) && $res['plain_result'] == '1') {
						$changes_str .= $res['text'] . "\n";
					} else {
						$changes_str .= '<b>' . $res['text'] . '</b>: ' . $res['from'] . (trim($res['to']) != '' ? ' --> ' . $res['to'] : '') . "\n";
					}
				}
			}
		}
		$log_object->insert_customer_history_log($_SESSION['login_email_address'], $changes_str);
	}
	
	// save supplier preferences
	$preferences_setting_array = array(KEY_SP_TIME_ZONE => $form_values_arr[KEY_SP_TIME_ZONE], KEY_SP_LANG => $form_values_arr[KEY_SP_LANG]);
	tep_db_query("DELETE FROM " . TABLE_USER_SETTING . " WHERE user_setting_user_id = '" . tep_db_input($_SESSION['customer_id']) . "' AND user_setting_key IN ('".implode("', '", array_keys($preferences_setting_array))."')");
	foreach ($preferences_setting_array as $preference_key => $preference_value) {
		$insert_preference_arr = array(	'user_setting_id' => '',
										'user_setting_user_id' => $_SESSION['customer_id'],
										'user_setting_key' => $preference_key,
										'user_setting_value' => $preference_value
										);
		$success_create_preference = tep_db_perform(TABLE_USER_SETTING, $insert_preference_arr);
	}
	$customers_security_obj->reset_security_counter();
	
	$messageStack->add_session($content, TEXT_UPDATE_SUCCESS, 'success');
	tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=0'));
}

function validate_tab_1($val_arr, $update_session=true) {
	global $messageStack, $form_session_name, $content, $customers_security_obj;
	$errorCount = 0;
	
	if (!trim($val_arr['current_password'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CURRENT_PASSWORD));
		$errorCount++;
	} elseif (!trim($val_arr['password'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_NEW_PASSWORD));
		$errorCount++;
	} elseif (!trim($val_arr['confirm_password'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CONFIRM_PASSWORD));
		$errorCount++;
	} elseif (trim($val_arr['password']) != trim($val_arr['confirm_password'])) {
		$messageStack->add_session($content, TEXT_NEW_PASSWORD_NOTMATCH);
		$errorCount++;
	} elseif (strlen(trim($val_arr['password'])) < (int)ENTRY_PASSWORD_MIN_LENGTH) {
		$messageStack->add_session($content, ENTRY_PASSWORD_ERROR);
		$errorCount++;
	}
	
	// check security answer
	$errorCount += $customers_security_obj->form_validation_edit_profile($val_arr);
	
	//Double check current password
	$password_select_sql = "select customers_password from ".TABLE_CUSTOMERS." where customers_id = '{$_SESSION['customer_id']}'";
	$password_result_sql = tep_db_query($password_select_sql);
	if ($password_result_row = tep_db_fetch_array($password_result_sql)) {
		if (!tep_validate_password($val_arr['current_password'], $password_result_row['customers_password'])) {
			$messageStack->add_session($content, TEXT_INVALID_CURRENT_PASSWORD);
			$errorCount++;
		}
	}
	
	if ($errorCount > 0) {
		return $errorCount;
	}
	
	$update_password_arr = array('customers_password' => tep_encrypt_password(tep_db_prepare_input($val_arr['password'])));
	tep_db_perform(TABLE_CUSTOMERS, $update_password_arr, 'update', "customers_id='{$_SESSION['customer_id']}'");
	$customers_security_obj->reset_security_counter();
	$messageStack->add_session($content, TEXT_UPDATE_SUCCESS, 'success');
	tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=1'));
}

function validate_tab_2($val_arr, $update_session=true) {
	global $messageStack, $form_session_name, $content, $form_values_arr;
	$errorCount = 0;

	//Skip validation if clicked update without adding any payment method
	if (count($val_arr['payment_method'])) {
		$_SESSION[$form_session_name]['payment_method'] = array();

		foreach ($val_arr['payment_method'] as $rownum => $row_payment_method_array) {
			foreach ($row_payment_method_array as $row_payment_method_id => $row_payment_method_field_array) {
				foreach ($row_payment_method_field_array as $row_payment_method_field_id => $row_payment_method_field_value) {
					if (!trim($row_payment_method_field_value)) {
						//Check if this field is mandatory only if empty.
						$payment_methods_fields_array = tep_get_payment_methods_fields($row_payment_method_id);
						if ((int)$payment_methods_fields_array[$row_payment_method_field_id]['payment_methods_fields_required']) {
							$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, $payment_methods_fields_array[$row_payment_method_field_id]['payment_methods_fields_title']));
							$errorCount++;
						}
					}
					if ($update_session) {
						$_SESSION[$form_session_name]['payment_method'][$rownum][$row_payment_method_id][$row_payment_method_field_id] = tep_db_prepare_input($row_payment_method_field_value);
					}
				} // end payment method field

			} //end payment method

		} //end each row
	} else {
		//Didnt provide even 1 bank account
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_BANK_ACCOUNTS));
		$errorCount++;
	}
	if ($errorCount > 0) {
		return $errorCount;
	}
	
	//Delete current data so we can insert from scratch
	$payment_methods_delete_sql = "DELETE FROM ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS."
									WHERE store_payment_account_book_id IN (
										SELECT store_payment_account_book_id FROM ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK."
										WHERE user_id = '{$_SESSION['customer_id']}' AND user_role = 'customers'
									)";
	$payment_methods_delete_success = tep_db_query($payment_methods_delete_sql);
	$payment_methods_delete_sql = "DELETE FROM ".TABLE_STORE_PAYMENT_ACCOUNT_BOOK."
									WHERE user_id = '{$_SESSION['customer_id']}' AND user_role = 'customers'
									";
	$payment_methods_delete_success = tep_db_query($payment_methods_delete_sql);
	
	if ($payment_methods_delete_success) {
		//Save payment methods
		foreach ($form_values_arr['payment_method'] as $rownum => $row_payment_method_array) {
			foreach ($row_payment_method_array as $row_payment_method_id => $row_payment_method_field_array) {
				$insert_root_setting_arr = array(
					'user_id' => $_SESSION['customer_id'],
					'user_role'	=> 'customers',
					'payment_methods_id' => $row_payment_method_id,
					'payment_methods_alias'	=> '',
					'store_payment_account_book_primary' => 0);
				$success_create_root_setting = tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK, $insert_root_setting_arr);
				$root_setting_id = tep_db_insert_id();
				$store_payment_account_book_primary = '';
				$payment_methods_alias = '';
				foreach ($row_payment_method_field_array as $row_payment_method_field_id => $row_payment_method_field_value) {
					switch ($row_payment_method_field_id) {
						case 'alias':
							$payment_methods_alias = $row_payment_method_field_value;
							break;
						case 'is_primary':
							if ((int)$row_payment_method_field_value) {
								$store_payment_account_book_primary = $row_payment_method_field_value;
							}
							break;
						default:
							$insert_child_setting_arr = array(
								'store_payment_account_book_id' => $root_setting_id,
								'payment_methods_fields_id' => $row_payment_method_field_id,
								'payment_methods_fields_value' => tep_db_prepare_input($row_payment_method_field_value)
							);
							$success_create_child_setting = tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS, $insert_child_setting_arr);
					}
				}//end for each field
				$update_root_setting_arr = array(
					'payment_methods_alias'	=> $payment_methods_alias,
					'store_payment_account_book_primary' => $store_payment_account_book_primary);
				$success_create_root_setting = tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK, $update_root_setting_arr, 'update', "store_payment_account_book_id='$root_setting_id'");
			}//end for each payment method
		}//end for each row
	}//end if
}

// for reset and current exisiting customer to change from pin to Q&A or reset Q&A
function validate_tab_3($val_arr, $update_session=true) {
	global $messageStack, $form_session_name, $content, $customers_security_obj, $get_params_always_exclude_array;
	$errorCount = 0;
	$errorCount = $customers_security_obj->form_validation($val_arr);
	
	if (!trim($val_arr['pin_number'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_PIN_NUMBER));
		$errorCount++;
	} else if (!tep_check_pin_number($val_arr['pin_number'])) {
		$messageStack->add_session($content, TEXT_INVALID_PIN_NUMBER);
		$errorCount++;
	}
	
	if ($errorCount > 0) {
		return $errorCount;
	}
	
	// no error then clear the pin and add the secret Q&A to our DB
	if (!$customers_security_obj->check_secret_question_isset() || $customers_security_obj->check_secret_question_is_reset()) {
		// save security Q&A
		$secret_question_array = array();
		$secret_answer_array = array();
		for ($ans_cnt = 1; $ans_cnt <= 3; $ans_cnt++) {
			$secret_question_array[] = $customers_security_obj->get_question_text($val_arr['question_'.$ans_cnt]);
			$secret_answer_array[] = $val_arr['answer_'.$ans_cnt];	
		}
		
		if ($customers_security_obj->set_customer_security_answer($secret_question_array, $secret_answer_array, ($customers_security_obj->check_secret_question_is_reset() ? 'update' : 'insert')) !== FALSE) {
			//remove pin code 
			$remove_pin_update_sql = "UPDATE " . TABLE_CUSTOMERS . " SET customers_pin_number = '' WHERE customers_id='" . tep_db_input($_SESSION['customer_id']) . "'";
			tep_db_query($remove_pin_update_sql);
			$messageStack->add_session($content, TEXT_QUESTION_AND_ANSWER_IS_SET, 'success');
			tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=1'));
		} else {
			$messageStack->add_session($content, TEXT_UPDATE_FAILED, 'error');
			tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=3'));
		}
	} else {
		$messageStack->add_session($content, TEXT_QUESTION_AND_ANSWER_IS_ALREADY_SET);
		tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=1'));
	}
}

//Here are the tabs. Using the array keys as the step number
$tabs_array = array(TEXT_PERSONAL_DETAILS, TEXT_CHANGE_PASSWORD, TEXT_BANK_ACCOUNTS);
if (tep_not_null($customers_security_obj->convert_start_date) && (!$customers_security_obj->check_secret_question_isset() || $customers_security_obj->check_secret_question_is_reset())) {
	$tabs_array[] = TEXT_SETTING_SECRET_QUESTION;
}
$get_params_always_exclude_array = array('current_step', 'action', 'remove_row_num');

//Creating session key based on filename so we know where it got its values.
$form_session_name = FILENAME_MY_ACCOUNT_MGMT;

//tep_session_unregister($form_session_name);
if (!tep_session_is_registered($form_session_name)) {
	//tep_session_unregister($form_session_name);
	$$form_session_name = array();
	tep_session_register($form_session_name);
	
	//Populate the session with the db values
	$existing_customer_select_sql = "SELECT * FROM " . TABLE_CUSTOMERS. " WHERE customers_id = '". $_SESSION['customer_id'] ."'";
	$existing_customer_result_sql = tep_db_query($existing_customer_select_sql);
	if (tep_db_num_rows($existing_customer_result_sql) > 0) {
		if ($existing_customer_row = tep_db_fetch_array($existing_customer_result_sql)) {
			$_SESSION[$form_session_name]['firstname'] = $existing_customer_row['customers_firstname'];
			$_SESSION[$form_session_name]['lastname'] = $existing_customer_row['customers_lastname'];
			$_SESSION[$form_session_name]['gender'] = $existing_customer_row['customers_gender'];
			$_SESSION[$form_session_name]['contact_no'] = $existing_customer_row['customers_telephone'];
			$_SESSION[$form_session_name]['fax_no'] = $existing_customer_row['customers_fax'];
			$_SESSION[$form_session_name]['qq_no'] = $existing_customer_row['customers_qq'];
			$_SESSION[$form_session_name]['msn_address'] = $existing_customer_row['customers_msn'];
			$_SESSION[$form_session_name]['yahoo_address'] = $existing_customer_row['customers_yahoo'];
			$_SESSION[$form_session_name]['icq_no'] = $existing_customer_row['customers_icq'];
			$_SESSION[$form_session_name]['mobile_no'] = preg_replace('/[^\d]/', '', $existing_customer_row['customers_mobile']);
			$_SESSION[$form_session_name]['email_address'] = $existing_customer_row['customers_email_address'];
			$_SESSION[$form_session_name]['customers_default_address_id'] = $existing_customer_row['customers_default_address_id'];
			
			$customers_dob = $existing_customer_row['customers_dob'];
			list($customers_dob_date, ) = explode(' ', $customers_dob);
			list($_SESSION[$form_session_name]['dob_year'], $_SESSION[$form_session_name]['dob_month'], $_SESSION[$form_session_name]['dob_day']) = explode('-', $customers_dob_date);
			
			$address_book_select_sql = "SELECT entry_street_address, entry_suburb, entry_postcode, entry_city, entry_state, entry_zone_id, entry_country_id FROM ".TABLE_ADDRESS_BOOK." WHERE address_book_id='{$_SESSION[$form_session_name]['customers_default_address_id']}'";
			$address_book_result_sql = tep_db_query($address_book_select_sql);
			if ($address_book_row = tep_db_fetch_array($address_book_result_sql)) {
				$_SESSION[$form_session_name]['address'] = $address_book_row['entry_street_address'];
				$_SESSION[$form_session_name]['suburb'] = $address_book_row['entry_suburb'];
				$_SESSION[$form_session_name]['postcode'] = $address_book_row['entry_postcode'];
				$_SESSION[$form_session_name]['city'] = $address_book_row['entry_city'];
				$_SESSION[$form_session_name]['country'] = $address_book_row['entry_country_id'];

				$states_array = get_country_states((int)$address_book_row['entry_country_id']);
				//First row is 'Please select'. If country has zones in our db, select list was used. so value of 'state' is rowid. Otherwise, value is user defined string.
				if (count($states_array) > 1) {
					$_SESSION[$form_session_name]['state'] = $address_book_row['entry_zone_id'];
				} else {
					$_SESSION[$form_session_name]['state'] = $address_book_row['entry_state'];
				}
			}

			$preferences_setting_array = array(KEY_SP_TIME_ZONE, KEY_SP_LANG);
			$customer_preferences_select_sql = "SELECT user_setting_key, user_setting_value FROM ".TABLE_USER_SETTING." WHERE user_setting_user_id = '{$_SESSION['customer_id']}' AND user_setting_key IN ('".implode("', '", $preferences_setting_array)."') ";
			$customer_preferences_result_sql = tep_db_query($customer_preferences_select_sql);
			while ($customer_preferences_row = tep_db_fetch_array($customer_preferences_result_sql)) {
				$_SESSION[$form_session_name][$customer_preferences_row['user_setting_key']] = $customer_preferences_row['user_setting_value'];
			}
		}

		//Populate session with saved payment accounts.
		$_SESSION[$form_session_name]['payment_method'] = array();
		$u_payment_methods_array = tep_get_user_payment_settings($_SESSION['customer_id'], 'customers');
		
		foreach ($u_payment_methods_array as $my_payment_settings) {
			$_SESSION[$form_session_name]['payment_method'][] = $my_payment_settings;
		}
	}
}

$form_values_arr = $_SESSION[$form_session_name];

//Initialize as first step/tab
$current_step = 0;
//set it redirect to setup security if expired.
$date_diff = $customers_security_obj->get_convert_pin_expired_date();
if (tep_not_null($date_diff) && $date_diff <= 0) {
	if ($current_step < 3) {
		$current_step = 3;
	}
}
$previous_step = 0;
$next_step = 1;

$referrer_url_array = array();
if (isset($_SERVER["HTTP_REFERER"])) {
	$referrer_url_array = parse_url($_SERVER["HTTP_REFERER"]);
}

//Ignore skipping steps by modifying the url. If that happens, always display the first step (don't enter here).
if ($referrer_url_array || !isset($_SERVER["HTTP_REFERER"])) {
	//Ignore posting from other domains/files.
	if ($referrer_url_array) {
		$referrer_url_str = "{$referrer_url_array['scheme']}://{$referrer_url_array['host']}{$referrer_url_array['path']}";
		
		$referer_path_parts = pathinfo($_SERVER["HTTP_REFERER"]);
		$valid_path_part = pathinfo(tep_href_link(FILENAME_MY_ACCOUNT_MGMT));
	}
	
	//if (isset($_GET['current_step']) && (int)$_GET['current_step'] >= 0 && $referrer_url_str == substr(tep_href_link(FILENAME_MY_ACCOUNT_MGMT), 0, strlen($referrer_url_str)))	{
	
	if (isset($_GET['current_step'])
	    && (int)$_GET['current_step'] >= 0
	    && (!isset($_SERVER["HTTP_REFERER"]) || $referer_path_parts['dirname'] == $valid_path_part['dirname'])
	   ) {
		$current_step = (int)$_GET['current_step'];
		//set it redirect to setup security if expired.
		$date_diff = $customers_security_obj->get_convert_pin_expired_date();
		if (tep_not_null($date_diff) && $date_diff <= 0) {
			if ($current_step < 3) {
				$current_step = 3;
			}
		}

		if ($current_step == count($tabs_array)) {
			//We're came from the last tab so current_tab is last tab +1. don't allow forward. Loop back to last tab.
			//redirects use $previous_step, so redirect to last tab as well.
			$next_step = $previous_step = $current_step - 1;
		} elseif ($current_step == 999) {
			//Clicked Final done
			$next_step = $previous_step = $current_step;
		} else {
			$next_step = $current_step + 1;
			$previous_step = $current_step - 1;
		}
		
		//Don't allow previous to be negative.
		$previous_step = max($previous_step, 0);
		if (isset($action)) {
			//Okay, so we have stuff to validate.
			//i.e. remove payment method is a link, not a post, which is why we use $_REQUEST['action']
			$errorCount = 0;
			switch ($action) {
				case 'process':
					//this tab's validation function
					$posted_tab_validation_func = 'validate_tab_' . $previous_step;
					if (function_exists($posted_tab_validation_func)) {
						$update_session = true;
						$val_arr = $_POST;
						if ($current_step == 999) {
							$val_arr = $form_values_arr;
							$update_session = false;
						}
						$errorCount = $posted_tab_validation_func($val_arr, $update_session);
						
						if ($errorCount > 0 || $current_step == count($tabs_array)) {
							//We are validating post from last tab. always redirect back to last tab.
							//If previous tab is last tab, always redirect back. Same if found error.
							//We have another button for confirmation to go confirm and continue past last tab.
//							if ($current_step == count($tabs_array)) {
//								$redirect_to_step = 999;
//								tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'action=process&current_step=' . $redirect_to_step));
//							} else {
								$redirect_to_step = $previous_step;
								tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=' . $redirect_to_step));
//							}
						}
					}
					break;
				case "add_pm":
					$form_content = $pm_object->add_payment_account(FILENAME_MY_ACCOUNT_MGMT);

					break;
				case "edit_pm":
					$form_content = $pm_object->add_payment_account(FILENAME_MY_ACCOUNT_MGMT, $HTTP_GET_VARS['book_id']);

					break;
				case 'add_payment_method':
					if ($_POST['payment_method']) {
						$_SESSION[$form_session_name]['payment_method'][][$_POST['payment_method']] = array();
					}
					tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=' . $previous_step));
					break;

				case 'remove_payment_method':
					if (isset($_GET['remove_row_num'])) {
						unset($_SESSION[$form_session_name]['payment_method'][$_GET['remove_row_num']]);
					}
					tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=' . $previous_step));
					break;
			}//end switch
		}
	}
}

$form_values_arr = $_SESSION[$form_session_name];

//Ok no errors, lets start rendering
//Prepare vars for the forms
switch($current_step) {
	case 0:
		$user_os = tep_get_os($HTTP_USER_AGENT);
		$country_selection_mode = ($user_os == "Windows" || $user_os == "Linux") ? 'xmlhttp_mode' : 'refresh_mode';

		$day_array = array( array('id' => '', 'text' => TEXT_DAY) );
		for ($i=1; $i <= 31; $i++) {
			$day_str = sprintf('%02d', $i);
			$day_array[] = array('id' => $day_str, 'text' => $day_str);
		}

		$month_array = array( array('id' => '', 'text' => TEXT_MONTH) );
		for ($i=1; $i <= 12; $i++) {
			$month_str = sprintf('%02d', $i);
			$month_array[] = array('id' => $month_str, 'text' => constant('TEXT_MONTH_'.$i));
		}

		$year_array = array( array('id' => '', 'text' => TEXT_YEAR) );
		for ($i=1945; $i <= (int)date('Y'); $i++) {
		   $year_array[] =array('id' => $i, 'text' => $i);
		}
		
		$form_values_arr['country'] = SIGNUP_DEFAULT_COUNTRY_ID;
		$states_array = array();
		if (isset($form_values_arr['country']) && (int)$form_values_arr['country'] > 0) {
			$states_array = get_country_states((int)$form_values_arr['country']);
		}

		$pref_language_array = tep_get_language_selection();
		
		$time_zone_array = array();
		foreach($GLOBALS['_DATE_TIMEZONE_DATA'] as $tid => $tres) {
			if (preg_match('/(?:GMT)[+-]?(\d*?)$/i', $tid, $regs)) {
				if ($regs[1] > 0 || $tid == 'GMT') {
					$time_zone_array[] = array('id' => $tid, 'text' => $tres["shortname"]);
				}
			}
		}

		break;
//	case 1:
//		//do nothing
//		break;
	case 2:
		if (!tep_not_null($action)) {
			$form_content = $pm_object->list_payment_account_book(FILENAME_MY_ACCOUNT_MGMT);
		}
		break;
	case 3:
		$date_diff = $customers_security_obj->get_convert_pin_expired_date();
		if (tep_not_null($date_diff) && !$customers_security_obj->check_secret_question_isset()) {
			$form_content = $customers_security_obj->display_customer_security_form('exist', $form_values_arr);
		} else if ($customers_security_obj->check_secret_question_is_reset()) {
			$form_content = $customers_security_obj->display_customer_security_form('reset', $form_values_arr);
		} else {
			tep_redirect(tep_href_link(FILENAME_MY_ACCOUNT_MGMT, 'current_step=0'));
		}
		break;
}
$form_values_arr = $_SESSION[$form_session_name];

//Define the javascript file
$javascript = 'form_check.js.php';

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_ACCOUNT_MGMT, '', 'SSL'));
require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>