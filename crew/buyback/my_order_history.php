<?php
/*
  	$Id: my_order_history.php,v 1.37 2009/06/26 07:44:36 keepeng.foong Exp $
	
	Developer: <PERSON>
  	Copyright (c) 2007 SKC Ventrue
	
  	Released under the GNU General Public License
*/

require('includes/application_top.php');
if (!tep_session_is_registered('customer_id')) {
	$messageStack->add_session(CONTENT_LOGIN, TEXT_MUST_LOGIN);
	$navigation->set_snapshot();
    tep_redirect(tep_href_link(FILENAME_LOGIN, '', 'SSL'));
}

/******************************************************************
	Buyback Order Expiration Cancellation Task
******************************************************************/
include_once(DIR_WS_CLASSES . 'vip_groups.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
tep_cancel_expirable_buyback_request();
// End of Buyback Order Expiration Cancellation Task

define('CNBB_REMARK_MAX_QUEUE_NO', 3);
define('CNBB_LOGIN_GAME_QUEUE_REMARK_ID', '135');

//define screenshot file size
$buybuck_screenshot_image_extension = array('jpeg', 'jpg');
define('BUYBACK_SCREENSHOT_MAX_SIZE', '512000');

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_MY_ORDER_HISTORY;
//Define the javascript file
$javascript = 'supplier_xmlhttp.js';

//Creating session key based on filename so we know where it got its values.
$form_session_name = FILENAME_MY_ORDER_HISTORY;
$new_session = false;

if (!tep_session_is_registered($form_session_name)) {
	$$form_session_name = array();
	tep_session_register($form_session_name);
	$new_session = true;
}

$form_values_arr = $_SESSION[$form_session_name];

if (isset($_REQUEST['action'])) {
	$action = $_REQUEST['action'];
} else {
	$action = '';
}

$allow_search_num_days_ago = 100;

//$odh_input_order_status_select = '1';
$odh_input_order_status_select = '0';
$odh_input_product_type_select = 1;
$odh_input_order_no = '';
$odh_input_game_select = 0;

$start_date = $odh_input_start_date = '';

if (isset($_POST['odh_input_start_date']) && tep_not_null($_POST['odh_input_start_date'])) {
	$_POST['odh_input_start_date'] = $start_date = $odh_input_start_date = strstr($odh_input_start_date, ':') ? $_POST['odh_input_start_date'].' 00:00:00' : $_POST['odh_input_start_date'];
} else if (isset($_GET['startdate']) && tep_not_null($_GET['startdate'])) {
	$_POST['odh_input_start_date'] = $start_date = $odh_input_start_date = strstr($odh_input_start_date, ':') ? $_GET['startdate'].' 00:00:00' : $_GET['startdate'];
}

//$end_date = $odh_input_end_date = date('Y-m-d') .' 23:59';
$end_date = $odh_input_end_date = '';

if (isset($_GET['order_status']) && tep_not_null($_GET['order_status'])) {
	$odh_input_order_status_select = $_GET['order_status'];
}



/**
 * Validate the post
**/
$errorCount = 0;
switch ($action) {
	case 'reset_session':
	    unset($_SESSION[$form_session_name]);
	    tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'));
	    
	    break;
	case 'confirm_sent':
		$buyback_request_group_id = isset($_POST['request_group_id']) ? (int)$_POST['request_group_id'] : 0;
		$buyback_quantity_confirmed = isset($_POST['sent_qty'][$buyback_request_group_id]) ? (int)$_POST['sent_qty'][$buyback_request_group_id] : 0;
		$buyback_request_id = isset($_POST['request_id']) ? (int)$_POST['request_id'] : 0;
		
		if (isset($_POST['btn_cancel_'.$buyback_request_group_id])) {	// Cancel this order
			if (tep_cancel_buyback_order($buyback_request_group_id)) {
				$messageStack->add_session($content, SUCCESS_ORDER_HISTORY_ORDER_CANCEL_SUCCESS, 'success');
			} else {
				$messageStack->add_session($content, ERROR_ORDER_HISTORY_ORDER_CANCEL_FAILED, 'error');
			}
		}
		
		tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action')) . 'action=search'));
		
		break;
	case 'search':
	default:
		$action = 'search';
		//Display pending orders on initial load (no post)
		//Assigned here so we know what to render in the switch() below.
		
		if (isset($_POST['odh_input_order_status_select']) && trim($_POST['odh_input_order_status_select'])) {
			$odh_input_order_status_select = tep_db_prepare_input($_POST['odh_input_order_status_select']);
		}
		
		if (isset($_POST['odh_input_product_type_select'])) $odh_input_product_type_select = tep_db_prepare_input($_POST['odh_input_product_type_select']);
		if (isset($_POST['odh_input_order_no'])) $odh_input_order_no = tep_db_prepare_input($_POST['odh_input_order_no']);
		if (isset($_POST['odh_input_game_select'])) $odh_input_game_select = tep_db_prepare_input($_POST['odh_input_game_select']);
		$odh_input_start_date = isset($_POST['odh_input_start_date']) ? tep_db_prepare_input($_POST['odh_input_start_date']) : date('Y-m-d');
		
		if (strstr($odh_input_start_date, ':')) {
			//user selected time as well.
			if (!strstr($odh_input_start_date, '00:00:00')) {
				$vipodh_input_start_date = $odh_input_start_date.':00';
			}
		} else {
			//user did not select time
			$odh_input_start_date = $odh_input_start_date.' 00:00:00';
		}
		
		$odh_input_end_date = isset($_POST['odh_input_end_date']) ? tep_db_prepare_input($_POST['odh_input_end_date']) : date('Y-m-d');
		
		if (strstr($odh_input_end_date, ':')) {
			//user selected time as well.
			if (!strstr($odh_input_end_date, '23:59:59')) {
				$odh_input_end_date = $odh_input_end_date.':59';
			}
		} else {
			//user did not select time
			$odh_input_end_date = $odh_input_end_date.' 23:59:59';
		}
		
		$date_range = 0;
		$start_date_where_sql = $end_date_where_sql = ' 1 ';
		
		if ((isset($_POST['odh_input_start_date']) && tep_not_null($_POST['odh_input_start_date'])) && 
		(isset($_POST['odh_input_end_date']) && tep_not_null($_POST['odh_input_end_date']))) {
			
			$date_range = tep_day_diff($odh_input_start_date, $odh_input_end_date);
			
			$start_date_where_sql = " brg.buyback_request_group_date >= '" . tep_db_input($odh_input_start_date) . "'";
			
			if ($date_range > $allow_search_num_days_ago) {
				$end_date_where_sql =  " brg.buyback_request_group_date <= DATE_ADD('".$odh_input_start_date."', INTERVAL ".$allow_search_num_days_ago." DAY)";
				$odh_input_end_date = $odh_input_end_date = date("Y-m-d 23:59:59", strtotime($odh_input_start_date." +".$allow_search_num_days_ago." day"));
			} else {
				$end_date_where_sql =  " brg.buyback_request_group_date <= '" . tep_db_input($odh_input_end_date) . "'";
			}
		} else {
			if (isset($_POST['odh_input_start_date']) && tep_not_null($_POST['odh_input_start_date'])) {
				$date_range = tep_day_diff($_POST['odh_input_start_date'], date("Y-m-d 23:59:59"));
				$start_date_where_sql = " brg.buyback_request_group_date >= '" . tep_db_input($odh_input_start_date) . "'";
				
				if ($date_range > $allow_search_num_days_ago) {
					$end_date_where_sql =  " brg.buyback_request_group_date <= DATE_ADD('".$odh_input_start_date."', INTERVAL ".$allow_search_num_days_ago." DAY)";
					$odh_input_end_date = date("Y-m-d 23:59:59", strtotime($odh_input_start_date." +".$allow_search_num_days_ago." day"));
				} else {
					$end_date_where_sql =  " brg.buyback_request_group_date <= '" . date("Y-m-d 23:59:59") . "'";
				}
			} elseif (isset($_POST['odh_input_end_date']) && tep_not_null($_POST['odh_input_end_date'])) {
				$start_date_where_sql = " brg.buyback_request_group_date >= DATE_ADD('".$odh_input_end_date."', INTERVAL -".$allow_search_num_days_ago." DAY)";
				$end_date_where_sql =  " brg.buyback_request_group_date <= '" . tep_db_input($odh_input_end_date) . "'";
				
				$odh_input_start_date = date("Y-m-d 23:59:59", strtotime($odh_input_end_date." -".$allow_search_num_days_ago." day"));
			}
		}
		
		break;
}

/**
 * Save form vars to session
 */
if($new_session || $action == 'search'){
	$_SESSION[$form_session_name]['odh_input_order_status_select'] = $odh_input_order_status_select;
	$_SESSION[$form_session_name]['odh_input_product_type_select'] = $odh_input_product_type_select;
	$_SESSION[$form_session_name]['odh_input_order_no'] = $odh_input_order_no;
	$_SESSION[$form_session_name]['odh_input_game_select'] = $odh_input_game_select;
	$_SESSION[$form_session_name]['odh_input_start_date'] = $odh_input_start_date;
	$_SESSION[$form_session_name]['odh_input_end_date'] = $odh_input_end_date;
} else {
	$odh_input_order_status_select = $_SESSION[$form_session_name]['odh_input_order_status_select'];
	$odh_input_product_type_select = $_SESSION[$form_session_name]['odh_input_product_type_select'];
	$odh_input_order_no = $_SESSION[$form_session_name]['odh_input_order_no'];
	$odh_input_game_select = $_SESSION[$form_session_name]['odh_input_game_select'];
	$odh_input_start_date = $_SESSION[$form_session_name]['odh_input_start_date'];
	$odh_input_end_date = $_SESSION[$form_session_name]['odh_input_end_date'];
	$start_date = $odh_input_start_date;
	$end_date = $odh_input_end_date;
}

if (trim($start_date_where_sql)==1 && trim($end_date_where_sql)==1) {
	$start_date_where_sql = " brg.buyback_request_group_date >= '" . tep_db_input($odh_input_start_date) . "'";
	$end_date_where_sql = " brg.buyback_request_group_date <= '" . tep_db_input($odh_input_end_date) . "'";
}

if ($errorCount > 0) {
	$messageStack->add_session($content, TEXT_ERROR_TRYAGAIN);
	tep_redirect(tep_href_link(FILENAME_MY_ORDER_HISTORY) );
}

/**
 * Start preparing search form. Save doing this if redirecting on error.
 */

$odh_store_account_balance = tep_get_store_account_balance($_SESSION['customer_id']);

$product_type_arr[] = array('id' => 'game_currency', 'text' => TEXT_GAME_CURRENCY);

//Order statuses. Reflects the processing cycle.
$order_status_arr = tep_get_buyback_status(true);

//All order status
$default_order_status_array = array(array('id' => '0', 'text' => TEXT_ALL_ORDER_STATUS));
$order_status_arr = array_merge($default_order_status_array, $order_status_arr);

//Game listing
$wbb_game_list_arr = tep_get_game_list_arr(array(array('id' => '0', 'text' => TEXT_ALL_GAMES)));

/**
 * Prepare for display results in div
 */
//Default
$odh_auto_refresh_results_bool = 1;

switch ($action) {
	case 'search':
		//Do the search
		$buyback_request_select_sql = "	SELECT brg.buyback_request_group_id, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date, brg.currency, brg.currency_value, brg.show_restock, brg.buyback_status_id,
											br.buyback_request_id, br.products_id, br.buyback_amount, br.buyback_request_quantity, bs.buyback_status_name, br.buyback_quantity_confirmed, br.restock_character, 
											br.buyback_dealing_type, pc.categories_id, brg.buyback_request_group_served, br.buyback_request_screenshot_before_name, br.buyback_request_screenshot_after_name 
										FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
										LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br ON brg.buyback_request_group_id=br.buyback_request_group_id
										INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc ON br.products_id=pc.products_id
										INNER JOIN " . TABLE_BUYBACK_STATUS . " AS bs ON ( brg.buyback_status_id=bs.buyback_status_id AND bs.language_id = '".$sup_languages_id."')
										WHERE brg.customers_id = '{$_SESSION['customer_id']}'
											AND brg.buyback_request_group_user_type = '1'
											AND brg.buyback_request_order_type='0'
											AND " . $start_date_where_sql . " 
											AND " . $end_date_where_sql;
											
		if ((int)$odh_input_order_status_select != 0) {
		    $buyback_request_select_sql .= " AND brg.buyback_status_id = '".$odh_input_order_status_select."' ";
		}
		if ($odh_input_order_no) {
			$buyback_request_select_sql .= " AND brg.buyback_request_group_id = '".$odh_input_order_no."' ";
		}
		
		tep_get_subcategories($category_array, $odh_input_game_select);
		if ($category_array) {
			$category_str = implode("', '", $category_array);
		} else {
			$category_str = $odh_input_game_select;
		}
		
		$buyback_request_select_sql .= " AND pc.categories_id IN ('" . $category_str . "')";
		$buyback_request_select_sql .= "  GROUP BY brg.buyback_request_group_id
										  ORDER BY brg.buyback_status_id asc, brg.buyback_request_group_date desc";
		$buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
		$buyback_request_num_rows = tep_db_num_rows($buyback_request_result_sql);
		
		$searchResultsHTML = '';
		
		$col_titles = array();
		$col_titles[0] = array('width' => 70, 'align' => 'left', 'title' => '<p>'.TEXT_ORDER_NO.'</p>');
		$col_titles[1] = array('width' => 90, 'align' => 'left', 'title' => '<p>'.TEXT_GAME.'</p>');
		$col_titles[2] = array('width' => 100, 'align' => 'left', 'title' => '<p>'.TEXT_SERVER.'</p>');
		$col_titles[3] = array('width' => 120, 'align' => 'left', 'title' => '<p>'.TABLE_HEADING_RECEIVER_CHAR_NAME.'</p>');
		$col_titles[4] = array('width' => 50, 'align' => 'left', 'title' => '<p>'.TEXT_REQUEST_QTY.'</p>');
		$col_titles[5] = array('width' => 50, 'align' => 'left', 'title' => '<p>'.TEXT_SENT_QTY.'</p>');
		$col_titles[6] = array('width' => 70, 'align' => 'left', 'title' => '<p>'.TEXT_AMOUNT_WITH_CURRENCY.'</p>');
		$col_titles[7] = array('width' => 50, 'align' => 'left', 'title' => '<p>'.TEXT_STATUS.'</p>');
		$col_titles[8] = array('width' => 90, 'align' => 'center', 'title' => '<p>'.TEXT_ACTION.'</p>');
		
		while($buyback_request_row = tep_db_fetch_array($buyback_request_result_sql)) {
			//Get restock character.
			$order_latest_remark = $restock_character = '';
			$show_restock = 0;
			
			if ((int)$buyback_request_row['buyback_status_id'] == 1) {
				//Get the latest character if char list is open
				$show_restock = (int)$buyback_request_row['show_restock'];
				if ($show_restock) {
					if (tep_not_null($buyback_request_row['restock_character'])) {
						$restock_character = $buyback_request_row['restock_character'];
					} else {
						$restock_character = tep_get_buyback_restock_char($buyback_request_row['products_id']);
					}
				}
			}
			
			$buyback_main_cat_name = tep_get_buyback_main_cat_info($buyback_request_row['categories_id']);
			$product_display_name = tep_display_category_path(tep_get_product_path($buyback_request_row['products_id']), $buyback_request_row['categories_id'], 'catalog', false);
			
			//counting queuing number
			$total_queue = 0;
			if ((int)$buyback_request_row['buyback_status_id'] == 1 && $buyback_request_row['buyback_request_group_served'] == '0' && $buyback_request_row['buyback_dealing_type'] == 'ofp_deal_on_game') {
			    $count_q_select_sql = "SELECT count(*) AS q_no
		                               FROM ". TABLE_BUYBACK_REQUEST_GROUP ." AS brg
		                               INNER JOIN ". TABLE_BUYBACK_REQUEST ." AS br
		                                   ON (brg.buyback_request_group_id=br.buyback_request_group_id)
		                               WHERE brg.buyback_request_group_id < '". (int) $buyback_request_row['buyback_request_group_id'] ."'
		                                   AND brg.buyback_status_id = '1'
		                                   AND brg.buyback_request_group_served = '0'
		                                   AND (br.buyback_dealing_type = 'ofp_deal_on_game'
		                                   		OR br.buyback_dealing_type = 'vip_deal_on_game')";
    			$count_q_result_sql = tep_db_query($count_q_select_sql);
    			$count_q_row = tep_db_fetch_array($count_q_result_sql);
    			$total_queue = $count_q_row['q_no'];
			}
			
			//display Restock Characters and queing message
		    if ($buyback_request_row['buyback_status_id'] != 1) {
		        $receiver_char_name_message = '';
		  	} else {
		    	if ($total_queue == 0) {
		    		if ($show_restock) {
			        	$receiver_char_name_message = $restock_character;
			        } else {
			        	$receiver_char_name_message = TEXT_CHARACTER_NAME_IN_PREPARATION;
			        }
			    } else if ($total_queue > 0) {
			        $receiver_char_name_message = sprintf(TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE, $total_queue);
			    }
			}
			
			$searchResultsHTML .= tep_draw_form('confirm_sent_' . $buyback_request_row['buyback_request_group_id'], FILENAME_MY_ORDER_HISTORY, tep_get_all_get_params(array('action')) . 'action=confirm_sent', 'post', ' enctype="multipart/form-data" id="confirm_sent_' . $buyback_request_row['buyback_request_group_id'] . '"');
			$searchResultsHTML .= '<table width="100%" border="0" cellpadding="0" cellspacing="0">';
			$searchResultsHTML .= "<tr>";
			$searchResultsHTML .= "<td width='{$col_titles[0]['width']}'><p>{$buyback_request_row['buyback_request_group_id']}</p></td>";
			$searchResultsHTML .= "<td width='{$col_titles[1]['width']}'><p>".trim($buyback_main_cat_name['text'])."</p></td>";
			$searchResultsHTML .= "<td width='{$col_titles[2]['width']}'><p>".trim($product_display_name)."</p></td>";
			$searchResultsHTML .= "<td width='{$col_titles[3]['width']}'><p>" . $receiver_char_name_message . "</p></td>";
			$searchResultsHTML .= "<td width='{$col_titles[4]['width']}'><p>{$buyback_request_row['buyback_request_quantity']}</p></td>";
			
			//if restock character available, let supplier fill in 2nd list qty
			$searchResultsHTML .= "<td width='{$col_titles[5]['width']}' align='center'>";
			if ($show_restock && ((int)$odh_input_order_status_select == 1 || (int)$odh_input_order_status_select == 0) && $total_queue == 0) {
				$searchResultsHTML .= tep_draw_input_field('sent_qty['.$buyback_request_row['buyback_request_group_id'].']', '', 'size="3" maxlength="9" id="sent_qty_'.$buyback_request_row['buyback_request_group_id'].'"');
			} else {
				$searchResultsHTML .= "<p>" . (int)$buyback_request_row['buyback_quantity_confirmed'] . "</p>";
			}
			$searchResultsHTML .= "</td>";
			
			$searchResultsHTML .= "<td width='{$col_titles[6]['width']}'><p>".$currencies->format($buyback_request_row['buyback_amount'], true, $buyback_request_row['currency'], $buyback_request_row['currency_value'])."</p></td>";
			$searchResultsHTML .= "<td width='{$col_titles[7]['width']}'><p>".$buyback_request_row['buyback_status_name']."</p></td>";
			
			//Action Column
			$searchResultsHTML .= "<td width='{$col_titles[8]['width']}'>";
			if ((int)$buyback_request_row['buyback_status_id'] == 1) {
				//Pending or 'Draft'
				$searchResultsHTML .=	tep_draw_hidden_field('request_group_id', $buyback_request_row['buyback_request_group_id'], 'size="8"') . 
										tep_draw_hidden_field('request_id', $buyback_request_row['buyback_request_id']);
											
				if ($show_restock && $total_queue == 0) {
					$searchResultsHTML .=	tep_draw_hidden_field('request_qty['.$buyback_request_row['buyback_request_group_id'].']', $buyback_request_row['buyback_request_quantity'], 'id="request_qty_'.$buyback_request_row['buyback_request_group_id'].'"') . 
											//tep_image_submit(BUTTON_CONFIRM, BUTTON_CONFIRM, ' name="odh_button_update_'. $buyback_request_row['buyback_request_group_id'].'" onClick="return validate_confirmed_qty(\''.(int)$buyback_request_row['buyback_request_id'].'\', \''.$buyback_request_row['buyback_request_group_id'].'\');"', 'generalBtn') . '&nbsp;';
											tep_image_button('', BUTTON_CONFIRM, ' name="odh_button_update_'. $buyback_request_row['buyback_request_group_id'].'"', 'onclick="return validate_confirmed_qty(\''.(int)$buyback_request_row['buyback_request_id'].'\', \''.$buyback_request_row['buyback_request_group_id'].'\');"') . '&nbsp;';
				}
				
				$searchResultsHTML .=	tep_image_submit(BUTTON_CANCEL, BUTTON_CANCEL, ' name="btn_cancel_'. $buyback_request_row['buyback_request_group_id'].'" onClick="return confirm(\''.JS_CONFIRM_CANCEL_ORDER.'\')"', 'generalBtn');
			} else {
				//Button for popup report
				$searchResultsHTML .= tep_image_button('', TEXT_VIEW, ' name="odh_button_update"', 'onclick="hiddenFloatingDiv(\'order_report\');show_order_report(\''.$buyback_request_row['buyback_request_group_id'].'\', \''.$languages_id.'\', \''.SID.'\');"')."<br><span class='title-text' id='wbb_div_msgField_{$buyback_request_row['buyback_request_group_id']}'></span>";
				if (!tep_not_null($buyback_request_row['buyback_request_screenshot_after_name']) && !tep_not_null($buyback_request_row['buyback_request_screenshot_before_name']) && BUYBACK_ENABLE_UPLOAD_SCREENSHOT == 'true' && (int)$buyback_request_row['buyback_status_id'] != 4) {
					$searchResultsHTML .= tep_image_button('', TEXT_UPLOAD_SS, ' name="upload_ss"', 'onclick="return upload_ss(\''.$buyback_request_row['buyback_request_group_id'].'\');"');
				}
			}
			$searchResultsHTML .= "</td>";
			
			$searchResultsHTML .= "</tr>";
			if ((int)$buyback_request_row['buyback_status_id'] == 1 || (int)$buyback_request_row['buyback_status_id'] == 2) {
				$show_expiry = 1;
				if ($buyback_request_row['buyback_status_id'] == 2) {
					$total_received_select_sql = "	SELECT sum(br.buyback_quantity_received) as total_received 
													FROM ".TABLE_BUYBACK_REQUEST." AS br
													WHERE br.buyback_request_group_id = '{$buyback_request_row['buyback_request_group_id']}' 
													GROUP BY buyback_request_group_id";
					$total_received_result_sql = tep_db_query($total_received_select_sql);
					if ($total_received_row = tep_db_fetch_array($total_received_result_sql)) {
						$total_received = (int)$total_received_row['total_received'];
					}
					
					if ($total_received)	$show_expiry = 0;
				}
				
//				if ($show_expiry) {
//					$searchResultsHTML .= '	<tr>
//												<td></td>
//												<td class="title-text"><p>'.TEXT_EXPIRES.'</p></td>
//												<td colspan="'.(count($col_titles)-1).'"><p>'. gmdate('H:i:s', strtotime($buyback_request_row['buyback_request_group_expiry_date'])+ (8*60*60)) . ' +10 ' . TEXT_MINUTES . '</p></td>
//											</tr>';
//				}
				
				if ($show_expiry && $buyback_request_row['buyback_request_group_expiry_date'] != '0000-00-00 00:00:00') {
					$get_time_sec = tep_day_diff(date("Y-m-d H:i:s"), $buyback_request_row['buyback_request_group_expiry_date'], 'sec');
					$expiry_mins = floor($get_time_sec/60);
					
					$searchResultsHTML .= '	<tr>
												<td></td>
												<td colspan="'.(count($col_titles)-2).'">
													<table width="100%" border="0" cellpadding="2" cellspacing="2" bgcolor="#E0ECF4">
														<tr>
															<td>
																<table width="100%" border="0" cellpadding="2" cellspacing="1">
																	<tr>
																		<td class="title-text" width="30%"><p style="color:red;">'.TEXT_EXPIRES.'</p></td>
																		<td class="title-text" width="70%" colspan="2"><p style="color:red;">'. $expiry_mins . ' +10 ' . TEXT_MINUTES . '</p></td>
																	</tr>';
					$searchResultsHTML .= '						</table>
															</td>
														</tr>			
													</table>
												</td>
												<td></td>
											</tr>';
				}
				
				if ((int)$buyback_request_row['buyback_status_id'] == 1) {
					if ($total_queue > 0) {
						if ($total_queue < (int)CNBB_REMARK_MAX_QUEUE_NO) {
							$predefined_order_comment_select_sql = "SELECT orders_comments_text 
																	FROM " . TABLE_ORDERS_COMMENTS . "
																	WHERE orders_comments_id = '".CNBB_LOGIN_GAME_QUEUE_REMARK_ID."'";
							$predefined_order_comment_result_sql = tep_db_query($predefined_order_comment_select_sql);
							$predefined_order_comment_row = tep_db_fetch_array($predefined_order_comment_result_sql);
							$order_latest_remark = $predefined_order_comment_row['orders_comments_text'];
						}
					} else {
						$buyback_remark_select_sql = "	SELECT comments 
														FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
														WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
															AND set_as_buyback_remarks = 1 
															AND customer_notified = 1 
															AND (comments IS NOT NULL AND comments <> '')";
						$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
						$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
						$order_latest_remark = $buyback_remark_row['comments'];
					}
				} else {
					$buyback_remark_select_sql = "	SELECT comments 
													FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
													WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
														AND set_as_buyback_remarks = 1 
														AND customer_notified = 1 
														AND (comments IS NOT NULL AND comments <> '')";
					$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
					$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
					$order_latest_remark = $buyback_remark_row['comments'];
				}
			} else {
				$buyback_remark_select_sql = "	SELECT comments 
												FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
												WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
													AND set_as_buyback_remarks = 1 
													AND customer_notified = 1 
													AND (comments IS NOT NULL AND comments <> '')";
				$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
				$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
				$order_latest_remark = $buyback_remark_row['comments'];
			}

			if (tep_not_null($order_latest_remark)) {
				$searchResultsHTML .= '	    <tr>
											    <td></td>
											    <td class="title-text" bgcolor="#ffffcc" colspan="'.(count($col_titles)-2).'">'.nl2br($order_latest_remark).'</td>
											    <td></td>
										    </tr>';
			}
			
			$searchResultsHTML .= "<tr><td colspan='".(count($col_titles))."'>" . tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '8') . "</td></tr>";
			$searchResultsHTML .= "<tr>";
			$searchResultsHTML .= "<td background='".DIR_WS_IMAGES . "space_line2.gif' height='1' colspan='".count($col_titles)."'>".tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')."
									</td>";
			$searchResultsHTML .= "</tr>";
			$searchResultsHTML .= '</table>';
			$searchResultsHTML .= "</form><!--end search result-->";
		}
		
		break;
	default:
		break;
}

$num_titles = count($col_titles);

$form_values_arr = $_SESSION[$form_session_name];

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_MY_ORDER_HISTORY, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>