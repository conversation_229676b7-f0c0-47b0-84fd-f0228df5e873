<?php
/*
  	$Id: password_forgotten.php,v 1.5 2008/08/15 10:56:05 chan Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

	Released under the GNU General Public License
*/

require('includes/application_top.php');

function randomize() {
	$salt = "ABCDEFGHIJKLMNOPQRSTUVWXWZabchefghjkmnpqrstuvwxyz0123456789";
	srand((double)microtime()*1000000);
	$i = 0;
	$pass = '';
	while ($i <= 7) {
		$num = rand() % 33;
		$tmp = substr($salt, $num, 1);
		$pass = $pass . $tmp;
		$i++;
	}
	return $pass;
}

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_PASSWORD_FORGOTTEN;
//Define the javascript file
$javascript = 'form_check.js.php';

//$customers_security_obj = new customers_security($_SESSION['sup_languages_id']);
$action = '';
if (isset($_POST['action'])) {
	$errorCount = 0;
	switch ($_POST['action']) {
		case 'check_email':
			if (!tep_not_null($_POST['email_address'])) {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_EMAIL_ADDRESS));
				$errorCount++;
			} else {
				$customer_email_address = $_POST['email_address'];
				$customer_info_select_sql = "	SELECT customers_id 
												FROM " . TABLE_CUSTOMERS . " 
												WHERE customers_status = '1' 
													AND customers_email_address = '" . tep_db_input($customer_email_address) . "'";
				$customer_info_result_sql = tep_db_query($customer_info_select_sql);
				if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
					$action = 'secret_question';
					$customers_security_obj->set_customers_id($customer_info_row['customers_id']);
				} else {
					$messageStack->add_session($content, ERROR_EMAIL_ADDRESS_INVALID);
					$errorCount++;
				}
			}
			if ($errorCount > 0) {
				tep_redirect(tep_href_link(FILENAME_PASSWORD_FORGOTTEN) );
			}
			break;
		case 'reset_password':
			if (!tep_not_null($_POST['email_address'])) {
				$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_EMAIL_ADDRESS));
				$errorCount++;
				tep_redirect(tep_href_link(FILENAME_PASSWORD_FORGOTTEN));
			} else {
				$customer_email_address = $_POST['email_address'];
				$customer_info_select_sql = "	SELECT customers_id, customers_firstname, customers_lastname, customers_gender, customers_email_address
												FROM " . TABLE_CUSTOMERS . " 
												WHERE customers_status = '1' 
													AND customers_email_address = '" . tep_db_input($customer_email_address) . "'";
				$customer_info_result_sql = tep_db_query($customer_info_select_sql);
				if ($customer_info_row = tep_db_fetch_array($customer_info_result_sql)) {
					$customers_security_obj->set_customers_id($customer_info_row['customers_id']);
					$error_answer += $customers_security_obj->form_validation_edit_profile($_POST);
					// update counter 
					$customers_security_obj->update_security_counter();
					if ($error_answer == 0) {
						$new_password = randomize();
			        	$success = tep_db_perform(TABLE_CUSTOMERS, array('customers_password' => tep_encrypt_password($new_password)), 'update',  "customers_id = '" . $customer_info_row['customers_id'] . "'");
						if ($success) {
							tep_mail(tep_mb_convert_encoding($customer_info_row['customers_firstname'] . ' ' . $customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_info_row['customers_email_address'], EMAIL_LOCAL_EMAIL_SUBJECT_PREFIX.tep_mb_convert_encoding(SUPPLIER_EMAIL_SUBJECT, EMAIL_CHARSET, CHARSET),
							 tep_mb_convert_encoding(tep_get_email_greeting(tep_mb_convert_encoding($customer_info_row['customers_firstname'], EMAIL_CHARSET, CHARSET), tep_mb_convert_encoding($customer_info_row['customers_lastname'], EMAIL_CHARSET, CHARSET), $customer_info_row['customers_gender']), CHARSET, EMAIL_CHARSET) . tep_mb_convert_encoding(sprintf(SUPPLIER_EMAIL_TEXT, tep_href_link(FILENAME_LOGIN, '', 'SSL', false), $customer_info_row['customers_email_address'], $new_password) . SUPPLIER_EMAIL_FOOTER, EMAIL_CHARSET, CHARSET),
							 STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
							$messageStack->add_session($content, TEXT_RESET_SUCCESS, 'success');
							$customers_security_obj->reset_security_counter();
							tep_redirect(tep_href_link(FILENAME_PASSWORD_FORGOTTEN) );
						} else {
							$messageStack->add_session($content, TEXT_RESET_FAILED);
							$errorCount++;
						}
					} else {
						//redirect back to secret question.
						tep_redirect(tep_href_link(FILENAME_PASSWORD_FORGOTTEN));
					}
				} else {
					$messageStack->add_session($content, ERROR_EMAIL_ADDRESS_INVALID);
					$errorCount++;
				}
			}
			
			if ($errorCount > 0) {
				tep_redirect(tep_href_link(FILENAME_PASSWORD_FORGOTTEN));
			}
			break;
	}
}
$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_PASSWORD_FORGOTTEN, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>