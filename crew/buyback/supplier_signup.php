<?php
/*
  	$Id: supplier_signup.php,v 1.19 2009/07/31 09:06:44 keepeng.foong Exp $

	osCommerce, Open Source E-Commerce Solutions
  	http://www.oscommerce.com

  	Copyright (c) 2002 osCommerce

	Released under the GNU General Public License
*/

require('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'user_agent.php');
require_once(DIR_WS_FUNCTIONS . 'customers_info_verification.php');
require_once(DIR_WS_CLASSES . 'vip_groups.php');


define('HEADING_TITLE', HEADER_TITLE_SIGNUP);
define('NAVBAR_TITLE', HEADER_TITLE_SIGNUP);

define('SIGNUP_DEFAULT_COUNTRY_ID', 44);	// Default to China

if (isset($HTTP_POST_VARS['btn_signup_agreement'])) {
	if (!tep_session_is_registered('session_signup_agreement')) {
		tep_session_register('session_signup_agreement');
		$session_signup_agreement = '1';
	} else {
		$session_signup_agreement = '1';
	}
}

function get_country_states($country_id) {
	global $form_values_arr, $COUNTRY_STATE_CHINESE_ARRAY;
	$states_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
  	$zones_select_sql = "SELECT zone_id, zone_name FROM " . TABLE_ZONES . " WHERE zone_country_id = '" . (int)$country_id . "' ORDER BY zone_name";
   	$zones_result_sql = tep_db_query($zones_select_sql);
   	while ($zones_row = tep_db_fetch_array($zones_result_sql)) {
   	    $states_array[] = array('id' => $zones_row['zone_id'], 'text' => isset($COUNTRY_STATE_CHINESE_ARRAY) ? $COUNTRY_STATE_CHINESE_ARRAY[$zones_row['zone_id']] : $zones_row['zone_name']);
   	}
   	return $states_array;
}

function validate_tab_0($val_arr, $update_session=true) {
	global $messageStack, $form_session_name, $content;
	$errorCount = 0;
	
	$email = tep_db_prepare_input($val_arr['email_address']);
	
	if (!trim($email)) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_EMAIL));
		$errorCount++;
	} else if (tep_validate_email($email) == false) {
      	$messageStack->add_session($content, ERROR_EMAIL_ADDRESS_INVALID);
      	$errorCount++;
	} else {
		//check if email exists
		$existing_customer_select_sql = "SELECT customers_status, email_verified FROM " . TABLE_CUSTOMERS. " WHERE customers_email_address = '". $email ."'";
		$existing_customer_result_sql = tep_db_query($existing_customer_select_sql);
		if (tep_db_num_rows($existing_customer_result_sql) > 0) {
			$email_error_msg = sprintf(TEXT_EMAIL_EXISTS, $email);
			
			if ($existing_customer_row = tep_db_fetch_array($existing_customer_result_sql)) {
				switch((int)$existing_customer_row['customers_status']) {
					case 1: //OK, can login
						//do nothing
						break;
					case 0: //Disabled (crook?)
						$email_error_msg .= ' '. TEXT_ACCOUNT_DISABLED_OR_PENDING_ADMIN;
						break;
				}
                
				switch ((int)$existing_customer_row['email_verified']) {
					case 1:
						break;
					case 0: //Pending email verification (New signup).
						$email_error_msg .= ' '. TEXT_ACCOUNT_EMAIL_NOT_VERIFIED;
						break;
				}
			}
			
			$messageStack->add_session($content, $email_error_msg);
			$errorCount++;
		}
	}
	
	if (!trim($val_arr['password'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_NEW_PASSWORD));
		$errorCount++;
	} else if (!trim($val_arr['confirm_password'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CONFIRM_PASSWORD));
		$errorCount++;
	} else if (trim($val_arr['password']) != trim($val_arr['confirm_password'])) {
		$messageStack->add_session($content, TEXT_PASSWORD_NOTMATCH);
		$errorCount++;
	} else if (strlen(trim($val_arr['password'])) < (int)ENTRY_PASSWORD_MIN_LENGTH) {
		$messageStack->add_session($content, ENTRY_PASSWORD_ERROR);
		$errorCount++;
	}
	
	if (!trim($val_arr['lastname'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_LAST_NAME));
		$errorCount++;
	}
	
	if (!trim($val_arr['firstname'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_FIRST_NAME));
		$errorCount++;
	}
	
	if (!trim($val_arr['dob_month']) || !trim($val_arr['dob_day']) || !trim($val_arr['dob_year'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_DATE_OF_BIRTH));
		$errorCount++;
	}
	
	if (!trim($val_arr['gender'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_GENDER));
		$errorCount++;
	}
	if (!trim($val_arr['address'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_ADDRESS));
		$errorCount++;
	}
	
	if (!trim($val_arr['city'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CITY));
		$errorCount++;
	}
	if (!trim($val_arr['state'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_STATE));
		$errorCount++;
	}
	
	$val_arr['country'] = SIGNUP_DEFAULT_COUNTRY_ID;
	/*
	if (!trim($val_arr['country'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_COUNTRY));
		$errorCount++;
	}
	*/
	
	if (!trim($val_arr['contact_area_no']) || !trim($val_arr['contact_phone_no'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_CONTACT_NO));
		$errorCount++;
	} else if (!is_numeric($val_arr['contact_area_no']) || !is_numeric($val_arr['contact_phone_no']))  {
		$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_CONTACT_NO));
		$errorCount++;
	} else if (strlen($val_arr['contact_area_no']) < 3 || strlen($val_arr['contact_phone_no']) < 7 )  { // check the area code at least 3 digit and the phone no at least 7 digit.
		$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_CONTACT_NO));
		$errorCount++;		
	}
	
	$val_arr['contact_area_no'] = sprintf('%04d', $val_arr['contact_area_no']);
	
	if (!trim($val_arr['mobile_no'])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_MOBILE_NO));
		$errorCount++;
	} else if (!is_numeric($val_arr['mobile_no'])) {
		$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_MOBILE_NO));
		$errorCount++;
	}
	
	if (!trim($val_arr['qq_no'])) {
	    $messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_QQ_NO));
		$errorCount++;
	} else if(!is_numeric($val_arr['qq_no'])) {
		$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_QQ_NO));
		$errorCount++;
	}
	
	if (trim($val_arr['msn_address']) && tep_validate_email($val_arr['msn_address']) == false) {
      	$messageStack->add_session($content, ERROR_MSN_ADDRESS_INVALID);
      	$errorCount++;
	}
	
	if (trim($val_arr['yahoo_address']) && tep_validate_email($val_arr['yahoo_address']) == false) {
      	$messageStack->add_session($content, ERROR_YAHOO_ADDRESS_INVALID);
      	$errorCount++;
	}
	
	if (trim($val_arr['icq_no']) && !is_numeric($val_arr['icq_no'])) {
      	$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_ICQ_NO));
      	$errorCount++;
	}
	
	/*
	if (trim($val_arr['fax_no']) && !is_numeric($val_arr['fax_no']))  { //Optional field
		$messageStack->add_session($content, sprintf(TEXT_INVALID_NUMBER, TEXT_FAX_NO));
		$errorCount++;
	}
	*/
    
	// Hardcode as requested
	$val_arr[KEY_SP_TIME_ZONE] = 'Etc/GMT-8';
	$val_arr[KEY_SP_LANG] = 2;	// Chinese language
	/*
	if (!trim($val_arr[KEY_SP_TIME_ZONE])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_TIME_ZONE));
		$errorCount++;
	}
	
	if (!trim($val_arr[KEY_SP_LANG])) {
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_LANGUAGE));
		$errorCount++;
	}
	*/
	if ($update_session) {
		$_SESSION[$form_session_name]['password'] = tep_db_prepare_input($val_arr['password']);
		$_SESSION[$form_session_name]['confirm_password'] = tep_db_prepare_input($val_arr['confirm_password']);
		$_SESSION[$form_session_name]['firstname'] = tep_db_prepare_input($val_arr['firstname']);
		$_SESSION[$form_session_name]['lastname'] = tep_db_prepare_input($val_arr['lastname']);
		$_SESSION[$form_session_name]['dob_month'] = tep_db_prepare_input($val_arr['dob_month']);
		$_SESSION[$form_session_name]['dob_day'] = tep_db_prepare_input($val_arr['dob_day']);
		$_SESSION[$form_session_name]['dob_year'] = tep_db_prepare_input($val_arr['dob_year']);
		$_SESSION[$form_session_name]['gender'] = tep_db_prepare_input($val_arr['gender']);
		$_SESSION[$form_session_name]['address'] = tep_db_prepare_input($val_arr['address']);
		$_SESSION[$form_session_name]['suburb'] = tep_db_prepare_input($val_arr['suburb']);
		$_SESSION[$form_session_name]['postcode'] = tep_db_prepare_input($val_arr['postcode']);
		$_SESSION[$form_session_name]['city'] = tep_db_prepare_input($val_arr['city']);
		$_SESSION[$form_session_name]['state'] = tep_db_prepare_input($val_arr['state']);
		$_SESSION[$form_session_name]['country'] = tep_db_prepare_input($val_arr['country']);
		$_SESSION[$form_session_name]['contact_area_no'] = tep_db_prepare_input($val_arr['contact_area_no']);
		$_SESSION[$form_session_name]['contact_phone_no'] = tep_db_prepare_input($val_arr['contact_phone_no']);
		$_SESSION[$form_session_name]['mobile_no'] = tep_db_prepare_input($val_arr['mobile_no']);
		$_SESSION[$form_session_name]['fax_no'] = tep_db_prepare_input($val_arr['fax_no']);
		$_SESSION[$form_session_name]['qq_no'] = tep_db_prepare_input($val_arr['qq_no']);
		$_SESSION[$form_session_name]['msn_address'] = tep_db_prepare_input($val_arr['msn_address']);
		$_SESSION[$form_session_name]['yahoo_address'] = tep_db_prepare_input($val_arr['yahoo_address']);
		$_SESSION[$form_session_name]['icq_no'] = tep_db_prepare_input($val_arr['icq_no']);
		$_SESSION[$form_session_name]['email_address'] = tep_db_prepare_input($val_arr['email_address']);
		//$_SESSION[$form_session_name]['apply_vip'] = tep_db_prepare_input($val_arr['apply_vip']);
		$_SESSION[$form_session_name][KEY_SP_TIME_ZONE] = tep_db_prepare_input($val_arr[KEY_SP_TIME_ZONE]);
		$_SESSION[$form_session_name][KEY_SP_LANG] = tep_db_prepare_input($val_arr[KEY_SP_LANG]);
	}
	return $errorCount;
}

function validate_tab_1($val_arr, $update_session=true) {
	global $customers_security_obj, $messageStack, $form_session_name, $content, $form_values_arr;
	$errorCount = $customers_security_obj->form_validation($val_arr, $update_session);
	return $errorCount;
}

function validate_tab_2($val_arr, $update_session=true) {
	global $messageStack, $form_session_name, $content, $form_values_arr;
	$errorCount = 0;

	//Skip validation if clicked update without adding any payment method
	if (count($val_arr['payment_method'])) {

		foreach ($val_arr['payment_method'] as $rownum => $row_payment_method_array) {

			foreach ($row_payment_method_array as $row_payment_method_id => $row_payment_method_field_array) {

				foreach ($row_payment_method_field_array as $row_payment_method_field_id => $row_payment_method_field_value) {
					//echo "<h1>$rownum - $row_payment_method_id - $row_payment_method_field_id - $row_payment_method_field_value</h1>";

					if (!trim($row_payment_method_field_value)) {
						//Check if this field is mandatory only if empty.
						$payment_methods_fields_array = tep_get_payment_methods_fields($row_payment_method_id);
						if ((int)$payment_methods_fields_array[$row_payment_method_field_id]['payment_methods_fields_required']) {
						//	$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_BANK_ACCOUNTS));
							$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, $payment_methods_fields_array[$row_payment_method_field_id]['payment_methods_fields_title']));
							$errorCount++;
						}
					}
					if ($update_session) {
						$_SESSION[$form_session_name]['payment_method'][$rownum][$row_payment_method_id][$row_payment_method_field_id] = tep_db_prepare_input($row_payment_method_field_value);
					}
				} // end payment method field
			} //end payment method
		} //end each row
	} else {
		//Didnt provide even 1 bank account
		$messageStack->add_session($content, sprintf(TEXT_MANDATORY_ISBLANK, TEXT_BANK_ACCOUNTS));
		$errorCount++;
	}
	return $errorCount;
}

function validate_tab_999($val_arr, $update_session=false) {
	global $messageStack, $form_session_name, $content, $form_values_arr, $get_params_always_exclude_array, $previous_step, $customers_security_obj;
	global $tabs_array;
	
	//Validate all the tabs again.
	$tabs_with_errors = array();
	foreach ($tabs_array as $step_number => $tab_name) {
		$errorCount = 0;
		$this_tab_validation_func = 'validate_tab_' . $step_number;
		if (function_exists($this_tab_validation_func)) {
			$errorCount = $this_tab_validation_func($val_arr, $update_session);
			if ($errorCount > 0) {
				$tabs_with_errors[] = $step_number;
			}
		}
	}
	
	if (count($tabs_with_errors)) {
		//Got errors, redirect to the tab of the first error :)
		$redirect_to_step = $tabs_with_errors[0];
		tep_redirect(tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=' . $redirect_to_step));
		exit;
	}
	
	$states_array = get_country_states((int)$form_values_arr['country']);
	//First row is 'Please select'. If country has zones in our db, select list was used. so value of 'state' is rowid. Otherwise, value is user defined string.
	if (count($states_array) > 1) {
		$state_str = '';
		$zone_id = $form_values_arr['state'];
	} else {
		$state_str = $form_values_arr['state'];
		$zone_id = '0';
	}
	
	// Validate Q&A
	$secret_question_array = array();
	$secret_answer_array = array();
	for ($ans_cnt = 1; $ans_cnt <= 1; $ans_cnt++) {
		$qa_question = $customers_security_obj->get_question_text($form_values_arr['question_'.$ans_cnt]);
		$qa_answer = $form_values_arr['answer_'.$ans_cnt];
		
		$secret_question_array[] = $qa_question;
		$secret_answer_array[] = $qa_answer;
		
		if (!tep_not_null($qa_question) || !tep_not_null($qa_answer)) {
			//Signup failed, Go back.
			$messageStack->add_session($content, TEXT_SIGNUP_FAILED);
			tep_redirect(tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=0'));
			exit;
		}
	}
	
	//save to database and create account
   	$activation_code = tep_gen_random_serial($form_values_arr['email_address'], TABLE_CUSTOMERS, 'serial_number', '', 12);
	
	$insert_user_arr = array(	'customers_id' 			=> 0,
								'customers_gender' 		=> strtolower($form_values_arr['gender']),
								'customers_firstname'	=> $form_values_arr['firstname'],
								'customers_lastname'	=> $form_values_arr['lastname'],
								'customers_dob' 		=> $form_values_arr['dob_year'] . '' . $form_values_arr['dob_month'] . '' . $form_values_arr['dob_day'],
								'customers_email_address'=> $form_values_arr['email_address'],
								'customers_telephone' 	=> $form_values_arr['contact_area_no'] . $form_values_arr['contact_phone_no'],
								/*'customers_fax' 		=> $form_values_arr['fax_no'],*/
								'customers_mobile' 		=> $form_values_arr['mobile_no'],
								'customers_qq' 			=> $form_values_arr['qq_no'],
								'customers_msn' 		=> $form_values_arr['msn_address'],
								'customers_yahoo' 		=> $form_values_arr['yahoo_address'],
								'customers_icq' 		=> $form_values_arr['icq_no'],
								'customers_password' 	=> tep_encrypt_password($form_values_arr['password']),
								'serial_number'			=> $activation_code,
								'customers_flag'		=> '',
								'account_activated'		=> 0,
								'customers_status' 		=> 0,
								'email_verified'		=> 0,
								'ref_id'				=> 0,
								'customers_login_sites' => '0,1'
							);
	$success_create_customer = tep_db_perform(TABLE_CUSTOMERS, $insert_user_arr);

	if (!$success_create_customer) {
		//Signup failed, Go back.
		$messageStack->add_session($content, TEXT_SIGNUP_FAILED);
		tep_redirect(tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=0'));
		exit;
	}
	$customer_id = tep_db_insert_id();
	
	$insert_address_arr = array('address_book_id' => '',
								'customers_id' => $customer_id,
								'entry_gender' => strtolower($form_values_arr['gender']),
								'entry_company' => '',
								'entry_firstname' => $form_values_arr['firstname'],
								'entry_lastname' => $form_values_arr['lastname'],
								'entry_street_address' => $form_values_arr['address'],
								'entry_suburb' => $form_values_arr['suburb'],
								'entry_postcode' => $form_values_arr['postcode'],
								'entry_city' => $form_values_arr['city'],
								'entry_state' => $state_str,
								'entry_country_id' => $form_values_arr['country'],
								'entry_zone_id' => $zone_id
							);
	$success_create_addbook = tep_db_perform(TABLE_ADDRESS_BOOK, $insert_address_arr);
	
	if (!$success_create_addbook) {
		//Signup failed, Go back.
		$messageStack->add_session($content, TEXT_SIGNUP_FAILED);
		tep_redirect(tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=0'));
		
		//delete the customer
		$customer_delete_sql = "delete from ".TABLE_CUSTOMERS." where customers_id='$customer_id'";
		tep_db_query($customer_delete_sql);
		
		exit;
	}
	
	$address_book_id = tep_db_insert_id();
	
	$vip_groups_obj = new vip_groups($customer_id);
	$group_id = 1; //VIP group id = 2
	$vip_groups_obj->set_vip_group($group_id, 0, $form_values_arr['country']);
	
	$update_customer_address_id = array('customers_default_address_id' => $address_book_id);
	tep_db_perform(TABLE_CUSTOMERS, $update_customer_address_id, 'update', "customers_id = '$customer_id'");
	
  	$insert_verification_arr = array(	'customers_id' => $customer_id,
    									'customers_info_value' => $form_values_arr['email_address'],
    									'info_verified' => 0,
    									'info_verification_type' => "email");
    tep_db_perform(TABLE_CUSTOMERS_INFO_VERIFICATION, $insert_verification_arr);
	
	$insert_customer_info_arr = array(	'customers_info_id' => (int)$customer_id,
										'customers_info_number_of_logons' => '0',
										'customers_info_date_account_created' => 'now()',
										'customers_info_account_created_ip' => $_SERVER['REMOTE_ADDR'],
										'customers_info_account_created_from' => SITE_ID
									);
	tep_db_perform(TABLE_CUSTOMERS_INFO, $insert_customer_info_arr);
	
	// Save Q&A
	$customers_security_obj->set_customers_id($customer_id);
	$customers_security_obj->set_customer_security_answer($secret_question_array, $secret_answer_array);
	
	//Save payment methods
	foreach ($form_values_arr['payment_method'] as $rownum => $row_payment_method_array) {
		foreach ($row_payment_method_array as $row_payment_method_id => $row_payment_method_field_array) {
			$insert_root_setting_arr = array(	'user_id' => $customer_id,
												'user_role'	=> 'customers',
												'payment_methods_id' => $row_payment_method_id,
												'payment_methods_alias'	=> '',
												'store_payment_account_book_primary' => 0);
			$success_create_root_setting = tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK, $insert_root_setting_arr);
			$root_setting_id = tep_db_insert_id();
			$store_payment_account_book_primary = '';
			$payment_methods_alias = '';
			foreach ($row_payment_method_field_array as $row_payment_method_field_id => $row_payment_method_field_value) {
				switch ($row_payment_method_field_id) {
					case 'alias':
						$payment_methods_alias = $row_payment_method_field_value;
						break;
					case 'is_primary':
						if ((int)$row_payment_method_field_value) {
							$store_payment_account_book_primary = $row_payment_method_field_value;
						}
						break;
					default:
						$insert_child_setting_arr = array(
							'store_payment_account_book_id' => $root_setting_id,
							'payment_methods_fields_id' => $row_payment_method_field_id,
							'payment_methods_fields_value' => tep_db_prepare_input($row_payment_method_field_value)
						);
						$success_create_child_setting = tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK_DETAILS, $insert_child_setting_arr);
				}
			}//end for each field
			$update_root_setting_arr = array(	'payment_methods_alias'	=> $payment_methods_alias,
												'store_payment_account_book_primary' => $store_payment_account_book_primary);
			$success_create_root_setting = tep_db_perform(TABLE_STORE_PAYMENT_ACCOUNT_BOOK, $update_root_setting_arr, 'update', "store_payment_account_book_id='$root_setting_id'");
		}//end for each payment method
	}//end for each row
	
	//Save Language
	$insert_preference_arr = array(	'user_setting_user_id' => $customer_id,
									'user_setting_key' => KEY_SP_LANG,
									'user_setting_value' => tep_db_prepare_input($val_arr[KEY_SP_LANG])
								);
	$success_create_preference = tep_db_perform(TABLE_USER_SETTING, $insert_preference_arr);
	
	//Save Time Zone
	$insert_preference_arr = array(	'user_setting_user_id' => $customer_id,
									'user_setting_key' => KEY_SP_TIME_ZONE,
									'user_setting_value' => tep_db_prepare_input($val_arr[KEY_SP_TIME_ZONE])
								);
	$success_create_preference = tep_db_perform(TABLE_USER_SETTING, $insert_preference_arr);
	
	//--Send welcome/verification email.
	$activate_url = tep_href_link(FILENAME_ACTIVATE_ACCOUNT, 'action=dir_ac&key='.$activation_code.'&email_address='.$form_values_arr['email_address'], 'SSL');
	$email_welcome = tep_mb_convert_encoding(tep_get_email_greeting(tep_mb_convert_encoding($form_values_arr['firstname'], EMAIL_CHARSET, CHARSET), tep_mb_convert_encoding($form_values_arr['lastname'], EMAIL_CHARSET, CHARSET), strtolower($form_values_arr['gender'])), CHARSET, EMAIL_CHARSET)
	   		 		. sprintf(EMAIL_SIGNUP_STEP1_BODY, $activate_url, $activate_url, $activation_code)
	      	 		. EMAIL_SIGNUP_FOOTER;
	tep_mail(tep_mb_convert_encoding($form_values_arr['firstname'] . ' ' . $form_values_arr['lastname'], EMAIL_CHARSET, CHARSET), $form_values_arr['email_address'], EMAIL_SIGNUP_STEP1_SUBJECT, $email_welcome, STORE_OWNER, STORE_OWNER_EMAIL_ADDRESS);
	$messageStack->add_session($content, sprintf(TEXT_SIGNUP_SUCCESS, $form_values_arr['email_address']), 'success');
	
	$customer_email = $form_values_arr['email_address'];
	
	unset($form_values_arr);
	tep_session_unregister($form_session_name);
	
	tep_redirect(tep_href_link(FILENAME_ACTIVATE_ACCOUNT, 'email_address='.$customer_email));
}

//Define content that gets included in the center column.
//Must match $content . ".tpl.php" in templates/content/
$content = CONTENT_SUPPLIER_SIGNUP;

//Here are the tabs. Using the array keys as the step number
//$tabs_array = array(TEXT_PERSONAL_DETAILS, TEXT_CREATE_PASSWORD, TEXT_BANK_ACCOUNTS);
$tabs_array = array(TEXT_PERSONAL_DETAILS, TEXT_SETTING_SECRET_QUESTION, TEXT_BANK_ACCOUNTS);

$get_params_always_exclude_array = array('current_step', 'action', 'remove_row_num');

//Creating session key based on filename so we know where it got its values.
$form_session_name = constant('FILENAME_SUPPLIER_SIGNUP');
if (!tep_session_is_registered($form_session_name)) {
	//tep_session_unregister($form_session_name);
	$$form_session_name = array();
	tep_session_register($form_session_name);
}

$form_values_arr = $_SESSION[$form_session_name];
//Initialize as first step/tab
$current_step = 0;
$previous_step = 0;
$next_step = 1;

//initialise obj for customers security.
//$customers_security_obj = new customers_security($_SESSION['sup_languages_id']);


$referrer_url_array = array();

if (isset($_SERVER["HTTP_REFERER"]))	$referrer_url_array = parse_url($_SERVER["HTTP_REFERER"]);

//Ignore skipping steps by modifying the url. If that happens, always display the first step (don't enter here).
if ($referrer_url_array) {
	//Ignore posting from other domains/files.
	$referrer_url_str = "{$referrer_url_array['scheme']}://{$referrer_url_array['host']}{$referrer_url_array['path']}";

	$referer_path_parts = pathinfo($_SERVER["HTTP_REFERER"]);
	$valid_path_part = pathinfo(tep_href_link(FILENAME_SUPPLIER_SIGNUP));

	//if (isset($_GET['current_step']) && (int)$_GET['current_step'] >= 0 && ($referrer_url_str == substr(tep_href_link(FILENAME_SUPPLIER_SIGNUP), 0, strlen($referrer_url_str))))	{
	if (isset($_GET['current_step'])
	    && (int)$_GET['current_step'] >= 0
	    && ($referer_path_parts['dirname'] == $valid_path_part['dirname'] && str_replace($referer_path_parts['extension'], '', $referer_path_parts['basename']) == str_replace($valid_path_part['extension'], '', $valid_path_part['basename']))
	   ) {
		$current_step = (int)$_GET['current_step'];
		if ($current_step == count($tabs_array)) {
			//We're came from the last tab so current_tab is last tab +1. don't allow forward. Loop back to last tab.
			//redirects use $previous_step, so redirect to last tab as well.
			$next_step = $previous_step = $current_step - 1;
		} elseif ($current_step == 999) {
			//Clicked Final done
			$next_step = $previous_step = $current_step;

		} else {
			$next_step = $current_step + 1;
			$previous_step = $current_step - 1;
		}
		//Don't allow previous to be negative.
		$previous_step = max($previous_step, 0);
						
		if (isset($_REQUEST['action'])) {
			//Okay, so we have stuff to validate.
			//i.e. remove payment method is a link, not a post, which is why we use $_REQUEST['action']
			$action = trim($_REQUEST['action']);
			$errorCount = 0;
			switch ($action) {
				case 'process':
					//this tab's validation function
					$posted_tab_validation_func = 'validate_tab_' . $previous_step;
					if (function_exists($posted_tab_validation_func)) {
						$update_session = true;
						$val_arr = $_POST;
						if ($current_step == 999) {
							$val_arr = $form_values_arr;
							$update_session = false;
						}

						$errorCount = $posted_tab_validation_func($val_arr, $update_session);

						if ($errorCount > 0 || $current_step == count($tabs_array)) {
							//We are validating post from last tab. always redirect back to last tab.
							//If previous tab is last tab, always redirect back. Same if found error.
							//We have another button for confirmation to go confirm and continue past last tab.
							if ($current_step == count($tabs_array)) {
								$redirect_to_step = 999;
								tep_redirect(tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . 'action=process&current_step=' . $redirect_to_step));
							} else {
								$redirect_to_step = $previous_step;
								tep_redirect(tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=' . $redirect_to_step));
							}
						}
					}
					break;

				case 'add_payment_method':

					if ($_POST['payment_method']) {
						$_SESSION[$form_session_name]['payment_method'][][$_POST['payment_method']] = '';
					}
					tep_redirect(tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=' . $previous_step));
					break;

				case 'remove_payment_method':
					if (isset($_GET['remove_row_num'])) {
						unset($_SESSION[$form_session_name]['payment_method'][$_GET['remove_row_num']]);
					}
					tep_redirect(tep_href_link(FILENAME_SUPPLIER_SIGNUP, tep_get_all_get_params($get_params_always_exclude_array) . 'current_step=' . $previous_step));
					break;
			}//end switch
		}
	}
}

//Ok no errors, lets start rendering
//Prepare vars for the forms
switch($current_step) {
	case 0:
		$user_os = tep_get_os($HTTP_USER_AGENT);
		$country_selection_mode = ($user_os == "Windows" || $user_os == "Linux") ? 'xmlhttp_mode' : 'refresh_mode';

		$day_array = array( array('id' => '', 'text' => TEXT_DAY) );
		for ($i=1; $i <= 31; $i++) {
			$day_str = sprintf('%02d', $i);
			$day_array[] = array('id' => $day_str, 'text' => $day_str);
		}

		$month_array = array( array('id' => '', 'text' => TEXT_MONTH) );
		for ($i=1; $i <= 12; $i++) {
			$month_str = sprintf('%02d', $i);
			$month_array[] = array('id' => $month_str, 'text' => constant('TEXT_MONTH_'.$i));
		}

		$year_array = array( array('id' => '', 'text' => TEXT_YEAR) );
		for ($i=1945; $i <= (int)date('Y'); $i++) {
		   $year_array[] =array('id' => $i, 'text' => $i);
		}
		
		$states_array = array();
		//if (isset($form_values_arr['country']) && (int)$form_values_arr['country'] > 0) {
		$states_array = get_country_states(44);		// Only for China
		//}

		$pref_language_array = tep_get_language_selection();

		$time_zone_array = array();
		foreach($GLOBALS['_DATE_TIMEZONE_DATA'] as $tid => $tres) {
			if (preg_match('/(?:GMT)[+-]?(\d*?)$/i', $tid, $regs)) {
				if ($regs[1] > 0 || $tid == 'GMT') {
					$time_zone_array[] = array('id' => $tid, 'text' => $tres["shortname"]);
				}
			}
		}

		break;
	case 2:
		$payment_method_array = array( array('id' => '', 'text' => PULL_DOWN_DEFAULT) );
		$payment_method_master_array = tep_get_payment_methods();
		foreach ($payment_method_master_array as $payment_method_id => $payment_method_label) {
			$payment_method_array[] = array('id' => $payment_method_id, 'text' => $payment_method_label);
		}
		break;
}

//Define the javascript file
$javascript = 'form_check.js.php';

$breadcrumb->add(NAVBAR_TITLE, tep_href_link(FILENAME_SUPPLIER_SIGNUP, '', 'SSL'));

require(DIR_WS_TEMPLATES . TEMPLATENAME_MAIN_PAGE);

require(DIR_WS_INCLUDES . 'application_bottom.php');
?>