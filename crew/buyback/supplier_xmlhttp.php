<?
error_reporting(null);
error_reporting(E_ERROR);
header("Cache-Control: no-cache");
header("Cache-Control: post-check=0,pre-check=0");
header("Cache-Control: max-age=0");
header("Pragma: no-cache");

header('Content-Type: text/xml');

include_once('includes/application_top.php');
require_once(DIR_WS_FUNCTIONS . 'supplier.php');
include_once(DIR_WS_CLASSES . 'buyback_supplier.php');
include_once(DIR_WS_CLASSES . 'vip_order.php');

require_once(DIR_WS_FUNCTIONS . 'supplier.php');

define('CNBB_REMARK_MAX_QUEUE_NO', 3);
define('CNBB_LOGIN_GAME_QUEUE_REMARK_ID', '135');

$action = $HTTP_GET_VARS['action'];

$currencies->set_decimal_places(DISPLAY_PRICE_DECIMAL);

// Internal Language
$languages_id = (isset($_SESSION['languages_id'])) ? $_SESSION['languages_id'] : '';

// Webpage Language
$sup_languages_id = (isset($_SESSION['sup_languages_id'])) ? $_SESSION['sup_languages_id'] : '';
$sup_language = (isset($_SESSION['sup_language'])) ? $_SESSION['sup_language'] : '';

$country_id = (isset($HTTP_GET_VARS['country_id'])) ? (int)$HTTP_GET_VARS['country_id'] : 0;
$buyback_cat_id = isset($HTTP_GET_VARS['buyback_cat_id']) ? (int)$HTTP_GET_VARS['buyback_cat_id'] : '';
$buyback_parent_cat_id = isset($HTTP_GET_VARS['buyback_parent_cat_id']) ? (int)$HTTP_GET_VARS['buyback_parent_cat_id'] : '';
$buyback_qty = isset($HTTP_GET_VARS['buyback_qty']) ? (int)$HTTP_GET_VARS['buyback_qty'] : '';
$buyback_products_id = isset($HTTP_GET_VARS['buyback_products_id']) ? (int)$HTTP_GET_VARS['buyback_products_id'] : '';
$buyback_req_grp_id = isset($HTTP_GET_VARS['buyback_req_grp_id']) ? (int)$HTTP_GET_VARS['buyback_req_grp_id'] : '';
$payments_id = isset($HTTP_GET_VARS['payment_id']) ? (int)$HTTP_GET_VARS['payment_id'] : 0;

$order_status_id = isset($HTTP_GET_VARS['order_status_id']) ? (int)$HTTP_GET_VARS['order_status_id'] : '';
$product_type = isset($HTTP_GET_VARS['product_type']) ? (int)$HTTP_GET_VARS['product_type'] : '';
$order_no = isset($HTTP_GET_VARS['order_no']) ? (int)$HTTP_GET_VARS['order_no'] : '';
$game_cat_id = isset($HTTP_GET_VARS['game_cat_id']) ? (int)$HTTP_GET_VARS['game_cat_id'] : '';
$start_date = isset($HTTP_GET_VARS['start_date']) ? urldecode($HTTP_GET_VARS['start_date']) : '';
$end_date = isset($HTTP_GET_VARS['end_date']) ? urldecode($HTTP_GET_VARS['end_date']) : '';

if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/' . "buyback.php")) {
	include_once(DIR_WS_LANGUAGES . $sup_language . '/' . "buyback.php");
}

echo '<response>';

if (tep_not_null($action)) {
	if ( !isset($_SERVER['HTTP_REFERER']) ||
		(strstr($_SERVER['HTTP_REFERER'], HTTP_SERVER) === FALSE && strstr($_SERVER['HTTP_REFERER'], HTTPS_SERVER) === FALSE)
		) {
		echo "You are not allowed to access from outside";
		echo '</response>';
		exit;
	}
	
	switch($action) {
		case 'get_server_list':
            $xml_str = "<servers>\n";
            if ($buyback_cat_id > 0) {
                /******************************************************************************
                	Cannot only get direct child instead look for ALL subcategories(all levels)
                	of this main category which has PRODUCTS
                ******************************************************************************/
                tep_get_subcategories($subcat_array, $buyback_cat_id);
				if ($subcat_array) {
				    $categories_select_sql = "	SELECT p2c.categories_id, cd.categories_name, p.products_id
				            					FROM " . TABLE_PRODUCTS_TO_CATEGORIES . " AS p2c
				            					INNER JOIN " . TABLE_PRODUCTS . " AS p
				            						ON (p2c.products_id = p.products_id)
				            					INNER JOIN " . TABLE_BUYBACK_PRODUCTS . " AS bp
													ON p2c.products_id=bp.products_id
				            					INNER JOIN " . TABLE_CATEGORIES_DESCRIPTION . " AS cd
				            						ON (p2c.categories_id = cd.categories_id AND cd.language_id = '" . (int)$languages_id . "')
				            					WHERE p2c.categories_id IN ('" . implode("', '", $subcat_array) . "')
				            						AND p2c.products_is_link=0
				            						AND p.custom_products_type_id=0
				            						AND p.products_bundle=''
													AND p.products_bundle_dynamic=''
				            					ORDER BY cd.categories_name";
				    $categories_result_sql = tep_db_query($categories_select_sql);

				    while ($categories_row = tep_db_fetch_array($categories_result_sql)) {
						$xml_str .= "<server>";
						$xml_str .= "<id><![CDATA[".$categories_row['categories_id']."]]></id>";
						$xml_str .= "<name><![CDATA[".strip_tags($categories_row['categories_name'])."]]></name>";
						$xml_str .= "</server>\n";
					}
				}
            }
            
            $xml_str .= "</servers>\n";
            if (tep_db_num_rows($categories_result_sql) > 0) {
                $xml_str .= "<error>0</error>";
            } else {
                $xml_str .= "<error>3</error>";
            }
            echo $xml_str;
			break;

		case 'get_backorder_buyback_server_list':
			$active_server_xml_str = $inactive_server_xml_str = '';
			
            if ($buyback_cat_id > 0) {
				$buybackSupplierObj = new buyback_supplier($buyback_cat_id);
				$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
				$buybackSupplierObj->calculate_offer_price();
				$buyback_list_status = $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status'];
				
				$buyback_quantity_unit = tep_not_null($buybackSupplierObj->game_buyback_unit) ? '('.$buybackSupplierObj->game_buyback_unit.')' : '';
				
				foreach ($buybackSupplierObj->products_arr as $products_id => $products_arr) {
					$product_display_name = $products_arr['categories_name'];
					
					//backorder may be flagged as server full (is_buyback == 0) if errors detected
					//if ((float)$products_arr['avg_offer_price'] > 0) {
						if ($buyback_list_status == '0' || (int)$products_arr['is_buyback']) {	// If list is closed, no point to distinguish active / inactive
							$active_server_xml_str .= "<server>";
							$active_server_xml_str .= "<id><![CDATA[".$products_id."]]></id>";
							$active_server_xml_str .= "<name><![CDATA[".$product_display_name."]]></name>";
							$active_server_xml_str .= "<unit_price><![CDATA[".$currencies->format($products_arr['avg_offer_price'], true, DISPLAY_CURRENCY)."]]></unit_price>";
							$active_server_xml_str .= "<min_qty><![CDATA[".(int)$products_arr['min_qty']."]]></min_qty>";
							$active_server_xml_str .= "<max_qty><![CDATA[".(int)$products_arr['max_qty']."]]></max_qty>";
							$active_server_xml_str .= "<qty_unit><![CDATA[".$buyback_quantity_unit."]]></qty_unit>";
							$active_server_xml_str .= "<is_buyback><![CDATA[".($buyback_list_status == '0' || (float)$products_arr['avg_offer_price'] <= 0 ? '-1' : (int)$products_arr['is_buyback'])."]]></is_buyback>";
							$active_server_xml_str .= "</server>\n";
						} else {
							$inactive_server_xml_str .= "<server>";
							$inactive_server_xml_str .= "<id><![CDATA[".$products_id."]]></id>";
							$inactive_server_xml_str .= "<name><![CDATA[".$product_display_name."]]></name>";
							$inactive_server_xml_str .= "<unit_price><![CDATA[".$currencies->format($products_arr['avg_offer_price'], true, DISPLAY_CURRENCY)."]]></unit_price>";
							$inactive_server_xml_str .= "<min_qty><![CDATA[".(int)$products_arr['min_qty']."]]></min_qty>";
							$inactive_server_xml_str .= "<max_qty><![CDATA[".(int)$products_arr['max_qty']."]]></max_qty>";
							$inactive_server_xml_str .= "<qty_unit><![CDATA[".$buyback_quantity_unit."]]></qty_unit>";
							$inactive_server_xml_str .= "<is_buyback><![CDATA[".(int)$products_arr['is_buyback']."]]></is_buyback>";
							$inactive_server_xml_str .= "</server>\n";
						}
					//}
				}
				
				if ($buyback_list_status == '0') {
					list($min, $max) = explode(',', $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller']);
					
					$xml_str .= "<game><list_status_msg><![CDATA[".($buyback_list_status=='0' ? sprintf(TEXT_MESSAGE_LIST_CLOSE, ($buybackSupplierObj->pending_orders-$min)) : '')."]]></list_status_msg></game>\n";
				}
            }
            
            $xml_str .= "<servers>".$active_server_xml_str.$inactive_server_xml_str."</servers>\n";
            if (count($buybackSupplierObj->products_arr) > 0) {
                $xml_str .= "<error>0</error>";
            } else {
                $xml_str .= "<error>3</error>";
            }
            echo $xml_str;
			break;
    	case 'get_buyback_server_list':
    		$active_server_xml_str = $inactive_server_xml_str = '';
			
            $selected_product = $buyback_products_id;
            $pre_sales_notice_count = 0; // Prevent email overload
            
            if ($buyback_cat_id > 0) {
				$buybackSupplierObj = new buyback_supplier($buyback_cat_id);
				$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
				$buybackSupplierObj->calculate_offer_price();
				$buyback_list_status = $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status'];
				
				$buybackSupplierObj->get_buyback_product_server_full_info();
				
				$buyback_quantity_unit = tep_not_null($buybackSupplierObj->game_buyback_unit) ? '('.$buybackSupplierObj->game_buyback_unit.')' : '';
				
				foreach ($buybackSupplierObj->products_arr as $products_id => $products_arr) {
					$product_display_name = $products_arr['categories_name'];
					
					//if ((float)$products_arr['avg_offer_price'] > 0) {
						if ($buyback_list_status == '0' || (int)$products_arr['is_buyback']) {
							if ((int)$selected_product <= 0) {
								$selected_product = $products_id;	// Get the first one as default selected product
							}
							$active_server_xml_str .= "<server>";
							$active_server_xml_str .= "<id><![CDATA[".$products_id."]]></id>";
							$active_server_xml_str .= "<name><![CDATA[".$product_display_name."]]></name>";
							$active_server_xml_str .= "<unit_price><![CDATA[".$currencies->format($products_arr['avg_offer_price'], true, DISPLAY_CURRENCY)."]]></unit_price>";
							$active_server_xml_str .= "<min_qty><![CDATA[".(int)$products_arr['min_qty']."]]></min_qty>";
							$active_server_xml_str .= "<max_qty><![CDATA[".(int)$products_arr['max_qty']."]]></max_qty>";
							$active_server_xml_str .= "<qty_unit><![CDATA[".$buyback_quantity_unit."]]></qty_unit>";
							$active_server_xml_str .= "<is_buyback><![CDATA[".($buyback_list_status == '0' || (float)$products_arr['avg_offer_price'] <= 0 ? '-1' : (int)$products_arr['is_buyback'])."]]></is_buyback>";
							$active_server_xml_str .= "</server>\n";
						} else {
							$inactive_server_xml_str .= "<server>";
							$inactive_server_xml_str .= "<id><![CDATA[".$products_id."]]></id>";
							$inactive_server_xml_str .= "<name><![CDATA[".$product_display_name."]]></name>";
							$inactive_server_xml_str .= "<unit_price><![CDATA[".$currencies->format($products_arr['avg_offer_price'], true, DISPLAY_CURRENCY)."]]></unit_price>";
							$inactive_server_xml_str .= "<min_qty><![CDATA[".(int)$products_arr['min_qty']."]]></min_qty>";
							$inactive_server_xml_str .= "<max_qty><![CDATA[".(int)$products_arr['max_qty']."]]></max_qty>";
							$inactive_server_xml_str .= "<qty_unit><![CDATA[".$buyback_quantity_unit."]]></qty_unit>";
							$inactive_server_xml_str .= "<is_buyback><![CDATA[".(int)$products_arr['is_buyback']."]]></is_buyback>";
							$inactive_server_xml_str .= "</server>\n";
						}
						
						if ($buyback_list_status == '1') {	// Only send notification if game list is OPEN
							if ((int)$products_arr['is_buyback']) {
								if ($pre_sales_notice_count < 5) {
									if (!isset($products_arr['is_server_full']) || $products_arr['is_server_full']) {
										$buybackSupplierObj->set_buyback_product_server_full_info($products_id, 0);
										$pre_sales_notice_count++;
									}
								}
							} else {
								if (!isset($products_arr['is_server_full']) || !$products_arr['is_server_full']) {
									$buybackSupplierObj->set_buyback_product_server_full_info($products_id, 1);
								}
							}
						}
					//}
				}
				
				if ($buyback_list_status == '0') {
					list($min, $max) = explode(',', $buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller']);
					
					$xml_str .= "<game><list_status_msg><![CDATA[".($buyback_list_status=='0' ? sprintf(TEXT_MESSAGE_LIST_CLOSE, ($buybackSupplierObj->pending_orders-$min)) : '')."]]></list_status_msg></game>\n";
				}
            }
			
            $xml_str .= "<servers>".$active_server_xml_str.$inactive_server_xml_str."</servers>\n";
            if (count($buybackSupplierObj->products_arr) > 0) {
                $xml_str .= "<error>0</error>";
            } else {
                $xml_str .= "<error>3</error>";
            }
            echo $xml_str;
            
            tep_xmlhttp_product_buyback_info($buyback_cat_id, $selected_product);
            
    	    break;
    	    
    	case 'get_buyback_product_info':
    		tep_xmlhttp_product_buyback_info($buyback_parent_cat_id, $buyback_products_id);
    		
    	    break;
			
    	case 'show_order_report':
    	    $xml_str = '';
			if ($buyback_req_grp_id) {
				$order_report_select_sql = "SELECT brg.buyback_request_group_id, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date,
												brg.buyback_request_group_comment, brg.buyback_request_contact_name, brg.buyback_request_contact_telephone,
												brg.currency, brg.currency_value, brg.buyback_status_id, 
												br.buyback_request_id, br.products_id, br.buyback_amount, br.buyback_request_quantity, br.buyback_sender_character,
												br.restock_character as saved_restock_character,
												pc.categories_id, pc.products_id, pd.products_name 
											FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
											LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
												ON (brg.buyback_request_group_id=br.buyback_request_group_id)
											INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
												ON (br.products_id=pc.products_id)
											INNER JOIN " . TABLE_PRODUCTS_DESCRIPTION . " AS pd 
												ON (pc.products_id=pd.products_id AND pd.language_id ='$languages_id')
											WHERE brg.customers_id = '".tep_db_input($_SESSION['customer_id'])."'
												AND buyback_request_group_user_type = '1'
												AND brg.buyback_request_group_id = '".tep_db_prepare_input($buyback_req_grp_id)."'";
				$order_report_result_sql = tep_db_query($order_report_select_sql);
				
				if ($order_report_row = tep_db_fetch_array($order_report_result_sql)) {
					$buyback_main_cat_name = tep_get_buyback_main_cat_info($order_report_row['categories_id']);
					$game_name = $buyback_main_cat_name['text'];
					$server_name = tep_display_category_path(tep_get_product_path($order_report_row['products_id']), $order_report_row['categories_id'], 'catalog', false);
					
					$req_qty = $order_report_row['buyback_request_quantity'];
					$product_name = $order_report_row['products_name'];
					$total_price = $currencies->format($order_report_row['buyback_amount'], true, $order_report_row['currency'], $order_report_row['currency_value']); //sum up the amounts if each buyback order has more more than one product in the future.
					$sender_character = $order_report_row['buyback_sender_character'];
					$restk_character = ($order_report_row['buyback_status_id'] == 1 ? $order_report_row['saved_restock_character'] : '');
					$delivery_time = $order_report_row['buyback_request_group_expiry_date'];
					$contact_name = $order_report_row['buyback_request_contact_name'];
					$contact_no = $order_report_row['buyback_request_contact_telephone'];
					$show_comments = $order_report_row['buyback_request_group_comment'];
					$order_reference = $buyback_req_grp_id;
				}
				
				$cat_cfg_array = tep_get_cfg_setting($buyback_products_id, 'product', 'BUYBACK_QUANTITY_UNIT', 'configuration_key' );
				
	            //Get Unit of measurement (ie. Million)
				$buyback_qty_unit = $cat_cfg_array['BUYBACK_QUANTITY_UNIT'];
				
        	    $xml_str .= "\n<cid><![CDATA[".trim($_SESSION['customer_id'])."]]></cid>";
        	    $xml_str .= "\n<game_name><![CDATA[".trim($game_name)."]]></game_name>";
        	    $xml_str .= "\n<server_name><![CDATA[".trim($server_name)."]]></server_name>";
        	    $xml_str .= "\n<req_qty><![CDATA[".$req_qty."]]></req_qty>";
        	    $xml_str .= "\n<uom><![CDATA[".$buyback_qty_unit."]]></uom>";
        	    $xml_str .= "\n<product_name><![CDATA[".$product_name."]]></product_name>";
        	    $xml_str .= "\n<total_price><![CDATA[".$total_price."]]></total_price>";
        	    $xml_str .= "\n<sender_character><![CDATA[".$sender_character."]]></sender_character>";
        	    $xml_str .= "\n<restk_character><![CDATA[".$restk_character."]]></restk_character>";
        	    $xml_str .= "\n<delivery_time><![CDATA[".$delivery_time."]]></delivery_time>";
        	    $xml_str .= "\n<contact_name><![CDATA[".$contact_name."]]></contact_name>";
        	    $xml_str .= "\n<contact_no><![CDATA[".$contact_no."]]></contact_no>";
        	    $xml_str .= "\n<show_comments><![CDATA[".$show_comments."]]></show_comments>";
        	    $xml_str .= "\n<order_reference><![CDATA[".$order_reference."]]></order_reference>";
			}
            echo $xml_str;
    		break;
			
    	case 'search_order_history':
    		if (file_exists(DIR_WS_LANGUAGES . $sup_language . '/' . "my_order_history.php")) {
				include_once(DIR_WS_LANGUAGES . $sup_language . '/' . "my_order_history.php");
			}
			
    	    $xml_str = '';
    	    $allow_search_num_days_ago = 60;
			
			if (tep_not_null($start_date)) {
				if (strpos($start_date, ':') !== false) {
					$start_date_str = " brg.buyback_request_group_date >= '" . $start_date . "'";
				} else {
					$start_date_str = " DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') >= '" . $start_date . "'";
				}
			} else {
				$sformat = 'Y-m-d H:i:s';
				$start_date = date($sformat);
				$start_date_str = " brg.buyback_request_group_date >= '" . $start_date . "'";
			}
			
			if (tep_not_null($end_date)) {
				if (strpos($end_date, ':') !== false) {
					$end_date_str = " brg.buyback_request_group_date <= '" . $end_date . "'";
				} else {
					$end_date_str = " DATE_FORMAT(brg.buyback_request_group_date, '%Y-%m-%d') <= '" . $end_date . "'";
				}
			} else {
				$eformat = 'Y-m-d H:i:s';
				$end_date = date($eformat);
				$end_date_str = " brg.buyback_request_group_date <= '" . $end_date . "'";
			}
			
			$buyback_request_select_sql = "	SELECT brg.buyback_request_group_id, brg.buyback_request_group_date, brg.buyback_request_group_expiry_date, brg.currency, brg.currency_value, brg.show_restock, brg.buyback_status_id,
												br.buyback_request_id, br.products_id, br.buyback_amount, br.buyback_request_quantity, bs.buyback_status_name, br.buyback_quantity_confirmed,  br.restock_character AS saved_restock_character,
												br.buyback_dealing_type, pc.categories_id, brg.buyback_request_group_served, br.buyback_request_screenshot_before_name, br.buyback_request_screenshot_after_name
											FROM ". TABLE_BUYBACK_REQUEST_GROUP . " AS brg
											LEFT JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
												ON brg.buyback_request_group_id=br.buyback_request_group_id
											INNER JOIN " . TABLE_PRODUCTS_TO_CATEGORIES . " AS pc 
												ON br.products_id=pc.products_id
											INNER JOIN " . TABLE_BUYBACK_STATUS . " AS bs 
												ON (brg.buyback_status_id=bs.buyback_status_id AND bs.language_id='".$sup_languages_id."')
											WHERE " . $start_date_str . " 
												AND " . $end_date_str . " 
												AND brg.customers_id = '" . tep_db_input($_SESSION['customer_id']) . "' 
												AND brg.buyback_request_order_type = '0'
												AND buyback_request_group_user_type = '1'";
			if ($order_status_id != 0) {
			    $buyback_request_select_sql .= " AND brg.buyback_status_id = '".$order_status_id."' ";
			}
			if ($order_no) {
				$buyback_request_select_sql .= "AND brg.buyback_request_group_id = '$order_no'";
			}
			
			tep_get_subcategories($category_array, $game_cat_id);
			if ($category_array) {
				$category_str = implode("', '", $category_array);
			} else {
				$category_str = $game_cat_id;
			}
			$buyback_request_select_sql .= "\n     AND pc.categories_id IN ('" . $category_str . "')";

			$buyback_request_select_sql .= "\n         GROUP BY brg.buyback_request_group_id
											           ORDER BY brg.buyback_status_id asc, brg.buyback_request_group_date desc ";
											           
			$buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
			$buyback_request_num_rows = tep_db_num_rows($buyback_request_result_sql);
			
			$xml_str .= "\n<results>";
			while ($buyback_request_row = tep_db_fetch_array($buyback_request_result_sql)) {
				$order_latest_remark = '';
				$allow_to_upload = 'no';
				
				$currencies->set_decimal_places($currencies->currencies[$buyback_request_row['currency']]['decimal_places']);
                
				//counting queuing number
				$total_queue = 0;
    			if ((int)$buyback_request_row['buyback_status_id'] == 1 && $buyback_request_row['buyback_request_group_served'] == '0' && $buyback_request_row['buyback_dealing_type'] == 'ofp_deal_on_game') {
    			    $count_q_select_sql = "SELECT count(*) AS q_no
    		                               FROM ". TABLE_BUYBACK_REQUEST_GROUP ." AS brg
    		                               INNER JOIN ". TABLE_BUYBACK_REQUEST ." AS br
    		                                   ON (brg.buyback_request_group_id=br.buyback_request_group_id)
    		                               WHERE brg.buyback_request_group_id < '". (int) $buyback_request_row['buyback_request_group_id'] ."'
    		                                   AND brg.buyback_status_id = '1'
    		                                   AND brg.buyback_request_group_served = '0'
    		                                   AND (br.buyback_dealing_type = 'ofp_deal_on_game'
    		                                   		OR br.buyback_dealing_type = 'vip_deal_on_game')";
        			$count_q_result_sql = tep_db_query($count_q_select_sql);
        			$count_q_row = tep_db_fetch_array($count_q_result_sql);
        			$total_queue = $count_q_row['q_no'];
    			}
    			
    			// $allow_to_upload: for the user who havent upload the ss yet.
    			if (!tep_not_null($buyback_request_row['buyback_request_screenshot_after_name']) && !tep_not_null($buyback_request_row['buyback_request_screenshot_before_name']) && BUYBACK_ENABLE_UPLOAD_SCREENSHOT == 'true') {
					$allow_to_upload = 'yes';
				}
				
				//Get restock character.
				$show_restock = 0;
				
				if ($buyback_request_row['buyback_status_id'] != 1) {
			        $receiver_char_name_message = '';
			  	} else {
			  		$show_restock = (int)$buyback_request_row['show_restock'];
			    	if ($total_queue == 0) {
			    		//if ($buyback_request_row['buyback_request_group_served'] == '0')	$show_restock = 0;	// Hack here to hide restock character from showing to supplier until our Operation Oficer click on Serve Customer button
			    		
			    		if ($show_restock) {
			    			if (tep_not_null($buyback_request_row['saved_restock_character'])) {
			    				$receiver_char_name_message = $buyback_request_row['saved_restock_character'];
			    			} else {
				        		$receiver_char_name_message = tep_get_buyback_restock_char($buyback_request_row['products_id']);
				        	}
				        } else {
				        	$receiver_char_name_message = TEXT_CHARACTER_NAME_IN_PREPARATION;
				        }
				    } else if ($total_queue > 0) {
				        $receiver_char_name_message = sprintf(TEXT_CHARACTER_NAME_IN_PREPARATION_QUEUE, $total_queue);
				    }
				}
				
				$buyback_main_cat_name = tep_get_buyback_main_cat_info($buyback_request_row['categories_id']);
				$product_display_name = tep_display_category_path(tep_get_product_path($buyback_request_row['products_id']), $buyback_request_row['categories_id'], 'catalog', false);
				
				$xml_str .= "\n<row>";
				$xml_str .= "\n<req_grp_id><![CDATA[".$buyback_request_row['buyback_request_group_id']."]]></req_grp_id>";
				$xml_str .= "\n<req_id><![CDATA[".$buyback_request_row['buyback_request_id']."]]></req_id>";
				$xml_str .= "\n<game_name><![CDATA[".$buyback_main_cat_name['text']."]]></game_name>";
				$xml_str .= "\n<server_name><![CDATA[".$product_display_name."]]></server_name>";
				$xml_str .= "\n<req_qty><![CDATA[".$buyback_request_row['buyback_request_quantity']."]]></req_qty>";
				$xml_str .= "\n<confirmed_qty><![CDATA[".$buyback_request_row['buyback_quantity_confirmed']."]]></confirmed_qty>";
				$xml_str .= "\n<amount><![CDATA[".$currencies->format($buyback_request_row['buyback_amount'], true, $buyback_request_row['currency'], $buyback_request_row['currency_value'])."]]></amount>";
				$xml_str .= "\n<show_restock><![CDATA[".$show_restock."]]></show_restock>";
				$xml_str .= "\n<restock_character><![CDATA[".$receiver_char_name_message."]]></restock_character>";
				$xml_str .= "\n<status_name><![CDATA[".$buyback_request_row['buyback_status_name']."]]></status_name>";
				$xml_str .= "\n<current_status_id><![CDATA[".$buyback_request_row['buyback_status_id']."]]></current_status_id>";
				$xml_str .= "\n<total_queue><![CDATA[".$total_queue."]]></total_queue>";
				// allow_to_upload: for the user who havent upload the ss yet.
				$xml_str .= "\n<allow_to_upload><![CDATA[".$allow_to_upload."]]></allow_to_upload>";
				
				
				//Get expirable	
				$show_expiry = 0;
				$expiry_time = '00:00:00';
				if ((int)$buyback_request_row['buyback_status_id'] == 1 || (int)$buyback_request_row['buyback_status_id'] == 2) {
					if ($buyback_request_row['buyback_status_id'] == 2) {
						$total_received_select_sql = "SELECT sum(br.buyback_quantity_received) as total_received 
														FROM ".TABLE_BUYBACK_REQUEST." AS br
														WHERE br.buyback_request_group_id = '{$buyback_request_row['buyback_request_group_id']}' 
														GROUP BY buyback_request_group_id";
						$total_received_result_sql = tep_db_query($total_received_select_sql);
						if ($total_received_row = tep_db_fetch_array($total_received_result_sql)) {
							$total_received = (int)$total_received_row['total_received'];
						}
						if ($total_received == 0) {
							$show_expiry = 1;
						}
					} else {
						$show_expiry = 1;
					}
					
					if ($show_expiry) {
						if ($buyback_request_row['buyback_request_group_expiry_date'] != '0000-00-00 00:00:00') {
							$expiry_time = gmdate('H:i:s', strtotime($buyback_request_row['buyback_request_group_expiry_date'])+ (8*60*60));
						} else {
							$show_expiry = 0;
						}
					}
					
					if ((int)$buyback_request_row['buyback_status_id'] == 1) {
						if ($total_queue > 0) {
							if ($total_queue < (int)CNBB_REMARK_MAX_QUEUE_NO) {
								$predefined_order_comment_select_sql = "SELECT orders_comments_text 
																		FROM " . TABLE_ORDERS_COMMENTS . "
																		WHERE orders_comments_id = '".CNBB_LOGIN_GAME_QUEUE_REMARK_ID."'";
								$predefined_order_comment_result_sql = tep_db_query($predefined_order_comment_select_sql);
								$predefined_order_comment_row = tep_db_fetch_array($predefined_order_comment_result_sql);
								$order_latest_remark = $predefined_order_comment_row['orders_comments_text'];
							}
						} else {
							$buyback_remark_select_sql = "	SELECT comments 
															FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
															WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
																AND set_as_buyback_remarks = 1 
																AND customer_notified = 1 
																AND (comments IS NOT NULL AND comments <> '')";
							$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
							$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
							$order_latest_remark = $buyback_remark_row['comments'];
						}
					} else {
						$buyback_remark_select_sql = "	SELECT comments 
														FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
														WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
															AND set_as_buyback_remarks = 1 
															AND customer_notified = 1 
															AND (comments IS NOT NULL AND comments <> '')";
						$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
						$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
						$order_latest_remark = $buyback_remark_row['comments'];
					}
				} else {
					$buyback_remark_select_sql = "	SELECT comments 
													FROM " . TABLE_BUYBACK_STATUS_HISTORY . "
													WHERE buyback_request_group_id = '".tep_db_input($buyback_request_row['buyback_request_group_id'])."' 
														AND set_as_buyback_remarks = 1 
														AND customer_notified = 1 
														AND (comments IS NOT NULL AND comments <> '')";
					$buyback_remark_result_sql = tep_db_query($buyback_remark_select_sql);
					$buyback_remark_row = tep_db_fetch_array($buyback_remark_result_sql);
					$order_latest_remark = $buyback_remark_row['comments'];
				}
				
				$xml_str .= "\n<show_expiry><![CDATA[".$show_expiry."]]></show_expiry>";
				$xml_str .= "\n<expiry_time><![CDATA[".$expiry_time."]]></expiry_time>";
				$xml_str .= "\n<buyback_remarks><![CDATA[".nl2br($order_latest_remark)."]]></buyback_remarks>";
				$xml_str .= "\n</row>";
			}
			$xml_str .= "\n</results>";
			$xml_str .= "\n<num_results><![CDATA[".$buyback_request_num_rows."]]></num_results>";
            echo $xml_str;
    		break;
    	case 'show_payment_report':
            // update report read
			$update_payment_read_sql = "UPDATE " . TABLE_STORE_PAYMENTS . " 
			                            SET store_payments_read_mode = 1 
			                            WHERE store_payments_id = '" . $payments_id . "'";
			tep_db_query($update_payment_read_sql);
			
            $result_html = '<table border="0" width="100%" cellspacing="0" cellpadding="3">
                                <tr>
                                    <td align="center">
                                        <table border="1" width="80%" cellspacing="0" cellpadding="2">
                                            <tr>
                                                <td class="main"><b>'. POP_PAYMENT_REPORT_DATETIME .'</b></td>
                                                <td class="main"><b>'. POP_PAYMENT_REPORT_PAYMENT_MESSAGE .'</b></td>
                                            </tr>';
			$payment_message_select_sql = " SELECT comments, date_added 
			                                FROM ". TABLE_STORE_PAYMENTS_HISTORY ."
			                                WHERE payee_notified = '1'
			                                AND store_payments_id = '". $payments_id ."'
			                                ORDER BY store_payments_history_id, date_added";
            $payment_message_result_sql = tep_db_query($payment_message_select_sql);
            while ($payment_message_row = tep_db_fetch_array($payment_message_result_sql)) {
                 $result_html .= '          <tr>
                                                <td class="main" width="50%" valign="top">'. $payment_message_row['date_added'] .'</td>
								                <td class="main" width="50%" valign="top">'. ($payment_message_row['comments'] ? nl2br($payment_message_row['comments']) : '&nbsp;') .'</td>
                                            </tr>';
            }
            
            $result_html .= '           </table>
                                    </td>
                                </tr>
                            </table>';
			
			echo $result_html;
			
    		break;
    	case 'update_buyback_request_group':
    		//valid ids ?
    		$buyback_request_group_id = isset($_POST['req_grp_id']) ? (int)$_POST['req_grp_id'] : 0;
			$buyback_quantity_confirmed = isset($_POST['sent_qty']) ? (int)$_POST['sent_qty'] : 0;
			$buyback_request_id = isset($_POST['req_id']) ? (int)$_POST['req_id'] : 0;
		
			$restock_character = '';
			$buyback_request_select_sql = "	SELECT br.buyback_dealing_type, br.products_id, br.buyback_unit_price, br.buyback_request_quantity, br.restock_character, l.locking_by 
											FROM " . TABLE_BUYBACK_REQUEST_GROUP . " AS brg 
											INNER JOIN " . TABLE_BUYBACK_REQUEST . " AS br 
												ON (brg.buyback_request_group_id = br.buyback_request_group_id) 
											LEFT JOIN " . TABLE_LOCKING . " AS l 
												ON (l.locking_trans_id = brg.buyback_request_group_id AND l.locking_table_name = '" . tep_db_input(TABLE_BUYBACK_REQUEST_GROUP) . "') 
											WHERE brg.buyback_request_group_id = '" . tep_db_input($buyback_request_group_id) . "' 
												AND brg.buyback_status_id = 1 
												AND brg.customers_id = '".tep_db_input($_POST['customer_id'])."' 
												AND br.buyback_request_id = '" . tep_db_input($buyback_request_id) . "'";
			$buyback_request_result_sql = tep_db_query($buyback_request_select_sql);
			
			if ($buyback_request_row = tep_db_fetch_array($buyback_request_result_sql)) {
				//Only allow +/- 10%
				$buyback_request_quantity = $buyback_request_row['buyback_request_quantity'];
				//$buyback_request_quantity_max = (int)$buyback_request_quantity * 1.1;
				//$buyback_request_quantity_min = (int)$buyback_request_quantity * 0.9;
				
				if (!is_numeric($buyback_quantity_confirmed) || $buyback_quantity_confirmed < 0 || $buyback_quantity_confirmed != (int)$buyback_request_quantity) {
				//if ($buyback_quantity_confirmed < $buyback_request_quantity_min || $buyback_quantity_confirmed > $buyback_request_quantity_max) {
					echo '<error_msg>'.TEXT_ERROR_INVALID_QTY.'</error_msg>';
				} else {
					$login_id = '';
					$update_confirmed_qty_array = array('buyback_quantity_confirmed' => $buyback_quantity_confirmed);
					
					if (!tep_not_null($buyback_request_row['restock_character'])) {
						$update_confirmed_qty_array['restock_character'] = tep_get_buyback_restock_char($buyback_request_row['products_id']);
					}
					
					if ($buyback_quantity_confirmed != $buyback_request_quantity) {
						//update the $$$. Only for view. Not used for calculating credit balance or payout
						//because that is calculated against the unit price column.
						$buyback_unit_price = $buyback_request_row['buyback_unit_price'];
						$update_confirmed_qty_array['buyback_amount'] = $buyback_unit_price * $buyback_quantity_confirmed;
					}
	
					tep_db_perform(TABLE_BUYBACK_REQUEST, $update_confirmed_qty_array, 'update', "buyback_request_group_id='$buyback_request_group_id' AND buyback_request_id='$buyback_request_id'");
					
					//Automatically set status to processing & Hide character status
					$update_request_group_status_array = array('buyback_status_id' => '2', 'show_restock' => '0', 'buyback_request_group_last_modified' => 'now()');
					tep_db_perform(TABLE_BUYBACK_REQUEST_GROUP, $update_request_group_status_array, 'update', "buyback_request_group_id='$buyback_request_group_id'");
					
					if (tep_not_null($buyback_request_row['locking_by'])) {
						tep_order_lock($buyback_request_group_id, ORDERS_LOG_UNLOCK_OWN_ORDER, $login_id);
					}
					
					$buyback_history_data_array = array('buyback_request_group_id' => $buyback_request_group_id,
								                        'buyback_status_id' => '2',
														'date_added' => 'now()',
														'customer_notified' => '0',
														'comments' => ''
														);
					tep_db_perform(TABLE_BUYBACK_STATUS_HISTORY, $buyback_history_data_array);
					//Update to db
					echo '<result>updated</result>';
				}
			} else {
				echo '<error_msg>'.TEXT_ERROR_GET_DATA_FAILED.'</error_msg>';
			}
    		break;
    	case 'redirect_upload_to_curl':
    			$customer_id = $_SESSION['customer_id'];
    			$req_grp_id = $_GET['req_grp_id'];
    			$file_name_1 = $_GET['file_name_1'];
    			$file_name_2 = $_GET['file_name_2'];
    			$sup_language = $_GET['sup_language'];
    			
    			$max_file_size = '500'; // KB
				$allow_file_type = array('jpg');
				$result = '';
				$result2 = '';
				$error = "";
				
				if (tep_not_null($file_name_1)) {
					$result = check_uploading_file ($file_name_1, $allow_file_type, $max_file_size);
				}
				
				if (tep_not_null($file_name_2)) {
					$result2 = check_uploading_file ($file_name_2, $allow_file_type, $max_file_size);
				}
				
				if ($result == 'approved' && $result2 == 'approved') {
					$file_1 = $_FILES[$file_name_1]['tmp_name'];
					$file_2 = $_FILES[$file_name_2]['tmp_name'];
	    			unset($response);

                    $url = tep_upload_href_link(FILENAME_UPLOAD, "action=ajax_upload&customer_id=".$customer_id."&req_grp_id=".$req_grp_id."&file_name_1=".$file_name_1."&file_name_2=".$file_name_2."&sup_language=".$sup_language, 'SSL');
                    $agent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)";
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL,$url);
                    curl_setopt($ch, CURLOPT_VERBOSE, 1);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
                    curl_setopt($ch, CURLOPT_POST, 1);
					curl_setopt($ch, CURLOPT_POSTFIELDS, array($file_name_1 => "@$file_1", $file_name_2 => "@$file_2"));
                    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
                    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);  //Windows 2003 Compatibility
                    $response = curl_exec($ch);
                    curl_close($ch);

                    echo $response;
					
				} else {
					if ($result != 'approved') {
						echo '<error_msg_1><![CDATA['.$result.']]></error_msg_1>';
					}
					if ($result2 != 'approved') {
						echo '<error_msg_2><![CDATA['.$result2.']]></error_msg_2>';
					}
				}
    		break;
		default:
			echo "<result>Unknown request!</result>";
			break;
	}
}

echo '</response>';

function check_uploading_file ($fileElementName, $allow_file_type, $max_file_size) {
	$msg = '';
	$pathinfo_arr = pathinfo($_FILES[$fileElementName]['name']);
	$file_type = strtolower($pathinfo_arr['extension']);
	
	if (($max_file_size * 1024) > $_FILES[$fileElementName]['size']) {
		if ($_FILES[$fileElementName]['error'] > 0) {
			$msg = $_FILES[$fileElementName]['error'];
		} elseif (!tep_not_null($_FILES[$fileElementName]['tmp_name']) || $_FILES[$fileElementName]['tmp_name'] == 'none') {
			$msg = '8';
		} else {
			if (in_array($file_type, $allow_file_type)) {
				$msg = 'approved';
			} else {
				$msg = '9';
			}
		}
	} else {
		$msg = '10';
	}
	return $msg;
}

function tep_xmlhttp_product_buyback_info($buyback_parent_cat_id, $buyback_products_id) {
	global $currencies, $sup_languages_id;
	
	$xml_str = '';
    $showError = false;
    $min_value = 0;
    $max_value = 0;
    $product_name = '&nbsp;';
    $product_id = '&nbsp;';

    if ($buyback_parent_cat_id > 0) {
        $actual_cat_id = tep_get_actual_product_cat_id($buyback_products_id);
		
		$buybackSupplierObj = new buyback_supplier($buyback_parent_cat_id, $buyback_products_id);
		$currencies->set_decimal_places($buybackSupplierObj->_DECIMAL_PLACES_DISPLAY);
		$buybackSupplierObj->calculate_offer_price();
		
		if (!isset($buybackSupplierObj->products_arr[$buyback_products_id])) {
			$showError = true;
		} else {
			$product_info_arr = $buybackSupplierObj->products_arr[$buyback_products_id];
			if ($buybackSupplierObj->buyback_supplier_settings['ofp_buyback_list_controller_status']=='0' || $product_info_arr['is_buyback'] == false) {
				$showError = true;
			}
		}
    }

    if ($showError) {
        $xml_str .= "<error>3</error>";
        $xml_str .= "<notice>0</notice>";
    } else {
       	$unit_price_base = (float)$product_info_arr['avg_offer_price'];	/*USD*/
		$unit_price_localised = $currencies->do_raw_conversion($unit_price_base, true, DISPLAY_CURRENCY);	/*Localised*/
    	$unit_price_localised_display = $currencies->format($unit_price_base, true, DISPLAY_CURRENCY);
		
        $xml_str .= "<error>0</error>";
        $xml_str .= "<notice>1</notice>";
	    $xml_str .= "<product_id><![CDATA[".$buyback_products_id."]]></product_id>";
	    $xml_str .= "<product_name><![CDATA[".$buybackSupplierObj->category_cat_path.' > '.$product_info_arr['categories_name']."]]></product_name>";
		$xml_str .= "<unit_price><![CDATA[".$unit_price_localised."]]></unit_price>";
	    $xml_str .= "<unit_price_display><![CDATA[".$unit_price_localised_display."]]></unit_price_display>";
        $xml_str .= "<min><![CDATA[".$product_info_arr['min_qty']."]]></min>";
        $xml_str .= "<max><![CDATA[".$product_info_arr['max_qty']."]]></max>";
	    $xml_str .= "<product_unit_name><![CDATA[".$product_info_arr['qty_unit']."]]></product_unit_name>";
	    if (isset($product_info_arr['upper_min_qty']) && $product_info_arr['upper_min_qty'] > 0 && $product_info_arr['upper_min_qty'] < $product_info_arr['max_qty']) {
			$xml_str .= "<bo_exact_qty_msg><![CDATA[".sprintf(TEXT_MATCH_BACKORDER_EXACT_AMOUNT, $product_info_arr['upper_min_qty'], $product_info_arr['max_qty'], $product_info_arr['upper_min_qty']+1, $product_info_arr['max_qty']-1)."]]></bo_exact_qty_msg>";
		}
    }
    
    echo '<selected_product>';
    echo $xml_str;
    echo '</selected_product>';
    
    unset($buybackProductObj);
}
?>