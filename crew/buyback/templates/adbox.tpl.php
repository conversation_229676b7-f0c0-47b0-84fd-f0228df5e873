<?php /* adbox template  */ ?>
  <tbody>
      <tr>
        <td><?=tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '4')?></td>
      </tr>
<?php
    foreach ($box_img_arr as $ad_object) {
      echo '<tr>
                <td align="center">' . $ad_object . '</td>
            </tr>
            <tr>
                <td>'. tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '4') .'</td>
            </tr>';
    }
?>
  </tbody>
<?php
$boxContent = '';
?>