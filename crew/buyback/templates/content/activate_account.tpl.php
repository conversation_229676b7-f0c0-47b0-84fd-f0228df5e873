<?php
$boxContent = '';

switch($action) {
	case 'suc_ac':
		$boxHeading = HEADER_TITLE_ACTIVATE_ACCOUNT;
		
		$boxContent = '<script type="text/javascript" src="includes/javascript/hints.js"></script>
						<table width="100%" border="0" cellspacing="2" cellpadding="0">
							<tr>
								<td class="instruction" colspan="2">' . TEXT_ACCOUNT_ACTIVATE_SUCCESS_ACTIVATE . '</td>
							</tr>
						</table>';
		
		break;
	default:
		$boxHeading = HEADER_TITLE_ACTIVATE_ACCOUNT;
		
		$boxContent = '	<script type="text/javascript" src="includes/javascript/hints.js"></script>
						<table width="100%" border="0" cellspacing="2" cellpadding="0">
							<tr>
								<td align="left">';
		
		$boxContent .= tep_draw_form('activate_account', FILENAME_ACTIVATE_ACCOUNT, tep_get_all_get_params(array('key')).'action=dir_ac', 'post');
		$boxContent .= '		<table width="100%" border="0" cellspacing="2" cellpadding="0">
									<tr>
										<td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '3') . '</p></td>
									</tr>';
		if (isset($_REQUEST['email_address']) && tep_not_null($_REQUEST['email_address'])) {
			$email_existence_select_sql = "	SELECT customers_id 
											FROM " . TABLE_CUSTOMERS . " 
											WHERE customers_email_address = '" . tep_db_input($_REQUEST['email_address']) . "' 
												AND account_activated = '0' ";
			$email_existence_result_sql = tep_db_query($email_existence_select_sql);
			
			if ($email_existence_row = tep_db_fetch_array($email_existence_result_sql)) {
				$boxContent .= '<tr>
									<td class="instruction" colspan="2">' . sprintf(TEXT_ACCOUNT_ACTIVATE_SUCCESS_REGISTER, $_REQUEST['email_address']) . '</td>
								</tr>
								<tr>
									<td height="5" colspan="2"></td>
								</tr>';
			}
		}
		
		$boxContent .= '		<tr>
									<td class="instruction" colspan="2">' . TEXT_INSTRUCTIONS_ACTIVATE . '</td>
								</tr>
								<tr>
									<td height="5" colspan="2"></td>
								</tr>
								<tr>
									<td class="inputLabel">' . TEXT_EMAIL_ADDRESS . '</td>
									<td class="inputField">' . (isset($_REQUEST['email_address']) && tep_not_null($_REQUEST['email_address']) ? $_REQUEST['email_address'] . tep_draw_hidden_field('email_address', $_REQUEST['email_address']) : tep_draw_input_field('email_address', (isset($_GET['email_address']) ? $_GET['email_address'] : ''), 'size="40"', true) . '<div class="hint"><p>' . TEXT_HELP_EMAIL_ADDRESS . '</p></div>') . '</td>
								</tr>
								<tr>
									<td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
								</tr>
								<tr>
									<td><p>' . TEXT_ACTIVATION_CODE . '</p></td>
									<td>' . tep_draw_input_field('key', (isset($_GET['key']) ? $_GET['key'] : ''), 'size="40"', true) . '<div class="hint"><p>' . TEXT_HELP_ACTIVATION_CODE . '</p></div></td>
								</tr>
								<tr>
									<td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
								</tr>
								<tr>
									<td colspan="2" align="right"><p>' . tep_image_submit(DIR_WS_IMAGES.'button_next.gif', TEXT_ACTIVATE_ACCOUNT, 'SSL') . '</p></td>
								</tr>
								<tr>
									<td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
								</tr>
							</table>
						</form>';
		
		$boxContent .= '	</td>
						</tr>
					</table>';
		
		break;
}

if (tep_not_null($boxContent))	require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
?>