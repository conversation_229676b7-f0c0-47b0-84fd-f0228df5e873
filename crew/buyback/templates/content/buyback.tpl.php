<script type="text/javascript">
<!--
	var global_loading_message = '<?=TEXT_LOADING_MESSAGE?>';
	var global_check_price_message = '<?=TEXT_MAIN_PLEASE_SELECT_GAME?>';

	
	function set_selected_server(products_id) {
		var languages_id = '<?=$languages_id?>';
		
		var serverListObj = document.getElementById('wbb_input_product_select');
		serverListObj.value = products_id;
		onBuybackServerSelection(serverListObj, languages_id);
	}

	function set_selected_game(game_cat_id, server_cat_id) {
		//Simulate changing game list dropdown.
		if (server_cat_id == 'undefined') {
			server_cat_id = '0';
		}
		
		var gameListObj = document.getElementById('wbb_input_game_select');
		var languages_id = '<?=$languages_id?>';
		gameListObj.value = game_cat_id;
		
		callEvent_onBuybackGameSelection(gameListObj, server_cat_id);
	}

	function refresh_game_list() {
        var gameListObj = document.getElementById('wbb_input_game_select');
        var languages_id = '<?=$languages_id?>';
        var server_cat_id = document.getElementById('wbb_input_product_select').value;
    
        if(gameListObj.value == 0){
            alert('<?=JS_NO_GAME_SELECTED?>');
        }else{
            callEvent_onBuybackGameSelection(gameListObj, server_cat_id);
        }
        
    }
	
	function callEvent_onBuybackGameSelection(gameListObj, server_cat_id) {
		var languages_id = '<?=$languages_id?>';
		divhtml = '';
		onBuybackGameSelection(gameListObj, languages_id, server_cat_id, "<?=SID?>");
	}

    function showError(msg_id) {
    	var errorMsg = '';
		if (msg_id == 1) {
    		errorMsg = '<?=TEXT_QUANTITY_INVALID?>';
    	} else if (msg_id == 2) {
    		errorMsg = '<?=TEXT_FORM_INCOMPLETE?>';
    	} else if (msg_id == 3) {
    		errorMsg = '<?=TEXT_NOT_ACCEPT_BUYBACK?>';
    	} else if (msg_id != null) {
    		errorMsg = msg_id;
    	}

        document.getElementById('wbb_tbody_error').className = 'hide';
        document.getElementById('wbb_span_error').innerHTML = errorMsg;
        document.getElementById('wbb_tbody_error').className = 'show';
    }

    function showNotice(msg_id) {
    	var noticeMsg = '';
    	if (msg_id == 1) {
    		noticeMsg = '<?=TEXT_EMAIL_IF_MORE_SUPPLY?>';
    	}
        document.getElementById('wbb_tbody_notice').className = 'hide';
        document.getElementById('wbb_span_notice').innerHTML = noticeMsg;
        document.getElementById('wbb_tbody_notice').className = 'show';
    }

	var divhtml = '';
	var div_row_count = 0;
	function add_server_listing_row(products_name, unit_price, buy_qty, cat_id, products_id, is_buyback) {
		if (is_buyback == true) {
			var make_order_link = '<input type="button" onClick="location.href=\''+'<?=tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('')))?>'+'#\'; set_selected_server(\''+products_id+'\', \''+cat_id+'\');" class="inputButton" value="<?=TEXT_SELECT_LINK?>">';
			var status_text = '<span class="navyBlueIndicator"><?=TEXT_ACTIVE?><\/span>';
		} else {
			var make_order_link = '<a href="javascript:;" class="inactiveLink"><?=TEXT_SELECT_LINK?></a>';
			var status_text = '<span class="redIndicator"><?=TEXT_INACTIVE?><\/span>';
		}
		
		if (is_buyback == '1') {
			var make_order_link = '<input type="button" onClick="location.href=\''+'<?=tep_href_link(FILENAME_BUYBACK, tep_get_all_get_params(array('')))?>'+'#\'; set_selected_server(\''+products_id+'\', \''+cat_id+'\');" class="inputButton" value="<?=TEXT_SELECT_LINK?>">';
			var status_text = '<span class="navyBlueIndicator"><?=TEXT_ACTIVE?><\/span>';
		} else if (is_buyback == '-1') {	// Game buyback list is closed
			var make_order_link = '<a href="javascript:;" class="inactiveLink"><?=TEXT_SELECT_LINK?></a>';
			var status_text = '<span class="redIndicator"><?=TEXT_LIST_CLOSE?><\/span>';
		} else {
			var make_order_link = '<a href="javascript:;" class="inactiveLink"><?=TEXT_SELECT_LINK?></a>';
			var status_text = '<span class="redIndicator"><?=TEXT_INACTIVE?><\/span>';
		}
		
		var row_style = (div_row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
		divhtml += '<tbody>'
				+ '<tr height="25" class="'+row_style+'"><td align="left" width="45%" class="ordersRecords">' + products_name + '<\/td>'
				+ '<td align="center" width="15%" class="ordersRecords">' +  unit_price + '<\/td>'
				+ '<td align="center" width="15%" class="ordersRecords">' +  buy_qty + '<\/td>'
				+ '<td align="center" width="10%" class="ordersRecords">' + status_text + '<\/td>'
				+ '<td align="center" width="##DYNAMIC_WIDTH##" class="ordersRecords">' + make_order_link + '<\/td>'
				+ '<\/tr>'
				+ '<\/tbody>';
		div_row_count++;
	}
	
	function show_server_listing_div() {
		if (div_row_count < 20) {
			divhtml = divhtml.replace(/##DYNAMIC_WIDTH##/ig, "15%");
		} else {
			divhtml = divhtml.replace(/##DYNAMIC_WIDTH##/ig, "13%");
		}
		
		div_row_count = 0;
		
		//document.getElementById('wbb_div_server_listing').innerHTML += divhtml;
		document.getElementById('wbb_div_server_listing').innerHTML = '<table border="0" cellpadding="0" cellspacing="0" width="100%">'+divhtml+'</table>';
		document.getElementById('wbb_div_server_listing').style.height = 250;
	}

 	function do_add_to_favourites() {
		var product_id = document.getElementById("wbb_input_product_select").value;
		var game_cat_id = document.getElementById("wbb_input_game_select").value;
		if (game_cat_id > 0 && product_id > 0) {
			document.getElementById('fvl_input_game_select').value = game_cat_id;
			document.getElementById('fvl_input_product_select').value = product_id;
			document.add_to_favourites.submit();
		}
	}
	
	function deal_type_select(id) {
	    document.getElementById(id).checked = true;
	}

// -->
</script>
<?
switch ($action) {
	case 'confirm_msg':
		$boxContent = '
			<table border="0" cellpadding="3" cellspacing="2">
				<tbody>
					<tr>
						<td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
					</tr>
		            <tr>
						<td colspan="2" class="messageStackSuccess">' . TEXT_ORDER_POSTED_INSTRUCTIONS . '</td>
		       		</tr>
					<tr>
						<td colspan="2" height="5">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '5').'</td>
		          	</tr>
		  		</tbody>
			</table>';
		require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE_DARK);
		
		// Displaying extra latest news
		include(DIR_WS_MODULES . FILENAME_EXTRA_LATEST_NEWS);
		echo "<br>";
		
		$boxContent = '
			<table border="0" cellpadding="3" cellspacing="2" align="center">
            	<tbody>
					<tr>
						<td colspan="2" background="' . DIR_WS_IMAGES . 'space.gif'. '" height="4"></td>
					</tr>
					<tr>
						<td><p>' . tep_image_submit('', TEXT_ADD_NEW, 'onClick="window.location = \''.tep_href_link(FILENAME_BUYBACK).'\';"') . '</p></td>
						<td><p>' . tep_image_submit('', TEXT_DELIVER_NOW, 'onClick="window.location = \''.tep_href_link(FILENAME_MY_ORDER_HISTORY).'\';"') . '</p></td>
			      	</tr>
					<tr>
						<td colspan="2" height="5">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '5').'</td>
					</tr>
				</tbody>
			</table>';
			
		require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
		
		break;
		
	case 'update2':
		echo tep_draw_form('buyback_form', FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=confirm' , "post", "onSubmit=\"document.buyback_form.wbb_button_confirm.disabled=true;\"");
		echo tep_draw_hidden_field('wbb_hidden_unit_price', (isset($form_values_arr['wbb_hidden_unit_price']) && $form_values_arr['wbb_hidden_unit_price'] ? $form_values_arr['wbb_hidden_unit_price'] : ''), ' id="wbb_hidden_unit_price" ');
		//Order Summary
		
		$boxContent = '
			<table border="0" cellpadding="3" cellspacing="2">
				<tbody>
					<tr>
						<td colspan="2">' . tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"') . '<span class="title-text">' . TEXT_HEADING_SUMMARY . '</span></td>
					</tr>
					<tr>
						<td width="200"><p>' . TEXT_GAME . '</p></td>
						<td><p><b>' . $buybackSupplierObj->category_cat_path . '</b></p></td>
					</tr>
					<tr>
						<td><p>' . TEXT_SERVER . '</p></td>
						<td><p><b>' . $buybackSupplierObj->products_arr[$selected_product_id]['categories_name'] . '</b></p></td>
					</tr>
					<tr>
						<td><p>' . TEXT_QUANTITY . '</p></td>
						<td><p><b>' . (isset($form_values_arr['wbb_input_qty']) && $form_values_arr['wbb_input_qty'] ? $form_values_arr['wbb_input_qty'] : '') . ' ' . $buybackSupplierObj->products_arr[$selected_product_id]['qty_unit'] . ' ' . TEXT_OF  . ' '. $buybackSupplierObj->category_cat_path.' > '.$buybackSupplierObj->products_arr[$selected_product_id]['categories_name'].'</b></p></td>
					</tr>
					<tr>
						<td><p>' . TEXT_TOTAL_PRICE . '</p></td>
						<td><p><b>' . $currencies->format((isset($form_values_arr['wbb_total_price']) && $form_values_arr['wbb_total_price'] ? $form_values_arr['wbb_total_price'] : 0), false, DISPLAY_CURRENCY) . '</b></p></td>
					</tr>';
		
		if (tep_not_null($_SESSION[$form_session_name]['trading_place'])) {
			$boxContent .= '<tr>
								<td><p>' . TEXT_MEETING_POINT . '</p></td>
								<td><p><b><span class="redIndicator">' . $_SESSION[$form_session_name]['trading_place'] . '</spam></b></p></td>
							</tr>';
		}
		
		$boxContent .= '<tr>
							<td colspan="2" height="5">'. tep_image(DIR_WS_IMAGES . 'space.gif', '', '5') . '<td>
						</tr>
					</tbody>
			</table>';
			
		require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
		
		//Seller's character
		$boxContent = '
			<table border="0" cellpadding="3" cellspacing="2">
				<tbody>
					<tr>
						<td colspan="2">' . tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"') . '<span class="title-text">' . TEXT_HEADING_CHARACTER_INFO . '</span></td>
					</tr>
					<tr>
						<td width="200"><p>' . TEXT_CHARACTER_NAME . '</p></td>
						<td><p>' . tep_draw_input_field('wbb_input_character_name', (isset($form_values_arr['wbb_input_character_name']) && $form_values_arr['wbb_input_character_name'] ? $form_values_arr['wbb_input_character_name'] : ''), 'id="wbb_input_character_name" size="31"', true) . '<br>' .TEXT_CHARACTER_NAME_HELP. '</p></td>
					</tr>';
        
		if (count($buyback_setting_value_array)) {
		    $boxContent .= '
		            <tr>
						<td><p>' . TEXT_DEALING_TYPE . '</p></td>
						<td><p>';
		    
		    foreach ($buyback_setting_value_array AS $radio_button_key) {
				$boxContent .= tep_draw_radio_field('wbb_input_dealing_type', $radio_button_key, (isset($_SESSION[$form_session_name]['wbb_input_dealing_type']) && ($_SESSION[$form_session_name]['wbb_input_dealing_type'] == $radio_button_key) ? true : false), '', 'id="'. $radio_button_key .'"') . "<span onmouseover=\"this.className='showHandOverBuyback';\" onClick=\"deal_type_select('". $radio_button_key ."')\">" . $dealing_type_array[$radio_button_key] . "</span>";
		    }
		    $boxContent .= '&nbsp;'. TEXT_FIELD_REQUIRED .'</p>
                        </td>
				    </tr>';
		}
		
		$boxContent .= '
		            <tr>
						<td valign="top"><p>' . TEXT_DELIVERY_TIME . '</p></td>
						<td><p>' . tep_draw_pull_down_menu('wbb_input_delivery_time', $delivery_time_arr, (isset($form_values_arr['wbb_input_delivery_time']) && $form_values_arr['wbb_input_delivery_time'] ? $form_values_arr['wbb_input_delivery_time'] : ''), 'id="wbb_input_delivery_time"', true) . '<br>' .TEXT_DELIVERY_TIME_HELP. '</p></td>
					</tr>
					<tr>
						<td colspan="2" height="5">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '5').'</td>
					</tr>
				</tbody>
			</table>';
			
		require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
		
		//Contact Info
		$boxContent = '
			<table border="0" cellpadding="3" cellspacing="2">
				<tbody>
					<tr>
						<td colspan="2">' . tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"') . '<span class="title-text">' . TEXT_HEADING_CONTACT_INFO . '</span></td>
			     	</tr>
					<tr>
						<td width="200"><p>' . TEXT_CONTACT_NAME . '</p></td>
						<td><p>' . tep_draw_input_field('wbb_input_contact_name', (isset($form_values_arr['wbb_input_contact_name']) && $form_values_arr['wbb_input_contact_name'] ? $form_values_arr['wbb_input_contact_name'] : $default_full_name), 'id="wbb_input_contact_name" size="31"', true) . '</p></td>
					</tr>
					<tr>
						<td><p>' . TEXT_CONTACT_NO . '</p></td>
						<td><p>' . tep_draw_input_field('wbb_input_contact_no', (isset($form_values_arr['wbb_input_contact_no']) && $form_values_arr['wbb_input_contact_no'] ? $form_values_arr['wbb_input_contact_no'] : $default_contact_no), 'id="wbb_input_contact_no" size="31"', true) . '</p></td>
					</tr>
					<tr>
						<td colspan="2" height="5">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '5').'</td>
					</tr>
				</tbody>
			</table>';
			
		require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
		
		$boxContent = '
			<table border="0" cellpadding="3" cellspacing="2">
				<tbody>
					<tr>
						<td width="200" valign="top"><p>' . TEXT_ADDITIONAL_COMMENTS . '</p></td>
						<td><p>
						' . tep_draw_textarea_field('wbb_textarea_comments', true, '40', '5', (isset($form_values_arr['wbb_textarea_comments']) && $form_values_arr['wbb_textarea_comments'] ? $form_values_arr['wbb_textarea_comments'] : ''), 'id="wbb_textarea_comments"') . '<br />
						'.tep_image('securimage_show.php?sid='.md5(uniqid(time()))).'<br />
						'.tep_draw_input_field("captcha_code", '', ' size="6" maxlength="6" id="captcha_code"').'
						</p></td>
					</tr>
					<tr>
						<td colspan="2" height="5">'.tep_image(DIR_WS_IMAGES . 'space.gif', '', '5').'</td>
					</tr>
				</tbody>
			</table>';
		
		require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
		
		$boxContent = '
			<table border="0" cellpadding="3" cellspacing="2">
				<tbody>
					<tr>
						<td>&nbsp;</td>
						<td align="center" valign="top">' . tep_image_submit(THEMA.'button_confirm.gif', TEXT_BUTTON_LABEL_STEP3, 'id="wbb_button_confirm" name="wbb_button_confirm" onClick="document.buyback_form.submit();document.buyback_form.wbb_button_confirm.disabled=true;"', 'generalBtn').'
						<br/><div name="wbb_div_msgField4" id="wbb_div_msgField4" class="generalText"></div></td>
					</tr>
				</tbody>
			</table>';
		
		echo $boxContent;
		echo '</form>';
		
		$boxContent = '
			<table border="0" width="100%" cellspacing="0" cellpadding="0" >
				<!-- Error Display -->
				<tbody class="hide" id="wbb_tbody_error" name="wbb_tbody_error">
					<tr>
						<td align="left" valign="top" class="errorText">' . 
							TEXT_NOTES . '<br>
							<div id="wbb_icon_error" name="wbb_icon_error" style="float: left;">' . 
								tep_image(DIR_WS_ICONS . 'icon_error.gif', ICON_WARNING) . '</div>
							<div id="wbb_span_error" name="wbb_span_error"></div>
		            	</td>
		            </tr>
	            </tbody>
	              	<tr>
	            		<td width="1" colspan="6">' . tep_draw_separator('pixel_trans.gif', '1', '1') . '</td>
	            	</tr>
	               	<!-- Notice Display -->
	            	<tbody class="hide" id="wbb_tbody_notice" name="wbb_tbody_notice">
	            		<tr>
		            		<td align="left" valign="top" class="noticeText">
		            			' . TEXT_NOTES . '<br>
								<div id="wbb_icon_notice" name="wbb_icon_notice" style="float: left;"></div>
								<div id="wbb_span_notice" name="wbb_span_notice"></div>
		            		</td>
	            		</tr>
	            	</tbody>
	                <tr>
	            		<td width="1" colspan="6">' . tep_draw_separator('pixel_trans.gif', '1', '1') . '</td>
	            	</tr>
				</table>';
		echo $boxContent;
		
		break;
		
	case 'update':
?>
		<table border="0" width="100%" cellspacing="0" cellpadding="0">
			<tbody>
		    	<tr>
					<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '16')?></td>
			        <td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>"></td>
			        <td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '16')?></td>
		      	</tr>
			    <tr>
			      	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
			        <td>
			            <!-- start repeat -->
			            <table cellpadding="0" border="0" cellspacing="5" width="100%">
			                <tbody>
			                    <tr>
			                      	<td>
									<!-- Start inner table -->
									<?=tep_draw_form('buyback_form', FILENAME_BUYBACK, tep_get_all_get_params(array('action')) . 'action=update2' , "post", "onSubmit=\"document.buyback_form.wbb_button_confirm.disabled=true;\"")?>
							    	<table border="0" width="100%" cellspacing="1" cellpadding="2" class="buttonBox">
					            		<tr>
											<td colspan="6">
												<table border="0" width="100%" cellspacing="0" cellpadding="0" class="buttonBox">
													<tr>
														<td class="generalText" width="50%" valign="top"><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '') . '&nbsp;<span class="title-text">' . TEXT_INSTRUCTIONS_HEADER1 . '</span><br><p>'. TEXT_INSTRUCTIONS_LEFT?></p></td>
							                			<td ><?=tep_draw_separator('pixel_trans.gif', '5', '1')?></td>
														<td class="generalText" width="50%" valign="top">
															<table border="0" width="100%" cellspacing="0" cellpadding="0" >
											                    <!-- Error Display -->
											            		<tbody class="<?=($wbb_error_found) ? 'show' : 'hide'?>" id="wbb_tbody_error" name="wbb_tbody_error">
											            			<tr>
												            			<td align="left" valign="top" class="errorText">
												            				<?=TEXT_NOTES?><br>
																			<div id="wbb_icon_error" name="wbb_icon_error" style="float: left;">
																			<?=tep_image(DIR_WS_ICONS . 'icon_error.gif', ICON_WARNING)?></div>
																			<div id="wbb_span_error" name="wbb_span_error"><?=$wbb_error_message?></div>
												            			</td>
												            		</tr>
											            		</tbody>
											                 	<tr>
											            			<td width="1" colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
											            		</tr>
										                    	<!-- Notice Display -->
										            			<tbody class="<?=($wbb_notice_found) ? 'show' : 'hide'?>" id="wbb_tbody_notice" name="wbb_tbody_notice">
											            			<tr>
												            			<td align="left" valign="top" class="noticeText">
												            				<?=TEXT_NOTES?><br>
																			<div id="wbb_icon_notice" name="wbb_icon_notice" style="float: left;"></div>
																			<div id="wbb_span_notice" name="wbb_span_notice"><?=$wbb_notice_message?></div>
												            			</td>
											            			</tr>
										            			</tbody>
											                	<tr>
											            			<td width="1" colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
											            		</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td colspan="3" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
													</tr>
												</table>
											</td>
					            		</tr>
					                	<tr>
					            			<td width="1" colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					            		</tr>
					                	<tr>
					            			<td width="1" colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					            		</tr>
					            		<!-- Game and server -->
					            		<tbody class="show" id="wbb_tbody_step1" name="wbb_tbody_step1">
					                		<tr>
					                			<td rowspan="3" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					                			<td align="left" valign="top"><p><?=TEXT_GAME?></td>
												<td rowspan="3" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					                			<td height="40" align="left" valign="top">
						                			<?='<p>'. tep_draw_pull_down_menu('wbb_input_game_select', $wbb_game_list_arr, (isset($form_values_arr['wbb_input_game_select']) && $form_values_arr['wbb_input_game_select'] ? $form_values_arr['wbb_input_game_select'] : ''), ' id="wbb_input_game_select" onChange="callEvent_onBuybackGameSelection(this, \'0\');" ')?>
					                                </p><div name="wbb_div_msgField1" id="wbb_div_msgField1" class="generalText"></div></td>
					                            <td rowspan="3" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					                            <td rowspan="3" valign="top">
					                            	<div class="generalTextHide" id="wbb_div_add_shortcut"><?=TEXT_ADD_SHORTCUT_TO ?><br>
														<?=tep_button(HEADER_TITLE_MY_FAVOURITE_LINKS, HEADER_TITLE_MY_FAVOURITE_LINKS, '', 'onclick="do_add_to_favourites()"')?>
					                            	</div>
					                            </td>
					                		</tr>
					                		<tr>
						                		<td align="left" valign="top"><p><?=TEXT_SERVER?></td>
					                			<td align="left" valign="top"><?='<p>'. tep_draw_pull_down_menu('wbb_input_product_select', array(0 => array('id' => 0, 'text' => PULL_DOWN_DEFAULT)), (isset($form_values_arr['wbb_input_product_select']) && $form_values_arr['wbb_input_product_select'] ? $form_values_arr['wbb_input_product_select'] : ''), ' id="wbb_input_product_select" disabled onChange="onBuybackServerSelection(this, \''.$languages_id.'\')" ')?>
					                                </p><br><div name="wbb_div_msgField2" id="wbb_div_msgField2" class="generalText"></div>
					                          	</td>
					                		</tr>
					            		</tbody>
					                	<tr>
					            			<td width="1" colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					            		</tr>
					            		<!-- Quantity -->
					            		<tbody class="hide" id="wbb_tbody_step2" name="wbb_tbody_step2">
					                		<tr>
					                			<td rowspan="2" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
						                		<td align="left" valign="top"><p><?=TEXT_QUANTITY?></td>
					                			<td rowspan="2" width="1" ><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					                			<td align="left" valign="top">
					                				<?=tep_draw_input_field('wbb_input_qty', (isset($form_values_arr['wbb_input_qty']) && $form_values_arr['wbb_input_qty'] ? $form_values_arr['wbb_input_qty'] : 0), ' size="20" maxlength="16" id="wbb_input_qty" disabled ')?>
					                			    <span id="wbb_span_product_unit_name" name="wbb_span_product_unit_name" class="generalText"></span>
						                			<br><div name="wbb_div_msgField2" id="wbb_div_msgField2" class="generalText"></div>
						                		</td>
						                		<td rowspan="2" width="1" >&nbsp;</td>
					                            <td rowspan="2" valign="top">
													<p>
							                			<?=TEXT_PRODUCT?>
								                		<span id="wbb_span_product_name" name="wbb_span_product_name" class="redIndicator"></span>
						                			    <br><?=TEXT_MIN?>
						                			    <span id="wbb_span_min_value" name="wbb_span_min_value" class="redIndicator"></span>
						                			    <?=TEXT_MAX?>
						                			    <span id="wbb_span_max_value" name="wbb_span_max_value" class="redIndicator"></span>
						                			    <br><?=TEXT_UNIT_PRICE?>
						                			    <span id="wbb_span_unit_price" name="wbb_span_unit_price" class="redIndicator"></span><br>
						                			    <span id="span_match_bo_exact_qty_msg" name="span_match_bo_exact_qty_msg"></span>
					                			    </p>
					                            </td>
					                		</tr>
					                		<tr>
					                			<td>&nbsp;</td>
					                			<td align="left" valign="top"><?=tep_image_submit(THEMA.'button_confirm.gif', TEXT_BUTTON_LABEL_STEP2, 'id="wbb_button_confirm" name="wbb_button_confirm" onClick="document.buyback_form.submit();document.buyback_form.wbb_button_confirm.disabled=true;"', 'generalBtn')?>
					                			<br/><div name="wbb_div_msgField3" id="wbb_div_msgField3" class="generalText"></div></td>
					                		</tr>
					                    </tbody>
					                	<tr>
					            			<td width="1" colspan="6"><?=tep_draw_separator('pixel_trans.gif', '1', '1')?></td>
					            		</tr>
							        </table>
							        <?
							          echo tep_draw_hidden_field('wbb_hidden_unit_price', (isset($form_values_arr['wbb_hidden_unit_price']) && $form_values_arr['wbb_hidden_unit_price'] ? $form_values_arr['wbb_hidden_unit_price'] : ''), ' id="wbb_hidden_unit_price" ');
					   				  echo tep_draw_hidden_field('wbb_hidden_products_id', (isset($form_values_arr['wbb_hidden_products_id']) && $form_values_arr['wbb_hidden_products_id'] ? $form_values_arr['wbb_hidden_products_id'] : ''), ' id="wbb_hidden_products_id" ');
					   				  echo tep_draw_hidden_field('wbb_hidden_min_value', (isset($form_values_arr['wbb_hidden_min_value']) && $form_values_arr['wbb_hidden_min_value'] ? $form_values_arr['wbb_hidden_min_value'] : ''), ' id="wbb_hidden_min_value" ');
					   				  //echo tep_draw_hidden_field('wbb_hidden_max_value', (isset($form_values_arr['wbb_hidden_max_value']) && $form_values_arr['wbb_hidden_max_value'] ? $form_values_arr['wbb_hidden_max_value'] : ''), ' id="wbb_hidden_max_value" ');
					   				  echo tep_draw_hidden_field('wbb_hidden_product_name', (isset($form_values_arr['wbb_hidden_product_name']) && $form_values_arr['wbb_hidden_product_name'] ? $form_values_arr['wbb_hidden_product_name'] : ''), ' id="wbb_hidden_product_name" ');
					   				  echo tep_draw_hidden_field('wbb_div_game_cat_id', '', ' id="wbb_div_game_cat_id" ');
							        ?>
							        </form>
									
							        <script type="text/javascript">
							        <!--
					                    //Mozilla caches form fields too well. Init on load if JS enabled.
					                    document.getElementById('wbb_input_game_select').selectedIndex = 0;
					                    document.getElementById('wbb_input_product_select').selectedIndex = 0;
							        // -->
							        </script>
									
									<div class="hide" height="1" width="1">
									<?
										echo tep_draw_form('add_to_favourites', FILENAME_MY_FAVOURITE_LINKS, tep_get_all_get_params(array('action')) . 'action=add');
										echo tep_draw_hidden_field('fvl_input_game_select', '0', 'id="fvl_input_game_select"');
										echo tep_draw_hidden_field('fvl_input_product_select', '0', 'id="fvl_input_product_select"');
										echo '</form>';
									?>
									</div>
									<!-- end inner table -->
								 </td>
			             	</tr>
						</tbody>
					</table>
					<!-- end repeat -->
				</td>
				<td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
			</tr>
		  	<tr>
		      	<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
		      	<td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
		      	<td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
			</tr>
			</tbody>
		</table>
		<div id="div_game_reopen_count_down" class="messageStackWarning" style="height:20px; padding-top: 5px;"></div>
		<table cellpadding="0" border="0" cellspacing="0" width="100%" bgcolor="#f5f5f5">
			<tbody>
			<tr>
				<td height="3" width="3"><?=tep_image(DIR_WS_IMAGES . 'box_grey01a.gif', '', '3', '3')?></td>
		        <td background="<?=DIR_WS_IMAGES . 'box_grey01b.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
		        <td height="3" width="3"><?=tep_image(DIR_WS_IMAGES . 'box_grey01c.gif', '', '3', '3')?></td>
			</tr>
			<tr>
				<td background="<?=DIR_WS_IMAGES?>box_grey01h.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
		        <td>
		        	<table cellpadding="0" cellspacing="0" height="257" width="100%">
		          		<tbody>
		          		<tr>
		            		<td height="20">
		            			<table cellpadding="0" cellspacing="2">
		              				<tr>
		                				<td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"')?><span class="title-text"><?=TEXT_HEADING_SERVER_LIST?></span></td>
		              				</tr>
		            			</table>
		            		</td>
		          		</tr>
		          		<tr>
		            		<td align="center">
		            			<table cellpadding="0" border="0" cellspacing="0" width="100%">
		                			<tbody>
		                			<tr>
		                  				<td height="40" align="right" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02a.gif', '', '5', '40')?></td>
		                  				<td height="40" background="<?=DIR_WS_IMAGES?>box_grey02b.gif">
		                    				<table border="0" cellpadding="0" cellspacing="0" height="40" width="100%">
			                    				<tbody>
			                    				<tr>
							                      	<td align="left" width="45%" class="title-text"><p><?=TEXT_HEADING_SERVER?></p></td>
																			<td align="center" width="15%" class="title-text"><p><?=TEXT_HEADING_UNIT_PRICE?></p></td>
																			<td align="center" width="15%" class="title-text"><p><?=TABLE_HEADING_PURCHASE_QTY?><BR><div id='wbb_div_table_heading_product_unit_name' name='wbb_span_table_heading_product_unit_name' class='title-text'></div></p></td>
																			<td align="center" width="10%"  class="title-text"><p><?=TEXT_HEADING_STATUS?></p></td>
																			<td align="right" width="15%"><?='<a href="javascript:;" onClick="refresh_game_list();" class="categoryNavigation">'.BUTTON_REFRESH.'</a>'?></td>
			                    				</tr>
			                    				<tr>
			                      					<td colspan="5" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
			                    				</tr>
				                    			</tbody>
			                    			</table>
		                 	 			</td>
		                  				<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02c.gif', '', '5', '40')?></td>
		                			</tr>
		                			<tr>
		                  				<td height="40" bgcolor="#f5f5f5" width="5"></td>
		                  				<td width="100%" height="0">
			                  				<table border="0" cellpadding="0" cellspacing="0" height="180" width="100%">
			                    				<tbody>
			                    				<tr>
			                      					<td colspan="3" valign="top">
														<div id="wbb_div_server_listing" name="wbb_div_server_listing" style="position:relative; height:250px; width:100%; overflow:auto;"><?='<span class="title-text">'.TEXT_MAIN_PLEASE_SELECT_GAME.'</span>'?></div>
			                      					</td>
			                    				</tr>
			                    				<tr>
			                      					<td colspan="3" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
			                    				</tr>
			                    				</tbody>
			                  				</table>
			                  			</td>
			                  			<td height="40" width="5" bgcolor="#f5f5f5">&nbsp;</td>
			                		</tr>
			                		<tr>
			                  			<td height="19" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02f.gif', '', '5', '19')?></td>
			                  			<td background="<?=DIR_WS_IMAGES?>box_grey02e.gif" height="19">&nbsp;</td>
			                  			<td height="19"><?=tep_image(DIR_WS_IMAGES . 'box_grey02d.gif', '', '5', '19')?></td>
			                		</tr>
			              			</tbody>
				              	</table>
							</td>
						</tr>
						</tbody>
					</table>
				</td>
		        <td background="<?=DIR_WS_IMAGES?>box_grey01d.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
			</tr>
		    <tr>
		        <td height="3" width="3"><?=tep_image(DIR_WS_IMAGES . 'box_grey01g.gif', '', '3', '3')?></td>
		        <td background="<?=DIR_WS_IMAGES?>box_grey01f.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
		        <td height="3" width="3"><?=tep_image(DIR_WS_IMAGES . 'box_grey01e.gif', '', '3', '3')?></td>
		    </tr>
		    </tbody>
		</table>
		<?
			$pre_select_server = isset($form_values_arr['wbb_input_product_select']) ? $form_values_arr['wbb_input_product_select'] : 0;
			$pre_select_game = isset($form_values_arr['wbb_input_game_select']) ? $form_values_arr['wbb_input_game_select'] : $top_cat_id;
		?>
		<script type="text/javascript">
			set_selected_game('<?=$pre_select_game?>', '<?=$pre_select_server?>');
		</script>
<?
		break;

}//end first form
?>