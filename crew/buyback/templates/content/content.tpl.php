<?
if(!function_exists('eval_buffer')) {
	function eval_buffer($string) {
		ob_start();
		eval("$string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_print_buffer')) {
	function eval_print_buffer($string) {
		ob_start();
		eval("print $string[2];");
		$return = ob_get_contents();
		ob_end_clean();
		return $return;
	}
}

if(!function_exists('eval_html')) {
	function eval_html($string) {
		$string = preg_replace_callback("/(<\?=)(.*?)\?>/si", "eval_print_buffer",$string);
		return preg_replace_callback("/(<\?php|<\?)(.*?)\?>/si", "eval_buffer",$string);
	}
}
?>
<table border="0" width="100%" cellspacing="0" cellpadding="0">
  <tbody>
      <tr>
		<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '40')?></td>
        <td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
            <table width="100%" border="0" cellpadding="0" cellspacing="2">
                <tbody><tr>
                  <td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', ' hspace="4"') . '&nbsp;<span class="title-text">' . NAVBAR_TITLE . '</span>' ?></td>
                </tr>
                </tbody>
            </table>
        </td>
        <td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '40')?></td>
      </tr>
      <tr>
      	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
        <td>
            <!-- start repeat -->
            <table cellpadding="2" border="0" cellspacing="0" width="100%">
                <tbody>
                    <tr>
                      <td><?=eval_html(TEXT_INFORMATION)?></td>
                    </tr>
                </tbody>
            </table>
            <!-- end repeat -->
        </td>
        <td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
      </tr>
      <tr>
      	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
        <td>
            <!-- start repeat -->
            <table cellpadding="2" border="0" cellspacing="0" width="100%">
                <tbody>
                    <tr>
                      <td>
            			<table border="0" width="100%" cellspacing="0" cellpadding="2">
              				<tr>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
                				<td align="left"><?=(tep_not_null($_GET['action']) && $_GET['action'] == 'order_history') ? tep_image_button(THEMA .'close1.gif', TEXT_CLOSED_DOWN, '','onClick="javascript:window.close()";') : tep_image_button(THEMA.'button_back.gif', TEXT_BACK, tep_href_link(FILENAME_FAQ))?></td>
                				<td width="10"><?=tep_draw_separator('pixel_trans.gif', '10', '1')?></td>
              				</tr>
            			</table>
                      </td>
                    </tr>
                </tbody>
            </table>
            <!-- end repeat -->
        </td>
        <td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
      </tr>
      <tr>
          <td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
          <td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
          <td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
      </tr>
  </tbody>
</table>