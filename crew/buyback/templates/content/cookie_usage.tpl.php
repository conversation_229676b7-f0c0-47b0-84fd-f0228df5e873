<!--    <table border="1" width="100%" cellspacing="0" cellpadding="0">
      <tr>
        <td>
        <table width="100%" height="30" border="0" cellpadding="0" cellspacing="0" background="<?=$title_center_image_url?>">
         <tr>
            <td width="100" align="center" valign="top"><img src="<?=$title_left_image_url?>" width="100" height="30"></td>
            <td class="pageHeadingTitle" valign="middle" align="center"><?=HEADING_TITLE?></td>
            <td width="102"><img src="<?=$title_right_image_url?>" width="102" height="30"></td>
         </tr>
        </table>
        </td>
      </tr>
      <tr>
        <td><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
      </tr>
      <tr>
        <td class="main"><table border="1" width="40%" cellspacing="0" cellpadding="0" align="right">
          <tr>
            <td><?php new infoBoxHeading(array(array('text' => BOX_INFORMATION_HEADING))); ?></td>
          </tr>
          <tr>
            <td><?php new infoBox(array(array('text' => BOX_INFORMATION))); ?></td>
          </tr>
        </table><?php echo TEXT_INFORMATION; ?></td>
      </tr>
    </table>
-->

    
	<table border="0" width="100%" cellspacing="0" cellpadding="0">
	  <tbody>
          <tr>
			<td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side01.gif', '', '3', '40')?></td>
            <td background="<?=DIR_WS_IMAGES . 'box_side02.gif'?>">
                <table width="100%" border="0" cellpadding="0" cellspacing="2">
                    <tbody><tr>
                      <td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', ' hspace="4"') . '&nbsp;<span class="title-text">' . HEADING_TITLE . '</span>' ?></td>
                    </tr>
                    </tbody>
                </table>
            </td>
            <td width="3" height="40"><?=tep_image(DIR_WS_IMAGES . 'box_side03.gif', '', '3', '40')?></td>
          </tr>
	      <tr>
	      	<td width="3" background="<?=DIR_WS_IMAGES . 'box_side08.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	        <td>
	            <!-- start repeat -->
	            <table cellpadding="2" border="0" cellspacing="0" width="100%">
	                <tbody>
	                    <tr>
		                      <td>
								<table border="0" width="40%" cellspacing="0" cellpadding="0" align="right">
						          <tr>
						            <td class="title-text"><?=BOX_INFORMATION_HEADING?></td>
						          </tr>
						          <tr>
						            <td class="infoBoxContents"><?=BOX_INFORMATION?></p></td>
						          </tr>
						        </table>		                      
								<span class="infoBoxContents"><?=TEXT_INFORMATION?></span>		                      	
							 </td>
	                    </tr>
	                    <tr>
							<td>
								<table border="0" width="100%" cellspacing="0" cellpadding="2">
					              <tr>
					                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
					                <td align="right"><?php echo tep_image_button(THEMA.'button_continue.gif', IMAGE_BUTTON_CONTINUE, '', 'onClick="window.location=\''.tep_href_link(FILENAME_DEFAULT).'\'"' ) ; ?></td>
					                <td width="10"><?php echo tep_draw_separator('pixel_trans.gif', '10', '1'); ?></td>
					              </tr>
					            </table>
							</td>
	                    </tr>
	                </tbody>
	            </table>
	            <!-- end repeat -->
	        </td>
	        <td width="3" background="<?=DIR_WS_IMAGES . 'box_side04.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
	      </tr>
	      <tr>
	          <td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side07.gif', '', '3', '16')?></td>
	          <td background="<?=DIR_WS_IMAGES . 'box_side06.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '', '16')?></td>
	          <td width="3" height="16"><?=tep_image(DIR_WS_IMAGES . 'box_side05.gif', '', '3', '16')?></td>
	      </tr>
	  </tbody>
	</table>	                      