<?
$infolinks_grp_select_sql = "	SELECT * 
								FROM " . TABLE_INFOLINKS_GROUPS . " 
								WHERE infolinks_groups_id IN ('".implode("', '", $faq_infolinks_groups_id_arr)."') 
								ORDER BY infolinks_groups_sort_order, infolinks_groups_title ASC;";
$infolinks_grp_result_sql = tep_db_query($infolinks_grp_select_sql);
while ($infolinks_grp_row = tep_db_fetch_array($infolinks_grp_result_sql)) {
	$boxContent = "</td></tr>";
  	if ((int)$infolinks_grp_row['infolinks_groups_show_title']==1) {
  		$boxHeading = $infolinks_grp_row['infolinks_groups_title'];
  		$background_color = 'class="infoBoxLeft"';
  		$corner_left = 'square';
  		$corner_right = 'square';
  		$background_image=trim($infolinks_grp_row['infolinks_groups_bg_image']);
  	} else {
  		$boxHeading = "";
  		$background_color = '';
  		$corner_left = '';
  		$corner_right = ''; 
  		$background_image=""; 
  	}
  	
  	$background_color = 'class="storeBox"';
  	$heading_title_style = 'class="storeBoxHeading"';
	$boxText = 'storeText';
  	$corner_left = 'square';
  	$corner_right = 'square';
  	
  	$group_id = (int)$infolinks_grp_row['infolinks_groups_id'];
  	
  	$infolinks_select_sql = "	SELECT i.*, ic.infolinks_contents_id
  								FROM " . TABLE_INFOLINKS . " AS i
  								INNER JOIN " . TABLE_INFOLINKS_CONTENTS . " AS ic
  									ON (i.infolinks_id=ic.infolinks_id)
  								WHERE i.infolinks_groups_id = '" . $group_id . "'
  									AND ic.infolinks_contents_page=1
  								ORDER BY i.infolinks_sort_order, i.infolinks_title ASC;";
	$infolinks_result_sql = tep_db_query($infolinks_select_sql);
	
  	$rowCount = tep_db_num_rows($infolinks_result_sql);
  	
	$count = 0;
	while ($infolinks_row = tep_db_fetch_array($infolinks_result_sql)) {
		if ((int)$infolinks_row['infolinks_new_window'] == 1) {
			$newwintag="target='_blank'";
		} else {
			$newwintag="target='_self'";
		}
		
		if (trim($infolinks_row['infolinks_URL']) == '') {
			$url = tep_href_link(FILENAME_INFOLINKS, tep_get_all_get_params(array('id', 'content_id')) . "id=".$infolinks_row['infolinks_id']."&content_id=".$infolinks_row['infolinks_contents_id']);
		} else {
			if (strpos($infolinks_row['infolinks_URL'], 'http') === 0) {
				$http_length = strpos($infolinks_row['infolinks_URL'], "//") + 2;
				if (strpos($infolinks_row['infolinks_URL'], $_SERVER['HTTP_HOST']) == $http_length) {
					if ($request_type == 'SSL' && strpos($infolinks_row['infolinks_URL'], 'https') !== 0) {
						$infolinks_row['infolinks_URL'] = 'https://' . substr($infolinks_row['infolinks_URL'], $http_length);
					}
				}
			}
			$url = $infolinks_row['infolinks_URL'];
		}
		
		$leftImage = '&nbsp;';
		$rightImage = '&nbsp;';
		
		if (trim($infolinks_row['infolinks_image']) != "") {
			if ($infolinks_row['infolinks_align']=='default' || $infolinks_row['infolinks_align']=='left') {
				$leftImage='<a href="'.$url.'" '.$newwintag.'><img src="'.$infolinks_row['infolinks_image'].'" alt="'.$infolinks_row['infolinks_title'].'" border="0"></a>';
				$rightImage="&nbsp;";
			} else {
				$rightImage='<a href="'.$url.'" '.$newwintag.'><img src="'.$infolinks_row['infolinks_image'].'" alt="'.$infolinks_row['infolinks_title'].'" border="0"></a>';
				$leftImage="&nbsp;";
			}
		}
		
		if ((int)$infolinks_row['infolinks_imageonly']==0) {
			$boxContent.= '
							<tr>
								<td class="storeText">
								<table border="0" width="100%" align="'.$infolinks_grp_row['infolinks_groups_align'].'" cellspacing="0" cellpadding="0">
									<tr>
										<td background="'.DIR_WS_IMAGES.'space_line2.gif" height="1">'.tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '1').'</td>
									</tr>
								</table><br>
								<table border="0" align="'.$infolinks_grp_row['infolinks_groups_align'].'" cellspacing="0" cellpadding="0">
									<tr>
										<td class="systemBoxText" valign="middle">'.$leftImage.'</td>
										<td class="systemBoxText" valign="middle"><a href="'.$url.'" '.$newwintag.'>'.$infolinks_row['infolinks_title'].'</a></td>
										<td class="systemBoxText" valign="middle">'.$rightImage.'</td>
									</tr>
								</table>
								</td>
						  </tr>';
		} else {
			$boxContent.= '<tr>
							<td class=storeText>
								<table align="'.$infolinks_grp_row['infolinks_groups_align'].'">
									<tr>
										<td colspan="3" background="'.DIR_WS_IMAGES.'space_line2.gif" height="1">'.tep_image(DIR_WS_IMAGES.'space.gif', '', '1', '1').'</td>
									</tr>
									<tr>
										<td class=systemBoxText vAlign=middle></TD>
										<td class=systemBoxText vAlign=middle>
											<a href="'.$url.'" '.$newwintag.'><img src="'.$infolinks_row['infolinks_image'].'" alt="'.$infolinks_row['infolinks_title'].'" border="0"></a>
										</td>
										<td class=systemBoxText vAlign=middle></TD>
									</tr>
								</table>';
		}
		
		if ( ($count+1) < $rowCount) {
			if (tep_not_null($infolinks_grp_row['infolinks_groups_seperator_image'])) {
				$boxContent.= '<tr><td width="100%" style="background: url(\''.$infolinks_grp_row['infolinks_groups_seperator_image'].'\'); background-repeat: repeat-x; background-position: left center;"></td>';
			} else {
				$boxContent.= '<tr>
								<td class=storeText>
									<table border="0" align="'.$infolinks_grp_row['infolinks_groups_align'].'">
									<tr>
										<td class=systemBoxText valign=middle></TD>
										<td class=systemBoxText valign=middle></td>
										<td class=systemBoxText valign=middle></TD>
									</tr>
									</table>';
			}
		}
		
		$count++;
	}
	
	if ($rowCount) {
		// Only show when there is infolinks for this categories
 		require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE); 
 	}
 	
	$background_color = '';
	$heading_title_style = '';
	$boxText = '';
	$corner_left = '';
	$corner_right = '';
	$background_image='';
}
?>