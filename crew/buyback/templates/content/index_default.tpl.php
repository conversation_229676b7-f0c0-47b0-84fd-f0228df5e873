<?
if ($show_popup) {
?>
	<link rel="stylesheet" type="text/css" href="<?=DIR_WS_JAVASCRIPT . 'subModal/subModal.css'?>" />
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'subModal/common.js'?>"></script>
	<script type="text/javascript" src="<?=DIR_WS_JAVASCRIPT . 'subModal/subModal.js'?>"></script>
	<script type="text/javascript">
		<!--
		var noticebox_msg = '<?=sprintf(POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_NOTICE, tep_href_link(FILENAME_MY_ACCOUNT_MGMT, tep_get_all_get_params($get_params_always_exclude_array).'current_step=3'), 'onclick="window.top.hidePopWin()"', $date_diff)?>';
		var noticebox_title = '<?=POPUP_TEXT_CHANGE_TO_SECRET_QUESTION_AND_ANSWER_TITLE?>';
		
		window.onload = function() {
			initPopUp();
			showPopWin(noticebox_msg, noticebox_title, 500, 300, null); 
		}
		//-->
	</script>
<?
}
?>
<script type="text/javascript">
	var divhtml = '';
	var div_row_count = 0;
	var countdown_secs_default = 200;
	var countdown_secs = 0;
	var coundown_active = false;
	var lockdown_form_secs = 2;
	var global_loading_message = '<?=TEXT_LOADING_MESSAGE?>';
	
	function redirect_to_order(products_id, game_cat_id) {
		var selfPage = '<?=tep_href_link(FILENAME_DEFAULT, tep_get_all_get_params(array('gcid', 'pid', 'action')))?>';
		var targetHref = selfPage + (selfPage.indexOf('?') != -1 ? '&' : '?') + 'action=redir_2_order' + '&gcid=' + game_cat_id + '&pid=' + products_id;
		window.location = targetHref;
	}
	
	function add_server_listing_row(products_name, unit_price, buy_qty, game_cat_id, products_id, is_buyback) {
		if (products_id == 'undefined' || products_id == null)	return;
		
		if (game_cat_id == 'undefined' || game_cat_id == null)	return;
		
		if (is_buyback == '1') {
			var make_order_link = '<input type="button" onClick="redirect_to_order(\'' + products_id + '\', \'' + game_cat_id + '\');" class="inputButton" value="<?=TEXT_SELECT_LINK?>">';
			var status_text = '<span class="navyBlueIndicator"><?=TEXT_ACTIVE?><\/span>';
		} else if (is_buyback == '-1') {	// Game buyback list is closed
			var make_order_link = '<a href="javascript:;" class="inactiveLink"><?=TEXT_SELECT_LINK?></a>';
			var status_text = '<span class="redIndicator"><?=TEXT_LIST_CLOSE?><\/span>';
		} else {
			var make_order_link = '<a href="javascript:;" class="inactiveLink"><?=TEXT_SELECT_LINK?></a>';
			var status_text = '<span class="redIndicator"><?=TEXT_INACTIVE?><\/span>';
		}
		
		var row_style = (div_row_count%2) ? 'ordersListingEven' : 'ordersListingOdd' ;
		
		divhtml += '<tbody>'
				+ '<tr height="25" class="'+row_style+'"><td align="left" width="45%" class="ordersRecords">' + products_name + '<\/td>'
				+ '<td align="right" width="15%" class="ordersRecords">' +  unit_price + '<\/td>'
				+ '<td align="right" width="15%" class="ordersRecords">' +  buy_qty + '<\/td>'
				+ '<td align="center" width="10%" class="ordersRecords">' + status_text + '<\/td>'
				+ '<td align="center" width="##DYNAMIC_WIDTH##">' + make_order_link + '<\/td>'
				+ '<\/tbody>';
		
		div_row_count++;
	}
	
	function show_server_listing_div() {
		if (div_row_count < 9) {
			divhtml = divhtml.replace(/##DYNAMIC_WIDTH##/ig, "15%");
		} else {
			divhtml = divhtml.replace(/##DYNAMIC_WIDTH##/ig, "13%");
		}
		
		div_row_count = 0;
		
		document.getElementById('wbb_div_server_listing').innerHTML = '<table border="0" cellpadding="0" cellspacing="0" width="100%">'+divhtml+'</table>';
		document.getElementById('wbb_div_server_listing').style.height = 250;
	}
	
	function toggleCountdown(cbChecked) {
		if (cbChecked == true && parseInt(DOMCall('wbb_div_game_cat_id').value) > 0) {
			start_coundown_activity();
		} else {
			stop_coundown_activity();
		}
	}
	
	function start_coundown_activity() {
		return true;
		
		coundown_active = true;
		countdown_secs = countdown_secs_default;
		document.getElementById('timer_main').innerHTML = '<?=TEXT_REFRESH_NOTICE . '<span id="timer_display" name="timer_display"></span>&nbsp;'.TEXT_SECONDS?>';
		doCountDown();
	}
	
	function stop_coundown_activity() {
		return true;
		
		coundown_active = false;
		disableDivInputs(false);
		document.getElementById('timer_main').innerHTML = '<span id="timer_display" name="timer_display"></span>';
	}
	
	function doCountDown() {
		return true;
		
		countdown_secs -= 1;
		document.getElementById('timer_display').innerHTML = countdown_secs;
		if (coundown_active == false) {
			document.getElementById('timer_main').innerHTML = '';
			return;
		}
		if (countdown_secs > 0) {
			if (countdown_secs == lockdown_form_secs) {
				disableDivInputs(true);
			}
			setTimeout("doCountDown()",1000);
		} else {
			set_selected_game(DOMCall('wbb_div_game_cat_id').value);
		}
	}
	
	function disableDivInputs(bool) {
		//Enter here during form lockdown
		//document.getElementById('price_list_cb').disabled=bool;
		if (bool == true) {
			document.getElementById('wbb_div_server_listing').innerHTML = '<p class="title-text" valign="middle" align="center">Please wait for refresh.</p>';
		}
	}
	
	function set_selected_game(game_cat_id) {
		if (DOMCall('game_link_'+game_cat_id) != null) {
			if (typeof(DOMCall('game_link_'+game_cat_id).innerText) != 'undefined') {
				var game_title = DOMCall('game_link_'+game_cat_id).innerText;
			} else {
				var game_title = DOMCall('game_link_'+game_cat_id).text;
			}
			DOMCall('dyn_game_title').innerHTML = game_title;	// Set the game title
			onQuickBuybackGameSelection(game_cat_id, '<?=$languages_id?>');
		} else {
			alert('<?=JS_NO_GAME_SELECTED?>');
		}
	}
</script>
<?='<div class="hide">'.tep_image(DIR_WS_IMAGES . 'loading.gif').'</div>'?>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<td>
			<!-- //News start -->
			<table width="100%" border="0" cellspacing="0" cellpadding="0">
			    <tr>
			      	<td align="left"><?=tep_image(DIR_WS_IMAGES . 'pixel_trans.gif', '', '1', '3')?></td>
			    </tr>
			    <tr>
			      	<td align="left">
<?
$latest_news_type_arr = array("Announcements" => array('type' => 'classic', 'template' => 'paging'));
foreach ($latest_news_type_arr as $LATEST_NEWS_TYPE => $latest_news_settings) {
?>
						<table border="0" width="100%" cellspacing="1" cellpadding="0">
							<tr>
							  	<td width="3" height="3"><?=tep_image(DIR_WS_IMAGES . 'box_infomiddle01.gif', '', '3', '3')?></td>
							  	<td background="<?=DIR_WS_IMAGES?>box_infomiddle02.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
							  	<td width="3" height="3"><?=tep_image(DIR_WS_IMAGES . 'box_infomiddle03.gif', '', '3', '3')?></td>
							</tr>
							<tr>
						  		<td background="<?=DIR_WS_IMAGES?>box_infomiddle08.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
						  		<td>
<?
	include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
	$more_link = tep_href_link(FILENAME_NEWS, "news_type=".$group_news['news_groups_id']);
?>
								</td>
						  		<td background="<?=DIR_WS_IMAGES?>box_infomiddle04.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
							</tr>
							<tr>
								<td height="15" width="3"><img src="<?=DIR_WS_IMAGES?>box_infobottom01.gif" alt="" height="15" width="3"></td>
								<td align="right" background="<?=DIR_WS_IMAGES?>box_infobottom02.gif" height="15"><!--a class="SystemNav" href="<?=$more_link?>"><?=tep_image(DIR_WS_IMAGES . 'box_infobottom04.gif', '', '78', '15')?></a--></td>
								<td height="15" width="3"><img src="images/box_infobottom03.gif" alt="" height="15" width="3"></td>
							</tr>
							<tr>
								<td colspan="3" class="storeBoxTransImg"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '5','4')?></td>
							</tr>
						</table>
<?
}
?>
			      	</td>
				</tr>
			</table>
			<!-- //News end -->
		</td>
	</tr>
	<tr>
		<td class="storeBoxTransImg"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '5','4')?></td>
	</tr>
	<tr>
		<td>
			<!-- Price List Start -->
			<table cellpadding="0" border="0" cellspacing="0" width="100%">
				<tbody>
				<tr>
					<td height="3" width="3"><?=tep_image(DIR_WS_IMAGES . 'box_grey01a.gif', '', '3', '3')?></td>
					<td background="<?=DIR_WS_IMAGES . 'box_grey01b.gif'?>"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
					<td height="3" width="3"><?=tep_image(DIR_WS_IMAGES . 'box_grey01c.gif', '', '3', '3')?></td>
				</tr>
				<tr bgcolor="#f5f5f5">
					<td background="<?=DIR_WS_IMAGES?>box_grey01h.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
					<td>
						<table border="0" cellpadding="0" cellspacing="0" width="100%">
					  		<tbody>
					  		<tr>
								<td height="20">
									<table border="0" width="100%" cellspacing="0" cellpadding="2">
						  				<tbody>
						  				<tr>
							  				<td colspan="3" height="1"><div id="div_game_reopen_count_down" class="messageStackWarning"></div></td>
										</tr>
						  				<tr>
											<td><?=tep_image(DIR_WS_ICONS . 'icon_03.gif', '', '10', '10', 'hspace="4"')?></td>
											<td width="100%" class="title-text" align="left"><?=TEXT_HEADING_PRICE_LIST?></td>
											<td align="right" class="title-text">&nbsp;</td>
						  				</tr>
										</tbody>
									</table>
								</td>
					  		</tr>
					  		<tr>
								<td align="center" valign="top">
									<table border="0" cellpadding="0" cellspacing="0" width="100%" bgcolor="#f5f5f5">
										<tbody>
										<tr>
								  			<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02a.gif', '', '5', '40')?></td>
								  			<td height="40" background="<?=DIR_WS_IMAGES?>box_grey02b.gif">
												<table border="0" cellpadding="0" cellspacing="0" height="40" width="100%">
													<tbody>
													<tr>
										  				<td align="left" width="45%" class="title-text"><p><?=TEXT_HEADING_SERVER?>&nbsp;<span id="dyn_game_title" class="title-text"></span></p></td>
										  				<td align="right" width="15%" class="title-text"><p><?=TEXT_HEADING_UNIT_PRICE?></p></td>
										  				<td align="right" width="15%" class="title-text"><p><?=TABLE_HEADING_PURCHASE_QTY?><div id='dyn_game_product_unit_name' name='dyn_game_product_unit_name' class='title-text'></div></p></td>
										  				<td align="center" width="10%"  class="title-text"><p><?=TEXT_HEADING_STATUS?></p></td>
										  				<td align="right" width="15%"><?='<a href="javascript:;" onClick="set_selected_game(DOMCall(\'wbb_div_game_cat_id\').value);" class="categoryNavigation">'.BUTTON_REFRESH.'</a>'?></td>
													</tr>
													<tr>
														<td colspan="5" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
													</td>
													</tbody>
												</table>
								  			</td>
								  			<td height="40" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02c.gif', '', '5', '40')?></td>
										</tr>
										<tr>
								  			<td height="40" bgcolor="#f5f5f5" width="5"></td>
								  			<td width="100%" height="0">
									  			<table border="0" cellpadding="0" cellspacing="0" width="100%">
													<tbody>
													<tr>
										  				<td colspan="3" valign="top">
															<div id="wbb_div_server_listing" name="wbb_div_server_listing" style="position:relative; height:30; width:100%; overflow:auto;"><?='<span class="title-text">'.TEXT_MAIN_PLEASE_SELECT_GAME.'</span>'?></div>
										  				</td>
													</tr>
													<tr>
										  				<td colspan="4" background="<?=DIR_WS_IMAGES . 'space_line2.gif'?>" height="1"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '1')?></td>
													</tr>
												</tbody>
									  		</table>
								  		</td>
								  		<td height="40" width="5" bgcolor="#f5f5f5">&nbsp;</td>
									</tr>
									<tr>
								  		<td height="19" width="5"><?=tep_image(DIR_WS_IMAGES . 'box_grey02f.gif', '', '5', '19')?></td>
								  		<td background="<?=DIR_WS_IMAGES?>box_grey02e.gif" height="19">&nbsp;</td>
								  		<td height="19"><?=tep_image(DIR_WS_IMAGES . 'box_grey02d.gif', '', '5', '19')?></td>
									</tr>
						  			</tbody>
						  		</table>
						  	</td>
						</tr>
						</tbody>
					</table>
				</td>
				<td background="<?=DIR_WS_IMAGES?>box_grey01d.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
			</tr>
			<tr>
				<td height="3" width="3"><?=tep_image(DIR_WS_IMAGES . 'box_grey01g.gif', '', '3', '3')?></td>
				<td background="<?=DIR_WS_IMAGES?>box_grey01f.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
				<td height="3" width="3"><?=tep_image(DIR_WS_IMAGES . 'box_grey01e.gif', '', '3', '3')?></td>
		  	</tr>
			</tbody>
		</table>
			<?=tep_draw_form('quick_buyback', FILENAME_DEFAULT, tep_get_all_get_params(), "post")
			.tep_draw_hidden_field('wbb_div_game_cat_id', '', ' id="wbb_div_game_cat_id" ')
			.'</form>'?>
			<!-- Price List End -->
		</td>
	</tr>
	<tr>
		<td class="storeBoxTransImg"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '5','4')?></td>
	</tr>
	<tr>
		<td>
			<!-- //News start -->		
			<table width="100%" border="0" cellspacing="0" cellpadding="0">
			    <tr>
			      <td align="left"><?=tep_image(DIR_WS_IMAGES . 'pixel_trans.gif', '', '1', '3')?></td>
			    </tr>
			    <tr>
			      <td align="left">
<?
$latest_news_type_arr = array(	'Promotions' => array('type' => 'short'), 
								'News' => array('type' => 'short'), 
								'Game Info' => array('type' => 'short')
							);
foreach ($latest_news_type_arr as $LATEST_NEWS_TYPE => $latest_news_settings) {
?>
					<table border="0" width="100%" cellspacing="1" cellpadding="0">
						<tr>
						  <td width="3" height="3"><?=tep_image(DIR_WS_IMAGES . 'box_infomiddle01.gif', '', '3', '3')?></td>
						  <td background="<?=DIR_WS_IMAGES?>box_infomiddle02.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '1', '3')?></td>
						  <td width="3" height="3"><?=tep_image(DIR_WS_IMAGES . 'box_infomiddle03.gif', '', '3', '3')?></td>
						</tr>
						<tr>
						  <td background="<?=DIR_WS_IMAGES?>box_infomiddle08.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
						  <td>
<?
	include(DIR_WS_MODULES . FILENAME_LATEST_NEWS);
	$more_link = tep_href_link(FILENAME_NEWS, "news_type=".$group_news['news_groups_id']);
?>
							</td>
						  <td background="<?=DIR_WS_IMAGES?>box_infomiddle04.gif"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '3', '1')?></td>
						</tr>
						<tr>
							<td height="15" width="3"><img src="<?=DIR_WS_IMAGES?>box_infobottom01.gif" alt="" height="15" width="3"></td>
							<td align="right" background="<?=DIR_WS_IMAGES?>box_infobottom02.gif" height="15"><a class="SystemNav" href="<?=$more_link?>"><?=tep_image(DIR_WS_IMAGES . 'box_infobottom04.gif', '', '78', '15')?></a></td>
							<td height="15" width="3"><img src="images/box_infobottom03.gif" alt="" height="15" width="3"></td>
						</tr>
						<tr>
							<td colspan="3" class="storeBoxTransImg"><?=tep_image(DIR_WS_IMAGES . 'space.gif', '', '5','4')?></td>
						</tr>
					</table>
<?
}
?>
			      </td>
			    </tr>
			</table>
			<!-- //News end -->		
		</td>
	</tr>
</table>
<?php
	$js_str = '';
	if (isset($first_game_cat_id) && $first_game_cat_id) {
		$js_str = "set_selected_game($first_game_cat_id);";
	}
?>
<script type="text/javascript">
	<?=$js_str?>
</script>