<?php
if ($messageStack->size(CONTENT_SUPPLIER_SIGNUP) > 0) {
	$boxContent = '<table cellspacing="2" cellpadding="0">
				    <tr>
				      <td>' . $messageStack->output(CONTENT_SUPPLIER_SIGNUP) . '</td>
				    </tr>
					</table>';

	require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
} // end error box

$boxContent = tep_draw_form('password_forgotten', FILENAME_ACTIVATE_ACCOUNT, tep_get_all_get_params(array('activation_code', 'email_address')), 'post') . tep_draw_hidden_field('action', 'reset_password');
$boxHeading = HEADING_NEW_CUSTOMER;
$boxContent .= '
				<table width="100%" border="0" cellspacing="2" cellpadding="0">
					<tr>
					  <td align="left">
						<table width="100%" border="0" cellspacing="2" cellpadding="0">
							<tr>
							  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '3') . '</p></td>
							</tr>
						  	<tr>
								<td colspan="2"><p>' . TEXT_NEW_CUSTOMER . '<br><br>' . TEXT_NEW_CUSTOMER_INTRODUCTION . '</p></td>
						  	</tr>
							<tr>
							  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
							</tr>
			                <tr>
			                    <td colspan="2" align="right"><p>' . tep_image_button('', IMAGE_BUTTON_CONTINUE, tep_href_link(FILENAME_SUPPLIER_SIGNUP, '', 'SSL')) . '</p></td>
			                </tr>
							<tr>
							  <td colspan="2" align="left"><p>' . tep_image(DIR_WS_IMAGES . 'space.gif', '', '10', '8') . '</p></td>
							</tr>
					  	</table>
					  </td>
					</tr>
				</table>
				</form>';

require(DIR_WS_TEMPLATES . TEMPLATENAME_INFOMIDDLE);
?>